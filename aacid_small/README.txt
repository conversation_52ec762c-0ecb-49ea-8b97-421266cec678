Generated by manually grepping records from the real ones, and then compressing using:

docker exec -it web bash -c 'for f in /app/aacid_small/*.jsonl; do echo "Processing $f"; t2sz $f -l 22 -s 1M -T 32 -f -o $f.seekable.zst; done'

# worldcat
- first 1000 records or so from annas_archive_meta__aacid__worldcat__20231001T025039Z--20231001T235839Z.jsonl
- records added at the end, from `zstdcat annas_archive_meta__aacid__worldcat__20241230T203056Z--20241230T203056Z.jsonl.seekable.zst | grep '"search_editions_response/1"'`
- single record aacid__worldcat__20241230T203056Z__JqCXDPVKyXRK2zvSBKK3Lv added (oclc:1193939360) (deliberately removed ISBNs so it doesn't match on that)
- 10 library records (result of `zstdcat annas_archive_meta__aacid__worldcat__20241230T203056Z--20241230T203056Z.jsonl.seekable.zst | grep '"other_meta_type":"library"' | head`)
- single record aacid__worldcat__20241230T203056Z__jxeXw98rzNiJbadnrhvkFC added

# zlib3
- Record with file: 22433983
- Record with multiple values: 27250246
- DMCA record: 28406459
- Spam record: 28403296
- Chinese collection record: 29212943

# Connections
- aacid__nexusstc_records__20240516T173540Z__eRfYDiAsk9u9RsE1T4LRiq => isbn13:9780080123011 => OCLC oclc:260
- aacid__ebscohost_records__20240823T161746Z__dNKnzFACHDdK3LMXwKKT7g => isbn13:9789004128101 => aacid__ia2_records__20240701T024508Z__fXwMUwGaE2u4Qi3vLi6hXe and aacid__ia2_acsmpdf_files__20240823T234615Z__Kxw3rjhx89g75T5rYtMPE6
- aacid__ia2_records__20240126T065114Z__36XV8fUiR5vpmLUMMamqyS (IA 1000carsofnycsol0000kore) => ol:OL10000075M (deliberately modified "openlibrary_edition" in the ia2_records AAC to match like this)
- OL /books/OL1000004M => md5:a50f2e8f2963888a976899e2c4675d70 (annas_archive identifier field for primary linking)
- OL /books/OL1000000M => ocaid:tankkillingantit0000hogg => aacid__ia2_records__20240126T070451Z__NvMQ2fj3EjR2pzmFn77hyJ (ISBN and openlib ID deliberately removed from aac record so that only ocaid matches)
- OL /books/OL1000003M => isbn10:1861523505 converted to isbn13:9781861523501 => aacid__ia2_records__20240126T065900Z__HoFf9oz2n3hxufw8hvrys2 (deliberately no ocaid match, and removed openlib ID from aac record)
- IA 100insightslesso0000maie (md5 74f3b80bbb292475043d13f21e5f5059) => isbn13:9780462099699 => ISBNdb 9780462099699
- IA foundationsofmar0000fahy (md5 b6b75de1b3a330095eb7388068c1b948) => aacid__worldcat__20241230T203056Z__JqCXDPVKyXRK2zvSBKK3Lv (oclc:1193939360) (deliberately removed ISBNs so it doesn't match on that)
- Scihub doi links (several): 10.1002/(SICI)(1997)5:1<1::aid-nt1>3.0.co;2-8.pdf (Modified to capital letters "SICI" to test DOI case insensitivity.) => md5:93b76bc6875ce7957eeec1247e7b83b9; 10.1007/0-306-47595-2.pdf => md5:1b9a20387c2ce2c837f0d552bb4e559d; 10.1007/b102786.pdf => md5:d63aa15ab0a797dbd851ae5f6f647611; 10.1036/0071438289.pdf => md5:a50f2e8f2963888a976899e2c4675d70; 10.1036/0071446508.pdf => md5:cff0dece0fbc9780f3c13daf1936dab7; 10.1385/1592591930.pdf => md5:2ee1728013cc3326af7abc91da9e8e55; 10.5822/978-1-61091-843-5_15.pdf => md5:a3e56a04e1e16c9e527c03cf85f63be0; 
- aacid__upload_records_aaaaarg__20240627T210551Z__4925970__UNSZAr3iqGXy4t3Uyyzzgy => Keywords "http://www.archive.org/details/100marvelsupreme0000samm" (manually added) => aacid__ia2_records__20240126T065114Z__P77QGfwfrzVPjMnGZA4wQB (ocaid:100marvelsupreme0000samm, deliberately one WITHOUT ia2_acsmpdf_files, otherwise it won't match)
- aacid__upload_records_woz9ts_duxiu__20240627T230829Z__12190448__G7BxAWxyvdwDsVhRsGWsGp => duxiu_ssid:14648061 (through extract_ssid_or_ssno_from_filepath) => aacid__duxiu_records__20240205T000000Z__6zNPtVef7GFMUCKoLnjPjv (duxiu_ssid:14648061; matched as "duxius_nontransitive_meta_only")
- aacid__upload_records_bpb9v_cadal__20240627T211853Z__5862676__aSd46Zg4RGcZ7MqmePAcVC => cadal_ssno:01020456 (through extract_ssid_or_ssno_from_filepath) => aacid__duxiu_records__20240130T000000Z__RLEZTJEFBcuCCGdmBrnfSB (cadal_ssno:01020456; matched as "duxius_nontransitive_meta_only")
- aacid__upload_records_trantor__20240627T211020Z__5440538__JUjjYnXXWfTgEDvpQCjPE5 => sha256:6043d539cc9d2a964ca6c134de580350b3877c566c57a37709439c923dbb14b5 => aacid__trantor_records__20240911T134314Z__EJxjScczMk8vWf8jEzcjie (and matching zlib3_record and zlib3_files so it shows up as md5s)
- aacid__upload_records_trantor__20240627T211001Z__5349018__c4B2WLNDiqqX7pQEekWWN7 => sha256:659162deb94ffcd0eb0c51169f43615b052d98ba8a8a8d0b05f7c3f2b7c848cc => aacid__trantor_records__20240911T134314Z__BAAHrjBHu943Ehof4Y3Wef (and matching zlib3_record and zlib3_files so it shows up as md5s)
- aacid__nexusstc_records__20240516T162126Z__43iCjWXoMWbsC9FSJjNfoQ => isbn13:9781108026512 => aacid__gbooks_records__20240920T051416Z__GETzR5Zximcxw4kAvBisvM
- aacid__nexusstc_records__20240516T152812Z__7ck1kAjKFPGL7hCYPT4ZPK => isbn13:9782130588252 => aacid__goodreads_records__20240913T115838Z__28223767__63Nx8yezHvKn6jPAEJCrfX
- aacid__zlib3_records__20240824T040725Z__29545078__ASMfze3MyphFoSdYbnbZ4v => isbn13:9789564084916 => aacid__libby_records__20240911T184811Z__10371786__DTjjwXDJjsHykZuDKRwJB8
- aacid__ia2_records__20240126T065937Z__PWLXDQfj8raXySASwwWYTH => isbn13:9787539190235 => aacid__worldcat__20241230T203056Z__7wzhmXXE72CM89Xz86jrL8
- aacid__upload_records_misc__20240627T233937Z__491971__6u64PEeu5seLzAXnExGGAW (and matching upload_files) => czech_oo42hcks_filename:SolenPapers/Solen_uro-200105-0009.pdf => aacid__czech_oo42hcks_records__20240917T175820Z__D3eKhyCaU624VewHp6HaQt
- aacid__upload_records_misc__20240627T233937Z__495641__fKdrwYUC9FWcodTAVRAoJu (and matching upload_files) => czech_oo42hcks_filename:CCCC/19530151.pdf => aacid__czech_oo42hcks_records__20240917T175820Z__L8awzAxEARfxubdXrok3QL
- aacid__upload_records_misc__20240627T233937Z__495639__3kP8itPUSuCvKiCfK4fLki (and matching upload_files) => czech_oo42hcks_filename:CCCC/19290658.pdf => aacid__czech_oo42hcks_records__20240917T175820Z__RMzzyh9GxgHa6ErpPoQ8EX
- aacid__ia2_records__20240126T070531Z__cT7Di2ntyu3QYKZCi8xKEH => isbn13:9789990500110 => aacid__cerlalc_records__20240918T044206Z__969UNYjPsEH4iMUC6NwPrc
- aacid__zlib3_records__20240809T200924Z__21891758__6JKGg7ar5ccWPjfSbU8mW8 => isbn13:9780586211281 => isbn13_prefix:9780586 => aacid__isbngrp_records__20240920T194930Z__A5mavEDkDnenRFaCXbGEZY
- aacid__zlib3_records__20240809T215546Z__27250306__oFf82h43Ta6EuVERvbVjp9 => isbn13:9785020171077 => aacid__rgb_records__20240919T161201Z__eVx9gaYSR4L5oiXpzXZ8Rr

# OCLC
- Testing handling of Pinyin in OCLC: aacid__ia2_records__20240701T024712Z__Sh6hUwpSdhnw93HTc5JvXC / aacid__ia2_acsmpdf_files__20240701T023706Z__WTmNK44PWWfrf5rvF8SFaU (/md5/529566fb502ee2ea3f949d8b2b3158a1) => isbn13:9787513717076 => aacid__worldcat__20241230T203056Z__3r25T8BKvPVgHDTAzxUYi3 / aacid__worldcat__20241230T203056Z__Guo45SEnRjJoDyBcz9oSKM
- Correct handling of Welsh language (800k written_population(), so edge case): aacid__worldcat__20241230T203056Z__dMGceY3zmXkGqFfexJzXLY
- pinyin edge case (timeout in our old case when we removed all spaces): aacid__worldcat__20241230T203056Z__NeCXeRfuDohL5uYnwZTKd8

# Upload
- .lit files shouldn't be marked as having a problem due to exiftool failing: aacid__upload_records_duxiu_main2__20250115T164002Z__2161813__Hz7FmJgpsNbFkf6WTysAma / aacid__upload_files_duxiu_main2__20250114T221524Z__UzKP3QAnBfjBhoPrg26GsM
- calibre .opf file matching: aacid__upload_records_misc__20241216T123335Z__63469__HcsA5RCMhhkukE7gvX4pK5 / aacid__upload_records_misc__20241216T123335Z__63459__2TVKKSqzS8YCwmaG7ZJRec / aacid__upload_files_misc__20241215T124604Z__FRVJ7ievvVKCjSy2RqrbLk / aacid__upload_files_misc__20241215T124604Z__iVvn8Jh3PZwZ3JJ4AoWDnr

# DuXiu
- Record in which "书名" ("title") in the INI data is the SSID: aacid__duxiu_records__20240305T000000Z__Moo2RJtqdVf6egw9ENaS98 / aacid__duxiu_records__20240205T000000Z__Wi3Bz3CWe79fq2wdPqL2Uh / aacid__duxiu_files__20240613T190635Z__cTFx8Lq38ktE9Saq6j58ZQ

# DuXiu new (from start of 2025)
- aacid__duxiu_records__20241116T000000Z__dm5WSBTVKGD6nbEJe3kxd3 => aacid__duxiu_files__20241205T044708Z__QXP63NKZK7Ax9Jj22XvKha
- aacid__duxiu_records__20250623T000000Z__3gSMPWWrFoWa85dw3Gu8Gx => aacid__duxiu_files__20250127T133530Z__XEYUZpM5h7B6XDrmMsXaSx
- aacid__duxiu_records__20250623T000001Z__AvmgXxksU66KNYuknJMrHL => aacid__duxiu_files__20250127T135216Z__YWYWeRMEfKr7trZ4K29ng2
- aacid__duxiu_records__20250623T000002Z__cb3jySznuMszR2csuwWULd => aacid__duxiu_files__20250127T141940Z__RSvRNYC86P4XSAjHXavkNQ

# HathiTrust
- aacid__hathitrust_records__20230505T141431Z__WB2SiCfx5q4DJETuByMSd4 => pairtree filename "aeu/pairtree_root/ar/k+/=1/39/60/=t/3t/t5/cr/6j/ark+=13960=t3tt5cr6j/ark+=13960=t3tt5cr6j.zip" => aacid__hathitrust_files__20250227T120812Z__22GT7yrb3SpiFbNagtGGv8 (found using `select * from annas_archive_meta__aacid__hathitrust_files join annas_archive_meta__aacid__hathitrust_records using (pairtree_filename) limit 1;`)

# Gbooks
- aacid__gbooks_records__20240920T051416Z__2XsfjxupUHySFGV4uAj6Hi => just because it has `industryIdentifiers` with "OTHER".
