# WARNING: `docker run --env-file` and `env_file` in docker-compose.yml behave differently!
# https://github.com/docker/cli/issues/4665#issuecomment-1821862464
# This .env file is aimed to be compatible with both, but we can't do that with values that contain spaces.

# Default values are optimized for production to avoid having to configure
# much in production.
#
# However it should be easy to get going in development too. If you see an
# uncommented option that means it's either mandatory to set or it's being
# overwritten in development to make your life easier.

# Mirror options
VALID_OTHER_DOMAINS=annas-archive.org,annas-archive.se

# ONLY for development, to get the first time `dbreset` going. Don't use in prod!
DATA_IMPORTS_MODE=1

# In production we use NETWORK_MODE=host so it works well with UFW. Locally
# the default of NETWORK_MODE=bridge is fine.
#NETWORK_MODE=bridge

# Enable BuildKit by default:
#   https://docs.docker.com/develop/develop-images/build_enhancements
DOCKER_BUILDKIT=1

# Rather than use the directory name, let's control the name of the project.
COMPOSE_PROJECT_NAME=allthethings

# In development we want all services to start but in production you don't
# need the asset watchers to run since assets get built into the image.
#
# You can even choose not to run mariadb in prod if you plan to use
# managed cloud services. Everything "just works", even optional depends_on!
#COMPOSE_PROFILES=mariadb,web,elasticsearch,elasticsearchaux,mariapersist,mariapersistreplica
COMPOSE_PROFILES=mariadb,assets,web,elasticsearch,elasticsearchaux,kibana,mariapersist,mailpit
# Use this profile to skip mariadb and elasticsearchaux for a "minimal" setup.
# The main search page should still work with this, though some pages might error out.
# COMPOSE_PROFILES=assets,web,elasticsearch,kibana,mariapersist,mailpit

# If you're running native Linux and your uid:gid isn't 1000:1000 you can set
# these to match your values before you build your image. You can check what
# your uid:gid is by running `id` from your terminal.
#UID=1000
#GID=1000

# In development avoid writing out bytecode to __pycache__ directories.
#PYTHONDONTWRITEBYTECODE=
PYTHONDONTWRITEBYTECODE=true

# You should generate a random string of 99+ characters for this value in prod.
# You can generate secure secrets by running: ./run flask secrets
SECRET_KEY=insecure_key_for_dev

# Another secret key for downloads
DOWNLOADS_SECRET_KEY=insecure_key_for_dev

# Customize elasticsearch and elasticsearchaux options.
# https://www.elastic.co/guide/en/elasticsearch/reference/current/advanced-configuration.html
ES_JAVA_OPTS_ELASTICSEARCH="-Xms256m -Xmx256m"
ES_JAVA_OPTS_ELASTICSEARCHAUX="-Xms256m -Xmx256m"
DOCKER_MAX_MEMORY_ELASTICSEARCH=2G
DOCKER_MAX_MEMORY_ELASTICSEARCHAUX=2G
DOCKER_MAX_MEMORY_WEB=2G

# Which environment is running?
# For Flask, it should be: "true" or "false"
# For Node, it should be: "development" or "production"
#FLASK_DEBUG=false
#NODE_ENV=production
FLASK_DEBUG=true
NODE_ENV=development

# In development with Docker Desktop / Linux the default value should work.
# If you have Docker running in a custom VM, put the VM's IP here instead.
#
# In production you'll want to set this to your domain name or whatever you
# plan to access in your browser, such as example.com.
#SERVER_NAME=localhost:8000

# The bind port for gunicorn.
#
# Be warned that if you change this value you'll need to change 8000 in both
# your Dockerfile and in a few spots in docker-compose.yml due to the nature of
# how this value can be set (Docker Compose doesn't support nested ENV vars).
#PORT=8000

# How many workers and threads should your app use? WEB_CONCURRENCY defaults
# to the server's CPU count * 2. That is a good starting point. In development
# it's a good idea to use 1 to avoid race conditions when debugging.
#WEB_CONCURRENCY=
WEB_CONCURRENCY=1
#PYTHON_MAX_THREADS=1

# Do you want code reloading to work with the gunicorn app server?
#WEB_RELOAD=false
WEB_RELOAD=true

#MARIADB_HOST=mariadb

# When setting up the replica, don't forgot to check
# out mariapersistreplica-conf/README.txt!
MARIAPERSIST_USER=mariapersist
MARIAPERSIST_PASSWORD=password
MARIAPERSIST_DATABASE=mariapersist
#MARIAPERSIST_HOST=mariapersist
#MARIAPERSIST_PORT=3333
#MARIAPERSIST_PORT_FORWARD=3333

MARIAPERSISTREPLICA_SERVER_ID=10000

#AUTOSSH_USER=someuser
#AUTOSSH_HOST=somehost
# Generate using ssh-keygen -t rsa -b 4096 -C "autossh" -f autossh_id_rsa
#AUTOSSH_ID_RSA=/home/<USER>/.ssh/autossh_id_rsa

# Connection string to Redis. This will be used to connect directly to Redis
# and for Celery. You can always split up your Redis servers later if needed.
#REDIS_URL=redis://redis:6379/0

# You can choose between DEBUG, INFO, WARNING, ERROR, CRITICAL or FATAL.
# DEBUG tends to get noisy but it could be useful for troubleshooting.
#CELERY_LOG_LEVEL=info

# Should Docker restart your containers if they go down in unexpected ways?
#DOCKER_RESTART_POLICY=unless-stopped
#DOCKER_RESTART_POLICY=no

# What health check test command do you want to run? In development, having it
# curl your web server will result in a lot of log spam, so setting it to
# /bin/true is an easy way to make the health check do basically nothing.
#DOCKER_WEB_HEALTHCHECK_TEST=curl localhost:8000/dyn/up/
DOCKER_WEB_HEALTHCHECK_TEST=/bin/true

# What ip:port should be published back to the Docker host for the app server?
# If you're using Docker Toolbox or a custom VM you can't use 127.0.0.1. This
# is being overwritten in dev to be compatible with more dev environments.
#
# If you have a port conflict because something else is using 8000 then you
# can either stop that process or change 8000 to be something else.
#
# Use the default in production to avoid having gunicorn directly accessible to
# the internet since it'll very likely be behind nginx or a load balancer.
#DOCKER_WEB_PORT_FORWARD=127.0.0.1:8000
DOCKER_WEB_PORT_FORWARD=8000

# What volume path should be used? In dev we want to volume mount everything
# so that we can develop our code without rebuilding our Docker images.
#DOCKER_WEB_VOLUME=./public:/app/public
DOCKER_WEB_VOLUME=.:/app

# To use a different ElasticSearch host:
#ELASTICSEARCH_HOST=http://elasticsearch:9200
#ELASTICSEARCHAUX_HOST=http://elasticsearchaux:9201

# To access ElasticSearch/Kibana externally:
#ELASTICSEARCH_PORT_FORWARD=9200
#KIBANA_PORT_FORWARD=5601

# Flask email password
#MAIL_PASSWORD=password

#MEMBERS_TELEGRAM_URL=

SLOW_DATA_IMPORTS=true
AACID_SMALL_DATA_IMPORTS=true
AA_EMAIL=<EMAIL>

OPENAI_API_KEY=

AAC_SFTP_IP=
AAC_SFTP_PORT=
AAC_SFTP_USERNAME=
AAC_SFTP_PASSWORD=
AAC_SFTP_REMOTE_PATH=
