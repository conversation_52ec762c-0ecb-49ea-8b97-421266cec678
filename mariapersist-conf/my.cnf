[mariadb]
port = 3333

key_buffer_size=10M

innodb_buffer_pool_size=10G
innodb_log_buffer_size=1G
innodb_log_file_size=1G
innodb_sort_buffer_size=64M
innodb_log_file_buffering=1

log-bin
log-basename=mariapersist
server_id=100
expire_logs_days=30

# https://severalnines.com/blog/database-performance-tuning-mariadb/
max_connections=20000
query_cache_type=OFF

idle_transaction_timeout=10
idle_write_transaction_timeout=10
innodb_lock_wait_timeout=20
innodb_rollback_on_timeout=1
lock_wait_timeout=20
max_statement_time=300
wait_timeout=600
net_read_timeout=600
group_concat_max_len=4294967295

[mariadbd]
collation-server = utf8mb4_bin
init-connect='SET NAMES utf8mb4'
character-set-server = utf8mb4

[client]
binary-as-hex = true
default-character-set=utf8mb4
