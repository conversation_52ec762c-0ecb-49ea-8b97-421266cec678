# Anna's Archive Containers: data format.

One-time scraped datasets should ideally follow our AAC conventions. Follow this guide to provide us with files that we can easily release.

IMPORTANT: Please ALSO store the original files (HTML, XML, JSON) and zip them, so we can refer to them if necessary.

## AAC format

Give us a single .jsonl file, which should be in the AAC format.

* Here is are examples: https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/aacid_small
* And here is the documentation: https://annas-archive.li/blog/annas-archive-containers.html

Essentially just wrap every line in `{"aacid":..,"metadata":<your original json>}`. Your original JSON should have the ID of the record as its first field. If you have fields of multiple types (e.g. "groups" and "books"), then you can prefix the ID with the type, e.g. "group_001" and "book_789".

The aacid should be of the format: `aacid__gbooks_records__<timestamp>__<short_uuid>` (where `short_uuid` is generated by https://github.com/skorokithakis/shortuuid/ or similar). The `timestamp` can be simply the timestamp of when you generate the JSON, in a format like this: `20230808T014342Z`.

So for example:

```
{"aacid":"aacid__gbooks_records__20230808T014342Z__URsJNGy5CjokTsNT6hUmmj","metadata":{"id":"dNC07lyONssC","etag":"KIIFqNBED0U","industryIdentifiers":[{"type":"ISBN_13","identifier":"9781108026512"},{"type":"ISBN_10","identifier":"1108026516"}],"title":"The Elements and Practice of Rigging, Seamanship, and Naval Tactics","subtitle":null,"authors":["David Steel"],"pageCount":204,"printType":"BOOK","language":"en","publishedDate":"2011-01-20"}}
```

Replace `gbooks_records` with an appropriate name for your collection, such as `magzdb_records`.

Then the filename should be: `annas_archive_meta__aacid__gbook_records__<first_timestamp>--<last_timestamp>.jsonl`

## Compress to .seekable.zst

Then finally compress using https://github.com/martinellimarco/t2sz:

`t2sz annas_archive_meta__aacid__gbook_records__<first_timestamp>--<last_timestamp>.jsonl -l 22 -s 10M -T 32 -o annas_archive_meta__aacid__gbook_records__<first_timestamp>--<last_timestamp>.jsonl.seekable.zst`


Here is the code from our `Dockerfile` for installing `t2sz`:

```Dockerfile
# Install latest, with support for threading for t2sz
RUN git clone --depth 1 https://github.com/facebook/zstd --branch v1.5.6
RUN cd zstd && make && make install
# Install t2sz
RUN git clone --depth 1 https://github.com/martinellimarco/t2sz --branch v1.1.2
RUN mkdir t2sz/build
RUN cd t2sz/build && cmake .. -DCMAKE_BUILD_TYPE="Release" && make && make install
# Env for t2sz finding latest libzstd
ENV LD_LIBRARY_PATH=/usr/local/lib
```

You can check that the final file is correct jsonl by running `zstdcat <compressed_filename> | jq .`
