services:
  "aa-data-import--mariadb":
    container_name: "aa-data-import--mariadb"
    image: "mariadb:10.10.2"
    environment:
      MARIADB_USER: "allthethings"
      MARIADB_PASSWORD: "password"
      MARIADB_ROOT_PASSWORD: "password"
      MARIADB_DATABASE: "allthethings"
      MARIADB_INITDB_SKIP_TZINFO: "1" # https://github.com/MariaDB/mariadb-docker/issues/262#issuecomment-672375238
    volumes:
      - "./mariadb-conf:/etc/mysql/conf.d"
      # These two are outside the repo, so we don't get huge contexts whenever building (neither in this subdir
      # nor when running docker in the root of the repo).
      - "../../aa-data-import--allthethings-mysql-data:/var/lib/mysql/"
      - "../../aa-data-import--temp-dir:/temp-dir"
      - "../../aa-data-import--mariadb-tmp-dir:/tmp"
    command: "--init-file /etc/mysql/conf.d/init.sql"

  "aa-data-import--elasticsearch":
    container_name: "aa-data-import--elasticsearch"
    build:
      context: '..'
      dockerfile: Dockerfile-elasticsearch
    environment:
      - "ES_PORT=9200"
      - "ES_SETTING_TRANSPORT_PORT=9300"
      - "ES_SETTING_DISCOVERY_TYPE=single-node"
      - "ES_SETTING_BOOTSTRAP_MEMORY__LOCK=true"
      # - "ES_JAVA_OPTS=-Xms8g -Xmx8g"
      - "ES_SETTING_XPACK_SECURITY_ENABLED=false"
    cap_add:
      - IPC_LOCK
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    volumes:
      - "../../aa-data-import--allthethings-elastic-data:/usr/share/elasticsearch/data"

  "aa-data-import--elasticsearchaux":
    container_name: "aa-data-import--elasticsearchaux"
    build:
      context: '..'
      dockerfile: Dockerfile-elasticsearch
    environment:
      - "ES_PORT=9201"
      - "ES_SETTING_HTTP_PORT=9201"
      - "ES_SETTING_TRANSPORT_PORT=9301"
      - "ES_SETTING_DISCOVERY_TYPE=single-node"
      - "ES_SETTING_BOOTSTRAP_MEMORY__LOCK=true"
      # - "ES_JAVA_OPTS=-Xms8g -Xmx8g"
      - "ES_SETTING_XPACK_SECURITY_ENABLED=false"
    cap_add:
      - IPC_LOCK
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    volumes:
      - "../../aa-data-import--allthethings-elasticsearchaux-data:/usr/share/elasticsearch/data"

  "aa-data-import--web":
    container_name: "aa-data-import--web"
    build:
      context: ".."
      target: "app"
      args:
        - "UID=1000"
        - "GID=1000"
    depends_on:
      - "aa-data-import--mariadb"
      - "aa-data-import--elasticsearch"
    env_file:
      - "./.env-data-imports-fixed"
      - "./.env-data-imports"
    restart: "unless-stopped"
    stop_grace_period: "3s"
    volumes:
      - "./scripts:/scripts"
      - "../../aa-data-import--temp-dir:/temp-dir"
      - "../../aa-data-import--allthethings-mysql-data:/aa-data-import--allthethings-mysql-data"
      - "../../aa-data-import--allthethings-elastic-data:/aa-data-import--allthethings-elastic-data"
      - "../../aa-data-import--allthethings-elasticsearchaux-data:/aa-data-import--allthethings-elasticsearchaux-data"
      - "../../aa-data-import--allthethings-file-data:/file-data"
      - "../../aa-data-import--allthethings-exports:/exports/"
      - "./mariadb-conf:/etc/mysql/conf.d"
      - "../public:/app/public"
    tty: true


  "aa-data-import--isbn-visualization":
    container_name: "aa-data-import--isbn-visualization"
    build:
      context: "../isbn-visualization"
    depends_on:
      - "aa-data-import--mariadb"
      - "aa-data-import--elasticsearch"
    env_file:
      - "./.env-data-imports-fixed"
      - "./.env-data-imports"
    restart: "unless-stopped"
    stop_grace_period: "3s"
    volumes:
      - "../../aa-data-import--isbn-visualization-temp-data:/app/data"
      - "../../aa-data-import--allthethings-file-data:/file-data"
      - "../../aa-data-import--allthethings-exports:/exports/"
    tty: true
    entrypoint: "tail -f /dev/null"
