[mariadb]
# skip-innodb
# innodb=OFF
default_storage_engine=MyISAM
default_tmp_storage_engine=MyISAM

default_storage_engine=MyISAM
key_buffer_size=250G
myisam_max_sort_file_size=10T
myisam_repair_threads=50
# These values not too high, otherwise load_libgenli.sh parallel's inserts might
# cause OOM.
myisam_sort_buffer_size=50G
bulk_insert_buffer_size=3G
sort_buffer_size=128M
max_connections=1000
max_allowed_packet=200M
group_concat_max_len=4294967295
innodb_flush_log_at_trx_commit=0
innodb_buffer_pool_size=10G
innodb_log_file_size=1G
innodb_sort_buffer_size=64M
max_delayed_threads=300

delayed_insert_timeout=3600000
net_read_timeout=3600000
net_write_timeout=3600000
wait_timeout=3600000
interactive_timeout=3600000
max_statement_time=3600000
idle_transaction_timeout=3600000
idle_write_transaction_timeout=3600000
innodb_lock_wait_timeout=3600000
lock_wait_timeout=3600000
thread_pool_idle_timeout=3600000
idle_readonly_transaction_timeout=3600000

connect_timeout=60
