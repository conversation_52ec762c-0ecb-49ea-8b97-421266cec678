[mariadb]
replicate_do_db=mariapersist

port = 3334

key_buffer_size=10M

innodb_buffer_pool_size=10G
innodb_log_file_size=1G
innodb_sort_buffer_size=64M
innodb_flush_log_at_trx_commit=2 # Too slow replication on non-NVMe drives otherwise, though this is a bit less safe.
slave_parallel_threads=20

log-bin
log-basename=mariapersist
expire_logs_days=30

# https://severalnines.com/blog/database-performance-tuning-mariadb/
max_connections=500
query_cache_type=OFF

[mariadbd]
collation-server = utf8mb4_bin
init-connect='SET NAMES utf8mb4'
character-set-server = utf8mb4

[client]
default-character-set=utf8mb4
