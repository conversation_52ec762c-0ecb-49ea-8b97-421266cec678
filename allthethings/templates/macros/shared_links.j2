{% macro html_a(text) %}<a{{ kwargs | xmlattr }}>{{ text }}</a>{% endmacro %}

{% set datasets_openlib = dict(href='/datasets/ol') %}
{% set donate = dict(href='/donate') %}
{% set metadata = dict(href='/metadata') %}
{% set torrents = dict(href='/torrents') %}
{% set torrents_derived_metadata = dict(href='/torrents#aa_derived_mirror_metadata') %}
{% set contact = dict(href='/contact') %}
{% set browser_verification = dict(href='/browser_verification') %}
{% set volunteering = dict(href='/volunteering') %}
{% set llm = dict(href='/llm') %}
{% set refer = dict(href='/refer') %}
{% set faqs_upload = dict(href='/faq#upload') %}
{% set faqs_help = dict(href='/faq#help') %}
{% set faqs_api = dict(href='/faq#api') %}
{% set faqs_what = dict(href='/faq#what') %}
{% set faqs_security = dict(href='/faq#security') %}
{% set anna_data_imports = dict(href='https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/main/data-imports/README.md') %}
{% set annas_translations = dict(href='https://translate.annas-archive.li/') %}
{% set annas_software = dict(href='https://software.annas-archive.li/') %}
{% set gitlab_issues = dict(href='https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/') %}
{% set gitlab_issue_mirrors = dict(href='https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/188') %}
{% set example_metadata_record = dict(href='/db/aarecord_elasticsearch/md5:8336332bf5877e3adbfb60ac70720cd5.json.html') %}
{% set alipay_pdf = dict(href='/alipay.pdf') %}
{% set email_dmca = '<EMAIL>' %}
{% set email_dmca_link = html_a(email_dmca, href=('mailto:' ~ email_dmca)) %}
{% set blog_aac = dict(href='/blog/annas-archive-containers.html') %}

{% set reddit_science_nexus = dict(href='https://www.reddit.com/r/science_nexus/', rel="noopener noreferrer nofollow", target='_blank') %}
{% set nexus_telegram = dict(href='https://t.me/nexus_aaron', rel="noopener noreferrer nofollow") %}
{% set telegram_volunteers = dict(href='', rel="noopener noreferrer nofollow") %}
{% set binance = dict(href="https://www.binance.com/en", rel="noopener noreferrer nofollow", target="_blank") %}
{% set coinbase = dict(href="https://www.coinbase.com", rel="noopener noreferrer nofollow", target="_blank") %}
{% set kraken = dict(href="https://www.kraken.com", rel="noopener noreferrer nofollow", target="_blank") %}
{% set open_library = dict(href='https://openlibrary.org/', rel="noopener noreferrer nofollow", target="_blank") %}

{% set contact_page_link = html_a(gettext('page.contact.title'), **contact) %}
{% set xmr_address_text = '8C1Tdvfhj6wHHPtvMHyAmn3jgt9vF9qSdKCYFy8U9ioB2Z16tEhjLSaB8qMSfzsnQeSrbohpYAiMgcW1acmmvCHQ4YGmZip' %}
{% set xmr_address %}<span class="text-xs break-all">{{ xmr_address_text }}</span>{% endset %}

{% set external_link = dict(rel="noopener noreferrer nofollow", target="_blank") %}
