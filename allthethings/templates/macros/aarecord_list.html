{% macro aarecord_list(aarecords=[], max_show_immediately=10, table=False) -%}
  <script>
    // We can't do this in Jinja because of https://github.com/pallets/jinja/issues/1693 :(
    if (!window.aarecord_list_code_loaded) {
      window.aarecord_list_code_loaded = true;

      var lastAnimationFrame = undefined;
      var topByElement = new Map();
      function render() {
        window.cancelAnimationFrame(lastAnimationFrame);
        lastAnimationFrame = window.requestAnimationFrame(() => {
          var bottomEdge = window.scrollY + window.innerHeight * 3; // Load 3 pages worth
          for (element of document.querySelectorAll('.js-scroll-hidden')) {
            if (!topByElement.get(element)) {
              topByElement.set(element, element.getBoundingClientRect().top + window.scrollY);
            }
            if (topByElement.get(element) <= bottomEdge) {
              element.classList.remove("js-scroll-hidden");
              element.innerHTML = element.innerHTML.replace('<' + '!--', '').replace('-' + '->', '')
            }
          }
        });
      }
      document.addEventListener('DOMContentLoaded', () => {
        document.addEventListener('scroll', () => {
          render();
        });
        render();
      });

      document.addEventListener("keydown", e => {
        const fields = Array.from(document.querySelectorAll('.js-vim-focus'));
        if (fields.length === 0) {
          return;
        }

        if (e.ctrlKey || e.metaKey || e.altKey) return;
        if (/^(?:input|textarea|select|button)$/i.test(e.target.tagName)) {
          if (e.key === "Escape") {
            e.preventDefault();
            fields[0].focus();
            return;
          }
          return;
        }
        if (e.key === "j" || e.key === "k") {
          e.preventDefault();
          const activeIndex = fields.indexOf(document.activeElement);
          if (activeIndex === -1) {
            fields[0].focus();
          } else {
            if (e.key === "j") {
              const newIndex = Math.min(activeIndex+1, fields.length-1);
              fields[newIndex].focus();
            } else {
              const newIndex = Math.max(activeIndex-1, 0);
              fields[newIndex].focus();
            }
          }
        }
      });
    }
  </script>


  {% if table %}
    {% set any_has_filename=(((aarecords | map(attribute="additional") | map(attribute="table_row") | selectattr("filename") | list) | length) > 0) %}

    <table class="text-sm w-full mt-4 h-fit">
      {% for aarecord in aarecords %}
        <tr class="group h-full even:bg-[#f2f2f2] hover:bg-yellow-100 aria-selected:bg-yellow-100 cursor-pointer relative {% if aarecord.file_unified_data.has_meaningful_problems %} opacity-40 {% endif %}">
          <td class="h-full w-[22px]">
            <a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px">
              {% if aarecord.additional.top_box.cover_url %}
                <span class="absolute left-0 top-[50%] hidden group-hover:block group-aria-selected:block">
                  <div id="hover_cover_aarecord_id__{{ aarecord.id }}" class="absolute right-1 top-0 min-w-[170px] -translate-y-1/2 group-hover:z-30 group-aria-selected:z-20 shadow-xl" >
                    <img class="min-w-[170px]" src="{{ aarecord.additional.top_box.cover_url }}" alt="" referrerpolicy="no-referrer" onerror="this.parentNode.removeChild(this)" loading="lazy" decoding="async"/>
                  </div>
                </span>
              {% endif %}

              <span id="table_cover_aarecord_id__{{ aarecord.id }}" class="relative overflow-hidden w-[22px] h-[30px] flex flex-col justify-center">
                <span class="block absolute w-full h-[28px] js-img-background-{{ md5(aarecord.additional.top_box.cover_url or '') }}" style="background-color: hsl({{ aarecord.additional.top_box.cover_missing_hue_deg }}deg 43% 73%)"></span>
                {% if aarecord.additional.top_box.cover_url %}
                  <img class="relative inline-block" src="{{ aarecord.additional.top_box.cover_url }}" alt="" referrerpolicy="no-referrer" onerror="this.parentNode.removeChild(this)" onload="for (let el of document.querySelectorAll('.js-img-background-{{ md5(aarecord.additional.top_box.cover_url or '') }}')) { el.parentNode.removeChild(el); }" loading="lazy" decoding="async"/>
                {% endif %}
              </span>
            </a>

            <a href="{{ aarecord.additional.path }}" class="js-vim-focus custom-a absolute w-full h-full top-0 left-0 outline-offset-[-2px] outline-2 rounded-[3px] focus:outline pointer-events-none z-10" onfocus="this.parentNode.parentNode.setAttribute('aria-selected', 'true')" onblur="this.parentNode.parentNode.setAttribute('aria-selected', 'false')"></a>
          </td>
          <td class="h-full {% if any_has_filename %}w-[18%]{% else %}w-[28%]{% endif %}"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.title}}{% for item in aarecord.additional.table_row.title_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}{% if aarecord.file_unified_data.has_meaningful_problems %}<span class="block text-xs text-gray-500">{{ gettext('page.search.results.issues') }}</span>{% endif %}</span></a></td>
          <td class="h-full {% if any_has_filename %}w-[18%]{% else %}w-[28%]{% endif %}"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.author}}{% for item in aarecord.additional.table_row.author_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}</span></a></td>
          <td class="h-full {% if any_has_filename %}w-[18%]{% else %}w-[28%]{% endif %}"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.publisher_and_edition}}{% for item in aarecord.additional.table_row.publisher_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}{% for item in aarecord.additional.table_row.edition_varia_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}</span></a></td>
          <td class="h-full"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.year}}{% for item in aarecord.additional.table_row.year_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}</span></a></td>
          <td class="h-full {% if any_has_filename %}w-[18%]{% endif %} max-w-[25%] text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.filename | replace('/', '<wbr>/' | safe) | replace('\\', '<wbr>\\' | safe) | replace('_', '<wbr>_' | safe) | replace('.', '<wbr>.' | safe)}}{% for item in aarecord.additional.table_row.original_filename_additional %}<span class="block text-xs text-gray-500">{{ item | replace('/', '<wbr>/' | safe) | replace('\\', '<wbr>\\' | safe) | replace('_', '<wbr>_' | safe) | replace('.', '<wbr>.' | safe) }}</span>{% endfor %}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.sources | replace('/', '<wbr>/' | safe)}}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.languages}}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.content_type}}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.extension}}{% for item in aarecord.additional.table_row.extension_additional %}<span class="block text-xs text-gray-500">{{ item }}</span>{% endfor %}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.filesize}}</span></a></td>
          <td class="h-full text-xs"><a href="{{ aarecord.additional.path }}" tabindex="-1" aria-disabled="true" style="overflow-wrap: break-word; max-height: 92px; border-top: 1px solid transparent; border-bottom: 1px solid transparent" class="custom-a flex flex-col h-full px-[0.5px] justify-around overflow-hidden group-hover:overflow-visible group-hover:relative group-hover:z-30 group-aria-selected:overflow-visible group-aria-selected:relative group-aria-selected:z-20"><span class="group-hover:bg-yellow-100 group-aria-selected:bg-yellow-100">{{aarecord.additional.table_row.id_name}}</span></a></td>
        </tr>
      {% endfor %}
    </table>
  {% else %}
    {% for aarecord in aarecords %}
      <div class="h-[110px] flex flex-col justify-center {% if loop.index0 > max_show_immediately %}js-scroll-hidden{% endif %}">
        {% if loop.index0 > max_show_immediately %}<!--{% endif %}
        <a href="{{ aarecord.additional.path }}" class="js-vim-focus h-[110px] custom-a flex items-center relative left-[-10px] w-[calc(100%+20px)] px-2.5 outline-offset-[-2px] outline-2 rounded-[3px] hover:bg-black/6.7 focus:outline {% if aarecord.file_unified_data.has_meaningful_problems %}opacity-40{% endif %}">
          <div class="flex-none">
            <div id="list_cover_aarecord_id__{{ aarecord.id }}" class="relative overflow-hidden w-[72px] h-[100px] flex flex-col justify-center">
              <div class="absolute w-full h-[94px] js-img-background-{{ md5(aarecord.additional.top_box.cover_url or '') }}" style="background-color: hsl({{ aarecord.additional.top_box.cover_missing_hue_deg }}deg 43% 73%)"></div>
              {% if aarecord.additional.top_box.cover_url %}
                <img class="relative inline-block" src="{{ aarecord.additional.top_box.cover_url }}" alt="" referrerpolicy="no-referrer" onerror="this.parentNode.removeChild(this)" onload="for (let el of document.querySelectorAll('.js-img-background-{{ md5(aarecord.additional.top_box.cover_url or '') }}')) { el.parentNode.removeChild(el); }" loading="lazy" decoding="async"/>
              {% endif %}
              {% if aarecord.extra_download_timestamp %}
                <div class="absolute bottom-0 p-1 text-[10px] bg-[rgba(200,200,200,0.9)] leading-none"><span title="{{ gettext('page.search.results.download_time') }}">{{ aarecord.extra_download_timestamp }}</span>{% if aarecord.extra_was_fast_download %}<span title="{{ gettext('page.search.results.fast_download') }}"> ⭐️</span>{% endif %}</div>
              {% endif %}
            </div>
          </div>
          <div class="relative top-[-1] pl-4 grow overflow-hidden">
            <div class="line-clamp-[2] leading-[1.2] text-[10px] lg:text-xs text-gray-500">{{ aarecord.additional.top_box.top_row }}</div>
            <h3 class="max-lg:line-clamp-[2] lg:truncate leading-[1.2] lg:leading-[1.35] text-md lg:text-xl font-bold">{{aarecord.additional.top_box.title}}</h3>
            <div class="truncate leading-[1.2] lg:leading-[1.35] max-lg:text-xs">{{aarecord.additional.top_box.publisher_and_edition}}</div>
            <div class="max-lg:line-clamp-[2] lg:truncate leading-[1.2] lg:leading-[1.35] max-lg:text-sm italic">{{aarecord.additional.top_box.author}}</div>
            {% if aarecord.file_unified_data.has_meaningful_problems %}<div class="text-xs lg:text-sm">{{ gettext('page.search.results.issues') }}</div>{% endif %}
            <div class="hidden">base score: {{ aarecord.search_only_fields.search_score_base_rank }}, final score: {{ aarecord._score }}</div>
          </div>
        </a>
        {% if loop.index0 > max_show_immediately %}-->{% endif %}
      </div>
    {% endfor %}
  {% endif %}
{%- endmacro %}
