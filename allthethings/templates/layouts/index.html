<!doctype html>
<html lang="{{ g.full_lang_code }}">
  <head>
    <meta charset="utf-8">
    <title>{% if self.title() %}{% block title %}{% endblock %} - {% endif %}{{ gettext('layout.index.title') }}</title>
    {% block darkreader_code_block %}
      <script type="text/javascript">{{ g.darkreader_code | safe }}</script>
    {% endblock %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/app.css') }}">
    {% if self.stylesheets() %}
      {% block stylesheets %}{% endblock %}
    {% endif %}
    {% if g.domain_lang_code in ['ar', 'fa', 'he', 'ur'] %}
      <!-- <style>body { direction: rtl; } </style> -->
    {% endif %}
    <script defer src="{{ url_for('static', filename='js/app.js') }}"></script>
    {% if self.meta_tags() %}
      {% block meta_tags %}{% endblock %}
    {% else %}
      <meta name="description" content="{{ gettext('layout.index.meta.description') }}" />
    {% endif %}
    <meta name="twitter:card" value="summary" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">
    <link rel="search" href="{{ url_for('static', filename='content-search.xml') }}" type="application/opensearchdescription+xml" title="{{ gettext('layout.index.meta.opensearch') }}" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <script>
      window.globalUpdateAaLoggedIn = function(aa_logged_in) {
        localStorage['aa_logged_in'] = aa_logged_in;
        if (localStorage['aa_logged_in'] === '1') {
          document.documentElement.classList.add("aa-logged-in");
        } else {
          document.documentElement.classList.remove("aa-logged-in");
        }
      }
      window.globalUpdateAaLoggedIn(localStorage['aa_logged_in'] || 0);

      // Focus search field when pressing "/".
      document.addEventListener("keydown", e => {
        if (e.key !== "/" || e.ctrlKey || e.metaKey || e.altKey) return;
        if (/^(?:input|textarea|select|button)$/i.test(e.target.tagName)) return;
        e.preventDefault();
        const fields = document.querySelectorAll('.js-slash-focus');
        const field = fields[fields.length - 1];
        if (field) {
          field.select();
          field.scrollIntoView({ block: "center", inline: "center" });
        }
      });
    </script>
  </head>
  <body>
    <script>
      (function() {
        if (location.hostname.includes('localhost')) {
          location.hostname = location.hostname.replace('localhost', 'localtest.me');
          return;
        }

        var langCodes = [{% for lang_code, _lang_name, _lang_name_current_locale in g.languages %}{{ lang_code | tojson }}, {% endfor %}];

        var domainPosition = 0;
        var potentialSubDomainLangCode = location.hostname.split(".")[0];
        var subDomainLangCode = 'en';
        if (langCodes.includes(potentialSubDomainLangCode) || potentialSubDomainLangCode === 'www') {
          domainPosition = potentialSubDomainLangCode.length + 1;
          if (potentialSubDomainLangCode !== 'www') {
            subDomainLangCode = potentialSubDomainLangCode;
          }
        }

        window.baseDomain = location.hostname.substring(domainPosition);

        function setLangCookie(langCode) {
          if (!langCodes.includes(langCode)) {
            return;
          }
          document.cookie = 'selected_lang=' + langCode + ';path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT;domain=' + window.baseDomain;
        }

        function redirectLang(langCode) {
          if (!langCodes.includes(langCode)) {
            return;
          }
          var prefix = '';
          if (langCode != 'en') {
            prefix = langCode + '.';
          }
          location.hostname = prefix + window.baseDomain;
        }

        window.handleChangeLang = function(event) {
          const langCode = event.target.value;
          setLangCookie(langCode);
          redirectLang(langCode);
        };

        // Let's also (for now) not set a cookie when getting referred.
        // {
        //   // If our referrer was (likely) a different domain of our website (with the same lang code),
        //   // then behave as if that lang code was set as a cookie all along.
        //   if (document.referrer.includes("://" + subDomainLangCode + ".")) {
        //     setLangCookie(subDomainLangCode);
        //   }
        // }

        // Browser-based language detection is too unreliable.
        // Disable for now.
        // {
        //   const cookieLangMatch = document.cookie.match(/selected_lang=([^$ ;}]+)/);
        //   // If there's no cookie yet, let's try to set one.
        //   if (!cookieLangMatch) {
        //     // See if the user's browser language is one that we support directly.
        //     for (const langCode of navigator.languages) {
        //       let domainLangCode = langCode;
        //       if (langCode.toLowerCase().includes("-hant") || langCode.toLowerCase().includes("-tw")) {
        //         domainLangCode = "tw";
        //       }

        //       // Take the first language that we support.
        //       if (langCodes.includes(domainLangCode)) {
        //         setLangCookie(domainLangCode);
        //         // Bail out so we don't redirect to a suboptimal language.
        //         break;
        //       }
        //     }
        //   }
        // }

        {
          const cookieLangMatch = document.cookie.match(/selected_lang=([^$ ;}]+)/);
          if (cookieLangMatch) {
            // Refresh cookie with a new expiry, in case the browser has
            // restricted it.
            var explicitlyRequestedLangCode = cookieLangMatch[1];
            setLangCookie(explicitlyRequestedLangCode);

            // If a cookie is set, that we want to go to the language, so let's redirect.
            if (explicitlyRequestedLangCode != subDomainLangCode) {
              redirectLang(explicitlyRequestedLangCode);
            }
          }
        }

        window.submitForm = function(event, url, handler) {
          event.preventDefault();

          const currentTarget = event.currentTarget;
          const fieldset = currentTarget.querySelector("fieldset");
          currentTarget.querySelector(".js-failure").classList.add("hidden");

          // Before disabling the fieldset.
          fetch(url, { method: "PUT", body: new FormData(currentTarget) })
            .then(function(response) {
              if (!response.ok) { throw "error"; }
              return response.json().then(function(jsonResponse) {
                fieldset.classList.add("hidden");
                currentTarget.querySelector(".js-success").classList.remove("hidden");
                if (handler) {
                  handler(jsonResponse);
                }
              });
            })
            .catch(function() {
              fieldset.removeAttribute("disabled", "disabled");
              fieldset.style.opacity = 1;
              currentTarget.querySelector(".js-failure").classList.remove("hidden");
            })
            .finally(function() {
              currentTarget.querySelector(".js-spinner").classList.add("invisible");
            });

          fieldset.setAttribute("disabled", "disabled");
          fieldset.style.opacity = 0.5;
          currentTarget.querySelector(".js-spinner").classList.remove("invisible");
        };
      })();
    </script>
    {% block main %}
      <div class="header" role="navigation">
        <div>
          <!-- TODO:Temporary extra -->
          <!-- blue -->
          <!-- <div class="bg-[#0195ff] hidden js-top-banner">
            <div class="max-w-[1050px] mx-auto px-4 py-2 text-[#fff] flex justify-between">
              <div>
                📄 New blog post: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/all-isbns.html">Visualizing All ISBNs — $10k by 2025-01-31</a>
              </div>
              <div>
                <a href="#" class="custom-a ml-2 text-[#fff] hover:text-[#ddd] js-top-banner-close">✕</a>
              </div>
            </div>
          </div> -->
          {% if g.is_membership_double %}
            <div class="bg-[#ff005b] hidden js-fundraiser-banner">
              <div class="max-w-[1050px] mx-auto px-4 py-2 text-[#fff] flex justify-center">
                <div>
                  <div>
                    {{ gettext('layout.index.header.banner.fundraiser.help') }}
                    {{ gettext('layout.index.header.banner.fundraiser.takedown') }}
                  </div>
                  <div style="position: relative; height: 16px; margin-top: 16px; margin-bottom: 16px">
                    <div style="position: absolute; left: 0; right: 0; top: 0; bottom: 0; background: white; overflow: hidden; border-radius: 16px; box-shadow: 0px 2px 4px 0px #00000038">
                      <div style="position: absolute; left: 0; top: 0; bottom: 0; width: {{ [100, (g.fraction_of_the_month+0.05)*100] | min | round }}%; background: #0095ff"></div>
                    </div>
                    <div style="position: absolute; left: {{ [100, (g.fraction_of_the_month+0.05)*100] | min | round }}%; top: 50%; width: 16px; height: 16px; transform: translate(-50%, -50%)">
                      <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: #0095ff66; border-radius: 100%; animation: header-ping 1.5s cubic-bezier(0,0,.2,1) infinite"></div>
                      <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: white; border-radius: 100%; box-shadow: 0 0 3px #00000069;"></div>
                    </div>
                  </div>
                  <div>➡️ {{ gettext('layout.index.header.banner.fundraiser.now') | replace('<strong>' | safe, '<strong style="color:#a0d7ff">' | safe) }} {{ gettext('layout.index.header.banner.fundraiser.valid_end_of_month') }} <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/donate">{{ gettext('layout.index.header.nav.donate') }}</a></div>

                  <!-- <div>
                    {{ gettext('layout.index.header.banner.issues.partners_closed') }}
                    {{ gettext('layout.index.header.banner.issues.memberships_extended') }}
                  </div>
                  <div>➡️ {{ gettext('layout.index.header.banner.fundraiser.this_month') | replace('<strong>' | safe, '<strong style="color:#a0d7ff">' | safe) }} <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/donate">{{ gettext('layout.index.header.nav.donate') }}</a></div> -->
                </div>
                <div>
                  <a href="#" class="custom-a ml-2 text-[#fff] hover:text-[#ddd] js-fundraiser-banner-close">✕</a>
                </div>
              </div>
            </div>
          {% else %}
            <!-- <div class="[html:not(.aa-logged-in)_&]:hidden"> -->
            {% if g.domain_lang_code == 'zh' %}
              <!-- blue -->
              <div class="bg-[#0195ff] hidden js-top-banner">
                <div class="max-w-[1050px] mx-auto px-4 py-2 text-[#fff] flex justify-between">
                  <div>
                    <!-- GFW, payment processors, ads -->
                    <!-- 我们正在寻找专业服务，可以帮助可靠地绕过GFW，例如通过设置定期更改的代理和域名，或其他技巧。如果您确实具有此方面的实际专业经验，请与我们联系。<strong>我们愿意为此付出代价。</strong><a class="custom-a text-[#fff] hover:text-[#ddd] underline text-xs" href="/contact">{{ gettext('page.contact.title') }}</a> -->
                    <!-- payment processors, ads -->
                    <!-- 我们还在寻找能够让我们保持匿名的专业支付宝/微信支付处理器，使用加密货币。此外，我们正在寻找希望放置小而别致广告的公司。 -->
                    <!-- payment processors -->
                    <!-- 我们还在寻找能够让我们保持匿名的专业支付宝/微信支付处理器，使用加密货币。 <a class="custom-a text-[#fff] hover:text-[#ddd] underline text-xs" href="/contact">{{ gettext('page.contact.title') }}</a> -->
                    <!-- long live annas-archive.li -->
                    <!-- ❌ 更新您的书签吧:annas-archive.org 已不复存在,欢迎访问annas-archive.li! 🎉 -->
                    📄 新博客文章: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/critical-window-chinese.html">海盗图书馆的关键时期</a>
                  </div>
                  <div>
                    <a href="#" class="custom-a ml-2 text-[#fff] hover:text-[#ddd] js-top-banner-close">✕</a>
                  </div>
                </div>
              </div>
            {% else %}
              <!-- blue -->
              <div class="bg-[#0195ff] hidden js-top-banner">
                <div class="max-w-[1050px] mx-auto px-4 py-2 text-[#fff] flex justify-between">
                  <!-- <div>
                    🎄 <strong>{{ gettext('layout.index.header.banner.holiday_gift') }}</strong> ❄️ {{ gettext('layout.index.header.banner.surprise') }} <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/donate">{{ gettext('layout.index.header.nav.donate') }}</a>
                  </div> -->
                  <!-- <div>
                    {{ gettext('layout.index.header.banner.mirrors') }} <a class="custom-a text-[#fff] hover:text-[#ddd] underline text-xs" href="/mirrors">{{ gettext('layout.index.header.learn_more') }}</a>
                  </div> -->
                  <!-- <div>
                    ❌ Update your bookmarks: annas-archive.org is no more, long live annas-archive.li! 🎉
                  </div> -->
                  <!-- <div>
                    {{ gettext('layout.index.header.banner.valentine_gift') }} {{ gettext('layout.index.header.banner.refer', percentage=50) }} <a class="custom-a text-[#fff] hover:text-[#ddd] underline text-xs" href="/refer">{{ gettext('layout.index.header.learn_more') }}</a>
                  </div> -->
                  <!-- <div>
                    📄 New blog post: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/critical-window.html">The critical window of shadow libraries</a> — <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="https://torrentfreak.com/annas-archive-loses-gs-domain-name-but-remains-resilient-240718/">TorrentFreak coverage</a>
                  </div> -->
                  <!-- <div>
                    📄 New blog post: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/ai-copyright.html">Copyright reform is necessary for national security</a>
                  </div> -->
                  <!-- <div>
                    We’re looking for <strong>experienced</strong> Amazon gift card resellers. Please <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/contact">contact us</a> with details (years in the business, price, max monthly volume, domains and amounts supported).
                  </div> -->
                  <div>
                    📄 New blog post: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/all-isbns-winners.html">Winners of the ISBN visualization bounty</a>
                  </div>
                  <div>
                    <a href="#" class="custom-a ml-2 text-[#fff] hover:text-[#ddd] js-top-banner-close">✕</a>
                  </div>
                </div>
              </div>
            {% endif %}


            <!-- blue -->
            <!-- <div class="bg-[#0195ff] hidden js-top-banner">  -->
            <!-- purple -->
            <!-- <div class="bg-[#7f01ff] hidden js-top-banner">  -->
            <!-- <div class="hidden js-top-banner text-xs sm:text-base [html:not(.aa-logged-in)_&]:hidden"> -->
              <!-- <div>
                {{ gettext('layout.index.header.banner.new_donation_method', method_name=('<strong>Paypal</strong>' | safe), donate_link_open_tag=('<a href="/donate" class="custom-a text-[#fff] hover:text-[#ddd] underline">' | safe)) }}
              </div> -->
              <!-- <div>
                We now have a <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="https://t.me/+D0zemuNzEdgyOGVk">Telegram</a> channel. Join us and discuss the future of Anna’s Archive.<br/>You can still also follow us on <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="https://www.reddit.com/r/Annas_Archive">Reddit</a>.
              </div> -->
              <!-- <div class="max-w-[1050px] mx-auto px-4 py-2">
                <div class="flex justify-between mb-2">
                  <div>{{ gettext('layout.index.banners.comics_fundraiser.text') | replace ('https://annas-blog.org', '/blog') | replace('https://t.me/annasarchiveorg', 'https://t.me/+D0zemuNzEdgyOGVk') }}</div>
                  <div><a href="#" class="custom-a text-[#777] hover:text-black js-top-banner-close">✕</a></div>
                </div>
                <div style="background: #fff; padding: 8px; border-radius: 8px; box-shadow: 0px 2px 4px 0px #00000020">
                  {% include 'macros/fundraiser.html' %}
                </div>
              </div> -->
              <!-- <div class="max-w-[1050px] mx-auto px-4 py-2 text-[#fff] flex justify-between bg-[#0160a7]">
                <div>
                  Do you know experts in <strong>anonymous merchant payments</strong>? Can you help us add more convenient ways to donate? PayPal, Alipay, credit cards, gift cards. Please <a class="custom-a text-[#fff] hover:text-[#ddd] underline break-all" href="/contact">contact us</a>.
                </div>
                <div>
                  <a href="#" class="custom-a text-[#fff] hover:text-[#ddd] js-top-banner-close">✕</a>
                </div>
              </div> -->
              <!-- <div class="max-w-[1050px] mx-auto text-[#fff] bg-[#0160a7]">
                <div class="flex justify-between">
                  <div class="px-4 py-2">
                    New technical blog post: <a class="custom-a text-[#fff] hover:text-[#ddd] underline" href="/blog/annas-archive-containers.html">Anna’s Archive Containers (AAC): standardizing releases from the world’s largest shadow library</a>
                  </div>
                  <div class="px-4 py-2">
                    <a href="#" class="custom-a text-[#fff] hover:text-[#ddd] js-top-banner-close">✕</a>
                  </div>
                </div>
                <div class="px-4 py-2 bg-green-500">
                  Do you know experts in <strong>anonymous merchant payments</strong>? Can you help us add more convenient ways to donate? PayPal, Alipay, credit cards, gift cards. Please <a class="custom-a text-[#fff] hover:text-[#ddd] underline break-all" href="/contact">contact us</a>.
                </div>
              </div> -->
            <!-- </div> -->
          {% endif %}
          <script>
            (function() {
              if (document.querySelector('.js-top-banner')) {
                var latestTopBannerType = '19';
                var topBannerMatch = document.cookie.match(/top_banner_hidden=([^$ ;}]+)/);
                var topBannerType = '';
                if (topBannerMatch) {
                  topBannerType = topBannerMatch[1];
                  // Refresh cookie.
                  document.cookie = 'top_banner_hidden=' + topBannerType + ';path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT';
                }
                if (topBannerType !== latestTopBannerType) {
                  document.querySelector('.js-top-banner').style.display = 'block';
                  document.querySelector('.js-top-banner-close').addEventListener('click', function(event) {
                    document.querySelector('.js-top-banner').style.display = 'none';
                    document.cookie = 'top_banner_hidden=' + latestTopBannerType + ';path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT';
                    event.preventDefault();
                    return false;
                  });
                }
              }
              if (document.querySelector('.js-fundraiser-banner')) {
                var latestFundraiserBannerType = '2';
                var fundraiserBannerMatch = document.cookie.match(/fundraiser_banner_hidden=([^$ ;}]+)/);
                var fundraiserBannerType = '';
                if (fundraiserBannerMatch) {
                  fundraiserBannerType = fundraiserBannerMatch[1];
                  // Refresh cookie.
                  document.cookie = 'fundraiser_banner_hidden=' + fundraiserBannerType + ';path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT';
                }
                if (fundraiserBannerType !== latestFundraiserBannerType) {
                  document.querySelector('.js-fundraiser-banner').style.display = 'block';
                  document.querySelector('.js-fundraiser-banner-close').addEventListener('click', function(event) {
                    document.querySelector('.js-fundraiser-banner').style.display = 'none';
                    document.cookie = 'fundraiser_banner_hidden=' + latestFundraiserBannerType + ';path=/;expires=Fri, 31 Dec 9999 23:59:59 GMT';
                    event.preventDefault();
                    return false;
                  });
                }
              }
            })();
          </script>
        </div>
        <div class="header-inner">
          <div class="header-inner-top">
            <a href="/" class="custom-a text-black hover:text-[#444]"><h1 class="text-2xl sm:text-4xl">{{ gettext('layout.index.header.title') }}</h1></a>
            
            <div class="flex gap-1 items-center">
              <button
                id="dark-button"
                type="button"
                class="bg-white text-md w-[24px] h-[24px] flex text-center rounded items-center justify-center text-gray-500"
                onclick="window.handleThemeSwitch('dark')"
              ><span class="icon-[ph--moon-bold]"></button>
              <button
                id="light-button"
                type="button"
                class="hidden bg-white text-md w-[24px] h-[24px] flex text-center rounded items-center justify-center text-gray-500"
                onclick="window.handleThemeSwitch('light')"
              ><span class="icon-[ph--sun-bold]"></button>
              
              <select class="text-md bg-center icon-[twemoji--globe-with-meridians] py-1 rounded text-gray-500 max-w-[50px] h-[24px] w-[24px] appearance-none bg-white" style="background-size: 1em;" onchange="handleChangeLang(event)">
                <option></option>
                {% for lang_code, lang_name, lang_name_current_locale in g.languages %}
                  <option value="{{ lang_code }}">{{ lang_code }} - {{ lang_name }}{% if lang_name_current_locale %} - {{ lang_name_current_locale }}{% endif %}{% if lang_code == g.domain_lang_code %} ☑️{% endif %}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <div class="mb-1.5">
            <div class="max-md:hidden">{{ g.header_tagline | safe }} <a class="text-xs" href="/faq">{{ gettext('layout.index.header.learn_more') }}</a></div>
            <div class="max-sm:hidden md:hidden">{{ g.header_tagline_mid | safe }} <a class="text-xs" href="/faq">{{ gettext('layout.index.header.learn_more') }}</a></div>
            <div class="sm:hidden text-sm">{{ g.header_tagline_short | safe }} <a class="text-xs" href="/faq">{{ gettext('layout.index.header.learn_more') }}</a></div>
          </div>

          <!-- <div style="position: relative; height: 16px; margin-top: 16px; margin-bottom: 16px;">
            <div style="position: absolute; left: 0; right: 0; top: 0; bottom: 0; background: white; overflow: hidden; border-radius: 16px; box-shadow: 0px 2px 4px 0px #00000038">
              <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 5%; background: #0095ff"></div>
              <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 5%; background: #0095ff"></div>
            </div>
          </div> -->

          <div class="hidden sm:flex text-xs mb-1 js-recent-downloads-container" aria-hidden="true">
            <!-- TODO:TRANSLATE ("Hide") -->
            <div class="font-bold shrink-0"><a href="#" title="Hide" class="js-recent-downloads-hide">✕</a> {{ gettext('layout.index.header.recent_downloads') }}&nbsp;&nbsp;</div>
            <div class="w-full overflow-hidden flex js-recent-downloads-scroll">
              <!-- Make sure tailwind picks up these classes -->
              <div class="shrink-0 min-w-full"></div>
              <div class="inline-block max-w-[50%] truncate"></div>
            </div>
            <script>
              (function() {
                if (!localStorage || localStorage.hideRecentDownloads) {
                  const removeEl = document.querySelector('.js-recent-downloads-container');
                  removeEl.parentNode.removeChild(removeEl);
                  return;
                }

                document.querySelector('.js-recent-downloads-hide').addEventListener('click', function(clickEl) {
                  if (localStorage) {
                    localStorage.hideRecentDownloads = true;
                  }
                  const removeEl = document.querySelector('.js-recent-downloads-container');
                  removeEl.parentNode.removeChild(removeEl);
                });

                function showRecentDownloads(items) {
                  // Biased towards initial positions, but whatever.
                  const shuffledItems = [...items].sort(() => Math.random() - 0.5).slice(0, 8);

                  const titlesLength = shuffledItems.map((item) => item.title).join("   ").length;
                  const scrollHtml = `<div class="shrink-0 min-w-full" style="animation: scroll ${Math.round(titlesLength/4)}s linear infinite">` + shuffledItems.map((item) => `<span class="inline-block truncate">&nbsp;•&nbsp;</span><a tabindex="-1" href="${(item.path[0] == '/' ? '' : '/') + item.path}" class="inline-block max-w-[50%] truncate">${item.title}</a>`).join('') + '</div>';
                  document.querySelector('.js-recent-downloads-scroll').innerHTML = scrollHtml + scrollHtml;
                }

                function fetchNewRecentDownloads(cb) {
                  setTimeout(() => {
                    fetch("/dyn/recent_downloads/").then((response) => response.json()).then((items) => {
                      if (localStorage) {
                        localStorage.recentDownloadsData = JSON.stringify({ items, time: Date.now() });
                      }
                      if (cb) {
                        cb(items);
                      }
                    });
                  }, 100);
                }

                if (localStorage && localStorage.recentDownloadsData) {
                  const recentDownloadsData = JSON.parse(localStorage.recentDownloadsData);
                  // console.log('recentDownloadsData', recentDownloadsData);
                  showRecentDownloads(recentDownloadsData.items);

                  const timeToRefresh = 65000 /* 65 sec */ - (Date.now() - recentDownloadsData.time);
                  // Fetch new data for the next page load.
                  if (timeToRefresh <= 0) {
                    fetchNewRecentDownloads(undefined);
                  } else {
                    setTimeout(() => {
                      fetchNewRecentDownloads(undefined);
                    }, timeToRefresh);
                  }
                } else {
                  fetchNewRecentDownloads((items) => {
                    showRecentDownloads(items);
                  });
                }
              })();
            </script>
          </div>

          <script>
            function topMenuToggle(event, className) {
              const el = document.querySelector("." + className);
              if (el.style.display === "block") {
                el.style.display = "none";
                el.setAttribute('aria-expanded', "false");
              } else {
                el.style.display = "block";
                el.setAttribute('aria-expanded', "true");
                function clickOutside(innerEvent) {
                  if (!el.contains(innerEvent.target)) {
                    el.style.display = "none";
                    el.setAttribute('aria-expanded', "false")
                    document.removeEventListener('click', clickOutside);
                    innerEvent.preventDefault();
                    return false;
                  }
                }
                setTimeout(function() {
                  document.addEventListener('click', clickOutside);
                }, 0);
              }
              event.preventDefault();
              return false;
            }
          </script>
         

          <div class="header-bar" data-testid="header-bar">
            <div class="header-links relative z-20">
              <a href="#" aria-expanded="false" onclick="topMenuToggle(event, 'js-top-menu-home')" class="header-link-first {{ 'header-link-active' if header_active.startswith('home') }}" style="margin-right: 24px;">
                <span class="header-link-normal">
                  {% if header_active == 'home/search' %}{{ gettext('layout.index.header.nav.search') }}
                  {% elif header_active == 'home/scidb' %}🧬&nbsp;{{ gettext('page.home.scidb.header') }}
                  {% elif header_active == 'home/faq' %}{{ gettext('layout.index.header.nav.faq') }}
                  {% elif header_active == 'home/metadata' %}{{ gettext('layout.index.header.nav.improve_metadata') }}
                  {% elif header_active == 'home/volunteering' %}{{ gettext('layout.index.header.nav.volunteering') }}
                  {% elif header_active == 'home/datasets' %}{{ gettext('layout.index.header.nav.datasets') }}
                  {% elif header_active == 'home/torrents' %}{{ gettext('layout.index.header.nav.torrents') }}
                  {% elif header_active == 'home/activity' %}{{ gettext('layout.index.header.nav.activity') }}
                  {% elif header_active == 'home/codes' %}{{ gettext('layout.index.header.nav.codes') }}
                  {% elif header_active == 'home/llm' %}{{ gettext('layout.index.header.nav.llm_data') }}
                  {% else %}{{ gettext('layout.index.header.nav.home') }}{% endif %}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
                <span class="header-link-bold">
                  {% if header_active == 'home/search' %}{{ gettext('layout.index.header.nav.search') }}
                  {% elif header_active == 'home/scidb' %}🧬&nbsp;{{ gettext('page.home.scidb.header') }}
                  {% elif header_active == 'home/faq' %}{{ gettext('layout.index.header.nav.faq') }}
                  {% elif header_active == 'home/metadata' %}{{ gettext('layout.index.header.nav.improve_metadata') }}
                  {% elif header_active == 'home/volunteering' %}{{ gettext('layout.index.header.nav.volunteering') }}
                  {% elif header_active == 'home/datasets' %}{{ gettext('layout.index.header.nav.datasets') }}
                  {% elif header_active == 'home/torrents' %}{{ gettext('layout.index.header.nav.torrents') }}
                  {% elif header_active == 'home/activity' %}{{ gettext('layout.index.header.nav.activity') }}
                  {% elif header_active == 'home/codes' %}{{ gettext('layout.index.header.nav.codes') }}
                  {% elif header_active == 'home/llm' %}{{ gettext('layout.index.header.nav.llm_data') }}
                  {% else %}{{ gettext('layout.index.header.nav.home') }}{% endif %}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
              </a>
              <div class="absolute left-0 top-full bg-[#f2f2f2] px-4 shadow js-top-menu-home hidden">
                <a class="custom-a block py-1 {% if header_active == 'home/home'         %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/">{{ gettext('layout.index.header.nav.home') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/search'       %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/search">{{ gettext('layout.index.header.nav.search') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/scidb'        %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/scidb">🧬&nbsp;{{ gettext('page.home.scidb.header') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/faq'          %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/faq">{{ gettext('layout.index.header.nav.faq') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/metadata'     %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/metadata">{{ gettext('layout.index.header.nav.improve_metadata') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/volunteering' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/volunteering">{{ gettext('layout.index.header.nav.volunteering') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/datasets'     %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/datasets">{{ gettext('layout.index.header.nav.datasets') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/torrents'     %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/torrents">{{ gettext('layout.index.header.nav.torrents') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/activity'     %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/activity">{{ gettext('layout.index.header.nav.activity') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/codes'        %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/member_codes">{{ gettext('layout.index.header.nav.codes') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'home/isbn-visualization' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/isbn-visualization/">ISBN Visualization<!-- TODO:TRANSLATE --></a>
                <a class="custom-a block py-1 {% if header_active == 'home/llm'          %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/llm">{{ gettext('layout.index.header.nav.llm_data') }}</a>
                <a class="custom-a block py-1 text-black/64 hover:text-black" href="/blog" target="_blank">{{ gettext('layout.index.header.nav.annasblog') }}</a>
                <a class="custom-a block py-1 text-black/64 hover:text-black" href="https://software.annas-archive.li" target="_blank">{{ gettext('layout.index.header.nav.annassoftware') }}</a>
                <a class="custom-a block py-1 text-black/64 hover:text-black" href="https://translate.annas-archive.li" target="_blank">{{ gettext('layout.index.header.nav.translate') }}</a>
              </div>
              <a href="/donate" class="{{ 'header-link-active' if header_active == 'donate' }}"><span class="header-link-normal">{{ gettext('layout.index.header.nav.donate') }}{% if g.is_membership_double %} <span class="ml-1 text-xs bg-[#ff005b] text-white px-1 rounded align-[1px]">x2</span>{% endif %}</span><span class="header-link-bold">{{ gettext('layout.index.header.nav.donate') }}{% if g.is_membership_double %} <span class="ml-1 text-xs bg-[#ff005b] text-white px-1 rounded align-[1px]">x2</span>{% endif %}</span></a>
            </div>
            {% if not g.hide_search_bar %}
              <form class="header-search hidden sm:flex" action="/search" method="get" role="search">
                <input class="js-slash-focus rounded" name="q" type="search" placeholder="{{ gettext('common.search.placeholder') }}" value="{{search_input}}" title="Focus: '/' Scroll search results: 'j', 'k'">
              </form>
            {% endif %}
            <div class="header-links header-links-right relative z-10 ml-auto items-center">
              <!-- <div class="mr-1 bg-[#0195ff] text-white text-xs font-medium px-1 py-0.5 rounded">{{ gettext('layout.index.header.nav.beta') }}</div> -->
              <a href="#" aria-expanded="false" onclick="topMenuToggle(event, 'js-top-menu-login')" class="header-link-first {{ 'header-link-active' if header_active.startswith('account') }} [html.aa-logged-in_&]:hidden">
                <span class="header-link-normal">
                  {{ gettext('layout.index.header.nav.login_register') }}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
                <span class="header-link-bold">
                  {{ gettext('layout.index.header.nav.login_register') }}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
              </a>
              <div class="absolute right-0 top-full bg-[#f2f2f2] px-4 shadow js-top-menu-login hidden">
                <a class="custom-a block py-1 {% if header_active == 'account' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/login">{{ gettext('layout.index.header.nav.login_register') }}</a>
              </div>
              <a href="#" aria-expanded="false" onclick="topMenuToggle(event, 'js-top-menu-account')" class="header-link-first {{ 'header-link-active' if header_active.startswith('account') }} [html:not(.aa-logged-in)_&]:hidden" style="margin-right: 8px;">
                <span class="header-link-normal">
                  {% if header_active == 'account/profile' %}{{ gettext('layout.index.header.nav.public_profile') }}
                  {% elif header_active == 'account/downloaded' %}{{ gettext('layout.index.header.nav.downloaded_files') }}
                  {% elif header_active == 'account/donations' %}{{ gettext('layout.index.header.nav.my_donations') }}
                  {% else %}{{ gettext('layout.index.header.nav.account') }}{% endif %}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
                <span class="header-link-bold">
                  {% if header_active == 'account/profile' %}{{ gettext('layout.index.header.nav.public_profile') }}
                  {% elif header_active == 'account/downloaded' %}{{ gettext('layout.index.header.nav.downloaded_files') }}
                  {% elif header_active == 'account/donations' %}{{ gettext('layout.index.header.nav.my_donations') }}
                  {% else %}{{ gettext('layout.index.header.nav.account') }}{% endif %}
                  <span class="icon-[material-symbols--arrow-drop-down] absolute text-lg mt-[3px] -ml-px"></span>
                </span>
              </a>
              <div class="absolute right-0 top-full bg-[#f2f2f2] px-4 shadow js-top-menu-account hidden">
                <a class="custom-a block py-1 {% if header_active == 'account' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/account">{{ gettext('layout.index.header.nav.account') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'account/profile' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/account/profile">{{ gettext('layout.index.header.nav.public_profile') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'account/downloaded' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/account/downloaded">{{ gettext('layout.index.header.nav.downloaded_files') }}</a>
                <a class="custom-a block py-1 {% if header_active == 'account/donations' %}font-bold text-black{% else %}text-black/64{% endif %} hover:text-black" href="/account/donations">{{ gettext('layout.index.header.nav.my_donations') }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <main class="main">{% if g.live_debug %}<div class="bg-red-100 p-2 inline-block mb-4 text-xs font-bold rounded">⚙️ LIVE/DEBUG version of this page. 🔨</div>{% endif %}{% block body %}{% endblock %}</main>
      <footer class="bg-black/5 text-[#777]" style="box-shadow: 0px 0px 7px rgb(0 0 0 / 30%)">
        <div class="max-w-[1050px] mx-auto p-[12px] leading-relaxed flex flex-wrap">
          <div class="mr-4 mb-4 grow">
            <strong class="font-bold text-black">{{ gettext('layout.index.footer.list1.header') }}</strong><br>
            <a class="custom-a hover:text-[#333]" href="/">{{ gettext('layout.index.header.nav.home') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/search">{{ gettext('layout.index.header.nav.search') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/scidb">🧬&nbsp;{{ gettext('page.home.scidb.header') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/faq">{{ gettext('layout.index.header.nav.faq') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/donate">{{ gettext('layout.index.header.nav.donate') }}</a><br>
            <select class="p-1 rounded text-gray-500 mt-1 max-w-[110px]" onchange="handleChangeLang(event)">
              {% for lang_code, lang_name, lang_name_current_locale in g.languages %}
                {% if g.domain_lang_code == lang_code %}
                  <option value="{{ lang_code }}">🌐 {{ lang_code }} - {{ lang_name }}{% if lang_name_current_locale %} - {{ lang_name_current_locale }}{% endif %}</option>
                {% endif %}
              {% endfor %}
              {% for lang_code, lang_name, lang_name_current_locale in g.languages %}
                <option value="{{ lang_code }}">{{ lang_code }} - {{ lang_name }}{% if lang_name_current_locale %} - {{ lang_name_current_locale }}{% endif %}{% if lang_code == g.domain_lang_code %} ☑️{% endif %}</option>
              {% endfor %}
            </select>
          </div>

          <div class="mr-4 mb-4 grow grow">
            <strong class="font-bold text-black">{{ gettext('layout.index.footer.list2.header') }}</strong><br>
            <a class="custom-a hover:text-[#333]" href="/contact">{{ gettext('page.contact.title') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/copyright">{{ gettext('layout.index.footer.list2.dmca_copyright') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="https://www.reddit.com/r/Annas_Archive">{{ gettext('layout.index.footer.list2.reddit') }}</a> / <a class="custom-a hover:text-[#333]" href="https://matrix.to/#/#annas:archivecommunication.org">Matrix</a><!-- TODO:TRANSLATE --><br>
            <a class="custom-a hover:text-[#333]" href="/blog">{{ gettext('layout.index.header.nav.annasblog') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="https://software.annas-archive.li">{{ gettext('layout.index.header.nav.annassoftware') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="https://translate.annas-archive.li">{{ gettext('layout.index.header.nav.translate') }}</a><br>
          </div>

          <div class="mr-4 mb-4 grow">
            <strong class="font-bold text-black">{{ gettext('layout.index.header.nav.advanced') }}</strong><br>
            <a class="custom-a hover:text-[#333]" href="/faq">{{ gettext('layout.index.header.nav.faq') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/metadata">{{ gettext('layout.index.header.nav.improve_metadata') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/volunteering">{{ gettext('layout.index.header.nav.volunteering') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/datasets">{{ gettext('layout.index.header.nav.datasets') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/torrents">{{ gettext('layout.index.header.nav.torrents') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/activity">{{ gettext('layout.index.header.nav.activity') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/member_codes">{{ gettext('layout.index.header.nav.codes') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/isbn-visualization/">ISBN Visualization<!-- TODO:TRANSLATE --></a><br>
            <a class="custom-a hover:text-[#333]" href="/llm">{{ gettext('layout.index.header.nav.llm_data') }}</a><br>
            <a class="custom-a hover:text-[#333]" href="/faq#security">{{ gettext('layout.index.header.nav.security') }}</a><br>
          </div>

          <div class="grow">
            <strong class="font-bold text-black">{{ gettext('layout.index.footer.list3.header') }}</strong><br>
            <a class="custom-a hover:text-[#333] js-annas-archive-li" href="https://annas-archive.li">annas-archive.li</a><br>
            <a class="custom-a hover:text-[#333] js-annas-archive-se" href="https://annas-archive.se">annas-archive.se</a><br>
            <a class="custom-a hover:text-[#333] js-annas-archive-org" href="https://annas-archive.org">annas-archive.org</a><br>
            <a class="custom-a hover:text-[#333]" href="https://open-slum.org/">{{ gettext('layout.index.footer.list3.link.slum', unaffiliated=((('<span class="text-xs">[' | safe) + gettext('layout.index.footer.list3.link.unaffiliated')) + (']</span>' | safe)) | safe) | replace('(','') | replace(')', '') }}</a><br>
          </div>
        </div>
      </footer>
    {% endblock %}
    <script>
      (function() {
        // Possible domains we can encounter:
        const validDomains = [
          {% for domain in g.valid_other_domains %}
              {% if 'localhost' not in domain %}
              {% if '-' in domain %}
                {% set domain_parts = domain.split('-') %}
                "{{ domain_parts[0] }}-" + "{{ domain_parts[1] }}",
              {% else %}
                "{{ domain }}",
              {% endif %}
              {% endif %}
          {% endfor %}
        ];

        const domainsToReplace = [...validDomains, window.baseDomain];
        // For checking and redirecting if our current host is down (but if Cloudflare still responds).
        const initialCheckMs = 0;
        const intervalCheckOtherDomains = 10000;
        const domainsToNavigateTo = validDomains;
        // For testing:
        // const domainsToNavigateTo = ["localtest.me:8000", "testing_redirects.localtest.me:8000"];

        // const isInvalidDomain = false;
        // const isInvalidDomain = true;
        const isInvalidDomain = !validDomains.includes(window.baseDomain);
        if (isInvalidDomain) {
          console.log("Invalid domain");
          // If the domain is invalid, replace window.baseDomain first, in case the domain
          // is something weird like 'weird.annas-archive.li'.
          domainsToReplace.unshift(window.baseDomain);
        }

        // First, set the mirror links at the bottom of the page.
        const loc = "" + window.location;
        let currentDomainToReplace = "localtest.me";
        for (const domain of domainsToReplace) {
          if (loc.includes(domain)) {
            currentDomainToReplace = domain;
            break;
          }
        }

        {% for domain in g.valid_other_domains %}
        {% if ('localhost' not in domain) and 'localtest' not in domain %}
        for (const el of document.querySelectorAll(".js-{{ domain.replace('.', '-') }}")) {
          {% if '-' in domain %}
            {% set domain_parts = domain.split('-') %}
          el.href = loc.replace(currentDomainToReplace, "{{ domain_parts[0] }}-" + "{{ domain_parts[1] }}");
          {% else %}
          el.href = loc.replace(currentDomainToReplace, "{{ domain }}");
          {% endif %}
        }
        {% endif %}
        {% endfor %}


        // Use the new domain in all links and forms.
        let areUsingOtherDomain = false;
        function useOtherDomain(domain) {
          if (areUsingOtherDomain) {
            return;
          }
          areUsingOtherDomain = true;
          if (isInvalidDomain) {
            const newLoc = loc.replace(currentDomainToReplace, domain);
            if (newLoc !== loc) {
              window.location = newLoc;
              return;
            }
          }
          const newOrigin = window.location.origin.replace(currentDomainToReplace, domain);
          for (const el of document.querySelectorAll("a")) {
            el.href = el.href.replace(currentDomainToReplace, domain);
          }
          for (const el of document.querySelectorAll("form")) {
            el.action = el.action.replace(currentDomainToReplace, domain);
          }
        }
        // useOtherDomain('annas-archive.li'); // For testing.

        function getRandomString() {
          return Math.random() + "." + Math.random() + "." + Math.random();
        }

        // Check if there are other domains that are still up. Use the first one that responds.
        let foundOtherDomain = false;
        function checkOtherDomains() {
          console.log('checkOtherDomains');
          if (foundOtherDomain) {
            return;
          }
          const otherFetchOptions = { mode: "cors", method: "GET", credentials: "omit", cache: "no-cache", redirect: "error" };
          for (const domain of domainsToNavigateTo) {
            if (currentDomainToReplace !== domain) {
              fetch('//' + domain + '/dyn/up/?' + getRandomString(), otherFetchOptions).then(function(response) {
                if (foundOtherDomain) {
                  return;
                }
                if (!(response.status >= 500 && response.status <= 599)) {
                  foundOtherDomain = true;
                  useOtherDomain(domain);
                }
              }).catch(function() {
                // Ignore.
              });
            }
          }
        }

        // If we're not on a valid domain, try to go to a valid domain.
        if (isInvalidDomain) {
          checkOtherDomains();
          // Keep checking in case one comes online.
          setInterval(checkOtherDomains, intervalCheckOtherDomains);
        } else {
          // Keep checking the current domain once, to see if it's still up.
          function checkCurrentDomain() {
            const currentFetchOptions = { method: "GET", credentials: "same-origin", cache: "no-cache", redirect: "error" };
            fetch('/dyn/up/?' + getRandomString(), currentFetchOptions).then(function(response) {
              // Only do something in the case of an actual error code from Cloudflare, not if the users network is bad.
              if (response.status >= 500 && response.status <= 599) {
                checkOtherDomains();
                // Keep checking in case one comes online.
                setInterval(checkOtherDomains, intervalCheckOtherDomains);
              }
              if (response.status === 200) {
                return response.json().then(function(jsonResponse) {
                  window.globalUpdateAaLoggedIn(jsonResponse.aa_logged_in);
                });
              }
            }).catch(function() {
              // Ignore; see above.
            });
          }
          setTimeout(checkCurrentDomain, initialCheckMs);
        }
      })();
    </script>
    <!-- Cloudflare Web Analytics --><script defer src='https://static.cloudflareinsights.com/beacon.min.js' data-cf-beacon='{"token": "a60431b24db04cf2beeef0864f1df5e4"}'></script><!-- End Cloudflare Web Analytics -->
  </body>
</html>
