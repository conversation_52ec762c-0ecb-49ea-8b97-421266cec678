<html>
   <head>
     <meta charset="utf-8">
     <title>{% if self.title() %}{% block title %}{% endblock %} - {% endif %}<PERSON>’s Blog</title>
     <style>
       * {
         box-sizing: border-box;
       }
       html, body {
         margin: 0;
         padding: 0;
       }
       body {
         font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
         font-size: 16px;
         line-height: 1.45;
       }
       .main {
         max-width: 700px;
         margin: 0 auto;
         padding: 20px;
       }
       .header {
         background: #fffe92;
       }
       .header-inner {
         max-width: 700px;
         margin: 0 auto;
         padding: 20px;
       }
       .header-inner > a, .header-inner > a:visited {
         font-family: cursive;
         font-size: 4em;
         text-decoration: none;
         color: black;
       }
       .header-inner a:hover, .header-inner a:focus {
         font-weight: bold;
         color: black;
       }
       .header-tagline {
         color: rgba(0,0,0,0.7);
       }
       .tldr {
         background: #f4f4f4;
         padding: 1em;
         margin: 1.5em auto;
         border-radius: 4px;
       }
       a, a:visited {
         color: #333;
       }
       a:hover, a:focus {
         color: #999;
       }
       h2, h3 {
         margin-top: 1em;
       }
       blockquote {
         border-left: 10px solid #999;
         padding-left: 1em;
         margin: 0;
       }
       ul {
         list-style-type: disc;
       }
       sup {
         font-size: 60%;
       }
       figure {
         margin:0;
       }
       figcaption {
         color: #555;
         font-size: 80%;
         margin-top: 8px;
       }
       @keyframes header-ping {
         75%, 100% {
             transform: scale(2);
             opacity: 0;
         }
       }
     </style>
     <meta name="viewport" content="width=device-width, initial-scale=1">
     <link rel="alternate" type="application/rss+xml" href="https://annas-archive.li/blog/rss.xml">
     <link rel="icon" href="data:,">
     {% if self.meta_tags() %}
       {% block meta_tags %}{% endblock %}
     {% endif %}
   </head>
   <body>
     <div class="header">
       <div class="header-inner">
         <a href="/blog">Anna’s Blog</a>
         <div class="header-tagline">{{ gettext('blog.template.subheading', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</div>
       </div>
     </div>
     <div class="main">
       {% block body %}{% endblock %}
     </div>
  </body>
</html>
