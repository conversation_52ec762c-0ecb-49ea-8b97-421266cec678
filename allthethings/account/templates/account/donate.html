{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.donate.title') }}{% endblock %}

{% block body %}
  {% from 'macros/copy_button.html' import copy_button %}

  {% if has_made_donations %}
    <div class="mb-4 p-4 overflow-hidden bg-black/5 break-words rounded">
      {% if existing_unpaid_donation_id %}
        <div class="mb-4">{{ gettext('page.donate.header.existing_unpaid_donation', a_donation=((' href="/account/donations/' + existing_unpaid_donation_id + '"') | safe)) }}</div>
      {% endif %}
      <div>{{ gettext('page.donate.header.existing_unpaid_donation_view_all', a_all_donations=(' href="/account/donations/"' | safe)) }}</div>
    </div>
  {% endif %}

  <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.donate.title') }}</h2>

  <p class="mb-4">
    {{ gettext('page.donate.header.text1') }} {{ gettext('page.donate.header.text2', a_donate=(' href="/donation_faq"' | safe)) }}
  </p>

  <!-- <p class="mb-4">
    {{ gettext('page.donate.refer.text1', a_refer=(' href="/refer" ' | safe)) }}
  </p> -->

  {% if ref_account_dict %}
    <!-- <div class="mb-4 p-4 overflow-hidden bg-yellow-200 break-words rounded">
      {% from 'macros/profile_link.html' import profile_link %}
      🤩
      {{ gettext('page.donate.bonus_downloads.main', percentage=30, profile_link=profile_link(ref_account_dict)) }}
      {{ gettext('page.donate.bonus_downloads.period') }}
    </div> -->
  {% endif %}

  {% macro fast_downloads(downloads, multiplier) %}
    {{ gettext('page.donate.perks.fast_downloads', number=((
      '<span class="line-through mr-1">{}</span><strong class="text-[#ff005b]">{}</strong>'.format(downloads, downloads*2)
      if g.is_membership_double
      else '<strong>{}</strong>'.format(downloads | string)) | safe
    )) }}
    {% if g.is_membership_double %}
      <div class="inline-block text-xs bg-[#ff005b] text-white px-1 rounded">{{ gettext('page.donate.perks.if_you_donate_this_month') }}</div>
    {% endif %}
  {% endmacro %}

  {% set checkmark_icon %}
    <span class="icon-[ion--checkmark-outline] absolute top-1 -left-5"></span>
  {% endset %}

  {% macro membership_tier(level, min, max, size_classes) %}
    <div class="{{ size_classes }} w-[calc(50%-6px)] px-2 py-4 bg-white border border-gray-200 aria-selected:border-[#09008e] rounded-lg shadow mb-3 js-membership-tier js-membership-tier-{{ level }}" aria-selected="false">
      <div class="whitespace-nowrap text-center mb-2">{{ membership_tier_names[level] | replace(' ', '<br>') | safe }}</div>
      <div class="text-center font-bold text-xl mb-2">{{ gettext('page.donate.membership_per_month', cost=((min | string | replace('.00', '')) + "-$" + (max | string | replace('.00', '')))) }}</div>
      <button onclick="window.membershipTierToggle('{{level}}')" class="text-center mb-1 block bg-[#0195ff] hover:bg-blue-600 [[aria-selected=true]_&]:bg-[#09008e] px-2 py-1 rounded-md text-white w-full">
        <span class="[[aria-selected=true]_&]:hidden">{{ gettext('page.donate.buttons.join') }}</span>
        <span class="[[aria-selected=false]_&]:hidden"><span class="icon-[ion--checkmark-circle-sharp] text-lg align-text-bottom"></span> {{ gettext('page.donate.buttons.selected') }}</span>
      </button>
      <!-- <div class="text-xs text-gray-500 text-center">{{ gettext('page.donate.buttons.up_to_discounts', percentage=MEMBERSHIP_DURATION_DISCOUNTS['96']+10) }}</div> -->
      <ul class="mt-4 pl-5">
        {{ caller() }}
      </ul>
    </div>
  {% endmacro %}

  <div class="js-membership-section-tier">
    <div class="flex flex-wrap justify-between md:overflow-hidden">
      {% call membership_tier('2', 2, 6, 'md:min-w-[170px] md:w-[21%]') %}
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🚀 {{ fast_downloads(MEMBERSHIP_DOWNLOADS_PER_DAY['2'], 2.0) | safe }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🧬&nbsp;{{ gettext('page.donate.perks.scidb') }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 👩‍💻 {{ gettext('page.donate.perks.jsonapi', a_api=(a.faqs_api | xmlattr)) }}</li>
        <!-- <li class="relative mb-1">{{ checkmark_icon | safe }} 💁‍♀️ {{ gettext('page.donate.perks.refer', percentage=30, a_refer=(a.refer | xmlattr)) }}</li> -->
        <!-- <li class="relative mb-1">{{ checkmark_icon | safe }} {{ gettext('page.donate.perks.credits') }}</li> -->
      {% endcall %}

      {% call membership_tier('3', 3, 9, 'md:min-w-[180px] md:w-[21%]') %}
        <li class="text-sm relative mb-1">{{ gettext('page.donate.perks.previous_plus') }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🚀 {{ fast_downloads(MEMBERSHIP_DOWNLOADS_PER_DAY['3'], 2.0) | safe }}</li>
        <!-- <li class="relative mb-1">{{ checkmark_icon | safe }} {{ gettext('page.donate.perks.early_access') }}</li> -->
      {% endcall %}

      {% call membership_tier('4', 9, 27, 'md:min-w-[180px] md:w-[23%]') %}
        <li class="text-sm relative mb-1">{{ gettext('page.donate.perks.previous_plus') }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🚀 {{ fast_downloads(MEMBERSHIP_DOWNLOADS_PER_DAY['4'], 2.0) | safe }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 😼 {{ gettext('page.donate.perks.exclusive_telegram') }}</li>
      {% endcall %}

      {% call membership_tier('5', 27, 81, 'md:min-w-[240px] md:w-[29%]') %}
        <li class="text-sm relative mb-1">{{ gettext('page.donate.perks.previous_plus') }}</li>
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🚀 {{ fast_downloads(MEMBERSHIP_DOWNLOADS_PER_DAY['5'], 2.0) | safe }}</li>
        <!-- <li class="relative mb-1">{{ checkmark_icon | safe }} 🤗 {{ gettext('page.donate.perks.adopt', div_months=(' class="text-gray-500 text-sm" ' | safe)) }}</li> -->
        <li class="relative mb-1">{{ checkmark_icon | safe }} 🤯 {{ gettext('page.donate.perks.legendary') }}</li>
      {% endcall %}
    </div>

    <div class="px-2 py-3 bg-white border border-gray-200 aria-selected:border-[#09008e] rounded-lg shadow mb-3 flex items-center justify-between flex-col md:flex-row">
      <div class="px-4 mb-2 md:mb-0">
        <div class="whitespace-nowrap text-center font-bold text-xl">{{ gettext('page.donate.expert.title') }}</div>
        <div class="text-sm text-gray-500 text-center"><a href="/contact">{{ gettext('page.donate.expert.contact_us') }}</a></div>
        <div class="text-xs text-gray-500 text-center max-w-[300px] whitespace-normal">{{ gettext('page.donate.small_team') }}</div>
      </div>
      <ul class="pl-5 md:pl-9 md:pr-3 w-full md:w-auto whitespace-nowrap">
        <li class="relative mb-1"><span class="icon-[ion--checkmark-outline] absolute top-1 -left-5"></span> 🚀 {{ gettext('page.donate.expert.unlimited_access') }}</li>
        <li class="relative max-md:mb-1"><span class="icon-[ion--checkmark-outline] absolute top-1 -left-5"></span> ⚡️ {{ gettext('page.donate.expert.direct_sftp') }}</li>
      </ul>
      <ul class="pl-5 md:pl-9 md:pr-3 w-full md:max-w-[45%]">
        <li class="relative"><span class="icon-[ion--checkmark-outline] absolute top-1 -left-5"></span> {{ gettext('page.donate.expert.enterprise_donation') }}
      </ul>
    </div>

    <div class="mb-4 text-sm text-gray-500 text-center">
      {{ gettext('page.donate.header.large_donations_wealthy') }}
      {{ gettext('page.donate.header.large_donations', email=(a.contact_page_link | safe)) }}
      <div class="mt-1">
        {{ gettext('page.donate.header.recurring', faq=(dict(href="/donation_faq") | xmlattr)) }}
      </div>
      <div class="hidden">{{ gettext('page.donate.without_membership', address=(a.xmr_address | safe)) }}</div>
    </div>
  </div>

  <div class="hidden js-membership-section-method">
    <p class="mt-8 mb-4">{{ gettext('page.donate.payment.select_method') }}</p>

    {% macro donate_button(method, label, discount_percent=0, large=False) %}
      <button class="js-membership-method js-membership-method-{{ method }} {% if not large %}text-xs{% endif %} self-center relative mb-1 bg-gray-500 hover:bg-gray-600 aria-selected:bg-[#09008e] px-2 py-1 rounded-md text-white mr-1" aria-selected="false" onclick="window.membershipMethodToggle('{{ method }}')" data-membership-method="{{ method }}">
        <span class="[[aria-selected=false]_&]:hidden">
          <span class="icon-[ion--checkmark-circle-sharp] {% if large %}text-lg{% else %}text-sm{% endif %} align-text-bottom"></span>
        </span>
        {{ label }}
        {% if discount_percent > 0 %}
          <span class="absolute left-1/2 -top-3.5 -translate-x-1/2 bg-[#0195ff] text-white text-xs font-medium px-1 py-0.5 rounded">{{ gettext('page.donate.discount', percentage=discount_percent) }}</span>
        {% endif %}
      </button>
    {% endmacro %}

    <div class="flex flex-col">
      <div class="flex flex-wrap items-end mt-2">
        <!-- Temporary unavailable text: -->
        <!-- {{ gettext('page.donate.payment.buttons.temporarily_unavailable') }} -->

        {% if g.domain_lang_code in ['de'] %}
          {{ donate_button('amazon_de', gettext('page.donate.payment.buttons.amazon_cc', amazon='Amazon.de'), discount_percent=0, large=True) }}
        {% elif g.domain_lang_code in ['fr'] %}
          {{ donate_button('amazon_fr', gettext('page.donate.payment.buttons.amazon_cc', amazon='Amazon.fr'), discount_percent=0, large=True) }}
        {% elif g.domain_lang_code in ['it'] %}
          {{ donate_button('amazon_it', gettext('page.donate.payment.buttons.amazon_cc', amazon='Amazon.it'), discount_percent=0, large=True) }}
        {% elif g.domain_lang_code in ['ja'] %}
          {{ donate_button('amazon_jp', gettext('page.donate.payment.buttons.amazon_cc', amazon='Amazon.co.jp'), discount_percent=0, large=True) }}
        {% elif g.domain_lang_code %}
          {{ donate_button('amazon', gettext('page.donate.payment.buttons.amazon_cc', amazon='Amazon.com'), discount_percent=0, large=True) }}
        {% endif %}

        <!-- {{ donate_button('payment1b_alipay_cc', gettext('page.donate.payment.buttons.bank_card_app'), discount_percent=0, large=True) }} -->
        <!-- {{ donate_button('payment1c_alipay_cc', gettext('page.donate.payment.buttons.bank_card_app'), discount_percent=0, large=True) }} -->
        {{ donate_button('payment1d_alipay_cc', gettext('page.donate.payment.buttons.bank_card_app'), discount_percent=0, large=True) }}
        <!-- {{ donate_button('payment3a_cc', gettext('page.donate.payment.buttons.bank_card_app'), discount_percent=0, large=True) }} -->

        {{ donate_button('payment2', gettext('page.donate.payment.buttons.crypto', bitcoin_icon=''), discount_percent=30, large=True) }}
        {{ donate_button('payment2cashapp', gettext('page.donate.payment.buttons.cashapp', bitcoin_icon=''), discount_percent=30) }}
        {{ donate_button('payment2revolut', gettext('page.donate.payment.buttons.revolut', bitcoin_icon=''), discount_percent=30) }}

        <!-- {{ donate_button('cc', gettext('page.donate.payment.buttons.credit_debit', bitcoin_icon=''), discount_percent=30) }} -->
        <!-- {{ donate_button('paypal', gettext('page.donate.payment.buttons.paypal', bitcoin_icon=''), discount_percent=30) }} -->
        <!-- {{ donate_button('paypalreg', gettext('page.donate.payment.buttons.paypalreg', bitcoin_icon=''), discount_percent=30) }} -->
        <!-- {{ donate_button('givebutter', gettext('page.donate.payment.buttons.givebutter'), discount_percent=30) }} -->

        <!-- {{ donate_button('bmc', gettext('page.donate.payment.buttons.bmc', bitcoin_icon=''), discount_percent=0) }} -->
        <!-- {{ donate_button('alipay', gettext('page.donate.payment.buttons.alipay', bitcoin_icon=''), discount_percent=0) }} -->
        <!-- {{ donate_button('pix', gettext('page.donate.payment.buttons.pix', bitcoin_icon=''), discount_percent=0) }} -->
        <!-- {{ donate_button('crypto', gettext('page.donate.payment.buttons.crypto', bitcoin_icon=''), discount_percent=30) }} -->
        <!-- {{ donate_button('pix', gettext('page.donate.payment.buttons.crypto', bitcoin_icon=''), discount_percent=30) }} -->

        <!-- {{ donate_button('payment2paypal', gettext('page.donate.payment.buttons.paypal_plain', bitcoin_icon=''), discount_percent=30) }} -->
        {{ donate_button('ccexp', gettext('page.donate.payment.buttons.bank_card'), discount_percent=0) }}
        <!-- {{ donate_button('hoodpay', gettext('page.donate.payment.buttons.credit_debit_backup', bitcoin_icon=''), discount_percent=0) }} -->
        <!-- {{ donate_button('payment2cc', gettext('page.donate.payment.buttons.credit_debit2', bitcoin_icon=''), discount_percent=0) }} -->

        <!-- {{ donate_button('binance', gettext('page.donate.payment.buttons.binance', bitcoin_icon=''), discount_percent=0) }} -->
      </div>

      <div class="flex flex-wrap w-full">  
        {% if g.domain_lang_code in ['de', 'fr', 'it', 'ja'] %}
          {{ donate_button('amazon', 'Amazon.com', discount_percent=0) }}
        {% endif %}
        {% if g.domain_lang_code in ['es'] %}
          {{ donate_button('amazon_es', 'Amazon.es', discount_percent=0) }}
        {% endif %}
        {{ donate_button('amazon_co_uk', 'Amazon.co.uk', discount_percent=0) }}
        {{ donate_button('amazon_ca', 'Amazon.ca', discount_percent=0) }}
        {{ donate_button('amazon_au', 'Amazon.com.au', discount_percent=0) }}
        {% if g.domain_lang_code not in ['de'] %}
          {{ donate_button('amazon_de', 'Amazon.de', discount_percent=0) }}
        {% endif %}
        {% if g.domain_lang_code not in ['es'] %}
          {{ donate_button('amazon_es', 'Amazon.es', discount_percent=0) }}
        {% endif %}
        {% if g.domain_lang_code not in ['fr'] %}
          {{ donate_button('amazon_fr', 'Amazon.fr', discount_percent=0) }}
        {% endif %}
        {% if g.domain_lang_code not in ['it'] %}
          {{ donate_button('amazon_it', 'Amazon.it', discount_percent=0) }}
        {% endif %}
        {% if g.domain_lang_code not in ['ja'] %}
          {{ donate_button('amazon_jp', 'Amazon.co.jp', discount_percent=0) }}
        {% endif %}
      </div>

      <!-- TODO:TRANSLATE "(higher limit)" -->
      <div class="flex flex-wrap w-full {% if g.domain_lang_code in ['zh','tw','ko','ja','th','ms'] %}-order-1{% endif %}">
        {{ donate_button('payment1d_wechat', gettext('page.donate.payment.buttons.wechat') + ' <span class="whitespace-nowrap text-xs">变体K</span>' | safe, discount_percent=0, large=True) }}
        {{ donate_button('payment1d_alipay', gettext('page.donate.payment.buttons.alipay') + ' <span class="whitespace-nowrap text-xs">变体K</span>' | safe, discount_percent=0, large=True) }}
        {{
          shuffle_stable_day([
            donate_button('payment1b_wechat', gettext('page.donate.payment.buttons.wechat') + ' <span class="whitespace-nowrap text-xs">变体R</span>' | safe, discount_percent=0),
            donate_button('payment3b', gettext('page.donate.payment.buttons.wechat') + ' <span class="whitespace-nowrap text-xs">变体T (higher limit)</span>' | safe, discount_percent=0),
          ]) | join('')
        }}
        <!-- {{ donate_button('payment3b', gettext('page.donate.payment.buttons.wechat') + ' <span class="whitespace-nowrap text-xs">变体K</span>' | safe, discount_percent=0) }} -->
        <!-- donate_button('payment1c_wechat', gettext('page.donate.payment.buttons.wechat') + ' <span class="whitespace-nowrap text-xs">变体S</span>' | safe, discount_percent=0), -->
        {{
          shuffle_stable_day([
            donate_button('payment1b_alipay', gettext('page.donate.payment.buttons.alipay') + ' <span class="whitespace-nowrap text-xs">变体R</span>' | safe, discount_percent=0),
            donate_button('payment3a', gettext('page.donate.payment.buttons.alipay') + ' <span class="whitespace-nowrap text-xs">变体T (higher limit)</span>' | safe, discount_percent=0),
          ]) | join('')
        }}
        <!-- {{ donate_button('payment3a', gettext('page.donate.payment.buttons.alipay') + ' <span class="whitespace-nowrap text-xs">变体K</span>' | safe, discount_percent=0) }} -->
        <!-- donate_button('payment1c_alipay', gettext('page.donate.payment.buttons.alipay') + ' <span class="whitespace-nowrap text-xs">变体S</span>' | safe, discount_percent=0), -->

        <!-- {{ donate_button('payment1b', gettext('page.donate.payment.buttons.alipay_wechat') + ' <span class="whitespace-nowrap text-xs">(变体R)</span>' | safe, discount_percent=0) }} -->
        <!-- {{ donate_button('payment1c', gettext('page.donate.payment.buttons.alipay_wechat') + ' <span class="whitespace-nowrap text-xs">(变体S)</span>' | safe, discount_percent=0) }} -->
      </div>
    </div>
  </div>

  {# spacer #}
  <div class="mb-4"></div>

  {% macro definition_item(term) -%}
    <dt class="col-start-1 border-t border-zinc-950/5 pt-3 text-zinc-800 first:border-none sm:border-t sm:border-zinc-950/5 sm:py-3 font-semibold">
      {{ term }}
    </dt>
    <dd class="pb-3 pt-1 text-zinc-950 sm:border-t sm:border-zinc-950/5 sm:py-3 sm:[&:nth-child(2)]:border-none">
      {{ caller() | safe }}
    </dd>
  {%- endmacro %}

  <div class="hidden js-membership-section-duration">
    <div class="js-membership-descr js-membership-descr-crypto">
      <p class="mb-4">{{ gettext('page.donate.payment.desc.crypto') }}</p>
    </div>

    <div class="js-membership-descr js-membership-descr-payment2">
      <p class="mb-4">{{ gettext('page.donate.payment.desc.crypto2') }}</p>

      <p class="mb-4">
        {{ gettext(
          'page.donate.payment.desc.crypto_suggestion_dynamic',
          options=(format_list([
            (a.html_a(gettext('page.donate.payment.processor.binance'), **a.binance) | safe),
            (a.html_a(gettext('page.donate.payment.processor.coinbase'), **a.coinbase) | safe),
            (a.html_a(gettext('page.donate.payment.processor.kraken'), **a.kraken) | safe),
          ], style='or') | safe),
        ) }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-paypal js-membership-descr-payment2paypal">
      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.paypal') }} -->
        {{ gettext('page.donate.payment.desc.paypal_short') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-paypal js-membership-descr-payment2cashapp">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.cashapp') }}
        {{ gettext('page.donate.payment.desc.cashapp_easy') }}
      </p>
      <!-- <p class="mb-4">
        {{ gettext('page.donate.payment.desc.cashapp_fee', amount='$25', fee='$2-4') }}
      </p> -->
    </div>

    <div class="js-membership-descr js-membership-descr-paypal js-membership-descr-payment2revolut">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.revolut') }}
        {{ gettext('page.donate.payment.desc.revolut_easy') }}
      </p>
      <!-- <p class="mb-4">
        {{ gettext('page.donate.payment.desc.cashapp_fee', amount='$25', fee='$2-4') }}
      </p> -->
    </div>

    <div class="js-membership-descr js-membership-descr-payment2cc">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.credit_debit') }}
        {{ gettext('page.donate.payment.desc.google_apple') }}
        <!-- {{ gettext('page.donate.payment.desc.elimate_discount', discount='20') }} -->
        {{ gettext('page.donate.payment.desc.longer_subs') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-binance">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.binance_p1') }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.binance_p2') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-paypalreg">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.paypalreg') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-givebutter">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.givebutter') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='$10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.com') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_co_uk">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='£10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.co.uk') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_fr">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='€10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.fr') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_it">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='€10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.it') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_ca">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='CA$15') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.ca') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_au">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='AUS$15') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.com.au') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_de">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='€10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.de') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-amazon_es">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.amazon') }}
        {{ gettext('page.donate.payment.desc.amazon_round', minimum='€10') }}
      </p>

      <p class="mb-4">
        <!-- {{ gettext('page.donate.payment.desc.amazon_com') }} -->
        {{ gettext('page.donate.payment.desc.amazon_cc', amazon='Amazon.es') }}
      </p>
    </div>


    <div class="js-membership-descr js-membership-descr-hoodpay">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.credit_debit') }}
        {{ gettext('page.donate.payment.desc.credit_debit_backup') }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-payment1b_alipay_cc js-membership-descr-payment1c_alipay_cc js-membership-descr-payment1d_alipay_cc js-membership-descr-payment3a_cc">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.bank_card_app') }}
      </p>

      <p class="mb-4 font-bold">
        {{ gettext('page.donate.payment.desc.bank_card_app.step1.header', style=(dict(class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5") | xmlattr)) }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc1',
            a_app_store=(dict(href="https://apps.apple.com/us/app/alipay-simplify-your-life/id333206289", **a.external_link) | xmlattr),
            a_play_store=(dict(href="https://play.google.com/store/apps/details?id=com.eg.android.AlipayGphone", **a.external_link) | xmlattr),
        ) }}
        {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc2') }}
        {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc3') }}
      </p>

      <p class="mb-4 font-bold">
        {{ gettext('page.donate.payment.desc.bank_card_app.step2.header', style=(dict(class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5") | xmlattr)) }}
      </p>

      <p class="mb-4">
        <img class="w-full max-w-[400px]" src="/images/alipay_cc.png">
      </p>

      <p class="mb-4 text-sm text-gray-500">
        {{ gettext('page.donate.payment.desc.bank_card_app.step2.desc1') }}
        {{ gettext('page.donate.payment.desc.bank_card_app.step2.desc2', a_alipay=(a.alipay_pdf | xmlattr)) }}
      </p>
    </div>

    <div class="js-membership-descr js-membership-descr-ccexp">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.credit_debit_explained') }}
      </p>

      <dl class="grid grid-cols-1 text-base/6 sm:grid-cols-[min(50%,theme(spacing.80))_auto] sm:text-sm/6">
        {% call definition_item(gettext('page.donate.payment.buttons.amazon') + ' ⭐️') %}
          {{ gettext('page.donate.ccexp.amazon_com') }}
        {% endcall %}
        {% call definition_item(gettext('page.donate.payment.buttons.alipay') + ' ⭐️') %}
          {{ gettext('page.donate.ccexp.alipay', a_alipay=(a.alipay_pdf | xmlattr)) }}
        {% endcall %}
        {% call definition_item(gettext('page.donate.payment.buttons.wechat')) %}
          {{ gettext('page.donate.ccexp.wechat') }}
        {% endcall %}
        {% call definition_item(gettext('page.donate.payment.buttons.crypto', bitcoin_icon='')) %}
          {{ gettext('page.donate.ccexp.crypto') }}
          {{ gettext(
            'page.donate.payment.desc.crypto_suggestion_dynamic',
            options=(format_list([
              (a.html_a(gettext('page.donate.payment.processor.binance'), **a.binance) | safe),
              (a.html_a(gettext('page.donate.payment.processor.coinbase'), **a.coinbase) | safe),
              (a.html_a(gettext('page.donate.payment.processor.kraken'), **a.kraken) | safe),
            ], style='or') | safe),
          ) | safe }}
        {% endcall %}
        {% call definition_item(gettext('page.donate.payment.desc.crypto_express_services')) %}
          <p class="mb-2">
            {{ gettext('page.donation.ccexp.crypto_express_services.1') }}
            {{ gettext('page.donation.ccexp.crypto_express_services.2') }}
          </p>
          <p class="mb-2">
            {{ gettext('page.donation.ccexp.crypto_express_services.3') }}
            {{ gettext('page.donation.ccexp.crypto_express_services.4') }}
          </p>
          <ul class="list-inside mb-2 ml-1">
            <li class="list-disc"><a href="https://paybis.com/" rel="noopener noreferrer nofollow" target="_blank">Paybis</a> {{ gettext('page.donation.payment2cc.method.paybis', minimum="$5") }}</li>
            <li class="list-disc"><a href="https://switchere.com/exchange/buy-bitcoin" rel="noopener noreferrer nofollow" target="_blank">Switchere</a> {{ gettext('page.donation.payment2cc.method.switchere', minimum="$10-$20") }}</li>
            <li class="list-disc"><a href="https://munzen.io/buy/bitcoin-btc" rel="noopener noreferrer nofollow" target="_blank">Münzen</a> {{ gettext('page.donation.payment2cc.method.munzen', minimum="$15") }}</li>
            <li class="list-disc"><a href="https://exchange.mercuryo.io/" rel="noopener noreferrer nofollow" target="_blank">Mercuryo.io</a> {{ gettext('page.donation.payment2cc.method.mercuryo', minimum="$30") }}</li>
            <li class="list-disc"><a href="https://www.moonpay.com/buy" rel="noopener noreferrer nofollow" target="_blank">Moonpay</a> {{ gettext('page.donation.payment2cc.method.moonpay', minimum="$35") }}</li>
            <li class="list-disc"><a href="https://buy.coingate.com/" rel="noopener noreferrer nofollow" target="_blank">Coingate</a> {{ gettext('page.donation.payment2cc.method.coingate', minimum="$45") }}</li>
          </ul>
          <p class="text-sm text-gray-500">{{ gettext('page.donation.payment2cc.cc2btc.outdated') }}</p>
        {% endcall %}
      </dl>
    </div>

    <!-- <div class="js-membership-descr js-membership-descr-bmc">
      <p class="mb-4">
        {{ gettext('page.donate.payment.desc.bmc') }}
      </p>
    </div> -->
  </div>

  <div class="hidden js-membership-section-duration-selector">
    <p class="mb-4">
      {{ gettext('page.donate.duration.intro') }}
    </p>

    <div class="flex">
      <div class="flex flex-col whitespace-nowrap">
        {% macro membership_duration_button(duration, label, discounted=False) %}
          <button class="js-membership-duration js-membership-duration-{{ duration }} relative mb-1 bg-gray-500 hover:bg-gray-600 aria-selected:bg-[#09008e] px-2 py-[0.5px] rounded-md text-white pl-3 mr-8" aria-selected="false" onclick="window.membershipDurationToggle('{{ duration }}')">
            <span class="[[aria-selected=false]_&]:invisible"><span class="icon-[ion--checkmark-circle-sharp] text-lg align-text-bottom"></span></span>
            {{ label }}<span class="invisible"><span class="icon-[ion--checkmark-circle-sharp] text-lg align-text-bottom"></span></span>
            {% if discounted %}
              <span class="absolute right-4 top-1/2 translate-x-full -translate-y-1/2 bg-[#0195ff] text-white text-xs font-medium px-1 py-0.5 rounded">
                {{ gettext('page.donate.discount', percentage=MEMBERSHIP_DURATION_DISCOUNTS[duration]) }}
              </span>
            {% endif %}
          </button>
        {% endmacro %}

        {{ membership_duration_button('1', gettext('page.donate.duration.1_mo')) }}
        {{ membership_duration_button('3', gettext('page.donate.duration.3_mo'), discounted=True) }}
        {{ membership_duration_button('6', gettext('page.donate.duration.6_mo'), discounted=True) }}
        {{ membership_duration_button('12', gettext('page.donate.duration.12_mo'), discounted=True) }}
        {{ membership_duration_button('24', gettext('page.donate.duration.24_mo'), discounted=True) }}
        {{ membership_duration_button('48', gettext('page.donate.duration.48_mo'), discounted=True) }}
        {{ membership_duration_button('96', gettext('page.donate.duration.96_mo'), discounted=True) }}
      </div>
      <div class="flex flex-col justify-center w-full max-w-[350px] text-center">
        {{ gettext('page.donate.duration.summary', div_monthly_cost=(' class="text-2xl font-bold js-membership-monthly-cost"' | safe), div_after=(' class="text-sm text-gray-500 font-light mb-4"' | safe), span_discount=(' class="font-extrabold js-membership-discount-percentage"' | safe), div_total=(' class="text-sm  text-gray-500 line-through js-membership-total-cost-no-discounts"></div><div class="text-2xl font-bold js-membership-total-cost" ' | safe), div_duration=(' class="text-sm text-gray-500 font-light js-membership-total-duration"' | safe)) }}
      </div>
    </div>

    <form onsubmit="window.submitForm(event, '/dyn/account/buy_membership/', (data) => { if (data.error) { alert(data.error); location.reload() } else { window.location = data.redirect_url } })" class="js-membership-form mt-4 mb-4">
      <fieldset class="mb-2">
        <div class="js-membership-donate-minimum mb-4 hidden">
          <span class="js-membership-donate-minimum-text">{{ gettext('page.donate.payment.minimum_method', amount=('<span class="js-membership-donate-minimum-amount"></span>' | safe)) }}</span>
          <div class="mt-2"><button class="bg-[#ddd] hover:bg-[#ccc] px-4 py-1 text-sm rounded-md text-white mb-1" onclick="event.preventDefault(); alert(document.querySelector('.js-membership-donate-minimum-text').innerText); return false;">{{ gettext('page.donate.buttons.donate') }}</button></div>
        </div>
        <div class="js-membership-donate-maximum mb-4 hidden">
          <span class="js-membership-donate-maximum-text">{{ gettext('page.donate.payment.maximum_method', amount=('<span class="js-membership-donate-maximum-amount"></span>' | safe)) }}</span>
          <div class="mt-2"><button class="bg-[#ddd] hover:bg-[#ccc] px-4 py-1 text-sm rounded-md text-white mb-1" onclick="event.preventDefault(); alert(document.querySelector('.js-membership-donate-maximum-text').innerText); return false;">{{ gettext('page.donate.buttons.donate') }}</button></div>
        </div>

        <div class="js-membership-donate-submit-confirmation">
          <div class="[html.aa-logged-in_&]:hidden">
            <p class="mb-4">
              {{ gettext('page.donate.login2', a_login=(' href="/login" ' | safe)) }}
            </p>
          </div>

          <div class="[html:not(.aa-logged-in)_&]:hidden">
            <div class="js-membership-descr js-membership-descr-payment2">
              <p class="mb-4">
                {{ gettext('page.donate.payment.crypto_select') }}
              </p>

              <!-- Be sure to update the validation list in `def account_buy_membership`! -->
              <select class="pr-8 mb-4 bg-black/6.7 px-2 py-1 rounded" name="pay_currency">
                <option value="xmr">XMR / Monero {{ gettext('page.donate.currency_lowest_minimum') }}</option>
                <option value="btc">BTC / Bitcoin</option>
                <option value="eth">ETH / Ethereum</option>
                <option value="ethbase">ETH-BASE / Ethereum-Base {{ gettext('page.donate.coinbase_eth') }}</option>
                <option value="bch">BCH / Bitcoin Cash</option>
                <option value="ltc">LTC / Litecoin</option>
                <option value="ada">ADA / Cardano</option>
                <option value="bnbbsc">BNB BSC / Binance Coin</option>
                <option value="busdbsc">BUSD BSC / Binance USD</option>
                <option value="dai">DAI-ERC20 / DAI-Ethereum</option>
                <option value="doge">DOGE / Dogecoin</option>
                <option value="dot">DOT / Polkadot</option>
                <!-- <option value="matic">MATIC / Polygon</option> -->
                <option value="near">NEAR</option>
                <option value="pax">PAX / Paxos</option>
                <option value="pyusd">PYUSD / PayPal USD {{ gettext('page.donate.currency_warning_high_minimum') }}</option>
                <option value="sol">SOL / Solana {{ gettext('page.donate.currency_warning_high_minimum') }}</option>
                <option value="ton">TON / Toncoin</option>
                <option value="trx">TRX / Tron</option>
                <!-- <option value="tusd">TUSD / TrueUSD</option> -->
                <option value="usdc">USDC-ERC20 / USDC-Ethereum {{ gettext('page.donate.currency_warning_high_minimum') }}</option>
                <option value="usdtbsc">USDT-BSC / Tether-Binance</option>
                <option value="usdterc20">USDT-ERC20 / Tether-Ethereum {{ gettext('page.donate.currency_warning_high_minimum') }}</option>
                <option value="usdttrc20">USDT-TRC20 / Tether-Tron</option>
                <option value="usdtsol">USDT-SOL / Tether-Solana</option>
                <!-- No XRP, needs a "tag" -->
                <!-- <option value="xrp">XRP / Ripple</option> -->
              </select>
            </div>

            <p class="mb-4">
              {{ gettext('page.donate.submit.confirm') }}
            </p>

            <input type="hidden" name="tier" value="">
            <input type="hidden" name="method" value="">
            <input type="hidden" name="duration" value="">
            <input type="hidden" name="costCentsUsdVerification" value="">
            <button type="submit" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">
              {{ gettext('page.donate.submit.button', span_cost=(' class="font-bold js-membership-donate-button-cost"' | safe), span_label=(' class="text-xs js-membership-donate-button-label"' | safe)) }}
            </button>
            <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>

            <p class="text-xs text-gray-500">
              {{ gettext('page.donate.submit.cancel_note') }}
            </p>
          </div>
        </fieldset>
        <div class="hidden js-success">{{ gettext('page.donate.submit.success') }}</div>
        <div class="hidden js-failure">{{ gettext('page.donate.submit.failure') }}</div>
      </div>
    </form>
  </div>

  <script>
    (function() {
      const MEMBERSHIP_TIER_COSTS = {{ MEMBERSHIP_TIER_COSTS | tojson }};
      const MEMBERSHIP_METHOD_DISCOUNTS = {{ MEMBERSHIP_METHOD_DISCOUNTS | tojson }};
      const MEMBERSHIP_DURATION_DISCOUNTS = {{ MEMBERSHIP_DURATION_DISCOUNTS | tojson }};
      const MEMBERSHIP_METHOD_MINIMUM_CENTS_USD = {{ MEMBERSHIP_METHOD_MINIMUM_CENTS_USD | tojson }};
      const MEMBERSHIP_METHOD_MAXIMUM_CENTS_NATIVE = {{ MEMBERSHIP_METHOD_MAXIMUM_CENTS_NATIVE | tojson }};
      const membershipCostsData = {{ membership_costs_data | tojson }};

      function getMembershipParams() {
        return Object.fromEntries(new URLSearchParams(location.search));
      }

      function updatePageFromUrl() {
        document.querySelectorAll('.js-membership-tier, .js-membership-method, .js-membership-duration').forEach((el) => el.setAttribute('aria-selected', 'false'));
        document.querySelectorAll('.js-membership-section-method, .js-membership-section-duration, .js-membership-section-duration-selector, .js-membership-descr, .js-membership-section-one-time').forEach((el) => el.classList.add("hidden"));

        const membershipParams = getMembershipParams();
        // console.log("updatePageFromUrl", membershipParams);

        let cost = 0;
        let duration = 1;
        if (Object.keys(MEMBERSHIP_TIER_COSTS).includes(membershipParams.tier)) {
          cost = MEMBERSHIP_TIER_COSTS[membershipParams.tier];
          document.querySelector(`.js-membership-tier-${membershipParams.tier}`).setAttribute('aria-selected', 'true');
          document.querySelector('.js-membership-section-method').classList.remove("hidden");
        }

        if (Object.keys(MEMBERSHIP_METHOD_DISCOUNTS).includes(membershipParams.method)) {
          document.querySelectorAll(`.js-membership-method-${membershipParams.method}`).forEach(el => el.setAttribute('aria-selected', 'true'));
          document.querySelectorAll(`.js-membership-descr-${membershipParams.method}`).forEach(el => el.classList.remove("hidden"));
          if (Object.keys(MEMBERSHIP_TIER_COSTS).includes(membershipParams.tier)) {
            document.querySelector('.js-membership-section-duration').classList.remove("hidden");
            if (membershipParams.method != 'ccexp') {
              document.querySelector('.js-membership-section-duration-selector').classList.remove("hidden");
            }
          }
        }

        if (Object.keys(MEMBERSHIP_DURATION_DISCOUNTS).includes(membershipParams.duration)) {
          duration = parseInt(membershipParams.duration);
          document.querySelector(`.js-membership-duration-${membershipParams.duration}`).setAttribute('aria-selected', 'true');
        } else {
          document.querySelector('.js-membership-duration-1').setAttribute('aria-selected', 'true');
        }

        const membershipParamsStr = [membershipParams.tier, membershipParams.method, membershipParams.duration || "1"].join(',');
        const costsData = membershipCostsData[membershipParamsStr];
        if (costsData) {
          document.querySelector('.js-membership-discount-percentage').innerText = `{{ gettext('page.donate.duration.summary.discount', percentage=('${costsData.discounts}' | safe)) }}`;
          document.querySelector('.js-membership-monthly-cost').innerText = `{{ gettext('page.donate.duration.summary.monthly_cost', monthly_cost=('${costsData.monthly_cents_str}' | safe)) }}`;
          document.querySelector('.js-membership-total-cost-no-discounts').innerText = costsData.cost_cents_native_currency_str_calculator_no_discounts;
          document.querySelector('.js-membership-total-cost').innerText = costsData.cost_cents_native_currency_str_calculator;
          document.querySelector('.js-membership-total-duration').innerText = {
            '1': `{{ gettext('page.donate.duration.summary.duration.1_mo') }}`,
            '3': `{{ gettext('page.donate.duration.summary.duration.3_mo') }}`,
            '6': `{{ gettext('page.donate.duration.summary.duration.6_mo') }}`,
            '12': `{{ gettext('page.donate.duration.summary.duration.12_mo') }}`,
            '24': `{{ gettext('page.donate.duration.summary.duration.24_mo') }}`,
            '48': `{{ gettext('page.donate.duration.summary.duration.48_mo') }}`,
            '96': `{{ gettext('page.donate.duration.summary.duration.96_mo') }}`,
          }[costsData.duration];
          document.querySelector('.js-membership-donate-button-cost').innerText = costsData.cost_cents_native_currency_str_button;
          document.querySelector('.js-membership-donate-button-label').innerText = {
            '1': `{{ gettext('page.donate.submit.button.label.1_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '3': `{{ gettext('page.donate.submit.button.label.3_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '6': `{{ gettext('page.donate.submit.button.label.6_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '12': `{{ gettext('page.donate.submit.button.label.12_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '24': `{{ gettext('page.donate.submit.button.label.24_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '48': `{{ gettext('page.donate.submit.button.label.48_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
            '96': `{{ gettext('page.donate.submit.button.label.96_mo', tier_name=('${costsData.tier_name}' | safe)) }}`,
          }[costsData.duration];
          document.querySelector('.js-membership-form [name=costCentsUsdVerification]').value = costsData.cost_cents_usd;

          // We might override this below.
          document.querySelector(".js-membership-donate-submit-confirmation").classList.remove("hidden");

          const minimumCents = MEMBERSHIP_METHOD_MINIMUM_CENTS_USD[membershipParams.method];
          const minimumEl = document.querySelector('.js-membership-donate-minimum');
          const minimumAmountEl = document.querySelector('.js-membership-donate-minimum-amount');
          if (costsData.cost_cents_usd < minimumCents) {
            minimumEl.classList.remove('hidden')
            minimumAmountEl.innerText = "$" + Math.floor(minimumCents/100) + " USD";
            document.querySelector(".js-membership-donate-submit-confirmation").classList.add("hidden");
          } else {
            minimumEl.classList.add('hidden')
          }

          const maximumCentsNative = MEMBERSHIP_METHOD_MAXIMUM_CENTS_NATIVE[membershipParams.method];
          const maximumEl = document.querySelector('.js-membership-donate-maximum');
          const maximumAmountEl = document.querySelector('.js-membership-donate-maximum-amount');
          if (maximumCentsNative && costsData.cost_cents_native_currency > maximumCentsNative) {
            maximumEl.classList.remove('hidden')
            maximumAmountEl.innerText = Math.floor(maximumCentsNative/100) + " " + costsData.native_currency_code;
            document.querySelector(".js-membership-donate-submit-confirmation").classList.add("hidden");
          } else {
            maximumEl.classList.add('hidden')
          }
        }

        document.querySelector('.js-membership-form [name=tier]').value = membershipParams.tier;
        document.querySelector('.js-membership-form [name=method]').value = membershipParams.method;
        document.querySelector('.js-membership-form [name=duration]').value = membershipParams.duration || 1;
      }

      window.addEventListener("popstate", updatePageFromUrl);
      window.addEventListener("DOMContentLoaded", updatePageFromUrl);
      updatePageFromUrl();

      window.membershipTierToggle = function(tierStr) {
        const membershipParams = getMembershipParams();
        if (membershipParams.tier === tierStr) {
          delete membershipParams.tier;
        } else {
          membershipParams.tier = tierStr;
          setTimeout(() => {
            window.scrollBy({ top: document.querySelector('.js-membership-section-tier').clientHeight, behavior: 'smooth' });
          });
        }
        window.history.replaceState(null, "", "?" + new URLSearchParams(membershipParams).toString());
        updatePageFromUrl();
      }
      window.membershipMethodToggle = function(methodStr) {
        const membershipParams = getMembershipParams();
        if (membershipParams.method === methodStr) {
          delete membershipParams.method;
        } else {
          membershipParams.method = methodStr;
        }
        window.history.replaceState(null, "", "?" + new URLSearchParams(membershipParams).toString());
        updatePageFromUrl();
      }
      window.membershipDurationToggle = function(durationStr) {
        const membershipParams = getMembershipParams();
        if (durationStr === "1") {
          delete membershipParams.duration;
        } else {
          membershipParams.duration = durationStr;
        }
        window.history.replaceState(null, "", "?" + new URLSearchParams(membershipParams).toString());
        updatePageFromUrl();
      }
    })();
  </script>
{% endblock %}
