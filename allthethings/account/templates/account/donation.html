{% extends "layouts/index.html" %}
{% import 'macros/shared_links.j2' as a %}

{% block title %}{{ gettext('page.donation.title') }}{% endblock %}

{% block body %}
  {% from 'macros/copy_button.html' import copy_button %}

  <div class="mb-4 p-6 overflow-hidden bg-black/5 break-words rounded">
    <div class="font-bold">{{ gettext('page.donation.title') }}</div>
    <div>{{ gettext('page.donation.header.date', date=(donation_dict.created | dateformat(format='long'))) }}</div>
    <div>
      {% if donation_dict.json.discounts > 0 %}
        {{ gettext('page.donation.header.total_including_discount', span_details=(' class="text-sm text-gray-500"' | safe),  total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_formal, monthly_amount_usd=donation_dict.monthly_amount_usd, duration=donation_dict.json.duration, discounts=donation_dict.json.discounts) }}
      {% else %}
        {{ gettext('page.donation.header.total_without_discount', span_details=(' class="text-sm text-gray-500"' | safe),  total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_formal, monthly_amount_usd=donation_dict.monthly_amount_usd, duration=donation_dict.json.duration) }}
      {% endif %}
    </div>
    {% if ref_account_dict %}
      <!-- <div class="text-sm">
        {% from 'macros/profile_link.html' import profile_link %}
        🤩
        {{ gettext('page.donate.bonus_downloads.main', percentage=50, profile_link=profile_link(ref_account_dict)) }}
        {{ gettext('page.donate.bonus_downloads.period') }}
      </div> -->
    {% endif %}
    <div>{{ gettext('page.donation.header.status', label=order_processing_status_labels[donation_dict.processing_status], span_label=(' class="italic"' | safe)) }}</div>
    <div class="text-xs text-gray-500">{{ gettext('page.donation.header.id', id=('+' + donation_dict.receipt_id)) }}</div>

    {% if donation_dict.processing_status in [0, 4] %}
      <div class="mt-2">
        <form onsubmit='window.submitForm(event, "/dyn/account/cancel_donation/" + {{ donation_dict.donation_id | tojson }})'>
          <fieldset>
            <button class="button text-sm bg-gray-500 hover:bg-gray-600 aria-selected:bg-[#09008e] px-2 py-1 rounded-md text-white" onclick="document.querySelector('.js-donation-cancel').classList.remove('hidden'); this.classList.add('hidden'); event.preventDefault()">{{ gettext('page.donation.header.cancel.button') }}</button>
            <span class="js-donation-cancel hidden">{{ gettext('page.donation.header.cancel.confirm.msg') }} <button type="submit" class="button text-sm bg-gray-500 hover:bg-gray-600 aria-selected:bg-[#09008e] px-2 py-1 rounded-md text-white">{{ gettext('page.donation.header.cancel.confirm.button') }}</button></span>
          </fieldset>
          <div class="hidden js-success">{{ gettext('page.donation.header.cancel.success') }} <a href="/donate?tier={{ donation_dict.json.tier }}&method={{ donation_dict.json.method }}&duration={{ donation_dict.json.duration }}">{{ gettext('page.donation.header.cancel.new_donation') }}</a></div>
          <div class="hidden js-failure">{{ gettext('page.donation.header.cancel.failure') }}</div>
        </form>
      </div>
    {% else %}
      <div class="mt-2"><a href="/donate?tier={{ donation_dict.json.tier }}&method={{ donation_dict.json.method }}&duration={{ donation_dict.json.duration }}" class="custom-a inline-block bg-gray-500 hover:bg-gray-600 aria-selected:bg-[#09008e] px-2 py-1 rounded-md text-white">{{ gettext('page.donation.header.reorder') }}</a></div>
    {% endif %}
  </div>

  {% if donation_dict.processing_status == 4 %}
    <div class='js-donation-instructions-hidden'>
      <p class="mb-4">
        {{ gettext('page.donation.old_instructions.intro_paid') }}
      </p>

      <a href="#" onclick="document.querySelector('.js-donation-instructions').classList.remove('hidden'); document.querySelector('.js-donation-instructions-hidden').classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a>
    </div>
  {% elif donation_dict.processing_status != 0 %}
    <div class='js-donation-instructions-hidden'>
      <p class="mb-4 font-bold">
        {{ gettext('page.donation.thank_you_donation') }}
      </p>

      <div class="mb-4">
        {{ gettext('page.donation.thank_you.secret_key') }}
        <div><span class="font-bold underline">{{ account_secret_key }}</span> {{ copy_button(account_secret_key) }}</div>
        <div class="">{{ gettext('page.donation.thank_you.locked_out') }}</div>
      </div>

      <p class="mb-4 text-sm text-gray-500">
        {{ gettext('page.donation.old_instructions.intro_outdated') }}
        <a href="#" onclick="document.querySelector('.js-donation-instructions').classList.remove('hidden'); document.querySelector('.js-donation-instructions-hidden').classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a>
      </p>
    </div>
  {% endif %}

  <div class="js-donation-instructions {% if donation_dict.processing_status != 0 %}hidden{% endif %}">
    {% if donation_dict.json.method in ['crypto', 'paypal'] %}
      <p class="mb-4">
        {{ gettext('page.donate.submit.crypto_note') }}
      </p>
    {% endif %}

    {% if donation_dict.json.method == 'crypto' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.crypto.top_header') }}</h2>

      <p class="mb-4 font-bold">{{ gettext('page.donation.payment.crypto.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

      <p class="">
        {{ gettext('page.donation.payment.crypto.text1', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
      </p>
      -->
    {% elif donation_dict.json.method == 'binance' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">1</span>Buy Bitcoin on Binance</p>

      <p class="mb-4">
        Create an account on <a href="https://www.binance.com/en" rel="noopener noreferrer nofollow" target="_blank">Binance</a> and buy Bitcoin (BTC). Use this <a href="https://www.binance.com/en/how-to-buy/bitcoin" rel="noopener noreferrer nofollow" target="_blank">extensive guide</a> if you get stuck.
      </p>

      <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">2</span>Transfer the Bitcoin to our address</p>

      <p class="mb-4">
        Go to Withdraw -> BTC.
      </p>

      <p class="mb-4">
        Enter our Bitcoin (BTC) address as the recipient, and follow the instructions to send your donation:
      </p>
      -->
    {% elif donation_dict.json.method == 'paypal' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <p class="mb-4 mt-6 font-bold">{{ gettext('page.donate.submit.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

      <p class="mb-4">
        {{ gettext('page.donate.one_time_payment.paypal.text2') }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donation.payment.paypal.text3', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
      </p>

      <p class="mb-4 mt-8 font-bold">{{ gettext('page.donate.submit.header2', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

      <p class="mb-4">
        {{ gettext('page.donate.one_time_payment.paypal.text4', transfer_icon=('<span class="icon-[cil--transfer] align-middle"></span>' | safe)) }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donation.payment.paypal.text5', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
      </p>
      -->
    {% elif donation_dict.json.method == 'hoodpay' %}
      <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.credit_debit_card_instructions') }}</h2>

      <!-- <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">1</span>{{ gettext('page.donation.credit_debit_card_our_page') }}</p> -->

      <p class="mb-4">
        {{ gettext('page.donation.donate_on_this_page', amount=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions, a_page=((' href="' + donation_dict.json.hoodpay_request.data.url + '" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank" ') | safe)) }} {{ gettext('page.donation.stepbystep_below') }}
      </p>

      <p class="mb-4">
        <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
        <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
      </p>

      <p class="mb-4">
        {{ gettext('page.donation.reset_timer') }}
      </p>

      <p class="mb-4">
        <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
      </p>

      <p class="mb-4">
        {{ gettext('page.donation.footer.issues_contact', email=(('<a class="break-all" href="mailto:' + donation_email + '">' + donation_email + '</a>') | safe)) }}
      </p>
    {% elif donation_dict.json.method == 'payment2paypal' %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.expired_already_paid') }}</strong> {{ gettext('page.donation.confirmation_can_take_a_while') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step1') }}</span>{{ gettext('page.donation.buy_pyusd') }}</p>

        <p class="mb-4">
          {{ gettext('page.donate.one_time_payment.paypal.text2') }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.pyusd.instructions') }}
          {{ gettext('page.donation.pyusd.more', more='$5', amount=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
        </p>

        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step2') }}</span>Transfer the PYUSD to our address</p>

        <p class="mb-4">
          {{ gettext('page.donation.pyusd.transfer', icon=('<span class="icon-[cil--transfer] align-middle"></span>' | safe)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.transfer_amount_to', amount=((donation_pay_amount + ' ' + (donation_dict.json.payment2_request.pay_currency | upper) + ' ' + copy_button(donation_pay_amount)) | safe), account=(((('<span class="break-all">' + donation_dict.json.payment2_request.pay_address + '</span>') | safe) + ' ' + copy_button(donation_dict.json.payment2_request.pay_address)) | safe)) }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>
      </div>
    {% elif donation_dict.json.method == 'payment2cashapp' %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.expired_already_paid') }}</strong> {{ gettext('page.donation.confirmation_can_take_a_while') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step1') }}</span>{{ gettext('page.donation.cash_app_btc.step1') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.cash_app_btc.step1.text1') }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.cash_app_btc.step1.more', more='$4', amount=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
        </p>

        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step2') }}</span>{{ gettext('page.donation.cash_app_btc.step2') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.cash_app_btc.step2.transfer', icon=('<span class="icon-[cil--transfer] align-middle"></span>' | safe), help_video=(dict(href="https://youtu.be/YldIAkST7fw?t=63") | xmlattr)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.transfer_amount_to', amount=((donation_pay_amount + ' ' + (donation_dict.json.payment2_request.pay_currency | upper) + ' ' + copy_button(donation_pay_amount)) | safe), account=(((('<span class="break-all">' + donation_dict.json.payment2_request.pay_address + '</span>') | safe) + ' ' + copy_button(donation_dict.json.payment2_request.pay_address)) | safe)) }}
        </p>

        <!-- <p class="mb-4">
          {{ gettext('page.donation.cash_app_btc.step2.rush_priority') }}
        </p> -->

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>

        <p class="mb-4">
          <!-- TODO:TRANSLATE -->
          If the website asks any information about the recipient, simply make something up. :)
        </p>
      </div>
    {% elif donation_dict.json.method == 'payment2revolut' %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.expired_already_paid') }}</strong> {{ gettext('page.donation.confirmation_can_take_a_while') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step1') }}</span>{{ gettext('page.donation.revolut.step1') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.revolut.step1.text1') }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.revolut.step1.more', more='$4', amount=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions) }}
        </p>

        <p class="mb-4 mt-6 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">{{ gettext('page.donation.step2') }}</span>{{ gettext('page.donation.revolut.step2') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.revolut.step2.transfer', icon=('<span class="icon-[cil--transfer] align-middle"></span>' | safe), help_video=(dict(href="https://youtu.be/iYMtrm5SViE") | xmlattr)) }}
        </p>

        <p class="mb-4 font-bold">{{ gettext('page.donation.revolut.btc_amount_below') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.transfer_amount_to', amount=((donation_pay_amount + ' ' + (donation_dict.json.payment2_request.pay_currency | upper) + ' ' + copy_button(donation_pay_amount)) | safe), account=(((('<span class="break-all">' + donation_dict.json.payment2_request.pay_address + '</span>') | safe) + ' ' + copy_button(donation_dict.json.payment2_request.pay_address)) | safe)) }}
        </p>

        <!-- <p class="mb-4">
          {{ gettext('page.donation.revolut.step2.rush_priority') }}
        </p> -->

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>

        <p class="mb-4">
          <!-- TODO:TRANSLATE -->
          If the website asks any information about the recipient, simply make something up. :)
        </p>
      </div>
    {% elif donation_dict.json.method == 'payment2cc' %}
      <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.credit_debit_card_instructions') }}</h2>

      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>
        
        <p class="mb-4">
          <strong>{{ gettext('page.donation.expired_already_paid') }}</strong> {{ gettext('page.donation.confirmation_can_take_a_while') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <p>
          {{ gettext('page.donation.payment2cc.cc2btc') }}
        </p>
        <ul class="list-inside mb-2 ml-1">
          <li class="list-disc"><a href="https://paybis.com/" rel="noopener noreferrer nofollow" target="_blank">Paybis</a> {{ gettext('page.donation.payment2cc.method.paybis', minimum="$5") }}</li>
          <li class="list-disc"><a href="https://switchere.com/exchange/buy-bitcoin" rel="noopener noreferrer nofollow" target="_blank">Switchere</a> {{ gettext('page.donation.payment2cc.method.switchere', minimum="$10-$20") }}</li>
          <li class="list-disc"><a href="https://munzen.io/buy/bitcoin-btc" rel="noopener noreferrer nofollow" target="_blank">Münzen</a> {{ gettext('page.donation.payment2cc.method.munzen', minimum="$15") }}</li>
          <li class="list-disc"><a href="https://exchange.mercuryo.io/" rel="noopener noreferrer nofollow" target="_blank">Mercuryo.io</a> {{ gettext('page.donation.payment2cc.method.mercuryo', minimum="$30") }}</li>
          <li class="list-disc"><a href="https://www.moonpay.com/buy" rel="noopener noreferrer nofollow" target="_blank">Moonpay</a> {{ gettext('page.donation.payment2cc.method.moonpay', minimum="$35") }}</li>
          <li class="list-disc"><a href="https://buy.coingate.com/" rel="noopener noreferrer nofollow" target="_blank">Coingate</a> {{ gettext('page.donation.payment2cc.method.coingate', minimum="$45") }}</li>
        </ul>
        <p class="mb-4 text-sm text-gray-500">{{ gettext('page.donation.payment2cc.cc2btc.outdated') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.payment2cc.cc2btc.form') }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.payment2cc.cc2btc.btc_amount') }}</strong> {{ donation_pay_amount }} {{ copy_button(donation_pay_amount) }}<br>{{ gettext('page.donation.payment2cc.exact_amount', underline=(' class="underline"' | safe)) }}<br>
          <strong>{{ gettext('page.donation.payment2cc.cc2btc.btc_address') }}</strong> {{ donation_dict.json.payment2_request.pay_address }} {{ copy_button(donation_dict.json.payment2_request.pay_address) }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>
      </div>
    {% elif donation_dict.json.method == 'payment2' %}
      <div class="mb-6 flex flex-col lg:flex-row items-start gap-6">
        <div class="flex-1">
          <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.crypto_instructions', coin_name=(donation_dict.json.payment2_request.pay_currency | upper)) }}</h2>
      
          {% if donation_time_expired %}
            <p class="mb-4">
              {{ gettext('page.donation.expired') }}
            </p>
      
            <p class="mb-4">
              <strong>{{ gettext('page.donation.expired_already_paid') }}</strong> {{ gettext('page.donation.confirmation_can_take_a_while') }}
            </p>
      
            <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>
      
            <div class="js-donation-instructions-main hidden">
          {% else %}
            <div>
          {% endif %}
            <p class="mb-4">
              {{ gettext('page.donation.transfer_amount_to', amount=((donation_pay_amount + ' ' + (donation_dict.json.payment2_request.pay_currency | upper) + ' ' + copy_button(donation_pay_amount)) | safe), account=(((('<span class="break-all">' + donation_dict.json.payment2_request.pay_address + '</span>') | safe) + ' ' + copy_button(donation_dict.json.payment2_request.pay_address)) | safe)) }}
            </p>
            <p class="mb-4">
              {{ gettext('page.donation.crypto_standard') }}
              {{ gettext('page.donation.confirmation_can_take_a_while') }}
            </p>
            <p class="mb-4">
              <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
              <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
            </p>
      
            <p class="mb-4">
              {{ gettext('page.donation.reset_timer') }}
            </p>
      
            <p class="mb-4">
              <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
            </p>
          </div>
        </div>
        
        <div class="flex-shrink-0 w-full lg:w-auto">
          <div class="text-center">
            <p class="mb-3 font-medium">{{ gettext('page.donation.crypto_qr_code_title') }}</p>
            <div class="inline-block p-4 bg-white border border-gray-300 rounded-lg shadow-sm">
              <div id="qr-code-canvas" class="w-32 h-32 bg-gray-100 flex items-center justify-center">
                <span class="text-gray-500 text-sm">Loading QR code...</span>
              </div>
            </div>
            <p class="mt-2 text-sm text-gray-600" style="max-width: 160px; margin-left: auto; margin-right: auto;">
              {{ gettext('page.donation.crypto_qr_code_instructions') }}
            </p>
            <p id="qr-text-fallback" class="mt-2 text-sm text-gray-600 sr-only"></p>
          </div>
        </div>
      </div>

      <script id="payment-data" type="application/json">
        {{ {
          'address':  donation_dict.json.payment2_request.pay_address | default(''),
          'amount':   donation_pay_amount                               | default(''),
          'currency': donation_dict.json.payment2_request.pay_currency  | default('')
        } | tojson }}
      </script>

      <script src="/qrcode/easy.qrcode.min.js"></script>
      <script>
        function parseUnits(amountStr, decimals) {
          const [whole, frac=''] = String(amountStr).split('.');
          const fracPadded = (frac + '0'.repeat(decimals)).slice(0, decimals);
          return BigInt(whole) * 10n**BigInt(decimals) + BigInt(fracPadded);
        }

        const currencyConfig = {
          xmr:     { type: 'native', scheme: 'monero',      param: 'tx_amount', decimals: null },
          btc:     { type: 'native', scheme: 'bitcoin',     param: 'amount',    decimals: null },
          eth:     { type: 'native', scheme: 'ethereum',    param: 'value',     decimals: 18 },
          ethbase: { type: 'native', scheme: 'ethereum',    param: 'value',     decimals: 18 },
          bch:     { type: 'native', scheme: 'bitcoincash', param: 'amount',    decimals: null },
          ltc:     { type: 'native', scheme: 'litecoin',    param: 'amount',    decimals: null },
          ada:     { type: 'native', scheme: 'cardano',     param: 'amount',    decimals: null },
          bnbbsc:  { type: 'native', scheme: 'bsc',         param: 'value',     decimals: 18 },
          doge:    { type: 'native', scheme: 'dogecoin',    param: 'amount',    decimals: null },
          dot:     { type: 'native', scheme: 'polkadot',    param: 'amount',    decimals: null },
          near:    { type: 'native', scheme: 'near',        param: 'amount',    decimals: null },
          sol:     { type: 'native', scheme: 'solana',      param: 'amount',    decimals: null },
          ton:     { type: 'native', scheme: 'ton',         param: 'amount',    decimals: null },
          trx:     { type: 'native', scheme: 'tron',        param: 'amount',    decimals: null },
          busdbsc:   { type: 'token', chain: 'bsc',      contract: '******************************************', decimals: 18 },
          dai:       { type: 'token', chain: 'ethereum', contract: '******************************************', decimals: 18 },
          pax:       { type: 'token', chain: 'ethereum', contract: '******************************************', decimals: 18 },
          pyusd:     { type: 'token', chain: 'ethereum', contract: '******************************************', decimals: 6 },
          usdc:      { type: 'token', chain: 'ethereum', contract: '******************************************', decimals: 6 },
          usdterc20: { type: 'token', chain: 'ethereum', contract: '******************************************', decimals: 6 },
          usdtbsc:   { type: 'token', chain: 'bsc',      contract: '******************************************', decimals: 18 },
          usdttrc20: { type: 'token', chain: 'tron',     contract: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',           decimals: 6 },
          usdtsol:   { type: 'token', chain: 'solana',   contract: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', decimals: 6 },
        };

        function generatePaymentURI(address, amount, currency) {
          const cfg = currencyConfig[currency.toLowerCase()];
          if (!cfg) return address;

          const value = cfg.decimals ? parseUnits(amount, cfg.decimals).toString() : amount;

          if (cfg.type === 'native') {
            const scheme = cfg.scheme === 'bsc' ? 'ethereum' : cfg.scheme;
            return `${scheme}:${address}?${cfg.param}=${value}`;
          }

          if (cfg.type === 'token') {
            switch (cfg.chain) {
              case 'ethereum':
              case 'bsc':
                return `ethereum:${address}?contractAddress=${cfg.contract}&value=${value}`;
              case 'tron':
                return `tron:${address}?token=${cfg.contract}&amount=${value}`;
              case 'solana':
                return `solana:${address}?amount=${amount}&spl-token=${cfg.contract}`;
              default:
                return address;
            }
          }
          return address;
        }

        function drawQRCode(containerId, text) {
          const container = document.getElementById(containerId);
          if (!container) {
            console.error(`QR Code container #${containerId} not found.`);
            return;
          }
          container.innerHTML = '';

          try {
            const qrCode = new QRCode(container, {
              text: text,
              width: 128,
              height: 128,
              colorDark: "#000000",
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.H,
              dotScale: 1
            });

          } catch (error) {
            console.error('QR Code generation failed:', error);
            const fallback = document.getElementById('qr-text-fallback');
            fallback.textContent = text;
            fallback.classList.remove('sr-only');
          }
        }

        function initQR() {
          const { address, amount, currency } = JSON.parse(
            document.getElementById('payment-data').textContent
          );
          if (!address || isNaN(parseFloat(amount))) return;
          
          const uri = generatePaymentURI(address, amount, currency);
          
          console.log('Generated URI:', uri);
          drawQRCode('qr-code-canvas', uri);
          
          document.getElementById('copy-uri').addEventListener('click', async () => {
            try {
              await navigator.clipboard.writeText(uri);
            } catch (e) {
              console.error('Copy failed', e);
            }
          });
        }

        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initQR);
        } else {
          initQR();
        }
      </script>
    {% elif donation_dict.json.method in ['amazon', 'amazon_co_uk', 'amazon_fr', 'amazon_it', 'amazon_ca', 'amazon_de', 'amazon_es', 'amazon_au', 'amazon_jp'] %}
      <p class="mb-4 font-bold">{{ gettext('page.donation.amazon.header') }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.amazon.form_instructions', a_form=(((' href="' | safe) + (donation_amazon_form | safe) + ('" rel="noopener noreferrer nofollow" target="_blank" ' | safe)) | safe), amount=((('<strong>' | safe) + donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions + ('</strong>' | safe)) | safe)) | replace('.com', donation_amazon_domain_replace) }}
          {{ gettext('page.donation.amazon.only_official') | replace('.com', donation_amazon_domain_replace) }}
        </p>

        <ul class="list-inside mb-4 ml-1">
          <!-- <li class="list-disc">{{ gettext('page.donate.payment.desc.amazon_com') }}</li> -->
          <li class="list-disc">{{ gettext('page.donate.payment.desc.amazon_message_1', amount=(('<strong>' | safe) + donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions + ('</strong>' | safe)) | safe) }}</li>
          <li class="list-disc">{{ gettext('page.donate.payment.desc.amazon_message') }}</li>
        </ul>

        <p class="mb-4">
          {{ gettext('page.donation.amazon.form_to') }} <span class="font-mono font-bold text-sm">giftcards+{{ donation_dict.receipt_id }}@annas-archive.org{{ copy_button('giftcards+' + donation_dict.receipt_id + '@annas-archive.org') }}</span>
          <br>
          <span class="text-sm text-gray-500">
            {{ gettext('page.donation.amazon.unique') }}
            {{ gettext('page.donation.amazon.only_use_once') }}
          </span>
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {{ gettext('page.donation.amazon.waiting_gift_card') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.amazon.confirm_automated', a_instr=(' href="https://www.amazon.com/gp/help/customer/display.html?nodeId=GPC35Y68PEZYG3ED" ' |safe)) }}
          <span class="text-sm text-gray-500">{{ gettext('page.donation.amazon.doesnt_work') }}</span>
        </p>

        <p class="mb-4">{{ gettext('page.donation.amazon.example') }}</p>

        <p class="mb-4 bg-gray-100 rounded">
          <img class="w-full max-w-[700px] p-2" src="/images/annagifts5.png">
        </p>
    {% elif donation_dict.json.method == 'paypalreg' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <h2 class="mt-4 mb-4 text-xl font-bold">PayPal (regular) instructions</h2>

      <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">1</span>Donate on PayPal</p>

      <p class="mb-4">
        Donate {{ donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions }} on PayPal to the following email address:
      </p>

      <p class="mb-4 font-mono font-bold text-sm break-all">
        <EMAIL>{{ copy_button('<EMAIL>') }}
      </p> -->
    {% elif donation_dict.json.method == 'bmc' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!--
        <h2 class="mt-4 mb-4 text-xl font-bold">“Buy Me a Coffee” instructions</h2>

        <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">1</span>Donate through our “Buy Me a Coffee” page</p>

        <p class="mb-4">
          Donate {{ donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions }} on <a href="https://gotopaypay.top/Home/pay?method=paypal" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank">this page</a>.
        </p>

        <p class="mb-4">
          {{ gettext('page.donate.strange_account') }}
        </p>
      -->
    {% elif donation_dict.json.method == 'givebutter' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>
    
      <!-- <h2 class="mt-4 mb-4 text-xl font-bold">“Card / PayPal / Venmo” instructions</h2>

      <p class="mb-4 font-bold"><span class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5">1</span>Donate through our “Card / PayPal / Venmo” page</p>

      <p class="mb-4">
        Donate {{ donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions }} on <a href="https://givebutter.com/donate-annas-archive" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank">this page</a>.
      </p> -->

      <!-- <p class="mb-4">
        {{ gettext('page.donate.strange_account') }}
      </p> -->
    {% elif donation_dict.json.method == 'alipay' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.alipay.top_header') }}</h2>

      <p class="mb-4 font-bold">{{ gettext('page.donation.payment.alipay.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

      <p class="mb-4">
        {{ gettext('page.donation.payment.alipay.text1_new', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions, a_account=(' href="https://gotopaypay.top/Home/pay?method=alipay" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank"' | safe)) }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donate.strange_account') }}
      </p> -->
    {% elif donation_dict.json.method in ['payment1b_alipay', 'payment1c_alipay', 'payment1d_alipay', 'payment3a'] %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.alipay.top_header') }}</h2>

        <p class="mb-4 font-bold">{{ gettext('page.donation.payment.alipay.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.payment.alipay.text1_new', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions, a_account=((' href="' | safe) + ((donation_dict.json.payment1_url or donation_dict.json.payment3_request.data.url or '') | safe) + ('" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank"' | safe) | safe)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.page_blocked') }}
        </p>

        <!-- <p class="mb-4">
          {{ gettext('page.donation.payment.alipay.error') }}
        </p> -->

        <!-- <p class="mb-4">
          {{ gettext('page.donate.strange_account') }}
        </p> -->

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>
      </div>
    {% elif donation_dict.json.method in ['payment1b_alipay_cc', 'payment1c_alipay_cc', 'payment1d_alipay_cc', 'payment3a_cc'] %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.alipay.top_header') }}</h2>

        <p class="mb-4 font-bold">
          {{ gettext('page.donate.payment.desc.bank_card_app.step1.header', style=(dict(class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5") | xmlattr)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc1',
              a_app_store=(dict(href="https://apps.apple.com/us/app/alipay-simplify-your-life/id333206289", **a.external_link) | xmlattr),
              a_play_store=(dict(href="https://play.google.com/store/apps/details?id=com.eg.android.AlipayGphone", **a.external_link) | xmlattr),
          ) }}
          {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc2') }}
          {{ gettext('page.donate.payment.desc.bank_card_app.step1.desc3') }}
        </p>

        <p class="mb-4 font-bold">
          {{ gettext('page.donate.payment.desc.bank_card_app.step2.header', style=(dict(class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5") | xmlattr)) }}
        </p>
  
        <p class="mb-4">
          <img class="w-full max-w-[400px]" src="/images/alipay_cc.png">
        </p>
  
        <p class="mb-4 text-sm text-gray-500">
          {{ gettext('page.donate.payment.desc.bank_card_app.step2.desc1') }}
          {{ gettext('page.donate.payment.desc.bank_card_app.step2.desc2', a_alipay=(a.alipay_pdf | xmlattr)) }}
        </p>
        
        <p class="mb-4 font-bold">
          {{ gettext('page.donation.bank_card_app.step3.header', style=(dict(class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5") | xmlattr)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.bank_card_app.step3.desc.1', a_href=(dict(href=(donation_dict.json.payment1_url or donation_dict.json.payment3_request.data.url or ''), class="font-bold", style="color: #0095ff", rel="noopener noreferrer nofollow", target="_blank") | xmlattr)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.bank_card_app.step3.desc.2') }}
          {{ gettext('page.donation.bank_card_app.step3.desc.3') }}
        </p>

        <p class="mb-4">
          <img class="w-full max-w-[206px]" src="/images/alipay_qr.png">
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.page_blocked') }}
        </p>

        <!-- <p class="mb-4">
          {{ gettext('page.donation.payment.alipay.error') }}
        </p> -->

        <!-- <p class="mb-4">
          {{ gettext('page.donate.strange_account') }}
        </p> -->

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>
      </div>
    {% elif donation_dict.json.method in ['payment1b_wechat', 'payment1c_wechat', 'payment1d_wechat', 'payment3b'] %}
      {% if donation_time_expired %}
        <p class="mb-4">
          {{ gettext('page.donation.expired') }}
        </p>

        <div class="mb-4"><a href="#" onclick="document.querySelector('.js-donation-instructions-main').classList.remove('hidden'); this.classList.add('hidden')">{{ gettext('page.donation.old_instructions.show_button') }}</a></div>

        <div class="js-donation-instructions-main hidden">
      {% else %}
        <div>
      {% endif %}
        <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.wechat.top_header') }}</h2>

        <p class="mb-4 font-bold">{{ gettext('page.donation.payment.wechat.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

        <p class="mb-4">
          {{ gettext('page.donation.payment.wechat.text1', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions, a_account=((' href="' | safe) + ((donation_dict.json.payment1_url or donation_dict.json.payment3_request.data.url or '') | safe) + ('" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank"' | safe) | safe)) }}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.page_blocked') }}
        </p>

        <p class="mb-4">
          <strong>{{ gettext('page.donation.status_header') }}</strong> {% if donation_confirming %}{{ gettext('page.donation.waiting_for_confirmation_refresh') }}{% else %}{{ gettext('page.donation.waiting_for_transfer_refresh') }}{% endif %}<br>
          <strong>{{ gettext('page.donation.time_left_header') }}</strong> {{ (donation_time_left | string).split('.')[0] }} {% if donation_time_left_not_much %}{{ gettext('page.donation.might_want_to_cancel') }}{% endif %}
        </p>

        <p class="mb-4">
          {{ gettext('page.donation.reset_timer') }}
        </p>

        <p class="mb-4">
          <button onclick="window.location.reload()" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.refresh_status') }}</button>
        </p>
      </div>
    {% elif donation_dict.json.method == 'pix' %}
      <p class="mb-4">
        {{ gettext('page.donation.expired') }}
      </p>

      <!-- <h2 class="mt-4 mb-4 text-xl font-bold">{{ gettext('page.donation.payment.pix.top_header') }}</h2>

      <p class="mb-4 font-bold">{{ gettext('page.donation.payment.pix.header1', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe)) }}</p>

      <p class="mb-4">
        {{ gettext('page.donation.payment.pix.text1', total=donation_dict.formatted_native_currency.cost_cents_native_currency_str_donation_page_instructions, a_account=(' href="https://gotopaypay.top/Home/pay?method=pix" class="font-bold" style="color: #0095ff" rel="noopener noreferrer nofollow" target="_blank"' | safe)) }}
      </p>

      <p class="mb-4">
        {{ gettext('page.donate.strange_account') }}
      </p> -->
    {% endif %}

    {% if donation_dict.json.method not in ['payment1b_alipay', 'payment1b_alipay_cc', 'payment1b_wechat', 'payment1c_alipay', 'payment1c_alipay_cc', 'payment1c_wechat', 'payment1d_alipay', 'payment1d_alipay_cc', 'payment1d_wechat', 'payment2', 'payment2paypal', 'payment2cashapp', 'payment2revolut', 'payment2cc', 'amazon', 'amazon_co_uk', 'amazon_fr', 'amazon_it', 'amazon_ca', 'amazon_de', 'amazon_es', 'amazon_au', 'amazon_jp', 'hoodpay', 'payment3a', 'payment3a_cc', 'payment3b'] %}
      <p class="mt-8 mb-4 font-bold">{{ gettext('page.donation.footer.header', span_circle=(' class="inline-block font-light rounded-full text-white bg-[#0195ff] w-[1.5em] h-[1.5em] text-center mr-1.5"' | safe), circle_number=(3 if donation_dict.json.method in ['paypal', 'binance'] else 2)) }}

      <p class="mb-4">
        {% if donation_dict.json.method == 'paypalreg' %}
          {{ gettext('page.donation.footer.verification') }}
        {% else %}
          {{ gettext('page.donation.footer.text1') }}
        {% endif %}
      </p>

      <p class="mb-4 font-mono font-bold text-sm break-all">
        AnnaReceipts+{{ donation_dict.receipt_id }}@proton.me{{ copy_button('AnnaReceipts+' + donation_dict.receipt_id + '@proton.me') }}
      </p>

      {% if donation_dict.json.method in ['crypto', 'paypal'] %}
        <p class="mb-4">
          {{ gettext('page.donation.footer.crypto_note') }}
        </p>
      {% endif %}

      <p class="mb-4">
        {{ gettext('page.donation.footer.text2') }}
      </p>

      <form onsubmit='event.preventDefault(); {% if donation_dict.processing_status == 0 %}window.submitForm(event, "/dyn/account/mark_manual_donation_sent/" + {{ donation_dict.donation_id | tojson }}){% endif %}' class="mb-4">
        <fieldset class="mb-2">

          <input type="hidden" name="tier" value="">
          <input type="hidden" name="method" value="">
          <input type="hidden" name="duration" value="">
          <input type="hidden" name="totalCentsVerification" value="">
          <button type="submit" class="bg-[#0195ff] hover:bg-blue-600 px-4 py-1 rounded-md text-white mb-1">{{ gettext('page.donation.footer.button') }}</button>
          <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
        </fieldset>
        <div class="hidden js-success">{{ gettext('page.donation.footer.success') }}</div>
        <div class="hidden js-failure">{{ gettext('page.donation.footer.failure') }}</div>
      </form>
    {% endif %}

    {% if donation_dict.json.method == 'hoodpay' %}
      <h2 class="mt-8 mb-4 text-xl font-bold">{{ gettext('page.donation.stepbystep') }}</h2>

      <p class="mb-4">{{ gettext('page.donation.crypto_dont_worry') }}</p>

      <p class="mb-4">{{ gettext('page.donation.hoodpay.step1') }}</p>

      <p class="mb-4 bg-gray-100 rounded">
        <img class="w-full max-w-[650px] p-2" src="/images/hoodpay/1.png">
      </p>

      <p class="mb-4">{{ gettext('page.donation.hoodpay.step2') }}</p>

      <p class="mb-4 bg-gray-100 rounded">
        <img class="w-full max-w-[650px] p-2" src="/images/hoodpay/2.png">
      </p>

      <p class="mb-4">{{ gettext('page.donation.hoodpay.step3') }}</p>

      <p class="mb-4 bg-gray-100 rounded">
        <img class="w-full max-w-[650px] p-2" src="/images/hoodpay/3.png">
      </p>

      <p class="mb-4">{{ gettext('page.donation.hoodpay.step4') }}</p>

      <p class="mb-4 bg-gray-100 rounded">
        <img class="w-full max-w-[650px] p-2" src="/images/hoodpay/4.png">
      </p>

      <p class="mb-4">{{ gettext('page.donation.hoodpay.step5') }}</p>

      <p class="mb-4 bg-gray-100 rounded">
        <img class="w-full max-w-[650px] p-2" src="/images/hoodpay/5.png">
      </p>

      <!-- <p class="mb-4">{{ gettext('page.donation.hoodpay.step6') }}</p> -->
    {% endif %}

    <p class="mb-4">
      {{ gettext('page.donation.footer.issues_contact', email=(('<a class="break-all" href="mailto:' + donation_email + '">' + donation_email + '</a>') | safe)) }}
      {{ gettext('page.donate.wait_new', span_hours=(' class="font-bold" ' | safe)) }}
      <span class="text-sm text-gray-500">{{ gettext('page.donate.small_team') }} {{ gettext('page.donate.mistake') }}</span>
    </p>
  </div>
{% endblock %}
