{% extends "layouts/index.html" %}

{% block title %}
  {% if account_dict %}
    {{ gettext('page.account.logged_in.title') }}
  {% else %}
    {{ gettext('page.account.logged_out.title') }}
  {% endif %}
{% endblock %}

{% block body %}
  {% from 'macros/copy_button.html' import copy_button %}

  {% if account_dict %}
    <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.account.logged_in.title') }}</h2>

    <script>window.globalUpdateAaLoggedIn(1);</script>
    
    {% from 'macros/profile_link.html' import profile_link %}
    <div>{{ gettext('page.account.logged_in.account_id', account_id=account_dict.account_id) }}</div>
    <div>{{ gettext('page.account.logged_in.public_profile', profile_link=profile_link(account_dict, account_dict.account_id)) }}</div>
    <div class="mb-4">{{ gettext('page.account.logged_in.secret_key_dont_share', secret_key=((('<a href="/account/secret_key">' | safe) + gettext('page.account.logged_in.secret_key_show') + ('</a>' | safe)))) }}</div>
    
    {% for membership in memberships %}
      <div class="{% if not membership.active %}line-through text-xs{% endif %}">{{ gettext('page.account.logged_in.membership_has_some', a_extend=((' href="/donate?tier=' + membership.membership_tier + '" class="text-sm hidden"') | safe), tier_name=membership.membership_name, until_date=(membership.membership_expiration | dateformat(format='long'))) }}</div>
    {% endfor %}
    {% if not account_fast_download_info %}
      <div class="mb-4">{{ gettext('page.account.logged_in.membership_none', a_become=(' href="/donate"' | safe)) }}</div>
    {% else %}
      <div class="">{{ gettext('page.account.logged_in.membership_fast_downloads_used', used=(account_fast_download_info.downloads_per_day-account_fast_download_info.downloads_left), total=account_fast_download_info.downloads_per_day ) | replace('24', '18') }} <a class="text-sm" href="/account/downloaded">{{ gettext('page.account.logged_in.which_downloads') }}</a></div>
      {% if account_fast_download_info.telegram_url %}
        <div class="my-4">{{ gettext('page.account.logged_in.telegram_group_wrapper', link=(((('<a href="' | safe) + account_fast_download_info.telegram_url + '">' | safe) + gettext('page.account.logged_in.telegram_group_join') + ('</a>' | safe)) | safe)) }}</div>
      {% else %}
        <div class="my-4">{{ gettext('page.account.logged_in.telegram_group_wrapper', link=(('<em>' | safe) + gettext('page.account.logged_in.telegram_group_upgrade', a_tier=('href="/donate"' | safe)) + ('</em>' | safe))) }}
      {% endif %}
      <!-- <div class="mb-4">{{ gettext('page.account.logged_in.membership_upgrade', email=(('<a href="/contact">' | safe + gettext('page.contact.title') + '</a>' | safe) | safe)) }}</div> -->
      <div class="mb-4">{{ gettext('page.account.logged_in.membership_multiple') | replace('24', '18') }}</div>
    {% endif %}

    <ul class="list-inside mb-4 ml-1">
      <li class="list-disc"><a href="/account/profile">{{ gettext('layout.index.header.nav.public_profile') }}</a></li>
      <li class="list-disc"><a href="/account/downloaded">{{ gettext('layout.index.header.nav.downloaded_files') }}</a></li>
      <li class="list-disc"><a href="/account/donations">{{ gettext('layout.index.header.nav.my_donations') }}</a></li>
    </ul>

    <form autocomplete="on" onsubmit="window.submitForm(event, '/dyn/account/logout/', (jsonResponse) => { window.globalUpdateAaLoggedIn(jsonResponse.aa_logged_in); })" class="mb-8">
      <fieldset class="mb-4">
        <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.account.logged_in.logout.button') }}</button>
        <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
      </fieldset>
      <div class="hidden js-success">{{ gettext('page.account.logged_in.logout.success') }}</div>
      <div class="hidden js-failure">{{ gettext('page.account.logged_in.logout.failure') }}</div>
    </form>
  {% else %}
    <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.account.logged_out.title') }}</h2>

    {% if request.args.get('key') %}
      <p class="mb-4">{{ gettext('page.account.logged_out.registered.text1', key=(request.args.get('key') + copy_button(request.args.get('key')) ), span_key=('class="font-bold underline" data-testid="secret-key"' | safe)) }}</p>

      <p class="mb-4">
        {{ gettext('page.account.logged_out.registered.text2') }}
      </p>

      <ul class="list-inside mb-4">
        {{ gettext('page.account.logged_out.registered.text3', li_item=(' class="list-disc"' | safe), a_download=((' href="data:application/octet-stream;charset=utf-8,' + request.args.get('key') + '" download="annas-archive-secret-key.txt"') | safe)) }}
      </ul>
    {% endif %}

    <p class="mb-1">{{ gettext('page.account.logged_out.key_form.text') }}</p>

    <form autocomplete="on" method="post" action="/account/" class="mb-4">
      <input type="password" autofocus autocomplete="current-password" id="key" name="key" required placeholder="{{ gettext('page.account.logged_out.key_form.placeholder') }}" class="w-full max-w-[400px] bg-black/6.7 px-2 py-1 mr-2 rounded mb-1" />
      <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.account.logged_out.key_form.button') }}</button>
      {% if invalid_key %}
        <p class="mb-1 text-red-500">{{ gettext('page.account.logged_out.key_form.invalid_key') }}</p>
      {% else %}
        <p class="mb-1 text-sm text-gray-500">{{ gettext('page.account.logged_out.key_form.dont_lose_key') }}</p>
      {% endif %}
    </form>

    {% if not request.args.get('key') %}
      <p class="mb-1">{{ gettext('page.account.logged_out.register.header') }}</p>

      <form autocomplete="on" method="post" action="/account/register" class="mb-4">
        <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">{{ gettext('page.account.logged_out.register.button') }}</button>
      </form>

      <p class="mb-4">
        {{ gettext('page.login.lost_key', a_contact=(' href="/contact" ' | safe)) }}
        {{ gettext('page.login.lost_key_contact') }}
      </p>

<!--       <p class="mb-1">{{ gettext('page.account.logged_out.old_email.button', a_open=(' href="#" onclick="document.querySelector(\'.js-account-email-form\').classList.remove(\'hidden\'); event.preventDefault(); return false"' | safe)) }}</p>

      <script>
        let accountEmailUsedForError;
        function accountShowError(email, msg) {
          accountEmailUsedForError = email;
          const errorMsgEl = document.querySelector(".js-account-email-validation-error-msg");
          errorMsgEl.innerText = msg + " Submit again to try anyway.";
          errorMsgEl.classList.remove("hidden");
        }

        function accountHideError() {
          accountEmailUsedForError = undefined;
          const errorMsgEl = document.querySelector(".js-account-email-validation-error-msg");
          errorMsgEl.classList.add("hidden");
        }

        function accountValidateEmail(event) {
          event.preventDefault();
          const currentTarget = event.currentTarget;
          const email = new FormData(currentTarget).get('email');

          if (accountEmailUsedForError === email) {
            return true;
          }

          const otherProblematicDomains = ["21cn.com"]
          if (otherProblematicDomains.some((domain) => email.endsWith(domain))) {
            accountShowError(email, "We are currently having issues delivering to this provider. Please use a different email. See below for suggestions.");
            return false;
          }
          if (window.emailMisspelled.microsoft.some((domain) => email.endsWith(domain))) {
            accountShowError(email, "We are currently having issues delivering to Microsoft accounts. Please use a different email. See below for suggestions.");
            return false;
          }
          suggestions = window.emailMisspelled.emailMisspelled({ domains: window.emailMisspelled.all })(email);
          if (suggestions.length > 0) {
            accountShowError(email, "Did you mean “" + suggestions[0].suggest + "”? Please double check!");
            return false;
          }
          if (!/^\S+@\S+\.\S+$/.test(email) || email.endsWith(".con")) {
            accountShowError(email, "It looks like you misspelled your email address. Please double check!");
            return false;
          }

          accountHideError();
          return true;
        }
      </script>

      <form autocomplete="on" onsubmit="if (accountValidateEmail(event)) {window.submitForm(event, '/dyn/account/access/'); document.querySelector('.js-account-sent-email').innerText = document.getElementById('email').value }" class="mb-4 hidden js-account-email-form">
        <fieldset class="mb-4">
          <input type="email" id="email" name="email" required placeholder="<EMAIL>" class="js-account-email w-full max-w-[400px] bg-black/6.7 px-2 py-1 mr-2 rounded mb-1" />
          <div class="js-account-email-validation-error-msg hidden mb-4 text-red-500"></div>
          <div class="mb-1">
            <button type="submit" class="mr-2 bg-[#777] hover:bg-[#999] text-white font-bold py-1 px-3 rounded shadow">Send my secret key to my email</button>
            <span class="js-spinner invisible mb-[-3px] text-xl text-[#555] inline-block icon-[svg-spinners--ring-resize]"></span>
          </div>
          <div class="mb-4">Note that we will discontinue email logins at some point, so make sure to save your secret key.</div>
        </fieldset>
        <div class="hidden js-success">✅ If <strong class="js-account-sent-email"></strong> is a valid account on Anna’s Archive, then we sent you an email. Check your email inbox. If you don’t see anything, wait a minute, and check your spam folder. If that doesn’t work, please register a new account above.</div>
        <div class="hidden js-failure">❌ Something went wrong. Please reload the page and try again.</div>
      </form> -->
    {% endif %}
  {% endif %}
{% endblock %}
