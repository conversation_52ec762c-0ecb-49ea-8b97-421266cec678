{% extends "layouts/index.html" %}

{% block title %}{{ gettext('page.downloaded.title') }}{% endblock %}

{% block body %}
  <h2 class="mt-4 mb-4 text-3xl font-bold">{{ gettext('page.downloaded.title') }}</h2>

  <p class="mb-4 max-w-[700px]">{{ gettext('page.downloaded.fast_partner_star', icon='⭐️') | replace(' ⭐️', '&nbsp⭐️' | safe) }} {{ gettext('page.downloaded.twice') }} {{ gettext('page.downloaded.fast_download_time') | replace('24', '18') }} {{ gettext('page.downloaded.times_utc') }} {{ gettext('page.downloaded.not_public') }}</p>

  {% if (aarecords_downloaded_last_18h+aarecords_downloaded_later) | length == 0 %}
    <p>{{ gettext('page.downloaded.no_files') }}</p>
  {% else %}
    {% from 'macros/aarecord_list.html' import aarecord_list %}

    {% if aarecords_downloaded_last_18h | length > 0 %}
      <h3 class="text-xl font-bold">{{ gettext('page.downloaded.last_18_hours') }}</h3>
      {{ aarecord_list(aarecords_downloaded_last_18h) }}
    {% endif %}
    {% if aarecords_downloaded_later | length > 0 %}
      {% if aarecords_downloaded_last_18h | length > 0 %}
        <h3 class="text-xl font-bold">{{ gettext('page.downloaded.earlier') }}</h3>
      {% endif %}
      {{ aarecord_list(aarecords_downloaded_later) }}
    {% endif %}
  {% endif %}
{% endblock %}
