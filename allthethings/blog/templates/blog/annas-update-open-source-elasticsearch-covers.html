{% extends "layouts/blog.html" %}

{% set title = gettext('blog.annas-update-2022.title') %}
{% set tldr = gettext('blog.annas-update-2022.tldr') %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/annas-update-open-source-elasticsearch-covers.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.annas-update-2022.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2022-12-09
  </p>

  <p class="tldr">{{ gettext('blog.annas-update-2022.tldr') }}</p>

  <p>{{ gettext('blog.annas-update-2022.text1') }}</p>

  <h2>{{ gettext('blog.annas-update-2022.open-source') }}</h2>

  <p>{{ gettext('blog.annas-update-2022.open-source.text1', annas_archive=({"href": "https://software.annas-archive.li/"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.annas-update-2022.open-source.text2') }}</p>

  <pre style="overflow-x: auto;"><code>var lastAnimationFrame = undefined;
var topByElement = {};

function render() {
  window.cancelAnimationFrame(lastAnimationFrame);
  lastAnimationFrame = window.requestAnimationFrame(() =&gt; {
    var bottomEdge = window.scrollY + window.innerHeight * 3; // Load 3 pages worth
    for (element of document.querySelectorAll(".js-scroll-hidden")) {
      if (!topByElement[element.id]) {
        topByElement[element.id] =
          element.getBoundingClientRect().top + window.scrollY;
      }
      if (topByElement[element.id] &lt;= bottomEdge) {
        element.classList.remove("js-scroll-hidden");
        element.innerHTML = element.innerHTML
          .replace("&lt;" + "!--", "")
          .replace("-" + "-&gt;", "");
      }
    }
  });
}

document.addEventListener("DOMContentLoaded", () =&gt; {
  document.addEventListener("scroll", () =&gt; {
    render();
  });
  render();
});</code></pre>

  <p>{{ gettext('blog.annas-update-2022.open-source.text3') }}</p>

  <p>{{ gettext('blog.annas-update-2022.open-source.text4', blog=({"href": "/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.annas-update-2022.open-source.text5') }}</p>

  <h2>{{ gettext('blog.annas-update-2022.es') }}</h2>

  <p>{{ gettext('blog.annas-update-2022.es.text1', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/6"} | xmlattr)) }}</p>

  <ul>
    <li>{{ gettext('blog.annas-update-2022.es.problem1') }}</li>
    <li>{{ gettext('blog.annas-update-2022.es.problem2') }}</li>
    <li>{{ gettext('blog.annas-update-2022.es.problem3') }}</li>
    <li>{{ gettext('blog.annas-update-2022.es.problem4', wikipedia_cjk_characters=({"href": "https://en.wikipedia.org/wiki/CJK_characters", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</li>
  </ul>

  <p>{{ gettext('blog.annas-update-2022.es.text2', youtube=({"href": "https://www.youtube.com/watch?v=QdkS6ZjeR7Q", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), elastic_co=({"href": "https://www.elastic.co/guide/en/elasticsearch/resiliency/current/index.html", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.annas-update-2022.es.text3', annas_archive_l140=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/648b425f91cf49107fc67194ad9e8afe2398243e/allthethings/cli/views.py#L140"} | xmlattr), annas_archive_l1115=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/648b425f91cf49107fc67194ad9e8afe2398243e/allthethings/page/views.py#L1115"} | xmlattr), annas_archive_l1635=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/648b425f91cf49107fc67194ad9e8afe2398243e/allthethings/page/views.py#L1635"} | xmlattr)) }}</p>

  <h2>{{ gettext('blog.annas-update-2022.covers') }}</h2>

  <p>{{ gettext('blog.annas-update-2022.covers.text1') }}</p>

  <p>{{ gettext('blog.annas-update-2022.covers.text2', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.annas-update-2022.covers.text3', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns-winners.signature', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
