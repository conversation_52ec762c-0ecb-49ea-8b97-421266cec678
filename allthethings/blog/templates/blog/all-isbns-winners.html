{% extends "layouts/blog.html" %}

{% set title = gettext("blog.all-isbns-winners.title") %}
{% set tldr = gettext("blog.all-isbns-winners.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:image" content="https://annas-archive.li/blog/isbn_images/all_isbns_smaller.png">
<meta property="og:type" content="article">
<meta property="og:url" content="https://annas-archive.li/blog/all-isbns-winners.html">
<meta property="og:description" content="{{ tldr }}">
<style>
  .main {
    max-width: unset;
  }
  h1, h2, p, ul, blockquote {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  figcaption {
    margin-top: 0;
    font-style: italic;
    text-align: center;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em">{{ gettext('blog.all-isbns-winners.title') }}</h1>
  <p style="font-style: italic; margin-top: 0">
    annas-archive.li/blog, 2024-02-24
  </p>

  <p class="tldr">{{ gettext('blog.all-isbns-winners.tldr') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.text1', all_isbns=({"href": "./all-isbns.html"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns-winners.text2') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.text3') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.text4') }}</p>

  <img src="isbn_images/all_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/md5_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cadal_ssno_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cerlalc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/duxiu_ssid_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/edsebk_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/gbooks_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/goodreads_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ia_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbndb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbngrp_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/libby_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/nexusstc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/oclc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ol_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/rgb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/trantor_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">

  <p>
    <script>window.prevIndex = window.curIndex = 0;</script>
    <select class="js-switcher-select" onchange="document.querySelector('.js-switcher-img').src = document.querySelector('.js-switcher-link').href = 'isbn_images/' + this.value; if (this.selectedIndex !== window.curIndex) { window.prevIndex = window.curIndex; window.curIndex = this.selectedIndex; }">
      <option value="all_isbns_smaller.png" selected>{{ gettext('blog.all-isbns-winners.opt.all') }} [all_isbns]</option>
      <option value="md5_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.md5') }} [md5]</option>
      <option value="cadal_ssno_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.cadal_ssno') }} [cadal_ssno]</option>
      <option value="cerlalc_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.cerlalc') }} [cerlalc]</option>
      <option value="duxiu_ssid_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.duxiu_ssid') }} [duxiu_ssid]</option>
      <option value="edsebk_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.edsebk') }} [edsebk]</option>
      <option value="gbooks_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.gbooks') }} [gbooks]</option>
      <option value="goodreads_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.goodreads') }} [goodreads]</option>
      <option value="ia_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.ia') }} [ia]</option>
      <option value="isbndb_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.isbndb') }} [isbndb]</option>
      <option value="isbngrp_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.isbngrp') }} [isbngrp]</option>
      <option value="libby_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.libby') }} [libby]</option>
      <option value="nexusstc_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.nexusstc') }} [nexusstc]</option>
      <option value="oclc_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.oclc') }} [oclc]</option>
      <option value="ol_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.ol') }} [ol]</option>
      <option value="rgb_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.rgb') }} [rgb]</option>
      <option value="trantor_isbns_smaller.png">{{ gettext('blog.all-isbns-winners.opt.trantor') }} [trantor]</option>
    </select>
      
    <button title="{{ gettext('common.back') }}" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex - 1 + select.options.length) % select.options.length; select.onchange()">⬅️</button>
    <button title="{{ gettext('common.forward') }}" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex + 1) % select.options.length; select.onchange()">➡️</button>
    <button title="{{ gettext('common.last') }}" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = window.prevIndex; select.onchange()">🔄</button>
  </p>

  <div style="margin: 0 -20px">
    <div style="text-align: center; margin: 1em 0">
      <a class="js-switcher-link" target="_blank" href="isbn_images/all_isbns_smaller.png">
        <img class="js-switcher-img" src="isbn_images/all_isbns_smaller.png" style="max-width: 100%; margin: 0 auto">
      </a>
    </div>
  </div>

  <p>{{ gettext('blog.all-isbns-winner.text5', all_isbns=({"href": "./all-isbns.html"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns-winner.text6', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244"} | xmlattr), a_2025_01_isbn_visualization_files=({"href": "/dyn/small_file/torrents/other_aa/aa_misc_data/2025-01-isbn-visualization-files.torrent"} | xmlattr)) }}</p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.first') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.first.text1', phiresky_github=({"href": "https://phiresky.github.io/blog/2025/visualizing-all-books-in-isbn-space/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), annas_archive_note_2951=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2951"} | xmlattr)) }}</p>

  <p>
    <video autoplay loop muted playsinline poster="isbn_winners/phiresky-zoom.png" style="max-width: 100%">
      <source src="isbn_winners/phiresky-zoom.mp4" type="video/mp4">
      <source src="isbn_winners/phiresky-zoom.webm" type="video/webm">
      <source src="isbn_winners/phiresky-zoom.ogv" type="video/ogg">
      <img src="isbn_winners/phiresky-zoom.png" alt="Your browser does not support the video tag.">
    </video>
  </p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.second') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.second.text1', annas_archive_note_2913=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2913"} | xmlattr), annas_archive_note_2971=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2971"} | xmlattr)) }}</p>

  <blockquote>{{ gettext('blog.all-isbns-winners.second.quote') }}</blockquote>

  <p>{{ gettext('blog.all-isbns-winners.second.text2') }}</p>

  <p><img src="isbn_winners/hypha.png" style="max-width: 100%; margin: 0 auto"></p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.third1') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.third1.text1', annas_archive_note_2940=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2940"} | xmlattr)) }}</p>

  <p><img src="isbn_winners/maxlion.png" style="max-width: 100%; margin: 0 auto"></p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.third2') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.third2.text1', annas_archive_note_2917=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2917"} | xmlattr)) }}</p>

  <p><img src="isbn_winners/abetusk.png" style="max-width: 100%; margin: 0 auto"></p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.third3') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.third3.text1', annas_archive_note_2975=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2975"} | xmlattr)) }}</p>

  <p><img src="isbn_winners/conundrumer0.jpeg" style="max-width: 100%; margin: 0 auto"></p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.third4') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.third4.text1', annas_archive_note_2947=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2947"} | xmlattr)) }}</p>

  <p><img src="isbn_winners/charelf.png" style="max-width: 100%; margin: 0 auto"></p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns-winners.notable') }}</h2>

  <p>{{ gettext('blog.all-isbns-winners.notable.text1') }}</p>

  <ul>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2945">BWV_1011:</a> <span>{{ gettext('blog.all-isbns-winners.notable.BWV_1011') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2919">robingchan:</a> <span>{{ gettext('blog.all-isbns-winners.notable.robingchan') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2925">reguster:</a> <span>{{ gettext('blog.all-isbns-winners.notable.reguster') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2944">orangereporter:</a> <span>{{ gettext('blog.all-isbns-winners.notable.orangereporter') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2958">joe.davis:</a> <span>{{ gettext('blog.all-isbns-winners.notable.joe.davis') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2954">timharding:</a> <span>{{ gettext('blog.all-isbns-winners.notable.timharding') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2935">j1618:</a> <span>{{ gettext('blog.all-isbns-winners.notable.j1618') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2792">immartian:</a> <span>{{ gettext('blog.all-isbns-winners.notable.immartian') }}</span></li>
    <li><a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244#note_2928">backrndsource:</a> <span>{{ gettext('blog.all-isbns-winners.notable.backrndsource') }}</span></li>
  </ul>

  <p>{{ gettext('blog.all-isbns-winners.notable.text2', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244"} | xmlattr), a_2025_01_isbn_visualization_files=({"href": "/dyn/small_file/torrents/other_aa/aa_misc_data/2025-01-isbn-visualization-files.torrent"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns-winners.notable.text3') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.notable.text4') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.gratitude') }}</p>

  <p>{{ gettext('blog.all-isbns-winners.footer', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
