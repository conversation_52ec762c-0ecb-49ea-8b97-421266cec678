{% extends "layouts/blog.html" %}

{% set title = gettext('blog.3x-new-books.title') %}
{% set tldr = '' %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/blog-3x-new-books.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.3x-new-books.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2022-09-25
  </p>

  <p>{{ gettext('blog.3x-new-books.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <blockquote>
    <p>{{ gettext('blog.3x-new-books.q1.text1') }}</p>

    <p>{{ gettext('blog.3x-new-books.q1.text2') }}</p>
  </blockquote>

  <p>{{ gettext('blog.3x-new-books.text2') }}</p>

  <p>{{ gettext('blog.3x-new-books.text3') }}</p>

  <p>{{ gettext('blog.3x-new-books.text4', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.3x-new-books.text5') }}</p>

  <p>{{ gettext('blog.3x-new-books.signature', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
