{% extends "layouts/blog.html" %}

{% set title = gettext("blog.ai-copyright.title") %}
{% set tldr = gettext("blog.ai-copyright.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:image" content="">
<meta property="og:type" content="article">
<meta property="og:url" content="https://annas-archive.li/blog/ai-copyright.html">
<meta property="og:description" content="{{ tldr }}">
<style>
  .main {
    max-width: unset;
  }
  h1, h2, p, ul {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  figcaption {
    margin-top: 0;
    font-style: italic;
    text-align: center;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em">{{ gettext('blog.ai-copyright.title') }}</h1>
  <p style="font-style: italic; margin-top: 0">
    annas-archive.li/blog, 2025-01-31 — <span>{{ gettext('blog.ai-copyright.subtitle', torrentfreak=({"href": "https://torrentfreak.com/pirate-libraries-are-forbidden-fruit-for-ai-companies-but-at-what-cost-250131/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), torrentfreak_2=({"href": "https://torrentfreak.com/annas-archive-urges-ai-copyright-overhaul-to-protect-national-security-250201/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</span>
  </p>

  <p class="tldr">{{ gettext('blog.ai-copyright.tldr') }}</p>

  <p>{{ gettext('blog.ai-copyright.text1') }}</p>

  <p>{{ gettext('blog.ai-copyright.text2') }}</p>

  <p>{{ gettext('blog.ai-copyright.text3') }}</p>

  <p>{{ gettext('blog.ai-copyright.text4') }}</p>

  <p>{{ gettext('blog.ai-copyright.text5', arxiv=({"href": "https://arxiv.org/pdf/2403.05525", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.ai-copyright.text6') }}</p>

  <p>{{ gettext('blog.ai-copyright.text7') }}</p>

  <p>{{ gettext('blog.ai-copyright.text8') }}</p>

  <p>{{ gettext('blog.ai-copyright.text9') }}</p>

  <p>{{ gettext('blog.ai-copyright.text10', torrentfreak=({"href": "https://torrentfreak.com/pirate-libraries-are-forbidden-fruit-for-ai-companies-but-at-what-cost-250131/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.ai-copyright.text11') }}</p>

  <p>{{ gettext('blog.ai-copyright.signature', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p style="font-style: italic;">{{ gettext('blog.ai-copyright.postscript', torrentfreak=({"href": "https://torrentfreak.com/pirate-libraries-are-forbidden-fruit-for-ai-companies-but-at-what-cost-250131/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), torrentfreak_2=({"href": "https://torrentfreak.com/annas-archive-urges-ai-copyright-overhaul-to-protect-national-security-250201/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
