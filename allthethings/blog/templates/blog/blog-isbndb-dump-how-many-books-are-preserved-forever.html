{% extends "layouts/blog.html" %}

{% set title = gettext("blog.isbndb-dump.title") %}
{% set tldr = gettext("blog.isbndb-dump.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html">
<meta property="og:image" content="http://annas-archive.li/blog/preservation-slider.png">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.isbndb-dump.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2022-10-31
  </p>

  <p class="tldr">{{ gettext('blog.isbndb-dump.tldr') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <div style="position: relative; height: 16px">
    <div style="position: absolute; left: 0; right: 0; top: 0; bottom: 0; background: hsl(0deg 0% 90%); overflow: hidden; border-radius: 16px; box-shadow: 0px 2px 4px 0px #00000038">
      <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 10%; background: #0095ff"></div>
    </div>
    <div style="position: absolute; left: 10%; top: 50%; width: 16px; height: 16px; transform: translate(-50%, -50%)">
      <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: #0095ff66; border-radius: 100%; animation: ping 1.5s cubic-bezier(0,0,.2,1) infinite"></div>
      <div style="position: absolute; left: 0; top: 0; width: 16px; height: 16px; background: white; border-radius: 100%;"></div>
    </div>
  </div>

  <div style="position: relative; padding-bottom: 5px">
    <div style="width: 14px; height: 14px; border-left: 1px solid gray; border-bottom: 1px solid gray; position: absolute; top: 5px; left: calc(10% - 1px)"></div>
    <div style="position: relative; left: calc(10% + 20px); width: calc(90% - 20px); top: 8px; font-size: 90%; color: #555">{{ gettext('blog.isbndb-dump.10%') }}</div>
  </div>

  <p>{{ gettext('blog.isbndb-dump.text2', booksearch_blogspot=({"href": "http://booksearch.blogspot.com/2010/08/books-of-world-stand-up-and-be-counted.html", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.isbndb-dump.text3', youtube=({"href": "https://www.youtube.com/watch?v=zQuIjwcEPv8", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.isbndb-dump.text4') }}</p>

  <ul>
    <li>{{ gettext('blog.isbndb-dump.maybe.copies') }}</li>
    <li>{{ gettext('blog.isbndb-dump.maybe.works') }}</li>
    <li>{{ gettext('blog.isbndb-dump.maybe.editions') }}</li>
    <li>{{ gettext('blog.isbndb-dump.maybe.files') }}</li>
  </ul>

  <p>{{ gettext('blog.isbndb-dump.text5') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text6') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text7') }}</p>

  <ul>
    <li>{{ gettext('blog.isbndb-dump.effort.google') }}</li>
    <li>{{ gettext('blog.isbndb-dump.effort.openlib', openlibrary=({"href": "https://openlibrary.org/developers/dumps", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</li>
    <li>{{ gettext('blog.isbndb-dump.effort.worldcat') }}</li>
    <li>{{ gettext('blog.isbndb-dump.effort.isbndb') }}</li>
    <li>{{ gettext('blog.isbndb-dump.effort.ils') }}</li>
  </ul>

  <p>{{ gettext('blog.isbndb-dump.text8', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), jsonlines=({"href": "https://jsonlines.org/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.isbndb-dump.text9') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text10') }}</p>

  <!-- TODO:TRANSLATE -->
  <table style="border-collapse: collapse;" cellpadding="8">
    <tbody><tr>
      <th></th>
      <th style="text-align: left;">Editions</th>
      <th style="text-align: left;">ISBNs</th>
    </tr>
    <tr style="background: #daf0ff">
      <th style="text-align: right;">ISBNdb</th>
      <td>-</td>
      <td>30,851,787</td>
    </tr>
    <tr>
      <th style="text-align: right;">Z-Library</th>
      <td>11,783,153</td>
      <td>3,581,309</td>
    </tr>
    <tr style="background: #daf0ff">
      <th style="text-align: right;">Open Library</th>
      <td>36,657,084</td>
      <td>17,371,977</td>
    </tr>
  </tbody></table>

  <p>{{ gettext('blog.isbndb-dump.text11') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text12') }}</p>

  <img src="venn.svg" style="max-height: 400px;">

  <p>{{ gettext('blog.isbndb-dump.text13') }}</p>

  <!-- TODO:TRANSLATE -->
  <table style="border-collapse: collapse;" cellpadding="8">
    <tbody><tr>
      <th style="text-align: right;">ISBNdb ∩ OpenLib</th>
      <td>10,177,281</td>
    </tr>
    <tr style="background: #daf0ff">
      <th style="text-align: right;">ISBNdb ∩ Zlib</th>
      <td>2,308,259</td>
    </tr>
    <tr>
      <th style="text-align: right;">Zlib ∩ OpenLib</th>
      <td>1,837,598</td>
    </tr>
    <tr style="background: #daf0ff">
      <th style="text-align: right;">ISBNdb ∩ Zlib ∩ OpenLib</th>
      <td>1,534,342</td>
    </tr>
  </tbody></table>

  <p>{{ gettext('blog.isbndb-dump.text14') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text15') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text16', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.isbndb-dump.text17') }}</p>

  <p>{{ gettext('blog.isbndb-dump.text18') }}</p>

  <p>{{ gettext('blog.isbndb-dump.signature', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <div style="font-size: 80%; margin-top: 4em">
    <p>{{ gettext('blog.isbndb-dump.fn1') }}</p>
    <p>{{ gettext('blog.isbndb-dump.fn2') }}</p>
    <p>{{ gettext('blog.isbndb-dump.fn3') }}</p>
  </div>
{% endblock %}
