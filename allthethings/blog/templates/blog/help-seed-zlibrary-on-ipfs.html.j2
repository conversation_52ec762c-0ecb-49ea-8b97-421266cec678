{% extends "layouts/blog.html" %}

{% block title %}Help seed Z-Library on IPFS{% endblock %}

{% block meta_tags %}
<meta name="description" content="YOU can help preserve access to this collection." />
<meta name="twitter:card" value="summary" />
<meta property="og:title" content="Help seed Z-Library on IPFS" />
<meta property="og:type" content="article" />
<meta property="og:url" content="http://annas-archive.li/blog/help-seed-zlibrary-on-ipfs.html" />
<meta property="og:description" content="YOU can help preserve access to this collection." />
{% endblock %}

{% block body %}
  <p t-msgid="blog.zlib-on-ipfs.deprecated">
    Warning: this blog post has been deprecated. We’ve decided that IPFS is not yet ready for prime time. We’ll still link to files on IPFS from Anna’s Archive when possible, but we won’t host it ourselves anymore, nor do we recommend others to mirror using IPFS. Please see our Torrents page if you want to help preserve our collection.
  </p>

  <div style="opacity: 30%">
    <h1 t-msgid="blog.zlib-on-ipfs.title">Help seed Z-Library on IPFS</h1>
    <p style="font-style: italic">
      annas-archive.li/blog, 2022-11-22
    </p>

    <p>
      A few days ago we <a href="putting-5,998,794-books-on-ipfs.html">posted</a> about the challenges we faced when hosting 31TB of books from Z-Library on IPFS. We have now figured out some more things, and we can happily report that things seem to be working — the full collection is now available on IPFS through <a href="https://annas-archive.li/">Anna’s Archive</a>. In this post we’ll share some of our latest discoveries, as well as how <em>YOU</em> can help preserve access to this collection.
    </p>

    <h2>Bitswap vs DHT</h2>

    <p>
        <!-- TODO: fix requirement to double-escape entities in translate-html -->
      One source of confusion for us was the difference between <code>ipfs bitswap reprovide</code> and <code>ipfs dht provide -r &lt;root-cid&gt;</code>. The former is much faster, but only seems to contact known peers. The latter is necessary for other peers in the network to discover you in the first place, but does not happen when you initially add the files using <code>ipfs daemon --offline</code> as we were doing. We are still not entirely sure about how all of this works exactly, so we opened a <a href="https://github.com/ipfs/kubo/issues/9429">docs ticket</a> — hopefully we can get this clarified soon!
    </p>

    <p>
      Even though we don’t fully understand what’s going on, we did find a short-term mitigation for "dht provide" taking so long. You can explicitly add public gateways in the peer list, and they will learn about you during the (much faster) "bitswap reprovide" phase. Peering is recommended for heavy-duty nodes anyway. A good list can be found <a href="https://docs.ipfs.tech/how-to/peering-with-content-providers/#content-provider-list">here</a>.
    </p>

    <p>
      We updated our script in <code>container-init.d/</code> to always add this peer list. We also added some logging information for the "bitswap reprovide" that runs every 12 hours:
    </p>

    <code><pre style="overflow-x: auto;">#!/bin/sh
  ipfs config --json Experimental.FilestoreEnabled true
  ipfs config --json Experimental.AcceleratedDHTClient true
  ipfs log level provider.batched debug
  ipfs config --json Peering.Peers '[{"ID": "QmcFf2FH3CEgTNHeMRGhN7HNHU1EXAxoEk6EFuSyXCsvRE", "Addrs": ["/dnsaddr/node-1.ingress.cloudflare-ipfs.com"]}]' # etc</pre></code>

    <h2>Help seed on IPFS</h2>

    <p>
      If you have spare bandwidth and space available, it would be immensely helpful to help seed our collection. These are roughly the steps to take:
    </p>

    <ol>
      <li>Get the data from BitTorrent (we have many more seeders there currently, and it is faster because of fewer individual files than in IPFS). We don’t link to it from here, but just Google for “Pirate Library Mirror” (EDIT: moved to <a href="https://en.wikipedia.org/wiki/Anna%27s_Archive">Anna’s Archive</a>).</li>
      <li>For data in the second release, mount the TAR files using <a href="https://github.com/mxmlnkn/ratarmount">ratarmount</a>, as described in our <a href="putting-5,998,794-books-on-ipfs.html">previous blog post</a>. We have also published the SQLite metadata in a separate torrent, for your convenience. Just put those files next to the TAR files.</li>
      <li>Launch one or multiple IPFS servers (see previous blog post; we currently use 4 servers in Docker). We recommend the configuration from above, but at a minimum make sure to enable <code>Experimental.FilestoreEnabled</code>. Be sure to put it behind a VPN or use a server that cannot be traced to you personally.</li>
      <li>Run something like <code>ipfs add --nocopy --recursive --hash=blake2b-256 --chunker=size-1048576 data-directory/</code>. Be sure to use these exact <code>hash</code> and <code>chunker</code> values, otherwise you will get a different CID! It might be good to do a quick test run and make sure your CIDs match with ours (we also posted a CSV file with our CIDs in one of the torrents). This can take a long time — multiple weeks for everything, if you use a single IPFS instance!</li>
      <li>Alternatively, you can do what we did: add in offline mode first, add the files, then take the node online, peer with public gateways, and then finally run <code>ipfs dht provide -r &lt;root-cid&gt;</code>. This has the advantage that you’ll start seeding files to public gateways sooner, but it is more involved.</li>
    </ol>

    If this is all too involved for you, or you only want to seed a small subset of the data, then it might be easier to pin a few directories:

    <ol>
      <li>Use a VPN.</li>
      <li>Install an <a href="https://docs.ipfs.io/install/">IPFS client</a>.</li>
      <li>Google the “Pirate Library Mirror” (EDIT: moved to <a href="https://en.wikipedia.org/wiki/Anna%27s_Archive">Anna’s Archive</a>), go to “The Z-Library Collection”, and find a list of directory CIDs at the bottom of the page.</li>
      <li>Pin one or more of these CIDs. It will automatically start downloading and seeding. You might need to open a port in your router for optimal performance</li>
      <li>If you have any more questions, be sure to check out the <a href="https://freeread.org/ipfs/">Library Genesis IPFS guide</a>.</li>
    </ol>

    <h2>Other ways to help</h2>

    If you don’t have the space and bandwidth to help seed on BitTorrent or IPFS, here are some other ways you can help, in increasing order of effort:

    <ul>
      <li>Follow us on <a href="https://www.reddit.com/user/AnnaArchivist">Reddit</a>.</li>
      <li>Tell your friends about <a href="https://annas-archive.li/">Anna’s Archive</a>.</li>
      <li>Donate to our “shadow charity” using cryptocurrency (see below for addresses). If you prefer donating by credit card, use one of these merchants with our BTC address as the wallet address: <a href="https://buy.coingate.com/" rel="noopener noreferrer" target="_blank">Coingate</a>, <a href="https://buy.bitcoin.com/" rel="noopener noreferrer" target="_blank">Bitcoin.com</a>, <a href="https://www.sendwyre.com/buy/btc" rel="noopener noreferrer" target="_blank">Sendwyre</a>.</li>
      <li>Help set up an <a href="https://ipfscluster.io/documentation/collaborative/setup/">IPFS Collaborative Cluster</a> for us. This would make it easier for people to participate in seeding our content on IPFS, but it’s a bunch of work that we currently simply don’t have the capacity for.</li>
      <li>Get involved in the development of <a href="https://annas-archive.li/">Anna’s Archive</a>, and/or in preservation of other collections. We’re in the process of setting up a self-hosted Gitlab instance for open source development, and Matrix chat room for coordination. For now, please reach out to us on <a href="https://www.reddit.com/user/AnnaArchivist">Reddit</a>.</li>
    </ul>

    <p>
      We’ve been seeing a lot of interest in our projects lately, so thank you all for your support (moral, financial, time). We really appreciate it, and it really helps us keep going.
    </p>

    <p>
      - Anna and the team (<a href="https://reddit.com/r/Annas_Archive/">Reddit</a>)
    </p>
  </div>
{% endblock %}
