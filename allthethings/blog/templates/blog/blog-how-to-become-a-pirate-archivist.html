{% extends "layouts/blog.html" %}

{% set title = gettext("blog.how-to.title") %}
{% set tldr = gettext("blog.how-to.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/blog-how-to-become-a-pirate-archivist.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.how-to.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2022-10-17 (translations: <a href="https://saveweb.othing.xyz/blog/2022/11/12/%e5%a6%82%e4%bd%95%e6%88%90%e4%b8%ba%e6%b5%b7%e7%9b%97%e6%a1%a3%e6%a1%88%e5%ad%98%e6%a1%a3%e8%80%85/">中文 [zh]</a>)
  </p>

  <p class="tldr">{{ gettext('blog.how-to.tldr') }}</p>

  <p>{{ gettext('blog.how-to.updates', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <ol>
    <li>{{ gettext('blog.how-to.updates.item1') }}</li>
    <li>{{ gettext('blog.how-to.updates.item2') }}</li>
  </ol>

  <p>{{ gettext('blog.how-to.text1') }}</p>

  <p>{{ gettext('blog.how-to.text2') }}</p>

  <p>{{ gettext('blog.how-to.text3') }}</p>

  <img src="party-guy.png" style="width: 100%; max-width: 400px;">

  <h2>{{ gettext('blog.how-to.community') }}</h2>

  <p>{{ gettext('blog.how-to.community.text1') }}</p>

  <p style="background: #ddd; padding: 1em">{{ gettext('blog.how-to.community.text2') }}</p>

  <p>{{ gettext('blog.how-to.community.text3') }}</p>

  <p>{{ gettext('blog.how-to.community.text4') }}</p>

  <p>{{ gettext('blog.how-to.community.text5') }}</p>

  <p>{{ gettext('blog.how-to.community.text6') }}</p>

  <h2>{{ gettext('blog.how-to.projects') }}</h2>

  <p>{{ gettext('blog.how-to.projects.text1') }}</p>

  <ol>
    <li>{{ gettext('blog.how-to.projects.phase1') }}</li>
    <li>{{ gettext('blog.how-to.projects.phase2') }}</li>
    <li>{{ gettext('blog.how-to.projects.phase3') }}</li>
    <li>{{ gettext('blog.how-to.projects.phase4') }}</li>
    <li>{{ gettext('blog.how-to.projects.phase5') }}</li>
    <li>{{ gettext('blog.how-to.projects.phase6') }}</li>
  </ol>

  <p>{{ gettext('blog.how-to.projects.text2') }}</p>

  <h3>{{ gettext('blog.how-to.projects.domain') }}</h3>

  <p>{{ gettext('blog.how-to.projects.domain.text1') }}</p>

  <p>{{ gettext('blog.how-to.projects.domain.text2') }}</p>

  <ul>
    <li>{{ gettext('blog.how-to.projects.domain.why.why') }}</li>
    <li>{{ gettext('blog.how-to.projects.domain.why.skills') }}</li>
    <li>{{ gettext('blog.how-to.projects.domain.why.time') }}</li>
    <li>{{ gettext('blog.how-to.projects.domain.why.target') }}</li>
    <li>{{ gettext('blog.how-to.projects.domain.why.thinking') }}</li>
  </ul>

  <p>{{ gettext('blog.how-to.projects.domain.text3') }}</p>

  <h3>{{ gettext('blog.how-to.projects.target') }}</h3>

  <p>{{ gettext('blog.how-to.projects.target.text1') }}</p>

  <ul>
    <li>{{ gettext('blog.how-to.projects.target.large') }}</li>
    <li>{{ gettext('blog.how-to.projects.target.unique') }}</li>
    <li>{{ gettext('blog.how-to.projects.target.accessible') }}</li>
    <li>{{ gettext('blog.how-to.projects.target.insight') }}</li>
  </ul>

  <p>{{ gettext('blog.how-to.projects.target.text2') }}</p>

  <p>{{ gettext('blog.how-to.projects.target.text3') }}</p>

  <h3>{{ gettext('blog.how-to.projects.metadata') }}</h3>

  <p>{{ gettext('blog.how-to.projects.metadata.text1') }}</p>

  <p>{{ gettext('blog.how-to.projects.metadata.text2') }}</p>

  <p>{{ gettext('blog.how-to.projects.metadata.text3') }}</p>

  <p>{{ gettext('blog.how-to.projects.metadata.text4') }}</p>

  <ul>
    <li>{{ gettext('blog.how-to.projects.metadata.title') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.location') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.id') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.size') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.hash') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.dates') }}</li>
    <li>{{ gettext('blog.how-to.projects.metadata.notes') }}</li>
  </ul>

  <p>{{ gettext('blog.how-to.projects.metadata.text5') }}</p>

  <p>{{ gettext('blog.how-to.projects.metadata.text6') }}</p>

  <h3>{{ gettext('blog.how-to.projects.data') }}</h3>

  <p>{{ gettext('blog.how-to.projects.data.text1') }}</p>

  <p>{{ gettext('blog.how-to.projects.data.text2') }}</p>

  <p>{{ gettext('blog.how-to.projects.data.text3') }}</p>

  <p>{{ gettext('blog.how-to.projects.data.text4') }}</p>

  <h3>{{ gettext('blog.how-to.projects.scraping') }}</h3>

  <p>{{ gettext('blog.how-to.projects.scraping.text1') }}</p>

  <p>{{ gettext('blog.how-to.projects.scraping.text2') }}</p>

  <p>{{ gettext('blog.how-to.projects.scraping.text3') }}</p>

  <p>{{ gettext('blog.how-to.projects.scraping.text4') }}</p>

  <p>{{ gettext('blog.how-to.projects.scraping.text5') }}</p>

  <h3>{{ gettext('blog.how-to.projects.distribution') }}</h3>

  <p>{{ gettext('blog.how-to.projects.distribution.text1') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text2') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text3') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text4') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text5') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text6') }}</p>

  <p>{{ gettext('blog.how-to.projects.distribution.text7') }}</p>

  <h2>{{ gettext('blog.how-to.conclusion') }}</h2>

  <p>{{ gettext('blog.how-to.conclusion.text1') }}</p>

  <p>{{ gettext('blog.how-to.signature', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
