{% extends "layouts/blog.html" %}

{% block meta_tags %}
<style>
table {
    border-collapse: collapse;
}
tr:nth-child(odd) { background: #f2f2f2; }
td {
    padding: 4px;
    white-space: nowrap;
    vertical-align: top;
}
td:first-child {
    margin: 0 8px;
}
</style>
{% endblock %}

{% block body %}
  <p>{{ gettext('blog.index.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
  <p>{{ gettext('blog.index.text2', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
  <p>{{ gettext('blog.index.text3') }}</p>

  <h2>{{ gettext('blog.index.heading') }}</h2>

  <table cellpadding="0" cellspacing="0">
    <tbody><tr>
      <td><a href="all-isbns-winners.html">{{ gettext("blog.all-isbns-winners.title") }}</a></td>
      <td>2025-02-24</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="ai-copyright.html">{{ gettext("blog.ai-copyright.title") }}</a></td>
      <td>2025-01-31</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="all-isbns.html">{{ gettext("blog.all-isbns.title") }}</a></td>
      <td>2024-12-15</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="critical-window.html">{{ gettext("blog.critical-window.title") }}</a></td>
      <td>2024-07-16</td>
      <td><a href="critical-window-chinese.html">中文 [zh]</a></td>
    </tr>
    <tr>
      <td><a href="duxiu-exclusive.html">{{ gettext("blog.duxiu-exclusive.title") }}</a></td>
      <td>2023-11-04</td>
      <td><a href="duxiu-exclusive-chinese.html">中文 [zh]</a></td>
    </tr>
    <tr>
      <td><a href="worldcat-scrape.html">{{ gettext("blog.worldcat-scrape.title") }}</a></td>
      <td>2023-10-03</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="annas-archive-containers.html">{{ gettext("blog.annas-archive-containers.title") }}</a></td>
      <td>2023-08-15</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="backed-up-the-worlds-largest-comics-shadow-lib.html">{{ gettext("blog.backed-up-libgen-li.title") }}</a></td>
      <td>2023-05-13</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="how-to-run-a-shadow-library.html">{{ gettext("blog.how-to-run.title") }}</a></td>
      <td>2023-03-19</td>
      <td><a href="it-how-to-run-a-shadow-library.html">italiano</a></td>
    </tr>
    <tr>
      <td><a href="annas-update-open-source-elasticsearch-covers.html">{{ gettext("blog.annas-update-2022.title") }}</a></td>
      <td>2022-12-09</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="help-seed-zlibrary-on-ipfs.html">{{ gettext("blog.zlib-on-ipfs.title") }}</a></td>
      <td>2022-11-22</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="putting-5,998,794-books-on-ipfs.html">{{ gettext("blog.books-on-ipfs.title") }}</a></td>
      <td>2022-11-19</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="blog-isbndb-dump-how-many-books-are-preserved-forever.html">{{ gettext("blog.isbndb-dump.title") }}</a></td>
      <td>2022-10-31</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="blog-how-to-become-a-pirate-archivist.html">{{ gettext("blog.how-to.title") }}</a></td>
      <td>2022-10-17</td>
      <td><a href="https://saveweb.othing.xyz/blog/2022/11/12/%e5%a6%82%e4%bd%95%e6%88%90%e4%b8%ba%e6%b5%b7%e7%9b%97%e6%a1%a3%e6%a1%88%e5%ad%98%e6%a1%a3%e8%80%85/">中文 [zh]</a></td>
    </tr>
    <tr>
      <td><a href="blog-3x-new-books.html">{{ gettext("blog.3x-new-books.title") }}</a></td>
      <td>2022-09-25</td>
      <td></td>
    </tr>
    <tr>
      <td><a href="blog-introducing.html">{{ gettext("blog.introducing.title") }}</a></td>
      <td>2022-07-01</td>
      <td></td>
    </tr>
  </tbody></table>
  <p>
    📡 <a href="rss.xml">RSS</a>
  </p>
{% endblock %}
