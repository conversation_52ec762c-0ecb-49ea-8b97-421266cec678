{% extends "layouts/blog.html" %}

{% set title = gettext('blog.annas-archive-containers.title') %}
{% set tldr = gettext('blog.annas-archive-containers.tldr') %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:image" content="https://annas-archive.li/blog/aac.png">
<meta property="og:type" content="article">
<meta property="og:url" content="https://annas-archive.li/blog/annas-archive-containers.html">
<meta property="og:description" content="{{ tldr }}">
<style>
  code { word-break: break-all; font-size: 89%; letter-spacing: -0.3px; }
</style>
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.annas-archive-containers.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2023-08-15
  </p>

  <p class="tldr">{{ gettext('blog.annas-archive-containers.tldr') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <table width="100%" cellpadding="0" cellspacing="0">
    <tbody><tr>
      <!-- TODO:TRANSLATE -->
      <th style="padding: 0.5rem; vertical-align: bottom; text-align: left" width="35%">Source</th>
      <th style="padding: 0.5rem; vertical-align: bottom; text-align: left" width="35%">Size</th>
      <th style="padding: 0.5rem; vertical-align: bottom; text-align: left" width="30%">Mirrored by <div class="inline sm:block">Anna’s Archive</div></th>
    </tr>
    <tr style="background: #f2f2f2;">
      <td style="padding: 0.5rem; vertical-align: top;">Sci-Hub</td>
      <td style="padding: 0.5rem; vertical-align: top;">86,614,441 files<br>87.2 TB</td>
      <td style="padding: 0.5rem; vertical-align: top; white-space: nowrap;">99.957%</td>
    </tr>
    <tr>
      <td style="padding: 0.5rem; vertical-align: top;">Library Genesis</td>
      <td style="padding: 0.5rem; vertical-align: top;">16,291,379 files<br>208.1 TB</td>
      <td style="padding: 0.5rem; vertical-align: top; white-space: nowrap;">87%</td>
    </tr>
    <tr style="background: #f2f2f2;">
      <td style="padding: 0.5rem; vertical-align: top;">Z-Library</td>
      <td style="padding: 0.5rem; vertical-align: top;">13,769,031 files<br>97.3 TB</td>
      <td style="padding: 0.5rem; vertical-align: top; white-space: nowrap;">99.91%</td>
    </tr>
    <tr style="font-weight: bold">
      <td style="padding: 0.5rem; vertical-align: top;">Total<div style="font-size: 87.5%; font-weight: normal; color: #6b7280;">Excluding duplicates</div></td>
      <td style="padding: 0.5rem; vertical-align: top;">111,081,811 files<br>419.5 TB</td>
      <td style="padding: 0.5rem; vertical-align: top; white-space: nowrap;">97.998%</td>
    </tr>
  </tbody></table>

  <p>{{ gettext('blog.annas-archive-containers.text2') }}</p>
  <ol>
    <li>{{ gettext('blog.annas-archive-containers.text2.li1') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.text2.li2') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.text2.li3') }}</li>
  </ol>

  <p>{{ gettext('blog.annas-archive-containers.text3') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.text4') }}</p>

  <h2>{{ gettext('blog.annas-archive-containers.goals.heading') }}</h2>

  <p>{{ gettext('blog.annas-archive-containers.goals.text1') }}</p>

  <ul>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal1') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal2') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal3') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal4') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal5') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal6') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal7') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal8') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal9') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.goal10') }}</li>
  </ul>

  <p>{{ gettext('blog.annas-archive-containers.goals.text2') }}</p>

  <ul>
    <li>{{ gettext('blog.annas-archive-containers.goals.nongoal1') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.nongoal2') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.goals.nongoal3') }}</li>
  </ul>

  <p>{{ gettext('blog.annas-archive-containers.goals.text3') }}</p>

  <h2>{{ gettext('blog.annas-archive-containers.standard.heading') }}</h2>

  <p>{{ gettext('blog.annas-archive-containers.standard.text1') }}</p>

  <ul>
    <li>{{ gettext('blog.annas-archive-containers.standard.aac') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.standard.collection') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.standard.records-files') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.standard.aacid') }}<ul>
        <li>{{ gettext('blog.annas-archive-containers.standard.aacid.collection') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.aacid.iso8601') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.aacid.collection-id') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.aacid.shortuuid', github_skorokithakis_shortuuid=({"href": "https://github.com/skorokithakis/shortuuid/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</li>
      </ul></li>
    <li>{{ gettext('blog.annas-archive-containers.standard.aacid-range') }}</li>
    <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file') }}<ul>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.filename') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.jsonlines', jsonlines=({"href": "https://jsonlines.org/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), zstd=({"href": "http://www.zstd.net/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.fields') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.metadata') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.data_folder') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.metadata-file.prefix') }}</li>
      </ul></li>
    <li>{{ gettext('blog.annas-archive-containers.standard.binary') }}<ul>
        <li>{{ gettext('blog.annas-archive-containers.standard.binary.name') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.binary.contents') }}</li>
        <li>{{ gettext('blog.annas-archive-containers.standard.binary.tip') }}</li>
      </ul></li>
    <li>{{ gettext('blog.annas-archive-containers.standard.torrents') }}</li>
  </ul>

  <h2>{{ gettext('blog.annas-archive-containers.example.heading') }}</h2>

  <p>{{ gettext('blog.annas-archive-containers.example.text1') }}</p>

  <ul>
    <li><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z</span>.jsonl.zst.torrent</code></li>
    <li><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z</span>.jsonl.zst.torrent</code></li>
  </ul>

  <p>{{ gettext('blog.annas-archive-containers.example.text2') }}</p>

  <ul>
    <li><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T055130Z--20230808T055131Z</span>.torrent</code></li>
    <li><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T120246Z--20230808T120247Z</span>.torrent</code></li>
    <li>…</li>
    <li><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230809T204340Z--20230809T204341Z</span>.torrent</code></li>
  </ul>

  <p>{{ gettext('blog.annas-archive-containers.example.text3') }}</p>

  <code style="font-size: 70%">
    {"aacid":"<span style="color: #0093ff">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z__22430000__hnyiZz2K44Ur5SBAuAgpg8</span>","metadata":{"zlibrary_id":22430000,"date_added":"2022-08-24","date_modified":"2023-04-05","extension":"epub","filesize_reported":483359,"md5_reported":"21f19f95c4b969d06fe5860a98e29f0d","title":"Els nens de la senyora Zlatin","author":"Maria Lluïsa Amorós","publisher":"ePubLibre","language":"catalan","series":"","volume":"","edition":"","year":"2021","pages":"","description":"França, 1943. Un grup de nens jueus, procedents de diversos països europeus, arriben a França per escapar de la tragèdia que devasta Europa durant la Segona Guerra Mundial. Amb l’ocupació de França per part dels alemanys, les seves vides corren perill. La Sabine Zlatin, infermera de la Creu Roja, tindrà cura d’ells i els buscarà un indret on puguin refugiar-se fins a l’acabament de la guerra. El 18 de maig del 1943, amb el temor que algú els aturi, arriben a Villa Anne-Marie, un casalici blanc on els nens compartiran pors i l’enyorança dels pares, que van deixar enrere, però també gaudiran de la pau del lloc, dels jocs vora la gran font i dels contes que en Léon, un educador, els relata perquè la son els venci. I, sobretot, retrobaran el valor de l’amistat, del primer amor i de tenir cura els uns dels altres.Paral·lelament, l’Octavi Verdier, un jove periodista, escriu una novel·la sobre la presència nazi a la Barcelona dels anys quaranta, que contrasta amb la Barcelona sotmesa pel franquisme. Durant aquest procés de creació que l’obliga a investigar, descobrirà què s’amaga darrere la porta del despatx d’en Gustau Verdier, el seu avi, que el 1944 va venir de França i va comprar una fàbrica tèxtil a Terrassa. En la recerca anirà a parar a Villa Anne-Marie, a Izieu.","cover_path":"/covers/books/21/f1/9f/21f19f95c4b969d06fe5860a98e29f0d.jpg","isbns":[],"category_id":""}}
  </code>

  <p>{{ gettext('blog.annas-archive-containers.example.text4') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.example.text5') }}</p>

  <code style="font-size: 70%">
    {"aacid":"<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span>","data_folder":"<span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>","metadata":{"zlibrary_id":"22433983","md5":"63332c8d6514aa6081d088de96ed1d4f"}}
  </code>

  <p>{{ gettext('blog.annas-archive-containers.example.text6') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.example.text7') }}</p>

  <h2>{{ gettext('blog.annas-archive-containers.conclusion.heading') }}</h2>

  <p>{{ gettext('blog.annas-archive-containers.conclusion.text1') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.conclusion.text2') }}</p>

  <p>{{ gettext('blog.annas-archive-containers.conclusion', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
