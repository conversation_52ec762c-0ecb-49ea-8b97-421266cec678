{% extends "layouts/blog.html" %}

{% set title = gettext("blog.critical-window.title") %}
{% set tldr = gettext("blog.critical-window.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:image" content="https://annas-archive.li/blog/growth.png">
<meta property="og:url" content="http://annas-archive.li/blog/critical-window.html">
<meta property="og:description" content="{{ tldr }}">
<style>
  figcaption {
    margin-top: 0;
    font-style: italic;
    text-align: center;
  }
  h1 {
    font-size: 26px;
    margin-bottom: 0.25em;
  }
  h2 {
    margin-top: 1.5em;
  }
  h3 {
    font-size: 16px;
  }
  blockquote {
    background: rgb(254 249 195);
    border-radius: .25rem;
    padding: 16px;
  }
</style>
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.critical-window.title') }}</h1>
  <p style="font-style: italic; margin-top: 0">
    annas-archive.li/blog, 2024-07-16, <span>{{ gettext('blog.critical-window.links', critical_window_chinese=({"href": "critical-window-chinese.html"} | xmlattr), reddit=({"href": "https://www.reddit.com/r/Annas_Archive/comments/1e4zfl0/new_blog_post_the_critical_window_of_shadow/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), news_ycombinator=({"href": "https://news.ycombinator.com/item?id=40980202", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</span>
  </p>

  <p class="tldr">{{ gettext('blog.critical-window.tldr') }}</p>

  <p>{{ gettext('blog.critical-window.text1') }}</p>

  <figure>
    <a href="https://annas-archive.li/torrents#stats"><img src="growth.png" style="max-width: 100%; margin-top: 0.5em; margin-bottom: 0.25em"></a>
    <figcaption>{{ gettext('blog.critical-window.fig1', annas_archive_stats=({"href": "https://annas-archive.li/torrents#stats"} | xmlattr)) }}</figcaption>
  </figure>

  <h2>{{ gettext('blog.critical-window.priorities') }}</h2>

  <p>{{ gettext('blog.critical-window.priorities.text1') }}</p>

  <p>{{ gettext('blog.critical-window.priorities.text2') }}</p>

  <ul>
    <li>{{ gettext('blog.critical-window.priorities.order.papers') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.organic') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.nonfiction-books') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.code') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.measurements') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.science-websites') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.nonfiction-other') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.nonfiction-transcripts') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.leaks') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.metadata') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.geographic') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.transcripts') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.order.fiction') }}</li>
  </ul>

  <p>{{ gettext('blog.critical-window.priorities.text3') }}</p>

  <p>{{ gettext('blog.critical-window.priorities.text4') }}</p>

  <p>{{ gettext('blog.critical-window.priorities.text5') }}</p>

  <ul>
    <li>{{ gettext('blog.critical-window.priorities.rarity.rare') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.rarity.underfocused') }}</li>
    <li>{{ gettext('blog.critical-window.priorities.rarity.at-risk') }}</li>
  </ul>

  <p>{{ gettext('blog.critical-window.priorities.text6') }}</p>

  <h2>{{ gettext('blog.critical-window.shadowlib') }}</h2>

  <p>{{ gettext('blog.critical-window.shadowlib.text1') }}</p>

  <p>{{ gettext('blog.critical-window.shadowlib.text2') }}</p>

  <p>{{ gettext('blog.critical-window.shadowlib.text3') }}</p>

  <p>{{ gettext('blog.critical-window.shadowlib.text4') }}</p>

  <blockquote>
    <p>{{ gettext('blog.critical-window.quote.the-lost') }}</p>
  </blockquote>

  <p>{{ gettext('blog.critical-window.shadowlib.text5') }}</p>

  <ul>
    <li>{{ gettext('blog.critical-window.shadowlib.example.metadata', worldcat_scrape=({"href": "worldcat-scrape.html"} | xmlattr)) }}</li>
    <li>{{ gettext('blog.critical-window.shadowlib.example.github') }}</li>
    <li>{{ gettext('blog.critical-window.shadowlib.example.reddit') }}</li>
  </ul>

  <h2>{{ gettext('blog.critical-window.copies') }}</h2>

  <p>{{ gettext('blog.critical-window.copies.text1', torrents_stats=({"href": "/torrents#stats"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.critical-window.copies.text2') }}</p>

  <h3>{{ gettext('blog.critical-window.low-hanging-fruit') }}</h3>

  <p>{{ gettext('blog.critical-window.low-hanging-fruit.text1') }}</p>

  <p>{{ gettext('blog.critical-window.low-hanging-fruit.text2') }}</p>

  <h3>{{ gettext('blog.critical-window.storage') }}</h3>

  <p>{{ gettext('blog.critical-window.storage.text1', diskprices=({"href": "https://diskprices.com/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.critical-window.storage.text2') }}</p>

  <p>{{ gettext('blog.critical-window.storage.text3') }}</p>

  <p>{{ gettext('blog.critical-window.storage.text4') }}</p>

  <figure>
    <div style="display: flex; flex-wrap: wrap; margin-bottom: 8px;">
        <a style="display: inline-block; max-width: 53%" href="https://en.wikipedia.org/wiki/History_of_hard_disk_drives"><img src="wikipedia-harddrives.svg" style="width: 100%"></a>
        <a style="display: inline-block; max-width: 47%" href="https://thecuberesearch.com/qlc-flash-hamrs-hdd/"><img src="wikibon-hdd.png" style="width: 100%"></a>
        <a style="display: inline-block; max-width: 45.5%" href="https://annas-archive.li/scidb/10.1063/1.5130404"><img src="tapeinthecloud.png" style="width: 100%"></a>
        <a style="display: inline-block; max-width: 54.5%" href="https://www.reddit.com/r/DataHoarder/comments/17sljc1/as_requested_an_improved_chart_of_ssd_vs_hdd/"><img src="reddit-hdd.png" style="width: 100%"></a>
    </div>
    <figcaption>{{ gettext('blog.critical-window.hdd-prices') }}</figcaption>
  </figure>

  <p>{{ gettext('blog.critical-window.storage.text5') }}</p>

  <h3>{{ gettext('blog.critical-window.storage.density') }}</h3>

  <p>{{ gettext('blog.critical-window.storage.density.text1') }}</p>

  <p>{{ gettext('blog.critical-window.storage.density.text2') }}</p>

  <p>{{ gettext('blog.critical-window.storage.density.text3') }}</p>

  <p>{{ gettext('blog.critical-window.storage.density.text4') }}</p>

  <figure>
    <a href="https://paperswithcode.com/sota/optical-character-recognition-on-benchmarking"><img src="chinese-ocr.png" style="max-width: 100%"></a>
    <figcaption>{{ gettext('blog.critical-window.ocr') }}</figcaption>
  </figure>

  <p>{{ gettext('blog.critical-window.storage.density.text5') }}</p>

  <p>{{ gettext('blog.critical-window.storage.density.text6') }}</p>

  <h2>{{ gettext('blog.critical-window.the-window') }}</h2>

  <p>{{ gettext('blog.critical-window.the-window.text1') }}</p>

  <p>{{ gettext('blog.critical-window.the-window.text2') }}</p>

  <p>{{ gettext('blog.critical-window.the-window.text3') }}</p>

  <p>{{ gettext('blog.critical-window.the-window.text4') }}</p>

  <p>{{ gettext('blog.critical-window.the-window.text5') }}</p>

  <p>{{ gettext('blog.critical-window.the-window.text6') }}</p>

  <p>{{ gettext('blog.critical-window.signature', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
