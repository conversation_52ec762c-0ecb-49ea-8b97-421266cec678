{% extends "layouts/blog.html" %}

{% block title %}{{ gettext('blog.duxiu-exclusive.title') }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction." />
<meta name="twitter:card" value="summary" />
<meta property="og:title" content="{{ gettext('blog.duxiu-exclusive.title') }}" />
<meta property="og:image" content="https://annas-archive.li/blog/duxiu-examples/1.jpg" />
<meta property="og:type" content="article" />
<meta property="og:url" content="https://annas-archive.li/blog/duxiu-exclusive.html" />
<meta property="og:description" content="Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction." />
<style>
  code { word-break: break-all; font-size: 89%; letter-spacing: -0.3px; }

  code ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 5px;
    height: 5px;
  }

  code ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(0, 0, 0, .3);
    box-shadow: 0 0 1px rgba(255, 255, 255, .3);
  }

  .code-block {
    background: #fffe9250;
    display: block;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em" t-msgid="blog.duxiu-exclusive.title">Exclusive access for LLM companies to largest Chinese non-fiction book collection in the world</h1>
  <p style="margin-top: 0; font-style: italic">
    annas-archive.li/blog, 2023-11-04, <span t-msgid="blog.duxiu-exclusive.subtitle"><a href="duxiu-exclusive-chinese.html">Chinese version 中文版</a>, <a href="https://news.ycombinator.com/item?id=38149093">Discuss on Hacker News</a></span>
  </p>

  <p class="tldr" t-msgid="blog.duxiu-exclusive.tldr">
    <em><strong>TL;DR:</strong> Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction.</em>
  </p>

  <p t-msgid="blog.duxiu-exclusive.text1">
    This is a short blog post. We’re looking for some company or institution to help us with OCR and text extraction for a massive collection we acquired, in exchange for exclusive early access. After the embargo period, we will of course release the entire collection.
  </p>

  <p t-msgid="blog.duxiu-exclusive.text2">
    High-quality academic text is extremely useful for training of LLMs. While our collection is Chinese, this should be even useful for training English LLMs: models seem encode concepts and knowledge regardless of the source language.
  </p>

  <p t-msgid="blog.duxiu-exclusive.text3">
    For this, text needs to be extracted from the scans. What does Anna’s Archive get out of it? Full-text search of the books for its users.
  </p>

  <p t-msgid="blog.duxiu-exclusive.text4">
    Because our goals align with that of LLM developers, we’re looking for a collaborator. We’re willing to give you <strong>exclusive early access to this collection in bulk for 1 year</strong>, if you can do proper OCR and text extraction. If you’re willing to share the entire code of your pipeline with us, we’d be willing to embargo the collection for longer.
  </p>

  <h3 t-msgid="blog.duxiu-exclusive.example_pages">Example pages</h3>

  <p t-msgid="blog.duxiu-exclusive.example_pages.text1">
    To prove to us that you have a good pipeline, here are some example pages to get started on, from a book on superconductors. Your pipeline should properly handle math, tables, charts, footnotes, and so on.
  </p>

  <div style="display: flex; width: 100%">
    <a style="width: 50%" href="duxiu-examples/1.jpg"><img style="width: 100%" src="duxiu-examples/1.jpg"></a>
    <a style="width: 50%" href="duxiu-examples/2.jpg"><img style="width: 100%" src="duxiu-examples/2.jpg"></a>
  </div>
  <div style="display: flex; width: 100%">
    <a style="width: 50%" href="duxiu-examples/3.jpg"><img style="width: 100%" src="duxiu-examples/3.jpg"></a>
    <a style="width: 50%" href="duxiu-examples/4.jpg"><img style="width: 100%" src="duxiu-examples/4.jpg"></a>
  </div>

  <p t-msgid="blog.duxiu-exclusive.example_pages.text2">
    Send your processed pages to our email. If they look good, we will send you more in private, and we expect you to be able to quickly run your pipeline on those as well. Once we’re satisfied, we can make a deal.
  </p>

  <h3 t-msgid="blog.duxiu-exclusive.collection">Collection</h3>

  <p t-msgid="blog.duxiu-exclusive.collection.text1">
    Some more information about the collection. <a href="https://www.duxiu.com/bottom/about.html">Duxiu</a> is a massive database of scanned books, created by the <a href="https://www.chaoxing.com/">SuperStar Digital Library Group</a>. Most are academic books, scanned in order to make them available digitally to universities and libraries. For our English-speaking audience, <a href="https://library.princeton.edu/eastasian/duxiu">Princeton</a> and the <a href="https://guides.lib.uw.edu/c.php?g=341344&p=2303522">University of Washington</a> have good overviews. There is also an excellent article giving more background: <a href="https://doi.org/10.1016/j.acalib.2009.03.012">“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (look it up in Anna’s Archive).
  </p>

  <p t-msgid="blog.duxiu-exclusive.collection.text2">
    The books from Duxiu have long been pirated on the Chinese internet. Usually they are being sold for less than a dollar by resellers. They are typically distributed using the Chinese equivalent of Google Drive, which has often been hacked to allow for more storage space. Some technical details can be found <a href="https://github.com/duty-machine/duty-machine/issues/2010">here</a> and <a href="https://github.com/821/821.github.io/blob/7bbcdc8dd2ec4bb637480e054fe760821b4ad7b8/_Notes/IT/DX-CX.md">here</a>.
  </p>

  <p t-msgid="blog.duxiu-exclusive.collection.text3">
    Though the books have been semi-publicly distributed, it is quite difficult to obtain them in bulk. We had this high on our TODO-list, and allocated multiple months of full-time work for it. However, recently an incredible, amazing, and talented volunteer reached out to us, telling us they had done all this work already — at great expense. They shared the full collection with us, without expecting anything in return, except the guarantee of long-term preservation. Truly remarkable. They agreed to ask for help in this way to get the collection OCR'ed.
  </p>

  <p t-msgid="blog.duxiu-exclusive.collection.text4">
    The collection is 7,543,702 files. This is more than Library Genesis non-fiction (about 5.3 million). Total file size is about 359TB (326TiB) in its current form.
  </p>

  <p t-msgid="blog.duxiu-exclusive.collection.text5">
    We’re open to other proposals and ideas. Just contact us. Check out Anna’s Archive for more information about our collections, preservation efforts, and how you can help. Thanks!
  </p>

  <p t-msgid="blog.duxiu-exclusive.signoff">
    - Anna and the team (<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, <a href="https://t.me/+D0zemuNzEdgyOGVk">Telegram</a>)
  </p>
{% endblock %}
