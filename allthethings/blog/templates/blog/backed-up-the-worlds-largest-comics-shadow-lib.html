{% extends "layouts/blog.html" %}

{% set title = gettext('blog.backed-up-libgen-li.title') %}
{% set tldr = gettext('blog.backed-up-libgen-li.tldr') %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:image" content="https://annas-archive.li/blog/dr-gordon.jpg">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/backed-up-the-worlds-largest-comics-shadow-lib.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.backed-up-libgen-li.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2023-05-13, <span>{{ gettext('blog.backed-up-libgen-li.links', news_ycombinator=({"href": "https://news.ycombinator.com/item?id=35931040", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</span>
  </p>

  <p class="tldr">{{ gettext('blog.backed-up-libgen-li.tldr') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.text1') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.text2') }}</p>

  <figure>
    <img src="dr-gordon.jpg" style="width: 100%; max-width: 400px">
    <figcaption>{{ gettext('blog.backed-up-libgen-li.fig1') }}</figcaption>
  </figure>

  <h2>{{ gettext('blog.backed-up-libgen-li.forks') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.forks.text1') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.forks.text2') }}</p>

  <h2>{{ gettext('blog.backed-up-libgen-li.collaboration') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.collaboration.text1') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.collaboration.text2') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.collaboration.text3') }}</p>

  <h2>{{ gettext('blog.backed-up-libgen-li.collection') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.collection.text1') }}</p>

  <div>
    <div><code>/repository</code></div>
    <div><code>    /0</code></div>
    <div><code>    /1000</code></div>
    <div><code>    /2000</code></div>
    <div><code>    /3000</code></div>
    <div><code>    …</code></div>
    <div><code>/comics0</code></div>
    <div><code>/comics1</code></div>
    <div><code>/comics2</code></div>
    <div><code>/comics3</code></div>
    <div><code>/comics4</code></div>
  </div>

  <p>{{ gettext('blog.backed-up-libgen-li.collection.text2') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.collection.text3') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.collection.text4') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.collection.text5') }}</p>

  <figure>
    <img src="i-librarian.webp" style="width: 100%; max-width: 300px">
    <figcaption>“I, Librarian”</figcaption>
  </figure>

  <h2>{{ gettext('blog.backed-up-libgen-li.analysis') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.analysis.text1') }}</p>

  <ol>
    <li>{{ gettext('blog.backed-up-libgen-li.analysis.item1') }}</li>
    <li>{{ gettext('blog.backed-up-libgen-li.analysis.item2') }}</li>
    <li>{{ gettext('blog.backed-up-libgen-li.analysis.item3') }}</li>
    <li>{{ gettext('blog.backed-up-libgen-li.analysis.item4') }}</li>
  </ol>

  <p>{{ gettext('blog.backed-up-libgen-li.analysis.text2') }}</p>

  <h2>{{ gettext('blog.backed-up-libgen-li.fundraiser') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text1') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text2') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text3') }}</p>

<!--   <div style="background: #f6f6f6; padding: 16px 8px; border-radius: 8px; box-shadow: 0px 2px 4px 0px #00000020">
    {% include 'macros/fundraiser.html' %}
  </div>
 -->

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text4') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text5', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text6') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text7') }}</p>

  <ul>
    <li><em>comics0__shoutout_to_tosec.torrent</em> (kindly adopted by Anonymous)</li>
    <li>TBD…</li>
  </ul>

  <p>{{ gettext('blog.backed-up-libgen-li.fundraiser.text8', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <h2>{{ gettext('blog.backed-up-libgen-li.next') }}</h2>

  <p>{{ gettext('blog.backed-up-libgen-li.next.text1') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.next.text2') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.next.text3') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.next.text4') }}</p>

  <p>{{ gettext('blog.backed-up-libgen-li.signature', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
