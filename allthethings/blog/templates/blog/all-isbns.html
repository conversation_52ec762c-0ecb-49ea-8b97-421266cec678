{% extends "layouts/blog.html" %}

{% set title = gettext("blog.all-isbns.title") %}
{% set tldr = gettext("blog.all-isbns.tldr") %}

{% block title %}Visualizing All ISBNs — $10k by 2025-01-31{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:image" content="">
<meta property="og:type" content="article">
<meta property="og:url" content="https://annas-archive.li/blog/all-isbns.html">
<meta property="og:description" content="{{ tldr }}">
<style>
  .main {
    max-width: unset;
  }
  h1, h2, p, ul {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  figcaption {
    margin-top: 0;
    font-style: italic;
    text-align: center;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em">{{ gettext('blog.all-isbns.title') }}</h1>
  <p style="font-style: italic; margin-top: 0">
    annas-archive.li/blog, 2024-12-15
  </p>

  <p class="tldr">{{ gettext('blog.all-isbns.tldr') }}</p>

  <p>{{ gettext('blog.all-isbns.text1') }}</p>

  <div style="margin: 0 -20px">
    <div style="text-align: center; margin: 1em 0">
      <a target="_blank" href="isbn_images/all_isbns_smaller.png">
        <img src="isbn_images/all_isbns_smaller.png" style="max-width: 100%; margin: 0 auto">
      </a>
    </div>
  </div>

  <p>{{ gettext('blog.all-isbns.text2') }}</p>

  <p>{{ gettext('blog.all-isbns.text3') }}</p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns.background') }}</h2>

  <p>{{ gettext('blog.all-isbns.background.text1') }}</p>

  <p>{{ gettext('blog.all-isbns.background.text2') }}</p>

  <p>{{ gettext('blog.all-isbns.background.text3', blog=({"href": "/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html"} | xmlattr), blog_2=({"href": "/blog/worldcat-scrape.html"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns.background.text4', blog=({"href": "/blog/critical-window.html"} | xmlattr)) }}</p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns.visualizing') }}</h2>

  <p>{{ gettext('blog.all-isbns.visualizing.text1') }}</p>

  <img src="isbn_images/all_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/md5_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cadal_ssno_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cerlalc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/duxiu_ssid_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/edsebk_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/gbooks_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/goodreads_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ia_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbndb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbngrp_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/libby_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/nexusstc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/oclc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ol_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/rgb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/trantor_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">

  <p>
    <script>window.prevIndex = window.curIndex = 0;</script>
    <select class="js-switcher-select" onchange="document.querySelector('.js-switcher-img').src = document.querySelector('.js-switcher-link').href = 'isbn_images/' + this.value; if (this.selectedIndex !== window.curIndex) { window.prevIndex = window.curIndex; window.curIndex = this.selectedIndex; }">
        <!-- TODO:TRANSLATE -->
      <option value="all_isbns_smaller.png" selected>All ISBNs [all_isbns]</option>
      <option value="md5_isbns_smaller.png">Files in Anna’s Archive [md5]</option>
      <option value="cadal_ssno_isbns_smaller.png">CADAL SSNOs [cadal_ssno]</option>
      <option value="cerlalc_isbns_smaller.png">CERLALC data leak [cerlalc]</option>
      <option value="duxiu_ssid_isbns_smaller.png">DuXiu SSIDs [duxiu_ssid]</option>
      <option value="edsebk_isbns_smaller.png">EBSCOhost’s eBook Index [edsebk]</option>
      <option value="gbooks_isbns_smaller.png">Google Books [gbooks]</option>
      <option value="goodreads_isbns_smaller.png">Goodreads [goodreads]</option>
      <option value="ia_isbns_smaller.png">Internet Archive [ia]</option>
      <option value="isbndb_isbns_smaller.png">ISBNdb [isbndb]</option>
      <option value="isbngrp_isbns_smaller.png">ISBN Global Register of Publishers [isbngrp]</option>
      <option value="libby_isbns_smaller.png">Libby [libby]</option>
      <option value="nexusstc_isbns_smaller.png">Nexus/STC [nexusstc]</option>
      <option value="oclc_isbns_smaller.png">OCLC/Worldcat [oclc]</option>
      <option value="ol_isbns_smaller.png">OpenLibrary [ol]</option>
      <option value="rgb_isbns_smaller.png">Russian State Library [rgb]</option>
      <option value="trantor_isbns_smaller.png">Imperial Library of Trantor [trantor]</option>
    </select>
      
    <!-- TODO:TRANSLATE -->
    <button title="Back" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex - 1 + select.options.length) % select.options.length; select.onchange()">⬅️</button>
    <button title="Forward" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex + 1) % select.options.length; select.onchange()">➡️</button>
    <button title="Last" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = window.prevIndex; select.onchange()">🔄</button>
  </p>

  <div style="margin: 0 -20px">
    <div style="text-align: center; margin: 1em 0">
      <a class="js-switcher-link" target="_blank" href="isbn_images/all_isbns_smaller.png">
        <img class="js-switcher-img" src="isbn_images/all_isbns_smaller.png" style="max-width: 100%; margin: 0 auto">
      </a>
    </div>
  </div>

  <p>{{ gettext('blog.all-isbns.visualizing.text2') }}</p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns.bounty') }}</h2>

  <p>{{ gettext('blog.all-isbns.bounty.text1', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns.bounty.text2') }}</p>

  <p>{{ gettext('blog.all-isbns.bounty.text3') }}</p>

  <ul>
    <li>{{ gettext('blog.all-isbns.bounty.req1') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.req2') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.req3') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.req4', github_xlcnd_isbnlib=({"href": "https://github.com/xlcnd/isbnlib/blob/dev/isbnlib/_data/data4info.py", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), annas_archive=({"href": "https://annas-archive.org/datasets/other_metadata"} | xmlattr), annas_archive_2=({"href": "https://annas-archive.org/torrents/other_metadata"} | xmlattr)) }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.req5') }}</li>
  </ul>

  <p>{{ gettext('blog.all-isbns.bounty.text4') }}</p>

  <ul>
    <li>{{ gettext('blog.all-isbns.bounty.bonus1') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus2') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus3') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus4') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus5') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus6') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus7') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus8') }}</li>
    <li>{{ gettext('blog.all-isbns.bounty.bonus9') }}</li>
  </ul>

  <p>{{ gettext('blog.all-isbns.bounty.text5') }}</p>

  <p>{{ gettext('blog.all-isbns.bounty.text6', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244"} | xmlattr)) }}</p>

  <h2 style="margin-top: 1.5em;">{{ gettext('blog.all-isbns.bounty.code') }}</h2>

  <p>{{ gettext('blog.all-isbns.bounty.code.text1', annas_archive=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/isbn_images"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns.bounty.code.text2', annas_archive_l1244_1319=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/369f1ae1074d8545eaeaf217ad690e505ef1aad1/allthethings/cli/views.py?page=2#L1244-1319"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.all-isbns.bounty.code.text3') }}</p>

  <p>{{ gettext('blog.all-isbns.signature', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
