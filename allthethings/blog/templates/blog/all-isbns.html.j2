{% extends "layouts/blog.html" %}

{% set title = gettext("blog.all-isbns.title") %}
{% set tldr = gettext("blog.all-isbns.tldr") %}

{% block title %}Visualizing All ISBNs — $10k by 2025-01-31{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}" />
<meta name="twitter:card" value="summary" />
<meta property="og:title" content="{{ title }}" />
<meta property="og:image" content="" />
<meta property="og:type" content="article" />
<meta property="og:url" content="https://annas-archive.li/blog/all-isbns.html" />
<meta property="og:description" content="{{ tldr }}" />
<style>
  .main {
    max-width: unset;
  }
  h1, h2, p, ul {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  figcaption {
    margin-top: 0;
    font-style: italic;
    text-align: center;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em" t-msgid="blog.all-isbns.title">Visualizing All ISBNs — $10,000 bounty by 2025-01-31</h1>
  <p style="font-style: italic; margin-top: 0">
    annas-archive.li/blog, 2024-12-15
  </p>

  <p class="tldr" t-msgid="blog.all-isbns.tldr">This picture represents the largest fully open “list of books” ever assembled in the history of humanity.</p>

  <p t-msgid="blog.all-isbns.text1">This picture is 1000×800 pixels. Each pixel represents 2,500 ISBNs. If we have a file for an ISBN, we make that pixel more green. If we know an ISBN has been issued, but we don’t have a matching file, we make it more red.</p>

  <div style="margin: 0 -20px">
    <div style="text-align: center; margin: 1em 0">
      <a target="_blank" href="isbn_images/all_isbns_smaller.png">
        <img src="isbn_images/all_isbns_smaller.png" style="max-width: 100%; margin: 0 auto">
      </a>
    </div>
  </div>

  <p t-msgid="blog.all-isbns.text2">In less than 300kb, this picture succinctly represents the largest fully open “list of books” ever assembled in the history of humanity (a few hundred GB compressed in full).</p>

  <p t-msgid="blog.all-isbns.text3">It also shows: there is a lot of work left in backing up books (we only have 16%).</p>

  <h2 style="margin-top: 1.5em;"  t-msgid="blog.all-isbns.background">Background</h2>

  <p t-msgid="blog.all-isbns.background.text1">How can Anna’s Archive achieve its mission of backing up all of humanity’s knowledge, without knowing which books are still out there? We need a TODO list. One way to map this out is through ISBN numbers, which since the 1970s have been assigned to every book published (in most countries).</p>

  <p t-msgid="blog.all-isbns.background.text2">There is no central authority that knows all ISBN assignments. Instead, it’s a distributed system, where countries get ranges of numbers, who then assign smaller ranges to major publishers, who might further sub-divide ranges to minor publishers. Finally individual numbers are assigned to books.</p>

  <p t-msgid="blog.all-isbns.background.text3">We started mapping ISBNs <a href="/blog/blog-isbndb-dump-how-many-books-are-preserved-forever.html">two years ago</a> with our scrape of ISBNdb. Since then, we have scraped many more metadata sources, such as <a href="/blog/worldcat-scrape.html">Worldcat</a>, Google Books, Goodreads, Libby, and more. A full list can be found on the “Datasets” and “Torrents” pages on Anna’s Archive. We now have by far the largest fully open, easily downloadable collection of book metadata (and thus ISBNs) in the world.</p>

  <p t-msgid="blog.all-isbns.background.text4">We’ve <a href="/blog/critical-window.html">written extensively</a> about why we care about preservation, and why we’re currently in a critical window. We must now identify rare, underfocused, and uniquely at-risk books and preserve them. Having good metadata on all books in the world helps with that.</p>

  <h2 style="margin-top: 1.5em;" t-msgid="blog.all-isbns.visualizing">Visualizing</h2>

  <p t-msgid="blog.all-isbns.visualizing.text1">Besides the overview image, we can also look at individual datasets we’ve acquired. Use the dropdown and buttons to switch between them.</p>

  <img src="isbn_images/all_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/md5_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cadal_ssno_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/cerlalc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/duxiu_ssid_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/edsebk_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/gbooks_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/goodreads_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ia_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbndb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/isbngrp_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/libby_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/nexusstc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/oclc_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/ol_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/rgb_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">
  <img src="isbn_images/trantor_isbns_smaller.png" style="position:absolute; visibility:hidden; width:1px">

  <p>
    <script>window.prevIndex = window.curIndex = 0;</script>
    <select class="js-switcher-select" onchange="document.querySelector('.js-switcher-img').src = document.querySelector('.js-switcher-link').href = 'isbn_images/' + this.value; if (this.selectedIndex !== window.curIndex) { window.prevIndex = window.curIndex; window.curIndex = this.selectedIndex; }">
        <!-- TODO:TRANSLATE -->
      <option value="all_isbns_smaller.png" selected>All ISBNs [all_isbns]</option>
      <option value="md5_isbns_smaller.png">Files in Anna’s Archive [md5]</option>
      <option value="cadal_ssno_isbns_smaller.png">CADAL SSNOs [cadal_ssno]</option>
      <option value="cerlalc_isbns_smaller.png">CERLALC data leak [cerlalc]</option>
      <option value="duxiu_ssid_isbns_smaller.png">DuXiu SSIDs [duxiu_ssid]</option>
      <option value="edsebk_isbns_smaller.png">EBSCOhost’s eBook Index [edsebk]</option>
      <option value="gbooks_isbns_smaller.png">Google Books [gbooks]</option>
      <option value="goodreads_isbns_smaller.png">Goodreads [goodreads]</option>
      <option value="ia_isbns_smaller.png">Internet Archive [ia]</option>
      <option value="isbndb_isbns_smaller.png">ISBNdb [isbndb]</option>
      <option value="isbngrp_isbns_smaller.png">ISBN Global Register of Publishers [isbngrp]</option>
      <option value="libby_isbns_smaller.png">Libby [libby]</option>
      <option value="nexusstc_isbns_smaller.png">Nexus/STC [nexusstc]</option>
      <option value="oclc_isbns_smaller.png">OCLC/Worldcat [oclc]</option>
      <option value="ol_isbns_smaller.png">OpenLibrary [ol]</option>
      <option value="rgb_isbns_smaller.png">Russian State Library [rgb]</option>
      <option value="trantor_isbns_smaller.png">Imperial Library of Trantor [trantor]</option>
    </select>
    &nbsp;&nbsp;
    <!-- TODO:TRANSLATE -->
    <button title="Back" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex - 1 + select.options.length) % select.options.length; select.onchange()">⬅️</button>
    <button title="Forward" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = (select.selectedIndex + 1) % select.options.length; select.onchange()">➡️</button>
    <button title="Last" style="border: none; background: none; cursor: pointer" onclick="var select = document.querySelector('.js-switcher-select'); select.selectedIndex = window.prevIndex; select.onchange()">🔄</button>
  </p>

  <div style="margin: 0 -20px">
    <div style="text-align: center; margin: 1em 0">
      <a class="js-switcher-link" target="_blank" href="isbn_images/all_isbns_smaller.png">
        <img class="js-switcher-img" src="isbn_images/all_isbns_smaller.png" style="max-width: 100%; margin: 0 auto">
      </a>
    </div>
  </div>

  <p t-msgid="blog.all-isbns.visualizing.text2">There are lots of interesting patterns to see in these pictures. Why is there some regularity of lines and blocks, that seems to happen at different scales? What are the empty areas? Why are certain datasets so clustered? We’ll leave these questions as an exercise for the reader.</p>

  <h2 style="margin-top: 1.5em;" t-msgid="blog.all-isbns.bounty">$10,000 bounty</h2>

  <p t-msgid="blog.all-isbns.bounty.text1">There is much to explore here, so we’re announcing a bounty for improving the visualization above. Unlike most of our bounties, this one is time-bound. You have to <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244">submit</a> your open source code by 2025-01-31 (23:59 UTC).</p>

  <p t-msgid="blog.all-isbns.bounty.text2">The best submission will get $6,000, second place is $3,000, and third place is $1,000. All bounties will be awarded using Monero (XMR).</p>

  <p t-msgid="blog.all-isbns.bounty.text3">Below are the minimal criteria. If no submission meets the criteria, we might still award some bounties, but that will be at our discretion.</p>

  <ul>
    <li t-msgid="blog.all-isbns.bounty.req1">Fork this repo, and edit this blog post HTML (no other backends besides our Flask backend are allowed).</li>
    <li t-msgid="blog.all-isbns.bounty.req2">Make the picture above smoothly zoomable, so you can zoom all the way to individual ISBNs. Clicking ISBNs should take you to a metadata page or search on Anna’s Archive.</li>
    <li t-msgid="blog.all-isbns.bounty.req3">You must still be able to switch between all different datasets.</li>
    <li t-msgid="blog.all-isbns.bounty.req4">Country ranges and publisher ranges should be highlighted on hover. You can use e.g. <a href="https://github.com/xlcnd/isbnlib/blob/dev/isbnlib/_data/data4info.py">data4info.py in isbnlib</a> for country info, and our “isbngrp” scrape for publishers (<a href="https://annas-archive.org/datasets/other_metadata">dataset</a>, <a href="https://annas-archive.org/torrents/other_metadata">torrent</a>).</li>
    <li t-msgid="blog.all-isbns.bounty.req5">It must work well on desktop and mobile.</li>
  </ul>

  <p t-msgid="blog.all-isbns.bounty.text4">For bonus points (these are just ideas — let your creativity run wild):</p>

  <ul>
    <li t-msgid="blog.all-isbns.bounty.bonus1">Strong consideration will be given to usability and how good it looks.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus2">Show actual metadata for individual ISBNs when zooming in, such as title and author.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus3">Better space-filling curve. E.g. a zig-zag, going from 0 to 4 on the first row and then back (in reverse) from 5 to 9 on the second row — recursively applied.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus4">Different or customizable color schemes.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus5">Special views for comparing datasets.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus6">Ways to debug issues, such as other metadata that don’t agree well (e.g. vastly different titles).</li>
    <li t-msgid="blog.all-isbns.bounty.bonus7">Annotating images with comments on ISBNs or ranges.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus8">Any heuristics for identifying rare or at-risk books.</li>
    <li t-msgid="blog.all-isbns.bounty.bonus9">Whatever creative ideas you can come up with!</li>
  </ul>

  <p t-msgid="blog.all-isbns.bounty.text5">
    You MAY completely veer off from the minimal criteria, and do a completely different visualization. If it’s really spectacular, then that qualifies for the bounty, but at our discretion.
  </p>

  <p t-msgid="blog.all-isbns.bounty.text6">
    Make submissions by posting a comment to <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/issues/244">this issue</a> with a link to your forked repo, merge request, or diff.
  </p>

  <h2 style="margin-top: 1.5em;" t-msgid="blog.all-isbns.bounty.code">Code</h2>

  <p t-msgid="blog.all-isbns.bounty.code.text1">The code to generate these images, as well as other examples, can be found in <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/tree/main/isbn_images">this directory</a>.</p>

  <p t-msgid="blog.all-isbns.bounty.code.text2">We came up with a compact data format, with which all the required ISBN information is about 75MB (compressed). The description of the data format and code to generate it can be found <a href="https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/369f1ae1074d8545eaeaf217ad690e505ef1aad1/allthethings/cli/views.py?page=2#L1244-1319">here</a>. For the bounty you’re not required to use this, but it is probably the most convenient format to get started with. You can transform our metadata however you want (though all your code has to be open source).</p>

  <p t-msgid="blog.all-isbns.bounty.code.text3">We can’t wait to see what you come up with. Good luck!</p>

  <p t-msgid="blog.all-isbns.signature">
    - Anna and the team (<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, <a href="https://t.me/+D0zemuNzEdgyOGVk">Telegram</a>)
  </p>
{% endblock %}
