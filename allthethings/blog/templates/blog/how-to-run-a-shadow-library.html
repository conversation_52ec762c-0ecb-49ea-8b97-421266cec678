{% extends "layouts/blog.html" %}

{% set title = gettext("blog.how-to-run.title") %}
{% set tldr = gettext("blog.how-to-run.tldr") %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:image" content="https://annas-archive.li/blog/copyright-bell-curve.png">
<meta property="og:url" content="http://annas-archive.li/blog/how-to-run-a-shadow-library.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.how-to-run.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2023-03-19
  </p>

  <p class="tldr">{{ gettext('blog.how-to-run.tldr') }}</p>

  <p>{{ gettext('blog.how-to-run.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), wikipedia_shadow_library=({"href": "https://en.wikipedia.org/wiki/Shadow_library", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), blog_isbndb_dump_how_many_books_are_preserved_forever=({"href": "blog-isbndb-dump-how-many-books-are-preserved-forever.html"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.how-to-run.text2') }}</p>

  <p>{{ gettext('blog.how-to-run.text3', blog_how_to_become_a_pirate_archivist=({"href": "blog-how-to-become-a-pirate-archivist.html"} | xmlattr)) }}</p>

  <h2>{{ gettext('blog.how-to-run.innovation-tokens') }}</h2>

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text1', mcfunley=({"href": "https://mcfunley.com/choose-boring-technology", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text2') }}</p>

  <img src="copyright-bell-curve.png" style="max-width: 100%">

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text3') }}</p>

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text4') }}</p>

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text5') }}</p>

  <p>{{ gettext('blog.how-to-run.innovation-tokens.text6') }}</p>

  <h2>{{ gettext('blog.how-to-run.architecture') }}</h2>

  <p>{{ gettext('blog.how-to-run.architecture.text1') }}</p>

  <img src="diagram1.svg" style="max-width: 100%">

  <p>{{ gettext('blog.how-to-run.architecture.text2') }}</p>

  <img src="diagram2.svg" style="max-width: 100%">

  <p>{{ gettext('blog.how-to-run.architecture.text3', blog_cloudflare=({"href": "https://blog.cloudflare.com/cloudflares-abuse-policies-and-approach/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <img src="diagram3.svg" style="max-width: 100%">

  <p>{{ gettext('blog.how-to-run.architecture.text4', annas_archive_l255=({"href": "https://software.annas-archive.li/AnnaArchivist/annas-archive/-/blob/0f730afd4cc9612ef0c12c0f1b46505a4fd1c724/allthethings/templates/layouts/index.html#L255"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.how-to-run.architecture.text5') }}</p>

  <img src="diagram4.svg" style="max-width: 100%">

  <p>{{ gettext('blog.how-to-run.architecture.text6') }}</p>

  <h2>{{ gettext('blog.how-to-run.tools') }}</h2>

  <p>{{ gettext('blog.how-to-run.tools.text1') }}</p>

  <ul>
    <li>{{ gettext('blog.how-to-run.tools.app') }}</li>
    <li>{{ gettext('blog.how-to-run.tools.proxy') }}</li>
    <li>{{ gettext('blog.how-to-run.tools.management') }}</li>
    <li>{{ gettext('blog.how-to-run.tools.dev') }}</li>
    <li>{{ gettext('blog.how-to-run.tools.onion') }}</li>
  </ul>

  <p>{{ gettext('blog.how-to-run.tools.text2', github_costela_wesher=({"href": "https://github.com/costela/wesher", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), github_k4yt3x_wg_meshconf=({"href": "https://github.com/k4yt3x/wg-meshconf", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), github_sshuttle=({"href": "https://github.com/sshuttle/sshuttle/issues/830", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.how-to-run.tools.text3') }}</p>

  <p>{{ gettext('blog.how-to-run.tools.text4') }}</p>

  <p>{{ gettext('blog.how-to-run.tools.text5') }}</p>

  <h2>{{ gettext('blog.how-to-run.conclusions') }}</h2>

  <p>{{ gettext('blog.how-to-run.conclusions.text1') }}</p>

  <p>{{ gettext('blog.how-to-run.conclusions.text2') }}</p>

  <p>{{ gettext('blog.how-to-run.signature', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
