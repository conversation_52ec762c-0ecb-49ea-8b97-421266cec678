{% extends "layouts/blog.html" %}

{% block title %}{{ gettext('blog.duxiu-exclusive.title') }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction.">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ gettext('blog.duxiu-exclusive.title') }}">
<meta property="og:image" content="https://annas-archive.li/blog/duxiu-examples/1.jpg">
<meta property="og:type" content="article">
<meta property="og:url" content="https://annas-archive.li/blog/duxiu-exclusive.html">
<meta property="og:description" content="Anna’s Archive acquired a unique collection of 7.5 million / 350TB Chinese non-fiction books — larger than Library Genesis. We’re willing to give an LLM company exclusive access, in exchange for high-quality OCR and text extraction.">
<style>
  code { word-break: break-all; font-size: 89%; letter-spacing: -0.3px; }

  code ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 5px;
    height: 5px;
  }

  code ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(0, 0, 0, .3);
    box-shadow: 0 0 1px rgba(255, 255, 255, .3);
  }

  .code-block {
    background: #fffe9250;
    display: block;
  }
</style>
{% endblock %}

{% block body %}
  <h1 style="font-size: 26px; margin-bottom: 0.25em">{{ gettext('blog.duxiu-exclusive.title') }}</h1>
  <p style="margin-top: 0; font-style: italic">
    annas-archive.li/blog, 2023-11-04, <span>{{ gettext('blog.duxiu-exclusive.subtitle', duxiu_exclusive_chinese=({"href": "duxiu-exclusive-chinese.html"} | xmlattr), news_ycombinator=({"href": "https://news.ycombinator.com/item?id=38149093", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</span>
  </p>

  <p class="tldr">{{ gettext('blog.duxiu-exclusive.tldr') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.text1') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.text2') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.text3') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.text4') }}</p>

  <h3>{{ gettext('blog.duxiu-exclusive.example_pages') }}</h3>

  <p>{{ gettext('blog.duxiu-exclusive.example_pages.text1') }}</p>

  <div style="display: flex; width: 100%">
    <a style="width: 50%" href="duxiu-examples/1.jpg"><img style="width: 100%" src="duxiu-examples/1.jpg"></a>
    <a style="width: 50%" href="duxiu-examples/2.jpg"><img style="width: 100%" src="duxiu-examples/2.jpg"></a>
  </div>
  <div style="display: flex; width: 100%">
    <a style="width: 50%" href="duxiu-examples/3.jpg"><img style="width: 100%" src="duxiu-examples/3.jpg"></a>
    <a style="width: 50%" href="duxiu-examples/4.jpg"><img style="width: 100%" src="duxiu-examples/4.jpg"></a>
  </div>

  <p>{{ gettext('blog.duxiu-exclusive.example_pages.text2') }}</p>

  <h3>{{ gettext('blog.duxiu-exclusive.collection') }}</h3>

  <p>{{ gettext('blog.duxiu-exclusive.collection.text1', duxiu=({"href": "https://www.duxiu.com/bottom/about.html", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), chaoxing=({"href": "https://www.chaoxing.com/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), library_princeton=({"href": "https://library.princeton.edu/eastasian/duxiu", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), guides_lib_uw=({"href": "https://guides.lib.uw.edu/c.php?g=341344&p=2303522", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), doi=({"href": "https://doi.org/10.1016/j.acalib.2009.03.012", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.collection.text2', github_duty_machine=({"href": "https://github.com/duty-machine/duty-machine/issues/2010", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), github_821_github_io=({"href": "https://github.com/821/821.github.io/blob/7bbcdc8dd2ec4bb637480e054fe760821b4ad7b8/_Notes/IT/DX-CX.md", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.collection.text3') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.collection.text4') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.collection.text5') }}</p>

  <p>{{ gettext('blog.duxiu-exclusive.signoff', reddit=({"href": "https://www.reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr), t_me=({"href": "https://t.me/+D0zemuNzEdgyOGVk", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
{% endblock %}
