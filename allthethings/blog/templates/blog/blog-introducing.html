{% extends "layouts/blog.html" %}

{% set title = gettext("blog.introducing.title") %}
{% set tldr = "" %}

{% block title %}{{ title }}{% endblock %}

{% block meta_tags %}
<meta name="description" content="{{ tldr }}">
<meta name="twitter:card" value="summary">
<meta property="og:title" content="{{ title }}">
<meta property="og:type" content="article">
<meta property="og:url" content="http://annas-archive.li/blog/blog-introducing.html">
<meta property="og:description" content="{{ tldr }}">
{% endblock %}

{% block body %}
  <h1>{{ gettext('blog.introducing.title') }}</h1>
  <p style="font-style: italic">
    annas-archive.li/blog, 2022-07-01
  </p>
  <p>{{ gettext('blog.introducing.text1', wikipedia_annas_archive=({"href": "https://en.wikipedia.org/wiki/Anna%27s_Archive", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
  <p>{{ gettext('blog.introducing.text2') }}</p>
  <ul>
    <li>{{ gettext('blog.introducing.focus.pirate') }}</li>
    <li>{{ gettext('blog.introducing.focus.library') }}</li>
    <li>{{ gettext('blog.introducing.focus.mirror') }}</li>
  </ul>
  <p>{{ gettext('blog.introducing.text3') }}</p>
  <p>{{ gettext('blog.introducing.text4') }}</p>
  <p>{{ gettext('blog.introducing.text5') }}</p>
  <p>{{ gettext('blog.introducing.text6') }}</p>
  <p>{{ gettext('blog.introducing.signature', reddit=({"href": "https://reddit.com/r/Annas_Archive/", "rel": "noopener noreferrer nofollow", "target": "_blank"} | xmlattr)) }}</p>
  <p>{{ gettext('blog.introducing.footnote') }}</p>
{% endblock %}
