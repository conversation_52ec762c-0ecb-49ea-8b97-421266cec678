��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b y  ud P  �f +   @h   lh �  �i �  gk P  1m    �n N   �o w   �o E   jp m   �p �  q h  �r 	  't �  1u �   *w   x �   z    | �   -~ �  �~ �  � A   �� �   � I   ǃ �  �    �� :  � E   P� '   �� 
   ��    ̈ ;   � "    �    C� 3   U�     �� /   ��    ډ I   �� +  @�   l� N   |� 4   ˌ     �    �    �    -�    9�    V� 	   c�    m�    �     ��    ��    �� 	   ȍ 
   ҍ    ��    �    �    %� m  A� i  �� �   � +   �� P  ؒ �   )� �   ̔ �   ��    �� �   ��    M� �   j� !   )� &  K�    r�   �� @   � l   P�    �� Z  ě =  � �  ]� (  )�    R� H   d� [   �� �   	� ,   �� *   � k   � 4   �� <   �� .   ��    &� w   -� �  �� ;   b� o   �� �   � :   ܧ H  � +   `�   �� �   �� �   :� T   ث �   -� �   � E   y� �   �� �   �� i   h� F   ү y   � 
   �� �   ��   D� E   _�    �� �   �� �   Q�    ;� �  C� �   ѵ   �� {  �� �   �� =  � �   "� Q   "� G   t� V   �� z   � m   �� W   �� +   T� �   �� v   � J   ��    �� l   �� U   b� �   �� �   p�    � 	  $�   .� P  @� H  �� �   �� �   ]� 6  K� �   �� �   /� �   �� r  z� �   �� �   ��    �� �   �� �   F� �    � s  �� �   T� �   �� �   d� �  � �   �� ?  m� /  �� &   �� X   � �   ]� Z   �� `  Q� �   �� u   s� �   �� '   k� !  �� �   �� �  y�    � _   � �   {� �   �   �� �   �� �  �� �  �� /   B� n  r� �  �� *  �� �  �� .  .� 	  ]� m   g� �   ��    c� �   k�   0� v  L� '  �� �   �� �   ��    �� a  �� ^  �� �   W     > �   K )  � �    �  � �   � \   �     �   V  �    �	 �   
    9   �   N [    l   | E   �   / 3   5    i ?  y �   �   � 5   � E      H v   f w   � d   U z  �    5    U �  r �   � �   � S   N �   � (   ,   U   ]     l! 	   �! '   �! A   �! /   �! 9   (" N   b" r   �"    $# )   8# =   b# H   �# &   �# 4   $ 0   E$ i   v$    �$    �$ !  % ;  #& �   _' �   A( �   0) �   �) L  r*    �+ �   �+ �   �, �   |- @  &. `  g/   �0 �  �2 �  }4 E   6 C   V6 .   �6 �   �6   p7 �  �9 +  G; ^  s< 0  �=   ? &  A '   6B I  ^B 1  �C y  �D    TF �   eF `  ZG �   �H q  JI �   �J    �K +   �K d   �K 	   RL y  \L   �N c  VP �   �R �   eS    T �   /T �   +U E   !V p   gV '  �V    X �   Y �  �Y g   8[ %  �[    �\ �  �\ �   �^ �  c_ �  1a �  �c �   �e    Of �   [f �  Dg    i c  (i �  �j 	  �l   �n �   �p =  �q E   �r �  s �    u    �u A   Ev R   �v 	   �v @   �v !   %w +   Gw "   sw    �w �   �w �  Bx �  �z �  �| �   �~ 	   � �  � %  �� r  �� �  (� A  �� =  �    )�   5�    :�    C� �  W� '  �� �  !� ]  �    u� �  �� �  g� �  A� �  � �  ��   ޟ ~  �� "   }� �   �� [   U� �  �� �  �� �   � �   ǩ �   J� d  �    V� �   r� J   � A   _�    �� 0   �� @   � (  )� �  R� i  � 3   Z� �  �� �   J�    �� �   � @   �� �   �� �   v� +   6� c   b�    ƺ S  ں   .� �  J� [  � x   O�    �� d   ��   F�    J� �   Q� �  �� �   {� 0   � /   A� ]  q� +   �� I  �� �  E� �  �� "   �� {   �� �   ^� m  �� �  T� 
   
� �   � ,   � �   5� �   �� '  �� �   �� N   �� +   ��   � :   &� �  a� P  � �   e� �   0� `   '� A   �� �   ��   t� p  |� �  �� �  �� 9   v� �   �� (  �� <  �� �   �� �  �� �  �� +   h� �  �� ,   �� �  �� S   ��    ��   ��   �� v  � �   }� #  }� �  ��   �� �   � �  A �  
 �   � �  K T  I	 :   �
 �   �
 �   a    �     7   �   C
 .   �
 $   �  D $  � �     � �   | �  . @  �    1 �   F     z       �    �    �    � %   �            )    ?    F 
   X    c 	   j    t    | 
   � 6   �    � 
   �    �    � �   � X   � !       < *   I &   t /   � 2   �     � 
       *    1    A    T    g    ~    �    �    � /   �    �      "     &   ?  -   f  2   �     �  +   �  1   !    8! T   M! B   �!    �! \   �! >   H"    �"    �" %   �"    �"    �"    #    $#    :#    I#    Q#    `#    m#    �# 	   �# 
   �#    �# "   �#    �#    �# 	   �#    �# 	   �#    $    $    %$ 	   ,$    6$    F$    R$    k$    s$    �$    �$    �$ 	   �$    �$ $   �$    %    
% %   %    9%    B%    ^%    e%    }%    �%    �% l   �% �   $& G   �& �   ' �  �' u   k)    �)    �)    
*    *    #*    /* 
   G* +   R* O   ~* ,   �* M   �* !   I+ 5   k+ *   �+ [   �+ Z   (, �   �, ^   ^- 4   �- %   �-    .    *.    2.    ;.    D.    U.    j.    o.    �.    �.    �.    �.    �.    �.    �.    �.    �.    �.     / 	   /    /    /    &/    @/   R/    a0    e0    t0     z0    �0 P   �0 \   �0 '   P1 -   x1 F   �1    �1    �1    �1 y    2    z2    �2 *   �2 u   �2    23    N3 n   a3 $   �3 2  �3 Y   (7 �   �7 �   8 �   �8 4   j9 �   �9 �   �: a  ; �   x<    `=    {= O   �= ?   �= N   > ]   _> O   �> K   
? a   Y? "   �? 1   �?    @    @ F   &@ &   m@    �@    �@    �@    �@ s   �@    <A *   KA D   vA    �A    �A Q   �A B   @B _  �B    �C    �C �   
D 
   �D    �D    �D    �D    
E .   E `  EE    �F    �F    �F 	   �F +  �F *   H    DH    LH Y   TH    �H    �H 1   �H    �H    	I )   I �   :I    �I    �I @   J !   FJ    hJ    zJ 
   �J $   �J N   �J    K 9   K �   LK f   �K K   QL    �L �   �L E   �M    �M 7   �M    )N �   >N �   �N    �O L   �O �   P �   �P     aQ "   �Q 
   �Q K  �Q %   �R '   %S    MS )   kS    �S �   �S    =T    \T 7   wT @   �T    �T    �T #   U #   3U 7  WU f  �W   �X 5   [ 3   =[    q[ "   ~[ I  �[ �   �\ �   �]    u^ �   �^ �  ~_    	a    'a   Fa U  Wc    �d 
   �d 6   �d    �d :  e     >f 7  _f �  �g �   Fi    2j �   Jj 5   �j (   k g   .k .  �k   �l �   �m �  ^n n   �o �   cp Z   Uq �   �q �   }r I   +s �   us 0    t +   Qt    }t    �t    �t )   �t #   �t A   �t 3   <u 	   pu 3   zu 1  �u :   �v �   w ~   x �   �x +   y "   By !   ey    �y .   �y #   �y /   �y &    z �   Gz '   -{ �   U{    | �   !|    �| �   _} 8  �} �   % -   � �   � 	   �� 2  �� h   ܁    E� �  \�    �    %�    <�    Q� )   p� 	   ��    �� J   �� �   �� �   �� 
   r�    }�    �� �   �� 8  ]� �   �� v   C�    ��    ω    �    ��    �    "�    3� G   ;� +   �� �  �� _   ��    � W   � s   p� P   � d   5� W   �� T   �    G� \   L� K   �� �   �� U   {� N   ѐ     � �   5� g   � >   �� b   Ò d   &� >   ��    ʓ 2   ӓ n   � �   u� 1   � �   C�    �� �   � J   � a   /� �   ��    3� �  <� �   %�    �    �    4�    <� �   B� 1   4� c   f� �   ʜ �   �� �   e� �   �� �   ʟ �   g� �   � \   ȡ   %� b   8� W  �� �   � �   �� 
   �� 
   �� 
   �� �   Ʀ 
   V� 
   d� M   r� ]   �� �   � 
   � j   �� 8   g� {   �� 9   � z   V� ]   Ѫ 
   /� �   =� 
   ȫ 
   ֫ 
   � G  � �   :� �  ��    o�    ��    �� �   �� !   @� ,   b� �   �� �   �� !   2�    T�    d� =   �� ?   �� 5    �    6� "   T� �   w�    ]� �  |� �   � �   � L   �� �   ��   �� �  �� %  �� �   �� �   n� �   ��    ~� b  �� $   �� g   � �   �� "  j� �   �� /  � �   E� }   �� v   h� &   �� ?   �    F� -   `�    ��    ��    �� �   ��    �� l   �� 4   �    E�    J�    Q�    ]� r   |� I   �� 6   9�   p� '   y�    ��    ��    ��     ��    ��    ��    �    �    �    �    %�    ,� .   4� �   c�    ��    � 
   �    %� 
   1�    <� 
   H�    S�    _� 	   w� $   �� d   ��    � 5   � Q   Q� �   �� �   J� �   �� �   �� �   ��   P� 
   o� ~   }� 4   �� C   1� B   u� �   �� �   V� K   A� c   ��    �� n   � ]   q� �   ��    V�     ]�    ~�    ��    ��    ��    �� ,   ��    
�    �    +�     G�    h�    ��    ��    ��    ��    ��    ��    ��    � %   � #   .� ~   R� �   �� '   R� &   z� f   �� Y   � l   b� +   �� )   �� ,   %� 9   R� :   �� .   �� �   ��   �� &  ��     � <   � �   X� #   �� �   � �   �� y   �� ?   J�    �� �   �� �   ;� g   �� 3   J� f   ~� �   ��    �� '   ��    �� =    � �   >� �   ��    F�    N�    W� !   ^� �   �� /   � (   B� 5   k� $   ��    �� !   �� O   ��    N� ]   i� 9   �� �   � M   �� M   '� �   u� =   ,�    j�    ��    ��    ��    ��    �    '� 7   G� 9   � X  �� O   � 6   b� G   �� ,   ��    � l   � �   �� �   � �   ��    �� �   �� "   �    =� �   Q�    � +   "� @   N� <   �� U   �� `   "� U   ��    ��     �� �   � /   �� (   �� M   � �   _� ;   I� �   �� c   � b   t� y   �� (   Q� :   z� y   ��    /  d   H  "   �  �   �  0   � =   �    � "    �   4 O    G   i `   � Y    I   l {   � �   2    �    � B   � O       j (       �    �    � ,   � �    e   �    � '   	 /   1 .   a '   � v   � 7   /	 t   g	 S   �	 $   0
 z   U
 �   �
 ,   � Y   �    L G   ^ :   �    � w   � {   k
 4   �
 C       ` 7   o J   �    � h    .   m    � '   � S   � �   /        "    8 @   N    � T   � 9   � �   4 z   �    X 9   g �   �    X �   x A    (   Z L   � �   �    �    �    �    � #   � +   � B   $    g    � 	   � #   � ;   � C   � 
   @ H   K B   � 
   �    � -       1 
   N P   \ �   � A   �    �    � �   � L  � ^   � 
   D W  R q  � /     w   L  "   �  >  �  #   &# h   J#    �# 
   �# �  �#    �% J   �% x   �% r   n& V   �&    8' `   W' 1   �'    �' x   ( G   �( ,   �( V   �( *   L) Y   w) �   �) �   V* -   �* �   + 7  �+ �   	- /   �- F  �- �   $/ �    0 '  �0 �   2 )   �2    �2 �   3 .   �3 �  �3 `   �5 <   56    r6    �6 �  �6    f8 �   k8 B  :9    }:   �; <   �< H   �< \   A= %   �= .   �= B   �= [   6>    �>    �> /   �>    �>    ? 3   *? \   ^? )   �?    �? \   �? �   I@ �   6A    �A    �A    �A H   B b   NB X   �B �   
C o   �C    ;D !   PD �   rD    HE �   UE �  �E �   qH O   pI .   �I    �I    �I    �I H    J /   IJ �   yJ   K U   L    sL    �L %   �L    �L _   �L 
   ;M ;   IM    �M 2   �M *   �M    �M    �M T   N    ZN    `N ,   sN    �N    �N ~   �N �   AO Q   P k   ]P H   �P �   Q    �Q    �Q �   �Q �   �R �   dS    &T u   /T G   �T H   �T _   6U g   �U ^   �U    ]V Q   qV    �V    �V    �V    W    W    0W    DW    WW    tW    �W 0   �W -   �W 3   �W    $X 5   DX -   zX +   �X    �X    �X G   Y    MY 	   jY 4   tY -   �Y Y   �Y )   1Z    [Z    rZ    �Z    �Z    �Z n   �Z P   =[ G   �[ �   �[    �\    �\    �\    �\ 3   �\ 	   2]    <]    N] u   c]    �]    �]    ^ 
   ^ 	   '^ :   1^    l^ �   �^    S_    l_    �_ #   �_ "   �_    �_ "   ` %   )` (   O`    x` +   �`    �` X   �` &   #a    Ja    ha <   ya @   �a    �a +   b !   Cb q   eb L   �b O   $c    tc    |c 	   �c    �c    �c    �c �  �c g   }e    �e    �e *   �e    *f +   Af    mf 	   rf q   |f H   �f �   7g    h $   h {   Ah $   �h     �h #   i &   'i 4   Ni &   �i    �i    �i <   �i    j (   2j =   [j U  �j 8   �k R   (l L   {l #   �l �   �l $   �m    �m O   �m    
n %   #n    In >   bn 8   �n +   �n �   o    �o    �o $   �o    �o    p    2p    Gp    fp    xp Y   �p �   �p    �q #   	r �   -r �   s    �s    �s    �s    t    t    5t    Ht    at    vt    �t    �t    �t 
   �t    �t    �t    �t    �t    u &   u �   :u �   v y  w /  {x �  �z �   Q| }  1}    �~   �~    � �   � �   �� �  �� �   g� {  � 5   �� �   ̅ @   |�    �� B   ۆ J   � e   i� _   χ �   /� �   �� �   `� V  �    u� �   �� �   _� b   H� �   ��    U� �   j� d  8� �   �� �   v�    ?� _   N� �   �� �   4� �   &� o   �� �   �    ��    ��    ؖ G   � 7   6�    n� �   }� =   �� e   =�    �� �   �� �   P� H   ݙ K   &� Y   r� c   ̚ Z   0� H   �� l   ԛ f   A� u   �� �   �    �� -   �� *   ڝ ]   � 2   c�    ��    �� F   ��    �     �    � 3   � *   N� A   y�    ��    ؟    �    � 	   � Z   �� �   X� X   ڠ 6   3�    j� #   r� )   ��    ��    ֡    ߡ 
   �    �    ��     �    	�    � 
   !� 
   ,�    7�    F� 
   O�    Z�    s� 	   ��    ��    ��    ��     ��    Т    � �   �    �� U   �� �   ��    ��    Ƥ    Ӥ    �    �    � 
   �� �   �� s   �� ?   -�    m�    � e   ��     � �   � �   �� -   %�    S� �   `� �   � F   �� �   �� s   �� /   � k   N�    ��    ګ K   �� �   F�    ج �   �� �   �� �   � P   ݮ    .�    F�    L�    ^�    g�    {�    ��    �� �   �� �   E� y   ư �   @� �   � ]   � E   D� �  �� ~  � �   �� �   �� 
  � ]  -�    �� �   �� ,  B� �   o� �  -� �  �� �   �� k  �    |� =   �� �   �� u  X� @   �� �   � 	    �    
�    � �   '� ?   �� i   �� -   i� b   �� H   �� b   C�    �� �   �� D   n� ,   �� W   �� �   8�    �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: vec
Language-Team: vec <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono facilmente replicabile la loro collezione, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno). No femo giudizi morali su chi che ciàrca schei par l'accesso in massa a na coleçion de libri ilegali. L'è fora de ogni dubio che la Z-Library la gà avudo sucesso nel espandere l'accesso ala conoscensa e nel trovar più libri. Semu qua solo par far la nostra parte: assicurar la conservazion a longo termine de sta coleçion privata. - Anna e el team (<a %(reddit)s>Reddit</a>) Nel rilascio originale del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>), abbiamo creato un mirror di Z-Library, una grande collezione di libri illegale. Come promemoria, ecco cosa abbiamo scritto in quel post originale del blog: Sta coleçion la risale a metà 2021. Intanto, la Z-Library la gà cresudo a un ritmo impressionante: i gà zontà circa 3.8 milioni de libri novi. Ghe xe qualche duplicato, certo, ma la maggior parte par eser libri novi legittimi, o scan de qualità più alta de libri già inviadi. Questo xe in gran parte dovudo al numero aumentà de moderatori volontari ala Z-Library, e al loro sistema de caricamento in massa con deduplicazion. Volemo congratularse con lori par sti risultati. Semo contenti de annunciar che gavemo recuperà tuti i libri che i xe stai zontà ala Z-Library tra l'ultimo specio e agosto 2022. Gavemo anca tornà indrio e raschià qualche libro che gavemo perso la prima volta. In totale, sta nova coleçion xe circa 24TB, che xe molto più granda de l'ultima (7TB). El nostro specio adesso xe 31TB in totale. Ancora, gavemo deduplicà contro Library Genesis, visto che ghe xe già torrent disponibili par sta coleçion. Par piaser, va al Pirate Library Mirror par veder la nova coleçion (EDIT: spostà su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>). Ghe xe più informazion là su come i file xe strutturadi, e cosa xe cambià da l'ultima volta. No ghe metaremo un link da qua, visto che xe solo un sito de blog che no ospita materiali ilegali. Naturalmente, far seeding xe anca un gran modo par darne na man. Grazie a tuti che xe in seeding coi nostri torrent precedenti. Semu grati par la risposta positiva, e contenti che ghe xe tante persone che se ciàpa de la conservazion de la conoscensa e de la cultura in sto modo inusuale. 3x nuovi libri aggiunti al Pirate Library Mirror (+24TB, 3.8 milioni di libri) Leggi gli articoli di accompagnamento da TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a> - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) articoli de accompagnamento da TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a> No tanto tempo fa, le “biblioteche ombra” le stava morendo. Sci-Hub, el grande archivio illegale de articoli accademici, ga smesso de accettar nuove opere, a causa de cause legali. “Z-Library”, la più grande biblioteca illegale de libri, ga visto i so presunti creatori arrestai per accuse penali de copyright. I xe riuscì incredibilmente a scampare all'arresto, ma la so biblioteca no xe meno minacciata. Alcuni paesi sta già facendo una versione de questo. TorrentFreak <a %(torrentfreak)s>ga riportà</a> che la Cina e il Giappone ga introdotto eccezioni AI nelle loro leggi sul copyright. No xe chiaro per noi come questo interagisce con i trattati internazionali, ma certamente dà copertura alle loro aziende domestiche, il che spiega quello che gavemo visto. Per quanto riguarda l'Archivio de Anna — continueremo il nostro lavoro sotterraneo radicato nella convinzione morale. Tuttavia, el nostro più grande desiderio xe de entrar nella luce, e amplificar il nostro impatto legalmente. Per favore, riformate il copyright. Quando Z-Library ga affrontà la chiusura, mi gavevo già fatto un backup de tutta la so biblioteca e stavo cercando una piattaforma par ospitarla. Questa xe stada la mia motivazione par iniziar l'Archivio de Anna: una continuazione de la missione dietro quei iniziative precedenti. Da allora semo cresciuti fino a diventare la più grande biblioteca ombra al mondo, ospitando più de 140 milioni de testi coperti da copyright in diversi formati — libri, articoli accademici, riviste, giornali e oltre. Mi e el me team semo ideologi. Credemo che preservar e ospitar sti file xe moralmente giusto. Le biblioteche in tutto el mondo le sta vedendo tagli ai finanziamenti, e no podemo fidar el patrimonio de l'umanità alle corporazioni. Poi xe rivà l'AI. Praticamente tutte le grandi aziende che costruisse LLM le ga contattà par addestrarse sui nostri dati. La maggior parte (ma no tutte!) delle aziende statunitensi le ga rivalutà una volta che le ga capì la natura illegale del nostro lavoro. Al contrario, le aziende cinesi le ga abbraccià con entusiasmo la nostra collezione, apparentemente no preoccupandose de la legalità. Questo xe notevole visto el ruolo de la Cina come firmataria de quasi tutti i principali trattati internazionali sul copyright. Gavemo dato accesso ad alta velocità a circa 30 aziende. La maggior parte de queste xe aziende LLM, e alcune xe broker de dati, che rivenderà la nostra collezione. La maggior parte xe cinesi, anche se gavemo lavorà con aziende dagli Stati Uniti, Europa, Russia, Corea del Sud e Giappone. DeepSeek <a %(arxiv)s>ga ammesso</a> che una versione precedente xe stada addestrada su parte de la nostra collezione, anche se xe molto riservati sul loro ultimo modello (probabilmente addestrato anche sui nostri dati). Se l'Occidente vol restar avanti nella corsa dei LLM, e in ultima analisi, dell'AGI, xe necessario riconsiderar la so posizione sul copyright, e presto. Che siate d'accordo o meno con noi sul nostro caso morale, questo sta diventando un caso de economia, e anche de sicurezza nazionale. Tutti i blocchi di potere sta costruendo super-scienziati artificiali, super-hacker e super-militari. La libertà de informazione sta diventando una questione de sopravvivenza par sti paesi — anche una questione de sicurezza nazionale. El nostro team xe da tutto el mondo, e no gavemo un allineamento particolare. Ma incoraggeremo i paesi con leggi sul copyright forti a usar sta minaccia esistenziale par riformarle. Allora, cosa fare? La nostra prima raccomandazione xe semplice: accorciar el termine del copyright. Negli Stati Uniti, el copyright xe concesso per 70 anni dopo la morte dell'autore. Questo xe assurdo. Podemo allinear questo con i brevetti, che xe concessi per 20 anni dopo il deposito. Questo dovria esser più che sufficiente par gli autori de libri, articoli, musica, arte e altre opere creative, par esser compensai completamente per i so sforzi (inclusi progetti a lungo termine come le adattazioni cinematografiche). Poi, al minimo, i responsabili delle politiche dovria includer eccezioni par la conservazione e la diffusione di massa dei testi. Se la perdita de entrate dai clienti individuali xe la principale preoccupazione, la distribuzione a livello personale podaria restar proibita. A sua volta, chi xe in grado de gestir vasti archivi — aziende che addestrano LLM, insieme a biblioteche e altri archivi — saria coperto da ste eccezioni. La riforma del copyright xe necessaria par la sicurezza nazionale TL;DR: I LLM cinesi (incluso DeepSeek) i xe stai addestrai sul me archivio illegale de libri e articoli — el più grande al mondo. L'Occidente ga bisogno de riformar la legge sul copyright come questione de sicurezza nazionale. Varda el <a %(all_isbns)s>post orginal del blog</a> par più informazion. Gavemo lancià un sfida par migliorar sto progetto. Gavemo deciso de premiar el primo posto con $6,000, el secondo con $3,000, e el terzo con $1,000. Dò l'enorme risposta e le incredibili proposte, gavemo deciso de ingrandir un poco el montepremi, e premiar quattro terzi posti con $500 ciascuno. I vincitori i xe qua soto, ma no te scordar de vardar tute le proposte <a %(annas_archive)s>qua</a>, o de scaricar el nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinà</a>. Primo posto $6,000: phiresky Sta <a %(phiresky_github)s>proposta</a> (<a %(annas_archive_note_2951)s>commento su Gitlab</a>) xe semplicemente tuto quel che volevimo, e anca de più! Gavemo particolarmente apprezzà le opzioni de visualizzazione incredibilmente flessibili (anca con shader personalizà), ma con una lista completa de preset. Gavemo anca apprezzà quanto tuto xe veloce e fluido, l'implementazion semplice (che no gà gnanca un backend), la minimappa ingegnosa, e l'ampia spiegazion nel loro <a %(phiresky_github)s>post del blog</a>. Un lavoro incredibile, e un vincitore ben merità! - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) I nostri cuori xe pieni de gratitudine. Idee notevoli Grattacieli par rarità Tanti cursori par confrontar datasets, come se fussi un DJ. Barra de scala co numero de libri. Etichette carine. Schema de colori de default figo e mappa de calore. Vista unica de la mappa e filtri Annotazioni, e anca statìsteghe in tempo reale Statìsteghe in tempo reale Alcune altre idee e implementazioni che gavemo particolarmente apprezzà: Podemo continuar par un bel po', ma fermemose qua. Assicurateve de vardar tute le sottomission <a %(annas_archive)s>qua</a>, o scaricar el nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinà</a>. Tante sottomission, e ognuna porta na prospettiva unica, sia in UI che in implementazion. Incorporaremo almanco la sottomission che ga ciapà el primo posto nel nostro sito principal, e forse anca altre. Gavemo anca inizià a pensar a come organizzar el processo de identificazion, conferma, e poi archiviazion dei libri più rari. Altro a vegner su sto fronte. Grazie a tuti che ga partecipà. Xe straordinario che tante persone ghe tenga. Facile attivazion dei datasets par confronti rapidi. Tuti i ISBN CADAL SSNOs Fuga de dati CERLALC DuXiu SSIDs Indice de eBook de EBSCOhost Google Books Goodreads Archivio Internet ISBNdb Registro Globale de Editori ISBN Libby File in l'Archivio de Anna Nexus/STC OCLC/Worldcat OpenLibrary Biblioteca Statale Russa Biblioteca Imperiale de Trantor Secondo posto $3,000: hypha “Anche se i quadrati e i rettangoli perfetti xe matematicamente piacevoli, no i fornisce una superior località in un contesto de mappatura. Credo che l'asimmetria inerente a sti Hilbert o Morton classici no sia un difetto ma una caratteristica. Proprio come el contorno a forma de stivale dell'Italia la rende immediatamente riconoscibile su una mappa, le "stranezze" uniche de ste curve le può servire come punti de riferimento cognitivi. Sta distintività la può migliorare la memoria spaziale e aiutar gli utenti a orientarse, potenzialmente rendendo più facile localizzare regioni specifiche o notare schemi.” Un'altra <a %(annas_archive_note_2913)s>proposta</a> incredibile. No xe flessibile come el primo posto, ma in realtà preferivimo la sua visualizzazione a livello macro rispetto al primo posto (curva che riempie lo spazio, confini, etichettatura, evidenziazione, panoramica e zoom). Un <a %(annas_archive_note_2971)s>commento</a> de Joe Davis che ne gà colpì: E ancora tante opzioni par visualizzare e renderizzare, oltre a un'interfaccia utente incredibilmente fluida e intuitiva. Un solido secondo posto! - Anna e el team (<a %(reddit)s>Reddit</a>) Qualche mese fa gavemo annunciato un <a %(all_isbns)s>premio da $10,000</a> par crear la miglior visualizzazione possibile dei nostri dati che mostra lo spazio ISBN. Gavemo enfatizzato il mostrar quali file gavemo/ga no ancora archivià, e successivamente un dataset che descrive quante biblioteche detiene ISBN (una misura de rarità). Semo stai sopraffatti dalla risposta. Xe stada tanta creatività. Un grande grazie a tutti quelli che ga partecipà: la vostra energia e entusiasmo xe contagiosi! In definitiva, gavemo volesto risponder a ste domande: <strong>quai libri esisti nel mondo, quanti gavemo za archivià, e sui quali libri dovemo focalizarse dopo?</strong> Xe fantastico veder che tante persone se interessa de ste domande. Gavemo inizià con una visualizzazione basica noi stessi. In manco de 300kb, sta imagine rappresenta in modo succinto la più grande “lista de libri” completamente aperta mai assemblà nella storia de l’umanità: Terzo posto $500 #1: maxlion In sta <a %(annas_archive_note_2940)s>proposta</a> gavemo veramente apprezzà i diversi tipi de viste, in particolare le viste de confronto e de editore. Terzo posto $500 #2: abetusk Anche se no gà l'interfaccia utente più raffinà, sta <a %(annas_archive_note_2917)s>proposta</a> soddisfa molti dei criteri. Gavemo particolarmente apprezzà la sua funzione de confronto. Terzo posto $500 #3: conundrumer0 Come el primo posto, sta <a %(annas_archive_note_2975)s>proposta</a> ne gà impressionà con la sua flessibilità. In definitiva, questo xe quel che rende un strumento de visualizzazione eccezionale: massima flessibilità par gli utenti esperti, mantenendo le cose semplici par gli utenti medi. Terzo posto $500 #4: charelf L'ultima <a %(annas_archive_note_2947)s>proposta</a> a ricever un premio xe abbastanza basica, ma gà delle caratteristiche uniche che gavemo veramente apprezzà. Gavemo apprezzà come mostra quante datasets copre un particolare ISBN come misura de popolarità/affidabilità. Gavemo anca veramente apprezzà la semplicità ma efficacia de usar un cursore de opacità par i confronti. Vincitori del premio da $10,000 per la visualizzazione dell'ISBN TL;DR: Gavemo ricevuto delle incredibili proposte per il premio da $10,000 per la visualizzazione dell'ISBN. Sfondo Come pol può l'Archivio de Anna raggiunger el so obiettivo de far na copia de backup de tuto el sapere de l'umanità, senza saver quali libri xe ancora là fora? Gavemo bisogno de na lista de cose da far. Un modo de mapar questo xe tramite i numeri ISBN, che dai ani '70 i xe stai assegnai a ogni libro pubblicà (in la maggior parte dei paesi). No ghe xe na autorità centrale che sa tute le assegnazioni ISBN. Invece, xe un sistema distribuito, dove i paesi riceve range de numeri, che poi i assegna range più piccoli ai editori principali, che podaria ulteriormente suddivider i range ai editori minori. Finalmente i numeri individuali i xe assegnai ai libri. Gavemo inizià a mapar i ISBN <a %(blog)s>do ani fa</a> col nostro scrape de ISBNdb. Da allora, gavemo scrapà molte altre fonti de metadata, come <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e altro. Na lista completa la se trova su le pagine “Datasets” e “Torrents” de l'Archivio de Anna. Gavemo adesso de gran lunga la più grande collezion completamente aperta e facilmente scaricabile de metadata de libri (e quindi ISBN) al mondo. Gavemo <a %(blog)s>scritto ampiamente</a> del perché ne importa la conservazion, e del perché semo attualmente in una finestra critica. Dovemo adesso identificar i libri rari, poco focalizai, e unicamente a rischio e conservarli. Aver boni metadata su tuti i libri del mondo ne aiuta in questo. Taglia de $10,000 Sarà data forte considerazion all'usabilità e a quanto ben el se vede. Mostra el metadata reale per i ISBN individuali quando te zoomi, come el titolo e l'autore. Miglior curva de riempimento dello spazio. Per esempio, un zig-zag, che va da 0 a 4 sulla prima riga e poi indrio (in reverso) da 5 a 9 sulla seconda riga — applicà ricorsivamente. Schemi de colori diversi o personalizzabili. Viste speciali per confrontare i datasets. Modi per risolvere problemi, come altri metadata che non concordano bene (ad esempio titoli molto diversi). Annotare immagini con commenti su ISBN o intervalli. Qualsiasi euristica per identificare libri rari o a rischio. Qualunque idea creativa che ti venga in mente! Codice Il codice per generare queste immagini, così come altri esempi, si trova in <a %(annas_archive)s>questa directory</a>. Abbiamo ideato un formato dati compatto, con il quale tutte le informazioni ISBN richieste sono circa 75MB (compresse). La descrizione del formato dati e il codice per generarlo si trovano <a %(annas_archive_l1244_1319)s>qui</a>. Per la ricompensa non sei obbligato a usare questo, ma è probabilmente il formato più conveniente per iniziare. Puoi trasformare i nostri metadata come vuoi (anche se tutto il tuo codice deve essere open source). Non vediamo l'ora di vedere cosa inventerai. Buona fortuna! Forka sto repo, e modifica sto post del blog HTML (no altri backend oltre al nostro backend Flask xe permessi). Fai che l'immagine qua sora sia zoomabile in modo fluido, cussì te podi zoomar fino ai ISBN individuali. Cliccar sui ISBN te dovrebbe portar a una pagina de metadata o a una ricerca su l'Archivio de Anna. Te devi ancora poder cambiar tra tute le diverse datasets. I range dei paesi e i range degli editori dovaria esser evidenzià quando te ghe passi sopra col mouse. Te podi usar per esempio <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> per le info sui paesi, e el nostro scrape “isbngrp” per gli editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). El deve funzionare ben su desktop e mobile. Ghe xe tanto da esplorar qua, quindi stemo annunciando una taglia per migliorar la visualizazion qua sora. A differenza de la maggior parte delle nostre taglie, questa xe a tempo limità. Te devi <a %(annas_archive)s>inviar</a> el to codice open source entro el 2025-01-31 (23:59 UTC). La mejo submission la riceverà $6,000, el secondo posto $3,000, e el terzo posto $1,000. Tute le taglie le sarà assegnà usando Monero (XMR). Qua sotto ghe xe i criteri minimi. Se nessuna submission rispetta i criteri, podemo ancora assegnar qualcossa de taglie, ma questo sarà a nostra discrezion. Per punti bonus (questi xe solo idee — lascia che la to creatività corra libera): Puoi completamente deviare dai criteri minimi e fare una visualizzazione completamente diversa. Se è davvero spettacolare, allora si qualifica per la ricompensa, ma a nostra discrezione. Fai le tue proposte postando un commento a <a %(annas_archive)s>questo problema</a> con un link al tuo repo forkato, richiesta di merge o diff. - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Sta imagine xe 1000×800 pixel. Ogni pixel rapresenta 2.500 ISBN. Se gavemo un file par un ISBN, rendemo quel pixel più verde. Se savemo che un ISBN xe stà emesso, ma no gavemo un file corispondente, lo rendemo più rosso. In meno de 300kb, sta imagine rapresenta in modo conciso la più grande “lista de libri” completamente aperta mai assemblà nella storia de l'umanità (qualche centinaio de GB compressi in toto). Mostra anca: ghe xe ancora tanto lavoro da far par far na copia de sicurezza dei libri (gavemo solo 16%). Visualizzar Tutti i ISBN — $10,000 de ricompensa entro el 2025-01-31 Sta imagine rapresenta la più grande “lista de libri” completamente aperta mai assemblà nella storia de l'umanità. Visualizazion Oltre all'immagine de visione generale, podemo anca guardar i datasets individuali che gavemo acquisì. Usa el menu a tendina e i bottoni per cambiar tra de lori. Ghe xe tanti schemi interessanti da veder in queste immagini. Perché ghe xe una certa regolarità de linee e blocchi, che sembra succeder a scale diverse? Cosa xe le aree vuote? Perché certi datasets xe cussì raggruppai? Lasseremo queste domande come un esercizio per el lettore. - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusione Con sto standard, podemo far rilasci più incrementali e aggiunger più facilmente nuove fonti de dati. Gavemo già qualche rilascio interessante in programma! Speremo anca che diventi più facile par altre biblioteche ombra de rispecchiar le nostre collezioni. Dopo tuto, el nostro obiettivo xe de preservar la conoscenza e la cultura umana par sempre, cussì più ridondanza ghe xe, mejo xe. Esempio Diamo un'occhiata al nostro recente rilascio de Z-Library come esempio. El consiste de do collezioni: “<span style="background: #fffaa3">zlib3_records</span>” e “<span style="background: #ffd6fe">zlib3_files</span>”. Questo ne permette de raspare e rilasciare separatamente i record de metadata dai file dei libri veri e propri. Cussì, gavemo rilascià do torrents con i file de metadata: Gavemo anca rilascià un mucchio de torrents con le cartèle de dati binari, ma solo par la collezion “<span style="background: #ffd6fe">zlib3_files</span>”, 62 in totale: Eseguendo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemo veder cossa ghe xe dentro: In sto caso, xe el metadata de un libro come riportà da Z-Library. A livello superiore gavemo solo “aacid” e “metadata”, ma gnente “data_folder”, visto che no ghe xe dati binari corrispondenti. L'AACID contiene “22430000” come ID primario, che podemo veder xe tratto da “zlibrary_id”. Podemo aspettarse che altri AAC in sta collezion gà la stessa struttura. Adesso eseguimo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Questo xe un metadata AAC molto più piccolo, anca se la parte principale de sto AAC xe situà altrove in un file binario! Dopo tuto, stavolta gavemo un “data_folder”, cussì podemo aspettarse che i dati binari corrispondenti se trova a <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. El “metadata” contiene el “zlibrary_id”, cussì podemo facilmente associarlo col AAC corrispondente nella collezion “zlib_records”. Podarìmo associarlo in diversi modi, par esemio tramite AACID — el standard no prescrive questo. Nota che no xe necessario che el campo “metadata” sia in JSON. Podarìa esser una stringa che contiene XML o qualsiasi altro formato de dati. Podarìa anca memorizzar le informazioni de metadata nel blob binario associato, par esemio se xe tanti dati. File e metadata eterogenei, nel formato più vicino possibile a quello originale. I dati binari può esser serviti direttamente da server web come Nginx. Identificatori eterogenei nelle librerie sorgente, o anche mancanza de identificatori. Rilasci separati de metadata rispetto ai dati dei file, o rilasci solo de metadata (ad esempio el nostro rilascio ISBNdb). Distribuzion tramite torrent, anche se con la possibilità de altri metodi de distribuzion (ad esempio IPFS). Registri immutabili, visto che dovemo assumere che i nostri torrent vivarà par sempre. Rilasci incrementali / rilasci appendibili. Leggibile e scrivibile da macchine, in modo conveniente e veloce, specialmente par la nostra pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Ispezion umana relativamente facile, anche se questo xe secondario rispetto alla leggibilità da parte delle macchine. Facile da seminare le nostre collezioni con un seedbox standard affittato. Obiettivi del design No ghe ne frega che i file sia facili da navigar manualmente su disco, o ricercabili senza pre-elaborazione. No ghe ne frega de esser direttamente compatibili con software de libreria esistente. Anche se dovria esser facile par chiunque seminare la nostra collezion usando i torrent, no ne aspettemo che i file sia utilizzabili senza conoscenze tecniche significative e impegno. El nostro caso d'uso principale xe la distribuzion de file e metadata associati da diverse collezioni esistenti. Le nostre considerazioni più importanti xe: Alcuni non-obiettivi: Visto che l'Archivio de Anna xe open source, volémo usar direttamente el nostro formato. Quando rinfreschemo el nostro indice de ricerca, accedemo solo ai percorsi pubblicamente disponibili, in modo che chiunque forki la nostra libreria possa avviarsi rapidamente. <strong>AAC.</strong> AAC (Contenitore de l'Archivio de Anna) xe un singolo elemento composto da <strong>metadata</strong>, e opzionalmente da <strong>dati binari</strong>, che i xe entrambi immutabili. El ga un identificatore unico globale, chiamà <strong>AACID</strong>. <strong>AACID.</strong> El formato de l'AACID xe questo: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Per esempio, un AACID reale che gavemo rilascià xe <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Intervallo AACID.</strong> Dato che i AACID contiene timestamp che aumenta monotonamente, podemo usarlo per indicare intervalli dentro una collezion particolare. Usimo questo formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, dove i timestamp xe inclusivi. Questo xe consistente con la notazion ISO 8601. Gli intervalli xe continui, e podi sovrapporsi, ma in caso de sovrapposizion deve contener record identici a quelli rilascià in precedenza in quella collezion (dato che i AAC xe immutabili). I record mancanti no xe permessi. <code>{collection}</code>: el nome de la collezion, che podi contener lettere ASCII, numeri e underscore (ma no doppi underscore). <code>{collection-specific ID}</code>: un identificatore specifico de la collezion, se applicabile, ad esempio l'ID de Z-Library. Podi esser omesso o troncato. Deve esser omesso o troncato se l'AACID altrimenti superassi i 150 caratteri. <code>{ISO 8601 timestamp}</code>: na versione corta de l'ISO 8601, sempre in UTC, ad esempio <code>20220723T194746Z</code>. Questo numero deve aumentare monotonamente per ogni rilascio, anche se i so esatti semantici podi differir per collezion. Suggerimo de usar el tempo de scraping o de generazion de l'ID. <code>{shortuuid}</code>: un UUID ma compresso in ASCII, ad esempio usando base57. Attualmente usimo la libreria Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Cartella de dati binari.</strong> Una cartella con i dati binari de un intervallo de AAC, per una collezion particolare. Questi ga le seguenti proprietà: La directory deve contener file de dati per tutti i AAC dentro l'intervallo specificato. Ogni file de dati deve aver el so AACID come nome del file (senza estensioni). El nome della directory deve esser un intervallo AACID, prefissato con <code style="color: green">annas_archive_data__</code>, e senza suffisso. Per esempio, uno dei nostri rilasci reali ga una directory chiamà<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Xe racomandà de far sti cartèle un poco gestibili in dimension, par esemio no più grandi de 100GB-1TB ciascuna, anca se sta racomandazion podaria cambiar col tempo. <strong>Collezion.</strong> Ogni AAC appartien a na collezion, che per definizion xe na lista de AAC che i xe semanticamente consistenti. Questo vol dir che se te fassi un cambiamento significativo al formato dei metadata, te devi crear na nova collezion. El standard <strong>File de metadata.</strong> Un file de metadata contiene i metadata de un intervallo de AAC, per una collezion particolare. Questi ga le seguenti proprietà: <code>data_folder</code> xe opzionale, e xe el nome de la cartella de dati binari che contiene i dati binari corrispondenti. El nome del file de dati binari corrispondente dentro quella cartella xe l'AACID del record. Ogni oggetto JSON deve contener i seguenti campi al livello superiore: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opzionale). No xe permessi altri campi. El nome del file deve esser un intervallo AACID, prefissato con <code style="color: red">annas_archive_meta__</code> e seguito da <code>.jsonl.zstd</code>. Per esempio, uno dei nostri rilasci xe chiamà<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Come indicato dall'estension del file, el tipo de file xe <a %(jsonlines)s>JSON Lines</a> compresso con <a %(zstd)s>Zstandard</a>. <code>metadata</code> xe metadata arbitrari, secondo i semantici de la collezion. Deve esser semanticamente consistenti dentro la collezion. El prefisso <code style="color: red">annas_archive_meta__</code> podi esser adattà al nome de la vostra istituzion, ad esempio <code style="color: red">my_institute_meta__</code>. <strong>Collezioni de “records” e “files”.</strong> Per convenzion, xe spesso conveniente rilasciar “records” e “files” come collezioni diverse, cussì che i possa esser rilascià a orari diversi, ad esempio basà sui tassi de scraping. Un “record” xe na collezion solo de metadata, che contiene informazioni come titoli de libri, autori, ISBN, ecc., mentre i “files” xe le collezioni che contiene i file veri e propri (pdf, epub). In definitiva, gavemo deciso de adotar un standard relativamente semplice. El xe abbastanza flessibile, no normativo, e un lavoro in corso. <strong>Torrents.</strong> I file de metadata e le cartèle de dati binari podarìa vegner impacà in torrents, con un torrent par ogni file de metadata, o un torrent par ogni cartèla de dati binari. I torrents gà da aver el nome originale del file/directory più el suffisso <code>.torrent</code> come nome del file. <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> è diventato di gran lunga la più grande biblioteca ombra del mondo, e l'unica biblioteca ombra della sua scala che è completamente open-source e open-data. Di seguito è riportata una tabella dalla nostra pagina Datasets (leggermente modificata): Abbiamo realizzato questo in tre modi: Rispecchiando le biblioteche ombra open-data esistenti (come Sci-Hub e Library Genesis). Aiutando le biblioteche ombra che vogliono essere più aperte, ma non avevano il tempo o le risorse per farlo (come la collezione di fumetti di Libgen). Raccogliendo dati da biblioteche che non desiderano condividere in massa (come Z-Library). Par (2) e (3) gestimo adesso na collezion considerevole de torrent da soli (centinaia de TB). Fin'ora gavemo trattà ste collezioni come casi unici, cioè infrastruttura e organizzazion dei dati su misura par ogni collezion. Questo aggiunge un sovraccarico significativo a ogni rilascio, e rende particolarmente difficile far rilasci più incrementali. Par questo gavemo deciso de standardizzar i nostri rilasci. Questo xe un post tecnico del blog in cui stemo introduzendo el nostro standard: <strong>Contenitori de l'Archivio de Anna</strong>. Contenitori de l'Archivio de Anna (AAC): standardizzare le pubblicazioni dalla più grande biblioteca ombra del mondo L'Archivio de Anna è diventato la più grande biblioteca ombra del mondo, richiedendo di standardizzare le nostre pubblicazioni. 300GB+ de copertine de libri rilasciàe Finalmente, semo contenti de annunciar un piccolo rilascio. In collaborazione coi ragazzi che gestisse el fork Libgen.rs, stemo condividendo tute le so copertine de libri tramite torrent e IPFS. Questo distribuirà el carico de visualizzar le copertine tra più macchine e le conserverà mejo. In molti (ma no in tuti) casi, le copertine dei libri xe incluse nei file stessi, cusì xe un tipo de "dati derivati". Ma averle in IPFS xe ancora molto utile par l'operazione giornaliera sia de l'Archivio de Anna che dei vari fork de Library Genesis. Come al solito, te podi trovar questo rilascio al Pirate Library Mirror (EDIT: spostà a <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>). No linkeremo qua, ma te lo podi trovar facilmente. Speremo de poder rallentar un poco el nostro ritmo, adesso che gavemo una decente alternativa a Z-Library. Questo carico de lavoro no xe particolarmente sostenibile. Se te sì interessà a dar na man con la programmazione, le operazioni sui server, o el lavoro de conservazione, contatene. Ghe xe ancora un sacco de <a %(annas_archive)s>lavoro da far</a>. Grazie par el tuo interesse e supporto. Passa a ElasticSearch Qualche query ci metteva un sacco de tempo, al punto che se ciapava tuti i collegamenti aperti. De default MySQL gà una lunghezza minima de parola, o l'indice podi diventare veramente grande. La gente riferiva de no poder cercar "Ben Hur". La ricerca era solo un po' veloce quando era completamente caricà in memoria, il che richiedeva de ciapar una macchina più costosa par farla girar, più qualche comando par precaricar l'indice all'avvio. No saremmo stati in grado de estenderla facilmente par costruir nuove funsionalità, come una mejo <a %(wikipedia_cjk_characters)s>tokenizzazione par le lingue senza spazi</a>, filtraggio/faccettatura, ordinamento, suggerimenti "volevi dire", autocompletamento, e cusì via. Uno dei nostri <a %(annas_archive)s>ticket</a> era un insieme de problemi col nostro sistema de ricerca. Gavemo usà la ricerca full-text de MySQL, visto che gavemo tuti i nostri dati in MySQL comunque. Ma gà i so limiti: Dopo aver parlà con un mucchio de esperti, gavemo deciso par ElasticSearch. No xe stà perfetto (i so suggerimenti "volevi dire" e le funsionalità de autocompletamento de default no xe granché), ma nel complesso xe stà molto mejo de MySQL par la ricerca. No semo ancora <a %(youtube)s>troppo entusiasti</a> de usarlo par dati critici (anche se gà fatto un sacco de <a %(elastic_co)s>progressi</a>), ma nel complesso semo abbastanza contenti del cambio. Par adesso, gavemo implementà una ricerca molto più veloce, un mejo supporto linguistico, un mejo ordinamento per rilevanza, diverse opzioni de ordinamento, e filtraggio su lingua/tipo de libro/tipo de file. Se te sì curioso de come funsiona, <a %(annas_archive_l140)s>dai</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>ocio</a>. Xe abbastanza accessibile, anche se ghe serviria qualche commento in più… L'Archivio de Anna xe completamente open source Credemo che l'informazion dovaria esser libera, e el nostro codice no fa eccezion. Gavemo rilascià tuto el nostro codice sulla nostra istanza Gitlab ospitata privatamente: <a %(annas_archive)s>Software de Anna</a>. Usèmo anca el tracker de problemi par organizzar el nostro lavoro. Se volete partecipar al nostro sviluppo, questo xe un gran bel posto par iniziare. Par darve un'idea delle cose su cui stemo lavorando, vedè el nostro lavoro recente sui miglioramenti delle prestazioni lato client. Visto che no gavemo ancora implementà la paginazion, spesso restituivimo pagine de ricerca molto lunghe, con 100-200 risultati. No volevimo tagliar i risultati de ricerca troppo presto, ma questo voleva dir che podarìa rallentar certi dispositivi. Par questo, gavemo implementà un piccolo trucco: gavemo avvolto la maggior parte dei risultati de ricerca nei commenti HTML (<code><!-- --></code>), e poi gavemo scritto un piccolo Javascript che rilevava quando un risultato doveva diventare visibile, al che gavemo scartato el commento: DOM "virtualization" implementà in 23 righe, no bisogno de librerie sofisticàe! Questo xe el tipo de codice pragmatico e veloce che te finissi par scrivar quando te gà poco tempo e problemi reali da risolver. Xe stà riferìo che la nostra ricerca adesso funziona ben anca sui dispositivi lenti! Un altro grande sforzo xe stà automatizar la costruzion del database. Quando gavemo lancià, gavemo semplicemente mescolà diverse fonti insieme. Adesso volémo tenerle aggiornàe, cusì gavemo scrito un mucchio de script par scaricar novi metadata dai do fork de Library Genesis e integrarli. El scopo xe no solo farlo utile par el nostro archivio, ma anca farlo facile par chiunque vogia sperimentar coi metadata delle biblioteche ombra. El scopo saria un notebook Jupyter che gà tute le sorte de metadata interessanti disponibili, cusì podemo far più ricerca come capir che <a %(blog)s>percentuale de ISBN xe conservà per sempre</a>. Finalmente, gavemo rinnovà el nostro sistema de donazioni. Adesso te podi usar una carta de credito par depositar direttamente i schei nei nostri portafogli crypto, senza veramente dover saper niente de criptovalute. Continueremo a monitorar quanto ben funsiona in pratica, ma xe un gran passo avanti. Con Z-Library che xe andà giù e i suoi (presunti) fondatori arrestati, gavemo lavorà senza sosta par fornir un'alternativa valida con l'Archivio de Anna (no lo linkeremo qua, ma podè cercarlo su Google). Ecco alcune delle cose che gavemo raggiunto recentemente. Aggiornamento de Anna: archivio completamente open source, ElasticSearch, più de 300GB de copertine de libri Gavemo lavorà senza sosta par fornir un'alternativa valida con l'Archivio de Anna. Ecco alcune delle cose che gavemo raggiunto recentemente. Analisi I duplicati semantici (diverse scansioni del stesso libro) teoreticamente se pol filtrar fora, ma xe complicà. Quando gavemo guardà manualmente tra i fumetti gavemo trovà troppi falsi positivi. Ghe xe alcuni duplicati puri per MD5, che xe relativamente spreco, ma filtrar fora quei ne darebbe solo circa 1% in de risparmio. A sta scala xe ancora circa 1TB, ma anca, a sta scala 1TB no importa realmente. Preferimo no rischiar de distrugger dati accidentalmente in sto processo. Gavemo trovà un mucchio de dati non-libro, come film basai su fumetti. Anca questo pare spreco, visto che questi xe già ampiamente disponibili per altri mezzi. Tuttavia, gavemo capì che no podemo semplicemente filtrar fora i file de film, visto che ghe xe anca <em>fumetti interattivi</em> che xe stai rilascià sul computer, che qualcuno ga registrà e salvà come film. In fin dei conti, qualsiasi cosa che potessimo eliminare dalla collezione risparmierebbe solo pochi percentuali. Poi ci siamo ricordati che siamo accumulatori di dati, e le persone che faranno il mirroring di questo sono anche accumulatori di dati, e quindi, “COSA VUOI DIRE, ELIMINARE?!” :) Quando te te trovi 95TB scaricai nel tuo cluster de storage, te provi a capir cosa ghe xe dentro… Gavemo fato un’analisi par veder se podemo ridur la dimension un poco, come par esempio eliminando i duplicati. Ecco alcune delle nostre scoperte: Pertanto, vi presentiamo la collezione completa e non modificata. È un sacco di dati, ma speriamo che abbastanza persone si prendano cura di condividerli comunque. Colaborazion Dada la so dimension, sta collezion xe da tempo su la nostra lista dei desideri, cusì dopo el nostro successo col backup de Z-Library, gavemo puntà a sta collezion. Al inizio la gavemo raschià direttamente, che xe stado un bel sfido, visto che el so server no gera in le migliori condizion. Gavemo recuperà circa 15TB in sto modo, ma xe stado lento. Fortunatamente, gavemo riusì a metterse in contatto col operatore de la biblioteca, che ga accettà de mandarne tuti i dati direttamente, che xe stado molto più veloce. Xe ancora volù più de mezo ano par trasferir e processar tuti i dati, e gavemo quasi perso tuto per colpa de un corrompimento del disco, che saria volù dir ricominciar da capo. Sta esperienza ne ga fatto creder che xe importante far girar sti dati il più presto possibile, cusì che i possa esser replicà in largo e in lungo. Semoghe solo a uno o do incidenti sfortunai de perder sta collezion par sempre! La collezion Muoversi veloce vol dir che la collezion xe un poco disorganizzata… Diamo un’occhiata. Imagina de aver un filesystem (che in realtà stemo dividendo tra i torrent): El primo directory, <code>/repository</code>, xe la parte più strutturà de questo. Sto directory contiene i cosiddetti “mille dir”: directory ciascuna con mile file, che xe numerai incrementalmente nel database. El directory <code>0</code> contiene file con comic_id da 0 a 999, e cusì via. Xe el stesso schema che Library Genesis ga usà par le so collezioni de narrativa e saggistica. L’idea xe che ogni “mille dir” vien automaticamente trasformà in un torrent appena xe pieno. Tuttavia, l’operatore de Libgen.li no ga mai fatto torrent par sta collezion, e cusì i mille dir probabilmente xe diventai scomodi, e ga lascià spazio ai “dir non ordinai”. Questi xe <code>/comics0</code> fino a <code>/comics4</code>. Tuti i ga strutture de directory uniche, che probabilmente ga senso par raccoglier i file, ma no ga tanto senso par noialtri adesso. Fortunatamente, el metadata fa ancora riferimento diretto a tuti sti file, cusì la so organizazion su disco no importa realmente! El metadata xe disponibile in forma de un database MySQL. Questo se pol scaricar direttamente dal sito de Libgen.li, ma lo renderemo disponibile anca in un torrent, insieme alla nostra tabella con tuti i hash MD5. <q>La dottoressa Barbara Gordon prova a perderse nel mondo ordinario de la biblioteca…</q> Fork de Libgen Prima, un poco de contesto. Podarìa che conossé Library Genesis par la so collezion epica de libri. Meno persone sa che i volontari de Library Genesis ga creato altri progetti, come una vasta collezion de riviste e documenti standard, un backup completo de Sci-Hub (in colaborazion con la fondatrice de Sci-Hub, Alexandra Elbakyan), e in effetti, una collezion massiccia de fumetti. A un certo punto, diversi operatori dei mirror de Library Genesis ga preso strade diverse, che ga portà alla situazion attuale de aver diversi “fork”, tuti che porta ancora el nome de Library Genesis. El fork Libgen.li ga in modo unico sta collezion de fumetti, oltre a una vasta collezion de riviste (che stemo lavorando anca su quela). Raccolta fondi Stiamo rilasciando questi dati in alcuni grandi blocchi. Il primo torrent è di <code>/comics0</code>, che abbiamo messo in un enorme file .tar da 12TB. È meglio per il tuo disco rigido e il software torrent rispetto a una miriade di file più piccoli. Come parte di questo rilascio, stiamo facendo una raccolta fondi. Stiamo cercando di raccogliere $20,000 per coprire i costi operativi e di contrattazione per questa collezione, oltre a consentire progetti in corso e futuri. Abbiamo alcuni <em>enormi</em> in lavorazione. <em>Chi sto supportando con la mia donazione?</em> In breve: stiamo facendo il backup di tutta la conoscenza e la cultura dell'umanità, rendendola facilmente accessibile. Tutto il nostro codice e i dati sono open source, siamo un progetto gestito completamente da volontari, e abbiamo salvato finora 125TB di libri (oltre ai torrent esistenti di Libgen e Scihub). In definitiva, stiamo costruendo un volano che consente e incentiva le persone a trovare, scansionare e fare il backup di tutti i libri del mondo. Scriveremo del nostro piano maestro in un post futuro. :) Se doni per un abbonamento di 12 mesi “Amazing Archivist” ($780), puoi <strong>“adottare un torrent”</strong>, il che significa che metteremo il tuo nome utente o messaggio nel nome di uno dei torrent! Puoi donare andando su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> e cliccando sul pulsante “Dona”. Stiamo anche cercando più volontari: ingegneri software, ricercatori di sicurezza, esperti di commercio anonimo e traduttori. Puoi anche supportarci fornendo servizi di hosting. E naturalmente, per favore condividi i nostri torrent! Grazie a tutti coloro che ci hanno già supportato così generosamente! State davvero facendo la differenza. Ecco i torrent rilasciati finora (stiamo ancora elaborando il resto): Tutti i torrent possono essere trovati su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> sotto “Datasets” (non colleghiamo direttamente lì, quindi i link a questo blog non vengono rimossi da Reddit, Twitter, ecc.). Da lì, segui il link al sito Tor. <a %(news_ycombinator)s>Discùti su Hacker News</a> Cosa c'è dopo? Un mucchio di torrent sono ottimi per la conservazione a lungo termine, ma non tanto per l'accesso quotidiano. Lavoreremo con partner di hosting per mettere tutti questi dati sul web (dato che L'Archivio de Anna non ospita nulla direttamente). Naturalmente, potrai trovare questi link di download su L'Archivio de Anna. Invitiamo anche tutti a fare qualcosa con questi dati! Aiutaci ad analizzarli meglio, deduplicarli, metterli su IPFS, remixarli, addestrare i tuoi modelli AI con essi, e così via. Sono tutti tuoi, e non vediamo l'ora di vedere cosa farai con essi. Infine, come detto prima, abbiamo ancora alcuni rilasci enormi in arrivo (se <em>qualcuno</em> potesse <em>accidentalmente</em> inviarci un dump di un <em>certo</em> database ACS4, sapete dove trovarci...), oltre a costruire il volano per fare il backup di tutti i libri del mondo. Quindi restate sintonizzati, abbiamo appena iniziato. - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) La più grande biblioteca ombra de fumetti xe probabilmente quella de un particolare fork de Library Genesis: Libgen.li. L'unico amministratore che gestisse quel sito xe riuscìo a collezionar una collezion de fumetti incredibile de più de 2 milioni de file, par un totale de più de 95TB. Tuttavia, a differenza de altre collezioni de Library Genesis, questa no era disponibile in blocco tramite torrent. Te podi accedere a sti fumetti solo individualmente tramite el so server personale lento — un unico punto de fallimento. Fin a oggi! In sto post ve contarémoghe de più su sta collezion, e su la nostra raccolta fondi par sostegnir più de sto lavoro. L'Archivio de Anna gà salvà la più grande biblioteca ombra de fumetti del mondo (95TB) — te podi aiutar a semearla La più grande biblioteca ombra de fumetti del mondo gà un unico punto de fallimento... fin a oggi. Avviso: sto post del blog xe stato deprecato. Gavemo deciso che IPFS no xe ancora pronto par el grande pubblico. Continueremo a linkar ai file su IPFS dall'Archivio de Anna quando xe possibile, ma no lo ospiteremo più noi stessi, né raccomandemo ad altri de farne il mirror usando IPFS. Consultè la nostra pagina dei Torrents se volè aiutar a preservare la nostra collezion. Metendo 5.998.794 libri su IPFS Una moltiplicazione di copie Tornando alla nostra domanda originale: come possiamo affermare di preservare le nostre collezioni in perpetuo? Il problema principale qui è che la nostra collezione è <a %(torrents_stats)s>cresciuta</a> rapidamente, estraendo e rendendo open-source alcune collezioni massicce (oltre al lavoro straordinario già svolto da altre biblioteche ombra di dati aperti come Sci-Hub e Library Genesis). Questa crescita dei dati rende più difficile replicare le collezioni in tutto il mondo. L'archiviazione dei dati è costosa! Ma siamo ottimisti, soprattutto osservando le seguenti tre tendenze. La <a %(annas_archive_stats)s>dimension totale</a> delle nostre collezioni, negli ultimi mesi, suddivisa par numero de seeders dei torrent. Tendenze dei prezzi degli HDD da fonti diverse (clicca per visualizzare lo studio). <a %(critical_window_chinese)s>Version cinese 中文版</a>, discuti su <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Abbiamo raccolto i frutti più facili Questo segue direttamente dalle nostre priorità discusse sopra. Preferiamo lavorare prima sulla liberazione di grandi collezioni. Ora che abbiamo assicurato alcune delle collezioni più grandi del mondo, ci aspettiamo che la nostra crescita sia molto più lenta. C'è ancora una lunga coda di collezioni più piccole, e nuovi libri vengono scansionati o pubblicati ogni giorno, ma il tasso sarà probabilmente molto più lento. Potremmo ancora raddoppiare o addirittura triplicare di dimensioni, ma su un periodo di tempo più lungo. Miglioramenti dell'OCR. Priorità Codice software de scienza e ingegneria Versioni finzionali o de intrattenimento de tuti i elementi sopra Dati geografici (p.e. mappe, rilievi geologici) Dati interni de corporazioni o governi (fughe de notizie) Dati de misurazion come misure scientifiche, dati economici, raporti aziendali Record de metadata in generale (de non-fiction e fiction; de altri media, arte, persone, ecc.; incluse recensioni) Libri di saggistica Riviste de non-fiction, giornali, manuali Trascrizioni de non-fiction de discorsi, documentari, podcast Dati organici come sequenze di DNA, semi di piante, o campioni microbici Articoli accademici, riviste, rapporti Siti web de scienza e ingegneria, discussioni online Trascrizioni de procedimenti legali o giudiziari Unicamente a rischio de distruzion (p.e. da guerra, tagli de fondi, cause legali, o persecuzion politica) Rari Unicamente sottofocalizzati Perché ne importa tanto dei articoli e dei libri? Mettemo da parte la nostra credenza fondamentale nella preservazion in generale — podemo scriver un altro post su questo. Allora perché articoli e libri specificamente? La risposta xe semplice: <strong>densità de informazion</strong>. Par megabyte de archiviazion, el testo scritto conserva più informazion de tutti i altri media. Anche se ne importa sia della conoscenza che della cultura, ne importa de più della prima. In generale, troviamo una gerarchia de densità de informazion e importanza de preservazion che se presenta più o meno così: La classifica in sta lista xe un poco arbitraria — diversi elementi xe pari o ghe xe disacordi dentro al nostro team — e probabilmente stemo dimenticando qualcossa de importante. Ma grosso modo xe cussì che prioritizemo. Qualchedun de sti elementi xe massa diversi dai altri par che ne preoccupemo (o xe già curati da altre istituzioni), come i dati organici o i dati geografici. Ma la maggior parte dei elementi in sta lista xe veramente importanti par noi. Un altro grande fattore nella nostra prioritizazion xe quanto a rischio xe un certo lavoro. Preferimo focalizarse su lavori che xe: Infine, ne importa la scala. Gavemo tempo e soldi limitai, quindi preferimo passar un mese a salvar 10.000 libri piuttosto che 1.000 libri — se xe più o meno de ugual valore e a rischio. <em><q>Ciò che è perso non può essere recuperato; ma salviamo ciò che rimane: non con volte e serrature che li proteggono dagli occhi e dall'uso del pubblico, consegnandoli al logorio del tempo, ma con una tale moltiplicazione di copie, da metterle al di là della portata degli incidenti.</q></em><br>— Thomas Jefferson, 1791 Biblioteche ombra Il codice può essere open source su Github, ma Github nel suo insieme non può essere facilmente replicato e quindi preservato (anche se in questo caso particolare ci sono copie sufficientemente distribuite della maggior parte dei repository di codice) I record di metadata possono essere visualizzati liberamente sul sito Worldcat, ma non scaricati in massa (fino a quando non li abbiamo <a %(worldcat_scrape)s>estratti</a>) Reddit è gratuito da usare, ma recentemente ha introdotto misure anti-scraping rigorose, a seguito dell'addestramento di LLM affamati di dati (ne parleremo più avanti) Ghe xe tante organizzazioni che ga missioni simili, e priorità simili. In effetti, ghe xe biblioteche, archivi, laboratori, musei, e altre istituzioni incaricate de la conservazion de sto tipo. Tante de queste xe ben finanziate, da governi, individui, o corporazioni. Ma ghe xe un grande punto cieco: il sistema legale. Qua sta el ruolo unico delle biblioteche ombra, e la rason par cui l'Archivio de Anna esiste. Podemo far robe che altre istituzioni no xe permesse de far. Adesso, no xe (spesso) che podemo archiviare materiali che xe illegali da conservare altrove. No, xe legale in tanti posti costruir un archivio con qualsiasi libri, articoli, riviste, e cussì via. Ma ciò che spesso manca ai archivi legali è la <strong>ridondanza e la longevità</strong>. Esistono libri di cui esiste solo una copia in qualche biblioteca fisica da qualche parte. Esistono record di metadata custoditi da una sola corporazione. Esistono giornali conservati solo su microfilm in un unico archivio. Le biblioteche possono subire tagli ai finanziamenti, le corporazioni possono fallire, gli archivi possono essere bombardati e bruciati fino a terra. Questo non è ipotetico — succede continuamente. La cosa che possiamo fare in modo unico su l'Archivio de Anna è conservare molte copie delle opere, su larga scala. Possiamo raccogliere articoli, libri, riviste e altro, e distribuirli in massa. Attualmente lo facciamo tramite torrent, ma le tecnologie esatte non importano e cambieranno nel tempo. La parte importante è ottenere molte copie distribuite in tutto il mondo. Questa citazione di oltre 200 anni fa è ancora vera: Una breve nota sul dominio pubblico. Poiché l'Archivio de Anna si concentra in modo unico su attività che sono illegali in molti luoghi del mondo, non ci preoccupiamo delle collezioni ampiamente disponibili, come i libri di dominio pubblico. Le entità legali spesso se ne prendono già cura. Tuttavia, ci sono considerazioni che a volte ci portano a lavorare su collezioni pubblicamente disponibili: - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. I costi di archiviazione continuano a diminuire esponenzialmente 3. Miglioramenti nella densità d'informazione Attualmente conserviamo i libri nei formati grezzi in cui ci vengono forniti. Certo, sono compressi, ma spesso sono ancora grandi scansioni o fotografie delle pagine. Fino ad ora, le uniche opzioni per ridurre la dimensione totale della nostra collezione sono state una compressione più aggressiva o la deduplicazione. Tuttavia, per ottenere risparmi significativi, entrambe sono troppo perdenti per i nostri gusti. La compressione pesante delle foto può rendere il testo appena leggibile. E la deduplicazione richiede un'alta fiducia che i libri siano esattamente gli stessi, il che è spesso troppo impreciso, specialmente se i contenuti sono gli stessi ma le scansioni sono fatte in occasioni diverse. C'è sempre stata una terza opzione, ma la sua qualità è stata così pessima che non l'abbiamo mai considerata: <strong>OCR, o Riconoscimento Ottico dei Caratteri</strong>. Questo è il processo di conversione delle foto in testo semplice, utilizzando l'IA per rilevare i caratteri nelle foto. Gli strumenti per questo esistono da tempo e sono stati abbastanza decenti, ma "abbastanza decenti" non è sufficiente per scopi di conservazione. Tuttavia, i recenti modelli di deep learning multimodali hanno fatto progressi estremamente rapidi, sebbene ancora a costi elevati. Ci aspettiamo che sia l'accuratezza che i costi migliorino drasticamente nei prossimi anni, al punto che diventerà realistico applicarli all'intera nostra biblioteca. Quando ciò accadrà, probabilmente conserveremo ancora i file originali, ma in aggiunta potremmo avere una versione molto più piccola della nostra biblioteca che la maggior parte delle persone vorrà duplicare. Il punto è che il testo grezzo stesso si comprime ancora meglio ed è molto più facile da deduplicare, offrendoci ancora più risparmi. In generale, non è irrealistico aspettarsi almeno una riduzione di 5-10 volte nella dimensione totale dei file, forse anche di più. Anche con una riduzione conservativa di 5 volte, staremmo guardando a <strong>$1,000–$3,000 in 10 anni anche se la nostra biblioteca triplicasse di dimensioni</strong>. Al momento della scrittura, i <a %(diskprices)s>prezzi dei dischi</a> per TB sono circa $12 per dischi nuovi, $8 per dischi usati e $4 per nastro. Se siamo conservatori e guardiamo solo ai dischi nuovi, significa che archiviare un petabyte costa circa $12,000. Se assumiamo che la nostra biblioteca triplicherà da 900TB a 2.7PB, ciò significherebbe $32,400 per replicare l'intera biblioteca. Aggiungendo elettricità, costo di altro hardware, e così via, arrotondiamo a $40,000. O con nastro più come $15,000–$20,000. Da un lato <strong>$15,000–$40,000 per la somma di tutta la conoscenza umana è un affare</strong>. Dall'altro lato, è un po' eccessivo aspettarsi tonnellate di copie complete, specialmente se vorremmo anche che quelle persone continuassero a seminare i loro torrent a beneficio degli altri. Questo è oggi. Ma il progresso avanza: I costi dei dischi rigidi per TB sono stati ridotti di circa un terzo negli ultimi 10 anni e probabilmente continueranno a diminuire a un ritmo simile. Il nastro sembra seguire una traiettoria simile. I prezzi degli SSD stanno scendendo ancora più velocemente e potrebbero superare i prezzi degli HDD entro la fine del decennio. Se questo si mantiene, allora tra 10 anni potremmo guardare solo $5,000–$13,000 per replicare l'intera collezione (1/3), o anche meno se cresciamo meno in dimensioni. Anche se ancora molti soldi, questo sarà raggiungibile per molte persone. E potrebbe essere ancora meglio a causa del prossimo punto… Su l'Archivio de Anna, spesso ne vien chiesto come podemo affermar de preservar le nostre collezioni in perpetuo, quando la dimension totale xe già vicina a 1 Petabyte (1000 TB), e continua a crescer. In questo articolo vedremo la nostra filosofia, e vedremo perché il prossimo decennio xe critico par la nostra missione de preservar la conoscenza e la cultura de l'umanità. Finestra critica Se queste previsioni sono accurate, ci <strong>basterà aspettare un paio d'anni</strong> prima che l'intera nostra collezione venga ampiamente duplicata. Così, nelle parole di Thomas Jefferson, "posta al di là della portata degli incidenti". Purtroppo, l'avvento degli LLM e il loro addestramento affamato di dati ha messo molti detentori di copyright sulla difensiva. Ancora più di quanto già non fossero. Molti siti web stanno rendendo più difficile il scraping e l'archiviazione, le cause legali volano, e nel frattempo le biblioteche fisiche e gli archivi continuano a essere trascurati. Possiamo solo aspettarci che queste tendenze continuino a peggiorare e che molte opere vadano perse ben prima di entrare nel pubblico dominio. <strong>Siamo alla vigilia di una rivoluzione nella conservazione, ma <q>ciò che è perso non può essere recuperato.</q></strong> Abbiamo una finestra critica di circa 5-10 anni durante la quale è ancora abbastanza costoso gestire una biblioteca ombra e creare molti duplicati in tutto il mondo, e durante la quale l'accesso non è stato ancora completamente chiuso. Se riusciamo a colmare questa finestra, allora avremo davvero conservato la conoscenza e la cultura dell'umanità in perpetuo. Non dovremmo lasciare che questo tempo vada sprecato. Non dovremmo lasciare che questa finestra critica si chiuda su di noi. Andiamo. La finestra critica delle biblioteche ombra Come podemo affermar de preservar le nostre collezioni in perpetuo, quando le xe già vicine a 1 PB? Collezion Qualche informazion in più sulla collezion. <a %(duxiu)s>Duxiu</a> xe un'enorme database de libri scanà, creato dal <a %(chaoxing)s>SuperStar Digital Library Group</a>. La maggior parte xe libri accademici, scanà par renderli disponibili digitalmente a università e biblioteche. Par el nostro pubblico anglofono, <a %(library_princeton)s>Princeton</a> e l'<a %(guides_lib_uw)s>Università de Washington</a> ga delle bone panoramiche. Ghe xe anca un articolo eccellente che dà più contesto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercalo nell'Archivio de Anna). I libri da Duxiu xe stati a lungo piratà su l'internet cinese. De solito i vien vendui par meno de un dollaro dai rivenditori. I xe tipicamente distribuiti usando l'equivalente cinese de Google Drive, che spesso xe stato hackerato par permetter più spazio de archiviazion. Alcuni dettagli tecnici i se trova <a %(github_duty_machine)s>qua</a> e <a %(github_821_github_io)s>qua</a>. Anca se i libri xe stati distribuiti semi-pubblicamente, xe abbastanza difficile ottenerli in massa. Gavemo messo questo in alto nella nostra lista de cose da far, e gavemo allocato più mesi de lavoro a tempo pieno par questo. Comunque, recentemente un volontario incredibile, straordinario e talentuoso ne ga contatà, dicendone che ga già fatto tutto sto lavoro — a grande spesa. I ne ga condiviso la collezion intera, senza aspettarse niente in cambio, eccetto la garanzia de preservazion a lungo termine. Veramente straordinario. I ga accettà de chieder aiuto in sto modo par far l'OCR della collezion. La collezion xe 7.543.702 file. Xe più de Library Genesis non-fiction (circa 5,3 milioni). La dimensione totale dei file xe circa 359TB (326TiB) nella sua forma attuale. Semo aperti ad altre proposte e idee. Basta contatarne. Consultè l'Archivio de Anna par più informazion sulle nostre collezioni, sforzi de preservazion, e come podè aiutar. Grazie! Pagine de esempio Par dimostrarne che gavè un bon pipeline, qua ghe xe delle pagine de esempio par cominciar, da un libro sui superconduttori. El vostro pipeline dovaria gestir adeguatamente la matematica, le tabelle, i grafici, le note a piè de pagina, e cussì via. Mandè le vostre pagine processà al nostro email. Se le par ben, ve mandaremo de più in privato, e ne aspettemo che siate in grado de far girar rapidamente el vostro pipeline anca su quei. Una volta che semo soddisfatti, podemo far un accordo. - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versione cinese 中文版</a>, <a %(news_ycombinator)s>Discuti su Hacker News</a> Questo è un breve post sul blog. Stiamo cercando un'azienda o un'istituzione che ci aiuti con l'OCR e l'estrazione del testo per una collezione massiccia che abbiamo acquisito, in cambio di un accesso esclusivo anticipato. Dopo il periodo di embargo, rilasceremo ovviamente l'intera collezione. El testo accademico de alta qualità xe estremamente utile par el training dei LLM. Anca se la nostra collezion xe in cinese, dovaria esser utile par el training dei LLM in inglese: i modelli par che i incodiga conceti e conoscenze indipendentemente da che lingua vien dal. Par questo, el testo ga da esser estrato dai scan. Cosa ghe cava fora l'Archivio de Anna? La ricerca testuale completa dei libri par i so utenti. Perché i nostri obiettivi i xe in linea con quei dei sviluppatori de LLM, semo in cerca de un collaboratore. Semoghe disposti a darve <strong>accesso anticipà esclusivo a sta collezion in massa par 1 ano</strong>, se podè far un OCR e un'estrazion del testo adeguà. Se semoghe disposti a condividi el codice intero del vostro pipeline con noi, semoghe disposti a embargo la collezion par più tempo. Accesso esclusivo per le aziende LLM alla più grande collezione di libri di saggistica cinese al mondo <em><strong>TL;DR:</strong> L'Archivio di Anna ha acquisito una collezione unica di 7,5 milioni / 350TB di libri di saggistica cinese — più grande di Library Genesis. Siamo disposti a dare a un'azienda LLM l'accesso esclusivo, in cambio di OCR di alta qualità ed estrazione del testo.</em> Architettura del sistema Dunque, poniamo che gavé trovà delle aziende disposte a ospitar el vostro sito senza chiuderve — chiamiamole “fornitori amanti della libertà” 😄. Scoprirete rapidamente che ospitar tuto con loro xe piuttosto costoso, quindi potreste voler trovar dei “fornitori economici” e far l'hosting effettivo lì, proxyando attraverso i fornitori amanti della libertà. Se lo fate bene, i fornitori economici no saprà mai cosa sté ospitando, e no riceverà mai lamentele. Con tuti sti fornitori ghe xe el rischio che ve chiuda comunque, quindi gavé bisogno anca de ridondanza. Gavemo bisogno de questo a tuti i livelli del nostro stack. Un'azienda un po' amante della libertà che se ga messa in una posizione interessante xe Cloudflare. I ga <a %(blog_cloudflare)s>sostenuto</a> che no xe un fornitore di hosting, ma un servizio, come un ISP. No xe quindi soggetti a richieste di rimozion DMCA o altre, e inoltra qualsiasi richiesta al vostro effettivo fornitore di hosting. I xe andai fin in tribunale par protegger sta struttura. Podemo quindi usarli come un altro strato de caching e protezion. Cloudflare no accetta pagamenti anonimi, quindi podemo solo usar el loro piano gratuito. Questo significa che no podemo usar le loro funzionalità de bilanciamento del carico o failover. Abbiamo quindi <a %(annas_archive_l255)s>implementato questo noi stessi</a> a livello de dominio. Al caricamento della pagina, el browser controllerà se el dominio attuale xe ancora disponibile, e se no, riscrive tuti i URL a un altro dominio. Dato che Cloudflare cache molte pagine, questo significa che un utente può atterrare sul nostro dominio principale, anca se el server proxy xe giù, e poi al clic successivo esser spostà su un altro dominio. Gavemo ancora anca preoccupazioni operative normali da affrontar, come monitorar la salute del server, registrare errori del backend e del frontend, e così via. La nostra architettura di failover permette maggiore robustezza anca su questo fronte, per esempio eseguendo un set completamente diverso de server su uno dei domini. Podemo anca eseguire versioni più vecie del codice e dei datasets su sto dominio separato, nel caso un bug critico nella versione principale passi inosservato. Podemo anca proteggerse contro Cloudflare che se rivolta contro de noi, rimuovendolo da uno dei domini, come sto dominio separato. Xe possibili diverse permutazioni de ste idee. Conclusione Xe stà un'esperienza interessante imparar come configurar un motore de ricerca de biblioteca ombra robusto e resiliente. Ghe xe tanti altri detagli da condivìder in post futuri, quindi fammi saver cosa te piaceria imparar de più! Come sempre, semo in cerca de donazioni par sostener questo lavoro, quindi assicurateve de visitar la pagina Donazioni su l'Archivio de Anna. Semo anca in cerca de altri tipi de supporto, come sovvenzioni, sponsor a lungo termine, fornitori de pagamenti ad alto rischio, forse anca (con gusto!) pubblicità. E se te vol contribuir col to tempo e le to abilità, semo sempre in cerca de sviluppatori, traduttori, e così via. Grazie par el to interesse e supporto. Token de innovazion Cominciemo col nostro stack tecnologico. El xe deliberatamente noioso. Usémo Flask, MariaDB e ElasticSearch. E xe letteralmente tuto qua. La ricerca xe un problema in gran parte risolto, e no gavemo intenzion de reinventarlo. In più, gavemo da spender i nostri <a %(mcfunley)s>token de innovazion</a> in qualcos'altro: no esser butai zo da le autorità. Dunque quanto xe legale o illegale l'Archivio de Anna esattamente? Questo dipende principalmente dalla giurisdizion legale. La maggior parte dei paesi crede in qualche forma de copyright, che significa che le persone o le aziende xe assegnai un monopolio esclusivo su certi tipi de opere per un certo periodo de tempo. Come nota a margine, all'Archivio de Anna credemo che, anca se ghe xe dei benefici, in generale el copyright xe un net-negativo par la società — ma questa xe una storia par un'altra volta. Sto monopolio esclusivo su certe opere significa che xe illegale par chiunque fora de sto monopolio distribuire direttamente ste opere — inclusi noi. Ma l'Archivio de Anna xe un motore de ricerca che no distribuisce direttamente ste opere (almeno no sul nostro sito clearnet), quindi dovressimo esser a posto, giusto? No esattamente. In molte giurisdizioni no xe solo illegale distribuire opere protette da copyright, ma anca collegarse a posti che lo fa. Un esempio classico de questo xe la legge DMCA dei Stati Uniti. Questa xe l'estremità più rigida dello spettro. All'altra estremità dello spettro ghe potrebbe teoricamente esser paesi senza leggi sul copyright, ma questi no esiste veramente. Praticamente ogni paese ga qualche forma de legge sul copyright nei libri. L'applicazione xe un'altra storia. Ghe xe tanti paesi con governi che no se preoccupa de far rispettar le leggi sul copyright. Ghe xe anca paesi in mezzo ai due estremi, che proibisce de distribuire opere protette da copyright, ma no proibisce de collegarse a ste opere. Un'altra considerazion xe a livello aziendale. Se un'azienda opera in una giurisdizion che no se preoccupa del copyright, ma l'azienda stessa no xe disposta a correr rischi, allora potrebbe chiudere el vostro sito appena qualcun se lamenta. Infine, una grande considerazion xe i pagamenti. Dato che gavemo bisogno de restar anonimi, no podemo usar i metodi de pagamento tradizionali. Questo ne lascia con le criptovalute, e solo un piccolo sottoinsieme de aziende le supporta (ghe xe carte de debito virtuali pagate con cripto, ma spesso no le xe accettate). - Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Gestisco <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, el più grande motore de ricerca open-source no-profit al mondo par le <a %(wikipedia_shadow_library)s>biblioteche ombra</a>, come Sci-Hub, Library Genesis, e Z-Library. El nostro obiettivo xe render la conoscenza e la cultura facilmente accessibili, e in definitiva costruir una comunità de persone che insieme archivia e preserva <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tutti i libri del mondo</a>. In sto articolo ve mostrerò come gestimo sto sito web, e le sfide uniche che vien con l'operar un sito web con uno stato legale discutibile, visto che no ghe xe un “AWS par le carità ombra”. <em>Consultè anca l'articolo gemelo <a %(blog_how_to_become_a_pirate_archivist)s>Come diventare un archivista pirata</a>.</em> Come gestir una biblioteca ombra: operazioni all'Archivio de Anna No ghe xe <q>AWS par le carità ombra,</q> cussì come gestimo l'Archivio de Anna? Strumenti Server dell'applicazione: Flask, MariaDB, ElasticSearch, Docker. Sviluppo: Gitlab, Weblate, Zulip. Gestione del server: Ansible, Checkmk, UFW. Hosting statico Onion: Tor, Nginx. Server proxy: Varnish. Vedemo quali strumenti usémo par realizzar tuto questo. Questo xe in continua evoluzion mentre affrontemo nuovi problemi e trovemo nuove soluzioni. Ghe xe qualcossa de decision che gavemo rivà a cambiar idea. Una xe la comunicazion tra i server: prima doparavemo Wireguard par questo, ma gavemo trovà che ogni tanto se ferma de trasmetter dati, o trasmette dati solo in una direzion. Questo xe successo con diversi setup de Wireguard che gavemo provà, come <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Gavemo provà anca a tunnelar i porti su SSH, doparando autossh e sshuttle, ma gavemo incontrà <a %(github_sshuttle)s>problemi là</a> (anca se no xe ancora chiaro se autossh soffre de problemi de TCP-over-TCP o no — me par solo una soluzion improvvisada ma forse xe in realtà a posto?). Inveze, gavemo tornà a le conession dirette tra i server, nascondendo che un server xe in esecuzion su i fornitori economici doparando il filtraggio IP con UFW. Questo ga el svantaggio che Docker no funziona ben con UFW, a meno che no dopari <code>network_mode: "host"</code>. Tutto questo xe un poco più soggetto a errori, perché esporai el to server a internet con solo una picola configurazion sbagliada. Forse dovessimo tornar a autossh — el feedback saria benvenù qua. Gavemo anca cambià idea su Varnish vs. Nginx. Al momento ne piase Varnish, ma ga i so difeti e spigoli. El stesso val par Checkmk: no ne piase tanto, ma funziona par adesso. Weblate xe stà decente ma no incredibile — ogni tanto temo che perderà i me dati ogni volta che provo a sincronizarlo col nostro repo git. Flask xe stà bon nel complesso, ma ga qualchi difeto strano che ga costà tanto tempo par debuggar, come configurar domini personalizadi, o problemi con la so integrazion SqlAlchemy. Finora gli altri strumenti xe stà ottimi: no gavemo lamentele serie su MariaDB, ElasticSearch, Gitlab, Zulip, Docker, e Tor. Tutti questi ga avù qualchi problema, ma niente de troppo serio o che consuma tempo. Comunità El primo sfido podarìa eser un che sorprende. No xe un problema tecnico, o un problema legale. Xe un problema psicologico: far sto lavoro nell'ombra pol eser incredibilmente solitario. Dipendendo da cosa te stai pianificando de far, e dal tuo modello de minaccia, te podarìa dover eser molto attento. Da un lato del spettro gavemo persone come Alexandra Elbakyan*, la fondadora de Sci-Hub, che xe molto aperta sulle sue attività. Ma ela xe a alto rischio de eser arrestada se la visitasse un paese occidentale a sto punto, e podarìa affrontar decenni de prigionia. Xe un rischio che te sarìa disposto a correr? Semo dall'altro lato del spettro; stemo molto attenti a no lasar nessuna traccia, e avemo una forte sicurezza operativa. * Come menzionà su HN da "ynno", inizialmente Alexandra no voleva eser conosuda: "I suoi server iera configuradi par emettere messaggi de errore dettagliadi da PHP, inclusi el percorso completo del file sorgente che causava el problema, che iera sotto la directory /home/<USER>" Quindi, usa nomi utente casuali sui computer che te usi par sta roba, nel caso te configuri qualcosa male. Sta segretezza, comunque, la gà un costo psicologico. La maggior parte delle persone ama eser riconosciuda par el lavoro che fa, e invece no te podi prenderti nessun merito par questo nella vita reale. Anca le cose semplici podarìa eser sfidanti, come amici che te domanda cosa te gà fato de recente (a un certo punto "smanettar col mio NAS / homelab" diventa vecio). Xe per questo che xe cusì importante trovar na comunità. Te podi rinunciar a un po' de sicurezza operativa confidandote con qualche amico molto stretto, che te sai che te podi fidar profondamente. Anca in quel caso stai attento a no metar niente per iscritto, nel caso che i debba consegnar le loro email alle autorità, o se i loro dispositivi xe compromessi in qualche altro modo. Meglio ancora xe trovar qualche altro pirata. Se i tuoi amici stretti xe interessadi a unirse a ti, fantastico! Altrimenti, te podarìa trovar altri online. Purtroppo sta xe ancora una comunità di nicchia. Finora gavemo trovà solo un pugno de altri che xe attivi in sto spazio. Bon punti de partenza parè che sia i forum de Library Genesis, e r/DataHoarder. L'Archive Team gà anca individui con idee simili, anca se i opera dentro la legge (anca se in qualche area grigia de la legge). Le scene tradizionali de "warez" e pirateria gà anca persone che pensa in modi simili. Semo drioi a idee su come favorir la comunità e esplorar idee. Sentìvì liberi de contatàrne su Twitter o Reddit. Forse podarìmo ospitar un tipo de forum o gruppo de ciacole. Un problema xe che questo pol facilmente vegnir censurà quando se dopara piattaforme comuni, cusì dovarìmo ospitarlo da soli. Ghe xe anca un compromesso tra far ste discussioni completamente publiche (più potenziale de coinvolgimento) contro farle private (no far saver ai potenziali "obiettivi" che stemo per raschiarli). Dovarìmo pensar a questo. Fène saver se sìe interessà a questo! Conclusione Speremo che questo sia utile per i nuovi archivisti pirati che stanno iniziando. Siamo entusiasti di darvi il benvenuto in questo mondo, quindi non esitate a contattarci. Preserviamo quanta più conoscenza e cultura del mondo possibile e riflettiamola ovunque. Progetti 4. Selezion de dati Spesso te pol doparar i metadata par capir un sottoinsieme ragionevole de dati da scaricar. Anca se alla fine te volessi scaricar tuti i dati, pol esser utile dar priorità ai elementi più importanti prima, nel caso te vegni scoperto e le difese vegni migliorà, o perché te gaveressi bisogno de comprar più dischi, o semplicemente perché qualcos'altro te capita in te la vita prima che te riesci a scaricar tuto. Par esempio, una colezzion podaria gaver più edizioni de la stessa risorsa de base (come un libro o un film), dove una xe segnalà come la mejo qualità. Salvare quelle edizioni prima faria tanto senso. Te podarissi alla fine voler salvar tuti i edizioni, visto che in certi casi i metadata podaria esser etichettà in modo incorreto, o podaria esserci compromessi sconosciuti tra le edizioni (par esempio, la "mejo edizion" podaria esser la mejo in molti modi ma pezo in altri, come un film che gà una risoluzion più alta ma manca de sottotitoli). Te pol anca cercar nel tuo database de metadata par trovar robe interessanti. Qual xe el file più grande che xe ospitato, e parché xe cussì grande? Qual xe el file più piccolo? Ghe xe schemi interessanti o inaspettati quando se tratta de certe categorie, lingue, e cussì via? Ghe xe titoli duplicati o molto simili? Ghe xe schemi su quando i dati xe stati aggiunti, come un giorno in cui molti file xe stati aggiunti in una volta? Spesso te pol imparar tanto guardando el dataset in modi diversi. Nel nostro caso, gavemo deduplicà i libri de Z-Library contro i hash md5 in Library Genesis, risparmiando cussì tanto tempo de scaricamento e spazio su disco. Questa xe una situazion abbastanza unica però. Nella maggior parte dei casi no ghe xe database completi de quali file xe già conservà adeguatamente dai pirati compagni. Questo in sé xe una grande opportunità par qualcun là fora. Saria fantastico gaver una panoramica aggiornata regolarmente de robe come musica e film che xe già ampiamente seminà sui siti torrent, e che quindi xe de priorità più bassa da includer nei specchi pirata. 6. Distribuzion Gavì i dati, dandovi cussì el possesso del primo specchio pirata al mondo del vostro obiettivo (probabilmente). In molti modi la parte più difficile xe finìa, ma la parte più rischiosa xe ancora davanti a voi. Dopo tuto, finora gavì stai furtivi; volando sotto el radar. Tuto quel che dovevi far xe doparar un buon VPN per tuto el tempo, no compilar i vostri dati personali in nessun modulo (ovvio), e forse doparar una sessione browser speciale (o anca un computer diverso). Adesso dovì distribuir i dati. Nel nostro caso volevimo prima contribuire i libri indrio a Library Genesis, ma poi gavì scoperto rapidamente le difficoltà in questo (ordinamento fiction vs non-fiction). Cussì gavì deciso de distribuir doparando torrent in stile Library Genesis. Se gavì l'opportunità de contribuire a un progetto esistente, allora questo podaria risparmiarvi tanto tempo. Comunque, attualmente no ghe xe molti specchi pirata ben organizzà là fora. Cussì diciamo che decidì de distribuir i torrent da soli. Prova a tener quei file piccoli, cussì xe facili da specchiar su altri siti. Dovì poi seminare i torrent da soli, restando comunque anonimi. Te pol doparar un VPN (con o senza port forwarding), o pagar con Bitcoin mescolà par un Seedbox. Se no savì cosa vol dir alcuni de quei termini, gavì un bel po' de lettura da far, visto che xe importante che capì i compromessi de rischio qua. Te pol ospitar i file torrent stessi su siti torrent esistenti. Nel nostro caso, gavì scelto de ospitar effettivamente un sito web, visto che volevimo anca diffonder la nostra filosofia in modo chiaro. Te pol far questo da solo in modo simile (doparimo Njalla par i nostri domini e hosting, pagà con Bitcoin mescolà), ma sentitevi liberi de contattarci par farci ospitar i vostri torrent. Gavì intenzion de costruir un indice completo de specchi pirata col tempo, se questa idea prende piede. Per quanto riguarda la selezion del VPN, xe già stato scritto tanto su questo, cussì ripeteremo solo el consiglio generale de sceglier in base alla reputazion. Politiche de no-log testate in tribunale con lunghe storie de protezion della privacy xe l'opzion de rischio più basso, secondo noi. Nota che anca quando fai tuto giusto, no te pol mai arrivar a zero rischio. Par esempio, quando semini i tuoi torrent, un attore statale altamente motivato pol probabilmente guardar i flussi de dati in entrata e in uscita par i server VPN, e dedurre chi te xe. O te pol semplicemente sbagliar in qualche modo. Probabilmente gavì già fatto, e lo faremo ancora. Fortunatamente, i stati nazionali no se preoccupa <em>così</em> tanto de la pirateria. Una decision par far par ogni progetto, xe se pubblicarlo doparando la stessa identità de prima, o no. Se continui a doparar lo stesso nome, allora i errori nella sicurezza operativa dei progetti precedenti podaria tornar a morderti. Ma pubblicar sotto nomi diversi significa che no costruisci una reputazion più duratura. Gavì scelto de aver una forte sicurezza operativa fin dall'inizio cussì podemo continuare a doparar la stessa identità, ma no esiteremo a pubblicar sotto un nome diverso se sbagliamo o se le circostanze lo richiede. Diffonder la parola pol esser complicato. Come gavì detto, questa xe ancora una comunità di nicchia. Originariamente gavì postato su Reddit, ma veramente gavì ottenuto trazione su Hacker News. Par adesso la nostra raccomandazion xe de postarlo in pochi posti e veder cosa succede. E ancora, contattateci. Gavì piacerebbe diffonder la parola de più sforzi de archivismo pirata. 1. Selezion de dominio / filosofia No ghe xe carenza de conoscenza e patrimonio culturale da salvar, che pol esser opprimente. Xe per questo che spesso xe utile fermarse un momento e pensar a cosa podè contribuire. Ognuno gà un modo diverso de pensar a questo, ma qua xe alcune domande che podarìa farve: Nel nostro caso, ne importava in particolare de la conservazion a lungo termine de la scienza. Savèmo de Library Genesis, e come che xe stata completamente specchiata molte volte usando i torrent. Ne piaseva sta idea. Poi un giorno, uno de noi gà provà a trovar dei libri de testo scientifici su Library Genesis, ma no li gà trovà, mettendo in dubbio quanto completa realmente fosse. Poi gavemo cercà quei libri de testo online, e li gavemo trovà in altri posti, che gà piantà el seme par el nostro progetto. Anca prima de saver del Z-Library, gavemo avù l'idea de no provàr a collezionar tuti quei libri manualmente, ma de focalizarse su specchiar collezioni esistenti, e contribuirle indrio a Library Genesis. Quali abilità gà che podè doparar a vostro vantaggio? Par esempio, se sìe esperti de sicurezza online, podè trovar modi de superar i blocchi IP par obiettivi sicuri. Se sìe bravi a organizzar comunità, allora forse podè radunar un po’ de persone intorno a un obiettivo. Xe utile saver un po’ de programmazione però, anche solo par mantener una bona sicurezza operativa durante sto processo. Quale saria un'area de alto impatto su cui focalizarse? Se te spenderà X ore su archiviazione pirata, allora come podè ottener el massimo "ritorno par el tuo investimento"? Quali xe i modi unici in cui te stai pensando a questo? Podarìa gaver idee o approcci interessanti che altri podarìa aver perso. Quanto tempo gà par questo? El nostro consiglio saria de cominciar piccolo e far progetti più grandi man mano che te ciapi la man, ma pol diventar tutto-assorbente. Parché sìe interessà a questo? Cosa ve appassiona? Se podemo metter insieme un gruppo de persone che archivia tuti i tipi de robe che ghe interessa specificamente, copriremo un bel po’! Saverè molto più de la persona media su la vostra passione, come quali xe i dati importanti da salvar, quali xe le mejo collezioni e comunità online, e cusì via. 3. Raschiamento de metadata Data aggiunta/modificata: così te podi tornar più tardi e scaricar file che no gà scaricà prima (anche se spesso te podi anca usar l'ID o l'hash par questo). Hash (md5, sha1): par confermar che te gà scaricà el file correttamente. ID: pol esser un ID interno, ma ID come ISBN o DOI xe utili anca. Nome file / posizione Descrizion, categoria, tag, autori, lingua, ecc. Dimensione: par calcolare quanto spazio su disco te gà bisogno. Diventemo un poco più tecnici qua. Par raschiare effettivamente i metadata dai siti web, gavemo tenùo le robe abbastanza semplici. Usémo script Python, a volte curl, e un database MySQL par salvar i risultati. No gavemo usà software de raschiamento sofisticà che pol mappare siti complessi, visto che finora gavemo solo bisogno de raschiare uno o do tipi de pagine enumerando semplicemente i ID e parsando l'HTML. Se no ghe xe pagine facilmente enumerabili, allora te podarìa aver bisogno de un crawler adeguà che prova a trovar tutte le pagine. Prima de iniziare a raschiare un sito intero, prova a farlo manualmente par un po'. Passa par un par de dozzine de pagine da solo, par capir come funziona. A volte te incrocerai già blocchi IP o altri comportamenti interessanti in sto modo. Lo stesso vale par il raschiamento dei dati: prima de andar troppo in profondità su sto obiettivo, assicurate de poder effettivamente scaricar i so dati in modo efficace. Par superar le restrizioni, ghe xe un par de robe che te podi provare. Ghe xe altri indirizzi IP o server che ospita i stessi dati ma no gà le stesse restrizioni? Ghe xe endpoint API che no gà restrizioni, mentre altri sì? A che velocità de download el tuo IP vien bloccà, e par quanto tempo? O no vieni bloccà ma rallentà? Cosa succede se te crei un account utente, come cambia le robe allora? Te podi usar HTTP/2 par tener le connessioni aperte, e questo aumenta la velocità con cui te podi richieder le pagine? Ghe xe pagine che elenca più file in una volta, e le informazioni elencate là xe sufficienti? Le robe che probabilmente te vorrai salvar include: Tipicamente, lo facemo in do fasi. Prima scaricamo i file HTML grezzi, solitamente direttamente in MySQL (par evitar tanti file piccoli, de cui parliamo più sotto). Poi, in un passaggio separato, passiamo par quei file HTML e i parsiamo in tabelle MySQL effettive. In questo modo no te gà bisogno de riscaricar tutto da zero se te scopri un errore nel tuo codice de parsing, visto che te podi semplicemente riprocessar i file HTML col nuovo codice. Xe anca spesso più facile parallelizzare el passaggio de processamento, risparmiando così un po' de tempo (e te podi scriver el codice de processamento mentre el raschiamento xe in corso, invece de dover scriver entrambi i passaggi in una volta). Finalmente, nota che per certi obiettivi lo scraping dei metadata xe tuto quel che ghe xe. Ghe xe alcune colezzioni de metadata enormi là fora che no xe conservà adeguatamente. Titolo Selezion de dominio / filosofia: Dove voì più o meno focalizarve, e parché? Quali xe le vostre passioni, abilità e circostanze uniche che podè doparar a vostro vantaggio? Selezion de obiettivo: Quale collezion specifica voì specchiar? Raschiar de metadata: Catalogar informazioni sui file, senza realmente scaricar i file stessi (che spesso xe molto più grandi). Selezion de dati: Basandose sui metadata, restringer quali dati xe più rilevanti da archiviare adesso. Podarìa esser tuto, ma spesso ghe xe un modo ragionevole de risparmiar spazio e banda. Raschiar de dati: Realmente ottener i dati. Distribuzion: Impacchetarlo in torrent, annunciarlo da qualche parte, farlo diffonder tra la gente. 5. Scraping de dati Adesso te xe pronto par scaricar effettivamente i dati in massa. Come menzionà prima, a questo punto te dovresti già aver scaricà manualmente un bel po' de file, par capir mejo el comportamento e le restrizioni del obiettivo. Comunque, ghe sarà ancora sorprese in serbo par ti una volta che te inizi a scaricar tanti file in una volta. El nostro consiglio qua xe principalmente de tenerlo semplice. Inizia semplicemente scaricando un bel po' de file. Te pol doparar Python, e poi espander a più thread. Ma a volte anca più semplice xe generar file Bash direttamente dal database, e poi eseguirne più in più finestre terminal par scalar. Un rapido trucco tecnico che vale la pena menzionare qua xe doparar OUTFILE in MySQL, che te pol scriver ovunque se te disabiliti "secure_file_priv" in mysqld.cnf (e assicurati de disabilitar/override anca AppArmor se te xe su Linux). Gavemo i dati su semplici dischi rigidi. Inizia con quel che gà, e espandi lentamente. Pol esser opprimente pensar de conservar centinaia de TB de dati. Se questa xe la situazion che te stai affrontando, metti fora prima un buon sottoinsieme, e nel tuo annuncio chiedi aiuto par conservar el resto. Se te volessi comprar più dischi rigidi da solo, allora r/DataHoarder gà delle buone risorse su come ottener buoni affari. Prova a no preoccuparti troppo de filesystem sofisticati. Xe facile cader nel buco del coniglio de configurar robe come ZFS. Un dettaglio tecnico de cui esser consapevoli però, xe che molti filesystem no gestisse ben tanti file. Gavemo trovà che un semplice workaround xe crear più directory, ad esempio par diversi range de ID o prefissi hash. Dopo aver scaricà i dati, assicurati de controllar l'integrità dei file doparando i hash nei metadata, se disponibili. 2. Selezion de obiettivo Accessibile: no usa un sacco de strati de protezion par impedirte de raschiare i so metadata e dati. Intuizion speciale: ti gà qualche informazion speciale su sto obiettivo, come se ti gà accesso speciale a sta collezion, o ti gà capìo come superar le so difese. No xe richiesto (el nostro progetto in arrivo no fa niente de speciale), ma certamente aiuta! Grando Dunque, gavemo la nostra area che stemo vardando, ora quale collezion specifica dovemo speciar? Ghe xe un par de robe che fa un bon obiettivo: Quando gavemo trovà i nostri libri de scienza su siti diversi da Library Genesis, gavemo provà a capir come i xe finìi su internet. Poi gavemo trovà Z-Library, e gavemo capìo che anca se la maggior parte dei libri no fa la so prima apparizion là, i finisse là alla fine. Gavemo imparà del so rapporto con Library Genesis, e la struttura de incentivi (finanziari) e l'interfaccia utente superiore, che i fa de 'sta collezion una molto più completa. Poi gavemo fato un po' de raschiamento preliminare de metadata e dati, e gavemo capìo che podemo superar i so limiti de download IP, sfruttando l'accesso speciale de un dei nostri membri a tanti server proxy. Mentre esplori diversi obiettivi, xe già importante nasconder le tue tracce usando VPN e indirizzi email usa e getta, de cui parleremo più avanti. Unico: no xe già ben coperto da altri progetti. Quando femo un progetto, el gà un par de fasi: Queste no xe fasi completamente indipendenti, e spesso intuizioni da una fase successiva te manda indrio a una fase precedente. Par esempio, durante el raschiar de metadata podarìa capir che l'obiettivo che gà selezionà gà meccanismi difensivi oltre el tuo livello de abilità (come blocchi IP), cusì te torni indrio e trovi un altro obiettivo. - Anna e el team (<a %(reddit)s>Reddit</a>) Libri interi podarìa eser scritti sul <em>perché</em> de la conservazion digital in generale, e de l'archivismo pirata in particolare, ma laseme dar un breve primer par chi no xe tanto familiar. El mondo sta producendo più conoscensa e cultura che mai, ma anca più de ela se sta perdendo che mai. L'umanità la se fida in gran parte de le corporazion come editori accademici, servizi de streaming, e compagnie de social media par sto patrimonio, e spesso no i gà dimostrà de eser gran custodi. Varda el documentario Digital Amnesia, o veramente qualsiasi discorso de Jason Scott. Ghe xe qualche istituzion che fa un bon lavoro de archiviare quanto più che pol, ma i xe vincoladi dalla legge. Come pirati, semo in una posizione unica par archiviare coleçion che lori no pol toccar, par via de l'applicazion del copyright o altre restrizion. Podemo anca speciar coleçion molte volte, in tuto el mondo, aumentando cusì le possibilità de una conservazion adeguada. Par adesso, no intraremo in discussioni sui pro e i contro de la proprietà intellettuale, la moralità de infrangere la legge, riflessioni sulla censura, o la questione de l'accesso ala conoscensa e ala cultura. Con tuto questo fuori dai piedi, tuffemose nel <em>come</em>. Condivideremo come la nostra squadra xe diventada archivisti pirati, e le lezioni che gavemo imparà lungo el percorso. Ghe xe molti sfidi quando te intraprendi sto viaggio, e speremo de poderte aiutar a superar qualcuna de ele. Come diventar un archivista pirata El primo sfido podarìa eser un che sorprende. No xe un problema tecnico, o un problema legale. Xe un problema psicologico. Prima de tuffarse, do aggiornamenti sul Pirate Library Mirror (EDIT: spostà su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>): Gavemo ricevù delle donazion estremamente generose. La prima xe stada de $10k da un individuo anonimo che gà anca supportà "bookwarrior", el fondador originale de Library Genesis. Un ringraziamento speciale a bookwarrior par aver facilità sta donazion. La seconda xe stada un altro $10k da un donatore anonimo, che se gà messo in contatto dopo la nostra ultima release, e xe stado ispirà a darne na man. Gavemo anca avudo un numero de donazion più piccole. Grazie tante par tuto el vostro supporto generoso. Gavemo dei progetti novi e entusiasmanti in cantiere che sto supporto aiuterà, quindi restè sintonizadi. Gavemo avudo qualche difficoltà tecnica co la dimension de la nostra seconda release, ma i nostri torrent xe su e in seeding adesso. Gavemo anca ricevù un'offerta generosa da un individuo anonimo par far seeding de la nostra coleçion sui loro server ad altissima velocità, quindi stemo facendo un caricamento speciale sui loro computer, dopo de che tuti che sta scaricando la coleçion dovarìa veder un gran miglioramento in velocità. Post del blog Ciao, son Anna. Go creato <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, la più grande biblioteca ombra del mondo. Questo xe el me blog personale, in cui mi e i me compagni scrivemo de pirateria, preservazion digitale, e altro. Conetete con mi su <a %(reddit)s>Reddit</a>. Nota che questo sito web xe solo un blog. Ospitèmo solo le nostre parole qua. No ghe xe torrent o altri file coperti da copyright ospitati o collegati qua. <strong>Biblioteca</strong> - Come la maggior parte delle biblioteche, ci concentriamo principalmente su materiali scritti come i libri. Potremmo espanderci in altri tipi di media in futuro. <strong>Mirror</strong> - Siamo strettamente uno specchio di biblioteche esistenti. Ci concentriamo sulla preservazione, non sul rendere i libri facilmente ricercabili e scaricabili (accesso) o sul favorire una grande comunità di persone che contribuiscono con nuovi libri (approvvigionamento). <strong>Pirata</strong> - Violiamo deliberatamente la legge sul copyright nella maggior parte dei paesi. Questo ci permette di fare qualcosa che le entità legali non possono fare: assicurarci che i libri siano riflessi ovunque. <em>Non colleghiamo i file da questo blog. Per favore, trovateli da soli.</em> - Anna e el team (<a %(reddit)s>Reddit</a>) Questo progetto (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>) mira a contribuire alla preservazione e liberazione della conoscenza umana. Facciamo il nostro piccolo e umile contributo, seguendo le orme dei grandi che ci hanno preceduto. L'obiettivo di questo progetto è illustrato dal suo nome: La prima biblioteca che abbiamo riflesso è Z-Library. Questa è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono con questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono la loro collezione facilmente riflettibile, il che impedisce una vasta preservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno). No femo giudizi morali su chi che ciàrca schei par l'accesso in massa a na coleçion de libri ilegali. L'è fora de ogni dubio che la Z-Library la gà avudo sucesso nel espandere l'accesso ala conoscensa e nel trovar più libri. Semu qua solo par far la nostra parte: assicurar la conservazion a longo termine de sta coleçion privata. Vorremmo invitarvi ad aiutare a preservare e liberare la conoscenza umana scaricando e seminando i nostri torrent. Vedi la pagina del progetto per ulteriori informazioni su come sono organizzati i dati. Invitiamo anche molto a contribuire con le vostre idee su quali collezioni riflettere successivamente e come procedere. Insieme possiamo ottenere molto. Questo è solo un piccolo contributo tra innumerevoli altri. Grazie, per tutto ciò che fate. Presentando il Mirror della Biblioteca Pirata: Preservando 7TB di libri (che non sono in Libgen) 10% of del patrimonio scritto dell'umanità preservato per sempre <strong>Google.</strong> Dopo tuto, i ga fatto questa ricerca par Google Books. Tuttavia, il loro metadata no xe accessibile in massa e piuttosto difficile da raschiare. <strong>Vari sistemi bibliotecari individuali e archivi.</strong> Ghe xe biblioteche e archivi che no xe stati indicizzati e aggregati da nessuno dei sopra menzionati, spesso perché xe sottofinanziati, o par altri motivi no vogliono condividere i loro dati con Open Library, OCLC, Google, e così via. Molti de questi ga record digitali accessibili attraverso internet, e spesso no xe molto ben protetti, quindi se vuoi aiutar e divertirti imparando sui strani sistemi bibliotecari, questi xe ottimi punti de partenza. <strong>ISBNdb.</strong> Questo xe il tema de questo post del blog. ISBNdb raschia vari siti web par il metadata dei libri, in particolare dati sui prezzi, che poi i vende ai librai, così i può prezzare i loro libri in accordo col resto del mercato. Visto che i ISBN xe abbastanza universali oggigiorno, i ga effettivamente costruito una "pagina web par ogni libro". <strong>Open Library.</strong> Come menzionato prima, questo xe il loro intero scopo. I ga ottenuto enormi quantità de dati bibliotecari da biblioteche cooperanti e archivi nazionali, e continua a farlo. I ga anche bibliotecari volontari e un team tecnico che sta cercando de deduplicare i record, e taggarli con ogni sorta de metadata. La cosa migliore xe che il loro dataset xe completamente aperto. Te pol semplicemente <a %(openlibrary)s>scaricarlo</a>. <strong>WorldCat.</strong> Questo xe un sito web gestito dal non-profit OCLC, che vende sistemi de gestione bibliotecaria. I aggrega metadata dei libri da tante biblioteche, e lo rende disponibile attraverso il sito web WorldCat. Tuttavia, i fa anche soldi vendendo questi dati, quindi no xe disponibile par il download in massa. I ga alcuni dataset in massa più limitati disponibili par il download, in cooperazione con biblioteche specifiche. 1. Par qualche definizion ragionevole de "per sempre". ;) 2. Naturalmente, l'eredità scritta de l'umanità xe molto più de libri, specialmente oggigiorno. Par il bene de questo post e dei nostri rilasci recenti stemo focalizandose sui libri, ma i nostri interessi se estende più in là. 3. Ghe xe molto de più che se podaria dir su Aaron Swartz, ma volemo solo menzionarlo brevemente, visto che el gioca un ruolo centrale in questa storia. Col passar del tempo, più persone podaria incontrar el suo nome par la prima volta, e successivamente tuffarse nel buco del coniglio da sole. <strong>Copie fisiche.</strong> Ovviamente questo no xe molto utile, visto che xe solo duplicati del stesso materiale. Saria fantastico se podessimo preservare tuti i annotazioni che le persone fa nei libri, come i famosi "scarabocchi nei margini" de Fermat. Ma purtroppo, questo rimarrà un sogno par un archivista. <strong>“Edizioni”.</strong> Qua conti ogni versione unica de un libro. Se qualcosa de esso xe diverso, come una copertina diversa o una prefazione diversa, conta come un'edizione diversa. <strong>File.</strong> Quando se lavora con biblioteche ombra come Library Genesis, Sci-Hub, o Z-Library, ghe xe un'altra considerazione. Ghe può esser multiple scansioni del stessa edizione. E le persone può far versioni migliori dei file esistenti, scansionando el testo usando OCR, o correggendo pagine che xe state scansionate a un angolo. Vogliamo contare questi file come una sola edizione, il che richiederebbe un buon metadata, o deduplicazione usando misure de similarità dei documenti. <strong>“Opere”.</strong> Par esempio “Harry Potter e la Camera dei Segreti” come concetto logico, che comprende tuti i versioni de esso, come traduzioni diverse e ristampe. Questa xe una definizione abbastanza utile, ma può esser difficile tracciare il confine de cosa conta. Par esempio, probabilmente vogliamo preservare traduzioni diverse, anche se le ristampe con solo piccole differenze potrebbe no esser così importanti. - Anna e el team (<a %(reddit)s>Reddit</a>) Con il Mirror della Biblioteca Pirata (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>), il nostro obiettivo è prendere tutti i libri del mondo e preservarli per sempre.<sup>1</sup> Tra i nostri torrent di Z-Library e i torrent originali di Library Genesis, abbiamo 11.783.153 file. Ma quanti sono davvero? Se deduplicassimo correttamente quei file, quale percentuale di tutti i libri del mondo abbiamo preservato? Ci piacerebbe davvero avere qualcosa del genere: Cominciamo con alcuni numeri approssimativi: In entrambi Z-Library/Libgen e Open Library ghe xe molti più libri che ISBN unici. Questo significa che molti de quei libri no ga ISBN, o xe semplicemente mancante il metadata dell'ISBN? Probabilmente possiamo rispondere a questa domanda con una combinazione de abbinamento automatico basato su altri attributi (titolo, autore, editore, ecc.), integrando più fonti de dati, e estraendo i ISBN dalle scansioni effettive dei libri stessi (nel caso de Z-Library/Libgen). Quanti de quei ISBN xe unici? Questo xe meglio illustrato con un diagramma di Venn: Par esser più precisi: Semo stai sorpresi da quanto poco se sovraponi! ISBNdb ga un gran numero de ISBN che no i compare né in Z-Library né in Open Library, e lo stesso val (in misura minore ma ancora sostanziale) par i altri do. Questo solleva molte nuove domande. Quanto aiutaria un abbinamento automatico nel etichettare i libri che no iera etichettai con ISBN? Ghe saria tanti abbinamenti e quindi un aumento del sovraponimento? Inoltre, cosa succederia se portassimo dentro un 4° o 5° dataset? Quanto sovraponimento vedremmo allora? Questo ne dà un punto de partenza. Podemo adesso guardar tutti i ISBN che no iera nel dataset de Z-Library, e che no i corrisponde neanche ai campi titolo/autore. Questo ne podaria dar un manico su come preservar tutti i libri del mondo: prima raschiando internet par scansioni, poi andando fora nella vita reale par scannerizzar i libri. Quest'ultimo podaria anche esser finanzià da la folla, o guidà da "taglie" da parte de persone che voria veder certi libri digitalizai. Tutto questo xe una storia par un altro momento. Se te vol dar na man con qualcossa de questo — ulteriore analisi; raschiar più metadata; trovar più libri; OCR dei libri; far questo par altri domini (per esempio articoli, audiolibri, film, programmi TV, riviste) o anche render qualcossa de questi dati disponibili par robe come ML / addestramento di modelli di linguaggio ampi — contateme (<a %(reddit)s>Reddit</a>). Se te xe specificamente interessà a l'analisi dei dati, stemo lavorando par render i nostri Datasets e script disponibili in un formato più facile da usar. Saria fantastico se te podessi semplicemente forkare un notebook e cominciar a giocar con questo. Infine, se te vol sostener questo lavoro, considera de far na donazion. Questa xe un'operazione gestida interamente da volontari, e la tua contribuzion fa una gran differenza. Ogni piccolo aiuto conta. Par adesso accettemo donazioni in crypto; vedi la pagina Donazioni su l'Archivio de Anna. Per una percentuale, abbiamo bisogno di un denominatore: il numero totale di libri mai pubblicati.<sup>2</sup> Prima della fine di Google Books, un ingegnere del progetto, Leonid Taycher, <a %(booksearch_blogspot)s>ha cercato di stimare</a> questo numero. Ha proposto — scherzosamente — 129.864.880 (“almeno fino a domenica”). Ha stimato questo numero costruendo un database unificato di tutti i libri del mondo. Per questo, ha raccolto diversi datasets e poi li ha uniti in vari modi. Parlando de sfuggita, ghe xe un'altra persona che ga provà a catalogar tuti i libri del mondo: Aaron Swartz, el defunto attivista digital e co-fondador de Reddit.<sup>3</sup> El <a %(youtube)s>ga inizià Open Library</a> col scopo de "una pagina web par ogni libro mai pubblicà", combinando dati da tante fonti diverse. El ga finì par pagar el prezzo più alto par el so lavoro de preservazion digital quando el xe stà perseguità par aver scaricà in massa articoli accademici, portandolo al suicidio. Inutile dirlo, questa xe una delle ragioni par cui el nostro gruppo xe pseudonimo, e par cui semo molto attenti. Open Library xe ancora gestì eroicamente da persone de Internet Archive, continuando l'eredità de Aaron. Torneremo su questo più avanti in questo post. Nel post del blog de Google, Taycher descrive alcuni dei problemi nel stimar questo numero. Prima de tuto, cosa costituisce un libro? Ghe xe alcune definizioni possibili: “Edizioni” sembra la definizione più pratica de cosa xe i “libri”. Comodamente, questa definizione xe anche usata par assegnar numeri ISBN unici. Un ISBN, o International Standard Book Number, xe comunemente usato par il commercio internazionale, visto che xe integrato col sistema internazionale de codici a barre ("International Article Number"). Se vuoi vender un libro nei negozi, el ga bisogno de un codice a barre, quindi te ottieni un ISBN. El post del blog de Taycher menziona che mentre i ISBN xe utili, no xe universali, visto che xe stati veramente adottati solo a metà degli anni settanta, e no dappertutto nel mondo. Tuttavia, l'ISBN xe probabilmente l'identificatore più ampiamente usato delle edizioni dei libri, quindi xe il nostro miglior punto de partenza. Se riusciamo a trovar tuti i ISBN del mondo, otteniamo una lista utile de quali libri ancora bisogna preservare. Allora, dove otteniamo i dati? Ghe xe un numero de sforzi esistenti che sta cercando de compilare una lista de tuti i libri del mondo: In questo post, semo felici de annunciar un piccolo rilascio (rispetto ai nostri precedenti rilasci de Z-Library). Gavemo raschiato la maggior parte de ISBNdb, e reso i dati disponibili par il torrenting sul sito web del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>; no lo linkeremo direttamente qua, basta cercarlo). Questi xe circa 30.9 milioni de record (20GB come <a %(jsonlines)s>JSON Lines</a>; 4.4GB compressi). Sul loro sito web i dichiara che in realtà i ga 32.6 milioni de record, quindi potremmo aver perso alcuni, o <em>loro</em> potrebbe star facendo qualcosa de sbagliato. In ogni caso, par ora no condivideremo esattamente come gavemo fatto — lasceremo questo come un esercizio par il lettore. ;-) Quello che condivideremo xe un'analisi preliminare, par cercar de avvicinarci a stimar il numero de libri nel mondo. Gavemo guardato a tre dataset: questo nuovo dataset de ISBNdb, il nostro rilascio originale de metadata che gavemo raschiato dalla biblioteca ombra Z-Library (che include Library Genesis), e il dump de dati de Open Library. Dump di ISBNdb, o Quanti Libri Sono Preservati per Sempre? Se dovessimo deduplicare correttamente i file dalle biblioteche ombra, quale percentuale di tutti i libri del mondo abbiamo preservato? Aggiornamenti su <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, la più grande biblioteca veramente aperta nella storia umana. <em>Redesign de WorldCat</em> Dati <strong>Formato?</strong> <a %(blog)s>Contenitori de l'Archivio de Anna (AAC)</a>, che xe essenzialmente <a %(jsonlines)s>JSON Lines</a> compressi con <a %(zstd)s>Zstandard</a>, più qualche semantica standardizzata. Sti contenitori i avvolge vari tipi de record, basai sui diversi scrapes che gavemo deployado. Un ano fa, gavemo <a %(blog)s>inizià</a> a risponder a questa domanda: <strong>Che percentuale de libri xe stà preservà permanentemente da le biblioteche ombra?</strong> Vedemo un poco de informazion basica sui dati: Una volta che un libro xe finìo in una biblioteca ombra de dati aperti come <a %(wikipedia_library_genesis)s>Library Genesis</a>, e adesso <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, el vien speciado in tutto el mondo (attraverso i torrent), preservandolo praticamente par sempre. Par risponder alla domanda de che percentuale de libri xe stà preservà, gavemo bisogno de saver el denominatore: quanti libri esisti in totale? E idealmente no gavemo solo un numero, ma veri metadata. Poi podemo no solo confrontarli con le biblioteche ombra, ma anca <strong>crear una lista de libri rimanenti da preservar!</strong> Podessimo anca iniziar a sognar un sforzo collettivo par scorrer questa lista. Gavemo <a %(wikipedia_isbndb_com)s>ISBNdb</a> e gavemo i dati da <a %(openlibrary)s>Open Library dataset</a>, ma i risultati no iera sodisfacenti. El problema principal xera che no ghe jera tanto de sovrapposizion de ISBN. Varda sto diagramma de Venn dal <a %(blog)s>nostro post sul blog</a>: Gavemo stado molto sorpresi da quanto poca sovrapposizion ghe jera tra ISBNdb e Open Library, tuti e do che include liberalmente dati da varie fonti, come web scrapes e archivi de biblioteche. Se tuti e do i fa un bon lavoro a trovar la maggior parte dei ISBN là fora, i so cerchi dovaria aver una sostanziale sovrapposizion, o un saria un sottoinsieme de l'altro. Questo ne ga fato domandar, quanti libri i resta <em>completamente fora de sti cerchi</em>? Gavemo bisogno de un database più grande. Xe qua che gavemo puntado al più grande database de libri al mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Questo xe un database proprietario da la non-profit <a %(wikipedia_oclc)s>OCLC</a>, che aggrega i record de metadata da biblioteche in tuto el mondo, in cambio de dar a ste biblioteche accesso al dataset completo, e farle comparir nei risultati de ricerca dei utenti finali. Anca se OCLC xe una non-profit, el so modello de business richiede de protegger el so database. Beh, semo dispiasei de dirlo, amici de OCLC, che noi lo stemo dando via tuto. :-) Nel corso de l'ultimo ano, gavemo scrupolosamente raschiado tuti i record de WorldCat. Al inizio, gavemo avudo un colpo de fortuna. WorldCat stava giusto lanciando el so completo redesign del sito web (in agosto 2022). Questo includeva un sostanziale rinnovamento dei so sistemi backend, introducendo molti difetti de sicurezza. Gavemo subito colto l'opportunità, e semo stai in grado de raschiar centinaia de milioni (!) de record in pochi giorni. Dopo de che, i difetti de sicurezza i xe stai lentamente sistemai uno per uno, finché l'ultimo che gavemo trovado xe stado corretto circa un mese fa. A quel punto gavemo praticamente tuti i record, e stemo solo cercando de ottener record de qualità leggermente più alta. Cussì gavemo sentido che xe ora de rilasciar! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> L'Archivio de Anna ga raschià tutto WorldCat (la più grande collezion de metadata de biblioteche del mondo) par far una lista de libri che bisogna preservar.</em> WorldCat Avviso: sto post del blog xe stato deprecato. Gavemo deciso che IPFS no xe ancora pronto par el grande pubblico. Continueremo a linkar ai file su IPFS dall'Archivio de Anna quando xe possibile, ma no lo ospiteremo più noi stessi, né raccomandemo ad altri de farne il mirror usando IPFS. Consultè la nostra pagina dei Torrents se volè aiutar a preservare la nostra collezion. Aiuta a seedar Z-Library su IPFS Download dal server partner SciDB Prestito esterno Prestit esterno (stampa disabilitata) Download esterno Esplora i metadati Contenùo nei torrent Indrio  (+%(num)s bonus) non pagato pagato cancellà scadùo aspettando che Anna confermi nò valido El testo seguente l'è disponìbiłe soło in ingleze. Va Ripristina Avanti Ultimo Se la vostra email no funziona sui forum de Libgen, racomandemo de doparar <a %(a_mail)s>Proton Mail</a> (gratis). Podè anca <a %(a_manual)s>richieder manualmente</a> che el vostro conto vegna ativà. (potrebbe richiedere <a %(a_browser)s>verifica del browser</a> — download illimitati!) Server Partner Veloce #%(number)s (consiglià) (un po' più veloce ma con lista d'attesa) (no xe richiesta verifica del browser) (nissuna verifica del browser o liste d'attesa) (senza lista d'attesa, ma può essere molto lento) Server Partner Lento #%(number)s Audiolibro Fumeto Libro (romanzo) Libro (sazìstego) Libro (sconjosùo) Artìgoło sientìfego Revista Partitura musicale Altro Documento normativo No tutte le pagine le xe stà convertìe in PDF Senjà come roto su Libgen.li Nò vizibiłe su Libgen.li Nò vizibiłe su Libgen.rs Fiction Nò vizibiłe su Libgen.rs Non-Fiction Esecuzione di exiftool fallita su questo file Contrassegnato come “file errato” in Z-Library Manca da Z-Library Contrassegnato come “spam” in Z-Library El file no se pol vierzer (p.e. file coroto, DRM) Reclamo di copyright Problemi di download (es. impossibile connettersi, messaggio di errore, molto lento) Metadati incorreti (es. titolo, descrizion, immagine de copertina) Altro Pessima qualità (p.e. problemi de formatazion, scansion de bassa qualità, pagine mancanti) Spam / file da rimuover (es. pubblicità, contenuto offensivo) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) in totale Brillant Bibliofilo Fortunato Bibliotecario Splendido Accumulador de Dati Archivista Ammirabile Download bonus Cerlalc Metadati cechi DuXiu 读秀 Indice eBook EBSCOhost Google Books Goodreads HathiTrust IA Prestito Digitale Controllà da IA ISBNdb ISBN GRP Libgen.li Escludendo “scimag” Libgen.rs Saggistica e Narrativa Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Biblioteca Statale Russa Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Caricamenti su AA Z-Library Z-Library Cinese Tìtoło, autor, DOI, ISBN, MD5, … Sercar Autor Descrisión e comenti de ła metadata Edisión Nome del archivio orizinàl Editor (cerca campo specifico) Tìtoło An de publegasión Detałi tecneghi (in ingleze) Sta moneta la ga un minimo più alto del solito. Par piaser seleziona na durata diversa o na moneta diversa. La richiesta no la xe stada completada. Par piaser, prova de novo tra qualche minuto, e se el problema persiste contatene a %(email)s con na schermata. Xe capità un errore sconossuo. Contatene a %(email)s con na schermata. Errore nel processamento del pagamento. Per piaser aspetta un momento e prova de novo. Se el problema persiste per più de 24 ore, contatene a %(email)s con uno screenshot. Stemo facendo una raccolta fondi par <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">salvar</a> la più grande biblioteca ombra de fumetti del mondo. Grazie par el to sostegno! <a href="/donate">Dona.</a> Se no te poi donar, considera de sostenerne contandone ai to amici, e seguindone su <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>. No ne mandàr email par <a %(a_request)s>richieder libri</a><br>o par pìceni (<10k) <a %(a_upload)s>caricamenti</a>. Archivo de Anna Reclami DMCA / copyright Restar in contato Reddit Alternative SLUM (%(unaffiliated)s) no afilià L'Archivio de Anna ga bisogno del to aiuto! Se donè adesso, riceverè <strong>dopio</strong> el numero de download veloci. Tanti prova a butar ne zo, ma noi combatemo. Se doni sto mese, te ga <strong>doppio</strong> el numero de download veloci. Valido fin a la fine de sto mese. Salvare el sapere umano: un gran bel regalo de festa! I abbonamenti sarà estesi de conseguensa. I server partner no i xe disponibili parché i xe stai seradi. I dovarìa tornar su presto. Par aumentar la resilienza de l'Archivio de Anna, sercemo volontari par far girar specchi. Gavémo un novo metodo de donazion disponibile: %(method_name)s. Par piaser considera de %(donate_link_open_tag)sdonar</a> — no xe economico mantener sto sito, e la to donazion fa veramente la diferenza. Grazie mile. Recomanda un amico, e sia ti che el to amico riceverè %(percentage)s%% download veloci bonus! Sorprendi un caro, daghe un account con abbonamento. Il regalo perfetto per San Valentino! Sàbar de più… Account Atività Avanzato Blog de Anna ↗ Programe de Anna ↗ beta Esploratore de Codici Datasets Donar File descartegadi FAQ Home Migliora i metadata Dati LLM Accedi / Registrati Le me donazion Profilo pubblico Sercar Sicurezza Torrents Traduir ↗ Volontariato & Ricompense Download recenti: 📚&nbsp;La più grande biblioteca open-source e open-data del mondo. ⭐️&nbsp;Specchi di Sci-Hub, Library Genesis, Z-Library e altro. 📈&nbsp;%(book_any)s libri, %(journal_article)s articoli, %(book_comic)s fumetti, %(magazine)s riviste — conservati per sempre.  e  e altro ancora DuXiu Internet Archive Lending Library LibGen 📚&nbsp;La più grande biblioteca veramente aperta nella storia dell'umanità. 📈&nbsp;%(book_count)s&nbsp;libri, %(paper_count)s&nbsp;articoli — preservai par sempre. ⭐️&nbsp;Se speciemio %(libraries)s. Racogliemo e rendemo open-source %(scraped)s. Tutto il nostro codice e i nostri dati sono completamente open source. OpenLib Sci-Hub ,  📚 La più grande biblioteca open-source open-data del mondo.<br>⭐️ Rispecchia Scihub, Libgen, Zlib e altro ancora. Z-Lib Archivio de Anna Richiesta non valida. Visita %(websites)s. La più grande biblioteca open-source e open-data del mondo. Specchi de Sci-Hub, Library Genesis, Z-Library, e altro. Serca in l'Archivio de Anna L'Archivio de Anna Par piaser rinfresca par provàr de novo. <a %(a_contact)s>Contatene</a> se el problema persiste par più ore. 🔥 Problema a carègar sta pàgina <li>1. Seguitece su <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spargete la voce sull'Archivio de Anna su Twitter, Reddit, Tiktok, Instagram, al vostro caffè o biblioteca locale, o dovunque andè! No credemo nel gatekeeping — se ne butta giù, ne rimettemo su da un'altra parte, visto che tutto el nostro codice e dati xe completamente open source.</li><li>3. Se podè, considerate de <a href="/donate">donar</a>.</li><li>4. Aiutate a <a href="https://translate.annas-software.org/">tradurre</a> el nostro sito in diverse lingue.</li><li>5. Se sì un ingegnere del software, considera de contribuire al nostro <a href="https://annas-software.org/">open source</a>, o de fare seeding dei nostri <a href="/datasets">torrent</a>.</li> 10. Crea o aiuta a mantenere la pagina Wikipedia per l'Archivio de Anna nella tua lingua. 11. Stemo cercando de inserir piccole, eleganti pubblicità. Se te volè far pubblicità su l'Archivio de Anna, par piaser contatene. 6. Se te sì un ricercatore de sicurezza, podemo usar le to abilità sia par l'attacco che par la difesa. Varda la nostra pagina <a %(a_security)s>Sicurezza</a>. 7. Semo in serca de esperti in pagamenti par mercanti anonimi. Te podi aiutarne a zontar modi più comodi par donar? PayPal, WeChat, carte regalo. Se te conossi qualcun, par piaser contatene. 8. Semo sempre in cerca de più capacità de server. 9. Te pol dar na man segnaland problemi coi file, lassando commenti e creando liste direttamente su sto sito. Te pol anca dar na man <a %(a_upload)s>caricando altri libri</a>, o sistemando problemi coi file o formattazione dei libri esistenti. Par informazioni più estese su come far volontariato, varda la nostra pagina <a %(a_volunteering)s>Volontariato & Ricompense</a>. Credemo fermamente nteła lìbara sircołasiòn de informasiòn, e nteła conservasiòn de ła conjosansa e cultura. Co 'sto motor de reserca, noialtri costruimo su łe spałe dei ziganti. Respetamo fondamente 'l duro laoro de łe parsone che łe gà creà łe tante biblioteche-onbra, e speramo che 'sto motor de reserca el poda slargar ła so portada. Par restar azornà su nostro progresso, segui Anna su <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> o <a href="https://t.me/annasarchiveorg">Telegram</a>. Par domande e feedback, par piaser contata Anna su %(email)s. Account ID: %(account_id)s Esci ❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo. ✅ Te sì adesso fora. Ricarica la pagina par tornar a entrar. Download veloci doparà (ultime 24 ore): <strong>%(used)s / %(total)s</strong> Abonamento: <strong>%(tier_name)s</strong> fin a %(until_date)s <a %(a_extend)s>(estendi)</a> Te pol combinar più abbonamenti (i download veloci per 24 ore i sarà sommà). Abonamento: <strong>Nissun</strong> <a %(a_become)s>(diventa un membro)</a> Contata Anna a %(email)s se te sì interesà a migliorar el to abbonamento a un livèl più alto. Profilo pubblico: %(profile_link)s Chiave segreta (non condividere!): %(secret_key)s mostra Zugniteve qua! Fae un <a %(a_tier)s>livèl più alto</a> par zugnir al nostro gruppo. Gruppo esclusivo su Telegram: %(link)s Conto quali download? Intra No perder el to codice! Codice segreto no valido. Verifica el to codice e prova ancora, o in alternativa registrite un novo conto qua soto. Chiave segreta Inserissi el to codice segreto par entrar: Vecio conto basà su email? Inserissi <a %(a_open)s>l'email qua</a>. Registra un novo conto No te ghè ancora un conto? Registrazion riuscida! El to codice segreto xe: <span %(span_key)s>%(key)s</span> Tegni ben sto codice. Se lo perdi, perderai l'accesso al to conto. <li %(li_item)s><strong>Segnalibro.</strong> Ti pol segnar sta pagina par recuperar el to codice.</li><li %(li_item)s><strong>Scarica.</strong> Clicca <a %(a_download)s>sto link</a> par scaricar el to codice.</li><li %(li_item)s><strong>Gestor de password.</strong> Dòpara un gestor de password par salvar el codice quando lo inserissi qua soto.</li> Intra / Registra Verìfica del browser Atension: el còdighe el gà carateri Unicode incorèti, e podarìa comportarse in maniere incorète in varie situazioni. El binario bruto el pol èssar decodificà dal rappresentazion base64 nel URL. Descrizion Eticheta Prefiso URL par un còdighe specifico Sito web Còdighi che inizia con “%(prefix_label)s” Si prega di non fare scraping di queste pagine. Invece, raccomandiamo di <a %(a_import)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB, e di eseguire il nostro <a %(a_software)s>codice open source</a>. I dati grezzi possono essere esplorati manualmente attraverso file JSON come <a %(a_json_file)s>questo</a>. Manco de %(count)s record URL generico Esploratore di Codici Indice de Esplora i codici con cui i record sono taggati, per prefisso. La colonna “record” mostra il numero di record taggati con codici con il prefisso dato, come visto nel motore di ricerca (inclusi i record solo metadati). La colonna “codici” mostra quanti codici effettivi hanno un dato prefisso. Prefiso de còdighe conossù “%(key)s” Più… Prefiso %(count)s record che corisponde a “%(prefix_label)s” page.codes.records_starting_with còdighi record “%%s” sarà sostituìo col valor del còdighe Serca l'Archivio de Anna Codici URL par còdighe specifico: “%(url)s” Questa pagina può richiedere un po' di tempo per essere generata, motivo per cui richiede un captcha di Cloudflare. <a %(a_donate)s>I membri</a> possono saltare il captcha. Abuso segnalà: Version mejo Voleu segnalar sto utente par comportamento abusivo o inadeguà? Problema col file: %(file_issue)s commento nascosto Rispondi Segnala abuso Gavì segnalà sto utente par abuso. Le richieste de copyright a sta email sarà ignorà; doparè el modulo invece. Mostra email Accogliamo con molto piacere i vostri feedback e domande! Comunque, par via del spam e delle email senza senso che ricevémo, par piaser spunta le casèle par confermàr che te capissi ste condizion par contatàrne. Qualsiasi altro modo di contattarci riguardo ai reclami sul copyright sarà automaticamente eliminato. Per reclami DMCA / sul copyright, usa <a %(a_copyright)s>questo modulo</a>. Email de contati URL su l’Archivio de Anna (obligatorio). Un par riga. Par piaser includi solo URL che descrive la stessa edizion esata de un libro. Se volé far un reclamo par più libri o più edizioni, par piaser invia sto modulo più volte. I reclami che raggrupa più libri o edizioni insieme sarà rifiutài. Indirizo (obligatorio) Descrizion chiara del materiale originale (obligatorio) E-mail (obligatorio) URL del materiale originale, un par riga (obligatorio). Par piaser includi quanti più possibile, par aiutarne a verificar el to reclamo (p.e. Amazon, WorldCat, Google Books, DOI). ISBN del materiale originale (se aplicabile). Un par riga. Par piaser includi solo quei che corisponde esatamente all’edizion par cui stai segnala un reclamo de copyright. El to nome (obligatorio) ❌ Qualcosa xe andà storto. Par piaser ricarica la pagina e prova de novo. ✅ Grazie par aver invià el to reclamo de copyright. Lo revisarémo il prima possibile. Par piaser ricarica la pagina par inviarne un altro. <a %(a_openlib)s>Open Library</a> URL del materiale originale, un par riga. Par piaser taca un momento par cercar el to materiale originale su Open Library. Questo ne aiuterà a verificar el to reclamo. Numero de telefono (obligatorio) Dichiarazion e firma (obligatorio) Invia reclamo Se avete un reclamo de copyright o DMCA, par piaser compilà sto modulo nel modo più preciso possibile. Se gavé problemi, contatene al nostro indirizo dedicà al DMCA: %(email)s. Nota che i reclami mandài a sto indirizo no sarà processài, xe solo par domande. Par piaser doparà el modulo qua soto par inviar i vostri reclami. Modulo de reclamo de copyright / DMCA Esempio de record su l'Archivio de Anna Torrent de l'Archivio de Anna Formato Contenitori de l'Archivio de Anna Script per importar i metadata Se te sì interesà a speciar sto dataset par <a %(a_archival)s>archiviazion</a> o par <a %(a_llm)s>addestramento de LLM</a>, contatene. Ultimo aggiornamento: %(date)s Sito principale %(source)s Documentazion dei metadata (la maggior parte dei campi) File specchiati da l'Archivio de Anna: %(count)s (%(percent)s%%) Risorse File totali: %(count)s Dimension totale dei file: %(size)s El nostro post del blog su sti dati <a %(duxiu_link)s>Duxiu</a> xe un database massivo de libri scanà, creato dal <a %(superstar_link)s>SuperStar Digital Library Group</a>. La maggior parte xe libri accademici, scanà par renderli disponibili digitalmente a università e biblioteche. Par el nostro pubblico che parla inglese, <a %(princeton_link)s>Princeton</a> e l'<a %(uw_link)s>Università de Washington</a> ga boni panorami. Ghe xe anca un articolo eccellente che dà più contesto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. I libri de Duxiu xe stati piratà da tanto tempo su internet cinese. De solito i xe vendui par meno de un dolar dai rivenditori. I xe tipicamente distribuiti usando l'equivalente cinese de Google Drive, che spesso xe stato hackerà par permettere più spazio de archiviazion. Alcuni dettagli tecnici se pol trovar <a %(link1)s>qua</a> e <a %(link2)s>qua</a>. Anca se i libri xe stati distribuiti semi-pubblicamente, xe abbastanza dificile ottenerli in massa. Gavemo messo questo alto nella nostra lista de cose da far, e gavemo dedicà mesi de lavoro a tempo pieno par questo. Comunque, a fine 2023 un volontario incredibile, straordinario e talentuoso ne ga contatà, dicendone che el ga già fatto tuto sto lavoro — a gran costo. El ne ga condiviso la collezion completa, senza aspettarse niente in cambio, a parte la garanzia de preservazion a lungo termine. Veramente rimarchevole. Più informazioni dai nostri volontari (note grezze): Adattà dal nostro <a %(a_href)s>post del blog</a>. DuXiu 读秀 %(count)s file page.datasets.files Sto dataset xe strettamente relazionà al <a %(a_datasets_openlib)s>dataset de Open Library</a>. El contien una raccolta de tuta la metadata e una gran parte de file da la Biblioteca de Prestito Digitale Controllà de l’IA. Gli aggiornamenti vien rilasciài nel <a %(a_aac)s>formato dei Contenitori de l’Archivio de Anna</a>. Questi record vien riferì direttamente dal dataset de Open Library, ma contien anca record che no xe in Open Library. Gavémo anca un numero de file de dati raccolti dai membri de la comunità nel corso dei ani. La collezion xe composta da do parti. Te ghé bisogno de tuti e do le parti par aver tuti i dati (ecceto i torrent superati, che xe segnài con una riga sui la pagina dei torrent). Biblioteca de Prestito Digitale la nostra prima versione, prima de standardizar sul formato <a %(a_aac)s>Contenitori de l'Archivio de Anna (AAC)</a>. Contien metadata (come json e xml), pdf (da i sistemi de prestito digital acsm e lcpdf), e miniature de copertina. versioni nuove incrementali, usando AAC. Contien solo metadata con timestamp dopo el 2023-01-01, visto che el resto xe già coperto da "ia". Anca tuti i file pdf, sta volta dai sistemi de prestito acsm e "bookreader" (el lettore web de IA). Nonostante el nome no sia proprio giusto, continuemo a inserir i file bookreader nella collezion ia2_acsmpdf_files, visto che i xe mutualmente esclusivi. IA Controlled Digital Lending 98%%+ dei file xe ricercabili. La nostra missione è archiviare tutti i libri del mondo (così come articoli, riviste, ecc.) e renderli ampiamente accessibili. Crediamo che tutti i libri dovrebbero essere replicati ampiamente, per garantire ridondanza e resilienza. Per questo stiamo raccogliendo file da una varietà di fonti. Alcune fonti sono completamente aperte e possono essere replicate in massa (come Sci-Hub). Altre sono chiuse e protettive, quindi cerchiamo di raschiarle per “liberare” i loro libri. Altre ancora si trovano in una via di mezzo. Tutti i nostri dati possono essere <a %(a_torrents)s>scaricati via torrent</a>, e tutti i nostri metadati possono essere <a %(a_anna_software)s>generati</a> o <a %(a_elasticsearch)s>scaricati</a> come database ElasticSearch e MariaDB. I dati grezzi possono essere esplorati manualmente attraverso file JSON come <a %(a_dbrecord)s>questo</a>. Metadati Sito web ISBN Ultimo aggiornamento: %(isbn_country_date)s (%(link)s) Risorse L'Agenzia Internazionale dell'ISBN rilascia regolarmente le gamme che ha assegnato alle agenzie nazionali dell'ISBN. Da questo possiamo derivare a che paese, regione o gruppo linguistico appartiene questo ISBN. Attualmente usiamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>. Informazioni sui paesi dell'ISBN Sto qua xe un dump de un sacco de chiamate a isbndb.com durante settembre 2022. Gavemo provà a coprir tute le gamme de ISBN. Xe circa 30,9 milioni de record. Sul loro sito i dichiara che in realtà i ga 32,6 milioni de record, quindi podemo aver perso qualcossa, o <em>i</em> podaria far qualcossa de sbaglià. Le risposte JSON xe praticamente grezze dal loro server. Un problema de qualità dei dati che gavemo notà, xe che par i numeri ISBN-13 che inizia con un prefisso diverso da “978-”, i include ancora un campo “isbn” che xe semplicemente el numero ISBN-13 con i primi 3 numeri taglià via (e el digito de controllo ricalcolà). Questo xe ovviamente sbaglià, ma xe cussì che i sembra farlo, quindi no gavemo alterà niente. Un altro problema potenziale che podessi incontrar, xe el fatto che el campo “isbn13” ga dei duplicati, quindi no podessi dopararlo come chiave primaria in un database. I campi “isbn13”+“isbn” combinà sembra che sia unici. Rilascio 1 (2022-10-31) I torrents de narrativa xe indrio (anca se i ID ~4-6M no i xe stai torrentià parchè i se sovrappone co i nostri torrents de Zlib). El nostro post del blog su la pubblicazion dei fumeti Torrenti de fumeti su l'Archivio de Anna Par la storia dei diversi fork de Library Genesis, varda la pagina de <a %(a_libgen_rs)s>Libgen.rs</a>. El Libgen.li contiene la maggior parte del stesso contenuto e metadati del Libgen.rs, ma ga alcune collezioni in più, cioè fumetti, riviste e documenti standard. Ga anca integrà <a %(a_scihub)s>Sci-Hub</a> nei suoi metadati e motore de ricerca, che xe quello che doparamo par la nostra base de dati. I metadati de sta biblioteca xe liberamente disponibili <a %(a_libgen_li)s>su libgen.li</a>. Comunque, sto server xe lento e no supporta la ripresa delle connessioni interrotte. Gli stessi file xe anca disponibili su <a %(a_ftp)s>un server FTP</a>, che funziona mejo. La saggistica par che se sia diversificada, ma senza novi torrenti. Par che questo sia successo da inizio 2022, anca se no gavemo verificà. Segondo l'amministrador de Libgen.li, la colecion “fiction_rus” (narrativa russa) la dovarìa eser coperta da torrent rilascià regularmente da <a %(a_booktracker)s>booktracker.org</a>, in particolare i torrent de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (che noi i speciem <a %(a_torrents)s>qua</a>, anca se no gavemo ancora stabilì quale torrent i corisponde a quale file). La colecion de narrativa la ga i so torrent (diversi da <a %(a_href)s>Libgen.rs</a>) che i parte da %(start)s. Certi range senza torrent (come i range de narrativa f_3463000 a f_4260000) i xe probabilemente file de Z-Library (o altri duplicati), anca se podarìmo voler far un po' de deduplicazion e crear torrent par i file unici de lgli in sti range. Le statistighe par tute le colecion le se pol trovar <a %(a_href)s>sul sito de libgen</a>. I torrent xe disponibili par la maggior parte del contesto addizionale, in particolare i torrent par i fumeti, le riviste e i documenti standard i xe stà rilascià in colaborazion con l'Archivio de Anna. Nota che i file torrent che se riferisse a “libgen.is” xe esplicitamente specchi de <a %(a_libgen)s>Libgen.rs</a> (“.is” xe un dominio diverso doparà da Libgen.rs). Una risorsa utile par doparar i metadati xe <a %(a_href)s>sta pagina</a>. %(icon)s La so colecion “fiction_rus” (narrativa russa) no la ga torrent dedicà, ma la xe coperta da torrent de altri, e noi tenimo un <a %(fiction_rus)s>specio</a>. Torrent de narrativa russa su l'Archivio de Anna Torrenti de narrativa su l'Archivio de Anna Forum de discussione Metadati Metadati via FTP Torrenti de riviste su l'Archivio de Anna Informazioni sui campi dei metadati Specio de altri torrenti (e torrenti unici de narrativa e fumeti) Torrent de documenti standard su l'Archivio de Anna Libgen.li Torrents de l'Archivio de Anna (copertine de libri) Library Genesis xe conosudo par far già generosamente disponibile i so dati in massa tramite torrents. La nostra collezion de Libgen consiste in dati ausiliari che lori no rilassa direttamente, in collaborazione con lori. Un gran ringraziamento a tuti i coinvolti con Library Genesis par lavorar con noi! El nostro blog su la pubblicazion de le copertine de libri Sta pagina xe su la versione “.rs”. La xe conosciuta per pubblicar costantemente sia i so metadati che el contenuto completo del so catalogo de libri. La so collezion de libri xe divisa tra una parte de narrativa e una de non narrativa. Una risorsa utile per doparar i metadati xe <a %(a_metadata)s>sta pagina</a> (blocca i range IP, podessi che te servi un VPN). A partire dal 2024-03, novi torrenti xe postai in <a %(a_href)s>sto thread del forum</a> (blocca i range IP, podessi che te servi un VPN). Torrenti de narrativa su l'Archivio de Anna Torrents de narrativa de Libgen.rs Forum de discussione de Libgen.rs Metadati de Libgen.rs Informazion sui campi de metadati de Libgen.rs Torrents de saggistica de Libgen.rs Torrenti de non narrativa su l'Archivio de Anna %(example)s par un libro de narrativa. Sta <a %(blog_post)s>prima pubblicazion</a> xe abbastanza picola: circa 300GB de copertine de libri dal fork de Libgen.rs, sia narrativa che saggistica. I xe organizadi nello stesso modo in cui i compare su libgen.rs, ad esempio: %(example)s par un libro de saggistica. Come con la collezion de Z-Library, i gavemo messo tuti in un grande file .tar, che se pol montar usando <a %(a_ratarmount)s>ratarmount</a> se te vol servir i file direttamente. Pubblicazion 1 (%(date)s) La storia rapida dei diversi fork de Library Genesis (o “Libgen”) xe che col tempo, le diverse persone coinvolte con Library Genesis ga avuto dei disaccordi e xe andae per la so strada. Secondo sto <a %(a_mhut)s>post del forum</a>, Libgen.li xe sta originalmente ospitato su “http://free-books.dontexist.com”. La versione “.fun” xe sta creà dal fondador originale. La xe in fase de ristrutturazion in favor de una nova versione più distribuita. La <a %(a_li)s>versione “.li”</a> ga una collezion massiccia de fumeti, cussì come altri contenuti, che no xe (ancora) disponibili per el download in blocco tramite torrenti. La ga una collezion separata de torrenti de libri de narrativa e contiene i metadati de <a %(a_scihub)s>Sci-Hub</a> nel so database. La versione “.rs” ga dati molto simili e rilascia costantemente la so collezion in torrenti in blocco. La xe divisa grosso modo in una sezione de “narrativa” e una de “non narrativa”. Originalmente su “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> in qualche senso xe anca un fork de Library Genesis, anca se ga doparà un nome diverso per el so progetto. Libgen.rs Arricchiamo anche la nostra collezione con sorgenti solo metadati, che possiamo abbinare ai file, ad esempio utilizzando numeri ISBN o altri campi. Di seguito è riportata una panoramica di queste. Ancora una volta, alcune di queste sorgenti sono completamente aperte, mentre per altre dobbiamo raschiarle. Nota che nella ricerca di metadati, mostriamo i record originali. Non facciamo alcuna fusione di record. Sorgenti solo metadati Open Library xe un progetto open source de l'Internet Archive par catalogar ogni libro nel mondo. El ga una de le operazioni de scansione de libri più grandi del mondo, e ga tanti libri disponibili par il prestito digitale. El so catalogo de metadati de libri xe liberamente disponibile par el download, e xe incluso su l'Archivio de Anna (anche se attualmente no xe ricercabile, a meno che no te cerchi esplicitamente un ID de Open Library). Open Library Escludendo i duplicati Ultimo aggiornamento Percentuali del numero de file %% speciaài da AA / torrents disponibili Dimension Sorgente Di seguito una rapida panoramica delle fonti dei file su Anna’s Archive. Dado che le biblioteche ombra spesso sincronizza i dati tra de lori, ghe xe un notevole sovrapposizion tra le biblioteche. Par questo i numeri no i somma al totale. La percentuale “speciaài e seminà da l’Archivio de Anna” mostra quanti file speciaemo noi stessi. Seminemo sti file in massa tramite torrents, e i xe disponibili par download diretto tramite siti partner. Panoramica Totale Torrent su l'Archivio de Anna Par un background su Sci-Hub, par piaser consulta el so <a %(a_scihub)s>sito ufficiale</a>, la <a %(a_wikipedia)s>pagina Wikipedia</a>, e sta <a %(a_radiolab)s>intervista in podcast</a>. Nota che Sci-Hub xe stà <a %(a_reddit)s>congelà dal 2021</a>. El xe stà congelà prima, ma nel 2021 xe stà aggiunti qualche milion de articoli. Comunque, un numero limità de articoli vien ancora aggiunto alle collezioni “scimag” de Libgen, anche se no abbastanza par giustificar nuovi torrents in massa. Usiamo i metadati de Sci-Hub come fornidi da <a %(a_libgen_li)s>Libgen.li</a> nella so collezion “scimag”. Usiamo anche el dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Nota che i torrent “smarch” sono <a %(a_smarch)s>deprecati</a> e quindi non inclusi nella nostra lista di torrent. Torrent su Libgen.li Torrent su Libgen.rs Metadata e torrent Aggiornamenti su Reddit Intervista podcast Pagina Wikipedia Sci-Hub Sci-Hub: bloccà da 2021; la maggior parte disponibile tramite torrents Libgen.li: piccole aggiunte da allora</div> Alcune biblioteche sorgente promuovono la condivisione massiva dei loro dati tramite torrent, mentre altre non condividono facilmente la loro collezione. In quest'ultimo caso, l'Archivio de Anna cerca di raschiare le loro collezioni e renderle disponibili (vedi la nostra pagina <a %(a_torrents)s>Torrents</a>). Ci sono anche situazioni intermedie, ad esempio, dove le biblioteche sorgente sono disposte a condividere, ma non hanno le risorse per farlo. In questi casi, cerchiamo anche di aiutare. Di seguito è riportata una panoramica di come interagiamo con le diverse biblioteche sorgente. Biblioteche de origine %(icon)s Vari archivi de file sparsi par l'internet cinese; spesso archivi a pagamento. %(icon)s La maggior parte dei file xe accessibili solo con conti premium BaiduYun; velocità de scaricamento lente. %(icon)s L'Archivio de Anna gestisse na collezion de <a %(duxiu)s>file DuXiu</a> %(icon)s Vari database de metadata sparsi su l'internet cinese; anche se spesso database a pagamento %(icon)s No ghe xe dump de metadata facilmente accessibili per tutta la loro collezion. %(icon)s L'Archivio de Anna gestisse na coleçion de <a %(duxiu)s>metadata DuXiu</a> File %(icon)s File disponibili solo per prestito in modo limità, con varie restrizioni d'accesso %(icon)s L'Archivio de Anna gestisce una collezion de <a %(ia)s>file IA</a> %(icon)s Qualche metadata xe disponibile tramite <a %(openlib)s>Open Library database dumps</a>, ma no copre tuto el coleçion de IA. %(icon)s No ghe xe dump de metadata facilmente accessibili par la so intera collezion %(icon)s L'Archivio de Anna gestisse na coleçion de <a %(ia)s>metadata IA</a> Ultimo aggiornamento %(icon)s L'Archivio de Anna e Libgen.li i gestisse in colaborazion colecion de <a %(comics)s>fumeti</a>, <a %(magazines)s>riviste</a>, <a %(standarts)s>documenti standard</a>, e <a %(fiction)s>narrativa (diversa da Libgen.rs)</a>. %(icon)s I torrenti de Non-Fiction i xe condivisi con Libgen.rs (e specchiati <a %(libgenli)s>qua</a>). %(icon)s Dump del database <a %(dbdumps)s>HTTP trimestrali</a> %(icon)s Torrenti automatizzati per <a %(nonfiction)s>Non-Fiction</a> e <a %(fiction)s>Fiction</a> %(icon)s L'Archivio de Anna gestisse na collezion de <a %(covers)s>torrent de copertine de libri</a> %(icon)s Dump del database <a %(dbdumps)s>HTTP giornalieri</a> Metadati %(icon)s <a %(dbdumps)s>Database dumps</a> mensili %(icon)s Torrent de dati disponibili <a %(scihub1)s>qua</a>, <a %(scihub2)s>qua</a>, e <a %(libgenli)s>qua</a> %(icon)s Qualche file novo el xe <a %(libgenrs)s>stà</a> <a %(libgenli)s>zonto</a> a “scimag” de Libgen, ma no abbastanza par giustificar novi torrent %(icon)s Sci-Hub gà fermà i file novi dal 2021. %(icon)s Dump de metadata disponibili <a %(scihub1)s>qua</a> e <a %(scihub2)s>qua</a>, come parte del <a %(libgenli)s>database de Libgen.li</a> (che usémo) Sorgente %(icon)s Vari fonti più piccole o occasionali. Incoraggiamo le persone a caricar prima in altre biblioteche ombra, ma a volte le persone ga collezioni che xe tropo grandi par altri da gestir, ma no abbastanza grandi par meritar na categoria propria. %(icon)s No disponibile direttamente in massa, protetto contro lo scraping %(icon)s L'Archivio de Anna gestisse na coleçion de <a %(worldcat)s>metadata OCLC (WorldCat)</a> %(icon)s L'Archivio de Anna e Z-Library gestisse in collaborazione na collezion de <a %(metadata)s>metadata de Z-Library</a> e <a %(files)s>file de Z-Library</a> Datasets Combiniamo tutte le sorgenti sopra in un unico database unificato che utilizziamo per servire questo sito web. Questo database unificato non è disponibile direttamente, ma poiché l'Archivio de Anna è completamente open source, può essere abbastanza facilmente <a %(a_generated)s>generato</a> o <a %(a_downloaded)s>scaricato</a> come database ElasticSearch e MariaDB. Gli script su quella pagina scaricheranno automaticamente tutti i metadati necessari dalle sorgenti menzionate sopra. Se desideri esplorare i nostri dati prima di eseguire quegli script localmente, puoi guardare i nostri file JSON, che collegano ulteriormente ad altri file JSON. <a %(a_json)s>Questo file</a> è un buon punto di partenza. Database unificato Torrent da L'Archivio de Anna sfoglia cerca Vari fonti più piccole o occasionali. Incoraggiamo le persone a caricar prima in altre biblioteche ombra, ma a volte le persone ga collezioni che xe tropo grandi par altri da gestir, ma no abbastanza grandi par meritar na categoria propria. Panoramica da <a %(a1)s>pàxene dei datasets</a>. Da <a %(a_href)s>aaaaarg.fail</a>. Sembra abbastanza completo. Da el nostro volontario “cgiym”. Da un torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Gà un'alta sovrapposizion con le collezioni de articoli esistenti, ma pochi corrispondenze MD5, cusì gavemo deciso de tenerlo completamente. Racolta de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), da volontario <q>j</q>. Corrisponde a <q>airitibooks</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>. Da na colession <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. In parte da la fonte originale, in parte da the-eye.eu, in parte da altri specchi. Da un sito torrent privato de libri, <a %(a_href)s>Bibliotik</a> (spesso referito come “Bib”), de cui i libri xe stati raggruppati in torrent par nome (A.torrent, B.torrent) e distribuiti tramite the-eye.eu. Da el nostro volontario “bpb9v”. Par più informazion su <a %(a_href)s>CADAL</a>, vedi le note nella nostra <a %(a_duxiu)s>pagina del dataset DuXiu</a>. Più da el nostro volontario “bpb9v”, principalmente file DuXiu, come anca un cartela “WenQu” e “SuperStar_Journals” (SuperStar xe la compagnia dietro DuXiu). Da el nostro volontario “cgiym”, testi cinesi da varie fonti (rappresentà come sottodirectory), inclusi da <a %(a_href)s>China Machine Press</a> (un importante editore cinese). Coleçion non-cinesi (rappresentà come sottodirectory) da el nostro volontario “cgiym”. Racolta de libri su l'architettura cinese, da volontario <q>cm</q>: <q>Gavìo da un sfruttamento de vulnerabilità de rete alla casa editrice, ma quel varco xe stà serà</q>. Corrisponde a <q>chinese_architecture</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>. Libri da casa editrice accademica <a %(a_href)s>De Gruyter</a>, raccolti da alcuni grandi torrent. Scrape de <a %(a_href)s>docer.pl</a>, un sito polacco de condivision de file focalizà sui libri e altre opere scritte. Scraped in tardo 2023 da el volontario “p”. No gavemo boni metadata dal sito originale (neanche le estensioni dei file), ma gavemo filtrà par file simili a libri e spesso semo riuscì a estrar metadata dai file stessi. DuXiu epubs, direttamente da DuXiu, raccolti dal volontario “w”. Solo i libri DuXiu recenti xe disponibili direttamente tramite ebook, quindi la maggior parte de questi deve esser recente. File DuXiu rimanenti dal volontario “m”, che no iera nel formato proprietario PDG de DuXiu (el dataset principale <a %(a_href)s>DuXiu</a>). Raccolti da tante fonti originali, purtroppo senza preservar quelle fonti nel filepath. <span></span> <span></span> <span></span> Racolta de libri erotici, da volontario <q>do no harm</q>. Corrisponde a <q>hentai</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>. <span></span> <span></span> Collezion raschiata da un editore giapponese de Manga dal volontario “t”. <a %(a_href)s>Archivi giudiziari selezionati de Longquan</a>, forniti dal volontario “c”. Scrape de <a %(a_href)s>magzdb.org</a>, un alleato de Library Genesis (xe linkà sulla homepage de libgen.rs) ma che no voleva fornire i so file direttamente. Ottenuto da el volontario “p” in tardo 2023. <span></span> Vari piccoli upload, troppo piccoli par esser na propria sotto-collezion, ma rappresentati come directory. Ebook da AvaxHome, un sito russo de condivision de file. Archivio de giornali e riviste. Corrisponde a <q>newsarch_magz</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>. Racolta de <a %(a1)s>Philosophy Documentation Center</a>. Collezion del volontario “o” che ga raccolto libri polacchi direttamente dai siti de rilascio originali (“scene”). Collezioni combinate de <a %(a_href)s>shuge.org</a> dai volontari “cgiym” e “woz9ts”. <span></span> <a %(a_href)s>“Biblioteca Imperiale de Trantor”</a> (nominata dopo la biblioteca fittizia), raschiata nel 2022 dal volontario “t”. <span></span> <span></span> <span></span> Sotto-sotto-collezioni (rappresentate come directory) dal volontario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (da <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, la mia piccola libreria — woz9ts: “Sto sito se concentra principalmente sulla condivisione de file ebook de alta qualità, alcuni dei quali xe impaginati dal proprietario stesso. El proprietario xe stato <a %(a_arrested)s>arrestato</a> nel 2019 e qualcuno ga fatto na collezion dei file che el ga condiviso.”). Restanti file DuXiu dal volontario “woz9ts”, che no iera nel formato proprietario PDG de DuXiu (ancora da convertir in PDF). La collezion "upload" xe divisa in sotto-collezioni più piccole, che xe indicate nei AACIDs e nei nomi dei torrent. Tutte le sotto-collezioni xe state prima deduplicate contro la collezion principale, anche se i file JSON "upload_records" contien ancora tante referenze ai file originali. I file non-libro xe stati anca rimossi dalla maggior parte delle sotto-collezioni, e tipicamente <em>no</em> xe notati nei JSON "upload_records". Le subcoleçion xe: Note Sotosubolession Tante sotto-collezioni stesse xe composte da sotto-sotto-collezioni (per esempio da fonti originali diverse), che xe rappresentate come directory nei campi "filepath". Caricamenti su l'Archivio de Anna Il nostro post sul blog riguardo questi dati <a %(a_worldcat)s>WorldCat</a> è un database proprietario della non-profit <a %(a_oclc)s>OCLC</a>, che aggrega record di metadata dalle biblioteche di tutto il mondo. È probabilmente la più grande collezione di metadata di biblioteche al mondo. In ottobre 2023 abbiamo <a %(a_scrape)s>rilasciato</a> una raccolta completa del database OCLC (WorldCat), nel <a %(a_aac)s>formato Contenitori dell'Archivio de Anna</a>. Otòbre 2023, rilascio iniziale: OCLC (WorldCat) Torrent da l'Archivio de Anna Esempio de record su l'Archivio de Anna (collezion originale) Esempio de record su l'Archivio de Anna (collezion “zlib3”) Torrenti da l'Archivio de Anna (metadata + contenuto) Post del blog su la Version 1 Post del blog su la Pubblicazion 2 Verso la fine del 2022, i presunti fondatori de Z-Library xe stai arrestai, e i domini xe stai sequestrai dalle autorità statunitensi. Da allora el sito ga lentamente ripreso a esser online. No se sa chi lo gestisse attualmente. Aggiornamento a febbraio 2023. Z-Library ga le so radici nella comunità de <a %(a_href)s>Library Genesis</a>, e inizialmente xe partìa co i so dati. Da allora, xe diventà molto più professionale, e ga na interfaccia molto più moderna. Per questo, xe in grado de ricever molte più donazioni, sia monetarie per continuar a migliorar el so sito, sia donazioni de libri novi. Ga accumulà na grande collezion in aggiunta a Library Genesis. La collezion xe composta da tre parti. Le pagine de descrizion originali per le prime do parti xe conservà qua sotto. Te gà bisogno de tutte e tre le parti per ottener tutti i dati (eccetto i torrent superati, che xe barrà sulla pagina dei torrent). %(title)s: la nostra prima pubblicazion. Questa xe stada la primissima pubblicazion de quel che allora se ciamava “Pirate Library Mirror” (“pilimi”). %(title)s: seconda versione, sta volta con tuti i file impacà in file .tar. %(title)s: nuove versioni incrementali, usando el formato <a %(a_href)s>Contenitori de l'Archivio de Anna (AAC)</a>, adesso rilassà in collaborazione con el team de Z-Library. El specchio iniziale xe stado ottenuto con gran fatica nel corso del 2021 e 2022. A questo punto xe un poco obsoleto: riflette lo stato della collezion a giugno 2021. Lo aggiorneremo in futuro. Al momento semo concentrà a far uscire questa prima pubblicazion. Dado che Library Genesis xe già conservà con torrent pubblici, e xe inclusa in Z-Library, gavemo fatto una deduplicazion di base contro Library Genesis a giugno 2022. Per questo gavemo usà gli hash MD5. Probabilmente ghe xe molto più contenuto duplicato nella libreria, come formati de file multipli co lo stesso libro. Questo xe difficile da rilevar con precision, quindi no lo femo. Dopo la deduplicazion, restemo con più de 2 milioni de file, per un totale de poco meno de 7TB. La collezion consiste de do parti: un dump MySQL “.sql.gz” del metadata, e i 72 file torrent de circa 50-100GB ciascun. El metadata contien i dati come riportà dal sito Z-Library (titolo, autore, descrizion, tipo de file), come anca la dimensione reale del file e el md5sum che gavemo osservà, visto che a volte questi no i coincide. Parè che ghe sia dei range de file per i quali el Z-Library stesso el ga metadata incorretti. Potaremo anca aver scaricà file incorretti in certi casi isolati, che cercheremo de rilevar e sistemar in futuro. I grandi file torrent contien el dato reale dei libri, con l'ID de Z-Library come nome del file. Le estensioni dei file le pol esser ricostruì usando el dump del metadata. La collezion xe un misto de contenuto de narrativa e non narrativa (no separà come in Library Genesis). La qualità la varia anca tanto. Sta prima versione xe adesso completamente disponibile. Nota che i file torrent i xe disponibili solo tramite el nostro specchio Tor. Pubblicazion 1 (%(date)s) Questo xe un singolo file torrent extra. No'l contien gnente de novo, ma el gà dentro dei dati che i pol ciapàr un poco de tempo da calcolar. Questo el fa comodo da gaver, visto che scaricar sto torrent el xe spesso più veloce che calcolarlo da zero. In particolare, el contien indici SQLite par i file tar, par l'uso con <a %(a_href)s>ratarmount</a>. Addendum de la versione 2 (%(date)s) Gavemo ottenuto tutti i libri che xe stai aggiunti a Z-Library tra el nostro ultimo specchio e agosto 2022. Gavemo anche rivisitato e raccolto alcuni libri che gavemo perso la prima volta. In totale, questa nova collezion xe circa 24TB. Anche sta collezion xe deduplicata contro Library Genesis, visto che ghe xe già torrent disponibili per quella collezion. I dati xe organizai in modo simile alla prima pubblicazion. Ghe xe un dump MySQL “.sql.gz” dei metadati, che include anche tutti i metadati della prima pubblicazion, superandola. Gavemo anche aggiunto alcune colonne nove: Gavemo menzionà questo l'ultima volta, ma solo per chiarir: “filename” e “md5” xe le proprietà reali del file, mentre “filename_reported” e “md5_reported” xe quel che gavemo raccolto da Z-Library. A volte sti do no xe d'accordo tra de lori, quindi gavemo incluso entrambi. Par sta versione, gavemo cambià la collazion a “utf8mb4_unicode_ci”, che dovarìa èsar compatibile co le versioni vecie de MySQL. I file de dati i xe simili a l'altra volta, anca se i xe molto più grandi. No gavemo proprio volù crear un mucchio de file torrent più picini. “pilimi-zlib2-0-14679999-extra.torrent” el contien tuti i file che gavemo perso ne l'ultima versione, mentre i altri torrent i xe tuti novi range de ID.  <strong>Aggiornamento %(date)s:</strong> Gavemo fato i nostri torrent massa grandi, causando problemi ai client torrent. Li gavemo rimossi e rilassà nuovi torrent. <strong>Aggiornamento %(date)s:</strong> Ghe xe ancora massa file, cusì i ghemo impacà in file tar e rilassà novi torrent. %(key)s: se sto file xe già in Library Genesis, sia nella collezion de non-fiction che de fiction (abbinato per md5). %(key)s: in quale torrent xe sto file. %(key)s: impostato quando no semo riusciti a scaricar el libro. Pubblicazion 2 (%(date)s) Rilasci Zlib (pagine de descrizion originali) Dominio Tor Sito principale Raccolta Z-Library La collezion “Cinese” in Z-Library par che sia la stessa de la nostra collezion DuXiu, ma con MD5 diversi. Escludemo sti file dai torrents par evitar duplicazion, ma i xe ancora visibili nel nostro indice de ricerca. Metadati Ottieni %(percentage)s%% download veloci bonus, perché sei stato referenziato dall'utente %(profile_link)s. Questo si applica all'intero periodo di abbonamento. Dona Unisse Selezionato fino a %(percentage)s%% sconti Alipay suporta carte de crédito/débito internazionali. Varda <a %(a_alipay)s>sta guida</a> par più informazion. Mandine carte regalo de Amazon.com doparà la to carta de credito/debito. Te poi comprar crypto doparà carte de credito/debito. WeChat (Weixin Pay) el suporta carte de credito/debito internazionali. Inte l'app de WeChat, va su “Me => Servizi => Portafoglio => Zonta una Carta”. Se no te vedi quelo, ativelo doparà “Me => Impostazioni => Generali => Strumenti => Weixin Pay => Ativa”. (usa quando mandi Ethereum da Coinbase) copiato! copia (importo minimo più basso) (avviso: importo minimo elevato) -%(percentage)s%% 12 mesi 1 mese 24 mesi 3 mesi 48 mesi 6 mesi 96 mesi Seleziona per quanto tempo desideri abbonarti. <div %(div_monthly_cost)s></div><div %(div_after)s>dopo <span %(span_discount)s></span> sconti</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% par 12 mesi par 1 mese par 24 mesi par 3 mesi par 48 mesi par 6 mesi par 96 mesi %(monthly_cost)s / mese contatene Server diretti <strong>SFTP</strong> Donazione a livello aziendale o scambio per nuove collezioni (ad es. nuove scansioni, datasets OCR). Accesso Esperto <strong>Accesso illimitato</strong> ad alta velocità <div %(div_question)s>Posso upgrade la me iscrizion o aver più iscrizioni?</div> <div %(div_question)s>Posso far una donazione senza diventare membro?</div> Certo. Accettemo donazioni de qualsiasi importo a sto indirizzo Monero (XMR): %(address)s. <div %(div_question)s>Cosa vol dir i range par mese?</div> Podè rivar al lato più baso de un range aplicando tuti i sconti, come sceglier un periodo più longo de un mese. <div %(div_question)s>I abbonamenti se rinnova automaticamente?</div> I abbonamenti <strong>no</strong> se rinnova automaticamente. Podè unirse par quanto tempo volè. <div %(div_question)s>Cosa fémone co le donazion?</div> 100%% xe par preservar e far accessibile la conoscenza e la cultura del mondo. Al momento, i soldi i va sopratuto par server, archiviazion e banda. Nissun scheo va ai membri del team personalmente. <div %(div_question)s>Podoi far na granda donasion?</div> Sto 'l sarìa fantàstego! Par donasión oltra cualche mejo de dołari, par piaser contatane diretamente a %(email)s. <div %(div_question)s>Ghèto altri mètodi de pagamento?</div> Atualmente no. Molte parsone no łe vołe che archivi come 'sto i ezista, łora ghemo da star cauti. Se te połi jutarne a impostar altri (pì convenienti) mètodi de pagamento in seguresa, par piaser contatane a %(email)s. FAQ Donazioni Ghe xe na <a %(a_donation)s>donazion esistente</a> in corso. Par piaser finissi o annulla sta donazion prima de farne na nova. <a %(a_all_donations)s>Varda tute le me donazion</a> Par donazioni superiori a $5000 contatene direttamente a %(email)s. Accettemo grandi donazioni da individui o istituzioni benestanti.  Sii consapevole che mentre i abbonamenti su sta pagina xe "par mese", i xe donazioni una tantum (no ricorrenti). Varda el <a %(faq)s>FAQ sulle donazioni</a>. L'Archivio de Anna xe un progetto senza fini de lucro, open-source e open-data. Donando e diventando un membro, ti supporti le nostre operazioni e lo sviluppo. A tuti i nostri membri: grazie de core par mantenerne in attività! ❤️ Par più informazioni, consulta el <a %(a_donate)s>FAQ delle Donazioni</a>. Par diventare un membro, par piaser <a %(a_login)s>Intra o Registra</a>. Grazie par el to sostegno! $%(cost)s / mese Se te ghè fato un sbajo durante el pagamento, no podemo far rimborsi, ma cercheremo de sistemar la situazion. Trova la pagina “Crypto” nella tua app o sito PayPal. Solitamente xe sotto “Finanze”. Và alla pagina “Bitcoin” nella tua app o sito PayPal. Schiaccia el boton “Trasferisci” %(transfer_icon)s, e dopo “Invia”. Alipay Alipay 支付宝 / WeChat 微信 Carta Regalo Amazon %(amazon)s carta regalo Carta bancaria Carta bancaria (usando app) Binance Carta de crédito/débito/Apple/Google (BMC) Cash App Carta di credito/debito Carta de crédito/débito 2 Carta di credito/debito (backup) Crypto %(bitcoin_icon)s Carta / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regolar) Pix (Brazil) Revolut (temporaneamente indisponibile) WeChat Scolé la tua criptovaluta preferida: Dona usando na carta regalo Amazon. <strong>IMPORTANTE:</strong> Sta opzion la xe par %(amazon)s. Se te vol doparar un altro sito de Amazon, selezionalo qua sora. <strong>IMPORTANTE:</strong> Supportemo solo Amazon.com, no altri siti Amazon. Par esempio, .de, .co.uk, .ca, NO i xe supportai. Par piaser NO scriver un messaggio tuo. Inserisci l'importo esatto: %(amount)s Nota che gavemo bisogno de arrotondar ai importi accettai dai nostri rivenditori (minimo %(minimum)s). Dona usando na carta de credito/debito, tramite l'app Alipay (facilissimo da configurar). Instala l'app Alipay dal <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>. Registrate usando el to numero de telefono. No xe richiesti altri dettagli personali. <span %(style)s>1</span>Instala l'app Alipay Supportà: Visa, MasterCard, JCB, Diners Club e Discover. Varda <a %(a_alipay)s>sta guida</a> par più informazioni. <span %(style)s>2</span>Agiongi carta bancaria Con Binance, acquisti Bitcoin con una carta di credito/debito o un conto bancario, e poi doni quel Bitcoin a noi. In questo modo possiamo rimanere sicuri e anonimi quando accettiamo la tua donazione. Binance xe disponibile in quasi ogni paese, e supporta la maggior parte delle banche e delle carte di credito/debito. Questa xe attualmente la nostra raccomandazion principale. Ringraziemose de averse preso el tempo de imparar come donar usando sto metodo, visto che ne aiuta tanto. Per le carte de credito, carte de debito, Apple Pay, e Google Pay, usémo “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Nel loro sistema, un “caffè” xe uguale a $5, quindi la tua donazion sarà arrotondata al multiplo più vicino de 5. Dona utilizzando Cash App. Se hai Cash App, questo è il modo più semplice per donare! Nota che per transazioni inferiori a %(amount)s, Cash App potrebbe addebitare una commissione di %(fee)s. Per %(amount)s o superiori, è gratuito! Dona con carta de credito o debito. Sto metodo usa un fornitore de criptovalute come conversion intermedia. Questo pol esser un poco confusionario, quindi per piaser usa sto metodo solo se gli altri metodi de pagamento no funsiona. No funsiona in tutti i paesi. No podemo suportar carte de crédito/débito diretamente, parchè le banche no vol lavorar con nualtri. ☹ Comunque, ghe xe diverse maniere de usar le carte de crédito/débito lo stesso, usando altri metodi de pagamento: Con le criptovalute puoi donare usando BTC, ETH, XMR e SOL. Usa questa opzione se sei già familiare con le criptovalute. Co le criptovalute ti pol donare usando BTC, ETH, XMR, e altro. Servizi express crypto Se te stè doparà cripto par la prima volta, te sugierimo de doparar %(options)s par comprar e donar Bitcoin (la criptovaluta original e più doparada). Nota che per piccole donazioni le commissioni della carta di credito potrebbero eliminare il nostro sconto %(discount)s%%, quindi consigliamo abbonamenti più lunghi. Dona usando carta de credito/debito, PayPal, o Venmo. Te pol scoler tra questi nella pagina successiva. Google Pay e Apple Pay potrebbero funzionare anche. Nota che par le piccole donazioni le commissioni xe alte, quindi raccomandemo abbonamenti più lunghi. Per donare utilizzando PayPal US, useremo PayPal Crypto, che ci permette di rimanere anonimi. Apprezziamo che tu prenda il tempo per imparare a donare utilizzando questo metodo, poiché ci aiuta molto. Dona usando PayPal. Dona usando el to conto PayPal regular. Dona usando Revolut. Se ti gà Revolut, questa xe la maniera più fácil de donar! Sto metodo de pagamento el permette solo un massimo de %(amount)s. Par piaser seleziona na durata o un metodo de pagamento diverso. Sto metodo de pagamento el richiede un minimo de %(amount)s. Par piaser seleziona na durata diversa o un altro metodo de pagamento. Binance Coinbase Kraken Seleziona un metodo di pagamento. “Adotta un torrent”: il tuo nome utente o messaggio nel nome del file torrent <div %(div_months)s>una volta ogni 12 mesi di abbonamento</div> El to nome utente o menzion anonima nei crediti Accesso anticipato a nuove funzionalità Telegram esclusivo con aggiornamenti dietro le quinte %(number)s download veloci al giorno se donésto sto mese! <a %(a_api)s>Accesso API JSON</a> Status leggendario nella preservazione della conoscenza e cultura dell'umanità Vantaggi precedenti, più: Guadagna <strong>%(percentage)s%% download bonus</strong> <a %(a_refer)s>invitando amici</a>. Articoli SciDB <strong>illimitati</strong> senza verifica Quando te domandi su conti o donazion, zonta el to ID del conto, screenshot, ricevute, più informazion possìbile. Controlemò l'email ogni 1-2 setimane, cusì no zontàr ste informazion ritarderà ogni risoluzion. Per ottenere ancora più download, <a %(a_refer)s>riferisci i tuoi amici</a>! Semu un picolo gruppo de volontari. Podaria voler 1-2 setimane par risponder. Nota che 'l nome o l'imàzene de ła conta i połe par strani. Nò te ghè da inpensierirse! Cueste conte ł'è gestie dai nostri donadori. Nostre conte nò ł'è stade mìa violade. Dona <span %(span_cost)s></span> <span %(span_label)s></span> par 12 mesi “%(tier_name)s” par 1 mese “%(tier_name)s” par 24 mesi “%(tier_name)s” par 3 mesi “%(tier_name)s” par 48 mesi “%(tier_name)s” per 6 mesi “%(tier_name)s” par 96 mesi “%(tier_name)s” Te pol ancora cancelar la donazion durante el checkout. Schiassa el boton de donazion par confermar sta donazion. <strong>Nota importante:</strong> I prezzi delle criptovalute possono fluttuare notevolmente, a volte anche del 20%% in pochi minuti. Questo è comunque meno delle commissioni che sosteniamo con molti fornitori di pagamento, che spesso addebitano il 50-60%% per lavorare con una “charity ombra” come la nostra. <u>Se ci invii la ricevuta con il prezzo originale che hai pagato, accrediteremo comunque il tuo account per l'abbonamento scelto</u> (purché la ricevuta non sia più vecchia di qualche ora). Apprezziamo davvero che tu sia disposto a sopportare cose del genere per supportarci! ❤️ ❌ Qualcosa no la xe andà ben. Par piaser ricarica la pagina e prova de novo. <span %(span_circle)s>1</span>Compre Bitcoin su Paypal <span %(span_circle)s>2</span>Trasferissi i Bitcoin al nostro indirizzo ✅ Reindirizendo a la pagina de donazion… Dona Par piaser, spetè almanco <span %(span_hours)s>24 ore</span> (e rinfrescà sta pagina) prima de contatarne. Se desideri fare una donazione (di qualsiasi importo) senza iscrizione, sentiti libero di usare questo indirizzo Monero (XMR): %(address)s. Dopo aver invià la tua carta regalo, el nostro sistema automatico la confermerà in pochi minuti. Se no funziona, prova a rinviare la tua carta regalo (<a %(a_instr)s>istruzioni</a>). Se ancora no funziona, par piaser mandene un'email e Anna la controllerà manualmente (sto processo pol ciapar qualche giorno), e assicurate de menzionare se ti ga già provà a rinviare. Esempio: Par piaser, dopara el <a %(a_form)s>formulario oficial de Amazon.com</a> par mandarne na carta regalo de %(amount)s al indirizo email qua soto. Email del destinatario nel modulo: Carta regalo Amazon No podemo accettar altri metodi de carte regalo, <strong>solo invià direttamente dal modulo ufficiale su Amazon.com</strong>. No podemo restituir la tua carta regalo se no usi sto modulo. Usa solo una volta. Unico per il tuo account, non condividerlo. Aspetando la carta regalo… (rinfresca la pagina par controlar) Varda la <a %(a_href)s>pagina de donazion con codice QR</a>. Scansiona el codice QR con l'app Alipay, o schiaccia el boton par abrir l'app Alipay. Par piaser, abbi pazienza; la pagina podaria impiegare un poco a carigarse visto che xe in Cina. <span %(style)s>3</span>Fai la donazion (scansiona el codice QR o schiaccia el boton) Compra moneta PYUSD su PayPal Compre Bitcoin (BTC) su Cash App Compre un poco de più (recomendemo %(more)s de più) del importo che sté donando (%(amount)s), par coprir le spese de transazion. Te tegnarà quel che resta. Va alla pagina “Bitcoin” (BTC) in Cash App. Transfèri el Bitcoin al nostro indirizo Par donazion pìcini (soto i $25), te podarìa dové doparar Rush o Priority. Clicca el boton “Manda bitcoin” par far un “ritiro”. Cambia da dollari a BTC schiassando l’icona %(icon)s. Inserissi el importo in BTC qua soto e clicca “Manda”. Varda <a %(help_video)s>sto video</a> se te ghe problemi. I servizi express xe comodi, ma i ga commissioni più alte. Puéi usar questo invece de un exchange crypto se te vol far na donazion più grande rapidamente e no te dispiase na commissione de $5-10. Assicurate de mandar l'importo crypto esato mostrà sulla pagina de donazion, no l'importo in $USD. Altrimenti la commissione sarà sottratta e no podemo processar automaticamente el to abbonamento. Qualche volta la conferma pol ciapàr fin a 24 ore, cusì assicùrate de rinfrescàr sta pàgina (anca se la xe scadùa). Istruzión par carta de credito / debito Dona tramite la nostra pagina de carta de credito / debito Qualchedun dei passi menziona i portafogni crypto, ma no te preoccupar, no te gà da imparar gnente de crypto par questo. %(coin_name)s istruzioni Scansiona questo codice QR con l'app Crypto Wallet per compilare rapidamente i dettagli di pagamento Scansionare il codice QR da pagare Supportiamo solo la versione standard delle criptovalute, nessuna rete o versione esotica delle monete. La conferma della transazione può richiedere fino a un'ora, a seconda della moneta. Dona %(amount)s su <a %(a_page)s>sta pagina</a>. Sta donazion la xe scadua. Par piaser annulla e crea na nova. Se gavì già pagà: Sì, go mandà l'email col recepì Se el tasso de cambio de le criptovalute xe varià durante la transazion, assicurè de includar la ricevuta che mostra el tasso de cambio original. Ve ringraziemo tanto par el disturbo de doparar le criptovalute, ne aiuta tanto! ❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo. <span %(span_circle)s>%(circle_number)s</span>Manda n'email col recepì Se te ghe problemi, contatene a %(email)s e includi più informazion possibili (come schermate). ✅ Grazie par la to donazion! Anna ativarà manualmente la to iscrizion in pochi giorni. Manda un recepì o na schermada al to indirizo de verificazion personale: Quando te ghè mandà l'email col recepì, schissa sto boton, cusì Anna la pol revisar a man (ghe volarà qualche giorno): Manda una ricevuta o uno screenshot al to indirizzo de verificazion personale. NO doparar sto indirizzo email par la to donazion PayPal. Anula Sì, per piaser anula Te sì sicuro de voler cancelar? No cancelar se te gà già pagà. ❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo. Fà na nova donazion ✅ La tua donazione è stata annullata. Data: %(date)s Identificatore: %(id)s Riordina Stato: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mese par %(duration)s mesi, incluso %(discounts)s%% sconto)</span> Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mese par %(duration)s mesi)</span> 1. Inserisci la to email. 2. Seleziona el to metodo de pagamento. 3. Seleziona de novo el to metodo de pagamento. 4. Seleziona il portafoglio “Self-hosted”. 5. Clicca “Confermo la proprietà”. 6. Dovresti ricevere una ricevuta via email. Per favore, inviacela e confermeremo la tua donazione il prima possibile. (potresti voler annullare e creare una nuova donazione) Le istruzión de pagamento xe adesso vecie. Se te vol far n'altra donazion, dopara el boton “Riordina” qua sora. Gavì già pagà. Se volè riveder le istruzioni de pagamento comunque, clicca qua: Mostra istruzión vecie de pagamento Se la pagina de donazion vien blocada, provè con na conession internet diversa (par esempio VPN o internet del telefono). Sfortunatamente, la pagina di Alipay è spesso accessibile solo dalla <strong>Cina continentale</strong>. Potresti dover disabilitare temporaneamente il tuo VPN, o utilizzare un VPN verso la Cina continentale (a volte funziona anche Hong Kong). <span %(span_circle)s>1</span>Dona su Alipay Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account Alipay</a> Istruzioni Alipay <span %(span_circle)s>1</span>Trasferisci a uno dei nostri conti crypto Dona l'importo totale de %(total)s a uno de sti indirizzi: Istruzioni Crypto Segui le istruzioni par comprar Bitcoin (BTC). Te gà solo bisogno de comprar la quantità che te vol donar, %(total)s. Inserissi el nostro indirizo Bitcoin (BTC) come destinatario, e segui le istruzión par mandar la to donazion de %(total)s: <span %(span_circle)s>1</span>Fà na donazion su Pix Fà na donazion de %(total)s doparà <a %(a_account)s>sto conto Pix Istruzioni Pix <span %(span_circle)s>1</span>Fà na donazion su WeChat Fà na donazion de %(total)s doparà <a %(a_account)s>sto conto WeChat</a> Istruzioni WeChat Dopara uno de i seguenti serviz express “carta de credito a Bitcoin”, che i ciapa solo pochi minuti: Indirizzo BTC / Bitcoin (portafoglio esterno): Importo BTC / Bitcoin: Compila i seguenti dettagli nel modulo: Se qualcossa de sto informassion xe fora de data, mandene un email par farne saver. Par piaser, dopara sto <span %(underline)s>importo esato</span>. El to costo totale podaria esser più alto par via dei costi de la carta de credito. Par importi bassi, purtroppo, questo podaria superar el nostro sconto. (minimo: %(minimum)s) (minimo: %(minimum)s) (minimo: %(minimum)s) (minimo: %(minimum)s, nessuna verifica per la prima transazione) (minimo: %(minimum)s) (minimo: %(minimum)s a seconda del paese, nessuna verifica per la prima transazione) Segui le istruzion par comprar moneta PYUSD (PayPal USD). Compra un poco de più (recomendemo %(more)s de più) del importo che stai donando (%(amount)s), par coprire le commissioni de transazione. Ti te tieni quel che avanza. Va alla pagina “PYUSD” nel to app o sito web de PayPal. Schiaccia el boton “Transfer” %(icon)s, e dopo “Send”. Aggiorna stato Per resettare il timer, basta creare una nuova donazione. Assicuriteve de doparar l'importo in BTC qua soto, <em>NO</em> euri o dollari, senò no podaremo ricever l'importo giusto e no podaremo confermar automaticamente la vostra iscrizion. Compre Bitcoin (BTC) su Revolut Compre un poco de più (recomendemo %(more)s de più) del importo che sté donando (%(amount)s), par coprir le spese de transazion. Te tegnarà quel che resta. Va alla pagina “Crypto” in Revolut par comprar Bitcoin (BTC). Transfèri el Bitcoin al nostro indirizo Par donazion pìcini (soto i $25) te podarìa dové doparar Rush o Priority. Clicca el boton “Manda bitcoin” par far un “ritiro”. Cambia da euri a BTC schiassando l’icona %(icon)s. Inserissi el importo in BTC qua soto e clicca “Manda”. Varda <a %(help_video)s>sto video</a> se te ghe problemi. Stato: 1 2 Guida passo-passo Vedi la guida passo-passo qua soto. Sennò te podares restar fora da sto conto! Se no l'has ancora fato, scrivi zo la to ciave segreta par intrar: Grasie par la to donazion! Tempo restante: Donazione Transfèri %(amount)s a %(account)s In atesa de conferma (rinfresca la pagina par controlar)… Aspettando il trasferimento (aggiorna la pagina per controllare)… Precedente I download veloci ne le ultime 24 ore conta verso el limite giornaliero. I scaricamenti da i Server Partner Veloci i xe segnà da %(icon)s. Ultime 18 ore Nissun file scaricado ancora. I file scaricà no i xe mostrà publicamente. Tutti gli orari sono in UTC. File scaricà Se te ghè scaricà un file con download veloci e lenti, el comparirà do volte. No te preocuparte massa, ghe xe tanti che descarica dai siti che noi lighemo, e xe estremamente raro aver problemi. Comunque, par star sicuri te racomandemo de doparar un VPN (pagà), o <a %(a_tor)s>Tor</a> (gratis). Go descartegà 1984 de George Orwell, vien la polizia a casa mia? Ti xe Anna! Chi xe Anna? Abbiamo un'API JSON stabile per i membri, per ottenere un URL di download veloce: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentazione all'interno del JSON stesso). Per altri casi d'uso, come iterare attraverso tutti i nostri file, costruire ricerche personalizzate e così via, raccomandiamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. I dati grezzi possono essere esplorati manualmente <a %(a_explore)s>attraverso file JSON</a>. La nostra lista di torrent grezzi può essere scaricata anche come <a %(a_torrents)s>JSON</a>. Avete un'API? No ospitén nissun materiale coperto da copyright qua. Semo un motore de ricerca, e come tale indexén solo i metadata che xe già publicamente disponibili. Quando te scarichi da ste fonti esterne, te sugerimo de verificar le leggi nella to giurisdizion riguardo a ciò che xe permesso. No semo responsabili par el contignuo ospitato da altri. Se te ghé dei reclami su ciò che vedi qua, el mejo xe contatar el sito original. Regolarmente tirén su i so cambiamenti nel nostro database. Se te pensi veramente de aver un reclamo DMCA valido a cui dovemo risponder, par favor compila el <a %(a_copyright)s>modulo de reclamo DMCA / Copyright</a>. Tignén seriamente i to reclami, e te risponderén appena possibile. Come podio segnalar una violazion de copyright? Qua ghe xe alcuni libri che ga un significato speciale par el mondo delle biblioteche ombra e la preservazion digitale: Quali xe i vostri libri preferiti? Voremo anca ricordar a tuti che tuto el nostro codice e i dati xe completamente open source. Questo xe unico par progetti come el nostro — no semo consapevoli de nissun altro progetto con un catalogo così massivo che xe anca completamente open source. Semo veramente contenti de acueiar chiunque pensi che gestimo mal el nostro progetto a prender el nostro codice e i dati e crear la so propria biblioteca ombra! No lo disén per dispetto o altro — pensén veramente che saria fantastico perché alzaria el livello par tuti, e preservaria mejo l'eredità de l'umanità. Odio come te gestissi sto progetto! Saremo contenti se la zente mettesse su <a %(a_mirrors)s>specchi</a>, e ghe daremo supporto finanziario. Come posso aiutar? Sì, lo femo. La nostra ispirazion par coleccionar metadata xe el obiettivo de Aaron Swartz de "una pagina web par ogni libro mai pubblicà", par el qual el ga creato <a %(a_openlib)s>Open Library</a>. Quel progetto ga fato ben, ma la nostra posizion unica ne permette de ottener metadata che lori no riesse. Un'altra ispirazion xe sta la nostra voja de saver <a %(a_blog)s>quanti libri ghe xe nel mondo</a>, par calcolar quanti libri ne resta ancora da salvar. Te colezi i metadati? Nota che mhut.org blocca certe range de IP, quindi podarìa servir un VPN. <strong>Android:</strong> Clicca sul menu coi tre punti in alto a destra e seleziona “Aggiungi alla schermata Home”. <strong>iOS:</strong> Clicca sul pulsante “Condividi” in basso e seleziona “Aggiungi alla schermata Home”. No gavemo una app ufficiale par el telefono, ma te pol instalar sto sito come una app. Gavè una app par el telefono? Per favore, inviali a l’<a %(a_archive)s>Internet Archive</a>. I li conserverà adeguatamente. Come posso donare libri o altri materiali fisici? Come posso richiedere libri? <a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — aggiornamenti regolari <a %(a_software)s>Software de Anna</a> — el nostro codice open source <a %(a_datasets)s>Datasets</a> — su i dati <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domini alternativi Ghe xe più risorse su l'Archivio de Anna? <a %(a_translate)s>Traduci su el Software de Anna</a> — el nostro sistema de traduzione <a %(a_wikipedia)s>Wikipedia</a> — più su de noi (per favor aiuta a mantener sta pagina aggiornata, o crea una par la to lengua!) Scegli le impostazioni che te piase, lasa el quadro de serca vacante, clicca “Serca”, e dopo segna la pagina nei preferiti del to browser. Come fazzo a salvar le impostazioni de serca? Semo contenti de acueiar i ricercatori de segurità che cerche vulnerabilità nei nostri sistemi. Semo grandi sostenitori de la divulgazion responsabile. Contatene <a %(a_contact)s>qua</a>. Al momento no podemo premiar bug bounty, ecceto par le vulnerabilità che ga <a %(a_link)s>el potenziale de comprometer la nostra anonimità</a>, par le quali ofrimo premi tra i $10k-50k. Voremo ofrir un ambito più ampio par i bug bounty in futuro! Nota che i attacchi de ingegneria sociale xe fora dal ambito. Se te sì interessà a la segurità offensiva, e te vol ajudar a archivar la conoscenza e la cultura del mondo, contatene. Ghe xe tanti modi in cui te podi ajudar. Gavé un programma de divulgazion responsabile? No gavemo letteralmente risorse sufficienti par dar a tutti nel mondo download ad alta velocità, per quanto ne piaceria. Se un benefattore ricco volesse fornirne questo, saria incredibile, ma finché no succede, stemo facendo del nostro meglio. Semo un progetto no-profit che riesce a malapena a sostenersi tramite donazioni. Xe per questo che gavemo implementà do sistemi par i download gratuiti, coi nostri partner: server condivisi con download lenti, e server leggermente più veloci con una lista d'attesa (par ridurre el numero de persone che scarica allo stesso tempo). Gavemo anca <a %(a_verification)s>verifica del browser</a> par i nostri download lenti, perché senò i bot e i scraper i abusaria, rendendo le cose ancora più lente par i utenti legittimi. Nota che, quando utilizzi il Tor Browser, potresti dover regolare le tue impostazioni di sicurezza. Al livello più basso delle opzioni, chiamato “Standard”, la sfida del turnstile Cloudflare riesce. Ai livelli più alti, chiamati “Più sicuro” e “Il più sicuro”, la sfida fallisce. Par i file grandi, a volte i download lenti se pol interromper a metà. Recomandemo de doparar un gestore de download (come JDownloader) par riprendere automaticamente i download grandi. Parché i download lenti xe cussì lenti? Domande Frequenti (FAQ) Usa il <a %(a_list)s>generatore di liste di torrent</a> per generare una lista di torrent che hanno più bisogno di seeding, entro i limiti del tuo spazio di archiviazione. Sì, vedi la pagina <a %(a_llm)s>dati LLM</a>. La maggior parte dei torrent contiene i file direttamente, il che significa che puoi istruire i client torrent a scaricare solo i file richiesti. Per determinare quali file scaricare, puoi <a %(a_generate)s>generare</a> i nostri metadati, o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Purtroppo, un certo numero di collezioni di torrent contiene file .zip o .tar alla radice, nel qual caso devi scaricare l'intero torrent prima di poter selezionare i file individuali. No ghe xe strumenti facili da doparar par filtrare i torrent, ma accetemo volentieri contributi. (Gavemo <a %(a_ideas)s>qualche idea</a> par sto caso però.) Risposta lunga: Risposta corta: no facilmente. Cerchiamo di mantenere al minimo la duplicazione o la sovrapposizione tra i torrent in questa lista, ma questo non può sempre essere raggiunto e dipende molto dalle politiche delle biblioteche di origine. Per le biblioteche che rilasciano i propri torrent, non è nelle nostre mani. Per i torrent rilasciati da Anna’s Archive, deduplichiamo solo in base all'hash MD5, il che significa che diverse versioni dello stesso libro non vengono deduplicate. Sì. Questi sono in realtà PDF ed EPUB, semplicemente non hanno un'estensione in molti dei nostri torrent. Ci sono due posti in cui puoi trovare i metadati per i file torrent, inclusi i tipi di file/estensioni: 1. Ogni collezione o rilascio ha i propri metadati. Ad esempio, i <a %(a_libgen_nonfic)s>torrent di Libgen.rs</a> hanno un database di metadati corrispondente ospitato sul sito web di Libgen.rs. Tipicamente colleghiamo le risorse di metadati rilevanti dalla <a %(a_datasets)s>pagina del dataset</a> di ciascuna collezione. 2. Recomandémose de <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> i nostri database ElasticSearch e MariaDB. Questi contien un mapeo par ogni record in l'Archivio de Anna ai so corispondenti file torrent (se disponibili), soto "torrent_paths" nel JSON de ElasticSearch. Certi client torrent no supporta dimensioni de pezzi grandi, che tanti dei nostri torrent gà (par quelli più novi no lo femo più — anca se xe valido secondo le specifiche!). Dunque, prova un altro client se te incroci sto problema, o lamentete coi creatori del to client torrent. Vorrei aiutare a fare seed, ma non ho molto spazio su disco. I torrent sono troppo lenti; posso scaricare i dati direttamente da voi? Posso scaricare solo un sottoinsieme dei file, come solo una particolare lingua o argomento? Come gestite i duplicati nei torrent? Posso ottenere la lista dei torrent come JSON? Non vedo PDF o EPUB nei torrent, solo file binari? Cosa devo fare? Parché el me client torrent no riesse a vierzi certi file torrent / link magnetici vostri? FAQ sui Torrent Come fazo a caricar novi libri? Vedi <a %(a_href)s>sto progetto eccellente</a>. Gavì un monitor de uptime? Cosa xe l'Archivio de Anna? Diventa un membro per utilizzare i download veloci. Adesso suportemo le carte regalo Amazon, carte de credito e debito, cripto, Alipay e WeChat. Ti xe finì i download veloci par ozéti. Accès Download orari nei ultimi 30 giorni. Media oraria: %(hourly)s. Media giornaliera: %(daily)s. Lavorémo con i nostri partner par far che le nostre collezioni sia facilmente e gratuitamente accessibili a tuti. Credémo che ognuno gà el diritto alla saggezza collettiva de l'umanità. E <a %(a_search)s>no a scapito dei autori</a>. I datasets doparà in l'Archivio de Anna xe completamente liberi, e i pol èssar duplicai in massa doparà i torrents. <a %(a_datasets)s>Sàpi de più…</a> Archivio a lungo termine Database completo Serca Libri, articoli, riviste, fumetti, registri di biblioteca, metadati, … Tuto el nostro <a %(a_code)s>codice</a> e <a %(a_datasets)s>dati</a> xe completamente open source. <span %(span_anna)s>L'Archivio de Anna</span> xe un progetto no-profit con do obiettivi: <li><strong>Preservazion:</strong> Archiviando tuto el sapere e la cultura de l'umanità.</li><li><strong>Accesso:</strong> Rendendo sto sapere e cultura disponibili a chiunque nel mondo.</li> Gavemo la più grande collezion de dati testuali de alta qualità del mondo. <a %(a_llm)s>Scopri de più…</a> Dati de training LLM 🪩 Speci: chiamata ai volontari Se te gestissi un processador de pagamenti anonimi ad alto risco, contatene. Semo anca in serca de persone che vol meter piccole publicità de bon gusto. Tutti i proventi va a le nostre iniziative de preservazion. Preservazion Stimemo de aver preservà circa <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% dei libri del mondo</a>. Preservémo libri, articoli, fumeti, riviste e altro ancora, portando sti materiali da varie <a href="https://en.wikipedia.org/wiki/Shadow_library">biblioteche ombra</a>, biblioteche ufficiali e altre collezioni in un solo posto. Tutti sti dati i xe preservai par sempre rendendoli facili da duplicar in massa — usando i torrent — risultando in tante copie in giro par el mondo. Alcune biblioteche ombra lo fa già da sole (es. Sci-Hub, Library Genesis), mentre l'Archivio de Anna “libera” altre biblioteche che no offre la distribussion in massa (es. Z-Library) o che no xe biblioteche ombra per niente (es. Internet Archive, DuXiu). Sta larga distribussion, combinada col codice open-source, rende el nostro sito resistente ai tentativi de chiusura, e assicura la preservassion a lungo termine del sapere e della cultura umana. Scopri de più sui <a href="/datasets">nostri datasets</a>. Se ti xe un <a %(a_member)s>membro</a>, no xe bisogno de verificare el browser. 🧬&nbsp;SciDB xe na continuazion de Sci-Hub. SciDB Vèrzi DOI Sci-Hub ga <a %(a_paused)s>sospeso</a> el caricamento de nuovi articoli. Accesso diretto a %(count)s articoli accademici 🧬&nbsp;SciDB è una continuazione di Sci-Hub, con la sua interfaccia familiare e la visualizzazione diretta dei PDF. Inserisci il tuo DOI per visualizzare. Abbiamo l'intera collezione di Sci-Hub, oltre a nuovi articoli. La maggior parte può essere visualizzata direttamente con un'interfaccia familiare, simile a Sci-Hub. Alcuni possono essere scaricati tramite fonti esterne, in tal caso mostriamo i link a queste. Ti pol dar un gran aiuto seminando i torrents. <a %(a_torrents)s>Sàpi de più…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Se cèrchene volontari Come progetto no-profit e open-source, semo sempre in serca de persone che ne possa dar na man. Download IPFS Lista par %(by)s, creà <span %(span_time)s>%(time)s</span> Salva ❌ Qualcosa è andato storto. Per favore riprova. ✅ Salvà. Par piaser ricarica la pagina. La lista xe vuota. modifica Aggiungi o rimuovi da questa lista trovando un file e aprendo la scheda “Liste”. Lista Come podemo aiutar Rimozion de sovrapposizioni (deduplicazione) Estrazion de testo e metadati OCR Semo in grado de fornire accesso ad alta velocità a tutte le nostre collezioni, come anca a collezioni non ancora rilasciate. Questo xe un accesso a livello aziendale che podemo fornire par donazioni nell'ordine de decine de migliaia de USD. Semo anca disposti a scambiarlo par collezioni de alta qualità che no gavemo ancora. Podemo rimborsarte se te riesi a fornirne un arricchimento dei nostri dati, come: Suporta l'archiviazion a lungo termine del sapere umano, mentre te ottieni dati migliori par el to modello! <a %(a_contact)s>Contatene</a> par discuter come podemo lavorar insieme. Xe ben capio che i LLM prospera su dati de alta qualità. Gavemo la più grande collezion de libri, articoli, riviste, ecc. del mondo, che xe tra le fonti de testo de più alta qualità. Dati LLM Scala e gamma uniche La nostra collezion contiene più de cento milioni de file, inclusi giornali accademici, manuali e riviste. Riusimo a raggiunger sta scala combinando grandi archivi esistenti. Alcune delle nostre collezioni de fonte xe già disponibili in massa (Sci-Hub e parti de Libgen). Alte fonti le gavemo liberà noi stessi. <a %(a_datasets)s>Datasets</a> mostra na panoramica completa. La nostra collezion include milioni de libri, articoli e riviste de prima dell'era degli e-book. Grandi parti de sta collezion le xe già state OCRizzate, e ga già poco sovrapposizion interna. Continua Se hai perso la tua chiave, per favore <a %(a_contact)s>contattaci</a> e fornisci quante più informazioni possibili. Potreste dover temporaneamente creare un nuovo account per contattarci. Par piaser <a %(a_account)s>faze el login</a> par vardar sta pagina.</a> Par evitar che i spam-bot crea tanti account, gavémo bisogno de verificar prima el to browser. Se te incastri in un ciclo infinito, te racomandemo de installar <a %(a_privacypass)s>Privacy Pass</a>. Pòdarìa anca esser utile disattivar i blocchi de pubblicità e altre estensioni del browser. Accedi / Registrati L'Archivio de Anna xe temporaneamente fora uso par manutenzion. Torna tra un'ora. Autore alternativo Descrizione alternativa Edizione alternativa Estensione alternativa Nome file alternativo Editore alternativo Titolo alternativo data de apertura del código Lézi de più… descrission Cerca nell'Archivio di Anna il numero CADAL SSNO Sercar Archivio d'Anna par nùmaro DuXiu SSID Serca in l'Archivio de Anna el numero DXID de DuXiu Sercar Archivio d'Anna par ISBN Sercar el Archivio d'Anna par nùmaro OCLC (WorldCat) Sercar el Archivio d'Anna par ID Open Library Visualizzatore online de l'Archivio de Anna %(count)s pagine interessate Dopo aver scaricà: Una versione migliore de sto file podarìa esser disponibile a %(link)s Download de torrent in massa collezion Dopara strumenti online par convertir tra i formati. Strumenti de conversion racomandai: %(links)s Par i file grandi, racomandemo de doparar un gestore de download par evitar interruzioni. Gestori de download racomandai: %(links)s Indice eBook EBSCOhost (solo par esperti) (clica anca su "GET" in alto) (clica "GET" in alto) Descàrreghe esterne <strong>🚀 Descàrreghe veloci</strong> Ti resta %(remaining)s par oggi. Gràssie de èsar un membro! ❤️ <strong>🚀 Download veloci</strong> Te ghè finìo i download veloci par oggi. Ti gà scaricà sto file de recente. I link i resta validi par un poco. Divaenta un <a %(a_membership)s>membro</a> par suportar la preservazion a longo termine de libri, articoli, e altro. Par mostrar la nostra gratitudine par el to suport, te gavarà download veloci. ❤️ 🚀 Download veloci 🐢 Download lenti Presta da l'Internet Archive Gateway IPFS #%(num)d (el połe èsar nesesario provar pì volte co IPFS) Libgen.li Libgen.rs Romanzo Libgen.rs Sazìstega i so annunci i xe conossui par contener software malizioso, quindi dòpara un blocca-annunci o no cliccar sui annunci Amazon ‘Manda a Kindle’ djazz ‘Manda a Kobo/Kindle’ MagzDB ManualsLib Nexus/STC (I file Nexus/STC podaria no esser affidabili da scaricar) Nissun download trovà. Tuti i speci i gà el steso archivo, e i gavarìa da èsar seguri da doparar. Dito cuesto, fa senpre atension co te scarghi archivi da internet. Par ezempio, segurate de mantenjir azornài i to dispozidivi. (nissun reindirizamento) Apri nel nostro visualizzatore (apri nel visualizzatore) Opzion #%(num)d: %(link)s %(extra)s Trova el record originale in CADAL Sercar da manual so DuXiu Trova el record original in ISBNdb Trova il record originale in WorldCat Trova el record original in Open Library Sercar altri databazi par ISBN (solo par utenti con disabilità de stampa) PubMed Gavarè bisogno de un lettore de ebook o PDF par vierzer el file, a seconda del formato. Lettori de ebook racomandai: %(links)s L'Archivio de Anna 🧬 SciDB Sci-Hub: %(doi)s (el DOI asosià el połe nò èsar disponìbiłe in Sci-Hub) Pòi mandar sia i file PDF che EPUB al to Kindle o Kobo eReader. Strumenti racomandai: %(links)s Più informazion nel <a %(a_slow)s>FAQ</a>. Sostien i autori e le biblioteche Se te piase questo e te lo pòi permetter, considera de comprar l'originale, o de sostener i autori direttamente. Se questo xe disponibile in biblioteca, considera de prestartelo gratis là. I download dal Server Partner no i xe temporaneamente disponibili par sto file. torrent Da partner fidàbili. Z-Library Z-Library su Tor (el bezonja el Tor Browser) mostra download esterni <span class="font-bold">❌ Sto file podarìa aver problemi, e xe stà nascosto da una biblioteca de origine.</span> Qualche volta xe su richiesta del detentor del copyright, qualche volta xe perché xe disponibile una alternativa mejo, ma qualche volta xe perché ghe xe un problema col file stesso. Podarìa ancora andar ben par scaricarlo, ma te racomandemo de cercar prima un file alternativo. Più detagli: Se te vol ancora scaricar sto file, assicurite de dòparar solo software fidà e aggiornà par aprirlo. commenti sui metadati AA: Cerca nell'Archivio di Anna “%(name)s” Esploratore de Codici: Visualizza in Esplora Codici “%(name)s” URL: Sito web: Se gavè sto file e no xe ancora disponibile su l'Archivio de Anna, considerè de <a %(a_request)s>carigarlo</a>. File de Prestito Digitale Controllato de l'Internet Archive “%(id)s” Sto qua xe un record de un file da l'Internet Archive, no un file scaricabile direttamente. Te pol provare a imprestar el libro (link qua soto), o doparar sto URL quando <a %(a_request)s>richiedi un file</a>. Migliora i metadati Record de metadata CADAL SSNO %(id)s Sto xe un record de metadati, no un file scaricabile. Te podi doparar sto URL quando <a %(a_request)s>richiedi un file</a>. Record de metadati DuXiu SSID %(id)s Record di metadata ISBNdb %(id)s MagzDB ID %(id)s record de metadata Nexus/STC ID %(id)s record de metadata Record di metadata del numero OCLC (WorldCat) %(id)s Open Library %(id)s record de metadata File de Sci-Hub “%(id)s” Njaùn rezultà “%(md5_input)s” no'l ze stà catà nte ła baze de dadi. Aggiungi commento (%(count)s) Puoi ottenere l'md5 dall'URL, ad esempio MD5 di una versione migliore di questo file (se applicabile). Compila questo campo se c'è un altro file che corrisponde strettamente a questo file (stessa edizione, stessa estensione del file se riesci a trovarne uno), che le persone dovrebbero usare invece di questo file. Se conosci una versione migliore di questo file al di fuori di Anna’s Archive, allora per favore <a %(a_upload)s>caricala</a>. Qualcosa è andato storto. Ricarica la pagina e riprova. Hai lasciato un commento. Potrebbe volerci un minuto prima che venga visualizzato. Per favore usa il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>. Descrivi il problema (obbligatorio) Se questo file è di alta qualità, puoi discutere qualsiasi cosa a riguardo qui! In caso contrario, utilizza il pulsante “Segnala problema file”. Ottima qualità del file (%(count)s) Qualità del file Impara come <a %(a_metadata)s>migliorare i metadati</a> di questo file da solo. Descrizione del problema Per favore <a %(a_login)s>accedi</a>. Ho adorato questo libro! Aiuta la comunità segnalando la qualità di questo file! 🙌 Qualcosa è andato storto. Ricarica la pagina e riprova. Segnala un problema con il file (%(count)s) Grazie per aver inviato il tuo report. Verrà mostrato su questa pagina e sarà esaminato manualmente da Anna (fino a quando non avremo un sistema di moderazione adeguato). Lascia un commento Invia segnalazione Cosa c'è che non va in questo file? Prendi in prestito (%(count)s) Commenti (%(count)s) Download (%(count)s) Esplora i metadati (%(count)s) Liste (%(count)s) Statìstiche (%(count)s) Per informazioni su questo particolare file, consulta il suo <a %(a_href)s>file JSON</a>. Questo è un file gestito dalla biblioteca <a %(a_ia)s>IA’s Controlled Digital Lending</a> e indicizzato da Anna’s Archive per la ricerca. Per informazioni sui vari datasets che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Datasets</a>. Metadati dal record collegato Migliora i metadati su Open Library Un “file MD5” è un hash che viene calcolato dai contenuti del file ed è ragionevolmente unico in base a quel contenuto. Tutte le biblioteche ombra che abbiamo indicizzato qui utilizzano principalmente gli MD5 per identificare i file. Un file potrebbe apparire in più biblioteche ombra. Per informazioni sui vari datasets che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Datasets</a>. Segnala la qualità del file Download totali: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadati cechi %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Attenzione: record collegati multipli: Quando te vedi un libro su l'Archivio de Anna, te pol veder vari campi: titolo, autore, editore, edizione, anno, descrizione, nome del file, e altro. Tutte ste informazioni le xe ciamade <em>metadati</em>. Dado che combiniamo libri da varie <em>biblioteche sorgente</em>, mostriamo qualsiasi metadato disponibile in quella biblioteca sorgente. Par esempio, par un libro che gavemo da Library Genesis, mostreremo el titolo dal database de Library Genesis. Qualche volta un libro el xe presente in <em>più</em> biblioteche sorgente, che podaria aver campi de metadati diversi. In quel caso, mostriamo semplicemente la versione più longa de ogni campo, visto che quella speremo contenga le informazioni più utili! Mostreremo ancora i altri campi sotto la descrizione, par esempio come "titolo alternativo" (ma solo se i xe diversi). Tiremmo fora anca <em>codici</em> come identificatori e classificatori dalla biblioteca sorgente. <em>Identificatori</em> rappresenta in modo unico una particolare edizione de un libro; esempi xe ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classificatori</em> raggruppa insieme più libri simili; esempi xe Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. Qualche volta sti codici i xe esplicitamente collegati nelle biblioteche sorgente, e qualche volta riusciamo a tirarli fora dal nome del file o dalla descrizione (principalmente ISBN e DOI). Podemo usar i identificatori par trovar record in <em>collezioni solo metadati</em>, come OpenLibrary, ISBNdb, o WorldCat/OCLC. Ghe xe una specifica <em>scheda metadati</em> nel nostro motore de ricerca se te volessi sfogliare ste collezioni. Usiamo i record corrispondenti par riempir i campi de metadati mancanti (par esempio se manca un titolo), o par esempio come "titolo alternativo" (se ghe xe un titolo esistente). Par veder esattamente da dove vien i metadati de un libro, vedi la <em>scheda "Dettagli tecnici"</em> su una pagina del libro. La gà un link al JSON grezzo par quel libro, con puntatori al JSON grezzo dei record originali. Par più informazioni, vedi le seguenti pagine: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Ricerca (scheda metadati)</a>, <a %(a_codes)s>Esploratore de codici</a>, e <a %(a_example)s>Esempio de metadati JSON</a>. Finalmente, tutti i nostri metadati i pol esser <a %(a_generated)s>generati</a> o <a %(a_downloaded)s>scaricati</a> come database ElasticSearch e MariaDB. Contesto Ti pol aiutar a preservar i libri migliorando i metadati! Prima, legi el contesto sui metadati su l'Archivio de Anna, e dopo impara come migliorar i metadati tramite el collegamento con Open Library, e guadagna un'abbonamento gratuito su l'Archivio de Anna. Migliora i metadati Dunque se te trovi un file con metadati sbagliati, come dovresti sistemarlo? Te pol andar alla biblioteca sorgente e seguir le sue procedure par sistemar i metadati, ma cosa fare se un file el xe presente in più biblioteche sorgente? Ghe xe un identificatore che el xe trattato in modo speciale su l'Archivio de Anna. <strong>El campo annas_archive md5 su Open Library el sovrascrive sempre tutti i altri metadati!</strong> Fermiamoci un attimo e impariamo de più su Open Library. Open Library la xe stata fondata nel 2006 da Aaron Swartz con l'obiettivo de "una pagina web par ogni libro mai pubblicato". La xe una sorta de Wikipedia par i metadati dei libri: tutti i pol modificarla, la xe liberamente licenziata, e la pol esser scaricata in blocco. La xe un database de libri che el xe più allineato con la nostra missione — in fatti, l'Archivio de Anna el xe stato ispirato dalla visione e vita de Aaron Swartz. Invece de reinventar la ruota, gavemo deciso de indirizzar i nostri volontari verso Open Library. Se te vedi un libro che gà metadati sbagliati, te pol aiutar nel seguente modo: Nota che questo funziona solo per i libri, no per i documenti accademici o altri tipi de file. Per altri tipi de file racomandemo ancora de catar la biblioteca de origine. Podaria voler qualche settimana prima che i cambiamenti vegni inclusi in Anna’s Archive, visto che gavemo bisogno de scaricar l'ultimo dump de dati de Open Library, e rigenerar el nostro indice de ricerca.  Va al <a %(a_openlib)s>sito web de Open Library</a>. Trova el record corretto del libro. <strong>ATTENZIONE:</strong> assicurati de selezionare la <strong>edizione</strong> corretta. In Open Library, ghe xe "opere" e "edizioni". Una "opera" podaria esser "Harry Potter e la Pietra Filosofale". Una "edizione" podaria esser: La prima edizion del 1997 pubblicada da Bloomsbery con 256 pagine. L'edizion tascabile del 2003 pubblicada da Raincoast Books con 223 pagine. La tradussion polaca del 2000 “Harry Potter I Kamie Filozoficzn” da Media Rodzina con 328 pagine. Tute ste edizions ga ISBN e contenuti diversi, cusì assicuraiteve de selessionar quela giusta! Modifica el record (o creelo se no'l esiste), e zonta più informazioni utili che te riesi! Te sì qua adesso, tanto val farlo ben. Soto “ID Numbers” seleziona “Anna’s Archive” e zonta el MD5 del libro da Anna’s Archive. Questa xe la stringa longa de letare e numeri dopo “/md5/” nel URL. Prova a catar altri file in Anna’s Archive che corisponde anca a sto record, e zontali anca quei. In futuro podemo grupare quei come duplicati sulla pagina de ricerca de Anna’s Archive. Quando te ga finìo, scrivi l'URL che te ga appena aggiornà. Una volta che te ga aggiornà almanco 30 records con i MD5 de Anna’s Archive, mandine un <a %(a_contact)s>email</a> e mandine la lista. Te daremo un'abonamento gratuito per Anna’s Archive, cusì te podi far sto lavoro più facilmente (e come ringraziamento per el to aiuto). Questi deve esser modifiche de alta qualità che zonta una quantità sostanziale de informazioni, senò la to richiesta sarà rifiutà. La to richiesta sarà anca rifiutà se qualcuna de le modifiche vien revertìa o corretta dai moderatori de Open Library. Collegamento con Open Library Se te ghe te involvi significativamente nel sviluppo e nelle operazioni del nostro lavoro, podemo parlar de condivider più del ricavo delle donazioni con ti, par che ti possa impiegarli come necessario. Pagheremo solo per l'hosting una volta che avrai tutto configurato e avrai dimostrato di essere in grado di mantenere l'archivio aggiornato con gli aggiornamenti. Questo significa che dovrai pagare per i primi 1-2 mesi di tasca tua. El vostro tempo no sarà compensà (e gnanca el nostro), visto che xe puro lavoro de volontariato. Siamo disposti a coprire le spese di hosting e VPN, inizialmente fino a $200 al mese. Questo è sufficiente per un server di ricerca di base e un proxy protetto da DMCA. Spese de ospitalità Par piaser <strong>no contatarne</strong> par domandar permesso, o par domande basiche. Le azioni parla più forte delle parole! Tutte le informazioni xe là fora, quindi va avanti e impianta el to specio. Sentiti libero di postare ticket o richieste di merge sul nostro Gitlab quando incontri problemi. Potremmo dover costruire alcune funzionalità specifiche per il mirror con te, come il rebranding da “Anna’s Archive” al nome del tuo sito web, (inizialmente) disabilitando gli account utente, o collegando le pagine dei libri al nostro sito principale. Una volta che il tuo mirror è in funzione, ti preghiamo di contattarci. Ci piacerebbe rivedere la tua OpSec, e una volta che sarà solida, collegheremo il tuo mirror e inizieremo a lavorare più strettamente con te. Grazie in anticipo a chiunque sia disposto a contribuire in questo modo! Non è per i deboli di cuore, ma consoliderebbe la longevità della più grande biblioteca veramente aperta nella storia umana. Come cominciar Par aumentare la resilienza de l'Archivio de Anna, stemo cercando volontari par gestir specchi. La vostra versione xe chiaramente distinta come uno specio, par esempio “L’Archivio de Bob, un specio de l’Archivio de Anna”. Sito disposto a prenderti i rischi associati a questo lavoro, che sono significativi. Hai una profonda comprensione della sicurezza operativa richiesta. I contenuti di <a %(a_shadow)s>questi</a> <a %(a_pirate)s>post</a> sono evidenti per te. Inizialmente non ti daremo accesso ai download del nostro server partner, ma se le cose andranno bene, potremo condividerlo con te. Te gestissi el codice open source de l’Archivio de Anna, e te aggiorni regolarmente sia el codice che i dati. Sito disposto a contribuire al nostro <a %(a_codebase)s>codice</a> — in collaborazione con il nostro team — per far sì che ciò accada. Cerchemo questo: Specchi: chiamata per volontari Fà n'altra donazion. Ancora gnente donazioni. <a %(a_donate)s>Fazo la me prima donazion.</a> I detagli de le donazion no i xe mostradi publicamente. Le me donazion 📡 Par duplicare in massa la nostra collezion, varda le pagine <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>. Download dal tuo indirizzo IP nelle ultime 24 ore: %(count)s. 🚀 Par scaricamenti più veloci e evitar i control browser, <a %(a_membership)s>diventa membro</a>. Scarica dal sito partner Continua pure a navigar l'Archivio de Anna in un'altra scheda mentre aspeti (se el to browser supporta l'aggiornamento delle schede in background). Continua pure a aspettar che più pagine de download se cariche in contemporanea (ma per piaser scarica solo un file alla volta per server). Una volta che te ghè un link de download, el xe valido par diverse ore. Grasie par l'attesa, questo rende el sito accessibile gratis par tuti! 😊 🔗 Tutti i link de download par sto file: <a %(a_main)s>Pagina principale del file</a>. ❌ I download lenti no xe disponibili tramite i VPN de Cloudflare o da indirizzi IP de Cloudflare. ❌ I download lenti i xe disponibili solo tramite el sito ufficiale. Visita %(websites)s. 📚 Dòpara el libro con sto URL: <a %(a_download)s>Dòpara adesso</a>. Par dar a tuti l'opportunità de scarigar i file gratis, te ghe da spetare prima de poder scarigar sto file. Par piaser, speta <span %(span_countdown)s>%(wait_seconds)s</span> secondi prima de scarigar sto file. Atension: ghe xe stà tanti download dal to IP ne le ultime 24 ore. I download podarìa èsser più lenti del solito. Se stai usando una VPN, una connessione internet condivisa, o il tuo ISP condivide gli IP, questo avviso potrebbe essere dovuto a ciò. Salva ❌ Qualcosa el xe ndà storto. Prova ancora. ✅ Salvà. Par piaser ricariga la pagina. Cambia el to nome visualizà. El to identificativo (la parte dopo “#”) no se pol cambiar. Profilo creato <span %(span_time)s>%(time)s</span> modifica Liste Crea una nuova lista trovando un file e aprendo la scheda “Liste”. Ancora gnente liste Profilo no catà. Profilo Al momento, no podemo accomodar richieste de libri. No ne mandè email con richieste de libri. Par piaser, fazi le to richieste sui forum de Z-Library o Libgen. Record in l'Archivio de Anna DOI: %(doi)s Scarica SciDB Nexus/STC Anteprima non ancora disponibile. Scarica il file da <a %(a_path)s>L'Archivio di Anna</a>. Per supportare l'accessibilità e la conservazione a lungo termine della conoscenza umana, diventa un <a %(a_donate)s>membro</a>. Come bonus, 🧬&nbsp;SciDB si carica più velocemente per i membri, senza alcun limite. Non funziona? Prova a <a %(a_refresh)s>aggiornare</a>. Sci-Hub Zonta un campo de ricerca specifico Cerca descrizioni e commenti sui metadati Anno di pubblicazione Avanzato Accesso Contenjìo Display Lista Tabèła Tipo d'archivo Łéngua Ordina par Pì grando Pì rełevante Pì novo (filesize) (codice sorgente aperto) (anno di pubblicazione) Pì vecio Casuale Pì cèo Sorgente racolto e reso open-source da AA Prestito Digitale (%(count)s) Articoli de rivista (%(count)s) Abbiamo trovato corrispondenze in: %(in)s. Puoi fare riferimento all'URL trovato lì quando <a %(a_request)s>richiedi un file</a>. Metadati (%(count)s) Per esplorar l'indice de ricerca per codici, usa el <a %(a_href)s>Codes Explorer</a>. L'indice de ricerca el vien aggiornà mensilmente. Al momento el include voci fino a %(last_data_refresh_date)s. Par più informazion tecniche, varda la pagina dei %(link_open_tag)sdataset</a>. Escludi Includi solo No controlà altro… Avanti … Precedente Sto indice de ricerca al momento include i metadata da la biblioteca de Prestito Digitale Controllato de l'Internet Archive. <a %(a_datasets)s>Più informazioni sui nostri datasets</a>. Par più biblioteche digitali, varda <a %(a_wikipedia)s>Wikipedia</a> e el <a %(a_mobileread)s>MobileRead Wiki</a>. Par reclami DMCA / copyright <a %(a_copyright)s>clicca qua</a>. Tempo di download Errore durante la ricerca. Prova a <a %(a_reload)s>ricaricar la pagina</a>. Se el problema persiste, manda un'email a %(email)s. Download veloce In realtà, chiunque può aiutare a preservare questi file facendo seeding della nostra <a %(a_torrents)s>lista unificata di torrent</a>. ➡️ A volte questo succede erroneamente quando il server di ricerca è lento. In tali casi, <a %(a_attrs)s>ricaricare</a> può aiutare. ❌ 'Sto archivo el połe gaver dei problemi. Sito papers? Sto indice de ricerca al momento inclùde metadata da varie fonti de metadata. <a %(a_datasets)s>Più informazion sui nostri datasets</a>. Ci sono molte, molte fonti di metadati per opere scritte in tutto il mondo. <a %(a_wikipedia)s>Questa pagina di Wikipedia</a> è un buon inizio, ma se conosci altre buone liste, faccelo sapere. Par i metadati, mostremo i record originali. No femo fusion de record. Gavemo attualmente el catalogo open più completo del mondo de libri, articoli, e altre opere scritte. Specchiemo Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e altro</a>. <span class="font-bold">Nessun file trovato.</span> Prova con termini di ricerca e filtri diversi o meno specifici. Risultati %(from)s-%(to)s (%(total)s in totale) Se trovi altre “biblioteche ombra” che dovremmo rispecchiare, o se hai domande, contattaci a %(email)s. %(num)d corrispondenze parziali %(num)d+ corispondenze parziali Scrivi nel riquadro per cercar file nelle biblioteche digitali in prestito. Scrivi nel quadro par sercar nel nostro catalogo de %(count)s file scaricabili direttamente, che noi <a %(a_preserve)s>preservemo par sempre</a>. Scrivi nel riquadro par cercar. Scrivi nel riquadro par cercar nel nostro catalogo de %(count)s articoli accademici e de rivista, che <a %(a_preserve)s>conservemo par sempre</a>. Scrivi nel riquadro par cercar metadati da le biblioteche. Questo el pol esser utile quando <a %(a_request)s>richiedi un file</a>. Suggerimento: dopara le scorciatoie da tastiera “/” (focus su cerca), “enter” (cerca), “j” (su), “k” (giù), “<” (pagina precedente), “>” (pagina successiva) par navigar più veloce. Questi sono record di metadati, <span %(classname)s>non</span> file scaricabili. Impostazioni de ricerca Serca Prestito Digitale Descarga Articoli di riviste Metadati Nova reserca %(search_input)s - Ricerca La ricerca ha impiegato troppo tempo, il che significa che potresti vedere risultati inaccurati. A volte <a %(a_reload)s>ricaricare</a> la pagina aiuta. La ricerca ha impiegato troppo tempo, il che è comune per le query ampie. I conteggi dei filtri potrebbero non essere accurati. Par carichi grandi (più de 10.000 file) che no vien acceptadi da Libgen o Z-Library, par piaser contatene a %(a_email)s. Par Libgen.li, assicurate de far prima el login su <a %(a_forum)s >el loro forum</a> con el nome utente %(username)s e la password %(password)s, e dopo tornate su la loro <a %(a_upload_page)s >pagina de caricamento</a>. Par adesso, sugierimo de cargar libri novi sui fork de Library Genesis. Qua ghe xe un <a %(a_guide)s>guida utile</a>. Nota che tuti i fork che indexemo su sto sito tira da sto stesso sistema de carica. Par caricamentini (fin a 10.000 file) par piaser caricali sia su %(first)s che su %(second)s. In alternativa, podè carigarli su Z-Library <a %(a_upload)s>qua</a>. Par cargar articoli academici, par piaser (oltre a Library Genesis) carighe anca su <a %(a_stc_nexus)s>STC Nexus</a>. I xe la mejo biblioteca ombra par articoli novi. No i xe ancora integradi, ma lo faremo a un certo punto. Te pol doparar el so <a %(a_telegram)s>bot de upload su Telegram</a>, o contatar l'indirizo segnà nel messagio fissà se te ghè massa file da cargar in sto modo. <span %(label)s>Lavoro di volontariato intenso (ricompense da USD$50 a USD$5,000):</span> se puoi dedicare molto tempo e/o risorse alla nostra missione, ci piacerebbe lavorare più da vicino con te. Alla fine potresti unirti al team interno. Anche se abbiamo un budget limitato, siamo in grado di assegnare <span %(bold)s>💰 ricompense monetarie</span> per il lavoro più intenso. <span %(label)s>Lavoro leggero de volontariato:</span> se te poi dedicar solo qualche ora qua e là, ghe xe ancora tante maniere in cui te poi dar na man. Ricompensemo i volontari costanti con <span %(bold)s>🤝 abbonamenti all’Archivio de Anna</span>. L’Archivio de Anna se basa sui volontari come ti. Accogliemo tuti i livelli de impegno, e gavemo do categorie principali de aiuto che cerchemo: Se non puoi dedicare il tuo tempo come volontario, puoi comunque aiutarci molto <a %(a_donate)s>donando denaro</a>, <a %(a_torrents)s>seminando i nostri torrent</a>, <a %(a_uploading)s>caricando libri</a>, o <a %(a_help)s>parlando ai tuoi amici di Anna’s Archive</a>. <span %(bold)s>Aziende:</span> offriamo accesso diretto ad alta velocità alle nostre collezioni in cambio di donazioni a livello aziendale o in cambio di nuove collezioni (ad esempio, nuove scansioni, datasets OCR, arricchimento dei nostri dati). <a %(a_contact)s>Contattaci</a> se sei interessato. Vedi anche la nostra <a %(a_llm)s>pagina LLM</a>. Tagie Semo sempre in serca de persone con boni skill de programmazione o de sicurezza offensiva par partecipar. Ti pol far una gran differenza nel preservar l'eredità de l'umanità. Come ringraziamento, regalemo l'abbonamento par contributi solidi. Come un gran ringraziamento, regalemo ricompense monetarie par compiti particolarmente importanti e difficili. No dovessi considerar questo come un sostituto de un lavoro, ma xe un incentivo in più e pol aiutar con i costi sostenui. La maggior parte del nostro codice xe open source, e chiederemo che anca el tuo codice sia tale quando assegnemo la ricompensa. Ghe xe qualche eccezione che podemo discuter individualmente. Le ricompense xe assegnàe alla prima persona che completa un compito. Sentitevi liberi de commentar su un ticket de ricompensa par far saver agli altri che stai lavorando su qualcosa, così che gli altri possano aspettar o contattarti par far squadra. Ma stai atento che gli altri xe ancora liberi de lavorar su el compito e tentar de batterti. Comunque, no assegnemo ricompense par lavori fatti male. Se ghe xe do submission de alta qualità fatte vicine (entro un giorno o do), podemo sceglier de assegnar ricompense a entrambi, a nostra discrezione, per esempio 100%% par la prima submission e 50%% par la seconda submission (quindi 150%% in totale). Par le ricompense più grandi (specialmente le ricompense de scraping), contattaci quando te gà completà ~5%% de el compito, e te sì sicuro che el tuo metodo se pol scalare al traguardo completo. Dovrai condividere el tuo metodo con noi così podemo dar feedback. Anca, in questo modo podemo decidere cosa far se ghe xe più persone che se avvicinano a una ricompensa, come potenzialmente assegnarla a più persone, incoraggiar le persone a far squadra, ecc. ATTENZIONE: i compiti con alta ricompensa xe <span %(bold)s>difficili</span> — podessi esser saggio iniziar con quelli più facili. Vai alla nostra <a %(a_gitlab)s>lista de issue su Gitlab</a> e ordina per “Label priority”. Questo mostra più o meno l'ordine dei compiti che ne interessa. I compiti senza ricompense esplicite xe ancora eligibili par l'abbonamento, specialmente quelli segnati come “Accepted” e “Anna’s favorite”. Podessi voler iniziar con un “Starter project”. Volontariato leggero Gavemo adesso anca un canale Matrix sincronizà a %(matrix)s. Se hai qualche ora libera, puoi aiutare in diversi modi. Assicurati di unirti alla <a %(a_telegram)s>chat dei volontari su Telegram</a>. Come segno di apprezzamento, di solito offriamo 6 mesi di “Bibliotecario Fortunato” per traguardi di base, e di più per il lavoro di volontariato continuato. Tutti i traguardi richiedono lavoro di alta qualità — il lavoro scadente ci danneggia più di quanto ci aiuti e lo rifiuteremo. Per favore <a %(a_contact)s>mandaci un'email</a> quando raggiungi un traguardo. %(links)s link o screenshot delle richieste che hai soddisfatto. Soddisfare le richieste di libri (o articoli, ecc.) sui forum di Z-Library o Library Genesis. Non abbiamo un nostro sistema di richieste di libri, ma rispecchiamo quelle biblioteche, quindi migliorarle rende migliore anche Anna’s Archive. Traguardo Compito Dipende dal compito. Piccoli compiti pubblicati nella nostra <a %(a_telegram)s>chat dei volontari su Telegram</a>. Di solito per l'adesione, a volte per piccole ricompense. Compiti piccoli postai nel nostro gruppo de chat par volontari. Assicurate de lassare un commento sui problemi che te sistemi, cussì che altri no duplichi el to lavoro. %(links)s link dei record che hai migliorato. Te pol doparar la <a %(a_list)s >lista de problemi de metadata casuali</a> come punto de partenza. Migliora i metadati <a %(a_metadata)s>collegandoli</a> con Open Library. Questi i dovarìa mostrarte mentre informi qualcun de l'Archivio de Anna, e lori che te ringrazia. %(links)s link o screenshot. Difondendo la parola de l'Archivio de Anna. Par esempio, racomandando libri su AA, colegando ai nostri post del blog, o in generale indirizando le persone al nostro sito. Traduci completamente una lingua (se non era già quasi completata). <a %(a_translate)s>Tradurre</a> il sito web. Link alla cronologia delle modifiche che mostra che hai fatto contributi significativi. Migliora la pagina Wikipedia di Anna’s Archive nella tua lingua. Includi informazioni dalla pagina Wikipedia di AA in altre lingue, e dal nostro sito web e blog. Aggiungi riferimenti ad AA su altre pagine rilevanti. Volontariato & Ricompense 