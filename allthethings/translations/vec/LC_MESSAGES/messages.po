#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Richiesta non valida. Visita %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " e "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "e altro ancora"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Se speciemio %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Racogliemo e rendemo open-source %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Tutto il nostro codice e i nostri dati sono completamente open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;La più grande biblioteca veramente aperta nella storia dell'umanità."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libri, %(paper_count)s&nbsp;articoli — preservai par sempre."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La più grande biblioteca open-source e open-data del mondo. ⭐️&nbsp;Specchi di Sci-Hub, Library Genesis, Z-Library e altro. 📈&nbsp;%(book_any)s libri, %(journal_article)s articoli, %(book_comic)s fumetti, %(magazine)s riviste — conservati per sempre."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 La più grande biblioteca open-source open-data del mondo.<br>⭐️ Rispecchia Scihub, Libgen, Zlib e altro ancora."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadati incorreti (es. titolo, descrizion, immagine de copertina)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemi di download (es. impossibile connettersi, messaggio di errore, molto lento)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "El file no se pol vierzer (p.e. file coroto, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Pessima qualità (p.e. problemi de formatazion, scansion de bassa qualità, pagine mancanti)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / file da rimuover (es. pubblicità, contenuto offensivo)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamo di copyright"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Altro"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Download bonus"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brillant Bibliofilo"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Fortunato Bibliotecario"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Splendido Accumulador de Dati"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Archivista Ammirabile"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) in totale"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "non pagato"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "pagato"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "cancellà"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "scadùo"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "aspettando che Anna confermi"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "nò valido"

#, fuzzy
msgid "page.donate.title"
msgstr "Dona"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Ghe xe na <a %(a_donation)s>donazion esistente</a> in corso. Par piaser finissi o annulla sta donazion prima de farne na nova."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Varda tute le me donazion</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "L'Archivio de Anna xe un progetto senza fini de lucro, open-source e open-data. Donando e diventando un membro, ti supporti le nostre operazioni e lo sviluppo. A tuti i nostri membri: grazie de core par mantenerne in attività! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Par più informazioni, consulta el <a %(a_donate)s>FAQ delle Donazioni</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Per ottenere ancora più download, <a %(a_refer)s>riferisci i tuoi amici</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Ottieni %(percentage)s%% download veloci bonus, perché sei stato referenziato dall'utente %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Questo si applica all'intero periodo di abbonamento."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s download veloci al giorno"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se donésto sto mese!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mese"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Unisse"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selezionato"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "fino a %(percentage)s%% sconti"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Articoli SciDB <strong>illimitati</strong> senza verifica"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Accesso API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Guadagna <strong>%(percentage)s%% download bonus</strong> <a %(a_refer)s>invitando amici</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "El to nome utente o menzion anonima nei crediti"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Vantaggi precedenti, più:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Accesso anticipato a nuove funzionalità"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram esclusivo con aggiornamenti dietro le quinte"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adotta un torrent”: il tuo nome utente o messaggio nel nome del file torrent <div %(div_months)s>una volta ogni 12 mesi di abbonamento</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Status leggendario nella preservazione della conoscenza e cultura dell'umanità"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Accesso Esperto"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contatene"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Semu un picolo gruppo de volontari. Podaria voler 1-2 setimane par risponder."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Accesso illimitato</strong> ad alta velocità"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Server diretti <strong>SFTP</strong>"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donazione a livello aziendale o scambio per nuove collezioni (ad es. nuove scansioni, datasets OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Accettemo grandi donazioni da individui o istituzioni benestanti. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Par donazioni superiori a $5000 contatene direttamente a %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Sii consapevole che mentre i abbonamenti su sta pagina xe \"par mese\", i xe donazioni una tantum (no ricorrenti). Varda el <a %(faq)s>FAQ sulle donazioni</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Se desideri fare una donazione (di qualsiasi importo) senza iscrizione, sentiti libero di usare questo indirizzo Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Seleziona un metodo di pagamento."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporaneamente indisponibile)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s carta regalo"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carta bancaria (usando app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carta di credito/debito"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regolar)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Carta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Carta de crédito/débito/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Carta bancaria"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carta di credito/debito (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carta de crédito/débito 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Con le criptovalute puoi donare usando BTC, ETH, XMR e SOL. Usa questa opzione se sei già familiare con le criptovalute."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Co le criptovalute ti pol donare usando BTC, ETH, XMR, e altro."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se te stè doparà cripto par la prima volta, te sugierimo de doparar %(options)s par comprar e donar Bitcoin (la criptovaluta original e più doparada)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Per donare utilizzando PayPal US, useremo PayPal Crypto, che ci permette di rimanere anonimi. Apprezziamo che tu prenda il tempo per imparare a donare utilizzando questo metodo, poiché ci aiuta molto."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Dona usando PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Dona utilizzando Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se hai Cash App, questo è il modo più semplice per donare!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Nota che per transazioni inferiori a %(amount)s, Cash App potrebbe addebitare una commissione di %(fee)s. Per %(amount)s o superiori, è gratuito!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Dona usando Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se ti gà Revolut, questa xe la maniera più fácil de donar!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Dona con carta de credito o debito."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay potrebbero funzionare anche."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Nota che per piccole donazioni le commissioni della carta di credito potrebbero eliminare il nostro sconto %(discount)s%%, quindi consigliamo abbonamenti più lunghi."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Nota che par le piccole donazioni le commissioni xe alte, quindi raccomandemo abbonamenti più lunghi."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, acquisti Bitcoin con una carta di credito/debito o un conto bancario, e poi doni quel Bitcoin a noi. In questo modo possiamo rimanere sicuri e anonimi quando accettiamo la tua donazione."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance xe disponibile in quasi ogni paese, e supporta la maggior parte delle banche e delle carte di credito/debito. Questa xe attualmente la nostra raccomandazion principale. Ringraziemose de averse preso el tempo de imparar come donar usando sto metodo, visto che ne aiuta tanto."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Dona usando el to conto PayPal regular."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Dona usando carta de credito/debito, PayPal, o Venmo. Te pol scoler tra questi nella pagina successiva."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Dona usando na carta regalo Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Nota che gavemo bisogno de arrotondar ai importi accettai dai nostri rivenditori (minimo %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Supportemo solo Amazon.com, no altri siti Amazon. Par esempio, .de, .co.uk, .ca, NO i xe supportai."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Sta opzion la xe par %(amazon)s. Se te vol doparar un altro sito de Amazon, selezionalo qua sora."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Sto metodo usa un fornitore de criptovalute come conversion intermedia. Questo pol esser un poco confusionario, quindi per piaser usa sto metodo solo se gli altri metodi de pagamento no funsiona. No funsiona in tutti i paesi."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona usando na carta de credito/debito, tramite l'app Alipay (facilissimo da configurar)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instala l'app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instala l'app Alipay dal <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrate usando el to numero de telefono."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "No xe richiesti altri dettagli personali."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Agiongi carta bancaria"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supportà: Visa, MasterCard, JCB, Diners Club e Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Varda <a %(a_alipay)s>sta guida</a> par più informazioni."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "No podemo suportar carte de crédito/débito diretamente, parchè le banche no vol lavorar con nualtri. ☹ Comunque, ghe xe diverse maniere de usar le carte de crédito/débito lo stesso, usando altri metodi de pagamento:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Carta Regalo Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Mandine carte regalo de Amazon.com doparà la to carta de credito/debito."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay suporta carte de crédito/débito internazionali. Varda <a %(a_alipay)s>sta guida</a> par più informazion."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) el suporta carte de credito/debito internazionali. Inte l'app de WeChat, va su “Me => Servizi => Portafoglio => Zonta una Carta”. Se no te vedi quelo, ativelo doparà “Me => Impostazioni => Generali => Strumenti => Weixin Pay => Ativa”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Te poi comprar crypto doparà carte de credito/debito."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servizi express crypto"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "I servizi express xe comodi, ma i ga commissioni più alte."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Puéi usar questo invece de un exchange crypto se te vol far na donazion più grande rapidamente e no te dispiase na commissione de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assicurate de mandar l'importo crypto esato mostrà sulla pagina de donazion, no l'importo in $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Altrimenti la commissione sarà sottratta e no podemo processar automaticamente el to abbonamento."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimo: %(minimum)s a seconda del paese, nessuna verifica per la prima transazione)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimo: %(minimum)s, nessuna verifica per la prima transazione)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se qualcossa de sto informassion xe fora de data, mandene un email par farne saver."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Per le carte de credito, carte de debito, Apple Pay, e Google Pay, usémo “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Nel loro sistema, un “caffè” xe uguale a $5, quindi la tua donazion sarà arrotondata al multiplo più vicino de 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Seleziona per quanto tempo desideri abbonarti."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mese"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 mesi"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 mesi"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 mesi"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 mesi"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 mesi"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 mesi"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>dopo <span %(span_discount)s></span> sconti</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Sto metodo de pagamento el richiede un minimo de %(amount)s. Par piaser seleziona na durata diversa o un altro metodo de pagamento."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Dona"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Sto metodo de pagamento el permette solo un massimo de %(amount)s. Par piaser seleziona na durata o un metodo de pagamento diverso."

#, fuzzy
msgid "page.donate.login2"
msgstr "Par diventare un membro, par piaser <a %(a_login)s>Intra o Registra</a>. Grazie par el to sostegno!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Scolé la tua criptovaluta preferida:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(importo minimo più basso)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(usa quando mandi Ethereum da Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(avviso: importo minimo elevato)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Schiassa el boton de donazion par confermar sta donazion."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Dona <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Te pol ancora cancelar la donazion durante el checkout."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Reindirizendo a la pagina de donazion…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Qualcosa no la xe andà ben. Par piaser ricarica la pagina e prova de novo."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mese"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "par 1 mese"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "par 3 mesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "par 6 mesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "par 12 mesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "par 24 mesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "par 48 mesi"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "par 96 mesi"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "par 1 mese “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "par 3 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "per 6 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "par 12 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "par 24 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "par 48 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "par 96 mesi “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donazione"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Data: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mese par %(duration)s mesi, incluso %(discounts)s%% sconto)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mese par %(duration)s mesi)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Stato: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identificatore: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Anula"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Te sì sicuro de voler cancelar? No cancelar se te gà già pagà."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Sì, per piaser anula"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ La tua donazione è stata annullata."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Fà na nova donazion"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Riordina"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Gavì già pagà. Se volè riveder le istruzioni de pagamento comunque, clicca qua:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Mostra istruzión vecie de pagamento"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Grasie par la to donazion!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Se no l'has ancora fato, scrivi zo la to ciave segreta par intrar:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Sennò te podares restar fora da sto conto!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Le istruzión de pagamento xe adesso vecie. Se te vol far n'altra donazion, dopara el boton “Riordina” qua sora."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> I prezzi delle criptovalute possono fluttuare notevolmente, a volte anche del 20%% in pochi minuti. Questo è comunque meno delle commissioni che sosteniamo con molti fornitori di pagamento, che spesso addebitano il 50-60%% per lavorare con una “charity ombra” come la nostra. <u>Se ci invii la ricevuta con il prezzo originale che hai pagato, accrediteremo comunque il tuo account per l'abbonamento scelto</u> (purché la ricevuta non sia più vecchia di qualche ora). Apprezziamo davvero che tu sia disposto a sopportare cose del genere per supportarci! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Sta donazion la xe scadua. Par piaser annulla e crea na nova."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Istruzioni Crypto"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Trasferisci a uno dei nostri conti crypto"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Dona l'importo totale de %(total)s a uno de sti indirizzi:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compre Bitcoin su Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Trova la pagina “Crypto” nella tua app o sito PayPal. Solitamente xe sotto “Finanze”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Segui le istruzioni par comprar Bitcoin (BTC). Te gà solo bisogno de comprar la quantità che te vol donar, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Trasferissi i Bitcoin al nostro indirizzo"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Và alla pagina “Bitcoin” nella tua app o sito PayPal. Schiaccia el boton “Trasferisci” %(transfer_icon)s, e dopo “Invia”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Inserissi el nostro indirizo Bitcoin (BTC) come destinatario, e segui le istruzión par mandar la to donazion de %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Istruzión par carta de credito / debito"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona tramite la nostra pagina de carta de credito / debito"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Dona %(amount)s su <a %(a_page)s>sta pagina</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Vedi la guida passo-passo qua soto."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Stato:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "In atesa de conferma (rinfresca la pagina par controlar)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Aspettando il trasferimento (aggiorna la pagina per controllare)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tempo restante:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(potresti voler annullare e creare una nuova donazione)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Per resettare il timer, basta creare una nuova donazione."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Aggiorna stato"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Se te ghe problemi, contatene a %(email)s e includi più informazion possibili (come schermate)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Se gavì già pagà:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Qualche volta la conferma pol ciapàr fin a 24 ore, cusì assicùrate de rinfrescàr sta pàgina (anca se la xe scadùa)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Compra moneta PYUSD su PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Segui le istruzion par comprar moneta PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Compra un poco de più (recomendemo %(more)s de più) del importo che stai donando (%(amount)s), par coprire le commissioni de transazione. Ti te tieni quel che avanza."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Va alla pagina “PYUSD” nel to app o sito web de PayPal. Schiaccia el boton “Transfer” %(icon)s, e dopo “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transfèri %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Compre Bitcoin (BTC) su Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Va alla pagina “Bitcoin” (BTC) in Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compre un poco de più (recomendemo %(more)s de più) del importo che sté donando (%(amount)s), par coprir le spese de transazion. Te tegnarà quel che resta."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfèri el Bitcoin al nostro indirizo"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clicca el boton “Manda bitcoin” par far un “ritiro”. Cambia da dollari a BTC schiassando l’icona %(icon)s. Inserissi el importo in BTC qua soto e clicca “Manda”. Varda <a %(help_video)s>sto video</a> se te ghe problemi."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Par donazion pìcini (soto i $25), te podarìa dové doparar Rush o Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Compre Bitcoin (BTC) su Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Va alla pagina “Crypto” in Revolut par comprar Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Compre un poco de più (recomendemo %(more)s de più) del importo che sté donando (%(amount)s), par coprir le spese de transazion. Te tegnarà quel che resta."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfèri el Bitcoin al nostro indirizo"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Clicca el boton “Manda bitcoin” par far un “ritiro”. Cambia da euri a BTC schiassando l’icona %(icon)s. Inserissi el importo in BTC qua soto e clicca “Manda”. Varda <a %(help_video)s>sto video</a> se te ghe problemi."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Assicuriteve de doparar l'importo in BTC qua soto, <em>NO</em> euri o dollari, senò no podaremo ricever l'importo giusto e no podaremo confermar automaticamente la vostra iscrizion."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Par donazion pìcini (soto i $25) te podarìa dové doparar Rush o Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Dopara uno de i seguenti serviz express “carta de credito a Bitcoin”, che i ciapa solo pochi minuti:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Compila i seguenti dettagli nel modulo:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Importo BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Par piaser, dopara sto <span %(underline)s>importo esato</span>. El to costo totale podaria esser più alto par via dei costi de la carta de credito. Par importi bassi, purtroppo, questo podaria superar el nostro sconto."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Indirizzo BTC / Bitcoin (portafoglio esterno):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s istruzioni"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Supportiamo solo la versione standard delle criptovalute, nessuna rete o versione esotica delle monete. La conferma della transazione può richiedere fino a un'ora, a seconda della moneta."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scansionare il codice QR da pagare"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scansiona questo codice QR con l'app Crypto Wallet per compilare rapidamente i dettagli di pagamento"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Carta regalo Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Par piaser, dopara el <a %(a_form)s>formulario oficial de Amazon.com</a> par mandarne na carta regalo de %(amount)s al indirizo email qua soto."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "No podemo accettar altri metodi de carte regalo, <strong>solo invià direttamente dal modulo ufficiale su Amazon.com</strong>. No podemo restituir la tua carta regalo se no usi sto modulo."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Inserisci l'importo esatto: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Par piaser NO scriver un messaggio tuo."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Email del destinatario nel modulo:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unico per il tuo account, non condividerlo."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usa solo una volta."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Aspetando la carta regalo… (rinfresca la pagina par controlar)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Dopo aver invià la tua carta regalo, el nostro sistema automatico la confermerà in pochi minuti. Se no funziona, prova a rinviare la tua carta regalo (<a %(a_instr)s>istruzioni</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Se ancora no funziona, par piaser mandene un'email e Anna la controllerà manualmente (sto processo pol ciapar qualche giorno), e assicurate de menzionare se ti ga già provà a rinviare."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Esempio:"

msgid "page.donate.strange_account"
msgstr "Nota che 'l nome o l'imàzene de ła conta i połe par strani. Nò te ghè da inpensierirse! Cueste conte ł'è gestie dai nostri donadori. Nostre conte nò ł'è stade mìa violade."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Istruzioni Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Dona su Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Se la pagina de donazion vien blocada, provè con na conession internet diversa (par esempio VPN o internet del telefono)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Sfortunatamente, la pagina di Alipay è spesso accessibile solo dalla <strong>Cina continentale</strong>. Potresti dover disabilitare temporaneamente il tuo VPN, o utilizzare un VPN verso la Cina continentale (a volte funziona anche Hong Kong)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Fai la donazion (scansiona el codice QR o schiaccia el boton)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Varda la <a %(a_href)s>pagina de donazion con codice QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scansiona el codice QR con l'app Alipay, o schiaccia el boton par abrir l'app Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Par piaser, abbi pazienza; la pagina podaria impiegare un poco a carigarse visto che xe in Cina."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Istruzioni WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Fà na donazion su WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Fà na donazion de %(total)s doparà <a %(a_account)s>sto conto WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Istruzioni Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Fà na donazion su Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Fà na donazion de %(total)s doparà <a %(a_account)s>sto conto Pix"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Manda n'email col recepì"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Manda una ricevuta o uno screenshot al to indirizzo de verificazion personale. NO doparar sto indirizzo email par la to donazion PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Manda un recepì o na schermada al to indirizo de verificazion personale:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Se el tasso de cambio de le criptovalute xe varià durante la transazion, assicurè de includar la ricevuta che mostra el tasso de cambio original. Ve ringraziemo tanto par el disturbo de doparar le criptovalute, ne aiuta tanto!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Quando te ghè mandà l'email col recepì, schissa sto boton, cusì Anna la pol revisar a man (ghe volarà qualche giorno):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Sì, go mandà l'email col recepì"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Grazie par la to donazion! Anna ativarà manualmente la to iscrizion in pochi giorni."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Guida passo-passo"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Qualchedun dei passi menziona i portafogni crypto, ma no te preoccupar, no te gà da imparar gnente de crypto par questo."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Inserisci la to email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Seleziona el to metodo de pagamento."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Seleziona de novo el to metodo de pagamento."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Seleziona il portafoglio “Self-hosted”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Clicca “Confermo la proprietà”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Dovresti ricevere una ricevuta via email. Per favore, inviacela e confermeremo la tua donazione il prima possibile."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Par piaser, spetè almanco <span %(span_hours)s>24 ore</span> (e rinfrescà sta pagina) prima de contatarne."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Se te ghè fato un sbajo durante el pagamento, no podemo far rimborsi, ma cercheremo de sistemar la situazion."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Le me donazion"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "I detagli de le donazion no i xe mostradi publicamente."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Ancora gnente donazioni. <a %(a_donate)s>Fazo la me prima donazion.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fà n'altra donazion."

#, fuzzy
msgid "page.downloaded.title"
msgstr "File scaricà"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "I scaricamenti da i Server Partner Veloci i xe segnà da %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Se te ghè scaricà un file con download veloci e lenti, el comparirà do volte."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "I download veloci ne le ultime 24 ore conta verso el limite giornaliero."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Tutti gli orari sono in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "I file scaricà no i xe mostrà publicamente."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nissun file scaricado ancora."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ultime 18 ore"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Precedente"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Conto"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Intra / Registra"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Account ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profilo pubblico: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chiave segreta (non condividere!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "mostra"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Abonamento: <strong>%(tier_name)s</strong> fin a %(until_date)s <a %(a_extend)s>(estendi)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Abonamento: <strong>Nissun</strong> <a %(a_become)s>(diventa un membro)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Download veloci doparà (ultime 24 ore): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "quali download?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Gruppo esclusivo su Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Zugniteve qua!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Fae un <a %(a_tier)s>livèl più alto</a> par zugnir al nostro gruppo."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contata Anna a %(email)s se te sì interesà a migliorar el to abbonamento a un livèl più alto."

#, fuzzy
msgid "page.contact.title"
msgstr "Email de contati"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Te pol combinar più abbonamenti (i download veloci per 24 ore i sarà sommà)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profilo pubblico"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "File descartegadi"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Le me donazion"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Esci"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Te sì adesso fora. Ricarica la pagina par tornar a entrar."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Qualcosa la xe andà storta. Par piaser ricarica la pagina e prova de novo."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registrazion riuscida! El to codice segreto xe: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Tegni ben sto codice. Se lo perdi, perderai l'accesso al to conto."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Segnalibro.</strong> Ti pol segnar sta pagina par recuperar el to codice.</li><li %(li_item)s><strong>Scarica.</strong> Clicca <a %(a_download)s>sto link</a> par scaricar el to codice.</li><li %(li_item)s><strong>Gestor de password.</strong> Dòpara un gestor de password par salvar el codice quando lo inserissi qua soto.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Inserissi el to codice segreto par entrar:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chiave segreta"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Intra"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Codice segreto no valido. Verifica el to codice e prova ancora, o in alternativa registrite un novo conto qua soto."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "No perder el to codice!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "No te ghè ancora un conto?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registra un novo conto"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Se hai perso la tua chiave, per favore <a %(a_contact)s>contattaci</a> e fornisci quante più informazioni possibili."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Potreste dover temporaneamente creare un nuovo account per contattarci."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vecio conto basà su email? Inserissi <a %(a_open)s>l'email qua</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Lista"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "modifica"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Salva"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Salvà. Par piaser ricarica la pagina."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Qualcosa è andato storto. Per favore riprova."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Lista par %(by)s, creà <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "La lista xe vuota."

#, fuzzy
msgid "page.list.new_item"
msgstr "Aggiungi o rimuovi da questa lista trovando un file e aprendo la scheda “Liste”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profilo"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilo no catà."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "modifica"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Cambia el to nome visualizà. El to identificativo (la parte dopo “#”) no se pol cambiar."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Salva"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Salvà. Par piaser ricariga la pagina."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Qualcosa el xe ndà storto. Prova ancora."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profilo creato <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Liste"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Ancora gnente liste"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Crea una nuova lista trovando un file e aprendo la scheda “Liste”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "La riforma del copyright xe necessaria par la sicurezza nazionale"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: I LLM cinesi (incluso DeepSeek) i xe stai addestrai sul me archivio illegale de libri e articoli — el più grande al mondo. L'Occidente ga bisogno de riformar la legge sul copyright come questione de sicurezza nazionale."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articoli de accompagnamento da TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "No tanto tempo fa, le “biblioteche ombra” le stava morendo. Sci-Hub, el grande archivio illegale de articoli accademici, ga smesso de accettar nuove opere, a causa de cause legali. “Z-Library”, la più grande biblioteca illegale de libri, ga visto i so presunti creatori arrestai per accuse penali de copyright. I xe riuscì incredibilmente a scampare all'arresto, ma la so biblioteca no xe meno minacciata."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quando Z-Library ga affrontà la chiusura, mi gavevo già fatto un backup de tutta la so biblioteca e stavo cercando una piattaforma par ospitarla. Questa xe stada la mia motivazione par iniziar l'Archivio de Anna: una continuazione de la missione dietro quei iniziative precedenti. Da allora semo cresciuti fino a diventare la più grande biblioteca ombra al mondo, ospitando più de 140 milioni de testi coperti da copyright in diversi formati — libri, articoli accademici, riviste, giornali e oltre."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mi e el me team semo ideologi. Credemo che preservar e ospitar sti file xe moralmente giusto. Le biblioteche in tutto el mondo le sta vedendo tagli ai finanziamenti, e no podemo fidar el patrimonio de l'umanità alle corporazioni."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Poi xe rivà l'AI. Praticamente tutte le grandi aziende che costruisse LLM le ga contattà par addestrarse sui nostri dati. La maggior parte (ma no tutte!) delle aziende statunitensi le ga rivalutà una volta che le ga capì la natura illegale del nostro lavoro. Al contrario, le aziende cinesi le ga abbraccià con entusiasmo la nostra collezione, apparentemente no preoccupandose de la legalità. Questo xe notevole visto el ruolo de la Cina come firmataria de quasi tutti i principali trattati internazionali sul copyright."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Gavemo dato accesso ad alta velocità a circa 30 aziende. La maggior parte de queste xe aziende LLM, e alcune xe broker de dati, che rivenderà la nostra collezione. La maggior parte xe cinesi, anche se gavemo lavorà con aziende dagli Stati Uniti, Europa, Russia, Corea del Sud e Giappone. DeepSeek <a %(arxiv)s>ga ammesso</a> che una versione precedente xe stada addestrada su parte de la nostra collezione, anche se xe molto riservati sul loro ultimo modello (probabilmente addestrato anche sui nostri dati)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se l'Occidente vol restar avanti nella corsa dei LLM, e in ultima analisi, dell'AGI, xe necessario riconsiderar la so posizione sul copyright, e presto. Che siate d'accordo o meno con noi sul nostro caso morale, questo sta diventando un caso de economia, e anche de sicurezza nazionale. Tutti i blocchi di potere sta costruendo super-scienziati artificiali, super-hacker e super-militari. La libertà de informazione sta diventando una questione de sopravvivenza par sti paesi — anche una questione de sicurezza nazionale."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "El nostro team xe da tutto el mondo, e no gavemo un allineamento particolare. Ma incoraggeremo i paesi con leggi sul copyright forti a usar sta minaccia esistenziale par riformarle. Allora, cosa fare?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "La nostra prima raccomandazione xe semplice: accorciar el termine del copyright. Negli Stati Uniti, el copyright xe concesso per 70 anni dopo la morte dell'autore. Questo xe assurdo. Podemo allinear questo con i brevetti, che xe concessi per 20 anni dopo il deposito. Questo dovria esser più che sufficiente par gli autori de libri, articoli, musica, arte e altre opere creative, par esser compensai completamente per i so sforzi (inclusi progetti a lungo termine come le adattazioni cinematografiche)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Poi, al minimo, i responsabili delle politiche dovria includer eccezioni par la conservazione e la diffusione di massa dei testi. Se la perdita de entrate dai clienti individuali xe la principale preoccupazione, la distribuzione a livello personale podaria restar proibita. A sua volta, chi xe in grado de gestir vasti archivi — aziende che addestrano LLM, insieme a biblioteche e altri archivi — saria coperto da ste eccezioni."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Alcuni paesi sta già facendo una versione de questo. TorrentFreak <a %(torrentfreak)s>ga riportà</a> che la Cina e il Giappone ga introdotto eccezioni AI nelle loro leggi sul copyright. No xe chiaro per noi come questo interagisce con i trattati internazionali, ma certamente dà copertura alle loro aziende domestiche, il che spiega quello che gavemo visto."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Per quanto riguarda l'Archivio de Anna — continueremo il nostro lavoro sotterraneo radicato nella convinzione morale. Tuttavia, el nostro più grande desiderio xe de entrar nella luce, e amplificar il nostro impatto legalmente. Per favore, riformate il copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leggi gli articoli di accompagnamento da TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vincitori del premio da $10,000 per la visualizzazione dell'ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Gavemo ricevuto delle incredibili proposte per il premio da $10,000 per la visualizzazione dell'ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Qualche mese fa gavemo annunciato un <a %(all_isbns)s>premio da $10,000</a> par crear la miglior visualizzazione possibile dei nostri dati che mostra lo spazio ISBN. Gavemo enfatizzato il mostrar quali file gavemo/ga no ancora archivià, e successivamente un dataset che descrive quante biblioteche detiene ISBN (una misura de rarità)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Semo stai sopraffatti dalla risposta. Xe stada tanta creatività. Un grande grazie a tutti quelli che ga partecipà: la vostra energia e entusiasmo xe contagiosi!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "In definitiva, gavemo volesto risponder a ste domande: <strong>quai libri esisti nel mondo, quanti gavemo za archivià, e sui quali libri dovemo focalizarse dopo?</strong> Xe fantastico veder che tante persone se interessa de ste domande."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Gavemo inizià con una visualizzazione basica noi stessi. In manco de 300kb, sta imagine rappresenta in modo succinto la più grande “lista de libri” completamente aperta mai assemblà nella storia de l’umanità:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tuti i ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "File in l'Archivio de Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuga de dati CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indice de eBook de EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archivio Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registro Globale de Editori ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Statale Russa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperiale de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Indrio"

#, fuzzy
msgid "common.forward"
msgstr "Avanti"

#, fuzzy
msgid "common.last"
msgstr "Ultimo"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Varda el <a %(all_isbns)s>post orginal del blog</a> par più informazion."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Gavemo lancià un sfida par migliorar sto progetto. Gavemo deciso de premiar el primo posto con $6,000, el secondo con $3,000, e el terzo con $1,000. Dò l'enorme risposta e le incredibili proposte, gavemo deciso de ingrandir un poco el montepremi, e premiar quattro terzi posti con $500 ciascuno. I vincitori i xe qua soto, ma no te scordar de vardar tute le proposte <a %(annas_archive)s>qua</a>, o de scaricar el nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinà</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primo posto $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Sta <a %(phiresky_github)s>proposta</a> (<a %(annas_archive_note_2951)s>commento su Gitlab</a>) xe semplicemente tuto quel che volevimo, e anca de più! Gavemo particolarmente apprezzà le opzioni de visualizzazione incredibilmente flessibili (anca con shader personalizà), ma con una lista completa de preset. Gavemo anca apprezzà quanto tuto xe veloce e fluido, l'implementazion semplice (che no gà gnanca un backend), la minimappa ingegnosa, e l'ampia spiegazion nel loro <a %(phiresky_github)s>post del blog</a>. Un lavoro incredibile, e un vincitore ben merità!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Secondo posto $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Un'altra <a %(annas_archive_note_2913)s>proposta</a> incredibile. No xe flessibile come el primo posto, ma in realtà preferivimo la sua visualizzazione a livello macro rispetto al primo posto (curva che riempie lo spazio, confini, etichettatura, evidenziazione, panoramica e zoom). Un <a %(annas_archive_note_2971)s>commento</a> de Joe Davis che ne gà colpì:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Anche se i quadrati e i rettangoli perfetti xe matematicamente piacevoli, no i fornisce una superior località in un contesto de mappatura. Credo che l'asimmetria inerente a sti Hilbert o Morton classici no sia un difetto ma una caratteristica. Proprio come el contorno a forma de stivale dell'Italia la rende immediatamente riconoscibile su una mappa, le \"stranezze\" uniche de ste curve le può servire come punti de riferimento cognitivi. Sta distintività la può migliorare la memoria spaziale e aiutar gli utenti a orientarse, potenzialmente rendendo più facile localizzare regioni specifiche o notare schemi.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ancora tante opzioni par visualizzare e renderizzare, oltre a un'interfaccia utente incredibilmente fluida e intuitiva. Un solido secondo posto!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terzo posto $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In sta <a %(annas_archive_note_2940)s>proposta</a> gavemo veramente apprezzà i diversi tipi de viste, in particolare le viste de confronto e de editore."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terzo posto $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Anche se no gà l'interfaccia utente più raffinà, sta <a %(annas_archive_note_2917)s>proposta</a> soddisfa molti dei criteri. Gavemo particolarmente apprezzà la sua funzione de confronto."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terzo posto $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Come el primo posto, sta <a %(annas_archive_note_2975)s>proposta</a> ne gà impressionà con la sua flessibilità. In definitiva, questo xe quel che rende un strumento de visualizzazione eccezionale: massima flessibilità par gli utenti esperti, mantenendo le cose semplici par gli utenti medi."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terzo posto $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "L'ultima <a %(annas_archive_note_2947)s>proposta</a> a ricever un premio xe abbastanza basica, ma gà delle caratteristiche uniche che gavemo veramente apprezzà. Gavemo apprezzà come mostra quante datasets copre un particolare ISBN come misura de popolarità/affidabilità. Gavemo anca veramente apprezzà la semplicità ma efficacia de usar un cursore de opacità par i confronti."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idee notevoli"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Alcune altre idee e implementazioni che gavemo particolarmente apprezzà:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Grattacieli par rarità"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statìsteghe in tempo reale"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotazioni, e anca statìsteghe in tempo reale"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista unica de la mappa e filtri"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schema de colori de default figo e mappa de calore."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facile attivazion dei datasets par confronti rapidi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etichette carine."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de scala co numero de libri."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Tanti cursori par confrontar datasets, come se fussi un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Podemo continuar par un bel po', ma fermemose qua. Assicurateve de vardar tute le sottomission <a %(annas_archive)s>qua</a>, o scaricar el nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinà</a>. Tante sottomission, e ognuna porta na prospettiva unica, sia in UI che in implementazion."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Incorporaremo almanco la sottomission che ga ciapà el primo posto nel nostro sito principal, e forse anca altre. Gavemo anca inizià a pensar a come organizzar el processo de identificazion, conferma, e poi archiviazion dei libri più rari. Altro a vegner su sto fronte."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Grazie a tuti che ga partecipà. Xe straordinario che tante persone ghe tenga."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "I nostri cuori xe pieni de gratitudine."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizzar Tutti i ISBN — $10,000 de ricompensa entro el 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Sta imagine rapresenta la più grande “lista de libri” completamente aperta mai assemblà nella storia de l'umanità."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Sta imagine xe 1000×800 pixel. Ogni pixel rapresenta 2.500 ISBN. Se gavemo un file par un ISBN, rendemo quel pixel più verde. Se savemo che un ISBN xe stà emesso, ma no gavemo un file corispondente, lo rendemo più rosso."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In meno de 300kb, sta imagine rapresenta in modo conciso la più grande “lista de libri” completamente aperta mai assemblà nella storia de l'umanità (qualche centinaio de GB compressi in toto)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Mostra anca: ghe xe ancora tanto lavoro da far par far na copia de sicurezza dei libri (gavemo solo 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Sfondo"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Come pol può l'Archivio de Anna raggiunger el so obiettivo de far na copia de backup de tuto el sapere de l'umanità, senza saver quali libri xe ancora là fora? Gavemo bisogno de na lista de cose da far. Un modo de mapar questo xe tramite i numeri ISBN, che dai ani '70 i xe stai assegnai a ogni libro pubblicà (in la maggior parte dei paesi)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "No ghe xe na autorità centrale che sa tute le assegnazioni ISBN. Invece, xe un sistema distribuito, dove i paesi riceve range de numeri, che poi i assegna range più piccoli ai editori principali, che podaria ulteriormente suddivider i range ai editori minori. Finalmente i numeri individuali i xe assegnai ai libri."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Gavemo inizià a mapar i ISBN <a %(blog)s>do ani fa</a> col nostro scrape de ISBNdb. Da allora, gavemo scrapà molte altre fonti de metadata, come <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e altro. Na lista completa la se trova su le pagine “Datasets” e “Torrents” de l'Archivio de Anna. Gavemo adesso de gran lunga la più grande collezion completamente aperta e facilmente scaricabile de metadata de libri (e quindi ISBN) al mondo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Gavemo <a %(blog)s>scritto ampiamente</a> del perché ne importa la conservazion, e del perché semo attualmente in una finestra critica. Dovemo adesso identificar i libri rari, poco focalizai, e unicamente a rischio e conservarli. Aver boni metadata su tuti i libri del mondo ne aiuta in questo."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizazion"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Oltre all'immagine de visione generale, podemo anca guardar i datasets individuali che gavemo acquisì. Usa el menu a tendina e i bottoni per cambiar tra de lori."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Ghe xe tanti schemi interessanti da veder in queste immagini. Perché ghe xe una certa regolarità de linee e blocchi, che sembra succeder a scale diverse? Cosa xe le aree vuote? Perché certi datasets xe cussì raggruppai? Lasseremo queste domande come un esercizio per el lettore."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Taglia de $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Ghe xe tanto da esplorar qua, quindi stemo annunciando una taglia per migliorar la visualizazion qua sora. A differenza de la maggior parte delle nostre taglie, questa xe a tempo limità. Te devi <a %(annas_archive)s>inviar</a> el to codice open source entro el 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La mejo submission la riceverà $6,000, el secondo posto $3,000, e el terzo posto $1,000. Tute le taglie le sarà assegnà usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Qua sotto ghe xe i criteri minimi. Se nessuna submission rispetta i criteri, podemo ancora assegnar qualcossa de taglie, ma questo sarà a nostra discrezion."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forka sto repo, e modifica sto post del blog HTML (no altri backend oltre al nostro backend Flask xe permessi)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Fai che l'immagine qua sora sia zoomabile in modo fluido, cussì te podi zoomar fino ai ISBN individuali. Cliccar sui ISBN te dovrebbe portar a una pagina de metadata o a una ricerca su l'Archivio de Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Te devi ancora poder cambiar tra tute le diverse datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "I range dei paesi e i range degli editori dovaria esser evidenzià quando te ghe passi sopra col mouse. Te podi usar per esempio <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> per le info sui paesi, e el nostro scrape “isbngrp” per gli editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "El deve funzionare ben su desktop e mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Per punti bonus (questi xe solo idee — lascia che la to creatività corra libera):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sarà data forte considerazion all'usabilità e a quanto ben el se vede."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mostra el metadata reale per i ISBN individuali quando te zoomi, come el titolo e l'autore."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Miglior curva de riempimento dello spazio. Per esempio, un zig-zag, che va da 0 a 4 sulla prima riga e poi indrio (in reverso) da 5 a 9 sulla seconda riga — applicà ricorsivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Schemi de colori diversi o personalizzabili."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Viste speciali per confrontare i datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modi per risolvere problemi, come altri metadata che non concordano bene (ad esempio titoli molto diversi)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotare immagini con commenti su ISBN o intervalli."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualsiasi euristica per identificare libri rari o a rischio."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Qualunque idea creativa che ti venga in mente!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Puoi completamente deviare dai criteri minimi e fare una visualizzazione completamente diversa. Se è davvero spettacolare, allora si qualifica per la ricompensa, ma a nostra discrezione."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fai le tue proposte postando un commento a <a %(annas_archive)s>questo problema</a> con un link al tuo repo forkato, richiesta di merge o diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Codice"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Il codice per generare queste immagini, così come altri esempi, si trova in <a %(annas_archive)s>questa directory</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Abbiamo ideato un formato dati compatto, con il quale tutte le informazioni ISBN richieste sono circa 75MB (compresse). La descrizione del formato dati e il codice per generarlo si trovano <a %(annas_archive_l1244_1319)s>qui</a>. Per la ricompensa non sei obbligato a usare questo, ma è probabilmente il formato più conveniente per iniziare. Puoi trasformare i nostri metadata come vuoi (anche se tutto il tuo codice deve essere open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Non vediamo l'ora di vedere cosa inventerai. Buona fortuna!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contenitori de l'Archivio de Anna (AAC): standardizzare le pubblicazioni dalla più grande biblioteca ombra del mondo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "L'Archivio de Anna è diventato la più grande biblioteca ombra del mondo, richiedendo di standardizzare le nostre pubblicazioni."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> è diventato di gran lunga la più grande biblioteca ombra del mondo, e l'unica biblioteca ombra della sua scala che è completamente open-source e open-data. Di seguito è riportata una tabella dalla nostra pagina Datasets (leggermente modificata):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Abbiamo realizzato questo in tre modi:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Rispecchiando le biblioteche ombra open-data esistenti (come Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Aiutando le biblioteche ombra che vogliono essere più aperte, ma non avevano il tempo o le risorse per farlo (come la collezione di fumetti di Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raccogliendo dati da biblioteche che non desiderano condividere in massa (come Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Par (2) e (3) gestimo adesso na collezion considerevole de torrent da soli (centinaia de TB). Fin'ora gavemo trattà ste collezioni come casi unici, cioè infrastruttura e organizzazion dei dati su misura par ogni collezion. Questo aggiunge un sovraccarico significativo a ogni rilascio, e rende particolarmente difficile far rilasci più incrementali."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Par questo gavemo deciso de standardizzar i nostri rilasci. Questo xe un post tecnico del blog in cui stemo introduzendo el nostro standard: <strong>Contenitori de l'Archivio de Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Obiettivi del design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "El nostro caso d'uso principale xe la distribuzion de file e metadata associati da diverse collezioni esistenti. Le nostre considerazioni più importanti xe:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "File e metadata eterogenei, nel formato più vicino possibile a quello originale."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatori eterogenei nelle librerie sorgente, o anche mancanza de identificatori."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Rilasci separati de metadata rispetto ai dati dei file, o rilasci solo de metadata (ad esempio el nostro rilascio ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuzion tramite torrent, anche se con la possibilità de altri metodi de distribuzion (ad esempio IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registri immutabili, visto che dovemo assumere che i nostri torrent vivarà par sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Rilasci incrementali / rilasci appendibili."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Leggibile e scrivibile da macchine, in modo conveniente e veloce, specialmente par la nostra pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Ispezion umana relativamente facile, anche se questo xe secondario rispetto alla leggibilità da parte delle macchine."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile da seminare le nostre collezioni con un seedbox standard affittato."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "I dati binari può esser serviti direttamente da server web come Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Alcuni non-obiettivi:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "No ghe ne frega che i file sia facili da navigar manualmente su disco, o ricercabili senza pre-elaborazione."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "No ghe ne frega de esser direttamente compatibili con software de libreria esistente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Anche se dovria esser facile par chiunque seminare la nostra collezion usando i torrent, no ne aspettemo che i file sia utilizzabili senza conoscenze tecniche significative e impegno."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Visto che l'Archivio de Anna xe open source, volémo usar direttamente el nostro formato. Quando rinfreschemo el nostro indice de ricerca, accedemo solo ai percorsi pubblicamente disponibili, in modo che chiunque forki la nostra libreria possa avviarsi rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "El standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "In definitiva, gavemo deciso de adotar un standard relativamente semplice. El xe abbastanza flessibile, no normativo, e un lavoro in corso."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contenitore de l'Archivio de Anna) xe un singolo elemento composto da <strong>metadata</strong>, e opzionalmente da <strong>dati binari</strong>, che i xe entrambi immutabili. El ga un identificatore unico globale, chiamà <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collezion.</strong> Ogni AAC appartien a na collezion, che per definizion xe na lista de AAC che i xe semanticamente consistenti. Questo vol dir che se te fassi un cambiamento significativo al formato dei metadata, te devi crear na nova collezion."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Collezioni de “records” e “files”.</strong> Per convenzion, xe spesso conveniente rilasciar “records” e “files” come collezioni diverse, cussì che i possa esser rilascià a orari diversi, ad esempio basà sui tassi de scraping. Un “record” xe na collezion solo de metadata, che contiene informazioni come titoli de libri, autori, ISBN, ecc., mentre i “files” xe le collezioni che contiene i file veri e propri (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> El formato de l'AACID xe questo: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Per esempio, un AACID reale che gavemo rilascià xe <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: el nome de la collezion, che podi contener lettere ASCII, numeri e underscore (ma no doppi underscore)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: na versione corta de l'ISO 8601, sempre in UTC, ad esempio <code>20220723T194746Z</code>. Questo numero deve aumentare monotonamente per ogni rilascio, anche se i so esatti semantici podi differir per collezion. Suggerimo de usar el tempo de scraping o de generazion de l'ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificatore specifico de la collezion, se applicabile, ad esempio l'ID de Z-Library. Podi esser omesso o troncato. Deve esser omesso o troncato se l'AACID altrimenti superassi i 150 caratteri."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID ma compresso in ASCII, ad esempio usando base57. Attualmente usimo la libreria Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Intervallo AACID.</strong> Dato che i AACID contiene timestamp che aumenta monotonamente, podemo usarlo per indicare intervalli dentro una collezion particolare. Usimo questo formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, dove i timestamp xe inclusivi. Questo xe consistente con la notazion ISO 8601. Gli intervalli xe continui, e podi sovrapporsi, ma in caso de sovrapposizion deve contener record identici a quelli rilascià in precedenza in quella collezion (dato che i AAC xe immutabili). I record mancanti no xe permessi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>File de metadata.</strong> Un file de metadata contiene i metadata de un intervallo de AAC, per una collezion particolare. Questi ga le seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "El nome del file deve esser un intervallo AACID, prefissato con <code style=\"color: red\">annas_archive_meta__</code> e seguito da <code>.jsonl.zstd</code>. Per esempio, uno dei nostri rilasci xe chiamà<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Come indicato dall'estension del file, el tipo de file xe <a %(jsonlines)s>JSON Lines</a> compresso con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Ogni oggetto JSON deve contener i seguenti campi al livello superiore: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opzionale). No xe permessi altri campi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> xe metadata arbitrari, secondo i semantici de la collezion. Deve esser semanticamente consistenti dentro la collezion."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> xe opzionale, e xe el nome de la cartella de dati binari che contiene i dati binari corrispondenti. El nome del file de dati binari corrispondente dentro quella cartella xe l'AACID del record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "El prefisso <code style=\"color: red\">annas_archive_meta__</code> podi esser adattà al nome de la vostra istituzion, ad esempio <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Cartella de dati binari.</strong> Una cartella con i dati binari de un intervallo de AAC, per una collezion particolare. Questi ga le seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "El nome della directory deve esser un intervallo AACID, prefissato con <code style=\"color: green\">annas_archive_data__</code>, e senza suffisso. Per esempio, uno dei nostri rilasci reali ga una directory chiamà<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "La directory deve contener file de dati per tutti i AAC dentro l'intervallo specificato. Ogni file de dati deve aver el so AACID come nome del file (senza estensioni)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Xe racomandà de far sti cartèle un poco gestibili in dimension, par esemio no più grandi de 100GB-1TB ciascuna, anca se sta racomandazion podaria cambiar col tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> I file de metadata e le cartèle de dati binari podarìa vegner impacà in torrents, con un torrent par ogni file de metadata, o un torrent par ogni cartèla de dati binari. I torrents gà da aver el nome originale del file/directory più el suffisso <code>.torrent</code> come nome del file."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Esempio"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Diamo un'occhiata al nostro recente rilascio de Z-Library come esempio. El consiste de do collezioni: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Questo ne permette de raspare e rilasciare separatamente i record de metadata dai file dei libri veri e propri. Cussì, gavemo rilascià do torrents con i file de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Gavemo anca rilascià un mucchio de torrents con le cartèle de dati binari, ma solo par la collezion “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 in totale:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Eseguendo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemo veder cossa ghe xe dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In sto caso, xe el metadata de un libro come riportà da Z-Library. A livello superiore gavemo solo “aacid” e “metadata”, ma gnente “data_folder”, visto che no ghe xe dati binari corrispondenti. L'AACID contiene “22430000” come ID primario, che podemo veder xe tratto da “zlibrary_id”. Podemo aspettarse che altri AAC in sta collezion gà la stessa struttura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Adesso eseguimo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Questo xe un metadata AAC molto più piccolo, anca se la parte principale de sto AAC xe situà altrove in un file binario! Dopo tuto, stavolta gavemo un “data_folder”, cussì podemo aspettarse che i dati binari corrispondenti se trova a <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. El “metadata” contiene el “zlibrary_id”, cussì podemo facilmente associarlo col AAC corrispondente nella collezion “zlib_records”. Podarìmo associarlo in diversi modi, par esemio tramite AACID — el standard no prescrive questo."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nota che no xe necessario che el campo “metadata” sia in JSON. Podarìa esser una stringa che contiene XML o qualsiasi altro formato de dati. Podarìa anca memorizzar le informazioni de metadata nel blob binario associato, par esemio se xe tanti dati."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusione"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con sto standard, podemo far rilasci più incrementali e aggiunger più facilmente nuove fonti de dati. Gavemo già qualche rilascio interessante in programma!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Speremo anca che diventi più facile par altre biblioteche ombra de rispecchiar le nostre collezioni. Dopo tuto, el nostro obiettivo xe de preservar la conoscenza e la cultura umana par sempre, cussì più ridondanza ghe xe, mejo xe."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Aggiornamento de Anna: archivio completamente open source, ElasticSearch, più de 300GB de copertine de libri"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Gavemo lavorà senza sosta par fornir un'alternativa valida con l'Archivio de Anna. Ecco alcune delle cose che gavemo raggiunto recentemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Con Z-Library che xe andà giù e i suoi (presunti) fondatori arrestati, gavemo lavorà senza sosta par fornir un'alternativa valida con l'Archivio de Anna (no lo linkeremo qua, ma podè cercarlo su Google). Ecco alcune delle cose che gavemo raggiunto recentemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L'Archivio de Anna xe completamente open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Credemo che l'informazion dovaria esser libera, e el nostro codice no fa eccezion. Gavemo rilascià tuto el nostro codice sulla nostra istanza Gitlab ospitata privatamente: <a %(annas_archive)s>Software de Anna</a>. Usèmo anca el tracker de problemi par organizzar el nostro lavoro. Se volete partecipar al nostro sviluppo, questo xe un gran bel posto par iniziare."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Par darve un'idea delle cose su cui stemo lavorando, vedè el nostro lavoro recente sui miglioramenti delle prestazioni lato client. Visto che no gavemo ancora implementà la paginazion, spesso restituivimo pagine de ricerca molto lunghe, con 100-200 risultati. No volevimo tagliar i risultati de ricerca troppo presto, ma questo voleva dir che podarìa rallentar certi dispositivi. Par questo, gavemo implementà un piccolo trucco: gavemo avvolto la maggior parte dei risultati de ricerca nei commenti HTML (<code><!-- --></code>), e poi gavemo scritto un piccolo Javascript che rilevava quando un risultato doveva diventare visibile, al che gavemo scartato el commento:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualization\" implementà in 23 righe, no bisogno de librerie sofisticàe! Questo xe el tipo de codice pragmatico e veloce che te finissi par scrivar quando te gà poco tempo e problemi reali da risolver. Xe stà riferìo che la nostra ricerca adesso funziona ben anca sui dispositivi lenti!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un altro grande sforzo xe stà automatizar la costruzion del database. Quando gavemo lancià, gavemo semplicemente mescolà diverse fonti insieme. Adesso volémo tenerle aggiornàe, cusì gavemo scrito un mucchio de script par scaricar novi metadata dai do fork de Library Genesis e integrarli. El scopo xe no solo farlo utile par el nostro archivio, ma anca farlo facile par chiunque vogia sperimentar coi metadata delle biblioteche ombra. El scopo saria un notebook Jupyter che gà tute le sorte de metadata interessanti disponibili, cusì podemo far più ricerca come capir che <a %(blog)s>percentuale de ISBN xe conservà per sempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, gavemo rinnovà el nostro sistema de donazioni. Adesso te podi usar una carta de credito par depositar direttamente i schei nei nostri portafogli crypto, senza veramente dover saper niente de criptovalute. Continueremo a monitorar quanto ben funsiona in pratica, ma xe un gran passo avanti."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Passa a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Uno dei nostri <a %(annas_archive)s>ticket</a> era un insieme de problemi col nostro sistema de ricerca. Gavemo usà la ricerca full-text de MySQL, visto che gavemo tuti i nostri dati in MySQL comunque. Ma gà i so limiti:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Qualche query ci metteva un sacco de tempo, al punto che se ciapava tuti i collegamenti aperti."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "De default MySQL gà una lunghezza minima de parola, o l'indice podi diventare veramente grande. La gente riferiva de no poder cercar \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La ricerca era solo un po' veloce quando era completamente caricà in memoria, il che richiedeva de ciapar una macchina più costosa par farla girar, più qualche comando par precaricar l'indice all'avvio."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "No saremmo stati in grado de estenderla facilmente par costruir nuove funsionalità, come una mejo <a %(wikipedia_cjk_characters)s>tokenizzazione par le lingue senza spazi</a>, filtraggio/faccettatura, ordinamento, suggerimenti \"volevi dire\", autocompletamento, e cusì via."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Dopo aver parlà con un mucchio de esperti, gavemo deciso par ElasticSearch. No xe stà perfetto (i so suggerimenti \"volevi dire\" e le funsionalità de autocompletamento de default no xe granché), ma nel complesso xe stà molto mejo de MySQL par la ricerca. No semo ancora <a %(youtube)s>troppo entusiasti</a> de usarlo par dati critici (anche se gà fatto un sacco de <a %(elastic_co)s>progressi</a>), ma nel complesso semo abbastanza contenti del cambio."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Par adesso, gavemo implementà una ricerca molto più veloce, un mejo supporto linguistico, un mejo ordinamento per rilevanza, diverse opzioni de ordinamento, e filtraggio su lingua/tipo de libro/tipo de file. Se te sì curioso de come funsiona, <a %(annas_archive_l140)s>dai</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>ocio</a>. Xe abbastanza accessibile, anche se ghe serviria qualche commento in più…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ de copertine de libri rilasciàe"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, semo contenti de annunciar un piccolo rilascio. In collaborazione coi ragazzi che gestisse el fork Libgen.rs, stemo condividendo tute le so copertine de libri tramite torrent e IPFS. Questo distribuirà el carico de visualizzar le copertine tra più macchine e le conserverà mejo. In molti (ma no in tuti) casi, le copertine dei libri xe incluse nei file stessi, cusì xe un tipo de \"dati derivati\". Ma averle in IPFS xe ancora molto utile par l'operazione giornaliera sia de l'Archivio de Anna che dei vari fork de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Come al solito, te podi trovar questo rilascio al Pirate Library Mirror (EDIT: spostà a <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>). No linkeremo qua, ma te lo podi trovar facilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Speremo de poder rallentar un poco el nostro ritmo, adesso che gavemo una decente alternativa a Z-Library. Questo carico de lavoro no xe particolarmente sostenibile. Se te sì interessà a dar na man con la programmazione, le operazioni sui server, o el lavoro de conservazione, contatene. Ghe xe ancora un sacco de <a %(annas_archive)s>lavoro da far</a>. Grazie par el tuo interesse e supporto."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "L'Archivio de Anna gà salvà la più grande biblioteca ombra de fumetti del mondo (95TB) — te podi aiutar a semearla"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La più grande biblioteca ombra de fumetti del mondo gà un unico punto de fallimento... fin a oggi."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discùti su Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La più grande biblioteca ombra de fumetti xe probabilmente quella de un particolare fork de Library Genesis: Libgen.li. L'unico amministratore che gestisse quel sito xe riuscìo a collezionar una collezion de fumetti incredibile de più de 2 milioni de file, par un totale de più de 95TB. Tuttavia, a differenza de altre collezioni de Library Genesis, questa no era disponibile in blocco tramite torrent. Te podi accedere a sti fumetti solo individualmente tramite el so server personale lento — un unico punto de fallimento. Fin a oggi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In sto post ve contarémoghe de più su sta collezion, e su la nostra raccolta fondi par sostegnir più de sto lavoro."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>La dottoressa Barbara Gordon prova a perderse nel mondo ordinario de la biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fork de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Prima, un poco de contesto. Podarìa che conossé Library Genesis par la so collezion epica de libri. Meno persone sa che i volontari de Library Genesis ga creato altri progetti, come una vasta collezion de riviste e documenti standard, un backup completo de Sci-Hub (in colaborazion con la fondatrice de Sci-Hub, Alexandra Elbakyan), e in effetti, una collezion massiccia de fumetti."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "A un certo punto, diversi operatori dei mirror de Library Genesis ga preso strade diverse, che ga portà alla situazion attuale de aver diversi “fork”, tuti che porta ancora el nome de Library Genesis. El fork Libgen.li ga in modo unico sta collezion de fumetti, oltre a una vasta collezion de riviste (che stemo lavorando anca su quela)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaborazion"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada la so dimension, sta collezion xe da tempo su la nostra lista dei desideri, cusì dopo el nostro successo col backup de Z-Library, gavemo puntà a sta collezion. Al inizio la gavemo raschià direttamente, che xe stado un bel sfido, visto che el so server no gera in le migliori condizion. Gavemo recuperà circa 15TB in sto modo, ma xe stado lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Fortunatamente, gavemo riusì a metterse in contatto col operatore de la biblioteca, che ga accettà de mandarne tuti i dati direttamente, che xe stado molto più veloce. Xe ancora volù più de mezo ano par trasferir e processar tuti i dati, e gavemo quasi perso tuto per colpa de un corrompimento del disco, che saria volù dir ricominciar da capo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Sta esperienza ne ga fatto creder che xe importante far girar sti dati il più presto possibile, cusì che i possa esser replicà in largo e in lungo. Semoghe solo a uno o do incidenti sfortunai de perder sta collezion par sempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La collezion"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Muoversi veloce vol dir che la collezion xe un poco disorganizzata… Diamo un’occhiata. Imagina de aver un filesystem (che in realtà stemo dividendo tra i torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "El primo directory, <code>/repository</code>, xe la parte più strutturà de questo. Sto directory contiene i cosiddetti “mille dir”: directory ciascuna con mile file, che xe numerai incrementalmente nel database. El directory <code>0</code> contiene file con comic_id da 0 a 999, e cusì via."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Xe el stesso schema che Library Genesis ga usà par le so collezioni de narrativa e saggistica. L’idea xe che ogni “mille dir” vien automaticamente trasformà in un torrent appena xe pieno."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tuttavia, l’operatore de Libgen.li no ga mai fatto torrent par sta collezion, e cusì i mille dir probabilmente xe diventai scomodi, e ga lascià spazio ai “dir non ordinai”. Questi xe <code>/comics0</code> fino a <code>/comics4</code>. Tuti i ga strutture de directory uniche, che probabilmente ga senso par raccoglier i file, ma no ga tanto senso par noialtri adesso. Fortunatamente, el metadata fa ancora riferimento diretto a tuti sti file, cusì la so organizazion su disco no importa realmente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "El metadata xe disponibile in forma de un database MySQL. Questo se pol scaricar direttamente dal sito de Libgen.li, ma lo renderemo disponibile anca in un torrent, insieme alla nostra tabella con tuti i hash MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analisi"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quando te te trovi 95TB scaricai nel tuo cluster de storage, te provi a capir cosa ghe xe dentro… Gavemo fato un’analisi par veder se podemo ridur la dimension un poco, come par esempio eliminando i duplicati. Ecco alcune delle nostre scoperte:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "I duplicati semantici (diverse scansioni del stesso libro) teoreticamente se pol filtrar fora, ma xe complicà. Quando gavemo guardà manualmente tra i fumetti gavemo trovà troppi falsi positivi."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Ghe xe alcuni duplicati puri per MD5, che xe relativamente spreco, ma filtrar fora quei ne darebbe solo circa 1% in de risparmio. A sta scala xe ancora circa 1TB, ma anca, a sta scala 1TB no importa realmente. Preferimo no rischiar de distrugger dati accidentalmente in sto processo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Gavemo trovà un mucchio de dati non-libro, come film basai su fumetti. Anca questo pare spreco, visto che questi xe già ampiamente disponibili per altri mezzi. Tuttavia, gavemo capì che no podemo semplicemente filtrar fora i file de film, visto che ghe xe anca <em>fumetti interattivi</em> che xe stai rilascià sul computer, che qualcuno ga registrà e salvà come film."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "In fin dei conti, qualsiasi cosa che potessimo eliminare dalla collezione risparmierebbe solo pochi percentuali. Poi ci siamo ricordati che siamo accumulatori di dati, e le persone che faranno il mirroring di questo sono anche accumulatori di dati, e quindi, “COSA VUOI DIRE, ELIMINARE?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Pertanto, vi presentiamo la collezione completa e non modificata. È un sacco di dati, ma speriamo che abbastanza persone si prendano cura di condividerli comunque."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Raccolta fondi"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Stiamo rilasciando questi dati in alcuni grandi blocchi. Il primo torrent è di <code>/comics0</code>, che abbiamo messo in un enorme file .tar da 12TB. È meglio per il tuo disco rigido e il software torrent rispetto a una miriade di file più piccoli."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Come parte di questo rilascio, stiamo facendo una raccolta fondi. Stiamo cercando di raccogliere $20,000 per coprire i costi operativi e di contrattazione per questa collezione, oltre a consentire progetti in corso e futuri. Abbiamo alcuni <em>enormi</em> in lavorazione."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Chi sto supportando con la mia donazione?</em> In breve: stiamo facendo il backup di tutta la conoscenza e la cultura dell'umanità, rendendola facilmente accessibile. Tutto il nostro codice e i dati sono open source, siamo un progetto gestito completamente da volontari, e abbiamo salvato finora 125TB di libri (oltre ai torrent esistenti di Libgen e Scihub). In definitiva, stiamo costruendo un volano che consente e incentiva le persone a trovare, scansionare e fare il backup di tutti i libri del mondo. Scriveremo del nostro piano maestro in un post futuro. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se doni per un abbonamento di 12 mesi “Amazing Archivist” ($780), puoi <strong>“adottare un torrent”</strong>, il che significa che metteremo il tuo nome utente o messaggio nel nome di uno dei torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Puoi donare andando su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> e cliccando sul pulsante “Dona”. Stiamo anche cercando più volontari: ingegneri software, ricercatori di sicurezza, esperti di commercio anonimo e traduttori. Puoi anche supportarci fornendo servizi di hosting. E naturalmente, per favore condividi i nostri torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Grazie a tutti coloro che ci hanno già supportato così generosamente! State davvero facendo la differenza."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Ecco i torrent rilasciati finora (stiamo ancora elaborando il resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tutti i torrent possono essere trovati su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a> sotto “Datasets” (non colleghiamo direttamente lì, quindi i link a questo blog non vengono rimossi da Reddit, Twitter, ecc.). Da lì, segui il link al sito Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Cosa c'è dopo?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un mucchio di torrent sono ottimi per la conservazione a lungo termine, ma non tanto per l'accesso quotidiano. Lavoreremo con partner di hosting per mettere tutti questi dati sul web (dato che L'Archivio de Anna non ospita nulla direttamente). Naturalmente, potrai trovare questi link di download su L'Archivio de Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Invitiamo anche tutti a fare qualcosa con questi dati! Aiutaci ad analizzarli meglio, deduplicarli, metterli su IPFS, remixarli, addestrare i tuoi modelli AI con essi, e così via. Sono tutti tuoi, e non vediamo l'ora di vedere cosa farai con essi."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Infine, come detto prima, abbiamo ancora alcuni rilasci enormi in arrivo (se <em>qualcuno</em> potesse <em>accidentalmente</em> inviarci un dump di un <em>certo</em> database ACS4, sapete dove trovarci...), oltre a costruire il volano per fare il backup di tutti i libri del mondo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Quindi restate sintonizzati, abbiamo appena iniziato."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nuovi libri aggiunti al Pirate Library Mirror (+24TB, 3.8 milioni di libri)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Nel rilascio originale del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>), abbiamo creato un mirror di Z-Library, una grande collezione di libri illegale. Come promemoria, ecco cosa abbiamo scritto in quel post originale del blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono facilmente replicabile la loro collezione, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "No femo giudizi morali su chi che ciàrca schei par l'accesso in massa a na coleçion de libri ilegali. L'è fora de ogni dubio che la Z-Library la gà avudo sucesso nel espandere l'accesso ala conoscensa e nel trovar più libri. Semu qua solo par far la nostra parte: assicurar la conservazion a longo termine de sta coleçion privata."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Sta coleçion la risale a metà 2021. Intanto, la Z-Library la gà cresudo a un ritmo impressionante: i gà zontà circa 3.8 milioni de libri novi. Ghe xe qualche duplicato, certo, ma la maggior parte par eser libri novi legittimi, o scan de qualità più alta de libri già inviadi. Questo xe in gran parte dovudo al numero aumentà de moderatori volontari ala Z-Library, e al loro sistema de caricamento in massa con deduplicazion. Volemo congratularse con lori par sti risultati."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Semo contenti de annunciar che gavemo recuperà tuti i libri che i xe stai zontà ala Z-Library tra l'ultimo specio e agosto 2022. Gavemo anca tornà indrio e raschià qualche libro che gavemo perso la prima volta. In totale, sta nova coleçion xe circa 24TB, che xe molto più granda de l'ultima (7TB). El nostro specio adesso xe 31TB in totale. Ancora, gavemo deduplicà contro Library Genesis, visto che ghe xe già torrent disponibili par sta coleçion."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Par piaser, va al Pirate Library Mirror par veder la nova coleçion (EDIT: spostà su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>). Ghe xe più informazion là su come i file xe strutturadi, e cosa xe cambià da l'ultima volta. No ghe metaremo un link da qua, visto che xe solo un sito de blog che no ospita materiali ilegali."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Naturalmente, far seeding xe anca un gran modo par darne na man. Grazie a tuti che xe in seeding coi nostri torrent precedenti. Semu grati par la risposta positiva, e contenti che ghe xe tante persone che se ciàpa de la conservazion de la conoscensa e de la cultura in sto modo inusuale."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Come diventar un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "El primo sfido podarìa eser un che sorprende. No xe un problema tecnico, o un problema legale. Xe un problema psicologico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Prima de tuffarse, do aggiornamenti sul Pirate Library Mirror (EDIT: spostà su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Gavemo ricevù delle donazion estremamente generose. La prima xe stada de $10k da un individuo anonimo che gà anca supportà \"bookwarrior\", el fondador originale de Library Genesis. Un ringraziamento speciale a bookwarrior par aver facilità sta donazion. La seconda xe stada un altro $10k da un donatore anonimo, che se gà messo in contatto dopo la nostra ultima release, e xe stado ispirà a darne na man. Gavemo anca avudo un numero de donazion più piccole. Grazie tante par tuto el vostro supporto generoso. Gavemo dei progetti novi e entusiasmanti in cantiere che sto supporto aiuterà, quindi restè sintonizadi."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Gavemo avudo qualche difficoltà tecnica co la dimension de la nostra seconda release, ma i nostri torrent xe su e in seeding adesso. Gavemo anca ricevù un'offerta generosa da un individuo anonimo par far seeding de la nostra coleçion sui loro server ad altissima velocità, quindi stemo facendo un caricamento speciale sui loro computer, dopo de che tuti che sta scaricando la coleçion dovarìa veder un gran miglioramento in velocità."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Libri interi podarìa eser scritti sul <em>perché</em> de la conservazion digital in generale, e de l'archivismo pirata in particolare, ma laseme dar un breve primer par chi no xe tanto familiar. El mondo sta producendo più conoscensa e cultura che mai, ma anca più de ela se sta perdendo che mai. L'umanità la se fida in gran parte de le corporazion come editori accademici, servizi de streaming, e compagnie de social media par sto patrimonio, e spesso no i gà dimostrà de eser gran custodi. Varda el documentario Digital Amnesia, o veramente qualsiasi discorso de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ghe xe qualche istituzion che fa un bon lavoro de archiviare quanto più che pol, ma i xe vincoladi dalla legge. Come pirati, semo in una posizione unica par archiviare coleçion che lori no pol toccar, par via de l'applicazion del copyright o altre restrizion. Podemo anca speciar coleçion molte volte, in tuto el mondo, aumentando cusì le possibilità de una conservazion adeguada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Par adesso, no intraremo in discussioni sui pro e i contro de la proprietà intellettuale, la moralità de infrangere la legge, riflessioni sulla censura, o la questione de l'accesso ala conoscensa e ala cultura. Con tuto questo fuori dai piedi, tuffemose nel <em>come</em>. Condivideremo come la nostra squadra xe diventada archivisti pirati, e le lezioni che gavemo imparà lungo el percorso. Ghe xe molti sfidi quando te intraprendi sto viaggio, e speremo de poderte aiutar a superar qualcuna de ele."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunità"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "El primo sfido podarìa eser un che sorprende. No xe un problema tecnico, o un problema legale. Xe un problema psicologico: far sto lavoro nell'ombra pol eser incredibilmente solitario. Dipendendo da cosa te stai pianificando de far, e dal tuo modello de minaccia, te podarìa dover eser molto attento. Da un lato del spettro gavemo persone come Alexandra Elbakyan*, la fondadora de Sci-Hub, che xe molto aperta sulle sue attività. Ma ela xe a alto rischio de eser arrestada se la visitasse un paese occidentale a sto punto, e podarìa affrontar decenni de prigionia. Xe un rischio che te sarìa disposto a correr? Semo dall'altro lato del spettro; stemo molto attenti a no lasar nessuna traccia, e avemo una forte sicurezza operativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Come menzionà su HN da \"ynno\", inizialmente Alexandra no voleva eser conosuda: \"I suoi server iera configuradi par emettere messaggi de errore dettagliadi da PHP, inclusi el percorso completo del file sorgente che causava el problema, che iera sotto la directory /home/<USER>" Quindi, usa nomi utente casuali sui computer che te usi par sta roba, nel caso te configuri qualcosa male."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Sta segretezza, comunque, la gà un costo psicologico. La maggior parte delle persone ama eser riconosciuda par el lavoro che fa, e invece no te podi prenderti nessun merito par questo nella vita reale. Anca le cose semplici podarìa eser sfidanti, come amici che te domanda cosa te gà fato de recente (a un certo punto \"smanettar col mio NAS / homelab\" diventa vecio)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Xe per questo che xe cusì importante trovar na comunità. Te podi rinunciar a un po' de sicurezza operativa confidandote con qualche amico molto stretto, che te sai che te podi fidar profondamente. Anca in quel caso stai attento a no metar niente per iscritto, nel caso che i debba consegnar le loro email alle autorità, o se i loro dispositivi xe compromessi in qualche altro modo."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Meglio ancora xe trovar qualche altro pirata. Se i tuoi amici stretti xe interessadi a unirse a ti, fantastico! Altrimenti, te podarìa trovar altri online. Purtroppo sta xe ancora una comunità di nicchia. Finora gavemo trovà solo un pugno de altri che xe attivi in sto spazio. Bon punti de partenza parè che sia i forum de Library Genesis, e r/DataHoarder. L'Archive Team gà anca individui con idee simili, anca se i opera dentro la legge (anca se in qualche area grigia de la legge). Le scene tradizionali de \"warez\" e pirateria gà anca persone che pensa in modi simili."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Semo drioi a idee su come favorir la comunità e esplorar idee. Sentìvì liberi de contatàrne su Twitter o Reddit. Forse podarìmo ospitar un tipo de forum o gruppo de ciacole. Un problema xe che questo pol facilmente vegnir censurà quando se dopara piattaforme comuni, cusì dovarìmo ospitarlo da soli. Ghe xe anca un compromesso tra far ste discussioni completamente publiche (più potenziale de coinvolgimento) contro farle private (no far saver ai potenziali \"obiettivi\" che stemo per raschiarli). Dovarìmo pensar a questo. Fène saver se sìe interessà a questo!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Progetti"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quando femo un progetto, el gà un par de fasi:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selezion de dominio / filosofia: Dove voì più o meno focalizarve, e parché? Quali xe le vostre passioni, abilità e circostanze uniche che podè doparar a vostro vantaggio?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selezion de obiettivo: Quale collezion specifica voì specchiar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raschiar de metadata: Catalogar informazioni sui file, senza realmente scaricar i file stessi (che spesso xe molto più grandi)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selezion de dati: Basandose sui metadata, restringer quali dati xe più rilevanti da archiviare adesso. Podarìa esser tuto, ma spesso ghe xe un modo ragionevole de risparmiar spazio e banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raschiar de dati: Realmente ottener i dati."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuzion: Impacchetarlo in torrent, annunciarlo da qualche parte, farlo diffonder tra la gente."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Queste no xe fasi completamente indipendenti, e spesso intuizioni da una fase successiva te manda indrio a una fase precedente. Par esempio, durante el raschiar de metadata podarìa capir che l'obiettivo che gà selezionà gà meccanismi difensivi oltre el tuo livello de abilità (come blocchi IP), cusì te torni indrio e trovi un altro obiettivo."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selezion de dominio / filosofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "No ghe xe carenza de conoscenza e patrimonio culturale da salvar, che pol esser opprimente. Xe per questo che spesso xe utile fermarse un momento e pensar a cosa podè contribuire."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Ognuno gà un modo diverso de pensar a questo, ma qua xe alcune domande che podarìa farve:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Parché sìe interessà a questo? Cosa ve appassiona? Se podemo metter insieme un gruppo de persone che archivia tuti i tipi de robe che ghe interessa specificamente, copriremo un bel po’! Saverè molto più de la persona media su la vostra passione, come quali xe i dati importanti da salvar, quali xe le mejo collezioni e comunità online, e cusì via."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quali abilità gà che podè doparar a vostro vantaggio? Par esempio, se sìe esperti de sicurezza online, podè trovar modi de superar i blocchi IP par obiettivi sicuri. Se sìe bravi a organizzar comunità, allora forse podè radunar un po’ de persone intorno a un obiettivo. Xe utile saver un po’ de programmazione però, anche solo par mantener una bona sicurezza operativa durante sto processo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quanto tempo gà par questo? El nostro consiglio saria de cominciar piccolo e far progetti più grandi man mano che te ciapi la man, ma pol diventar tutto-assorbente."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Quale saria un'area de alto impatto su cui focalizarse? Se te spenderà X ore su archiviazione pirata, allora come podè ottener el massimo \"ritorno par el tuo investimento\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quali xe i modi unici in cui te stai pensando a questo? Podarìa gaver idee o approcci interessanti che altri podarìa aver perso."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Nel nostro caso, ne importava in particolare de la conservazion a lungo termine de la scienza. Savèmo de Library Genesis, e come che xe stata completamente specchiata molte volte usando i torrent. Ne piaseva sta idea. Poi un giorno, uno de noi gà provà a trovar dei libri de testo scientifici su Library Genesis, ma no li gà trovà, mettendo in dubbio quanto completa realmente fosse. Poi gavemo cercà quei libri de testo online, e li gavemo trovà in altri posti, che gà piantà el seme par el nostro progetto. Anca prima de saver del Z-Library, gavemo avù l'idea de no provàr a collezionar tuti quei libri manualmente, ma de focalizarse su specchiar collezioni esistenti, e contribuirle indrio a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selezion de obiettivo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Dunque, gavemo la nostra area che stemo vardando, ora quale collezion specifica dovemo speciar? Ghe xe un par de robe che fa un bon obiettivo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grando"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unico: no xe già ben coperto da altri progetti."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessibile: no usa un sacco de strati de protezion par impedirte de raschiare i so metadata e dati."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Intuizion speciale: ti gà qualche informazion speciale su sto obiettivo, come se ti gà accesso speciale a sta collezion, o ti gà capìo come superar le so difese. No xe richiesto (el nostro progetto in arrivo no fa niente de speciale), ma certamente aiuta!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quando gavemo trovà i nostri libri de scienza su siti diversi da Library Genesis, gavemo provà a capir come i xe finìi su internet. Poi gavemo trovà Z-Library, e gavemo capìo che anca se la maggior parte dei libri no fa la so prima apparizion là, i finisse là alla fine. Gavemo imparà del so rapporto con Library Genesis, e la struttura de incentivi (finanziari) e l'interfaccia utente superiore, che i fa de 'sta collezion una molto più completa. Poi gavemo fato un po' de raschiamento preliminare de metadata e dati, e gavemo capìo che podemo superar i so limiti de download IP, sfruttando l'accesso speciale de un dei nostri membri a tanti server proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Mentre esplori diversi obiettivi, xe già importante nasconder le tue tracce usando VPN e indirizzi email usa e getta, de cui parleremo più avanti."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Raschiamento de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Diventemo un poco più tecnici qua. Par raschiare effettivamente i metadata dai siti web, gavemo tenùo le robe abbastanza semplici. Usémo script Python, a volte curl, e un database MySQL par salvar i risultati. No gavemo usà software de raschiamento sofisticà che pol mappare siti complessi, visto che finora gavemo solo bisogno de raschiare uno o do tipi de pagine enumerando semplicemente i ID e parsando l'HTML. Se no ghe xe pagine facilmente enumerabili, allora te podarìa aver bisogno de un crawler adeguà che prova a trovar tutte le pagine."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Prima de iniziare a raschiare un sito intero, prova a farlo manualmente par un po'. Passa par un par de dozzine de pagine da solo, par capir come funziona. A volte te incrocerai già blocchi IP o altri comportamenti interessanti in sto modo. Lo stesso vale par il raschiamento dei dati: prima de andar troppo in profondità su sto obiettivo, assicurate de poder effettivamente scaricar i so dati in modo efficace."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Par superar le restrizioni, ghe xe un par de robe che te podi provare. Ghe xe altri indirizzi IP o server che ospita i stessi dati ma no gà le stesse restrizioni? Ghe xe endpoint API che no gà restrizioni, mentre altri sì? A che velocità de download el tuo IP vien bloccà, e par quanto tempo? O no vieni bloccà ma rallentà? Cosa succede se te crei un account utente, come cambia le robe allora? Te podi usar HTTP/2 par tener le connessioni aperte, e questo aumenta la velocità con cui te podi richieder le pagine? Ghe xe pagine che elenca più file in una volta, e le informazioni elencate là xe sufficienti?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Le robe che probabilmente te vorrai salvar include:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titolo"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome file / posizione"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pol esser un ID interno, ma ID come ISBN o DOI xe utili anca."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dimensione: par calcolare quanto spazio su disco te gà bisogno."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): par confermar che te gà scaricà el file correttamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data aggiunta/modificata: così te podi tornar più tardi e scaricar file che no gà scaricà prima (anche se spesso te podi anca usar l'ID o l'hash par questo)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrizion, categoria, tag, autori, lingua, ecc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Tipicamente, lo facemo in do fasi. Prima scaricamo i file HTML grezzi, solitamente direttamente in MySQL (par evitar tanti file piccoli, de cui parliamo più sotto). Poi, in un passaggio separato, passiamo par quei file HTML e i parsiamo in tabelle MySQL effettive. In questo modo no te gà bisogno de riscaricar tutto da zero se te scopri un errore nel tuo codice de parsing, visto che te podi semplicemente riprocessar i file HTML col nuovo codice. Xe anca spesso più facile parallelizzare el passaggio de processamento, risparmiando così un po' de tempo (e te podi scriver el codice de processamento mentre el raschiamento xe in corso, invece de dover scriver entrambi i passaggi in una volta)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, nota che per certi obiettivi lo scraping dei metadata xe tuto quel che ghe xe. Ghe xe alcune colezzioni de metadata enormi là fora che no xe conservà adeguatamente."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selezion de dati"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Spesso te pol doparar i metadata par capir un sottoinsieme ragionevole de dati da scaricar. Anca se alla fine te volessi scaricar tuti i dati, pol esser utile dar priorità ai elementi più importanti prima, nel caso te vegni scoperto e le difese vegni migliorà, o perché te gaveressi bisogno de comprar più dischi, o semplicemente perché qualcos'altro te capita in te la vita prima che te riesci a scaricar tuto."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Par esempio, una colezzion podaria gaver più edizioni de la stessa risorsa de base (come un libro o un film), dove una xe segnalà come la mejo qualità. Salvare quelle edizioni prima faria tanto senso. Te podarissi alla fine voler salvar tuti i edizioni, visto che in certi casi i metadata podaria esser etichettà in modo incorreto, o podaria esserci compromessi sconosciuti tra le edizioni (par esempio, la \"mejo edizion\" podaria esser la mejo in molti modi ma pezo in altri, come un film che gà una risoluzion più alta ma manca de sottotitoli)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Te pol anca cercar nel tuo database de metadata par trovar robe interessanti. Qual xe el file più grande che xe ospitato, e parché xe cussì grande? Qual xe el file più piccolo? Ghe xe schemi interessanti o inaspettati quando se tratta de certe categorie, lingue, e cussì via? Ghe xe titoli duplicati o molto simili? Ghe xe schemi su quando i dati xe stati aggiunti, come un giorno in cui molti file xe stati aggiunti in una volta? Spesso te pol imparar tanto guardando el dataset in modi diversi."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Nel nostro caso, gavemo deduplicà i libri de Z-Library contro i hash md5 in Library Genesis, risparmiando cussì tanto tempo de scaricamento e spazio su disco. Questa xe una situazion abbastanza unica però. Nella maggior parte dei casi no ghe xe database completi de quali file xe già conservà adeguatamente dai pirati compagni. Questo in sé xe una grande opportunità par qualcun là fora. Saria fantastico gaver una panoramica aggiornata regolarmente de robe come musica e film che xe già ampiamente seminà sui siti torrent, e che quindi xe de priorità più bassa da includer nei specchi pirata."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scraping de dati"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Adesso te xe pronto par scaricar effettivamente i dati in massa. Come menzionà prima, a questo punto te dovresti già aver scaricà manualmente un bel po' de file, par capir mejo el comportamento e le restrizioni del obiettivo. Comunque, ghe sarà ancora sorprese in serbo par ti una volta che te inizi a scaricar tanti file in una volta."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "El nostro consiglio qua xe principalmente de tenerlo semplice. Inizia semplicemente scaricando un bel po' de file. Te pol doparar Python, e poi espander a più thread. Ma a volte anca più semplice xe generar file Bash direttamente dal database, e poi eseguirne più in più finestre terminal par scalar. Un rapido trucco tecnico che vale la pena menzionare qua xe doparar OUTFILE in MySQL, che te pol scriver ovunque se te disabiliti \"secure_file_priv\" in mysqld.cnf (e assicurati de disabilitar/override anca AppArmor se te xe su Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Gavemo i dati su semplici dischi rigidi. Inizia con quel che gà, e espandi lentamente. Pol esser opprimente pensar de conservar centinaia de TB de dati. Se questa xe la situazion che te stai affrontando, metti fora prima un buon sottoinsieme, e nel tuo annuncio chiedi aiuto par conservar el resto. Se te volessi comprar più dischi rigidi da solo, allora r/DataHoarder gà delle buone risorse su come ottener buoni affari."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Prova a no preoccuparti troppo de filesystem sofisticati. Xe facile cader nel buco del coniglio de configurar robe come ZFS. Un dettaglio tecnico de cui esser consapevoli però, xe che molti filesystem no gestisse ben tanti file. Gavemo trovà che un semplice workaround xe crear più directory, ad esempio par diversi range de ID o prefissi hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Dopo aver scaricà i dati, assicurati de controllar l'integrità dei file doparando i hash nei metadata, se disponibili."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuzion"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Gavì i dati, dandovi cussì el possesso del primo specchio pirata al mondo del vostro obiettivo (probabilmente). In molti modi la parte più difficile xe finìa, ma la parte più rischiosa xe ancora davanti a voi. Dopo tuto, finora gavì stai furtivi; volando sotto el radar. Tuto quel che dovevi far xe doparar un buon VPN per tuto el tempo, no compilar i vostri dati personali in nessun modulo (ovvio), e forse doparar una sessione browser speciale (o anca un computer diverso)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Adesso dovì distribuir i dati. Nel nostro caso volevimo prima contribuire i libri indrio a Library Genesis, ma poi gavì scoperto rapidamente le difficoltà in questo (ordinamento fiction vs non-fiction). Cussì gavì deciso de distribuir doparando torrent in stile Library Genesis. Se gavì l'opportunità de contribuire a un progetto esistente, allora questo podaria risparmiarvi tanto tempo. Comunque, attualmente no ghe xe molti specchi pirata ben organizzà là fora."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Cussì diciamo che decidì de distribuir i torrent da soli. Prova a tener quei file piccoli, cussì xe facili da specchiar su altri siti. Dovì poi seminare i torrent da soli, restando comunque anonimi. Te pol doparar un VPN (con o senza port forwarding), o pagar con Bitcoin mescolà par un Seedbox. Se no savì cosa vol dir alcuni de quei termini, gavì un bel po' de lettura da far, visto che xe importante che capì i compromessi de rischio qua."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Te pol ospitar i file torrent stessi su siti torrent esistenti. Nel nostro caso, gavì scelto de ospitar effettivamente un sito web, visto che volevimo anca diffonder la nostra filosofia in modo chiaro. Te pol far questo da solo in modo simile (doparimo Njalla par i nostri domini e hosting, pagà con Bitcoin mescolà), ma sentitevi liberi de contattarci par farci ospitar i vostri torrent. Gavì intenzion de costruir un indice completo de specchi pirata col tempo, se questa idea prende piede."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Per quanto riguarda la selezion del VPN, xe già stato scritto tanto su questo, cussì ripeteremo solo el consiglio generale de sceglier in base alla reputazion. Politiche de no-log testate in tribunale con lunghe storie de protezion della privacy xe l'opzion de rischio più basso, secondo noi. Nota che anca quando fai tuto giusto, no te pol mai arrivar a zero rischio. Par esempio, quando semini i tuoi torrent, un attore statale altamente motivato pol probabilmente guardar i flussi de dati in entrata e in uscita par i server VPN, e dedurre chi te xe. O te pol semplicemente sbagliar in qualche modo. Probabilmente gavì già fatto, e lo faremo ancora. Fortunatamente, i stati nazionali no se preoccupa <em>così</em> tanto de la pirateria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una decision par far par ogni progetto, xe se pubblicarlo doparando la stessa identità de prima, o no. Se continui a doparar lo stesso nome, allora i errori nella sicurezza operativa dei progetti precedenti podaria tornar a morderti. Ma pubblicar sotto nomi diversi significa che no costruisci una reputazion più duratura. Gavì scelto de aver una forte sicurezza operativa fin dall'inizio cussì podemo continuare a doparar la stessa identità, ma no esiteremo a pubblicar sotto un nome diverso se sbagliamo o se le circostanze lo richiede."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Diffonder la parola pol esser complicato. Come gavì detto, questa xe ancora una comunità di nicchia. Originariamente gavì postato su Reddit, ma veramente gavì ottenuto trazione su Hacker News. Par adesso la nostra raccomandazion xe de postarlo in pochi posti e veder cosa succede. E ancora, contattateci. Gavì piacerebbe diffonder la parola de più sforzi de archivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusione"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Speremo che questo sia utile per i nuovi archivisti pirati che stanno iniziando. Siamo entusiasti di darvi il benvenuto in questo mondo, quindi non esitate a contattarci. Preserviamo quanta più conoscenza e cultura del mondo possibile e riflettiamola ovunque."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentando il Mirror della Biblioteca Pirata: Preservando 7TB di libri (che non sono in Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Questo progetto (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>) mira a contribuire alla preservazione e liberazione della conoscenza umana. Facciamo il nostro piccolo e umile contributo, seguendo le orme dei grandi che ci hanno preceduto."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "L'obiettivo di questo progetto è illustrato dal suo nome:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Violiamo deliberatamente la legge sul copyright nella maggior parte dei paesi. Questo ci permette di fare qualcosa che le entità legali non possono fare: assicurarci che i libri siano riflessi ovunque."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Come la maggior parte delle biblioteche, ci concentriamo principalmente su materiali scritti come i libri. Potremmo espanderci in altri tipi di media in futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Siamo strettamente uno specchio di biblioteche esistenti. Ci concentriamo sulla preservazione, non sul rendere i libri facilmente ricercabili e scaricabili (accesso) o sul favorire una grande comunità di persone che contribuiscono con nuovi libri (approvvigionamento)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La prima biblioteca che abbiamo riflesso è Z-Library. Questa è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono con questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono la loro collezione facilmente riflettibile, il che impedisce una vasta preservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "No femo giudizi morali su chi che ciàrca schei par l'accesso in massa a na coleçion de libri ilegali. L'è fora de ogni dubio che la Z-Library la gà avudo sucesso nel espandere l'accesso ala conoscensa e nel trovar più libri. Semu qua solo par far la nostra parte: assicurar la conservazion a longo termine de sta coleçion privata."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Vorremmo invitarvi ad aiutare a preservare e liberare la conoscenza umana scaricando e seminando i nostri torrent. Vedi la pagina del progetto per ulteriori informazioni su come sono organizzati i dati."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Invitiamo anche molto a contribuire con le vostre idee su quali collezioni riflettere successivamente e come procedere. Insieme possiamo ottenere molto. Questo è solo un piccolo contributo tra innumerevoli altri. Grazie, per tutto ciò che fate."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Non colleghiamo i file da questo blog. Per favore, trovateli da soli.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump di ISBNdb, o Quanti Libri Sono Preservati per Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se dovessimo deduplicare correttamente i file dalle biblioteche ombra, quale percentuale di tutti i libri del mondo abbiamo preservato?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Con il Mirror della Biblioteca Pirata (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>L'Archivio de Anna</a>), il nostro obiettivo è prendere tutti i libri del mondo e preservarli per sempre.<sup>1</sup> Tra i nostri torrent di Z-Library e i torrent originali di Library Genesis, abbiamo 11.783.153 file. Ma quanti sono davvero? Se deduplicassimo correttamente quei file, quale percentuale di tutti i libri del mondo abbiamo preservato? Ci piacerebbe davvero avere qualcosa del genere:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of del patrimonio scritto dell'umanità preservato per sempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Per una percentuale, abbiamo bisogno di un denominatore: il numero totale di libri mai pubblicati.<sup>2</sup> Prima della fine di Google Books, un ingegnere del progetto, Leonid Taycher, <a %(booksearch_blogspot)s>ha cercato di stimare</a> questo numero. Ha proposto — scherzosamente — 129.864.880 (“almeno fino a domenica”). Ha stimato questo numero costruendo un database unificato di tutti i libri del mondo. Per questo, ha raccolto diversi datasets e poi li ha uniti in vari modi."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Parlando de sfuggita, ghe xe un'altra persona che ga provà a catalogar tuti i libri del mondo: Aaron Swartz, el defunto attivista digital e co-fondador de Reddit.<sup>3</sup> El <a %(youtube)s>ga inizià Open Library</a> col scopo de \"una pagina web par ogni libro mai pubblicà\", combinando dati da tante fonti diverse. El ga finì par pagar el prezzo più alto par el so lavoro de preservazion digital quando el xe stà perseguità par aver scaricà in massa articoli accademici, portandolo al suicidio. Inutile dirlo, questa xe una delle ragioni par cui el nostro gruppo xe pseudonimo, e par cui semo molto attenti. Open Library xe ancora gestì eroicamente da persone de Internet Archive, continuando l'eredità de Aaron. Torneremo su questo più avanti in questo post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Nel post del blog de Google, Taycher descrive alcuni dei problemi nel stimar questo numero. Prima de tuto, cosa costituisce un libro? Ghe xe alcune definizioni possibili:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copie fisiche.</strong> Ovviamente questo no xe molto utile, visto che xe solo duplicati del stesso materiale. Saria fantastico se podessimo preservare tuti i annotazioni che le persone fa nei libri, come i famosi \"scarabocchi nei margini\" de Fermat. Ma purtroppo, questo rimarrà un sogno par un archivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Opere”.</strong> Par esempio “Harry Potter e la Camera dei Segreti” come concetto logico, che comprende tuti i versioni de esso, come traduzioni diverse e ristampe. Questa xe una definizione abbastanza utile, ma può esser difficile tracciare il confine de cosa conta. Par esempio, probabilmente vogliamo preservare traduzioni diverse, anche se le ristampe con solo piccole differenze potrebbe no esser così importanti."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edizioni”.</strong> Qua conti ogni versione unica de un libro. Se qualcosa de esso xe diverso, come una copertina diversa o una prefazione diversa, conta come un'edizione diversa."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>File.</strong> Quando se lavora con biblioteche ombra come Library Genesis, Sci-Hub, o Z-Library, ghe xe un'altra considerazione. Ghe può esser multiple scansioni del stessa edizione. E le persone può far versioni migliori dei file esistenti, scansionando el testo usando OCR, o correggendo pagine che xe state scansionate a un angolo. Vogliamo contare questi file come una sola edizione, il che richiederebbe un buon metadata, o deduplicazione usando misure de similarità dei documenti."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edizioni” sembra la definizione più pratica de cosa xe i “libri”. Comodamente, questa definizione xe anche usata par assegnar numeri ISBN unici. Un ISBN, o International Standard Book Number, xe comunemente usato par il commercio internazionale, visto che xe integrato col sistema internazionale de codici a barre (\"International Article Number\"). Se vuoi vender un libro nei negozi, el ga bisogno de un codice a barre, quindi te ottieni un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "El post del blog de Taycher menziona che mentre i ISBN xe utili, no xe universali, visto che xe stati veramente adottati solo a metà degli anni settanta, e no dappertutto nel mondo. Tuttavia, l'ISBN xe probabilmente l'identificatore più ampiamente usato delle edizioni dei libri, quindi xe il nostro miglior punto de partenza. Se riusciamo a trovar tuti i ISBN del mondo, otteniamo una lista utile de quali libri ancora bisogna preservare."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Allora, dove otteniamo i dati? Ghe xe un numero de sforzi esistenti che sta cercando de compilare una lista de tuti i libri del mondo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Dopo tuto, i ga fatto questa ricerca par Google Books. Tuttavia, il loro metadata no xe accessibile in massa e piuttosto difficile da raschiare."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Come menzionato prima, questo xe il loro intero scopo. I ga ottenuto enormi quantità de dati bibliotecari da biblioteche cooperanti e archivi nazionali, e continua a farlo. I ga anche bibliotecari volontari e un team tecnico che sta cercando de deduplicare i record, e taggarli con ogni sorta de metadata. La cosa migliore xe che il loro dataset xe completamente aperto. Te pol semplicemente <a %(openlibrary)s>scaricarlo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Questo xe un sito web gestito dal non-profit OCLC, che vende sistemi de gestione bibliotecaria. I aggrega metadata dei libri da tante biblioteche, e lo rende disponibile attraverso il sito web WorldCat. Tuttavia, i fa anche soldi vendendo questi dati, quindi no xe disponibile par il download in massa. I ga alcuni dataset in massa più limitati disponibili par il download, in cooperazione con biblioteche specifiche."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Questo xe il tema de questo post del blog. ISBNdb raschia vari siti web par il metadata dei libri, in particolare dati sui prezzi, che poi i vende ai librai, così i può prezzare i loro libri in accordo col resto del mercato. Visto che i ISBN xe abbastanza universali oggigiorno, i ga effettivamente costruito una \"pagina web par ogni libro\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Vari sistemi bibliotecari individuali e archivi.</strong> Ghe xe biblioteche e archivi che no xe stati indicizzati e aggregati da nessuno dei sopra menzionati, spesso perché xe sottofinanziati, o par altri motivi no vogliono condividere i loro dati con Open Library, OCLC, Google, e così via. Molti de questi ga record digitali accessibili attraverso internet, e spesso no xe molto ben protetti, quindi se vuoi aiutar e divertirti imparando sui strani sistemi bibliotecari, questi xe ottimi punti de partenza."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In questo post, semo felici de annunciar un piccolo rilascio (rispetto ai nostri precedenti rilasci de Z-Library). Gavemo raschiato la maggior parte de ISBNdb, e reso i dati disponibili par il torrenting sul sito web del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>; no lo linkeremo direttamente qua, basta cercarlo). Questi xe circa 30.9 milioni de record (20GB come <a %(jsonlines)s>JSON Lines</a>; 4.4GB compressi). Sul loro sito web i dichiara che in realtà i ga 32.6 milioni de record, quindi potremmo aver perso alcuni, o <em>loro</em> potrebbe star facendo qualcosa de sbagliato. In ogni caso, par ora no condivideremo esattamente come gavemo fatto — lasceremo questo come un esercizio par il lettore. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Quello che condivideremo xe un'analisi preliminare, par cercar de avvicinarci a stimar il numero de libri nel mondo. Gavemo guardato a tre dataset: questo nuovo dataset de ISBNdb, il nostro rilascio originale de metadata che gavemo raschiato dalla biblioteca ombra Z-Library (che include Library Genesis), e il dump de dati de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Cominciamo con alcuni numeri approssimativi:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In entrambi Z-Library/Libgen e Open Library ghe xe molti più libri che ISBN unici. Questo significa che molti de quei libri no ga ISBN, o xe semplicemente mancante il metadata dell'ISBN? Probabilmente possiamo rispondere a questa domanda con una combinazione de abbinamento automatico basato su altri attributi (titolo, autore, editore, ecc.), integrando più fonti de dati, e estraendo i ISBN dalle scansioni effettive dei libri stessi (nel caso de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quanti de quei ISBN xe unici? Questo xe meglio illustrato con un diagramma di Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Par esser più precisi:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Semo stai sorpresi da quanto poco se sovraponi! ISBNdb ga un gran numero de ISBN che no i compare né in Z-Library né in Open Library, e lo stesso val (in misura minore ma ancora sostanziale) par i altri do. Questo solleva molte nuove domande. Quanto aiutaria un abbinamento automatico nel etichettare i libri che no iera etichettai con ISBN? Ghe saria tanti abbinamenti e quindi un aumento del sovraponimento? Inoltre, cosa succederia se portassimo dentro un 4° o 5° dataset? Quanto sovraponimento vedremmo allora?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Questo ne dà un punto de partenza. Podemo adesso guardar tutti i ISBN che no iera nel dataset de Z-Library, e che no i corrisponde neanche ai campi titolo/autore. Questo ne podaria dar un manico su come preservar tutti i libri del mondo: prima raschiando internet par scansioni, poi andando fora nella vita reale par scannerizzar i libri. Quest'ultimo podaria anche esser finanzià da la folla, o guidà da \"taglie\" da parte de persone che voria veder certi libri digitalizai. Tutto questo xe una storia par un altro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se te vol dar na man con qualcossa de questo — ulteriore analisi; raschiar più metadata; trovar più libri; OCR dei libri; far questo par altri domini (per esempio articoli, audiolibri, film, programmi TV, riviste) o anche render qualcossa de questi dati disponibili par robe come ML / addestramento di modelli di linguaggio ampi — contateme (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se te xe specificamente interessà a l'analisi dei dati, stemo lavorando par render i nostri Datasets e script disponibili in un formato più facile da usar. Saria fantastico se te podessi semplicemente forkare un notebook e cominciar a giocar con questo."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Infine, se te vol sostener questo lavoro, considera de far na donazion. Questa xe un'operazione gestida interamente da volontari, e la tua contribuzion fa una gran differenza. Ogni piccolo aiuto conta. Par adesso accettemo donazioni in crypto; vedi la pagina Donazioni su l'Archivio de Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Par qualche definizion ragionevole de \"per sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Naturalmente, l'eredità scritta de l'umanità xe molto più de libri, specialmente oggigiorno. Par il bene de questo post e dei nostri rilasci recenti stemo focalizandose sui libri, ma i nostri interessi se estende più in là."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Ghe xe molto de più che se podaria dir su Aaron Swartz, ma volemo solo menzionarlo brevemente, visto che el gioca un ruolo centrale in questa storia. Col passar del tempo, più persone podaria incontrar el suo nome par la prima volta, e successivamente tuffarse nel buco del coniglio da sole."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La finestra critica delle biblioteche ombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Come podemo affermar de preservar le nostre collezioni in perpetuo, quando le xe già vicine a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Version cinese 中文版</a>, discuti su <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Su l'Archivio de Anna, spesso ne vien chiesto come podemo affermar de preservar le nostre collezioni in perpetuo, quando la dimension totale xe già vicina a 1 Petabyte (1000 TB), e continua a crescer. In questo articolo vedremo la nostra filosofia, e vedremo perché il prossimo decennio xe critico par la nostra missione de preservar la conoscenza e la cultura de l'umanità."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "La <a %(annas_archive_stats)s>dimension totale</a> delle nostre collezioni, negli ultimi mesi, suddivisa par numero de seeders dei torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorità"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Perché ne importa tanto dei articoli e dei libri? Mettemo da parte la nostra credenza fondamentale nella preservazion in generale — podemo scriver un altro post su questo. Allora perché articoli e libri specificamente? La risposta xe semplice: <strong>densità de informazion</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Par megabyte de archiviazion, el testo scritto conserva più informazion de tutti i altri media. Anche se ne importa sia della conoscenza che della cultura, ne importa de più della prima. In generale, troviamo una gerarchia de densità de informazion e importanza de preservazion che se presenta più o meno così:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Articoli accademici, riviste, rapporti"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dati organici come sequenze di DNA, semi di piante, o campioni microbici"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libri di saggistica"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Codice software de scienza e ingegneria"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dati de misurazion come misure scientifiche, dati economici, raporti aziendali"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Siti web de scienza e ingegneria, discussioni online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Riviste de non-fiction, giornali, manuali"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Trascrizioni de non-fiction de discorsi, documentari, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dati interni de corporazioni o governi (fughe de notizie)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Record de metadata in generale (de non-fiction e fiction; de altri media, arte, persone, ecc.; incluse recensioni)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dati geografici (p.e. mappe, rilievi geologici)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Trascrizioni de procedimenti legali o giudiziari"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versioni finzionali o de intrattenimento de tuti i elementi sopra"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La classifica in sta lista xe un poco arbitraria — diversi elementi xe pari o ghe xe disacordi dentro al nostro team — e probabilmente stemo dimenticando qualcossa de importante. Ma grosso modo xe cussì che prioritizemo."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Qualchedun de sti elementi xe massa diversi dai altri par che ne preoccupemo (o xe già curati da altre istituzioni), come i dati organici o i dati geografici. Ma la maggior parte dei elementi in sta lista xe veramente importanti par noi."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un altro grande fattore nella nostra prioritizazion xe quanto a rischio xe un certo lavoro. Preferimo focalizarse su lavori che xe:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rari"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamente sottofocalizzati"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamente a rischio de distruzion (p.e. da guerra, tagli de fondi, cause legali, o persecuzion politica)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Infine, ne importa la scala. Gavemo tempo e soldi limitai, quindi preferimo passar un mese a salvar 10.000 libri piuttosto che 1.000 libri — se xe più o meno de ugual valore e a rischio."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteche ombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ghe xe tante organizzazioni che ga missioni simili, e priorità simili. In effetti, ghe xe biblioteche, archivi, laboratori, musei, e altre istituzioni incaricate de la conservazion de sto tipo. Tante de queste xe ben finanziate, da governi, individui, o corporazioni. Ma ghe xe un grande punto cieco: il sistema legale."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Qua sta el ruolo unico delle biblioteche ombra, e la rason par cui l'Archivio de Anna esiste. Podemo far robe che altre istituzioni no xe permesse de far. Adesso, no xe (spesso) che podemo archiviare materiali che xe illegali da conservare altrove. No, xe legale in tanti posti costruir un archivio con qualsiasi libri, articoli, riviste, e cussì via."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ma ciò che spesso manca ai archivi legali è la <strong>ridondanza e la longevità</strong>. Esistono libri di cui esiste solo una copia in qualche biblioteca fisica da qualche parte. Esistono record di metadata custoditi da una sola corporazione. Esistono giornali conservati solo su microfilm in un unico archivio. Le biblioteche possono subire tagli ai finanziamenti, le corporazioni possono fallire, gli archivi possono essere bombardati e bruciati fino a terra. Questo non è ipotetico — succede continuamente."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "La cosa che possiamo fare in modo unico su l'Archivio de Anna è conservare molte copie delle opere, su larga scala. Possiamo raccogliere articoli, libri, riviste e altro, e distribuirli in massa. Attualmente lo facciamo tramite torrent, ma le tecnologie esatte non importano e cambieranno nel tempo. La parte importante è ottenere molte copie distribuite in tutto il mondo. Questa citazione di oltre 200 anni fa è ancora vera:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ciò che è perso non può essere recuperato; ma salviamo ciò che rimane: non con volte e serrature che li proteggono dagli occhi e dall'uso del pubblico, consegnandoli al logorio del tempo, ma con una tale moltiplicazione di copie, da metterle al di là della portata degli incidenti.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Una breve nota sul dominio pubblico. Poiché l'Archivio de Anna si concentra in modo unico su attività che sono illegali in molti luoghi del mondo, non ci preoccupiamo delle collezioni ampiamente disponibili, come i libri di dominio pubblico. Le entità legali spesso se ne prendono già cura. Tuttavia, ci sono considerazioni che a volte ci portano a lavorare su collezioni pubblicamente disponibili:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "I record di metadata possono essere visualizzati liberamente sul sito Worldcat, ma non scaricati in massa (fino a quando non li abbiamo <a %(worldcat_scrape)s>estratti</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Il codice può essere open source su Github, ma Github nel suo insieme non può essere facilmente replicato e quindi preservato (anche se in questo caso particolare ci sono copie sufficientemente distribuite della maggior parte dei repository di codice)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit è gratuito da usare, ma recentemente ha introdotto misure anti-scraping rigorose, a seguito dell'addestramento di LLM affamati di dati (ne parleremo più avanti)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Una moltiplicazione di copie"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tornando alla nostra domanda originale: come possiamo affermare di preservare le nostre collezioni in perpetuo? Il problema principale qui è che la nostra collezione è <a %(torrents_stats)s>cresciuta</a> rapidamente, estraendo e rendendo open-source alcune collezioni massicce (oltre al lavoro straordinario già svolto da altre biblioteche ombra di dati aperti come Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Questa crescita dei dati rende più difficile replicare le collezioni in tutto il mondo. L'archiviazione dei dati è costosa! Ma siamo ottimisti, soprattutto osservando le seguenti tre tendenze."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Abbiamo raccolto i frutti più facili"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Questo segue direttamente dalle nostre priorità discusse sopra. Preferiamo lavorare prima sulla liberazione di grandi collezioni. Ora che abbiamo assicurato alcune delle collezioni più grandi del mondo, ci aspettiamo che la nostra crescita sia molto più lenta."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "C'è ancora una lunga coda di collezioni più piccole, e nuovi libri vengono scansionati o pubblicati ogni giorno, ma il tasso sarà probabilmente molto più lento. Potremmo ancora raddoppiare o addirittura triplicare di dimensioni, ma su un periodo di tempo più lungo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. I costi di archiviazione continuano a diminuire esponenzialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Al momento della scrittura, i <a %(diskprices)s>prezzi dei dischi</a> per TB sono circa $12 per dischi nuovi, $8 per dischi usati e $4 per nastro. Se siamo conservatori e guardiamo solo ai dischi nuovi, significa che archiviare un petabyte costa circa $12,000. Se assumiamo che la nostra biblioteca triplicherà da 900TB a 2.7PB, ciò significherebbe $32,400 per replicare l'intera biblioteca. Aggiungendo elettricità, costo di altro hardware, e così via, arrotondiamo a $40,000. O con nastro più come $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Da un lato <strong>$15,000–$40,000 per la somma di tutta la conoscenza umana è un affare</strong>. Dall'altro lato, è un po' eccessivo aspettarsi tonnellate di copie complete, specialmente se vorremmo anche che quelle persone continuassero a seminare i loro torrent a beneficio degli altri."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Questo è oggi. Ma il progresso avanza:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "I costi dei dischi rigidi per TB sono stati ridotti di circa un terzo negli ultimi 10 anni e probabilmente continueranno a diminuire a un ritmo simile. Il nastro sembra seguire una traiettoria simile. I prezzi degli SSD stanno scendendo ancora più velocemente e potrebbero superare i prezzi degli HDD entro la fine del decennio."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendenze dei prezzi degli HDD da fonti diverse (clicca per visualizzare lo studio)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se questo si mantiene, allora tra 10 anni potremmo guardare solo $5,000–$13,000 per replicare l'intera collezione (1/3), o anche meno se cresciamo meno in dimensioni. Anche se ancora molti soldi, questo sarà raggiungibile per molte persone. E potrebbe essere ancora meglio a causa del prossimo punto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Miglioramenti nella densità d'informazione"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Attualmente conserviamo i libri nei formati grezzi in cui ci vengono forniti. Certo, sono compressi, ma spesso sono ancora grandi scansioni o fotografie delle pagine."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Fino ad ora, le uniche opzioni per ridurre la dimensione totale della nostra collezione sono state una compressione più aggressiva o la deduplicazione. Tuttavia, per ottenere risparmi significativi, entrambe sono troppo perdenti per i nostri gusti. La compressione pesante delle foto può rendere il testo appena leggibile. E la deduplicazione richiede un'alta fiducia che i libri siano esattamente gli stessi, il che è spesso troppo impreciso, specialmente se i contenuti sono gli stessi ma le scansioni sono fatte in occasioni diverse."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "C'è sempre stata una terza opzione, ma la sua qualità è stata così pessima che non l'abbiamo mai considerata: <strong>OCR, o Riconoscimento Ottico dei Caratteri</strong>. Questo è il processo di conversione delle foto in testo semplice, utilizzando l'IA per rilevare i caratteri nelle foto. Gli strumenti per questo esistono da tempo e sono stati abbastanza decenti, ma \"abbastanza decenti\" non è sufficiente per scopi di conservazione."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tuttavia, i recenti modelli di deep learning multimodali hanno fatto progressi estremamente rapidi, sebbene ancora a costi elevati. Ci aspettiamo che sia l'accuratezza che i costi migliorino drasticamente nei prossimi anni, al punto che diventerà realistico applicarli all'intera nostra biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Miglioramenti dell'OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quando ciò accadrà, probabilmente conserveremo ancora i file originali, ma in aggiunta potremmo avere una versione molto più piccola della nostra biblioteca che la maggior parte delle persone vorrà duplicare. Il punto è che il testo grezzo stesso si comprime ancora meglio ed è molto più facile da deduplicare, offrendoci ancora più risparmi."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "In generale, non è irrealistico aspettarsi almeno una riduzione di 5-10 volte nella dimensione totale dei file, forse anche di più. Anche con una riduzione conservativa di 5 volte, staremmo guardando a <strong>$1,000–$3,000 in 10 anni anche se la nostra biblioteca triplicasse di dimensioni</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Finestra critica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se queste previsioni sono accurate, ci <strong>basterà aspettare un paio d'anni</strong> prima che l'intera nostra collezione venga ampiamente duplicata. Così, nelle parole di Thomas Jefferson, \"posta al di là della portata degli incidenti\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Purtroppo, l'avvento degli LLM e il loro addestramento affamato di dati ha messo molti detentori di copyright sulla difensiva. Ancora più di quanto già non fossero. Molti siti web stanno rendendo più difficile il scraping e l'archiviazione, le cause legali volano, e nel frattempo le biblioteche fisiche e gli archivi continuano a essere trascurati."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Possiamo solo aspettarci che queste tendenze continuino a peggiorare e che molte opere vadano perse ben prima di entrare nel pubblico dominio."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Siamo alla vigilia di una rivoluzione nella conservazione, ma <q>ciò che è perso non può essere recuperato.</q></strong> Abbiamo una finestra critica di circa 5-10 anni durante la quale è ancora abbastanza costoso gestire una biblioteca ombra e creare molti duplicati in tutto il mondo, e durante la quale l'accesso non è stato ancora completamente chiuso."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se riusciamo a colmare questa finestra, allora avremo davvero conservato la conoscenza e la cultura dell'umanità in perpetuo. Non dovremmo lasciare che questo tempo vada sprecato. Non dovremmo lasciare che questa finestra critica si chiuda su di noi."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Andiamo."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accesso esclusivo per le aziende LLM alla più grande collezione di libri di saggistica cinese al mondo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versione cinese 中文版</a>, <a %(news_ycombinator)s>Discuti su Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archivio di Anna ha acquisito una collezione unica di 7,5 milioni / 350TB di libri di saggistica cinese — più grande di Library Genesis. Siamo disposti a dare a un'azienda LLM l'accesso esclusivo, in cambio di OCR di alta qualità ed estrazione del testo.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Questo è un breve post sul blog. Stiamo cercando un'azienda o un'istituzione che ci aiuti con l'OCR e l'estrazione del testo per una collezione massiccia che abbiamo acquisito, in cambio di un accesso esclusivo anticipato. Dopo il periodo di embargo, rilasceremo ovviamente l'intera collezione."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "El testo accademico de alta qualità xe estremamente utile par el training dei LLM. Anca se la nostra collezion xe in cinese, dovaria esser utile par el training dei LLM in inglese: i modelli par che i incodiga conceti e conoscenze indipendentemente da che lingua vien dal."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Par questo, el testo ga da esser estrato dai scan. Cosa ghe cava fora l'Archivio de Anna? La ricerca testuale completa dei libri par i so utenti."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Perché i nostri obiettivi i xe in linea con quei dei sviluppatori de LLM, semo in cerca de un collaboratore. Semoghe disposti a darve <strong>accesso anticipà esclusivo a sta collezion in massa par 1 ano</strong>, se podè far un OCR e un'estrazion del testo adeguà. Se semoghe disposti a condividi el codice intero del vostro pipeline con noi, semoghe disposti a embargo la collezion par più tempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pagine de esempio"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Par dimostrarne che gavè un bon pipeline, qua ghe xe delle pagine de esempio par cominciar, da un libro sui superconduttori. El vostro pipeline dovaria gestir adeguatamente la matematica, le tabelle, i grafici, le note a piè de pagina, e cussì via."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Mandè le vostre pagine processà al nostro email. Se le par ben, ve mandaremo de più in privato, e ne aspettemo che siate in grado de far girar rapidamente el vostro pipeline anca su quei. Una volta che semo soddisfatti, podemo far un accordo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collezion"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Qualche informazion in più sulla collezion. <a %(duxiu)s>Duxiu</a> xe un'enorme database de libri scanà, creato dal <a %(chaoxing)s>SuperStar Digital Library Group</a>. La maggior parte xe libri accademici, scanà par renderli disponibili digitalmente a università e biblioteche. Par el nostro pubblico anglofono, <a %(library_princeton)s>Princeton</a> e l'<a %(guides_lib_uw)s>Università de Washington</a> ga delle bone panoramiche. Ghe xe anca un articolo eccellente che dà più contesto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercalo nell'Archivio de Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "I libri da Duxiu xe stati a lungo piratà su l'internet cinese. De solito i vien vendui par meno de un dollaro dai rivenditori. I xe tipicamente distribuiti usando l'equivalente cinese de Google Drive, che spesso xe stato hackerato par permetter più spazio de archiviazion. Alcuni dettagli tecnici i se trova <a %(github_duty_machine)s>qua</a> e <a %(github_821_github_io)s>qua</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Anca se i libri xe stati distribuiti semi-pubblicamente, xe abbastanza difficile ottenerli in massa. Gavemo messo questo in alto nella nostra lista de cose da far, e gavemo allocato più mesi de lavoro a tempo pieno par questo. Comunque, recentemente un volontario incredibile, straordinario e talentuoso ne ga contatà, dicendone che ga già fatto tutto sto lavoro — a grande spesa. I ne ga condiviso la collezion intera, senza aspettarse niente in cambio, eccetto la garanzia de preservazion a lungo termine. Veramente straordinario. I ga accettà de chieder aiuto in sto modo par far l'OCR della collezion."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La collezion xe 7.543.702 file. Xe più de Library Genesis non-fiction (circa 5,3 milioni). La dimensione totale dei file xe circa 359TB (326TiB) nella sua forma attuale."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Semo aperti ad altre proposte e idee. Basta contatarne. Consultè l'Archivio de Anna par più informazion sulle nostre collezioni, sforzi de preservazion, e come podè aiutar. Grazie!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avviso: sto post del blog xe stato deprecato. Gavemo deciso che IPFS no xe ancora pronto par el grande pubblico. Continueremo a linkar ai file su IPFS dall'Archivio de Anna quando xe possibile, ma no lo ospiteremo più noi stessi, né raccomandemo ad altri de farne il mirror usando IPFS. Consultè la nostra pagina dei Torrents se volè aiutar a preservare la nostra collezion."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Aiuta a seedar Z-Library su IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Come gestir una biblioteca ombra: operazioni all'Archivio de Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "No ghe xe <q>AWS par le carità ombra,</q> cussì come gestimo l'Archivio de Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Gestisco <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, el più grande motore de ricerca open-source no-profit al mondo par le <a %(wikipedia_shadow_library)s>biblioteche ombra</a>, come Sci-Hub, Library Genesis, e Z-Library. El nostro obiettivo xe render la conoscenza e la cultura facilmente accessibili, e in definitiva costruir una comunità de persone che insieme archivia e preserva <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tutti i libri del mondo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In sto articolo ve mostrerò come gestimo sto sito web, e le sfide uniche che vien con l'operar un sito web con uno stato legale discutibile, visto che no ghe xe un “AWS par le carità ombra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Consultè anca l'articolo gemelo <a %(blog_how_to_become_a_pirate_archivist)s>Come diventare un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Token de innovazion"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Cominciemo col nostro stack tecnologico. El xe deliberatamente noioso. Usémo Flask, MariaDB e ElasticSearch. E xe letteralmente tuto qua. La ricerca xe un problema in gran parte risolto, e no gavemo intenzion de reinventarlo. In più, gavemo da spender i nostri <a %(mcfunley)s>token de innovazion</a> in qualcos'altro: no esser butai zo da le autorità."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Dunque quanto xe legale o illegale l'Archivio de Anna esattamente? Questo dipende principalmente dalla giurisdizion legale. La maggior parte dei paesi crede in qualche forma de copyright, che significa che le persone o le aziende xe assegnai un monopolio esclusivo su certi tipi de opere per un certo periodo de tempo. Come nota a margine, all'Archivio de Anna credemo che, anca se ghe xe dei benefici, in generale el copyright xe un net-negativo par la società — ma questa xe una storia par un'altra volta."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Sto monopolio esclusivo su certe opere significa che xe illegale par chiunque fora de sto monopolio distribuire direttamente ste opere — inclusi noi. Ma l'Archivio de Anna xe un motore de ricerca che no distribuisce direttamente ste opere (almeno no sul nostro sito clearnet), quindi dovressimo esser a posto, giusto? No esattamente. In molte giurisdizioni no xe solo illegale distribuire opere protette da copyright, ma anca collegarse a posti che lo fa. Un esempio classico de questo xe la legge DMCA dei Stati Uniti."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Questa xe l'estremità più rigida dello spettro. All'altra estremità dello spettro ghe potrebbe teoricamente esser paesi senza leggi sul copyright, ma questi no esiste veramente. Praticamente ogni paese ga qualche forma de legge sul copyright nei libri. L'applicazione xe un'altra storia. Ghe xe tanti paesi con governi che no se preoccupa de far rispettar le leggi sul copyright. Ghe xe anca paesi in mezzo ai due estremi, che proibisce de distribuire opere protette da copyright, ma no proibisce de collegarse a ste opere."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Un'altra considerazion xe a livello aziendale. Se un'azienda opera in una giurisdizion che no se preoccupa del copyright, ma l'azienda stessa no xe disposta a correr rischi, allora potrebbe chiudere el vostro sito appena qualcun se lamenta."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Infine, una grande considerazion xe i pagamenti. Dato che gavemo bisogno de restar anonimi, no podemo usar i metodi de pagamento tradizionali. Questo ne lascia con le criptovalute, e solo un piccolo sottoinsieme de aziende le supporta (ghe xe carte de debito virtuali pagate con cripto, ma spesso no le xe accettate)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architettura del sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Dunque, poniamo che gavé trovà delle aziende disposte a ospitar el vostro sito senza chiuderve — chiamiamole “fornitori amanti della libertà” 😄. Scoprirete rapidamente che ospitar tuto con loro xe piuttosto costoso, quindi potreste voler trovar dei “fornitori economici” e far l'hosting effettivo lì, proxyando attraverso i fornitori amanti della libertà. Se lo fate bene, i fornitori economici no saprà mai cosa sté ospitando, e no riceverà mai lamentele."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con tuti sti fornitori ghe xe el rischio che ve chiuda comunque, quindi gavé bisogno anca de ridondanza. Gavemo bisogno de questo a tuti i livelli del nostro stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Un'azienda un po' amante della libertà che se ga messa in una posizione interessante xe Cloudflare. I ga <a %(blog_cloudflare)s>sostenuto</a> che no xe un fornitore di hosting, ma un servizio, come un ISP. No xe quindi soggetti a richieste di rimozion DMCA o altre, e inoltra qualsiasi richiesta al vostro effettivo fornitore di hosting. I xe andai fin in tribunale par protegger sta struttura. Podemo quindi usarli come un altro strato de caching e protezion."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare no accetta pagamenti anonimi, quindi podemo solo usar el loro piano gratuito. Questo significa che no podemo usar le loro funzionalità de bilanciamento del carico o failover. Abbiamo quindi <a %(annas_archive_l255)s>implementato questo noi stessi</a> a livello de dominio. Al caricamento della pagina, el browser controllerà se el dominio attuale xe ancora disponibile, e se no, riscrive tuti i URL a un altro dominio. Dato che Cloudflare cache molte pagine, questo significa che un utente può atterrare sul nostro dominio principale, anca se el server proxy xe giù, e poi al clic successivo esser spostà su un altro dominio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Gavemo ancora anca preoccupazioni operative normali da affrontar, come monitorar la salute del server, registrare errori del backend e del frontend, e così via. La nostra architettura di failover permette maggiore robustezza anca su questo fronte, per esempio eseguendo un set completamente diverso de server su uno dei domini. Podemo anca eseguire versioni più vecie del codice e dei datasets su sto dominio separato, nel caso un bug critico nella versione principale passi inosservato."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Podemo anca proteggerse contro Cloudflare che se rivolta contro de noi, rimuovendolo da uno dei domini, come sto dominio separato. Xe possibili diverse permutazioni de ste idee."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Strumenti"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vedemo quali strumenti usémo par realizzar tuto questo. Questo xe in continua evoluzion mentre affrontemo nuovi problemi e trovemo nuove soluzioni."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Server dell'applicazione: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Server proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestione del server: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Sviluppo: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hosting statico Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ghe xe qualcossa de decision che gavemo rivà a cambiar idea. Una xe la comunicazion tra i server: prima doparavemo Wireguard par questo, ma gavemo trovà che ogni tanto se ferma de trasmetter dati, o trasmette dati solo in una direzion. Questo xe successo con diversi setup de Wireguard che gavemo provà, come <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Gavemo provà anca a tunnelar i porti su SSH, doparando autossh e sshuttle, ma gavemo incontrà <a %(github_sshuttle)s>problemi là</a> (anca se no xe ancora chiaro se autossh soffre de problemi de TCP-over-TCP o no — me par solo una soluzion improvvisada ma forse xe in realtà a posto?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Inveze, gavemo tornà a le conession dirette tra i server, nascondendo che un server xe in esecuzion su i fornitori economici doparando il filtraggio IP con UFW. Questo ga el svantaggio che Docker no funziona ben con UFW, a meno che no dopari <code>network_mode: \"host\"</code>. Tutto questo xe un poco più soggetto a errori, perché esporai el to server a internet con solo una picola configurazion sbagliada. Forse dovessimo tornar a autossh — el feedback saria benvenù qua."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Gavemo anca cambià idea su Varnish vs. Nginx. Al momento ne piase Varnish, ma ga i so difeti e spigoli. El stesso val par Checkmk: no ne piase tanto, ma funziona par adesso. Weblate xe stà decente ma no incredibile — ogni tanto temo che perderà i me dati ogni volta che provo a sincronizarlo col nostro repo git. Flask xe stà bon nel complesso, ma ga qualchi difeto strano che ga costà tanto tempo par debuggar, come configurar domini personalizadi, o problemi con la so integrazion SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Finora gli altri strumenti xe stà ottimi: no gavemo lamentele serie su MariaDB, ElasticSearch, Gitlab, Zulip, Docker, e Tor. Tutti questi ga avù qualchi problema, ma niente de troppo serio o che consuma tempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusione"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Xe stà un'esperienza interessante imparar come configurar un motore de ricerca de biblioteca ombra robusto e resiliente. Ghe xe tanti altri detagli da condivìder in post futuri, quindi fammi saver cosa te piaceria imparar de più!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Come sempre, semo in cerca de donazioni par sostener questo lavoro, quindi assicurateve de visitar la pagina Donazioni su l'Archivio de Anna. Semo anca in cerca de altri tipi de supporto, come sovvenzioni, sponsor a lungo termine, fornitori de pagamenti ad alto rischio, forse anca (con gusto!) pubblicità. E se te vol contribuir col to tempo e le to abilità, semo sempre in cerca de sviluppatori, traduttori, e così via. Grazie par el to interesse e supporto."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e el team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Ciao, son Anna. Go creato <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, la più grande biblioteca ombra del mondo. Questo xe el me blog personale, in cui mi e i me compagni scrivemo de pirateria, preservazion digitale, e altro."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conetete con mi su <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Nota che questo sito web xe solo un blog. Ospitèmo solo le nostre parole qua. No ghe xe torrent o altri file coperti da copyright ospitati o collegati qua."

#, fuzzy
msgid "blog.index.heading"
msgstr "Post del blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Metendo 5.998.794 libri su IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avviso: sto post del blog xe stato deprecato. Gavemo deciso che IPFS no xe ancora pronto par el grande pubblico. Continueremo a linkar ai file su IPFS dall'Archivio de Anna quando xe possibile, ma no lo ospiteremo più noi stessi, né raccomandemo ad altri de farne il mirror usando IPFS. Consultè la nostra pagina dei Torrents se volè aiutar a preservare la nostra collezion."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archivio de Anna ga raschià tutto WorldCat (la più grande collezion de metadata de biblioteche del mondo) par far una lista de libri che bisogna preservar.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Un ano fa, gavemo <a %(blog)s>inizià</a> a risponder a questa domanda: <strong>Che percentuale de libri xe stà preservà permanentemente da le biblioteche ombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Una volta che un libro xe finìo in una biblioteca ombra de dati aperti come <a %(wikipedia_library_genesis)s>Library Genesis</a>, e adesso <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, el vien speciado in tutto el mondo (attraverso i torrent), preservandolo praticamente par sempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Par risponder alla domanda de che percentuale de libri xe stà preservà, gavemo bisogno de saver el denominatore: quanti libri esisti in totale? E idealmente no gavemo solo un numero, ma veri metadata. Poi podemo no solo confrontarli con le biblioteche ombra, ma anca <strong>crear una lista de libri rimanenti da preservar!</strong> Podessimo anca iniziar a sognar un sforzo collettivo par scorrer questa lista."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Gavemo <a %(wikipedia_isbndb_com)s>ISBNdb</a> e gavemo i dati da <a %(openlibrary)s>Open Library dataset</a>, ma i risultati no iera sodisfacenti. El problema principal xera che no ghe jera tanto de sovrapposizion de ISBN. Varda sto diagramma de Venn dal <a %(blog)s>nostro post sul blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Gavemo stado molto sorpresi da quanto poca sovrapposizion ghe jera tra ISBNdb e Open Library, tuti e do che include liberalmente dati da varie fonti, come web scrapes e archivi de biblioteche. Se tuti e do i fa un bon lavoro a trovar la maggior parte dei ISBN là fora, i so cerchi dovaria aver una sostanziale sovrapposizion, o un saria un sottoinsieme de l'altro. Questo ne ga fato domandar, quanti libri i resta <em>completamente fora de sti cerchi</em>? Gavemo bisogno de un database più grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Xe qua che gavemo puntado al più grande database de libri al mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Questo xe un database proprietario da la non-profit <a %(wikipedia_oclc)s>OCLC</a>, che aggrega i record de metadata da biblioteche in tuto el mondo, in cambio de dar a ste biblioteche accesso al dataset completo, e farle comparir nei risultati de ricerca dei utenti finali."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Anca se OCLC xe una non-profit, el so modello de business richiede de protegger el so database. Beh, semo dispiasei de dirlo, amici de OCLC, che noi lo stemo dando via tuto. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Nel corso de l'ultimo ano, gavemo scrupolosamente raschiado tuti i record de WorldCat. Al inizio, gavemo avudo un colpo de fortuna. WorldCat stava giusto lanciando el so completo redesign del sito web (in agosto 2022). Questo includeva un sostanziale rinnovamento dei so sistemi backend, introducendo molti difetti de sicurezza. Gavemo subito colto l'opportunità, e semo stai in grado de raschiar centinaia de milioni (!) de record in pochi giorni."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redesign de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dopo de che, i difetti de sicurezza i xe stai lentamente sistemai uno per uno, finché l'ultimo che gavemo trovado xe stado corretto circa un mese fa. A quel punto gavemo praticamente tuti i record, e stemo solo cercando de ottener record de qualità leggermente più alta. Cussì gavemo sentido che xe ora de rilasciar!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Vedemo un poco de informazion basica sui dati:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contenitori de l'Archivio de Anna (AAC)</a>, che xe essenzialmente <a %(jsonlines)s>JSON Lines</a> compressi con <a %(zstd)s>Zstandard</a>, più qualche semantica standardizzata. Sti contenitori i avvolge vari tipi de record, basai sui diversi scrapes che gavemo deployado."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dati"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Xe capità un errore sconossuo. Contatene a %(email)s con na schermata."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Sta moneta la ga un minimo più alto del solito. Par piaser seleziona na durata diversa o na moneta diversa."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "La richiesta no la xe stada completada. Par piaser, prova de novo tra qualche minuto, e se el problema persiste contatene a %(email)s con na schermata."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Errore nel processamento del pagamento. Per piaser aspetta un momento e prova de novo. Se el problema persiste per più de 24 ore, contatene a %(email)s con uno screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "commento nascosto"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema col file: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Version mejo"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Voleu segnalar sto utente par comportamento abusivo o inadeguà?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Segnala abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso segnalà:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Gavì segnalà sto utente par abuso."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Rispondi"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Per favore <a %(a_login)s>accedi</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Hai lasciato un commento. Potrebbe volerci un minuto prima che venga visualizzato."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Qualcosa è andato storto. Ricarica la pagina e riprova."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s pagine interessate"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nò vizibiłe su Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nò vizibiłe su Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nò vizibiłe su Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Senjà come roto su Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Manca da Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Contrassegnato come “spam” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Contrassegnato come “file errato” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "No tutte le pagine le xe stà convertìe in PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Esecuzione di exiftool fallita su questo file"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (sconjosùo)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libro (sazìstego)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libro (romanzo)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artìgoło sientìfego"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documento normativo"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Fumeto"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musicale"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibro"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Altro"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Download dal server partner"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Download esterno"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Prestito esterno"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Prestit esterno (stampa disabilitata)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Esplora i metadati"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Contenùo nei torrent"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Cinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Caricamenti su AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Indice eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadati cechi"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Statale Russa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Tìtoło"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Editor"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edisión"

msgid "common.specific_search_fields.year"
msgstr "An de publegasión"

msgid "common.specific_search_fields.original_filename"
msgstr "Nome del archivio orizinàl"

msgid "common.specific_search_fields.description_comments"
msgstr "Descrisión e comenti de ła metadata"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "I download dal Server Partner no i xe temporaneamente disponibili par sto file."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Server Partner Veloce #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(consiglià)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(nissuna verifica del browser o liste d'attesa)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Server Partner Lento #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(un po' più veloce ma con lista d'attesa)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(senza lista d'attesa, ma può essere molto lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Sazìstega"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Romanzo"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(clica anca su \"GET\" in alto)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(clica \"GET\" in alto)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "i so annunci i xe conossui par contener software malizioso, quindi dòpara un blocca-annunci o no cliccar sui annunci"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(I file Nexus/STC podaria no esser affidabili da scaricar)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library su Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(el bezonja el Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Presta da l'Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(solo par utenti con disabilità de stampa)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(el DOI asosià el połe nò èsar disponìbiłe in Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "collezion"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Download de torrent in massa"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(solo par esperti)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Sercar Archivio d'Anna par ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Sercar altri databazi par ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Trova el record original in ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Sercar el Archivio d'Anna par ID Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Trova el record original in Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Sercar el Archivio d'Anna par nùmaro OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Trova il record originale in WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Sercar Archivio d'Anna par nùmaro DuXiu SSID"

msgid "page.md5.box.download.original_duxiu"
msgstr "Sercar da manual so DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Cerca nell'Archivio di Anna il numero CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Trova el record originale in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Serca in l'Archivio de Anna el numero DXID de DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Indice eBook EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "L'Archivio de Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(no xe richiesta verifica del browser)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadati cechi %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadati"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "descrission"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nome file alternativo"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Titolo alternativo"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autore alternativo"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editore alternativo"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edizione alternativa"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Estensione alternativa"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "commenti sui metadati"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descrizione alternativa"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "data de apertura del código"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "File de Sci-Hub “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "File de Prestito Digitale Controllato de l'Internet Archive “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Sto qua xe un record de un file da l'Internet Archive, no un file scaricabile direttamente. Te pol provare a imprestar el libro (link qua soto), o doparar sto URL quando <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Se gavè sto file e no xe ancora disponibile su l'Archivio de Anna, considerè de <a %(a_request)s>carigarlo</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Record di metadata ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s record de metadata"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Record di metadata del numero OCLC (WorldCat) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Record de metadati DuXiu SSID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Record de metadata CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s record de metadata"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s record de metadata"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Sto xe un record de metadati, no un file scaricabile. Te podi doparar sto URL quando <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadati dal record collegato"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Migliora i metadati su Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Attenzione: record collegati multipli:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Migliora i metadati"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Segnala la qualità del file"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Tempo di download"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Sito web:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Cerca nell'Archivio di Anna “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Esploratore de Codici:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Visualizza in Esplora Codici “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lézi de più…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Download (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Prendi in prestito (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Esplora i metadati (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Commenti (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Liste (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statìstiche (%(count)s)"

msgid "common.tech_details"
msgstr "Detałi tecneghi (in ingleze)"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Sto file podarìa aver problemi, e xe stà nascosto da una biblioteca de origine.</span> Qualche volta xe su richiesta del detentor del copyright, qualche volta xe perché xe disponibile una alternativa mejo, ma qualche volta xe perché ghe xe un problema col file stesso. Podarìa ancora andar ben par scaricarlo, ma te racomandemo de cercar prima un file alternativo. Più detagli:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Una versione migliore de sto file podarìa esser disponibile a %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Se te vol ancora scaricar sto file, assicurite de dòparar solo software fidà e aggiornà par aprirlo."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Download veloci"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "Divaenta un <a %(a_membership)s>membro</a> par suportar la preservazion a longo termine de libri, articoli, e altro. Par mostrar la nostra gratitudine par el to suport, te gavarà download veloci. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se doni sto mese, te ga <strong>doppio</strong> el numero de download veloci."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Descàrreghe veloci</strong> Ti resta %(remaining)s par oggi. Gràssie de èsar un membro! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Download veloci</strong> Te ghè finìo i download veloci par oggi."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Ti gà scaricà sto file de recente. I link i resta validi par un poco."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opzion #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nissun reindirizamento)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(apri nel visualizzatore)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Recomanda un amico, e sia ti che el to amico riceverè %(percentage)s%% download veloci bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Sàbar de più…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Download lenti"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Da partner fidàbili."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Più informazion nel <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(potrebbe richiedere <a %(a_browser)s>verifica del browser</a> — download illimitati!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Dopo aver scaricà:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Apri nel nostro visualizzatore"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostra download esterni"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Descàrreghe esterne"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nissun download trovà."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Tuti i speci i gà el steso archivo, e i gavarìa da èsar seguri da doparar. Dito cuesto, fa senpre atension co te scarghi archivi da internet. Par ezempio, segurate de mantenjir azornài i to dispozidivi."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Par i file grandi, racomandemo de doparar un gestore de download par evitar interruzioni."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Gestori de download racomandai: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Gavarè bisogno de un lettore de ebook o PDF par vierzer el file, a seconda del formato."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Lettori de ebook racomandai: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visualizzatore online de l'Archivio de Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Dopara strumenti online par convertir tra i formati."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Strumenti de conversion racomandai: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Pòi mandar sia i file PDF che EPUB al to Kindle o Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Strumenti racomandai: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon ‘Manda a Kindle’"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz ‘Manda a Kobo/Kindle’"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Sostien i autori e le biblioteche"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Se te piase questo e te lo pòi permetter, considera de comprar l'originale, o de sostener i autori direttamente."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Se questo xe disponibile in biblioteca, considera de prestartelo gratis là."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Qualità del file"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Aiuta la comunità segnalando la qualità di questo file! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Segnala un problema con il file (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Ottima qualità del file (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Aggiungi commento (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Cosa c'è che non va in questo file?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Per favore usa il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Descrivi il problema (obbligatorio)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descrizione del problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 di una versione migliore di questo file (se applicabile)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Compila questo campo se c'è un altro file che corrisponde strettamente a questo file (stessa edizione, stessa estensione del file se riesci a trovarne uno), che le persone dovrebbero usare invece di questo file. Se conosci una versione migliore di questo file al di fuori di Anna’s Archive, allora per favore <a %(a_upload)s>caricala</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Puoi ottenere l'md5 dall'URL, ad esempio"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Invia segnalazione"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Impara come <a %(a_metadata)s>migliorare i metadati</a> di questo file da solo."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Grazie per aver inviato il tuo report. Verrà mostrato su questa pagina e sarà esaminato manualmente da Anna (fino a quando non avremo un sistema di moderazione adeguato)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Qualcosa è andato storto. Ricarica la pagina e riprova."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Se questo file è di alta qualità, puoi discutere qualsiasi cosa a riguardo qui! In caso contrario, utilizza il pulsante “Segnala problema file”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Ho adorato questo libro!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lascia un commento"

msgid "common.english_only"
msgstr "El testo seguente l'è disponìbiłe soło in ingleze."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Download totali: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “file MD5” è un hash che viene calcolato dai contenuti del file ed è ragionevolmente unico in base a quel contenuto. Tutte le biblioteche ombra che abbiamo indicizzato qui utilizzano principalmente gli MD5 per identificare i file."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un file potrebbe apparire in più biblioteche ombra. Per informazioni sui vari datasets che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Questo è un file gestito dalla biblioteca <a %(a_ia)s>IA’s Controlled Digital Lending</a> e indicizzato da Anna’s Archive per la ricerca. Per informazioni sui vari datasets che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Per informazioni su questo particolare file, consulta il suo <a %(a_href)s>file JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema a carègar sta pàgina"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Par piaser rinfresca par provàr de novo. <a %(a_contact)s>Contatene</a> se el problema persiste par più ore."

msgid "page.md5.invalid.header"
msgstr "Njaùn rezultà"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” no'l ze stà catà nte ła baze de dadi."

#, fuzzy
msgid "page.login.title"
msgstr "Accedi / Registrati"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verìfica del browser"

#, fuzzy
msgid "page.login.text1"
msgstr "Par evitar che i spam-bot crea tanti account, gavémo bisogno de verificar prima el to browser."

#, fuzzy
msgid "page.login.text2"
msgstr "Se te incastri in un ciclo infinito, te racomandemo de installar <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Pòdarìa anca esser utile disattivar i blocchi de pubblicità e altre estensioni del browser."

#, fuzzy
msgid "page.codes.title"
msgstr "Codici"

#, fuzzy
msgid "page.codes.heading"
msgstr "Esploratore di Codici"

#, fuzzy
msgid "page.codes.intro"
msgstr "Esplora i codici con cui i record sono taggati, per prefisso. La colonna “record” mostra il numero di record taggati con codici con il prefisso dato, come visto nel motore di ricerca (inclusi i record solo metadati). La colonna “codici” mostra quanti codici effettivi hanno un dato prefisso."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Questa pagina può richiedere un po' di tempo per essere generata, motivo per cui richiede un captcha di Cloudflare. <a %(a_donate)s>I membri</a> possono saltare il captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Si prega di non fare scraping di queste pagine. Invece, raccomandiamo di <a %(a_import)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB, e di eseguire il nostro <a %(a_software)s>codice open source</a>. I dati grezzi possono essere esplorati manualmente attraverso file JSON come <a %(a_json_file)s>questo</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefiso"

#, fuzzy
msgid "common.form.go"
msgstr "Va"

#, fuzzy
msgid "common.form.reset"
msgstr "Ripristina"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Serca l'Archivio de Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Atension: el còdighe el gà carateri Unicode incorèti, e podarìa comportarse in maniere incorète in varie situazioni. El binario bruto el pol èssar decodificà dal rappresentazion base64 nel URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefiso de còdighe conossù “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefiso"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Eticheta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descrizion"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL par un còdighe specifico"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sarà sostituìo col valor del còdighe"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL generico"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Sito web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s record che corisponde a “%(prefix_label)s”"
msgstr[1] ""

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL par còdighe specifico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Più…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Còdighi che inizia con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indice de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "record"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "còdighi"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Manco de %(count)s record"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Per reclami DMCA / sul copyright, usa <a %(a_copyright)s>questo modulo</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Qualsiasi altro modo di contattarci riguardo ai reclami sul copyright sarà automaticamente eliminato."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Accogliamo con molto piacere i vostri feedback e domande!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Comunque, par via del spam e delle email senza senso che ricevémo, par piaser spunta le casèle par confermàr che te capissi ste condizion par contatàrne."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Le richieste de copyright a sta email sarà ignorà; doparè el modulo invece."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "I server partner no i xe disponibili parché i xe stai seradi. I dovarìa tornar su presto."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "I abbonamenti sarà estesi de conseguensa."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "No ne mandàr email par <a %(a_request)s>richieder libri</a><br>o par pìceni (<10k) <a %(a_upload)s>caricamenti</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Quando te domandi su conti o donazion, zonta el to ID del conto, screenshot, ricevute, più informazion possìbile. Controlemò l'email ogni 1-2 setimane, cusì no zontàr ste informazion ritarderà ogni risoluzion."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostra email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Modulo de reclamo de copyright / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Se avete un reclamo de copyright o DMCA, par piaser compilà sto modulo nel modo più preciso possibile. Se gavé problemi, contatene al nostro indirizo dedicà al DMCA: %(email)s. Nota che i reclami mandài a sto indirizo no sarà processài, xe solo par domande. Par piaser doparà el modulo qua soto par inviar i vostri reclami."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL su l’Archivio de Anna (obligatorio). Un par riga. Par piaser includi solo URL che descrive la stessa edizion esata de un libro. Se volé far un reclamo par più libri o più edizioni, par piaser invia sto modulo più volte."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "I reclami che raggrupa più libri o edizioni insieme sarà rifiutài."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "El to nome (obligatorio)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Indirizo (obligatorio)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Numero de telefono (obligatorio)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (obligatorio)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descrizion chiara del materiale originale (obligatorio)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN del materiale originale (se aplicabile). Un par riga. Par piaser includi solo quei che corisponde esatamente all’edizion par cui stai segnala un reclamo de copyright."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL del materiale originale, un par riga. Par piaser taca un momento par cercar el to materiale originale su Open Library. Questo ne aiuterà a verificar el to reclamo."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL del materiale originale, un par riga (obligatorio). Par piaser includi quanti più possibile, par aiutarne a verificar el to reclamo (p.e. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Dichiarazion e firma (obligatorio)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Invia reclamo"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Grazie par aver invià el to reclamo de copyright. Lo revisarémo il prima possibile. Par piaser ricarica la pagina par inviarne un altro."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Qualcosa xe andà storto. Par piaser ricarica la pagina e prova de novo."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Se te sì interesà a speciar sto dataset par <a %(a_archival)s>archiviazion</a> o par <a %(a_llm)s>addestramento de LLM</a>, contatene."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "La nostra missione è archiviare tutti i libri del mondo (così come articoli, riviste, ecc.) e renderli ampiamente accessibili. Crediamo che tutti i libri dovrebbero essere replicati ampiamente, per garantire ridondanza e resilienza. Per questo stiamo raccogliendo file da una varietà di fonti. Alcune fonti sono completamente aperte e possono essere replicate in massa (come Sci-Hub). Altre sono chiuse e protettive, quindi cerchiamo di raschiarle per “liberare” i loro libri. Altre ancora si trovano in una via di mezzo."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Tutti i nostri dati possono essere <a %(a_torrents)s>scaricati via torrent</a>, e tutti i nostri metadati possono essere <a %(a_anna_software)s>generati</a> o <a %(a_elasticsearch)s>scaricati</a> come database ElasticSearch e MariaDB. I dati grezzi possono essere esplorati manualmente attraverso file JSON come <a %(a_dbrecord)s>questo</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Panoramica"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Di seguito una rapida panoramica delle fonti dei file su Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sorgente"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Dimension"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% speciaài da AA / torrents disponibili"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentuali del numero de file"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ultimo aggiornamento"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Saggistica e Narrativa"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s file"
msgstr[1] ""

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: bloccà da 2021; la maggior parte disponibile tramite torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: piccole aggiunte da allora</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Escludendo “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "I torrents de narrativa xe indrio (anca se i ID ~4-6M no i xe stai torrentià parchè i se sovrappone co i nostri torrents de Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "La collezion “Cinese” in Z-Library par che sia la stessa de la nostra collezion DuXiu, ma con MD5 diversi. Escludemo sti file dai torrents par evitar duplicazion, ma i xe ancora visibili nel nostro indice de ricerca."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Prestito Digitale Controllà da IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dei file xe ricercabili."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totale"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Escludendo i duplicati"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Dado che le biblioteche ombra spesso sincronizza i dati tra de lori, ghe xe un notevole sovrapposizion tra le biblioteche. Par questo i numeri no i somma al totale."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "La percentuale “speciaài e seminà da l’Archivio de Anna” mostra quanti file speciaemo noi stessi. Seminemo sti file in massa tramite torrents, e i xe disponibili par download diretto tramite siti partner."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Biblioteche de origine"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Alcune biblioteche sorgente promuovono la condivisione massiva dei loro dati tramite torrent, mentre altre non condividono facilmente la loro collezione. In quest'ultimo caso, l'Archivio de Anna cerca di raschiare le loro collezioni e renderle disponibili (vedi la nostra pagina <a %(a_torrents)s>Torrents</a>). Ci sono anche situazioni intermedie, ad esempio, dove le biblioteche sorgente sono disposte a condividere, ma non hanno le risorse per farlo. In questi casi, cerchiamo anche di aiutare."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Di seguito è riportata una panoramica di come interagiamo con le diverse biblioteche sorgente."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sorgente"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "File"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dump del database <a %(dbdumps)s>HTTP giornalieri</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrenti automatizzati per <a %(nonfiction)s>Non-Fiction</a> e <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s L'Archivio de Anna gestisse na collezion de <a %(covers)s>torrent de copertine de libri</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub gà fermà i file novi dal 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dump de metadata disponibili <a %(scihub1)s>qua</a> e <a %(scihub2)s>qua</a>, come parte del <a %(libgenli)s>database de Libgen.li</a> (che usémo)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrent de dati disponibili <a %(scihub1)s>qua</a>, <a %(scihub2)s>qua</a>, e <a %(libgenli)s>qua</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Qualche file novo el xe <a %(libgenrs)s>stà</a> <a %(libgenli)s>zonto</a> a “scimag” de Libgen, ma no abbastanza par giustificar novi torrent"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dump del database <a %(dbdumps)s>HTTP trimestrali</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s I torrenti de Non-Fiction i xe condivisi con Libgen.rs (e specchiati <a %(libgenli)s>qua</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s L'Archivio de Anna e Libgen.li i gestisse in colaborazion colecion de <a %(comics)s>fumeti</a>, <a %(magazines)s>riviste</a>, <a %(standarts)s>documenti standard</a>, e <a %(fiction)s>narrativa (diversa da Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s La so colecion “fiction_rus” (narrativa russa) no la ga torrent dedicà, ma la xe coperta da torrent de altri, e noi tenimo un <a %(fiction_rus)s>specio</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s L'Archivio de Anna e Z-Library gestisse in collaborazione na collezion de <a %(metadata)s>metadata de Z-Library</a> e <a %(files)s>file de Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Qualche metadata xe disponibile tramite <a %(openlib)s>Open Library database dumps</a>, ma no copre tuto el coleçion de IA."

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s No ghe xe dump de metadata facilmente accessibili par la so intera collezion"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s L'Archivio de Anna gestisse na coleçion de <a %(ia)s>metadata IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s File disponibili solo per prestito in modo limità, con varie restrizioni d'accesso"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s L'Archivio de Anna gestisce una collezion de <a %(ia)s>file IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Vari database de metadata sparsi su l'internet cinese; anche se spesso database a pagamento"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s No ghe xe dump de metadata facilmente accessibili per tutta la loro collezion."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s L'Archivio de Anna gestisse na coleçion de <a %(duxiu)s>metadata DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Vari archivi de file sparsi par l'internet cinese; spesso archivi a pagamento."

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La maggior parte dei file xe accessibili solo con conti premium BaiduYun; velocità de scaricamento lente."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s L'Archivio de Anna gestisse na collezion de <a %(duxiu)s>file DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Vari fonti più piccole o occasionali. Incoraggiamo le persone a caricar prima in altre biblioteche ombra, ma a volte le persone ga collezioni che xe tropo grandi par altri da gestir, ma no abbastanza grandi par meritar na categoria propria."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Sorgenti solo metadati"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Arricchiamo anche la nostra collezione con sorgenti solo metadati, che possiamo abbinare ai file, ad esempio utilizzando numeri ISBN o altri campi. Di seguito è riportata una panoramica di queste. Ancora una volta, alcune di queste sorgenti sono completamente aperte, mentre per altre dobbiamo raschiarle."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "La nostra ispirazion par coleccionar metadata xe el obiettivo de Aaron Swartz de \"una pagina web par ogni libro mai pubblicà\", par el qual el ga creato <a %(a_openlib)s>Open Library</a>. Quel progetto ga fato ben, ma la nostra posizion unica ne permette de ottener metadata che lori no riesse. Un'altra ispirazion xe sta la nostra voja de saver <a %(a_blog)s>quanti libri ghe xe nel mondo</a>, par calcolar quanti libri ne resta ancora da salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nota che nella ricerca di metadati, mostriamo i record originali. Non facciamo alcuna fusione di record."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ultimo aggiornamento"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Database dumps</a> mensili"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s No disponibile direttamente in massa, protetto contro lo scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s L'Archivio de Anna gestisse na coleçion de <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Database unificato"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Combiniamo tutte le sorgenti sopra in un unico database unificato che utilizziamo per servire questo sito web. Questo database unificato non è disponibile direttamente, ma poiché l'Archivio de Anna è completamente open source, può essere abbastanza facilmente <a %(a_generated)s>generato</a> o <a %(a_downloaded)s>scaricato</a> come database ElasticSearch e MariaDB. Gli script su quella pagina scaricheranno automaticamente tutti i metadati necessari dalle sorgenti menzionate sopra."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Se desideri esplorare i nostri dati prima di eseguire quegli script localmente, puoi guardare i nostri file JSON, che collegano ulteriormente ad altri file JSON. <a %(a_json)s>Questo file</a> è un buon punto di partenza."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adattà dal nostro <a %(a_href)s>post del blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> xe un database massivo de libri scanà, creato dal <a %(superstar_link)s>SuperStar Digital Library Group</a>. La maggior parte xe libri accademici, scanà par renderli disponibili digitalmente a università e biblioteche. Par el nostro pubblico che parla inglese, <a %(princeton_link)s>Princeton</a> e l'<a %(uw_link)s>Università de Washington</a> ga boni panorami. Ghe xe anca un articolo eccellente che dà più contesto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "I libri de Duxiu xe stati piratà da tanto tempo su internet cinese. De solito i xe vendui par meno de un dolar dai rivenditori. I xe tipicamente distribuiti usando l'equivalente cinese de Google Drive, che spesso xe stato hackerà par permettere più spazio de archiviazion. Alcuni dettagli tecnici se pol trovar <a %(link1)s>qua</a> e <a %(link2)s>qua</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Anca se i libri xe stati distribuiti semi-pubblicamente, xe abbastanza dificile ottenerli in massa. Gavemo messo questo alto nella nostra lista de cose da far, e gavemo dedicà mesi de lavoro a tempo pieno par questo. Comunque, a fine 2023 un volontario incredibile, straordinario e talentuoso ne ga contatà, dicendone che el ga già fatto tuto sto lavoro — a gran costo. El ne ga condiviso la collezion completa, senza aspettarse niente in cambio, a parte la garanzia de preservazion a lungo termine. Veramente rimarchevole."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Risorse"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "File totali: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Dimension totale dei file: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "File specchiati da l'Archivio de Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ultimo aggiornamento: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrent de l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Esempio de record su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "El nostro post del blog su sti dati"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Script per importar i metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formato Contenitori de l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Più informazioni dai nostri volontari (note grezze):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Sto dataset xe strettamente relazionà al <a %(a_datasets_openlib)s>dataset de Open Library</a>. El contien una raccolta de tuta la metadata e una gran parte de file da la Biblioteca de Prestito Digitale Controllà de l’IA. Gli aggiornamenti vien rilasciài nel <a %(a_aac)s>formato dei Contenitori de l’Archivio de Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Questi record vien riferì direttamente dal dataset de Open Library, ma contien anca record che no xe in Open Library. Gavémo anca un numero de file de dati raccolti dai membri de la comunità nel corso dei ani."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La collezion xe composta da do parti. Te ghé bisogno de tuti e do le parti par aver tuti i dati (ecceto i torrent superati, che xe segnài con una riga sui la pagina dei torrent)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "la nostra prima versione, prima de standardizar sul formato <a %(a_aac)s>Contenitori de l'Archivio de Anna (AAC)</a>. Contien metadata (come json e xml), pdf (da i sistemi de prestito digital acsm e lcpdf), e miniature de copertina."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "versioni nuove incrementali, usando AAC. Contien solo metadata con timestamp dopo el 2023-01-01, visto che el resto xe già coperto da \"ia\". Anca tuti i file pdf, sta volta dai sistemi de prestito acsm e \"bookreader\" (el lettore web de IA). Nonostante el nome no sia proprio giusto, continuemo a inserir i file bookreader nella collezion ia2_acsmpdf_files, visto che i xe mutualmente esclusivi."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sito principale %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca de Prestito Digitale"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentazion dei metadata (la maggior parte dei campi)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informazioni sui paesi dell'ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "L'Agenzia Internazionale dell'ISBN rilascia regolarmente le gamme che ha assegnato alle agenzie nazionali dell'ISBN. Da questo possiamo derivare a che paese, regione o gruppo linguistico appartiene questo ISBN. Attualmente usiamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Risorse"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultimo aggiornamento: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sito web ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Par la storia dei diversi fork de Library Genesis, varda la pagina de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "El Libgen.li contiene la maggior parte del stesso contenuto e metadati del Libgen.rs, ma ga alcune collezioni in più, cioè fumetti, riviste e documenti standard. Ga anca integrà <a %(a_scihub)s>Sci-Hub</a> nei suoi metadati e motore de ricerca, che xe quello che doparamo par la nostra base de dati."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "I metadati de sta biblioteca xe liberamente disponibili <a %(a_libgen_li)s>su libgen.li</a>. Comunque, sto server xe lento e no supporta la ripresa delle connessioni interrotte. Gli stessi file xe anca disponibili su <a %(a_ftp)s>un server FTP</a>, che funziona mejo."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "I torrent xe disponibili par la maggior parte del contesto addizionale, in particolare i torrent par i fumeti, le riviste e i documenti standard i xe stà rilascià in colaborazion con l'Archivio de Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La colecion de narrativa la ga i so torrent (diversi da <a %(a_href)s>Libgen.rs</a>) che i parte da %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Segondo l'amministrador de Libgen.li, la colecion “fiction_rus” (narrativa russa) la dovarìa eser coperta da torrent rilascià regularmente da <a %(a_booktracker)s>booktracker.org</a>, in particolare i torrent de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (che noi i speciem <a %(a_torrents)s>qua</a>, anca se no gavemo ancora stabilì quale torrent i corisponde a quale file)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Le statistighe par tute le colecion le se pol trovar <a %(a_href)s>sul sito de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "La saggistica par che se sia diversificada, ma senza novi torrenti. Par che questo sia successo da inizio 2022, anca se no gavemo verificà."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certi range senza torrent (come i range de narrativa f_3463000 a f_4260000) i xe probabilemente file de Z-Library (o altri duplicati), anca se podarìmo voler far un po' de deduplicazion e crear torrent par i file unici de lgli in sti range."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Nota che i file torrent che se riferisse a “libgen.is” xe esplicitamente specchi de <a %(a_libgen)s>Libgen.rs</a> (“.is” xe un dominio diverso doparà da Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Una risorsa utile par doparar i metadati xe <a %(a_href)s>sta pagina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrenti de narrativa su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrenti de fumeti su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrenti de riviste su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrent de documenti standard su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrent de narrativa russa su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadati via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informazioni sui campi dei metadati"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Specio de altri torrenti (e torrenti unici de narrativa e fumeti)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum de discussione"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "El nostro post del blog su la pubblicazion dei fumeti"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "La storia rapida dei diversi fork de Library Genesis (o “Libgen”) xe che col tempo, le diverse persone coinvolte con Library Genesis ga avuto dei disaccordi e xe andae per la so strada."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La versione “.fun” xe sta creà dal fondador originale. La xe in fase de ristrutturazion in favor de una nova versione più distribuita."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La versione “.rs” ga dati molto simili e rilascia costantemente la so collezion in torrenti in blocco. La xe divisa grosso modo in una sezione de “narrativa” e una de “non narrativa”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originalmente su “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>versione “.li”</a> ga una collezion massiccia de fumeti, cussì come altri contenuti, che no xe (ancora) disponibili per el download in blocco tramite torrenti. La ga una collezion separata de torrenti de libri de narrativa e contiene i metadati de <a %(a_scihub)s>Sci-Hub</a> nel so database."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Secondo sto <a %(a_mhut)s>post del forum</a>, Libgen.li xe sta originalmente ospitato su “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> in qualche senso xe anca un fork de Library Genesis, anca se ga doparà un nome diverso per el so progetto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Sta pagina xe su la versione “.rs”. La xe conosciuta per pubblicar costantemente sia i so metadati che el contenuto completo del so catalogo de libri. La so collezion de libri xe divisa tra una parte de narrativa e una de non narrativa."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Una risorsa utile per doparar i metadati xe <a %(a_metadata)s>sta pagina</a> (blocca i range IP, podessi che te servi un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partire dal 2024-03, novi torrenti xe postai in <a %(a_href)s>sto thread del forum</a> (blocca i range IP, podessi che te servi un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrenti de non narrativa su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrenti de narrativa su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadati de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informazion sui campi de metadati de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de saggistica de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de narrativa de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum de discussione de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents de l'Archivio de Anna (copertine de libri)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "El nostro blog su la pubblicazion de le copertine de libri"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis xe conosudo par far già generosamente disponibile i so dati in massa tramite torrents. La nostra collezion de Libgen consiste in dati ausiliari che lori no rilassa direttamente, in collaborazione con lori. Un gran ringraziamento a tuti i coinvolti con Library Genesis par lavorar con noi!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Pubblicazion 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Sta <a %(blog_post)s>prima pubblicazion</a> xe abbastanza picola: circa 300GB de copertine de libri dal fork de Libgen.rs, sia narrativa che saggistica. I xe organizadi nello stesso modo in cui i compare su libgen.rs, ad esempio:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s par un libro de saggistica."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s par un libro de narrativa."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Come con la collezion de Z-Library, i gavemo messo tuti in un grande file .tar, che se pol montar usando <a %(a_ratarmount)s>ratarmount</a> se te vol servir i file direttamente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> è un database proprietario della non-profit <a %(a_oclc)s>OCLC</a>, che aggrega record di metadata dalle biblioteche di tutto il mondo. È probabilmente la più grande collezione di metadata di biblioteche al mondo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Otòbre 2023, rilascio iniziale:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "In ottobre 2023 abbiamo <a %(a_scrape)s>rilasciato</a> una raccolta completa del database OCLC (WorldCat), nel <a %(a_aac)s>formato Contenitori dell'Archivio de Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrent da l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Il nostro post sul blog riguardo questi dati"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library xe un progetto open source de l'Internet Archive par catalogar ogni libro nel mondo. El ga una de le operazioni de scansione de libri più grandi del mondo, e ga tanti libri disponibili par il prestito digitale. El so catalogo de metadati de libri xe liberamente disponibile par el download, e xe incluso su l'Archivio de Anna (anche se attualmente no xe ricercabile, a meno che no te cerchi esplicitamente un ID de Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadati"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rilascio 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Sto qua xe un dump de un sacco de chiamate a isbndb.com durante settembre 2022. Gavemo provà a coprir tute le gamme de ISBN. Xe circa 30,9 milioni de record. Sul loro sito i dichiara che in realtà i ga 32,6 milioni de record, quindi podemo aver perso qualcossa, o <em>i</em> podaria far qualcossa de sbaglià."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Le risposte JSON xe praticamente grezze dal loro server. Un problema de qualità dei dati che gavemo notà, xe che par i numeri ISBN-13 che inizia con un prefisso diverso da “978-”, i include ancora un campo “isbn” che xe semplicemente el numero ISBN-13 con i primi 3 numeri taglià via (e el digito de controllo ricalcolà). Questo xe ovviamente sbaglià, ma xe cussì che i sembra farlo, quindi no gavemo alterà niente."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Un altro problema potenziale che podessi incontrar, xe el fatto che el campo “isbn13” ga dei duplicati, quindi no podessi dopararlo come chiave primaria in un database. I campi “isbn13”+“isbn” combinà sembra che sia unici."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Par un background su Sci-Hub, par piaser consulta el so <a %(a_scihub)s>sito ufficiale</a>, la <a %(a_wikipedia)s>pagina Wikipedia</a>, e sta <a %(a_radiolab)s>intervista in podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Nota che Sci-Hub xe stà <a %(a_reddit)s>congelà dal 2021</a>. El xe stà congelà prima, ma nel 2021 xe stà aggiunti qualche milion de articoli. Comunque, un numero limità de articoli vien ancora aggiunto alle collezioni “scimag” de Libgen, anche se no abbastanza par giustificar nuovi torrents in massa."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usiamo i metadati de Sci-Hub come fornidi da <a %(a_libgen_li)s>Libgen.li</a> nella so collezion “scimag”. Usiamo anche el dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Nota che i torrent “smarch” sono <a %(a_smarch)s>deprecati</a> e quindi non inclusi nella nostra lista di torrent."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata e torrent"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent su Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent su Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Aggiornamenti su Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pagina Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Intervista podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Caricamenti su l'Archivio de Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Panoramica da <a %(a1)s>pàxene dei datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Vari fonti più piccole o occasionali. Incoraggiamo le persone a caricar prima in altre biblioteche ombra, ma a volte le persone ga collezioni che xe tropo grandi par altri da gestir, ma no abbastanza grandi par meritar na categoria propria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La collezion \"upload\" xe divisa in sotto-collezioni più piccole, che xe indicate nei AACIDs e nei nomi dei torrent. Tutte le sotto-collezioni xe state prima deduplicate contro la collezion principale, anche se i file JSON \"upload_records\" contien ancora tante referenze ai file originali. I file non-libro xe stati anca rimossi dalla maggior parte delle sotto-collezioni, e tipicamente <em>no</em> xe notati nei JSON \"upload_records\"."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Tante sotto-collezioni stesse xe composte da sotto-sotto-collezioni (per esempio da fonti originali diverse), che xe rappresentate come directory nei campi \"filepath\"."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Le subcoleçion xe:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Sotosubolession"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Note"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "sfoglia"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cerca"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Da <a %(a_href)s>aaaaarg.fail</a>. Sembra abbastanza completo. Da el nostro volontario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Da un torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Gà un'alta sovrapposizion con le collezioni de articoli esistenti, ma pochi corrispondenze MD5, cusì gavemo deciso de tenerlo completamente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Racolta de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), da volontario <q>j</q>. Corrisponde a <q>airitibooks</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Da na colession <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. In parte da la fonte originale, in parte da the-eye.eu, in parte da altri specchi."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Da un sito torrent privato de libri, <a %(a_href)s>Bibliotik</a> (spesso referito come “Bib”), de cui i libri xe stati raggruppati in torrent par nome (A.torrent, B.torrent) e distribuiti tramite the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Da el nostro volontario “bpb9v”. Par più informazion su <a %(a_href)s>CADAL</a>, vedi le note nella nostra <a %(a_duxiu)s>pagina del dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Più da el nostro volontario “bpb9v”, principalmente file DuXiu, come anca un cartela “WenQu” e “SuperStar_Journals” (SuperStar xe la compagnia dietro DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Da el nostro volontario “cgiym”, testi cinesi da varie fonti (rappresentà come sottodirectory), inclusi da <a %(a_href)s>China Machine Press</a> (un importante editore cinese)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleçion non-cinesi (rappresentà come sottodirectory) da el nostro volontario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Racolta de libri su l'architettura cinese, da volontario <q>cm</q>: <q>Gavìo da un sfruttamento de vulnerabilità de rete alla casa editrice, ma quel varco xe stà serà</q>. Corrisponde a <q>chinese_architecture</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libri da casa editrice accademica <a %(a_href)s>De Gruyter</a>, raccolti da alcuni grandi torrent."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape de <a %(a_href)s>docer.pl</a>, un sito polacco de condivision de file focalizà sui libri e altre opere scritte. Scraped in tardo 2023 da el volontario “p”. No gavemo boni metadata dal sito originale (neanche le estensioni dei file), ma gavemo filtrà par file simili a libri e spesso semo riuscì a estrar metadata dai file stessi."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direttamente da DuXiu, raccolti dal volontario “w”. Solo i libri DuXiu recenti xe disponibili direttamente tramite ebook, quindi la maggior parte de questi deve esser recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "File DuXiu rimanenti dal volontario “m”, che no iera nel formato proprietario PDG de DuXiu (el dataset principale <a %(a_href)s>DuXiu</a>). Raccolti da tante fonti originali, purtroppo senza preservar quelle fonti nel filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Racolta de libri erotici, da volontario <q>do no harm</q>. Corrisponde a <q>hentai</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collezion raschiata da un editore giapponese de Manga dal volontario “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivi giudiziari selezionati de Longquan</a>, forniti dal volontario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape de <a %(a_href)s>magzdb.org</a>, un alleato de Library Genesis (xe linkà sulla homepage de libgen.rs) ma che no voleva fornire i so file direttamente. Ottenuto da el volontario “p” in tardo 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Vari piccoli upload, troppo piccoli par esser na propria sotto-collezion, ma rappresentati come directory."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebook da AvaxHome, un sito russo de condivision de file."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivio de giornali e riviste. Corrisponde a <q>newsarch_magz</q> metadata in <a %(a1)s><q>Altri metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Racolta de <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collezion del volontario “o” che ga raccolto libri polacchi direttamente dai siti de rilascio originali (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Collezioni combinate de <a %(a_href)s>shuge.org</a> dai volontari “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperiale de Trantor”</a> (nominata dopo la biblioteca fittizia), raschiata nel 2022 dal volontario “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sotto-sotto-collezioni (rappresentate come directory) dal volontario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (da <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, la mia piccola libreria — woz9ts: “Sto sito se concentra principalmente sulla condivisione de file ebook de alta qualità, alcuni dei quali xe impaginati dal proprietario stesso. El proprietario xe stato <a %(a_arrested)s>arrestato</a> nel 2019 e qualcuno ga fatto na collezion dei file che el ga condiviso.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Restanti file DuXiu dal volontario “woz9ts”, che no iera nel formato proprietario PDG de DuXiu (ancora da convertir in PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrent da L'Archivio de Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Raccolta Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ga le so radici nella comunità de <a %(a_href)s>Library Genesis</a>, e inizialmente xe partìa co i so dati. Da allora, xe diventà molto più professionale, e ga na interfaccia molto più moderna. Per questo, xe in grado de ricever molte più donazioni, sia monetarie per continuar a migliorar el so sito, sia donazioni de libri novi. Ga accumulà na grande collezion in aggiunta a Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Aggiornamento a febbraio 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Verso la fine del 2022, i presunti fondatori de Z-Library xe stai arrestai, e i domini xe stai sequestrai dalle autorità statunitensi. Da allora el sito ga lentamente ripreso a esser online. No se sa chi lo gestisse attualmente."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La collezion xe composta da tre parti. Le pagine de descrizion originali per le prime do parti xe conservà qua sotto. Te gà bisogno de tutte e tre le parti per ottener tutti i dati (eccetto i torrent superati, che xe barrà sulla pagina dei torrent)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: la nostra prima pubblicazion. Questa xe stada la primissima pubblicazion de quel che allora se ciamava “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: seconda versione, sta volta con tuti i file impacà in file .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: nuove versioni incrementali, usando el formato <a %(a_href)s>Contenitori de l'Archivio de Anna (AAC)</a>, adesso rilassà in collaborazione con el team de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenti da l'Archivio de Anna (metadata + contenuto)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Esempio de record su l'Archivio de Anna (collezion originale)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Esempio de record su l'Archivio de Anna (collezion “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sito principale"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Dominio Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Post del blog su la Version 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Post del blog su la Pubblicazion 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Rilasci Zlib (pagine de descrizion originali)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Pubblicazion 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "El specchio iniziale xe stado ottenuto con gran fatica nel corso del 2021 e 2022. A questo punto xe un poco obsoleto: riflette lo stato della collezion a giugno 2021. Lo aggiorneremo in futuro. Al momento semo concentrà a far uscire questa prima pubblicazion."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Dado che Library Genesis xe già conservà con torrent pubblici, e xe inclusa in Z-Library, gavemo fatto una deduplicazion di base contro Library Genesis a giugno 2022. Per questo gavemo usà gli hash MD5. Probabilmente ghe xe molto più contenuto duplicato nella libreria, come formati de file multipli co lo stesso libro. Questo xe difficile da rilevar con precision, quindi no lo femo. Dopo la deduplicazion, restemo con più de 2 milioni de file, per un totale de poco meno de 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La collezion consiste de do parti: un dump MySQL “.sql.gz” del metadata, e i 72 file torrent de circa 50-100GB ciascun. El metadata contien i dati come riportà dal sito Z-Library (titolo, autore, descrizion, tipo de file), come anca la dimensione reale del file e el md5sum che gavemo osservà, visto che a volte questi no i coincide. Parè che ghe sia dei range de file per i quali el Z-Library stesso el ga metadata incorretti. Potaremo anca aver scaricà file incorretti in certi casi isolati, che cercheremo de rilevar e sistemar in futuro."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "I grandi file torrent contien el dato reale dei libri, con l'ID de Z-Library come nome del file. Le estensioni dei file le pol esser ricostruì usando el dump del metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La collezion xe un misto de contenuto de narrativa e non narrativa (no separà come in Library Genesis). La qualità la varia anca tanto."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Sta prima versione xe adesso completamente disponibile. Nota che i file torrent i xe disponibili solo tramite el nostro specchio Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Pubblicazion 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Gavemo ottenuto tutti i libri che xe stai aggiunti a Z-Library tra el nostro ultimo specchio e agosto 2022. Gavemo anche rivisitato e raccolto alcuni libri che gavemo perso la prima volta. In totale, questa nova collezion xe circa 24TB. Anche sta collezion xe deduplicata contro Library Genesis, visto che ghe xe già torrent disponibili per quella collezion."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "I dati xe organizai in modo simile alla prima pubblicazion. Ghe xe un dump MySQL “.sql.gz” dei metadati, che include anche tutti i metadati della prima pubblicazion, superandola. Gavemo anche aggiunto alcune colonne nove:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: se sto file xe già in Library Genesis, sia nella collezion de non-fiction che de fiction (abbinato per md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in quale torrent xe sto file."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: impostato quando no semo riusciti a scaricar el libro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Gavemo menzionà questo l'ultima volta, ma solo per chiarir: “filename” e “md5” xe le proprietà reali del file, mentre “filename_reported” e “md5_reported” xe quel che gavemo raccolto da Z-Library. A volte sti do no xe d'accordo tra de lori, quindi gavemo incluso entrambi."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Par sta versione, gavemo cambià la collazion a “utf8mb4_unicode_ci”, che dovarìa èsar compatibile co le versioni vecie de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "I file de dati i xe simili a l'altra volta, anca se i xe molto più grandi. No gavemo proprio volù crear un mucchio de file torrent più picini. “pilimi-zlib2-0-14679999-extra.torrent” el contien tuti i file che gavemo perso ne l'ultima versione, mentre i altri torrent i xe tuti novi range de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Aggiornamento %(date)s:</strong> Gavemo fato i nostri torrent massa grandi, causando problemi ai client torrent. Li gavemo rimossi e rilassà nuovi torrent."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Aggiornamento %(date)s:</strong> Ghe xe ancora massa file, cusì i ghemo impacà in file tar e rilassà novi torrent."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addendum de la versione 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Questo xe un singolo file torrent extra. No'l contien gnente de novo, ma el gà dentro dei dati che i pol ciapàr un poco de tempo da calcolar. Questo el fa comodo da gaver, visto che scaricar sto torrent el xe spesso più veloce che calcolarlo da zero. In particolare, el contien indici SQLite par i file tar, par l'uso con <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Domande Frequenti (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Cosa xe l'Archivio de Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>L'Archivio de Anna</span> xe un progetto no-profit con do obiettivi:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservazion:</strong> Archiviando tuto el sapere e la cultura de l'umanità.</li><li><strong>Accesso:</strong> Rendendo sto sapere e cultura disponibili a chiunque nel mondo.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tuto el nostro <a %(a_code)s>codice</a> e <a %(a_datasets)s>dati</a> xe completamente open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Preservazion"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Preservémo libri, articoli, fumeti, riviste e altro ancora, portando sti materiali da varie <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteche ombra</a>, biblioteche ufficiali e altre collezioni in un solo posto. Tutti sti dati i xe preservai par sempre rendendoli facili da duplicar in massa — usando i torrent — risultando in tante copie in giro par el mondo. Alcune biblioteche ombra lo fa già da sole (es. Sci-Hub, Library Genesis), mentre l'Archivio de Anna “libera” altre biblioteche che no offre la distribussion in massa (es. Z-Library) o che no xe biblioteche ombra per niente (es. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Sta larga distribussion, combinada col codice open-source, rende el nostro sito resistente ai tentativi de chiusura, e assicura la preservassion a lungo termine del sapere e della cultura umana. Scopri de più sui <a href=\"/datasets\">nostri datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Stimemo de aver preservà circa <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% dei libri del mondo</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Accès"

#, fuzzy
msgid "page.home.access.text"
msgstr "Lavorémo con i nostri partner par far che le nostre collezioni sia facilmente e gratuitamente accessibili a tuti. Credémo che ognuno gà el diritto alla saggezza collettiva de l'umanità. E <a %(a_search)s>no a scapito dei autori</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Download orari nei ultimi 30 giorni. Media oraria: %(hourly)s. Media giornaliera: %(daily)s."

msgid "page.about.text2"
msgstr "Credemo fermamente nteła lìbara sircołasiòn de informasiòn, e nteła conservasiòn de ła conjosansa e cultura. Co 'sto motor de reserca, noialtri costruimo su łe spałe dei ziganti. Respetamo fondamente 'l duro laoro de łe parsone che łe gà creà łe tante biblioteche-onbra, e speramo che 'sto motor de reserca el poda slargar ła so portada."

msgid "page.about.text3"
msgstr "Par restar azornà su nostro progresso, segui Anna su <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Par domande e feedback, par piaser contata Anna su %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Come posso aiutar?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Seguitece su <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Spargete la voce sull'Archivio de Anna su Twitter, Reddit, Tiktok, Instagram, al vostro caffè o biblioteca locale, o dovunque andè! No credemo nel gatekeeping — se ne butta giù, ne rimettemo su da un'altra parte, visto che tutto el nostro codice e dati xe completamente open source.</li><li>3. Se podè, considerate de <a href=\"/donate\">donar</a>.</li><li>4. Aiutate a <a href=\"https://translate.annas-software.org/\">tradurre</a> el nostro sito in diverse lingue.</li><li>5. Se sì un ingegnere del software, considera de contribuire al nostro <a href=\"https://annas-software.org/\">open source</a>, o de fare seeding dei nostri <a href=\"/datasets\">torrent</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Gavemo adesso anca un canale Matrix sincronizà a %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Se te sì un ricercatore de sicurezza, podemo usar le to abilità sia par l'attacco che par la difesa. Varda la nostra pagina <a %(a_security)s>Sicurezza</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Semo in serca de esperti in pagamenti par mercanti anonimi. Te podi aiutarne a zontar modi più comodi par donar? PayPal, WeChat, carte regalo. Se te conossi qualcun, par piaser contatene."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Semo sempre in cerca de più capacità de server."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Te pol dar na man segnaland problemi coi file, lassando commenti e creando liste direttamente su sto sito. Te pol anca dar na man <a %(a_upload)s>caricando altri libri</a>, o sistemando problemi coi file o formattazione dei libri esistenti."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Crea o aiuta a mantenere la pagina Wikipedia per l'Archivio de Anna nella tua lingua."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Stemo cercando de inserir piccole, eleganti pubblicità. Se te volè far pubblicità su l'Archivio de Anna, par piaser contatene."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Saremo contenti se la zente mettesse su <a %(a_mirrors)s>specchi</a>, e ghe daremo supporto finanziario."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Par informazioni più estese su come far volontariato, varda la nostra pagina <a %(a_volunteering)s>Volontariato & Ricompense</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Parché i download lenti xe cussì lenti?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "No gavemo letteralmente risorse sufficienti par dar a tutti nel mondo download ad alta velocità, per quanto ne piaceria. Se un benefattore ricco volesse fornirne questo, saria incredibile, ma finché no succede, stemo facendo del nostro meglio. Semo un progetto no-profit che riesce a malapena a sostenersi tramite donazioni."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Xe per questo che gavemo implementà do sistemi par i download gratuiti, coi nostri partner: server condivisi con download lenti, e server leggermente più veloci con una lista d'attesa (par ridurre el numero de persone che scarica allo stesso tempo)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Gavemo anca <a %(a_verification)s>verifica del browser</a> par i nostri download lenti, perché senò i bot e i scraper i abusaria, rendendo le cose ancora più lente par i utenti legittimi."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Nota che, quando utilizzi il Tor Browser, potresti dover regolare le tue impostazioni di sicurezza. Al livello più basso delle opzioni, chiamato “Standard”, la sfida del turnstile Cloudflare riesce. Ai livelli più alti, chiamati “Più sicuro” e “Il più sicuro”, la sfida fallisce."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Par i file grandi, a volte i download lenti se pol interromper a metà. Recomandemo de doparar un gestore de download (come JDownloader) par riprendere automaticamente i download grandi."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "FAQ Donazioni"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>I abbonamenti se rinnova automaticamente?</div> I abbonamenti <strong>no</strong> se rinnova automaticamente. Podè unirse par quanto tempo volè."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Posso upgrade la me iscrizion o aver più iscrizioni?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Ghèto altri mètodi de pagamento?</div> Atualmente no. Molte parsone no łe vołe che archivi come 'sto i ezista, łora ghemo da star cauti. Se te połi jutarne a impostar altri (pì convenienti) mètodi de pagamento in seguresa, par piaser contatane a %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Cosa vol dir i range par mese?</div> Podè rivar al lato più baso de un range aplicando tuti i sconti, come sceglier un periodo più longo de un mese."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Cosa fémone co le donazion?</div> 100%% xe par preservar e far accessibile la conoscenza e la cultura del mondo. Al momento, i soldi i va sopratuto par server, archiviazion e banda. Nissun scheo va ai membri del team personalmente."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Podoi far na granda donasion?</div> Sto 'l sarìa fantàstego! Par donasión oltra cualche mejo de dołari, par piaser contatane diretamente a %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Posso far una donazione senza diventare membro?</div> Certo. Accettemo donazioni de qualsiasi importo a sto indirizzo Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Come fazo a caricar novi libri?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "In alternativa, podè carigarli su Z-Library <a %(a_upload)s>qua</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Par caricamentini (fin a 10.000 file) par piaser caricali sia su %(first)s che su %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Par adesso, sugierimo de cargar libri novi sui fork de Library Genesis. Qua ghe xe un <a %(a_guide)s>guida utile</a>. Nota che tuti i fork che indexemo su sto sito tira da sto stesso sistema de carica."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Par Libgen.li, assicurate de far prima el login su <a %(a_forum)s >el loro forum</a> con el nome utente %(username)s e la password %(password)s, e dopo tornate su la loro <a %(a_upload_page)s >pagina de caricamento</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Se la vostra email no funziona sui forum de Libgen, racomandemo de doparar <a %(a_mail)s>Proton Mail</a> (gratis). Podè anca <a %(a_manual)s>richieder manualmente</a> che el vostro conto vegna ativà."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Nota che mhut.org blocca certe range de IP, quindi podarìa servir un VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Par carichi grandi (più de 10.000 file) che no vien acceptadi da Libgen o Z-Library, par piaser contatene a %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Par cargar articoli academici, par piaser (oltre a Library Genesis) carighe anca su <a %(a_stc_nexus)s>STC Nexus</a>. I xe la mejo biblioteca ombra par articoli novi. No i xe ancora integradi, ma lo faremo a un certo punto. Te pol doparar el so <a %(a_telegram)s>bot de upload su Telegram</a>, o contatar l'indirizo segnà nel messagio fissà se te ghè massa file da cargar in sto modo."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Come posso richiedere libri?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Al momento, no podemo accomodar richieste de libri."

#, fuzzy
msgid "page.request.forums"
msgstr "Par piaser, fazi le to richieste sui forum de Z-Library o Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "No ne mandè email con richieste de libri."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Te colezi i metadati?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Sì, lo femo."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Go descartegà 1984 de George Orwell, vien la polizia a casa mia?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "No te preocuparte massa, ghe xe tanti che descarica dai siti che noi lighemo, e xe estremamente raro aver problemi. Comunque, par star sicuri te racomandemo de doparar un VPN (pagà), o <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Come fazzo a salvar le impostazioni de serca?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Scegli le impostazioni che te piase, lasa el quadro de serca vacante, clicca “Serca”, e dopo segna la pagina nei preferiti del to browser."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Gavè una app par el telefono?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "No gavemo una app ufficiale par el telefono, ma te pol instalar sto sito come una app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Clicca sul menu coi tre punti in alto a destra e seleziona “Aggiungi alla schermata Home”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Clicca sul pulsante “Condividi” in basso e seleziona “Aggiungi alla schermata Home”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Avete un'API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Abbiamo un'API JSON stabile per i membri, per ottenere un URL di download veloce: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentazione all'interno del JSON stesso)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Per altri casi d'uso, come iterare attraverso tutti i nostri file, costruire ricerche personalizzate e così via, raccomandiamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. I dati grezzi possono essere esplorati manualmente <a %(a_explore)s>attraverso file JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "La nostra lista di torrent grezzi può essere scaricata anche come <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ sui Torrent"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Vorrei aiutare a fare seed, ma non ho molto spazio su disco."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Usa il <a %(a_list)s>generatore di liste di torrent</a> per generare una lista di torrent che hanno più bisogno di seeding, entro i limiti del tuo spazio di archiviazione."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "I torrent sono troppo lenti; posso scaricare i dati direttamente da voi?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Sì, vedi la pagina <a %(a_llm)s>dati LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Posso scaricare solo un sottoinsieme dei file, come solo una particolare lingua o argomento?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Risposta corta: no facilmente."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Risposta lunga:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "La maggior parte dei torrent contiene i file direttamente, il che significa che puoi istruire i client torrent a scaricare solo i file richiesti. Per determinare quali file scaricare, puoi <a %(a_generate)s>generare</a> i nostri metadati, o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Purtroppo, un certo numero di collezioni di torrent contiene file .zip o .tar alla radice, nel qual caso devi scaricare l'intero torrent prima di poter selezionare i file individuali."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Gavemo <a %(a_ideas)s>qualche idea</a> par sto caso però.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "No ghe xe strumenti facili da doparar par filtrare i torrent, ma accetemo volentieri contributi."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Come gestite i duplicati nei torrent?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Cerchiamo di mantenere al minimo la duplicazione o la sovrapposizione tra i torrent in questa lista, ma questo non può sempre essere raggiunto e dipende molto dalle politiche delle biblioteche di origine. Per le biblioteche che rilasciano i propri torrent, non è nelle nostre mani. Per i torrent rilasciati da Anna’s Archive, deduplichiamo solo in base all'hash MD5, il che significa che diverse versioni dello stesso libro non vengono deduplicate."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Posso ottenere la lista dei torrent come JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Sì."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Non vedo PDF o EPUB nei torrent, solo file binari? Cosa devo fare?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Questi sono in realtà PDF ed EPUB, semplicemente non hanno un'estensione in molti dei nostri torrent. Ci sono due posti in cui puoi trovare i metadati per i file torrent, inclusi i tipi di file/estensioni:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Ogni collezione o rilascio ha i propri metadati. Ad esempio, i <a %(a_libgen_nonfic)s>torrent di Libgen.rs</a> hanno un database di metadati corrispondente ospitato sul sito web di Libgen.rs. Tipicamente colleghiamo le risorse di metadati rilevanti dalla <a %(a_datasets)s>pagina del dataset</a> di ciascuna collezione."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomandémose de <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> i nostri database ElasticSearch e MariaDB. Questi contien un mapeo par ogni record in l'Archivio de Anna ai so corispondenti file torrent (se disponibili), soto \"torrent_paths\" nel JSON de ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Parché el me client torrent no riesse a vierzi certi file torrent / link magnetici vostri?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Certi client torrent no supporta dimensioni de pezzi grandi, che tanti dei nostri torrent gà (par quelli più novi no lo femo più — anca se xe valido secondo le specifiche!). Dunque, prova un altro client se te incroci sto problema, o lamentete coi creatori del to client torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Gavé un programma de divulgazion responsabile?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Semo contenti de acueiar i ricercatori de segurità che cerche vulnerabilità nei nostri sistemi. Semo grandi sostenitori de la divulgazion responsabile. Contatene <a %(a_contact)s>qua</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Al momento no podemo premiar bug bounty, ecceto par le vulnerabilità che ga <a %(a_link)s>el potenziale de comprometer la nostra anonimità</a>, par le quali ofrimo premi tra i $10k-50k. Voremo ofrir un ambito più ampio par i bug bounty in futuro! Nota che i attacchi de ingegneria sociale xe fora dal ambito."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Se te sì interessà a la segurità offensiva, e te vol ajudar a archivar la conoscenza e la cultura del mondo, contatene. Ghe xe tanti modi in cui te podi ajudar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ghe xe più risorse su l'Archivio de Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — aggiornamenti regolari"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software de Anna</a> — el nostro codice open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduci su el Software de Anna</a> — el nostro sistema de traduzione"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — su i dati"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domini alternativi"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — più su de noi (per favor aiuta a mantener sta pagina aggiornata, o crea una par la to lengua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Come podio segnalar una violazion de copyright?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "No ospitén nissun materiale coperto da copyright qua. Semo un motore de ricerca, e come tale indexén solo i metadata che xe già publicamente disponibili. Quando te scarichi da ste fonti esterne, te sugerimo de verificar le leggi nella to giurisdizion riguardo a ciò che xe permesso. No semo responsabili par el contignuo ospitato da altri."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Se te ghé dei reclami su ciò che vedi qua, el mejo xe contatar el sito original. Regolarmente tirén su i so cambiamenti nel nostro database. Se te pensi veramente de aver un reclamo DMCA valido a cui dovemo risponder, par favor compila el <a %(a_copyright)s>modulo de reclamo DMCA / Copyright</a>. Tignén seriamente i to reclami, e te risponderén appena possibile."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Odio come te gestissi sto progetto!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Voremo anca ricordar a tuti che tuto el nostro codice e i dati xe completamente open source. Questo xe unico par progetti come el nostro — no semo consapevoli de nissun altro progetto con un catalogo così massivo che xe anca completamente open source. Semo veramente contenti de acueiar chiunque pensi che gestimo mal el nostro progetto a prender el nostro codice e i dati e crear la so propria biblioteca ombra! No lo disén per dispetto o altro — pensén veramente che saria fantastico perché alzaria el livello par tuti, e preservaria mejo l'eredità de l'umanità."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Gavì un monitor de uptime?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Vedi <a %(a_href)s>sto progetto eccellente</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Come posso donare libri o altri materiali fisici?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Per favore, inviali a l’<a %(a_archive)s>Internet Archive</a>. I li conserverà adeguatamente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Chi xe Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Ti xe Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Quali xe i vostri libri preferiti?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Qua ghe xe alcuni libri che ga un significato speciale par el mondo delle biblioteche ombra e la preservazion digitale:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Ti xe finì i download veloci par ozéti."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Diventa un membro per utilizzare i download veloci."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Adesso suportemo le carte regalo Amazon, carte de credito e debito, cripto, Alipay e WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Database completo"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libri, articoli, riviste, fumetti, registri di biblioteca, metadati, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Serca"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ga <a %(a_paused)s>sospeso</a> el caricamento de nuovi articoli."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB xe na continuazion de Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accesso diretto a %(count)s articoli accademici"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Vèrzi"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Se ti xe un <a %(a_member)s>membro</a>, no xe bisogno de verificare el browser."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archivio a lungo termine"

#, fuzzy
msgid "page.home.archive.body"
msgstr "I datasets doparà in l'Archivio de Anna xe completamente liberi, e i pol èssar duplicai in massa doparà i torrents. <a %(a_datasets)s>Sàpi de più…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Ti pol dar un gran aiuto seminando i torrents. <a %(a_torrents)s>Sàpi de più…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Dati de training LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Gavemo la più grande collezion de dati testuali de alta qualità del mondo. <a %(a_llm)s>Scopri de più…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Speci: chiamata ai volontari"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Se cèrchene volontari"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Come progetto no-profit e open-source, semo sempre in serca de persone che ne possa dar na man."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Se te gestissi un processador de pagamenti anonimi ad alto risco, contatene. Semo anca in serca de persone che vol meter piccole publicità de bon gusto. Tutti i proventi va a le nostre iniziative de preservazion."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog de Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Download IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Tutti i link de download par sto file: <a %(a_main)s>Pagina principale del file</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Gateway IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(el połe èsar nesesario provar pì volte co IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Par scaricamenti più veloci e evitar i control browser, <a %(a_membership)s>diventa membro</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Par duplicare in massa la nostra collezion, varda le pagine <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Dati LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Xe ben capio che i LLM prospera su dati de alta qualità. Gavemo la più grande collezion de libri, articoli, riviste, ecc. del mondo, che xe tra le fonti de testo de più alta qualità."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Scala e gamma uniche"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "La nostra collezion contiene più de cento milioni de file, inclusi giornali accademici, manuali e riviste. Riusimo a raggiunger sta scala combinando grandi archivi esistenti."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Alcune delle nostre collezioni de fonte xe già disponibili in massa (Sci-Hub e parti de Libgen). Alte fonti le gavemo liberà noi stessi. <a %(a_datasets)s>Datasets</a> mostra na panoramica completa."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "La nostra collezion include milioni de libri, articoli e riviste de prima dell'era degli e-book. Grandi parti de sta collezion le xe già state OCRizzate, e ga già poco sovrapposizion interna."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Come podemo aiutar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Semo in grado de fornire accesso ad alta velocità a tutte le nostre collezioni, come anca a collezioni non ancora rilasciate."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Questo xe un accesso a livello aziendale che podemo fornire par donazioni nell'ordine de decine de migliaia de USD. Semo anca disposti a scambiarlo par collezioni de alta qualità che no gavemo ancora."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Podemo rimborsarte se te riesi a fornirne un arricchimento dei nostri dati, come:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Rimozion de sovrapposizioni (deduplicazione)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Estrazion de testo e metadati"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Suporta l'archiviazion a lungo termine del sapere umano, mentre te ottieni dati migliori par el to modello!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contatene</a> par discuter come podemo lavorar insieme."

#, fuzzy
msgid "page.login.continue"
msgstr "Continua"

#, fuzzy
msgid "page.login.please"
msgstr "Par piaser <a %(a_account)s>faze el login</a> par vardar sta pagina.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "L'Archivio de Anna xe temporaneamente fora uso par manutenzion. Torna tra un'ora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Migliora i metadati"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Ti pol aiutar a preservar i libri migliorando i metadati! Prima, legi el contesto sui metadati su l'Archivio de Anna, e dopo impara come migliorar i metadati tramite el collegamento con Open Library, e guadagna un'abbonamento gratuito su l'Archivio de Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Contesto"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Quando te vedi un libro su l'Archivio de Anna, te pol veder vari campi: titolo, autore, editore, edizione, anno, descrizione, nome del file, e altro. Tutte ste informazioni le xe ciamade <em>metadati</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Dado che combiniamo libri da varie <em>biblioteche sorgente</em>, mostriamo qualsiasi metadato disponibile in quella biblioteca sorgente. Par esempio, par un libro che gavemo da Library Genesis, mostreremo el titolo dal database de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Qualche volta un libro el xe presente in <em>più</em> biblioteche sorgente, che podaria aver campi de metadati diversi. In quel caso, mostriamo semplicemente la versione più longa de ogni campo, visto che quella speremo contenga le informazioni più utili! Mostreremo ancora i altri campi sotto la descrizione, par esempio come \"titolo alternativo\" (ma solo se i xe diversi)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Tiremmo fora anca <em>codici</em> come identificatori e classificatori dalla biblioteca sorgente. <em>Identificatori</em> rappresenta in modo unico una particolare edizione de un libro; esempi xe ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classificatori</em> raggruppa insieme più libri simili; esempi xe Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. Qualche volta sti codici i xe esplicitamente collegati nelle biblioteche sorgente, e qualche volta riusciamo a tirarli fora dal nome del file o dalla descrizione (principalmente ISBN e DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Podemo usar i identificatori par trovar record in <em>collezioni solo metadati</em>, come OpenLibrary, ISBNdb, o WorldCat/OCLC. Ghe xe una specifica <em>scheda metadati</em> nel nostro motore de ricerca se te volessi sfogliare ste collezioni. Usiamo i record corrispondenti par riempir i campi de metadati mancanti (par esempio se manca un titolo), o par esempio come \"titolo alternativo\" (se ghe xe un titolo esistente)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Par veder esattamente da dove vien i metadati de un libro, vedi la <em>scheda \"Dettagli tecnici\"</em> su una pagina del libro. La gà un link al JSON grezzo par quel libro, con puntatori al JSON grezzo dei record originali."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Par più informazioni, vedi le seguenti pagine: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Ricerca (scheda metadati)</a>, <a %(a_codes)s>Esploratore de codici</a>, e <a %(a_example)s>Esempio de metadati JSON</a>. Finalmente, tutti i nostri metadati i pol esser <a %(a_generated)s>generati</a> o <a %(a_downloaded)s>scaricati</a> come database ElasticSearch e MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Collegamento con Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Dunque se te trovi un file con metadati sbagliati, come dovresti sistemarlo? Te pol andar alla biblioteca sorgente e seguir le sue procedure par sistemar i metadati, ma cosa fare se un file el xe presente in più biblioteche sorgente?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Ghe xe un identificatore che el xe trattato in modo speciale su l'Archivio de Anna. <strong>El campo annas_archive md5 su Open Library el sovrascrive sempre tutti i altri metadati!</strong> Fermiamoci un attimo e impariamo de più su Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library la xe stata fondata nel 2006 da Aaron Swartz con l'obiettivo de \"una pagina web par ogni libro mai pubblicato\". La xe una sorta de Wikipedia par i metadati dei libri: tutti i pol modificarla, la xe liberamente licenziata, e la pol esser scaricata in blocco. La xe un database de libri che el xe più allineato con la nostra missione — in fatti, l'Archivio de Anna el xe stato ispirato dalla visione e vita de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Invece de reinventar la ruota, gavemo deciso de indirizzar i nostri volontari verso Open Library. Se te vedi un libro che gà metadati sbagliati, te pol aiutar nel seguente modo:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Va al <a %(a_openlib)s>sito web de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Trova el record corretto del libro. <strong>ATTENZIONE:</strong> assicurati de selezionare la <strong>edizione</strong> corretta. In Open Library, ghe xe \"opere\" e \"edizioni\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Una \"opera\" podaria esser \"Harry Potter e la Pietra Filosofale\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Una \"edizione\" podaria esser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La prima edizion del 1997 pubblicada da Bloomsbery con 256 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "L'edizion tascabile del 2003 pubblicada da Raincoast Books con 223 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La tradussion polaca del 2000 “Harry Potter I Kamie Filozoficzn” da Media Rodzina con 328 pagine."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Tute ste edizions ga ISBN e contenuti diversi, cusì assicuraiteve de selessionar quela giusta!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Modifica el record (o creelo se no'l esiste), e zonta più informazioni utili che te riesi! Te sì qua adesso, tanto val farlo ben."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Soto “ID Numbers” seleziona “Anna’s Archive” e zonta el MD5 del libro da Anna’s Archive. Questa xe la stringa longa de letare e numeri dopo “/md5/” nel URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Prova a catar altri file in Anna’s Archive che corisponde anca a sto record, e zontali anca quei. In futuro podemo grupare quei come duplicati sulla pagina de ricerca de Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando te ga finìo, scrivi l'URL che te ga appena aggiornà. Una volta che te ga aggiornà almanco 30 records con i MD5 de Anna’s Archive, mandine un <a %(a_contact)s>email</a> e mandine la lista. Te daremo un'abonamento gratuito per Anna’s Archive, cusì te podi far sto lavoro più facilmente (e come ringraziamento per el to aiuto). Questi deve esser modifiche de alta qualità che zonta una quantità sostanziale de informazioni, senò la to richiesta sarà rifiutà. La to richiesta sarà anca rifiutà se qualcuna de le modifiche vien revertìa o corretta dai moderatori de Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Nota che questo funziona solo per i libri, no per i documenti accademici o altri tipi de file. Per altri tipi de file racomandemo ancora de catar la biblioteca de origine. Podaria voler qualche settimana prima che i cambiamenti vegni inclusi in Anna’s Archive, visto che gavemo bisogno de scaricar l'ultimo dump de dati de Open Library, e rigenerar el nostro indice de ricerca."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Specchi: chiamata per volontari"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Par aumentare la resilienza de l'Archivio de Anna, stemo cercando volontari par gestir specchi."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Cerchemo questo:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Te gestissi el codice open source de l’Archivio de Anna, e te aggiorni regolarmente sia el codice che i dati."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "La vostra versione xe chiaramente distinta come uno specio, par esempio “L’Archivio de Bob, un specio de l’Archivio de Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Sito disposto a prenderti i rischi associati a questo lavoro, che sono significativi. Hai una profonda comprensione della sicurezza operativa richiesta. I contenuti di <a %(a_shadow)s>questi</a> <a %(a_pirate)s>post</a> sono evidenti per te."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Sito disposto a contribuire al nostro <a %(a_codebase)s>codice</a> — in collaborazione con il nostro team — per far sì che ciò accada."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inizialmente non ti daremo accesso ai download del nostro server partner, ma se le cose andranno bene, potremo condividerlo con te."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Spese de ospitalità"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Siamo disposti a coprire le spese di hosting e VPN, inizialmente fino a $200 al mese. Questo è sufficiente per un server di ricerca di base e un proxy protetto da DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Pagheremo solo per l'hosting una volta che avrai tutto configurato e avrai dimostrato di essere in grado di mantenere l'archivio aggiornato con gli aggiornamenti. Questo significa che dovrai pagare per i primi 1-2 mesi di tasca tua."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "El vostro tempo no sarà compensà (e gnanca el nostro), visto che xe puro lavoro de volontariato."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se te ghe te involvi significativamente nel sviluppo e nelle operazioni del nostro lavoro, podemo parlar de condivider più del ricavo delle donazioni con ti, par che ti possa impiegarli come necessario."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Come cominciar"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Par piaser <strong>no contatarne</strong> par domandar permesso, o par domande basiche. Le azioni parla più forte delle parole! Tutte le informazioni xe là fora, quindi va avanti e impianta el to specio."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Sentiti libero di postare ticket o richieste di merge sul nostro Gitlab quando incontri problemi. Potremmo dover costruire alcune funzionalità specifiche per il mirror con te, come il rebranding da “Anna’s Archive” al nome del tuo sito web, (inizialmente) disabilitando gli account utente, o collegando le pagine dei libri al nostro sito principale."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Una volta che il tuo mirror è in funzione, ti preghiamo di contattarci. Ci piacerebbe rivedere la tua OpSec, e una volta che sarà solida, collegheremo il tuo mirror e inizieremo a lavorare più strettamente con te."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Grazie in anticipo a chiunque sia disposto a contribuire in questo modo! Non è per i deboli di cuore, ma consoliderebbe la longevità della più grande biblioteca veramente aperta nella storia umana."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Scarica dal sito partner"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ I download lenti i xe disponibili solo tramite el sito ufficiale. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ I download lenti no xe disponibili tramite i VPN de Cloudflare o da indirizzi IP de Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Par piaser, speta <span %(span_countdown)s>%(wait_seconds)s</span> secondi prima de scarigar sto file."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Dòpara el libro con sto URL: <a %(a_download)s>Dòpara adesso</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Grasie par l'attesa, questo rende el sito accessibile gratis par tuti! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Atension: ghe xe stà tanti download dal to IP ne le ultime 24 ore. I download podarìa èsser più lenti del solito."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Download dal tuo indirizzo IP nelle ultime 24 ore: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Se stai usando una VPN, una connessione internet condivisa, o il tuo ISP condivide gli IP, questo avviso potrebbe essere dovuto a ciò."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Par dar a tuti l'opportunità de scarigar i file gratis, te ghe da spetare prima de poder scarigar sto file."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Continua pure a navigar l'Archivio de Anna in un'altra scheda mentre aspeti (se el to browser supporta l'aggiornamento delle schede in background)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Continua pure a aspettar che più pagine de download se cariche in contemporanea (ma per piaser scarica solo un file alla volta per server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Una volta che te ghè un link de download, el xe valido par diverse ore."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Archivio de Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Record in l'Archivio de Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Scarica"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Per supportare l'accessibilità e la conservazione a lungo termine della conoscenza umana, diventa un <a %(a_donate)s>membro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Come bonus, 🧬&nbsp;SciDB si carica più velocemente per i membri, senza alcun limite."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Non funziona? Prova a <a %(a_refresh)s>aggiornare</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Anteprima non ancora disponibile. Scarica il file da <a %(a_path)s>L'Archivio di Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB è una continuazione di Sci-Hub, con la sua interfaccia familiare e la visualizzazione diretta dei PDF. Inserisci il tuo DOI per visualizzare."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Abbiamo l'intera collezione di Sci-Hub, oltre a nuovi articoli. La maggior parte può essere visualizzata direttamente con un'interfaccia familiare, simile a Sci-Hub. Alcuni possono essere scaricati tramite fonti esterne, in tal caso mostriamo i link a queste."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Ricerca"

msgid "page.search.title.new"
msgstr "Nova reserca"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Includi solo"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Escludi"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "No controlà"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Descarga"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Articoli di riviste"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Prestito Digitale"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadati"

msgid "common.search.placeholder"
msgstr "Tìtoło, autor, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Sercar"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Impostazioni de ricerca"

#, fuzzy
msgid "page.search.submit"
msgstr "Serca"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "La ricerca ha impiegato troppo tempo, il che è comune per le query ampie. I conteggi dei filtri potrebbero non essere accurati."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "La ricerca ha impiegato troppo tempo, il che significa che potresti vedere risultati inaccurati. A volte <a %(a_reload)s>ricaricare</a> la pagina aiuta."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Display"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabèła"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avanzato"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Cerca descrizioni e commenti sui metadati"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Zonta un campo de ricerca specifico"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(cerca campo specifico)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Anno di pubblicazione"

msgid "page.search.filters.content.header"
msgstr "Contenjìo"

msgid "page.search.filters.filetype.header"
msgstr "Tipo d'archivo"

#, fuzzy
msgid "page.search.more"
msgstr "altro…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Accesso"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Sorgente"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "racolto e reso open-source da AA"

msgid "page.search.filters.language.header"
msgstr "Łéngua"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordina par"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Pì rełevante"

msgid "page.search.filters.sorting.newest"
msgstr "Pì novo"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(anno di pubblicazione)"

msgid "page.search.filters.sorting.oldest"
msgstr "Pì vecio"

msgid "page.search.filters.sorting.largest"
msgstr "Pì grando"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(filesize)"

msgid "page.search.filters.sorting.smallest"
msgstr "Pì cèo"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(codice sorgente aperto)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Casuale"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "L'indice de ricerca el vien aggiornà mensilmente. Al momento el include voci fino a %(last_data_refresh_date)s. Par più informazion tecniche, varda la pagina dei %(link_open_tag)sdataset</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Per esplorar l'indice de ricerca per codici, usa el <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Scrivi nel quadro par sercar nel nostro catalogo de %(count)s file scaricabili direttamente, che noi <a %(a_preserve)s>preservemo par sempre</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "In realtà, chiunque può aiutare a preservare questi file facendo seeding della nostra <a %(a_torrents)s>lista unificata di torrent</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Gavemo attualmente el catalogo open più completo del mondo de libri, articoli, e altre opere scritte. Specchiemo Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e altro</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Se trovi altre “biblioteche ombra” che dovremmo rispecchiare, o se hai domande, contattaci a %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Par reclami DMCA / copyright <a %(a_copyright)s>clicca qua</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Suggerimento: dopara le scorciatoie da tastiera “/” (focus su cerca), “enter” (cerca), “j” (su), “k” (giù), “<” (pagina precedente), “>” (pagina successiva) par navigar più veloce."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Sito papers?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Scrivi nel riquadro par cercar nel nostro catalogo de %(count)s articoli accademici e de rivista, che <a %(a_preserve)s>conservemo par sempre</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Scrivi nel riquadro per cercar file nelle biblioteche digitali in prestito."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Sto indice de ricerca al momento include i metadata da la biblioteca de Prestito Digitale Controllato de l'Internet Archive. <a %(a_datasets)s>Più informazioni sui nostri datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Par più biblioteche digitali, varda <a %(a_wikipedia)s>Wikipedia</a> e el <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Scrivi nel riquadro par cercar metadati da le biblioteche. Questo el pol esser utile quando <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Sto indice de ricerca al momento inclùde metadata da varie fonti de metadata. <a %(a_datasets)s>Più informazion sui nostri datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Par i metadati, mostremo i record originali. No femo fusion de record."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Ci sono molte, molte fonti di metadati per opere scritte in tutto il mondo. <a %(a_wikipedia)s>Questa pagina di Wikipedia</a> è un buon inizio, ma se conosci altre buone liste, faccelo sapere."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Scrivi nel riquadro par cercar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Questi sono record di metadati, <span %(classname)s>non</span> file scaricabili."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Errore durante la ricerca."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Prova a <a %(a_reload)s>ricaricar la pagina</a>. Se el problema persiste, manda un'email a %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nessun file trovato.</span> Prova con termini di ricerca e filtri diversi o meno specifici."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A volte questo succede erroneamente quando il server di ricerca è lento. In tali casi, <a %(a_attrs)s>ricaricare</a> può aiutare."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Abbiamo trovato corrispondenze in: %(in)s. Puoi fare riferimento all'URL trovato lì quando <a %(a_request)s>richiedi un file</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Articoli de rivista (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Prestito Digitale (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadati (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Risultati %(from)s-%(to)s (%(total)s in totale)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ corispondenze parziali"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d corrispondenze parziali"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Volontariato & Ricompense"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "L’Archivio de Anna se basa sui volontari come ti. Accogliemo tuti i livelli de impegno, e gavemo do categorie principali de aiuto che cerchemo:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lavoro leggero de volontariato:</span> se te poi dedicar solo qualche ora qua e là, ghe xe ancora tante maniere in cui te poi dar na man. Ricompensemo i volontari costanti con <span %(bold)s>🤝 abbonamenti all’Archivio de Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Lavoro di volontariato intenso (ricompense da USD$50 a USD$5,000):</span> se puoi dedicare molto tempo e/o risorse alla nostra missione, ci piacerebbe lavorare più da vicino con te. Alla fine potresti unirti al team interno. Anche se abbiamo un budget limitato, siamo in grado di assegnare <span %(bold)s>💰 ricompense monetarie</span> per il lavoro più intenso."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Se non puoi dedicare il tuo tempo come volontario, puoi comunque aiutarci molto <a %(a_donate)s>donando denaro</a>, <a %(a_torrents)s>seminando i nostri torrent</a>, <a %(a_uploading)s>caricando libri</a>, o <a %(a_help)s>parlando ai tuoi amici di Anna’s Archive</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Aziende:</span> offriamo accesso diretto ad alta velocità alle nostre collezioni in cambio di donazioni a livello aziendale o in cambio di nuove collezioni (ad esempio, nuove scansioni, datasets OCR, arricchimento dei nostri dati). <a %(a_contact)s>Contattaci</a> se sei interessato. Vedi anche la nostra <a %(a_llm)s>pagina LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Volontariato leggero"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Se hai qualche ora libera, puoi aiutare in diversi modi. Assicurati di unirti alla <a %(a_telegram)s>chat dei volontari su Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Come segno di apprezzamento, di solito offriamo 6 mesi di “Bibliotecario Fortunato” per traguardi di base, e di più per il lavoro di volontariato continuato. Tutti i traguardi richiedono lavoro di alta qualità — il lavoro scadente ci danneggia più di quanto ci aiuti e lo rifiuteremo. Per favore <a %(a_contact)s>mandaci un'email</a> quando raggiungi un traguardo."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Compito"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Traguardo"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Difondendo la parola de l'Archivio de Anna. Par esempio, racomandando libri su AA, colegando ai nostri post del blog, o in generale indirizando le persone al nostro sito."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s link o screenshot."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Questi i dovarìa mostrarte mentre informi qualcun de l'Archivio de Anna, e lori che te ringrazia."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Migliora i metadati <a %(a_metadata)s>collegandoli</a> con Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Te pol doparar la <a %(a_list)s >lista de problemi de metadata casuali</a> come punto de partenza."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assicurate de lassare un commento sui problemi che te sistemi, cussì che altri no duplichi el to lavoro."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s link dei record che hai migliorato."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Tradurre</a> il sito web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traduci completamente una lingua (se non era già quasi completata)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Migliora la pagina Wikipedia di Anna’s Archive nella tua lingua. Includi informazioni dalla pagina Wikipedia di AA in altre lingue, e dal nostro sito web e blog. Aggiungi riferimenti ad AA su altre pagine rilevanti."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link alla cronologia delle modifiche che mostra che hai fatto contributi significativi."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Soddisfare le richieste di libri (o articoli, ecc.) sui forum di Z-Library o Library Genesis. Non abbiamo un nostro sistema di richieste di libri, ma rispecchiamo quelle biblioteche, quindi migliorarle rende migliore anche Anna’s Archive."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s link o screenshot delle richieste che hai soddisfatto."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Piccoli compiti pubblicati nella nostra <a %(a_telegram)s>chat dei volontari su Telegram</a>. Di solito per l'adesione, a volte per piccole ricompense."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Compiti piccoli postai nel nostro gruppo de chat par volontari."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dipende dal compito."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Tagie"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Semo sempre in serca de persone con boni skill de programmazione o de sicurezza offensiva par partecipar. Ti pol far una gran differenza nel preservar l'eredità de l'umanità."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Come ringraziamento, regalemo l'abbonamento par contributi solidi. Come un gran ringraziamento, regalemo ricompense monetarie par compiti particolarmente importanti e difficili. No dovessi considerar questo come un sostituto de un lavoro, ma xe un incentivo in più e pol aiutar con i costi sostenui."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "La maggior parte del nostro codice xe open source, e chiederemo che anca el tuo codice sia tale quando assegnemo la ricompensa. Ghe xe qualche eccezione che podemo discuter individualmente."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Le ricompense xe assegnàe alla prima persona che completa un compito. Sentitevi liberi de commentar su un ticket de ricompensa par far saver agli altri che stai lavorando su qualcosa, così che gli altri possano aspettar o contattarti par far squadra. Ma stai atento che gli altri xe ancora liberi de lavorar su el compito e tentar de batterti. Comunque, no assegnemo ricompense par lavori fatti male. Se ghe xe do submission de alta qualità fatte vicine (entro un giorno o do), podemo sceglier de assegnar ricompense a entrambi, a nostra discrezione, per esempio 100%% par la prima submission e 50%% par la seconda submission (quindi 150%% in totale)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Par le ricompense più grandi (specialmente le ricompense de scraping), contattaci quando te gà completà ~5%% de el compito, e te sì sicuro che el tuo metodo se pol scalare al traguardo completo. Dovrai condividere el tuo metodo con noi così podemo dar feedback. Anca, in questo modo podemo decidere cosa far se ghe xe più persone che se avvicinano a una ricompensa, come potenzialmente assegnarla a più persone, incoraggiar le persone a far squadra, ecc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ATTENZIONE: i compiti con alta ricompensa xe <span %(bold)s>difficili</span> — podessi esser saggio iniziar con quelli più facili."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Vai alla nostra <a %(a_gitlab)s>lista de issue su Gitlab</a> e ordina per “Label priority”. Questo mostra più o meno l'ordine dei compiti che ne interessa. I compiti senza ricompense esplicite xe ancora eligibili par l'abbonamento, specialmente quelli segnati come “Accepted” e “Anna’s favorite”. Podessi voler iniziar con un “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Aggiornamenti su <a %(wikipedia_annas_archive)s>l'Archivio de Anna</a>, la più grande biblioteca veramente aperta nella storia umana."

#, fuzzy
msgid "layout.index.title"
msgstr "L'Archivio de Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "La più grande biblioteca open-source e open-data del mondo. Specchi de Sci-Hub, Library Genesis, Z-Library, e altro."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Serca in l'Archivio de Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "L'Archivio de Anna ga bisogno del to aiuto!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Tanti prova a butar ne zo, ma noi combatemo."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se donè adesso, riceverè <strong>dopio</strong> el numero de download veloci."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valido fin a la fine de sto mese."

msgid "layout.index.header.nav.donate"
msgstr "Donar"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvare el sapere umano: un gran bel regalo de festa!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Sorprendi un caro, daghe un account con abbonamento."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Par aumentar la resilienza de l'Archivio de Anna, sercemo volontari par far girar specchi."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Il regalo perfetto per San Valentino!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Gavémo un novo metodo de donazion disponibile: %(method_name)s. Par piaser considera de %(donate_link_open_tag)sdonar</a> — no xe economico mantener sto sito, e la to donazion fa veramente la diferenza. Grazie mile."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Stemo facendo una raccolta fondi par <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">salvar</a> la più grande biblioteca ombra de fumetti del mondo. Grazie par el to sostegno! <a href=\"/donate\">Dona.</a> Se no te poi donar, considera de sostenerne contandone ai to amici, e seguindone su <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Download recenti:"

msgid "layout.index.header.nav.search"
msgstr "Sercar"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Migliora i metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Volontariato & Ricompense"

msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Atività"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Esploratore de Codici"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Dati LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Home"

msgid "layout.index.header.nav.annassoftware"
msgstr "Programe de Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduir ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Accedi / Registrati"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Account"

msgid "layout.index.footer.list1.header"
msgstr "Archivo de Anna"

msgid "layout.index.footer.list2.header"
msgstr "Restar in contato"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "Reclami DMCA / copyright"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avanzato"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sicurezza"

msgid "layout.index.footer.list3.header"
msgstr "Alternative"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "no afilià"

msgid "page.search.results.issues"
msgstr "❌ 'Sto archivo el połe gaver dei problemi."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Download veloce"

#, fuzzy
msgid "page.donate.copy"
msgstr "copia"

#, fuzzy
msgid "page.donate.copied"
msgstr "copiato!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Precedente"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Avanti"

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Spècio Anónimo da Z-Library #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Donar"

#~ msgid "page.donate.header"
#~ msgstr "Donar"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive l'è un prozeto sensa intaresi monetari de còdeze averto, fato conpletamente da volontari. Asetemo łe donasiòn par covrir łe nostre speze, che łe include hosting, nomi de dominio, sviłupo, e altre."

#~ msgid "page.donate.text3"
#~ msgstr "Donasiòn recenti: %(donations)s. Grasie a tuti par ła vostra zenerozità. Ne piace molto ła fidusa che te si drio rimétar in noialtri, co cualsesìa l'inporto che te połi sparanjar."

#~ msgid "page.donate.text4"
#~ msgstr "Par donar, sfiora el to mètodo preferìo soto. In cazo de problemi, par piaser contatane a %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Carta de crèdito/dèbito"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Crypto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Domande"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Pasi:"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Crypto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(fonsiona anca par BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.text2"
#~ msgstr "Co łe to luminarie, nantri podemo mantenjir 'sto sito ativo, mejorar łe fonsionalità e conservar pì racolte."

#~ msgid "page.donate.cc.header"
#~ msgstr "Carta de crèdito/dèbito"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Và a %(link_open_tag)s'sta pàzina</a> e segui łe istrusiòn, o lezendo el còdeze QR o clicando el link “paypal.me”. Se 'l nò'l fonsionar, proa a azornar ła pàzina, zà che ła połe darte na conta difarente."

#~ msgid "page.donate.cc.text1"
#~ msgstr "Doparemo Sendwyre par depozitar danaro diretamente inte'l nostro portafojo de Bitcoin (BTC). El proceso el rechiede calche 5 menudi."

#~ msgid "page.donate.cc.text2"
#~ msgstr "'Sto metodo el gà un inporto mìnimo de transasiòn de $30, e na comisiòn de calche $5."

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copia ła indretura del nostro portafojo Bitcoin (BTC): %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Va a %(link_open_tag)s'sta pàzina<a/> e clica su \"cronpa cripto istantaneamente\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Taca ła indretura del nostro portafojo, e segui łe istrusión"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Par piaser, dopara %(link_open_tag)s'sta conta Alipay</a> par inviar ła to donasión. Se no'l fonsionar, proa a azornar ła pàzina, zà che ła połe darte na conta difarente."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "'Sto mètodo de donasión no'l ze atualmente disponìbiłe. Par piaser controła pì tarde. Te ringrasiemo par vołer donar, ło preziamo da vero!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Par piaser, dopara %(link_open_tag)s'sta pàzina Pix<a/> par inviar ła to donasión. Se no'l fonsionar, proa a azornar ła pàzina, zà che ła połe darte na conta difarente."

#~ msgid "page.donate.faq.header"
#~ msgstr "Domande frequenti"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> el ze un progeto che'l smira a catalogar tuti i libri ezistenti, incluindo dadi da tante fonti. Anca trasemo el progreso de l'umanità nel render tuti 'sti libri fasilmente disponìbiłi nt'un formà dizitałe, traverso “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibioteche-onbra</a>”. Altre informasión nte 'l <a href=\"/about\">'Sora'.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Libro (tuti)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Inisio"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datasets ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Njaùn rezultà"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” no'l ze un nùmaro ISBN vàłido. I ISBN i gà 10 o 13 caràteri, sensa contar i tratini opsionałi. Tuti i caràteri i gà da èsar numàri, altroche l'ùltemo, che'l połe èsar anca na “X”. L'ùltimo caràtere el ze el “caràtere de controło”, che gà da corespondar un vałor checksum otenjìo dal calcolo de i altri nùmari. El gà da èsar anca int'un range vàłido, destinà da ła agensia internasionałe ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Archivi corespondenti nte ła baze de dadi:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Njaùn archivo corispondente catà nte ła baze de dadi."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr ""

#~ msgid "page.donate.header.text3"
#~ msgstr ""

#~ msgid "page.donate.buttons.one_time"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Se te belche gà cryptomonede, 'ste l'è łe nostre indreture."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Grasie par jutarne! 'Sto prozeto no'l sarìa posibiłe sensa te."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr ""

#~ msgid "page.donate.login"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Inisio"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Sora"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donar"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datasets"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "App mòbiłe"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog de Anna"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Programe de Anna"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traduir"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datasets ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Njaùn rezultado"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" no'l par un DOI. El gà da scomìnsiar co \"10.\" e contenjir un slash."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL całònego: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "'Sto file el połe èsar in %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "File corespondenti inte ła nostra baze de dati:"

#~ msgid "page.doi.results.none"
#~ msgstr "Nisùn file corispondente catà nte ła nostra baze de dati."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Podoi contribuir de altre maniere?</div> Sì! Varda ła <a href=\"/about\">pàzina 'Sora'</a> nte ła sesión “Come jutarne”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Sora"

#~ msgid "page.about.header"
#~ msgstr "Sora"

#~ msgid "page.home.search.header"
#~ msgstr "Sercar"

#~ msgid "page.home.search.intro"
#~ msgstr "Sercar el nostro catàlogo de biblioteche-onbra."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr "Anna’s Archive l'è un motor de reserca sensa intaresi monetari de còdeze averto par łe “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteche-onbra</a>”. Lu l'è stà creà da <a href=\"http://annas-blog.org\">Anna</a>, che ła gà sentìo che ghe zera un bezonjo de un posto sentral par sercar dei libri, zornai, fumeti, reviste e altri documenti."

#~ msgid "page.about.text4"
#~ msgstr "Se te ghè un reciamo DMCA valido, vedi el fondo de 'sta pàzina, o contatane a %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Descoverzar libri"

#~ msgid "page.home.explore.intro"
#~ msgstr "'Sti i ze na conbinasión de libri popolari, e libri che i gà na sinjificansa spesiałe al mondo de bibioteche-onbra e a ła prezervasión dizitałe."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Sora"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "App mòbiłe"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Come jutarne"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr ""

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "solo questo mese!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ga <a %(a_closed)s>sospeso</a> el caricamento de nuovi articoli."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Seleziona un'opzione di pagamento. Offriamo sconti per i pagamenti in criptovaluta %(bitcoin_icon)s, perché comportano (molte) meno commissioni."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Seleziona un'opzione di pagamento. Attualmente abbiamo solo pagamenti basati su criptovalute %(bitcoin_icon)s, poiché i processori di pagamento tradizionali si rifiutano di lavorare con noi."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "No podemo supportar direttamente le carte de credito/debito, perché le banche no vol lavorar con noi. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Comunque, ghe xe diversi modi per usar le carte de credito/debito lo stesso, usando i nostri altri metodi de pagamento:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descàrreghe"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se te stai usando le criptovalute par la prima volta, te sugierimo de usar %(option1)s, %(option2)s, o %(option3)s par comprar e donar Bitcoin (la criptovaluta originale e più usata)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 collegamenti di record che hai migliorato."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 collegamenti o screenshot."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 collegamenti o screenshot delle richieste che hai soddisfatto."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se sei interessato a fare il mirroring di questi datasets per scopi di <a %(a_faq)s>archiviazione</a> o <a %(a_llm)s>addestramento LLM</a>, contattaci."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se te sì interessà a far un mirror de sto dataset par <a %(a_archival)s>archiviazion</a> o par <a %(a_llm)s>addestramento LLM</a>, par piaser contatene."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sito principale"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informazioni sui paesi ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se sei interessato a rispecchiare questo dataset per scopi di <a %(a_archival)s>archiviazione</a> o <a %(a_llm)s>addestramento LLM</a>, contattaci."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L'Agenzia Internazionale ISBN rilascia regolarmente le gamme che ha assegnato alle agenzie nazionali ISBN. Da questo possiamo derivare a quale paese, regione o gruppo linguistico appartiene questo ISBN. Attualmente utilizziamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Risorse"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultimo aggiornamento: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sito web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadati"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Escludendo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "La nostra ispirazion par collezionar metadata la xe el obiettivo de Aaron Swartz de “na pagina web par ogni libro mai pubblicà”, par la quale el ga creato <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Quel progetto è andato bene, ma la nostra posizione unica ci permette di ottenere metadata che loro non possono."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Un'altra ispirazione è stata il nostro desiderio di sapere <a %(a_blog)s>quanti libri ci sono nel mondo</a>, così possiamo calcolare quanti libri ci restano ancora da salvare."

#~ msgid "page.partner_download.text1"
#~ msgstr "Par dar a tuti l'opportunità de scaricar i file gratis, te ghè da spetàr <strong>%(wait_seconds)s secondi</strong> prima de poder scaricar sto file."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Rinfresca automaticamente la pagina. Se te perdi la finestra de download, el timer ripartirà, quindi xe racomandà el rinfresco automatico."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Scarica adesso"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr ""

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converti: dopara i strumenti online par convertire tra formati. Par esempio, par convertire tra epub e pdf, dopara <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descàrrega el file (pdf o epub i xe suportài), dopo <a %(a_kindle)s>mandelo a Kindle</a> doparàndo el sito, l'app, o l'email. Strumenti utili: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Supporta gli autori: Se ti piace questo e puoi permettertelo, considera di acquistare l'originale o di supportare direttamente gli autori."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Supporta le biblioteche: Se sto xe disponibile nella vostra biblioteca locale, considerè de prestarlo gratis là."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s No disponibile direttamente in massa, solo in semi-massa dietro un paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s L'Archivio de Anna gestisse na coleçion de <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb xe 'na azienda che raspa varie librerie online par catar i metadati dei ISBN. L'Archivio de Anna ga fato dei backup dei metadati dei libri de ISBNdb. Questi metadati xe disponibili tramite l'Archivio de Anna (anca se attualmente no i xe cercabili, a meno che no se cerchi esplicitamente un numero ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Par dettagli tecnici, varda qua soto. A un certo punto podemo dopararli par determinar quali libri manca ancora da le biblioteche ombra, par prioritizar quali libri catar e/o scannerizar."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "El nostro post sul blog riguardo sti dati"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Raspa de ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Attualmente gavemo un singolo torrent, che contiene un file gzipped de 4,4GB <a %(a_jsonl)s>JSON Lines</a> (20GB decompresso): “isbndb_2022_09.jsonl.gz”. Par importar un file “.jsonl” in PostgreSQL, podessi doparar qualcosa come <a %(a_script)s>sto script</a>. Podessi anca piparlo direttamente doparando qualcosa come %(example_code)s cussì che el decomprime in tempo reale."

#~ msgid "page.donate.wait"
#~ msgstr "Par piaser, speta almanco <span %(span_hours)s>do ore</span> (e rinfresca sta pagina) prima de contatarne."

#~ msgid "page.codes.search_archive"
#~ msgstr "Serca l'Archivio de Anna par “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Dona doparando Alipay o WeChat. Te pol scoler tra questi nella pagina successiva."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Diffondere la parola di Anna’s Archive sui social media e nei forum online, raccomandando libri o liste su AA, o rispondendo a domande."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La collezion de Fiction la xe divergià ma la ga ancora <a %(libgenli)s>torrenti</a>, anche se no i xe stati aggiornà dal 2022 (gavemo download diretti)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s L'Archivio de Anna e Libgen.li gestisse insieme coleçion de <a %(comics)s>fumeti</a> e <a %(magazines)s>riviste</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s No ghe xe torrent par le coleçion de narrativa russa e documenti standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "No ghe xe torrent disponibili par el contenuto aggiuntivo. I torrent che xe sul sito de Libgen.li xe specchi de altri torrent elencà qua. L'unica eccezione xe i torrent de narrativa che inizia da %(fiction_starting_point)s. I torrent de fumetti e riviste xe rilascià come collaborazione tra l'Archivio de Anna e Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Da na coleçion <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origine esatta sconossua. In parte da the-eye.eu, in parte da altre fonti."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

