��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ld �   df -   Ug   �g �  �h �  1j /  �k �   �l ?   �m k   n F   }n e   �n K  *o   vp �   �q w  @r �   �s �  �t �  9v �  �w �   �y Q  ]z \  �{ '   } �   4} H   �} �  C~    � �  	� F   �    N�    m�    �� :   �� !   Ԃ    �� '   	� !   1�    S�    o� -   |� (  �� �   ӄ B   �� )   � 
   
�    �    $�    8�    D�    `� 	   m�    w�    ��    ��    ��    �� 	   ͆ 
   ׆    �    �    
�     � �  9� <  '� x   d� -   ݊ 3  � �   ?� �   ͌ �   ��    W� �   s�    �� �   �     ��   ͏    ϐ D  � '   0� W   X�    ��   ��   ӓ �  � &  ��    �� $   �� K   ܗ �   (� $   �� &   Ә `   �� .   [� 6   �� '   ��    � d   � r  U� B   ț w   � �   �� >    � (  _� 3   �� �   �� w   �� �   /� H   �� �   �� �   �� F   � �   X� �   E� T   � ?   5� W   u� 	   ͤ �   פ �   X� F   W�    �� �   �� �   H�    � O  � �   ?�   ߩ T  � �   =� 0  )� �   Z� 8   ;� J   t� 6   �� d   �� L   [� K   ��    �� �   � <   �� <   �    � ]   *� <   �� �   Ŵ w   Y�    ѵ �   �   �� C  �� �  � x   � �   c�   !� �   *� �   ν �   \� W  � �   8� �   ��    �� �   �� �   -� �   �� b  �� t   �� h   f� �   �� {  s� o   ��   _� �   � 0   n� O   �� f   �� <   V� 6  �� �   �� H   x� `   �� #   "� �  F� �   � 9  ��    � B   4� ~   w� �   �� �   �� �   [� �  � �  �� !   A� E  c�   �� �   �� 1  �� �   ��   �� O   �� �   �    �� �   ��   I� ;  X� �   �� �   � �   d�    �� K  �� &  E� �   l�    '� �   .�   �� �   �� �  �� �   ^� O   � 
   n� `  |� 0  ��    � �   � �   �� �  �� �   �� 8  �� Q   �� K   9� �   �� 5   i�    ��   �� �   ��   �� 0   �� F   �� �  4� �   �� h   N� W   �� >       N    n J  ~ �   � m   { ?   � }   ) '   � �   � �   �    c    p    } *   � -   � *   � <    h   Z    � $   � 6   � <   2    o $   � $   � W   �    0	    7	 �   M	 �   @
 �   ( �   � r   � �   ,
   �
     �    �   � �   4   � A  � ~   k  � ;  � F   . $   u    � {   � �  / E  � �   � �   � �   � �  � �   � $   �  �   �    �! :  �"    �# �   $ �   �$ i   �%   "& �   9'    �'    ( U   2(    �( /  �( S  �*   , �   *. �   �.    l/ �   y/ �   F0 F   11 b   x1 �   �1 �   �2 {   �3 G  4 N   W5 �   �5    �6 �  �6 �   ]8 �   9   �: w  �< �   U>    �> �   ? �  �?    KA 8  XA z  �B �  D �  �E �   �G #  qH F   �I �  �I �   �K p   8L 0   �L R   �L    -M =   4M $   rM ,   �M #   �M    �M �   N �  �N �  &Q �  �R �   �T    �U f  �U �  X E  �Y b  [   y\ �  �^    x` �   `    ^a    ea X  ua �  �b �  �d   Of 	   Qh �  [h �  j �  �k �  $m g  o �  mq :  6s    qt �   �t `   (u v  �u I   x �   Jy u   �y �   Xz ,  {    4| �   F| 9   �| I   }    U} 0   f} 3   �} �  �} %  {   �� '   �� G  ؂ �    �    �� |   �� 3   '� e   [� �   �� $   ]� E   ��    ȇ �   ؇ �  Ո �  �� 1  9� b   k�    ΍ V   ލ   5�    P� �   T� b  ܏ �   ?� 3   ǒ 6   ��   2� -   E� �  s� /  b� �  �� !   7� �   Y� �   ޙ   t� n  ��    �� �   � -   � �   � �   �� �   R� �   � H   �� -   	� �   7� 3   	�   =� �   \� �   M� �   � E   �� #   � �   *� �  ƨ ;  �� �  �� m  H� ,   �� �   � �   ��   �� �   �� �  \� @  �� -   9� �  g� *   0� t  [� F   и    � �  *� �  �� ]  u� �   ӽ   �� �  �� �  i� �   � V  �� �  �� {   �� �  
�   �� 3   �� r   	� z   |�    ��    � �   � �   � 3   ��   �� �  �� �   h� �  h� W  � �   f� \  �   q�    z� �   ��    K� >  T�    ��    ��    ��    ��    ��    ��    �    �    +�    2� 	   N� 	   X� 	   b� 	   l�    v� 	   �� $   ��    ��    ��    ��    �� �   �� N   �� 0   ��    �    &�    B� *   ^� '   �� 0   �� 	   ��    ��    ��    �    �    ,�    9�    @�    G�    N� $   [� #   ��    �� )   �� 2   �� #   � '   @� #   h� *   �� B   ��    �� 9   � 9   A�    {� <   �� C   ��    �    � "   1�    T�    d�    }�    ��    ��    ��    ��    ��    �� 
   �� 	    � 
   
�    �    �    .�    6� 	   ?�    I� 	   \�    f�    �    �� 	   ��    ��    ��    ��    ��    ��    ��    �    � 
   2�    =� 6   M�    ��    ��    ��    ��    �� 	   ��    ��    ��    ��    �� c   	� r   m� M   �� �   .� �  �� z   W�    �� )   ��    �    �     �    '� 	   ?� $   I� U   n� 9   �� V   ��    U� 3   n�    �� T   �� Z   � �   q� c   f� ?   ��    
�    #�    6�    =�    D�    K�    _� 	   s�    }� 	   ��    ��    ��    ��    ��    ��    ��    ��    ��    �    � 	   �    #� 
   *�    5�    K�   [�    l�    r�    v�     }�    �� =   �� Z   �� 8   >� ,   w� 6   ��    ��    ��    �� v   ��    g�    m� +   }� s   ��    �    9� f   I� #   �� ?  �� ^   � j   s� �   �� �   m� 0   7� �   h� z   1� �   �� �   ��    ��    �� (   �� 4   �� Q   � _   c� X   �� D   � 9   a� %   �� &   ��    ��    �� H   � !   N�    p�    w�    ��    �� W   ��    �    � W   %�    }�    �� B   �� ]   �� �  A�    �     �  �   �     �    �    �    �    � (   �   �     	       $    4 �   ;     	   7    A .   H    w    ~ $   �    �    � "   � �   �    o    � 6   �    �    �    �     '    H   6     *   � �   � H   < C   �    � �   � E   �    � '   �    	 �   '	 �   �	    W
 7   p
 j   �
 �       �    �    � H   '   U
 !   }
    �
    �
    �
 �   �
    �    � $   � =   �            0 $   K �  p 9  i �  � 9   � 4   �    �     �    �    �   �    A �   W l  ?    �     � �  �   � 	   � 
   � 0   �    	 �       �      # �   �     `! u   |! $   �! !   " e   9"   �" �   �# �   �$ _  % c   t& �   �& Q   �' {   ( �   �( E   ) �   \) *   * !   1*    S* 	   `*    j* !   �*    �* ?   �* $   �* 	    + *   *+ �   U+ '   7, �   _, x   �, {   u- !   �-    .    ).    ?.    R.    q. $   �. !   �. �   �. $   �/ �   �/    |0 �   �0 n   N1 g   �1    %2 �   &3 -   �3 �   �3 	   d4    n4 i   o5    �5 Q  �5    >7    K7    [7    h7 *   �7    �7    �7 3   �7 �   �7 �   �8    :9    A9    H9 �   d9   
: �   ; q   �;    '<    =<    S<    f<    y<    �<    �< D   �< 0   �< �  = 6   �>    �> Q   ? T   V? O   �? W   �? K   S@ R   �@    �@ K   �@ @   EA {   �A ?   B C   BB    �B �   �B \   aC 8   �C \   �C N   TD 5   �D 	   �D 2   �D s   E �   �E =   F �   RF    G �   	G 0   �G _   �G �   XH 	   �H �  �H �   mJ    ?K $   OK    tK    {K �   �K (   4L f   ]L �   �L �   vM �   AN �   �N �   �O �   &P �   �P P   �Q   �Q Z   �R F  KS �   �T �   +U 
   	V 
   V 
   %V �   3V 
   �V 
   �V C   �V I   %W �   oW 
   .X T   <X J   �X r   �X /   OY `   Y ^   �Y 
   ?Z w   MZ 
   �Z 
   �Z 
   �Z �  �Z x   �\ d  0]    �^    �^ 	   �^ �   �^    L_ !   e_ �   �_ �   g`    �`    a $   +a 3   Pa 8   �a :   �a "   �a "   b �   >b    �b q  �b �   id x   -e L   �e �   �e �   yf �  Mg �  �h ~   �j o   qk i   �k    Kl *  al    �m 5  �m �   �n �   �o t   �p �   
q �   r �   �r p   6s (   �s 1   �s    t "   t 	   ;t 	   Et    Ot �   _t 	   u _   'u '   �u    �u    �u 	   �u    �u i   �u @   Ov 7   �v �   �v ,   �w    �w    �w    �w !   x    0x 	   Bx    Lx 	   Ux    _x    hx    qx 	   zx    �x �   �x    5y 	   Fy    Py 	   Yy    cy    ly    uy    ~y    �y    �y &   �y V   �y    )z &   <z L   cz �   �z �   S{ �   �{ �   �| �   {}   ~    4 �   G 3   � ;   � :   =� �   x� �   '� P   � L   R�    �� `   �� {   � �   �� 	   �    �    1�    D� 	   Y�    c�    |� )   �� 	   ��    ��    ˄    �    �    � #   :�    ^�    e�    x�    ��    ��    �� !   �� !   ̅ y   � �   h� *   �    G� ]   g� I   Ň k   � !   {� !   �� '   �� >   � >   &� '   e� �   �� �   U� 8  2�    k� C   �� �   Ɍ $   V� �   {� �   H� t   �� 2   l�    �� �   ��    H� c   Ȑ -   ,� K   Z� �   ��    j� '   ��    �� 7   �� b   �� b   \�    ��    Ɠ    ϓ    ֓ �   � 6   �    �� 1   ̔ !   ��     �     <� 0   ]� !   �� ^   �� 9   � �   I� @   � ]   _� �   �� ?   B�    ��    ��    ��    ژ    ��    �    /� *   K� *   v�   �� :   �� 9   �� ?   /�    o�    �� b   ��    �� �   v� �   "� 	   �� �   � )   m�    �� �   ��    g� '   z� *   �� 0   ͠ Z   �� K   Y� H   ��    � %   
� �   3� 5   ˢ !   � O   #� �   s� K   I� �   �� Z   � K   y� b   ť    (� .   B� c   q�    զ V   �    A� �   V� 5   �� <   5�    r� !   �� �   �� (   b� R   �� u   ީ O   T� 3   �� ~   ت l   W�    ī    ˫ B   ޫ :   !�    \�    o�    ��    ��    �� .   �� �   � n   x�    �    �    � 2   >� $   q� l   �� 9   � `   =� H   ��    � e   � �   i� 3   /� E   c�    �� ?   �� 3   ��    3� Y   F� d   �� -   � 8   3�    l� 0   |� B   ��    � ]   � *   a�    �� *   �� E   д �   �    �    ��    � 5   5�    k� V   �� 5   ݶ �   � s   ��    � $   ,� �   Q� $   �� �   � E   �� !   �� O   � �   i� 	   ?�    I�    K�    M� !   ]� *   � ?   ��    �     �    � !   � *   9� *   d�    �� *   �� ;   ��    ��    � $   (�    M�    f� N   v� �   Ž N   ��    ߾    � �   � 
  �� M   ��    ��   � n  2�    �� T   ��    � �  1� -   � ]   I�    ��    �� �  ��    d� V   }� c   �� c   8� ]   ��    �� a   � -   x�    �� t   �� ?   1� /   q� S   �� 0   �� L   &� �   s� �   � !   �� �   �� #  R� �   v� '   
� \  5� �   �� �   h� �   � �   �� !   }�    �� u   �� A   (� �  j� Z   � X   g�    ��    ��   ��    l� �   s�   !�   2� >  D� 6   �� N   �� Q   	� '   [� .   �� X   �� U   �    a�    z� 3   �� !   ��    �� $   � i   '� 0   ��    �� ]   �� �   -� y   ��    s�    ��    �� E   �� ^   �� Z   <� �   �� Y   P� $   ��    �� �   ��    �� �   �� s  O� �   �� A   �� #   ��    ��    ��    �� :   �� &   9� �   `� �   �� I   ��    ��    � *   (�    S� W   h� 
   �� @   ��    �    � +   0�    \�    l� W   s�    ��    ��    ��    �     � N   $� �   s� <   �� Q   ;� <   �� �   ��    m�    �� �   �� �   0� �   ��    �� c   �� <   �� 7   .� `   f� X   �� E    � 
   f� C   t�    ��    ��    ��    ��    ��    ��    �    �    )�    <� *   C� *   n� *   �� '   �� 2   �� +   �    K�    h�    �� 3   ��    ��    �� '   �� !   � E   -� $   s�    ��    �� '   ��    ��    � E   � 0   _� E   �� �   ��    s�    ��    ��    �� 0   �� 	   ��    �    � ]   =� "   �� $   ��    �� 
   �� 	   �� +   ��    +� �   >�    ��    ��    � #   5�    Y�    y�     �� "   �� &   �� -   � $   1�    V� \   ]� '   ��    ��    �� -   � R   <�    �� <   ��    �� `   � K   b� 6   ��    ��    �� 	     
        #     <  &  O  W   v    �    � )   �     )   %    O    T `   \ :   � �   �    � !   � u   � "   ^    �     � #   � -   � #       6 	   R I   \    � +   � 0   � �    3    ?   : M   z    � �   � $   c    � F   �    �    �    		 5   	 3   U	    �	 �   �	    *
    7
    D
    c
    y
    �
    �
    �
    �
 M   �
 �   D     !   9 �   [ �   
    �
    �
    �
    �
            /    H    ^    p    �    � 
   �    �    �    �    �    �    � �    �   � _  �   8 }  O �   � h  �    � �   �    � �   � �   � r  � �   � G  � 1    �   8 8   �     5   + :   a g   � [     �   `  �   �  �   �! �  N"    @$ �   S$ �   �$ `   �% �   �%    �& �   �& '  ^' �   �( �   8)    �) Z   �) c   C* �   �* r   g+ N   �+ v   ),    �,    �,    �, 2   �, *   -    C- {   V- D   �- _   .    w. �   �. f   / -   / C   �/ 4   �/ \   &0 K   �0 '   �0 c   �0 L   [1 ]   �1 ~   2    �2 !   �2 '   �2 J   �2 9   !3    [3    b3 K   i3    �3    �3    �3 *   �3 '   4 1   ;4    m4    �4    �4    �4 	   �4 E   �4 V   �4 R   J5 3   �5    �5    �5 $   �5    6    $6    +6    86    ?6    F6    M6    T6    a6    h6    o6 	   v6    �6    �6    �6    �6    �6    �6    �6    �6    �6    �6    	7 r   "7    �7 Q   �7 �   �7    �8 	   �8 	   �8    �8 	   �8    �8 	   �8 �   �8 x   �9 =   
:    H:    U: b   q:    �: y   �: u   [; "   �;    �; q   < �   v< Q   -= �   = ]   5> /   �> ~   �>    B?    W? H   m? �   �? $   :@ ~   _@ |   �@ ~   [A N   �A    )B    6B    =B    JB    QB 	   ^B    hB    uB z   �B `   
C t   kC �   �C �   �D ^   yE Q   �E �  *F a  �G �   I {   J   ~J (  �K    �L �   �L   ;M �   MN   �N h  �P p   ]R ?  �R    T F   !T �   hT B  �T .   BV �   qV 	   SW    ]W    dW �   wW 6   X Q   <X %   �X J   �X F   �X W   FY    �Y �   �Y ?   QZ &   �Z 6   �Z �   �Z    �[  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: zh
Language-Team: zh <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library是一个受欢迎的（且非法的）图书馆。他们将Library Genesis的收藏变得易于搜索。除此之外，他们在通过激励贡献用户以各种福利来征集新书方面变得非常有效。他们目前没有将这些新书返还给Library Genesis。而且与Library Genesis不同，他们没有让他们的收藏易于镜像，这阻碍了广泛的保存。这对他们的商业模式很重要，因为他们对批量访问他们的收藏（每天超过10本书）收费。 我们不对非法书籍收藏的批量访问收费做道德判断。毫无疑问，Z-Library在扩大知识获取和获取更多书籍方面取得了成功。我们只是来做我们的一部分：确保这个私人收藏的长期保存。 - Anna和团队（<a %(reddit)s>Reddit</a>） 在海盗图书馆镜像的最初发布中（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>），我们制作了Z-Library的镜像，这是一个大型非法书籍收藏。作为提醒，这是我们在最初的博客文章中写的内容： 那个收藏可以追溯到2021年中期。与此同时，Z-Library以惊人的速度增长：他们新增了大约380万本新书。里面确实有一些重复的书籍，但大多数似乎是全新的书籍，或者是之前提交书籍的更高质量扫描。这在很大程度上是因为Z-Library的志愿者管理员数量增加，以及他们的去重批量上传系统。我们想祝贺他们取得这些成就。 我们很高兴地宣布，我们已经获取了自上次镜像到2022年8月期间添加到Z-Library的所有书籍。我们还回过头来抓取了一些我们第一次错过的书籍。总的来说，这个新收藏大约有24TB，比上次的7TB大得多。我们的镜像现在总共是31TB。同样，我们对Library Genesis进行了去重，因为该收藏已经有可用的种子。 请访问海盗图书馆镜像以查看新收藏（编辑：已移至<a %(wikipedia_annas_archive)s>Anna的档案</a>）。那里有更多关于文件结构的信息，以及自上次以来的变化。我们不会从这里链接到它，因为这只是一个博客网站，不托管任何非法材料。 当然，做种也是帮助我们的好方法。感谢所有为我们之前的种子做种的人。我们对积极的回应表示感激，并且很高兴有这么多人以这种不同寻常的方式关心知识和文化的保存。 海盗图书馆镜像新增3倍新书（+24TB，380万本书） 阅读TorrentFreak的配套文章：<a %(torrentfreak)s>第一篇</a>，<a %(torrentfreak_2)s>第二篇</a> - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak的相关文章：<a %(torrentfreak)s>第一篇</a>，<a %(torrentfreak_2)s>第二篇</a> 不久前，“影子库”正面临消亡。Sci-Hub，这个庞大的非法学术论文档案库，由于诉讼停止了接收新作品。“Z-Library”，这个最大的非法图书库，其被指控的创建者因刑事版权指控被捕。他们不可思议地设法逃脱了逮捕，但他们的图书库仍然面临威胁。 一些国家已经在这样做了。TorrentFreak <a %(torrentfreak)s>报道</a>，中国和日本已经在其版权法中引入了AI例外。我们不清楚这如何与国际条约互动，但这无疑为其国内公司提供了保护，这解释了我们所看到的情况。 至于安娜档案——我们将继续基于道德信念的地下工作。然而，我们最大的愿望是走向光明，并合法地扩大我们的影响力。请改革版权。 当Z-Library面临关闭时，我已经备份了其整个图书库，并正在寻找一个平台来存放它。这就是我创建安娜档案的动机：延续那些早期计划背后的使命。自那时起，我们已成长为世界上最大的影子库，托管超过1.4亿份受版权保护的文本，涵盖多种格式——书籍、学术论文、杂志、报纸等。 我和我的团队是意识形态者。我们相信保存和托管这些文件在道德上是正确的。世界各地的图书馆都在削减资金，我们也不能将人类的遗产托付给企业。 然后，人工智能出现了。几乎所有构建LLM的大公司都联系了我们，以便在我们的数据上进行训练。大多数（但不是全部！）总部位于美国的公司在意识到我们工作的非法性质后重新考虑了。相比之下，中国公司热情地接受了我们的收藏，显然对其合法性毫不在意。这一点值得注意，因为中国是几乎所有主要国际版权条约的签署国。 我们为大约30家公司提供了高速访问。大多数是LLM公司，还有一些是数据经纪人，他们将转售我们的收藏。大多数是中国公司，但我们也与来自美国、欧洲、俄罗斯、韩国和日本的公司合作。DeepSeek <a %(arxiv)s>承认</a>，其早期版本曾在我们的部分收藏上进行训练，尽管他们对最新模型保持缄默（不过可能也在我们的数据上进行了训练）。 如果西方想在LLM和最终的AGI竞赛中保持领先地位，就需要重新考虑其在版权问题上的立场，并且要尽快。无论您是否同意我们的道德立场，这现在已经成为一个经济问题，甚至是国家安全问题。所有权力集团都在打造人工超级科学家、超级黑客和超级军事力量。信息自由正在成为这些国家的生存问题——甚至是国家安全问题。 我们的团队来自世界各地，并没有特定的立场。但我们鼓励那些拥有强大版权法的国家利用这一生存威胁来改革它们。那么该怎么做呢？ 我们的第一个建议很简单：缩短版权期限。在美国，版权在作者去世后授予70年。这是荒谬的。我们可以将其与专利对齐，专利在申请后授予20年。这应该足够让书籍、论文、音乐、艺术和其他创作作品的作者获得充分的报酬（包括电影改编等长期项目）。 那么，至少，政策制定者应该包括大规模保存和传播文本的例外条款。如果对个人客户的收入损失是主要担忧，个人层面的分发可以继续被禁止。反过来，那些有能力管理庞大存储库的人——训练LLM的公司，以及图书馆和其他档案馆——将被这些例外条款所涵盖。 版权改革对国家安全是必要的 简而言之：中国的LLM（包括DeepSeek）是基于我非法的书籍和论文档案进行训练的——这是世界上最大的。西方需要彻底改革版权法，以维护国家安全。 请参阅<a %(all_isbns)s>原始博客文章</a>以获取更多信息。 我们发起了一项挑战来改进这一点。我们将颁发一等奖6000美元，二等奖3000美元，三等奖1000美元。由于反响热烈和提交作品的精彩，我们决定略微增加奖金池，并颁发四个三等奖，每个500美元。获奖者如下，但请务必查看所有提交作品<a %(annas_archive)s>这里</a>，或下载我们的<a %(a_2025_01_isbn_visualization_files)s>合并种子</a>。 一等奖6000美元：phiresky 这个<a %(phiresky_github)s>提交</a>（<a %(annas_archive_note_2951)s>Gitlab评论</a>）完全是我们想要的一切，甚至更多！我们特别喜欢其极其灵活的可视化选项（甚至支持自定义着色器），同时还提供了全面的预设列表。我们也喜欢一切的快速和流畅，简单的实现（甚至不需要后端），巧妙的缩略图，以及他们在<a %(phiresky_github)s>博客文章</a>中的详细解释。令人难以置信的工作，实至名归的赢家！ - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 我们心中充满了感激。 值得注意的想法 稀有度摩天大楼 许多滑块用于比较Datasets，就像您是DJ一样。 带有书籍数量的比例尺。 漂亮的标签。 酷炫的默认配色方案和热图。 独特的地图视图和过滤器 注释，以及实时统计 实时统计 我们特别喜欢的一些想法和实现： 我们可以继续说下去，但就到这里吧。一定要查看所有提交<a %(annas_archive)s>这里</a>，或者下载我们的<a %(a_2025_01_isbn_visualization_files)s>综合种子</a>。如此多的提交，每一个都带来了独特的视角，无论是在用户界面还是实现上。 我们至少会将第一名的提交作品纳入我们的网站，可能还有其他一些。我们也开始考虑如何组织识别、确认并存档最稀有书籍的过程。更多信息即将推出。 感谢所有参与者。令人惊叹的是有这么多人关心。 轻松切换Datasets以便快速比较。 所有ISBN CADAL SSNOs CERLALC数据泄露 读秀SSIDs EBSCOhost的电子书索引 谷歌图书 Goodreads 互联网档案馆 ISBNdb ISBN全球出版商注册 Libby 安娜的档案中的文件 Nexus/STC OCLC/Worldcat OpenLibrary 俄罗斯国家图书馆 川陀帝国图书馆 第二名 $3,000：hypha “虽然完美的正方形和矩形在数学上令人愉悦，但在映射上下文中并不提供更好的局部性。我认为这些Hilbert或经典Morton曲线中固有的不对称性不是缺陷，而是特征。就像意大利著名的靴子形轮廓使其在地图上立即可识别一样，这些曲线的独特‘怪癖’可能作为认知地标。这种独特性可以增强空间记忆，帮助用户定位自己，可能使定位特定区域或注意到模式变得更容易。” 另一个令人难以置信的<a %(annas_archive_note_2913)s>提交</a>。虽然不如第一名灵活，但我们实际上更喜欢其宏观层面的可视化（空间填充曲线、边界、标签、突出显示、平移和缩放）。Joe Davis的<a %(annas_archive_note_2971)s>评论</a>引起了我们的共鸣： 还有很多可视化和渲染的选项，以及一个极其流畅和直观的用户界面。一个稳固的第二名！ - Anna和团队（<a %(reddit)s>Reddit</a>） 几个月前，我们宣布了一项<a %(all_isbns)s>$10,000奖金</a>，以制作我们数据的最佳可视化，展示ISBN空间。我们强调展示哪些文件我们已经/尚未存档，并且我们后来提供了一个数据集，描述了有多少图书馆持有ISBN（稀有度的衡量标准）。 我们对大家的反响感到不胜感激。创意层出不穷。衷心感谢每一位参与者：你们的活力和热情具有感染力！ 最终，我们希望回答以下问题：<strong>世界上有哪些书籍存在，我们已经归档了多少，以及接下来我们应该关注哪些书籍？</strong> 很高兴看到这么多人关心这些问题。 我们自己开始了一个基本的可视化。在不到300kb的情况下，这张图片简洁地代表了人类历史上最大规模的完全开放的“书籍列表”： 第三名 $500 #1：maxlion 在这个<a %(annas_archive_note_2940)s>提交</a>中，我们非常喜欢不同类型的视图，特别是比较和出版商视图。 第三名 $500 #2：abetusk 虽然不是最精致的用户界面，这个<a %(annas_archive_note_2917)s>提交</a>满足了很多要求。我们特别喜欢它的比较功能。 第三名 $500 #3：conundrumer0 像第一名一样，这个<a %(annas_archive_note_2975)s>提交</a>以其灵活性给我们留下了深刻印象。最终，这就是一个伟大可视化工具的关键：为高级用户提供最大的灵活性，同时保持对普通用户的简单性。 第三名 $500 #4：charelf 最后一个获得赏金的<a %(annas_archive_note_2947)s>提交</a>相当基础，但有一些我们非常喜欢的独特功能。我们喜欢他们展示有多少个Datasets覆盖特定ISBN作为受欢迎度/可靠性的衡量标准。我们也非常喜欢使用不透明度滑块进行比较的简单但有效的方法。 $10,000 ISBN可视化奖金的获奖者 简而言之：我们收到了令人难以置信的$10,000 ISBN可视化奖金提交。 背景 Anna的档案馆如何在不知道哪些书籍仍然存在的情况下实现备份全人类知识的使命？我们需要一个待办事项清单。绘制这张图的一种方法是通过ISBN号码，自20世纪70年代以来，大多数国家出版的每本书都被分配了ISBN。 没有一个中央机构知道所有ISBN的分配。相反，这是一个分布式系统，各国获得一系列数字，然后将较小的范围分配给主要出版商，后者可能会进一步将范围细分给次要出版商。最后，单个数字被分配给书籍。 我们在<a %(blog)s>两年前</a>开始通过抓取ISBNdb来映射ISBN。从那时起，我们抓取了更多的metadata来源，如<a %(blog_2)s>Worldcat</a>、Google Books、Goodreads、Libby等。完整列表可以在Anna的档案馆的“Datasets”和“种子”页面上找到。我们现在拥有迄今为止世界上最大、完全开放、易于下载的书籍metadata（因此也是ISBN）集合。 我们<a %(blog)s>详细写过</a>为什么我们关心保存，以及为什么我们目前处于一个关键窗口。我们现在必须识别稀有的、未被关注的和独特的高风险书籍并加以保存。拥有关于世界上所有书籍的良好metadata有助于实现这一目标。 $10,000 悬赏 将特别考虑可用性和外观。 在放大时显示单个 ISBN 的实际 metadata，例如标题和作者。 更好的空间填充曲线。例如，锯齿形，从第一行的 0 到 4，然后在第二行反向从 5 到 9——递归应用。 不同或可定制的配色方案。 用于比较Datasets的特殊视图。 调试问题的方法，例如其他不太一致的 metadata（例如，标题差异很大）。 用评论对ISBN或范围进行图像注释。 识别稀有或濒危书籍的任何启发式方法。 无论你能想到什么创意点子！ 代码 生成这些图像的代码以及其他示例可以在<a %(annas_archive)s>此目录</a>中找到。 我们设计了一种紧凑的数据格式，所有所需的ISBN信息压缩后约为75MB。数据格式的描述和生成代码可以在<a %(annas_archive_l1244_1319)s>这里</a>找到。为了获得奖励，你不必使用这个格式，但它可能是最方便的入门格式。你可以随意转换我们的metadata（尽管你所有的代码必须是开源的）。 我们迫不及待地想看看你会有什么创意。祝好运！ Fork 此仓库，并编辑此博客文章的 HTML（除了我们的 Flask 后端外，不允许使用其他后端）。 使上面的图片可以平滑缩放，以便您可以一直缩放到单个 ISBN。点击 ISBN 应该带您到 Anna 的档案的 metadata 页面或搜索。 您仍然必须能够在所有不同的Datasets之间切换。 国家范围和出版商范围应在悬停时突出显示。您可以使用例如 <a %(github_xlcnd_isbnlib)s>isbnlib 中的 data4info.py</a> 获取国家信息，以及我们的“isbngrp”抓取获取出版商信息（<a %(annas_archive)s>dataset</a>，<a %(annas_archive_2)s>torrent</a>）。 它必须在桌面和移动设备上运行良好。 这里有很多值得探索的内容，因此我们宣布为改进上述可视化提供悬赏。与我们大多数悬赏不同，这次是有时间限制的。您必须在 2025-01-31 (23:59 UTC) 前<a %(annas_archive)s>提交</a>您的开源代码。 最佳提交将获得 $6,000，第二名为 $3,000，第三名为 $1,000。所有悬赏将使用 Monero (XMR) 支付。 以下是最低标准。如果没有提交符合标准，我们可能仍会颁发一些悬赏，但这将由我们自行决定。 加分项（这些只是想法——让您的创造力尽情发挥）： 你可以完全偏离最低标准，做一个完全不同的可视化。如果它真的很出色，那么我们会酌情考虑给予奖励。 通过在<a %(annas_archive)s>此问题</a>下发表评论，附上你的分叉仓库、合并请求或差异的链接来提交作品。 - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 这张图片是1000×800像素。每个像素代表2,500个ISBN。如果我们有一个ISBN的文件，我们会让那个像素更绿。如果我们知道一个ISBN已经发行，但我们没有匹配的文件，我们会让它更红。 在不到300kb的情况下，这张图片简洁地代表了人类历史上最大规模的完全开放的“书籍列表”（完整压缩后几百GB）。 这也表明：在备份书籍方面还有很多工作要做（我们只有16%）。 可视化所有ISBN——$10,000赏金，截止日期2025-01-31 这张图片代表了人类历史上最大规模的完全开放的“书籍列表”。 可视化 除了概览图像外，我们还可以查看我们获取的各个Datasets。使用下拉菜单和按钮在它们之间切换。 在这些图片中可以看到许多有趣的模式。为什么会有一些线条和块状的规律性，这似乎在不同的尺度上发生？空白区域是什么？为什么某些Datasets如此集中？我们将这些问题留给读者作为练习。 - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 结论 通过这个标准，我们可以更逐步地发布版本，并更容易地添加新的数据源。我们已经有一些令人兴奋的发布计划在进行中！ 我们也希望其他影子库更容易地镜像我们的收藏。毕竟，我们的目标是永远保存人类的知识和文化，所以冗余越多越好。 示例 让我们以最近的Z-Library发布为例。它由两个集合组成：“<span style="background: #fffaa3">zlib3_records</span>”和“<span style="background: #ffd6fe">zlib3_files</span>”。这使我们能够分别抓取和发布metadata记录与实际书籍文件。因此，我们发布了两个带有metadata文件的种子： 我们还发布了一些带有二进制数据文件夹的种子，但仅限于“<span style="background: #ffd6fe">zlib3_files</span>”集合，总共62个： 通过运行<code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>，我们可以看到其中的内容： 在这种情况下，它是Z-Library报告的书籍的metadata。在顶层我们只有“aacid”和“metadata”，但没有“data_folder”，因为没有相应的二进制数据。AACID包含“22430000”作为主要ID，我们可以看到它取自“zlibrary_id”。我们可以预期该集合中的其他AAC具有相同的结构。 现在让我们运行<code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>： 这是一个小得多的AAC metadata，尽管这个AAC的大部分位于其他地方的一个二进制文件中！毕竟，这次我们有一个“data_folder”，所以我们可以预期相应的二进制数据位于<code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>。这个“metadata”包含了“zlibrary_id”，因此我们可以轻松地将其与“zlib_records”集合中的相应AAC关联起来。我们本可以通过多种不同的方式进行关联，例如通过AACID——标准并没有规定这一点。 请注意，“metadata”字段本身不一定是JSON。它可以是包含XML或任何其他数据格式的字符串。您甚至可以将metadata信息存储在关联的二进制数据块中，例如如果数据量很大。 异构文件和metadata，尽可能接近原始格式。 二进制数据可以通过像Nginx这样的网络服务器直接提供。 源库中的异构标识符，甚至缺乏标识符。 metadata与文件数据的单独发布，或仅metadata的发布（例如我们的ISBNdb发布）。 通过种子分发，但也可能采用其他分发方法（例如IPFS）。 不可变记录，因为我们应该假设我们的种子将永久存在。 增量发布/可追加发布。 机器可读和可写，方便快捷，特别是针对我们的技术栈（Python、MySQL、ElasticSearch、Transmission、Debian、ext4）。 人类检查相对容易，但这在机器可读性之后。 使用标准租用的种子盒轻松播种我们的收藏。 设计目标 我们不关心文件在磁盘上手动导航的容易性，或无需预处理即可搜索。 我们不关心与现有图书馆软件的直接兼容性。 虽然任何人都可以轻松使用种子播种我们的收藏，但我们不期望文件在没有显著技术知识和承诺的情况下可用。 我们的主要用例是分发来自不同现有集合的文件及相关metadata。我们最重要的考虑因素是： 一些非目标： 由于Anna的档案是开源的，我们希望直接使用我们的格式。当我们刷新搜索索引时，我们只访问公开可用的路径，以便任何分叉我们库的人都能快速启动和运行。 <strong>AAC。</strong> AAC（Anna的档案容器）是一个由<strong>metadata</strong>组成的单一项目，可选地包括<strong>二进制数据</strong>，两者都是不可变的。它有一个全球唯一的标识符，称为<strong>AACID</strong>。 <strong>AACID。</strong> AACID的格式如下：<code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>。例如，我们发布的一个实际AACID是<code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>。 <strong>AACID范围。</strong> 由于AACID包含单调递增的时间戳，我们可以用它来表示特定集合内的范围。我们使用这种格式：<code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>，其中时间戳是包含的。这与ISO 8601表示法一致。范围是连续的，并且可能重叠，但在重叠的情况下，必须包含与该集合中先前发布的相同记录（因为AAC是不可变的）。不允许缺少记录。 <code>{collection}</code>：集合名称，可以包含ASCII字母、数字和下划线（但不能有双下划线）。 <code>{collection-specific ID}</code>：集合特定的标识符（如果适用），例如Z-Library ID。可以省略或截断。如果AACID超过150个字符，则必须省略或截断。 <code>{ISO 8601 timestamp}</code>：ISO 8601的简短版本，总是以UTC表示，例如<code>20220723T194746Z</code>。这个数字必须在每次发布时单调递增，尽管其确切语义可以因集合而异。我们建议使用抓取或生成ID的时间。 <code>{shortuuid}</code>：一个UUID，但压缩为ASCII，例如使用base57。我们目前使用<a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python库。 <strong>二进制数据文件夹。</strong> 一个包含特定集合中一系列AAC的二进制数据的文件夹。其具有以下属性： 目录必须包含指定范围内所有AAC的数据文件。每个数据文件必须以其AACID作为文件名（无扩展名）。 目录名称必须是AACID范围，以<code style="color: green">annas_archive_data__</code>为前缀，无后缀。例如，我们的一个实际发布版本有一个名为<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>的目录。 建议将这些文件夹的大小控制在一定范围内，例如每个不超过100GB-1TB，尽管这一建议可能会随着时间的推移而改变。 <strong>集合。</strong> 每个AAC都属于一个集合，集合的定义是语义一致的AAC列表。这意味着如果你对metadata格式进行了重大更改，那么你必须创建一个新集合。 标准 <strong>Metadata文件。</strong> Metadata文件包含一个特定集合的AAC范围的metadata。这些文件具有以下属性： <code>data_folder</code>是可选的，是包含相应二进制数据的二进制数据文件夹的名称。该文件夹内相应二进制数据的文件名是记录的AACID。 每个JSON对象必须在顶层包含以下字段：<strong>aacid</strong>、<strong>metadata</strong>、<strong>data_folder</strong>（可选）。不允许有其他字段。 文件名必须是AACID范围，以<code style="color: red">annas_archive_meta__</code>为前缀，并以<code>.jsonl.zstd</code>为后缀。例如，我们的一个发布版本名为<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>。 如文件扩展名所示，文件类型是使用<a %(jsonlines)s>JSON Lines</a>压缩的<a %(zstd)s>Zstandard</a>。 <code>metadata</code>是任意的metadata，符合集合的语义。它必须在集合内语义一致。 <code style="color: red">annas_archive_meta__</code>前缀可以根据您的机构名称进行调整，例如<code style="color: red">my_institute_meta__</code>。 <strong>“记录”和“文件”集合。</strong> 按惯例，通常将“记录”和“文件”作为不同的集合发布是很方便的，这样它们可以根据不同的时间表发布，例如基于抓取速率。“记录”是一个仅包含metadata的集合，包含书名、作者、ISBN等信息，而“文件”是包含实际文件（pdf、epub）的集合。 最终，我们选择了一个相对简单的标准。它相当宽松，非规范化，并且仍在进行中。 <strong>种子文件。</strong> metadata文件和二进制数据文件夹可以打包在种子文件中，每个metadata文件一个种子，或每个二进制数据文件夹一个种子。种子文件必须以原始文件/目录名加上<code>.torrent</code>后缀作为其文件名。 <a %(wikipedia_annas_archive)s>安娜的档案</a>已成为迄今为止世界上最大的影子库，也是唯一一个完全开源和开放数据的同规模影子库。以下是我们Datasets页面上的一张表格（略有修改）： 我们通过三种方式实现了这一目标： 镜像现有的开放数据影子图书馆（如Sci-Hub和Library Genesis）。 帮助那些希望更加开放但没有时间或资源的影子图书馆（如Libgen漫画收藏）。 抓取不愿意批量分享的图书馆（如Z-Library）。 对于（2）和（3），我们现在自己管理了大量的种子集合（数百TB）。到目前为止，我们将这些集合视为一次性项目，这意味着为每个集合定制基础设施和数据组织。这为每次发布增加了显著的开销，并使得进行更多增量发布特别困难。 这就是为什么我们决定对发布进行标准化。这是一篇技术博客文章，我们在其中介绍了我们的标准：<strong>安娜的档案容器</strong>。 安娜的档案容器（AAC）：标准化全球最大影子库的发布 安娜的档案已成为世界上最大的影子库，这要求我们对发布进行标准化。 发布了300GB以上的书籍封面 最后，我们很高兴宣布一个小版本。与运营Libgen.rs分支的团队合作，我们通过种子和IPFS共享他们所有的书籍封面。这将把查看封面的负载分散到更多的机器上，并更好地保存它们。在许多（但不是全部）情况下，书籍封面包含在文件本身中，所以这算是“派生数据”。但将其放在IPFS中对于安娜的档案和各种Library Genesis分支的日常操作仍然非常有用。 像往常一样，您可以在海盗图书馆镜像中找到此版本（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>）。我们不会在此链接，但您可以轻松找到它。 希望我们现在可以稍微放松一下，因为我们有了一个不错的Z-Library替代品。这种工作负载并不特别可持续。如果您有兴趣帮助编程、服务器操作或保存工作，请务必联系我们。仍有很多<a %(annas_archive)s>工作要做</a>。感谢您的兴趣和支持。 切换到ElasticSearch 有些查询耗时极长，以至于会占用所有开放连接。 默认情况下，MySQL有一个最小字长，否则索引可能会变得非常大。有人报告无法搜索“Ben Hur”。 搜索只有在完全加载到内存中时才会稍微快一些，这需要我们购买更昂贵的机器来运行此操作，并在启动时预加载索引。 我们无法轻松扩展它以构建新功能，例如更好的<a %(wikipedia_cjk_characters)s>非空格语言的分词</a>、过滤/分面、排序、“您是想找”建议、自动完成等。 我们的一张<a %(annas_archive)s>工单</a>是我们搜索系统问题的杂项。我们使用MySQL全文搜索，因为我们的所有数据都在MySQL中。但它有其局限性： 在与一群专家交谈后，我们选择了ElasticSearch。它并不完美（他们默认的“您是说”建议和自动完成功能很糟糕），但总体来说，比MySQL在搜索方面要好得多。我们仍然不<a %(youtube)s>太热衷</a>于将其用于任何关键任务数据（尽管他们已经取得了很多<a %(elastic_co)s>进展</a>），但总体来说，我们对这一转换感到非常满意。 目前，我们已经实现了更快的搜索、更好的语言支持、更好的相关性排序、不同的排序选项以及按语言/书籍类型/文件类型的过滤。如果您对其工作原理感到好奇，<a %(annas_archive_l140)s>可以</a><a %(annas_archive_l1115)s>查看</a><a %(annas_archive_l1635)s>一下</a>。它相当易于访问，不过可能需要更多的注释…… 安娜的档案是完全开源的 我们相信信息应该是免费的，我们自己的代码也不例外。我们已经在我们私有托管的Gitlab实例上发布了我们所有的代码：<a %(annas_archive)s>安娜的软件</a>。我们还使用问题跟踪器来组织我们的工作。如果您想参与我们的开发，这是一个很好的起点。 为了让您了解我们正在进行的工作，以下是我们最近在客户端性能改进方面的工作。由于我们尚未实现分页功能，因此我们经常会返回非常长的搜索页面，包含100-200个结果。我们不想过早截断搜索结果，但这确实意味着会减慢某些设备的速度。为此，我们实施了一个小技巧：我们将大多数搜索结果包装在HTML注释中（<code><!-- --></code>），然后编写了一些JavaScript来检测何时应该显示结果，此时我们会取消注释： DOM“虚拟化”在23行代码中实现，无需复杂的库！这是在时间有限且需要解决实际问题时所产生的快速实用代码。据报道，我们的搜索现在在慢速设备上运行良好！ 另一个重大努力是自动化构建数据库。我们启动时，只是随意地将不同的来源拼凑在一起。现在我们希望保持它们的更新，因此我们编写了一些脚本，从两个Library Genesis分支下载新的metadata，并进行整合。目标不仅是让这对我们的档案有用，还要让任何想要玩转影子图书馆metadata的人都能轻松使用。目标是创建一个Jupyter笔记本，其中包含各种有趣的metadata，以便我们可以进行更多研究，比如找出<a %(blog)s>ISBN的保存比例</a>。 最后，我们改进了捐赠系统。您现在可以使用信用卡直接将资金存入我们的加密货币钱包，而无需真正了解加密货币。我们将继续监控这在实践中的效果，但这是一件大事。 随着Z-Library的关闭及其（据称）创始人被捕，我们一直在夜以继日地努力，为您提供一个好的替代方案——安娜的档案（我们不会在这里链接，但您可以在谷歌上搜索）。以下是我们最近取得的一些成就。 安娜的更新：完全开源的档案，ElasticSearch，300GB+的书籍封面 我们一直在夜以继日地努力，为您提供一个好的替代方案——安娜的档案。以下是我们最近取得的一些成就。 分析 语义重复（同一本书的不同扫描）理论上可以被过滤掉，但这很棘手。当我们手动查看漫画时，发现了太多的误报。 有一些仅通过MD5识别的重复项，这相对来说是浪费的，但过滤掉这些只会节省大约1% in。 在这个规模上，这仍然大约是1TB，但同样，在这个规模上，1TB并不重要。我们宁愿不冒险在此过程中意外破坏数据。 我们发现了一些非书籍数据，例如基于漫画书的电影。这似乎也是浪费，因为这些已经通过其他方式广泛可用。然而，我们意识到我们不能仅仅过滤掉电影文件，因为还有一些<em>互动漫画书</em>是在电脑上发布的，有人录制并保存为电影。 最终，我们能从集合中删除的任何东西只会节省几个百分点。然后我们想起我们是数据囤积者，而那些将要镜像这些数据的人也是数据囤积者，所以，“你说什么，删除？！” :) 当你将 95TB 的数据倒入你的存储集群时，你会试图弄清楚里面到底有什么……我们进行了一些分析，看看是否可以通过删除重复项来稍微减少大小。以下是我们的一些发现： 因此，我们向您展示完整、未修改的集合。这是大量的数据，但我们希望足够多的人会关心并继续分享它。 合作 鉴于其规模，这个合集长期以来一直在我们的愿望清单上，所以在我们成功备份 Z-Library 之后，我们将目光投向了这个合集。起初我们直接抓取它，这是一项相当大的挑战，因为他们的服务器状况不佳。我们通过这种方式获得了大约 15TB，但进展缓慢。 幸运的是，我们设法与图书馆的运营者取得了联系，他同意直接将所有数据发送给我们，这快了很多。即便如此，传输和处理所有数据仍然花费了半年多的时间，我们几乎因磁盘损坏而失去所有数据，这意味着要重新开始。 这次经历让我们相信，尽快将这些数据发布出去是很重要的，以便它可以被广泛镜像。我们只需一两次不幸的事件就可能永远失去这个合集！ 合集 快速行动确实意味着这个合集有点无序……让我们来看看。想象一下我们有一个文件系统（实际上我们正在通过种子文件分割它）： 第一个目录，<code>/repository</code>，是其中更结构化的部分。此目录包含所谓的“千目录”：每个目录包含一千个文件，这些文件在数据库中按顺序编号。目录<code>0</code>包含comic_id为0–999的文件，依此类推。 这与Library Genesis用于其小说和非小说收藏的方案相同。这个想法是，每个“千目录”一旦填满，就会自动转变为一个种子。 然而，Libgen.li 的运营者从未为这个合集制作种子文件，因此成千上万个目录可能变得不便，最终变成了“未分类目录”。这些是 <code>/comics0</code> 到 <code>/comics4</code>。它们都包含独特的目录结构，可能在收集文件时有意义，但现在对我们来说意义不大。幸运的是，metadata 仍然直接指向所有这些文件，因此它们在磁盘上的存储组织实际上并不重要！ metadata 以 MySQL 数据库的形式提供。这可以直接从 Libgen.li 网站下载，但我们也会将其与我们自己的包含所有 MD5 哈希的表一起通过种子文件提供。 <q>Barbara Gordon 博士试图在图书馆的平凡世界中迷失自己…</q> Libgen 分支 首先，一些背景信息。您可能知道 Library Genesis 因其庞大的图书收藏而闻名。较少人知道的是，Library Genesis 的志愿者还创建了其他项目，例如大量的杂志和标准文档收藏、Sci-Hub 的完整备份（与 Sci-Hub 的创始人 Alexandra Elbakyan 合作），以及确实是一个庞大的漫画收藏。 在某个时候，Library Genesis 镜像的不同运营者各奔东西，这导致了当前拥有多个不同“分支”的情况，所有这些分支仍然使用 Library Genesis 的名称。Libgen.li 分支独有这个漫画收藏，以及一个相当大的杂志收藏（我们也在处理这个）。 筹款活动 我们正在以一些大块的形式发布这些数据。第一个种子是<code>/comics0</code>，我们将其放入一个巨大的12TB .tar文件中。这比无数个小文件更适合您的硬盘和种子软件。 作为此次发布的一部分，我们正在进行筹款活动。我们希望筹集20,000美元以支付此集合的运营和合同费用，并支持正在进行和未来的项目。我们有一些<em>庞大的</em>项目正在进行中。 <em>我捐款支持的是谁？</em> 简而言之：我们正在备份人类的所有知识和文化，并使其易于访问。我们所有的代码和数据都是开源的，我们是一个完全由志愿者运营的项目，到目前为止我们已经保存了125TB的书籍（除了Libgen和Scihub现有的种子）。最终，我们正在构建一个飞轮，激励和鼓励人们寻找、扫描和备份世界上的所有书籍。我们将在未来的文章中写下我们的总体计划。:) 如果您捐赠12个月的“惊奇档案员”会员（780美元），您可以<strong>“领养一个种子”</strong>，这意味着我们会将您的用户名或信息放在其中一个种子的文件名中！ 您可以通过访问<a %(wikipedia_annas_archive)s>Anna的档案</a>并点击“捐赠”按钮来捐款。我们也在寻找更多志愿者：软件工程师、安全研究员、匿名商家专家和翻译人员。您还可以通过提供托管服务来支持我们。当然，请继续分享我们的种子！ 感谢所有已经如此慷慨支持我们的人！你们真的在创造不同。 以下是迄今为止发布的种子（我们仍在处理其余部分）： 所有种子文件可以在<a %(wikipedia_annas_archive)s>安娜的档案</a>的“Datasets”下找到（我们不会直接链接到那里，以免链接被Reddit、Twitter等移除）。从那里，跟随链接到Tor网站。 <a %(news_ycombinator)s>在 Hacker News 上讨论</a> 接下来是什么？ 一堆种子文件非常适合长期保存，但不太适合日常访问。我们将与托管合作伙伴合作，将所有这些数据上传到网络上（因为安娜的档案不直接托管任何内容）。当然，您可以在安娜的档案上找到这些下载链接。 我们也邀请大家利用这些数据！帮助我们更好地分析、去重、放到IPFS上、重新混合、用它训练你的AI模型等等。这些都是你的，我们迫不及待地想看看你会用它做些什么。 最后，如前所述，我们仍有一些重大发布即将到来（如果<em>某人</em>能<em>意外地</em>给我们发送一个<em>特定的</em>ACS4数据库的转储，你知道在哪里找到我们……），以及建立飞轮以备份世界上所有的书籍。 所以请继续关注，我们才刚刚开始。 - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 最大的漫画书影子库可能是某个 Library Genesis 分支：Libgen.li。运营该网站的管理员收集了一个疯狂的漫画合集，超过 200 万个文件，总计超过 95TB。然而，与其他 Library Genesis 合集不同，这个合集并没有通过种子文件批量提供。你只能通过他缓慢的个人服务器单独访问这些漫画——一个单点故障。直到今天！ 在这篇文章中，我们将告诉您更多关于这个合集的信息，以及我们为支持更多此类工作的筹款活动。 Anna的档案已经备份了世界上最大的漫画影子图书馆（95TB）——您可以帮助播种 世界上最大的漫画书影子图书馆曾有一个单点故障……直到今天。 警告：这篇博客文章已被弃用。我们决定IPFS尚未准备好用于主流。我们仍将在可能的情况下从安娜的档案链接到IPFS上的文件，但我们不再自己托管，也不建议其他人使用IPFS进行镜像。如果您想帮助保存我们的收藏，请查看我们的种子页面。 将5,998,794本书放在IPFS上 副本的倍增 回到我们最初的问题：我们如何声称可以永久保存我们的收藏？这里的主要问题是我们的收藏正在以快速的速度<a %(torrents_stats)s>增长</a>，通过抓取和开源一些庞大的收藏（在其他开放数据影子图书馆如Sci-Hub和Library Genesis已经完成的惊人工作之上）。 这种数据的增长使得收藏在全球范围内的镜像变得更加困难。数据存储很昂贵！但我们持乐观态度，尤其是在观察到以下三个趋势时。 我们收藏的<a %(annas_archive_stats)s>总量</a>，在过去几个月中，按种子用户数量划分。 来自不同来源的HDD价格趋势（点击查看研究）。 <a %(critical_window_chinese)s>中文版</a>，在<a %(reddit)s>Reddit</a>上讨论，<a %(news_ycombinator)s>Hacker News</a> 1. 我们已经摘取了低垂的果实 这一点直接来自我们上面讨论的优先事项。我们更愿意首先致力于解放大型收藏。现在我们已经确保了一些世界上最大的收藏，我们预计我们的增长将会慢得多。 仍然有许多较小的收藏，以及每天都有新书被扫描或出版，但速度可能会慢得多。我们可能仍会在规模上翻倍甚至三倍，但会在更长的时间内。 OCR改进。 优先事项 科学与工程软件代码 上述所有内容的虚构或娱乐版本 地理数据（例如地图、地质调查） 公司或政府的内部数据（泄漏） 测量数据，如科学测量、经济数据、公司报告 一般的metadata记录（包括非虚构和虚构；其他媒体、艺术、人物等；包括评论） 非虚构类书籍 非虚构类杂志、报纸、手册 非虚构类演讲、纪录片、播客的文字记录 有机数据，如DNA序列、植物种子或微生物样本 学术论文、期刊、报告 科学与工程网站、在线讨论 法律或法庭程序的文字记录 独特且面临被毁风险（例如因战争、资金削减、诉讼或政治迫害） 稀有 独特且未被关注 为什么我们如此关心论文和书籍？让我们暂且搁置我们对保存的基本信念——我们可能会为此另写一篇文章。那么，为什么特别是论文和书籍呢？答案很简单：<strong>信息密度</strong>。 每兆字节的存储中，书面文字在所有媒体中存储的信息最多。虽然我们关心知识和文化，但我们更关心前者。总体而言，我们发现信息密度和保存重要性的层次结构大致如下： 此列表中的排名有些随意——我们团队内部对几个项目有并列或分歧——而且我们可能忘记了一些重要类别。但这大致是我们的优先次序。 其中一些项目与其他项目差异太大，以至于我们不必担心（或已由其他机构处理），例如有机数据或地理数据。但此列表中的大多数项目对我们来说实际上很重要。 我们优先考虑的另一个重要因素是某一作品的风险程度。我们更愿意专注于以下作品： 最后，我们关心规模。我们的时间和资金有限，所以我们宁愿花一个月的时间拯救10,000本书，而不是1,000本书——如果它们的价值和风险相当。 <em><q>失去的无法恢复；但让我们拯救剩下的：不是通过金库和锁将它们与公众的视线和使用隔离开来，将它们交给时间的浪费，而是通过复制的增多，使它们超出意外的范围。</q></em><br>— 托马斯·杰斐逊，1791年 影子图书馆 代码可以在Github上开源，但Github整体上不能轻易被镜像和保存（尽管在这种情况下，大多数代码库有足够分布的副本） 在Worldcat网站上可以自由查看metadata记录，但不能批量下载（直到我们<a %(worldcat_scrape)s>抓取</a>它们） Reddit可以免费使用，但最近由于数据饥渴的LLM训练而采取了严格的反抓取措施（稍后会详细介绍） 有许多组织有着类似的使命和优先事项。确实，有图书馆、档案馆、实验室、博物馆和其他负责此类保存的机构。许多这些机构由政府、个人或公司提供充足的资金。但它们有一个巨大的盲点：法律体系。 这就是影子图书馆的独特角色，以及安娜的档案存在的原因。我们可以做其他机构不被允许做的事情。现在，并不是（经常）说我们可以存档其他地方非法保存的材料。不，在许多地方，建立一个包含任何书籍、论文、杂志等的档案是合法的。 但合法档案通常缺乏的是<strong>冗余和持久性</strong>。有些书籍在某个地方的实体图书馆中只有一本。有些metadata记录由单一公司保管。有些报纸仅在单一档案中以缩微胶卷保存。图书馆可能会遭遇资金削减，公司可能会破产，档案可能会被炸毁和烧毁。这不是假设——这种情况经常发生。 我们在安娜的档案中可以独特地做到的是大规模存储许多作品的副本。我们可以收集论文、书籍、杂志等，并批量分发。目前我们通过种子来实现这一点，但具体技术并不重要，并且会随着时间而改变。重要的是将许多副本分发到世界各地。200多年前的这句话至今仍然适用： 关于公共领域的简短说明。由于安娜的档案独特地专注于在世界许多地方非法的活动，我们不去处理广泛可用的收藏，例如公共领域的书籍。合法实体通常已经很好地照顾了这些。然而，有一些考虑因素使我们有时会处理公开可用的收藏： - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. 存储成本继续呈指数下降 3. 信息密度的改进 我们目前以原始格式存储书籍。虽然它们是压缩的，但通常它们仍然是页面的扫描件或照片。 到目前为止，缩小我们收藏总大小的唯一选择是通过更激进的压缩或去重。然而，要获得足够显著的节省，这两者对我们来说都损失太大。对照片进行重度压缩可能会使文本几乎无法阅读。而去重需要对书籍完全相同有很高的信心，这通常不够准确，尤其是当内容相同但扫描是在不同场合进行时。 一直以来还有第三种选择，但其质量如此糟糕以至于我们从未考虑过：<strong>OCR，或光学字符识别</strong>。这是将照片转换为纯文本的过程，通过使用AI检测照片中的字符。此类工具早已存在，并且相当不错，但“相当不错”不足以用于保存目的。 然而，最近的多模态深度学习模型取得了极其迅速的进展，尽管成本仍然很高。我们预计未来几年准确性和成本都会显著改善，以至于可以实际应用于我们的整个图书馆。 当那时到来时，我们可能仍会保留原始文件，但此外我们可以拥有一个大多数人都希望镜像的小得多的图书馆版本。关键是原始文本本身压缩得更好，并且更容易去重，从而为我们节省更多。 总体而言，期望总文件大小至少减少5-10倍并不不切实际，甚至可能更多。即使是保守的5倍减少，我们也将在10年内看到<strong>$1,000–$3,000的节省，即使我们的图书馆规模扩大三倍</strong>。 截至撰写本文时，<a %(diskprices)s>磁盘价格</a>每TB约为新磁盘12美元，二手磁盘8美元，磁带4美元。如果我们保守地只看新磁盘，这意味着存储一拍字节大约需要12,000美元。如果我们假设我们的图书馆将从900TB增长到2.7PB，这将意味着32,400美元来镜像我们的整个图书馆。加上电力、其他硬件成本等，我们将其四舍五入为40,000美元。或者使用磁带大约是15,000–20,000美元。 一方面，<strong>15,000–40,000美元对于所有人类知识的总和来说是个便宜货</strong>。另一方面，期望大量完整副本有点陡峭，尤其是如果我们还希望那些人继续为他人播种他们的种子。 那是今天。但进步在前进： 过去10年中，硬盘每TB的成本大约减少了三分之一，并可能继续以类似的速度下降。磁带似乎也在类似的轨迹上。SSD价格下降得更快，可能在本世纪末超过HDD价格。 如果这成立，那么在10年内，我们可能只需5,000–13,000美元来镜像我们的整个收藏（1/3），如果我们增长较少，甚至更少。虽然仍然是一大笔钱，但这将对许多人来说是可以实现的。而且可能会更好，因为下一个点…… 在安娜的档案馆，我们经常被问到，当我们的收藏总量已经接近1 PB（1000 TB）并且还在增长时，我们如何能声称永久保存我们的收藏。在本文中，我们将探讨我们的理念，并了解为什么未来十年对我们保存人类知识和文化的使命至关重要。 关键窗口 如果这些预测准确，我们<strong>只需等待几年</strong>，我们的整个收藏就会被广泛镜像。因此，用托马斯·杰斐逊的话来说，“置于意外之外”。 不幸的是，随着LLM的出现及其对数据的渴求，许多版权持有者变得更加防御。许多网站正在使抓取和存档变得更加困难，诉讼不断，而与此同时，实体图书馆和档案馆继续被忽视。 我们只能预期这些趋势会继续恶化，许多作品将在进入公共领域之前就被遗失。 <strong>我们正处于保存革命的前夕，但<q>失去的无法恢复。</q></strong> 我们有一个大约5-10年的关键窗口期，在此期间，运营影子图书馆和在世界各地创建许多镜像仍然相当昂贵，并且在此期间访问尚未完全关闭。 如果我们能跨越这个窗口，那么我们确实将永远保存人类的知识和文化。我们不应让这段时间白白浪费。我们不应让这个关键窗口在我们面前关闭。 让我们开始吧。 影子图书馆的关键窗口 当我们的收藏已经接近1 PB时，我们如何声称可以永久保存它们？ 收藏 关于收藏的一些更多信息。<a %(duxiu)s>读秀</a>是一个庞大的扫描书籍数据库，由<a %(chaoxing)s>超星数字图书馆集团</a>创建。大多数是学术书籍，扫描后以数字形式提供给大学和图书馆。对于我们的英语观众，<a %(library_princeton)s>普林斯顿</a>和<a %(guides_lib_uw)s>华盛顿大学</a>有很好的概述。还有一篇优秀的文章提供了更多背景信息：<a %(doi)s>“数字化中国书籍：超星读秀学者搜索引擎的案例研究”</a>（在安娜的档案中查找）。 读秀的书籍长期以来在中国互联网上被盗版。通常它们被转售商以不到一美元的价格出售。它们通常通过中国版的Google Drive分发，该平台经常被破解以允许更多存储空间。一些技术细节可以在<a %(github_duty_machine)s>这里</a>和<a %(github_821_github_io)s>这里</a>找到。 虽然这些书籍已经半公开地分发，但要大批量获取它们仍然相当困难。我们将此列为待办事项的首要任务，并为此分配了数月的全职工作。然而，最近一位令人难以置信、才华横溢的志愿者联系了我们，告诉我们他们已经完成了所有这些工作——付出了巨大的代价。他们与我们分享了完整的收藏，不求任何回报，只希望能长期保存。真是了不起。他们同意以这种方式寻求帮助，以便对收藏进行OCR处理。 该收藏有7,543,702个文件。这比Library Genesis的非小说类（约530万）还多。当前形式的总文件大小约为359TB（326TiB）。 我们欢迎其他建议和想法。请联系我们。查看安娜档案馆以获取有关我们收藏、保存工作以及您如何提供帮助的更多信息。谢谢！ 示例页面 为了向我们证明您有一个良好的管道，这里有一些示例页面供您开始使用，来自一本关于超导体的书。您的管道应能正确处理数学、表格、图表、脚注等。 将您处理过的页面发送到我们的电子邮件。如果它们看起来不错，我们会私下发送更多给您，并希望您能够快速在这些页面上运行您的流程。一旦我们满意，我们可以达成协议。 - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>中文版</a>，<a %(news_ycombinator)s>在Hacker News上讨论</a> 这是一篇简短的博客文章。我们正在寻找一些公司或机构来帮助我们对我们获得的大量收藏进行OCR和文本提取，以换取独家早期访问。在禁运期结束后，我们当然会发布整个收藏。 高质量的学术文本对于LLM的训练极为有用。虽然我们的收藏是中文的，但这对于训练英语LLM也应该有用：模型似乎无论源语言如何都能编码概念和知识。 为此，需要从扫描件中提取文本。安娜的档案能从中得到什么？为用户提供书籍的全文搜索。 因为我们的目标与LLM开发者一致，我们正在寻找一个合作伙伴。如果您能进行适当的OCR和文本提取，我们愿意给予您<strong>为期1年的独家提前访问此收藏的权限</strong>。如果您愿意与我们分享您的整个管道代码，我们愿意将该收藏的发布时间延长。 为LLM公司提供全球最大中文非小说书籍收藏的独家访问权限 <em><strong>简而言之：</strong> Anna的档案获得了一个独特的750万/350TB中文非小说书籍收藏——比Library Genesis更大。我们愿意给予一家LLM公司独家访问权限，以换取高质量的OCR和文本提取。</em> 系统架构 假设您找到了一些愿意托管您网站而不关闭您的公司的——我们称之为“热爱自由的提供商”😄。您会很快发现，使用他们托管所有内容相当昂贵，因此您可能希望找到一些“廉价提供商”并在那里进行实际托管，通过热爱自由的提供商进行代理。如果您做得对，廉价提供商将永远不知道您在托管什么，也不会收到任何投诉。 对于所有这些提供商来说，总是存在被关闭的风险，因此您还需要冗余。我们需要在我们技术栈的所有层面上实现这一点。 一家相对热爱自由的公司是Cloudflare，他们将自己置于一个有趣的位置。他们<a %(blog_cloudflare)s>辩称</a>自己不是托管提供商，而是像ISP一样的公用事业。因此，他们不受数字千年版权法或其他删除请求的约束，并将任何请求转发给您的实际托管提供商。他们甚至为保护这种结构而诉诸法律。因此，我们可以将他们用作另一层缓存和保护。 Cloudflare不接受匿名支付，因此我们只能使用他们的免费计划。这意味着我们无法使用他们的负载均衡或故障转移功能。因此，我们在域级别<a %(annas_archive_l255)s>自行实现了这些功能</a>。页面加载时，浏览器会检查当前域是否仍然可用，如果不可用，它会将所有URL重写为其他域。由于Cloudflare缓存了许多页面，这意味着即使代理服务器宕机，用户也可以进入我们的主域，然后在下一次点击时被转移到另一个域。 我们仍然需要处理正常的运营问题，例如监控服务器健康状况，记录后端和前端错误等。我们的故障转移架构在这方面也提供了更强的鲁棒性，例如在一个域上运行一组完全不同的服务器。我们甚至可以在这个独立域上运行旧版本的代码和数据集，以防主版本中的关键错误未被发现。 我们还可以通过从一个域名中移除Cloudflare来对其可能的反对进行对冲，比如这个独立的域名。这些想法的不同排列是可能的。 结论 学习如何建立一个强大且有弹性的影子库搜索引擎是一次有趣的经历。还有很多细节将在后续文章中分享，所以请告诉我您想了解更多的内容！ 一如既往，我们正在寻找捐款以支持这项工作，所以请务必查看安娜的档案上的捐赠页面。我们也在寻找其他类型的支持，例如资助、长期赞助商、高风险支付提供商，甚至可能是（有品味的！）广告。如果您想贡献您的时间和技能，我们一直在寻找开发人员、翻译人员等。感谢您的兴趣和支持。 创新代币 让我们从我们的技术栈开始。它故意保持简单。我们使用Flask、MariaDB和ElasticSearch。就是这样。搜索基本上是一个已解决的问题，我们不打算重新发明它。此外，我们必须将我们的<a %(mcfunley)s>创新代币</a>花在其他事情上：不被当局关闭。 那么安娜档案馆到底是合法还是非法的呢？这主要取决于法律管辖区。大多数国家相信某种形式的版权，这意味着人们或公司在一定时期内被赋予某些类型作品的独占垄断权。顺便说一句，在安娜档案馆，我们认为虽然版权有一些好处，但总体上对社会是负面的——但这是另一个故事。 这种对某些作品的独占垄断意味着，除了这个垄断之外的任何人直接分发这些作品都是非法的——包括我们。但安娜档案馆是一个搜索引擎，不直接分发这些作品（至少不是在我们的网站上），所以我们应该没问题，对吧？不完全是。在许多司法管辖区，不仅分发受版权保护的作品是非法的，链接到分发这些作品的地方也是非法的。一个经典的例子是美国的数字千年版权法。 这是光谱中最严格的一端。在光谱的另一端，理论上可能存在没有任何版权法的国家，但这些国家实际上并不存在。几乎每个国家都有某种形式的版权法。执行是另一个问题。有很多国家的政府不愿意执行版权法。也有一些国家介于这两个极端之间，禁止分发受版权保护的作品，但不禁止链接到这些作品。 另一个考虑因素是公司层面。如果一家公司在一个不关心版权的司法管辖区运营，但公司本身不愿承担任何风险，那么一旦有人投诉，他们可能会立即关闭您的网站。 最后，一个重要的考虑因素是支付。由于我们需要保持匿名，我们不能使用传统的支付方式。这使我们只能使用加密货币，而只有一小部分公司支持这些（有些是通过加密货币支付的虚拟借记卡，但它们通常不被接受）。 - 安娜和团队 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 我运营<a %(wikipedia_annas_archive)s>安娜档案馆</a>，这是全球最大的开源非营利搜索引擎，专注于<a %(wikipedia_shadow_library)s>影子图书馆</a>，如Sci-Hub、Library Genesis和Z-Library。我们的目标是让知识和文化易于获取，并最终建立一个由人们共同存档和保存<a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>世界上所有书籍</a>的社区。 在这篇文章中，我将展示我们如何运营这个网站，以及运营一个法律地位存疑的网站所带来的独特挑战，因为没有“影子慈善机构的AWS”。 <em>还请查看姐妹文章<a %(blog_how_to_become_a_pirate_archivist)s>如何成为海盗档案员</a>。</em> 如何运行影子库：安娜档案馆的操作 没有<q>影子慈善机构的AWS，</q>那么我们如何运行安娜档案馆？ 工具 应用服务器：Flask、MariaDB、ElasticSearch、Docker。 开发：Gitlab、Weblate、Zulip。 服务器管理：Ansible、Checkmk、UFW。 洋葱静态托管：Tor，Nginx。 代理服务器：Varnish。 让我们看看我们使用了哪些工具来完成这一切。随着我们遇到新问题并找到新解决方案，这一过程仍在不断演变。 有些决策我们反复斟酌。其中之一是服务器之间的通信：我们曾经使用Wireguard，但发现它偶尔会停止传输任何数据，或者只在一个方向上传输数据。这种情况发生在我们尝试的几种不同的Wireguard设置中，例如<a %(github_costela_wesher)s>wesher</a>和<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>。我们还尝试通过SSH隧道端口，使用autossh和sshuttle，但也遇到了<a %(github_sshuttle)s>问题</a>（尽管我仍不清楚autossh是否存在TCP-over-TCP问题——这对我来说感觉像是一个不太可靠的解决方案，但也许实际上是可以的？）。 相反，我们回到了服务器之间的直接连接，隐藏了服务器在廉价提供商上运行的事实，使用UFW进行IP过滤。这有一个缺点，即Docker与UFW配合不佳，除非您使用<code>network_mode: "host"</code>。所有这些都更容易出错，因为只需一个小的配置错误，您就会将服务器暴露在互联网上。也许我们应该回到autossh——在这里非常欢迎反馈。 我们也曾在Varnish和Nginx之间反复权衡。目前我们喜欢Varnish，但它确实有一些怪癖和粗糙的地方。同样适用于Checkmk：我们并不喜欢它，但目前它能用。Weblate还算可以，但并不出色——我有时担心每当我尝试将其与我们的git仓库同步时，它会丢失我的数据。Flask总体上不错，但它有一些奇怪的怪癖，导致调试花费了大量时间，比如配置自定义域，或与其SqlAlchemy集成的问题。 到目前为止，其他工具表现良好：我们对MariaDB、ElasticSearch、Gitlab、Zulip、Docker和Tor没有严重的抱怨。所有这些工具都有一些问题，但没有什么特别严重或耗时的。 社区 第一个挑战可能会让人感到意外。它不是技术问题，也不是法律问题。它是一个心理问题：在阴影中进行这项工作可能会非常孤独。根据你计划做的事情和你的威胁模型，你可能需要非常小心。在光谱的一端，我们有像Sci-Hub创始人Alexandra Elbakyan*这样的人，她对自己的活动非常开放。但如果她此时访问西方国家，她有被逮捕的高风险，可能面临数十年的监禁。你愿意承担这样的风险吗？我们在光谱的另一端；非常小心不留下任何痕迹，并拥有强大的操作安全性。 * 正如HN上的“ynno”所提到的，Alexandra最初不想被人知道：“她的服务器被设置为发出PHP的详细错误信息，包括故障源文件的完整路径，该文件位于目录/home/<USER>/ homelab”会变得无趣）。 这就是为什么找到一些社区是如此重要。你可以通过向一些非常亲密的朋友倾诉来放弃一些操作安全性，这些朋友是你知道可以深深信任的。即便如此，也要小心不要把任何东西写下来，以防他们不得不将他们的电子邮件交给当局，或者他们的设备以其他方式被破坏。 更好的是找到一些海盗同伴。如果你的亲密朋友有兴趣加入你，那就太好了！否则，你可能可以在网上找到其他人。遗憾的是，这仍然是一个小众社区。到目前为止，我们只找到了一小部分活跃在这个领域的人。好的起点似乎是Library Genesis论坛和r/DataHoarder。档案团队也有志同道合的人，尽管他们在法律范围内运作（即使在法律的灰色地带）。传统的“warez”和盗版场景也有一些思维方式相似的人。 我们对如何促进社区发展和探索想法持开放态度。欢迎在Twitter或Reddit上给我们留言。也许我们可以举办某种论坛或聊天小组。一个挑战是，使用常见平台时，这很容易被审查，所以我们必须自己托管。此外，在完全公开讨论（更多潜在参与）与私密讨论（不让潜在“目标”知道我们即将抓取他们）之间也存在权衡。我们需要考虑这一点。如果您对此感兴趣，请告诉我们！ 结论 希望这对刚开始的海盗档案管理员有所帮助。我们很高兴欢迎您进入这个世界，所以不要犹豫与我们联系。让我们尽可能多地保存世界的知识和文化，并将其广泛镜像。 项目 4. 数据选择 通常，您可以使用metadata来确定合理的数据子集进行下载。即使您最终想要下载所有数据，优先下载最重要的项目也是有用的，以防您被检测到并且防御措施得到改进，或者因为您需要购买更多磁盘，或者只是因为在您下载所有内容之前生活中出现了其他事情。 例如，一个收藏可能包含同一资源（如书籍或电影）的多个版本，其中一个被标记为最佳质量。优先保存这些版本是很有意义的。最终，您可能希望保存所有版本，因为在某些情况下，metadata可能被错误标记，或者版本之间可能存在未知的权衡（例如，“最佳版本”在大多数方面是最好的，但在其他方面可能较差，比如电影有更高的分辨率但缺少字幕）。 您还可以搜索您的metadata数据库以找到有趣的内容。托管的最大文件是什么，为什么它这么大？最小的文件是什么？在某些类别、语言等方面是否存在有趣或意想不到的模式？是否有重复或非常相似的标题？数据添加时是否有模式，比如某一天同时添加了很多文件？通过以不同方式查看数据集，您通常可以学到很多东西。 在我们的案例中，我们通过Library Genesis中的md5哈希对Z-Library的书籍进行了去重，从而节省了大量的下载时间和磁盘空间。不过，这是一种相当独特的情况。在大多数情况下，没有全面的数据库记录哪些文件已经被其他同行妥善保存。这本身就是一个巨大的机会。能够定期更新音乐和电影等内容的概览，这些内容已经在种子网站上广泛传播，因此在海盗镜像中优先级较低，那将是很棒的。 6. 分发 您拥有了数据，从而拥有了世界上第一个目标的海盗镜像（很可能）。在许多方面，最困难的部分已经结束，但最危险的部分仍在前方。毕竟，到目前为止，您一直在隐秘行动；在雷达下飞行。您所要做的就是始终使用良好的VPN，不在任何表格中填写您的个人信息（显然），并可能使用特殊的浏览器会话（甚至是不同的计算机）。 现在您必须分发数据。在我们的案例中，我们首先想将书籍贡献回Library Genesis，但很快发现了其中的困难（小说与非小说的分类）。因此，我们决定使用Library Genesis风格的种子进行分发。如果您有机会为现有项目做出贡献，那可以为您节省大量时间。然而，目前没有很多组织良好的海盗镜像。 假设您决定自己分发种子。尽量保持这些文件小，以便它们可以轻松地在其他网站上镜像。然后，您将不得不自己播种种子，同时保持匿名。您可以使用VPN（有或没有端口转发），或者用混合比特币支付Seedbox。如果您不知道这些术语的含义，您将需要进行大量阅读，因为了解这里的风险权衡很重要。 您可以在现有的种子网站上托管种子文件。在我们的案例中，我们选择实际托管一个网站，因为我们还希望以清晰的方式传播我们的理念。您可以以类似的方式自己进行此操作（我们使用Njalla进行域名和托管，并用混合比特币支付），但也可以随时联系我们，让我们为您托管种子。如果这个想法流行起来，我们希望随着时间的推移建立一个全面的海盗镜像索引。 至于VPN的选择，关于这一点已经有很多讨论，因此我们只重复选择信誉良好的VPN的普遍建议。经过实际法院测试的无日志政策和长期保护隐私的记录是我们认为风险最低的选择。请注意，即使您做对了所有事情，也永远无法将风险降至零。例如，当您在播种种子时，一个高度动机的国家行为者可能会查看VPN服务器的进出数据流，并推断出您的身份。或者您可能会以某种方式出错。我们可能已经出错过，并且还会再次出错。幸运的是，国家并不太关心<em>盗版</em>。 每个项目需要做出的一个决定是，是否使用与之前相同的身份发布。如果您继续使用相同的名称，那么早期项目中的操作安全错误可能会回来困扰您。但以不同的名称发布意味着您无法建立更持久的声誉。我们选择从一开始就拥有强大的操作安全性，以便可以继续使用相同的身份，但如果我们出错或情况需要，我们不会犹豫以不同的名称发布。 传播信息可能很棘手。如我们所说，这仍然是一个小众社区。我们最初在Reddit上发布，但真正获得关注是在Hacker News上。目前，我们的建议是在几个地方发布，看看会发生什么。再次，联系我们。我们很乐意传播更多海盗档案努力的信息。 1. 域名选择/哲学 知识和文化遗产的保存没有短缺，这可能会让人不知所措。这就是为什么通常有必要花点时间思考您的贡献可以是什么。 每个人对这件事的思考方式都不同，但这里有一些您可以问自己的问题： 在我们的情况下，我们特别关心科学的长期保存。我们知道Library Genesis，并且知道它通过种子多次被完全镜像。我们喜欢这个想法。然后有一天，我们中的一个人试图在Library Genesis上找到一些科学教科书，但找不到它们，这让我们怀疑它的完整性。然后我们在网上搜索这些教科书，并在其他地方找到了它们，这为我们的项目播下了种子。即使在我们知道Z-Library之前，我们就有了不试图手动收集所有这些书籍的想法，而是专注于镜像现有的收藏，并将它们贡献回Library Genesis。 您有哪些技能可以利用？例如，如果您是在线安全专家，您可以找到击败安全目标IP封锁的方法。如果您擅长组织社区，那么也许您可以围绕一个目标召集一些人。不过，了解一些编程知识是有用的，即使只是为了在整个过程中保持良好的操作安全。 什么是值得重点关注的高杠杆领域？如果你打算花X小时在海盗归档上，那么你如何才能获得最大的“投资回报”？ 你对这个问题有什么独特的思考方式？你可能有一些其他人可能错过的有趣想法或方法。 你有多少时间可以投入到这上面？我们的建议是从小项目开始，随着熟练度的提高再做更大的项目，但这可能会变得非常耗费时间。 你为什么对这个感兴趣？你热衷于什么？如果我们能找到一群人，他们都归档他们特别关心的东西，那就能覆盖很多！你会比普通人更了解你的热情所在，比如哪些是重要的数据需要保存，哪些是最好的收藏和在线社区，等等。 3. Metadata抓取 添加/修改日期：这样您可以稍后回来下载之前未下载的文件（尽管您通常也可以使用ID或哈希来实现）。 哈希（md5, sha1）：确认您正确下载了文件。 ID：可以是一些内部ID，但像ISBN或DOI这样的ID也很有用。 文件名/位置 描述、类别、标签、作者、语言等。 大小：用于计算您需要多少磁盘空间。 为了从网站上抓取metadata，我们保持了相对简单的方法。我们使用Python脚本，有时使用curl，并将结果存储在MySQL数据库中。我们没有使用任何可以映射复杂网站的高级抓取软件，因为到目前为止，我们只需要通过枚举ID和解析HTML来抓取一两种页面。如果页面不能轻松枚举，那么您可能需要一个合适的爬虫来尝试找到所有页面。 在您开始抓取整个网站之前，先手动尝试一下。自己浏览几十个页面，以了解其工作原理。有时您会在此过程中遇到IP封锁或其他有趣的行为。数据抓取也是如此：在深入研究这个目标之前，确保您能够有效地下载其数据。 为了绕过限制，您可以尝试一些方法。是否有其他IP地址或服务器托管相同的数据但没有相同的限制？是否有API端点没有限制，而其他端点有？在什么下载速率下您的IP会被封锁，封锁多长时间？或者您没有被封锁但被限速？如果您创建一个用户账户，情况会如何变化？您能否使用HTTP/2保持连接打开，这是否会增加您请求页面的速率？是否有页面一次列出多个文件，并且那里列出的信息是否足够？ 您可能想要保存的内容包括： 我们通常分两个阶段进行。首先，我们下载原始HTML文件，通常直接导入MySQL（以避免大量小文件，下面会详细讨论）。然后，在一个单独的步骤中，我们遍历这些HTML文件并将其解析为实际的MySQL表。这样，如果您在解析代码中发现错误，您就不必从头开始重新下载所有内容，因为您可以使用新代码重新处理HTML文件。这也通常更容易并行化处理步骤，从而节省一些时间（而且您可以在抓取运行时编写处理代码，而不必同时编写两个步骤）。 最后，请注意，对于某些目标，metadata抓取就是全部。有一些巨大的metadata集合没有得到妥善保存。 标题 领域选择/理念：您大致想要关注哪里，为什么？您有哪些独特的激情、技能和环境可以利用？ 目标选择：您将镜像哪个特定的收藏？ Metadata抓取：记录有关文件的信息，而不实际下载（通常更大）的文件本身。 数据选择：基于Metadata，缩小当前最相关的归档数据范围。可能是所有数据，但通常有合理的方法来节省空间和带宽。 数据抓取：实际获取数据。 分发：将其打包成种子，在某处宣布，让人们传播。 5. 数据抓取 现在您已经准备好实际批量下载数据。如前所述，此时您应该已经手动下载了一些文件，以更好地了解目标的行为和限制。然而，当您真正开始一次下载大量文件时，仍然会有意外情况发生。 我们的建议主要是保持简单。首先只需下载一堆文件。您可以使用Python，然后扩展到多线程。但有时更简单的方法是直接从数据库生成Bash文件，然后在多个终端窗口中运行多个文件以进行扩展。这里值得一提的一个快速技术技巧是使用MySQL中的OUTFILE，如果您在mysqld.cnf中禁用“secure_file_priv”，则可以在任何地方写入（如果您使用Linux，请确保也禁用/覆盖AppArmor）。 我们将数据存储在简单的硬盘上。以您现有的设备开始，并慢慢扩展。考虑存储数百TB的数据可能会让人不知所措。如果您面临这种情况，只需先放出一个好的子集，并在您的公告中请求帮助存储其余部分。如果您确实想自己获取更多硬盘，那么r/DataHoarder上有一些关于获得好交易的好资源。 尽量不要过于担心高级文件系统。很容易陷入设置ZFS之类的事情的陷阱。不过需要注意的一个技术细节是，许多文件系统无法很好地处理大量文件。我们发现一个简单的解决方法是创建多个目录，例如用于不同的ID范围或哈希前缀。 下载数据后，请务必使用metadata中的哈希检查文件的完整性（如果可用）。 2. 目标选择 可访问：没有使用大量保护层来阻止你抓取他们的metadata和数据。 特别见解：你对这个目标有一些特别的信息，比如你以某种方式对这个收藏有特别的访问权限，或者你找到了破解他们防御的方法。这不是必须的（我们即将进行的项目没有做任何特别的事情），但这肯定有帮助！ 大 所以，我们有了我们关注的领域，现在我们要镜像哪个特定的收藏？有几件事可以构成一个好的目标： 当我们在Library Genesis以外的网站上发现我们的科学教科书时，我们试图弄清楚它们是如何进入互联网的。然后我们发现了Z-Library，并意识到虽然大多数书籍不是首先在那里出现，但它们最终会出现在那里。我们了解了它与Library Genesis的关系，以及（财务）激励结构和优越的用户界面，这两者使其成为一个更完整的收藏。然后我们进行了一些初步的metadata和数据抓取，并意识到我们可以绕过他们的IP下载限制，利用我们的一名成员对大量代理服务器的特殊访问。 在您探索不同目标时，使用VPN和一次性电子邮件地址隐藏您的踪迹已经很重要，我们稍后会详细讨论。 独特：不是其他项目已经很好覆盖的。 当我们进行一个项目时，它有几个阶段： 这些阶段并不是完全独立的，通常后期阶段的见解会让您回到早期阶段。例如，在Metadata抓取过程中，您可能会意识到您选择的目标有超出您技能水平的防御机制（如IP封锁），因此您会回去寻找不同的目标。 - Anna和团队（<a %(reddit)s>Reddit</a>） 关于数字保存的原因，尤其是海盗档案保存，可以写整本书，但让我们为那些不太熟悉的人提供一个快速入门。世界正在比以往任何时候都生产更多的知识和文化，但同时也比以往任何时候都失去更多。人类主要将这种遗产托付给学术出版商、流媒体服务和社交媒体公司等企业，而他们往往没有被证明是优秀的管理者。看看纪录片《数字失忆》，或者任何Jason Scott的演讲。 有些机构在尽可能多地归档方面做得很好，但他们受法律限制。作为海盗，我们处于一个独特的位置，可以归档他们无法触及的收藏，因为版权执行或其他限制。我们还可以在全球范围内多次镜像收藏，从而增加适当保存的机会。 目前，我们不会讨论知识产权的利弊、违法行为的道德性、对审查制度的思考，或知识和文化获取的问题。把这些都放在一边，让我们深入探讨<em>如何</em>。我们将分享我们的团队如何成为海盗档案员，以及我们在此过程中学到的经验。当你踏上这段旅程时，会面临许多挑战，希望我们能帮助你解决其中的一些问题。 如何成为一名海盗档案员 第一个挑战可能会让人感到意外。它不是一个技术问题，也不是一个法律问题，而是一个心理问题。 在我们深入探讨之前，关于海盗图书馆镜像的两个更新（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>）： 我们收到了一些非常慷慨的捐赠。第一个是来自匿名个人的1万美元，他也一直在支持“bookwarrior”，Library Genesis的原始创始人。特别感谢bookwarrior促成了这次捐赠。第二个是来自另一位匿名捐赠者的1万美元，他在我们上次发布后与我们联系，并受到启发来帮助我们。我们还收到了一些较小的捐赠。非常感谢你们的慷慨支持。我们有一些令人兴奋的新项目正在筹备中，这将得到支持，所以请继续关注。 我们在第二次发布的大小上遇到了一些技术困难，但我们的种子现在已经上线并开始做种。我们还收到了一位匿名人士的慷慨提议，愿意在他们的超高速服务器上为我们的收藏做种，因此我们正在向他们的机器进行特别上传，之后其他下载收藏的人应该会看到速度的大幅提升。 博客文章 您好，我是安娜。我创建了<a %(wikipedia_annas_archive)s>安娜的档案</a>，这是世界上最大的影子库。这是我的个人博客，我和我的团队在这里写关于盗版、数字保存等内容。 在<a %(reddit)s>Reddit</a>上与我联系。 请注意，这个网站只是一个博客。我们这里只托管我们自己的文字。这里没有托管或链接任何种子或其他受版权保护的文件。 <strong>图书馆</strong> - 像大多数图书馆一样，我们主要关注书籍等书面材料。未来我们可能会扩展到其他类型的媒体。 <strong>镜像</strong> - 我们严格作为现有图书馆的镜像。我们专注于保存，而不是让书籍易于搜索和下载（访问）或培养一个贡献新书的大型社区（来源）。 <strong>海盗</strong> - 我们故意违反大多数国家的版权法。这使我们能够做法律实体无法做到的事情：确保书籍被广泛镜像。 <em>我们不会从这个博客链接到文件。请自行查找。</em> - Anna和团队（<a %(reddit)s>Reddit</a>） 该项目（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>）旨在为人类知识的保存和解放做出贡献。我们在前人的伟大足迹上，做出我们小而谦逊的贡献。 这个项目的重点通过其名称得以体现： 我们镜像的第一个图书馆是Z-Library。这是一个受欢迎（且非法）的图书馆。他们将Library Genesis的收藏变得易于搜索。除此之外，他们通过激励贡献用户以各种福利，变得非常有效地征集新书贡献。目前，他们并没有将这些新书贡献回Library Genesis。而且与Library Genesis不同，他们没有让他们的收藏易于镜像，这阻碍了广泛的保存。这对他们的商业模式很重要，因为他们对批量访问其收藏（每天超过10本书）收费。 我们不对非法书籍收藏的批量访问收费做道德判断。毫无疑问，Z-Library在扩大知识获取和获取更多书籍方面取得了成功。我们只是来做我们的一部分：确保这个私人收藏的长期保存。 我们希望邀请您通过下载和做种我们的种子来帮助保存和解放人类知识。有关数据如何组织的更多信息，请参阅项目页面。 我们也非常欢迎您贡献您的想法，关于接下来要镜像哪些收藏，以及如何进行。我们可以一起实现很多。这只是无数贡献中的一小部分。感谢您所做的一切。 介绍海盗图书馆镜像：保存7TB的书籍（不在Libgen中） 10% o人类书面遗产永久保存 <strong>Google。</strong> 毕竟，他们为Google Books进行了这项研究。然而，他们的metadata无法批量访问，并且相当难以抓取。 <strong>各种独立的图书馆系统和档案馆。</strong> 有些图书馆和档案馆没有被上述任何一个索引和聚合，通常是因为资金不足，或者出于其他原因不愿与Open Library、OCLC、Google等分享他们的数据。许多这些确实有通过互联网访问的数字记录，并且通常没有很好地保护，因此如果您想帮助并乐于学习奇怪的图书馆系统，这些是很好的起点。 <strong>ISBNdb。</strong> 这是这篇博客文章的主题。ISBNdb从各种网站抓取书籍metadata，特别是定价数据，然后将其出售给书商，以便他们可以根据市场的其余部分为他们的书定价。由于ISBN在当今相当普遍，他们实际上建立了“每本书的网页”。 <strong>Open Library。</strong> 如前所述，这是他们的整个使命。他们从合作图书馆和国家档案馆中获取了大量的图书馆数据，并继续这样做。他们还有志愿图书管理员和技术团队，试图去重记录，并用各种metadata标记它们。最重要的是，他们的数据集是完全开放的。您可以简单地<a %(openlibrary)s>下载</a>。 <strong>WorldCat。</strong> 这是一个由非营利组织OCLC运营的网站，OCLC销售图书馆管理系统。他们从许多图书馆中聚合书籍metadata，并通过WorldCat网站提供。然而，他们也通过销售这些数据赚钱，因此无法批量下载。他们确实与特定图书馆合作，提供一些更有限的批量数据集供下载。 1. 对“永远”的某种合理定义。;) 2. 当然，人类的书面遗产远不止书籍，尤其是在当今时代。为了这篇文章和我们最近的发布，我们专注于书籍，但我们的兴趣更广泛。 3. 关于Aaron Swartz还有很多可以说的，但我们只想简要提及他，因为他在这个故事中扮演了关键角色。随着时间的推移，可能会有更多人第一次听到他的名字，并随后自己深入了解。 <strong>实体副本。</strong> 显然这不是很有帮助，因为它们只是相同材料的重复。如果我们能保存人们在书中做的所有注释，比如费马著名的“边缘涂鸦”，那就太酷了。但遗憾的是，这将仍然是档案管理员的梦想。 <strong>“版本”。</strong> 在这里，您计算书籍的每个独特版本。如果它的任何方面不同，比如不同的封面或不同的前言，它就算作不同的版本。 <strong>文件。</strong> 在与影子图书馆如Library Genesis、Sci-Hub或Z-Library合作时，还有一个额外的考虑因素。可能会有同一版本的多次扫描。人们可以通过使用OCR扫描文本或纠正角度扫描的页面来制作现有文件的更好版本。我们希望只将这些文件计为一个版本，这需要良好的metadata，或使用文档相似性度量进行去重。 <strong>“作品”。</strong> 例如，“哈利·波特与密室”作为一个逻辑概念，涵盖了它的所有版本，如不同的翻译和再版。这是一种有用的定义，但很难划定界限。例如，我们可能希望保存不同的翻译，尽管只有细微差别的再版可能不那么重要。 - Anna和团队（<a %(reddit)s>Reddit</a>） 通过海盗图书馆镜像（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>），我们的目标是收集世界上所有的书籍，并永久保存它们。<sup>1</sup>在我们的Z-Library种子和原始Library Genesis种子之间，我们有11,783,153个文件。但这到底是多少呢？如果我们正确地对这些文件进行去重，我们保存了世界上多少百分比的书籍？我们真的希望有这样的东西： 让我们从一些粗略的数字开始： 在Z-Library/Libgen和Open Library中，书籍数量远多于唯一的ISBN。这是否意味着许多书籍没有ISBN，或者只是缺少ISBN元数据？我们可能可以通过基于其他属性（标题、作者、出版商等）的自动匹配、引入更多数据源以及从实际书籍扫描中提取ISBN（在Z-Library/Libgen的情况下）来回答这个问题。 这些ISBN中有多少是唯一的？这最好用维恩图来说明： 更精确地说： 我们对重叠之少感到惊讶！ISBNdb有大量的ISBN没有出现在Z-Library或Open Library中，其他两个也是如此（虽然程度较小但仍然显著）。这引发了许多新问题。自动匹配在标记未标记ISBN的书籍方面能有多大帮助？会有很多匹配从而增加重叠吗？另外，如果我们引入第四或第五个数据集，会看到多少重叠？ 这确实为我们提供了一个起点。我们现在可以查看所有不在Z-Library数据集中的ISBN，并且也不匹配标题/作者字段。这可以帮助我们保存世界上的所有书籍：首先通过在互联网上抓取扫描件，然后在现实生活中扫描书籍。后者甚至可以通过众筹实现，或者由希望看到特定书籍数字化的人提供“赏金”来驱动。所有这些都是另一个时间的故事。 如果您想帮助其中的任何一项——进一步分析；抓取更多元数据；寻找更多书籍；对书籍进行OCR；在其他领域（如论文、有声书、电影、电视节目、杂志）中进行这些工作，甚至将这些数据用于机器学习/大语言模型训练等用途——请联系我（<a %(reddit)s>Reddit</a>）。 如果您对数据分析特别感兴趣，我们正在努力使我们的数据集和脚本以更易于使用的格式提供。如果您能直接分叉一个笔记本并开始使用，那就太好了。 最后，如果您想支持这项工作，请考虑捐款。这是一个完全由志愿者运营的项目，您的贡献会产生巨大的影响。每一点帮助都很重要。目前我们接受加密货币捐款；请参阅Anna的档案馆的捐赠页面。 对于百分比，我们需要一个分母：有史以来出版的书籍总数。<sup>2</sup>在Google Books消亡之前，该项目的一名工程师Leonid Taycher<a %(booksearch_blogspot)s>试图估算</a>这个数字。他开玩笑地得出了129,864,880（“至少到星期天”）。他通过建立一个世界上所有书籍的统一数据库来估算这个数字。为此，他汇集了不同的数据集，然后以各种方式将它们合并。 顺便说一句，还有另一个人试图将世界上所有的书籍编目：已故的数字活动家和Reddit联合创始人Aaron Swartz。<sup>3</sup> 他<a %(youtube)s>创办了Open Library</a>，目标是“为每本出版的书创建一个网页”，结合来自许多不同来源的数据。他最终为他的数字保存工作付出了最高的代价，因为他因批量下载学术论文而被起诉，导致他自杀。不用说，这就是我们小组使用化名的原因之一，也是我们非常小心的原因。Open Library仍然由互联网档案馆的工作人员英勇地运营，继续Aaron的遗产。我们将在本文后面回到这一点。 在Google的博客文章中，Taycher描述了估算这个数字的一些挑战。首先，什么构成一本书？有几种可能的定义： “版本”似乎是“书籍”最实用的定义。方便的是，这个定义也用于分配唯一的ISBN号。ISBN，即国际标准书号，通常用于国际商务，因为它与国际条码系统（“国际商品编号”）集成在一起。如果您想在商店中销售书籍，就需要一个条码，因此您需要获得ISBN。 Taycher的博客文章提到，虽然ISBN很有用，但它们并不普遍，因为它们实际上是在七十年代中期才被广泛采用，并且并非在全球范围内都使用。尽管如此，ISBN可能是书籍版本中最广泛使用的标识符，因此它是我们最好的起点。如果我们能找到世界上所有的ISBN，我们就能得到一份有用的书籍清单，知道哪些书籍仍需保存。 那么，我们从哪里获取数据呢？目前有许多现有的努力正在尝试编制世界上所有书籍的清单： 在这篇文章中，我们很高兴宣布一个小型发布（与我们之前的Z-Library发布相比）。我们抓取了大部分ISBNdb，并将数据在海盗图书馆镜像网站上提供种子下载（编辑：已移至<a %(wikipedia_annas_archive)s>安娜的档案</a>；我们不会在此直接链接，只需搜索即可）。这些大约有3090万条记录（20GB作为<a %(jsonlines)s>JSON Lines</a>；4.4GB压缩后）。在他们的网站上，他们声称实际上有3260万条记录，所以我们可能遗漏了一些，或者<em>他们</em>可能做错了什么。无论如何，目前我们不会分享我们是如何做到的——我们将其留作读者的练习。😉 我们将分享一些初步分析，以尝试更接近估算世界上书籍的数量。我们查看了三个数据集：这个新的ISBNdb数据集，我们从Z-Library影子库（包括Library Genesis）抓取的元数据的原始发布，以及Open Library的数据转储。 ISBNdb转储，或有多少书籍被永久保存？ 如果我们正确地对影子图书馆的文件进行去重，我们保存了世界上多少百分比的书籍？ 关于<a %(wikipedia_annas_archive)s>安娜的档案</a>的更新，这是人类历史上最大的真正开放图书馆。 <em>WorldCat改版</em> 数据 <strong>格式？</strong> <a %(blog)s>Anna的档案容器 (AAC)</a>，本质上是用<a %(jsonlines)s>JSON Lines</a>压缩的<a %(zstd)s>Zstandard</a>，加上一些标准化的语义。这些容器包装了我们部署的不同抓取类型的记录。 一年前，我们<a %(blog)s>着手</a>回答这个问题：<strong>影子图书馆永久保存了多少百分比的书籍？</strong> 让我们看看关于数据的一些基本信息： 一旦一本书进入像<a %(wikipedia_library_genesis)s>Library Genesis</a>这样的开放数据影子库，现在还有<a %(wikipedia_annas_archive)s>安娜的档案</a>，它就会在全球范围内被镜像（通过种子），从而实际上永远保存下来。 要回答保存了多少百分比的书籍这个问题，我们需要知道分母：总共有多少书籍？理想情况下，我们不仅有一个数字，还有实际的metadata。然后我们不仅可以将它们与影子图书馆匹配，还可以<strong>创建一个待办书籍清单以供保存！</strong> 我们甚至可以开始梦想一个众包的努力来完成这个待办清单。 我们抓取了<a %(wikipedia_isbndb_com)s>ISBNdb</a>，并下载了<a %(openlibrary)s>Open Library 数据集</a>，但结果并不理想。主要问题是 ISBN 的重叠部分不多。请参见<a %(blog)s>我们的博客文章</a>中的这个维恩图： 我们对ISBNdb和Open Library之间的重叠之少感到非常惊讶，这两个数据库都广泛地包含来自各种来源的数据，例如网络抓取和图书馆记录。如果它们都能很好地找到大多数ISBN，它们的圈子肯定会有大量重叠，或者一个是另一个的子集。这让我们想知道，有多少书籍完全<em>在这些圈子之外</em>？我们需要一个更大的数据库。 那时我们将目光投向了世界上最大的图书数据库：<a %(wikipedia_worldcat)s>WorldCat</a>。这是一个由非营利组织<a %(wikipedia_oclc)s>OCLC</a>拥有的专有数据库，它从世界各地的图书馆聚合metadata记录，以换取让这些图书馆访问完整数据集，并在最终用户的搜索结果中显示。 即使OCLC是一个非营利组织，他们的商业模式也需要保护他们的数据库。好吧，我们很遗憾地说，OCLC的朋友们，我们将全部公开。:-) 在过去的一年里，我们仔细地抓取了所有WorldCat记录。起初，我们碰到了一个幸运的机会。WorldCat刚刚推出了他们完整的网站改版（2022年8月）。这包括对其后端系统的重大改造，引入了许多安全漏洞。我们立即抓住了这个机会，能够在短短几天内抓取数亿条记录！ 在那之后，安全漏洞被一个接一个地慢慢修复，直到我们发现的最后一个漏洞在大约一个月前被修补。到那时，我们几乎拥有了所有记录，只是为了获得稍高质量的记录。所以我们觉得是时候发布了！ 1.3B WorldCat 抓取 <em><strong>简而言之：</strong> 安娜的档案抓取了所有的WorldCat（世界上最大的图书馆metadata集合），以制作需要保存的书籍的待办事项列表。</em> WorldCat 警告：这篇博客文章已被弃用。我们决定IPFS尚未准备好用于主流。我们仍将在可能的情况下从安娜的档案链接到IPFS上的文件，但我们不再自己托管，也不建议其他人使用IPFS进行镜像。如果您想帮助保存我们的收藏，请查看我们的种子页面。 帮助在IPFS上播种Z-Library 合作服务器下载 SciDB 外部借阅 外部借阅（无法打印） 外部下载 查阅元数据 包含在种子中 返回  (+%(num)s 次奖励加速) 未支付 已支付 已取消 已过期 等待安娜确认中 无效的 下面的文字仅以英文继续。 前往 重置 前进 最后 如果你的电子邮件地址无法在 Libgen 论坛上使用，我们推荐使用 <a %(a_mail)s>Proton Mail</a>（免费）。你也可以<a %(a_manual)s>手动申请</a>激活你的账号。 （可能需要<a %(a_browser)s>验证浏览器</a>——无限次下载！） 高速服务器（合作方提供） #%(number)s （推荐） （稍快但需要排队） （无需验证浏览器） （无需浏览器验证或排队等候） （无需排队，但可能非常慢） 低速服务器（合作方提供） #%(number)s 有声书 漫画 小说类图书 非小说类图书 未知类型的图书 期刊文章 杂志 乐谱 其他 标准文档 并非所有页面都能转换为PDF 在 Libgen.li 中被标记为损坏 在 Libgen.li 中不可见 在 Libgen.rs 的小说板块中不可见 在 Libgen.rs 的非虚构文学板块中不可见 运型exiftool处理此文件失败 在Z-Library中标记为“坏文件” 在 Z-Library 中被标记为丢失 在Z-Library中标记为“垃圾邮件” 无法打开文件（如文件损坏或数字版权保护限制） 版权声明 下载问题（例如无法连接、报错、非常慢） 错误的元数据（如标题、描述或封面图片） 其他 质量差（例如格式问题、扫描质量差、缺页） 垃圾内容/文件应被删除（例如广告、侮辱性内容） %(amount)s (%(amount_usd)s) 总共 %(amount)s 总共 %(amount)s (%(amount_usd)s) 有才的书虫 幸运的图书管理员 闪耀的数字收藏家 杰出的档案专家 额外下载 Cerlalc 捷克元数据 读秀 EBSCOhost 电子书索引 Google 图书 Goodreads HathiTrust IA IA 受控数字借阅 ISBNdbs ISBN GRP Libgen.li 排除“scimag” Libgen.rs 非小说类和小说类 Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrarys 俄罗斯国家图书馆 Sci-Hubs 通过 Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor 上传到安娜的档案 Z-Librarys Z-Library中文 SSID、DXID、标题、作者、DOI、ISBN、MD5…… 搜索 作者 描述和元数据中的注释 版本 原文件名 出版者 （搜索特定字段） 标题 出版年份 技术细节 这种币的最低金额比其他的要高。请选择其他订阅时长或换一种支付方式。 无法完成请求。请在几分钟之后重试，如果一直这样，请带上截图联系我们：%(email)s。 发生了未知错误。请通过 %(email)s 联系我们，并附上截图。 支付处理错误。请等待一会然后重试。如果该问题持续了超过 24 小时，请通过 %(email)s 联系我们，并附上截图。 我们正在为<a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">备份</a>世界上最大的影子漫画图书馆筹款。感谢您的支持！<a href="/donate">点此捐助</a>。如果您无法捐助，也请帮忙转告亲友，或关注我们的 <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> 或 <a href="https://t.me/annasarchiveorg">Telegram</a>。 不要给我们发邮件来<a %(a_request)s>请求图书</a><br>或提供小批量（<1万）<a %(a_upload)s>上传</a>。 安娜的档案 数字千年版权法(DCMA)/ 版权声明 关注我们 Reddit 镜像 SLUM (%(unaffiliated)s) 无关联 安娜的档案需要您的帮助！ 如果您现在捐赠，您将获得 <strong>双倍</strong> 的快速下载次数。 许多人试图关停我们，但我们会奋起反击。 如果您在本月捐款，您将获得<strong>双倍</strong>的快速下载次数。 有效期至本月底。 拯救人类知识：一份很棒的节日礼物！ 会员资格将相应延长。 由于托管关闭，合作服务器目前不可用。它们很快会重新上线。 为提高安娜的档案的容灾能力，我们正在招募能运营镜像的志愿者。 我们现在有了一种新的捐款途径： %(method_name)s. 恳请考虑通过 %(donate_link_open_tag)s 向我们捐款 </a> — 运营这个网站花费了很大的资金和心血，而每一笔捐款都对我们十分重要。非常感谢. 向一位朋友推广，你和你的朋友都能多获得 %(percentage)s%% 的高速下载次数！ 给你所爱的人一个惊喜，给他们一个会员帐户。 最佳情人节礼物！ 了解更多…… 账户 活动 高级 安娜的博客 ↗ 安娜的软件 ↗ 测试版 代码浏览器 数据集 捐赠 已下载文件 常问问题 主页 改进元数据 大语言模型数据 登录 / 注册 我的捐赠 公开资料 搜索 安全性 种子 翻译 ↗ 志愿服务与悬赏 近期下载： 📚&nbsp;世界最大开源及开放数据图书馆。 ⭐️&nbsp;不仅是 Sci-Hub、Library Genesis、Z-Library 等站的镜像。 📈&nbsp;%(book_any)s 本图书、%(journal_article)s 篇论文、%(book_comic)s 部漫画、%(magazine)s 期杂志——永久保存。  和  等 读秀 Internet Archive Lending Library LibGen 📚&nbsp;人类历史上最大的完全开放的图书馆。 📈&nbsp;%(book_count)s&nbsp;本图书、%(paper_count)s&nbsp;篇论文被永久保存。 ⭐️&nbsp;我们建立了 %(libraries)s的镜像站。 我们数据抓取并开源了%(scraped)s。 我们所有的代码和数据都是完全开源的。 OpenLib Sci-Hub 、  📚 世界上最大的开源、开放数据图书馆。<br>⭐️ 不仅是 Scihub、Libgen、Zlib 等站的镜像。 Z-Lib 安娜的档案 无效的请求。请访问 %(websites)s。 世界上最大的开源、开放数据图书馆。不仅是 Sci-Hub, Library Genesis, Z-Library 等站的镜像。 在安娜的档案中搜索 安娜的档案 请刷新重试。如果问题几个小时以内还未解决，请<a %(a_contact)s>联系我们</a>。 🔥 加载此页面时出现问题 <li>1. 在<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>或<a href="https://t.me/annasarchiveorg">Telegram</a>上关注我们。</li><li>2. 在推特、Reddit、抖音、Instagram、当地的咖啡馆或图书馆，或任何你去的地方，传播关于安娜档案馆的信息!我们不相信守门人——如果我们被关闭，我们会在其他地方重新出现，因为我们所有的代码和数据都是完全开源的。</li><li>3. 如果你有能力，可以考虑<a href="/donate">捐赠</a>。</li><li>4. 帮助将我们的网站<a href="https://translate.annas-software.org/">翻译</a>成不同的语言。</li><li>5. 如果您是一名软件工程师，请考虑为我们的<a href="https://annas-software.org/">开源</a>做出贡献，或者为我们的<a href="/datasets">种子</a>做种。</li> 10. 创建或帮助维护你使用的语言版本维基百科的《安娜的档案》页面。 11. 我们想要投放精巧的小广告。如果你想在安娜的档案上打广告，请联系我们。 6. 如果你是安全研究人员，利用你的技能，我们既可以进攻也可以防守。请参阅<a %(a_security)s>安全</a>页面。 7. 我们在寻找能处理匿名商户支付方式的专家。能否请你帮我们增加更便捷的捐款方式？PayPal、微信、礼品卡等。如果你听说过这样的人，请联系我们。 8. 我们一直需要更多的服务器资源。 9. 帮助我们的途径还有：直接在本站上反馈文件问题、撰写评论、创建书单；<a %(a_upload)s>上传更多图书</a>、修复文件问题或改善现有图书的排版样式。 有关如何志愿服务的更多详细信息，请参阅我们的<a %(a_volunteering)s>志愿服务与悬赏</a>页面。 我们坚信，信息能自由流通，知识和文化能得以保存。有了这个搜索引擎，我们就站在了巨人的肩膀上。我们敬重各种影子图书馆建立者的辛勤付出，并希望这个搜索引擎能扩大它们的影响力。 要了解我们的最新进展，请关注安娜的<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>或<a href="https://t.me/annasarchiveorg">Telegram</a>。如有问题和反馈，请通过%(email)s联系Anna。 账户 ID: %(account_id)s 退出登录 ❌ 出错了，请刷新页面重试。 ✅ 账号已退出，刷新页面以重新登录。 高速下载使用量（最近24小时）：<strong>%(used)s / %(total)s</strong> 会员：<strong>%(tier_name)s</strong>，%(until_date)s 到期<a %(a_extend)s>（续期）</a> 您可以合并多个会员资格 (每 24 小时的快速下载将被累加在一起)。 会员：<strong>无</strong> <a %(a_become)s>（成为会员）</a> 如欲提升会员级别，请联系安娜：%(email)s。 公开个人资料：%(profile_link)s （不要共享！）：%(secret_key)s 显示 点击加入我们！ 升级到<a %(a_tier)s>更高档次</a>，可以加入我们的群组。 独家 Telegram 群组：%(link)s 账号 下载过什么？ 登录 不要弄丢你的密钥！ 密钥无效。请检查密钥并再次尝试，或者在下方注册一个新账号。 密钥 输入密钥以登录： 旧的电子邮件账户？在这里输入您的<a %(a_open)s>电子邮件地址</a>。 注册新账号 还没有账号？ 注册成功！您的密钥为：<span %(span_key)s>%(key)s</span> 请小心保存此密钥。如果您丢失了它，您将失去访问您的帐户的权限。 <li %(li_item)s><strong>加入书签。</strong>你可以在浏览器中收藏此页面以保存你的密钥。</li><li %(li_item)s><strong>下载。</strong> 点击<a %(a_download)s>此链接</a>下载你的密钥。</li><li %(li_item)s><strong>密码管理器。</strong>为方便起见，下面已经填充了你的密钥。这样当你登录时，你可以在密码管理器中保存密钥。</li> 登录 / 注册 浏览器验证 警告：代码中包含不正确的Unicode字符，可能在各种情况下表现不正确。可以从URL中的base64表示中解码原始二进制文件。 描述 标签 前缀 特定代码的URL 网站 以“%(prefix_label)s”开头的代码 请不要爬取这些页面。我们建议<a %(a_import)s>生成</a>或<a %(a_download)s>下载</a>我们的ElasticSearch和MariaDB数据库，并运行我们的<a %(a_software)s>开源代码</a>。原始数据可以通过<a %(a_json_file)s>这个</a>等JSON文件手动检索。 少于%(count)s条记录 通用URL 代码浏览器 索引 通过前缀探索记录标记的代码。“记录”列显示在搜索引擎中标记有给定前缀代码的记录数量（包括仅元数据记录）。“代码”列显示具有给定前缀的实际代码数量。 已知代码前缀“%(key)s” 更多… 前缀 %(count)s条记录匹配“%(prefix_label)s” 代码 记录 “%%s”将被替换为代码的值 搜索安娜的档案 代码 特定代码的URL：“%(url)s” 此页面生成可能需要一些时间，因此需要通过Cloudflare验证码。<a %(a_donate)s>会员</a>可以跳过验证码。 已举报违规行为： 更好的版本 您是否要举报此用户的滥用或不当行为？ 文件问题：%(file_issue)s 隐藏评论 回复 举报滥用 您已举报此用户的违规行为。 发送到此邮箱的版权诉求将被忽略，请改用表单提交。 显示邮箱 我们非常欢迎你的反馈和提问！ 然而，由于我们收到了大量垃圾和无意义邮件，请选中勾选框，表示你已经理解了联系我们的条件。 通过任何其他方式联系我们的版权投诉将被自动删除。 DMCA/版权投诉，请使用<a %(a_copyright)s>这份表单</a>。 联系邮箱 安娜的档案上的 URL（必填）。每行一个。请仅包含描述同一版本书籍的 URL。如果您想对多本书或多个版本提出声明，请多次提交此表格。 将多本书籍或多个版本绑定在一起的请求将被拒绝。 地址（必填） 对源材料的清晰描述（必填） 电子邮件（必填） 源材料的 URL，每行一个（必填）。请尽可能多地包含，以帮助我们验证您的声明（例如，亚马逊，WorldCat，Google Books，DOI）。 如果适用，请提供源材料的ISBN编号。每行一个。请仅包括与您报告版权声明的版本完全匹配的ISBN编号。 您的姓名（必填） ❌ 出了点问题。请重新加载页面并重试。 ✅ 感谢您提交版权声明。我们会尽快审核。请重新加载页面以提交另一个声明。 请提供来源材料的<a%(a_openlib)s>Open Library</a>链接，每行一个。请花些时间在Open Library中搜索您的来源材料。这将有助于我们验证您的声明。 电话号码（必填） 声明和签名（必填） 提交声明 如果您有数字千年版权法或其他版权声明，请尽可能准确地填写此表格。如果遇到任何问题，请通过我们专门的数字千年版权法地址：%(email)s 联系我们。请注意，发送到此地址的声明将不予处理，仅用于问题咨询。请使用下面的表格提交您的声明。 数字千年版权法 / 版权声明表 安娜的档案上的示例记录 安娜的档案的种子文件 安娜的档案容器格式 导入元数据的脚本 如果您有兴趣镜像此数据集以用于<a %(a_archival)s>存档</a>或<a %(a_llm)s>大语言模型训练</a>目的，请联系我们。 最后更新：%(date)s 主%(source)s网站 元数据文档（大多数字段） 安娜的档案镜像的文件：%(count)s（%(percent)s%%） 资源 文件总数：%(count)s 文件总大小：%(size)s 我们关于此数据的博客文章 <a %(duxiu_link)s>读秀</a>是一个庞大的扫描书籍数据库，由<a %(superstar_link)s>超星数字图书馆集团</a>创建。大多数是学术书籍，扫描后以数字形式提供给大学和图书馆。对于我们的英语读者，<a %(princeton_link)s>普林斯顿大学</a>和<a %(uw_link)s>华盛顿大学</a>有很好的概述。还有一篇优秀的文章提供了更多背景信息：<a %(article_link)s>“数字化中国书籍：超星读秀学者搜索引擎案例研究”</a>。 读秀的书籍长期以来在中国互联网上被盗版。通常它们被转售商以不到一美元的价格出售。它们通常通过中国版的Google Drive分发，这些平台经常被黑客攻击以增加存储空间。一些技术细节可以在<a %(link1)s>这里</a>和<a %(link2)s>这里</a>找到。 尽管这些书籍已经半公开分发，但要批量获取它们还是相当困难的。我们将此列为待办事项的高优先级，并分配了数月的全职工作时间。然而，在2023年末，一位令人难以置信、惊人且才华横溢的志愿者联系了我们，告诉我们他们已经完成了所有这些工作——并为此付出了巨大的代价。他们与我们分享了完整的收藏，不求任何回报，只希望能长期保存。真是令人钦佩。 来自我们志愿者的更多信息（原始笔记）： 改编自我们的 <a %(a_href)s>博客文章</a>。 读秀 %(count)s 文件 此数据集与<a %(a_datasets_openlib)s>Open Library 数据集</a>密切相关。它包含所有元数据的抓取以及来自 IA 的受控数字借阅图书馆的大部分文件。更新会以<a %(a_aac)s>Anna’s Archive 容器格式</a>发布。 这些记录直接引用自 Open Library 数据集，但也包含 Open Library 中没有的记录。我们还拥有多年来由社区成员抓取的许多数据文件。 该收藏由两部分组成。您需要两部分才能获取所有数据（除了在种子页面上被划掉的已被取代的种子）。 数字借阅图书馆 我们首次发布的版本，在我们标准化<a %(a_aac)s>Anna’s Archive Containers (AAC)格式</a>之前。包含元数据（以json和xml格式）、pdf文件（来自acsm和lcpdf数字借阅系统）以及封面缩略图。 增量新版本，使用AAC格式。仅包含时间戳在2023-01-01之后的元数据，因为其余部分已由“ia”覆盖。此外，还包括所有pdf文件，这次来自acsm和“bookreader”（IA的网页阅读器）借阅系统。尽管名称不完全正确，我们仍将bookreader文件填充到ia2_acsmpdf_files集合中，因为它们是互斥的。 IA受控数字借阅 98%%+的文件是可搜索的。 我们的使命是存档世界上所有的书籍（以及论文、杂志等），并使它们触手可及。我们相信所有的书籍都应该被广泛镜像，以确保冗余和弹性。这就是为什么我们从各种来源汇集文件。一些来源是完全开放的，可以批量镜像（例如Sci-Hub）。其他来源是封闭和保护的，所以我们尝试爬取它们以“解放”它们的书籍。还有一些则介于两者之间。 我们所有的数据都可以<a %(a_torrents)s>通过种子下载</a>，所有的元数据都可以<a %(a_anna_software)s>生成</a>或<a %(a_elasticsearch)s>下载</a>为ElasticSearch和MariaDB数据库。原始数据可以通过<a %(a_dbrecord)s>此</a>类JSON文件手动浏览。 元数据 ISBN网站 最后更新： %(isbn_country_date)s (%(link)s) 资源 国际ISBN机构定期发布分配给国家ISBN机构的范围。由此我们可以推断出该ISBN属于哪个国家、地区或语言组。我们目前间接使用这些数据，通过<a %(a_isbnlib)s>isbnlib</a> Python库。 ISBN国家信息 这是2022年9月期间对isbndb.com进行大量调用的转储。我们尝试覆盖所有的ISBN范围。这大约是3090万条记录。在他们的网站上，他们声称实际上有3260万条记录，所以我们可能遗漏了一些，或者<em>他们</em>可能做错了什么。 JSON响应几乎是直接从他们的服务器获取的。我们注意到的一个数据质量问题是，对于以不同前缀而不是“978-”开头的ISBN-13号码，他们仍然包含一个“isbn”字段，该字段只是将ISBN-13号码的前三个数字去掉（并重新计算校验位）。这显然是错误的，但这是他们的做法，所以我们没有更改它。 您可能会遇到的另一个潜在问题是，“isbn13”字段有重复项，因此您不能将其用作数据库中的主键。“isbn13”+“isbn”字段组合似乎是唯一的。 发布版本 1 (2022-10-31) 小说种子下载进度落后（尽管ID ~4-6M未通过种子下载，因为它们与我们的Zlib种子重叠）。 关于漫画书发布的博客文章 安娜的档案上的漫画种子 关于不同Library Genesis分支的背景故事，请参阅<a %(a_libgen_rs)s>Libgen.rs</a>页面。 Libgen.li包含与Libgen.rs大部分相同的内容和元数据，但在此基础上增加了一些集合，即漫画、杂志和标准文档。它还将<a %(a_scihub)s>Sci-Hub</a>集成到其元数据和搜索引擎中，这是我们用于数据库的内容。 该图书馆的元数据可在<a %(a_libgen_li)s>libgen.li</a>免费获取。然而，该服务器速度较慢且不支持断点续传。相同的文件也可以在<a %(a_ftp)s>FTP服务器</a>上获取，效果更好。 非虚构类书籍似乎也有所分化，但没有新的种子。这似乎是自2022年初以来发生的，尽管我们尚未验证这一点。 根据Libgen.li管理员的说法，“fiction_rus”（俄罗斯小说）收藏应该由<a %(a_booktracker)s>booktracker.org</a>定期发布的种子覆盖，尤其是<a %(a_flibusta)s>flibusta</a>和<a %(a_librusec)s>lib.rus.ec</a>的种子（我们在<a %(a_torrents)s>这里</a>镜像，尽管我们尚未确定哪些种子对应哪些文件）。 小说收藏有其自己的种子（与<a %(a_href)s>Libgen.rs</a>不同），从%(start)s开始。 某些没有种子的范围（例如小说范围f_3463000到f_4260000）可能是Z-Library（或其他重复）文件，尽管我们可能需要进行一些去重，并为这些范围内的lgli独特文件制作种子。 所有收藏的统计数据可以在<a %(a_href)s>libgen的网站</a>上找到。 大多数附加内容都有种子可用，尤其是与安娜档案合作发布的漫画、杂志和标准文档的种子。 请注意，指向“libgen.is”的种子文件明确是<a %(a_libgen)s>Libgen.rs</a>的镜像（“.is”是Libgen.rs使用的不同域名）。 使用元数据的一个有用资源是<a %(a_href)s>此页面</a>。 %(icon)s 他们的“fiction_rus”收藏（俄罗斯小说）没有专门的种子，但由其他人的种子覆盖，我们保留一个<a %(fiction_rus)s>镜像</a>。 安娜的档案上的俄罗斯小说种子 安娜的档案上的小说种子 讨论论坛 元数据 通过FTP获取元数据 安娜的档案上的杂志种子 元数据字段信息 其他种子的镜像（以及独特的小说和漫画种子） 安娜档案中的标准文档种子 Libgen.li 安娜的档案的种子（书籍封面） Library Genesis以通过种子批量慷慨地提供其数据而闻名。我们的Libgen收藏由他们不直接发布的辅助数据组成，并与他们合作。非常感谢所有参与Library Genesis的人与我们合作！ 关于书籍封面发布的博客文章 此页面是关于“.rs”版本的。它以一致发布其元数据和完整书籍目录内容而闻名。其书籍收藏分为小说和非小说部分。 使用元数据的一个有用资源是<a %(a_metadata)s>此页面</a>（在屏蔽IP段可能需要使用VPN访问）。 截至2024-03，新种子正在<a %(a_href)s>此论坛帖子</a>中发布（在屏蔽IP段可能需要使用VPN访问）。 安娜的档案上的小说种子 Libgen.rs小说种子 Libgen.rs讨论论坛 Libgen.rs元数据 Libgen.rs元数据字段信息 Libgen.rs非小说种子 安娜的档案上的非小说种子 %(example)s 对于小说书籍。 这个<a %(blog_post)s>首次发布</a>的内容相当小：大约300GB的Libgen.rs分支的书籍封面，包括小说和非小说。它们的组织方式与在libgen.rs上的显示方式相同，例如： %(example)s 对于非小说书籍。 就像Z-Library的收藏一样，我们将它们全部放在一个大的.tar文件中，如果您想直接提供文件服务，可以使用<a %(a_ratarmount)s>ratarmount</a>挂载。 发布版本 1 (%(date)s) 关于不同的Library Genesis（或称“Libgen”）分支的简短故事是，随着时间的推移，参与Library Genesis的不同人员发生了分歧，并各自分道扬镳。 根据这个<a %(a_mhut)s>论坛帖子</a>，Libgen.li 最初托管于“http://free-books.dontexist.com”。 “.fun”版本由原始创始人创建。它正在被改版为一个新的、更分布式的版本。 <a %(a_li)s>“.li”版本</a>拥有大量的漫画收藏，以及其他内容，这些内容（尚未）通过种子批量下载。它确实有一个单独的小说书籍种子集合，并且其数据库中包含<a %(a_scihub)s>Sci-Hub</a>的元数据。 “.rs”版本的数据非常相似，并且最常通过批量种子发布其收藏。它大致分为“小说”和“非小说”部分。 最初来源于“http://gen.lib.rus.ec”。 <a %(a_zlib)s>Z-Library</a>在某种意义上也是Library Genesis的一个分支，尽管他们使用了不同的项目名称。 Libgen.rs 我们还通过仅元数据来源丰富我们的馆藏，我们可以使用ISBN号码或其他字段将其匹配到文件。以下是这些来源的概述。同样，其中一些来源是完全开放的，而对于其他来源，我们必须抓取它们。 请注意，在元数据搜索中，我们显示的是原始记录。我们没有进行记录的合并。 仅元数据来源 Open Library是Internet Archive的一个开源项目，旨在为世界上的每本书编目。它拥有世界上最大的书籍扫描操作之一，并且有许多书籍可供电子借阅。其书籍元数据目录可免费下载，并包含在安娜的档案中（尽管目前不在搜索中，除非您明确搜索Open Library ID）。 Open Library 排除重复项 最近更新 文件数量的百分比 %%由安娜的档案镜像/可用的种子 大小 来源 以下是安娜的档案上文件来源的概览。 由于影子图书馆经常相互同步数据，图书馆之间有相当大的重叠。这就是为什么这些数字加起来并不等于总数。 “由安娜的档案镜像和做种”的百分比显示了我们自己镜像的文件数量。我们通过种子批量做种这些文件，并通过合作网站提供直接下载。 概述 总计 安娜的档案上的种子 有关Sci-Hub的背景情况，请参阅其<a %(a_scihub)s>官方网站</a>、<a %(a_wikipedia)s>维基百科页面</a>和这个<a %(a_radiolab)s>播客采访</a>。 请注意，Sci-Hub自<a %(a_reddit)s>2021年起被冻结</a>。它之前也被冻结过，但在2021年增加了几百万篇论文。不过，Libgen的“scimag”收藏中仍然会添加一些有限数量的论文，但不足以保证新的批量种子。 我们使用由<a %(a_libgen_li)s>Libgen.li</a>在其“scimag”收藏中提供的Sci-Hub元数据。我们还使用了<a %(a_dois)s>dois-2022-02-12.7z</a>数据集。 请注意，“smarch”种子已被<a %(a_smarch)s>弃用</a>，因此未被包含在我们的种子列表中。 Libgen.li上的种子 Libgen.rs上的种子 元数据和种子 Reddit上的更新 播客采访 维基百科页面 Sci-Hub Sci-Hub：自2021年以来被冻结；大部分可通过种子下载 Libgen.li：自那时以来有少量新增</div> 一些源图书馆通过种子文件推广其数据的批量共享，而其他图书馆则不轻易分享其收藏。在后者的情况下，安娜的档案会尝试爬取他们的收藏，并使其可用（请参阅我们的<a %(a_torrents)s>种子文件</a>页面）。也有介于两者之间的情况，例如，源图书馆愿意分享，但没有资源这样做。在这些情况下，我们也会尝试提供帮助。 以下是我们与不同源图书馆接口的概述。 源图书馆 %(icon)s 中国互联网上散布着很多文件数据库；但通常是付费的 %(icon)s 大多数文件只能通过高级百度云账户访问；下载速度慢。 %(icon)s 安娜的档案管理着一份 <a %(duxiu)s>读秀文件</a> 的收藏 %(icon)s 散布在中国互联网上散布着各种元数据库，但通常是付费的 %(icon)s 没有可轻松访问的元数据转储可用于其整个收藏。 %(icon)s 安娜的档案管理着一份 <a %(duxiu)s>读秀元数据</a> 的收藏 文件 %(icon)s 文件仅可在有限的条件下借阅，且有各种访问限制 %(icon)s 安娜的档案管理着一系列<a %(ia)s>IA文件</a> %(icon)s 一些元数据可通过<a %(openlib)s>Open Library数据库转储</a>获取，但这些并不涵盖整个IA收藏 %(icon)s 没无法轻松获取其整个集合的元数据转储 %(icon)s 安娜的档案管理着一系列<a %(ia)s>IA元数据</a> 最后更新 %(icon)s 安娜的档案和Libgen.li合作管理<a %(comics)s>漫画书</a>、<a %(magazines)s>杂志</a>、<a %(standarts)s>标准文档</a>和<a %(fiction)s>小说（从Libgen.rs分离）</a>的收藏。 %(icon)s 非小说类种子与Libgen.rs共享（并在<a %(libgenli)s>这里</a>镜像）。 %(icon)s 每季度<a %(dbdumps)s>HTTP数据库转储</a> %(icon)s <a %(nonfiction)s>非小说类</a>和<a %(fiction)s>小说类</a>的自动化种子 %(icon)s 安娜的档案管理着一系列<a %(covers)s>书籍封面种子</a> %(icon)s 每日<a %(dbdumps)s>HTTP数据库转储</a> 元数据 %(icon)s 每月 <a %(dbdumps)s>数据库转储</a> %(icon)s 数据种子可在<a %(scihub1)s>这里</a>、<a %(scihub2)s>这里</a>和<a %(libgenli)s>这里</a>获取 %(icon)s 一些新文件 <a %(libgenrs)s>正在</a><a %(libgenli)s>被添加</a>到Libgen的“scimag”，但不足以生成新的种子 自2021年以来，%(icon)sSci-Hub已停止获取新文件。 %(icon)s 元数据转储可在<a %(scihub1)s>这里</a>和<a %(scihub2)s>这里</a>获取，也可作为<a %(libgenli)s>Libgen.li数据库</a>的一部分（我们使用的） 来源 %(icon)s 对各种较小或一次性的来源。我们鼓励人们先上传到其他影子图书馆，但有时人们的收藏太大，其他人无法整理，但又不足以单独分类。 %(icon)s 无法直接批量获取，防止抓取 %(icon)s 安娜的档案管理着一份 <a %(worldcat)s>OCLC (WorldCat) 元数据</a> 的收藏 %(icon)s 安娜的档案 和 Z-Library 共同管理<a %(metadata)s>Z-Library元数据</a>和<a %(files)s>Z-Library文件</a>的收藏 数据集 我们将上述所有来源合并到一个统一的数据库中，用于服务此网站。这个统一的数据库不能直接获取，但由于安娜的档案是完全开源的，可以相对容易地<a %(a_generated)s>生成</a>或<a %(a_downloaded)s>下载</a>为ElasticSearch和MariaDB数据库。该页面上的脚本将自动从上述提到的来源下载所有必要的元数据。 如果您想在本地运行这些脚本之前浏览我们的数据，可以查看我们的JSON文件，这些文件进一步链接到其他JSON文件。<a %(a_json)s>这个文件</a>是一个很好的起点。 统一数据库 安娜的档案提供的种子文件 浏览 搜索 各种较小或一次性的来源。我们鼓励人们先上传到其他影子图书馆，但有时人们的收藏太大，其他人无法整理，但又不足以单独分类。 从<a %(a1)s>Datasets页面</a>概览。 来自 <a %(a_href)s>aaaaarg.fail</a>。看起来相当完整。来自我们的志愿者“cgiym”。 来自 <a %(a_href)s><q>ACM Digital Library 2020</q></a> 的种子。与现有的论文集合有相当高的重叠，但很少有MD5匹配，因此我们决定完全保留它。 对<q>iRead eBooks</q>（发音为<q>ai rit i-books</q>；airitibooks.com）的抓取，由志愿者<q>j</q>完成。对应于<a %(a1)s><q>其他metadata抓取</q></a>中的<q>airitibooks</q>metadata。 来自<a %(a1)s><q>亚历山大图书馆</q></a>的收藏。部分来自原始来源，部分来自the-eye.eu，部分来自其他镜像。 来自一个私人图书种子网站 <a %(a_href)s>Bibliotik</a>（通常称为“Bib”），这些书籍按名称（A.torrent, B.torrent）打包成种子并通过the-eye.eu分发。 来自我们的志愿者“bpb9v”。有关 <a %(a_href)s>CADAL</a> 的更多信息，请参阅我们 <a %(a_duxiu)s>读秀数据集页面</a> 中的注释。 更多来自我们的志愿者“bpb9v”，主要是读秀文件，以及一个名为“WenQu”和“SuperStar_Journals”的文件夹（超星是读秀背后的公司）。 来自我们的志愿者“cgiym”，来自各种来源的中文文本（表示为子目录），包括来自 <a %(a_href)s>中国机械工业出版社</a>（一家主要的中国出版商）。 来自我们的志愿者“cgiym”的非中文集合（表示为子目录）。 关于中国建筑的书籍抓取，由志愿者<q>cm</q>完成：<q>我通过利用出版社的网络漏洞获取了这些书籍，但该漏洞现已被修复</q>。对应于<a %(a1)s><q>其他metadata抓取</q></a>中的<q>chinese_architecture</q>metadata。 从几个大型种子中收集的学术出版社 <a %(a_href)s>De Gruyter</a> 的书籍。 抓取自 <a %(a_href)s>docer.pl</a>，一个专注于书籍和其他书面作品的波兰文件共享网站。由志愿者“p”在2023年末抓取。我们没有从原网站获得好的元数据（甚至没有文件扩展名），但我们过滤了类似书籍的文件，并且通常能够从文件本身提取元数据。 由志愿者“w”收集的读秀的epub文件。读秀仅提供最近几年书籍的电子版，因此这个集合中的大部分书都是很新的。 来自志愿者“m”的剩余读秀文件，这些文件不是读秀专有的PDG格式（主要的 <a %(a_href)s>读秀数据集</a>）。从许多原始来源收集，可惜没有在文件路径中保留这些来源。 <span></span> <span></span> <span></span> 对情色书籍的抓取，由志愿者<q>do no harm</q>完成。对应于<a %(a1)s><q>其他metadata抓取</q></a>中的<q>hentai</q>metadata。 <span></span> <span></span> 由志愿者“t”从一家日本漫画出版社抓取的集合。 由志愿者“c”提供的<a %(a_href)s>龙泉司法档案精选</a>。 抓取自 <a %(a_href)s>magzdb.org</a>，Library Genesis的盟友（它链接在libgen.rs主页上），但他们不想直接提供他们的文件。由志愿者“p”在2023年末获得。 <span></span> 各种小型上传，太小而无法作为自己的子集合，但表示为目录。 来自AvaxHome的电子书，这是一个俄罗斯的文件共享网站。 报纸和杂志的存档。对应于<a %(a1)s><q>其他metadata抓取</q></a>中的<q>newsarch_magz</q>metadata。 对<a %(a1)s>哲学文献中心</a>的抓取。 志愿者“o”的集合，他直接从原始发布（“scene”）网站收集波兰书籍。 由志愿者“cgiym”和“woz9ts”收集的 <a %(a_href)s>shuge.org</a> 的合并集合。 <span></span> <a %(a_href)s>“川陀帝国图书馆”</a>（以虚构的图书馆命名），由志愿者“t”在2022年抓取。 <span></span> <span></span> <span></span> 志愿者“woz9ts”的子子集合（表示为目录）：<a %(a_program_think)s>program-think</a>，<a %(a_haodoo)s>haodoo</a>，<a %(a_skqs)s>skqs</a>（由中国台北的 <a %(a_sikuquanshu)s>迪志</a>），mebook（mebook.cc，我的小书屋——woz9ts：“这个网站主要分享高质量的电子书文件，其中一些是由站长自己排版的。站长在2019年<a %(a_arrested)s>被捕</a>，有人收集了他分享的文件。”）。 志愿者“woz9ts”提供的剩余读秀文件，这些文件不是读秀专有的PDG格式（仍需转换为PDF）。 “上传”收藏被分成较小的子收藏，这些子收藏在 AACIDs 和种子名称中有所标示。所有子收藏首先与主收藏进行去重，尽管元数据“upload_records”JSON 文件仍包含许多对原始文件的引用。大多数子收藏中也删除了非书籍文件，通常在“upload_records”JSON 中未注明 <em>not</em>。 子收藏包括： 备注 子合集 许多子收藏本身由子子收藏组成（例如来自不同的原始来源），这些子子收藏在“filepath”字段中表示为目录。 上传到安娜的档案 关于这些数据的博客文章 <a %(a_worldcat)s>WorldCat</a> 是一个由非营利组织 <a %(a_oclc)s>OCLC</a> 创建的专有数据库，汇集了来自世界各地图书馆的元数据记录。它可能是世界上最大的图书馆元数据集合。 2023年10月，我们以<a %(a_aac)s>安娜的档案的容器格式</a><a %(a_scrape)s>发布</a>了对OCLC（WorldCat）数据库的全面抓取。 2023年10月，初始发布： OCLC (WorldCat) 安娜的档案提供的种子文件 安娜的档案上的示例记录（原始收藏） 安娜的档案上的示例记录（“zlib3”收藏） 安娜的档案提供的种子文件（元数据+内容） 关于发布版本1的博客文章 关于发布版本2的博客文章 在2022年底，Z-Library的涉嫌创始人被捕，且域名被美国当局查封。但之后该网站以另外的方式被未知运营者重新上线。 截至2023年2月的更新。 Z-Library起源于<a %(a_href)s>Library Genesis</a>社区，最初是用他们的数据启动的。从那时起，它已经大大专业化，并拥有了更现代的界面。因此，他们能够获得更多的捐款，无论是金钱上的捐款以继续改进他们的网站，还是新书的捐赠。除了Library Genesis之外，他们还积累了大量的收藏。 该收藏由三部分组成。前两部分的原始描述页面保留在下面。您需要所有三部分才能获取所有数据（除了在种子页面上被划掉的已被取代的种子）。 %(title)s：我们的首次发布。这是当时被称为“海盗图书馆镜像”（“pilimi”）的首次发布。 %(title)s：第二次发布，这次所有文件都包装在.tar文件中。 %(title)s：增量新发布，使用<a %(a_href)s>Anna’s Archive容器（AAC）格式</a>，现在与Z-Library团队合作发布。 最初的镜像是在2021年和2022年期间辛苦获得的。目前它有些过时：它反映了2021年6月的收藏状态。我们将在未来更新这一点。现在我们专注于发布这个首次发布。 由于Library Genesis已经通过公共种子保存，并且包含在Z-Library中，我们在2022年6月对Library Genesis进行了基本的重复数据删除。为此，我们使用了MD5哈希值。图书馆中可能还有很多重复内容，例如同一本书的多种文件格式。这很难准确检测，所以我们没有进行检测。重复数据删除后，我们剩下了超过200万个文件，总计不到7TB。 该收藏由两部分组成：一个MySQL “.sql.gz”格式的元数据转储，以及72个每个约50-100GB的种子文件。元数据包含Z-Library网站报告的数据（标题、作者、描述、文件类型），以及我们观察到的实际文件大小和md5sum，因为有时这些数据不一致。似乎有一些文件范围Z-Library本身的元数据是错误的。在某些孤立的情况下，我们可能也下载了错误的文件，我们将在未来尝试检测并修复这些问题。 大型种子文件包含实际的书籍数据，文件名为Z-Library ID。文件扩展名可以使用元数据转储重建。 该收藏是非小说和小说内容的混合（不像Library Genesis那样分开）。质量也参差不齐。 这个首发版本现在已经完全可用。请注意，种子文件仅通过我们的Tor镜像提供。 版本1（%(date)s） 这是一个额外的单个种子文件。它不包含任何新信息，但其中包含一些计算起来需要时间的数据。因此，拥有它很方便，因为下载这个种子通常比从头计算要快。特别是，它包含用于<a %(a_href)s>ratarmount</a>的tar文件的SQLite索引。 版本2附录（%(date)s） 我们获取了自上次镜像以来到2022年8月添加到Z-Library的所有书籍。我们还回溯并抓取了一些第一次错过的书籍。总的来说，这个新收藏大约有24TB。同样，这个收藏也进行了与Library Genesis的重复数据删除，因为该收藏已经有可用的种子。 数据的组织方式与首发版本类似。有一个MySQL “.sql.gz”格式的元数据转储，其中还包括首发版本的所有元数据，从而取代了它。我们还添加了一些新列： 我们上次提到过，但为了澄清：“filename”和“md5”是文件的实际属性，而“filename_reported”和“md5_reported”是我们从Z-Library抓取的内容。有时这两者不一致，所以我们都包括了。 对于这个版本，我们将排序规则更改为“utf8mb4_unicode_ci”，这应该与旧版本的MySQL兼容。 数据文件与上次类似，但它们大得多。我们实在不愿意创建大量较小的种子文件。“pilimi-zlib2-0-14679999-extra.torrent”包含了我们在上次发布中错过的所有文件，而其他种子都是新的ID范围。  <strong>更新 %(date)s：</strong> 我们制作的大多数种子文件太大，导致种子客户端难以处理。我们已将其删除并发布了新的种子。 <strong>更新 %(date)s：</strong> 仍然存在很多零散文件，所以我们将它们打包成tar文件并再次发布了新的种子。 %(key)s：此文件是否已在Library Genesis中，无论是非小说还是小说收藏（通过md5匹配）。 %(key)s：此文件在哪个种子中。 %(key)s：当我们无法下载书籍时设置。 版本2（%(date)s） Zlib发布（原始描述页面） Tor域名 主网站 Z-Library抓取 Z-Library中的“中文”收藏似乎与我们的读秀收藏相同，但MD5不同。我们从种子中排除这些文件以避免重复，但仍在我们的搜索索引中显示它们。 元数据 你获得了%(percentage)s%%奖励加速，因为你点击了%(profile_link)s的邀请链接。 这在整个会员期限内都有效。 捐赠 加入 已选中 最高折扣 %(percentage)s%% 支付宝支持国际信用卡/借记卡。有关更多信息，请参阅<a %(a_alipay)s>此指南</a>。 使用信用卡/借记卡给我们发送 Amazon.com 礼品卡。 你可以使用信用卡/借记卡购买加密货币。 微信 (WeChat) 支付支持国际信用卡/借记卡。在微信程序中，转到“我 => 服务 => 钱包 => 新增卡片”。如果你找不到，请通过“我 => 设置 => 通用 => 辅助工具 => 微信支付 => 启用”打开。 （使用Coinbase发送以太坊时使用） 已复制！ 复制 （最低金额） （警告：最低金额较高） -%(percentage)s%% 12 个月 1 个月 24 个月 3 个月 48个月 6 个月 96 个月 选择您的订阅时长。 <div %(div_monthly_cost)s></div><div %(div_after)s>折扣 <span %(span_discount)s></span></div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 个月 1 个月 24 个月 3 个月 48个月 6 个月 96个月 %(monthly_cost)s / 月 联系我们 直连 <strong>SFTP</strong> 服务器 企业级捐赠或交换新书集（例如：新扫描版、有 OCR 的数据集）。 专用访问方式 <strong>不限量</strong>高速访问 <div %(div_question)s>我可以升级会员或获得多个会员吗？</div> <div %(div_question)s>我可以只捐款而不成为会员吗？</div> 当然可以。 我们接受任何金额的捐款，Monero (XMR) 地址为：%(address)s。 <div %(div_question)s>每月范围是什么意思？</div> 您可以通过应用所有折扣（例如选择超过一个月的期限）来达到范围的下限。 <div %(div_question)s>会员资格会自动续期吗？</div>会员<strong>不会</strong>自动续期。入会时间长短由你决定。 <div %(div_question)s>你们将捐赠用在什么地方？</div> 100%%将用来保存世界的知识和文化，使之触手可及。目前我们主要把钱花在服务器、存储和带宽上。没有任何资金会流向任何团队成员个人。 <div %(div_question)s>我可以进行大额的捐款吗？</div>这太酷了！对于数千美元及以上的捐款，请直接通过%(email)s联系我们。 <div %(div_question)s>你们还有其他支付方式吗？</div>目前还没有。许多人不希望这样的档案馆能够存在，所以我们必须保持谨慎。如果您可以帮助我们安全地建立别的（更便利的）支付方式，请通过 %(email)s 联系我们。 捐赠常见问题 你目前还有一个<a %(a_donation)s>正在进行的捐赠</a>。在捐赠新的一笔之前，请先完成或取消那笔捐赠。 <a %(a_all_donations)s>查看我的全部捐赠</a> 超过$5000的捐赠请直接通过%(email)s联系我们。 我们欢迎富裕的个人或机构提供大额捐赠。  请注意，虽然此页面上的会员资格是“按月”计算，但它们是一次性捐赠（不会自动续费）。请参阅<a %(faq)s>捐赠常见问题解答</a>。 安娜的档案是一个非盈利的、开源的、公开数据的项目。通过捐赠和成为会员，您可以支持我们的运营和发展。致我们所有的会员：感谢你们让我们继续前进！ ❤️ 欲了解更多信息，请查看<a %(a_donate)s>捐赠常见问题解答</a>。 加入会员，请<a %(a_login)s>登录或注册</a>。感谢您的支持！ $%(cost)s / 月 如果你在付款时出现了操作错误，我们没有办法退款，但是会尽量补救。 在您的PayPal应用程序或网站中找到“加密货币”(Crypto) 页面。通常是在“财务”选项的下面。 去你的PayPal应用程序或网站上的“比特币”页面。按下“转账”按钮 %(transfer_icon)s，然后点“发送”。 支付宝 支付宝 / 微信 亚马逊礼品卡 %(amazon)s 礼品卡 银行卡 银行卡（使用app） 币安 信用卡/借记卡/Apple/Google（BMC） Cash Apps 信用卡/借记卡 信用卡/借记卡 2 信用卡/借记卡（备用） 加密货币 %(bitcoin_icon)s 银行卡 / PayPal / Venmo PayPal（美国） %(bitcoin_icon)s PayPal PayPal（常规） Pix（巴西） Revolut （暂不可用） 微信 选择您首选的加密货币： 使用亚马逊礼品卡捐赠。 <strong>重要：</strong> 此选项适用于%(amazon)s。如果您想使用其他亚马逊网站，请在上方选择。 <strong>重要提示：</strong> 我们仅支持 Amazon.com，其他亚马逊网站均不支持。例如，.de、.co.uk、.ca（德国、英国、加拿大分站）均不支持。 请【不要】编写你自己的信息。 输入准确金额：%(amount)s 注意，我们需要将金额取整至经销商可接受的数字（最小 %(minimum)s）。 通过支付宝使用信用卡/借记卡捐赠（设置超级简单）。 从<a %(a_app_store)s>苹果应用商店</a>或<a %(a_play_store)s>谷歌应用商店</a>安装支付宝。 使用您的手机号码注册。 无需提供其他个人信息。 <span %(style)s>1</span>安装支付宝 支持：Visa、MasterCard、JCB、Diners Club 和 Discover。 有关更多信息，请参阅<a %(a_alipay)s>此指南</a>。 <span %(style)s>2</span>添加银行卡 使用币安，您可以使用信用卡/借记卡或银行账户购买比特币，然后将比特币捐赠给我们。通过这种方式，我们在接受您的捐赠时可以保持安全和匿名。 币安支持大多数银行及信用卡/借记卡，几乎在任何国家都能使用。这是我们目前最推荐的捐赠方式。感谢您花时间学习如何用这种方式捐赠，我们由衷感谢您的帮助。 对于信用卡、借记卡、Apple Pay 和 Google Pay，我们使用“Buy Me a Coffee”服务系统（BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>）。在此系统中，一杯“咖啡”折合 5 美元，因此您的捐款将四舍五入到最接近的 5 美元 的整数倍。 使用 Cash App 捐赠。 如果你有 Cash App 账号，这是最方便的捐赠方式了！ 注意，对金额少于 %(amount)s 的交易，Cash App 会收取 %(fee)s 的手续费。若金额不少于 %(amount)s，就是免费的！ 使用信用卡或借记卡捐赠。 这种方法使用加密货币提供商作为媒介。这种方式操作可能比较复杂，因此请仅在其他支付方法无效时使用此方法。而且，这种方法并不适用于所有国家。 我们无法直接支持信用卡/借记卡，因为银行不愿意与我们合作。☹ 但是，仍有几种方法可以通过其他支付方式使用信用卡/借记卡： 您可以使用加密货币 BTC, ETH, XMR 和 SOL 进行捐赠。如果您熟悉加密货币，请使用此选项。 你可以捐赠 BTC、ETH、XMR 等加密货币。 加密货币快速服务 如果您是第一次使用加密货币，我们建议使用%(options)s购买和捐赠比特币（最早且使用最广泛的加密货币）。 注意，小额捐赠时，信用卡手续费会抵消我们 %(discount)s%% 的折扣，所以我们推荐更长期的订阅。 在跳转后的页面，您可以选择使用信用卡/借记卡，PayPal或Venmo向我们捐赠。 谷歌支付和苹果支付也是可行的。 请注意，小额捐赠的费用较高，因此我们建议长期订阅。 在 PayPal（美国）的捐赠方式中，我们将使用 PayPal Crypto，这可以让我们保持匿名。我们感谢您花时间学习如何用这种方式捐赠，这样让我们很省心。 使用 PayPal 捐赠。 使用您的常规PayPal账户捐款。 使用Revolut捐款。 如果您有Revolut，这是最简单的捐款方式！ 这种支付方式最高限额 %(amount)s。请选择其他订阅时长或换一种支付方式。 这种支付方式最低需要 %(amount)s。请选择其他订阅时长或换一种支付方式。 币安 Coinbase Kraken 请选择付款方式。 “认领一个种子”：把你的用户名或指定信息添加到一个 torrent 文件名 <div %(div_months)s>会员每12个月一次</div> 将你的用户名或匿名捐赠写进贡献者名单 提前尝鲜新功能 独家 Telegram 联系方式，订阅幕后新闻 每天 %(number)s 次高速下载 如果您在本月捐款！ <a %(a_api)s>JSON API</a> 访问 保存人类知识和文化方面的传奇地位 前档所有福利，还包括： 通过<a %(a_refer)s>推荐朋友</a>赚取<strong>%(percentage)s%%次额外下载</strong>。 <strong>不限量</strong>、无需验证的 SciDB 论文 在询问账户或捐赠问题时，请提供您的账户ID、截图、收据以及尽可能多的信息。我们每1-2周才检查一次电子邮件，因此确实这些信息会导致问题迟迟无法解决。 <a %(a_refer)s>向好友推广</a>，获取更多下载次数！ 由于我们是一个由志愿者组成的小型团队，或许需要1-2周时间来回复。 账户名或头像可能会有点奇怪。不必担心！这个账户由我们的捐赠伙伴管理，我们的账户没有被黑。 捐赠 <span %(span_cost)s></span> <span %(span_label)s></span> 12 个月 “%(tier_name)s” 1 个月 “%(tier_name)s” 24 个月“%(tier_name)s” 3 个月 “%(tier_name)s” 48个月“%(tier_name)s” 6 个月“%(tier_name)s” 96个月“%(tier_name)s” 您仍然可以在结帐时取消捐赠。 点击“捐赠”按钮确认此捐赠。 <strong>重要提示</strong>：加密货币价格可能会大幅波动，有时甚至在几分钟内波动20%%。这仍然比我们与许多支付服务提供商合作所产生的费用要低，他们通常会收取像我们这样的“影子慈善机构”50-60%%的费用。<u>如果您将收据与您所支付的原始价格一起发送给我们，我们仍然会将您所选择的会员资格记入您的帐户</u>（只要收据不超过几个小时）。我们真的很感谢你愿意忍受这样的事情来支持我们！❤️ ❌ 出问题了。请重新加载页面并再试一次。 <span %(span_circle)s>1</span>在Paypal上购买比特币 <span %(span_circle)s>2</span>把比特币转到我们的地址 ✅ 跳转到捐赠页面… 捐赠 请等待至少 <span %(span_hours)s>24 小时</span>（并刷新此页面）后再联系我们。 如果您想在没有会员资格的情况下捐款（任意金额），请随意使用此 Monero (XMR) 地址：%(address)s。 在寄送出您的礼品卡后，我们的自动化系统会在几分钟内确认。如果没有成功的话，请尝试重新寄送礼品卡(<a %(a_instr)s>说明</a>)。 如果还是不管用的话，请给我们发邮件，安娜会手动检查请求（这可能会花几天时间）。请确保在邮件中提及是否已经尝试重新寄送过礼品卡。 例如： 请使用 <a %(a_form)s> 亚马逊网站官方表格 </a> 将金额为 %(amount)s 的礼品卡发送到以下电子邮件地址。 表单中 ”To" 收件人电子邮箱： 亚马逊礼品卡 我们无法接收其他方式的礼品卡，<strong>只能通过 Amazon.com 上的官方表格直接发送</strong>。如果没有使用此表格，我们将无法退还您的礼品卡。 仅使用一次。 专属于您的账户，请勿分享。 等待礼品卡…（刷新页面查看） 打开<a %(a_href)s>二维码捐赠页面</a>。 使用支付宝应用程序扫描二维码，或按下按钮打开支付宝应用程序。 请耐心等待；由于页面在中国，可能需要一些时间加载。 <span %(style)s>3</span>进行捐赠（扫描二维码或按下按钮） 在 PayPal 上购买 PYUSD 币 在 Cash App 上购买比特币 (BTC) 购买比你想捐赠的数量（%(amount)s）稍微多一点的币（我们推荐 %(more)s）以支付交易手续费，剩下的币你可以存着。 进入 Cash App 中的“比特币” (BTC) 页面。 将比特币转至我们的账户 对于小额捐赠（低于 $25），您可能需要使用 Rush 或 Priority。 点击“发送比特币”按钮进行“提现”。通过按%(icon)s图标从美元切换到BTC。在下方输入BTC金额并点击“发送”。如果遇到问题，请参阅<a %(help_video)s>此视频</a>。 快速服务虽然更为便捷，但同时也会产生高昂的手续费。 如果您希望方便快捷地进行大金额的捐赠，并且不介意额外多支付5-10美元的手续费，可以使用此服务。 请务必发送捐赠页面上显示的确切加密货币金额，而不是美元金额。 否则费用将被扣除，但我们无法自动处理您的会员资格。 有时确认可能需要长达24小时，因此请务必刷新此页面（即使它已过期）。 信用卡/借记卡说明 通过我们的信用卡/借记卡页面捐赠 有些步骤提到了加密货币钱包。不用担心，此处你无需深入了解加密货币。 %(coin_name)s 说明 使用您的加密钱包应用程序扫描此QR码，以快速填写付款详细信息 扫描QR码要付款 我们只支持标准版本的加密货币，不支持特殊网络或版本的加密货币。对于不同种类的加密货币，可能需要一小时来确认交易。 在<a %(a_page)s>这个页面</a>捐赠 %(amount)s。 这笔捐赠已经超时。请取消并重新创建一笔。 如果您已经付款： 是的，我已经发送了收据 如果加密货币汇率在交易期间波动，请务必包括显示原始汇率的收据。我们非常感谢您不厌其烦地使用加密货币，这对我们非常有帮助！ ❌ 出错了，请刷新页面重试。 <span %(span_circle)s>%(circle_number)s</span>把收据用电子邮件发给我们 如果您遇到了任何问题，请通过%(email)s联系我们，并附上尽可能多的信息（例如截图）。 ✅ 感谢你的捐赠！安娜将在几天内手动激活您的会员资格。 发送收据或截图到您的个人验证地址： 当你用电子邮件发送了收据之后，点击这个按钮，这样安娜可以手动审核（这可能需要几天）： 将收据或截图发送到您的个人验证地址。请勿使用此电子邮件地址进行PayPal捐赠。 取消 是的，请取消 您确定要取消吗？如果您已经付款，请不要取消。 ❌ 出问题了。请重新加载页面并再试一次。 进行新的捐赠 ✅ 您的捐赠已被取消。 日期：%(date)s 标识符：%(id)s 再次捐赠 状态：<span %(span_label)s>%(label)s</span> 总计：%(total)s <span %(span_details)s>（每月 %(monthly_amount_usd)s，共 %(duration)s 个月，折扣 %(discounts)s%%）</span> 总计：%(total)s <span %(span_details)s>（每月 %(monthly_amount_usd)s，共 %(duration)s 个月）</span> 1. 输入你的电子邮箱。 2. 选择支付方式。 3. 再次选择支付方式。 4. 选择自行托管的（Self-hosted）钱包。 5. 点击“我确认所有权”。 6. 你会收到一封邮件回复。请将这封邮件转发给我们，我们会尽快确认你的捐赠。 （你可能想取消，然后重新创建一笔捐赠） 此付款已经过期。如果您想再次捐赠，请点击上面的“重新捐赠”按钮。 您已经付款了。如果你想查看付款详情，请点击这里： 显示以前的付款说明 如果捐赠页面被屏蔽，请尝试使用其他互联网连接（例如 VPN 或手机网络）。 不幸的是，支付宝页面通常只能从<strong>中国大陆</strong>访问。您可能需要暂时禁用您的VPN，或使用连接到中国大陆的VPN（有时连接到香港也可以）。 <span %(span_circle)s>1</span>在支付宝上捐款 使用<a %(a_account)s>此支付宝账户</a>捐赠总金额%(total)s 支付宝使用说明 <span %(span_circle)s>1</span>转到我们的加密货币账户 将总金额%(total)s捐赠至以下地址之一： 加密货币说明 按照说明购买比特币(BTC)。你只需要购买你想捐赠的数量，%(total)s。 输入我们的比特币(BTC)地址作为收款人，并按照指示发送您 %(total)s 的捐款： <span %(span_circle)s>1</span>在Pix上捐赠 使用Pix帐户<a %(a_account)s>捐献总金额%(total)s Pix使用说明 <span %(span_circle)s>1</span>通过微信捐赠 使用<a %(a_account)s>此微信账号</a>捐赠总金额%(total)s 微信使用说明 使用以下任何“信用卡到比特币”的快速服务，这些服务只需几分钟： BTC / 比特币地址（外部钱包）： BTC / 比特币金额： 请在表格中填写以下详细信息： 如果有任何过时的信息，请发送电子邮件告知我们。 请使用此 <span %(underline)s>确切金额</span>。由于信用卡的手续费，您的总费用可能会更高。对于小额支付，这可能会超过我们的折扣，不便之处，敬请谅解。 （最低：%(minimum)s） （最低：%(minimum)s） （最低：%(minimum)s） （最低：%(minimum)s，首次交易无需验证） （最低：%(minimum)s） （最低：%(minimum)s，在首次交易中是否需要验证取决于所在国家） 按照下列说明购买 PYUSD 币（PayPal USD）。 购买比你想捐赠的数量（%(amount)s）稍微多一点的币（我们推荐 %(more)s）以支付交易手续费，剩下的币你可以存着。 在 PayPal 应用或网站上找到“PYUSD”页面。点击“转账”按钮 %(icon)s，然后点“发送”。 更新状态 进行捐赠即可重置计时器。 请务必使用以下的 BTC 金额，<em>而不是</em> 欧元或美元，否则我们将无法收到正确的金额，也无法自动确认您的会员资格。 在 Revolut 上购买比特币 (BTC) 购买比你想捐赠的数量（%(amount)s）稍微多一点的币（我们推荐 %(more)s）以支付交易手续费，剩下的币你可以存着。 进入 Revolut 中的“加密货币”页面购买比特币 (BTC)。 将比特币转至我们的账户 对于小额捐赠（低于 $25），您可能需要使用 Rush 或 Priority。 点击“发送比特币”按钮进行“提现”。通过按%(icon)s图标从欧元切换到BTC。在下方输入BTC金额并点击“发送”。如果遇到问题，请参阅<a %(help_video)s>此视频</a>。 状态： 1 2 分步骤指南 请参阅下面的分步指南。 否则你可能登录不了这个账号！ 如果你还不知道怎么做，先记下你的登录密钥： 感谢您的捐赠！ 剩余时间： 捐赠 给 %(account)s 转账 %(amount)s 等待确认（刷新页面查看）…… 等待转账（刷新页面查看）…… 较早 24h内的高速下载次数已达上限。 高速合作伙伴下载服务器的标志为：%(icon)s。 最近18小时 还没有下载过文件。 下载的文件不会公开显示。 所有时区均为UTC。 已下载文件 如果你既用了高速下载又用了低速下载，这里会显示两次。 不用太担心，有很多人从我们链接的网站下载，遇到麻烦的情况极其罕见。不过，为了安全起见，我们建议使用 VPN（付费）或 <a %(a_tor)s>Tor</a>（免费）。 我下载了乔治·奥威尔写的《1984》，会有警察来逮捕我吗？ 您就是安娜！ 谁是安娜？ 我们为会员提供一个稳定的 JSON API，用于获取快速下载 URL：<a %(a_fast_download)s>/dyn/api/fast_download.json</a>（文档在 JSON 内）。 对于其他用例，例如遍历我们所有的文件、构建自定义搜索等，我们建议<a %(a_generate)s>生成</a>或<a %(a_download)s>下载</a>我们的ElasticSearch和MariaDB数据库。原始数据可以通过<a %(a_explore)s>JSON文件</a>手动浏览。 我们的原始种子列表也可以作为<a %(a_torrents)s>JSON</a>下载。 你们有 API 吗？ 我们不在此处托管任何受版权保护的材料。我们是一个搜索引擎，因此只索引已经公开的元数据。从这些外部来源下载时，我们建议您检查您所在司法管辖区的法律，了解允许的内容。我们不对他人托管的内容负责。 如果您对这里看到的内容有任何投诉，最好的办法是联系原始网站。我们定期将他们的更改拉入我们的数据库。如果您确实认为您有一个有效的数字千年版权法投诉需要我们回应，请填写<a %(a_copyright)s>数字千年版权法/版权投诉表</a>。我们会认真对待您的投诉，并尽快回复您。 如何报告版权侵权？ 以下是一些对影子图书馆和数字保存领域具有特殊意义的书籍： 你最喜欢的书是什么？ 我们还想提醒大家，我们所有的代码和数据都是完全开源的。这对于像我们这样的项目来说是独一无二的——我们不知道还有其他任何拥有如此庞大目录且完全开源的项目。我们非常欢迎任何认为我们项目运行不佳的人拿走我们的代码和数据，建立自己的影子库！我们不是出于恶意这么说——我们真心认为这会很棒，因为这会提高大家的标准，更好地保存人类的遗产。 我讨厌你们运营这个项目的方式！ 我们想要有人帮忙搭建<a %(a_mirrors)s>镜像</a>，也能在经济上给予资助。 我能提供什么帮助？ 确实如此。 我们收集元数据的灵感来自 Aaron Swartz 的目标，即“为每本出版的书创建一个网页”，为此他创建了 <a %(a_openlib)s>Open Library</a>。该项目做得很好，但我们的独特位置使我们能够获取他们无法获取的元数据。另一个灵感是我们想知道 <a %(a_blog)s>世界上有多少本书</a>，以便计算我们还剩下多少书需要保存。 你收集元数据吗？ 请注意，mhut.org 会屏蔽某些 IP 范围，因此可能需要使用 VPN访问。 <strong>安卓：</strong> 点击右上角的三点菜单，然后选择“添加到主屏幕”。 <strong>iOS：</strong> 点击底部的“分享”按钮，然后选择“添加到主屏幕”。 我们没有官方的移动端应用程序，但您可以将此网站安装为应用程序。 你有移动端应用吗？ 请将它们发送到<a %(a_archive)s>Internet Archive</a>。他们会妥善保存这些材料。 我如何捐赠书籍或其他实物材料？ 如何请求图书？ <a %(a_blog)s>安娜的博客</a>，<a %(a_reddit_u)s>Reddit</a>，<a %(a_reddit_r)s>Subreddit</a>——定期更新 <a %(a_software)s>安娜的软件</a> — 我们的开源代码 <a %(a_datasets)s>Datasets</a> — 关于数据 <a %(a_li)s>.li</a>，<a %(a_se)s>.se</a>，<a %(a_org)s>.org</a>——备用域名 关于安娜的档案，是否有更多资源？ <a %(a_translate)s>在安娜的软件上翻译</a> — 我们的翻译系统 <a %(a_wikipedia)s>维基百科</a> — 了解更多关于我们的信息（请帮助保持此页面更新，或为您的语言创建一个页面！） 选择您喜欢的设置，保持搜索框为空，点击“搜索”，然后使用浏览器的书签功能将页面加入书签。 如何保存我的搜索设置？ 我们欢迎安全研究人员搜索我们系统中的漏洞。我们是负责任披露的坚定支持者。请通过<a %(a_contact)s>此处</a>联系我们。 我们目前无法奖励漏洞赏金，除非是具有<a %(a_link)s>可能危及我们匿名性</a>的漏洞，对于这些漏洞，我们提供 1 万到 5 万美元不等的赏金。我们希望将来能提供更广泛的漏洞赏金范围！请注意，社会工程攻击不在范围内。 如果您对攻防安全感兴趣，并希望帮助存档世界的知识和文化，请务必联系我们。您可以通过多种方式提供帮助。 你们有负责任的披露计划吗？ 尽管我们真的很想为世界上每个人都提供高速下载，但是我们确实没有足够的资源做到这一点。如果有一位富有的资助者愿意为我们提供这方面的支持，那将是非常了不起的，但在那之前，我们会尽力而为。我们是一个非营利项目，几乎只能通过捐赠来维持自己。 这就是为什么我们与合作伙伴部署了两种免费下载系统：一种是下载速度慢的服务器，一种是下载速度稍快但需要排队等待的服务器（以减少同时下载的人数）。 我们还为慢速下载配置了<a %(a_verification)s>浏览器验证</a>，以防止机器人和爬虫滥用它们，导致正常用户的下载速度变得更慢。 请注意，使用Tor浏览器时，您可能需要调整安全设置。在最低的选项“标准”中，Cloudflare验证码挑战成功。在较高的选项“更安全”和“最安全”中，挑战失败。 对于大文件，有时下载速度慢会导致中途中断。我们建议使用下载管理器（如JDownloader）来自动恢复大文件下载。 为什么下载速度这么慢？ 常见问题 (FAQ) 使用<a %(a_list)s>种子列表生成器</a>生成在您的存储空间限制内最需要被做种的种子列表。 是的，请查看<a %(a_llm)s>大语言模型数据</a>页面。 大多数种子直接包含文件，这意味着您可以指示种子客户端仅下载所需的文件。要确定下载哪些文件，您可以<a %(a_generate)s>生成</a>我们的元数据，或<a %(a_download)s>下载</a>我们的ElasticSearch和MariaDB数据库。不幸的是，一些种子集合在根目录中包含.zip或.tar文件，在这种情况下，您需要下载整个种子才能选择单个文件。 目前还没有易于使用的工具来筛选种子文件，但我们欢迎您的贡献。 （不过，我们确实有<a %(a_ideas)s>一些想法</a>来应对后一种情况。） 详细回答： 简短回答：不容易。 我们尽量保持此列表中的种子文件最小的重复或重叠，但这并不总是能实现，并且在很大程度上取决于源库的政策。对于发布自己种子文件的图书馆，这超出了我们的控制范围。对于 Anna’s Archive 发布的种子文件，我们仅基于 MD5 哈希值进行去重，这意味着同一本书的不同版本不会被去重。 有。 这些实际上是PDF和EPUB文件，只是我们的许多种子中没有扩展名。您可以在两个地方找到种子文件的元数据，包括文件类型/扩展名： 1. 每个集合或发布都有其自己的元数据。例如，<a %(a_libgen_nonfic)s>Libgen.rs 种子</a>有一个对应的元数据库托管在Libgen.rs网站上。我们通常会从每个集合的<a %(a_datasets)s>数据集页面</a>链接到相关的元数据资源。 2. 我们建议<a %(a_generate)s>生成</a>或<a %(a_download)s>下载</a>我们的ElasticSearch和MariaDB数据库。这些数据库包含Anna’s Archive中每条记录与其对应种子文件的映射（如果有），在ElasticSearch JSON中的“torrent_paths”下。 有些种子客户端不支持较大的片段大小，而我们的许多种子文件都有这种情况（对于较新的种子文件，我们不再这样做——即使这在规范中是有效的！）。如果遇到这种情况，请尝试使用不同的客户端，或者向您的种子客户端的开发者投诉。 我想帮助做种，但我没有太多磁盘空间。 种子下载速度太慢了；我可以直接从你们那里下载数据吗？ 我可以只下载部分文件，比如某个特定语言或主题的文件吗？ 如何处理种子中的重复文件？ 我可以获取JSON格式的种子列表吗？ 我在种子文件中没有看到PDF或EPUB，只有二进制文件？我该怎么办？ 为什么我的种子客户端无法打开你们的一些种子文件/磁力链接？ 种子文件常见问题 怎样上传新书？ 请查看<a %(a_href)s>这个优秀的项目</a>。 您有运行时间监控器吗？ 安娜的档案是什么？ 成为会员以享受高速下载。 我们现在支持亚马逊礼品卡、信用卡和借记卡、加密货币、支付宝和微信支付。 你已经用完了今日的高速下载次数。 开放获取 过去30日内每小时下载量。平均每小时：%(hourly)s。平均每天：%(daily)s。 我们与合作伙伴共同努力，让任何人都能轻松、自由地获得我们的收藏。我们相信每个人都有权分享人类的集体智慧，不<a %(a_search)s>枉费作者的心血</a>。 安娜的档案使用的数据集完全开放，可以使用种子批量镜像。<a %(a_datasets)s>了解更多……</a> 长期存档 完整数据库 搜索 图书、论文、杂志、漫画、图书馆记录、元数据…… 我们所有的<a %(a_code)s>代码</a>和<a %(a_datasets)s>数据</a>都是完全开源的。 <span %(span_anna)s>安娜的档案</span>是一个非盈利性项目，有两个目标： <li><strong>保存：</strong>备份人类所有的知识和文化。</li><li><strong>开放获取：</strong>让世界上的任何人都可访问并获取这些知识和文化。</li> 我们有全世界最大的高质量文本数据集。<a %(a_llm)s>了解更多……</a> 大语言模型（LLM）训练数据 🪩 镜像：征集志愿者 如果你正在运营一个支持高风险、匿名的支付处理商，请联系我们。我们也在寻找精巧的小广告投放者。所有的收益将用于我们的文化保存事业。 保存 我们估计我们已经保存了<a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">全球约5%%的图书</a>。 我们保存书籍、论文、漫画、杂志等资料，将这些资料从各种<a href="https://en.wikipedia.org/wiki/Shadow_library">影子图书馆</a>、官办图书馆、其他收藏集等集中到一处。通过简化批量复制的过程——利用BT种子，这些数据在世界各处就能有多份副本，从而得以永久保存。有些影子图书馆自身已采用这种方式（如 Sci-Hub、Library Genesis），而安娜的档案还“解放”了其他不提供批量分发的影子图书馆（如 Z-Library）、无法称为影子图书馆的图书馆（如 Internet Archive、读秀）的馆藏数据。 数据的广泛传播、代码的开源共享，使我们网站不惧取缔的风险，并保证了人类知识文化的长期保存。进一步了解<a href="/datasets">我们的数据集</a>。 如果你是<a %(a_member)s>会员</a>，无需浏览器验证。 🧬 SciDB 是 Sci-Hub 的延续。 SciDB 打开 DOI Sci-Hub <a %(a_paused)s>已暂停</a>新论文的上传。 直接访问 %(count)s 篇学术论文 🧬&nbsp;SciDB 是 Sci-Hub 的延续，具有类似Sci-Hub的界面和直接查看 PDF 的功能。输入您的 DOI 以查看。 我们拥有完整的Sci-Hub收藏以及新的论文。大多数可以通过类似于Sci-Hub的熟悉界面直接查看。一些可以通过外部来源下载，在这种情况下我们会显示链接。 做种对我们帮助很大。<a %(a_torrents)s>进一步了解……</a> >%(count)s 个上传者 <%(count)s 个上传者 %(count_min)s–%(count_max)s 个上传者 🤝 招募志愿者 作为一个非营利的开源项目，我们一直在寻找愿意提供帮助的人。 IPFS下载 %(by)s 的书单，创建于 <span %(span_time)s>%(time)s</span> 保存 ❌ 出错了，请重试。 ✅ 保存成功，请重新加载网页。 列表为空。 编辑 通过查找文件并打开“列表”选项卡，在列表中添加或删除文件。 书单 我们如何提供帮助 去重（删除重复项） 文本和元数据提取 OCR 我们能够提供对我们完整收藏以及未发布收藏的高速访问。 我们可以为数万美元的捐赠提供企业级访问权限。我们也愿意用这个来交换我们尚未拥有的高质量收藏。 如果您能为我们提供数据丰富的信息，例如： 支持人类知识的长期存档，同时为您的模型获取更好的数据！ 请<a %(a_contact)s>联系我们</a>以讨论如何合作。 众所周知，大语言模型依赖高质量数据。我们拥有世界上最大的书籍、论文、杂志等收藏，这些都是最高质量的文本来源。 大语言模型数据 独特的规模和范围 我们馆藏有超过一亿个文件，包括学术期刊、教科书和杂志。我们通过合并现有的大型存储库来实现这一规模。 我们的一些来源收藏已经可以批量获取（Sci-Hub和部分Libgen）。其他来源是我们自己解放的。<a %(a_datasets)s>Datasets</a>显示了完整的概述。 我们的馆藏包括数百万本电子书时代之前的书籍、论文和杂志。该馆藏的大部分已经进行了OCR处理，并且内部重复率很低。 继续 如果你丢失了密钥，请<a %(a_contact)s>联系我们</a>，并提供尽可能多的信息。 你可能需要临时创建一个新账号来联系我们。 请先<a %(a_account)s>登录</a>再查看本页。</a> 为了防止垃圾邮件机器人创建大量帐户，我们需要首先验证您的浏览器。 如果您陷入无限循环，我们建议安装<a %(a_privacypass)s>Privacy Pass</a>。 它还可能有助于关闭广告拦截器和其他浏览器扩展。 登录/注册 安娜的档案由于维护暂时关闭。 请一小时后再来。 备选作者 备用描述 备用版本 备用扩展名 备用文件名 备用出版商 备选标题 开源日期 更多信息…… 描述 在安娜的档案中搜索 CADAL SSNO 号 在安娜的档案中搜索读秀 SSID 号 在安娜的档案中搜索读秀 DXID 号 在安娜的档案中检索 ISBN 书号 在安娜的档案中检索 OCLC (WorldCat) 编号 在安娜的档案中检索 Open Library ID Anna的档案在线查看器 %(count)s 个受影响的页面 下载后： 此文件可能有更好的版本，位于 %(link)s 批量种子下载 馆藏 使用在线工具进行格式转换。 推荐的转换工具：%(links)s 对于大文件，我们建议使用下载管理器以防止中断。 推荐的下载管理器：%(links)s EBSCOhost 电子书索引 （仅限专家） （也可以点击顶部的“GET”） （点击顶部的“GET”） 外部下载 今日下载剩余 %(remaining)s 次。感谢您成为会员！❤️ 你已经用完了今日的高速下载次数。 你最近下载过此文件。链接在一段时间内仍然有效。 成为<a %(a_membership)s>会员</a>以支持书籍、论文等的长期保存。为了感谢您对我们的支持，您将获得高速下载权益。❤️ 🚀 快速下载 🐢 低速下载 从互联网档案馆借阅 IPFS 网关 #%(num)d （使用 IPFS 时您可能需要多次尝试） Libgen.li Libgen.rs 的小说板块 Libgen.rs 非虚构文学板块 已知他们的广告包含恶意软件，因此请使用广告拦截器或不要点击广告 亚马逊的“发送到 Kindle” djazz 的“发送到 Kobo/Kindle” MagzDB ManualsLib Nexus/STC （Nexus/STC 文件下载可能不可靠） 未找到下载。 所有选项下载的文件都相同，应该可以安全使用。即使这样，从互联网下载文件时始终要小心。例如，确保您的设备更新及时。 （无重定向） 在我们的查看器中打开 （在查看器中打开） 选项 #%(num)d: %(link)s %(extra)s 在 CADAL 中查找原始记录 手动在读秀网站上搜索 在 ISBNdb 中查找原始记录 在 WorldCat 中查找原始记录 在 Open Library 中查找原始记录 在各种其他数据库中检索 ISBN 书号 （仅限印刷品阅读障碍者） PubMed 您将需要一个电子书或 PDF 阅读器来打开文件，具体取决于文件格式。 推荐的电子书阅读器：%(links)s 安娜的档案 🧬 SciDB Sci-Hub: %(doi)s （相关 DOI 在Sci-Hub中可能不可用） 您可以将 PDF 和 EPUB 文件发送到您的 Kindle 或 Kobo 电子阅读器。 推荐的工具：%(links)s 更多信息请参见<a %(a_slow)s>常见问题解答</a>。 支持作者和图书馆 如果您喜欢这个并且能够负担得起，请考虑购买原版，或直接支持作者。 如果您当地的图书馆有这本书，请考虑在那里免费借阅。 此文件暂时无法通过合作方服务器下载。 种子 由可信的合作方提供。 Z-Library Z-Library TOR （需要TOR浏览器） 显示外部下载 <span class="font-bold">❌ 此文件可能有问题，已从源库中隐藏。</span> 有时这是应版权所有者的要求，有时是因为有更好的选择， 但有时是因为文件本身有问题。 下载可能仍然没问题，但我们建议先搜索替代文件。 更多细节： 如果您仍想下载此文件，请确保仅使用受信任的最新软件打开它。 元数据中的注释 AA: 在安娜的档案中搜索“%(name)s” 代码浏览器： 在代码浏览器中查看“%(name)s” URL: 网站: 若你拥有的文件未被安娜的档案收录，请考虑<a %(a_request)s>上传文件</a>。 互联网档案馆控制的数字借阅文件 “%(id)s” 这是一条互联网档案馆文件的记录，不是直接可以下载的文件。你可以尝试借阅此书（链接如下），或用该网址<a %(a_request)s>请求一份文件</a>。 改进元数据 CADAL SSNO %(id)s 元数据记录 这是元数据记录，而非可下载的文件。你可以使用这个链接来<a %(a_request)s>请求文件</a>。 读秀 SSID %(id)s 元数据记录 ISBNdb %(id)s 元数据记录 MagzDB ID %(id)s 元数据记录 Nexus/STC ID %(id)s 元数据记录 OCLC (WorldCat) 编号 %(id)s 元数据记录 Open Library %(id)s 元数据记录 Sci-Hub 文件 “%(id)s” 未找到 未在我们的数据库中找到与“%(md5_input)s”匹配的文件。 添加评论（%(count)s） 你可以从 URL 获取 MD5 值，例如。 此文件更好版本的MD5（如果适用）。 如果有另一个文件与此文件非常匹配（相同版本，相同文件扩展名），人们应该使用该文件而不是此文件。如果你有安娜的档案未保存的更好的版本，请<a %(a_upload)s>上传</a>。 出了点问题。请重新加载页面并重试。 您留下了一条评论。可能需要一分钟才能显示。 请使用<a %(a_copyright)s>数字千年版权法 / 版权声明表格</a>。 描述问题（必填） 如果这个文件质量很好，您可以在此处讨论任何相关内容！如果不是，请点击“反馈文件问题”。 极佳的文件质量（%(count)s） 文件质量 了解如何亲自<a %(a_metadata)s>改进此文件的元数据</a>。 问题描述 请<a %(a_login)s>登录</a>。 我喜欢这本书！ 通过反馈此文件的质量来改进社区！ 🙌 出了点问题。请重新加载页面并重试。 反馈文件问题 (%(count)s) 感谢您提交反馈。它将显示在此页面上，并由安娜人工审核（直到我们有一个合适的审核系统）。 留下评论 提交反馈 这个文件有什么问题？ 借阅（%(count)s） 评论（%(count)s） 下载次数（%(count)s） 查阅元数据（%(count)s） 列表（%(count)s） 统计（%(count)s） 有关此文件的详细信息，请查看其<a %(a_href)s>JSON 文件</a>。 这是由<a %(a_ia)s>IA的受控数字借阅</a>图书馆管理的文件，并由安娜的档案索引以供搜索。有关我们编译的各种数据集的信息，请参见<a %(a_datasets)s>Datasets页面</a>。 来自关联记录的元数据 改进Open Library上的元数据 “文件的MD5”是根据文件内容计算出的哈希值，并且基于该内容具有相当的唯一性。我们这里索引的所有影子图书馆都主要使用MD5来标识文件。 一个文件可能会出现在多个影子图书馆中。有关我们编译的各种数据集的信息，请参见<a %(a_datasets)s>数据集页面</a>。 反馈文件质量 总下载量：%(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} 捷克元数据 %(id)s} 独秀 SSID %(id)s} EBSCOhost edsebk %(id)s} Google 图书 %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} 警告：多个关联记录： 当您在安娜的档案上查看一本书时，您可以看到各种字段：标题、作者、出版社、版本、年份、描述、文件名等。所有这些信息统称为<em>元数据</em>。 由于我们将书籍从各种<em>源图书馆</em>中汇集在一起，我们会显示该源图书馆中可用的任何元数据。例如，对于我们从Library Genesis获取的书籍，我们将显示来自Library Genesis数据库的标题。 有时一本书会出现在<em>多个</em>来源图书馆中，这些图书馆可能有不同的元数据字段。在这种情况下，我们只展示所有字段中的最长版本，因为这个版本可能包含最多的有用信息！我们仍然会在下方显示其他字段，例如作为“备选标题”（但仅当它们不同的时候）。 我们还从来源图书馆中提取<em>代码</em>，如标识符和分类器。<em>标识符</em>唯一地代表一本书的特定版本；例如ISBN、DOI、Open Library ID、Google Books ID或Amazon ID。<em>分类器</em>将多个相似的书籍分组在一起；例如杜威十进制分类法（DCC）、国际十进分类法（UDC）、美国国会图书馆分类法（LCC）、RVK或GOST。有时这些代码在来源图书馆中明确链接，有时我们可以从文件名或描述中提取它们（主要是ISBN和DOI）。 我们可以使用标识符在<em>仅元数据集合</em>中找到记录，例如OpenLibrary、ISBNdb或WorldCat/OCLC。如果您想浏览这些集合，我们的搜索引擎中有一个特定的<em>元数据标签</em>。我们使用匹配的记录来填补缺失的元数据字段（例如，如果缺少标题），或者例如作为“替代标题”（如果已有标题）。 要查看一本书的元数据来源，请参阅书籍页面上的<em>“技术细节”选项卡</em>。其中包含指向该书原始JSON的链接，并指向原始记录的JSON。 有关更多信息，请参见以下页面：<a %(a_datasets)s>Datasets</a>，<a %(a_search_metadata)s>Search (metadata tab)</a>，<a %(a_codes)s>Codes Explorer</a>，以及<a %(a_example)s>Example metadata JSON</a>。最后，我们所有的元数据都可以<a %(a_generated)s>生成</a>或<a %(a_downloaded)s>下载</a>为ElasticSearch和MariaDB数据库。 背景 您可以通过改进元数据来帮助保存书籍！首先，阅读安娜的档案上关于元数据的背景知识，然后学习如何通过与Open Library链接来改进元数据，并在安娜的档案上获得免费会员资格。 改进元数据 如果您遇到元数据不正确的文件，应该如何修复？您可以前往源图书馆并按照其修复元数据的程序进行操作，但如果文件存在于多个源图书馆中，该怎么办呢？ 在安娜档案馆上有一个特殊的标识符。<strong>Open Library上的annas_archive md5字段总是覆盖所有其他元数据！</strong>让我们先退一步，了解一下Open Library。 Open Library由Aaron Swartz于2006年创立，目标是“为每一本出版的书籍创建一个网页”。它有点像书籍元数据的维基百科：每个人都可以编辑，拥有自由许可，并且可以批量下载。这是一个与我们使命最契合的书籍数据库——事实上，安娜的档案从Aaron Swartz的愿景和人生中受到了启发。 相比于重新造轮子，取而代之的是我们决定将我们的志愿者引导到Open Library。如果您看到一本书的元数据不正确，您可以通过以下方式提供帮助： 请注意，这仅适用于书籍，不适用于学术论文或其他类型的文件。对于其他类型的文件，我们仍然建议查找源图书馆。由于我们需要下载最新的Open Library数据转储，并重新生成我们的搜索索引，因此更改可能需要几周时间才能包含在安娜的档案中。  前往<a %(a_openlib)s>Open Library网站</a>。 找到正确的书籍记录。<strong>警告：</strong>请务必选择正确的<strong>版本</strong>。在Open Library中，有“作品”和“版本”。 “作品”可以是《哈利·波特与魔法石》。 “版本”可以是： 1997年由Bloomsbery出版的第一版，共256页。 2003年由Raincoast Books出版的平装版，共223页。 2000年由Media Rodzina出版的波兰语翻译版《Harry Potter I Kamie Filozoficzn》，共328页。 以上这些版本都有不同的ISBN和不同的内容，请务必选择正确的版本！ 编辑记录（如果不存在则创建），并尽可能添加有用的信息！既然你已经到这一步了，不妨让记录变得非常完善。 在“ID Numbers”下选择“Anna’s Archive”，并添加来自Anna’s Archive的书籍MD5。这是URL中“/md5/”之后的长串字母和数字。 尝试在安娜的档案中找到其他也匹配此记录的文件，并将它们添加进去。将来我们可以在安娜的档案的搜索页面上将这些文件分组为重复项。 完成后，写下你刚刚更新的URL。一旦你更新了至少30条带有Anna’s Archive MD5的记录，给我们发送一封<a %(a_contact)s>电子邮件</a>并发送列表。我们将为你提供Anna’s Archive的免费会员资格，以便你更轻松地进行这项工作（并感谢你的帮助）。这些必须是高质量的编辑，添加了大量信息，否则你的请求将被拒绝。如果任何编辑被Open Library的管理员撤销或更正，你的请求也会被拒绝。 Open Library链接 如果您在我们的工作开发和运营中投入了大量精力，我们可以讨论与您分享更多的捐赠收入，以便您根据需要进行部署。 我们只有在您完成所有设置并证明您能够保持档案更新后，才会支付托管费用。这意味着您需要自掏腰包支付前1-2个月的费用。 您消耗的时间不会得到补偿（我们的也一样），因为这纯粹是志愿工作。 我们愿意承担托管和VPN费用，最初每月最高可达200美元。这足以支付一个基本的搜索服务器和一个受数字千年版权法保护的代理服务器。 托管费用 请<strong>不要联系我们</strong>询问许可或基本问题。行动胜于言辞！所有信息都在那里，所以请直接开始设置您的镜像。 如果遇到问题，请随时在我们的Gitlab上提交工单或合并请求。我们可能需要与您一起构建一些特定于镜像的功能，例如将“Anna’s Archive”重新命名为您的网站名称，（初期）禁用用户账户，或从书籍页面链接回我们的网站。 一旦您的镜像运行起来，请联系我们。我们会审查您的操作安全，一旦确认无误，我们将链接到您的镜像，并开始与您更紧密地合作。 提前感谢任何愿意以这种方式贡献的人！这不是胆小者所能承受的，但这将巩固人类历史上最大真正开放图书馆的长久性。 入门指南 为提高安娜的档案的容灾能力，我们正在招募能运营镜像的志愿者。 您的版本显然被区分为镜像，例如“鲍勃的档案，一个安娜的档案镜像”。 您愿意承担与此工作相关的重大风险。您对所需的操作安全有深刻理解。<a %(a_shadow)s>这些</a> <a %(a_pirate)s>帖子</a>的内容对您来说是不言自明的。 最初我们不会提供访问合作服务器下载的权限，但如果一切顺利，我们可以与您分享。 您运行安娜的档案的开源代码库，并定期更新代码和数据。 您愿意与我们的团队合作，为我们的<a %(a_codebase)s>代码库</a>做出贡献，以实现这一目标。 我们正在寻找这些： 镜像：征集志愿者 再次捐赠。 还没有捐款。<a %(a_donate)s>捐赠一笔</a> 捐赠的详细资料不会公开显示。 我的捐赠记录 📡对于我们集合的批量镜像，请查看<a %(a_datasets)s >数据集</a>和<a %(a_torrents)s>Torrents</a>页面。 过去 24 小时内从您的 IP 地址下载的次数：%(count)s。 🚀要获得更快的下载并跳过浏览器检查，<a %(a_membership)s >成为会员</a>。 从合作伙伴网站下载 在等待时，您可以在不同的标签页中继续浏览安娜的档案（如果您的浏览器支持后台标签页刷新）。 可以同时等待多个下载页面加载（但请每个服务器上每次只下载一个文件）。 获得的下载链接在数小时内有效。 感谢您的等待，这使得网站对所有人免费开放！😊 <a %(a_main)s>&lt;此文件的所有下载地址</a> ❌ 无法通过 Cloudflare VPN 或其他方式从 Cloudflare IP 地址进行缓慢下载。 ❌ 下载速度慢只能通过官方网站进行。 访问 %(websites)s。 <a %(a_download)s>📚立即下载</a > 为了让所有人都可以免费下载文件，你需要等待一段时间才能下载此文件。 请<span %(span_countdown)s>%(wait_seconds)s</span> 秒后下载此文件。 警告：过去 24 小时内，您的 IP 地址有大量下载。 下载可能比平常慢。 如果您使用 VPN、共享互联网连接或您的 ISP 共享 IP，那么这个警告可能是由于这些原因引起的。 保存 ❌出错了，请再试一次。 ✅已保存。请重新加载页面。 更改显示名称。你的标识符（# 号后的内容）无法更改。 个人资料创建于 <span %(span_time)s>%(time)s</span> 编辑 列表 通过找到文件并打开“列表”选项卡来创建一个新列表。 暂无列表 未找到个人信息。 个人信息 目前，我们无法满足图书需求。 不要给我们发邮件请求图书。 请在 Z-Library 或 Libgen 论坛提出请求。 安娜的档案中的记录 DOI: %(doi)s 下载 SciDB Nexus/STC 暂无预览。请从<a %(a_path)s>安娜的档案</a>下载文件。 成为<a %(a_donate)s>会员</a>以支持人类知识的可访问性和长期保存。 作为额外奖励，🧬&nbsp;SciDB 对会员加载更快，没有任何限制。 有问题？请尝试<a %(a_refresh)s>刷新</a>。 Sci-Hub 增加特定搜索字段 搜索描述和元数据中的注释 出版年份 高级 访问方式 内容 显示 列表 表格 文件类型 语言 排序 最大 最相关 最新 (文件大小) （已开源） (出版年份) 最旧 随机 最小 来源 安娜的档案抓取并开源 数字借阅 (%(count)s) 期刊文章 (%(count)s) 在这些地方有匹配的结果：%(in)s。你可以在<a %(a_request)s>请求文件</a>时引用那些网址。 元数据 (%(count)s) 要通过代码探索搜索索引，请使用<a %(a_href)s>代码浏览器</a>。 检索索引每月更新。目前它包含截至 %(last_data_refresh_date)s 的条目。欲了解更多技术信息，参见 %(link_open_tag)s数据集页面</a>。 排除 仅包含 未选中 更多…… 下一页 … 上一页 此搜索索引当前包括来自互联网档案馆的受控数字借阅（Controlled Digital Lending）图书馆。<a %(a_datasets)s关于我们数据集的更多信息</a>. 更多数字借阅图书馆，请参阅<a %(a_wikipedia)s>维基百科</a>和 <a %(a_mobileread)s>MobileRead Wiki</a>。 DMCA/版权问题，请<a %(a_copyright)s>点击此处</a>。 下载时间 搜索时发生了错误。 请尝试<a %(a_reload)s>刷新页面</a>。如果一直有问题，请联系我们：%(email)s。 高速下载 事实上，任何人都可以通过做种我们的<a %(a_torrents)s>统一种子列表</a>来帮助保存这些文件。 ➡️ 这种情况有时会在搜索服务器速度慢时发生。你可能需要<a %(a_attrs)s>重新加载</a> 。 ❌ 这个文件可能有问题。 寻找论文？ 此处索引目前包含 ISBNdb 和 Open Library 的元数据。<a %(a_datasets)s>了解我们的数据集</a>。 世界上有非常多的书面作品元数据的来源，可以先从<a %(a_wikipedia)s>这篇维基百科文章</a>看起。如果你知道其他不错的列表，请告诉我们。 我们展示元数据的原始记录。我们对这些记录不做任何合并。 我们有目前世界上最完整的图书、论文和其他书面文献的开放编目。我们镜像了 Sci-Hub、Library Genesis、Z-Library <a %(a_datasets)s>等数据源</a>。 <span %(classname)s>未找到文件。</span>尝试更少或不同的搜索词和过滤器。 结果集 %(from)s-%(to)s（总计 %(total)s） 如果你还知道其他我们应该镜像的“影子图书馆”，或者你有任何问题，请联系我们：%(email)s。 %(num)d 部分匹配 %(num)d+ 部分匹配 在搜索框输入内容，以搜索数字借阅图书馆中的文件。 在框内输入文字以搜索我们共%(count)s个可直接下载的文件，我们会 <a %(a_preserve)s>永久保存</a>它们。 在框中键入要搜索的内容。 在框中输入文本，搜索我们<a %(a_preserve)s>永久保存</a>的 %(count)s 篇学术论文和期刊文章的索引。 在方框内输入内容搜索来自图书馆的元数据。这在 <a %(a_request)s>请求文件</a>时可能会有帮助。 提示：使用快捷键 “/”（搜索框）, “enter”（搜索）, “j”（上）, “k”（下）可快速导航。 这些是元数据记录，<span %(classname)s>不是</span>可下载文件。 搜索设置 搜索 数字借阅 下载 期刊文章 元数据 新的搜索 %(search_input)s - 搜索 搜索花费了太长时间，你可能会看到不准确的结果。有时<a %(a_reload)s>刷新一下</a>就能解决。 搜索运行时间过长，一般是搜索条件太宽泛了。过滤器计数可能不准确。 对于不被 Libgen 或 Z-Library 接受的大量上传（超过10000个文件），请联系我们：%(a_email)s。 对于Libgen.li，请确保首先使用用户名%(username)s和密码%(password)s登录到<a %(a_forum)s>他们的论坛</a>，然后返回到他们的<a %(a_upload_page)s>上传页面</a>。 目前，我们建议你先在 Library Genesis 的分支网站上传新书。这里是一份<a %(a_guide)s>快速指南</a>。注意，本站索引的那两个分支网站都从同一个上传系统中获取数据。 对于小型上传（最多10,000个文件），请将它们上传到%(first)s和%(second)s。 或者，您可以通过<a %(a_upload)s>这里</a>将它们上传到Z-Library 。 要上传学术论文，请也（除了 Library Genesis 之外）上传到 <a %(a_stc_nexus)s>STC Nexus</a>。他们是新论文的最佳影子库。我们还没有集成他们，但我们会在某个时候集成。您可以使用他们的 <a %(a_telegram)s>Telegram 上传机器人</a>，或者如果您有太多文件无法通过这种方式上传，请联系他们置顶消息中列出的地址。 <span %(label)s>大型志愿工作（50美元至5000美元赏金）：</span>如果您能够投入大量时间和/或资源来支持我们的工作，那么我们会与您建立更紧密的合作关系。最终您可以加入核心团队。尽管我们的预算有限，我们仍能够为最繁重的工作提供<span %(bold)s>💰金钱赏金</span>。 <span %(label)s>轻量志愿工作：</span>如果您只能抽出零星的几个小时，仍然有很多方式可以帮助我们。我们会奖励持续贡献的志愿者 <span %(bold)s>🤝 安娜的档案会员资格</span>。 安娜的档案离不开像您这样的志愿者。我们欢迎各种程度的参与，并主要寻求以下两类帮助： 如果您没有空余的时间来做志愿服务，您仍然可以通过<a %(a_donate)s>捐款</a>、<a %(a_torrents)s>做种我们的种子</a>、<a %(a_uploading)s>上传书籍</a>或<a %(a_help)s>向您的朋友传播安娜的档案</a>来帮助我们。 <span %(bold)s>公司：</span>我们提供高速直接访问我们的收藏，以换取企业级捐赠或交换新收藏（例如新扫描、OCR的Datasets、丰富我们的数据）。如果您有兴趣，请<a %(a_contact)s>联系我们</a>。另请参阅我们的<a %(a_llm)s>LLM页面</a>。 悬赏 我们一直在招募编程或攻防安全技能扎实的人才加入。你的参与可以为保护人类知识遗产带来深远影响。 作为感谢，我们会为做出实质性贡献的人提供会员资格。作为特别感谢，我们会为特别重要和困难的任务提供金钱奖励。这不应被视为工作的替代品，但它是一个额外的激励，并且可以帮助抵消产生的费用。 我们的大部分代码是开源的，当颁发悬赏时，我们也会要求您的代码是开源的。我们可以根据具体情况讨论一些例外情况。 悬赏将颁发给第一个完成任务的人。请随时在悬赏票据上发表评论，让其他人知道您正在处理某个任务，这样其他人可以暂缓或联系您合作。但请注意，其他人仍然可以自由地进行工作并尝试抢先完成。然而，我们不会为草率的工作颁发悬赏。如果有两个高质量的提交在时间上接近（在一两天内），我们可能会酌情选择向两者颁发悬赏，例如第一个提交100%%，第二个提交50%%（总共150%%）。 对于较大的悬赏任务（尤其是数据抓取任务），请在完成约5%%时联系我们，并确保您的方法可以扩展到整个里程碑。您需要与我们分享您的方法，以便我们提供反馈。此外，这样我们可以决定在有多人接近完成悬赏时该怎么做，例如可能向多人颁发悬赏，鼓励人们合作等。 警告：高悬赏任务<span %(bold)s>难度大</span>——从较容易的任务开始可能会更加明智。 前往我们的<a %(a_gitlab)s>Gitlab问题列表</a>并按“标签优先级”排序。这大致显示了我们关心的任务顺序。没有明确悬赏的任务仍然有资格获得会员资格，特别是那些标记为“已接受”和“Anna’s favorite”的任务。您可能想从“入门项目”开始。 轻量志愿工作 我们现在还有一个同步的Matrix频道，地址为%(matrix)s。 如果您有几个小时的空闲时间，可以通过多种方式提供帮助。请务必加入<a %(a_telegram)s>Telegram上的志愿者聊天</a>。 作为感谢，我们通常会为基本里程碑提供6个月的“幸运图书管理员”会员资格，持续的志愿工作会获得更多奖励。所有里程碑都需要高质量的工作——粗糙的工作对我们弊大于利，我们会拒绝。请在达到里程碑时<a %(a_contact)s>给我们发邮件</a>。 %(links)s 链接或已完成请求的截图。 在Z-Library或Library Genesis论坛上满足书籍（或论文等）的请求。我们没有自己的书籍请求系统，但我们会镜像这些图书馆，因此让它们变得更好也会让安娜的档案变得更好。 里程碑 任务 视任务而定。 在我们的<a %(a_telegram)s>Telegram志愿者聊天</a>中发布的小任务。通常是为了会员资格，有时是为了小额赏金。 在我们的志愿者聊天群中发布的小任务。 请确保在您解决的问题上留下评论，以免他人重复您的工作。 %(links)s 条记录链接已改进。 您可以使用<a %(a_list)s>随机metadata问题列表</a>作为起点。 通过<a %(a_metadata)s>链接</a>到Open Library来改进元数据。 这些应该显示您告诉某人关于安娜档案的信息，并且他们感谢您。 %(links)s 条链接或截图。 传播安娜档案的消息。例如，推荐AA上的书籍，链接到我们的博客文章，或一般性地引导人们访问我们的网站。 完全翻译一种语言（如果它还没有接近完成。） <a %(a_translate)s>翻译</a>网站。 链接到显示您做出重大贡献的编辑历史。 改进“安娜档案”的维基百科页面。包括来自其他语言的AA维基百科页面的信息，以及我们网站和博客中的信息。在其他相关页面上添加对AA的引用。 志愿服务与悬赏 