��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  sd   :h 1   Sj �  �j   Cl �  ao \  \r �  �t }   cv |   �v K   ^w q   �w �  x W  �z �  } �  �~ �  �� �  5� $  � �  +� t  � �  �� �  E� V   ے �  2� `   ɔ ;  *� "   f�   �� K   �� @   ޛ    � 0   <� x   m� >   �    %� >   A� .   �� G   �� (   �� g    � �  �� �  ~� u   >� \   ��    �     �    ,�    D� %   P�    v�    ��    ��    �� ,   ��    � .   � 	   � 
   '�    5� &   O� 1   v� !   �� �  ʤ �  �� �   r� 1   X� *  �� �   �� �  �� :  ^� $   �� �   �� $   �� �   �� )   �� �  ӳ $   i� [  �� 2   � �   � 
   ��   ��   ̺ �  ϼ �  a�    a� }   t� ~   ��    q� 7   r� C   �� �   �� [   �� r   � G   t�    �� �   �� �  h� z   � �   ��   q� z   �� �  	� Z   �� �  �� �   ��   �� �   �� *  K� �   v� K   E� �  ��   V� �   o� T   � �   c�    #�   ;� �  >� K   �� 
   :� %  E� {  k�    �� =  �� �   .� )  � s  1� �   ��   �� �  �� j   1� �   �� �   !� �   �� �   Y� �   �� @   �� �   �� �   �� x   B�    �� �   �� �   ��   � �   >�    � �  -� �  �� �  �� �  � �   �� X  y� �  ��   � �   � �   � �  �   b �  o 
   	 �   	 7  
   D �  \ �    �   � �   � �  � �   | �  ' }  � 8   { �   � �   6 �   *   �   � �   � �   ~ 6   @ d  w ?  �! T  # $   q% �   �% �   *& m  )' �  �( K  * �  g+ �  L. H   �0 p  1   �3 �  �7 �  y9 �  y= �  o? �   BA �   �A 
   �B '  �B �  �C V  �E �  �G �  �I �   /K 
   )L D  4L B  yN S  �P    R /  R �  MS '   U 3  HV M  |Y �   �Z    Y[ .  k[ �  �]    �_ e  �_ �  a �  �b d  Mf (  �g �   �i y   �j s   k @   tl    �l �  �l �  �n �  �p F   �r K   �r �  !s    �u �   �v �   �w �  Nx @   {    U{ S  n{ 4  �} �   �~ �   � �   F� 6   ހ �  � [  �� "    �    #� =   2� O   p� K   �� J   � w   W� �   υ "   �� I   �� m   � �   [� 3   ܇ S   � M   d� �   �� 
   P� (   [� �  �� �  .� Z  � �  k� �   � c  Ր �  9�    2�   P� >  Е �   � &  � _  -� 6  �� �  ğ �  �� K   >� T   �� 3   ߥ   � %  *� �  P� �  �   Ʈ �  � n  �� �  � F   �� �  � �  � |  ػ 
   U� t  c�   ؿ �   ��   �� i  �� 
   :� 4   E� �   z�    I� �  V� �  � �  �� �   �� T  ��    �� �  � �  �� K   2� �   ~� t  �� o  t� �   �� c  �� �   1� �  ��    h� �  �   � B  � �  a� �  N� �   %� 
   � R  #� �  v�    7�   L� �  b� �  U� o  �� �  L  7   K   G �  � a  2 �   � h   +	 �   �	 
   0
 c   ;
 2   �
 U   �
 B   ( #   k -  � K  � 5  	 �  ? L      \ �  k ^   A  z!   �# �  �% �  k) 
   M- �  X-    /    // �  M/ �  2 /  �5 �  9 
   �< 
  �< �  �? d  �B �  -F   �I }  �N S  MR ,   �T +  �T �   �U �  �V �  �[ 7  �^ �   �_   �` s  �a    Bd 4  ]d �   �e �   ,f    �f I   �f o   4g �  �g �  Dk R  n ^   Xr �  �r �   Pw 
   4x /  ?x a   oy �   �y ]  �z C   �{ �   9|    �| G  �| �  @   "� i  1� �   ��    v� �   �� �  I�    � �   &�   � 0  5� r   f� e   ْ 2  ?� 1   r� �  �� O  b� �  �� 1   �� �   Ҟ �   �� F  �� v  أ    O� �  c� 4   � !  � 1  =� �  o� ~  � x   �� 1   � �  8� O   ϯ L  �   l� h  �� �  � �   �� a   B� �   �� �  �� W  q� 4  ��   �� N   � V  U� �  �� �  c� ]  G�   �� �  %� 1   ��   � J   � �  R� �   �     �� G  �� ?  � d  R� �  �� �  u� *  d� �  �� �   �� �  i� �  G� �   � �  ��   �� U   �� �   � �   �� 1   �     �  �  �    � Q   � �   �  � �  � �  1
 E  '
   m �  �   B !   H D  j    � �  � ?   � $   �    �    � 2       F ,   b #   �    �    �    � 
   �    �     /       I C   P 
   �    �    �    � P  � w    *   �    � 8   � ;    M   C \   � (   �        '    7    K    f    � 
   �    �    �    � J   � 1   #  *   U  3   �  :   �  ?   �  Q   /! *   �! J   �! O   �!    G" t   d" Z   �"    4# x   =# �   �#    :$    V$     f$     �$ &   �$    �$    �$    %    )%    1%    J% "   W%    z%    �%    �%    �% +   �%    �%    �% 	   �% ,   �% 	   #& +   -&    Y&    _& 	   r& '   |&    �& &   �&    �& &   �&    '    $'    ,'    A'    W' ?   v'    �'    �' 3   �'    �'    	(    %( '   .( 
   V(    a(    s( �   �( �   3) �   0* ?  �*   , �   .    �. !   �.    �. 
   
/    /    &/    @/ <   R/ �   �/ z   #0 �   �0 +   91 H   e1 C   �1 �   �1 �   �2 `  83 �   �4 l   :5 .   �5    �5    �5    �5    6    6 "   36    V6    _6    {6    �6 %   �6    �6    �6 #   �6    7 !   7    A7    U7    o7    x7    �7    �7 3   �7    �7 X  8    [9    d9 	   t9     ~9    �9 ^   �9 v   : >   |: K   �: X   ;    `;    r;    z; �   ~; 
   <    ,< 6   F< �   }< '   =    C= �   ]= 9   $> �  ^> �   C �   �C    uD Z  vE ]   �F �  /G �   �H   �I G  �K (   �L 
   M �   M �   �M ~   %N _   �N �   O T   �O �   �O +   �P ;   �P    Q    !Q |   ;Q .   �Q    �Q    �Q    R $   )R �   NR    �R L   S u   SS '   �S *   �S b   T �   T "  +U !   NW    pW 	  �W 
   �X    �X    �X (   �X    �X B   �X   4Y "   7[ 
   Z[    e[    �[   �[ ,   �]    �]    �] �   �]    �^    �^ G   �^ 3   _    5_ 0   >_ �   o_ +   h`    �` ~   �` #   %a    Ia    aa '   qa K   �a �   �a    ~b P   �b �   �b �   �c u   wd    �d �  e �   �f    2g 7   Fg    ~g   �g   �h    �i }   �i �   [j ;  Uk    �l '   �l     �l �  �l ,   �o +   �o /   �o (   p 9   8p 
  rp    �q %   �q A   �q Z   r 
   br    mr     �r ?   �r g  �r �  Sv 1  �x K   | @   h|    �| )   �| �  �| o  v~   � *   � o  � �  �� +   � 7   G� �  � �  �    �    � 6   � 
   ?� �  J�    F� �  f� ~  I� i  ȑ    2� �   L� Q   � /   ^� �   �� �  � �  �   �� I  �� �   ӛ �  |� {   ��   y� �   �� v   �� (  
� F   3� -   z�    ��    �� %   ʢ 1   � .   "� e   Q� S   �� 	   � J   � �  `� Q   � Y  `� �   �� �   �� -   �� !   ��    ש    � 8   
� (   F� 4   o� +   �� &  Ъ 2   �� 7  *�    b� %  z� �   �� �   B� �  ��   �� 0   ² �   � 	   ǳ �  ѳ �   ǵ &   t� �  ��    h� &   �� )   �� *   ӹ C   ��    B�    K� ~   T�   Ӻ �  � 
   ��    �� $   ��   ý �  Ǿ   ]� �   o�    %�    C� '   a�    ��    ��    ��    �� b   �� >   I� 5  �� �   ��    D� �   b� �   � u   �� �   >� �   �� |   ��    � �   � ^   �� �   � �   �� e   x� )   �� ,  � �   5� G   �� y   � �   �� F   �    a� A   s� �   �� �   H� T   � �   j�    _� �  h� l   K� �   �� �   B�    �� �  � n  �    z� 9   ��    ��    �� �  �� :   �� |   � *  �� �   �� �   �� B  {� �   �� �   ��   f� �   o� �  � �   �� L  U�   �� N  �� 
   � 
   � 
   +� �   9� 
   � 
   � `   -� �   �� I  � 
   Z� �   h� S   4� �   �� H   >� �   �� w    � 
   �� �   �� 
   U� 
   c� 
   q�   � �   �� �  m� $   �    &�    /� �   E� .   ?� �  n� �  � �   �  .   � '   � ;   � K   + L   w X   � 3    3   Q h  � (   � �   V  � �   	 x   �	 �   B
 �  < �  � X  � 5   �   R �       � M  � "   / P  R <  � �  � �   � �  [ +    �   A! �   #" 6   �" O   %#    u# 0   �#    �#    �#    �# ^  	$    h% �   z% J   &    a&    s&    �& )   �& �   �& �   t' o   �' n  e( Y   �)    .*    D*    T* 5   l*    �* 	   �*    �* 	   �*    �* 	   �*    �* 	   �* i   �* �   e+    ,    !,    7,    L,    b,    w,    �,    �,    �,    �, 2   �, �   #-    �- <   �- �   . �   �.   �/ �   �0 �  �1 J  (3 8  s4    �6 �   �6 D   �7 �   �7 h   L8 �   �8 x  �9 U   ; �   u;    < �   < �   �< �   �=    3>     :>    [>    r>    �> ;   �>    �> +   �>    ?    ?    <? *   [?    �?    �? +   �?    �?    �?    @    @ .   @    K@ B   W@ P   �@ �   �@ �   �A 5   �B )   �B �   C �   �C �   WD T   �D @   AE ;   �E ]   �E V   F ;   sF E  �F �  �G �  �I B   GK l   �K �   �K E   �L �  
M g  �N �   +P �    Q &   �Q :  �Q �   
S �   �S G   �T �   �T �  �U @   ;W e   |W A   �W k   $X �   �X �   VY    Z    Z    (Z D   /Z �   tZ D   =[ 4   �[ ]   �[ -   \ .   C\ )   r\ [   �\ #   �\ �   ] I   �] �  �] }   �_ �   	`   �` J   �a )   �a (   b )   @b (   jb )   �b (   �b )   �b T   c P   ec �  �c }   �g K   h h   Ph N   �h    i �   i �   �i )  �j J  �k 	   m �   m !   �m    n @  &n .   go H   �o f   �o <   Fp }   �p �   q b   �q )   �q 5   r 1  Nr G   �s :   �s �   t t  �t c   v �   tv �   Ww �   �w �   ~x 0   &y T   Wy �   �y    lz �   �z Q   P{ 0  �{ C   �| h   } =   �} @   �} x  �} |   x U   � �   K� �   5� Z   ˁ �   &� �   ��    ̃ 1   �� �   � }   ��    ,� ;   E�    ��    ��    �� 1   Ӆ �   � w   �� (   � @   D� M   �� 9   Ӈ M   
� �   [� B   #� �   f� �   A� :   �� �   :� �  � ;   �� s   ��    5� g   I� U   ��    � �   � �   �� 8   Ő t   ��    s� @   �� {   ʑ    F� �   _� -   �    E� =   [� �   �� 6  Q� d   ��    �    � d   )�    �� ~   �� Z   +� %  �� �   �� !   C� g   e�   ͙ 4   � 1  � l   M� :   �� �   �� t  �� 	   �    �    �     � @   1� H   r� �   �� #   A�    e�    v� /   � f   �� ^   �    u� s   ~� m   �    `� C   w� g   �� '   #� %   K� �   q� |  � �   ��    �    ,� �   A� �  +� �   � "   �� h  ̩ �  5� Q   
� �   \� :   �� �  5� X   � �   i� -   � !   5� �  W� 6   &� �   ]� �   � �   �� �   D� 2   �� �   '� `   » 6   #� �   Z� [   � @   B� ]   �� ^   � p   @� �   ��    �� K   ��   � %  � M  B� N   �� :  �� �  � ]  �� �  �� S  �� 3   "� -   V� �   �� C   � I  �� �   
� T   ��    � /   � �  K� 
   +� A  9� �  {� �  @�   �� �   ��    o� �   �� Y   �� e   �� �   R� �   ��    d� 9   w� V   �� 9   �    B� P   a� �   �� =   M� 
   �� �   ��   "�   ��     ��    ��    �� o   � �   x� �   ��   �� �   ��     � -   2� a  `�    �� �   �� 	  �� �  �� q   5� .   ��    ��    ��    �� [   �� E   I� �   �� s  h� �   ��    b�    x� (   �� #   �� �   ��    �� P   ��    �� U   � Y   W�    ��    �� }   �� 
   ^� )   i� .   �� ,   ��    �� �   �� D  �� �   �� �   ~� �   � D  ��    �� #   �� 4  �   Q  (  V     �   � ~   [ k   � �   F �   � �   � !   = �   _    �        (    B !   Z    |    � (   �    � 
   � B   � =   8 K   v 8   � W   � >   S	 1   �	 !   �	 %   �	 R   
 %   _
    �
 q   �
 9    �   > =   � "   * !   M V   o O   �    
 �   5
 |   �
 �   ? S  �    G    e '   �    � T   � 	            < �   \ ,   = 1   j    �    �    � d   � *   F �   q '   p '   �    � )   � 2   	 1   < 3   n <   � >   � O    9   n    � �   � 3   [ '   �    � B   � �    '   � J   � A    �   M �    w   �    * 2   7    j    � *   � -   � �  � �   � !   �    � 8   �      >   $     c     u  �   �  O   ]! u  �! ,   ## 0   P# �   �# 0   g$ ,   �$ D   �$ M   
% >   X% 2   �%    �% $   �% a   
& (   o& <   �& Q   �& �  '' y   �( k   a) k   �) 0   9*   j* -   m+    �+ r   �+    ', 8   B, .   {, a   �, y   - =   �-   �-    �.    �. -   /    =/    T/    k/ 3   �/    �/ "   �/    �/ �  {0 .   ;2 B   j2 ~  �2   ,4 1   D5 !   v5    �5    �5     �5    �5    �5    6     6    56    Q6 0   `6    �6    �6    �6    �6    �6    �6 3    7 F  47 �  {8 4  :   8< �  V? E  B �  [C 
   -E �  ;E ,   �F �  G e  �H �  &J G  �L c  
N D   nP   �P O   �Q )   R o   6R {   �R �   "S �   �S )  ]T �   �U K  wV �  �W    �[ H  �[ �  �\ �   s^   _     ` w  ;` #  �a e  �c 9  =e    wf �   �f �   g 9  �g �   �h �   �i �   �j &   Tk $   {k !   �k d   �k Y   'l    �l �   �l ^   6m �   �m ?   +n 	  kn �   uo �   hp �   �p o   q l   �q �   \r �   �r �   ws �   Ft �   �t �   �u    v P   �v [   �v �   @w D   �w    (x    <x i   Kx /   �x     �x    y U   y ^   ky \   �y .   'z    Vz    cz    {z    �z �   �z �   @{ ~   �{ a   I|    �| 5   �| D   �|    .} 
   E} 
   P}    [} 
   d} 
   o}    z}    �}    �}    �}    �}    �}    �}    �}    �}    ~    -~    ?~    O~    d~ D   m~ )   �~ !   �~ �   �~    � �   �   j�    ��    ��    ��    Ɂ    Ձ    ށ 
   � �   � �   � a   ��    ��    � �   0�    ք �   � �   �� D   �� *   ʆ �   �� F  ˇ �   � (  �� �   �� +   ��   �� 8   �� 9   � u   +�   �� A   �� �   � �   �   �� �   ��    E�    b�    t�    ��    ��    ��    ˒    ے �   �� �   �� �   ��   i� d  {� �   �� x   z� �  � 2  �� d  ̝    1� �  2� �  �    ۣ T  � �  ?�   � �  9� �  7� �   � #  �� "   � _   � �   e� Y  E� k   �� �  � 
   ��    ��    �� �   · `   �� �   � G   �� �    � z   �� �   � /   û "  �    � 8   �� ^   Ͻ _  .� /   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ur
Language-Team: ur <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 زیڈ-لائبریری ایک مقبول (اور غیر قانونی) لائبریری ہے۔ انہوں نے لائبریری جینیسس کے مجموعے کو لے کر اسے آسانی سے تلاش کے قابل بنا دیا ہے۔ اس کے علاوہ، وہ نئی کتابوں کے تعاون کو ترغیب دے کر صارفین کو مختلف فوائد فراہم کر کے بہت مؤثر ہو گئے ہیں۔ وہ فی الحال ان نئی کتابوں کو لائبریری جینیسس میں واپس نہیں دیتے۔ اور لائبریری جینیسس کے برعکس، وہ اپنے مجموعے کو آسانی سے مرر نہیں بناتے، جو وسیع پیمانے پر تحفظ کو روکتا ہے۔ یہ ان کے کاروباری ماڈل کے لیے اہم ہے، کیونکہ وہ اپنے مجموعے تک بلک رسائی کے لیے پیسے وصول کرتے ہیں (روزانہ 10 سے زیادہ کتابیں)۔ ہم غیر قانونی کتابوں کے مجموعے تک بلک رسائی کے لیے پیسے وصول کرنے کے بارے میں اخلاقی فیصلے نہیں کرتے۔ اس میں کوئی شک نہیں کہ زیڈ-لائبریری نے علم تک رسائی کو بڑھانے اور مزید کتابیں حاصل کرنے میں کامیابی حاصل کی ہے۔ ہم یہاں صرف اپنا حصہ ڈالنے کے لیے ہیں: اس نجی مجموعے کے طویل مدتی تحفظ کو یقینی بنانا۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>) پائریٹ لائبریری مرر کے اصل اجراء میں (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر منتقل کر دیا گیا)، ہم نے زیڈ-لائبریری، جو کہ ایک بڑی غیر قانونی کتابوں کا مجموعہ ہے، کا مرر بنایا۔ یاد دہانی کے طور پر، یہ وہی ہے جو ہم نے اس اصل بلاگ پوسٹ میں لکھا تھا: یہ مجموعہ 2021 کے وسط تک کا تھا۔ اس دوران، زیڈ-لائبریری حیرت انگیز رفتار سے بڑھ رہی ہے: انہوں نے تقریباً 3.8 ملین نئی کتابیں شامل کی ہیں۔ وہاں کچھ نقلیں موجود ہیں، لیکن اس کا زیادہ تر حصہ بظاہر نئی کتابیں ہیں، یا پہلے سے جمع کرائی گئی کتابوں کے اعلیٰ معیار کے اسکین ہیں۔ یہ بڑی حد تک زیڈ-لائبریری میں رضاکارانہ موڈریٹرز کی بڑھتی ہوئی تعداد اور ان کے بلک اپلوڈ سسٹم کے ساتھ ڈیڈوپلیکیشن کی وجہ سے ہے۔ ہم ان کامیابیوں پر انہیں مبارکباد دینا چاہتے ہیں۔ ہم یہ اعلان کرتے ہوئے خوش ہیں کہ ہم نے زیڈ-لائبریری میں شامل کی گئی تمام کتابیں حاصل کر لی ہیں جو ہمارے آخری مرر اور اگست 2022 کے درمیان شامل کی گئی تھیں۔ ہم نے کچھ کتابیں بھی واپس جا کر حاصل کی ہیں جو ہم پہلی بار میں کھو چکے تھے۔ مجموعی طور پر، یہ نیا مجموعہ تقریباً 24TB ہے، جو پچھلے والے (7TB) سے بہت بڑا ہے۔ ہمارا مرر اب کل 31TB ہے۔ ہم نے لائبریری جینیسس کے خلاف ڈیڈوپلیکیشن کی، کیونکہ اس مجموعے کے لیے پہلے سے ہی ٹورینٹس دستیاب ہیں۔ براہ کرم نئی مجموعہ کو دیکھنے کے لیے پائریٹ لائبریری مرر پر جائیں (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر منتقل کر دیا گیا)۔ وہاں فائلوں کی ساخت کے بارے میں مزید معلومات موجود ہیں، اور پچھلی بار سے کیا تبدیلیاں آئی ہیں۔ ہم یہاں سے اس کا لنک نہیں دیں گے، کیونکہ یہ صرف ایک بلاگ ویب سائٹ ہے جو کسی غیر قانونی مواد کی میزبانی نہیں کرتی۔ یقیناً، سیڈنگ بھی ہماری مدد کرنے کا ایک بہترین طریقہ ہے۔ ان سب کا شکریہ جو ہمارے پچھلے ٹورینٹس کے سیڈنگ کر رہے ہیں۔ ہم مثبت ردعمل کے لیے شکر گزار ہیں، اور خوش ہیں کہ اتنے لوگ علم اور ثقافت کے تحفظ کی اس غیر معمولی طریقے سے پرواہ کرتے ہیں۔ پائریٹ لائبریری مرر میں 3x نئی کتابیں شامل کی گئیں (+24TB، 3.8 ملین کتابیں) TorrentFreak کے ساتھی مضامین پڑھیں: <a %(torrentfreak)s>پہلا</a>, <a %(torrentfreak_2)s>دوسرا</a> - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak کے ساتھی مضامین: <a %(torrentfreak)s>پہلا</a>, <a %(torrentfreak_2)s>دوسرا</a> کچھ عرصہ پہلے، "شیڈو لائبریریاں" ختم ہو رہی تھیں۔ Sci-Hub، جو کہ تعلیمی مقالوں کی بڑی غیر قانونی آرکائیو ہے، نے مقدمات کی وجہ سے نئے کام لینا بند کر دیا تھا۔ "زی-لائبریری"، جو کتابوں کی سب سے بڑی غیر قانونی لائبریری ہے، کے مبینہ تخلیق کاروں کو مجرمانہ کاپی رائٹ الزامات پر گرفتار کیا گیا۔ وہ حیرت انگیز طور پر اپنی گرفتاری سے بچ نکلے، لیکن ان کی لائبریری اب بھی خطرے میں ہے۔ کچھ ممالک پہلے ہی اس کا ایک ورژن کر رہے ہیں۔ TorrentFreak نے <a %(torrentfreak)s>رپورٹ</a> کیا کہ چین اور جاپان نے اپنی کاپی رائٹ قوانین میں AI استثنیات متعارف کرائی ہیں۔ ہمیں یہ واضح نہیں ہے کہ یہ بین الاقوامی معاہدوں کے ساتھ کیسے تعامل کرتا ہے، لیکن یہ یقینی طور پر ان کی ملکی کمپنیوں کو تحفظ فراہم کرتا ہے، جو کہ ہم نے جو دیکھا ہے اس کی وضاحت کرتا ہے۔ جہاں تک انا کی آرکائیو کا تعلق ہے — ہم اپنی اخلاقی یقین پر مبنی زیر زمین کام جاری رکھیں گے۔ پھر بھی ہماری سب سے بڑی خواہش یہ ہے کہ ہم روشنی میں آئیں، اور اپنے اثر کو قانونی طور پر بڑھائیں۔ براہ کرم کاپی رائٹ کی اصلاح کریں۔ جب زی-لائبریری کو بندش کا سامنا کرنا پڑا، تو میں نے پہلے ہی اس کی پوری لائبریری کا بیک اپ لے لیا تھا اور اسے رکھنے کے لیے ایک پلیٹ فارم کی تلاش میں تھا۔ یہ میری انا کی آرکائیو شروع کرنے کی تحریک تھی: ان پہلے اقدامات کے پیچھے مشن کا تسلسل۔ ہم تب سے دنیا کی سب سے بڑی شیڈو لائبریری بن چکے ہیں، جو مختلف فارمیٹس میں 140 ملین سے زیادہ کاپی رائٹ شدہ متون کی میزبانی کر رہی ہے — کتابیں، تعلیمی مقالے، میگزین، اخبارات، اور اس سے آگے۔ میری ٹیم اور میں نظریاتی ہیں۔ ہم یقین رکھتے ہیں کہ ان فائلوں کو محفوظ کرنا اور میزبانی کرنا اخلاقی طور پر درست ہے۔ دنیا بھر کی لائبریریوں کو فنڈنگ میں کٹوتی کا سامنا ہے، اور ہم انسانیت کی وراثت کو کارپوریشنز پر بھی بھروسہ نہیں کر سکتے۔ پھر AI آیا۔ تقریباً تمام بڑی کمپنیاں جو LLMs بنا رہی ہیں، نے ہمارے ڈیٹا پر تربیت کے لیے ہم سے رابطہ کیا۔ زیادہ تر (لیکن سب نہیں!) امریکی کمپنیاں نے جب ہمارے کام کی غیر قانونی نوعیت کو سمجھا تو دوبارہ غور کیا۔ اس کے برعکس، چینی کمپنیوں نے ہمارے مجموعے کو جوش و خروش سے اپنایا، بظاہر اس کی قانونی حیثیت سے بے پرواہ۔ یہ قابل ذکر ہے کیونکہ چین تقریباً تمام بڑے بین الاقوامی کاپی رائٹ معاہدوں کا دستخط کنندہ ہے۔ ہم نے تقریباً 30 کمپنیوں کو تیز رفتار رسائی دی ہے۔ ان میں سے زیادہ تر LLM کمپنیاں ہیں، اور کچھ ڈیٹا بروکرز ہیں، جو ہمارے مجموعے کو دوبارہ فروخت کریں گے۔ زیادہ تر چینی ہیں، حالانکہ ہم نے امریکہ، یورپ، روس، جنوبی کوریا، اور جاپان کی کمپنیوں کے ساتھ بھی کام کیا ہے۔ DeepSeek نے <a %(arxiv)s>اعتراف</a> کیا کہ ایک پہلے ورژن کو ہمارے مجموعے کے ایک حصے پر تربیت دی گئی تھی، حالانکہ وہ اپنے تازہ ترین ماڈل کے بارے میں خاموش ہیں (شاید وہ بھی ہمارے ڈیٹا پر تربیت یافتہ ہے)۔ اگر مغرب LLMs کی دوڑ میں آگے رہنا چاہتا ہے، اور بالآخر، AGI میں، تو اسے جلد ہی کاپی رائٹ پر اپنی پوزیشن پر نظر ثانی کرنے کی ضرورت ہے۔ چاہے آپ ہمارے اخلاقی کیس سے متفق ہوں یا نہ ہوں، یہ اب اقتصادیات کا معاملہ بن رہا ہے، اور یہاں تک کہ قومی سلامتی کا بھی۔ تمام پاور بلاکس مصنوعی سپر سائنسدان، سپر ہیکرز، اور سپر فوجیں بنا رہے ہیں۔ معلومات کی آزادی ان ممالک کے لیے بقا کا معاملہ بن رہی ہے — یہاں تک کہ قومی سلامتی کا بھی۔ ہماری ٹیم دنیا بھر سے ہے، اور ہمارا کوئی خاص جھکاؤ نہیں ہے۔ لیکن ہم ان ممالک کی حوصلہ افزائی کریں گے جن کے پاس مضبوط کاپی رائٹ قوانین ہیں کہ وہ اس وجودی خطرے کو ان کی اصلاح کے لیے استعمال کریں۔ تو کیا کرنا ہے؟ ہماری پہلی سفارش سیدھی سادی ہے: کاپی رائٹ کی مدت کو کم کریں۔ امریکہ میں، کاپی رائٹ مصنف کی موت کے 70 سال بعد دیا جاتا ہے۔ یہ مضحکہ خیز ہے۔ ہم اسے پیٹنٹس کے ساتھ ہم آہنگ کر سکتے ہیں، جو فائلنگ کے 20 سال بعد دیے جاتے ہیں۔ یہ کتابوں، مقالوں، موسیقی، فن، اور دیگر تخلیقی کاموں کے مصنفین کے لیے ان کی کوششوں کا مکمل معاوضہ حاصل کرنے کے لیے کافی وقت ہونا چاہیے (بشمول طویل مدتی منصوبے جیسے فلمی موافقت)۔ پھر، کم از کم، پالیسی سازوں کو متون کے بڑے پیمانے پر تحفظ اور تقسیم کے لیے استثنیات شامل کرنی چاہئیں۔ اگر انفرادی صارفین سے کھوئی ہوئی آمدنی بنیادی تشویش ہے، تو ذاتی سطح کی تقسیم ممنوع رہ سکتی ہے۔ اس کے بدلے میں، وہ لوگ جو وسیع ذخائر کا انتظام کرنے کی صلاحیت رکھتے ہیں — LLMs کی تربیت کرنے والی کمپنیاں، لائبریریوں اور دیگر آرکائیوز کے ساتھ — ان استثنیات کے تحت ہوں گے۔ قومی سلامتی کے لیے کاپی رائٹ اصلاحات ضروری ہیں۔ مختصر خلاصہ: چینی LLMs (جس میں DeepSeek بھی شامل ہے) میری غیر قانونی کتابوں اور مقالوں کی آرکائیو پر تربیت یافتہ ہیں — جو دنیا کی سب سے بڑی ہے۔ مغرب کو قومی سلامتی کے معاملے کے طور پر کاپی رائٹ قانون کو از سر نو ترتیب دینے کی ضرورت ہے۔ <a %(all_isbns)s>اصل بلاگ پوسٹ</a> مزید معلومات کے لیے دیکھیں۔ ہم نے اس کو بہتر بنانے کے لیے ایک چیلنج جاری کیا۔ ہم نے پہلے مقام کے لیے $6,000، دوسرے مقام کے لیے $3,000، اور تیسرے مقام کے لیے $1,000 کا انعام دینے کا اعلان کیا۔ زبردست ردعمل اور شاندار پیشکشوں کی وجہ سے، ہم نے انعامی پول کو تھوڑا سا بڑھانے کا فیصلہ کیا ہے، اور چار تیسرے مقام کے لیے $500 فی کس انعام دینے کا فیصلہ کیا ہے۔ فاتحین نیچے دیے گئے ہیں، لیکن تمام پیشکشوں کو <a %(annas_archive)s>یہاں</a> دیکھنا یقینی بنائیں، یا ہمارا <a %(a_2025_01_isbn_visualization_files)s>مشترکہ ٹورینٹ</a> ڈاؤن لوڈ کریں۔ پہلا مقام $6,000: phiresky یہ <a %(phiresky_github)s>پیشکش</a> (<a %(annas_archive_note_2951)s>Gitlab تبصرہ</a>) بالکل وہی ہے جو ہم چاہتے تھے، اور اس سے بھی زیادہ! ہمیں خاص طور پر لچکدار ویژولائزیشن آپشنز پسند آئے (یہاں تک کہ کسٹم شیڈرز کی حمایت کرتے ہوئے)، لیکن ایک جامع پریسیٹ کی فہرست کے ساتھ۔ ہمیں یہ بھی پسند آیا کہ سب کچھ کتنا تیز اور ہموار ہے، سادہ عمل درآمد (جس کا کوئی بیک اینڈ بھی نہیں ہے)، ہوشیار منی میپ، اور ان کے <a %(phiresky_github)s>بلاگ پوسٹ</a> میں وسیع وضاحت۔ شاندار کام، اور مستحق فاتح! - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ہمارے دل شکرگزاری سے بھرے ہوئے ہیں۔ قابل ذکر خیالات بلند عمارتیں نایابی کے لیے ڈیٹا سیٹس کا موازنہ کرنے کے لیے بہت سے سلائیڈرز، جیسے آپ ایک DJ ہیں۔ کتابوں کی تعداد کے ساتھ اسکیل بار۔ خوبصورت لیبلز۔ ٹھنڈی ڈیفالٹ رنگ سکیم اور ہیٹ میپ۔ منفرد نقشہ منظر اور فلٹرز تشریحات، اور براہ راست اعداد و شمار بھی براہ راست اعداد و شمار کچھ مزید خیالات اور عمل درآمد جو ہمیں خاص طور پر پسند آئے: ہم کچھ دیر کے لیے جاری رکھ سکتے ہیں، لیکن یہاں رک جاتے ہیں۔ تمام جمع کرائی گئی چیزوں کو <a %(annas_archive)s>یہاں</a> دیکھنا یقینی بنائیں، یا ہمارا <a %(a_2025_01_isbn_visualization_files)s>مشترکہ ٹورینٹ</a> ڈاؤن لوڈ کریں۔ اتنی ساری جمع کرائی گئی چیزیں، اور ہر ایک UI یا عمل درآمد میں ایک منفرد نقطہ نظر لاتی ہے۔ ہم کم از کم پہلی جگہ کی جمع کرائی گئی چیز کو اپنی مرکزی ویب سائٹ میں شامل کریں گے، اور شاید کچھ دیگر بھی۔ ہم نے نایاب ترین کتابوں کی شناخت، تصدیق، اور پھر محفوظ کرنے کے عمل کو منظم کرنے کے بارے میں سوچنا بھی شروع کر دیا ہے۔ اس محاذ پر مزید آنے والا ہے۔ تمام شرکاء کا شکریہ۔ یہ حیرت انگیز ہے کہ اتنے لوگ پرواہ کرتے ہیں۔ جلدی موازنہ کے لیے ڈیٹا سیٹس کو آسانی سے ٹوگل کرنا۔ تمام ISBNs CADAL SSNOs CERLALC ڈیٹا لیک DuXiu SSIDs EBSCOhost کی ای بک انڈیکس گوگل بکس گڈریڈز انٹرنیٹ آرکائیو ISBNdb ISBN گلوبل رجسٹر آف پبلشرز لیبی آنا کا آرکائیو میں فائلیں Nexus/STC OCLC/Worldcat اوپن لائبریری روسی ریاستی لائبریری امپیریل لائبریری آف ٹرانٹر دوسرا مقام $3,000: hypha “جبکہ کامل مربع اور مستطیل ریاضیاتی طور پر خوشگوار ہیں، وہ نقشہ سازی کے سیاق و سباق میں اعلی مقامییت فراہم نہیں کرتے۔ میرا ماننا ہے کہ ان ہلبرٹ یا کلاسک مورٹن میں موجود عدم توازن کوئی خامی نہیں بلکہ ایک خصوصیت ہے۔ جیسے اٹلی کی مشہور بوٹ کی شکل کا خاکہ اسے نقشے پر فوری طور پر پہچاننے کے قابل بناتا ہے، ان منحنی خطوط کی منفرد "خصوصیات" علمی نشانات کے طور پر کام کر سکتی ہیں۔ یہ انفرادیت مقامی یادداشت کو بڑھا سکتی ہے اور صارفین کو خود کو واقف کرنے میں مدد دے سکتی ہے، ممکنہ طور پر مخصوص علاقوں کو تلاش کرنا یا نمونوں کو نوٹ کرنا آسان بنا سکتی ہے۔” ایک اور شاندار <a %(annas_archive_note_2913)s>پیشکش</a>۔ پہلے مقام کی طرح لچکدار نہیں، لیکن ہمیں دراصل اس کی میکرو سطح کی ویژولائزیشن پہلے مقام پر پسند آئی (اسپیس فلنگ کرو، بارڈرز، لیبلنگ، ہائی لائٹنگ، پیننگ، اور زومنگ)۔ جو ڈیوس کے <a %(annas_archive_note_2971)s>تبصرہ</a> نے ہمیں متاثر کیا: اور پھر بھی ویژولائزنگ اور رینڈرنگ کے لیے بہت سارے اختیارات، نیز ایک ناقابل یقین حد تک ہموار اور بدیہی UI۔ ایک مضبوط دوسرا مقام! - انا اور ٹیم (<a %(reddit)s>Reddit</a>) کچھ مہینے پہلے ہم نے <a %(all_isbns)s>$10,000 انعام</a> کا اعلان کیا تھا تاکہ ہمارے ڈیٹا کی بہترین ممکنہ بصری نمائندگی بنائی جا سکے جو ISBN اسپیس کو دکھائے۔ ہم نے اس بات پر زور دیا کہ کون سی فائلیں ہم نے محفوظ کی ہیں/نہیں کی ہیں، اور بعد میں ایک ڈیٹا سیٹ جس میں یہ بتایا گیا کہ کتنی لائبریریاں ISBNs رکھتی ہیں (نایابی کا پیمانہ)۔ ہم جواب سے مغلوب ہو گئے ہیں۔ بہت زیادہ تخلیقی صلاحیت رہی ہے۔ ہر اس شخص کا بہت شکریہ جنہوں نے حصہ لیا: آپ کی توانائی اور جوش و خروش متعدی ہیں! آخرکار، ہم مندرجہ ذیل سوالات کا جواب دینا چاہتے تھے: <strong>دنیا میں کونسی کتابیں موجود ہیں، ہم نے اب تک کتنی محفوظ کی ہیں، اور اگلی کونسی کتابوں پر توجہ مرکوز کرنی چاہیے؟</strong> یہ دیکھ کر خوشی ہوتی ہے کہ بہت سے لوگ ان سوالات کی پرواہ کرتے ہیں۔ ہم نے خود ایک بنیادی بصری نمائندگی سے آغاز کیا۔ 300kb سے کم میں، یہ تصویر انسانی تاریخ میں اب تک جمع کی گئی سب سے بڑی مکمل طور پر کھلی "کتابوں کی فہرست" کو مختصر طور پر پیش کرتی ہے: تیسرا مقام $500 #1: maxlion اس <a %(annas_archive_note_2940)s>پیشکش</a> میں ہمیں مختلف قسم کے نظارے واقعی پسند آئے، خاص طور پر موازنہ اور پبلشر کے نظارے۔ تیسرا مقام $500 #2: abetusk اگرچہ سب سے زیادہ پالش شدہ UI نہیں ہے، یہ <a %(annas_archive_note_2917)s>پیشکش</a> بہت سے خانوں کو چیک کرتی ہے۔ ہمیں خاص طور پر اس کی موازنہ کی خصوصیت پسند آئی۔ تیسرا مقام $500 #3: conundrumer0 پہلے مقام کی طرح، یہ <a %(annas_archive_note_2975)s>پیشکش</a> اپنی لچک کے ساتھ ہمیں متاثر کرتی ہے۔ آخر کار یہی وہ چیز ہے جو ایک عظیم ویژولائزیشن ٹول بناتی ہے: پاور صارفین کے لیے زیادہ سے زیادہ لچک، جبکہ اوسط صارفین کے لیے چیزوں کو سادہ رکھنا۔ تیسرا مقام $500 #4: charelf آخری <a %(annas_archive_note_2947)s>پیشکش</a> جو انعام حاصل کرتی ہے کافی بنیادی ہے، لیکن اس میں کچھ منفرد خصوصیات ہیں جو ہمیں واقعی پسند آئیں۔ ہمیں یہ پسند آیا کہ وہ دکھاتے ہیں کہ کتنے ڈیٹا سیٹس کسی خاص ISBN کو مقبولیت/قابل اعتمادیت کے پیمانے کے طور پر کور کرتے ہیں۔ ہمیں موازنہ کے لیے اوپسیٹی سلائیڈر کے استعمال کی سادگی لیکن مؤثریت بھی واقعی پسند آئی۔ $10,000 ISBN بصری انعام کے فاتحین مختصر خلاصہ: ہمیں $10,000 ISBN بصری انعام کے لیے کچھ حیرت انگیز گذارشات موصول ہوئیں۔ پس منظر آنا کے آرکائیو کا مقصد انسانیت کے تمام علم کا بیک اپ بنانا کیسے حاصل کیا جا سکتا ہے، جب ہمیں یہ معلوم نہیں کہ کون سی کتابیں ابھی بھی موجود ہیں؟ ہمیں ایک TODO فہرست کی ضرورت ہے۔ اس کا نقشہ بنانے کا ایک طریقہ ISBN نمبروں کے ذریعے ہے، جو 1970 کی دہائی سے ہر شائع شدہ کتاب کو (زیادہ تر ممالک میں) دیے گئے ہیں۔ کوئی مرکزی اتھارٹی نہیں ہے جو تمام ISBN اسائنمنٹس کو جانتی ہو۔ اس کے بجائے، یہ ایک تقسیم شدہ نظام ہے، جہاں ممالک کو نمبروں کی رینجز دی جاتی ہیں، جو پھر بڑے پبلشرز کو چھوٹی رینجز تفویض کرتے ہیں، جو مزید چھوٹے پبلشرز کو رینجز تقسیم کر سکتے ہیں۔ آخر میں انفرادی نمبرز کتابوں کو دیے جاتے ہیں۔ ہم نے دو سال پہلے ISBNdb کی سکریپنگ کے ساتھ ISBNs کا نقشہ بنانا شروع کیا۔ تب سے، ہم نے بہت سے مزید metadata ذرائع کو سکریپ کیا ہے، جیسے <a %(blog_2)s>ورلڈ کیٹ</a>، گوگل بکس، گڈریڈز، لبی، اور مزید۔ مکمل فہرست "Datasets" اور "Torrents" صفحات پر آنا کے آرکائیو میں دیکھی جا سکتی ہے۔ اب ہمارے پاس دنیا میں سب سے بڑا مکمل طور پر کھلا، آسانی سے ڈاؤن لوڈ ہونے والا کتاب metadata (اور اس طرح ISBNs) کا مجموعہ ہے۔ ہم نے <a %(blog)s>وسیع پیمانے پر لکھا ہے</a> کہ ہم تحفظ کی پرواہ کیوں کرتے ہیں، اور ہم اس وقت ایک نازک دور میں کیوں ہیں۔ ہمیں اب نایاب، کم توجہ دی گئی، اور منفرد طور پر خطرے میں پڑی کتابوں کی شناخت کرنی چاہیے اور انہیں محفوظ کرنا چاہیے۔ دنیا کی تمام کتابوں پر اچھا metadata ہونا اس میں مدد کرتا ہے۔ $10,000 انعام استعمال کی صلاحیت اور اس کی خوبصورتی کو مضبوطی سے مدنظر رکھا جائے گا۔ زوم کرتے وقت انفرادی ISBNs کے لیے اصل metadata دکھائیں، جیسے عنوان اور مصنف۔ بہتر جگہ بھرنے والا منحنی خط۔ مثلاً ایک زگ زیگ، پہلی قطار میں 0 سے 4 تک جانا اور پھر دوسری قطار میں (الٹ میں) 5 سے 9 تک جانا — بار بار لاگو کیا گیا۔ مختلف یا حسب ضرورت رنگ سکیمیں۔ Datasets کا موازنہ کرنے کے لیے خاص نظارے۔ مسائل کو ڈیبگ کرنے کے طریقے، جیسے کہ دیگر metadata جو اچھی طرح سے متفق نہیں ہیں (مثلاً بہت مختلف عنوانات)۔ ISBNs یا رینجز پر تبصرے کے ساتھ تصاویر کی تشریح کرنا۔ نایاب یا خطرے میں پڑی کتابوں کی شناخت کے لیے کوئی بھی ہیورسٹکس۔ جو بھی تخلیقی خیالات آپ کے ذہن میں آئیں! کوڈ ان تصاویر کو بنانے کے لیے کوڈ، اور دیگر مثالیں، <a %(annas_archive)s>اس ڈائریکٹری</a> میں مل سکتی ہیں۔ ہم نے ایک جامع ڈیٹا فارمیٹ تیار کیا، جس کے ساتھ تمام مطلوبہ ISBN معلومات تقریباً 75MB (کمپریسڈ) ہیں۔ ڈیٹا فارمیٹ کی تفصیل اور اسے بنانے کے لیے کوڈ <a %(annas_archive_l1244_1319)s>یہاں</a> پایا جا سکتا ہے۔ انعام کے لیے آپ کو اسے استعمال کرنے کی ضرورت نہیں ہے، لیکن یہ شاید شروع کرنے کے لیے سب سے زیادہ آسان فارمیٹ ہے۔ آپ ہمارے metadata کو جیسے چاہیں تبدیل کر سکتے ہیں (حالانکہ آپ کا تمام کوڈ اوپن سورس ہونا چاہیے)۔ ہم یہ دیکھنے کے لیے بے تاب ہیں کہ آپ کیا تخلیق کرتے ہیں۔ نیک تمنائیں! اس ریپو کو فورک کریں، اور اس بلاگ پوسٹ HTML میں ترمیم کریں (ہمارے Flask بیک اینڈ کے علاوہ کوئی اور بیک اینڈ کی اجازت نہیں ہے)۔ اوپر دی گئی تصویر کو ہموار زوم کے قابل بنائیں، تاکہ آپ انفرادی ISBNs تک زوم کر سکیں۔ ISBNs پر کلک کرنے سے آپ کو آنا کے آرکائیو پر metadata صفحہ یا تلاش پر لے جانا چاہیے۔ آپ کو اب بھی تمام مختلف Datasets کے درمیان سوئچ کرنے کے قابل ہونا چاہیے۔ ملک کی رینجز اور پبلشر کی رینجز کو ہور پر نمایاں کیا جانا چاہیے۔ آپ مثلاً <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> ملک کی معلومات کے لیے استعمال کر سکتے ہیں، اور ہمارے "isbngrp" سکریپ کو پبلشرز کے لیے (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)۔ یہ ڈیسک ٹاپ اور موبائل پر اچھی طرح کام کرنا چاہیے۔ یہاں بہت کچھ دریافت کرنے کے لیے ہے، لہذا ہم اوپر دی گئی تصویری نمائش کو بہتر بنانے کے لیے ایک انعام کا اعلان کر رہے ہیں۔ ہمارے زیادہ تر انعامات کے برعکس، یہ وقت کی پابندی والا ہے۔ آپ کو اپنا اوپن سورس کوڈ 2025-01-31 (23:59 UTC) تک <a %(annas_archive)s>جمع کرانا</a> ہوگا۔ بہترین جمع کرائی گئی تخلیق کو $6,000 ملے گا، دوسری جگہ کو $3,000، اور تیسری جگہ کو $1,000۔ تمام انعامات Monero (XMR) کے ذریعے دیے جائیں گے۔ ذیل میں کم از کم معیار ہیں۔ اگر کوئی جمع کرائی گئی تخلیق معیار پر پورا نہیں اترتی، تو ہم پھر بھی کچھ انعامات دے سکتے ہیں، لیکن یہ ہماری صوابدید پر ہوگا۔ اضافی پوائنٹس کے لیے (یہ صرف خیالات ہیں — اپنی تخلیقی صلاحیت کو آزادانہ طور پر چلنے دیں): آپ مکمل طور پر کم از کم معیار سے ہٹ سکتے ہیں، اور مکمل طور پر مختلف بصریات بنا سکتے ہیں۔ اگر یہ واقعی شاندار ہے، تو یہ انعام کے لیے اہل ہے، لیکن یہ ہمارے صوابدید پر ہے۔ اپنی شراکتیں <a %(annas_archive)s>اس مسئلے</a> پر تبصرہ پوسٹ کرکے کریں جس میں آپ کے فورکڈ ریپو، مرج درخواست، یا فرق کا لنک ہو۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) یہ تصویر 1000×800 پکسلز کی ہے۔ ہر پکسل 2,500 ISBNs کی نمائندگی کرتا ہے۔ اگر ہمارے پاس کسی ISBN کے لیے فائل ہے، تو ہم اس پکسل کو زیادہ سبز بناتے ہیں۔ اگر ہمیں معلوم ہے کہ کوئی ISBN جاری کیا گیا ہے، لیکن ہمارے پاس کوئی مماثل فائل نہیں ہے، تو ہم اسے زیادہ سرخ بناتے ہیں۔ 300kb سے کم میں، یہ تصویر انسانی تاریخ میں اب تک جمع کی گئی سب سے بڑی مکمل طور پر کھلی "کتابوں کی فہرست" کی مختصر نمائندگی کرتی ہے (کچھ سو GB مکمل طور پر کمپریسڈ)۔ یہ بھی ظاہر کرتا ہے: کتابوں کا بیک اپ لینے میں ابھی بہت کام باقی ہے (ہمارے پاس صرف 16% ہیں)۔ تمام ISBNs کی بصری نمائندگی — $10,000 انعام 2025-01-31 تک یہ تصویر انسانی تاریخ میں اب تک جمع کی گئی سب سے بڑی مکمل طور پر کھلی "کتابوں کی فہرست" کی نمائندگی کرتی ہے۔ تصویری نمائش مجموعی تصویر کے علاوہ، ہم انفرادی Datasets کو بھی دیکھ سکتے ہیں جو ہم نے حاصل کیے ہیں۔ ان کے درمیان سوئچ کرنے کے لیے ڈراپ ڈاؤن اور بٹن استعمال کریں۔ ان تصویروں میں دیکھنے کے لیے بہت سے دلچسپ نمونے ہیں۔ کیوں کچھ لائنوں اور بلاکس کی باقاعدگی ہے، جو مختلف پیمانوں پر ہوتی نظر آتی ہے؟ خالی علاقے کیا ہیں؟ کچھ Datasets اتنے کلسٹرڈ کیوں ہیں؟ ہم ان سوالات کو قاری کے لیے ایک مشق کے طور پر چھوڑ دیں گے۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) نتیجہ اس معیار کے ساتھ، ہم ریلیز کو زیادہ تدریجی بنا سکتے ہیں، اور نئے ڈیٹا ذرائع کو زیادہ آسانی سے شامل کر سکتے ہیں۔ ہمارے پاس پہلے ہی کچھ دلچسپ ریلیز پائپ لائن میں ہیں! ہم یہ بھی امید کرتے ہیں کہ دوسرے شیڈو لائبریریوں کے لیے ہماری مجموعوں کی نقل کرنا آسان ہو جائے۔ آخر کار، ہمارا مقصد انسانی علم اور ثقافت کو ہمیشہ کے لیے محفوظ کرنا ہے، لہٰذا جتنی زیادہ تکرار ہو، اتنا ہی بہتر ہے۔ مثال آئیے ہماری حالیہ زی-لائبریری ریلیز کو ایک مثال کے طور پر دیکھتے ہیں۔ یہ دو مجموعوں پر مشتمل ہے: “<span style="background: #fffaa3">zlib3_records</span>” اور “<span style="background: #ffd6fe">zlib3_files</span>”۔ یہ ہمیں اصل کتاب کی فائلوں سے الگ میٹا ڈیٹا ریکارڈز کو سکریپ اور ریلیز کرنے کی اجازت دیتا ہے۔ اس طرح، ہم نے میٹا ڈیٹا فائلوں کے ساتھ دو ٹورینٹس جاری کیے: ہم نے بائنری ڈیٹا فولڈرز کے ساتھ بھی کئی ٹورینٹس جاری کیے، لیکن صرف “<span style="background: #ffd6fe">zlib3_files</span>” مجموعہ کے لیے، کل 62: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> کو چلا کر ہم دیکھ سکتے ہیں کہ اس کے اندر کیا ہے: اس صورت میں، یہ زی-لائبریری کی رپورٹ کردہ کتاب کا میٹا ڈیٹا ہے۔ اوپر کی سطح پر ہمارے پاس صرف “aacid” اور “metadata” ہے، لیکن کوئی “data_folder” نہیں ہے، کیونکہ اس کے مطابق کوئی بائنری ڈیٹا نہیں ہے۔ AACID میں “22430000” بطور پرائمری آئی ڈی شامل ہے، جو ہم دیکھ سکتے ہیں کہ “zlibrary_id” سے لیا گیا ہے۔ ہم توقع کر سکتے ہیں کہ اس مجموعہ میں دیگر AACs کی ساخت بھی ایسی ہی ہوگی۔ اب آئیے <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> کو چلائیں: یہ ایک بہت چھوٹا AAC میٹا ڈیٹا ہے، حالانکہ اس AAC کا بڑا حصہ کہیں اور بائنری فائل میں واقع ہے! آخر کار، اس بار ہمارے پاس ایک “data_folder” ہے، لہٰذا ہم توقع کر سکتے ہیں کہ متعلقہ بائنری ڈیٹا <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> پر واقع ہوگا۔ “metadata” میں “zlibrary_id” شامل ہے، لہٰذا ہم اسے “zlib_records” مجموعہ میں متعلقہ AAC کے ساتھ آسانی سے جوڑ سکتے ہیں۔ ہم مختلف طریقوں سے جوڑ سکتے تھے، مثلاً AACID کے ذریعے — معیار اس کی وضاحت نہیں کرتا۔ نوٹ کریں کہ “metadata” فیلڈ کا خود JSON ہونا ضروری نہیں ہے۔ یہ XML یا کسی اور ڈیٹا فارمیٹ پر مشتمل ایک سٹرنگ ہو سکتی ہے۔ آپ یہاں تک کہ متعلقہ بائنری بلاک میں میٹا ڈیٹا معلومات کو محفوظ کر سکتے ہیں، مثلاً اگر یہ بہت زیادہ ڈیٹا ہے۔ مختلف قسم کی فائلیں اور metadata، جتنا ممکن ہو اصل شکل کے قریب۔ بائنری ڈیٹا کو ویب سرورز جیسے Nginx کے ذریعے براہ راست فراہم کیا جا سکتا ہے۔ ماخذ لائبریریوں میں مختلف قسم کے شناخت کنندگان، یا شناخت کنندگان کی عدم موجودگی۔ metadata کے مقابلے میں فائل ڈیٹا کی علیحدہ ریلیز، یا صرف metadata کی ریلیز (مثلاً ہماری ISBNdb ریلیز)۔ ٹورینٹس کے ذریعے تقسیم، حالانکہ دیگر تقسیم کے طریقوں کا امکان بھی موجود ہے (مثلاً IPFS)۔ ناقابل تغیر ریکارڈز، کیونکہ ہمیں فرض کرنا چاہئے کہ ہمارے ٹورینٹس ہمیشہ کے لئے زندہ رہیں گے۔ انکریمنٹل ریلیز / قابل اضافہ ریلیز۔ مشین کے لئے قابل پڑھائی اور لکھائی، آسانی اور تیزی سے، خاص طور پر ہمارے اسٹیک کے لئے (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)۔ کچھ حد تک آسان انسانی معائنہ، حالانکہ یہ مشین کی پڑھائی کے مقابلے میں ثانوی ہے۔ ہمارے مجموعوں کو ایک معیاری کرائے کے seedbox کے ساتھ آسانی سے seed کرنا۔ ڈیزائن کے مقاصد ہمیں فائلوں کو ڈسک پر دستی طور پر نیویگیٹ کرنے میں آسانی کی پرواہ نہیں ہے، یا بغیر preprocessing کے تلاش کرنے کی۔ ہمیں موجودہ لائبریری سافٹ ویئر کے ساتھ براہ راست مطابقت کی پرواہ نہیں ہے۔ جبکہ یہ کسی کے لئے بھی ہمارے مجموعے کو ٹورینٹس کے ذریعے seed کرنا آسان ہونا چاہئے، ہم توقع نہیں کرتے کہ فائلیں بغیر اہم تکنیکی علم اور عزم کے قابل استعمال ہوں گی۔ ہمارا بنیادی استعمال کیس مختلف موجودہ مجموعوں سے فائلوں اور متعلقہ metadata کی تقسیم ہے۔ ہمارے سب سے اہم غور و فکر یہ ہیں: کچھ غیر مقاصد: چونکہ آنا کا آرکائیو اوپن سورس ہے، ہم اپنے فارمیٹ کو براہ راست استعمال کرنا چاہتے ہیں۔ جب ہم اپنی تلاش کے انڈیکس کو تازہ کرتے ہیں، تو ہم صرف عوامی طور پر دستیاب راستوں تک رسائی حاصل کرتے ہیں، تاکہ جو بھی ہماری لائبریری کو فورک کرے وہ جلدی سے کام شروع کر سکے۔ <strong>AAC.</strong> AAC (آنا کا آرکائیو کنٹینر) ایک واحد آئٹم ہے جو <strong>میٹا ڈیٹا</strong> اور اختیاری طور پر <strong>بائنری ڈیٹا</strong> پر مشتمل ہوتا ہے، دونوں ناقابل تغیر ہیں۔ اس کا ایک عالمی منفرد شناخت کنندہ ہوتا ہے، جسے <strong>AACID</strong> کہا جاتا ہے۔ <strong>AACID.</strong> AACID کا فارمیٹ یہ ہے: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>۔ مثال کے طور پر، ایک حقیقی AACID جو ہم نے جاری کیا ہے وہ ہے <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>۔ <strong>AACID رینج.</strong> چونکہ AACIDs میں یکساں طور پر بڑھتے ہوئے ٹائم اسٹیمپس شامل ہوتے ہیں، ہم اس کا استعمال کسی خاص مجموعہ کے اندر رینجز کو ظاہر کرنے کے لیے کر سکتے ہیں۔ ہم اس فارمیٹ کا استعمال کرتے ہیں: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>، جہاں ٹائم اسٹیمپس شامل ہوتے ہیں۔ یہ ISO 8601 نوٹیشن کے مطابق ہے۔ رینجز مسلسل ہوتے ہیں، اور اوورلیپ ہو سکتے ہیں، لیکن اوورلیپ کی صورت میں اس مجموعہ میں پہلے جاری کردہ کے طور پر یکساں ریکارڈز پر مشتمل ہونا چاہیے (چونکہ AACs ناقابل تغیر ہیں)۔ گمشدہ ریکارڈز کی اجازت نہیں ہے۔ <code>{collection}</code>: مجموعہ کا نام، جس میں ASCII حروف، نمبر، اور انڈر سکور شامل ہو سکتے ہیں (لیکن ڈبل انڈر سکور نہیں)۔ <code>{collection-specific ID}</code>: مجموعہ کے لیے مخصوص شناخت کنندہ، اگر قابل اطلاق ہو، مثلاً Z-Library ID۔ اسے چھوڑا یا مختصر کیا جا سکتا ہے۔ اگر AACID 150 حروف سے زیادہ ہو جائے تو اسے چھوڑنا یا مختصر کرنا ضروری ہے۔ <code>{ISO 8601 timestamp}</code>: ISO 8601 کا مختصر ورژن، ہمیشہ UTC میں، مثلاً <code>20220723T194746Z</code>۔ یہ نمبر ہر ریلیز کے لیے یکساں طور پر بڑھنا چاہیے، حالانکہ اس کے عین معنی ہر مجموعہ کے لیے مختلف ہو سکتے ہیں۔ ہم سکریپنگ یا ID بنانے کے وقت کا استعمال کرنے کی تجویز دیتے ہیں۔ <code>{shortuuid}</code>: ایک UUID لیکن ASCII میں کمپریسڈ، مثلاً base57 کا استعمال کرتے ہوئے۔ ہم فی الحال <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python لائبریری استعمال کرتے ہیں۔ <strong>بائنری ڈیٹا فولڈر.</strong> ایک فولڈر جو ایک خاص مجموعہ کے لیے AACs کی رینج کا بائنری ڈیٹا پر مشتمل ہوتا ہے۔ ان کی درج ذیل خصوصیات ہوتی ہیں: ڈائریکٹری میں مخصوص رینج کے تمام AACs کے لیے ڈیٹا فائلز شامل ہونی چاہئیں۔ ہر ڈیٹا فائل کا نام اس کا AACID ہونا چاہیے (کوئی ایکسٹینشن نہیں)۔ ڈائریکٹری کا نام ایک AACID رینج ہونا چاہیے، جس کے شروع میں <code style="color: green">annas_archive_data__</code> ہو، اور کوئی لاحقہ نہ ہو۔ مثال کے طور پر، ہماری ایک حقیقی ریلیز میں ایک ڈائریکٹری کا نام ہے<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>۔ یہ تجویز دی جاتی ہے کہ ان فولڈرز کو کچھ حد تک قابل انتظام بنایا جائے، مثلاً ہر ایک 100GB-1TB سے زیادہ نہ ہو، حالانکہ یہ سفارش وقت کے ساتھ تبدیل ہو سکتی ہے۔ <strong>مجموعہ.</strong> ہر AAC ایک مجموعہ کا حصہ ہوتا ہے، جو تعریف کے مطابق AACs کی ایک فہرست ہوتی ہے جو معنوی طور پر مستقل ہوتی ہے۔ اس کا مطلب یہ ہے کہ اگر آپ میٹا ڈیٹا کے فارمیٹ میں کوئی اہم تبدیلی کرتے ہیں، تو آپ کو ایک نیا مجموعہ بنانا ہوگا۔ معیار <strong>میٹا ڈیٹا فائل.</strong> ایک میٹا ڈیٹا فائل ایک خاص مجموعہ کے لیے AACs کی رینج کا میٹا ڈیٹا شامل کرتی ہے۔ ان کی درج ذیل خصوصیات ہوتی ہیں: <code>data_folder</code> اختیاری ہے، اور یہ بائنری ڈیٹا فولڈر کا نام ہے جو متعلقہ بائنری ڈیٹا پر مشتمل ہوتا ہے۔ اس فولڈر کے اندر متعلقہ بائنری ڈیٹا کی فائل کا نام ریکارڈ کا AACID ہوتا ہے۔ ہر JSON آبجیکٹ میں درج ذیل فیلڈز اوپر کی سطح پر شامل ہونے چاہئیں: <strong>aacid</strong>, <strong>میٹا ڈیٹا</strong>, <strong>data_folder</strong> (اختیاری)۔ کوئی اور فیلڈز کی اجازت نہیں ہے۔ فائل کا نام ایک AACID رینج ہونا چاہیے، جس کے شروع میں <code style="color: red">annas_archive_meta__</code> اور آخر میں <code>.jsonl.zstd</code> ہو۔ مثال کے طور پر، ہماری ایک ریلیز کا نام ہے<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>۔ جیسا کہ فائل ایکسٹینشن سے ظاہر ہوتا ہے، فائل کی قسم <a %(jsonlines)s>JSON Lines</a> ہے جو <a %(zstd)s>Zstandard</a> کے ساتھ کمپریسڈ ہے۔ <code>میٹا ڈیٹا</code> مجموعہ کی معنویات کے مطابق من مانی میٹا ڈیٹا ہے۔ یہ مجموعہ کے اندر معنوی طور پر مستقل ہونا چاہیے۔ <code style="color: red">annas_archive_meta__</code> کا پیش لفظ آپ کے ادارے کے نام کے مطابق ڈھالا جا سکتا ہے، مثلاً <code style="color: red">my_institute_meta__</code>۔ <strong>“ریکارڈز” اور “فائلز” مجموعے.</strong> روایت کے مطابق، “ریکارڈز” اور “فائلز” کو مختلف مجموعوں کے طور پر جاری کرنا اکثر آسان ہوتا ہے، تاکہ انہیں مختلف شیڈولز پر جاری کیا جا سکے، مثلاً سکریپنگ کی شرحوں کی بنیاد پر۔ “ریکارڈ” ایک میٹا ڈیٹا پر مبنی مجموعہ ہوتا ہے، جس میں کتابوں کے عنوانات، مصنفین، ISBNs وغیرہ جیسی معلومات شامل ہوتی ہیں، جبکہ “فائلز” وہ مجموعے ہوتے ہیں جن میں اصل فائلیں (پی ڈی ایف، ای پب) شامل ہوتی ہیں۔ آخرکار، ہم نے ایک نسبتاً سادہ معیار پر اتفاق کیا۔ یہ کافی ڈھیلا، غیر معیاری، اور ترقی پذیر ہے۔ <strong>ٹورینٹس۔</strong> میٹا ڈیٹا فائلز اور بائنری ڈیٹا فولڈرز کو ٹورینٹس میں باندھا جا سکتا ہے، ہر میٹا ڈیٹا فائل کے لیے ایک ٹورینٹ، یا ہر بائنری ڈیٹا فولڈر کے لیے ایک ٹورینٹ۔ ٹورینٹس کے فائل نام میں اصل فائل/ڈائریکٹری کا نام اور <code>.torrent</code> لاحقہ شامل ہونا چاہیے۔ <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> دنیا کی سب سے بڑی شیڈو لائبریری بن چکی ہے، اور اپنی نوعیت کی واحد شیڈو لائبریری ہے جو مکمل طور پر اوپن سورس اور اوپن ڈیٹا ہے۔ نیچے ہمارے Datasets صفحے سے ایک جدول ہے (تھوڑا ترمیم شدہ): ہم نے یہ تین طریقوں سے حاصل کیا: موجودہ اوپن ڈیٹا شیڈو لائبریریوں کی عکاسی کرنا (جیسے Sci-Hub اور Library Genesis)۔ شیڈو لائبریریوں کی مدد کرنا جو زیادہ اوپن ہونا چاہتی ہیں، لیکن ان کے پاس ایسا کرنے کے لیے وقت یا وسائل نہیں تھے (جیسے Libgen کامکس مجموعہ)۔ ایسی لائبریریوں کو سکریپ کرنا جو بلک میں شیئر نہیں کرنا چاہتیں (جیسے Z-Library)۔ (2) اور (3) کے لئے ہم اب خود ایک قابل قدر تعداد میں ٹورینٹس کا مجموعہ منظم کرتے ہیں (100s of TBs)۔ اب تک ہم نے ان مجموعوں کو ایک بار کے طور پر دیکھا ہے، یعنی ہر مجموعہ کے لئے مخصوص بنیادی ڈھانچہ اور ڈیٹا تنظیم۔ یہ ہر ریلیز میں کافی اوور ہیڈ شامل کرتا ہے، اور مزید انکریمنٹل ریلیز کرنا خاص طور پر مشکل بناتا ہے۔ اسی لئے ہم نے اپنی ریلیز کو معیاری بنانے کا فیصلہ کیا۔ یہ ایک تکنیکی بلاگ پوسٹ ہے جس میں ہم اپنا معیار متعارف کروا رہے ہیں: <strong>آنا کا آرکائیو کنٹینرز</strong>۔ آنا کا آرکائیو کنٹینرز (AAC): دنیا کی سب سے بڑی شیڈو لائبریری سے ریلیزز کو معیاری بنانا آنا کا آرکائیو دنیا کی سب سے بڑی شیڈو لائبریری بن چکی ہے، جس کی وجہ سے ہمیں اپنی ریلیزز کو معیاری بنانا پڑا۔ 300GB+ کتابوں کے کور جاری کیے گئے آخر کار، ہم ایک چھوٹے ریلیز کا اعلان کرنے پر خوش ہیں۔ Libgen.rs فورک کو چلانے والے لوگوں کے ساتھ تعاون میں، ہم ان کے تمام کتابوں کے کور کو ٹورینٹس اور IPFS کے ذریعے شیئر کر رہے ہیں۔ یہ کورز کو دیکھنے کے بوجھ کو مزید مشینوں میں تقسیم کرے گا، اور انہیں بہتر محفوظ کرے گا۔ بہت سے (لیکن سب نہیں) معاملات میں، کتابوں کے کور خود فائلوں میں شامل ہوتے ہیں، لہذا یہ ایک طرح کا "مشتق ڈیٹا" ہے۔ لیکن IPFS میں اس کا ہونا دونوں Anna’s Archive اور مختلف Library Genesis فورکس کی روزانہ کی کارروائی کے لیے اب بھی بہت مفید ہے۔ حسب معمول، آپ اس ریلیز کو Pirate Library Mirror پر پا سکتے ہیں (ایڈیٹ: <a %(wikipedia_annas_archive)s>Anna’s Archive</a> پر منتقل کر دیا گیا)۔ ہم یہاں اس کا لنک نہیں دیں گے، لیکن آپ اسے آسانی سے تلاش کر سکتے ہیں۔ امید ہے کہ ہم اپنی رفتار کو تھوڑا سا آرام دے سکتے ہیں، اب جب کہ ہمارے پاس Z-Library کا ایک معقول متبادل ہے۔ یہ کام کا بوجھ خاص طور پر پائیدار نہیں ہے۔ اگر آپ پروگرامنگ، سرور آپریشنز، یا تحفظ کے کام میں مدد کرنے میں دلچسپی رکھتے ہیں، تو ہم سے ضرور رابطہ کریں۔ ابھی بھی بہت سا <a %(annas_archive)s>کام کرنا باقی ہے</a>۔ آپ کی دلچسپی اور حمایت کا شکریہ۔ ElasticSearch پر سوئچ کریں کچھ سوالات بہت زیادہ وقت لیتے تھے، یہاں تک کہ وہ تمام کھلی کنکشنز کو روک دیتے تھے۔ بذریعہ ڈیفالٹ MySQL میں کم از کم لفظ کی لمبائی ہوتی ہے، یا آپ کا انڈیکس واقعی بڑا ہو سکتا ہے۔ لوگوں نے رپورٹ کیا کہ وہ "Ben Hur" کی تلاش نہیں کر سکتے۔ تلاش صرف اس وقت کچھ تیز تھی جب مکمل طور پر میموری میں لوڈ کی گئی تھی، جس کے لیے ہمیں اس پر چلانے کے لیے ایک زیادہ مہنگی مشین حاصل کرنی پڑی، اس کے علاوہ کچھ کمانڈز انڈیکس کو اسٹارٹ اپ پر پری لوڈ کرنے کے لیے۔ ہم اسے آسانی سے نئے فیچرز بنانے کے لیے بڑھا نہیں سکتے تھے، جیسے کہ بہتر <a %(wikipedia_cjk_characters)s>غیر سفید جگہوں والی زبانوں کے لیے ٹوکنائزیشن</a>، فلٹرنگ/فیسٹنگ، ترتیب دینا، "کیا آپ کا مطلب یہ تھا" تجاویز، آٹو کمپلیٹ، وغیرہ۔ ہمارے <a %(annas_archive)s>ٹکٹس</a> میں سے ایک ہمارے تلاش کے نظام کے مسائل کا ایک مجموعہ تھا۔ ہم نے MySQL فل ٹیکسٹ سرچ کا استعمال کیا، کیونکہ ہمارے پاس تمام ڈیٹا MySQL میں تھا۔ لیکن اس کی کچھ حدود تھیں: کئی ماہرین سے بات کرنے کے بعد، ہم نے ElasticSearch پر فیصلہ کیا۔ یہ کامل نہیں رہا (ان کی ڈیفالٹ "کیا آپ کا مطلب یہ تھا" تجاویز اور آٹو کمپلیٹ فیچرز خراب ہیں)، لیکن مجموعی طور پر یہ MySQL کے مقابلے میں تلاش کے لیے بہت بہتر رہا ہے۔ ہم ابھی بھی اسے کسی بھی مشن-کریٹیکل ڈیٹا کے لیے استعمال کرنے کے بارے میں <a %(youtube)s>زیادہ پرجوش نہیں</a> ہیں (حالانکہ انہوں نے بہت <a %(elastic_co)s>پیشرفت</a> کی ہے)، لیکن مجموعی طور پر ہم سوئچ سے کافی خوش ہیں۔ فی الحال، ہم نے بہت تیز تلاش، بہتر زبان کی حمایت، بہتر مطابقت کی ترتیب، مختلف ترتیب کے اختیارات، اور زبان/کتاب کی قسم/فائل کی قسم پر فلٹرنگ کو نافذ کیا ہے۔ اگر آپ کو یہ جاننے میں دلچسپی ہے کہ یہ کیسے کام کرتا ہے، <a %(annas_archive_l140)s>دیکھیں</a> <a %(annas_archive_l1115)s>ایک</a> <a %(annas_archive_l1635)s>نظر</a> ڈالیں۔ یہ کافی قابل رسائی ہے، حالانکہ اس میں کچھ مزید تبصرے کی ضرورت ہو سکتی ہے… اینا کا آرکائیو مکمل طور پر اوپن سورس ہے ہم یقین رکھتے ہیں کہ معلومات آزاد ہونی چاہیے، اور ہمارا اپنا کوڈ بھی اس سے مستثنیٰ نہیں ہے۔ ہم نے اپنا تمام کوڈ اپنی نجی میزبانی شدہ گٹ لیب انسٹینس پر جاری کیا ہے: <a %(annas_archive)s>اینا کا سافٹ ویئر</a>۔ ہم اپنے کام کو منظم کرنے کے لیے مسئلہ ٹریکر کا بھی استعمال کرتے ہیں۔ اگر آپ ہماری ترقی میں شامل ہونا چاہتے ہیں، تو یہ شروع کرنے کے لیے ایک بہترین جگہ ہے۔ آپ کو ان چیزوں کا ذائقہ دینے کے لیے جن پر ہم کام کر رہے ہیں، ہمارے حالیہ کلائنٹ سائیڈ کارکردگی میں بہتری کے کام کو دیکھیں۔ چونکہ ہم نے ابھی تک صفحہ بندی کو نافذ نہیں کیا ہے، ہم اکثر بہت طویل تلاش کے صفحات واپس کرتے ہیں، جن میں 100-200 نتائج ہوتے ہیں۔ ہم تلاش کے نتائج کو بہت جلدی ختم نہیں کرنا چاہتے تھے، لیکن اس کا مطلب یہ تھا کہ یہ کچھ ڈیوائسز کو سست کر دیتا۔ اس کے لیے، ہم نے ایک چھوٹی سی چال نافذ کی: ہم نے زیادہ تر تلاش کے نتائج کو HTML تبصروں (<code><!-- --></code>) میں لپیٹ دیا، اور پھر ایک چھوٹا جاوا اسکرپٹ لکھا جو پتہ لگاتا ہے کہ کب کوئی نتیجہ نظر آنا چاہیے، اس لمحے پر ہم تبصرہ کو کھول دیتے ہیں: DOM "ورچوئلائزیشن" 23 لائنوں میں نافذ کیا گیا، کسی فینسی لائبریری کی ضرورت نہیں! یہ وہ قسم کا فوری عملی کوڈ ہے جو آپ کے پاس محدود وقت میں ہوتا ہے، اور حقیقی مسائل کو حل کرنے کی ضرورت ہوتی ہے۔ یہ رپورٹ کیا گیا ہے کہ ہماری تلاش اب سست ڈیوائسز پر بھی اچھی طرح کام کرتی ہے! ایک اور بڑا کام ڈیٹا بیس کی تعمیر کو خودکار بنانا تھا۔ جب ہم نے لانچ کیا، تو ہم نے مختلف ذرائع کو بے ترتیبی سے اکٹھا کیا۔ اب ہم انہیں اپ ڈیٹ رکھنا چاہتے ہیں، اس لیے ہم نے دو Library Genesis فورکس سے نیا metadata ڈاؤن لوڈ کرنے کے لیے کئی اسکرپٹس لکھے، اور انہیں ضم کیا۔ مقصد یہ ہے کہ نہ صرف ہمارے آرکائیو کے لیے یہ مفید بنائیں، بلکہ ان لوگوں کے لیے بھی آسانی پیدا کریں جو شیڈو لائبریری metadata کے ساتھ کھیلنا چاہتے ہیں۔ مقصد یہ ہوگا کہ ایک Jupyter نوٹ بک ہو جس میں ہر قسم کا دلچسپ metadata دستیاب ہو، تاکہ ہم مزید تحقیق کر سکیں جیسے کہ یہ معلوم کرنا کہ <a %(blog)s>کتنے فیصد ISBNs ہمیشہ کے لیے محفوظ ہیں</a>۔ آخر کار، ہم نے اپنے عطیہ نظام کو دوبارہ ترتیب دیا۔ آپ اب کریڈٹ کارڈ کا استعمال کرتے ہوئے براہ راست ہمارے کرپٹو والٹس میں پیسے جمع کر سکتے ہیں، بغیر کرپٹو کرنسیوں کے بارے میں کچھ جانے۔ ہم اس بات کی نگرانی کرتے رہیں گے کہ یہ عملی طور پر کتنا اچھا کام کرتا ہے، لیکن یہ ایک بڑی بات ہے۔ زی-لائبریری کے بند ہونے اور اس کے (مبینہ) بانیوں کی گرفتاری کے ساتھ، ہم نے اینا کے آرکائیو کے ساتھ ایک اچھا متبادل فراہم کرنے کے لیے دن رات کام کیا ہے (ہم یہاں اس کا لنک نہیں دیں گے، لیکن آپ اسے گوگل کر سکتے ہیں)۔ یہاں کچھ چیزیں ہیں جو ہم نے حال ہی میں حاصل کی ہیں۔ اینا کی تازہ کاری: مکمل اوپن سورس آرکائیو، الیسٹک سرچ، 300GB+ کتابوں کے کورز ہم نے اینا کے آرکائیو کے ساتھ ایک اچھا متبادل فراہم کرنے کے لیے دن رات کام کیا ہے۔ یہاں کچھ چیزیں ہیں جو ہم نے حال ہی میں حاصل کی ہیں۔ تجزیہ سمانتک ڈپلیکیٹس (ایک ہی کتاب کے مختلف اسکین) نظریاتی طور پر فلٹر کیے جا سکتے ہیں، لیکن یہ مشکل ہے۔ جب ہم نے کامکس کو دستی طور پر دیکھا تو ہمیں بہت زیادہ غلط مثبت ملے۔ کچھ ڈپلیکیٹس صرف MD5 کے ذریعے ہیں، جو نسبتاً فضول ہے، لیکن ان کو فلٹر کرنے سے ہمیں تقریباً 1% in کی بچت ہوگی۔ اس پیمانے پر یہ اب بھی تقریباً 1TB ہے، لیکن اس پیمانے پر 1TB واقعی اہم نہیں ہے۔ ہم اس عمل میں غلطی سے ڈیٹا کو تباہ کرنے کا خطرہ مول نہیں لینا چاہتے۔ ہم نے کچھ غیر کتابی ڈیٹا پایا، جیسے کہ کامک بکس پر مبنی فلمیں۔ یہ بھی فضول لگتا ہے، کیونکہ یہ پہلے ہی دوسرے ذرائع سے وسیع پیمانے پر دستیاب ہیں۔ تاہم، ہمیں احساس ہوا کہ ہم فلمی فائلوں کو صرف فلٹر نہیں کر سکتے، کیونکہ کچھ <em>انٹرایکٹو کامک بکس</em> بھی ہیں جو کمپیوٹر پر جاری کی گئیں، جنہیں کسی نے ریکارڈ کیا اور فلموں کے طور پر محفوظ کیا۔ آخر کار، مجموعہ سے جو کچھ بھی ہم حذف کر سکتے تھے وہ صرف چند فیصد کی بچت کرے گا۔ پھر ہمیں یاد آیا کہ ہم ڈیٹا جمع کرنے والے ہیں، اور جو لوگ اس کی نقل کریں گے وہ بھی ڈیٹا جمع کرنے والے ہیں، اور اس لیے، "آپ کا کیا مطلب ہے، حذف کریں؟!" :) جب آپ کے اسٹوریج کلسٹر میں 95TB ڈمپ کیا جاتا ہے، تو آپ یہ سمجھنے کی کوشش کرتے ہیں کہ اس میں کیا ہے… ہم نے کچھ تجزیہ کیا تاکہ یہ دیکھ سکیں کہ آیا ہم سائز کو تھوڑا سا کم کر سکتے ہیں، جیسے کہ ڈپلیکیٹس کو ہٹا کر۔ یہاں کچھ ہمارے نتائج ہیں: لہذا، ہم آپ کو مکمل، غیر ترمیم شدہ مجموعہ پیش کر رہے ہیں۔ یہ بہت زیادہ ڈیٹا ہے، لیکن ہمیں امید ہے کہ کافی لوگ اسے سیڈ کرنے کی پرواہ کریں گے۔ تعاون اس کے سائز کے پیش نظر، یہ مجموعہ طویل عرصے سے ہماری خواہش کی فہرست میں تھا، لہذا Z-Library کا بیک اپ لینے میں ہماری کامیابی کے بعد، ہم نے اس مجموعے پر اپنی نظریں جما لیں۔ شروع میں ہم نے اسے براہ راست سکریپ کیا، جو کہ ایک بڑا چیلنج تھا، کیونکہ ان کا سرور بہترین حالت میں نہیں تھا۔ اس طرح ہمیں تقریباً 15TB ملا، لیکن یہ سست رفتار تھا۔ خوش قسمتی سے، ہم لائبریری کے آپریٹر سے رابطہ کرنے میں کامیاب ہو گئے، جنہوں نے ہمیں تمام ڈیٹا براہ راست بھیجنے پر رضامندی ظاہر کی، جو کہ بہت تیز تھا۔ پھر بھی، تمام ڈیٹا کو منتقل اور پروسیس کرنے میں آدھے سال سے زیادہ کا وقت لگا، اور ہم تقریباً اسے ڈسک کرپشن کی وجہ سے کھو بیٹھے، جس کا مطلب ہوتا کہ ہمیں دوبارہ شروع کرنا پڑتا۔ اس تجربے نے ہمیں یقین دلایا ہے کہ اس ڈیٹا کو جلد از جلد باہر نکالنا ضروری ہے، تاکہ اسے دور دور تک عکس بند کیا جا سکے۔ ہم اس مجموعے کو ہمیشہ کے لیے کھونے سے صرف ایک یا دو بدقسمت واقعات دور ہیں! مجموعہ تیزی سے کام کرنے کا مطلب یہ ہے کہ مجموعہ کچھ غیر منظم ہو سکتا ہے… آئیے دیکھتے ہیں۔ تصور کریں کہ ہمارے پاس ایک فائل سسٹم ہے (جسے حقیقت میں ہم ٹورینٹس میں تقسیم کر رہے ہیں): پہلی ڈائریکٹری، <code>/repository</code>، اس کا زیادہ منظم حصہ ہے۔ اس ڈائریکٹری میں نام نہاد "ہزار ڈائریکٹریز" شامل ہیں: ہر ایک ڈائریکٹری میں ہزاروں فائلیں ہیں، جو ڈیٹا بیس میں بتدریج نمبر دی گئی ہیں۔ ڈائریکٹری <code>0</code> میں 0–999 کے comic_id والی فائلیں شامل ہیں، اور اسی طرح۔ یہی اسکیم Library Genesis اپنی فکشن اور نان فکشن مجموعوں کے لیے استعمال کر رہا ہے۔ خیال یہ ہے کہ ہر "ہزار ڈائریکٹری" کو خود بخود ٹورینٹ میں تبدیل کر دیا جائے جب یہ بھر جائے۔ تاہم، Libgen.li آپریٹر نے اس مجموعے کے لیے کبھی ٹورینٹس نہیں بنائے، اور اس طرح ہزاروں ڈائریکٹریز ممکنہ طور پر غیر آرام دہ ہو گئیں، اور "غیر منظم ڈائریکٹریز" کی شکل اختیار کر گئیں۔ یہ <code>/comics0</code> سے <code>/comics4</code> تک ہیں۔ ان سب میں منفرد ڈائریکٹری ڈھانچے ہیں، جو شاید فائلوں کو جمع کرنے کے لیے معنی خیز تھے، لیکن اب ہمارے لیے زیادہ معنی نہیں رکھتے۔ خوش قسمتی سے، metadata اب بھی براہ راست ان تمام فائلوں کا حوالہ دیتا ہے، لہذا ان کی ڈسک پر ذخیرہ تنظیم واقعی اہم نہیں ہے! metadata MySQL ڈیٹا بیس کی شکل میں دستیاب ہے۔ یہ براہ راست Libgen.li ویب سائٹ سے ڈاؤن لوڈ کیا جا سکتا ہے، لیکن ہم اسے اپنے ٹیبل کے ساتھ ایک ٹورینٹ میں بھی دستیاب کریں گے جس میں تمام MD5 ہیشز شامل ہیں۔ <q>ڈاکٹر باربرا گورڈن خود کو لائبریری کی عام دنیا میں کھونے کی کوشش کرتی ہیں…</q> Libgen فورکس پہلے، کچھ پس منظر۔ آپ شاید Library Genesis کو ان کے شاندار کتابی مجموعے کے لیے جانتے ہوں۔ کم لوگ جانتے ہیں کہ Library Genesis کے رضاکاروں نے دیگر منصوبے بھی بنائے ہیں، جیسے کہ رسائل اور معیاری دستاویزات کا ایک بڑا مجموعہ، Sci-Hub کا مکمل بیک اپ (Sci-Hub کی بانی، الیگزینڈرا الباکیان کے تعاون سے)، اور واقعی، کامکس کا ایک بڑا مجموعہ۔ کسی وقت Library Genesis کے مختلف آئینے کے آپریٹرز نے اپنے الگ راستے اختیار کیے، جس کی وجہ سے موجودہ صورتحال پیدا ہوئی کہ مختلف "فورکس" موجود ہیں، جو اب بھی Library Genesis کا نام رکھتے ہیں۔ Libgen.li فورک کے پاس خاص طور پر یہ کامکس مجموعہ ہے، نیز ایک بڑا رسائل کا مجموعہ (جس پر ہم بھی کام کر رہے ہیں)۔ فنڈ ریزر ہم اس ڈیٹا کو کچھ بڑے حصوں میں جاری کر رہے ہیں۔ پہلا ٹورینٹ <code>/comics0</code> کا ہے، جسے ہم نے ایک بڑے 12TB .tar فائل میں رکھا ہے۔ یہ آپ کی ہارڈ ڈرائیو اور ٹورینٹ سافٹ ویئر کے لیے ہزاروں چھوٹی فائلوں سے بہتر ہے۔ اس ریلیز کے حصے کے طور پر، ہم ایک فنڈ ریزر کر رہے ہیں۔ ہم اس مجموعے کے آپریشنل اور کنٹریکٹنگ اخراجات کو پورا کرنے کے لیے $20,000 جمع کرنے کی کوشش کر رہے ہیں، نیز جاری اور مستقبل کے منصوبوں کو فعال کرنے کے لیے۔ ہمارے پاس کچھ <em>بڑے</em> منصوبے ہیں۔ <em>میں اپنی عطیہ سے کس کی مدد کر رہا ہوں؟</em> مختصر میں: ہم انسانیت کے تمام علم اور ثقافت کا بیک اپ لے رہے ہیں، اور اسے آسانی سے قابل رسائی بنا رہے ہیں۔ ہمارا تمام کوڈ اور ڈیٹا اوپن سورس ہے، ہم مکمل طور پر رضاکارانہ طور پر چلنے والا منصوبہ ہیں، اور ہم نے اب تک 125TB کتابوں کو محفوظ کیا ہے (Libgen اور Scihub کے موجودہ ٹورینٹس کے علاوہ)۔ آخر کار ہم ایک فلائی وہیل بنا رہے ہیں جو لوگوں کو دنیا کی تمام کتابیں تلاش کرنے، اسکین کرنے، اور بیک اپ کرنے کی ترغیب دیتا ہے۔ ہم اپنے ماسٹر پلان کے بارے میں ایک مستقبل کی پوسٹ میں لکھیں گے۔ :) اگر آپ 12 ماہ کی "Amazing Archivist" رکنیت ($780) کے لیے عطیہ کرتے ہیں، تو آپ کو <strong>“ایک ٹورینٹ کو اپنانے”</strong> کا موقع ملتا ہے، یعنی ہم آپ کے صارف نام یا پیغام کو ٹورینٹس میں سے ایک کے فائل نام میں شامل کریں گے! آپ <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر جا کر اور "عطیہ کریں" بٹن پر کلک کر کے عطیہ کر سکتے ہیں۔ ہم مزید رضاکاروں کی تلاش میں بھی ہیں: سافٹ ویئر انجینئرز، سیکیورٹی محققین، گمنام مرچنٹ ماہرین، اور مترجمین۔ آپ ہمیں ہوسٹنگ سروسز فراہم کر کے بھی سپورٹ کر سکتے ہیں۔ اور یقیناً، براہ کرم ہمارے ٹورینٹس کو سیڈ کریں! ان سب کا شکریہ جنہوں نے ہمیں پہلے ہی اتنی فراخدلی سے سپورٹ کیا ہے! آپ واقعی فرق پیدا کر رہے ہیں۔ یہاں اب تک جاری کردہ ٹورینٹس ہیں (ہم ابھی باقی کو پروسیس کر رہے ہیں): تمام ٹورینٹس <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر "Datasets" کے تحت مل سکتے ہیں (ہم وہاں براہ راست لنک نہیں دیتے، تاکہ اس بلاگ کے لنکس Reddit، Twitter، وغیرہ سے ہٹائے نہ جائیں)۔ وہاں سے، Tor ویب سائٹ کے لنک کی پیروی کریں۔ <a %(news_ycombinator)s>Hacker News پر تبصرہ کریں</a> آگے کیا ہے؟ طویل مدتی تحفظ کے لیے ٹورینٹس کا ایک مجموعہ بہترین ہے، لیکن روزمرہ کی رسائی کے لیے اتنا نہیں۔ ہم اس تمام ڈیٹا کو ویب پر لانے کے لیے ہوسٹنگ پارٹنرز کے ساتھ کام کریں گے (چونکہ انا کا آرکائیو براہ راست کچھ بھی ہوسٹ نہیں کرتا)۔ یقیناً آپ انا کے آرکائیو پر یہ ڈاؤن لوڈ لنکس تلاش کر سکیں گے۔ ہم ہر کسی کو اس ڈیٹا کے ساتھ کچھ کرنے کی دعوت بھی دے رہے ہیں! ہماری مدد کریں کہ اسے بہتر تجزیہ کریں، ڈپلیکیٹ کو ہٹائیں، اسے IPFS پر ڈالیں، اسے دوبارہ مکس کریں، اپنے AI ماڈلز کو اس کے ساتھ تربیت دیں، وغیرہ۔ یہ سب آپ کا ہے، اور ہم بے صبری سے دیکھنے کے منتظر ہیں کہ آپ اس کے ساتھ کیا کرتے ہیں۔ آخر کار، جیسا کہ پہلے کہا گیا، ہمارے پاس ابھی بھی کچھ بڑے ریلیز آنے والے ہیں (اگر <em>کوئی</em> ہمیں <em>غلطی سے</em> ایک <em>خاص</em> ACS4 ڈیٹا بیس کا ڈمپ بھیج دے، تو آپ جانتے ہیں کہ ہمیں کہاں تلاش کرنا ہے…) اور دنیا کی تمام کتابوں کا بیک اپ لینے کے لیے فلائی وہیل بنانا۔ تو جڑے رہیں، ہم ابھی شروعات کر رہے ہیں۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) کامک بکس کی سب سے بڑی شیڈو لائبریری ممکنہ طور پر ایک خاص Library Genesis فورک کی ہے: Libgen.li۔ اس سائٹ کو چلانے والے ایک ایڈمنسٹریٹر نے 2 ملین سے زیادہ فائلوں کا ایک پاگل کامکس مجموعہ جمع کیا، جو 95TB سے زیادہ ہے۔ تاہم، دیگر Library Genesis مجموعوں کے برعکس، یہ ایک ساتھ ٹورینٹس کے ذریعے دستیاب نہیں تھا۔ آپ ان کامکس کو صرف اس کے سست ذاتی سرور کے ذریعے انفرادی طور پر رسائی حاصل کر سکتے تھے — ایک واحد ناکامی کا نقطہ۔ آج تک! اس پوسٹ میں ہم آپ کو اس مجموعے کے بارے میں مزید بتائیں گے، اور اس کام کی مزید حمایت کے لیے ہمارے فنڈ ریزر کے بارے میں بھی معلومات فراہم کریں گے۔ انا کا آرکائیو نے دنیا کی سب سے بڑی کامکس شیڈو لائبریری (95TB) کا بیک اپ لیا ہے — آپ اسے سیڈ کرنے میں مدد کر سکتے ہیں دنیا کی سب سے بڑی کامک بکس شیڈو لائبریری میں ایک واحد ناکامی کا نقطہ تھا.. آج تک۔ انتباہ: اس بلاگ پوسٹ کو منسوخ کر دیا گیا ہے۔ ہم نے فیصلہ کیا ہے کہ IPFS ابھی تک پرائم ٹائم کے لیے تیار نہیں ہے۔ ہم اب بھی اینا کے آرکائیو سے IPFS پر فائلوں سے لنک کریں گے جب ممکن ہو، لیکن ہم اسے خود مزید میزبانی نہیں کریں گے، اور نہ ہی ہم دوسروں کو IPFS کا استعمال کرتے ہوئے آئینہ بنانے کی سفارش کرتے ہیں۔ اگر آپ ہماری کلیکشن کو محفوظ رکھنے میں مدد کرنا چاہتے ہیں تو براہ کرم ہمارے ٹورینٹس صفحہ کو دیکھیں۔ 5,998,794 کتابیں آئی پی ایف ایس پر ڈالنا نقلوں کی کثرت واپس ہمارے اصل سوال کی طرف: ہم اپنی مجموعوں کو ہمیشہ کے لیے محفوظ رکھنے کا دعویٰ کیسے کر سکتے ہیں؟ یہاں بنیادی مسئلہ یہ ہے کہ ہماری مجموعہ تیزی سے <a %(torrents_stats)s>بڑھ رہی ہے</a>، کچھ بڑے مجموعوں کو اسکریپنگ اور اوپن سورسنگ کے ذریعے (ان حیرت انگیز کاموں کے علاوہ جو پہلے ہی دیگر اوپن ڈیٹا شیڈو لائبریریوں جیسے Sci-Hub اور Library Genesis نے کیے ہیں)۔ ڈیٹا میں یہ اضافہ دنیا بھر میں مجموعوں کو عکس بند کرنا مشکل بنا دیتا ہے۔ ڈیٹا اسٹوریج مہنگا ہے! لیکن ہم پر امید ہیں، خاص طور پر جب ہم درج ذیل تین رجحانات کا مشاہدہ کرتے ہیں۔ پچھلے چند مہینوں میں، ہمارے مجموعات کا <a %(annas_archive_stats)s>کل سائز</a>، ٹورینٹ سیڈرز کی تعداد کے حساب سے تقسیم کیا گیا۔ مختلف ذرائع سے ایچ ڈی ڈی قیمت کے رجحانات (مطالعہ دیکھنے کے لیے کلک کریں)۔ <a %(critical_window_chinese)s>چینی ورژن 中文版</a>, <a %(reddit)s>Reddit</a> پر بحث کریں، <a %(news_ycombinator)s>Hacker News</a> 1. ہم نے آسان مواقع کو چن لیا ہے یہ براہ راست ہماری اوپر بیان کردہ ترجیحات سے متعلق ہے۔ ہم پہلے بڑے مجموعوں کو آزاد کرنے پر کام کرنا پسند کرتے ہیں۔ اب جب کہ ہم نے دنیا کے کچھ بڑے مجموعوں کو محفوظ کر لیا ہے، ہم توقع کرتے ہیں کہ ہماری ترقی بہت سست ہو جائے گی۔ اب بھی چھوٹے مجموعوں کی ایک لمبی قطار ہے، اور ہر روز نئی کتابیں اسکین یا شائع ہوتی ہیں، لیکن شرح ممکنہ طور پر بہت سست ہوگی۔ ہم اب بھی سائز میں دوگنا یا تین گنا ہو سکتے ہیں، لیکن ایک طویل مدت میں۔ او سی آر میں بہتری۔ ترجیحات سائنس اور انجینئرنگ سافٹ ویئر کوڈ مندرجہ بالا تمام کی افسانوی یا تفریحی ورژنز جغرافیائی ڈیٹا (مثلاً نقشے، ارضیاتی سروے) کارپوریشنز یا حکومتوں سے داخلی ڈیٹا (لیک) پیمائش کے ڈیٹا جیسے سائنسی پیمائش، اقتصادی ڈیٹا، کارپوریٹ رپورٹس عمومی طور پر میٹا ڈیٹا ریکارڈز (غیر افسانوی اور افسانوی؛ دیگر میڈیا، آرٹ، لوگ وغیرہ؛ بشمول جائزے) غیر افسانوی کتابیں غیر افسانوی میگزین، اخبارات، ہدایت نامے غیر افسانوی تقاریر، دستاویزی فلمیں، پوڈکاسٹس کے ٹرانسکرپٹس نامیاتی ڈیٹا جیسے ڈی این اے سیکوینسز، پودوں کے بیج، یا مائکروبیل نمونے اکیڈمک پیپرز، جرنلز، رپورٹس سائنس اور انجینئرنگ ویب سائٹس، آن لائن مباحثے قانونی یا عدالتی کارروائیوں کے ٹرانسکرپٹس تباہی کے خطرے میں منفرد (مثلاً جنگ، فنڈنگ کی کٹوتی، مقدمات، یا سیاسی ظلم و ستم کے ذریعے) نایاب منفرد طور پر غیر مرکوز ہم کاغذات اور کتابوں کی اتنی پرواہ کیوں کرتے ہیں؟ آئیے عمومی طور پر تحفظ کے بارے میں ہمارے بنیادی عقیدے کو ایک طرف رکھیں — ہم اس پر ایک اور پوسٹ لکھ سکتے ہیں۔ تو خاص طور پر کاغذات اور کتابیں کیوں؟ جواب سادہ ہے: <strong>معلومات کی کثافت</strong>۔ اسٹوریج کے فی میگابائٹ، تحریری متن تمام میڈیا میں سب سے زیادہ معلومات ذخیرہ کرتا ہے۔ جبکہ ہم علم اور ثقافت دونوں کی پرواہ کرتے ہیں، ہم پہلے کی زیادہ پرواہ کرتے ہیں۔ مجموعی طور پر، ہم معلومات کی کثافت اور تحفظ کی اہمیت کی ایک درجہ بندی پاتے ہیں جو کچھ اس طرح نظر آتی ہے: اس فہرست میں درجہ بندی کچھ حد تک من مانی ہے — کئی اشیاء میں برابری یا ہماری ٹیم کے اندر اختلافات ہیں — اور ہم شاید کچھ اہم زمروں کو بھول رہے ہیں۔ لیکن یہ تقریباً ہماری ترجیحات کی عکاسی کرتا ہے۔ ان میں سے کچھ اشیاء اتنی مختلف ہیں کہ ہمیں ان کی فکر کرنے کی ضرورت نہیں (یا پہلے ہی دیگر اداروں کے ذریعے دیکھ بھال کی جا رہی ہیں)، جیسے کہ نامیاتی ڈیٹا یا جغرافیائی ڈیٹا۔ لیکن اس فہرست میں زیادہ تر اشیاء دراصل ہمارے لیے اہم ہیں۔ ہماری ترجیح میں ایک اور بڑا عنصر یہ ہے کہ کسی خاص کام کو کتنا خطرہ ہے۔ ہم ان کاموں پر توجہ مرکوز کرنا پسند کرتے ہیں جو: آخر میں، ہم پیمانے کی پرواہ کرتے ہیں۔ ہمارے پاس محدود وقت اور پیسہ ہے، اس لیے ہم ایک مہینہ 10,000 کتابیں بچانے میں گزارنا پسند کریں گے بجائے 1,000 کتابوں کے — اگر وہ تقریباً یکساں قیمتی اور خطرے میں ہوں۔ <em><q>گمشدہ کو بازیافت نہیں کیا جا سکتا؛ لیکن آئیے جو باقی ہے اسے بچائیں: نہ کہ تہہ خانوں اور تالوں کے ذریعے جو انہیں عوام کی نظر اور استعمال سے بچاتے ہیں، وقت کے ضیاع کے حوالے کرتے ہوئے، بلکہ ایسی نقلوں کی کثرت کے ذریعے، جو انہیں حادثے کی پہنچ سے باہر رکھے۔</q></em><br>— تھامس جیفرسن، 1791 شیڈو لائبریریاں کوڈ گٹ ہب پر اوپن سورس ہو سکتا ہے، لیکن گٹ ہب کو مجموعی طور پر آسانی سے عکس بند نہیں کیا جا سکتا اور اس طرح محفوظ نہیں کیا جا سکتا (حالانکہ اس خاص معاملے میں زیادہ تر کوڈ ریپوزٹریز کی کافی تقسیم شدہ کاپیاں موجود ہیں) میٹا ڈیٹا ریکارڈز کو ورلڈ کیٹ ویب سائٹ پر آزادانہ طور پر دیکھا جا سکتا ہے، لیکن انہیں بڑی تعداد میں ڈاؤن لوڈ نہیں کیا جا سکتا (جب تک کہ ہم نے انہیں <a %(worldcat_scrape)s>اسکریپ</a> نہیں کیا) ریڈٹ کا استعمال مفت ہے، لیکن حال ہی میں ڈیٹا کے بھوکے LLM ٹریننگ کے بعد سخت اینٹی اسکریپنگ اقدامات کیے گئے ہیں (اس کے بارے میں بعد میں مزید) بہت سی تنظیمیں ہیں جن کے مشن اور ترجیحات ملتی جلتی ہیں۔ درحقیقت، ایسی لائبریریاں، آرکائیوز، لیبز، میوزیم، اور دیگر ادارے ہیں جو اس قسم کے تحفظ کے ذمہ دار ہیں۔ ان میں سے بہت سے حکومتوں، افراد، یا کارپوریشنز کے ذریعہ اچھی طرح سے مالی اعانت فراہم کی جاتی ہے۔ لیکن ان کے پاس ایک بڑا اندھا مقام ہے: قانونی نظام۔ یہاں شیڈو لائبریریوں کا منفرد کردار ہے، اور اینا کے آرکائیو کے وجود کی وجہ۔ ہم وہ کام کر سکتے ہیں جو دوسرے ادارے کرنے کی اجازت نہیں رکھتے۔ اب، یہ (اکثر) نہیں ہے کہ ہم ایسے مواد کو محفوظ کر سکتے ہیں جو کہیں اور محفوظ کرنا غیر قانونی ہے۔ نہیں، یہ بہت سی جگہوں پر قانونی ہے کہ کسی بھی کتاب، کاغذات، میگزین وغیرہ کے ساتھ ایک آرکائیو بنایا جائے۔ لیکن جو چیز قانونی آرکائیوز میں اکثر کمی ہوتی ہے وہ ہے <strong>اضافی اور طویل مدتی بقا</strong>۔ ایسی کتابیں موجود ہیں جن کی صرف ایک کاپی کسی جسمانی لائبریری میں کہیں موجود ہے۔ ایسے میٹا ڈیٹا ریکارڈز موجود ہیں جو ایک ہی کارپوریشن کے زیر حفاظت ہیں۔ ایسے اخبارات موجود ہیں جو صرف مائیکروفلم پر ایک ہی آرکائیو میں محفوظ ہیں۔ لائبریریوں کی فنڈنگ کٹ سکتی ہے، کارپوریشنز دیوالیہ ہو سکتی ہیں، آرکائیوز بمباری اور جلائے جا سکتے ہیں۔ یہ مفروضہ نہیں ہے — یہ ہر وقت ہوتا ہے۔ اینا کے آرکائیو میں ہم جو منفرد کام کر سکتے ہیں وہ ہے کاموں کی کئی کاپیاں ذخیرہ کرنا، بڑے پیمانے پر۔ ہم کاغذات، کتابیں، میگزین، اور مزید جمع کر سکتے ہیں، اور انہیں بڑی تعداد میں تقسیم کر سکتے ہیں۔ ہم فی الحال یہ ٹورینٹس کے ذریعے کرتے ہیں، لیکن اصل ٹیکنالوجیز اہم نہیں ہیں اور وقت کے ساتھ بدل جائیں گی۔ اہم حصہ یہ ہے کہ دنیا بھر میں کئی کاپیاں تقسیم کی جائیں۔ یہ قول جو 200 سال پہلے کہا گیا تھا، اب بھی سچ ہے: عوامی ڈومین کے بارے میں ایک مختصر نوٹ۔ چونکہ انا کا آرکائیو منفرد طور پر ان سرگرمیوں پر توجہ مرکوز کرتا ہے جو دنیا کے بہت سے مقامات پر غیر قانونی ہیں، ہم عوامی ڈومین کتابوں جیسے وسیع پیمانے پر دستیاب مجموعوں کی پرواہ نہیں کرتے۔ قانونی ادارے اکثر اس کا اچھی طرح خیال رکھتے ہیں۔ تاہم، کچھ غور و فکر ہیں جو ہمیں بعض اوقات عوامی طور پر دستیاب مجموعوں پر کام کرنے پر مجبور کرتے ہیں: - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. اسٹوریج کی قیمتیں مسلسل تیزی سے کم ہو رہی ہیں 3. معلومات کی کثافت میں بہتری ہم فی الحال کتابوں کو ان کے دیے گئے خام فارمیٹس میں محفوظ کرتے ہیں۔ یقیناً، وہ کمپریسڈ ہوتی ہیں، لیکن اکثر وہ اب بھی صفحات کے بڑے اسکین یا تصاویر ہوتی ہیں۔ اب تک، ہمارے مجموعے کے کل سائز کو کم کرنے کے لیے واحد اختیارات زیادہ جارحانہ کمپریشن یا ڈیڈوپلیکیشن رہے ہیں۔ تاہم، خاطر خواہ بچت حاصل کرنے کے لیے، دونوں ہمارے ذوق کے لیے بہت زیادہ نقصان دہ ہیں۔ تصاویر کی بھاری کمپریشن متن کو بمشکل پڑھنے کے قابل بنا سکتی ہے۔ اور ڈیڈوپلیکیشن کے لیے کتابوں کے بالکل ایک جیسے ہونے کا اعلیٰ اعتماد درکار ہوتا ہے، جو اکثر بہت زیادہ غیر درست ہوتا ہے، خاص طور پر اگر مواد ایک جیسا ہو لیکن اسکین مختلف مواقع پر کیے گئے ہوں۔ ہمیشہ ایک تیسرا آپشن موجود رہا ہے، لیکن اس کا معیار اتنا خراب تھا کہ ہم نے کبھی اس پر غور نہیں کیا: <strong>او سی آر، یا آپٹیکل کریکٹر ریکگنیشن</strong>۔ یہ تصاویر کو سادہ متن میں تبدیل کرنے کا عمل ہے، جس میں AI کا استعمال کرتے ہوئے تصاویر میں حروف کی شناخت کی جاتی ہے۔ اس کے لیے ٹولز طویل عرصے سے موجود ہیں، اور کافی اچھے رہے ہیں، لیکن "کافی اچھا" تحفظ کے مقاصد کے لیے کافی نہیں ہے۔ تاہم، حالیہ ملٹی موڈل ڈیپ لرننگ ماڈلز نے انتہائی تیزی سے ترقی کی ہے، حالانکہ اب بھی زیادہ لاگت پر۔ ہم توقع کرتے ہیں کہ آنے والے سالوں میں درستگی اور لاگت دونوں میں ڈرامائی بہتری آئے گی، یہاں تک کہ یہ ہماری پوری لائبریری پر لاگو کرنے کے لیے حقیقت پسندانہ ہو جائے گا۔ جب ایسا ہوگا، تو ہم ممکنہ طور پر اصل فائلوں کو محفوظ رکھیں گے، لیکن اس کے علاوہ ہمارے پاس اپنی لائبریری کا ایک بہت چھوٹا ورژن ہو سکتا ہے جسے زیادہ تر لوگ آئینہ بنانا چاہیں گے۔ اہم بات یہ ہے کہ خام متن خود بھی بہتر کمپریس ہوتا ہے، اور ڈیڈوپلیکیشن کرنا بھی بہت آسان ہوتا ہے، جس سے ہمیں مزید بچت ملتی ہے۔ مجموعی طور پر یہ غیر حقیقی نہیں ہے کہ کل فائل سائز میں کم از کم 5-10 گنا کمی کی توقع کی جائے، شاید اس سے بھی زیادہ۔ یہاں تک کہ ایک محتاط 5 گنا کمی کے ساتھ، ہم <strong>$1,000–$3,000 کی 10 سال میں دیکھ رہے ہوں گے، یہاں تک کہ اگر ہماری لائبریری کا سائز تین گنا ہو جائے</strong>۔ تحریر کے وقت، <a %(diskprices)s>ڈسک کی قیمتیں</a> فی ٹی بی تقریباً $12 نئی ڈسکوں کے لیے، $8 استعمال شدہ ڈسکوں کے لیے، اور $4 ٹیپ کے لیے ہیں۔ اگر ہم محتاط رہیں اور صرف نئی ڈسکوں کو دیکھیں، تو اس کا مطلب ہے کہ ایک پیٹا بائٹ کو ذخیرہ کرنے کی لاگت تقریباً $12,000 ہے۔ اگر ہم فرض کریں کہ ہماری لائبریری 900TB سے 2.7PB تک تین گنا ہو جائے گی، تو اس کا مطلب ہوگا کہ ہماری پوری لائبریری کو عکس بند کرنے کے لیے $32,400۔ بجلی، دیگر ہارڈ ویئر کی لاگت، وغیرہ شامل کرتے ہوئے، آئیے اسے $40,000 تک گول کریں۔ یا ٹیپ کے ساتھ زیادہ $15,000–$20,000۔ ایک طرف <strong>$15,000–$40,000 تمام انسانی علم کی مجموعی قیمت کے لیے ایک سودے کی طرح ہے</strong>۔ دوسری طرف، یہ مکمل کاپیاں رکھنے کی توقع کرنا تھوڑا مہنگا ہے، خاص طور پر اگر ہم یہ بھی چاہیں کہ وہ لوگ دوسروں کے فائدے کے لیے اپنے ٹورینٹس کو جاری رکھیں۔ یہ آج کی بات ہے۔ لیکن ترقی آگے بڑھتی ہے: گزشتہ 10 سالوں میں ہارڈ ڈرائیو کی قیمتیں فی ٹی بی تقریباً ایک تہائی کم ہو چکی ہیں، اور ممکنہ طور پر اسی رفتار سے کم ہوتی رہیں گی۔ ٹیپ بھی اسی راستے پر نظر آتا ہے۔ ایس ایس ڈی کی قیمتیں اور بھی تیزی سے گر رہی ہیں، اور دہائی کے آخر تک ایچ ڈی ڈی کی قیمتوں پر قبضہ کر سکتی ہیں۔ اگر یہ برقرار رہتا ہے، تو 10 سالوں میں ہم اپنی پوری مجموعہ کو عکس بند کرنے کے لیے صرف $5,000–$13,000 (1/3) دیکھ سکتے ہیں، یا اس سے بھی کم اگر ہم سائز میں کم بڑھیں۔ اگرچہ یہ اب بھی بہت زیادہ پیسہ ہے، یہ بہت سے لوگوں کے لیے قابل حصول ہوگا۔ اور یہ اگلے نقطہ کی وجہ سے اور بھی بہتر ہو سکتا ہے… اینّا کے آرکائیو میں، ہم سے اکثر پوچھا جاتا ہے کہ ہم اپنی مجموعات کو ہمیشہ کے لیے محفوظ رکھنے کا دعویٰ کیسے کر سکتے ہیں، جب کہ کل سائز پہلے ہی 1 پیٹا بائٹ (1000 ٹی بی) کے قریب پہنچ رہا ہے، اور ابھی بھی بڑھ رہا ہے۔ اس مضمون میں ہم اپنی فلسفہ پر نظر ڈالیں گے، اور دیکھیں گے کہ اگلی دہائی ہمارے مشن کے لیے کیوں اہم ہے کہ انسانیت کے علم اور ثقافت کو محفوظ رکھا جائے۔ اہم وقت اگر یہ پیش گوئیاں درست ہیں، تو ہمیں <strong>صرف چند سال انتظار کرنا ہوگا</strong> اس سے پہلے کہ ہمارا پورا مجموعہ وسیع پیمانے پر آئینہ دار ہو جائے۔ اس طرح، تھامس جیفرسن کے الفاظ میں، "حادثے کی پہنچ سے باہر رکھا گیا۔" بدقسمتی سے، LLMs کی آمد، اور ان کی ڈیٹا کی بھوک کی تربیت، نے بہت سے کاپی رائٹ ہولڈرز کو دفاعی بنا دیا ہے۔ اس سے بھی زیادہ جو وہ پہلے ہی تھے۔ بہت سی ویب سائٹس کو اسکریپ اور آرکائیو کرنا مشکل بنا رہی ہیں، مقدمے بازی ہو رہی ہے، اور اس دوران جسمانی لائبریریاں اور آرکائیوز نظر انداز ہوتے جا رہے ہیں۔ ہم صرف توقع کر سکتے ہیں کہ یہ رجحانات بدتر ہوتے جائیں گے، اور بہت سے کام عوامی ڈومین میں داخل ہونے سے پہلے ہی ضائع ہو جائیں گے۔ <strong>ہم تحفظ میں انقلاب کے دہانے پر ہیں، لیکن <q>جو کھو گیا ہے اسے بازیافت نہیں کیا جا سکتا۔</q></strong> ہمارے پاس تقریباً 5-10 سال کی ایک اہم ونڈو ہے جس کے دوران شیڈو لائبریری کو چلانا اور دنیا بھر میں بہت سے آئینے بنانا اب بھی کافی مہنگا ہے، اور جس کے دوران رسائی کو مکمل طور پر بند نہیں کیا گیا ہے۔ اگر ہم اس ونڈو کو عبور کر سکتے ہیں، تو ہم واقعی انسانیت کے علم اور ثقافت کو ہمیشہ کے لیے محفوظ کر لیں گے۔ ہمیں اس وقت کو ضائع نہیں ہونے دینا چاہیے۔ ہمیں اس اہم ونڈو کو اپنے اوپر بند نہیں ہونے دینا چاہیے۔ چلیں۔ شیڈو لائبریریوں کی اہم کھڑکی ہم اپنی مجموعات کو ہمیشہ کے لئے محفوظ رکھنے کا دعویٰ کیسے کر سکتے ہیں، جب کہ وہ پہلے ہی 1 پی بی کے قریب پہنچ رہی ہیں؟ مجموعہ مجموعہ کے بارے میں کچھ مزید معلومات۔ <a %(duxiu)s>Duxiu</a> اسکین شدہ کتابوں کا ایک وسیع ڈیٹا بیس ہے، جو <a %(chaoxing)s>SuperStar Digital Library Group</a> نے بنایا ہے۔ زیادہ تر تعلیمی کتابیں ہیں، جو یونیورسٹیوں اور لائبریریوں کو ڈیجیٹل طور پر دستیاب کرنے کے لیے اسکین کی گئی ہیں۔ ہمارے انگریزی بولنے والے سامعین کے لیے، <a %(library_princeton)s>پرنسٹن</a> اور <a %(guides_lib_uw)s>یونیورسٹی آف واشنگٹن</a> کے پاس اچھے جائزے ہیں۔ ایک بہترین مضمون بھی ہے جو مزید پس منظر فراہم کرتا ہے: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (انا کے آرکائیو میں اسے تلاش کریں)۔ Duxiu کی کتابیں طویل عرصے سے چینی انٹرنیٹ پر غیر قانونی طور پر تقسیم کی جا رہی ہیں۔ عام طور پر انہیں دوبارہ فروخت کرنے والوں کے ذریعہ ایک ڈالر سے بھی کم میں فروخت کیا جا رہا ہے۔ انہیں عام طور پر گوگل ڈرائیو کے چینی متبادل کا استعمال کرتے ہوئے تقسیم کیا جاتا ہے، جسے اکثر زیادہ اسٹوریج کی جگہ کی اجازت دینے کے لیے ہیک کیا گیا ہے۔ کچھ تکنیکی تفصیلات <a %(github_duty_machine)s>یہاں</a> اور <a %(github_821_github_io)s>یہاں</a> مل سکتی ہیں۔ اگرچہ کتابیں نیم عوامی طور پر تقسیم کی گئی ہیں، انہیں بڑی مقدار میں حاصل کرنا کافی مشکل ہے۔ یہ ہمارے کرنے کے کاموں کی فہرست میں بہت اوپر تھا، اور ہم نے اس کے لیے مکمل وقت کے کام کے لیے کئی مہینے مختص کیے۔ تاہم، حال ہی میں ایک ناقابل یقین، حیرت انگیز، اور باصلاحیت رضاکار نے ہم سے رابطہ کیا، ہمیں بتایا کہ انہوں نے یہ سارا کام پہلے ہی کر لیا ہے — بڑی قیمت پر۔ انہوں نے پورا مجموعہ ہمارے ساتھ شیئر کیا، بغیر کسی بدلے کی توقع کے، سوائے طویل مدتی تحفظ کی ضمانت کے۔ واقعی قابل ذکر۔ انہوں نے اس طرح مدد مانگنے پر اتفاق کیا تاکہ مجموعہ کو OCR کیا جا سکے۔ مجموعہ میں 7,543,702 فائلیں ہیں۔ یہ لائبریری جینیسس غیر افسانوی (تقریباً 5.3 ملین) سے زیادہ ہے۔ موجودہ شکل میں کل فائل سائز تقریباً 359TB (326TiB) ہے۔ ہم دیگر تجاویز اور خیالات کے لیے کھلے ہیں۔ بس ہم سے رابطہ کریں۔ ہماری کلیکشنز، تحفظ کی کوششوں، اور آپ کس طرح مدد کر سکتے ہیں اس کے بارے میں مزید معلومات کے لیے اینا کا آرکائیو دیکھیں۔ شکریہ! مثال کے صفحات ہمیں یہ ثابت کرنے کے لیے کہ آپ کے پاس ایک اچھی پائپ لائن ہے، یہاں کچھ مثال کے صفحات ہیں جن پر کام شروع کریں، ایک کتاب سے جو سپر کنڈکٹرز پر ہے۔ آپ کی پائپ لائن کو ریاضی، جدولیں، چارٹس، حاشیے وغیرہ کو صحیح طریقے سے سنبھالنا چاہیے۔ اپنے پروسیس شدہ صفحات ہمیں ای میل کریں۔ اگر وہ اچھے لگتے ہیں، تو ہم آپ کو مزید نجی طور پر بھیجیں گے، اور ہم توقع کرتے ہیں کہ آپ ان پر بھی جلدی سے اپنی پائپ لائن چلا سکیں گے۔ جب ہم مطمئن ہوں گے، تو ہم ایک معاہدہ کر سکتے ہیں۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>چینی ورژن 中文版</a>, <a %(news_ycombinator)s>Hacker News پر تبصرہ کریں</a> یہ ایک مختصر بلاگ پوسٹ ہے۔ ہم کسی کمپنی یا ادارے کی تلاش میں ہیں جو ہمارے لیے OCR اور متن نکالنے میں مدد کرے، بدلے میں خصوصی ابتدائی رسائی کے لیے۔ پابندی کی مدت کے بعد، ہم یقینی طور پر پورا مجموعہ جاری کریں گے۔ اعلی معیار کا تعلیمی متن LLMs کی تربیت کے لیے انتہائی مفید ہے۔ جبکہ ہمارا مجموعہ چینی ہے، یہ انگریزی LLMs کی تربیت کے لیے بھی مفید ہونا چاہیے: ماڈلز بظاہر تصورات اور علم کو ماخذ زبان سے قطع نظر انکوڈ کرتے ہیں۔ اس کے لیے، متن کو اسکینز سے نکالنے کی ضرورت ہے۔ انا کا آرکائیو اس سے کیا حاصل کرتا ہے؟ اپنے صارفین کے لیے کتابوں کی مکمل متن تلاش۔ کیونکہ ہمارے مقاصد LLM ڈویلپرز کے ساتھ ہم آہنگ ہیں، ہم ایک ساتھی کی تلاش میں ہیں۔ اگر آپ مناسب OCR اور متن نکال سکتے ہیں تو ہم آپ کو <strong>اس مجموعہ تک 1 سال کے لیے خصوصی ابتدائی رسائی دینے کے لیے تیار ہیں</strong>۔ اگر آپ اپنی پوری پائپ لائن کا کوڈ ہمارے ساتھ شیئر کرنے کے لیے تیار ہیں، تو ہم مجموعہ کو طویل عرصے تک پابندی میں رکھنے کے لیے تیار ہوں گے۔ دنیا کی سب سے بڑی چینی نان فکشن کتابوں کے مجموعے تک LLM کمپنیوں کی خصوصی رسائی <em><strong>مختصر خلاصہ:</strong> انا کا آرکائیو نے 7.5 ملین / 350TB چینی غیر افسانوی کتابوں کا ایک منفرد مجموعہ حاصل کیا ہے — جو کہ لائبریری جینیسس سے بڑا ہے۔ ہم ایک LLM کمپنی کو اعلی معیار کی OCR اور متن نکالنے کے بدلے خصوصی رسائی دینے کے لیے تیار ہیں۔</em> سسٹم کی ساخت تو فرض کریں کہ آپ کو کچھ کمپنیاں مل گئیں جو آپ کی ویب سائٹ کی میزبانی کرنے کے لیے تیار ہیں بغیر آپ کو بند کیے — آئیے انہیں “آزادی پسند فراہم کنندگان” کہتے ہیں 😄۔ آپ جلدی سے دیکھیں گے کہ ان کے ساتھ سب کچھ ہوسٹ کرنا کافی مہنگا ہے، لہذا آپ کچھ “سستے فراہم کنندگان” تلاش کرنا چاہیں گے اور اصل میزبانی وہاں کریں گے، آزادی پسند فراہم کنندگان کے ذریعے پراکسی کرتے ہوئے۔ اگر آپ نے یہ صحیح طریقے سے کیا، تو سستے فراہم کنندگان کبھی نہیں جان پائیں گے کہ آپ کیا ہوسٹ کر رہے ہیں، اور کبھی کوئی شکایت نہیں ملے گی۔ ان تمام فراہم کنندگان کے ساتھ یہ خطرہ موجود ہے کہ وہ آپ کو بہرحال بند کر دیں گے، لہذا آپ کو بھی اضافی ضرورت ہے۔ ہمیں اپنے اسٹیک کی تمام سطحوں پر اس کی ضرورت ہے۔ ایک نسبتاً آزادی پسند کمپنی جس نے خود کو ایک دلچسپ پوزیشن میں رکھا ہے وہ Cloudflare ہے۔ انہوں نے <a %(blog_cloudflare)s>دلیل دی</a> ہے کہ وہ ایک ہوسٹنگ فراہم کنندہ نہیں ہیں، بلکہ ایک یوٹیلیٹی ہیں، جیسے کہ ایک ISP۔ اس لیے وہ DMCA یا دیگر ٹیک ڈاؤن درخواستوں کے تابع نہیں ہیں، اور آپ کے اصل ہوسٹنگ فراہم کنندہ کو کوئی بھی درخواستیں بھیجتے ہیں۔ انہوں نے اس ڈھانچے کی حفاظت کے لیے عدالت میں جانے تک کا قدم اٹھایا ہے۔ اس لیے ہم انہیں کیشنگ اور تحفظ کی ایک اور تہہ کے طور پر استعمال کر سکتے ہیں۔ Cloudflare گمنام ادائیگیاں قبول نہیں کرتا، اس لیے ہم صرف ان کا مفت منصوبہ استعمال کر سکتے ہیں۔ اس کا مطلب یہ ہے کہ ہم ان کی لوڈ بیلنسنگ یا فیل اوور خصوصیات استعمال نہیں کر سکتے۔ اس لیے ہم نے <a %(annas_archive_l255)s>یہ خود ڈومین سطح پر نافذ کیا</a>۔ صفحہ لوڈ ہونے پر، براؤزر چیک کرے گا کہ آیا موجودہ ڈومین اب بھی دستیاب ہے، اور اگر نہیں، تو یہ تمام URLs کو کسی دوسرے ڈومین پر دوبارہ لکھ دیتا ہے۔ چونکہ Cloudflare بہت سے صفحات کو کیش کرتا ہے، اس کا مطلب ہے کہ صارف ہمارے مرکزی ڈومین پر اتر سکتا ہے، یہاں تک کہ اگر پراکسی سرور بند ہو، اور پھر اگلی کلک پر کسی دوسرے ڈومین پر منتقل ہو سکتا ہے۔ ہمارے پاس اب بھی معمول کے آپریشنل خدشات ہیں جن سے نمٹنا ہے، جیسے سرور کی صحت کی نگرانی، بیک اینڈ اور فرنٹ اینڈ کی غلطیوں کا لاگ کرنا، وغیرہ۔ ہماری فیل اوور آرکیٹیکچر اس محاذ پر زیادہ مضبوطی کی اجازت دیتی ہے، مثال کے طور پر ایک ڈومین پر مکمل طور پر مختلف سرورز کا ایک سیٹ چلا کر۔ ہم یہاں تک کہ کوڈ اور ڈیٹا سیٹس کے پرانے ورژن اس علیحدہ ڈومین پر چلا سکتے ہیں، اگر مرکزی ورژن میں کوئی اہم بگ نظر انداز ہو جائے۔ ہم Cloudflare کے خلاف ہونے کے امکان کو بھی کم کر سکتے ہیں، اسے ایک ڈومین سے ہٹا کر، جیسے کہ یہ الگ ڈومین۔ ان خیالات کے مختلف امتزاج ممکن ہیں۔ نتیجہ مضبوط اور مضبوط شیڈو لائبریری سرچ انجن کو ترتیب دینے کا تجربہ دلچسپ رہا ہے۔ بعد کی پوسٹس میں مزید تفصیلات شیئر کرنے کے لیے بہت کچھ ہے، تو مجھے بتائیں کہ آپ کس بارے میں مزید جاننا چاہتے ہیں! ہمیشہ کی طرح، ہم اس کام کی حمایت کے لیے عطیات کی تلاش میں ہیں، لہٰذا اینا کے آرکائیو پر ڈونیٹ پیج کو ضرور دیکھیں۔ ہم دیگر اقسام کی حمایت بھی تلاش کر رہے ہیں، جیسے کہ گرانٹس، طویل مدتی اسپانسرز، ہائی رسک پیمنٹ پرووائیڈرز، شاید (ذوق و شوق سے!) اشتہارات بھی۔ اور اگر آپ اپنا وقت اور مہارتیں دینا چاہتے ہیں، تو ہم ہمیشہ ڈویلپرز، مترجمین وغیرہ کی تلاش میں رہتے ہیں۔ آپ کی دلچسپی اور حمایت کا شکریہ۔ جدت کے ٹوکن آئیے اپنی ٹیک اسٹیک سے شروع کرتے ہیں۔ یہ جان بوجھ کر بورنگ ہے۔ ہم Flask، MariaDB، اور ElasticSearch استعمال کرتے ہیں۔ بس یہی ہے۔ تلاش ایک حد تک حل شدہ مسئلہ ہے، اور ہم اسے دوبارہ ایجاد کرنے کا ارادہ نہیں رکھتے۔ اس کے علاوہ، ہمیں اپنے <a %(mcfunley)s>جدت کے ٹوکن</a> کسی اور چیز پر خرچ کرنے ہیں: حکام کے ذریعہ بند نہ ہونا۔ تو انا کا آرکائیو قانونی یا غیر قانونی کس حد تک ہے؟ یہ زیادہ تر قانونی دائرہ اختیار پر منحصر ہے۔ زیادہ تر ممالک کسی نہ کسی شکل میں کاپی رائٹ پر یقین رکھتے ہیں، جس کا مطلب ہے کہ لوگوں یا کمپنیوں کو کچھ خاص قسم کے کاموں پر ایک خاص مدت کے لیے خصوصی اجارہ داری دی جاتی ہے۔ ایک طرف، انا کے آرکائیو میں ہمارا ماننا ہے کہ کچھ فوائد کے باوجود، مجموعی طور پر کاپی رائٹ معاشرے کے لیے نقصان دہ ہے — لیکن یہ کہانی کسی اور وقت کے لیے ہے۔ کچھ کاموں پر یہ خصوصی اجارہ داری کا مطلب ہے کہ اس اجارہ داری کے باہر کسی کے لیے بھی ان کاموں کو براہ راست تقسیم کرنا غیر قانونی ہے — بشمول ہمارے۔ لیکن انا کا آرکائیو ایک سرچ انجن ہے جو ان کاموں کو براہ راست تقسیم نہیں کرتا (کم از کم ہماری کلیرنیٹ ویب سائٹ پر نہیں)، تو ہمیں ٹھیک ہونا چاہیے، ٹھیک ہے؟ بالکل نہیں۔ بہت سے دائرہ اختیار میں نہ صرف کاپی رائٹ شدہ کاموں کو تقسیم کرنا غیر قانونی ہے، بلکہ ان جگہوں سے لنک کرنا بھی غیر قانونی ہے جو ایسا کرتے ہیں۔ اس کی ایک کلاسک مثال ریاستہائے متحدہ کا DMCA قانون ہے۔ یہ سپیکٹرم کا سب سے سخت اختتام ہے۔ سپیکٹرم کے دوسرے سرے پر نظریاتی طور پر ایسے ممالک ہو سکتے ہیں جن میں کوئی کاپی رائٹ قوانین نہیں ہیں، لیکن یہ واقعی موجود نہیں ہیں۔ تقریباً ہر ملک میں کچھ نہ کچھ کاپی رائٹ قانون موجود ہے۔ نفاذ ایک مختلف کہانی ہے۔ بہت سے ایسے ممالک ہیں جن کی حکومتیں کاپی رائٹ قانون کو نافذ کرنے کی پرواہ نہیں کرتی ہیں۔ ایسے ممالک بھی ہیں جو ان دو انتہاؤں کے درمیان ہیں، جو کاپی رائٹ شدہ کاموں کی تقسیم کو ممنوع قرار دیتے ہیں، لیکن ایسے کاموں سے لنک کرنے کی ممانعت نہیں کرتے۔ ایک اور غور کمپنی کی سطح پر ہے۔ اگر کوئی کمپنی کسی ایسے دائرہ اختیار میں کام کرتی ہے جو کاپی رائٹ کی پرواہ نہیں کرتا، لیکن کمپنی خود کوئی خطرہ مول لینے کے لیے تیار نہیں ہے، تو وہ آپ کی ویب سائٹ کو بند کر سکتی ہے جیسے ہی کوئی اس کے بارے میں شکایت کرتا ہے۔ آخر میں، ایک بڑا غور ادائیگیاں ہیں۔ چونکہ ہمیں گمنام رہنے کی ضرورت ہے، ہم روایتی ادائیگی کے طریقے استعمال نہیں کر سکتے۔ اس سے ہمیں کرپٹو کرنسیوں کے ساتھ چھوڑ دیا جاتا ہے، اور صرف کمپنیوں کا ایک چھوٹا سا ذیلی حصہ ان کی حمایت کرتا ہے (کرپٹو کے ذریعے ادا کردہ ورچوئل ڈیبٹ کارڈز ہیں، لیکن انہیں اکثر قبول نہیں کیا جاتا)۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) میں <a %(wikipedia_annas_archive)s>انا کا آرکائیو</a> چلاتا ہوں، جو کہ دنیا کا سب سے بڑا اوپن سورس غیر منافع بخش سرچ انجن ہے <a %(wikipedia_shadow_library)s>سایہ دار لائبریریوں</a> کے لیے، جیسے Sci-Hub، Library Genesis، اور Z-Library۔ ہمارا مقصد علم اور ثقافت کو آسانی سے دستیاب بنانا ہے، اور بالآخر لوگوں کی ایک کمیونٹی بنانا ہے جو مل کر <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>دنیا کی تمام کتابوں</a> کو محفوظ اور محفوظ رکھیں۔ اس مضمون میں، میں دکھاؤں گا کہ ہم اس ویب سائٹ کو کیسے چلاتے ہیں، اور ایک ایسی ویب سائٹ کو چلانے کے منفرد چیلنجز جو قانونی حیثیت کے لحاظ سے مشکوک ہے، کیونکہ "سایہ دار خیراتی اداروں کے لیے AWS" نہیں ہے۔ <em>بہن مضمون بھی دیکھیں <a %(blog_how_to_become_a_pirate_archivist)s>کیسے ایک قزاق آرکائیوسٹ بنیں</a>۔</em> ایک شیڈو لائبریری کیسے چلائیں: انا کے آرکائیو میں آپریشنز سایہ دار خیراتی اداروں کے لیے کوئی <q>AWS نہیں ہے،</q> تو ہم انا کا آرکائیو کیسے چلاتے ہیں؟ اوزار ایپلیکیشن سرور: فلاسک، ماریا ڈی بی، ایلاسٹک سرچ، ڈاکر۔ ترقی: گٹ لیب، ویب لیٹ، زولپ۔ سرور مینجمنٹ: اینسبل، چیک ایم کے، یو ایف ڈبلیو۔ اونین سٹیٹک ہوسٹنگ: ٹور، اینجن ایکس۔ پراکسی سرور: وارنش۔ آئیے دیکھتے ہیں کہ ہم یہ سب کچھ حاصل کرنے کے لیے کون سے اوزار استعمال کرتے ہیں۔ یہ بہت زیادہ ترقی پذیر ہے کیونکہ ہم نئے مسائل کا سامنا کرتے ہیں اور نئے حل تلاش کرتے ہیں۔ کچھ فیصلے ہیں جن پر ہم نے آگے پیچھے کیا ہے۔ ایک سرورز کے درمیان مواصلات ہے: ہم اس کے لیے Wireguard استعمال کرتے تھے، لیکن پایا کہ یہ کبھی کبھار کوئی ڈیٹا منتقل کرنا بند کر دیتا ہے، یا صرف ایک سمت میں ڈیٹا منتقل کرتا ہے۔ یہ کئی مختلف Wireguard سیٹ اپ کے ساتھ ہوا جو ہم نے آزمایا، جیسے <a %(github_costela_wesher)s>wesher</a> اور <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>۔ ہم نے SSH کے ذریعے پورٹس کو ٹنل کرنے کی بھی کوشش کی، autossh اور sshuttle کا استعمال کرتے ہوئے، لیکن وہاں <a %(github_sshuttle)s>مسائل کا سامنا کرنا پڑا</a> (حالانکہ یہ ابھی تک واضح نہیں ہے کہ آیا autossh TCP-over-TCP مسائل سے دوچار ہے یا نہیں — یہ میرے لیے ایک عجیب حل کی طرح محسوس ہوتا ہے لیکن شاید یہ دراصل ٹھیک ہے؟)۔ اس کے بجائے، ہم نے سستے فراہم کنندگان پر سرور چلانے کو چھپانے کے لیے UFW کے ساتھ IP-فلٹرنگ کا استعمال کرتے ہوئے سرورز کے درمیان براہ راست کنکشن پر واپس چلے گئے۔ اس کا نقصان یہ ہے کہ Docker UFW کے ساتھ اچھی طرح کام نہیں کرتا، جب تک کہ آپ <code>network_mode: "host"</code> استعمال نہ کریں۔ یہ سب کچھ تھوڑا زیادہ غلطی کا شکار ہے، کیونکہ آپ صرف ایک چھوٹی سی غلط ترتیب کے ساتھ اپنے سرور کو انٹرنیٹ پر بے نقاب کر دیں گے۔ شاید ہمیں autossh پر واپس جانا چاہیے — یہاں رائے کا خیرمقدم کیا جائے گا۔ ہم نے Varnish اور Nginx پر بھی آگے پیچھے کیا ہے۔ ہمیں فی الحال Varnish پسند ہے، لیکن اس میں کچھ عجیب و غریب چیزیں اور کھردری کنارے ہیں۔ Checkmk کے لیے بھی یہی بات لاگو ہوتی ہے: ہمیں یہ پسند نہیں ہے، لیکن یہ فی الحال کام کرتا ہے۔ Weblate ٹھیک رہا ہے لیکن ناقابل یقین نہیں — مجھے کبھی کبھی ڈر لگتا ہے کہ یہ میرے ڈیٹا کو کھو دے گا جب بھی میں اسے ہمارے git ریپو کے ساتھ ہم آہنگ کرنے کی کوشش کرتا ہوں۔ Flask مجموعی طور پر اچھا رہا ہے، لیکن اس میں کچھ عجیب و غریب چیزیں ہیں جنہیں ڈیبگ کرنے میں کافی وقت لگا ہے، جیسے کہ حسب ضرورت ڈومینز کی تشکیل، یا اس کے SqlAlchemy انضمام کے مسائل۔ اب تک دوسرے اوزار بہت اچھے رہے ہیں: ہمیں MariaDB، ElasticSearch، Gitlab، Zulip، Docker، اور Tor کے بارے میں کوئی سنجیدہ شکایت نہیں ہے۔ ان سب میں کچھ مسائل رہے ہیں، لیکن کچھ بھی زیادہ سنجیدہ یا وقت طلب نہیں۔ کمیونٹی پہلا چیلنج شاید حیران کن ہو سکتا ہے۔ یہ کوئی تکنیکی مسئلہ نہیں ہے، یا قانونی مسئلہ نہیں ہے۔ یہ ایک نفسیاتی مسئلہ ہے: سائے میں یہ کام کرنا ناقابل یقین حد تک تنہا ہو سکتا ہے۔ اس بات پر منحصر ہے کہ آپ کیا کرنے کا ارادہ رکھتے ہیں، اور آپ کا خطرہ ماڈل کیا ہے، آپ کو بہت محتاط رہنا پڑ سکتا ہے۔ سپیکٹرم کے ایک سرے پر ہمارے پاس Alexandra Elbakyan جیسی لوگ ہیں، Sci-Hub کی بانی، جو اپنی سرگرمیوں کے بارے میں بہت کھلی ہیں۔ لیکن وہ اس وقت کسی مغربی ملک کا دورہ کرنے پر گرفتار ہونے کے خطرے میں ہیں، اور دہائیوں کی قید کا سامنا کر سکتی ہیں۔ کیا یہ ایک خطرہ ہے جو آپ لینے کے لیے تیار ہوں گے؟ ہم سپیکٹرم کے دوسرے سرے پر ہیں؛ کوئی نشان نہ چھوڑنے کے لیے بہت محتاط رہنا، اور مضبوط آپریشنل سیکیورٹی رکھنا۔ * جیسا کہ HN پر "ynno" نے ذکر کیا، Alexandra ابتدائی طور پر معروف نہیں ہونا چاہتی تھیں: "ان کے سرورز کو PHP سے تفصیلی ایرر میسجز خارج کرنے کے لیے سیٹ کیا گیا تھا، بشمول فالٹنگ سورس فائل کا مکمل راستہ، جو ڈائریکٹری /home/<USER>" لہذا، اس چیز کے لیے آپ جو کمپیوٹر استعمال کرتے ہیں ان پر بے ترتیب یوزر نیم استعمال کریں، اگر آپ کچھ غلط کنفیگر کر دیں۔ تاہم، یہ راز داری نفسیاتی قیمت کے ساتھ آتی ہے۔ زیادہ تر لوگ اپنے کام کے لئے پہچانے جانے کو پسند کرتے ہیں، اور پھر بھی آپ حقیقی زندگی میں اس کا کوئی کریڈٹ نہیں لے سکتے۔ یہاں تک کہ سادہ چیزیں بھی چیلنجنگ ہو سکتی ہیں، جیسے دوست آپ سے پوچھتے ہیں کہ آپ کیا کر رہے ہیں (کسی وقت "میرے NAS / ہوم لیب کے ساتھ چھیڑ چھاڑ" پرانا ہو جاتا ہے)۔ اسی لئے کچھ کمیونٹی تلاش کرنا بہت اہم ہے۔ آپ کچھ آپریشنل سیکیورٹی کو چھوڑ سکتے ہیں کچھ بہت قریبی دوستوں کے ساتھ راز داری کر کے، جن پر آپ کو گہرا اعتماد ہے۔ پھر بھی محتاط رہیں کہ کچھ بھی تحریری طور پر نہ ڈالیں، اگر انہیں اپنے ای میلز حکام کو دینا پڑیں، یا اگر ان کے آلات کسی اور طریقے سے متاثر ہو جائیں۔ بہتر یہ ہے کہ کچھ ساتھی قزاق تلاش کریں۔ اگر آپ کے قریبی دوست آپ کے ساتھ شامل ہونے میں دلچسپی رکھتے ہیں، تو بہت اچھا! ورنہ، آپ آن لائن دوسروں کو تلاش کر سکتے ہیں۔ بدقسمتی سے یہ اب بھی ایک نچ کمیونٹی ہے۔ اب تک ہم نے صرف چند دیگر افراد کو پایا ہے جو اس جگہ میں فعال ہیں۔ اچھے آغاز کے مقامات Library Genesis فورمز، اور r/DataHoarder لگتے ہیں۔ آرکائیو ٹیم میں بھی ہم خیال افراد ہیں، حالانکہ وہ قانون کے اندر کام کرتے ہیں (چاہے قانون کے کچھ گرے علاقوں میں بھی)۔ روایتی "warez" اور قزاقی کے مناظر میں بھی ایسے لوگ ہیں جو اسی طرح سوچتے ہیں۔ ہم کمیونٹی کو فروغ دینے اور خیالات کو تلاش کرنے کے لئے خیالات کے لئے کھلے ہیں۔ ہمیں Twitter یا Reddit پر پیغام بھیجنے میں آزاد محسوس کریں۔ شاید ہم کسی قسم کے فورم یا چیٹ گروپ کی میزبانی کر سکتے ہیں۔ ایک چیلنج یہ ہے کہ جب عام پلیٹ فارمز کا استعمال کرتے ہوئے یہ آسانی سے سنسر ہو سکتا ہے، لہذا ہمیں خود اس کی میزبانی کرنی ہوگی۔ ان مباحثوں کو مکمل طور پر عوامی بنانے (زیادہ ممکنہ شمولیت) اور اسے نجی بنانے (ممکنہ "ہدف" کو یہ نہ بتانا کہ ہم انہیں سکریپ کرنے والے ہیں) کے درمیان بھی ایک توازن ہے۔ ہمیں اس کے بارے میں سوچنا ہوگا۔ ہمیں بتائیں اگر آپ اس میں دلچسپی رکھتے ہیں! نتیجہ امید ہے کہ یہ نئے شروع ہونے والے قزاقی آرکائیوسٹس کے لیے مددگار ثابت ہوگا۔ ہم آپ کو اس دنیا میں خوش آمدید کہنے کے لیے پرجوش ہیں، لہذا ہم سے رابطہ کرنے میں ہچکچائیں نہیں۔ آئیے دنیا کے علم اور ثقافت کو جتنا ممکن ہو محفوظ کریں، اور اسے دور دور تک پھیلائیں۔ پروجیکٹس 4. ڈیٹا کا انتخاب اکثر آپ metadata کا استعمال کر کے ڈاؤن لوڈ کرنے کے لیے ڈیٹا کا ایک معقول ذیلی سیٹ معلوم کر سکتے ہیں۔ یہاں تک کہ اگر آپ بالآخر تمام ڈیٹا ڈاؤن لوڈ کرنا چاہتے ہیں، تو یہ سب سے اہم اشیاء کو پہلے ترجیح دینا مفید ہو سکتا ہے، اس صورت میں کہ آپ کا پتہ چل جائے اور دفاعات بہتر ہو جائیں، یا کیونکہ آپ کو مزید ڈسک خریدنے کی ضرورت ہو، یا محض اس لیے کہ آپ کی زندگی میں کچھ اور آ جائے اس سے پہلے کہ آپ سب کچھ ڈاؤن لوڈ کر سکیں۔ مثال کے طور پر، ایک مجموعہ میں ایک ہی بنیادی وسیلہ (جیسے کتاب یا فلم) کے متعدد ایڈیشن ہو سکتے ہیں، جہاں ایک کو بہترین معیار کے طور پر نشان زد کیا گیا ہو۔ ان ایڈیشنز کو پہلے محفوظ کرنا بہت سمجھداری کی بات ہوگی۔ آپ بالآخر تمام ایڈیشنز کو محفوظ کرنا چاہیں گے، کیونکہ بعض صورتوں میں metadata غلط طور پر ٹیگ کیا جا سکتا ہے، یا ایڈیشنز کے درمیان نامعلوم سمجھوتے ہو سکتے ہیں (مثال کے طور پر، "بہترین ایڈیشن" زیادہ تر طریقوں میں بہترین ہو سکتا ہے لیکن دوسرے طریقوں میں بدتر ہو سکتا ہے، جیسے فلم کا زیادہ ریزولوشن ہونا لیکن سب ٹائٹلز کا نہ ہونا)۔ آپ اپنی metadata ڈیٹا بیس کو دلچسپ چیزیں تلاش کرنے کے لیے بھی تلاش کر سکتے ہیں۔ سب سے بڑی فائل کون سی ہے جو ہوسٹ کی گئی ہے، اور یہ اتنی بڑی کیوں ہے؟ سب سے چھوٹی فائل کون سی ہے؟ کیا کچھ خاص زمروں، زبانوں وغیرہ کے حوالے سے دلچسپ یا غیر متوقع نمونے ہیں؟ کیا وہاں ڈپلیکیٹ یا بہت ملتے جلتے عنوانات ہیں؟ کیا ڈیٹا کے شامل ہونے کے وقت کے نمونے ہیں، جیسے ایک دن جس میں بہت سی فائلیں ایک ساتھ شامل کی گئیں؟ آپ اکثر مختلف طریقوں سے ڈیٹا سیٹ کو دیکھ کر بہت کچھ سیکھ سکتے ہیں۔ ہمارے معاملے میں، ہم نے Z-Library کی کتابوں کو Library Genesis میں md5 ہیشز کے خلاف ڈی ڈپلیکیٹ کیا، اس طرح بہت سا ڈاؤن لوڈ وقت اور ڈسک اسپیس بچایا۔ یہ ایک بہت ہی منفرد صورتحال ہے۔ زیادہ تر معاملات میں ایسی کوئی جامع ڈیٹا بیس نہیں ہیں کہ کون سی فائلیں پہلے ہی ساتھی قزاقوں کے ذریعہ مناسب طریقے سے محفوظ ہیں۔ یہ خود میں وہاں کسی کے لیے ایک بہت بڑا موقع ہے۔ یہ بہت اچھا ہوگا کہ موسیقی اور فلموں جیسی چیزوں کا باقاعدگی سے اپ ڈیٹ شدہ جائزہ ہو جو پہلے ہی ٹورینٹ ویب سائٹس پر وسیع پیمانے پر سیڈ کی گئی ہیں، اور اس لیے قزاقوں کے آئینے میں شامل کرنے کے لیے کم ترجیحی ہیں۔ 6. تقسیم آپ کے پاس ڈیٹا ہے، اس طرح آپ کو دنیا کا پہلا قزاق آئینہ حاصل ہو گیا ہے (زیادہ تر ممکنہ طور پر)۔ بہت سے طریقوں سے سب سے مشکل حصہ ختم ہو چکا ہے، لیکن سب سے زیادہ خطرناک حصہ ابھی آپ کے سامنے ہے۔ آخرکار، اب تک آپ چپکے چپکے رہے ہیں؛ ریڈار کے نیچے پرواز کر رہے ہیں۔ آپ کو بس ایک اچھا VPN استعمال کرنا تھا، کسی بھی فارم میں اپنی ذاتی تفصیلات نہیں بھرنی تھیں (ظاہر ہے)، اور شاید ایک خاص براؤزر سیشن (یا یہاں تک کہ ایک مختلف کمپیوٹر) استعمال کرنا تھا۔ اب آپ کو ڈیٹا تقسیم کرنا ہے۔ ہمارے معاملے میں ہم پہلے کتابوں کو Library Genesis میں واپس دینا چاہتے تھے، لیکن پھر جلدی سے اس میں مشکلات کا پتہ چلا (افسانہ بمقابلہ غیر افسانہ کی ترتیب)۔ لہذا ہم نے Library Genesis طرز کے ٹورینٹس کا استعمال کرتے ہوئے تقسیم کرنے کا فیصلہ کیا۔ اگر آپ کے پاس کسی موجودہ پروجیکٹ میں تعاون کرنے کا موقع ہے، تو یہ آپ کو بہت سا وقت بچا سکتا ہے۔ تاہم، فی الحال وہاں بہت سے منظم قزاق آئینے نہیں ہیں۔ تو فرض کریں کہ آپ خود ٹورینٹس تقسیم کرنے کا فیصلہ کرتے ہیں۔ ان فائلوں کو چھوٹا رکھنے کی کوشش کریں، تاکہ انہیں دوسرے ویب سائٹس پر آئینہ دار کرنا آسان ہو۔ آپ کو پھر بھی ٹورینٹس کو خود سیڈ کرنا ہوگا، جبکہ گمنام رہنا ہوگا۔ آپ VPN استعمال کر سکتے ہیں (پورٹ فارورڈنگ کے ساتھ یا بغیر)، یا Seedbox کے لیے تملڈ بٹ کوائنز کے ساتھ ادائیگی کر سکتے ہیں۔ اگر آپ کو ان میں سے کچھ اصطلاحات کا مطلب نہیں معلوم، تو آپ کو پڑھنے کے لیے بہت کچھ کرنا ہوگا، کیونکہ یہ ضروری ہے کہ آپ یہاں خطرے کے سمجھوتوں کو سمجھیں۔ آپ خود ٹورینٹ فائلوں کو موجودہ ٹورینٹ ویب سائٹس پر ہوسٹ کر سکتے ہیں۔ ہمارے معاملے میں، ہم نے دراصل ایک ویب سائٹ کی میزبانی کا انتخاب کیا، کیونکہ ہم اپنی فلسفہ کو واضح طریقے سے پھیلانا بھی چاہتے تھے۔ آپ یہ خود اسی طرح کر سکتے ہیں (ہم اپنے ڈومینز اور ہوسٹنگ کے لیے Njalla استعمال کرتے ہیں، جو تملڈ بٹ کوائنز کے ساتھ ادا کیا جاتا ہے)، لیکن ہمیں اپنے ٹورینٹس کی میزبانی کرنے کے لیے ہم سے رابطہ کرنے میں بھی آزاد محسوس کریں۔ اگر یہ خیال مقبول ہوتا ہے تو ہم وقت کے ساتھ قزاق آئینوں کا ایک جامع انڈیکس بنانے کی کوشش کر رہے ہیں۔ VPN کے انتخاب کے بارے میں پہلے ہی بہت کچھ لکھا جا چکا ہے، اس لیے ہم صرف یہ مشورہ دیں گے کہ شہرت کی بنیاد پر انتخاب کریں۔ عدالت میں آزمودہ کوئی لاگ نہ رکھنے کی پالیسیاں جو طویل عرصے سے پرائیویسی کی حفاظت کر رہی ہیں، ہمارے خیال میں سب سے کم خطرے کا آپشن ہیں۔ نوٹ کریں کہ جب آپ سب کچھ صحیح کرتے ہیں، تب بھی آپ کبھی بھی خطرے کو صفر تک نہیں پہنچا سکتے۔ مثال کے طور پر، جب آپ اپنے ٹورینٹس کو سیڈ کر رہے ہوتے ہیں، تو ایک انتہائی متحرک ریاستی اداکار شاید VPN سرورز کے لیے آنے اور جانے والے ڈیٹا کے بہاؤ کو دیکھ سکتا ہے، اور یہ معلوم کر سکتا ہے کہ آپ کون ہیں۔ یا آپ کسی طرح سے غلطی کر سکتے ہیں۔ ہم شاید پہلے ہی کر چکے ہیں، اور دوبارہ کریں گے۔ خوش قسمتی سے، ریاستیں <em>اتنی</em> زیادہ پرواہ نہیں کرتی ہیں کہ وہ قزاقی کے بارے میں فکر مند ہوں۔ ہر پروجیکٹ کے لیے ایک فیصلہ کرنا ہوتا ہے کہ آیا اسے پہلے کی طرح کی شناخت کے ساتھ شائع کرنا ہے یا نہیں۔ اگر آپ وہی نام استعمال کرتے رہتے ہیں، تو پہلے کے پروجیکٹس میں آپریشنل سیکیورٹی کی غلطیاں آپ کو نقصان پہنچا سکتی ہیں۔ لیکن مختلف ناموں کے تحت شائع کرنے کا مطلب ہے کہ آپ طویل مدتی شہرت نہیں بنا سکتے۔ ہم نے شروع سے ہی مضبوط آپریشنل سیکیورٹی کا انتخاب کیا تاکہ ہم وہی شناخت استعمال کر سکیں، لیکن اگر ہم غلطی کرتے ہیں یا حالات اس کا تقاضا کرتے ہیں تو ہم مختلف نام کے تحت شائع کرنے میں ہچکچائیں گے نہیں۔ بات کو پھیلانا مشکل ہو سکتا ہے۔ جیسا کہ ہم نے کہا، یہ اب بھی ایک مخصوص کمیونٹی ہے۔ ہم نے اصل میں Reddit پر پوسٹ کیا، لیکن واقعی Hacker News پر توجہ حاصل کی۔ فی الحال ہماری سفارش یہ ہے کہ اسے چند جگہوں پر پوسٹ کریں اور دیکھیں کہ کیا ہوتا ہے۔ اور دوبارہ، ہم سے رابطہ کریں۔ ہم مزید قزاقی آرکائیوز کی کوششوں کے بارے میں بات پھیلانے کے خواہاں ہیں۔ 1. ڈومین کا انتخاب / فلسفہ محفوظ کرنے کے لیے علم اور ثقافتی ورثے کی کوئی کمی نہیں ہے، جو کہ زبردست ہو سکتا ہے۔ اسی لیے اکثر یہ مفید ہوتا ہے کہ ایک لمحہ لیں اور سوچیں کہ آپ کا تعاون کیا ہو سکتا ہے۔ ہر کسی کا اس بارے میں سوچنے کا مختلف طریقہ ہوتا ہے، لیکن یہاں کچھ سوالات ہیں جو آپ اپنے آپ سے پوچھ سکتے ہیں: ہمارے معاملے میں، ہم خاص طور پر سائنس کے طویل مدتی تحفظ کے بارے میں فکر مند تھے۔ ہمیں Library Genesis کے بارے میں معلوم تھا، اور یہ کہ اسے کئی بار مکمل طور پر ٹورینٹس کے ذریعے عکس بند کیا گیا تھا۔ ہمیں یہ خیال بہت پسند آیا۔ پھر ایک دن، ہم میں سے ایک نے Library Genesis پر کچھ سائنسی درسی کتابیں تلاش کرنے کی کوشش کی، لیکن انہیں نہیں مل سکا، جس سے یہ شک پیدا ہوا کہ یہ واقعی کتنا مکمل تھا۔ پھر ہم نے ان درسی کتابوں کو آن لائن تلاش کیا، اور انہیں دوسری جگہوں پر پایا، جس نے ہمارے منصوبے کے لیے بیج بو دیا۔ یہاں تک کہ جب ہم Z-Library کے بارے میں نہیں جانتے تھے، ہمارے پاس یہ خیال تھا کہ ان تمام کتابوں کو دستی طور پر جمع کرنے کی کوشش نہ کریں، بلکہ موجودہ مجموعوں کو عکس بند کرنے پر توجہ مرکوز کریں، اور انہیں Library Genesis میں واپس شامل کریں۔ آپ کے پاس کون سی مہارتیں ہیں جنہیں آپ اپنے فائدے کے لیے استعمال کر سکتے ہیں؟ مثال کے طور پر، اگر آپ آن لائن سیکیورٹی کے ماہر ہیں، تو آپ محفوظ اہداف کے لیے آئی پی بلاکس کو شکست دینے کے طریقے تلاش کر سکتے ہیں۔ اگر آپ کمیونٹیز کو منظم کرنے میں ماہر ہیں، تو شاید آپ کسی مقصد کے ارد گرد کچھ لوگوں کو اکٹھا کر سکتے ہیں۔ کچھ پروگرامنگ جاننا مفید ہے، اگر صرف اس عمل کے دوران اچھی آپریشنل سیکیورٹی کو برقرار رکھنے کے لیے۔ کون سا اعلیٰ فائدہ مند علاقہ ہے جس پر توجہ مرکوز کی جائے؟ اگر آپ قزاقی آرکائیونگ پر X گھنٹے صرف کرنے جا رہے ہیں، تو پھر آپ اپنے "بک کے لئے سب سے بڑا بینگ" کیسے حاصل کر سکتے ہیں؟ آپ اس بارے میں منفرد طریقے سے کیسے سوچ رہے ہیں؟ آپ کے پاس کچھ دلچسپ خیالات یا طریقے ہو سکتے ہیں جو دوسروں نے نظرانداز کر دیے ہوں۔ آپ کے پاس اس کے لئے کتنا وقت ہے؟ ہمارا مشورہ یہ ہوگا کہ چھوٹے سے شروع کریں اور جیسے جیسے آپ کو اس کی عادت ہو جائے، بڑے پروجیکٹس کریں، لیکن یہ سب کچھ کھا سکتا ہے۔ آپ اس میں دلچسپی کیوں رکھتے ہیں؟ آپ کس چیز کے بارے میں پرجوش ہیں؟ اگر ہم لوگوں کا ایک گروپ حاصل کر سکتے ہیں جو ان چیزوں کو محفوظ کرتے ہیں جن کی وہ خاص طور پر پرواہ کرتے ہیں، تو یہ بہت کچھ کا احاطہ کرے گا! آپ اپنے شوق کے بارے میں اوسط شخص سے بہت زیادہ جانیں گے، جیسے کہ محفوظ کرنے کے لیے اہم ڈیٹا کیا ہے، بہترین مجموعے اور آن لائن کمیونٹیز کیا ہیں، وغیرہ۔ 3. Metadata سکریپنگ تاریخ شامل/ترمیم شدہ: تاکہ آپ بعد میں واپس آ سکیں اور فائلیں ڈاؤن لوڈ کر سکیں جو آپ نے پہلے ڈاؤن لوڈ نہیں کی تھیں (حالانکہ آپ اکثر اس کے لیے ID یا ہیش بھی استعمال کر سکتے ہیں)۔ ہیش (md5، sha1): اس بات کی تصدیق کرنے کے لیے کہ آپ نے فائل کو صحیح طریقے سے ڈاؤن لوڈ کیا ہے۔ آئی ڈی: کچھ اندرونی آئی ڈی ہو سکتی ہے، لیکن آئی ایس بی این یا DOI جیسے آئی ڈی بھی مفید ہیں۔ فائل کا نام / مقام تفصیل، زمرہ، ٹیگز، مصنفین، زبان، وغیرہ۔ سائز: یہ حساب لگانے کے لیے کہ آپ کو کتنی ڈسک اسپیس کی ضرورت ہے۔ آئیے یہاں تھوڑا سا تکنیکی ہو جائیں۔ ویب سائٹس سے دراصل metadata سکریپ کرنے کے لیے، ہم نے چیزوں کو کافی سادہ رکھا ہے۔ ہم Python اسکرپٹس، کبھی کبھار curl، اور MySQL ڈیٹا بیس کا استعمال کرتے ہیں تاکہ نتائج کو ذخیرہ کیا جا سکے۔ ہم نے کوئی فینسی سکریپنگ سافٹ ویئر استعمال نہیں کیا ہے جو پیچیدہ ویب سائٹس کو نقشہ بنا سکے، کیونکہ اب تک ہمیں صرف ids کے ذریعے صفحات کو شمار کرنے اور HTML کو پارس کرنے کی ضرورت تھی۔ اگر صفحات کو آسانی سے شمار نہیں کیا جا سکتا، تو آپ کو ایک مناسب کرالر کی ضرورت ہو سکتی ہے جو تمام صفحات کو تلاش کرنے کی کوشش کرے۔ پوری ویب سائٹ کو سکریپ کرنا شروع کرنے سے پہلے، تھوڑی دیر کے لیے اسے دستی طور پر کرنے کی کوشش کریں۔ خود کچھ درجن صفحات کے ذریعے جائیں، تاکہ آپ کو یہ سمجھنے میں مدد ملے کہ یہ کیسے کام کرتا ہے۔ کبھی کبھی آپ اس طرح IP بلاکس یا دیگر دلچسپ رویے کا سامنا کریں گے۔ ڈیٹا سکریپنگ کے لیے بھی یہی بات ہے: اس ہدف میں بہت گہرائی میں جانے سے پہلے، یقینی بنائیں کہ آپ اس کا ڈیٹا مؤثر طریقے سے ڈاؤن لوڈ کر سکتے ہیں۔ پابندیوں سے بچنے کے لیے، آپ کچھ چیزیں آزما سکتے ہیں۔ کیا کوئی اور IP پتے یا سرورز ہیں جو وہی ڈیٹا ہوسٹ کرتے ہیں لیکن ان پر وہی پابندیاں نہیں ہیں؟ کیا کوئی API اینڈ پوائنٹس ہیں جن پر پابندیاں نہیں ہیں، جبکہ دیگر پر ہیں؟ کس رفتار سے ڈاؤن لوڈ کرنے پر آپ کا IP بلاک ہو جاتا ہے، اور کتنی دیر کے لیے؟ یا آپ بلاک نہیں ہوتے بلکہ رفتار کم کر دی جاتی ہے؟ اگر آپ ایک صارف اکاؤنٹ بناتے ہیں، تو چیزیں کیسے بدلتی ہیں؟ کیا آپ HTTP/2 کا استعمال کر سکتے ہیں تاکہ کنکشن کھلے رہیں، اور کیا اس سے آپ کے صفحات کی درخواست کرنے کی شرح میں اضافہ ہوتا ہے؟ کیا ایسے صفحات ہیں جو ایک ساتھ متعدد فائلوں کی فہرست دیتے ہیں، اور کیا وہاں درج معلومات کافی ہیں؟ چیزیں جو آپ شاید محفوظ کرنا چاہیں گے ان میں شامل ہیں: ہم عام طور پر یہ دو مراحل میں کرتے ہیں۔ پہلے ہم خام HTML فائلیں ڈاؤن لوڈ کرتے ہیں، عام طور پر براہ راست MySQL میں (بہت ساری چھوٹی فائلوں سے بچنے کے لیے، جس کے بارے میں ہم نیچے مزید بات کرتے ہیں)۔ پھر، ایک علیحدہ مرحلے میں، ہم ان HTML فائلوں کے ذریعے جاتے ہیں اور انہیں اصل MySQL ٹیبلز میں پارس کرتے ہیں۔ اس طرح اگر آپ اپنی پارسنگ کوڈ میں کوئی غلطی دریافت کرتے ہیں تو آپ کو سب کچھ دوبارہ ڈاؤن لوڈ کرنے کی ضرورت نہیں ہے، کیونکہ آپ نئی کوڈ کے ساتھ HTML فائلوں کو دوبارہ پروسیس کر سکتے ہیں۔ یہ اکثر پروسیسنگ مرحلے کو متوازی بنانے میں بھی آسان ہوتا ہے، اس طرح کچھ وقت بچتا ہے (اور آپ سکریپنگ کے دوران پروسیسنگ کوڈ لکھ سکتے ہیں، بجائے اس کے کہ دونوں مراحل کو ایک ساتھ لکھنا پڑے)۔ آخر میں، نوٹ کریں کہ کچھ اہداف کے لیے metadata سکریپنگ ہی سب کچھ ہے۔ وہاں کچھ بڑے metadata مجموعے ہیں جو مناسب طریقے سے محفوظ نہیں ہیں۔ عنوان ڈومین کا انتخاب / فلسفہ: آپ تقریباً کہاں توجہ مرکوز کرنا چاہتے ہیں، اور کیوں؟ آپ کے منفرد شوق، مہارتیں، اور حالات کیا ہیں جنہیں آپ اپنے فائدے کے لئے استعمال کر سکتے ہیں؟ ہدف کا انتخاب: آپ کون سا مخصوص مجموعہ آئینہ بنائیں گے؟ میٹا ڈیٹا سکریپنگ: فائلوں کے بارے میں معلومات کی کیٹلاگنگ، بغیر اصل میں (اکثر بہت بڑی) فائلوں کو ڈاؤن لوڈ کیے۔ ڈیٹا کا انتخاب: میٹا ڈیٹا کی بنیاد پر، یہ تنگ کرنا کہ کون سا ڈیٹا ابھی آرکائیو کرنے کے لئے سب سے زیادہ متعلقہ ہے۔ یہ سب کچھ ہو سکتا ہے، لیکن اکثر جگہ اور بینڈوڈتھ بچانے کا ایک معقول طریقہ ہوتا ہے۔ ڈیٹا سکریپنگ: اصل میں ڈیٹا حاصل کرنا۔ تقسیم: اسے ٹورینٹس میں پیکج کرنا، کہیں اعلان کرنا، لوگوں کو اسے پھیلانے کے لئے حاصل کرنا۔ 5. ڈیٹا اسکریپنگ اب آپ واقعی میں ڈیٹا کو بڑی مقدار میں ڈاؤن لوڈ کرنے کے لیے تیار ہیں۔ جیسا کہ پہلے ذکر کیا گیا ہے، اس وقت آپ کو پہلے ہی ہدف کے رویے اور پابندیوں کو بہتر طور پر سمجھنے کے لیے دستی طور پر بہت سی فائلیں ڈاؤن لوڈ کرنی چاہئیں۔ تاہم، جب آپ ایک ساتھ بہت سی فائلیں ڈاؤن لوڈ کرنے کے لیے پہنچیں گے تو آپ کے لیے ابھی بھی حیرتیں موجود ہوں گی۔ ہمارا مشورہ یہاں بنیادی طور پر اسے سادہ رکھنے کا ہے۔ بس بہت سی فائلیں ڈاؤن لوڈ کر کے شروع کریں۔ آپ Python استعمال کر سکتے ہیں، اور پھر متعدد تھریڈز تک توسیع کر سکتے ہیں۔ لیکن بعض اوقات اس سے بھی زیادہ سادہ یہ ہے کہ براہ راست ڈیٹا بیس سے Bash فائلیں تیار کریں، اور پھر انہیں متعدد ٹرمینل ونڈوز میں چلائیں تاکہ پیمانہ بڑھایا جا سکے۔ یہاں ایک فوری تکنیکی چال کا ذکر کرنا قابل ہے کہ MySQL میں OUTFILE کا استعمال کریں، جسے آپ کہیں بھی لکھ سکتے ہیں اگر آپ mysqld.cnf میں "secure_file_priv" کو غیر فعال کر دیں (اور اگر آپ لینکس پر ہیں تو AppArmor کو بھی غیر فعال/اوور رائیڈ کرنا یقینی بنائیں)۔ ہم ڈیٹا کو سادہ ہارڈ ڈسک پر محفوظ کرتے ہیں۔ جو کچھ بھی آپ کے پاس ہے اس سے شروع کریں، اور آہستہ آہستہ توسیع کریں۔ سینکڑوں TBs ڈیٹا کو ذخیرہ کرنے کے بارے میں سوچنا زبردست ہو سکتا ہے۔ اگر یہ وہ صورتحال ہے جس کا آپ سامنا کر رہے ہیں، تو پہلے ایک اچھا ذیلی سیٹ نکالیں، اور اپنے اعلان میں باقی کو ذخیرہ کرنے میں مدد کے لیے کہیں۔ اگر آپ خود مزید ہارڈ ڈرائیوز حاصل کرنا چاہتے ہیں، تو r/DataHoarder کے پاس اچھے سودے حاصل کرنے کے بارے میں کچھ اچھے وسائل ہیں۔ فینسی فائل سسٹمز کے بارے میں زیادہ فکر کرنے کی کوشش نہ کریں۔ ZFS جیسی چیزوں کو ترتیب دینے کے خرگوش کے سوراخ میں گرنا آسان ہے۔ تاہم، ایک تکنیکی تفصیل سے آگاہ ہونا یہ ہے کہ بہت سے فائل سسٹمز بہت سی فائلوں کے ساتھ اچھی طرح سے کام نہیں کرتے ہیں۔ ہم نے پایا ہے کہ ایک سادہ حل یہ ہے کہ متعدد ڈائریکٹریز بنائیں، مثلاً مختلف ID رینجز یا ہیش پری فکسز کے لیے۔ ڈیٹا ڈاؤن لوڈ کرنے کے بعد، اگر دستیاب ہو تو metadata میں ہیشز کا استعمال کرتے ہوئے فائلوں کی سالمیت کو چیک کرنا یقینی بنائیں۔ 2. ہدف کا انتخاب قابل رسائی: آپ کے metadata اور ڈیٹا کو سکریپ کرنے سے روکنے کے لیے بہت ساری حفاظتی تہوں کا استعمال نہ کریں۔ خاص بصیرت: آپ کے پاس اس ہدف کے بارے میں کچھ خاص معلومات ہیں، جیسے کہ آپ کو کسی طرح اس مجموعہ تک خاص رسائی حاصل ہے، یا آپ نے ان کے دفاع کو شکست دینے کا طریقہ معلوم کر لیا ہے۔ یہ ضروری نہیں ہے (ہمارا آنے والا منصوبہ کچھ خاص نہیں کرتا)، لیکن یہ یقینی طور پر مدد کرتا ہے! بڑا تو، ہمارے پاس وہ علاقہ ہے جس پر ہم نظر ڈال رہے ہیں، اب ہم کس مخصوص مجموعہ کو عکس بند کریں؟ کچھ چیزیں ہیں جو ایک اچھے ہدف کے لیے اہم ہیں: جب ہم نے اپنی سائنسی درسی کتابیں Library Genesis کے علاوہ دیگر ویب سائٹس پر پائیں، تو ہم نے یہ جاننے کی کوشش کی کہ وہ انٹرنیٹ پر کیسے آئیں۔ پھر ہمیں Z-Library ملا، اور احساس ہوا کہ اگرچہ زیادہ تر کتابیں پہلے وہاں ظاہر نہیں ہوتیں، لیکن وہ بالآخر وہاں پہنچ جاتی ہیں۔ ہم نے اس کے Library Genesis کے ساتھ تعلقات، اور (مالی) ترغیبی ڈھانچے اور اعلیٰ صارف انٹرفیس کے بارے میں سیکھا، جنہوں نے اسے ایک زیادہ مکمل مجموعہ بنا دیا۔ پھر ہم نے کچھ ابتدائی metadata اور ڈیٹا سکریپنگ کی، اور احساس ہوا کہ ہم ان کے IP ڈاؤن لوڈ کی حدود کو نظرانداز کر سکتے ہیں، ہمارے ایک رکن کی بہت سے پراکسی سرورز تک خاص رسائی کا فائدہ اٹھاتے ہوئے۔ جب آپ مختلف اہداف کو تلاش کر رہے ہیں، تو پہلے ہی اپنے نشانات کو چھپانا ضروری ہے، VPNs اور عارضی ای میل پتوں کا استعمال کرتے ہوئے، جس کے بارے میں ہم بعد میں مزید بات کریں گے۔ منفرد: پہلے سے ہی دوسرے منصوبوں کے ذریعے اچھی طرح سے شامل نہ ہو۔ جب ہم کوئی پروجیکٹ کرتے ہیں، تو اس کے کچھ مراحل ہوتے ہیں: یہ مکمل طور پر آزاد مراحل نہیں ہیں، اور اکثر بعد کے مرحلے سے بصیرت آپ کو پہلے مرحلے پر واپس بھیج دیتی ہے۔ مثال کے طور پر، میٹا ڈیٹا سکریپنگ کے دوران آپ کو احساس ہو سکتا ہے کہ آپ کے منتخب کردہ ہدف کے پاس آپ کی مہارت کی سطح سے آگے کے دفاعی میکانزم ہیں (جیسے آئی پی بلاکس)، لہذا آپ واپس جا کر ایک مختلف ہدف تلاش کرتے ہیں۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>) پورے کتابیں ڈیجیٹل تحفظ کے <em>کیوں</em> کے بارے میں لکھی جا سکتی ہیں، اور خاص طور پر پائریٹ آرکائیوزم کے بارے میں، لیکن آئیے ان لوگوں کے لیے ایک مختصر تعارف دیتے ہیں جو زیادہ واقف نہیں ہیں۔ دنیا پہلے سے کہیں زیادہ علم اور ثقافت پیدا کر رہی ہے، لیکن پہلے سے کہیں زیادہ اس کا نقصان بھی ہو رہا ہے۔ انسانیت بڑی حد تک اس ورثے کو تعلیمی پبلشرز، اسٹریمنگ سروسز، اور سوشل میڈیا کمپنیوں جیسے کارپوریشنز کے سپرد کرتی ہے، اور انہوں نے اکثر اچھے محافظ ثابت نہیں کیے ہیں۔ ڈیجیٹل امینیشیا دستاویزی فلم دیکھیں، یا واقعی جیسن سکاٹ کی کوئی بھی بات چیت۔ کچھ ادارے ہیں جو جتنا ممکن ہو محفوظ کرنے کا اچھا کام کرتے ہیں، لیکن وہ قانون کے پابند ہیں۔ بحیثیت قزاق، ہم ایک منفرد پوزیشن میں ہیں کہ ان مجموعوں کو محفوظ کریں جنہیں وہ چھو نہیں سکتے، کاپی رائٹ کے نفاذ یا دیگر پابندیوں کی وجہ سے۔ ہم دنیا بھر میں کئی بار مجموعے کو مرر بھی کر سکتے ہیں، اس طرح مناسب تحفظ کے امکانات کو بڑھا سکتے ہیں۔ فی الحال، ہم دانشورانہ املاک کے فوائد اور نقصانات، قانون توڑنے کی اخلاقیات، سنسرشپ پر غور و فکر، یا علم اور ثقافت تک رسائی کے مسئلے پر بحث میں نہیں جائیں گے۔ ان سب کو راستے سے ہٹا کر، آئیے <em>کیسے</em> میں غوطہ لگائیں۔ ہم شیئر کریں گے کہ ہماری ٹیم کیسے پائریٹ آرکائیوسٹ بنی، اور راستے میں جو سبق ہم نے سیکھے۔ جب آپ اس سفر کا آغاز کرتے ہیں تو بہت سے چیلنجز ہوتے ہیں، اور امید ہے کہ ہم ان میں سے کچھ میں آپ کی مدد کر سکتے ہیں۔ پائریٹ آرکائیوسٹ کیسے بنیں پہلا چیلنج شاید حیران کن ہو سکتا ہے۔ یہ کوئی تکنیکی مسئلہ نہیں ہے، یا قانونی مسئلہ نہیں ہے۔ یہ ایک نفسیاتی مسئلہ ہے۔ اس میں غوطہ لگانے سے پہلے، پائریٹ لائبریری مرر کے بارے میں دو اپ ڈیٹس (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر منتقل کر دیا گیا): ہمیں کچھ انتہائی فراخدلانہ عطیات ملے۔ پہلا $10k تھا ایک گمنام فرد سے جو "بک واریر" کی بھی حمایت کر رہا ہے، جو لائبریری جینیسس کا اصل بانی ہے۔ اس عطیہ کو ممکن بنانے کے لیے بک واریر کا خصوصی شکریہ۔ دوسرا $10k کا عطیہ ایک اور گمنام عطیہ دہندہ سے تھا، جو ہماری آخری ریلیز کے بعد ہم سے رابطے میں آیا، اور مدد کرنے کے لیے متاثر ہوا۔ ہمارے پاس کچھ چھوٹے عطیات بھی تھے۔ آپ کی فراخدلانہ حمایت کے لیے بہت شکریہ۔ ہمارے پاس کچھ دلچسپ نئے منصوبے ہیں جن کی یہ حمایت کرے گا، لہذا جڑے رہیں۔ ہمیں اپنی دوسری ریلیز کے سائز کے ساتھ کچھ تکنیکی مشکلات کا سامنا کرنا پڑا، لیکن ہمارے ٹورینٹس اب اپ اور سیڈنگ کر رہے ہیں۔ ہمیں ایک گمنام فرد سے ہماری مجموعہ کو ان کے بہت تیز رفتار سرورز پر سیڈ کرنے کی فراخدلانہ پیشکش بھی ملی، لہذا ہم ان کی مشینوں پر ایک خاص اپلوڈ کر رہے ہیں، جس کے بعد ہر کوئی جو مجموعہ ڈاؤن لوڈ کر رہا ہے، رفتار میں بڑی بہتری دیکھے گا۔ بلاگ پوسٹس ہیلو، میں اینا ہوں۔ میں نے <a %(wikipedia_annas_archive)s>اینا کا آرکائیو</a> بنایا، جو دنیا کی سب سے بڑی شیڈو لائبریری ہے۔ یہ میرا ذاتی بلاگ ہے، جس میں میں اور میری ٹیم کے اراکین قزاقی، ڈیجیٹل تحفظ، اور مزید کے بارے میں لکھتے ہیں۔ <a %(reddit)s>Reddit</a> پر مجھ سے جڑیں۔ نوٹ کریں کہ یہ ویب سائٹ صرف ایک بلاگ ہے۔ ہم یہاں صرف اپنے الفاظ کی میزبانی کرتے ہیں۔ یہاں کوئی ٹورینٹس یا دیگر کاپی رائٹ شدہ فائلیں میزبانی یا لنک نہیں کی جاتیں۔ <strong>لائبریری</strong> - زیادہ تر لائبریریوں کی طرح، ہم بنیادی طور پر تحریری مواد جیسے کتابوں پر توجہ مرکوز کرتے ہیں۔ ہم مستقبل میں دیگر اقسام کے میڈیا میں توسیع کر سکتے ہیں۔ <strong>آئینہ</strong> - ہم موجودہ لائبریریوں کا صرف ایک آئینہ ہیں۔ ہم تحفظ پر توجہ مرکوز کرتے ہیں، نہ کہ کتابوں کو آسانی سے تلاش کرنے اور ڈاؤن لوڈ کرنے (رسائی) یا لوگوں کی ایک بڑی کمیونٹی کو فروغ دینے پر جو نئی کتابیں فراہم کرتے ہیں (ماخذ)۔ <strong>قزاق</strong> - ہم جان بوجھ کر زیادہ تر ممالک میں کاپی رائٹ قانون کی خلاف ورزی کرتے ہیں۔ یہ ہمیں کچھ ایسا کرنے کی اجازت دیتا ہے جو قانونی ادارے نہیں کر سکتے: اس بات کو یقینی بنانا کہ کتابیں دور دور تک آئینہ دار ہوں۔ <em>ہم اس بلاگ سے فائلوں کا لنک نہیں دیتے۔ براہ کرم خود تلاش کریں۔</em> - انا اور ٹیم (<a %(reddit)s>Reddit</a>) یہ پروجیکٹ (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> میں منتقل کر دیا گیا) انسانی علم کے تحفظ اور آزادی میں حصہ ڈالنے کا ارادہ رکھتا ہے۔ ہم اپنے سے پہلے عظیم لوگوں کے نقش قدم پر چلتے ہوئے، اپنی چھوٹی اور عاجزانہ شراکت کرتے ہیں۔ اس پروجیکٹ کا فوکس اس کے نام سے واضح ہوتا ہے: پہلی لائبریری جس کا ہم نے آئینہ بنایا ہے وہ Z-Library ہے۔ یہ ایک مقبول (اور غیر قانونی) لائبریری ہے۔ انہوں نے Library Genesis کے مجموعے کو لے کر اسے آسانی سے تلاش کرنے کے قابل بنا دیا ہے۔ اس کے علاوہ، انہوں نے نئی کتابوں کی شراکتوں کو موثر طریقے سے حاصل کرنے میں کامیابی حاصل کی ہے، صارفین کو مختلف فوائد کے ساتھ شراکت کرنے کی ترغیب دے کر۔ وہ فی الحال ان نئی کتابوں کو Library Genesis میں واپس نہیں دیتے ہیں۔ اور Library Genesis کے برعکس، وہ اپنے مجموعے کو آسانی سے آئینہ دار نہیں بناتے، جو وسیع تحفظ کو روکتا ہے۔ یہ ان کے کاروباری ماڈل کے لیے اہم ہے، کیونکہ وہ اپنے مجموعے تک بلک میں رسائی کے لیے پیسے وصول کرتے ہیں (روزانہ 10 سے زیادہ کتابیں)۔ ہم غیر قانونی کتابوں کے مجموعے تک بلک رسائی کے لیے پیسے وصول کرنے کے بارے میں اخلاقی فیصلے نہیں کرتے۔ اس میں کوئی شک نہیں کہ زیڈ-لائبریری نے علم تک رسائی کو بڑھانے اور مزید کتابیں حاصل کرنے میں کامیابی حاصل کی ہے۔ ہم یہاں صرف اپنا حصہ ڈالنے کے لیے ہیں: اس نجی مجموعے کے طویل مدتی تحفظ کو یقینی بنانا۔ ہم آپ کو دعوت دینا چاہیں گے کہ ہمارے ٹورینٹس کو ڈاؤن لوڈ اور سیڈ کر کے انسانی علم کو محفوظ اور آزاد کرنے میں مدد کریں۔ ڈیٹا کو کیسے منظم کیا گیا ہے اس کے بارے میں مزید معلومات کے لیے پروجیکٹ پیج دیکھیں۔ ہم آپ کو یہ بھی دعوت دینا چاہیں گے کہ آپ اپنی تجاویز پیش کریں کہ اگلے کون سے مجموعے کا آئینہ بنانا ہے، اور اس کے بارے میں کیسے جانا ہے۔ مل کر ہم بہت کچھ حاصل کر سکتے ہیں۔ یہ بے شمار دیگر میں سے ایک چھوٹا سا تعاون ہے۔ آپ جو کچھ بھی کرتے ہیں اس کے لیے شکریہ۔ قزاقی لائبریری آئینہ متعارف کروا رہے ہیں: 7TB کتابوں کا تحفظ (جو Libgen میں نہیں ہیں) انسانیت کی تحریری وراثت کا 10% o فیصد ہمیشہ کے لیے محفوظ <strong>گوگل۔</strong> آخرکار، انہوں نے گوگل بکس کے لیے یہ تحقیق کی۔ تاہم، ان کا میٹا ڈیٹا بلک میں قابل رسائی نہیں ہے اور اسے سکریپ کرنا کافی مشکل ہے۔ <strong>مختلف انفرادی لائبریری سسٹمز اور آرکائیوز۔</strong> ایسی لائبریریاں اور آرکائیوز ہیں جنہیں اوپر دی گئی کسی بھی چیز کے ذریعہ انڈیکس اور جمع نہیں کیا گیا ہے، اکثر اس لیے کہ وہ کم فنڈڈ ہیں، یا کسی اور وجہ سے وہ اپنا ڈیٹا اوپن لائبریری، OCLC، گوگل وغیرہ کے ساتھ شیئر نہیں کرنا چاہتے۔ ان میں سے بہت سے لوگوں کے پاس انٹرنیٹ کے ذریعے قابل رسائی ڈیجیٹل ریکارڈز ہیں، اور وہ اکثر بہت اچھی طرح سے محفوظ نہیں ہوتے ہیں، لہذا اگر آپ مدد کرنا چاہتے ہیں اور عجیب و غریب لائبریری سسٹمز کے بارے میں سیکھنے میں کچھ مزہ لینا چاہتے ہیں، تو یہ بہترین نقطہ آغاز ہیں۔ <strong>ISBNdb۔</strong> یہ اس بلاگ پوسٹ کا موضوع ہے۔ ISBNdb کتاب کے میٹا ڈیٹا کے لیے مختلف ویب سائٹس کو سکریپ کرتا ہے، خاص طور پر قیمتوں کے ڈیٹا کو، جسے وہ پھر کتاب فروشوں کو فروخت کرتے ہیں، تاکہ وہ اپنی کتابوں کی قیمت باقی مارکیٹ کے مطابق مقرر کر سکیں۔ چونکہ ISBNs آج کل کافی عالمگیر ہیں، انہوں نے مؤثر طریقے سے "ہر کتاب کے لیے ایک ویب صفحہ" بنایا ہے۔ <strong>اوپن لائبریری۔</strong> جیسا کہ پہلے ذکر کیا گیا ہے، یہ ان کا پورا مشن ہے۔ انہوں نے تعاون کرنے والی لائبریریوں اور قومی آرکائیوز سے لائبریری ڈیٹا کی بڑی مقدار حاصل کی ہے، اور ایسا کرتے رہتے ہیں۔ ان کے پاس رضاکار لائبریرین اور ایک تکنیکی ٹیم بھی ہے جو ریکارڈز کو ڈیڈوپلیکیٹ کرنے کی کوشش کر رہی ہے، اور انہیں ہر قسم کے میٹا ڈیٹا کے ساتھ ٹیگ کر رہی ہے۔ سب سے اچھی بات یہ ہے کہ ان کا ڈیٹا سیٹ مکمل طور پر کھلا ہے۔ آپ اسے آسانی سے <a %(openlibrary)s>ڈاؤن لوڈ</a> کر سکتے ہیں۔ <strong>ورلڈ کیٹ۔</strong> یہ ایک ویب سائٹ ہے جو غیر منافع بخش OCLC کے ذریعہ چلائی جاتی ہے، جو لائبریری مینجمنٹ سسٹمز فروخت کرتی ہے۔ وہ بہت سی لائبریریوں سے کتاب کے میٹا ڈیٹا کو جمع کرتے ہیں، اور اسے ورلڈ کیٹ ویب سائٹ کے ذریعے دستیاب کرتے ہیں۔ تاہم، وہ اس ڈیٹا کو فروخت کرکے پیسہ بھی کماتے ہیں، اس لیے یہ بلک ڈاؤن لوڈ کے لیے دستیاب نہیں ہے۔ ان کے پاس کچھ زیادہ محدود بلک ڈیٹا سیٹس مخصوص لائبریریوں کے ساتھ تعاون میں ڈاؤن لوڈ کے لیے دستیاب ہیں۔ 1. "ہمیشہ کے لئے" کی کچھ معقول تعریف کے لئے۔ ;) 2. یقیناً، انسانیت کی تحریری وراثت کتابوں سے کہیں زیادہ ہے، خاص طور پر آج کل۔ اس پوسٹ اور ہماری حالیہ ریلیزز کے لئے ہم کتابوں پر توجہ مرکوز کر رہے ہیں، لیکن ہماری دلچسپیاں مزید آگے بڑھتی ہیں۔ 3. ایرن سوارتز کے بارے میں بہت کچھ کہا جا سکتا ہے، لیکن ہم صرف ان کا مختصر ذکر کرنا چاہتے تھے، کیونکہ وہ اس کہانی میں ایک اہم کردار ادا کرتے ہیں۔ وقت گزرنے کے ساتھ، مزید لوگ ان کا نام پہلی بار سن سکتے ہیں، اور خود ہی اس کہانی میں غوطہ لگا سکتے ہیں۔ <strong>فزیکل کاپیاں۔</strong> ظاہر ہے کہ یہ بہت مددگار نہیں ہے، کیونکہ یہ صرف اسی مواد کی نقلیں ہیں۔ یہ بہت اچھا ہوگا اگر ہم ان تمام تشریحات کو محفوظ کر سکیں جو لوگ کتابوں میں کرتے ہیں، جیسے کہ فرماٹ کی مشہور "حاشیوں میں لکھائی"۔ لیکن افسوس، یہ ایک آرکائیوسٹ کا خواب ہی رہے گا۔ <strong>“ایڈیشنز”.</strong> یہاں آپ کتاب کے ہر منفرد ورژن کو شمار کرتے ہیں۔ اگر اس کے بارے میں کچھ بھی مختلف ہے، جیسے کہ ایک مختلف کور یا ایک مختلف پیش لفظ، تو یہ ایک مختلف ایڈیشن کے طور پر شمار ہوتا ہے۔ <strong>فائلیں۔</strong> جب لائبریری جینیسس، سائ-ہب، یا Z-لائبریری جیسی شیڈو لائبریریوں کے ساتھ کام کرتے ہیں، تو ایک اضافی غور ہوتا ہے۔ ایک ہی ایڈیشن کے متعدد اسکین ہو سکتے ہیں۔ اور لوگ موجودہ فائلوں کے بہتر ورژن بنا سکتے ہیں، متن کو OCR کا استعمال کرتے ہوئے اسکین کرکے، یا ان صفحات کو درست کرکے جو زاویہ پر اسکین کیے گئے تھے۔ ہم ان فائلوں کو صرف ایک ایڈیشن کے طور پر شمار کرنا چاہتے ہیں، جس کے لیے اچھے میٹا ڈیٹا کی ضرورت ہوگی، یا دستاویز کی مماثلت کے اقدامات کا استعمال کرتے ہوئے ڈیڈوپلیکیشن کی ضرورت ہوگی۔ <strong>“کام”.</strong> مثال کے طور پر "ہیری پوٹر اور چیمبر آف سیکریٹس" ایک منطقی تصور کے طور پر، اس کے تمام ورژنز کو شامل کرتے ہوئے، جیسے مختلف تراجم اور دوبارہ اشاعتیں۔ یہ ایک طرح کی مفید تعریف ہے، لیکن یہ فیصلہ کرنا مشکل ہو سکتا ہے کہ کیا شمار ہوتا ہے۔ مثال کے طور پر، ہم شاید مختلف تراجم کو محفوظ کرنا چاہیں گے، حالانکہ صرف معمولی فرق کے ساتھ دوبارہ اشاعتیں اتنی اہم نہیں ہو سکتی ہیں۔ - انا اور ٹیم (<a %(reddit)s>Reddit</a>) قزاق لائبریری آئینہ (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر منتقل کر دیا گیا)، ہمارا مقصد دنیا کی تمام کتابوں کو جمع کرنا اور انہیں ہمیشہ کے لیے محفوظ کرنا ہے۔<sup>1</sup> ہمارے Z-لائبریری ٹورینٹس اور اصل لائبریری جینیسس ٹورینٹس کے درمیان، ہمارے پاس 11,783,153 فائلیں ہیں۔ لیکن حقیقت میں یہ کتنی ہیں؟ اگر ہم ان فائلوں کو صحیح طریقے سے ڈیڈوپلیکیٹ کریں، تو ہم نے دنیا کی تمام کتابوں کا کتنے فیصد محفوظ کیا ہے؟ ہم واقعی کچھ ایسا چاہتے ہیں: آئیے کچھ ابتدائی نمبروں سے شروع کرتے ہیں: Z-لائبریری/لائبجن اور اوپن لائبریری دونوں میں منفرد ISBNs سے زیادہ کتابیں ہیں۔ کیا اس کا مطلب یہ ہے کہ ان میں سے بہت سی کتابوں کے پاس ISBNs نہیں ہیں، یا کیا ISBN میٹا ڈیٹا صرف غائب ہے؟ ہم شاید اس سوال کا جواب دیگر خصوصیات (عنوان، مصنف، ناشر، وغیرہ) کی بنیاد پر خودکار مماثلت کے امتزاج کے ساتھ دے سکتے ہیں، مزید ڈیٹا ذرائع کو شامل کرکے، اور اصل کتاب کے اسکینز سے ISBNs نکال کر (Z-لائبریری/لائبجن کے معاملے میں)۔ ان میں سے کتنے ISBNs منفرد ہیں؟ یہ سب سے بہتر وین ڈایاگرام کے ساتھ واضح کیا گیا ہے: زیادہ واضح طور پر: ہم اس بات پر حیران تھے کہ کتنا کم اوورلیپ ہے! ISBNdb کے پاس بہت زیادہ ISBNs ہیں جو نہ تو Z-Library میں نظر آتے ہیں اور نہ ہی Open Library میں، اور یہی بات (کم لیکن پھر بھی کافی حد تک) دوسرے دو کے لئے بھی درست ہے۔ یہ بہت سے نئے سوالات کو جنم دیتا ہے۔ غیر ٹیگ شدہ کتابوں کو ISBNs کے ساتھ ٹیگ کرنے میں خودکار میچنگ کتنی مددگار ہوگی؟ کیا بہت زیادہ میچز ہوں گے اور اس کے نتیجے میں اوورلیپ میں اضافہ ہوگا؟ نیز، اگر ہم چوتھا یا پانچواں ڈیٹا سیٹ شامل کریں تو کیا ہوگا؟ تب ہمیں کتنا اوورلیپ نظر آئے گا؟ یہ ہمیں ایک نقطہ آغاز فراہم کرتا ہے۔ ہم اب ان تمام ISBNs کو دیکھ سکتے ہیں جو Z-Library ڈیٹا سیٹ میں نہیں تھے، اور جو عنوان/مصنف کے فیلڈز سے بھی میل نہیں کھاتے۔ یہ ہمیں دنیا کی تمام کتابوں کو محفوظ کرنے کا ایک طریقہ فراہم کر سکتا ہے: پہلے انٹرنیٹ سے سکینز کے لئے تلاش کر کے، پھر حقیقی زندگی میں جا کر کتابوں کو سکین کر کے۔ مؤخر الذکر کو بھی ہجوم کی مالی مدد سے یا انعامات کے ذریعے چلایا جا سکتا ہے جو لوگ خاص کتابوں کو ڈیجیٹل دیکھنا چاہتے ہیں۔ یہ سب ایک مختلف وقت کی کہانی ہے۔ اگر آپ اس میں سے کسی کے ساتھ مدد کرنا چاہتے ہیں — مزید تجزیہ؛ مزید میٹا ڈیٹا کو سکریپ کرنا؛ مزید کتابیں تلاش کرنا؛ کتابوں کا OCR کرنا؛ دوسرے ڈومینز کے لیے یہ کرنا (مثلاً مقالے، آڈیو بکس، فلمیں، ٹی وی شوز، میگزین) یا یہاں تک کہ اس ڈیٹا کو ML / بڑے زبان کے ماڈل کی تربیت جیسی چیزوں کے لیے دستیاب کرنا — براہ کرم مجھ سے رابطہ کریں (<a %(reddit)s>ریڈڈیٹ</a>)۔ اگر آپ خاص طور پر ڈیٹا تجزیہ میں دلچسپی رکھتے ہیں، تو ہم اپنے ڈیٹا سیٹس اور اسکرپٹس کو زیادہ آسانی سے استعمال کے قابل فارمیٹ میں دستیاب کرنے پر کام کر رہے ہیں۔ یہ بہت اچھا ہوگا اگر آپ صرف ایک نوٹ بک کو فورک کر سکیں اور اس کے ساتھ کھیلنا شروع کر سکیں۔ آخر میں، اگر آپ اس کام کی حمایت کرنا چاہتے ہیں، تو براہ کرم عطیہ کرنے پر غور کریں۔ یہ مکمل طور پر رضاکارانہ طور پر چلنے والا آپریشن ہے، اور آپ کی شراکت بہت بڑا فرق پیدا کرتی ہے۔ ہر تھوڑی مدد کرتی ہے۔ فی الحال ہم کرپٹو میں عطیات لیتے ہیں؛ انا کے آرکائیو کے ڈونیٹ صفحے پر دیکھیں۔ فیصد کے لیے، ہمیں ایک مخرج کی ضرورت ہے: اب تک شائع ہونے والی کتابوں کی کل تعداد۔<sup>2</sup> گوگل بکس کے خاتمے سے پہلے، اس پروجیکٹ کے ایک انجینئر، لیونڈ ٹائچر، نے <a %(booksearch_blogspot)s>اس نمبر کا اندازہ لگانے کی کوشش کی</a>۔ انہوں نے زبان میں گال کے ساتھ 129,864,880 ("کم از کم اتوار تک") کے ساتھ سامنے آیا۔ انہوں نے دنیا کی تمام کتابوں کا ایک متحد ڈیٹا بیس بنا کر اس نمبر کا اندازہ لگایا۔ اس کے لیے، انہوں نے مختلف ڈیٹا سیٹس کو اکٹھا کیا اور پھر انہیں مختلف طریقوں سے ضم کیا۔ ایک مختصر ضمنی بات کے طور پر، ایک اور شخص ہے جس نے دنیا کی تمام کتابوں کو کیٹلاگ کرنے کی کوشش کی: ایرون سوارتز، مرحوم ڈیجیٹل کارکن اور ریڈڈیٹ کے شریک بانی۔<sup>3</sup> انہوں نے <a %(youtube)s>اوپن لائبریری شروع کی</a> جس کا مقصد "اب تک شائع ہونے والی ہر کتاب کے لیے ایک ویب صفحہ" تھا، جو مختلف ذرائع سے ڈیٹا کو یکجا کرتا ہے۔ جب انہیں تعلیمی مقالوں کو بلک ڈاؤن لوڈ کرنے پر مقدمہ چلایا گیا تو انہوں نے اپنی ڈیجیٹل تحفظ کے کام کی حتمی قیمت ادا کی، جس کی وجہ سے ان کی خودکشی ہوئی۔ کہنے کی ضرورت نہیں، یہ ہماری گروپ کے گمنام ہونے کی ایک وجہ ہے، اور ہم بہت محتاط ہیں۔ اوپن لائبریری ابھی بھی انٹرنیٹ آرکائیو کے لوگوں کے ذریعہ بہادری سے چلائی جا رہی ہے، ایرون کی میراث کو جاری رکھے ہوئے ہے۔ ہم اس پوسٹ میں بعد میں اس پر واپس آئیں گے۔ گوگل کے بلاگ پوسٹ میں، ٹائچر اس نمبر کا اندازہ لگانے میں کچھ چیلنجز بیان کرتا ہے۔ سب سے پہلے، کتاب کیا ہے؟ کچھ ممکنہ تعریفیں ہیں: "ایڈیشنز" "کتابیں" کیا ہیں کی سب سے عملی تعریف معلوم ہوتی ہیں۔ سہولت سے، یہ تعریف منفرد ISBN نمبروں کو تفویض کرنے کے لیے بھی استعمال کی جاتی ہے۔ ایک ISBN، یا انٹرنیشنل اسٹینڈرڈ بک نمبر، بین الاقوامی تجارت کے لیے عام طور پر استعمال ہوتا ہے، کیونکہ یہ بین الاقوامی بارکوڈ سسٹم ("انٹرنیشنل آرٹیکل نمبر") کے ساتھ مربوط ہے۔ اگر آپ اسٹورز میں کتاب بیچنا چاہتے ہیں، تو اسے بارکوڈ کی ضرورت ہوتی ہے، اس لیے آپ کو ISBN ملتا ہے۔ ٹائچر کی بلاگ پوسٹ میں ذکر کیا گیا ہے کہ جب کہ ISBNs مفید ہیں، وہ عالمگیر نہیں ہیں، کیونکہ انہیں واقعی ستر کی دہائی کے وسط میں اپنایا گیا تھا، اور دنیا بھر میں نہیں۔ پھر بھی، ISBN شاید کتاب کے ایڈیشنز کا سب سے زیادہ استعمال ہونے والا شناخت کنندہ ہے، اس لیے یہ ہمارا بہترین نقطہ آغاز ہے۔ اگر ہم دنیا کے تمام ISBNs تلاش کر سکتے ہیں، تو ہمیں ان کتابوں کی ایک مفید فہرست مل جاتی ہے جنہیں ابھی تک محفوظ کرنے کی ضرورت ہے۔ تو، ہمیں ڈیٹا کہاں سے ملتا ہے؟ کچھ موجودہ کوششیں ہیں جو دنیا کی تمام کتابوں کی فہرست مرتب کرنے کی کوشش کر رہی ہیں: اس پوسٹ میں، ہم ایک چھوٹی ریلیز کا اعلان کرتے ہوئے خوش ہیں (ہمارے پچھلے Z-لائبریری ریلیزز کے مقابلے میں)۔ ہم نے زیادہ تر ISBNdb کو سکریپ کیا، اور ڈیٹا کو قزاق لائبریری آئینہ کی ویب سائٹ پر ٹورینٹنگ کے لیے دستیاب کر دیا (ترمیم: <a %(wikipedia_annas_archive)s>آنا کا آرکائیو</a> پر منتقل کر دیا گیا؛ ہم یہاں اسے براہ راست لنک نہیں کریں گے، بس اسے تلاش کریں)۔ یہ تقریباً 30.9 ملین ریکارڈز ہیں (20GB بطور <a %(jsonlines)s>JSON لائنز</a>; 4.4GB gzipped)۔ ان کی ویب سائٹ پر وہ دعویٰ کرتے ہیں کہ ان کے پاس دراصل 32.6 ملین ریکارڈز ہیں، لہذا ہم نے شاید کسی طرح کچھ کھو دیا ہے، یا <em>وہ</em> کچھ غلط کر رہے ہیں۔ کسی بھی صورت میں، فی الحال ہم بالکل یہ نہیں بتائیں گے کہ ہم نے یہ کیسے کیا — ہم اسے قاری کے لیے ایک مشق کے طور پر چھوڑ دیں گے۔ ;-) جو ہم شیئر کریں گے وہ کچھ ابتدائی تجزیہ ہے، دنیا میں کتابوں کی تعداد کا اندازہ لگانے کے قریب پہنچنے کی کوشش کرنا۔ ہم نے تین ڈیٹا سیٹس کو دیکھا: یہ نیا ISBNdb ڈیٹا سیٹ، ہمارے اصل میٹا ڈیٹا کی ریلیز جو ہم نے Z-لائبریری شیڈو لائبریری سے سکریپ کی (جس میں لائبریری جینیسس شامل ہے)، اور اوپن لائبریری ڈیٹا ڈمپ۔ ISBNdb ڈمپ، یا کتنی کتابیں ہمیشہ کے لیے محفوظ ہیں؟ اگر ہم شیڈو لائبریریوں سے فائلوں کو صحیح طریقے سے ڈیڈوپلیکیٹ کریں، تو دنیا کی تمام کتابوں کا کتنا فیصد ہم نے محفوظ کیا ہے؟ <a %(wikipedia_annas_archive)s>اینا کے آرکائیو</a> کے بارے میں اپ ڈیٹس، انسانی تاریخ کی سب سے بڑی واقعی کھلی لائبریری۔ <em>ورلڈ کیٹ کا نیا ڈیزائن</em> ڈیٹا <strong>فارمیٹ؟</strong> <a %(blog)s>آنا کا آرکائیو کنٹینرز (AAC)</a>، جو بنیادی طور پر <a %(jsonlines)s>JSON Lines</a> کو <a %(zstd)s>Zstandard</a> کے ساتھ کمپریس کیا گیا ہے، اور کچھ معیاری semantics شامل ہیں۔ یہ کنٹینرز مختلف قسم کے ریکارڈز کو لپیٹتے ہیں، جو مختلف سکریپس کے مطابق ہیں جو ہم نے تعینات کیے۔ ایک سال پہلے، ہم نے اس سوال کا جواب دینے کے لیے <a %(blog)s>آغاز کیا</a>: <strong>کتابوں کا کون سا فیصد شیڈو لائبریریوں کے ذریعے مستقل طور پر محفوظ کیا گیا ہے؟</strong> آئیے ڈیٹا کی کچھ بنیادی معلومات پر نظر ڈالیں: ایک بار جب کوئی کتاب <a %(wikipedia_library_genesis)s>لائبریری جینیسس</a> جیسی اوپن ڈیٹا شیڈو لائبریری میں آ جاتی ہے، اور اب <a %(wikipedia_annas_archive)s>اینا کا آرکائیو</a> میں، تو یہ دنیا بھر میں (ٹورینٹس کے ذریعے) عکس بند ہو جاتی ہے، اس طرح یہ عملی طور پر ہمیشہ کے لیے محفوظ ہو جاتی ہے۔ کتابوں کے کس فیصد کو محفوظ کیا گیا ہے، اس سوال کا جواب دینے کے لیے ہمیں مخرج جاننے کی ضرورت ہے: کل کتنی کتابیں موجود ہیں؟ اور مثالی طور پر ہمارے پاس صرف ایک عدد نہیں بلکہ حقیقی metadata ہونا چاہیے۔ پھر ہم نہ صرف انہیں شیڈو لائبریریوں کے خلاف ملا سکتے ہیں، بلکہ محفوظ کرنے کے لیے باقی کتابوں کی ایک TODO فہرست بھی بنا سکتے ہیں! ہم اس TODO فہرست کو مکمل کرنے کے لیے ایک عوامی کوشش کا خواب بھی دیکھ سکتے ہیں۔ ہم نے <a %(wikipedia_isbndb_com)s>ISBNdb</a> کو سکریپ کیا، اور <a %(openlibrary)s>Open Library dataset</a> کو ڈاؤن لوڈ کیا، لیکن نتائج غیر تسلی بخش تھے۔ بنیادی مسئلہ یہ تھا کہ ISBNs کا زیادہ تر اوورلیپ نہیں تھا۔ ہمارے بلاگ پوسٹ سے یہ وین ڈایاگرام دیکھیں: ہمیں یہ دیکھ کر بہت حیرت ہوئی کہ ISBNdb اور Open Library کے درمیان کتنا کم اوورلیپ تھا، دونوں ہی مختلف ذرائع جیسے ویب سکریپ اور لائبریری ریکارڈز سے ڈیٹا شامل کرتے ہیں۔ اگر وہ دونوں وہاں موجود زیادہ تر ISBNs کو تلاش کرنے میں اچھا کام کرتے ہیں، تو ان کے دائرے یقینی طور پر کافی اوورلیپ کریں گے، یا ایک دوسرے کا ذیلی مجموعہ ہوگا۔ اس نے ہمیں حیران کر دیا کہ کتنی کتابیں مکمل طور پر ان دائروں سے باہر ہیں؟ ہمیں ایک بڑی ڈیٹا بیس کی ضرورت ہے۔ یہی وقت تھا جب ہم نے دنیا کی سب سے بڑی کتاب ڈیٹا بیس پر نظر ڈالی: <a %(wikipedia_worldcat)s>ورلڈ کیٹ</a>۔ یہ ایک غیر منافع بخش <a %(wikipedia_oclc)s>OCLC</a> کی ملکیتی ڈیٹا بیس ہے، جو دنیا بھر کی لائبریریوں سے metadata ریکارڈز کو جمع کرتی ہے، بدلے میں ان لائبریریوں کو مکمل ڈیٹا سیٹ تک رسائی دیتی ہے، اور انہیں اختتامی صارفین کی تلاش کے نتائج میں دکھاتی ہے۔ حالانکہ OCLC ایک غیر منافع بخش ادارہ ہے، ان کے کاروباری ماڈل کو ان کی ڈیٹا بیس کی حفاظت کی ضرورت ہوتی ہے۔ خیر، OCLC کے دوستو، ہمیں یہ سب کچھ دینے پر معذرت ہے۔ :-) گزشتہ سال کے دوران، ہم نے ورلڈ کیٹ کے تمام ریکارڈز کو بڑی محنت سے سکریپ کیا۔ شروع میں، ہمیں ایک خوش قسمتی ملی۔ ورلڈ کیٹ نے اپنی مکمل ویب سائٹ کا نیا ڈیزائن (اگست 2022 میں) جاری کیا تھا۔ اس میں ان کے بیک اینڈ سسٹمز کی کافی حد تک تبدیلی شامل تھی، جس نے بہت سے سیکیورٹی خامیوں کو متعارف کرایا۔ ہم نے فوراً موقع کا فائدہ اٹھایا، اور محض چند دنوں میں کروڑوں (!) ریکارڈز کو سکریپ کرنے میں کامیاب ہو گئے۔ اس کے بعد، سیکیورٹی خامیوں کو ایک ایک کر کے آہستہ آہستہ ٹھیک کیا گیا، یہاں تک کہ آخری خامی جو ہمیں ملی تھی، تقریباً ایک ماہ پہلے ٹھیک کی گئی۔ اس وقت تک ہمارے پاس تقریباً تمام ریکارڈز تھے، اور ہم صرف تھوڑے بہتر معیار کے ریکارڈز کے لیے جا رہے تھے۔ تو ہم نے محسوس کیا کہ یہ جاری کرنے کا وقت ہے! 1.3B ورلڈ کیٹ اسکریپ <em><strong>مختصر خلاصہ:</strong> اینا کے آرکائیو نے ورلڈ کیٹ (دنیا کی سب سے بڑی لائبریری میٹا ڈیٹا کلیکشن) کو اسکریپ کیا تاکہ ان کتابوں کی فہرست بنائی جا سکے جنہیں محفوظ کرنے کی ضرورت ہے۔</em> ورلڈ کیٹ انتباہ: اس بلاگ پوسٹ کو منسوخ کر دیا گیا ہے۔ ہم نے فیصلہ کیا ہے کہ IPFS ابھی تک پرائم ٹائم کے لیے تیار نہیں ہے۔ ہم اب بھی اینا کے آرکائیو سے IPFS پر فائلوں سے لنک کریں گے جب ممکن ہو، لیکن ہم اسے خود مزید میزبانی نہیں کریں گے، اور نہ ہی ہم دوسروں کو IPFS کا استعمال کرتے ہوئے آئینہ بنانے کی سفارش کرتے ہیں۔ اگر آپ ہماری کلیکشن کو محفوظ رکھنے میں مدد کرنا چاہتے ہیں تو براہ کرم ہمارے ٹورینٹس صفحہ کو دیکھیں۔ IPFS پر Z-Library کو بیج کرنے میں مدد کریں پارٹنر سرور ڈاؤنلوڈ سائ ڈی بی بیرونی ادھار بیرونی ادھار (پرنٹ غیر فعال) بیرونی ڈاؤنلوڈ میٹا ڈیٹا کو دریافت کریں ٹورینٹس میں شامل ہے واپس  (+%(num)s بونس) بغیر معاوضہ ادا شدہ منسوخ شدہ میعاد ختم اینا کی تصدیق کا انتظار ہے غلط نیچے دیا گیا متن انگریزی میں جاری ہے۔ جائیں ری سیٹ کریں آگے آخری اگر آپ کا ای میل پتہ Libgen فورمز پر کام نہیں کرتا، تو ہم <a %(a_mail)s>Proton Mail</a> (مفت) استعمال کرنے کی تجویز دیتے ہیں۔ آپ <a %(a_manual)s>دستی طور پر درخواست</a> بھی کر سکتے ہیں تاکہ آپ کا اکاؤنٹ فعال ہو سکے۔ (شاید <a %(a_browser)s>براؤزر کی تصدیق</a> کی ضرورت ہو — لامحدود ڈاؤن لوڈز!) فاسٹ پارٹنر سرور #%(number)s (تجویز کردہ) (تھوڑا تیز لیکن ویٹ لسٹ کے ساتھ) (کوئی براؤزر تصدیق کی ضرورت نہیں) (کوئی براؤزر تصدیق یا انتظار کی فہرست نہیں) (کوئی انتظار کی فہرست نہیں، لیکن بہت سست ہو سکتا ہے) سست پارٹنر سرور #%(number)s آڈیو بُک کامک بُک کتاب (فکشن) کتاب (غیر فکشن) کتاب (نامعلوم) تحقیقی مضمون رسالہ موسیقی کا اسکور دیگر دستاویز تمام صفحات کو PDF میں تبدیل نہیں کیا جا سکا ‫Libgen.li‏ میں لِنک میسر نہیں ‫Libgen.li‏ میں موجود نہیں ‫Libgen.rs‏ فکشن میں موجود نہیں ‫Libgen.rs‏ غیر فکشن میں موجود نہیں اس فائل پر exiftool چلانے میں ناکام رہا Z-Library میں "خراب فائل" کے طور پر نشان زد کیا گیا ‫Z-Library‏ میں موجود نہیں Z-Library میں "اسپام" کے طور پر نشان زد کیا گیا فائل نہیں کھولی جا سکتی (مثلاً خراب فائل، DRM) کاپی رائٹ دعویٰ ڈاؤن لوڈنگ کے مسائل (مثلاً کنیکٹ نہیں ہو رہا، ایرر میسج، بہت سست) غلط میٹا ڈیٹا (مثلاً عنوان، تفصیل، سرورق کی تصویر) دیگر ناقص معیار (مثلاً فارمیٹنگ کے مسائل، ناقص اسکین معیار، غائب صفحات) سپیم / فائل کو ہٹایا جانا چاہئے (مثال کے طور پر اشتہارات، توہین آمیز مواد) %(amount)s (%(amount_usd)s) %(amount)s کل %(amount)s (%(amount_usd)s) کل شاندار کتابی کیڑا خوش قسمت کتب خانہ دار دلچسپ ڈیٹا ہورڈر شاندار آرکائیوسٹ بونس ڈاؤن لوڈز Cerlalc چیک میٹا ڈیٹا DuXiu 读秀 EBSCOhost ای بُک انڈیکس گوگل بکس گُڈریڈز ہاتھی ٹرسٹ IA IA کنٹرولڈ ڈیجیٹل لینڈنگ ISBNdb ISBN GRP Libgen.li “scimag” کو خارج کرتے ہوئے Libgen.rs غیر افسانوی اور افسانوی Libby میگز ڈی بی Nexus/STC او سی ایل سی (ورلڈ کیٹ) OpenLibrary روسی ریاستی لائبریری Sci-Hub Libgen.li “scimag” کے ذریعے Sci-Hub / Libgen “scimag” Trantor AA پر اپلوڈز زی-لائبریری زی-لائبریری چینی نام کتاب، مصنف، ‫DOI‏، ‫ISBN‏، MD5، … تلاش مصنف تفصیل اور میٹا ڈیٹا کے تبصرے ایڈیشن اصل فائل کا نام ناشر (مخصوص فیلڈ تلاش کریں) عنوان سال اشاعت تکنیکی تفصیلات اس سکے کی کم از کم حد معمول سے زیادہ ہے۔ براہ کرم ایک مختلف مدت یا ایک مختلف سکہ منتخب کریں۔ درخواست مکمل نہیں ہو سکی۔ براہ کرم چند منٹوں میں دوبارہ کوشش کریں، اور اگر یہ بار بار ہوتا ہے تو ہمیں %(email)s پر اسکرین شاٹ کے ساتھ رابطہ کریں۔ ایک نامعلوم خرابی پیش آئی۔ براہ کرم %(email)s پر اسکرین شاٹ کے ساتھ ہم سے رابطہ کریں۔ ادائیگی کی پروسیسنگ میں خرابی۔ براہ کرم ایک لمحہ انتظار کریں اور دوبارہ کوشش کریں۔ اگر مسئلہ 24 گھنٹے سے زیادہ برقرار رہے تو براہ کرم ہمیں %(email)s پر اسکرین شاٹ کے ساتھ رابطہ کریں۔ ہم دنیا کی سب سے بڑی کامکس شیڈو لائبریری کا بیک اپ لینے کے لیے فنڈ ریزر چلا رہے ہیں۔ آپ کی حمایت کا شکریہ! <a href="/donate">عطیہ کریں۔</a> اگر آپ عطیہ نہیں کر سکتے، تو اپنے دوستوں کو بتا کر اور ہمیں <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>، یا <a href="https://t.me/annasarchiveorg">Telegram</a> پر فالو کر کے ہماری حمایت کرنے پر غور کریں۔ <a %(a_request)s>کتابیں طلب کرنے</a> یا چھوٹی (<10k) <a %(a_upload)s>اپ لوڈز</a> کے لیے ہمیں ای میل نہ کریں۔ ایناز آرکائیو DMCA / کاپی رائٹ دعوے رابطے میں رہئے ریڈِٹ متبادلات سلم (%(unaffiliated)s) غیر منسلک Anna’s Archive کو آپ کی مدد کی ضرورت ہے! اگر آپ ابھی عطیہ کرتے ہیں، تو آپ کو <strong>دوگنا</strong> تیز ڈاؤن لوڈز کی تعداد ملتی ہے۔ بہت سے لوگ ہمیں نیچے لانے کی کوشش کرتے ہیں، لیکن ہم مقابلہ کرتے ہیں۔ اگر آپ اس مہینے عطیہ کرتے ہیں، تو آپ کو <strong>دوگنا</strong> تیز ڈاؤن لوڈز کی تعداد ملتی ہے۔ اس مہینے کے آخر تک درست۔ انسانی علم کی بچت: چھٹی کا ایک عظیم تحفہ! رکنیت کو اسی کے مطابق بڑھایا جائے گا۔ شراکت دار سرورز میزبانی کی بندش کی وجہ سے دستیاب نہیں ہیں۔ وہ جلد ہی دوبارہ فعال ہو جائیں گے۔ اینّا کے آرکائیو کی مضبوطی بڑھانے کے لیے، ہم مرر چلانے کے لیے رضاکاروں کی تلاش میں ہیں۔ ہمارے پاس اب عطیہ کا ایک نیا طریقہ کار دستیاب ہے: ‫%(method_name)s‏۔ براہ مہربانی ‫%(donate_link_open_tag)s‏عطیہ کریں‪</a>‏ — اس ویب سائٹ کو چلانا کم خرچ نہیں ہے، اور آپ کا عطیہ ایک فرق قائم کر سکتا ہے۔ بہت شکریہ۔ ایک دوست کو مدعو کریں، اور آپ اور آپ کے دوست دونوں کو %(percentage)s%% بونس تیز ڈاؤن لوڈز ملیں گے! کسی پیارے کو سرپرائز کریں، انہیں ممبرشپ کے ساتھ اکاؤنٹ دیں۔ محبت کے دن کا بہترین تحفہ! مزید جانیں… اکاؤنٹ سرگرمی ایڈوانسڈ اینا کا بلاگ ↗ اینا کا سافٹویئر ↗ بیٹا کوڈز ایکسپلورر ڈیٹاسیٹ عطیہ کریں ڈاؤن لوڈ کردہ فائلیں عمومی سوالات مرکزی صفحہ میٹا ڈیٹا بہتر کریں LLM ڈیٹا لاگ ان / رجسٹر کریں میری عطیات عوامی پروفائل تلاش سیکیورٹی ٹورینٹس ترجمہ کریں ↗ رضاکارانہ خدمات اور انعامات حالیہ ڈاؤن لوڈز: 📚&nbsp;دنیا کی سب سے بڑی اوپن سورس اوپن ڈیٹا لائبریری۔ ⭐️&nbsp;Sci-Hub، Library Genesis، Z-Library، اور مزید کے مررز۔ 📈&nbsp;%(book_any)s کتابیں، %(journal_article)s مقالے، %(book_comic)s کامکس، %(magazine)s میگزین — ہمیشہ کے لیے محفوظ۔  اور  اور مزید دو شو Internet Archive Lending Library LibGen 📚&nbsp;انسانی تاریخ کی سب سے بڑی واقعی کھلی لائبریری۔ 📈&nbsp;%(book_count)s&nbsp;کتابیں، %(paper_count)s&nbsp;مضامین — ہمیشہ کے لیے محفوظ۔ ⭐️&nbsp;ہم %(libraries)s کا عکس بناتے ہیں۔ ہم %(scraped)s کو سکریپ اور اوپن سورس کرتے ہیں۔ ہمارا تمام کوڈ اور ڈیٹا مکمل طور پر اوپن سورس ہے۔ اوپن لائب Sci-Hub ،  📚 دنیا کی سب سے بڑی اوپن سورس اوپن ڈیٹا لائبریری۔<br>⭐️ Scihub، Libgen، Zlib، اور مزید کے آئینے۔ زی-لائب ایناز آرکائیو غلط درخواست۔ %(websites)s پر جائیں۔ دنیا کی سب سے بڑی اوپن سورس اوپن ڈیٹا لائبریری۔ Sci-Hub، Library Genesis، Z-Library، اور مزید کے مررز۔ Anna’s Archive کی تلاش کریں ایناز آرکائیو براہ کرم دوبارہ کوشش کرنے کے لیے ریفریش کریں۔ <a %(a_contact)s>ہم سے رابطہ کریں</a> اگر مسئلہ کئی گھنٹوں تک برقرار رہے۔ 🔥 اس صفحے کو لوڈ کرنے میں مسئلہ <li>1. ہمیں <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>، یا <a href="https://t.me/annasarchiveorg">Telegram</a> پر فالو کریں۔</li><li>2. Twitter، Reddit، Tiktok، Instagram، اپنے مقامی کیفے یا لائبریری میں، یا جہاں بھی جائیں، Anna’s Archive کے بارے میں بات کریں! ہم گیٹ کیپنگ پر یقین نہیں رکھتے — اگر ہمیں ہٹا دیا گیا تو ہم کہیں اور دوبارہ نمودار ہو جائیں گے، کیونکہ ہمارا سارا کوڈ اور ڈیٹا مکمل طور پر اوپن سورس ہے۔</li><li>3. اگر آپ قابل ہیں، تو <a href="/donate">عطیہ کرنے</a> پر غور کریں۔</li><li>4. ہماری ویب سائٹ کو مختلف زبانوں میں <a href="https://translate.annas-software.org/">ترجمہ کرنے</a> میں مدد کریں۔</li><li>5. اگر آپ سافٹ ویئر انجینئر ہیں، تو ہمارے <a href="https://annas-software.org/">اوپن سورس</a> میں تعاون کرنے، یا ہمارے <a href="/datasets">ٹورینٹس</a> کو سیڈ کرنے پر غور کریں۔</li> 10. اپنی زبان میں Anna’s Archive کے لئے ویکیپیڈیا صفحہ بنائیں یا اس کی دیکھ بھال میں مدد کریں۔ 11. ہم چھوٹے، ذوق دار اشتہارات لگانے کے خواہاں ہیں۔ اگر آپ Anna’s Archive پر اشتہار دینا چاہتے ہیں، تو براہ کرم ہمیں بتائیں۔ 6. اگر آپ ایک سیکیورٹی محقق ہیں، تو ہم آپ کی مہارتوں کو دونوں حملہ اور دفاع کے لیے استعمال کر سکتے ہیں۔ ہماری <a %(a_security)s>سیکیورٹی</a> صفحہ دیکھیں۔ 7. ہم گمنام تاجروں کے لئے ادائیگی کے ماہرین کی تلاش میں ہیں۔ کیا آپ ہمیں مزید آسان طریقے شامل کرنے میں مدد کر سکتے ہیں؟ PayPal، WeChat، گفٹ کارڈز۔ اگر آپ کسی کو جانتے ہیں، تو براہ کرم ہم سے رابطہ کریں۔ 8. ہم ہمیشہ مزید سرور کی گنجائش کی تلاش میں رہتے ہیں۔ 9. آپ فائل کے مسائل کی رپورٹنگ، تبصرے چھوڑنے، اور اس ویب سائٹ پر فہرستیں بنانے میں مدد کر سکتے ہیں۔ آپ <a %(a_upload)s>مزید کتابیں اپ لوڈ</a> کر کے، یا موجودہ کتابوں کے فائل مسائل یا فارمیٹنگ کو درست کر کے بھی مدد کر سکتے ہیں۔ رضاکارانہ خدمات کے بارے میں مزید تفصیلی معلومات کے لیے، ہماری <a %(a_volunteering)s>رضاکارانہ خدمات اور انعامات</a> صفحہ دیکھیں۔ ہم معلومات کی مفت رسائی اور علم و تہذیب کے تحفظ کے اصول پر یقین رکھتے ہیں۔ اس سرچ انجن کےذریعے ہم عظیم افراد کی علمی کاوشوں کو آگے بڑھا رہے ہیں۔ ہم ان تمام افراد کی محنت کا احترام کرتے ہیں جنھوں نے مختلف خفیہ آن لائن کتب خانے بنائے، اور ہم امید رکھتے ہیں کہ یہ سرچ انجن ان تک رسائی کو وسیع تر کرے گا۔ ہماری پیشرفت سے باخبر رہنے کے لیے، اینا کو <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> یا <a href="https://t.me/annasarchiveorg">Telegram</a> پر فالو کریں۔ سوالات اور آراء کے لیے براہ کرم اینا سے %(email)s پر رابطہ کریں۔ اکاؤنٹ آئی ڈی: %(account_id)s لاگ آؤٹ ❌ کچھ غلط ہو گیا ہے۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ ✅ آپ اب لاگ آؤٹ ہو چکے ہیں۔ دوبارہ لاگ ان کرنے کے لئے صفحہ کو ری لوڈ کریں۔ تیز ڈاؤن لوڈ استعمال کیے گئے (پچھلے 24 گھنٹوں میں): <strong>%(used)s / %(total)s</strong> رکنیت: <strong>%(tier_name)s</strong> تک %(until_date)s <a %(a_extend)s>(توسیع)</a> آپ متعدد رکنیتوں کو جمع کر سکتے ہیں (تیز ڈاؤن لوڈز ہر 24 گھنٹوں میں ملا کر دیے جائیں گے)۔ رکنیت: <strong>کوئی نہیں</strong> <a %(a_become)s>(رکن بنیں)</a> اگر آپ اپنی رکنیت کو اعلیٰ درجے میں اپ گریڈ کرنے میں دلچسپی رکھتے ہیں تو اینا سے %(email)s پر رابطہ کریں۔ عوامی پروفائل: %(profile_link)s خفیہ کلید (شیئر نہ کریں!): %(secret_key)s دکھائیں یہاں شامل ہوں! ہمارے گروپ میں شامل ہونے کے لیے <a %(a_tier)s>اعلی درجے</a> میں اپ گریڈ کریں۔ خصوصی ٹیلیگرام گروپ: %(link)s اکاؤنٹ کون سے ڈاؤن لوڈز؟ لاگ ان کریں اپنی کلید نہ کھوئیں! غلط خفیہ کلید۔ اپنی کلید کی تصدیق کریں اور دوبارہ کوشش کریں، یا نیچے نیا اکاؤنٹ رجسٹر کریں۔ خفیہ کلید لاگ ان کرنے کے لیے اپنا خفیہ کلید درج کریں: پرانا ای میل پر مبنی اکاؤنٹ؟ اپنا <a %(a_open)s>ای میل یہاں درج کریں</a>۔ نیا اکاؤنٹ رجسٹر کریں ابھی تک اکاؤنٹ نہیں ہے؟ رجسٹریشن کامیاب! آپ کی خفیہ کلید ہے: <span %(span_key)s>%(key)s</span> اس کلید کو احتیاط سے محفوظ کریں۔ اگر آپ اسے کھو دیتے ہیں، تو آپ اپنے اکاؤنٹ تک رسائی کھو دیں گے۔ <li %(li_item)s><strong>بک مارک.</strong> آپ اس صفحے کو بک مارک کر سکتے ہیں تاکہ اپنی کلید کو بازیافت کر سکیں۔</li><li %(li_item)s><strong>ڈاؤن لوڈ.</strong> اپنی کلید ڈاؤن لوڈ کرنے کے لئے <a %(a_download)s>اس لنک</a> پر کلک کریں۔</li><li %(li_item)s><strong>پاس ورڈ مینیجر.</strong> جب آپ نیچے کلید درج کریں تو اسے محفوظ کرنے کے لئے پاس ورڈ مینیجر استعمال کریں۔</li> لاگ ان / رجسٹر کریں براؤزر کی تصدیق انتباہ: کوڈ میں غلط یونیکوڈ کریکٹرز ہیں، اور مختلف حالات میں غلط طریقے سے کام کر سکتا ہے۔ خام بائنری کو URL میں base64 نمائندگی سے ڈی کوڈ کیا جا سکتا ہے۔ تفصیل لیبل پیشوند کسی مخصوص کوڈ کے لیے URL ویب سائٹ کوڈز جو “%(prefix_label)s” سے شروع ہوتے ہیں براہ کرم ان صفحات کو سکریپ نہ کریں۔ اس کے بجائے ہم <a %(a_import)s>جنریٹ کرنے</a> یا <a %(a_download)s>ڈاؤن لوڈ کرنے</a> کی سفارش کرتے ہیں ہمارے ElasticSearch اور MariaDB ڈیٹا بیسز، اور ہمارا <a %(a_software)s>اوپن سورس کوڈ</a> چلائیں۔ خام ڈیٹا کو JSON فائلوں کے ذریعے دستی طور پر دریافت کیا جا سکتا ہے جیسے <a %(a_json_file)s>یہ ایک</a>۔ %(count)s سے کم ریکارڈز عام URL کوڈز ایکسپلورر فہرست بندی کوڈز کو دریافت کریں جن کے ساتھ ریکارڈز ٹیگ کیے گئے ہیں، پیشوند کے ذریعے۔ "ریکارڈز" کالم دکھاتا ہے کہ کتنے ریکارڈز کو دیے گئے پیشوند کے ساتھ کوڈز کے ساتھ ٹیگ کیا گیا ہے، جیسا کہ سرچ انجن میں دیکھا گیا ہے (صرف میٹا ڈیٹا ریکارڈز سمیت)۔ "کوڈز" کالم دکھاتا ہے کہ کتنے حقیقی کوڈز کے پاس دیے گئے پیشوند ہیں۔ معروف کوڈ پیشوند “%(key)s” مزید… پیشوند %(count)s ریکارڈ جو “%(prefix_label)s” سے مطابقت رکھتے ہیں %(count)s ریکارڈز جو “%(prefix_label)s” سے مطابقت رکھتے ہیں کوڈز ریکارڈز “%%” کو کوڈ کی قیمت سے تبدیل کیا جائے گا آنا کے آرکائیو میں تلاش کریں کوڈز مخصوص کوڈ کے لیے URL: “%(url)s” اس صفحے کو تیار کرنے میں کچھ وقت لگ سکتا ہے، اسی لیے اس کے لیے کلاؤڈ فلیر کیپچا کی ضرورت ہوتی ہے۔ <a %(a_donate)s>ممبران</a> کیپچا کو چھوڑ سکتے ہیں۔ بدسلوکی کی اطلاع دی گئی: بہتر ورژن کیا آپ اس صارف کو بدسلوکی یا نامناسب رویے کے لیے رپورٹ کرنا چاہتے ہیں؟ فائل مسئلہ: %(file_issue)s پوشیدہ تبصرہ جواب دیں بدسلوکی کی رپورٹ کریں آپ نے اس صارف کو بدسلوکی کے لیے رپورٹ کیا۔ اس ای میل پر کاپی رائٹ کے دعوے نظر انداز کر دیے جائیں گے؛ براہ کرم فارم استعمال کریں۔ ای میل دکھائیں ہم آپ کی آراء اور سوالات کا خیرمقدم کرتے ہیں! تاہم، ہمیں ملنے والے اسپام اور فضول ای میلز کی تعداد کی وجہ سے، براہ کرم ان شرائط کو سمجھنے کی تصدیق کے لیے باکسز کو چیک کریں۔ کاپی رائٹ کے دعووں کے بارے میں ہم سے رابطہ کرنے کے کسی اور طریقے کو خود بخود حذف کر دیا جائے گا۔ DMCA / کاپی رائٹ دعووں کے لئے، <a %(a_copyright)s>اس فارم</a> کا استعمال کریں۔ رابطہ ای میل Anna’s Archive پر URLs (ضروری)۔ ایک لائن میں ایک۔ براہ کرم صرف وہ URLs شامل کریں جو کتاب کے بالکل ایک ہی ایڈیشن کی وضاحت کرتی ہیں۔ اگر آپ متعدد کتابوں یا متعدد ایڈیشنز کے لیے دعویٰ کرنا چاہتے ہیں، تو براہ کرم اس فارم کو متعدد بار جمع کرائیں۔ وہ دعوے جو متعدد کتابوں یا ایڈیشنز کو ایک ساتھ جوڑتے ہیں، مسترد کر دیے جائیں گے۔ پتہ (ضروری) ماخذ مواد کی واضح وضاحت (ضروری) ای میل (ضروری) ماخذ مواد کے URLs، ایک لائن میں ایک (ضروری)۔ براہ کرم جتنے ممکن ہو شامل کریں، تاکہ ہمیں آپ کے دعوے کی تصدیق کرنے میں مدد ملے (مثلاً Amazon، WorldCat، Google Books، DOI)۔ ماخذ مواد کے ISBNs (اگر قابل اطلاق ہو)۔ ایک لائن میں ایک۔ براہ کرم صرف وہی شامل کریں جو بالکل اس ایڈیشن سے میل کھاتے ہیں جس کے لیے آپ کاپی رائٹ دعویٰ کر رہے ہیں۔ آپ کا نام (ضروری) ❌ کچھ غلط ہو گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ ✅ آپ کا کاپی رائٹ دعویٰ جمع کرانے کے لیے شکریہ۔ ہم اسے جلد از جلد جائزہ لیں گے۔ براہ کرم دوسرا دعویٰ جمع کرانے کے لیے صفحہ دوبارہ لوڈ کریں۔ <a %(a_openlib)s>Open Library</a> URLs ماخذ مواد کے، ایک لائن میں ایک۔ براہ کرم اپنے ماخذ مواد کے لیے Open Library میں تلاش کرنے کے لیے ایک لمحہ نکالیں۔ یہ ہمیں آپ کے دعوے کی تصدیق کرنے میں مدد دے گا۔ فون نمبر (ضروری) بیان اور دستخط (ضروری) دعویٰ جمع کروائیں اگر آپ کے پاس کوئی DCMA یا دیگر کاپی رائٹ دعویٰ ہے، تو براہ کرم اس فارم کو جتنا ممکن ہو درست طریقے سے پُر کریں۔ اگر آپ کو کسی مسئلے کا سامنا ہو، تو براہ کرم ہمارے مخصوص DMCA ایڈریس پر ہم سے رابطہ کریں: %(email)s۔ نوٹ کریں کہ اس ایڈریس پر بھیجے گئے دعوے پر کارروائی نہیں کی جائے گی، یہ صرف سوالات کے لیے ہے۔ براہ کرم اپنے دعوے جمع کرانے کے لیے نیچے دیے گئے فارم کا استعمال کریں۔ DMCA / کاپی رائٹ دعویٰ فارم Anna’s Archive پر مثال ریکارڈ Anna’s Archive کے ذریعے ٹورینٹس Anna’s Archive Containers فارمیٹ میٹا ڈیٹا درآمد کرنے کے اسکرپٹس اگر آپ اس ڈیٹا سیٹ کو <a %(a_archival)s>آرکائیول</a> یا <a %(a_llm)s>ایل ایل ایم ٹریننگ</a> کے مقاصد کے لیے مرر کرنے میں دلچسپی رکھتے ہیں، تو براہ کرم ہم سے رابطہ کریں۔ آخری اپ ڈیٹ: %(date)s مرکزی %(source)s ویب سائٹ میٹا ڈیٹا دستاویزات (زیادہ تر فیلڈز) Anna’s Archive کے ذریعے آئینہ دار فائلیں: %(count)s (%(percent)s%%) وسائل کل فائلیں: %(count)s کل فائل سائز: %(size)s اس ڈیٹا کے بارے میں ہماری بلاگ پوسٹ <a %(duxiu_link)s>Duxiu</a> ایک وسیع ڈیٹا بیس ہے جس میں اسکین شدہ کتابیں شامل ہیں، جو <a %(superstar_link)s>SuperStar Digital Library Group</a> نے تیار کی ہیں۔ زیادہ تر تعلیمی کتابیں ہیں، جو یونیورسٹیوں اور لائبریریوں کو ڈیجیٹل طور پر دستیاب کرنے کے لیے اسکین کی گئی ہیں۔ ہماری انگریزی بولنے والی سامعین کے لیے، <a %(princeton_link)s>پرنسٹن</a> اور <a %(uw_link)s>یونیورسٹی آف واشنگٹن</a> کے پاس اچھے جائزے ہیں۔ اس کے علاوہ ایک بہترین مضمون بھی ہے جو مزید پس منظر فراہم کرتا ہے: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>۔ Duxiu کی کتابیں طویل عرصے سے چینی انٹرنیٹ پر غیر قانونی طور پر دستیاب ہیں۔ عام طور پر انہیں دوبارہ فروخت کرنے والوں کے ذریعہ ایک ڈالر سے بھی کم قیمت پر فروخت کیا جاتا ہے۔ انہیں عام طور پر گوگل ڈرائیو کے چینی متبادل کا استعمال کرتے ہوئے تقسیم کیا جاتا ہے، جسے اکثر زیادہ اسٹوریج کی جگہ کے لیے ہیک کیا گیا ہے۔ کچھ تکنیکی تفصیلات <a %(link1)s>یہاں</a> اور <a %(link2)s>یہاں</a> مل سکتی ہیں۔ اگرچہ کتابیں نیم عوامی طور پر تقسیم کی گئی ہیں، لیکن انہیں بڑی مقدار میں حاصل کرنا کافی مشکل ہے۔ یہ ہمارے TODO لسٹ میں سب سے اوپر تھا، اور ہم نے اس کے لیے کئی مہینوں کی مکمل وقتی کام مختص کیا۔ تاہم، 2023 کے آخر میں ایک ناقابل یقین، حیرت انگیز، اور باصلاحیت رضاکار نے ہم سے رابطہ کیا، اور بتایا کہ انہوں نے یہ سارا کام پہلے ہی کر لیا ہے — بڑی قیمت پر۔ انہوں نے ہمیں مکمل مجموعہ شیئر کیا، بغیر کسی بدلے کی توقع کے، سوائے طویل مدتی تحفظ کی ضمانت کے۔ واقعی قابل ذکر۔ ہمارے رضاکاروں سے مزید معلومات (خام نوٹس): ہماری <a %(a_href)s>بلاگ پوسٹ</a> سے ماخوذ۔ DuXiu 读秀 %(count)s فائل %(count)s فائلیں یہ ڈیٹا سیٹ <a %(a_datasets_openlib)s>Open Library ڈیٹا سیٹ</a> سے قریبی تعلق رکھتا ہے۔ اس میں IA کی کنٹرولڈ ڈیجیٹل لینڈنگ لائبریری سے تمام میٹا ڈیٹا اور فائلوں کا ایک بڑا حصہ شامل ہے۔ اپ ڈیٹس <a %(a_aac)s>Anna’s Archive Containers فارمیٹ</a> میں جاری کی جاتی ہیں۔ یہ ریکارڈز براہ راست Open Library ڈیٹا سیٹ سے حوالہ دیے جا رہے ہیں، لیکن ان میں وہ ریکارڈز بھی شامل ہیں جو Open Library میں نہیں ہیں۔ ہمارے پاس کمیونٹی ممبران کے ذریعے سالوں سے سکریپ کیے گئے متعدد ڈیٹا فائلز بھی ہیں۔ مجموعہ دو حصوں پر مشتمل ہے۔ آپ کو تمام ڈیٹا حاصل کرنے کے لیے دونوں حصوں کی ضرورت ہے (سوائے ان ٹورینٹس کے جو ٹورینٹس صفحے پر کراس آؤٹ کیے گئے ہیں)۔ ڈیجیٹل لینڈنگ لائبریری ہماری پہلی ریلیز، اس سے پہلے کہ ہم نے <a %(a_aac)s>Anna’s Archive Containers (AAC) فارمیٹ</a> کو معیاری بنایا۔ اس میں میٹا ڈیٹا (جیسن اور ایکس ایم ایل)، پی ڈی ایفز (acsm اور lcpdf ڈیجیٹل لینڈنگ سسٹمز سے)، اور کور تھمب نیلز شامل ہیں۔ AAC استعمال کرتے ہوئے اضافی نئی ریلیزیں۔ صرف 2023-01-01 کے بعد کے ٹائم اسٹیمپس کے ساتھ میٹا ڈیٹا شامل ہے، کیونکہ باقی پہلے ہی "ia" کے ذریعے شامل کیا جا چکا ہے۔ اس کے علاوہ تمام پی ڈی ایف فائلیں، اس بار acsm اور "bookreader" (IA کا ویب ریڈر) لینڈنگ سسٹمز سے۔ نام بالکل صحیح نہ ہونے کے باوجود، ہم پھر بھی bookreader فائلوں کو ia2_acsmpdf_files کلیکشن میں شامل کرتے ہیں، کیونکہ وہ ایک دوسرے سے مختلف ہیں۔ IA کنٹرولڈ ڈیجیٹل لینڈنگ 98%%+ فائلیں تلاش کی جا سکتی ہیں۔ ہمارا مشن دنیا کی تمام کتابوں (اور ساتھ ہی کاغذات، رسائل وغیرہ) کو محفوظ کرنا اور انہیں وسیع پیمانے پر قابل رسائی بنانا ہے۔ ہم یقین رکھتے ہیں کہ تمام کتابوں کو وسیع پیمانے پر مرر کیا جانا چاہیے، تاکہ ریڈنڈنسی اور ریزیلینسی کو یقینی بنایا جا سکے۔ یہی وجہ ہے کہ ہم مختلف ذرائع سے فائلیں اکٹھا کر رہے ہیں۔ کچھ ذرائع مکمل طور پر کھلے ہیں اور انہیں بلک میں مرر کیا جا سکتا ہے (جیسے Sci-Hub)۔ دیگر بند اور حفاظتی ہیں، لہذا ہم ان کی کتابوں کو "آزاد" کرنے کے لیے انہیں سکریپ کرنے کی کوشش کرتے ہیں۔ دیگر کہیں درمیان میں آتے ہیں۔ ہمارا تمام ڈیٹا <a %(a_torrents)s>ٹورنٹ</a> کیا جا سکتا ہے، اور ہمارا تمام میٹا ڈیٹا <a %(a_anna_software)s>جنریٹ</a> یا <a %(a_elasticsearch)s>ڈاؤن لوڈ</a> کیا جا سکتا ہے جیسے ElasticSearch اور MariaDB ڈیٹا بیسز۔ خام ڈیٹا کو JSON فائلوں کے ذریعے دستی طور پر دریافت کیا جا سکتا ہے جیسے <a %(a_dbrecord)s>یہ</a>۔ میٹا ڈیٹا ISBN ویب سائٹ آخری اپ ڈیٹ: %(isbn_country_date)s (%(link)s) وسائل بین الاقوامی ISBN ایجنسی باقاعدگی سے وہ حدود جاری کرتی ہے جو اس نے قومی ISBN ایجنسیوں کو مختص کی ہیں۔ اس سے ہم یہ اخذ کر سکتے ہیں کہ یہ ISBN کس ملک، علاقے، یا زبان کے گروپ سے تعلق رکھتا ہے۔ ہم فی الحال اس ڈیٹا کو بالواسطہ طور پر استعمال کرتے ہیں، <a %(a_isbnlib)s>isbnlib</a> پائیتھون لائبریری کے ذریعے۔ ISBN ملک کی معلومات یہ ستمبر 2022 کے دوران isbndb.com پر کی گئی بہت سی کالز کا ڈمپ ہے۔ ہم نے تمام ISBN رینجز کو کور کرنے کی کوشش کی۔ یہ تقریباً 30.9 ملین ریکارڈز ہیں۔ ان کی ویب سائٹ پر وہ دعویٰ کرتے ہیں کہ ان کے پاس دراصل 32.6 ملین ریکارڈز ہیں، لہذا ہم شاید کچھ چھوٹ گئے ہوں، یا <em>وہ</em> کچھ غلط کر رہے ہوں۔ JSON جوابات ان کے سرور سے تقریباً خام ہیں۔ ایک ڈیٹا کوالٹی مسئلہ جو ہم نے نوٹ کیا، وہ یہ ہے کہ ISBN-13 نمبرز جو "978-" کے مختلف پری فکس سے شروع ہوتے ہیں، ان میں اب بھی ایک "isbn" فیلڈ شامل ہوتی ہے جو صرف ISBN-13 نمبر ہوتا ہے جس کے پہلے 3 نمبر کاٹ دیے جاتے ہیں (اور چیک ڈیجٹ دوبارہ حساب کیا جاتا ہے)۔ یہ ظاہر ہے کہ غلط ہے، لیکن وہ ایسا ہی کرتے ہیں، لہذا ہم نے اسے تبدیل نہیں کیا۔ ایک اور ممکنہ مسئلہ جس کا آپ سامنا کر سکتے ہیں، یہ حقیقت ہے کہ "isbn13" فیلڈ میں ڈپلیکیٹس ہیں، لہذا آپ اسے ڈیٹا بیس میں پرائمری کی کے طور پر استعمال نہیں کر سکتے۔ "isbn13" + "isbn" فیلڈز مل کر منفرد معلوم ہوتی ہیں۔ ریلیز 1 (2022-10-31) فکشن ٹورینٹس پیچھے ہیں (اگرچہ IDs ~4-6M ٹورینٹ نہیں ہوئے کیونکہ وہ ہمارے Zlib ٹورینٹس کے ساتھ اوورلیپ کرتے ہیں)۔ ہمارا بلاگ پوسٹ کامک بکس کی ریلیز کے بارے میں Anna’s Archive پر کامکس ٹورینٹس مختلف Library Genesis فورکس کی پس منظر کہانی کے لیے، <a %(a_libgen_rs)s>Libgen.rs</a> کا صفحہ دیکھیں۔ Libgen.li میں زیادہ تر وہی مواد اور میٹا ڈیٹا شامل ہے جو Libgen.rs میں ہے، لیکن اس کے اوپر کچھ مجموعے ہیں، یعنی کامکس، میگزینز، اور معیاری دستاویزات۔ اس نے <a %(a_scihub)s>Sci-Hub</a> کو اپنے میٹا ڈیٹا اور سرچ انجن میں بھی ضم کر دیا ہے، جو ہم اپنی ڈیٹا بیس کے لیے استعمال کرتے ہیں۔ اس لائبریری کا میٹا ڈیٹا آزادانہ طور پر <a %(a_libgen_li)s>libgen.li پر</a> دستیاب ہے۔ تاہم، یہ سرور سست ہے اور ٹوٹے ہوئے کنکشنز کو دوبارہ شروع کرنے کی حمایت نہیں کرتا۔ وہی فائلیں <a %(a_ftp)s>ایک FTP سرور</a> پر بھی دستیاب ہیں، جو بہتر کام کرتا ہے۔ غیر افسانوی ادب بھی مختلف نظر آتا ہے، لیکن بغیر کسی نئے ٹورینٹس کے۔ ایسا لگتا ہے کہ یہ ابتدائی 2022 سے ہوا ہے، حالانکہ ہم نے اس کی تصدیق نہیں کی ہے۔ Libgen.li کے منتظم کے مطابق، "fiction_rus" (روسی افسانہ) مجموعہ کو <a %(a_booktracker)s>booktracker.org</a> سے باقاعدگی سے جاری کردہ ٹورینٹس کے ذریعے کور کیا جانا چاہیے، خاص طور پر <a %(a_flibusta)s>flibusta</a> اور <a %(a_librusec)s>lib.rus.ec</a> ٹورینٹس (جنہیں ہم <a %(a_torrents)s>یہاں</a> عکس کرتے ہیں، حالانکہ ہم نے ابھی تک یہ طے نہیں کیا ہے کہ کون سے ٹورینٹس کس فائل سے مطابقت رکھتے ہیں)۔ افسانوی مجموعہ کے اپنے ٹورینٹس ہیں (جو <a %(a_href)s>Libgen.rs</a> سے مختلف ہیں) جو %(start)s سے شروع ہوتے ہیں۔ کچھ رینجز جن کے ٹورینٹس نہیں ہیں (جیسے کہ افسانوی رینجز f_3463000 سے f_4260000) ممکنہ طور پر Z-Library (یا دیگر نقل) فائلیں ہیں، حالانکہ ہم ان رینجز میں lgli-منفرد فائلوں کے لیے کچھ ڈیڈوپلیکیشن کرنا اور ٹورینٹس بنانا چاہ سکتے ہیں۔ تمام مجموعوں کے اعدادوشمار <a %(a_href)s>libgen کی ویب سائٹ</a> پر مل سکتے ہیں۔ زیادہ تر اضافی مواد کے لیے ٹورینٹس دستیاب ہیں، خاص طور پر کامکس، میگزینز، اور معیاری دستاویزات کے ٹورینٹس آنا کے آرکائیو کے ساتھ تعاون میں جاری کیے گئے ہیں۔ نوٹ کریں کہ "libgen.is" کا حوالہ دینے والی ٹورینٹ فائلیں واضح طور پر <a %(a_libgen)s>Libgen.rs</a> کے آئینے ہیں ("is" ایک مختلف ڈومین ہے جو Libgen.rs استعمال کرتا ہے)۔ میٹا ڈیٹا استعمال کرنے میں ایک مددگار وسیلہ <a %(a_href)s>یہ صفحہ</a> ہے۔ %(icon)s ان کا "fiction_rus" مجموعہ (روسی افسانہ) کے لیے کوئی مخصوص ٹورینٹس نہیں ہیں، لیکن یہ دوسروں کے ٹورینٹس کے ذریعے کور کیا جاتا ہے، اور ہم ایک <a %(fiction_rus)s>عکس</a> رکھتے ہیں۔ آنا کے آرکائیو پر روسی افسانوی ٹورینٹس Anna’s Archive پر فکشن ٹورینٹس بحث فورم میٹا ڈیٹا FTP کے ذریعے میٹا ڈیٹا Anna’s Archive پر میگزین ٹورینٹس میٹا ڈیٹا فیلڈ کی معلومات دیگر ٹورینٹس کا آئینہ (اور منفرد فکشن اور کامکس ٹورینٹس) آنا کے آرکائیو پر معیاری دستاویزات کے ٹورینٹس Libgen.li ٹورینٹس Anna’s Archive کے ذریعے (کتابوں کے کور) Library Genesis اپنی ڈیٹا کو بلک میں ٹورینٹس کے ذریعے فراخدلی سے فراہم کرنے کے لئے مشہور ہے۔ ہماری Libgen کلیکشن میں اضافی ڈیٹا شامل ہے جو وہ براہ راست جاری نہیں کرتے، ان کے ساتھ شراکت داری میں۔ Library Genesis کے ساتھ کام کرنے والے تمام افراد کا بہت شکریہ! ہمارا بلاگ کتابوں کے کور کی ریلیز کے بارے میں یہ صفحہ ".rs" ورژن کے بارے میں ہے۔ یہ اپنے میٹا ڈیٹا اور اپنی کتابوں کے کیٹلاگ کے مکمل مواد کو باقاعدگی سے شائع کرنے کے لیے جانا جاتا ہے۔ اس کی کتابوں کی کلیکشن فکشن اور نان فکشن حصے میں تقسیم ہے۔ میٹا ڈیٹا استعمال کرنے میں ایک مددگار وسیلہ <a %(a_metadata)s>یہ صفحہ</a> ہے (آئی پی رینجز کو بلاک کرتا ہے، وی پی این کی ضرورت ہو سکتی ہے)۔ 2024-03 تک، نئے ٹورینٹس <a %(a_href)s>اس فورم تھریڈ</a> میں پوسٹ کیے جا رہے ہیں (آئی پی رینجز کو بلاک کرتا ہے، وی پی این کی ضرورت ہو سکتی ہے)۔ فکشن ٹورینٹس Anna’s Archive پر Libgen.rs فکشن ٹورینٹس Libgen.rs بحث فورم Libgen.rs میٹا ڈیٹا Libgen.rs میٹا ڈیٹا فیلڈ کی معلومات Libgen.rs نان فکشن ٹورینٹس نان فکشن ٹورینٹس Anna’s Archive پر %(example)s فکشن کتاب کے لئے۔ یہ <a %(blog_post)s>پہلی ریلیز</a> کافی چھوٹی ہے: تقریباً 300GB کی کتابوں کے کورز Libgen.rs فورک سے، دونوں فکشن اور نان فکشن۔ یہ اسی طرح منظم ہیں جیسے وہ libgen.rs پر نظر آتے ہیں، مثلاً: %(example)s نان فکشن کتاب کے لئے۔ Z-Library کلیکشن کی طرح، ہم نے انہیں ایک بڑے .tar فائل میں رکھا ہے، جسے <a %(a_ratarmount)s>ratarmount</a> کا استعمال کرتے ہوئے ماؤنٹ کیا جا سکتا ہے اگر آپ فائلوں کو براہ راست سرور کرنا چاہتے ہیں۔ ریلیز 1 (%(date)s) Library Genesis (یا "Libgen") کی مختلف فورکس کی مختصر کہانی یہ ہے کہ وقت کے ساتھ، Library Genesis میں شامل مختلف لوگوں کے درمیان اختلافات پیدا ہو گئے، اور وہ الگ الگ راستوں پر چل پڑے۔ اس <a %(a_mhut)s>فورم پوسٹ</a> کے مطابق، Libgen.li اصل میں “http://free-books.dontexist.com” پر میزبانی کی گئی تھی۔ ".fun" ورژن اصل بانی نے بنایا تھا۔ اسے ایک نئے، زیادہ تقسیم شدہ ورژن کے حق میں دوبارہ تیار کیا جا رہا ہے۔ <a %(a_li)s>".li" ورژن</a> میں کامکس کا ایک بڑا مجموعہ ہے، اور دیگر مواد بھی ہے، جو ابھی تک بلک ڈاؤن لوڈ کے لیے ٹورینٹس کے ذریعے دستیاب نہیں ہے۔ اس میں فکشن بکس کا ایک الگ ٹورینٹ کلیکشن ہے، اور اس کے ڈیٹا بیس میں <a %(a_scihub)s>Sci-Hub</a> کا میٹا ڈیٹا بھی شامل ہے۔ ".rs" ورژن میں بہت ملتی جلتی معلومات ہیں، اور یہ باقاعدگی سے اپنی کلیکشن کو بلک ٹورینٹس میں جاری کرتا ہے۔ یہ تقریباً "فکشن" اور "نان فکشن" سیکشن میں تقسیم ہے۔ اصل میں “http://gen.lib.rus.ec” پر۔ <a %(a_zlib)s>Z-Library</a> بھی کسی حد تک Library Genesis کی ایک فورک ہے، حالانکہ انہوں نے اپنے پروجیکٹ کے لیے ایک مختلف نام استعمال کیا۔ Libgen.rs ہم اپنی کلیکشن کو صرف میٹا ڈیٹا کے ماخذوں سے بھی مالا مال کرتے ہیں، جنہیں ہم فائلوں سے ملا سکتے ہیں، مثلاً ISBN نمبرز یا دیگر فیلڈز کا استعمال کرتے ہوئے۔ نیچے ان کا ایک جائزہ دیا گیا ہے۔ پھر سے، ان میں سے کچھ ماخذ مکمل طور پر کھلے ہیں، جبکہ دیگر کے لیے ہمیں انہیں سکریپ کرنا پڑتا ہے۔ نوٹ کریں کہ میٹا ڈیٹا تلاش میں، ہم اصل ریکارڈز دکھاتے ہیں۔ ہم ریکارڈز کا کوئی انضمام نہیں کرتے۔ صرف میٹا ڈیٹا کے ماخذ اوپن لائبریری ایک اوپن سورس پروجیکٹ ہے جو Internet Archive کی طرف سے دنیا کی ہر کتاب کو کیٹلاگ کرنے کے لئے ہے۔ اس کے پاس دنیا کی سب سے بڑی کتاب سکیننگ آپریشنز میں سے ایک ہے، اور اس کے پاس بہت سی کتابیں ڈیجیٹل قرض کے لئے دستیاب ہیں۔ اس کی کتاب میٹا ڈیٹا کیٹلاگ مفت ڈاؤن لوڈ کے لئے دستیاب ہے، اور Anna’s Archive پر شامل ہے (حالانکہ فی الحال تلاش میں نہیں، سوائے اس کے کہ آپ خاص طور پر اوپن لائبریری ID کے لئے تلاش کریں)۔ اوپن لائبریری نقل کو خارج کرتے ہوئے آخری بار اپ ڈیٹ کیا گیا فائلوں کی تعداد کے فیصد %% AA کے ذریعے مررڈ / ٹورینٹس دستیاب ہیں سائز ماخذ نیچے اینا کی آرکائیو پر فائلوں کے ذرائع کا ایک مختصر جائزہ دیا گیا ہے۔ چونکہ شیڈو لائبریریاں اکثر ایک دوسرے سے ڈیٹا ہم آہنگ کرتی ہیں، اس لیے لائبریریوں کے درمیان کافی اوورلیپ ہوتا ہے۔ اسی لیے نمبرز کل میں شامل نہیں ہوتے۔ "Anna’s Archive کے ذریعے آئینہ دار اور سیڈ کیے گئے" فیصد ظاہر کرتا ہے کہ ہم کتنی فائلیں خود آئینہ دار کرتے ہیں۔ ہم ان فائلوں کو بلک میں ٹورینٹس کے ذریعے سیڈ کرتے ہیں، اور انہیں پارٹنر ویب سائٹس کے ذریعے براہ راست ڈاؤن لوڈ کے لیے دستیاب کرتے ہیں۔ جائزہ کل Anna’s Archive پر ٹورینٹس Sci-Hub کے پس منظر کے لئے، براہ کرم اس کے <a %(a_scihub)s>سرکاری ویب سائٹ</a>، <a %(a_wikipedia)s>ویکیپیڈیا صفحہ</a>، اور اس <a %(a_radiolab)s>پوڈکاسٹ انٹرویو</a> کا حوالہ دیں۔ نوٹ کریں کہ Sci-Hub <a %(a_reddit)s>2021 سے منجمد</a> ہے۔ یہ پہلے بھی منجمد تھا، لیکن 2021 میں چند ملین پیپرز شامل کیے گئے۔ پھر بھی، کچھ محدود تعداد میں پیپرز Libgen “scimag” کلیکشنز میں شامل کیے جاتے ہیں، حالانکہ نئے بلک ٹورینٹس کے لئے کافی نہیں۔ ہم Sci-Hub میٹا ڈیٹا کو <a %(a_libgen_li)s>Libgen.li</a> کی “scimag” کلیکشن میں فراہم کردہ کے طور پر استعمال کرتے ہیں۔ ہم <a %(a_dois)s>dois-2022-02-12.7z</a> ڈیٹاسیٹ بھی استعمال کرتے ہیں۔ نوٹ کریں کہ “smarch” ٹورینٹس <a %(a_smarch)s>منسوخ</a> ہیں اور اس لئے ہماری ٹورینٹس کی فہرست میں شامل نہیں ہیں۔ Libgen.li پر ٹورینٹس Libgen.rs پر ٹورینٹس میٹا ڈیٹا اور ٹورینٹس Reddit پر اپ ڈیٹس پوڈ کاسٹ انٹرویو ویکیپیڈیا صفحہ Sci-Hub Sci-Hub: 2021 سے منجمد؛ زیادہ تر ٹورینٹس کے ذریعے دستیاب ہیں Libgen.li: اس کے بعد سے معمولی اضافے</div> کچھ ماخذ لائبریریاں اپنے ڈیٹا کو ٹورینٹس کے ذریعے بڑے پیمانے پر شیئر کرنے کو فروغ دیتی ہیں، جبکہ دیگر اپنی کلیکشن کو آسانی سے شیئر نہیں کرتیں۔ اس صورت میں، Anna’s Archive ان کی کلیکشن کو سکریپ کرنے کی کوشش کرتی ہے، اور انہیں دستیاب بناتی ہے (دیکھیں ہمارا <a %(a_torrents)s>ٹورینٹس</a> صفحہ)۔ درمیان کی صورت حال بھی ہوتی ہے، مثال کے طور پر، جہاں ماخذ لائبریریاں شیئر کرنے کے لیے تیار ہوتی ہیں، لیکن ان کے پاس وسائل نہیں ہوتے۔ ان صورتوں میں، ہم بھی مدد کرنے کی کوشش کرتے ہیں۔ نیچے مختلف ماخذ لائبریریوں کے ساتھ ہمارے انٹرفیس کا ایک جائزہ دیا گیا ہے۔ ماخذ لائبریریاں %(icon)s مختلف فائل ڈیٹا بیسز جو چینی انٹرنیٹ پر بکھرے ہوئے ہیں؛ اگرچہ اکثر ادائیگی والے ڈیٹا بیسز ہیں۔ %(icon)s زیادہ تر فائلیں صرف پریمیم BaiduYun اکاؤنٹس کے ذریعے قابل رسائی ہیں؛ ڈاؤن لوڈنگ کی رفتار سست ہے۔ %(icon)s انا کا آرکائیو <a %(duxiu)s>DuXiu فائلز</a> کی کلیکشن کو منظم کرتا ہے۔ %(icon)s مختلف میٹا ڈیٹا ڈیٹا بیس چینی انٹرنیٹ پر بکھرے ہوئے ہیں؛ حالانکہ اکثر ادائیگی والے ڈیٹا بیس ہیں %(icon)s ان کی پوری کلیکشن کے لیے آسانی سے قابل رسائی میٹا ڈیٹا ڈمپس دستیاب نہیں ہیں۔ %(icon)s انا کا آرکائیو <a %(duxiu)s>DuXiu میٹا ڈیٹا</a> کی کلیکشن کو منظم کرتا ہے۔ فائلیں %(icon)s فائلز صرف محدود بنیادوں پر ادھار لینے کے لیے دستیاب ہیں، مختلف رسائی پابندیوں کے ساتھ %(icon)s Anna’s Archive <a %(ia)s>IA فائلز</a> کا مجموعہ منظم کرتا ہے %(icon)s کچھ میٹا ڈیٹا <a %(openlib)s>Open Library ڈیٹا بیس ڈمپس</a> کے ذریعے دستیاب ہے، لیکن وہ پورے IA مجموعے کا احاطہ نہیں کرتے %(icon)s ان کے پورے مجموعے کے لیے کوئی آسانی سے قابل رسائی میٹا ڈیٹا ڈمپس دستیاب نہیں ہیں %(icon)s Anna’s Archive <a %(ia)s>IA میٹا ڈیٹا</a> کا مجموعہ منظم کرتا ہے آخری بار اپ ڈیٹ کیا گیا %(icon)s آنا کا آرکائیو اور Libgen.li مل کر <a %(comics)s>کامک بکس</a>, <a %(magazines)s>میگزینز</a>, <a %(standarts)s>معیاری دستاویزات</a>, اور <a %(fiction)s>افسانہ (Libgen.rs سے مختلف)</a> کے مجموعے منظم کرتے ہیں۔ %(icon)s نان فکشن ٹورینٹس Libgen.rs کے ساتھ شیئر کیے جاتے ہیں (اور <a %(libgenli)s>یہاں</a> آئینہ دار ہیں)۔ %(icon)s سہ ماہی <a %(dbdumps)s>HTTP ڈیٹا بیس ڈمپس</a> %(icon)s <a %(nonfiction)s>نان فکشن</a> اور <a %(fiction)s>فکشن</a> کے لیے خودکار ٹورینٹس %(icon)s Anna’s Archive <a %(covers)s>کتاب کے سرورق کے ٹورینٹس</a> کا مجموعہ منظم کرتا ہے %(icon)s روزانہ <a %(dbdumps)s>HTTP ڈیٹا بیس ڈمپس</a> میٹا ڈیٹا %(icon)s ماہانہ <a %(dbdumps)s>ڈیٹا بیس ڈمپس</a> %(icon)s ڈیٹا ٹورینٹس <a %(scihub1)s>یہاں</a>, <a %(scihub2)s>یہاں</a>, اور <a %(libgenli)s>یہاں</a> دستیاب ہیں %(icon)s کچھ نئی فائلز <a %(libgenrs)s>Libgen</a> کے "scimag" میں <a %(libgenli)s>شامل</a> کی جا رہی ہیں، لیکن نئے ٹورینٹس کے لیے کافی نہیں ہیں %(icon)s Sci-Hub نے 2021 سے نئے فائلز کو منجمد کر دیا ہے۔ %(icon)s میٹا ڈیٹا ڈمپس <a %(scihub1)s>یہاں</a> اور <a %(scihub2)s>یہاں</a> دستیاب ہیں، اور <a %(libgenli)s>Libgen.li ڈیٹا بیس</a> کا حصہ بھی ہیں (جسے ہم استعمال کرتے ہیں) ماخذ %(icon)s مختلف چھوٹے یا ایک بار کے ذرائع۔ ہم لوگوں کو پہلے دوسرے شیڈو لائبریریوں میں اپلوڈ کرنے کی ترغیب دیتے ہیں، لیکن کبھی کبھی لوگوں کے پاس ایسی کلیکشنز ہوتی ہیں جو دوسروں کے لیے ترتیب دینے کے لیے بہت بڑی ہوتی ہیں، اگرچہ اتنی بڑی نہیں ہوتی کہ اپنی کیٹیگری کی مستحق ہوں۔ %(icon)s براہ راست بلک میں دستیاب نہیں، اسکریپنگ کے خلاف محفوظ۔ %(icon)s انا کا آرکائیو <a %(worldcat)s>OCLC (WorldCat) میٹا ڈیٹا</a> کی کلیکشن کو منظم کرتا ہے۔ %(icon)s Anna’s Archive اور Z-Library مل کر <a %(metadata)s>Z-Library میٹا ڈیٹا</a> اور <a %(files)s>Z-Library فائلز</a> کے مجموعے منظم کرتے ہیں ڈیٹا سیٹس ہم اوپر دیے گئے تمام ماخذوں کو ایک یکجا شدہ ڈیٹا بیس میں ملا دیتے ہیں جسے ہم اس ویب سائٹ کو سروس دینے کے لیے استعمال کرتے ہیں۔ یہ یکجا شدہ ڈیٹا بیس براہ راست دستیاب نہیں ہے، لیکن چونکہ Anna’s Archive مکمل طور پر اوپن سورس ہے، اسے کافی آسانی سے <a %(a_generated)s>جنریٹ</a> یا <a %(a_downloaded)s>ڈاؤن لوڈ</a> کیا جا سکتا ہے جیسے ElasticSearch اور MariaDB ڈیٹا بیسز۔ اس صفحے پر موجود اسکرپٹس خود بخود اوپر دیے گئے ماخذوں سے تمام ضروری میٹا ڈیٹا ڈاؤن لوڈ کر لیں گے۔ اگر آپ ان اسکرپٹس کو مقامی طور پر چلانے سے پہلے ہمارے ڈیٹا کو دریافت کرنا چاہتے ہیں، تو آپ ہمارے JSON فائلوں کو دیکھ سکتے ہیں، جو مزید دیگر JSON فائلوں سے لنک کرتی ہیں۔ <a %(a_json)s>یہ فائل</a> ایک اچھا نقطہ آغاز ہے۔ یکجا شدہ ڈیٹا بیس انا کا آرکائیو کے ذریعے ٹورینٹس براؤز کریں تلاش کریں مختلف چھوٹے یا ایک بار کے ذرائع۔ ہم لوگوں کو پہلے دوسرے شیڈو لائبریریوں میں اپلوڈ کرنے کی ترغیب دیتے ہیں، لیکن کبھی کبھی لوگوں کے پاس ایسی کلیکشنز ہوتی ہیں جو دوسروں کے لیے ترتیب دینے کے لیے بہت بڑی ہوتی ہیں، اگرچہ اتنی بڑی نہیں ہوتی کہ اپنی کیٹیگری کی مستحق ہوں۔ <a %(a1)s>ڈیٹا سیٹس صفحہ</a> سے جائزہ۔ <a %(a_href)s>aaaaarg.fail</a> سے۔ بظاہر کافی مکمل ہے۔ ہمارے رضاکار "cgiym" کی طرف سے۔ <a %(a_href)s><q>ACM Digital Library 2020</q></a> ٹورینٹ سے۔ موجودہ مقالوں کے مجموعوں کے ساتھ کافی زیادہ مشابہت ہے، لیکن بہت کم MD5 میچز ہیں، اس لیے ہم نے اسے مکمل طور پر رکھنے کا فیصلہ کیا۔ رضاکار <q>j</q> کی جانب سے <q>iRead eBooks</q> (آئی ریڈ آئی-بکس؛ airitibooks.com) کا اسکریپ۔ <a %(a1)s><q>دیگر metadata اسکریپس</q></a> میں <q>airitibooks</q> metadata سے مطابقت رکھتا ہے۔ مجموعہ <a %(a1)s><q>Bibliotheca Alexandrina</q></a> سے۔ جزوی طور پر اصل ماخذ سے، جزوی طور پر the-eye.eu سے، جزوی طور پر دیگر آئینوں سے۔ ایک نجی کتابوں کے ٹورینٹ ویب سائٹ <a %(a_href)s>Bibliotik</a> (جسے اکثر "Bib" کہا جاتا ہے) سے، جس کی کتابیں نام کے مطابق ٹورینٹس میں بنڈل کی گئی تھیں (A.torrent, B.torrent) اور the-eye.eu کے ذریعے تقسیم کی گئیں۔ ہمارے رضاکار "bpb9v" کی طرف سے۔ <a %(a_href)s>CADAL</a> کے بارے میں مزید معلومات کے لیے، ہمارے <a %(a_duxiu)s>DuXiu dataset page</a> میں نوٹس دیکھیں۔ ہمارے رضاکار "bpb9v" کی طرف سے مزید، زیادہ تر DuXiu فائلیں، اور ایک فولڈر "WenQu" اور "SuperStar_Journals" (SuperStar DuXiu کے پیچھے کمپنی ہے)۔ ہمارے رضاکار "cgiym" کی طرف سے، مختلف ذرائع سے چینی متون (ذیلی ڈائریکٹریوں کے طور پر نمائندگی کی گئی)، بشمول <a %(a_href)s>China Machine Press</a> (ایک بڑا چینی پبلشر)۔ ہمارے رضاکار "cgiym" کی طرف سے غیر چینی مجموعے (ذیلی ڈائریکٹریوں کے طور پر نمائندگی کی گئی)۔ چینی فن تعمیر کے بارے میں کتابوں کا اسکریپ، رضاکار <q>cm</q> کی جانب سے: <q>میں نے اسے اشاعتی گھر میں نیٹ ورک کی کمزوری کا فائدہ اٹھا کر حاصل کیا، لیکن وہ خلا اب بند ہو چکا ہے</q>۔ <a %(a1)s><q>دیگر metadata اسکریپس</q></a> میں <q>chinese_architecture</q> metadata سے مطابقت رکھتا ہے۔ اکیڈمک پبلشنگ ہاؤس <a %(a_href)s>De Gruyter</a> کی کتابیں، چند بڑے ٹورینٹس سے جمع کی گئیں۔ <a %(a_href)s>docer.pl</a> کا اسکریپ، ایک پولش فائل شیئرنگ ویب سائٹ جو کتابوں اور دیگر تحریری کاموں پر مرکوز ہے۔ 2023 کے آخر میں رضاکار "p" کے ذریعہ اسکریپ کیا گیا۔ ہمارے پاس اصل ویب سائٹ سے اچھا میٹا ڈیٹا نہیں ہے (یہاں تک کہ فائل ایکسٹینشنز بھی نہیں)، لیکن ہم نے کتاب جیسی فائلوں کو فلٹر کیا اور اکثر فائلوں سے میٹا ڈیٹا نکالنے میں کامیاب رہے۔ DuXiu epubs، براہ راست DuXiu سے، رضاکار "w" کے ذریعہ جمع کی گئیں۔ صرف حالیہ DuXiu کتابیں براہ راست ای بکس کے ذریعے دستیاب ہیں، اس لیے ان میں سے زیادہ تر حالیہ ہونی چاہئیں۔ رضاکار "m" کی طرف سے باقی DuXiu فائلیں، جو DuXiu کے ملکیتی PDG فارمیٹ میں نہیں تھیں (مرکزی <a %(a_href)s>DuXiu dataset</a>)۔ بہت سے اصل ذرائع سے جمع کی گئیں، بدقسمتی سے ان ذرائع کو فائل پاتھ میں محفوظ کیے بغیر۔ <span></span> <span></span> <span></span> رضاکار <q>do no harm</q> کی جانب سے شہوانی کتابوں کا اسکریپ۔ <a %(a1)s><q>دیگر metadata اسکریپس</q></a> میں <q>hentai</q> metadata سے مطابقت رکھتا ہے۔ <span></span> <span></span> ایک جاپانی مانگا پبلشر سے اسکریپ، رضاکار "t" کے ذریعہ۔ <a %(a_href)s>Longquan کے منتخب عدالتی آرکائیوز</a>، رضاکار "c" کے ذریعہ فراہم کردہ۔ <a %(a_href)s>magzdb.org</a> کا اسکریپ، جو Library Genesis کا اتحادی ہے (یہ libgen.rs ہوم پیج پر منسلک ہے) لیکن جو اپنی فائلیں براہ راست فراہم نہیں کرنا چاہتا تھا۔ 2023 کے آخر میں رضاکار "p" کے ذریعہ حاصل کیا گیا۔ <span></span> مختلف چھوٹی اپلوڈز، جو اپنی ذیلی مجموعہ کے طور پر بہت چھوٹی ہیں، لیکن ڈائریکٹریوں کے طور پر نمائندگی کی گئی ہیں۔ AvaxHome سے ای بکس، ایک روسی فائل شیئرنگ ویب سائٹ۔ اخبارات اور رسائل کا آرکائیو۔ <a %(a1)s><q>دیگر metadata اسکریپس</q></a> میں <q>newsarch_magz</q> metadata سے مطابقت رکھتا ہے۔ <a %(a1)s>فلسفہ دستاویزات مرکز</a> کا اسکریپ۔ رضاکار "o" کا مجموعہ جس نے پولش کتابیں براہ راست اصل ریلیز ("سین") ویب سائٹس سے جمع کیں۔ <a %(a_href)s>shuge.org</a> کے مشترکہ مجموعے، رضاکاروں "cgiym" اور "woz9ts" کے ذریعہ۔ <span></span> <a %(a_href)s>"Imperial Library of Trantor"</a> (افسانوی لائبریری کے نام پر)، 2022 میں رضاکار "t" کے ذریعہ اسکریپ کیا گیا۔ <span></span> <span></span> <span></span> رضاکار "woz9ts" کی طرف سے ذیلی ذیلی مجموعے (ڈائریکٹریوں کے طور پر نمائندگی کی گئی): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (تائیوان میں <a %(a_sikuquanshu)s>Dizhi(迪志)</a> کے ذریعہ)، mebook (mebook.cc, میری چھوٹی کتابوں کا کمرہ — woz9ts: "یہ سائٹ بنیادی طور پر اعلی معیار کی ای بک فائلوں کا اشتراک کرنے پر مرکوز ہے، جن میں سے کچھ کو مالک نے خود ترتیب دیا ہے۔ مالک کو 2019 میں <a %(a_arrested)s>گرفتار</a> کیا گیا تھا اور کسی نے ان فائلوں کا مجموعہ بنایا جو اس نے شیئر کی تھیں۔")۔ باقی ماندہ DuXiu فائلیں رضاکار "woz9ts" کی طرف سے، جو DuXiu کے ملکیتی PDG فارمیٹ میں نہیں تھیں (ابھی PDF میں تبدیل کی جانی ہیں)۔ "اپلوڈ" کلیکشن کو چھوٹی ذیلی کلیکشنز میں تقسیم کیا گیا ہے، جو AACIDs اور ٹورینٹ ناموں میں ظاہر کی گئی ہیں۔ تمام ذیلی کلیکشنز کو پہلے مرکزی کلیکشن کے خلاف ڈیڈپلیکیٹ کیا گیا، اگرچہ میٹا ڈیٹا "upload_records" JSON فائلز میں اب بھی اصل فائلز کے بہت سے حوالہ جات موجود ہیں۔ زیادہ تر ذیلی کلیکشنز سے غیر کتابی فائلز بھی ہٹا دی گئی ہیں، اور عام طور پر "upload_records" JSON میں نوٹ نہیں کی گئی ہیں۔ ذیلی کلیکشنز یہ ہیں: نوٹس ذیلی مجموعہ بہت سی ذیلی کلیکشنز خود ذیلی ذیلی کلیکشنز پر مشتمل ہیں (مثلاً مختلف اصل ذرائع سے)، جو "filepath" فیلڈز میں ڈائریکٹریز کے طور پر ظاہر کی گئی ہیں۔ انا کے آرکائیو میں اپلوڈز تکنیکی تفصیلات کے لیے، نیچے دیکھیں۔ کسی وقت ہم اس کا استعمال یہ تعین کرنے کے لیے کر سکتے ہیں کہ کون سی کتابیں ابھی بھی شیڈو لائبریریوں سے غائب ہیں، تاکہ یہ ترجیح دی جا سکے کہ کون سی کتابیں تلاش کرنی ہیں اور/یا اسکین کرنی ہیں۔ <a %(a_worldcat)s>ورلڈ کیٹ</a> ایک ملکیتی ڈیٹا بیس ہے جو غیر منافع بخش <a %(a_oclc)s>او سی ایل سی</a> کے ذریعہ چلایا جاتا ہے، جو دنیا بھر کی لائبریریوں سے میٹا ڈیٹا ریکارڈز کو جمع کرتا ہے۔ یہ ممکنہ طور پر دنیا کا سب سے بڑا لائبریری میٹا ڈیٹا مجموعہ ہے۔ اکتوبر 2023 میں ہم نے <a %(a_scrape)s>جاری کیا</a> او سی ایل سی (ورلڈ کیٹ) ڈیٹا بیس کا ایک جامع سکریپ، <a %(a_aac)s>اینا کا آرکائیو کنٹینرز فارمیٹ</a> میں۔ اکتوبر 2023، ابتدائی ریلیز: او سی ایل سی (ورلڈ کیٹ) اینا کا آرکائیو کے ذریعے ٹورینٹس انا کا آرکائیو پر مثال ریکارڈ (اصل کلیکشن) انا کا آرکائیو پر مثال ریکارڈ ("zlib3" کلیکشن) انا کا آرکائیو کے ذریعے ٹورینٹس (میٹا ڈیٹا + مواد) ریلیز 1 کے بارے میں بلاگ پوسٹ ریلیز 2 کے بارے میں بلاگ پوسٹ 2022 کے آخر میں، Z-لائبریری کے مبینہ بانیوں کو گرفتار کیا گیا، اور ڈومینز کو امریکی حکام نے ضبط کر لیا۔ تب سے ویب سائٹ آہستہ آہستہ دوبارہ آن لائن ہو رہی ہے۔ یہ معلوم نہیں ہے کہ فی الحال اسے کون چلا رہا ہے۔ فروری 2023 کی تازہ کاری۔ Z-لائبریری کی جڑیں <a %(a_href)s>Library Genesis</a> کمیونٹی میں ہیں، اور اصل میں ان کے ڈیٹا سے بوٹ اسٹرپ کی گئی تھی۔ تب سے، اس نے کافی حد تک پیشہ ورانہ انداز اختیار کر لیا ہے، اور اس کا انٹرفیس بہت زیادہ جدید ہے۔ اس لیے وہ اپنی ویب سائٹ کو بہتر بنانے کے لیے مالی طور پر اور نئی کتابوں کے عطیات دونوں طرح سے زیادہ عطیات حاصل کرنے کے قابل ہیں۔ انہوں نے Library Genesis کے علاوہ ایک بڑی کلیکشن جمع کر لی ہے۔ کلیکشن تین حصوں پر مشتمل ہے۔ پہلے دو حصوں کے لیے اصل وضاحتی صفحات نیچے محفوظ ہیں۔ تمام ڈیٹا حاصل کرنے کے لیے آپ کو تینوں حصے درکار ہیں (سوائے ان ٹورینٹس کے جو ٹورینٹس کے صفحے پر کراس آؤٹ ہیں)۔ %(title)s: ہماری پہلی ریلیز۔ یہ وہ پہلی ریلیز تھی جسے اس وقت "پائریٹ لائبریری مرر" ("پلیمی") کہا جاتا تھا۔ %(title)s: دوسری ریلیز، اس بار تمام فائلیں .tar فائلوں میں لپیٹی گئی ہیں۔ %(title)s: انکریمنٹل نئی ریلیز، <a %(a_href)s>انا کا آرکائیو کنٹینرز (AAC) فارمیٹ</a> استعمال کرتے ہوئے، اب Z-لائبریری ٹیم کے ساتھ تعاون میں جاری کی گئی۔ ابتدائی مرر 2021 اور 2022 کے دوران محنت سے حاصل کیا گیا تھا۔ اس وقت یہ تھوڑا پرانا ہے: یہ جون 2021 میں کلیکشن کی حالت کی عکاسی کرتا ہے۔ ہم مستقبل میں اسے اپ ڈیٹ کریں گے۔ ابھی ہم اس پہلی ریلیز کو جاری کرنے پر توجہ مرکوز کر رہے ہیں۔ چونکہ Library Genesis پہلے ہی عوامی ٹورینٹس کے ساتھ محفوظ ہے، اور Z-Library میں شامل ہے، ہم نے جون 2022 میں Library Genesis کے خلاف بنیادی ڈیڈوپلیکیشن کی۔ اس کے لیے ہم نے MD5 ہیشز کا استعمال کیا۔ لائبریری میں ممکنہ طور پر بہت زیادہ ڈپلیکیٹ مواد موجود ہے، جیسے کہ ایک ہی کتاب کے مختلف فائل فارمیٹس۔ اسے درست طریقے سے پتہ لگانا مشکل ہے، اس لیے ہم نہیں کرتے۔ ڈیڈوپلیکیشن کے بعد ہمارے پاس 2 ملین سے زیادہ فائلیں بچی ہیں، جن کا مجموعی حجم تقریباً 7TB ہے۔ مجموعہ دو حصوں پر مشتمل ہے: میٹا ڈیٹا کا MySQL “.sql.gz” ڈمپ، اور 72 ٹورینٹ فائلیں جو ہر ایک تقریباً 50-100GB کی ہیں۔ میٹا ڈیٹا میں Z-Library ویب سائٹ کے ذریعہ رپورٹ کردہ ڈیٹا (عنوان، مصنف، تفصیل، فائل ٹائپ) شامل ہے، نیز اصل فائل سائز اور md5sum جو ہم نے مشاہدہ کیا، کیونکہ کبھی کبھی یہ متفق نہیں ہوتے۔ کچھ فائلوں کی رینجز ہیں جن کے لیے Z-Library خود غلط میٹا ڈیٹا رکھتا ہے۔ کچھ الگ تھلگ معاملات میں، ہم نے غلطی سے فائلیں ڈاؤن لوڈ کی ہوں گی، جنہیں ہم مستقبل میں پتہ لگانے اور ٹھیک کرنے کی کوشش کریں گے۔ بڑی ٹورینٹ فائلوں میں اصل کتاب کا ڈیٹا ہوتا ہے، جس میں Z-Library ID فائل نام کے طور پر ہوتا ہے۔ فائل ایکسٹینشنز کو میٹا ڈیٹا ڈمپ کا استعمال کرتے ہوئے دوبارہ تعمیر کیا جا سکتا ہے۔ مجموعہ غیر افسانوی اور افسانوی مواد کا مرکب ہے (Library Genesis کی طرح الگ نہیں کیا گیا)۔ معیار بھی بہت مختلف ہے۔ یہ پہلا ریلیز اب مکمل طور پر دستیاب ہے۔ نوٹ کریں کہ ٹورینٹ فائلیں صرف ہمارے Tor مرر کے ذریعے دستیاب ہیں۔ ریلیز 1 (%(date)s) یہ ایک اضافی ٹورینٹ فائل ہے۔ اس میں کوئی نئی معلومات نہیں ہیں، لیکن اس میں کچھ ڈیٹا ہے جسے کمپیوٹ کرنے میں وقت لگ سکتا ہے۔ اس لیے اسے رکھنا آسان ہے، کیونکہ اس ٹورینٹ کو ڈاؤن لوڈ کرنا اکثر اسے شروع سے کمپیوٹ کرنے سے تیز ہوتا ہے۔ خاص طور پر، اس میں tar فائلوں کے لیے SQLite انڈیکس شامل ہیں، جو <a %(a_href)s>ratarmount</a> کے ساتھ استعمال کے لیے ہیں۔ ریلیز 2 ضمیمہ (%(date)s) ہم نے وہ تمام کتابیں حاصل کر لی ہیں جو ہمارے آخری مرر اور اگست 2022 کے درمیان Z-Library میں شامل کی گئی تھیں۔ ہم نے کچھ کتابیں بھی دوبارہ حاصل کی ہیں جو ہم پہلی بار چھوڑ چکے تھے۔ مجموعی طور پر، یہ نیا مجموعہ تقریباً 24TB ہے۔ ایک بار پھر، یہ مجموعہ Library Genesis کے خلاف ڈیڈوپلیکیشن کیا گیا ہے، کیونکہ اس مجموعہ کے لیے پہلے ہی ٹورینٹس دستیاب ہیں۔ ڈیٹا پہلی ریلیز کی طرح منظم ہے۔ میٹا ڈیٹا کا MySQL “.sql.gz” ڈمپ ہے، جس میں پہلی ریلیز کا تمام میٹا ڈیٹا بھی شامل ہے، اس طرح اسے سپرسیڈنگ کرتا ہے۔ ہم نے کچھ نئے کالم بھی شامل کیے ہیں: ہم نے یہ آخری بار ذکر کیا تھا، لیکن صرف وضاحت کے لیے: “filename” اور “md5” فائل کی اصل خصوصیات ہیں، جبکہ “filename_reported” اور “md5_reported” وہ ہیں جو ہم نے Z-Library سے حاصل کیں۔ کبھی کبھی یہ دونوں ایک دوسرے سے متفق نہیں ہوتے، اس لیے ہم نے دونوں کو شامل کیا۔ اس ریلیز کے لیے، ہم نے collation کو “utf8mb4_unicode_ci” میں تبدیل کیا، جو MySQL کے پرانے ورژنز کے ساتھ مطابقت پذیر ہونا چاہیے۔ ڈیٹا فائلیں پچھلی بار کی طرح ہیں، حالانکہ وہ بہت بڑی ہیں۔ ہم نے بہت زیادہ چھوٹی ٹورینٹ فائلیں بنانے کی زحمت نہیں کی۔ “pilimi-zlib2-0-14679999-extra.torrent” میں وہ تمام فائلیں شامل ہیں جو ہم نے پچھلی ریلیز میں چھوڑ دی تھیں، جبکہ باقی ٹورینٹس سب نئے ID رینجز ہیں۔  <strong>اپ ڈیٹ %(date)s:</strong> ہم نے اپنے زیادہ تر ٹورینٹس بہت بڑے بنائے، جس کی وجہ سے ٹورینٹ کلائنٹس کو مشکلات کا سامنا کرنا پڑا۔ ہم نے انہیں ہٹا دیا اور نئے ٹورینٹس جاری کیے۔ <strong>اپ ڈیٹ %(date)s:</strong> فائلیں اب بھی بہت زیادہ تھیں، اس لیے ہم نے انہیں tar فائلوں میں لپیٹ دیا اور دوبارہ نئے ٹورینٹس جاری کیے۔ %(key)s: آیا یہ فائل پہلے ہی Library Genesis میں موجود ہے، چاہے غیر افسانوی یا افسانوی مجموعہ میں (md5 کے ذریعے ملاپ کیا گیا)۔ %(key)s: یہ فائل کس ٹورینٹ میں ہے۔ %(key)s: جب ہم کتاب ڈاؤن لوڈ کرنے میں ناکام رہے۔ ریلیز 2 (%(date)s) Zlib ریلیز (اصل وضاحتی صفحات) ٹور ڈومین مرکزی ویب سائٹ Z-لائبریری سکریپ Z-Library میں "چینی" مجموعہ بظاہر ہمارے DuXiu مجموعہ جیسا ہی ہے، لیکن مختلف MD5s کے ساتھ۔ ہم ان فائلوں کو نقل سے بچنے کے لیے ٹورینٹس سے خارج کرتے ہیں، لیکن پھر بھی انہیں ہماری تلاش کی فہرست میں دکھاتے ہیں۔ میٹا ڈیٹا آپ کو %(percentage)s%% بونس تیز ڈاؤن لوڈز ملتے ہیں، کیونکہ آپ کو صارف %(profile_link)s نے مدعو کیا ہے۔ یہ پورے رکنیت کے دورانیے پر لاگو ہوتا ہے۔ عطیہ کریں شامل ہوں منتخب شدہ %(percentage)s%% تک کی رعایتیں Alipay بین الاقوامی کریڈٹ/ڈیبٹ کارڈز کی حمایت کرتا ہے۔ مزید معلومات کے لیے <a %(a_alipay)s>یہ گائیڈ</a> دیکھیں۔ ہمیں Amazon.com گفٹ کارڈز اپنے کریڈٹ/ڈیبٹ کارڈ کا استعمال کرتے ہوئے بھیجیں۔ آپ کریڈٹ/ڈیبٹ کارڈز کا استعمال کرتے ہوئے کرپٹو خرید سکتے ہیں۔ WeChat (Weixin Pay) بین الاقوامی کریڈٹ/ڈیبٹ کارڈز کی حمایت کرتا ہے۔ WeChat ایپ میں، "Me => Services => Wallet => Add a Card" پر جائیں۔ اگر آپ کو یہ نظر نہیں آتا، تو "Me => Settings => General => Tools => Weixin Pay => Enable" کا استعمال کرتے ہوئے اسے فعال کریں۔ (جب Coinbase سے ایتھیریم بھیج رہے ہوں تو استعمال کریں) کاپی ہو گیا! نقل کریں (کم از کم رقم) (انتباہ: کم از کم رقم زیادہ ہے) -%(percentage)s%% 12 ماہ 1 مہینہ 24 ماہ 3 ماہ 48 ماہ 6 ماہ 96 ماہ منتخب کریں کہ آپ کتنے عرصے کے لیے سبسکرائب کرنا چاہتے ہیں۔ <div %(div_monthly_cost)s></div><div %(div_after)s>بعد میں <span %(span_discount)s></span> رعایتیں</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 ماہ کے لیے 1 ماہ کے لیے 24 ماہ کے لیے 3 ماہ کے لیے 48 ماہ کے لیے 6 ماہ کے لیے 96 ماہ کے لیے %(monthly_cost)s / ماہ ہم سے رابطہ کریں براہ راست <strong>SFTP</strong> سرورز نئی کلیکشنز کے لیے انٹرپرائز سطح کا عطیہ یا تبادلہ (مثلاً نئے اسکینز، OCR’ed ڈیٹا سیٹس)۔ ماہر رسائی <strong>لامحدود</strong> تیز رفتار رسائی <div %(div_question)s>کیا میں اپنی ممبرشپ اپ گریڈ کر سکتا ہوں یا متعدد ممبرشپ حاصل کر سکتا ہوں؟</div> <div %(div_question)s>کیا میں رکن بنے بغیر عطیہ کر سکتا ہوں؟</div> بالکل۔ ہم اس Monero (XMR) ایڈریس پر کسی بھی رقم کا عطیہ قبول کرتے ہیں: %(address)s۔ <div %(div_question)s>ماہانہ حدود کا کیا مطلب ہے؟</div> آپ تمام رعایتوں کا اطلاق کر کے ایک حد کے نچلے حصے تک پہنچ سکتے ہیں، جیسے کہ ایک ماہ سے زیادہ مدت کا انتخاب کرنا۔ <div %(div_question)s>کیا ممبرشپ خود بخود تجدید ہوتی ہے؟</div> ممبرشپ <strong>خود بخود</strong> تجدید نہیں ہوتی۔ آپ جتنا چاہیں شامل ہو سکتے ہیں۔ <div %(div_question)s>آپ عطیات پر کیا خرچ کرتے ہیں؟</div> 100%% دنیا کے علم اور ثقافت کو محفوظ اور قابل رسائی بنانے پر جا رہا ہے۔ فی الحال ہم زیادہ تر سرورز، اسٹوریج، اور بینڈوڈتھ پر خرچ کرتے ہیں۔ کوئی پیسہ کسی ٹیم ممبر کو ذاتی طور پر نہیں جا رہا۔ ‫<div %(div_question)s>‏کیا میں ایک بڑا عطیہ کر سکتا ہوں؟‫</div>‏ یہ ہمارے لئے بڑی خوشی کی بات ہو گی! اگر آپ کچھ ہزار ڈالرز سے زیادہ عطیہ کرنا چاہتے ہیں، تو ہم سے ‫%(email)s‏ پر براہ راست رابطہ کریں۔ ‫<div %(div_question)s>‏کیا عطیہ کیلئے رقم منتقلی کا کوئی اور طریقہ کار دستیاب ہے؟‫</div>‏ فی الحال نہیں۔ بہت سے لوگ نہیں چاہتے کے اِس جیسے آرکائیو موجود ہوں، اس لئے ہمیں محتاط رہنا پڑتا ہے۔ اگر آپ رقم منتقلی کیلئے اس کے علاوہ کوئی محفوظ (اور آسان) طریقہ کار مہیا کرنے میں ہماری مدد کر سکتے ہیں، تو ہم سے ‫%(email)s‏ پر رابطہ کریں۔ عطیہ FAQ آپ کے پاس ایک <a %(a_donation)s>موجودہ عطیہ</a> جاری ہے۔ براہ کرم نیا عطیہ کرنے سے پہلے اس عطیہ کو مکمل کریں یا منسوخ کریں۔ <a %(a_all_donations)s>میری تمام عطیات دیکھیں</a> $5000 سے زیادہ کے عطیات کے لیے براہ کرم ہم سے براہ راست %(email)s پر رابطہ کریں۔ ہم امیر افراد یا اداروں سے بڑے عطیات کا خیرمقدم کرتے ہیں۔  براہ کرم نوٹ کریں کہ اس صفحے پر ممبرشپ "ماہانہ" ہیں، یہ ایک بار کی عطیات ہیں (بار بار نہیں)۔ مزید معلومات کے لیے <a %(faq)s>عطیہ FAQ</a> دیکھیں۔ اینا کا آرکائیو ایک غیر منافع بخش، اوپن سورس، اوپن ڈیٹا پروجیکٹ ہے۔ عطیہ دے کر اور رکن بن کر، آپ ہماری کارروائیوں اور ترقی کی حمایت کرتے ہیں۔ ہمارے تمام اراکین کا شکریہ: آپ ہمیں جاری رکھنے میں مدد دیتے ہیں! ❤️ مزید معلومات کے لیے، <a %(a_donate)s>عطیہ FAQ</a> دیکھیں۔ رکن بننے کے لیے، براہ کرم <a %(a_login)s>لاگ ان کریں یا رجسٹر کریں</a>۔ آپ کی حمایت کا شکریہ! $%(cost)s / ماہ اگر آپ نے ادائیگی کے دوران کوئی غلطی کی ہے، تو ہم رقم کی واپسی نہیں کر سکتے، لیکن ہم اسے درست کرنے کی کوشش کریں گے۔ اپنے پے پال ایپ یا ویب سائٹ میں "کرپٹو" صفحہ تلاش کریں۔ یہ عام طور پر "مالیات" کے تحت ہوتا ہے۔ اپنے PayPal ایپ یا ویب سائٹ میں "Bitcoin" صفحے پر جائیں۔ "Transfer" بٹن %(transfer_icon)s دبائیں، اور پھر "Send"۔ Alipay Alipay 支付宝 / WeChat 微信 Amazon گفٹ کارڈ %(amazon)s گفٹ کارڈ بینک کارڈ بینک کارڈ (ایپ استعمال کرتے ہوئے) بائنانس کریڈٹ/ڈیبٹ/ایپل/گوگل (BMC) Cash App کریڈٹ/ڈیبٹ کارڈ کریڈٹ/ڈیبٹ کارڈ 2 کریڈٹ/ڈیبٹ کارڈ (بیک اپ) کرپٹو %(bitcoin_icon)s کارڈ / PayPal / Venmo پے پال (امریکہ) %(bitcoin_icon)s PayPal PayPal (باقاعدہ) Pix (Brazil) Revolut (عارضی طور پر دستیاب نہیں) وی چیٹ اپنا پسندیدہ کرپٹو کوائن منتخب کریں: Amazon گفٹ کارڈ کا استعمال کرتے ہوئے عطیہ کریں۔ <strong>اہم: </strong> یہ آپشن %(amazon)s کے لیے ہے۔ اگر آپ کسی دوسرے ایمیزون ویب سائٹ کا استعمال کرنا چاہتے ہیں، تو اسے اوپر منتخب کریں۔ <strong>اہم:</strong> ہم صرف Amazon.com کی حمایت کرتے ہیں، دیگر Amazon ویب سائٹس کی نہیں۔ مثال کے طور پر، .de، .co.uk، .ca، کی حمایت نہیں کی جاتی۔ براہ کرم اپنا پیغام نہ لکھیں۔ عین رقم درج کریں: %(amount)s نوٹ کریں کہ ہمیں اپنے ری سیلرز کے ذریعہ قبول شدہ مقداروں کو گول کرنے کی ضرورت ہے (کم از کم %(minimum)s)۔ کریڈٹ/ڈیبٹ کارڈ کے ذریعے عطیہ کریں، Alipay ایپ کے ذریعے (بہت آسانی سے سیٹ اپ کیا جا سکتا ہے)۔ <a %(a_app_store)s>ایپل ایپ اسٹور</a> یا <a %(a_play_store)s>گوگل پلے اسٹور</a> سے Alipay ایپ انسٹال کریں۔ اپنے فون نمبر کا استعمال کرتے ہوئے رجسٹر کریں۔ مزید ذاتی تفصیلات کی ضرورت نہیں ہے۔ <span %(style)s>1</span>Alipay ایپ انسٹال کریں مدد شدہ: ویزا، ماسٹر کارڈ، JCB، ڈائنرز کلب اور ڈسکور۔ مزید معلومات کے لیے <a %(a_alipay)s>یہ گائیڈ</a> دیکھیں۔ <span %(style)s>2</span>بینک کارڈ شامل کریں Binance کے ساتھ، آپ کریڈٹ/ڈیبٹ کارڈ یا بینک اکاؤنٹ کے ساتھ بٹ کوائن خریدتے ہیں، اور پھر وہ بٹ کوائن ہمیں عطیہ کرتے ہیں۔ اس طرح ہم آپ کے عطیہ کو قبول کرتے وقت محفوظ اور گمنام رہ سکتے ہیں۔ Binance تقریباً ہر ملک میں دستیاب ہے، اور زیادہ تر بینکوں اور کریڈٹ/ڈیبٹ کارڈز کی حمایت کرتا ہے۔ یہ فی الحال ہماری اہم سفارش ہے۔ ہم آپ کے اس طریقے سے عطیہ کرنے کا طریقہ سیکھنے کے لیے وقت نکالنے کی تعریف کرتے ہیں، کیونکہ اس سے ہمیں بہت مدد ملتی ہے۔ کریڈٹ کارڈز، ڈیبٹ کارڈز، ایپل پے، اور گوگل پے کے لیے، ہم "بائے می آ کافی" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) استعمال کرتے ہیں۔ ان کے نظام میں، ایک "کافی" $5 کے برابر ہے، لہذا آپ کا عطیہ 5 کے قریب ترین عدد میں گول کر دیا جائے گا۔ Cash App کا استعمال کرتے ہوئے عطیہ کریں۔ اگر آپ کے پاس Cash App ہے، تو یہ عطیہ کرنے کا سب سے آسان طریقہ ہے! نوٹ کریں کہ %(amount)s سے کم کی ٹرانزیکشنز کے لیے، Cash App %(fee)s فیس وصول کر سکتا ہے۔ %(amount)s یا اس سے زیادہ کے لیے، یہ مفت ہے! کریڈٹ یا ڈیبٹ کارڈ کے ذریعے عطیہ کریں۔ یہ طریقہ ایک کرپٹو کرنسی فراہم کنندہ کو ایک درمیانی تبدیلی کے طور پر استعمال کرتا ہے۔ یہ تھوڑا سا الجھا ہوا ہو سکتا ہے، لہذا براہ کرم صرف اس طریقے کا استعمال کریں اگر دوسرے ادائیگی کے طریقے کام نہیں کرتے ہیں۔ یہ تمام ممالک میں بھی کام نہیں کرتا۔ ہم براہ راست کریڈٹ/ڈیبٹ کارڈز کی حمایت نہیں کر سکتے، کیونکہ بینک ہمارے ساتھ کام نہیں کرنا چاہتے۔ ☹ تاہم، کریڈٹ/ڈیبٹ کارڈز استعمال کرنے کے کئی طریقے ہیں، دوسرے ادائیگی کے طریقوں کا استعمال کرتے ہوئے: آپ BTC، ETH، XMR، اور SOL کا استعمال کرتے ہوئے کرپٹو کے ذریعے عطیہ کر سکتے ہیں۔ اس آپشن کو استعمال کریں اگر آپ پہلے سے کرپٹو کرنسی سے واقف ہیں۔ کریپٹو کے ساتھ آپ BTC، ETH، XMR، اور مزید کا استعمال کرتے ہوئے عطیہ کر سکتے ہیں۔ کرپٹو ایکسپریس سروسز اگر آپ پہلی بار کرپٹو استعمال کر رہے ہیں، تو ہم تجویز کرتے ہیں کہ %(options)s کا استعمال کریں تاکہ بٹ کوائن (اصل اور سب سے زیادہ استعمال ہونے والی کرپٹو کرنسی) خریدیں اور عطیہ کریں۔ نوٹ کریں کہ چھوٹے عطیات کے لئے کریڈٹ کارڈ کی فیس ہمارے %(discount)s%% رعایت کو ختم کر سکتی ہے، اس لئے ہم طویل سبسکرپشنز کی سفارش کرتے ہیں۔ کریڈٹ/ڈیبٹ کارڈ، PayPal، یا Venmo کا استعمال کرتے ہوئے عطیہ کریں۔ آپ اگلے صفحے پر ان میں سے انتخاب کر سکتے ہیں۔ گوگل پے اور ایپل پے بھی کام کر سکتے ہیں۔ نوٹ کریں کہ چھوٹے عطیات کے لیے فیس زیادہ ہوتی ہے، اس لیے ہم طویل سبسکرپشنز کی سفارش کرتے ہیں۔ PayPal US کا استعمال کرتے ہوئے عطیہ کرنے کے لیے، ہم PayPal Crypto استعمال کریں گے، جو ہمیں گمنام رہنے کی اجازت دیتا ہے۔ ہم آپ کی اس طریقے سے عطیہ کرنے کے طریقے کو سیکھنے کے لیے وقت نکالنے کی تعریف کرتے ہیں، کیونکہ یہ ہمیں بہت مدد دیتا ہے۔ PayPal کا استعمال کرتے ہوئے عطیہ کریں۔ اپنے باقاعدہ PayPal اکاؤنٹ کا استعمال کرتے ہوئے عطیہ کریں۔ Revolut کا استعمال کرتے ہوئے عطیہ کریں۔ اگر آپ کے پاس Revolut ہے، تو یہ عطیہ کرنے کا سب سے آسان طریقہ ہے! یہ ادائیگی کا طریقہ زیادہ سے زیادہ %(amount)s کی اجازت دیتا ہے۔ براہ کرم مختلف مدت یا ادائیگی کا طریقہ منتخب کریں۔ اس ادائیگی کے طریقے کے لیے کم از کم %(amount)s درکار ہیں۔ براہ کرم مختلف مدت یا ادائیگی کا طریقہ منتخب کریں۔ بائنانس Coinbase Kraken براہ کرم ادائیگی کا طریقہ منتخب کریں۔ “ایک ٹورینٹ اپنائیں”: آپ کا صارف نام یا پیغام ایک ٹورینٹ فائل کے نام میں <div %(div_months)s>ہر 12 ماہ کی رکنیت کے بعد</div> آپ کا صارف نام یا کریڈٹس میں گمنام ذکر نئی خصوصیات تک ابتدائی رسائی پردے کے پیچھے کی تازہ کاریوں کے ساتھ خصوصی ٹیلیگرام %(number)s تیز ڈاؤن لوڈز فی دن اگر آپ اس مہینے عطیہ کریں! <a %(a_api)s>JSON API</a> تک رسائی انسانیت کے علم اور ثقافت کے تحفظ میں افسانوی حیثیت پچھلے فوائد، علاوہ: <strong>%(percentage)s%% بونس ڈاؤن لوڈز</strong> حاصل کریں <a %(a_refer)s>دوستوں کو مدعو کر کے</a>۔ SciDB پیپرز <strong>لامحدود</strong> بغیر تصدیق کے جب اکاؤنٹ یا عطیہ کے سوالات پوچھیں، تو اپنے اکاؤنٹ کی شناخت، اسکرین شاٹس، رسیدیں، جتنی زیادہ معلومات ہو سکے شامل کریں۔ ہم ہر 1-2 ہفتے میں صرف اپنی ای میل چیک کرتے ہیں، اس لیے اس معلومات کو شامل نہ کرنے سے کسی بھی حل میں تاخیر ہوگی۔ زیادہ ڈاؤن لوڈز حاصل کرنے کے لیے، <a %(a_refer)s>اپنے دوستوں کو مدعو کریں</a>! ہم رضاکاروں کی ایک چھوٹی سی ٹیم ہیں۔ ہمیں جواب دینے میں 1-2 ہفتے لگ سکتے ہیں۔ ممکن ہے کہ اکاؤنٹ کا نام یا تصویر آپ کو عجیب لگے۔ مگر پریشان مت ہوں، ان اکاؤنٹس کا انتظام ہمارے عطیات کے پارٹنرز کرتے ہیں۔ ہمارے اکاؤنٹس ہیک نہیں ہوئے۔ عطیہ کریں <span %(span_cost)s></span> <span %(span_label)s></span> 12 ماہ کے لیے “%(tier_name)s” 1 ماہ کے لیے “%(tier_name)s” 24 ماہ کے لیے “%(tier_name)s” 3 ماہ کے لئے “%(tier_name)s” 48 ماہ کے لیے “%(tier_name)s” 6 ماہ کے لیے “%(tier_name)s” 96 ماہ کے لیے “%(tier_name)s” آپ چیک آؤٹ کے دوران بھی عطیہ منسوخ کر سکتے ہیں۔ اس عطیہ کی تصدیق کے لیے عطیہ بٹن پر کلک کریں۔ <strong>اہم نوٹ:</strong> کرپٹو کی قیمتیں بہت زیادہ اتار چڑھاؤ کر سکتی ہیں، کبھی کبھی چند منٹوں میں 20%% تک۔ یہ اب بھی بہت سے ادائیگی فراہم کنندگان کے ساتھ ہونے والی فیسوں سے کم ہے، جو اکثر 50-60%% وصول کرتے ہیں کیونکہ وہ ہمارے جیسے "شیڈو چیریٹی" کے ساتھ کام کرتے ہیں۔ <u>اگر آپ ہمیں اصل قیمت کے ساتھ رسید بھیجتے ہیں جو آپ نے ادا کی ہے، تو ہم پھر بھی آپ کے اکاؤنٹ کو منتخب کردہ رکنیت کے لیے کریڈٹ کریں گے</u> (جب تک کہ رسید چند گھنٹوں سے زیادہ پرانی نہ ہو)۔ ہم واقعی اس بات کی تعریف کرتے ہیں کہ آپ ہماری حمایت کے لیے اس طرح کی چیزوں کو برداشت کرنے کے لیے تیار ہیں! ❤️ ❌ کچھ غلط ہو گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ <span %(span_circle)s>1</span>پے پال پر بٹ کوائن خریدیں <span %(span_circle)s>2</span> بٹ کوائن کو ہمارے ایڈریس پر ٹرانسفر کریں ✅ عطیہ کے صفحے پر ری ڈائریکٹ کیا جا رہا ہے… عطیہ کریں ہم سے رابطہ کرنے سے پہلے کم از کم <span %(span_hours)s>24 گھنٹے</span> انتظار کریں (اور اس صفحہ کو ریفریش کریں)۔ اگر آپ بغیر رکنیت کے (کسی بھی رقم کی) عطیہ کرنا چاہتے ہیں، تو براہ کرم اس Monero (XMR) ایڈریس کا استعمال کریں: %(address)s۔ گفٹ کارڈ بھیجنے کے بعد، ہمارا خودکار نظام چند منٹوں میں اس کی تصدیق کر دے گا۔ اگر یہ کام نہیں کرتا، تو اپنے گفٹ کارڈ کو دوبارہ بھیجنے کی کوشش کریں (<a %(a_instr)s>ہدایات</a>)۔ اگر یہ پھر بھی کام نہیں کرتا تو براہ کرم ہمیں ای میل کریں اور اینا اسے دستی طور پر جائزہ لے گی (اس میں چند دن لگ سکتے ہیں)، اور یہ ضرور بتائیں کہ آپ نے دوبارہ بھیجنے کی کوشش کی ہے یا نہیں۔ مثال: براہ کرم ہمیں <a %(a_form)s>سرکاری Amazon.com فارم</a> کا استعمال کرتے ہوئے نیچے دیے گئے ای میل ایڈریس پر %(amount)s کا گفٹ کارڈ بھیجیں۔ "To" recipient email in the form: ایمیزون گفٹ کارڈ ہم دیگر طریقوں کے گفٹ کارڈ قبول نہیں کر سکتے، <strong>صرف سرکاری فارم سے براہ راست بھیجے گئے Amazon.com پر</strong>۔ اگر آپ اس فارم کا استعمال نہیں کرتے تو ہم آپ کا گفٹ کارڈ واپس نہیں کر سکتے۔ صرف ایک بار استعمال کریں۔ آپ کے اکاؤنٹ کے لیے منفرد، شیئر نہ کریں۔ گفٹ کارڈ کا انتظار ہے… (چیک کرنے کے لیے صفحہ ریفریش کریں) <a %(a_href)s>QR کوڈ عطیہ صفحہ</a> کھولیں۔ Alipay ایپ کے ساتھ QR کوڈ اسکین کریں، یا Alipay ایپ کھولنے کے لیے بٹن دبائیں۔ براہ کرم صبر کریں؛ صفحہ لوڈ ہونے میں کچھ وقت لگ سکتا ہے کیونکہ یہ چین میں ہے۔ <span %(style)s>3</span>عطیہ کریں (QR کوڈ اسکین کریں یا بٹن دبائیں) PayPal پر PYUSD کوائن خریدیں کیش ایپ پر بٹ کوائن (BTC) خریدیں تھوڑی زیادہ خریدیں (ہم تجویز کرتے ہیں کہ آپ %(more)s زیادہ خریدیں) جتنا آپ عطیہ کر رہے ہیں (%(amount)s)، تاکہ ٹرانزیکشن فیس کو پورا کیا جا سکے۔ جو کچھ بچ جائے گا وہ آپ کے پاس رہے گا۔ کیش ایپ میں "بٹ کوائن" (BTC) صفحے پر جائیں۔ بٹ کوائن ہمارے پتے پر منتقل کریں چھوٹے عطیات (25 ڈالر سے کم) کے لیے، آپ کو رش یا ترجیح استعمال کرنے کی ضرورت ہو سکتی ہے۔ "بٹ کوائن بھیجیں" بٹن پر کلک کریں تاکہ "نکالنا" ہو سکے۔ ڈالر سے BTC میں تبدیل کرنے کے لیے %(icon)s آئیکن دبائیں۔ نیچے BTC کی مقدار درج کریں اور "بھیجیں" پر کلک کریں۔ اگر آپ کو مشکل ہو تو <a %(help_video)s>اس ویڈیو</a> کو دیکھیں۔ ایکسپریس سروسز آسان ہیں، لیکن زیادہ فیس چارج کرتی ہیں۔ اگر آپ جلدی سے بڑا عطیہ کرنا چاہتے ہیں اور $5-10 کی فیس کی پرواہ نہیں کرتے تو آپ اسے کرپٹو ایکسچینج کے بجائے استعمال کر سکتے ہیں۔ یقینی بنائیں کہ عطیہ صفحے پر دکھائی گئی عین کرپٹو رقم بھیجیں، نہ کہ $USD میں رقم۔ ورنہ فیس منہا کی جائے گی اور ہم آپ کی ممبرشپ کو خودکار طور پر پروسیس نہیں کر سکیں گے۔ کبھی کبھار تصدیق میں 24 گھنٹے تک لگ سکتے ہیں، لہذا اس صفحے کو تازہ کریں (چاہے یہ ختم ہو چکا ہو)۔ کریڈٹ / ڈیبٹ کارڈ کی ہدایات ہمارے کریڈٹ / ڈیبٹ کارڈ صفحے کے ذریعے عطیہ کریں کچھ مراحل میں کرپٹو والٹس کا ذکر ہے، لیکن فکر نہ کریں، آپ کو کرپٹو کے بارے میں کچھ سیکھنے کی ضرورت نہیں ہے۔ %(coin_name)s ہدایات ﮟﯾﺮﮐ ﻦﯿﮑﺳﺍ ﻮﮐ ﮈﻮﮐ ﺭﺁ ﻮﯿﮐ ﺱﺍ ﮫﺗﺎﺳ ﮯﮐ ﭗﯾﺍ ﭧﯿﻟﺍﻭ ﻮﭩﭙﯾﺮﮐ ﮯﻨﭘﺍ ﮯﺌﻟ ﮯﮐ ﮯﻧﺮﮐ ﺮﭘُ ﮯﺳ ﯼﺪ ﮟﯾﺮﮐ ﻦﯿﮑﺳﺍ ﮈﻮﮐ QR ﮯﺌﻟ ﮯﮐ ﮯﻧﺮﮐ ﺍﺩﺍ ہم صرف کرپٹو کوائنز کے معیاری ورژن کی حمایت کرتے ہیں، کوئی غیر معمولی نیٹ ورکس یا کوائنز کے ورژن نہیں۔ ٹرانزیکشن کی تصدیق میں ایک گھنٹہ تک لگ سکتا ہے، کوائن پر منحصر ہے۔ <a %(a_page)s>اس صفحے</a> پر %(amount)s عطیہ کریں۔ یہ عطیہ ختم ہو چکا ہے۔ براہ کرم منسوخ کریں اور نیا بنائیں۔ اگر آپ نے پہلے ہی ادائیگی کر دی ہے: جی ہاں، میں نے اپنی رسید ایمیل کی ہے اگر کرپٹو ایکسچینج ریٹ لین دین کے دوران میں تبدیل ہو گیا تھا، تو براہ کرم وصولی کی رسید شامل کریں جس میں اصل ایکسچینج ریٹ دکھایا گیا ہو۔ ہم واقعی آپ کی کرپٹو کے استعمال کی قدر کرتے ہیں، یہ ہماری بہت مدد کرتا ہے! ❌ کچھ غلط ہو گیا۔ براہ کرم صفحہ کو دوبارہ لوڈ کریں اور پھر کوشش کریں۔ <span %(span_circle)s>%(circle_number)s</span> ہمیں رسید ای میل کریں اگر آپ کو کسی مسئلے کا سامنا ہو تو براہ کرم ہم سے %(email)s پر رابطہ کریں اور جتنا ممکن ہو سکے معلومات فراہم کریں (جیسے کہ اسکرین شاٹس)۔ ✅ آپ کے عطیہ کا شکریہ! آنا آپ کی رکنیت کو چند دنوں کے اندر دستی طور پر فعال کر دے گی۔ اپنے ذاتی تصدیقی پتے پر رسید یا اسکرین شاٹ بھیجیں: جب آپ نے اپنی رسید ای میل کر دی ہو، تو اس بٹن پر کلک کریں، تاکہ انا اسے دستی طور پر جائزہ لے سکے (اس میں کچھ دن لگ سکتے ہیں): اپنے ذاتی تصدیقی پتے پر رسید یا اسکرین شاٹ بھیجیں۔ براہ کرم اس ای میل پتے کو اپنے PayPal عطیہ کے لیے استعمال نہ کریں۔ منسوخ کریں جی ہاں، براہ کرم منسوخ کریں کیا آپ واقعی منسوخ کرنا چاہتے ہیں؟ اگر آپ نے پہلے ہی ادائیگی کر دی ہے تو منسوخ نہ کریں۔ ❌ کچھ غلط ہو گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ نیا عطیہ کریں ✅ آپ کا عطیہ منسوخ کر دیا گیا ہے۔ تاریخ: %(date)s شناخت کنندہ: %(id)s دوبارہ ترتیب دیں حیثیت: <span %(span_label)s>%(label)s</span> کل: %(total)s <span %(span_details)s>(%(duration)s مہینوں کے لیے %(monthly_amount_usd)s / ماہ، بشمول %(discounts)s%% رعایت)</span> کل: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / مہینہ کے لئے %(duration)s مہینے)</span> 1. اپنا ایمیل درج کریں۔ 2. اپنا ادائیگی کا طریقہ منتخب کریں۔ 3. اپنا ادائیگی کا طریقہ دوبارہ منتخب کریں۔ 4. "خود میزبان" والیٹ منتخب کریں۔ 5. "میں ملکیت کی تصدیق کرتا ہوں" پر کلک کریں۔ 6. آپ کو ایک ایمیل رسید موصول ہونی چاہیے۔ براہ کرم وہ ہمیں بھیجیں، اور ہم آپ کے عطیہ کی تصدیق جلد از جلد کریں گے۔ (آپ منسوخ کر کے نیا عطیہ بنا سکتے ہیں) ادائیگی کی ہدایات اب پرانی ہو چکی ہیں۔ اگر آپ ایک اور عطیہ کرنا چاہتے ہیں، تو اوپر "دوبارہ آرڈر کریں" بٹن کا استعمال کریں۔ آپ پہلے ہی ادائیگی کر چکے ہیں۔ اگر آپ پھر بھی ادائیگی کی ہدایات کا جائزہ لینا چاہتے ہیں، تو یہاں کلک کریں: پرانے ادائیگی کے ہدایات دکھائیں اگر عطیہ صفحہ بلاک ہو جائے تو مختلف انٹرنیٹ کنکشن (مثلاً VPN یا فون انٹرنیٹ) استعمال کرنے کی کوشش کریں۔ بدقسمتی سے، Alipay کا صفحہ اکثر صرف <strong>چین کے مین لینڈ</strong> سے قابل رسائی ہوتا ہے۔ آپ کو اپنا VPN عارضی طور پر غیر فعال کرنا پڑ سکتا ہے، یا چین کے مین لینڈ (یا کبھی کبھار ہانگ کانگ بھی کام کرتا ہے) کے لیے VPN استعمال کرنا پڑ سکتا ہے۔ <span %(span_circle)s>1</span>Alipay پر عطیہ کریں اس Alipay اکاؤنٹ کے ذریعے %(total)s کی کل رقم عطیہ کریں <a %(a_account)s>یہاں</a> Alipay ہدایات <span %(span_circle)s>1</span> ہمارے کسی کرپٹو اکاؤنٹ میں ٹرانسفر کریں کل رقم %(total)s ان پتوں میں سے کسی ایک پر عطیہ کریں: کرپٹو ہدایات بٹ کوائن (BTC) خریدنے کے لیے ہدایات پر عمل کریں۔ آپ کو صرف اتنی رقم خریدنے کی ضرورت ہے جتنی آپ عطیہ کرنا چاہتے ہیں، %(total)s۔ ہمارا بٹ کوائن (BTC) ایڈریس وصول کنندہ کے طور پر درج کریں، اور اپنی عطیہ کی رقم %(total)s بھیجنے کے لیے ہدایات پر عمل کریں: <span %(span_circle)s>1</span>Pix پر عطیہ کریں کل رقم %(total)s عطیہ کریں <a %(a_account)s>اس Pix اکاؤنٹ کا استعمال کرتے ہوئے Pix کی ہدایات <span %(span_circle)s>1</span>وی چیٹ پر عطیہ کریں <a %(a_account)s>اس WeChat اکاؤنٹ</a> کا استعمال کرتے ہوئے کل رقم %(total)s عطیہ کریں وی چیٹ ہدایات مندرجہ ذیل "کریڈٹ کارڈ سے بٹ کوائن" ایکسپریس سروسز میں سے کسی کا استعمال کریں، جو صرف چند منٹ لیتے ہیں: BTC / Bitcoin پتہ (بیرونی والٹ): BTC / Bitcoin رقم: فارم میں درج ذیل تفصیلات پُر کریں: اگر اس معلومات میں سے کوئی بھی پرانی ہو گئی ہو، تو براہ کرم ہمیں ای میل کریں تاکہ ہمیں آگاہ کیا جا سکے۔ براہ کرم اس <span %(underline)s>مخصوص رقم</span> کا استعمال کریں۔ آپ کی کل لاگت کریڈٹ کارڈ فیس کی وجہ سے زیادہ ہو سکتی ہے۔ چھوٹی رقم کے لیے یہ بدقسمتی سے ہماری رعایت سے زیادہ ہو سکتی ہے۔ (کم از کم: %(minimum)s، پہلی ٹرانزیکشن کے لیے کوئی تصدیق نہیں) (کم از کم: %(minimum)s) (کم از کم: %(minimum)s) (کم از کم: %(minimum)s، پہلی ٹرانزیکشن کے لیے کوئی تصدیق نہیں) (کم از کم: %(minimum)s) (کم از کم: %(minimum)s ملک کے لحاظ سے، پہلی ٹرانزیکشن کے لیے کوئی تصدیق نہیں) PYUSD کوائن (PayPal USD) خریدنے کے لئے ہدایات پر عمل کریں۔ تھوڑی زیادہ خریدیں (ہم تجویز کرتے ہیں کہ %(more)s زیادہ خریدیں) جتنا آپ عطیہ کر رہے ہیں (%(amount)s)، تاکہ ٹرانزیکشن فیس کو پورا کیا جا سکے۔ جو بچ جائے گا وہ آپ کے پاس رہے گا۔ اپنے PayPal ایپ یا ویب سائٹ میں "PYUSD" صفحے پر جائیں۔ "Transfer" بٹن %(icon)s دبائیں، اور پھر "Send"۔ اسٹیٹس اپ ڈیٹ کریں ٹائمر کو دوبارہ ترتیب دینے کے لیے، صرف ایک نیا عطیہ کریں۔ براہ کرم نیچے دی گئی BTC رقم کا استعمال کریں، <em>یورو یا ڈالر</em> نہیں، ورنہ ہمیں صحیح رقم نہیں ملے گی اور ہم آپ کی رکنیت کو خودکار طور پر تصدیق نہیں کر سکیں گے۔ ریوولٹ پر بٹ کوائن (BTC) خریدیں تھوڑی زیادہ خریدیں (ہم تجویز کرتے ہیں کہ آپ %(more)s زیادہ خریدیں) جتنا آپ عطیہ کر رہے ہیں (%(amount)s)، تاکہ ٹرانزیکشن فیس کو پورا کیا جا سکے۔ جو کچھ بچ جائے گا وہ آپ کے پاس رہے گا۔ ریوولٹ میں "کرپٹو" صفحے پر جائیں تاکہ بٹ کوائن (BTC) خرید سکیں۔ بٹ کوائن ہمارے پتے پر منتقل کریں چھوٹے عطیات (25 ڈالر سے کم) کے لیے، آپ کو رش یا ترجیح استعمال کرنے کی ضرورت ہو سکتی ہے۔ "بٹ کوائن بھیجیں" بٹن پر کلک کریں تاکہ "نکالنا" ہو سکے۔ یورو سے BTC میں تبدیل کرنے کے لیے %(icon)s آئیکن دبائیں۔ نیچے BTC کی مقدار درج کریں اور "بھیجیں" پر کلک کریں۔ اگر آپ کو مشکل ہو تو <a %(help_video)s>اس ویڈیو</a> کو دیکھیں۔ حالت: 1 2 مرحلہ وار رہنمائی نیچے دی گئی مرحلہ وار گائیڈ دیکھیں۔ ورنہ آپ اس اکاؤنٹ سے لاک آؤٹ ہو سکتے ہیں! اگر آپ نے ابھی تک نہیں کیا ہے، تو لاگ ان کرنے کے لیے اپنی خفیہ کلید لکھ لیں: آپ کے عطیہ کا شکریہ! باقی وقت: عطیہ %(amount)s کو %(account)s منتقل کریں تصدیق کا انتظار ہے (چیک کرنے کے لیے صفحہ کو ریفریش کریں)… منتقلی کا انتظار (چیک کرنے کے لیے صفحہ ریفریش کریں)… پہلے پچھلے 24 گھنٹوں میں تیز ڈاؤن لوڈز روزانہ کی حد میں شمار ہوتے ہیں۔ فاسٹ پارٹنر سرورز سے ڈاؤن لوڈز کو %(icon)s سے نشان زد کیا گیا ہے۔ آخری 18 گھنٹے ابھی تک کوئی فائل ڈاؤن لوڈ نہیں ہوئی۔ ڈاؤن لوڈ کی گئی فائلیں عوامی طور پر نہیں دکھائی جاتی ہیں۔ تمام اوقات UTC میں ہیں۔ ڈاؤن لوڈ کردہ فائلیں اگر آپ نے تیز اور سست دونوں ڈاؤن لوڈز کے ساتھ فائل ڈاؤن لوڈ کی ہے، تو یہ دو بار ظاہر ہوگی۔ زیادہ فکر نہ کریں، بہت سے لوگ ہماری طرف سے منسلک ویب سائٹس سے ڈاؤن لوڈ کر رہے ہیں، اور مشکل میں پڑنا انتہائی نایاب ہے۔ تاہم، محفوظ رہنے کے لئے ہم VPN (ادائیگی شدہ) یا <a %(a_tor)s>Tor</a> (مفت) استعمال کرنے کی سفارش کرتے ہیں۔ میں نے جارج آرویل کی کتاب 1984 ڈاؤن لوڈ کی ہے، کیا پولیس میرے دروازے پر آئے گی؟ آپ آنا ہیں! آنا کون ہے؟ ہمارے پاس ممبران کے لیے ایک مستحکم JSON API ہے، تیز ڈاؤن لوڈ URL حاصل کرنے کے لیے: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON کے اندر دستاویزات)۔ دیگر استعمالات کے لیے، جیسے کہ ہماری تمام فائلوں کے ذریعے تکرار کرنا، کسٹم سرچ بنانا، وغیرہ، ہم <a %(a_generate)s>جنریٹ</a> یا <a %(a_download)s>ڈاؤن لوڈ</a> کرنے کی سفارش کرتے ہیں ہماری ElasticSearch اور MariaDB ڈیٹا بیسز۔ خام ڈیٹا کو دستی طور پر <a %(a_explore)s>JSON فائلوں</a> کے ذریعے دریافت کیا جا سکتا ہے۔ ہماری خام ٹورینٹس کی فہرست کو <a %(a_torrents)s>JSON</a> کے طور پر بھی ڈاؤن لوڈ کیا جا سکتا ہے۔ کیا آپ کے پاس API ہے؟ ہم یہاں کسی بھی کاپی رائٹ شدہ مواد کی میزبانی نہیں کرتے ہیں۔ ہم ایک سرچ انجن ہیں، اور اس طرح صرف میٹا ڈیٹا کو انڈیکس کرتے ہیں جو پہلے سے ہی عوامی طور پر دستیاب ہے۔ جب ان بیرونی ذرائع سے ڈاؤن لوڈ کرتے ہیں، تو ہم تجویز کریں گے کہ آپ اپنے دائرہ اختیار میں قوانین کو چیک کریں کہ کیا اجازت ہے۔ ہم دوسروں کے ذریعہ میزبانی کردہ مواد کے ذمہ دار نہیں ہیں۔ اگر آپ کو یہاں جو کچھ نظر آتا ہے اس کے بارے میں شکایات ہیں، تو آپ کے لیے بہترین طریقہ یہ ہے کہ اصل ویب سائٹ سے رابطہ کریں۔ ہم باقاعدگی سے ان کی تبدیلیوں کو اپنے ڈیٹا بیس میں شامل کرتے ہیں۔ اگر آپ واقعی سوچتے ہیں کہ آپ کے پاس ایک جائز DMCA شکایت ہے جس کا ہمیں جواب دینا چاہیے، تو براہ کرم <a %(a_copyright)s>DMCA / کاپی رائٹ شکایت فارم</a> پُر کریں۔ ہم آپ کی شکایات کو سنجیدگی سے لیتے ہیں، اور جلد از جلد آپ سے رابطہ کریں گے۔ میں کاپی رائٹ کی خلاف ورزی کی اطلاع کیسے دوں؟ یہاں کچھ کتابیں ہیں جو شیڈو لائبریریوں اور ڈیجیٹل تحفظ کی دنیا میں خاص اہمیت رکھتی ہیں: آپ کی پسندیدہ کتابیں کون سی ہیں؟ ہم یہ بھی یاد دلانا چاہیں گے کہ ہمارا تمام کوڈ اور ڈیٹا مکمل طور پر اوپن سورس ہے۔ یہ ہمارے جیسے پروجیکٹس کے لیے منفرد ہے — ہمیں کسی اور پروجیکٹ کا علم نہیں ہے جس کے پاس اتنی بڑی کیٹلاگ ہو جو مکمل طور پر اوپن سورس بھی ہو۔ ہم کسی بھی شخص کا خیرمقدم کرتے ہیں جو سوچتا ہے کہ ہم اپنا پروجیکٹ خراب طریقے سے چلا رہے ہیں کہ وہ ہمارا کوڈ اور ڈیٹا لے کر اپنی شیڈو لائبریری قائم کریں! ہم یہ غصے یا کسی اور وجہ سے نہیں کہہ رہے ہیں — ہم واقعی سوچتے ہیں کہ یہ بہت اچھا ہوگا کیونکہ اس سے سب کے لیے معیار بلند ہوگا، اور انسانیت کی میراث کو بہتر طریقے سے محفوظ کیا جائے گا۔ مجھے نفرت ہے کہ آپ اس پروجیکٹ کو کیسے چلا رہے ہیں! ہم چاہتے ہیں کہ لوگ <a %(a_mirrors)s>مرر</a> سیٹ اپ کریں، اور ہم اس کے لیے مالی مدد فراہم کریں گے۔ میں کیسے مدد کر سکتا ہوں؟ ہم واقعی کرتے ہیں۔ ہمارے میٹا ڈیٹا جمع کرنے کی تحریک ایرن سوارتز کا مقصد "ہر شائع شدہ کتاب کے لیے ایک ویب صفحہ" ہے، جس کے لیے انہوں نے <a %(a_openlib)s>Open Library</a> بنائی۔ اس پروجیکٹ نے اچھا کام کیا ہے، لیکن ہماری منفرد پوزیشن ہمیں وہ میٹا ڈیٹا حاصل کرنے کی اجازت دیتی ہے جو وہ نہیں کر سکتے۔ ایک اور تحریک ہماری یہ خواہش تھی کہ ہم جان سکیں <a %(a_blog)s>دنیا میں کتنی کتابیں ہیں</a>، تاکہ ہم حساب لگا سکیں کہ ہمیں کتنی کتابیں بچانی باقی ہیں۔ کیا آپ میٹا ڈیٹا جمع کرتے ہیں؟ نوٹ کریں کہ mhut.org کچھ IP رینجز کو بلاک کرتا ہے، لہذا VPN کی ضرورت پڑ سکتی ہے۔ <strong>اینڈرائیڈ:</strong> اوپر دائیں کونے میں تین نقطوں والے مینو پر کلک کریں، اور "مرکزی صفحہ میں شامل کریں" منتخب کریں۔ <strong>iOS:</strong> نیچے "شیئر" بٹن پر کلک کریں، اور "مرکزی صفحہ میں شامل کریں" منتخب کریں۔ ہمارے پاس کوئی سرکاری موبائل ایپ نہیں ہے، لیکن آپ اس ویب سائٹ کو ایپ کے طور پر انسٹال کر سکتے ہیں۔ کیا آپ کے پاس موبائل ایپ ہے؟ براہ کرم انہیں <a %(a_archive)s>Internet Archive</a> کو بھیجیں۔ وہ انہیں مناسب طریقے سے محفوظ کریں گے۔ میں کتابیں یا دیگر جسمانی مواد کیسے عطیہ کر سکتا ہوں؟ میں کتابیں کیسے درخواست کروں؟ <a %(a_blog)s>انا کا بلاگ</a>, <a %(a_reddit_u)s>ریڈٹ</a>, <a %(a_reddit_r)s>سبریڈٹ</a> — باقاعدہ اپ ڈیٹس <a %(a_software)s>انا کا سافٹ ویئر</a> — ہمارا اوپن سورس کوڈ <a %(a_datasets)s>Datasets</a> — ڈیٹا کے بارے میں <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — متبادل ڈومینز کیا انا کی آرکائیو کے بارے میں مزید وسائل موجود ہیں؟ <a %(a_translate)s>انا کے سافٹ ویئر پر ترجمہ کریں</a> — ہمارا ترجمہ نظام <a %(a_wikipedia)s>ویکیپیڈیا</a> — ہمارے بارے میں مزید (براہ کرم اس صفحے کو اپ ڈیٹ رکھنے میں مدد کریں، یا اپنی زبان کے لیے ایک صفحہ بنائیں!) اپنی پسند کی ترتیبات منتخب کریں، تلاش کے خانے کو خالی رکھیں، "تلاش" پر کلک کریں، اور پھر اپنے براؤزر کی بک مارک خصوصیت کا استعمال کرتے ہوئے صفحہ کو بک مارک کریں۔ میں اپنی تلاش کی ترتیبات کیسے محفوظ کروں؟ ہم سیکیورٹی محققین کو ہمارے سسٹمز میں کمزوریوں کی تلاش کے لیے خوش آمدید کہتے ہیں۔ ہم ذمہ دارانہ انکشاف کے بڑے حامی ہیں۔ ہم سے <a %(a_contact)s>یہاں</a> رابطہ کریں۔ ہم فی الحال بگ باؤنٹیز دینے سے قاصر ہیں، سوائے ان کمزوریوں کے جو <a %(a_link)s>ہماری گمنامی کو سمجھوتہ کرنے کی صلاحیت</a> رکھتی ہیں، جن کے لیے ہم $10k-50k کی حد میں باؤنٹیز پیش کرتے ہیں۔ ہم مستقبل میں بگ باؤنٹیز کے لیے وسیع تر دائرہ کار پیش کرنا چاہیں گے! براہ کرم نوٹ کریں کہ سوشل انجینئرنگ حملے دائرہ کار سے باہر ہیں۔ اگر آپ جارحانہ سیکیورٹی میں دلچسپی رکھتے ہیں، اور دنیا کے علم اور ثقافت کو محفوظ کرنے میں مدد کرنا چاہتے ہیں، تو ہم سے رابطہ کرنا یقینی بنائیں۔ بہت سے طریقے ہیں جن سے آپ مدد کر سکتے ہیں۔ کیا آپ کے پاس ذمہ دارانہ انکشاف پروگرام ہے؟ ہمارے پاس واقعی اتنے وسائل نہیں ہیں کہ ہم دنیا بھر کے ہر شخص کو تیز رفتار ڈاؤن لوڈز فراہم کر سکیں، جتنا کہ ہم چاہتے ہیں۔ اگر کوئی امیر محسن ہمارے لیے یہ فراہم کرنا چاہے تو یہ حیرت انگیز ہوگا، لیکن اس وقت تک، ہم اپنی پوری کوشش کر رہے ہیں۔ ہم ایک غیر منافع بخش منصوبہ ہیں جو بمشکل عطیات کے ذریعے خود کو برقرار رکھ سکتا ہے۔ اسی وجہ سے ہم نے اپنے شراکت داروں کے ساتھ مفت ڈاؤن لوڈز کے لیے دو نظام نافذ کیے ہیں: مشترکہ سرورز کے ساتھ سست ڈاؤن لوڈز، اور تھوڑا تیز سرورز کے ساتھ ویٹ لسٹ (تاکہ ایک ہی وقت میں ڈاؤن لوڈ کرنے والے لوگوں کی تعداد کم ہو)۔ ہم نے اپنے سست ڈاؤن لوڈز کے لیے <a %(a_verification)s>براؤزر کی تصدیق</a> بھی شامل کی ہے، کیونکہ بصورت دیگر بوٹس اور سکریپرز ان کا غلط استعمال کریں گے، جس سے جائز صارفین کے لیے چیزیں اور بھی سست ہو جائیں گی۔ نوٹ کریں کہ، Tor براؤزر استعمال کرتے وقت، آپ کو اپنی سیکیورٹی سیٹنگز کو ایڈجسٹ کرنے کی ضرورت پڑ سکتی ہے۔ سب سے کم آپشن، جسے “Standard” کہا جاتا ہے، پر Cloudflare ٹرن اسٹائل چیلنج کامیاب ہوتا ہے۔ اعلیٰ آپشنز، جنہیں “Safer” اور “Safest” کہا جاتا ہے، پر چیلنج ناکام ہوتا ہے۔ بڑے فائلوں کے لیے کبھی کبھار سست ڈاؤن لوڈز درمیان میں ٹوٹ سکتے ہیں۔ ہم تجویز کرتے ہیں کہ ایک ڈاؤن لوڈ مینیجر (جیسے JDownloader) استعمال کریں تاکہ بڑے ڈاؤن لوڈز کو خود بخود دوبارہ شروع کیا جا سکے۔ ڈاؤن لوڈز اتنے سست کیوں ہیں؟ اکثر پوچھے گئے سوالات (FAQ) <a %(a_list)s>ٹورینٹ لسٹ جنریٹر</a> استعمال کریں تاکہ ٹورینٹس کی فہرست تیار کی جا سکے جو آپ کی اسٹوریج اسپیس کی حدود میں سب سے زیادہ ضرورت مند ہیں۔ جی ہاں، <a %(a_llm)s>LLM ڈیٹا</a> صفحہ دیکھیں۔ زیادہ تر ٹورینٹس میں فائلیں براہ راست ہوتی ہیں، جس کا مطلب ہے کہ آپ ٹورینٹ کلائنٹس کو صرف مطلوبہ فائلیں ڈاؤن لوڈ کرنے کی ہدایت دے سکتے ہیں۔ یہ تعین کرنے کے لیے کہ کون سی فائلیں ڈاؤن لوڈ کرنی ہیں، آپ ہمارا <a %(a_generate)s>میٹا ڈیٹا جنریٹ</a> کر سکتے ہیں، یا ہماری ElasticSearch اور MariaDB ڈیٹا بیسز <a %(a_download)s>ڈاؤن لوڈ</a> کر سکتے ہیں۔ بدقسمتی سے، کچھ ٹورینٹ مجموعوں میں جڑ میں .zip یا .tar فائلیں ہوتی ہیں، اس صورت میں آپ کو انفرادی فائلوں کو منتخب کرنے سے پہلے پورا ٹورینٹ ڈاؤن لوڈ کرنا ہوگا۔ آسانی سے استعمال ہونے والے ٹولز ابھی تک دستیاب نہیں ہیں، لیکن ہم تعاون کا خیرمقدم کرتے ہیں۔ (ہمارے پاس <a %(a_ideas)s>کچھ خیالات</a> ہیں اس کے لیے۔) طویل جواب: مختصر جواب: آسانی سے نہیں۔ ہم اس فہرست میں ٹورینٹس کے درمیان نقل یا اوورلیپ کو کم سے کم رکھنے کی کوشش کرتے ہیں، لیکن یہ ہمیشہ حاصل نہیں کیا جا سکتا، اور یہ بڑی حد تک ماخذ لائبریریوں کی پالیسیوں پر منحصر ہے۔ ان لائبریریوں کے لیے جو اپنے ٹورینٹس جاری کرتی ہیں، یہ ہمارے ہاتھ میں نہیں ہے۔ انا کی آرکائیو کے ذریعہ جاری کردہ ٹورینٹس کے لیے، ہم صرف MD5 ہیش کی بنیاد پر نقل کو ختم کرتے ہیں، جس کا مطلب ہے کہ ایک ہی کتاب کے مختلف ورژن نقل نہیں ہوتے۔ جی ہاں۔ یہ دراصل PDFs اور EPUBs ہیں، ان میں سے بہت سے ٹورینٹس میں ان کا ایکسٹینشن نہیں ہے۔ دو جگہیں ہیں جہاں آپ ٹورینٹ فائلوں کے لیے میٹا ڈیٹا تلاش کر سکتے ہیں، بشمول فائل کی اقسام/ایکسٹینشنز: 1. ہر مجموعہ یا ریلیز کا اپنا میٹا ڈیٹا ہوتا ہے۔ مثال کے طور پر، <a %(a_libgen_nonfic)s>Libgen.rs ٹورینٹس</a> کا ایک متعلقہ میٹا ڈیٹا ڈیٹا بیس Libgen.rs ویب سائٹ پر ہوسٹ کیا گیا ہے۔ ہم عام طور پر ہر مجموعہ کے <a %(a_datasets)s>ڈیٹاسیٹ پیج</a> سے متعلقہ میٹا ڈیٹا وسائل سے لنک کرتے ہیں۔ 2. ہم <a %(a_generate)s>جنریٹ</a> یا <a %(a_download)s>ڈاؤن لوڈ</a> کرنے کی سفارش کرتے ہیں ہماری ElasticSearch اور MariaDB ڈیٹا بیسز۔ ان میں انا کی آرکائیو کے ہر ریکارڈ کا اس کے متعلقہ ٹورینٹ فائلوں (اگر دستیاب ہو) کے ساتھ میپنگ شامل ہے، ElasticSearch JSON میں "torrent_paths" کے تحت۔ کچھ ٹورینٹ کلائنٹس بڑے پیس سائزز کو سپورٹ نہیں کرتے، جو کہ ہماری بہت سی ٹورینٹس میں ہوتے ہیں (نئے ٹورینٹس کے لیے ہم اب ایسا نہیں کرتے — حالانکہ یہ وضاحتوں کے مطابق درست ہے!)۔ لہذا اگر آپ کو اس مسئلے کا سامنا ہو تو مختلف کلائنٹ آزمائیں، یا اپنے ٹورینٹ کلائنٹ کے بنانے والوں سے شکایت کریں۔ میں سیڈنگ میں مدد کرنا چاہتا ہوں، لیکن میرے پاس زیادہ ڈسک اسپیس نہیں ہے۔ ٹورینٹس بہت سست ہیں؛ کیا میں ڈیٹا براہ راست آپ سے ڈاؤن لوڈ کر سکتا ہوں؟ کیا میں صرف فائلوں کا ایک ذیلی سیٹ ڈاؤن لوڈ کر سکتا ہوں، جیسے کہ صرف ایک خاص زبان یا موضوع؟ آپ ٹورینٹس میں نقل فائلوں کو کیسے ہینڈل کرتے ہیں؟ کیا میں ٹورینٹ کی فہرست کو JSON کے طور پر حاصل کر سکتا ہوں؟ مجھے ٹورینٹس میں PDFs یا EPUBs نظر نہیں آتے، صرف بائنری فائلیں؟ میں کیا کروں؟ میرا ٹورینٹ کلائنٹ آپ کی کچھ ٹورینٹ فائلز / مقناطیسی لنکس کیوں نہیں کھول سکتا؟ ٹورینٹس FAQ میں نئی کتابیں کیسے اپلوڈ کروں؟ براہ کرم <a %(a_href)s>اس شاندار پروجیکٹ</a> کو دیکھیں۔ کیا آپ کے پاس اپ ٹائم مانیٹر ہے؟ Anna’s Archive کیا ہے؟ تیز ڈاؤن لوڈز استعمال کرنے کے لیے ممبر بنیں۔ ہم اب ایمیزون گفٹ کارڈز، کریڈٹ اور ڈیبٹ کارڈز، کرپٹو، Alipay، اور WeChat کو سپورٹ کرتے ہیں۔ آج کے تیز ڈاؤنلوڈز ختم ہو چکے ہیں۔ رسائی پچھلے 30 دنوں میں فی گھنٹہ ڈاؤن لوڈز۔ فی گھنٹہ اوسط: %(hourly)s۔ روزانہ اوسط: %(daily)s۔ ہم اپنے مجموعے کو آسانی سے اور مفت میں کسی کے لیے بھی قابل رسائی بنانے کے لیے شراکت داروں کے ساتھ کام کرتے ہیں۔ ہم یقین رکھتے ہیں کہ ہر کسی کو انسانیت کی اجتماعی حکمت کا حق ہے۔ اور <a %(a_search)s>مصنفین کی قیمت پر نہیں</a>۔ اینّا کے آرکائیو میں استعمال ہونے والے ڈیٹا سیٹس مکمل طور پر کھلے ہیں، اور انہیں ٹورینٹس کے ذریعے بڑی مقدار میں مرر کیا جا سکتا ہے۔ <a %(a_datasets)s>مزید جانیں…</a> طویل مدتی آرکائیو مکمل ڈیٹا بیس تلاش کریں کتابیں، مقالے، رسالے، کامکس، لائبریری ریکارڈز، میٹا ڈیٹا، … ہمارا تمام <a %(a_code)s>کوڈ</a> اور <a %(a_datasets)s>ڈیٹا</a> مکمل طور پر اوپن سورس ہے۔ <span %(span_anna)s>اینا کا آرکائیو</span> ایک غیر منافع بخش منصوبہ ہے جس کے دو مقاصد ہیں: <li><strong>تحفظ:</strong> انسانیت کے تمام علم اور ثقافت کا بیک اپ بنانا۔</li><li><strong>رسائی:</strong> اس علم اور ثقافت کو دنیا کے کسی بھی شخص کے لیے دستیاب بنانا۔</li> ہمارے پاس دنیا کا سب سے بڑا اعلی معیار کے متن کا مجموعہ ہے۔ <a %(a_llm)s>مزید جانیں…</a> LLM training data 🪩 آئینے: رضاکاروں کی کال اگر آپ ایک ہائی رسک گمنام ادائیگی پروسیسر چلاتے ہیں، تو براہ کرم ہم سے رابطہ کریں۔ ہم ایسے لوگوں کی بھی تلاش میں ہیں جو چھوٹے اشتہارات لگانا چاہتے ہیں۔ تمام آمدنی ہمارے تحفظ کی کوششوں میں جاتی ہے۔ تحفظ ہم اندازہ لگاتے ہیں کہ ہم نے دنیا کی تقریباً <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% کتابیں محفوظ کر لی ہیں</a>۔ ہم مختلف <a href="https://en.wikipedia.org/wiki/Shadow_library">شیڈو لائبریریوں</a>، سرکاری لائبریریوں، اور دیگر مجموعوں سے مواد کو ایک جگہ جمع کر کے کتابیں، مقالے، کامکس، رسالے، اور مزید محفوظ کرتے ہیں۔ یہ تمام ڈیٹا ہمیشہ کے لیے محفوظ رہتا ہے کیونکہ اسے بلک میں نقل کرنا آسان بنا دیا گیا ہے — ٹورینٹس کا استعمال کرتے ہوئے — جس کے نتیجے میں دنیا بھر میں کئی کاپیاں بنتی ہیں۔ کچھ شیڈو لائبریریاں پہلے ہی یہ کام خود کرتی ہیں (مثلاً Sci-Hub، Library Genesis)، جبکہ Anna’s Archive ان لائبریریوں کو "آزاد" کرتی ہے جو بلک تقسیم کی پیشکش نہیں کرتی ہیں (مثلاً Z-Library) یا بالکل بھی شیڈو لائبریریاں نہیں ہیں (مثلاً Internet Archive، DuXiu)۔ یہ وسیع تقسیم، اوپن سورس کوڈ کے ساتھ مل کر، ہماری ویب سائٹ کو ٹیک ڈاؤنز کے خلاف مضبوط بناتی ہے، اور انسانیت کے علم اور ثقافت کے طویل مدتی تحفظ کو یقینی بناتی ہے۔ <a href="/datasets">ہمارے ڈیٹا سیٹس</a> کے بارے میں مزید جانیں۔ اگر آپ <a %(a_member)s>رکن</a> ہیں، تو براؤزر کی تصدیق کی ضرورت نہیں ہے۔ 🧬&nbsp;SciDB Sci-Hub کا تسلسل ہے۔ SciDB کھولیں DOI Sci-Hub نے نئے مقالے اپلوڈ کرنا <a %(a_paused)s>روک دیا</a> ہے۔ %(count)s تعلیمی مقالوں تک براہ راست رسائی 🧬&nbsp;SciDB Sci-Hub کا تسلسل ہے، اس کے مانوس انٹرفیس اور پی ڈی ایفز کو براہ راست دیکھنے کے ساتھ۔ دیکھنے کے لیے اپنا DOI درج کریں۔ ہمارے پاس مکمل Sci-Hub مجموعہ ہے، نیز نئے پیپرز بھی۔ زیادہ تر کو Sci-Hub کی طرح مانوس انٹرفیس کے ساتھ براہ راست دیکھا جا سکتا ہے۔ کچھ کو بیرونی ذرائع سے ڈاؤن لوڈ کیا جا سکتا ہے، اس صورت میں ہم ان کے لنکس دکھاتے ہیں۔ آپ ٹورینٹس کو سیڈ کرکے بہت زیادہ مدد کر سکتے ہیں۔ <a %(a_torrents)s>مزید جانیں…</a> >%(count)s سیڈرز <%(count)s سیڈرز %(count_min)s–%(count_max)s سیڈرز 🤝 رضاکاروں کی تلاش ایک غیر منافع بخش، اوپن سورس پروجیکٹ کے طور پر، ہم ہمیشہ لوگوں کی مدد کے لیے تلاش میں رہتے ہیں۔ IPFS ڈاؤنلوڈز %(by)s کی فہرست، تخلیق کردہ <span %(span_time)s>%(time)s</span> محفوظ کریں ❌ کچھ غلط ہو گیا ہے۔ براہ کرم دوبارہ کوشش کریں۔ ✅ محفوظ ہو گیا۔ براہ کرم صفحہ کو دوبارہ لوڈ کریں۔ فہرست خالی ہے۔ ترمیم کریں فائل تلاش کر کے اور "فہرستیں" ٹیب کھول کر اس فہرست میں شامل یا ہٹا دیں۔ فہرست ہم کیسے مدد کر سکتے ہیں اوورلیپ کو ہٹانا (deduplication) متن اور میٹا ڈیٹا نکالنا OCR ہم اپنی مکمل مجموعوں کے ساتھ ساتھ غیر جاری مجموعوں تک تیز رفتار رسائی فراہم کرنے کے قابل ہیں۔ یہ انٹرپرائز سطح کی رسائی ہے جو ہم ہزاروں امریکی ڈالر کی عطیات کی حد میں فراہم کر سکتے ہیں۔ ہم اس کے بدلے میں اعلیٰ معیار کے مجموعے کے لیے بھی تیار ہیں جو ہمارے پاس ابھی تک نہیں ہیں۔ ہم آپ کو رقم واپس کر سکتے ہیں اگر آپ ہمیں ہمارے ڈیٹا کی افزودگی فراہم کر سکتے ہیں، جیسے: انسانی علم کے طویل مدتی آرکائیو کی حمایت کریں، جبکہ اپنے ماڈل کے لیے بہتر ڈیٹا حاصل کریں! <a %(a_contact)s>ہم سے رابطہ کریں</a> تاکہ ہم مل کر کام کرنے کے طریقے پر بات کر سکیں۔ یہ اچھی طرح سمجھا جاتا ہے کہ LLMs اعلیٰ معیار کے ڈیٹا پر ترقی کرتے ہیں۔ ہمارے پاس دنیا میں کتابوں، مقالوں، رسالوں وغیرہ کا سب سے بڑا مجموعہ ہے، جو کچھ اعلیٰ معیار کے متن کے ذرائع ہیں۔ LLM ڈیٹا منفرد پیمانہ اور حد ہمارے مجموعے میں ایک سو ملین سے زیادہ فائلیں شامل ہیں، جن میں تعلیمی جرنلز، نصابی کتابیں، اور رسالے شامل ہیں۔ ہم بڑے موجودہ ذخائر کو یکجا کر کے اس پیمانے کو حاصل کرتے ہیں۔ ہمارے کچھ ماخذ مجموعے پہلے ہی بلک میں دستیاب ہیں (Sci-Hub، اور Libgen کے کچھ حصے)۔ دیگر ذرائع ہم نے خود آزاد کیے۔ <a %(a_datasets)s>Datasets</a> مکمل جائزہ دکھاتا ہے۔ ہمارے مجموعے میں ای بک دور سے پہلے کی لاکھوں کتابیں، مقالے، اور رسالے شامل ہیں۔ اس مجموعے کے بڑے حصے پہلے ہی OCR کیے جا چکے ہیں، اور پہلے ہی اندرونی اوورلیپ بہت کم ہے۔ جاری رکھیں اگر آپ نے اپنی کلید کھو دی ہے، تو براہ کرم <a %(a_contact)s>ہم سے رابطہ کریں</a> اور جتنا ممکن ہو سکے معلومات فراہم کریں۔ آپ کو ہم سے رابطہ کرنے کے لیے عارضی طور پر نیا اکاؤنٹ بنانا پڑ سکتا ہے۔ براہ کرم <a %(a_account)s>لاگ ان کریں</a> اس صفحے کو دیکھنے کے لیے۔</a> سپیم بوٹس کو بہت سے اکاؤنٹس بنانے سے روکنے کے لیے، ہمیں پہلے آپ کے براؤزر کی تصدیق کرنی ہوگی۔ اگر آپ ایک لامتناہی لوپ میں پھنس جاتے ہیں، تو ہم <a %(a_privacypass)s>Privacy Pass</a> انسٹال کرنے کی تجویز دیتے ہیں۔ یہ بھی مددگار ثابت ہو سکتا ہے کہ ایڈ بلاکرز اور دیگر براؤزر ایکسٹینشنز کو بند کر دیں۔ لاگ ان / رجسٹر کریں انا کی آرکائیو عارضی طور پر دیکھ بھال کے لیے بند ہے۔ براہ کرم ایک گھنٹے بعد واپس آئیں۔ متبادل مصنف متبادل تفصیل متبادل ایڈیشن متبادل توسیع متبادل فائل کا نام متبادل ناشر متبادل عنوان تاریخ اوپن سورس کی گئی مزید پڑھیں… تفصیل CADAL SSNO نمبر کے لیے Anna’s Archive تلاش کریں Anna’s Archive میں DuXiu SSID نمبر تلاش کریں اینّا کے آرکائیو میں DuXiu DXID نمبر تلاش کریں انا کے آرکائیو میں ISBN تلاش کریں اینّا کے آرکائیو میں OCLC (ورلڈ کیٹ) نمبر تلاش کریں Open Library ID کے لئے Anna’s Archive تلاش کریں آنا کا آرکائیو آن لائن ناظر %(count)s متاثرہ صفحات ڈاؤن لوڈ کرنے کے بعد: اس فائل کا بہتر ورژن %(link)s پر دستیاب ہو سکتا ہے بلک ٹورینٹ ڈاؤن لوڈز مجموعہ فارمیٹس کے درمیان تبدیل کرنے کے لیے آن لائن ٹولز استعمال کریں۔ تجویز کردہ تبدیلی کے ٹولز: %(links)s بڑے فائلوں کے لیے، ہم ڈاؤن لوڈ مینیجر استعمال کرنے کی تجویز دیتے ہیں تاکہ رکاوٹوں سے بچا جا سکے۔ تجویز کردہ ڈاؤن لوڈ مینیجرز: %(links)s EBSCOhost ای بُک انڈیکس (صرف ماہرین کے لیے) ‫(اوپر دئے گئے ‫”‎GET‏‏“ کے بٹن کو بھی دبائیں) ‫(اوپر دئے گئے ‫”‎GET‏‏“ کے بٹن کو دبائیں) بیرونی ڈاؤن لوڈز <strong>🚀 تیز ڈاؤن لوڈز</strong> آپ کے پاس آج %(remaining)s باقی ہیں۔ رکن بننے کا شکریہ! ❤️ <strong>🚀 تیز ڈاؤنلوڈز</strong> آپ نے آج کے لیے تیز ڈاؤنلوڈز ختم کر دیے ہیں۔ <strong>🚀 تیز ڈاؤن لوڈز</strong> آپ نے حال ہی میں یہ فائل ڈاؤن لوڈ کی ہے۔ لنکس کچھ دیر کے لیے درست رہتے ہیں۔ <strong>🚀 تیز ڈاؤن لوڈز</strong> کتابوں، مقالوں، اور مزید کے طویل مدتی تحفظ کی حمایت کے لیے <a %(a_membership)s>رکن</a> بنیں۔ آپ کی حمایت کے لیے ہمارا شکریہ ادا کرنے کے لیے، آپ کو تیز ڈاؤن لوڈز ملتے ہیں۔ ❤️ 🚀 تیز ڈاؤن لوڈز 🐢 سست ڈاؤن لوڈز Internet Archive سے ادھار لیں IPFS Gateway #%(num)d ‫(IPFS‏ کے ساتھ آپ کو متعدد بار کوشش کرنا پڑے گی) Libgen.li ‫‎Libgen.rs ‏فکشن ‫Libgen.rs ‏غیر فکشن ان کے اشتہارات میں نقصان دہ سافٹ ویئر شامل ہونے کے امکانات ہیں، اس لیے اشتہار بلاکر استعمال کریں یا اشتہارات پر کلک نہ کریں۔ ایمیزون کا "سینڈ ٹو کنڈل" ڈیجاز کا "سینڈ ٹو کوبو/کنڈل" میگز ڈی بی مینولز لائب نیکسس/ایس ٹی سی (Nexus/STC فائلیں ڈاؤن لوڈ کرنے کے لئے غیر معتبر ہو سکتی ہیں) کوئی ڈاؤن لوڈ نہیں ملا۔ ڈاؤنلوڈ کے دئے گئے تمام طریقہ کار محفوظ ہیں۔ تاہم، انٹرنیٹ سے فائلز ڈاؤنلوڈ کرتے وقت ہمیشہ احتیاط برتیں۔ مثلاً، اپنی ڈیوائس کو اپڈیٹ رکھیں۔ (کوئی ری ڈائریکٹ نہیں) ہمارے ناظر میں کھولیں (ناظر میں کھولیں) اختیار #%(num)d: %(link)s %(extra)s CADAL میں اصل ریکارڈ تلاش کریں DuXiu پر دستی طور پر تلاش کریں ISBNdb میں اصل ریکارڈ تلاش کریں ورلڈ کیٹ میں اصل ریکارڈ تلاش کریں اصل ریکارڈ کو Open Library میں تلاش کریں ISBN کے لیے مختلف دیگر ڈیٹا بیسز میں تلاش کریں (صرف پرنٹ معذور سرپرستوں کے لیے) پب میڈ فائل کھولنے کے لیے آپ کو ای بک یا پی ڈی ایف ریڈر کی ضرورت ہوگی، جو فائل کے فارمیٹ پر منحصر ہے۔ تجویز کردہ ای بک ریڈرز: %(links)s اینا کا آرکائیو 🧬 SciDB Sci-Hub: %(doi)s ‏(منسلک ‫DOI‏، ‫Sci-Hub‏ میں میسر نہیں) آپ پی ڈی ایف اور ای پب دونوں فائلیں اپنے کنڈل یا کوبو ای ریڈر پر بھیج سکتے ہیں۔ تجویز کردہ ٹولز: %(links)s <a %(a_slow)s>عمومی سوالات</a> میں مزید معلومات۔ مصنفین اور لائبریریوں کی حمایت کریں اگر آپ کو یہ پسند ہے اور آپ اس کی استطاعت رکھتے ہیں، تو اصل خریدنے پر غور کریں، یا براہ راست مصنفین کی حمایت کریں۔ اگر یہ آپ کی مقامی لائبریری میں دستیاب ہے، تو وہاں سے مفت میں ادھار لینے پر غور کریں۔ پارٹنر سرور ڈاؤن لوڈز اس فائل کے لیے عارضی طور پر دستیاب نہیں ہیں۔ ٹورینٹ قابل اعتماد شراکت داروں سے۔ زی-لائبریری Z-Library پر Tor (ٹور براؤزر درکار ہو گا) بیرونی ڈاؤن لوڈز دکھائیں ‫<span class="font-bold">❌‏ اس فائل میں بعض مسائل ہو سکتے ہیں، اور اسے ماخذ لائبریری میں چھپا دیا گیا ہے۔‫</span>‏ بعض اوقات یہ کاپی رائٹ کے حامل فرد کی درخواست کے باعث ہوتا ہے، بعض اوقات کوئی بہتر متبادل فائل میسر ہوتی ہے، لیکن بعض اوقات یہ فائل کے ساتھ کسی مسئلے کی وجہ سے بھی ہو سکتا ہے۔ یہ شاید ابھی بھی ٹھیک ڈاؤنلوڈ ہو جائے لیکن ہم تجویز کرتے ہیں کہ پہلے آپ کوئی متبادل فائل تلاش کر کے دیکھیں۔ مزید تفصیلات: اگر آپ پھر بھی یہ فائل ڈاؤنلوڈ کرنا چاہتے ہیں، تو یقینی بنائیں کہ آپ اسے صرف قابلِ بھروسہ اور اپڈیٹ شدہ سافٹویئر کے ذریعہ ہی کھولیں۔ میٹا ڈیٹا کے تبصرے AA: Anna’s Archive میں “%(name)s” تلاش کریں کوڈز ایکسپلورر: کوڈز ایکسپلورر میں دیکھیں “%(name)s” یو آر ایل: ویب سائٹ: اگر آپ کے پاس یہ فائل ہے اور یہ ابھی تک Anna’s Archive میں دستیاب نہیں ہے، تو براہ کرم <a %(a_request)s>اسے اپ لوڈ کریں</a> پر غور کریں۔ Internet Archive کنٹرولڈ ڈیجیٹل لینڈنگ فائل “%(id)s” یہ Internet Archive سے فائل کا ریکارڈ ہے، براہ راست ڈاؤن لوڈ کرنے والی فائل نہیں ہے۔ آپ کتاب کو ادھار لینے کی کوشش کر سکتے ہیں (نیچے دیے گئے لنک سے)، یا <a %(a_request)s>فائل کی درخواست</a> کرتے وقت اس URL کا استعمال کر سکتے ہیں۔ میٹا ڈیٹا کو بہتر بنائیں CADAL SSNO %(id)s میٹا ڈیٹا ریکارڈ یہ ایک میٹا ڈیٹا ریکارڈ ہے، ڈاؤن لوڈ کرنے والی فائل نہیں ہے۔ آپ اس URL کو استعمال کر سکتے ہیں جب <a %(a_request)s>فائل کی درخواست کریں</a>۔ DuXiu SSID %(id)s میٹا ڈیٹا ریکارڈ ISBNdb %(id)s میٹا ڈیٹا ریکارڈ میگز ڈی بی آئی ڈی %(id)s میٹا ڈیٹا ریکارڈ نیکسس/ایس ٹی سی آئی ڈی %(id)s میٹا ڈیٹا ریکارڈ OCLC (WorldCat) نمبر %(id)s میٹا ڈیٹا ریکارڈ Open Library %(id)s میٹا ڈیٹا ریکارڈ Sci-Hub فائل “%(id)s” تلاش نہیں کیا جا سکا ‫”%(md5_input)s“‏ ہمارے ڈیٹابیس میں تلاش نہیں کیا جا سکا۔ تبصرہ شامل کریں (%(count)s) آپ URL سے md5 حاصل کر سکتے ہیں، مثلاً اس فائل کے بہتر ورژن کا MD5 (اگر قابل اطلاق ہو)۔ اگر کوئی اور فائل ہے جو اس فائل سے قریب تر ہے (ایک ہی ایڈیشن، ایک ہی فائل ایکسٹینشن اگر آپ کو مل سکے)، جسے لوگوں کو اس فائل کے بجائے استعمال کرنا چاہیے۔ اگر آپ کو اینا کی آرکائیو کے باہر اس فائل کا بہتر ورژن معلوم ہے، تو براہ کرم <a %(a_upload)s>اپ لوڈ کریں</a>. کچھ غلط ہو گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ آپ نے ایک تبصرہ چھوڑا۔ اسے ظاہر ہونے میں ایک منٹ لگ سکتا ہے۔ براہ کرم <a %(a_copyright)s>DMCA / کاپی رائٹ کلیم فارم</a> استعمال کریں۔ مسئلے کی وضاحت کریں (ضروری) اگر اس فائل کی معیار بہترین ہے، تو آپ یہاں اس کے بارے میں کچھ بھی بات کر سکتے ہیں! اگر نہیں، تو براہ کرم "فائل مسئلہ رپورٹ کریں" بٹن استعمال کریں۔ عمدہ فائل کا معیار (%(count)s) فائل کا معیار <a %(a_metadata)s>اس فائل کے میٹا ڈیٹا کو بہتر بنانے</a> کے طریقے سیکھیں۔ مسئلے کی تفصیل براہ کرم <a %(a_login)s>لاگ ان کریں</a>. مجھے یہ کتاب بہت پسند آئی! اس فائل کے معیار کی رپورٹ کر کے کمیونٹی کی مدد کریں! 🙌 کچھ غلط ہو گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں اور دوبارہ کوشش کریں۔ فائل کے مسئلے کی رپورٹ کریں (%(count)s) آپ کی رپورٹ جمع کرانے کا شکریہ۔ یہ اس صفحے پر دکھائی جائے گی، اور اینا کے ذریعہ دستی طور پر جائزہ لیا جائے گا (جب تک کہ ہمارے پاس مناسب اعتدال کا نظام نہ ہو)۔ تبصرہ چھوڑیں رپورٹ جمع کریں اس فائل میں کیا مسئلہ ہے؟ ادھار (%(count)s) تبصرے (%(count)s) ڈاؤن لوڈز (%(count)s) میٹا ڈیٹا دریافت کریں (%(count)s) فہرستیں (%(count)s) اعداد و شمار (%(count)s) اس خاص فائل کے بارے میں معلومات کے لیے، اس کا <a %(a_href)s>JSON فائل</a> دیکھیں۔ یہ ایک فائل ہے جو <a %(a_ia)s>IA کی کنٹرولڈ ڈیجیٹل لینڈنگ</a> لائبریری کے ذریعہ منظم کی گئی ہے، اور تلاش کے لیے اینا کی آرکائیو کے ذریعہ انڈیکس کی گئی ہے۔ ان مختلف ڈیٹا سیٹس کے بارے میں معلومات کے لیے جو ہم نے مرتب کیے ہیں، <a %(a_datasets)s>ڈیٹا سیٹس صفحہ</a> دیکھیں۔ منسلک ریکارڈ سے میٹا ڈیٹا اوپن لائبریری پر میٹا ڈیٹا بہتر کریں "فائل MD5" ایک ہیش ہے جو فائل کے مواد سے حساب کیا جاتا ہے، اور اس مواد کی بنیاد پر معقول حد تک منفرد ہوتا ہے۔ تمام شیڈو لائبریریاں جو ہم نے یہاں انڈیکس کی ہیں، بنیادی طور پر فائلوں کی شناخت کے لیے MD5s استعمال کرتی ہیں۔ ایک فائل متعدد شیڈو لائبریریوں میں ظاہر ہو سکتی ہے۔ ان مختلف ڈیٹا سیٹس کے بارے میں معلومات کے لیے جو ہم نے مرتب کیے ہیں، <a %(a_datasets)s>ڈیٹا سیٹس صفحہ</a> دیکھیں۔ فائل کے معیار کی رپورٹ کریں کل ڈاؤن لوڈز: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} چیک میٹا ڈیٹا %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} گوگل بکس %(id)s} گڈریڈز %(id)s} ہاتھی ٹرسٹ %(id)s} ISBNdb %(id)s} آئی ایس بی این جی آر پی %(id)s} لیبی %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} آر ایس ایل %(id)s} ٹرینٹر %(id)s} انتباہ: متعدد منسلک ریکارڈز: جب آپ Anna’s Archive پر کسی کتاب کو دیکھتے ہیں، تو آپ مختلف فیلڈز دیکھ سکتے ہیں: عنوان، مصنف، پبلشر، ایڈیشن، سال، تفصیل، فائل نام، اور مزید۔ ان تمام معلومات کو <em>میٹا ڈیٹا</em> کہا جاتا ہے۔ چونکہ ہم مختلف <em>سورس لائبریریوں</em> سے کتابیں جمع کرتے ہیں، ہم اس سورس لائبریری میں دستیاب میٹا ڈیٹا کو دکھاتے ہیں۔ مثال کے طور پر، اگر ہمیں کوئی کتاب Library Genesis سے ملی ہے، تو ہم Library Genesis کے ڈیٹا بیس سے عنوان دکھائیں گے۔ کبھی کبھی ایک کتاب <em>متعدد</em> سورس لائبریریوں میں موجود ہوتی ہے، جن میں مختلف میٹا ڈیٹا فیلڈز ہو سکتے ہیں۔ اس صورت میں، ہم ہر فیلڈ کا سب سے طویل ورژن دکھاتے ہیں، کیونکہ امید ہے کہ اس میں سب سے زیادہ مفید معلومات ہوں گی! ہم پھر بھی تفصیل کے نیچے دیگر فیلڈز دکھائیں گے، مثلاً "متبادل عنوان" (لیکن صرف اگر وہ مختلف ہوں)۔ ہم سورس لائبریری سے <em>کوڈز</em> جیسے کہ شناخت کنندگان اور درجہ بندیاں بھی نکالتے ہیں۔ <em>شناخت کنندگان</em> کسی کتاب کے خاص ایڈیشن کی منفرد نمائندگی کرتے ہیں؛ مثالیں ہیں ISBN، DOI، Open Library ID، Google Books ID، یا Amazon ID۔ <em>درجہ بندیاں</em> متعدد مشابہ کتابوں کو گروپ کرتی ہیں؛ مثالیں ہیں Dewey Decimal (DCC)، UDC، LCC، RVK، یا GOST۔ کبھی کبھی یہ کوڈز سورس لائبریریوں میں واضح طور پر لنک ہوتے ہیں، اور کبھی کبھی ہم انہیں فائل نام یا تفصیل سے نکال سکتے ہیں (بنیادی طور پر ISBN اور DOI)۔ ہم شناخت کنندگان کو <em>صرف میٹا ڈیٹا کی کلیکشنز</em> میں ریکارڈز تلاش کرنے کے لیے استعمال کر سکتے ہیں، جیسے OpenLibrary، ISBNdb، یا WorldCat/OCLC۔ اگر آپ ان کلیکشنز کو براؤز کرنا چاہتے ہیں تو ہمارے سرچ انجن میں ایک مخصوص <em>میٹا ڈیٹا ٹیب</em> ہے۔ ہم میچنگ ریکارڈز کو غائب میٹا ڈیٹا فیلڈز کو بھرنے کے لیے استعمال کرتے ہیں (مثلاً اگر کوئی عنوان غائب ہے)، یا مثلاً "متبادل عنوان" کے طور پر (اگر کوئی موجودہ عنوان ہے)۔ کسی کتاب کے میٹا ڈیٹا کی اصل جگہ دیکھنے کے لیے، کتاب کے صفحے پر <em>“تکنیکی تفصیلات” ٹیب</em> دیکھیں۔ اس میں اس کتاب کے خام JSON کے لنک کے ساتھ اصل ریکارڈز کے خام JSON کے پوائنٹرز شامل ہیں۔ مزید معلومات کے لیے، درج ذیل صفحات دیکھیں: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, اور <a %(a_example)s>Example metadata JSON</a>۔ آخر میں، ہمارا تمام میٹا ڈیٹا <a %(a_generated)s>generated</a> یا <a %(a_downloaded)s>downloaded</a> کے طور پر ElasticSearch اور MariaDB ڈیٹا بیسز میں دستیاب ہے۔ پس منظر آپ میٹا ڈیٹا کو بہتر بنا کر کتابوں کے تحفظ میں مدد کر سکتے ہیں! پہلے، Anna’s Archive پر میٹا ڈیٹا کے بارے میں پس منظر پڑھیں، اور پھر Open Library کے ساتھ لنکنگ کے ذریعے میٹا ڈیٹا کو بہتر بنانے کا طریقہ سیکھیں، اور Anna’s Archive پر مفت ممبرشپ حاصل کریں۔ میٹا ڈیٹا کو بہتر بنائیں تو اگر آپ کو خراب میٹا ڈیٹا کے ساتھ کوئی فائل ملتی ہے، تو آپ اسے کیسے ٹھیک کریں گے؟ آپ سورس لائبریری میں جا سکتے ہیں اور میٹا ڈیٹا کو ٹھیک کرنے کے طریقہ کار پر عمل کر سکتے ہیں، لیکن اگر کوئی فائل متعدد سورس لائبریریوں میں موجود ہو تو کیا کریں؟ Anna’s Archive پر ایک شناخت کنندہ خاص طور پر اہمیت رکھتا ہے۔ <strong>Open Library پر annas_archive md5 فیلڈ ہمیشہ تمام دیگر میٹا ڈیٹا کو اوور رائیڈ کرتا ہے!</strong> پہلے تھوڑا پیچھے چلتے ہیں اور Open Library کے بارے میں سیکھتے ہیں۔ Open Library 2006 میں Aaron Swartz نے اس مقصد کے ساتھ قائم کی تھی کہ "ہر شائع شدہ کتاب کے لیے ایک ویب صفحہ"۔ یہ کتاب کے میٹا ڈیٹا کے لیے ایک قسم کی ویکیپیڈیا ہے: ہر کوئی اسے ایڈٹ کر سکتا ہے، یہ آزادانہ طور پر لائسنس یافتہ ہے، اور بلک میں ڈاؤن لوڈ کی جا سکتی ہے۔ یہ ایک کتابی ڈیٹا بیس ہے جو ہمارے مشن کے ساتھ سب سے زیادہ ہم آہنگ ہے — درحقیقت، Anna’s Archive Aaron Swartz کے وژن اور زندگی سے متاثر ہوا ہے۔ پہیہ دوبارہ ایجاد کرنے کے بجائے، ہم نے اپنے رضاکاروں کو Open Library کی طرف موڑنے کا فیصلہ کیا۔ اگر آپ کو کوئی کتاب ملتی ہے جس کا میٹا ڈیٹا غلط ہے، تو آپ مندرجہ ذیل طریقے سے مدد کر سکتے ہیں: نوٹ کریں کہ یہ صرف کتابوں کے لیے کام کرتا ہے، نہ کہ علمی مقالوں یا دیگر قسم کی فائلوں کے لیے۔ دیگر قسم کی فائلوں کے لیے ہم اب بھی ماخذ لائبریری تلاش کرنے کی سفارش کرتے ہیں۔ Anna’s Archive میں تبدیلیاں شامل ہونے میں چند ہفتے لگ سکتے ہیں، کیونکہ ہمیں تازہ ترین Open Library ڈیٹا ڈمپ ڈاؤن لوڈ کرنا ہوتا ہے، اور ہماری سرچ انڈیکس کو دوبارہ تیار کرنا ہوتا ہے۔  <a %(a_openlib)s>Open Library ویب سائٹ</a> پر جائیں۔ صحیح کتاب کا ریکارڈ تلاش کریں۔ <strong>انتباہ:</strong> یقینی بنائیں کہ آپ صحیح <strong>ایڈیشن</strong> منتخب کریں۔ Open Library میں، آپ کے پاس "ورکس" اور "ایڈیشنز" ہیں۔ ایک "ورک" ہو سکتا ہے "Harry Potter and the Philosopher's Stone"۔ ایک "ایڈیشن" ہو سکتا ہے: 1997 کا پہلا ایڈیشن Bloomsbery کی طرف سے شائع ہوا جس میں 256 صفحات ہیں۔ 2003 کا پیپر بیک ایڈیشن Raincoast Books کی طرف سے شائع ہوا جس میں 223 صفحات ہیں۔ 2000 کا پولش ترجمہ “Harry Potter I Kamie Filozoficzn” Media Rodzina کی طرف سے شائع ہوا جس میں 328 صفحات ہیں۔ ان تمام ایڈیشنز کے مختلف ISBN اور مختلف مواد ہیں، لہذا صحیح ایڈیشن منتخب کرنے کا خیال رکھیں! ریکارڈ میں ترمیم کریں (یا اگر کوئی موجود نہیں ہے تو نیا بنائیں)، اور جتنا زیادہ مفید معلومات شامل کر سکتے ہیں شامل کریں! آپ یہاں ہیں تو ریکارڈ کو واقعی شاندار بنائیں۔ “ID Numbers” کے تحت “Anna’s Archive” منتخب کریں اور کتاب کا MD5 Anna’s Archive سے شامل کریں۔ یہ URL میں “/md5/” کے بعد حروف اور نمبروں کی لمبی سیریز ہے۔ Anna’s Archive میں دیگر فائلیں تلاش کرنے کی کوشش کریں جو اس ریکارڈ سے بھی میل کھاتی ہوں، اور انہیں بھی شامل کریں۔ مستقبل میں ہم انہیں Anna’s Archive سرچ پیج پر ڈپلیکیٹس کے طور پر گروپ کر سکتے ہیں۔ جب آپ کام مکمل کر لیں، تو وہ URL لکھ لیں جو آپ نے ابھی اپ ڈیٹ کیا ہے۔ جب آپ نے Anna’s Archive MD5s کے ساتھ کم از کم 30 ریکارڈز اپ ڈیٹ کر لیے ہوں، تو ہمیں ایک <a %(a_contact)s>ای میل</a> بھیجیں اور ہمیں فہرست بھیجیں۔ ہم آپ کو Anna’s Archive کی مفت ممبرشپ دیں گے، تاکہ آپ یہ کام زیادہ آسانی سے کر سکیں (اور آپ کی مدد کے لیے شکریہ کے طور پر)۔ یہ اعلی معیار کی ترامیم ہونی چاہئیں جو کافی مقدار میں معلومات شامل کرتی ہوں، ورنہ آپ کی درخواست مسترد کر دی جائے گی۔ آپ کی درخواست بھی مسترد کر دی جائے گی اگر کوئی بھی ترمیم Open Library کے موڈریٹرز کی طرف سے واپس لی گئی یا درست کی گئی۔ Open Library لنکنگ اگر آپ ہمارے کام کی ترقی اور آپریشنز میں نمایاں طور پر شامل ہو جاتے ہیں، تو ہم آپ کے ساتھ مزید عطیہ کی آمدنی شیئر کرنے پر بات کر سکتے ہیں، تاکہ آپ اسے ضروریات کے مطابق استعمال کر سکیں۔ ہم صرف ہوسٹنگ کے لئے ادائیگی کریں گے جب آپ نے سب کچھ سیٹ اپ کر لیا ہو، اور یہ ثابت کر دیا ہو کہ آپ آرکائیو کو اپ ڈیٹس کے ساتھ اپ ٹو ڈیٹ رکھنے کے قابل ہیں۔ اس کا مطلب ہے کہ آپ کو پہلے 1-2 مہینے کے اخراجات خود برداشت کرنے ہوں گے۔ آپ کے وقت کی کوئی معاوضہ نہیں دی جائے گی (اور نہ ہی ہمارا)، کیونکہ یہ خالص رضاکارانہ کام ہے۔ ہم ابتدائی طور پر $200 فی مہینہ تک ہوسٹنگ اور VPN کے اخراجات برداشت کرنے کے لئے تیار ہیں۔ یہ ایک بنیادی سرچ سرور اور DMCA-محفوظ پراکسی کے لئے کافی ہے۔ ہوسٹنگ کے اخراجات براہ کرم <strong>ہم سے رابطہ نہ کریں</strong> اجازت مانگنے کے لئے، یا بنیادی سوالات کے لئے۔ عمل الفاظ سے زیادہ بلند آواز میں بولتے ہیں! تمام معلومات وہاں موجود ہیں، لہذا بس اپنے آئینہ کو سیٹ اپ کرنے کے ساتھ آگے بڑھیں۔ جب آپ کو مسائل کا سامنا ہو تو ہمارے Gitlab پر ٹکٹ یا مرج ریکویسٹ پوسٹ کرنے میں ہچکچاہٹ نہ کریں۔ ہمیں آپ کے ساتھ کچھ آئینہ مخصوص خصوصیات بنانی پڑ سکتی ہیں، جیسے کہ "Anna’s Archive" سے آپ کی ویب سائٹ کے نام پر ری برانڈنگ، (ابتدائی طور پر) صارف اکاؤنٹس کو غیر فعال کرنا، یا کتاب کے صفحات سے ہماری مرکزی سائٹ پر لنک کرنا۔ جب آپ کا آئینہ چل رہا ہو، تو براہ کرم ہم سے رابطہ کریں۔ ہم آپ کی OpSec کا جائزہ لینا پسند کریں گے، اور جب یہ مضبوط ہو جائے گا، تو ہم آپ کے آئینہ کو لنک کریں گے، اور آپ کے ساتھ قریب سے کام کرنا شروع کریں گے۔ پہلے سے شکریہ ان سب کا جو اس طرح تعاون کرنے کے لئے تیار ہیں! یہ کمزور دل والوں کے لئے نہیں ہے، لیکن یہ انسانی تاریخ کی سب سے بڑی واقعی کھلی لائبریری کی لمبی عمر کو مضبوط کرے گا۔ شروع کرنا Anna’s Archive کی لچک کو بڑھانے کے لیے، ہم رضاکاروں کی تلاش میں ہیں جو مرر چلائیں۔ آپ کا ورژن واضح طور پر ایک آئینہ کے طور پر ممتاز ہے، مثلاً "Bob’s Archive, an Anna’s Archive mirror"۔ آپ اس کام سے منسلک خطرات کو قبول کرنے کے لئے تیار ہیں، جو کہ اہم ہیں۔ آپ کو آپریشنل سیکیورٹی کی گہری سمجھ ہے جو درکار ہے۔ <a %(a_shadow)s>یہ</a> <a %(a_pirate)s>پوسٹس</a> آپ کے لئے خود واضح ہیں۔ ابتدائی طور پر ہم آپ کو ہمارے پارٹنر سرور ڈاؤن لوڈز تک رسائی نہیں دیں گے، لیکن اگر سب کچھ ٹھیک رہا، تو ہم آپ کے ساتھ یہ شیئر کر سکتے ہیں۔ آپ Anna’s Archive کے اوپن سورس کوڈبیس کو چلاتے ہیں، اور آپ باقاعدگی سے کوڈ اور ڈیٹا دونوں کو اپ ڈیٹ کرتے ہیں۔ آپ ہمارے <a %(a_codebase)s>کوڈبیس</a> میں تعاون کرنے کے لئے تیار ہیں — ہماری ٹیم کے ساتھ مل کر — تاکہ یہ ممکن ہو سکے۔ ہم اس کی تلاش میں ہیں: مرر: رضاکاروں کی کال ایک اور عطیہ کریں۔ ابھی تک کوئی عطیہ نہیں۔ <a %(a_donate)s>اپنا پہلا عطیہ کریں۔</a> عطیات کی تفصیلات عوامی طور پر ظاہر نہیں کی جاتیں۔ میری عطیات 📡 ہماری مجموعہ کی بلک مررنگ کے لیے، <a %(a_datasets)s>Datasets</a> اور <a %(a_torrents)s>Torrents</a> صفحات دیکھیں۔ آپ کے IP ایڈریس سے پچھلے 24 گھنٹوں میں ڈاؤنلوڈز: %(count)s۔ 🚀 تیز ڈاؤنلوڈز حاصل کرنے اور براؤزر چیکس کو چھوڑنے کے لیے، <a %(a_membership)s>رکن بنیں</a>۔ شراکت دار ویب سائٹ سے ڈاؤن لوڈ کریں انتظار کرتے ہوئے کسی دوسرے ٹیب میں Anna’s Archive کو براؤز کرنے کے لیے آزاد محسوس کریں (اگر آپ کا براؤزر بیک گراؤنڈ ٹیبز کو ریفریش کرنے کی حمایت کرتا ہے)۔ ایک ہی وقت میں متعدد ڈاؤن لوڈ صفحات کو لوڈ کرنے کے لیے آزاد محسوس کریں (لیکن براہ کرم ہر سرور پر ایک وقت میں صرف ایک فائل ڈاؤن لوڈ کریں)۔ ایک بار جب آپ کو ڈاؤن لوڈ لنک مل جائے تو یہ کئی گھنٹوں کے لیے درست ہوتا ہے۔ انتظار کرنے کا شکریہ، یہ ویب سائٹ کو ہر کسی کے لیے مفت میں قابل رسائی رکھتا ہے! 😊 🔗 اس فائل کے تمام ڈاؤنلوڈ لنکس: <a %(a_main)s>فائل کا مرکزی صفحہ</a>۔ ❌ سست ڈاؤن لوڈز Cloudflare VPNs یا Cloudflare IP پتوں سے دستیاب نہیں ہیں۔ ❌ سست ڈاؤن لوڈز صرف سرکاری ویب سائٹ کے ذریعے دستیاب ہیں۔ ملاحظہ کریں %(websites)s۔ 📚 ڈاؤن لوڈ کرنے کے لئے درج ذیل URL استعمال کریں: <a %(a_download)s>ابھی ڈاؤن لوڈ کریں</a>۔ تاکہ ہر کسی کو مفت میں فائلیں ڈاؤن لوڈ کرنے کا موقع مل سکے، آپ کو اس فائل کو ڈاؤن لوڈ کرنے سے پہلے انتظار کرنا ہوگا۔ براہ کرم اس فائل کو ڈاؤن لوڈ کرنے کے لیے <span %(span_countdown)s>%(wait_seconds)s</span> سیکنڈ انتظار کریں۔ انتباہ: پچھلے 24 گھنٹوں میں آپ کے آئی پی ایڈریس سے بہت زیادہ ڈاؤن لوڈز ہوئے ہیں۔ ڈاؤن لوڈز معمول سے سست ہو سکتے ہیں۔ اگر آپ VPN، مشترکہ انٹرنیٹ کنکشن استعمال کر رہے ہیں، یا آپ کا ISP IPs شیئر کرتا ہے، تو یہ وارننگ اس کی وجہ سے ہو سکتی ہے۔ محفوظ کریں ❌ کچھ غلط ہو گیا۔ براہ کرم دوبارہ کوشش کریں۔ ✅ محفوظ کر لیا گیا۔ براہ کرم صفحہ دوبارہ لوڈ کریں۔ اپنا ڈسپلے نام تبدیل کریں۔ آپ کا شناختی نمبر (جو “#” کے بعد آتا ہے) تبدیل نہیں کیا جا سکتا۔ پروفائل بنایا گیا <span %(span_time)s>%(time)s</span> ترمیم کریں فہرستیں فائل تلاش کر کے اور "فہرستیں" ٹیب کھول کر نئی فہرست بنائیں۔ ابھی تک کوئی فہرست نہیں ہے پروفائل نہیں ملا۔ پروفائل اس وقت، ہم کتاب کی درخواستیں قبول نہیں کر سکتے۔ براہ کرم ہمیں اپنی کتاب کی درخواستیں ای میل نہ کریں۔ براہ کرم اپنی درخواستیں Z-Library یا Libgen فورمز پر کریں۔ ریکارڈ انا کی آرکائیو میں DOI: %(doi)s ڈاؤنلوڈ کریں SciDB نیکسس/ایس ٹی سی ابھی تک کوئی پیش نظارہ دستیاب نہیں ہے۔ فائل کو <a %(a_path)s>اینا کا آرکائیو</a> سے ڈاؤن لوڈ کریں۔ انسانی علم کی رسائی اور طویل مدتی تحفظ کی حمایت کے لیے، <a %(a_donate)s>رکن</a> بنیں۔ بونس کے طور پر، 🧬&nbsp;SciDB اراکین کے لیے تیز لوڈ ہوتا ہے، بغیر کسی حد کے۔ کام نہیں کر رہا؟ <a %(a_refresh)s>ریفریش</a> کرنے کی کوشش کریں۔ Sci-Hub مخصوص تلاش کا میدان شامل کریں تفصیلات اور میٹا ڈیٹا تبصرے تلاش کریں اشاعت کا سال اعلیٰ رسائی مواد ڈسپلے فہرست جدول فائل کی قسم زبان ترتیب دیں سب سے بڑا موزوں ترین تازہ ترین (فائل سائز) (اوپن سورسڈ) (اشاعت کا سال) قدیم ترین بے ترتیب سب سے چھوٹا ماخذ AA کے ذریعے سکریپ اور اوپن سورس کیے گئے ڈیجیٹل لائبریری (%(count)s) جرنل مضامین (%(count)s) ہم نے میچز پائے ہیں: %(in)s۔ آپ وہاں پائی گئی URL کا حوالہ دے سکتے ہیں جب <a %(a_request)s>فائل کی درخواست</a> کرتے ہیں۔ میٹا ڈیٹا (%(count)s) کوڈز کے ذریعے سرچ انڈیکس کو دریافت کرنے کے لیے، <a %(a_href)s>Codes Explorer</a> استعمال کریں۔ سرچ انڈیکس ہر ماہ اپڈیٹ کیا جاتا ہے۔ اِس وقت اس میں ‫%(last_data_refresh_date)s‏ تک کا مواد موجود ہے۔ مزید تکنیکی تفصیلات کیلئے ‫%(link_open_tag)s‏ڈیٹاسیٹ کا صفحہ‫</a>‏ دیکھیں۔ خارج کریں صرف شامل کریں غیر چیک شدہ مزید… اگلا … پچھلا یہ سرچ انڈیکس فی الحال Internet Archive کی کنٹرولڈ ڈیجیٹل لینڈنگ لائبریری سے میٹا ڈیٹا شامل کرتا ہے۔ <a %(a_datasets)s>ہمارے ڈیٹا سیٹس کے بارے میں مزید</a>۔ مزید ڈیجیٹل لائبریریوں کے لیے، <a %(a_wikipedia)s>ویکیپیڈیا</a> اور <a %(a_mobileread)s>موبائل ریڈ وکی</a> دیکھیں۔ DMCA / کاپی رائٹ دعووں کے لیے <a %(a_copyright)s>یہاں کلک کریں</a>۔ ڈاؤن لوڈ کا وقت تلاش میں مسئلہ۔ <a %(a_reload)s>صفحہ دوبارہ لوڈ کریں</a>۔ اگر مسئلہ برقرار رہے تو براہ کرم ہمیں %(email)s پر ای میل کریں۔ تیز ڈاؤن لوڈ حقیقت میں، کوئی بھی ہمارے <a %(a_torrents)s>متحدہ فہرست ٹورینٹس</a> کو سیڈ کر کے ان فائلوں کو محفوظ رکھنے میں مدد کر سکتا ہے۔ ➡️ کبھی کبھار یہ غلطی سے ہوتا ہے جب سرچ سرور سست ہوتا ہے۔ ایسے معاملات میں، <a %(a_attrs)s>ری لوڈنگ</a> مدد کر سکتا ہے۔ ‫❌ اس فائل میں بعض مسائل ہو سکتے ہیں۔ کاغذات کی تلاش میں ہیں؟ یہ تلاش انڈیکس فی الحال مختلف میٹا ڈیٹا ذرائع سے میٹا ڈیٹا شامل کرتا ہے۔ <a %(a_datasets)s>ہمارے ڈیٹا سیٹس کے بارے میں مزید</a>۔ دنیا بھر میں تحریری کاموں کے لیے میٹا ڈیٹا کے بہت سے ذرائع ہیں۔ <a %(a_wikipedia)s>یہ ویکیپیڈیا صفحہ</a> ایک اچھی شروعات ہے، لیکن اگر آپ کو دیگر اچھی فہرستوں کا علم ہے تو براہ کرم ہمیں بتائیں۔ میٹا ڈیٹا کے لیے، ہم اصل ریکارڈز دکھاتے ہیں۔ ہم ریکارڈز کو ضم نہیں کرتے۔ ہمارے پاس اس وقت دنیا کا سب سے جامع کھلا کیٹلاگ ہے جس میں کتابیں، مقالے، اور دیگر تحریری کام شامل ہیں۔ ہم Sci-Hub، Library Genesis، Z-Library، <a %(a_datasets)s>اور مزید</a> کو مرر کرتے ہیں۔ ‫<span class="font-bold">‏کوئی فائل تلاش نہیں ہوئی۔‫</span>‏ تلاش کیلئے کم یا مختلف الفاظ اور فلٹرز استعمال کر کے دیکھیں۔ نتائج %(from)s-%(to)s (%(total)s کل) اگر آپ کو کوئی اور "شیڈو لائبریریاں" ملتی ہیں جنہیں ہمیں آئینہ دار کرنا چاہیے، یا اگر آپ کے کوئی سوالات ہیں، تو براہ کرم ہم سے %(email)s پر رابطہ کریں۔ ‫%(num)d‏ جزوی طور پر مقابل نتائج ‫%(num)d+‏ جزوی طور پر مقابل نتائج ڈیجیٹل لائبریریوں میں فائلز تلاش کرنے کے لیے باکس میں ٹائپ کریں۔ ہمارے کیٹلاگ میں %(count)s براہ راست ڈاؤن لوڈ ہونے والی فائلوں کی تلاش کے لیے باکس میں ٹائپ کریں، جنہیں ہم <a %(a_preserve)s>ہمیشہ کے لیے محفوظ رکھتے ہیں</a>۔ تلاش کرنے کے لیے باکس میں ٹائپ کریں۔ ہمارے کیٹلاگ میں %(count)s تعلیمی مقالے اور جرنل آرٹیکلز تلاش کرنے کے لیے باکس میں ٹائپ کریں، جنہیں ہم <a %(a_preserve)s>ہمیشہ کے لیے محفوظ رکھتے ہیں</a>۔ لائبریریوں سے میٹا ڈیٹا تلاش کرنے کے لیے باکس میں ٹائپ کریں۔ یہ <a %(a_request)s>فائل کی درخواست کرتے وقت</a> مفید ہو سکتا ہے۔ ٹپ: تیز تر نیویگیشن کے لئے کی بورڈ شارٹ کٹس استعمال کریں “/” (تلاش فوکس)، “enter” (تلاش)، “j” (اوپر)، “k” (نیچے)، “<” (پچھلا صفحہ)، “>” (اگلا صفحہ)۔ یہ میٹا ڈیٹا ریکارڈز ہیں، <span %(classname)s>ڈاؤن لوڈ کرنے کے قابل فائلیں نہیں</span>۔ تلاش کی ترتیبات تلاش کریں ڈیجیٹل قرضہ ڈاؤن لوڈ کریں جرنل مضامین میٹا ڈیٹا نئی تلاش %(search_input)s - تلاش تلاش میں زیادہ وقت لگ گیا، جس کا مطلب ہے کہ آپ کو غیر درست نتائج نظر آ سکتے ہیں۔ کبھی کبھی <a %(a_reload)s>صفحہ دوبارہ لوڈ کرنا</a> مددگار ثابت ہوتا ہے۔ تلاش میں زیادہ وقت لگ گیا، جو کہ وسیع سوالات کے لیے عام ہے۔ فلٹر کی گنتی درست نہیں ہو سکتی۔ بڑے اپلوڈز (10,000 سے زیادہ فائلیں) جو Libgen یا Z-Library کے ذریعہ قبول نہیں کی جاتی ہیں، براہ کرم ہم سے %(a_email)s پر رابطہ کریں۔ Libgen.li کے لیے، پہلے <a %(a_forum)s >ان کے فورم</a> پر صارف نام %(username)s اور پاس ورڈ %(password)s کے ساتھ لاگ ان کریں، اور پھر ان کے <a %(a_upload_page)s >اپلوڈ صفحے</a> پر واپس جائیں۔ فی الحال، ہم تجویز کرتے ہیں کہ نئی کتابیں Library Genesis فورکس پر اپلوڈ کریں۔ یہاں ایک <a %(a_guide)s>مفید گائیڈ</a> ہے۔ نوٹ کریں کہ دونوں فورکس جو ہم اس ویب سائٹ پر انڈیکس کرتے ہیں، اسی اپلوڈ سسٹم سے کھینچتے ہیں۔ چھوٹی اپلوڈز (10,000 فائلز تک) کے لیے براہ کرم انہیں دونوں %(first)s اور %(second)s پر اپلوڈ کریں۔ متبادل کے طور پر، آپ انہیں Z-Library <a %(a_upload)s>یہاں</a> اپلوڈ کر سکتے ہیں۔ اکیڈمک مقالے اپلوڈ کرنے کے لیے، براہ کرم (Library Genesis کے علاوہ) انہیں <a %(a_stc_nexus)s>STC Nexus</a> پر بھی اپلوڈ کریں۔ وہ نئے مقالوں کے لیے بہترین شیڈو لائبریری ہیں۔ ہم نے ابھی تک انہیں ضم نہیں کیا ہے، لیکن ہم کسی وقت کریں گے۔ آپ ان کے <a %(a_telegram)s>اپلوڈ بوٹ پر Telegram</a> استعمال کر سکتے ہیں، یا اگر آپ کے پاس اپلوڈ کرنے کے لیے بہت زیادہ فائلیں ہیں تو ان کے پن کردہ پیغام میں درج ایڈریس سے رابطہ کریں۔ <span %(label)s>بھاری رضاکارانہ کام (USD$50-USD$5,000 انعامات):</span> اگر آپ ہمارے مشن کے لیے بہت زیادہ وقت اور/یا وسائل وقف کر سکتے ہیں، تو ہم آپ کے ساتھ قریب سے کام کرنا پسند کریں گے۔ آخر کار آپ اندرونی ٹیم میں شامل ہو سکتے ہیں۔ اگرچہ ہمارا بجٹ محدود ہے، ہم سب سے زیادہ شدید کام کے لیے <span %(bold)s>💰 مالی انعامات</span> دینے کے قابل ہیں۔ <span %(label)s>ہلکا رضاکارانہ کام:</span> اگر آپ یہاں اور وہاں صرف چند گھنٹے نکال سکتے ہیں، تو بھی آپ کی مدد کے بہت سے طریقے ہیں۔ ہم مستقل رضاکاروں کو <span %(bold)s>🤝 Anna’s Archive کی ممبرشپ</span> کے ساتھ انعام دیتے ہیں۔ Anna’s Archive آپ جیسے رضاکاروں پر انحصار کرتا ہے۔ ہم تمام عزم کی سطحوں کا خیرمقدم کرتے ہیں، اور مدد کے لئے دو اہم اقسام ہیں جن کی ہم تلاش کر رہے ہیں: اگر آپ اپنا وقت رضاکارانہ طور پر نہیں دے سکتے، تو آپ اب بھی <a %(a_donate)s>پیسے عطیہ کر کے</a>, <a %(a_torrents)s>ہمارے ٹورینٹس کو سیڈ کر کے</a>, <a %(a_uploading)s>کتابیں اپ لوڈ کر کے</a>, یا <a %(a_help)s>اپنے دوستوں کو Anna’s Archive کے بارے میں بتا کر</a> ہماری بہت مدد کر سکتے ہیں۔ <span %(bold)s>کمپنیاں:</span> ہم اپنی کلیکشنز تک تیز رفتار براہ راست رسائی کے بدلے میں انٹرپرائز سطح کے عطیہ یا نئی کلیکشنز کے تبادلے (مثلاً نئے اسکینز، OCR’ed datasets، ہمارے ڈیٹا کو بہتر بنانا) کی پیشکش کرتے ہیں۔ اگر آپ یہ ہیں تو <a %(a_contact)s>ہم سے رابطہ کریں</a>۔ ہمارا <a %(a_llm)s>LLM صفحہ</a> بھی دیکھیں۔ انعامات ہم ہمیشہ ایسے لوگوں کی تلاش میں رہتے ہیں جن کے پاس مضبوط پروگرامنگ یا جارحانہ سیکیورٹی کی مہارت ہو تاکہ وہ شامل ہو سکیں۔ آپ انسانیت کی میراث کو محفوظ رکھنے میں سنجیدہ کردار ادا کر سکتے ہیں۔ شکریہ کے طور پر، ہم ٹھوس شراکتوں کے لیے رکنیت دیتے ہیں۔ بڑے شکریہ کے طور پر، ہم خاص طور پر اہم اور مشکل کاموں کے لیے مالی انعامات دیتے ہیں۔ اسے ملازمت کے متبادل کے طور پر نہیں دیکھا جانا چاہیے، لیکن یہ ایک اضافی ترغیب ہے اور اس سے آنے والے اخراجات میں مدد مل سکتی ہے۔ ہمارا زیادہ تر کوڈ اوپن سورس ہے، اور جب انعام دیا جائے گا تو ہم آپ کے کوڈ سے بھی یہی توقع کریں گے۔ کچھ استثنیات ہیں جن پر ہم انفرادی بنیادوں پر بات کر سکتے ہیں۔ انعامات اس شخص کو دیے جاتے ہیں جو سب سے پہلے کام مکمل کرتا ہے۔ براہ کرم انعامی ٹکٹ پر تبصرہ کریں تاکہ دوسروں کو معلوم ہو کہ آپ کسی چیز پر کام کر رہے ہیں، تاکہ دوسرے انتظار کریں یا آپ سے رابطہ کریں تاکہ ٹیم بنائیں۔ لیکن آگاہ رہیں کہ دوسرے بھی اس پر کام کرنے کے لیے آزاد ہیں اور آپ سے پہلے مکمل کرنے کی کوشش کر سکتے ہیں۔ تاہم، ہم ناقص کام کے لیے انعامات نہیں دیتے۔ اگر دو اعلیٰ معیار کی شراکتیں قریب قریب (ایک یا دو دن کے اندر) کی جائیں، تو ہم اپنی صوابدید پر دونوں کو انعام دینے کا انتخاب کر سکتے ہیں، مثال کے طور پر پہلی شراکت کے لیے 100%% اور دوسری شراکت کے لیے 50%% (یعنی کل 150%%)۔ بڑے انعامات کے لیے (خاص طور پر اسکریپنگ انعامات)، براہ کرم ہم سے رابطہ کریں جب آپ نے اس کا ~5%% مکمل کر لیا ہو، اور آپ کو یقین ہو کہ آپ کا طریقہ مکمل سنگ میل تک پہنچ جائے گا۔ آپ کو اپنا طریقہ ہمارے ساتھ شیئر کرنا ہوگا تاکہ ہم رائے دے سکیں۔ اس طرح ہم یہ فیصلہ کر سکتے ہیں کہ اگر کئی لوگ انعام کے قریب پہنچ رہے ہیں تو کیا کرنا ہے، جیسے کہ ممکنہ طور پر اسے کئی لوگوں کو دینا، لوگوں کو ٹیم بنانے کی ترغیب دینا، وغیرہ۔ انتباہ: زیادہ انعامی کام <span %(bold)s>مشکل</span> ہیں — ہو سکتا ہے کہ آسان کاموں سے شروع کرنا عقلمندی ہو۔ ہمارے <a %(a_gitlab)s>Gitlab مسائل کی فہرست</a> پر جائیں اور "لیبل ترجیح" کے لحاظ سے ترتیب دیں۔ یہ تقریباً ان کاموں کی ترتیب دکھاتا ہے جن کی ہمیں پرواہ ہے۔ جن کاموں میں واضح انعامات نہیں ہیں وہ اب بھی رکنیت کے اہل ہیں، خاص طور پر وہ جو "قبول شدہ" اور "Anna’s favorite" کے نشان زد ہیں۔ آپ "اسٹارٹر پروجیکٹ" سے شروع کرنا چاہیں گے۔ ہلکا رضاکارانہ کام اب ہمارے پاس %(matrix)s پر ایک ہم آہنگ میٹرکس چینل بھی ہے۔ اگر آپ کے پاس کچھ گھنٹے فارغ ہیں، تو آپ کئی طریقوں سے مدد کر سکتے ہیں۔ ضرور <a %(a_telegram)s>Telegram پر رضاکاروں کی چیٹ</a> میں شامل ہوں۔ تشکر کے طور پر، ہم عام طور پر بنیادی سنگ میلوں کے لیے 6 ماہ کی “خوش قسمت لائبریرین” دیتے ہیں، اور مسلسل رضاکارانہ کام کے لیے مزید دیتے ہیں۔ تمام سنگ میلوں کے لیے اعلیٰ معیار کا کام ضروری ہے — ناقص کام ہمیں فائدہ پہنچانے کے بجائے نقصان پہنچاتا ہے اور ہم اسے مسترد کر دیں گے۔ براہ کرم <a %(a_contact)s>ہمیں ای میل کریں</a> جب آپ سنگ میل تک پہنچیں۔ %(links)s لنکس یا اسکرین شاٹس ان درخواستوں کی جو آپ نے پوری کیں۔ Z-Library یا Library Genesis فورمز پر کتاب (یا مقالہ وغیرہ) کی درخواستیں پوری کرنا۔ ہمارے پاس اپنی کتاب کی درخواست کا نظام نہیں ہے، لیکن ہم ان لائبریریوں کی عکاسی کرتے ہیں، اس لیے انہیں بہتر بنانا Anna’s Archive کو بھی بہتر بناتا ہے۔ سنگ میل کام کام پر منحصر ہے۔ ہمارے <a %(a_telegram)s>Telegram پر رضاکاروں کی چیٹ</a> پر پوسٹ کیے گئے چھوٹے کام۔ عام طور پر رکنیت کے لیے، کبھی کبھار چھوٹے انعامات کے لیے۔ ہمارے رضاکار چیٹ گروپ میں چھوٹے کام پوسٹ کیے گئے ہیں۔ یقینی بنائیں کہ آپ جن مسائل کو حل کرتے ہیں ان پر تبصرہ چھوڑیں، تاکہ دوسرے آپ کے کام کو نقل نہ کریں۔ %(links)s ریکارڈز کے لنکس جو آپ نے بہتر کیے۔ آپ <a %(a_list)s >بے ترتیب metadata مسائل کی فہرست</a> کو نقطہ آغاز کے طور پر استعمال کر سکتے ہیں۔ Open Library کے ساتھ <a %(a_metadata)s>لنکنگ</a> کے ذریعے میٹا ڈیٹا کو بہتر بنائیں۔ یہ آپ کو آنا کے آرکائیو کے بارے میں کسی کو بتاتے ہوئے اور ان کا شکریہ ادا کرتے ہوئے دکھانا چاہیے۔ %(links)s لنکس یا اسکرین شاٹس۔ آنا کے آرکائیو کا پیغام پھیلانا۔ مثال کے طور پر، AA پر کتابوں کی سفارش کرنا، ہمارے بلاگ پوسٹس کا لنک دینا، یا عام طور پر لوگوں کو ہماری ویب سائٹ کی طرف متوجہ کرنا۔ ایک زبان کا مکمل ترجمہ کریں (اگر یہ پہلے سے مکمل ہونے کے قریب نہیں تھا)۔ <a %(a_translate)s>ویب سائٹ کا ترجمہ</a>۔ ایڈیٹ ہسٹری کا لنک دکھائیں کہ آپ نے اہم شراکتیں کیں۔ اپنی زبان میں Anna’s Archive کے لیے ویکیپیڈیا صفحہ کو بہتر بنائیں۔ دیگر زبانوں میں AA کے ویکیپیڈیا صفحہ، اور ہماری ویب سائٹ اور بلاگ سے معلومات شامل کریں۔ دیگر متعلقہ صفحات پر AA کے حوالہ جات شامل کریں۔ رضاکارانہ کام اور انعامات 