msgid "layout.index.invalid_request"
msgstr "Cerere invalidă. Vizitează %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "Lib<PERSON>en"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Bibliotecă de împrumut a Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " și "

msgid "layout.index.header.tagline_and_more"
msgstr "și altele"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Acesta este un site oglindă pentru %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Copiem și punem la dispoziție în mod liber %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Toate datele și codul nostru sunt open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Cea mai mare bibliotecă cu adevărat dechisă din istoria umanității."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;cărți, %(paper_count)s&nbsp;articole științifice — conservate pentru totdeauna."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Cea mai mare biblioteca cu sursă deschisă și date deschise din lume. ⭐️&nbsp;Includem Sci-Hub, Library Genesis, Z-Library și altele. 📈&nbsp;%(book_any)s cărți, %(journal_article)s articole științifice, %(book_comic)s benzi desenate, %(magazine)s reviste — conservate pentru totdeauna."

msgid "layout.index.header.tagline_short"
msgstr "📚 Cea mai mare bibliotecă cu sursă deschisă și date deschise din lume.<br>⭐️ Include Sci-hub, Libgen, Zlib și altele."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadate incorecte (de exemplu titlu, descriere, imagine de copertă)"

msgid "common.md5_report_type_mapping.download"
msgstr "Probleme de descărcare (de exemplu conectare eșuată, mesaj de eroare, conexiune lentă)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Fișierul nu poate fi deschis (de exemplu fișier corupt, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Calitate scăzută (de exemplu probleme de formatare, scanare necalitativă, pagini lipsă)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fișierul trebuie eliminat (de exemplu publicitate, conținut abuziv)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamație privind drepturile de autor"

msgid "common.md5_report_type_mapping.other"
msgstr "Altele"

msgid "common.membership.tier_name.bonus"
msgstr "Descărcări bonus"

msgid "common.membership.tier_name.2"
msgstr "Șoarece de bibliotecă briliant"

msgid "common.membership.tier_name.3"
msgstr "Bibliotecar Norocos"

msgid "common.membership.tier_name.4"
msgstr "Orbitorul Acumulator-de-date"

msgid "common.membership.tier_name.5"
msgstr "Arhivar Uimitor"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "neplătit"

msgid "common.donation.order_processing_status_labels.1"
msgstr "plătit"

msgid "common.donation.order_processing_status_labels.2"
msgstr "anulat"

msgid "common.donation.order_processing_status_labels.3"
msgstr "expirat"

msgid "common.donation.order_processing_status_labels.4"
msgstr "în așteptarea confirmării de la Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "invalid"

msgid "page.donate.title"
msgstr "Donează"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Ai deja o <a %(a_donation)s>donație</a> în curs. Te rog termină sau anulează acea donație înainte de a face una nouă."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Arată-mi toate donațiile mele</a>"

msgid "page.donate.header.text1"
msgstr "Anna’s Archive este un proiect non-profit, open-source, open data. Donând și devenind membru, ne susțineți operațiunile și dezvoltarea. Tuturor membrilor noștri: vă mulțumim că ne susțineți! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Pentru mai multe informații, consultați <a %(a_donate)s>Donații FAQ</a>."

msgid "page.donate.refer.text1"
msgstr "Pentru și mai multe descărcări, <a %(a_refer)s>invitați-vă prietenii</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Primiți un bonus de %(percentage)s%% descărcări rapide, pentru că ați fost invitat de utilizatorul %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Aceasta se aplică pe întreaga perioadă de membru."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s descărcări rapide pe zi"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "dacă donați luna aceasta!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / lună"

msgid "page.donate.buttons.join"
msgstr "Alătură-te"

msgid "page.donate.buttons.selected"
msgstr "Selectat"

msgid "page.donate.buttons.up_to_discounts"
msgstr "până la %(percentage)s%% reducere"

msgid "page.donate.perks.scidb"
msgstr "Articole SciDB<strong>nelimitate</strong> fără verificare"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Acces API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Câștigă <strong>%(percentage)s%% descărcări bonus</strong> prin <a %(a_refer)s>recomandarea prietenilor</a>."

msgid "page.donate.perks.credits"
msgstr "Numele tău de utilizator sau menționarea anonimă în credite"

msgid "page.donate.perks.previous_plus"
msgstr "Avantajele anterioare, plus:"

msgid "page.donate.perks.early_access"
msgstr "Acces timpuriu la funcții noi"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Acces exclusiv Telegram cu actualizări din culise"

msgid "page.donate.perks.adopt"
msgstr "„Adoptă un torrent”: numele tău de utilizator sau mesajul tău într-un nume de fișier torrent <div %(div_months)s>o dată la 12 luni de membru</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Statut legendar în conservarea cunoștințelor și culturii umanității"

msgid "page.donate.expert.title"
msgstr "Acces pentru experți"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contactați-ne"

msgid "page.donate.small_team"
msgstr "Suntem o echipă mică de voluntari. Ne-ar putea lua 1-2 săptămâni pentru a răspunde."

msgid "page.donate.expert.unlimited_access"
msgstr "Acces<strong>nelimitat</strong>de viteză ridicată"

msgid "page.donate.expert.direct_sftp"
msgstr "Servere <strong>SFTP</strong> directe"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donații la nivel de antrepriză sau schimb pentru colecții noi (de exemplu scanări noi, seturi de date scanate cu OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Primim cu bucurie donații mari de la persoane sau instituții bogate. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pentru donații de peste 5000 USD, vă rugăm să ne contactați direct la %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Fiți conștienți că, deși abonamentele de pe această pagină sunt „pe lună”, ele sunt donații unice (ne-recurente). Consultați <a %(faq)s>Donații FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Dacă doriți să faceți o donație (orice sumă) fără a deveni membru, folosiți această adresă Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Vă rugăm să selectați o metodă de plată."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(momentan indisponibil)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s card cadou"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Card bancar (folosind aplicația)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Cripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Card de credit/debit"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regulat)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Card / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Credit/debit/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Card bancar"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Card de credit/debit (rezervă)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Card de credit/debit 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Cu crypto puteți dona folosind BTC, ETH, XMR și SOL. Utilizați această opțiune dacă sunteți deja familiarizat cu criptomonedele."

msgid "page.donate.payment.desc.crypto2"
msgstr "Cu criptomonede poți dona folosind BTC, ETH, XMR și altele."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Dacă folosiți criptomonede pentru prima dată, vă sugerăm să folosiți %(options)s pentru a cumpăra și dona Bitcoin (criptomoneda originală și cea mai utilizată)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Pentru a dona folosind PayPal, vom folosi PayPal Crypto, care ne permite să rămânem anonimi. Apreciem că v-ați acordat timp pentru a învăța cum să donați folosind această metodă, deoarece ne ajută foarte mult."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Donează folosind PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Donează folosind Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Dacă ai Cash App, aceasta este cea mai ușoară modalitate de a dona!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Ține cont de faptul că pentru tranzacțiile mai mici de %(amount)s, Cash App poate percepe un comision de %(fee)s. Pentru %(amount)s sau mai mult, e gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donează folosind Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Dacă ai Revolut, aceasta este cea mai simplă modalitate de a dona!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Donează cu un card de credit sau de debit."

msgid "page.donate.payment.desc.google_apple"
msgstr "Ar putea funcționa și Google Pay sau Apple Pay."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Ține cont de faptul că pentru donații mici comisioanele cardului de credit ar putea elimina reducerea noastră de %(discount)s%%, așa că recomandăm abonamente pe o durată mai lungă."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Ține cont de faptul că pentru donații mici comisioanele sunt ridicate, așa că recomandăm abonamente mai lungi."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Cu Binance, cumpărați Bitcoin cu un card de credit/debit sau cont bancar, și apoi donați acel Bitcoin către noi. În acest fel, putem rămâne siguri și anonimi când acceptăm donația dumneavoastră."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance este disponibil în aproape toate țările și suportă majoritatea băncilor și cardurilor de credit/debit. Aceasta este în prezent recomandarea noastră principală. Apreciem că vă luați timpul să învățați cum să donați folosind această metodă, deoarece ne ajută foarte mult."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donează folosind contul tău PayPal regulat."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donați folosind card de credit/debit, PayPal sau Venmo. Puteți alege între acestea pe pagina următoare."

msgid "page.donate.payment.desc.amazon"
msgstr "Donează folosind un card cadou Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Ține cont de faptul că suntem nevoiți să rotunjim la valori acceptate de către revânzătorii noștri (minim %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> Acceptăm doar Amazon.com, nu alte site-uri Amazon. De exemplu, .de, .co.uk, .ca NU sunt acceptate."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANT:</strong> Această opțiune este pentru %(amazon)s. Dacă doriți să utilizați un alt site Amazon, selectați-l mai sus."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Această metodă folosește un furnizor de criptomonede ca intermediar de conversie. Acest lucru poate fi puțin confuz, așa că vă rugăm să folosiți această metodă doar dacă celelalte metode de plată nu funcționează. De asemenea, nu funcționează în toate țările."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donați folosind un card de credit/debit, prin aplicația Alipay (foarte ușor de configurat)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instalați aplicația Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instalați aplicația Alipay din <a %(a_app_store)s>Apple App Store</a> sau <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Înregistrați-vă folosind numărul de telefon."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nu sunt necesare alte detalii personale."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Adăugați card bancar"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Suportate: Visa, MasterCard, JCB, Diners Club și Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Consultați <a %(a_alipay)s>acest ghid</a> pentru mai multe informații."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nu putem accepta carduri de credit/debit direct, deoarece băncile nu vor să colaboreze cu noi. ☹ Totuși, există mai multe modalități de a folosi carduri de credit/debit, utilizând alte metode de plată:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Card cadou Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Trimiteți-ne carduri cadou Amazon.com folosind cardul dvs. de credit/debit."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay acceptă carduri internaționale de credit/debit. Vezi <a %(a_alipay)s>acest ghid</a> pentru mai multe informații."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) suportă carduri de credit/debit internaționale. În aplicația WeChat, mergeți la „Eu => Servicii => Portofel => Adăugați un card”. Dacă nu vedeți asta, activați-l folosind „Eu => Setări => General => Instrumente => Weixin Pay => Activare”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Puteți cumpăra criptomonede folosind carduri de credit/debit."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servicii expres crypto"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Serviciile expres sunt convenabile, dar percep taxe mai mari."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Puteți folosi acest serviciu în locul unui schimb crypto dacă doriți să faceți rapid o donație mai mare și nu vă deranjează o taxă de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Asigurați-vă că trimiteți exact suma crypto afișată pe pagina de donații, nu suma în $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "În caz contrar, taxa va fi scăzută și nu putem procesa automat abonamentul dumneavoastră."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minim: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minim: %(minimum)s în funcție de țară, fără verificare pentru prima tranzacție)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minim: %(minimum)s, fără verificare pentru prima tranzacție)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minim: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minim: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minim: %(minimum)s, fără verificare pentru prima tranzacție)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Dacă oricare dintre aceste informații este depășită, vă rugăm să ne trimiteți un email pentru a ne anunța."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Pentru carduri de credit, carduri de debit, Apple Pay și Google Pay, folosim „Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). În sistemul lor, o „cafea” este egală cu 5$, astfel încât donația dumneavoastră va fi rotunjită la cel mai apropiat multiplu de 5."

msgid "page.donate.duration.intro"
msgstr "Selectați pentru cât timp doriți să vă abonați."

msgid "page.donate.duration.1_mo"
msgstr "1 lună"

msgid "page.donate.duration.3_mo"
msgstr "3 luni"

msgid "page.donate.duration.6_mo"
msgstr "6 luni"

msgid "page.donate.duration.12_mo"
msgstr "12 luni"

msgid "page.donate.duration.24_mo"
msgstr "24 de luni"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 luni"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 luni"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>după <span %(span_discount)s></span> reduceri</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Această modalitate de plată necesită un minim de %(amount)s. Te rugăm alege o durată sau modalitate de plată diferită."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donează"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Această metodă de plată permite doar un maxim de %(amount)s. Vă rugăm să selectați o durată sau o metodă de plată diferită."

#, fuzzy
msgid "page.donate.login2"
msgstr "Pentru a deveni membru, vă rugăm să <a %(a_login)s>vă autentificați sau să vă înregistrați</a>. Vă mulțumim pentru sprijin!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Selectați moneda cripto preferată:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(cel mai mic cuantum minim)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(folosiți când trimiteți Ethereum de la Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(avertisment: sumă minimă mare)"

msgid "page.donate.submit.confirm"
msgstr "Faceți clic pe butonul Donați pentru a confirma această donație."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donați <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Puteți anula în continuare donația în timpul plății."

msgid "page.donate.submit.success"
msgstr "✅ Redirecționare către pagina de donații…"

msgid "page.donate.submit.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / lună"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "pentru 1 lună"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "pentru 3 luni"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "pentru 6 luni"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "pentru 12 luni"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "pentru 24 de luni"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "pentru 48 de luni"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "pentru 96 de luni"

msgid "page.donate.submit.button.label.1_mo"
msgstr "pentru 1 lună “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "pentru 3 luni “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "pentru 6 luni “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "pentru 12 luni “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "pentru 24 de luni „%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "pentru 48 de luni „%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "pentru 96 de luni „%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donații"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/lună pentru %(duration)s luni, inclusiv %(discounts)s%% discount)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/lună pentru %(duration)s luni)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificator: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Cancel"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Sigur doriți să anulați? Nu anulați dacă ați plătit deja."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Da, vreau să anulez"

msgid "page.donation.header.cancel.success"
msgstr "✅ Donația ta a fost anulată."

msgid "page.donation.header.cancel.new_donation"
msgstr "Fă o nouă donație"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

msgid "page.donation.header.reorder"
msgstr "Comandă din nou"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Ai plătit deja. Dacă oricum doriți să consultați instrucțiunile de plată, faceți clic aici:"

msgid "page.donation.old_instructions.show_button"
msgstr "Afișați instrucțiunile de plată vechi"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Vă mulțumim pentru donația dumneavoastră!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Dacă nu ați făcut-o deja, notați-vă cheia secretă pentru a vă conecta:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Altfel, s-ar putea să fiți blocat din acest cont!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Instrucțiunile de plată sunt acum depășite. Dacă doriți să faceți o altă donație, utilizați butonul „Comandă din nou” de mai sus."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Notă importantă:</strong> prețurile cripto pot fluctua puternic, uneori chiar și cu 20%% în câteva minute. Această fluctuație este totuși mai mică decât taxele pe care le suportăm cu mulți furnizori de plăți, care percep adesea 50-60%% pentru lucrul cu o „organizație caritabilă din umbră” ca noi. <u>Dacă ne trimiteți chitanța cu prețul inițial pe care l-ați plătit, vă vom credita în continuare contul pentru calitatea de membru aleasă</u> (atâta timp cât chitanța nu este mai veche de câteva ore). Apreciem foarte mult că sunteți dispus să suportați astfel de lucruri pentru a ne susține! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Această donație a expirat. Vă rugăm să anulați și să creați una nouă."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instrucțiuni de cripto"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfer într-unul dintre conturile noastre cripto"

msgid "page.donation.payment.crypto.text1"
msgstr "Donați suma totală de %(total)s la una dintre aceste adrese:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Cumpărați Bitcoin pe Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Găsiți pagina „Crypto” în aplicația sau site-ul PayPal. Aceasta se află de obicei în „Finanțe”."

msgid "page.donation.payment.paypal.text3"
msgstr "Urmați instrucțiunile pentru a cumpăra Bitcoin (BTC). Trebuie doar să cumpărați suma pe care doriți să o donați, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transferă Bitcoin la adresa noastră"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Accesați pagina „Bitcoin” din aplicația sau site-ul dvs. PayPal. Apăsați butonul „Transfer” %(transfer_icon)s, apoi „Trimite”."

msgid "page.donation.payment.paypal.text5"
msgstr "Introduceți adresa noastră Bitcoin (BTC) ca destinatar și urmați instrucțiunile pentru a trimite donația dvs. de %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrucțiuni pentru card de credit / debit"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donează prin pagina noastră de card de credit / debit"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donează %(amount)s pe <a %(a_page)s>această pagină</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Consultați ghidul pas cu pas de mai jos."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Stare:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Așteptând confirmarea (reîmprospătați pagina pentru a verifica)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Așteptați transferul (reîmprospătați pagina pentru a verifica)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Timp rămas:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(este posibil să doriți să anulați și să creați o nouă donație)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Pentru a reseta cronometrul, pur și simplu creați o nouă donație."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Actualizează starea"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Dacă întâmpinați probleme, vă rugăm să ne contactați la %(email)s și să includeți cât mai multe informații posibil (cum ar fi capturi de ecran)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Dacă ați plătit deja:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Uneori confirmarea poate dura până la 24 de ore, așa că asigurați-vă că reîmprospătați această pagină (chiar dacă a expirat)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Cumpărați monedă PYUSD pe PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Urmați instrucțiunile pentru a cumpăra monedă PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Cumpărați puțin mai mult (recomandăm %(more)s mai mult) decât suma pe care o donați (%(amount)s), pentru a acoperi taxele de tranzacție. Veți păstra orice rămâne."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Accesați pagina „PYUSD” din aplicația sau site-ul PayPal. Apăsați butonul „Transfer” %(icon)s, apoi „Trimite”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transferați %(amount)s către %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Cumpărați Bitcoin (BTC) pe Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Accesați pagina „Bitcoin” (BTC) din Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Cumpărați puțin mai mult (recomandăm %(more)s mai mult) decât suma pe care o donați (%(amount)s), pentru a acoperi taxele de tranzacție. Veți păstra orice rămâne."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transferați Bitcoin la adresa noastră"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Faceți clic pe butonul „Trimite bitcoin” pentru a face o „retragere”. Schimbați din dolari în BTC apăsând pe pictograma %(icon)s. Introduceți suma BTC de mai jos și faceți clic pe „Trimite”. Consultați <a %(help_video)s>acest video</a> dacă întâmpinați dificultăți."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Pentru donații mici (sub $25), este posibil să fie nevoie să folosiți Rush sau Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Cumpărați Bitcoin (BTC) pe Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Accesați pagina „Crypto” din Revolut pentru a cumpăra Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Cumpărați puțin mai mult (recomandăm %(more)s mai mult) decât suma pe care o donați (%(amount)s), pentru a acoperi taxele de tranzacție. Veți păstra orice rămâne."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transferați Bitcoin la adresa noastră"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Faceți clic pe butonul „Trimite bitcoin” pentru a face o „retragere”. Schimbați din euro în BTC apăsând pe pictograma %(icon)s. Introduceți suma BTC de mai jos și faceți clic pe „Trimite”. Consultați <a %(help_video)s>acest video</a> dacă întâmpinați dificultăți."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Asigurați-vă că folosiți suma în BTC de mai jos, <em>NU</em> euro sau dolari, altfel nu vom primi suma corectă și nu putem confirma automat calitatea de membru."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Pentru donații mici (sub $25) este posibil să fie nevoie să folosiți Rush sau Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Folosiți oricare dintre următoarele servicii „card de credit către Bitcoin” express, care durează doar câteva minute:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Completați următoarele detalii în formular:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Suma BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Vă rugăm să folosiți această <span %(underline)s>sumă exactă</span>. Costul total ar putea fi mai mare din cauza taxelor de pe cardul de credit. Pentru sume mici, acest lucru poate depăși reducerea noastră, din păcate."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Adresa BTC / Bitcoin (portofel extern):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "Instrucțiuni %(coin_name)s"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Suportăm doar versiunea standard a monedelor cripto, fără rețele sau versiuni exotice ale monedelor. Poate dura până la o oră pentru a confirma tranzacția, în funcție de monedă."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scanați codul QR pentru a plăti"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scanați acest cod QR cu aplicația dvs. de portofel cripto pentru a completa rapid detaliile de plată"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Card cadou Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Vă rugăm să folosiți <a %(a_form)s>formularul oficial Amazon.com</a> pentru a ne trimite un card cadou de %(amount)s la adresa de email de mai jos."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Nu putem accepta alte metode de carduri cadou, <strong>doar trimise direct din formularul oficial de pe Amazon.com</strong>. Nu putem returna cardul cadou dacă nu folosiți acest formular."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Introduceți suma exactă: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Vă rugăm să NU scrieți propriul mesaj."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Emailul destinatarului „Către” în formular:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unic pentru contul dvs., nu împărtășiți."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Utilizați o singură dată."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Așteptând cardul cadou… (reîmprospătați pagina pentru a verifica)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "După trimiterea cardului cadou, sistemul nostru automatizat îl va confirma în câteva minute. Dacă acest lucru nu funcționează, încercați să retrimiteți cardul cadou (<a %(a_instr)s>instrucțiuni</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Dacă nici asta nu funcționează, vă rugăm să ne trimiteți un email și Anna va revizui manual (acest lucru poate dura câteva zile), și asigurați-vă că menționați dacă ați încercat să retrimiteți deja."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Exemplu:"

msgid "page.donate.strange_account"
msgstr "Ia în considerare faptul că numele sau fotografia contului ar putea arăta neobișnuit. Nu trebuie să îți faci griji! Aceste conturi sunt gestionate de partenerii noștri de donații. Conturile noastre nu au fost sparte."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instrucțiuni Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donează pe Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donați suma totală de %(total)s folosind <a %(a_account)s>acest cont Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Dacă pagina de donații este blocată, încercați o altă conexiune la internet (de exemplu, VPN sau internet de pe telefon)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Din păcate, pagina Alipay este adesea accesibilă doar din <strong>China continentală</strong>. Este posibil să fie necesar să dezactivați temporar VPN-ul sau să utilizați un VPN către China continentală (sau Hong Kong funcționează uneori)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faceți donația (scanați codul QR sau apăsați butonul)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Deschideți <a %(a_href)s>pagina de donații cu cod QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scanați codul QR cu aplicația Alipay sau apăsați butonul pentru a deschide aplicația Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Vă rugăm să aveți răbdare; pagina poate dura ceva timp să se încarce deoarece este în China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instrucțiuni WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donează pe WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donează suma totală de %(total)s folosind <a %(a_account)s>acest cont WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instrucțiuni Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donează pe Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Donează suma totală de %(total)s folosind <a %(a_account)s>acest cont Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Trimite-ne chitanța prin e-mail"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Trimiteți o chitanță sau o captură de ecran la adresa dvs. personală de verificare. NU folosiți această adresă de email pentru donația PayPal."

msgid "page.donation.footer.text1"
msgstr "Trimiteți o chitanță sau o captură de ecran la adresa dvs. personală de verificare:"

msgid "page.donation.footer.crypto_note"
msgstr "Dacă cursul de schimb cripto a fluctuat în timpul tranzacției, asigurați-vă că includeți chitanța care arată cursul de schimb inițial. Apreciem foarte mult efortul depus pentru a folosi cripto, ne ajută foarte mult!"

msgid "page.donation.footer.text2"
msgstr "După ce ați trimis chitanța prin e-mail, faceți clic pe acest buton, astfel încât Anna să o poată examina manual (acest lucru poate dura câteva zile):"

msgid "page.donation.footer.button"
msgstr "Da, am trimis chitanța pe e-mail"

msgid "page.donation.footer.success"
msgstr "✅ Mulțumim pentru donație! Anna îți va activa manual calitatea de membru în câteva zile."

msgid "page.donation.footer.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Ghid pas cu pas"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Unele dintre pași menționează portofele crypto, dar nu vă faceți griji, nu trebuie să învățați nimic despre crypto pentru asta."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Introduceți adresa de email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Selectați metoda de plată."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selectați din nou metoda de plată."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Selectați portofelul „Self-hosted”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Faceți clic pe „Confirm deținerea”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Ar trebui să primiți o chitanță prin email. Vă rugăm să ne trimiteți aceasta și vom confirma donația cât mai curând posibil."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Vă rugăm să așteptați cel puțin <span %(span_hours)s>24 de ore</span> (și să reîmprospătați această pagină) înainte de a ne contacta."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Dacă ați făcut o greșeală în timpul plății, nu putem face rambursări, dar vom încerca să rezolvăm problema."

msgid "page.my_donations.title"
msgstr "Donațiile mele"

msgid "page.my_donations.not_shown"
msgstr "Detaliile donațiilor nu sunt afișate public."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nicio donație încă. <a %(a_donate)s>Fă prima mea donație.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Faceți o altă donație."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Fișiere descărcate"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Descărcările de la Serverele Partenere Rapide sunt marcate de %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Dacă ați descărcat un fișier cu descărcări rapide și lente, acesta va apărea de două ori."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Descărcările rapide din ultimele 24 de ore se contorizează în limita zilnică."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Toate orele sunt în UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Fișierele descărcate nu sunt afișate public."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Niciun fișier descărcat încă."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ultimele 18 ore"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Mai devreme"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Cont"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Autentificare / Înregistrare"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID cont: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profil public: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Cheie secretă (nu o împărtăși!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "afișează"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Calitatea de membru: <strong>%(tier_name)s</strong> până la %(until_date)s <a %(a_extend)s>(prelungește)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Calitatea de membru: <strong>Niciuna</strong> <a %(a_become)s>(devino membru)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Descărcări rapide utilizate (ultimele 24 de ore): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "care descărcări?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grup exclusiv pe Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Alătură-te nouă aici!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Upgradează la un <a %(a_tier)s>nivel superior</a> pentru a te alătura grupului nostru."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contactează Anna la %(email)s dacă ești interesat să-ți upgradezi calitatea de membru la un nivel superior."

#, fuzzy
msgid "page.contact.title"
msgstr "Email de contact"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Puteți combina mai multe abonamente (descărcările rapide pe 24 de ore vor fi adunate)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profil public"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Fișiere descărcate"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Donațiile mele"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Deconectare"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Ați fost deconectat. Reîncărcați pagina pentru a vă conecta din nou."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Înregistrare reușită! Cheia dvs. secretă este: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Salvați această cheie cu grijă. Dacă o pierdeți, veți pierde accesul la contul dvs."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Marcaj.</strong> Puteți marca această pagină pentru a recupera cheia.</li><li %(li_item)s><strong>Descărcare.</strong> Faceți clic <a %(a_download)s>pe acest link</a> pentru a descărca cheia.</li><li %(li_item)s><strong>Manager de parole.</strong> Utilizați un manager de parole pentru a salva cheia când o introduceți mai jos.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Introduceți cheia secretă pentru a vă conecta:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Cheie secretă"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Autentificare"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Cheie secretă invalidă. Verificați cheia și încercați din nou, sau înregistrați un nou cont mai jos."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Nu pierdeți cheia!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Nu aveți încă un cont?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Înregistrați un cont nou"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Dacă ați pierdut cheia, vă rugăm să <a %(a_contact)s>ne contactați</a> și să furnizați cât mai multe informații posibil."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Este posibil să trebuiască să creați temporar un nou cont pentru a ne contacta."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Cont vechi bazat pe email? Introduceți <a %(a_open)s>emailul aici</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Listă"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "editează"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Salvează"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Salvat. Vă rugăm să reîncărcați pagina."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să încercați din nou."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Listă de %(by)s, creată <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Lista este goală."

#, fuzzy
msgid "page.list.new_item"
msgstr "Adăugați sau eliminați din această listă găsind un fișier și deschizând fila „Liste”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilul nu a fost găsit."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "editează"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Schimbați numele de afișare. Identificatorul dvs. (partea de după „#”) nu poate fi schimbat."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Salvează"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Salvat. Vă rugăm să reîncărcați pagina."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Ceva a mers prost. Vă rugăm să încercați din nou."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profil creat <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Liste"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Nicio listă încă"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Creați o listă nouă găsind un fișier și deschizând fila „Liste”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reforma drepturilor de autor este necesară pentru securitatea națională"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Pe scurt: LLM-urile chinezești (inclusiv DeepSeek) sunt antrenate pe arhiva mea ilegală de cărți și lucrări — cea mai mare din lume. Occidentul trebuie să revizuiască legea drepturilor de autor ca o chestiune de securitate națională."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articole însoțitoare de TorrentFreak: <a %(torrentfreak)s>primul</a>, <a %(torrentfreak_2)s>al doilea</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Nu cu mult timp în urmă, „bibliotecile-umbră” erau pe cale de dispariție. Sci-Hub, masiva arhivă ilegală de lucrări academice, a încetat să mai primească lucrări noi, din cauza proceselor. „Z-Library”, cea mai mare bibliotecă ilegală de cărți, a văzut cum creatorii săi presupusi au fost arestați pentru acuzații de încălcare a drepturilor de autor. Au reușit incredibil să scape de arest, dar biblioteca lor nu este mai puțin amenințată."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Când Z-Library a fost amenințată cu închiderea, deja am făcut o copie de rezervă a întregii sale biblioteci și căutam o platformă pentru a o găzdui. Aceasta a fost motivația mea pentru a începe Arhiva Annei: o continuare a misiunii din spatele acelor inițiative anterioare. De atunci, am crescut pentru a deveni cea mai mare bibliotecă-umbră din lume, găzduind peste 140 de milioane de texte protejate de drepturi de autor în diverse formate — cărți, lucrări academice, reviste, ziare și altele."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Echipa mea și cu mine suntem ideologi. Credem că păstrarea și găzduirea acestor fișiere este moral corectă. Bibliotecile din întreaga lume se confruntă cu reduceri de finanțare, și nu putem avea încredere în moștenirea umanității nici în corporații."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Apoi a venit AI. Practic toate companiile majore care construiesc LLM-uri ne-au contactat pentru a se antrena pe datele noastre. Majoritatea (dar nu toate!) companiilor din SUA au reconsiderat odată ce au realizat natura ilegală a muncii noastre. În schimb, firmele chineze au îmbrățișat cu entuziasm colecția noastră, aparent nefiind deranjate de legalitatea acesteia. Acest lucru este notabil având în vedere rolul Chinei ca semnatar al aproape tuturor tratatelor internaționale majore privind drepturile de autor."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Am oferit acces de mare viteză la aproximativ 30 de companii. Majoritatea sunt companii LLM, iar unele sunt brokeri de date, care vor revinde colecția noastră. Majoritatea sunt chineze, deși am lucrat și cu companii din SUA, Europa, Rusia, Coreea de Sud și Japonia. DeepSeek a <a %(arxiv)s>recunoscut</a> că o versiune anterioară a fost antrenată pe o parte din colecția noastră, deși sunt discreți cu privire la cel mai recent model al lor (probabil și acesta antrenat pe datele noastre)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Dacă Occidentul dorește să rămână în fruntea cursei LLM-urilor și, în cele din urmă, a AGI, trebuie să-și reconsidere poziția asupra drepturilor de autor, și curând. Indiferent dacă sunteți de acord cu noi sau nu în cazul nostru moral, acesta devine acum un caz de economie și chiar de securitate națională. Toate blocurile de putere construiesc super-oameni de știință artificiali, super-hackeri și super-armate. Libertatea informației devine o chestiune de supraviețuire pentru aceste țări — chiar o chestiune de securitate națională."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Echipa noastră este din întreaga lume și nu avem o aliniere particulară. Dar am încuraja țările cu legi stricte privind drepturile de autor să folosească această amenințare existențială pentru a le reforma. Deci, ce să facem?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Prima noastră recomandare este simplă: scurtarea termenului de copyright. În SUA, drepturile de autor sunt acordate pentru 70 de ani după moartea autorului. Acest lucru este absurd. Putem alinia acest lucru cu brevetele, care sunt acordate pentru 20 de ani după depunere. Acest lucru ar trebui să fie mai mult decât suficient pentru ca autorii de cărți, lucrări, muzică, artă și alte opere creative să fie compensați pe deplin pentru eforturile lor (inclusiv proiecte pe termen lung, cum ar fi adaptările cinematografice)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Apoi, cel puțin, factorii de decizie ar trebui să includă excepții pentru conservarea și diseminarea în masă a textelor. Dacă pierderea veniturilor de la clienții individuali este principala îngrijorare, distribuția la nivel personal ar putea rămâne interzisă. La rândul lor, cei capabili să gestioneze vaste depozite — companii care antrenează LLM-uri, împreună cu biblioteci și alte arhive — ar fi acoperiți de aceste excepții."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Unele țări fac deja o versiune a acestui lucru. TorrentFreak a <a %(torrentfreak)s>raportat</a> că China și Japonia au introdus excepții AI în legile lor privind drepturile de autor. Nu ne este clar cum interacționează acest lucru cu tratatele internaționale, dar cu siguranță oferă acoperire companiilor lor interne, ceea ce explică ceea ce am observat."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "În ceea ce privește Arhiva Annei — vom continua munca noastră subterană bazată pe convingeri morale. Totuși, cea mai mare dorință a noastră este să ieșim la lumină și să ne amplificăm impactul legal. Vă rugăm să reformați drepturile de autor."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Citiți articolele însoțitoare de la TorrentFreak: <a %(torrentfreak)s>primul</a>, <a %(torrentfreak_2)s>al doilea</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Câștigătorii recompensei de 10.000 USD pentru vizualizarea ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Pe scurt: Am primit niște propuneri incredibile pentru recompensa de 10.000 USD pentru vizualizarea ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Cu câteva luni în urmă, am anunțat o <a %(all_isbns)s>recompensă de 10.000 de dolari</a> pentru a realiza cea mai bună vizualizare posibilă a datelor noastre care arată spațiul ISBN. Am subliniat importanța de a arăta care fișiere le-am arhivat sau nu, și ulterior un set de date care descrie câte biblioteci dețin ISBN-uri (o măsură a rarității)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Am fost copleșiți de răspuns. A fost atât de multă creativitate. Un mare mulțumesc tuturor celor care au participat: energia și entuziasmul vostru sunt contagioase!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "În cele din urmă, am vrut să răspundem la următoarele întrebări: <strong>ce cărți există în lume, câte am arhivat deja și pe care ar trebui să ne concentrăm în continuare?</strong> Este minunat să vedem că atât de mulți oameni sunt interesați de aceste întrebări."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Am început cu o vizualizare de bază noi înșine. În mai puțin de 300kb, această imagine reprezintă succint cea mai mare „listă de cărți” complet deschisă adunată vreodată în istoria umanității:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Toate ISBN-urile"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Fișiere în Arhiva Annei"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO-uri"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Scurgere de date CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID-uri"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indexul de eBook-uri EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Cărți"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Arhiva Internetului"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registrul Global al Editorilor ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca de Stat a Rusiei"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperială din Trantor"

#, fuzzy
msgid "common.back"
msgstr "Înapoi"

#, fuzzy
msgid "common.forward"
msgstr "Înainte"

#, fuzzy
msgid "common.last"
msgstr "Ultimul"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Vă rugăm să consultați <a %(all_isbns)s>postarea originală de pe blog</a> pentru mai multe informații."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Am lansat o provocare pentru a îmbunătăți acest lucru. Am acorda o recompensă de 6.000 de dolari pentru locul întâi, 3.000 de dolari pentru locul doi și 1.000 de dolari pentru locul trei. Datorită răspunsului copleșitor și a trimiterilor incredibile, am decis să creștem ușor fondul de premii și să acordăm un loc trei împărțit în patru, de câte 500 de dolari fiecare. Câștigătorii sunt mai jos, dar asigurați-vă că vedeți toate trimiterile <a %(annas_archive)s>aici</a>, sau descărcați <a %(a_2025_01_isbn_visualization_files)s>torrentul nostru combinat</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Locul întâi 6.000 de dolari: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Această <a %(phiresky_github)s>trimitere</a> (<a %(annas_archive_note_2951)s>comentariu Gitlab</a>) este pur și simplu tot ce ne-am dorit și mai mult! Ne-au plăcut în mod special opțiunile de vizualizare incredibil de flexibile (chiar și suportând shadere personalizate), dar cu o listă cuprinzătoare de presetări. De asemenea, ne-a plăcut cât de rapid și lin este totul, implementarea simplă (care nici măcar nu are un backend), minimapa ingenioasă și explicația extinsă în <a %(phiresky_github)s>postarea lor pe blog</a>. O muncă incredibilă și un câștigător bine meritat!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Locul doi 3.000 de dolari: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "O altă <a %(annas_archive_note_2913)s>propunere</a> incredibilă. Nu este la fel de flexibilă ca locul întâi, dar de fapt am preferat vizualizarea la nivel macro față de locul întâi (curbă de umplere a spațiului, granițe, etichetare, evidențiere, panoramare și zoom). Un <a %(annas_archive_note_2971)s>comentariu</a> de Joe Davis a rezonat cu noi:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Deși pătratele și dreptunghiurile perfecte sunt plăcute din punct de vedere matematic, ele nu oferă o localitate superioară într-un context de cartografiere. Cred că asimetria inerentă acestor curbe Hilbert sau Morton clasice nu este un defect, ci o caracteristică. La fel cum conturul faimos în formă de cizmă al Italiei o face instantaneu recunoscută pe o hartă, „ciudățeniile” unice ale acestor curbe pot servi drept repere cognitive. Această distinctivitate poate îmbunătăți memoria spațială și ajuta utilizatorii să se orienteze, făcând potențial mai ușoară localizarea unor regiuni specifice sau observarea unor tipare.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Și totuși, multe opțiuni pentru vizualizare și redare, precum și o interfață de utilizare incredibil de fluidă și intuitivă. Un loc doi solid!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Locul trei $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "În această <a %(annas_archive_note_2940)s>propunere</a> ne-au plăcut foarte mult diferitele tipuri de vizualizări, în special vizualizările de comparație și cele ale editorilor."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Locul trei $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Deși nu are cea mai finisată interfață de utilizare, această <a %(annas_archive_note_2917)s>propunere</a> bifează multe dintre cerințe. Ne-a plăcut în mod special funcția sa de comparație."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Locul trei $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Ca și locul întâi, această <a %(annas_archive_note_2975)s>propunere</a> ne-a impresionat prin flexibilitatea sa. În cele din urmă, aceasta este ceea ce face un instrument de vizualizare grozav: flexibilitate maximă pentru utilizatorii avansați, menținând lucrurile simple pentru utilizatorii obișnuiți."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Locul trei $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Ultima <a %(annas_archive_note_2947)s>propunere</a> care primește o recompensă este destul de simplă, dar are câteva caracteristici unice care ne-au plăcut foarte mult. Ne-a plăcut cum arată câte datasets acoperă un anumit ISBN ca măsură a popularității/fiabilității. De asemenea, ne-a plăcut foarte mult simplitatea, dar eficiența utilizării unui cursor de opacitate pentru comparații."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idei notabile"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Câteva idei și implementări suplimentare care ne-au plăcut în mod special:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Zgârie-nori pentru raritate"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistici în timp real"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Adnotări și, de asemenea, statistici în timp real"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vizualizare unică a hărții și filtre"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schelet de culori implicit și hartă termică cool."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Comutare ușoară a datasets pentru comparații rapide."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etichete frumoase."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Bară de scală cu numărul de cărți."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "O mulțime de glisoare pentru a compara datasets, ca și cum ai fi un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Am putea continua pentru o vreme, dar să ne oprim aici. Asigurați-vă că verificați toate contribuțiile <a %(annas_archive)s>aici</a> sau descărcați <a %(a_2025_01_isbn_visualization_files)s>torrentul nostru combinat</a>. Atât de multe contribuții, și fiecare aduce o perspectivă unică, fie în UI, fie în implementare."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Vom încorpora cel puțin prima propunere câștigătoare în site-ul nostru principal și poate și altele. De asemenea, am început să ne gândim la modul de organizare a procesului de identificare, confirmare și apoi arhivare a celor mai rare cărți. Mai multe informații vor urma în acest sens."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Mulțumim tuturor celor care au participat. Este uimitor că atât de mulți oameni sunt interesați."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Inimile noastre sunt pline de recunoștință."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Vizualizarea tuturor ISBN-urilor — recompensă de 10.000 $ până la 31-01-2025"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Această imagine reprezintă cea mai mare „listă de cărți” complet deschisă, creată vreodată în istoria umanității."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Această imagine are 1000×800 pixeli. Fiecare pixel reprezintă 2.500 de ISBN-uri. Dacă avem un fișier pentru un ISBN, facem pixelul mai verde. Dacă știm că un ISBN a fost emis, dar nu avem un fișier corespunzător, îl facem mai roșu."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "În mai puțin de 300kb, această imagine reprezintă succint cea mai mare „listă de cărți” complet deschisă, creată vreodată în istoria umanității (câteva sute de GB comprimate în totalitate)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "De asemenea, arată: mai este mult de lucru în susținerea cărților (avem doar 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Context"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Cum poate Arhiva Annei să-și îndeplinească misiunea de a susține toată cunoașterea umanității, fără a ști care cărți sunt încă acolo? Avem nevoie de o listă TODO. O modalitate de a cartografia acest lucru este prin numerele ISBN, care din anii 1970 au fost atribuite fiecărei cărți publicate (în majoritatea țărilor)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nu există o autoritate centrală care să cunoască toate atribuțiile ISBN. În schimb, este un sistem distribuit, în care țările primesc intervale de numere, care apoi atribuie intervale mai mici editorilor majori, care ar putea împărți în continuare intervalele editorilor minori. În cele din urmă, numerele individuale sunt atribuite cărților."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Am început să cartografiem ISBN-urile <a %(blog)s>acum doi ani</a> cu extragerea noastră de la ISBNdb. De atunci, am extras multe alte surse de metadata, cum ar fi <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby și altele. O listă completă poate fi găsită pe paginile „Datasets” și „Torrents” de pe Arhiva Annei. Acum avem de departe cea mai mare colecție complet deschisă și ușor de descărcat de metadata despre cărți (și astfel ISBN-uri) din lume."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Am <a %(blog)s>scris pe larg</a> despre de ce ne pasă de conservare și de ce ne aflăm în prezent într-o fereastră critică. Trebuie acum să identificăm cărți rare, neglijate și unice în pericol și să le conservăm. A avea metadata bună despre toate cărțile din lume ajută la asta."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Vizualizare"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Pe lângă imaginea de ansamblu, putem privi și seturile de date individuale pe care le-am achiziționat. Folosiți meniul derulant și butoanele pentru a comuta între ele."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Există multe modele interesante de văzut în aceste imagini. De ce există o anumită regularitate a liniilor și blocurilor, care pare să se întâmple la diferite scări? Care sunt zonele goale? De ce sunt anumite seturi de date atât de aglomerate? Vom lăsa aceste întrebări ca un exercițiu pentru cititor."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensă de 10.000 $"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Există multe de explorat aici, așa că anunțăm o recompensă pentru îmbunătățirea vizualizării de mai sus. Spre deosebire de majoritatea recompenselor noastre, aceasta este limitată în timp. Trebuie să <a %(annas_archive)s>trimiteți</a> codul sursă deschis până la 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Cea mai bună propunere va primi 6.000 $, locul al doilea va primi 3.000 $, iar locul al treilea va primi 1.000 $. Toate recompensele vor fi acordate folosind Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Mai jos sunt criteriile minime. Dacă nicio propunere nu îndeplinește criteriile, este posibil să acordăm totuși unele recompense, dar aceasta va fi la discreția noastră."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork-uiți acest repo și editați acest post de blog HTML (nu sunt permise alte backend-uri în afară de backend-ul nostru Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faceți ca imaginea de mai sus să fie zoomabilă lin, astfel încât să puteți mări până la ISBN-uri individuale. Clicul pe ISBN-uri ar trebui să vă ducă la o pagină de metadata sau la o căutare pe Arhiva Annei."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Trebuie să puteți comuta între toate seturile de date diferite."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Intervalele de țară și intervalele de editori ar trebui să fie evidențiate la trecerea cu mouse-ul. Puteți folosi, de exemplu, <a %(github_xlcnd_isbnlib)s>data4info.py în isbnlib</a> pentru informații despre țară și scrape-ul nostru „isbngrp” pentru editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Trebuie să funcționeze bine pe desktop și mobil."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pentru puncte bonus (acestea sunt doar idei — lăsați-vă creativitatea să zboare):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Se va acorda o atenție deosebită utilizabilității și aspectului estetic."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Afișați metadata reale pentru ISBN-uri individuale atunci când măriți, cum ar fi titlul și autorul."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "O curbă de umplere a spațiului mai bună. De exemplu, un zig-zag, mergând de la 0 la 4 pe primul rând și apoi înapoi (în sens invers) de la 5 la 9 pe al doilea rând — aplicat recursiv."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Scheme de culori diferite sau personalizabile."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vizualizări speciale pentru compararea seturilor de date."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modalități de a depana probleme, cum ar fi alte metadata care nu sunt de acord bine (de exemplu, titluri foarte diferite)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Adnotarea imaginilor cu comentarii despre ISBN-uri sau intervale."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Orice euristici pentru identificarea cărților rare sau în pericol."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Orice idei creative pe care le puteți concepe!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Puteți să vă abateți complet de la criteriile minime și să faceți o vizualizare complet diferită. Dacă este cu adevărat spectaculoasă, atunci se califică pentru recompensă, dar la discreția noastră."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faceți propuneri postând un comentariu la <a %(annas_archive)s>această problemă</a> cu un link către repo-ul dvs. fork-uit, cererea de îmbinare sau diferența."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Cod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Codul pentru a genera aceste imagini, precum și alte exemple, poate fi găsit în <a %(annas_archive)s>acest director</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Am conceput un format de date compact, cu care toate informațiile necesare despre ISBN sunt aproximativ 75MB (comprimat). Descrierea formatului de date și codul pentru a-l genera pot fi găsite <a %(annas_archive_l1244_1319)s>aici</a>. Pentru recompensă nu ești obligat să folosești acest format, dar probabil este cel mai convenabil pentru a începe. Poți transforma metadata noastră cum dorești (deși tot codul tău trebuie să fie open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nu putem aștepta să vedem ce vei crea. Mult noroc!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Containerele Arhivei Annei (AAC): standardizarea lansărilor din cea mai mare bibliotecă de umbră din lume"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Arhiva Annei a devenit cea mai mare bibliotecă de umbră din lume, necesitând standardizarea lansărilor noastre."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Arhiva Annei</a> a devenit de departe cea mai mare bibliotecă de umbră din lume și singura bibliotecă de umbră de această amploare care este complet open-source și open-data. Mai jos este un tabel de pe pagina noastră de Datasets (ușor modificat):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Am realizat acest lucru în trei moduri:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Reflectând biblioteci de umbră open-data existente (cum ar fi Sci-Hub și Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Ajutând biblioteci de umbră care doresc să fie mai deschise, dar nu au avut timp sau resurse pentru a face acest lucru (cum ar fi colecția de benzi desenate Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Extrăgând date din biblioteci care nu doresc să partajeze în masă (cum ar fi Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Pentru (2) și (3) gestionăm acum o colecție considerabilă de torrente noi înșine (sute de TB). Până acum am abordat aceste colecții ca proiecte unice, ceea ce înseamnă infrastructură personalizată și organizare de date pentru fiecare colecție. Acest lucru adaugă o suprasarcină semnificativă fiecărei lansări și face deosebit de dificilă realizarea de lansări mai incrementale."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "De aceea am decis să standardizăm lansările noastre. Acesta este un articol tehnic de blog în care introducem standardul nostru: <strong>Containerele Arhivei Annei</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Obiective de design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Cazul nostru principal de utilizare este distribuția de fișiere și metadata asociate din diferite colecții existente. Cele mai importante considerații ale noastre sunt:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Fișiere și metadata eterogene, cât mai aproape de formatul original posibil."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatori eterogeni în bibliotecile sursă sau chiar lipsa identificatorilor."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Lansări separate de metadata față de datele fișierelor sau lansări doar de metadata (de exemplu, lansarea noastră ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuție prin torrente, deși cu posibilitatea altor metode de distribuție (de exemplu, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Înregistrări imuabile, deoarece ar trebui să presupunem că torrentele noastre vor trăi pentru totdeauna."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Lansări incrementale / lansări adăugabile."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Citibil și scriibil de mașină, convenabil și rapid, în special pentru stiva noastră (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspecție umană relativ ușoară, deși aceasta este secundară față de citibilitatea de către mașină."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Ușor de semănat colecțiile noastre cu un seedbox standard închiriat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Datele binare pot fi servite direct de servere web precum Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Unele non-obiective:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nu ne pasă dacă fișierele sunt ușor de navigat manual pe disc sau căutabile fără preprocesare."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nu ne pasă să fim direct compatibili cu software-ul de bibliotecă existent."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Deși ar trebui să fie ușor pentru oricine să semene colecția noastră folosind torrente, nu ne așteptăm ca fișierele să fie utilizabile fără cunoștințe tehnice semnificative și angajament."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Deoarece Arhiva Annei este open source, dorim să folosim direct formatul nostru. Când reîmprospătăm indexul de căutare, accesăm doar căile disponibile public, astfel încât oricine își poate clona biblioteca noastră și să înceapă rapid."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standardul"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "În cele din urmă, ne-am stabilit pe un standard relativ simplu. Este destul de flexibil, nenormativ și în curs de dezvoltare."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Containerul Arhivei Annei) este un singur element care constă din <strong>metadata</strong> și, opțional, <strong>date binare</strong>, ambele fiind imuabile. Are un identificator unic la nivel global, numit <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Colecție.</strong> Fiecare AAC aparține unei colecții, care prin definiție este o listă de AAC-uri care sunt semantic consistente. Asta înseamnă că, dacă faci o schimbare semnificativă în formatul metadatelor, atunci trebuie să creezi o nouă colecție."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>„înregistrări” și colecții de „fișiere”.</strong> Prin convenție, este adesea convenabil să lansăm „înregistrări” și „fișiere” ca colecții diferite, astfel încât să poată fi lansate la diferite intervale, de exemplu, pe baza ratelor de scraping. O „înregistrare” este o colecție doar de metadata, conținând informații precum titluri de cărți, autori, ISBN-uri etc., în timp ce „fișierele” sunt colecțiile care conțin fișierele propriu-zise (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Formatul AACID este acesta: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. De exemplu, un AACID real pe care l-am lansat este <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: numele colecției, care poate conține litere ASCII, numere și sublinieri (dar fără sublinieri duble)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: o versiune scurtă a ISO 8601, întotdeauna în UTC, de exemplu <code>20220723T194746Z</code>. Acest număr trebuie să crească monoton pentru fiecare lansare, deși semantica sa exactă poate diferi în funcție de colecție. Sugerăm utilizarea timpului de scraping sau de generare a ID-ului."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificator specific colecției, dacă este aplicabil, de exemplu, ID-ul Z-Library. Poate fi omis sau trunchiat. Trebuie omis sau trunchiat dacă AACID ar depăși altfel 150 de caractere."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID dar comprimat la ASCII, de exemplu, folosind base57. În prezent folosim biblioteca Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Interval AACID.</strong> Deoarece AACID-urile conțin timestamp-uri care cresc monoton, putem folosi asta pentru a denota intervale într-o colecție particulară. Folosim acest format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, unde timestamp-urile sunt incluse. Acest lucru este consistent cu notația ISO 8601. Intervalele sunt continue și pot se suprapune, dar în caz de suprapunere trebuie să conțină înregistrări identice cu cele lansate anterior în acea colecție (deoarece AAC-urile sunt imuabile). Înregistrările lipsă nu sunt permise."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Fișier de metadata.</strong> Un fișier de metadata conține metadata unui interval de AAC-uri, pentru o colecție particulară. Acestea au următoarele proprietăți:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Numele fișierului trebuie să fie un interval AACID, prefixat cu <code style=\"color: red\">annas_archive_meta__</code> și urmat de <code>.jsonl.zstd</code>. De exemplu, una dintre lansările noastre se numește<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Așa cum este indicat de extensia fișierului, tipul fișierului este <a %(jsonlines)s>JSON Lines</a> comprimat cu <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Fiecare obiect JSON trebuie să conțină următoarele câmpuri la nivelul de sus: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opțional). Nu sunt permise alte câmpuri."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> este metadata arbitrară, conform semnificației colecției. Trebuie să fie semantic consistentă în cadrul colecției."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> este opțional și reprezintă numele folderului de date binare care conține datele binare corespunzătoare. Numele fișierului de date binare corespunzător din acel folder este AACID-ul înregistrării."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Prefixul <code style=\"color: red\">annas_archive_meta__</code> poate fi adaptat la numele instituției dumneavoastră, de exemplu <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Folder de date binare.</strong> Un folder cu datele binare ale unui interval de AAC-uri, pentru o colecție anume. Acestea au următoarele proprietăți:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Numele directorului trebuie să fie un interval AACID, prefixat cu <code style=\"color: green\">annas_archive_data__</code>, fără sufix. De exemplu, una dintre lansările noastre reale are un director numit<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Directorul trebuie să conțină fișiere de date pentru toate AAC-urile din intervalul specificat. Fiecare fișier de date trebuie să aibă AACID-ul său ca nume de fișier (fără extensii)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Este recomandat să faceți aceste foldere oarecum gestionabile ca dimensiune, de exemplu, nu mai mari de 100GB-1TB fiecare, deși această recomandare poate să se schimbe în timp."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrente.</strong> Fișierele de metadata și folderele de date binare pot fi grupate în torrente, cu un torrent per fișier de metadata sau un torrent per folder de date binare. Torrentele trebuie să aibă numele original al fișierului/directorului plus un sufix <code>.torrent</code> ca nume de fișier."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplu"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Să luăm ca exemplu lansarea noastră recentă Z-Library. Aceasta constă din două colecții: „<span style=\"background: #fffaa3\">zlib3_records</span>” și „<span style=\"background: #ffd6fe\">zlib3_files</span>”. Acest lucru ne permite să extragem și să lansăm separat înregistrările de metadate de fișierele reale ale cărților. Astfel, am lansat două torrente cu fișiere de metadate:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "De asemenea, am lansat o serie de torrente cu foldere de date binare, dar doar pentru colecția „<span style=\"background: #ffd6fe\">zlib3_files</span>”, în total 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Rulând <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> putem vedea ce este în interior:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "În acest caz, este vorba despre metadatele unei cărți raportate de Z-Library. La nivel superior avem doar „aacid” și „metadata”, dar nu și „data_folder”, deoarece nu există date binare corespunzătoare. AACID conține „22430000” ca ID principal, care vedem că este preluat din „zlibrary_id”. Ne putem aștepta ca alte AAC-uri din această colecție să aibă aceeași structură."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Acum să rulăm <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Aceasta este o metadata AAC mult mai mică, deși cea mai mare parte a acestui AAC se află în altă parte într-un fișier binar! La urma urmei, avem un „data_folder” de această dată, așa că ne putem aștepta ca datele binare corespunzătoare să fie localizate la <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata” conține „zlibrary_id”, așa că putem asocia ușor cu AAC-ul corespunzător din colecția „zlib_records”. Am fi putut asocia în mai multe moduri diferite, de exemplu prin AACID — standardul nu prescrie asta."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Rețineți că nu este necesar ca câmpul „metadata” să fie el însuși JSON. Ar putea fi un șir care conține XML sau orice alt format de date. Ați putea chiar să stocați informații de metadata în blobul binar asociat, de exemplu, dacă este o cantitate mare de date."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Concluzie"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Cu acest standard, putem face lansări mai incremental și putem adăuga mai ușor surse noi de date. Avem deja câteva lansări interesante în pregătire!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "De asemenea, sperăm să devină mai ușor pentru alte biblioteci de umbră să oglindească colecțiile noastre. La urma urmei, scopul nostru este să păstrăm cunoștințele și cultura umană pentru totdeauna, așa că cu cât mai multă redundanță, cu atât mai bine."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Actualizarea Annei: arhivă complet open source, ElasticSearch, peste 300GB de coperți de cărți"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Am lucrat non-stop pentru a oferi o alternativă bună cu Arhiva Annei. Iată câteva dintre lucrurile pe care le-am realizat recent."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Odată cu închiderea Z-Library și arestarea (presupusă) a fondatorilor săi, am lucrat neîncetat pentru a oferi o alternativă bună cu Arhiva Annei (nu vom pune un link aici, dar puteți căuta pe Google). Iată câteva dintre realizările noastre recente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Arhiva Annei este complet open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Credem că informația ar trebui să fie gratuită, iar codul nostru nu face excepție. Am publicat tot codul nostru pe instanța noastră Gitlab găzduită privat: <a %(annas_archive)s>Software-ul Annei</a>. De asemenea, folosim tracker-ul de probleme pentru a ne organiza munca. Dacă doriți să vă implicați în dezvoltarea noastră, acesta este un loc excelent pentru a începe."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Pentru a vă oferi o idee despre lucrurile la care lucrăm, luați în considerare munca noastră recentă privind îmbunătățirile de performanță pe partea clientului. Deoarece nu am implementat încă paginarea, adesea returnam pagini de căutare foarte lungi, cu 100-200 de rezultate. Nu am vrut să întrerupem rezultatele căutării prea devreme, dar acest lucru însemna că ar încetini unele dispozitive. Pentru aceasta, am implementat un mic truc: am înfășurat majoritatea rezultatelor căutării în comentarii HTML (<code><!-- --></code>), și apoi am scris un mic Javascript care ar detecta când un rezultat ar trebui să devină vizibil, moment în care am desfășura comentariul:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "\"Virtualizarea\" DOM implementată în 23 de linii, fără a fi nevoie de biblioteci sofisticate! Acesta este genul de cod pragmatic rapid pe care îl obții atunci când ai timp limitat și probleme reale care trebuie rezolvate. S-a raportat că acum căutarea noastră funcționează bine pe dispozitive lente!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un alt efort major a fost automatizarea construirii bazei de date. Când am lansat, am adunat diferite surse la întâmplare. Acum dorim să le menținem actualizate, așa că am scris o serie de scripturi pentru a descărca noi metadata de la cele două fork-uri Library Genesis și a le integra. Scopul nu este doar să facem acest lucru util pentru arhiva noastră, ci să facem lucrurile ușoare pentru oricine dorește să se joace cu metadata bibliotecilor de umbră. Scopul ar fi un notebook Jupyter care să aibă tot felul de metadata interesante disponibile, astfel încât să putem face mai multe cercetări, cum ar fi să aflăm ce <a %(blog)s>procent de ISBN-uri sunt păstrate pentru totdeauna</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "În cele din urmă, am reînnoit sistemul nostru de donații. Acum puteți folosi un card de credit pentru a depune direct bani în portofelele noastre cripto, fără a fi nevoie să știți prea multe despre criptomonede. Vom continua să monitorizăm cât de bine funcționează acest lucru în practică, dar este un pas important."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Trecerea la ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Unul dintre <a %(annas_archive)s>biletele</a> noastre era un amestec de probleme cu sistemul nostru de căutare. Am folosit căutarea full-text MySQL, deoarece aveam toate datele noastre în MySQL oricum. Dar avea limitele sale:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Unele interogări durau extrem de mult, până la punctul în care acaparau toate conexiunile deschise."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "În mod implicit, MySQL are o lungime minimă a cuvântului, sau indexul poate deveni foarte mare. Oamenii raportau că nu puteau căuta „Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Căutarea era doar oarecum rapidă când era complet încărcată în memorie, ceea ce ne-a cerut să obținem o mașină mai scumpă pentru a rula acest lucru, plus câteva comenzi pentru a preîncărca indexul la pornire."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nu am fi putut să-l extindem ușor pentru a construi noi funcționalități, cum ar fi o mai bună <a %(wikipedia_cjk_characters)s>tokenizare pentru limbile fără spații</a>, filtrare/faceting, sortare, sugestii „ați vrut să spuneți”, completare automată și așa mai departe."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "După ce am discutat cu o serie de experți, ne-am decis asupra ElasticSearch. Nu a fost perfect (sugestiile lor implicite „ați vrut să spuneți” și funcțiile de completare automată sunt slabe), dar per total a fost mult mai bun decât MySQL pentru căutare. Încă nu suntem <a %(youtube)s>prea entuziasmați</a> să-l folosim pentru date critice (deși au făcut multe <a %(elastic_co)s>progrese</a>), dar per total suntem destul de mulțumiți de schimbare."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Deocamdată, am implementat o căutare mult mai rapidă, suport mai bun pentru limbi, sortare mai relevantă, opțiuni diferite de sortare și filtrare pe baza limbii/tipului de carte/tipului de fișier. Dacă sunteți curios cum funcționează, <a %(annas_archive_l140)s>aruncați</a> <a %(annas_archive_l1115)s>o</a> <a %(annas_archive_l1635)s>privire</a>. Este destul de accesibil, deși ar putea folosi mai multe comentarii…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Peste 300GB de coperți de cărți lansate"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "În cele din urmă, suntem bucuroși să anunțăm o mică lansare. În colaborare cu cei care operează fork-ul Libgen.rs, împărtășim toate coperțile lor de cărți prin torrente și IPFS. Acest lucru va distribui încărcătura vizualizării coperților între mai multe mașini și le va conserva mai bine. În multe (dar nu toate) cazuri, coperțile cărților sunt incluse în fișierele în sine, așa că acestea sunt un fel de „date derivate”. Dar a le avea în IPFS este încă foarte util pentru funcționarea zilnică a atât a Arhivei Annei, cât și a diferitelor fork-uri Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Ca de obicei, puteți găsi această lansare la Pirate Library Mirror (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>). Nu vom pune un link aici, dar îl puteți găsi cu ușurință."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Sperăm că ne putem relaxa puțin ritmul, acum că avem o alternativă decentă la Z-Library. Această sarcină de lucru nu este deosebit de sustenabilă. Dacă sunteți interesat să ajutați cu programarea, operațiunile serverului sau lucrările de conservare, nu ezitați să ne contactați. Mai este încă mult <a %(annas_archive)s>de lucru de făcut</a>. Vă mulțumim pentru interesul și sprijinul acordat."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Arhiva Annei a salvat cea mai mare bibliotecă de benzi desenate din umbră din lume (95TB) — puteți ajuta la seed-ul acesteia"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Cea mai mare bibliotecă de benzi desenate din umbră din lume avea un singur punct de eșec... până astăzi."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discută pe Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Cea mai mare bibliotecă de umbră de benzi desenate este probabil cea a unui anumit fork Library Genesis: Libgen.li. Administratorul unic care gestionează acel site a reușit să colecteze o colecție incredibilă de benzi desenate de peste 2 milioane de fișiere, totalizând peste 95TB. Totuși, spre deosebire de alte colecții Library Genesis, aceasta nu era disponibilă în masă prin torrente. Puteai accesa aceste benzi desenate doar individual prin serverul său personal lent — un singur punct de eșec. Până astăzi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "În această postare vă vom spune mai multe despre această colecție și despre strângerea noastră de fonduri pentru a sprijini mai mult această muncă."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon încearcă să se piardă în lumea banală a bibliotecii…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fork-uri Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Mai întâi, un pic de context. Probabil cunoașteți Library Genesis pentru colecția lor epică de cărți. Mai puțini oameni știu că voluntarii Library Genesis au creat alte proiecte, cum ar fi o colecție considerabilă de reviste și documente standard, un backup complet al Sci-Hub (în colaborare cu fondatoarea Sci-Hub, Alexandra Elbakyan), și, într-adevăr, o colecție masivă de benzi desenate."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "La un moment dat, diferiți operatori ai oglinzilor Library Genesis și-au urmat propriile căi, ceea ce a dus la situația actuală de a avea un număr de „fork-uri” diferite, toate purtând încă numele Library Genesis. Fork-ul Libgen.li are în mod unic această colecție de benzi desenate, precum și o colecție considerabilă de reviste (la care lucrăm și noi)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaborare"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Având în vedere dimensiunea sa, această colecție a fost de mult timp pe lista noastră de dorințe, așa că după succesul nostru cu backup-ul Z-Library, ne-am concentrat pe această colecție. La început am extras-o direct, ceea ce a fost o adevărată provocare, deoarece serverul lor nu era în cea mai bună condiție. Am obținut aproximativ 15TB în acest fel, dar a fost un proces lent."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Din fericire, am reușit să luăm legătura cu operatorul bibliotecii, care a fost de acord să ne trimită toate datele direct, ceea ce a fost mult mai rapid. Totuși, a durat mai mult de jumătate de an să transferăm și să procesăm toate datele, și aproape că le-am pierdut pe toate din cauza unei coruperi a discului, ceea ce ar fi însemnat să începem de la zero."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Această experiență ne-a făcut să credem că este important să facem aceste date disponibile cât mai repede posibil, astfel încât să poată fi oglindite pe scară largă. Suntem la doar unul sau două incidente nefericite de a pierde această colecție pentru totdeauna!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Colecția"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Mișcarea rapidă înseamnă că colecția este puțin neorganizată… Să aruncăm o privire. Imaginați-vă că avem un sistem de fișiere (pe care, în realitate, îl împărțim între torrente):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Primul director, <code>/repository</code>, este partea mai structurată a acestuia. Acest director conține așa-numitele „mii de directoare”: directoare fiecare cu o mie de fișiere, care sunt numerotate incremental în baza de date. Directorul <code>0</code> conține fișiere cu comic_id 0–999, și așa mai departe."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Acesta este același sistem pe care Library Genesis l-a folosit pentru colecțiile sale de ficțiune și non-ficțiune. Ideea este că fiecare „mie de directoare” este transformat automat într-un torrent de îndată ce este completat."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Cu toate acestea, operatorul Libgen.li nu a creat niciodată torrente pentru această colecție, așa că miile de directoare probabil au devenit incomode și au dat loc „directoarelor nesortate”. Acestea sunt <code>/comics0</code> până la <code>/comics4</code>. Toate conțin structuri de directoare unice, care probabil aveau sens pentru colectarea fișierelor, dar nu prea au sens pentru noi acum. Din fericire, metadata se referă direct la toate aceste fișiere, așa că organizarea lor pe disc nu contează de fapt!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata este disponibilă sub forma unei baze de date MySQL. Aceasta poate fi descărcată direct de pe site-ul Libgen.li, dar o vom face disponibilă și într-un torrent, alături de propriul nostru tabel cu toate hash-urile MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analiză"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Când primești 95TB descărcați în clusterul tău de stocare, încerci să înțelegi ce se află acolo… Am făcut o analiză pentru a vedea dacă putem reduce puțin dimensiunea, de exemplu prin eliminarea duplicatelor. Iată câteva dintre constatările noastre:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplicatele semantice (scanări diferite ale aceleiași cărți) pot fi teoretic filtrate, dar este complicat. Când am verificat manual benzile desenate, am găsit prea multe alarme false."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Există unele duplicate doar prin MD5, ceea ce este relativ risipitor, dar eliminarea acestora ne-ar oferi doar aproximativ 1% in economii. La această scară, asta înseamnă totuși aproximativ 1TB, dar, de asemenea, la această scară 1TB nu contează cu adevărat. Preferăm să nu riscăm să distrugem accidental date în acest proces."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Am găsit o mulțime de date non-cărți, cum ar fi filme bazate pe benzi desenate. Acest lucru pare, de asemenea, risipitor, deoarece acestea sunt deja disponibile pe scară largă prin alte mijloace. Totuși, ne-am dat seama că nu puteam pur și simplu să filtrăm fișierele de filme, deoarece există și <em>benzi desenate interactive</em> care au fost lansate pe computer, pe care cineva le-a înregistrat și salvat ca filme."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "În cele din urmă, orice am putea șterge din colecție ar economisi doar câteva procente. Apoi ne-am amintit că suntem colecționari de date, iar cei care vor oglindi acest lucru sunt, de asemenea, colecționari de date, așa că, „CE VREȚI SĂ SPUNEȚI, ȘTERGE?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Prin urmare, vă prezentăm colecția completă, nemodificată. Este o mulțime de date, dar sperăm că suficient de mulți oameni vor dori să o seed-eze oricum."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Strângere de fonduri"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Lansăm aceste date în câteva bucăți mari. Primul torrent este de <code>/comics0</code>, pe care l-am pus într-un fișier .tar uriaș de 12TB. Este mai bine pentru hard disk-ul și software-ul de torrent decât o mulțime de fișiere mai mici."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Ca parte a acestei lansări, organizăm o strângere de fonduri. Căutăm să strângem 20.000 de dolari pentru a acoperi costurile operaționale și de contractare pentru această colecție, precum și pentru a permite proiecte viitoare și în desfășurare. Avem câteva proiecte <em>masive</em> în lucru."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Pe cine sprijin cu donația mea?</em> Pe scurt: salvăm toate cunoștințele și cultura umanității și le facem ușor accesibile. Tot codul și datele noastre sunt open source, suntem un proiect condus complet de voluntari și am salvat până acum 125TB de cărți (în plus față de torrentele existente ale Libgen și Scihub). În cele din urmă, construim un mecanism care permite și încurajează oamenii să găsească, să scaneze și să salveze toate cărțile din lume. Vom scrie despre planul nostru principal într-o postare viitoare. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Dacă donați pentru un abonament de 12 luni „Amazing Archivist” (780 USD), puteți <strong>„adopta un torrent”</strong>, ceea ce înseamnă că vom pune numele de utilizator sau mesajul dvs. în numele unui dintre torrente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Puteți dona accesând <a %(wikipedia_annas_archive)s>Arhiva Annei</a> și făcând clic pe butonul „Donează”. De asemenea, căutăm mai mulți voluntari: ingineri software, cercetători în securitate, experți în comerț anonim și traducători. Ne puteți sprijini și oferind servicii de găzduire. Și, bineînțeles, vă rugăm să seed-uiți torrentele noastre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Mulțumim tuturor celor care ne-au sprijinit deja atât de generos! Faceți cu adevărat o diferență."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Iată torrentele lansate până acum (încă procesăm restul):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Toate torrentele pot fi găsite pe <a %(wikipedia_annas_archive)s>Arhiva Annei</a> sub „Datasets” (nu punem link direct acolo, pentru ca linkurile către acest blog să nu fie eliminate de pe Reddit, Twitter etc.). De acolo, urmați linkul către site-ul Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Ce urmează?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "O mulțime de torrente sunt excelente pentru conservarea pe termen lung, dar nu atât de mult pentru accesul zilnic. Vom colabora cu parteneri de găzduire pentru a pune toate aceste date pe web (deoarece Arhiva Annei nu găzduiește nimic direct). Desigur, veți putea găsi aceste linkuri de descărcare pe Arhiva Annei."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "De asemenea, invităm pe toată lumea să facă lucruri cu aceste date! Ajutați-ne să le analizăm mai bine, să le deduplicăm, să le punem pe IPFS, să le remixăm, să vă antrenați modelele AI cu ele și așa mai departe. Sunt ale voastre și abia așteptăm să vedem ce veți face cu ele."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "În cele din urmă, așa cum am spus înainte, avem încă câteva lansări masive care urmează (dacă <em>cineva</em> ar putea <em>accidental</em> să ne trimită un dump al unei baze de date <em>anumite</em> ACS4, știți unde să ne găsiți...), precum și construirea unui mecanism pentru a face backup la toate cărțile din lume."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Așadar, rămâneți pe fază, abia am început."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x cărți noi adăugate la Oglinda Bibliotecii Piraților (+24TB, 3,8 milioane de cărți)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "În lansarea originală a Oglinzii Bibliotecii Piraților (EDIT: mutată la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>), am realizat o oglindă a Z-Library, o mare colecție ilegală de cărți. Ca reamintire, iată ce am scris în acea postare originală pe blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library este o bibliotecă populară (și ilegală). Au preluat colecția Library Genesis și au făcut-o ușor de căutat. Pe lângă asta, au devenit foarte eficienți în a solicita contribuții noi de cărți, stimulând utilizatorii care contribuie cu diverse beneficii. În prezent, nu contribuie cu aceste cărți noi înapoi la Library Genesis. Și, spre deosebire de Library Genesis, nu fac colecția lor ușor de oglindit, ceea ce împiedică conservarea largă. Acest lucru este important pentru modelul lor de afaceri, deoarece percep bani pentru accesul la colecția lor în vrac (mai mult de 10 cărți pe zi)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nu facem judecăți morale despre perceperea de bani pentru accesul în vrac la o colecție ilegală de cărți. Este fără îndoială că Z-Library a avut succes în extinderea accesului la cunoștințe și în obținerea mai multor cărți. Suntem aici doar pentru a ne face partea: asigurarea conservării pe termen lung a acestei colecții private."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Acea colecție datează de la mijlocul anului 2021. Între timp, Z-Library a crescut într-un ritm uluitor: au adăugat aproximativ 3,8 milioane de cărți noi. Sigur, există unele duplicate acolo, dar majoritatea par a fi cărți cu adevărat noi sau scanări de calitate superioară ale cărților trimise anterior. Acest lucru se datorează în mare parte numărului crescut de moderatori voluntari de la Z-Library și sistemului lor de încărcare în masă cu deduplicare. Dorim să îi felicităm pentru aceste realizări."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Suntem bucuroși să anunțăm că am obținut toate cărțile care au fost adăugate la Z-Library între ultima noastră oglindă și august 2022. De asemenea, am revenit și am extras unele cărți pe care le-am ratat prima dată. În total, această nouă colecție are aproximativ 24TB, ceea ce este mult mai mare decât cea anterioară (7TB). Oglinda noastră are acum un total de 31TB. Din nou, am deduplicat față de Library Genesis, deoarece există deja torrente disponibile pentru acea colecție."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Vă rugăm să accesați Pirate Library Mirror pentru a verifica noua colecție (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>). Acolo există mai multe informații despre cum sunt structurate fișierele și ce s-a schimbat de la ultima dată. Nu vom face legătura de aici, deoarece acesta este doar un site de blog care nu găzduiește materiale ilegale."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Desigur, seeding-ul este, de asemenea, o modalitate excelentă de a ne ajuta. Mulțumim tuturor celor care seed-uiesc setul nostru anterior de torrente. Suntem recunoscători pentru răspunsul pozitiv și fericiți că există atât de mulți oameni care se preocupă de păstrarea cunoașterii și culturii în acest mod neobișnuit."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Cum să devii un arhivist pirat"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Prima provocare ar putea fi una surprinzătoare. Nu este o problemă tehnică sau legală. Este o problemă psihologică."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Înainte de a începe, două actualizări despre Pirate Library Mirror (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Am primit niște donații extrem de generoase. Prima a fost de 10.000 de dolari de la o persoană anonimă care a sprijinit și \"bookwarrior\", fondatorul original al Library Genesis. Mulțumiri speciale lui bookwarrior pentru facilitarea acestei donații. A doua a fost o altă donație de 10.000 de dolari de la un donator anonim, care a luat legătura după ultima noastră lansare și a fost inspirat să ajute. Am avut, de asemenea, un număr de donații mai mici. Mulțumim foarte mult pentru tot sprijinul vostru generos. Avem câteva proiecte noi și interesante în pregătire pe care acestea le vor susține, așa că rămâneți pe fază."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Am avut unele dificultăți tehnice cu dimensiunea celei de-a doua lansări, dar torrentele noastre sunt acum sus și seed-uiesc. De asemenea, am primit o ofertă generoasă de la o persoană anonimă de a seed-ui colecția noastră pe serverele lor de mare viteză, așa că facem un upload special pe mașinile lor, după care toți ceilalți care descarcă colecția ar trebui să vadă o îmbunătățire semnificativă a vitezei."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Se pot scrie cărți întregi despre <em>de ce</em> al păstrării digitale în general și al arhivismului pirat în special, dar să oferim un scurt ghid pentru cei care nu sunt prea familiarizați. Lumea produce mai multă cunoaștere și cultură ca niciodată, dar și mai mult din aceasta se pierde ca niciodată. Omenirea încredințează în mare parte corporațiilor precum editorii academici, serviciile de streaming și companiile de social media acest patrimoniu, iar acestea nu s-au dovedit adesea a fi mari păstrători. Verificați documentarul Digital Amnesia sau orice discurs al lui Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Există unele instituții care fac o treabă bună arhivând cât de mult pot, dar sunt limitate de lege. Ca pirați, suntem într-o poziție unică de a arhiva colecții pe care ei nu le pot atinge, din cauza aplicării drepturilor de autor sau a altor restricții. De asemenea, putem oglindi colecții de mai multe ori, în întreaga lume, crescând astfel șansele unei păstrări corespunzătoare."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Deocamdată, nu vom intra în discuții despre avantajele și dezavantajele proprietății intelectuale, moralitatea încălcării legii, reflecții asupra cenzurii sau problema accesului la cunoaștere și cultură. Cu toate acestea lămurite, să ne adâncim în <em>cum</em>. Vom împărtăși cum echipa noastră a devenit arhiviști pirați și lecțiile pe care le-am învățat pe parcurs. Există multe provocări atunci când pornești în această călătorie și sperăm să te putem ajuta cu unele dintre ele."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunitate"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Prima provocare ar putea fi una surprinzătoare. Nu este o problemă tehnică sau legală. Este o problemă psihologică: a face această muncă în umbră poate fi incredibil de singuratic. În funcție de ceea ce plănuiești să faci și de modelul tău de amenințare, s-ar putea să trebuiască să fii foarte atent. La un capăt al spectrului avem oameni precum Alexandra Elbakyan*, fondatoarea Sci-Hub, care este foarte deschisă cu privire la activitățile sale. Dar ea este la un risc ridicat de a fi arestată dacă ar vizita o țară occidentală în acest moment și ar putea face față unor decenii de închisoare. Este acesta un risc pe care ai fi dispus să ți-l asumi? Noi suntem la celălalt capăt al spectrului; fiind foarte atenți să nu lăsăm nicio urmă și având o securitate operațională puternică."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Așa cum a menționat pe HN de \"ynno\", Alexandra inițial nu a vrut să fie cunoscută: \"Serverele ei erau configurate să emită mesaje de eroare detaliate din PHP, inclusiv calea completă a fișierului sursă cu eroare, care era sub directorul /home/<USER>" Așadar, folosiți nume de utilizator aleatorii pe computerele pe care le folosiți pentru aceste lucruri, în cazul în care configurați greșit ceva."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Această secretomanie, totuși, vine cu un cost psihologic. Majoritatea oamenilor iubesc să fie recunoscuți pentru munca pe care o fac, și totuși nu poți lua niciun credit pentru asta în viața reală. Chiar și lucrurile simple pot fi provocatoare, cum ar fi prietenii care te întreabă ce ai mai făcut (la un moment dat \"mă joc cu NAS-ul / homelab-ul meu\" devine plictisitor)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "De aceea este atât de important să găsești o comunitate. Poți renunța la o parte din securitatea operațională confidențându-te unor prieteni foarte apropiați, despre care știi că poți avea încredere profundă. Chiar și atunci, fii atent să nu pui nimic în scris, în cazul în care trebuie să predea e-mailurile autorităților sau dacă dispozitivele lor sunt compromise în alt mod."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Mai bine este să găsești niște pirați colegi. Dacă prietenii tăi apropiați sunt interesați să ți se alăture, grozav! În caz contrar, s-ar putea să găsești alții online. Din păcate, aceasta este încă o comunitate de nișă. Până acum am găsit doar câțiva alții care sunt activi în acest domeniu. Locuri bune de început par a fi forumurile Library Genesis și r/DataHoarder. Echipa Archive are, de asemenea, indivizi cu gânduri similare, deși operează în cadrul legii (chiar dacă în unele zone gri ale legii). Scenele tradiționale de \"warez\" și piraterie au, de asemenea, oameni care gândesc în moduri similare."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Suntem deschiși la idei despre cum să încurajăm comunitatea și să explorăm idei. Simțiți-vă liberi să ne trimiteți mesaje pe Twitter sau Reddit. Poate am putea găzdui un fel de forum sau grup de chat. O provocare este că acest lucru poate fi ușor cenzurat atunci când folosim platforme comune, așa că ar trebui să-l găzduim noi înșine. Există, de asemenea, un compromis între a avea aceste discuții complet publice (mai mult potențial de implicare) versus a le face private (să nu lăsăm potențialele \"ținte\" să știe că suntem pe cale să le extragem). Va trebui să ne gândim la asta. Anunțați-ne dacă sunteți interesați de acest lucru!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proiecte"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Când facem un proiect, acesta are câteva etape:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selecția domeniului / filozofie: Unde vrei să te concentrezi aproximativ și de ce? Care sunt pasiunile, abilitățile și circumstanțele tale unice pe care le poți folosi în avantajul tău?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selecția țintei: Care colecție specifică o vei oglindi?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Extracția de metadata: Catalogarea informațiilor despre fișiere, fără a descărca efectiv fișierele (adesea mult mai mari) în sine."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selecția datelor: Pe baza metadata, restrângerea datelor care sunt cele mai relevante pentru arhivare acum. Ar putea fi totul, dar adesea există o modalitate rezonabilă de a economisi spațiu și lățime de bandă."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Extracția de date: Obținerea efectivă a datelor."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuție: Ambalarea în torrente, anunțarea undeva, obținerea de oameni care să le răspândească."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Acestea nu sunt faze complet independente și adesea perspectivele dintr-o fază ulterioară te trimit înapoi la o fază anterioară. De exemplu, în timpul extragerii de metadata, s-ar putea să realizezi că ținta pe care ai selectat-o are mecanisme de apărare dincolo de nivelul tău de competență (cum ar fi blocările IP), așa că te întorci și găsești o altă țintă."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selecția domeniului / filozofie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nu există lipsă de cunoaștere și patrimoniu cultural de salvat, ceea ce poate fi copleșitor. De aceea, este adesea util să iei un moment și să te gândești la ce contribuție poți aduce."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Fiecare persoană are o modalitate diferită de a gândi despre acest subiect, dar iată câteva întrebări pe care le-ați putea pune:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "De ce sunteți interesat de acest lucru? Ce vă pasionează? Dacă putem aduna un grup de oameni care arhivează tipurile de lucruri de care le pasă în mod specific, asta ar acoperi mult! Veți ști mult mai multe decât persoana obișnuită despre pasiunea dumneavoastră, cum ar fi ce date sunt importante de salvat, care sunt cele mai bune colecții și comunități online și așa mai departe."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Ce abilități aveți pe care le puteți folosi în avantajul dumneavoastră? De exemplu, dacă sunteți expert în securitate online, puteți găsi modalități de a învinge blocajele IP pentru ținte sigure. Dacă sunteți excelent la organizarea comunităților, atunci poate puteți aduna câțiva oameni în jurul unui scop. Este util să cunoașteți puțin programare, chiar dacă doar pentru a menține o bună securitate operațională pe parcursul acestui proces."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Cât timp aveți pentru acest lucru? Sfatul nostru ar fi să începeți cu lucruri mici și să treceți la proiecte mai mari pe măsură ce vă obișnuiți, dar poate deveni copleșitor."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Care ar fi o zonă cu efect de pârghie ridicat pe care să vă concentrați? Dacă veți petrece X ore pe arhivarea piratată, atunci cum puteți obține cel mai mare \"bang for your buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Care sunt modalitățile unice în care gândiți despre acest subiect? S-ar putea să aveți idei sau abordări interesante pe care alții le-ar fi ratat."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "În cazul nostru, ne-am preocupat în mod special de păstrarea pe termen lung a științei. Știam despre Library Genesis și cum a fost complet oglindit de multe ori folosind torrente. Ne-a plăcut acea idee. Apoi, într-o zi, unul dintre noi a încercat să găsească niște manuale științifice pe Library Genesis, dar nu le-a putut găsi, punând sub semnul întrebării cât de complet era cu adevărat. Am căutat apoi acele manuale online și le-am găsit în alte locuri, ceea ce a plantat sămânța pentru proiectul nostru. Chiar înainte de a ști despre Z-Library, aveam ideea de a nu încerca să colectăm toate acele cărți manual, ci să ne concentrăm pe oglindirea colecțiilor existente și să le contribuim înapoi la Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selecția țintei"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Deci, avem zona noastră pe care o analizăm, acum ce colecție specifică oglindim? Există câteva lucruri care fac o țintă bună:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Mare"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unic: nu este deja bine acoperit de alte proiecte."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accesibil: nu folosește multe straturi de protecție pentru a preveni extragerea metadata și a datelor lor."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Informație specială: ai niște informații speciale despre această țintă, cum ar fi că ai acces special la această colecție sau ai descoperit cum să le învingi apărarea. Acest lucru nu este necesar (proiectul nostru viitor nu face nimic special), dar cu siguranță ajută!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Când am găsit manualele noastre de știință pe site-uri web altele decât Library Genesis, am încercat să ne dăm seama cum au ajuns pe internet. Apoi am găsit Z-Library și am realizat că, deși majoritatea cărților nu își fac prima apariție acolo, ajung în cele din urmă acolo. Am aflat despre relația sa cu Library Genesis și structura de stimulente (financiare) și interfața superioară a utilizatorului, ambele făcându-l o colecție mult mai completă. Apoi am făcut câteva extrageri preliminare de metadata și date și am realizat că putem ocoli limitele de descărcare IP, valorificând accesul special al unuia dintre membrii noștri la multe servere proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Pe măsură ce explorați diferite ținte, este deja important să vă ascundeți urmele folosind VPN-uri și adrese de email de unică folosință, despre care vom vorbi mai târziu."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Extracția de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Să devenim puțin mai tehnici aici. Pentru a extrage efectiv metadata de pe site-uri web, am păstrat lucrurile destul de simple. Folosim scripturi Python, uneori curl, și o bază de date MySQL pentru a stoca rezultatele. Nu am folosit niciun software de extragere sofisticat care poate cartografia site-uri web complexe, deoarece până acum am avut nevoie doar să extragem unul sau două tipuri de pagini enumerând doar prin id-uri și analizând HTML-ul. Dacă nu există pagini ușor de enumerat, atunci s-ar putea să aveți nevoie de un crawler adecvat care încearcă să găsească toate paginile."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Înainte de a începe să extrageți un întreg site web, încercați să faceți acest lucru manual pentru o vreme. Parcurgeți câteva zeci de pagini singur, pentru a vă face o idee despre cum funcționează. Uneori veți întâlni deja blocaje IP sau alte comportamente interesante în acest fel. Același lucru este valabil și pentru extragerea de date: înainte de a intra prea adânc în această țintă, asigurați-vă că puteți descărca efectiv datele sale."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Pentru a ocoli restricțiile, există câteva lucruri pe care le puteți încerca. Există alte adrese IP sau servere care găzduiesc aceleași date, dar nu au aceleași restricții? Există puncte finale API care nu au restricții, în timp ce altele au? La ce rată de descărcare IP-ul dumneavoastră este blocat și pentru cât timp? Sau nu sunteți blocat, ci doar încetinit? Ce se întâmplă dacă creați un cont de utilizator, cum se schimbă lucrurile atunci? Puteți folosi HTTP/2 pentru a menține conexiunile deschise și asta crește rata la care puteți solicita pagini? Există pagini care listează mai multe fișiere deodată și informațiile listate acolo sunt suficiente?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Lucruri pe care probabil doriți să le salvați includ:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titlu"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nume fișier / locație"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: poate fi un ID intern, dar ID-uri precum ISBN sau DOI sunt utile de asemenea."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dimensiune: pentru a calcula cât spațiu pe disc aveți nevoie."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): pentru a confirma că ați descărcat fișierul corect."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data adăugată/modificată: astfel încât să puteți reveni mai târziu și să descărcați fișierele pe care nu le-ați descărcat anterior (deși puteți folosi adesea și ID-ul sau hash-ul pentru aceasta)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descriere, categorie, etichete, autori, limbă etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "De obicei, facem acest lucru în două etape. Mai întâi descărcăm fișierele HTML brute, de obicei direct în MySQL (pentru a evita multe fișiere mici, despre care vorbim mai jos). Apoi, într-un pas separat, parcurgem acele fișiere HTML și le analizăm în tabele MySQL reale. În acest fel, nu trebuie să re-descărcați totul de la zero dacă descoperiți o greșeală în codul de analiză, deoarece puteți re-procesa fișierele HTML cu noul cod. De asemenea, este adesea mai ușor să paralelizați pasul de procesare, economisind astfel timp (și puteți scrie codul de procesare în timp ce scraping-ul rulează, în loc să scrieți ambele etape simultan)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "În cele din urmă, rețineți că pentru unele ținte, scraping-ul de metadata este tot ce există. Există unele colecții uriașe de metadata care nu sunt păstrate corespunzător."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selecția datelor"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Adesea puteți folosi metadata pentru a determina un subset rezonabil de date de descărcat. Chiar dacă în cele din urmă doriți să descărcați toate datele, poate fi util să prioritizați elementele cele mai importante mai întâi, în cazul în care sunteți detectat și apărarea este îmbunătățită, sau pentru că ar trebui să cumpărați mai multe discuri, sau pur și simplu pentru că altceva apare în viața dvs. înainte de a putea descărca totul."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "De exemplu, o colecție ar putea avea mai multe ediții ale aceleiași resurse de bază (cum ar fi o carte sau un film), unde una este marcată ca fiind de cea mai bună calitate. Salvarea acelor ediții mai întâi ar avea mult sens. S-ar putea să doriți în cele din urmă să salvați toate edițiile, deoarece în unele cazuri metadata ar putea fi etichetată incorect, sau ar putea exista compromisuri necunoscute între ediții (de exemplu, \"cea mai bună ediție\" ar putea fi cea mai bună în majoritatea modurilor, dar mai slabă în altele, cum ar fi un film având o rezoluție mai mare, dar lipsind subtitrările)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "De asemenea, puteți căuta în baza de date de metadata pentru a găsi lucruri interesante. Care este cel mai mare fișier găzduit și de ce este atât de mare? Care este cel mai mic fișier? Există modele interesante sau neașteptate când vine vorba de anumite categorii, limbi și așa mai departe? Există titluri duplicate sau foarte similare? Există modele privind momentul în care datele au fost adăugate, cum ar fi o zi în care multe fișiere au fost adăugate deodată? Puteți învăța adesea multe uitându-vă la setul de date în moduri diferite."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "În cazul nostru, am deduplicat cărțile Z-Library împotriva hash-urilor md5 din Library Genesis, economisind astfel mult timp de descărcare și spațiu pe disc. Aceasta este o situație destul de unică totuși. În majoritatea cazurilor, nu există baze de date cuprinzătoare despre care fișiere sunt deja păstrate corespunzător de către alți pirați. Aceasta în sine este o oportunitate uriașă pentru cineva de acolo. Ar fi grozav să avem o privire de ansamblu actualizată regulat despre lucruri precum muzica și filmele care sunt deja semănate pe scară largă pe site-urile de torrent, și sunt, prin urmare, o prioritate mai mică de inclus în oglinzile piraților."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scraping-ul datelor"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Acum sunteți gata să descărcați efectiv datele în masă. După cum am menționat anterior, în acest moment ar trebui să fi descărcat deja manual o grămadă de fișiere, pentru a înțelege mai bine comportamentul și restricțiile țintei. Totuși, vor fi încă surprize pentru dvs. odată ce veți începe să descărcați multe fișiere deodată."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Sfatul nostru aici este în principal să păstrați lucrurile simple. Începeți prin a descărca doar o grămadă de fișiere. Puteți folosi Python și apoi să extindeți la mai multe fire. Dar uneori chiar mai simplu este să generați fișiere Bash direct din baza de date și apoi să rulați mai multe dintre ele în mai multe ferestre de terminal pentru a scala. Un truc tehnic rapid care merită menționat aici este utilizarea OUTFILE în MySQL, pe care îl puteți scrie oriunde dacă dezactivați \"secure_file_priv\" în mysqld.cnf (și asigurați-vă că dezactivați/suprascrieți și AppArmor dacă sunteți pe Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Stocăm datele pe discuri dure simple. Începeți cu ceea ce aveți și extindeți încet. Poate fi copleșitor să vă gândiți la stocarea a sute de TB de date. Dacă aceasta este situația cu care vă confruntați, puneți mai întâi un subset bun și în anunțul dvs. cereți ajutor pentru stocarea restului. Dacă doriți să obțineți mai multe hard disk-uri, atunci r/DataHoarder are câteva resurse bune pentru a obține oferte bune."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Încercați să nu vă faceți prea multe griji cu privire la sistemele de fișiere sofisticate. Este ușor să cădeți în capcana de a configura lucruri precum ZFS. Un detaliu tehnic de care trebuie să fiți conștienți, totuși, este că multe sisteme de fișiere nu se descurcă bine cu multe fișiere. Am descoperit că o soluție simplă este să creați mai multe directoare, de exemplu pentru diferite intervale de ID-uri sau prefixe de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "După descărcarea datelor, asigurați-vă că verificați integritatea fișierelor folosind hash-urile din metadata, dacă sunt disponibile."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuția"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Aveți datele, oferindu-vă astfel posesia primei oglinzi pirat din lume a țintei dvs. (cel mai probabil). În multe feluri, partea cea mai grea s-a terminat, dar partea cea mai riscantă este încă înaintea dvs. Până acum, ați fost discret; zburând sub radar. Tot ce trebuia să faceți era să folosiți un VPN bun pe tot parcursul, să nu completați detaliile personale în niciun formular (evident), și poate să folosiți o sesiune specială de browser (sau chiar un alt computer)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Acum trebuie să distribuiți datele. În cazul nostru, am vrut mai întâi să contribuim cu cărțile înapoi la Library Genesis, dar apoi am descoperit rapid dificultățile în acest sens (sortarea ficțiune vs non-ficțiune). Așa că am decis să distribuim folosind torrente în stil Library Genesis. Dacă aveți oportunitatea de a contribui la un proiect existent, atunci asta v-ar putea economisi mult timp. Totuși, nu sunt multe oglinzi pirat bine organizate în prezent."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Așadar, să spunem că decideți să distribuiți torrentele singur. Încercați să păstrați acele fișiere mici, astfel încât să fie ușor de oglindit pe alte site-uri web. Va trebui apoi să semănați torrentele singur, rămânând totuși anonim. Puteți folosi un VPN (cu sau fără redirecționare de porturi) sau să plătiți cu Bitcoins amestecați pentru un Seedbox. Dacă nu știți ce înseamnă unii dintre acești termeni, veți avea de citit mult, deoarece este important să înțelegeți compromisurile de risc aici."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Puteți găzdui fișierele torrent pe site-uri de torrent existente. În cazul nostru, am ales să găzduim de fapt un site web, deoarece am vrut să ne răspândim filosofia într-un mod clar. Puteți face acest lucru singur într-un mod similar (noi folosim Njalla pentru domeniile și găzduirea noastră, plătite cu Bitcoins amestecați), dar de asemenea, nu ezitați să ne contactați pentru a vă găzdui torrentele. Căutăm să construim un index cuprinzător al oglinzilor pirat în timp, dacă această idee prinde."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "În ceea ce privește selecția VPN, s-a scris deja mult despre acest subiect, așa că vom repeta doar sfatul general de a alege după reputație. Politicile reale de non-log testate în instanță, cu un istoric lung de protejare a intimității, sunt opțiunea cu cel mai mic risc, în opinia noastră. Rețineți că, chiar și atunci când faceți totul corect, nu puteți ajunge niciodată la un risc zero. De exemplu, atunci când semănați torrentele, un actor de stat național foarte motivat poate probabil să analizeze fluxurile de date de intrare și ieșire pentru serverele VPN și să deducă cine sunteți. Sau pur și simplu puteți greși cumva. Probabil că deja am făcut-o și o vom face din nou. Din fericire, statele naționale nu le pasă <em>atât de mult</em> de piraterie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "O decizie de luat pentru fiecare proiect este dacă să-l publicați folosind aceeași identitate ca înainte sau nu. Dacă continuați să folosiți același nume, atunci greșelile în securitatea operațională din proiectele anterioare ar putea să vă afecteze. Dar publicarea sub nume diferite înseamnă că nu construiți o reputație de durată mai lungă. Am ales să avem o securitate operațională puternică de la început, astfel încât să putem continua să folosim aceeași identitate, dar nu vom ezita să publicăm sub un nume diferit dacă greșim sau dacă circumstanțele o cer."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "A face cunoscut proiectul poate fi dificil. După cum am spus, aceasta este încă o comunitate de nișă. Inițial am postat pe Reddit, dar am avut cu adevărat succes pe Hacker News. Deocamdată, recomandarea noastră este să postați în câteva locuri și să vedeți ce se întâmplă. Și din nou, contactați-ne. Ne-ar plăcea să răspândim vestea despre mai multe eforturi de arhivare pirat."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Concluzie"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Sperăm că acest lucru este util pentru arhiviștii pirați care abia încep. Suntem încântați să vă primim în această lume, așa că nu ezitați să ne contactați. Să păstrăm cât mai mult din cunoștințele și cultura lumii și să le oglindim pe scară largă."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introducerea Oglinzii Bibliotecii Piraților: Conservarea a 7TB de cărți (care nu sunt în Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Acest proiect (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>) își propune să contribuie la conservarea și eliberarea cunoștințelor umane. Facem o mică și umilă contribuție, pe urmele marilor înaintea noastră."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Focalizarea acestui proiect este ilustrată de numele său:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirat</strong> - Încălcăm deliberat legea drepturilor de autor în majoritatea țărilor. Acest lucru ne permite să facem ceva ce entitățile legale nu pot face: să ne asigurăm că cărțile sunt oglindite pe scară largă."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotecă</strong> - Ca majoritatea bibliotecilor, ne concentrăm în principal pe materiale scrise, cum ar fi cărțile. S-ar putea să ne extindem în viitor și la alte tipuri de media."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Oglindă</strong> - Suntem strict o oglindă a bibliotecilor existente. Ne concentrăm pe conservare, nu pe a face cărțile ușor de căutat și descărcat (acces) sau pe a încuraja o mare comunitate de oameni care contribuie cu cărți noi (sursă)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Prima bibliotecă pe care am oglindit-o este Z-Library. Aceasta este o bibliotecă populară (și ilegală). Au preluat colecția Library Genesis și au făcut-o ușor de căutat. Pe lângă asta, au devenit foarte eficienți în a solicita noi contribuții de cărți, prin stimularea utilizatorilor care contribuie cu diverse beneficii. În prezent, nu contribuie cu aceste noi cărți înapoi la Library Genesis. Și spre deosebire de Library Genesis, nu fac colecția lor ușor de oglindit, ceea ce împiedică o conservare largă. Acest lucru este important pentru modelul lor de afaceri, deoarece percep bani pentru accesarea colecției lor în vrac (mai mult de 10 cărți pe zi)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nu facem judecăți morale despre perceperea de bani pentru accesul în vrac la o colecție ilegală de cărți. Este fără îndoială că Z-Library a avut succes în extinderea accesului la cunoștințe și în obținerea mai multor cărți. Suntem aici doar pentru a ne face partea: asigurarea conservării pe termen lung a acestei colecții private."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Am dori să vă invităm să ajutați la conservarea și eliberarea cunoașterii umane descărcând și semănând torrentele noastre. Consultați pagina proiectului pentru mai multe informații despre cum sunt organizate datele."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "De asemenea, vă invităm foarte mult să contribuiți cu ideile voastre despre ce colecții să oglindim în continuare și cum să procedăm. Împreună putem realiza multe. Aceasta este doar o mică contribuție printre nenumărate altele. Vă mulțumim pentru tot ceea ce faceți."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nu legăm fișierele de pe acest blog. Vă rugăm să le găsiți singur.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump ISBNdb, sau Câte Cărți Sunt Conservate pentru Totdeauna?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Dacă am deduplica corect fișierele din bibliotecile de umbră, ce procent din toate cărțile din lume am conservat?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Cu Pirate Library Mirror (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>), scopul nostru este să luăm toate cărțile din lume și să le conservăm pentru totdeauna.<sup>1</sup> Între torrentele noastre Z-Library și torrentele originale Library Genesis, avem 11.783.153 de fișiere. Dar câte sunt acestea, de fapt? Dacă am deduplica corect acele fișiere, ce procent din toate cărțile din lume am conservat? Ne-ar plăcea cu adevărat să avem ceva de genul acesta:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of din patrimoniul scris al umanității conservat pentru totdeauna"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Pentru un procentaj, avem nevoie de un numitor: numărul total de cărți publicate vreodată.<sup>2</sup> Înainte de dispariția Google Books, un inginer al proiectului, Leonid Taycher, <a %(booksearch_blogspot)s>a încercat să estimeze</a> acest număr. A ajuns — în glumă — la 129.864.880 („cel puțin până duminică”). A estimat acest număr construind o bază de date unificată a tuturor cărților din lume. Pentru aceasta, a adunat diferite seturi de date și apoi le-a combinat în diverse moduri."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Ca o scurtă paranteză, există o altă persoană care a încercat să catalogheze toate cărțile din lume: Aaron Swartz, regretatul activist digital și co-fondator Reddit.<sup>3</sup> El <a %(youtube)s>a început Open Library</a> cu scopul de a avea „o pagină web pentru fiecare carte publicată vreodată”, combinând date din multe surse diferite. A ajuns să plătească prețul suprem pentru munca sa de conservare digitală când a fost urmărit penal pentru descărcarea în masă a lucrărilor academice, ceea ce a dus la sinuciderea sa. Este de la sine înțeles că acesta este unul dintre motivele pentru care grupul nostru este pseudonim și de ce suntem foarte atenți. Open Library este încă condus eroic de oamenii de la Internet Archive, continuând moștenirea lui Aaron. Vom reveni la acest subiect mai târziu în această postare."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "În postarea de pe blogul Google, Taycher descrie unele dintre provocările cu estimarea acestui număr. În primul rând, ce constituie o carte? Există câteva definiții posibile:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copii fizice.</strong> Evident, acest lucru nu este foarte util, deoarece sunt doar duplicate ale aceluiași material. Ar fi grozav dacă am putea conserva toate adnotările pe care oamenii le fac în cărți, cum ar fi faimoasele „mâzgălituri pe margini” ale lui Fermat. Dar, din păcate, aceasta va rămâne un vis al arhiviștilor."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Lucrări”.</strong> De exemplu, „Harry Potter și Camera Secretelor” ca un concept logic, care cuprinde toate versiunile sale, cum ar fi diferite traduceri și reeditări. Aceasta este o definiție destul de utilă, dar poate fi dificil să tragi linia a ceea ce contează. De exemplu, probabil dorim să păstrăm diferite traduceri, deși reeditările cu doar diferențe minore ar putea să nu fie la fel de importante."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Ediții”.</strong> Aici numărați fiecare versiune unică a unei cărți. Dacă ceva despre ea este diferit, cum ar fi o copertă diferită sau un prefață diferită, se consideră o ediție diferită."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Fișiere.</strong> Când lucrați cu biblioteci de umbră precum Library Genesis, Sci-Hub sau Z-Library, există o considerație suplimentară. Pot exista mai multe scanări ale aceleiași ediții. Și oamenii pot crea versiuni mai bune ale fișierelor existente, prin scanarea textului folosind OCR sau corectarea paginilor care au fost scanate la un unghi. Dorim să numărăm aceste fișiere ca o singură ediție, ceea ce ar necesita metadate bune sau deduplicare folosind măsuri de similaritate a documentelor."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Edițiile” par a fi cea mai practică definiție a ceea ce sunt „cărțile”. Convenabil, această definiție este folosită și pentru atribuirea numerelor ISBN unice. Un ISBN, sau Număr Standard Internațional de Carte, este utilizat frecvent în comerțul internațional, deoarece este integrat cu sistemul internațional de coduri de bare („Număr Internațional de Articol”). Dacă doriți să vindeți o carte în magazine, aceasta are nevoie de un cod de bare, așa că obțineți un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Postarea pe blog a lui Taycher menționează că, deși ISBN-urile sunt utile, nu sunt universale, deoarece au fost adoptate cu adevărat doar la mijlocul anilor '70 și nu peste tot în lume. Totuși, ISBN-ul este probabil cel mai utilizat identificator al edițiilor de cărți, așa că este cel mai bun punct de plecare al nostru. Dacă putem găsi toate ISBN-urile din lume, obținem o listă utilă a cărților care mai trebuie conservate."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Așadar, de unde obținem datele? Există o serie de eforturi existente care încearcă să compileze o listă a tuturor cărților din lume:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> La urma urmei, au făcut această cercetare pentru Google Books. Cu toate acestea, metadata lor nu este accesibilă în vrac și este destul de greu de extras."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Așa cum am menționat anterior, aceasta este întreaga lor misiune. Au obținut cantități masive de date de bibliotecă de la biblioteci cooperante și arhive naționale și continuă să facă acest lucru. De asemenea, au bibliotecari voluntari și o echipă tehnică care încearcă să deduplicateze înregistrările și să le eticheteze cu tot felul de metadata. Cel mai bun lucru este că dataset-ul lor este complet deschis. Puteți pur și simplu <a %(openlibrary)s>să-l descărcați</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Acesta este un site administrat de organizația non-profit OCLC, care vinde sisteme de management al bibliotecilor. Ei agregă metadata despre cărți de la multe biblioteci și le fac disponibile prin intermediul site-ului WorldCat. Totuși, ei câștigă bani și din vânzarea acestor date, așa că nu sunt disponibile pentru descărcare în masă. Au totuși unele seturi de date în masă mai limitate disponibile pentru descărcare, în cooperare cu biblioteci specifice."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Acesta este subiectul acestei postări de blog. ISBNdb extrage date de pe diverse site-uri pentru metadata despre cărți, în special date despre prețuri, pe care le vând apoi librarilor, astfel încât aceștia să își poată stabili prețurile în conformitate cu restul pieței. Deoarece ISBN-urile sunt destul de universale în zilele noastre, au construit efectiv o „pagină web pentru fiecare carte”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Diverse sisteme individuale de biblioteci și arhive.</strong> Există biblioteci și arhive care nu au fost indexate și agregate de niciuna dintre cele de mai sus, adesea pentru că sunt subfinanțate sau din alte motive nu doresc să își împărtășească datele cu Open Library, OCLC, Google și așa mai departe. Multe dintre acestea au înregistrări digitale accesibile prin internet și adesea nu sunt foarte bine protejate, așa că dacă doriți să ajutați și să vă distrați învățând despre sisteme de biblioteci ciudate, acestea sunt puncte de plecare excelente."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "În această postare, suntem bucuroși să anunțăm o mică lansare (comparativ cu lansările noastre anterioare Z-Library). Am extras majoritatea datelor din ISBNdb și le-am făcut disponibile pentru descărcare prin torrent pe site-ul Pirate Library Mirror (EDIT: mutat la <a %(wikipedia_annas_archive)s>Arhiva Annei</a>; nu vom pune un link direct aici, căutați-l). Acestea sunt aproximativ 30,9 milioane de înregistrări (20GB ca <a %(jsonlines)s>JSON Lines</a>; 4,4GB comprimat). Pe site-ul lor, ei susțin că au de fapt 32,6 milioane de înregistrări, așa că s-ar putea să fi ratat cumva unele, sau <em>ei</em> ar putea face ceva greșit. În orice caz, deocamdată nu vom împărtăși exact cum am făcut-o — vom lăsa asta ca un exercițiu pentru cititor. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Ceea ce vom împărtăși este o analiză preliminară, pentru a încerca să ne apropiem de estimarea numărului de cărți din lume. Ne-am uitat la trei seturi de date: acest nou set de date ISBNdb, lansarea noastră originală de metadate pe care le-am extras din biblioteca de umbră Z-Library (care include Library Genesis) și dump-ul de date Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Să începem cu câteva cifre aproximative:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "În ambele Z-Library/Libgen și Open Library există mult mai multe cărți decât ISBN-uri unice. Înseamnă asta că multe dintre acele cărți nu au ISBN-uri sau pur și simplu lipsesc metadatele ISBN? Probabil putem răspunde la această întrebare cu o combinație de potrivire automată bazată pe alte atribute (titlu, autor, editor etc.), aducând mai multe surse de date și extragând ISBN-uri din scanările reale ale cărților (în cazul Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Câte dintre acele ISBN-uri sunt unice? Acest lucru este cel mai bine ilustrat cu un diagramă Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Pentru a fi mai precis:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Am fost surprinși de cât de puțin se suprapun! ISBNdb are o cantitate uriașă de ISBN-uri care nu apar nici în Z-Library, nici în Open Library, și același lucru este valabil (într-o măsură mai mică, dar totuși substanțială) pentru celelalte două. Acest lucru ridică multe întrebări noi. Cât de mult ar ajuta potrivirea automată în etichetarea cărților care nu au fost etichetate cu ISBN-uri? Ar exista multe potriviri și, prin urmare, o suprapunere crescută? De asemenea, ce s-ar întâmpla dacă am aduce un al patrulea sau al cincilea set de date? Cât de multă suprapunere am vedea atunci?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Acest lucru ne oferă un punct de plecare. Acum putem privi toate ISBN-urile care nu erau în setul de date Z-Library și care nu se potrivesc nici cu câmpurile titlu/autor. Acest lucru ne poate oferi un punct de sprijin pentru a păstra toate cărțile din lume: mai întâi prin extragerea de pe internet a scanărilor, apoi prin ieșirea în viața reală pentru a scana cărți. Acesta din urmă ar putea fi chiar finanțat de mulțime sau condus de „recompense” de la persoane care ar dori să vadă anumite cărți digitalizate. Toate acestea sunt o poveste pentru altă dată."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Dacă doriți să ajutați cu oricare dintre acestea — analize suplimentare; extragerea mai multor metadate; găsirea mai multor cărți; OCR-ul cărților; realizarea acestora pentru alte domenii (de exemplu, lucrări, cărți audio, filme, emisiuni TV, reviste) sau chiar punerea la dispoziție a unor date pentru lucruri precum ML / instruirea modelelor de limbaj mari — vă rog să mă contactați (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Dacă sunteți interesat în mod special de analiza datelor, lucrăm la punerea la dispoziție a seturilor noastre de date și a scripturilor într-un format mai ușor de utilizat. Ar fi grozav dacă ați putea doar să copiați un notebook și să începeți să vă jucați cu acesta."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "În cele din urmă, dacă doriți să sprijiniți această activitate, vă rugăm să luați în considerare să faceți o donație. Aceasta este o operațiune condusă în întregime de voluntari, iar contribuția dumneavoastră face o diferență uriașă. Fiecare contribuție contează. Deocamdată acceptăm donații în criptomonede; vedeți pagina Donează pe Arhiva Annei."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pentru o definiție rezonabilă a „pentru totdeauna”. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Desigur, patrimoniul scris al umanității este mult mai mult decât cărți, mai ales în zilele noastre. Pentru scopul acestei postări și al lansărilor noastre recente, ne concentrăm pe cărți, dar interesele noastre se extind mai departe."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Se pot spune multe despre Aaron Swartz, dar am vrut doar să-l menționăm pe scurt, deoarece joacă un rol esențial în această poveste. Pe măsură ce trece timpul, mai mulți oameni ar putea întâlni numele său pentru prima dată și ulterior să se aventureze singuri în această poveste complexă."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Fereastra critică a bibliotecilor de umbră"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Cum putem pretinde că ne păstrăm colecțiile pentru totdeauna, când acestea se apropie deja de 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versiunea chineză 中文版</a>, discutați pe <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "La Arhiva Annei, suntem adesea întrebați cum putem pretinde că ne păstrăm colecțiile pentru totdeauna, când dimensiunea totală se apropie deja de 1 Petabyte (1000 TB) și continuă să crească. În acest articol vom analiza filosofia noastră și vom vedea de ce următorul deceniu este critic pentru misiunea noastră de a păstra cunoștințele și cultura umanității."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Dimensiunea totală</a> a colecțiilor noastre, în ultimele luni, defalcată după numărul de seeders de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorități"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "De ce ne pasă atât de mult de lucrări și cărți? Să lăsăm deoparte credința noastră fundamentală în conservare în general — s-ar putea să scriem o altă postare despre asta. Deci, de ce lucrări și cărți în mod specific? Răspunsul este simplu: <strong>densitatea informației</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte de stocare, textul scris stochează cea mai mare cantitate de informație dintre toate mediile. Deși ne pasă atât de cunoaștere, cât și de cultură, ne pasă mai mult de prima. În general, găsim o ierarhie a densității informației și a importanței conservării care arată aproximativ astfel:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Lucrări academice, jurnale, rapoarte"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Date organice precum secvențe ADN, semințe de plante sau mostre microbiene"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Cărți de non-ficțiune"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Cod software pentru știință și inginerie"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Date de măsurare precum măsurători științifice, date economice, rapoarte corporative"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Site-uri web de știință și inginerie, discuții online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Reviste de non-ficțiune, ziare, manuale"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcrieri de non-ficțiune ale discuțiilor, documentarelor, podcasturilor"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Date interne de la corporații sau guverne (scurgeri)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Înregistrări de metadata în general (de non-ficțiune și ficțiune; ale altor medii, artă, persoane etc.; inclusiv recenzii)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Date geografice (de exemplu, hărți, studii geologice)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcrieri ale procedurilor legale sau de instanță"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versiuni ficționale sau de divertisment ale tuturor celor de mai sus"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Clasamentul din această listă este oarecum arbitrar — mai multe elemente sunt la egalitate sau există dezacorduri în cadrul echipei noastre — și probabil uităm unele categorii importante. Dar aceasta este aproximativ cum prioritizăm."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Unele dintre aceste elemente sunt prea diferite de celelalte pentru a ne face griji (sau sunt deja gestionate de alte instituții), cum ar fi datele organice sau datele geografice. Dar majoritatea elementelor din această listă sunt de fapt importante pentru noi."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un alt factor important în prioritizarea noastră este cât de mult este în pericol o anumită lucrare. Preferăm să ne concentrăm pe lucrări care sunt:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rare"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unic neglijate"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unic în pericol de distrugere (de exemplu, din cauza războiului, reducerilor de finanțare, proceselor sau persecuției politice)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "În cele din urmă, ne pasă de scară. Avem timp și bani limitați, așa că am prefera să petrecem o lună salvând 10.000 de cărți decât 1.000 de cărți — dacă sunt aproximativ la fel de valoroase și în pericol."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteci de umbră"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Există multe organizații care au misiuni similare și priorități similare. Într-adevăr, există biblioteci, arhive, laboratoare, muzee și alte instituții însărcinate cu conservarea de acest tip. Multe dintre acestea sunt bine finanțate, de guverne, indivizi sau corporații. Dar au un punct orb masiv: sistemul legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Aici se află rolul unic al bibliotecilor de umbră și motivul pentru care Arhiva Annei există. Putem face lucruri pe care alte instituții nu au voie să le facă. Acum, nu este (adesea) că putem arhiva materiale care sunt ilegale de păstrat în altă parte. Nu, este legal în multe locuri să construiești o arhivă cu orice cărți, lucrări, reviste și așa mai departe."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Dar ceea ce arhivele legale adesea nu au este <strong>redundanța și longevitatea</strong>. Există cărți din care există doar o copie într-o bibliotecă fizică undeva. Există înregistrări de metadata păzite de o singură corporație. Există ziare păstrate doar pe microfilm într-o singură arhivă. Bibliotecile pot suferi reduceri de finanțare, corporațiile pot da faliment, arhivele pot fi bombardate și arse până la temelii. Acest lucru nu este ipotetic — se întâmplă tot timpul."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Lucrul unic pe care îl putem face la Arhiva Annei este să stocăm multe copii ale lucrărilor, la scară largă. Putem colecta lucrări, cărți, reviste și altele, și le putem distribui în masă. În prezent, facem acest lucru prin torrente, dar tehnologiile exacte nu contează și se vor schimba în timp. Partea importantă este distribuirea multor copii în întreaga lume. Acest citat de acum peste 200 de ani este încă valabil:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ceea ce s-a pierdut nu poate fi recuperat; dar să salvăm ceea ce rămâne: nu prin seifuri și lacăte care le îndepărtează de ochii și utilizarea publicului, condamnându-le la risipa timpului, ci printr-o astfel de multiplicare a copiilor, încât să le plasăm dincolo de atingerea accidentului.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "O notă rapidă despre domeniul public. Deoarece Arhiva Annei se concentrează în mod unic pe activități care sunt ilegale în multe locuri din lume, nu ne deranjăm cu colecțiile larg disponibile, cum ar fi cărțile din domeniul public. Entitățile legale au grijă adesea de acestea. Totuși, există considerații care ne fac uneori să lucrăm la colecții disponibile public:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Înregistrările de metadata pot fi vizualizate gratuit pe site-ul Worldcat, dar nu pot fi descărcate în masă (până când le-am <a %(worldcat_scrape)s>extras</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Codul poate fi open source pe Github, dar Github în ansamblu nu poate fi ușor oglindit și astfel păstrat (deși în acest caz particular există copii suficient de distribuite ale majorității depozitelor de cod)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit este gratuit de utilizat, dar recent a impus măsuri stricte anti-extragere, în urma antrenamentului LLM înfometat de date (mai multe despre asta mai târziu)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "O multiplicare a copiilor"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Revenind la întrebarea noastră inițială: cum putem pretinde că ne păstrăm colecțiile pentru totdeauna? Problema principală aici este că colecția noastră a <a %(torrents_stats)s>crescut</a> rapid, prin extragerea și open-sourcing-ul unor colecții masive (pe lângă munca uimitoare deja făcută de alte biblioteci de umbră cu date deschise, cum ar fi Sci-Hub și Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Această creștere a datelor face mai dificilă oglindirea colecțiilor în întreaga lume. Stocarea datelor este costisitoare! Dar suntem optimiști, mai ales când observăm următoarele trei tendințe."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Am cules fructele la îndemână"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Acest lucru urmează direct din prioritățile noastre discutate mai sus. Preferăm să lucrăm la eliberarea colecțiilor mari mai întâi. Acum că am securizat unele dintre cele mai mari colecții din lume, ne așteptăm ca creșterea noastră să fie mult mai lentă."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Există încă un lung șir de colecții mai mici, iar cărți noi sunt scanate sau publicate în fiecare zi, dar ritmul va fi probabil mult mai lent. S-ar putea să ne dublăm sau chiar să ne triplăm dimensiunea, dar pe o perioadă mai lungă de timp."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Costurile de stocare continuă să scadă exponențial"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "La momentul scrierii, <a %(diskprices)s>prețurile discurilor</a> pe TB sunt în jur de 12 dolari pentru discuri noi, 8 dolari pentru discuri folosite și 4 dolari pentru bandă. Dacă suntem conservatori și ne uităm doar la discuri noi, asta înseamnă că stocarea unui petabyte costă aproximativ 12.000 de dolari. Dacă presupunem că biblioteca noastră se va tripla de la 900TB la 2,7PB, asta ar însemna 32.400 de dolari pentru a oglindi întreaga noastră bibliotecă. Adăugând electricitatea, costul altor echipamente hardware și așa mai departe, să rotunjim la 40.000 de dolari. Sau cu bandă mai mult ca 15.000–20.000 de dolari."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Pe de o parte, <strong>15.000–40.000 USD pentru suma tuturor cunoștințelor umane este o afacere bună</strong>. Pe de altă parte, este puțin cam mult să ne așteptăm la tone de copii complete, mai ales dacă ne dorim ca acei oameni să continue să își partajeze torrentele în beneficiul altora."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Asta este astăzi. Dar progresul merge înainte:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Costurile hard disk-urilor per TB au fost reduse aproximativ la o treime în ultimii 10 ani și probabil vor continua să scadă într-un ritm similar. Banda magnetică pare să urmeze o traiectorie similară. Prețurile SSD-urilor scad și mai rapid și ar putea depăși prețurile HDD-urilor până la sfârșitul deceniului."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendințele prețurilor HDD din diferite surse (faceți clic pentru a vizualiza studiul)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Dacă acest lucru se menține, atunci în 10 ani am putea ajunge să cheltuim doar 5.000–13.000 USD pentru a oglindi întreaga noastră colecție (1/3), sau chiar mai puțin dacă creștem mai puțin în dimensiune. Deși încă o sumă mare de bani, aceasta va fi accesibilă pentru mulți oameni. Și ar putea fi chiar mai bine datorită următorului punct…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Îmbunătățiri în densitatea informației"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "În prezent, stocăm cărțile în formatele brute în care ne sunt furnizate. Sigur, ele sunt comprimate, dar adesea sunt încă scanări mari sau fotografii ale paginilor."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Până acum, singurele opțiuni pentru a micșora dimensiunea totală a colecției noastre au fost printr-o compresie mai agresivă sau deduplicare. Cu toate acestea, pentru a obține economii semnificative, ambele sunt prea pierderi pentru gustul nostru. Compresia puternică a fotografiilor poate face textul abia lizibil. Iar deduplicarea necesită o încredere ridicată că cărțile sunt exact aceleași, ceea ce este adesea prea inexact, mai ales dacă conținutul este același, dar scanările sunt realizate în ocazii diferite."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "A existat întotdeauna o a treia opțiune, dar calitatea sa a fost atât de abisală încât nu am luat-o niciodată în considerare: <strong>OCR, sau Recunoașterea Optică a Caracterelor</strong>. Acesta este procesul de conversie a fotografiilor în text simplu, folosind AI pentru a detecta caracterele din fotografii. Instrumentele pentru aceasta au existat de mult timp și au fost destul de decente, dar „destul de decent” nu este suficient pentru scopuri de conservare."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Cu toate acestea, modelele recente de învățare profundă multi-modale au făcut progrese extrem de rapide, deși încă la costuri ridicate. Ne așteptăm ca atât acuratețea, cât și costurile să se îmbunătățească dramatic în anii următori, până la punctul în care va deveni realist să le aplicăm întregii noastre biblioteci."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Îmbunătățiri OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Când se va întâmpla acest lucru, probabil vom păstra în continuare fișierele originale, dar în plus am putea avea o versiune mult mai mică a bibliotecii noastre pe care majoritatea oamenilor vor dori să o oglindească. Partea interesantă este că textul brut în sine se comprimă și mai bine și este mult mai ușor de deduplicat, oferindu-ne și mai multe economii."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "În general, nu este nerealist să ne așteptăm la o reducere de cel puțin 5-10 ori a dimensiunii totale a fișierelor, poate chiar mai mult. Chiar și cu o reducere conservatoare de 5 ori, ne-am uita la <strong>1.000–3.000 USD în 10 ani, chiar dacă biblioteca noastră se triplează în dimensiune</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Fereastră critică"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Dacă aceste previziuni sunt corecte, <strong>trebuie doar să așteptăm câțiva ani</strong> până când întreaga noastră colecție va fi oglindită pe scară largă. Astfel, în cuvintele lui Thomas Jefferson, „plasată dincolo de atingerea accidentului”."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Din păcate, apariția LLM-urilor și antrenamentul lor avar de date a pus mulți deținători de drepturi de autor în defensivă. Chiar mai mult decât erau deja. Multe site-uri web fac mai dificilă extragerea și arhivarea, procesele sunt în desfășurare, iar în tot acest timp bibliotecile și arhivele fizice continuă să fie neglijate."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Ne putem aștepta doar ca aceste tendințe să continue să se înrăutățească și multe lucrări să fie pierdute cu mult înainte de a intra în domeniul public."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Suntem în ajunul unei revoluții în conservare, dar <q>ceea ce este pierdut nu poate fi recuperat.</q></strong> Avem o fereastră critică de aproximativ 5-10 ani în care este încă destul de scump să operăm o bibliotecă de umbră și să creăm multe oglinzi în întreaga lume, și în care accesul nu a fost complet închis încă."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Dacă putem traversa această fereastră, atunci vom fi păstrat cunoștințele și cultura umanității pentru totdeauna. Nu ar trebui să lăsăm acest timp să se irosească. Nu ar trebui să lăsăm această fereastră critică să se închidă asupra noastră."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Să începem."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Acces exclusiv pentru companiile LLM la cea mai mare colecție de cărți de non-ficțiune chinezească din lume"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versiunea chineză 中文版</a>, <a %(news_ycombinator)s>Discută pe Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Pe scurt:</strong> Arhiva Annei a achiziționat o colecție unică de 7,5 milioane / 350TB de cărți de non-ficțiune chinezească — mai mare decât Library Genesis. Suntem dispuși să oferim unei companii LLM acces exclusiv, în schimbul unui OCR de înaltă calitate și extragerea textului.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Acesta este un scurt articol de blog. Căutăm o companie sau o instituție care să ne ajute cu OCR și extragerea textului pentru o colecție masivă pe care am achiziționat-o, în schimbul accesului exclusiv anticipat. După perioada de embargo, vom lansa, desigur, întreaga colecție."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Textele academice de înaltă calitate sunt extrem de utile pentru antrenarea LLM-urilor. Deși colecția noastră este chinezească, aceasta ar trebui să fie utilă chiar și pentru antrenarea LLM-urilor în engleză: modelele par să codifice concepte și cunoștințe indiferent de limba sursă."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Pentru aceasta, textul trebuie extras din scanări. Ce obține Arhiva Annei din asta? Căutare full-text a cărților pentru utilizatorii săi."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Deoarece obiectivele noastre se aliniază cu cele ale dezvoltatorilor LLM, căutăm un colaborator. Suntem dispuși să vă oferim <strong>acces exclusiv anticipat la această colecție în masă pentru 1 an</strong>, dacă puteți face un OCR și o extragere a textului corespunzătoare. Dacă sunteți dispuși să împărtășiți întregul cod al fluxului dvs. de lucru cu noi, am fi dispuși să prelungim perioada de embargo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pagini de exemplu"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Pentru a ne demonstra că aveți un flux de lucru bun, iată câteva pagini exemplu pentru a începe, dintr-o carte despre supraconductori. Fluxul dumneavoastră de lucru ar trebui să gestioneze corect matematica, tabelele, graficele, notele de subsol și așa mai departe."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Trimiteți paginile procesate la adresa noastră de email. Dacă arată bine, vă vom trimite mai multe în privat și ne așteptăm să puteți rula rapid fluxul de lucru pe acestea. Odată ce suntem mulțumiți, putem face o înțelegere."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Colecție"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Câteva informații suplimentare despre colecție. <a %(duxiu)s>Duxiu</a> este o bază de date masivă de cărți scanate, creată de <a %(chaoxing)s>SuperStar Digital Library Group</a>. Majoritatea sunt cărți academice, scanate pentru a fi disponibile digital universităților și bibliotecilor. Pentru publicul nostru vorbitor de engleză, <a %(library_princeton)s>Princeton</a> și <a %(guides_lib_uw)s>Universitatea din Washington</a> oferă prezentări generale bune. Există, de asemenea, un articol excelent care oferă mai multe informații: <a %(doi)s>„Digitizarea cărților chinezești: un studiu de caz al motorului de căutare SuperStar DuXiu Scholar”</a> (căutați-l în Arhiva Annei)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Cărțile din Duxiu au fost de mult timp piratate pe internetul chinezesc. De obicei, sunt vândute pentru mai puțin de un dolar de către revânzători. De obicei, sunt distribuite folosind echivalentul chinezesc al Google Drive, care a fost adesea hack-uit pentru a permite mai mult spațiu de stocare. Unele detalii tehnice pot fi găsite <a %(github_duty_machine)s>aici</a> și <a %(github_821_github_io)s>aici</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Deși cărțile au fost distribuite semi-public, este destul de dificil să le obții în masă. Am avut acest lucru sus pe lista noastră de TO-DO și am alocat mai multe luni de muncă cu normă întreagă pentru asta. Cu toate acestea, recent, un voluntar incredibil, uimitor și talentat ne-a contactat, spunându-ne că a făcut deja toată această muncă — cu mari cheltuieli. Ne-au împărtășit întreaga colecție, fără a aștepta nimic în schimb, cu excepția garanției de păstrare pe termen lung. Cu adevărat remarcabil. Au fost de acord să ceară ajutor în acest mod pentru a obține OCR-ul colecției."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Colecția cuprinde 7.543.702 fișiere. Aceasta este mai mult decât non-ficțiunea din Library Genesis (aproximativ 5,3 milioane). Dimensiunea totală a fișierelor este de aproximativ 359TB (326TiB) în forma sa actuală."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Suntem deschiși la alte propuneri și idei. Doar contactați-ne. Consultați Arhiva Annei pentru mai multe informații despre colecțiile noastre, eforturile de păstrare și cum puteți ajuta. Mulțumim!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avertisment: acest articol de blog a fost învechit. Am decis că IPFS nu este încă pregătit pentru utilizare pe scară largă. Vom continua să legăm fișierele pe IPFS din Arhiva Annei când este posibil, dar nu le vom mai găzdui noi înșine și nici nu recomandăm altora să le oglindească folosind IPFS. Vă rugăm să consultați pagina noastră de Torrents dacă doriți să ajutați la păstrarea colecției noastre."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Ajutați la distribuirea Z-Library pe IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cum să conduci o bibliotecă de umbră: operațiuni la Arhiva Annei"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nu există <q>AWS pentru organizațiile caritabile de umbră,</q> așa că cum conducem Arhiva Annei?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Conduc <a %(wikipedia_annas_archive)s>Arhiva Annei</a>, cel mai mare motor de căutare open-source non-profit din lume pentru <a %(wikipedia_shadow_library)s>biblioteci de umbră</a>, precum Sci-Hub, Library Genesis și Z-Library. Scopul nostru este să facem cunoștințele și cultura ușor accesibile și, în cele din urmă, să construim o comunitate de oameni care împreună arhivează și conservă <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>toate cărțile din lume</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "În acest articol voi arăta cum gestionăm acest site și provocările unice care vin cu operarea unui site cu statut legal discutabil, deoarece nu există un „AWS pentru organizații caritabile de umbră”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Verificați și articolul asociat <a %(blog_how_to_become_a_pirate_archivist)s>Cum să devii un arhivist pirat</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Jetoane de inovație"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Să începem cu tehnologia noastră. Este deliberat plictisitoare. Folosim Flask, MariaDB și ElasticSearch. Asta este literalmente tot. Căutarea este în mare parte o problemă rezolvată și nu intenționăm să o reinventăm. În plus, trebuie să ne cheltuim <a %(mcfunley)s>jetoanele de inovație</a> pe altceva: să nu fim închiși de autorități."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Deci, cât de legală sau ilegală este exact Arhiva Annei? Acest lucru depinde în mare măsură de jurisdicția legală. Majoritatea țărilor cred într-o formă de copyright, ceea ce înseamnă că persoanelor sau companiilor li se atribuie un monopol exclusiv asupra anumitor tipuri de lucrări pentru o anumită perioadă de timp. Ca o paranteză, la Arhiva Annei credem că, deși există unele beneficii, în general copyright-ul este un net-negativ pentru societate — dar aceasta este o poveste pentru altă dată."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Acest monopol exclusiv asupra anumitor lucrări înseamnă că este ilegal pentru oricine din afara acestui monopol să distribuie direct acele lucrări — inclusiv pentru noi. Dar Arhiva Annei este un motor de căutare care nu distribuie direct acele lucrări (cel puțin nu pe site-ul nostru de clearnet), deci ar trebui să fie în regulă, nu? Nu chiar. În multe jurisdicții, nu este doar ilegal să distribui lucrări protejate de copyright, ci și să faci legături către locuri care o fac. Un exemplu clasic al acestui lucru este legea DMCA din Statele Unite."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Acesta este capătul cel mai strict al spectrului. La celălalt capăt al spectrului ar putea exista teoretic țări fără legi de copyright, dar acestea nu există cu adevărat. Aproape fiecare țară are o formă de lege a copyright-ului în vigoare. Aplicarea este o altă poveste. Există multe țări cu guverne care nu se preocupă să aplice legea copyright-ului. Există, de asemenea, țări între cele două extreme, care interzic distribuirea lucrărilor protejate de copyright, dar nu interzic legătura către astfel de lucrări."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "O altă considerație este la nivel de companie. Dacă o companie operează într-o jurisdicție care nu se preocupă de drepturile de autor, dar compania însăși nu este dispusă să își asume niciun risc, atunci ar putea închide site-ul dvs. de îndată ce cineva se plânge de el."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "În cele din urmă, o considerație importantă sunt plățile. Deoarece trebuie să rămânem anonimi, nu putem folosi metode tradiționale de plată. Acest lucru ne lasă cu criptomonedele, și doar un mic subset de companii le acceptă (există carduri de debit virtuale plătite cu cripto, dar sunt adesea neacceptate)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arhitectura sistemului"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Deci, să zicem că ați găsit câteva companii dispuse să găzduiască site-ul dumneavoastră fără să vă închidă — să le numim „furnizori iubitori de libertate” 😄. Veți descoperi rapid că găzduirea tuturor cu ei este destul de costisitoare, așa că poate doriți să găsiți niște „furnizori ieftini” și să faceți găzduirea efectivă acolo, proxy prin furnizorii iubitori de libertate. Dacă faceți totul corect, furnizorii ieftini nu vor ști niciodată ce găzduiți și nu vor primi niciodată plângeri."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Cu toți acești furnizori există riscul să vă închidă oricum, așa că aveți nevoie și de redundanță. Avem nevoie de aceasta la toate nivelurile stivei noastre."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "O companie oarecum iubitoare de libertate care s-a pus într-o poziție interesantă este Cloudflare. Ei au <a %(blog_cloudflare)s>argumentat</a> că nu sunt un furnizor de găzduire, ci o utilitate, ca un ISP. Prin urmare, nu sunt supuși cererilor de retragere DMCA sau altor cereri de retragere și redirecționează orice cerere către furnizorul dumneavoastră de găzduire real. Au mers până la a merge în instanță pentru a proteja această structură. Prin urmare, îi putem folosi ca un alt strat de caching și protecție."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nu acceptă plăți anonime, așa că putem folosi doar planul lor gratuit. Acest lucru înseamnă că nu putem folosi funcțiile lor de echilibrare a încărcării sau de failover. Prin urmare, <a %(annas_archive_l255)s>am implementat acest lucru noi înșine</a> la nivel de domeniu. La încărcarea paginii, browserul va verifica dacă domeniul curent este încă disponibil și, dacă nu, rescrie toate URL-urile către un alt domeniu. Deoarece Cloudflare cachează multe pagini, acest lucru înseamnă că un utilizator poate ajunge pe domeniul nostru principal, chiar dacă serverul proxy este căzut, și apoi la următorul clic să fie mutat pe un alt domeniu."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "De asemenea, avem preocupări operaționale normale de gestionat, cum ar fi monitorizarea sănătății serverului, înregistrarea erorilor de backend și frontend și așa mai departe. Arhitectura noastră de failover permite o mai mare robustețe și în acest sens, de exemplu prin rularea unui set complet diferit de servere pe unul dintre domenii. Putem chiar rula versiuni mai vechi ale codului și dataset-urilor pe acest domeniu separat, în cazul în care un bug critic în versiunea principală trece neobservat."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Putem, de asemenea, să ne protejăm împotriva unei eventuale întoarceri a Cloudflare împotriva noastră, eliminându-l de pe unul dintre domenii, cum ar fi acest domeniu separat. Sunt posibile diferite permutări ale acestor idei."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Unelte"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Să vedem ce unelte folosim pentru a realiza toate acestea. Acest lucru evoluează foarte mult pe măsură ce întâmpinăm noi probleme și găsim noi soluții."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Server de aplicații: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Server proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Management server: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Dezvoltare: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Găzduire statică Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Există unele decizii asupra cărora am oscilat. Una dintre ele este comunicarea între servere: obișnuiam să folosim Wireguard pentru aceasta, dar am constatat că ocazional încetează să mai transmită date sau transmite date doar într-o singură direcție. Acest lucru s-a întâmplat cu mai multe configurații Wireguard pe care le-am încercat, cum ar fi <a %(github_costela_wesher)s>wesher</a> și <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Am încercat, de asemenea, tunelarea porturilor prin SSH, folosind autossh și sshuttle, dar am întâmpinat <a %(github_sshuttle)s>probleme acolo</a> (deși încă nu este clar pentru mine dacă autossh suferă de probleme TCP-over-TCP sau nu — doar mi se pare o soluție improvizată, dar poate că este de fapt în regulă?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "În schimb, am revenit la conexiuni directe între servere, ascunzând faptul că un server rulează pe furnizori ieftini folosind filtrarea IP cu UFW. Acest lucru are dezavantajul că Docker nu funcționează bine cu UFW, decât dacă folosiți <code>network_mode: \"host\"</code>. Toate acestea sunt puțin mai predispuse la erori, deoarece veți expune serverul la internet cu doar o mică configurare greșită. Poate ar trebui să revenim la autossh — feedback-ul ar fi foarte binevenit aici."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Am oscilat și între Varnish și Nginx. În prezent, ne place Varnish, dar are ciudățeniile și asperitățile sale. Același lucru se aplică și pentru Checkmk: nu ne place, dar funcționează pentru moment. Weblate a fost acceptabil, dar nu incredibil — uneori mă tem că îmi va pierde datele ori de câte ori încerc să le sincronizez cu repo-ul nostru git. Flask a fost bun în general, dar are unele ciudățenii care au costat mult timp pentru depanare, cum ar fi configurarea domeniilor personalizate sau problemele cu integrarea sa SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Până acum, celelalte instrumente au fost grozave: nu avem plângeri serioase despre MariaDB, ElasticSearch, Gitlab, Zulip, Docker și Tor. Toate acestea au avut unele probleme, dar nimic prea serios sau consumator de timp."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Concluzie"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "A fost o experiență interesantă să învățăm cum să configurăm un motor de căutare pentru o bibliotecă de umbră robustă și rezistentă. Sunt multe alte detalii de împărtășit în postările viitoare, așa că anunțați-mă despre ce ați dori să aflați mai multe!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Ca întotdeauna, căutăm donații pentru a susține această activitate, așa că asigurați-vă că verificați pagina de Donații pe Arhiva Annei. De asemenea, căutăm alte tipuri de sprijin, cum ar fi granturi, sponsori pe termen lung, furnizori de plăți cu risc ridicat, poate chiar reclame (de bun gust!). Și dacă doriți să contribuiți cu timpul și abilitățile dumneavoastră, căutăm mereu dezvoltatori, traducători și așa mai departe. Vă mulțumim pentru interesul și sprijinul acordat."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna și echipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Bună, sunt Anna. Am creat <a %(wikipedia_annas_archive)s>Arhiva Annei</a>, cea mai mare bibliotecă de umbră din lume. Acesta este blogul meu personal, în care eu și colegii mei scriem despre piraterie, conservarea digitală și altele."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conectează-te cu mine pe <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Rețineți că acest site este doar un blog. Găzduim aici doar propriile noastre cuvinte. Nu sunt găzduite sau legate torrente sau alte fișiere protejate prin drepturi de autor."

#, fuzzy
msgid "blog.index.heading"
msgstr "Postări pe blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 miliarde de extrageri WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Punerea a 5.998.794 de cărți pe IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avertisment: acest articol de blog a fost învechit. Am decis că IPFS nu este încă pregătit pentru utilizare pe scară largă. Vom continua să legăm fișierele pe IPFS din Arhiva Annei când este posibil, dar nu le vom mai găzdui noi înșine și nici nu recomandăm altora să le oglindească folosind IPFS. Vă rugăm să consultați pagina noastră de Torrents dacă doriți să ajutați la păstrarea colecției noastre."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Pe scurt:</strong> Arhiva Annei a extras toate datele din WorldCat (cea mai mare colecție de metadata de bibliotecă din lume) pentru a crea o listă TODO de cărți care trebuie păstrate.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Acum un an, ne-am <a %(blog)s>propus</a> să răspundem la această întrebare: <strong>Ce procent de cărți au fost păstrate permanent de bibliotecile din umbră?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Odată ce o carte ajunge într-o bibliotecă din umbră cu date deschise, cum ar fi <a %(wikipedia_library_genesis)s>Library Genesis</a>, și acum <a %(wikipedia_annas_archive)s>Arhiva Annei</a>, aceasta este oglindită în întreaga lume (prin torrente), păstrând-o practic pentru totdeauna."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Pentru a răspunde la întrebarea despre ce procent de cărți a fost păstrat, trebuie să știm numitorul: câte cărți există în total? Și ideal nu avem doar un număr, ci și metadata reală. Apoi putem nu doar să le comparăm cu bibliotecile din umbră, dar și <strong>să creăm o listă TODO de cărți rămase de păstrat!</strong> Am putea chiar să începem să visăm la un efort colectiv pentru a parcurge această listă TODO."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Am extras date din <a %(wikipedia_isbndb_com)s>ISBNdb</a> și am descărcat setul de date <a %(openlibrary)s>Open Library</a>, dar rezultatele au fost nesatisfăcătoare. Problema principală a fost că nu a existat o suprapunere semnificativă a ISBN-urilor. Vedeți acest diagram Venn din <a %(blog)s>postarea noastră pe blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Am fost foarte surprinși de cât de puțină suprapunere a existat între ISBNdb și Open Library, ambele incluzând liber date din diverse surse, cum ar fi extrageri web și înregistrări de bibliotecă. Dacă ambele fac o treabă bună în a găsi majoritatea ISBN-urilor existente, cercurile lor ar avea cu siguranță o suprapunere substanțială sau unul ar fi un subset al celuilalt. Ne-a făcut să ne întrebăm câte cărți cad <em>complet în afara acestor cercuri</em>? Avem nevoie de o bază de date mai mare."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Atunci ne-am îndreptat atenția către cea mai mare bază de date de cărți din lume: <a %(wikipedia_worldcat)s>WorldCat</a>. Aceasta este o bază de date proprietară deținută de organizația non-profit <a %(wikipedia_oclc)s>OCLC</a>, care agregă înregistrări de metadata din biblioteci din întreaga lume, în schimbul oferirii acestor biblioteci acces la setul complet de date și apariției lor în rezultatele de căutare ale utilizatorilor finali."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Chiar dacă OCLC este o organizație non-profit, modelul lor de afaceri necesită protejarea bazei lor de date. Ei bine, ne pare rău să spunem, prieteni de la OCLC, că oferim totul gratuit. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "În ultimul an, am extras meticulos toate înregistrările WorldCat. La început, am avut noroc. WorldCat tocmai își lansa reproiectarea completă a site-ului (în august 2022). Aceasta a inclus o revizuire substanțială a sistemelor lor de backend, introducând multe vulnerabilități de securitate. Am profitat imediat de oportunitate și am reușit să extragem sute de milioane (!) de înregistrări în doar câteva zile."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Reproiectarea WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "După aceea, vulnerabilitățile de securitate au fost reparate încet, una câte una, până când ultima pe care am găsit-o a fost remediată acum aproximativ o lună. Până atunci aveam aproape toate înregistrările și ne concentram doar pe obținerea unor înregistrări de calitate ușor mai ridicată. Așa că am simțit că este timpul să le lansăm!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Să aruncăm o privire asupra unor informații de bază despre date:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Containerele Arhivei Annei (AAC)</a>, care sunt în esență <a %(jsonlines)s>JSON Lines</a> comprimate cu <a %(zstd)s>Zstandard</a>, plus unele semantici standardizate. Aceste containere înfășoară diferite tipuri de înregistrări, bazate pe diferitele extrageri pe care le-am implementat."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Date"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "A apărut o eroare necunoscută. Vă rugăm să ne contactați la %(email)s cu o captură de ecran."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Această monedă are un minim mai mare decât de obicei. Vă rugăm să selectați o durată diferită sau o altă monedă."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Solicitarea nu a putut fi completată. Te rugăm să încerci din nou în câteva minute, și dacă problema persistă, contactează-ne la %(email)s cu o captură de ecran."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Eroare în procesarea plății. Vă rugăm să așteptați un moment și să încercați din nou. Dacă problema persistă mai mult de 24 de ore, vă rugăm să ne contactați la %(email)s cu o captură de ecran."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentariu ascuns"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problemă fișier: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versiune mai bună"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Doriți să raportați acest utilizator pentru comportament abuziv sau inadecvat?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Raportează abuzul"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuz raportat:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Ați raportat acest utilizator pentru abuz."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Răspunde"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Vă rugăm să <a %(a_login)s>vă autentificați</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Ați lăsat un comentariu. Poate dura un minut până să apară."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "Pagini afectate %(count)s"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nu este vizibil în Libgen.rs Non-ficțiune"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nu este vizibil în Libgen.rs Ficțiune"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nu este vizibil în Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcat ca nefuncțional în Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Lipsește din Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcat ca „spam” în Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcat ca „fișier rău” în Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nu toate paginile au putut fi convertite în PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Eșec la rularea exiftool pe acest fișier"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Carte (necunoscut/ă)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Carte (non-ficțiune)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Carte (ficțiune)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Articol de jurnal"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documente standard"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revistă"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Benzi desenate"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitură muzicală"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiobook"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Altele"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Descărcare de pe serverul partener"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Descărcare externă"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Împrumut extern"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Împrumut extern (tipărire dezactivată)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Explorați metadatele"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Conținut în torrente"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chineză"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Încărcări pe AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Index eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadate cehe"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca de Stat a Rusiei"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titlu"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Autor"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Editură"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Ediție"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Anul publicării"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nume original al fișierului"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Descriere și comentarii despre metadate"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Descărcările de pe serverul partener nu sunt disponibile temporar pentru acest fișier."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Server Partener Rapid #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomandat)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(fără verificare în browser sau liste de așteptare)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Server Partener Lent #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(ușor mai rapid, dar cu listă de așteptare)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(fără listă de așteptare, dar poate fi foarte lent)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-ficțiune"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficțiune"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(de asemenea fă click pe “OBȚINE” deasupra)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(fă click pe “OBȚINE” deasupra)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "reclamele lor sunt cunoscute pentru a conține software malițios, așa că folosiți un blocator de reclame sau nu faceți clic pe reclame"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Fișierele Nexus/STC pot fi nesigure pentru descărcare)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library pe Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(este necesar browser-ul Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Împrumută de la Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(doar pentru utilizatorii cu dizabilități de tipărire)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI asociat poate să nu fie disponibil în Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "colecție"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Descărcări torrent în masă"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(doar pentru experți)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Căutați în Arhiva Annei după ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Căutați în diverse alte baze de date pentru ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Găsiți înregistrarea originală în ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Caută în Arhiva Annei după ID-ul Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Găsiți înregistrarea originală în Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Căutați în Arhiva Annei după numărul OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Găsiți înregistrarea originală în WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Căutați în Arhiva Annei numărul SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Căutați manual pe DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Căutați în Arhiva Annei după numărul CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Găsiți înregistrarea originală în CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Căutați în Arhiva Annei după numărul DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Index eBook EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Arhiva Annei 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(fără verificare a browserului necesară)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadate cehe %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadate"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "descriere"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nume de fișier alternativ"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Titlu alternativ"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autor alternativ"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editură alternativă"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Ediție alternativă"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extensie alternativă"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "comentarii metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descriere alternativă"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "data deschiderii sursei"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Fișier Sci-Hub „%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Fișier Internet Archive Controlled Digital Lending „%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Acesta este un record al unui fișier de pe Internet Archive, nu un fișier descărcabil direct. Puteți încerca să împrumutați cartea (link mai jos) sau să folosiți acest URL când <a %(a_request)s>solicitați un fișier</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Dacă aveți acest fișier și nu este încă disponibil în Arhiva Annei, luați în considerare <a %(a_request)s>încărcarea lui</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s înregistrare metadata"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Înregistrare de metadate Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Număr OCLC (WorldCat) %(id)s înregistrare de metadate"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Înregistrare metadata SSID DuXiu %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Număr CADAL SSNO %(id)s înregistrare de metadate"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Înregistrare metadate MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Înregistrare metadate Nexus/STC ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Aceasta este o înregistrare de metadate, nu un fișier descărcabil. Puteți folosi acest URL când <a %(a_request)s>solicitați un fișier</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadate din înregistrarea legată"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Îmbunătățiți metadatele pe Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Avertisment: înregistrări multiple legate:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Îmbunătățiți metadatele"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Raportați calitatea fișierului"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Timp de descărcare"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Website:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Căutați în Arhiva Annei pentru “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorator de Coduri:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vizualizați în Explorer de Coduri „%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Citește mai mult…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Descărcări (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Împrumută (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Explorați metadatele (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentarii (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Liste (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistici (%(count)s)"

msgid "common.tech_details"
msgstr "Arată detalii tehnice"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌Acest fișier ar putea prezenta probleme și a fost ascuns de o bibliotecă-sursă.</span> Uneori asta se întâmplă la cererea deținătorului drepturilor de autor, uneori pentru că este disponibilă o alternativă mai bună, dar alteori datorită unei probleme cu fișierul în sine. Ar putea să fie totuși în regulă să-l descărcați, dar recomandăm ca întâi să căutați un fișier alternativ. Mai multe detalii:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "O versiune mai bună a acestui fișier ar putea fi disponibilă la %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Dacă încă vreo să descarci acest fișier, asigură-te că foloseși software de încredere și updatat pentru a-l deschide."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Descărcări rapide"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Descărcări rapide</strong> Deveniți <a %(a_membership)s>membru</a> pentru a sprijini conservarea pe termen lung a cărților, articolelor și altele. Pentru a vă arăta recunoștința noastră pentru sprijinul dvs., veți beneficia de descărcări rapide. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Dacă donați în această lună, veți primi <strong>dublu</strong> numărul de descărcări rapide."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Descărcări rapide</strong> Mai aveți %(remaining)s astăzi. Mulțumim că sunteți membru! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Descărcări rapide</strong> Ați epuizat descărcările rapide pentru astăzi."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Descărcări rapide</strong> Ați descărcat acest fișier recent. Linkurile rămân valabile pentru o perioadă."

msgid "page.md5.box.download.option"
msgstr "Opțiunea #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(fără redirecționare)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(deschide în vizualizator)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Recomandați unui prieten și atât dumneavoastră, cât și prietenul dumneavoastră veți primi %(percentage)s%% descărcări rapide bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Aflați mai multe…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Descărcări lente"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "De la parteneri de încredere."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mai multe informații în <a %(a_slow)s>Întrebări frecvente</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(poate necesita <a %(a_browser)s>verificarea browserului</a> — descărcări nelimitate!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "După descărcare:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Deschide în vizualizatorul nostru"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "arată descărcările externe"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Descărcări externe"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nu s-au găsit descărcări."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Toate opțiunile descărcate ar trebui să fie sigure pentru folosire. Acestea fiind spuse, te rugăm să fii precaut când descarci fișiere de pe internet. De exemplu, asigură-te că dispozitivele tale sunt updatate la zi."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Pentru fișiere mari, vă recomandăm să folosiți un manager de descărcare pentru a preveni întreruperile."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Manageri de descărcare recomandați: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Veți avea nevoie de un cititor de ebook-uri sau PDF pentru a deschide fișierul, în funcție de formatul fișierului."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Cititoare de ebook-uri recomandate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Vizualizator online Arhiva Annei"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Folosiți instrumente online pentru a converti între formate."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Instrumente de conversie recomandate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Puteți trimite atât fișiere PDF, cât și EPUB către Kindle sau Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Instrumente recomandate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Funcția Amazon „Trimite la Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "Funcția djazz „Trimite la Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Susțineți autorii și bibliotecile"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Dacă vă place acest lucru și vă permiteți, luați în considerare achiziționarea originalului sau susținerea directă a autorilor."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Dacă este disponibilă la biblioteca locală, luați în considerare să o împrumutați gratuit de acolo."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Calitatea fișierului"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Ajutați comunitatea raportând calitatea acestui fișier! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Raportați o problemă cu fișierul (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Calitate excelentă a fișierului (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Adăugați un comentariu (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Ce este în neregulă cu acest fișier?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Vă rugăm să folosiți <a %(a_copyright)s>formularul de reclamație DMCA / Drepturi de autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Descrieți problema (obligatoriu)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descrierea problemei"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 al unei versiuni mai bune a acestui fișier (dacă este aplicabil)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Completați acest câmp dacă există un alt fișier care se potrivește îndeaproape cu acest fișier (aceeași ediție, aceeași extensie de fișier dacă puteți găsi una), pe care oamenii ar trebui să-l folosească în locul acestui fișier. Dacă știți de o versiune mai bună a acestui fișier în afara Arhivei Annei, atunci vă rugăm să <a %(a_upload)s>o încărcați</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Puteți obține md5 din URL, de exemplu"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Trimiteți raportul"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Învață cum să <a %(a_metadata)s>îmbunătățești metadatele</a> pentru acest fișier singur."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Mulțumim pentru trimiterea raportului. Acesta va fi afișat pe această pagină și va fi revizuit manual de Anna (până când vom avea un sistem de moderare adecvat)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Ceva a mers prost. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Dacă acest fișier are o calitate excelentă, puteți discuta orice despre el aici! Dacă nu, vă rugăm să folosiți butonul „Raportează o problemă cu fișierul”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Mi-a plăcut această carte!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lasă un comentariu"

msgid "common.english_only"
msgstr "Textul continuă mai jos în engleză."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total descărcări: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un „MD5 al fișierului” este un hash care se calculează din conținutul fișierului și este rezonabil de unic pe baza acelui conținut. Toate bibliotecile shadow pe care le-am indexat aici folosesc în principal MD5-uri pentru a identifica fișierele."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un fișier poate apărea în mai multe biblioteci shadow. Pentru informații despre diferitele datasets pe care le-am compilat, consultați <a %(a_datasets)s>pagina Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Acesta este un fișier gestionat de biblioteca <a %(a_ia)s>Împrumut Digital Controlat al IA</a> și indexat de Arhiva Annei pentru căutare. Pentru informații despre diferitele datasets pe care le-am compilat, consultați <a %(a_datasets)s>pagina Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pentru informații despre acest fișier specific, consultați <a %(a_href)s>fișierul JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problemă la încărcarea acestei pagini"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Vă rugăm să reîmprospătați pentru a încerca din nou. <a %(a_contact)s>Contactați-ne</a> dacă problema persistă timp de mai multe ore."

msgid "page.md5.invalid.header"
msgstr "Nu a fost găsit"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” nu a fost găsit în baza noastră de date."

#, fuzzy
msgid "page.login.title"
msgstr "Autentificare / Înregistrare"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verificare browser"

#, fuzzy
msgid "page.login.text1"
msgstr "Pentru a preveni crearea de conturi de către spam-bots, trebuie să verificăm mai întâi browserul tău."

#, fuzzy
msgid "page.login.text2"
msgstr "Dacă rămâneți blocat într-o buclă infinită, vă recomandăm să instalați <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Poate ajuta și dezactivarea blocatorilor de reclame și a altor extensii de browser."

#, fuzzy
msgid "page.codes.title"
msgstr "Coduri"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorator de Coduri"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explorați codurile cu care sunt etichetate înregistrările, după prefix. Coloana „înregistrări” arată numărul de înregistrări etichetate cu coduri cu prefixul dat, așa cum se vede în motorul de căutare (inclusiv înregistrările doar cu metadate). Coloana „coduri” arată câte coduri reale au un anumit prefix."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Această pagină poate dura ceva timp pentru a se genera, motiv pentru care necesită un captcha Cloudflare. <a %(a_donate)s>Membrii</a> pot sări peste captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Vă rugăm să nu extrageți aceste pagini. În schimb, vă recomandăm <a %(a_import)s>generarea</a> sau <a %(a_download)s>descărcarea</a> bazelor noastre de date ElasticSearch și MariaDB și rularea <a %(a_software)s>codului nostru open source</a>. Datele brute pot fi explorate manual prin fișiere JSON, cum ar fi <a %(a_json_file)s>acesta</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefix"

#, fuzzy
msgid "common.form.go"
msgstr "Mergi"

#, fuzzy
msgid "common.form.reset"
msgstr "Resetează"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Căutați Arhiva Annei"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Avertisment: codul conține caractere Unicode incorecte și poate funcționa incorect în diverse situații. Binarele brute pot fi decodificate din reprezentarea base64 în URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefix de cod cunoscut „%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefix"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etichetă"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descriere"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL pentru un cod specific"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "„%%s” va fi înlocuit cu valoarea codului"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL generic"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s înregistrare care se potrivește cu „%(prefix_label)s”"
msgstr[1] ""
msgstr[2] "%(count)s înregistrări care se potrivesc cu „%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL pentru codul specific: „%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mai mult…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Coduri care încep cu „%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "înregistrări"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "coduri"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Mai puțin de %(count)s înregistrări"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pentru reclamații DMCA / de copyright, folosiți <a %(a_copyright)s>acest formular</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Orice alte modalități de a ne contacta în legătură cu revendicările de drepturi de autor vor fi șterse automat."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Apreciem foarte mult feedback-ul și întrebările dumneavoastră!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Cu toate acestea, din cauza cantității de spam și emailuri fără sens pe care le primim, vă rugăm să bifați căsuțele pentru a confirma că înțelegeți aceste condiții pentru a ne contacta."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Revendicările de drepturi de autor trimise la acest email vor fi ignorate; folosiți formularul în schimb."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Serverele partenere nu sunt disponibile din cauza închiderii găzduirii. Ar trebui să fie din nou funcționale în curând."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Abonamentele vor fi prelungite corespunzător."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nu ne trimiteți emailuri pentru a <a %(a_request)s>solicita cărți</a><br>sau încărcări mici (<10k) <a %(a_upload)s>încărcări</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Când puneți întrebări despre cont sau donații, adăugați ID-ul contului, capturi de ecran, chitanțe, cât mai multe informații posibil. Verificăm emailul nostru doar la fiecare 1-2 săptămâni, așa că neincluzând aceste informații va întârzia orice rezolvare."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Afișează emailul"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formular de revendicare DMCA / Drepturi de autor"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Dacă aveți o revendicare DMCA sau altă revendicare de drepturi de autor, vă rugăm să completați acest formular cât mai precis posibil. Dacă întâmpinați probleme, vă rugăm să ne contactați la adresa noastră dedicată DMCA: %(email)s. Rețineți că revendicările trimise prin e-mail la această adresă nu vor fi procesate, este doar pentru întrebări. Vă rugăm să folosiți formularul de mai jos pentru a trimite revendicările."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL-uri pe Arhiva Annei (obligatoriu). Unul pe linie. Vă rugăm să includeți doar URL-uri care descriu exact aceeași ediție a unei cărți. Dacă doriți să faceți o revendicare pentru mai multe cărți sau mai multe ediții, vă rugăm să trimiteți acest formular de mai multe ori."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Revendicările care grupează mai multe cărți sau ediții vor fi respinse."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Numele dumneavoastră (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adresa (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Număr de telefon (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descriere clară a materialului sursă (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN-urile materialului sursă (dacă este aplicabil). Unul pe linie. Vă rugăm să includeți doar acelea care corespund exact ediției pentru care raportați o revendicare de drepturi de autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL-uri ale materialului sursă, unul pe linie. Vă rugăm să căutați materialul sursă în Open Library. Acest lucru ne va ajuta să vă verificăm revendicarea."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL-uri către materialul sursă, unul pe linie (obligatoriu). Vă rugăm să includeți cât mai multe posibil, pentru a ne ajuta să vă verificăm revendicarea (de exemplu, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declarație și semnătură (obligatoriu)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Trimiteți revendicarea"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Vă mulțumim pentru trimiterea revendicării de drepturi de autor. O vom revizui cât mai curând posibil. Vă rugăm să reîncărcați pagina pentru a trimite alta."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Ceva nu a mers bine. Vă rugăm să reîncărcați pagina și să încercați din nou."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Dacă sunteți interesat să oglindiți acest set de date pentru <a %(a_archival)s>arhivare</a> sau pentru <a %(a_llm)s>antrenarea LLM</a>, vă rugăm să ne contactați."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Misiunea noastră este să arhivăm toate cărțile din lume (precum și lucrările, revistele etc.) și să le facem larg accesibile. Credem că toate cărțile ar trebui să fie oglindite pe scară largă, pentru a asigura redundanța și reziliența. De aceea adunăm fișiere dintr-o varietate de surse. Unele surse sunt complet deschise și pot fi oglindite în masă (cum ar fi Sci-Hub). Altele sunt închise și protective, așa că încercăm să le extragem pentru a „elibera” cărțile lor. Altele se situează undeva între."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Toate datele noastre pot fi <a %(a_torrents)s>descărcate prin torrent</a>, iar toate metadatele noastre pot fi <a %(a_anna_software)s>generate</a> sau <a %(a_elasticsearch)s>descărcate</a> ca baze de date ElasticSearch și MariaDB. Datele brute pot fi explorate manual prin fișiere JSON, cum ar fi <a %(a_dbrecord)s>acesta</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Prezentare generală"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Mai jos este o prezentare rapidă a surselor fișierelor din Arhiva Annei."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sursă"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Dimensiune"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% oglindit de AA / torrente disponibile"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Procente din numărul de fișiere"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ultima actualizare"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-ficțiune și Ficțiune"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "Fișier %(count)s"
msgstr[1] ""
msgstr[2] "Fișiere %(count)s"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Prin Libgen.li „scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: înghețat din 2021; majoritatea disponibile prin torrente"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: adăugiri minore de atunci</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluzând “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrentele de ficțiune sunt în urmă (deși ID-urile ~4-6M nu sunt torrente deoarece se suprapun cu torrentele noastre Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Colecția „chineză” din Z-Library pare a fi aceeași cu colecția noastră DuXiu, dar cu MD5-uri diferite. Excludem aceste fișiere din torrente pentru a evita duplicarea, dar le arătăm în continuare în indexul nostru de căutare."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Împrumut Digital Controlat de IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dintre fișiere sunt căutabile."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluzând duplicatele"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Deoarece bibliotecile de umbră sincronizează adesea datele între ele, există o suprapunere considerabilă între biblioteci. De aceea, numerele nu se adună la total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Procentajul „oglindit și semănat de Arhiva Annei” arată câte fișiere oglindim noi înșine. Semănăm aceste fișiere în masă prin torrente și le facem disponibile pentru descărcare directă prin intermediul site-urilor partenere."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Biblioteci sursă"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Unele biblioteci sursă promovează partajarea în masă a datelor lor prin torrente, în timp ce altele nu își partajează colecția în mod liber. În acest din urmă caz, Arhiva Annei încearcă să extragă colecțiile lor și să le facă disponibile (vezi pagina noastră de <a %(a_torrents)s>Torrente</a>). Există și situații intermediare, de exemplu, în care bibliotecile sursă sunt dispuse să partajeze, dar nu au resursele necesare pentru a face acest lucru. În aceste cazuri, încercăm și noi să ajutăm."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Mai jos este o prezentare generală a modului în care interacționăm cu diferitele biblioteci sursă."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sursă"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Fișiere"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dumpuri zilnice HTTP ale bazei de date</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrente automate pentru <a %(nonfiction)s>Non-Ficțiune</a> și <a %(fiction)s>Ficțiune</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(covers)s>torrente cu coperți de cărți</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen „scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub a înghețat fișierele noi din 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadate disponibile <a %(scihub1)s>aici</a> și <a %(scihub2)s>aici</a>, precum și ca parte a <a %(libgenli)s>bazei de date Libgen.li</a> (pe care o folosim)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torente de date disponibile <a %(scihub1)s>aici</a>, <a %(scihub2)s>aici</a> și <a %(libgenli)s>aici</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Unele fișiere noi sunt <a %(libgenrs)s>adăugate</a> la „scimag” de pe Libgen, dar nu suficient pentru a justifica noi torrente."

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dumpuri trimestriale <a %(dbdumps)s>HTTP ale bazei de date</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrentele de non-ficțiune sunt partajate cu Libgen.rs (și oglindite <a %(libgenli)s>aici</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Arhiva Annei și Libgen.li gestionează împreună colecții de <a %(comics)s>benzi desenate</a>, <a %(magazines)s>reviste</a>, <a %(standarts)s>documente standard</a> și <a %(fiction)s>ficțiune (divergentă de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Colecția lor „fiction_rus” (ficțiune rusă) nu are torrente dedicate, dar este acoperită de torrente de la alții, iar noi păstrăm o <a %(fiction_rus)s>oglindă</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Arhiva Annei și Z-Library gestionează colaborativ o colecție de <a %(metadata)s>metadate Z-Library</a> și <a %(files)s>fișiere Z-Library</a>."

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Unele metadate sunt disponibile prin <a %(openlib)s>dumpuri ale bazei de date Open Library</a>, dar acestea nu acoperă întreaga colecție IA."

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nu sunt disponibile dumpuri de metadate ușor accesibile pentru întreaga lor colecție."

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(ia)s>metadate IA</a>."

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Fișiere disponibile doar pentru împrumut pe o bază limitată, cu diverse restricții de acces."

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(ia)s>fișiere IA</a>."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Diverse baze de date de metadate împrăștiate pe internetul chinez; deși adesea baze de date plătite."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nu sunt disponibile dumpuri de metadate ușor accesibile pentru întreaga lor colecție."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(duxiu)s>metadate DuXiu</a>."

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Diverse baze de date de fișiere împrăștiate pe internetul chinez; deși adesea baze de date plătite."

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Majoritatea fișierelor sunt accesibile doar folosind conturi premium BaiduYun; viteze de descărcare lente."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(duxiu)s>fișiere DuXiu</a>."

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Diverse surse mai mici sau ocazionale. Încurajăm oamenii să încarce mai întâi în alte biblioteci shadow, dar uneori oamenii au colecții care sunt prea mari pentru ca alții să le sorteze, deși nu suficient de mari pentru a justifica propria categorie."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Surse doar cu metadate"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "De asemenea, îmbogățim colecția noastră cu surse doar cu metadate, pe care le putem asocia cu fișiere, de exemplu, folosind numere ISBN sau alte câmpuri. Mai jos este o prezentare generală a acestora. Din nou, unele dintre aceste surse sunt complet deschise, în timp ce pentru altele trebuie să le extragem."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Inspirația noastră pentru colectarea metadatelor este obiectivul lui Aaron Swartz de a avea „o pagină web pentru fiecare carte publicată vreodată”, pentru care a creat <a %(a_openlib)s>Open Library</a>. Acest proiect a avut succes, dar poziția noastră unică ne permite să obținem metadate pe care ei nu le pot obține. O altă inspirație a fost dorința noastră de a ști <a %(a_blog)s>câte cărți există în lume</a>, astfel încât să putem calcula câte cărți mai avem de salvat."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Rețineți că în căutarea de metadate, afișăm înregistrările originale. Nu facem nicio fuziune a înregistrărilor."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ultima actualizare"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dumpuri de baze de date</a> lunare"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nu este disponibil direct în masă, protejat împotriva scraping-ului"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(worldcat)s>metadate OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Bază de date unificată"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Combinăm toate sursele de mai sus într-o singură bază de date unificată pe care o folosim pentru a deservi acest site web. Această bază de date unificată nu este disponibilă direct, dar deoarece Arhiva Annei este complet open source, poate fi destul de ușor <a %(a_generated)s>generată</a> sau <a %(a_downloaded)s>descărcată</a> ca baze de date ElasticSearch și MariaDB. Scripturile de pe acea pagină vor descărca automat toate metadatele necesare din sursele menționate mai sus."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Dacă doriți să explorați datele noastre înainte de a rula acele scripturi local, puteți consulta fișierele noastre JSON, care fac legătura cu alte fișiere JSON. <a %(a_json)s>Acest fișier</a> este un punct de plecare bun."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptat din <a %(a_href)s>postarea noastră pe blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> este o bază de date masivă de cărți scanate, creată de <a %(superstar_link)s>SuperStar Digital Library Group</a>. Majoritatea sunt cărți academice, scanate pentru a fi disponibile digital universităților și bibliotecilor. Pentru publicul nostru vorbitor de engleză, <a %(princeton_link)s>Princeton</a> și <a %(uw_link)s>University of Washington</a> au prezentări generale bune. Există, de asemenea, un articol excelent care oferă mai multe informații: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Cărțile de la Duxiu au fost de mult timp piratate pe internetul chinezesc. De obicei, sunt vândute pentru mai puțin de un dolar de către revânzători. Ele sunt distribuite în mod obișnuit folosind echivalentul chinezesc al Google Drive, care a fost adesea hack-uit pentru a permite mai mult spațiu de stocare. Unele detalii tehnice pot fi găsite <a %(link1)s>aici</a> și <a %(link2)s>aici</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Deși cărțile au fost distribuite semi-public, este destul de dificil să le obțineți în masă. Am avut acest lucru sus pe lista noastră de TO-DO și am alocat mai multe luni de muncă cu normă întreagă pentru asta. Cu toate acestea, la sfârșitul anului 2023, un voluntar incredibil, uimitor și talentat ne-a contactat, spunându-ne că a făcut deja toată această muncă — cu mari cheltuieli. Ne-au împărtășit întreaga colecție, fără a aștepta nimic în schimb, cu excepția garanției de păstrare pe termen lung. Cu adevărat remarcabil."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resurse"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total fișiere: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Dimensiune totală fișiere: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Fișiere oglindite de Arhiva Annei: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ultima actualizare: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrente de la Arhiva Annei"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplu de înregistrare pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Postarea noastră pe blog despre aceste date"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripturi pentru importul metadatelor"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formatul Containerele Arhivei Annei"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mai multe informații de la voluntarii noștri (note brute):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "Împrumut digital controlat IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Acest set de date este strâns legat de <a %(a_datasets_openlib)s>setul de date Open Library</a>. Conține o extragere a tuturor metadatelor și o mare parte din fișierele din Biblioteca de Împrumut Digital Controlat a IA. Actualizările sunt lansate în <a %(a_aac)s>formatul Containerelor Arhiva Annei</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Aceste înregistrări sunt referite direct din setul de date Open Library, dar conțin și înregistrări care nu sunt în Open Library. De asemenea, avem un număr de fișiere de date extrase de membrii comunității de-a lungul anilor."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Colecția constă din două părți. Aveți nevoie de ambele părți pentru a obține toate datele (cu excepția torentelor înlocuite, care sunt tăiate pe pagina torentelor)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "prima noastră lansare, înainte de a standardiza pe formatul <a %(a_aac)s>Containerele Arhivei Annei (AAC)</a>. Conține metadate (ca json și xml), pdf-uri (din sistemele de împrumut digital acsm și lcpdf) și miniaturi de copertă."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "lansări noi incrementale, folosind AAC. Conține doar metadate cu marcaje temporale după 2023-01-01, deoarece restul este deja acoperit de „ia”. De asemenea, toate fișierele pdf, de această dată din sistemele de împrumut acsm și „bookreader” (cititorul web al IA). Deși numele nu este exact corect, încă populăm fișierele bookreader în colecția ia2_acsmpdf_files, deoarece sunt mutual exclusive."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Site-ul principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca Digitală de Împrumut"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentație metadate (majoritatea câmpurilor)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informații despre țara ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Agenția Internațională ISBN publică regulat intervalele pe care le-a alocat agențiilor naționale ISBN. Din acestea putem deduce cărei țări, regiuni sau grup lingvistic îi aparține acest ISBN. În prezent, folosim aceste date indirect, prin intermediul bibliotecii Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resurse"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultima actualizare: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Website ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadate"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Pentru povestea diferitelor fork-uri Library Genesis, vedeți pagina pentru <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li conține majoritatea aceluiași conținut și metadate ca și Libgen.rs, dar are câteva colecții suplimentare, și anume benzi desenate, reviste și documente standard. De asemenea, a integrat <a %(a_scihub)s>Sci-Hub</a> în metadatele și motorul său de căutare, pe care le folosim pentru baza noastră de date."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadatele pentru această bibliotecă sunt disponibile gratuit <a %(a_libgen_li)s>la libgen.li</a>. Cu toate acestea, acest server este lent și nu suportă reluarea conexiunilor întrerupte. Aceleași fișiere sunt disponibile și pe <a %(a_ftp)s>un server FTP</a>, care funcționează mai bine."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrentele sunt disponibile pentru majoritatea conținutului suplimentar, cele mai notabile fiind torrentele pentru benzi desenate, reviste și documente standard, lansate în colaborare cu Arhiva Annei."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Colecția de ficțiune are propriile sale torrente (divergente de <a %(a_href)s>Libgen.rs</a>) începând de la %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Conform administratorului Libgen.li, colecția „fiction_rus” (ficțiune rusă) ar trebui să fie acoperită de torrentele lansate regulat de <a %(a_booktracker)s>booktracker.org</a>, cele mai notabile fiind torrentele <a %(a_flibusta)s>flibusta</a> și <a %(a_librusec)s>lib.rus.ec</a> (pe care le oglindim <a %(a_torrents)s>aici</a>, deși nu am stabilit încă ce torrente corespund cu ce fișiere)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statisticile pentru toate colecțiile pot fi găsite <a %(a_href)s>pe site-ul libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-ficțiunea pare, de asemenea, să se fi diversificat, dar fără noi torente. Se pare că acest lucru s-a întâmplat de la începutul anului 2022, deși nu am verificat acest lucru."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Anumite intervale fără torrente (cum ar fi intervalele de ficțiune f_3463000 până la f_4260000) sunt probabil fișiere Z-Library (sau alte duplicate), deși am putea dori să facem o deduplicare și să creăm torrente pentru fișierele unice lgli din aceste intervale."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Rețineți că fișierele torrent care se referă la „libgen.is” sunt explicit oglinzi ale <a %(a_libgen)s>Libgen.rs</a> („.is” este un domeniu diferit folosit de Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "O resursă utilă pentru utilizarea metadatelor este <a %(a_href)s>această pagină</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrente de ficțiune pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrente de benzi desenate pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrente de reviste pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrente de documente standard pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrente de ficțiune rusă pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadate"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadate prin FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informații despre câmpurile metadatelor"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Oglindă a altor torrente (și torrente unice de ficțiune și benzi desenate)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum de discuții"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Postarea noastră pe blog despre lansarea benzilor desenate"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Povestea rapidă a diferitelor ramuri ale Library Genesis (sau „Libgen”) este că, de-a lungul timpului, diferitele persoane implicate în Library Genesis au avut neînțelegeri și au mers pe drumuri separate."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Versiunea „.fun” a fost creată de fondatorul original. Este în curs de revizuire în favoarea unei noi versiuni, mai distribuite."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Versiunea „.rs” are date foarte similare și își lansează cel mai constant colecția în torrente de volum mare. Este împărțită aproximativ într-o secțiune de „ficțiune” și una de „non-ficțiune”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Inițial la „http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Versiunea „.li”</a> are o colecție masivă de benzi desenate, precum și alt conținut, care nu este (încă) disponibil pentru descărcare în masă prin torrente. Are o colecție separată de torrente de cărți de ficțiune și conține metadatele <a %(a_scihub)s>Sci-Hub</a> în baza sa de date."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Conform acestei <a %(a_mhut)s>postări pe forum</a>, Libgen.li a fost găzduit inițial la „http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> într-un anumit sens este, de asemenea, o ramură a Library Genesis, deși au folosit un nume diferit pentru proiectul lor."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Această pagină este despre versiunea „.rs”. Este cunoscută pentru publicarea constantă atât a metadatelor, cât și a conținutului complet al catalogului său de cărți. Colecția sa de cărți este împărțită între o secțiune de ficțiune și una de non-ficțiune."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "O resursă utilă pentru utilizarea metadatelor este <a %(a_metadata)s>această pagină</a> (blochează intervalele de IP, ar putea fi necesar un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Începând cu martie 2024, noi torrente sunt postate în <a %(a_href)s>acest fir de discuție pe forum</a> (blochează intervale de IP, s-ar putea să fie necesar un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrente Non-Fiction pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrente Fiction pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadate Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informații despre câmpurile de metadate Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrente Non-Fiction Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrente Fiction Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum de discuții Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrente de la Arhiva Annei (coperti de cărți)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Blogul nostru despre lansarea coperților de cărți"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis este cunoscut pentru generozitatea cu care își pune datele la dispoziție în mod masiv prin torrente. Colecția noastră Libgen constă în date auxiliare pe care ei nu le eliberează direct, în parteneriat cu ei. Mulțumiri tuturor celor implicați în Library Genesis pentru colaborarea cu noi!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Lansarea 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Această <a %(blog_post)s>primă lansare</a> este destul de mică: aproximativ 300GB de coperți de cărți din fork-ul Libgen.rs, atât fiction cât și non-fiction. Ele sunt organizate în același mod în care apar pe libgen.rs, de exemplu:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pentru o carte non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pentru o carte fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "La fel ca în colecția Z-Library, le-am pus pe toate într-un fișier mare .tar, care poate fi montat folosind <a %(a_ratarmount)s>ratarmount</a> dacă doriți să serviți fișierele direct."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> este o bază de date proprietară a organizației non-profit <a %(a_oclc)s>OCLC</a>, care agregă înregistrări de metadate din biblioteci din întreaga lume. Este probabil cea mai mare colecție de metadate de bibliotecă din lume."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octombrie 2023, lansare inițială:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "În octombrie 2023 am <a %(a_scrape)s>lansat</a> o extragere cuprinzătoare a bazei de date OCLC (WorldCat), în <a %(a_aac)s>formatul Containerelor Arhivei Annei</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrente de la Arhiva Annei"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Postarea noastră pe blog despre aceste date"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library este un proiect open source al Internet Archive pentru a cataloga fiecare carte din lume. Are una dintre cele mai mari operațiuni de scanare a cărților din lume și are multe cărți disponibile pentru împrumut digital. Catalogul său de metadate de cărți este disponibil gratuit pentru descărcare și este inclus pe Arhiva Annei (deși momentan nu este în căutare, cu excepția cazului în care căutați explicit un ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadate"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Lansare 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Acesta este un dump al multor apeluri către isbndb.com din septembrie 2022. Am încercat să acoperim toate intervalele ISBN. Acestea sunt aproximativ 30,9 milioane de înregistrări. Pe site-ul lor, ei susțin că au de fapt 32,6 milioane de înregistrări, așa că este posibil să fi ratat unele, sau <em>ei</em> ar putea face ceva greșit."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Răspunsurile JSON sunt practic brute de pe serverul lor. O problemă de calitate a datelor pe care am observat-o este că pentru numerele ISBN-13 care încep cu un prefix diferit de „978-”, ei includ totuși un câmp „isbn” care este pur și simplu numărul ISBN-13 cu primele 3 cifre tăiate (și cifra de control recalculată). Acest lucru este evident greșit, dar așa par să procedeze, așa că nu am modificat acest aspect."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "O altă problemă potențială pe care o puteți întâlni este faptul că câmpul „isbn13” are duplicate, așa că nu îl puteți folosi ca cheie primară într-o bază de date. Câmpurile combinate „isbn13”+„isbn” par să fie unice."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pentru informații despre Sci-Hub, vă rugăm să consultați <a %(a_scihub)s>site-ul oficial</a>, <a %(a_wikipedia)s>pagina Wikipedia</a> și acest <a %(a_radiolab)s>interviu podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Rețineți că Sci-Hub a fost <a %(a_reddit)s>înghețat din 2021</a>. A fost înghețat anterior, dar în 2021 au fost adăugate câteva milioane de lucrări. Totuși, un număr limitat de lucrări sunt adăugate în colecțiile „scimag” de la Libgen, deși nu suficient pentru a justifica noi torrente în masă."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Folosim metadatele Sci-Hub furnizate de <a %(a_libgen_li)s>Libgen.li</a> în colecția sa „scimag”. De asemenea, folosim dataset-ul <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Rețineți că torrentele „smarch” sunt <a %(a_smarch)s>depreciate</a> și, prin urmare, nu sunt incluse în lista noastră de torrente."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrente pe Arhiva Annei"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadate și torrente"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrente pe Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrente pe Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Actualizări pe Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pagina Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Interviu podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Încărcări în Arhiva Annei"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Prezentare generală de pe <a %(a1)s>pagina de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Diverse surse mai mici sau ocazionale. Încurajăm oamenii să încarce mai întâi în alte biblioteci shadow, dar uneori oamenii au colecții prea mari pentru ca alții să le sorteze, deși nu suficient de mari pentru a justifica propria categorie."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Colecția „upload” este împărțită în subcolecții mai mici, care sunt indicate în AACID-uri și numele torentelor. Toate subcolecțiile au fost deduplicate mai întâi față de colecția principală, deși fișierele JSON „upload_records” conțin încă multe referințe la fișierele originale. Fișierele non-carte au fost, de asemenea, eliminate din majoritatea subcolecțiilor și sunt de obicei <em>nu</em> notate în „upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Multe subcolecții în sine sunt compuse din sub-sub-colecții (de exemplu, din surse originale diferite), care sunt reprezentate ca directoare în câmpurile „filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Subcolecțiile sunt:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcolecție"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Note"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "răsfoire"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "căutare"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De la <a %(a_href)s>aaaaarg.fail</a>. Pare a fi destul de complet. De la voluntarul nostru „cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "De la un torent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Are o suprapunere destul de mare cu colecțiile existente de lucrări, dar foarte puține potriviri MD5, așa că am decis să o păstrăm complet."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape de <q>iRead eBooks</q> (= fonetic <q>ai rit i-books</q>; airitibooks.com), realizat de voluntarul <q>j</q>. Corespunde cu metadata <q>airitibooks</q> în <a %(a1)s><q>Alte scrape-uri de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Dintr-o colecție <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parțial din sursa originală, parțial de la the-eye.eu, parțial din alte oglinzi."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "De pe un site privat de torrente de cărți, <a %(a_href)s>Bibliotik</a> (adesea denumit „Bib”), ale cărui cărți au fost grupate în torrente după nume (A.torrent, B.torrent) și distribuite prin the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "De la voluntarul nostru „bpb9v”. Pentru mai multe informații despre <a %(a_href)s>CADAL</a>, consultați notele din <a %(a_duxiu)s>pagina setului de date DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mai multe de la voluntarul nostru „bpb9v”, în principal fișiere DuXiu, precum și un folder „WenQu” și „SuperStar_Journals” (SuperStar este compania din spatele DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "De la voluntarul nostru „cgiym”, texte chinezești din diverse surse (reprezentate ca subdirectoare), inclusiv de la <a %(a_href)s>China Machine Press</a> (un important editor chinez)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Colecții non-chinezești (reprezentate ca subdirectoare) de la voluntarul nostru „cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape de cărți despre arhitectura chineză, realizat de voluntarul <q>cm</q>: <q>Am obținut-o exploatând o vulnerabilitate de rețea la editură, dar acea breșă a fost închisă între timp</q>. Corespunde cu metadata <q>chinese_architecture</q> în <a %(a1)s><q>Alte scrape-uri de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Cărți de la editura academică <a %(a_href)s>De Gruyter</a>, colectate din câteva torrente mari."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape de pe <a %(a_href)s>docer.pl</a>, un site polonez de partajare de fișiere axat pe cărți și alte lucrări scrise. Scrape realizat la sfârșitul anului 2023 de voluntarul „p”. Nu avem metadate bune de pe site-ul original (nici măcar extensii de fișiere), dar am filtrat fișierele asemănătoare cărților și am reușit adesea să extragem metadate din fișierele în sine."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direct de la DuXiu, colectate de voluntarul „w”. Doar cărțile recente DuXiu sunt disponibile direct prin ebooks, așa că majoritatea acestora trebuie să fie recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Restul fișierelor DuXiu de la voluntarul „m”, care nu erau în formatul proprietar PDG DuXiu (principalul <a %(a_href)s>set de date DuXiu</a>). Colectate din multe surse originale, din păcate fără a păstra acele surse în calea fișierului."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape de cărți erotice, realizat de voluntarul <q>do no harm</q>. Corespunde cu metadata <q>hentai</q> în <a %(a1)s><q>Alte scrape-uri de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Colecție scrape de la un editor japonez de Manga de către voluntarul „t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arhive judiciare selectate din Longquan</a>, furnizate de voluntarul „c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape de pe <a %(a_href)s>magzdb.org</a>, un aliat al Library Genesis (este legat pe pagina principală libgen.rs) dar care nu a dorit să furnizeze fișierele direct. Obținut de voluntarul „p” la sfârșitul anului 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Diverse încărcări mici, prea mici pentru a fi subcolecții proprii, dar reprezentate ca directoare."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebook-uri de la AvaxHome, un site rusesc de partajare de fișiere."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arhivă de ziare și reviste. Corespunde cu metadata <q>newsarch_magz</q> în <a %(a1)s><q>Alte scrape-uri de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape de la <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Colecția voluntarului „o” care a colectat cărți poloneze direct de pe site-urile de lansare originale („scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Colecții combinate de la <a %(a_href)s>shuge.org</a> de către voluntarii „cgiym” și „woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>„Biblioteca Imperială din Trantor”</a> (numită după biblioteca fictivă), extrasă în 2022 de voluntarul „t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-colecții (reprezentate ca directoare) de la voluntarul „woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (de <a %(a_sikuquanshu)s>Dizhi(迪志)</a> în Taiwan), mebook (mebook.cc, 我的小书屋, camera mea mică de cărți — woz9ts: „Acest site se concentrează în principal pe partajarea fișierelor ebook de înaltă calitate, unele dintre ele fiind tipărite de proprietar însuși. Proprietarul a fost <a %(a_arrested)s>arestat</a> în 2019 și cineva a făcut o colecție de fișiere pe care le-a partajat.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Restul fișierelor DuXiu de la voluntarul „woz9ts”, care nu erau în formatul proprietar PDG DuXiu (încă de convertit în PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrente de la Arhiva Annei"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Scrape Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library își are rădăcinile în comunitatea <a %(a_href)s>Library Genesis</a> și inițial a fost lansată cu datele lor. De atunci, s-a profesionalizat considerabil și are o interfață mult mai modernă. Prin urmare, sunt capabili să obțină mult mai multe donații, atât monetare pentru a continua îmbunătățirea site-ului, cât și donații de cărți noi. Au acumulat o colecție mare în plus față de Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Actualizare din februarie 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "La sfârșitul anului 2022, presupusii fondatori ai Z-Library au fost arestați, iar domeniile au fost confiscate de autoritățile din Statele Unite. De atunci, site-ul a început să reapară online. Nu se știe cine îl administrează în prezent."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Colecția constă din trei părți. Paginile de descriere originale pentru primele două părți sunt păstrate mai jos. Aveți nevoie de toate cele trei părți pentru a obține toate datele (cu excepția torrentelor înlocuite, care sunt tăiate pe pagina de torrente)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: prima noastră lansare. Aceasta a fost prima lansare a ceea ce atunci se numea „Pirate Library Mirror” („pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: a doua lansare, de data aceasta cu toate fișierele împachetate în fișiere .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: lansări noi incrementale, folosind formatul <a %(a_href)s>Containerele Arhivei Annei (AAC)</a>, acum lansate în colaborare cu echipa Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrente de la Arhiva Annei (metadate + conținut)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplu de înregistrare pe Arhiva Annei (colecția originală)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplu de înregistrare pe Arhiva Annei (colecția „zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Site principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domeniu Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Postare pe blog despre Lansarea 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Postare pe blog despre Lansarea 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Lansări Zlib (pagini de descriere originale)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Lansarea 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Oglinda inițială a fost obținută cu mare efort pe parcursul anilor 2021 și 2022. În acest moment este ușor depășită: reflectă starea colecției din iunie 2021. Vom actualiza aceasta în viitor. În prezent, ne concentrăm pe lansarea acestei prime versiuni."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Deoarece Library Genesis este deja păstrată cu torrente publice și este inclusă în Z-Library, am făcut o deduplicare de bază împotriva Library Genesis în iunie 2022. Pentru aceasta am folosit hash-uri MD5. Probabil există mult mai mult conținut duplicat în bibliotecă, cum ar fi mai multe formate de fișiere pentru aceeași carte. Acest lucru este greu de detectat cu precizie, așa că nu o facem. După deduplicare, rămânem cu peste 2 milioane de fișiere, totalizând puțin sub 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Colecția constă din două părți: un dump MySQL „.sql.gz” al metadatelor și cele 72 de fișiere torrent de aproximativ 50-100GB fiecare. Metadatele conțin datele raportate de site-ul Z-Library (titlu, autor, descriere, tip de fișier), precum și dimensiunea reală a fișierului și md5sum-ul pe care l-am observat, deoarece uneori acestea nu corespund. Se pare că există intervale de fișiere pentru care Z-Library însăși are metadate incorecte. De asemenea, este posibil să fi descărcat incorect fișiere în unele cazuri izolate, pe care vom încerca să le detectăm și să le remediem în viitor."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Fișierele torrent mari conțin datele reale ale cărților, cu ID-ul Z-Library ca nume de fișier. Extensiile fișierelor pot fi reconstruite folosind dump-ul de metadate."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Colecția este un amestec de conținut non-ficțiune și ficțiune (nu este separat ca în Library Genesis). Calitatea variază de asemenea foarte mult."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Această primă lansare este acum complet disponibilă. Rețineți că fișierele torrent sunt disponibile doar prin oglinda noastră Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Lansarea 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Am obținut toate cărțile care au fost adăugate la Z-Library între ultima noastră oglindă și august 2022. De asemenea, am revenit și am extras unele cărți pe care le-am ratat prima dată. În total, această nouă colecție are aproximativ 24TB. Din nou, această colecție este deduplicată față de Library Genesis, deoarece există deja torrente disponibile pentru acea colecție."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Datele sunt organizate similar cu prima lansare. Există un dump MySQL “.sql.gz” al metadatelor, care include și toate metadatele din prima lansare, înlocuind-o astfel. Am adăugat și câteva coloane noi:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: dacă acest fișier este deja în Library Genesis, fie în colecția de non-ficțiune, fie în colecția de ficțiune (potrivit md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: în ce torrent se află acest fișier."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: setat când nu am reușit să descărcăm cartea."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Am menționat acest lucru ultima dată, dar doar pentru a clarifica: “filename” și “md5” sunt proprietățile reale ale fișierului, în timp ce “filename_reported” și “md5_reported” sunt ceea ce am extras de la Z-Library. Uneori, aceste două nu sunt de acord una cu cealaltă, așa că le-am inclus pe ambele."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pentru această lansare, am schimbat collation-ul la “utf8mb4_unicode_ci”, care ar trebui să fie compatibil cu versiunile mai vechi de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Fișierele de date sunt similare cu ultima dată, deși sunt mult mai mari. Pur și simplu nu ne-am deranjat să creăm tone de fișiere torrent mai mici. “pilimi-zlib2-0-14679999-extra.torrent” conține toate fișierele pe care le-am ratat în ultima lansare, în timp ce celelalte torrente sunt toate noi intervale de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Actualizare %(date)s:</strong> Am făcut majoritatea torrentelor noastre prea mari, ceea ce a cauzat probleme clienților de torrent. Le-am eliminat și am lansat noi torrente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Actualizare %(date)s:</strong> Încă erau prea multe fișiere, așa că le-am împachetat în fișiere tar și am lansat din nou noi torrente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addendum la Lansarea 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Acesta este un singur fișier torrent suplimentar. Nu conține nicio informație nouă, dar are unele date care pot dura ceva timp pentru a fi calculate. Acest lucru îl face convenabil de avut, deoarece descărcarea acestui torrent este adesea mai rapidă decât calcularea de la zero. În special, conține indexuri SQLite pentru fișierele tar, pentru utilizare cu <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Întrebări frecvente (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Ce este Arhiva Annei?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Arhiva Annei</span> este un proiect non-profit cu două obiective:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservare:</strong> Backup-ul tuturor cunoștințelor și culturii umanității.</li><li><strong>Acces:</strong> Punerea la dispoziție a acestor cunoștințe și culturi pentru oricine din lume.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tot <a %(a_code)s>codul</a> și <a %(a_datasets)s>datele</a> noastre sunt complet open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Conservare"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Preservăm cărți, lucrări, benzi desenate, reviste și multe altele, aducând aceste materiale din diverse <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteci de umbră</a>, biblioteci oficiale și alte colecții într-un singur loc. Toate aceste date sunt păstrate pentru totdeauna prin facilitarea duplicării lor în masă — folosind torrente — rezultând în multe copii în întreaga lume. Unele biblioteci de umbră fac deja acest lucru singure (de exemplu, Sci-Hub, Library Genesis), în timp ce Arhiva Annei „eliberează” alte biblioteci care nu oferă distribuție în masă (de exemplu, Z-Library) sau nu sunt deloc biblioteci de umbră (de exemplu, Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Această distribuție largă, combinată cu codul open-source, face ca site-ul nostru să fie rezistent la eliminări și asigură păstrarea pe termen lung a cunoștințelor și culturii umanității. Aflați mai multe despre <a href=\"/datasets\">seturile noastre de date</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Estimăm că am păstrat aproximativ <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% din cărțile lumii</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Acces"

#, fuzzy
msgid "page.home.access.text"
msgstr "Lucrăm cu parteneri pentru a face colecțiile noastre ușor și gratuit accesibile pentru oricine. Credem că toată lumea are dreptul la înțelepciunea colectivă a umanității. Și <a %(a_search)s>nu în detrimentul autorilor</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Descărcări pe oră în ultimele 30 de zile. Medie pe oră: %(hourly)s. Medie zilnică: %(daily)s."

msgid "page.about.text2"
msgstr "Credem cu tărie în cursul liber al informației și în păstrarea cunoștințelor și a culturii. Cu acest motor de căutare, construim pe umerii giganților. Respectăm profund munca asiduă a oamenilor care au creat diverse librării clandestine și sperăm că acest motor de căutare le va lărgi raza de acțiune."

#, fuzzy
msgid "page.about.text3"
msgstr "Pentru a rămâne la curent cu progresul nostru, urmărește Anna pe <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> sau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pentru întrebări și feedback, te rugăm să o contactezi pe Anna la %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Cum pot ajuta?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Urmărește-ne pe <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, sau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Răspândește vestea despre Arhiva Annei pe Twitter, Reddit, Tiktok, Instagram, la cafeneaua sau biblioteca locală, sau oriunde te duci! Nu credem în restricționare — dacă suntem închiși, ne vom reapărea în altă parte, deoarece tot codul și datele noastre sunt complet open source.</li><li>3. Dacă poți, ia în considerare <a href=\"/donate\">donarea</a>.</li><li>4. Ajută la <a href=\"https://translate.annas-software.org/\">traducerea</a> site-ului nostru în diferite limbi.</li><li>5. Dacă ești inginer software, ia în considerare contribuția la <a href=\"https://annas-software.org/\">open source</a>, sau seedarea <a href=\"/datasets\">torrentelor</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Avem acum și un canal Matrix sincronizat la %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Dacă sunteți un cercetător în securitate, ne puteți folosi abilitățile atât pentru ofensivă, cât și pentru apărare. Consultați pagina noastră de <a %(a_security)s>Securitate</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Căutăm experți în plăți pentru comercianți anonimi. Ne puteți ajuta să adăugăm modalități mai convenabile de a dona? PayPal, WeChat, carduri cadou. Dacă cunoașteți pe cineva, vă rugăm să ne contactați."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Întotdeauna căutăm mai multă capacitate de server."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Puteți ajuta raportând problemele fișierelor, lăsând comentarii și creând liste chiar pe acest site. De asemenea, puteți ajuta <a %(a_upload)s>încărcând mai multe cărți</a> sau remediind problemele fișierelor sau formatarea cărților existente."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Creați sau ajutați la menținerea paginii Wikipedia pentru Arhiva Annei în limba dumneavoastră."

#, fuzzy
msgid "page.about.help.text11"
msgstr "Căutăm să plasăm reclame mici și de bun gust. Dacă doriți să faceți reclamă pe Arhiva Annei, vă rugăm să ne anunțați."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Ne-ar plăcea ca oamenii să configureze <a %(a_mirrors)s>oglinzi</a>, și vom sprijini financiar acest lucru."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pentru informații mai detaliate despre cum să deveniți voluntar, consultați pagina noastră <a %(a_volunteering)s>Voluntariat & Recompense</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "De ce sunt descărcările lente atât de lente?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Nu avem literalmente suficiente resurse pentru a oferi tuturor din lume descărcări de mare viteză, oricât de mult ne-am dori. Dacă un binefăcător bogat ar dori să ne ofere acest lucru, ar fi incredibil, dar până atunci, facem tot posibilul. Suntem un proiect non-profit care abia se poate susține prin donații."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Acesta este motivul pentru care am implementat două sisteme pentru descărcări gratuite, cu partenerii noștri: servere partajate cu descărcări lente și servere puțin mai rapide cu o listă de așteptare (pentru a reduce numărul de persoane care descarcă în același timp)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Avem, de asemenea, <a %(a_verification)s>verificare a browserului</a> pentru descărcările lente, deoarece altfel boții și scrapers-urile le vor abuza, făcând lucrurile și mai lente pentru utilizatorii legitimi."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Rețineți că, atunci când utilizați Tor Browser, este posibil să fie necesar să ajustați setările de securitate. La cea mai joasă dintre opțiuni, numită „Standard”, provocarea Cloudflare turnstile reușește. La opțiunile mai ridicate, numite „Mai sigur” și „Cel mai sigur”, provocarea eșuează."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Pentru fișiere mari, descărcările lente se pot întrerupe la mijloc. Vă recomandăm să folosiți un manager de descărcări (cum ar fi JDownloader) pentru a relua automat descărcările mari."

msgid "page.donate.faq.title"
msgstr "Donații FAQ"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Abonamentele se reînnoiesc automat?</div> Abonamentele <strong>nu</strong> se reînnoiesc automat. Vă puteți alătura oricât de mult sau scurt doriți."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Pot să-mi upgradez abonamentul sau să obțin mai multe abonamente?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Dispuneți de alte metode de plată?</div> Momentan nu. O mulțime de oameni nu își doresc ca astfel de arhive să existe, deci trebuie să avem grijă. Dacă ne poți ajuta să configurăm alte metode de plată (mai convenabile) în siguranță, te rugăm să ne contactezi la %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Ce înseamnă intervalele pe lună?</div> Puteți ajunge la partea inferioară a unui interval aplicând toate reducerile, cum ar fi alegerea unei perioade mai lungi de o lună."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Pe ce cheltuiți donațiile?</div> 100%% Sunt destinate păstrării și accesibilității cunoștințelor și culturii lumii. În prezent, le cheltuim mai ales pe servere, stocare și lățime de bandă. Niciun membru nu va primi bani personal. Oricum ar fi prea periculos."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Pot face o donație mare?</div> Ar fi uimitor! Pentru donații de peste o mie de dolari, te rugăm să ne contactezi direct la %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Pot face o donație fără a deveni membru?</div> Desigur. Acceptăm donații de orice sumă la această adresă Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Cum pot încărca cărți noi?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativ, le puteți încărca pe Z-Library <a %(a_upload)s>aici</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pentru încărcări mici (până la 10.000 de fișiere) vă rugăm să le încărcați atât pe %(first)s cât și pe %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Deocamdată, sugerăm încărcarea noilor cărți pe fork-urile Library Genesis. Iată un <a %(a_guide)s>ghid util</a>. Rețineți că ambele fork-uri pe care le indexăm pe acest site web trag din acest sistem de încărcare."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pentru Libgen.li, asigurați-vă că vă conectați mai întâi pe <a %(a_forum)s>forum-ul lor</a> cu numele de utilizator %(username)s și parola %(password)s, apoi reveniți la <a %(a_upload_page)s>pagina lor de încărcare</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Dacă adresa dvs. de email nu funcționează pe forumurile Libgen, vă recomandăm să folosiți <a %(a_mail)s>Proton Mail</a> (gratuit). De asemenea, puteți <a %(a_manual)s>solicita manual</a> activarea contului dvs."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Rețineți că mhut.org blochează anumite intervale de IP-uri, așa că ar putea fi necesar un VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pentru încărcări mari (peste 10.000 de fișiere) care nu sunt acceptate de Libgen sau Z-Library, vă rugăm să ne contactați la %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Pentru a încărca lucrări academice, vă rugăm să le încărcați și pe (în plus față de Library Genesis) <a %(a_stc_nexus)s>STC Nexus</a>. Ei sunt cea mai bună bibliotecă de umbră pentru lucrări noi. Nu i-am integrat încă, dar o vom face la un moment dat. Puteți folosi <a %(a_telegram)s>botul lor de încărcare pe Telegram</a> sau contactați adresa listată în mesajul lor fixat dacă aveți prea multe fișiere de încărcat în acest mod."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Cum pot solicita cărți?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "În acest moment, nu putem onora cererile de cărți."

#, fuzzy
msgid "page.request.forums"
msgstr "Te rugăm să faci solicitările pe forumurile Z-Library sau Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nu ne trimiteți cereri de cărți prin email."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Colectați metadate?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Da, colectăm."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Am descărcat 1984 de George Orwell, va veni poliția la ușa mea?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Nu vă faceți prea multe griji, sunt mulți oameni care descarcă de pe site-urile la care facem legătura, și este extrem de rar să aveți probleme. Totuși, pentru a rămâne în siguranță, vă recomandăm să folosiți un VPN (plătit) sau <a %(a_tor)s>Tor</a> (gratuit)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Cum îmi salvez setările de căutare?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selectați setările dorite, lăsați caseta de căutare goală, faceți clic pe „Căutare”, apoi marcați pagina folosind funcția de marcaj a browserului dvs."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Aveți o aplicație mobilă?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nu avem o aplicație mobilă oficială, dar puteți instala acest site web ca aplicație."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Apăsați pe meniul cu trei puncte din colțul din dreapta sus și selectați „Adăugați pe ecranul principal”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Apăsați pe butonul „Partajare” din partea de jos și selectați „Adăugați pe ecranul principal”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Aveți un API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Avem un API JSON stabil pentru membri, pentru a obține un URL de descărcare rapidă: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentația este inclusă în JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pentru alte cazuri de utilizare, cum ar fi iterarea prin toate fișierele noastre, construirea unei căutări personalizate și așa mai departe, recomandăm <a %(a_generate)s>generarea</a> sau <a %(a_download)s>descărcarea</a> bazelor noastre de date ElasticSearch și MariaDB. Datele brute pot fi explorate manual <a %(a_explore)s>prin fișiere JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Lista noastră de torrente brute poate fi descărcată și ca <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Întrebări frecvente despre torrente"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Aș dori să ajut la seed, dar nu am prea mult spațiu pe disc."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Utilizați <a %(a_list)s>generatorul de liste de torrente</a> pentru a genera o listă de torrente care au cea mai mare nevoie de torrenting, în limitele spațiului dvs. de stocare."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrentele sunt prea lente; pot descărca datele direct de la voi?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Da, consultați pagina <a %(a_llm)s>date LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Pot descărca doar un subset de fișiere, cum ar fi doar o anumită limbă sau un anumit subiect?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Răspuns scurt: nu ușor."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Răspuns lung:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Majoritatea torrentelor conțin fișierele direct, ceea ce înseamnă că puteți instrui clienții de torrent să descarce doar fișierele necesare. Pentru a determina ce fișiere să descărcați, puteți <a %(a_generate)s>genera</a> metadatele noastre sau <a %(a_download)s>descărca</a> bazele noastre de date ElasticSearch și MariaDB. Din păcate, un număr de colecții de torrente conțin fișiere .zip sau .tar la rădăcină, caz în care trebuie să descărcați întregul torrent înainte de a putea selecta fișiere individuale."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Tot avem <a %(a_ideas)s>câteva idei</a> pentru acest caz.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nu există încă instrumente ușor de utilizat pentru filtrarea torrentelor, dar suntem deschiși la contribuții."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Cum gestionați duplicatele în torrente?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Încercăm să menținem duplicarea sau suprapunerea minimă între torrentele din această listă, dar acest lucru nu poate fi întotdeauna realizat și depinde foarte mult de politicile bibliotecilor sursă. Pentru bibliotecile care își publică propriile torrente, nu este în mâinile noastre. Pentru torrentele lansate de Arhiva Annei, deduplicăm doar pe baza hash-ului MD5, ceea ce înseamnă că diferitele versiuni ale aceleași cărți nu sunt deduplicate."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Pot obține lista de torrente ca JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Da."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Nu văd PDF-uri sau EPUB-uri în torrente, doar fișiere binare? Ce fac?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Acestea sunt de fapt PDF-uri și EPUB-uri, doar că nu au o extensie în multe dintre torrentele noastre. Există două locuri în care puteți găsi metadatele pentru fișierele torrent, inclusiv tipurile/extensiile de fișiere:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Fiecare colecție sau lansare are propriile metadate. De exemplu, <a %(a_libgen_nonfic)s>torrentele Libgen.rs</a> au o bază de date de metadate corespunzătoare găzduită pe site-ul Libgen.rs. De obicei, facem legătura către resursele de metadate relevante din <a %(a_datasets)s>pagina dataset-ului</a> fiecărei colecții."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Vă recomandăm să <a %(a_generate)s>generați</a> sau să <a %(a_download)s>descărcați</a> bazele noastre de date ElasticSearch și MariaDB. Acestea conțin o mapare pentru fiecare înregistrare din Arhiva Annei către fișierele torrent corespunzătoare (dacă sunt disponibile), sub „torrent_paths” în JSON-ul ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "De ce clientul meu de torrent nu poate deschide unele dintre fișierele torrent / linkurile magnetice ale voastre?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Unii clienți de torrent nu suportă dimensiuni mari ale pieselor, pe care multe dintre torrentele noastre le au (pentru cele mai noi nu mai facem asta — chiar dacă este valid conform specificațiilor!). Așadar, încercați un alt client dacă întâmpinați această problemă sau faceți o plângere către producătorii clientului vostru de torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Aveți un program de divulgare responsabilă?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Îi încurajăm pe cercetătorii în securitate să caute vulnerabilități în sistemele noastre. Suntem mari susținători ai divulgării responsabile. Contactați-ne <a %(a_contact)s>aici</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "În prezent, nu putem oferi recompense pentru bug-uri, cu excepția vulnerabilităților care au <a %(a_link)s>potențialul de a compromite anonimatul nostru</a>, pentru care oferim recompense între 10.000 și 50.000 de dolari. Ne-ar plăcea să oferim un domeniu mai larg pentru recompensele de bug-uri în viitor! Vă rugăm să rețineți că atacurile de inginerie socială sunt în afara domeniului de aplicare."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Dacă sunteți interesat de securitatea ofensivă și doriți să ajutați la arhivarea cunoștințelor și culturii lumii, asigurați-vă că ne contactați. Există multe moduri în care puteți ajuta."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Există mai multe resurse despre Arhiva Annei?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blogul Annei</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizări regulate"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software-ul Annei</a> — codul nostru open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduceți pe Software-ul Annei</a> — sistemul nostru de traducere"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — despre date"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domenii alternative"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mai multe despre noi (vă rugăm să ajutați la actualizarea acestei pagini sau să creați una pentru propria limbă!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Cum raportez o încălcare a drepturilor de autor?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nu găzduim niciun material protejat prin drepturi de autor aici. Suntem un motor de căutare și, ca atare, indexăm doar metadate care sunt deja disponibile public. Când descărcați de la aceste surse externe, vă sugerăm să verificați legile din jurisdicția dvs. cu privire la ceea ce este permis. Nu suntem responsabili pentru conținutul găzduit de alții."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Dacă aveți plângeri despre ceea ce vedeți aici, cea mai bună opțiune este să contactați site-ul original. Încărcăm regulat modificările lor în baza noastră de date. Dacă chiar credeți că aveți o plângere validă DMCA la care ar trebui să răspundem, vă rugăm să completați <a %(a_copyright)s>formularul de reclamație DMCA / Drepturi de autor</a>. Luăm în serios plângerile dvs. și vă vom răspunde cât mai curând posibil."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Urăsc modul în care gestionați acest proiect!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "De asemenea, dorim să reamintim tuturor că tot codul și datele noastre sunt complet open source. Acest lucru este unic pentru proiecte ca al nostru — nu cunoaștem niciun alt proiect cu un catalog la fel de masiv care să fie complet open source. Îi încurajăm foarte mult pe cei care cred că gestionăm prost proiectul nostru să ia codul și datele noastre și să își creeze propria bibliotecă shadow! Nu spunem asta din răutate sau ceva de genul acesta — credem cu adevărat că ar fi minunat, deoarece ar ridica standardele pentru toată lumea și ar păstra mai bine moștenirea umanității."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Aveți un monitor de disponibilitate?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Vă rugăm să vedeți <a %(a_href)s>acest proiect excelent</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Cum pot dona cărți sau alte materiale fizice?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Vă rugăm să le trimiteți la <a %(a_archive)s>Internet Archive</a>. Ei le vor păstra corespunzător."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Cine este Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Tu ești Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Care sunt cărțile voastre preferate?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Iată câteva cărți care au o semnificație specială pentru lumea bibliotecilor de umbră și a conservării digitale:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Ați epuizat descărcările rapide pentru astăzi."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Deveniți membru pentru a utiliza descărcări rapide."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Acum acceptăm carduri cadou Amazon, carduri de credit și debit, criptomonede, Alipay și WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Baza de date completă"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Cărți, lucrări, reviste, benzi desenate, înregistrări de bibliotecă, metadate, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Căutare"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub a <a %(a_paused)s>suspendat</a> încărcarea de noi lucrări."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB este o continuare a Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Acces direct la %(count)s lucrări academice"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Deschide"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Dacă sunteți <a %(a_member)s>membru</a>, verificarea browserului nu este necesară."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Arhivă pe termen lung"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Seturile de date utilizate în Arhiva Annei sunt complet deschise și pot fi oglindite în masă folosind torrente. <a %(a_datasets)s>Aflați mai multe…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Puteți ajuta enorm prin seeding-ul torrentelor. <a %(a_torrents)s>Aflați mai multe…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seederi"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Date de antrenament LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Avem cea mai mare colecție de date text de înaltă calitate din lume. <a %(a_llm)s>Aflați mai multe…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Oglinzi: apel pentru voluntari"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Căutăm voluntari"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Fiind un proiect non-profit, open-source, căutăm mereu persoane care să ne ajute."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Dacă administrați un procesator de plăți anonim cu risc ridicat, vă rugăm să ne contactați. De asemenea, căutăm persoane interesate să plaseze reclame mici și de bun gust. Toate veniturile merg către eforturile noastre de conservare."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog-ul Annei ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Descărcări IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Toate linkurile de descărcare pentru acest fișier: <a %(a_main)s>Pagina principală a fișierului</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(este posibil să fie nevoie să încerci de mai multe ori cu IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Pentru a obține descărcări mai rapide și a sări peste verificările browserului, <a %(a_membership)s>deveniți membru</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Pentru oglindirea în masă a colecției noastre, consultați paginile <a %(a_datasets)s>Datasets</a> și <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Date LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Este bine cunoscut faptul că LLM-urile prosperă pe baza datelor de înaltă calitate. Avem cea mai mare colecție de cărți, lucrări, reviste etc. din lume, care sunt unele dintre cele mai calitative surse de text."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Scală și gamă unică"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Colecția noastră conține peste o sută de milioane de fișiere, inclusiv jurnale academice, manuale și reviste. Realizăm această scală prin combinarea unor depozite mari existente."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Unele dintre colecțiile noastre sursă sunt deja disponibile în masă (Sci-Hub și părți din Libgen). Alte surse le-am eliberat noi înșine. <a %(a_datasets)s>Datasets</a> arată o prezentare completă."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Colecția noastră include milioane de cărți, lucrări și reviste dinaintea erei e-book-urilor. Părți mari din această colecție au fost deja OCR-izate și au deja puține suprapuneri interne."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Cum putem ajuta"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Suntem capabili să oferim acces de mare viteză la colecțiile noastre complete, precum și la colecții nepublicate."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Acesta este un acces la nivel de întreprindere pe care îl putem oferi pentru donații în valoare de zeci de mii de dolari USD. Suntem, de asemenea, dispuși să schimbăm acest acces pentru colecții de înaltă calitate pe care nu le avem încă."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Vă putem rambursa dacă ne puteți oferi îmbogățirea datelor noastre, cum ar fi:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Eliminarea suprapunerilor (deduplicare)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extracția textului și a metadatelor"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Susțineți arhivarea pe termen lung a cunoștințelor umane, în timp ce obțineți date mai bune pentru modelul dumneavoastră!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contactați-ne</a> pentru a discuta cum putem colabora."

#, fuzzy
msgid "page.login.continue"
msgstr "Continuă"

#, fuzzy
msgid "page.login.please"
msgstr "Vă rugăm să <a %(a_account)s>vă conectați</a> pentru a vizualiza această pagină.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Arhiva Annei este temporar închisă pentru întreținere. Vă rugăm să reveniți într-o oră."

#, fuzzy
msgid "page.metadata.header"
msgstr "Îmbunătățiți metadatele"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Puteți ajuta la conservarea cărților prin îmbunătățirea metadatelor! Mai întâi, citiți fundalul despre metadate pe Arhiva Annei, apoi învățați cum să îmbunătățiți metadatele prin legătura cu Open Library și câștigați un abonament gratuit pe Arhiva Annei."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Fundal"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Când vizualizați o carte pe Arhiva Annei, puteți vedea diverse câmpuri: titlu, autor, editor, ediție, an, descriere, nume de fișier și altele. Toate aceste informații sunt numite <em>metadate</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Deoarece combinăm cărți din diverse <em>biblioteci sursă</em>, afișăm orice metadate sunt disponibile în acea bibliotecă sursă. De exemplu, pentru o carte pe care am obținut-o de la Library Genesis, vom afișa titlul din baza de date a Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Uneori, o carte este prezentă în <em>mai multe</em> biblioteci sursă, care ar putea avea câmpuri de metadate diferite. În acest caz, afișăm pur și simplu cea mai lungă versiune a fiecărui câmp, deoarece aceasta sperăm că conține cele mai utile informații! Vom afișa totuși celelalte câmpuri sub descriere, de exemplu ca „titlu alternativ” (dar numai dacă sunt diferite)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Extragem, de asemenea, <em>coduri</em> precum identificatori și clasificatori din biblioteca sursă. <em>Identificatorii</em> reprezintă în mod unic o anumită ediție a unei cărți; exemple sunt ISBN, DOI, Open Library ID, Google Books ID sau Amazon ID. <em>Clasificatorii</em> grupează împreună mai multe cărți similare; exemple sunt Dewey Decimal (DCC), UDC, LCC, RVK sau GOST. Uneori aceste coduri sunt legate explicit în bibliotecile sursă, iar alteori le putem extrage din numele fișierului sau descriere (în principal ISBN și DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Putem folosi identificatorii pentru a găsi înregistrări în <em>colecții doar de metadate</em>, cum ar fi OpenLibrary, ISBNdb sau WorldCat/OCLC. Există o filă specifică <em>metadate</em> în motorul nostru de căutare dacă doriți să răsfoiți acele colecții. Folosim înregistrările potrivite pentru a completa câmpurile de metadate lipsă (de exemplu, dacă lipsește un titlu) sau, de exemplu, ca „titlu alternativ” (dacă există un titlu existent)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Pentru a vedea exact de unde provin metadatele unei cărți, consultați fila <em>„Detalii tehnice”</em> de pe pagina unei cărți. Aceasta are un link către JSON-ul brut pentru acea carte, cu indicii către JSON-ul brut al înregistrărilor originale."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Pentru mai multe informații, consultați următoarele pagini: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> și <a %(a_example)s>Example metadata JSON</a>. În final, toate metadatele noastre pot fi <a %(a_generated)s>generate</a> sau <a %(a_downloaded)s>descărcate</a> ca baze de date ElasticSearch și MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Legătura cu Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Deci, dacă întâlniți un fișier cu metadate incorecte, cum ar trebui să-l reparați? Puteți merge la biblioteca sursă și urma procedurile acesteia pentru a repara metadatele, dar ce să faceți dacă un fișier este prezent în mai multe biblioteci sursă?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Există un identificator care este tratat special pe Arhiva Annei. <strong>Câmpul annas_archive md5 de pe Open Library întotdeauna suprascrie toate celelalte metadate!</strong> Să facem un pas înapoi și să învățăm despre Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library a fost fondată în 2006 de Aaron Swartz cu scopul de a crea „o pagină web pentru fiecare carte publicată vreodată”. Este un fel de Wikipedia pentru metadatele cărților: oricine poate să o editeze, este licențiată liber și poate fi descărcată în masă. Este o bază de date de cărți care este cel mai bine aliniată cu misiunea noastră — de fapt, Arhiva Annei a fost inspirată de viziunea și viața lui Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "În loc să reinventăm roata, am decis să redirecționăm voluntarii noștri către Open Library. Dacă vedeți o carte care are metadate incorecte, puteți ajuta în următorul mod:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Mergeți la <a %(a_openlib)s>site-ul Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Găsiți înregistrarea corectă a cărții. <strong>AVERTISMENT:</strong> asigurați-vă că selectați <strong>ediția</strong> corectă. În Open Library, aveți „opere” și „ediții”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "O „operă” ar putea fi „Harry Potter și Piatra Filosofală”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "O „ediție” ar putea fi:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Ediția din 1997, prima ediție publicată de Bloomsbery, cu 256 de pagini."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Ediția broșată din 2003, publicată de Raincoast Books, cu 223 de pagini."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Traducerea poloneză din 2000 „Harry Potter I Kamie Filozoficzn” de Media Rodzina, cu 328 de pagini."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Toate aceste ediții au ISBN-uri și conținuturi diferite, așa că asigurați-vă că selectați pe cea corectă!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Editați înregistrarea (sau creați-o dacă nu există) și adăugați cât mai multe informații utile! Sunteți aici acum, așa că ar fi bine să faceți înregistrarea cu adevărat uimitoare."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sub „Numere de identificare” selectați „Arhiva Annei” și adăugați MD5-ul cărții din Arhiva Annei. Acesta este șirul lung de litere și cifre după „/md5/” în URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Încercați să găsiți alte fișiere în Arhiva Annei care se potrivesc cu această înregistrare și adăugați-le și pe acelea. În viitor, le putem grupa ca duplicate pe pagina de căutare a Arhivei Annei."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Când ați terminat, notați URL-ul pe care tocmai l-ați actualizat. Odată ce ați actualizat cel puțin 30 de înregistrări cu MD5-uri din Arhiva Annei, trimiteți-ne un <a %(a_contact)s>email</a> și trimiteți-ne lista. Vă vom oferi un abonament gratuit pentru Arhiva Annei, astfel încât să puteți face această muncă mai ușor (și ca mulțumire pentru ajutorul dvs.). Acestea trebuie să fie editări de înaltă calitate care adaugă cantități substanțiale de informații, altfel cererea dvs. va fi respinsă. Cererea dvs. va fi, de asemenea, respinsă dacă oricare dintre editări este anulată sau corectată de moderatorii Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Rețineți că acest lucru funcționează doar pentru cărți, nu pentru lucrări academice sau alte tipuri de fișiere. Pentru alte tipuri de fișiere, recomandăm în continuare găsirea bibliotecii sursă. Poate dura câteva săptămâni pentru ca modificările să fie incluse în Arhiva Annei, deoarece trebuie să descărcăm cea mai recentă descărcare de date Open Library și să regenerăm indexul nostru de căutare."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Oglinzi: apel pentru voluntari"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Pentru a crește reziliența Arhivei Annei, căutăm voluntari pentru a rula oglinzi."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Căutăm următoarele:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Rulați codul sursă deschis al Arhivei Annei și actualizați regulat atât codul, cât și datele."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Versiunea dvs. este clar distinsă ca o oglindă, de exemplu, „Arhiva lui Bob, o oglindă a Arhivei Annei”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Sunteți dispus să vă asumați riscurile asociate cu această muncă, care sunt semnificative. Aveți o înțelegere profundă a securității operaționale necesare. Conținutul <a %(a_shadow)s>acestor</a> <a %(a_pirate)s>postări</a> vă este evident."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Sunteți dispus să contribuiți la <a %(a_codebase)s>codul nostru sursă</a> — în colaborare cu echipa noastră — pentru a face acest lucru posibil."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inițial, nu îți vom oferi acces la descărcările serverului nostru partener, dar dacă lucrurile merg bine, putem împărtăși asta cu tine."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Cheltuieli de găzduire"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Suntem dispuși să acoperim cheltuielile de găzduire și VPN, inițial până la 200 USD pe lună. Aceasta este suficient pentru un server de căutare de bază și un proxy protejat de DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Vom plăti pentru găzduire doar după ce aveți totul configurat și ați demonstrat că puteți menține arhiva actualizată cu actualizări. Aceasta înseamnă că va trebui să plătiți din buzunar pentru primele 1-2 luni."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Timpul tău nu va fi compensat (și nici al nostru), deoarece aceasta este muncă pur voluntară."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Dacă vă implicați semnificativ în dezvoltarea și operațiunile muncii noastre, putem discuta despre împărțirea unei părți mai mari din veniturile din donații cu dvs., pentru a le folosi după cum este necesar."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Începeți"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Vă rugăm <strong>nu ne contactați</strong> pentru a cere permisiunea sau pentru întrebări de bază. Acțiunile vorbesc mai tare decât cuvintele! Toate informațiile sunt acolo, așa că mergeți înainte și configurați-vă oglinda."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Simțiți-vă liber să postați tichete sau cereri de fuziune pe Gitlab-ul nostru când întâmpinați probleme. Este posibil să trebuiască să construim unele funcții specifice oglinzii împreună cu dvs., cum ar fi rebranding-ul de la „Arhiva Annei” la numele site-ului dvs., (inițial) dezactivarea conturilor de utilizator sau legarea înapoi la site-ul nostru principal de pe paginile de cărți."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Odată ce aveți oglinda funcțională, vă rugăm să ne contactați. Ne-ar plăcea să vă revizuim securitatea operațională și, odată ce aceasta este solidă, vom face legătura cu oglinda dvs. și vom începe să lucrăm mai strâns împreună cu dvs."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Mulțumim anticipat oricui este dispus să contribuie în acest fel! Nu este pentru cei slabi de inimă, dar ar consolida longevitatea celei mai mari biblioteci cu adevărat deschise din istoria umanității."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Descarcă de pe site-ul partener"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Descărcările lente sunt disponibile doar prin site-ul oficial. Vizitați %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Descărcările lente nu sunt disponibile prin VPN-urile Cloudflare sau de la adresele IP Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Vă rugăm să așteptați <span %(span_countdown)s>%(wait_seconds)s</span> secunde pentru a descărca acest fișier."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Utilizați următorul URL pentru a descărca: <a %(a_download)s>Descarcă acum</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Mulțumim pentru așteptare, acest lucru menține site-ul accesibil gratuit pentru toată lumea! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Avertisment: au fost multe descărcări de la adresa dvs. IP în ultimele 24 de ore. Descărcările ar putea fi mai lente decât de obicei."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descărcări de la adresa dvs. IP în ultimele 24 de ore: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Dacă utilizați un VPN, o conexiune la internet partajată sau ISP-ul dvs. partajează IP-uri, acest avertisment ar putea fi din cauza asta."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Pentru a oferi tuturor oportunitatea de a descărca fișiere gratuit, trebuie să așteptați înainte de a putea descărca acest fișier."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Simțiți-vă liber să continuați să navigați în Arhiva Annei într-o filă diferită în timp ce așteptați (dacă browserul dvs. suportă reîmprospătarea filelor de fundal)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Simțiți-vă liber să așteptați încărcarea mai multor pagini de descărcare în același timp (dar vă rugăm să descărcați doar un fișier la un moment dat pe server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Odată ce obțineți un link de descărcare, acesta este valabil pentru câteva ore."

msgid "layout.index.header.title"
msgstr "Arhiva Annei"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Înregistrare în Arhiva Annei"

#, fuzzy
msgid "page.scidb.download"
msgstr "Descărcare"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Pentru a sprijini accesibilitatea și conservarea pe termen lung a cunoștințelor umane, deveniți <a %(a_donate)s>membru</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Ca bonus, 🧬&nbsp;SciDB se încarcă mai repede pentru membri, fără nicio limită."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Nu funcționează? Încercați <a %(a_refresh)s>reîmprospătarea</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Previzualizare indisponibilă momentan. Descărcați fișierul de la <a %(a_path)s>Arhiva Annei</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB este o continuare a Sci-Hub, cu interfața sa familiară și vizualizarea directă a fișierelor PDF. Introduceți DOI-ul pentru a vizualiza."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Avem întreaga colecție Sci-Hub, precum și lucrări noi. Majoritatea pot fi vizualizate direct cu o interfață familiară, similară cu Sci-Hub. Unele pot fi descărcate prin surse externe, caz în care afișăm linkuri către acestea."

msgid "page.search.title.results"
msgstr "%(search_input)s - Căutare"

msgid "page.search.title.new"
msgstr "Căutare nouă"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Includeți doar"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Excludeți"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Necorespunzător"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Descarcă"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Articole de jurnal"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Împrumut Digital"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadate"

msgid "common.search.placeholder"
msgstr "Caută titlu, autor, limbă, tip de fișier, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Caută"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Setări de căutare"

#, fuzzy
msgid "page.search.submit"
msgstr "Căutare"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Căutarea a durat prea mult, ceea ce este comun pentru interogări largi. Numărul de filtre poate să nu fie precis."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Căutarea a durat prea mult, ceea ce înseamnă că s-ar putea să vedeți rezultate inexacte. Uneori <a %(a_reload)s>reîncărcarea</a> paginii ajută."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Afișare"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Listă"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avansat"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Caută descrieri și comentarii de metadate"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Adăugați câmp de căutare specific"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(caută câmp specific)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Anul publicării"

msgid "page.search.filters.content.header"
msgstr "Conținut"

msgid "page.search.filters.filetype.header"
msgstr "Tip de fișier"

#, fuzzy
msgid "page.search.more"
msgstr "mai mult…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Acces"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Sursă"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "preluat și open-sourced de AA"

msgid "page.search.filters.language.header"
msgstr "Limbă"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordonează după"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Cel mai relevant"

msgid "page.search.filters.sorting.newest"
msgstr "Cel mai nou"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(anul publicării)"

msgid "page.search.filters.sorting.oldest"
msgstr "Cel mai vechi"

msgid "page.search.filters.sorting.largest"
msgstr "Cel mai mare"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(dimensiunea fișierului)"

msgid "page.search.filters.sorting.smallest"
msgstr "Cel mai mic"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(cod sursă deschis)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aleatoriu"

msgid "page.search.header.update_info"
msgstr "Indexul de căutare este updatat lunar. Momentan include înregistrări până la %(last_data_refresh_date)s. Pentru mai multă informație tehnică, vezi %(link_open_tag)spagina seturilor de date</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Pentru a explora indexul de căutare după coduri, utilizați <a %(a_href)s>Exploratorul de Coduri</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Tastați în casetă pentru a căuta în catalogul nostru de %(count)s fișiere descărcabile direct, pe care le <a %(a_preserve)s>păstrăm pentru totdeauna</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "De fapt, oricine poate ajuta la conservarea acestor fișiere prin seeding-ul <a %(a_torrents)s>listei noastre unificate de torrente</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "În prezent, avem cel mai cuprinzător catalog deschis de cărți, lucrări și alte opere scrise din lume. Oglindim Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>și altele</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Dacă găsești alte „biblioteci din umbră” pe care ar trebui să le oglindim, sau dacă ai întrebări, te rugăm să ne contactezi la %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Pentru cereri DMCA / drepturi de autor <a %(a_copyright)s>faceți clic aici</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Sfat: folosiți scurtăturile de la tastatură „/” (focus căutare), „enter” (căutare), „j” (sus), „k” (jos), „<” (pagina anterioară), „>” (pagina următoare) pentru o navigare mai rapidă."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Căutați lucrări?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Tastați în casetă pentru a căuta în catalogul nostru de %(count)s lucrări academice și articole de jurnal, pe care le <a %(a_preserve)s>păstrăm pentru totdeauna</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Tastați în casetă pentru a căuta fișiere în biblioteci digitale de împrumut."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Acest index de căutare include în prezent metadate din biblioteca de Împrumut Digital Controlat a Internet Archive. <a %(a_datasets)s>Mai multe despre seturile noastre de date</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Pentru mai multe biblioteci digitale de împrumut, vedeți <a %(a_wikipedia)s>Wikipedia</a> și <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Tastați în casetă pentru a căuta metadate din biblioteci. Acest lucru poate fi util când <a %(a_request)s>solicitați un fișier</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Acest index de căutare include în prezent metadate din diverse surse de metadate. <a %(a_datasets)s>Mai multe despre seturile noastre de date</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Pentru metadate, afișăm înregistrările originale. Nu facem nicio fuziune a înregistrărilor."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Există multe, multe surse de metadate pentru opere scrise în întreaga lume. <a %(a_wikipedia)s>Această pagină Wikipedia</a> este un bun început, dar dacă știți alte liste bune, vă rugăm să ne anunțați."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Tastați în casetă pentru a căuta."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Acestea sunt înregistrări de metadate, <span %(classname)s>nu</span> fișiere descărcabile."

msgid "page.search.results.error.header"
msgstr "Eroare la căutare."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Încercați să <a %(a_reload)s>reîncărcați pagina</a>. Dacă problema persistă, vă rugăm să ne trimiteți un email la %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nu au fost găsite fișiere.</span> Încearcă termeni și filtre mai puține sau diferite."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Uneori acest lucru se întâmplă incorect când serverul de căutare este lent. În astfel de cazuri, <a %(a_attrs)s>reîncărcarea</a> poate ajuta."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Am găsit potriviri în: %(in)s. Puteți face referire la URL-ul găsit acolo când <a %(a_request)s>solicitați un fișier</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Articole de jurnal (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Împrumut Digital (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadate (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Rezultate %(from)s-%(to)s (%(total)s total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ potriviri parțiale"

msgid "page.search.results.partial"
msgstr "%(num)d potriviri parțiale"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariat și recompense"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Arhiva Annei se bazează pe voluntari ca tine. Acceptăm toate nivelurile de angajament și avem două categorii principale de ajutor pe care le căutăm:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Muncă ușoară de voluntariat:</span> dacă poți aloca doar câteva ore din când în când, există încă multe moduri în care poți ajuta. Răsplătim voluntarii consecvenți cu <span %(bold)s>🤝 abonamente la Arhiva Annei</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Muncă voluntară intensă (recompense între USD$50-USD$5,000):</span> dacă poți dedica mult timp și/sau resurse misiunii noastre, ne-ar plăcea să lucrăm mai îndeaproape cu tine. În cele din urmă, poți să te alături echipei interne. Deși avem un buget restrâns, putem acorda <span %(bold)s>💰 recompense monetare</span> pentru munca cea mai intensă."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Dacă nu poți să-ți oferi timpul ca voluntar, ne poți ajuta mult prin <a %(a_donate)s>donarea de bani</a>, <a %(a_torrents)s>seeding-ul torrentelor noastre</a>, <a %(a_uploading)s>încărcarea de cărți</a> sau <a %(a_help)s>spunând prietenilor tăi despre Arhiva Annei</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Companii:</span> oferim acces direct de mare viteză la colecțiile noastre în schimbul unei donații la nivel de întreprindere sau în schimbul unor colecții noi (de exemplu, scanări noi, datasets OCR, îmbogățirea datelor noastre). <a %(a_contact)s>Contactează-ne</a> dacă ești interesat. Vezi și <a %(a_llm)s>pagina noastră LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariat ușor"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Dacă ai câteva ore libere, poți ajuta în mai multe moduri. Asigură-te că te alături <a %(a_telegram)s>chatului voluntarilor pe Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Ca semn de apreciere, de obicei oferim 6 luni de „Bibliotecar Norocos” pentru realizări de bază și mai mult pentru munca voluntară continuă. Toate realizările necesită muncă de înaltă calitate — munca neglijentă ne dăunează mai mult decât ne ajută și o vom respinge. Te rugăm să <a %(a_contact)s>ne trimiți un email</a> când atingi o realizare."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Sarcină"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Realizare"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Răspândirea veștii despre Arhiva Annei. De exemplu, prin recomandarea de cărți pe AA, legături către postările noastre de pe blog sau, în general, direcționarea oamenilor către site-ul nostru."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s linkuri sau capturi de ecran."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Acestea ar trebui să arate cum informați pe cineva despre Arhiva Annei, iar acea persoană vă mulțumește."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Îmbunătățește metadata prin <a %(a_metadata)s>linking</a> cu Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Puteți folosi <a %(a_list)s >lista de probleme aleatorii de metadata</a> ca punct de plecare."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Asigurați-vă că lăsați un comentariu la problemele pe care le rezolvați, astfel încât alții să nu vă dubleze munca."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s linkuri ale înregistrărilor pe care le-ați îmbunătățit."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traducerea</a> site-ului web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Tradu complet o limbă (dacă nu era deja aproape de finalizare)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Îmbunătățește pagina Wikipedia pentru Arhiva Annei în limba ta. Include informații de pe pagina Wikipedia a AA în alte limbi și de pe site-ul nostru și blog. Adaugă referințe la AA pe alte pagini relevante."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link către istoricul editărilor care arată că ai făcut contribuții semnificative."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Îndeplinește cereri de cărți (sau lucrări, etc) pe forumurile Z-Library sau Library Genesis. Nu avem propriul nostru sistem de cereri de cărți, dar oglindim acele biblioteci, așa că îmbunătățirea lor face și Arhiva Annei mai bună."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s linkuri sau capturi de ecran ale cererilor pe care le-ați îndeplinit."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Sarcini mici postate pe <a %(a_telegram)s>chatul voluntarilor pe Telegram</a>. De obicei pentru membership, uneori pentru recompense mici."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Sarcini mici postate în grupul nostru de chat pentru voluntari."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depinde de sarcină."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Recompense"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Căutăm mereu persoane cu abilități solide de programare sau securitate ofensivă pentru a se implica. Puteți contribui semnificativ la păstrarea moștenirii umanității."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Ca mulțumire, oferim abonamente pentru contribuții solide. Ca o mulțumire imensă, oferim recompense financiare pentru sarcini deosebit de importante și dificile. Acestea nu ar trebui privite ca un înlocuitor pentru un loc de muncă, dar reprezintă un stimulent suplimentar și pot ajuta la acoperirea costurilor suportate."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Majoritatea codului nostru este open source, și vom cere același lucru pentru codul dumneavoastră atunci când acordăm recompense. Există câteva excepții pe care le putem discuta individual."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Recompensele sunt acordate primei persoane care finalizează o sarcină. Simțiți-vă liber să comentați pe un bilet de recompensă pentru a informa pe alții că lucrați la ceva, astfel încât ceilalți să se abțină sau să vă contacteze pentru a face echipă. Dar fiți conștienți că alții sunt încă liberi să lucreze la aceeași sarcină și să încerce să vă depășească. Totuși, nu acordăm recompense pentru muncă neglijentă. Dacă două contribuții de înaltă calitate sunt făcute aproape simultan (într-o zi sau două), putem alege să acordăm recompense ambelor, la discreția noastră, de exemplu 100%% pentru prima contribuție și 50%% pentru a doua contribuție (deci 150%% în total)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Pentru recompensele mai mari (în special recompensele pentru scraping), vă rugăm să ne contactați când ați completat ~5%% din sarcină și sunteți încrezători că metoda dumneavoastră va scala până la obiectivul complet. Va trebui să împărtășiți metoda cu noi pentru a putea oferi feedback. De asemenea, în acest fel putem decide ce să facem dacă sunt mai multe persoane care se apropie de o recompensă, cum ar fi acordarea acesteia mai multor persoane, încurajarea oamenilor să facă echipă, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVERTISMENT: sarcinile cu recompense mari sunt <span %(bold)s>dificile</span> — ar putea fi înțelept să începeți cu cele mai ușoare."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Accesați <a %(a_gitlab)s>lista noastră de probleme Gitlab</a> și sortați după „Prioritate etichetă”. Aceasta arată aproximativ ordinea sarcinilor care ne interesează. Sarcinile fără recompense explicite sunt încă eligibile pentru abonamente, în special cele marcate „Acceptat” și „Favoritul Annei”. Poate doriți să începeți cu un „Proiect de început”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Actualizări despre <a %(wikipedia_annas_archive)s>Arhiva Annei</a>, cea mai mare bibliotecă cu adevărat deschisă din istoria umanității."

msgid "layout.index.title"
msgstr "Arhiva Annei"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Cea mai mare bibliotecă open-source și open-data din lume. Oglindește Sci-Hub, Library Genesis, Z-Library și altele."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Căutați în Arhiva Annei"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Arhiva Annei are nevoie de ajutorul dumneavoastră!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Mulți încearcă să ne doboare, dar noi luptăm înapoi."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Dacă donați acum, primiți <strong>dublu</strong> numărul de descărcări rapide."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valabil până la sfârșitul acestei luni."

msgid "layout.index.header.nav.donate"
msgstr "Donează"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvarea cunoștințelor umane: un cadou grozav de sărbători!"

msgid "layout.index.header.banner.surprise"
msgstr "Surprindeți o persoană dragă, acordați-i un cont cu abonament."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Pentru a crește reziliența Arhivei Annei, căutăm voluntari pentru a rula oglinzi."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Cadoul perfect de Valentine’s!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Este disponibilă o metodă nouă pentru a dona: %(method_name)s. Te rugăm să iei în considerare să faci o %(donate_link_open_tag)sdonație</a> — nu este ușor să menținem acest site în stare funcțională, iar donația ta face diferența. Îți mulțumim enorm."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Organizăm o strângere de fonduri pentru <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">backup-ul</a> celei mai mari biblioteci de benzi desenate din lume. Mulțumim pentru sprijin! <a href=\"/donate\">Donați.</a> Dacă nu puteți dona, luați în considerare să ne sprijiniți spunându-le prietenilor și urmărindu-ne pe <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> sau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Descărcări recente:"

msgid "layout.index.header.nav.search"
msgstr "Caută"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Întrebări frecvente"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Îmbunătățiți metadatele"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariat & Recompense"

msgid "layout.index.header.nav.datasets"
msgstr "Seturi de date"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrente"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activitate"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorator de Coduri"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Date LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Acasă"

msgid "layout.index.header.nav.annassoftware"
msgstr "Software-ul Annei ↗"

msgid "layout.index.header.nav.translate"
msgstr "Tradu ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Autentificare / Inregistreaza-te"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Cont"

msgid "layout.index.footer.list1.header"
msgstr "Arhiva Annei"

msgid "layout.index.footer.list2.header"
msgstr "Contact"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / reclamații de copyright"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avansat"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Securitate"

msgid "layout.index.footer.list3.header"
msgstr "Alternative"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "neafiliat"

msgid "page.search.results.issues"
msgstr "❌ Acest fișier ar putea prezenta probleme."

msgid "page.search.results.fast_download"
msgstr "Descarcare rapida"

msgid "page.donate.copy"
msgstr "copiază"

msgid "page.donate.copied"
msgstr "copiat!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Anterior"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Următorul"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% din moștenirea scrisă a omenirii păstrat pentru totdeauna %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Seturi de date ▶ Fișiere ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Descarcă de la:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Avem multiple opțiuni de descărcare în caz că una din ele este indisponibilă. Toate conțin exact același fișier."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Ia în considerare faptul ca Arhiva Annei nu găzduiește nimic din conținutul de aici. Noi pur și simplu oferim linkuri la website-urile altor oameni. Dacă ești de părere că ai o plângere DMCA validă, te rugăm vizitează pagina %(about_link)sdespre</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Oglindă Anonimă #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Donează"

#~ msgid "page.donate.header"
#~ msgstr "Donează"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive (Arhiva Annei) este un proiect nonprofit, open-source condus în totalitate de voluntari. Acceptăm donații pentru a ne acoperi costurile, care includ găzduire, numele domeniilor, dezvoltare și altele."

#~ msgid "page.donate.text2"
#~ msgstr "Prin contribuțiile dumneavoastră suntem în stare să menținem acest site în stare funcțională, să-i îmbunătățim caracteristicile și să păstrăm mai multe colecții."

#~ msgid "page.donate.text3"
#~ msgstr "Donații recente: %(donations)s. Mulțumim tuturor pentru generozitate. Apreciem cu adevărat încrederea pe care ne-o acordați cu orice sumă de care vă puteți lipsi."

#~ msgid "page.donate.text4"
#~ msgstr "Pentru a dona, selectează metoda preferată dedesubt. Dacă întâmpini vreo problemă, te rugăm să ne contactezi la %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Card de credit/debit"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Crypto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Întrebări"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Du-te la %(link_open_tag)saceastă pagină</a> și urmează instrucțiunile, ori scanând codul Qr, ori apăsând link-ul “paypal.me”. Dacă nu funcționează, încearcă să reîncarci pagina, din moment ce asta ți-ar putea da un cont diferit."

#~ msgid "page.donate.cc.header"
#~ msgstr "Card de credit/debit"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Folosim Sendwyre pentru a depozita bani direct in portofelul nostru Bitcoin (BTC). Durează aproximativ 5 minute pentru a fi completat."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Această metodă presupune o tranzacție minimă de $30 și o taxă de aproximativ $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Pași:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copiază adresa portofelului nostru Bitcoin (BTC): %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Du-te pe %(link_open_tag)saceastă pagină</a> și fă click pe \"cumpără crypto instant\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Lipește adresa portofelului nostru și urmează instrucțiunile"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Crypto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(funcționează și pentru BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Te rugăm să folosești acest cont %(link_open_tag)sAlipay</a> pentru a trimite donația. Dacă nu funcționează, încearcă să reîncarci pagina, din moment ce asta îți poate da un cont diferit."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Această opțiune pentru donații este momentan indisponibilă. Te rugăm să revii mai târziu. Îți mulțumim că vrei să donezi, chiar apreciem acest lucru!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Te rugăm să folosești %(link_open_tag)saceastă pagină Pix</a> pentru a trimite donația. Dacă nu funcționează, încearcă să reîncarci pagina, din moment ce asta îți poate oferi un alt cont."

#~ msgid "page.donate.faq.header"
#~ msgstr "Întrebări adresate frecvent"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Arhiva Annei</span> este un proiect al cărui scop este să catalogheze toate cărțile existente prin agregarea datelor din diverse surse. De asemenea, măsurăm progresul umanității către a face toate aceste cărți ușor disponibile în format digital, prin “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteci clandestine</a>”. Află mai multe <a href=\"/about\">despre noi.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Carte (orice)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Acasă"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Seturi de date ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Nu a fost găsit"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” nu este un număr ISBN valid. ISBN-urile au 10 sau 13 caractere, fără a număra cratimele opționale. Toate caracterele trebuie să fie numere, cu excepția ultimului, care ar putea fi și “X”. Ultimul caracter este “cifra de verificare”, care trebuie să corespundă cu valoarea unei sume de control computate din alte numere. Trebuie și să se afle într-un interval valid, alocat de către Agenția Internațională ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Fișiere corespunzătoare în baza noastră de date:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Nu au fost găsite fișiere corespunzătoare în baza noastră de date."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Caută ▶ %(num)d+ rezultate pentru <span class=\"italic\">%(search_input)s</span> (în metadata bibliotecilor clandestine)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Caută ▶ %(num)d rezultate pentru <span class=\"italic\">%(search_input)s</span> (în metadata bibliotecilor clandestine)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Caută ▶ Eroare de cătuare pentru <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Caută ▶ Căutare nouă"

#~ msgid "page.donate.header.text3"
#~ msgstr "De asemenea, puteți face o donație fără a crea un cont (aceași metode de plată sunt acceptate pentru donațiile și abonamentele unice):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Faceți o donație anonimă unică (fără avantaje)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Selectați o opțiune de plată. Vă rugăm să luați în considerare utilizarea unei plăți bazate pe criptomonede %(bitcoin_icon)s, deoarece suportăm (mult) mai puține taxe."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Dacă ai deja valute crypto, acestea sunt adresele noastre."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Îți mulțumim enorm pentru ajutor! Acest proiect nu ar putea fi posibil fără tine."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Pentru a dona folosind PayPal, vom folosi PayPal Crypto, care ne permite să rămânem anonimi. Apreciem că v-ați acordat timp pentru a învăța cum să donați folosind această metodă, deoarece ne ajută foarte mult."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Urmați instrucțiunile pentru a cumpăra Bitcoin (BTC). Trebuie doar să cumpărați suma pe care doriți să o donați."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Introduceți adresa dvs. Bitcoin (BTC) ca destinatar și urmați instrucțiunile pentru a vă trimite donația:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Utilizați <a %(a_account)s>acest cont Alipay</a> pentru a vă trimite donația."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Utilizați <a %(a_account)s>acest cont Pix</a> pentru a vă trimite donația."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr ""

#~ msgid "page.donate.login"
#~ msgstr "Pentru a deveni membru, <a href=\"/login\">Conectați-vă sau Înregistrați-vă</a>. Dacă preferați să nu vă creați un cont, selectați „Fă o donație anonimă unică” mai sus. Mulțumesc pentru sprijin!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Acasă"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Despre"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donează"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Seturi de date"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog-ul Annei"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Software-ul Annei"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Tradu"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Copii ale conținutului %(libraries)s și altele."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Seturi de date ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Nu a fost găsit"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" nu pare a fi un DOI. Ar trebui să înceapă cu \"10.\" și să conțină \"/\"."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL canonic: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Acest fișier ar putea fi în %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Verificând fișiere din baza noastră de date:"

#~ msgid "page.doi.results.none"
#~ msgstr "Nu au fost găsite fișiere care să corespundă în baza noastră de date."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Pot contribui în alte moduri?</div> Da! Vezi pagina <a href=\"/about\">despre</a> în cadrul “Cum poți ajuta”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Nu-mi place că „monetizați” Arhiva Annei!</div> Dacă nu vă place modul în care ne desfășurăm proiectul, mergeți să rulați propria bibliotecă umbră! Toate codurile și datele noastre sunt open source, așa că nimic nu te oprește. ;)"

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Despre"

#~ msgid "page.about.header"
#~ msgstr "Despre"

#~ msgid "page.home.search.header"
#~ msgstr "Caută"

#~ msgid "page.home.search.intro"
#~ msgstr "Caută în catalogul nostru de biblioteci clandestine."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr "Anna’s Archive (Arhiva Annei) este un motor de căutare nonprofit și open-source pentru “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">biblioteci clandestine</a>”. A fost creat de <a href=\"http://annas-blog.org\">Anna</a>, care a simțit că exista o nevoie pentru un loc central în care să poți căuta cărți, articole, benzi desenate, reviste și alte documente."

#~ msgid "page.about.text4"
#~ msgstr "Dacă ai o plângere DMCA validă, du-te la finalul acestei pagini sau contactează-ne la %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Explorează cărți"

#~ msgid "page.home.explore.intro"
#~ msgstr "Acestea sunt o combinație de cărți populare și cărți care au o însemnătate specială pentru lumea bibliotecilor clandestine și a păstrării digitale."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Despre"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "aplicație pentru mobil"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Cum poți ajuta"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Donează suma totală de %(total)s folosind <a %(a_account)s>acest cont Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "doar luna aceasta!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub a <a %(a_closed)s>oprit</a> încărcarea de lucrări noi."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selectați o opțiune de plată. Oferim reduceri pentru plățile bazate pe criptomonede %(bitcoin_icon)s, deoarece suportăm (mult) mai puține taxe."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selectează o modalitate de plată. Deocamdată acceptăm doar plata prin criptomonede %(bitcoin_icon)s, deoarece procesorii de plată tradiționali refuză să lucreze cu noi."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nu putem accepta direct carduri de credit/debit, deoarece băncile nu vor să colaboreze cu noi. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Cu toate acestea, există mai multe modalități de a folosi carduri de credit/debit, utilizând celelalte metode de plată ale noastre:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Descărcări lente și externe"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descărcări"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Daca folosești cryptomonede pentru prima dată, recomandăm să folosești %(option1)s, %(option2)s sau %(option3)s pentru a cumpăra și a dona Bitcoin (criptomoneda originală și cea mai utilizată)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 de linkuri ale înregistrărilor pe care le-ai îmbunătățit."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 de linkuri sau capturi de ecran."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 de linkuri sau capturi de ecran ale cererilor pe care le-ai îndeplinit."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Dacă sunteți interesat să oglindiți aceste datasets pentru <a %(a_faq)s>arhivare</a> sau pentru <a %(a_llm)s>antrenarea LLM</a>, vă rugăm să ne contactați."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Dacă sunteți interesat să oglindiți acest set de date pentru <a %(a_archival)s>arhivare</a> sau <a %(a_llm)s>antrenament LLM</a>, vă rugăm să ne contactați."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Site principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informații despre țara ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Dacă sunteți interesat să oglindiți acest set de date pentru scopuri de <a %(a_archival)s>arhivare</a> sau <a %(a_llm)s>antrenare LLM</a>, vă rugăm să ne contactați."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Agenția Internațională ISBN eliberează periodic intervalele pe care le-a alocat agențiilor naționale ISBN. Din aceasta putem deduce cărei țări, regiuni sau grup lingvistic îi aparține acest ISBN. În prezent, folosim aceste date indirect, prin intermediul bibliotecii Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resurse"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultima actualizare: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Site-ul ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadate"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluzând „scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Inspirația noastră pentru colectarea metadatelor este obiectivul lui Aaron Swartz de a avea „o pagină web pentru fiecare carte publicată vreodată”, pentru care a creat <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Acest proiect a avut succes, dar poziția noastră unică ne permite să obținem metadate pe care ei nu le pot obține."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "O altă inspirație a fost dorința noastră de a ști <a %(a_blog)s>câte cărți există în lume</a>, astfel încât să putem calcula câte cărți mai avem de salvat."

#~ msgid "page.partner_download.text1"
#~ msgstr "Pentru a oferi tuturor oportunitatea de a descărca fișiere gratuit, trebuie să așteptați <strong>%(wait_seconds)s secunde</strong> înainte de a putea descărca acest fișier."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Reîmprospătare automată a paginii. Dacă ratați fereastra de descărcare, cronometrul va reporni, așa că reîmprospătarea automată este recomandată."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Descarcă acum"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Folosiți instrumente online pentru a converti între formate. De exemplu, pentru a converti între epub și pdf, folosiți <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descărcați fișierul (pdf sau epub sunt acceptate), apoi <a %(a_kindle)s>trimiteți-l la Kindle</a> folosind web, aplicația sau emailul. Instrumente utile: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Sprijiniți autorii: Dacă vă place acest conținut și vă permiteți, luați în considerare achiziționarea originalului sau sprijinirea directă a autorilor."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Sprijiniți bibliotecile: Dacă acest material este disponibil la biblioteca dvs. locală, luați în considerare împrumutul gratuit de acolo."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nu este disponibil direct în masă, doar în semi-masă în spatele unui paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Arhiva Annei gestionează o colecție de <a %(isbndb)s>metadate ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb este o companie care extrage metadate ISBN din diverse librării online. Arhiva Annei a realizat copii de siguranță ale metadatelor cărților din ISBNdb. Aceste metadate sunt disponibile prin intermediul Arhivei Annei (deși momentan nu sunt incluse în căutare, cu excepția cazului în care căutați explicit un număr ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Pentru detalii tehnice, vedeți mai jos. La un moment dat, putem folosi aceste date pentru a determina care cărți lipsesc încă din bibliotecile shadow, pentru a prioritiza cărțile pe care să le găsim și/sau scanăm."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Postarea noastră pe blog despre aceste date"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Extracție ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "În prezent, avem un singur torrent, care conține un fișier JSON Lines de 4,4GB comprimat cu gzip (20GB necomprimat): „isbndb_2022_09.jsonl.gz”. Pentru a importa un fișier „.jsonl” în PostgreSQL, puteți folosi ceva de genul <a %(a_script)s>acestui script</a>. Puteți chiar să-l pipetați direct folosind ceva de genul %(example_code)s pentru a-l decomprima în timp real."

#~ msgid "page.donate.wait"
#~ msgstr "Vă rugăm să așteptați cel puțin <span %(span_hours)s>două ore</span> (și să reîmprospătați această pagină) înainte de a ne contacta."

#~ msgid "page.codes.search_archive"
#~ msgstr "Caută în Arhiva Annei pentru „%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donează folosind Alipay sau WeChat. Poți alege între acestea pe pagina următoare."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Răspândește vestea despre Arhiva Annei pe rețelele sociale și forumurile online, recomandând cărți sau liste pe AA, sau răspunzând la întrebări."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Colecția de ficțiune a divergat, dar încă are <a %(libgenli)s>torrente</a>, deși nu a fost actualizată din 2022 (avem totuși descărcări directe)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Arhiva Annei și Libgen.li gestionează colaborativ colecții de <a %(comics)s>benzi desenate</a> și <a %(magazines)s>reviste</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nu există torrente pentru colecțiile de ficțiune rusă și documente standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Nu există torrente disponibile pentru conținutul suplimentar. Torrentele de pe site-ul Libgen.li sunt oglinzi ale altor torrente listate aici. Singura excepție sunt torrentele de ficțiune care încep de la %(fiction_starting_point)s. Torrentele de benzi desenate și reviste sunt lansate ca o colaborare între Arhiva Annei și Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Dintr-o colecție <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> originea exactă este neclară. Parțial de la the-eye.eu, parțial din alte surse."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegramă"

