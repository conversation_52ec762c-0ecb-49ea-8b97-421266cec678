��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b G  �d �  h 4   �i w  j �  ~k �  `n   &q �  <s �   u �   �u O   &v |   vv Y  �v �  My M  I{ �  �| J   �  h� �  %� �  �� !  F� �  h� d  3� N   �� .  � �   � �  �� 2   �� �  ʓ O   �� +   �    � "   0� s   S� +   Ǘ    � F   � '   S� Z   {�    ֘ b   �� �  W� }  � e   �� V   �    Y�    r�    ��    �� 9   ȝ    �    �    !�    =� /   D�    t� +   }� 	   �� 
   ��    �� ,   � ;   � 1   J� S  |� �  Т �   �� 4   p� �  �� �   z� v  � �   �� 4   �� �   �� 4   {�   �� 9   �� `  � 4   R� �  �� H   w� }   ��    >� �  M� �  � k  Ĵ   0� )   �� d   ڸ }   ?� �   �� D   �� &   �� �   !� T   Ż b   � B   }�    �� �   ͼ �  y� `    � �   �� !  >� w   `� }  �� }   V� a  �� �   6� �   � s   �� �   P� �   D� O   � P  n�   �� �   �� e   T� �   ��    D�   Q� �  S� O   �    _�   n� 5  t�    �� �  �� �   �� 
  ]�   k� �   {� �  m� �  U� l   �� s   S� u   �� �   =� s   �� �   M� I   �� �   '� {   �� Z   S�    �� �   �� n   g�   �� �   �� *   �� e  �� R  6� p  ��   �� �   �   �� �   � �   �� �   �� �   ^� �  E� F  �� )  5�    _� �   n� *  2� �   ]� �  Z� �   �� �   �� �   I� �    �   � �  c g  , $   � s   � �   - �    �  � �   �
 y   O �   � H   Q   � 3  �   � !   � �    �   � 7  � a  � M   d  j -  � @   � �  > '  � �  $"   �# �   ' `  �( �   �) �   �*    ;+   J+ �  Z, W  L. k  �0 O  2 �   `3    L4 %  [4 �  �6 =  )8    g9 �   x9 �  ]:   �; �  = �   �? �   �@    A    /A �  PC    NE \  fE h  �F �  ,H 8  K �  IL h   N s   �N l  �N 7   hP    �P �  �P �  tR �  (T <   �U O   V �  dV �   KY �   �Y �   �Z   F[ )   d]    �] &  �]   �_ �   �` ^   �a �   �a .   �b C  �b X  �c =   Ue    �e (   �e N   �e c   f `   �f �   �f �   pg "   ;h F   ^h c   �h �   	i G   �i U   �i D   8j �   }j 
   /k #   :k `  ^k �  �l $  dn j  �o �   �p   �q �  �r    Yt C  wt �   �u �   }v �  Pw   Iy �  f{ �  ~ U  �� O   �� Q   F� 4   �� �   ̓ �  Ƅ *  �� �  � �  �� u  �� ]  � o  i� I   ٓ �  #� �  ֕ �  z�    o� L  ��   К �   � �  ��   5� 
   Q� 9   _� �   ��    6� k  C�   �� #  �� #  ܩ    �    � w   � p  �� O   	� �   Y� w  ܯ O  T� �   �� L  q�    �� �  >�    � �  ��    �� �  �� �  b� �  � �   ��    �� :  �� s  #�    �� �  �� �  �� U  ��   �� ^  �� �  Z� O   *� 4  z�   �� �   �� X   c� o   ��    ,� B   ;� )   ~� 1   �� 4   ��    � �   /� �  
� s  �� �  
� $  ��    � �  �   �� �  � @  �� �  � ^  ��    '� n  6�    ��     �� g  �� =  ?�   } c  �    � �  	 �  � �  " �  � W  �   � �  � -   �   � �    �  � F  ~" �   �$ �   �% 
  �&   �'    �) !  �) c   	+ ~   m+     �+ T   
, P   b, �  �, #  �/ �  �1 >   d5 �  �5 �   �9    �: �   �: O   z; �   �; 2  x< K   �= �   �=    {> �  �>   o@ �  �C 3  F �   7H    �H �   I n  �I 
   K �   "K ~  �K �   iO P   dP G   �P �  �P 4   �R t  �R �  DV �  $X 0   �Z �   �Z �   �[ �  Q\   #_    ;a !  Ya 2   {b   �b �   �c S  �d @  �e u   0g 4   �g N  �g J   *i k  ui �  �l -  �n o  �o �   Bq R   �q �   %r !  s %  &v �  Lx Z  �z L   X} 3  �} x  �~ �  R�   #� �  4� J  ą 4   � �  D� 6   � �  #� �   �    �� '  ��   � `  � q  J� �  �� �  \�    � �   
� P  � �  4� �   ʦ   �� �  �� ^   s� �   ҭ �   �� '   O�    w� �  �� �   G� ^   !� ^  �� ;  ߳ �  � �  ̷ P  ��   � N  � �  6� +   ��   �    �   � <   =� a   z� (   ��    � 7   !�    Y� ,   o� $   ��    ��    ��    �� 
   ��    ��    � )   '�    Q� :   a�    ��    ��    ��    �� �   �� o   �� (   �    B� =   R� 0   �� D   �� _   � (   f�    ��    ��    ��    ��    ��    �    �    %�    A�    H� 9   `� Q   �� :   �� d   '� d   �� 3   �� @   %� '   f� ;   �� i   �� "   4� |   W� �   ��    U� �   ^� j   ��    J�    f� *   ~�    ��     ��    ��    ��    �     � 0   (�    Y� 0   f�    ��    �� 
   ��    �� =   �� _   �    b� 	   k�    u� 	   ��    ��    ��    �� 	   �� �   �� -   m� ,   ��    ��    ��    �    �    +� $   H� &   m� 2   ��    ��    �� ;   ��    � "   $�    G�    T�    t�    �� :   �� |   �� �   M� z   � �   �� )  y� �   ��    O� :   a�    �� 
   ��    ��    �� !   �� 9   �� p   9� C   �� y   �� -   h� M   �� 9   �� �   � r   ��   � v   3� X   �� 0   �    4�    M�    V� 
   _�    j�    ��    ��    ��    ��    �� !   ��    �    ,� *   J�    u�    ��    �� "   ��    �� 
   ��    ��    ��    �    %� �  B�    ��    ��    
� >   � .   ^� N   �� q   �� /   N� +   ~� L   �� )   ��    !�    A� �   E�    �    '� '   9� �   a�    ��    � �   1� 3   �� 5  �� v   2� �   �� �   Q� H  � I   T�   �� �   �� S  c� 4  �� '   ��    � _   � J   }� d   �� p   -� �   �� [   #� g   � )   �� >   �    P�    W� U   j� 6   ��    ��    �    �    � �   ;�    �� .   �� f   � "   ~� .   �� W   �� u   (� �  ��    +�     A�   b�    k� 
   r� 
   }�     ��    �� /   �� �  ��     �    �        $ �  - 4   &    [ 
   k #  v 
   � 
   � 6   � %   �    
 /    �   L (       D d   V *   �    �    �     G   " {   j    � ;   � �   8	 �   �	 q   ~
 0   �
 Q  ! V   s    � :   � .   !
 �   P
 �   >     g   4 �   �   �     � *   �    �   � F    3   Y 3   � /   � L   � �   >     &   3 B   Z g   �     &    ,   ; 6   h �  �   � �  � O   Y <   �    � g   � �  [  T   " �   U# (   P$ A  y$ K  �% A   ( 0   I( �  z( �  A+    �, 
   �, 3   �,    $- �  3- 2   / �  8/ 2  �0 +  3    ?4 �   ]4 C   /5 G   s5 �   �5 �  `6 h  8 �   w9 �  o: �   V< �  �< u   �>   ? �   
@ n   �@ �   NA E   @B :   �B    �B    �B *   �B <   $C 5   aC k   �C M   D 	   QD K   [D t  �D 6   F ,  SF �   �G �   @H I   $I 9   nI &   �I &   �I D   �I @   ;J P   |J "   �J H  �J )   9L   cL    oM �   �M �   }N �   $O �  �O �   �Q (   xR �   �R 	   cS �  mS �   1U 1   �U c  �U    [X #   sX    �X    �X C   �X 
   
Y    Y b   "Y   �Y T  �Z    �[    �[ -   	\ �   7\ �  -] �   �^ �   �_    c`    �` 4   �`    �`    �`    
a    )a Q   1a A   �a �  �a {   |d !   �d �   e �   �e ]   Yf �   �f w   dg n   �g    Kh }   Zh W   �h �   0i w   �i q   rj    �j /  �j }   &l a   �l �   m l   �m Z   �m    Un d   un �   �n �   ao P   ,p �   }p    cq �  pq ]   �r {   Us �   �s    �t �  �t 2  Uw "   �x +   �x    �x    �x }  �x 7   ez r   �z &  { �   7| �   1} �   �} �   �~ �   � �   c� `   5� �  �� �   � 9  ��   � q  � 
   s� 
   �� 
   �� �   �� 
   f� 
   t� b   �� r   �    X� 
   Y� �   g� Z   �� �   V� E   � �   8� p   Ӎ 
   D� �   R� 
   � 
   &� 
   4� �  B� �   � �  �� '   q�    ��    �� �   �� 1   �� 8   � �  � �   �� .   n�    �� 3   �� U   � J   7� ^   �� -   � -   � X  =� 3   �� -  ʜ �  �� �   z� z   *� �   �� l  �� �  �� �  Ϧ   {� �   �� �   Y�    ��   � $   *� �  O� Y  O� �  �� �   T� �  � �   � �   з �   �� =   X� S   ��    � :   �    A�    N� &   j� 4  ��    ƻ a   � 1   H�    z�    �� 
   �� (   �� �   �� q   ^� l   н    =� 7   ^�    ��    ��    �� .   Ŀ    � 
   �    � 
   #� 
   1� 
   <� 
   J� 
   U� B   c� �   ��    H�    Y�    x� $   ��    ��    ��    ��    ��    �    � e   /� �   ��    r� :   �� z   �� �   B� �   �   � �  � �   �� 3  s� %   �� �   �� =   W� k   �� S   �   U� �   a� y   [� \   ��    2� �   I� t   �� �   X�    �� 4   �� -   4�    b�    �� 7   ��    �� 1   ��     � 5   /� 7   e� 1   �� .   �� (   �� M   '�    u�    ��    ��    ��    ��    �� 9   � 9   H� �   �� �   *� 6   �� .   $� �   S� �   �� �   k� *   � G   .� ;   v� =   �� T   �� 4   E� .  z� _  �� l  	� ,   v� Z   �� �   �� F   �� ?   � ,  @� �   m� v   .� (   �� �   �� �   �� �   b� ^   � |   r� �   �� +   �� T   � "   d� P   �� �   �� �   [�    ��    ��    �� /   �� �   )� z   �� /   d� R   �� .   �� $   � -   ;� N   i� ;   �� �   �� �   y� \   � f   ]� U   �� �   � A   �� 0   � #   O� 6   s� '   �� *   �� #   �� *   !� N   L� <   �� �  �� _   �� S   �� M   S� 6   ��    �� �   �� �   v� �   �   �� 	   �� �   �� )   �� -   �� �   � &   �� .   � H   B� =   �� q   �� �   ;� ^   �� 0   � 4   L� �   �� E   Q� 1   �� }   �� ?  G� U   �  �   �  �   � h   � �   � L   � D   � �   ,    � �   � U   � �    F   � [    )   v $   � ,  � R   � j   E	 �   �	 �   w
 ]   �
 �   Z �       � *   � =   
 _   R
     �
    �
    �
    	 
     4   . �   c |    $   � 3   � C   � 5     /   V �   � E   1 �   w e    1   v �   � 7  * I   b o   � )    h   F X   � ,    �   5 z   � 2   8 N   k    � 4   � z       } �   � 3   ?    s =   � �   �   f d   w $   � $    d   & $   � v   � d   ' �   � �   L    � =   � �   0 ,     �   ?  O   ! 1   _! {   �! =  
" 
   K#    Y#    \#    _# 2   ~# '   �# f   �# "   @$    c$ 
   ~$ &   �$ H   �$ H   �$    B% h   O% P   �%    	& 2   & =   O& M   �& !   �& V   �& �   T' \   ;(    �(    �( �   �( �  �) a   3+ =   �+ #  �+ )  �- D   !0 �   f0 #   �0 8  1 K   J4 �   �4    "5    :5 �  K5 0   �7 �   8 �   �8 �   E9 h   �9 3   <: t   p: ]   �:    C; �   `; R   �; Q   :< ]   �< O   �< l   := �   �= �   _> )   ? �   8? �  @ "  �A 4   !C �  VC J  0E   {F m  �G �   I K   �I    FJ �   bJ =   /K �  mK �   9N e   �N    ,O 3   KO �  O    =R L  ER �  �S ~  LU �  �V x   �X l   )Y �   �Y I   Z Q   fZ s   �Z �   ,[ '   �[ '   �[ V   \ 0   v\ "   �\ <   �\ �   ] ?   �]    �] �   �] �   �^ �   �_     Y` "   z`    �` �   �` �   .a `   �a �   b �   �b >   vc *   �c 	  �c 
   �d �   �d D  �e k  �h \   Hj T   �j (   �j    #k &   ,k P   Sk >   �k �   �k F  �l j   �m    Zn    nn &   �n !   �n w   �n    Co R   Wo    �o 1   �o 6   �o    p    7p p   @p 
   �p $   �p 5   �p =   q 3   Uq �   �q R  7r w   �s �   t S   �t   �t    �u    v   +v �   Dw   8x 
   Iy y   Ty `   �y L   /z �   |z n   { �   t{    | h   |    �|    �|    �|    �|    �|    �|    �| /   }    <} 
   Q} m   \} I   �} I   ~ 5   ^~ K   �~ Q   �~ 8   2 )   k    � H   � ,   �    "� ]   /� 8   �� {   ƀ 8   B� 0   {�    �� &   Ɓ $   �    � �   .� �   �� �   B� �   σ    ��    ؄ F   ��    >� 4   [� '   �� F   �� F   �� �   F� +   � )   �    I� 
   P� 	   [� G   e�    �� ^  ˇ     *� ,   K�    x� %   �� _   �� )   � �   E� F   Њ J   � M   b� ?   ��    �� |   �� I   t� E   �� (   � t   -� e   �� -   � V   6� *   �� �   �� �   V� [   ߏ 
   ;� '   F�    n�    �� &   �� *    �  � �   �� ,   �    J� 5   c�    �� 6   �� 
   � 
   �� �   	� b   �� �   �� *   � g   � �   � A   � _   R� <   �� ?   � P   /� M   �� '   Θ    �� F   � !   V� U   x� S   Ι �  "� c   � S   L� o   �� "   � �   3� ,   �    ;� t   O�    Ğ 4   ڞ    � N   /� c   ~� :   � �   �    ��    � ,   *�     W�    x�    �� H   ��    ��     � �   ;� p  â >   4� >   s�    �� �   ӥ '   Ǧ *   �    �    -� 8   =�    v�    ��    ��    ��    ɧ    ܧ    � 
   ��    
�    �    +�    8�    D� 1   T� ?  �� i  Ʃ   0� �  B� z  � g  ��   ��    � �  � *   �� s  ڷ 5  N� =  �� 
  ¼   ͽ <   ٿ �   � W   �� &   F� n   m� w   �� �   T� �   �� �   �� �   �� 	  �� 0  ��    �� �   �� )  �� c   �� �   E�    �   7� �  >�   � �   #� 
   #� k   .� �   ��   #� �   ;� �   � �   ��    s� '   ��    �� a   �� ;   /�    k� �   z� Y   � �   u� %   �� �   � �   � a   �� g   /� n   �� �   � �   �� h   .� �   �� u   J� �   �� �   ��    ]� 1   f� 6   �� `   �� :   0�    k�    t� c   ��    ��    �    !� >   9� ?   x� S   ��    �    *�    7�    >� 	   D� z   N� �   �� �   ^� H   ��    :� !   B� M   d�    �� 
   ��    �� 
   ��    �� 
   ��    ��    �    �    �    -�    :�    N�    [�    o�    ��    ��    ��    ��    �� =   �� '   � '   7� �   _� +   �� ~   +� �   ��    ��    ��    ��    ��    ��    ��    �� 
  �� �   � �   ��    r�    �� �   ��    c� �   w� �   O� 6   /� %   f� �   �� C  k� �   �� 
  6� �   D� 7   �� �   �    ��     �� j   � �   {� &   X� �   � �   S� �   � w   ��    >�    X�    _�    {�    ��    ��    ��    �� �   �� �   �� �   ~� 5  j� #  �� �   �� m   I� I  �� i   h  k �   � {  � �  $    �	 �   
 e   �   g   J
 n  � �   9 �  �    � P   � �   < �   ^   � I  C    �    �    � �   � ^   � �   � E   x �   � l   C q   � .   "   Q `   V /   � ]   � P  E     �!  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ar
Language-Team: ar <<EMAIL>>
Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=0 && n%100<=2 ? 4 : 5);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 مكتبة الزّاي هي مكتبة شهيرة (وغير قانونية). لقد أخذوا مجموعة Library Genesis وجعلوها قابلة للبحث بسهولة. بالإضافة إلى ذلك، أصبحوا فعالين جدًا في طلب مساهمات الكتب الجديدة، من خلال تحفيز المستخدمين المساهمين بمزايا مختلفة. حاليًا، لا يساهمون بهذه الكتب الجديدة مرة أخرى إلى Library Genesis. وعلى عكس Library Genesis، لا يجعلون مجموعتهم قابلة للانعكاس بسهولة، مما يمنع الحفظ الواسع. هذا مهم لنموذج أعمالهم، حيث أنهم يفرضون رسومًا للوصول إلى مجموعتهم بشكل جماعي (أكثر من 10 كتب في اليوم). نحن لا نصدر أحكامًا أخلاقية بشأن فرض رسوم مالية للوصول الجماعي إلى مجموعة كتب غير قانونية. لا شك أن مكتبة الزّاي قد نجحت في توسيع الوصول إلى المعرفة وتوفير المزيد من الكتب. نحن هنا ببساطة لنقوم بدورنا: ضمان الحفاظ طويل الأمد على هذه المجموعة الخاصة. - آنّا والفريق (<a %(reddit)s>Reddit</a>) في الإصدار الأصلي من عاكسة مكتبة القراصنة (تعديل: تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>)، قمنا بعمل عاكسة لمكتبة الزّاي، وهي مجموعة كتب غير قانونية كبيرة. كتذكير، هذا ما كتبناه في تلك المدونة الأصلية: تعود تلك المجموعة إلى منتصف عام 2021. في هذه الأثناء، كانت مكتبة الزّاي تنمو بمعدل مذهل: لقد أضافوا حوالي 3.8 مليون كتاب جديد. هناك بعض النسخ المكررة، بالطبع، لكن الغالبية العظمى منها تبدو كتبًا جديدة بشكل شرعي، أو نسخًا بجودة أعلى من الكتب المقدمة سابقًا. يعود ذلك بشكل كبير إلى زيادة عدد المشرفين المتطوعين في مكتبة الزّاي، ونظام التحميل الجماعي الخاص بهم مع إزالة التكرارات. نود أن نهنئهم على هذه الإنجازات. نحن سعداء بالإعلان عن أننا حصلنا على جميع الكتب التي أضيفت إلى مكتبة الزّاي بين آخر عاكسة لنا وأغسطس 2022. كما عدنا وجمعنا بعض الكتب التي فاتتنا في المرة الأولى. بشكل عام، هذه المجموعة الجديدة تبلغ حوالي 24 تيرابايت، وهي أكبر بكثير من المجموعة السابقة (7 تيرابايت). الآن، عاكستنا تبلغ 31 تيرابايت في المجموع. مرة أخرى، قمنا بإزالة التكرارات مقابل Library Genesis، حيث تتوفر بالفعل تورنتات لتلك المجموعة. يرجى الذهاب إلى عاكسة مكتبة القراصنة للاطلاع على المجموعة الجديدة (تعديل: تم النقل إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>). هناك المزيد من المعلومات هناك حول كيفية هيكلة الملفات، وما الذي تغير منذ المرة الأخيرة. لن نقوم بالربط إليها من هنا، حيث أن هذا مجرد موقع مدونة لا يستضيف أي مواد غير قانونية. بالطبع، يُعتبر المساهمة في التحميل أيضًا طريقة رائعة لمساعدتنا. شكرًا لكل من يساهم في تحميل مجموعتنا السابقة من التورنتات. نحن ممتنون للاستجابة الإيجابية، وسعداء بوجود الكثير من الأشخاص الذين يهتمون بالحفاظ على المعرفة والثقافة بهذه الطريقة غير التقليدية. تمت إضافة 3 كتب جديدة إلى عاكسة مكتبة القراصنة (+24 تيرابايت، 3.8 مليون كتاب) اقرأ المقالات المرافقة من TorrentFreak: <a %(torrentfreak)s>الأولى</a>، <a %(torrentfreak_2)s>الثانية</a> - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) مقالات مرافقة من TorrentFreak: <a %(torrentfreak)s>الأولى</a>، <a %(torrentfreak_2)s>الثانية</a> لم يمض وقت طويل، كانت "مكتبات الظل" تحتضر. توقفت Sci-Hub، الأرشيف الضخم غير القانوني للأوراق الأكاديمية، عن استقبال أعمال جديدة بسبب الدعاوى القضائية. شهدت "مكتبة الزّاي"، أكبر مكتبة غير قانونية للكتب، اعتقال من يُزعم أنهم منشئوها بتهم انتهاك حقوق الطبع والنشر. لقد تمكنوا بشكل لا يصدق من الهروب من اعتقالهم، لكن مكتبتهم لا تزال مهددة. بعض الدول تقوم بالفعل بنسخة من هذا. أفادت TorrentFreak <a %(torrentfreak)s>بأن</a> الصين واليابان قد أدخلتا استثناءات للذكاء الاصطناعي في قوانين حقوق الطبع والنشر الخاصة بهما. من غير الواضح لنا كيف يتفاعل هذا مع المعاهدات الدولية، لكنه بالتأكيد يوفر غطاءً لشركاتهم المحلية، مما يفسر ما كنا نراه. أما بالنسبة لرَبيدةُ آنّا — سنواصل عملنا السري المتجذر في القناعة الأخلاقية. ومع ذلك، فإن أكبر أمنياتنا هي الدخول إلى النور، وتضخيم تأثيرنا بشكل قانوني. يرجى إصلاح حقوق الطبع والنشر. عندما واجهت مكتبة الزّاي الإغلاق، كنت قد قمت بالفعل بنسخ مكتبتها بالكامل وكنت أبحث عن منصة لاستضافتها. كان ذلك دافعي لبدء رَبيدةُ آنّا: استمرار للمهمة وراء تلك المبادرات السابقة. لقد نمونا منذ ذلك الحين لنصبح أكبر مكتبة ظل في العالم، حيث نستضيف أكثر من 140 مليون نص محمي بحقوق الطبع والنشر عبر العديد من الصيغ — كتب، أوراق أكاديمية، مجلات، صحف، وما إلى ذلك. فريقي وأنا أيديولوجيون. نعتقد أن الحفاظ على هذه الملفات واستضافتها هو أمر صحيح أخلاقياً. المكتبات حول العالم تشهد تخفيضات في التمويل، ولا يمكننا الوثوق بتراث البشرية للشركات أيضاً. ثم جاء الذكاء الاصطناعي. تواصلت معنا جميع الشركات الكبرى تقريباً التي تبني LLMs للتدريب على بياناتنا. أعادت معظم الشركات الأمريكية (لكن ليس كلها!) النظر بمجرد أن أدركت الطبيعة غير القانونية لعملنا. على النقيض من ذلك، تبنت الشركات الصينية مجموعتنا بحماس، دون أن تزعجها قانونيتها. وهذا ملحوظ بالنظر إلى دور الصين كدولة موقعة على جميع المعاهدات الدولية الرئيسية لحقوق الطبع والنشر تقريباً. لقد قدمنا وصولاً عالي السرعة إلى حوالي 30 شركة. معظمها شركات LLM، وبعضها وسطاء بيانات، سيعيدون بيع مجموعتنا. معظمها صينية، رغم أننا عملنا أيضاً مع شركات من الولايات المتحدة وأوروبا وروسيا وكوريا الجنوبية واليابان. اعترفت DeepSeek <a %(arxiv)s>بأن</a> نسخة سابقة تم تدريبها على جزء من مجموعتنا، رغم أنهم متكتمون بشأن نموذجهم الأخير (ربما تم تدريبه أيضاً على بياناتنا). إذا أراد الغرب البقاء في المقدمة في سباق LLMs، وفي النهاية، AGI، فإنه يحتاج إلى إعادة النظر في موقفه من حقوق الطبع والنشر، وبسرعة. سواء كنت توافق معنا أو لا على قضيتنا الأخلاقية، فإن هذا الآن يصبح قضية اقتصادية، وحتى قضية أمن قومي. جميع الكتل القوية تبني علماء خارقين اصطناعيين، ومخترقين خارقين، وجيوش خارقة. حرية المعلومات تصبح مسألة بقاء لهذه الدول — حتى مسألة أمن قومي. فريقنا من جميع أنحاء العالم، وليس لدينا انحياز معين. لكننا نشجع الدول ذات قوانين حقوق الطبع والنشر القوية على استخدام هذا التهديد الوجودي لإصلاحها. فما العمل؟ توصيتنا الأولى بسيطة: تقصير مدة حقوق الطبع والنشر. في الولايات المتحدة، تُمنح حقوق الطبع والنشر لمدة 70 عاماً بعد وفاة المؤلف. هذا أمر سخيف. يمكننا مواءمة هذا مع براءات الاختراع، التي تُمنح لمدة 20 عاماً بعد التقديم. يجب أن يكون هذا أكثر من كافٍ للمؤلفين للكتب والأوراق والموسيقى والفن والأعمال الإبداعية الأخرى للحصول على تعويض كامل عن جهودهم (بما في ذلك المشاريع طويلة الأجل مثل تكييف الأفلام). ثم، على الأقل، يجب على صانعي السياسات تضمين استثناءات للحفاظ الجماعي ونشر النصوص. إذا كان فقدان الإيرادات من العملاء الفرديين هو القلق الرئيسي، يمكن أن يظل التوزيع على المستوى الشخصي محظوراً. في المقابل، سيتم تغطية أولئك القادرين على إدارة مستودعات ضخمة — الشركات التي تدرب LLMs، إلى جانب المكتبات والأرشيفات الأخرى — بهذه الاستثناءات. إصلاح حقوق الطبع والنشر ضروري للأمن القومي ملخص: تم تدريب LLMs الصينية (بما في ذلك DeepSeek) على أرشيفي غير القانوني من الكتب والأوراق — الأكبر في العالم. يحتاج الغرب إلى إصلاح قانون حقوق الطبع والنشر كمسألة أمن قومي. يرجى الاطلاع على <a %(all_isbns)s>المنشور الأصلي في المدونة</a> لمزيد من المعلومات. قمنا بتحدي لتحسين هذا. كنا سنمنح جائزة المركز الأول بقيمة 6,000 دولار، والمركز الثاني بقيمة 3,000 دولار، والمركز الثالث بقيمة 1,000 دولار. نظرًا للاستجابة الهائلة والتقديمات الرائعة، قررنا زيادة مجموع الجوائز قليلاً، ومنح جائزة المركز الثالث لأربعة فائزين بقيمة 500 دولار لكل منهم. الفائزون مذكورون أدناه، ولكن تأكد من الاطلاع على جميع التقديمات <a %(annas_archive)s>هنا</a>، أو تحميل <a %(a_2025_01_isbn_visualization_files)s>التورنت المجمع</a> الخاص بنا. المركز الأول 6,000 دولار: phiresky هذا <a %(phiresky_github)s>التقديم</a> (<a %(annas_archive_note_2951)s>تعليق Gitlab</a>) هو ببساطة كل ما أردناه، وأكثر! أحببنا بشكل خاص خيارات التصور المرنة بشكل لا يصدق (حتى دعم المظللات المخصصة)، ولكن مع قائمة شاملة من الإعدادات المسبقة. كما أحببنا مدى سرعة وسلاسة كل شيء، والتنفيذ البسيط (الذي لا يحتوي حتى على خلفية)، والخريطة المصغرة الذكية، والشرح الواسع في <a %(phiresky_github)s>منشور المدونة</a> الخاص بهم. عمل رائع، والفائز المستحق! - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) قلوبنا مليئة بالامتنان. أفكار ملحوظة ناطحات سحاب للندرة الكثير من المنزلقات لمقارنة مجموعات البيانات، كما لو كنت دي جي. شريط مقياس مع عدد الكتب. تسميات جميلة. نظام ألوان افتراضي رائع وخريطة حرارية. عرض خريطة فريد وفلاتر التعليقات التوضيحية، وأيضًا الإحصائيات المباشرة إحصائيات مباشرة بعض الأفكار والتنفيذات الأخرى التي أحببناها بشكل خاص: يمكننا الاستمرار لفترة، لكن دعونا نتوقف هنا. تأكد من الاطلاع على جميع المشاركات <a %(annas_archive)s>هنا</a>، أو قم بتنزيل <a %(a_2025_01_isbn_visualization_files)s>التورنت المجمع</a> الخاص بنا. العديد من المشاركات، وكل واحدة تقدم منظورًا فريدًا، سواء في واجهة المستخدم أو التنفيذ. سنقوم على الأقل بدمج المشاركة الفائزة بالمركز الأول في موقعنا الرئيسي، وربما بعض المشاركات الأخرى. لقد بدأنا أيضًا في التفكير في كيفية تنظيم عملية تحديد وتأكيد ثم أرشفة الكتب النادرة. المزيد قادم في هذا الصدد. شكرًا لكل من شارك. إنه لأمر مدهش أن يهتم الكثير من الناس. تبديل سهل لمجموعات البيانات للمقارنات السريعة. جميع أرقام ISBN أرقام SSNO في CADAL تسريب بيانات CERLALC أرقام SSID في DuXiu فهرس الكتب الإلكترونية في EBSCOhost كتب جوجل جودريدز أرشيف الإنترنت ISBNdb السجل العالمي للناشرين ISBN ليبي الملفات في رَبيدةُ آنّا Nexus/STC OCLC/Worldcat المكتبة المفتوحة المكتبة الوطنية الروسية المكتبة الإمبراطورية في ترانتور المركز الثاني 3,000 دولار: hypha "بينما المربعات والمستطيلات المثالية مرضية رياضيًا، إلا أنها لا توفر تفوقًا في السياق الخريطي. أعتقد أن عدم التماثل المتأصل في هذه الأشكال مثل هيلبرت أو مورتون الكلاسيكي ليس عيبًا بل ميزة. تمامًا مثل الشكل الشهير لإيطاليا الذي يشبه الحذاء يجعلها معروفة على الفور على الخريطة، قد تخدم "الخصائص الفريدة" لهذه المنحنيات كمعالم معرفية. يمكن أن تعزز هذه الخصوصية الذاكرة المكانية وتساعد المستخدمين على توجيه أنفسهم، مما يجعل تحديد المناطق المحددة أو ملاحظة الأنماط أسهل." تقديم رائع آخر <a %(annas_archive_note_2913)s>تقديم</a>. ليس مرنًا مثل المركز الأول، لكننا فضلنا بالفعل تصوره على المستوى الكلي على المركز الأول (منحنى ملء الفضاء، الحدود، التسمية، التمييز، التحريك، والتكبير). تعليق <a %(annas_archive_note_2971)s>تعليق</a> من جو ديفيس تردد صداه لدينا: ولا يزال هناك الكثير من الخيارات للتصور والعرض، بالإضافة إلى واجهة مستخدم سلسة وبديهية بشكل لا يصدق. مركز ثانٍ قوي! - آنّا والفريق (<a %(reddit)s>Reddit</a>) قبل بضعة أشهر، أعلنا عن <a %(all_isbns)s>جائزة بقيمة 10,000 دولار</a> لإنشاء أفضل تصور ممكن لبياناتنا التي تعرض مساحة ISBN. أكدنا على إظهار الملفات التي قمنا بأرشفتها والتي لم نقم بأرشفتها بعد، وقدمنا لاحقاً مجموعة بيانات تصف عدد المكتبات التي تحتفظ بـ ISBNs (كمقياس للندرة). لقد غمرتنا الردود. كان هناك الكثير من الإبداع. شكر كبير لكل من شارك: طاقتك وحماسك معديان! في النهاية، أردنا الإجابة على الأسئلة التالية: <strong>ما هي الكتب الموجودة في العالم، كم منها قمنا بأرشفتها بالفعل، وعلى أي الكتب يجب أن نركز بعد ذلك؟</strong> من الرائع أن نرى الكثير من الناس يهتمون بهذه الأسئلة. بدأنا بتصور أساسي بأنفسنا. في أقل من 300 كيلوبايت، تمثل هذه الصورة بشكل موجز أكبر "قائمة كتب" مفتوحة بالكامل تم تجميعها في تاريخ البشرية: المركز الثالث 500 دولار #1: maxlion في هذا <a %(annas_archive_note_2940)s>التقديم</a> أحببنا حقًا الأنواع المختلفة من العروض، خاصة عرض المقارنة وعرض الناشر. المركز الثالث 500 دولار #2: abetusk على الرغم من أن واجهة المستخدم ليست الأكثر صقلًا، إلا أن هذا <a %(annas_archive_note_2917)s>التقديم</a> يحقق الكثير من المتطلبات. أحببنا بشكل خاص ميزة المقارنة. المركز الثالث 500 دولار #3: conundrumer0 مثل المركز الأول، أدهشنا هذا <a %(annas_archive_note_2975)s>التقديم</a> بمرونته. في النهاية، هذا ما يجعل أداة التصور رائعة: أقصى قدر من المرونة للمستخدمين المحترفين، مع الحفاظ على البساطة للمستخدمين العاديين. المركز الثالث 500 دولار #4: charelf التقديم الأخير <a %(annas_archive_note_2947)s>التقديم</a> الذي حصل على جائزة هو بسيط جدًا، ولكنه يحتوي على بعض الميزات الفريدة التي أحببناها حقًا. أحببنا كيف يظهرون عدد مجموعات البيانات التي تغطي ISBN معين كمعيار للشعبية/الموثوقية. كما أحببنا بساطة وفعالية استخدام شريط التعتيم للمقارنات. الفائزون بجائزة تصور ISBN بقيمة 10,000 دولار ملخص: حصلنا على بعض المشاركات الرائعة لجائزة تصور ISBN بقيمة 10,000 دولار. الخلفية كيف يمكن لرَبيدةُ آنّا تحقيق مهمتها في نسخ جميع معارف البشرية احتياطيًا، دون معرفة الكتب التي لا تزال موجودة؟ نحن بحاجة إلى قائمة مهام. إحدى الطرق لتخطيط ذلك هي من خلال أرقام ISBN، التي تم تخصيصها لكل كتاب نُشر منذ السبعينيات (في معظم البلدان). لا توجد سلطة مركزية تعرف جميع تخصيصات ISBN. بدلاً من ذلك، هو نظام موزع، حيث تحصل البلدان على نطاقات من الأرقام، ثم تقوم بتخصيص نطاقات أصغر للناشرين الرئيسيين، الذين قد يقومون بتقسيم النطاقات إلى ناشرين أصغر. وأخيرًا يتم تخصيص الأرقام الفردية للكتب. بدأنا في تخطيط أرقام ISBN <a %(blog)s>منذ عامين</a> مع جمعنا لبيانات ISBNdb. منذ ذلك الحين، قمنا بجمع العديد من مصادر metadata الأخرى، مثل <a %(blog_2)s>Worldcat</a>، وGoogle Books، وGoodreads، وLibby، والمزيد. يمكن العثور على قائمة كاملة في صفحات "Datasets" و"Torrents" على رَبيدةُ آنّا. لدينا الآن أكبر مجموعة مفتوحة بالكامل وقابلة للتنزيل بسهولة من metadata الكتب (وبالتالي أرقام ISBN) في العالم. لقد <a %(blog)s>كتبنا بشكل موسع</a> عن سبب اهتمامنا بالحفظ، ولماذا نحن حاليًا في نافذة حرجة. يجب علينا الآن تحديد الكتب النادرة والمهمشة والفريدة المعرضة للخطر وحفظها. وجود metadata جيدة لجميع الكتب في العالم يساعد في ذلك. مكافأة بقيمة 10,000 دولار سيتم إعطاء اعتبار قوي للاستخدامية وكيفية المظهر الجيد. عرض metadata الفعلية لأرقام ISBN الفردية عند التكبير، مثل العنوان والمؤلف. منحنى ملء المساحة بشكل أفضل. مثلاً، زيغ-زاغ، يبدأ من 0 إلى 4 في الصف الأول ثم يعود (بالعكس) من 5 إلى 9 في الصف الثاني — يتم تطبيقه بشكل متكرر. مخططات ألوان مختلفة أو قابلة للتخصيص. عرض خاص لمقارنة Datasets. طرق لتصحيح المشكلات، مثل metadata الأخرى التي لا تتفق جيدًا (مثل العناوين المختلفة بشكل كبير). تعليق الصور بملاحظات على أرقام ISBN أو النطاقات. أي استراتيجيات لتحديد الكتب النادرة أو المعرضة للخطر. أي أفكار إبداعية يمكنك التوصل إليها! الشفرة يمكن العثور على الشفرة لتوليد هذه الصور، بالإضافة إلى أمثلة أخرى، في <a %(annas_archive)s>هذا الدليل</a>. لقد توصلنا إلى تنسيق بيانات مضغوط، حيث أن جميع معلومات ISBN المطلوبة تبلغ حوالي 75 ميجابايت (مضغوطة). يمكن العثور على وصف تنسيق البيانات والشفرة لتوليدها <a %(annas_archive_l1244_1319)s>هنا</a>. للحصول على المكافأة، لست ملزمًا باستخدام هذا، ولكنه ربما يكون التنسيق الأكثر ملاءمة للبدء به. يمكنك تحويل metadata الخاصة بنا كيفما تشاء (على الرغم من أن جميع الشفرة الخاصة بك يجب أن تكون مفتوحة المصدر). لا يمكننا الانتظار لرؤية ما ستتوصل إليه. حظًا موفقًا! قم بتفريع هذا المستودع، وقم بتحرير HTML لهذا المنشور في المدونة (لا يُسمح بأي خلفيات أخرى غير خلفيتنا Flask). اجعل الصورة أعلاه قابلة للتكبير بسلاسة، بحيث يمكنك التكبير حتى أرقام ISBN الفردية. يجب أن يؤدي النقر على أرقام ISBN إلى نقلك إلى صفحة metadata أو البحث في رَبيدةُ آنّا. يجب أن تظل قادرًا على التبديل بين جميع مجموعات البيانات المختلفة. يجب تمييز نطاقات البلدان ونطاقات الناشرين عند التمرير. يمكنك استخدام مثلاً <a %(github_xlcnd_isbnlib)s>data4info.py في isbnlib</a> للحصول على معلومات عن البلدان، و"isbngrp" الخاص بنا لجمع الناشرين (<a %(annas_archive)s>dataset</a>، <a %(annas_archive_2)s>torrent</a>). يجب أن يعمل بشكل جيد على أجهزة الكمبيوتر المكتبية والهواتف المحمولة. هناك الكثير لاستكشافه هنا، لذا نحن نعلن عن مكافأة لتحسين التصور أعلاه. على عكس معظم مكافآتنا، هذه المكافأة محددة بوقت. يجب عليك <a %(annas_archive)s>تقديم</a> كود المصدر المفتوح الخاص بك بحلول 2025-01-31 (23:59 UTC). أفضل تقديم سيحصل على 6,000 دولار، والمركز الثاني 3,000 دولار، والمركز الثالث 1,000 دولار. سيتم منح جميع المكافآت باستخدام Monero (XMR). فيما يلي المعايير الدنيا. إذا لم يلبِ أي تقديم المعايير، قد نمنح بعض المكافآت، ولكن سيكون ذلك وفقًا لتقديرنا. للحصول على نقاط إضافية (هذه مجرد أفكار — دع إبداعك ينطلق بحرية): يمكنك الابتعاد تمامًا عن المعايير الدنيا، والقيام بتصور مختلف تمامًا. إذا كان مذهلاً حقًا، فإنه يستحق المكافأة، ولكن وفقًا لتقديرنا. قم بتقديم المشاركات عن طريق نشر تعليق على <a %(annas_archive)s>هذه المسألة</a> مع رابط إلى مستودعك المتشعب، أو طلب الدمج، أو الفرق. - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) هذه الصورة بحجم 1000×800 بكسل. كل بكسل يمثل 2,500 رقم ISBN. إذا كان لدينا ملف لرقم ISBN، نجعل ذلك البكسل أكثر خضرة. إذا كنا نعرف أن رقم ISBN قد تم إصداره، ولكن ليس لدينا ملف مطابق، نجعله أكثر احمرارًا. في أقل من 300 كيلوبايت، تمثل هذه الصورة بشكل موجز أكبر "قائمة كتب" مفتوحة بالكامل تم تجميعها في تاريخ البشرية (بضع مئات من الجيجابايت مضغوطة بالكامل). كما يظهر: هناك الكثير من العمل المتبقي في نسخ الكتب احتياطيًا (لدينا فقط 16%). تصور جميع أرقام ISBN — مكافأة قدرها 10,000 دولار بحلول 2025-01-31 تمثل هذه الصورة أكبر "قائمة كتب" مفتوحة بالكامل تم تجميعها في تاريخ البشرية. التصور بجانب صورة النظرة العامة، يمكننا أيضًا النظر إلى مجموعات البيانات الفردية التي حصلنا عليها. استخدم القائمة المنسدلة والأزرار للتبديل بينها. هناك الكثير من الأنماط المثيرة للاهتمام لرؤيتها في هذه الصور. لماذا هناك بعض الانتظام في الخطوط والكتل، الذي يبدو أنه يحدث على مقاييس مختلفة؟ ما هي المناطق الفارغة؟ لماذا تكون بعض مجموعات البيانات متجمعة جدًا؟ سنترك هذه الأسئلة كتمرين للقارئ. - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) الخاتمة مع هذا المعيار، يمكننا إصدار الإصدارات بشكل أكثر تدريجيًا، وإضافة مصادر بيانات جديدة بسهولة أكبر. لدينا بالفعل بعض الإصدارات المثيرة في الأفق! نأمل أيضًا أن يصبح من الأسهل للمكتبات الظلية الأخرى عكس مجموعاتنا. بعد كل شيء، هدفنا هو الحفاظ على المعرفة والثقافة البشرية إلى الأبد، لذا كلما زادت التكرار كان ذلك أفضل. مثال لنلقِ نظرة على إصدارنا الأخير من مكتبة الزّاي كمثال. يتكون من مجموعتين: "<span style="background: #fffaa3">zlib3_records</span>" و"<span style="background: #ffd6fe">zlib3_files</span>". يتيح لنا ذلك استخراج وإصدار سجلات الميتاداتا بشكل منفصل عن ملفات الكتب الفعلية. لذلك، أصدرنا تورنتين مع ملفات ميتاداتا: كما أصدرنا مجموعة من التورنتات مع مجلدات البيانات الثنائية، ولكن فقط لمجموعة "<span style="background: #ffd6fe">zlib3_files</span>"، بإجمالي 62: بتشغيل <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> يمكننا رؤية ما بداخلها: في هذه الحالة، إنها ميتاداتا لكتاب كما أبلغت عنها مكتبة الزّاي. في المستوى الأعلى لدينا فقط "aacid" و"metadata"، ولكن لا يوجد "data_folder"، حيث لا توجد بيانات ثنائية مقابلة. يحتوي AACID على "22430000" كمعرف رئيسي، والذي يمكننا رؤيته مأخوذ من "zlibrary_id". يمكننا توقع أن تحتوي AACs الأخرى في هذه المجموعة على نفس الهيكل. الآن دعونا نشغل <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: هذه ميتاداتا AAC أصغر بكثير، على الرغم من أن الجزء الأكبر من هذا AAC يقع في مكان آخر في ملف ثنائي! بعد كل شيء، لدينا "data_folder" هذه المرة، لذلك يمكننا توقع أن تكون البيانات الثنائية المقابلة موجودة في <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. تحتوي "metadata" على "zlibrary_id"، لذلك يمكننا بسهولة ربطها بـ AAC المقابل في مجموعة "zlib_records". كان بإمكاننا الربط بعدة طرق مختلفة، على سبيل المثال من خلال AACID — المعيار لا يفرض ذلك. لاحظ أنه ليس من الضروري أيضًا أن يكون حقل "metadata" نفسه JSON. يمكن أن يكون سلسلة تحتوي على XML أو أي تنسيق بيانات آخر. يمكنك حتى تخزين معلومات الميتاداتا في الكتلة الثنائية المرتبطة، على سبيل المثال إذا كانت كمية البيانات كبيرة. ملفات وmetadata غير متجانسة، بأقرب شكل ممكن إلى الصيغة الأصلية. يمكن تقديم البيانات الثنائية مباشرة بواسطة خوادم الويب مثل Nginx. معرفات غير متجانسة في المكتبات المصدرية، أو حتى عدم وجود معرفات. إصدارات منفصلة من metadata مقابل بيانات الملفات، أو إصدارات metadata فقط (مثل إصدارنا من ISBNdb). التوزيع عبر التورنت، مع إمكانية استخدام طرق توزيع أخرى (مثل IPFS). سجلات غير قابلة للتغيير، حيث يجب أن نفترض أن التورنت الخاص بنا سيعيش إلى الأبد. إصدارات تدريجية / إصدارات قابلة للإلحاق. قابلة للقراءة والكتابة آليًا، بشكل مريح وسريع، خاصة لمكدسنا (Python، MySQL، ElasticSearch، Transmission، Debian، ext4). سهولة الفحص البشري إلى حد ما، رغم أن هذا ثانوي بالنسبة لقراءة الآلة. سهولة زراعة مجموعاتنا باستخدام seedbox مستأجر قياسي. أهداف التصميم لا نهتم بأن تكون الملفات سهلة التنقل يدويًا على القرص، أو قابلة للبحث دون معالجة مسبقة. لا نهتم بأن نكون متوافقين مباشرة مع برامج المكتبات الموجودة. بينما يجب أن يكون من السهل على أي شخص زراعة مجموعتنا باستخدام التورنت، لا نتوقع أن تكون الملفات قابلة للاستخدام دون معرفة تقنية كبيرة والتزام. حالة الاستخدام الأساسية لدينا هي توزيع الملفات وmetadata المرتبطة بها من مجموعات مختلفة موجودة. أهم اعتباراتنا هي: بعض الأهداف غير المهمة: نظرًا لأن رَبيدةُ آنّا مفتوحة المصدر، نريد استخدام تنسيقنا مباشرة. عندما نقوم بتحديث فهرس البحث الخاص بنا، نصل فقط إلى المسارات المتاحة للجمهور، بحيث يمكن لأي شخص يقوم بتفريع مكتبتنا البدء بسرعة. <strong>AAC.</strong> AAC (حاوية رَبيدةُ آنّا) هو عنصر واحد يتكون من <strong>metadata</strong>، وبيانات ثنائية اختيارية <strong>binary data</strong>، وكلاهما غير قابل للتغيير. يحتوي على معرف فريد عالميًا، يسمى <strong>AACID</strong>. <strong>AACID.</strong> تنسيق AACID هو كالتالي: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. على سبيل المثال، AACID الفعلي الذي أصدرناه هو <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID range.</strong> نظرًا لأن AACIDs تحتوي على طوابع زمنية متزايدة بشكل متزايد، يمكننا استخدام ذلك لتحديد النطاقات داخل مجموعة معينة. نستخدم هذا التنسيق: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>، حيث تكون الطوابع الزمنية شاملة. هذا متسق مع تدوين ISO 8601. النطاقات مستمرة، وقد تتداخل، ولكن في حالة التداخل يجب أن تحتوي على سجلات متطابقة مع تلك التي تم إصدارها سابقًا في تلك المجموعة (نظرًا لأن AACs غير قابلة للتغيير). لا يُسمح بالسجلات المفقودة. <code>{collection}</code>: اسم المجموعة، والذي قد يحتوي على حروف ASCII، أرقام، وشرطات سفلية (لكن لا يحتوي على شرطات سفلية مزدوجة). <code>{collection-specific ID}</code>: معرف خاص بالمجموعة، إذا كان ذلك ممكنًا، مثل معرف مكتبة الزّاي. قد يتم حذفه أو تقصيره. يجب حذفه أو تقصيره إذا كان AACID سيتجاوز 150 حرفًا. <code>{ISO 8601 timestamp}</code>: نسخة قصيرة من ISO 8601، دائمًا في UTC، على سبيل المثال <code>20220723T194746Z</code>. يجب أن يزيد هذا الرقم بشكل متزايد لكل إصدار، على الرغم من أن دلالاته الدقيقة يمكن أن تختلف لكل مجموعة. نقترح استخدام وقت الاستخلاص أو توليد المعرف. <code>{shortuuid}</code>: UUID مضغوط إلى ASCII، على سبيل المثال باستخدام base57. نستخدم حاليًا مكتبة <a %(github_skorokithakis_shortuuid)s>shortuuid</a> في بايثون. <strong>Binary data folder.</strong> مجلد يحتوي على البيانات الثنائية لنطاق من AACs، لمجموعة معينة. تحتوي هذه المجلدات على الخصائص التالية: يجب أن يحتوي الدليل على ملفات البيانات لجميع AACs ضمن النطاق المحدد. يجب أن يحتوي كل ملف بيانات على AACID كاسم الملف (بدون امتدادات). يجب أن يكون اسم الدليل نطاق AACID، مسبوقًا بـ <code style="color: green">annas_archive_data__</code>، وبدون لاحقة. على سبيل المثال، أحد إصداراتنا الفعلية يحتوي على دليل يسمى<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. يُوصى بجعل هذه المجلدات قابلة للإدارة إلى حد ما من حيث الحجم، على سبيل المثال، ألا تكون أكبر من 100 جيجابايت إلى 1 تيرابايت لكل منها، على الرغم من أن هذه التوصية قد تتغير بمرور الوقت. <strong>Collection.</strong> كل AAC ينتمي إلى مجموعة، والتي تُعرف بأنها قائمة من AACs متسقة دلاليًا. وهذا يعني أنه إذا قمت بإجراء تغيير كبير في تنسيق metadata، فعليك إنشاء مجموعة جديدة. المعيار <strong>Metadata file.</strong> يحتوي ملف metadata على metadata لنطاق من AACs، لمجموعة معينة. تحتوي هذه الملفات على الخصائص التالية: <code>data_folder</code> اختياري، وهو اسم مجلد البيانات الثنائية الذي يحتوي على البيانات الثنائية المقابلة. اسم الملف للبيانات الثنائية المقابلة داخل ذلك المجلد هو AACID للسجل. يجب أن يحتوي كل كائن JSON على الحقول التالية في المستوى الأعلى: <strong>aacid</strong>، <strong>metadata</strong>، <strong>data_folder</strong> (اختياري). لا يُسمح بأي حقول أخرى. يجب أن يكون اسم الملف نطاق AACID، مسبوقًا بـ <code style="color: red">annas_archive_meta__</code> ومتبوعة بـ <code>.jsonl.zstd</code>. على سبيل المثال، أحد إصداراتنا يسمى<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. كما هو موضح بواسطة امتداد الملف، نوع الملف هو <a %(jsonlines)s>JSON Lines</a> مضغوط باستخدام <a %(zstd)s>Zstandard</a>. <code>metadata</code> هو metadata عشوائي، وفقًا لدلالات المجموعة. يجب أن يكون متسقًا دلاليًا داخل المجموعة. يمكن تعديل البادئة <code style="color: red">annas_archive_meta__</code> لتتناسب مع اسم مؤسستك، على سبيل المثال <code style="color: red">my_institute_meta__</code>. <strong>“records” and “files” collections.</strong> وفقًا للعرف، غالبًا ما يكون من الملائم إصدار "السجلات" و"الملفات" كمجموعات مختلفة، بحيث يمكن إصدارها في جداول زمنية مختلفة، على سبيل المثال بناءً على معدلات الاستخلاص. "السجل" هو مجموعة تحتوي فقط على metadata، تحتوي على معلومات مثل عناوين الكتب، المؤلفين، أرقام ISBN، إلخ، بينما "الملفات" هي المجموعات التي تحتوي على الملفات الفعلية نفسها (pdf، epub). في النهاية، استقرينا على معيار بسيط نسبيًا. إنه مرن إلى حد ما، غير إلزامي، وقيد التطوير. <strong>التورنتات.</strong> يمكن تجميع ملفات الميتاداتا ومجلدات البيانات الثنائية في تورنتات، مع تورنت واحد لكل ملف ميتاداتا، أو تورنت واحد لكل مجلد بيانات ثنائية. يجب أن تحتوي التورنتات على اسم الملف/الدليل الأصلي بالإضافة إلى لاحقة <code>.torrent</code> كاسم الملف. <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> أصبحت إلى حد بعيد أكبر مكتبة ظل في العالم، والمكتبة الوحيدة من نوعها التي تكون مفتوحة المصدر والبيانات بالكامل. أدناه جدول من صفحة Datasets الخاصة بنا (تم تعديله قليلاً): حققنا ذلك بثلاث طرق: عكس مكتبات الظل المفتوحة البيانات الموجودة (مثل Sci-Hub وLibrary Genesis). مساعدة مكتبات الظل التي ترغب في أن تكون أكثر انفتاحًا، ولكن لم يكن لديها الوقت أو الموارد للقيام بذلك (مثل مجموعة Libgen للقصص المصورة). جمع البيانات من المكتبات التي لا ترغب في المشاركة بالجملة (مثل مكتبة الزّاي). بالنسبة للنقطتين (2) و(3)، نحن الآن ندير مجموعة كبيرة من التورنت بأنفسنا (مئات التيرابايت). حتى الآن، تعاملنا مع هذه المجموعات كحالات فردية، مما يعني بنية تحتية وتنظيم بيانات مخصص لكل مجموعة. هذا يضيف عبئًا كبيرًا لكل إصدار، ويجعل من الصعب بشكل خاص القيام بإصدارات تدريجية أكثر. لهذا السبب قررنا توحيد إصداراتنا. هذا منشور تقني نعرض فيه معيارنا: <strong>حاويات رَبيدةُ آنّا</strong>. حاويات رَبيدةُ آنّا (AAC): توحيد الإصدارات من أكبر مكتبة ظل في العالم أصبحت رَبيدةُ آنّا أكبر مكتبة ظل في العالم، مما يتطلب منا توحيد إصداراتنا. إصدار أكثر من 300 جيجابايت من أغلفة الكتب أخيرًا، نحن سعداء بالإعلان عن إصدار صغير. بالتعاون مع الأشخاص الذين يديرون فرع Libgen.rs، نحن نشارك جميع أغلفة كتبهم عبر التورنت وIPFS. سيوزع هذا الحمل من عرض الأغلفة بين المزيد من الأجهزة، وسيحافظ عليها بشكل أفضل. في العديد من الحالات (ولكن ليس كلها)، يتم تضمين أغلفة الكتب في الملفات نفسها، لذا فإن هذا نوع من "البيانات المشتقة". لكن وجودها في IPFS لا يزال مفيدًا جدًا للتشغيل اليومي لكل من رَبيدةُ آنّا والفروع المختلفة لـ Library Genesis. كالعادة، يمكنك العثور على هذا الإصدار في مرآة مكتبة القراصنة (ملاحظة: تم نقله إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>). لن نقوم بالربط إليه هنا، لكن يمكنك العثور عليه بسهولة. نأمل أن نتمكن من تخفيف وتيرتنا قليلاً، الآن بعد أن لدينا بديل لائق لمكتبة الزّاي. هذا العبء ليس مستدامًا بشكل خاص. إذا كنت مهتمًا بالمساعدة في البرمجة، أو عمليات الخادم، أو أعمال الحفظ، فلا تتردد في التواصل معنا. لا يزال هناك الكثير من <a %(annas_archive)s>العمل الذي يجب القيام به</a>. شكرًا لاهتمامك ودعمك. التحول إلى ElasticSearch بعض الاستفسارات استغرقت وقتًا طويلاً جدًا، إلى درجة أنها كانت تحتكر جميع الاتصالات المفتوحة. افتراضيًا، لدى MySQL طول كلمة أدنى، أو يمكن أن يصبح الفهرس كبيرًا جدًا. أبلغ الناس عن عدم قدرتهم على البحث عن "Ben Hur". كان البحث سريعًا إلى حد ما فقط عندما تم تحميله بالكامل في الذاكرة، مما تطلب منا الحصول على جهاز أكثر تكلفة لتشغيله، بالإضافة إلى بعض الأوامر لتحميل الفهرس عند بدء التشغيل. لم نكن لنتمكن من تمديده بسهولة لبناء ميزات جديدة، مثل <a %(wikipedia_cjk_characters)s>تحليل الرموز بشكل أفضل للغات غير المفصولة بمسافات</a>، التصفية/التصنيف، الفرز، اقتراحات "هل تقصد"، الإكمال التلقائي، وهكذا. كانت واحدة من <a %(annas_archive)s>التذاكر</a> لدينا عبارة عن مجموعة من المشاكل مع نظام البحث لدينا. استخدمنا بحث النص الكامل في MySQL، حيث كان لدينا جميع بياناتنا في MySQL على أي حال. لكن كان له حدوده: بعد التحدث إلى مجموعة من الخبراء، استقرينا على ElasticSearch. لم يكن مثاليًا (اقتراحات "هل تقصد" الافتراضية وميزات الإكمال التلقائي لديهم سيئة)، لكن بشكل عام كان أفضل بكثير من MySQL للبحث. ما زلنا لسنا <a %(youtube)s>متأكدين تمامًا</a> من استخدامه لأي بيانات حيوية (على الرغم من أنهم حققوا الكثير من <a %(elastic_co)s>التقدم</a>)، لكن بشكل عام نحن سعداء جدًا بالتحول. في الوقت الحالي، قمنا بتنفيذ بحث أسرع بكثير، دعم لغوي أفضل، فرز أفضل من حيث الصلة، خيارات فرز مختلفة، وتصفية على نوع اللغة/الكتاب/الملف. إذا كنت مهتمًا بكيفية عمله، <a %(annas_archive_l140)s>ألقِ</a> <a %(annas_archive_l1115)s>نظرة</a> <a %(annas_archive_l1635)s>عليه</a>. إنه متاح بشكل كبير، على الرغم من أنه يمكن أن يستخدم بعض التعليقات الإضافية… رَبيدةُ آنّا مفتوحة المصدر بالكامل نحن نؤمن بأن المعلومات يجب أن تكون مجانية، وكودنا الخاص ليس استثناءً. لقد أصدرنا كل كودنا على مثيل Gitlab المستضاف لدينا: <a %(annas_archive)s>برمجيات آنّا</a>. نستخدم أيضًا متعقب القضايا لتنظيم عملنا. إذا كنت ترغب في المشاركة في تطويرنا، فهذا مكان رائع للبدء. لإعطائك لمحة عن الأشياء التي نعمل عليها، خذ عملنا الأخير على تحسينات الأداء من جانب العميل. نظرًا لأننا لم ننفذ الترقيم بعد، كنا غالبًا ما نعيد صفحات بحث طويلة جدًا، مع 100-200 نتيجة. لم نرغب في قطع نتائج البحث في وقت مبكر جدًا، ولكن هذا يعني أنه سيبطئ بعض الأجهزة. لهذا، قمنا بتنفيذ خدعة صغيرة: قمنا بتغليف معظم نتائج البحث في تعليقات HTML (<code><!-- --></code>)، ثم كتبنا القليل من Javascript الذي يكتشف متى يجب أن تصبح النتيجة مرئية، في تلك اللحظة نقوم بفك التعليق: تم تنفيذ "الافتراضية" لـ DOM في 23 سطرًا فقط، دون الحاجة إلى مكتبات فاخرة! هذا هو نوع الكود العملي السريع الذي ينتهي بك الأمر إليه عندما يكون لديك وقت محدود ومشاكل حقيقية تحتاج إلى حل. لقد تم الإبلاغ عن أن بحثنا الآن يعمل بشكل جيد على الأجهزة البطيئة! كان جهدًا كبيرًا آخر هو أتمتة بناء قاعدة البيانات. عندما أطلقنا، قمنا بجمع مصادر مختلفة بشكل عشوائي. الآن نريد أن نبقيها محدثة، لذا كتبنا مجموعة من السكربتات لتنزيل metadata جديدة من فرعي Library Genesis ودمجها. الهدف ليس فقط جعل هذا مفيدًا لأرشيفنا، بل تسهيل الأمور لأي شخص يريد اللعب مع metadata مكتبة الظل. الهدف سيكون دفتر Jupyter يحتوي على جميع أنواع metadata المثيرة للاهتمام، حتى نتمكن من إجراء المزيد من الأبحاث مثل معرفة <a %(blog)s>نسبة ISBNs المحفوظة للأبد</a>. أخيرًا، قمنا بتجديد نظام التبرعات لدينا. يمكنك الآن استخدام بطاقة ائتمان لإيداع الأموال مباشرة في محافظنا المشفرة، دون الحاجة إلى معرفة أي شيء عن العملات المشفرة. سنواصل مراقبة مدى نجاح هذا في الممارسة، لكن هذا أمر كبير. مع توقف مكتبة الزّاي واعتقال مؤسسيها (المزعومين)، عملنا على مدار الساعة لتقديم بديل جيد مع رَبيدةُ آنّا (لن نقوم بربطها هنا، ولكن يمكنك البحث عنها في جوجل). إليكم بعض الأشياء التي حققناها مؤخرًا. تحديث آنّا: أرشيف مفتوح المصدر بالكامل، ElasticSearch، أكثر من 300 جيجابايت من أغلفة الكتب لقد عملنا على مدار الساعة لتقديم بديل جيد مع رَبيدةُ آنّا. إليكم بعض الأشياء التي حققناها مؤخرًا. التحليل يمكن نظريًا تصفية التكرارات الدلالية (مسوحات مختلفة لنفس الكتاب)، ولكنها صعبة. عند النظر يدويًا في القصص المصورة وجدنا الكثير من الإيجابيات الكاذبة. هناك بعض التكرارات فقط بواسطة MD5، وهو أمر غير فعال نسبيًا، ولكن تصفية تلك التكرارات ستوفر لنا حوالي 1% in من التوفير. على هذا النطاق، لا يزال ذلك حوالي 1 تيرابايت، ولكن أيضًا، على هذا النطاق، 1 تيرابايت لا يهم حقًا. نفضل عدم المخاطرة بتدمير البيانات عن طريق الخطأ في هذه العملية. وجدنا مجموعة من البيانات غير المتعلقة بالكتب، مثل الأفلام المستندة إلى القصص المصورة. يبدو ذلك أيضًا غير فعال، حيث أن هذه الأفلام متاحة بالفعل على نطاق واسع بوسائل أخرى. ومع ذلك، أدركنا أننا لا يمكننا فقط تصفية ملفات الأفلام، حيث توجد أيضًا <em>كتب قصص مصورة تفاعلية</em> تم إصدارها على الكمبيوتر، وقام شخص ما بتسجيلها وحفظها كأفلام. في النهاية، أي شيء يمكننا حذفه من المجموعة لن يوفر سوى بضع نسب مئوية. ثم تذكرنا أننا مهووسون بالبيانات، والأشخاص الذين سيقومون بعكس هذه البيانات هم أيضًا مهووسون بالبيانات، لذا، "ماذا تعني بالحذف؟!" :) عندما تحصل على 95 تيرابايت في مجموعة التخزين الخاصة بك، تحاول فهم ما يوجد هناك... قمنا ببعض التحليل لنرى ما إذا كان بإمكاننا تقليل الحجم قليلاً، مثل إزالة التكرارات. إليكم بعض من نتائجنا: لذلك نقدم لكم المجموعة الكاملة غير المعدلة. إنها كمية كبيرة من البيانات، لكننا نأمل أن يهتم عدد كافٍ من الأشخاص بنشرها على أي حال. التعاون نظرًا لحجمها، كانت هذه المجموعة على قائمة أمنياتنا منذ فترة طويلة، لذا بعد نجاحنا في النسخ الاحتياطي لمكتبة الزّاي، وضعنا أنظارنا على هذه المجموعة. في البداية قمنا بجمعها مباشرة، وكان ذلك تحديًا كبيرًا، حيث لم يكن الخادم في أفضل حالاته. حصلنا على حوالي 15 تيرابايت بهذه الطريقة، ولكن كان التقدم بطيئًا. لحسن الحظ، تمكنا من التواصل مع مشغل المكتبة، الذي وافق على إرسال جميع البيانات إلينا مباشرة، مما كان أسرع بكثير. استغرق الأمر أكثر من نصف عام لنقل ومعالجة جميع البيانات، وكدنا نفقدها جميعًا بسبب تلف القرص، مما كان يعني البدء من جديد. جعلتنا هذه التجربة نعتقد أنه من المهم نشر هذه البيانات في أسرع وقت ممكن، حتى يمكن نسخها على نطاق واسع. نحن على بعد حادثة أو اثنتين غير محظوظتين من فقدان هذه المجموعة إلى الأبد! المجموعة التحرك بسرعة يعني أن المجموعة غير منظمة قليلاً... دعونا نلقي نظرة. تخيل أن لدينا نظام ملفات (والذي في الواقع نقسمه عبر التورنت): الدليل الأول، <code>/repository</code>، هو الجزء الأكثر تنظيمًا من هذا. يحتوي هذا الدليل على ما يسمى "أدلة الألف": أدلة تحتوي كل منها على ألف ملف، يتم ترقيمها تدريجيًا في قاعدة البيانات. يحتوي الدليل <code>0</code> على ملفات مع comic_id من 0 إلى 999، وهكذا. هذا هو نفس النظام الذي استخدمته مكتبة جينيسيس لمجموعاتها من الخيال واللاخيال. الفكرة هي أن كل "دليل ألف" يتحول تلقائيًا إلى تورنت بمجرد امتلائه. ومع ذلك، لم يقم مشغل Libgen.li بإنشاء تورنت لهذه المجموعة، وبالتالي أصبحت أدلة الألف غير مريحة، وأفسحت المجال لـ "أدلة غير مرتبة". هذه هي <code>/comics0</code> إلى <code>/comics4</code>. تحتوي جميعها على هياكل دليل فريدة، ربما كانت منطقية لجمع الملفات، لكنها لا تبدو منطقية بالنسبة لنا الآن. لحسن الحظ، لا تزال metadata تشير مباشرة إلى جميع هذه الملفات، لذا فإن تنظيمها على القرص لا يهم فعليًا! تتوفر metadata في شكل قاعدة بيانات MySQL. يمكن تنزيلها مباشرة من موقع Libgen.li، لكننا سنوفرها أيضًا في تورنت، إلى جانب جدولنا الخاص بجميع تجزئات MD5. <q>تحاول الدكتورة باربرا جوردون أن تفقد نفسها في العالم العادي للمكتبة...</q> تفرعات Libgen أولاً، بعض الخلفية. قد تعرفون مكتبة جينيسيس لمجموعتها الضخمة من الكتب. عدد أقل من الناس يعرف أن متطوعي مكتبة جينيسيس قد أنشأوا مشاريع أخرى، مثل مجموعة كبيرة من المجلات والوثائق القياسية، ونسخة احتياطية كاملة من Sci-Hub (بالتعاون مع مؤسسة Sci-Hub، ألكسندرا إلباكيان)، وبالفعل، مجموعة ضخمة من القصص المصورة. في مرحلة ما، ذهب مشغلو مرايا مكتبة جينيسيس في طرقهم المنفصلة، مما أدى إلى الوضع الحالي بوجود عدد من "التفرعات" المختلفة، وكلها لا تزال تحمل اسم مكتبة جينيسيس. تفرع Libgen.li يحتوي بشكل فريد على هذه المجموعة من القصص المصورة، بالإضافة إلى مجموعة كبيرة من المجلات (التي نعمل عليها أيضًا). جمع التبرعات نحن نطلق هذه البيانات في بعض الأجزاء الكبيرة. أول تورنت هو <code>/comics0</code>، الذي وضعناه في ملف .tar ضخم بحجم 12 تيرابايت. هذا أفضل لمحرك الأقراص الصلب وبرامج التورنت من عدد لا يحصى من الملفات الصغيرة. كجزء من هذا الإصدار، نقوم بجمع التبرعات. نحن نسعى لجمع 20,000 دولار لتغطية تكاليف التشغيل والتعاقد لهذه المجموعة، وكذلك تمكين المشاريع الجارية والمستقبلية. لدينا بعض المشاريع <em>الضخمة</em> قيد التنفيذ. <em>من أدعم بتبرعي؟</em> باختصار: نحن ندعم جميع المعرفة والثقافة الإنسانية، ونجعلها متاحة بسهولة. كل شفراتنا وبياناتنا مفتوحة المصدر، نحن مشروع يديره متطوعون بالكامل، وقد أنقذنا حتى الآن 125 تيرابايت من الكتب (بالإضافة إلى تورنتات Libgen وScihub الموجودة). في النهاية، نحن نبني عجلة دوارة تمكن وتحفز الناس على العثور على جميع الكتب في العالم ومسحها ضوئيًا ونسخها احتياطيًا. سنكتب عن خطتنا الرئيسية في منشور مستقبلي. :) إذا تبرعت للحصول على عضوية "Amazing Archivist" لمدة 12 شهرًا (780 دولارًا)، يمكنك <strong>“تبني تورنت”</strong>، مما يعني أننا سنضع اسم المستخدم أو الرسالة الخاصة بك في اسم ملف أحد التورنتات! يمكنك التبرع بالذهاب إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> والنقر على زر "تبرع". نحن أيضًا نبحث عن المزيد من المتطوعين: مهندسي البرمجيات، باحثي الأمن، خبراء التجارة المجهولين، والمترجمين. يمكنك أيضًا دعمنا بتقديم خدمات الاستضافة. وبالطبع، يرجى نشر تورنتاتنا! شكرًا لكل من دعمنا بسخاء حتى الآن! أنتم حقًا تحدثون فرقًا. إليكم التورنتات التي تم إصدارها حتى الآن (ما زلنا نعالج الباقي): يمكن العثور على جميع التورنتات على <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> تحت "Datasets" (نحن لا نربط هناك مباشرة، حتى لا تتم إزالة الروابط إلى هذه المدونة من Reddit وTwitter وما إلى ذلك). من هناك، اتبع الرابط إلى موقع Tor. <a %(news_ycombinator)s>ناقش على Hacker News</a> ما التالي؟ مجموعة من التورنتات رائعة للحفظ طويل الأمد، لكنها ليست كذلك للوصول اليومي. سنعمل مع شركاء الاستضافة للحصول على كل هذه البيانات على الويب (حيث أن رَبيدةُ آنّا لا تستضيف أي شيء مباشرة). بالطبع ستتمكن من العثور على روابط التنزيل هذه على رَبيدةُ آنّا. نحن ندعو الجميع أيضًا للقيام بأشياء مع هذه البيانات! ساعدنا في تحليلها بشكل أفضل، وإزالة التكرار، ووضعها على IPFS، وإعادة مزجها، وتدريب نماذج الذكاء الاصطناعي الخاصة بك بها، وما إلى ذلك. إنها كلها لك، ولا يمكننا الانتظار لرؤية ما ستفعله بها. أخيرًا، كما قيل من قبل، لا يزال لدينا بعض الإصدارات الضخمة القادمة (إذا <em>شخص ما</em> يمكنه <em>بالصدفة</em> إرسال تفريغ لقاعدة بيانات <em>معينة</em> ACS4، فأنت تعرف أين تجدنا...)، بالإضافة إلى بناء العجلة الدوارة لنسخ جميع الكتب في العالم احتياطيًا. لذا ابقوا متابعين، نحن فقط بدأنا. - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) أكبر مكتبة ظل للقصص المصورة هي على الأرجح تلك الخاصة بفرع معين من Library Genesis: Libgen.li. تمكن المسؤول الوحيد الذي يدير هذا الموقع من جمع مجموعة هائلة من القصص المصورة تضم أكثر من 2 مليون ملف، بإجمالي يزيد عن 95 تيرابايت. ومع ذلك، على عكس مجموعات Library Genesis الأخرى، لم تكن هذه المجموعة متاحة بشكل جماعي عبر التورنت. كان بإمكانك الوصول إلى هذه القصص المصورة بشكل فردي فقط عبر خادمه الشخصي البطيء — نقطة فشل واحدة. حتى اليوم! في هذا المنشور، سنخبركم المزيد عن هذه المجموعة وعن حملتنا لجمع التبرعات لدعم المزيد من هذا العمل. رَبيدةُ آنّا قامت بنسخ أكبر مكتبة ظل للقصص المصورة في العالم (95 تيرابايت) — يمكنك المساعدة في توزيعها أكبر مكتبة ظل للقصص المصورة في العالم كانت تحتوي على نقطة فشل واحدة.. حتى اليوم. تحذير: تم إهمال هذه التدوينة. لقد قررنا أن IPFS ليست جاهزة بعد للوقت الرئيسي. سنظل نربط الملفات على IPFS من رَبيدةُ آنّا عندما يكون ذلك ممكنًا، لكننا لن نستضيفها بأنفسنا بعد الآن، ولا نوصي الآخرين بعكسها باستخدام IPFS. يرجى الاطلاع على صفحة التورنت الخاصة بنا إذا كنت تريد المساعدة في الحفاظ على مجموعتنا. وضع 5,998,794 كتابًا على IPFS تكرار النسخ بالعودة إلى سؤالنا الأصلي: كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد؟ المشكلة الرئيسية هنا هي أن مجموعتنا كانت <a %(torrents_stats)s>تنمو</a> بسرعة كبيرة، من خلال الكشط والمصدر المفتوح لبعض المجموعات الضخمة (بالإضافة إلى العمل الرائع الذي قامت به بالفعل مكتبات الظل المفتوحة البيانات مثل Sci-Hub وLibrary Genesis). هذا النمو في البيانات يجعل من الصعب عكس المجموعات حول العالم. تخزين البيانات مكلف! لكننا متفائلون، خاصة عند ملاحظة الاتجاهات الثلاثة التالية. <a %(annas_archive_stats)s>الحجم الإجمالي</a> لمجموعاتنا، خلال الأشهر القليلة الماضية، مقسمًا حسب عدد موزعي التورنت. اتجاهات أسعار HDD من مصادر مختلفة (انقر لعرض الدراسة). <a %(critical_window_chinese)s>النسخة الصينية 中文版</a>، ناقش على <a %(reddit)s>Reddit</a>، <a %(news_ycombinator)s>Hacker News</a> 1. لقد قطفنا الثمار السهلة هذا يتبع مباشرة من أولوياتنا التي نوقشت أعلاه. نفضل العمل على تحرير المجموعات الكبيرة أولاً. الآن بعد أن قمنا بتأمين بعض أكبر المجموعات في العالم، نتوقع أن يكون نمونا أبطأ بكثير. لا يزال هناك ذيل طويل من المجموعات الأصغر، ويتم مسح الكتب الجديدة أو نشرها كل يوم، لكن المعدل سيكون على الأرجح أبطأ بكثير. قد نضاعف أو حتى نضاعف حجمنا ثلاث مرات، ولكن على مدى فترة زمنية أطول. تحسينات التعرف الضوئي على الحروف. الأولويات برامج العلوم والهندسة الإصدارات الخيالية أو الترفيهية لكل ما سبق البيانات الجغرافية (مثل الخرائط، المسوحات الجيولوجية) البيانات الداخلية من الشركات أو الحكومات (التسريبات) بيانات القياس مثل القياسات العلمية، البيانات الاقتصادية، التقارير المؤسسية سجلات metadata بشكل عام (للأعمال غير الخيالية والخيالية؛ للوسائط الأخرى، الفن، الأشخاص، إلخ؛ بما في ذلك المراجعات) الكتب غير الخيالية المجلات غير الخيالية، الصحف، الكتيبات النصوص غير الخيالية للمحادثات، الوثائقيات، البودكاست البيانات العضوية مثل تسلسلات الحمض النووي، بذور النباتات، أو عينات الميكروبات الأوراق الأكاديمية، المجلات، التقارير مواقع العلوم والهندسة، المناقشات عبر الإنترنت نصوص الإجراءات القانونية أو القضائية معرضة بشكل فريد لخطر التدمير (مثل الحرب، تخفيضات التمويل، الدعاوى القضائية، أو الاضطهاد السياسي) نادرة غير مركزة بشكل فريد لماذا نهتم كثيرًا بالأوراق والكتب؟ دعونا نضع جانبًا اعتقادنا الأساسي في الحفظ بشكل عام - قد نكتب منشورًا آخر حول ذلك. إذًا لماذا الأوراق والكتب تحديدًا؟ الإجابة بسيطة: <strong>كثافة المعلومات</strong>. لكل ميغابايت من التخزين، يخزن النص المكتوب أكبر قدر من المعلومات من بين جميع الوسائط. بينما نهتم بالمعرفة والثقافة على حد سواء، فإننا نهتم أكثر بالأولى. بشكل عام، نجد تسلسلًا هرميًا لكثافة المعلومات وأهمية الحفظ يبدو تقريبًا هكذا: الترتيب في هذه القائمة هو إلى حد ما اعتباطي - بعض العناصر متعادلة أو هناك خلافات داخل فريقنا - وربما ننسى بعض الفئات المهمة. لكن هذا هو تقريباً كيف نحدد الأولويات. بعض هذه العناصر مختلفة جداً عن الأخرى بحيث لا نقلق بشأنها (أو يتم الاعتناء بها بالفعل من قبل مؤسسات أخرى)، مثل البيانات العضوية أو البيانات الجغرافية. لكن معظم العناصر في هذه القائمة مهمة بالنسبة لنا. عامل كبير آخر في تحديد أولوياتنا هو مدى تعرض عمل معين للخطر. نفضل التركيز على الأعمال التي هي: أخيراً، نهتم بالحجم. لدينا وقت ومال محدود، لذا نفضل قضاء شهر في إنقاذ 10,000 كتاب بدلاً من 1,000 كتاب - إذا كانت ذات قيمة متساوية تقريباً ومعرضة للخطر. <em><q>لا يمكن استعادة المفقود؛ لكن دعونا نحفظ ما تبقى: ليس عن طريق الخزائن والأقفال التي تحميها من أعين الجمهور واستخدامها، في تسليمها إلى هدر الوقت، ولكن عن طريق مثل هذا التكرار للنسخ، الذي يضعها خارج نطاق الحوادث.</q></em><br>— توماس جيفرسون، 1791 المكتبات الظلية يمكن أن يكون الكود مفتوح المصدر على Github، لكن Github ككل لا يمكن عكسه بسهولة وبالتالي الحفاظ عليه (على الرغم من أنه في هذه الحالة الخاصة هناك نسخ موزعة بشكل كافٍ لمعظم مستودعات الكود) يمكن عرض سجلات metadata بحرية على موقع Worldcat، ولكن لا يمكن تنزيلها بكميات كبيرة (حتى نقوم <a %(worldcat_scrape)s>بكشطها</a>) Reddit مجاني للاستخدام، لكنه وضع مؤخرًا تدابير صارمة ضد الكشط، في أعقاب تدريب LLM الجائع للبيانات (المزيد عن ذلك لاحقًا) هناك العديد من المنظمات التي لديها مهام مشابهة، وأولويات مشابهة. في الواقع، هناك مكتبات، أرشيفات، مختبرات، متاحف، ومؤسسات أخرى مكلفة بالحفاظ على هذا النوع. العديد من هذه المؤسسات ممولة بشكل جيد، من قبل الحكومات، الأفراد، أو الشركات. لكن لديهم نقطة عمياء ضخمة: النظام القانوني. هنا يكمن الدور الفريد للمكتبات الظلية، والسبب في وجود رَبيدةُ آنّا. يمكننا القيام بأشياء لا يُسمح للمؤسسات الأخرى القيام بها. الآن، ليس (غالباً) أننا نستطيع أرشفة المواد التي من غير القانوني الحفاظ عليها في أماكن أخرى. لا، من القانوني في العديد من الأماكن بناء أرشيف مع أي كتب، أوراق، مجلات، وهكذا. لكن ما تفتقر إليه الأرشيفات القانونية غالبًا هو <strong>التكرار وطول الأمد</strong>. هناك كتب لا يوجد منها سوى نسخة واحدة في مكتبة مادية ما في مكان ما. هناك سجلات metadata محمية من قبل شركة واحدة. هناك صحف محفوظة فقط على الميكروفيلم في أرشيف واحد. يمكن أن تتعرض المكتبات لخفض التمويل، ويمكن أن تفلس الشركات، ويمكن أن تُقصف الأرشيفات وتُحرق حتى الأرض. هذا ليس افتراضياً - يحدث هذا طوال الوقت. الشيء الذي يمكننا القيام به بشكل فريد في رَبيدةُ آنّا هو تخزين العديد من النسخ من الأعمال، على نطاق واسع. يمكننا جمع الأوراق والكتب والمجلات والمزيد، وتوزيعها بكميات كبيرة. نقوم حاليًا بذلك من خلال التورنت، لكن التقنيات الدقيقة لا تهم وستتغير بمرور الوقت. الجزء المهم هو توزيع العديد من النسخ في جميع أنحاء العالم. لا يزال هذا الاقتباس من أكثر من 200 عام صحيحًا: ملاحظة سريعة حول الملكية العامة. نظرًا لأن رَبيدةُ آنّا تركز بشكل فريد على الأنشطة التي تعتبر غير قانونية في العديد من الأماكن حول العالم، فإننا لا نهتم بالمجموعات المتاحة على نطاق واسع، مثل الكتب العامة. غالبًا ما تهتم الكيانات القانونية بذلك بشكل جيد. ومع ذلك، هناك اعتبارات تجعلنا نعمل أحيانًا على المجموعات المتاحة للجمهور: - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) 2. تكاليف التخزين تستمر في الانخفاض بشكل كبير 3. تحسينات في كثافة المعلومات نحن حالياً نخزن الكتب في الصيغ الخام التي تُعطى لنا. بالطبع، هي مضغوطة، ولكن غالباً ما تكون لا تزال مسحاً ضوئياً كبيراً أو صوراً للصفحات. حتى الآن، كانت الخيارات الوحيدة لتقليص الحجم الكلي لمجموعتنا هي من خلال ضغط أكثر عدوانية، أو إزالة التكرار. ومع ذلك، للحصول على توفير كبير بما فيه الكفاية، فإن كلاهما يفقد الكثير من الجودة بالنسبة لنا. يمكن أن يجعل الضغط الثقيل للصور النص بالكاد مقروءاً. وتتطلب إزالة التكرار ثقة عالية بأن الكتب هي نفسها تماماً، وهو ما يكون غالباً غير دقيق، خاصة إذا كانت المحتويات هي نفسها ولكن المسح الضوئي تم في مناسبات مختلفة. كان هناك دائماً خيار ثالث، لكن جودته كانت سيئة للغاية لدرجة أننا لم نعتبره: <strong>التعرف الضوئي على الحروف (OCR)</strong>. هذه هي عملية تحويل الصور إلى نص عادي، باستخدام الذكاء الاصطناعي لاكتشاف الحروف في الصور. لقد وجدت أدوات لهذا منذ فترة طويلة، وكانت جيدة إلى حد ما، ولكن "جيدة إلى حد ما" ليست كافية لأغراض الحفظ. ومع ذلك، فإن النماذج الحديثة للتعلم العميق متعددة الوسائط قد أحرزت تقدماً سريعاً للغاية، رغم أنها لا تزال بتكاليف عالية. نتوقع أن تتحسن الدقة والتكاليف بشكل كبير في السنوات القادمة، إلى النقطة التي يصبح فيها من الواقعي تطبيقها على مكتبتنا بأكملها. عندما يحدث ذلك، من المحتمل أن نحافظ على الملفات الأصلية، ولكن بالإضافة إلى ذلك يمكن أن يكون لدينا نسخة أصغر بكثير من مكتبتنا التي سيرغب معظم الناس في عكسها. النقطة المهمة هي أن النص الخام نفسه يضغط بشكل أفضل، ومن الأسهل بكثير إزالة التكرار، مما يوفر لنا المزيد من التوفير. بشكل عام، ليس من غير الواقعي توقع تقليل حجم الملفات الإجمالي بنسبة 5-10 مرات على الأقل، وربما أكثر. حتى مع تقليل محافظ بنسبة 5 مرات، سننظر إلى <strong>1000-3000 دولار في 10 سنوات حتى لو تضاعف حجم مكتبتنا ثلاث مرات</strong>. في وقت كتابة هذا التقرير، <a %(diskprices)s>أسعار الأقراص</a> لكل تيرابايت حوالي 12 دولارًا للأقراص الجديدة، و8 دولارات للأقراص المستخدمة، و4 دولارات للشريط. إذا كنا محافظين وننظر فقط إلى الأقراص الجديدة، فهذا يعني أن تخزين بيتابايت يكلف حوالي 12,000 دولار. إذا افترضنا أن مكتبتنا ستتضاعف ثلاث مرات من 900 تيرابايت إلى 2.7 بيتابايت، فهذا يعني 32,400 دولار لعكس مكتبتنا بالكامل. بإضافة الكهرباء وتكلفة الأجهزة الأخرى وما إلى ذلك، دعونا نرفعها إلى 40,000 دولار. أو مع الشريط أكثر مثل 15,000–20,000 دولار. من ناحية <strong>15,000–40,000 دولار لمجموع المعرفة البشرية هو صفقة رابحة</strong>. من ناحية أخرى، من الصعب توقع الكثير من النسخ الكاملة، خاصة إذا كنا نود أيضًا أن يستمر هؤلاء الأشخاص في تحميل التورنت لصالح الآخرين. هذا هو اليوم. لكن التقدم يسير إلى الأمام: تم تخفيض تكاليف الأقراص الصلبة لكل تيرابايت إلى الثلث تقريبًا خلال السنوات العشر الماضية، ومن المحتمل أن تستمر في الانخفاض بوتيرة مماثلة. يبدو أن الشريط يسير في مسار مشابه. أسعار SSD تنخفض بشكل أسرع، وقد تتفوق على أسعار HDD بحلول نهاية العقد. إذا استمر هذا، فقد ننظر في غضون 10 سنوات إلى 5,000–13,000 دولار فقط لعكس مجموعتنا بالكامل (1/3)، أو حتى أقل إذا نمونا أقل في الحجم. وبينما لا يزال الكثير من المال، سيكون هذا متاحًا للعديد من الأشخاص. وقد يكون الأمر أفضل بسبب النقطة التالية… في رَبيدةُ آنّا، غالبًا ما يُسألنا كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد، عندما يكون الحجم الإجمالي يقترب بالفعل من 1 بيتابايت (1000 تيرابايت)، ولا يزال ينمو. في هذه المقالة سننظر في فلسفتنا، ونرى لماذا العقد القادم حاسم لمهمتنا في الحفاظ على معرفة وثقافة البشرية. نافذة حرجة إذا كانت هذه التوقعات دقيقة، فنحن <strong>نحتاج فقط إلى الانتظار لبضع سنوات</strong> قبل أن يتم عكس مجموعتنا بالكامل على نطاق واسع. وبالتالي، في كلمات توماس جيفرسون، "وضعت خارج نطاق الحوادث". لسوء الحظ، فإن ظهور LLMs، وتدريبها الذي يتطلب الكثير من البيانات، قد وضع العديد من أصحاب حقوق الطبع والنشر في موقف دفاعي. أكثر مما كانوا عليه بالفعل. العديد من المواقع تجعل من الصعب جمع البيانات وأرشفتها، والدعاوى القضائية تتطاير، وفي الوقت نفسه تستمر المكتبات والأرشيفات الفيزيائية في الإهمال. يمكننا فقط توقع استمرار هذه الاتجاهات في التفاقم، وفقدان العديد من الأعمال قبل أن تدخل المجال العام. <strong>نحن على أعتاب ثورة في الحفظ، ولكن <q>ما فقد لا يمكن استعادته.</q></strong> لدينا نافذة حرجة لمدة 5-10 سنوات تقريباً حيث لا يزال من المكلف تشغيل مكتبة الظل وإنشاء العديد من العاكسات حول العالم، وحيث لم يتم إغلاق الوصول تماماً بعد. إذا تمكنا من تجاوز هذه النافذة، فسنكون قد حفظنا بالفعل معرفة وثقافة البشرية إلى الأبد. يجب ألا ندع هذا الوقت يضيع. يجب ألا ندع هذه النافذة الحرجة تغلق علينا. لننطلق. النافذة الحرجة للمكتبات الظلية كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد، عندما تقترب بالفعل من 1 بيتابايت؟ مجموعة بعض المعلومات الإضافية عن المجموعة. <a %(duxiu)s>Duxiu</a> هي قاعدة بيانات ضخمة للكتب الممسوحة ضوئيًا، أنشأتها <a %(chaoxing)s>مجموعة المكتبة الرقمية سوبرستار</a>. معظمها كتب أكاديمية، تم مسحها ضوئيًا لجعلها متاحة رقميًا للجامعات والمكتبات. لجمهورنا الناطق باللغة الإنجليزية، <a %(library_princeton)s>برينستون</a> و<a %(guides_lib_uw)s>جامعة واشنطن</a> لديهما نظرات عامة جيدة. هناك أيضًا مقال ممتاز يقدم المزيد من الخلفية: <a %(doi)s>“رقمنة الكتب الصينية: دراسة حالة لمحرك البحث سوبرستار دوكسيو”</a> (ابحث عنه في رَبيدةُ آنّا). تم قرصنة الكتب من Duxiu منذ فترة طويلة على الإنترنت الصيني. عادة ما يتم بيعها بأقل من دولار من قبل البائعين. يتم توزيعها عادة باستخدام ما يعادل Google Drive الصيني، والذي تم اختراقه غالبًا للسماح بمساحة تخزين أكبر. يمكن العثور على بعض التفاصيل التقنية <a %(github_duty_machine)s>هنا</a> و<a %(github_821_github_io)s>هنا</a>. على الرغم من أن الكتب تم توزيعها بشكل شبه عام، إلا أنه من الصعب جدًا الحصول عليها بكميات كبيرة. كان لدينا هذا في قائمة المهام الخاصة بنا، وخصصنا عدة أشهر من العمل بدوام كامل لذلك. ومع ذلك، مؤخرًا تواصل معنا متطوع رائع ومذهل وموهوب، وأخبرنا أنه قام بكل هذا العمل بالفعل - بتكلفة كبيرة. شارك المجموعة الكاملة معنا، دون توقع أي شيء في المقابل، باستثناء ضمان الحفظ طويل الأمد. حقًا مذهل. وافقوا على طلب المساعدة بهذه الطريقة للحصول على المجموعة OCR'ed. المجموعة تحتوي على 7,543,702 ملف. هذا أكثر من مكتبة جينيسيس للكتب غير الخيالية (حوالي 5.3 مليون). الحجم الإجمالي للملفات حوالي 359 تيرابايت (326 تيبيبايت) في شكلها الحالي. نحن منفتحون على مقترحات وأفكار أخرى. فقط تواصل معنا. تحقق من رَبيدةُ آنّا لمزيد من المعلومات حول مجموعاتنا وجهود الحفظ، وكيف يمكنك المساعدة. شكرًا! صفحات مثال لإثبات لنا أن لديك خط أنابيب جيد، إليك بعض الصفحات النموذجية للبدء بها، من كتاب عن الموصلات الفائقة. يجب أن يتعامل خط الأنابيب الخاص بك بشكل صحيح مع الرياضيات والجداول والرسوم البيانية والحواشي وما إلى ذلك. أرسل صفحاتك المعالجة إلى بريدنا الإلكتروني. إذا كانت تبدو جيدة، سنرسل لك المزيد بشكل خاص، ونتوقع أن تكون قادرًا على تشغيل خط الأنابيب الخاص بك بسرعة على تلك أيضًا. بمجرد أن نكون راضين، يمكننا إبرام صفقة. - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>النسخة الصينية 中文版</a>، <a %(news_ycombinator)s>ناقش على Hacker News</a> هذه تدوينة قصيرة. نحن نبحث عن شركة أو مؤسسة لمساعدتنا في التعرف الضوئي على الحروف واستخراج النصوص لمجموعة ضخمة حصلنا عليها، مقابل الوصول الحصري المبكر. بعد فترة الحظر، سنقوم بالطبع بإصدار المجموعة بأكملها. النص الأكاديمي عالي الجودة مفيد للغاية لتدريب LLMs. بينما مجموعتنا صينية، يجب أن يكون هذا مفيدًا حتى لتدريب LLMs الإنجليزية: يبدو أن النماذج تشفر المفاهيم والمعرفة بغض النظر عن لغة المصدر. لهذا، يجب استخراج النص من المسح الضوئي. ماذا تستفيد رَبيدةُ آنّا من ذلك؟ البحث النصي الكامل في الكتب لمستخدميها. لأن أهدافنا تتماشى مع أهداف مطوري LLM، نحن نبحث عن متعاون. نحن على استعداد لمنحك <strong>وصولًا حصريًا مبكرًا إلى هذه المجموعة بكميات كبيرة لمدة عام واحد</strong>، إذا كنت تستطيع القيام بعملية OCR واستخراج النص بشكل صحيح. إذا كنت على استعداد لمشاركة الكود الكامل لخط الأنابيب الخاص بك معنا، سنكون على استعداد لحظر المجموعة لفترة أطول. الوصول الحصري لشركات LLM إلى أكبر مجموعة كتب غير خيالية صينية في العالم <em><strong>ملخص:</strong> رَبيدةُ آنّا حصلت على مجموعة فريدة من 7.5 مليون / 350 تيرابايت من الكتب الصينية غير الخيالية — أكبر من Library Genesis. نحن على استعداد لمنح شركة LLM وصولاً حصرياً، مقابل التعرف الضوئي على الحروف عالي الجودة واستخراج النصوص.</em> هندسة النظام لنفترض أنك وجدت بعض الشركات التي ترغب في استضافة موقعك على الويب دون إغلاقه - دعنا نسميها "مزودي الحرية المحبين" 😄. ستجد بسرعة أن استضافة كل شيء معهم مكلفة للغاية، لذا قد ترغب في العثور على بعض "المزودين الرخيصين" والقيام بالاستضافة الفعلية هناك، مع التوجيه عبر مزودي الحرية المحبين. إذا قمت بذلك بشكل صحيح، فلن يعرف المزودون الرخيصون أبدًا ما الذي تستضيفه، ولن يتلقوا أي شكاوى. مع كل هؤلاء المزودين، هناك خطر من إغلاقهم لك على أي حال، لذا تحتاج أيضًا إلى التكرار. نحن بحاجة إلى ذلك على جميع مستويات البنية التحتية لدينا. شركة واحدة محبة للحرية وضعت نفسها في موقف مثير للاهتمام هي Cloudflare. لقد <a %(blog_cloudflare)s>جادلوا</a> بأنهم ليسوا مزود استضافة، بل مرفق، مثل مزود خدمة الإنترنت. لذلك، هم ليسوا خاضعين لطلبات الإزالة بموجب DMCA أو غيرها، ويقومون بإحالة أي طلبات إلى مزود الاستضافة الفعلي الخاص بك. لقد ذهبوا إلى حد الذهاب إلى المحكمة لحماية هذا الهيكل. لذلك يمكننا استخدامهم كطبقة أخرى من التخزين المؤقت والحماية. Cloudflare لا تقبل المدفوعات المجهولة، لذا يمكننا فقط استخدام خطتهم المجانية. هذا يعني أننا لا يمكننا استخدام ميزات التوازن في التحميل أو الفشل. لذلك <a %(annas_archive_l255)s>قمنا بتنفيذ ذلك بأنفسنا</a> على مستوى النطاق. عند تحميل الصفحة، سيتحقق المتصفح مما إذا كان النطاق الحالي لا يزال متاحًا، وإذا لم يكن كذلك، فإنه يعيد كتابة جميع عناوين URL إلى نطاق مختلف. نظرًا لأن Cloudflare تخزن العديد من الصفحات مؤقتًا، فهذا يعني أن المستخدم يمكنه الوصول إلى نطاقنا الرئيسي، حتى إذا كان خادم الوكيل معطلاً، ثم في النقر التالي يتم نقله إلى نطاق آخر. لا يزال لدينا أيضًا مخاوف تشغيلية عادية للتعامل معها، مثل مراقبة صحة الخادم، وتسجيل الأخطاء في الواجهة الخلفية والأمامية، وما إلى ذلك. تتيح لنا بنية الفشل لدينا مزيدًا من المتانة في هذا الجانب أيضًا، على سبيل المثال عن طريق تشغيل مجموعة مختلفة تمامًا من الخوادم على أحد النطاقات. يمكننا حتى تشغيل إصدارات أقدم من الكود وDatasets على هذا النطاق المنفصل، في حالة عدم ملاحظة خطأ حرج في الإصدار الرئيسي. يمكننا أيضًا التحوط ضد تحول Cloudflare ضدنا، عن طريق إزالته من أحد النطاقات، مثل هذا النطاق المنفصل. يمكن تنفيذ تركيبات مختلفة من هذه الأفكار. الخاتمة لقد كانت تجربة مثيرة للاهتمام لتعلم كيفية إعداد محرك بحث مكتبة الظل قوي ومرن. هناك الكثير من التفاصيل لمشاركتها في منشورات لاحقة، لذا دعني أعرف ما الذي تود معرفة المزيد عنه! كما هو الحال دائمًا، نحن نبحث عن التبرعات لدعم هذا العمل، لذا تأكد من زيارة صفحة التبرع في رَبيدةُ آنّا. نحن نبحث أيضًا عن أنواع أخرى من الدعم، مثل المنح، الرعاة على المدى الطويل، مزودي الدفع عالي المخاطر، وربما حتى (إعلانات ذوقية!). وإذا كنت ترغب في المساهمة بوقتك ومهاراتك، فنحن دائمًا نبحث عن مطورين، مترجمين، وما إلى ذلك. شكرًا لاهتمامك ودعمك. رموز الابتكار لنبدأ بتقنية البنية التحتية لدينا. إنها مملة بشكل متعمد. نحن نستخدم Flask وMariaDB وElasticSearch. هذا هو كل شيء حرفيًا. البحث هو مشكلة تم حلها إلى حد كبير، ولا ننوي إعادة اختراعها. بالإضافة إلى ذلك، علينا أن ننفق <a %(mcfunley)s>رموز الابتكار</a> الخاصة بنا على شيء آخر: عدم التعرض للإزالة من قبل السلطات. إذًا، ما مدى قانونية أو عدم قانونية رَبيدةُ آنّا بالضبط؟ هذا يعتمد في الغالب على الولاية القضائية القانونية. تعتقد معظم الدول في شكل من أشكال حقوق الطبع والنشر، مما يعني أن الأشخاص أو الشركات يتم تعيينهم احتكارًا حصريًا على أنواع معينة من الأعمال لفترة زمنية معينة. كجانب جانبي، في رَبيدةُ آنّا نعتقد أنه بينما هناك بعض الفوائد، فإن حقوق الطبع والنشر بشكل عام هي سلبية صافية للمجتمع - لكن هذه قصة لوقت آخر. هذا الاحتكار الحصري على بعض الأعمال يعني أنه من غير القانوني لأي شخص خارج هذا الاحتكار توزيع تلك الأعمال مباشرة - بما في ذلك نحن. لكن رَبيدةُ آنّا هو محرك بحث لا يوزع تلك الأعمال مباشرة (على الأقل ليس على موقعنا على الويب في الشبكة النظيفة)، لذا يجب أن نكون بخير، أليس كذلك؟ ليس بالضبط. في العديد من الولايات القضائية، ليس من غير القانوني فقط توزيع الأعمال المحمية بحقوق الطبع والنشر، ولكن أيضًا الربط بأماكن تقوم بذلك. مثال كلاسيكي على ذلك هو قانون DMCA في الولايات المتحدة. هذا هو الطرف الأكثر صرامة من الطيف. في الطرف الآخر من الطيف، يمكن أن تكون هناك دول نظريًا بدون قوانين حقوق الطبع والنشر على الإطلاق، لكن هذه لا توجد حقًا. تقريبًا كل دولة لديها شكل من أشكال قانون حقوق الطبع والنشر في الكتب. التنفيذ هو قصة مختلفة. هناك الكثير من الدول التي لا تهتم حكوماتها بتنفيذ قانون حقوق الطبع والنشر. هناك أيضًا دول بين النقيضين، التي تحظر توزيع الأعمال المحمية بحقوق الطبع والنشر، ولكن لا تحظر الربط بتلك الأعمال. اعتبار آخر هو على مستوى الشركة. إذا كانت الشركة تعمل في ولاية قضائية لا تهتم بحقوق الطبع والنشر، ولكن الشركة نفسها ليست مستعدة لتحمل أي مخاطر، فقد تغلق موقعك على الويب بمجرد أن يشتكي أي شخص منه. أخيرًا، اعتبار كبير هو المدفوعات. نظرًا لأننا بحاجة إلى البقاء مجهولين، لا يمكننا استخدام طرق الدفع التقليدية. هذا يترك لنا العملات المشفرة، وفقط مجموعة صغيرة من الشركات تدعمها (هناك بطاقات خصم افتراضية مدفوعة بالعملات المشفرة، لكنها غالبًا غير مقبولة). - آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) أدير <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر محرك بحث مفتوح المصدر غير ربحي في العالم لـ<a %(wikipedia_shadow_library)s>مكتبات الظل</a>، مثل Sci-Hub وLibrary Genesis وZ-Library. هدفنا هو جعل المعرفة والثقافة متاحة بسهولة، وفي النهاية بناء مجتمع من الأشخاص الذين يقومون معًا بأرشفة وحفظ <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>جميع الكتب في العالم</a>. في هذه المقالة سأوضح كيف ندير هذا الموقع، والتحديات الفريدة التي تأتي مع تشغيل موقع ويب ذو وضع قانوني مشكوك فيه، حيث لا يوجد "AWS للجمعيات الخيرية الظل". <em>تحقق أيضًا من المقالة الشقيقة <a %(blog_how_to_become_a_pirate_archivist)s>كيف تصبح أرشيفيًا قرصانًا</a>.</em> كيفية تشغيل مكتبة الظل: العمليات في رَبيدةُ آنّا لا يوجد <q>AWS للجمعيات الخيرية الظل،</q> فكيف ندير رَبيدةُ آنّا؟ الأدوات خادم التطبيق: Flask وMariaDB وElasticSearch وDocker. التطوير: Gitlab وWeblate وZulip. إدارة الخادم: Ansible وCheckmk وUFW. استضافة ثابتة على Onion: Tor، Nginx. خادم الوكيل: Varnish. دعونا نلقي نظرة على الأدوات التي نستخدمها لتحقيق كل هذا. هذا يتطور بشكل كبير مع مواجهتنا لمشاكل جديدة وإيجاد حلول جديدة. هناك بعض القرارات التي ترددنا فيها. أحدها هو الاتصال بين الخوادم: كنا نستخدم Wireguard لهذا الغرض، لكن وجدنا أنه يتوقف أحيانًا عن نقل أي بيانات، أو ينقل البيانات في اتجاه واحد فقط. حدث هذا مع عدة إعدادات مختلفة لـ Wireguard التي جربناها، مثل <a %(github_costela_wesher)s>wesher</a> و<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. كما جربنا توجيه المنافذ عبر SSH، باستخدام autossh وsshuttle، لكن واجهنا <a %(github_sshuttle)s>مشاكل هناك</a> (على الرغم من أنه لا يزال غير واضح لي إذا كان autossh يعاني من مشاكل TCP-over-TCP أم لا - يبدو لي كحل غير مثالي ولكن ربما يكون جيدًا بالفعل؟). بدلاً من ذلك، عدنا إلى الاتصالات المباشرة بين الخوادم، مع إخفاء أن الخادم يعمل على مزودين رخيصين باستخدام تصفية IP مع UFW. هذا له عيب أن Docker لا يعمل بشكل جيد مع UFW، إلا إذا استخدمت <code>network_mode: "host"</code>. كل هذا أكثر عرضة للأخطاء، لأنك ستعرض خادمك للإنترنت مع مجرد خطأ بسيط في التكوين. ربما يجب أن نعود إلى autossh - سيكون من المفيد جدًا الحصول على تعليقات هنا. لقد ترددنا أيضًا بين Varnish وNginx. حاليًا نفضل Varnish، لكنه يحتوي على بعض العيوب والحواف الخشنة. ينطبق نفس الشيء على Checkmk: لا نحبه، لكنه يعمل في الوقت الحالي. Weblate كان جيدًا ولكن ليس مذهلاً - أحيانًا أخشى أن يفقد بياناتي كلما حاولت مزامنتها مع مستودع git الخاص بنا. Flask كان جيدًا بشكل عام، لكنه يحتوي على بعض العيوب الغريبة التي كلفت الكثير من الوقت لحلها، مثل تكوين النطاقات المخصصة، أو المشاكل مع تكامل SqlAlchemy. حتى الآن كانت الأدوات الأخرى رائعة: ليس لدينا شكاوى جدية حول MariaDB، ElasticSearch، Gitlab، Zulip، Docker، وTor. جميعها واجهت بعض المشاكل، لكن لا شيء خطير أو مستهلك للوقت بشكل مفرط. المجتمع قد تكون التحدي الأول مفاجئًا. إنه ليس مشكلة تقنية، أو مشكلة قانونية. إنه مشكلة نفسية: القيام بهذا العمل في الظل يمكن أن يكون وحيدًا بشكل لا يصدق. اعتمادًا على ما تخطط للقيام به، ونموذج التهديد الخاص بك، قد تضطر إلى أن تكون حذرًا جدًا. في أحد أطراف الطيف لدينا أشخاص مثل ألكسندرا إلباكيان*، مؤسسة Sci-Hub، التي تكون منفتحة جدًا بشأن أنشطتها. لكنها في خطر كبير من الاعتقال إذا زارت دولة غربية في هذه المرحلة، وقد تواجه عقودًا من السجن. هل هذا خطر ترغب في تحمله؟ نحن في الطرف الآخر من الطيف؛ نكون حذرين جدًا لعدم ترك أي أثر، ونمتلك أمانًا تشغيليًا قويًا. * كما ذكر في HN بواسطة "ynno"، لم تكن ألكسندرا ترغب في البداية في أن تكون معروفة: "كانت خوادمها معدة لإصدار رسائل خطأ مفصلة من PHP، بما في ذلك المسار الكامل لملف المصدر الذي يحتوي على الخطأ، والذي كان تحت دليل /home/<USER>" لذا، استخدم أسماء مستخدمين عشوائية على أجهزة الكمبيوتر التي تستخدمها لهذا العمل، في حال قمت بتكوين شيء بشكل خاطئ. لكن تلك السرية تأتي بتكلفة نفسية. معظم الناس يحبون أن يتم الاعتراف بعملهم، ومع ذلك لا يمكنك أن تأخذ أي فضل لهذا في الحياة الواقعية. حتى الأشياء البسيطة يمكن أن تكون تحديًا، مثل الأصدقاء الذين يسألونك عما كنت تفعله (في مرحلة ما "العبث مع NAS / homelab" يصبح قديمًا). لهذا السبب من المهم جدًا العثور على بعض المجتمع. يمكنك التخلي عن بعض الأمان التشغيلي من خلال الثقة في بعض الأصدقاء المقربين جدًا، الذين تعرف أنك تستطيع الوثوق بهم بعمق. حتى في هذه الحالة كن حذرًا لعدم وضع أي شيء في الكتابة، في حال اضطروا إلى تسليم رسائلهم الإلكترونية إلى السلطات، أو إذا تم اختراق أجهزتهم بطريقة أخرى. الأفضل من ذلك هو العثور على بعض القراصنة الزملاء. إذا كان أصدقاؤك المقربون مهتمين بالانضمام إليك، فهذا رائع! وإلا، قد تتمكن من العثور على آخرين عبر الإنترنت. للأسف، لا يزال هذا مجتمعًا متخصصًا. حتى الآن وجدنا فقط عددًا قليلاً من الآخرين الذين ينشطون في هذا المجال. تبدو الأماكن الجيدة للبدء هي منتديات Library Genesis، وr/DataHoarder. فريق الأرشيف أيضًا لديه أفراد متشابهين في التفكير، على الرغم من أنهم يعملون ضمن القانون (حتى لو في بعض المناطق الرمادية من القانون). مشاهد "warez" التقليدية وقرصنة أيضًا لديها أشخاص يفكرون بطرق مشابهة. نحن منفتحون على الأفكار حول كيفية تعزيز المجتمع واستكشاف الأفكار. لا تتردد في مراسلتنا على تويتر أو ريديت. ربما يمكننا استضافة نوع من المنتدى أو مجموعة دردشة. أحد التحديات هو أن هذا يمكن أن يتم حجبه بسهولة عند استخدام المنصات الشائعة، لذا سيتعين علينا استضافته بأنفسنا. هناك أيضًا توازن بين جعل هذه المناقشات عامة بالكامل (لزيادة التفاعل المحتمل) وبين جعلها خاصة (لعدم إبلاغ "الأهداف" المحتملة بأننا على وشك جمع بياناتهم). سنحتاج إلى التفكير في ذلك. أخبرنا إذا كنت مهتمًا بذلك! الخاتمة نأمل أن يكون هذا مفيدًا للأرشيفيين القراصنة الجدد. نحن متحمسون للترحيب بكم في هذا العالم، فلا تترددوا في التواصل معنا. دعونا نحافظ على أكبر قدر ممكن من المعرفة والثقافة العالمية، ونعكسها على نطاق واسع. المشاريع 4. اختيار البيانات غالبًا يمكنك استخدام metadata لتحديد مجموعة فرعية معقولة من البيانات لتنزيلها. حتى إذا كنت ترغب في النهاية في تنزيل جميع البيانات، فقد يكون من المفيد إعطاء الأولوية للعناصر الأكثر أهمية أولاً، في حال تم اكتشافك وتحسين الدفاعات، أو لأنك قد تحتاج إلى شراء المزيد من الأقراص، أو ببساطة لأن شيئًا آخر قد يحدث في حياتك قبل أن تتمكن من تنزيل كل شيء. على سبيل المثال، قد تحتوي مجموعة على إصدارات متعددة من نفس المورد الأساسي (مثل كتاب أو فيلم)، حيث يتم تمييز أحدها بأنه ذو جودة أفضل. سيكون من المنطقي حفظ تلك الإصدارات أولاً. قد ترغب في النهاية في حفظ جميع الإصدارات، حيث في بعض الحالات قد يتم تصنيف metadata بشكل غير صحيح، أو قد تكون هناك تنازلات غير معروفة بين الإصدارات (على سبيل المثال، قد يكون "أفضل إصدار" هو الأفضل في معظم النواحي ولكنه أسوأ في نواحٍ أخرى، مثل أن يكون للفيلم دقة أعلى ولكنه يفتقد إلى الترجمة). يمكنك أيضًا البحث في قاعدة بيانات metadata الخاصة بك للعثور على أشياء مثيرة للاهتمام. ما هو أكبر ملف مستضاف، ولماذا هو كبير جدًا؟ ما هو أصغر ملف؟ هل هناك أنماط مثيرة للاهتمام أو غير متوقعة عندما يتعلق الأمر بفئات معينة، لغات، وهكذا؟ هل هناك عناوين مكررة أو متشابهة جدًا؟ هل هناك أنماط لمتى تمت إضافة البيانات، مثل يوم واحد تمت فيه إضافة العديد من الملفات دفعة واحدة؟ يمكنك غالبًا تعلم الكثير من خلال النظر إلى مجموعة البيانات بطرق مختلفة. في حالتنا، قمنا بإزالة التكرار بين كتب مكتبة الزّاي و md5 hashes في Library Genesis، مما وفر الكثير من وقت التنزيل ومساحة القرص. هذه حالة فريدة إلى حد ما. في معظم الحالات، لا توجد قواعد بيانات شاملة للملفات التي تم حفظها بشكل صحيح بالفعل من قبل القراصنة الآخرين. هذا في حد ذاته فرصة كبيرة لشخص ما هناك. سيكون من الرائع الحصول على نظرة عامة محدثة بانتظام عن أشياء مثل الموسيقى والأفلام التي يتم توزيعها بشكل واسع على مواقع التورنت، وبالتالي تكون ذات أولوية أقل لتضمينها في العاكسات القرصانية. 6. التوزيع لديك البيانات، مما يمنحك حيازة أول عاكسة قرصانية في العالم لهدفك (على الأرجح). في العديد من النواحي، انتهى الجزء الأصعب، لكن الجزء الأكثر خطورة لا يزال أمامك. بعد كل شيء، حتى الآن كنت متخفيًا؛ تطير تحت الرادار. كل ما كان عليك فعله هو استخدام VPN جيد طوال الوقت، وعدم ملء تفاصيلك الشخصية في أي نماذج (بديهي)، وربما استخدام جلسة متصفح خاصة (أو حتى جهاز كمبيوتر مختلف). الآن عليك توزيع البيانات. في حالتنا، أردنا أولاً المساهمة بالكتب مرة أخرى إلى Library Genesis، لكننا اكتشفنا بسرعة الصعوبات في ذلك (تصنيف الخيال مقابل غير الخيال). لذلك قررنا التوزيع باستخدام تورنتات على نمط Library Genesis. إذا كانت لديك الفرصة للمساهمة في مشروع قائم، فقد يوفر لك ذلك الكثير من الوقت. ومع ذلك، لا توجد العديد من العاكسات القرصانية المنظمة بشكل جيد حاليًا. لذا لنفترض أنك قررت توزيع التورنتات بنفسك. حاول أن تبقي تلك الملفات صغيرة، حتى يسهل عكسها على مواقع أخرى. سيتعين عليك بعد ذلك بذور التورنتات بنفسك، مع البقاء مجهول الهوية. يمكنك استخدام VPN (مع أو بدون توجيه المنافذ)، أو الدفع باستخدام عملات البيتكوين المجمعة للحصول على Seedbox. إذا كنت لا تعرف ما تعنيه بعض هذه المصطلحات، فستحتاج إلى قراءة الكثير، حيث من المهم أن تفهم المخاطر هنا. يمكنك استضافة ملفات التورنت نفسها على مواقع التورنت الحالية. في حالتنا، اخترنا استضافة موقع ويب فعليًا، حيث أردنا أيضًا نشر فلسفتنا بطريقة واضحة. يمكنك القيام بذلك بنفسك بطريقة مماثلة (نحن نستخدم Njalla لنطاقاتنا واستضافتنا، مدفوعة بعملات البيتكوين المجمعة)، ولكن لا تتردد في الاتصال بنا لنستضيف تورنتاتك. نحن نسعى لبناء فهرس شامل للعاكسات القرصانية بمرور الوقت، إذا لاقت هذه الفكرة رواجًا. أما بالنسبة لاختيار VPN، فقد كُتب الكثير عن هذا بالفعل، لذا سنكرر فقط النصيحة العامة بالاختيار بناءً على السمعة. السياسات الفعلية التي تم اختبارها في المحاكم بعدم الاحتفاظ بالسجلات مع سجلات طويلة في حماية الخصوصية هي الخيار الأقل خطورة، في رأينا. لاحظ أنه حتى عندما تقوم بكل شيء بشكل صحيح، لا يمكنك الوصول إلى مستوى صفر من المخاطر. على سبيل المثال، عند بذور التورنتات الخاصة بك، يمكن لجهة فاعلة ذات دوافع عالية من الدولة أن تنظر في تدفقات البيانات الواردة والصادرة لخوادم VPN، وتستنتج من أنت. أو يمكنك ببساطة أن تخطئ بطريقة ما. ربما قد فعلنا ذلك بالفعل، وسنفعل ذلك مرة أخرى. لحسن الحظ، الدول لا تهتم كثيرًا بالقرصنة. أحد القرارات التي يجب اتخاذها لكل مشروع، هو ما إذا كنت ستنشره باستخدام نفس الهوية كما كان من قبل، أم لا. إذا واصلت استخدام نفس الاسم، فقد تعود الأخطاء في الأمان التشغيلي من المشاريع السابقة لتؤذيك. ولكن النشر بأسماء مختلفة يعني أنك لا تبني سمعة طويلة الأمد. اخترنا أن يكون لدينا أمان تشغيلي قوي من البداية حتى نتمكن من الاستمرار في استخدام نفس الهوية، لكننا لن نتردد في النشر تحت اسم مختلف إذا أخطأنا أو إذا دعت الظروف إلى ذلك. نشر الكلمة يمكن أن يكون صعبًا. كما قلنا، هذه لا تزال مجتمعًا متخصصًا. نشرنا في الأصل على Reddit، لكننا حققنا نجاحًا حقيقيًا على Hacker News. في الوقت الحالي، توصيتنا هي نشرها في بعض الأماكن ورؤية ما يحدث. ومرة أخرى، اتصل بنا. نود نشر كلمة المزيد من جهود الأرشفة القرصانية. 1. اختيار المجال / الفلسفة لا يوجد نقص في المعرفة والتراث الثقافي الذي يجب حفظه، مما قد يكون مرهقًا. لهذا السبب غالبًا ما يكون من المفيد أن تأخذ لحظة وتفكر في ما يمكن أن تكون مساهمتك. لدى الجميع طريقة مختلفة للتفكير في هذا، ولكن هنا بعض الأسئلة التي يمكنك أن تسأل نفسك: في حالتنا، كنا نهتم بشكل خاص بالحفاظ على العلوم على المدى الطويل. كنا نعرف عن Library Genesis، وكيف تم عكسها بالكامل عدة مرات باستخدام التورنت. أحببنا تلك الفكرة. ثم في يوم من الأيام، حاول أحدنا العثور على بعض الكتب العلمية في Library Genesis، لكنه لم يتمكن من العثور عليها، مما أثار الشكوك حول مدى اكتمالها حقًا. ثم بحثنا عن تلك الكتب عبر الإنترنت، ووجدناها في أماكن أخرى، مما زرع بذرة مشروعنا. حتى قبل أن نعرف عن مكتبة الزّاي، كانت لدينا فكرة عدم محاولة جمع كل تلك الكتب يدويًا، بل التركيز على عكس المجموعات الموجودة، والمساهمة بها مرة أخرى في Library Genesis. ما هي المهارات التي تمتلكها والتي يمكنك استخدامها لصالحك؟ على سبيل المثال، إذا كنت خبيرًا في الأمن عبر الإنترنت، يمكنك إيجاد طرق لهزيمة حظر IP للأهداف الآمنة. إذا كنت بارعًا في تنظيم المجتمعات، فربما يمكنك جمع بعض الأشخاص حول هدف معين. من المفيد معرفة بعض البرمجة، ولو فقط للحفاظ على أمان العمليات الجيد طوال هذه العملية. ما هو المجال ذو الرافعة العالية الذي يجب التركيز عليه؟ إذا كنت ستقضي X ساعة في أرشفة القرصنة، فكيف يمكنك الحصول على أكبر "عائد على استثمارك"؟ ما هي الطرق الفريدة التي تفكر بها في هذا؟ قد تكون لديك بعض الأفكار أو الأساليب المثيرة للاهتمام التي قد يكون الآخرون قد فاتتهم. كم من الوقت لديك لهذا؟ نصيحتنا ستكون البدء بمشاريع صغيرة والقيام بمشاريع أكبر كلما اكتسبت الخبرة، ولكن يمكن أن يصبح الأمر مستهلكًا للوقت بشكل كامل. لماذا أنت مهتم بهذا؟ ما الذي تشعر بالشغف تجاهه؟ إذا تمكنا من جمع مجموعة من الأشخاص الذين يقومون جميعًا بأرشفة الأشياء التي يهتمون بها بشكل خاص، فسيغطي ذلك الكثير! ستعرف أكثر بكثير من الشخص العادي عن شغفك، مثل ما هي البيانات المهمة للحفظ، وما هي أفضل المجموعات والمجتمعات عبر الإنترنت، وهكذا. 3. استخراج metadata تاريخ الإضافة/التعديل: حتى تتمكن من العودة لاحقًا وتنزيل الملفات التي لم تقم بتنزيلها من قبل (على الرغم من أنه يمكنك غالبًا أيضًا استخدام المعرف أو الهاش لهذا). الهاش (md5، sha1): للتأكد من أنك قمت بتنزيل الملف بشكل صحيح. المعرف: يمكن أن يكون معرف داخلي، لكن المعرفات مثل ISBN أو DOI مفيدة أيضًا. اسم الملف / الموقع الوصف، الفئة، العلامات، المؤلفون، اللغة، إلخ. الحجم: لحساب مقدار مساحة القرص التي تحتاجها. لنصبح أكثر تقنية هنا. لاستخراج metadata من المواقع، أبقينا الأمور بسيطة. نستخدم سكربتات بايثون، وأحيانًا curl، وقاعدة بيانات MySQL لتخزين النتائج فيها. لم نستخدم أي برامج استخراج متقدمة يمكنها رسم خرائط المواقع المعقدة، حيث أننا حتى الآن احتجنا فقط لاستخراج نوع أو نوعين من الصفحات عن طريق التعداد عبر المعرفات وتحليل HTML. إذا لم تكن هناك صفحات يمكن تعدادها بسهولة، فقد تحتاج إلى زاحف مناسب يحاول العثور على جميع الصفحات. قبل أن تبدأ في استخراج موقع كامل، حاول القيام بذلك يدويًا لفترة. قم بزيارة بضع عشرات من الصفحات بنفسك، لتتعرف على كيفية عمل ذلك. أحيانًا ستواجه حظر IP أو سلوكًا مثيرًا للاهتمام بهذه الطريقة. ينطبق الأمر نفسه على استخراج البيانات: قبل التعمق في هذا الهدف، تأكد من أنك تستطيع بالفعل تنزيل بياناته بفعالية. لتجاوز القيود، هناك بعض الأشياء التي يمكنك تجربتها. هل هناك أي عناوين IP أو خوادم أخرى تستضيف نفس البيانات ولكن لا تحتوي على نفس القيود؟ هل هناك أي نقاط نهاية API لا تحتوي على قيود، بينما تحتوي الأخرى على قيود؟ عند أي معدل تنزيل يتم حظر IP الخاص بك، ولأي مدة؟ أو هل لا يتم حظرك ولكن يتم تقليل السرعة؟ ماذا لو أنشأت حساب مستخدم، كيف تتغير الأمور بعد ذلك؟ هل يمكنك استخدام HTTP/2 للحفاظ على الاتصالات مفتوحة، وهل يزيد ذلك من معدل طلب الصفحات؟ هل هناك صفحات تسرد ملفات متعددة في وقت واحد، وهل المعلومات المدرجة هناك كافية؟ الأشياء التي ربما تريد حفظها تشمل: نقوم عادةً بذلك على مرحلتين. أولاً نقوم بتنزيل ملفات HTML الخام، عادةً مباشرة إلى MySQL (لتجنب الكثير من الملفات الصغيرة، والتي نتحدث عنها أكثر أدناه). ثم، في خطوة منفصلة، نقوم بمرور تلك الملفات HTML وتحليلها إلى جداول MySQL الفعلية. بهذه الطريقة لا تحتاج إلى إعادة تنزيل كل شيء من البداية إذا اكتشفت خطأ في كود التحليل الخاص بك، حيث يمكنك فقط إعادة معالجة ملفات HTML بالكود الجديد. كما أنه غالبًا ما يكون من الأسهل موازاة خطوة المعالجة، مما يوفر بعض الوقت (ويمكنك كتابة كود المعالجة أثناء تشغيل الاستخراج، بدلاً من الاضطرار إلى كتابة كلا الخطوتين في وقت واحد). أخيرًا، لاحظ أن بالنسبة لبعض الأهداف، فإن جمع بيانات metadata هو كل ما يمكن القيام به. هناك بعض مجموعات metadata الضخمة التي لم يتم حفظها بشكل صحيح. العنوان اختيار المجال / الفلسفة: أين تريد التركيز بشكل تقريبي، ولماذا؟ ما هي شغفك ومهاراتك وظروفك الفريدة التي يمكنك استخدامها لصالحك؟ اختيار الهدف: أي مجموعة محددة ستقوم بعكسها؟ جمع metadata: فهرسة المعلومات حول الملفات، دون تنزيل الملفات نفسها (التي غالبًا ما تكون أكبر بكثير). اختيار البيانات: بناءً على metadata، تضييق نطاق البيانات الأكثر صلة بالأرشفة الآن. قد يكون كل شيء، ولكن غالبًا ما يكون هناك طريقة معقولة لتوفير المساحة وعرض النطاق الترددي. جمع البيانات: الحصول على البيانات فعليًا. التوزيع: تغليفها في تورنت، الإعلان عنها في مكان ما، وجعل الناس ينشرونها. 5. جمع البيانات الآن أنت جاهز لتنزيل البيانات بكميات كبيرة. كما ذكرنا سابقًا، في هذه المرحلة يجب أن تكون قد قمت بالفعل بتنزيل مجموعة من الملفات يدويًا، لفهم سلوك وقيود الهدف بشكل أفضل. ومع ذلك، لا يزال هناك مفاجآت في انتظارك بمجرد أن تبدأ في تنزيل الكثير من الملفات دفعة واحدة. نصيحتنا هنا هي أن تبقي الأمور بسيطة. ابدأ فقط بتنزيل مجموعة من الملفات. يمكنك استخدام Python، ثم التوسع إلى عدة خيوط. ولكن في بعض الأحيان يكون الأمر أبسط حتى من ذلك، وهو إنشاء ملفات Bash مباشرة من قاعدة البيانات، ثم تشغيل عدة منها في نوافذ طرفية متعددة لتوسيع النطاق. حيلة تقنية سريعة تستحق الذكر هنا هي استخدام OUTFILE في MySQL، والتي يمكنك كتابتها في أي مكان إذا قمت بتعطيل "secure_file_priv" في mysqld.cnf (وتأكد أيضًا من تعطيل/تجاوز AppArmor إذا كنت تستخدم Linux). نحن نخزن البيانات على أقراص صلبة بسيطة. ابدأ بما لديك، وتوسع ببطء. قد يكون من المرهق التفكير في تخزين مئات التيرابايت من البيانات. إذا كان هذا هو الوضع الذي تواجهه، فقط ضع مجموعة جيدة أولاً، وفي إعلانك اطلب المساعدة في تخزين الباقي. إذا كنت ترغب في الحصول على المزيد من الأقراص الصلبة بنفسك، فإن r/DataHoarder يحتوي على بعض الموارد الجيدة للحصول على صفقات جيدة. حاول ألا تقلق كثيرًا بشأن أنظمة الملفات الفاخرة. من السهل الوقوع في حفرة إعداد أشياء مثل ZFS. ولكن هناك تفصيل تقني يجب أن تكون على دراية به، وهو أن العديد من أنظمة الملفات لا تتعامل بشكل جيد مع الكثير من الملفات. لقد وجدنا أن الحل البسيط هو إنشاء أدلة متعددة، على سبيل المثال لمجالات معرفات مختلفة أو بادئات تجزئة. بعد تنزيل البيانات، تأكد من التحقق من سلامة الملفات باستخدام التجزئات في metadata، إذا كانت متوفرة. 2. اختيار الهدف قابلة للوصول: لا تستخدم الكثير من طبقات الحماية لمنعك من استخراج metadata والبيانات الخاصة بهم. رؤية خاصة: لديك بعض المعلومات الخاصة حول هذا الهدف، مثل أنك لديك وصول خاص إلى هذه المجموعة، أو اكتشفت كيفية التغلب على دفاعاتهم. هذا ليس مطلوبًا (مشروعنا القادم لا يقوم بأي شيء خاص)، لكنه بالتأكيد يساعد! كبيرة إذن، لدينا المنطقة التي ننظر إليها، الآن أي مجموعة محددة نقوم بعكسها؟ هناك بعض الأشياء التي تجعل الهدف جيدًا: عندما وجدنا كتبنا الدراسية العلمية على مواقع أخرى غير Library Genesis، حاولنا معرفة كيف وصلت إلى الإنترنت. ثم وجدنا مكتبة الزّاي، وأدركنا أنه بينما لا تظهر معظم الكتب هناك أولاً، فإنها في النهاية تصل إلى هناك. تعلمنا عن علاقتها بـ Library Genesis، وهيكل الحوافز (المالية) وواجهة المستخدم المتفوقة، وكلاهما جعلاها مجموعة أكثر اكتمالاً. ثم قمنا ببعض عمليات استخراج metadata والبيانات الأولية، وأدركنا أنه يمكننا تجاوز حدود تنزيل IP الخاصة بهم، مستفيدين من الوصول الخاص لأحد أعضائنا إلى الكثير من خوادم البروكسي. أثناء استكشافك لأهداف مختلفة، من المهم بالفعل إخفاء آثارك باستخدام VPNs وعناوين البريد الإلكتروني المؤقتة، والتي سنتحدث عنها أكثر لاحقًا. فريدة: غير مغطاة بشكل جيد من قبل مشاريع أخرى. عندما نقوم بمشروع، فإنه يمر بعدة مراحل: هذه ليست مراحل مستقلة تمامًا، وغالبًا ما تعيدك الأفكار من مرحلة لاحقة إلى مرحلة سابقة. على سبيل المثال، أثناء جمع metadata قد تدرك أن الهدف الذي اخترته لديه آليات دفاعية تتجاوز مستوى مهاراتك (مثل حظر IP)، لذا تعود وتجد هدفًا مختلفًا. - آنّا والفريق (<a %(reddit)s>Reddit</a>) يمكن كتابة كتب كاملة عن <em>لماذا</em> الحفظ الرقمي بشكل عام، والأرشفة القرصانية بشكل خاص، لكن دعونا نقدم مقدمة سريعة لأولئك الذين ليسوا على دراية كبيرة. العالم ينتج المزيد من المعرفة والثقافة أكثر من أي وقت مضى، ولكن أيضًا يتم فقدان المزيد منها أكثر من أي وقت مضى. البشرية تعتمد بشكل كبير على الشركات مثل الناشرين الأكاديميين، وخدمات البث، وشركات وسائل التواصل الاجتماعي للحفاظ على هذا التراث، وغالبًا ما لم يثبتوا أنهم حراس عظماء. تحقق من الفيلم الوثائقي Digital Amnesia، أو أي حديث لجيسون سكوت. هناك بعض المؤسسات التي تقوم بعمل جيد في الأرشفة بقدر ما تستطيع، لكنها مقيدة بالقانون. كقراصنة، نحن في وضع فريد لأرشفة مجموعات لا يمكنهم لمسها، بسبب إنفاذ حقوق الطبع والنشر أو قيود أخرى. يمكننا أيضًا عكس المجموعات عدة مرات، عبر العالم، مما يزيد من فرص الحفظ السليم. في الوقت الحالي، لن ندخل في مناقشات حول إيجابيات وسلبيات الملكية الفكرية، أو أخلاقية كسر القانون، أو التأملات في الرقابة، أو مسألة الوصول إلى المعرفة والثقافة. مع كل ذلك بعيدًا عن الطريق، دعونا نغوص في <em>كيف</em>. سنشارك كيف أصبح فريقنا أرشيفيين قراصنة، والدروس التي تعلمناها على طول الطريق. هناك العديد من التحديات عندما تبدأ في هذه الرحلة، ونأمل أن نساعدك في تجاوز بعضها. كيفية أن تصبح أرشيفي قرصان قد تكون التحدي الأول مفاجئًا. إنه ليس مشكلة تقنية، أو مشكلة قانونية. إنه مشكلة نفسية. قبل أن نبدأ، هناك تحديثان حول عاكسة مكتبة القراصنة (تعديل: تم النقل إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>): حصلنا على بعض التبرعات السخية للغاية. كان الأول 10 آلاف دولار من شخص مجهول كان يدعم أيضًا "bookwarrior"، المؤسس الأصلي لـ Library Genesis. شكر خاص لـ bookwarrior لتسهيل هذا التبرع. كان الثاني 10 آلاف دولار أخرى من متبرع مجهول، تواصل معنا بعد إصدارنا الأخير، وألهمه للمساعدة. كما حصلنا على عدد من التبرعات الأصغر. شكرًا جزيلاً على كل دعمكم السخي. لدينا بعض المشاريع الجديدة المثيرة في الأفق التي سيدعمها هذا، لذا تابعونا. واجهنا بعض الصعوبات التقنية مع حجم إصدارنا الثاني، لكن تورنتاتنا الآن متاحة ويتم تحميلها. كما حصلنا على عرض سخي من شخص مجهول لتحميل مجموعتنا على خوادمهم ذات السرعة العالية جدًا، لذا نقوم بتحميل خاص إلى أجهزتهم، وبعد ذلك يجب أن يلاحظ الجميع الذين يقومون بتحميل المجموعة تحسنًا كبيرًا في السرعة. منشورات المدونة مرحبًا، أنا آنّا. أنشأت <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر مكتبة ظل في العالم. هذا هو مدونتي الشخصية، حيث أكتب أنا وفريقي عن القرصنة، الحفظ الرقمي، والمزيد. تواصل معي على <a %(reddit)s>Reddit</a>. يرجى ملاحظة أن هذا الموقع هو مجرد مدونة. نحن نستضيف كلماتنا فقط هنا. لا يتم استضافة أو ربط أي ملفات تورنت أو ملفات محمية بحقوق الطبع والنشر هنا. <strong>مكتبة</strong> - مثل معظم المكتبات، نركز بشكل أساسي على المواد المكتوبة مثل الكتب. قد نتوسع في أنواع أخرى من الوسائط في المستقبل. <strong>عاكسة</strong> - نحن عاكسة بحتة للمكتبات الموجودة. نركز على الحفظ، وليس على جعل الكتب قابلة للبحث والتنزيل بسهولة (الوصول) أو تعزيز مجتمع كبير من الأشخاص الذين يساهمون بكتب جديدة (المصدر). <strong>قرصان</strong> - نحن نتعمد انتهاك قانون حقوق الطبع والنشر في معظم البلدان. هذا يسمح لنا بفعل شيء لا يمكن للكيانات القانونية القيام به: التأكد من أن الكتب يتم عكسها على نطاق واسع. <em>نحن لا نربط الملفات من هذه المدونة. يرجى العثور عليها بنفسك.</em> - آنّا والفريق (<a %(reddit)s>Reddit</a>) يهدف هذا المشروع (تم نقله إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>) إلى المساهمة في الحفاظ على المعرفة البشرية وتحريرها. نحن نقدم مساهمتنا الصغيرة والمتواضعة، على خطى العظماء الذين سبقونا. يتم توضيح تركيز هذا المشروع من خلال اسمه: أول مكتبة قمنا بعكسها هي مكتبة الزّاي. هذه مكتبة شهيرة (وغير قانونية). لقد أخذوا مجموعة Library Genesis وجعلوها قابلة للبحث بسهولة. بالإضافة إلى ذلك، أصبحوا فعالين جدًا في طلب مساهمات الكتب الجديدة، من خلال تحفيز المستخدمين المساهمين بمزايا مختلفة. حاليًا، لا يساهمون بهذه الكتب الجديدة مرة أخرى في Library Genesis. وعلى عكس Library Genesis، لا يجعلون مجموعتهم قابلة للعكس بسهولة، مما يمنع الحفظ الواسع. هذا مهم لنموذج أعمالهم، حيث يفرضون رسومًا على الوصول إلى مجموعتهم بشكل كبير (أكثر من 10 كتب في اليوم). نحن لا نصدر أحكامًا أخلاقية بشأن فرض رسوم مالية للوصول الجماعي إلى مجموعة كتب غير قانونية. لا شك أن مكتبة الزّاي قد نجحت في توسيع الوصول إلى المعرفة وتوفير المزيد من الكتب. نحن هنا ببساطة لنقوم بدورنا: ضمان الحفاظ طويل الأمد على هذه المجموعة الخاصة. نود دعوتكم للمساعدة في الحفاظ على المعرفة البشرية وتحريرها عن طريق تنزيل وتوزيع التورنتات الخاصة بنا. راجع صفحة المشروع لمزيد من المعلومات حول كيفية تنظيم البيانات. نود أيضًا دعوتكم للمساهمة بأفكاركم حول المجموعات التي يجب عكسها بعد ذلك، وكيفية القيام بذلك. معًا يمكننا تحقيق الكثير. هذه مجرد مساهمة صغيرة بين العديد من المساهمات الأخرى. شكرًا لكم، على كل ما تفعلونه. تقديم عاكسة المكتبة القرصانية: الحفاظ على 7 تيرابايت من الكتب (التي ليست في Libgen) 10% o من التراث المكتوب للبشرية محفوظ إلى الأبد <strong>جوجل.</strong> بعد كل شيء، قاموا بهذا البحث من أجل كتب جوجل. ومع ذلك، فإن metadata الخاصة بهم ليست متاحة بشكل جماعي وصعبة الكشط. <strong>أنظمة المكتبات الفردية والأرشيفات المختلفة.</strong> هناك مكتبات وأرشيفات لم يتم فهرستها وتجميعها من قبل أي من المذكورين أعلاه، غالبًا لأنها تعاني من نقص التمويل، أو لأسباب أخرى لا ترغب في مشاركة بياناتها مع المكتبة المفتوحة، OCLC، جوجل، وما إلى ذلك. الكثير من هذه المكتبات لديها سجلات رقمية متاحة عبر الإنترنت، وغالبًا ما لا تكون محمية بشكل جيد، لذا إذا كنت ترغب في المساعدة والاستمتاع بتعلم أنظمة المكتبات الغريبة، فهذه نقاط انطلاق رائعة. <strong>ISBNdb.</strong> هذا هو موضوع منشور المدونة هذا. يقوم ISBNdb بكشط مواقع ويب مختلفة للحصول على metadata الكتب، وخاصة بيانات التسعير، التي يبيعونها بعد ذلك لبائعي الكتب، حتى يتمكنوا من تسعير كتبهم بما يتماشى مع بقية السوق. نظرًا لأن أرقام ISBN أصبحت شاملة إلى حد ما في الوقت الحاضر، فقد بنوا فعليًا "صفحة ويب لكل كتاب". <strong>المكتبة المفتوحة.</strong> كما ذُكر سابقًا، هذه هي مهمتهم بالكامل. لقد حصلوا على كميات هائلة من بيانات المكتبات من المكتبات المتعاونة والأرشيفات الوطنية، ويواصلون القيام بذلك. لديهم أيضًا أمناء مكتبات متطوعون وفريق تقني يحاول إزالة التكرار من السجلات، ووضع علامات عليها بجميع أنواع metadata. والأفضل من ذلك كله، أن مجموعة بياناتهم مفتوحة تمامًا. يمكنك ببساطة <a %(openlibrary)s>تحميلها</a>. <strong>WorldCat.</strong> هذا موقع تديره منظمة غير ربحية OCLC، التي تبيع أنظمة إدارة المكتبات. يجمعون metadata الكتب من الكثير من المكتبات، ويجعلونها متاحة من خلال موقع WorldCat. ومع ذلك، فإنهم يربحون أيضًا من بيع هذه البيانات، لذا فهي ليست متاحة للتنزيل الجماعي. لديهم بعض مجموعات البيانات الجماعية المحدودة المتاحة للتنزيل، بالتعاون مع مكتبات محددة. 1. لبعض التعريفات المعقولة لـ "إلى الأبد". ;) 2. بالطبع، التراث المكتوب للبشرية هو أكثر بكثير من الكتب، خاصة في الوقت الحاضر. من أجل هذا المنشور وإصداراتنا الأخيرة نركز على الكتب، لكن اهتماماتنا تمتد إلى أبعد من ذلك. 3. هناك الكثير مما يمكن قوله عن آرون شوارتز، لكننا أردنا فقط ذكره بإيجاز، لأنه يلعب دورًا محوريًا في هذه القصة. مع مرور الوقت، قد يصادف المزيد من الناس اسمه لأول مرة، ويمكنهم بعد ذلك الغوص في التفاصيل بأنفسهم. <strong>النسخ المادية.</strong> من الواضح أن هذا ليس مفيدًا جدًا، لأنها مجرد نسخ مكررة من نفس المادة. سيكون من الرائع إذا استطعنا الحفاظ على جميع التعليقات التي يكتبها الناس في الكتب، مثل "الخربشات في الهوامش" الشهيرة لفيرمات. لكن للأسف، سيظل ذلك حلمًا للأرشيفيين. <strong>“الإصدارات”.</strong> هنا تحسب كل نسخة فريدة من الكتاب. إذا كان هناك أي شيء مختلف فيها، مثل غلاف مختلف أو مقدمة مختلفة، فإنها تُعتبر إصدارًا مختلفًا. <strong>الملفات.</strong> عند العمل مع مكتبات الظل مثل Library Genesis وSci-Hub وZ-Library، هناك اعتبار إضافي. يمكن أن يكون هناك مسح ضوئي متعدد لنفس الإصدار. ويمكن للناس إنشاء نسخ أفضل من الملفات الموجودة، عن طريق مسح النص باستخدام OCR، أو تصحيح الصفحات التي تم مسحها بزاوية. نريد أن نحسب هذه الملفات كإصدار واحد فقط، مما يتطلب metadata جيدة، أو إزالة التكرار باستخدام مقاييس تشابه الوثائق. <strong>“الأعمال”.</strong> على سبيل المثال "هاري بوتر وغرفة الأسرار" كمفهوم منطقي، يشمل جميع نسخه، مثل الترجمات المختلفة وإعادة الطبع. هذا نوع من التعريف المفيد، لكنه يمكن أن يكون صعبًا في تحديد ما الذي يُعتبر. على سبيل المثال، ربما نريد الحفاظ على الترجمات المختلفة، رغم أن إعادة الطبع مع اختلافات طفيفة قد لا تكون بنفس الأهمية. - آنّا والفريق (<a %(reddit)s>Reddit</a>) مع عاكسة المكتبة القرصانية (تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>)، هدفنا هو أخذ جميع الكتب في العالم، وحفظها إلى الأبد.<sup>1</sup> بين تورنتات مكتبة الزّاي، وتورنتات Library Genesis الأصلية، لدينا 11,783,153 ملفًا. لكن كم هو ذلك حقًا؟ إذا قمنا بإزالة التكرار بشكل صحيح من تلك الملفات، فما هي النسبة المئوية من جميع الكتب في العالم التي قمنا بحفظها؟ نود حقًا أن يكون لدينا شيء مثل هذا: لنبدأ ببعض الأرقام التقريبية: في كل من مكتبة الزّاي/Libgen والمكتبة المفتوحة، هناك العديد من الكتب أكثر من أرقام ISBN الفريدة. هل يعني ذلك أن الكثير من تلك الكتب ليس لديها أرقام ISBN، أم أن metadata الخاصة بـ ISBN مفقودة ببساطة؟ يمكننا على الأرجح الإجابة على هذا السؤال بمزيج من المطابقة التلقائية بناءً على سمات أخرى (العنوان، المؤلف، الناشر، إلخ)، وجلب المزيد من مصادر البيانات، واستخراج أرقام ISBN من عمليات المسح الفعلية للكتب نفسها (في حالة مكتبة الزّاي/Libgen). كم عدد تلك الأرقام الفريدة من نوعها؟ هذا موضح بشكل أفضل باستخدام مخطط فين: لتكون أكثر دقة: لقد فوجئنا بمدى قلة التداخل الموجود! يحتوي ISBNdb على عدد هائل من أرقام ISBN التي لا تظهر في مكتبة الزّاي أو Open Library، وينطبق الأمر نفسه (بدرجة أقل ولكن لا تزال كبيرة) على المكتبتين الأخريين. يثير هذا العديد من الأسئلة الجديدة. إلى أي مدى يمكن أن يساعد المطابقة الآلية في تصنيف الكتب التي لم تُصنف بأرقام ISBN؟ هل سيكون هناك الكثير من المطابقات وبالتالي زيادة في التداخل؟ أيضًا، ماذا سيحدث إذا أضفنا مجموعة بيانات رابعة أو خامسة؟ كم من التداخل سنرى حينها؟ هذا يعطينا نقطة انطلاق. يمكننا الآن النظر في جميع أرقام ISBN التي لم تكن في مجموعة بيانات مكتبة الزّاي، والتي لا تتطابق مع حقول العنوان/المؤلف أيضًا. يمكن أن يمنحنا ذلك وسيلة للحفاظ على جميع الكتب في العالم: أولاً عن طريق جمع الإنترنت للحصول على المسوحات، ثم الخروج في الحياة الواقعية لمسح الكتب. يمكن حتى تمويل الأخير جماعيًا، أو تحفيزه من خلال "مكافآت" من الأشخاص الذين يرغبون في رؤية كتب معينة يتم رقمنتها. كل ذلك قصة لوقت آخر. إذا كنت ترغب في المساعدة في أي من هذا - تحليل إضافي؛ جمع المزيد من metadata؛ العثور على المزيد من الكتب؛ تحويل الكتب إلى نصوص باستخدام OCR؛ القيام بذلك لمجالات أخرى (مثل الأوراق، الكتب الصوتية، الأفلام، البرامج التلفزيونية، المجلات) أو حتى جعل بعض هذه البيانات متاحة لأشياء مثل تدريب النماذج اللغوية الكبيرة - يرجى الاتصال بي (<a %(reddit)s>Reddit</a>). إذا كنت مهتمًا بشكل خاص بتحليل البيانات، فإننا نعمل على جعل مجموعات البيانات والبرامج النصية الخاصة بنا متاحة بتنسيق أسهل للاستخدام. سيكون من الرائع إذا كان بإمكانك فقط نسخ دفتر ملاحظات وبدء اللعب بهذا. أخيرًا، إذا كنت ترغب في دعم هذا العمل، يرجى النظر في تقديم تبرع. هذه عملية تُدار بالكامل من قبل متطوعين، ومساهمتك تحدث فرقًا كبيرًا. كل جزء يساعد. في الوقت الحالي، نقبل التبرعات بالعملات المشفرة؛ انظر صفحة التبرع في رَبيدةُ آنّا. للحصول على نسبة مئوية، نحتاج إلى مقام: العدد الإجمالي للكتب التي تم نشرها على الإطلاق.<sup>2</sup> قبل زوال Google Books، حاول مهندس في المشروع، ليونيد تايشر، <a %(booksearch_blogspot)s>تقدير</a> هذا الرقم. توصل — بشكل ساخر — إلى 129,864,880 ("على الأقل حتى يوم الأحد"). قدر هذا الرقم من خلال بناء قاعدة بيانات موحدة لجميع الكتب في العالم. لهذا، جمع مجموعات بيانات مختلفة ثم دمجها بطرق متنوعة. بالمناسبة، هناك شخص آخر حاول فهرسة جميع الكتب في العالم: آرون شوارتز، الناشط الرقمي الراحل وأحد مؤسسي Reddit.<sup>3</sup> لقد <a %(youtube)s>بدأ المكتبة المفتوحة</a> بهدف "صفحة ويب لكل كتاب تم نشره على الإطلاق"، حيث جمع البيانات من مصادر مختلفة. انتهى به الأمر بدفع الثمن النهائي لعمله في الحفاظ الرقمي عندما تم محاكمته لتنزيله الأكاديمي بالجملة، مما أدى إلى انتحاره. لا حاجة للقول، هذا أحد الأسباب التي تجعل مجموعتنا تستخدم الأسماء المستعارة، ولماذا نحن حذرون جدًا. لا تزال المكتبة المفتوحة تُدار بشكل بطولي من قبل الأشخاص في أرشيف الإنترنت، مما يواصل إرث آرون. سنعود إلى هذا لاحقًا في هذا المنشور. في منشور مدونة جوجل، يصف تايشر بعض التحديات في تقدير هذا الرقم. أولاً، ما الذي يشكل كتابًا؟ هناك بعض التعريفات الممكنة: “الإصدارات” تبدو التعريف الأكثر عملية لماهية “الكتب”. بشكل ملائم، يُستخدم هذا التعريف أيضًا لتخصيص أرقام ISBN الفريدة. رقم ISBN، أو الرقم الدولي المعياري للكتاب، يُستخدم عادة في التجارة الدولية، لأنه مدمج مع نظام الباركود الدولي ("الرقم الدولي للمقالة"). إذا كنت تريد بيع كتاب في المتاجر، فإنه يحتاج إلى باركود، لذا تحصل على ISBN. يذكر منشور مدونة تايشر أنه بينما تكون أرقام ISBN مفيدة، إلا أنها ليست شاملة، لأنها لم تُعتمد فعليًا إلا في منتصف السبعينيات، وليس في كل مكان حول العالم. ومع ذلك، فإن ISBN هو على الأرجح المعرف الأكثر استخدامًا لإصدارات الكتب، لذا فهو أفضل نقطة انطلاق لدينا. إذا استطعنا العثور على جميع أرقام ISBN في العالم، نحصل على قائمة مفيدة بالكتب التي لا تزال بحاجة إلى الحفاظ عليها. إذًا، من أين نحصل على البيانات؟ هناك عدد من الجهود القائمة التي تحاول تجميع قائمة بجميع الكتب في العالم: في هذا المنشور، نحن سعداء بالإعلان عن إصدار صغير (مقارنة بإصدارات مكتبة الزّاي السابقة). لقد قمنا بكشط معظم ISBNdb، وجعلنا البيانات متاحة للتورنت على موقع مكتبة القراصنة العاكسة (تعديل: تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>؛ لن نقوم بربطها هنا مباشرة، فقط ابحث عنها). هذه حوالي 30.9 مليون سجل (20 جيجابايت كـ <a %(jsonlines)s>JSON Lines</a>؛ 4.4 جيجابايت مضغوطة). على موقعهم، يدعون أن لديهم بالفعل 32.6 مليون سجل، لذا قد نكون قد فقدنا بعضًا منها بطريقة ما، أو <em>قد</em> يكونون هم الذين يفعلون شيئًا خاطئًا. في كلتا الحالتين، لن نشارك حاليًا كيف فعلنا ذلك بالضبط — سنترك ذلك كتمرين للقارئ. ;-) ما سنشاركه هو بعض التحليل الأولي، لمحاولة الاقتراب من تقدير عدد الكتب في العالم. نظرنا إلى ثلاث مجموعات بيانات: مجموعة بيانات ISBNdb الجديدة هذه، إصدارنا الأصلي من metadata الذي قمنا بكشطه من مكتبة الظل ز-لايبراري (التي تشمل مكتبة جينيسيس)، وتفريغ بيانات المكتبة المفتوحة. تفريغ ISBNdb، أو كم عدد الكتب التي يتم حفظها إلى الأبد؟ إذا كنا سنقوم بإزالة التكرار بشكل صحيح من المكتبات الظلية، فما هي النسبة المئوية من جميع الكتب في العالم التي قمنا بحفظها؟ تحديثات حول <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر مكتبة مفتوحة حقًا في تاريخ البشرية. <em>إعادة تصميم WorldCat</em> البيانات <strong>التنسيق؟</strong> <a %(blog)s>حاويات رَبيدةُ آنّا (AAC)</a>، وهي في الأساس <a %(jsonlines)s>JSON Lines</a> مضغوطة باستخدام <a %(zstd)s>Zstandard</a>، بالإضافة إلى بعض الدلالات الموحدة. هذه الحاويات تحتوي على أنواع مختلفة من السجلات، بناءً على عمليات الجمع المختلفة التي قمنا بها. قبل عام، <a %(blog)s>بدأنا</a> في الإجابة على هذا السؤال: <strong>ما هي نسبة الكتب التي تم حفظها بشكل دائم بواسطة مكتبات الظل؟</strong> لنلقِ نظرة على بعض المعلومات الأساسية حول البيانات: بمجرد أن يدخل كتاب إلى مكتبة ظل مفتوحة البيانات مثل <a %(wikipedia_library_genesis)s>Library Genesis</a>، والآن <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، يتم نسخه في جميع أنحاء العالم (عبر التورنت)، مما يحفظه عمليًا إلى الأبد. للإجابة على سؤال نسبة الكتب التي تم حفظها، نحتاج إلى معرفة المقام: كم عدد الكتب الموجودة في المجموع؟ ومن المثالي أن لا نحصل فقط على رقم، بل على metadata فعلية. ثم يمكننا ليس فقط مطابقتها مع مكتبات الظل، ولكن أيضًا <strong>إنشاء قائمة TODO للكتب المتبقية للحفظ!</strong> يمكننا حتى أن نحلم بجهد جماعي للانتقال إلى أسفل هذه القائمة. قمنا بجمع البيانات من <a %(wikipedia_isbndb_com)s>ISBNdb</a>، وقمنا بتنزيل <a %(openlibrary)s>مجموعة بيانات المكتبة المفتوحة</a>، لكن النتائج لم تكن مرضية. كانت المشكلة الرئيسية هي عدم وجود تداخل كبير في أرقام ISBN. انظر إلى هذا المخطط الفين من <a %(blog)s>منشور مدونتنا</a>: لقد فوجئنا جدًا بمدى قلة التداخل بين ISBNdb وOpen Library، وكلاهما يتضمن البيانات بحرية من مصادر متنوعة، مثل جمع البيانات من الويب وسجلات المكتبات. إذا كان كلاهما يقوم بعمل جيد في العثور على معظم أرقام ISBN الموجودة، فإن دوائرهما بالتأكيد ستكون لها تداخل كبير، أو ستكون إحداهما مجموعة فرعية من الأخرى. جعلنا نتساءل، كم عدد الكتب التي تقع <em>تمامًا خارج هذه الدوائر</em>؟ نحن بحاجة إلى قاعدة بيانات أكبر. هذا هو الوقت الذي وضعنا فيه أنظارنا على أكبر قاعدة بيانات للكتب في العالم: <a %(wikipedia_worldcat)s>WorldCat</a>. هذه قاعدة بيانات مملوكة من قبل المنظمة غير الربحية <a %(wikipedia_oclc)s>OCLC</a>، التي تجمع سجلات metadata من المكتبات في جميع أنحاء العالم، مقابل منح تلك المكتبات الوصول إلى مجموعة البيانات الكاملة، وظهورها في نتائج البحث للمستخدمين النهائيين. على الرغم من أن OCLC هي منظمة غير ربحية، إلا أن نموذج عملهم يتطلب حماية قاعدة بياناتهم. حسنًا، نحن آسفون لنقول، أصدقاؤنا في OCLC، نحن نعطيها كلها. :-) على مدار العام الماضي، قمنا بجمع جميع سجلات WorldCat بدقة. في البداية، حصلنا على فرصة محظوظة. كانت WorldCat تقوم بإطلاق إعادة تصميم كاملة لموقعها (في أغسطس 2022). تضمن ذلك تجديدًا كبيرًا لأنظمتهم الخلفية، مما أدى إلى ظهور العديد من الثغرات الأمنية. استغللنا الفرصة على الفور، وتمكنا من جمع مئات الملايين (!) من السجلات في غضون أيام قليلة. بعد ذلك، تم إصلاح الثغرات الأمنية ببطء واحدة تلو الأخرى، حتى تم تصحيح آخر واحدة وجدناها قبل حوالي شهر. بحلول ذلك الوقت كان لدينا تقريبًا جميع السجلات، وكنا نسعى فقط للحصول على سجلات ذات جودة أعلى قليلاً. لذا شعرنا أنه حان الوقت للإصدار! 1.3 مليار استخراج من WorldCat <em><strong>ملخص:</strong> رَبيدةُ آنّا قامت باستخراج جميع بيانات WorldCat (أكبر مجموعة metadata للمكتبات في العالم) لإنشاء قائمة TODO للكتب التي تحتاج إلى الحفظ.</em> WorldCat تحذير: تم إهمال هذه التدوينة. لقد قررنا أن IPFS ليست جاهزة بعد للوقت الرئيسي. سنظل نربط الملفات على IPFS من رَبيدةُ آنّا عندما يكون ذلك ممكنًا، لكننا لن نستضيفها بأنفسنا بعد الآن، ولا نوصي الآخرين بعكسها باستخدام IPFS. يرجى الاطلاع على صفحة التورنت الخاصة بنا إذا كنت تريد المساعدة في الحفاظ على مجموعتنا. ساعد في توزيع مكتبة الزّاي على IPFS تنزيل من خادوم شريك (كمكتبة الزّاي والتكّوين وغيرهما) مجمع البيانات العلمية استعارة خارجية استعارة خارجية (لعُسر القراءة) تنزيل خارجي استكشف البيانات الوصفية موجودة في التورنتات رجوع  (+%(num)s زيادة) غير مدفوع مدفوع مَلْغي منتهي الصلاحية في انتظار آنّا لتأكيده غير صالح باقي الكتابة باللغة الإنگليزية. اذهب إعادة تعيين تقدم الأخير إن لم يُقبل بريدك الرقمي من منتديات مكتبة التَّكوين، فسجّل بـ<a %(a_mail)s> Proton Mail </a> (مجانًا). و<a %(a_manual)s> اطلب </a> تفعيل حسابك. (قد يُلزم <a %(a_browser)s> التحقق من المتصفح </a> - تنزيلات غير محدودة!) خادوم شريك سريع #%(number)s (موصى به) (أسرع قليلاً ولكن مع قائمة انتظار) (لا يلزم التحقق من المتصفح) (لا تحقق من المتصفح أو قوائم الانتظار) (لا توجد قائمة انتظار، ولكن يمكن أن يكون بطيئًا جدًا) خادوم شريك بطيء #%(number)s الكتاب الصوتي قصة مصورة كتاب (خيالي) كتاب (واقعي) كتاب (غير معروف) مقال أكاديمي مجلة مقطوعة موسيقية آخر وثيقة معايير لم يتم تحويل جميع الصفحات إلى PDF عُلّم عليه «معطّلٌ» في مكتبة التَّكوين Libgen.li لا تظهر في مكتبة التَّكوين Libgen.li لا تظهر في قسم المحتوى الخيالي من مكتبة التَّكوين Libgen.rs لا تظهر في قسم المحتوى الواقعي من مكتبة التَّكوين Libgen.rs فشل تشغيل exiftool على هذا الملف مُعلّمة كـ "ملف سيء" في مكتبة الزّاي مفقود من مكتبة الزّاي مُعلّمة كـ "سبام" في مكتبة الزّاي لا يمكن فتح الملف (قد يكون تالف ، أو مقيّد بالحقوق الرقمية) ادعاءٌ بحق التأليف علّة في التنزيل (قد يكون بسبب الاتصال، أو بَلاغ أخطاء، أو سرعة ضعيفة) بيانات وصفية خاطئة (قد يكون الخطأ في العنوان، أو الوصف، أو صورة الغلاف) أخرى جودة رديئة ( قد يكون التنسيق خاطئًا، أو التصوير سيئًا، أو صفحات مفقودة) ملف عشوائي / يجب حذف الملف (قد يكون إعلانًا، أو محتوًى مسيئ) %(amount)s (%(amount_usd)s) بمجموع %(amount)s %(amount)s (%(amount_usd)s) المجموع محب للكتب قيّم مكتبةٍ مسعود مُكتنز بارع رابد عجيب تنزيلات إضافية Cerlalc البيانات الوصفية التشيكية DuXiu 读秀 فهرس كتب EBSCOhost الإلكترونية كتب جوجل جودريدز HathiTrust IA الإعارة الرقمية المُتحكم بها من IA مجمع بيانات النظام القياسي الدولي لترقيم الكتب (ISBNdb) ISBN GRP Libgen.li باستثناء "scimag" Libgen.rs غير خيالي وخيالي ليبي MagzDB Nexus/STC OCLC (WorldCat): الفهرس العالمي هو مشروع فهرس موحد، تابع لمركز المكتبة الرقمية على الإنترنت المكتبة المفتوحة (OpenLibrary) المكتبة الوطنية الروسية مجمع العلوم (Sci-Hub) عبر Libgen.li "scimag" Sci-Hub / Libgen "scimag" ترانتور التحميلات إلى AA مكتبة الزّاي ( Z-Library) مكتبة الزّاي الصينية عنوان، المؤلف، DOI، ISBN، MD5، … بحث المؤلف الوصف وملاحظات البيانات الوصفية النسخة الاسم الأصلي للملف الناشر (ابحث في حقل محدد) العنوان سنة النشر التفاصيل التقنية «بالإنگليزية» لهذه العملة حد أدنى أعلى من المعتاد. رجاءً اختر مدّةً أو عملة مختلفة. طلبك لم يُكمَل. حاول مرة أخرى بعد دقائق، وإن استمر هذا، فراسلنا بـ %(email)s واضمُم في رسالتك لقطة للشاشة.. حدث خطأ غير معروف. ارسل رسالة لـ %(email)s واضمُم في رسالتك لقطة للشاشة.. خطأ حدث في عملية الدفع. انتظر قليلًا وحاول مرة أخرى. إن استمر معك هذا الخطأ لأكثر من 24 ساعة، فراسلنا بـ%(email)s واضمُم في رسالتك لقطة للشاشة. نحن ندير حملة لجمع التبرعات <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html"> النسخ الاحتياطي </a> لأكبر الرسوم الهزلية مكتبة الظل في العالم. شكرا لدعمك! <a href="/donate"> تبرع. </a> إذا كنت لا تستطيع التبرع ، ففكر في دعمنا بإخبار أصدقائك، ومتابعتنا على <a href="https://www.reddit.com/r/Annas_Archive">Reddit </a> أو <a href="https://t.me/annasarchiveorg">Telegram </a>. لا تراسلنا <a %(a_request)s>لطلب الكتب</a><br>أو لرفع مجموعة قليلة من الكتب (أقل من عشرة آلاف) <a %(a_upload)s></a>. أرشيف آنا مطالبات حقوق التأليف والنشر / DMCA للتواصل ريديت البدائل SLUM (%(unaffiliated)s) غير مُنتمٍ لأي جهة رَبيدةُ آنّا تحتاج إلى مساعدتك! إذا تبرعت الآن، ستحصل على <strong>ضعف</strong> عدد التنزيلات السريعة. يحاول الكثيرون إسقاطنا، لكننا نقاوم. إذا تبرعت هذا الشهر، ستحصل على <strong>ضعف</strong> عدد التنزيلات السريعة. صالح حتى نهاية هذا الشهر. إنقاذ المعرفة البشرية: هدية العيد العظيمة! سيتم تمديد العضويات وفقًا لذلك. الخوادم الشريكة غير متاحة بسبب إغلاق الاستضافة. يجب أن تعود للعمل قريبًا. لزيادة مرونة رَبيدةُ آنّا، نحن نبحث عن متطوعين لتشغيل المرايا. لدينا طريقة جديدة للتبرع: %(method_name)s الرجاء التفكير في %(donate_link_open_tag)s التبرع </a> — إدارة هذا الموقع ليست قليلة التكلفة، وتبرعك حقاً يصنع فرقاً. شكراً جزيلاً لك. اذكر صديقًا لك ووتحصلان كلاكما على %(percentage)s تنزيلات سريعة إضافية! مفاجأة أحد أفراد أسرته، ومنحهم حسابا مع العضوية. الهدية المثالية لعيد الحب! اعرف المزيد… حساب نشاط متقدم مدونة آنا ↗ برمجيات آنا ↖ تجريبي مستكشف الأكواد مجموعات البيانات تبرع الملفات المُنزّلة الأسئلة الشائعة الصفحة الرئيسية تحسين البيانات الوصفية بيانات LLM دخول / تسجيل تبرعاتي الملف الشخصي العام بحث حماية ملفات تورنت ترجمة ↗ التطوع والمكافآت أحدث التنزيلات: 📚&nbsp;أعظم المكاتب المفتوحة في تأريخ البشر. ⭐️&nbsp;نُظهر ما في مجمع العلوم (Sci-Hub)، ومكتبة التَّكوين (LibGen library)، ومكتبة الزّاي (Z-Lib)، وغيرهم الكثير. 📈&nbsp;%(book_any)s كتابًا، و%(journal_article)s ورقةً، و%(book_comic)s قصّة مصوّرة، و%(magazine)s مجلّة = محفوظات إلى الأبد.  و  والكثير القارئ (DuXiu) مكتبة الإعارة من رَبيدةُ الشّابكة مكتبة التَّكوين (LibGen library) 📚&nbsp;أعظم المكاتب المفتوحة في تأريخ البشر. 📈&nbsp;%(book_count)s&nbsp;كتابًا، و%(paper_count)s&nbsp; ورقةً محفوظات إلى الأبد. ⭐️&nbsp;نُظهر ما في %(libraries)s. نجرد ونفتح مصادر %(scraped)s. كل كودنا وبياناتنا مفتوحة المصدر بالكامل. المكتبة المفتوحة (OpenLib) مجمع العلوم (Sci-Hub) ،  📚&nbsp;أعظم مكتبة ذات بيانات مفتوحة في عالم.<br>⭐️ نُظهر ما في مجمع العلوم ومكتبة التَّكوين، وغيرهما الكثير. مكتبة الزّاي (Z-Lib) أرشيف آنا طلب خاطئ. زُرْ %(websites)s. أكبر مكتبة مفتوحة المصدر مفتوحة المصدر في العالم. يتضمن Sci-Hub و Library Genesis و Z-Library والمزيد. ابحث في أرشيف آنا أرشيف آنا يرجى التحديث للمحاولة مرة أخرى. <a %(a_contact)s>اتصل بنا</a> إذا استمرت المشكلة لعدة ساعات. 🔥 مشكلة في تحميل هذه الصفحة <li>1. تابعنا على <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>، أو <a href="https://t.me/annasarchiveorg">تلغرام</a> أ>.</li><li>2. انشر رَبيدة آنّا في إكس (تويتر سابقًا)، وريديت، وتِك تُك، وإنستَغرام، وفي مقاهي القراءة ومكتبات بلدتك، ولأ مكان تذهب إليه! نحن ضد التستّرو تمرير العلم خفية - فإن حاربونا وأصرّوا على إسقاطنا فسنظهر في مكان آخر، هذا لأن ترميز موقعنا وبياناتنا مفتوحة كلّها للعامة.</li><li>3. فإن كنت قادرًا، <a href="/donate">تبرّع</a>.</li><li>4. وساعد في <a href="https://translate.annas-software.org/">ترجمة</a> موقعنا إلى لغات مختلفة.</li><li>5. إذا كنت مهندس برمجيات، فساهم في <a href="https://annas-software.org/">المصدر المفتوح</a>، أو شارك ما لدينا وساعد <a href="/datasets">بالتورنت</a>.</li> 10. أنشئ أو ساعد في الحفاظ على صفحة رَبيدة آنّا في ويكيبيديا بلغتك. 11. نتطلع لوضع إعلانات صغيرة وجميلة غير مزعجة. فأخبرنا إن كنت ترغب في الإعلان على رَبيدة آنّا. 6. إن كنت باحثًا أمنيًا، فيمكننا الإستعانة بمهاراتك هجومًا ودفاعًا. راجع صفحتنا عن <a %(a_security)s>الأمان</a>. 7. نبحث عن خبراء في مجال المدفوعات للتجار المجهولين. هل يمكنك مساعدتنا في إضافة طرق أكثر ملاءمة للتبرع؟ بَيْ بَالْ، وي شات، بطاقات الدفع المسبق. فإن كنت تعرف شخصًا فاتصل بنا واخبرنا. 8. نسعى دائمًا لإضافة مساحة أكبر للخادوم. 9. ساعد بالإبلاغ عن مشكلة في ملف واترك تعاليقًا وانشئ قوائمًا مباشرة على هذا الموقع. وساعد<a %(a_upload)s>في رفع كتبٍ أكثر</a> أو اصلح مشاكل الملف ونسّق الكتب الموجودة. لمزيد من المعلومات حول كيفية التطوع، راجع صفحة <a %(a_volunteering)s>التطوع والمكافآت</a> الخاصة بنا. نحن ندين بدين حرية إيصال العلم والحفاظ على المعرفة والثقافة. ومحرك البحث هذا أصله كبار مكتبات الظل. فنقدر ما قدّمه مؤسسو مكتبات الظل كلّها ونرجوا أن يكون هذا مساعدًا لهم في توسيع أعمالهم. لتعلم ما عندنا من تطورات، تابع آنَّا على <a href="https://www.reddit.com/r/Annas_Archive/"> ريديت </a> أو <a href="https://t.me/annasarchiveorg">تليغرام</a>. للأسئلة والاقتراحات تواصل مع آنَّا بالبريد الرقمي %(email)s. معرّف الحساب: %(account_id)s اخرج ❌ حدث خطأ ما. يرجى إعادة تحميل الصفحة وحاول مرة أخرى. ✅ خرجت. أعد تحميل الصفحة للدخول مرة أخرى. استخدام التنزيل السريع (آخر 24 ساعة): <strong>%(used)s %(total)s</strong> نوع الانضمام: <strong>%(tier_name)s </strong> حتى%(until_date)s <a %(a_extend)s> (مدّها) </a> يمكنك الجمع بين عضويات متعددة (ستُضاف التنزيلات السريعة لكل 24 ساعة معًا). نوع الانضمام: <strong> لا يوجد </ strong> <a %(a_become)s> (انضم) </a> تواصل مع آنّا بـ %(email)s إن كنت مهتمًا للإشتراك في فئة أعلى. ملفّك العامة: %(profile_link)s المفتاح السري (لا تشاركه!): %(secret_key)s عرض انضم معنا! اشترك في <a %(a_tier)sفئة أعلى</a> للإنضمام لمجموعتنا. مجموعة التلگرام الحصرية: %(link)s الحساب أي تنزيل؟ ادخل لا تفقد مفتاحك! مفتاح سري غير صحيح. تحقق من مفتاحك وحاول مرة أخرى، أو سجّل حسابًا جديدًا بالأسفل. المفتاح السري اكتب مفتاحك السري للدخول: حساب ببريد رقميّ قديم؟ اكتب <a %(a_open)s> بريدك الرقمي هنا </a>. سجّل حسابًا جديدًا ألا تملك حسابًا حتى الآن؟ نجح التسجيل! مفتاحك السري هو: <span %(span_key)s>%(key)s </span> احفظ هذا المفتاح ولا تفقده. فإن فقدته فلن تقدر على الدخول لحسابك. <li %(li_item)s> <strong> علّمها. </ strong> علّم هذه الصفحة لاسترداد مفتاحك. </li> <li %(li_item)s> <strong> نزّله </strong> انقر على <a %(a_download)s> هذا الرابط </a> لتنزيل مفتاحك. </li> <li %(li_item)s> <strong>احفظه في حافظة </ strong> استخدم حافظة كلمات المرور لحفظ مفتاحك. </li> دخول / تسجيل التحقق من المتصفح تحذير: يحتوي الكود على أحرف Unicode غير صحيحة، وقد يتصرف بشكل غير صحيح في مواقف مختلفة. يمكن فك تشفير البيانات الثنائية الخام من تمثيل base64 في عنوان URL. وصف تسمية بادئة عنوان URL لكود محدد موقع ويب أكواد تبدأ بـ “%(prefix_label)s” يرجى عدم استخراج البيانات من هذه الصفحات. بدلاً من ذلك، نوصي <a %(a_import)s>بتوليد</a> أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا، وتشغيل <a %(a_software)s>الكود المفتوح المصدر</a> الخاص بنا. يمكن استكشاف البيانات الخام يدويًا من خلال ملفات JSON مثل <a %(a_json_file)s>هذا الملف</a>. أقل من %(count)s سجلات عنوان URL عام مستكشف الأكواد فهرس استكشف الأكواد التي تم وسم السجلات بها، حسب البادئة. يعرض عمود "السجلات" عدد السجلات الموسومة بالأكواد ذات البادئة المعطاة، كما هو موضح في محرك البحث (بما في ذلك السجلات التي تحتوي على البيانات الوصفية فقط). يعرض عمود "الأكواد" عدد الأكواد الفعلية التي تحتوي على البادئة المعطاة. بادئة الكود المعروفة “%(key)s” المزيد… بادئة 0 سجلات مطابقة لـ “%(prefix_label)s” %(count)s سجل مطابق لـ “%(prefix_label)s” 2 سجلات مطابقة لـ “%(prefix_label)s” page.codes.records_starting_with page.codes.records_starting_with %(count)s سجلات مطابقة لـ “%(prefix_label)s” أكواد سجلات سيتم استبدال “%%” بقيمة الكود ابحث في رَبيدةُ آنّا الأكواد عنوان URL لكود محدد: “%(url)s” قد تستغرق هذه الصفحة بعض الوقت للتوليد، ولهذا السبب تتطلب كابتشا من Cloudflare. يمكن <a %(a_donate)s>للأعضاء</a> تخطي الكابتشا. تم الإبلاغ عن الإساءة: نسخة أفضل هل تريد الإبلاغ عن هذا المستخدم لسلوك مسيء أو غير لائق؟ مشكلة في الملف: %(file_issue)s تعليق مخفي الرد الإبلاغ عن إساءة لقد أبلغت عن هذا المستخدم بسبب الإساءة. ولن نرد على أي مطالبات للحقوق على هذا البريد؛ فاملء النموذج إن أردت. اظهر البريد ونُرحب بملاحظاتكم واستفساراتكم! وتصلنا رسائل مزعجة عشوائية كثيرة، فرجاءً علّم على المربعات لنعلم أنك قد فهمت شروط مراسلتنا. وسنحذف كلّ الرسائل التي تصلنا للمطالبة بالحقوق مالم تأتي من هذا النموذج ولن نعتدّ بها. للمطالبة بحقوق الملكية الرقيمة: املء <a %(a_copyright)s>هذا النموذج</a>. البريد الإلكتروني للتواصل عناوين URL على رَبيدةُ آنّا (مطلوبة). واحد في كل سطر. يرجى تضمين عناوين URL التي تصف نفس الطبعة من الكتاب فقط. إذا كنت ترغب في تقديم شكوى لعدة كتب أو عدة طبعات، يرجى تقديم هذا النموذج عدة مرات. سيتم رفض الشكاوى التي تجمع بين عدة كتب أو طبعات. العنوان (مطلوب) وصف واضح للمادة المصدرية (مطلوب) البريد الإلكتروني (مطلوب) عناوين URL للمادة المصدرية، واحد في كل سطر (مطلوب). يرجى تضمين أكبر عدد ممكن، لمساعدتنا في التحقق من شكواك (مثل Amazon، WorldCat، Google Books، DOI). أرقام ISBN للمادة المصدرية (إذا كان ذلك ينطبق). واحد في كل سطر. يرجى تضمين الأرقام التي تتطابق تمامًا مع الطبعة التي تبلغ عنها. اسمك (مطلوب) ❌ حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى. ✅ شكرًا لك على تقديم شكوى حقوق الطبع والنشر الخاصة بك. سنقوم بمراجعتها في أقرب وقت ممكن. يرجى إعادة تحميل الصفحة لتقديم شكوى أخرى. <a %(a_openlib)s>عناوين Open Library</a> للمادة المصدرية، واحد في كل سطر. يرجى أخذ لحظة للبحث في Open Library عن المادة المصدرية الخاصة بك. سيساعدنا ذلك في التحقق من شكواك. رقم الهاتف (مطلوب) البيان والتوقيع (مطلوب) تقديم الشكوى إذا كان لديك شكوى DMCA أو شكوى حقوق طبع ونشر أخرى، يرجى ملء هذا النموذج بدقة قدر الإمكان. إذا واجهت أي مشاكل، يرجى الاتصال بنا على عنوان DMCA المخصص: %(email)s. لاحظ أن الشكاوى المرسلة عبر البريد الإلكتروني إلى هذا العنوان لن يتم معالجتها، فهو مخصص للأسئلة فقط. يرجى استخدام النموذج أدناه لتقديم شكاويك. نموذج تقديم شكوى حقوق الطبع والنشر / DMCA مثال على سجل في رَبيدةُ آنّا التورنت بواسطة رَبيدةُ آنّا تنسيق حاويات رَبيدةُ آنّا البرامج النصية لاستيراد البيانات الوصفية إذا كنت مهتمًا بعمل نسخة مرآة من مجموعة البيانات هذه لأغراض <a %(a_archival)s>الأرشفة</a> أو <a %(a_llm)s>تدريب LLM</a>، يرجى الاتصال بنا. آخر تحديث: %(date)s الموقع الرئيسي %(source)s توثيق البيانات الوصفية (معظم الحقول) الملفات التي تم عكسها بواسطة رَبيدةُ آنّا: %(count)s (%(percent)s%%) الموارد إجمالي الملفات: %(count)s إجمالي حجم الملفات: %(size)s منشور مدونتنا عن هذه البيانات <a %(duxiu_link)s>Duxiu</a> هي قاعدة بيانات ضخمة للكتب الممسوحة ضوئيًا، أنشأتها <a %(superstar_link)s>مجموعة المكتبة الرقمية SuperStar</a>. معظمها كتب أكاديمية، تم مسحها ضوئيًا لجعلها متاحة رقميًا للجامعات والمكتبات. لجمهورنا الناطق باللغة الإنجليزية، <a %(princeton_link)s>برينستون</a> و<a %(uw_link)s>جامعة واشنطن</a> لديهما نظرات عامة جيدة. هناك أيضًا مقال ممتاز يقدم المزيد من الخلفية: <a %(article_link)s>“رقمنة الكتب الصينية: دراسة حالة لمحرك البحث SuperStar DuXiu Scholar”</a>. تم قرصنة الكتب من Duxiu منذ فترة طويلة على الإنترنت الصيني. عادةً ما يتم بيعها بأقل من دولار واحد من قبل البائعين. يتم توزيعها عادةً باستخدام ما يعادل Google Drive في الصين، والذي غالبًا ما يتم اختراقه للسماح بمساحة تخزين أكبر. يمكن العثور على بعض التفاصيل التقنية <a %(link1)s>هنا</a> و<a %(link2)s>هنا</a>. على الرغم من أن الكتب تم توزيعها بشكل شبه علني، إلا أنه من الصعب جدًا الحصول عليها بكميات كبيرة. كان هذا على رأس قائمة مهامنا، وخصصنا عدة أشهر من العمل بدوام كامل لذلك. ومع ذلك، في أواخر عام 2023، تواصل معنا متطوع رائع ومذهل وموهوب، وأخبرنا أنه قد قام بكل هذا العمل بالفعل — بتكلفة كبيرة. شارك المجموعة الكاملة معنا، دون توقع أي شيء في المقابل، باستثناء ضمان الحفظ طويل الأمد. حقًا أمر رائع. مزيد من المعلومات من متطوعينا (ملاحظات خام): مقتبس من <a %(a_href)s>منشور مدونتنا</a>. DuXiu 读秀 0 ملفات %(count)s ملف 2 ملفات page.datasets.files page.datasets.files %(count)s ملفات هذه المجموعة مرتبطة ارتباطًا وثيقًا بمجموعة <a %(a_datasets_openlib)s>Open Library</a>. تحتوي على نسخة من جميع البيانات الوصفية وجزء كبير من الملفات من مكتبة الإعارة الرقمية المُتحكم بها من IA. يتم إصدار التحديثات بتنسيق <a %(a_aac)s>حاويات رَبيدةُ آنّا</a>. يتم الإشارة إلى هذه السجلات مباشرة من مجموعة Open Library، ولكنها تحتوي أيضًا على سجلات غير موجودة في Open Library. لدينا أيضًا عدد من ملفات البيانات التي تم جمعها بواسطة أعضاء المجتمع على مر السنين. تتكون المجموعة من جزأين. تحتاج إلى كلا الجزأين للحصول على جميع البيانات (باستثناء السيول التي تم تجاوزها، والتي يتم شطبها في صفحة السيول). مكتبة الإعارة الرقمية إصدارنا الأول، قبل أن نعتمد على <a %(a_aac)s>تنسيق حاويات رَبيدةُ آنّا (AAC)</a>. يحتوي على البيانات الوصفية (بصيغة json و xml)، ملفات pdf (من أنظمة الإعارة الرقمية acsm و lcpdf)، وصور مصغرة للأغلفة. إصدارات جديدة تدريجية، باستخدام AAC. تحتوي فقط على البيانات الوصفية مع الطوابع الزمنية بعد 2023-01-01، حيث أن الباقي مغطى بالفعل بواسطة "ia". أيضًا جميع ملفات pdf، هذه المرة من أنظمة الإعارة acsm و "bookreader" (قارئ الويب الخاص بـ IA). على الرغم من أن الاسم ليس دقيقًا تمامًا، إلا أننا ما زلنا نملأ ملفات bookreader في مجموعة ia2_acsmpdf_files، لأنها حصرية. الإعارة الرقمية الخاضعة للتحكم من IA 98%%+ من الملفات قابلة للبحث. مهمتنا هي أرشفة جميع الكتب في العالم (وكذلك الأوراق والمجلات، إلخ)، وجعلها متاحة على نطاق واسع. نعتقد أن جميع الكتب يجب أن تكون معكوسة على نطاق واسع لضمان التكرار والمرونة. لهذا السبب نحن نجمع الملفات من مجموعة متنوعة من المصادر. بعض المصادر مفتوحة تمامًا ويمكن عكسها بكميات كبيرة (مثل Sci-Hub). البعض الآخر مغلق وحمايي، لذا نحاول استخراجها من أجل "تحرير" كتبهم. والبعض الآخر يقع في مكان ما بينهما. يمكن تحميل جميع بياناتنا عبر <a %(a_torrents)s>التورنت</a>، ويمكن توليد جميع بياناتنا الوصفية أو <a %(a_anna_software)s>تنزيلها</a> كقواعد بيانات ElasticSearch وMariaDB. يمكن استكشاف البيانات الخام يدويًا من خلال ملفات JSON مثل <a %(a_dbrecord)s>هذا</a>. البيانات الوصفية موقع ISBN آخر تحديث: %(isbn_country_date)s (%(link)s) الموارد تقوم الوكالة الدولية لـ ISBN بإصدار النطاقات التي تم تخصيصها للوكالات الوطنية لـ ISBN بانتظام. من هذا يمكننا استنتاج البلد أو المنطقة أو المجموعة اللغوية التي ينتمي إليها هذا ISBN. نحن نستخدم هذه البيانات حاليًا بشكل غير مباشر، من خلال مكتبة Python <a %(a_isbnlib)s>isbnlib</a>. معلومات الدولة الخاصة بـ ISBN هذا تفريغ للعديد من المكالمات إلى isbndb.com خلال سبتمبر 2022. حاولنا تغطية جميع نطاقات ISBN. هذه حوالي 30.9 مليون سجل. على موقعهم، يدعون أن لديهم فعليًا 32.6 مليون سجل، لذا قد نكون قد فقدنا بعضًا منها بطريقة ما، أو <em>قد</em> يكونون قد ارتكبوا خطأ ما. استجابات JSON هي تقريبًا خام من خادمهم. إحدى مشكلات جودة البيانات التي لاحظناها هي أنه بالنسبة لأرقام ISBN-13 التي تبدأ برمز مختلف عن "978-"، فإنهم لا يزالون يتضمنون حقل "isbn" الذي هو ببساطة رقم ISBN-13 مع حذف الأرقام الثلاثة الأولى (وإعادة حساب رقم التحقق). هذا خطأ واضح، ولكن هذا هو ما يبدو أنهم يفعلونه، لذا لم نقم بتغييره. مشكلة أخرى محتملة قد تواجهها هي حقيقة أن حقل "isbn13" يحتوي على تكرارات، لذا لا يمكنك استخدامه كمفتاح أساسي في قاعدة البيانات. يبدو أن الحقول "isbn13"+"isbn" مجتمعة تكون فريدة. الإصدار 1 (2022-10-31) تورنتات الخيال متأخرة (على الرغم من أن المعرفات ~4-6M لم يتم تورنتها لأنها تتداخل مع تورنتات مكتبة الزّاي الخاصة بنا). منشور مدونتنا عن إصدار القصص المصورة تورنتات القصص المصورة على رَبيدةُ آنّا للحصول على خلفية حول الفروع المختلفة لـ Library Genesis، انظر الصفحة الخاصة بـ <a %(a_libgen_rs)s>Libgen.rs</a>. يحتوي Libgen.li على معظم نفس المحتوى والبيانات الوصفية مثل Libgen.rs، ولكنه يحتوي على بعض المجموعات الإضافية، وهي الكوميديا والمجلات والوثائق القياسية. كما أنه دمج <a %(a_scihub)s>Sci-Hub</a> في بياناته الوصفية ومحرك البحث، وهو ما نستخدمه لقاعدة بياناتنا. البيانات الوصفية لهذه المكتبة متاحة مجانًا <a %(a_libgen_li)s>في libgen.li</a>. ومع ذلك، فإن هذا الخادم بطيء ولا يدعم استئناف الاتصالات المقطوعة. نفس الملفات متاحة أيضًا على <a %(a_ftp)s>خادم FTP</a>، والذي يعمل بشكل أفضل. يبدو أن الكتب غير الخيالية قد تباعدت أيضًا، ولكن دون وجود سيل جديد. يبدو أن هذا قد حدث منذ أوائل عام 2022، على الرغم من أننا لم نتحقق من ذلك. وفقًا لمسؤول Libgen.li، يجب أن تكون مجموعة "fiction_rus" (الأدب الروسي) مغطاة بتورنتات يتم إصدارها بانتظام من <a %(a_booktracker)s>booktracker.org</a>، وخاصة تورنتات <a %(a_flibusta)s>flibusta</a> و<a %(a_librusec)s>lib.rus.ec</a> (التي نقوم بعكسها <a %(a_torrents)s>هنا</a>، على الرغم من أننا لم نحدد بعد أي تورنتات تتوافق مع أي ملفات). مجموعة الأدب لديها تورنتات خاصة بها (مفصولة عن <a %(a_href)s>Libgen.rs</a>) تبدأ من %(start)s. بعض النطاقات التي لا تحتوي على تورنتات (مثل نطاقات الأدب f_3463000 إلى f_4260000) من المحتمل أن تكون ملفات مكتبة الزّاي (أو ملفات مكررة أخرى)، على الرغم من أننا قد نرغب في إجراء بعض إزالة التكرار وإنشاء تورنتات للملفات الفريدة لـ lgli في هذه النطاقات. يمكن العثور على إحصائيات لجميع المجموعات <a %(a_href)s>على موقع libgen</a>. التورنتات متاحة لمعظم المحتوى الإضافي، وخاصة التورنتات للكتب المصورة، المجلات، والوثائق القياسية التي تم إصدارها بالتعاون مع رَبيدةُ آنّا. يرجى ملاحظة أن ملفات التورنت التي تشير إلى "libgen.is" هي عاكسات صريحة لـ <a %(a_libgen)s>Libgen.rs</a> (".is" هو نطاق مختلف يستخدمه Libgen.rs). مورد مفيد لاستخدام البيانات الوصفية هو <a %(a_href)s>هذه الصفحة</a>. %(icon)s مجموعتهم "fiction_rus" (الأدب الروسي) لا تحتوي على تورنتات مخصصة، ولكنها مغطاة بتورنتات من الآخرين، ونحن نحافظ على <a %(fiction_rus)s>عاكسة</a>. تورنتات الأدب الروسي على رَبيدةُ آنّا تورنتات الخيال على رَبيدةُ آنّا منتدى النقاش البيانات الوصفية البيانات الوصفية عبر FTP تورنتات المجلات على رَبيدةُ آنّا معلومات حقل البيانات الوصفية عاكسة لتورنتات أخرى (وتورنتات فريدة للخيال والقصص المصورة) تورنتات الوثائق القياسية على رَبيدةُ آنّا Libgen.li تورنتات بواسطة رَبيدةُ آنّا (أغلفة الكتب) تُعرف Library Genesis بالفعل بجعل بياناتها متاحة بسخاء بكميات كبيرة من خلال التورنتات. تتكون مجموعتنا من Libgen من بيانات مساعدة لا يفرجون عنها مباشرة، بالشراكة معهم. شكرًا جزيلاً لكل من شارك في Library Genesis للعمل معنا! مدونتنا حول إصدار أغلفة الكتب هذه الصفحة تتعلق بالنسخة ".rs". وهي معروفة بنشرها المتسق لكل من بياناتها الوصفية والمحتويات الكاملة لفهرس كتبها. يتم تقسيم مجموعة كتبها بين قسم الخيال وقسم غير الخيال. مورد مفيد لاستخدام البيانات الوصفية هو <a %(a_metadata)s>هذه الصفحة</a> (تحظر نطاقات IP، قد يكون استخدام VPN مطلوبًا). اعتبارًا من 2024-03، يتم نشر التورنتات الجديدة في <a %(a_href)s>هذا الموضوع في المنتدى</a> (يتم حظر نطاقات IP، قد يكون استخدام VPN مطلوبًا). تورنتات الكتب الخيالية على رَبيدةُ آنّا تورنتات الكتب الخيالية لـ Libgen.rs منتدى النقاش لـ Libgen.rs بيانات وصفية لـ Libgen.rs معلومات حقل البيانات الوصفية لـ Libgen.rs تورنتات الكتب غير الخيالية لـ Libgen.rs تورنتات الكتب غير الخيالية على رَبيدةُ آنّا %(example)s لكتاب خيالي. هذا <a %(blog_post)s>الإصدار الأول</a> صغير جدًا: حوالي 300 جيجابايت من أغلفة الكتب من فرع Libgen.rs، سواء كانت خيالية أو غير خيالية. وهي منظمة بنفس الطريقة التي تظهر بها على libgen.rs، على سبيل المثال: %(example)s لكتاب غير خيالي. تمامًا مثل مجموعة مكتبة الزّاي، وضعناها جميعًا في ملف .tar كبير، والذي يمكن تركيبه باستخدام <a %(a_ratarmount)s>ratarmount</a> إذا كنت ترغب في تقديم الملفات مباشرة. الإصدار 1 (%(date)s) القصة السريعة لتفرعات Library Genesis (أو "Libgen") المختلفة هي أنه مع مرور الوقت، اختلف الأشخاص المشاركون في Library Genesis وذهبوا في طرقهم المنفصلة. وفقًا لهذا <a %(a_mhut)s>المنشور في المنتدى</a>، كان Libgen.li مستضافًا في الأصل على "http://free-books.dontexist.com". تم إنشاء النسخة ".fun" من قبل المؤسس الأصلي. يتم إعادة تصميمها لصالح نسخة جديدة وأكثر توزيعًا. <a %(a_li)s>النسخة ".li"</a> تحتوي على مجموعة ضخمة من القصص المصورة، بالإضافة إلى محتويات أخرى، التي لم تتوفر بعد للتنزيل الجماعي عبر التورنتات. لديها مجموعة تورنت منفصلة لكتب الخيال، وتحتوي على البيانات الوصفية لـ <a %(a_scihub)s>Sci-Hub</a> في قاعدة بياناتها. النسخة ".rs" تحتوي على بيانات مشابهة جدًا، وتصدر مجموعتها بشكل متسق في تورنتات ضخمة. يتم تقسيمها تقريبًا إلى قسم "الخيال" وقسم "غير الخيال". أصلاً في "http://gen.lib.rus.ec". <a %(a_zlib)s>مكتبة الزّاي</a> هي أيضًا نوع من تفرعات Library Genesis، على الرغم من أنهم استخدموا اسمًا مختلفًا لمشروعهم. Libgen.rs نحن أيضًا نثري مجموعتنا بمصادر البيانات الوصفية فقط، والتي يمكننا مطابقتها مع الملفات، على سبيل المثال باستخدام أرقام ISBN أو حقول أخرى. فيما يلي نظرة عامة على تلك المصادر. مرة أخرى، بعض هذه المصادر مفتوحة تمامًا، بينما يجب علينا جمعها من مصادر أخرى. لاحظ أنه في بحث البيانات الوصفية، نعرض السجلات الأصلية. نحن لا نقوم بدمج السجلات. مصادر البيانات الوصفية فقط مكتبة الزّاي هو مشروع مفتوح المصدر من Internet Archive لفهرسة كل كتاب في العالم. لديها واحدة من أكبر عمليات مسح الكتب في العالم، ولديها العديد من الكتب المتاحة للإعارة الرقمية. كتالوج البيانات الوصفية للكتب متاح للتنزيل مجانًا، وهو مدرج في رَبيدةُ آنّا (على الرغم من أنه غير متاح حاليًا في البحث، إلا إذا قمت بالبحث صراحةً عن معرف مكتبة الزّاي). مكتبة الزّاي باستثناء التكرارات آخر تحديث نسب عدد الملفات %% معكوسة بواسطة AA / التورنتات المتاحة الحجم المصدر فيما يلي نظرة سريعة على مصادر الملفات في رَبيدةُ آنّا. نظرًا لأن المكتبات الظلية غالبًا ما تقوم بمزامنة البيانات من بعضها البعض، فهناك تداخل كبير بين المكتبات. لهذا السبب لا تتطابق الأرقام مع الإجمالي. تُظهر نسبة "المعكوسة والمزروعة بواسطة رَبيدةُ آنّا" عدد الملفات التي نعكسها بأنفسنا. نقوم بزراعة تلك الملفات بكميات كبيرة عبر التورنتات، ونجعلها متاحة للتنزيل المباشر عبر مواقع الشركاء. نظرة عامة الإجمالي تورنتات على رَبيدةُ آنّا لمزيد من المعلومات حول Sci-Hub، يرجى الرجوع إلى <a %(a_scihub)s>الموقع الرسمي</a>، <a %(a_wikipedia)s>صفحة ويكيبيديا</a>، وهذه <a %(a_radiolab)s>المقابلة الصوتية</a>. يرجى ملاحظة أن Sci-Hub قد تم <a %(a_reddit)s>تجميده منذ عام 2021</a>. تم تجميده من قبل، ولكن في عام 2021 تمت إضافة بضعة ملايين من الأوراق. ومع ذلك، يتم إضافة عدد محدود من الأوراق إلى مجموعات "scimag" في Libgen، ولكن ليس بما يكفي لتبرير تورنتات جديدة بالجملة. نستخدم البيانات الوصفية لـ Sci-Hub كما هو مقدم من <a %(a_libgen_li)s>Libgen.li</a> في مجموعة "scimag". نستخدم أيضًا مجموعة البيانات <a %(a_dois)s>dois-2022-02-12.7z</a>. يرجى ملاحظة أن تورنتات "smarch" <a %(a_smarch)s>مهملة</a> وبالتالي غير مدرجة في قائمة التورنتات لدينا. تورنتات على Libgen.li تورنتات على Libgen.rs البيانات الوصفية والتورنتات تحديثات على Reddit مقابلة صوتية صفحة ويكيبيديا Sci-Hub Sci-Hub: مجمدة منذ 2021؛ معظمها متاح عبر التورنتات Libgen.li: إضافات طفيفة منذ ذلك الحين</div> بعض المكتبات المصدرية تروّج لمشاركة بياناتها بشكل كبير عبر التورنت، بينما لا تشارك مكتبات أخرى مجموعتها بسهولة. في الحالة الأخيرة، تحاول رَبيدةُ آنّا جمع مجموعاتهم وجعلها متاحة (انظر صفحة <a %(a_torrents)s>التورنت</a> الخاصة بنا). هناك أيضًا حالات وسطية، على سبيل المثال، حيث تكون المكتبات المصدرية مستعدة للمشاركة، ولكنها لا تملك الموارد للقيام بذلك. في هذه الحالات، نحاول أيضًا المساعدة. فيما يلي نظرة عامة على كيفية تفاعلنا مع المكتبات المصدرية المختلفة. المكتبات المصدرية %(icon)s قواعد بيانات ملفات متنوعة منتشرة عبر الإنترنت الصيني؛ غالبًا ما تكون قواعد بيانات مدفوعة. %(icon)s معظم الملفات متاحة فقط باستخدام حسابات BaiduYun المميزة؛ سرعات التنزيل بطيئة. %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(duxiu)s>ملفات DuXiu</a> %(icon)s قواعد بيانات وصفية متنوعة منتشرة في الإنترنت الصيني؛ رغم أنها غالبًا قواعد بيانات مدفوعة %(icon)s لا تتوفر تفريغات للبيانات الوصفية بسهولة لمجموعتهم الكاملة. %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(duxiu)s>بيانات DuXiu الوصفية</a> الملفات %(icon)s الملفات متاحة فقط للاستعارة على أساس محدود، مع قيود وصول متنوعة %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(ia)s>ملفات IA</a> %(icon)s بعض البيانات الوصفية متاحة من خلال <a %(openlib)s>تفريغات قاعدة بيانات Open Library</a>، لكنها لا تغطي مجموعة IA بالكامل %(icon)s لا توجد تفريغات بيانات وصفية متاحة بسهولة لمجموعتهم الكاملة %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(ia)s>البيانات الوصفية لـ IA</a> آخر تحديث %(icon)s رَبيدةُ آنّا وLibgen.li يديران بشكل تعاوني مجموعات من <a %(comics)s>الكتب المصورة</a>، <a %(magazines)s>المجلات</a>، <a %(standarts)s>الوثائق القياسية</a>، و<a %(fiction)s>الأدب (المفصول عن Libgen.rs)</a>. %(icon)s السيول للكتب غير الخيالية تُشارك مع Libgen.rs (ومرآة <a %(libgenli)s>هنا</a>). %(icon)s <a %(dbdumps)s>تفريغات قاعدة البيانات HTTP</a> ربع السنوية %(icon)s السيول الآلية لـ <a %(nonfiction)s>الكتب غير الخيالية</a> و<a %(fiction)s>الخيالية</a> %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(covers)s>سيول أغلفة الكتب</a> %(icon)s <a %(dbdumps)s>تفريغات قاعدة البيانات HTTP</a> اليومية البيانات الوصفية %(icon)s تفريغات قاعدة البيانات الشهرية <a %(dbdumps)s>database dumps</a> %(icon)s السيول البيانات متاحة <a %(scihub1)s>هنا</a>، <a %(scihub2)s>هنا</a>، و<a %(libgenli)s>هنا</a> %(icon)s بعض الملفات الجديدة <a %(libgenrs)s>تتم</a> <a %(libgenli)s>إضافتها</a> إلى "scimag" في Libgen، ولكن ليس بما يكفي لتبرير سيول جديدة %(icon)s Sci-Hub قد جمدت الملفات الجديدة منذ عام 2021. %(icon)s تفريغات البيانات الوصفية متاحة <a %(scihub1)s>هنا</a> و<a %(scihub2)s>هنا</a>، وكذلك كجزء من <a %(libgenli)s>قاعدة بيانات Libgen.li</a> (التي نستخدمها) المصدر %(icon)s مصادر أصغر أو فردية متنوعة. نشجع الناس على التحميل إلى مكتبات الظل الأخرى أولاً، ولكن في بعض الأحيان يكون لدى الناس مجموعات كبيرة جدًا بحيث لا يمكن للآخرين فرزها، ولكنها ليست كبيرة بما يكفي لتستحق فئة خاصة بها. %(icon)s غير متاحة مباشرة بكميات كبيرة، محمية ضد الكشط. %(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(worldcat)s>بيانات OCLC (WorldCat) الوصفية</a> %(icon)s رَبيدةُ آنّا ومكتبة الزّاي يديران معًا مجموعة من <a %(metadata)s>البيانات الوصفية لمكتبة الزّاي</a> و<a %(files)s>ملفات مكتبة الزّاي</a> Datasets نحن نجمع كل المصادر المذكورة أعلاه في قاعدة بيانات موحدة نستخدمها لخدمة هذا الموقع. هذه القاعدة الموحدة ليست متاحة مباشرة، ولكن بما أن رَبيدةُ آنّا مفتوحة المصدر بالكامل، يمكن بسهولة <a %(a_generated)s>توليدها</a> أو <a %(a_downloaded)s>تحميلها</a> كقواعد بيانات ElasticSearch وMariaDB. ستقوم السكربتات في تلك الصفحة بتحميل جميع البيانات الوصفية المطلوبة تلقائيًا من المصادر المذكورة أعلاه. إذا كنت ترغب في استكشاف بياناتنا قبل تشغيل تلك السكربتات محليًا، يمكنك النظر في ملفات JSON الخاصة بنا، والتي ترتبط بملفات JSON أخرى. <a %(a_json)s>هذا الملف</a> هو نقطة انطلاق جيدة. قاعدة بيانات موحدة تورنتات من رَبيدةُ آنّا تصفح بحث مصادر أصغر أو فردية متنوعة. نشجع الناس على التحميل إلى مكتبات الظل الأخرى أولاً، ولكن في بعض الأحيان يكون لدى الناس مجموعات كبيرة جدًا بحيث لا يمكن للآخرين فرزها، ولكنها ليست كبيرة بما يكفي لتستحق فئة خاصة بها. نظرة عامة من <a %(a1)s>صفحة Datasets</a>. من <a %(a_href)s>aaaaarg.fail</a>. يبدو أنه مكتمل إلى حد كبير. من متطوعنا "cgiym". من <a %(a_href)s><q>ACM Digital Library 2020</q></a> تورنت. يحتوي على تداخل كبير مع مجموعات الأوراق الموجودة، ولكن هناك عدد قليل جدًا من التطابقات مع MD5، لذلك قررنا الاحتفاظ به بالكامل. استخلاص من <q>iRead eBooks</q> (= صوتيًا <q>ai rit i-books</q>; airitibooks.com)، بواسطة المتطوع <q>j</q>. يتوافق مع metadata <q>airitibooks</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>. من مجموعة <a %(a1)s><q>مكتبة الإسكندرية</q></a>. جزئيًا من المصدر الأصلي، جزئيًا من the-eye.eu، جزئيًا من مرايا أخرى. من موقع تورنت خاص بالكتب، <a %(a_href)s>Bibliotik</a> (غالبًا ما يُشار إليه بـ "Bib")، حيث تم تجميع الكتب في تورنتات بأسماء (A.torrent, B.torrent) وتوزيعها عبر the-eye.eu. من متطوعنا "bpb9v". لمزيد من المعلومات حول <a %(a_href)s>CADAL</a>، انظر الملاحظات في <a %(a_duxiu)s>صفحة مجموعة بيانات DuXiu</a>. المزيد من متطوعنا "bpb9v"، معظمها ملفات DuXiu، بالإضافة إلى مجلد "WenQu" و"SuperStar_Journals" (SuperStar هي الشركة وراء DuXiu). من متطوعنا "cgiym"، نصوص صينية من مصادر متنوعة (ممثلة كأدلة فرعية)، بما في ذلك من <a %(a_href)s>China Machine Press</a> (ناشر صيني رئيسي). مجموعات غير صينية (ممثلة كأدلة فرعية) من متطوعنا "cgiym". استخلاص لكتب عن العمارة الصينية، بواسطة المتطوع <q>cm</q>: <q>حصلت عليها من خلال استغلال ثغرة في شبكة دار النشر، لكن تم إغلاق تلك الثغرة منذ ذلك الحين</q>. يتوافق مع metadata <q>chinese_architecture</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>. كتب من دار النشر الأكاديمية <a %(a_href)s>De Gruyter</a>، جمعت من بعض التورنتات الكبيرة. استخلاص من <a %(a_href)s>docer.pl</a>، موقع مشاركة ملفات بولندي يركز على الكتب والأعمال المكتوبة الأخرى. تم استخلاصه في أواخر 2023 بواسطة المتطوع "p". ليس لدينا بيانات وصفية جيدة من الموقع الأصلي (ولا حتى امتدادات الملفات)، لكننا قمنا بتصفية الملفات التي تشبه الكتب وغالبًا ما تمكنا من استخراج البيانات الوصفية من الملفات نفسها. ملفات DuXiu بصيغة epub، مباشرة من DuXiu، جمعت بواسطة المتطوع "w". تتوفر فقط الكتب الحديثة من DuXiu مباشرة من خلال الكتب الإلكترونية، لذا يجب أن تكون معظم هذه الكتب حديثة. الملفات المتبقية من DuXiu من المتطوع "m"، والتي لم تكن بتنسيق PDG الخاص بـ DuXiu (البيانات الرئيسية <a %(a_href)s>لمجموعة بيانات DuXiu</a>). جمعت من العديد من المصادر الأصلية، للأسف دون الحفاظ على تلك المصادر في مسار الملف. <span></span> <span></span> <span></span> استخلاص لكتب إيروتيكية، بواسطة المتطوع <q>do no harm</q>. يتوافق مع metadata <q>hentai</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>. <span></span> <span></span> مجموعة مستخلصة من ناشر مانغا ياباني بواسطة المتطوع "t". <a %(a_href)s>الأرشيف القضائي المختار لـ Longquan</a>، مقدمة من المتطوع "c". استخلاص من <a %(a_href)s>magzdb.org</a>، حليف لـ Library Genesis (مرتبط بصفحة libgen.rs الرئيسية) لكنه لم يرغب في توفير ملفاته مباشرة. حصل عليها المتطوع "p" في أواخر 2023. <span></span> تحميلات صغيرة متنوعة، صغيرة جدًا لتكون مجموعة فرعية خاصة بها، لكنها ممثلة كأدلة. كتب إلكترونية من AvaxHome، موقع روسي لمشاركة الملفات. أرشيف للصحف والمجلات. يتوافق مع metadata <q>newsarch_magz</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>. استخلاص من <a %(a1)s>مركز توثيق الفلسفة</a>. مجموعة المتطوع "o" الذي جمع الكتب البولندية مباشرة من مواقع الإصدار الأصلية ("المشهد"). مجموعات مشتركة من <a %(a_href)s>shuge.org</a> بواسطة المتطوعين "cgiym" و"woz9ts". <span></span> <a %(a_href)s>“المكتبة الإمبراطورية لترانتور”</a> (سميت على اسم المكتبة الخيالية)، مستخلصة في 2022 بواسطة المتطوع "t". <span></span> <span></span> <span></span> مجموعات فرعية فرعية (ممثلة كأدلة) من المتطوع "woz9ts": <a %(a_program_think)s>program-think</a>، <a %(a_haodoo)s>haodoo</a>، <a %(a_skqs)s>skqs</a> (بواسطة <a %(a_sikuquanshu)s>Dizhi(迪志)</a> في تايوان)، mebook (mebook.cc، 我的小书屋، غرفتي الصغيرة للكتب — woz9ts: "يركز هذا الموقع بشكل رئيسي على مشاركة ملفات الكتب الإلكترونية عالية الجودة، بعضها منسق بواسطة المالك نفسه. تم <a %(a_arrested)s>اعتقال</a> المالك في 2019 وقام شخص ما بجمع الملفات التي شاركها."). الملفات المتبقية من DuXiu من المتطوع "woz9ts"، والتي لم تكن في تنسيق PDG الخاص بـ DuXiu (لا تزال بحاجة إلى تحويل إلى PDF). مجموعة "التحميل" مقسمة إلى مجموعات فرعية أصغر، والتي يتم الإشارة إليها في AACIDs وأسماء التورنت. تم إزالة التكرارات من جميع المجموعات الفرعية أولاً مقابل المجموعة الرئيسية، على الرغم من أن ملفات JSON "upload_records" للبيانات الوصفية لا تزال تحتوي على الكثير من الإشارات إلى الملفات الأصلية. تم أيضًا إزالة الملفات غير الكتابية من معظم المجموعات الفرعية، وعادةً ما لا يتم الإشارة إليها في "upload_records" JSON. المجموعات الفرعية هي: ملاحظات مجموعة فرعية العديد من المجموعات الفرعية نفسها تتكون من مجموعات فرعية فرعية (مثلًا من مصادر أصلية مختلفة)، والتي يتم تمثيلها كدليل في حقول "filepath". التحميلات إلى رَبيدةُ آنّا منشور مدونتنا حول هذه البيانات <a %(a_worldcat)s>WorldCat</a> هي قاعدة بيانات مملوكة من قبل المنظمة غير الربحية <a %(a_oclc)s>OCLC</a>، والتي تجمع سجلات البيانات الوصفية من المكتبات في جميع أنحاء العالم. من المحتمل أن تكون أكبر مجموعة بيانات وصفية للمكتبات في العالم. في أكتوبر 2023، <a %(a_scrape)s>أصدرنا</a> عملية استخراج شاملة لقاعدة بيانات OCLC (WorldCat)، بتنسيق <a %(a_aac)s>حاويات رَبيدةُ آنّا</a>. أكتوبر 2023، الإصدار الأول: OCLC (WorldCat) تورنتات بواسطة رَبيدةُ آنّا مثال على سجل في رَبيدةُ آنّا (المجموعة الأصلية) مثال على سجل في رَبيدةُ آنّا (مجموعة "zlib3") تورنتات من رَبيدةُ آنّا (البيانات الوصفية + المحتوى) منشور مدونة حول الإصدار 1 منشور مدونة حول الإصدار 2 في أواخر عام 2022، تم اعتقال المؤسسين المزعومين لمكتبة الزّاي، وتمت مصادرة النطاقات من قبل السلطات الأمريكية. منذ ذلك الحين، كان الموقع يعود ببطء إلى الإنترنت. من غير المعروف من يديره حاليًا. تحديث اعتبارًا من فبراير 2023. تعود جذور مكتبة الزّاي إلى مجتمع <a %(a_href)s>Library Genesis</a>، وبدأت في الأصل ببياناتهم. منذ ذلك الحين، أصبحت أكثر احترافية بشكل كبير، ولديها واجهة أكثر حداثة. لذلك، يمكنهم الحصول على المزيد من التبرعات، سواءً المالية لتحسين موقعهم الإلكتروني، أو تبرعات بالكتب الجديدة. لقد جمعوا مجموعة كبيرة بالإضافة إلى Library Genesis. تتكون المجموعة من ثلاثة أجزاء. يتم الحفاظ على صفحات الوصف الأصلية للجزئين الأولين أدناه. تحتاج إلى الأجزاء الثلاثة للحصول على جميع البيانات (باستثناء التورنتات التي تم تجاوزها، والتي تم شطبها في صفحة التورنتات). %(title)s: الإصدار الأول. كان هذا هو الإصدار الأول لما كان يسمى آنذاك "عاكسة المكتبة القرصانية" ("pilimi"). %(title)s: الإصدار الثاني، هذه المرة مع جميع الملفات ملفوفة في ملفات .tar. %(title)s: إصدارات جديدة تدريجية، باستخدام <a %(a_href)s>تنسيق حاويات رَبيدةُ آنّا (AAC)</a>، الآن يتم إصدارها بالتعاون مع فريق مكتبة الزّاي. تم الحصول على العاكسة الأولية بشق الأنفس على مدار عامي 2021 و2022. في هذه المرحلة، هي قديمة بعض الشيء: تعكس حالة المجموعة في يونيو 2021. سنقوم بتحديث هذا في المستقبل. نحن الآن نركز على إصدار هذا الإصدار الأول. نظرًا لأن Library Genesis محفوظة بالفعل مع التورنتات العامة، وهي مدرجة في مكتبة الزّاي، فقد قمنا بعملية إزالة التكرار الأساسية ضد Library Genesis في يونيو 2022. استخدمنا لهذا الغرض تجزئة MD5. من المحتمل أن يكون هناك الكثير من المحتوى المكرر في المكتبة، مثل تنسيقات ملفات متعددة لنفس الكتاب. من الصعب اكتشاف ذلك بدقة، لذا لا نقوم بذلك. بعد إزالة التكرار، تبقى لدينا أكثر من 2 مليون ملف، بإجمالي يقل قليلاً عن 7 تيرابايت. تتكون المجموعة من جزأين: تفريغ MySQL “.sql.gz” للبيانات الوصفية، و72 ملف تورنت بحجم يتراوح بين 50-100 جيجابايت لكل منها. تحتوي البيانات الوصفية على البيانات كما أبلغ عنها موقع مكتبة الزّاي (العنوان، المؤلف، الوصف، نوع الملف)، بالإضافة إلى الحجم الفعلي للملف وmd5sum الذي لاحظناه، حيث أن هذه البيانات قد لا تتطابق أحيانًا. يبدو أن هناك نطاقات من الملفات التي تحتوي مكتبة الزّاي نفسها على بيانات وصفية غير صحيحة. قد نكون أيضًا قد قمنا بتنزيل ملفات بشكل غير صحيح في بعض الحالات المعزولة، والتي سنحاول اكتشافها وإصلاحها في المستقبل. تحتوي ملفات التورنت الكبيرة على بيانات الكتب الفعلية، مع معرف مكتبة الزّاي كاسم الملف. يمكن إعادة بناء امتدادات الملفات باستخدام تفريغ البيانات الوصفية. المجموعة هي مزيج من المحتوى غير الخيالي والخيالي (غير مفصول كما في Library Genesis). الجودة أيضًا متباينة بشكل كبير. هذا الإصدار الأول متاح الآن بالكامل. لاحظ أن ملفات التورنت متاحة فقط من خلال عاكسنا على Tor. الإصدار 1 (%(date)s) هذا ملف تورنت إضافي واحد. لا يحتوي على أي معلومات جديدة، ولكنه يحتوي على بعض البيانات التي قد تستغرق وقتًا طويلاً لحسابها. مما يجعله مريحًا للاحتفاظ به، حيث أن تنزيل هذا التورنت غالبًا ما يكون أسرع من حسابه من الصفر. على وجه الخصوص، يحتوي على فهارس SQLite لملفات tar، للاستخدام مع <a %(a_href)s>ratarmount</a>. ملحق الإصدار 2 (%(date)s) لقد حصلنا على جميع الكتب التي أضيفت إلى مكتبة الزّاي بين آخر عاكس لنا وأغسطس 2022. كما عدنا وقمنا بجمع بعض الكتب التي فاتتنا في المرة الأولى. بشكل عام، هذه المجموعة الجديدة تبلغ حوالي 24 تيرابايت. مرة أخرى، هذه المجموعة مكررة ضد Library Genesis، حيث أن هناك تورنتات متاحة بالفعل لتلك المجموعة. البيانات منظمة بشكل مشابه للإصدار الأول. هناك تفريغ MySQL “.sql.gz” للبيانات الوصفية، والذي يتضمن أيضًا جميع البيانات الوصفية من الإصدار الأول، وبالتالي يحل محله. كما أضفنا بعض الأعمدة الجديدة: ذكرنا هذا في المرة الأخيرة، ولكن للتوضيح: "اسم الملف" و "md5" هما الخصائص الفعلية للملف، في حين أن "اسم الملف المبلغ عنه" و "md5 المبلغ عنه" هما ما جمعناه من مكتبة الزّاي. أحيانًا لا يتطابق هذان الاثنان مع بعضهما البعض، لذا قمنا بتضمين كليهما. لهذا الإصدار، قمنا بتغيير الترتيب إلى “utf8mb4_unicode_ci”، والذي يجب أن يكون متوافقًا مع الإصدارات الأقدم من MySQL. ملفات البيانات مشابهة للمرة السابقة، على الرغم من أنها أكبر بكثير. لم نتمكن ببساطة من إنشاء العديد من ملفات التورنت الصغيرة. يحتوي “pilimi-zlib2-0-14679999-extra.torrent” على جميع الملفات التي فاتتنا في الإصدار الأخير، بينما تحتوي التورنتات الأخرى على نطاقات معرف جديدة.  <strong>تحديث %(date)s:</strong> لقد جعلنا معظم تورنتاتنا كبيرة جدًا، مما تسبب في صعوبة لعملاء التورنت. لقد أزلناها وأصدرنا تورنتات جديدة. <strong>تحديث %(date)s:</strong> لا تزال هناك ملفات كثيرة جدًا، لذا قمنا بلفها في ملفات tar وأصدرنا تورنتات جديدة مرة أخرى. %(key)s: ما إذا كان هذا الملف موجودًا بالفعل في Library Genesis، سواء في مجموعة غير الخيال أو الخيال (مطابق بواسطة md5). %(key)s: أي تورنت يحتوي على هذا الملف. %(key)s: تم تعيينه عندما لم نتمكن من تنزيل الكتاب. الإصدار 2 (%(date)s) إصدارات Zlib (صفحات الوصف الأصلية) نطاق Tor الموقع الرئيسي استخراج مكتبة الزّاي يبدو أن مجموعة "الصينية" في مكتبة الزّاي هي نفسها مجموعة DuXiu الخاصة بنا، ولكن مع MD5s مختلفة. نستثني هذه الملفات من التورنتات لتجنب التكرار، ولكن نظهرها في فهرس البحث لدينا. البيانات الوصفية إليك %(percentage)s تنزيل إضافي سريع؛ وذلك لذكر %(profile_link)s لك. يسري هذا طيلة فترة انضمامك. تبرع انضم محددة تخفيضات حتّى %(percentage)s%% يدعم Alipay بطاقات الائتمان/الخصم الدولية. انظر <a %(a_alipay)s>هذا الدليل</a> لمزيد من المعلومات. أرسل لنا بطاقات هدايا Amazon.com باستخدام بطاقتك الائتمانية/الخصم. يمكنك شراء العملات المشفرة باستخدام بطاقات الائتمان/الخصم. يدعم WeChat (Weixin Pay) بطاقات الائتمان/الخصم الدولية. في تطبيق WeChat، انتقل إلى "Me => Services => Wallet => Add a Card". إذا لم ترَ ذلك، فعّله باستخدام "Me => Settings => General => Tools => Weixin Pay => Enable". (استخدم عند إرسال Ethereum من Coinbase) منسوخ! نسخ (أقل مبلغ ممكن) (تحذير: الحد الأدنى مرتفع) -%(percentage)s%% 12 شهرًا شهر واحد 24 شهرًا 3 أشهر 48 شهرًا 6 أشهر 96 شهرًا اختر المدة التي ترغب بالاشتراك فيها. <div %(div_monthly_cost)s></div><div %(div_after)s>بعد <span %(span_discount)s></span> خصومات</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% لاثنا عشرَ شهرًا لشهر واحد لأربعة وعشرين شهرًا لثلاثة أشهر لمدة 48 شهرًا لستة أشهر لمدة 96 شهرًا %(monthly_cost)s / شهر اتصل بنا خوادم مباشرة <strong>لبروتكول النقل الآمن للملفات (SFTP)</strong> تبرعٌ كتبرعِ المؤسسات، أو مقابل مجموعات جديدة (كملفات جديدة ممسوحة ضوئيًا، أو مجمع بيانات معرّفٌ بصريًا على المحارف (OCR)). لأصحاب الخبرة تنزيل سريعٌ و<strong>غير محدود</strong> <div %(div_question)s>هل يمكنني ترقية عضويتي أو الحصول على عضويات متعددة؟</div> <div %(div_question)s>هل يمكنني التبرع دون أن أصبح عضوًا؟</div> بالتأكيد. نحن نقبل التبرعات بأي مبلغ على هذا العنوان Monero (XMR): %(address)s. <div %(div_question)s>ماذا تعني النطاقات لكل شهر؟</div> يمكنك الوصول إلى الجانب الأدنى من النطاق بتطبيق جميع الخصومات، مثل اختيار فترة أطول من شهر. <div %(div_question)s> هل تتجدّد الإشتراكات تلقائيًا؟ </div> <strong>لا</strong> تتجدد الإشتراكات تلقائيًا. تستطيع الإنضمام بالمدة التي تُريد قصيرة كانت أم طويلة. <div %(div_question)s>على ماذا تنفق التبرعات؟</div> 100%% لحفظ معرفة وثقافة العالم وجعلها في متناول الجميع. فنصرف أغلبها على الخوادم والتخزين وأمور الموقع التقنية الأخرى. ولن يحصل أيُّ فرد من الفريق على قرش واحد من هذه التبرعات. <div %(div_question)s>هل بإمكاني التبرع بمبلغ كبير؟</div> سيُذهلنا هذا! فتواصل معنا بـ%(email)s إن أردت التبرع بأكثر من ثلاثة آلاف دولار. <div %(div_question)s> أتوجد طرق أخرى للدفع؟ </div> حاليا لا. أغلبهم يُحاربون ربائد كهذه، لهذا نأخذ حذرنا متى استطعنا. إن كانت عندك طرق أخرى أكثر أمانًا وأسهل استخدامًا فراسلنا %(email)s. سؤال وجواب عن التبرع عندك <a %(a_donation)s>تبرعًا</a> قيد التنفيذ. أكمل أو ألغي ذاك التبرع ثم قدّم جديدًا. <a %(a_all_donations)s>اعرض تبرعاتي كلّها</a> للتبرّعات التي تزيد عن 5000 دولارًا، راسلنا رجاءً على t %(email)s. نقبل التبرّعات الضخمة من الأثرياء والمؤسسات.  يرجى ملاحظة أنه على الرغم من أن العضويات في هذه الصفحة هي "شهريًا"، إلا أنها تبرعات لمرة واحدة (غير متكررة). انظر <a %(faq)s>الأسئلة المتكررة عن التبرع</a>. ربيدة آنّا مشروع غير ربحي مفتوح المصدر والبيانات. وتبرعك يُعيننا وبه تتطور أعمالنا فتصير واحدًا منّا. فشكرًا لأصحابنا الذين ساندونا! ❤️ لمزيد من المعلومات، تحقق من <a %(a_donate)s>الأسئلة المتكررة عن التبرع</a>. للانظمام <a %(a_login)s> ادخل أو سجّل</a>. وشكرًا لدعمك لنا! $%(cost)s / الشهر لا يُمكننا ردّ الأموال إن أخطأت بشيء في الدفع، ولكننا سنُصحح ما يُمكن ونسعى لإرضاءك. ابحث عن «Crypto» في تطبيق أو موقع بَيْ بَالْ. غالبًا تكون في «Finances». ابحث عن «Crypto» في تطبيق أو موقع بَيْ بَالْ. اضغط على «Transfer» %(transfer_icon)s، ثم «Send». علي للدفع (Alipay) علي للدفع (Alipay) / وِتشاتْ (WeChat) بطاقة آمازون مسبقة الدفع %(amazon)s بطاقة هدية بطاقة بنكية بطاقة بنكية (باستخدام التطبيق) بينانس بطاقة ائتمان/خصم/Apple/Google (BMC) تطبيق الدفع (Cash App) بطاقة ائتمانية/السحب المباشر بطاقة ائتمانية/السحب المباشر 2 بطاقة ائتمان/خصم (احتياطية) العملات المعماة %(bitcoin_icon)s بطاقة / بَيْ بَالْ / Venmo بَيْ بَالْ الولايات المتحدة (PayPal) %(bitcoin_icon)s بَيْ بَالْ (PayPal) بَيْ بَالْ (عادي) بيكس البرازيل (Pix) Revolut (غير متاح مؤقتا) وِتشاتْ (WeChat) اختر العملة المعماة التي تُريد: تبرع ببطاقة آمازون مسبقة الدفع. <strong>هام:</strong> هذا الخيار مخصص لـ %(amazon)s. إذا كنت ترغب في استخدام موقع أمازون آخر، اختره أعلاه. <strong>هام</strong>: ندعم موقع Amazon.com فقط، وليس غيره من المواقع المتفرعة منه. فمثلا لا نقبل نطاقات .de وco.uk وca وغيرهم. من فضلك لا تكتب رسالتك الخاصة. أدخل المبلغ الدقيق: %(amount)s لتنتبه إلى مبالغنا المقبولة من موزعينا، فإننا نُقاربها لها، وحدّها الإدنى %(minimum)s. تبرع باستخدام بطاقة ائتمان/خصم، من خلال تطبيق Alipay (سهل جدًا في الإعداد). قم بتثبيت تطبيق Alipay من <a %(a_app_store)s>متجر تطبيقات آبل</a> أو <a %(a_play_store)s>متجر جوجل بلاي</a>. سجل باستخدام رقم هاتفك. لا توجد حاجة لمزيد من التفاصيل الشخصية. <span %(style)s>1</span>قم بتثبيت تطبيق Alipay مدعوم: Visa، MasterCard، JCB، Diners Club وDiscover. انظر <a %(a_alipay)s>هذا الدليل</a> لمزيد من المعلومات. <span %(style)s>2</span>أضف بطاقة بنكية مع Binance، يمكنك شراء البيتكوين باستخدام بطاقة الائتمان/الخصم أو الحساب البنكي، ثم التبرع بتلك البيتكوين لنا. بهذه الطريقة يمكننا البقاء آمنين ومجهولين عند قبول تبرعك. باينانس متاح في كل بلد تقريبًا، ويدعم معظم البنوك وبطاقات الائتمان/الخصم. هذه هي توصيتنا الرئيسية حاليًا. نحن نقدر أنك تأخذ الوقت لتعلم كيفية التبرع باستخدام هذه الطريقة، لأنها تساعدنا كثيرًا. بالنسبة لبطاقات الائتمان، بطاقات الخصم، Apple Pay، و Google Pay، نستخدم "Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). في نظامهم، تعادل "القهوة" الواحدة 5 دولارات، لذا سيتم تقريب تبرعك إلى أقرب مضاعف لـ 5. تبرع بتطبيق الدفع (Cash App). إن كان عندك تطبيق الدفع (Cash App) فهو أسهل طرق التبرع! ولتنتبه للمعاملات التي تقل عن %(amount)s، فقد يفرض تطبيق الدفع (Cash App) عمولة بـ%(fee)s. وما فات %(amount)s فدون عمولة! تبرع ببطاقة الائتمان أو السحب المباشر. تستخدم هذه الطريقة مزود عملات مشفرة كوسيط للتحويل. قد يكون هذا مربكًا بعض الشيء، لذا يرجى استخدام هذه الطريقة فقط إذا لم تعمل طرق الدفع الأخرى. كما أنها لا تعمل في جميع البلدان. لا يمكننا دعم بطاقات الائتمان/الخصم مباشرةً، لأن البنوك لا تريد العمل معنا. ☹ ومع ذلك، هناك عدة طرق لاستخدام بطاقات الائتمان/الخصم على أي حال، باستخدام طرق دفع أخرى: والتبرع بالعملات المعماة يكون بـ BTC أو ETH أو XMR أو SOL. لا تختر هذا الخيار إلا إن كنت تعلمها ومعتاد بالدفع بها. والتبرع بالعملات المعماة يكون بـ BTC أو ETH أو XMR أو SOL وغيرها الكثير. خدمات التشفير السريعة إذا كنت تستخدم العملات المشفرة لأول مرة، نقترح استخدام %(options)s لشراء والتبرع بعملة البيتكوين (أول وأشهر عملة مشفرة). ولتنتبه فإن التبرعات الصغيرة ببطاقة الائتمان قد تُلغي تخفيضنا هذا %(discount)s%% ؛ لذا ننصح باشتراكٍ طويل المدة. تبرع باستخدام بطاقة الائتمان/الخصم، بَيْ بَالْ، أو Venmo. يمكنك الاختيار بين هذه في الصفحة التالية. قد يعمل الدفع بقوقل (Google Pay) والدفع بآبل (Apple Pay) أيضًا. لتنتبه أن التبرعات الصغيرة عمولتها أكبر، فننصح باشتراك طويل الفترة. سنستخدم بَيْ بَالْ المعماة إن اخترت بَيْ بَالْ؛ ذلك لأنّنا نُجهلُ بها فلا نُعرف. فنشكر لك سعيك في تعلم التبرع بهذه الطريقة لنحفظ سرنا. تبرع بـ بَيْ بَالْ (PayPal). تبرع باستخدام حساب بَيْ بَالْ العادي الخاص بك. تبرع باستخدام Revolut. إذا كان لديك Revolut، فهذا هو أسهل طريقة للتبرع! هذا الخيار لا يسمح بأكثر من %(amount)s. فاختر خيار دفع آخر أو مدة غيرها رجاءً. هذا الخيار لا يسمح بأقل من %(amount)s. فاختر خيار دفع آخر أو مدة غيرها رجاءً. بينانس Coinbase Kraken من فضلك اختر خيارًا للدفع. «توقيع ملف تورنت»: اسمك المستخدم أو رسالةٌ تكتبها على ملف تورنت <div %(div_months)s>مرّة كلّ 12 شهرًا من انضمامك</div> ذكر اسمك المُستخدم أو بدونه -تستُّرًا عليك- في قائمة الشكر والتقدير وصول مبكر للمزايا الجديدة دخول حصّري لمجموعة تلگرام بها تحديثاتٌ سريّة %(number)s تنزيل سريع في اليوم إذا تبرعت هذا الشهر! <a %(a_api)s>الوصول إلى JSON API</a> منزلة عظيمة وفضل في حفظ علوم و آداب البشرية تشمل المزايا السابقة، إضافة إلى: اكسب <strong>%(percentage)s%% تنزيلات إضافية</strong> عن طريق <a %(a_refer)s>إحالة الأصدقاء</a>. مقالات <strong>كثيرة غير محصورة</strong> من مجمع البيانات العلمية (SciDB) ودون تحقيق عند طرح أسئلة حول الحساب أو التبرعات، أضف معرف حسابك، لقطات الشاشة، الإيصالات، وأكبر قدر ممكن من المعلومات. نحن نتحقق من بريدنا الإلكتروني كل 1-2 أسبوع، لذا عدم تضمين هذه المعلومات سيؤخر أي حل. للحصول على المزيد من التنزيلات، <a %(a_refer)s>أحِل أصدقائك</a>! لقلّتنا قد نتأخر في الرد عليكم لأسبوع أو اثنين. لاحظ أن اسم الحساب أو الصورة قد تبدو غريبة. لا تقلق! هذه الحسابات تٌدار من شركاء التبرع. حساباتنا لم تُخترق. تبرع <span %(span_cost)s></span> <span %(span_label)s></span> لاثنا عشرَ شهرًا «%(tier_name)s» لشهر واحد «%(tier_name)s» لأربعة وعشرين شهرًا «%(tier_name)s» لثلاثة أشهر «%(tier_name)s» لمدة 48 شهرًا “%(tier_name)s” لستة أشهر «%(tier_name)s» لمدة 96 شهرًا “%(tier_name)s” بمقدورك إلغاء التبرع عند آخر خطوة في الدفع. اضغط على زر التبرع لتأكيد التبرع. <strong>ملاحظة مهمة</strong> أسعار العملات المعماة غير ثابتة فتصل أحيانًا إلى 20%% في دقائق. ومع هذا فهي أقل الخيارات عمولةً، فغيرها من الخيارات تصل عمولتها حتى الـ 50-60%% من قيمة التبرع وذلك لأننا «مؤسسة ظل خيرية» على حسب قولهم . <u>إن أرسلت لنا إيصال الاستلام بالمبلغ الذي دفعته، سنضيف لحسابك نوع الانضمام الذي اخترته</u> (على أن تراسلنا بعد إصدار الإيصال بساعات قليلة). فشكرًا لك على تحمّل عناء دعمنا! ❤️ ❌ حدث خطأ ما. أعد تحميل الصفحة رجاءً وحاول مرة ثانية. <span %(span_circle)s> 1 </span> اشترِ بِتكوين من بَيْ بَالْ <span %(span_circle)s> 2 </span> حوّل البِتكوين لعنواننا ✅ يُعاد توجيهك لصفحة التبرع… تبرع يرجى الانتظار على الأقل <span %(span_hours)s>24 ساعة</span> (وتحديث هذه الصفحة) قبل الاتصال بنا. إذا كنت ترغب في تقديم تبرع (أي مبلغ) دون عضوية، لا تتردد في استخدام عنوان Monero (XMR) هذا: %(address)s. سيتحقق نظامنا تلقائيًا من البطاقة التي أرسلتها. وإن فشلت العملية، فحاول إعادة إرسال البطاقة (<a %(a_instr)s>الإرشادات</a>). إذا لم ينجح ذلك، فراسلنا على بريدنا الرقمي وستُراجع آنا الطلب والبطاقة بنفسها (قد يستغرق هذا بضعة أيام)، واذكر في رسالتك إن حاولت إعادة الإرسال. مثال: يرجى ملئ <a %(a_form)s>استمارة Amazon.com الرسمية</a> لإرسال بطاقة دفع مسبق بـ %(amount)s إلى عنوان البريد الرقمي أدناه. اكتب في «To» هذا البريد: بطاقة آمازون مسبقة الدفع لا نقبل ببطاقات الدفع المسبق <strong>إلا من الاستمارة الرسمية من Amazon.com</strong>. وإن لم تُرسل بهذه الاستمارة فلا يُمكننا ردُّها. استخدم مرة واحدة فقط. خاصٌّ بحسابك فلا تُشاركه. في انتظار البطاقة... (حدّث الصفحة للتحقق) افتح <a %(a_href)s>صفحة التبرع برمز QR</a>. امسح رمز QR باستخدام تطبيق Alipay، أو اضغط على الزر لفتح تطبيق Alipay. يرجى التحلي بالصبر؛ قد تستغرق الصفحة بعض الوقت للتحميل لأنها في الصين. <span %(style)s>3</span>قم بالتبرع (امسح رمز QR أو اضغط على الزر) شراء عملة PYUSD من بَيْ بَالْ شراء بيتكوين (BTC) على تطبيق Cash اشترِ قليلاً أكثر (نوصي بـ %(more)s أكثر) من المبلغ الذي تتبرع به (%(amount)s)، لتغطية رسوم المعاملات. ستحتفظ بأي مبلغ متبقي. اذهب إلى صفحة "بيتكوين" (BTC) في تطبيق Cash. حوّل البيتكوين إلى عنواننا للتبرعات الصغيرة (أقل من ٢٥ دولارًا)، قد تحتاج إلى استخدام Rush أو Priority. اضغط على زر "إرسال البيتكوين" لإجراء "سحب". قم بالتبديل من الدولار إلى BTC بالضغط على أيقونة %(icon)s. أدخل مبلغ BTC أدناه واضغط على "إرسال". شاهد <a %(help_video)s>هذا الفيديو</a> إذا واجهت مشكلة. الخدمات السريعة مريحة، لكنها تفرض رسومًا أعلى. يمكنك استخدام هذا بدلاً من تبادل العملات المشفرة إذا كنت ترغب في تقديم تبرع أكبر بسرعة ولا تمانع في دفع رسوم تتراوح بين 5-10 دولارات. تأكد من إرسال المبلغ الدقيق للعملات المشفرة المعروض في صفحة التبرع، وليس المبلغ بالدولار الأمريكي. وإلا سيتم خصم الرسوم ولن نتمكن من معالجة عضويتك تلقائيًا. أحيانًا قد يستغرق التأكيد ما يصل إلى 24 ساعة، لذا تأكد من تحديث هذه الصفحة (حتى لو انتهت صلاحيتها). إرشادات البطاقة الائتمانية/السحب المباشر التبرع ببطاقة ائتمانية/السحب المباشر تشير بعض الخطوات إلى محافظ العملات المعماة، فلا تقلق، لا يتطلب منك معرفة مسبقة بها. إرشادات %(coin_name)s ﺔﻋﺮﺴﺑ ﻊﻓﺪﻟﺍ ﻞﻴﺻﺎﻔﺗ ءﻞﻤﻟ ﺮﻴﻔﺸﺘﻟﺍ ﺔﻈﻔﺤﻣ ﻖﻴﺒﻄﺗ ﻡﺍﺪﺨﺘﺳﺎﺑ ﺍﺬﻫ ﺔﻌﻳﺮﺴﻟﺍ ﺔﺑﺎﺠﺘﺳﻻ﻿ﺍ ﺰﻣﺭ  ﻊﻓﺪﻠﻟ ﺔﻌﻳﺮﺴﻟﺍ ﺔﺑﺎﺠﺘﺳﻻ﻿ﺍ ﺰﻣﺭ ﺢﺴﻣ ندعم إصدارًا واحدًا فقط (standard) من العملات المعماة. قد يستغرق تأكيد الحوالة ساعة من الزمن، وهذا على حسب العملة. تبرع بـ %(amount)s على هذه <a %(a_page)s>الصفحة</a>. انتهت صلاحية هذا التبرع. رجاءً إلغية وانشئ جديدًا. إذا كنت قد دفعت بالفعل: أجل، أرسلتُ الإيصال إذا تذبذب سعر صرف العملة المعماة أثناء المعاملة ، فتأكد من تضمين الإيصال الذي يوضح سعر الصرف الحقيقي. نشكر لك تحمّل عناء دعمنا بالعملات المعماة ، فهي تساعدنا كثيرًا! ❌ حدث خطأ ما. أعد تحميل الصفحة وحاول مرة أخرى. <span %(span_circle)s>%(circle_number)s </span> ارسل لنا الإيصال بالبريد الرقمي إن صادفت مشكلة فراسلنا على %(email)s واكتب في رسالتك مفصّلًا مشكلتك وماحدث معك موضحًا إياها بصورة للشاشة -إن وجد-. ✅ شكرًا لتبرعك! ستُنشّط آنّا حسابك بانضمامٍ يُناسب تبرعك في أيامٍ قليلة. ارسل إيصالًا أو لقطة شاشة إلى عنوان التحققك الشخصي: اضغط على هذا الزر عند إرسالك الإيصال بالبريد الرقمي حتى تُراجعه آنّا بنفسها (قد تمتد المراجعة أيامًا): أرسل إيصالاً أو لقطة شاشة إلى عنوان التحقق الشخصي الخاص بك. لا تستخدم هذا العنوان البريدي لتبرعك عبر بَيْ بَالْ. إلغي نعم متأكد، إلغي من فضلك أمتأكد من إلغاءك؟ لا تلغي إن دفعت. ❌ حدث خطأ ما. أعد تحميل الصفحة رجاءً وحاول مرة ثانية. قدم تبرعًا جديدًا ✅ أُلغيَ تبرعك. التاريخ: %(date)s المعرّف: %(id)s دفع آخر الحالة: <span %(span_label)s>%(label)s </span> المجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s في الشهر لـ%(duration)s شهرًا، ومعها %(discounts)s تخفيض)</span> المجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s في الشهر لـ%(duration)s شهرًا)</span> 1. اكتب بريدك الرقمي. 2. اختر خيارًا يُناسبك للدفع. 3. اختر خيارًا يُناسبك للدفع مرة أخرى. 4. حدد محفظةً باستضافة داخلية. 5. انقر على «أؤكد الملكية». 6. من المفترض أن إيصالًا قد وصلك على بريدك الرقمي. ارسله لنا، وبه سنُؤكد تبرعك في أقرب وقت ممكن. (لعلّك تلغي تبرعك وتبدأ واحدًا جديدًا) إرشادات الدفع قديمة الآن. إن كنت ترغب في تقديم تبرع آخر ، فاستخدم زر «دفع آخر» أعلاه. لقد دفعت. إن كنت ترغب في مراجعة إرشادات الدفع ، انقر هنا: اظهر إرشادات الدفع القديمة إذا تم حظر صفحة التبرع، جرب اتصال إنترنت مختلف (مثل VPN أو إنترنت الهاتف). للأسف، صفحة Alipay غالبًا ما تكون متاحة فقط من <strong>البر الرئيسي للصين</strong>. قد تحتاج إلى تعطيل VPN مؤقتًا، أو استخدام VPN إلى البر الرئيسي للصين (أو هونغ كونغ يعمل أحيانًا أيضًا). <span %(span_circle)s> 1 </span> تبرع من علي للدفع (Alipay) تبرع بالمبلغ الإجمالي %(total)s باستخدام <a %(a_account)s>حساب Alipay هذا</a> إرشادات علي للدفع (Alipay) <span %(span_circle)s> 1 </span> حوّل لواحد من حساباتنا للعملات المعماة تبرع بالمبلغ الإجمالي لـ%(total)s لأحد هذه العناوين: ارشادات العملات المعماة اتبع الارشادات لشراء بِتكوين (BTC). واشتر المبلغ الذي تُريد التبرع به ،%(total)s. اكتب عنواننا للبِتكوين (BTC)، واتبع الإرشادات لإرسال تبرعك هذا %(total)s: <span %(span_circle)s> 1 </span> تبرع من Pix تبرع إجمالي بـ%(total)s من <a %(a_account)s> حساب Pix هذا تعليمات Pix <span %(span_circle)s>1</span>تبرع على WeChat تبرع بالمبلغ الإجمالي %(total)s باستخدام <a %(a_account)s>هذا الحساب على WeChat</a> تعليمات WeChat استخدم أيًا من خدمات "بطاقة الائتمان إلى بيتكوين" السريعة التالية، والتي تستغرق بضع دقائق فقط: عنوان BTC / Bitcoin (محفظة خارجية): مبلغ BTC / Bitcoin: املأ التفاصيل التالية في النموذج: إذا كانت أي من هذه المعلومات غير محدثة، يرجى مراسلتنا عبر البريد الإلكتروني لإعلامنا. يرجى استخدام <span %(underline)s>هذا المبلغ المحدد</span>. قد يكون إجمالي التكلفة أعلى بسبب رسوم بطاقة الائتمان. للأسف، قد يكون هذا أكثر من خصمنا للمبالغ الصغيرة. (الحد الأدنى: %(minimum)s، لا حاجة للتحقق في المعاملة الأولى) (الحد الأدنى: %(minimum)s) (الحد الأدنى: %(minimum)s) (الحد الأدنى: %(minimum)s، لا حاجة للتحقق في المعاملة الأولى) (الحد الأدنى: %(minimum)s) (الحد الأدنى: %(minimum)s حسب البلد، لا حاجة للتحقق في المعاملة الأولى) اتبع الإرشادات لشراء عملة PYUSD (بَيْ بَالْ دولار أمريكي). اشترِ أكثر (نوصي بـ %(more)s ) من القيمة التي تتبرع بها (%(amount)s)، فيكون من ضمنها قيمة العمولة. والباقي يبقى عندك. انتقل إلى صفحة «PYUSD» في تطبيق أو موقع بَيْ بَالْ . اضغط على «Transfer» %(icon)s، ثم «Send». حدّث الحالة انشئ تبرعًا جديدًا لتصفير المؤقت. تأكد من استخدام مبلغ BTC أدناه، <em>وليس</em> اليورو أو الدولار، وإلا فلن نتلقى المبلغ الصحيح ولن نتمكن من تأكيد عضويتك تلقائيًا. شراء بيتكوين (BTC) على Revolut اشترِ قليلاً أكثر (نوصي بـ %(more)s أكثر) من المبلغ الذي تتبرع به (%(amount)s)، لتغطية رسوم المعاملات. ستحتفظ بأي مبلغ متبقي. اذهب إلى صفحة "Crypto" في Revolut لشراء بيتكوين (BTC). حوّل البيتكوين إلى عنواننا للتبرعات الصغيرة (أقل من ٢٥ دولارًا) قد تحتاج إلى استخدام Rush أو Priority. اضغط على زر "إرسال البيتكوين" لإجراء "سحب". قم بالتبديل من اليورو إلى BTC بالضغط على أيقونة %(icon)s. أدخل مبلغ BTC أدناه واضغط على "إرسال". شاهد <a %(help_video)s>هذا الفيديو</a> إذا واجهت مشكلة. الحالة: ١ ٢ إرشاد خطوة بخطوة راجع الدليل التفصيلي أدناه. وإلا قد يتم قفل حسابك! إذا لم تقم بذلك بالفعل، اكتب مفتاحك السري لتسجيل الدخول: شكرًا لك على تبرعك! الوقت المتبقي: تبرّع حوّل %(amount)s إلى %(account)s في انتظار التأكيد (حدّث الصفحة للتحقق)… في انتظار التحويل (حدّث الصفحة للتحقق)… سابقاً تُحتسب التنزيلات السريعة خلال آخر 24 ساعة ضمن الحد اليومي. تُميّز التنزيلات من الخوادم السريعة بـ%(icon)s. آخر 18 ساعة لم تُنزّل أي ملفات حتى الآن. لا تُعرض الملفات المُنزّلة علنًا. جميع الأوقات بالتوقيت العالمي الموحّد (UTC). الملفات المُنزّلة إن نزّلت ملفًا بتنزيل سريع وبطيئ، فسيظهر مرتين. لا تحمل همًّا، نزّل أُناسٌ كثر من المواقع الموصولة ولم يطرأ عليهم شيء. إلّا أننا نوصي باستخدام VPN (مدفوع)، أو <a %(a_tor)s>Tor</a> (مجاني). نزّلت كتابًا منكم، فهل سيُقبض علي لانتهاك حقٍّ ما؟ أنت آنّا! من هي آنّا؟ لدينا واجهة برمجة تطبيقات JSON للأعضاء صالحة ومستقرة، للحصول على رابط تنزيل سريع: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (التوثيق داخل JSON نفسه). لحالات الاستخدام الأخرى، مثل تكرار جميع ملفاتنا، وبناء بحث مخصص، وما إلى ذلك، نوصي <a %(a_generate)s>بإنشاء</a> أو <a %(a_download)s>تنزيل</a> قواعد بياناتنا ElasticSearch وMariaDB. واستكشف بياناتنا كما هي يدويًا <a %(a_explore)s>من خلال ملفات JSON</a>. ويمكنك تنزيل قائمتنا للتورنت كـ <a %(a_torrents)s>JSON</a> أيضًا. أعندكم واجهة برمجة التطبيقات (API)؟ نحن لا نستضيف أي مواد محمية بحقوق الطبع والنشر هنا. نحن محرك بحث، وبالتالي نقوم فقط بفهرسة البيانات الوصفية المتاحة بالفعل للجمهور. عند التنزيل من هذه المصادر الخارجية، نقترح عليك التحقق من القوانين في نطاق سلطتك القضائية فيما يتعلق بما هو مسموح به. نحن غير مسؤولين عن المحتوى المستضاف من قبل الآخرين. إذا كانت لديك شكاوى حول ما تراه هنا، فإن أفضل رهان لك هو الاتصال بالموقع الأصلي. نحن نقوم بانتظام بسحب تغييراتهم إلى قاعدة بياناتنا. إذا كنت تعتقد حقًا أن لديك شكوى DMCA صالحة يجب أن نرد عليها، يرجى ملء <a %(a_copyright)s>نموذج شكوى DMCA / حقوق الطبع والنشر</a>. نحن نأخذ شكاويك على محمل الجد، وسنعود إليك في أقرب وقت ممكن. كيف أبلغ عن انتهاك حقوق الطبع والنشر؟ إليك بعض الكتب التي تحمل أهمية خاصة لعالم المكتبات الظلية والحفاظ الرقمي: ما هي كتبك المفضلة؟ نود أيضًا أن نذكّر الجميع بأن جميع أكوادنا وبياناتنا مفتوحة المصدر بالكامل. هذا أمر فريد من نوعه لمشاريع مثل مشروعنا — نحن غير مدركين لأي مشروع آخر يحتوي على كتالوج ضخم مماثل ومفتوح المصدر بالكامل أيضًا. نرحب بشدة بأي شخص يعتقد أننا ندير مشروعنا بشكل سيء أن يأخذ أكوادنا وبياناتنا ويؤسس مكتبته الظل الخاصة! نحن لا نقول هذا بدافع الحقد أو شيء من هذا القبيل — نحن نعتقد بصدق أن هذا سيكون رائعًا لأنه سيرفع المعايير للجميع، ويحافظ بشكل أفضل على إرث البشرية. أكره الطريقة التي تديرون بها هذا المشروع! نُقدّر من ينقل موقعنا و<a %(a_mirrors)s>يُظهر</a> ما فيه، وسندعم كلّ من استطاع ماليًا. كيف أُساعدك؟ نعم نجمع. إلهامنا لجمع البيانات الوصفية هو هدف آرون شوارتز "صفحة ويب واحدة لكل كتاب تم نشره على الإطلاق"، والذي أنشأ من أجله <a %(a_openlib)s>Open Library</a>. لقد نجح هذا المشروع، لكن موقعنا الفريد يسمح لنا بالحصول على بيانات وصفية لا يمكنهم الحصول عليها. كان مصدر إلهام آخر هو رغبتنا في معرفة <a %(a_blog)s>عدد الكتب الموجودة في العالم</a>، حتى نتمكن من حساب عدد الكتب التي لا تزال بحاجة إلى الإنقاذ. أتجمعون البيانات الوصفية؟ يرجى ملاحظة أن mhut.org يحظر بعض نطاقات IP، لذا قد يكون من الضروري استخدام VPN. <strong>أندرويد:</strong> انقر على قائمة النقاط الثلاث أعلى اليمين، واختر «إضافة إلى الشاشة الرئيسة». <strong>iOS:</strong> انقر على زر «مشاركة» في الأسفل، واختر «إضافة إلى الشاشة الرئيسة». لا نملك تطبيقًا رسميًا لنا، ولكن يمكنك حفظ موقعنا كتطبيق. أعندكم تطبيق للهاتف النقال؟ يرجى إرسالها إلى <a %(a_archive)s>Internet Archive</a>. سيقومون بحفظها بشكل صحيح. كيف يمكنني التبرع بالكتب أو المواد المادية الأخرى؟ كيف أطلب كتبًا؟ <a %(a_blog)s>مدونة آنّا</a>، <a %(a_reddit_u)s>Reddit</a>، <a %(a_reddit_r)s>Subreddit</a> — تحديثات منتظمة <a %(a_software)s>برنامج آنّا</a> — كودنا مفتوح المصدر <a %(a_datasets)s>مجموعات البيانات</a> — حول البيانات <a %(a_li)s>.li</a>، <a %(a_se)s>.se</a>، <a %(a_org)s>.org</a> — نطاقات بديلة هل هناك المزيد من الموارد حول رَبيدةُ آنّا؟ <a %(a_translate)s>ترجمة على برنامج آنّا</a> — نظام الترجمة الخاص بنا <a %(a_wikipedia)s>ويكيبيديا</a> — المزيد عنا (يرجى المساعدة في تحديث هذه الصفحة، أو إنشاء واحدة للغتك الخاصة!) حدد ما تفضل من الإعدادات، ثم دع مربع البحث فارغًا، وانقر على «بحث»"، واحفظ الصفحة بمؤشّر متصفحك. كيف أحفظ إعدادات بحثي؟ نرحب بالباحثين الأمنيين للبحث عن الثغرات في أنظمتنا. نحن من المؤيدين الكبار للإفصاح المسؤول. اتصل بنا <a %(a_contact)s>هنا</a>. نحن غير قادرين حاليًا على منح مكافآت الأخطاء، باستثناء الثغرات التي لديها <a %(a_link)s>إمكانية تعريض هويتنا للخطر</a>، والتي نقدم لها مكافآت تتراوح بين 10 آلاف و50 ألف دولار. نود أن نقدم نطاقًا أوسع لمكافآت الأخطاء في المستقبل! يرجى ملاحظة أن هجمات الهندسة الاجتماعية خارج النطاق. إذا كنت مهتمًا بأمن المعلومات الهجومي، وترغب في المساعدة في أرشفة معرفة وثقافة العالم، تأكد من الاتصال بنا. هناك العديد من الطرق التي يمكنك من خلالها المساعدة. هل لديكم برنامج إفصاح مسؤول؟ نحن حرفياً لا نملك الموارد الكافية لتوفير تنزيلات عالية السرعة للجميع في العالم، بقدر ما نود ذلك. إذا كان هناك متبرع غني يرغب في تقديم هذا لنا، فسيكون ذلك رائعًا، ولكن حتى ذلك الحين، نحن نحاول جاهدين. نحن مشروع غير ربحي بالكاد يستطيع الاستمرار من خلال التبرعات. لهذا السبب قمنا بتنفيذ نظامين للتنزيل المجاني، مع شركائنا: خوادم مشتركة مع تنزيلات بطيئة، وخوادم أسرع قليلاً مع قائمة انتظار (لتقليل عدد الأشخاص الذين يقومون بالتنزيل في نفس الوقت). لدينا أيضًا <a %(a_verification)s>تحقق من المتصفح</a> لتنزيلاتنا البطيئة، لأن الروبوتات والبرامج الخبيثة ستسيء استخدامها، مما يجعل الأمور أبطأ للمستخدمين الشرعيين. لاحظ أنه عند استخدام متصفح Tor، قد تحتاج إلى ضبط إعدادات الأمان الخاصة بك. في أدنى الخيارات، المسماة "قياسي"، ينجح تحدي Cloudflare turnstile. في الخيارات الأعلى، المسماة "أكثر أمانًا" و"الأكثر أمانًا"، يفشل التحدي. في بعض الأحيان، قد تتوقف التنزيلات الكبيرة بسبب بطء الاتصال. نوصي باستخدام مدير تنزيل (مثل JDownloader) لاستئناف التنزيلات الكبيرة تلقائيًا. لماذا تكون التنزيلات البطيئة بطيئة جدًا؟ سؤال وجواب (س ج) استخدم <a %(a_list)s>مولد قائمة التورنت</a> لإنشاء قائمة بالتورنتات التي تحتاج للتوزع أكثر من غيرها، وضمن مساحة تخزينك. نعم، انظر صفحة <a %(a_llm)s>بيانات LLM</a>. تحتوي معظم التورنتات على الملفات مباشرة، مما يعني أنه يمكنك توجيه عملاء التورنت لتنزيل الملفات المطلوبة فقط. لتحديد الملفات التي يجب تنزيلها، يمكنك <a %(a_generate)s>توليد</a> بياناتنا الوصفية، أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا. للأسف، تحتوي بعض مجموعات التورنت على ملفات .zip أو .tar في الجذر، وفي هذه الحالة تحتاج إلى تنزيل التورنت بالكامل قبل أن تتمكن من اختيار الملفات الفردية. لا توجد أدوات سهلة الاستخدام لتصفية التورنت حتى الآن، لكننا نرحب بالمساهمات. (لدينا <a %(a_ideas)s>بعض الأفكار</a> للحالة الأخيرة على أي حال.) الإجابة الطويلة: الإجابة القصيرة: ليس بسهولة. نحاول الحفاظ على الحد الأدنى من التكرار أو التداخل بين التورنتات في هذه القائمة، ولكن لا يمكن دائمًا تحقيق ذلك، ويعتمد بشكل كبير على سياسات المكتبات المصدرية. بالنسبة للمكتبات التي تصدر تورنتاتها الخاصة، فإن الأمر خارج عن أيدينا. بالنسبة للتورنتات التي تصدرها رَبيدةُ آنّا، نقوم بإزالة التكرار فقط بناءً على تجزئة MD5، مما يعني أن الإصدارات المختلفة من نفس الكتاب لا يتم إزالة تكرارها. نعم. هذه في الواقع ملفات PDF و EPUB، لكنها لا تحتوي على امتداد في العديد من تورنتاتنا. هناك مكانان يمكنك فيهما العثور على البيانات الوصفية لملفات التورنت، بما في ذلك أنواع الملفات/الامتدادات: 1. كل مجموعة أو إصدار له بيانات وصفية خاصة به. على سبيل المثال، <a %(a_libgen_nonfic)s>تورنتات Libgen.rs</a> لديها قاعدة بيانات وصفية مقابلة مستضافة على موقع Libgen.rs. نحن عادةً نربط بموارد البيانات الوصفية ذات الصلة من صفحة <a %(a_datasets)s>مجموعة البيانات</a> لكل مجموعة. 2. نوصي <a %(a_generate)s>بتوليد</a> أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا. تحتوي هذه على خريطة لكل سجل في رَبيدةُ آنّا إلى ملفات التورنت المقابلة له (إذا كانت متاحة)، تحت "torrent_paths" في JSON الخاص بـ ElasticSearch. بعض عملاء التورنت لا يدعمون أحجام القطع الكبيرة، والتي تحتوي عليها الكثير من ملفات التورنت لدينا (بالنسبة للملفات الأحدث لم نعد نفعل ذلك - حتى وإن كان ذلك صالحًا وفقًا للمواصفات!). لذا جرب عميلًا مختلفًا إذا واجهت هذه المشكلة، أو اشتكِ إلى صانعي عميل التورنت الخاص بك. أود المساعدة في النشر والتوزيع، لكن ليس لدي مساحة كبيرة على القرص. التورنتات بطيئة جدًا؛ هل يمكنني تنزيل البيانات مباشرة منك؟ هل يمكنني تنزيل مجموعة فرعية فقط من الملفات، مثل لغة معينة أو موضوع معين؟ كيف تتعاملون مع التكرارات في التورنتات؟ هل يمكنني الحصول على قائمة التورنت بصيغة JSON؟ لا أرى ملفات PDF أو EPUB في التورنتات، فقط ملفات ثنائية؟ ماذا أفعل؟ لماذا لا يستطيع عميل التورنت الخاص بي فتح بعض ملفات التورنت / روابط المغناطيس الخاصة بكم؟ سؤال وجواب عن التورنت كيف أرفع كتبًا جديدة؟ يرجى الاطلاع على <a %(a_href)s>هذا المشروع الممتاز</a>. هل لديك مراقب وقت التشغيل؟ ماهي رَبيدةُ آنّا؟ انضم لنا لتقدر على التنزيل بسرعة. ندعم الآن بطاقات الهدايا من أمازون، وبطاقات الائتمان والخصم، والعملات المشفرة، وAlipay، وWeChat. أكملت تنزيلاتك السريعة لهذا اليوم. وصول التنزيلات كل ساعة في آخر 30 يومًا. متوسط الساعة: %(hourly)s. المتوسط اليومي: %(daily)s. نعمل مع شركائنا لنُسهّل إتاحة ما عندنا من مجموعات مجانًا لأي شخص. فلكل فرد الحق في العلم وما فيه من حكمة. و <a %(a_search)s> ليس على حساب المؤلفين </a>. مجمع البيانات المستخدمة في رَبيدة آنا مفتوحة كلّها، ويمكن عكس وإظهار ما فيها وتوزيعه بالتورنت. <a %(a_datasets)s>اعرف أكثر…</a> ربيدة طويلة الأمد قاعدة بيانات كاملة ابحث كتبٌ، وأوراق بحثية، ومجلات، وقصص مصورة، وسجلات المكتبة، وبيانات وصفية، … كل <a %(a_code)s>الرموز</a> و<a %(a_datasets)s>البيانات</a> الخاصة بنا مفتوحة المصدر بالكامل. <span %(span_anna)s> رَبيدةُ آنّا</ span> مشروع غير ربحي له هدفان: <li> <strong> الحفظ: </strong> حفظ جميع علوم و آداب البشرية. </li> <li> <strong> وصلة: </strong> إيصال هذه المعارف والعلوم لأي شخص في العالم. </li> عندنا أكبر مجموعة في العالم من النصوص عالية الجودة. <a %(a_llm)s>اعرف أكثر…</a> موارد تدريب النموذج اللغوي الكبير 🪩 المرايا: نداء للتطوع راسلنا إن كنت تُدير وسيلة دفع لا يُعرف بها الدافع. وإننا نبحث عن من يُريد نشر إعلانٍ صغير جميل على موقعنا. وكلّ العوائد تُصب للحفاظ على ما نحفظ هنا. الحفظ حفظنا تقريبا <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html"> 5 %% من الكتب في العالم </a>. نحفظ الكتب والرسائل والمجلات والقصص المصورة وغيرها الكثير، فنأخذها من <a href="https://en.wikipedia.org/wiki/Shadow_library"> مكتبات الظل</a> والمكتبات الرسمية وأماكن أخرى ونجمعها كلها في مكان واحد. وهي محفوظة للأبد ويسهل تكريرها جملةً -بالتورنت- فيصير للعمل الواحد نُسخ عدّة في كلّ العالم. وبعض مكتبات الظل توفر هذا (كمكتبة التَّكوين، ومجمع العلوم)، وتُوفر رَبيدة آنَّا التوزيع بالجملة للمكتبات التي لا تمنح هذا (كمكتبة الزّاي) ومنها المكتبات غير الظلية (كرَبيدة الشّابكة، والدّوشية). فهذا التوزيع الكبير الواسع مع توفّر ترميز موقعنا للعامة كلاهما يُشكلان حاجزًا منيعًا لمن يريد الإطاحة بنا، كما يضمنان حفظًا آجلًا لعلوم و آداب البشرية. وللاستزادة اقرأ عن <a href="/datasets">مجمع بياناتنا</a>. لا يلزم التحقق من متصفحك إن كنت <a %(a_member)s>مشتركًا</a>. 🧬&nbsp;مجمع العلوم متصل بمجمع البيانات العلمية. مجمع البيانات العلمية افتح معرّف الأغراض الرقمي <a %(a_paused)s>أجّل</a> مجمع العلوم رفع أوراق جديدة. وصول مباشر إلى %(count)s ورقة أكاديمية 🧬&nbsp;مجمع البيانات العلمية هو استمرار لـ Sci-Hub، بواجهته المألوفة وعرض مباشر لملفات PDF. أدخل DOI الخاص بك لعرضه. لدينا مجموعة Sci-Hub الكاملة، بالإضافة إلى أوراق جديدة. يمكن عرض معظمها مباشرة بواجهة مألوفة، مشابهة لـ Sci-Hub. يمكن تنزيل بعضها من مصادر خارجية، وفي هذه الحالة نعرض روابط لتلك المصادر. ساعد بتوزيع ومشاركة ملفات التورنت. <a %(a_torrents)s>اعرف أكثر…</a> >%(count)s موزع <%(count)s موزع %(count_min)s–%(count_max)s موزع 🤝 نبحث عن متطوعين كمشروع غير ربحي ومفتوح المصدر، نحن دائمًا نبحث عن أشخاص للمساعدة. تنزيلات IPFS رتّبها %(by)s، بوقت إنشاءها <span %(span_time)s>%(time)s </span> احفظ ❌ حدث خطأ ما. حاول مرة اخرى. ✅ حُفظ. رجاء أعد تحميل الصفحة. القائمة فارغة. عدّل أضف أو أزل من هذه القائمة بالبحث عن ملف وفتح تبويبة «القوائم». قائمة كيف يمكننا المساعدة إزالة التداخل (إزالة التكرار) استخراج النصوص والبيانات الوصفية التعرف الضوئي على الحروف (OCR) نحن قادرون على توفير وصول عالي السرعة إلى مجموعاتنا الكاملة، وكذلك إلى المجموعات غير المنشورة. هذا هو الوصول على مستوى المؤسسات الذي يمكننا توفيره مقابل تبرعات في حدود عشرات الآلاف من الدولارات الأمريكية. نحن أيضًا على استعداد لمبادلة هذا بمجموعات عالية الجودة التي لا نملكها بعد. يمكننا رد أموالك إذا كنت قادرًا على تزويدنا بإثراء بياناتنا، مثل: دعم الأرشفة طويلة الأمد للمعرفة البشرية، مع الحصول على بيانات أفضل لنموذجك! <a %(a_contact)s>اتصل بنا</a> لمناقشة كيفية العمل معًا. من المفهوم جيدًا أن LLMs تزدهر على البيانات عالية الجودة. لدينا أكبر مجموعة من الكتب والأوراق والمجلات، وما إلى ذلك في العالم، وهي من أعلى مصادر النصوص جودة. بيانات LLM نطاق وحجم فريدان تحتوي مجموعتنا على أكثر من مئة مليون ملف، بما في ذلك المجلات الأكاديمية والكتب الدراسية والمجلات. نحقق هذا الحجم من خلال دمج المستودعات الكبيرة الموجودة. بعض مجموعات مصادرنا متاحة بالفعل بكميات كبيرة (Sci-Hub، وأجزاء من Libgen). مصادر أخرى حررناها بأنفسنا. <a %(a_datasets)s>Datasets</a> تعرض نظرة عامة كاملة. تتضمن مجموعتنا ملايين الكتب والأوراق والمجلات من قبل عصر الكتب الإلكترونية. تم إجراء OCR لأجزاء كبيرة من هذه المجموعة، ولديها بالفعل تداخل داخلي قليل. استمر إن فقدت مفتاحك <a %(a_contact)s>فراسلنا</a> واكتب ما عندك من معلومات وأدلّة. قد يتحتم عليك إنشاء حساب جديد -غير دائم- للتواصل معنا. <a %(a_account)s>سجّل دخولك</a> لرؤية هذه الصفحة.</a> لمنع الآليين المزعجين من إنشاء حسابات كثيرة، علينا أولًا التحقق من متصفحك. إذا علقت في حلقة لا نهائية، نوصي بتثبيت <a %(a_privacypass)s>Privacy Pass</a>. جرّب إطفاء أدوات حظر الإعلانات وإضافات المتصفح الأخرى فلعلها تكون السبب في هذا. ادخل / سجّل رَبيدةُ آنّا متوقفة مؤقتًا للصيانة. يرجى العودة بعد ساعة. مؤلف بديل وصف بديل إصدار بديل امتداد بديل اسم ملف بديل ناشر بديل عنوان بديل تاريخ فتحها للعامة مجانًا اقرأ أكثر… الوصف ابحث عن رقم «SSNO» للمكتبة الرقمية الصينية (CADAL) في رَبيدة آنّا ابحث عن رقم «SSID» للدّوشية في رَبيدة آنّا ابحث عن رقم «DXID» للدّوشية في رَبيدة آنّا ابحث عن الردمك في رَبيدة آنّا ابحث عن رقم الفهرس العالمي في رَبيدة آنّا ابحث عن معرف المكتبة المفتوحة في رَبيدة آنّا عارض رَبيدةُ آنّا عبر الإنترنت %(count)s الصفحات المتأثرة بعد التحميل: قد تتوفر نُسخة أفضل من هذا الملف في %(link)s تنزيلات التورنت بالجملة مجموعة استخدم الأدوات عبر الإنترنت للتحويل بين التنسيقات. أدوات التحويل الموصى بها: %(links)s بالنسبة للملفات الكبيرة، نوصي باستخدام مدير تنزيل لمنع الانقطاعات. مديرو التنزيل الموصى بهم: %(links)s فهرس كتب EBSCOhost الإلكترونية (للعارفين فقط) (وانقر على «GET» أعلاه) (انقر على «GET» أعلاه) تنزيلات خارجية <strong>🚀 تنزيلات سريعة</strong> مازال عندك %(remaining)s يومًا/أيام. شكرا لانضمامك! ❤️ <strong>🚀 تنزيلات سريعة</strong> أكملت تنزيلاتك السريعة المسموح لك بها لهذا اليوم. <strong> 🚀 تنزيلات سريعة</strong> نزّلت هذا الملف مؤخرًا. الروابط صالحة لفترة معينة. <strong> 🚀 تنزيلات سريعة </strong> <a %(a_membership)s> انضم</a> لدعمنا في حفظ الكتب والرسائل وغيرهما. ولنشكرك نُتيح لك التنزيل أسرع من البقية. ❤️ 🚀 تنزيلات سريعة 🐢 تنزيلات بطيئة استعارة من رَبيدةُ الشّابكة (Internet Archive) بوابة «IPFS» #%(num)d (كرّر محاولاتك مع «IPFS» لتنجح) مكتبة التَّكوين Libgen.li القسم الخيالي من مكتبة التَّكوين Libgen.rs القسم الواقعي من مكتبة التَّكوين Libgen.rs إعلاناتهم معروفة باحتوائها على برامج ضارة، لذا استخدم مانع الإعلانات أو لا تنقر على الإعلانات خدمة "Send to Kindle" من أمازون خدمة "Send to Kobo/Kindle" من djazz MagzDB ManualsLib Nexus/STC (قد تكون ملفات Nexus/STC غير موثوقة للتنزيل) لا توجد تنزيلات. كلّ خيارات التنزيل آمنة وبها نفس الملف. إلّا أن الحذر واجب عند تنزيل الملفات من الشّابكة، وخاصة إن كانت من مواقع خارجية لا تتبع رَبيدة آنَّا. فتأكد من تحديث أجهزتك باستمرار كأقل أساليب الحماية. (بدون إعادة توجيه) افتح في العارض الخاص بنا (افتح في العارض) خيار #%(num)d: %(link)s %(extra)s ابحث عن السجل الأصلي في المكتبة الرقمية الصينية (CADAL) ابحث بنفسك في الدّوشية ابحث عن السجل الأصلي في مجمع بيانات النظام القياسي الدولي لترقيم الكتب (ISBNdb) ابحث عن السجل الأصلي في الفهرس العالمي ابحث عن السجل الأصلي في المكتبة المفتوحة ابحث عن الردمك في قواعد بيانات أخرى متنوعة (للداعمين أصحاب عسر في القراءة فقط) PubMed ستحتاج إلى قارئ كتب إلكترونية أو قارئ PDF لفتح الملف، حسب تنسيق الملف. قراء الكتب الإلكترونية الموصى بهم: %(links)s رَبيدةُ آنّا 🧬 مجمع البيانات العلمية مجمع العلوم (Sci-Hub): %(doi)s معرّفات الأغراض الرقمية المرتبطة بها قد لا تتوفر في مجمع العلوم يمكنك إرسال ملفات PDF وEPUB إلى جهاز Kindle أو Kobo eReader الخاص بك. الأدوات الموصى بها: %(links)s مزيد من المعلومات في <a %(a_slow)s>الأسئلة الشائعة</a>. دعم المؤلفين والمكتبات إذا أعجبك هذا ويمكنك تحمل تكلفته، فكر في شراء النسخة الأصلية، أو دعم المؤلفين مباشرةً. إذا كان هذا متاحًا في مكتبتك المحلية، فكر في استعارة الكتاب مجانًا من هناك. تنزيل هذا الملف من الخادوم الشريك غير متاح مؤقتًا. تورنت من شركائنا الموثوقين. مكتبة الزّاي Z-Library على Tor (يحتاج إلى متصفح الTor) عرض التنزيلات الخارجية <span class="font-bold">❌ قد يكون بهذا الملف مشاكل واُخفيَ من مصدره. </span> وسبب هذا إما لطلب من صاحب حقوق النشر، أو لوجود بديلٍ أفضل، أو لمشكلة في الملف نفسه. ربما أمكنك تنزيله، إلّا أنّنا نوصي بالبحث عن ملف آخر بديل له. للمزيد من التفاصيل: إن مازلت على رأيك في تنزيله فتأكد من فتحه ببرامج موثوقة ومحدّثة تجنبًا لأية مشكلة. تعاليق البيانات الوصفية رَبيدةُ آنّا: ابحث في رَبيدة آنّا عن «%(name)s» مستكشف الأكواد: عرض في مستكشف الأكواد “%(name)s” الرابط: الموقع: إن كنت تملك هذا الملف وهو غير موجود في رَبيدة آنّا، فأعنّا و <a %(a_request)s>حمّله</a>. ملف بالإعارة الرقمية المضبوطة لرَبيدة الشّابكة «%(id)s» هذا سجل لملف من رَبيدة الشّابكة، وغير قابل للتنزيل المباشر. فاستعر الكتاب (من الرابط أدناه)، أول اطلب ملفًا <a %(a_request)s>من هذا الرابط</a>. تحسين البيانات الوصفية سجل بيانات «SSNO» الوصفية للمكتبة الرقمية الصينية (CADAL) %(id)s هذا سجل بيانات وصفية، وليس ملفًا للتنزيل. فاطلب ملفًا <a %(a_request)s>من هذا الرابط</a>. سجل بيانات «SSID» الوصفية لدّوشية %(id)s سجل البيانات الوصفية للمجمع الرقمي لترقيم الكتب %(id)s سجل البيانات الوصفية لـ MagzDB ID %(id)s سجل البيانات الوصفية لـ Nexus/STC ID %(id)s سجل البيانات الوصفية للفهرس العالمي (OCLC) %(id)s سجل البيانات الوصفية للمكتبة المفتوحة %(id)s ملف مجمع العلوم «%(id)s» لم يُعثر عليه لم يُعثر على %(md5_input)s في قاعدة بياناتنا. إضافة تعليق (%(count)s) يمكنك الحصول على md5 من عنوان URL، على سبيل المثال MD5 لنسخة أفضل من هذا الملف (إذا كان ذلك ممكنًا). املأ هذا إذا كان هناك ملف آخر يتطابق بشكل وثيق مع هذا الملف (نفس الإصدار، نفس امتداد الملف إذا كان بإمكانك العثور على واحد)، والذي يجب أن يستخدمه الناس بدلاً من هذا الملف. إذا كنت تعرف نسخة أفضل من هذا الملف خارج رَبيدةُ آنّا، فيرجى <a %(a_upload)s>تحميلها</a>. حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى. لقد تركت تعليقًا. قد يستغرق الأمر دقيقة ليظهر. يرجى استخدام <a %(a_copyright)s>نموذج مطالبة DMCA / حقوق الطبع والنشر</a>. وصف المشكلة (مطلوب) إذا كان هذا الملف ذو جودة عالية، يمكنك مناقشة أي شيء عنه هنا! إذا لم يكن كذلك، يرجى استخدام زر "الإبلاغ عن مشكلة في الملف". جودة الملف ممتازة (%(count)s) جودة الملف تعلم كيفية <a %(a_metadata)s>تحسين البيانات الوصفية</a> لهذا الملف بنفسك. وصف المشكلة يرجى <a %(a_login)s>تسجيل الدخول</a>. أحببت هذا الكتاب! ساعد المجتمع بالإبلاغ عن جودة هذا الملف! 🙌 حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى. الإبلاغ عن مشكلة في الملف (%(count)s) شكرًا لك على تقديم تقريرك. سيتم عرضه على هذه الصفحة، وكذلك مراجعته يدويًا بواسطة آنّا (حتى يكون لدينا نظام مراجعة مناسب). اترك تعليقًا إرسال التقرير ما المشكلة في هذا الملف؟ الاستعارات (%(count)s) التعليقات (%(count)s) التنزيلات (%(count)s) عمليات البحث عن البيانات الوصفية (%(count)s) القوائم (%(count)s) الإحصائيات (%(count)s) لمزيد من المعلومات حول هذا الملف المحدد، تحقق من <a %(a_href)s>ملف JSON</a> الخاص به. هذا ملف مُدار بواسطة مكتبة <a %(a_ia)s>الإعارة الرقمية المُتحكم بها من IA</a>، ومفهرس بواسطة رَبيدةُ آنّا للبحث. لمزيد من المعلومات حول مجموعات البيانات المختلفة التي قمنا بتجميعها، راجع <a %(a_datasets)s>صفحة Datasets</a>. البيانات الوصفية من السجل المرتبط تحسين البيانات الوصفية على Open Library "ملف MD5" هو تجزئة يتم حسابها من محتويات الملف، وهي فريدة بشكل معقول بناءً على هذا المحتوى. جميع المكتبات الظلية التي قمنا بفهرستها هنا تستخدم MD5s لتحديد الملفات. قد يظهر الملف في مكتبات ظلية متعددة. لمزيد من المعلومات حول مجموعات البيانات المختلفة التي قمنا بتجميعها، راجع <a %(a_datasets)s>صفحة Datasets</a>. الإبلاغ عن جودة الملف إجمالي التنزيلات: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} البيانات الوصفية التشيكية %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} تحذير: سجلات مرتبطة متعددة: عندما تنظر إلى كتاب على رَبيدةُ آنّا، يمكنك رؤية حقول مختلفة: العنوان، المؤلف، الناشر، الطبعة، السنة، الوصف، اسم الملف، والمزيد. كل هذه المعلومات تُسمى <em>البيانات الوصفية</em>. نظرًا لأننا نجمع الكتب من <em>مكتبات مصدر</em> مختلفة، فإننا نعرض أي بيانات وصفية متاحة في مكتبة المصدر تلك. على سبيل المثال، بالنسبة لكتاب حصلنا عليه من Library Genesis، سنعرض العنوان من قاعدة بيانات Library Genesis. أحيانًا يكون الكتاب موجودًا في <em>مكتبات مصدر</em> متعددة، والتي قد تحتوي على حقول بيانات وصفية مختلفة. في هذه الحالة، نعرض ببساطة النسخة الأطول من كل حقل، لأن تلك النسخة تحتوي على الأرجح على المعلومات الأكثر فائدة! سنظل نعرض الحقول الأخرى أسفل الوصف، مثل "عنوان بديل" (ولكن فقط إذا كانت مختلفة). نقوم أيضًا باستخراج <em>الأكواد</em> مثل المعرفات والمصنفات من مكتبة المصدر. <em>المعرفات</em> تمثل بشكل فريد طبعة معينة من كتاب؛ أمثلة على ذلك هي ISBN، DOI، معرف Open Library، معرف Google Books، أو معرف Amazon. <em>المصنفات</em> تجمع بين كتب متشابهة متعددة؛ أمثلة على ذلك هي Dewey Decimal (DCC)، UDC، LCC، RVK، أو GOST. أحيانًا تكون هذه الأكواد مرتبطة بشكل صريح في مكتبات المصدر، وأحيانًا يمكننا استخراجها من اسم الملف أو الوصف (بشكل أساسي ISBN و DOI). يمكننا استخدام المعرفات للعثور على سجلات في <em>مجموعات البيانات الوصفية فقط</em>، مثل OpenLibrary، ISBNdb، أو WorldCat/OCLC. هناك علامة تبويب <em>البيانات الوصفية</em> محددة في محرك البحث الخاص بنا إذا كنت ترغب في تصفح تلك المجموعات. نستخدم السجلات المطابقة لملء حقول البيانات الوصفية المفقودة (مثل إذا كان العنوان مفقودًا)، أو مثل "عنوان بديل" (إذا كان هناك عنوان موجود). لمعرفة بالضبط من أين جاءت البيانات الوصفية لكتاب ما، انظر إلى علامة التبويب <em>“التفاصيل التقنية”</em> في صفحة الكتاب. تحتوي على رابط إلى JSON الخام لذلك الكتاب، مع إشارات إلى JSON الخام للسجلات الأصلية. لمزيد من المعلومات، انظر الصفحات التالية: <a %(a_datasets)s>Datasets</a>، <a %(a_search_metadata)s>البحث (علامة تبويب البيانات الوصفية)</a>، <a %(a_codes)s>مستكشف الأكواد</a>، و<a %(a_example)s>مثال على JSON للبيانات الوصفية</a>. أخيرًا، يمكن <a %(a_generated)s>توليد</a> أو <a %(a_downloaded)s>تحميل</a> جميع بياناتنا الوصفية كقواعد بيانات ElasticSearch و MariaDB. الخلفية يمكنك المساعدة في حفظ الكتب من خلال تحسين البيانات الوصفية! أولاً، اقرأ الخلفية حول البيانات الوصفية على رَبيدةُ آنّا، ثم تعلم كيفية تحسين البيانات الوصفية من خلال الربط مع Open Library، واحصل على عضوية مجانية على رَبيدةُ آنّا. تحسين البيانات الوصفية إذاً إذا واجهت ملفًا يحتوي على بيانات وصفية سيئة، كيف يجب عليك إصلاحه؟ يمكنك الذهاب إلى مكتبة المصدر واتباع إجراءاتها لإصلاح البيانات الوصفية، ولكن ماذا تفعل إذا كان الملف موجودًا في مكتبات مصدر متعددة؟ هناك معرف واحد يتم التعامل معه بشكل خاص على رَبيدةُ آنّا. <strong>حقل annas_archive md5 في Open Library يتجاوز دائمًا جميع البيانات الوصفية الأخرى!</strong> دعونا نعود قليلاً ونتعلم عن Open Library. تأسست Open Library في عام 2006 بواسطة آرون شوارتز بهدف "صفحة ويب واحدة لكل كتاب تم نشره على الإطلاق". إنها نوع من ويكيبيديا لبيانات الكتب الوصفية: يمكن للجميع تحريرها، وهي مرخصة بحرية، ويمكن تنزيلها بكميات كبيرة. إنها قاعدة بيانات للكتب تتماشى بشكل كبير مع مهمتنا — في الواقع، رَبيدةُ آنّا مستوحاة من رؤية وحياة آرون شوارتز. بدلاً من إعادة اختراع العجلة، قررنا توجيه متطوعينا نحو Open Library. إذا رأيت كتابًا يحتوي على بيانات وصفية غير صحيحة، يمكنك المساعدة بالطريقة التالية: لاحظ أن هذا ينطبق فقط على الكتب، وليس الأوراق الأكاديمية أو أنواع الملفات الأخرى. بالنسبة لأنواع الملفات الأخرى، نوصي بالعثور على مكتبة المصدر. قد يستغرق الأمر بضعة أسابيع لتضمين التغييرات في رَبيدةُ آنّا، حيث نحتاج إلى تنزيل أحدث تفريغ بيانات Open Library، وإعادة إنشاء فهرس البحث الخاص بنا.  اذهب إلى <a %(a_openlib)s>موقع Open Library</a>. ابحث عن سجل الكتاب الصحيح. <strong>تحذير:</strong> تأكد من اختيار <strong>الطبعة</strong> الصحيحة. في Open Library، لديك "الأعمال" و"الطبعات". يمكن أن تكون "العمل" هو "هاري بوتر وحجر الفيلسوف". يمكن أن تكون "الطبعة": الطبعة الأولى لعام 1997 التي نشرتها بلومزبري وتتكون من 256 صفحة. الطبعة الورقية لعام 2003 التي نشرتها رينكوست بوكس وتتكون من 223 صفحة. الترجمة البولندية لعام 2000 "هاري بوتر وحجر الفيلسوف" بواسطة ميديا رودزينا وتتكون من 328 صفحة. جميع هذه الطبعات لها أرقام ISBN مختلفة ومحتويات مختلفة، لذا تأكد من اختيار الطبعة الصحيحة! قم بتحرير السجل (أو إنشائه إذا لم يكن موجودًا)، وأضف أكبر قدر ممكن من المعلومات المفيدة! أنت هنا الآن على أي حال، لذا اجعل السجل رائعًا حقًا. تحت "أرقام الهوية" اختر "رَبيدةُ آنّا" وأضف MD5 للكتاب من رَبيدةُ آنّا. هذا هو السلسلة الطويلة من الحروف والأرقام بعد "/md5/" في عنوان URL. حاول العثور على ملفات أخرى في رَبيدةُ آنّا تتطابق أيضًا مع هذا السجل، وأضفها كذلك. في المستقبل يمكننا تجميعها كنسخ مكررة في صفحة بحث رَبيدةُ آنّا. عند الانتهاء، اكتب عنوان URL الذي قمت بتحديثه للتو. بمجرد تحديثك لما لا يقل عن 30 سجلًا باستخدام MD5 من رَبيدةُ آنّا، أرسل لنا <a %(a_contact)s>بريدًا إلكترونيًا</a> وأرسل لنا القائمة. سنمنحك عضوية مجانية في رَبيدةُ آنّا، حتى تتمكن من القيام بهذا العمل بسهولة أكبر (وكشكر لك على مساعدتك). يجب أن تكون هذه التعديلات عالية الجودة وتضيف كميات كبيرة من المعلومات، وإلا سيتم رفض طلبك. سيتم أيضًا رفض طلبك إذا تم التراجع عن أي من التعديلات أو تصحيحها بواسطة مشرفي Open Library. الربط مع Open Library إذا شاركت بشكل كبير في تطوير وتشغيل عملنا، يمكننا مناقشة مشاركة المزيد من إيرادات التبرعات معك، لتستخدمها حسب الضرورة. سندفع فقط مقابل الاستضافة بمجرد أن يكون لديك كل شيء معدًا، وقد أثبتت أنك قادر على الحفاظ على الأرشيف محدثًا بالتحديثات. هذا يعني أنك ستدفع من جيبك الخاص لأول 1-2 شهر. وقتك لن يتم تعويضه (وكذلك وقتنا)، لأن هذا عمل تطوعي بحت. نحن على استعداد لتغطية نفقات الاستضافة وVPN، في البداية حتى 200 دولار شهريًا. هذا يكفي لخادم بحث أساسي ووكيل محمي من DMCA. نفقات الاستضافة يرجى <strong>عدم الاتصال بنا</strong> لطلب الإذن، أو لطرح أسئلة أساسية. الأفعال أبلغ من الأقوال! كل المعلومات موجودة، لذا ابدأ بإعداد العاكسة الخاصة بك. لا تتردد في نشر تذاكر أو طلبات دمج إلى Gitlab الخاص بنا عندما تواجه مشكلات. قد نحتاج إلى بناء بعض الميزات الخاصة بالعاكسة معك، مثل إعادة التسمية من "رَبيدةُ آنّا" إلى اسم موقعك، (في البداية) تعطيل حسابات المستخدمين، أو الربط بموقعنا الرئيسي من صفحات الكتب. بمجرد أن تكون عاكستك قيد التشغيل، يرجى الاتصال بنا. نود مراجعة أمان التشغيل الخاص بك، وبمجرد أن يكون ذلك قويًا، سنربط بعاكستك، ونبدأ في العمل بشكل أقرب معك. شكرًا مقدمًا لأي شخص على استعداد للمساهمة بهذه الطريقة! إنه ليس لضعاف القلوب، ولكنه سيعزز من ديمومة أكبر مكتبة مفتوحة حقًا في تاريخ البشرية. البدء لزيادة مرونة رَبيدةُ آنّا، نبحث عن متطوعين لتشغيل المرايا. نسختك مميزة بوضوح كعاكسة، على سبيل المثال "أرشيف بوب، عاكسة لرَبيدةُ آنّا". أنت على استعداد لتحمل المخاطر المرتبطة بهذا العمل، وهي كبيرة. لديك فهم عميق للأمن التشغيلي المطلوب. محتويات <a %(a_shadow)s>هذه</a> <a %(a_pirate)s>المشاركات</a> واضحة لك. في البداية لن نعطيك حق الوصول إلى تنزيلات خادم الشريك الخاص بنا، ولكن إذا سارت الأمور بشكل جيد، يمكننا مشاركة ذلك معك. أنت تدير قاعدة الشيفرة المفتوحة المصدر لرَبيدةُ آنّا، وتقوم بتحديث الشيفرة والبيانات بانتظام. أنت على استعداد للمساهمة في <a %(a_codebase)s>قاعدة الشيفرة</a> الخاصة بنا - بالتعاون مع فريقنا - لتحقيق ذلك. نحن نبحث عن هذا: مرايا: دعوة للمتطوعين تبرّعٌ آخر. لا تبرعات حتى اللحظة. <a %(a_donate)s> تبرّع الآن لأول مرة. </a> لا تُعرض تفاصيل التبرعات للعامة. تبرعاتي 📡 لإظهار كل مافي مجموعتنا ونسخها = تصفح <a %(a_datasets)s>مجمع البيانات</a> و<a %(a_torrents)s>التورنت</a>. التنزيلات من عنوان IP الخاص بك في آخر 24 ساعة: %(count)s. <a %(a_membership)s>انضم</a> لتحصل على تنزيل سريع وتتفادى التحقق من متصفحك كلّ مرة. تنزيل من موقع الشريك لا تتردد في مواصلة تصفح رَبيدةُ آنّا في علامة تبويب مختلفة أثناء الانتظار (إذا كان متصفحك يدعم تحديث علامات التبويب في الخلفية). لا تتردد في انتظار تحميل صفحات تنزيل متعددة في نفس الوقت (ولكن يرجى تنزيل ملف واحد فقط في نفس الوقت لكل خادم). بمجرد حصولك على رابط التنزيل، يكون صالحًا لعدة ساعات. شكرًا لانتظارك، هذا يجعل الموقع متاحًا مجانًا للجميع! 😊 🔗 كلّ روابط تنزيل هذا الملف: <a %(a_main)s>الصفحة الرئيسة للملف</a>. ❌ التنزيلات البطيئة غير متاحة عبر شبكات VPN الخاصة بـ Cloudflare أو من عناوين IP الخاصة بـ Cloudflare. ❌ التنزيلات البطيئة متاحة فقط من خلال الموقع الرسمي. قم بالزيارة %(websites)s. 📚 ستخدم عنوان URL التالي لتحميل: <a %(a_download)s>التحميل الان</a>. من أجل إعطاء الجميع فرصة لتحميل الملفات مجانًا، يجب عليك الانتظار قبل أن تتمكن من تحميل هذا الملف. يرجى الانتظار <span %(span_countdown)s>%(wait_seconds)s</span> ثانية لتحميل هذا الملف. تحذير: كان هناك الكثير من التنزيلات من عنوان IP الخاص بك خلال الـ ٢٤ ساعة الماضية. قد تكون التنزيلات أبطأ من المعتاد. إذا كنت تستخدم VPN، أو اتصال إنترنت مشترك، أو أن مزود خدمة الإنترنت يشارك عناوين IP، فقد يكون هذا التحذير بسبب ذلك. احفظ ❌ حدث خطأ ما. حاول مرة اخرى. ✅ حُفظ. رجاء أعد تحميل الصفحة. غيّر اسمك المعروض. لا يُمكنك تغيير معرّفك (ما يلي «#»). أُنشئ الملف <span %(span_time)s>%(time)s </span> عدّل القوائم انشئ قائمةً جديدة بالبحث عن ملف وفتح تبويبة «القوائم». لا قوائم حتى الآن لا وجود للملف. الملف الشخصي لا نقبل طلبات لتوفير الكتب حاليًا. لا تكتب لنا بريدًا تطلب فيه كتابًا. اكتب طلبك في منتديات مكتبة الزّاي والتَّكوين. سجل في أرشيف آنا DOI: %(doi)s نزل SciDB Nexus/STC لا توجد معاينة متاحة بعد. قم بتنزيل الملف من <a %(a_path)s>رَبيدةُ آنّا</a>. لدعم الوصول والحفاظ على المعرفة البشرية على المدى الطويل، انضم كـ <a %(a_donate)s>عضو</a>. كمكافأة، 🧬&nbsp;مجمع البيانات العلمية يتم تحميله بشكل أسرع للأعضاء، بدون أي حدود. لا يعمل؟ حاول <a %(a_refresh)s>إعادة التحميل</a>. Sci-Hub إضافة حقل بحث محدد ابحث في الأوصاف وتعليقات البيانات الوصفية سنة النشر متقدم الوصول محتوى عرض قائمة جدول نوع الملف لغة ترتيب حسب الأكبر الأكثر صلة الأحدث (حجم الملف) (مفتوح المصدر) (سنة النشر) الأقدم عشوائي الأصغر المصدر تم جمعها ومصدرها المفتوح بواسطة AA الإقراض الرقمي (%(count)s) مقالات المجلات (%(count)s) لقد وجدنا تطابقات في: %(in)s. يمكنك الرجوع إلى عنوان URL الموجود هناك عند <a %(a_request)s>طلب ملف</a>. البيانات الوصفية (%(count)s) لاستكشاف فهرس البحث بواسطة الأكواد، استخدم <a %(a_href)s>مستكشف الأكواد</a>. يتم تحديث فهرس البحث شهرياً. يتضمن حالياً مدخلات حتى تاريخ %(last_data_refresh_date)s. للمزيد من المعلومات اطلع على %(link_open_tag)s صفحة البيانات</a>. استبعاد تضمين فقط غير مُراجَع المزيد… التالي … السابق يتضمن فهرس البحث هذا حاليًا بيانات وصفية من مكتبة الإقراض الرقمي الخاضع للرقابة في أرشيف الإنترنت. <a %(a_datasets)s>المزيد حول مجموعات البيانات لدينا</a>. لمزيد من مكتبات الإعارة الرقمية، راجع <a %(a_wikipedia)s>ويكيبيديا</a> و<a %(a_mobileread)s>MobileRead Wiki</a>. بالنسبة إلى مطالبات قانون الألفية الجديدة لحقوق طبع ونشر المواد الرقمية / حقوق الطبع والنشر <a %(a_copyright)s>انقر هنا</a>. وقت التنزيل خطأ أثناء البحث. حاول <a %(a_reload)s>إعادة تحميل الصفحة</a>. إذا استمرت المشكلة، يُرجى مراسلتنا عبر البريد الإلكتروني على %(email)s. تحميل سريع في الواقع، يمكن لأي شخص المساعدة في الحفاظ على هذه الملفات عن طريق توزيع <a %(a_torrents)s>قائمة التورنت الموحدة</a> الخاصة بنا. ➡️ يحدث هذا أحيانًا بشكل غير صحيح عندما يكون خادم البحث بطيئًا. في مثل هذه الحالات، يمكن أن يساعد <a %(a_attrs)s>إعادة التحميل</a>. ❌ هذا الملف قد يكون فيه مشاكل. تبحث عن أوراق بحثية؟ يتضمن فهرس البحث هذا حاليًا بيانات تعريفية من مصادر بيانات تعريفية مختلفة. <a %(a_datasets)s>المزيد حول مجموعات البيانات لدينا</a>. هناك العديد والعديد من مصادر البيانات الوصفية للأعمال المكتوبة حول العالم. <a %(a_wikipedia)s>صفحة ويكيبيديا هذه</a> هي بداية جيدة، ولكن إذا كنت تعرف قوائم جيدة أخرى، فيُرجى إخبارنا بذلك. بالنسبة للبيانات الوصفية، نعرض السجلات الأصلية. نحن لا نقوم بدمج السجلات. لدينا حاليًا الكتالوج المفتوح الأكثر شمولاً في العالم للكتب والأبحاث والأعمال المكتوبة الأخرى. نحن نعكس Sci-Hub وLibrary Genesis وZ-Library و<a %(a_datasets)s>والمزيد</a>. <span class="font-bold"> لم توجد أي ملفات. </span> حاول استخدام مدخلات أقل أو مختلفة في البحث والتنقية. النتائج %(from)s-%(to)s (%(total)s إجمالي) إذا وجدت "مكتبات ظل" أخرى يجب علينا مرآتة، أو إذا كانت لديك أية أسئلة، فيُرجى الاتصال بنا على %(email)s. مطابقات جزئي %(num)d مطابقات جزئي %(num)d+ اكتب في المربع للبحث عن الملفات في مكتبات الإقراض الرقمية. اكتب في المربع للبحث في كتالوجنا الذي يضم %(count)s من الملفات القابلة للتنزيل مباشرة، والتي <a %(a_preserve)s>نحتفظ بها إلى الأبد</a>. اكتب في المربع للبحث. اكتب في المربع للبحث في كتالوجنا من %(count)s الأوراق الأكاديمية والمقالات العلمية، التي <a %(a_preserve)s>نحافظ عليها للأبد</a>. اكتب في المربع للبحث عن بيانات التعريف من المكتبات. يمكن أن يكون هذا مفيدًا عند <a %(a_request)s>طلب ملف</a>. نصيحة: استخدم اختصارات لوحة المفاتيح "/" (تركيز البحث)، و"enter" (بحث)، و"j" (لأعلى)، و"k" (لأسفل) للتنقل بشكل أسرع. هذه سجلات بيانات وصفية، <span %(classname)s>وليست</span> ملفات قابلة للتنزيل. إعدادات البحث بحث الإقراض الرقمي نزل مقالات المجلات البيانات الوصفية بحث جديد %(search_input)s - بحث استغرق البحث وقتًا طويلاً، مما يعني أنك قد ترى نتائج غير دقيقة. في بعض الأحيان، تساعد <a %(a_reload)s>إعادة تحميل</a> الصفحة. استغرق البحث وقتًا طويلاً، وهو أمر شائع بالنسبة للاستعلامات واسعة النطاق. قد لا تكون أعداد التصفيات دقيقة. الملفات الكثيرة (أكثر من عشرة ألف ملف) راسلنا بـ%(a_email)s لرفعها، ذلك لأن هذا العدد الكبير لا يُقبل من مكتبة الزّاي ومكتبة التَّكوين. بالنسبة لـ Libgen.li، تأكد من تسجيل الدخول أولاً على <a %(a_forum)s>منتداهم</a> باستخدام اسم المستخدم %(username)s وكلمة المرور %(password)s، ثم العودة إلى <a %(a_upload_page)s>صفحة التحميل الخاصة بهم</a>. نقترح الآن رفع كتب جديدة للفروع المعدّلة من مكتبة التَّكوين. هذا <a %(a_guide)s> دليل مفيد </a>. لاحظ أن كلا الفرعين اللذين فهرسناهما على موقعنا هذا يسحبان من نفس النظام. لعمليات التحميل الصغيرة (حتى 10,000 ملف) يرجى تحميلها إلى كل من %(first)s و%(second)s. بدلاً من ذلك، يمكنك تحميلها إلى مكتبة الزّاي <a %(a_upload)s>هنا</a>. لتحميل الأوراق الأكاديمية، يرجى أيضًا (بالإضافة إلى Library Genesis) التحميل إلى <a %(a_stc_nexus)s>STC Nexus</a>. هم أفضل مكتبة ظل للأوراق الجديدة. لم نقم بدمجهم بعد، لكننا سنفعل ذلك في وقت ما. يمكنك استخدام <a %(a_telegram)s>روبوت التحميل على Telegram</a>، أو الاتصال بالعنوان المدرج في رسالتهم المثبتة إذا كان لديك الكثير من الملفات لتحميلها بهذه الطريقة. <span %(label)s>العمل التطوعي المكثف (مكافآت مالية تتراوح بين 50 دولارًا أمريكيًا و5000 دولار أمريكي):</span> إذا كنت قادرًا على تخصيص الكثير من الوقت و/أو الموارد لمهمتنا، فنحن نود العمل معك بشكل أوثق. في النهاية يمكنك الانضمام إلى الفريق الداخلي. على الرغم من أن ميزانيتنا محدودة، إلا أننا قادرون على منح <span %(bold)s>💰 مكافآت مالية</span> للعمل الأكثر كثافة. <span %(label)s>عمل تطوعي خفيف:</span> إذا كان بإمكانك توفير بضع ساعات هنا وهناك، لا يزال هناك العديد من الطرق التي يمكنك من خلالها المساعدة. نكافئ المتطوعين المستمرين بـ <span %(bold)s>🤝 عضويات في رَبيدةُ آنّا</span>. يعتمد رَبيدةُ آنّا على متطوعين مثلك. نرحب بجميع مستويات الالتزام، ولدينا فئتان رئيسيتان من المساعدة التي نبحث عنها: إذا لم تكن قادرًا على التطوع بوقتك، فلا يزال بإمكانك مساعدتنا كثيرًا من خلال <a %(a_donate)s>التبرع بالمال</a>، <a %(a_torrents)s>مشاركة التورنت الخاص بنا</a>، <a %(a_uploading)s>رفع الكتب</a>، أو <a %(a_help)s>إخبار أصدقائك عن رَبيدةُ آنّا</a>. <span %(bold)s>الشركات:</span> نحن نقدم وصولًا مباشرًا عالي السرعة إلى مجموعاتنا مقابل تبرع على مستوى المؤسسة أو مقابل مجموعات جديدة (مثل عمليات المسح الجديدة، Datasets الممسوحة ضوئيًا، إثراء بياناتنا). <a %(a_contact)s>اتصل بنا</a> إذا كنت مهتمًا. انظر أيضًا صفحتنا <a %(a_llm)s>LLM</a>. المكافآت نحن دائمًا نبحث عن أشخاص يمتلكون مهارات برمجة قوية أو مهارات في الأمن الهجومي للمشاركة. يمكنك أن تساهم بشكل جدي في الحفاظ على إرث البشرية. كشكر لك، نقدم عضوية للمساهمات القوية. وكشكر كبير، نقدم مكافآت مالية للمهام المهمة والصعبة بشكل خاص. لا ينبغي اعتبار هذا بديلاً عن وظيفة، ولكنه حافز إضافي ويمكن أن يساعد في تغطية التكاليف المتكبدة. معظم كودنا مفتوح المصدر، وسنطلب أن يكون كودك كذلك عند منح المكافأة. هناك بعض الاستثناءات التي يمكننا مناقشتها على أساس فردي. تُمنح المكافآت لأول شخص يكمل المهمة. لا تتردد في التعليق على تذكرة المكافأة لإعلام الآخرين بأنك تعمل على شيء ما، حتى يتمكن الآخرون من التوقف أو الاتصال بك للتعاون. ولكن كن على علم بأن الآخرين لا يزالون أحرارًا في العمل عليها أيضًا ومحاولة التفوق عليك. ومع ذلك، لا نمنح المكافآت للعمل الرديء. إذا تم تقديم مساهمتين عاليتي الجودة في وقت قريب من بعضهما (في غضون يوم أو يومين)، فقد نختار منح المكافآت لكليهما، وفقًا لتقديرنا، على سبيل المثال 100%% للمساهمة الأولى و50%% للمساهمة الثانية (أي 150%% إجمالاً). بالنسبة للمكافآت الكبيرة (خاصة مكافآت التجريف)، يرجى الاتصال بنا عندما تكمل ~5%% منها، وتكون واثقًا من أن طريقتك ستتوسع لتشمل المعلم الكامل. سيتعين عليك مشاركة طريقتك معنا حتى نتمكن من تقديم الملاحظات. أيضًا، بهذه الطريقة يمكننا أن نقرر ما يجب فعله إذا كان هناك عدة أشخاص يقتربون من المكافأة، مثل منحها لعدة أشخاص، تشجيع الناس على التعاون، إلخ. تحذير: المهام ذات المكافآت العالية <span %(bold)s>صعبة</span> — قد يكون من الحكمة البدء بالمهام الأسهل. اذهب إلى <a %(a_gitlab)s>قائمة مشكلات Gitlab الخاصة بنا</a> وقم بالفرز حسب "أولوية التسمية". هذا يظهر تقريبًا ترتيب المهام التي نهتم بها. المهام التي لا تحتوي على مكافآت صريحة لا تزال مؤهلة للعضوية، خاصة تلك التي تم وضع علامة "مقبولة" و"المفضلة لدى آنّا". قد ترغب في البدء بمشروع "مبتدئ". التطوع الخفيف لدينا الآن أيضًا قناة Matrix متزامنة على %(matrix)s. إذا كان لديك بضع ساعات فراغ، يمكنك المساعدة بعدة طرق. تأكد من الانضمام إلى <a %(a_telegram)s>دردشة المتطوعين على Telegram</a>. كعربون تقدير، نعطي عادةً 6 أشهر من عضوية "أمين المكتبة المحظوظ" للإنجازات الأساسية، وأكثر للعمل التطوعي المستمر. تتطلب جميع الإنجازات عملًا عالي الجودة — العمل الرديء يضرنا أكثر مما ينفعنا وسنرفضه. يرجى <a %(a_contact)s>إرسال بريد إلكتروني إلينا</a> عندما تصل إلى إنجاز. %(links)s روابط أو لقطات شاشة للطلبات التي قمت بتلبيتها. تلبية طلبات الكتب (أو الأوراق، إلخ) على منتديات مكتبة الزّاي أو Library Genesis. ليس لدينا نظام طلبات الكتب الخاص بنا، لكننا نعكس تلك المكتبات، لذا فإن تحسينها يجعل رَبيدةُ آنّا أفضل أيضًا. الإنجاز المهمة يعتمد على المهمة. مهام صغيرة منشورة على <a %(a_telegram)s>دردشة المتطوعين على Telegram</a>. عادةً للحصول على عضوية، وأحيانًا لمكافآت صغيرة. مهام صغيرة منشورة في مجموعة الدردشة التطوعية لدينا. تأكد من ترك تعليق على المشكلات التي تقوم بإصلاحها، حتى لا يكرر الآخرون عملك. %(links)s روابط السجلات التي قمت بتحسينها. يمكنك استخدام <a %(a_list)s>قائمة مشكلات الميتاداتا العشوائية</a> كنقطة انطلاق. تحسين البيانات الوصفية عن طريق <a %(a_metadata)s>الربط</a> مع Open Library. يجب أن تُظهر هذه أنك تُخبر شخصًا ما عن رَبيدةُ آنّا، وهو يشكرك. %(links)s روابط أو لقطات شاشة. نشر كلمة رَبيدةُ آنّا. على سبيل المثال، من خلال التوصية بالكتب على AA، أو الربط بمشاركات مدونتنا، أو توجيه الناس بشكل عام إلى موقعنا الإلكتروني. ترجمة لغة كاملة (إذا لم تكن قريبة من الاكتمال بالفعل). <a %(a_translate)s>ترجمة</a> الموقع. رابط إلى تاريخ التعديل يظهر أنك قمت بمساهمات كبيرة. تحسين صفحة ويكيبيديا الخاصة برَبيدةُ آنّا بلغتك. تضمين معلومات من صفحة ويكيبيديا الخاصة بـ AA بلغات أخرى، ومن موقعنا الإلكتروني ومدونتنا. إضافة مراجع إلى AA على الصفحات ذات الصلة الأخرى. التطوع والمكافآت 