msgid "layout.index.invalid_request"
msgstr "طلب خاطئ. زُرْ %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "مجمع العلوم (Sci-Hub)"

msgid "layout.index.header.tagline_libgen"
msgstr "مكتبة التَّكوين (LibGen library)"

msgid "layout.index.header.tagline_zlib"
msgstr "مكتبة الزّاي (Z-Lib)"

msgid "layout.index.header.tagline_openlib"
msgstr "المكتبة المفتوحة (OpenLib)"

msgid "layout.index.header.tagline_ia"
msgstr "مكتبة الإعارة من رَبيدةُ الشّابكة"

msgid "layout.index.header.tagline_duxiu"
msgstr "القارئ (DuXiu)"

msgid "layout.index.header.tagline_separator"
msgstr "، "

msgid "layout.index.header.tagline_and"
msgstr " و "

msgid "layout.index.header.tagline_and_more"
msgstr "والكثير"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;نُظهر ما في %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "نجرد ونفتح مصادر %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "كل كودنا وبياناتنا مفتوحة المصدر بالكامل."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;أعظم المكاتب المفتوحة في تأريخ البشر."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;كتابًا، و%(paper_count)s&nbsp; ورقةً محفوظات إلى الأبد."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;أعظم المكاتب المفتوحة في تأريخ البشر. ⭐️&nbsp;نُظهر ما في مجمع العلوم (Sci-Hub)، ومكتبة التَّكوين (LibGen library)، ومكتبة الزّاي (Z-Lib)، وغيرهم الكثير. 📈&nbsp;%(book_any)s كتابًا، و%(journal_article)s ورقةً، و%(book_comic)s قصّة مصوّرة، و%(magazine)s مجلّة = محفوظات إلى الأبد."

msgid "layout.index.header.tagline_short"
msgstr "📚&nbsp;أعظم مكتبة ذات بيانات مفتوحة في عالم.<br>⭐️ نُظهر ما في مجمع العلوم ومكتبة التَّكوين، وغيرهما الكثير."

msgid "common.md5_report_type_mapping.metadata"
msgstr "بيانات وصفية خاطئة (قد يكون الخطأ في العنوان، أو الوصف، أو صورة الغلاف)"

msgid "common.md5_report_type_mapping.download"
msgstr "علّة في التنزيل (قد يكون بسبب الاتصال، أو بَلاغ أخطاء، أو سرعة ضعيفة)"

msgid "common.md5_report_type_mapping.broken"
msgstr "لا يمكن فتح الملف (قد يكون تالف ، أو مقيّد بالحقوق الرقمية)"

msgid "common.md5_report_type_mapping.pages"
msgstr "جودة رديئة ( قد يكون التنسيق خاطئًا، أو التصوير سيئًا، أو صفحات مفقودة)"

msgid "common.md5_report_type_mapping.spam"
msgstr "ملف عشوائي / يجب حذف الملف (قد يكون إعلانًا، أو محتوًى مسيئ)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "ادعاءٌ بحق التأليف"

msgid "common.md5_report_type_mapping.other"
msgstr "أخرى"

msgid "common.membership.tier_name.bonus"
msgstr "تنزيلات إضافية"

msgid "common.membership.tier_name.2"
msgstr "محب للكتب"

msgid "common.membership.tier_name.3"
msgstr "قيّم مكتبةٍ مسعود"

msgid "common.membership.tier_name.4"
msgstr "مُكتنز بارع"

msgid "common.membership.tier_name.5"
msgstr "رابد عجيب"

msgid "common.membership.format_currency.total"
msgstr "بمجموع %(amount)s"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) المجموع"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s زيادة)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "غير مدفوع"

msgid "common.donation.order_processing_status_labels.1"
msgstr "مدفوع"

msgid "common.donation.order_processing_status_labels.2"
msgstr "مَلْغي"

msgid "common.donation.order_processing_status_labels.3"
msgstr "منتهي الصلاحية"

msgid "common.donation.order_processing_status_labels.4"
msgstr "في انتظار آنّا لتأكيده"

msgid "common.donation.order_processing_status_labels.5"
msgstr "غير صالح"

msgid "page.donate.title"
msgstr "تبرع"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "عندك <a %(a_donation)s>تبرعًا</a> قيد التنفيذ. أكمل أو ألغي ذاك التبرع ثم قدّم جديدًا."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>اعرض تبرعاتي كلّها</a>"

msgid "page.donate.header.text1"
msgstr "ربيدة آنّا مشروع غير ربحي مفتوح المصدر والبيانات. وتبرعك يُعيننا وبه تتطور أعمالنا فتصير واحدًا منّا. فشكرًا لأصحابنا الذين ساندونا! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "لمزيد من المعلومات، تحقق من <a %(a_donate)s>الأسئلة المتكررة عن التبرع</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "للحصول على المزيد من التنزيلات، <a %(a_refer)s>أحِل أصدقائك</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "إليك %(percentage)s تنزيل إضافي سريع؛ وذلك لذكر %(profile_link)s لك."

msgid "page.donate.bonus_downloads.period"
msgstr "يسري هذا طيلة فترة انضمامك."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s تنزيل سريع في اليوم"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "إذا تبرعت هذا الشهر!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / الشهر"

msgid "page.donate.buttons.join"
msgstr "انضم"

msgid "page.donate.buttons.selected"
msgstr "محددة"

msgid "page.donate.buttons.up_to_discounts"
msgstr "تخفيضات حتّى %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "مقالات <strong>كثيرة غير محصورة</strong> من مجمع البيانات العلمية (SciDB) ودون تحقيق"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>الوصول إلى JSON API</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "اكسب <strong>%(percentage)s%% تنزيلات إضافية</strong> عن طريق <a %(a_refer)s>إحالة الأصدقاء</a>."

msgid "page.donate.perks.credits"
msgstr "ذكر اسمك المُستخدم أو بدونه -تستُّرًا عليك- في قائمة الشكر والتقدير"

msgid "page.donate.perks.previous_plus"
msgstr "تشمل المزايا السابقة، إضافة إلى:"

msgid "page.donate.perks.early_access"
msgstr "وصول مبكر للمزايا الجديدة"

msgid "page.donate.perks.exclusive_telegram"
msgstr "دخول حصّري لمجموعة تلگرام بها تحديثاتٌ سريّة"

msgid "page.donate.perks.adopt"
msgstr "«توقيع ملف تورنت»: اسمك المستخدم أو رسالةٌ تكتبها على ملف تورنت <div %(div_months)s>مرّة كلّ 12 شهرًا من انضمامك</div>"

msgid "page.donate.perks.legendary"
msgstr "منزلة عظيمة وفضل في حفظ علوم و آداب البشرية"

msgid "page.donate.expert.title"
msgstr "لأصحاب الخبرة"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "اتصل بنا"

msgid "page.donate.small_team"
msgstr "لقلّتنا قد نتأخر في الرد عليكم لأسبوع أو اثنين."

msgid "page.donate.expert.unlimited_access"
msgstr "تنزيل سريعٌ و<strong>غير محدود</strong>"

msgid "page.donate.expert.direct_sftp"
msgstr "خوادم مباشرة <strong>لبروتكول النقل الآمن للملفات (SFTP)</strong>"

msgid "page.donate.expert.enterprise_donation"
msgstr "تبرعٌ كتبرعِ المؤسسات، أو مقابل مجموعات جديدة (كملفات جديدة ممسوحة ضوئيًا، أو مجمع بيانات معرّفٌ بصريًا على المحارف (OCR))."

msgid "page.donate.header.large_donations_wealthy"
msgstr "نقبل التبرّعات الضخمة من الأثرياء والمؤسسات. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "للتبرّعات التي تزيد عن 5000 دولارًا، راسلنا رجاءً على t %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "يرجى ملاحظة أنه على الرغم من أن العضويات في هذه الصفحة هي \"شهريًا\"، إلا أنها تبرعات لمرة واحدة (غير متكررة). انظر <a %(faq)s>الأسئلة المتكررة عن التبرع</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "إذا كنت ترغب في تقديم تبرع (أي مبلغ) دون عضوية، لا تتردد في استخدام عنوان Monero (XMR) هذا: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "من فضلك اختر خيارًا للدفع."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(غير متاح مؤقتا)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s بطاقة هدية"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "بطاقة بنكية (باستخدام التطبيق)"

msgid "page.donate.payment.buttons.crypto"
msgstr "العملات المعماة %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "تطبيق الدفع (Cash App)"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "بطاقة ائتمانية/السحب المباشر"

msgid "page.donate.payment.buttons.paypal"
msgstr "بَيْ بَالْ الولايات المتحدة (PayPal) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "بَيْ بَالْ (عادي)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "بطاقة / بَيْ بَالْ / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "بطاقة ائتمان/خصم/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "علي للدفع (Alipay)"

msgid "page.donate.payment.buttons.pix"
msgstr "بيكس البرازيل (Pix)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "بَيْ بَالْ (PayPal)"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "بطاقة بنكية"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "بطاقة ائتمان/خصم (احتياطية)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "بطاقة ائتمانية/السحب المباشر 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "بينانس"

msgid "page.donate.payment.buttons.wechat"
msgstr "وِتشاتْ (WeChat)"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "علي للدفع (Alipay) / وِتشاتْ (WeChat)"

msgid "page.donate.payment.desc.crypto"
msgstr "والتبرع بالعملات المعماة يكون بـ BTC أو ETH أو XMR أو SOL. لا تختر هذا الخيار إلا إن كنت تعلمها ومعتاد بالدفع بها."

msgid "page.donate.payment.desc.crypto2"
msgstr "والتبرع بالعملات المعماة يكون بـ BTC أو ETH أو XMR أو SOL وغيرها الكثير."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "إذا كنت تستخدم العملات المشفرة لأول مرة، نقترح استخدام %(options)s لشراء والتبرع بعملة البيتكوين (أول وأشهر عملة مشفرة)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "بينانس"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "سنستخدم بَيْ بَالْ المعماة إن اخترت بَيْ بَالْ؛ ذلك لأنّنا نُجهلُ بها فلا نُعرف. فنشكر لك سعيك في تعلم التبرع بهذه الطريقة لنحفظ سرنا."

msgid "page.donate.payment.desc.paypal_short"
msgstr "تبرع بـ بَيْ بَالْ (PayPal)."

msgid "page.donate.payment.desc.cashapp"
msgstr "تبرع بتطبيق الدفع (Cash App)."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "إن كان عندك تطبيق الدفع (Cash App) فهو أسهل طرق التبرع!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "ولتنتبه للمعاملات التي تقل عن %(amount)s، فقد يفرض تطبيق الدفع (Cash App) عمولة بـ%(fee)s. وما فات %(amount)s فدون عمولة!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "تبرع باستخدام Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "إذا كان لديك Revolut، فهذا هو أسهل طريقة للتبرع!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "تبرع ببطاقة الائتمان أو السحب المباشر."

msgid "page.donate.payment.desc.google_apple"
msgstr "قد يعمل الدفع بقوقل (Google Pay) والدفع بآبل (Apple Pay) أيضًا."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "ولتنتبه فإن التبرعات الصغيرة ببطاقة الائتمان قد تُلغي تخفيضنا هذا %(discount)s%% ؛ لذا ننصح باشتراكٍ طويل المدة."

msgid "page.donate.payment.desc.longer_subs"
msgstr "لتنتبه أن التبرعات الصغيرة عمولتها أكبر، فننصح باشتراك طويل الفترة."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "مع Binance، يمكنك شراء البيتكوين باستخدام بطاقة الائتمان/الخصم أو الحساب البنكي، ثم التبرع بتلك البيتكوين لنا. بهذه الطريقة يمكننا البقاء آمنين ومجهولين عند قبول تبرعك."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "باينانس متاح في كل بلد تقريبًا، ويدعم معظم البنوك وبطاقات الائتمان/الخصم. هذه هي توصيتنا الرئيسية حاليًا. نحن نقدر أنك تأخذ الوقت لتعلم كيفية التبرع باستخدام هذه الطريقة، لأنها تساعدنا كثيرًا."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "تبرع باستخدام حساب بَيْ بَالْ العادي الخاص بك."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "تبرع باستخدام بطاقة الائتمان/الخصم، بَيْ بَالْ، أو Venmo. يمكنك الاختيار بين هذه في الصفحة التالية."

msgid "page.donate.payment.desc.amazon"
msgstr "تبرع ببطاقة آمازون مسبقة الدفع."

msgid "page.donate.payment.desc.amazon_round"
msgstr "لتنتبه إلى مبالغنا المقبولة من موزعينا، فإننا نُقاربها لها، وحدّها الإدنى %(minimum)s."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>هام</strong>: ندعم موقع Amazon.com فقط، وليس غيره من المواقع المتفرعة منه. فمثلا لا نقبل نطاقات .de وco.uk وca وغيرهم."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>هام:</strong> هذا الخيار مخصص لـ %(amazon)s. إذا كنت ترغب في استخدام موقع أمازون آخر، اختره أعلاه."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "تستخدم هذه الطريقة مزود عملات مشفرة كوسيط للتحويل. قد يكون هذا مربكًا بعض الشيء، لذا يرجى استخدام هذه الطريقة فقط إذا لم تعمل طرق الدفع الأخرى. كما أنها لا تعمل في جميع البلدان."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "تبرع باستخدام بطاقة ائتمان/خصم، من خلال تطبيق Alipay (سهل جدًا في الإعداد)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>قم بتثبيت تطبيق Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "قم بتثبيت تطبيق Alipay من <a %(a_app_store)s>متجر تطبيقات آبل</a> أو <a %(a_play_store)s>متجر جوجل بلاي</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "سجل باستخدام رقم هاتفك."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "لا توجد حاجة لمزيد من التفاصيل الشخصية."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>أضف بطاقة بنكية"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "مدعوم: Visa، MasterCard، JCB، Diners Club وDiscover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "انظر <a %(a_alipay)s>هذا الدليل</a> لمزيد من المعلومات."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "لا يمكننا دعم بطاقات الائتمان/الخصم مباشرةً، لأن البنوك لا تريد العمل معنا. ☹ ومع ذلك، هناك عدة طرق لاستخدام بطاقات الائتمان/الخصم على أي حال، باستخدام طرق دفع أخرى:"

msgid "page.donate.payment.buttons.amazon"
msgstr "بطاقة آمازون مسبقة الدفع"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "أرسل لنا بطاقات هدايا Amazon.com باستخدام بطاقتك الائتمانية/الخصم."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "يدعم Alipay بطاقات الائتمان/الخصم الدولية. انظر <a %(a_alipay)s>هذا الدليل</a> لمزيد من المعلومات."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "يدعم WeChat (Weixin Pay) بطاقات الائتمان/الخصم الدولية. في تطبيق WeChat، انتقل إلى \"Me => Services => Wallet => Add a Card\". إذا لم ترَ ذلك، فعّله باستخدام \"Me => Settings => General => Tools => Weixin Pay => Enable\"."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "يمكنك شراء العملات المشفرة باستخدام بطاقات الائتمان/الخصم."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "خدمات التشفير السريعة"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "الخدمات السريعة مريحة، لكنها تفرض رسومًا أعلى."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "يمكنك استخدام هذا بدلاً من تبادل العملات المشفرة إذا كنت ترغب في تقديم تبرع أكبر بسرعة ولا تمانع في دفع رسوم تتراوح بين 5-10 دولارات."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "تأكد من إرسال المبلغ الدقيق للعملات المشفرة المعروض في صفحة التبرع، وليس المبلغ بالدولار الأمريكي."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "وإلا سيتم خصم الرسوم ولن نتمكن من معالجة عضويتك تلقائيًا."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(الحد الأدنى: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(الحد الأدنى: %(minimum)s حسب البلد، لا حاجة للتحقق في المعاملة الأولى)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(الحد الأدنى: %(minimum)s، لا حاجة للتحقق في المعاملة الأولى)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(الحد الأدنى: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(الحد الأدنى: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(الحد الأدنى: %(minimum)s، لا حاجة للتحقق في المعاملة الأولى)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "إذا كانت أي من هذه المعلومات غير محدثة، يرجى مراسلتنا عبر البريد الإلكتروني لإعلامنا."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "بالنسبة لبطاقات الائتمان، بطاقات الخصم، Apple Pay، و Google Pay، نستخدم \"Buy Me a Coffee\" (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). في نظامهم، تعادل \"القهوة\" الواحدة 5 دولارات، لذا سيتم تقريب تبرعك إلى أقرب مضاعف لـ 5."

msgid "page.donate.duration.intro"
msgstr "اختر المدة التي ترغب بالاشتراك فيها."

msgid "page.donate.duration.1_mo"
msgstr "شهر واحد"

msgid "page.donate.duration.3_mo"
msgstr "3 أشهر"

msgid "page.donate.duration.6_mo"
msgstr "6 أشهر"

msgid "page.donate.duration.12_mo"
msgstr "12 شهرًا"

msgid "page.donate.duration.24_mo"
msgstr "24 شهرًا"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 شهرًا"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 شهرًا"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>بعد <span %(span_discount)s></span> خصومات</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "هذا الخيار لا يسمح بأقل من %(amount)s. فاختر خيار دفع آخر أو مدة غيرها رجاءً."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "تبرع"

msgid "page.donate.payment.maximum_method"
msgstr "هذا الخيار لا يسمح بأكثر من %(amount)s. فاختر خيار دفع آخر أو مدة غيرها رجاءً."

msgid "page.donate.login2"
msgstr "للانظمام <a %(a_login)s> ادخل أو سجّل</a>. وشكرًا لدعمك لنا!"

msgid "page.donate.payment.crypto_select"
msgstr "اختر العملة المعماة التي تُريد:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(أقل مبلغ ممكن)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(استخدم عند إرسال Ethereum من Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(تحذير: الحد الأدنى مرتفع)"

msgid "page.donate.submit.confirm"
msgstr "اضغط على زر التبرع لتأكيد التبرع."

msgid "page.donate.submit.button"
msgstr "تبرع <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "بمقدورك إلغاء التبرع عند آخر خطوة في الدفع."

msgid "page.donate.submit.success"
msgstr "✅ يُعاد توجيهك لصفحة التبرع…"

msgid "page.donate.submit.failure"
msgstr "❌ حدث خطأ ما. أعد تحميل الصفحة رجاءً وحاول مرة ثانية."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / شهر"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "لشهر واحد"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "لثلاثة أشهر"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "لستة أشهر"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "لاثنا عشرَ شهرًا"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "لأربعة وعشرين شهرًا"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "لمدة 48 شهرًا"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "لمدة 96 شهرًا"

msgid "page.donate.submit.button.label.1_mo"
msgstr "لشهر واحد «%(tier_name)s»"

msgid "page.donate.submit.button.label.3_mo"
msgstr "لثلاثة أشهر «%(tier_name)s»"

msgid "page.donate.submit.button.label.6_mo"
msgstr "لستة أشهر «%(tier_name)s»"

msgid "page.donate.submit.button.label.12_mo"
msgstr "لاثنا عشرَ شهرًا «%(tier_name)s»"

msgid "page.donate.submit.button.label.24_mo"
msgstr "لأربعة وعشرين شهرًا «%(tier_name)s»"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "لمدة 48 شهرًا “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "لمدة 96 شهرًا “%(tier_name)s”"

msgid "page.donation.title"
msgstr "تبرّع"

msgid "page.donation.header.date"
msgstr "التاريخ: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "المجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s في الشهر لـ%(duration)s شهرًا، ومعها %(discounts)s تخفيض)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "المجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s في الشهر لـ%(duration)s شهرًا)</span>"

msgid "page.donation.header.status"
msgstr "الحالة: <span %(span_label)s>%(label)s </span>"

msgid "page.donation.header.id"
msgstr "المعرّف: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "إلغي"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "أمتأكد من إلغاءك؟ لا تلغي إن دفعت."

msgid "page.donation.header.cancel.confirm.button"
msgstr "نعم متأكد، إلغي من فضلك"

msgid "page.donation.header.cancel.success"
msgstr "✅ أُلغيَ تبرعك."

msgid "page.donation.header.cancel.new_donation"
msgstr "قدم تبرعًا جديدًا"

msgid "page.donation.header.cancel.failure"
msgstr "❌ حدث خطأ ما. أعد تحميل الصفحة رجاءً وحاول مرة ثانية."

msgid "page.donation.header.reorder"
msgstr "دفع آخر"

msgid "page.donation.old_instructions.intro_paid"
msgstr "لقد دفعت. إن كنت ترغب في مراجعة إرشادات الدفع ، انقر هنا:"

msgid "page.donation.old_instructions.show_button"
msgstr "اظهر إرشادات الدفع القديمة"

msgid "page.donation.thank_you_donation"
msgstr "شكرًا لك على تبرعك!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "إذا لم تقم بذلك بالفعل، اكتب مفتاحك السري لتسجيل الدخول:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "وإلا قد يتم قفل حسابك!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "إرشادات الدفع قديمة الآن. إن كنت ترغب في تقديم تبرع آخر ، فاستخدم زر «دفع آخر» أعلاه."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>ملاحظة مهمة</strong> أسعار العملات المعماة غير ثابتة فتصل أحيانًا إلى 20%% في دقائق. ومع هذا فهي أقل الخيارات عمولةً، فغيرها من الخيارات تصل عمولتها حتى الـ 50-60%% من قيمة التبرع وذلك لأننا «مؤسسة ظل خيرية» على حسب قولهم . <u>إن أرسلت لنا إيصال الاستلام بالمبلغ الذي دفعته، سنضيف لحسابك نوع الانضمام الذي اخترته</u> (على أن تراسلنا بعد إصدار الإيصال بساعات قليلة). فشكرًا لك على تحمّل عناء دعمنا! ❤️"

msgid "page.donation.expired"
msgstr "انتهت صلاحية هذا التبرع. رجاءً إلغية وانشئ جديدًا."

msgid "page.donation.payment.crypto.top_header"
msgstr "ارشادات العملات المعماة"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s> 1 </span> حوّل لواحد من حساباتنا للعملات المعماة"

msgid "page.donation.payment.crypto.text1"
msgstr "تبرع بالمبلغ الإجمالي لـ%(total)s لأحد هذه العناوين:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s> 1 </span> اشترِ بِتكوين من بَيْ بَالْ"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "ابحث عن «Crypto» في تطبيق أو موقع بَيْ بَالْ. غالبًا تكون في «Finances»."

msgid "page.donation.payment.paypal.text3"
msgstr "اتبع الارشادات لشراء بِتكوين (BTC). واشتر المبلغ الذي تُريد التبرع به ،%(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s> 2 </span> حوّل البِتكوين لعنواننا"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "ابحث عن «Crypto» في تطبيق أو موقع بَيْ بَالْ. اضغط على «Transfer» %(transfer_icon)s، ثم «Send»."

msgid "page.donation.payment.paypal.text5"
msgstr "اكتب عنواننا للبِتكوين (BTC)، واتبع الإرشادات لإرسال تبرعك هذا %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "إرشادات البطاقة الائتمانية/السحب المباشر"

msgid "page.donation.credit_debit_card_our_page"
msgstr "التبرع ببطاقة ائتمانية/السحب المباشر"

msgid "page.donation.donate_on_this_page"
msgstr "تبرع بـ %(amount)s على هذه <a %(a_page)s>الصفحة</a>."

msgid "page.donation.stepbystep_below"
msgstr "راجع الدليل التفصيلي أدناه."

msgid "page.donation.status_header"
msgstr "الحالة:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "في انتظار التأكيد (حدّث الصفحة للتحقق)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "في انتظار التحويل (حدّث الصفحة للتحقق)…"

msgid "page.donation.time_left_header"
msgstr "الوقت المتبقي:"

msgid "page.donation.might_want_to_cancel"
msgstr "(لعلّك تلغي تبرعك وتبدأ واحدًا جديدًا)"

msgid "page.donation.reset_timer"
msgstr "انشئ تبرعًا جديدًا لتصفير المؤقت."

msgid "page.donation.refresh_status"
msgstr "حدّث الحالة"

msgid "page.donation.footer.issues_contact"
msgstr "إن صادفت مشكلة فراسلنا على %(email)s واكتب في رسالتك مفصّلًا مشكلتك وماحدث معك موضحًا إياها بصورة للشاشة -إن وجد-."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "إذا كنت قد دفعت بالفعل:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "أحيانًا قد يستغرق التأكيد ما يصل إلى 24 ساعة، لذا تأكد من تحديث هذه الصفحة (حتى لو انتهت صلاحيتها)."

#, fuzzy
msgid "page.donation.step1"
msgstr "١"

msgid "page.donation.buy_pyusd"
msgstr "شراء عملة PYUSD من بَيْ بَالْ"

msgid "page.donation.pyusd.instructions"
msgstr "اتبع الإرشادات لشراء عملة PYUSD (بَيْ بَالْ دولار أمريكي)."

msgid "page.donation.pyusd.more"
msgstr "اشترِ أكثر (نوصي بـ %(more)s ) من القيمة التي تتبرع بها (%(amount)s)، فيكون من ضمنها قيمة العمولة. والباقي يبقى عندك."

#, fuzzy
msgid "page.donation.step2"
msgstr "٢"

msgid "page.donation.pyusd.transfer"
msgstr "انتقل إلى صفحة «PYUSD» في تطبيق أو موقع بَيْ بَالْ . اضغط على «Transfer» %(icon)s، ثم «Send»."

msgid "page.donation.transfer_amount_to"
msgstr "حوّل %(amount)s إلى %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "شراء بيتكوين (BTC) على تطبيق Cash"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "اذهب إلى صفحة \"بيتكوين\" (BTC) في تطبيق Cash."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "اشترِ قليلاً أكثر (نوصي بـ %(more)s أكثر) من المبلغ الذي تتبرع به (%(amount)s)، لتغطية رسوم المعاملات. ستحتفظ بأي مبلغ متبقي."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "حوّل البيتكوين إلى عنواننا"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "اضغط على زر \"إرسال البيتكوين\" لإجراء \"سحب\". قم بالتبديل من الدولار إلى BTC بالضغط على أيقونة %(icon)s. أدخل مبلغ BTC أدناه واضغط على \"إرسال\". شاهد <a %(help_video)s>هذا الفيديو</a> إذا واجهت مشكلة."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "للتبرعات الصغيرة (أقل من ٢٥ دولارًا)، قد تحتاج إلى استخدام Rush أو Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "شراء بيتكوين (BTC) على Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "اذهب إلى صفحة \"Crypto\" في Revolut لشراء بيتكوين (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "اشترِ قليلاً أكثر (نوصي بـ %(more)s أكثر) من المبلغ الذي تتبرع به (%(amount)s)، لتغطية رسوم المعاملات. ستحتفظ بأي مبلغ متبقي."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "حوّل البيتكوين إلى عنواننا"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "اضغط على زر \"إرسال البيتكوين\" لإجراء \"سحب\". قم بالتبديل من اليورو إلى BTC بالضغط على أيقونة %(icon)s. أدخل مبلغ BTC أدناه واضغط على \"إرسال\". شاهد <a %(help_video)s>هذا الفيديو</a> إذا واجهت مشكلة."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "تأكد من استخدام مبلغ BTC أدناه، <em>وليس</em> اليورو أو الدولار، وإلا فلن نتلقى المبلغ الصحيح ولن نتمكن من تأكيد عضويتك تلقائيًا."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "للتبرعات الصغيرة (أقل من ٢٥ دولارًا) قد تحتاج إلى استخدام Rush أو Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "استخدم أيًا من خدمات \"بطاقة الائتمان إلى بيتكوين\" السريعة التالية، والتي تستغرق بضع دقائق فقط:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "املأ التفاصيل التالية في النموذج:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "مبلغ BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "يرجى استخدام <span %(underline)s>هذا المبلغ المحدد</span>. قد يكون إجمالي التكلفة أعلى بسبب رسوم بطاقة الائتمان. للأسف، قد يكون هذا أكثر من خصمنا للمبالغ الصغيرة."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "عنوان BTC / Bitcoin (محفظة خارجية):"

msgid "page.donation.crypto_instructions"
msgstr "إرشادات %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "ندعم إصدارًا واحدًا فقط (standard) من العملات المعماة. قد يستغرق تأكيد الحوالة ساعة من الزمن، وهذا على حسب العملة."

msgid "page.donation.crypto_qr_code_title"
msgstr "ﻊﻓﺪﻠﻟ ﺔﻌﻳﺮﺴﻟﺍ ﺔﺑﺎﺠﺘﺳﻻ﻿ﺍ ﺰﻣﺭ ﺢﺴﻣ"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "ﺔﻋﺮﺴﺑ ﻊﻓﺪﻟﺍ ﻞﻴﺻﺎﻔﺗ ءﻞﻤﻟ ﺮﻴﻔﺸﺘﻟﺍ ﺔﻈﻔﺤﻣ ﻖﻴﺒﻄﺗ ﻡﺍﺪﺨﺘﺳﺎﺑ ﺍﺬﻫ ﺔﻌﻳﺮﺴﻟﺍ ﺔﺑﺎﺠﺘﺳﻻ﻿ﺍ ﺰﻣﺭ "

msgid "page.donation.amazon.header"
msgstr "بطاقة آمازون مسبقة الدفع"

msgid "page.donation.amazon.form_instructions"
msgstr "يرجى ملئ <a %(a_form)s>استمارة Amazon.com الرسمية</a> لإرسال بطاقة دفع مسبق بـ %(amount)s إلى عنوان البريد الرقمي أدناه."

msgid "page.donation.amazon.only_official"
msgstr "لا نقبل ببطاقات الدفع المسبق <strong>إلا من الاستمارة الرسمية من Amazon.com</strong>. وإن لم تُرسل بهذه الاستمارة فلا يُمكننا ردُّها."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "أدخل المبلغ الدقيق: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "من فضلك لا تكتب رسالتك الخاصة."

msgid "page.donation.amazon.form_to"
msgstr "اكتب في «To» هذا البريد:"

msgid "page.donation.amazon.unique"
msgstr "خاصٌّ بحسابك فلا تُشاركه."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "استخدم مرة واحدة فقط."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "في انتظار البطاقة... (حدّث الصفحة للتحقق)"

msgid "page.donation.amazon.confirm_automated"
msgstr "سيتحقق نظامنا تلقائيًا من البطاقة التي أرسلتها. وإن فشلت العملية، فحاول إعادة إرسال البطاقة (<a %(a_instr)s>الإرشادات</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "إذا لم ينجح ذلك، فراسلنا على بريدنا الرقمي وستُراجع آنا الطلب والبطاقة بنفسها (قد يستغرق هذا بضعة أيام)، واذكر في رسالتك إن حاولت إعادة الإرسال."

msgid "page.donation.amazon.example"
msgstr "مثال:"

msgid "page.donate.strange_account"
msgstr "لاحظ أن اسم الحساب أو الصورة قد تبدو غريبة. لا تقلق! هذه الحسابات تٌدار من شركاء التبرع. حساباتنا لم تُخترق."

msgid "page.donation.payment.alipay.top_header"
msgstr "إرشادات علي للدفع (Alipay)"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s> 1 </span> تبرع من علي للدفع (Alipay)"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "تبرع بالمبلغ الإجمالي %(total)s باستخدام <a %(a_account)s>حساب Alipay هذا</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "إذا تم حظر صفحة التبرع، جرب اتصال إنترنت مختلف (مثل VPN أو إنترنت الهاتف)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "للأسف، صفحة Alipay غالبًا ما تكون متاحة فقط من <strong>البر الرئيسي للصين</strong>. قد تحتاج إلى تعطيل VPN مؤقتًا، أو استخدام VPN إلى البر الرئيسي للصين (أو هونغ كونغ يعمل أحيانًا أيضًا)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>قم بالتبرع (امسح رمز QR أو اضغط على الزر)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "افتح <a %(a_href)s>صفحة التبرع برمز QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "امسح رمز QR باستخدام تطبيق Alipay، أو اضغط على الزر لفتح تطبيق Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "يرجى التحلي بالصبر؛ قد تستغرق الصفحة بعض الوقت للتحميل لأنها في الصين."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "تعليمات WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>تبرع على WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "تبرع بالمبلغ الإجمالي %(total)s باستخدام <a %(a_account)s>هذا الحساب على WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "تعليمات Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s> 1 </span> تبرع من Pix"

msgid "page.donation.payment.pix.text1"
msgstr "تبرع إجمالي بـ%(total)s من <a %(a_account)s> حساب Pix هذا"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s </span> ارسل لنا الإيصال بالبريد الرقمي"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "أرسل إيصالاً أو لقطة شاشة إلى عنوان التحقق الشخصي الخاص بك. لا تستخدم هذا العنوان البريدي لتبرعك عبر بَيْ بَالْ."

msgid "page.donation.footer.text1"
msgstr "ارسل إيصالًا أو لقطة شاشة إلى عنوان التحققك الشخصي:"

msgid "page.donation.footer.crypto_note"
msgstr "إذا تذبذب سعر صرف العملة المعماة أثناء المعاملة ، فتأكد من تضمين الإيصال الذي يوضح سعر الصرف الحقيقي. نشكر لك تحمّل عناء دعمنا بالعملات المعماة ، فهي تساعدنا كثيرًا!"

msgid "page.donation.footer.text2"
msgstr "اضغط على هذا الزر عند إرسالك الإيصال بالبريد الرقمي حتى تُراجعه آنّا بنفسها (قد تمتد المراجعة أيامًا):"

msgid "page.donation.footer.button"
msgstr "أجل، أرسلتُ الإيصال"

msgid "page.donation.footer.success"
msgstr "✅ شكرًا لتبرعك! ستُنشّط آنّا حسابك بانضمامٍ يُناسب تبرعك في أيامٍ قليلة."

msgid "page.donation.footer.failure"
msgstr "❌ حدث خطأ ما. أعد تحميل الصفحة وحاول مرة أخرى."

msgid "page.donation.stepbystep"
msgstr "إرشاد خطوة بخطوة"

msgid "page.donation.crypto_dont_worry"
msgstr "تشير بعض الخطوات إلى محافظ العملات المعماة، فلا تقلق، لا يتطلب منك معرفة مسبقة بها."

msgid "page.donation.hoodpay.step1"
msgstr "1. اكتب بريدك الرقمي."

msgid "page.donation.hoodpay.step2"
msgstr "2. اختر خيارًا يُناسبك للدفع."

msgid "page.donation.hoodpay.step3"
msgstr "3. اختر خيارًا يُناسبك للدفع مرة أخرى."

msgid "page.donation.hoodpay.step4"
msgstr "4. حدد محفظةً باستضافة داخلية."

msgid "page.donation.hoodpay.step5"
msgstr "5. انقر على «أؤكد الملكية»."

msgid "page.donation.hoodpay.step6"
msgstr "6. من المفترض أن إيصالًا قد وصلك على بريدك الرقمي. ارسله لنا، وبه سنُؤكد تبرعك في أقرب وقت ممكن."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "يرجى الانتظار على الأقل <span %(span_hours)s>24 ساعة</span> (وتحديث هذه الصفحة) قبل الاتصال بنا."

msgid "page.donate.mistake"
msgstr "لا يُمكننا ردّ الأموال إن أخطأت بشيء في الدفع، ولكننا سنُصحح ما يُمكن ونسعى لإرضاءك."

msgid "page.my_donations.title"
msgstr "تبرعاتي"

msgid "page.my_donations.not_shown"
msgstr "لا تُعرض تفاصيل التبرعات للعامة."

msgid "page.my_donations.no_donations"
msgstr "لا تبرعات حتى اللحظة. <a %(a_donate)s> تبرّع الآن لأول مرة. </a>"

msgid "page.my_donations.make_another"
msgstr "تبرّعٌ آخر."

msgid "page.downloaded.title"
msgstr "الملفات المُنزّلة"

msgid "page.downloaded.fast_partner_star"
msgstr "تُميّز التنزيلات من الخوادم السريعة بـ%(icon)s."

msgid "page.downloaded.twice"
msgstr "إن نزّلت ملفًا بتنزيل سريع وبطيئ، فسيظهر مرتين."

msgid "page.downloaded.fast_download_time"
msgstr "تُحتسب التنزيلات السريعة خلال آخر 24 ساعة ضمن الحد اليومي."

msgid "page.downloaded.times_utc"
msgstr "جميع الأوقات بالتوقيت العالمي الموحّد (UTC)."

msgid "page.downloaded.not_public"
msgstr "لا تُعرض الملفات المُنزّلة علنًا."

msgid "page.downloaded.no_files"
msgstr "لم تُنزّل أي ملفات حتى الآن."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "آخر 18 ساعة"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "سابقاً"

msgid "page.account.logged_in.title"
msgstr "الحساب"

msgid "page.account.logged_out.title"
msgstr "دخول / تسجيل"

msgid "page.account.logged_in.account_id"
msgstr "معرّف الحساب: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "ملفّك العامة: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "المفتاح السري (لا تشاركه!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "عرض"

msgid "page.account.logged_in.membership_has_some"
msgstr "نوع الانضمام: <strong>%(tier_name)s </strong> حتى%(until_date)s <a %(a_extend)s> (مدّها) </a>"

msgid "page.account.logged_in.membership_none"
msgstr "نوع الانضمام: <strong> لا يوجد </ strong> <a %(a_become)s> (انضم) </a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "استخدام التنزيل السريع (آخر 24 ساعة): <strong>%(used)s %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "أي تنزيل؟"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "مجموعة التلگرام الحصرية: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "انضم معنا!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "اشترك في <a %(a_tier)sفئة أعلى</a> للإنضمام لمجموعتنا."

msgid "page.account.logged_in.membership_upgrade"
msgstr "تواصل مع آنّا بـ %(email)s إن كنت مهتمًا للإشتراك في فئة أعلى."

#, fuzzy
msgid "page.contact.title"
msgstr "البريد الإلكتروني للتواصل"

msgid "page.account.logged_in.membership_multiple"
msgstr "يمكنك الجمع بين عضويات متعددة (ستُضاف التنزيلات السريعة لكل 24 ساعة معًا)."

msgid "layout.index.header.nav.public_profile"
msgstr "الملف الشخصي العام"

msgid "layout.index.header.nav.downloaded_files"
msgstr "الملفات المُنزّلة"

msgid "layout.index.header.nav.my_donations"
msgstr "تبرعاتي"

msgid "page.account.logged_in.logout.button"
msgstr "اخرج"

msgid "page.account.logged_in.logout.success"
msgstr "✅ خرجت. أعد تحميل الصفحة للدخول مرة أخرى."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ حدث خطأ ما. يرجى إعادة تحميل الصفحة وحاول مرة أخرى."

msgid "page.account.logged_out.registered.text1"
msgstr "نجح التسجيل! مفتاحك السري هو: <span %(span_key)s>%(key)s </span>"

msgid "page.account.logged_out.registered.text2"
msgstr "احفظ هذا المفتاح ولا تفقده. فإن فقدته فلن تقدر على الدخول لحسابك."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s> <strong> علّمها. </ strong> علّم هذه الصفحة لاسترداد مفتاحك. </li> <li %(li_item)s> <strong> نزّله </strong> انقر على <a %(a_download)s> هذا الرابط </a> لتنزيل مفتاحك. </li> <li %(li_item)s> <strong>احفظه في حافظة </ strong> استخدم حافظة كلمات المرور لحفظ مفتاحك. </li>"

msgid "page.account.logged_out.key_form.text"
msgstr "اكتب مفتاحك السري للدخول:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "المفتاح السري"

msgid "page.account.logged_out.key_form.button"
msgstr "ادخل"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "مفتاح سري غير صحيح. تحقق من مفتاحك وحاول مرة أخرى، أو سجّل حسابًا جديدًا بالأسفل."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "لا تفقد مفتاحك!"

msgid "page.account.logged_out.register.header"
msgstr "ألا تملك حسابًا حتى الآن؟"

msgid "page.account.logged_out.register.button"
msgstr "سجّل حسابًا جديدًا"

msgid "page.login.lost_key"
msgstr "إن فقدت مفتاحك <a %(a_contact)s>فراسلنا</a> واكتب ما عندك من معلومات وأدلّة."

msgid "page.login.lost_key_contact"
msgstr "قد يتحتم عليك إنشاء حساب جديد -غير دائم- للتواصل معنا."

msgid "page.account.logged_out.old_email.button"
msgstr "حساب ببريد رقميّ قديم؟ اكتب <a %(a_open)s> بريدك الرقمي هنا </a>."

msgid "page.list.title"
msgstr "قائمة"

msgid "page.list.header.edit.link"
msgstr "عدّل"

msgid "page.list.edit.button"
msgstr "احفظ"

msgid "page.list.edit.success"
msgstr "✅ حُفظ. رجاء أعد تحميل الصفحة."

msgid "page.list.edit.failure"
msgstr "❌ حدث خطأ ما. حاول مرة اخرى."

msgid "page.list.by_and_date"
msgstr "رتّبها %(by)s، بوقت إنشاءها <span %(span_time)s>%(time)s </span>"

msgid "page.list.empty"
msgstr "القائمة فارغة."

msgid "page.list.new_item"
msgstr "أضف أو أزل من هذه القائمة بالبحث عن ملف وفتح تبويبة «القوائم»."

msgid "page.profile.title"
msgstr "الملف الشخصي"

msgid "page.profile.not_found"
msgstr "لا وجود للملف."

msgid "page.profile.header.edit"
msgstr "عدّل"

msgid "page.profile.change_display_name.text"
msgstr "غيّر اسمك المعروض. لا يُمكنك تغيير معرّفك (ما يلي «#»)."

msgid "page.profile.change_display_name.button"
msgstr "احفظ"

msgid "page.profile.change_display_name.success"
msgstr "✅ حُفظ. رجاء أعد تحميل الصفحة."

msgid "page.profile.change_display_name.failure"
msgstr "❌ حدث خطأ ما. حاول مرة اخرى."

msgid "page.profile.created_time"
msgstr "أُنشئ الملف <span %(span_time)s>%(time)s </span>"

msgid "page.profile.lists.header"
msgstr "القوائم"

msgid "page.profile.lists.no_lists"
msgstr "لا قوائم حتى الآن"

msgid "page.profile.lists.new_list"
msgstr "انشئ قائمةً جديدة بالبحث عن ملف وفتح تبويبة «القوائم»."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "إصلاح حقوق الطبع والنشر ضروري للأمن القومي"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "ملخص: تم تدريب LLMs الصينية (بما في ذلك DeepSeek) على أرشيفي غير القانوني من الكتب والأوراق — الأكبر في العالم. يحتاج الغرب إلى إصلاح قانون حقوق الطبع والنشر كمسألة أمن قومي."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "مقالات مرافقة من TorrentFreak: <a %(torrentfreak)s>الأولى</a>، <a %(torrentfreak_2)s>الثانية</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "لم يمض وقت طويل، كانت \"مكتبات الظل\" تحتضر. توقفت Sci-Hub، الأرشيف الضخم غير القانوني للأوراق الأكاديمية، عن استقبال أعمال جديدة بسبب الدعاوى القضائية. شهدت \"مكتبة الزّاي\"، أكبر مكتبة غير قانونية للكتب، اعتقال من يُزعم أنهم منشئوها بتهم انتهاك حقوق الطبع والنشر. لقد تمكنوا بشكل لا يصدق من الهروب من اعتقالهم، لكن مكتبتهم لا تزال مهددة."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "عندما واجهت مكتبة الزّاي الإغلاق، كنت قد قمت بالفعل بنسخ مكتبتها بالكامل وكنت أبحث عن منصة لاستضافتها. كان ذلك دافعي لبدء رَبيدةُ آنّا: استمرار للمهمة وراء تلك المبادرات السابقة. لقد نمونا منذ ذلك الحين لنصبح أكبر مكتبة ظل في العالم، حيث نستضيف أكثر من 140 مليون نص محمي بحقوق الطبع والنشر عبر العديد من الصيغ — كتب، أوراق أكاديمية، مجلات، صحف، وما إلى ذلك."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "فريقي وأنا أيديولوجيون. نعتقد أن الحفاظ على هذه الملفات واستضافتها هو أمر صحيح أخلاقياً. المكتبات حول العالم تشهد تخفيضات في التمويل، ولا يمكننا الوثوق بتراث البشرية للشركات أيضاً."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "ثم جاء الذكاء الاصطناعي. تواصلت معنا جميع الشركات الكبرى تقريباً التي تبني LLMs للتدريب على بياناتنا. أعادت معظم الشركات الأمريكية (لكن ليس كلها!) النظر بمجرد أن أدركت الطبيعة غير القانونية لعملنا. على النقيض من ذلك، تبنت الشركات الصينية مجموعتنا بحماس، دون أن تزعجها قانونيتها. وهذا ملحوظ بالنظر إلى دور الصين كدولة موقعة على جميع المعاهدات الدولية الرئيسية لحقوق الطبع والنشر تقريباً."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "لقد قدمنا وصولاً عالي السرعة إلى حوالي 30 شركة. معظمها شركات LLM، وبعضها وسطاء بيانات، سيعيدون بيع مجموعتنا. معظمها صينية، رغم أننا عملنا أيضاً مع شركات من الولايات المتحدة وأوروبا وروسيا وكوريا الجنوبية واليابان. اعترفت DeepSeek <a %(arxiv)s>بأن</a> نسخة سابقة تم تدريبها على جزء من مجموعتنا، رغم أنهم متكتمون بشأن نموذجهم الأخير (ربما تم تدريبه أيضاً على بياناتنا)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "إذا أراد الغرب البقاء في المقدمة في سباق LLMs، وفي النهاية، AGI، فإنه يحتاج إلى إعادة النظر في موقفه من حقوق الطبع والنشر، وبسرعة. سواء كنت توافق معنا أو لا على قضيتنا الأخلاقية، فإن هذا الآن يصبح قضية اقتصادية، وحتى قضية أمن قومي. جميع الكتل القوية تبني علماء خارقين اصطناعيين، ومخترقين خارقين، وجيوش خارقة. حرية المعلومات تصبح مسألة بقاء لهذه الدول — حتى مسألة أمن قومي."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "فريقنا من جميع أنحاء العالم، وليس لدينا انحياز معين. لكننا نشجع الدول ذات قوانين حقوق الطبع والنشر القوية على استخدام هذا التهديد الوجودي لإصلاحها. فما العمل؟"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "توصيتنا الأولى بسيطة: تقصير مدة حقوق الطبع والنشر. في الولايات المتحدة، تُمنح حقوق الطبع والنشر لمدة 70 عاماً بعد وفاة المؤلف. هذا أمر سخيف. يمكننا مواءمة هذا مع براءات الاختراع، التي تُمنح لمدة 20 عاماً بعد التقديم. يجب أن يكون هذا أكثر من كافٍ للمؤلفين للكتب والأوراق والموسيقى والفن والأعمال الإبداعية الأخرى للحصول على تعويض كامل عن جهودهم (بما في ذلك المشاريع طويلة الأجل مثل تكييف الأفلام)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "ثم، على الأقل، يجب على صانعي السياسات تضمين استثناءات للحفاظ الجماعي ونشر النصوص. إذا كان فقدان الإيرادات من العملاء الفرديين هو القلق الرئيسي، يمكن أن يظل التوزيع على المستوى الشخصي محظوراً. في المقابل، سيتم تغطية أولئك القادرين على إدارة مستودعات ضخمة — الشركات التي تدرب LLMs، إلى جانب المكتبات والأرشيفات الأخرى — بهذه الاستثناءات."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "بعض الدول تقوم بالفعل بنسخة من هذا. أفادت TorrentFreak <a %(torrentfreak)s>بأن</a> الصين واليابان قد أدخلتا استثناءات للذكاء الاصطناعي في قوانين حقوق الطبع والنشر الخاصة بهما. من غير الواضح لنا كيف يتفاعل هذا مع المعاهدات الدولية، لكنه بالتأكيد يوفر غطاءً لشركاتهم المحلية، مما يفسر ما كنا نراه."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "أما بالنسبة لرَبيدةُ آنّا — سنواصل عملنا السري المتجذر في القناعة الأخلاقية. ومع ذلك، فإن أكبر أمنياتنا هي الدخول إلى النور، وتضخيم تأثيرنا بشكل قانوني. يرجى إصلاح حقوق الطبع والنشر."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "اقرأ المقالات المرافقة من TorrentFreak: <a %(torrentfreak)s>الأولى</a>، <a %(torrentfreak_2)s>الثانية</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "الفائزون بجائزة تصور ISBN بقيمة 10,000 دولار"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "ملخص: حصلنا على بعض المشاركات الرائعة لجائزة تصور ISBN بقيمة 10,000 دولار."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "قبل بضعة أشهر، أعلنا عن <a %(all_isbns)s>جائزة بقيمة 10,000 دولار</a> لإنشاء أفضل تصور ممكن لبياناتنا التي تعرض مساحة ISBN. أكدنا على إظهار الملفات التي قمنا بأرشفتها والتي لم نقم بأرشفتها بعد، وقدمنا لاحقاً مجموعة بيانات تصف عدد المكتبات التي تحتفظ بـ ISBNs (كمقياس للندرة)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "لقد غمرتنا الردود. كان هناك الكثير من الإبداع. شكر كبير لكل من شارك: طاقتك وحماسك معديان!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "في النهاية، أردنا الإجابة على الأسئلة التالية: <strong>ما هي الكتب الموجودة في العالم، كم منها قمنا بأرشفتها بالفعل، وعلى أي الكتب يجب أن نركز بعد ذلك؟</strong> من الرائع أن نرى الكثير من الناس يهتمون بهذه الأسئلة."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "بدأنا بتصور أساسي بأنفسنا. في أقل من 300 كيلوبايت، تمثل هذه الصورة بشكل موجز أكبر \"قائمة كتب\" مفتوحة بالكامل تم تجميعها في تاريخ البشرية:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "جميع أرقام ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "الملفات في رَبيدةُ آنّا"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "أرقام SSNO في CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "تسريب بيانات CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "أرقام SSID في DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "فهرس الكتب الإلكترونية في EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "كتب جوجل"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "جودريدز"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "أرشيف الإنترنت"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "السجل العالمي للناشرين ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "ليبي"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "المكتبة المفتوحة"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "المكتبة الوطنية الروسية"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "المكتبة الإمبراطورية في ترانتور"

#, fuzzy
msgid "common.back"
msgstr "رجوع"

#, fuzzy
msgid "common.forward"
msgstr "تقدم"

#, fuzzy
msgid "common.last"
msgstr "الأخير"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "يرجى الاطلاع على <a %(all_isbns)s>المنشور الأصلي في المدونة</a> لمزيد من المعلومات."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "قمنا بتحدي لتحسين هذا. كنا سنمنح جائزة المركز الأول بقيمة 6,000 دولار، والمركز الثاني بقيمة 3,000 دولار، والمركز الثالث بقيمة 1,000 دولار. نظرًا للاستجابة الهائلة والتقديمات الرائعة، قررنا زيادة مجموع الجوائز قليلاً، ومنح جائزة المركز الثالث لأربعة فائزين بقيمة 500 دولار لكل منهم. الفائزون مذكورون أدناه، ولكن تأكد من الاطلاع على جميع التقديمات <a %(annas_archive)s>هنا</a>، أو تحميل <a %(a_2025_01_isbn_visualization_files)s>التورنت المجمع</a> الخاص بنا."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "المركز الأول 6,000 دولار: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "هذا <a %(phiresky_github)s>التقديم</a> (<a %(annas_archive_note_2951)s>تعليق Gitlab</a>) هو ببساطة كل ما أردناه، وأكثر! أحببنا بشكل خاص خيارات التصور المرنة بشكل لا يصدق (حتى دعم المظللات المخصصة)، ولكن مع قائمة شاملة من الإعدادات المسبقة. كما أحببنا مدى سرعة وسلاسة كل شيء، والتنفيذ البسيط (الذي لا يحتوي حتى على خلفية)، والخريطة المصغرة الذكية، والشرح الواسع في <a %(phiresky_github)s>منشور المدونة</a> الخاص بهم. عمل رائع، والفائز المستحق!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "المركز الثاني 3,000 دولار: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "تقديم رائع آخر <a %(annas_archive_note_2913)s>تقديم</a>. ليس مرنًا مثل المركز الأول، لكننا فضلنا بالفعل تصوره على المستوى الكلي على المركز الأول (منحنى ملء الفضاء، الحدود، التسمية، التمييز، التحريك، والتكبير). تعليق <a %(annas_archive_note_2971)s>تعليق</a> من جو ديفيس تردد صداه لدينا:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "\"بينما المربعات والمستطيلات المثالية مرضية رياضيًا، إلا أنها لا توفر تفوقًا في السياق الخريطي. أعتقد أن عدم التماثل المتأصل في هذه الأشكال مثل هيلبرت أو مورتون الكلاسيكي ليس عيبًا بل ميزة. تمامًا مثل الشكل الشهير لإيطاليا الذي يشبه الحذاء يجعلها معروفة على الفور على الخريطة، قد تخدم \"الخصائص الفريدة\" لهذه المنحنيات كمعالم معرفية. يمكن أن تعزز هذه الخصوصية الذاكرة المكانية وتساعد المستخدمين على توجيه أنفسهم، مما يجعل تحديد المناطق المحددة أو ملاحظة الأنماط أسهل.\""

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "ولا يزال هناك الكثير من الخيارات للتصور والعرض، بالإضافة إلى واجهة مستخدم سلسة وبديهية بشكل لا يصدق. مركز ثانٍ قوي!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "المركز الثالث 500 دولار #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "في هذا <a %(annas_archive_note_2940)s>التقديم</a> أحببنا حقًا الأنواع المختلفة من العروض، خاصة عرض المقارنة وعرض الناشر."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "المركز الثالث 500 دولار #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "على الرغم من أن واجهة المستخدم ليست الأكثر صقلًا، إلا أن هذا <a %(annas_archive_note_2917)s>التقديم</a> يحقق الكثير من المتطلبات. أحببنا بشكل خاص ميزة المقارنة."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "المركز الثالث 500 دولار #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "مثل المركز الأول، أدهشنا هذا <a %(annas_archive_note_2975)s>التقديم</a> بمرونته. في النهاية، هذا ما يجعل أداة التصور رائعة: أقصى قدر من المرونة للمستخدمين المحترفين، مع الحفاظ على البساطة للمستخدمين العاديين."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "المركز الثالث 500 دولار #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "التقديم الأخير <a %(annas_archive_note_2947)s>التقديم</a> الذي حصل على جائزة هو بسيط جدًا، ولكنه يحتوي على بعض الميزات الفريدة التي أحببناها حقًا. أحببنا كيف يظهرون عدد مجموعات البيانات التي تغطي ISBN معين كمعيار للشعبية/الموثوقية. كما أحببنا بساطة وفعالية استخدام شريط التعتيم للمقارنات."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "أفكار ملحوظة"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "بعض الأفكار والتنفيذات الأخرى التي أحببناها بشكل خاص:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "ناطحات سحاب للندرة"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "إحصائيات مباشرة"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "التعليقات التوضيحية، وأيضًا الإحصائيات المباشرة"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "عرض خريطة فريد وفلاتر"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "نظام ألوان افتراضي رائع وخريطة حرارية."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "تبديل سهل لمجموعات البيانات للمقارنات السريعة."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "تسميات جميلة."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "شريط مقياس مع عدد الكتب."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "الكثير من المنزلقات لمقارنة مجموعات البيانات، كما لو كنت دي جي."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "يمكننا الاستمرار لفترة، لكن دعونا نتوقف هنا. تأكد من الاطلاع على جميع المشاركات <a %(annas_archive)s>هنا</a>، أو قم بتنزيل <a %(a_2025_01_isbn_visualization_files)s>التورنت المجمع</a> الخاص بنا. العديد من المشاركات، وكل واحدة تقدم منظورًا فريدًا، سواء في واجهة المستخدم أو التنفيذ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "سنقوم على الأقل بدمج المشاركة الفائزة بالمركز الأول في موقعنا الرئيسي، وربما بعض المشاركات الأخرى. لقد بدأنا أيضًا في التفكير في كيفية تنظيم عملية تحديد وتأكيد ثم أرشفة الكتب النادرة. المزيد قادم في هذا الصدد."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "شكرًا لكل من شارك. إنه لأمر مدهش أن يهتم الكثير من الناس."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "قلوبنا مليئة بالامتنان."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "تصور جميع أرقام ISBN — مكافأة قدرها 10,000 دولار بحلول 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "تمثل هذه الصورة أكبر \"قائمة كتب\" مفتوحة بالكامل تم تجميعها في تاريخ البشرية."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "هذه الصورة بحجم 1000×800 بكسل. كل بكسل يمثل 2,500 رقم ISBN. إذا كان لدينا ملف لرقم ISBN، نجعل ذلك البكسل أكثر خضرة. إذا كنا نعرف أن رقم ISBN قد تم إصداره، ولكن ليس لدينا ملف مطابق، نجعله أكثر احمرارًا."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "في أقل من 300 كيلوبايت، تمثل هذه الصورة بشكل موجز أكبر \"قائمة كتب\" مفتوحة بالكامل تم تجميعها في تاريخ البشرية (بضع مئات من الجيجابايت مضغوطة بالكامل)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "كما يظهر: هناك الكثير من العمل المتبقي في نسخ الكتب احتياطيًا (لدينا فقط 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "الخلفية"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "كيف يمكن لرَبيدةُ آنّا تحقيق مهمتها في نسخ جميع معارف البشرية احتياطيًا، دون معرفة الكتب التي لا تزال موجودة؟ نحن بحاجة إلى قائمة مهام. إحدى الطرق لتخطيط ذلك هي من خلال أرقام ISBN، التي تم تخصيصها لكل كتاب نُشر منذ السبعينيات (في معظم البلدان)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "لا توجد سلطة مركزية تعرف جميع تخصيصات ISBN. بدلاً من ذلك، هو نظام موزع، حيث تحصل البلدان على نطاقات من الأرقام، ثم تقوم بتخصيص نطاقات أصغر للناشرين الرئيسيين، الذين قد يقومون بتقسيم النطاقات إلى ناشرين أصغر. وأخيرًا يتم تخصيص الأرقام الفردية للكتب."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "بدأنا في تخطيط أرقام ISBN <a %(blog)s>منذ عامين</a> مع جمعنا لبيانات ISBNdb. منذ ذلك الحين، قمنا بجمع العديد من مصادر metadata الأخرى، مثل <a %(blog_2)s>Worldcat</a>، وGoogle Books، وGoodreads، وLibby، والمزيد. يمكن العثور على قائمة كاملة في صفحات \"Datasets\" و\"Torrents\" على رَبيدةُ آنّا. لدينا الآن أكبر مجموعة مفتوحة بالكامل وقابلة للتنزيل بسهولة من metadata الكتب (وبالتالي أرقام ISBN) في العالم."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "لقد <a %(blog)s>كتبنا بشكل موسع</a> عن سبب اهتمامنا بالحفظ، ولماذا نحن حاليًا في نافذة حرجة. يجب علينا الآن تحديد الكتب النادرة والمهمشة والفريدة المعرضة للخطر وحفظها. وجود metadata جيدة لجميع الكتب في العالم يساعد في ذلك."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "التصور"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "بجانب صورة النظرة العامة، يمكننا أيضًا النظر إلى مجموعات البيانات الفردية التي حصلنا عليها. استخدم القائمة المنسدلة والأزرار للتبديل بينها."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "هناك الكثير من الأنماط المثيرة للاهتمام لرؤيتها في هذه الصور. لماذا هناك بعض الانتظام في الخطوط والكتل، الذي يبدو أنه يحدث على مقاييس مختلفة؟ ما هي المناطق الفارغة؟ لماذا تكون بعض مجموعات البيانات متجمعة جدًا؟ سنترك هذه الأسئلة كتمرين للقارئ."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "مكافأة بقيمة 10,000 دولار"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "هناك الكثير لاستكشافه هنا، لذا نحن نعلن عن مكافأة لتحسين التصور أعلاه. على عكس معظم مكافآتنا، هذه المكافأة محددة بوقت. يجب عليك <a %(annas_archive)s>تقديم</a> كود المصدر المفتوح الخاص بك بحلول 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "أفضل تقديم سيحصل على 6,000 دولار، والمركز الثاني 3,000 دولار، والمركز الثالث 1,000 دولار. سيتم منح جميع المكافآت باستخدام Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "فيما يلي المعايير الدنيا. إذا لم يلبِ أي تقديم المعايير، قد نمنح بعض المكافآت، ولكن سيكون ذلك وفقًا لتقديرنا."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "قم بتفريع هذا المستودع، وقم بتحرير HTML لهذا المنشور في المدونة (لا يُسمح بأي خلفيات أخرى غير خلفيتنا Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "اجعل الصورة أعلاه قابلة للتكبير بسلاسة، بحيث يمكنك التكبير حتى أرقام ISBN الفردية. يجب أن يؤدي النقر على أرقام ISBN إلى نقلك إلى صفحة metadata أو البحث في رَبيدةُ آنّا."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "يجب أن تظل قادرًا على التبديل بين جميع مجموعات البيانات المختلفة."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "يجب تمييز نطاقات البلدان ونطاقات الناشرين عند التمرير. يمكنك استخدام مثلاً <a %(github_xlcnd_isbnlib)s>data4info.py في isbnlib</a> للحصول على معلومات عن البلدان، و\"isbngrp\" الخاص بنا لجمع الناشرين (<a %(annas_archive)s>dataset</a>، <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "يجب أن يعمل بشكل جيد على أجهزة الكمبيوتر المكتبية والهواتف المحمولة."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "للحصول على نقاط إضافية (هذه مجرد أفكار — دع إبداعك ينطلق بحرية):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "سيتم إعطاء اعتبار قوي للاستخدامية وكيفية المظهر الجيد."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "عرض metadata الفعلية لأرقام ISBN الفردية عند التكبير، مثل العنوان والمؤلف."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "منحنى ملء المساحة بشكل أفضل. مثلاً، زيغ-زاغ، يبدأ من 0 إلى 4 في الصف الأول ثم يعود (بالعكس) من 5 إلى 9 في الصف الثاني — يتم تطبيقه بشكل متكرر."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "مخططات ألوان مختلفة أو قابلة للتخصيص."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "عرض خاص لمقارنة Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "طرق لتصحيح المشكلات، مثل metadata الأخرى التي لا تتفق جيدًا (مثل العناوين المختلفة بشكل كبير)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "تعليق الصور بملاحظات على أرقام ISBN أو النطاقات."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "أي استراتيجيات لتحديد الكتب النادرة أو المعرضة للخطر."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "أي أفكار إبداعية يمكنك التوصل إليها!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "يمكنك الابتعاد تمامًا عن المعايير الدنيا، والقيام بتصور مختلف تمامًا. إذا كان مذهلاً حقًا، فإنه يستحق المكافأة، ولكن وفقًا لتقديرنا."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "قم بتقديم المشاركات عن طريق نشر تعليق على <a %(annas_archive)s>هذه المسألة</a> مع رابط إلى مستودعك المتشعب، أو طلب الدمج، أو الفرق."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "الشفرة"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "يمكن العثور على الشفرة لتوليد هذه الصور، بالإضافة إلى أمثلة أخرى، في <a %(annas_archive)s>هذا الدليل</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "لقد توصلنا إلى تنسيق بيانات مضغوط، حيث أن جميع معلومات ISBN المطلوبة تبلغ حوالي 75 ميجابايت (مضغوطة). يمكن العثور على وصف تنسيق البيانات والشفرة لتوليدها <a %(annas_archive_l1244_1319)s>هنا</a>. للحصول على المكافأة، لست ملزمًا باستخدام هذا، ولكنه ربما يكون التنسيق الأكثر ملاءمة للبدء به. يمكنك تحويل metadata الخاصة بنا كيفما تشاء (على الرغم من أن جميع الشفرة الخاصة بك يجب أن تكون مفتوحة المصدر)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "لا يمكننا الانتظار لرؤية ما ستتوصل إليه. حظًا موفقًا!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "حاويات رَبيدةُ آنّا (AAC): توحيد الإصدارات من أكبر مكتبة ظل في العالم"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "أصبحت رَبيدةُ آنّا أكبر مكتبة ظل في العالم، مما يتطلب منا توحيد إصداراتنا."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> أصبحت إلى حد بعيد أكبر مكتبة ظل في العالم، والمكتبة الوحيدة من نوعها التي تكون مفتوحة المصدر والبيانات بالكامل. أدناه جدول من صفحة Datasets الخاصة بنا (تم تعديله قليلاً):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "حققنا ذلك بثلاث طرق:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "عكس مكتبات الظل المفتوحة البيانات الموجودة (مثل Sci-Hub وLibrary Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "مساعدة مكتبات الظل التي ترغب في أن تكون أكثر انفتاحًا، ولكن لم يكن لديها الوقت أو الموارد للقيام بذلك (مثل مجموعة Libgen للقصص المصورة)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "جمع البيانات من المكتبات التي لا ترغب في المشاركة بالجملة (مثل مكتبة الزّاي)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "بالنسبة للنقطتين (2) و(3)، نحن الآن ندير مجموعة كبيرة من التورنت بأنفسنا (مئات التيرابايت). حتى الآن، تعاملنا مع هذه المجموعات كحالات فردية، مما يعني بنية تحتية وتنظيم بيانات مخصص لكل مجموعة. هذا يضيف عبئًا كبيرًا لكل إصدار، ويجعل من الصعب بشكل خاص القيام بإصدارات تدريجية أكثر."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "لهذا السبب قررنا توحيد إصداراتنا. هذا منشور تقني نعرض فيه معيارنا: <strong>حاويات رَبيدةُ آنّا</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "أهداف التصميم"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "حالة الاستخدام الأساسية لدينا هي توزيع الملفات وmetadata المرتبطة بها من مجموعات مختلفة موجودة. أهم اعتباراتنا هي:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "ملفات وmetadata غير متجانسة، بأقرب شكل ممكن إلى الصيغة الأصلية."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "معرفات غير متجانسة في المكتبات المصدرية، أو حتى عدم وجود معرفات."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "إصدارات منفصلة من metadata مقابل بيانات الملفات، أو إصدارات metadata فقط (مثل إصدارنا من ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "التوزيع عبر التورنت، مع إمكانية استخدام طرق توزيع أخرى (مثل IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "سجلات غير قابلة للتغيير، حيث يجب أن نفترض أن التورنت الخاص بنا سيعيش إلى الأبد."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "إصدارات تدريجية / إصدارات قابلة للإلحاق."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "قابلة للقراءة والكتابة آليًا، بشكل مريح وسريع، خاصة لمكدسنا (Python، MySQL، ElasticSearch، Transmission، Debian، ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "سهولة الفحص البشري إلى حد ما، رغم أن هذا ثانوي بالنسبة لقراءة الآلة."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "سهولة زراعة مجموعاتنا باستخدام seedbox مستأجر قياسي."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "يمكن تقديم البيانات الثنائية مباشرة بواسطة خوادم الويب مثل Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "بعض الأهداف غير المهمة:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "لا نهتم بأن تكون الملفات سهلة التنقل يدويًا على القرص، أو قابلة للبحث دون معالجة مسبقة."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "لا نهتم بأن نكون متوافقين مباشرة مع برامج المكتبات الموجودة."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "بينما يجب أن يكون من السهل على أي شخص زراعة مجموعتنا باستخدام التورنت، لا نتوقع أن تكون الملفات قابلة للاستخدام دون معرفة تقنية كبيرة والتزام."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "نظرًا لأن رَبيدةُ آنّا مفتوحة المصدر، نريد استخدام تنسيقنا مباشرة. عندما نقوم بتحديث فهرس البحث الخاص بنا، نصل فقط إلى المسارات المتاحة للجمهور، بحيث يمكن لأي شخص يقوم بتفريع مكتبتنا البدء بسرعة."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "المعيار"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "في النهاية، استقرينا على معيار بسيط نسبيًا. إنه مرن إلى حد ما، غير إلزامي، وقيد التطوير."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (حاوية رَبيدةُ آنّا) هو عنصر واحد يتكون من <strong>metadata</strong>، وبيانات ثنائية اختيارية <strong>binary data</strong>، وكلاهما غير قابل للتغيير. يحتوي على معرف فريد عالميًا، يسمى <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collection.</strong> كل AAC ينتمي إلى مجموعة، والتي تُعرف بأنها قائمة من AACs متسقة دلاليًا. وهذا يعني أنه إذا قمت بإجراء تغيير كبير في تنسيق metadata، فعليك إنشاء مجموعة جديدة."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” and “files” collections.</strong> وفقًا للعرف، غالبًا ما يكون من الملائم إصدار \"السجلات\" و\"الملفات\" كمجموعات مختلفة، بحيث يمكن إصدارها في جداول زمنية مختلفة، على سبيل المثال بناءً على معدلات الاستخلاص. \"السجل\" هو مجموعة تحتوي فقط على metadata، تحتوي على معلومات مثل عناوين الكتب، المؤلفين، أرقام ISBN، إلخ، بينما \"الملفات\" هي المجموعات التي تحتوي على الملفات الفعلية نفسها (pdf، epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> تنسيق AACID هو كالتالي: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. على سبيل المثال، AACID الفعلي الذي أصدرناه هو <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: اسم المجموعة، والذي قد يحتوي على حروف ASCII، أرقام، وشرطات سفلية (لكن لا يحتوي على شرطات سفلية مزدوجة)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: نسخة قصيرة من ISO 8601، دائمًا في UTC، على سبيل المثال <code>20220723T194746Z</code>. يجب أن يزيد هذا الرقم بشكل متزايد لكل إصدار، على الرغم من أن دلالاته الدقيقة يمكن أن تختلف لكل مجموعة. نقترح استخدام وقت الاستخلاص أو توليد المعرف."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: معرف خاص بالمجموعة، إذا كان ذلك ممكنًا، مثل معرف مكتبة الزّاي. قد يتم حذفه أو تقصيره. يجب حذفه أو تقصيره إذا كان AACID سيتجاوز 150 حرفًا."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID مضغوط إلى ASCII، على سبيل المثال باستخدام base57. نستخدم حاليًا مكتبة <a %(github_skorokithakis_shortuuid)s>shortuuid</a> في بايثون."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID range.</strong> نظرًا لأن AACIDs تحتوي على طوابع زمنية متزايدة بشكل متزايد، يمكننا استخدام ذلك لتحديد النطاقات داخل مجموعة معينة. نستخدم هذا التنسيق: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>، حيث تكون الطوابع الزمنية شاملة. هذا متسق مع تدوين ISO 8601. النطاقات مستمرة، وقد تتداخل، ولكن في حالة التداخل يجب أن تحتوي على سجلات متطابقة مع تلك التي تم إصدارها سابقًا في تلك المجموعة (نظرًا لأن AACs غير قابلة للتغيير). لا يُسمح بالسجلات المفقودة."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata file.</strong> يحتوي ملف metadata على metadata لنطاق من AACs، لمجموعة معينة. تحتوي هذه الملفات على الخصائص التالية:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "يجب أن يكون اسم الملف نطاق AACID، مسبوقًا بـ <code style=\"color: red\">annas_archive_meta__</code> ومتبوعة بـ <code>.jsonl.zstd</code>. على سبيل المثال، أحد إصداراتنا يسمى<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "كما هو موضح بواسطة امتداد الملف، نوع الملف هو <a %(jsonlines)s>JSON Lines</a> مضغوط باستخدام <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "يجب أن يحتوي كل كائن JSON على الحقول التالية في المستوى الأعلى: <strong>aacid</strong>، <strong>metadata</strong>، <strong>data_folder</strong> (اختياري). لا يُسمح بأي حقول أخرى."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> هو metadata عشوائي، وفقًا لدلالات المجموعة. يجب أن يكون متسقًا دلاليًا داخل المجموعة."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> اختياري، وهو اسم مجلد البيانات الثنائية الذي يحتوي على البيانات الثنائية المقابلة. اسم الملف للبيانات الثنائية المقابلة داخل ذلك المجلد هو AACID للسجل."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "يمكن تعديل البادئة <code style=\"color: red\">annas_archive_meta__</code> لتتناسب مع اسم مؤسستك، على سبيل المثال <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binary data folder.</strong> مجلد يحتوي على البيانات الثنائية لنطاق من AACs، لمجموعة معينة. تحتوي هذه المجلدات على الخصائص التالية:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "يجب أن يكون اسم الدليل نطاق AACID، مسبوقًا بـ <code style=\"color: green\">annas_archive_data__</code>، وبدون لاحقة. على سبيل المثال، أحد إصداراتنا الفعلية يحتوي على دليل يسمى<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "يجب أن يحتوي الدليل على ملفات البيانات لجميع AACs ضمن النطاق المحدد. يجب أن يحتوي كل ملف بيانات على AACID كاسم الملف (بدون امتدادات)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "يُوصى بجعل هذه المجلدات قابلة للإدارة إلى حد ما من حيث الحجم، على سبيل المثال، ألا تكون أكبر من 100 جيجابايت إلى 1 تيرابايت لكل منها، على الرغم من أن هذه التوصية قد تتغير بمرور الوقت."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>التورنتات.</strong> يمكن تجميع ملفات الميتاداتا ومجلدات البيانات الثنائية في تورنتات، مع تورنت واحد لكل ملف ميتاداتا، أو تورنت واحد لكل مجلد بيانات ثنائية. يجب أن تحتوي التورنتات على اسم الملف/الدليل الأصلي بالإضافة إلى لاحقة <code>.torrent</code> كاسم الملف."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "مثال"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "لنلقِ نظرة على إصدارنا الأخير من مكتبة الزّاي كمثال. يتكون من مجموعتين: \"<span style=\"background: #fffaa3\">zlib3_records</span>\" و\"<span style=\"background: #ffd6fe\">zlib3_files</span>\". يتيح لنا ذلك استخراج وإصدار سجلات الميتاداتا بشكل منفصل عن ملفات الكتب الفعلية. لذلك، أصدرنا تورنتين مع ملفات ميتاداتا:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "كما أصدرنا مجموعة من التورنتات مع مجلدات البيانات الثنائية، ولكن فقط لمجموعة \"<span style=\"background: #ffd6fe\">zlib3_files</span>\"، بإجمالي 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "بتشغيل <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> يمكننا رؤية ما بداخلها:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "في هذه الحالة، إنها ميتاداتا لكتاب كما أبلغت عنها مكتبة الزّاي. في المستوى الأعلى لدينا فقط \"aacid\" و\"metadata\"، ولكن لا يوجد \"data_folder\"، حيث لا توجد بيانات ثنائية مقابلة. يحتوي AACID على \"22430000\" كمعرف رئيسي، والذي يمكننا رؤيته مأخوذ من \"zlibrary_id\". يمكننا توقع أن تحتوي AACs الأخرى في هذه المجموعة على نفس الهيكل."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "الآن دعونا نشغل <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "هذه ميتاداتا AAC أصغر بكثير، على الرغم من أن الجزء الأكبر من هذا AAC يقع في مكان آخر في ملف ثنائي! بعد كل شيء، لدينا \"data_folder\" هذه المرة، لذلك يمكننا توقع أن تكون البيانات الثنائية المقابلة موجودة في <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. تحتوي \"metadata\" على \"zlibrary_id\"، لذلك يمكننا بسهولة ربطها بـ AAC المقابل في مجموعة \"zlib_records\". كان بإمكاننا الربط بعدة طرق مختلفة، على سبيل المثال من خلال AACID — المعيار لا يفرض ذلك."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "لاحظ أنه ليس من الضروري أيضًا أن يكون حقل \"metadata\" نفسه JSON. يمكن أن يكون سلسلة تحتوي على XML أو أي تنسيق بيانات آخر. يمكنك حتى تخزين معلومات الميتاداتا في الكتلة الثنائية المرتبطة، على سبيل المثال إذا كانت كمية البيانات كبيرة."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "الخاتمة"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "مع هذا المعيار، يمكننا إصدار الإصدارات بشكل أكثر تدريجيًا، وإضافة مصادر بيانات جديدة بسهولة أكبر. لدينا بالفعل بعض الإصدارات المثيرة في الأفق!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "نأمل أيضًا أن يصبح من الأسهل للمكتبات الظلية الأخرى عكس مجموعاتنا. بعد كل شيء، هدفنا هو الحفاظ على المعرفة والثقافة البشرية إلى الأبد، لذا كلما زادت التكرار كان ذلك أفضل."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "تحديث آنّا: أرشيف مفتوح المصدر بالكامل، ElasticSearch، أكثر من 300 جيجابايت من أغلفة الكتب"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "لقد عملنا على مدار الساعة لتقديم بديل جيد مع رَبيدةُ آنّا. إليكم بعض الأشياء التي حققناها مؤخرًا."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "مع توقف مكتبة الزّاي واعتقال مؤسسيها (المزعومين)، عملنا على مدار الساعة لتقديم بديل جيد مع رَبيدةُ آنّا (لن نقوم بربطها هنا، ولكن يمكنك البحث عنها في جوجل). إليكم بعض الأشياء التي حققناها مؤخرًا."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "رَبيدةُ آنّا مفتوحة المصدر بالكامل"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "نحن نؤمن بأن المعلومات يجب أن تكون مجانية، وكودنا الخاص ليس استثناءً. لقد أصدرنا كل كودنا على مثيل Gitlab المستضاف لدينا: <a %(annas_archive)s>برمجيات آنّا</a>. نستخدم أيضًا متعقب القضايا لتنظيم عملنا. إذا كنت ترغب في المشاركة في تطويرنا، فهذا مكان رائع للبدء."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "لإعطائك لمحة عن الأشياء التي نعمل عليها، خذ عملنا الأخير على تحسينات الأداء من جانب العميل. نظرًا لأننا لم ننفذ الترقيم بعد، كنا غالبًا ما نعيد صفحات بحث طويلة جدًا، مع 100-200 نتيجة. لم نرغب في قطع نتائج البحث في وقت مبكر جدًا، ولكن هذا يعني أنه سيبطئ بعض الأجهزة. لهذا، قمنا بتنفيذ خدعة صغيرة: قمنا بتغليف معظم نتائج البحث في تعليقات HTML (<code><!-- --></code>)، ثم كتبنا القليل من Javascript الذي يكتشف متى يجب أن تصبح النتيجة مرئية، في تلك اللحظة نقوم بفك التعليق:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "تم تنفيذ \"الافتراضية\" لـ DOM في 23 سطرًا فقط، دون الحاجة إلى مكتبات فاخرة! هذا هو نوع الكود العملي السريع الذي ينتهي بك الأمر إليه عندما يكون لديك وقت محدود ومشاكل حقيقية تحتاج إلى حل. لقد تم الإبلاغ عن أن بحثنا الآن يعمل بشكل جيد على الأجهزة البطيئة!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "كان جهدًا كبيرًا آخر هو أتمتة بناء قاعدة البيانات. عندما أطلقنا، قمنا بجمع مصادر مختلفة بشكل عشوائي. الآن نريد أن نبقيها محدثة، لذا كتبنا مجموعة من السكربتات لتنزيل metadata جديدة من فرعي Library Genesis ودمجها. الهدف ليس فقط جعل هذا مفيدًا لأرشيفنا، بل تسهيل الأمور لأي شخص يريد اللعب مع metadata مكتبة الظل. الهدف سيكون دفتر Jupyter يحتوي على جميع أنواع metadata المثيرة للاهتمام، حتى نتمكن من إجراء المزيد من الأبحاث مثل معرفة <a %(blog)s>نسبة ISBNs المحفوظة للأبد</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "أخيرًا، قمنا بتجديد نظام التبرعات لدينا. يمكنك الآن استخدام بطاقة ائتمان لإيداع الأموال مباشرة في محافظنا المشفرة، دون الحاجة إلى معرفة أي شيء عن العملات المشفرة. سنواصل مراقبة مدى نجاح هذا في الممارسة، لكن هذا أمر كبير."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "التحول إلى ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "كانت واحدة من <a %(annas_archive)s>التذاكر</a> لدينا عبارة عن مجموعة من المشاكل مع نظام البحث لدينا. استخدمنا بحث النص الكامل في MySQL، حيث كان لدينا جميع بياناتنا في MySQL على أي حال. لكن كان له حدوده:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "بعض الاستفسارات استغرقت وقتًا طويلاً جدًا، إلى درجة أنها كانت تحتكر جميع الاتصالات المفتوحة."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "افتراضيًا، لدى MySQL طول كلمة أدنى، أو يمكن أن يصبح الفهرس كبيرًا جدًا. أبلغ الناس عن عدم قدرتهم على البحث عن \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "كان البحث سريعًا إلى حد ما فقط عندما تم تحميله بالكامل في الذاكرة، مما تطلب منا الحصول على جهاز أكثر تكلفة لتشغيله، بالإضافة إلى بعض الأوامر لتحميل الفهرس عند بدء التشغيل."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "لم نكن لنتمكن من تمديده بسهولة لبناء ميزات جديدة، مثل <a %(wikipedia_cjk_characters)s>تحليل الرموز بشكل أفضل للغات غير المفصولة بمسافات</a>، التصفية/التصنيف، الفرز، اقتراحات \"هل تقصد\"، الإكمال التلقائي، وهكذا."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "بعد التحدث إلى مجموعة من الخبراء، استقرينا على ElasticSearch. لم يكن مثاليًا (اقتراحات \"هل تقصد\" الافتراضية وميزات الإكمال التلقائي لديهم سيئة)، لكن بشكل عام كان أفضل بكثير من MySQL للبحث. ما زلنا لسنا <a %(youtube)s>متأكدين تمامًا</a> من استخدامه لأي بيانات حيوية (على الرغم من أنهم حققوا الكثير من <a %(elastic_co)s>التقدم</a>)، لكن بشكل عام نحن سعداء جدًا بالتحول."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "في الوقت الحالي، قمنا بتنفيذ بحث أسرع بكثير، دعم لغوي أفضل، فرز أفضل من حيث الصلة، خيارات فرز مختلفة، وتصفية على نوع اللغة/الكتاب/الملف. إذا كنت مهتمًا بكيفية عمله، <a %(annas_archive_l140)s>ألقِ</a> <a %(annas_archive_l1115)s>نظرة</a> <a %(annas_archive_l1635)s>عليه</a>. إنه متاح بشكل كبير، على الرغم من أنه يمكن أن يستخدم بعض التعليقات الإضافية…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "إصدار أكثر من 300 جيجابايت من أغلفة الكتب"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "أخيرًا، نحن سعداء بالإعلان عن إصدار صغير. بالتعاون مع الأشخاص الذين يديرون فرع Libgen.rs، نحن نشارك جميع أغلفة كتبهم عبر التورنت وIPFS. سيوزع هذا الحمل من عرض الأغلفة بين المزيد من الأجهزة، وسيحافظ عليها بشكل أفضل. في العديد من الحالات (ولكن ليس كلها)، يتم تضمين أغلفة الكتب في الملفات نفسها، لذا فإن هذا نوع من \"البيانات المشتقة\". لكن وجودها في IPFS لا يزال مفيدًا جدًا للتشغيل اليومي لكل من رَبيدةُ آنّا والفروع المختلفة لـ Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "كالعادة، يمكنك العثور على هذا الإصدار في مرآة مكتبة القراصنة (ملاحظة: تم نقله إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>). لن نقوم بالربط إليه هنا، لكن يمكنك العثور عليه بسهولة."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "نأمل أن نتمكن من تخفيف وتيرتنا قليلاً، الآن بعد أن لدينا بديل لائق لمكتبة الزّاي. هذا العبء ليس مستدامًا بشكل خاص. إذا كنت مهتمًا بالمساعدة في البرمجة، أو عمليات الخادم، أو أعمال الحفظ، فلا تتردد في التواصل معنا. لا يزال هناك الكثير من <a %(annas_archive)s>العمل الذي يجب القيام به</a>. شكرًا لاهتمامك ودعمك."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "رَبيدةُ آنّا قامت بنسخ أكبر مكتبة ظل للقصص المصورة في العالم (95 تيرابايت) — يمكنك المساعدة في توزيعها"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "أكبر مكتبة ظل للقصص المصورة في العالم كانت تحتوي على نقطة فشل واحدة.. حتى اليوم."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>ناقش على Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "أكبر مكتبة ظل للقصص المصورة هي على الأرجح تلك الخاصة بفرع معين من Library Genesis: Libgen.li. تمكن المسؤول الوحيد الذي يدير هذا الموقع من جمع مجموعة هائلة من القصص المصورة تضم أكثر من 2 مليون ملف، بإجمالي يزيد عن 95 تيرابايت. ومع ذلك، على عكس مجموعات Library Genesis الأخرى، لم تكن هذه المجموعة متاحة بشكل جماعي عبر التورنت. كان بإمكانك الوصول إلى هذه القصص المصورة بشكل فردي فقط عبر خادمه الشخصي البطيء — نقطة فشل واحدة. حتى اليوم!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "في هذا المنشور، سنخبركم المزيد عن هذه المجموعة وعن حملتنا لجمع التبرعات لدعم المزيد من هذا العمل."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>تحاول الدكتورة باربرا جوردون أن تفقد نفسها في العالم العادي للمكتبة...</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "تفرعات Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "أولاً، بعض الخلفية. قد تعرفون مكتبة جينيسيس لمجموعتها الضخمة من الكتب. عدد أقل من الناس يعرف أن متطوعي مكتبة جينيسيس قد أنشأوا مشاريع أخرى، مثل مجموعة كبيرة من المجلات والوثائق القياسية، ونسخة احتياطية كاملة من Sci-Hub (بالتعاون مع مؤسسة Sci-Hub، ألكسندرا إلباكيان)، وبالفعل، مجموعة ضخمة من القصص المصورة."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "في مرحلة ما، ذهب مشغلو مرايا مكتبة جينيسيس في طرقهم المنفصلة، مما أدى إلى الوضع الحالي بوجود عدد من \"التفرعات\" المختلفة، وكلها لا تزال تحمل اسم مكتبة جينيسيس. تفرع Libgen.li يحتوي بشكل فريد على هذه المجموعة من القصص المصورة، بالإضافة إلى مجموعة كبيرة من المجلات (التي نعمل عليها أيضًا)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "التعاون"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "نظرًا لحجمها، كانت هذه المجموعة على قائمة أمنياتنا منذ فترة طويلة، لذا بعد نجاحنا في النسخ الاحتياطي لمكتبة الزّاي، وضعنا أنظارنا على هذه المجموعة. في البداية قمنا بجمعها مباشرة، وكان ذلك تحديًا كبيرًا، حيث لم يكن الخادم في أفضل حالاته. حصلنا على حوالي 15 تيرابايت بهذه الطريقة، ولكن كان التقدم بطيئًا."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "لحسن الحظ، تمكنا من التواصل مع مشغل المكتبة، الذي وافق على إرسال جميع البيانات إلينا مباشرة، مما كان أسرع بكثير. استغرق الأمر أكثر من نصف عام لنقل ومعالجة جميع البيانات، وكدنا نفقدها جميعًا بسبب تلف القرص، مما كان يعني البدء من جديد."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "جعلتنا هذه التجربة نعتقد أنه من المهم نشر هذه البيانات في أسرع وقت ممكن، حتى يمكن نسخها على نطاق واسع. نحن على بعد حادثة أو اثنتين غير محظوظتين من فقدان هذه المجموعة إلى الأبد!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "المجموعة"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "التحرك بسرعة يعني أن المجموعة غير منظمة قليلاً... دعونا نلقي نظرة. تخيل أن لدينا نظام ملفات (والذي في الواقع نقسمه عبر التورنت):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "الدليل الأول، <code>/repository</code>، هو الجزء الأكثر تنظيمًا من هذا. يحتوي هذا الدليل على ما يسمى \"أدلة الألف\": أدلة تحتوي كل منها على ألف ملف، يتم ترقيمها تدريجيًا في قاعدة البيانات. يحتوي الدليل <code>0</code> على ملفات مع comic_id من 0 إلى 999، وهكذا."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "هذا هو نفس النظام الذي استخدمته مكتبة جينيسيس لمجموعاتها من الخيال واللاخيال. الفكرة هي أن كل \"دليل ألف\" يتحول تلقائيًا إلى تورنت بمجرد امتلائه."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "ومع ذلك، لم يقم مشغل Libgen.li بإنشاء تورنت لهذه المجموعة، وبالتالي أصبحت أدلة الألف غير مريحة، وأفسحت المجال لـ \"أدلة غير مرتبة\". هذه هي <code>/comics0</code> إلى <code>/comics4</code>. تحتوي جميعها على هياكل دليل فريدة، ربما كانت منطقية لجمع الملفات، لكنها لا تبدو منطقية بالنسبة لنا الآن. لحسن الحظ، لا تزال metadata تشير مباشرة إلى جميع هذه الملفات، لذا فإن تنظيمها على القرص لا يهم فعليًا!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "تتوفر metadata في شكل قاعدة بيانات MySQL. يمكن تنزيلها مباشرة من موقع Libgen.li، لكننا سنوفرها أيضًا في تورنت، إلى جانب جدولنا الخاص بجميع تجزئات MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "التحليل"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "عندما تحصل على 95 تيرابايت في مجموعة التخزين الخاصة بك، تحاول فهم ما يوجد هناك... قمنا ببعض التحليل لنرى ما إذا كان بإمكاننا تقليل الحجم قليلاً، مثل إزالة التكرارات. إليكم بعض من نتائجنا:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "يمكن نظريًا تصفية التكرارات الدلالية (مسوحات مختلفة لنفس الكتاب)، ولكنها صعبة. عند النظر يدويًا في القصص المصورة وجدنا الكثير من الإيجابيات الكاذبة."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "هناك بعض التكرارات فقط بواسطة MD5، وهو أمر غير فعال نسبيًا، ولكن تصفية تلك التكرارات ستوفر لنا حوالي 1% in من التوفير. على هذا النطاق، لا يزال ذلك حوالي 1 تيرابايت، ولكن أيضًا، على هذا النطاق، 1 تيرابايت لا يهم حقًا. نفضل عدم المخاطرة بتدمير البيانات عن طريق الخطأ في هذه العملية."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "وجدنا مجموعة من البيانات غير المتعلقة بالكتب، مثل الأفلام المستندة إلى القصص المصورة. يبدو ذلك أيضًا غير فعال، حيث أن هذه الأفلام متاحة بالفعل على نطاق واسع بوسائل أخرى. ومع ذلك، أدركنا أننا لا يمكننا فقط تصفية ملفات الأفلام، حيث توجد أيضًا <em>كتب قصص مصورة تفاعلية</em> تم إصدارها على الكمبيوتر، وقام شخص ما بتسجيلها وحفظها كأفلام."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "في النهاية، أي شيء يمكننا حذفه من المجموعة لن يوفر سوى بضع نسب مئوية. ثم تذكرنا أننا مهووسون بالبيانات، والأشخاص الذين سيقومون بعكس هذه البيانات هم أيضًا مهووسون بالبيانات، لذا، \"ماذا تعني بالحذف؟!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "لذلك نقدم لكم المجموعة الكاملة غير المعدلة. إنها كمية كبيرة من البيانات، لكننا نأمل أن يهتم عدد كافٍ من الأشخاص بنشرها على أي حال."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "جمع التبرعات"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "نحن نطلق هذه البيانات في بعض الأجزاء الكبيرة. أول تورنت هو <code>/comics0</code>، الذي وضعناه في ملف .tar ضخم بحجم 12 تيرابايت. هذا أفضل لمحرك الأقراص الصلب وبرامج التورنت من عدد لا يحصى من الملفات الصغيرة."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "كجزء من هذا الإصدار، نقوم بجمع التبرعات. نحن نسعى لجمع 20,000 دولار لتغطية تكاليف التشغيل والتعاقد لهذه المجموعة، وكذلك تمكين المشاريع الجارية والمستقبلية. لدينا بعض المشاريع <em>الضخمة</em> قيد التنفيذ."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>من أدعم بتبرعي؟</em> باختصار: نحن ندعم جميع المعرفة والثقافة الإنسانية، ونجعلها متاحة بسهولة. كل شفراتنا وبياناتنا مفتوحة المصدر، نحن مشروع يديره متطوعون بالكامل، وقد أنقذنا حتى الآن 125 تيرابايت من الكتب (بالإضافة إلى تورنتات Libgen وScihub الموجودة). في النهاية، نحن نبني عجلة دوارة تمكن وتحفز الناس على العثور على جميع الكتب في العالم ومسحها ضوئيًا ونسخها احتياطيًا. سنكتب عن خطتنا الرئيسية في منشور مستقبلي. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "إذا تبرعت للحصول على عضوية \"Amazing Archivist\" لمدة 12 شهرًا (780 دولارًا)، يمكنك <strong>“تبني تورنت”</strong>، مما يعني أننا سنضع اسم المستخدم أو الرسالة الخاصة بك في اسم ملف أحد التورنتات!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "يمكنك التبرع بالذهاب إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> والنقر على زر \"تبرع\". نحن أيضًا نبحث عن المزيد من المتطوعين: مهندسي البرمجيات، باحثي الأمن، خبراء التجارة المجهولين، والمترجمين. يمكنك أيضًا دعمنا بتقديم خدمات الاستضافة. وبالطبع، يرجى نشر تورنتاتنا!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "شكرًا لكل من دعمنا بسخاء حتى الآن! أنتم حقًا تحدثون فرقًا."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "إليكم التورنتات التي تم إصدارها حتى الآن (ما زلنا نعالج الباقي):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "يمكن العثور على جميع التورنتات على <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a> تحت \"Datasets\" (نحن لا نربط هناك مباشرة، حتى لا تتم إزالة الروابط إلى هذه المدونة من Reddit وTwitter وما إلى ذلك). من هناك، اتبع الرابط إلى موقع Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "ما التالي؟"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "مجموعة من التورنتات رائعة للحفظ طويل الأمد، لكنها ليست كذلك للوصول اليومي. سنعمل مع شركاء الاستضافة للحصول على كل هذه البيانات على الويب (حيث أن رَبيدةُ آنّا لا تستضيف أي شيء مباشرة). بالطبع ستتمكن من العثور على روابط التنزيل هذه على رَبيدةُ آنّا."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "نحن ندعو الجميع أيضًا للقيام بأشياء مع هذه البيانات! ساعدنا في تحليلها بشكل أفضل، وإزالة التكرار، ووضعها على IPFS، وإعادة مزجها، وتدريب نماذج الذكاء الاصطناعي الخاصة بك بها، وما إلى ذلك. إنها كلها لك، ولا يمكننا الانتظار لرؤية ما ستفعله بها."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "أخيرًا، كما قيل من قبل، لا يزال لدينا بعض الإصدارات الضخمة القادمة (إذا <em>شخص ما</em> يمكنه <em>بالصدفة</em> إرسال تفريغ لقاعدة بيانات <em>معينة</em> ACS4، فأنت تعرف أين تجدنا...)، بالإضافة إلى بناء العجلة الدوارة لنسخ جميع الكتب في العالم احتياطيًا."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "لذا ابقوا متابعين، نحن فقط بدأنا."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "تمت إضافة 3 كتب جديدة إلى عاكسة مكتبة القراصنة (+24 تيرابايت، 3.8 مليون كتاب)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "في الإصدار الأصلي من عاكسة مكتبة القراصنة (تعديل: تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>)، قمنا بعمل عاكسة لمكتبة الزّاي، وهي مجموعة كتب غير قانونية كبيرة. كتذكير، هذا ما كتبناه في تلك المدونة الأصلية:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "مكتبة الزّاي هي مكتبة شهيرة (وغير قانونية). لقد أخذوا مجموعة Library Genesis وجعلوها قابلة للبحث بسهولة. بالإضافة إلى ذلك، أصبحوا فعالين جدًا في طلب مساهمات الكتب الجديدة، من خلال تحفيز المستخدمين المساهمين بمزايا مختلفة. حاليًا، لا يساهمون بهذه الكتب الجديدة مرة أخرى إلى Library Genesis. وعلى عكس Library Genesis، لا يجعلون مجموعتهم قابلة للانعكاس بسهولة، مما يمنع الحفظ الواسع. هذا مهم لنموذج أعمالهم، حيث أنهم يفرضون رسومًا للوصول إلى مجموعتهم بشكل جماعي (أكثر من 10 كتب في اليوم)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "نحن لا نصدر أحكامًا أخلاقية بشأن فرض رسوم مالية للوصول الجماعي إلى مجموعة كتب غير قانونية. لا شك أن مكتبة الزّاي قد نجحت في توسيع الوصول إلى المعرفة وتوفير المزيد من الكتب. نحن هنا ببساطة لنقوم بدورنا: ضمان الحفاظ طويل الأمد على هذه المجموعة الخاصة."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "تعود تلك المجموعة إلى منتصف عام 2021. في هذه الأثناء، كانت مكتبة الزّاي تنمو بمعدل مذهل: لقد أضافوا حوالي 3.8 مليون كتاب جديد. هناك بعض النسخ المكررة، بالطبع، لكن الغالبية العظمى منها تبدو كتبًا جديدة بشكل شرعي، أو نسخًا بجودة أعلى من الكتب المقدمة سابقًا. يعود ذلك بشكل كبير إلى زيادة عدد المشرفين المتطوعين في مكتبة الزّاي، ونظام التحميل الجماعي الخاص بهم مع إزالة التكرارات. نود أن نهنئهم على هذه الإنجازات."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "نحن سعداء بالإعلان عن أننا حصلنا على جميع الكتب التي أضيفت إلى مكتبة الزّاي بين آخر عاكسة لنا وأغسطس 2022. كما عدنا وجمعنا بعض الكتب التي فاتتنا في المرة الأولى. بشكل عام، هذه المجموعة الجديدة تبلغ حوالي 24 تيرابايت، وهي أكبر بكثير من المجموعة السابقة (7 تيرابايت). الآن، عاكستنا تبلغ 31 تيرابايت في المجموع. مرة أخرى، قمنا بإزالة التكرارات مقابل Library Genesis، حيث تتوفر بالفعل تورنتات لتلك المجموعة."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "يرجى الذهاب إلى عاكسة مكتبة القراصنة للاطلاع على المجموعة الجديدة (تعديل: تم النقل إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>). هناك المزيد من المعلومات هناك حول كيفية هيكلة الملفات، وما الذي تغير منذ المرة الأخيرة. لن نقوم بالربط إليها من هنا، حيث أن هذا مجرد موقع مدونة لا يستضيف أي مواد غير قانونية."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "بالطبع، يُعتبر المساهمة في التحميل أيضًا طريقة رائعة لمساعدتنا. شكرًا لكل من يساهم في تحميل مجموعتنا السابقة من التورنتات. نحن ممتنون للاستجابة الإيجابية، وسعداء بوجود الكثير من الأشخاص الذين يهتمون بالحفاظ على المعرفة والثقافة بهذه الطريقة غير التقليدية."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "كيفية أن تصبح أرشيفي قرصان"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "قد تكون التحدي الأول مفاجئًا. إنه ليس مشكلة تقنية، أو مشكلة قانونية. إنه مشكلة نفسية."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "قبل أن نبدأ، هناك تحديثان حول عاكسة مكتبة القراصنة (تعديل: تم النقل إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "حصلنا على بعض التبرعات السخية للغاية. كان الأول 10 آلاف دولار من شخص مجهول كان يدعم أيضًا \"bookwarrior\"، المؤسس الأصلي لـ Library Genesis. شكر خاص لـ bookwarrior لتسهيل هذا التبرع. كان الثاني 10 آلاف دولار أخرى من متبرع مجهول، تواصل معنا بعد إصدارنا الأخير، وألهمه للمساعدة. كما حصلنا على عدد من التبرعات الأصغر. شكرًا جزيلاً على كل دعمكم السخي. لدينا بعض المشاريع الجديدة المثيرة في الأفق التي سيدعمها هذا، لذا تابعونا."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "واجهنا بعض الصعوبات التقنية مع حجم إصدارنا الثاني، لكن تورنتاتنا الآن متاحة ويتم تحميلها. كما حصلنا على عرض سخي من شخص مجهول لتحميل مجموعتنا على خوادمهم ذات السرعة العالية جدًا، لذا نقوم بتحميل خاص إلى أجهزتهم، وبعد ذلك يجب أن يلاحظ الجميع الذين يقومون بتحميل المجموعة تحسنًا كبيرًا في السرعة."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "يمكن كتابة كتب كاملة عن <em>لماذا</em> الحفظ الرقمي بشكل عام، والأرشفة القرصانية بشكل خاص، لكن دعونا نقدم مقدمة سريعة لأولئك الذين ليسوا على دراية كبيرة. العالم ينتج المزيد من المعرفة والثقافة أكثر من أي وقت مضى، ولكن أيضًا يتم فقدان المزيد منها أكثر من أي وقت مضى. البشرية تعتمد بشكل كبير على الشركات مثل الناشرين الأكاديميين، وخدمات البث، وشركات وسائل التواصل الاجتماعي للحفاظ على هذا التراث، وغالبًا ما لم يثبتوا أنهم حراس عظماء. تحقق من الفيلم الوثائقي Digital Amnesia، أو أي حديث لجيسون سكوت."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "هناك بعض المؤسسات التي تقوم بعمل جيد في الأرشفة بقدر ما تستطيع، لكنها مقيدة بالقانون. كقراصنة، نحن في وضع فريد لأرشفة مجموعات لا يمكنهم لمسها، بسبب إنفاذ حقوق الطبع والنشر أو قيود أخرى. يمكننا أيضًا عكس المجموعات عدة مرات، عبر العالم، مما يزيد من فرص الحفظ السليم."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "في الوقت الحالي، لن ندخل في مناقشات حول إيجابيات وسلبيات الملكية الفكرية، أو أخلاقية كسر القانون، أو التأملات في الرقابة، أو مسألة الوصول إلى المعرفة والثقافة. مع كل ذلك بعيدًا عن الطريق، دعونا نغوص في <em>كيف</em>. سنشارك كيف أصبح فريقنا أرشيفيين قراصنة، والدروس التي تعلمناها على طول الطريق. هناك العديد من التحديات عندما تبدأ في هذه الرحلة، ونأمل أن نساعدك في تجاوز بعضها."

#, fuzzy
msgid "blog.how-to.community"
msgstr "المجتمع"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "قد تكون التحدي الأول مفاجئًا. إنه ليس مشكلة تقنية، أو مشكلة قانونية. إنه مشكلة نفسية: القيام بهذا العمل في الظل يمكن أن يكون وحيدًا بشكل لا يصدق. اعتمادًا على ما تخطط للقيام به، ونموذج التهديد الخاص بك، قد تضطر إلى أن تكون حذرًا جدًا. في أحد أطراف الطيف لدينا أشخاص مثل ألكسندرا إلباكيان*، مؤسسة Sci-Hub، التي تكون منفتحة جدًا بشأن أنشطتها. لكنها في خطر كبير من الاعتقال إذا زارت دولة غربية في هذه المرحلة، وقد تواجه عقودًا من السجن. هل هذا خطر ترغب في تحمله؟ نحن في الطرف الآخر من الطيف؛ نكون حذرين جدًا لعدم ترك أي أثر، ونمتلك أمانًا تشغيليًا قويًا."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* كما ذكر في HN بواسطة \"ynno\"، لم تكن ألكسندرا ترغب في البداية في أن تكون معروفة: \"كانت خوادمها معدة لإصدار رسائل خطأ مفصلة من PHP، بما في ذلك المسار الكامل لملف المصدر الذي يحتوي على الخطأ، والذي كان تحت دليل /home/<USER>" لذا، استخدم أسماء مستخدمين عشوائية على أجهزة الكمبيوتر التي تستخدمها لهذا العمل، في حال قمت بتكوين شيء بشكل خاطئ."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "لكن تلك السرية تأتي بتكلفة نفسية. معظم الناس يحبون أن يتم الاعتراف بعملهم، ومع ذلك لا يمكنك أن تأخذ أي فضل لهذا في الحياة الواقعية. حتى الأشياء البسيطة يمكن أن تكون تحديًا، مثل الأصدقاء الذين يسألونك عما كنت تفعله (في مرحلة ما \"العبث مع NAS / homelab\" يصبح قديمًا)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "لهذا السبب من المهم جدًا العثور على بعض المجتمع. يمكنك التخلي عن بعض الأمان التشغيلي من خلال الثقة في بعض الأصدقاء المقربين جدًا، الذين تعرف أنك تستطيع الوثوق بهم بعمق. حتى في هذه الحالة كن حذرًا لعدم وضع أي شيء في الكتابة، في حال اضطروا إلى تسليم رسائلهم الإلكترونية إلى السلطات، أو إذا تم اختراق أجهزتهم بطريقة أخرى."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "الأفضل من ذلك هو العثور على بعض القراصنة الزملاء. إذا كان أصدقاؤك المقربون مهتمين بالانضمام إليك، فهذا رائع! وإلا، قد تتمكن من العثور على آخرين عبر الإنترنت. للأسف، لا يزال هذا مجتمعًا متخصصًا. حتى الآن وجدنا فقط عددًا قليلاً من الآخرين الذين ينشطون في هذا المجال. تبدو الأماكن الجيدة للبدء هي منتديات Library Genesis، وr/DataHoarder. فريق الأرشيف أيضًا لديه أفراد متشابهين في التفكير، على الرغم من أنهم يعملون ضمن القانون (حتى لو في بعض المناطق الرمادية من القانون). مشاهد \"warez\" التقليدية وقرصنة أيضًا لديها أشخاص يفكرون بطرق مشابهة."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "نحن منفتحون على الأفكار حول كيفية تعزيز المجتمع واستكشاف الأفكار. لا تتردد في مراسلتنا على تويتر أو ريديت. ربما يمكننا استضافة نوع من المنتدى أو مجموعة دردشة. أحد التحديات هو أن هذا يمكن أن يتم حجبه بسهولة عند استخدام المنصات الشائعة، لذا سيتعين علينا استضافته بأنفسنا. هناك أيضًا توازن بين جعل هذه المناقشات عامة بالكامل (لزيادة التفاعل المحتمل) وبين جعلها خاصة (لعدم إبلاغ \"الأهداف\" المحتملة بأننا على وشك جمع بياناتهم). سنحتاج إلى التفكير في ذلك. أخبرنا إذا كنت مهتمًا بذلك!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "المشاريع"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "عندما نقوم بمشروع، فإنه يمر بعدة مراحل:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "اختيار المجال / الفلسفة: أين تريد التركيز بشكل تقريبي، ولماذا؟ ما هي شغفك ومهاراتك وظروفك الفريدة التي يمكنك استخدامها لصالحك؟"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "اختيار الهدف: أي مجموعة محددة ستقوم بعكسها؟"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "جمع metadata: فهرسة المعلومات حول الملفات، دون تنزيل الملفات نفسها (التي غالبًا ما تكون أكبر بكثير)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "اختيار البيانات: بناءً على metadata، تضييق نطاق البيانات الأكثر صلة بالأرشفة الآن. قد يكون كل شيء، ولكن غالبًا ما يكون هناك طريقة معقولة لتوفير المساحة وعرض النطاق الترددي."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "جمع البيانات: الحصول على البيانات فعليًا."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "التوزيع: تغليفها في تورنت، الإعلان عنها في مكان ما، وجعل الناس ينشرونها."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "هذه ليست مراحل مستقلة تمامًا، وغالبًا ما تعيدك الأفكار من مرحلة لاحقة إلى مرحلة سابقة. على سبيل المثال، أثناء جمع metadata قد تدرك أن الهدف الذي اخترته لديه آليات دفاعية تتجاوز مستوى مهاراتك (مثل حظر IP)، لذا تعود وتجد هدفًا مختلفًا."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. اختيار المجال / الفلسفة"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "لا يوجد نقص في المعرفة والتراث الثقافي الذي يجب حفظه، مما قد يكون مرهقًا. لهذا السبب غالبًا ما يكون من المفيد أن تأخذ لحظة وتفكر في ما يمكن أن تكون مساهمتك."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "لدى الجميع طريقة مختلفة للتفكير في هذا، ولكن هنا بعض الأسئلة التي يمكنك أن تسأل نفسك:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "لماذا أنت مهتم بهذا؟ ما الذي تشعر بالشغف تجاهه؟ إذا تمكنا من جمع مجموعة من الأشخاص الذين يقومون جميعًا بأرشفة الأشياء التي يهتمون بها بشكل خاص، فسيغطي ذلك الكثير! ستعرف أكثر بكثير من الشخص العادي عن شغفك، مثل ما هي البيانات المهمة للحفظ، وما هي أفضل المجموعات والمجتمعات عبر الإنترنت، وهكذا."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "ما هي المهارات التي تمتلكها والتي يمكنك استخدامها لصالحك؟ على سبيل المثال، إذا كنت خبيرًا في الأمن عبر الإنترنت، يمكنك إيجاد طرق لهزيمة حظر IP للأهداف الآمنة. إذا كنت بارعًا في تنظيم المجتمعات، فربما يمكنك جمع بعض الأشخاص حول هدف معين. من المفيد معرفة بعض البرمجة، ولو فقط للحفاظ على أمان العمليات الجيد طوال هذه العملية."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "كم من الوقت لديك لهذا؟ نصيحتنا ستكون البدء بمشاريع صغيرة والقيام بمشاريع أكبر كلما اكتسبت الخبرة، ولكن يمكن أن يصبح الأمر مستهلكًا للوقت بشكل كامل."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "ما هو المجال ذو الرافعة العالية الذي يجب التركيز عليه؟ إذا كنت ستقضي X ساعة في أرشفة القرصنة، فكيف يمكنك الحصول على أكبر \"عائد على استثمارك\"؟"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "ما هي الطرق الفريدة التي تفكر بها في هذا؟ قد تكون لديك بعض الأفكار أو الأساليب المثيرة للاهتمام التي قد يكون الآخرون قد فاتتهم."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "في حالتنا، كنا نهتم بشكل خاص بالحفاظ على العلوم على المدى الطويل. كنا نعرف عن Library Genesis، وكيف تم عكسها بالكامل عدة مرات باستخدام التورنت. أحببنا تلك الفكرة. ثم في يوم من الأيام، حاول أحدنا العثور على بعض الكتب العلمية في Library Genesis، لكنه لم يتمكن من العثور عليها، مما أثار الشكوك حول مدى اكتمالها حقًا. ثم بحثنا عن تلك الكتب عبر الإنترنت، ووجدناها في أماكن أخرى، مما زرع بذرة مشروعنا. حتى قبل أن نعرف عن مكتبة الزّاي، كانت لدينا فكرة عدم محاولة جمع كل تلك الكتب يدويًا، بل التركيز على عكس المجموعات الموجودة، والمساهمة بها مرة أخرى في Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. اختيار الهدف"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "إذن، لدينا المنطقة التي ننظر إليها، الآن أي مجموعة محددة نقوم بعكسها؟ هناك بعض الأشياء التي تجعل الهدف جيدًا:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "كبيرة"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "فريدة: غير مغطاة بشكل جيد من قبل مشاريع أخرى."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "قابلة للوصول: لا تستخدم الكثير من طبقات الحماية لمنعك من استخراج metadata والبيانات الخاصة بهم."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "رؤية خاصة: لديك بعض المعلومات الخاصة حول هذا الهدف، مثل أنك لديك وصول خاص إلى هذه المجموعة، أو اكتشفت كيفية التغلب على دفاعاتهم. هذا ليس مطلوبًا (مشروعنا القادم لا يقوم بأي شيء خاص)، لكنه بالتأكيد يساعد!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "عندما وجدنا كتبنا الدراسية العلمية على مواقع أخرى غير Library Genesis، حاولنا معرفة كيف وصلت إلى الإنترنت. ثم وجدنا مكتبة الزّاي، وأدركنا أنه بينما لا تظهر معظم الكتب هناك أولاً، فإنها في النهاية تصل إلى هناك. تعلمنا عن علاقتها بـ Library Genesis، وهيكل الحوافز (المالية) وواجهة المستخدم المتفوقة، وكلاهما جعلاها مجموعة أكثر اكتمالاً. ثم قمنا ببعض عمليات استخراج metadata والبيانات الأولية، وأدركنا أنه يمكننا تجاوز حدود تنزيل IP الخاصة بهم، مستفيدين من الوصول الخاص لأحد أعضائنا إلى الكثير من خوادم البروكسي."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "أثناء استكشافك لأهداف مختلفة، من المهم بالفعل إخفاء آثارك باستخدام VPNs وعناوين البريد الإلكتروني المؤقتة، والتي سنتحدث عنها أكثر لاحقًا."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. استخراج metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "لنصبح أكثر تقنية هنا. لاستخراج metadata من المواقع، أبقينا الأمور بسيطة. نستخدم سكربتات بايثون، وأحيانًا curl، وقاعدة بيانات MySQL لتخزين النتائج فيها. لم نستخدم أي برامج استخراج متقدمة يمكنها رسم خرائط المواقع المعقدة، حيث أننا حتى الآن احتجنا فقط لاستخراج نوع أو نوعين من الصفحات عن طريق التعداد عبر المعرفات وتحليل HTML. إذا لم تكن هناك صفحات يمكن تعدادها بسهولة، فقد تحتاج إلى زاحف مناسب يحاول العثور على جميع الصفحات."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "قبل أن تبدأ في استخراج موقع كامل، حاول القيام بذلك يدويًا لفترة. قم بزيارة بضع عشرات من الصفحات بنفسك، لتتعرف على كيفية عمل ذلك. أحيانًا ستواجه حظر IP أو سلوكًا مثيرًا للاهتمام بهذه الطريقة. ينطبق الأمر نفسه على استخراج البيانات: قبل التعمق في هذا الهدف، تأكد من أنك تستطيع بالفعل تنزيل بياناته بفعالية."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "لتجاوز القيود، هناك بعض الأشياء التي يمكنك تجربتها. هل هناك أي عناوين IP أو خوادم أخرى تستضيف نفس البيانات ولكن لا تحتوي على نفس القيود؟ هل هناك أي نقاط نهاية API لا تحتوي على قيود، بينما تحتوي الأخرى على قيود؟ عند أي معدل تنزيل يتم حظر IP الخاص بك، ولأي مدة؟ أو هل لا يتم حظرك ولكن يتم تقليل السرعة؟ ماذا لو أنشأت حساب مستخدم، كيف تتغير الأمور بعد ذلك؟ هل يمكنك استخدام HTTP/2 للحفاظ على الاتصالات مفتوحة، وهل يزيد ذلك من معدل طلب الصفحات؟ هل هناك صفحات تسرد ملفات متعددة في وقت واحد، وهل المعلومات المدرجة هناك كافية؟"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "الأشياء التي ربما تريد حفظها تشمل:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "العنوان"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "اسم الملف / الموقع"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "المعرف: يمكن أن يكون معرف داخلي، لكن المعرفات مثل ISBN أو DOI مفيدة أيضًا."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "الحجم: لحساب مقدار مساحة القرص التي تحتاجها."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "الهاش (md5، sha1): للتأكد من أنك قمت بتنزيل الملف بشكل صحيح."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "تاريخ الإضافة/التعديل: حتى تتمكن من العودة لاحقًا وتنزيل الملفات التي لم تقم بتنزيلها من قبل (على الرغم من أنه يمكنك غالبًا أيضًا استخدام المعرف أو الهاش لهذا)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "الوصف، الفئة، العلامات، المؤلفون، اللغة، إلخ."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "نقوم عادةً بذلك على مرحلتين. أولاً نقوم بتنزيل ملفات HTML الخام، عادةً مباشرة إلى MySQL (لتجنب الكثير من الملفات الصغيرة، والتي نتحدث عنها أكثر أدناه). ثم، في خطوة منفصلة، نقوم بمرور تلك الملفات HTML وتحليلها إلى جداول MySQL الفعلية. بهذه الطريقة لا تحتاج إلى إعادة تنزيل كل شيء من البداية إذا اكتشفت خطأ في كود التحليل الخاص بك، حيث يمكنك فقط إعادة معالجة ملفات HTML بالكود الجديد. كما أنه غالبًا ما يكون من الأسهل موازاة خطوة المعالجة، مما يوفر بعض الوقت (ويمكنك كتابة كود المعالجة أثناء تشغيل الاستخراج، بدلاً من الاضطرار إلى كتابة كلا الخطوتين في وقت واحد)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "أخيرًا، لاحظ أن بالنسبة لبعض الأهداف، فإن جمع بيانات metadata هو كل ما يمكن القيام به. هناك بعض مجموعات metadata الضخمة التي لم يتم حفظها بشكل صحيح."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. اختيار البيانات"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "غالبًا يمكنك استخدام metadata لتحديد مجموعة فرعية معقولة من البيانات لتنزيلها. حتى إذا كنت ترغب في النهاية في تنزيل جميع البيانات، فقد يكون من المفيد إعطاء الأولوية للعناصر الأكثر أهمية أولاً، في حال تم اكتشافك وتحسين الدفاعات، أو لأنك قد تحتاج إلى شراء المزيد من الأقراص، أو ببساطة لأن شيئًا آخر قد يحدث في حياتك قبل أن تتمكن من تنزيل كل شيء."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "على سبيل المثال، قد تحتوي مجموعة على إصدارات متعددة من نفس المورد الأساسي (مثل كتاب أو فيلم)، حيث يتم تمييز أحدها بأنه ذو جودة أفضل. سيكون من المنطقي حفظ تلك الإصدارات أولاً. قد ترغب في النهاية في حفظ جميع الإصدارات، حيث في بعض الحالات قد يتم تصنيف metadata بشكل غير صحيح، أو قد تكون هناك تنازلات غير معروفة بين الإصدارات (على سبيل المثال، قد يكون \"أفضل إصدار\" هو الأفضل في معظم النواحي ولكنه أسوأ في نواحٍ أخرى، مثل أن يكون للفيلم دقة أعلى ولكنه يفتقد إلى الترجمة)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "يمكنك أيضًا البحث في قاعدة بيانات metadata الخاصة بك للعثور على أشياء مثيرة للاهتمام. ما هو أكبر ملف مستضاف، ولماذا هو كبير جدًا؟ ما هو أصغر ملف؟ هل هناك أنماط مثيرة للاهتمام أو غير متوقعة عندما يتعلق الأمر بفئات معينة، لغات، وهكذا؟ هل هناك عناوين مكررة أو متشابهة جدًا؟ هل هناك أنماط لمتى تمت إضافة البيانات، مثل يوم واحد تمت فيه إضافة العديد من الملفات دفعة واحدة؟ يمكنك غالبًا تعلم الكثير من خلال النظر إلى مجموعة البيانات بطرق مختلفة."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "في حالتنا، قمنا بإزالة التكرار بين كتب مكتبة الزّاي و md5 hashes في Library Genesis، مما وفر الكثير من وقت التنزيل ومساحة القرص. هذه حالة فريدة إلى حد ما. في معظم الحالات، لا توجد قواعد بيانات شاملة للملفات التي تم حفظها بشكل صحيح بالفعل من قبل القراصنة الآخرين. هذا في حد ذاته فرصة كبيرة لشخص ما هناك. سيكون من الرائع الحصول على نظرة عامة محدثة بانتظام عن أشياء مثل الموسيقى والأفلام التي يتم توزيعها بشكل واسع على مواقع التورنت، وبالتالي تكون ذات أولوية أقل لتضمينها في العاكسات القرصانية."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. جمع البيانات"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "الآن أنت جاهز لتنزيل البيانات بكميات كبيرة. كما ذكرنا سابقًا، في هذه المرحلة يجب أن تكون قد قمت بالفعل بتنزيل مجموعة من الملفات يدويًا، لفهم سلوك وقيود الهدف بشكل أفضل. ومع ذلك، لا يزال هناك مفاجآت في انتظارك بمجرد أن تبدأ في تنزيل الكثير من الملفات دفعة واحدة."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "نصيحتنا هنا هي أن تبقي الأمور بسيطة. ابدأ فقط بتنزيل مجموعة من الملفات. يمكنك استخدام Python، ثم التوسع إلى عدة خيوط. ولكن في بعض الأحيان يكون الأمر أبسط حتى من ذلك، وهو إنشاء ملفات Bash مباشرة من قاعدة البيانات، ثم تشغيل عدة منها في نوافذ طرفية متعددة لتوسيع النطاق. حيلة تقنية سريعة تستحق الذكر هنا هي استخدام OUTFILE في MySQL، والتي يمكنك كتابتها في أي مكان إذا قمت بتعطيل \"secure_file_priv\" في mysqld.cnf (وتأكد أيضًا من تعطيل/تجاوز AppArmor إذا كنت تستخدم Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "نحن نخزن البيانات على أقراص صلبة بسيطة. ابدأ بما لديك، وتوسع ببطء. قد يكون من المرهق التفكير في تخزين مئات التيرابايت من البيانات. إذا كان هذا هو الوضع الذي تواجهه، فقط ضع مجموعة جيدة أولاً، وفي إعلانك اطلب المساعدة في تخزين الباقي. إذا كنت ترغب في الحصول على المزيد من الأقراص الصلبة بنفسك، فإن r/DataHoarder يحتوي على بعض الموارد الجيدة للحصول على صفقات جيدة."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "حاول ألا تقلق كثيرًا بشأن أنظمة الملفات الفاخرة. من السهل الوقوع في حفرة إعداد أشياء مثل ZFS. ولكن هناك تفصيل تقني يجب أن تكون على دراية به، وهو أن العديد من أنظمة الملفات لا تتعامل بشكل جيد مع الكثير من الملفات. لقد وجدنا أن الحل البسيط هو إنشاء أدلة متعددة، على سبيل المثال لمجالات معرفات مختلفة أو بادئات تجزئة."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "بعد تنزيل البيانات، تأكد من التحقق من سلامة الملفات باستخدام التجزئات في metadata، إذا كانت متوفرة."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. التوزيع"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "لديك البيانات، مما يمنحك حيازة أول عاكسة قرصانية في العالم لهدفك (على الأرجح). في العديد من النواحي، انتهى الجزء الأصعب، لكن الجزء الأكثر خطورة لا يزال أمامك. بعد كل شيء، حتى الآن كنت متخفيًا؛ تطير تحت الرادار. كل ما كان عليك فعله هو استخدام VPN جيد طوال الوقت، وعدم ملء تفاصيلك الشخصية في أي نماذج (بديهي)، وربما استخدام جلسة متصفح خاصة (أو حتى جهاز كمبيوتر مختلف)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "الآن عليك توزيع البيانات. في حالتنا، أردنا أولاً المساهمة بالكتب مرة أخرى إلى Library Genesis، لكننا اكتشفنا بسرعة الصعوبات في ذلك (تصنيف الخيال مقابل غير الخيال). لذلك قررنا التوزيع باستخدام تورنتات على نمط Library Genesis. إذا كانت لديك الفرصة للمساهمة في مشروع قائم، فقد يوفر لك ذلك الكثير من الوقت. ومع ذلك، لا توجد العديد من العاكسات القرصانية المنظمة بشكل جيد حاليًا."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "لذا لنفترض أنك قررت توزيع التورنتات بنفسك. حاول أن تبقي تلك الملفات صغيرة، حتى يسهل عكسها على مواقع أخرى. سيتعين عليك بعد ذلك بذور التورنتات بنفسك، مع البقاء مجهول الهوية. يمكنك استخدام VPN (مع أو بدون توجيه المنافذ)، أو الدفع باستخدام عملات البيتكوين المجمعة للحصول على Seedbox. إذا كنت لا تعرف ما تعنيه بعض هذه المصطلحات، فستحتاج إلى قراءة الكثير، حيث من المهم أن تفهم المخاطر هنا."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "يمكنك استضافة ملفات التورنت نفسها على مواقع التورنت الحالية. في حالتنا، اخترنا استضافة موقع ويب فعليًا، حيث أردنا أيضًا نشر فلسفتنا بطريقة واضحة. يمكنك القيام بذلك بنفسك بطريقة مماثلة (نحن نستخدم Njalla لنطاقاتنا واستضافتنا، مدفوعة بعملات البيتكوين المجمعة)، ولكن لا تتردد في الاتصال بنا لنستضيف تورنتاتك. نحن نسعى لبناء فهرس شامل للعاكسات القرصانية بمرور الوقت، إذا لاقت هذه الفكرة رواجًا."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "أما بالنسبة لاختيار VPN، فقد كُتب الكثير عن هذا بالفعل، لذا سنكرر فقط النصيحة العامة بالاختيار بناءً على السمعة. السياسات الفعلية التي تم اختبارها في المحاكم بعدم الاحتفاظ بالسجلات مع سجلات طويلة في حماية الخصوصية هي الخيار الأقل خطورة، في رأينا. لاحظ أنه حتى عندما تقوم بكل شيء بشكل صحيح، لا يمكنك الوصول إلى مستوى صفر من المخاطر. على سبيل المثال، عند بذور التورنتات الخاصة بك، يمكن لجهة فاعلة ذات دوافع عالية من الدولة أن تنظر في تدفقات البيانات الواردة والصادرة لخوادم VPN، وتستنتج من أنت. أو يمكنك ببساطة أن تخطئ بطريقة ما. ربما قد فعلنا ذلك بالفعل، وسنفعل ذلك مرة أخرى. لحسن الحظ، الدول لا تهتم كثيرًا بالقرصنة."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "أحد القرارات التي يجب اتخاذها لكل مشروع، هو ما إذا كنت ستنشره باستخدام نفس الهوية كما كان من قبل، أم لا. إذا واصلت استخدام نفس الاسم، فقد تعود الأخطاء في الأمان التشغيلي من المشاريع السابقة لتؤذيك. ولكن النشر بأسماء مختلفة يعني أنك لا تبني سمعة طويلة الأمد. اخترنا أن يكون لدينا أمان تشغيلي قوي من البداية حتى نتمكن من الاستمرار في استخدام نفس الهوية، لكننا لن نتردد في النشر تحت اسم مختلف إذا أخطأنا أو إذا دعت الظروف إلى ذلك."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "نشر الكلمة يمكن أن يكون صعبًا. كما قلنا، هذه لا تزال مجتمعًا متخصصًا. نشرنا في الأصل على Reddit، لكننا حققنا نجاحًا حقيقيًا على Hacker News. في الوقت الحالي، توصيتنا هي نشرها في بعض الأماكن ورؤية ما يحدث. ومرة أخرى، اتصل بنا. نود نشر كلمة المزيد من جهود الأرشفة القرصانية."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "الخاتمة"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "نأمل أن يكون هذا مفيدًا للأرشيفيين القراصنة الجدد. نحن متحمسون للترحيب بكم في هذا العالم، فلا تترددوا في التواصل معنا. دعونا نحافظ على أكبر قدر ممكن من المعرفة والثقافة العالمية، ونعكسها على نطاق واسع."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "تقديم عاكسة المكتبة القرصانية: الحفاظ على 7 تيرابايت من الكتب (التي ليست في Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "يهدف هذا المشروع (تم نقله إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>) إلى المساهمة في الحفاظ على المعرفة البشرية وتحريرها. نحن نقدم مساهمتنا الصغيرة والمتواضعة، على خطى العظماء الذين سبقونا."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "يتم توضيح تركيز هذا المشروع من خلال اسمه:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>قرصان</strong> - نحن نتعمد انتهاك قانون حقوق الطبع والنشر في معظم البلدان. هذا يسمح لنا بفعل شيء لا يمكن للكيانات القانونية القيام به: التأكد من أن الكتب يتم عكسها على نطاق واسع."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>مكتبة</strong> - مثل معظم المكتبات، نركز بشكل أساسي على المواد المكتوبة مثل الكتب. قد نتوسع في أنواع أخرى من الوسائط في المستقبل."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>عاكسة</strong> - نحن عاكسة بحتة للمكتبات الموجودة. نركز على الحفظ، وليس على جعل الكتب قابلة للبحث والتنزيل بسهولة (الوصول) أو تعزيز مجتمع كبير من الأشخاص الذين يساهمون بكتب جديدة (المصدر)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "أول مكتبة قمنا بعكسها هي مكتبة الزّاي. هذه مكتبة شهيرة (وغير قانونية). لقد أخذوا مجموعة Library Genesis وجعلوها قابلة للبحث بسهولة. بالإضافة إلى ذلك، أصبحوا فعالين جدًا في طلب مساهمات الكتب الجديدة، من خلال تحفيز المستخدمين المساهمين بمزايا مختلفة. حاليًا، لا يساهمون بهذه الكتب الجديدة مرة أخرى في Library Genesis. وعلى عكس Library Genesis، لا يجعلون مجموعتهم قابلة للعكس بسهولة، مما يمنع الحفظ الواسع. هذا مهم لنموذج أعمالهم، حيث يفرضون رسومًا على الوصول إلى مجموعتهم بشكل كبير (أكثر من 10 كتب في اليوم)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "نحن لا نصدر أحكامًا أخلاقية بشأن فرض رسوم مالية للوصول الجماعي إلى مجموعة كتب غير قانونية. لا شك أن مكتبة الزّاي قد نجحت في توسيع الوصول إلى المعرفة وتوفير المزيد من الكتب. نحن هنا ببساطة لنقوم بدورنا: ضمان الحفاظ طويل الأمد على هذه المجموعة الخاصة."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "نود دعوتكم للمساعدة في الحفاظ على المعرفة البشرية وتحريرها عن طريق تنزيل وتوزيع التورنتات الخاصة بنا. راجع صفحة المشروع لمزيد من المعلومات حول كيفية تنظيم البيانات."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "نود أيضًا دعوتكم للمساهمة بأفكاركم حول المجموعات التي يجب عكسها بعد ذلك، وكيفية القيام بذلك. معًا يمكننا تحقيق الكثير. هذه مجرد مساهمة صغيرة بين العديد من المساهمات الأخرى. شكرًا لكم، على كل ما تفعلونه."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>نحن لا نربط الملفات من هذه المدونة. يرجى العثور عليها بنفسك.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "تفريغ ISBNdb، أو كم عدد الكتب التي يتم حفظها إلى الأبد؟"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "إذا كنا سنقوم بإزالة التكرار بشكل صحيح من المكتبات الظلية، فما هي النسبة المئوية من جميع الكتب في العالم التي قمنا بحفظها؟"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "مع عاكسة المكتبة القرصانية (تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>)، هدفنا هو أخذ جميع الكتب في العالم، وحفظها إلى الأبد.<sup>1</sup> بين تورنتات مكتبة الزّاي، وتورنتات Library Genesis الأصلية، لدينا 11,783,153 ملفًا. لكن كم هو ذلك حقًا؟ إذا قمنا بإزالة التكرار بشكل صحيح من تلك الملفات، فما هي النسبة المئوية من جميع الكتب في العالم التي قمنا بحفظها؟ نود حقًا أن يكون لدينا شيء مثل هذا:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% o من التراث المكتوب للبشرية محفوظ إلى الأبد"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "للحصول على نسبة مئوية، نحتاج إلى مقام: العدد الإجمالي للكتب التي تم نشرها على الإطلاق.<sup>2</sup> قبل زوال Google Books، حاول مهندس في المشروع، ليونيد تايشر، <a %(booksearch_blogspot)s>تقدير</a> هذا الرقم. توصل — بشكل ساخر — إلى 129,864,880 (\"على الأقل حتى يوم الأحد\"). قدر هذا الرقم من خلال بناء قاعدة بيانات موحدة لجميع الكتب في العالم. لهذا، جمع مجموعات بيانات مختلفة ثم دمجها بطرق متنوعة."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "بالمناسبة، هناك شخص آخر حاول فهرسة جميع الكتب في العالم: آرون شوارتز، الناشط الرقمي الراحل وأحد مؤسسي Reddit.<sup>3</sup> لقد <a %(youtube)s>بدأ المكتبة المفتوحة</a> بهدف \"صفحة ويب لكل كتاب تم نشره على الإطلاق\"، حيث جمع البيانات من مصادر مختلفة. انتهى به الأمر بدفع الثمن النهائي لعمله في الحفاظ الرقمي عندما تم محاكمته لتنزيله الأكاديمي بالجملة، مما أدى إلى انتحاره. لا حاجة للقول، هذا أحد الأسباب التي تجعل مجموعتنا تستخدم الأسماء المستعارة، ولماذا نحن حذرون جدًا. لا تزال المكتبة المفتوحة تُدار بشكل بطولي من قبل الأشخاص في أرشيف الإنترنت، مما يواصل إرث آرون. سنعود إلى هذا لاحقًا في هذا المنشور."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "في منشور مدونة جوجل، يصف تايشر بعض التحديات في تقدير هذا الرقم. أولاً، ما الذي يشكل كتابًا؟ هناك بعض التعريفات الممكنة:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>النسخ المادية.</strong> من الواضح أن هذا ليس مفيدًا جدًا، لأنها مجرد نسخ مكررة من نفس المادة. سيكون من الرائع إذا استطعنا الحفاظ على جميع التعليقات التي يكتبها الناس في الكتب، مثل \"الخربشات في الهوامش\" الشهيرة لفيرمات. لكن للأسف، سيظل ذلك حلمًا للأرشيفيين."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“الأعمال”.</strong> على سبيل المثال \"هاري بوتر وغرفة الأسرار\" كمفهوم منطقي، يشمل جميع نسخه، مثل الترجمات المختلفة وإعادة الطبع. هذا نوع من التعريف المفيد، لكنه يمكن أن يكون صعبًا في تحديد ما الذي يُعتبر. على سبيل المثال، ربما نريد الحفاظ على الترجمات المختلفة، رغم أن إعادة الطبع مع اختلافات طفيفة قد لا تكون بنفس الأهمية."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“الإصدارات”.</strong> هنا تحسب كل نسخة فريدة من الكتاب. إذا كان هناك أي شيء مختلف فيها، مثل غلاف مختلف أو مقدمة مختلفة، فإنها تُعتبر إصدارًا مختلفًا."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>الملفات.</strong> عند العمل مع مكتبات الظل مثل Library Genesis وSci-Hub وZ-Library، هناك اعتبار إضافي. يمكن أن يكون هناك مسح ضوئي متعدد لنفس الإصدار. ويمكن للناس إنشاء نسخ أفضل من الملفات الموجودة، عن طريق مسح النص باستخدام OCR، أو تصحيح الصفحات التي تم مسحها بزاوية. نريد أن نحسب هذه الملفات كإصدار واحد فقط، مما يتطلب metadata جيدة، أو إزالة التكرار باستخدام مقاييس تشابه الوثائق."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“الإصدارات” تبدو التعريف الأكثر عملية لماهية “الكتب”. بشكل ملائم، يُستخدم هذا التعريف أيضًا لتخصيص أرقام ISBN الفريدة. رقم ISBN، أو الرقم الدولي المعياري للكتاب، يُستخدم عادة في التجارة الدولية، لأنه مدمج مع نظام الباركود الدولي (\"الرقم الدولي للمقالة\"). إذا كنت تريد بيع كتاب في المتاجر، فإنه يحتاج إلى باركود، لذا تحصل على ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "يذكر منشور مدونة تايشر أنه بينما تكون أرقام ISBN مفيدة، إلا أنها ليست شاملة، لأنها لم تُعتمد فعليًا إلا في منتصف السبعينيات، وليس في كل مكان حول العالم. ومع ذلك، فإن ISBN هو على الأرجح المعرف الأكثر استخدامًا لإصدارات الكتب، لذا فهو أفضل نقطة انطلاق لدينا. إذا استطعنا العثور على جميع أرقام ISBN في العالم، نحصل على قائمة مفيدة بالكتب التي لا تزال بحاجة إلى الحفاظ عليها."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "إذًا، من أين نحصل على البيانات؟ هناك عدد من الجهود القائمة التي تحاول تجميع قائمة بجميع الكتب في العالم:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>جوجل.</strong> بعد كل شيء، قاموا بهذا البحث من أجل كتب جوجل. ومع ذلك، فإن metadata الخاصة بهم ليست متاحة بشكل جماعي وصعبة الكشط."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>المكتبة المفتوحة.</strong> كما ذُكر سابقًا، هذه هي مهمتهم بالكامل. لقد حصلوا على كميات هائلة من بيانات المكتبات من المكتبات المتعاونة والأرشيفات الوطنية، ويواصلون القيام بذلك. لديهم أيضًا أمناء مكتبات متطوعون وفريق تقني يحاول إزالة التكرار من السجلات، ووضع علامات عليها بجميع أنواع metadata. والأفضل من ذلك كله، أن مجموعة بياناتهم مفتوحة تمامًا. يمكنك ببساطة <a %(openlibrary)s>تحميلها</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> هذا موقع تديره منظمة غير ربحية OCLC، التي تبيع أنظمة إدارة المكتبات. يجمعون metadata الكتب من الكثير من المكتبات، ويجعلونها متاحة من خلال موقع WorldCat. ومع ذلك، فإنهم يربحون أيضًا من بيع هذه البيانات، لذا فهي ليست متاحة للتنزيل الجماعي. لديهم بعض مجموعات البيانات الجماعية المحدودة المتاحة للتنزيل، بالتعاون مع مكتبات محددة."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> هذا هو موضوع منشور المدونة هذا. يقوم ISBNdb بكشط مواقع ويب مختلفة للحصول على metadata الكتب، وخاصة بيانات التسعير، التي يبيعونها بعد ذلك لبائعي الكتب، حتى يتمكنوا من تسعير كتبهم بما يتماشى مع بقية السوق. نظرًا لأن أرقام ISBN أصبحت شاملة إلى حد ما في الوقت الحاضر، فقد بنوا فعليًا \"صفحة ويب لكل كتاب\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>أنظمة المكتبات الفردية والأرشيفات المختلفة.</strong> هناك مكتبات وأرشيفات لم يتم فهرستها وتجميعها من قبل أي من المذكورين أعلاه، غالبًا لأنها تعاني من نقص التمويل، أو لأسباب أخرى لا ترغب في مشاركة بياناتها مع المكتبة المفتوحة، OCLC، جوجل، وما إلى ذلك. الكثير من هذه المكتبات لديها سجلات رقمية متاحة عبر الإنترنت، وغالبًا ما لا تكون محمية بشكل جيد، لذا إذا كنت ترغب في المساعدة والاستمتاع بتعلم أنظمة المكتبات الغريبة، فهذه نقاط انطلاق رائعة."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "في هذا المنشور، نحن سعداء بالإعلان عن إصدار صغير (مقارنة بإصدارات مكتبة الزّاي السابقة). لقد قمنا بكشط معظم ISBNdb، وجعلنا البيانات متاحة للتورنت على موقع مكتبة القراصنة العاكسة (تعديل: تم نقلها إلى <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>؛ لن نقوم بربطها هنا مباشرة، فقط ابحث عنها). هذه حوالي 30.9 مليون سجل (20 جيجابايت كـ <a %(jsonlines)s>JSON Lines</a>؛ 4.4 جيجابايت مضغوطة). على موقعهم، يدعون أن لديهم بالفعل 32.6 مليون سجل، لذا قد نكون قد فقدنا بعضًا منها بطريقة ما، أو <em>قد</em> يكونون هم الذين يفعلون شيئًا خاطئًا. في كلتا الحالتين، لن نشارك حاليًا كيف فعلنا ذلك بالضبط — سنترك ذلك كتمرين للقارئ. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "ما سنشاركه هو بعض التحليل الأولي، لمحاولة الاقتراب من تقدير عدد الكتب في العالم. نظرنا إلى ثلاث مجموعات بيانات: مجموعة بيانات ISBNdb الجديدة هذه، إصدارنا الأصلي من metadata الذي قمنا بكشطه من مكتبة الظل ز-لايبراري (التي تشمل مكتبة جينيسيس)، وتفريغ بيانات المكتبة المفتوحة."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "لنبدأ ببعض الأرقام التقريبية:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "في كل من مكتبة الزّاي/Libgen والمكتبة المفتوحة، هناك العديد من الكتب أكثر من أرقام ISBN الفريدة. هل يعني ذلك أن الكثير من تلك الكتب ليس لديها أرقام ISBN، أم أن metadata الخاصة بـ ISBN مفقودة ببساطة؟ يمكننا على الأرجح الإجابة على هذا السؤال بمزيج من المطابقة التلقائية بناءً على سمات أخرى (العنوان، المؤلف، الناشر، إلخ)، وجلب المزيد من مصادر البيانات، واستخراج أرقام ISBN من عمليات المسح الفعلية للكتب نفسها (في حالة مكتبة الزّاي/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "كم عدد تلك الأرقام الفريدة من نوعها؟ هذا موضح بشكل أفضل باستخدام مخطط فين:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "لتكون أكثر دقة:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "لقد فوجئنا بمدى قلة التداخل الموجود! يحتوي ISBNdb على عدد هائل من أرقام ISBN التي لا تظهر في مكتبة الزّاي أو Open Library، وينطبق الأمر نفسه (بدرجة أقل ولكن لا تزال كبيرة) على المكتبتين الأخريين. يثير هذا العديد من الأسئلة الجديدة. إلى أي مدى يمكن أن يساعد المطابقة الآلية في تصنيف الكتب التي لم تُصنف بأرقام ISBN؟ هل سيكون هناك الكثير من المطابقات وبالتالي زيادة في التداخل؟ أيضًا، ماذا سيحدث إذا أضفنا مجموعة بيانات رابعة أو خامسة؟ كم من التداخل سنرى حينها؟"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "هذا يعطينا نقطة انطلاق. يمكننا الآن النظر في جميع أرقام ISBN التي لم تكن في مجموعة بيانات مكتبة الزّاي، والتي لا تتطابق مع حقول العنوان/المؤلف أيضًا. يمكن أن يمنحنا ذلك وسيلة للحفاظ على جميع الكتب في العالم: أولاً عن طريق جمع الإنترنت للحصول على المسوحات، ثم الخروج في الحياة الواقعية لمسح الكتب. يمكن حتى تمويل الأخير جماعيًا، أو تحفيزه من خلال \"مكافآت\" من الأشخاص الذين يرغبون في رؤية كتب معينة يتم رقمنتها. كل ذلك قصة لوقت آخر."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "إذا كنت ترغب في المساعدة في أي من هذا - تحليل إضافي؛ جمع المزيد من metadata؛ العثور على المزيد من الكتب؛ تحويل الكتب إلى نصوص باستخدام OCR؛ القيام بذلك لمجالات أخرى (مثل الأوراق، الكتب الصوتية، الأفلام، البرامج التلفزيونية، المجلات) أو حتى جعل بعض هذه البيانات متاحة لأشياء مثل تدريب النماذج اللغوية الكبيرة - يرجى الاتصال بي (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "إذا كنت مهتمًا بشكل خاص بتحليل البيانات، فإننا نعمل على جعل مجموعات البيانات والبرامج النصية الخاصة بنا متاحة بتنسيق أسهل للاستخدام. سيكون من الرائع إذا كان بإمكانك فقط نسخ دفتر ملاحظات وبدء اللعب بهذا."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "أخيرًا، إذا كنت ترغب في دعم هذا العمل، يرجى النظر في تقديم تبرع. هذه عملية تُدار بالكامل من قبل متطوعين، ومساهمتك تحدث فرقًا كبيرًا. كل جزء يساعد. في الوقت الحالي، نقبل التبرعات بالعملات المشفرة؛ انظر صفحة التبرع في رَبيدةُ آنّا."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. لبعض التعريفات المعقولة لـ \"إلى الأبد\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. بالطبع، التراث المكتوب للبشرية هو أكثر بكثير من الكتب، خاصة في الوقت الحاضر. من أجل هذا المنشور وإصداراتنا الأخيرة نركز على الكتب، لكن اهتماماتنا تمتد إلى أبعد من ذلك."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. هناك الكثير مما يمكن قوله عن آرون شوارتز، لكننا أردنا فقط ذكره بإيجاز، لأنه يلعب دورًا محوريًا في هذه القصة. مع مرور الوقت، قد يصادف المزيد من الناس اسمه لأول مرة، ويمكنهم بعد ذلك الغوص في التفاصيل بأنفسهم."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "النافذة الحرجة للمكتبات الظلية"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد، عندما تقترب بالفعل من 1 بيتابايت؟"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>النسخة الصينية 中文版</a>، ناقش على <a %(reddit)s>Reddit</a>، <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "في رَبيدةُ آنّا، غالبًا ما يُسألنا كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد، عندما يكون الحجم الإجمالي يقترب بالفعل من 1 بيتابايت (1000 تيرابايت)، ولا يزال ينمو. في هذه المقالة سننظر في فلسفتنا، ونرى لماذا العقد القادم حاسم لمهمتنا في الحفاظ على معرفة وثقافة البشرية."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>الحجم الإجمالي</a> لمجموعاتنا، خلال الأشهر القليلة الماضية، مقسمًا حسب عدد موزعي التورنت."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "الأولويات"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "لماذا نهتم كثيرًا بالأوراق والكتب؟ دعونا نضع جانبًا اعتقادنا الأساسي في الحفظ بشكل عام - قد نكتب منشورًا آخر حول ذلك. إذًا لماذا الأوراق والكتب تحديدًا؟ الإجابة بسيطة: <strong>كثافة المعلومات</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "لكل ميغابايت من التخزين، يخزن النص المكتوب أكبر قدر من المعلومات من بين جميع الوسائط. بينما نهتم بالمعرفة والثقافة على حد سواء، فإننا نهتم أكثر بالأولى. بشكل عام، نجد تسلسلًا هرميًا لكثافة المعلومات وأهمية الحفظ يبدو تقريبًا هكذا:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "الأوراق الأكاديمية، المجلات، التقارير"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "البيانات العضوية مثل تسلسلات الحمض النووي، بذور النباتات، أو عينات الميكروبات"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "الكتب غير الخيالية"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "برامج العلوم والهندسة"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "بيانات القياس مثل القياسات العلمية، البيانات الاقتصادية، التقارير المؤسسية"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "مواقع العلوم والهندسة، المناقشات عبر الإنترنت"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "المجلات غير الخيالية، الصحف، الكتيبات"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "النصوص غير الخيالية للمحادثات، الوثائقيات، البودكاست"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "البيانات الداخلية من الشركات أو الحكومات (التسريبات)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "سجلات metadata بشكل عام (للأعمال غير الخيالية والخيالية؛ للوسائط الأخرى، الفن، الأشخاص، إلخ؛ بما في ذلك المراجعات)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "البيانات الجغرافية (مثل الخرائط، المسوحات الجيولوجية)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "نصوص الإجراءات القانونية أو القضائية"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "الإصدارات الخيالية أو الترفيهية لكل ما سبق"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "الترتيب في هذه القائمة هو إلى حد ما اعتباطي - بعض العناصر متعادلة أو هناك خلافات داخل فريقنا - وربما ننسى بعض الفئات المهمة. لكن هذا هو تقريباً كيف نحدد الأولويات."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "بعض هذه العناصر مختلفة جداً عن الأخرى بحيث لا نقلق بشأنها (أو يتم الاعتناء بها بالفعل من قبل مؤسسات أخرى)، مثل البيانات العضوية أو البيانات الجغرافية. لكن معظم العناصر في هذه القائمة مهمة بالنسبة لنا."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "عامل كبير آخر في تحديد أولوياتنا هو مدى تعرض عمل معين للخطر. نفضل التركيز على الأعمال التي هي:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "نادرة"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "غير مركزة بشكل فريد"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "معرضة بشكل فريد لخطر التدمير (مثل الحرب، تخفيضات التمويل، الدعاوى القضائية، أو الاضطهاد السياسي)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "أخيراً، نهتم بالحجم. لدينا وقت ومال محدود، لذا نفضل قضاء شهر في إنقاذ 10,000 كتاب بدلاً من 1,000 كتاب - إذا كانت ذات قيمة متساوية تقريباً ومعرضة للخطر."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "المكتبات الظلية"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "هناك العديد من المنظمات التي لديها مهام مشابهة، وأولويات مشابهة. في الواقع، هناك مكتبات، أرشيفات، مختبرات، متاحف، ومؤسسات أخرى مكلفة بالحفاظ على هذا النوع. العديد من هذه المؤسسات ممولة بشكل جيد، من قبل الحكومات، الأفراد، أو الشركات. لكن لديهم نقطة عمياء ضخمة: النظام القانوني."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "هنا يكمن الدور الفريد للمكتبات الظلية، والسبب في وجود رَبيدةُ آنّا. يمكننا القيام بأشياء لا يُسمح للمؤسسات الأخرى القيام بها. الآن، ليس (غالباً) أننا نستطيع أرشفة المواد التي من غير القانوني الحفاظ عليها في أماكن أخرى. لا، من القانوني في العديد من الأماكن بناء أرشيف مع أي كتب، أوراق، مجلات، وهكذا."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "لكن ما تفتقر إليه الأرشيفات القانونية غالبًا هو <strong>التكرار وطول الأمد</strong>. هناك كتب لا يوجد منها سوى نسخة واحدة في مكتبة مادية ما في مكان ما. هناك سجلات metadata محمية من قبل شركة واحدة. هناك صحف محفوظة فقط على الميكروفيلم في أرشيف واحد. يمكن أن تتعرض المكتبات لخفض التمويل، ويمكن أن تفلس الشركات، ويمكن أن تُقصف الأرشيفات وتُحرق حتى الأرض. هذا ليس افتراضياً - يحدث هذا طوال الوقت."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "الشيء الذي يمكننا القيام به بشكل فريد في رَبيدةُ آنّا هو تخزين العديد من النسخ من الأعمال، على نطاق واسع. يمكننا جمع الأوراق والكتب والمجلات والمزيد، وتوزيعها بكميات كبيرة. نقوم حاليًا بذلك من خلال التورنت، لكن التقنيات الدقيقة لا تهم وستتغير بمرور الوقت. الجزء المهم هو توزيع العديد من النسخ في جميع أنحاء العالم. لا يزال هذا الاقتباس من أكثر من 200 عام صحيحًا:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>لا يمكن استعادة المفقود؛ لكن دعونا نحفظ ما تبقى: ليس عن طريق الخزائن والأقفال التي تحميها من أعين الجمهور واستخدامها، في تسليمها إلى هدر الوقت، ولكن عن طريق مثل هذا التكرار للنسخ، الذي يضعها خارج نطاق الحوادث.</q></em><br>— توماس جيفرسون، 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "ملاحظة سريعة حول الملكية العامة. نظرًا لأن رَبيدةُ آنّا تركز بشكل فريد على الأنشطة التي تعتبر غير قانونية في العديد من الأماكن حول العالم، فإننا لا نهتم بالمجموعات المتاحة على نطاق واسع، مثل الكتب العامة. غالبًا ما تهتم الكيانات القانونية بذلك بشكل جيد. ومع ذلك، هناك اعتبارات تجعلنا نعمل أحيانًا على المجموعات المتاحة للجمهور:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "يمكن عرض سجلات metadata بحرية على موقع Worldcat، ولكن لا يمكن تنزيلها بكميات كبيرة (حتى نقوم <a %(worldcat_scrape)s>بكشطها</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "يمكن أن يكون الكود مفتوح المصدر على Github، لكن Github ككل لا يمكن عكسه بسهولة وبالتالي الحفاظ عليه (على الرغم من أنه في هذه الحالة الخاصة هناك نسخ موزعة بشكل كافٍ لمعظم مستودعات الكود)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit مجاني للاستخدام، لكنه وضع مؤخرًا تدابير صارمة ضد الكشط، في أعقاب تدريب LLM الجائع للبيانات (المزيد عن ذلك لاحقًا)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "تكرار النسخ"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "بالعودة إلى سؤالنا الأصلي: كيف يمكننا الادعاء بالحفاظ على مجموعاتنا إلى الأبد؟ المشكلة الرئيسية هنا هي أن مجموعتنا كانت <a %(torrents_stats)s>تنمو</a> بسرعة كبيرة، من خلال الكشط والمصدر المفتوح لبعض المجموعات الضخمة (بالإضافة إلى العمل الرائع الذي قامت به بالفعل مكتبات الظل المفتوحة البيانات مثل Sci-Hub وLibrary Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "هذا النمو في البيانات يجعل من الصعب عكس المجموعات حول العالم. تخزين البيانات مكلف! لكننا متفائلون، خاصة عند ملاحظة الاتجاهات الثلاثة التالية."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. لقد قطفنا الثمار السهلة"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "هذا يتبع مباشرة من أولوياتنا التي نوقشت أعلاه. نفضل العمل على تحرير المجموعات الكبيرة أولاً. الآن بعد أن قمنا بتأمين بعض أكبر المجموعات في العالم، نتوقع أن يكون نمونا أبطأ بكثير."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "لا يزال هناك ذيل طويل من المجموعات الأصغر، ويتم مسح الكتب الجديدة أو نشرها كل يوم، لكن المعدل سيكون على الأرجح أبطأ بكثير. قد نضاعف أو حتى نضاعف حجمنا ثلاث مرات، ولكن على مدى فترة زمنية أطول."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. تكاليف التخزين تستمر في الانخفاض بشكل كبير"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "في وقت كتابة هذا التقرير، <a %(diskprices)s>أسعار الأقراص</a> لكل تيرابايت حوالي 12 دولارًا للأقراص الجديدة، و8 دولارات للأقراص المستخدمة، و4 دولارات للشريط. إذا كنا محافظين وننظر فقط إلى الأقراص الجديدة، فهذا يعني أن تخزين بيتابايت يكلف حوالي 12,000 دولار. إذا افترضنا أن مكتبتنا ستتضاعف ثلاث مرات من 900 تيرابايت إلى 2.7 بيتابايت، فهذا يعني 32,400 دولار لعكس مكتبتنا بالكامل. بإضافة الكهرباء وتكلفة الأجهزة الأخرى وما إلى ذلك، دعونا نرفعها إلى 40,000 دولار. أو مع الشريط أكثر مثل 15,000–20,000 دولار."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "من ناحية <strong>15,000–40,000 دولار لمجموع المعرفة البشرية هو صفقة رابحة</strong>. من ناحية أخرى، من الصعب توقع الكثير من النسخ الكاملة، خاصة إذا كنا نود أيضًا أن يستمر هؤلاء الأشخاص في تحميل التورنت لصالح الآخرين."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "هذا هو اليوم. لكن التقدم يسير إلى الأمام:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "تم تخفيض تكاليف الأقراص الصلبة لكل تيرابايت إلى الثلث تقريبًا خلال السنوات العشر الماضية، ومن المحتمل أن تستمر في الانخفاض بوتيرة مماثلة. يبدو أن الشريط يسير في مسار مشابه. أسعار SSD تنخفض بشكل أسرع، وقد تتفوق على أسعار HDD بحلول نهاية العقد."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "اتجاهات أسعار HDD من مصادر مختلفة (انقر لعرض الدراسة)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "إذا استمر هذا، فقد ننظر في غضون 10 سنوات إلى 5,000–13,000 دولار فقط لعكس مجموعتنا بالكامل (1/3)، أو حتى أقل إذا نمونا أقل في الحجم. وبينما لا يزال الكثير من المال، سيكون هذا متاحًا للعديد من الأشخاص. وقد يكون الأمر أفضل بسبب النقطة التالية…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. تحسينات في كثافة المعلومات"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "نحن حالياً نخزن الكتب في الصيغ الخام التي تُعطى لنا. بالطبع، هي مضغوطة، ولكن غالباً ما تكون لا تزال مسحاً ضوئياً كبيراً أو صوراً للصفحات."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "حتى الآن، كانت الخيارات الوحيدة لتقليص الحجم الكلي لمجموعتنا هي من خلال ضغط أكثر عدوانية، أو إزالة التكرار. ومع ذلك، للحصول على توفير كبير بما فيه الكفاية، فإن كلاهما يفقد الكثير من الجودة بالنسبة لنا. يمكن أن يجعل الضغط الثقيل للصور النص بالكاد مقروءاً. وتتطلب إزالة التكرار ثقة عالية بأن الكتب هي نفسها تماماً، وهو ما يكون غالباً غير دقيق، خاصة إذا كانت المحتويات هي نفسها ولكن المسح الضوئي تم في مناسبات مختلفة."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "كان هناك دائماً خيار ثالث، لكن جودته كانت سيئة للغاية لدرجة أننا لم نعتبره: <strong>التعرف الضوئي على الحروف (OCR)</strong>. هذه هي عملية تحويل الصور إلى نص عادي، باستخدام الذكاء الاصطناعي لاكتشاف الحروف في الصور. لقد وجدت أدوات لهذا منذ فترة طويلة، وكانت جيدة إلى حد ما، ولكن \"جيدة إلى حد ما\" ليست كافية لأغراض الحفظ."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "ومع ذلك، فإن النماذج الحديثة للتعلم العميق متعددة الوسائط قد أحرزت تقدماً سريعاً للغاية، رغم أنها لا تزال بتكاليف عالية. نتوقع أن تتحسن الدقة والتكاليف بشكل كبير في السنوات القادمة، إلى النقطة التي يصبح فيها من الواقعي تطبيقها على مكتبتنا بأكملها."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "تحسينات التعرف الضوئي على الحروف."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "عندما يحدث ذلك، من المحتمل أن نحافظ على الملفات الأصلية، ولكن بالإضافة إلى ذلك يمكن أن يكون لدينا نسخة أصغر بكثير من مكتبتنا التي سيرغب معظم الناس في عكسها. النقطة المهمة هي أن النص الخام نفسه يضغط بشكل أفضل، ومن الأسهل بكثير إزالة التكرار، مما يوفر لنا المزيد من التوفير."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "بشكل عام، ليس من غير الواقعي توقع تقليل حجم الملفات الإجمالي بنسبة 5-10 مرات على الأقل، وربما أكثر. حتى مع تقليل محافظ بنسبة 5 مرات، سننظر إلى <strong>1000-3000 دولار في 10 سنوات حتى لو تضاعف حجم مكتبتنا ثلاث مرات</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "نافذة حرجة"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "إذا كانت هذه التوقعات دقيقة، فنحن <strong>نحتاج فقط إلى الانتظار لبضع سنوات</strong> قبل أن يتم عكس مجموعتنا بالكامل على نطاق واسع. وبالتالي، في كلمات توماس جيفرسون، \"وضعت خارج نطاق الحوادث\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "لسوء الحظ، فإن ظهور LLMs، وتدريبها الذي يتطلب الكثير من البيانات، قد وضع العديد من أصحاب حقوق الطبع والنشر في موقف دفاعي. أكثر مما كانوا عليه بالفعل. العديد من المواقع تجعل من الصعب جمع البيانات وأرشفتها، والدعاوى القضائية تتطاير، وفي الوقت نفسه تستمر المكتبات والأرشيفات الفيزيائية في الإهمال."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "يمكننا فقط توقع استمرار هذه الاتجاهات في التفاقم، وفقدان العديد من الأعمال قبل أن تدخل المجال العام."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>نحن على أعتاب ثورة في الحفظ، ولكن <q>ما فقد لا يمكن استعادته.</q></strong> لدينا نافذة حرجة لمدة 5-10 سنوات تقريباً حيث لا يزال من المكلف تشغيل مكتبة الظل وإنشاء العديد من العاكسات حول العالم، وحيث لم يتم إغلاق الوصول تماماً بعد."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "إذا تمكنا من تجاوز هذه النافذة، فسنكون قد حفظنا بالفعل معرفة وثقافة البشرية إلى الأبد. يجب ألا ندع هذا الوقت يضيع. يجب ألا ندع هذه النافذة الحرجة تغلق علينا."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "لننطلق."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "الوصول الحصري لشركات LLM إلى أكبر مجموعة كتب غير خيالية صينية في العالم"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>النسخة الصينية 中文版</a>، <a %(news_ycombinator)s>ناقش على Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>ملخص:</strong> رَبيدةُ آنّا حصلت على مجموعة فريدة من 7.5 مليون / 350 تيرابايت من الكتب الصينية غير الخيالية — أكبر من Library Genesis. نحن على استعداد لمنح شركة LLM وصولاً حصرياً، مقابل التعرف الضوئي على الحروف عالي الجودة واستخراج النصوص.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "هذه تدوينة قصيرة. نحن نبحث عن شركة أو مؤسسة لمساعدتنا في التعرف الضوئي على الحروف واستخراج النصوص لمجموعة ضخمة حصلنا عليها، مقابل الوصول الحصري المبكر. بعد فترة الحظر، سنقوم بالطبع بإصدار المجموعة بأكملها."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "النص الأكاديمي عالي الجودة مفيد للغاية لتدريب LLMs. بينما مجموعتنا صينية، يجب أن يكون هذا مفيدًا حتى لتدريب LLMs الإنجليزية: يبدو أن النماذج تشفر المفاهيم والمعرفة بغض النظر عن لغة المصدر."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "لهذا، يجب استخراج النص من المسح الضوئي. ماذا تستفيد رَبيدةُ آنّا من ذلك؟ البحث النصي الكامل في الكتب لمستخدميها."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "لأن أهدافنا تتماشى مع أهداف مطوري LLM، نحن نبحث عن متعاون. نحن على استعداد لمنحك <strong>وصولًا حصريًا مبكرًا إلى هذه المجموعة بكميات كبيرة لمدة عام واحد</strong>، إذا كنت تستطيع القيام بعملية OCR واستخراج النص بشكل صحيح. إذا كنت على استعداد لمشاركة الكود الكامل لخط الأنابيب الخاص بك معنا، سنكون على استعداد لحظر المجموعة لفترة أطول."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "صفحات مثال"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "لإثبات لنا أن لديك خط أنابيب جيد، إليك بعض الصفحات النموذجية للبدء بها، من كتاب عن الموصلات الفائقة. يجب أن يتعامل خط الأنابيب الخاص بك بشكل صحيح مع الرياضيات والجداول والرسوم البيانية والحواشي وما إلى ذلك."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "أرسل صفحاتك المعالجة إلى بريدنا الإلكتروني. إذا كانت تبدو جيدة، سنرسل لك المزيد بشكل خاص، ونتوقع أن تكون قادرًا على تشغيل خط الأنابيب الخاص بك بسرعة على تلك أيضًا. بمجرد أن نكون راضين، يمكننا إبرام صفقة."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "مجموعة"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "بعض المعلومات الإضافية عن المجموعة. <a %(duxiu)s>Duxiu</a> هي قاعدة بيانات ضخمة للكتب الممسوحة ضوئيًا، أنشأتها <a %(chaoxing)s>مجموعة المكتبة الرقمية سوبرستار</a>. معظمها كتب أكاديمية، تم مسحها ضوئيًا لجعلها متاحة رقميًا للجامعات والمكتبات. لجمهورنا الناطق باللغة الإنجليزية، <a %(library_princeton)s>برينستون</a> و<a %(guides_lib_uw)s>جامعة واشنطن</a> لديهما نظرات عامة جيدة. هناك أيضًا مقال ممتاز يقدم المزيد من الخلفية: <a %(doi)s>“رقمنة الكتب الصينية: دراسة حالة لمحرك البحث سوبرستار دوكسيو”</a> (ابحث عنه في رَبيدةُ آنّا)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "تم قرصنة الكتب من Duxiu منذ فترة طويلة على الإنترنت الصيني. عادة ما يتم بيعها بأقل من دولار من قبل البائعين. يتم توزيعها عادة باستخدام ما يعادل Google Drive الصيني، والذي تم اختراقه غالبًا للسماح بمساحة تخزين أكبر. يمكن العثور على بعض التفاصيل التقنية <a %(github_duty_machine)s>هنا</a> و<a %(github_821_github_io)s>هنا</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "على الرغم من أن الكتب تم توزيعها بشكل شبه عام، إلا أنه من الصعب جدًا الحصول عليها بكميات كبيرة. كان لدينا هذا في قائمة المهام الخاصة بنا، وخصصنا عدة أشهر من العمل بدوام كامل لذلك. ومع ذلك، مؤخرًا تواصل معنا متطوع رائع ومذهل وموهوب، وأخبرنا أنه قام بكل هذا العمل بالفعل - بتكلفة كبيرة. شارك المجموعة الكاملة معنا، دون توقع أي شيء في المقابل، باستثناء ضمان الحفظ طويل الأمد. حقًا مذهل. وافقوا على طلب المساعدة بهذه الطريقة للحصول على المجموعة OCR'ed."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "المجموعة تحتوي على 7,543,702 ملف. هذا أكثر من مكتبة جينيسيس للكتب غير الخيالية (حوالي 5.3 مليون). الحجم الإجمالي للملفات حوالي 359 تيرابايت (326 تيبيبايت) في شكلها الحالي."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "نحن منفتحون على مقترحات وأفكار أخرى. فقط تواصل معنا. تحقق من رَبيدةُ آنّا لمزيد من المعلومات حول مجموعاتنا وجهود الحفظ، وكيف يمكنك المساعدة. شكرًا!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "تحذير: تم إهمال هذه التدوينة. لقد قررنا أن IPFS ليست جاهزة بعد للوقت الرئيسي. سنظل نربط الملفات على IPFS من رَبيدةُ آنّا عندما يكون ذلك ممكنًا، لكننا لن نستضيفها بأنفسنا بعد الآن، ولا نوصي الآخرين بعكسها باستخدام IPFS. يرجى الاطلاع على صفحة التورنت الخاصة بنا إذا كنت تريد المساعدة في الحفاظ على مجموعتنا."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "ساعد في توزيع مكتبة الزّاي على IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "كيفية تشغيل مكتبة الظل: العمليات في رَبيدةُ آنّا"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "لا يوجد <q>AWS للجمعيات الخيرية الظل،</q> فكيف ندير رَبيدةُ آنّا؟"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "أدير <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر محرك بحث مفتوح المصدر غير ربحي في العالم لـ<a %(wikipedia_shadow_library)s>مكتبات الظل</a>، مثل Sci-Hub وLibrary Genesis وZ-Library. هدفنا هو جعل المعرفة والثقافة متاحة بسهولة، وفي النهاية بناء مجتمع من الأشخاص الذين يقومون معًا بأرشفة وحفظ <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>جميع الكتب في العالم</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "في هذه المقالة سأوضح كيف ندير هذا الموقع، والتحديات الفريدة التي تأتي مع تشغيل موقع ويب ذو وضع قانوني مشكوك فيه، حيث لا يوجد \"AWS للجمعيات الخيرية الظل\"."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>تحقق أيضًا من المقالة الشقيقة <a %(blog_how_to_become_a_pirate_archivist)s>كيف تصبح أرشيفيًا قرصانًا</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "رموز الابتكار"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "لنبدأ بتقنية البنية التحتية لدينا. إنها مملة بشكل متعمد. نحن نستخدم Flask وMariaDB وElasticSearch. هذا هو كل شيء حرفيًا. البحث هو مشكلة تم حلها إلى حد كبير، ولا ننوي إعادة اختراعها. بالإضافة إلى ذلك، علينا أن ننفق <a %(mcfunley)s>رموز الابتكار</a> الخاصة بنا على شيء آخر: عدم التعرض للإزالة من قبل السلطات."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "إذًا، ما مدى قانونية أو عدم قانونية رَبيدةُ آنّا بالضبط؟ هذا يعتمد في الغالب على الولاية القضائية القانونية. تعتقد معظم الدول في شكل من أشكال حقوق الطبع والنشر، مما يعني أن الأشخاص أو الشركات يتم تعيينهم احتكارًا حصريًا على أنواع معينة من الأعمال لفترة زمنية معينة. كجانب جانبي، في رَبيدةُ آنّا نعتقد أنه بينما هناك بعض الفوائد، فإن حقوق الطبع والنشر بشكل عام هي سلبية صافية للمجتمع - لكن هذه قصة لوقت آخر."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "هذا الاحتكار الحصري على بعض الأعمال يعني أنه من غير القانوني لأي شخص خارج هذا الاحتكار توزيع تلك الأعمال مباشرة - بما في ذلك نحن. لكن رَبيدةُ آنّا هو محرك بحث لا يوزع تلك الأعمال مباشرة (على الأقل ليس على موقعنا على الويب في الشبكة النظيفة)، لذا يجب أن نكون بخير، أليس كذلك؟ ليس بالضبط. في العديد من الولايات القضائية، ليس من غير القانوني فقط توزيع الأعمال المحمية بحقوق الطبع والنشر، ولكن أيضًا الربط بأماكن تقوم بذلك. مثال كلاسيكي على ذلك هو قانون DMCA في الولايات المتحدة."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "هذا هو الطرف الأكثر صرامة من الطيف. في الطرف الآخر من الطيف، يمكن أن تكون هناك دول نظريًا بدون قوانين حقوق الطبع والنشر على الإطلاق، لكن هذه لا توجد حقًا. تقريبًا كل دولة لديها شكل من أشكال قانون حقوق الطبع والنشر في الكتب. التنفيذ هو قصة مختلفة. هناك الكثير من الدول التي لا تهتم حكوماتها بتنفيذ قانون حقوق الطبع والنشر. هناك أيضًا دول بين النقيضين، التي تحظر توزيع الأعمال المحمية بحقوق الطبع والنشر، ولكن لا تحظر الربط بتلك الأعمال."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "اعتبار آخر هو على مستوى الشركة. إذا كانت الشركة تعمل في ولاية قضائية لا تهتم بحقوق الطبع والنشر، ولكن الشركة نفسها ليست مستعدة لتحمل أي مخاطر، فقد تغلق موقعك على الويب بمجرد أن يشتكي أي شخص منه."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "أخيرًا، اعتبار كبير هو المدفوعات. نظرًا لأننا بحاجة إلى البقاء مجهولين، لا يمكننا استخدام طرق الدفع التقليدية. هذا يترك لنا العملات المشفرة، وفقط مجموعة صغيرة من الشركات تدعمها (هناك بطاقات خصم افتراضية مدفوعة بالعملات المشفرة، لكنها غالبًا غير مقبولة)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "هندسة النظام"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "لنفترض أنك وجدت بعض الشركات التي ترغب في استضافة موقعك على الويب دون إغلاقه - دعنا نسميها \"مزودي الحرية المحبين\" 😄. ستجد بسرعة أن استضافة كل شيء معهم مكلفة للغاية، لذا قد ترغب في العثور على بعض \"المزودين الرخيصين\" والقيام بالاستضافة الفعلية هناك، مع التوجيه عبر مزودي الحرية المحبين. إذا قمت بذلك بشكل صحيح، فلن يعرف المزودون الرخيصون أبدًا ما الذي تستضيفه، ولن يتلقوا أي شكاوى."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "مع كل هؤلاء المزودين، هناك خطر من إغلاقهم لك على أي حال، لذا تحتاج أيضًا إلى التكرار. نحن بحاجة إلى ذلك على جميع مستويات البنية التحتية لدينا."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "شركة واحدة محبة للحرية وضعت نفسها في موقف مثير للاهتمام هي Cloudflare. لقد <a %(blog_cloudflare)s>جادلوا</a> بأنهم ليسوا مزود استضافة، بل مرفق، مثل مزود خدمة الإنترنت. لذلك، هم ليسوا خاضعين لطلبات الإزالة بموجب DMCA أو غيرها، ويقومون بإحالة أي طلبات إلى مزود الاستضافة الفعلي الخاص بك. لقد ذهبوا إلى حد الذهاب إلى المحكمة لحماية هذا الهيكل. لذلك يمكننا استخدامهم كطبقة أخرى من التخزين المؤقت والحماية."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare لا تقبل المدفوعات المجهولة، لذا يمكننا فقط استخدام خطتهم المجانية. هذا يعني أننا لا يمكننا استخدام ميزات التوازن في التحميل أو الفشل. لذلك <a %(annas_archive_l255)s>قمنا بتنفيذ ذلك بأنفسنا</a> على مستوى النطاق. عند تحميل الصفحة، سيتحقق المتصفح مما إذا كان النطاق الحالي لا يزال متاحًا، وإذا لم يكن كذلك، فإنه يعيد كتابة جميع عناوين URL إلى نطاق مختلف. نظرًا لأن Cloudflare تخزن العديد من الصفحات مؤقتًا، فهذا يعني أن المستخدم يمكنه الوصول إلى نطاقنا الرئيسي، حتى إذا كان خادم الوكيل معطلاً، ثم في النقر التالي يتم نقله إلى نطاق آخر."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "لا يزال لدينا أيضًا مخاوف تشغيلية عادية للتعامل معها، مثل مراقبة صحة الخادم، وتسجيل الأخطاء في الواجهة الخلفية والأمامية، وما إلى ذلك. تتيح لنا بنية الفشل لدينا مزيدًا من المتانة في هذا الجانب أيضًا، على سبيل المثال عن طريق تشغيل مجموعة مختلفة تمامًا من الخوادم على أحد النطاقات. يمكننا حتى تشغيل إصدارات أقدم من الكود وDatasets على هذا النطاق المنفصل، في حالة عدم ملاحظة خطأ حرج في الإصدار الرئيسي."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "يمكننا أيضًا التحوط ضد تحول Cloudflare ضدنا، عن طريق إزالته من أحد النطاقات، مثل هذا النطاق المنفصل. يمكن تنفيذ تركيبات مختلفة من هذه الأفكار."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "الأدوات"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "دعونا نلقي نظرة على الأدوات التي نستخدمها لتحقيق كل هذا. هذا يتطور بشكل كبير مع مواجهتنا لمشاكل جديدة وإيجاد حلول جديدة."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "خادم التطبيق: Flask وMariaDB وElasticSearch وDocker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "خادم الوكيل: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "إدارة الخادم: Ansible وCheckmk وUFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "التطوير: Gitlab وWeblate وZulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "استضافة ثابتة على Onion: Tor، Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "هناك بعض القرارات التي ترددنا فيها. أحدها هو الاتصال بين الخوادم: كنا نستخدم Wireguard لهذا الغرض، لكن وجدنا أنه يتوقف أحيانًا عن نقل أي بيانات، أو ينقل البيانات في اتجاه واحد فقط. حدث هذا مع عدة إعدادات مختلفة لـ Wireguard التي جربناها، مثل <a %(github_costela_wesher)s>wesher</a> و<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. كما جربنا توجيه المنافذ عبر SSH، باستخدام autossh وsshuttle، لكن واجهنا <a %(github_sshuttle)s>مشاكل هناك</a> (على الرغم من أنه لا يزال غير واضح لي إذا كان autossh يعاني من مشاكل TCP-over-TCP أم لا - يبدو لي كحل غير مثالي ولكن ربما يكون جيدًا بالفعل؟)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "بدلاً من ذلك، عدنا إلى الاتصالات المباشرة بين الخوادم، مع إخفاء أن الخادم يعمل على مزودين رخيصين باستخدام تصفية IP مع UFW. هذا له عيب أن Docker لا يعمل بشكل جيد مع UFW، إلا إذا استخدمت <code>network_mode: \"host\"</code>. كل هذا أكثر عرضة للأخطاء، لأنك ستعرض خادمك للإنترنت مع مجرد خطأ بسيط في التكوين. ربما يجب أن نعود إلى autossh - سيكون من المفيد جدًا الحصول على تعليقات هنا."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "لقد ترددنا أيضًا بين Varnish وNginx. حاليًا نفضل Varnish، لكنه يحتوي على بعض العيوب والحواف الخشنة. ينطبق نفس الشيء على Checkmk: لا نحبه، لكنه يعمل في الوقت الحالي. Weblate كان جيدًا ولكن ليس مذهلاً - أحيانًا أخشى أن يفقد بياناتي كلما حاولت مزامنتها مع مستودع git الخاص بنا. Flask كان جيدًا بشكل عام، لكنه يحتوي على بعض العيوب الغريبة التي كلفت الكثير من الوقت لحلها، مثل تكوين النطاقات المخصصة، أو المشاكل مع تكامل SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "حتى الآن كانت الأدوات الأخرى رائعة: ليس لدينا شكاوى جدية حول MariaDB، ElasticSearch، Gitlab، Zulip، Docker، وTor. جميعها واجهت بعض المشاكل، لكن لا شيء خطير أو مستهلك للوقت بشكل مفرط."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "الخاتمة"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "لقد كانت تجربة مثيرة للاهتمام لتعلم كيفية إعداد محرك بحث مكتبة الظل قوي ومرن. هناك الكثير من التفاصيل لمشاركتها في منشورات لاحقة، لذا دعني أعرف ما الذي تود معرفة المزيد عنه!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "كما هو الحال دائمًا، نحن نبحث عن التبرعات لدعم هذا العمل، لذا تأكد من زيارة صفحة التبرع في رَبيدةُ آنّا. نحن نبحث أيضًا عن أنواع أخرى من الدعم، مثل المنح، الرعاة على المدى الطويل، مزودي الدفع عالي المخاطر، وربما حتى (إعلانات ذوقية!). وإذا كنت ترغب في المساهمة بوقتك ومهاراتك، فنحن دائمًا نبحث عن مطورين، مترجمين، وما إلى ذلك. شكرًا لاهتمامك ودعمك."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- آنّا والفريق (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "مرحبًا، أنا آنّا. أنشأت <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر مكتبة ظل في العالم. هذا هو مدونتي الشخصية، حيث أكتب أنا وفريقي عن القرصنة، الحفظ الرقمي، والمزيد."

#, fuzzy
msgid "blog.index.text2"
msgstr "تواصل معي على <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "يرجى ملاحظة أن هذا الموقع هو مجرد مدونة. نحن نستضيف كلماتنا فقط هنا. لا يتم استضافة أو ربط أي ملفات تورنت أو ملفات محمية بحقوق الطبع والنشر هنا."

#, fuzzy
msgid "blog.index.heading"
msgstr "منشورات المدونة"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3 مليار استخراج من WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "وضع 5,998,794 كتابًا على IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "تحذير: تم إهمال هذه التدوينة. لقد قررنا أن IPFS ليست جاهزة بعد للوقت الرئيسي. سنظل نربط الملفات على IPFS من رَبيدةُ آنّا عندما يكون ذلك ممكنًا، لكننا لن نستضيفها بأنفسنا بعد الآن، ولا نوصي الآخرين بعكسها باستخدام IPFS. يرجى الاطلاع على صفحة التورنت الخاصة بنا إذا كنت تريد المساعدة في الحفاظ على مجموعتنا."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>ملخص:</strong> رَبيدةُ آنّا قامت باستخراج جميع بيانات WorldCat (أكبر مجموعة metadata للمكتبات في العالم) لإنشاء قائمة TODO للكتب التي تحتاج إلى الحفظ.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "قبل عام، <a %(blog)s>بدأنا</a> في الإجابة على هذا السؤال: <strong>ما هي نسبة الكتب التي تم حفظها بشكل دائم بواسطة مكتبات الظل؟</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "بمجرد أن يدخل كتاب إلى مكتبة ظل مفتوحة البيانات مثل <a %(wikipedia_library_genesis)s>Library Genesis</a>، والآن <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، يتم نسخه في جميع أنحاء العالم (عبر التورنت)، مما يحفظه عمليًا إلى الأبد."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "للإجابة على سؤال نسبة الكتب التي تم حفظها، نحتاج إلى معرفة المقام: كم عدد الكتب الموجودة في المجموع؟ ومن المثالي أن لا نحصل فقط على رقم، بل على metadata فعلية. ثم يمكننا ليس فقط مطابقتها مع مكتبات الظل، ولكن أيضًا <strong>إنشاء قائمة TODO للكتب المتبقية للحفظ!</strong> يمكننا حتى أن نحلم بجهد جماعي للانتقال إلى أسفل هذه القائمة."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "قمنا بجمع البيانات من <a %(wikipedia_isbndb_com)s>ISBNdb</a>، وقمنا بتنزيل <a %(openlibrary)s>مجموعة بيانات المكتبة المفتوحة</a>، لكن النتائج لم تكن مرضية. كانت المشكلة الرئيسية هي عدم وجود تداخل كبير في أرقام ISBN. انظر إلى هذا المخطط الفين من <a %(blog)s>منشور مدونتنا</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "لقد فوجئنا جدًا بمدى قلة التداخل بين ISBNdb وOpen Library، وكلاهما يتضمن البيانات بحرية من مصادر متنوعة، مثل جمع البيانات من الويب وسجلات المكتبات. إذا كان كلاهما يقوم بعمل جيد في العثور على معظم أرقام ISBN الموجودة، فإن دوائرهما بالتأكيد ستكون لها تداخل كبير، أو ستكون إحداهما مجموعة فرعية من الأخرى. جعلنا نتساءل، كم عدد الكتب التي تقع <em>تمامًا خارج هذه الدوائر</em>؟ نحن بحاجة إلى قاعدة بيانات أكبر."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "هذا هو الوقت الذي وضعنا فيه أنظارنا على أكبر قاعدة بيانات للكتب في العالم: <a %(wikipedia_worldcat)s>WorldCat</a>. هذه قاعدة بيانات مملوكة من قبل المنظمة غير الربحية <a %(wikipedia_oclc)s>OCLC</a>، التي تجمع سجلات metadata من المكتبات في جميع أنحاء العالم، مقابل منح تلك المكتبات الوصول إلى مجموعة البيانات الكاملة، وظهورها في نتائج البحث للمستخدمين النهائيين."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "على الرغم من أن OCLC هي منظمة غير ربحية، إلا أن نموذج عملهم يتطلب حماية قاعدة بياناتهم. حسنًا، نحن آسفون لنقول، أصدقاؤنا في OCLC، نحن نعطيها كلها. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "على مدار العام الماضي، قمنا بجمع جميع سجلات WorldCat بدقة. في البداية، حصلنا على فرصة محظوظة. كانت WorldCat تقوم بإطلاق إعادة تصميم كاملة لموقعها (في أغسطس 2022). تضمن ذلك تجديدًا كبيرًا لأنظمتهم الخلفية، مما أدى إلى ظهور العديد من الثغرات الأمنية. استغللنا الفرصة على الفور، وتمكنا من جمع مئات الملايين (!) من السجلات في غضون أيام قليلة."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>إعادة تصميم WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "بعد ذلك، تم إصلاح الثغرات الأمنية ببطء واحدة تلو الأخرى، حتى تم تصحيح آخر واحدة وجدناها قبل حوالي شهر. بحلول ذلك الوقت كان لدينا تقريبًا جميع السجلات، وكنا نسعى فقط للحصول على سجلات ذات جودة أعلى قليلاً. لذا شعرنا أنه حان الوقت للإصدار!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "لنلقِ نظرة على بعض المعلومات الأساسية حول البيانات:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>التنسيق؟</strong> <a %(blog)s>حاويات رَبيدةُ آنّا (AAC)</a>، وهي في الأساس <a %(jsonlines)s>JSON Lines</a> مضغوطة باستخدام <a %(zstd)s>Zstandard</a>، بالإضافة إلى بعض الدلالات الموحدة. هذه الحاويات تحتوي على أنواع مختلفة من السجلات، بناءً على عمليات الجمع المختلفة التي قمنا بها."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "البيانات"

msgid "dyn.buy_membership.error.unknown"
msgstr "حدث خطأ غير معروف. ارسل رسالة لـ %(email)s واضمُم في رسالتك لقطة للشاشة.."

msgid "dyn.buy_membership.error.minimum"
msgstr "لهذه العملة حد أدنى أعلى من المعتاد. رجاءً اختر مدّةً أو عملة مختلفة."

msgid "dyn.buy_membership.error.try_again"
msgstr "طلبك لم يُكمَل. حاول مرة أخرى بعد دقائق، وإن استمر هذا، فراسلنا بـ %(email)s واضمُم في رسالتك لقطة للشاشة.."

msgid "dyn.buy_membership.error.wait"
msgstr "خطأ حدث في عملية الدفع. انتظر قليلًا وحاول مرة أخرى. إن استمر معك هذا الخطأ لأكثر من 24 ساعة، فراسلنا بـ%(email)s واضمُم في رسالتك لقطة للشاشة."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "تعليق مخفي"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "مشكلة في الملف: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "نسخة أفضل"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "هل تريد الإبلاغ عن هذا المستخدم لسلوك مسيء أو غير لائق؟"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "الإبلاغ عن إساءة"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "تم الإبلاغ عن الإساءة:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "لقد أبلغت عن هذا المستخدم بسبب الإساءة."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "الرد"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "يرجى <a %(a_login)s>تسجيل الدخول</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "لقد تركت تعليقًا. قد يستغرق الأمر دقيقة ليظهر."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s الصفحات المتأثرة"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "لا تظهر في قسم المحتوى الواقعي من مكتبة التَّكوين Libgen.rs"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "لا تظهر في قسم المحتوى الخيالي من مكتبة التَّكوين Libgen.rs"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "لا تظهر في مكتبة التَّكوين Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "عُلّم عليه «معطّلٌ» في مكتبة التَّكوين Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "مفقود من مكتبة الزّاي"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "مُعلّمة كـ \"سبام\" في مكتبة الزّاي"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "مُعلّمة كـ \"ملف سيء\" في مكتبة الزّاي"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "لم يتم تحويل جميع الصفحات إلى PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "فشل تشغيل exiftool على هذا الملف"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "كتاب (غير معروف)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "كتاب (واقعي)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "كتاب (خيالي)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "مقال أكاديمي"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "وثيقة معايير"

msgid "common.md5_content_type_mapping.magazine"
msgstr "مجلة"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "قصة مصورة"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "مقطوعة موسيقية"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "الكتاب الصوتي"

msgid "common.md5_content_type_mapping.other"
msgstr "آخر"

msgid "common.access_types_mapping.aa_download"
msgstr "تنزيل من خادوم شريك (كمكتبة الزّاي والتكّوين وغيرهما)"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "مجمع البيانات العلمية"

msgid "common.access_types_mapping.external_download"
msgstr "تنزيل خارجي"

msgid "common.access_types_mapping.external_borrow"
msgstr "استعارة خارجية"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "استعارة خارجية (لعُسر القراءة)"

msgid "common.access_types_mapping.meta_explore"
msgstr "استكشف البيانات الوصفية"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "موجودة في التورنتات"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "مكتبة الزّاي ( Z-Library)"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "مكتبة الزّاي الصينية"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "مجمع بيانات النظام القياسي الدولي لترقيم الكتب (ISBNdb)"

msgid "common.record_sources_mapping.ol"
msgstr "المكتبة المفتوحة (OpenLibrary)"

msgid "common.record_sources_mapping.scihub"
msgstr "مجمع العلوم (Sci-Hub)"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat): الفهرس العالمي هو مشروع فهرس موحد، تابع لمركز المكتبة الرقمية على الإنترنت"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "التحميلات إلى AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "فهرس كتب EBSCOhost الإلكترونية"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "البيانات الوصفية التشيكية"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "كتب جوجل"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "جودريدز"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "ليبي"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "المكتبة الوطنية الروسية"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "ترانتور"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "العنوان"

msgid "common.specific_search_fields.author"
msgstr "المؤلف"

msgid "common.specific_search_fields.publisher"
msgstr "الناشر"

msgid "common.specific_search_fields.edition_varia"
msgstr "النسخة"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "سنة النشر"

msgid "common.specific_search_fields.original_filename"
msgstr "الاسم الأصلي للملف"

msgid "common.specific_search_fields.description_comments"
msgstr "الوصف وملاحظات البيانات الوصفية"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "تنزيل هذا الملف من الخادوم الشريك غير متاح مؤقتًا."

msgid "common.md5.servers.fast_partner"
msgstr "خادوم شريك سريع #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(موصى به)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(لا تحقق من المتصفح أو قوائم الانتظار)"

msgid "common.md5.servers.slow_partner"
msgstr "خادوم شريك بطيء #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(أسرع قليلاً ولكن مع قائمة انتظار)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(لا توجد قائمة انتظار، ولكن يمكن أن يكون بطيئًا جدًا)"

msgid "page.md5.box.download.scihub"
msgstr "مجمع العلوم (Sci-Hub): %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "القسم الواقعي من مكتبة التَّكوين Libgen.rs"

msgid "page.md5.box.download.lgrsfic"
msgstr "القسم الخيالي من مكتبة التَّكوين Libgen.rs"

msgid "page.md5.box.download.lgli"
msgstr "مكتبة التَّكوين Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(وانقر على «GET» أعلاه)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(انقر على «GET» أعلاه)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "إعلاناتهم معروفة باحتوائها على برامج ضارة، لذا استخدم مانع الإعلانات أو لا تنقر على الإعلانات"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(قد تكون ملفات Nexus/STC غير موثوقة للتنزيل)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "مكتبة الزّاي"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library على Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(يحتاج إلى متصفح الTor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "استعارة من رَبيدةُ الشّابكة (Internet Archive)"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(للداعمين أصحاب عسر في القراءة فقط)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "معرّفات الأغراض الرقمية المرتبطة بها قد لا تتوفر في مجمع العلوم"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "مجموعة"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "تورنت"

msgid "page.md5.box.download.bulk_torrents"
msgstr "تنزيلات التورنت بالجملة"

msgid "page.md5.box.download.experts_only"
msgstr "(للعارفين فقط)"

msgid "page.md5.box.download.aa_isbn"
msgstr "ابحث عن الردمك في رَبيدة آنّا"

msgid "page.md5.box.download.other_isbn"
msgstr "ابحث عن الردمك في قواعد بيانات أخرى متنوعة"

msgid "page.md5.box.download.original_isbndb"
msgstr "ابحث عن السجل الأصلي في مجمع بيانات النظام القياسي الدولي لترقيم الكتب (ISBNdb)"

msgid "page.md5.box.download.aa_openlib"
msgstr "ابحث عن معرف المكتبة المفتوحة في رَبيدة آنّا"

msgid "page.md5.box.download.original_openlib"
msgstr "ابحث عن السجل الأصلي في المكتبة المفتوحة"

msgid "page.md5.box.download.aa_oclc"
msgstr "ابحث عن رقم الفهرس العالمي في رَبيدة آنّا"

msgid "page.md5.box.download.original_oclc"
msgstr "ابحث عن السجل الأصلي في الفهرس العالمي"

msgid "page.md5.box.download.aa_duxiu"
msgstr "ابحث عن رقم «SSID» للدّوشية في رَبيدة آنّا"

msgid "page.md5.box.download.original_duxiu"
msgstr "ابحث بنفسك في الدّوشية"

msgid "page.md5.box.download.aa_cadal"
msgstr "ابحث عن رقم «SSNO» للمكتبة الرقمية الصينية (CADAL) في رَبيدة آنّا"

msgid "page.md5.box.download.original_cadal"
msgstr "ابحث عن السجل الأصلي في المكتبة الرقمية الصينية (CADAL)"

msgid "page.md5.box.download.aa_dxid"
msgstr "ابحث عن رقم «DXID» للدّوشية في رَبيدة آنّا"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "فهرس كتب EBSCOhost الإلكترونية"

msgid "page.md5.box.download.scidb"
msgstr "رَبيدةُ آنّا 🧬 مجمع البيانات العلمية"

msgid "common.md5.servers.no_browser_verification"
msgstr "(لا يلزم التحقق من المتصفح)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "البيانات الوصفية التشيكية %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "البيانات الوصفية"

msgid "page.md5.box.descr_title"
msgstr "الوصف"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "اسم ملف بديل"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "عنوان بديل"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "مؤلف بديل"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "ناشر بديل"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "إصدار بديل"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "امتداد بديل"

msgid "page.md5.box.metadata_comments_title"
msgstr "تعاليق البيانات الوصفية"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "وصف بديل"

msgid "page.md5.box.date_open_sourced_title"
msgstr "تاريخ فتحها للعامة مجانًا"

msgid "page.md5.header.scihub"
msgstr "ملف مجمع العلوم «%(id)s»"

msgid "page.md5.header.ia"
msgstr "ملف بالإعارة الرقمية المضبوطة لرَبيدة الشّابكة «%(id)s»"

msgid "page.md5.header.ia_desc"
msgstr "هذا سجل لملف من رَبيدة الشّابكة، وغير قابل للتنزيل المباشر. فاستعر الكتاب (من الرابط أدناه)، أول اطلب ملفًا <a %(a_request)s>من هذا الرابط</a>."

msgid "page.md5.header.consider_upload"
msgstr "إن كنت تملك هذا الملف وهو غير موجود في رَبيدة آنّا، فأعنّا و <a %(a_request)s>حمّله</a>."

msgid "page.md5.header.meta_isbn"
msgstr "سجل البيانات الوصفية للمجمع الرقمي لترقيم الكتب %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "سجل البيانات الوصفية للمكتبة المفتوحة %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "سجل البيانات الوصفية للفهرس العالمي (OCLC) %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "سجل بيانات «SSID» الوصفية لدّوشية %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "سجل بيانات «SSNO» الوصفية للمكتبة الرقمية الصينية (CADAL) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "سجل البيانات الوصفية لـ MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "سجل البيانات الوصفية لـ Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "هذا سجل بيانات وصفية، وليس ملفًا للتنزيل. فاطلب ملفًا <a %(a_request)s>من هذا الرابط</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "البيانات الوصفية من السجل المرتبط"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "تحسين البيانات الوصفية على Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "تحذير: سجلات مرتبطة متعددة:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "تحسين البيانات الوصفية"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "الإبلاغ عن جودة الملف"

msgid "page.search.results.download_time"
msgstr "وقت التنزيل"

msgid "page.md5.codes.url"
msgstr "الرابط:"

msgid "page.md5.codes.website"
msgstr "الموقع:"

msgid "page.md5.codes.aa_abbr"
msgstr "رَبيدةُ آنّا:"

msgid "page.md5.codes.aa_search"
msgstr "ابحث في رَبيدة آنّا عن «%(name)s»"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "مستكشف الأكواد:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "عرض في مستكشف الأكواد “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "اقرأ أكثر…"

msgid "page.md5.tabs.downloads"
msgstr "التنزيلات (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "الاستعارات (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "عمليات البحث عن البيانات الوصفية (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "التعليقات (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "القوائم (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "الإحصائيات (%(count)s)"

msgid "common.tech_details"
msgstr "التفاصيل التقنية «بالإنگليزية»"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ قد يكون بهذا الملف مشاكل واُخفيَ من مصدره. </span> وسبب هذا إما لطلب من صاحب حقوق النشر، أو لوجود بديلٍ أفضل، أو لمشكلة في الملف نفسه. ربما أمكنك تنزيله، إلّا أنّنا نوصي بالبحث عن ملف آخر بديل له. للمزيد من التفاصيل:"

msgid "page.md5.box.download.better_file"
msgstr "قد تتوفر نُسخة أفضل من هذا الملف في %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "إن مازلت على رأيك في تنزيله فتأكد من فتحه ببرامج موثوقة ومحدّثة تجنبًا لأية مشكلة."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 تنزيلات سريعة"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong> 🚀 تنزيلات سريعة </strong> <a %(a_membership)s> انضم</a> لدعمنا في حفظ الكتب والرسائل وغيرهما. ولنشكرك نُتيح لك التنزيل أسرع من البقية. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "إذا تبرعت هذا الشهر، ستحصل على <strong>ضعف</strong> عدد التنزيلات السريعة."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 تنزيلات سريعة</strong> مازال عندك %(remaining)s يومًا/أيام. شكرا لانضمامك! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 تنزيلات سريعة</strong> أكملت تنزيلاتك السريعة المسموح لك بها لهذا اليوم."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong> 🚀 تنزيلات سريعة</strong> نزّلت هذا الملف مؤخرًا. الروابط صالحة لفترة معينة."

msgid "page.md5.box.download.option"
msgstr "خيار #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(بدون إعادة توجيه)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(افتح في العارض)"

msgid "layout.index.header.banner.refer"
msgstr "اذكر صديقًا لك ووتحصلان كلاكما على %(percentage)s تنزيلات سريعة إضافية!"

msgid "layout.index.header.learn_more"
msgstr "اعرف المزيد…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 تنزيلات بطيئة"

msgid "page.md5.box.download.trusted_partners"
msgstr "من شركائنا الموثوقين."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "مزيد من المعلومات في <a %(a_slow)s>الأسئلة الشائعة</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(قد يُلزم <a %(a_browser)s> التحقق من المتصفح </a> - تنزيلات غير محدودة!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "بعد التحميل:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "افتح في العارض الخاص بنا"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "عرض التنزيلات الخارجية"

msgid "page.md5.box.download.header_external"
msgstr "تنزيلات خارجية"

msgid "page.md5.box.download.no_found"
msgstr "لا توجد تنزيلات."

msgid "page.md5.box.download.no_issues_notice"
msgstr "كلّ خيارات التنزيل آمنة وبها نفس الملف. إلّا أن الحذر واجب عند تنزيل الملفات من الشّابكة، وخاصة إن كانت من مواقع خارجية لا تتبع رَبيدة آنَّا. فتأكد من تحديث أجهزتك باستمرار كأقل أساليب الحماية."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "بالنسبة للملفات الكبيرة، نوصي باستخدام مدير تنزيل لمنع الانقطاعات."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "مديرو التنزيل الموصى بهم: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "ستحتاج إلى قارئ كتب إلكترونية أو قارئ PDF لفتح الملف، حسب تنسيق الملف."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "قراء الكتب الإلكترونية الموصى بهم: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "عارض رَبيدةُ آنّا عبر الإنترنت"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "استخدم الأدوات عبر الإنترنت للتحويل بين التنسيقات."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "أدوات التحويل الموصى بها: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "يمكنك إرسال ملفات PDF وEPUB إلى جهاز Kindle أو Kobo eReader الخاص بك."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "الأدوات الموصى بها: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "خدمة \"Send to Kindle\" من أمازون"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "خدمة \"Send to Kobo/Kindle\" من djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "دعم المؤلفين والمكتبات"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "إذا أعجبك هذا ويمكنك تحمل تكلفته، فكر في شراء النسخة الأصلية، أو دعم المؤلفين مباشرةً."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "إذا كان هذا متاحًا في مكتبتك المحلية، فكر في استعارة الكتاب مجانًا من هناك."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "جودة الملف"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "ساعد المجتمع بالإبلاغ عن جودة هذا الملف! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "الإبلاغ عن مشكلة في الملف (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "جودة الملف ممتازة (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "إضافة تعليق (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "ما المشكلة في هذا الملف؟"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "يرجى استخدام <a %(a_copyright)s>نموذج مطالبة DMCA / حقوق الطبع والنشر</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "وصف المشكلة (مطلوب)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "وصف المشكلة"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 لنسخة أفضل من هذا الملف (إذا كان ذلك ممكنًا)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "املأ هذا إذا كان هناك ملف آخر يتطابق بشكل وثيق مع هذا الملف (نفس الإصدار، نفس امتداد الملف إذا كان بإمكانك العثور على واحد)، والذي يجب أن يستخدمه الناس بدلاً من هذا الملف. إذا كنت تعرف نسخة أفضل من هذا الملف خارج رَبيدةُ آنّا، فيرجى <a %(a_upload)s>تحميلها</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "يمكنك الحصول على md5 من عنوان URL، على سبيل المثال"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "إرسال التقرير"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "تعلم كيفية <a %(a_metadata)s>تحسين البيانات الوصفية</a> لهذا الملف بنفسك."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "شكرًا لك على تقديم تقريرك. سيتم عرضه على هذه الصفحة، وكذلك مراجعته يدويًا بواسطة آنّا (حتى يكون لدينا نظام مراجعة مناسب)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "إذا كان هذا الملف ذو جودة عالية، يمكنك مناقشة أي شيء عنه هنا! إذا لم يكن كذلك، يرجى استخدام زر \"الإبلاغ عن مشكلة في الملف\"."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "أحببت هذا الكتاب!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "اترك تعليقًا"

msgid "common.english_only"
msgstr "باقي الكتابة باللغة الإنگليزية."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "إجمالي التنزيلات: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "\"ملف MD5\" هو تجزئة يتم حسابها من محتويات الملف، وهي فريدة بشكل معقول بناءً على هذا المحتوى. جميع المكتبات الظلية التي قمنا بفهرستها هنا تستخدم MD5s لتحديد الملفات."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "قد يظهر الملف في مكتبات ظلية متعددة. لمزيد من المعلومات حول مجموعات البيانات المختلفة التي قمنا بتجميعها، راجع <a %(a_datasets)s>صفحة Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "هذا ملف مُدار بواسطة مكتبة <a %(a_ia)s>الإعارة الرقمية المُتحكم بها من IA</a>، ومفهرس بواسطة رَبيدةُ آنّا للبحث. لمزيد من المعلومات حول مجموعات البيانات المختلفة التي قمنا بتجميعها، راجع <a %(a_datasets)s>صفحة Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "لمزيد من المعلومات حول هذا الملف المحدد، تحقق من <a %(a_href)s>ملف JSON</a> الخاص به."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 مشكلة في تحميل هذه الصفحة"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "يرجى التحديث للمحاولة مرة أخرى. <a %(a_contact)s>اتصل بنا</a> إذا استمرت المشكلة لعدة ساعات."

msgid "page.md5.invalid.header"
msgstr "لم يُعثر عليه"

msgid "page.md5.invalid.text"
msgstr "لم يُعثر على %(md5_input)s في قاعدة بياناتنا."

msgid "page.login.title"
msgstr "ادخل / سجّل"

#, fuzzy
msgid "page.browserverification.header"
msgstr "التحقق من المتصفح"

msgid "page.login.text1"
msgstr "لمنع الآليين المزعجين من إنشاء حسابات كثيرة، علينا أولًا التحقق من متصفحك."

#, fuzzy
msgid "page.login.text2"
msgstr "إذا علقت في حلقة لا نهائية، نوصي بتثبيت <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "جرّب إطفاء أدوات حظر الإعلانات وإضافات المتصفح الأخرى فلعلها تكون السبب في هذا."

#, fuzzy
msgid "page.codes.title"
msgstr "الأكواد"

#, fuzzy
msgid "page.codes.heading"
msgstr "مستكشف الأكواد"

#, fuzzy
msgid "page.codes.intro"
msgstr "استكشف الأكواد التي تم وسم السجلات بها، حسب البادئة. يعرض عمود \"السجلات\" عدد السجلات الموسومة بالأكواد ذات البادئة المعطاة، كما هو موضح في محرك البحث (بما في ذلك السجلات التي تحتوي على البيانات الوصفية فقط). يعرض عمود \"الأكواد\" عدد الأكواد الفعلية التي تحتوي على البادئة المعطاة."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "قد تستغرق هذه الصفحة بعض الوقت للتوليد، ولهذا السبب تتطلب كابتشا من Cloudflare. يمكن <a %(a_donate)s>للأعضاء</a> تخطي الكابتشا."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "يرجى عدم استخراج البيانات من هذه الصفحات. بدلاً من ذلك، نوصي <a %(a_import)s>بتوليد</a> أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا، وتشغيل <a %(a_software)s>الكود المفتوح المصدر</a> الخاص بنا. يمكن استكشاف البيانات الخام يدويًا من خلال ملفات JSON مثل <a %(a_json_file)s>هذا الملف</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "بادئة"

#, fuzzy
msgid "common.form.go"
msgstr "اذهب"

#, fuzzy
msgid "common.form.reset"
msgstr "إعادة تعيين"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "ابحث في رَبيدةُ آنّا"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "تحذير: يحتوي الكود على أحرف Unicode غير صحيحة، وقد يتصرف بشكل غير صحيح في مواقف مختلفة. يمكن فك تشفير البيانات الثنائية الخام من تمثيل base64 في عنوان URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "بادئة الكود المعروفة “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "بادئة"

#, fuzzy
msgid "page.codes.code_label"
msgstr "تسمية"

#, fuzzy
msgid "page.codes.code_description"
msgstr "وصف"

#, fuzzy
msgid "page.codes.code_url"
msgstr "عنوان URL لكود محدد"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "سيتم استبدال “%%” بقيمة الكود"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "عنوان URL عام"

#, fuzzy
msgid "page.codes.code_website"
msgstr "موقع ويب"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "0 سجلات مطابقة لـ “%(prefix_label)s”"
msgstr[1] "%(count)s سجل مطابق لـ “%(prefix_label)s”"
msgstr[2] "2 سجلات مطابقة لـ “%(prefix_label)s”"
msgstr[3] ""
msgstr[4] ""
msgstr[5] "%(count)s سجلات مطابقة لـ “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "عنوان URL لكود محدد: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "المزيد…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "أكواد تبدأ بـ “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "فهرس"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "سجلات"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "أكواد"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "أقل من %(count)s سجلات"

msgid "page.contact.dmca.form"
msgstr "للمطالبة بحقوق الملكية الرقيمة: املء <a %(a_copyright)s>هذا النموذج</a>."

msgid "page.contact.dmca.delete"
msgstr "وسنحذف كلّ الرسائل التي تصلنا للمطالبة بالحقوق مالم تأتي من هذا النموذج ولن نعتدّ بها."

msgid "page.contact.checkboxes.text1"
msgstr "ونُرحب بملاحظاتكم واستفساراتكم!"

msgid "page.contact.checkboxes.text2"
msgstr "وتصلنا رسائل مزعجة عشوائية كثيرة، فرجاءً علّم على المربعات لنعلم أنك قد فهمت شروط مراسلتنا."

msgid "page.contact.checkboxes.copyright"
msgstr "ولن نرد على أي مطالبات للحقوق على هذا البريد؛ فاملء النموذج إن أردت."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "الخوادم الشريكة غير متاحة بسبب إغلاق الاستضافة. يجب أن تعود للعمل قريبًا."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "سيتم تمديد العضويات وفقًا لذلك."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "لا تراسلنا <a %(a_request)s>لطلب الكتب</a><br>أو لرفع مجموعة قليلة من الكتب (أقل من عشرة آلاف) <a %(a_upload)s></a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "عند طرح أسئلة حول الحساب أو التبرعات، أضف معرف حسابك، لقطات الشاشة، الإيصالات، وأكبر قدر ممكن من المعلومات. نحن نتحقق من بريدنا الإلكتروني كل 1-2 أسبوع، لذا عدم تضمين هذه المعلومات سيؤخر أي حل."

msgid "page.contact.checkboxes.show_email_button"
msgstr "اظهر البريد"

#, fuzzy
msgid "page.copyright.title"
msgstr "نموذج تقديم شكوى حقوق الطبع والنشر / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "إذا كان لديك شكوى DMCA أو شكوى حقوق طبع ونشر أخرى، يرجى ملء هذا النموذج بدقة قدر الإمكان. إذا واجهت أي مشاكل، يرجى الاتصال بنا على عنوان DMCA المخصص: %(email)s. لاحظ أن الشكاوى المرسلة عبر البريد الإلكتروني إلى هذا العنوان لن يتم معالجتها، فهو مخصص للأسئلة فقط. يرجى استخدام النموذج أدناه لتقديم شكاويك."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "عناوين URL على رَبيدةُ آنّا (مطلوبة). واحد في كل سطر. يرجى تضمين عناوين URL التي تصف نفس الطبعة من الكتاب فقط. إذا كنت ترغب في تقديم شكوى لعدة كتب أو عدة طبعات، يرجى تقديم هذا النموذج عدة مرات."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "سيتم رفض الشكاوى التي تجمع بين عدة كتب أو طبعات."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "اسمك (مطلوب)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "العنوان (مطلوب)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "رقم الهاتف (مطلوب)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "البريد الإلكتروني (مطلوب)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "وصف واضح للمادة المصدرية (مطلوب)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "أرقام ISBN للمادة المصدرية (إذا كان ذلك ينطبق). واحد في كل سطر. يرجى تضمين الأرقام التي تتطابق تمامًا مع الطبعة التي تبلغ عنها."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>عناوين Open Library</a> للمادة المصدرية، واحد في كل سطر. يرجى أخذ لحظة للبحث في Open Library عن المادة المصدرية الخاصة بك. سيساعدنا ذلك في التحقق من شكواك."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "عناوين URL للمادة المصدرية، واحد في كل سطر (مطلوب). يرجى تضمين أكبر عدد ممكن، لمساعدتنا في التحقق من شكواك (مثل Amazon، WorldCat، Google Books، DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "البيان والتوقيع (مطلوب)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "تقديم الشكوى"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ شكرًا لك على تقديم شكوى حقوق الطبع والنشر الخاصة بك. سنقوم بمراجعتها في أقرب وقت ممكن. يرجى إعادة تحميل الصفحة لتقديم شكوى أخرى."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ حدث خطأ ما. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "إذا كنت مهتمًا بعمل نسخة مرآة من مجموعة البيانات هذه لأغراض <a %(a_archival)s>الأرشفة</a> أو <a %(a_llm)s>تدريب LLM</a>، يرجى الاتصال بنا."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "مهمتنا هي أرشفة جميع الكتب في العالم (وكذلك الأوراق والمجلات، إلخ)، وجعلها متاحة على نطاق واسع. نعتقد أن جميع الكتب يجب أن تكون معكوسة على نطاق واسع لضمان التكرار والمرونة. لهذا السبب نحن نجمع الملفات من مجموعة متنوعة من المصادر. بعض المصادر مفتوحة تمامًا ويمكن عكسها بكميات كبيرة (مثل Sci-Hub). البعض الآخر مغلق وحمايي، لذا نحاول استخراجها من أجل \"تحرير\" كتبهم. والبعض الآخر يقع في مكان ما بينهما."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "يمكن تحميل جميع بياناتنا عبر <a %(a_torrents)s>التورنت</a>، ويمكن توليد جميع بياناتنا الوصفية أو <a %(a_anna_software)s>تنزيلها</a> كقواعد بيانات ElasticSearch وMariaDB. يمكن استكشاف البيانات الخام يدويًا من خلال ملفات JSON مثل <a %(a_dbrecord)s>هذا</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "نظرة عامة"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "فيما يلي نظرة سريعة على مصادر الملفات في رَبيدةُ آنّا."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "المصدر"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "الحجم"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% معكوسة بواسطة AA / التورنتات المتاحة"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "نسب عدد الملفات"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "آخر تحديث"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "غير خيالي وخيالي"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "0 ملفات"
msgstr[1] "%(count)s ملف"
msgstr[2] "2 ملفات"
msgstr[3] ""
msgstr[4] ""
msgstr[5] "%(count)s ملفات"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "عبر Libgen.li \"scimag\""

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: مجمدة منذ 2021؛ معظمها متاح عبر التورنتات"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: إضافات طفيفة منذ ذلك الحين</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "باستثناء \"scimag\""

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "تورنتات الخيال متأخرة (على الرغم من أن المعرفات ~4-6M لم يتم تورنتها لأنها تتداخل مع تورنتات مكتبة الزّاي الخاصة بنا)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "يبدو أن مجموعة \"الصينية\" في مكتبة الزّاي هي نفسها مجموعة DuXiu الخاصة بنا، ولكن مع MD5s مختلفة. نستثني هذه الملفات من التورنتات لتجنب التكرار، ولكن نظهرها في فهرس البحث لدينا."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "الإعارة الرقمية المُتحكم بها من IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ من الملفات قابلة للبحث."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "الإجمالي"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "باستثناء التكرارات"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "نظرًا لأن المكتبات الظلية غالبًا ما تقوم بمزامنة البيانات من بعضها البعض، فهناك تداخل كبير بين المكتبات. لهذا السبب لا تتطابق الأرقام مع الإجمالي."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "تُظهر نسبة \"المعكوسة والمزروعة بواسطة رَبيدةُ آنّا\" عدد الملفات التي نعكسها بأنفسنا. نقوم بزراعة تلك الملفات بكميات كبيرة عبر التورنتات، ونجعلها متاحة للتنزيل المباشر عبر مواقع الشركاء."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "المكتبات المصدرية"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "بعض المكتبات المصدرية تروّج لمشاركة بياناتها بشكل كبير عبر التورنت، بينما لا تشارك مكتبات أخرى مجموعتها بسهولة. في الحالة الأخيرة، تحاول رَبيدةُ آنّا جمع مجموعاتهم وجعلها متاحة (انظر صفحة <a %(a_torrents)s>التورنت</a> الخاصة بنا). هناك أيضًا حالات وسطية، على سبيل المثال، حيث تكون المكتبات المصدرية مستعدة للمشاركة، ولكنها لا تملك الموارد للقيام بذلك. في هذه الحالات، نحاول أيضًا المساعدة."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "فيما يلي نظرة عامة على كيفية تفاعلنا مع المكتبات المصدرية المختلفة."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "المصدر"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "الملفات"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>تفريغات قاعدة البيانات HTTP</a> اليومية"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s السيول الآلية لـ <a %(nonfiction)s>الكتب غير الخيالية</a> و<a %(fiction)s>الخيالية</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(covers)s>سيول أغلفة الكتب</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen \"scimag\""

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub قد جمدت الملفات الجديدة منذ عام 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s تفريغات البيانات الوصفية متاحة <a %(scihub1)s>هنا</a> و<a %(scihub2)s>هنا</a>، وكذلك كجزء من <a %(libgenli)s>قاعدة بيانات Libgen.li</a> (التي نستخدمها)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s السيول البيانات متاحة <a %(scihub1)s>هنا</a>، <a %(scihub2)s>هنا</a>، و<a %(libgenli)s>هنا</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s بعض الملفات الجديدة <a %(libgenrs)s>تتم</a> <a %(libgenli)s>إضافتها</a> إلى \"scimag\" في Libgen، ولكن ليس بما يكفي لتبرير سيول جديدة"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>تفريغات قاعدة البيانات HTTP</a> ربع السنوية"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s السيول للكتب غير الخيالية تُشارك مع Libgen.rs (ومرآة <a %(libgenli)s>هنا</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s رَبيدةُ آنّا وLibgen.li يديران بشكل تعاوني مجموعات من <a %(comics)s>الكتب المصورة</a>، <a %(magazines)s>المجلات</a>، <a %(standarts)s>الوثائق القياسية</a>، و<a %(fiction)s>الأدب (المفصول عن Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s مجموعتهم \"fiction_rus\" (الأدب الروسي) لا تحتوي على تورنتات مخصصة، ولكنها مغطاة بتورنتات من الآخرين، ونحن نحافظ على <a %(fiction_rus)s>عاكسة</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s رَبيدةُ آنّا ومكتبة الزّاي يديران معًا مجموعة من <a %(metadata)s>البيانات الوصفية لمكتبة الزّاي</a> و<a %(files)s>ملفات مكتبة الزّاي</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s بعض البيانات الوصفية متاحة من خلال <a %(openlib)s>تفريغات قاعدة بيانات Open Library</a>، لكنها لا تغطي مجموعة IA بالكامل"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s لا توجد تفريغات بيانات وصفية متاحة بسهولة لمجموعتهم الكاملة"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(ia)s>البيانات الوصفية لـ IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s الملفات متاحة فقط للاستعارة على أساس محدود، مع قيود وصول متنوعة"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(ia)s>ملفات IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s قواعد بيانات وصفية متنوعة منتشرة في الإنترنت الصيني؛ رغم أنها غالبًا قواعد بيانات مدفوعة"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s لا تتوفر تفريغات للبيانات الوصفية بسهولة لمجموعتهم الكاملة."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(duxiu)s>بيانات DuXiu الوصفية</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s قواعد بيانات ملفات متنوعة منتشرة عبر الإنترنت الصيني؛ غالبًا ما تكون قواعد بيانات مدفوعة."

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s معظم الملفات متاحة فقط باستخدام حسابات BaiduYun المميزة؛ سرعات التنزيل بطيئة."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(duxiu)s>ملفات DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s مصادر أصغر أو فردية متنوعة. نشجع الناس على التحميل إلى مكتبات الظل الأخرى أولاً، ولكن في بعض الأحيان يكون لدى الناس مجموعات كبيرة جدًا بحيث لا يمكن للآخرين فرزها، ولكنها ليست كبيرة بما يكفي لتستحق فئة خاصة بها."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "مصادر البيانات الوصفية فقط"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "نحن أيضًا نثري مجموعتنا بمصادر البيانات الوصفية فقط، والتي يمكننا مطابقتها مع الملفات، على سبيل المثال باستخدام أرقام ISBN أو حقول أخرى. فيما يلي نظرة عامة على تلك المصادر. مرة أخرى، بعض هذه المصادر مفتوحة تمامًا، بينما يجب علينا جمعها من مصادر أخرى."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "إلهامنا لجمع البيانات الوصفية هو هدف آرون شوارتز \"صفحة ويب واحدة لكل كتاب تم نشره على الإطلاق\"، والذي أنشأ من أجله <a %(a_openlib)s>Open Library</a>. لقد نجح هذا المشروع، لكن موقعنا الفريد يسمح لنا بالحصول على بيانات وصفية لا يمكنهم الحصول عليها. كان مصدر إلهام آخر هو رغبتنا في معرفة <a %(a_blog)s>عدد الكتب الموجودة في العالم</a>، حتى نتمكن من حساب عدد الكتب التي لا تزال بحاجة إلى الإنقاذ."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "لاحظ أنه في بحث البيانات الوصفية، نعرض السجلات الأصلية. نحن لا نقوم بدمج السجلات."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "آخر تحديث"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s تفريغات قاعدة البيانات الشهرية <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s غير متاحة مباشرة بكميات كبيرة، محمية ضد الكشط."

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(worldcat)s>بيانات OCLC (WorldCat) الوصفية</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "قاعدة بيانات موحدة"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "نحن نجمع كل المصادر المذكورة أعلاه في قاعدة بيانات موحدة نستخدمها لخدمة هذا الموقع. هذه القاعدة الموحدة ليست متاحة مباشرة، ولكن بما أن رَبيدةُ آنّا مفتوحة المصدر بالكامل، يمكن بسهولة <a %(a_generated)s>توليدها</a> أو <a %(a_downloaded)s>تحميلها</a> كقواعد بيانات ElasticSearch وMariaDB. ستقوم السكربتات في تلك الصفحة بتحميل جميع البيانات الوصفية المطلوبة تلقائيًا من المصادر المذكورة أعلاه."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "إذا كنت ترغب في استكشاف بياناتنا قبل تشغيل تلك السكربتات محليًا، يمكنك النظر في ملفات JSON الخاصة بنا، والتي ترتبط بملفات JSON أخرى. <a %(a_json)s>هذا الملف</a> هو نقطة انطلاق جيدة."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "مقتبس من <a %(a_href)s>منشور مدونتنا</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> هي قاعدة بيانات ضخمة للكتب الممسوحة ضوئيًا، أنشأتها <a %(superstar_link)s>مجموعة المكتبة الرقمية SuperStar</a>. معظمها كتب أكاديمية، تم مسحها ضوئيًا لجعلها متاحة رقميًا للجامعات والمكتبات. لجمهورنا الناطق باللغة الإنجليزية، <a %(princeton_link)s>برينستون</a> و<a %(uw_link)s>جامعة واشنطن</a> لديهما نظرات عامة جيدة. هناك أيضًا مقال ممتاز يقدم المزيد من الخلفية: <a %(article_link)s>“رقمنة الكتب الصينية: دراسة حالة لمحرك البحث SuperStar DuXiu Scholar”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "تم قرصنة الكتب من Duxiu منذ فترة طويلة على الإنترنت الصيني. عادةً ما يتم بيعها بأقل من دولار واحد من قبل البائعين. يتم توزيعها عادةً باستخدام ما يعادل Google Drive في الصين، والذي غالبًا ما يتم اختراقه للسماح بمساحة تخزين أكبر. يمكن العثور على بعض التفاصيل التقنية <a %(link1)s>هنا</a> و<a %(link2)s>هنا</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "على الرغم من أن الكتب تم توزيعها بشكل شبه علني، إلا أنه من الصعب جدًا الحصول عليها بكميات كبيرة. كان هذا على رأس قائمة مهامنا، وخصصنا عدة أشهر من العمل بدوام كامل لذلك. ومع ذلك، في أواخر عام 2023، تواصل معنا متطوع رائع ومذهل وموهوب، وأخبرنا أنه قد قام بكل هذا العمل بالفعل — بتكلفة كبيرة. شارك المجموعة الكاملة معنا، دون توقع أي شيء في المقابل، باستثناء ضمان الحفظ طويل الأمد. حقًا أمر رائع."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "الموارد"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "إجمالي الملفات: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "إجمالي حجم الملفات: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "الملفات التي تم عكسها بواسطة رَبيدةُ آنّا: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "آخر تحديث: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "التورنت بواسطة رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "مثال على سجل في رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "منشور مدونتنا عن هذه البيانات"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "البرامج النصية لاستيراد البيانات الوصفية"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "تنسيق حاويات رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "مزيد من المعلومات من متطوعينا (ملاحظات خام):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "الإعارة الرقمية الخاضعة للتحكم من IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "هذه المجموعة مرتبطة ارتباطًا وثيقًا بمجموعة <a %(a_datasets_openlib)s>Open Library</a>. تحتوي على نسخة من جميع البيانات الوصفية وجزء كبير من الملفات من مكتبة الإعارة الرقمية المُتحكم بها من IA. يتم إصدار التحديثات بتنسيق <a %(a_aac)s>حاويات رَبيدةُ آنّا</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "يتم الإشارة إلى هذه السجلات مباشرة من مجموعة Open Library، ولكنها تحتوي أيضًا على سجلات غير موجودة في Open Library. لدينا أيضًا عدد من ملفات البيانات التي تم جمعها بواسطة أعضاء المجتمع على مر السنين."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "تتكون المجموعة من جزأين. تحتاج إلى كلا الجزأين للحصول على جميع البيانات (باستثناء السيول التي تم تجاوزها، والتي يتم شطبها في صفحة السيول)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "إصدارنا الأول، قبل أن نعتمد على <a %(a_aac)s>تنسيق حاويات رَبيدةُ آنّا (AAC)</a>. يحتوي على البيانات الوصفية (بصيغة json و xml)، ملفات pdf (من أنظمة الإعارة الرقمية acsm و lcpdf)، وصور مصغرة للأغلفة."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "إصدارات جديدة تدريجية، باستخدام AAC. تحتوي فقط على البيانات الوصفية مع الطوابع الزمنية بعد 2023-01-01، حيث أن الباقي مغطى بالفعل بواسطة \"ia\". أيضًا جميع ملفات pdf، هذه المرة من أنظمة الإعارة acsm و \"bookreader\" (قارئ الويب الخاص بـ IA). على الرغم من أن الاسم ليس دقيقًا تمامًا، إلا أننا ما زلنا نملأ ملفات bookreader في مجموعة ia2_acsmpdf_files، لأنها حصرية."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "الموقع الرئيسي %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "مكتبة الإعارة الرقمية"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "توثيق البيانات الوصفية (معظم الحقول)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "معلومات الدولة الخاصة بـ ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "تقوم الوكالة الدولية لـ ISBN بإصدار النطاقات التي تم تخصيصها للوكالات الوطنية لـ ISBN بانتظام. من هذا يمكننا استنتاج البلد أو المنطقة أو المجموعة اللغوية التي ينتمي إليها هذا ISBN. نحن نستخدم هذه البيانات حاليًا بشكل غير مباشر، من خلال مكتبة Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "الموارد"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "آخر تحديث: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "موقع ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "البيانات الوصفية"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "للحصول على خلفية حول الفروع المختلفة لـ Library Genesis، انظر الصفحة الخاصة بـ <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "يحتوي Libgen.li على معظم نفس المحتوى والبيانات الوصفية مثل Libgen.rs، ولكنه يحتوي على بعض المجموعات الإضافية، وهي الكوميديا والمجلات والوثائق القياسية. كما أنه دمج <a %(a_scihub)s>Sci-Hub</a> في بياناته الوصفية ومحرك البحث، وهو ما نستخدمه لقاعدة بياناتنا."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "البيانات الوصفية لهذه المكتبة متاحة مجانًا <a %(a_libgen_li)s>في libgen.li</a>. ومع ذلك، فإن هذا الخادم بطيء ولا يدعم استئناف الاتصالات المقطوعة. نفس الملفات متاحة أيضًا على <a %(a_ftp)s>خادم FTP</a>، والذي يعمل بشكل أفضل."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "التورنتات متاحة لمعظم المحتوى الإضافي، وخاصة التورنتات للكتب المصورة، المجلات، والوثائق القياسية التي تم إصدارها بالتعاون مع رَبيدةُ آنّا."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "مجموعة الأدب لديها تورنتات خاصة بها (مفصولة عن <a %(a_href)s>Libgen.rs</a>) تبدأ من %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "وفقًا لمسؤول Libgen.li، يجب أن تكون مجموعة \"fiction_rus\" (الأدب الروسي) مغطاة بتورنتات يتم إصدارها بانتظام من <a %(a_booktracker)s>booktracker.org</a>، وخاصة تورنتات <a %(a_flibusta)s>flibusta</a> و<a %(a_librusec)s>lib.rus.ec</a> (التي نقوم بعكسها <a %(a_torrents)s>هنا</a>، على الرغم من أننا لم نحدد بعد أي تورنتات تتوافق مع أي ملفات)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "يمكن العثور على إحصائيات لجميع المجموعات <a %(a_href)s>على موقع libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "يبدو أن الكتب غير الخيالية قد تباعدت أيضًا، ولكن دون وجود سيل جديد. يبدو أن هذا قد حدث منذ أوائل عام 2022، على الرغم من أننا لم نتحقق من ذلك."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "بعض النطاقات التي لا تحتوي على تورنتات (مثل نطاقات الأدب f_3463000 إلى f_4260000) من المحتمل أن تكون ملفات مكتبة الزّاي (أو ملفات مكررة أخرى)، على الرغم من أننا قد نرغب في إجراء بعض إزالة التكرار وإنشاء تورنتات للملفات الفريدة لـ lgli في هذه النطاقات."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "يرجى ملاحظة أن ملفات التورنت التي تشير إلى \"libgen.is\" هي عاكسات صريحة لـ <a %(a_libgen)s>Libgen.rs</a> (\".is\" هو نطاق مختلف يستخدمه Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "مورد مفيد لاستخدام البيانات الوصفية هو <a %(a_href)s>هذه الصفحة</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "تورنتات الخيال على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "تورنتات القصص المصورة على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "تورنتات المجلات على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "تورنتات الوثائق القياسية على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "تورنتات الأدب الروسي على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "البيانات الوصفية"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "البيانات الوصفية عبر FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "معلومات حقل البيانات الوصفية"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "عاكسة لتورنتات أخرى (وتورنتات فريدة للخيال والقصص المصورة)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "منتدى النقاش"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "منشور مدونتنا عن إصدار القصص المصورة"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "القصة السريعة لتفرعات Library Genesis (أو \"Libgen\") المختلفة هي أنه مع مرور الوقت، اختلف الأشخاص المشاركون في Library Genesis وذهبوا في طرقهم المنفصلة."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "تم إنشاء النسخة \".fun\" من قبل المؤسس الأصلي. يتم إعادة تصميمها لصالح نسخة جديدة وأكثر توزيعًا."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "النسخة \".rs\" تحتوي على بيانات مشابهة جدًا، وتصدر مجموعتها بشكل متسق في تورنتات ضخمة. يتم تقسيمها تقريبًا إلى قسم \"الخيال\" وقسم \"غير الخيال\"."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "أصلاً في \"http://gen.lib.rus.ec\"."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>النسخة \".li\"</a> تحتوي على مجموعة ضخمة من القصص المصورة، بالإضافة إلى محتويات أخرى، التي لم تتوفر بعد للتنزيل الجماعي عبر التورنتات. لديها مجموعة تورنت منفصلة لكتب الخيال، وتحتوي على البيانات الوصفية لـ <a %(a_scihub)s>Sci-Hub</a> في قاعدة بياناتها."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "وفقًا لهذا <a %(a_mhut)s>المنشور في المنتدى</a>، كان Libgen.li مستضافًا في الأصل على \"http://free-books.dontexist.com\"."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>مكتبة الزّاي</a> هي أيضًا نوع من تفرعات Library Genesis، على الرغم من أنهم استخدموا اسمًا مختلفًا لمشروعهم."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "هذه الصفحة تتعلق بالنسخة \".rs\". وهي معروفة بنشرها المتسق لكل من بياناتها الوصفية والمحتويات الكاملة لفهرس كتبها. يتم تقسيم مجموعة كتبها بين قسم الخيال وقسم غير الخيال."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "مورد مفيد لاستخدام البيانات الوصفية هو <a %(a_metadata)s>هذه الصفحة</a> (تحظر نطاقات IP، قد يكون استخدام VPN مطلوبًا)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "اعتبارًا من 2024-03، يتم نشر التورنتات الجديدة في <a %(a_href)s>هذا الموضوع في المنتدى</a> (يتم حظر نطاقات IP، قد يكون استخدام VPN مطلوبًا)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "تورنتات الكتب غير الخيالية على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "تورنتات الكتب الخيالية على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "بيانات وصفية لـ Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "معلومات حقل البيانات الوصفية لـ Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "تورنتات الكتب غير الخيالية لـ Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "تورنتات الكتب الخيالية لـ Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "منتدى النقاش لـ Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "تورنتات بواسطة رَبيدةُ آنّا (أغلفة الكتب)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "مدونتنا حول إصدار أغلفة الكتب"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "تُعرف Library Genesis بالفعل بجعل بياناتها متاحة بسخاء بكميات كبيرة من خلال التورنتات. تتكون مجموعتنا من Libgen من بيانات مساعدة لا يفرجون عنها مباشرة، بالشراكة معهم. شكرًا جزيلاً لكل من شارك في Library Genesis للعمل معنا!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "الإصدار 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "هذا <a %(blog_post)s>الإصدار الأول</a> صغير جدًا: حوالي 300 جيجابايت من أغلفة الكتب من فرع Libgen.rs، سواء كانت خيالية أو غير خيالية. وهي منظمة بنفس الطريقة التي تظهر بها على libgen.rs، على سبيل المثال:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s لكتاب غير خيالي."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s لكتاب خيالي."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "تمامًا مثل مجموعة مكتبة الزّاي، وضعناها جميعًا في ملف .tar كبير، والذي يمكن تركيبه باستخدام <a %(a_ratarmount)s>ratarmount</a> إذا كنت ترغب في تقديم الملفات مباشرة."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> هي قاعدة بيانات مملوكة من قبل المنظمة غير الربحية <a %(a_oclc)s>OCLC</a>، والتي تجمع سجلات البيانات الوصفية من المكتبات في جميع أنحاء العالم. من المحتمل أن تكون أكبر مجموعة بيانات وصفية للمكتبات في العالم."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "أكتوبر 2023، الإصدار الأول:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "في أكتوبر 2023، <a %(a_scrape)s>أصدرنا</a> عملية استخراج شاملة لقاعدة بيانات OCLC (WorldCat)، بتنسيق <a %(a_aac)s>حاويات رَبيدةُ آنّا</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "تورنتات بواسطة رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "منشور مدونتنا حول هذه البيانات"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "مكتبة الزّاي"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "مكتبة الزّاي هو مشروع مفتوح المصدر من Internet Archive لفهرسة كل كتاب في العالم. لديها واحدة من أكبر عمليات مسح الكتب في العالم، ولديها العديد من الكتب المتاحة للإعارة الرقمية. كتالوج البيانات الوصفية للكتب متاح للتنزيل مجانًا، وهو مدرج في رَبيدةُ آنّا (على الرغم من أنه غير متاح حاليًا في البحث، إلا إذا قمت بالبحث صراحةً عن معرف مكتبة الزّاي)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "البيانات الوصفية"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "الإصدار 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "هذا تفريغ للعديد من المكالمات إلى isbndb.com خلال سبتمبر 2022. حاولنا تغطية جميع نطاقات ISBN. هذه حوالي 30.9 مليون سجل. على موقعهم، يدعون أن لديهم فعليًا 32.6 مليون سجل، لذا قد نكون قد فقدنا بعضًا منها بطريقة ما، أو <em>قد</em> يكونون قد ارتكبوا خطأ ما."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "استجابات JSON هي تقريبًا خام من خادمهم. إحدى مشكلات جودة البيانات التي لاحظناها هي أنه بالنسبة لأرقام ISBN-13 التي تبدأ برمز مختلف عن \"978-\"، فإنهم لا يزالون يتضمنون حقل \"isbn\" الذي هو ببساطة رقم ISBN-13 مع حذف الأرقام الثلاثة الأولى (وإعادة حساب رقم التحقق). هذا خطأ واضح، ولكن هذا هو ما يبدو أنهم يفعلونه، لذا لم نقم بتغييره."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "مشكلة أخرى محتملة قد تواجهها هي حقيقة أن حقل \"isbn13\" يحتوي على تكرارات، لذا لا يمكنك استخدامه كمفتاح أساسي في قاعدة البيانات. يبدو أن الحقول \"isbn13\"+\"isbn\" مجتمعة تكون فريدة."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "لمزيد من المعلومات حول Sci-Hub، يرجى الرجوع إلى <a %(a_scihub)s>الموقع الرسمي</a>، <a %(a_wikipedia)s>صفحة ويكيبيديا</a>، وهذه <a %(a_radiolab)s>المقابلة الصوتية</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "يرجى ملاحظة أن Sci-Hub قد تم <a %(a_reddit)s>تجميده منذ عام 2021</a>. تم تجميده من قبل، ولكن في عام 2021 تمت إضافة بضعة ملايين من الأوراق. ومع ذلك، يتم إضافة عدد محدود من الأوراق إلى مجموعات \"scimag\" في Libgen، ولكن ليس بما يكفي لتبرير تورنتات جديدة بالجملة."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "نستخدم البيانات الوصفية لـ Sci-Hub كما هو مقدم من <a %(a_libgen_li)s>Libgen.li</a> في مجموعة \"scimag\". نستخدم أيضًا مجموعة البيانات <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "يرجى ملاحظة أن تورنتات \"smarch\" <a %(a_smarch)s>مهملة</a> وبالتالي غير مدرجة في قائمة التورنتات لدينا."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "تورنتات على رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "البيانات الوصفية والتورنتات"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "تورنتات على Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "تورنتات على Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "تحديثات على Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "صفحة ويكيبيديا"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "مقابلة صوتية"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "التحميلات إلى رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "نظرة عامة من <a %(a1)s>صفحة Datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "مصادر أصغر أو فردية متنوعة. نشجع الناس على التحميل إلى مكتبات الظل الأخرى أولاً، ولكن في بعض الأحيان يكون لدى الناس مجموعات كبيرة جدًا بحيث لا يمكن للآخرين فرزها، ولكنها ليست كبيرة بما يكفي لتستحق فئة خاصة بها."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "مجموعة \"التحميل\" مقسمة إلى مجموعات فرعية أصغر، والتي يتم الإشارة إليها في AACIDs وأسماء التورنت. تم إزالة التكرارات من جميع المجموعات الفرعية أولاً مقابل المجموعة الرئيسية، على الرغم من أن ملفات JSON \"upload_records\" للبيانات الوصفية لا تزال تحتوي على الكثير من الإشارات إلى الملفات الأصلية. تم أيضًا إزالة الملفات غير الكتابية من معظم المجموعات الفرعية، وعادةً ما لا يتم الإشارة إليها في \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "العديد من المجموعات الفرعية نفسها تتكون من مجموعات فرعية فرعية (مثلًا من مصادر أصلية مختلفة)، والتي يتم تمثيلها كدليل في حقول \"filepath\"."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "المجموعات الفرعية هي:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "مجموعة فرعية"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "ملاحظات"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "تصفح"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "بحث"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "من <a %(a_href)s>aaaaarg.fail</a>. يبدو أنه مكتمل إلى حد كبير. من متطوعنا \"cgiym\"."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "من <a %(a_href)s><q>ACM Digital Library 2020</q></a> تورنت. يحتوي على تداخل كبير مع مجموعات الأوراق الموجودة، ولكن هناك عدد قليل جدًا من التطابقات مع MD5، لذلك قررنا الاحتفاظ به بالكامل."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "استخلاص من <q>iRead eBooks</q> (= صوتيًا <q>ai rit i-books</q>; airitibooks.com)، بواسطة المتطوع <q>j</q>. يتوافق مع metadata <q>airitibooks</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "من مجموعة <a %(a1)s><q>مكتبة الإسكندرية</q></a>. جزئيًا من المصدر الأصلي، جزئيًا من the-eye.eu، جزئيًا من مرايا أخرى."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "من موقع تورنت خاص بالكتب، <a %(a_href)s>Bibliotik</a> (غالبًا ما يُشار إليه بـ \"Bib\")، حيث تم تجميع الكتب في تورنتات بأسماء (A.torrent, B.torrent) وتوزيعها عبر the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "من متطوعنا \"bpb9v\". لمزيد من المعلومات حول <a %(a_href)s>CADAL</a>، انظر الملاحظات في <a %(a_duxiu)s>صفحة مجموعة بيانات DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "المزيد من متطوعنا \"bpb9v\"، معظمها ملفات DuXiu، بالإضافة إلى مجلد \"WenQu\" و\"SuperStar_Journals\" (SuperStar هي الشركة وراء DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "من متطوعنا \"cgiym\"، نصوص صينية من مصادر متنوعة (ممثلة كأدلة فرعية)، بما في ذلك من <a %(a_href)s>China Machine Press</a> (ناشر صيني رئيسي)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "مجموعات غير صينية (ممثلة كأدلة فرعية) من متطوعنا \"cgiym\"."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "استخلاص لكتب عن العمارة الصينية، بواسطة المتطوع <q>cm</q>: <q>حصلت عليها من خلال استغلال ثغرة في شبكة دار النشر، لكن تم إغلاق تلك الثغرة منذ ذلك الحين</q>. يتوافق مع metadata <q>chinese_architecture</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "كتب من دار النشر الأكاديمية <a %(a_href)s>De Gruyter</a>، جمعت من بعض التورنتات الكبيرة."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "استخلاص من <a %(a_href)s>docer.pl</a>، موقع مشاركة ملفات بولندي يركز على الكتب والأعمال المكتوبة الأخرى. تم استخلاصه في أواخر 2023 بواسطة المتطوع \"p\". ليس لدينا بيانات وصفية جيدة من الموقع الأصلي (ولا حتى امتدادات الملفات)، لكننا قمنا بتصفية الملفات التي تشبه الكتب وغالبًا ما تمكنا من استخراج البيانات الوصفية من الملفات نفسها."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "ملفات DuXiu بصيغة epub، مباشرة من DuXiu، جمعت بواسطة المتطوع \"w\". تتوفر فقط الكتب الحديثة من DuXiu مباشرة من خلال الكتب الإلكترونية، لذا يجب أن تكون معظم هذه الكتب حديثة."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "الملفات المتبقية من DuXiu من المتطوع \"m\"، والتي لم تكن بتنسيق PDG الخاص بـ DuXiu (البيانات الرئيسية <a %(a_href)s>لمجموعة بيانات DuXiu</a>). جمعت من العديد من المصادر الأصلية، للأسف دون الحفاظ على تلك المصادر في مسار الملف."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "استخلاص لكتب إيروتيكية، بواسطة المتطوع <q>do no harm</q>. يتوافق مع metadata <q>hentai</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "مجموعة مستخلصة من ناشر مانغا ياباني بواسطة المتطوع \"t\"."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>الأرشيف القضائي المختار لـ Longquan</a>، مقدمة من المتطوع \"c\"."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "استخلاص من <a %(a_href)s>magzdb.org</a>، حليف لـ Library Genesis (مرتبط بصفحة libgen.rs الرئيسية) لكنه لم يرغب في توفير ملفاته مباشرة. حصل عليها المتطوع \"p\" في أواخر 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "تحميلات صغيرة متنوعة، صغيرة جدًا لتكون مجموعة فرعية خاصة بها، لكنها ممثلة كأدلة."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "كتب إلكترونية من AvaxHome، موقع روسي لمشاركة الملفات."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "أرشيف للصحف والمجلات. يتوافق مع metadata <q>newsarch_magz</q> في <a %(a1)s><q>استخلاصات metadata الأخرى</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "استخلاص من <a %(a1)s>مركز توثيق الفلسفة</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "مجموعة المتطوع \"o\" الذي جمع الكتب البولندية مباشرة من مواقع الإصدار الأصلية (\"المشهد\")."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "مجموعات مشتركة من <a %(a_href)s>shuge.org</a> بواسطة المتطوعين \"cgiym\" و\"woz9ts\"."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“المكتبة الإمبراطورية لترانتور”</a> (سميت على اسم المكتبة الخيالية)، مستخلصة في 2022 بواسطة المتطوع \"t\"."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "مجموعات فرعية فرعية (ممثلة كأدلة) من المتطوع \"woz9ts\": <a %(a_program_think)s>program-think</a>، <a %(a_haodoo)s>haodoo</a>، <a %(a_skqs)s>skqs</a> (بواسطة <a %(a_sikuquanshu)s>Dizhi(迪志)</a> في تايوان)، mebook (mebook.cc، 我的小书屋، غرفتي الصغيرة للكتب — woz9ts: \"يركز هذا الموقع بشكل رئيسي على مشاركة ملفات الكتب الإلكترونية عالية الجودة، بعضها منسق بواسطة المالك نفسه. تم <a %(a_arrested)s>اعتقال</a> المالك في 2019 وقام شخص ما بجمع الملفات التي شاركها.\")."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "الملفات المتبقية من DuXiu من المتطوع \"woz9ts\"، والتي لم تكن في تنسيق PDG الخاص بـ DuXiu (لا تزال بحاجة إلى تحويل إلى PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "تورنتات من رَبيدةُ آنّا"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "استخراج مكتبة الزّاي"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "تعود جذور مكتبة الزّاي إلى مجتمع <a %(a_href)s>Library Genesis</a>، وبدأت في الأصل ببياناتهم. منذ ذلك الحين، أصبحت أكثر احترافية بشكل كبير، ولديها واجهة أكثر حداثة. لذلك، يمكنهم الحصول على المزيد من التبرعات، سواءً المالية لتحسين موقعهم الإلكتروني، أو تبرعات بالكتب الجديدة. لقد جمعوا مجموعة كبيرة بالإضافة إلى Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "تحديث اعتبارًا من فبراير 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "في أواخر عام 2022، تم اعتقال المؤسسين المزعومين لمكتبة الزّاي، وتمت مصادرة النطاقات من قبل السلطات الأمريكية. منذ ذلك الحين، كان الموقع يعود ببطء إلى الإنترنت. من غير المعروف من يديره حاليًا."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "تتكون المجموعة من ثلاثة أجزاء. يتم الحفاظ على صفحات الوصف الأصلية للجزئين الأولين أدناه. تحتاج إلى الأجزاء الثلاثة للحصول على جميع البيانات (باستثناء التورنتات التي تم تجاوزها، والتي تم شطبها في صفحة التورنتات)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: الإصدار الأول. كان هذا هو الإصدار الأول لما كان يسمى آنذاك \"عاكسة المكتبة القرصانية\" (\"pilimi\")."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: الإصدار الثاني، هذه المرة مع جميع الملفات ملفوفة في ملفات .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: إصدارات جديدة تدريجية، باستخدام <a %(a_href)s>تنسيق حاويات رَبيدةُ آنّا (AAC)</a>، الآن يتم إصدارها بالتعاون مع فريق مكتبة الزّاي."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "تورنتات من رَبيدةُ آنّا (البيانات الوصفية + المحتوى)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "مثال على سجل في رَبيدةُ آنّا (المجموعة الأصلية)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "مثال على سجل في رَبيدةُ آنّا (مجموعة \"zlib3\")"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "الموقع الرئيسي"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "نطاق Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "منشور مدونة حول الإصدار 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "منشور مدونة حول الإصدار 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "إصدارات Zlib (صفحات الوصف الأصلية)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "الإصدار 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "تم الحصول على العاكسة الأولية بشق الأنفس على مدار عامي 2021 و2022. في هذه المرحلة، هي قديمة بعض الشيء: تعكس حالة المجموعة في يونيو 2021. سنقوم بتحديث هذا في المستقبل. نحن الآن نركز على إصدار هذا الإصدار الأول."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "نظرًا لأن Library Genesis محفوظة بالفعل مع التورنتات العامة، وهي مدرجة في مكتبة الزّاي، فقد قمنا بعملية إزالة التكرار الأساسية ضد Library Genesis في يونيو 2022. استخدمنا لهذا الغرض تجزئة MD5. من المحتمل أن يكون هناك الكثير من المحتوى المكرر في المكتبة، مثل تنسيقات ملفات متعددة لنفس الكتاب. من الصعب اكتشاف ذلك بدقة، لذا لا نقوم بذلك. بعد إزالة التكرار، تبقى لدينا أكثر من 2 مليون ملف، بإجمالي يقل قليلاً عن 7 تيرابايت."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "تتكون المجموعة من جزأين: تفريغ MySQL “.sql.gz” للبيانات الوصفية، و72 ملف تورنت بحجم يتراوح بين 50-100 جيجابايت لكل منها. تحتوي البيانات الوصفية على البيانات كما أبلغ عنها موقع مكتبة الزّاي (العنوان، المؤلف، الوصف، نوع الملف)، بالإضافة إلى الحجم الفعلي للملف وmd5sum الذي لاحظناه، حيث أن هذه البيانات قد لا تتطابق أحيانًا. يبدو أن هناك نطاقات من الملفات التي تحتوي مكتبة الزّاي نفسها على بيانات وصفية غير صحيحة. قد نكون أيضًا قد قمنا بتنزيل ملفات بشكل غير صحيح في بعض الحالات المعزولة، والتي سنحاول اكتشافها وإصلاحها في المستقبل."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "تحتوي ملفات التورنت الكبيرة على بيانات الكتب الفعلية، مع معرف مكتبة الزّاي كاسم الملف. يمكن إعادة بناء امتدادات الملفات باستخدام تفريغ البيانات الوصفية."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "المجموعة هي مزيج من المحتوى غير الخيالي والخيالي (غير مفصول كما في Library Genesis). الجودة أيضًا متباينة بشكل كبير."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "هذا الإصدار الأول متاح الآن بالكامل. لاحظ أن ملفات التورنت متاحة فقط من خلال عاكسنا على Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "الإصدار 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "لقد حصلنا على جميع الكتب التي أضيفت إلى مكتبة الزّاي بين آخر عاكس لنا وأغسطس 2022. كما عدنا وقمنا بجمع بعض الكتب التي فاتتنا في المرة الأولى. بشكل عام، هذه المجموعة الجديدة تبلغ حوالي 24 تيرابايت. مرة أخرى، هذه المجموعة مكررة ضد Library Genesis، حيث أن هناك تورنتات متاحة بالفعل لتلك المجموعة."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "البيانات منظمة بشكل مشابه للإصدار الأول. هناك تفريغ MySQL “.sql.gz” للبيانات الوصفية، والذي يتضمن أيضًا جميع البيانات الوصفية من الإصدار الأول، وبالتالي يحل محله. كما أضفنا بعض الأعمدة الجديدة:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ما إذا كان هذا الملف موجودًا بالفعل في Library Genesis، سواء في مجموعة غير الخيال أو الخيال (مطابق بواسطة md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: أي تورنت يحتوي على هذا الملف."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: تم تعيينه عندما لم نتمكن من تنزيل الكتاب."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "ذكرنا هذا في المرة الأخيرة، ولكن للتوضيح: \"اسم الملف\" و \"md5\" هما الخصائص الفعلية للملف، في حين أن \"اسم الملف المبلغ عنه\" و \"md5 المبلغ عنه\" هما ما جمعناه من مكتبة الزّاي. أحيانًا لا يتطابق هذان الاثنان مع بعضهما البعض، لذا قمنا بتضمين كليهما."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "لهذا الإصدار، قمنا بتغيير الترتيب إلى “utf8mb4_unicode_ci”، والذي يجب أن يكون متوافقًا مع الإصدارات الأقدم من MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "ملفات البيانات مشابهة للمرة السابقة، على الرغم من أنها أكبر بكثير. لم نتمكن ببساطة من إنشاء العديد من ملفات التورنت الصغيرة. يحتوي “pilimi-zlib2-0-14679999-extra.torrent” على جميع الملفات التي فاتتنا في الإصدار الأخير، بينما تحتوي التورنتات الأخرى على نطاقات معرف جديدة. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>تحديث %(date)s:</strong> لقد جعلنا معظم تورنتاتنا كبيرة جدًا، مما تسبب في صعوبة لعملاء التورنت. لقد أزلناها وأصدرنا تورنتات جديدة."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>تحديث %(date)s:</strong> لا تزال هناك ملفات كثيرة جدًا، لذا قمنا بلفها في ملفات tar وأصدرنا تورنتات جديدة مرة أخرى."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "ملحق الإصدار 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "هذا ملف تورنت إضافي واحد. لا يحتوي على أي معلومات جديدة، ولكنه يحتوي على بعض البيانات التي قد تستغرق وقتًا طويلاً لحسابها. مما يجعله مريحًا للاحتفاظ به، حيث أن تنزيل هذا التورنت غالبًا ما يكون أسرع من حسابه من الصفر. على وجه الخصوص، يحتوي على فهارس SQLite لملفات tar، للاستخدام مع <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "سؤال وجواب (س ج)"

msgid "page.faq.what_is.title"
msgstr "ماهي رَبيدةُ آنّا؟"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s> رَبيدةُ آنّا</ span> مشروع غير ربحي له هدفان:"

msgid "page.home.intro.text2"
msgstr "<li> <strong> الحفظ: </strong> حفظ جميع علوم و آداب البشرية. </li> <li> <strong> وصلة: </strong> إيصال هذه المعارف والعلوم لأي شخص في العالم. </li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "كل <a %(a_code)s>الرموز</a> و<a %(a_datasets)s>البيانات</a> الخاصة بنا مفتوحة المصدر بالكامل."

msgid "page.home.preservation.header"
msgstr "الحفظ"

msgid "page.home.preservation.text1"
msgstr "نحفظ الكتب والرسائل والمجلات والقصص المصورة وغيرها الكثير، فنأخذها من <a href=\"https://en.wikipedia.org/wiki/Shadow_library\"> مكتبات الظل</a> والمكتبات الرسمية وأماكن أخرى ونجمعها كلها في مكان واحد. وهي محفوظة للأبد ويسهل تكريرها جملةً -بالتورنت- فيصير للعمل الواحد نُسخ عدّة في كلّ العالم. وبعض مكتبات الظل توفر هذا (كمكتبة التَّكوين، ومجمع العلوم)، وتُوفر رَبيدة آنَّا التوزيع بالجملة للمكتبات التي لا تمنح هذا (كمكتبة الزّاي) ومنها المكتبات غير الظلية (كرَبيدة الشّابكة، والدّوشية)."

msgid "page.home.preservation.text2"
msgstr "فهذا التوزيع الكبير الواسع مع توفّر ترميز موقعنا للعامة كلاهما يُشكلان حاجزًا منيعًا لمن يريد الإطاحة بنا، كما يضمنان حفظًا آجلًا لعلوم و آداب البشرية. وللاستزادة اقرأ عن <a href=\"/datasets\">مجمع بياناتنا</a>."

msgid "page.home.preservation.label"
msgstr "حفظنا تقريبا <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\"> 5 %% من الكتب في العالم </a>."

msgid "page.home.access.header"
msgstr "وصول"

msgid "page.home.access.text"
msgstr "نعمل مع شركائنا لنُسهّل إتاحة ما عندنا من مجموعات مجانًا لأي شخص. فلكل فرد الحق في العلم وما فيه من حكمة. و <a %(a_search)s> ليس على حساب المؤلفين </a>."

msgid "page.home.access.label"
msgstr "التنزيلات كل ساعة في آخر 30 يومًا. متوسط الساعة: %(hourly)s. المتوسط اليومي: %(daily)s."

msgid "page.about.text2"
msgstr "نحن ندين بدين حرية إيصال العلم والحفاظ على المعرفة والثقافة. ومحرك البحث هذا أصله كبار مكتبات الظل. فنقدر ما قدّمه مؤسسو مكتبات الظل كلّها ونرجوا أن يكون هذا مساعدًا لهم في توسيع أعمالهم."

msgid "page.about.text3"
msgstr "لتعلم ما عندنا من تطورات، تابع آنَّا على <a href=\"https://www.reddit.com/r/Annas_Archive/\"> ريديت </a> أو <a href=\"https://t.me/annasarchiveorg\">تليغرام</a>. للأسئلة والاقتراحات تواصل مع آنَّا بالبريد الرقمي %(email)s."

msgid "page.faq.help.title"
msgstr "كيف أُساعدك؟"

msgid "page.about.help.text"
msgstr "<li>1. تابعنا على <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>، أو <a href=\"https://t.me/annasarchiveorg\">تلغرام</a> أ>.</li><li>2. انشر رَبيدة آنّا في إكس (تويتر سابقًا)، وريديت، وتِك تُك، وإنستَغرام، وفي مقاهي القراءة ومكتبات بلدتك، ولأ مكان تذهب إليه! نحن ضد التستّرو تمرير العلم خفية - فإن حاربونا وأصرّوا على إسقاطنا فسنظهر في مكان آخر، هذا لأن ترميز موقعنا وبياناتنا مفتوحة كلّها للعامة.</li><li>3. فإن كنت قادرًا، <a href=\"/donate\">تبرّع</a>.</li><li>4. وساعد في <a href=\"https://translate.annas-software.org/\">ترجمة</a> موقعنا إلى لغات مختلفة.</li><li>5. إذا كنت مهندس برمجيات، فساهم في <a href=\"https://annas-software.org/\">المصدر المفتوح</a>، أو شارك ما لدينا وساعد <a href=\"/datasets\">بالتورنت</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "لدينا الآن أيضًا قناة Matrix متزامنة على %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. إن كنت باحثًا أمنيًا، فيمكننا الإستعانة بمهاراتك هجومًا ودفاعًا. راجع صفحتنا عن <a %(a_security)s>الأمان</a>."

msgid "page.about.help.text7"
msgstr "7. نبحث عن خبراء في مجال المدفوعات للتجار المجهولين. هل يمكنك مساعدتنا في إضافة طرق أكثر ملاءمة للتبرع؟ بَيْ بَالْ، وي شات، بطاقات الدفع المسبق. فإن كنت تعرف شخصًا فاتصل بنا واخبرنا."

msgid "page.about.help.text8"
msgstr "8. نسعى دائمًا لإضافة مساحة أكبر للخادوم."

msgid "page.about.help.text9"
msgstr "9. ساعد بالإبلاغ عن مشكلة في ملف واترك تعاليقًا وانشئ قوائمًا مباشرة على هذا الموقع. وساعد<a %(a_upload)s>في رفع كتبٍ أكثر</a> أو اصلح مشاكل الملف ونسّق الكتب الموجودة."

msgid "page.about.help.text10"
msgstr "10. أنشئ أو ساعد في الحفاظ على صفحة رَبيدة آنّا في ويكيبيديا بلغتك."

msgid "page.about.help.text11"
msgstr "11. نتطلع لوضع إعلانات صغيرة وجميلة غير مزعجة. فأخبرنا إن كنت ترغب في الإعلان على رَبيدة آنّا."

msgid "page.faq.help.mirrors"
msgstr "نُقدّر من ينقل موقعنا و<a %(a_mirrors)s>يُظهر</a> ما فيه، وسندعم كلّ من استطاع ماليًا."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "لمزيد من المعلومات حول كيفية التطوع، راجع صفحة <a %(a_volunteering)s>التطوع والمكافآت</a> الخاصة بنا."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "لماذا تكون التنزيلات البطيئة بطيئة جدًا؟"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "نحن حرفياً لا نملك الموارد الكافية لتوفير تنزيلات عالية السرعة للجميع في العالم، بقدر ما نود ذلك. إذا كان هناك متبرع غني يرغب في تقديم هذا لنا، فسيكون ذلك رائعًا، ولكن حتى ذلك الحين، نحن نحاول جاهدين. نحن مشروع غير ربحي بالكاد يستطيع الاستمرار من خلال التبرعات."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "لهذا السبب قمنا بتنفيذ نظامين للتنزيل المجاني، مع شركائنا: خوادم مشتركة مع تنزيلات بطيئة، وخوادم أسرع قليلاً مع قائمة انتظار (لتقليل عدد الأشخاص الذين يقومون بالتنزيل في نفس الوقت)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "لدينا أيضًا <a %(a_verification)s>تحقق من المتصفح</a> لتنزيلاتنا البطيئة، لأن الروبوتات والبرامج الخبيثة ستسيء استخدامها، مما يجعل الأمور أبطأ للمستخدمين الشرعيين."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "لاحظ أنه عند استخدام متصفح Tor، قد تحتاج إلى ضبط إعدادات الأمان الخاصة بك. في أدنى الخيارات، المسماة \"قياسي\"، ينجح تحدي Cloudflare turnstile. في الخيارات الأعلى، المسماة \"أكثر أمانًا\" و\"الأكثر أمانًا\"، يفشل التحدي."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "في بعض الأحيان، قد تتوقف التنزيلات الكبيرة بسبب بطء الاتصال. نوصي باستخدام مدير تنزيل (مثل JDownloader) لاستئناف التنزيلات الكبيرة تلقائيًا."

msgid "page.donate.faq.title"
msgstr "سؤال وجواب عن التبرع"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s> هل تتجدّد الإشتراكات تلقائيًا؟ </div> <strong>لا</strong> تتجدد الإشتراكات تلقائيًا. تستطيع الإنضمام بالمدة التي تُريد قصيرة كانت أم طويلة."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>هل يمكنني ترقية عضويتي أو الحصول على عضويات متعددة؟</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s> أتوجد طرق أخرى للدفع؟ </div> حاليا لا. أغلبهم يُحاربون ربائد كهذه، لهذا نأخذ حذرنا متى استطعنا. إن كانت عندك طرق أخرى أكثر أمانًا وأسهل استخدامًا فراسلنا %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>ماذا تعني النطاقات لكل شهر؟</div> يمكنك الوصول إلى الجانب الأدنى من النطاق بتطبيق جميع الخصومات، مثل اختيار فترة أطول من شهر."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>على ماذا تنفق التبرعات؟</div> 100%% لحفظ معرفة وثقافة العالم وجعلها في متناول الجميع. فنصرف أغلبها على الخوادم والتخزين وأمور الموقع التقنية الأخرى. ولن يحصل أيُّ فرد من الفريق على قرش واحد من هذه التبرعات."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>هل بإمكاني التبرع بمبلغ كبير؟</div> سيُذهلنا هذا! فتواصل معنا بـ%(email)s إن أردت التبرع بأكثر من ثلاثة آلاف دولار."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>هل يمكنني التبرع دون أن أصبح عضوًا؟</div> بالتأكيد. نحن نقبل التبرعات بأي مبلغ على هذا العنوان Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "كيف أرفع كتبًا جديدة؟"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "بدلاً من ذلك، يمكنك تحميلها إلى مكتبة الزّاي <a %(a_upload)s>هنا</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "لعمليات التحميل الصغيرة (حتى 10,000 ملف) يرجى تحميلها إلى كل من %(first)s و%(second)s."

msgid "page.upload.text1"
msgstr "نقترح الآن رفع كتب جديدة للفروع المعدّلة من مكتبة التَّكوين. هذا <a %(a_guide)s> دليل مفيد </a>. لاحظ أن كلا الفرعين اللذين فهرسناهما على موقعنا هذا يسحبان من نفس النظام."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "بالنسبة لـ Libgen.li، تأكد من تسجيل الدخول أولاً على <a %(a_forum)s>منتداهم</a> باستخدام اسم المستخدم %(username)s وكلمة المرور %(password)s، ثم العودة إلى <a %(a_upload_page)s>صفحة التحميل الخاصة بهم</a>."

msgid "common.libgen.email"
msgstr "إن لم يُقبل بريدك الرقمي من منتديات مكتبة التَّكوين، فسجّل بـ<a %(a_mail)s> Proton Mail </a> (مجانًا). و<a %(a_manual)s> اطلب </a> تفعيل حسابك."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "يرجى ملاحظة أن mhut.org يحظر بعض نطاقات IP، لذا قد يكون من الضروري استخدام VPN."

msgid "page.upload.large.text"
msgstr "الملفات الكثيرة (أكثر من عشرة ألف ملف) راسلنا بـ%(a_email)s لرفعها، ذلك لأن هذا العدد الكبير لا يُقبل من مكتبة الزّاي ومكتبة التَّكوين."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "لتحميل الأوراق الأكاديمية، يرجى أيضًا (بالإضافة إلى Library Genesis) التحميل إلى <a %(a_stc_nexus)s>STC Nexus</a>. هم أفضل مكتبة ظل للأوراق الجديدة. لم نقم بدمجهم بعد، لكننا سنفعل ذلك في وقت ما. يمكنك استخدام <a %(a_telegram)s>روبوت التحميل على Telegram</a>، أو الاتصال بالعنوان المدرج في رسالتهم المثبتة إذا كان لديك الكثير من الملفات لتحميلها بهذه الطريقة."

msgid "page.faq.request.title"
msgstr "كيف أطلب كتبًا؟"

msgid "page.request.cannot_accomodate"
msgstr "لا نقبل طلبات لتوفير الكتب حاليًا."

msgid "page.request.forums"
msgstr "اكتب طلبك في منتديات مكتبة الزّاي والتَّكوين."

msgid "page.request.dont_email"
msgstr "لا تكتب لنا بريدًا تطلب فيه كتابًا."

msgid "page.faq.metadata.title"
msgstr "أتجمعون البيانات الوصفية؟"

msgid "page.faq.metadata.indeed"
msgstr "نعم نجمع."

msgid "page.faq.1984.title"
msgstr "نزّلت كتابًا منكم، فهل سيُقبض علي لانتهاك حقٍّ ما؟"

msgid "page.faq.1984.text"
msgstr "لا تحمل همًّا، نزّل أُناسٌ كثر من المواقع الموصولة ولم يطرأ عليهم شيء. إلّا أننا نوصي باستخدام VPN (مدفوع)، أو <a %(a_tor)s>Tor</a> (مجاني)."

msgid "page.faq.save_search.title"
msgstr "كيف أحفظ إعدادات بحثي؟"

msgid "page.faq.save_search.text1"
msgstr "حدد ما تفضل من الإعدادات، ثم دع مربع البحث فارغًا، وانقر على «بحث»\"، واحفظ الصفحة بمؤشّر متصفحك."

msgid "page.faq.mobile.title"
msgstr "أعندكم تطبيق للهاتف النقال؟"

msgid "page.faq.mobile.text1"
msgstr "لا نملك تطبيقًا رسميًا لنا، ولكن يمكنك حفظ موقعنا كتطبيق."

msgid "page.faq.mobile.android"
msgstr "<strong>أندرويد:</strong> انقر على قائمة النقاط الثلاث أعلى اليمين، واختر «إضافة إلى الشاشة الرئيسة»."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> انقر على زر «مشاركة» في الأسفل، واختر «إضافة إلى الشاشة الرئيسة»."

msgid "page.faq.api.title"
msgstr "أعندكم واجهة برمجة التطبيقات (API)؟"

msgid "page.faq.api.text1"
msgstr "لدينا واجهة برمجة تطبيقات JSON للأعضاء صالحة ومستقرة، للحصول على رابط تنزيل سريع: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (التوثيق داخل JSON نفسه)."

msgid "page.faq.api.text2"
msgstr "لحالات الاستخدام الأخرى، مثل تكرار جميع ملفاتنا، وبناء بحث مخصص، وما إلى ذلك، نوصي <a %(a_generate)s>بإنشاء</a> أو <a %(a_download)s>تنزيل</a> قواعد بياناتنا ElasticSearch وMariaDB. واستكشف بياناتنا كما هي يدويًا <a %(a_explore)s>من خلال ملفات JSON</a>."

msgid "page.faq.api.text3"
msgstr "ويمكنك تنزيل قائمتنا للتورنت كـ <a %(a_torrents)s>JSON</a> أيضًا."

msgid "page.faq.torrents.title"
msgstr "سؤال وجواب عن التورنت"

msgid "page.faq.torrents.q1"
msgstr "أود المساعدة في النشر والتوزيع، لكن ليس لدي مساحة كبيرة على القرص."

msgid "page.faq.torrents.a1"
msgstr "استخدم <a %(a_list)s>مولد قائمة التورنت</a> لإنشاء قائمة بالتورنتات التي تحتاج للتوزع أكثر من غيرها، وضمن مساحة تخزينك."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "التورنتات بطيئة جدًا؛ هل يمكنني تنزيل البيانات مباشرة منك؟"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "نعم، انظر صفحة <a %(a_llm)s>بيانات LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "هل يمكنني تنزيل مجموعة فرعية فقط من الملفات، مثل لغة معينة أو موضوع معين؟"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "الإجابة القصيرة: ليس بسهولة."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "الإجابة الطويلة:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "تحتوي معظم التورنتات على الملفات مباشرة، مما يعني أنه يمكنك توجيه عملاء التورنت لتنزيل الملفات المطلوبة فقط. لتحديد الملفات التي يجب تنزيلها، يمكنك <a %(a_generate)s>توليد</a> بياناتنا الوصفية، أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا. للأسف، تحتوي بعض مجموعات التورنت على ملفات .zip أو .tar في الجذر، وفي هذه الحالة تحتاج إلى تنزيل التورنت بالكامل قبل أن تتمكن من اختيار الملفات الفردية."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(لدينا <a %(a_ideas)s>بعض الأفكار</a> للحالة الأخيرة على أي حال.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "لا توجد أدوات سهلة الاستخدام لتصفية التورنت حتى الآن، لكننا نرحب بالمساهمات."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "كيف تتعاملون مع التكرارات في التورنتات؟"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "نحاول الحفاظ على الحد الأدنى من التكرار أو التداخل بين التورنتات في هذه القائمة، ولكن لا يمكن دائمًا تحقيق ذلك، ويعتمد بشكل كبير على سياسات المكتبات المصدرية. بالنسبة للمكتبات التي تصدر تورنتاتها الخاصة، فإن الأمر خارج عن أيدينا. بالنسبة للتورنتات التي تصدرها رَبيدةُ آنّا، نقوم بإزالة التكرار فقط بناءً على تجزئة MD5، مما يعني أن الإصدارات المختلفة من نفس الكتاب لا يتم إزالة تكرارها."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "هل يمكنني الحصول على قائمة التورنت بصيغة JSON؟"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "نعم."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "لا أرى ملفات PDF أو EPUB في التورنتات، فقط ملفات ثنائية؟ ماذا أفعل؟"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "هذه في الواقع ملفات PDF و EPUB، لكنها لا تحتوي على امتداد في العديد من تورنتاتنا. هناك مكانان يمكنك فيهما العثور على البيانات الوصفية لملفات التورنت، بما في ذلك أنواع الملفات/الامتدادات:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. كل مجموعة أو إصدار له بيانات وصفية خاصة به. على سبيل المثال، <a %(a_libgen_nonfic)s>تورنتات Libgen.rs</a> لديها قاعدة بيانات وصفية مقابلة مستضافة على موقع Libgen.rs. نحن عادةً نربط بموارد البيانات الوصفية ذات الصلة من صفحة <a %(a_datasets)s>مجموعة البيانات</a> لكل مجموعة."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. نوصي <a %(a_generate)s>بتوليد</a> أو <a %(a_download)s>تنزيل</a> قواعد بيانات ElasticSearch وMariaDB الخاصة بنا. تحتوي هذه على خريطة لكل سجل في رَبيدةُ آنّا إلى ملفات التورنت المقابلة له (إذا كانت متاحة)، تحت \"torrent_paths\" في JSON الخاص بـ ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "لماذا لا يستطيع عميل التورنت الخاص بي فتح بعض ملفات التورنت / روابط المغناطيس الخاصة بكم؟"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "بعض عملاء التورنت لا يدعمون أحجام القطع الكبيرة، والتي تحتوي عليها الكثير من ملفات التورنت لدينا (بالنسبة للملفات الأحدث لم نعد نفعل ذلك - حتى وإن كان ذلك صالحًا وفقًا للمواصفات!). لذا جرب عميلًا مختلفًا إذا واجهت هذه المشكلة، أو اشتكِ إلى صانعي عميل التورنت الخاص بك."

#, fuzzy
msgid "page.faq.security.title"
msgstr "هل لديكم برنامج إفصاح مسؤول؟"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "نرحب بالباحثين الأمنيين للبحث عن الثغرات في أنظمتنا. نحن من المؤيدين الكبار للإفصاح المسؤول. اتصل بنا <a %(a_contact)s>هنا</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "نحن غير قادرين حاليًا على منح مكافآت الأخطاء، باستثناء الثغرات التي لديها <a %(a_link)s>إمكانية تعريض هويتنا للخطر</a>، والتي نقدم لها مكافآت تتراوح بين 10 آلاف و50 ألف دولار. نود أن نقدم نطاقًا أوسع لمكافآت الأخطاء في المستقبل! يرجى ملاحظة أن هجمات الهندسة الاجتماعية خارج النطاق."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "إذا كنت مهتمًا بأمن المعلومات الهجومي، وترغب في المساعدة في أرشفة معرفة وثقافة العالم، تأكد من الاتصال بنا. هناك العديد من الطرق التي يمكنك من خلالها المساعدة."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "هل هناك المزيد من الموارد حول رَبيدةُ آنّا؟"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>مدونة آنّا</a>، <a %(a_reddit_u)s>Reddit</a>، <a %(a_reddit_r)s>Subreddit</a> — تحديثات منتظمة"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>برنامج آنّا</a> — كودنا مفتوح المصدر"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>ترجمة على برنامج آنّا</a> — نظام الترجمة الخاص بنا"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>مجموعات البيانات</a> — حول البيانات"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>، <a %(a_se)s>.se</a>، <a %(a_org)s>.org</a> — نطاقات بديلة"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>ويكيبيديا</a> — المزيد عنا (يرجى المساعدة في تحديث هذه الصفحة، أو إنشاء واحدة للغتك الخاصة!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "كيف أبلغ عن انتهاك حقوق الطبع والنشر؟"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "نحن لا نستضيف أي مواد محمية بحقوق الطبع والنشر هنا. نحن محرك بحث، وبالتالي نقوم فقط بفهرسة البيانات الوصفية المتاحة بالفعل للجمهور. عند التنزيل من هذه المصادر الخارجية، نقترح عليك التحقق من القوانين في نطاق سلطتك القضائية فيما يتعلق بما هو مسموح به. نحن غير مسؤولين عن المحتوى المستضاف من قبل الآخرين."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "إذا كانت لديك شكاوى حول ما تراه هنا، فإن أفضل رهان لك هو الاتصال بالموقع الأصلي. نحن نقوم بانتظام بسحب تغييراتهم إلى قاعدة بياناتنا. إذا كنت تعتقد حقًا أن لديك شكوى DMCA صالحة يجب أن نرد عليها، يرجى ملء <a %(a_copyright)s>نموذج شكوى DMCA / حقوق الطبع والنشر</a>. نحن نأخذ شكاويك على محمل الجد، وسنعود إليك في أقرب وقت ممكن."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "أكره الطريقة التي تديرون بها هذا المشروع!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "نود أيضًا أن نذكّر الجميع بأن جميع أكوادنا وبياناتنا مفتوحة المصدر بالكامل. هذا أمر فريد من نوعه لمشاريع مثل مشروعنا — نحن غير مدركين لأي مشروع آخر يحتوي على كتالوج ضخم مماثل ومفتوح المصدر بالكامل أيضًا. نرحب بشدة بأي شخص يعتقد أننا ندير مشروعنا بشكل سيء أن يأخذ أكوادنا وبياناتنا ويؤسس مكتبته الظل الخاصة! نحن لا نقول هذا بدافع الحقد أو شيء من هذا القبيل — نحن نعتقد بصدق أن هذا سيكون رائعًا لأنه سيرفع المعايير للجميع، ويحافظ بشكل أفضل على إرث البشرية."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "هل لديك مراقب وقت التشغيل؟"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "يرجى الاطلاع على <a %(a_href)s>هذا المشروع الممتاز</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "كيف يمكنني التبرع بالكتب أو المواد المادية الأخرى؟"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "يرجى إرسالها إلى <a %(a_archive)s>Internet Archive</a>. سيقومون بحفظها بشكل صحيح."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "من هي آنّا؟"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "أنت آنّا!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "ما هي كتبك المفضلة؟"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "إليك بعض الكتب التي تحمل أهمية خاصة لعالم المكتبات الظلية والحفاظ الرقمي:"

msgid "page.fast_downloads.no_more_new"
msgstr "أكملت تنزيلاتك السريعة لهذا اليوم."

msgid "page.fast_downloads.no_member"
msgstr "انضم لنا لتقدر على التنزيل بسرعة."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "ندعم الآن بطاقات الهدايا من أمازون، وبطاقات الائتمان والخصم، والعملات المشفرة، وAlipay، وWeChat."

msgid "page.home.full_database.header"
msgstr "قاعدة بيانات كاملة"

msgid "page.home.full_database.subtitle"
msgstr "كتبٌ، وأوراق بحثية، ومجلات، وقصص مصورة، وسجلات المكتبة، وبيانات وصفية، …"

msgid "page.home.full_database.search"
msgstr "ابحث"

msgid "page.home.scidb.header"
msgstr "مجمع البيانات العلمية"

msgid "layout.index.header.nav.beta"
msgstr "تجريبي"

msgid "page.home.scidb.scihub_paused"
msgstr "<a %(a_paused)s>أجّل</a> مجمع العلوم رفع أوراق جديدة."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;مجمع العلوم متصل بمجمع البيانات العلمية."

msgid "page.home.scidb.subtitle"
msgstr "وصول مباشر إلى %(count)s ورقة أكاديمية"

msgid "page.home.scidb.placeholder_doi"
msgstr "معرّف الأغراض الرقمي"

msgid "page.home.scidb.open"
msgstr "افتح"

msgid "page.home.scidb.browser_verification"
msgstr "لا يلزم التحقق من متصفحك إن كنت <a %(a_member)s>مشتركًا</a>."

msgid "page.home.archive.header"
msgstr "ربيدة طويلة الأمد"

msgid "page.home.archive.body"
msgstr "مجمع البيانات المستخدمة في رَبيدة آنا مفتوحة كلّها، ويمكن عكس وإظهار ما فيها وتوزيعه بالتورنت. <a %(a_datasets)s>اعرف أكثر…</a>"

msgid "page.home.torrents.body"
msgstr "ساعد بتوزيع ومشاركة ملفات التورنت. <a %(a_torrents)s>اعرف أكثر…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s موزع"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s موزع"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s موزع"

msgid "page.home.llm.header"
msgstr "موارد تدريب النموذج اللغوي الكبير"

msgid "page.home.llm.body"
msgstr "عندنا أكبر مجموعة في العالم من النصوص عالية الجودة. <a %(a_llm)s>اعرف أكثر…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 المرايا: نداء للتطوع"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 نبحث عن متطوعين"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "كمشروع غير ربحي ومفتوح المصدر، نحن دائمًا نبحث عن أشخاص للمساعدة."

msgid "page.home.payment_processor.body"
msgstr "راسلنا إن كنت تُدير وسيلة دفع لا يُعرف بها الدافع. وإننا نبحث عن من يُريد نشر إعلانٍ صغير جميل على موقعنا. وكلّ العوائد تُصب للحفاظ على ما نحفظ هنا."

msgid "layout.index.header.nav.annasblog"
msgstr "مدونة آنا ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "تنزيلات IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 كلّ روابط تنزيل هذا الملف: <a %(a_main)s>الصفحة الرئيسة للملف</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "بوابة «IPFS» #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(كرّر محاولاتك مع «IPFS» لتنجح)"

msgid "page.partner_download.faster_downloads"
msgstr "<a %(a_membership)s>انضم</a> لتحصل على تنزيل سريع وتتفادى التحقق من متصفحك كلّ مرة."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 لإظهار كل مافي مجموعتنا ونسخها = تصفح <a %(a_datasets)s>مجمع البيانات</a> و<a %(a_torrents)s>التورنت</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "بيانات LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "من المفهوم جيدًا أن LLMs تزدهر على البيانات عالية الجودة. لدينا أكبر مجموعة من الكتب والأوراق والمجلات، وما إلى ذلك في العالم، وهي من أعلى مصادر النصوص جودة."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "نطاق وحجم فريدان"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "تحتوي مجموعتنا على أكثر من مئة مليون ملف، بما في ذلك المجلات الأكاديمية والكتب الدراسية والمجلات. نحقق هذا الحجم من خلال دمج المستودعات الكبيرة الموجودة."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "بعض مجموعات مصادرنا متاحة بالفعل بكميات كبيرة (Sci-Hub، وأجزاء من Libgen). مصادر أخرى حررناها بأنفسنا. <a %(a_datasets)s>Datasets</a> تعرض نظرة عامة كاملة."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "تتضمن مجموعتنا ملايين الكتب والأوراق والمجلات من قبل عصر الكتب الإلكترونية. تم إجراء OCR لأجزاء كبيرة من هذه المجموعة، ولديها بالفعل تداخل داخلي قليل."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "كيف يمكننا المساعدة"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "نحن قادرون على توفير وصول عالي السرعة إلى مجموعاتنا الكاملة، وكذلك إلى المجموعات غير المنشورة."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "هذا هو الوصول على مستوى المؤسسات الذي يمكننا توفيره مقابل تبرعات في حدود عشرات الآلاف من الدولارات الأمريكية. نحن أيضًا على استعداد لمبادلة هذا بمجموعات عالية الجودة التي لا نملكها بعد."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "يمكننا رد أموالك إذا كنت قادرًا على تزويدنا بإثراء بياناتنا، مثل:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "التعرف الضوئي على الحروف (OCR)"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "إزالة التداخل (إزالة التكرار)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "استخراج النصوص والبيانات الوصفية"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "دعم الأرشفة طويلة الأمد للمعرفة البشرية، مع الحصول على بيانات أفضل لنموذجك!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>اتصل بنا</a> لمناقشة كيفية العمل معًا."

msgid "page.login.continue"
msgstr "استمر"

msgid "page.login.please"
msgstr "<a %(a_account)s>سجّل دخولك</a> لرؤية هذه الصفحة.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "رَبيدةُ آنّا متوقفة مؤقتًا للصيانة. يرجى العودة بعد ساعة."

#, fuzzy
msgid "page.metadata.header"
msgstr "تحسين البيانات الوصفية"

#, fuzzy
msgid "page.metadata.body1"
msgstr "يمكنك المساعدة في حفظ الكتب من خلال تحسين البيانات الوصفية! أولاً، اقرأ الخلفية حول البيانات الوصفية على رَبيدةُ آنّا، ثم تعلم كيفية تحسين البيانات الوصفية من خلال الربط مع Open Library، واحصل على عضوية مجانية على رَبيدةُ آنّا."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "الخلفية"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "عندما تنظر إلى كتاب على رَبيدةُ آنّا، يمكنك رؤية حقول مختلفة: العنوان، المؤلف، الناشر، الطبعة، السنة، الوصف، اسم الملف، والمزيد. كل هذه المعلومات تُسمى <em>البيانات الوصفية</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "نظرًا لأننا نجمع الكتب من <em>مكتبات مصدر</em> مختلفة، فإننا نعرض أي بيانات وصفية متاحة في مكتبة المصدر تلك. على سبيل المثال، بالنسبة لكتاب حصلنا عليه من Library Genesis، سنعرض العنوان من قاعدة بيانات Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "أحيانًا يكون الكتاب موجودًا في <em>مكتبات مصدر</em> متعددة، والتي قد تحتوي على حقول بيانات وصفية مختلفة. في هذه الحالة، نعرض ببساطة النسخة الأطول من كل حقل، لأن تلك النسخة تحتوي على الأرجح على المعلومات الأكثر فائدة! سنظل نعرض الحقول الأخرى أسفل الوصف، مثل \"عنوان بديل\" (ولكن فقط إذا كانت مختلفة)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "نقوم أيضًا باستخراج <em>الأكواد</em> مثل المعرفات والمصنفات من مكتبة المصدر. <em>المعرفات</em> تمثل بشكل فريد طبعة معينة من كتاب؛ أمثلة على ذلك هي ISBN، DOI، معرف Open Library، معرف Google Books، أو معرف Amazon. <em>المصنفات</em> تجمع بين كتب متشابهة متعددة؛ أمثلة على ذلك هي Dewey Decimal (DCC)، UDC، LCC، RVK، أو GOST. أحيانًا تكون هذه الأكواد مرتبطة بشكل صريح في مكتبات المصدر، وأحيانًا يمكننا استخراجها من اسم الملف أو الوصف (بشكل أساسي ISBN و DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "يمكننا استخدام المعرفات للعثور على سجلات في <em>مجموعات البيانات الوصفية فقط</em>، مثل OpenLibrary، ISBNdb، أو WorldCat/OCLC. هناك علامة تبويب <em>البيانات الوصفية</em> محددة في محرك البحث الخاص بنا إذا كنت ترغب في تصفح تلك المجموعات. نستخدم السجلات المطابقة لملء حقول البيانات الوصفية المفقودة (مثل إذا كان العنوان مفقودًا)، أو مثل \"عنوان بديل\" (إذا كان هناك عنوان موجود)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "لمعرفة بالضبط من أين جاءت البيانات الوصفية لكتاب ما، انظر إلى علامة التبويب <em>“التفاصيل التقنية”</em> في صفحة الكتاب. تحتوي على رابط إلى JSON الخام لذلك الكتاب، مع إشارات إلى JSON الخام للسجلات الأصلية."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "لمزيد من المعلومات، انظر الصفحات التالية: <a %(a_datasets)s>Datasets</a>، <a %(a_search_metadata)s>البحث (علامة تبويب البيانات الوصفية)</a>، <a %(a_codes)s>مستكشف الأكواد</a>، و<a %(a_example)s>مثال على JSON للبيانات الوصفية</a>. أخيرًا، يمكن <a %(a_generated)s>توليد</a> أو <a %(a_downloaded)s>تحميل</a> جميع بياناتنا الوصفية كقواعد بيانات ElasticSearch و MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "الربط مع Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "إذاً إذا واجهت ملفًا يحتوي على بيانات وصفية سيئة، كيف يجب عليك إصلاحه؟ يمكنك الذهاب إلى مكتبة المصدر واتباع إجراءاتها لإصلاح البيانات الوصفية، ولكن ماذا تفعل إذا كان الملف موجودًا في مكتبات مصدر متعددة؟"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "هناك معرف واحد يتم التعامل معه بشكل خاص على رَبيدةُ آنّا. <strong>حقل annas_archive md5 في Open Library يتجاوز دائمًا جميع البيانات الوصفية الأخرى!</strong> دعونا نعود قليلاً ونتعلم عن Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "تأسست Open Library في عام 2006 بواسطة آرون شوارتز بهدف \"صفحة ويب واحدة لكل كتاب تم نشره على الإطلاق\". إنها نوع من ويكيبيديا لبيانات الكتب الوصفية: يمكن للجميع تحريرها، وهي مرخصة بحرية، ويمكن تنزيلها بكميات كبيرة. إنها قاعدة بيانات للكتب تتماشى بشكل كبير مع مهمتنا — في الواقع، رَبيدةُ آنّا مستوحاة من رؤية وحياة آرون شوارتز."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "بدلاً من إعادة اختراع العجلة، قررنا توجيه متطوعينا نحو Open Library. إذا رأيت كتابًا يحتوي على بيانات وصفية غير صحيحة، يمكنك المساعدة بالطريقة التالية:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " اذهب إلى <a %(a_openlib)s>موقع Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "ابحث عن سجل الكتاب الصحيح. <strong>تحذير:</strong> تأكد من اختيار <strong>الطبعة</strong> الصحيحة. في Open Library، لديك \"الأعمال\" و\"الطبعات\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "يمكن أن تكون \"العمل\" هو \"هاري بوتر وحجر الفيلسوف\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "يمكن أن تكون \"الطبعة\":"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "الطبعة الأولى لعام 1997 التي نشرتها بلومزبري وتتكون من 256 صفحة."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "الطبعة الورقية لعام 2003 التي نشرتها رينكوست بوكس وتتكون من 223 صفحة."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "الترجمة البولندية لعام 2000 \"هاري بوتر وحجر الفيلسوف\" بواسطة ميديا رودزينا وتتكون من 328 صفحة."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "جميع هذه الطبعات لها أرقام ISBN مختلفة ومحتويات مختلفة، لذا تأكد من اختيار الطبعة الصحيحة!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "قم بتحرير السجل (أو إنشائه إذا لم يكن موجودًا)، وأضف أكبر قدر ممكن من المعلومات المفيدة! أنت هنا الآن على أي حال، لذا اجعل السجل رائعًا حقًا."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "تحت \"أرقام الهوية\" اختر \"رَبيدةُ آنّا\" وأضف MD5 للكتاب من رَبيدةُ آنّا. هذا هو السلسلة الطويلة من الحروف والأرقام بعد \"/md5/\" في عنوان URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "حاول العثور على ملفات أخرى في رَبيدةُ آنّا تتطابق أيضًا مع هذا السجل، وأضفها كذلك. في المستقبل يمكننا تجميعها كنسخ مكررة في صفحة بحث رَبيدةُ آنّا."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "عند الانتهاء، اكتب عنوان URL الذي قمت بتحديثه للتو. بمجرد تحديثك لما لا يقل عن 30 سجلًا باستخدام MD5 من رَبيدةُ آنّا، أرسل لنا <a %(a_contact)s>بريدًا إلكترونيًا</a> وأرسل لنا القائمة. سنمنحك عضوية مجانية في رَبيدةُ آنّا، حتى تتمكن من القيام بهذا العمل بسهولة أكبر (وكشكر لك على مساعدتك). يجب أن تكون هذه التعديلات عالية الجودة وتضيف كميات كبيرة من المعلومات، وإلا سيتم رفض طلبك. سيتم أيضًا رفض طلبك إذا تم التراجع عن أي من التعديلات أو تصحيحها بواسطة مشرفي Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "لاحظ أن هذا ينطبق فقط على الكتب، وليس الأوراق الأكاديمية أو أنواع الملفات الأخرى. بالنسبة لأنواع الملفات الأخرى، نوصي بالعثور على مكتبة المصدر. قد يستغرق الأمر بضعة أسابيع لتضمين التغييرات في رَبيدةُ آنّا، حيث نحتاج إلى تنزيل أحدث تفريغ بيانات Open Library، وإعادة إنشاء فهرس البحث الخاص بنا."

#, fuzzy
msgid "page.mirrors.title"
msgstr "مرايا: دعوة للمتطوعين"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "لزيادة مرونة رَبيدةُ آنّا، نبحث عن متطوعين لتشغيل المرايا."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "نحن نبحث عن هذا:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "أنت تدير قاعدة الشيفرة المفتوحة المصدر لرَبيدةُ آنّا، وتقوم بتحديث الشيفرة والبيانات بانتظام."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "نسختك مميزة بوضوح كعاكسة، على سبيل المثال \"أرشيف بوب، عاكسة لرَبيدةُ آنّا\"."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "أنت على استعداد لتحمل المخاطر المرتبطة بهذا العمل، وهي كبيرة. لديك فهم عميق للأمن التشغيلي المطلوب. محتويات <a %(a_shadow)s>هذه</a> <a %(a_pirate)s>المشاركات</a> واضحة لك."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "أنت على استعداد للمساهمة في <a %(a_codebase)s>قاعدة الشيفرة</a> الخاصة بنا - بالتعاون مع فريقنا - لتحقيق ذلك."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "في البداية لن نعطيك حق الوصول إلى تنزيلات خادم الشريك الخاص بنا، ولكن إذا سارت الأمور بشكل جيد، يمكننا مشاركة ذلك معك."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "نفقات الاستضافة"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "نحن على استعداد لتغطية نفقات الاستضافة وVPN، في البداية حتى 200 دولار شهريًا. هذا يكفي لخادم بحث أساسي ووكيل محمي من DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "سندفع فقط مقابل الاستضافة بمجرد أن يكون لديك كل شيء معدًا، وقد أثبتت أنك قادر على الحفاظ على الأرشيف محدثًا بالتحديثات. هذا يعني أنك ستدفع من جيبك الخاص لأول 1-2 شهر."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "وقتك لن يتم تعويضه (وكذلك وقتنا)، لأن هذا عمل تطوعي بحت."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "إذا شاركت بشكل كبير في تطوير وتشغيل عملنا، يمكننا مناقشة مشاركة المزيد من إيرادات التبرعات معك، لتستخدمها حسب الضرورة."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "البدء"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "يرجى <strong>عدم الاتصال بنا</strong> لطلب الإذن، أو لطرح أسئلة أساسية. الأفعال أبلغ من الأقوال! كل المعلومات موجودة، لذا ابدأ بإعداد العاكسة الخاصة بك."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "لا تتردد في نشر تذاكر أو طلبات دمج إلى Gitlab الخاص بنا عندما تواجه مشكلات. قد نحتاج إلى بناء بعض الميزات الخاصة بالعاكسة معك، مثل إعادة التسمية من \"رَبيدةُ آنّا\" إلى اسم موقعك، (في البداية) تعطيل حسابات المستخدمين، أو الربط بموقعنا الرئيسي من صفحات الكتب."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "بمجرد أن تكون عاكستك قيد التشغيل، يرجى الاتصال بنا. نود مراجعة أمان التشغيل الخاص بك، وبمجرد أن يكون ذلك قويًا، سنربط بعاكستك، ونبدأ في العمل بشكل أقرب معك."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "شكرًا مقدمًا لأي شخص على استعداد للمساهمة بهذه الطريقة! إنه ليس لضعاف القلوب، ولكنه سيعزز من ديمومة أكبر مكتبة مفتوحة حقًا في تاريخ البشرية."

msgid "page.partner_download.header"
msgstr "تنزيل من موقع الشريك"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ التنزيلات البطيئة متاحة فقط من خلال الموقع الرسمي. قم بالزيارة %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ التنزيلات البطيئة غير متاحة عبر شبكات VPN الخاصة بـ Cloudflare أو من عناوين IP الخاصة بـ Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "يرجى الانتظار <span %(span_countdown)s>%(wait_seconds)s</span> ثانية لتحميل هذا الملف."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 ستخدم عنوان URL التالي لتحميل: <a %(a_download)s>التحميل الان</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "شكرًا لانتظارك، هذا يجعل الموقع متاحًا مجانًا للجميع! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "تحذير: كان هناك الكثير من التنزيلات من عنوان IP الخاص بك خلال الـ ٢٤ ساعة الماضية. قد تكون التنزيلات أبطأ من المعتاد."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "التنزيلات من عنوان IP الخاص بك في آخر 24 ساعة: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "إذا كنت تستخدم VPN، أو اتصال إنترنت مشترك، أو أن مزود خدمة الإنترنت يشارك عناوين IP، فقد يكون هذا التحذير بسبب ذلك."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "من أجل إعطاء الجميع فرصة لتحميل الملفات مجانًا، يجب عليك الانتظار قبل أن تتمكن من تحميل هذا الملف."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "لا تتردد في مواصلة تصفح رَبيدةُ آنّا في علامة تبويب مختلفة أثناء الانتظار (إذا كان متصفحك يدعم تحديث علامات التبويب في الخلفية)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "لا تتردد في انتظار تحميل صفحات تنزيل متعددة في نفس الوقت (ولكن يرجى تنزيل ملف واحد فقط في نفس الوقت لكل خادم)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "بمجرد حصولك على رابط التنزيل، يكون صالحًا لعدة ساعات."

msgid "layout.index.header.title"
msgstr "أرشيف آنا"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "سجل في أرشيف آنا"

msgid "page.scidb.download"
msgstr "نزل"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "لدعم الوصول والحفاظ على المعرفة البشرية على المدى الطويل، انضم كـ <a %(a_donate)s>عضو</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "كمكافأة، 🧬&nbsp;مجمع البيانات العلمية يتم تحميله بشكل أسرع للأعضاء، بدون أي حدود."

msgid "page.scidb.refresh"
msgstr "لا يعمل؟ حاول <a %(a_refresh)s>إعادة التحميل</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "لا توجد معاينة متاحة بعد. قم بتنزيل الملف من <a %(a_path)s>رَبيدةُ آنّا</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;مجمع البيانات العلمية هو استمرار لـ Sci-Hub، بواجهته المألوفة وعرض مباشر لملفات PDF. أدخل DOI الخاص بك لعرضه."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "لدينا مجموعة Sci-Hub الكاملة، بالإضافة إلى أوراق جديدة. يمكن عرض معظمها مباشرة بواجهة مألوفة، مشابهة لـ Sci-Hub. يمكن تنزيل بعضها من مصادر خارجية، وفي هذه الحالة نعرض روابط لتلك المصادر."

msgid "page.search.title.results"
msgstr "%(search_input)s - بحث"

msgid "page.search.title.new"
msgstr "بحث جديد"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "تضمين فقط"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "استبعاد"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "غير مُراجَع"

msgid "page.search.tabs.download"
msgstr "نزل"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "مقالات المجلات"

msgid "page.search.tabs.digital_lending"
msgstr "الإقراض الرقمي"

msgid "page.search.tabs.metadata"
msgstr "البيانات الوصفية"

msgid "common.search.placeholder"
msgstr "عنوان، المؤلف، DOI، ISBN، MD5، …"

msgid "common.search.submit"
msgstr "بحث"

msgid "page.search.search_settings"
msgstr "إعدادات البحث"

msgid "page.search.submit"
msgstr "بحث"

msgid "page.search.too_long_broad_query"
msgstr "استغرق البحث وقتًا طويلاً، وهو أمر شائع بالنسبة للاستعلامات واسعة النطاق. قد لا تكون أعداد التصفيات دقيقة."

msgid "page.search.too_inaccurate"
msgstr "استغرق البحث وقتًا طويلاً، مما يعني أنك قد ترى نتائج غير دقيقة. في بعض الأحيان، تساعد <a %(a_reload)s>إعادة تحميل</a> الصفحة."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "عرض"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "قائمة"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "جدول"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "متقدم"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "ابحث في الأوصاف وتعليقات البيانات الوصفية"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "إضافة حقل بحث محدد"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(ابحث في حقل محدد)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "سنة النشر"

msgid "page.search.filters.content.header"
msgstr "محتوى"

msgid "page.search.filters.filetype.header"
msgstr "نوع الملف"

msgid "page.search.more"
msgstr "المزيد…"

msgid "page.search.filters.access.header"
msgstr "الوصول"

msgid "page.search.filters.source.header"
msgstr "المصدر"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "تم جمعها ومصدرها المفتوح بواسطة AA"

msgid "page.search.filters.language.header"
msgstr "لغة"

msgid "page.search.filters.order_by.header"
msgstr "ترتيب حسب"

msgid "page.search.filters.sorting.most_relevant"
msgstr "الأكثر صلة"

msgid "page.search.filters.sorting.newest"
msgstr "الأحدث"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(سنة النشر)"

msgid "page.search.filters.sorting.oldest"
msgstr "الأقدم"

msgid "page.search.filters.sorting.largest"
msgstr "الأكبر"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(حجم الملف)"

msgid "page.search.filters.sorting.smallest"
msgstr "الأصغر"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(مفتوح المصدر)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "عشوائي"

msgid "page.search.header.update_info"
msgstr "يتم تحديث فهرس البحث شهرياً. يتضمن حالياً مدخلات حتى تاريخ %(last_data_refresh_date)s. للمزيد من المعلومات اطلع على %(link_open_tag)s صفحة البيانات</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "لاستكشاف فهرس البحث بواسطة الأكواد، استخدم <a %(a_href)s>مستكشف الأكواد</a>."

msgid "page.search.results.search_downloads"
msgstr "اكتب في المربع للبحث في كتالوجنا الذي يضم %(count)s من الملفات القابلة للتنزيل مباشرة، والتي <a %(a_preserve)s>نحتفظ بها إلى الأبد</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "في الواقع، يمكن لأي شخص المساعدة في الحفاظ على هذه الملفات عن طريق توزيع <a %(a_torrents)s>قائمة التورنت الموحدة</a> الخاصة بنا."

msgid "page.search.results.most_comprehensive"
msgstr "لدينا حاليًا الكتالوج المفتوح الأكثر شمولاً في العالم للكتب والأبحاث والأعمال المكتوبة الأخرى. نحن نعكس Sci-Hub وLibrary Genesis وZ-Library و<a %(a_datasets)s>والمزيد</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "إذا وجدت \"مكتبات ظل\" أخرى يجب علينا مرآتة، أو إذا كانت لديك أية أسئلة، فيُرجى الاتصال بنا على %(email)s."

msgid "page.search.results.dmca"
msgstr "بالنسبة إلى مطالبات قانون الألفية الجديدة لحقوق طبع ونشر المواد الرقمية / حقوق الطبع والنشر <a %(a_copyright)s>انقر هنا</a>."

msgid "page.search.results.shortcuts"
msgstr "نصيحة: استخدم اختصارات لوحة المفاتيح \"/\" (تركيز البحث)، و\"enter\" (بحث)، و\"j\" (لأعلى)، و\"k\" (لأسفل) للتنقل بشكل أسرع."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "تبحث عن أوراق بحثية؟"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "اكتب في المربع للبحث في كتالوجنا من %(count)s الأوراق الأكاديمية والمقالات العلمية، التي <a %(a_preserve)s>نحافظ عليها للأبد</a>."

msgid "page.search.results.search_digital_lending"
msgstr "اكتب في المربع للبحث عن الملفات في مكتبات الإقراض الرقمية."

msgid "page.search.results.digital_lending_info"
msgstr "يتضمن فهرس البحث هذا حاليًا بيانات وصفية من مكتبة الإقراض الرقمي الخاضع للرقابة في أرشيف الإنترنت. <a %(a_datasets)s>المزيد حول مجموعات البيانات لدينا</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "لمزيد من مكتبات الإعارة الرقمية، راجع <a %(a_wikipedia)s>ويكيبيديا</a> و<a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "اكتب في المربع للبحث عن بيانات التعريف من المكتبات. يمكن أن يكون هذا مفيدًا عند <a %(a_request)s>طلب ملف</a>."

msgid "page.search.results.metadata_info"
msgstr "يتضمن فهرس البحث هذا حاليًا بيانات تعريفية من مصادر بيانات تعريفية مختلفة. <a %(a_datasets)s>المزيد حول مجموعات البيانات لدينا</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "بالنسبة للبيانات الوصفية، نعرض السجلات الأصلية. نحن لا نقوم بدمج السجلات."

msgid "page.search.results.metadata_info_more"
msgstr "هناك العديد والعديد من مصادر البيانات الوصفية للأعمال المكتوبة حول العالم. <a %(a_wikipedia)s>صفحة ويكيبيديا هذه</a> هي بداية جيدة، ولكن إذا كنت تعرف قوائم جيدة أخرى، فيُرجى إخبارنا بذلك."

msgid "page.search.results.search_generic"
msgstr "اكتب في المربع للبحث."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "هذه سجلات بيانات وصفية، <span %(classname)s>وليست</span> ملفات قابلة للتنزيل."

msgid "page.search.results.error.header"
msgstr "خطأ أثناء البحث."

msgid "page.search.results.error.unknown"
msgstr "حاول <a %(a_reload)s>إعادة تحميل الصفحة</a>. إذا استمرت المشكلة، يُرجى مراسلتنا عبر البريد الإلكتروني على %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\"> لم توجد أي ملفات. </span> حاول استخدام مدخلات أقل أو مختلفة في البحث والتنقية."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ يحدث هذا أحيانًا بشكل غير صحيح عندما يكون خادم البحث بطيئًا. في مثل هذه الحالات، يمكن أن يساعد <a %(a_attrs)s>إعادة التحميل</a>."

msgid "page.search.found_matches.main"
msgstr "لقد وجدنا تطابقات في: %(in)s. يمكنك الرجوع إلى عنوان URL الموجود هناك عند <a %(a_request)s>طلب ملف</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "مقالات المجلات (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "الإقراض الرقمي (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "البيانات الوصفية (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "النتائج %(from)s-%(to)s (%(total)s إجمالي)"

msgid "page.search.results.partial_more"
msgstr "مطابقات جزئي %(num)d+"

msgid "page.search.results.partial"
msgstr "مطابقات جزئي %(num)d"

#, fuzzy
msgid "page.volunteering.title"
msgstr "التطوع والمكافآت"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "يعتمد رَبيدةُ آنّا على متطوعين مثلك. نرحب بجميع مستويات الالتزام، ولدينا فئتان رئيسيتان من المساعدة التي نبحث عنها:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>عمل تطوعي خفيف:</span> إذا كان بإمكانك توفير بضع ساعات هنا وهناك، لا يزال هناك العديد من الطرق التي يمكنك من خلالها المساعدة. نكافئ المتطوعين المستمرين بـ <span %(bold)s>🤝 عضويات في رَبيدةُ آنّا</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>العمل التطوعي المكثف (مكافآت مالية تتراوح بين 50 دولارًا أمريكيًا و5000 دولار أمريكي):</span> إذا كنت قادرًا على تخصيص الكثير من الوقت و/أو الموارد لمهمتنا، فنحن نود العمل معك بشكل أوثق. في النهاية يمكنك الانضمام إلى الفريق الداخلي. على الرغم من أن ميزانيتنا محدودة، إلا أننا قادرون على منح <span %(bold)s>💰 مكافآت مالية</span> للعمل الأكثر كثافة."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "إذا لم تكن قادرًا على التطوع بوقتك، فلا يزال بإمكانك مساعدتنا كثيرًا من خلال <a %(a_donate)s>التبرع بالمال</a>، <a %(a_torrents)s>مشاركة التورنت الخاص بنا</a>، <a %(a_uploading)s>رفع الكتب</a>، أو <a %(a_help)s>إخبار أصدقائك عن رَبيدةُ آنّا</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>الشركات:</span> نحن نقدم وصولًا مباشرًا عالي السرعة إلى مجموعاتنا مقابل تبرع على مستوى المؤسسة أو مقابل مجموعات جديدة (مثل عمليات المسح الجديدة، Datasets الممسوحة ضوئيًا، إثراء بياناتنا). <a %(a_contact)s>اتصل بنا</a> إذا كنت مهتمًا. انظر أيضًا صفحتنا <a %(a_llm)s>LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "التطوع الخفيف"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "إذا كان لديك بضع ساعات فراغ، يمكنك المساعدة بعدة طرق. تأكد من الانضمام إلى <a %(a_telegram)s>دردشة المتطوعين على Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "كعربون تقدير، نعطي عادةً 6 أشهر من عضوية \"أمين المكتبة المحظوظ\" للإنجازات الأساسية، وأكثر للعمل التطوعي المستمر. تتطلب جميع الإنجازات عملًا عالي الجودة — العمل الرديء يضرنا أكثر مما ينفعنا وسنرفضه. يرجى <a %(a_contact)s>إرسال بريد إلكتروني إلينا</a> عندما تصل إلى إنجاز."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "المهمة"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "الإنجاز"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "نشر كلمة رَبيدةُ آنّا. على سبيل المثال، من خلال التوصية بالكتب على AA، أو الربط بمشاركات مدونتنا، أو توجيه الناس بشكل عام إلى موقعنا الإلكتروني."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s روابط أو لقطات شاشة."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "يجب أن تُظهر هذه أنك تُخبر شخصًا ما عن رَبيدةُ آنّا، وهو يشكرك."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "تحسين البيانات الوصفية عن طريق <a %(a_metadata)s>الربط</a> مع Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "يمكنك استخدام <a %(a_list)s>قائمة مشكلات الميتاداتا العشوائية</a> كنقطة انطلاق."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "تأكد من ترك تعليق على المشكلات التي تقوم بإصلاحها، حتى لا يكرر الآخرون عملك."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s روابط السجلات التي قمت بتحسينها."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>ترجمة</a> الموقع."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "ترجمة لغة كاملة (إذا لم تكن قريبة من الاكتمال بالفعل)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "تحسين صفحة ويكيبيديا الخاصة برَبيدةُ آنّا بلغتك. تضمين معلومات من صفحة ويكيبيديا الخاصة بـ AA بلغات أخرى، ومن موقعنا الإلكتروني ومدونتنا. إضافة مراجع إلى AA على الصفحات ذات الصلة الأخرى."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "رابط إلى تاريخ التعديل يظهر أنك قمت بمساهمات كبيرة."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "تلبية طلبات الكتب (أو الأوراق، إلخ) على منتديات مكتبة الزّاي أو Library Genesis. ليس لدينا نظام طلبات الكتب الخاص بنا، لكننا نعكس تلك المكتبات، لذا فإن تحسينها يجعل رَبيدةُ آنّا أفضل أيضًا."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s روابط أو لقطات شاشة للطلبات التي قمت بتلبيتها."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "مهام صغيرة منشورة على <a %(a_telegram)s>دردشة المتطوعين على Telegram</a>. عادةً للحصول على عضوية، وأحيانًا لمكافآت صغيرة."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "مهام صغيرة منشورة في مجموعة الدردشة التطوعية لدينا."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "يعتمد على المهمة."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "المكافآت"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "نحن دائمًا نبحث عن أشخاص يمتلكون مهارات برمجة قوية أو مهارات في الأمن الهجومي للمشاركة. يمكنك أن تساهم بشكل جدي في الحفاظ على إرث البشرية."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "كشكر لك، نقدم عضوية للمساهمات القوية. وكشكر كبير، نقدم مكافآت مالية للمهام المهمة والصعبة بشكل خاص. لا ينبغي اعتبار هذا بديلاً عن وظيفة، ولكنه حافز إضافي ويمكن أن يساعد في تغطية التكاليف المتكبدة."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "معظم كودنا مفتوح المصدر، وسنطلب أن يكون كودك كذلك عند منح المكافأة. هناك بعض الاستثناءات التي يمكننا مناقشتها على أساس فردي."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "تُمنح المكافآت لأول شخص يكمل المهمة. لا تتردد في التعليق على تذكرة المكافأة لإعلام الآخرين بأنك تعمل على شيء ما، حتى يتمكن الآخرون من التوقف أو الاتصال بك للتعاون. ولكن كن على علم بأن الآخرين لا يزالون أحرارًا في العمل عليها أيضًا ومحاولة التفوق عليك. ومع ذلك، لا نمنح المكافآت للعمل الرديء. إذا تم تقديم مساهمتين عاليتي الجودة في وقت قريب من بعضهما (في غضون يوم أو يومين)، فقد نختار منح المكافآت لكليهما، وفقًا لتقديرنا، على سبيل المثال 100%% للمساهمة الأولى و50%% للمساهمة الثانية (أي 150%% إجمالاً)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "بالنسبة للمكافآت الكبيرة (خاصة مكافآت التجريف)، يرجى الاتصال بنا عندما تكمل ~5%% منها، وتكون واثقًا من أن طريقتك ستتوسع لتشمل المعلم الكامل. سيتعين عليك مشاركة طريقتك معنا حتى نتمكن من تقديم الملاحظات. أيضًا، بهذه الطريقة يمكننا أن نقرر ما يجب فعله إذا كان هناك عدة أشخاص يقتربون من المكافأة، مثل منحها لعدة أشخاص، تشجيع الناس على التعاون، إلخ."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "تحذير: المهام ذات المكافآت العالية <span %(bold)s>صعبة</span> — قد يكون من الحكمة البدء بالمهام الأسهل."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "اذهب إلى <a %(a_gitlab)s>قائمة مشكلات Gitlab الخاصة بنا</a> وقم بالفرز حسب \"أولوية التسمية\". هذا يظهر تقريبًا ترتيب المهام التي نهتم بها. المهام التي لا تحتوي على مكافآت صريحة لا تزال مؤهلة للعضوية، خاصة تلك التي تم وضع علامة \"مقبولة\" و\"المفضلة لدى آنّا\". قد ترغب في البدء بمشروع \"مبتدئ\"."

#, fuzzy
msgid "blog.template.subheading"
msgstr "تحديثات حول <a %(wikipedia_annas_archive)s>رَبيدةُ آنّا</a>، أكبر مكتبة مفتوحة حقًا في تاريخ البشرية."

msgid "layout.index.title"
msgstr "أرشيف آنا"

msgid "layout.index.meta.description"
msgstr "أكبر مكتبة مفتوحة المصدر مفتوحة المصدر في العالم. يتضمن Sci-Hub و Library Genesis و Z-Library والمزيد."

msgid "layout.index.meta.opensearch"
msgstr "ابحث في أرشيف آنا"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "رَبيدةُ آنّا تحتاج إلى مساعدتك!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "يحاول الكثيرون إسقاطنا، لكننا نقاوم."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "إذا تبرعت الآن، ستحصل على <strong>ضعف</strong> عدد التنزيلات السريعة."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "صالح حتى نهاية هذا الشهر."

msgid "layout.index.header.nav.donate"
msgstr "تبرع"

msgid "layout.index.header.banner.holiday_gift"
msgstr "إنقاذ المعرفة البشرية: هدية العيد العظيمة!"

msgid "layout.index.header.banner.surprise"
msgstr "مفاجأة أحد أفراد أسرته، ومنحهم حسابا مع العضوية."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "لزيادة مرونة رَبيدةُ آنّا، نحن نبحث عن متطوعين لتشغيل المرايا."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "الهدية المثالية لعيد الحب!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "لدينا طريقة جديدة للتبرع: %(method_name)s الرجاء التفكير في %(donate_link_open_tag)s التبرع </a> — إدارة هذا الموقع ليست قليلة التكلفة، وتبرعك حقاً يصنع فرقاً. شكراً جزيلاً لك."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "نحن ندير حملة لجمع التبرعات <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\"> النسخ الاحتياطي </a> لأكبر الرسوم الهزلية مكتبة الظل في العالم. شكرا لدعمك! <a href=\"/donate\"> تبرع. </a> إذا كنت لا تستطيع التبرع ، ففكر في دعمنا بإخبار أصدقائك، ومتابعتنا على <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit </a> أو <a href=\"https://t.me/annasarchiveorg\">Telegram </a>."

msgid "layout.index.header.recent_downloads"
msgstr "أحدث التنزيلات:"

msgid "layout.index.header.nav.search"
msgstr "بحث"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "الأسئلة الشائعة"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "تحسين البيانات الوصفية"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "التطوع والمكافآت"

msgid "layout.index.header.nav.datasets"
msgstr "مجموعات البيانات"

msgid "layout.index.header.nav.torrents"
msgstr "ملفات تورنت"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "نشاط"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "مستكشف الأكواد"

msgid "layout.index.header.nav.llm_data"
msgstr "بيانات LLM"

msgid "layout.index.header.nav.home"
msgstr "الصفحة الرئيسية"

msgid "layout.index.header.nav.annassoftware"
msgstr "برمجيات آنا ↖"

msgid "layout.index.header.nav.translate"
msgstr "ترجمة ↗"

msgid "layout.index.header.nav.login_register"
msgstr "دخول / تسجيل"

msgid "layout.index.header.nav.account"
msgstr "حساب"

msgid "layout.index.footer.list1.header"
msgstr "أرشيف آنا"

msgid "layout.index.footer.list2.header"
msgstr "للتواصل"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "مطالبات حقوق التأليف والنشر / DMCA"

msgid "layout.index.footer.list2.reddit"
msgstr "ريديت"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "متقدم"

msgid "layout.index.header.nav.security"
msgstr "حماية"

msgid "layout.index.footer.list3.header"
msgstr "البدائل"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "غير مُنتمٍ لأي جهة"

msgid "page.search.results.issues"
msgstr "❌ هذا الملف قد يكون فيه مشاكل."

msgid "page.search.results.fast_download"
msgstr "تحميل سريع"

msgid "page.donate.copy"
msgstr "نسخ"

msgid "page.donate.copied"
msgstr "منسوخ!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "السابق"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "التالي"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "%(extra)s %(link)s :المرآة رقم %(num)d"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5٪ من التراث المكتوب للإنسانية محفوظ إلى الأبد %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "مجموعات البيانات ▶ الملفات ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "تنزيل كتاب إلكتروني مجاني / ملف %(extension)s من:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "لدينا خيارات متعددة للتحميل في حالة تعطل إحداها. جميعها تحمل نفس الملف."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "لاحظ أن أرشيف آنا لا يستضيف أي محتوى هنا. نحن فقط نحمل وصلات لمواقع أناس آخرين. لو رأيت أن لديك شكوى صحيحة تبعاً لقانون الألفية للملكية الرقمية، الرجاء الاطلاع على صفحة %(about_link)sعن الموقع</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "مرآة Z-Library مجهول رقم %(num)d"

#~ msgid "page.donate.title"
#~ msgstr "تبرع"

#~ msgid "page.donate.header"
#~ msgstr "تبرع"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive هو مشروع غير ربحي مفتوح المصدر، يديره متطوعون فقط. نأخذ التبرعات لتغطية تكاليفنا، والتي تشمل الاستضافة، وأسماء النطاق، والتطوير، وغيرها من النفقات."

#~ msgid "page.donate.text2"
#~ msgstr "من خلال مساهماتكم يمكننا الحفاظ على خدمة هذا الموقع، وتحسين ميزاته، والحفاظ على المزيد من المجموعات."

#~ msgid "page.donate.text3"
#~ msgstr "تبرعات حديثة: %(donations)s شكرا للجميع من أجل كرمك. نحن حقا نقدر وضع ثقتكم فينا مهما كان المبلغ الذي تعطي."

#~ msgid "page.donate.text4"
#~ msgstr "للتبرع اختار الطريقة المفضلة لديك. إذا كان لديك أي مشكلة اتصل بنا علي %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "بطاقة الائتمان/الخصم"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "عملة التشفير"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "أسئلة"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "اذهب إلى %(link_open_tag)sهذه الصفحة</a> اتبع التعليمات إما عن طريق المسح \"QR code\" أو النقر فوق الرابط “paypal.me”. إذا لم ينجح الأمر قم بتحمل الصفحة مرة أخرى."

#~ msgid "page.donate.cc.header"
#~ msgstr "بطاقة الائتمان/الخصم"

#~ msgid "page.donate.cc.text1"
#~ msgstr "نحن نستخدم Sendwyre لإيداع الأموال مُبَاشَرَةً إلى محفظة \"Bitcoin (BTC)\". يستغرق حوالي 5 دقائق لإكمال."

#~ msgid "page.donate.cc.text2"
#~ msgstr "هذه الطريقة تستلزم حد أدنى لمبلغ المعاملة يبلغ 30 دولارًا، ورسوم تبلغ حوالي 5 دولارات."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "خطوات:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1.نسخ عنوان محفظة الخاصة بنا \"Bitcoin (BTC)\": %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "اذهب إلى %(link_open_tag)sهذه الصفحة</a> وانقر فوق \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. الصق العنوان المحفظتنا و اتبع الحطوات"

#~ msgid "page.donate.crypto.header"
#~ msgstr "عملة التشفير"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(يعمل أيضًا بالنسبة إلى BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "الرجاء استخدام هذا الحساب ال%(link_open_tag)s\"Alipay\"</a> لإرسال تبرعك. اذا لم يعمل قم بتحمل الصفحة مرة آخرى منذ قد يطتيك حساب آخر."

#~ msgid "page.donate.alipay.url"
#~ msgstr "الرابط"

#~ msgid "page.donate.out_of_order"
#~ msgstr "خيار التبرع هذا خارج النظام حاليًا. يرجى التحقق مرة أخرى في وقت لاحق. شكرا على الرغبة في التبرع، نحن نقدر ذلك حقًا!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "الرجاء استخدام %(link_open_tag)s ال\"Pix\" هذا صفحة</a> لإرسال تبرعك. اذا لم يعمل قم بتحمل الصفحة مرة آخرى منذ قد يطتيك حساب آخر."

#~ msgid "page.donate.faq.header"
#~ msgstr "الأسئلة الشائعة"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">أرشيف آنا </span>مشروع يهدف إلى تصنيف جميع الكتب الموجودة على الإطلاق، من خلال جمع البيانات من مصادر متنوعة. كما نقوم بتتبع تقدم الإنسانية نحو جعل كل هذه الكتب متوفرة بسهولة بشكل رقمي عب <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">مكتبات الظل. </a> تعلم المزيد <a href=\"/about\"> عنا.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "كتاب (أي نوع)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "الصفحة الرئيسية"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "مجموعات البيانات ◀ ISBN %(isbn_input)s ◀ ISBNs"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "لم يتم العثور عليه"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "%(isbn_input)s ليس رقم ISBN صحبح. أرقام ISBN تتكون من 10 - 13 خانة بالإضافة إلى الشرطات الاختيارية. يجب أن تكون الخانات جميعها أرقام باستثناء الخانة الأخيرة التي من الممكن أن تكون X. الخانة الأخيرة تستخدم للتدقيق حيث يجب أن تطابق قيمة تحقق محسوبة من الأرقام الأخرى. كما يجب أن تكون في نطاق صحيح محدد من الوكالة الدولية لترقيم الكتب."

#~ msgid "page.isbn.results.text"
#~ msgstr "الملفات مطابقه في قاعدة البيانات الخاصة بنا:"

#~ msgid "page.isbn.results.none"
#~ msgstr "لم يتم العثور على الملفات مطابقه في قاعدة البيانات الخاصة بنا."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "بحث ◀ %(num)d + نتائج البحث عن <span class=\"italic\">%(search_input)s</span> (في البيانات الميتا لمكتبات الظل)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "بحث ◀ %(num)d نتائج البحث عن <span class=\"italic\">%(search_input)s</span> (في بيانات الميتا لمكتبات الظل)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "بحث ◀‎‏ حطأ البحث ل <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "بحث ◀ بحث جديد"

#~ msgid "page.donate.header.text3"
#~ msgstr "يمكنك ايضا التبرع دون الحاجة لانشاء حساب (نفس طرق الدفع مدعومة للتبرعات الفردية وكذلك للعضويات):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "تبرع لمرة واحدة بشكل مجهول (لا مزايا)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "حدد خيار الدفع. يرجى التفكير في استخدام الدفع المستند إلى التشفير %(bitcoin_icon)s ، لأننا نتحمل (طريقة) رسوم أقل."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "إذا كان لديك بالفعل أموال مشفرة ، فهذه هي عناويننا."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "شكرا جزيلا للمساعدة! لن يكون هذا المشروع ممكنًا بدونكم."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "للتبرع باستخدام PayPal US ، سنستخدم PayPal Crypto ، والذي يتيح لنا عدم الكشف عن هويتنا. نحن نقدر لك الوقت الذي تقضيه في تعلم كيفية التبرع باستخدام هذه الطريقة ، لأنها تساعدنا كثيرًا."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "اتبع التعليمات لشراء Bitcoin (BTC). ما عليك سوى شراء المبلغ الذي تريد التبرع به."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "إذا فقدت بعض عملات البيتكوين بسبب التقلبات أو الرسوم ، <em> من فضلك لا تقلق </em>. هذا أمر طبيعي مع العملات المشفرة ، لكنه يسمح لنا بالعمل دون الكشف عن هويتنا."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "أدخل عنوان Bitcoin (BTC) الخاص بنا كمستلم ، واتبع التعليمات لإرسال تبرعك:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "الرجاء استخدام <a %(a_account)s> حساب Alipay هذا </a> لإرسال تبرعك."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "الرجاء استخدام <a %(a_account)s> حساب Pix هذا </a> لإرسال تبرعك."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "إذا كانت طريقة دفعتك ليس في القائمة, أسهل شيء تفعله سيكون لتحميل<a href=\"https://paypal.com/\">PayPal</a> أو <a href=\"https://coinbase.com/\">Coinbase</a> على هاتفك, وشراء قليلا من \"Bitcoin (BTC)\" هناك. يمكنك بعد ذلك إرسالها إلى عنواننا: %(address)s. في معظم البلدان, هذا يجب أن يستغرق بضع دقائق فقط للإعداد."

#~ msgid "page.search.results.error.text"
#~ msgstr "حاول <a href=\"javascript:location.reload()\">تحمل الصفحة مرة أخرى</a>. إذا استمرت المشكلة الرجاء دعونا نعرف <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> أو <a href=\"https://t.me/annasarchiveorg\">تليغرام</a>."

#~ msgid "page.donate.login"
#~ msgstr "لكي تصبح عضوًا، يرجى <a href=\"/login\">ادخل او سجل حساب جديد</a>. ان لم ترغب في انشاء حساب، اختر \"تبرع لمرة واحدة بشكل مجهول\" في الاعلى. شكرا على دعمك!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "الصفحة الرئيسية"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "عن الموقع"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "تبرع"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "مجموعات البيانات"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "برنامج الهاتف"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "مدونة آنا"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "برمجيات آنا"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "ترجمة"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "تويتر"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s تورنت"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;محتوى %(libraries)s وأكثر."

#~ msgid "page.home.preservation.text"
#~ msgstr "نحافظ على الكتب ، والأوراق ، والقصص المصورة ، والمجلات ، والمزيد ، من خلال جلب هذه المواد من مختلف <a href=\"https://en.wikipedia.org/wiki/Shadow_library\"> مكتبات الظل </a> معًا في مكان واحد. يتم الاحتفاظ بكل هذه البيانات إلى الأبد من خلال تسهيل تكرارها بكميات كبيرة ، مما ينتج عنه نسخ عديدة حول العالم. هذا التوزيع الواسع ، جنبًا إلى جنب مع التعليمات البرمجية مفتوحة المصدر ، يجعل موقعنا أيضًا مرنًا للإزالة. تعرف على المزيد حول <a href=\"/datasets\"> مجموعات البيانات لدينا </a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "مجموعات البيانات ◀ DOI %(doi_input)s ◀ DOIs"

#~ msgid "page.doi.invalid.header"
#~ msgstr "لم يتم العثور عليه"

#~ msgid "page.doi.invalid.text"
#~ msgstr "%(doi_input)s لا يبدو مثل DOI. يجب أن تبدأ ب\"10.\" و يجب أن يكون فيه \"/\"."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "الرابط القانوني: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "هذا الملف قد يكون في %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "ملفات مطابقة في قاعدة بياناتنا:"

#~ msgid "page.doi.results.none"
#~ msgstr "لا توجد ملفات مطابقة في قاعدة البيانات الخاصة بنا."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 التنزيلات السريعة</strong> لقد نفدت التنزيلات السريعة لهذا اليوم. يُرجى الاتصال بـآنا على %(email)s إذا كنت مهتمًا بترقية عضويتك."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "لقد نفدت التنزيلات السريعة اليوم. اتصل بـآنا على %(email)s إذا كنت مهتمًا بترقية عضويتك."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>هل يمكنني المساهمة بطرق أخرى؟</div> نعم! انظر في <a href=\"/about\">صفحة حول</a> تحت \"كيفية المساعدة\"."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>لا أحب أنك \"تحقق الدخل\" من أرشيف آنا!</div> إذا لم تعجبك الطريقة التي ندير بها مشروعنا، شغّل مكتبة الظل الخاصة بك! جميع التعليمات البرمجية والبيانات لدينا مفتوحة المصدر، لذلك لا شيء يمنعك. ;)"

#~ msgid "page.request.title"
#~ msgstr "طلب كتب"

#~ msgid "page.request.text1"
#~ msgstr "في الوقت الحالي ، هل يمكنك طلب كتب إلكترونية على <a %(a_forum)s> منتدى Libgen.rs </a>؟ يمكنك إنشاء حساب هناك والنشر في أحد هذه المواضيع:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s> بالنسبة إلى الكتب الإلكترونية ، استخدم <a %(a_ebook)s> هذا الموضوع </a>. </li> <li %(li_item)s> بالنسبة للكتب غير المتوفرة ككتب إلكترونية ، استخدم <%(a_regular)s a> هذا الموضوع </a>. </li>"

#~ msgid "page.request.text3"
#~ msgstr "في كلتا الحالتين ، تأكد من اتباع القواعد المذكورة في المواضيع."

#~ msgid "page.upload.title"
#~ msgstr "رفع"

#~ msgid "page.upload.libgen.header"
#~ msgstr "مكتبة التَّكوين (Library Genesis)"

#~ msgid "page.upload.zlib.header"
#~ msgstr "مكتبة الزّاي (Z-Library)"

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "عن الموقع"

#~ msgid "page.about.header"
#~ msgstr "عن الموقع"

#~ msgid "page.home.search.header"
#~ msgstr "بحث"

#~ msgid "page.home.search.intro"
#~ msgstr "بحث في كتالوج لدينا من مكتبات الظل."

#~ msgid "page.home.random_book.header"
#~ msgstr "كتاب عشوائي"

#~ msgid "page.home.random_book.intro"
#~ msgstr "اذهب إلى كتاب عشوائي من الكتالوج."

#~ msgid "page.home.random_book.submit"
#~ msgstr "كتاب عشوائي"

#~ msgid "page.about.text1"
#~ msgstr "أرشيف آنا هو محرك غير ربحي مفتوح المصدر للبحث في مكتبات الظل. تم إنشائه من قبل <a href=\"http://annas-blog.org\">آنا</a> مؤسسة <a href=\"https://en.wikipedia.org/wiki/Pirate_Library_Mirror\"> \"Pirate Library Mirror\" </a>النسخة الاحتياطية من مكتبة الظل Z-Library. شعرت آنا أن هناك حاجة لمكان مركزي واحد للبحث عن الكتب، الأوراق البحثية، القصص المصورة، المجلات، وغيرها من الوثائق."

#~ msgid "page.about.text4"
#~ msgstr "إذا كان لديك شكوى DMCA مقبولة انظر الي اسفل الصفحة او رسلنا علي %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "استكشف الكتب"

#~ msgid "page.home.explore.intro"
#~ msgstr "هذا مزيج من الكتب المشهورة والكتب التي تحمل أهمية خاصة في عالم مكتبات الظل والحفظ الرقمي."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat غير الرسمية"

#~ msgid "page.wechat.body"
#~ msgstr "لدينا صفحة WeChat غير رسمية، يديرها أحد أعضاء المجتمع. استخدم الرمز أدناه للوصول."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "حول"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "برنامج الهاتف"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "غير رسمي WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "طلب كتب"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "رفع"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "إحالة الأصدقاء"

#~ msgid "page.about.help.header"
#~ msgstr "طرق للمساعدة"

#~ msgid "page.refer.title"
#~ msgstr "قم بإحالة الأصدقاء للحصول على تنزيلات سريعة إضافية"

#~ msgid "page.refer.section1.intro"
#~ msgstr "يمكن للأعضاء إحالة الأصدقاء وكسب تنزيلات إضافية."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "لكل صديق يصبح عضوا:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>يحصلون</strong> على تنزيلات إضافية %(percentage)s بالإضافة إلى التنزيلات اليومية المنتظمة، طوال مدة عضويتهم."

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "رَبيدةُ الشّابكة (Internet Archive)"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "تبرع إجمالي بـ%(total)s من <a %(a_account)s> حساب علي للدفع (Alipay) هذا"

#~ msgid "page.upload.zlib.text"
#~ msgstr "أو ارفعهم لمكتبة الزّاي من <a %(a_upload)s>هنا</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "نحبث عن متطوعين لإدارة المرايا وإظهار ما في موقعنا حتّى نحمي ربيدة آنَّا ومنعهم من الإطاحة بها. <a href=\"/mirrors\">اعرف أكثر...</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "فقط هذا الشهر!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub قد <a %(a_closed)s>أوقف</a> تحميل الأوراق الجديدة."

#~ msgid "page.donate.payment.intro"
#~ msgstr "اختر خيارًا للدفع. عندك تخفيض إن اخترت الدفع بالعملات المعماة %(bitcoin_icon)s، وذلك لقلّة عمولتها."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "اختر خيارًا للدفع. لا خيارٌ الآن إلا بالعملات المعماة %(bitcoin_icon)s، وذلك لأن وسائل الدفع المعروفة يرفضوننا."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "لا يمكننا دعم بطاقات الائتمان/الخصم مباشرة، لأن البنوك لا تريد العمل معنا. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "ومع ذلك، هناك عدة طرق لاستخدام بطاقات الائتمان/الخصم على أي حال، باستخدام طرق الدفع الأخرى لدينا:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 تنزيلات بطيئة وخارجية"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "التنزيلات"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "إن كان هذا أول دفع لك بالعملات المعماة فننصح بـ%(option1)s، أو %(option2)s، أو%(option3)s لشراء البِتْكُوينْ والتّبرع بها (وهي أول عملة معماة وأكثرها استخدامًا)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 رابطًا للسجلات التي قمت بتحسينها."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 رابط أو لقطة شاشة."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 رابطًا أو لقطة شاشة للطلبات التي قمت بتلبيتها."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "إذا كنت مهتمًا بعكس هذه المجموعات من البيانات لأغراض <a %(a_faq)s>الأرشفة</a> أو <a %(a_llm)s>تدريب LLM</a>، يرجى الاتصال بنا."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "إذا كنت مهتمًا بمرآة هذه المجموعة لأغراض <a %(a_archival)s>الأرشفة</a> أو <a %(a_llm)s>تدريب LLM</a>، يرجى الاتصال بنا."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "الموقع الرئيسي"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "معلومات بلد ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "إذا كنت مهتمًا بعكس هذه المجموعة من البيانات لأغراض <a %(a_archival)s>الأرشفة</a> أو <a %(a_llm)s>تدريب LLM</a>، يرجى الاتصال بنا."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "تقوم الوكالة الدولية لـ ISBN بإصدار النطاقات التي خصصتها للوكالات الوطنية لـ ISBN بانتظام. من هذا يمكننا استنتاج البلد أو المنطقة أو المجموعة اللغوية التي ينتمي إليها هذا ISBN. نحن نستخدم هذه البيانات حاليًا بشكل غير مباشر، من خلال مكتبة Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "الموارد"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "آخر تحديث: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "موقع ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "البيانات الوصفية"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "باستثناء \"scimag\""

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "إلهامنا لجمع البيانات الوصفية هو هدف آرون شوارتز \"صفحة ويب واحدة لكل كتاب تم نشره على الإطلاق\"، والذي أنشأ من أجله <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "لقد نجح هذا المشروع، لكن موقعنا الفريد يسمح لنا بالحصول على البيانات الوصفية التي لا يمكنهم الحصول عليها."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "كان مصدر إلهام آخر هو رغبتنا في معرفة <a %(a_blog)s>عدد الكتب الموجودة في العالم</a>، حتى نتمكن من حساب عدد الكتب التي لا تزال بحاجة إلى إنقاذ."

#~ msgid "page.partner_download.text1"
#~ msgstr "من أجل إعطاء الجميع فرصة لتنزيل الملفات مجانًا، تحتاج إلى الانتظار <strong>%(wait_seconds)s ثانية</strong> قبل أن تتمكن من تنزيل هذا الملف."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "تحديث الصفحة تلقائيًا. إذا فاتتك نافذة التنزيل، فسيتم إعادة تشغيل المؤقت، لذا يُوصى بالتحديث التلقائي."

#~ msgid "page.partner_download.download_now"
#~ msgstr "التحميل الان"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "تحويل: استخدم الأدوات عبر الإنترنت للتحويل بين الصيغ. على سبيل المثال، لتحويل بين epub و pdf، استخدم <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: قم بتنزيل الملف (يدعم pdf أو epub)، ثم <a %(a_kindle)s>أرسله إلى Kindle</a> باستخدام الويب أو التطبيق أو البريد الإلكتروني. أدوات مفيدة: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "دعم المؤلفين: إن أعجبك هذا وعندك ثمنه، فاشترِ النسخة المباعة منهم، أو ادعمهم مباشرةً بطرق أخرى."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "دعم المكتبات: إن توفّر هذا في مكتبة بلدتك، فاستعره مجانًا منهم."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s غير متاحة مباشرة بكميات كبيرة، فقط بكميات شبه كبيرة خلف جدار دفع."

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s رَبيدةُ آنّا تدير مجموعة من <a %(isbndb)s>بيانات ISBNdb الوصفية</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb هي شركة تقوم بجمع البيانات الوصفية لـ ISBN من مختلف المكتبات الإلكترونية. رَبيدةُ آنّا تقوم بعمل نسخ احتياطية من بيانات الكتب الوصفية لـ ISBNdb. هذه البيانات الوصفية متاحة من خلال رَبيدةُ آنّا (ولكنها ليست متاحة حاليًا في البحث، إلا إذا قمت بالبحث عن رقم ISBN بشكل صريح)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "للتفاصيل التقنية، انظر أدناه. في مرحلة ما يمكننا استخدامها لتحديد الكتب التي لا تزال مفقودة من المكتبات الظلية، من أجل تحديد الأولويات للكتب التي يجب العثور عليها و/أو مسحها ضوئيًا."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "منشور مدونتنا حول هذه البيانات"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "جمع بيانات ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "حاليًا لدينا تورنت واحد يحتوي على ملف JSON Lines مضغوط بحجم 4.4 جيجابايت (20 جيجابايت غير مضغوط): \"isbndb_2022_09.jsonl.gz\". لاستيراد ملف \".jsonl\" إلى PostgreSQL، يمكنك استخدام شيء مثل <a %(a_script)s>هذا السكربت</a>. يمكنك حتى توجيهه مباشرة باستخدام شيء مثل %(example_code)s بحيث يتم فك الضغط أثناء الطيران."

#~ msgid "page.donate.wait"
#~ msgstr "رجاءً انتظر <span %(span_hours)s ساعتان</span> على الأقل -ثم حدّث الصفحة- قبل التواصل معنا."

#~ msgid "page.codes.search_archive"
#~ msgstr "ابحث في رَبيدةُ آنّا عن “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "تبرع بعلي للدفع (Alipay) أو وِتشاتْ (WeChat). تستطيع الاختيار بينهما في الصفحة التالية."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "نشر كلمة رَبيدةُ آنّا على وسائل التواصل الاجتماعي والمنتديات عبر الإنترنت، من خلال التوصية بكتاب أو قوائم على AA، أو الإجابة على الأسئلة."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s مجموعة الكتب الخيالية قد تباينت ولكن لا تزال تحتوي على <a %(libgenli)s>سيول</a>، رغم أنها لم تُحدث منذ عام 2022 (لدينا تنزيلات مباشرة)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s رَبيدةُ آنّا وLibgen.li يديران معًا مجموعات من <a %(comics)s>الكتب المصورة</a> و<a %(magazines)s>المجلات</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s لا توجد سيول لمجموعات الأدب الروسي والوثائق القياسية."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "لا توجد تورنتات متاحة للمحتوى الإضافي. التورنتات الموجودة على موقع Libgen.li هي عاكسات لتورنتات أخرى مدرجة هنا. الاستثناء الوحيد هو تورنتات الخيال التي تبدأ من %(fiction_starting_point)s. يتم إصدار تورنتات القصص المصورة والمجلات كجزء من تعاون بين رَبيدةُ آنّا وLibgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "من مجموعة <a %(a_href)s><q>مكتبة الإسكندرية،</q></a> الأصل الدقيق غير واضح. جزئيًا من the-eye.eu، وجزئيًا من مصادر أخرى."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "تليغرام"

