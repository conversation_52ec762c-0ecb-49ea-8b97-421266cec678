��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b 9  sd 3  �f +   �g   
h �  i �  k h  �l   4n L   Mo f   �o E   p `   Gp �  �p O  \r �   �s �  �t �   gv �  Iw �  y �  �z �   �| �  �} {  6 7   �� �   � L   �� �  �    ރ 6  �� E   3�    y�    ��    �� D   ��    �    "� &   2�    Y� #   t�    �� 9   �� ,  �   � B   %� 4   h�    �� 
   ��    �� 
   ͊    ۊ 
   �� 	   �    �    � "   &�    I�    O� 	   c� 
   m�    {�    �� $   ��    ŋ Z  ދ K  9� �   �� +   � U  ;� �   �� �   )� �   $�    � �   
�    �� �   �� !   j� $  ��    �� }  Ζ -   L� K   z�    Ƙ 8  Ϙ 8  � �  A�   �    � I   � Z   \� �   �� &   Z� 0   �� t   �� ;   '� H   c� 5   ��    � q   � �  Y� >   �� o   3� �   �� 7   U�   �� 3   ��   ť �   ʦ �   V� L   � �   <� �   �� E   �� �   � �   ɪ _   �� <   � s   � 
   �� �   ��   5� E   I� 
   �� �   �� �   3�    �� V  � �   ]� �   � i  � �   Z� ;  A�    }� C   ~� :   ¹ Z   �� i   X� ^   º V   !� 0   x� �   �� R   ,� :   � 
   �� f   ż V   ,� �   �� �   @�    ʾ �   ھ �   ̿ C  �� 4  � �   C� �   �� 1  �� �   �� �   �� �   � ^  �� �   � �   �� 
   �� �   �� �   7� �   � g  �� o   #� �   �� �    � �  �� t   y�   ��   � "   � O   +� �   {� C   	� k  M� �   �� a   m� s   ��    C�   _� �   e� g  +�    �� [   �� �   � �   �� �   ^� �   ^� �  5� �  �� $   �� N  �� q  �   s� u  �� (  �� �   '� S   � y   r�    �� �   �� 1  �� d  �� �   G� �   � �   � 	   �� I  �� J  �� �   A� 	   $� �   .� $  �� �   �� �  �� �   �� Y   �� 
   �� b  �� P  U�    �  �   �    �   � �   � V  � Y    I   [   � 4   �    � (  �   
   ) 5   F E   | �  � �   �    B ]   � o        �    � u  � �   F �    ?   � �   � ,   ` �   � �   �    t    � %   � 4   � 9   � 6   , H   c z   �    ' $   7 =   \ F   � *   � :    :   G `   �    �    �        �    �   � �   � �   x D  :     ! �   �! �   a" �   �" P  }# p  �$ �  ?& �   ( h  �) E   �* 3   D+ &   x+ �   �+ �  E, ~  . &  �/ 1  �0   �1 �  �2   �4 0   6   O6 ,  V7 Y  �8    �9 �   �9 )  �: �   �; 4  {< �   �=    �> *   �> _   �>    $? a  ,? �  �A H  (C �   qE �   F 
   �F �   �F �   �G E   �H s   �H   QI �   UJ y   DK {  �K O   :M   �M    �N �  �N �   �P �  1Q X  #S �  |U �   BW 
   �W �   �W �  �X    �Z `  �Z �  �[   �] �  �_ �   �a *  �b E   �c �  Bd �   �e x   �f 8   Ng U   �g    �g :   �g "   !h ,   Dh "   qh    �h �   �h �  Ii �  l $  n �   1p    
q �  q �  t U  v t  fw C  �x I  { 
   i} �   t}    o~ 
   x~ w  �~ 	  � �  � <  ڃ    � �  '� �  � �  �� �  v� �  T�   ;� B  P�    �� �   �� b   `� �  Ô w  �� �   
� }   �� �   *� R  Қ    %� �   :� ?   ޜ H   �    g� 8   |� 7   ��   � �  	� s  �� +   
� �  6� �   զ    k� �   r� .   
� x   9� �   �� $   k� \   ��    � M  �� *  K� �  v� W  �� �   U�    װ e   � 3  N�    �� v   �� �  �� �   �� 2   7� /   j� 3  �� +   η 2  �� x  -� �  ��    n� �   �� �   � *  �� x  ��    C� �   P� +   +� �   W� �   �� �   �� �   �� H   Y� +   �� �   �� 5   �� k  �� 3  \� �   �� �   [� \   V� 8   �� �   ��   �� [  �� �  �� �  �� 0   _� �   ��   a�   j� �   �� �  5� �  � +   �� �  �� "   �� �  �� T   �    �� �  �� �  �� y  �� �   W� �   ?� �  9� '  %� �   M� �  �� �  �� z   8� �  �� 7  �� :   �� v   � {   ��    ��    � .  � �   M� ;   ��   7� �  @�   � �  � �  �� �   �  �  D   �    � �       � o  � '   A    i    �    � "   �    �    �    �    �    �         
        + !   3    U '   ]    �    �    �    � �   � S   k    � 
   � "   � $   	 /   1	 ,   a	    �	    �	 
   �	    �	    �	    �	    �	    
    
    !
    '
 )   8
    b
    �
    �
 #   �
 +   �
 %   
    0 %   F 1   l    � J   � 8   �    3 V   8 D   �    �    � "   
    %
    5
    H
    _
    p
    �
    �
    �
    �
 
   �
 	   �
 
   �
    �
    �
        
 	         	   9    C    a    g 	   n    x    �    �    �    �    �    �    � 	   
     &   '    N 	   S !   ]        �    �    �    �    �    � e   � �   A O   � �   - �  � y   �        *    B    Q 
   X    c    {    � L   � 4   � ^   + %   � 1   � (   � c    ^   o �   � V   � 9       @    ]    j 	   p    z    �    �    � 
   �    �    �    �    �    �    �    �            (    9 
   >    I 	   R    \    x    �    �    �    �     �    � D   � Z    %   i &   � +   �    �    �    � f   �    \    b *   r j   �         {   ' $   �   � `   �  �   ?! �   �! �   W" ,   *# �   W# �   P$ )  �$ �    &    �&    ' D   ' H   [' P   �' [   �' X   Q( C   �( i   �( "   X) -   {)    �)    �) N   �) #   
*    1*    7*    I*    R* g   k*    �* 1   �* F   +    ]+    p+ S   �+ X   �+ h  9,    �-    �- �   �- 	   o.    y.    �.    �.    �. ,   �. E  �. !    0    B0 
   O0    ]0 /  i0    �1    �1    �1 }   �1    E2    K2 .   X2    �2    �2 %   �2 �   �2    P3 
   d3 E   r3    �3    �3    �3    �3 *   �3 M   &4 
   t4 ;   4 �   �4 K   X5 H   �5    �5 �   �5 A   �6    07 .   C7    r7 �   �7 �   >8    �8 D   �8 �   99 �   �9    �: !   �: 
   �: c  �: "   R<     u<    �<    �< "   �< �   �<    �=    �= )   �= :   �= 	    >    *>    H> #   d> :  �> j  �@ �  .B 3   "D 3   VD    �D    �D   �D �   �E �   �F    #G �   =G �  H    �I    �I �  �I '  �K    �L 
   �L 0   �L 	   %M   /M    KN )  `N �  �O �   $Q    R }   +R /   �R $   �R r   �R   qS   �T �   �U ~  JV r   �W �   <X P   -Y �   ~Y �   /Z L   �Z �   [ *   �[ +   �[    !\    1\    :\ !   K\    m\ M   �\ *   �\ 	   ] (   ]   5] %   B^ �   h^ �   F_ �   �_ /   P` %   �`    �`    �` "   �`    �` &   a (   :a �   ca    ?b �   _b    c �   &c w   �c �   hd 1  �d �    f *   �f �   g 	   �g �   �g ]   �h    �h �  �h    �j    �j    �j    �j )   �j    k    k K   !k �   mk �   l    �l    �l    �l �   m   �m �   �n m   �o    �o    	p     p    5p    Np    ^p    mp ?   up )   �p �  �p S   �r    �r o   �r o   cs N   �s t   "t K   �t P   �t    4u e   :u H   �u �   �u U   tv K   �v    w �   %w X   
x =   cx a   �x V   y 8   Zy    �y 4   �y n   �y �   @z 0   �z �   �z    �{   �{ >   �| ^   �| �   B}    �} �  �} �   �    k�    z�    ��    �� �   �� *   �� b   �� �   !� �   �� �   �� �   G� �   � �   �� �   3� Y   �   K� \   ^� O  �� �   � �   �� 
   �� 
   �� 
   �� �   ŋ 
   U� 
   c� D   q� P   �� �   � 
   Ǎ h   Ս 2   >� u   q� :   � f   "� Z   �� 
   � }   � 
   p� 
   ~� 
   �� �  �� �   �� �  �    ��    ��    Ɣ �   ֔    p�    �� �   �� �   ��    ;�    Z�    j� 9   �� <   �� 0   ��    .�    H� �   b�    =� �  [� �   �� �   ֛ I   a� �   �� 
  E� �  P�   � �   %� ~   �� u   5�    �� X  ��    � i  6� �   �� 
  �� {   ��    � �   <� �   � z   x� %   � 5   �    O� &   d� 
   �� 
   ��    �� �   ��    �� d   �� &   �    3�    9�    A�     F� n   g� E   ֮ 5   � �   R� ,   H�    u�    ~�    �� !   ��    ��    Ӱ    ߰    � 
   ��    �� 
   �    �    "� �   A�    ޱ    �    ��    �    �    +�    ;�    J�    Z�    t� %   �� e   ��    � 1   � \   N� �   �� �   W� �   �   �� �   ��   b� 
   � {   �� 3   	� F   =� L   �� �   ѹ �   j� =   1� `   o�    л e   � ]   I� �   ��    (�     /�    P�    `�    u�    ~�    ��     ��    ½    ˽    ޽    �    �    (�    >�    [�    b�    r�    �    ��    �� !   �� (   ̾ w   �� �   m� ,   �� &   "� b   I� Z   �� s   � '   {� 9   �� .   �� 8   � ;   E� )   �� �   ��   r�   y�    �� C   �� x   �� #   `� �   �� �   m� �   4� <   ��    �� �   � {   �� d   � *   �� P   �� �   ��    �� #   ��    � >   ,�    k� u   ��    a�    i�    r� "   y� �   �� 4   (� !   ]� 2   � %   ��    �� !   �� B   �    ]� a   x� ;   �� �   � K   � C   Z� �   �� >   R� #   ��     �� #   �� "   �� #   � "   A� #   d� 5   �� =   �� 5  �� E   2� 6   x� ?   �� $   ��    � _   � �   z� �   �� �   ��    �� �   �� #   $�    H� �   Y�    *�     ?� 5   `� -   �� R   �� @   � M   X�    ��     �� �   �� ,   w� !   �� P   �� �   � 5   � �   <� a   �� X   *� u   �� &   �� +    � r   L�    �� [   ��    7� �   T� 3   � C   8�    |� "   �� �   �� D   �� N   �� �   � V   �� M   �� ~   K� �   ��    R�    Y� K   u� @   ��    � (   �    @�    O�    e� -   v� �   �� k   2�    ��    ��    �� "   �� &   � {   ;� 5   �� |   �� R   j�     �� g   �� �   F� .   !� X   P�    �� D   �� ;   �    ?� u   S� s   �� +   =� W   i�    �� .   �� X   �    [� g   p� )   ��    � '   � R   @� �   �� B   d�    ��    �� B   ��    � S   /� ;   �� �   �� �   T�    �� C   �� �   *�    �� �   �� @   �� !   �� O   �� �   8�    %�    -�    /�    1� #   G� -   k� T   ��    �� 
   �    � #   � 8   ?� 8   x� 	   �� B   �� =   ��    <�    K� (   f�    ��    �� V   �� �   
� ?   ��    !� 
   -� �   ;� 2  �� L   �    g� I  w� k  �� .   - `   \    � /  � *   
 `   5    �    � �  �    � N   � q   � Y   _ V   �     d   % <   �    � ~   � C   d	 ,   �	 W   �	 +   -
 P   Y
 �   �
 �   8 ,   � �   � _  � �     (   � N  � �   , �    "  � �    ,   �    � �   � (   � �  � j   � @       G    S �  j     �    &  � &  � %   K   ? E   � R   � +   $ "   P Q   s W   �     "   * 2   M    �    � +   � Q   � *   5     `  e   h  �   �  �   �! 
   6" 
   D"    R" M   W" S   �" O   �" �   I# X   $    i$ &   z$ �   �$    d% �   m% �  & �   �( N   �) .   �)    '*    -*    2* A   6* 1   x* �   �* �   0+ N   �+    H,    [, %   n,    �, R   �,    �, >   -    O- )   U- *   -    �-    �- V   �-    .    . "   4.    W.    q. Z   u. �   �. E   �/ g   �/ L   @0 �   �0    B1    K1 �   d1 �   2 �   �2    �3 o   �3 D   4 H   V4 b   �4 l   5 P   o5    �5 \   �5    66    K6    `6    s6    �6    �6    �6    �6    �6 	   �6 *   �6 *   7 *   <7 "   g7 /   �7 (   �7    �7    8    8 A   ,8    n8    �8 :   �8 *   �8 X   �8 /   O9    9    �9 !   �9    �9    �9 k    : T   l: c   �: �   %;    �;    <    /<    J< .   `< 	   �<    �<    �<    �<     H= $   i=    �= 
   �= 	   �= 4   �=    �= �   �=    �>    ?    ?     0?    Q?    m?     �?    �? "   �? '   �? )   @    9@ S   @@ !   �@    �@    �@ /   �@ I   A    XA )   vA    �A f   �A Y   'B M   �B    �B    �B 	   �B    �B    C    (C �  BC s   �D    [E    pE +   tE    �E "   �E    �E 	   �E v   �E <   XF �   �F    ZG    lG    �G    H    ,H #   HH &   lH +   �H !   �H    �H    �H 6   I    =I     \I 9   }I    �I A   �J A   �J M   <K    �K �   �K    *L    HL T   TL    �L '   �L    �L A   �L A   :M     |M �   �M    4N    IN    VN    rN    �N    �N    �N    �N    �N K   �N �   GO    -P "   FP �   iP �   6Q    �Q    �Q    �Q    R    R    8R    KR    dR    zR    �R    �R    �R 
   �R    �R    �R    �R    �R    S    S �   6S �   �S o  �T   aV �  nX �   #Z i  [    k\ �   t\    p] �   �] �   m^ �  L_ �   �` A  �a 4   �b �   c =   �c    �c ;   d ?   Ld h   �d r   �d �   he �   $f �   �f >  �g    �i �   �i �   �j \   _k �   �k    pl �   �l l  ]m �   �n �   �o    Op c   \p `   �p �   !q �   r j   �r z   s    �s !   �s    �s D   �s '   t    ;t }   Kt ?   �t i   	u    su �   �u �   "v @   �v M   �v L   6w j   �w c   �w M   Rx g   �x a   y }   jy }   �y    fz )   lz *   �z U   �z 4   {    L{    T{ B   [{    �{    �{    �{ 9   �{ 2   | N   9|    �|    �|    �|    �| 	   �| \   �| r   &} T   �} ;   �}    *~    2~ )   P~ 
   z~    �~    �~    �~    �~    �~    �~    �~    �~    �~    �~ 
   �~    �~ 
   �~ 
   �~         	       )    0    7    S    n p   �    � Q   � �   e� 	   #�    -�    <�    C�    K�    Q�    U� �   \� w   �� =   v�    ��    Â {   Ԃ    P� �   `�    �    d�    �� |   �� �   � ^   � �   M� _   �� -   ^� �   ��    
�    #� E   :� �   ��    � �   -� }   ĉ �   B� Q   �    >�    R�    W�    f�    o�    �� 	   ��    �� �   �� \   >� �   �� �    � �   � j   ێ G   F� �  �� a  Y� �   �� �   ��   B� J  G�    �� �   ��   O� �   _� s  � �  |� }   =� F  ��    � =   � �   V� R  � E   9� �   �    k�    t�    |� �   �� 3   � l   Q� 2   �� ^   � F   P� H   �� $   � �   � L   �� ,   � K   � �   \�    2�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: nn
Language-Team: nn <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library er eit populært (og ulovleg) bibliotek. Dei har tatt Library Genesis-samlinga og gjort den lett søkbar. I tillegg har dei blitt svært effektive til å be om nye bokbidrag, ved å motivere bidragsytande brukarar med ulike fordelar. Dei bidreg for tida ikkje med desse nye bøkene tilbake til Library Genesis. Og i motsetning til Library Genesis, gjer dei ikkje samlinga si lett spegelbar, noko som hindrar brei bevaring. Dette er viktig for forretningsmodellen deira, sidan dei krev pengar for tilgang til samlinga deira i bulk (meir enn 10 bøker per dag). Vi gjer ikkje moralske vurderingar om å krevje pengar for bulktilgang til ei ulovleg boksamling. Det er utan tvil at Z-Library har vore suksessfulle i å utvide tilgangen til kunnskap, og skaffe fleire bøker. Vi er rett og slett her for å gjere vår del: sikre langtidsbevaring av denne private samlinga. - Anna og teamet (<a %(reddit)s>Reddit</a>) I den opprinnelege utgjevinga av Pirate Library Mirror (EDIT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), laga vi ein spegel av Z-Library, ei stor ulovleg boksamling. Som ei påminning, dette er kva vi skreiv i det opprinnelege blogginnlegget: Den samlinga daterte seg tilbake til midten av 2021. I mellomtida har Z-Library vokst i eit forbløffande tempo: dei har lagt til om lag 3,8 millionar nye bøker. Det er nokre duplikat der, sjølvsagt, men det meste ser ut til å vere genuint nye bøker, eller høgare kvalitetsskanningar av tidlegare innsendte bøker. Dette er i stor grad takka vere det auka talet på frivillige moderatorar ved Z-Library, og deira system for masseopplasting med deduplisering. Vi vil gratulere dei med desse prestasjonane. Vi er glade for å kunngjere at vi har fått alle bøkene som vart lagt til Z-Library mellom vår siste spegling og august 2022. Vi har også gått tilbake og henta nokre bøker vi gjekk glipp av første gongen. Alt i alt er denne nye samlinga om lag 24TB, som er mykje større enn den førre (7TB). Vår spegling er no 31TB totalt. Igjen, vi dedupliserte mot Library Genesis, sidan det allereie finst torrentar tilgjengelege for den samlinga. Ver venleg å gå til Pirate Library Mirror for å sjekke ut den nye samlinga (EDIT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Det er meir informasjon der om korleis filene er strukturerte, og kva som har endra seg sidan sist. Vi vil ikkje lenkje til det herfrå, sidan dette berre er ei bloggside som ikkje vert vert for ulovlege materialar. Sjølvsagt er det også ein flott måte å hjelpe oss på ved å dele. Takk til alle som deler våre tidlegare sett med torrentar. Vi er takksame for den positive responsen, og glade for at det er så mange som bryr seg om bevaring av kunnskap og kultur på denne uvanlege måten. 3x nye bøker lagt til i Pirate Library Mirror (+24TB, 3,8 millionar bøker) Les følgesartiklene av TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>andre</a> - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) fylgjeartiklar av TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>andre</a> Ikkje for lenge sidan var “skuggebibliotek” i ferd med å døy ut. Sci-Hub, det massive ulovlege arkivet av akademiske artiklar, hadde slutta å ta inn nye verk, grunna søksmål. “Z-Library”, det største ulovlege biblioteket av bøker, såg dei påståtte skaparane sine arresterte på grunn av kriminelle opphavsrettsanklager. Dei klarte utruleg nok å rømme frå arrestasjonen, men biblioteket deira er ikkje mindre truga. Noen land gjør allerede en versjon av dette. TorrentFreak <a %(torrentfreak)s>rapporterte</a> at Kina og Japan har innført AI-unntak i sine opphavsrettslover. Det er uklart for oss hvordan dette samhandler med internasjonale traktater, men det gir absolutt dekning til deres innenlandske selskaper, noe som forklarer hva vi har sett. Når det gjelder Annas Arkiv — vi vil fortsette vårt undergrunnsarbeid forankret i moralsk overbevisning. Likevel er vårt største ønske å komme inn i lyset og forsterke vår innvirkning lovlig. Vennligst reformer opphavsretten. Då Z-Library stod overfor nedstenging, hadde eg allereie sikkerheitskopiert heile biblioteket deira og leita etter ein plattform for å huse det. Det var motivasjonen min for å starte Annas Arkiv: ei vidareføring av misjonen bak dei tidlegare initiativa. Vi har sidan vakse til å bli det største skuggebiblioteket i verda, med over 140 millionar opphavsrettsbeskytta tekstar på tvers av mange format — bøker, akademiske artiklar, magasin, aviser og meir. Mitt team og eg er ideologar. Vi trur at det er moralsk rett å bevare og hoste desse filene. Bibliotek rundt om i verda ser budsjettkutt, og vi kan ikkje stole på at menneskeheitens arv blir tatt vare på av selskap heller. Så kom AI. Nesten alle store selskap som bygger LLM-ar kontakta oss for å trene på dataene våre. Dei fleste (men ikkje alle!) USA-baserte selskap ombestemte seg då dei innsåg den ulovlege naturen av arbeidet vårt. Derimot har kinesiske firma entusiastisk omfamna samlinga vår, tilsynelatande utan å bry seg om lovlegheita. Dette er merkverdig gitt Kinas rolle som signatar til nesten alle store internasjonale opphavsrettstraktatar. Vi har gitt høghastigheitstilgang til om lag 30 selskap. Dei fleste av dei er LLM-selskap, og nokre er datameklarar, som vil videreselje samlinga vår. Dei fleste er kinesiske, sjølv om vi også har jobba med selskap frå USA, Europa, Russland, Sør-Korea og Japan. DeepSeek <a %(arxiv)s>innrømma</a> at ein tidlegare versjon blei trena på ein del av samlinga vår, sjølv om dei er tause om den nyaste modellen sin (sannsynlegvis også trena på dataene våre). Om Vesten vil halde seg i front i kappløpet om LLM-ar, og til slutt, AGI, må dei revurdere posisjonen sin på opphavsrett, og snart. Om du er einig med oss eller ikkje i vår moralske sak, blir dette no ei sak om økonomi, og til og med om nasjonal tryggleik. Alle maktblokkar bygger kunstige supervitskapsfolk, superhackarar og supermilitærar. Informasjonsfridom blir eit spørsmål om overleving for desse landa — til og med eit spørsmål om nasjonal tryggleik. Vårt team er frå heile verda, og vi har ikkje ei bestemt tilknyting. Men vi vil oppmuntre land med sterke opphavsrettslover til å bruke denne eksistensielle trusselen til å reformere dei. Så kva skal ein gjere? Vår første anbefaling er enkel: forkort opphavsrettsperioden. I USA blir opphavsrett gitt for 70 år etter forfattarens død. Dette er absurd. Vi kan bringe dette i tråd med patentar, som blir gitt for 20 år etter innlevering. Dette bør vere meir enn nok tid for forfattarar av bøker, artiklar, musikk, kunst og andre kreative verk, til å bli fullt kompensert for innsatsen sin (inkludert lengre prosjekt som filmatiseringar). Så, som eit minimum, bør politikarar inkludere unntak for massebevaring og spreiing av tekstar. Om tapt inntekt frå individuelle kundar er hovudbekymringa, kan distribusjon på personleg nivå forbli forbode. Til gjengjeld vil dei som er i stand til å handtere store samlingar — selskap som trenar LLM-ar, saman med bibliotek og andre arkiv — vere dekka av desse unntaka. Opphavsrettsreform er nødvendig for nasjonal tryggleik TL;DR: Kinesiske LLM-ar (inkludert DeepSeek) er trena på mitt ulovlege arkiv av bøker og artiklar — det største i verda. Vesten må overhale opphavsrettslova som eit spørsmål om nasjonal tryggleik. Sjå den <a %(all_isbns)s>opprinnelege bloggposten</a> for meir informasjon. Vi utfordra folk til å forbetre dette. Vi ville gi ein førstepremie på $6,000, andreplass på $3,000, og tredjeplass på $1,000. På grunn av den overveldande responsen og dei utrulege bidraga, har vi bestemt oss for å auke premiepotten litt, og gi ein delt tredjeplass til fire bidrag på $500 kvar. Vinnarane er nedanfor, men hugs å sjå alle bidraga <a %(annas_archive)s>her</a>, eller last ned vår <a %(a_2025_01_isbn_visualization_files)s>kombinerte torrent</a>. Førsteplass $6,000: phiresky Denne <a %(phiresky_github)s>innsendinga</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) er rett og slett alt vi ønsket oss, og meir til! Vi likte spesielt dei utruleg fleksible visualiseringsalternativa (som til og med støttar tilpassa shaders), men med ei omfattande liste over forhåndsinnstillingar. Vi likte også kor raskt og smidig alt er, den enkle implementeringa (som ikkje ein gong har ein backend), den smarte minikartet, og den omfattande forklaringa i deira <a %(phiresky_github)s>blogginnlegg</a>. Utrolig arbeid, og ein velfortent vinnar! - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Vi er fulle av takksemd. Merkbare idear Skyskraparar for sjeldanheit Mange glidebrytarar for å samanlikne datasets, som om du er ein DJ. Skalastrek med tal på bøker. Fine etikettar. Kul standard fargeskjema og varmekart. Unik kartvisning og filter Merknader, og også live statistikk Live statistikk Nokre fleire idear og implementeringar vi spesielt likte: Vi kunne halde fram ei stund, men la oss stoppe her. Sørg for å sjå alle innsendingane <a %(annas_archive)s>her</a>, eller last ned vår <a %(a_2025_01_isbn_visualization_files)s>kombinerte torrent</a>. Så mange innsendingar, og kvar ein gir eit unikt perspektiv, anten i UI eller implementering. Vi vil i det minste inkorporere førsteplassinnsendinga på vår hovudnettstad, og kanskje nokre andre. Vi har også byrja å tenkje på korleis vi kan organisere prosessen med å identifisere, stadfeste og deretter arkivere dei sjeldnaste bøkene. Meir kjem på denne fronten. Takk til alle som deltok. Det er fantastisk at så mange bryr seg. Enkel veksling av datasets for raske samanlikningar. Alle ISBN-er CADAL SSNO-er CERLALC-datalekkasje DuXiu SSID-ar EBSCOhost sin eBook-indeks Google Bøker Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Filer i Annas Arkiv Nexus/STC OCLC/Worldcat OpenLibrary Russlands statsbibliotek Det keiserlege biblioteket i Trantor Andreplass $3,000: hypha “Sjølv om perfekte kvadratar og rektangel er matematisk tiltalande, gir dei ikkje overlegen lokalitet i ein kartleggingskontekst. Eg trur asymmetrien som er iboande i desse Hilbert eller klassiske Morton ikkje er ein feil, men ein eigenskap. Akkurat som Italias berømte støvleforma omriss gjer det umiddelbart gjenkjenneleg på eit kart, kan dei unike "særtrekka" til desse kurvene tene som kognitive landemerke. Denne distinktheita kan forbetre romleg minne og hjelpe brukarar med å orientere seg, potensielt gjere det enklare å lokalisere spesifikke område eller legge merke til mønster.” Enda ei utruleg <a %(annas_archive_note_2913)s>innsending</a>. Ikkje like fleksibel som førsteplassen, men vi føretrekte faktisk makronivå-visualiseringa over førsteplassen (space-filling curve, grenser, merking, utheving, panorering og zooming). Ein <a %(annas_archive_note_2971)s>kommentar</a> av Joe Davis resonerte med oss: Og framleis mange alternativ for visualisering og rendering, samt ein utruleg smidig og intuitiv brukargrensesnitt. Ein solid andreplass! - Anna og teamet (<a %(reddit)s>Reddit</a>) For noen måneder siden annonserte vi en <a %(all_isbns)s>$10 000 premie</a> for å lage den best mulige visualiseringen av våre data som viser ISBN-området. Vi la vekt på å vise hvilke filer vi har/ikke har arkivert allerede, og vi la senere til et datasett som beskriver hvor mange biblioteker som har ISBN-er (et mål på sjeldenhet). Vi har blitt overveldet av responsen. Det har vært så mye kreativitet. En stor takk til alle som har deltatt: deres energi og entusiasme er smittsom! Til slutt ønsket vi å svare på følgende spørsmål: <strong>hvilke bøker finnes i verden, hvor mange har vi allerede arkivert, og hvilke bøker bør vi fokusere på neste?</strong> Det er flott å se at så mange bryr seg om disse spørsmålene. Vi startet med en grunnleggende visualisering selv. På mindre enn 300kb representerer dette bildet kortfattet den største fullt åpne "listen over bøker" noensinne samlet i menneskehetens historie: Tredjeplass $500 #1: maxlion I denne <a %(annas_archive_note_2940)s>innsendinga</a> likte vi verkeleg dei ulike typane visningar, spesielt samanliknings- og forlagsvisningane. Tredjeplass $500 #2: abetusk Sjølv om ikkje det mest polerte brukargrensesnittet, sjekkar denne <a %(annas_archive_note_2917)s>innsendinga</a> mange av boksane. Vi likte spesielt samanlikningsfunksjonen. Tredjeplass $500 #3: conundrumer0 Som førsteplassen, imponerte denne <a %(annas_archive_note_2975)s>innsendinga</a> oss med fleksibiliteten sin. Til sjuande og sist er det dette som gjer eit flott visualiseringsverktøy: maksimal fleksibilitet for avanserte brukarar, medan det held ting enkelt for gjennomsnittlege brukarar. Tredjeplass $500 #4: charelf Den siste <a %(annas_archive_note_2947)s>innsendinga</a> for å få ein premie er ganske enkel, men har nokre unike trekk som vi likte veldig godt. Vi likte korleis dei viser kor mange datasets som dekker ein bestemt ISBN som eit mål på popularitet/pålitelegheit. Vi likte også veldig godt enkelheita, men effektiviteten av å bruke ein opasitetsglidebrytar for samanlikningar. Vinnere av $10 000 ISBN-visualiseringspremien TL;DR: Vi fikk noen utrolige bidrag til $10 000 ISBN-visualiseringspremien. Bakgrunn Korleis kan Annas Arkiv oppnå sitt mål om å sikkerhetskopiere all menneskehetens kunnskap, utan å vite kva bøker som framleis finst der ute? Vi treng ei TODO-liste. Ein måte å kartlegge dette på er gjennom ISBN-nummer, som sidan 1970-talet har blitt tildelt kvar bok som er publisert (i dei fleste land). Det finst ingen sentral myndighet som kjenner til alle ISBN-tildelingar. I staden er det eit distribuert system, der land får tildelt nummerområde, som deretter tildeler mindre område til store forlag, som kanskje vidare underdeler område til mindre forlag. Til slutt blir individuelle nummer tildelt bøker. Vi starta kartlegginga av ISBN-ar <a %(blog)s>for to år sidan</a> med vår scraping av ISBNdb. Sidan då har vi skrapa mange fleire metadata-kjelder, som <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, og meir. Ei fullstendig liste kan finnast på sidene “Datasets” og “Torrents” på Annas Arkiv. Vi har no den desidert største fullt opne, lett nedlastbare samlinga av bokmetadata (og dermed ISBN-ar) i verda. Vi har <a %(blog)s>skrive omfattande</a> om kvifor vi bryr oss om bevaring, og kvifor vi for tida er i eit kritisk vindu. Vi må no identifisere sjeldne, underfokuserte og unikt risikoutsette bøker og bevare dei. Å ha god metadata på alle bøker i verda hjelper med det. $10,000 premie Sterk vurdering vil bli gitt til brukervennlighet og hvor bra det ser ut. Vis faktisk metadata for individuelle ISBN-er når du zoomer inn, som tittel og forfatter. Bedre plassfyllingskurve. F.eks. en sikksakk, som går fra 0 til 4 på første rad og deretter tilbake (i revers) fra 5 til 9 på andre rad — anvendt rekursivt. Ulike eller tilpassbare fargeskjemaer. Spesielle visninger for å sammenligne Datasets. Måter å feilsøke problemer, som annen metadata som ikke stemmer godt overens (f.eks. svært forskjellige titler). Annotere bilder med kommentarer på ISBN-er eller områder. Eventuelle heuristikker for å identifisere sjeldne eller truede bøker. Hvilke som helst kreative ideer du kan komme opp med! Kode Koden for å generere disse bildene, samt andre eksempler, kan finnes i <a %(annas_archive)s>denne katalogen</a>. Vi kom opp med eit kompakt dataformat, der all nødvendig ISBN-informasjon er om lag 75MB (komprimert). Skildringa av dataformatet og koden for å generere det kan finnast <a %(annas_archive_l1244_1319)s>her</a>. For premien er du ikkje påkravd å bruke dette, men det er truleg det mest praktiske formatet å starte med. Du kan transformere vår metadata slik du vil (men all koden din må vere open source). Vi kan ikkje vente med å sjå kva du kjem opp med. Lykke til! Fork dette repoet, og rediger denne bloggpostens HTML (ingen andre backends enn vår Flask-backend er tillatt). Gjer bildet ovanfor smidig zoom-bart, slik at du kan zoome heilt inn til individuelle ISBN-ar. Å klikke på ISBN-ar skal ta deg til ei metadata-side eller søk på Annas Arkiv. Du må framleis kunne bytte mellom alle ulike datasets. Landområde og forlagsområde bør bli utheva ved hover. Du kan bruke f.eks. <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> for landinfo, og vår “isbngrp” scraping for forlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Det må fungere godt på både stasjonær og mobil. Det er mykje å utforske her, så vi annonserer ein premie for å forbetre visualiseringa ovanfor. I motsetning til dei fleste av våre premiar, er denne tidsavgrensa. Du må <a %(annas_archive)s>sende inn</a> din open source-kode innan 2025-01-31 (23:59 UTC). Den beste innsendinga vil få $6,000, andreplass er $3,000, og tredjeplass er $1,000. Alle premiar vil bli utdelt ved bruk av Monero (XMR). Nedanfor er dei minimale kriteriene. Om ingen innsending møter kriteriene, kan vi framleis tildele nokre premiar, men det vil vere etter vårt skjønn. For ekstrapoeng (dette er bare ideer — la kreativiteten din løpe løpsk): Du KAN fullstendig avvike fra de minimale kriteriene, og lage en helt annen visualisering. Hvis det er virkelig spektakulært, kvalifiserer det for belønningen, men etter vårt skjønn. Gjør innleveringer ved å poste en kommentar til <a %(annas_archive)s>denne saken</a> med en lenke til din forgreinede repo, sammenslåingsforespørsel eller diff. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dette biletet er 1000×800 pikslar. Kvar piksel representerer 2,500 ISBN-ar. Om vi har ei fil for ein ISBN, gjer vi den pikselen meir grøn. Om vi veit ein ISBN er utstedt, men vi ikkje har ei matchande fil, gjer vi den meir raud. På mindre enn 300kb representerer dette bildet den største fullstendig åpne «boklisten» som noen gang er samlet i menneskehetens historie (noen hundre GB komprimert i sin helhet). Det viser også: det er mykje arbeid igjen med å sikkerhetskopiere bøker (vi har berre 16%%). Visualisere alle ISBN-ar — $10,000 premie innan 2025-01-31 Dette biletet representerer den største fullt opne "lista over bøker" som nokon gong er samla i menneskehistoria. Visualisering I tillegg til oversiktsbildet, kan vi også sjå på individuelle datasets vi har skaffa. Bruk nedtrekksmenyen og knappane for å bytte mellom dei. Det er mange interessante mønster å sjå i desse bilda. Kvifor er det noko regelmessigheit av linjer og blokker, som ser ut til å skje på ulike skalaer? Kva er dei tomme områda? Kvifor er visse datasets så samla? Vi lar desse spørsmåla stå som ei øving for lesaren. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklusjon Med denne standarden kan vi gjere utgjevingar meir gradvis, og lettare leggje til nye datakjelder. Vi har allereie nokre spennande utgjevingar på gang! Vi håpar også at det blir enklare for andre skyggebibliotek å spegle samlingane våre. Tross alt er målet vårt å bevare menneskeleg kunnskap og kultur for alltid, så jo meir redundans, jo betre. Eksempel La oss se på vår nylige Z-Library-utgivelse som et eksempel. Den består av to samlinger: «<span style="background: #fffaa3">zlib3_records</span>» og «<span style="background: #ffd6fe">zlib3_files</span>». Dette lar oss skrape og utgi metadata-poster separat fra de faktiske bokfilene. Som sådan utga vi to torrenter med metadatafiler: Vi utga også en rekke torrenter med binære datafolder, men kun for «<span style="background: #ffd6fe">zlib3_files</span>»-samlingen, totalt 62: Ved å kjøre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se hva som er inni: I dette tilfellet er det metadata av en bok som rapportert av Z-Library. På toppnivå har vi kun «aacid» og «metadata», men ingen «data_folder», siden det ikke er noen tilsvarende binære data. AACID inneholder «22430000» som primær-ID, som vi kan se er hentet fra «zlibrary_id». Vi kan forvente at andre AAC-er i denne samlingen har samme struktur. La oss nå kjøre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Dette er ein mykje mindre AAC-metadata, sjølv om hovuddelen av denne AAC er plassert ein annan stad i ei binær fil! Tross alt har vi ein “data_folder” denne gongen, så vi kan forvente at den tilsvarande binære dataen er plassert på <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” inneheld “zlibrary_id”, så vi kan enkelt knytte det til den tilsvarande AAC i “zlib_records”-samlinga. Vi kunne ha knytt det saman på fleire ulike måtar, til dømes gjennom AACID — standarden føreskriv ikkje det. Merk at det heller ikkje er nødvendig for “metadata”-feltet å vere JSON. Det kan vere ein streng som inneheld XML eller eit anna dataformat. Du kan til og med lagre metadata-informasjon i den tilknytte binære bloben, til dømes om det er mykje data. Heterogene filer og metadata, så nært originalformatet som mulig. Binære data kan serveres direkte av webservere som Nginx. Heterogene identifikatorer i kildesamlingene, eller til og med mangel på identifikatorer. Separate utgivelser av metadata vs fildata, eller kun metadata-utgivelser (f.eks. vår ISBNdb-utgivelse). Distribusjon gjennom torrenter, men med mulighet for andre distribusjonsmetoder (f.eks. IPFS). Uforanderlige oppføringer, siden vi bør anta at våre torrenter vil leve for alltid. Inkrementelle utgivelser / utvidbare utgivelser. Maskinlesbare og skrivbare, praktisk og raskt, spesielt for vår stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Noe enkel menneskelig inspeksjon, selv om dette er sekundært til maskinlesbarhet. Enkel å så våre samlinger med en standard leid seedbox. Designmål Vi bryr oss ikke om at filer er enkle å navigere manuelt på disk, eller søkbare uten forbehandling. Vi bryr oss ikke om å være direkte kompatible med eksisterende bibliotekprogramvare. Selv om det skal være enkelt for hvem som helst å så vår samling ved hjelp av torrenter, forventer vi ikke at filene skal være brukbare uten betydelig teknisk kunnskap og engasjement. Vårt primære bruksområde er distribusjon av filer og tilhørende metadata fra ulike eksisterende samlinger. Våre viktigste hensyn er: Noen ikke-mål: Siden Anna sitt Arkiv er åpen kildekode, ønsker vi å bruke vårt format direkte. Når vi oppdaterer vår søkeindeks, har vi kun tilgang til offentlig tilgjengelige stier, slik at alle som forgrener vårt bibliotek kan komme raskt i gang. <strong>AAC.</strong> AAC (Anna sitt Arkiv Container) er en enkelt enhet bestående av <strong>metadata</strong>, og eventuelt <strong>binære data</strong>, som begge er uforanderlige. Den har en globalt unik identifikator, kalt <strong>AACID</strong>. <strong>AACID.</strong> Formatet på AACID er slik: <code style="color: #0093ff">aacid__{samling}__{ISO 8601 tidsstempel}__{samling-spesifikk ID}__{shortuuid}</code>. Til dømes, ein faktisk AACID vi har sleppt er <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-område.</strong> Siden AACID-er inneholder monotont økende tidsstempler, kan vi bruke det til å angi områder innenfor en bestemt samling. Vi bruker dette formatet: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, der tidsstemplene er inkludert. Dette er i samsvar med ISO 8601-notasjon. Områder er kontinuerlige og kan overlappe, men i tilfelle overlapping må de inneholde identiske poster som den som tidligere ble utgitt i den samlingen (siden AAC-er er uforanderlige). Manglende poster er ikke tillatt. <code>{samling}</code>: namnet på samlinga, som kan innehalde ASCII-bokstavar, tal og understrekar (men ikkje doble understrekar). <code>{samling-spesifikk ID}</code>: ein samling-spesifikk identifikator, om aktuelt, til dømes Z-Library ID. Kan utelatast eller forkortast. Må utelatast eller forkortast om AACID elles ville overstige 150 teikn. <code>{ISO 8601 tidsstempel}</code>: ein kort versjon av ISO 8601, alltid i UTC, til dømes <code>20220723T194746Z</code>. Dette talet må auke monotont for kvar utgjeving, sjølv om den eksakte semantikken kan variere per samling. Vi foreslår å bruke tidspunktet for skraping eller generering av ID-en. <code>{shortuuid}</code>: ein UUID men komprimert til ASCII, til dømes ved å bruke base57. Vi bruker for tida <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket. <strong>Binær datafolder.</strong> En mappe med de binære dataene til et område av AAC-er, for en bestemt samling. Disse har følgende egenskaper: Mappen må inneholde datafiler for alle AAC-er innenfor det spesifiserte området. Hver datafil må ha sin AACID som filnavn (ingen utvidelser). Mappenavnet må være et AACID-område, prefikset med <code style="color: green">annas_archive_data__</code>, og ingen suffiks. For eksempel, en av våre faktiske utgivelser har en mappe kalt<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Det anbefales å gjøre disse mappene noe håndterbare i størrelse, f.eks. ikke større enn 100GB-1TB hver, selv om denne anbefalingen kan endres over tid. <strong>Samling.</strong> Kvar AAC høyrer til ei samling, som per definisjon er ei liste over AAC-ar som er semantisk konsistente. Det betyr at om du gjer ei vesentleg endring i formatet på metadataa, må du opprette ei ny samling. Standarden <strong>Metadatafil.</strong> En metadatafil inneholder metadataene til et område av AAC-er, for en bestemt samling. Disse har følgende egenskaper: <code>data_folder</code> er valgfritt, og er navnet på den binære datafolderen som inneholder de tilsvarende binære dataene. Filnavnet til de tilsvarende binære dataene i den mappen er postens AACID. Hvert JSON-objekt må inneholde følgende felt på toppnivå: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valgfritt). Ingen andre felt er tillatt. Filnavnet må være et AACID-område, prefikset med <code style="color: red">annas_archive_meta__</code> og etterfulgt av <code>.jsonl.zstd</code>. For eksempel, en av våre utgivelser heter<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Som indikert av filtypen, er filtypen <a %(jsonlines)s>JSON Lines</a> komprimert med <a %(zstd)s>Zstandard</a>. <code>metadata</code> er vilkårlige metadata, i henhold til semantikken i samlingen. Det må være semantisk konsistent innenfor samlingen. Prefikset <code style="color: red">annas_archive_meta__</code> kan tilpasses navnet på din institusjon, f.eks. <code style="color: red">my_institute_meta__</code>. <strong>“postar” og “filer” samlingar.</strong> Etter konvensjon er det ofte praktisk å sleppe “postar” og “filer” som forskjellige samlingar, slik at dei kan sleppast på ulike tidspunkt, til dømes basert på skrapefrekvensar. Ein “post” er ei samling som berre inneheld metadata, med informasjon som boktitlar, forfattarar, ISBN-ar, osv., medan “filer” er samlingane som inneheld sjølve filene (pdf, epub). Til slutt landet vi på en relativt enkel standard. Den er ganske løs, ikke-normativ, og et arbeid under utvikling. <strong>Torrenter.</strong> Metadatafilene og de binære datafolderne kan pakkes i torrenter, med én torrent per metadatafil, eller én torrent per binær datafolder. Torrentene må ha det originale fil-/mappenavnet pluss et <code>.torrent</code> suffiks som deres filnavn. <a %(wikipedia_annas_archive)s>Annas Arkiv</a> har blitt det desidert største skuggebiblioteket i verda, og det einaste skuggebiblioteket av sin skala som er fullstendig open-source og open-data. Nedanfor er ein tabell frå vår Datasets-side (litt modifisert): Vi oppnådde dette på tre måtar: Spegle eksisterande open-data skuggebibliotek (som Sci-Hub og Library Genesis). Hjelpe skuggebibliotek som ønskjer å vere meir opne, men ikkje hadde tid eller ressursar til å gjere det (som Libgen teikneseriesamlinga). Skrape bibliotek som ikkje ønskjer å dele i bulk (som Z-Library). For (2) og (3) administrerer vi no ein betydeleg samling av torrentar sjølve (100-tals TB). Så langt har vi nærma oss desse samlingane som enkeltståande, noko som betyr skreddarsydd infrastruktur og dataorganisering for kvar samling. Dette legg til betydeleg overhead til kvar utgjeving, og gjer det spesielt vanskeleg å gjere meir inkrementelle utgjevingar. Det er derfor vi bestemte oss for å standardisere våre utgjevingar. Dette er eit teknisk blogginnlegg der vi introduserer vår standard: <strong>Annas Arkiv Behaldarar</strong>. Annas Arkiv Behaldarar (AAC): standardisering av utgjevingar frå verdas største skuggebibliotek Annas Arkiv har blitt det største skuggebiblioteket i verda, noko som krev at vi standardiserer våre utgjevingar. 300GB+ med bokomslag utgitt Endelig er vi glade for å kunngjøre en liten utgivelse. I samarbeid med folkene som driver Libgen.rs-forgreiningen, deler vi alle deres bokomslag gjennom torrenter og IPFS. Dette vil fordele belastningen av å se omslagene blant flere maskiner, og vil bevare dem bedre. I mange (men ikke alle) tilfeller er bokomslagene inkludert i filene selv, så dette er en slags “avledet data”. Men å ha det i IPFS er fortsatt veldig nyttig for daglig drift av både Annas Arkiv og de ulike Library Genesis-forgreiningene. Som vanleg kan du finne denne utgjevinga på Pirate Library Mirror (REDIGERT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi vil ikkje lenkje til det her, men du kan lett finne det. Forhåpentlegvis kan vi slappe av litt no, sidan vi har eit anstendig alternativ til Z-Library. Denne arbeidsmengda er ikkje spesielt berekraftig. Om du er interessert i å hjelpe til med programmering, serverdrift eller bevaringsarbeid, ta gjerne kontakt med oss. Det er framleis mykje <a %(annas_archive)s>arbeid å gjere</a>. Takk for interessa og støtta. Bytte til ElasticSearch Nokre søk tok veldig lang tid, til det punktet der dei ville oppta alle opne tilkoplingar. Som standard har MySQL ei minimumsordlengd, eller indeksen din kan bli veldig stor. Folk rapporterte at dei ikkje kunne søke etter “Ben Hur”. Søket var berre noko raskt når det var fullt lasta i minnet, noko som kravde at vi fekk ein dyrare maskin for å køyre dette på, pluss nokre kommandoar for å førlaste indeksen ved oppstart. Vi ville ikke ha vært i stand til å utvide det enkelt for å bygge nye funksjoner, som bedre <a %(wikipedia_cjk_characters)s>tokenisering for språk uten mellomrom</a>, filtrering/fasettering, sortering, "mente du" forslag, autoutfylling, og så videre. Eit av våre <a %(annas_archive)s>billettar</a> var ei samling av problem med søkesystemet vårt. Vi brukte MySQL fulltekst-søk, sidan vi hadde alle dataene våre i MySQL uansett. Men det hadde sine avgrensingar: Etter å ha snakket med en rekke eksperter, bestemte vi oss for ElasticSearch. Det har ikke vært perfekt (deres standard “mente du” forslag og autoutfyllingsfunksjoner er dårlige), men totalt sett har det vært mye bedre enn MySQL for søk. Vi er fortsatt ikke <a %(youtube)s>for ivrige</a> på å bruke det for noen kritiske data (selv om de har gjort mye <a %(elastic_co)s>fremgang</a>), men totalt sett er vi ganske fornøyde med byttet. For nå har vi implementert mye raskere søk, bedre språkundestøttelse, bedre relevanssortering, forskjellige sorteringsalternativer, og filtrering på språk/boktype/filtype. Hvis du er nysgjerrig på hvordan det fungerer, <a %(annas_archive_l140)s>ta</a> <a %(annas_archive_l1115)s>en</a> <a %(annas_archive_l1635)s>titt</a>. Det er ganske tilgjengelig, selv om det kunne trengt noen flere kommentarer… Annas Arkiv er fullt open kjeldekode Vi trur at informasjon skal vere fri, og vår eigen kode er ikkje noko unntak. Vi har frigitt all koden vår på vår privat hosta Gitlab-instans: <a %(annas_archive)s>Annas Programvare</a>. Vi bruker også problemsporaren for å organisere arbeidet vårt. Om du vil engasjere deg i utviklinga vår, er dette ein flott stad å starte. For å gi deg ein smakebit på kva vi jobbar med, ta ein titt på vårt nylege arbeid med ytelsesforbetringar på klientsida. Sidan vi ikkje har implementert paginering enno, ville vi ofte returnere veldig lange søkesider, med 100-200 resultat. Vi ville ikkje kutte av søkeresultata for tidleg, men dette betydde at det ville sakke ned nokre einingar. For dette implementerte vi eit lite triks: vi pakka dei fleste søkeresultata inn i HTML-kommentarar (<code><!-- --></code>), og skreiv deretter ein liten Javascript som ville oppdage når eit resultat skulle bli synleg, på det tidspunktet ville vi pakke ut kommentaren: DOM "virtualisering" implementert i 23 linjer, ingen behov for fancy bibliotek! Dette er den typen rask pragmatisk kode du endar opp med når du har avgrensa tid, og reelle problem som må løysast. Det har blitt rapportert at søket vårt no fungerer godt på trege einingar! Ein annan stor innsats var å automatisere bygginga av databasen. Då vi lanserte, trekte vi berre tilfeldig saman ulike kjelder. No vil vi halde dei oppdaterte, så vi skreiv ein haug med skript for å laste ned ny metadata frå dei to Library Genesis-greinene, og integrerer dei. Målet er ikkje berre å gjere dette nyttig for vårt arkiv, men å gjere det enkelt for alle som vil leike seg med skyggebibliotek-metadata. Målet ville vere ein Jupyter-notatbok som har alle slags interessante metadata tilgjengeleg, slik at vi kan gjere meir forsking som å finne ut kva <a %(blog)s>prosent av ISBN-ar er bevart for alltid</a>. Til slutt har vi fornya donasjonssystemet vårt. Du kan no bruke eit kredittkort for å direkte setje inn pengar i våre kryptovaluta-lommebøker, utan å verkeleg trenge å vite noko om kryptovaluta. Vi vil halde fram med å overvake kor godt dette fungerer i praksis, men dette er ein stor sak. Med Z-Library nede og deira (påståtte) grunnleggjarar arresterte, har vi jobba døgnet rundt for å tilby eit godt alternativ med Annas Arkiv (vi vil ikkje lenkje til det her, men du kan Google det). Her er nokre av tinga vi har oppnådd nyleg. Annas Oppdatering: fullt open kjeldekode-arkiv, ElasticSearch, 300GB+ med bokomslag Vi har jobba døgnet rundt for å tilby eit godt alternativ med Annas Arkiv. Her er nokre av tinga vi har oppnådd nyleg. Analyse Semantiske duplikater (forskjellige skanninger av samme bok) kan teoretisk filtreres ut, men det er vanskelig. Når vi manuelt så gjennom tegneseriene fant vi for mange falske positiver. Det er nokre duplikat berre basert på MD5, som er relativt sløsande, men å filtrere dei ut ville berre gje oss om lag 1%% i sparing. På denne skalaen er det framleis om lag 1TB, men også, på denne skalaen betyr ikkje 1TB så mykje. Vi vil heller ikkje risikere å øydeleggje data i denne prosessen. Vi fann ein haug med ikkje-bokdata, som filmar basert på teikneseriar. Det verkar også sløsande, sidan desse allereie er vidt tilgjengelege gjennom andre middel. Men vi innsåg at vi ikkje berre kunne filtrere ut filmfiler, sidan det også finst <em>interaktive teikneseriar</em> som vart sleppt på datamaskin, som nokon spelte inn og lagra som filmar. Til slutt, alt vi kunne slette frå samlinga ville berre spare nokre få prosent. Så hugsa vi at vi er datahamstrarar, og dei som vil spegle dette er også datahamstrarar, og så, “KVA MEINER DU, SLETTE?!” :) Når du får 95TB dumpet inn i lagringsklyngen din, prøver du å forstå hva som egentlig er der… Vi gjorde noen analyser for å se om vi kunne redusere størrelsen litt, for eksempel ved å fjerne duplikater. Her er noen av funnene våre: Vi presenterer derfor for dykk den fullstendige, uendra samlinga. Det er mykje data, men vi håpar at nok folk vil bry seg om å dele det vidare. Samarbeid Gitt størrelsen har denne samlingen lenge vært på vår ønskeliste, så etter vår suksess med å sikkerhetskopiere Z-Library, satte vi blikket på denne samlingen. Først skrapet vi den direkte, noe som var en utfordring, siden serveren deres ikke var i beste stand. Vi fikk omtrent 15TB på denne måten, men det gikk sakte. Heldigvis klarte vi å komme i kontakt med operatøren av biblioteket, som gikk med på å sende oss alle dataene direkte, noe som gikk mye raskere. Det tok fortsatt mer enn et halvt år å overføre og behandle alle dataene, og vi holdt på å miste alt på grunn av disk-korrupsjon, noe som ville betydd å starte helt på nytt. Denne opplevelsen har fått oss til å tro at det er viktig å få disse dataene ut så raskt som mulig, slik at de kan speiles vidt og bredt. Vi er bare en eller to uheldige hendelser unna å miste denne samlingen for alltid! Samlingen Å bevege seg raskt betyr at samlingen er litt uorganisert… La oss ta en titt. Tenk deg at vi har et filsystem (som i virkeligheten deler vi opp i torrents): Den første katalogen, <code>/repository</code>, er den mer strukturerte delen av dette. Denne katalogen inneholder såkalte "tusen dirs": kataloger hver med tusen filer, som er inkrementelt nummerert i databasen. Katalogen <code>0</code> inneholder filer med comic_id 0–999, og så videre. Dette er det samme opplegget som Library Genesis har brukt for sine skjønnlitterære og faglitterære samlinger. Ideen er at hver "tusen dir" automatisk blir gjort om til en torrent så snart den er fylt opp. Imidlertid laget aldri Libgen.li-operatøren torrents for denne samlingen, og derfor ble tusen dirs sannsynligvis upraktiske, og ga vei til "usorterte dirs". Disse er <code>/comics0</code> gjennom <code>/comics4</code>. De inneholder alle unike katalogstrukturer, som sannsynligvis ga mening for å samle filene, men som ikke gir så mye mening for oss nå. Heldigvis refererer metadataene fortsatt direkte til alle disse filene, så deres lagringsorganisering på disk spiller ingen rolle! Metadataene er tilgjengelige i form av en MySQL-database. Denne kan lastes ned direkte fra Libgen.li-nettstedet, men vi vil også gjøre den tilgjengelig i en torrent, sammen med vår egen tabell med alle MD5-hashene. <q>Dr. Barbara Gordon prøver å miste seg selv i bibliotekets hverdagslige verden…</q> Libgen-forker Først litt bakgrunn. Du kjenner kanskje Library Genesis for deres episke boksamling. Færre vet at Library Genesis-frivillige har opprettet andre prosjekter, som en betydelig samling av magasiner og standarddokumenter, en full backup av Sci-Hub (i samarbeid med grunnleggeren av Sci-Hub, Alexandra Elbakyan), og faktisk en massiv samling av tegneserier. På et tidspunkt gikk forskjellige operatører av Library Genesis-speil hver sin vei, noe som førte til den nåværende situasjonen med å ha en rekke forskjellige "forker", alle fortsatt med navnet Library Genesis. Libgen.li-forken har unikt denne tegneseriesamlingen, samt en betydelig samling av magasiner (som vi også jobber med). Innsamlingsaksjon Vi slepp denne dataen i nokre store bolkar. Den første torrenten er av <code>/comics0</code>, som vi har lagt i ei stor 12TB .tar-fil. Det er betre for harddisken din og torrentprogramvaren enn ein haug med mindre filer. Som ein del av denne utgjevinga, gjennomfører vi ein innsamlingsaksjon. Vi ønskjer å samle inn 20 000 dollar for å dekke drifts- og kontraktskostnader for denne samlinga, samt mogleggjere pågåande og framtidige prosjekt. Vi har nokre <em>massive</em> prosjekt på gang. <em>Kven støttar eg med donasjonen min?</em> Kort sagt: vi sikkerheitskopierer all kunnskap og kultur i menneskeheita, og gjer det lett tilgjengeleg. All koden og dataen vår er open kjeldekode, vi er eit fullstendig frivillig prosjekt, og vi har redda 125TB med bøker så langt (i tillegg til dei eksisterande torrentane til Libgen og Scihub). Til slutt byggjer vi eit svinghjul som mogleggjer og motiverer folk til å finne, skanne og sikkerheitskopiere alle bøkene i verda. Vi vil skrive om vår hovudplan i eit framtidig innlegg. :) Om du donerer for eit 12 månaders “Amazing Archivist”-medlemskap ($780), får du <strong>“adoptere ein torrent”</strong>, som betyr at vi vil setje brukarnamnet ditt eller meldinga di i filnamnet til ein av torrentane! Du kan donere ved å gå til <a %(wikipedia_annas_archive)s>Annas Arkiv</a> og klikke på “Doner”-knappen. Vi ser også etter fleire frivillige: programvareingeniørar, sikkerheitsforskarar, anonyme handels-ekspertar og omsetjarar. Du kan også støtte oss ved å tilby hostingtenester. Og sjølvsagt, ver venleg å dele torrentane våre! Takk til alle som allereie har støtta oss så generøst! De gjer verkeleg ein forskjell. Her er torrentane som er sleppt så langt (vi behandlar framleis resten): Alle torrentane kan finnast på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under “Datasets” (vi lenkjer ikkje direkte dit, slik at lenkjer til denne bloggen ikkje blir fjerna frå Reddit, Twitter, osv.). Derifrå kan du følgje lenkja til Tor-nettstaden. <a %(news_ycombinator)s>Diskuter på Hacker News</a> Kva er det neste? Ein haug med torrentar er flotte for langtidsbevaring, men ikkje så mykje for dagleg tilgang. Vi vil samarbeide med hostingpartnarar for å få all denne dataen opp på nettet (sidan Annas Arkiv ikkje hostar noko direkte). Sjølvsagt vil du kunne finne desse nedlastingslenkjene på Annas Arkiv. Vi inviterer også alle til å gjere noko med denne dataen! Hjelp oss med å analysere den betre, fjerne duplikat, leggje den på IPFS, remikse den, trene AI-modellane dine med den, og så vidare. Det er alt ditt, og vi kan ikkje vente med å sjå kva du gjer med det. Til slutt, som sagt før, har vi framleis nokre massive utgjevingar på gang (om <em>nokon</em> kunne <em>tilfeldigvis</em> sende oss ein dump av ein <em>spesiell</em> ACS4-database, veit du kvar du finn oss…), samt byggje svinghjulet for å sikkerheitskopiere alle bøkene i verda. Så hald deg oppdatert, vi har berre så vidt starta. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Den største skyggebiblioteket av tegneserier er sannsynligvis en bestemt Library Genesis-fork: Libgen.li. Den ene administratoren som driver det nettstedet klarte å samle en vanvittig samling av tegneserier på over 2 millioner filer, som totalt utgjør over 95TB. Men i motsetning til andre Library Genesis-samlinger, var denne ikke tilgjengelig i bulk gjennom torrents. Du kunne bare få tilgang til disse tegneseriene individuelt gjennom hans trege personlige server — et enkelt feilpunkt. Inntil i dag! I dette innlegget vil vi fortelle deg mer om denne samlingen, og om vår innsamlingsaksjon for å støtte mer av dette arbeidet. Annas Arkiv har sikkerheitskopiert verdas største skuggebibliotek for teikneseriar (95TB) — du kan hjelpe til med å så det Verdas største skuggebibliotek for teikneseriar hadde eit enkelt feilpunkt.. fram til i dag. Advarsel: dette blogginnlegget er foreldet. Vi har bestemt at IPFS ennå ikke er klar for prime time. Vi vil fortsatt lenke til filer på IPFS fra Annas Arkiv når det er mulig, men vi vil ikke lenger være vert for det selv, og vi anbefaler heller ikke andre å speile ved hjelp av IPFS. Vennligst se vår Torrents-side hvis du vil hjelpe med å bevare vår samling. Legger 5 998 794 bøker på IPFS Ei mangfaldiggjering av kopiar Tilbake til det opprinnelege spørsmålet vårt: korleis kan vi hevde å bevare samlingane våre i all æve? Hovudproblemet her er at samlinga vår har <a %(torrents_stats)s>vakse</a> raskt, ved å skrape og open-source nokre massive samlingar (på toppen av det fantastiske arbeidet som allereie er gjort av andre open-data skuggebibliotek som Sci-Hub og Library Genesis). Denne veksten i data gjer det vanskelegare for samlingane å bli spegla rundt om i verda. Datastorage er dyrt! Men vi er optimistiske, spesielt når vi observerer dei følgjande tre trendane. Den <a %(annas_archive_stats)s>totale storleiken</a> på samlingane våre, dei siste månadene, brote ned etter talet på torrent-seedarar. HDD-pristrendar frå ulike kjelder (klikk for å sjå studien). <a %(critical_window_chinese)s>Kinesisk versjon 中文版</a>, diskuter på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Vi har plukka dei lågt hengande fruktene Denne følgjer direkte frå prioriteringane våre diskutert ovanfor. Vi føretrekk å arbeide med å frigjere store samlingar først. No som vi har sikra nokre av dei største samlingane i verda, forventar vi at veksten vår vil vere mykje saktare. Det er framleis ein lang hale av mindre samlingar, og nye bøker blir skanna eller publiserte kvar dag, men tempoet vil truleg vere mykje lågare. Vi kan framleis doble eller til og med triple i storleik, men over ein lengre tidsperiode. Forbetringar i OCR. Prioriteringar Vitskapleg og teknisk programvarekode Fiktive eller underhaldningsversjonar av alt ovanfor Geografiske data (f.eks. kart, geologiske undersøkingar) Intern data frå selskap eller regjeringar (lekkasjar) Måledata som vitskaplege målingar, økonomiske data, bedriftsrapportar Metadataoppføringar generelt (av sakprosa og skjønnlitteratur; av andre media, kunst, personar, osv.; inkludert omtalar) Sakprosa bøker Sakprosa magasin, aviser, handbøker Sakprosa transkripsjonar av foredrag, dokumentarar, podkastar Organiske data som DNA-sekvensar, plantefrø eller mikrobielle prøver Akademiske artiklar, tidsskrift, rapportar Vitskaplege og tekniske nettsider, nettbaserte diskusjonar Transkripsjonar av juridiske eller rettslege forhandlingar Unikt i fare for øydelegging (t.d. av krig, budsjettkutt, søksmål eller politisk forfølging) Sjeldne Unikt underfokuserte Kvifor bryr vi oss så mykje om artiklar og bøker? La oss setje til side vår grunnleggjande tru på bevaring generelt — vi kan skrive eit anna innlegg om det. Så kvifor artiklar og bøker spesifikt? Svaret er enkelt: <strong>informasjonstettleik</strong>. Per megabyte med lagring, lagrar skriftleg tekst mest informasjon av alle media. Sjølv om vi bryr oss om både kunnskap og kultur, bryr vi oss meir om det første. Totalt sett finn vi ei rangering av informasjonstettleik og viktigheita av bevaring som ser omtrent slik ut: Rangeringa i denne lista er noko vilkårleg — fleire element er likestilte eller har usemje innanfor teamet vårt — og vi gløymer sannsynlegvis nokre viktige kategoriar. Men dette er omtrent korleis vi prioriterer. Nokre av desse elementa er for ulike frå dei andre til at vi treng å bekymre oss (eller er allereie tatt hand om av andre institusjonar), som organisk data eller geografisk data. Men dei fleste av elementa på denne lista er faktisk viktige for oss. Ein annan stor faktor i vår prioritering er kor mykje risiko eit bestemt verk står overfor. Vi føretrekk å fokusere på verk som er: Til slutt bryr vi oss om skala. Vi har avgrensa tid og pengar, så vi vil heller bruke ein månad på å redde 10 000 bøker enn 1 000 bøker — dersom dei er om lag like verdifulle og i fare. <em><q>Det tapte kan ikkje gjenopprettast; men la oss redde det som er igjen: ikkje ved hvelv og låsar som stenger dei frå offentleg auge og bruk, ved å overlate dei til tidens sløsing, men ved ei slik mangfaldiggjering av kopiar, som skal plassere dei utanfor rekkevidda av uhell.</q></em><br>— Thomas Jefferson, 1791 Skyggebibliotek Kode kan vere open kjelde på Github, men Github som heilskap kan ikkje lett speglast og dermed bevarast (sjølv om det i dette tilfellet finst tilstrekkeleg distribuerte kopiar av dei fleste koderepositorium) Metadataoppføringar kan fritt sjåast på Worldcat-nettstaden, men ikkje lastast ned i bulk (før vi <a %(worldcat_scrape)s>skrapa</a> dei) Reddit er gratis å bruke, men har nyleg innført strenge anti-skrapetiltak, i kjølvatnet av datahungrige LLM-treningar (meir om det seinare) Det er mange organisasjonar som har liknande oppdrag, og liknande prioriteringar. Faktisk finst det bibliotek, arkiv, laboratorium, museum og andre institusjonar som har ansvar for bevaring av denne typen. Mange av desse er godt finansierte, av regjeringar, enkeltpersonar eller selskap. Men dei har eit stort blindpunkt: rettssystemet. Her ligg den unike rolla til skyggebibliotek, og grunnen til at Anna sitt Arkiv eksisterer. Vi kan gjere ting som andre institusjonar ikkje har lov til å gjere. No er det ikkje (ofte) slik at vi kan arkivere materiale som er ulovleg å bevare andre stader. Nei, det er lovleg mange stader å bygge eit arkiv med kva som helst bøker, artiklar, magasin, og så vidare. Men det som ofte manglar i lovlege arkiv er <strong>redundans og varigheit</strong>. Det finst bøker der berre éin kopi eksisterer i eit fysisk bibliotek ein stad. Det finst metadataoppføringar vakta av eit enkelt selskap. Det finst aviser berre bevart på mikrofilm i eit enkelt arkiv. Bibliotek kan få kutt i finansieringa, selskap kan gå konkurs, arkiv kan bli bomba og brent til grunnen. Dette er ikkje hypotetisk — det skjer heile tida. Det vi kan gjere unikt i Anna sitt Arkiv er å lagre mange kopiar av verk, i stor skala. Vi kan samle artiklar, bøker, magasin og meir, og distribuere dei i bulk. Vi gjer dette for tida gjennom torrentar, men dei nøyaktige teknologiane spelar ingen rolle og vil endre seg over tid. Det viktige er å få mange kopiar distribuert over heile verda. Dette sitatet frå over 200 år sidan er framleis sant: Ei rask merknad om offentleg domene. Sidan Anna sitt Arkiv unikt fokuserer på aktivitetar som er ulovlege mange stader i verda, bryr vi oss ikkje om allment tilgjengelege samlingar, som bøker i offentleg domene. Lovlege einingar tek ofte allereie godt vare på det. Men det finst omsyn som gjer at vi av og til arbeider med offentleg tilgjengelege samlingar: - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Lagringskostnader fortset å falle eksponentielt 3. Forbetringar i informasjonsdensitet Vi lagrar for tida bøker i dei rå formata dei blir gitt til oss. Sjølvsagt er dei komprimerte, men ofte er dei framleis store skanningar eller fotografi av sider. Fram til no har dei einaste alternativa for å krympe den totale storleiken på samlinga vår vore gjennom meir aggressiv komprimering, eller deduplisering. Men for å få store nok besparingar, er begge for tapande for vår smak. Tung komprimering av bilete kan gjere tekst knapt lesbar. Og deduplisering krev høg tillit til at bøker er nøyaktig dei same, noko som ofte er for unøyaktig, spesielt om innhaldet er det same, men skanningane er gjort på ulike tidspunkt. Det har alltid vore eit tredje alternativ, men kvaliteten har vore så elendig at vi aldri vurderte det: <strong>OCR, eller optisk teikngjenkjenning</strong>. Dette er prosessen med å konvertere bilete til rein tekst, ved å bruke AI for å oppdage teikna i bileta. Verktøy for dette har lenge eksistert, og har vore ganske bra, men "ganske bra" er ikkje nok for bevaringsformål. Men nylege multimodale djupnelæringsmodellar har gjort ekstremt raske framsteg, sjølv om dei framleis er kostbare. Vi forventar at både nøyaktigheit og kostnader vil forbetre seg dramatisk i dei kommande åra, til det punktet der det vil bli realistisk å bruke på heile biblioteket vårt. Når det skjer, vil vi truleg framleis bevare dei originale filene, men i tillegg kan vi ha ein mykje mindre versjon av biblioteket vårt som dei fleste vil ønske å spegle. Det fine er at råtekst sjølv komprimerer endå betre, og er mykje enklare å deduplisere, noko som gir oss endå meir besparing. Samla sett er det ikkje urealistisk å forvente minst ein 5-10x reduksjon i total filstorleik, kanskje endå meir. Sjølv med ein konservativ 5x reduksjon, vil vi sjå på <strong>$1,000–$3,000 om 10 år sjølv om biblioteket vårt triplar i storleik</strong>. På tidspunktet for skrivinga er <a %(diskprices)s>diskprisar</a> per TB rundt $12 for nye diskar, $8 for brukte diskar, og $4 for tape. Om vi er konservative og berre ser på nye diskar, betyr det at lagring av ein petabyte kostar om lag $12,000. Om vi antar at biblioteket vårt vil triple frå 900TB til 2.7PB, vil det bety $32,400 for å spegle heile biblioteket vårt. Legg til straum, kostnad for anna maskinvare, og så vidare, la oss runde det opp til $40,000. Eller med tape meir som $15,000–$20,000. På den eine sida er <strong>$15,000–$40,000 for summen av all menneskeleg kunnskap eit kupp</strong>. På den andre sida er det litt bratt å forvente mange fullstendige kopiar, spesielt om vi også vil at desse personane skal halde fram med å så sine torrentar til nytte for andre. Det er i dag. Men framgangen marsjerer framover: Kostnadene for harddiskar per TB har blitt omtrent tredelt dei siste 10 åra, og vil truleg fortsetje å falle i eit liknande tempo. Tape ser ut til å vere på ein liknande bane. SSD-prisar fell endå raskare, og kan ta over HDD-prisar innan slutten av tiåret. Om dette held, kan vi om 10 år sjå på berre $5,000–$13,000 for å spegle heile samlinga vår (1/3), eller endå mindre om vi veks mindre i storleik. Sjølv om det framleis er mykje pengar, vil dette vere oppnåeleg for mange menneske. Og det kan bli endå betre på grunn av det neste punktet… I Anna sitt Arkiv blir vi ofte spurde korleis vi kan hevde å bevare samlingane våre i all æve, når den totale storleiken allereie nærmar seg 1 Petabyte (1000 TB), og framleis veks. I denne artikkelen vil vi sjå på filosofien vår, og kvifor det neste tiåret er kritisk for vårt oppdrag om å bevare menneskeheita sin kunnskap og kultur. Kritisk vindauge Om desse prognosane er nøyaktige, treng vi <strong>berre å vente eit par år</strong> før heile samlinga vår vil bli vidt spegla. Dermed, i Thomas Jeffersons ord, "plassert utanfor rekkevidda av uhell." Dessverre har framveksten av LLM-ar, og deira datahungrige trening, sett mange opphavsrettshavarar på defensiven. Endå meir enn dei allereie var. Mange nettsider gjer det vanskelegare å skrape og arkivere, søksmål flyg rundt, og samstundes blir fysiske bibliotek og arkiv framleis neglisjert. Vi kan berre forvente at desse trendane vil fortsetje å forverre seg, og mange verk vil gå tapt lenge før dei kjem inn i det offentlege domenet. <strong>Vi er på terskelen til ein revolusjon i bevaring, men <q>det tapte kan ikkje gjenopprettast.</q></strong> Vi har eit kritisk vindauge på om lag 5-10 år der det framleis er ganske dyrt å drive eit skyggebibliotek og lage mange speglar rundt om i verda, og der tilgangen ikkje er heilt stengt enno. Om vi kan bygge bru over dette vindauget, vil vi verkeleg ha bevart menneskeheita si kunnskap og kultur for alltid. Vi bør ikkje la denne tida gå til spille. Vi bør ikkje la dette kritiske vindauget lukke seg for oss. La oss gå. Det kritiske vindauget for skuggebibliotek Korleis kan vi hevde å bevare samlingane våre i all æve, når dei allereie nærmar seg 1 PB? Samling Nokre informasjon om samlinga. <a %(duxiu)s>Duxiu</a> er ein massiv database med skanna bøker, skapt av <a %(chaoxing)s>SuperStar Digital Library Group</a>. Dei fleste er akademiske bøker, skanna for å gjere dei tilgjengelege digitalt for universitet og bibliotek. For vårt engelsktalande publikum har <a %(library_princeton)s>Princeton</a> og <a %(guides_lib_uw)s>University of Washington</a> gode oversikter. Det finst også ein utmerka artikkel som gir meir bakgrunn: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (søk den opp i Annas Arkiv). Bøkene frå Duxiu har lenge blitt piratkopiert på det kinesiske internettet. Vanlegvis blir dei selde for mindre enn ein dollar av vidareforhandlarar. Dei blir typisk distribuert ved hjelp av den kinesiske ekvivalenten til Google Drive, som ofte har blitt hacka for å tillate meir lagringsplass. Nokre tekniske detaljar kan finnast <a %(github_duty_machine)s>her</a> og <a %(github_821_github_io)s>her</a>. Sjølv om bøkene har blitt semi-offentleg distribuert, er det ganske vanskeleg å få tak i dei i bulk. Vi hadde dette høgt på TODO-lista vår, og sette av fleire månader med fulltidsarbeid for det. Men nyleg tok ein utruleg, fantastisk og talentfull frivillig kontakt med oss, og fortalde at dei allereie hadde gjort alt dette arbeidet — til stor kostnad. Dei delte heile samlinga med oss, utan å forvente noko i retur, bortsett frå garantien om langtidsbevaring. Verkeleg bemerkelsesverdig. Dei gjekk med på å be om hjelp på denne måten for å få samlinga OCR-behandla. Samlinga er 7 543 702 filer. Dette er meir enn Library Genesis sakprosa (om lag 5,3 millionar). Total filstorleik er om lag 359TB (326TiB) i sin noverande form. Vi er opne for andre forslag og idear. Ta berre kontakt med oss. Sjekk ut Annas Arkiv for meir informasjon om samlingane våre, bevaringsinnsatsen, og korleis du kan hjelpe. Takk! Eksempelsider For å bevise for oss at du har ein god pipeline, her er nokre eksempel på sider å starte med, frå ei bok om superleiarar. Din pipeline bør handtere matematikk, tabellar, diagram, fotnotar, og så vidare, på ein skikkeleg måte. Send dei prosesserte sidene dine til e-posten vår. Om dei ser bra ut, vil vi sende deg meir privat, og vi forventar at du raskt kan køyre pipelinen din på dei også. Når vi er fornøgde, kan vi inngå ein avtale. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kinesisk versjon 中文版</a>, <a %(news_ycombinator)s>Diskuter på Hacker News</a> Dette er eit kort blogginnlegg. Vi ser etter eit selskap eller institusjon som kan hjelpe oss med OCR og tekstekstraksjon for ei massiv samling vi har skaffa, i byte mot eksklusiv tidleg tilgang. Etter embargo-perioden vil vi sjølvsagt sleppe heile samlinga. Høgkvalitets akademisk tekst er ekstremt nyttig for trening av LLM-ar. Sjølv om samlinga vår er kinesisk, bør dette vere nyttig for trening av engelske LLM-ar: modellar ser ut til å koda konsept og kunnskap uavhengig av kjeldespråk. For dette må tekst bli henta ut frå skanna. Kva får Annas Arkiv ut av det? Fulltekstsøk i bøkene for brukarane sine. Fordi måla våre samsvarer med dei til LLM-utviklarar, ser vi etter ein samarbeidspartnar. Vi er villige til å gi deg <strong>eksklusiv tidleg tilgang til denne samlinga i bulk i 1 år</strong>, om du kan utføre skikkeleg OCR og tekstekstraksjon. Om du er villig til å dele heile koden til rørleidninga di med oss, vil vi vere villige til å forlenge embargoen for samlinga. Eksklusiv tilgang for LLM-selskap til verdas største kinesiske sakprosasamling <em><strong>TL;DR:</strong> Annas Arkiv har skaffa ei unik samling av 7,5 millionar / 350TB kinesiske sakprosabøker — større enn Library Genesis. Vi er villige til å gi eit LLM-selskap eksklusiv tilgang, i byte mot høgkvalitets OCR og tekstekstraksjon.</em> Systemarkitektur Så la oss seie at du har funne nokre selskap som er villige til å vere vert for nettsida di utan å stenge deg ned — la oss kalle desse "fridomselskande tilbydarar" 😄. Du vil raskt oppdage at det er ganske dyrt å vere vert for alt hos dei, så du kan ønskje å finne nokre "billige tilbydarar" og gjere den faktiske hosting der, med proxy gjennom dei fridomselskande tilbydarane. Om du gjer det rett, vil dei billige tilbydarane aldri vite kva du er vert for, og aldri motta nokon klager. Med alle desse tilbydarane er det ein risiko for at dei stenger deg ned uansett, så du treng også redundans. Vi treng dette på alle nivå i vår stabel. Eit noko fridomselskande selskap som har plassert seg i ei interessant posisjon er Cloudflare. Dei har <a %(blog_cloudflare)s>argumentert</a> for at dei ikkje er ein hostingtilbydar, men ein nytte, som ein ISP. Dei er derfor ikkje underlagt DMCA eller andre nedtaksforespurnader, og sender vidare alle forespurnader til din faktiske hostingtilbydar. Dei har gått så langt som å gå til retten for å beskytte denne strukturen. Vi kan derfor bruke dei som eit anna lag av caching og beskyttelse. Cloudflare aksepterer ikkje anonyme betalingar, så vi kan berre bruke deira gratisplan. Dette betyr at vi ikkje kan bruke deira lastbalansering eller failover-funksjonar. Vi har derfor <a %(annas_archive_l255)s>implementert dette sjølv</a> på domenenivå. Ved sidelasting vil nettlesaren sjekke om det gjeldande domenet framleis er tilgjengeleg, og om ikkje, omskriv det alle URL-ar til eit anna domene. Sidan Cloudflare cacher mange sider, betyr dette at ein brukar kan lande på vårt hovuddomen, sjølv om proxy-serveren er nede, og deretter ved neste klikk bli flytta over til eit anna domene. Vi har framleis også vanlege operasjonelle bekymringar å handtere, som å overvake serverhelse, loggføre backend- og frontend-feil, og så vidare. Vår failover-arkitektur tillèt meir robustheit på denne fronten også, for eksempel ved å køyre eit heilt anna sett med serverar på eit av domena. Vi kan til og med køyre eldre versjonar av koden og datasetta på dette separate domenet, i tilfelle ein kritisk feil i hovudversjonen går uoppdaga. Vi kan også sikre oss mot at Cloudflare vender seg mot oss, ved å fjerne det frå eit av domena, som dette separate domenet. Ulike permutasjonar av desse ideane er mogleg. Konklusjon Det har vore ei interessant oppleving å lære korleis ein set opp ein robust og motstandsdyktig skuggebibliotek-søkemotor. Det er mange fleire detaljar å dele i seinare innlegg, så gi meg beskjed om kva du vil lære meir om! Som alltid ser vi etter donasjonar for å støtte dette arbeidet, så ver sikker på å sjekke ut Doner-sida på Anna sitt Arkiv. Vi ser også etter andre typar støtte, som tilskot, langsiktige sponsorar, høgrisikobetalingstilbydarar, kanskje til og med (smakfulle!) annonser. Og om du vil bidra med tid og ferdigheiter, ser vi alltid etter utviklarar, omsetjarar, og så vidare. Takk for interessa og støtta di. Innovasjonspoeng La oss starte med vår teknologiske stabel. Den er med vilje kjedeleg. Vi bruker Flask, MariaDB og ElasticSearch. Det er bokstaveleg talt alt. Søking er stort sett eit løyst problem, og vi har ikkje tenkt å oppfinne det på nytt. Dessutan må vi bruke våre <a %(mcfunley)s>innovasjonspoeng</a> på noko anna: å ikkje bli tatt ned av styresmaktene. Så kor lovleg eller ulovleg er eigentleg Anna sitt Arkiv? Dette avheng mest av den juridiske jurisdiksjonen. Dei fleste land trur på ein eller annan form for opphavsrett, som betyr at personar eller selskap får tildelt eit eksklusivt monopol på visse typar verk i ein viss periode. Som ein digresjon, i Anna sitt Arkiv trur vi at sjølv om det er nokre fordelar, er opphavsrett totalt sett eit nettonegativt for samfunnet — men det er ei historie for ein annan gong. Dette eksklusive monopolet på visse verk betyr at det er ulovleg for nokon utanfor dette monopolet å direkte distribuere desse verka — inkludert oss. Men Anna sitt Arkiv er ein søkemotor som ikkje direkte distribuerer desse verka (i alle fall ikkje på vår clearnet-nettside), så vi burde vere i orden, ikkje sant? Ikkje heilt. I mange jurisdiksjonar er det ikkje berre ulovleg å distribuere opphavsrettsbeskytta verk, men også å lenke til stader som gjer det. Eit klassisk døme på dette er den amerikanske DMCA-lova. Det er den strengaste enden av spekteret. På den andre enden av spekteret kan det teoretisk sett vere land utan opphavsrettslovar i det heile, men desse finst eigentleg ikkje. Nesten alle land har ein eller annan form for opphavsrettslov på bøkene. Håndheving er ei anna historie. Det er mange land med regjeringar som ikkje bryr seg om å håndheve opphavsrettslova. Det er også land mellom dei to ytterpunkta, som forbyr distribusjon av opphavsrettsbeskytta verk, men ikkje forbyr lenking til slike verk. Ein annan vurdering er på selskapsnivå. Om eit selskap opererer i ein jurisdiksjon som ikkje bryr seg om opphavsrett, men selskapet sjølv ikkje er villig til å ta nokon risiko, kan dei stenge ned nettsida di så snart nokon klagar på den. Til slutt er betalingar ei stor vurdering. Sidan vi må halde oss anonyme, kan vi ikkje bruke tradisjonelle betalingsmetodar. Dette etterlet oss med kryptovaluta, og berre eit lite utval av selskap støttar desse (det finst virtuelle debetkort betalt med krypto, men dei blir ofte ikkje akseptert). - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Eg driv <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdas største open-kjelde ikkje-kommersielle søkemotor for <a %(wikipedia_shadow_library)s>skyggebibliotek</a>, som Sci-Hub, Library Genesis, og Z-Library. Målet vårt er å gjere kunnskap og kultur lett tilgjengeleg, og til slutt byggje eit fellesskap av menneske som saman arkiverer og bevarer <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle bøkene i verda</a>. I denne artikkelen vil eg vise korleis vi driv denne nettsida, og dei unike utfordringane som følgjer med å drive ei nettside med tvilsam juridisk status, sidan det ikkje finst noko "AWS for skuggeveldedigheiter". <em>Sjekk også ut søsterartikkelen <a %(blog_how_to_become_a_pirate_archivist)s>Korleis bli ein piratarkivar</a>.</em> Korleis drive eit skyggebibliotek: drift ved Annas Arkiv Det finst ikkje <q>AWS for skyggeveldedigheiter,</q> så korleis driv vi Annas Arkiv? Verktøy Applikasjonsserver: Flask, MariaDB, ElasticSearch, Docker. Utvikling: Gitlab, Weblate, Zulip. Serveradministrasjon: Ansible, Checkmk, UFW. Onion statisk hosting: Tor, Nginx. Proxy-server: Varnish. La oss sjå på kva verktøy vi bruker for å oppnå alt dette. Dette utviklar seg veldig mykje etter kvart som vi møter nye problem og finn nye løysingar. Det er nokre avgjerder vi har gått att og fram på. Ein av dei er kommunikasjonen mellom serverar: vi brukte Wireguard til dette, men fann ut at det av og til stoppar å overføre data, eller berre overfører data i éin retning. Dette skjedde med fleire ulike Wireguard-oppsett som vi prøvde, som <a %(github_costela_wesher)s>wesher</a> og <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi prøvde også å tunnelere portar over SSH, ved å bruke autossh og sshuttle, men støyte på <a %(github_sshuttle)s>problem der</a> (sjølv om det framleis ikkje er klart for meg om autossh lid av TCP-over-TCP-problem eller ikkje — det kjennest berre som ei klønete løysing for meg, men kanskje det faktisk er greitt?). I staden gjekk vi tilbake til direkte tilkoplingar mellom serverar, og skjulte at ein server køyrer på dei billegare leverandørane ved å bruke IP-filtrering med UFW. Dette har ulempa at Docker ikkje fungerer godt med UFW, med mindre du bruker <code>network_mode: "host"</code>. Alt dette er litt meir feilutsett, fordi du vil eksponere serveren din for internett med berre ei lita feilkonfigurasjon. Kanskje vi burde gå tilbake til autossh — tilbakemeldingar ville vere svært velkomne her. Vi har også gått att og fram på Varnish vs. Nginx. Vi likar for tida Varnish, men det har sine særtrekk og ujamne kantar. Det same gjeld for Checkmk: vi elskar det ikkje, men det fungerer for no. Weblate har vore greitt, men ikkje fantastisk — eg fryktar av og til at det vil miste dataene mine når eg prøver å synkronisere det med git-repoet vårt. Flask har vore bra totalt sett, men det har nokre rare særtrekk som har kosta mykje tid å feilsøke, som å konfigurere eigendefinerte domener, eller problem med SqlAlchemy-integrasjonen. Så langt har dei andre verktøya vore flotte: vi har ingen alvorlege klager på MariaDB, ElasticSearch, Gitlab, Zulip, Docker og Tor. Alle desse har hatt nokre problem, men ingenting altfor alvorleg eller tidkrevjande. Samfunn Den første utfordringa kan vere overraskande. Det er ikkje eit teknisk problem, eller eit juridisk problem. Det er eit psykologisk problem: å gjere dette arbeidet i skuggen kan vere utruleg einsamt. Avhengig av kva du planlegg å gjere, og trusselmodellen din, kan det vere nødvendig å vere svært forsiktig. På den eine enden av spekteret har vi folk som Alexandra Elbakyan*, grunnleggaren av Sci-Hub, som er svært open om aktivitetane sine. Men ho er i stor fare for å bli arrestert om ho skulle besøke eit vestleg land på dette tidspunktet, og kan risikere fleire tiår i fengsel. Er det ein risiko du er villig til å ta? Vi er på den andre enden av spekteret; vi er svært forsiktige med å ikkje etterlate spor, og har sterk operasjonell tryggleik. * Som nemnt på HN av "ynno", ville Alexandra i utgangspunktet ikkje vere kjend: "Serverane hennar var sett opp til å sende ut detaljerte feilmeldingar frå PHP, inkludert full sti til feilkjeldefila, som var under katalogen /home/<USER>" Så, bruk tilfeldige brukarnamn på datamaskinene du bruker til dette, i tilfelle du konfigurerer noko feil. Den hemmeleghaldinga kjem imidlertid med ein psykologisk kostnad. Dei fleste elskar å bli anerkjende for arbeidet dei gjer, men du kan ikkje ta ære for dette i det verkelege livet. Til og med enkle ting kan vere utfordrande, som når vener spør kva du har halde på med (på eit tidspunkt blir "tukle med NAS / heime-labben min" gammalt). Dette er kvifor det er så viktig å finne eit fellesskap. Du kan gi opp litt operasjonell tryggleik ved å stole på nokre svært nære vener, som du veit du kan stole djupt på. Sjølv då bør du vere forsiktig med å ikkje skrive noko ned, i tilfelle dei må overlevere e-postane sine til myndigheitene, eller om enhetene deira blir kompromitterte på ein annan måte. Endå betre er det å finne nokre medpiratar. Om dine nære vener er interesserte i å bli med deg, flott! Om ikkje, kan du kanskje finne andre på nettet. Dessverre er dette framleis eit nisjefellesskap. Så langt har vi berre funne ein handfull andre som er aktive på dette området. Gode startstader ser ut til å vere Library Genesis-foruma, og r/DataHoarder. Archive Team har også likesinna individ, sjølv om dei opererer innanfor lova (sjølv om det er i nokre gråsoner av lova). Dei tradisjonelle "warez"- og piratscenene har også folk som tenker på liknande måtar. Vi er opne for idear om korleis vi kan fremje fellesskap og utforske idear. Ta gjerne kontakt med oss på Twitter eller Reddit. Kanskje vi kunne arrangere eit slags forum eller chattegruppe. Ei utfordring er at dette lett kan bli sensurert når ein bruker vanlege plattformer, så vi måtte ha arrangert det sjølve. Det er også ein avveging mellom å ha desse diskusjonane heilt offentleg (meir potensiell engasjement) versus å gjere det privat (ikkje la potensielle "mål" vite at vi er i ferd med å skrape dei). Vi må tenkje på det. Gi oss beskjed om du er interessert i dette! Konklusjon Forhåpentlegvis er dette nyttig for nybyrjande piratarkivarar. Vi er glade for å ønskje deg velkommen til denne verda, så ikkje nøl med å ta kontakt. La oss bevare så mykje av verdas kunnskap og kultur som mogleg, og spegle det vidt og breitt. Prosjekt 4. Dataval Ofte kan du bruke metadata til å finne ut ein rimeleg del av data å laste ned. Sjølv om du til slutt vil laste ned alle data, kan det vere nyttig å prioritere dei viktigaste elementa først, i tilfelle du blir oppdaga og forsvar blir forbetra, eller fordi du må kjøpe fleire diskar, eller rett og slett fordi noko anna dukkar opp i livet ditt før du kan laste ned alt. Til dømes kan ei samling ha fleire utgåver av den same underliggande ressursen (som ei bok eller ein film), der éi er merka som den beste kvaliteten. Å lagre dei utgåvene først ville gi mykje meining. Du vil kanskje til slutt lagre alle utgåver, sidan i nokre tilfelle kan metadata vere feilmerka, eller det kan vere ukjende avvegingar mellom utgåver (til dømes kan den "beste utgåva" vere best på dei fleste måtar, men dårlegare på andre måtar, som ein film med høgare oppløysing men utan undertekstar). Du kan òg søkje i metadata-databasen din for å finne interessante ting. Kva er den største fila som er vert, og kvifor er ho så stor? Kva er den minste fila? Er det interessante eller uventa mønster når det gjeld visse kategoriar, språk, og så vidare? Er det duplikat eller svært like titlar? Er det mønster for når data vart lagt til, som ein dag der mange filer vart lagt til samtidig? Du kan ofte lære mykje ved å sjå på datasettet på ulike måtar. I vårt tilfelle dedupliserte vi Z-Library-bøker mot md5-hashane i Library Genesis, og sparte dermed mykje nedlastingstid og diskplass. Dette er ein ganske unik situasjon, men. I dei fleste tilfelle finst det ikkje omfattande databasar over kva filer som allereie er skikkeleg bevart av andre piratar. Dette i seg sjølv er ein stor moglegheit for nokon der ute. Det ville vere flott å ha ei regelmessig oppdatert oversikt over ting som musikk og filmar som allereie er breitt sådd på torrent-nettstader, og som derfor er lågare prioritet å inkludere i piratspeglar. 6. Distribusjon Du har dataen, og dermed har du verdas første piratspegel av målet ditt (mest sannsynleg). På mange måtar er den vanskelegaste delen over, men den mest risikable delen er framleis framfor deg. Tross alt, så langt har du vore usynleg; flydd under radaren. Alt du måtte gjere var å bruke ein god VPN heile vegen, ikkje fylle inn personlege detaljar i nokon skjema (duh), og kanskje bruke ei spesiell nettlesarøkt (eller til og med ein annan datamaskin). No må du distribuere dataene. I vårt tilfelle ville vi først bidra med bøkene tilbake til Library Genesis, men oppdaga raskt vanskane med det (fiksjon vs. ikkje-fiksjon sortering). Så vi bestemte oss for distribusjon ved bruk av Library Genesis-stil torrents. Om du har moglegheit til å bidra til eit eksisterande prosjekt, kan det spare deg mykje tid. Men det er ikkje mange velorganiserte piratspeglar der ute for tida. Så la oss seie at du bestemmer deg for å distribuere torrents sjølv. Prøv å halde filene små, slik at dei er lette å spegle på andre nettstader. Du må deretter så torrents sjølv, medan du framleis held deg anonym. Du kan bruke ein VPN (med eller utan port forwarding), eller betale med tumla Bitcoins for ein Seedbox. Om du ikkje veit kva nokre av desse termene betyr, vil du ha ein del lesing å gjere, sidan det er viktig at du forstår risikofordelane her. Du kan hoste torrent-filene sjølve på eksisterande torrent-nettstader. I vårt tilfelle valde vi å faktisk hoste ei nettside, sidan vi òg ville spreie filosofien vår på ein klar måte. Du kan gjere dette sjølv på ein liknande måte (vi bruker Njalla for domena og hosting, betalt med tumla Bitcoins), men du kan òg gjerne kontakte oss for å la oss hoste torrentane dine. Vi ser etter å bygge ein omfattande indeks over piratspeglar over tid, om denne ideen slår an. Når det gjeld VPN-val, er det skrive mykje om dette allereie, så vi vil berre gjenta det generelle rådet om å velje etter omdømme. Faktiske rettsprova ingen-logg-politikkar med lange spor av å beskytte personvern er det lågaste risikovalet, etter vår meining. Merk at sjølv når du gjer alt riktig, kan du aldri kome til null risiko. Til dømes, når du sår torrentane dine, kan ein høgt motivert nasjonalstatleg aktør sannsynlegvis sjå på innkomande og utgåande dataflytar for VPN-serverar, og dedusere kven du er. Eller du kan rett og slett gjere ein feil på ein eller annan måte. Vi har sannsynlegvis allereie gjort det, og vil gjere det igjen. Heldigvis bryr ikkje nasjonalstatar seg <em>så</em> mykje om piratkopiering. Ein avgjerd for kvart prosjekt er om ein skal publisere det med same identitet som før, eller ikkje. Om du held fram med å bruke same namn, kan feil i operasjonell tryggleik frå tidlegare prosjekt kome tilbake og bite deg. Men å publisere under ulike namn betyr at du ikkje byggjer eit langvarig omdømme. Vi valde å ha sterk operasjonell tryggleik frå starten av slik at vi kan halde fram med å bruke same identitet, men vi vil ikkje nøle med å publisere under eit anna namn om vi gjer ein feil eller om omstenda krev det. Å få ordet ut kan vere vanskeleg. Som vi sa, er dette framleis eit nisjesamfunn. Vi posta opphavleg på Reddit, men fekk verkeleg fart på Hacker News. For no er vår tilråding å poste det på nokre få stader og sjå kva som skjer. Og igjen, kontakt oss. Vi vil gjerne spreie ordet om fleire piratarkivisme-innsatsar. 1. Domeneval / filosofi Det er ingen mangel på kunnskap og kulturarv som må reddast, noko som kan vere overveldande. Difor er det ofte nyttig å ta eit augeblikk og tenkje over kva ditt bidrag kan vere. Alle har ein ulik måte å tenkje på dette, men her er nokre spørsmål du kan stille deg sjølv: I vårt tilfelle brydde vi oss spesielt om langtidsbevaring av vitskap. Vi visste om Library Genesis, og korleis det var fullstendig spegla mange gonger ved hjelp av torrentar. Vi elska den ideen. Så ein dag prøvde ein av oss å finne nokre vitskaplege lærebøker på Library Genesis, men kunne ikkje finne dei, noko som sette spørsmålsteikn ved kor komplett det eigentleg var. Vi søkte deretter etter desse lærebøkene på nettet, og fann dei andre stader, noko som planta frøet for prosjektet vårt. Allereie før vi visste om Z-Library, hadde vi ideen om ikkje å prøve å samle alle desse bøkene manuelt, men å fokusere på å spegle eksisterande samlingar, og bidra med dei tilbake til Library Genesis. Kva ferdigheiter har du som du kan bruke til din fordel? Til dømes, om du er ein ekspert på netttryggleik, kan du finne måtar å omgå IP-blokkeringar for sikre mål. Om du er flink til å organisere samfunn, kan du kanskje samle folk rundt eit mål. Det er nyttig å kunne litt programmering, om ikkje anna for å halde god operasjonell tryggleik gjennom heile prosessen. Kva ville vere eit område med høg innverknad å fokusere på? Om du skal bruke X timar på piratarkivering, korleis kan du få mest mogleg ut av innsatsen din? Kva unike måtar tenkjer du på dette? Du kan ha nokre interessante idear eller tilnærmingar som andre kanskje har oversett. Kor mykje tid har du til dette? Vårt råd er å starte i det små og ta på deg større prosjekt etter kvart som du blir meir erfaren, men det kan bli altoppslukande. Kvifor er du interessert i dette? Kva brenn du for? Om vi kan få ein gjeng med folk som alle arkiverer dei tinga dei spesifikt bryr seg om, ville det dekke mykje! Du vil vite mykje meir enn den gjennomsnittlege personen om din lidenskap, som kva som er viktig data å lagre, kva som er dei beste samlingane og nettmiljøa, og så vidare. 3. Metadata-scraping Dato lagt til/modifisert: slik at du kan kome tilbake seinare og laste ned filer du ikkje lasta ned før (sjølv om du ofte òg kan bruke ID eller hash til dette). Hash (md5, sha1): for å stadfeste at du lasta ned fila riktig. ID: kan vere ein intern ID, men ID-ar som ISBN eller DOI er òg nyttige. Filnamn / plassering Skildring, kategori, stikkord, forfattarar, språk, osb. Storleik: for å rekne ut kor mykje diskplass du treng. La oss bli litt meir tekniske her. For faktisk å skrape metadata frå nettsider, har vi halde ting ganske enkelt. Vi bruker Python-skript, av og til curl, og ein MySQL-database for å lagre resultata i. Vi har ikkje brukt noko fancy skrapesoftware som kan kartlegge komplekse nettsider, sidan vi så langt berre har trengt å skrape ein eller to typar sider ved å berre enumerere gjennom id-ar og analysere HTML. Om det ikkje er lett å enumerere sider, kan det vere nødvendig med ein skikkeleg crawler som prøver å finne alle sidene. Før du startar å skrape ei heil nettside, prøv å gjere det manuelt ei stund. Gå gjennom nokre dusin sider sjølv, for å få ein følelse av korleis det fungerer. Nokre gonger vil du allereie møte IP-blokker eller annan interessant åtferd på denne måten. Det same gjeld for dataskraping: før du går for djupt inn i dette målet, sørg for at du faktisk kan laste ned dataene effektivt. For å kome rundt restriksjonar, er det nokre ting du kan prøve. Er det nokre andre IP-adresser eller serverar som hostar dei same dataene, men ikkje har dei same restriksjonane? Er det nokre API-endepunkt som ikkje har restriksjonar, medan andre har? Ved kva nedlastingsrate blir IP-en din blokkert, og for kor lenge? Eller blir du ikkje blokkert, men throttla ned? Kva om du opprettar ein brukarkonto, korleis endrar ting seg då? Kan du bruke HTTP/2 for å halde tilkoplingar opne, og aukar det raten du kan be om sider? Er det sider som listar fleire filer på ein gong, og er informasjonen som er lista der tilstrekkeleg? Ting du sannsynlegvis vil lagre inkluderer: Vi gjer dette vanlegvis i to steg. Først lastar vi ned dei rå HTML-filene, vanlegvis direkte inn i MySQL (for å unngå mange små filer, som vi snakkar meir om nedanfor). Deretter, i eit separat steg, går vi gjennom dei HTML-filene og analyserer dei inn i faktiske MySQL-tabellar. På denne måten treng du ikkje å laste ned alt på nytt frå botnen av om du oppdagar ein feil i analyse-koden din, sidan du berre kan prosessere HTML-filene på nytt med den nye koden. Det er òg ofte lettare å parallellisere prosesseringssteget, og dermed spare tid (og du kan skrive prosesseringskoden medan skrapinga køyrer, i staden for å måtte skrive begge stega samtidig). Til slutt, merk at for nokre mål er metadata-skraping alt som finst. Det finst nokre store metadata-samlingar der ute som ikkje er skikkeleg bevart. Tittel Domeneval / filosofi: Kvar vil du omtrent fokusere, og kvifor? Kva er dine unike lidenskapar, ferdigheiter og omstende som du kan bruke til din fordel? Målval: Kva spesifikke samling vil du spegle? Metadata-skraping: Katalogisere informasjon om filene utan å faktisk laste ned dei (ofte mykje større) filene sjølve. Dataval: Basert på metadata, avgrense kva data som er mest relevant å arkivere akkurat no. Det kan vere alt, men ofte finst det ein fornuftig måte å spare plass og bandbreidde på. Data-skraping: Faktisk hente dataen. Distribusjon: Pakke det inn i torrentar, annonsere det ein stad, få folk til å spreie det. 5. Datascraping No er du klar til å faktisk laste ned dataen i bulk. Som nemnt tidlegare, på dette tidspunktet bør du allereie manuelt ha lasta ned ein haug med filer, for å betre forstå åtferda og avgrensingane til målet. Men det vil framleis vere overraskingar i vente for deg når du faktisk kjem til å laste ned mange filer på ein gong. Vårt råd her er hovudsakleg å halde det enkelt. Start med å berre laste ned ein haug med filer. Du kan bruke Python, og deretter utvide til fleire trådar. Men av og til er det endå enklare å generere Bash-filer direkte frå databasen, og deretter køyre fleire av dei i fleire terminalvindauge for å skalere opp. Eit raskt teknisk triks verdt å nemne her er å bruke OUTFILE i MySQL, som du kan skrive kvar som helst om du deaktiverer "secure_file_priv" i mysqld.cnf (og ver sikker på å også deaktivere/overstyre AppArmor om du er på Linux). Vi lagrar dataen på enkle harddiskar. Start med det du har, og utvid sakte. Det kan vere overveldande å tenkje på å lagre hundrevis av TB med data. Om det er situasjonen du står overfor, berre legg ut eit godt delsett først, og i kunngjeringa di be om hjelp til å lagre resten. Om du vil skaffe fleire harddiskar sjølv, har r/DataHoarder nokre gode ressursar for å få gode tilbod. Prøv å ikkje bekymre deg for mykje om fancy filsystem. Det er lett å falle ned i kaninhòlet med å setje opp ting som ZFS. Ein teknisk detalj å vere merksam på, er at mange filsystem ikkje handterer mange filer godt. Vi har funne ut at ein enkel omgåing er å lage fleire katalogar, til dømes for ulike ID-område eller hash-prefiksar. Etter å ha lasta ned dataen, ver sikker på å sjekke integriteten til filene ved å bruke hashar i metadataen, om tilgjengeleg. 2. Målutvelging Tilgjengeleg: brukar ikkje mange lag med vern for å hindre deg i å skrape metadataa og dataa deira. Spesiell innsikt: du har noko spesiell informasjon om dette målet, som at du på ein eller annan måte har spesiell tilgang til denne samlinga, eller du har funne ut korleis du kan omgå forsvaret deira. Dette er ikkje nødvendig (vårt komande prosjekt gjer ikkje noko spesielt), men det hjelper absolutt! Stor Så, vi har vårt område vi ser på, kva spesifikke samling skal vi spegle? Det er nokre ting som gjer eit godt mål: Då vi fann våre vitskaplege lærebøker på nettsider andre enn Library Genesis, prøvde vi å finne ut korleis dei hadde funne vegen til internett. Vi fann deretter Z-Library, og innsåg at sjølv om dei fleste bøkene ikkje først dukkar opp der, endar dei til slutt opp der. Vi lærte om forholdet deira til Library Genesis, og den (økonomiske) insentivstrukturen og den overlegne brukargrensesnittet, som begge gjorde det til ei mykje meir komplett samling. Vi gjorde deretter noko innleiande metadata- og datascraping, og innsåg at vi kunne omgå deira IP-nedlastingsgrenser, ved å utnytte ein av våre medlemmers spesielle tilgang til mange proxy-serverar. Når du utforskar ulike mål, er det allereie viktig å skjule spora dine ved å bruke VPN-ar og eingongs-e-postadresser, som vi vil snakke meir om seinare. Unik: ikkje allereie godt dekka av andre prosjekt. Når vi gjer eit prosjekt, har det nokre fasar: Desse er ikkje heilt uavhengige fasar, og ofte sender innsikt frå ein seinare fase deg tilbake til ein tidlegare fase. Til dømes, under metadata-skraping kan du innsjå at målet du valde har forsvarsmekanismar utover ditt ferdigheitsnivå (som IP-blokkeringar), så du går tilbake og finn eit anna mål. - Anna og teamet (<a %(reddit)s>Reddit</a>) Heile bøker kan skrivast om kvifor digital bevaring er viktig generelt, og piratarkivisme spesielt, men la oss gi ein rask innføring for dei som ikkje er så kjende med det. Verda produserer meir kunnskap og kultur enn nokon gong før, men også meir av det går tapt enn nokon gong før. Menneskeheita stolar i stor grad på selskap som akademiske forlag, straumetenester og sosiale medieselskap med denne arven, og dei har ofte ikkje vist seg å vere gode forvaltarar. Sjå dokumentaren Digital Amnesia, eller eigentleg kva som helst foredrag av Jason Scott. Det finst nokre institusjonar som gjer ein god jobb med å arkivere så mykje som dei kan, men dei er bundne av lova. Som piratar er vi i ein unik posisjon til å arkivere samlingar som dei ikkje kan røre, på grunn av opphavsrettshåndheving eller andre restriksjonar. Vi kan også spegle samlingar mange gonger over heile verda, og dermed auke sjansane for riktig bevaring. For no vil vi ikkje gå inn i diskusjonar om fordelar og ulemper ved immaterielle rettar, moralen ved å bryte lova, funderingar om sensur, eller spørsmålet om tilgang til kunnskap og kultur. Med alt det ute av vegen, la oss dykke inn i korleis. Vi vil dele korleis teamet vårt blei piratarkivarar, og leksjonane vi lærte undervegs. Det er mange utfordringar når du legg ut på denne reisa, og forhåpentlegvis kan vi hjelpe deg gjennom nokre av dei. Korleis bli ein piratarkivar Den første utfordringa kan vere ei overraskande ei. Det er ikkje eit teknisk problem, eller eit juridisk problem. Det er eit psykologisk problem. Før vi dykkar inn, to oppdateringar om Pirate Library Mirror (EDIT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>): Vi fikk noen ekstremt sjenerøse donasjoner. Den første var $10k fra den anonyme personen som også har støttet "bookwarrior", den opprinnelige grunnleggeren av Library Genesis. Spesiell takk til bookwarrior for å ha tilrettelagt for denne donasjonen. Den andre var ytterligere $10k fra en anonym giver, som tok kontakt etter vår siste utgivelse, og ble inspirert til å hjelpe. Vi hadde også en rekke mindre donasjoner. Tusen takk for all deres sjenerøse støtte. Vi har noen spennende nye prosjekter på gang som dette vil støtte, så følg med. Vi hadde nokre tekniske utfordringar med storleiken på vår andre utgiving, men no er torrenta våre oppe og i gang. Vi fekk også eit generøst tilbod frå ein anonym person om å dele samlinga vår på deira svært raske serverar, så vi gjer ein spesiell opplasting til deira maskiner, etterpå bør alle andre som lastar ned samlinga sjå ei stor forbetring i hastigheit. Blogginnlegg Hei, eg er Anna. Eg skapte <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdas største skuggebibliotek. Dette er min personlege blogg, der eg og mine lagkameratar skriv om piratkopiering, digital bevaring, og meir. Koble med meg på <a %(reddit)s>Reddit</a>. Merk at denne nettsida berre er ein blogg. Vi hostar berre våre eigne ord her. Ingen torrentar eller andre opphavsrettsbeskytta filer er hosta eller lenka her. <strong>Bibliotek</strong> - Som dei fleste bibliotek fokuserer vi primært på skriftlege materialar som bøker. Vi kan utvide til andre typar media i framtida. <strong>Spegel</strong> - Vi er strengt tatt ein spegel av eksisterande bibliotek. Vi fokuserer på bevaring, ikkje på å gjere bøker lett søkbare og nedlastbare (tilgang) eller å fremje eit stort samfunn av folk som bidreg med nye bøker (kjelder). <strong>Pirat</strong> - Vi bryt med vilje opphavsrettslova i dei fleste land. Dette gjer at vi kan gjere noko som lovlege einingar ikkje kan: sørgje for at bøker blir spegla vidt og breitt. <em>Vi lenker ikkje til filene frå denne bloggen. Finn dei sjølv.</em> - Anna og teamet (<a %(reddit)s>Reddit</a>) Dette prosjektet (REDIGERT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) har som mål å bidra til bevaring og frigjering av menneskeleg kunnskap. Vi gjer vårt vesle og audmjuke bidrag, i fotspora til dei store før oss. Fokuset til dette prosjektet er illustrert av namnet: Det første biblioteket vi har spegla er Z-Library. Dette er eit populært (og ulovleg) bibliotek. Dei har tatt Library Genesis-samlinga og gjort den lett søkbar. I tillegg har dei blitt svært effektive til å be om nye bokbidrag, ved å gi insentiv til bidragsytande brukarar med ulike fordelar. Dei bidreg for tida ikkje med desse nye bøkene tilbake til Library Genesis. Og i motsetning til Library Genesis, gjer dei ikkje samlinga si lett spegelbar, noko som hindrar vid bevaring. Dette er viktig for forretningsmodellen deira, sidan dei tar betalt for tilgang til samlinga si i bulk (meir enn 10 bøker per dag). Vi gjer ikkje moralske vurderingar om å krevje pengar for bulktilgang til ei ulovleg boksamling. Det er utan tvil at Z-Library har vore suksessfulle i å utvide tilgangen til kunnskap, og skaffe fleire bøker. Vi er rett og slett her for å gjere vår del: sikre langtidsbevaring av denne private samlinga. Vi vil gjerne invitere deg til å hjelpe med å bevare og frigjere menneskeleg kunnskap ved å laste ned og dele våre torrentar. Sjå prosjektsida for meir informasjon om korleis dataene er organisert. Vi vil også veldig gjerne invitere deg til å bidra med ideane dine for kva samlingar som skal speglast neste gong, og korleis ein skal gå fram. Sammen kan vi oppnå mykje. Dette er berre eit lite bidrag blant utallige andre. Takk, for alt du gjer. Vi introduserer Piratbibliotekets spegel: Bevaring av 7TB med bøker (som ikkje er i Libgen) 10%% av menneskeheitens skriftlege arv bevart for alltid <strong>Google.</strong> Tross alt gjorde dei denne forskinga for Google Books. Men metadataen deira er ikkje tilgjengeleg i bulk og ganske vanskeleg å skrape. <strong>Ulike individuelle bibliotekssystem og arkiv.</strong> Det finst bibliotek og arkiv som ikkje har blitt indekserte og aggregerte av nokon av dei ovanfor, ofte fordi dei er underfinansierte, eller av andre grunnar ikkje vil dele dataene sine med Open Library, OCLC, Google, og så vidare. Mange av desse har digitale registreringar tilgjengelege gjennom internett, og dei er ofte ikkje veldig godt beskytta, så om du vil hjelpe til og ha det moro med å lære om rare bibliotekssystem, er desse flotte utgangspunkt. <strong>ISBNdb.</strong> Dette er temaet for denne bloggposten. ISBNdb skrapar ulike nettsider for bokmetadata, spesielt prisdata, som dei deretter sel til bokhandlarar, slik at dei kan prissetje bøkene sine i samsvar med resten av marknaden. Sidan ISBN-ar er ganske universelle no for tida, har dei effektivt bygd ei “nettside for kvar bok”. <strong>Open Library.</strong> Som nemnt før, er dette heile oppdraget deira. Dei har henta store mengder bibliotekdata frå samarbeidande bibliotek og nasjonale arkiv, og held fram med det. Dei har også frivillige bibliotekarar og eit teknisk team som prøver å fjerne duplikat i registreringane, og merke dei med alle slags metadata. Best av alt, datasettet deira er heilt ope. Du kan enkelt <a %(openlibrary)s>laste det ned</a>. <strong>WorldCat.</strong> Dette er ei nettside drive av den ideelle organisasjonen OCLC, som sel bibliotekstyringssystem. Dei samlar bokmetadata frå mange bibliotek, og gjer det tilgjengeleg gjennom WorldCat-nettsida. Men dei tener også pengar på å selje desse dataene, så dei er ikkje tilgjengelege for nedlasting i bulk. Dei har nokre meir avgrensa bulk-datasett tilgjengelege for nedlasting, i samarbeid med spesifikke bibliotek. 1. For ei rimeleg definisjon av "for alltid". ;) 2. Sjølvsagt er menneskeheita sitt skriftlege arv mykje meir enn berre bøker, spesielt i dag. For denne posten og våre siste utgjevingar fokuserer vi på bøker, men interessene våre strekkjer seg lenger. 3. Det er mykje meir som kan seiast om Aaron Swartz, men vi ville berre nemne han kort, sidan han spelar ei avgjerande rolle i denne historia. Etter kvart som tida går, kan fleire kome over namnet hans for første gong, og deretter dykke ned i kaninhòlet sjølv. <strong>Fysiske eksemplar.</strong> Openbart er ikkje dette veldig nyttig, sidan dei berre er duplikat av det same materialet. Det ville vore kult om vi kunne bevare alle notat folk gjer i bøker, som Fermats berømte “klotter i margen”. Men akk, det vil forbli ein arkivars draum. <strong>“Utgåver”.</strong> Her tel du kvar unik versjon av ei bok. Om noko ved den er annleis, som eit anna omslag eller eit anna forord, tel det som ei anna utgåve. <strong>Filer.</strong> Når ein arbeider med skyggebibliotek som Library Genesis, Sci-Hub eller Z-Library, er det ein ekstra vurdering. Det kan vere fleire skanningar av den same utgåva. Og folk kan lage betre versjonar av eksisterande filer, ved å skanne teksten ved hjelp av OCR, eller rette opp sider som blei skanna i ein vinkel. Vi vil berre telle desse filene som éi utgåve, noko som vil krevje god metadata, eller deduplisering ved hjelp av dokumentlikskapsmålingar. <strong>“Verk”.</strong> For eksempel “Harry Potter og Mysteriekammeret” som eit logisk konsept, som omfattar alle versjonar av det, som ulike omsetjingar og opptrykk. Dette er ein slags nyttig definisjon, men det kan vere vanskeleg å trekke linja for kva som tel. For eksempel vil vi sannsynlegvis bevare ulike omsetjingar, sjølv om opptrykk med berre mindre forskjellar kanskje ikkje er like viktige. - Anna og teamet (<a %(reddit)s>Reddit</a>) Med Pirate Library Mirror (REDIGERT: flytta til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), er målet vårt å ta alle bøkene i verda og bevare dei for alltid.<sup>1</sup> Mellom Z-Library-torrenta våre og dei originale Library Genesis-torrenta, har vi 11 783 153 filer. Men kor mange er det eigentleg? Om vi dedupliserte desse filene skikkeleg, kva prosentdel av alle bøkene i verda har vi bevart? Vi vil verkeleg gjerne ha noko som dette: La oss starte med nokre grove tal: I både Z-Library/Libgen og Open Library er det mange fleire bøker enn unike ISBN-ar. Tyder det at mange av desse bøkene ikkje har ISBN-ar, eller manglar berre ISBN-metadataen? Vi kan truleg svare på dette spørsmålet med ein kombinasjon av automatisert matching basert på andre attributtar (tittel, forfattar, forlag, osv.), å hente inn fleire datakjelder, og å trekke ut ISBN-ar frå sjølve bokskanna (i tilfelle av Z-Library/Libgen). Kor mange av desse ISBN-ane er unike? Dette er best illustrert med eit Venn-diagram: For å vere meir presis: Vi vart overraska over kor lite overlapp det er! ISBNdb har ein stor mengde ISBN-ar som ikkje dukkar opp i verken Z-Library eller Open Library, og det same gjeld (i mindre, men framleis betydeleg grad) for dei andre to. Dette reiser mange nye spørsmål. Kor mykje ville automatisert matching hjelpe med å merke bøkene som ikkje var merkte med ISBN-ar? Ville det vere mange treff og dermed auka overlapp? Og kva ville skje om vi hentar inn eit 4. eller 5. datasett? Kor mykje overlapp ville vi sjå då? Dette gir oss eit utgangspunkt. Vi kan no sjå på alle ISBN-ar som ikkje var i Z-Library-datasettet, og som heller ikkje samsvarar med tittel/forfattar-felt. Det kan gi oss eit handtak på å bevare alle bøkene i verda: først ved å skrape internett for skannar, deretter ved å gå ut i verkelegheita for å skanne bøker. Det siste kan til og med bli folkefinansiert, eller drive av “dusørar” frå folk som vil sjå spesifikke bøker digitaliserte. Alt det er ei historie for ein annan gong. Om du vil hjelpe til med noko av dette — vidare analyse; skraping av meir metadata; finne fleire bøker; OCR’ing av bøker; gjere dette for andre domene (f.eks. artiklar, lydbøker, filmar, TV-seriar, magasin) eller til og med gjere noko av desse dataene tilgjengelege for ting som ML / store språkmodelltreningar — ver venleg å kontakte meg (<a %(reddit)s>Reddit</a>). Om du er spesielt interessert i dataanalysen, jobbar vi med å gjere datasetta og skripta våre tilgjengelege i eit meir brukervennleg format. Det ville vere flott om du berre kunne forke ein notatbok og begynne å leike med dette. Til slutt, om du vil støtte dette arbeidet, ver venleg å vurdere å gi ei gåve. Dette er ein heilt frivillig drift, og ditt bidrag gjer ein stor forskjell. Kvar bit hjelper. For no tek vi imot gåver i krypto; sjå Gåve-sida på Anna sitt Arkiv. For ein prosentdel, treng vi ein nemnar: det totale talet på bøker som nokon gong er publisert.<sup>2</sup> Før Google Books gjekk under, prøvde ein ingeniør på prosjektet, Leonid Taycher, <a %(booksearch_blogspot)s>å estimere</a> dette talet. Han kom opp — med glimt i auga — med 129 864 880 (“i alle fall til søndag”). Han estimerte dette talet ved å bygge ein samla database over alle bøkene i verda. For dette samla han ulike datasett og slo dei saman på ulike måtar. Som ein rask digresjon, er det ein annan person som prøvde å katalogisere alle bøkene i verda: Aaron Swartz, den avdøde digitale aktivisten og Reddit-medstiftaren.<sup>3</sup> Han <a %(youtube)s>starta Open Library</a> med målet om “ei nettside for kvar bok som nokon gong er publisert”, ved å kombinere data frå mange ulike kjelder. Han enda opp med å betale den ultimate prisen for sitt digitale bevaringsarbeid då han blei tiltalt for masse-nedlasting av akademiske artiklar, noko som førte til sjølvmordet hans. Det er unødvendig å seie at dette er ein av grunnane til at gruppa vår er pseudonym, og kvifor vi er svært forsiktige. Open Library blir framleis heroisk drive av folk ved Internet Archive, som held fram med Aarons arv. Vi kjem tilbake til dette seinare i dette innlegget. I Google-blogginnlegget skildrar Taycher nokre av utfordringane med å estimere dette talet. Først, kva utgjer ei bok? Det er nokre moglege definisjonar: “Utgåver” verkar som den mest praktiske definisjonen av kva “bøker” er. Praktisk nok blir denne definisjonen også brukt for å tildele unike ISBN-nummer. Ein ISBN, eller Internasjonalt Standard Boknummer, blir vanlegvis brukt for internasjonal handel, sidan det er integrert med det internasjonale strekkodesystemet (”International Article Number”). Om du vil selje ei bok i butikkar, treng den ein strekkode, så du får ein ISBN. Taycher sin bloggpost nemner at sjølv om ISBN-ar er nyttige, er dei ikkje universelle, sidan dei berre vart verkeleg adopterte på midten av syttitalet, og ikkje overalt i verda. Likevel er ISBN truleg den mest brukte identifikatoren for bokutgåver, så det er vårt beste utgangspunkt. Om vi kan finne alle ISBN-ar i verda, får vi ei nyttig liste over kva bøker som framleis treng å bli bevart. Så, kvar får vi data frå? Det er fleire eksisterande initiativ som prøver å samle ei liste over alle bøkene i verda: I denne posten er vi glade for å kunngjere ei lita utgjeving (samanlikna med våre tidlegare Z-Library-utgjevingar). Vi skrapa mesteparten av ISBNdb, og gjorde dataene tilgjengelege for torrenting på nettsida til Pirate Library Mirror (EDIT: flytta til <a %(wikipedia_annas_archive)s>Anna sitt Arkiv</a>; vi vil ikkje lenkje direkte her, berre søk etter det). Dette er om lag 30,9 millionar registreringar (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippa). På nettsida deira hevdar dei at dei faktisk har 32,6 millionar registreringar, så vi kan ha gått glipp av nokre, eller <em>dei</em> kan gjere noko feil. Uansett, for no vil vi ikkje dele nøyaktig korleis vi gjorde det — vi vil la det vere ei oppgåve for lesaren. ;-) Det vi vil dele er nokre førebelse analysar, for å prøve å kome nærare å estimere talet på bøker i verda. Vi såg på tre datasett: dette nye ISBNdb-datasettet, vår originale utgjeving av metadata som vi skrapa frå Z-Library skuggebibliotek (som inkluderer Library Genesis), og Open Library data dump. ISBNdb dump, eller Kor Mange Bøker Er Bevarte For Alltid? Om vi skulle deduplikere filene frå skyggebiblioteka skikkeleg, kva prosentdel av alle bøkene i verda har vi bevart? Oppdateringar om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det største verkeleg opne biblioteket i menneskehistoria. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Annas Arkiv Behaldarar (AAC)</a>, som i hovudsak er <a %(jsonlines)s>JSON Lines</a> komprimert med <a %(zstd)s>Zstandard</a>, pluss nokre standardiserte semantikkar. Desse behaldarane pakkar inn ulike typar postar, basert på dei forskjellige skrapa vi har utført. For et år siden <a %(blog)s>begynte vi</a> å svare på dette spørsmålet: <strong>Hvilken prosentandel av bøker har blitt permanent bevart av skyggebiblioteker?</strong> La oss sjå på litt grunnleggjande informasjon om dataene: Når en bok kommer inn i et åpen-data skyggebibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, og nå <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, blir den speilet over hele verden (gjennom torrents), og dermed praktisk talt bevart for alltid. For å svare på spørsmålet om kva prosentdel av bøkene som er bevart, må vi vite nevneren: kor mange bøker finst det totalt? Og ideelt sett har vi ikkje berre eit tal, men faktisk metadata. Då kan vi ikkje berre matche dei mot skuggebibliotek, men også <strong>lage ei TODO-liste over dei resterande bøkene som skal bevarast!</strong> Vi kan til og med begynne å drøyme om ein folkefinansiert innsats for å gå gjennom denne TODO-lista. Vi skrapa <a %(wikipedia_isbndb_com)s>ISBNdb</a>, og lasta ned <a %(openlibrary)s>Open Library dataset</a>, men resultata var utilfredsstillande. Hovudproblemet var at det ikkje var mykje overlapp av ISBN-ar. Sjå dette Venn-diagrammet frå <a %(blog)s>blogginnlegget vårt</a>: Vi vart svært overraska over kor lite overlapp det var mellom ISBNdb og Open Library, som begge inkluderer data frå ulike kjelder, som nettskraping og bibliotekregistreringar. Om dei begge gjer ein god jobb med å finne dei fleste ISBN-ar der ute, ville sirklane deira sikkert hatt betydeleg overlapp, eller ein ville vore ein delmengde av den andre. Det fekk oss til å undre, kor mange bøker fell <em>fullstendig utanfor desse sirklane</em>? Vi treng ein større database. Det var då vi sette blikket vårt på den største bokdatabasen i verda: <a %(wikipedia_worldcat)s>WorldCat</a>. Dette er ein eigedomsbeskytta database av den ideelle organisasjonen <a %(wikipedia_oclc)s>OCLC</a>, som samlar metadataoppføringar frå bibliotek over heile verda, i byte mot å gi desse biblioteka tilgang til heile datasettet, og få dei til å dukke opp i sluttbrukarane sine søkeresultat. Sjølv om OCLC er ein ideell organisasjon, krev forretningsmodellen deira at dei beskyttar databasen sin. Vel, vi er lei for å seie det, vener hos OCLC, vi gir det heile bort. :-) I løpet av det siste året har vi nøye skrapa alle WorldCat-postar. I starten fekk vi eit lykketreff. WorldCat var nettopp i ferd med å rulle ut den fullstendige redesignen av nettsida si (i august 2022). Dette inkluderte ei omfattande overhaling av baksystema deira, som introduserte mange sikkerheitsfeil. Vi utnytta straks moglegheita, og klarte å skrape hundrevis av millionar (!) av postar på berre nokre dagar. Etter det vart sikkerheitsfeila sakte fiksa ein etter ein, til den siste vi fann vart lappa for om lag ein månad sidan. På det tidspunktet hadde vi stort sett alle postane, og gjekk berre for litt høgare kvalitet på postane. Så vi følte det var på tide å sleppe det! 1,3 milliarder WorldCat-uttrekk <em><strong>TL;DR:</strong> Annas Arkiv har skrapet hele WorldCat (verdens største bibliotekmetadata-samling) for å lage en TODO-liste over bøker som må bevares.</em> WorldCat Advarsel: dette blogginnlegget er foreldet. Vi har bestemt at IPFS ennå ikke er klar for prime time. Vi vil fortsatt lenke til filer på IPFS fra Annas Arkiv når det er mulig, men vi vil ikke lenger være vert for det selv, og vi anbefaler heller ikke andre å speile ved hjelp av IPFS. Vennligst se vår Torrents-side hvis du vil hjelpe med å bevare vår samling. Hjelp til med å så Z-Library på IPFS Partner Server-nedlasting SciDB Ekstern lån Ekstern lån (utskrift deaktivert) Ekstern nedlasting Utforsk metadata Inneholdt i torrents Tilbake  (+%(num)s bonus) ikkje betalt betalt kansellert utgått ventar på at Anna skal stadfeste ugyldig Teksten nedanfor held fram på engelsk. Gå Tilbakestill Framover Siste Hvis e-postadressen din ikke fungerer på Libgen-forumene, anbefaler vi å bruke <a %(a_mail)s>Proton Mail</a> (gratis). Du kan også <a %(a_manual)s>manuelt be</a> om at kontoen din blir aktivert. (kan krevje <a %(a_browser)s>nettlesarverifisering</a> — uavgrensa nedlastingar!) Rask Partnarserver #%(number)s (anbefalt) (litt raskare, men med venteliste) (ingen nettleserverifisering kreves) (ingen nettlesarverifisering eller ventelister) (ingen venteliste, men kan vere veldig treg) Sein Partnarserver #%(number)s Lydbok Tegneserie Bok (skjønnlitteratur) Bok (sakprosa) Bok (ukjent) Tidsskriftartikkel Magasin Musikalsk partitur Annet Standarddokument Ikke alle sider kunne konverteres til PDF Merket som ødelagt i Libgen.li Ikke synlig i Libgen.li Ikke synlig i Libgen.rs Fiction Ikke synlig i Libgen.rs Non-Fiction Kjøring av exiftool feilet på denne filen Merka som «dårleg fil» i Z-Library Mangler fra Z-Library Merka som «søppelpost» i Z-Library Fila kan ikkje opnast (f.eks. øydelagd fil, DRM) Opphavsrettskrav Nedlastingsproblem (f.eks. kan ikkje kople til, feilmelding, veldig tregt) Feil metadata (f.eks. tittel, beskriving, omslagsbilete) Anna Dårleg kvalitet (f.eks. formateringsproblem, dårleg skannekvalitet, manglande sider) Søppelpost / fil bør fjernast (f.eks. reklame, støytande innhald) %(amount)s (%(amount_usd)s) %(amount)s totalt %(amount)s (%(amount_usd)s) totalt Briljant Bokorm Lukkeleg Librarian Dundrande Datahamstrar Amazande Arkivar Bonusnedlastingar Cerlalc Tsjekkiske metadata DuXiu 读秀 EBSCOhost eBook-indeks Google Bøker Goodreads HathiTrust IA IA Kontrollert Digital Utlån ISBNdb ISBN GRP Libgen.li Ekskluderer “scimag” Libgen.rs Sakprosa og skjønnlitteratur Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russlands statsbibliotek Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Opplastinger til AA Z-Library Z-Library Kinesisk Tittel, forfatter, DOI, ISBN, MD5, … Søk Forfattar Skildring og metadata-kommentarar Utgåve Original filnamn Forlag (søk spesifikt felt) Tittel Utgivingsår Tekniske detaljar Denne mynten har en høyere enn vanlig minimum. Vennligst velg en annen varighet eller en annen mynt. Forespørselen kunne ikke fullføres. Vennligst prøv igjen om noen minutter, og hvis det fortsetter å skje, kontakt oss på %(email)s med et skjermbilde. En ukjent feil oppstod. Vennligst kontakt oss på %(email)s med et skjermbilde. Feil i betalingsbehandling. Vennligst vent et øyeblikk og prøv igjen. Hvis problemet vedvarer i mer enn 24 timer, vennligst kontakt oss på %(email)s med et skjermbilde. Vi køyrer ein innsamlingsaksjon for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">å sikkerheitskopiere</a> verdas største skuggebibliotek for teikneseriar. Takk for støtta! <a href="/donate">Doner.</a> Om du ikkje kan donere, vurder å støtte oss ved å fortelje venene dine, og følgje oss på <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>. Ikkje send e-post til oss for å <a %(a_request)s>be om bøker</a><br>eller små (<10k) <a %(a_upload)s>opplastingar</a>. Annas Arkiv DMCA / opphavsrettskrav Hald kontakten Reddit Alternativ SLUM (%(unaffiliated)s) ikkje tilknytt Annas Arkiv trenger din hjelp! Om du donerer no, får du <strong>dobbel</strong> mengde raske nedlastingar. Mange prøver å ta oss ned, men vi kjemper tilbake. Hvis du donerer denne måneden, får du <strong>dobbelt så mange</strong> raske nedlastinger. Gyldig til slutten av denne månaden. Å redde menneskelig kunnskap: en flott julegave! Medlemskap vil bli forlenga tilsvarande. Partnartenester er utilgjengelege på grunn av stenging av hosting. Dei bør vere oppe igjen snart. For å øke motstandsdyktigheten til Annas Arkiv, ser vi etter frivillige til å kjøre speil. Vi har ein ny donasjonsmetode tilgjengeleg: %(method_name)s. Ver venleg å vurder %(donate_link_open_tag)så donere</a> — det er ikkje billeg å drive denne nettsida, og di donasjon gjer verkeleg ein forskjell. Tusen takk. Tips ein ven, og både du og venen din får %(percentage)s%% bonus raske nedlastingar! Overrask en du er glad i, gi dem en konto med medlemskap. Den perfekte Valentinsgåva! Lær meir… Konto Aktivitet Avansert Anna sin Blogg ↗ Annas programvare ↗ beta Kodeutforskar Datasett Doner Nedlastede filer FAQ Heim Forbetre metadata LLM-data Logg inn / Registrer Mine donasjoner Offentlig profil Søk Sikkerheit Torrents Omset ↗ Frivillig arbeid & dusørar Nylege nedlastingar: 📚&nbsp;Verdas største open-source open-data bibliotek. ⭐️&nbsp;Spegl Sci-Hub, Library Genesis, Z-Library, og meir. 📈&nbsp;%(book_any)s bøker, %(journal_article)s artiklar, %(book_comic)s teikneseriar, %(magazine)s magasin — bevart for alltid.  og  og meir DuXiu Internet Archive Lending Library Libgen 📚&nbsp;Den største verkeleg opne biblioteket i menneskehistoria. 📈&nbsp;%(book_count)s&nbsp;bøker, %(paper_count)s&nbsp;artiklar — bevart for alltid. ⭐️&nbsp;Vi speglar %(libraries)s. Vi skrapar og open-source %(scraped)s. All vår kode og data er heilt open source. OpenLib Sci-Hub ,  📚 Verdas største open-source open-data bibliotek.<br>⭐️ Speglar Scihub, Libgen, Zlib, og meir. Z-Lib Anna sitt Arkiv Ugyldig forespørsel. Besøk %(websites)s. Verdens største åpen kildekode åpen data-bibliotek. Speiler Sci-Hub, Library Genesis, Z-Library og mer. Søk i Annas Arkiv Annas Arkiv Ver vennligst sida på nytt for å prøve igjen. <a %(a_contact)s>Kontakt oss</a> dersom problemet vedvarer i fleire timar. 🔥 Problem med å laste denne sida <li>1. Følg oss på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spre ordet om Anna’s Archive på Twitter, Reddit, Tiktok, Instagram, på din lokale kafé eller bibliotek, eller kvar du enn går! Vi trur ikkje på portvakter — om vi blir tatt ned, dukkar vi berre opp ein annan stad, sidan all vår kode og data er heilt open source.</li><li>3. Om du kan, vurder å <a href="/donate">donere</a>.</li><li>4. Hjelp til med å <a href="https://translate.annas-software.org/">omsetje</a> nettsida vår til ulike språk.</li><li>5. Om du er programvareingeniør, vurder å bidra til vår <a href="https://annas-software.org/">open source</a>, eller så frø våre <a href="/datasets">torrents</a>.</li> 10. Lag eller hjelp til med å vedlikehalde Wikipedia-sida for Anna’s Archive på ditt språk. 11. Vi ser etter å plassere små, smakfulle annonser. Hvis du ønsker å annonsere på Anna sitt Arkiv, vennligst gi oss beskjed. 6. Om du er ein sikkerheitsforskar, kan vi bruke dine ferdigheiter både til angrep og forsvar. Sjekk ut vår <a %(a_security)s>Sikkerheit</a>-side. 7. Vi ser etter ekspertar på betalingar for anonyme seljarar. Kan du hjelpe oss med å legge til meir praktiske måtar å donere på? PayPal, WeChat, gåvekort. Om du kjenner nokon, ver venleg å kontakte oss. 8. Vi ser alltid etter meir serverkapasitet. 9. Du kan hjelpe ved å rapportere filproblem, legge igjen kommentarar, og lage lister rett på denne nettsida. Du kan også hjelpe ved å <a %(a_upload)s>laste opp fleire bøker</a>, eller fikse filproblem eller formatering av eksisterande bøker. For mer omfattende informasjon om hvordan du kan være frivillig, se vår <a %(a_volunteering)s>Frivillighet & Belønninger</a>-side. Vi trur sterkt på fri flyt av informasjon, og bevaring av kunnskap og kultur. Med denne søkemotoren bygger vi på skuldrene til giganter. Vi har djupt respekt for det harde arbeidet til dei som har skapt dei ulike skyggebiblioteka, og vi håper at denne søkemotoren vil utvide deira rekkevidde. For å halde deg oppdatert på vår framgang, følg Anna på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> eller <a href="https://t.me/annasarchiveorg">Telegram</a>. For spørsmål og tilbakemeldingar, kontakt Anna på %(email)s. Konto-ID: %(account_id)s Logg ut ❌ Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. ✅ Du er nå logget ut. Last inn siden på nytt for å logge inn igjen. Raske nedlastingar brukt (siste 24 timar): <strong>%(used)s / %(total)s</strong> Medlemskap: <strong>%(tier_name)s</strong> til %(until_date)s <a %(a_extend)s>(forleng)</a> Du kan kombinere flere medlemskap (raske nedlastinger per 24 timer vil bli lagt sammen). Medlemskap: <strong>Ingen</strong> <a %(a_become)s>(bli medlem)</a> Kontakt Anna på %(email)s hvis du er interessert i å oppgradere medlemskapet ditt til et høyere nivå. Offentleg profil: %(profile_link)s Hemmeleg nøkkel (ikkje del!): %(secret_key)s vis Bli med oss her! Oppgrader til et <a %(a_tier)s>høyere nivå</a> for å bli med i vår gruppe. Eksklusiv Telegram-gruppe: %(link)s Konto kva nedlastingar? Logg inn Ikke mist nøkkelen din! Ugyldig hemmelig nøkkel. Verifiser nøkkelen din og prøv igjen, eller registrer en ny konto nedenfor. Hemmelig nøkkel Skriv inn din hemmelige nøkkel for å logge inn: Gammel e-postbasert konto? Skriv inn din <a %(a_open)s>e-post her</a>. Registrer ny konto Har du ikke en konto ennå? Registrering vellykket! Din hemmelige nøkkel er: <span %(span_key)s>%(key)s</span> Lagre denne nøkkelen nøye. Hvis du mister den, vil du miste tilgangen til kontoen din. <li %(li_item)s><strong>Bokmerke.</strong> Du kan bokmerke denne siden for å hente nøkkelen din.</li><li %(li_item)s><strong>Last ned.</strong> Klikk <a %(a_download)s>denne lenken</a> for å laste ned nøkkelen din.</li><li %(li_item)s><strong>Passordbehandler.</strong> Bruk en passordbehandler for å lagre nøkkelen når du skriver den inn nedenfor.</li> Logg inn / Registrer Nettlesarverifisering Åtvaring: koden har feil Unicode-teikn i seg, og kan oppføre seg feil i ulike situasjonar. Den rå binæren kan dekodast frå base64-representasjonen i URL-en. Skildring Etikett Prefiks URL for ein spesifikk kode Nettstad Kodar som startar med “%(prefix_label)s” Ver venleg å ikkje skrape desse sidene. I staden anbefaler vi <a %(a_import)s>generering</a> eller <a %(a_download)s>nedlasting</a> av våre ElasticSearch- og MariaDB-databasar, og å køyre vår <a %(a_software)s>open kjeldekode</a>. Dei rå dataa kan utforskast manuelt gjennom JSON-filer som <a %(a_json_file)s>denne</a>. Færre enn %(count)s oppføringar Generisk URL Kodeutforskar Indeks over Utforsk kodane som oppføringane er merka med, etter prefiks. Kolonna «oppføringar» viser talet på oppføringar merka med kodar med den gitte prefiksen, slik det er sett i søkemotoren (inkludert metadata-only oppføringar). Kolonna «kodar» viser kor mange faktiske kodar som har ein gitt prefiks. Kjent kodeprefiks «%(key)s» Meir… Prefiks %(count)s oppføring som samsvarer med “%(prefix_label)s” %(count)s oppføringar som samsvarer med “%(prefix_label)s” kodar oppføringar «%%s» vil bli erstatta med verdien til koden Søk i Annas Arkiv Kodar URL for spesifikk kode: “%(url)s” Denne sida kan ta litt tid å generere, og derfor krev ho ein Cloudflare captcha. <a %(a_donate)s>Medlemmer</a> kan hoppe over captchaen. Misbruk rapportert: Betre versjon Vil du rapportere denne brukaren for misbruk eller upassande åtferd? Filproblem: %(file_issue)s skjult kommentar Svar Rapporter misbruk Du rapporterte denne brukaren for misbruk. Opphavsrettskrav til denne e-posten vil bli ignorert; bruk skjemaet i staden. Vis e-post Vi set stor pris på tilbakemeldingane og spørsmåla dine! Men på grunn av mengda spam og tullete e-postar vi får, ver venleg å kryss av boksane for å stadfeste at du forstår desse vilkåra for å kontakte oss. Andre måtar å kontakte oss om opphavsrettskrav vil bli automatisk sletta. For DMCA / opphavsrettskrav, bruk <a %(a_copyright)s>dette skjemaet</a>. Kontakt e-post URL-er på Anna sitt arkiv (påkrevd). Én per linje. Vennligst inkluder kun URL-er som beskriver nøyaktig samme utgave av en bok. Hvis du vil gjøre krav på flere bøker eller flere utgaver, vennligst send inn dette skjemaet flere ganger. Krav som samler flere bøker eller utgaver sammen vil bli avvist. Adresse (påkrevd) Klar beskrivelse av kildematerialet (påkrevd) E-post (påkrevd) URL-er til kildematerialet, én per linje (påkrevd). Vennligst inkluder så mange som mulig, for å hjelpe oss med å verifisere ditt krav (f.eks. Amazon, WorldCat, Google Books, DOI). ISBN-er til kildematerialet (hvis aktuelt). Én per linje. Vennligst inkluder kun de som nøyaktig samsvarer med utgaven du rapporterer et opphavsrettskrav for. Ditt navn (påkrevd) ❌ Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. ✅ Takk for at du sendte inn ditt opphavsrettskrav. Vi vil gjennomgå det så snart som mulig. Vennligst last inn siden på nytt for å sende inn et nytt krav. <a %(a_openlib)s>Open Library</a> URL-er til kildematerialet, én per linje. Vennligst ta deg tid til å søke i Open Library etter ditt kildemateriale. Dette vil hjelpe oss med å verifisere ditt krav. Telefonnummer (påkrevd) Erklæring og signatur (påkrevd) Send inn krav Hvis du har et DCMA- eller annet opphavsrettskrav, vennligst fyll ut dette skjemaet så nøyaktig som mulig. Hvis du støter på problemer, vennligst kontakt oss på vår dedikerte DMCA-adresse: %(email)s. Merk at krav sendt til denne adressen ikke vil bli behandlet, den er kun for spørsmål. Vennligst bruk skjemaet nedenfor for å sende inn dine krav. DMCA / Skjema for opphavsrettskrav Eksempelpost på Anna sitt Arkiv Torrentar av Anna sitt Arkiv Anna sitt arkivbeholderformat Skript for importering av metadata Hvis du er interessert i å speile dette datasettet for <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-trening</a>, vennligst kontakt oss. Sist oppdatert: %(date)s Hoved %(source)s nettside Metadata-dokumentasjon (dei fleste felta) Filer spegla av Anna sitt Arkiv: %(count)s (%(percent)s%%) Ressurser Totalt antal filer: %(count)s Total filstorleik: %(size)s Vårt blogginnlegg om disse dataene <a %(duxiu_link)s>Duxiu</a> er en massiv database med skannede bøker, opprettet av <a %(superstar_link)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøker, skannet for å gjøre dem tilgjengelige digitalt for universiteter og biblioteker. For vårt engelsktalende publikum har <a %(princeton_link)s>Princeton</a> og <a %(uw_link)s>University of Washington</a> gode oversikter. Det finnes også en utmerket artikkel som gir mer bakgrunn: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Bøkene fra Duxiu har lenge blitt piratkopiert på det kinesiske internettet. Vanligvis blir de solgt for mindre enn en dollar av forhandlere. De distribueres typisk ved bruk av den kinesiske ekvivalenten til Google Drive, som ofte har blitt hacket for å tillate mer lagringsplass. Noen tekniske detaljer kan finnes <a %(link1)s>her</a> og <a %(link2)s>her</a>. Selv om bøkene har blitt semi-offentlig distribuert, er det ganske vanskelig å skaffe dem i bulk. Vi hadde dette høyt på vår TODO-liste, og allokerte flere måneder med fulltidsarbeid for det. Men, sent i 2023 tok en utrolig, fantastisk og talentfull frivillig kontakt med oss og fortalte at de allerede hadde gjort alt dette arbeidet — til stor kostnad. De delte hele samlingen med oss, uten å forvente noe i retur, bortsett fra garantien om langsiktig bevaring. Virkelig bemerkelsesverdig. Mer informasjon fra våre frivillige (rå notater): Tilpasset fra vårt <a %(a_href)s>blogginnlegg</a>. DuXiu 读秀 %(count)s fil %(count)s filer Dette datasettet er nært knyttet til <a %(a_datasets_openlib)s>Open Library-datasettet</a>. Det inneholder en skraping av all metadata og en stor del av filer fra IA’s Controlled Digital Lending Library. Oppdateringer blir utgitt i <a %(a_aac)s>Anna sitt arkivbeholderformat</a>. Desse postane er henta direkte frå Open Library-datasettet, men inneheld også postar som ikkje er i Open Library. Vi har også ei rekke datafiler som er skrapa av samfunnsmedlemmer gjennom åra. Samlinga består av to delar. Du treng begge delane for å få all data (bortsett frå utgåtte torrentar, som er kryssa ut på torrentsida). Digitalt Utlånsbibliotek vår første utgåve, før vi standardiserte på <a %(a_aac)s>Anna sitt Arkiv Containers (AAC) format</a>. Inneheld metadata (som json og xml), pdf-ar (frå acsm og lcpdf digitale utlånssystem), og omslagsminiatyrar. inkrementelle nye utgåver, ved bruk av AAC. Inneheld berre metadata med tidsstempel etter 2023-01-01, sidan resten allereie er dekt av “ia”. Også alle pdf-filer, denne gongen frå acsm og “bookreader” (IA sitt nettlesarverktøy) utlånssystem. Sjølv om namnet ikkje er heilt korrekt, fyller vi framleis bookreader-filer inn i ia2_acsmpdf_files-samlinga, sidan dei er gjensidig utelukkande. IA Kontrollert Digital Utlån 98%%+ av filer er søkbare. Vår misjon er å arkivere alle bøkene i verda (samt artiklar, magasin, osv.), og gjere dei vidt tilgjengelege. Vi trur at alle bøker bør speglast vidt og breitt, for å sikre redundans og motstandsdyktigheit. Dette er kvifor vi samlar filer frå ei rekke kjelder. Nokre kjelder er heilt opne og kan speglast i bulk (som Sci-Hub). Andre er lukka og beskyttande, så vi prøver å skrape dei for å “frigjere” bøkene deira. Andre igjen fell ein stad i mellom. Alle dataene våre kan <a %(a_torrents)s>torrenterast</a>, og all metadataen vår kan <a %(a_anna_software)s>genererast</a> eller <a %(a_elasticsearch)s>lastast ned</a> som ElasticSearch- og MariaDB-databasar. Rådataene kan manuelt utforskast gjennom JSON-filer som <a %(a_dbrecord)s>denne</a>. Metadata ISBN-nettsted Sist oppdatert: %(isbn_country_date)s (%(link)s) Ressurser Den internasjonale ISBN-byrået gir jevnlig ut områdene som er tildelt nasjonale ISBN-byråer. Fra dette kan vi utlede hvilket land, region eller språkgruppe denne ISBN tilhører. Vi bruker for tiden disse dataene indirekte, gjennom <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket. ISBN-landinformasjon Dette er en dump av mange kall til isbndb.com i løpet av september 2022. Vi prøvde å dekke alle ISBN-områder. Dette er omtrent 30,9 millioner poster. På deres nettsted hevder de at de faktisk har 32,6 millioner poster, så vi kan ha gått glipp av noen, eller <em>de</em> kan gjøre noe feil. JSON-responsene er stort sett rå fra deres server. Et datakvalitetsproblem vi la merke til, er at for ISBN-13-numre som starter med et annet prefiks enn “978-”, inkluderer de fortsatt et “isbn”-felt som ganske enkelt er ISBN-13-nummeret med de første 3 sifrene kuttet av (og kontrollsifferet rekalkulert). Dette er åpenbart feil, men det er slik de ser ut til å gjøre det, så vi endret det ikke. Et annet potensielt problem du kan støte på, er at “isbn13”-feltet har duplikater, så du kan ikke bruke det som en primærnøkkel i en database. Kombinasjonen av “isbn13”+“isbn”-feltene ser imidlertid ut til å være unik. Utgivelse 1 (2022-10-31) Skjønnlitterære torrentar er bak (sjølv om ID-ar ~4-6M ikkje er torrentert sidan dei overlappar med våre Zlib-torrentar). Vårt blogginnlegg om utgivelsen av tegneserier Tegneserie-torrenter på Annas Arkiv For bakgrunnshistorien til de forskjellige Library Genesis-forkene, se siden for <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li inneholder det meste av det samme innholdet og metadataene som Libgen.rs, men har noen samlinger i tillegg, nemlig tegneserier, magasiner og standarddokumenter. Det har også integrert <a %(a_scihub)s>Sci-Hub</a> i sine metadata og søkemotor, som vi bruker for vår database. Metadataene for dette biblioteket er fritt tilgjengelige <a %(a_libgen_li)s>på libgen.li</a>. Imidlertid er denne serveren treg og støtter ikke gjenopptakelse av brutt tilkobling. De samme filene er også tilgjengelige på <a %(a_ftp)s>en FTP-server</a>, som fungerer bedre. Sakprosa ser også ut til å ha divergerte, men utan nye torrentar. Det ser ut til at dette har skjedd sidan tidleg i 2022, sjølv om vi ikkje har verifisert dette. Ifølge administratoren av Libgen.li, bør "fiction_rus" (russisk fiksjon) samlingen dekkes av regelmessig utgitte torrenter fra <a %(a_booktracker)s>booktracker.org</a>, spesielt <a %(a_flibusta)s>flibusta</a> og <a %(a_librusec)s>lib.rus.ec</a> torrenter (som vi speiler <a %(a_torrents)s>her</a>, selv om vi ennå ikke har fastslått hvilke torrenter som tilsvarer hvilke filer). Fiksjonssamlingen har sine egne torrenter (forskjellig fra <a %(a_href)s>Libgen.rs</a>) som starter på %(start)s. Visse områder uten torrenter (som fiksjonsområdene f_3463000 til f_4260000) er sannsynligvis Z-Library (eller andre duplikat) filer, selv om vi kanskje vil gjøre noe deduplisering og lage torrenter for lgli-unike filer i disse områdene. Statistikk for alle samlinger kan finnes <a %(a_href)s>på libgens nettside</a>. Torrentar er tilgjengelege for det meste av det ekstra innhaldet, spesielt torrentar for teikneseriar, magasin og standarddokument har blitt utgitt i samarbeid med Annas Arkiv. Merk at torrentfilene som refererer til “libgen.is” eksplisitt er speil av <a %(a_libgen)s>Libgen.rs</a> (“.is” er et annet domene brukt av Libgen.rs). En nyttig ressurs for å bruke metadataene er <a %(a_href)s>denne siden</a>. %(icon)s Deira “fiction_rus”-samling (russisk fiksjon) har ingen dedikerte torrentar, men er dekka av torrentar frå andre, og vi held ein <a %(fiction_rus)s>spegel</a>. Russiske fiksjonstorrenter på Annas Arkiv Skjønnlitterære torrenter på Annas Arkiv Diskusjonsforum Metadata Metadata via FTP Magasin-torrenter på Annas Arkiv Informasjon om metadatafelt Speil av andre torrenter (og unike skjønnlitterære og tegneserie-torrenter) Standard dokumenttorrenter på Annas Arkiv Libgen.li Torrentar av Anna sitt Arkiv (bokomslag) Library Genesis er kjent for allereie å gjere dataene sine tilgjengelege i bulk gjennom torrentar. Vår Libgen-samling består av tilleggsdata som dei ikkje slepp direkte, i samarbeid med dei. Stor takk til alle involverte i Library Genesis for å samarbeide med oss! Vår blogg om utgjevinga av bokomslag Denne siden handler om “.rs”-versjonen. Den er kjent for konsekvent å publisere både metadataene og hele innholdet i bokkatalogen sin. Boksamlingen er delt mellom en skjønnlitterær og en ikke-skjønnlitterær del. En nyttig ressurs for å bruke metadataene er <a %(a_metadata)s>denne siden</a> (blokkerer IP-områder, VPN kan være nødvendig). Frå og med 2024-03 blir nye torrentar lagt ut i <a %(a_href)s>denne forumtråden</a> (blokkerer IP-område, VPN kan vere nødvendig). Skjønnlitteratur-torrentar på Anna sitt Arkiv Libgen.rs Skjønnlitteratur-torrentar Libgen.rs Diskusjonsforum Libgen.rs Metadata Libgen.rs metadata feltinformasjon Libgen.rs Sakprosa-torrentar Sakprosa-torrentar på Anna sitt Arkiv %(example)s for ei skjønnlitterær bok. Denne <a %(blog_post)s>første utgjevinga</a> er ganske liten: om lag 300GB med bokomslag frå Libgen.rs-forken, både skjønnlitteratur og sakprosa. Dei er organisert på same måte som dei vises på libgen.rs, f.eks.: %(example)s for ei sakprosabok. Akkurat som med Z-Library-samlinga, har vi lagt dei alle i ei stor .tar-fil, som kan monterast ved bruk av <a %(a_ratarmount)s>ratarmount</a> om du vil servere filene direkte. Utgjeving 1 (%(date)s) Den korte historien om de forskjellige Library Genesis (eller “Libgen”) forgreningene, er at over tid, hadde de forskjellige personene involvert i Library Genesis en uenighet, og gikk hver sin vei. Ifølge dette <a %(a_mhut)s>foruminnlegget</a> var Libgen.li opphavleg hosta på “http://free-books.dontexist.com”. “.fun”-versjonen ble opprettet av den opprinnelige grunnleggeren. Den blir fornyet til fordel for en ny, mer distribuert versjon. <a %(a_li)s>“.li”-versjonen</a> har en massiv samling av tegneserier, samt annet innhold, som ikke (ennå) er tilgjengelig for bulk-nedlasting gjennom torrenter. Den har en egen torrent-samling av skjønnlitterære bøker, og den inneholder metadataene til <a %(a_scihub)s>Sci-Hub</a> i databasen sin. “.rs”-versjonen har svært like data, og utgir mest konsekvent samlingen sin i bulk-torrenter. Den er grovt delt inn i en “skjønnlitterær” og en “ikke-skjønnlitterær” seksjon. Opphavleg på “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> er på en måte også en forgrening av Library Genesis, selv om de brukte et annet navn for prosjektet sitt. Libgen.rs Vi berikar også samlinga vår med metadata-kjelder, som vi kan matche til filer, til dømes ved å bruke ISBN-nummer eller andre felt. Nedanfor er ei oversikt over desse. Nokre av desse kjeldene er heilt opne, medan andre må vi skrape. Merk at i metadata-søk viser vi dei originale postane. Vi gjer ingen samanslåing av postar. Metadata-kjelder Open Library er eit open source-prosjekt av Internet Archive for å katalogisere alle bøker i verda. Det har ein av verdas største bokskanningsoperasjonar, og har mange bøker tilgjengelege for digital utlån. Metadata-katalogen for bøker er fritt tilgjengeleg for nedlasting, og er inkludert på Anna sitt Arkiv (men ikkje for tida i søk, bortsett frå om du eksplisitt søker etter ein Open Library ID). Open Library Ekskludert duplikater Sist oppdatert Prosentdelar av talet på filer %% spegla av AA / torrentar tilgjengelege Storleik Kjelde Nedanfor er ein rask oversikt over kjeldene til filene på Anna sitt Arkiv. Siden skyggelibraryene ofte synkroniserer data fra hverandre, er det betydelig overlapping mellom bibliotekene. Derfor stemmer ikke tallene overens med totalen. Prosentandelen “speilet og sådd av Anna’s Arkiv” viser hvor mange filer vi speiler selv. Vi sår disse filene i bulk gjennom torrenter, og gjør dem tilgjengelige for direkte nedlasting gjennom partnernettsteder. Oversikt Totalt Torrents på Anna sitt Arkiv For ein bakgrunn om Sci-Hub, vennligst sjå på <a %(a_scihub)s>den offisielle nettsida</a>, <a %(a_wikipedia)s>Wikipedia-sida</a>, og denne <a %(a_radiolab)s>podkastintervjuet</a>. Merk at Sci-Hub har vore <a %(a_reddit)s>frosen sidan 2021</a>. Det var frosen før, men i 2021 vart nokre millionar artiklar lagt til. Framleis blir eit avgrensa tal artiklar lagt til i Libgen “scimag”-samlingane, men ikkje nok til å rettferdiggjere nye bulk-torrents. Vi bruker Sci-Hub metadata som levert av <a %(a_libgen_li)s>Libgen.li</a> i “scimag”-samlinga. Vi bruker også <a %(a_dois)s>dois-2022-02-12.7z</a>-datasettet. Merk at “smarch”-torrents er <a %(a_smarch)s>utgått</a> og derfor ikkje inkludert i vår torrents-liste. Torrents på Libgen.li Torrents på Libgen.rs Metadata og torrents Oppdateringar på Reddit Podkastintervju Wikipedia-side Sci-Hub Sci-Hub: frosen sidan 2021; mest tilgjengeleg gjennom torrentar Libgen.li: mindre tillegg sidan då</div> Nokre kjeldesamlingar fremjar deling av dataene sine gjennom torrentar, medan andre ikkje deler samlinga si så lett. I sistnemnde tilfelle prøver Anna sitt Arkiv å skrape samlingane deira og gjere dei tilgjengelege (sjå vår <a %(a_torrents)s>Torrents</a>-side). Det finst også mellomting, til dømes der kjeldesamlingar er villige til å dele, men ikkje har ressursane til det. I slike tilfelle prøver vi også å hjelpe til. Nedanfor er ei oversikt over korleis vi interagerer med dei ulike kjeldesamlingane. Kildelibraryer %(icon)s Ulike fil-databasar spreidde rundt på det kinesiske internettet; sjølv om dei ofte er betaldatabasar %(icon)s Dei fleste filene er berre tilgjengelege med premium BaiduYun-kontoar; treige nedlastingshastigheiter. %(icon)s Annas Arkiv administrerer ein samling av <a %(duxiu)s>DuXiu filer</a> %(icon)s Ulike metadata-databasar spreidde rundt på det kinesiske internettet; sjølv om dei ofte er betaldatabasar %(icon)s Ingen lett tilgjengelege metadatauttrekk for heile samlinga deira. %(icon)s Annas Arkiv administrerer ei samling av <a %(duxiu)s>DuXiu-metadata</a> Filer %(icon)s Filer berre tilgjengelege for utlån på ein avgrensa basis, med ulike tilgangsrestriksjonar %(icon)s Annas Arkiv administrerer ein samling av <a %(ia)s>IA filer</a> %(icon)s Nokre metadata er tilgjengelege gjennom <a %(openlib)s>Open Library databaseuttrekk</a>, men dei dekkjer ikkje heile IA-samlinga. %(icon)s Ingen lett tilgjengelege metadata-dump tilgjengeleg for heile samlinga deira %(icon)s Annas Arkiv administrerer ein samling av <a %(ia)s>IA metadata</a> Sist oppdatert %(icon)s Annas Arkiv og Libgen.li administrerer i fellesskap samlingar av <a %(comics)s>teikneseriar</a>, <a %(magazines)s>magasin</a>, <a %(standarts)s>standarddokument</a>, og <a %(fiction)s>fiksjon (skilt frå Libgen.rs)</a>. %(icon)s Sakprosa-torrentar blir delt med Libgen.rs (og spegla <a %(libgenli)s>her</a>). %(icon)s Kvartalsvise <a %(dbdumps)s>HTTP-databaseuttrekk</a> %(icon)s Automatiserte torrentar for <a %(nonfiction)s>sakprosa</a> og <a %(fiction)s>fiksjon</a> %(icon)s Annas Arkiv administrerer ei samling av <a %(covers)s>bokomslagstorrentar</a> %(icon)s Daglege <a %(dbdumps)s>HTTP-database-dumpar</a> Metadata %(icon)s Månadlege <a %(dbdumps)s>database-dump</a> %(icon)s Data-torrents tilgjengeleg <a %(scihub1)s>her</a>, <a %(scihub2)s>her</a>, og <a %(libgenli)s>her</a> %(icon)s Nokre nye filer blir <a %(libgenrs)s>lagt til</a> i Libgen sin "scimag", men ikkje nok til å rettferdiggjere nye torrentar. %(icon)s Sci-Hub har fryse nye filer sidan 2021. %(icon)s Metadata-dump tilgjengeleg <a %(scihub1)s>her</a> og <a %(scihub2)s>her</a>, samt som del av <a %(libgenli)s>Libgen.li-databasen</a> (som vi brukar) Kjelde %(icon)s Ulike mindre eller eingongskjelder. Vi oppmodar folk til å laste opp til andre skuggebibliotek først, men av og til har folk samlingar som er for store til at andre kan sortere gjennom dei, men ikkje store nok til å rettferdiggjere sin eigen kategori. %(icon)s Ikkje tilgjengeleg direkte i bulk, verna mot scraping %(icon)s Annas Arkiv administrerer ein samling av <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Annas Arkiv og Z-Library samarbeider om å administrere ein samling av <a %(metadata)s>Z-Library metadata</a> og <a %(files)s>Z-Library filer</a> Datasets Vi kombinerer alle dei ovanfor nemnde kjeldene til ein samla database som vi brukar til å tene denne nettsida. Denne samla databasen er ikkje tilgjengeleg direkte, men sidan Anna sitt Arkiv er heilt open kjeldekode, kan den ganske enkelt <a %(a_generated)s>genererast</a> eller <a %(a_downloaded)s>lastast ned</a> som ElasticSearch- og MariaDB-databasar. Skripta på den sida vil automatisk laste ned all nødvendig metadata frå dei nemnde kjeldene. Om du vil utforske dataene våre før du køyrer desse skripta lokalt, kan du sjå på JSON-filene våre, som lenker vidare til andre JSON-filer. <a %(a_json)s>Denne fila</a> er ein god start. Samla database Torrentar av Annas Arkiv bla gjennom søk Ulike mindre eller eingongskjelder. Vi oppmodar folk til å laste opp til andre skuggebibliotek først, men av og til har folk samlingar som er for store for andre å sortere gjennom, men ikkje store nok til å fortene sin eigen kategori. Oversikt frå <a %(a1)s>Datasets-sida</a>. Frå <a %(a_href)s>aaaaarg.fail</a>. Verkar å vere ganske komplett. Frå vår frivillige "cgiym". Frå ein <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ganske høg overlapp med eksisterande artikkelsamlingar, men svært få MD5-treff, så vi bestemte oss for å behalde den fullstendig. Skraping av <q>iRead eBooks</q> (= fonetisk <q>ai rit i-books</q>; airitibooks.com), av frivillig <q>j</q>. Tilsvarer <q>airitibooks</q> metadata i <a %(a1)s><q>Andre metadata-skrapingar</q></a>. Frå ei samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvis frå den originale kjelda, delvis frå the-eye.eu, delvis frå andre speglar. Frå ein privat boktorrent-nettstad, <a %(a_href)s>Bibliotik</a> (ofte referert til som "Bib"), der bøker vart samla i torrentar etter namn (A.torrent, B.torrent) og distribuert gjennom the-eye.eu. Frå vår frivillige “bpb9v”. For meir informasjon om <a %(a_href)s>CADAL</a>, sjå notatane på vår <a %(a_duxiu)s>DuXiu datasett-side</a>. Meir frå vår frivillige “bpb9v”, mest DuXiu-filer, samt ei mappe “WenQu” og “SuperStar_Journals” (SuperStar er selskapet bak DuXiu). Frå vår frivillige “cgiym”, kinesiske tekstar frå ulike kjelder (representert som underkatalogar), inkludert frå <a %(a_href)s>China Machine Press</a> (ein stor kinesisk forleggar). Ikkje-kinesiske samlingar (representert som underkatalogar) frå vår frivillige "cgiym". Skraping av bøker om kinesisk arkitektur, av frivillig <q>cm</q>: <q>Eg fekk tak i det ved å utnytte ein nettverkssårbarheit hos forlaget, men det smuttholet er no lukka</q>. Tilsvarer <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andre metadata-skrapingar</q></a>. Bøker frå akademisk forlag <a %(a_href)s>De Gruyter</a>, samla frå nokre store torrentar. Skraping av <a %(a_href)s>docer.pl</a>, ei polsk fildeleingsside med fokus på bøker og andre skriftlege verk. Skrapa seint i 2023 av frivillig “p”. Vi har ikkje gode metadata frå den originale nettsida (ikkje ein gong filendingar), men vi filtrerte for bokliknande filer og klarte ofte å trekke ut metadata frå filene sjølve. DuXiu-epubs, direkte frå DuXiu, samla av frivillig “w”. Berre nyare DuXiu-bøker er tilgjengelege direkte gjennom ebøker, så dei fleste av desse må vere nyare. Resterande DuXiu-filer frå frivillig “m”, som ikkje var i DuXiu sitt eigne PDG-format (det viktigaste <a %(a_href)s>DuXiu datasettet</a>). Samla frå mange originale kjelder, dessverre utan å bevare desse kjeldene i filstien. <span></span> <span></span> <span></span> Skraping av erotiske bøker, av frivillig <q>do no harm</q>. Tilsvarer <q>hentai</q> metadata i <a %(a1)s><q>Andre metadata-skrapingar</q></a>. <span></span> <span></span> Samling skrapa frå ein japansk mangaforleggar av frivillig “t”. <a %(a_href)s>Utvalde rettsarkiv frå Longquan</a>, levert av frivillig “c”. Scrape av <a %(a_href)s>magzdb.org</a>, ein alliert av Library Genesis (det er lenka på libgen.rs heimesida) men som ikkje ville gi filene sine direkte. Skaffa av frivillig "p" seint i 2023. <span></span> Ulike små opplastingar, for små til å vere sin eigen underkolleksjon, men representert som katalogar. E-bøker frå AvaxHome, ei russisk fildelingsside. Arkiv av aviser og magasin. Tilsvarer <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andre metadata-skrapingar</q></a>. Skraping av <a %(a1)s>Philosophy Documentation Center</a>. Samling av frivillig "o" som samla polske bøker direkte frå originale utgivingsnettstader ("scene"). Kombinerte samlingar av <a %(a_href)s>shuge.org</a> av frivillige «cgiym» og «woz9ts». <span></span> <a %(a_href)s>«Imperial Library of Trantor»</a> (namngitt etter det fiktive biblioteket), skrapa i 2022 av frivillig «t». <span></span> <span></span> <span></span> Under-under-samlingar (representert som katalogar) frå frivillig "woz9ts": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (av <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mitt lille bokrom — woz9ts: "Denne sida fokuserer hovudsakleg på å dele høgkvalitets e-bokfiler, nokre av dei er satt opp av eigaren sjølv. Eigaren vart <a %(a_arrested)s>arrestert</a> i 2019 og nokon laga ei samling av filene han delte."). Gjenverande DuXiu-filer frå frivillig "woz9ts", som ikkje var i DuXiu proprietære PDG-format (skal framleis konverterast til PDF). "Opplasting"-samlinga er delt opp i mindre undersamlingar, som er indikert i AACID-ane og torrent-namna. Alle undersamlingar vart først deduplisert mot hovudsamlinga, sjølv om metadata "upload_records" JSON-filene framleis inneheld mange referansar til dei originale filene. Ikkje-bokfiler vart også fjerna frå dei fleste undersamlingane, og er typisk <em>ikkje</em> notert i "upload_records" JSON. Undersamlingane er: Notat Underkolleksjon Mange undersamlingar består sjølv av under-under-samlingar (f.eks. frå ulike originale kjelder), som er representert som katalogar i "filepath"-felta. Opplastingar til Annas Arkiv Vår bloggpost om desse dataene <a %(a_worldcat)s>WorldCat</a> er ein eigedomsbeskytta database av den ideelle organisasjonen <a %(a_oclc)s>OCLC</a>, som samlar metadataoppføringar frå bibliotek over heile verda. Det er truleg den største bibliotekmetadata-samlinga i verda. I oktober 2023 <a %(a_scrape)s>slapp</a> vi ein omfattande skraping av OCLC (WorldCat)-databasen, i <a %(a_aac)s>Anna sitt Arkiv Containers-format</a>. Oktober 2023, første utgåve: OCLC (WorldCat) Torrents av Anna sitt Arkiv Døme på oppføring i Anna sitt Arkiv (original samling) Døme på oppføring i Anna sitt Arkiv (“zlib3” samling) Torrents av Anna sitt Arkiv (metadata + innhald) Blogginnlegg om Utgåve 1 Blogginnlegg om Utgåve 2 Seint i 2022 vart dei påståtte grunnleggjarane av Z-Library arresterte, og domena vart beslaglagt av amerikanske styresmakter. Sidan då har nettsida sakte kome tilbake på nettet. Det er ukjent kven som driv den no. Oppdatering per februar 2023. Z-Library har sine røter i <a %(a_href)s>Library Genesis</a>-samfunnet, og starta opp med deira data. Sidan då har det blitt profesjonalisert betydeleg, og har eit mykje meir moderne grensesnitt. Dei er derfor i stand til å få mange fleire donasjonar, både økonomisk for å halde fram med å forbetre nettsida, samt donasjonar av nye bøker. Dei har samla ein stor samling i tillegg til Library Genesis. Samlinga består av tre delar. Dei originale beskrivingssidene for dei to første delane er bevart nedanfor. Du treng alle tre delane for å få all data (bortsett frå erstatta torrents, som er kryssa ut på torrents-sida). %(title)s: vår første utgåve. Dette var den aller første utgåva av det som då vart kalla “Pirate Library Mirror” (“pilimi”). %(title)s: andre utgåve, denne gongen med alle filer pakka i .tar-filer. %(title)s: inkrementelle nye utgåver, ved bruk av <a %(a_href)s>Anna sitt Arkiv Containers (AAC) format</a>, no utgitt i samarbeid med Z-Library-teamet. Den første spegelen vart møysommeleg henta i løpet av 2021 og 2022. På dette tidspunktet er den litt utdatert: den reflekterer tilstanden til samlinga i juni 2021. Vi vil oppdatere dette i framtida. Akkurat no er vi fokuserte på å få ut denne første utgåva. Sidan Library Genesis allereie er bevart med offentlege torrentar, og er inkludert i Z-Library, gjorde vi ei grunnleggjande deduplisering mot Library Genesis i juni 2022. For dette brukte vi MD5-hashar. Det er sannsynlegvis mykje meir duplisert innhald i biblioteket, som fleire filformat med same bok. Dette er vanskeleg å oppdage nøyaktig, så vi gjer det ikkje. Etter dedupliseringa sit vi igjen med over 2 millionar filer, totalt litt under 7TB. Samlinga består av to delar: ein MySQL “.sql.gz” dump av metadataen, og dei 72 torrentfilene på rundt 50-100GB kvar. Metadataen inneheld data som rapportert av Z-Library-nettstaden (tittel, forfattar, skildring, filtype), samt den faktiske filstorleiken og md5sum som vi observerte, sidan desse av og til ikkje stemmer overeins. Det ser ut til å vere rekkjer av filer der Z-Library sjølv har feil metadata. Vi kan også ha lasta ned filer feil i nokre isolerte tilfelle, som vi vil prøve å oppdage og rette i framtida. Dei store torrentfilene inneheld sjølve bokdataene, med Z-Library ID som filnamn. Filendingane kan rekonstruerast ved hjelp av metadata-dumpen. Samlinga er ein miks av sakprosa og skjønnlitteratur (ikkje skilt ut som i Library Genesis). Kvaliteten varierer også mykje. Denne første utgåva er no fullt tilgjengeleg. Merk at torrentfilene berre er tilgjengelege gjennom vår Tor-spegel. Utgåve 1 (%(date)s) Dette er ei enkelt ekstra torrentfil. Den inneheld ikkje ny informasjon, men den har nokre data i seg som kan ta tid å rekne ut. Det gjer det praktisk å ha, sidan nedlasting av denne torrenten ofte er raskare enn å rekne det ut frå grunnen av. Spesielt inneheld den SQLite-indeksar for tar-filene, for bruk med <a %(a_href)s>ratarmount</a>. Utgåve 2 tillegg (%(date)s) Vi har fått tak i alle bøkene som vart lagt til Z-Library mellom vår siste spegel og august 2022. Vi har også gått tilbake og skrapa nokre bøker som vi gjekk glipp av første gongen. Alt i alt er denne nye samlinga på om lag 24TB. Igjen er denne samlinga deduplisert mot Library Genesis, sidan det allereie finst torrentar tilgjengelege for den samlinga. Dataene er organisert på liknande måte som i den første utgåva. Det er ein MySQL “.sql.gz” dump av metadataen, som også inkluderer all metadata frå den første utgåva, og dermed erstattar den. Vi har også lagt til nokre nye kolonnar: Vi nemnde dette sist, men berre for å klargjere: “filename” og “md5” er dei faktiske eigenskapane til fila, medan “filename_reported” og “md5_reported” er det vi skrapa frå Z-Library. Av og til stemmer ikkje desse to overeins, så vi inkluderte begge. For denne utgåva endra vi kollasjonen til “utf8mb4_unicode_ci”, som skal vere kompatibel med eldre versjonar av MySQL. Datafilene er liknande som sist, sjølv om dei er mykje større. Vi orka rett og slett ikkje å lage mange mindre torrentfiler. “pilimi-zlib2-0-14679999-extra.torrent” inneheld alle filene vi gjekk glipp av i den siste utgåva, medan dei andre torrentane er alle nye ID-rekkjer.  <strong>Oppdatering %(date)s:</strong> Vi gjorde dei fleste av torrentane våre for store, noko som fekk torrentklientar til å slite. Vi har fjerna dei og gitt ut nye torrentar. <strong>Oppdatering %(date)s:</strong> Det var framleis for mange filer, så vi pakka dei inn i tar-filer og gav ut nye torrentar igjen. %(key)s: om denne fila allereie er i Library Genesis, anten i sakprosa- eller skjønnlitteratursamlinga (matchet ved md5). %(key)s: kva torrent denne fila er i. %(key)s: sett når vi ikkje klarte å laste ned boka. Utgåve 2 (%(date)s) Zlib-utgåver (originale beskrivingar) Tor-domene Hovudnettstad Z-Library skraping Den “kinesiske” samlinga i Z-Library ser ut til å vere den same som vår DuXiu-samling, men med ulike MD5-ar. Vi ekskluderer desse filene frå torrentar for å unngå duplisering, men viser dei framleis i søkjeindeksen vår. Metadata Du får %(percentage)s%% bonus raske nedlastingar, fordi du vart verva av brukaren %(profile_link)s. Dette gjeld for heile medlemsperioden. Doner Bli med Vald opptil %(percentage)s%% rabattar Alipay støttar internasjonale kreditt-/debetkort. Sjå <a %(a_alipay)s>denne guiden</a> for meir informasjon. Send oss Amazon.com-gåvekort ved å bruke kreditt-/debetkortet ditt. Du kan kjøpe krypto ved å bruke kreditt-/debetkort. WeChat (Weixin Pay) støttar internasjonale kreditt-/debetkort. I WeChat-appen, gå til “Me => Services => Wallet => Add a Card”. Om du ikkje ser det, aktiver det ved å bruke “Me => Settings => General => Tools => Weixin Pay => Enable”. (bruk når du sender Ethereum frå Coinbase) kopiert! kopier (lågaste minimumsbeløp) (åtvaring: høgt minimumsbeløp) -%(percentage)s%% 12 månader 1 månad 24 månader 3 månader 48 månader 6 månader 96 månader Vel kor lenge du vil abonnere. <div %(div_monthly_cost)s></div><div %(div_after)s>etter <span %(span_discount)s></span> rabattar</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% for 12 månader for 1 månad for 24 månader for 3 månader for 48 månader for 6 månader for 96 månader %(monthly_cost)s / månad kontakt oss Direkte <strong>SFTP</strong> servere Donasjon på bedriftsnivå eller bytte for nye samlinger (f.eks. nye skanninger, OCR’ede datasets). Ekspert-tilgang <strong>Ubegrenset</strong> høyhastighetstilgang <div %(div_question)s>Kan jeg oppgradere medlemskapet mitt eller få flere medlemskap?</div> <div %(div_question)s>Kan jeg gi en donasjon uten å bli medlem?</div> Selvfølgelig. Vi aksepterer donasjoner av alle beløp på denne Monero (XMR)-adressen: %(address)s. <div %(div_question)s>Kva betyr intervalla per månad?</div> Du kan kome til den lågare sida av eit intervall ved å bruke alle rabattar, som å velje ein periode lengre enn ein månad. <div %(div_question)s>Fornyes medlemskap automatisk?</div> Medlemskap <strong>fornyes ikke</strong> automatisk. Du kan bli medlem så lenge eller kort som du vil. <div %(div_question)s>Hva bruker dere donasjoner på?</div> 100%% går til å bevare og gjøre verdens kunnskap og kultur tilgjengelig. For øyeblikket bruker vi det mest på servere, lagring og båndbredde. Ingen penger går til noen teammedlemmer personlig. <div %(div_question)s>Kan jeg gi en stor donasjon?</div> Det ville vært fantastisk! For donasjoner over noen tusen dollar, vennligst kontakt oss direkte på %(email)s. <div %(div_question)s>Har dere andre betalingsmetoder?</div> Foreløpig ikke. Mange ønsker ikke at arkiver som dette skal eksistere, så vi må være forsiktige. Hvis du kan hjelpe oss med å sette opp andre (mer praktiske) betalingsmetoder trygt, vennligst ta kontakt på %(email)s. Donasjons-FAQ Du har ein <a %(a_donation)s>pågåande donasjon</a>. Fullfør eller avbryt denne donasjonen før du lagar ein ny donasjon. <a %(a_all_donations)s>Vis alle mine donasjonar</a> For donasjoner over $5000 vennligst kontakt oss direkte på %(email)s. Vi tar imot store donasjoner fra velstående individer eller institusjoner.  Ver merksam på at medan medlemskapa på denne sida er «per månad», er dei eingongsdonasjonar (ikkje gjentakande). Sjå <a %(faq)s>Donasjons-FAQ</a>. Anna sitt Arkiv er eit ideelt, open-kjeldekode, open-data prosjekt. Ved å donere og bli medlem, støttar du vår drift og utvikling. Til alle våre medlemmar: takk for at de held oss i gang! ❤️ For meir informasjon, sjå <a %(a_donate)s>Donasjons-FAQ</a>. For å bli medlem, ver venleg <a %(a_login)s>Logg inn eller Registrer deg</a>. Takk for støtta! $%(cost)s / månad Hvis du gjorde en feil under betalingen, kan vi ikke gi refusjon, men vi vil prøve å rette det opp. Finn "Krypto"-siden i PayPal-appen eller på nettstedet. Dette er vanligvis under "Finanser". Gå til "Bitcoin"-siden i PayPal-appen eller på nettstedet. Trykk på "Overfør"-knappen %(transfer_icon)s, og deretter "Send". Alipay Alipay 支付宝 / WeChat 微信 Amazon gavekort %(amazon)s gåvekort Bankkort Bankkort (ved bruk av app) Binance Kreditt/debet/Apple/Google (BMC) Cash App Kreditt-/debetkort Kreditt-/debetkort 2 Kreditt-/debetkort (reserve) Krypto %(bitcoin_icon)s Kort / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (vanleg) Pix (Brazil) Revolut (midlertidig utilgjengeleg) WeChat Vel din føretrekte kryptovaluta: Doner ved å bruke eit Amazon-gåvekort. <strong>VIKTIG:</strong> Dette alternativet er for %(amazon)s. Om du vil bruke ein annan Amazon-nettside, vel den over. <strong>VIKTIG:</strong> Vi støttar berre Amazon.com, ikkje andre Amazon-nettstader. For eksempel, .de, .co.uk, .ca, er IKKJE støtta. Ver venleg og IKKJE skriv din eigen melding. Skriv inn nøyaktig beløp: %(amount)s Merk at vi må runde av til beløp som blir akseptert av våre forhandlarar (minimum %(minimum)s). Doner ved å bruke eit kreditt-/debetkort, gjennom Alipay-appen (superenkel å sette opp). Installer Alipay-appen frå <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>. Registrer deg med telefonnummeret ditt. Ingen ytterlegare personlege opplysningar er nødvendige. <span %(style)s>1</span>Installer Alipay-appen Støtta: Visa, MasterCard, JCB, Diners Club og Discover. Sjå <a %(a_alipay)s>denne guiden</a> for meir informasjon. <span %(style)s>2</span>Legg til bankkort Med Binance kan du kjøpe Bitcoin med eit kreditt-/debetkort eller bankkonto, og deretter donere den Bitcoinen til oss. På den måten kan vi forbli sikre og anonyme når vi tek imot donasjonen din. Binance er tilgjengeleg i nesten alle land, og støttar dei fleste bankar og kreditt-/debetkort. Dette er for tida vår hovudanbefaling. Vi set pris på at du tek deg tid til å lære korleis du kan donere ved å bruke denne metoden, sidan det hjelper oss mykje. For kredittkort, debetkort, Apple Pay og Google Pay, bruker vi “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). I deira system er ein “kaffi” lik $5, så donasjonen din vil bli avrunda til næraste multiplum av 5. Doner ved å bruke Cash App. Dersom du har Cash App, er dette den enklaste måten å donere på! Merk at for transaksjonar under %(amount)s, kan Cash App ta eit %(fee)s gebyr. For %(amount)s eller meir, er det gratis! Doner med kreditt- eller debetkort. Denne metoden bruker ein kryptovalutaleverandør som ein mellomledd for konvertering. Dette kan vere litt forvirrande, så bruk berre denne metoden dersom andre betalingsmetodar ikkje fungerer. Det fungerer heller ikkje i alle land. Vi kan ikkje støtte kreditt-/debetkort direkte, fordi bankane ikkje vil samarbeide med oss. ☹ Men det finst fleire måtar å bruke kreditt-/debetkort likevel, ved å bruke andre betalingsmetodar: Med krypto kan du donere ved å bruke BTC, ETH, XMR og SOL. Bruk dette alternativet dersom du allereie er kjent med kryptovaluta. Med krypto kan du donere ved å bruke BTC, ETH, XMR og meir. Krypto ekspresstenester Om du bruker krypto for første gong, foreslår vi å bruke %(options)s for å kjøpe og donere Bitcoin (den originale og mest brukte kryptovalutaen). Merk at for små donasjonar kan kredittkortgebyra eliminere vår %(discount)s%% rabatt, så vi anbefaler lengre abonnement. Doner ved å bruke kreditt-/debetkort, PayPal eller Venmo. Du kan velje mellom desse på neste side. Google Pay og Apple Pay kan også fungere. Merk at for små donasjonar er gebyra høge, så vi anbefaler lengre abonnement. For å donere ved å bruke PayPal US, skal vi bruke PayPal Crypto, som lar oss vere anonyme. Vi set pris på at du tek deg tid til å lære korleis du kan donere ved å bruke denne metoden, sidan det hjelper oss mykje. Doner ved å bruke PayPal. Doner med din vanlege PayPal-konto. Doner med Revolut. Om du har Revolut, er dette den enklaste måten å donere på! Denne betalingsmetoden tillèt berre eit maksimum av %(amount)s. Ver venleg å velje ein annan varigheit eller betalingsmetode. Denne betalingsmetoden krev eit minimum av %(amount)s. Ver venleg å velje ein annan varigheit eller betalingsmetode. Binance Coinbase Kraken Vennligst velg en betalingsmetode. “Adopter ein torrent”: brukarnamnet ditt eller melding i ein torrent-filnamn <div %(div_months)s>ein gong kvart 12. medlemsmånad</div> Brukarnamnet ditt eller anonym omtale i krediteringa Tidleg tilgang til nye funksjonar Eksklusiv Telegram med oppdateringar bak kulissane %(number)s raske nedlastingar per dag hvis du donerer denne månaden! <a %(a_api)s>JSON API</a> tilgang Legendarisk status i bevaring av menneskehetens kunnskap og kultur Tidlegare fordelar, pluss: Tjen <strong>%(percentage)s%% bonus nedlastingar</strong> ved å <a %(a_refer)s>verve venner</a>. SciDB artiklar <strong>ubegrensa</strong> utan verifisering Når du spør om konto- eller donasjonsspørsmål, legg ved kontoid, skjermbilete, kvitteringar, så mykje informasjon som mogleg. Vi sjekkar berre e-posten vår kvar 1-2 veker, så å ikkje inkludere denne informasjonen vil forseinke ei løysing. For å få endå fleire nedlastingar, <a %(a_refer)s>verv vennene dine</a>! Vi er et lite team av frivillige. Det kan ta oss 1-2 uker å svare. Merk at kontonamnet eller biletet kan sjå rart ut. Ingen grunn til bekymring! Desse kontoane blir administrerte av våre donasjonspartnarar. Kontoane våre har ikkje blitt hacka. Doner <span %(span_cost)s></span> <span %(span_label)s></span> for 12 månader “%(tier_name)s” for 1 månad “%(tier_name)s” for 24 månader “%(tier_name)s” for 3 månader “%(tier_name)s” for 48 månader “%(tier_name)s” for 6 månader “%(tier_name)s” for 96 månader “%(tier_name)s” Du kan framleis avbryte donasjonen under utsjekkinga. Klikk på donasjonsknappen for å stadfeste denne donasjonen. <strong>Viktig merknad:</strong> Kryptopriser kan svinge voldsomt, noen ganger så mye som 20%% på få minutter. Dette er fortsatt mindre enn gebyrene vi pådrar oss med mange betalingsleverandører, som ofte tar 50-60%% for å jobbe med en "skyggeveldedighet" som oss. <u>Hvis du sender oss kvitteringen med den opprinnelige prisen du betalte, vil vi fortsatt kreditere kontoen din for det valgte medlemskapet</u> (så lenge kvitteringen ikke er eldre enn noen timer). Vi setter virkelig pris på at du er villig til å tåle slike ting for å støtte oss! ❤️ ❌ Noko gjekk gale. Ver venleg å last sida på nytt og prøv igjen. <span %(span_circle)s>1</span>Kjøp Bitcoin på Paypal <span %(span_circle)s>2</span>Overfør Bitcoin til vår adresse ✅ Omdirigerer til donasjonssida… Doner Vent minst <span %(span_hours)s>24 timar</span> (og oppdater denne sida) før du kontaktar oss. Hvis du ønsker å gi en donasjon (hvilket som helst beløp) uten medlemskap, kan du bruke denne Monero (XMR) adressen: %(address)s. Etter å ha sendt gåvekortet ditt, vil vårt automatiserte system bekrefte det innan nokre minutt. Om dette ikkje fungerer, prøv å sende gåvekortet på nytt (<a %(a_instr)s>instruksjonar</a>). Om det framleis ikkje fungerer, ver venleg og send oss ein e-post, så vil Anna manuelt sjekke det (dette kan ta nokre dagar), og ver sikker på å nemne om du har prøvd å sende på nytt allereie. Døme: Ver venleg og bruk <a %(a_form)s>det offisielle Amazon.com-skjemaet</a> for å sende oss eit gåvekort på %(amount)s til e-postadressa nedanfor. «Til» mottakar-e-post i skjemaet: Amazon-gåvekort Vi kan ikkje akseptere andre metodar for gåvekort, <strong>berre sendt direkte frå det offisielle skjemaet på Amazon.com</strong>. Vi kan ikkje returnere gåvekortet ditt om du ikkje brukar dette skjemaet. Bruk berre ein gong. Unik for kontoen din, ikkje del. Vent på gåvekortet… (oppdater sida for å sjekke) Opne <a %(a_href)s>QR-kode donasjonssida</a>. Skann QR-koden med Alipay-appen, eller trykk på knappen for å opne Alipay-appen. Ver tolmodig; sida kan ta litt tid å laste sidan den er i Kina. <span %(style)s>3</span>Gjer donasjon (skann QR-kode eller trykk på knappen) Kjøp PYUSD-mynt på PayPal Kjøp Bitcoin (BTC) på Cash App Kjøp litt meir (me anbefaler %(more)s meir) enn beløpet du donerer (%(amount)s), for å dekke transaksjonsgebyr. Du vil behalde det som er igjen. Gå til “Bitcoin” (BTC)-sida i Cash App. Overfør Bitcoin til vår adresse For små donasjonar (under $25), kan det hende du må bruke Rush eller Priority. Klikk på “Send bitcoin”-knappen for å gjere eit “uttak”. Bytt frå dollar til BTC ved å trykke på %(icon)s-ikonet. Skriv inn BTC-beløpet nedanfor og klikk “Send”. Sjå <a %(help_video)s>denne videoen</a> om du står fast. Ekspresstenester er praktiske, men har høgare gebyr. Du kan bruke dette i staden for ein kryptobørs om du ønskjer å raskt gi ein større donasjon og ikkje har noko imot eit gebyr på $5-10. Ver sikker på å sende nøyaktig kryptobeløp som vist på donasjonssida, ikkje beløpet i $USD. Elles vil gebyret bli trekt frå, og vi kan ikkje automatisk behandle medlemskapet ditt. Av og til kan stadfesting ta opptil 24 timar, så ver sikker på å oppdatere denne sida (sjølv om ho har gått ut). Instruksjoner for kreditt- / debetkort Doner gjennom vår kreditt- / debetkortside Noen av trinnene nevner kryptolommebøker, men ikke bekymre deg, du trenger ikke å lære noe om krypto for dette. %(coin_name)s instruksjoner Skann denne QR -koden med Crypto Wallet -appen din for raskt å fylle ut betalingsdetaljene Skann QR -kode for å betale Vi støtter kun standardversjonen av kryptomynter, ingen eksotiske nettverk eller versjoner av mynter. Det kan ta opptil en time å bekrefte transaksjonen, avhengig av mynten. Doner %(amount)s på <a %(a_page)s>denne siden</a>. Denne donasjonen har utløpt. Vennligst kanseller og opprett en ny. Om du allereie har betalt: Ja, jeg har sendt kvitteringen min Hvis kryptovalutakursen svingte under transaksjonen, sørg for å inkludere kvitteringen som viser den opprinnelige kursen. Vi setter stor pris på at du tar bryet med å bruke krypto, det hjelper oss mye! ❌ Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. <span %(span_circle)s>%(circle_number)s</span>Send oss kvitteringen på e-post Hvis du støter på problemer, vennligst kontakt oss på %(email)s og inkluder så mye informasjon som mulig (for eksempel skjermbilder). ✅ Takk for donasjonen! Anna vil manuelt aktivere medlemskapet ditt innen noen dager. Send en kvittering eller skjermbilde til din personlige verifikasjonsadresse: Når du har sendt kvitteringen på e-post, klikk denne knappen, så kan Anna manuelt gjennomgå den (dette kan ta noen dager): Send ein kvittering eller skjermbilete til din personlege verifikasjonsadresse. IKKJE bruk denne e-postadressa for din PayPal-donasjon. Avbryt Ja, ver så snill å avbryt Er du sikker på at du vil avbryte? Ikkje avbryt om du allereie har betalt. ❌ Noe gikk galt. Vennligst last siden på nytt og prøv igjen. Gjør en ny donasjon ✅ Donasjonen din har blitt kansellert. Dato: %(date)s Identifikator: %(id)s Bestill på nytt Status: <span %(span_label)s>%(label)s</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad for %(duration)s månader, inkludert %(discounts)s%% rabatt)</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad for %(duration)s månader)</span> 1. Skriv inn e-posten din. 2. Velg betalingsmetode. 3. Velg betalingsmetode igjen. 4. Velg “Selvhostet” lommebok. 5. Klikk “Jeg bekrefter eierskap”. 6. Du skal motta en kvittering på e-post. Vennligst send den til oss, så bekrefter vi donasjonen din så snart som mulig. (du kan ønske å avbryte og opprette en ny donasjon) Betalingsinstruksjonene er nå utdaterte. Hvis du ønsker å gjøre en ny donasjon, bruk "Bestill på nytt"-knappen ovenfor. Du har allerede betalt. Hvis du vil se betalingsinstruksjonene likevel, klikk her: Vis gamle betalingsinstruksjoner Om donasjonssida blir blokkert, prøv ein annan internettforbindelse (f.eks. VPN eller mobilinternett). Dessverre er Alipay-sida ofte berre tilgjengeleg frå <strong>fastlands-Kina</strong>. Du kan måtte midlertidig deaktivere VPN-en din, eller bruke ein VPN til fastlands-Kina (eller Hong Kong fungerer også av og til). <span %(span_circle)s>1</span>Doner på Alipay Doner totalbeløpet på %(total)s ved å bruke <a %(a_account)s>denne Alipay-kontoen</a> Alipay-instruksjonar <span %(span_circle)s>1</span>Overfør til en av våre kryptokontoer Doner totalbeløpet av %(total)s til en av disse adressene: Kryptoinstruksjoner Følg instruksjonene for å kjøpe Bitcoin (BTC). Du trenger bare å kjøpe beløpet du ønsker å donere, %(total)s. Skriv inn vår Bitcoin (BTC)-adresse som mottaker, og følg instruksjonene for å sende din donasjon på %(total)s: <span %(span_circle)s>1</span>Doner på Pix Doner det totale beløpet på %(total)s ved å bruke <a %(a_account)s>denne Pix-kontoen Pix-instruksjonar <span %(span_circle)s>1</span>Doner på WeChat Doner totalbeløpet på %(total)s ved å bruke <a %(a_account)s>denne WeChat-kontoen</a> WeChat-instruksjonar Bruk ein av dei følgjande “kredittkort til Bitcoin”-ekspresstenestene, som berre tek nokre minutt: BTC / Bitcoin-adresse (ekstern lommebok): BTC / Bitcoin-beløp: Fyll ut følgjande detaljar i skjemaet: Om noko av denne informasjonen er utdatert, send oss ein e-post for å gi beskjed. Ver venleg å bruk denne <span %(underline)s>nøyaktige summen</span>. Den totale kostnaden din kan vere høgare på grunn av kredittkortgebyr. For små beløp kan dette dessverre vere meir enn rabatten vår. (minimum: %(minimum)s, ingen verifisering for første transaksjon) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, ingen verifisering for første transaksjon) (minimum: %(minimum)s) (minimum: %(minimum)s avhengig av land, ingen verifisering for første transaksjon) Følg instruksjonene for å kjøpe PYUSD-mynt (PayPal USD). Kjøp litt mer (vi anbefaler %(more)s mer) enn beløpet du donerer (%(amount)s), for å dekke transaksjonsgebyrer. Du beholder alt som er til overs. Gå til “PYUSD”-siden i PayPal-appen eller på nettstedet. Trykk på “Overfør”-knappen %(icon)s, og deretter “Send”. Oppdater status For å tilbakestille timeren, opprett ganske enkelt en ny donasjon. Pass på å bruke BTC-beløpet nedanfor, <em>IKKJE</em> euro eller dollar, elles vil vi ikkje motta korrekt beløp og kan ikkje automatisk bekrefte medlemskapet ditt. Kjøp Bitcoin (BTC) på Revolut Kjøp litt meir (me anbefaler %(more)s meir) enn beløpet du donerer (%(amount)s), for å dekke transaksjonsgebyr. Du vil behalde det som er igjen. Gå til “Crypto”-sida i Revolut for å kjøpe Bitcoin (BTC). Overfør Bitcoin til vår adresse For små donasjonar (under $25) kan det hende du må bruke Rush eller Priority. Klikk på “Send bitcoin”-knappen for å gjere eit “uttak”. Bytt frå euro til BTC ved å trykke på %(icon)s-ikonet. Skriv inn BTC-beløpet nedanfor og klikk “Send”. Sjå <a %(help_video)s>denne videoen</a> om du står fast. Status: 1 2 Trinn-for-trinn guide Se trinn-for-trinn-guiden nedenfor. Ellers kan du bli låst ute av denne kontoen! Hvis du ikke allerede har gjort det, skriv ned din hemmelige nøkkel for innlogging: Takk for donasjonen din! Tid igjen: Donasjon Overfør %(amount)s til %(account)s Venter på bekreftelse (oppdater siden for å sjekke)… Venter på overføring (oppdater siden for å sjekke)… Tidlegare Raske nedlastingar dei siste 24 timane tel mot den daglege grensa. Nedlastingar frå Fast Partner Servers er merka med %(icon)s. Siste 18 timar Ingen filer nedlasta enno. Nedlasta filer er ikkje offentleg viste. Alle tider er i UTC. Nedlasta filer Om du lasta ned ei fil med både raske og trege nedlastingar, vil ho visast to gonger. Ikkje ver for uroa, det er mange som lastar ned frå nettsidene vi lenkar til, og det er svært sjeldan å få problem. For å vere trygg anbefaler vi å bruke ein VPN (betalt), eller <a %(a_tor)s>Tor</a> (gratis). Eg lasta ned 1984 av George Orwell, kjem politiet på døra mi? Du er Anna! Kven er Anna? Vi har ein stabil JSON API for medlemmar, for å få ein rask nedlastings-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasjon innanfor JSON sjølv). For andre bruksområde, som å iterere gjennom alle filene våre, byggje tilpassa søk, og så vidare, anbefaler vi <a %(a_generate)s>å generere</a> eller <a %(a_download)s>laste ned</a> ElasticSearch- og MariaDB-databasane våre. Rådataene kan utforskast manuelt <a %(a_explore)s>gjennom JSON-filer</a>. Rå torrent-lista vår kan lastast ned som <a %(a_torrents)s>JSON</a> også. Har de ein API? Vi vert ikkje vert for noko opphavsrettsleg materiale her. Vi er ein søkemotor, og indekserer berre metadata som allereie er offentleg tilgjengeleg. Når du lastar ned frå desse eksterne kjeldene, tilrår vi å sjekke lovene i din jurisdiksjon med omsyn til kva som er tillate. Vi er ikkje ansvarlege for innhald vert av andre. Om du har klager på det du ser her, er det beste å kontakte den opphavlege nettsida. Vi hentar regelmessig endringane deira inn i databasen vår. Om du verkeleg meiner du har ein gyldig DMCA-klage vi bør svare på, ver venleg å fyll ut <a %(a_copyright)s>DMCA / Copyright-kravskjemaet</a>. Vi tek klagene dine på alvor, og vil svare deg så snart som mogleg. Korleis rapporterer eg brot på opphavsretten? Her er nokre bøker som har spesiell betydning for verda av skyggebibliotek og digital bevaring: Kva er favorittbøkene dine? Vi vil også minne alle om at all koden og dataene våre er heilt open source. Dette er unikt for prosjekt som vårt — vi kjenner ikkje til noko anna prosjekt med ein tilsvarande massiv katalog som også er heilt open source. Vi ønskjer veldig velkommen alle som meiner vi driv prosjektet vårt dårleg til å ta koden og dataene våre og setje opp sitt eige skyggebibliotek! Vi seier ikkje dette av vond vilje eller noko — vi meiner genuint at dette ville vere fantastisk sidan det ville heve standarden for alle, og betre bevare menneskeheita sitt arv. Eg hatar korleis de driv dette prosjektet! Vi vil gjerne at folk setter opp <a %(a_mirrors)s>speil</a>, og vi vil støtte dette økonomisk. Korleis kan eg hjelpe? Ja, det gjer vi. Vår inspirasjon for å samle metadata er Aaron Swartz sitt mål om «ei nettside for kvar bok som nokon gong er publisert», som han skapte <a %(a_openlib)s>Open Library</a> for. Det prosjektet har gjort det bra, men vår unike posisjon gjer at vi kan få metadata som dei ikkje kan. Ein annan inspirasjon var vårt ynskje om å vite <a %(a_blog)s>kor mange bøker det finst i verda</a>, slik at vi kan rekne ut kor mange bøker vi framleis har att å redde. Samlar de metadata? Merk at mhut.org blokkerer visse IP-områder, så en VPN kan være nødvendig. <strong>Android:</strong> Klikk på menyen med tre prikkar øvst til høgre, og vel "Legg til på startskjermen". <strong>iOS:</strong> Klikk på "Del" knappen nedst, og vel "Legg til på startskjermen". Vi har ikkje ein offisiell mobilapp, men du kan installere denne nettsida som ein app. Har de ein mobilapp? Send dei til <a %(a_archive)s>Internet Archive</a>. Dei vil ta vare på dei på ein skikkeleg måte. Korleis kan eg donere bøker eller andre fysiske materialar? Hvordan kan jeg be om bøker? <a %(a_blog)s>Anna sin Blogg</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmessige oppdateringar <a %(a_software)s>Anna sin Programvare</a> — vår open kjeldekode <a %(a_datasets)s>Datasett</a> — om dataen <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domener Er det fleire ressursar om Anna sitt Arkiv? <a %(a_translate)s>Omset på Anna sin Programvare</a> — vårt omsetjingssystem <a %(a_wikipedia)s>Wikipedia</a> — meir om oss (ver venleg å hjelpe med å halde denne sida oppdatert, eller lag ei for ditt eige språk!) Vel innstillingane du likar, hald søkjeboksen tom, klikk "Søk", og bokmerk sida ved å bruke bokmerke-funksjonen i nettlesaren din. Korleis lagrar eg søkjeinnstillingane mine? Vi ønskjer sikkerheitsforskarar velkomne til å søkje etter sårbarheiter i systema våre. Vi er store tilhengjarar av ansvarleg avsløring. Kontakt oss <a %(a_contact)s>her</a>. Vi er for tida ikkje i stand til å gi ut feilfinnarbelønningar, bortsett frå sårbarheiter som har <a %(a_link)s>potensial til å kompromittere anonymiteten vår</a>, for dei tilbyr vi belønningar i området $10k-50k. Vi ønskjer å tilby eit breiare omfang for feilfinnarbelønningar i framtida! Merk at sosialtekniske angrep er utanfor omfanget. Om du er interessert i offensiv sikkerheit, og vil hjelpe til med å arkivere verdas kunnskap og kultur, ver sikker på å kontakte oss. Det er mange måtar du kan hjelpe til på. Har de eit ansvarleg avsløringsprogram? Vi har bokstavelig talt ikke nok ressurser til å gi alle i verden høyhastighetsnedlastinger, så mye som vi skulle ønske det. Hvis en rik velgjører ønsker å trå til og gi oss dette, ville det vært utrolig, men inntil da gjør vi vårt beste. Vi er et non-profit prosjekt som knapt kan opprettholde seg selv gjennom donasjoner. Dette er grunnen til at vi implementerte to systemer for gratis nedlastinger, med våre partnere: delte servere med langsomme nedlastinger, og litt raskere servere med venteliste (for å redusere antall personer som laster ned samtidig). Vi har også <a %(a_verification)s>nettleserverifisering</a> for våre langsomme nedlastinger, fordi ellers vil roboter og skrapere misbruke dem, noe som gjør ting enda tregere for legitime brukere. Merk at når du brukar Tor-nettlesaren, kan det vere nødvendig å justere tryggingsinnstillingane dine. På det lågaste alternativet, kalla “Standard”, lukkast Cloudflare turnstile-utfordringa. På dei høgare alternativa, kalla “Sikrere” og “Sikrest”, mislykkast utfordringa. For store filer kan nedlastinger noen ganger bryte i midten. Vi anbefaler å bruke en nedlastingsbehandler (som JDownloader) for automatisk å gjenoppta store nedlastinger. Kvifor er dei trege nedlastingane så trege? Ofte stilte spørsmål (FAQ) Bruk <a %(a_list)s>torrent-lista generatoren</a> for å generere ei liste over torrentar som treng mest deling, innanfor lagringsplassgrensene dine. Ja, sjå <a %(a_llm)s>LLM data</a>-sida. Dei fleste torrentane inneheld filene direkte, noko som betyr at du kan instruere torrent-klientar til berre å laste ned dei nødvendige filene. For å avgjere kva filer du skal laste ned, kan du <a %(a_generate)s>generere</a> metadataen vår, eller <a %(a_download)s>laste ned</a> ElasticSearch- og MariaDB-databasane våre. Diverre inneheld nokre torrent-samlingar .zip- eller .tar-filer i rota, i så fall må du laste ned heile torrenten før du kan velje individuelle filer. Ingen enkle verktøy for å filtrere ned torrents er tilgjengelige ennå, men vi ønsker bidrag velkommen. (Vi har <a %(a_ideas)s>nokre idear</a> for det siste tilfellet.) Langt svar: Kort svar: ikkje lett. Vi prøver å halde minimal duplisering eller overlapp mellom torrentane i denne lista, men dette kan ikkje alltid oppnåast, og avheng mykje av retningslinjene til kjeldebiblioteka. For bibliotek som gir ut sine eigne torrenter, er det utanfor vår kontroll. For torrenter utgitt av Anna sitt Arkiv, dedupliserer vi berre basert på MD5-hash, noko som betyr at ulike versjonar av same bok ikkje blir deduplisert. Ja. Dette er faktisk PDF-ar og EPUB-ar, dei har berre ikkje ei filending i mange av torrentane våre. Det er to stader der du kan finne metadata for torrentfiler, inkludert filtypar/filendingar: 1. Kvar samling eller utgiving har sin eigen metadata. Til dømes, <a %(a_libgen_nonfic)s>Libgen.rs torrenter</a> har ein tilsvarande metadata-database hosta på Libgen.rs-nettstaden. Vi lenkjer vanlegvis til relevante metadata-ressursar frå kvar samling si <a %(a_datasets)s>datasettside</a>. 2. Vi anbefaler å <a %(a_generate)s>generere</a> eller <a %(a_download)s>laste ned</a> ElasticSearch- og MariaDB-databasane våre. Desse inneheld ei kartlegging for kvar post i Anna sitt Arkiv til dei tilsvarande torrentfilene (om tilgjengeleg), under “torrent_paths” i ElasticSearch JSON. Noen torrentklienter støtter ikke store bitstørrelser, som mange av torrentene våre har (for nyere gjør vi ikke dette lenger – selv om det er gyldig i henhold til spesifikasjonene!). Så prøv en annen klient hvis du støter på dette, eller klag til produsentene av torrentklienten din. Eg vil gjerne hjelpe til med å dele, men eg har ikkje mykje lagringsplass. Torrentane er for treige; kan eg laste ned dataene direkte frå dykk? Kan eg laste ned berre ein del av filene, som berre eit bestemt språk eller emne? Korleis handterer de duplikat i torrentane? Kan eg få torrent-lista som JSON? Eg ser ikkje PDF-ar eller EPUB-ar i torrentane, berre binære filer? Kva gjer eg? Hvorfor kan ikke torrentklienten min åpne noen av torrentfilene / magnetlinkene deres? Torrents FAQ Hvordan laster jeg opp nye bøker? Sjå <a %(a_href)s>dette framifrå prosjektet</a>. Har de ein oppetidsovervakar? Kva er Anna’s Archive? Bli medlem for å bruke raske nedlastingar. Vi støtter nå Amazon-gavekort, kreditt- og debetkort, krypto, Alipay og WeChat. Du har brukt opp raske nedlastingar i dag. Tilgang Nedlastingar per time dei siste 30 dagane. Timegjennomsnitt: %(hourly)s. Dagsgjennomsnitt: %(daily)s. Vi samarbeider med partnarar for å gjere samlingane våre lett og fritt tilgjengelege for alle. Vi trur at alle har rett til den kollektive visdomen til menneskeheten. Og <a %(a_search)s>ikkje på bekostning av forfattarar</a>. Datasetta brukt i Anna sitt Arkiv er heilt opne, og kan speglast i bulk ved hjelp av torrentar. <a %(a_datasets)s>Lær meir…</a> Langtidsarkiv Full database Søk Bøker, artiklar, magasin, teikneseriar, bibliotekoppføringar, metadata, … All vår <a %(a_code)s>kode</a> og <a %(a_datasets)s>data</a> er heilt open source. <span %(span_anna)s>Anna’s Archive</span> er eit ideelt prosjekt med to mål: <li><strong>Bevaring:</strong> Sikkerhetskopiering av all kunnskap og kultur i menneskeheten.</li><li><strong>Tilgang:</strong> Gjere denne kunnskapen og kulturen tilgjengeleg for alle i verda.</li> Vi har verdas største samling av høgkvalitets tekstdata. <a %(a_llm)s>Lær meir…</a> LLM-treningsdata 🪩 Speglar: oppmoding til frivillige Om du driv ein høgrisiko anonym betalingsformidlar, ver venleg å kontakt oss. Vi ser også etter folk som kan plassere smakfulle småannonsar. Alle inntekter går til våre bevaringsinnsatsar. Bevaring Vi estimerer at vi har bevart om lag <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% av verdas bøker</a>. Vi bevarer bøker, artiklar, teikneseriar, magasin og meir, ved å samle desse materiala frå ulike <a href="https://en.wikipedia.org/wiki/Shadow_library">skyggebibliotek</a>, offisielle bibliotek og andre samlingar på ein stad. Alle desse dataene blir bevart for alltid ved å gjere det enkelt å duplisere dei i store mengder — ved hjelp av torrentar — noko som resulterer i mange kopiar rundt om i verda. Nokre skyggebibliotek gjer allereie dette sjølv (f.eks. Sci-Hub, Library Genesis), medan Anna’s Archive “frigjer” andre bibliotek som ikkje tilbyr distribusjon i store mengder (f.eks. Z-Library) eller ikkje er skyggebibliotek i det heile tatt (f.eks. Internet Archive, DuXiu). Denne breie distribusjonen, kombinert med open-source kode, gjer nettsida vår motstandsdyktig mot nedstengingar, og sikrar langtidsbevaring av menneskehetens kunnskap og kultur. Lær meir om <a href="/datasets">våre datasett</a>. Om du er ein <a %(a_member)s>medlem</a>, treng du ikkje nettlesarverifisering. 🧬&nbsp;SciDB er ein fortsetjing av Sci-Hub. SciDB Open DOI Sci-Hub har <a %(a_paused)s>pause</a> opplasting av nye artiklar. Direkte tilgang til %(count)s akademiske artiklar 🧬&nbsp;SciDB er en fortsettelse av Sci-Hub, med sitt kjente grensesnitt og direkte visning av PDF-er. Skriv inn din DOI for å se. Vi har hele Sci-Hub-samlingen, samt nye artikler. De fleste kan vises direkte med et kjent grensesnitt, likt Sci-Hub. Noen kan lastes ned gjennom eksterne kilder, i så fall viser vi lenker til disse. Du kan hjelpe enormt ved å dele torrentar. <a %(a_torrents)s>Lær meir…</a> >%(count)s delarar <%(count)s delarar %(count_min)s–%(count_max)s delarar 🤝 Søker frivillige Som eit ideelt, open-source prosjekt, ser vi alltid etter folk som kan hjelpe til. IPFS-nedlastingar Liste av %(by)s, opprettet <span %(span_time)s>%(time)s</span> Lagre ❌ Noe gikk galt. Vennligst prøv igjen. ✅ Lagret. Vennligst last siden på nytt. Listen er tom. rediger Legg til eller fjern fra denne listen ved å finne en fil og åpne fanen “Lister”. Liste Hvordan vi kan hjelpe Fjerne overlapping (deduplisering) Tekst- og metadatauttrekk OCR Vi kan tilby høyhastighetstilgang til våre fulle samlinger, samt til uutgitte samlinger. Dette er tilgang på bedriftsnivå som vi kan tilby for donasjoner i størrelsesorden titusenvis av USD. Vi er også villige til å bytte dette mot høykvalitetssamlinger som vi ikke har ennå. Vi kan refundere deg hvis du kan gi oss berikelse av våre data, som: Støtt langsiktig arkivering av menneskelig kunnskap, samtidig som du får bedre data for modellen din! <a %(a_contact)s>Kontakt oss</a> for å diskutere hvordan vi kan samarbeide. Det er godt forstått at LLM-er trives på data av høy kvalitet. Vi har verdens største samling av bøker, artikler, magasiner osv., som er noen av de høyeste kvalitetstekstene. LLM-data Unik skala og rekkevidde Samlingen vår inneholder over hundre millioner filer, inkludert akademiske tidsskrifter, lærebøker og magasiner. Vi oppnår denne skalaen ved å kombinere store eksisterende arkiver. Noen av våre kildekolleksjoner er allerede tilgjengelige i bulk (Sci-Hub og deler av Libgen). Andre kilder har vi frigjort selv. <a %(a_datasets)s>Datasets</a> viser en full oversikt. Samlingen vår inkluderer millioner av bøker, artikler og magasiner fra før e-bok-æraen. Store deler av denne samlingen har allerede blitt OCR’et, og har allerede lite intern overlapping. Fortsett Om du har mistet nøkkelen din, vennligst <a %(a_contact)s>kontakt oss</a> og gi så mye informasjon som mulig. Du må kanskje midlertidig opprette en ny konto for å kontakte oss. Ver venleg å <a %(a_account)s>logge inn</a> for å sjå denne sida.</a> For å hindre spam-botar frå å opprette mange kontoar, må vi først verifisere nettlesaren din. Dersom du kjem inn i ein uendeleg sløyfe, anbefaler vi å installere <a %(a_privacypass)s>Privacy Pass</a>. Det kan også hjelpe å slå av annonseblokkerarar og andre nettlesarutvidingar. Logg inn / Registrer deg Anna sitt Arkiv er midlertidig nede for vedlikehald. Ver venleg å kome tilbake om ein time. Alternativ forfattar Alternativ skildring Alternativ utgåve Alternativ utviding Alternativ filnamn Alternativ forlag Alternativ tittel dato open sourced Les meir… skildring Søk i Annas Arkiv etter CADAL SSNO nummer Søk i Annas Arkiv etter DuXiu SSID nummer Søk i Annas Arkiv etter DuXiu DXID nummer Søk i Anna’s Archive etter ISBN Søk i Annas Arkiv etter OCLC (WorldCat) nummer Søk i Annas Arkiv etter Open Library ID Anna sitt Arkiv sin nettvisar %(count)s berørte sider Etter nedlasting: Ei betre versjon av denne fila kan vere tilgjengeleg på %(link)s Bulk torrent-nedlastingar samling Bruk nettbaserte verktøy for å konvertere mellom format. Anbefalte konverteringsverktøy: %(links)s For store filer anbefaler vi å bruke ein nedlastingsadministrator for å unngå avbrot. Anbefalte nedlastingsadministratorar: %(links)s EBSCOhost eBook-indeks (berre for ekspertar) (klikk også på “GET” øvst) (klikk på “GET” øvst) Eksterne nedlastingar <strong>🚀 Raske nedlastingar</strong> Du har %(remaining)s igjen i dag. Takk for at du er medlem! ❤️ <strong>🚀 Raske nedlastingar</strong> Du har brukt opp dagens raske nedlastingar. <strong>🚀 Raske nedlastingar</strong> Du lasta nyleg ned denne fila. Lenker er gyldige ei stund. <strong>🚀 Raske nedlastingar</strong> Bli ein <a %(a_membership)s>medlem</a> for å støtte langtidsbevaring av bøker, artiklar og meir. For å vise vår takksemd for di støtte, får du raske nedlastingar. ❤️ 🚀 Raske nedlastingar 🐢 Sakte nedlastingar Lån frå Internet Archive IPFS Gateway #%(num)d (du må kanskje prøve fleire gonger med IPFS) Libgen.li Libgen.rs Skjønnlitteratur Libgen.rs Sakprosa annonsene deira er kjende for å innehalde skadeleg programvare, så bruk ein annonseblokkering eller ikkje klikk på annonsene Amazon sitt “Send to Kindle” djazz sitt “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC-filer kan vere upålitelege å laste ned) Ingen nedlastingar funne. Alle nedlastingsalternativa har same fila, og bør vere trygge å bruke. Det sagt, ver alltid forsiktig når du lastar ned filer frå internett, spesielt frå sider eksterne til Anna sitt Arkiv. Til dømes, sørg for å halde einingane dine oppdaterte. (ingen omdirigering) Opne i vår visaren (opne i visaren) Val #%(num)d: %(link)s %(extra)s Finn originalposten i CADAL Søk manuelt på DuXiu Finn originaloppføring i ISBNdb Finn originalposten i WorldCat Finn originalposten i Open Library Søk i ulike andre databasar etter ISBN (berre for brukarar med utskriftshemming) PubMed Du vil trenge ein e-bok- eller PDF-lesar for å opne fila, avhengig av filformatet. Anbefalte e-boklesarar: %(links)s Annas Arkiv 🧬 SciDB Sci-Hub: %(doi)s (tilknytt DOI kan vere utilgjengeleg i Sci-Hub) Du kan sende både PDF- og EPUB-filer til Kindle- eller Kobo-lesaren din. Anbefalte verktøy: %(links)s Meir informasjon i <a %(a_slow)s>FAQ</a>. Støtt forfattarar og bibliotek Om du likar dette og har råd til det, vurder å kjøpe originalen, eller støtt forfattarane direkte. Om dette er tilgjengeleg på det lokale biblioteket ditt, vurder å låne det gratis der. Partner Server-nedlastingar er mellombels ikkje tilgjengelege for denne fila. torrent Frå pålitelege partnarar. Z-Library Z-bibliotek på Tor (krev Tor-nettlesaren) vis eksterne nedlastingar <span class="font-bold">❌ Denne fila kan ha problem, og har blitt skjult frå eit kjeldesbibliotek.</span> Nokre gonger er dette på førespurnad frå ein opphavsrettshavar, andre gonger er det fordi eit betre alternativ er tilgjengeleg, men nokre gonger er det på grunn av eit problem med fila sjølv. Det kan framleis vere greitt å laste ned, men vi tilrår først å søkje etter ei alternativ fil. Meir detaljar: Om du framleis vil laste ned denne fila, sørg for å berre bruke påliteleg, oppdatert programvare for å opne ho. metadata-kommentarar AA: Søk i Anna sitt Arkiv etter “%(name)s” Kodeutforskar: Vis i Kodeutforskar “%(name)s” URL: Nettstad: Hvis du har denne filen og den ikke er tilgjengelig i Annas Arkiv ennå, vurder å <a %(a_request)s>laste den opp</a>. Internet Archive Controlled Digital Lending fil “%(id)s” Dette er en post av en fil fra Internet Archive, ikke en direkte nedlastbar fil. Du kan prøve å låne boken (lenke nedenfor), eller bruke denne URL-en når du <a %(a_request)s>ber om en fil</a>. Forbetre metadata CADAL SSNO %(id)s metadata post Dette er ein metadataoppføring, ikkje ei nedlastbar fil. Du kan bruke denne URL-en når du <a %(a_request)s>ber om ei fil</a>. DuXiu SSID %(id)s metadata post ISBNdb %(id)s metadata post MagzDB ID %(id)s metadataoppføring Nexus/STC ID %(id)s metadataoppføring OCLC (WorldCat) nummer %(id)s metadata post Open Library %(id)s metadata post Sci-Hub fil “%(id)s” Ikkje funne “%(md5_input)s” vart ikkje funne i databasen vår. Legg til kommentar (%(count)s) Du kan få md5 frå URL-en, t.d. MD5 av ein betre versjon av denne fila (om tilgjengeleg). Fyll ut dette om det finst ei anna fil som liknar denne (same utgåve, same filtype om mogleg), som folk bør bruke i staden for denne fila. Om du veit om ein betre versjon av denne fila utanfor Anna sitt Arkiv, ver venleg <a %(a_upload)s>last opp den</a>. Noko gjekk gale. Ver venleg å last sida på nytt og prøv igjen. Du la igjen ein kommentar. Det kan ta eit minutt før den visast. Ver venleg å bruk <a %(a_copyright)s>DMCA / Skjema for opphavsrettskrav</a>. Beskriv problemet (påkravd) Om denne fila har høg kvalitet, kan du diskutere alt om den her! Om ikkje, ver venleg å bruk knappen “Rapporter filproblem”. Flott filkvalitet (%(count)s) Filkvalitet Lær korleis du kan <a %(a_metadata)s>forbetre metadataen</a> for denne fila sjølv. Problembeskriving Ver venleg <a %(a_login)s>logg inn</a>. Eg elska denne boka! Hjelp samfunnet ved å rapportere kvaliteten på denne fila! 🙌 Noko gjekk gale. Ver venleg å last sida på nytt og prøv igjen. Rapporter filproblem (%(count)s) Takk for at du sende inn rapporten din. Den vil bli vist på denne sida, og manuelt gjennomgått av Anna (til vi har eit skikkeleg moderasjonssystem). Legg igjen kommentar Send rapport Kva er gale med denne fila? Lån (%(count)s) Kommentarar (%(count)s) Nedlastingar (%(count)s) Utforsk metadata (%(count)s) Lister (%(count)s) Statistikk (%(count)s) For informasjon om denne spesifikke fila, sjå <a %(a_href)s>JSON-fila</a>. Dette er ei fil administrert av <a %(a_ia)s>IA’s Controlled Digital Lending</a>-bibliotek, og indeksert av Anna sitt Arkiv for søk. For informasjon om dei ulike datasetsa vi har samla, sjå <a %(a_datasets)s>Datasets-sida</a>. Metadata frå lenka post Forbetre metadata på Open Library Ein “fil MD5” er ein hash som blir rekna ut frå filinnhaldet, og er rimeleg unik basert på det innhaldet. Alle skyggebiblioteka vi har indeksert her bruker primært MD5-ar for å identifisere filer. Ei fil kan dukke opp i fleire skyggebibliotek. For informasjon om dei ulike datasetsa vi har samla, sjå <a %(a_datasets)s>Datasets-sida</a>. Rapporter filkvalitet Totale nedlastingar: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tsjekkiske metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Bøker %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Advarsel: fleire lenka postar: Når du ser på ei bok på Anna sitt Arkiv, kan du sjå ulike felt: tittel, forfattar, forlag, utgåve, år, beskriving, filnamn, og meir. Alle desse informasjonsbitane vert kalla <em>metadata</em>. Sidan vi kombinerer bøker frå ulike <em>kjeldebibliotek</em>, viser vi kva metadata som er tilgjengeleg i det kjeldebiblioteket. Til dømes, for ei bok som vi fekk frå Library Genesis, vil vi vise tittelen frå Library Genesis sin database. Av og til er ei bok tilgjengeleg i <em>fleire</em> kjeldebibliotek, som kan ha ulike metadatafelt. I slike tilfelle viser vi rett og slett den lengste versjonen av kvart felt, sidan den forhåpentlegvis inneheld den mest nyttige informasjonen! Vi vil framleis vise dei andre felta under beskrivinga, til dømes som ”alternativ tittel” (men berre om dei er ulike). Vi trekkjer også ut <em>kodar</em> som identifikatorar og klassifikatorar frå kjeldebiblioteket. <em>Identifikatorar</em> representerer unikt ei bestemt utgåve av ei bok; døme er ISBN, DOI, Open Library ID, Google Books ID, eller Amazon ID. <em>Klassifikatorar</em> grupperer saman fleire liknande bøker; døme er Dewey Decimal (DCC), UDC, LCC, RVK, eller GOST. Av og til er desse kodane eksplisitt lenka i kjeldebiblioteka, og av og til kan vi trekkje dei ut frå filnamnet eller beskrivinga (hovudsakleg ISBN og DOI). Vi kan bruke identifikatorar til å finne oppføringar i <em>metadata-berre samlingar</em>, som OpenLibrary, ISBNdb, eller WorldCat/OCLC. Det er ein spesifikk <em>metadata-fane</em> i søkemotoren vår om du vil bla gjennom desse samlingane. Vi bruker samsvarande oppføringar til å fylle ut manglande metadatafelt (til dømes om ein tittel manglar), eller til dømes som “alternativ tittel” (om det finst ein eksisterande tittel). For å sjå nøyaktig kvar metadataen til ei bok kjem frå, sjå <em>“Tekniske detaljar”-fanen</em> på ei boksida. Den har ei lenke til rå-JSON for den boka, med peikarar til rå-JSON av dei originale oppføringane. For meir informasjon, sjå følgjande sider: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Søk (metadata-fane)</a>, <a %(a_codes)s>Kodeutforskar</a>, og <a %(a_example)s>Døme på metadata JSON</a>. Til slutt kan all metadataen vår <a %(a_generated)s>genererast</a> eller <a %(a_downloaded)s>lastast ned</a> som ElasticSearch- og MariaDB-databasar. Bakgrunn Du kan hjelpe til med å bevare bøker ved å forbetre metadata! Først, les bakgrunnen om metadata på Anna sitt Arkiv, og lær deretter korleis du kan forbetre metadata gjennom å lenke med Open Library, og få gratis medlemskap på Anna sitt Arkiv. Forbetre metadata Så om du kjem over ei fil med dårleg metadata, korleis skal du fikse det? Du kan gå til kjeldebiblioteket og følgje prosedyrene deira for å fikse metadata, men kva skal du gjere om ei fil er tilgjengeleg i fleire kjeldebibliotek? Det er ein identifikator som vert behandla spesielt på Anna sitt Arkiv. <strong>Feltet annas_archive md5 på Open Library overstyrer alltid all annan metadata!</strong> La oss ta eit steg tilbake og lære om Open Library. Open Library vart grunnlagt i 2006 av Aaron Swartz med målet om “ei nettside for kvar bok som nokon gong er publisert”. Det er ein slags Wikipedia for bokmetadata: alle kan redigere det, det er fritt lisensiert, og kan lastast ned i bulk. Det er ein bokdatabase som er mest i tråd med vårt oppdrag — faktisk har Anna sitt Arkiv vorte inspirert av Aaron Swartz sin visjon og liv. I staden for å finne opp hjulet på nytt, bestemte vi oss for å omdirigere våre frivillige mot Open Library. Om du ser ei bok som har feil metadata, kan du hjelpe til på følgjande måte: Merk at dette kun fungerer for bøker, ikke akademiske artikler eller andre typer filer. For andre typer filer anbefaler vi fortsatt å finne kildebiblioteket. Det kan ta noen uker før endringene blir inkludert i Anna’s Archive, siden vi må laste ned den nyeste Open Library-datadumpen og regenerere vår søkeindeks.  Gå til <a %(a_openlib)s>Open Library-nettsida</a>. Finn den korrekte bokoppføringa. <strong>ADVARSEL:</strong> ver sikker på å velje den korrekte <strong>utgåva</strong>. I Open Library har du “verk” og “utgåver”. Eit “verk” kan vere “Harry Potter og De Vises Stein”. Ei “utgåve” kan vere: Førsteutgaven fra 1997 utgitt av Bloomsbery med 256 sider. Pocketutgaven fra 2003 utgitt av Raincoast Books med 223 sider. Den polske oversettelsen fra 2000 “Harry Potter I Kamie Filozoficzn” av Media Rodzina med 328 sider. Alle disse utgavene har forskjellige ISBN-er og forskjellig innhold, så vær sikker på at du velger den riktige! Rediger posten (eller opprett den hvis den ikke finnes), og legg til så mye nyttig informasjon som mulig! Du er her nå uansett, så du kan like gjerne gjøre posten virkelig fantastisk. Under “ID-nummer” velger du “Anna’s Archive” og legger til MD5-en til boken fra Anna’s Archive. Dette er den lange strengen av bokstaver og tall etter “/md5/” i URL-en. Prøv å finne andre filer i Anna’s Archive som også matcher denne posten, og legg til disse også. I fremtiden kan vi gruppere disse som duplikater på Anna’s Archive søkeside. Når du er ferdig, skriv ned URL-en som du nettopp oppdaterte. Når du har oppdatert minst 30 poster med Anna’s Archive MD5-er, send oss en <a %(a_contact)s>e-post</a> og send oss listen. Vi gir deg et gratis medlemskap for Anna’s Archive, slik at du lettere kan gjøre dette arbeidet (og som en takk for hjelpen). Disse må være høykvalitetsredigeringer som legger til betydelige mengder informasjon, ellers vil forespørselen din bli avvist. Forespørselen din vil også bli avvist hvis noen av redigeringene blir omgjort eller korrigert av Open Library-moderatorer. Open Library-lenking Hvis du blir betydelig involvert i utviklingen og driften av vårt arbeid, kan vi diskutere å dele mer av donasjonsinntektene med deg, slik at du kan bruke dem etter behov. Vi vil kun betale for vertskap når du har alt satt opp, og har vist at du er i stand til å holde arkivet oppdatert med oppdateringer. Dette betyr at du må betale for de første 1-2 månedene selv. Din tid vil ikke bli kompensert (og heller ikke vår), siden dette er rent frivillig arbeid. Vi er villige til å dekke vertskaps- og VPN-utgifter, i utgangspunktet opptil $200 per måned. Dette er tilstrekkelig for en grunnleggende søkeserver og en DMCA-beskyttet proxy. Vertskapsutgifter Vennligst <strong>ikke kontakt oss</strong> for å be om tillatelse, eller for grunnleggende spørsmål. Handlinger taler høyere enn ord! All informasjonen er der ute, så bare sett i gang med å sette opp ditt speil. Føl deg fri til å legge inn billetter eller forespørsler om sammenslåing til vår Gitlab når du støter på problemer. Vi kan trenge å bygge noen speilspesifikke funksjoner med deg, som å omprofilere fra «Anna’s Archive» til navnet på ditt nettsted, (i utgangspunktet) deaktivere brukerkontoer, eller lenke tilbake til vårt hovednettsted fra boksider. Når du har ditt speil oppe og kjører, vennligst kontakt oss. Vi vil gjerne gjennomgå din OpSec, og når det er solid, vil vi lenke til ditt speil, og begynne å jobbe tettere sammen med deg. Takk på forhånd til alle som er villige til å bidra på denne måten! Det er ikke for de svake, men det vil styrke varigheten av det største virkelig åpne biblioteket i menneskets historie. Komme i gang For å øke motstandsdyktigheten til Anna’s Archive, ser vi etter frivillige til å kjøre speil. Din versjon er tydelig merket som et speil, f.eks. «Bobs Arkiv, et speil av Anna’s Archive». Du er villig til å ta risikoene forbundet med dette arbeidet, som er betydelige. Du har en dyp forståelse av den nødvendige operasjonelle sikkerheten. Innholdet i <a %(a_shadow)s>disse</a> <a %(a_pirate)s>innleggene</a> er selvinnlysende for deg. I utgangspunktet vil vi ikke gi deg tilgang til våre partner-servernedlastinger, men hvis alt går bra, kan vi dele det med deg. Du kjører den åpne kildekoden til Anna’s Archive, og du oppdaterer regelmessig både koden og dataene. Du er villig til å bidra til vår <a %(a_codebase)s>kodebase</a> — i samarbeid med vårt team — for å få dette til. Vi ser etter dette: Speil: oppfordring til frivillige Gjer ei ny donasjon. Ingen donasjonar enno. <a %(a_donate)s>Gjer mi første donasjon.</a> Donasjonsdetaljer vises ikke offentlig. Mine donasjoner 📡 For masse-nedlasting av samlinga vår, sjekk ut <a %(a_datasets)s>Datasets</a> og <a %(a_torrents)s>Torrents</a>-sidene. Nedlastingar frå IP-adressa di dei siste 24 timane: %(count)s. 🚀 For raskare nedlastingar og for å hoppe over nettlesarsjekkane, <a %(a_membership)s>bli medlem</a>. Last ned frå partnarside Du kan gjerne fortsette å bla gjennom Anna sitt Arkiv i ein annan fane medan du ventar (om nettlesaren din støttar oppdatering av bakgrunnsfaner). Du kan gjerne vente på at fleire nedlastingssider lastar samtidig (men ver venleg å berre laste ned éi fil om gongen per server). Når du får ein nedlastingslenke, er den gyldig i fleire timar. Takk for at du ventar, dette held nettsida tilgjengeleg gratis for alle! 😊 🔗 Alle nedlastingslenker for denne fila: <a %(a_main)s>Fil hovudside</a>. ❌ Sakte nedlastingar er ikkje tilgjengelege gjennom Cloudflare VPN-ar eller frå Cloudflare IP-adresser. ❌ Sakte nedlastingar er berre tilgjengelege gjennom den offisielle nettsida. Besøk %(websites)s. 📚 Bruk følgjande URL for å laste ned: <a %(a_download)s>Last ned no</a>. For å gi alle moglegheit til å laste ned filer gratis, må du vente før du kan laste ned denne fila. Ver venleg <span %(span_countdown)s>%(wait_seconds)s</span> sekundar for å laste ned denne fila. Åtvaring: Det har vore mange nedlastingar frå IP-adressa di dei siste 24 timane. Nedlastingane kan vere tregare enn vanleg. Om du brukar ein VPN, delt internettforbindelse, eller om ISP-en din deler IP-ar, kan denne åtvaringa vere på grunn av det. Lagre ❌ Noe gikk galt. Vennligst prøv igjen. ✅ Lagret. Vennligst last siden på nytt. Endre visningsnavnet ditt. Identifikatoren din (delen etter “#”) kan ikke endres. Profil opprettet <span %(span_time)s>%(time)s</span> rediger Lister Opprett en ny liste ved å finne en fil og åpne fanen «Lister». Ingen lister ennå Profil ikke funnet. Profil For øyeblikket kan vi ikke imøtekomme bokforespørsler. Ikkje send oss bokførespurnadene dine på e-post. Ver venleg å legg inn førespurnadene dine på Z-Library eller Libgen-foruma. Registrering i Annas Arkiv DOI: %(doi)s Last ned SciDB Nexus/STC Ingen forhåndsvisning tilgjengelig ennå. Last ned filen fra <a %(a_path)s>Annas Arkiv</a>. For å støtte tilgjengeligheten og langtidsbevaringen av menneskelig kunnskap, bli en <a %(a_donate)s>medlem</a>. Som en bonus, 🧬&nbsp;SciDB laster raskere for medlemmer, uten noen begrensninger. Fungerer det ikke? Prøv å <a %(a_refresh)s>oppdatere</a>. Sci-Hub Legg til spesifikt søkjefelt Søk beskrivelser og metadata kommentarer Utgivelsesår Avansert Tilgang Innhold Vis Liste Tabell Filtype Språk Sorter etter Største Mest relevant Nyaste (filstorleik) (open source) (utgivelsesår) Eldste Tilfeldig Minste Kjelde skrapa og open-sourca av AA Digital utlån (%(count)s) Tidsskriftartikler (%(count)s) Vi har funnet treff i: %(in)s. Du kan referere til URL-en funnet der når du <a %(a_request)s>ber om en fil</a>. Metadata (%(count)s) For å utforske søkeindeksen etter koder, bruk <a %(a_href)s>Codes Explorer</a>. Søkjeindeksen vert oppdatert månadleg. Han inkluderer for tida oppføringar fram til %(last_data_refresh_date)s. For meir teknisk informasjon, sjå <a %(link_open_tag)s>datasets-sida</a>. Ekskluder Inkluder berre Umerka meir… Neste … Førre Denne søkjeindeksen inkluderer for tida metadata frå Internet Archive sitt Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Meir om datasetta våre</a>. For fleire digitale utlånsbibliotek, sjå <a %(a_wikipedia)s>Wikipedia</a> og <a %(a_mobileread)s>MobileRead Wiki</a>. For DMCA / opphavsrettskrav <a %(a_copyright)s>klikk her</a>. Nedlastingstid Feil under søk. Prøv <a %(a_reload)s>å laste inn siden på nytt</a>. Hvis problemet vedvarer, vennligst send oss en e-post på %(email)s. Rask nedlasting Faktisk kan kven som helst hjelpe til med å bevare desse filene ved å dele vår <a %(a_torrents)s>samla liste over torrentar</a>. ➡️ Nokre gonger skjer dette feilaktig når søkeserveren er treg. I slike tilfelle kan <a %(a_attrs)s>omlasting</a> hjelpe. ❌ Denne fila kan ha problem. Ser du etter artiklar? Denne søkjeindeksen inkluderer for tida metadata frå ulike metadatakjelder. <a %(a_datasets)s>Meir om datasetta våre</a>. Det finst mange, mange kjelder til metadata for skriftlege verk rundt om i verda. <a %(a_wikipedia)s>Denne Wikipedia-sida</a> er ein god start, men om du kjenner til andre gode lister, ver venleg å gi oss beskjed. For metadata viser vi dei originale oppføringane. Vi gjer ingen samanslåing av oppføringar. Vi har for tida verdas mest omfattande opne katalog av bøker, artiklar og andre skriftlege verk. Vi speglar Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>og meir</a>. <span class="font-bold">Ingen filer funnet.</span> Prøv færre eller andre søkeord og filtre. Resultater %(from)s-%(to)s (%(total)s totalt) Om du finn andre “skuggebibliotek” som vi bør spegle, eller om du har spørsmål, ver venleg å kontakte oss på %(email)s. %(num)d delvise treff %(num)d+ delvise treff Skriv i boksen for å søkje etter filer i digitale utlånsbibliotek. Skriv i boksen for å søkje i katalogen vår av %(count)s filer som kan lastast ned direkte, som vi <a %(a_preserve)s>bevarer for alltid</a>. Skriv i boksen for å søkje. Skriv i boksen for å søkje i katalogen vår av %(count)s akademiske artiklar og tidsskriftartiklar, som vi <a %(a_preserve)s>bevarer for alltid</a>. Skriv i boksen for å søkje etter metadata frå bibliotek. Dette kan vere nyttig når du <a %(a_request)s>ber om ei fil</a>. Tips: bruk tastatursnarvegar “/” (søkjefokus), “enter” (søk), “j” (opp), “k” (ned), “<” (førre side), “>” (neste side) for raskare navigering. Dette er metadataoppføringer, <span %(classname)s>ikke</span> nedlastbare filer. Søkjeinnstillingar Søk Digital utlån Last ned Tidsskriftartikler Metadata Nytt søk %(search_input)s - Søk Søket tok for lang tid, noko som betyr at du kanskje ser unøyaktige resultat. Nokre gonger kan <a %(a_reload)s>oppdatering</a> av sida hjelpe. Søket tok for lang tid, noko som er vanleg for breie søk. Filtertala kan vere unøyaktige. For store opplastinger (over 10 000 filer) som ikke blir akseptert av Libgen eller Z-Library, vennligst kontakt oss på %(a_email)s. For Libgen.li, sørg for å først logge inn på <a %(a_forum)s >deira forum</a> med brukarnamn %(username)s og passord %(password)s, og deretter returnere til deira <a %(a_upload_page)s >opplastingsside</a>. Foreløpig foreslår vi å laste opp nye bøker til Library Genesis-forgreningene. Her er en <a %(a_guide)s>praktisk guide</a>. Merk at begge forgreningene vi indekserer på denne nettsiden henter fra dette samme opplastingssystemet. For små opplastingar (opptil 10 000 filer) ver venleg å laste dei opp til både %(first)s og %(second)s. Alternativt kan du laste dem opp til Z-Library <a %(a_upload)s>her</a>. For å laste opp akademiske artikler, vennligst last dem også opp til <a %(a_stc_nexus)s>STC Nexus</a> (i tillegg til Library Genesis). De er det beste skyggebiblioteket for nye artikler. Vi har ikke integrert dem ennå, men vi vil gjøre det på et tidspunkt. Du kan bruke deres <a %(a_telegram)s>opplastingsbot på Telegram</a>, eller kontakte adressen som er oppført i deres festede melding hvis du har for mange filer til å laste opp på denne måten. <span %(label)s>Tung frivillig arbeid (USD$50-USD$5,000 belønninger):</span> om du kan dedikere mye tid og/eller ressurser til vårt oppdrag, vil vi gjerne jobbe tettere med deg. Etter hvert kan du bli med i kjerneteamet. Selv om vi har et stramt budsjett, kan vi tildele <span %(bold)s>💰 monetære belønninger</span> for det mest intense arbeidet. <span %(label)s>Lett frivillig arbeid:</span> hvis du bare kan avse noen timer her og der, er det fortsatt mange måter du kan hjelpe til på. Vi belønner konsekvente frivillige med <span %(bold)s>🤝 medlemskap til Anna’s Archive</span>. Anna’s Archive er avhengig av frivillige som deg. Vi ønsker alle engasjementsnivåer velkommen, og har to hovedkategorier av hjelp vi ser etter: Om du ikke kan frivilliggi din tid, kan du fortsatt hjelpe oss mye ved å <a %(a_donate)s>donere penger</a>, <a %(a_torrents)s>seede våre torrenter</a>, <a %(a_uploading)s>laste opp bøker</a>, eller <a %(a_help)s>fortelle dine venner om Anna’s Archive</a>. <span %(bold)s>Bedrifter:</span> vi tilbyr høyhastighets direkte tilgang til våre samlinger i bytte mot donasjoner på bedriftsnivå eller bytte mot nye samlinger (f.eks. nye skanninger, OCR’ede datasets, berikelse av våre data). <a %(a_contact)s>Kontakt oss</a> om dette gjelder deg. Se også vår <a %(a_llm)s>LLM-side</a>. Dusørar Vi ser alltid etter folk med solide programmerings- eller offensiv sikkerhetsferdigheter til å bli involvert. Du kan gjøre en betydelig innsats for å bevare menneskehetens arv. Som en takk gir vi bort medlemskap for solide bidrag. Som en stor takk gir vi bort monetære dusørar for spesielt viktige og vanskelige oppgaver. Dette bør ikke sees på som en erstatning for en jobb, men det er et ekstra insentiv og kan hjelpe med påløpte kostnader. Mesteparten av koden vår er åpen kildekode, og vi vil be om det samme av koden din når vi tildeler dusøren. Det finnes noen unntak som vi kan diskutere individuelt. Dusørar tildeles den første personen som fullfører en oppgave. Kommenter gjerne på en dusørbillett for å la andre vite at du jobber med noe, slik at andre kan holde seg unna eller kontakte deg for å samarbeide. Men vær oppmerksom på at andre fortsatt er fri til å jobbe med det også og prøve å slå deg. Vi tildeler imidlertid ikke dusørar for slurvete arbeid. Hvis to høykvalitetsinnleveringer gjøres nær hverandre (innen en dag eller to), kan vi velge å tildele dusørar til begge, etter vårt skjønn, for eksempel 100%% for den første innleveringen og 50%% for den andre innleveringen (så 150%% totalt). For de større dusørene (spesielt skrapedusørar), vennligst kontakt oss når du har fullført ~5%% av det, og du er trygg på at metoden din vil skalere til hele milepælen. Du må dele metoden din med oss slik at vi kan gi tilbakemelding. På denne måten kan vi også bestemme hva vi skal gjøre hvis det er flere personer som nærmer seg en dusør, for eksempel potensielt tildele den til flere personer, oppmuntre folk til å samarbeide, osv. ADVARSEL: de høye dusøroppgavene er <span %(bold)s>vanskelige</span> — det kan være lurt å starte med enklere oppgaver. Gå til vår <a %(a_gitlab)s>Gitlab-issues-liste</a> og sorter etter "Label priority". Dette viser omtrent rekkefølgen på oppgavene vi bryr oss om. Oppgaver uten eksplisitte dusørar er fortsatt kvalifisert for medlemskap, spesielt de merket "Accepted" og "Anna’s favorite". Du vil kanskje starte med et "Starter project". Lett frivillig arbeid Vi har no også ein synkronisert Matrix-kanal på %(matrix)s. Om du har noen timer til overs, kan du hjelpe til på flere måter. Sørg for å bli med i <a %(a_telegram)s>frivilligchatten på Telegram</a>. Som en takk gir vi vanligvis ut 6 måneder med “Lykkeleg Bibliotekar” for grunnleggende milepæler, og mer for fortsatt frivillig arbeid. Alle milepæler krever arbeid av høy kvalitet — slurvete arbeid skader oss mer enn det hjelper, og vi vil avvise det. Vennligst <a %(a_contact)s>send oss en e-post</a> når du når en milepæl. %(links)s lenker eller skjermbilete av førespurnader du har oppfylt. Oppfylle bok- (eller papir, etc.) forespørsler på Z-Library eller Library Genesis-forumene. Vi har ikke vårt eget bokforespørselssystem, men vi speiler disse bibliotekene, så å gjøre dem bedre gjør Anna’s Archive bedre også. Milepæl Oppgave Avhenger av oppgaven. Små oppgaver postet i vår <a %(a_telegram)s>frivilligchat på Telegram</a>. Vanligvis for medlemskap, noen ganger for små belønninger. Små oppgaver lagt ut i vår frivillige chatgruppe. Sørg for å legge igjen en kommentar på problemene du løser, slik at andre ikke dupliserer arbeidet ditt. %(links)s lenker til oppføringar du har forbetra. Du kan bruke <a %(a_list)s >listen over tilfeldige metadata-problemer</a> som et utgangspunkt. Forbedre metadata ved å <a %(a_metadata)s>lenke</a> med Open Library. Desse bør vise at du fortel nokon om Annas Arkiv, og at dei takkar deg. %(links)s lenker eller skjermbilete. Spre ordet om Annas Arkiv. Til dømes ved å anbefale bøker på AA, lenkje til blogginnlegga våre, eller generelt vise folk til nettsida vår. Fullstendig oversette et språk (om det ikke allerede var nær fullføring). <a %(a_translate)s>Oversette</a> nettstedet. Lenke til redigeringshistorikk som viser at du har gjort betydelige bidrag. Forbedre Wikipedia-siden for Anna’s Archive på ditt språk. Inkluder informasjon fra AA’s Wikipedia-sider på andre språk, og fra vår nettside og blogg. Legg til referanser til AA på andre relevante sider. Frivillighet & Belønninger 