#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Яңылыш һорау. %(websites)s сайтына инеү."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Интернет Архивының Китапханаһы"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " һәм "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "һәм башҡалар"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Беҙ %(libraries)s күсермәһен эшләйбеҙ."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Беҙ мәғлүмәтте йыйып, асыҡ сығанаҡҡа сығарабыҙ %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Бөтә код һәм мәғлүмәттәребеҙ тулыһынса асыҡ сығанаҡлы."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Кешелек тарихындағы иң ҙур ысын асыҡ китапхана."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;китаптар, %(paper_count)s&nbsp;ғилми мәҡәләләр — мәңгелеккә һаҡланған."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Донъялағы иң ҙур асыҡ сығанаҡлы асыҡ мәғлүмәт китапханаһы. ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library һәм башҡаларҙың күсермәләрен эшләй. 📈&nbsp;%(book_any)s китаптар, %(journal_article)s ғилми мәҡәләләр, %(book_comic)s комикстар, %(magazine)s журналдар — мәңгелеккә һаҡланған."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Донъялағы иң ҙур асыҡ сығанаҡлы асыҡ мәғлүмәт китапханаһы.<br>⭐️ Scihub, Libgen, Zlib һәм башҡаларҙың күсермәләрен эшләй."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Дөрөҫ булмаған метадата (мәҫәлән, исем, тасуирлама, тышлыҡ һүрәте)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Йөкләү проблемалары (мәҫәлән, тоташып булмай, хаталар хәбәрҙәре, бик әкрен)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Файл асылмай (мәҫәлән, боҙолған файл, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Түбән сифатлы (мәҫәлән, форматлау проблемалары, насар скан сифатлы, етешмәгән биттәр)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Спам / файлды юйырға кәрәк (мәҫәлән, реклама, яман һүҙҙәр)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Автор хоҡуғы дәғүәһе"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Башҡа"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Бонус йөкләүҙәр"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Бик Белемле Китап ҡорто"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Уңышлы Китапханасы"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Ялтыраған Мәғлүмәт Йыйыусы"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Ғәжәйеп Архивсы"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s дөйөм"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) дөйөм"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s бонус)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "түленмәгән"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "түленгән"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "юйылған"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "ваҡыт сыҡҡан"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "Аннанан раҫлау көтөлә"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "яраҡһыҙ"

#, fuzzy
msgid "page.donate.title"
msgstr "Иғәнә"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Һеҙҙең <a %(a_donation)s>ағымдағы иғәнәгеҙ</a> тамамланмаған. Яңы иғәнә яһар алдынан уны тамамлағыҙ йәки туҡтатығыҙ."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Бөтә иғәнәләремде ҡарарға</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Аннаның Архивы - коммерцияға ҡарамаған, асыҡ сығанаҡлы, асыҡ мәғлүмәтле проект. Иғәнә яһап һәм ағза булып, һеҙ беҙҙең эшмәкәрлек һәм үҫешебеҙҙе хуплайһығыҙ. Бөтә ағзаларыбыҙға: беҙҙе хуплағанығыҙ өсөн рәхмәт! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Ҡатмарлыраҡ мәғлүмәт өсөн, <a %(a_donate)s>Иғәнә FAQ</a>-ын ҡарағыҙ."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Тағы ла күберәк йөкләп алыу өсөн, <a %(a_refer)s>дуҫтарығыҙҙы саҡырығыҙ</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Һеҙгә %(percentage)s%% өҫтәмә тиҙ йөкләп алыуҙар бирелә, сөнки һеҙҙе ҡулланыусы %(profile_link)s саҡырған."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Был бөтә ағзалыҡ осорона ҡағыла."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s тиҙ йөкләп алыуҙар көнөнә"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "әгәр һеҙ был айҙа иғәнә индерһәгеҙ!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / ай"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Ҡушылырға"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Һайланған"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "%(percentage)s%% тиклем ташламалар"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB мәҡәләләре <strong>сикһеҙ</strong> тикшереүһеҙ"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> инеү"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "<a %(a_refer)s>дуҫтарығыҙҙы саҡырып</a> <strong>%(percentage)s%% өҫтәмә йөкләп алыуҙар</strong> алығыҙ."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Кредита һеҙҙең ҡулланыусы исемегеҙ йәки аноним телгә алыу"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Алдағы өҫтөнлөктәр, өҫтәп:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Яңы функцияларға иртә инеү"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Эксклюзив Телеграм каналында эш барышы тураһында яңыртыуҙар"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Торрентты ҡабул итеү”: һеҙҙең ҡулланыусы исемегеҙ йәки хәбәрегеҙ торрент файл исемендә <div %(div_months)s>ағзалыҡтың һәр 12 айында бер тапҡыр</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Кеше белеме һәм мәҙәниәтен һаҡлауҙа легендар статус"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Эксперттар өсөн инеү"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "беҙҙең менән бәйләнешкә сығығыҙ"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Беҙ ирекмәндәрҙән торған бәләкәй генә команда. Яуап биреү өсөн 1-2 аҙна ваҡыт талап ителеүе мөмкин."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Сикһеҙ</strong> юғары тиҙлекле инеү"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Тура <strong>SFTP</strong> серверҙары"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Яңы коллекциялар өсөн предприятие кимәлендәге иғәнә йәки алмашыу (мәҫәлән, яңы сканерҙар, OCR-ланған мәғлүмәттәр)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Беҙ бай шәхестәрҙән йәки учреждениеларҙан ҙур иғәнәләрҙе ҡабул итәбеҙ. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "$5000 ашыу иғәнәләр өсөн беҙгә тура бәйләнешкә сығығыҙ: %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Иғтибар итегеҙ, был биттәге ағзалыҡтар «ай һайын» булһа ла, улар бер тапҡыр ғына яһалған иғәнәләр (даими түгел). <a %(faq)s>Иғәнә тураһында йыш бирелгән һорауҙар</a> бүлеген ҡарағыҙ."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Әгәр һеҙ ағзалыҡһыҙ (теләһә ниндәй сумма) иғәнә индерергә теләһәгеҙ, был Monero (XMR) адресын ҡулланығыҙ: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Зинһар, түләү ысулын һайлағыҙ."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(ваҡытлыса ҡулланылмай)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s бүләк картаһы"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Банк картаһы (ҡушымта ҡулланып)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Крипто %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Кредит/дебет картаһы"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (АҚШ) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (ғәҙәти)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Карта / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Кредит/дебет/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Бразилия)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Банк картаһы"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Кредит/дебет картаһы (резерв)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Кредит/дебет картаһы 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Криптовалюта менән һеҙ BTC, ETH, XMR һәм SOL ҡулланып иғәнә яһай алаһығыҙ. Был вариантты криптовалюта менән таныш булған осраҡта ҡулланығыҙ."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Криптовалюта менән һеҙ BTC, ETH, XMR һәм башҡа төрҙәрен ҡулланып иғәнә яһай алаһығыҙ."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Әгәр һеҙ криптовалютаны тәүге тапҡыр ҡулланаһығыҙ икән, Bitcoin (төп һәм иң күп ҡулланылған криптовалюта) һатып алыу һәм иғәнә яһау өсөн %(options)s ҡулланыуҙы тәҡдим итәбеҙ."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "PayPal US ҡулланып иғәнә яһау өсөн, беҙ PayPal Crypto ҡулланасаҡбыҙ, был беҙгә аноним булып ҡалырға мөмкинлек бирә. Был ысулды өйрәнеү өсөн ваҡыт бүлгәнегеҙ өсөн рәхмәт, сөнки был беҙгә ҙур ярҙам буласаҡ."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "PayPal ҡулланып иғәнә яһағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Cash App ҡулланып иғәнә яһағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Әгәр һеҙҙә Cash App бар икән, был иғәнә яһауҙың иң еңел ысулы!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Иғәнә күләме %(amount)s аҫтында булғанда, Cash App %(fee)s комиссияһын ала. %(amount)s йәки күберәк булғанда, был бушлай!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Revolut ҡулланып иғәнә яһағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Әгәр һеҙҙә Revolut бар икән, был иғәнә яһауҙың иң еңел ысулы!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Кредит йәки дебет картаһы менән иғәнә яһағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay һәм Apple Pay шулай уҡ эшләргә мөмкин."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Иғәнәләрҙең бәләкәй булыуын иҫәпкә алып, кредит картаһы комиссиялары беҙҙең %(discount)s%% ташламаһын юҡҡа сығара ала, шуға күрә оҙайлы яҙылыуҙарҙы тәҡдим итәбеҙ."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Иғәнәләрҙең бәләкәй булыуын иҫәпкә алып, комиссиялар юғары, шуға күрә оҙайлы яҙылыуҙарҙы тәҡдим итәбеҙ."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Binance менән, һеҙ кредит/дебет картаһы йәки банк иҫәбенән Bitcoin һатып алаһығыҙ, һәм шунан һуң беҙгә шул Bitcoin-ды бүләк итәһегеҙ. Был ысул менән беҙ һеҙҙең бүләктәрегеҙҙе ҡабул иткәндә хәүефһеҙ һәм аноним булып ҡала алабыҙ."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance тиерлек һәр илдә бар, һәм күпселек банк һәм кредит/дебет карталарын хуплай. Был беҙҙең төп тәҡдимебеҙ. Был ысул менән нисек бүләк итеүҙе өйрәнеү өсөн ваҡыт бүлгәнегеҙ өсөн рәхмәт, сөнки был беҙгә бик ярҙам итә."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Үҙегеҙҙең ғәҙәти PayPal аккаунтығыҙҙы ҡулланып иғәнә яһағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Кредит/дебет картаһы, PayPal йәки Venmo ҡулланып бүләк итегеҙ. Киләһе биттә былар араһынан һайлай алаһығыҙ."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Amazon бүләк картаһы ҡулланып бүләк итегеҙ."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Иҫегеҙҙә тотоғоҙ, беҙгә һатыусылар ҡабул иткән суммаға түңәрәкләргә кәрәк (минимум %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>МӨҺИМ:</strong> Беҙ тик Amazon.com-ды ғына хуплайбыҙ, башҡа Amazon веб-сайттарын түгел. Мәҫәлән, .de, .co.uk, .ca, ХУПЛАНМАЙ."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>МӨҺИМ:</strong> Был вариант %(amazon)s өсөн. Әгәр башҡа Amazon сайты ҡулланырға теләһәгеҙ, уны өҫтә һайлағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Был ысул криптовалюта провайдерын аралаш конверсия итеп ҡуллана. Был бер аҙ буталсыҡ булыуы мөмкин, шуға күрә был ысулды тик башҡа түләү ысулдары эшләмәһә генә ҡулланығыҙ. Шулай уҡ был бөтә илдәрҙә лә эшләмәй."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Кредит/дебет картаһы ярҙамында, Alipay ҡушымтаһы аша иғәнә яһағыҙ (өҫтәмә ҡуйыу бик еңел)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Alipay ҡушымтаһын ҡуйығыҙ"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Alipay ҡушымтаһын <a %(a_app_store)s>Apple App Store</a> йәки <a %(a_play_store)s>Google Play Store</a> магазиндарынан ҡуйығыҙ."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Телефон номерығыҙҙы ҡулланып теркәлегеҙ."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Башҡа шәхси мәғлүмәттәр талап ителмәй."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Банк картаһын өҫтәгеҙ"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Яраҡлы: Visa, MasterCard, JCB, Diners Club һәм Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Ҡатмарлыраҡ мәғлүмәт өсөн <a %(a_alipay)s>был ҡулланманы</a> ҡарағыҙ."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Беҙ тура кредит/дебет карталарын ҡабул итә алмайбыҙ, сөнки банктар беҙ менән эшләргә теләмәй. ☹ Әммә башҡа түләү ысулдарын ҡулланып, кредит/дебет карталарын ҡулланыуҙың бер нисә ысулы бар:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon бүләк картаһы"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Кредит/дебет картаһы ҡулланып Amazon.com бүләк карталарын ебәрегеҙ."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay халыҡ-ара кредит/дебет карталарын ҡабул итә. Тулыраҡ мәғлүмәт өсөн <a %(a_alipay)s>был ҡулланманы</a> ҡарағыҙ."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) халыҡ-ара кредит/дебет карталарын хуплай. WeChat ҡушымтаһында, “Мин => Хеҙмәттәр => Кәштә => Картаны өҫтәү” бүлегенә барығыҙ. Әгәр был күренмәһә, “Мин => Көйләүҙәр => Дөйөм => Ҡоралдар => Weixin Pay => Ҡулланыу” бүлегендә уны ҡулланыуҙы мөмкин итегеҙ."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Кредит/дебет карталарын ҡулланып криптовалюта һатып ала алаһығыҙ."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Крипто экспресс хеҙмәттәре"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Экспресс хеҙмәттәр уңайлы, ләкин юғарыраҡ комиссия ала."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Әгәр һеҙ ҙур иғәнә яһарға теләһәгеҙ һәм $5-10 комиссияға ҡаршы булмаһағыҙ, крипто биржаһы урынына быны ҡуллана алаһығыҙ."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Иғәнә битендә күрһәтелгән крипто суммаһын, $USD суммаһын түгел, ебәреүегеҙгә инанығыҙ."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Юғиһә комиссия тотоласаҡ һәм беҙ ағзалығығыҙҙы автоматик эшкәртә алмайбыҙ."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(минимум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(минимум: %(minimum)s в зависимости от страны, без проверки для первой транзакции)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(минимум: %(minimum)s, без проверки для первой транзакции)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(минимум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(минимум: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(минимум: %(minimum)s, без проверки для первой транзакции)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Әгәр ҙә был мәғлүмәт иҫкергән булһа, беҙгә электрон почта аша хәбәр итегеҙ."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Кредит карталары, дебет карталары, Apple Pay һәм Google Pay өсөн, беҙ “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>) ҡулланабыҙ. Уларҙың системаһында, бер “кофе” $5-ҡа тиң, шуға күрә һеҙҙең бүләгегеҙ 5-кә яҡынлаштырыласаҡ."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Нисә ваҡытҡа яҙылырға теләгәнегеҙҙе һайлағыҙ."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 ай"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 ай"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 ай"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 ай"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 ай"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 ай"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 ай"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>жеңілдіктерден кейін <span %(span_discount)s></span></div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Бұл төлем әдісі үшін ең аз мөлшер %(amount)s қажет. Басқа мерзімді немесе төлем әдісін таңдаңыз."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Иғәнә"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Бұл төлем әдісі үшін ең көп мөлшер %(amount)s ғана рұқсат етіледі. Басқа мерзімді немесе төлем әдісін таңдаңыз."

#, fuzzy
msgid "page.donate.login2"
msgstr "Мүше болу үшін, өтінеміз <a %(a_login)s>Кіру немесе Тіркелу</a>. Қолдауыңызға рахмет!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Таңдаулы криптовалютаны таңдаңыз:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(ең төменгі мөлшер)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(Coinbase-тан Ethereum ебәргәндә ҡулланығыҙ)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(ескерту: жоғары ең төменгі мөлшер)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Бұл қайырымдылықты растау үшін \"Қайырымдылық жасау\" түймесін басыңыз."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Қайырымдылық жасау <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Сіз әлі де төлем кезінде қайырымдылықты болдырмауға болады."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Қайырымдылық бетіне бағытталуда…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Қате орын алды. Бетті қайта жүктеп, қайтадан көріңіз."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / ай"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "1 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "3 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "6 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "12 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "24 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "48 айға"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "96 айға"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "1 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "3 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "6 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "12 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "24 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "48 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "96 айға “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Иғәнә"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Дата: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Дөйөм сумма: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / айына %(duration)s айға, шул иҫәптән %(discounts)s%% ташлама)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Дөйөм сумма: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / айына %(duration)s айға)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Статус: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Идентификатор: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Юйыу"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Һеҙ ысынлап та юйырға теләйһегеҙме? Әгәр ҙә һеҙ инде түләгән булһағыҙ, юймағыҙ."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Эйе, зинһар, юйығыҙ"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Һеҙҙең иғәнәгеҙ кире ҡағылды."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Яңы иғәнә яһарға"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Нимәлер дөрөҫ түгел. Битте яңынан тейәп ҡарағыҙ һәм ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Ҡабаттан заказ биреү"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Һеҙ инде түләгәнһегеҙ. Түләү күрһәтмәләрен ҡабаттан ҡарарға теләһәгеҙ, бында баҫығыҙ:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Иҫке түләү күрһәтмәләрен күрһәтеү"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Иғәнәгеҙ өсөн рәхмәт!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Әгәр ҙә һеҙ әле яҙмаған булһағыҙ, инә өсөн серле асҡысты яҙып ҡуйығыҙ:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Юғиһә, был аккаунтҡа инеү мөмкинлеген юғалтыуығыҙ ихтимал!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Түләү күрһәтмәләре хәҙер иҫкергән. Яңы иғәнә яһарға теләһәгеҙ, өҫтәге “Ҡабаттан заказ биреү” төймәһен ҡулланығыҙ."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Мөһим иҫкәрмә:</strong> Криптовалюта хаҡы ҡырҡа үҙгәреүсән, ҡайһы саҡта хатта бер нисә минут эсендә 20%% тиклем үҙгәрә. Был беҙгә күп түләү провайдерҙары менән эшләгәндә ингән 50-60%% түләүҙәрҙән кәмерәк. <u>Әгәр ҙә һеҙ беҙгә түләгән хаҡығыҙ менән квитанцияны ебәрһәгеҙ, беҙ һеҙҙең аккаунтҡа һайланған ағзалыҡ өсөн кредит бирәсәкбеҙ</u> (квитанция бер нисә сәғәттән иҫкермәгән булһа). Беҙҙе хуплағанығыҙ өсөн бик рәхмәтлебеҙ! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Был иғәнә ваҡыты сыҡҡан. Зинһар, кире ҡағығыҙ һәм яңынан яһағыҙ."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Крипто күрһәтмәләре"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Беҙҙең крипто иҫәптәрҙең береһенә күсереү"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "%(total)s дөйөм суммаһын ошо адресҡа иғәнә итегеҙ:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Paypal аша Bitcoin һатып алығыҙ"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "PayPal ҡушымтаһында йәки веб-сайтында “Crypto” битен табығыҙ. Был ғәҙәттә “Finances” бүлегендә була."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Bitcoin (BTC) һатып алыу өсөн күрһәтмәләрҙе үтәгеҙ. Һеҙгә тик иғәнә итергә теләгән сумма ғына кәрәк, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Bitcoin-ды беҙҙең адресҡа күсереү"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "PayPal ҡушымтаһында йәки веб-сайтында “Bitcoin” битенә күсегеҙ. “Күсерергә” төймәһенә баҫығыҙ %(transfer_icon)s, һәм һуңынан “Ебәрергә”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Bitcoin (BTC) адресын алыу өсөн беҙҙең адресҡа индерегеҙ, һәм %(total)s күләмендәге иғәнәгеҙҙе ебәреү өсөн күрһәтмәләрҙе үтәгеҙ:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Кредит / дебет картаһы буйынса күрһәтмәләр"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Беҙҙең кредит / дебет картаһы битендә иғәнә яһағыҙ"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "%(amount)s иғәнә яһағыҙ <a %(a_page)s>был биттә</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Түбәндәге аҙымлап күрһәтмәләрҙе ҡарағыҙ."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Статус:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Растауҙы көтөү (тикшереү өсөн битте яңыртығыҙ)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Күсереүҙе көтөү (тикшереү өсөн битте яңыртығыҙ)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Ҡалған ваҡыт:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(һеҙ иғәнәне туҡтатып, яңынан булдырырға теләүегеҙ мөмкин)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Таймерҙы яңыртыу өсөн, ябай ғына яңы иғәнә булдырығыҙ."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Статусты яңыртыу"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Әгәр ниндәй ҙә булһа мәсьәләләргә осраһағыҙ, %(email)s адресы буйынса беҙгә мөрәжәғәт итегеҙ һәм мөмкин тиклем күберәк мәғлүмәт (мәҫәлән, скриншоттар) индерегеҙ."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Әгәр һеҙ инде түләгәнһегеҙ икән:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Ҡайһы саҡта раҫлау 24 сәғәткә тиклем ваҡыт ала ала, шуға күрә был битте яңыртығыҙ (хатта ул ваҡыт үткән булһа ла)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "PayPal аша PYUSD монетаһын һатып алығыҙ"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "PYUSD монетаһын (PayPal USD) һатып алыу өсөн күрһәтмәләрҙе үтәгеҙ."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Транзакция өсөн комиссияны ҡаплау өсөн, һеҙ иғәнә яһаған суммаға ҡарағанда %(more)s күберәк һатып алырға тәҡдим итәбеҙ. Ҡалғанын үҙегеҙҙә ҡалдыра алаһығыҙ."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "PayPal ҡушымтаһы йәки сайтының “PYUSD” битенә күсегеҙ. “Күсереү” төймәһенә %(icon)s баҫығыҙ, һәм һуңынан “Ебәреү”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "%(amount)s %(account)s күсерегеҙ"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Купите Bitcoin (BTC) на Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Перейдите на страницу «Bitcoin» (BTC) в Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Купите немного больше (мы рекомендуем %(more)s больше), чем сумма вашего пожертвования (%(amount)s), чтобы покрыть комиссию за транзакцию. Остаток останется у вас."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Переведите Bitcoin на наш адрес"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Нажмите кнопку «Отправить bitcoin», чтобы сделать «вывод». Переключитесь с долларов на BTC, нажав на значок %(icon)s. Введите сумму BTC ниже и нажмите «Отправить». Если возникнут трудности, посмотрите <a %(help_video)s>это видео</a>."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Для небольших пожертвований (менее $25) вам, возможно, придется использовать Rush или Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Купите Bitcoin (BTC) на Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Перейдите на страницу «Crypto» в Revolut, чтобы купить Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Купите немного больше (мы рекомендуем %(more)s больше), чем сумма вашего пожертвования (%(amount)s), чтобы покрыть комиссию за транзакцию. Остаток останется у вас."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Переведите Bitcoin на наш адрес"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Нажмите кнопку «Отправить bitcoin», чтобы сделать «вывод». Переключитесь с евро на BTC, нажав на значок %(icon)s. Введите сумму BTC ниже и нажмите «Отправить». Если возникнут трудности, посмотрите <a %(help_video)s>это видео</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Түбәндәге BTC суммаһын ҡулланыуығыҙға инанығыҙ, <em>евро йәки доллар ТҮГЕЛ</em>, юғиһә беҙ дөрөҫ сумма ала алмайбыҙ һәм ағзалығығыҙҙы автоматик рәүештә раҫлай алмайбыҙ."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Для небольших пожертвований (менее $25) вам, возможно, придется использовать Rush или Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Используйте любой из следующих экспресс-сервисов «кредитная карта в Bitcoin», которые занимают всего несколько минут:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Формаға түбәндәге мәғлүмәтте тултырығыҙ:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin суммаһы:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Зинһар, был <span %(underline)s>төгәл сумма</span>-ны ҡулланығыҙ. Һеҙҙең дөйөм сығымдар кредит картаһы өсөн комиссиялар арҡаһында күберәк булыуы мөмкин. Кескәй сумма өсөн был беҙҙең ташламаны уҙып китергә мөмкин, ҡыҙғанысҡа ҡаршы."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin адресы (тышҡы ҡушымта):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s күрһәтмәләр"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Беҙ тик стандарт версиялы криптомонеталарҙы ғына хуплайбыҙ, экзотик селтәрҙәр йәки монеталар версиялары түгел. Транзакцияны раҫлау өсөн бер сәғәткә тиклем ваҡыт талап ителеүе мөмкин, монетаға бәйле."

msgid "page.donation.crypto_qr_code_title"
msgstr "Түләү өсөн QR кодты сканерлағыҙ"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Түләү деталдәрен тиҙ генә тултырыу өсөн был QR кодты крипто-кошелёк программаһы менән сканерлағыҙ."

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon бүләк картаһы"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Зинһар, беҙгә бүләк картаһын ебәреү өсөн <a %(a_form)s>рәсми Amazon.com формаһын</a> ҡулланығыҙ, түбәндәге электрон почта адресына %(amount)s."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Беҙ башҡа ысулдар менән ебәрелгән бүләк карталарын ҡабул итә алмайбыҙ, <strong>тик рәсми Amazon.com формаһы аша ебәрелгәндәрҙе генә</strong>. Әгәр һеҙ был форманы ҡулланмаһағыҙ, беҙ һеҙҙең бүләк картаһын кире ҡайтара алмайбыҙ."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Төгәл суммаһын индерегеҙ: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Зинһар, үҙегеҙҙең хәбәрегеҙҙе яҙмағыҙ."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Формаға “Кемгә” электрон почта адресы:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Һеҙҙең иҫәп яҙмағыҙға уникаль, уртаҡлашмағыҙ."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Бер тапҡыр ғына ҡулланығыҙ."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Бүләк картаһын көтәбеҙ… (тикшереү өсөн битте яңыртығыҙ)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Бүләк картаһын ебәргәндән һуң, беҙҙең автоматлаштырылған система уны бер нисә минут эсендә раҫлаясаҡ. Әгәр был эшләмәһә, бүләк картаһын ҡабаттан ебәреп ҡарағыҙ (<a %(a_instr)s>инструкциялар</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Әгәр был да эшләмәһә, зинһар, беҙгә электрон почта ебәрегеҙ, һәм Анна уны ҡулдан тикшерәсәк (был бер нисә көн алырға мөмкин), һәм һеҙҙең ҡабаттан ебәреп ҡарағанығыҙҙы телгә алырға онотмағыҙ."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Өлгө:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Иҫәп яҙмаһы исеме йәки һүрәте сәйер күренеүе мөмкин. Борсолмағыҙ! Был иҫәп яҙмалары беҙҙең иғәнә партнерҙары тарафынан идара ителә. Беҙҙең иҫәп яҙмаларыбыҙға хакерҙар үтеп инмәгән."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay инструкциялары"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Alipay аша иғәнә яһау"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "<a %(a_account)s>был Alipay иҫәп яҙмаһын</a> ҡулланып, дөйөм сумма %(total)s иғәнә яһағыҙ"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Әгәр ҙә иғәнә битенә инеү ябылһа, интернетҡа тоташыуҙың башҡа төрөн (мәҫәлән, VPN йәки телефон интернеты) ҡулланып ҡарағыҙ."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Ҡыҙғанысҡа ҡаршы, Alipay битенә йыш ҡына <strong>ҡытай континенталь өлкәһенән</strong> генә инеп була. VPN-ды ваҡытлыса һүндерергә йәки Ҡытай континенталь өлкәһенә (йәки ҡайһы берҙә Гонконгҡа) VPN ҡулланырға кәрәк булыуы мөмкин."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Иғәнә яһағыҙ (QR-кодты сканерлағыҙ йәки төймәгә баҫығыҙ)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "<a %(a_href)s>QR-кодлы иғәнә битен</a> асығыҙ."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Alipay ҡушымтаһы менән QR-кодты сканерлағыҙ, йәки Alipay ҡушымтаһын асыу өсөн төймәгә баҫығыҙ."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Зинһар, сабыр итегеҙ; биттең йөкләнеүе бер аҙ ваҡыт ала ала, сөнки ул Ҡытайҙа урынлашҡан."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat инструкциялары"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>WeChat аша иғәнә яһау"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "<a %(a_account)s>был WeChat иҫәп яҙмаһын</a> ҡулланып, дөйөм сумма %(total)s иғәнә яһағыҙ"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix инструкциялары"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Pix аша иғәнә яһау"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Донат общую сумму %(total)s используя <a %(a_account)s>этот Pix аккаунт"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Отправьте нам квитанцию по электронной почте"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Шәхси тикшереү адресығыҙға квитанция йәки скриншот ебәрегеҙ. Был электрон почта адресын PayPal иғәнәге өсөн ҡулланмағыҙ."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Отправьте квитанцию или скриншот на ваш личный адрес для проверки:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Если курс криптовалюты изменился во время транзакции, обязательно включите квитанцию, показывающую исходный курс. Мы очень ценим ваши усилия по использованию криптовалюты, это нам очень помогает!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Когда вы отправите квитанцию по электронной почте, нажмите эту кнопку, чтобы Анна могла вручную проверить её (это может занять несколько дней):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Да, я отправил квитанцию по электронной почте"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Спасибо за ваш донат! Анна вручную активирует ваше членство в течение нескольких дней."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Что-то пошло не так. Пожалуйста, перезагрузите страницу и попробуйте снова."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Пошаговое руководство"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Некоторые шаги упоминают криптокошельки, но не волнуйтесь, вам не нужно ничего изучать о криптовалюте для этого."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Введите ваш email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Выберите способ оплаты."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Выберите способ оплаты снова."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Выберите “Самостоятельно хостируемый” кошелек."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Нажмите “Я подтверждаю владение”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Вы должны получить квитанцию по электронной почте. Пожалуйста, отправьте её нам, и мы подтвердим ваш донат как можно скорее."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Зинһар, беҙгә мөрәжәғәт иткәнсе, кәмендә <span %(span_hours)s>24 сәғәт</span> көтөгөҙ (һәм был битте яңыртығыҙ)."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Если вы допустили ошибку при оплате, мы не можем сделать возврат, но постараемся исправить ситуацию."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Мои донаты"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Детали донатов не отображаются публично."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Әлегә иғәнәләр юҡ. <a %(a_donate)s>Беренсе иғәнәмде яһарға.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Тағы бер иғәнә яһарға."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Күсереп алынған файлдар"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Тиҙ Партнер Серверҙарынан күсереп алынған файлдар %(icon)s менән билдәләнгән."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Әгәр һеҙ файлды тиҙ һәм әкрен күсереп алыу менән күсереп алған булһағыҙ, ул ике тапҡыр күрһәтеләсәк."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Һуңғы 24 сәғәттәге тиҙ күсереп алыуҙар көндәлек лимитҡа инә."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Бөтә ваҡыттар UTC буйынса."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Күсереп алынған файлдар йәмәғәтселеккә күрһәтелмәй."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Әлегә файлдар күсереп алынмаған."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Һуңғы 18 сәғәт"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Элек"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Аккаунт"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Системаға инеү / Теркәлеү"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Аккаунт ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Йәмәғәт профиле: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Секрет асҡыс (уртаҡлашмағыҙ!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "күрһәтергә"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Әғзалыҡ: <strong>%(tier_name)s</strong> %(until_date)s тиклем <a %(a_extend)s>(оҙайтырға)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Әғзалыҡ: <strong>Юҡ</strong> <a %(a_become)s>(әғза булырға)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Тиҙ күсереп алыуҙар (һуңғы 24 сәғәт): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "ниндәй күсереп алыуҙар?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Эксклюзив Telegram төркөмө: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Беҙгә ҡушылығыҙ!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Төркөмөбөҙгә ҡушылыу өсөн <a %(a_tier)s>юғарыраҡ кимәлгә</a> күсегеҙ."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Әгәр ағзалығығыҙҙы юғарыраҡ кимәлгә күтәрергә теләһәгеҙ, Анна менән %(email)s бәйләнешкә сығығыҙ."

#, fuzzy
msgid "page.contact.title"
msgstr "Бәйләнеш электрон почтаһы"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Бер нисә ағзалыҡты берләштерә алаһығыҙ (24 сәғәт эсендә тиҙ йөкләүҙәр бергә ҡушыласаҡ)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Йәмәғәт профиле"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Йөкләнгән файлдар"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Минең иғәнәләрем"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Сығыу"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Һеҙ хәҙер сығыу яһанығыҙ. Ҡабат инеү өсөн битте яңыртығыҙ."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Нимәлер дөрөҫ булманы. Битте яңыртып, ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Теркәлеү уңышлы! Һеҙҙең серле асҡысығыҙ: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Был асҡысты һаҡлап ҡуйығыҙ. Әгәр уны юғалтаһығыҙ, иҫәп яҙмағыҙға инеү мөмкинлеген юғалтаһығыҙ."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Һаҡлау.</strong> Асҡысығыҙҙы кире ҡайтарыу өсөн был битте һаҡлай алаһығыҙ.</li><li %(li_item)s><strong>Йөкләү.</strong> Асҡысығыҙҙы йөкләү өсөн <a %(a_download)s>был һылтанмаға</a> баҫығыҙ.</li><li %(li_item)s><strong>Пароль менеджеры.</strong> Асҡысты аҫта индергәндә пароль менеджерын ҡулланығыҙ.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Инеү өсөн серле асҡысығыҙҙы индерегеҙ:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Серле асҡыс"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Инеү"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Яңылыш серле асҡыс. Асҡысығыҙҙы тикшерегеҙ һәм ҡабатлап ҡарағыҙ, йәки аҫта яңы иҫәп яҙмаһын теркәгеҙ."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Асҡысығыҙҙы юғалтмағыҙ!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Әлегә иҫәп яҙмағыҙ юҡмы?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Яңы иҫәп яҙмаһын теркәү"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Әгәр асҡысығыҙҙы юғалтһағыҙ, зинһар, <a %(a_contact)s>беҙҙең менән бәйләнешкә сығығыҙ</a> һәм мөмкин тиклем күберәк мәғлүмәт бирегеҙ."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Беҙҙең менән бәйләнешкә сығыу өсөн ваҡытлыса яңы иҫәп яҙмаһы булдырырға тура килеүе мөмкин."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Иҫке электрон почтаға нигеҙләнгән иҫәп яҙмаһы? <a %(a_open)s>Бында электрон почтағыҙҙы индерегеҙ</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Исемлек"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "мөхәррирләү"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Һаҡлау"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Һаҡланды. Зинһар, битте яңыртығыҙ."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Нимәлер дөрөҫ булманы. Зинһар, ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "%(by)s буйынса исемлек, <span %(span_time)s>%(time)s</span> булдырылған"

#, fuzzy
msgid "page.list.empty"
msgstr "Исемлек буш."

#, fuzzy
msgid "page.list.new_item"
msgstr "Файл табып, “Исемлектәр” вкладкаһын асып, был исемлеккә өҫтәгеҙ йәки алып ташлағыҙ."

#, fuzzy
msgid "page.profile.title"
msgstr "Профиль"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Профиль табылманы."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "мөхәррирләү"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Күрһәтелгән исемегеҙҙе үҙгәртегеҙ. Идентификаторығыҙ ( “#” билдәһенән һуңғы өлөшө) үҙгәртелә алмай."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Һаҡлау"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Һаҡланды. Зинһар, битте яңыртығыҙ."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Нимәлер дөрөҫ булманы. Зинһар, ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.profile.created_time"
msgstr "<span %(span_time)s>%(time)s</span> булдырылған профиль"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Исемлектәр"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Бер исемлекләр юк"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Файл табып, “Исемлекләр” вкладкасын ачу белән яңа исемлек булдыру."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Авторлыҡ хоҡуҡтарын реформалау милли хәүефһеҙлек өсөн кәрәк"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Ҡыҫҡаса: Ҡытай LLM-дары (DeepSeek индереп) минең законһыҙ китаптар һәм мәҡәләләр архивында — донъялағы иң ҙуры — өйрәнелгән. Көнбайыш милли хәүефһеҙлек мәсьәләһе булараҡ авторлыҡ хоҡуҡтарын үҙгәртеп ҡорорға тейеш."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "TorrentFreak тарафынан яҙылған мәҡәләләр: <a %(torrentfreak)s>беренсе</a>, <a %(torrentfreak_2)s>икенсе</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Күптән түгел генә \"күңелкәй-китапханалар\" юҡҡа сыға ине. Ғәйәт ҙур законһыҙ академик мәҡәләләр архивы Sci-Hub яңы эштәр ҡабул итеүҙе туҡтатты, сөнки уларға ҡаршы дәғүәләр бирелде. \"Z-Library\", китаптарҙың иң ҙур законһыҙ китапханаһы, уның ғәйепләнеүселәре авторлыҡ хоҡуҡтарын боҙоуҙа ғәйепләнеп ҡулға алынды. Улар ғәжәп итеп ҡулға алыныуҙан ҡотолдо, әммә уларҙың китапханаһы хәүеф аҫтында ҡала."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Z-Library ябылыуға дусар булғанда, мин уның бөтә китапханаһын резервҡа күсереп ҡуйғайным һәм уны урынлаштырыу өсөн платформа эҙләй инем. Был мине Аннаның Архивын башларға этәрҙе: был башланғыстарҙың миссияһын дауам итеү. Шул ваҡыттан алып беҙ донъялағы иң ҙур күләгәле китапханаға әйләндек, 140 миллиондан ашыу авторлыҡ хоҡуҡлы текстарҙы төрлө форматтарҙа — китаптар, академик мәҡәләләр, журналдар, гәзиттәр һәм башҡаларҙы — урынлаштырҙыҡ."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Минең команда һәм мин идеологтар. Беҙ был файлдарҙы һаҡлау һәм урынлаштырыу әхлаҡи яҡтан дөрөҫ тип иҫәпләйбеҙ. Донъя буйлап китапханалар финанслауҙы ҡыҫҡарта, һәм беҙ кешелектең мираҫын корпорацияларға ышанып тапшыра алмайбыҙ."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Шунан һуң AI килде. LLM төҙөгән бөтә ҙур компаниялар тиерлек беҙҙең мәғлүмәттәрҙә өйрәнергә беҙгә мөрәжәғәт итте. Күпселеге (әммә барыһы ла түгел!) АҠШ-та урынлашҡан компаниялар беҙҙең эштең законһыҙ булыуын аңлағас, кире уйланы. Ҡытай компаниялары, киреһенсә, беҙҙең коллекцияны дәртләнеп ҡабул итте, уның законлылығына борсолмай. Был Ҡытайҙың бөтә төп халыҡ-ара авторлыҡ хоҡуҡтары килешеүҙәренә ҡул ҡуйыусы булараҡ ролен иҫәпкә алғанда иғтибарға лайыҡ."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Беҙ яҡынса 30 компанияға юғары тиҙлекле инеү мөмкинлеге бирҙек. Уларҙың күбеһе LLM компаниялары, ә ҡайһы берҙәре мәғлүмәт брокерҙары, улар беҙҙең коллекцияны һатасаҡ. Күпселеге Ҡытайҙан, әммә беҙ шулай уҡ АҠШ, Европа, Рәсәй, Көньяҡ Корея һәм Япония компаниялары менән дә эшләнек. DeepSeek <a %(arxiv)s>танылды</a>, тип элекке версияһы беҙҙең коллекцияның бер өлөшөндә өйрәнелгән, әммә уларҙың һуңғы моделе тураһында бик аҙ мәғлүмәт бар (моғайын, ул да беҙҙең мәғлүмәттәрҙә өйрәнелгәндер)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Көнбайыш LLM-дар ярышында, һәм ахыр сиктә, AGI-ҙа алда ҡалырға теләһә, ул авторлыҡ хоҡуҡтары буйынса үҙ позицияһын яңынан ҡарарға тейеш, һәм тиҙ арала. Беҙҙең әхлаҡи дәлилдәребеҙ менән килешәһегеҙме, юҡмы, был хәҙер иҡтисад һәм хатта милли хәүефһеҙлек мәсьәләһе булып китә. Бөтә көс блоктары яһалма супер-ғалимдар, супер-хакерҙар һәм супер-армиялар төҙөй. Мәғлүмәт иреклеге был илдәр өсөн йәшәү мәсьәләһенә әйләнә — хатта милли хәүефһеҙлек мәсьәләһенә."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Беҙҙең команда бөтә донъя буйлап, һәм беҙҙә айырым бер йүнәлеш юҡ. Әммә беҙ авторлыҡ хоҡуҡтары ҡаты булған илдәрҙе был экзистенциаль хәүефте уларҙы реформалау өсөн ҡулланырға саҡырабыҙ. Шулай итеп, нимә эшләргә?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Беҙҙең беренсе тәҡдим ябай: авторлыҡ хоҡуҡтары срогын ҡыҫҡартыу. АҠШ-та авторлыҡ хоҡуҡтары авторҙың үлеменән һуң 70 йылға бирелә. Был абсурд. Беҙ быны патенттар менән бер рәткә килтерә алабыҙ, улар ғариза бирелгәндән һуң 20 йылға бирелә. Был китаптар, мәҡәләләр, музыка, сәнғәт һәм башҡа ижади эштәр авторҙарының үҙ хеҙмәттәре өсөн тулыһынса компенсация алыу өсөн етерлек ваҡыт булырға тейеш (кино адаптациялары кеүек оҙайлы проекттарҙы индереп)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Шунан һуң, иң кәмендә, сәйәсәтселәр текстарҙы күпләп һаҡлау һәм таратыу өсөн айырым ҡағиҙәләр индерергә тейеш. Әгәр айырым клиенттарҙан юғалған килем төп борсолоу булһа, шәхси кимәлдә таратыу тыйылырға мөмкин. Үҙ сиратында, киң репозиторийҙар менән идара итеүгә һәләтле булғандар — LLM-дар өйрәткән компаниялар, китапханалар һәм башҡа архивтар менән бергә — был айырым ҡағиҙәләр менән ҡапланасаҡ."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Ҡайһы бер илдәр бының бер версияһын инде эшләй. TorrentFreak <a %(torrentfreak)s>хәбәр итте</a>, тип Ҡытай һәм Япония үҙҙәренең авторлыҡ хоҡуҡтары закондарына AI айырым ҡағиҙәләр индергән. Был халыҡ-ара килешеүҙәр менән нисек бәйләнештә булыуы беҙгә билдәһеҙ, әммә был уларҙың эске компанияларына ҡаплау бирә, һәм был беҙ күргәнде аңлата."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Аннаның Архивына килгәндә — беҙ әхлаҡи ышанысҡа нигеҙләнгән ер аҫты эшебеҙҙе дауам итәсәкбеҙ. Әммә беҙҙең иң ҙур теләгебеҙ — яҡтылыҡҡа сығыу һәм үҙебеҙҙең йоғонтообоҙҙо законлы рәүештә арттырыу. Зинһар, авторлыҡ хоҡуҡтарын реформалағыҙ."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "TorrentFreak тарафынан яҙылған мәҡәләләрҙе уҡығыҙ: <a %(torrentfreak)s>беренсе</a>, <a %(torrentfreak_2)s>икенсе</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "$10,000 ISBN визуализация премияһы еңеүселәре"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Ҡыҫҡаса: $10,000 ISBN визуализация премияһына ғәжәйеп эштәр алдыҡ."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Бер нисә ай элек беҙ үҙебеҙҙең мәғлүмәттәрҙең ISBN киңлеген күрһәтеү өсөн иң яҡшы визуализация яһау өсөн <a %(all_isbns)s>$10,000 премия</a> иғлан иттек. Беҙ ниндәй файлдарҙы архивлағаныбыҙҙы/архивламағаныбыҙҙы күрһәтеүгә баҫым яһаныҡ, һәм һуңынан ISBN-дарҙы нисә китапхана тотоуын (һирәклек үлсәме) тасуирлаған мәғлүмәттәр йыйылмаһын өҫтәнек."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Яуаптар беҙҙе таң ҡалдырҙы. Шул тиклем күп ижадилыҡ булды. Ҡатнашҡан һәр кемгә ҙур рәхмәт: һеҙҙең энергия һәм дәрт йоҡтороусы!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Ахыр сиктә беҙ түбәндәге һорауҙарға яуап бирергә теләнек: <strong>донъяла ниндәй китаптар бар, беҙ күпмеһен инде архивланыҡ, һәм артабан ниндәй китаптарға иғтибар итергә тейешбеҙ?</strong> Был һорауҙарға күп кешенең иғтибар итеүе бик яҡшы."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Беҙ үҙебеҙ ябай визуализациянан башланыҡ. 300 кб-тан кәмерәк күләмдә, был һүрәт кешелек тарихында иң ҙур тулы асыҡ \"китаптар исемлеге\"н ҡыҫҡа ғына итеп күрһәтә:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Бөтә ISBN-дар"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Аннаның Архивындағы файлдар"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO-лары"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC мәғлүмәттәре сығыуы"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID-тары"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost-тың электрон китаптар индексы"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Китаптар"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Интернет Архивы"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Глобаль Нәшриәтселәр Реестры"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Рәсәй Дәүләт Китапханаһы"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Трантор Империя Китапханаһы"

#, fuzzy
msgid "common.back"
msgstr "Kembali"

#, fuzzy
msgid "common.forward"
msgstr "Maju"

#, fuzzy
msgid "common.last"
msgstr "Terakhir"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Зинһар, күберәк мәғлүмәт өсөн <a %(all_isbns)s>төп блог яҙмаһын</a> ҡарағыҙ."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Беҙ быларҙы яҡшыртыу өсөн бер һынау тәҡдим иттек. Беренсе урын өсөн $6,000, икенсе урын өсөн $3,000, һәм өсөнсө урын өсөн $1,000 бүләк бирергә вәғәҙә иттек. Күп һанлы яуаптар һәм ғәжәйеп тәҡдимдәр арҡаһында, беҙ бүләк фондын бер аҙ арттырырға һәм дүрт өсөнсө урынға $500 бирергә ҡарар иттек. Еңеүселәр түбәндә күрһәтелгән, әммә бөтә тәҡдимдәрҙе <a %(annas_archive)s>бында</a> ҡарарға йәки беҙҙең <a %(a_2025_01_isbn_visualization_files)s>берләштерелгән торрентты</a> күсереп алырға онотмағыҙ."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Беренсе урын $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Был <a %(phiresky_github)s>тәҡдим</a> (<a %(annas_archive_note_2951)s>Gitlab комментарийы</a>) беҙ теләгән бөтә нәмәне һәм унан да күберәк тәҡдим итә! Беҙ айырыуса ғәжәйеп гибкость визуализация варианттарын (хатта махсус шейдерҙарҙы хуплау) оҡшаттыҡ, әммә тулы алдан билдәләнгән параметрҙар исемлеге менән. Шулай уҡ беҙ бөтә нәмәнең тиҙ һәм шым эшләүен, ябай имплементацияны (хатта backend юҡ), аҡыллы мини-картаны һәм уларҙың <a %(phiresky_github)s>блог яҙмаһында</a> киң аңлатманы оҡшаттыҡ. Ғәжәйеп эш, һәм лайыҡлы еңеүсе!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Икенсе урын $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Тағы бер ғәжәйеп <a %(annas_archive_note_2913)s>тәҡдим</a>. Беренсе урын кеүек гибкость түгел, әммә беҙ уның макро кимәлдәге визуализацияһын беренсе урындан өҫтөн күрҙек (кеңлек тултырыусы кривая, сиктәр, яҙыу, билдәләү, панорама һәм зум). Джо Дэвис <a %(annas_archive_note_2971)s>комментарийы</a> беҙгә тәьҫир итте:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Идеаль квадраттар һәм тура мөйөштәр математик яҡтан ҡәнәғәтләндерә, әммә улар картала контекстта өҫтөнлөклө локаллек тәҡдим итмәй. Мин был Хилберт йәки классик Мортондың асимметрияһы етешһеҙлек түгел, ә үҙенсәлек тип иҫәпләйем. Италияның билдәле итек формаһындағы контуры картаға шунда уҡ танылыу биргән кеүек, был криваяларҙың уникаль \"ғәжәплектәре\" когнитив ориентирҙар булып хеҙмәт итә ала. Был айырымлыҡтар киңлек хәтерен яҡшырта һәм ҡулланыусыларға үҙҙәрен ориентирларға ярҙам итә ала, бәлки, билдәле төбәктәрҙе табыуҙы йәки үрнәктәрҙе күреүҙе еңеләйтә.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Һәм визуализациялау һәм рендеринг өсөн күп варианттар, шулай уҡ ғәжәйеп шым һәм интуитив интерфейс. Ышаныслы икенсе урын!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Өсөнсө урын $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Был <a %(annas_archive_note_2940)s>тәҡдимдә</a> беҙ төрлө ҡараштарҙы, айырыуса сағыштырыу һәм нәшриәт ҡараштарын бик оҡшаттыҡ."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Өсөнсө урын $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Иң шым интерфейс булмаһа ла, был <a %(annas_archive_note_2917)s>тәҡдим</a> күп талаптарҙы үтәй. Беҙ айырыуса уның сағыштырыу функцияһын оҡшаттыҡ."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Өсөнсө урын $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Беренсе урын кеүек, был <a %(annas_archive_note_2975)s>тәҡдим</a> беҙҙе гибкость менән таң ҡалдырҙы. Ахырҙа, был визуализация ҡоралы өсөн иң яҡшыһы: көслө ҡулланыусылар өсөн максималь гибкость, уртаса ҡулланыусылар өсөн ябайлыҡты һаҡлап."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Өсөнсө урын $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Бүләк алған һуңғы <a %(annas_archive_note_2947)s>тәҡдим</a> бик ябай, әммә беҙгә бик оҡшаған уникаль функциялар бар. Уларҙың күпме datasets билдәле ISBN-ды популярлыҡ/ышаныслыҡ үлсәме итеп күрһәтеүен оҡшаттыҡ. Шулай уҡ сағыштырыу өсөн үтә күренмәле слайдер ҡулланыуҙың ябайлығы, әммә һөҙөмтәлелеге беҙгә бик оҡшаны."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Иҫтәлекле идеялар"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Беҙ айырыуса оҡшаған ҡайһы бер идеялар һәм имплементациялар:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Бейәлек өсөн күккә олғашҡан биналар"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Тере статистика"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Аннотациялар һәм тере статистика"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Үҙенсәлекле карта күренеше һәм фильтрҙар"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Шәп стандарт төҫ схемаһы һәм йылылыҡ картаһы."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Datasets-ты тиҙ сағыштырыу өсөн еңел алмаштырыу."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Матур яҙыуҙар."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Китаптар һаны менән масштаб һыҙығы."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Datasets-ты сағыштырыу өсөн күп һанлы слайдерҙар, әйтерһең дә, һеҙ DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Беҙ оҙаҡ дауам итә ала инек, ләкин бында туҡтайыҡ. Бөтә тәҡдимдәрҙе <a %(annas_archive)s>бында</a> ҡарағыҙ, йәки беҙҙең <a %(a_2025_01_isbn_visualization_files)s>берләштерелгән торрентты</a> күсереп алығыҙ. Күп тәҡдимдәр, һәм һәр береһе үҙенсәлекле ҡараш килтерә, UI йәки ғәмәлгә ашырыуҙа булһынмы."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Беҙ, иң кәмендә, беренсе урын алған тәҡдимде төп веб-сайтыбыҙға индерәсәкбеҙ, һәм, бәлки, ҡайһы бер башҡаларын да. Шулай уҡ иң һирәк китаптарҙы билдәләү, раҫлау һәм архивлау процесын ойоштороу тураһында уйлай башланыҡ. Был йәһәттән тағы ла күберәк мәғлүмәт буласаҡ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Ҡатнашҡан һәр кемгә рәхмәт. Күп кешенең хәстәрлек күреүе ғәжәйеп."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Йөрәктәребеҙ рәхмәт менән тулы."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Бөтә ISBN-дарҙы визуализациялау — 2025-01-31-гә $10,000 бүләк"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Был һүрәт кешелек тарихында йыйылған иң ҙур асыҡ \"китаптар исемлеге\"н күрһәтә."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Был һүрәт 1000×800 пиксель. Һәр пиксель 2,500 ISBN-ды күрһәтә. Әгәр ISBN өсөн файл бар икән, беҙ ул пикселде йәшелерәк итәбеҙ. Әгәр ISBN бирелгәнен беләбеҙ, ләкин тап килгән файл юҡ икән, уны ҡыҙылыраҡ итәбеҙ."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "300kb-тан кәмерәк күләмдә, был һүрәт кешелек тарихында йыйылған иң ҙур асыҡ \"китаптар исемлеге\"н ҡыҫҡа ғына итеп күрһәтә (тулыһынса ҡыҫылғанда бер нисә йөҙ ГБ)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ул шулай уҡ күрһәтә: китаптарҙы резервлауҙа күп эш ҡалған (беҙҙә тик 16% ғына)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Фон"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Аннаның Архивы барлық адамзат білімін сақтап қалу миссиясын, әлі де бар кітаптарды білмей қалай жүзеге асыра алады? Бізге TODO тізімі қажет. Мұны картаға түсірудің бір жолы - 1970 жылдардан бастап әрбір жарияланған кітапқа (көптеген елдерде) тағайындалған ISBN нөмірлері арқылы."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Барлық ISBN тағайындауларын білетін орталық орган жоқ. Оның орнына, бұл таратылған жүйе, мұнда елдер нөмірлердің диапазондарын алады, содан кейін оларды ірі баспагерлерге, ал олар кіші баспагерлерге бөлетін болады. Соңында жеке нөмірлер кітаптарға тағайындалады."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Біз ISBN-дерді <a %(blog)s>екі жыл бұрын</a> ISBNdb-ны жинаумен картаға түсіруді бастадық. Содан бері біз көптеген басқа metadata көздерін, мысалы, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby және басқаларын жинадық. Толық тізімді Аннаның Архивіндегі “Datasets” және “Torrents” беттерінен табуға болады. Қазір бізде әлемдегі ең үлкен толық ашық, оңай жүктелетін кітап metadata (және осылайша ISBN) жинағы бар."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Біз неге сақтау маңызды екенін және неге біз қазір маңызды кезеңде екенімізді <a %(blog)s>кеңінен жаздық</a>. Енді сирек, назардан тыс қалған және ерекше қауіп-қатерге ұшыраған кітаптарды анықтап, оларды сақтауымыз керек. Әлемдегі барлық кітаптар туралы жақсы metadata болуы осыған көмектеседі."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Көрнекілеу"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Шолу суретінен басқа, біз алған жеке datasets-терді де қарастыра аламыз. Оларды ауыстыру үшін ашылмалы мәзір мен батырмаларды пайдаланыңыз."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Бұл суреттерде көруге болатын көптеген қызықты үлгілер бар. Неліктен әртүрлі масштабтарда болатын сызықтар мен блоктардың кейбір тұрақтылығы бар? Бос аймақтар қандай? Неліктен кейбір datasets-тер соншалықты топталған? Бұл сұрақтарды оқырманға жаттығу ретінде қалдырамыз."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 сыйақы"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Мұнда зерттеуге көп нәрсе бар, сондықтан біз жоғарыдағы визуализацияны жақсарту үшін сыйақы жариялаймыз. Көптеген сыйақыларымыздан айырмашылығы, бұл уақытпен шектелген. Сіз өзіңіздің ашық бастапқы кодыңызды 2025-01-31 (23:59 UTC) дейін <a %(annas_archive)s>жіберуіңіз</a> керек."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Ең жақсы жіберілім $6,000 алады, екінші орын $3,000, ал үшінші орын $1,000 алады. Барлық сыйақылар Monero (XMR) арқылы беріледі."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Төменде минималды критерийлер берілген. Егер ешбір жіберілім критерийлерге сәйкес келмесе, біз әлі де кейбір сыйақыларды бере аламыз, бірақ бұл біздің қалауымыз бойынша болады."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Осы репозиторийді тармақтап, осы блог жазбасының HTML-ін өңдеңіз (біздің Flask backend-тен басқа басқа backend-терге рұқсат етілмейді)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Жоғарыдағы суретті ISBN-дерге дейін тегіс масштабтауға мүмкіндік беріңіз. ISBN-дерді басу Аннаның Архивіндегі metadata бетіне немесе іздеуге апаруы керек."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Сіз әлі де барлық әртүрлі datasets-тер арасында ауыса алуыңыз керек."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Ел диапазондары мен баспагер диапазондарының үстіне апарғанда бөлектелуі керек. Мысалы, ел туралы ақпарат үшін <a %(github_xlcnd_isbnlib)s>isbnlib ішіндегі data4info.py</a> және баспагерлер үшін біздің “isbngrp” жинауымызды (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>) пайдалана аласыз."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Бұл жұмыс үстелінде және мобильді құрылғыларда жақсы жұмыс істеуі керек."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Қосымша ұпайлар үшін (бұл тек идеялар — шығармашылығыңызды еркін жіберіңіз):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Пайдалануға ыңғайлылық пен көрнекі көрінісіне үлкен назар аударылады."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "ISBN-дерге жақындағанда, мысалы, атауы мен авторы сияқты нақты metadata көрсетіңіз."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Жақсырақ кеңістік толтыру қисығы. Мысалы, зиг-заг, бірінші қатарда 0-ден 4-ке дейін, содан кейін екінші қатарда кері бағытта 5-тен 9-ға дейін — рекурсивті түрде қолданылған."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Төрлө йәки көйләнгән төҫ схемалары."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Datasets сағыштырыу өсөн махсус ҡараштар."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Мәсьәләләрҙе отладкалау ысулдары, мәҫәлән, яҡшы тап килмәгән башҡа metadata (мәҫәлән, бик төрлө исемдәр)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "ISBN йәки диапазондар буйынса аңлатмалар менән һүрәттәрҙе аннотациялау."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Һирәк йәки хәүеф аҫтындағы китаптарҙы билдәләү өсөн ниндәйҙер эвристик ысулдар."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Һеҙ уйлап сығара алған теләһә ниндәй ижади идеялар!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Һеҙ минималь критерийҙарҙан тулыһынса ситкә китә алаһығыҙ һәм бөтөнләй башҡа визуализация эшләй алаһығыҙ. Әгәр ул ысынлап та ғәжәйеп булһа, был премияға дәғүә итә, ләкин беҙҙең ҡарамаҡта."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Үҙегеҙҙең форкланған репоға, берләштереү һорауына йәки айырмаға һылтанма менән <a %(annas_archive)s>был мәсьәләгә</a> аңлатма ебәреп, тәҡдимдәр яһағыҙ."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Код"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Был һүрәттәрҙе булдырыу өсөн код, шулай уҡ башҡа миҫалдар <a %(annas_archive)s>был директорияла</a> табырға мөмкин."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Беҙ 75МБ (ҡыҫылған) тирәһе кәрәкле ISBN мәғлүмәттәре менән компактлы мәғлүмәт форматын уйлап таптыҡ. Мәғлүмәт форматының тасуирламаһы һәм уны булдырыу өсөн код <a %(annas_archive_l1244_1319)s>бында</a> табырға мөмкин. Премия өсөн быны ҡулланыу мотлаҡ түгел, ләкин башлау өсөн иң уңайлы формат булыуы ихтимал. Һеҙ беҙҙең metadata-ны теләгәнсә үҙгәртә алаһығыҙ (ләкин бөтә кодығыҙ асыҡ сығанаҡлы булырға тейеш)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Беҙ һеҙҙең нимә уйлап сығарғанығыҙҙы күрергә түҙемһеҙлек менән көтәбеҙ. Уңыштар!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Аннаның Архив Контейнерҙары (AAC): донъяның иң ҙур күләгә китапханаһынан сығарылыштарҙы стандартлаштырыу"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Аннаның Архивы донъяның иң ҙур күләгә китапханаһына әйләнде, һәм беҙгә сығарылыштарыбыҙҙы стандартлаштырыу талап ителә."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Аннаның Архивы</a> донъяның иң ҙур күләгә китапханаһына әйләнде, һәм уның масштабы буйынса тулыһынса асыҡ сығанаҡлы һәм асыҡ мәғлүмәтле берҙән-бер күләгә китапханаһы. Түбәндә беҙҙең Datasets битенән таблица (бик аҙ үҙгәртелгән):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Беҙ быны өс ысул менән башҡарҙыҡ:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Мәғлүмәттәре асыҡ булған күләгә китапханаларын (мәҫәлән, Sci-Hub һәм Library Genesis) көйләү."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Асығыраҡ булырға теләгән, ләкин быға ваҡыттары йәки ресурстары булмаған күләгә китапханаларына ярҙам итеү (мәҫәлән, Libgen комикстар коллекцияһы)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Күпләп бүлешергә теләмәгән китапханаларҙы (мәҫәлән, Z-Library) ҡырып йыйыу."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Өсөн (2) һәм (3) беҙ хәҙер үҙебеҙҙең күп һанлы торренттар коллекцияһын (100-ләгән ТБ) идара итәбеҙ. Әлегә тиклем беҙ был коллекцияларҙы бер тапҡыр ҡулланыла торған итеп ҡараныҡ, йәғни һәр коллекция өсөн махсус инфраструктура һәм мәғлүмәттәрҙе ойоштороу. Был һәр сығарылышҡа ҙур өҫтәмә сығымдар өҫтәй һәм incremental сығарылыштарҙы башҡарыуҙы айырыуса ауырлаштыра."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Шуға күрә беҙ сығарылыштарыбыҙҙы стандартлаштырырға ҡарар иттек. Был техник блог яҙмаһы, унда беҙ үҙебеҙҙең стандартты индерәбеҙ: <strong>Аннаның Архив Контейнерҙары</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Дизайн маҡсаттары"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Беҙҙең төп ҡулланыу осрағы - төрлө булған коллекцияларҙан файлдар һәм уларға бәйле metadata таратыу. Беҙҙең иң мөһим ҡараштар:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Төрлө форматтағы файлдар һәм metadata, мөмкин тиклем оригинал форматҡа яҡын."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Сығанаҡ китапханаларҙа төрлө идентификаторҙар, хатта идентификаторҙарҙың булмауы."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Metadata һәм файл мәғлүмәттәре айырым сығарылыштары, йәки тик metadata сығарылыштары (мәҫәлән, беҙҙең ISBNdb сығарылышы)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Торренттар аша таратыу, ләкин башҡа таратыу ысулдары мөмкинлеге менән (мәҫәлән, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Үҙгәрешһеҙ яҙмалар, сөнки беҙ торренттарыбыҙҙың мәңге йәшәйәсәген фаразларға тейешбеҙ."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Incremental сығарылыштар / өҫтәлмә сығарылыштар."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Машина уҡый һәм яҙа ала, уңайлы һәм тиҙ, айырыуса беҙҙең стек өсөн (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Кеше тарафынан тикшереүе еңел, ләкин был машина уҡый алыуҙан һуңғы урында тора."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Стандарт арендаланған seedbox менән коллекцияларҙы еңел тулыландырыу."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Икеле мәғлүмәттәр Nginx кеүек веб-серверҙар тарафынан тура хеҙмәтләндерелә ала."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Кайһы бер маҡсаттар:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Файлдарҙы дискта ҡулдан еңел йөрөтөү йәки эшкәртеүһеҙ эҙләү мөмкинлеге тураһында беҙ хәстәрлек күрмәйбеҙ."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Беҙ булған китапхана программаһы менән тура килеү тураһында хәстәрлек күрмәйбеҙ."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Беҙҙең коллекцияны торренттар ярҙамында тулыландырыу һәр кем өсөн еңел булырға тейеш булһа ла, файлдарҙы техник белем һәм йөкләмә булмайынса ҡулланыу мөмкин түгел тип көтәбеҙ."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Аннаның Архивы асыҡ сығанаҡлы булғанлыҡтан, беҙ үҙ форматыбыҙҙы туранан-тура ҡулланырға теләйбеҙ. Эҙләү индексыбыҙҙы яңыртҡанда, беҙ тик асыҡтан-асыҡ булған юлдарға ғына мөрәжәғәт итәбеҙ, шуның менән китапханабыҙҙы форклаған һәр кем тиҙ арала эш башлай ала."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Стандарт"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Һуңында, беҙ сағыштырмаса ябай стандартҡа килештек. Ул бик ирекле, нормаларға ярашлы түгел һәм эш барышында."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Аннаның Архив Контейнеры) — <strong>metadata</strong> һәм ихтыяр буйынса <strong>binary data</strong> ингән бер элемент, уларҙың икеһе лә үҙгәрешһеҙ. Уның глобаль уникаль идентификаторы бар, <strong>AACID</strong> тип атала."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Коллекция.</strong> Һәр AAC коллекцияға ҡарай, ул, билдәләмә буйынса, семантик яҡтан ярашлы AAC-тар исемлеге. Тимәк, әгәр metadata форматында мөһим үҙгәреш индерһәгеҙ, яңы коллекция булдырырға кәрәк."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“яҙмалар” һәм “файлдар” коллекциялары.</strong> Ғәҙәттә, “яҙмалар” һәм “файлдар”ҙы айырым коллекциялар итеп сығарыу уңайлы, уларҙы төрлө график буйынса сығарырға мөмкин, мәҫәлән, йыйыу тиҙлегенә ҡарап. “Яҙма” — тик metadata ингән коллекция, унда китап исемдәре, авторҙар, ISBN һ.б. мәғлүмәт бар, ә “файлдар” — үҙҙәрендә ысын файлдар (pdf, epub) булған коллекциялар."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> AACID форматы былай: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Мәҫәлән, беҙ сығарған ысын AACID: <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: коллекция исеме, унда ASCII хәрефтәре, һандар һәм аҫты һыҙылған билдәләр булыуы мөмкин (әммә ике тапҡыр аҫты һыҙылған билдәләр юҡ)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: ISO 8601 ҡыҫҡа версияһы, һәр ваҡыт UTC-ла, мәҫәлән, <code>20220723T194746Z</code>. Был һан һәр сығарылыш өсөн монотон рәүештә артырға тейеш, әммә уның аныҡ мәғәнәһе коллекцияға ҡарап үҙгәрергә мөмкин. Беҙ йыйыу йәки ID булдырыу ваҡытын ҡулланырға тәҡдим итәбеҙ."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: коллекцияға хас идентификатор, әгәр кәрәк булһа, мәҫәлән, Z-Library ID. Ҡалдырырға йәки ҡыҫҡартырға мөмкин. Әгәр AACID 150 символдан артып китһә, мотлаҡ ҡалдырырға йәки ҡыҫҡартырға кәрәк."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, әммә ASCII-ға ҡыҫҡартылған, мәҫәлән, base57 ҡулланып. Беҙ хәҙерге ваҡытта <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python китапханаһын ҡулланабыҙ."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID диапазоны.</strong> AACID-тар монотон рәүештә арта барған ваҡыт билдәләмәләре ингәнлектән, беҙ уларҙы айырым коллекция эсендә диапазондарҙы билдәләү өсөн ҡуллана алабыҙ. Беҙ был форматты ҡулланабыҙ: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, унда ваҡыт билдәләмәләре индерелгән. Был ISO 8601 билдәләмәһе менән ярашлы. Диапазондар өҙлөкһөҙ, һәм улар бер-береһен ҡаплауы мөмкин, әммә ҡаплағанда, был коллекцияла алдан сығарылған яҙмалар менән тап килергә тейеш (сөнки AAC-тар үҙгәрешһеҙ). Юғалған яҙмалар рөхсәт ителмәй."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata файл.</strong> Metadata файл бер коллекция өсөн AAC-тар диапазонының metadata-һын үҙ эсенә ала. Уларҙың түбәндәге үҙенсәлектәре бар:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Файл исеме AACID диапазоны булырға тейеш, <code style=\"color: red\">annas_archive_meta__</code> менән башланып, <code>.jsonl.zstd</code> менән тамамланырға тейеш. Мәҫәлән, беҙҙең сығарылыштарҙың береһе<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> тип атала."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Файл киңәйтмәһе күрһәтелгәнсә, файл төрө <a %(jsonlines)s>JSON Lines</a> <a %(zstd)s>Zstandard</a> менән ҡыҫҡартылған."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Һәр JSON объекты түбәндәге яландарҙы юғары кимәлдә үҙ эсенә алырға тейеш: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (ихтыяр буйынса). Башҡа яландар рөхсәт ителмәй."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> — коллекцияның семантикаһына ярашлы ирекле metadata. Ул коллекция эсендә семантик яҡтан ярашлы булырға тейеш."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> ихтыяр буйынса, һәм ул ярашлы бинар мәғлүмәтте үҙ эсенә алған бинар мәғлүмәт папкаһының исеме. Был папкалағы ярашлы бинар мәғлүмәттең файл исеме яҙманың AACID-ы."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code> префиксы һеҙҙең учреждение исеменә яраҡлаштырылырға мөмкин, мәҫәлән, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Бинар мәғлүмәт папкаһы.</strong> Бер коллекция өсөн AAC-тар диапазонының бинар мәғлүмәтен үҙ эсенә алған папка. Уларҙың түбәндәге үҙенсәлектәре бар:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Директория исеме AACID диапазоны булырға тейеш, <code style=\"color: green\">annas_archive_data__</code> менән башланып, һәм өҫтәмә булмаҫҡа тейеш. Мәҫәлән, беҙҙең ысын сығарылыштарҙың береһе<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> тип атала."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Директория күрһәтелгән диапазон эсендәге бөтә AAC-тар өсөн мәғлүмәт файлдарын үҙ эсенә алырға тейеш. Һәр мәғлүмәт файлының исеме AACID булырға тейеш (өҫтәмәләрһеҙ)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Был папкаларҙы ҙур булмаған күләмдә тоторға тәҡдим ителә, мәҫәлән, һәр береһе 100ГБ-1ТБ-тан ҙурыраҡ булмаһын, ләкин был тәҡдим ваҡыт үтеү менән үҙгәрергә мөмкин."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Торренттар.</strong> Метадата файлдары һәм бинар мәғлүмәт папкалары торренттарға берләштерелергә мөмкин, һәр метадата файлына бер торрент йәки һәр бинар мәғлүмәт папкаһына бер торрент. Торренттарҙың файл исеме оригиналь файл/директория исеменә <code>.torrent</code> өҫтәмәһе менән булырға тейеш."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Өлгө"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Һуңғы Z-Library сығарылышын өлгө итеп ҡарайыҡ. Ул ике коллекциянан тора: “<span style=\"background: #fffaa3\">zlib3_records</span>” һәм “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Был беҙгә метадата яҙмаларын айырым йыйып һәм сығарып алырға мөмкинлек бирә. Шуға күрә, беҙ метадата файлдары менән ике торрент сығарҙыҡ:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Беҙ шулай уҡ бинар мәғлүмәт папкалары менән күп торренттар сығарҙыҡ, ләкин тик “<span style=\"background: #ffd6fe\">zlib3_files</span>” коллекцияһы өсөн генә, барыһы 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "<code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> эшләтеп, эсендә нимә барлығын күрә алабыҙ:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Был осраҡта, был Z-Library тарафынан хәбәр ителгән китаптың метадатаһы. Юғары кимәлдә беҙҙә тик “aacid” һәм “metadata” бар, ләкин “data_folder” юҡ, сөнки ярашлы бинар мәғлүмәт юҡ. AACID төп ID булараҡ “22430000”-ды үҙ эсенә ала, һәм был “zlibrary_id”-тан алынғанын күрә алабыҙ. Был коллекциялағы башҡа AAC-тарҙың да шул уҡ структураға эйә булыуын көтә алабыҙ."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Ә хәҙер <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> эшләтеп ҡарайыҡ:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Был күпкә бәләкәйерәк AAC метадатаһы, ләкин был AAC-тың төп өлөшө башҡа урында, бинар файлда урынлашҡан! Ахырҙа, беҙҙә был юлы “data_folder” бар, шуға күрә ярашлы бинар мәғлүмәттең <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> урынлашҡан булыуын көтә алабыҙ. “Metadata” “zlibrary_id”-ты үҙ эсенә ала, шуға күрә уны “zlib_records” коллекцияһындағы ярашлы AAC менән еңел бәйләй алабыҙ. Беҙ уны төрлө юлдар менән бәйләй ала инек, мәҫәлән, AACID аша — стандарт быны талап итмәй."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "“Metadata” яланының үҙе JSON булыуы ла мотлаҡ түгеллеген иҫегеҙҙә тотоғоҙ. Ул XML йәки башҡа мәғлүмәт форматындағы юл булыуы мөмкин. Һеҙ хатта метадата мәғлүмәтен бәйле бинар блокта һаҡлай алаһығыҙ, мәҫәлән, әгәр ул күп мәғлүмәт булһа."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Йомғаҡ"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Был стандарт менән, беҙ сығарылыштарҙы күберәк этаплап эшләй алабыҙ һәм яңы мәғлүмәт сығанаҡтарын еңелерәк өҫтәй алабыҙ. Беҙҙә инде бер нисә ҡыҙыҡлы сығарылыш әҙерләнә!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Беҙ шулай уҡ башҡа күләгә китапханаларының беҙҙең коллекцияларҙы күсереп алыуын еңеләйтеүгә өмөтләнәбеҙ. Ахырҙа, беҙҙең маҡсат — кешелек белемдәрен һәм мәҙәниәтен мәңге һаҡлау, шуға күрә күберәк резерв күсермәләр булһа, шул тиклем яҡшыраҡ."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Аннаның Яңылығы: тулыһынса асыҡ сығанаҡлы архив, ElasticSearch, 300ГБ+ китап тышлыҡтары"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Беҙ Аннаның Архивы менән яҡшы альтернатива тәҡдим итеү өсөн тәүлек әйләнәһенә эшләйбеҙ. Бына һуңғы ваҡытта ирешелгән ҡайһы бер ҡаҙаныштарыбыҙ."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Z-Library ябылыуы һәм уның (имеш) нигеҙләүселәре ҡулға алыныуы менән, беҙ Аннаның Архивы менән яҡшы альтернатива тәҡдим итеү өсөн тәүлек әйләнәһенә эшләйбеҙ (бында һылтанма бирмәйәсәкбеҙ, ләкин уны Google аша таба алаһығыҙ). Бына һуңғы ваҡытта ирешелгән ҡайһы бер ҡаҙаныштарыбыҙ."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Аннаның Архивы тулыһынса асыҡ сығанаҡлы"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Беҙ мәғлүмәт ирекле булырға тейеш тип иҫәпләйбеҙ, һәм беҙҙең үҙ кодтарыбыҙ ҙа был ҡағиҙәнән ситтә түгел. Беҙ бөтә кодтарыбыҙҙы шәхси Gitlab инстансыбыҙҙа сығарҙыҡ: <a %(annas_archive)s>Аннаның Программалары</a>. Шулай уҡ эшебеҙҙе ойоштороу өсөн мәсьәләләр трекерын ҡулланабыҙ. Әгәр һеҙ беҙҙең үҫешкә ҡушылырға теләһәгеҙ, был башлау өсөн яҡшы урын."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Беҙ эшләгән эштәрҙең тәмен биреү өсөн, клиент яғынан эш һөҙөмтәлелеген яҡшыртыу буйынса һуңғы эштәребеҙҙе ҡарағыҙ. Беҙ әлегә биттәрҙе бүлеүҙе индермәгәнгә күрә, йыш ҡына бик оҙон эҙләү биттәрен, 100-200 һөҙөмтә менән ҡайтара инек. Эҙләү һөҙөмтәләрен бик иртә өҙөргә теләмәнек, ләкин был ҡайһы бер ҡоролмаларҙы әкренәйтә ине. Бының өсөн беҙ бәләкәй генә хәйлә индерҙек: эҙләү һөҙөмтәләренең күбеһен HTML аңлатмаларына (<code><!-- --></code>) урап, һуңынан һөҙөмтә күренергә тейеш булған мәлде билдәләүсе бәләкәй Javascript яҙҙыҡ, шул мәлдә аңлатманы урап ала инек:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"виртуализацияһы\" 23 юлда ғәмәлгә ашырылды, ҡатмарлы китапханалар кәрәкмәй! Ваҡыт сикләнгәндә һәм ысын проблемаларҙы хәл итергә кәрәк булғанда, һеҙ шундай тиҙ һәм прагматик код менән тамамлайһығыҙ. Хәҙер беҙҙең эҙләү системаһы әкрен ҡоролмаларҙа яҡшы эшләй тип хәбәр ителде!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Тағы бер ҙур тырышлыҡ - мәғлүмәт базаһын төҙөүҙе автоматлаштырыу булды. Башта беҙ төрлө сығанаҡтарҙы тәртипһеҙ рәүештә бергә йыйҙыҡ. Хәҙер беҙ уларҙы яңыртып торорға теләйбеҙ, шуға күрә ике Library Genesis тармағынан яңы metadata йөкләп алыу һәм уларҙы интеграциялау өсөн бер нисә скрипт яҙҙыҡ. Маҡсат - был беҙҙең архив өсөн генә түгел, ә күләгәле китапхана metadata-һы менән уйнағыһы килгән һәр кем өсөн файҙалы булһын. Маҡсат - төрлө ҡыҙыҡлы metadata булған Jupyter дәфтәре булдырыу, шуның менән беҙ ISBN-дарҙың ниндәй өлөшө мәңге һаҡлана икәнен асыҡлау кеүек тикшеренеүҙәрҙе күберәк башҡара алабыҙ."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Ниһайәт, беҙ донор системаһын яңырттыҡ. Хәҙер һеҙ кредит картаһы ярҙамында крипто әмияндарыбыҙға аҡса күсерә алаһығыҙ, криптовалюталар тураһында бер нәмә лә белергә кәрәкмәй. Бының практикада нисек эшләүен күҙәтеп торасаҡбыҙ, ләкин был ҙур яңылыҡ."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "ElasticSearch-ҡа күсеү"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Беҙҙең <a %(annas_archive)s>билеттарҙың</a> береһе эҙләү системаһы менән бәйле мәсьәләләр йыйылмаһы ине. Беҙ MySQL тулы текст эҙләүен ҡулландык, сөнки бөтә мәғлүмәттәребеҙ MySQL-да ине. Ләкин уның сиктәре бар ине:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Ҡайһы бер һорауҙар бик оҙаҡ ваҡыт алды, асыҡ тоташыуҙарҙың барыһын да биләгәнгә тиклем."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "MySQL-ҙың стандарт буйынса минималь һүҙ оҙонлоғо бар, йәки индексығыҙ бик ҙур булыуы мөмкин. Кешеләр \"Ben Hur\" эҙләй алмауҙарын хәбәр иттеләр."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Эҙләү тулыһынса хәтерҙә йөкләнгәндә генә тиҙ булды, был беҙгә быларҙы эшләтеү өсөн ҡиммәтерәк машина алырға мәжбүр итте, өҫтәүенә индекстарҙы башланғанда йөкләү өсөн ҡайһы бер командалар кәрәк булды."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Беҙ уны яңы функциялар төҙөү өсөн еңел генә киңәйтә алмаҫ инек, мәҫәлән, аҡ араһы булмаған телдәр өсөн яҡшыраҡ <a %(wikipedia_cjk_characters)s>токенизация</a>, фильтрлау/фасетлау, сортлау, \"һеҙ шуны аңлаттығыҙмы\" тәҡдимдәре, автокомплит һәм башҡалар."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Күп эксперттар менән һөйләшкәндән һуң, беҙ ElasticSearch-ҡа туҡталдыҡ. Ул идеаль булманы (уларҙың стандарт \"һеҙ шуны аңлаттығыҙмы\" тәҡдимдәре һәм автокомплит функциялары насар), ләкин дөйөм алғанда, эҙләү өсөн MySQL-дан күпкә яҡшыраҡ булды. Беҙ уны ниндәйҙер миссия-критик мәғлүмәт өсөн ҡулланыуҙа <a %(youtube)s>бик ышаныслы түгелбеҙ</a> (улар күп <a %(elastic_co)s>алға китеш</a> яһанылар), ләкин дөйөм алғанда, күсеү менән бик ҡәнәғәтбеҙ."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Әлегә, беҙ күпкә тиҙерәк эҙләү, яҡшыраҡ тел ярҙамы, яҡшыраҡ релевантлыҡ сортлау, төрлө сортлау варианттары һәм тел/китап төрө/файл төрө буйынса фильтрлау индерҙек. Был нисек эшләгәнен белергә теләһәгеҙ, <a %(annas_archive_l140)s>ҡарағыҙ</a>. Ул бик аңлайышлы, ләкин тағы ла бер нисә аңлатма кәрәк булыр ине…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300ГБ+ китап тышлыҡтары сығарылды"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Ниһайәт, беҙ бәләкәй сығарылышты иғлан итергә шатбыҙ. Libgen.rs тармағын эшләтеүсе кешеләр менән хеҙмәттәшлектә, беҙ уларҙың бөтә китап тышлыҡтарын торренты һәм IPFS аша бүлешәбеҙ. Был тышлыҡтарҙы ҡарау йөгөн күберәк машиналар араһында тарата һәм уларҙы яҡшыраҡ һаҡлай. Күп осраҡта (әммә бөтәһендә лә түгел), китап тышлыҡтары файлдарҙың үҙҙәрендә индерелгән, шуға күрә был \"сығарылған мәғлүмәт\" кеүек. Әммә IPFS-та булыуы көндәлек эш өсөн бик файҙалы, Аннаның Архивы һәм төрлө Library Genesis тармаҡтары өсөн."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Ғәҙәттәгесә, был сығарылышты Пират Китапхана Көҙгөһөндә таба алаһығыҙ (ТӨҘӘТМӘ: <a %(wikipedia_annas_archive)s>Аннаның Архивына</a> күсерелде). Бында уға һылтанма бирмәйәсәкбеҙ, ләкин уны еңел таба алаһығыҙ."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Өмөт итәбеҙ, хәҙер Z-Library-ға лайыҡлы альтернатива булдырғандан һуң, беҙ темпты бер аҙ яйлата алабыҙ. Был эш йөгө айырыуса тотҡарланмай. Әгәр һеҙ программалау, сервер операциялары йәки һаҡлау эше менән ярҙам итергә теләһәгеҙ, беҙгә мөрәжәғәт итегеҙ. Һаман да күп <a %(annas_archive)s>эш башҡарырға кәрәк</a>. Ҡыҙыҡһыныуығыҙ һәм ярҙамығыҙ өсөн рәхмәт."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Аннаның Архивы донъяның иң ҙур комикстар күләгәле китапханаһын (95ТБ) һаҡланы — һеҙ уны таратыуға ярҙам итә алаһығыҙ"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Донъяның иң ҙур комикстар күләгәле китапханаһы бер генә нөктәгә бәйле ине.. бөгөнгә тиклем."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Hacker News-та фекер алышығыҙ</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Комикстарҙың иң ҙур күләгәле китапханаһы, моғайын, билдәле бер Library Genesis тармағыныҡы: Libgen.li. Ул сайтты эшләтеүсе бер администратор 2 миллиондан ашыу файлдан торған иҫ киткес комикстар коллекцияһын йыйҙы, дөйөм 95ТБ-тан ашыу. Әммә башҡа Library Genesis коллекцияларынан айырмалы рәүештә, был коллекция торренты аша күмәртәләп ҡулланыуға мөмкин булманы. Һеҙ был комикстарҙы уның әкрен шәхси серверы аша ғына айырым ҡуллана ала инегеҙ — бер генә нөктәгә бәйле ине. Бөгөнгә тиклем!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Беҙ был яҙмала һеҙгә был коллекция тураһында күберәк һөйләйәсәкбеҙ, һәм был эште күберәк яҡлау өсөн беҙҙең аҡса йыйыу кампанияһы тураһында."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Доктор Барбара Гордон китапхана донъяһында үҙен юғалтырға тырыша…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen тармаҡтары"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Башта, бер аҙ мәғлүмәт. Һеҙ, бәлки, Library Genesis-ты уларҙың эпик китап коллекцияһы менән беләһегеҙ. Күберәк кеше белмәй, тип Library Genesis ирекмәндәре башҡа проекттар булдырған, мәҫәлән, журналдар һәм стандарт документтарҙың ҙур коллекцияһы, Sci-Hub-тың тулы резерв күсермәһе (Sci-Hub-тың нигеҙләүсеһе Александра Элбакян менән хеҙмәттәшлектә), һәм ысынлап та, комикстарҙың ҙур коллекцияһы."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Бер мәлдә Library Genesis көҙгөһө операторҙары айырым юлдар менән китте, был хәҙерге ваҡытта бер нисә төрлө \"тармаҡ\" булыуына килтерҙе, барыһы ла Library Genesis исемен йөрөтә. Libgen.li тармағы үҙенсәлекле рәүештә был комикстар коллекцияһына эйә, шулай уҡ ҙур журналдар коллекцияһына (беҙ ҙә был өҫтөндә эшләйбеҙ)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Хеҙмәттәшлек"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Уның ҙурлығын иҫәпкә алып, был коллекция оҙаҡ ваҡыт беҙҙең теләк исемлегендә булды, шуға күрә Z-Library-ҙы резервҡа алыуҙағы уңышыбыҙҙан һуң, беҙ был коллекцияға күҙ һалдыҡ. Башта беҙ уны туранан-тура күсереп алдыҡ, был ҙур ҡыйынлыҡ булды, сөнки уларҙың серверы иң яҡшы хәлдә түгел ине. Беҙ был ысул менән яҡынса 15ТБ алдыҡ, ләкин был бик әкрен барҙы."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Ярай әле, беҙ китапхана операторы менән бәйләнешкә инә алдыҡ, ул беҙгә бөтә мәғлүмәтте туранан-тура ебәрергә ризалашты, был күпкә тиҙерәк булды. Бөтә мәғлүмәтте күсереү һәм эшкәртеү өсөн ярты йылдан ашыу ваҡыт талап ителде, һәм беҙ уны диск боҙолоуы арҡаһында тиерлек юғалттыҡ, был барыһын да яңынан башлауҙы аңлатыр ине."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Был тәжрибә беҙгә был мәғлүмәтте мөмкин тиклем тиҙерәк таратыу мөһим тип уйларға мәжбүр итте, шуға күрә ул киң таралып күсерелһен. Беҙ был коллекцияны мәңгегә юғалтыуҙан бер-ике бәхетһеҙ осраҡлы ваҡиға алыҫлығында ғына торабыҙ!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Коллекция"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Тиҙ хәрәкәт итеү коллекцияның бер аҙ тәртипһеҙ булыуын аңлата… Әйҙәгеҙ ҡарайыҡ. Беҙҙең файл системаһы бар тип күҙ алдына килтерегеҙ (ысынбарлыҡта беҙ уны торренттар араһында бүләбеҙ):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Беренсе директория, <code>/repository</code>, быларҙың күберәк структуралы өлөшө. Был директория \"мең директориялар\" тип аталған: һәр береһе мең файл менән директориялар, улар базаһында инкременталь һанланған. Директория <code>0</code> комикс_id 0–999 булған файлдарҙы үҙ эсенә ала, һәм шулай дауам итә."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Был Library Genesis үҙенең нәфис һәм нәфис булмаған әҙәбиәт коллекциялары өсөн ҡулланған схема менән бер үк. Идея шунда, һәр \"мең директория\" тулғандан һуң автоматик рәүештә торрентҡа әйләнә."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Әммә, Libgen.li операторы был коллекция өсөн бер ҡасан да торренттар яһамаған, һәм шуға күрә мең директориялар уңайһыҙ булғандыр, һәм \"тәртипһеҙ директориялар\"ға юл биргәндер. Улар <code>/comics0</code> аша <code>/comics4</code>. Улар барыһы ла уникаль директория структураларын үҙ эсенә ала, улар файлдарҙы йыйыу өсөн мәғәнәле булғандыр, ләкин хәҙер беҙгә бик аңлашылмай. Ярай әле, metadata был файлдарға туранан-тура һылтана, шуға күрә уларҙың дисктағы һаҡлау ойошторолошо ысынлап та мөһим түгел!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata MySQL базаһы формаһында бар. Был Libgen.li веб-сайтынан туранан-тура күсереп алырға мөмкин, ләкин беҙ уны үҙебеҙҙең MD5 хэштары менән бергә торрентта ла тәҡдим итәсәкбеҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Анализ"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "95ТБ һеҙҙең һаҡлау кластерына ташланғанда, унда нимә барлығын аңларға тырышаһығыҙ… Беҙ дубликаттарҙы алып ташлау кеүек ысулдар менән күләмде бер аҙ кәметә алабыҙмы тип анализ яһаныҡ. Бына ҡайһы бер табыштарыбыҙ:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Семантик дубликаттарҙы (бер үк китаптың төрлө сканерҙары) теоретик яҡтан фильтрлап була, ләкин был ҡатмарлы. Комикстарҙы ҡулдан ҡарағанда, беҙ бик күп ялған ыңғай һөҙөмтәләр таптыҡ."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "MD5 буйынса ҡайһы бер дубликаттар бар, был сағыштырмаса файҙаһыҙ, ләкин уларҙы фильтрлау беҙгә яҡынса 1% in экономия бирер ине. Был масштабта был барыбер яҡынса 1ТБ, ләкин шулай уҡ, был масштабта 1ТБ ысынлап та мөһим түгел. Беҙ был процестә мәғлүмәтте осраҡлы рәүештә юҡ итеү хәүефен алырға теләмәйбеҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Беҙ китап булмаған мәғлүмәттәрҙе таптыҡ, мәҫәлән, комикстарға нигеҙләнгән фильмдар. Был шулай уҡ файҙаһыҙ кеүек күренә, сөнки улар башҡа ысулдар менән киң таралған. Әммә, беҙ фильм файлдарын ғына фильтрлай алмайбыҙ тип аңланыҡ, сөнки шулай уҡ <em>интерактив комикстар</em> бар, улар компьютерҙа сығарылған, кемдер уларҙы яҙып алып, фильмдар итеп һаҡлаған."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Ахырҙа, коллекциянан нимәлер юйыу бер нисә процент ҡына һаҡлап ҡаласаҡ. Шунан беҙ иҫкә төшөрҙөк: беҙ мәғлүмәт йыйыусылар, һәм быларҙы күсереп аласаҡ кешеләр ҙә мәғлүмәт йыйыусылар, һәм шуға күрә, “НИМӘ ТИГӘН ҺҮҘ, ЮЙЫУ?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Шулай итеп, беҙ һеҙгә тулы, үҙгәртелмәгән коллекцияны тәҡдим итәбеҙ. Был күп мәғлүмәт, әммә беҙ күп кешеләрҙең уны таратыуға иғтибар итәсәгенә өмөтләнәбез."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Иғәнә йыйыу"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Беҙ был мәғлүмәтте ҙур өлөштәрҙә сығарабыҙ. Беренсе торрент <code>/comics0</code> тора, уны бер ҙур 12TB .tar файлға һалдыҡ. Был һеҙҙең ҡаты диск һәм торрент программаһы өсөн күп бәләкәй файлдарға ҡарағанда яҡшыраҡ."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Был сығарылыштың бер өлөшө булараҡ, беҙ иғәнә йыйыу ойошторабыҙ. Был коллекцияның эшләү һәм контракт сығымдарын ҡаплау өсөн $20,000 йыйырға ниәтләйбеҙ, шулай уҡ ағымдағы һәм киләсәк проекттарҙы мөмкин итеү өсөн. Беҙҙең ҡайһы бер <em>ҙур</em> проекттар эш барышында."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Мин үҙемдең иғәнәм менән кемде хуплайым?</em> Ҡыҫҡаһы: беҙ кешелектең бөтә белем һәм мәҙәниәтен һаҡлайбыҙ һәм уны еңел ҡулланыу мөмкинлеген бирәбеҙ. Бөтә код һәм мәғлүмәт асыҡ сығанаҡлы, беҙ тулыһынса ирекмәндәр тарафынан алып барылған проект, һәм әлегә тиклем 125TB китап һаҡланыҡ (Libgen һәм Scihub-тың булған торренттарына өҫтәп). Ахырҙа, беҙ бөтә донъя китаптарын табыу, сканерлау һәм һаҡлау өсөн кешеләрҙе дәртләндереүсе һәм мөмкин итеүсе механизм төҙөйбөҙ. Беҙҙең төп план тураһында киләсәктәге яҙмала яҙырбыҙ. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Әгәр һеҙ 12 айлыҡ “Amazing Archivist” ағзалыҡҡа ($780) иғәнә индерһәгеҙ, һеҙ <strong>“торрентты уллыҡҡа алыу”</strong> мөмкинлеген алаһығыҙ, йәғни беҙ һеҙҙең ҡулланыусы исемегеҙҙе йәки хәбәрегеҙҙе торренттарҙың береһенең файл исеменә ҡуйырбыҙ!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Иғәнә индереү өсөн <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> сайтына инеп, “Иғәнә индереү” төймәһенә баҫығыҙ. Шулай уҡ беҙгә күберәк ирекмәндәр кәрәк: программа тәьминәте инженерҙары, хәүефһеҙлек тикшеренеүселәре, аноним сауҙа белгестәре һәм тәржемәселәр. Беҙҙе хостинг хеҙмәттәре менән тәьмин итеп тә ярҙам итә алаһығыҙ. Һәм, әлбиттә, беҙҙең торренттарҙы таратығыҙ!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Беҙҙе шул тиклем юмарт хуплаған һәр кемгә рәхмәт! Һеҙ ысынлап та үҙгәреш яһайһығыҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Әлегә сығарылған торренттар бында (ҡалғандарын эшкәртәбеҙ әле):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Бөтә торренттарҙы <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> сайтында “Datasets” бүлегендә табырға мөмкин (беҙ унда тура һылтанма бирмәйбеҙ, шуға күрә был блогҡа һылтанмалар Reddit, Twitter һ.б. алып ташланмай). Унан Tor сайтына һылтанмаға эйәрегеҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Артабан нимә?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Күп торренттар оҙайлы һаҡлау өсөн яҡшы, әммә көндәлек ҡулланыу өсөн түгел. Беҙ был мәғлүмәтте интернетҡа ҡуйыу өсөн хостинг партнерҙары менән эшләйәсәкбеҙ (сөнки Аннаның Архивы бер нәмә лә тура хостингламай). Әлбиттә, был йөкләү һылтанмаларын Аннаның Архивында таба алаһығыҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Беҙ шулай уҡ һәр кемде был мәғлүмәт менән нимәлер эшләргә саҡырабыҙ! Беҙгә уны яҡшыраҡ анализларға, дубликаттарҙы бөтөрөргә, IPFS-ҡа ҡуйырға, AI моделдәрегеҙҙе уға өйрәтергә ярҙам итегеҙ, һәм башҡалар. Был барыһы һеҙҙеке, һәм беҙ һеҙҙең нимә эшләрегеҙҙе түҙемһеҙлек менән көтәбеҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Ахырҙа, алда әйтелгәнсә, беҙҙә әле ҡайһы бер ҙур сығарылыштар бар (әгәр <em>кемдер</em> <em>осраҡлы рәүештә</em> бер <em>билдәле</em> ACS4 базаһының күсермәһен ебәрә алһа, беҙҙе ҡайҙан табырға беләһегеҙ…), шулай уҡ бөтә донъя китаптарын һаҡлау өсөн механизм төҙөү."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Шулай итеп, көтөгөҙ, беҙ әле генә башлайбыҙ."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "Пират Китапхана Көҙгөһөнә 3 яңы китап өҫтәлде (+24TB, 3,8 миллион китап)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Пират Китапхана Көҙгөһөнөң тәүге сығарылышында (ТӨҘӘТМӘ: <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> күсерелде), беҙ Z-Library, ҙур законһыҙ китап коллекцияһының көҙгөһөн яһаныҡ. Иҫкәртеү өсөн, был тәүге блог яҙмаһында беҙ быны яҙҙыҡ:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library популяр (һәм законһыҙ) китапхана. Улар Library Genesis коллекцияһын алып, уны еңел эҙләнмәле иткән. Бынан тыш, улар яңы китаптарҙы индереүҙе дәртләндереүсе төрлө өҫтөнлөктәр менән файҙаланыусыларҙы йәлеп итеүҙә бик һөҙөмтәле булдылар. Әлеге ваҡытта улар был яңы китаптарҙы Library Genesis-ҡа кире индермәйҙәр. Library Genesis-тан айырмалы рәүештә, улар үҙ коллекцияһын еңел күсереп алырлыҡ итмәйҙәр, был киң һаҡлауҙы тотҡарлай. Был уларҙың бизнес моделенә мөһим, сөнки улар коллекцияға күпләп инеү өсөн (көнөнә 10 китаптан артыҡ) аҡса ала."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Беҙ законһыҙ китаптар коллекцияһына күпләп инеү өсөн аҡса алыу тураһында әхлаҡи ҡарарҙар ҡабул итмәйбеҙ. Z-Library белемгә инеүҙе киңәйтеүҙә һәм күберәк китаптар сығарыуҙа уңышлы булды, быға шик юҡ. Беҙ бында үҙ өлөшөбөҙҙө индереү өсөн генә: был шәхси коллекцияның оҙайлы һаҡланышын тәьмин итеү."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Ул коллекция 2021 йылдың уртаһына ҡарай. Шул арала, Z-Library ғәжәп тиҙлектә үҫә: улар яҡынса 3,8 миллион яңы китап өҫтәне. Унда ҡайһы бер дубликаттар бар, әлбиттә, ләкин күпселеге ысынлап та яңы китаптар, йәки алдан тапшырылған китаптарҙың юғары сифатлы сканерҙары кеүек күренә. Был күп осраҡта Z-Library-ҙа ирекмән модераторҙарҙың артыуы һәм уларҙың дубликаттарҙы бөтөрөү менән күпләп йөкләү системаһы арҡаһында. Беҙ уларҙы был ҡаҙаныштары менән ҡотларға теләйбеҙ."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Беҙ Z-Library-ға һуңғы көҙгөбөҙ һәм 2022 йылдың авгусына тиклем өҫтәлгән бөтә китаптарҙы алғаныбыҙҙы шатланып хәбәр итәбеҙ. Шулай уҡ беҙ тәүге тапҡыр үткәреп ебәргән ҡайһы бер китаптарҙы кире алып килдек. Дөйөм алғанда, был яңы коллекция яҡынса 24ТБ, был алдағыһынан (7ТБ) күпкә ҙурыраҡ. Беҙҙең көҙгөбөҙ хәҙер дөйөм 31ТБ. Тағы ла, беҙ Library Genesis менән дубликаттарҙы бөтөрҙөк, сөнки был коллекция өсөн торренттар инде бар."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Зинһар, яңы коллекцияны ҡарау өсөн Pirate Library Mirror-ға барығыҙ (ТӨҘӘТМӘ: <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> күсерелде). Унда файлдарҙың нисек төҙөлгәнлеге һәм һуңғы тапҡырҙан алып нимәләр үҙгәргәнлеге тураһында күберәк мәғлүмәт бар. Беҙ бынан һылтанма бирмәйәсәкбеҙ, сөнки был законһыҙ материалдарҙы урынлаштырмаған блог сайты ғына."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Әлбиттә, орлоҡлау ҙа беҙгә ярҙам итеүҙең яҡшы ысулы. Беҙҙең алдағы торренттар йыйылмаһын орлоҡлаған һәр кемгә рәхмәт. Позитив яуап өсөн рәхмәтлебеҙ, һәм был ғәҙәти булмаған ысулда белем һәм мәҙәниәт һаҡланышы тураһында хәстәрлек күргән күп кешеләр булыуына шатбыҙ."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Ҡараҡ архивист булыу ысулы"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Беренсе ҡаршылыҡ көтөлмәгән булыуы мөмкин. Был техник проблема ла, юридик проблема ла түгел. Был психологик проблема."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Башҡаға күсер алдынан, Pirate Library Mirror тураһында ике яңылыҡ (ТӨҘӘТМӘ: <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> күсерелде):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Беҙ бик юмарт иғәнәләр алдыҡ. Беренсеһе \"bookwarrior\", Library Genesis-тың төп нигеҙләүсеһен дә хуплаған аноним шәхестән $10k булды. Был иғәнәне ойошторған өсөн bookwarrior-ға айырым рәхмәт. Икенсеһе һуңғы сығарылышыбыҙҙан һуң беҙҙең менән бәйләнешкә сыҡҡан һәм ярҙам итергә илһамланған аноним иғәнәсе тарафынан тағы $10k булды. Шулай уҡ бер нисә бәләкәйерәк иғәнәләр булды. Бөтә юмарт ярҙамығыҙ өсөн ҙур рәхмәт. Беҙҙең ҡайһы бер ҡыҙыҡлы яңы проекттар бар, уларҙы был ярҙамлаясаҡ, шуға күрә көтөп тороғоҙ."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Икенсе сығарылышыбыҙҙың ҙурлығы менән техник ауырлыҡтар булды, ләкин беҙҙең торренттар хәҙер эшләп тора һәм орлоҡлана. Шулай уҡ аноним шәхестән беҙҙең коллекцияны уларҙың бик юғары тиҙлекле серверҙарында орлоҡлау тәҡдиме булды, шуға күрә беҙ уларҙың машиналарына махсус йөкләү яһайбыҙ, шунан һуң коллекцияны йөкләгән һәр кем тиҙлектә ҙур яҡшыртыу күрергә тейеш."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Дөйөм алғанда, цифрлы һаҡланыштың <em>ни өсөн</em> икәнлеге тураһында, һәм айырыуса ҡараҡ архивизм тураһында тулы китаптар яҙырға мөмкин, ләкин былар менән таныш булмағандар өсөн ҡыҫҡа аңлатма бирәйек. Донъя элеккегә ҡарағанда күберәк белем һәм мәҙәниәт етештерә, ләкин шулай уҡ элеккегә ҡарағанда күберәк юғала. Кешелек был мираҫты академик нәшриәттәр, стриминг хеҙмәттәре һәм социаль медиа компаниялары кеүек корпорацияларға ышанып тапшыра, һәм улар йыш ҡына яҡшы һаҡлаусылар булып сыҡмай. Digital Amnesia документаль фильмын ҡарағыҙ, йәки Джейсон Скоттың теләһә ниндәй сығышын."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ҡайһы бер учреждениелар мөмкин тиклем күберәк архивлауҙа яҡшы эш башҡара, ләкин улар закон менән сикләнгән. Ҡараҡтар булараҡ, беҙ улар ҡағылмай торған коллекцияларҙы архивлау өсөн уникаль хәлдәбеҙ, авторлыҡ хоҡуҡтарын үтәү йәки башҡа сикләүҙәр арҡаһында. Шулай уҡ коллекцияны донъя буйлап күп тапҡыр күсереп, тейешле һаҡланыш мөмкинлеген арттыра алабыҙ."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Әлегә, интеллектуаль милек, законды боҙоу әхлаҡиәте, цензура тураһында уйланыуҙар, йәки белем һәм мәҙәниәткә инеү мәсьәләһе тураһында бәхәстәргә инмәйәсәкбеҙ. Быларҙың барыһы ла хәл ителгәс, <em>нисек</em> икәнлегенә күсәйек. Беҙҙең команда нисек ҡараҡ архивистарға әйләнде һәм был юлда өйрәнгән дәрестәребеҙҙе уртаҡлашырбыҙ. Был юлға сыҡҡанда күп ҡаршылыҡтар бар, һәм беҙ уларҙың ҡайһы берҙәрендә һеҙгә ярҙам итә алырбыҙ тип ышанабыҙ."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Йәмғиәт"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Беренсе ҡаршылыҡ көтөлмәгән булыуы мөмкин. Был техник проблема ла, юридик проблема ла түгел. Был психологик проблема: күләгәлә эшләү бик яңғыҙ булыуы мөмкин. Һеҙ нимә эшләргә планлаштыраһығыҙ һәм хәүеф моделе ниндәй булыуына ҡарап, бик һаҡ булырға тура килеүе мөмкин. Спектрҙың бер осонда Sci-Hub нигеҙләүсеһе Александра Элбакян* кеүек кешеләр бар, улар үҙ эшмәкәрлектәре тураһында бик асыҡ. Ләкин ул хәҙерге ваҡытта көнбайыш илгә барһа, ҡулға алыныу хәүефе ҙур, һәм унда ун йыллыҡ төрмә срогы менән осрашыуы мөмкин. Был һеҙ ҡабул итергә әҙер булған хәүефме? Беҙ спектрҙың икенсе осондабыҙ; бер ниндәй эҙ ҡалдырмаҫҡа тырышып, көслө оператив хәүефһеҙлек менән бик һаҡ эш итәбеҙ."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* HN-да \"ynno\" тарафынан телгә алынғанса, Александра башта билдәле булырға теләмәгән: \"Уның серверҙары PHP-тан тулы хаталар хәбәрҙәрен сығарырға көйләнгән, шул иҫәптән хаталы сығанаҡ файлдың тулы юлы, ул /home/<USER>" Шулай итеп, был эштәр өсөн ҡулланған компьютерҙарҙа осраҡлы ҡулланыусы исемдәре ҡулланығыҙ, әгәр ҙә һеҙ нимәнелер дөрөҫ көйләмәһәгеҙ."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Әммә был серлелек психологик сығым менән килә. Күпселек кешеләр үҙ эштәре өсөн танылыу ярата, һәм һеҙ был эш өсөн ысын тормошта бер ниндәй кредит ала алмайһығыҙ. Хатта ябай әйберҙәр ҙә ауыр булыуы мөмкин, мәҫәлән, дуҫтар һеҙҙән нимә менән шөғөлләнгәнегеҙҙе һорағанда (бер мәл \"минең NAS / homelab менән мәшғүл булыу\" иҫкерә)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Шуға күрә ҡайһы бер йәмғиәт табыу бик мөһим. Һеҙ ҡайһы бер бик яҡын дуҫтарығыҙға ышанып, ҡайһы бер оператив хәүефһеҙлекте бирә алаһығыҙ, уларға тәрән ышаныс белдерә алаһығыҙ. Шул саҡта ла уларҙың электрон почталарын властарға тапшырырға тура килһә, йәки уларҙың ҡоролмалары башҡа ысул менән компрометирланған булһа, бер нәмә лә яҙмаҫҡа иғтибар итегеҙ."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Тағы ла яҡшыраҡ - ҡайһы бер башҡа ҡараҡтар табыу. Әгәр һеҙҙең яҡын дуҫтарығыҙ һеҙгә ҡушылырға теләһә, бик яҡшы! Юҡһа, һеҙ интернетта башҡаларҙы таба алаһығыҙ. Ҡыҙғанысҡа ҡаршы, был әле лә тар йәмғиәт. Әлегә тиклем беҙ был өлкәлә әүҙем булған бер нисә кешене генә таптыҡ. Яҡшы башланғыс урындар булып Library Genesis форумдары һәм r/DataHoarder тора. Archive Team шулай уҡ оҡшаш фекерле кешеләргә эйә, ләкин улар закон сиктәрендә эшләй (хатта закондың ҡайһы бер һоро өлкәләрендә булһа ла). Традицион \"warez\" һәм пиратлыҡ сәхнәләре шулай уҡ оҡшаш фекер йөрөткән кешеләргә эйә."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Беҙ берләшмәне үҫтереү һәм идеяларҙы өйрәнеү юлдарына асыҡбыҙ. Беҙгә Twitter йәки Reddit аша хәбәр ебәрә алаһығыҙ. Бәлки, беҙ ниндәйҙер форум йәки чат төркөмө ойошторорға мөмкинбеҙ. Бер проблема шунда: был дөйөм платформаларҙы ҡулланғанда еңел генә цензураға эләгергә мөмкин, шуға күрә беҙ уны үҙебеҙгә ойошторорға тура килер. Шулай уҡ был һөйләшеүҙәрҙе тулыһынса асыҡ итеү (күберәк ҡатнашыу мөмкинлеге) менән шәхси итеү (потенциаль \"маҡсаттар\" беҙ уларҙы йыйырға әҙерләнгәнде белмәү) араһында компромисс бар. Был турала уйларға кәрәк булыр. Әгәр ҙә һеҙ был эш менән ҡыҙыҡһынаһығыҙ икән, беҙгә белдерегеҙ!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Проекттар"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Беҙ проект эшләгәндә, ул бер нисә этаптан тора:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Домен һайлау / философия: Һеҙ ҡайҙа яҡынса иғтибар итергә теләйһегеҙ, һәм ни өсөн? Һеҙҙең уникаль мауығыуҙар, күнекмәләр һәм шарттар ниндәй, уларҙы үҙ файҙағыҙға нисек ҡуллана алаһығыҙ?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Маҡсат һайлау: Ҡайһы бер коллекцияны һеҙ күсерәсәкһегеҙ?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata йыйыу: Файлдар тураһында мәғлүмәттәрҙе каталоглау, үҙҙәрен (йыш ҡына күпкә ҙурыраҡ) файлдарҙы йөкләмәйенсә."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Мәғлүмәт һайлау: Metadata нигеҙендә, хәҙерге ваҡытта архивлау өсөн иң мөһим мәғлүмәтте һайлау. Барыһы ла булыуы мөмкин, ләкин йыш ҡына урын һәм киңлек һаҡлауҙың аҡыллы ысулы бар."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Мәғлүмәт йыйыу: Мәғлүмәтте ысынлап алыу."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Таратыу: Уны торенттарға төрөү, ҡайҙалыр иғлан итеү, кешеләрҙе уны таратыуға йәлеп итеү."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Былар тулыһынса бәйһеҙ этаптар түгел, һәм йыш ҡына һуңғы этаптағы аңлауҙар һеҙҙе алдағы этапҡа кире ҡайтара. Мәҫәлән, metadata йыйыу ваҡытында һеҙ һайлаған маҡсаттың һеҙҙең күнекмәләр кимәленән юғары һаҡланыу механизмдары (мәҫәлән, IP блоктары) барлығын аңлайһығыҙ, һәм һеҙ кире ҡайтып, башҡа маҡсат табаһығыҙ."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Домен һайлау / философия"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Һаҡланырға тейешле белем һәм мәҙәни мираҫтың етешмәүе юҡ, был иһә аптырашта ҡалдырырға мөмкин. Шуға күрә йыш ҡына бер аҙ туҡтап, һеҙҙең өлөшөгөҙ ниндәй булырға мөмкинлеген уйлау файҙалы."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Был турала һәр кемдең уйлау ысулы төрлөсә, ләкин үҙегеҙгә бирә ала торған ҡайһы бер һорауҙар:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Ни өсөн һеҙ был эш менән ҡыҙыҡһынаһығыҙ? Һеҙ нимә менән мауығаһығыҙ? Әгәр ҙә беҙ бер төркөм кешене йыйып, улар үҙҙәрен ҡыҙыҡһындырған әйберҙәрҙе архивлаһа, был күп нәмәне ҡаплар ине! Һеҙ үҙегеҙҙең мауығыуығыҙ тураһында уртаса кешенән күпкә күберәк белерһегеҙ, мәҫәлән, ниндәй мәғлүмәтте һаҡлау мөһим, иң яҡшы коллекциялар һәм онлайн берләшмәләр ниндәй, һәм башҡалар."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Һеҙҙең ниндәй күнекмәләрегеҙ бар, уларҙы үҙ файҙағыҙға нисек ҡуллана алаһығыҙ? Мәҫәлән, әгәр ҙә һеҙ онлайн хәүефһеҙлек буйынса эксперт булһағыҙ, һеҙ IP блоктарын еңеү юлдарын таба алаһығыҙ. Әгәр ҙә һеҙ берләшмәләрҙе ойоштороуҙа оҫта булһағыҙ, бәлки, һеҙ бер маҡсат тирәләй кешеләрҙе йыйып ала алаһығыҙ. Әммә был процесты яҡшы итеп алып барыу өсөн программалауҙы белеү файҙалы."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Был эшкә күпме ваҡыт бүлә алаһығыҙ? Беҙҙең кәңәшебеҙ - бәләкәйҙән башлау һәм был эшкә өйрәнгән һайын ҙурыраҡ проекттар эшләү, ләкин был эш тулыһынса үҙенә йәлеп итә ала."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Ҡайһы юғары һөҙөмтәле өлкәгә иғтибар итергә кәрәк? Әгәр ҙә һеҙ пират архивлауҙа X сәғәт сарыф итәсәкһегеҙ икән, нисек итеп иң ҙур \"ҡиммәт өсөн һөҙөмтә\" ала алаһығыҙ?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Был турала һеҙҙең уникаль уйлау ысулдары ниндәй? Бәлки, һеҙҙә башҡаларҙың иғтибарынан ситтә ҡалған ҡыҙыҡлы идеялар йәки ҡараштар барҙыр."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Беҙҙең осраҡта, беҙ фәндең оҙайлы ваҡыт һаҡланыуына айырыуса иғтибар иттек. Беҙ Library Genesis тураһында белдек, һәм уның торенттар ярҙамында күп тапҡыр тулыһынса күсерелгәнен белдек. Был идеяны яраттыҡ. Бер көн, беҙҙән береһе Library Genesis-та ҡайһы бер фәнни дәреслектәрҙе табырға тырышты, ләкин уларҙы таба алманы, был уның ысынлап тулы булыуына шик тыуҙырҙы. Шунан беҙ был дәреслектәрҙе интернетта эҙләнек һәм уларҙы башҡа урындарҙа таптыҡ, был беҙҙең проект өсөн орлоҡ булды. Z-Library тураһында белгәнгә тиклем үк, беҙ был китаптарҙы ҡулдан йыйырға тырышмау, ә булған коллекцияларға иғтибар итеү һәм уларҙы Library Genesis-ҡа кире ҡайтарыу идеяһын уйланыҡ."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Маҡсат һайлау"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Шулай итеп, беҙ ҡараған өлкәбеҙҙе билдәләнек, хәҙер ниндәй махсус коллекцияны күсерергә кәрәк? Яҡшы маҡсат өсөн бер нисә мөһим фактор бар:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Ҙур"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Уникаль: башҡа проекттар тарафынан яҡшы яҡтыртылмаған."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Асыҡ: уларҙың metadata һәм мәғлүмәтен йыйыуҙы ҡыйынлаштырыусы күп ҡатламлы һаҡлау саралары ҡулланмай."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Махсус мәғлүмәт: һеҙ был маҡсат тураһында махсус мәғлүмәткә эйә, мәҫәлән, һеҙ был коллекцияға махсус инеүгә эйә, йәки уларҙың һаҡлау сараларын еңеү ысулын тапҡанһығыҙ. Был мотлаҡ түгел (беҙҙең киләсәк проект махсус бер нәмә лә эшләмәй), әммә был ярҙам итә!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Беҙ фәнни дәреслектәрҙе Library Genesis-тан башҡа сайттарҙа тапҡас, уларҙың интернетҡа нисек килеп эләгеүен асыҡларға тырыштыҡ. Шунан һуң Z-Library-ҙы таптыҡ һәм күпселек китаптар тәүҙә унда барлыҡҡа килмәһә лә, улар ахырҙа унда барлыҡҡа килеүен аңланыҡ. Беҙ уның Library Genesis менән бәйләнешен, (финанс) стимул структураһын һәм өҫтөнлөклө ҡулланыусы интерфейсын өйрәндек, быларҙың барыһы ла уны күпкә тулы коллекцияға әйләндерҙе. Шунан һуң беҙ башланғыс metadata һәм мәғлүмәт йыйыуҙы башҡарҙыҡ һәм IP йөкләнеш сикләүҙәрен урап үтеү мөмкинлеген аңланыҡ, беҙҙең ағзаларҙың береһенең күп прокси-серверҙарға махсус инеүен файҙаланып."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Төрлө маҡсаттарҙы өйрәнгәндә, VPN-дар һәм ваҡытлыса электрон почта адресын ҡулланып, үҙ эҙегеҙҙе йәшереү мөһим, был турала һуңыраҡ һөйләшербеҙ."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata йыйыу"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Ә хәҙер техник яҡтан ҡарайыҡ. Сайттарҙан metadata йыйыу өсөн беҙ барыһын да ябай тоттоҡ. Беҙ Python скрипттарын, ҡайһы саҡта curl, һәм һөҙөмтәләрҙе һаҡлау өсөн MySQL базаһын ҡулланабыҙ. Беҙ ҡатмарлы сайттарҙы картаға төшөрә алған ҡатмарлы йыйыу программаларын ҡулланманыҡ, сөнки әлегә тиклем беҙгә тик бер йәки ике төр биттәрҙе идентификаторҙар аша үтеп, HTML-ды анализлау ғына кәрәк булды. Әгәр ҙә еңел үтеп булмай торған биттәр булһа, бөтә биттәрҙе табырға тырышҡан дөрөҫ йыйыу программаһы кәрәк булыуы мөмкин."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Бөтөн сайтты йыйыуҙы башлар алдынан, бер аҙ ҡулдан эшләп ҡарағыҙ. Үҙегеҙ бер нисә тиҫтә битте ҡарап сығығыҙ, был нисек эшләгәнен аңлар өсөн. Ҡайһы саҡта һеҙ IP блокировкаларына йәки башҡа ҡыҙыҡлы хәлдәргә тап булыуығыҙ мөмкин. Мәғлүмәт йыйыу өсөн дә шул уҡ ҡағыла: был маҡсатҡа тәрән үтеп ингәнсе, уның мәғлүмәтен һөҙөмтәле йыя алыуығыҙға инанығыҙ."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Сикләүҙәрҙе урап үтеү өсөн бер нисә ысулды һынап ҡарарға мөмкин. Шул уҡ мәғлүмәтте һаҡлаған, әммә шул уҡ сикләүҙәргә эйә булмаған башҡа IP адресы йәки серверҙар бармы? Сикләүҙәр булмаған API нөктәләре бармы, ә башҡаларында бармы? Һеҙҙең IP ниндәй йөкләнеш тиҙлегендә блокировкаланған, һәм күпме ваҡытҡа? Йәки һеҙ блокировкаланмаған, әммә тиҙлек кәметелгәнме? Әгәр ҡулланыусы иҫәбе булдырһағыҙ, хәл нисек үҙгәрә? HTTP/2 ҡулланып, тоташыуҙарҙы асыҡ тоторға мөмкинме, һәм был биттәрҙе һорау тиҙлеген арттыра аламы? Бер юлы бер нисә файлды күрһәтеүсе биттәр бармы, һәм унда күрһәтелгән мәғлүмәт етерлекме?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Һеҙ һаҡларға теләгән әйберҙәр:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Исем"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Файл исеме / урынлашыуы"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: ҡайһы бер эске ID булыуы мөмкин, әммә ISBN йәки DOI кеүек ID-лар ҙа файҙалы."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Үлсәм: күпме диск урыны кәрәклеген иҫәпләү өсөн."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Хэш (md5, sha1): файлды дөрөҫ йөкләгәнегеҙҙе раҫлау өсөн."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Өҫтәлгән/үҙгәртелгән дата: һуңыраҡ кире ҡайтып, алда йөкләмәгән файлдарҙы йөкләү өсөн (әммә бының өсөн йыш ҡына ID йәки хэш ҡулланырға мөмкин)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Тасуирлама, категория, тэгтар, авторҙар, тел һ.б."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Беҙ, ғәҙәттә, быны ике этапта эшләйбеҙ. Башта беҙ HTML файлдарын йөкләйбеҙ, ғәҙәттә туранан-тура MySQL-ға (күп ваҡ файлдарҙан ҡотолоу өсөн, был турала аҫта һөйләшербеҙ). Шунан һуң, айырым этапта, был HTML файлдарын үтеп, уларҙы ысын MySQL таблицаларына әйләндерәбеҙ. Был ысул менән, әгәр анализлау кодында хата тапһағыҙ, барыһын да яңынан йөкләргә кәрәкмәй, сөнки һеҙ яңы код менән HTML файлдарын яңынан эшкәртә алаһығыҙ. Шулай уҡ эшкәртеү этапын параллелләштереү еңелерәк, был ваҡытты һаҡлай (һәм һеҙ йыйыу эшләгәндә эшкәртеү кодын яҙа алаһығыҙ, ике этапты бер юлы яҙырға кәрәкмәй)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Ахырҙа, ҡайһы бер маҡсаттар өсөн метадата йыйыу ғына бар. Унда дөрөҫ һаҡланмаған ҙур метадата коллекциялары бар."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Мәғлүмәт һайлау"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Йыш ҡына һеҙ метадата ярҙамында күсереп алыу өсөн мәғлүмәттең аҡыллы өлөшөн билдәләй алаһығыҙ. Һеҙ ахырҙа бөтә мәғлүмәтте күсереп алырға теләһәгеҙ ҙә, иң мөһим әйберҙәрҙе беренсе урынға ҡуйыу файҙалы булыуы мөмкин, сөнки һеҙҙе асыҡлауҙары һәм һаҡланыу саралары яҡшыртылыуы ихтимал, йәки һеҙгә күберәк диск һатып алырға кәрәк булыуы мөмкин, йәки барыһын да күсереп алғансы тормошоғоҙҙа башҡа нәмәләр килеп сығыуы мөмкин."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Мәҫәлән, коллекцияла бер үк ресурстың (китап йәки фильм кеүек) бер нисә баҫмаһы булыуы мөмкин, уларҙың береһе иң яҡшы сифатлы тип билдәләнгән. Был баҫмаларҙы беренсе һаҡлау бик аҡыллы булыр ине. Һеҙ ахырҙа бөтә баҫмаларҙы һаҡларға теләүегеҙ мөмкин, сөнки ҡайһы бер осраҡтарҙа метадата дөрөҫ билдәләнмәгән булыуы мөмкин, йәки баҫмалар араһында билдәһеҙ компромисстар булыуы мөмкин (мәҫәлән, \"иң яҡшы баҫма\" күпселек йәһәттән иң яҡшыһы булыуы мөмкин, әммә башҡа йәһәттән насарыраҡ, мәҫәлән, фильм юғарыраҡ сифатлы, әммә субтитрҙар юҡ)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Һеҙ шулай уҡ метадата базағыҙҙы ҡыҙыҡлы әйберҙәр табыу өсөн эҙләй алаһығыҙ. Иң ҙур файл ниндәй, һәм ул ни өсөн шул тиклем ҙур? Иң бәләкәй файл ниндәй? Айырым категориялар, телдәр һәм башҡалар буйынса ҡыҙыҡлы йәки көтөлмәгән үрнәктәр бармы? Дубликат йәки бик оҡшаш исемдәр бармы? Мәғлүмәт ҡасан өҫтәлгән, мәҫәлән, бер көндә күп файлдар өҫтәлгән кеүек үрнәктәр бармы? Һеҙ йыш ҡына мәғлүмәт йыйылмаһына төрлө юлдар менән ҡарап күп нәмә белә алаһығыҙ."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Беҙҙең осраҡта, беҙ Z-Library китаптарын Library Genesis-тағы md5 хэштарына ҡаршы дубликаттан арындырҙыҡ, был күп күсереп алыу ваҡытын һәм диск киңлеген һаҡланы. Әммә был бик үҙенсәлекле хәл. Күпселек осраҡта пираттар тарафынан дөрөҫ һаҡланған файлдарҙың тулы базаһы юҡ. Был үҙе үк кемдер өсөн ҙур мөмкинлек. Музыка һәм фильмдар кеүек әйберҙәрҙең даими яңыртылған күҙәтеүе булһа, был бик яҡшы булыр ине, улар инде торрент сайттарында киң таралған, һәм шуға күрә пират көҙгөһөнә индереү өсөн түбәнерәк өҫтөнлөккә эйә."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Мәғлүмәт йыйыу"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Хәҙер һеҙ мәғлүмәтте күпләп күсереп алырға әҙер. Алда әйтелгәнсә, был ваҡытта һеҙ инде маҡсаттың тәртибе һәм сикләүҙәрен яҡшыраҡ аңлау өсөн ҡулдан күп файлдар күсереп алған булырға тейеш. Әммә күп файлдарҙы бер юлы күсереп ала башлағас, һеҙҙе һаман да көтөлмәгән хәлдәр көтәсәк."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Беҙҙең кәңәшебеҙ - уны ябай тотоу. Башта күп файлдар күсереп алығыҙ. Һеҙ Python ҡуллана алаһығыҙ, һәм унан һуң бер нисә епкә киңәйтә алаһығыҙ. Әммә ҡайһы саҡта хатта ябайыраҡ ысул - базаҙан тура Bash файлдары булдырыу, һәм уларҙы бер нисә терминал тәҙрәһендә бер нисә тапҡыр эшләтеп киңәйтеү. Бында телгә алырға лайыҡлы тиҙ техник хәйлә - MySQL-да OUTFILE ҡулланыу, уны mysqld.cnf-ҙа \"secure_file_priv\"-ты һүндерһәгеҙ, теләһә ҡайҙа яҙа алаһығыҙ (әгәр һеҙ Linux-та булһағыҙ, AppArmor-ҙы ла һүндерергә/үҙгәртергә онотмағыҙ)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Беҙ мәғлүмәтте ябай ҡаты дисктарҙа һаҡлайбыҙ. Нимә бар, шуның менән башлағыҙ, һәм яйлап киңәйтегеҙ. Йөҙҙәрсә ТБ мәғлүмәтте һаҡлау тураһында уйлау ҡурҡыныс булыуы мөмкин. Әгәр һеҙ ошо хәлдә булһағыҙ, тәүҙә яҡшы өлөшөн сығарығыҙ, һәм иғланда ҡалғанын һаҡлауҙа ярҙам һорағыҙ. Әгәр үҙегеҙ күберәк ҡаты дисктар алырға теләһәгеҙ, r/DataHoarder-ҙа яҡшы килешеүҙәр алыу буйынса яҡшы ресурстар бар."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Күпселек файл системалары тураһында артыҡ борсолмаҫҡа тырышығыҙ. ZFS кеүек әйберҙәрҙе көйләүҙең ҡуяныҡ тишегенә төшөүе еңел. Әммә бер техник деталь - күп файлдар менән эш итеүҙә күпселек файл системалары яҡшы эшләмәй. Беҙ ябай эшкәртеү ысулы - бер нисә каталог булдырыу, мәҫәлән, төрлө ID диапазондары йәки хэш префикстары өсөн."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Мәғлүмәтте күсереп алғандан һуң, метадата хэштары ярҙамында файлдарҙың бөтөнлөгөн тикшерегеҙ, әгәр бар икән."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Таратыу"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Һеҙҙә мәғлүмәт бар, тимәк, һеҙҙең маҡсаттың донъялағы беренсе пират көҙгөһө (иң ихтимал). Күп йәһәттән иң ауыр өлөшө тамам, әммә иң хәүефле өлөшө әле алда. Ахырҙа, әлегә тиклем һеҙ йәшерен инегеҙ; радарҙан ситтә оса инегеҙ. Бөтә эшегеҙ яҡшы VPN ҡулланыу, бер ниндәй формала шәхси мәғлүмәттәрҙе тултырмау (эйе), һәм, бәлки, махсус браузер сессияһын (йәки хатта башҡа компьютер) ҡулланыу ине."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Хәҙер һеҙ мәғлүмәтте таратырға тейеш. Беҙҙең осраҡта, беҙ тәүҙә китаптарҙы Library Genesis-ҡа кире ҡайтарырға теләнек, әммә шунда уҡ уның ауырлыҡтарын асыҡланыҡ (фантастика һәм фәнни әҙәбиәтте айырыу). Шуға күрә беҙ Library Genesis стилендәге торренттарҙы ҡулланып таратыуҙы һайланыҡ. Әгәр һеҙҙә булған проектҡа өлөш индереү мөмкинлеге булһа, был һеҙгә күп ваҡытты һаҡлап ҡаласаҡ. Әммә әлеге ваҡытта күп яҡшы ойошторолған пират көҙгөһө юҡ."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Шулай итеп, әйтәйек, һеҙ үҙегеҙ торренттар таратырға ҡарар иттегеҙ. Был файлдарҙы бәләкәйерәк итеп һаҡларға тырышығыҙ, уларҙы башҡа сайттарҙа көҙгөһө итеп ҡуйыу еңелерәк булһын. Һеҙгә шунан һуң торренттарҙы үҙегеҙ сәсергә тура киләсәк, шул уҡ ваҡытта аноним булып ҡалырға. VPN ҡуллана алаһығыҙ (порт форвардингы менән йәки унһыҙ), йәки Seedbox өсөн тумбл биткойндар менән түләй алаһығыҙ. Әгәр был терминдарҙың ҡайһы берҙәренең мәғәнәһен белмәһәгеҙ, күп уҡырға тура киләсәк, сөнки бында хәүефле компромисстарҙы аңлау мөһим."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Торрент файлдарын үҙҙәре булған торрент сайттарында урынлаштыра алаһығыҙ. Беҙҙең осраҡта, беҙ сайтты үҙебеҙ урынлаштырырға ҡарар иттек, сөнки беҙ шулай уҡ үҙ фәлсәфәбеҙҙе асыҡ итеп таратырға теләнек. Һеҙ быны үҙегеҙ ҙә ошондай уҡ ысул менән эшләй алаһығыҙ (беҙ домендар һәм хостинг өсөн Njalla ҡулланабыҙ, тумбл биткойндар менән түләнгән), әммә шулай уҡ беҙҙең менән бәйләнешкә инегеҙ, беҙ һеҙҙең торренттарығыҙҙы урынлаштырырға әҙербеҙ. Әгәр был идея таралһа, беҙ ваҡыт үтеү менән пират көҙгөһөнөң тулы индексын төҙөргә теләйбеҙ."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "VPN һайлау буйынса күп яҙылған, шуға күрә беҙ репутация буйынса һайлауҙың дөйөм кәңәшен ҡабатлайбыҙ. Оҙайлы ваҡытлы шәхси мәғлүмәтте һаҡлау тарихы булған судта тикшерелгән логтар булмау сәйәсәте - беҙҙең фекеребеҙсә, иң түбән хәүефле вариант. Һеҙ барыһын да дөрөҫ эшләгәндә лә, хәүефте нульгә төшөрә алмайһығыҙ. Мәҫәлән, торренттарығыҙҙы сәсер саҡта, бик мотивациялы дәүләт актёры VPN серверҙары өсөн ингән һәм сыҡҡан мәғлүмәт ағымдарын ҡарап, һеҙҙең кем икәнегеҙҙе асыҡлай ала. Йәки һеҙ ябай ғына хаталана алаһығыҙ. Беҙ, моғайын, инде хаталанғанбыҙ, һәм тағы ла хаталанасаҡбыҙ. Бәхеткә күрә, дәүләттәр пиратлыҡ тураһында шул тиклем ҡайғыртмай."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Һәр проект өсөн бер ҡарар ҡабул итергә кәрәк, уны элекке шәхес менән баҫтырырға йәки юҡ. Әгәр һеҙ шул уҡ исемде ҡулланыуҙы дауам итһәгеҙ, алдағы проекттарҙан оператив хәүефһеҙлек хаталары һеҙгә кире ҡағыла ала. Әммә төрлө исемдәр аҫтында баҫтырыу оҙайлы репутация төҙөмәй. Беҙ баштан уҡ көслө оператив хәүефһеҙлеккә эйә булырға ҡарар иттек, шуға күрә шул уҡ шәхесте ҡулланыуҙы дауам итә алабыҙ, әммә хаталанһаҡ йәки шарттар талап итһә, башҡа исем аҫтында баҫтырыуҙан тартынмаясаҡбыҙ."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Хәбәрҙе таратыу ҡатмарлы булыуы мөмкин. Беҙ әйткәнсә, был әле лә тар йәмәғәт. Беҙ башта Reddit-та баҫтырҙыҡ, әммә ысынлап та Hacker News-та популярлыҡ яуланыҡ. Әлегә беҙҙең тәҡдим - бер нисә урында баҫтырыу һәм нимә булырын ҡарау. Һәм йәнә, беҙҙең менән бәйләнешкә инегеҙ. Беҙ пират архивлау тырышлыҡтары тураһында күберәк һүҙ таратырға теләр инек."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Йомғаҡ"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Беҙ яңы башлаған пират архивистар өсөн был файҙалы булыр тип ышанабыҙ. Һеҙҙе был донъяға ҡаршы алыу өсөн түҙемһеҙлек менән көтәбеҙ, шуға күрә беҙгә мөрәжәғәт итергә ҡурҡмағыҙ. Донъяның белем һәм мәҙәниәтен мөмкин тиклем күберәк һаҡлайыҡ һәм уны киң таратайыҡ."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Пират Китапхана Көҙгөһөн тәҡдим итәбеҙ: 7ТБ китаптарҙы һаҡлау (улар Libgen-да юҡ)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Был проект (<a %(wikipedia_annas_archive)s>Аннаның Архивы</a> адресына күсерелде) кешелек белемдәрен һаҡлау һәм азат итеүгә өлөш индереүҙе маҡсат итеп ҡуя. Беҙ үҙебеҙҙең бәләкәй һәм тыйнаҡ өлөшөбөҙҙө индерәбеҙ, беҙҙән алда булған бөйөктәрҙең юлынан барабыҙ."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Был проекттың маҡсаты уның исеме менән күрһәтелә:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Пират</strong> - Беҙ күпселек илдәрҙә авторлыҡ хоҡуғы закондарын аңлы рәүештә боҙабыҙ. Был беҙгә юридик ойошмалар эшләй алмаған эште башҡарырға мөмкинлек бирә: китаптарҙың киң һәм алыҫ таралыуын тәьмин итеү."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Китапхана</strong> - Күпселек китапханалар кеүек, беҙ башлыса китаптар кеүек яҙма материалдарға иғтибар итәбеҙ. Киләсәктә башҡа төр медиаға ла киңәйеүебеҙ ихтимал."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Көҙгө</strong> - Беҙ бары тик булған китапханаларҙың көҙгөһө генә. Беҙ һаҡлауға иғтибар итәбеҙ, китаптарҙы еңел эҙләү һәм күсереп алыу (кереш) йәки яңы китаптар индереүсе ҙур берләшмә булдырыуға (сығанаҡ) түгел."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Беҙ көҙгөһө булған тәүге китапхана - Z-Library. Был популяр (һәм законһыҙ) китапхана. Улар Library Genesis коллекцияһын алып, уны еңел эҙләнерлек итеп эшләгән. Өҫтәүенә, улар яңы китаптар индереүҙе дәртләндереүсе ҡулланыусыларға төрлө өҫтөнлөктәр тәҡдим итеп, яңы китаптар индереүҙә бик һөҙөмтәле булған. Улар әлеге ваҡытта был яңы китаптарҙы Library Genesis-ҡа кире индермәй. Library Genesis-тан айырмалы рәүештә, улар үҙ коллекцияһын еңел көҙгө яһарлыҡ итеп эшләмәй, был киң һаҡлауҙы тотҡарлай. Был уларҙың бизнес моделе өсөн мөһим, сөнки улар үҙ коллекцияһына күпләп (көнөнә 10 китаптан артыҡ) инеү өсөн аҡса ала."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Беҙ законһыҙ китаптар коллекцияһына күпләп инеү өсөн аҡса алыу тураһында әхлаҡи ҡарарҙар ҡабул итмәйбеҙ. Z-Library белемгә инеүҙе киңәйтеүҙә һәм күберәк китаптар сығарыуҙа уңышлы булды, быға шик юҡ. Беҙ бында үҙ өлөшөбөҙҙө индереү өсөн генә: был шәхси коллекцияның оҙайлы һаҡланышын тәьмин итеү."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Беҙ һеҙҙе беҙҙең торренттарҙы күсереп алып һәм уларҙы таратып, кешелек белемдәрен һаҡлау һәм азат итеүгә ярҙам итергә саҡырабыҙ. Мәғлүмәттәрҙең нисек ойошторолғаны тураһында күберәк мәғлүмәт өсөн проект битенә ҡарағыҙ."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Беҙ шулай уҡ һеҙҙе киләһе ниндәй коллекцияларға көҙгө яһарға һәм уны нисек башҡарырға кәрәклеге тураһында идеяларығыҙҙы индерергә саҡырабыҙ. Бергәләп беҙ күп нәмәгә ирешә алабыҙ. Был иҫәпһеҙ-һанһыҙ башҡалар араһында бәләкәй генә өлөш. Һеҙҙең эшләгән бөтә эштәрегеҙ өсөн рәхмәт."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Беҙ был блогтан файлдарға һылтанма бирмәйбеҙ. Үҙегеҙ табығыҙ.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb сығарыу, йәки Нисә Китап Мәңге Һаҡлана?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Әгәр беҙ күләгәле китапханаларҙан файлдарҙы дөрөҫ дубликатламай торған булһаҡ, донъяның бөтә китаптарының нисә проценты һаҡланған булыр ине?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Пират Китапхана Көҙгөһө менән (EDIT: <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> адресына күсерелде), беҙҙең маҡсат - донъяның бөтә китаптарын алып, уларҙы мәңге һаҡлау.<sup>1</sup> Беҙҙең Z-Library торренттары һәм оригиналь Library Genesis торренттары араһында 11,783,153 файл бар. Әммә был ысынлап та нисә? Әгәр беҙ был файлдарҙы дөрөҫ дубликатлаһаҡ, донъяның бөтә китаптарының нисә проценты һаҡланған булыр ине? Беҙ ысынлап та ошондай нәмәгә эйә булырға теләр инек:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% oкешелек яҙма мираҫы мәңге һаҡлана"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Процент өсөн беҙгә бүленеүсе кәрәк: бөтә ваҡыттарҙа баҫылған китаптарҙың дөйөм һаны.<sup>2</sup> Google Books юҡҡа сыҡҡанға тиклем, проект инженеры Леонид Тайчер был һанды <a %(booksearch_blogspot)s>баһаларға тырышты</a>. Ул — шаяртып — 129,864,880 (“кәм тигәндә йәкшәмбегә тиклем”) һанына килде. Ул был һанды донъяның бөтә китаптарының берләштерелгән базаһын төҙөп баһаланы. Бының өсөн ул төрлө Dataset-тарҙы бергә йыйып, уларҙы төрлө юлдар менән берләштерҙе."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Ҡыҫҡа ғына әйткәндә, донъялағы бөтә китаптарҙы каталоглаштырырға тырышҡан тағы бер кеше бар ине: һуңғы цифрлы активист һәм Reddit-тың нигеҙләүсеһе Аарон Шварц.<sup>3</sup> Ул <a %(youtube)s>Open Library-ҙы башланы</a>, һәр баҫылған китап өсөн \"бер веб-бит\" маҡсаты менән, төрлө сығанаҡтарҙан мәғлүмәттәрҙе берләштереп. Ул академик мәҡәләләрҙе күпләп күсереп алыу өсөн хөкөм ителгәндә, цифрлы һаҡлау эше өсөн иң юғары хаҡты түләне, һәм был уның үҙ-үҙенә ҡул һалыуына килтерҙе. Беҙҙең төркөмдөң псевдоним булыу сәбәптәренең береһе, һәм беҙ бик һаҡ булыуыбыҙҙың сәбәбе лә ошо. Open Library әле лә Интернет Архивындағы кешеләр тарафынан геройҙарса алып барыла, Аарондың мираҫын дауам итеп. Был хаҡта һуңыраҡ был яҙмала кире ҡайтырбыҙ."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Google блог яҙмаһында Тейчер был һанды баһалауҙағы ҡайһы бер ҡыйынлыҡтарҙы тасуирлай. Беренсенән, китап нимә ул? Бер нисә мөмкин билдәләмә бар:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Физик күсермәләр.</strong> Әлбиттә, был бик файҙалы түгел, сөнки улар шул уҡ материалдың күсермәләре генә. Китаптарҙағы кешеләр яһаған бөтә аннотацияларҙы һаҡлап ҡалыу мөмкин булһа, бик шәп булыр ине, мәҫәлән, Ферматтың билдәле \"яҙмалары\". Әммә, ҡыҙғанысҡа ҡаршы, был архиварийҙың хыялы булып ҡаласаҡ."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Әҫәрҙәр”.</strong> Мәҫәлән, \"Гарри Поттер һәм серҙәр бүлмәһе\" логик концепция булараҡ, уның бөтә версияларын, төрлө тәржемәләрен һәм ҡабат баҫмаларын үҙ эсенә ала. Был файҙалы билдәләмә кеүек, ләкин нимә иҫәпкә алынғанын билдәләүе ауыр булыуы мөмкин. Мәҫәлән, беҙ, моғайын, төрлө тәржемәләрҙе һаҡлап ҡалырға теләрбеҙ, әммә тик бәләкәй үҙгәрештәр менән ҡабат баҫмалар шул тиклем мөһим булмаҫҡа мөмкин."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Баҫмалар”.</strong> Бында һеҙ китаптың һәр уникаль версияһын иҫәпләйһегеҙ. Уның тураһында нимәлер үҙгәрһә, мәҫәлән, башҡа тышлыҡ йәки башҡа алғы һүҙ, ул башҡа баҫма тип иҫәпләнә."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Файлдар.</strong> Library Genesis, Sci-Hub йәки Z-Library кеүек күләгәле китапханалар менән эшләгәндә, өҫтәмә ҡарау кәрәк. Бер үк баҫманың бер нисә сканы булыуы мөмкин. Һәм кешеләр булған файлдарҙың яҡшыраҡ версияларын яһай ала, текстты OCR ярҙамында сканерлап, йәки мөйөштә сканерланған биттәрҙе төҙәтеп. Беҙ был файлдарҙы бер баҫма тип иҫәпләргә теләйбеҙ, был яҡшы metadata йәки документ оҡшашлыҡ саралары ярҙамында дубликаттарҙы бөтөрөүҙе талап итәсәк."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Баҫмалар” “китаптар” нимә икәнлегенең иң практик билдәләмәһе кеүек. Уңайлы, был билдәләмә шулай уҡ уникаль ISBN һандарын тәғәйенләү өсөн ҡулланыла. ISBN, йәки Халыҡ-ара стандарт китап номеры, халыҡ-ара сауҙа өсөн йыш ҡулланыла, сөнки ул халыҡ-ара штрих-код системаһы менән интеграцияланған (\"Халыҡ-ара мәҡәлә номеры\"). Әгәр һеҙ китапты магазиндарҙа һатырға теләһәгеҙ, уға штрих-код кәрәк, шуға күрә ISBN алаһығыҙ."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Тейчерҙың блог яҙмаһы ISBN-дар файҙалы булһа ла, улар универсаль түгел, сөнки улар ысынлап та етмешенсе йылдар уртаһында ғына ҡабул ителгән, һәм донъяла һәр ерҙә түгел, тип билдәләй. Шулай ҙа, ISBN, моғайын, китап баҫмаларының иң киң ҡулланылған идентификаторы, шуға күрә был беҙҙең иң яҡшы башланғыс нөктә. Әгәр беҙ донъялағы бөтә ISBN-дарҙы таба алһаҡ, беҙгә һаҡланырға тейеш китаптарҙың файҙалы исемлеген алабыҙ."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Шулай итеп, беҙ мәғлүмәтте ҡайҙан алабыҙ? Донъялағы бөтә китаптарҙың исемлеген төҙөргә тырышҡан бер нисә барлыҡҡа килгән тырышлыҡтар бар:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Ахырҙа, улар был тикшеренеүҙе Google Books өсөн эшләнеләр. Әммә, уларҙың metadata-һы күпләп ҡулланыу өсөн асыҡ түгел һәм уны ҡырып алыу бик ауыр."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Алда әйтелгәнсә, был уларҙың бөтә миссияһы. Улар хеҙмәттәшлек иткән китапханаларҙан һәм милли архивтарҙан ҙур күләмдә китапхана мәғлүмәттәрен сығарҙылар һәм быны дауам итәләр. Улар шулай уҡ ирекмән китапханасылар һәм төрлө metadata менән яҙмаларҙы дубликаттан арындырырға тырышҡан техник командаға эйә. Иң яҡшыһы, уларҙың dataset-ы тулыһынса асыҡ. Һеҙ уны ябай ғына <a %(openlibrary)s>күсереп ала алаһығыҙ</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Был коммерцияға ҡарамаған OCLC тарафынан алып барылған веб-сайт, ул китапхана идара итеү системаларын һата. Улар күп китапханаларҙан китап metadata-һын йыйып, WorldCat веб-сайты аша уны ҡулланыуға бирәләр. Әммә, улар был мәғлүмәтте һатыуҙан аҡса эшләйҙәр, шуға күрә ул күпләп күсереп алыу өсөн асыҡ түгел. Улар ҡайһы бер китапханалар менән хеҙмәттәшлектә күберәк сикләнгән күпләп dataset-тарҙы күсереп алыу өсөн асыҡ итәләр."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Был блог яҙмаһының темаһы. ISBNdb төрлө веб-сайттарҙан китап metadata-һын, айырыуса хаҡ мәғлүмәтен, йыйып, уларҙы китап һатыусыларға һата, шуға күрә улар үҙ китаптарының хаҡын баҙарҙың ҡалған өлөшө менән ярашлы билдәләй ала. Бөгөнгө көндә ISBN-дар бик универсаль булғанлыҡтан, улар ысынлап та \"һәр китап өсөн веб-бит\" төҙөнөләр."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Төрлө айырым китапхана системалары һәм архивтар.</strong> Китапханалар һәм архивтар бар, улар юғарыла әйтелгәндәрҙең береһе менән индексталмаған һәм йыйылмаған, йыш ҡына улар финанслау етмәүе арҡаһында, йәки башҡа сәбәптәр буйынса Open Library, OCLC, Google менән мәғлүмәттәрен бүлешергә теләмәйҙәр. Уларҙың күпселеге интернет аша ҡулланыуға асыҡ цифрлы яҙмаларға эйә, һәм улар йыш ҡына бик яҡшы һаҡланмаған, шуға күрә әгәр һеҙ ярҙам итергә һәм ғәжәп китапхана системалары тураһында өйрәнеп күңел асырға теләһәгеҙ, былар яҡшы башланғыс нөктәләр."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Был яҙмала, беҙ бәләкәй релизды (элекке Z-Library релиздары менән сағыштырғанда) иғлан итеүгә шатбыҙ. Беҙ ISBNdb-ның күп өлөшөн ҡырып алдыҡ һәм мәғлүмәтте Pirate Library Mirror веб-сайтында торрентлау өсөн асыҡ иттек (ӨҘӨМТӘ: <a %(wikipedia_annas_archive)s>Аннаның Архивы</a> күсерелде; беҙ уны бында тура бәйләмәйәсәкбеҙ, ябай ғына эҙләгеҙ). Был яҡынса 30,9 миллион яҙма (20ГБ <a %(jsonlines)s>JSON Lines</a> булараҡ; 4,4ГБ ҡыҫылған). Уларҙың веб-сайтында улар ысынлап та 32,6 миллион яҙмаға эйә булыуҙарын белдерәләр, шуға күрә беҙ ниндәйҙер рәүештә ҡайһы берҙәрен юғалтҡанбыҙ, йәки <em>улар</em> нимәлер дөрөҫ эшләмәйҙәр. Һәр хәлдә, әлегә беҙ уны нисек эшләгәнебеҙҙе аныҡ бүлешмәйәсәкбеҙ — был уҡыусы өсөн күнекмә булып ҡаласаҡ. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Беҙ бүлешәсәк нәмә — донъялағы китаптар һанын баһалауға яҡынлашырға тырышыу өсөн ҡайһы бер башланғыс анализ. Беҙ өс dataset-ты ҡараныҡ: был яңы ISBNdb dataset-ы, Z-Library күләгәле китапхананан ҡырып алынған metadata-ның тәүге релизы (Library Genesis-ты үҙ эсенә ала), һәм Open Library мәғлүмәттәрен ташлау."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Киләһе менән башлайыҡ:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Z-Library/Libgen һәм Open Library-ҙа уникаль ISBN-дарҙан күпкә күберәк китаптар бар. Был күп китаптарҙың ISBN-дары юҡ тигәнде аңлатамы, әллә ISBN metadata-һы ябай ғына юҡмы? Беҙ, моғайын, был һорауға башҡа атрибуттар (исем, автор, нәшриәт һ.б.) нигеҙендә автоматик тура килтереү, күберәк мәғлүмәт сығанаҡтарын йәлеп итеү, һәм ISBN-дарҙы китап сканерҙарынан үҙҙәренән сығарыу (Z-Library/Libgen осрағында) менән яуап бирә алабыҙ."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Шул ISBN-дарҙың нисәүһе уникаль? Был иң яҡшы Венн диаграммаһы менән күрһәтелә:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Төгәлрәк әйткәндә:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Беҙҙе нисек аҙ тап килеүе аптыратты! ISBNdb-ла Z-Library йәки Open Library-ҙа күрһәтелмәгән бик күп ISBN бар, һәм шул уҡ хәл (кәмерәк, ләкин һаман да ҙур дәрәжәлә) башҡа икеһе өсөн дә дөрөҫ. Был күп яңы һорауҙар тыуҙыра. ISBN-дар менән билдәләнмәгән китаптарҙы билдәләүҙә автоматик тап килеү күпме ярҙам итер ине? Күп тап килеүҙәр булыр инеме һәм шуға күрә тап килеү күбәйер инеме? Шулай уҡ, әгәр беҙ 4-се йәки 5-се Dataset-ты индерһәк, ни булыр ине? Шул саҡта күпме тап килеү күрер инек?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Был беҙгә башланғыс нөктәне бирә. Хәҙер беҙ Z-Library Dataset-ында булмаған һәм исем/автор яландары менән дә тап килмәгән бөтә ISBN-дарҙы ҡарай алабыҙ. Был беҙгә донъялағы бөтә китаптарҙы һаҡлау мөмкинлеген бирә: башта интернеттан сканерлау, һуңынан ысын тормошта китаптарҙы сканерлау. Һуңғыһы хатта халыҡтан аҡса йыйыу менән йәки айырым китаптарҙы цифрлаштырырға теләгән кешеләрҙән \"бүләктәр\" менән башҡарылырға мөмкин. Былар барыһы ла башҡа ваҡыт өсөн хикәйә."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Әгәр һеҙ быларҙың береһендә ярҙам итергә теләһәгеҙ — артабан анализлау; күберәк metadata йыйыу; күберәк китаптар табыу; китаптарҙы OCR-лау; быларҙы башҡа өлкәләр өсөн эшләү (мәҫәлән, мәҡәләләр, аудиокитаптар, фильмдар, телевизион тапшырыуҙар, журналдар) йәки хатта был мәғлүмәттәрҙең ҡайһы берҙәрен ML / ҙур тел моделе уҡытыу өсөн ҡулланыу — зинһар, минең менән бәйләнешкә сығығыҙ (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Әгәр һеҙгә айырыуса мәғлүмәт анализы ҡыҙыҡлы булһа, беҙ Dataset-тарҙы һәм скрипттарҙы ҡулланыу өсөн еңелерәк форматта тәҡдим итеү өҫтөндә эшләйбеҙ. Блокнотты ябай ғына тармаҡлап, былар менән уйнай башлау бик шәп булыр ине."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Ниһайәт, әгәр һеҙ был эште хупларға теләһәгеҙ, иғәнә яһауҙы уйлағыҙ. Был тулыһынса ирекмәндәр тарафынан башҡарылған операция, һәм һеҙҙең өлөшөҙ ҙур үҙгәреш яһай. Һәр өлөш ярҙам итә. Әлегә беҙ крипто менән иғәнәләр ҡабул итәбеҙ; Anna’s Archive сайтындағы Donate битен ҡарағыҙ."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. \"мәңге\" тигәндең ниндәйҙер аҡыллы билдәләмәһе өсөн. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Әлбиттә, кешелектең яҙма мираҫы китаптарҙан күпкә күберәк, айырыуса хәҙерге ваҡытта. Был пост һәм беҙҙең һуңғы сығарылыштар өсөн беҙ китаптарға иғтибар итәбеҙ, ләкин беҙҙең ҡыҙыҡһыныуҙар артабан да киңәйә."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Аарон Сварц тураһында күп нәмә әйтергә мөмкин, ләкин беҙ уны ҡыҫҡаса ғына телгә алырға теләнек, сөнки ул был хикәйәлә мөһим роль уйнай. Ваҡыт үтеү менән, күберәк кеше уның исемен тәүге тапҡыр ишетергә мөмкин, һәм улар үҙҙәре өсөн был серле донъяға сумасаҡ."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Күләгә китапханаларының мөһим тәҙрәһе"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Коллекцияларыбыҙҙы мәңгелек һаҡлай алабыҙ тип нисек дәғүә итә алабыҙ, ә улар инде 1 ПБ-ға яҡынлаша?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Ҡытай версияһы 中文版</a>, <a %(reddit)s>Reddit</a> сайтында фекер алышыу, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Anna’s Archive сайтында, коллекцияларыбыҙҙы мәңгелек һаҡлай алабыҙ тип нисек дәғүә итә алабыҙ, ә уларҙың дөйөм күләме инде 1 Петабайтҡа (1000 ТБ) яҡынлаша һәм һаман да үҫә, тип йыш һорайҙар. Был мәҡәләлә беҙ философиябыҙға ҡарап, кешелектең белем һәм мәҙәниәтен һаҡлау миссияһы өсөн киләһе ун йыллыҡтың ни өсөн мөһим булыуын ҡарарбыҙ."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Һуңғы бер нисә айҙа, торент сийдерҙары һаны буйынса бүленгән коллекцияларыбыҙҙың <a %(annas_archive_stats)s>дөйөм күләме</a>."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Өҫтөнлөктәр"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Ни өсөн беҙ мәҡәләләр һәм китаптар тураһында шул тиклем ҡайғыртабыҙ? Дөйөм һаҡлау тураһындағы төп ышанысыбыҙҙы ситкә ҡуйып, быға айырым пост яҙа алабыҙ. Шулай итеп, ни өсөн айырыуса мәҡәләләр һәм китаптар? Яуап ябай: <strong>мәғлүмәт тығыҙлығы</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Һаҡлауҙың һәр мегабайтына яҙма текст бөтә медиа төрҙәренән иң күп мәғлүмәтте һаҡлай. Беҙ белем һәм мәҙәниәт тураһында ҡайғыртабыҙ, ләкин беренсеһенә күберәк иғтибар итәбеҙ. Дөйөм алғанда, беҙ мәғлүмәт тығыҙлығы һәм һаҡлау мөһимлеге буйынса түбәндәгесә иерархия табабыҙ:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Академик мәҡәләләр, журналдар, отчеттар"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Органик мәғлүмәт, мәҫәлән, ДНК эҙмә-эҙлеклелеге, үҫемлек орлоҡтары йәки микроб өлгөләре"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Фәнни-популяр китаптар"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Фән һәм инженерия программалары коды"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Фәнни үлсәүҙәр, иҡтисади мәғлүмәттәр, корпоратив отчеттар кеүек үлсәү мәғлүмәттәре"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Фән һәм инженерия веб-сайттары, онлайн фекер алышыуҙар"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Документаль журналдар, гәзиттәр, ҡулланмалар"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Документаль һөйләшеүҙәр, документаль фильмдар, подкасттарҙың транскрипциялары"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Корпорациялар йәки хөкүмәттәрҙең эске мәғлүмәттәре (үткәреп ебәреүҙәр)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Метадата яҙмалары ғөмүмән (документаль һәм нәфис әҫәрҙәр; башҡа медиа, сәнғәт, кешеләр һ.б.; шул иҫәптән баһалар)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Географик мәғлүмәттәр (мәҫәлән, карталар, геологик тикшеренеүҙәр)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Юридик йәки суд эштәренең транскрипциялары"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Юғарыла әйтелгәндәрҙең нәфис йәки күңел асыу версиялары"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Был исемлектәге рейтинг бер аҙ шартлы — бер нисә элемент тигеҙ йәки беҙҙең команда эсендә бәхәстәр бар — һәм беҙ, моғайын, ҡайһы бер мөһим категорияларҙы онотҡанбыҙ. Әммә был яҡынса беҙҙең өҫтөнлөктәрҙе билдәләй."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Был элементтарҙың ҡайһы берҙәре беҙҙе борсоу өсөн артыҡ айырылып тора (йәки башҡа учреждениелар тарафынан хәстәрлек күрелгән), мәҫәлән, органик мәғлүмәттәр йәки географик мәғлүмәттәр. Әммә был исемлектәге элементтарҙың күбеһе ысынлап та беҙҙең өсөн мөһим."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Беҙҙең өҫтөнлөктәрҙе билдәләүҙә тағы бер ҙур фактор — ниндәйҙер эштең ниндәй хәүеф аҫтында булыуы. Беҙ түбәндәге эштәргә иғтибар итергә тырышабыҙ:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Һирәк"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Уникаль иғтибарһыҙ ҡалған"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Уникаль юҡҡа сығыу хәүефе аҫтында (мәҫәлән, һуғыш, финанс ҡыҫҡартыуҙар, суд эштәре йәки сәйәси эҙәрлекләү арҡаһында)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Һуңғыһы, беҙ масштабҡа иғтибар итәбеҙ. Беҙҙең ваҡыт һәм аҡса сикләнгән, шуға күрә 10 000 китапты ҡотҡарыуға бер ай сарыф итеүҙе өҫтөн күрәбеҙ, әгәр улар бер үк ҡиммәтле һәм хәүеф аҫтында булһа, 1 000 китапҡа ҡарағанда."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Күләгә китапханалары"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ошондай уҡ миссиялары һәм өҫтөнлөктәре булған күп ойошмалар бар. Ысынлап та, был төр һаҡлау менән шөғөлләнгән китапханалар, архивтар, лабораториялар, музейҙар һәм башҡа учреждениелар бар. Уларҙың күбеһе хөкүмәттәр, шәхестәр йәки корпорациялар тарафынан яҡшы финансланған. Әммә уларҙың бер ҙур һуҡыр нөктәһе бар: хоҡуҡ системаһы."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Бында күләгә китапханаларының уникаль роле һәм Аннаның Архивы барлыҡҡа килеү сәбәбе ята. Беҙ башҡаларға рөхсәт ителмәгән эштәрҙе башҡара алабыҙ. Хәҙер, был йыш ҡына башҡа урындарҙа һаҡлау тыйылған материалдарҙы архивлау мөмкинлеге түгел. Юҡ, күп урындарҙа теләһә ниндәй китаптар, ҡағыҙҙар, журналдар менән архив төҙөү законлы."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Әммә юридик архивтарҙа йыш ҡына <strong>ҡабатлау һәм оҙайлы һаҡланыу</strong> етешмәй. Донъяла ҡайһы бер физик китапханаларҙа бер генә нөсхәһе булған китаптар бар. Бер генә корпорация һаҡлаған metadata яҙмалары бар. Бер генә архивта микрофильмда һаҡланған гәзиттәр бар. Китапханалар финанслауҙы ҡыҫҡартыуы мөмкин, корпорациялар бөлөүе мөмкин, архивтар бомбаға тотолоп, яндырылыуы мөмкин. Был гипотетик түгел — был һәр ваҡыт була."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Аннаның Архивында беҙ үҙенсәлекле рәүештә күп нөсхәләрҙе һаҡлай алабыҙ, ҙур күләмдә. Беҙ ҡағыҙҙар, китаптар, журналдар һәм башҡаларҙы йыйып, күпләп тарата алабыҙ. Әлеге ваҡытта беҙ быны торренттар аша эшләйбеҙ, ләкин технологиялар мөһим түгел һәм ваҡыт үтеү менән үҙгәрәсәк. Иң мөһиме — күп нөсхәләрҙе донъя буйлап таратыу. 200 йылдан ашыу элек әйтелгән был цитата әле лә дөрөҫ:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Юғалғанды кире ҡайтарып булмай; ләкин ҡалғанды һаҡлайыҡ: уларҙы ваҡыттың юғалтыуына тапшырмай, йәмәғәт күҙенән һәм ҡулланыуҙан һаҡлап, сейфтар һәм йоҙаҡтар менән түгел, ә нөсхәләрҙе күбәйтеп, уларҙы осраҡлы хәлдәрҙән һаҡлайыҡ.</q></em><br>— Томас Джефферсон, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Йәмәғәт милке тураһында ҡыҫҡа ғына иҫкәрмә. Аннаның Архивы уникаль рәүештә донъяның күп урындарында законһыҙ булған эшмәкәрлеккә йүнәлтелгәнлектән, беҙ йәмәғәт милкендәге китаптар кеүек киң таралған коллекциялар менән шөғөлләнмәйбеҙ. Законлы ойошмалар быларҙы йыш ҡына яҡшы һаҡлай. Әммә ҡайһы бер осраҡтарҙа беҙ йәмәғәткә асыҡ коллекциялар менән дә эшләргә мәжбүрбеҙ:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata яҙмаларын Worldcat сайтында ирекле ҡарарға мөмкин, ләкин күпләп күсереп алыу мөмкин түгел (беҙ уларҙы <a %(worldcat_scrape)s>ҡырып</a> алғанға тиклем)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Код Github-та асыҡ сығанаҡлы булыуы мөмкин, ләкин Github тулыһынса еңел генә күсереп алынмай һәм һаҡланмай (әммә был осраҡта күпселек код репозиторийҙарының етерлек күсермәләре бар)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit ҡулланыу өсөн бушлай, ләкин һуңғы ваҡытта мәғлүмәткә асыҡ LLM уҡытыу арҡаһында ҡаты анти-ҡырып алыу саралары индерҙе (был хаҡта һуңыраҡ)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Күсермәләрҙе күбәйтеү"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Баштағы һорауыбыҙға кире ҡайтайыҡ: коллекцияларҙы мәңгелеккә нисек һаҡлай алабыҙ тип әйтә алабыҙ? Бында төп проблема шунда: беҙҙең коллекция <a %(torrents_stats)s>тиҙ үҫә</a>, ҙур коллекцияларҙы ҡырып һәм асыҡ сығанаҡлы итеп эшләү арҡаһында (Sci-Hub һәм Library Genesis кеүек башҡа асыҡ мәғлүмәт күләгәле китапханаларҙың ғәжәйеп эштәренә өҫтәп)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Мәғлүмәттең был үҫеше коллекцияларға донъя буйлап күсермәләр яһауҙы ауырлаштыра. Мәғлүмәт һаҡлау ҡиммәт! Ләкин беҙ оптимистик ҡарашта, айырыуса түбәндәге өс тенденцияны күҙәтеп."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Беҙ еңел табышты йыйып алдыҡ"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Был беҙҙең өҫтөнлөктәрҙән тура килә. Беҙ ҙур коллекцияларға тәү сиратта ирек биреү өҫтөнлөк бирәбеҙ. Хәҙер беҙ донъялағы иң ҙур коллекцияларҙың ҡайһы берҙәрен һаҡлағандан һуң, үҫешебеҙҙең күпкә әкренерәк булыуын көтәбеҙ."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Әле лә бәләкәй коллекцияларҙың оҙон ҡойроғо бар, һәм яңы китаптар көн һайын сканерлана йәки баҫыла, ләкин тиҙлек күпкә әкренерәк булыр. Беҙ әле лә ике йәки өс тапҡырға ҙурыраҡ булыуыбыҙ мөмкин, ләкин оҙайлы ваҡыт эсендә."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Һаҡлау сығымдары экспоненциаль рәүештә кәмей"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Яҙыу ваҡытына ҡарағанда, <a %(diskprices)s>диск хаҡтары</a> һәр ТБ өсөн яңы дисктар өсөн яҡынса $12, ҡулланылған дисктар өсөн $8, һәм таҫма өсөн $4. Әгәр беҙ һаҡсыл булһаҡ һәм тик яңы дисктарға ҡараһаҡ, был бер петабайтты һаҡлау яҡынса $12,000 тора. Әгәр беҙҙең китапхана 900ТБ-тан 2.7ПБ-ҡа өс тапҡырға артасаҡ тип фараз итһәк, был беҙҙең бөтә китапхананы күсереп алыу өсөн $32,400 тигәнде аңлата. Электр энергияһын, башҡа ҡорамалдарҙың хаҡын һәм башҡаларҙы өҫтәп, был $40,000-ға тиклем түңәрәкләйек. Ә таҫма менән $15,000–$20,000 тирәһе."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Бер яҡтан <strong>бөтә кешелек белеме өсөн $15,000–$40,000 бик арзан</strong>. Икенсе яҡтан, күп нөсхәләрҙе тулыһынса күсереп алыуҙы көтөү бер аҙ ҡиммәт, айырыуса әгәр беҙ был кешеләрҙең башҡалар файҙаһына үҙҙәренең торренттарын таратыуын теләһәк."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Был бөгөн. Ләкин прогресс алға бара:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Һәр ТБ өсөн ҡаты диск хаҡтары һуңғы 10 йылда яҡынса өс тапҡырға кәмегән, һәм ошондай уҡ тиҙлектә кәмеүен дауам итер тип көтөлә. Таҫма ла ошондай уҡ юлда. SSD хаҡтары тағы ла тиҙерәк кәмей, һәм ун йыл аҙағына тиклем HDD хаҡтарын уҙып китер."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Төрлө сығанаҡтарҙан HDD хаҡтары тенденциялары (тикшереүҙе ҡарау өсөн баҫығыҙ)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Әгәр был шулай булһа, ун йылдан һуң беҙ бөтә коллекцияны күсереп алыу өсөн тик $5,000–$13,000 ғына кәрәк булыр (1/3), йәки әгәр беҙ ҙурыраҡ булмаһаҡ, тағы ла аҙыраҡ. Был әле лә күп аҡса, ләкин күп кешеләр өсөн мөмкин буласаҡ. Һәм был киләһе пункт арҡаһында тағы ла яҡшыраҡ булыуы мөмкин…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Мәғлүмәт тығыҙлығын яҡшыртыу"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Әлеге ваҡытта беҙгә тапшырылған китаптарҙы уларҙың төп форматтарында һаҡлайбыҙ. Әлбиттә, улар ҡыҫылған, әммә йыш ҡына улар ҙур сканерҙар йәки биттәрҙең фотолары булып ҡала."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Бөгөнгә тиклем беҙҙең коллекцияның дөйөм күләмен кәметеүҙең берҙән-бер варианттары - нығыраҡ ҡыҫыу йәки дубликаттарҙы бөтөрөү булды. Әммә, етди экономияға өлгәшеү өсөн, икеһе лә беҙҙең өсөн артыҡ юғалтыуҙар менән бәйле. Фотоларҙы ныҡ ҡыҫыу текстарҙы уҡылмаҫлыҡ итә ала. Ә дубликаттарҙы бөтөрөү китаптарҙың тап шул уҡ булыуына юғары ышаныс талап итә, был йыш ҡына дөрөҫ түгел, айырыуса әгәр контент шул уҡ булһа ла, сканерҙар төрлө ваҡыттарҙа эшләнгән булһа."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Өсөнсө вариант һәр ваҡыт булған, әммә уның сифаты шул тиклем насар булған, беҙ уны бер ҡасан да ҡарамағанбыҙ: <strong>OCR, йәғни Оптик Символдарҙы Таный белеү</strong>. Был фотоларҙы ябай текстҡа әйләндереү процессы, фотоларҙағы символдарҙы таный белеү өсөн AI ҡулланыу. Был өсөн ҡоралдар күптән бар, һәм улар ярайһы уҡ яҡшы, әммә \"ярайһы уҡ яҡшы\" һаҡлау маҡсаттары өсөн етмәй."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Әммә, һуңғы күп модальлы тәрән өйрәнеү моделдәре бик тиҙ үҫеш яһаны, әммә әле лә юғары сығымдар менән. Беҙ киләһе йылдарҙа теүәллек һәм сығымдарҙың ныҡлап яҡшыртыласағына ышанабыҙ, һәм был беҙҙең бөтә китапханаға ҡулланыуҙы реаль итәсәк."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR яҡшыртыуҙары."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Был булғанда, беҙ, моғайын, төп файлдарҙы һаҡларбыҙ, әммә өҫтәп, беҙҙең китапхананың күпкә бәләкәйерәк версияһын булдыра алабыҙ, уны күпселек кеше күсереп алырға теләр. Сөнки ябай текст үҙе лә яҡшыраҡ ҡыҫыла, һәм дубликаттарҙы бөтөрөү еңелерәк, был беҙгә тағы ла күберәк экономия бирә."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Дөйөм алғанда, дөйөм файл күләмендә 5-10 тапҡырға кәметеүҙе көтөү нереаль түгел, бәлки тағы ла күберәк. Хатта һаҡсыл 5 тапҡыр кәметеү менән, беҙ 10 йыл эсендә китапхана өс тапҡырға артһа ла, <strong>$1,000–$3,000 экономияға өлгәшер инек</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Критик тәҙрә"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Әгәр был прогноздар дөрөҫ булһа, беҙгә <strong>бөтә коллекциябыҙ киң күсереп алына башлағанға тиклем бер нисә йыл ғына көтөргә кәрәк</strong>. Шул саҡта, Томас Джефферсон һүҙҙәре менән әйткәндә, \"осраҡлы хәлдәрҙән тыш\" урынлаштырыласаҡ."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Ҡыҙғанысҡа ҡаршы, LLM-дарҙың барлыҡҡа килеүе һәм уларҙың мәғлүмәткә асығыуы күп авторлыҡ хоҡуҡтары эйәләрен һаҡланыуға этәрҙе. Уларҙың күпселеге инде шулай ине. Күпселек веб-сайттарҙы күсереп алыу һәм архивлау ҡатмарлаша, суд процестары бара, һәм шул уҡ ваҡытта физик китапханалар һәм архивтар иғтибарһыҙ ҡала."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Беҙ был тенденцияларҙың насараясағын ғына көтә алабыҙ, һәм күп эштәрҙең йәмәғәт милкенә ингәнгә тиклем юғалауын көтәбеҙ."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Беҙ һаҡлауҙа революцияның башында торабыҙ, әммә <q>юғалғанды кире ҡайтарып булмай.</q></strong> Беҙҙә яҡынса 5-10 йыллыҡ критик тәҙрә бар, шул ваҡыт эсендә күләгәле китапхана эшләү һәм донъя буйлап күп күсермәләр булдырыу әле лә ҡиммәт, һәм шул ваҡыт эсендә инеү тулыһынса ябылмаған."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Әгәр беҙ был тәҙрәне күпереп үтә алһаҡ, беҙ ысынлап та кешелектең белем һәм мәҙәниәтен мәңгелеккә һаҡлар инек. Был ваҡытты бушҡа үткәрмәҫкә тейешбеҙ. Был критик тәҙрәне ябыуҙы үҙебеҙгә рөхсәт итмәҫкә тейешбеҙ."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Әйҙәгеҙ, эшкә тотонайыҡ."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Донъялағы иң ҙур ҡытайса фәнни-ғәмәли китаптар коллекцияһына LLM компаниялары өсөн эксклюзив инеү"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Ҡытай версияһы 中文版</a>, <a %(news_ycombinator)s>Hacker News-та фекер алышыу</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Ҡыҫҡаса: </strong> Аннаның Архивы 7,5 миллион / 350TB ҡытайса фәнни-ғәмәли китаптарҙың уникаль коллекцияһын алды — Library Genesis-тан ҙурыраҡ. Беҙ LLM компанияһына эксклюзив инеү бирергә әҙербеҙ, юғары сифатлы OCR һәм текст сығарыу менән алмашҡа.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Был ҡыҫҡа блог яҙмаһы. Беҙ ҙур коллекция өсөн OCR һәм текст сығарыуҙа ярҙам итеүсе компания йәки учреждение эҙләйбеҙ, эксклюзив иртә инеү менән алмашҡа. Эмбарго осоронан һуң, әлбиттә, беҙ бөтә коллекцияны сығарасаҡбыҙ."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "LLM-дарҙы уҡытыу өсөн юғары сифатлы академик текст бик файҙалы. Беҙҙең коллекция ҡытайса булһа ла, был инглиз LLM-дарын уҡытыу өсөн дә файҙалы булырға тейеш: моделдәр концепцияларҙы һәм белемде сығанаҡ теленә ҡарамаҫтан кодлай кеүек."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Бының өсөн текст сканерҙарҙан сығарылырға тейеш. Аннаның Архивы бының менән нимә ала? Китаптарҙың тулы текст эҙләү мөмкинлеген ҡулланыусылары өсөн."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Сөнки беҙҙең маҡсаттар LLM эшләүселәрҙең маҡсаттары менән тап килә, беҙ хеҙмәттәшлек эҙләйбеҙ. Әгәр һеҙ дөрөҫ OCR һәм текст сығарыу эшен башҡара алһағыҙ, беҙ һеҙгә был коллекцияға 1 йыл дауамында күпләп эксклюзив иртә инеү мөмкинлеге бирергә әҙербеҙ. Әгәр һеҙ үҙегеҙҙең бөтә кодты беҙгә бүлешергә әҙер булһағыҙ, беҙ коллекцияны оҙағыраҡ ваҡытҡа эмбаргоға ҡуйырға әҙербеҙ."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Өлгө биттәр"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Беҙгә һеҙҙең яҡшы эшкәртеү системағыҙ барлығын иҫбатлау өсөн, бына башлау өсөн ҡайһы бер өлгө биттәр, суперпроводниктар тураһында китаптан. Һеҙҙең системағыҙ математиканы, таблицаларҙы, диаграммаларҙы, иҫкәрмәләрҙе һәм башҡаларҙы дөрөҫ эшкәртергә тейеш."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Эшкәртелгән биттәрегеҙҙе беҙҙең электрон почтаға ебәрегеҙ. Әгәр улар яҡшы күренһә, беҙ һеҙгә шәхси рәүештә күберәк ебәрербеҙ, һәм беҙ һеҙҙең уларҙы тиҙ арала эшкәртеү системағыҙҙа эшкәртә алырһығыҙ тип көтәбеҙ. Беҙ ҡәнәғәт булғас, килешеү төҙөй алабыҙ."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Коллекция"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Коллекция тураһында тағы ла күберәк мәғлүмәт. <a %(duxiu)s>Duxiu</a> — <a %(chaoxing)s>SuperStar Digital Library Group</a> тарафынан булдырылған сканерланған китаптарҙың ҙур базаһы. Күпселеге академик китаптар, улар университеттар һәм китапханалар өсөн цифрлы форматта ҡулланыу өсөн сканерланған. Инглиз телле аудиториябыҙ өсөн <a %(library_princeton)s>Принстон</a> һәм <a %(guides_lib_uw)s>Вашингтон университеты</a> яҡшы күҙәтеүҙәр бирә. Шулай уҡ күберәк мәғлүмәт биргән бик яҡшы мәҡәлә бар: <a %(doi)s>“Ҡытай китаптарын цифрлаштырыу: SuperStar DuXiu Scholar Search Engine буйынса осраҡ өйрәнеү”</a> (Аннаның Архивында эҙләгеҙ)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Duxiu китаптары ҡытай интернетында күптән пиратланған. Ғәҙәттә уларҙы һатыусылар бер долларҙан кәмерәк хаҡҡа һата. Улар ғәҙәттә Google Drive-тың ҡытайса эквиваленты ҡулланып таратыла, ул йыш ҡына күберәк һаҡлау урыны өсөн хакерланған. Ҡайһы бер техник деталдәрҙе <a %(github_duty_machine)s>бында</a> һәм <a %(github_821_github_io)s>бында</a> табырға мөмкин."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Китаптар ярым-асыҡ рәүештә таратылһа ла, уларҙы күпләп алыу бик ауыр. Беҙҙең эштәр исемлегендә был юғары урында ине, һәм беҙ быға тулы ваҡытлы эш өсөн бер нисә ай бүлдек. Әммә, һуңғы ваҡытта, ғәжәйеп, иҫ киткес, һәм талантлы ирекмән беҙгә мөрәжәғәт итте, улар был эште ҙур сығымдар менән башҡарғандарын әйтте. Улар беҙгә тулы коллекцияны бер нәмә лә көтмәйенсә бүлеште, оҙайлы ваҡыт һаҡлау гарантияһы ғына һораны. Ысынлап та иҫ киткес. Улар коллекцияны OCR-лау өсөн ярҙам һорарға ризалашты."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Коллекция 7,543,702 файлдан тора. Был Library Genesis-тың фәнни булмаған өлөшөнән күберәк (яҡынса 5,3 миллион). Дөйөм файл күләме уның хәҙерге формаһында яҡынса 359TB (326TiB)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Беҙ башҡа тәҡдимдәргә һәм идеяларға асыҡбыҙ. Беҙгә мөрәжәғәт итегеҙ. Беҙҙең коллекциялар, һаҡлау тырышлыҡтары һәм нисек ярҙам итә алырығыҙ тураһында күберәк мәғлүмәт өсөн Аннаның Архивын ҡарағыҙ. Рәхмәт!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Иҫкәртеү: был блог яҙмаһы иҫкергән. Беҙ IPFS-тың әле төп ваҡытҡа әҙер түгел тип ҡарар иттек. Беҙ Аннаның Архивынан IPFS-ҡа файлдарға һылтанма бирәбеҙ, әммә үҙебеҙ уны ҡабул итмәйәсәкбеҙ, һәм башҡаларға IPFS ҡулланып күсермә яһарға тәҡдим итмәйбеҙ. Коллекциябыҙҙы һаҡларға ярҙам итергә теләһәгеҙ, беҙҙең Торренттар битен ҡарағыҙ."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Z-Library-ҙы IPFS-та таратыуға ярҙам итегеҙ"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Күләгәле китапхана нисек эшләргә: Аннаның Архивында операциялар"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Күләгәле хәйриәләр өсөн <q>AWS юҡ,</q> шулай булғас, Аннаның Архивын нисек эшләтеп ебәрәбеҙ?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Мин <a %(wikipedia_annas_archive)s>Аннаның Архивын</a> етәкләйем, Sci-Hub, Library Genesis һәм Z-Library кеүек күләгәле китапханалар өсөн донъяның иң ҙур асыҡ сығанаҡлы коммерцияһыҙ эҙләү системаһы. Беҙҙең маҡсат — белем һәм мәҙәниәтте еңел ҡулланыу мөмкинлеген булдырыу, һәм ахыр сиктә бөтә донъя китаптарын архивлау һәм һаҡлау өсөн бергәләп эшләгән кешеләрҙең берләшмәһен төҙөү."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Был мәҡәләлә мин был веб-сайтты нисек эшләтеп ебәреүебеҙҙе һәм законлы статусы шикле булған веб-сайтты эшләтеп ебәреү менән бәйле уникаль ҡыйынлыҡтарҙы күрһәтәсәкмен, сөнки күләгәле хәйриәләр өсөн “AWS” юҡ."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Шулай уҡ <a %(blog_how_to_become_a_pirate_archivist)s>Пират архивист булыу</a> тураһында туған мәҡәләне ҡарағыҙ.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Инновация токендары"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Техник стектан башлайыҡ. Ул махсус рәүештә ябай. Беҙ Flask, MariaDB һәм ElasticSearch ҡулланабыҙ. Был барыһы ла. Эҙләү мәсьәләһе күпселек хәл ителгән, һәм беҙ уны яңынан уйлап сығарырға йыйынмайбыҙ. Бынан тыш, беҙ <a %(mcfunley)s>инновация токендары</a>н башҡа нәмәгә сарыф итергә тейешбеҙ: властар тарафынан ябылмауға."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Шулай итеп, Аннаның Архивы ни тиклем законлы йәки законһыҙ? Был күбеһенсә юридик юрисдикцияға бәйле. Күпселек илдәр ниндәйҙер авторлыҡ хоҡуғына ышана, был кешеләргә йәки компанияларға билдәле бер төр эштәргә билдәле бер ваҡытҡа эксклюзив монополия бирелеүен аңлата. Ҡыҫҡаса әйткәндә, Аннаның Архивында беҙ ҡайһы бер өҫтөнлөктәр булһа ла, дөйөм алғанда авторлыҡ хоҡуғы йәмғиәт өсөн кире йоғонто яһай тип ышанабыҙ — ләкин был башҡа ваҡыт өсөн тарих."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Билдәле бер эштәргә эксклюзив монополия булыуы, был монополиянан тыш кемгәлер был эштәрҙе туранан-тура таратыу законһыҙ булыуын аңлата — шул иҫәптән беҙгә лә. Ләкин Аннаның Архивы — туранан-тура был эштәрҙе таратмаған эҙләү системаһы (кәм тигәндә беҙҙең асыҡ сайтта түгел), шуға күрә беҙгә барыһы ла яҡшы булырға тейеш, шулаймы? Тап шулай түгел. Күп юрисдикцияларҙа авторлыҡ хоҡуғы менән яҡланған эштәрҙе таратыу ғына түгел, ә уларға һылтанмалар биреү ҙә законһыҙ. Былтың классик миҫалы — Америка Ҡушма Штаттарының DMCA законы."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Был спектрҙың иң ҡәтғи осо. Спектрҙың икенсе осонда теоретик рәүештә бөтөнләй авторлыҡ хоҡуғы булмаған илдәр булыуы мөмкин, ләкин улар ысынбарлыҡта юҡ. Тиерлек һәр илдә ниндәйҙер авторлыҡ хоҡуғы законы бар. Законды үтәү — башҡа тарих. Авторлыҡ хоҡуғы закондарын үтәүҙе теләмәгән хөкүмәтле илдәр күп. Шулай уҡ ике край араһында урынлашҡан илдәр бар, улар авторлыҡ хоҡуғы менән яҡланған эштәрҙе таратыуҙы тыйһа ла, уларға һылтанмалар биреүҙе тыймай."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Тағы бер ҡараш — компания кимәлендә. Әгәр компания авторлыҡ хоҡуғына иғтибар итмәгән юрисдикцияла эшләһә лә, компания үҙе бер ниндәй ҙә хәүефкә барырға теләмәй икән, улар һеҙҙең сайтты кемдер уға зарланһа, шунда уҡ ябырға мөмкин."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Һуңғыһы, ҙур ҡараш — түләүҙәр. Аноним булып ҡалырға кәрәк булғанлыҡтан, беҙ традицион түләү ысулдарын ҡуллана алмайбыҙ. Был беҙҙе криптовалюталар менән сикләй, һәм уларҙы тик бер нисә компания ғына хуплай (крипто менән түләнгән виртуаль дебет карталары бар, ләкин улар йыш ҡына ҡабул ителмәй)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Система архитектураһы"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Шулай итеп, әйтәйек, һеҙҙе сайтты ябмайынса хостинг менән тәьмин итергә әҙер компаниялар таптығыҙ — уларҙы “ирек һөйөүселәр” тип атайыҡ 😄. Һеҙ тиҙ арала улар менән барыһын да хостинг итеү бик ҡиммәт икәнен аңларһығыҙ, шуға күрә “арзан тәьмин итеүселәр”ҙе табып, ысын хостингты унда эшләргә теләрһегеҙ, ирек һөйөүселәр аша прокси итеп. Әгәр һеҙ уны дөрөҫ эшләһәгеҙ, арзан тәьмин итеүселәр һеҙ нимә хостинг иткәнегеҙҙе белмәҫ һәм бер ниндәй ҙә зарланмаҫ."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Был тәьмин итеүселәрҙең барыһы менән дә һеҙҙе барыбер ябып ҡуйыу хәүефе бар, шуға күрә һеҙгә резерв кәрәк. Беҙгә был стектың бөтә кимәлдәрендә кәрәк."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Бер аҙ ирек һөйөүсән компания, үҙен ҡыҙыҡлы позицияға ҡуйған — Cloudflare. Улар үҙҙәрен хостинг тәьмин итеүсе түгел, ә ISP кеүек коммуналь хеҙмәт тип <a %(blog_cloudflare)s>дәлилләгәндәр</a>. Шуға күрә улар DMCA йәки башҡа ябылыу һорауҙарына буйһонмай, һәм һеҙҙең ысын хостинг тәьмин итеүсегә һорауҙарҙы ебәрәләр. Улар был структураны яҡлау өсөн судҡа тиклем барғандар. Шуға күрә беҙ уларҙы кэшлау һәм һаҡлау өсөн тағы бер ҡатлам итеп ҡуллана алабыҙ."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare аноним түләүҙәрҙе ҡабул итмәй, шуға күрә беҙ уларҙың бушлай планын ғына ҡуллана алабыҙ. Был уларҙың йөкләмәне баланслау йәки резервлау функцияларын ҡуллана алмауыбыҙҙы аңлата. Шуға күрә беҙ быларҙы домен кимәлендә <a %(annas_archive_l255)s>үҙебеҙ ғәмәлгә ашырҙыҡ</a>. Битте йөкләгәндә, браузер ағымдағы домендың әле лә барлығын тикшерә, һәм әгәр юҡ икән, бөтә URL-дарҙы башҡа доменға үҙгәртә. Cloudflare күп биттәрҙе кэшлай, был ҡулланыусының төп доменға эләгеүен аңлата, хатта прокси сервер эшләмәһә лә, һәм артабанғы баҫҡанда икенсе доменға күсерелә."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Беҙ шулай уҡ серверҙың торошон күҙәтеү, артҡы һәм алғы яҡ хаталарын теркәү кеүек ғәҙәти эш мәсьәләләре менән дә шөғөлләнергә тейешбеҙ. Беҙҙең резерв архитектура был йәһәттән дә ныҡлыҡты арттыра, мәҫәлән, домендарҙың береһендә бөтөнләй башҡа серверҙар йыйылмаһын эшләтеп. Беҙ хатта төп версиялағы критик хата иғтибарҙан ситтә ҡалһа, был айырым доменда кодтың һәм datasets-тың иҫке версияларын эшләтә алабыҙ."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Беҙ шулай уҡ Cloudflare-ҙың беҙгә ҡаршы сығыуына ҡаршы һаҡлана алабыҙ, уны бер домендан, мәҫәлән, был айырым домендан алып ташлап. Был идеяларҙың төрлө комбинациялары мөмкин."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ҡоралдар"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Быларҙың барыһын да башҡарыу өсөн ниндәй ҡоралдар ҡулланыуыбыҙға ҡарайыҡ. Был яңы проблемаларға осраған һайын һәм яңы ҡарарҙар тапҡан һайын үҙгәреп тора."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Ҡушымта серверы: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Прокси сервер: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Сервер менән идара итеү: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Үҫтереү: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Тор статик хостинг: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Беҙ ҡайһы бер ҡарарҙарҙы ҡабат-ҡабат ҡараныҡ. Берәүһе серверҙар араһындағы бәйләнеш: быға тиклем беҙ бының өсөн Wireguard ҡуллана инек, ләкин ул ҡайһы саҡта бер ниндәй ҙә мәғлүмәт ебәрмәй башлай, йәки мәғлүмәтте тик бер йүнәлештә генә ебәрә. Был бер нисә төрлө Wireguard көйләүҙәрендә булды, мәҫәлән, <a %(github_costela_wesher)s>wesher</a> һәм <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Шулай уҡ SSH аша порттарҙы туннелләүҙе, autossh һәм sshuttle ҡулланып ҡараныҡ, ләкин унда <a %(github_sshuttle)s>проблемаларға осраныҡ</a> (автоссх TCP-өҫтөндә-TCP проблемаларынан яфаланамы, юҡмы — был миңә әле лә аныҡ түгел — был миңә яраҡһыҙ хәл кеүек тойола, ләкин, бәлки, был ысынлап та яҡшы эшләйҙер?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Бының урынына, беҙ серверҙар араһында тура бәйләнештәргә кире ҡайттыҡ, арзан провайдерҙарҙа сервер эшләүен IP-фильтрлау менән UFW ҡулланып йәшерҙек. Был UFW менән Docker яҡшы эшләмәүе менән бәйле, әгәр һеҙ <code>network_mode: \"host\"</code> ҡулланмаһағыҙ. Былар барыһы ла хаталарға күберәк бирелеүсән, сөнки һеҙ серверығыҙҙы интернетҡа бәләкәй генә конфигурация хатаһы менән асып ҡуйыуығыҙ мөмкин. Бәлки, беҙгә кире автоссх-ҡа күсергә кәрәктер — бында фекерҙәрегеҙ бик кәрәк булыр ине."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Беҙ шулай уҡ Varnish менән Nginx араһында ла ҡарарҙарҙы ҡабат-ҡабат ҡараныҡ. Әлеге ваҡытта Varnish оҡшай, ләкин уның үҙенсәлектәре һәм ҡырҡыу мөйөштәре бар. Шул уҡ хәл Checkmk өсөн дә: беҙ уны яратмайбыҙ, ләкин ул әлегә эшләп тора. Weblate ярайһы, ләкин иҫ киткес түгел — мин уны беҙҙең git репо менән синхронлаштырырға тырышҡанда мәғлүмәтемде юғалтыр тип ҡайһы саҡта ҡурҡам. Flask дөйөм алғанда яҡшы, ләкин уның ҡайһы бер сәйер үҙенсәлектәре бар, уларҙы отладкалау күп ваҡытты алды, мәҫәлән, махсус домендарҙы көйләү, йәки уның SqlAlchemy интеграцияһы менән проблемалар."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Әлегә башҡа ҡоралдар бик яҡшы булды: MariaDB, ElasticSearch, Gitlab, Zulip, Docker һәм Tor тураһында етди дәғүәләр юҡ. Быларҙың барыһында ла ҡайһы бер проблемалар булды, ләкин бер ниндәй ҙә етди йәки ваҡытты күп ала торған проблемалар юҡ."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Йомғаҡ"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Күләгәле китапхана эҙләү системаһын нисек ныҡлы һәм сыҙамлы итеп көйләүҙе өйрәнеү ҡыҙыҡлы тәжрибә булды. Киләсәктә уртаҡлашыр өсөн бик күп деталдәр бар, шуға күрә һеҙ нимә тураһында күберәк белергә теләйһегеҙ икәнен әйтегеҙ!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Һәр ваҡыттағыса, был эште хуплау өсөн иғәнәләр эҙләйбеҙ, шуға күрә Аннаның Архивы сайтындағы Иғәнә битен тикшерергә онотмағыҙ. Шулай уҡ башҡа ярҙам төрҙәрен дә эҙләйбеҙ, мәҫәлән, гранттар, оҙайлы ваҡытлы спонсорҙар, юғары хәүефле түләү провайдерҙары, бәлки хатта (тәмле!) реклама. Әгәр һеҙ үҙегеҙҙең ваҡыт һәм күнекмәләрегеҙҙе индерергә теләһәгеҙ, беҙ һәр ваҡыт эшләүселәр, тәржемәселәр һәм башҡаларҙы эҙләйбеҙ. Ҡыҙыҡһыныуығыҙ һәм ярҙамығыҙ өсөн рәхмәт."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Анна һәм команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Сәләм, мин Анна. Мин <a %(wikipedia_annas_archive)s>Аннаның Архивы</a>н, донъяның иң ҙур күләгәле китапханаһын булдырҙым. Был минең шәхси блогым, унда мин һәм минең команда ағзалары пиратлыҡ, цифрлы һаҡлау һәм башҡа темалар тураһында яҙабыҙ."

#, fuzzy
msgid "blog.index.text2"
msgstr "Миңә <a %(reddit)s>Reddit</a> аша тоташығыҙ."

#, fuzzy
msgid "blog.index.text3"
msgstr "Иғтибар итегеҙ, был сайт тик блог. Бында беҙ тик үҙ һүҙҙәребеҙҙе генә урынлаштырабыҙ. Бында бер ниндәй ҙә торренттар йәки башҡа авторлыҡ хоҡуҡлы файлдар урынлаштырылмаған йәки бәйләнмәгән."

#, fuzzy
msgid "blog.index.heading"
msgstr "Блог яҙмалары"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat мәғлүмәт йыйыу"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5,998,794 китапты IPFS-ҡа урынлаштырыу"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Иҫкәртеү: был блог яҙмаһы иҫкергән. Беҙ IPFS-тың әле төп ваҡытҡа әҙер түгел тип ҡарар иттек. Беҙ Аннаның Архивынан IPFS-ҡа файлдарға һылтанма бирәбеҙ, әммә үҙебеҙ уны ҡабул итмәйәсәкбеҙ, һәм башҡаларға IPFS ҡулланып күсермә яһарға тәҡдим итмәйбеҙ. Коллекциябыҙҙы һаҡларға ярҙам итергә теләһәгеҙ, беҙҙең Торренттар битен ҡарағыҙ."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Ҡыҫҡаса:</strong> Аннаның Архивы бөтә WorldCat (донъяның иң ҙур китапхана metadata коллекцияһы) мәғлүмәтен йыйып, һаҡланырға тейеш китаптарҙың эштәр исемлеген төҙөнө.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Бер йыл элек беҙ был һорауға <a %(blog)s>яуап эҙләй башланыҡ</a>: <strong>Китаптарҙың ниндәй проценты күләгәле китапханалар тарафынан мәңгелеккә һаҡланған?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Бер китап <a %(wikipedia_library_genesis)s>Library Genesis</a> кеүек асыҡ мәғлүмәт күләгәле китапханаға, һәм хәҙер <a %(wikipedia_annas_archive)s>Аннаның Архивы</a>на ингәс, ул бөтә донъя буйлап (торренттар аша) күсерелә, шуның менән уны мәңгелеккә һаҡлай."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Китаптарҙың ниндәй проценты һаҡланғанын белеү өсөн, беҙгә дөйөм һан кәрәк: бөтәһе нисә китап бар? Һәм идеаль рәүештә беҙҙә тик һан ғына түгел, ә ысын metadata булырға тейеш. Шунан беҙ уларҙы күләгәле китапханалар менән сағыштыра алабыҙ, шулай уҡ һаҡланырға тейеш китаптарҙың <strong>эштәр исемлеген төҙөй алабыҙ!</strong> Хатта был эштәр исемлеген үтәү өсөн халыҡтан ярҙам йыйыу тураһында хыяллана башлай алабыҙ."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Беҙ <a %(wikipedia_isbndb_com)s>ISBNdb</a> сайтынан мәғлүмәт йыйҙыҡ һәм <a %(openlibrary)s>Open Library dataset</a> йөкләнек, ләкин һөҙөмтәләр ҡәнәғәтләнерлек булманы. Төп проблема ISBN-дарҙың күпләп тап килмәүендә ине. Беҙҙең блог яҙмаһынан был Венн диаграммаһын ҡарағыҙ:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Беҙ ISBNdb һәм Open Library араһында нисек аҙ тап килеү булыуына бик аптырағайныҡ, сөнки улар икеһе лә төрлө сығанаҡтарҙан, мәҫәлән, веб-скрейптар һәм китапхана яҙмаларынан мәғлүмәтте иркен индерә. Әгәр улар икеһе лә ундағы күпселек ISBN-дарҙы табыуҙа яҡшы эшләһә, уларҙың түңәрәктәре һис шикһеҙ ҙур тап килеүгә эйә булыр ине, йәки береһе икенсеһенең өлөшө булыр ине. Был беҙҙе уйландырҙы, нисә китап был түңәрәктәрҙән тулыһынса ситтә ҡала икән? Беҙгә ҙурыраҡ база кәрәк."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Шул саҡта беҙ донъяның иң ҙур китап базаһына күҙ һалырға булдыҡ: <a %(wikipedia_worldcat)s>WorldCat</a>. Был коммерцияға ҡарамаған <a %(wikipedia_oclc)s>OCLC</a> ойошмаһының базаһы, ул бөтә донъя китапханаларынан metadata яҙмаларын йыйып, уларға тулы мәғлүмәт базаһына инеү мөмкинлеге бирә һәм уларҙы ҡулланыусыларҙың эҙләү һөҙөмтәләрендә күрһәтеүҙе тәьмин итә."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "OCLC коммерцияға ҡарамаған булһа ла, уларҙың бизнес моделе мәғлүмәт базаһын һаҡлауҙы талап итә. Ярай, OCLC дуҫтары, беҙ барыһын да бушлай бирәбеҙ. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Үткән йыл эсендә беҙ WorldCat яҙмаларын ентекләп йыйҙыҡ. Башта беҙгә уңыш йылмайҙы. WorldCat үҙҙәренең тулы сайттарын яңыртыуҙы башланы (2022 йылдың авгусында). Был уларҙың артҡы системаларын ҙур үҙгәртеүҙе үҙ эсенә алды, күп кенә хәүефһеҙлек етешһеҙлектәрен индерҙе. Беҙ шунда уҡ мөмкинлектән файҙаландыҡ һәм бер нисә көн эсендә йөҙ миллион (!) яҙмаларҙы йыйып алдыҡ."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat яңыртыу</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Шунан һуң хәүефһеҙлек етешһеҙлектәре берәмләп төҙәтелде, һуңғыһы яҡынса бер ай элек төҙәтелде. Шул ваҡытҡа беҙҙә тиерлек бөтә яҙмалар бар ине, һәм беҙ тик бер аҙ юғарыраҡ сифатлы яҙмаларҙы ғына йыйырға тырыштыҡ. Шуға күрә беҙ сығарырға ваҡыт тип һананыҡ!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Мәғлүмәт тураһында ҡайһы бер төп мәғлүмәттәргә күҙ һалайыҡ:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Формат?</strong> <a %(blog)s>Аннаның Архив Контейнерҙары (AAC)</a>, ул <a %(jsonlines)s>JSON Lines</a> менән <a %(zstd)s>Zstandard</a> ярҙамында ҡыҫылған, өҫтәүенә ҡайһы бер стандартлаштырылған семантикалар. Был контейнерҙар беҙ ҡулланған төрлө скрейптарға нигеҙләнгән төрлө типтағы яҙмаларҙы уратып ала."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Мәғлүмәт"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Билгесез хата килеп чыкты. Скриншот белән %(email)s адресына мөрәҗәгать итегез."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Бу тәңкә гадәттәгедән югарырак минималга ия. Зинһар, башка вакыт яки башка тәңкә сайлагыз."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Сорауны үтәп булмады. Берничә минуттан кабатлап карагыз, әгәр дә бу кабатлана икән, скриншот белән %(email)s адресына мөрәҗәгать итегез."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Түләү эшкәртүдә хата. Бер мизгел көтеп, кабатлап карагыз. Әгәр дә мәсьәлә 24 сәгатьтән артык дәвам итсә, скриншот белән %(email)s адресына мөрәҗәгать итегез."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "йәшерен аңлатма"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Файл мәсьәләһе: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Яҡшыраҡ версия"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Был ҡулланыусыны насар йәки яраҡһыҙ тәртибе өсөн хәбәр итергә теләйһегеҙме?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Насарлыҡты хәбәр итеү"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Насарлыҡ хәбәр ителде:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Һеҙ был ҡулланыусыны насарлыҡ өсөн хәбәр иттегеҙ."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Яуап биреү"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Зинһар, <a %(a_login)s>логин яһағыҙ</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Һеҙ фекер ҡалдырҙығыҙ. Ул бер минут эсендә күрһәтелергә мөмкин."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Нимәлер дөрөҫ булманы. Битте яңыртып, ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s тәэсирләнгән битләр"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Libgen.rs Non-Fiction күренми"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Libgen.rs Fiction күренми"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Libgen.li күренми"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Libgen.li сайтында бозылган дип билгеләнгән"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Z-Library сайтында юк"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Z-Library-ҙа “спам” тип билдәләнгән"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "“насар файл” типендә билдәләнгән"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Барлык битләрне PDF форматына күчереп булмады"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Бу файлда exiftool эшләтеп булмады"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Китап (билгесез)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Китап (нәфис булмаган әдәбият)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Китап (нәфис әдәбият)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Журнал мәкаләсе"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Стандартлар документы"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Журнал"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Комик китап"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Музыкаль партитура"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Аудиокитап"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Башҡа"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Партнёр серверынан күсереп алыу"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Тышҡы күсереп алыу"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Тышҡы ҡарызға алыу"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Тышҡы ҡарызға алыу (басма мөмкинлеге юҡ)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Метаданные өйрәнеү"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Торренттарҙа бар"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Ҡытайса"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "AA-ға йөкләүҙәр"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Чех метадатаһы"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Рәсәй дәүләт китапханаһы"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Титул"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Автор"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Нәшриәт"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Басма"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Басылған йыл"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Тәүге файл исеме"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Тасуирлама һәм метадата аңлатмалары"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Загрузки с партнёрского сервера временно недоступны для этого файла."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Тиҙ Партнер Серверы #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(тәҡдим ителә)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(браузер тикшереүе йәки көтөү исемлеге юҡ)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Аҡрын Партнер Серверы #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(бераҙ тиҙерәк, ләкин көтөү исемлеге бар)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(көтөү исемлеге юҡ, ләкин бик аҡрын булыуы мөмкин)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Нон-фикшн"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Фикшн"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(также нажмите «GET» вверху)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(нажмите «GET» вверху)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "их реклама может содержать вредоносное ПО, поэтому используйте блокировщик рекламы или не нажимайте на рекламу"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC файлдарын күсереп алыу ышанысһыҙ булыуы мөмкин)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library на Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(требуется браузер Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Взять в аренду из Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(только для пользователей с ограниченными возможностями печати)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(связанный DOI может быть недоступен в Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "коллекция"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "торрент"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Массовые загрузки торрентов"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(только для экспертов)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Поиск по ISBN в Anna’s Archive"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Поиск по различным другим базам данных по ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Найти оригинальную запись в ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Анна архивынан Open Library ID буйынса эҙләү"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Open Library-ҙа төп яҙманы табыу"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Анна архивынан OCLC (WorldCat) номеры буйынса эҙләү"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "WorldCat-та төп яҙманы табыу"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Анна архивынан DuXiu SSID номеры буйынса эҙләү"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "DuXiu-ҙа ҡулдан эҙләү"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Анна архивынан CADAL SSNO номеры буйынса эҙләү"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "CADAL-да төп яҙманы табыу"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Анна архивынан DuXiu DXID номеры буйынса эҙләү"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Анна архивы 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(браузер тикшереүе талап ителмәй)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Чех метадатаһы %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Трантор %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Метадата"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "тасуирлама"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Альтернатив файл исеме"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Альтернатив титул"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Альтернатив автор"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Альтернатив нәшриәт"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Альтернатив басма"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Альтернативное расширение"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "метадата аңлатмалары"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Альтернатив тасуирлама"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "дата открытия исходного кода"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub файлы “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Контролдәге Цифрлы Аренда файлы “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Был Internet Archive-тан файл яҙмаһы, тура йөкләп алыу файлы түгел. Һеҙ китапты ҡуртымға алырға тырыша алаһығыҙ (түбәндәге һылтанма), йәки был URL-ды <a %(a_request)s>файл һорағанда</a> ҡуллана алаһығыҙ."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Әгәр һеҙҙә был файл бар һәм ул әле Анна архивында юҡ икән, <a %(a_request)s>йөкләп ҡуйырға</a> уйлағыҙ."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) номеры %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s метадата яҙмаһы"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Был метадата яҙмаһы, күсереп алыу файлы түгел. <a %(a_request)s>Файл һорағанда</a> был URL-ды ҡуллана алаһығыҙ."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Бәйле яҙманың метадатаһы"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Open Library-ҙа метадатаһын яҡшыртыу"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Иҫкәртеү: бер нисә бәйле яҙма:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Метадатаны яҡшыртыу"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Файл сифатын хәбәр итеү"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Күсереп алыу ваҡыты"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Веб-сайт:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "“%(name)s” өсөн Аннаның Архивында эҙләү"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Кодтар Эҙләүсеһе:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Кодтар Эҙләүсеһендә “%(name)s” ҡарау"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Тулыраҡ уҡыу…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Күсереп алыуҙар (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Алып тороу (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Метадатаны өйрәнеү (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Фекерҙәр (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Исемлектәр (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Статистика (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Техник детальдәр"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Был файлда проблемалар булыуы мөмкин, һәм ул сығанаҡ китапхананан йәшерелгән.</span> Ҡайһы берҙә был автор хоҡуҡтары эйәһенең үтенесе буйынса, ҡайһы берҙә яҡшыраҡ альтернатива булған өсөн, әммә ҡайһы берҙә файлдың үҙендә проблема булған өсөн. Күсереп алыу өсөн яраҡлы булыуы мөмкин, әммә башта альтернатива файл эҙләүҙе тәҡдим итәбеҙ. Тулыраҡ мәғлүмәт:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Был файлдың яҡшыраҡ версияһы %(link)s булыуы мөмкин"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Әгәр ҙә был файлды күсереп алырға теләһәгеҙ, уны асыу өсөн тик ышаныслы, яңыртылған программалар ҡулланығыҙ."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Тиҙ йөкләүҙәр"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Тиҙ күсереп алыуҙар</strong> Китаптар, мәҡәләләр һәм башҡаларҙы оҙайлы һаҡлауҙы тәьмин итеү өсөн <a %(a_membership)s>ағза</a> булығыҙ. Һеҙҙең ярҙамығыҙ өсөн рәхмәтебеҙҙе белдереү өсөн, тиҙ күсереп алыуҙар тәҡдим итәбеҙ. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Әгәр һеҙ был айҙа иғәнә индерһәгеҙ, <strong>ике тапҡыр</strong> күберәк тиҙ загрузкалар алаһығыҙ."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Тиҙ күсереп алыуҙар</strong> Бөгөн һеҙҙә %(remaining)s ҡалды. Ағза булғанығыҙ өсөн рәхмәт! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Тиҙ тейәүҙәр</strong> Бөгөнгө тиҙ тейәүҙәр лимиты тамамланды."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Тиҙ тейәүҙәр</strong> Һеҙ был файлды яңыраҡ тейәнегеҙ. Һылтанмалар бер аҙ ваҡытҡа тиклем ғәмәлдә ҡала."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Вариант #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(юлланма юҡ)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(ҡараусыла асыу)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Дуҫығыҙҙы саҡырығыҙ, һәм һеҙ ҙә, дуҫығыҙ ҙа %(percentage)s%% бонус тиҙ тейәүҙәр алығыҙ!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Тулыраҡ мәғлүмәт…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Аҡрын тейәүҙәр"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Ышаныслы партнерҙарҙан."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Тулыраҡ мәғлүмәт <a %(a_slow)s>Йыш бирелгән һорауҙар</a> бүлегендә."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(мөмкин <a %(a_browser)s>браузерҙы раҫлау</a> талап ителер — сикһеҙ тейәүҙәр!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Күсереп алғандан һуң:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Беҙҙең ҡараусыла асыу"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "тышҡы йөкләүҙәрҙе күрһәтеү"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Тышҡы тейәүҙәр"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Тейәүҙәр табылманы."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Бөтә тейәү варианттары бер үк файлды тәҡдим итә, һәм ҡулланыу өсөн хәүефһеҙ булырға тейеш. Шулай ҙа, интернеттан файлдар тейәгәндә һәр ваҡыт һаҡ булығыҙ, бигерәк тә Anna’s Archive сайтынан тыш сығанаҡтарҙан. Мәҫәлән, ҡоролмаларығыҙҙы яңыртып тороуҙы онотмағыҙ."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Ҙур файлдар өсөн, өҙөлөүҙәрҙе булдырмаҫ өсөн, күсереп алыу менеджерын ҡулланыуҙы тәҡдим итәбеҙ."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Тәҡдим ителгән күсереп алыу менеджерҙары: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Файлды асыу өсөн, файл форматына ҡарап, электрон китап йәки PDF уҡыусы кәрәк буласаҡ."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Тәҡдим ителгән электрон китап уҡыусылары: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Аннаның Архивы онлайн ҡараусыһы"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Форматтар араһында үҙгәртеү өсөн онлайн ҡоралдар ҡулланығыҙ."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Тәҡдим ителгән үҙгәртеү ҡоралдары: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Һеҙ PDF һәм EPUB файлдарын Kindle йәки Kobo eReader-ға ебәрә алаһығыҙ."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Тәҡдим ителгән ҡоралдар: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon-дың “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz-тың “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Авторҙарҙы һәм китапханаларҙы яҡлағыҙ"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Әгәр был һеҙгә оҡшаһа һәм мөмкинлегегеҙ булһа, оригиналды һатып алыуҙы йәки авторҙарҙы туранан-тура яҡлауҙы уйлағыҙ."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Әгәр был китап һеҙҙең урындағы китапханала бар икән, уны шунда бушлай алып тороу мөмкинлеген ҡарағыҙ."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Файл сифаты"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Был файлдың сифатын хәбәр итеп, берләшмәгә ярҙам итегеҙ! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Файл проблемаһын хәбәр итеү (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Бик яҡшы файл сифаты (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Комментарий өҫтәргә (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Был файлда нимә дөрөҫ түгел?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Зинһар, <a %(a_copyright)s>DMCA / Авторлыҡ хоҡуғы дәғүә формаһын</a> ҡулланығыҙ."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Мәсьәләне тасуирлау (мәжбүри)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Мәсьәлә тасуирламаһы"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "Был файлдың яҡшыраҡ версияһының MD5 (әгәр бар икән)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Әгәр был файлға яҡын булған башҡа файл бар икән (шул уҡ баҫма, шул уҡ файл киңәйтеүе, әгәр таба алһағыҙ), кешеләр был файл урынына уны ҡулланырға тейеш. Әгәр Anna’s Archive-тан тыш был файлдың яҡшыраҡ версияһын белһәгеҙ, зинһар, <a %(a_upload)s>йөкләгеҙ</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "URL-дан md5 ала алаһығыҙ, мәҫәлән"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Хәбәрҙе ебәреү"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Файлдың метадатаһын үҙегеҙ нисек яҡшыртырға өйрәнегеҙ <a %(a_metadata)s>бында</a>."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Хәбәрегеҙҙе ебәргәнегеҙ өсөн рәхмәт. Ул был биттә күрһәтеләсәк, шулай уҡ Анна тарафынан ҡулдан тикшереләсәк (беҙҙә тейешле модерация системаһы булмағанға тиклем)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Нимәлер дөрөҫ булманы. Битте яңыртып, ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Әгәр был файл юғары сифатлы булһа, бында уның тураһында теләһә нимә тураһында фекер алыша алаһығыҙ! Әгәр юҡ икән, “Файл проблемаһын хәбәр итеү” төймәһен ҡулланығыҙ."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Миңә был китап оҡшаны!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Фекер ҡалдырыу"

#, fuzzy
msgid "common.english_only"
msgstr "Түбәндәге текст инглиз телендә дауам итә."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Дөйөм күсереп алыуҙар: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“Файл MD5” - файлдың эсендәге мәғлүмәттәрҙән иҫәпләнгән хэш, һәм ул шул мәғлүмәттәргә нигеҙләнеп уникаль булып тора. Беҙ бында индексациялаған бөтә күләгә китапханалары башлыса файлдарҙы идентификациялау өсөн MD5 ҡуллана."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Файл бер нисә күләгә китапханаларында күренергә мөмкин. Беҙ йыйған төрлө datasets тураһында мәғлүмәт өсөн <a %(a_datasets)s>Datasets битен</a> ҡарағыҙ."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Был файл <a %(a_ia)s>IA-ның Контролдә тотолған Цифрлы Алыштырыу</a> китапханаһы тарафынан идара ителә, һәм Анна архивы тарафынан эҙләү өсөн индексацияланған. Беҙ йыйған төрлө datasets тураһында мәғлүмәт өсөн <a %(a_datasets)s>Datasets битен</a> ҡарағыҙ."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Был файл тураһында мәғлүмәт өсөн уның <a %(a_href)s>JSON файлын</a> ҡарағыҙ."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Был битте тейәүҙә проблема"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Зинһар, яңыртығыҙ һәм ҡабатлап ҡарағыҙ. Мәсъәлә бер нисә сәғәт дауам итһә, <a %(a_contact)s>беҙҙең менән бәйләнешкә сығығыҙ</a>."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Табылманы"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” беҙҙең базаһында табылманы."

#, fuzzy
msgid "page.login.title"
msgstr "Керегеҙ / Теркәлегеҙ"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Браузерҙы тикшереү"

#, fuzzy
msgid "page.login.text1"
msgstr "Спам-боттарҙың күп иҫәп яҙмалары булдырыуынан һаҡланыу өсөн, беҙ тәүҙә һеҙҙең браузерҙы тикшерергә тейешбеҙ."

#, fuzzy
msgid "page.login.text2"
msgstr "Әгәр ҙә һеҙ сикһеҙ циклға эләкһәгеҙ, <a %(a_privacypass)s>Privacy Pass</a> ҡуйырға тәҡдим итәбеҙ."

#, fuzzy
msgid "page.login.text3"
msgstr "Реклама блокировкаларын һәм башҡа браузер киңәйтеүҙәрен һүндереү ҙә ярҙам итә ала."

#, fuzzy
msgid "page.codes.title"
msgstr "Кодтар"

#, fuzzy
msgid "page.codes.heading"
msgstr "Кодтарҙы өйрәнеү"

#, fuzzy
msgid "page.codes.intro"
msgstr "Префикс буйынса яҙмаларға бирелгән кодтарҙы өйрәнегеҙ. “Яҙмалар” бағанаһы эҙләү системаһында (метадата ғына булған яҙмаларҙы ла индереп) бирелгән префикс менән кодтар бирелгән яҙмалар һанын күрһәтә. “Кодтар” бағанаһы бирелгән префикс менән нисә реаль код барлығын күрһәтә."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Был битте генерациялау ваҡыт ала, шуға күрә Cloudflare captcha талап ителә. <a %(a_donate)s>Ағзалар</a> captcha-ны урап үтә ала."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Зинһар, был биттәрҙе скрапингламағыҙ. Уның урынына беҙ <a %(a_import)s>генерациялауҙы</a> йәки <a %(a_download)s>йөкләп алыуҙы</a> һәм беҙҙең <a %(a_software)s>асыҡ сығанаҡ кодын</a> ҡулланыуҙы тәҡдим итәбеҙ. Сығанаҡ мәғлүмәтте JSON файлдары аша ҡулдан өйрәнергә мөмкин, мәҫәлән, <a %(a_json_file)s>был файл</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Префикс"

#, fuzzy
msgid "common.form.go"
msgstr "Барырға"

#, fuzzy
msgid "common.form.reset"
msgstr "Ҡабатлау"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Аннаның Архивын эҙләү"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Иҫкәртеү: кодта дөрөҫ булмаған Unicode символдары бар, һәм ул төрлө хәлдәрҙә дөрөҫ эшләмәҫкә мөмкин. Сығанаҡ бинарҙы URL-дағы base64 репрезентацияһынан декодлау мөмкин."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Билдәле код префиксы “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Префикс"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Ярлыҡ"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Тасуирлама"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL өсөн махсус код"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%” кодтың ҡиммәте менән алмаштырыласаҡ"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Дөйөм URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Веб-сайт"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s “%(prefix_label)s” менән тура килгән яҙма"
msgstr[1] "%(count)s “%(prefix_label)s” менән тура килгән яҙмалар"

#, fuzzy
msgid "page.codes.url_link"
msgstr "Махсус код өсөн URL: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Тағы ла…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "“%(prefix_label)s” менән башланған кодтар"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Индекс"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "яҙмалар"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "кодтар"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "%(count)s яҙманан кәмерәк"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "DMCA / авторлыҡ хоҡуҡтары дәғүәләре өсөн <a %(a_copyright)s>был форманы</a> ҡулланығыҙ."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Авторлыҡ хоҡуҡтары буйынса башҡа бәйләнеш ысулдары автоматик рәүештә юйыласаҡ."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Һеҙҙең фекерҙәрегеҙҙе һәм һорауҙарығыҙҙы бик шатланып ҡабул итәбеҙ!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Шулай ҙа, беҙгә килгән спам һәм мәғәнәһеҙ хаттарҙың күплеге арҡаһында, зинһар, беҙгә бәйләнешкә сығыу шарттарын аңлауығыҙҙы раҫлау өсөн ҡумталарҙы тикшерегеҙ."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Авторлыҡ хоҡуҡтары дәғүәләре был электрон почтаға иғтибар ителмәйәсәк; уның урынына форманы ҡулланығыҙ."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Партнёрские серверы недоступны из-за закрытия хостинга. Они должны снова заработать в ближайшее время."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Членства будут продлены соответственно."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Беҙгә <a %(a_request)s>китаптар һорап</a><br>йәки бәләкәй (<10к) <a %(a_upload)s>йөкләүҙәр</a> өсөн хат яҙмағыҙ."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Иҫәп яҙмаһы йәки иғәнә һорауҙары менән мөрәжәғәт иткәндә, иҫәп яҙмаһы ID-һын, скриншоттарҙы, квитанцияларҙы, мөмкин тиклем күберәк мәғлүмәтте өҫтәгеҙ. Беҙ электрон почтаны 1-2 аҙнаға бер тикшерәбеҙ, шуға күрә был мәғлүмәтте индермәү ниндәй ҙә булһа хәл итеүҙе тотҡарлаясаҡ."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Электрон почтаны күрһәтеү"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Авторлыҡ хоҡуғы дәғүәһе формаһы"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Әгәр һеҙҙең DMCA йәки башҡа авторлыҡ хоҡуғы дәғүәһе бар икән, зинһар, был форманы мөмкин тиклем теүәл тултырығыҙ. Әгәр ниндәй ҙә булһа мәсьәләләргә осраһағыҙ, беҙҙең махсус DMCA адресына мөрәжәғәт итегеҙ: %(email)s. Иғтибар итегеҙ, был адресҡа ебәрелгән дәғүәләр эшкәртелмәйәсәк, ул тик һорауҙар өсөн генә. Дәғүәләрегеҙҙе ебәреү өсөн түбәндәге форманы ҡулланығыҙ."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "Anna’s Archive сайтындағы URL-дар (мәжбүри). Бер юлға бер URL. Зинһар, тик бер үк баҫманың URL-дарын ғына индерегеҙ. Әгәр бер нисә китап йәки бер нисә баҫма өсөн дәғүә яһарға теләһәгеҙ, был форманы бер нисә тапҡыр ебәрегеҙ."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Бер нисә китап йәки баҫманы бергә туплаған дәғүәләр кире ҡағыласаҡ."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Һеҙҙең исемегеҙ (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Адрес (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Телефон номеры (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Электрон почта (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Сығанаҡ материалдың аныҡ тасуирламаһы (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "Сығанаҡ материалдың ISBN-дары (әгәр бар икән). Бер юлға бер ISBN. Зинһар, тик дәғүә яһалған баҫмаға тап килгән ISBN-дарҙы ғына индерегеҙ."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "Сығанаҡ материалдың <a %(a_openlib)s>Open Library</a> URL-дары, бер юлға бер URL. Зинһар, сығанаҡ материалды Open Library-ҙа эҙләү өсөн бер аҙ ваҡыт бүлегеҙ. Был беҙҙең дәғүәгеҙҙе тикшереүгә ярҙам итәсәк."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "Сығанаҡ материалға URL-дар, бер юлға бер URL (мәжбүри). Зинһар, дәғүәгеҙҙе тикшереүгә ярҙам итеү өсөн мөмкин тиклем күберәк URL индерегеҙ (мәҫәлән, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Ғариза һәм ҡултамға (мәжбүри)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Дәғүәне ебәреү"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Авторлыҡ хоҡуғы дәғүәгеҙҙе ебәргәнегеҙ өсөн рәхмәт. Беҙ уны мөмкин тиклем тиҙерәк тикшерәсәкбеҙ. Икенсе дәғүә ебәреү өсөн битте яңыртығыҙ."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Нимәлер дөрөҫ булманы. Зинһар, битте яңыртып ҡабатлап ҡарағыҙ."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Әгәр ҙә һеҙ был мәғлүмәттәр йыйылмаһын <a %(a_archival)s>архивлау</a> йәки <a %(a_llm)s>LLM уҡытыу</a> маҡсатында күсерергә теләһәгеҙ, беҙгә мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Беҙҙең миссия - донъялағы бөтә китаптарҙы (шулай уҡ мәҡәләләр, журналдар һ.б.) архивлау һәм уларҙы киң ҡулланыуға мөмкин итеү. Беҙ бөтә китаптарҙың киң таралыуын һәм уларҙың резерв күсермәләрен булдырыуҙы яҡлайбыҙ. Шуға күрә беҙ төрлө сығанаҡтарҙан файлдарҙы бергә туплайбыҙ. Ҡайһы бер сығанаҡтар тулыһынса асыҡ һәм күмәртәләп күсереп алырға мөмкин (мәҫәлән, Sci-Hub). Башҡалары ябыҡ һәм һаҡсыл, шуға күрә уларҙың китаптарын “азат итеү” өсөн уларҙы ҡырып алабыҙ. Тағы ла ҡайһы берҙәре араһында уртала урынлашҡан."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Бөтә мәғлүмәттәребеҙҙе <a %(a_torrents)s>торрент</a> аша күсереп алырға мөмкин, һәм бөтә метадатабыҙҙы <a %(a_anna_software)s>генерацияларға</a> йәки <a %(a_elasticsearch)s>ElasticSearch һәм MariaDB базалары</a> итеп күсереп алырға мөмкин. Сығанаҡ мәғлүмәттәрҙе ҡулдан <a %(a_dbrecord)s>был</a> кеүек JSON файлдары аша өйрәнергә мөмкин."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Күҙәтеү"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Түбәндә Анна архивындағы файлдар сығанаҡтарының ҡыҫҡа күҙәтеүе."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Сығанаҡ"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Үлсәм"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% AA тарафынан күсерелгән / торренттар бар"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Файлдар һанының проценттары"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Һуңғы яңыртыу"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Документаль һәм әҙәби әҫәрҙәр"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s файл"
msgstr[1] "%(count)s файлдар"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Libgen.li “scimag” аша"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: 2021 йылдан туңдырылған; күбеһе торренттар аша бар"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: шунан бирле бәләкәй өҫтәмәләр</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "“scimag”-ды индермәйенсә"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Фантастика торренты артта (хотя ID ~4-6M не торрентились, так как они пересекаются с нашими Zlib торрентами)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Коллекция «Китайский» в Z-Library, похоже, такая же, как наша коллекция DuXiu, но с разными MD5. Мы исключаем эти файлы из торрентов, чтобы избежать дублирования, но все равно показываем их в нашем поисковом индексе."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Контролируемая Цифровая Выдача"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ файлов доступны для поиска."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Всего"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Исключая дубликаты"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Поскольку теневые библиотеки часто синхронизируют данные друг с другом, существует значительное перекрытие между библиотеками. Поэтому цифры не складываются в общую сумму."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Процент «зеркалированных и раздаваемых Anna’s Archive» показывает, сколько файлов мы зеркалим сами. Мы раздаем эти файлы оптом через торренты и делаем их доступными для прямого скачивания через партнерские сайты."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Библиотеки-источники"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Кейбер сығанаҡ китапханалар үҙҙәренең мәғлүмәттәрен торренттар аша күмәкләп уртаҡлашыуҙы хуплай, ә икенселәре үҙ коллекцияларын еңел генә уртаҡлашмай. Һуңғы осраҡта, Аннаның Архивы уларҙың коллекцияларын скрапинг ярҙамында йыйып, уларҙы ҡулланыуға тәҡдим итә (ҡарағыҙ беҙҙең <a %(a_torrents)s>Торренттар</a> битен). Шулай уҡ аралаш осраҡтар ҙа бар, мәҫәлән, сығанаҡ китапханалар уртаҡлашырға әҙер, ләкин уларҙың ресурстары етмәй. Был осраҡтарҙа ла беҙ ярҙам итергә тырышабыҙ."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Түбәндә беҙ төрлө сығанаҡ китапханалар менән нисек эш итеүебеҙ тураһында дөйөм күҙәтеү бирелгән."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Сығанаҡ"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Файлдар"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Көндәлек <a %(dbdumps)s>HTTP мәғлүмәт базаһы күсермәләре</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s <a %(nonfiction)s>Документаль әҙәбиәт</a> һәм <a %(fiction)s>Әҙәбиәт</a> өсөн автоматлаштырылған торренттар"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Аннаның Архивы <a %(covers)s>китап тыштары торренттары</a> коллекцияһын идара итә"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub 2021 йылдан яңы файлдарҙы туңдырҙы."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Метадата дамптары <a %(scihub1)s>бында</a> һәм <a %(scihub2)s>бында</a> бар, шулай уҡ <a %(libgenli)s>Libgen.li базаһы</a>ның бер өлөшө булараҡ (беҙ уны ҡулланабыҙ)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Мәғлүмәт торрендары <a %(scihub1)s>бында</a>, <a %(scihub2)s>бында</a> һәм <a %(libgenli)s>бында</a> ҡарарға мөмкин"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Libgen-дың “scimag” бүлегенә ҡайһы бер яңы файлдар <a %(libgenrs)s>өҫтәлә</a>, ләкин яңы торрендар булдырырлыҡ түгел"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Өс ай һайын <a %(dbdumps)s>HTTP базаһы күсермәләре</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Нон-фикшн торрендары Libgen.rs менән уртаҡлашыла (һәм <a %(libgenli)s>бында</a> күсермәләнә)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Аннаның Архивы һәм Libgen.li берлектә <a %(comics)s>комикстар</a>, <a %(magazines)s>журналдар</a>, <a %(standarts)s>стандарт документтар</a> һәм <a %(fiction)s>әҙәбиәт (Libgen.rs-тан айырылған)</a> коллекцияларын идара итәләр."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Уларҙың “fiction_rus” коллекцияһы (рус әҙәбиәте) айырым торренттар менән тәьмин ителмәгән, әммә башҡа торренттар менән ҡапланған, һәм беҙ <a %(fiction_rus)s>күҙгә күренгән</a> һаҡлайбыҙ."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Аннаның Архивы һәм Z-Library Z-Library метадатаһы һәм Z-Library файлдары коллекцияһын берлектә идара итә"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Ҡайһы бер метадата <a %(openlib)s>Open Library базаһы күсермәләре</a> аша ҡарарға мөмкин, ләкин улар бөтә IA коллекцияһын үҙ эсенә алмай"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Уларҙың бөтә коллекцияһы өсөн еңел ҡарарға мөмкин метадата күсермәләре юҡ"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Аннаның Архивы IA метадатаһы коллекцияһын идара итә"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Файлдарҙы сикләнгән шарттарҙа ғына ҡуртымға алырға мөмкин, төрлө инеү сикләүҙәре менән"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Аннаның Архивы IA файлдары коллекцияһын идара итә"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Ҡытай интернетында төрлө метадата базалары таралған; ләкин улар йыш ҡына түләүле"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Уларҙың бөтә коллекцияһы өсөн еңел ҡарарға мөмкин метадата күсермәләре юҡ."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Аннаның Архивы <a %(duxiu)s>DuXiu метадатаһын</a> идара итә"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Ҡытай интернетында таралған төрлө файл базалары; йыш ҡына түләүле базалар"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Күпселек файлдарҙы тик премиум BaiduYun аккаунттары менән генә асырға мөмкин; тиҙлек менән йөкләү."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Аннаның Архивы <a %(duxiu)s>DuXiu файлдарын</a> идара итә"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Төрлө бәләкәй йәки бер тапҡыр ҡулланыла торған сығанаҡтар. Беҙ кешеләрҙе башта башҡа күләгә китапханаларына йөкләргә дәртләндерәбеҙ, ләкин ҡайһы берҙә кешеләрҙең башҡаларға айырып ҡарарға ҙур булмаған, әммә үҙ категорияһына лайыҡ булмаған коллекциялары бар."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Тик метадата сығанаҡтары"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Беҙ шулай уҡ коллекциябыҙҙы тик метадата сығанаҡтары менән байытабыҙ, уларҙы файлдарға тура килтерә алабыҙ, мәҫәлән, ISBN номерҙары йәки башҡа баҫыуҙар ярҙамында. Түбәндә уларҙың дөйөм күҙәтеүе бирелгән. Тағы ла, ҡайһы бер сығанаҡтар тулыһынса асыҡ, ә икенселәре өсөн беҙ уларҙы скрапинг ярҙамында алабыҙ."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Метадата йыйыу өсөн илһамыбыҙ Аарон Шварцтың “һәр баҫылған китап өсөн бер веб-бит” маҡсаты булды, ул <a %(a_openlib)s>Open Library</a> проектын булдырҙы. Был проект уңышлы эшләне, ләкин беҙҙең уникаль позициябыҙ улар ала алмаған метадата алырға мөмкинлек бирә. Тағы бер илһамыбыҙ донъяла <a %(a_blog)s>күпме китап барлығын</a> белеү теләге булды, шуның өсөн беҙ һаҡларға тейешле китаптарҙың һанын иҫәпләй алабыҙ."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Метадата эҙләүҙә, беҙ оригиналь яҙмаларҙы күрһәтеүебеҙҙе иҫегеҙҙә тотоғоҙ. Беҙ яҙмаларҙы берләштермәйбеҙ."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Һуңғы яңыртыу"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Ай һайын <a %(dbdumps)s>база күсермәләре</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Тура күмәртәләп булмай, скрапингтан һаҡланған"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Аннаның Архивы <a %(worldcat)s>OCLC (WorldCat) метадатаһын</a> идара итә"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Берләштерелгән база"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Беҙ бөтә юғарыла күрһәтелгән сығанаҡтарҙы берләштерелгән базаға берләштерәбеҙ, ул был веб-сайтты хеҙмәтләндереү өсөн ҡулланыла. Был берләштерелгән база туранан-тура ҡулланыуға асыҡ түгел, ләкин Аннаның Архивы тулыһынса асыҡ сығанаҡлы булғанлыҡтан, уны <a %(a_generated)s>генерациялау</a> йәки <a %(a_downloaded)s>ElasticSearch һәм MariaDB базалары</a> итеп йөкләп алыу еңел. Был биттәге скрипттар автоматик рәүештә юғарыла күрһәтелгән сығанаҡтарҙан бөтә кәрәкле метадатаны йөкләп аласаҡ."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Әгәр һеҙ был скрипттарҙы локаль рәүештә эшләтеп ҡарағансы, беҙҙең мәғлүмәттәрҙе өйрәнергә теләһәгеҙ, беҙҙең JSON файлдарына ҡарай алаһығыҙ, улар артабан башҡа JSON файлдарына һылтанма бирә. <a %(a_json)s>Был файл</a> яҡшы башланғыс нөктәһе булып тора."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Беҙҙең <a %(a_href)s>блог яҙмаһынан</a> яраҡлаштырылған."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> — <a %(superstar_link)s>SuperStar Digital Library Group</a> тарафынан булдырылған сканланған китаптарҙың ҙур базаһы. Күпселеге академик китаптар, улар университеттар һәм китапханалар өсөн цифрлы форматта ҡарарға мөмкин булһын өсөн сканланған. Беҙҙең инглиз телле аудитория өсөн <a %(princeton_link)s>Принстон</a> һәм <a %(uw_link)s>Вашингтон университеты</a> яҡшы күҙәтеүҙәр тәҡдим итә. Шулай уҡ өҫтәмә мәғлүмәт биргән бик яҡшы мәҡәлә бар: <a %(article_link)s>“Ҡытай китаптарын цифрлаштырыу: SuperStar DuXiu Scholar эҙләү системаһы буйынса тикшеренеү”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "DuXiu китаптары күптән инде Ҡытай интернетында пиратланған. Ғәҙәттә уларҙы һатыусылар бер долларҙан кәмерәк хаҡҡа һата. Улар ғәҙәттә Google Drive-тың ҡытайса эквиваленты ярҙамында таратыла, ул йыш ҡына күберәк һаҡлау урыны булдырыу өсөн хакерланған. Ҡайһы бер техник деталдәрҙе <a %(link1)s>бында</a> һәм <a %(link2)s>бында</a> табырға мөмкин."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Китаптар ярым-йәмәғәтселеккә таратылған булһа ла, уларҙы күпләп алыу бик ауыр. Беҙҙең эштәр исемлегендә был юғары урында ине, һәм беҙ быға бер нисә ай тулы ваҡытлы эш бүлдек. Әммә 2023 йылдың аҙағында ғәжәйеп, иҫ киткес һәм һәләтле ирекмән беҙгә мөрәжәғәт итте, улар был эште инде башҡарғандарын әйтте — ҙур сығымдар менән. Улар беҙгә тулы коллекцияны бер ниндәй ҙә кире ҡайтарыу көтмәйенсә бүлештеләр, оҙайлы һаҡлау гарантияһы ғына һорап. Ысынлап та иҫ киткес."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Ресурстар"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Файлдарҙың дөйөм һаны: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Файлдарҙың дөйөм күләме: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Аннаның Архивы тарафынан күсерелгән файлдар: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Һуңғы яңыртыу: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Аннаның Архивы торренты"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Аннаның Архивында миҫал яҙмаһы"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Был мәғлүмәт тураһында беҙҙең блог яҙмаһы"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Метаданные импортлау өсөн скрипттар"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Аннаның Архив Контейнерҙары форматы"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Ирекмәндәребеҙҙән күберәк мәғлүмәт (сығанаҡ яҙмалар):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Контролируемое Цифровое Кредитование"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Был мәғлүмәттәр йыйылмаһы <a %(a_datasets_openlib)s>Open Library мәғлүмәттәр йыйылмаһы</a> менән тығыҙ бәйләнгән. Ул IA-ның Контролдә тотолған Цифрлы Китапханаһынан бөтә метаданные һәм файлдарҙың ҙур өлөшөн үҙ эсенә ала. Яңыртыуҙар <a %(a_aac)s>Anna’s Archive Контейнерҙар форматы</a>нда сығарыла."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Был яҙмалар Open Library мәғлүмәттәр йыйылмаһынан тура алынған, әммә Open Library-ҙа булмаған яҙмаларҙы ла үҙ эсенә ала. Шулай уҡ бер нисә йыл дауамында берләшмә ағзалары тарафынан йыйылған мәғлүмәт файлдары бар."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Коллекция ике өлөштән тора. Бөтә мәғлүмәтте алыу өсөн ике өлөштө лә кәрәк (торренттар битендә һыҙып ташланған иҫке торренттарҙан башҡа)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "беренсе сығарылышыбыҙ, <a %(a_aac)s>Аннаның Архив Контейнерҙары (AAC) форматына</a> стандартлаштырғанға тиклем. Метаданные (json һәм xml форматында), pdf-тар (acsm һәм lcpdf цифрлы ҡуртым системаларынан), һәм тышлыҡ миниатюраларын үҙ эсенә ала."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "AAC ҡулланып, яңы сығарылыштар. 2023-01-01-ҙән һуңғы ваҡыт тамғалары менән метаданные ғына үҙ эсенә ала, сөнки ҡалғандары инде “ia” менән ҡапланған. Шулай уҡ бөтә pdf файлдары, был юлы acsm һәм “bookreader” (IA-ның веб-уҡыусыһы) ҡуртым системаларынан. Исеме тап килмәһә лә, беҙ bookreader файлдарын ia2_acsmpdf_files коллекцияһына индерәбеҙ, сөнки улар бер-береһен ҡабатламай."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Основной сайт %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Цифрлы Ҡуртым Китапханаһы"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Метаданные документацияһы (күпселек ялан)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Информация о стране по ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Международное агентство ISBN регулярно выпускает диапазоны, которые оно выделило национальным агентствам ISBN. Из этого мы можем определить, к какой стране, региону или языковой группе принадлежит этот ISBN. В настоящее время мы используем эти данные косвенно, через библиотеку Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Ресурсы"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Последнее обновление: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Веб-сайт ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Метаданные"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Для предыстории различных форков Library Genesis см. страницу <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li күпселек шул уҡ контентты һәм метадатарҙы Libgen.rs кеүек үк үҙ эсенә ала, әммә өҫтәмә рәүештә комикстар, журналдар һәм стандарт документтар коллекцияларын да индерә. Ул шулай уҡ <a %(a_scihub)s>Sci-Hub</a>-ты метадатарға һәм эҙләү системаһына интеграциялаған, һәм беҙ уны үҙебеҙҙең база өсөн ҡулланабыҙ."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Был китапхананың метадатарын <a %(a_libgen_li)s>libgen.li сайтында</a> бушлай алырға мөмкин. Әммә был сервер әкрен эшләй һәм өҙөлгән тоташыуҙарҙы яңынан тоташтырыуҙы хупламай. Шул уҡ файлдар <a %(a_ftp)s>FTP серверында</a> ла бар, һәм ул яҡшыраҡ эшләй."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Өҫтәмә контенттың күпселеге өсөн торренттар бар, айырыуса комикстар, журналдар һәм стандарт документтар өсөн торренттар Аннаның Архивы менән берлектә сығарылған."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Әҙәбиәт коллекцияһының үҙ торренттары бар (Libgen.rs-тан айырылған) %(start)s башлана."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Libgen.li администраторы әйтеүенсә, “fiction_rus” (рус әҙәбиәте) коллекцияһы <a %(a_booktracker)s>booktracker.org</a> сайтынан даими сығарылған торренттар менән ҡапланырға тейеш, айырыуса <a %(a_flibusta)s>flibusta</a> һәм <a %(a_librusec)s>lib.rus.ec</a> торренттары (беҙ уларҙы <a %(a_torrents)s>бында</a> һаҡлайбыҙ, әммә ниндәй торренттарҙың ниндәй файлдарға тап килеүен әлегә асыҡламағанбыҙ)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Бөтә коллекцияларҙың статистикаһын <a %(a_href)s>libgen сайты</a>нда табырға мөмкин."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Документаль әҙәбиәт тә айырылып сыҡҡан кеүек, әммә яңы торренттарһыҙ. Был 2022 йылдың башынан бирле булған кеүек күренә, ләкин беҙ быны раҫламағанбыҙ."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Торренттар булмаған ҡайһы бер диапазондар (мәҫәлән, f_3463000-ҙән f_4260000-гә тиклем әҙәбиәт диапазондары) Z-Library (йәки башҡа дубликат) файлдары булыуы ихтимал, әммә беҙ ҡайһы бер дубликаттарҙы бөтөрөп, был диапазондарҙа lgli-уникаль файлдар өсөн торренттар яһарға теләйбеҙ."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "“libgen.is” тигән торрент файлдарының <a %(a_libgen)s>Libgen.rs</a> көҙгөһө булыуын иҫегеҙҙә тотоғоҙ (“.is” - Libgen.rs ҡулланған башҡа домен)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Метадатарҙы ҡулланыу өсөн файҙалы ресурс - <a %(a_href)s>был бит</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Anna’s Archive сайтында фантастика торренттары"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Anna’s Archive сайтында комикстар торренттары"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Anna’s Archive сайтында журналдар торренттары"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Аннаның Архивында стандарт документ торренттары"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Аннаның Архивында рус әҙәбиәте торренттары"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Метадата"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "FTP аша метадата"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Метадата ялан мәғлүмәттәре"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Башҡа торренттарҙың көҙгөһө (һәм уникаль фантастика һәм комикстар торренттары)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Фекер алышыу форумы"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Комикстар сығарылышы тураһында блог яҙмаһы"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Төрлө Library Genesis (йәки “Libgen”) тармаҡтарының ҡыҫҡа тарихы шунда: ваҡыт үтеү менән, Library Genesis менән шөғөлләнгән төрлө кешеләр араһында аңлашылмаусылыҡтар килеп сыҡты, һәм улар айырым юлдар менән китте."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "“fun” версияһы тәүге нигеҙләүсе тарафынан булдырылды. Ул яңы, күберәк таратылған версия файҙаһына яңыртыла."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "“rs” версияһы бик оҡшаш мәғлүмәттәргә эйә, һәм улар коллекцияларын күберәк торренттарҙа сығара. Ул яҡынса “фантастика” һәм “фәнни-ғәмәли” бүлектәренә бүленә."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Башта “http://gen.lib.rus.ec” сайтында булған."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>“li” версияһы</a> комикстарҙың ҙур коллекцияһына эйә, шулай уҡ башҡа контенттар ҙа бар, улар (әле) торренттар аша күмәртәләп йөкләп алыу өсөн мөмкин түгел. Ул айырым фантастика китаптары торрент коллекцияһына эйә, һәм уның базаһында <a %(a_scihub)s>Sci-Hub</a> метадатаһы бар."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Был <a %(a_mhut)s>форумдағы яҙмаға</a> ярашлы, Libgen.li башта “http://free-books.dontexist.com” сайтында урынлашҡан булған."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> ҡайһы бер мәғәнәлә Library Genesis тармағы булып тора, әммә улар үҙ проекттары өсөн башҡа исем ҡулланды."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Был биттә “.rs” версияһы тураһында һүҙ бара. Ул үҙенең метадатаһын һәм китап каталогының тулы йөкмәткеһен даими баҫтырып сығарыуы менән билдәле. Китап коллекцияһы нәфис әҙәбиәт һәм нәфис булмаған әҙәбиәт өлөштәренә бүленгән."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Метадата ҡулланыуҙа ярҙамсы ресурс булып <a %(a_metadata)s>был бит</a> тора (IP диапазондарын блоклай, VPN кәрәк булыуы мөмкин)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "2024-03 йылдан башлап, яңы торренттар <a %(a_href)s>был форум ебендә</a> баҫтырылып сығарыла (IP диапазондарын блоклай, VPN кәрәк булыуы мөмкин)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Аннаның Архивында нәфис булмаған әҙәбиәт торренттары"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Аннаның Архивында нәфис әҙәбиәт торренттары"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Метадатаһы"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs метадатаһы буйынса мәғлүмәт"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs нәфис булмаған әҙәбиәт торренттары"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs нәфис әҙәбиәт торренттары"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Фекер алышыу форумы"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Аннаның Архивы торренттары (китап тышлыҡтары)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Китап тышлыҡтары сығарылышы тураһында блогыбыҙ"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis үҙенең мәғлүмәттәрен торренттар аша күпләп биреүе менән билдәле. Беҙҙең Libgen коллекцияһы уларҙың туранан-тура сығармаған ярҙамсы мәғлүмәттәренән тора, улар менән хеҙмәттәшлек итәбеҙ. Library Genesis менән эшләгән һәр кемгә ҙур рәхмәт!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Сығарылыш 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Был <a %(blog_post)s>беренсе сығарылыш</a> бик бәләкәй: Libgen.rs форкынан яҡынса 300ГБ китап тышлыҡтары, нәфис һәм нәфис булмаған әҙәбиәт. Улар libgen.rs сайтында күренгәнсә ойошторолған, мәҫәлән:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "Нәфис булмаған китап өсөн %(example)s."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "Нәфис китап өсөн %(example)s."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Z-Library коллекцияһы кеүек үк, беҙ уларҙы ҙур .tar файлында йыйҙыҡ, уны <a %(a_ratarmount)s>ratarmount</a> ҡулланып туранан-тура хеҙмәтләндереү өсөн монтировать итергә мөмкин."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> — это проприетарная база данных некоммерческой организации <a %(a_oclc)s>OCLC</a>, которая агрегирует записи метаданных из библиотек по всему миру. Вероятно, это крупнейшая коллекция библиотечных метаданных в мире."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "2023 йылдың октябре, башланғыс сығарылыш:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "В октябре 2023 года мы <a %(a_scrape)s>выпустили</a> комплексный скрапинг базы данных OCLC (WorldCat) в формате <a %(a_aac)s>контейнеров Аннин Архив</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Торренты от Аннин Архив"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Наш блог-пост об этих данных"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library — Интернет Архивының һәр китапты каталоглаштырыу өсөн асыҡ сығанаҡ проекты. Ул донъялағы иң ҙур китап сканлау операцияларының береһе булып тора һәм күп китаптарҙы цифрлы ҡуртымға бирә. Уның китап метадатаһы каталогы ирекле рәүештә күсереп алыу өсөн бар, һәм Аннаның Архивында индерелгән (хәҙерге ваҡытта эҙләүҙә юҡ, тик Open Library ID буйынса эҙләгәндә генә)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Метаданные"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Выпуск 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Это дамп множества запросов к isbndb.com в сентябре 2022 года. Мы пытались охватить все диапазоны ISBN. Это около 30,9 миллионов записей. На их сайте они утверждают, что у них на самом деле есть 32,6 миллиона записей, так что мы могли как-то пропустить некоторые, или <em>они</em> могли что-то сделать неправильно."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Ответы JSON практически сырые с их сервера. Одна из проблем с качеством данных, которую мы заметили, заключается в том, что для номеров ISBN-13, которые начинаются с другого префикса, чем «978-», они все равно включают поле «isbn», которое просто является номером ISBN-13 с обрезанными первыми 3 цифрами (и пересчитанной контрольной цифрой). Это явно неправильно, но так они, похоже, делают, поэтому мы не изменяли это."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Еще одна потенциальная проблема, с которой вы можете столкнуться, заключается в том, что поле «isbn13» имеет дубликаты, поэтому вы не можете использовать его в качестве первичного ключа в базе данных. Поля «isbn13»+«isbn» в сочетании, похоже, уникальны."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Для получения информации о Sci-Hub, пожалуйста, обратитесь к его <a %(a_scihub)s>официальному сайту</a>, <a %(a_wikipedia)s>странице в Википедии</a> и этому <a %(a_radiolab)s>подкаст-интервью</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Обратите внимание, что Sci-Hub был <a %(a_reddit)s>заморожен с 2021 года</a>. Он был заморожен и ранее, но в 2021 году было добавлено несколько миллионов статей. Тем не менее, некоторое ограниченное количество статей добавляется в коллекции Libgen “scimag”, хотя этого недостаточно для создания новых массовых торрентов."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Мы используем метаданные Sci-Hub, предоставленные <a %(a_libgen_li)s>Libgen.li</a> в его коллекции “scimag”. Мы также используем набор данных <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Обратите внимание, что торренты “smarch” <a %(a_smarch)s>устарели</a> и поэтому не включены в наш список торрентов."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Торренты на Аннин Архив"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Метаданные и торренты"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Торренты на Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Торренты на Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Обновления на Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Страница в Википедии"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Подкаст-интервью"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Аннаның Архивына йөкләүҙәр"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Йөкмәтке <a %(a1)s>Datasets битенән</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Төрлө бәләкәй йәки бер тапҡыр ғына булған сығанаҡтар. Беҙ кешеләрҙе башта башҡа күләгә китапханаларына йөкләргә дәртләндерәбеҙ, ләкин ҡайһы саҡта кешеләрҙең башҡаларға айырып ҡарарға бик ҙур, ләкин үҙ категорияһын булдырырға етерлек ҙур булмаған коллекциялары була."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "«йөкләү» коллекцияһы бәләкәйерәк субколлекцияларҙа бүленгән, улар AACID-тарҙа һәм торрент исемдәрендә күрһәтелгән. Бөтә субколлекциялар тәүҙә төп коллекцияға ҡаршы дубликаттарҙан таҙартылды, әммә «йөкләү_яҙмалары» метадатаһы JSON файлдарында һаман да оригиналь файлдарға күп һылтанмалар бар. Китап булмаған файлдар күпселек субколлекцияларҙан алынды, һәм ғәҙәттә «йөкләү_яҙмалары» JSON-да билдәләнмәй."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Күп субколлекциялар үҙҙәре суб-суб-коллекцияларҙан тора (мәҫәлән, төрлө оригиналь сығанаҡтарҙан), улар “файл юлы” яланында директориялар булараҡ күрһәтелә."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Субколлекциялар:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Эске коллекция"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Иҫкәрмәләр"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "ҡарарға"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "эҙләү"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "<a %(a_href)s>aaaaarg.fail</a> сайтынан. Тулыға яҡын күренә. Беҙҙең ирекмән «cgiym» тарафынан."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "<a %(a_href)s><q>ACM Digital Library 2020</q></a> торрентынан. Барлыҡҡа килгән мәҡәләләр коллекциялары менән юғары оҡшашлыҡ бар, әммә бик аҙ MD5 тап килеүҙәр, шуға күрә уны тулыһынса һаҡларға ҡарар иттек."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "<q>iRead eBooks</q> (фәнетик рәүештә <q>ai rit i-books</q>; airitibooks.com) сайтынан ирекмән <q>j</q> тарафынан йыйылған мәғлүмәт. <a %(a1)s><q>Башҡа metadata йыйылмалары</q></a> битендәге <q>airitibooks</q> metadata-һына тап килә."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "<a %(a1)s><q>Библиотека Александрина</q></a> коллекцияһынан. Өлөшләтә төп сығанаҡтан, өлөшләтә the-eye.eu сайтынан, өлөшләтә башҡа көҙгөләрҙән."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Шәхси китаптар торрент сайтынан, <a %(a_href)s>Bibliotik</a> (йыш ҡына «Bib» тип атала), китаптар исем буйынса торренттарға тупланған (A.torrent, B.torrent) һәм the-eye.eu аша таратылған."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Беҙҙең ирекмән «bpb9v» тарафынан. <a %(a_href)s>CADAL</a> тураһында күберәк мәғлүмәт өсөн, беҙҙең <a %(a_duxiu)s>DuXiu мәғлүмәттәр битендә</a> иҫкәрмәләрҙе ҡарағыҙ."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Беҙҙең ирекмән «bpb9v» тарафынан күберәк, күбеһе DuXiu файлдары, шулай уҡ «WenQu» һәм «SuperStar_Journals» папкалары (SuperStar - DuXiu артындағы компания)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Беҙҙең ирекмән «cgiym» тарафынан, төрлө сығанаҡтарҙан ҡытай текстары (субдиректориялар рәүешендә күрһәтелгән), шул иҫәптән <a %(a_href)s>China Machine Press</a> (ҡытайҙың төп нәшриәте) сығанаҡтарынан."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Беҙҙең ирекмән «cgiym» тарафынан ҡытай булмаған коллекциялар (субдиректориялар рәүешендә күрһәтелгән)."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Ҡытай архитектураһы тураһында китаптарҙы ирекмән <q>cm</q> йыйған: <q>Мин уны нәшриәт йортонда селтәрҙең етешһеҙлеген файҙаланып алдым, ләкин был етешһеҙлек хәҙер ябылған</q>. <a %(a1)s><q>Башҡа metadata йыйылмалары</q></a> битендәге <q>chinese_architecture</q> metadata-һына тап килә."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "<a %(a_href)s>De Gruyter</a> академик нәшриәтенән китаптар, бер нисә ҙур торренттан йыйылған."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "<a %(a_href)s>docer.pl</a> сайтынан алынған, китаптар һәм башҡа яҙма эштәргә йүнәлтелгән поляк файл алмашыу сайты. 2023 йылдың аҙағында ирекмән «p» тарафынан алынған. Оригиналь сайттан яҡшы метадата юҡ (хатта файл киңәйтеүҙәре лә юҡ), әммә китапҡа оҡшаған файлдарҙы фильтрланыҡ һәм йыш ҡына файлдарҙың үҙенән метадата ала алдыҡ."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epub-тары, DuXiu-нан тура, ирекмән «w» тарафынан йыйылған. Тура электрон китаптар аша тик һуңғы DuXiu китаптары ғына бар, шуға күрә уларҙың күбеһе һуңғы булырға тейеш."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Ирекмән “m” тарафынан DuXiu төп <a %(a_href)s>DuXiu мәғлүмәттәр йыйылмаһы</a> форматында булмаған DuXiu файлдары. Күп оригиналь сығанаҡтарҙан йыйылған, ҡыҙғанысҡа ҡаршы, был сығанаҡтарҙы файл юлына һаҡламайынса."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Эротик китаптарҙы ирекмән <q>do no harm</q> йыйған. <a %(a1)s><q>Башҡа metadata йыйылмалары</q></a> битендәге <q>hentai</q> metadata-һына тап килә."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Япон манга нәшриәтенән ирекмән “t” тарафынан йыйылған коллекция."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Лонгцюандың һайланған суд архивтары</a>, ирекмән “c” тарафынан тәҡдим ителгән."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "<a %(a_href)s>magzdb.org</a> скрапы, Library Genesis менән союздаш (ул libgen.rs баш битендә бәйләнгән), ләкин улар үҙ файлдарын тура тәҡдим итергә теләмәне. 2023 йылдың аҙағында ирекмән “p” тарафынан алынған."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Төрлө бәләкәй йөкләүҙәр, үҙҙәренең субколлекцияһы булараҡ бик бәләкәй, ләкин директориялар булараҡ күрһәтелгән."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "AvaxHome, Рәсәй файл алмашыу сайты, электрон китаптары."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Гәзит һәм журналдар архивы. <a %(a1)s><q>Башҡа metadata йыйылмалары</q></a> битендәге <q>newsarch_magz</q> metadata-һына тап килә."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Философия Документация Үҙәге</a> мәғлүмәттәре."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Ирекмән “o” коллекцияһы, ул поляк китаптарын оригиналь сығанаҡ сайттарынан тура йыйған."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "<a %(a_href)s>shuge.org</a> сайтынан ирекмәндәр «cgiym» һәм «woz9ts» тарафынан берләштерелгән коллекциялар."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>«Транторҙың Император Китапханаһы»</a> (уйҙырма китапхана исеменән алынған), 2022 йылда ирекмән «t» тарафынан алынған."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Ирекмән “woz9ts” тарафынан суб-суб-коллекциялар (директориялар булараҡ күрһәтелгән): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (Тайванда <a %(a_sikuquanshu)s>Dizhi(迪志)</a> тарафынан), mebook (mebook.cc, 我的小书屋, минең бәләкәй китап бүлмәм — woz9ts: “Был сайт, нигеҙҙә, юғары сифатлы электрон китап файлдарын уртаҡлашыуға йүнәлтелгән, ҡайһы берҙәре хужа тарафынан үҙе тарафынан типографияланған. Хужа 2019 йылда <a %(a_arrested)s>ҡулға алынған</a> һәм кемдер уның уртаҡлашҡан файлдарынан коллекция яһаған.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "DuXiu proprietary PDG форматында булмаған, ирекмән «woz9ts» тарафынан ҡалған DuXiu файлдары (һаман да PDF-ҡа әйләндерелергә тейеш)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Анна архивы торренттары"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library йыйылған мәғлүмәт"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library <a %(a_href)s>Library Genesis</a> йәмәғәтселегендә тамырланған, һәм башта уларҙың мәғлүмәттәре менән башланған. Шунан бирле ул һөнәри кимәлгә күтәрелде һәм күпкә заманса интерфейсы бар. Шуға күрә улар күпкә күберәк иғәнәләр ала ала, сайттарын яҡшыртыу өсөн аҡсалата ғына түгел, яңы китаптар иғәнәләре лә. Улар Library Genesis-ҡа өҫтәп ҙур коллекция йыйған."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "2023 йылдың февраленә яңыртыу."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "2022 йылдың аҙағында Z-Library-ҙың ғәйепләнгән нигеҙләүселәре ҡулға алынды, һәм домендар Америка Ҡушма Штаттары властары тарафынан тартып алынды. Шунан бирле сайт яйлап яңынан интернетҡа сыға башланы. Уны кем етәкләүе билдәһеҙ."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Коллекция өс өлөштән тора. Беренсе ике өлөш өсөн оригиналь тасуирлау биттәре аҫта һаҡланған. Бөтә мәғлүмәтте алыу өсөн өс өлөш тә кәрәк (торренттар битендә һыҙылған иҫкергән торренттарҙан башҡа)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: беҙҙең тәүге сығарылыш. Был «Пират Китапхана Көҙгөһө» («pilimi») тип аталғандың тәүге сығарылышы ине."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: икенсе сығарылыш, был юлы бөтә файлдар .tar файлдарына төрөлгән."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: яңы сығарылыштар, хәҙер <a %(a_href)s>Аннаның Архив Контейнерҙары (AAC) форматы</a> ҡулланып, Z-Library командаһы менән хеҙмәттәшлектә сығарыла."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Аннаның Архивы торренттары (метадата + контент)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Аннаның Архивында миҫал яҙмаһы (төп коллекция)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Аннаның Архивында миҫал яҙмаһы (“zlib3” коллекцияһы)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Төп веб-сайт"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor домены"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "1-се сығарылыш тураһында блог яҙмаһы"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "2-се сығарылыш тураһында блог яҙмаһы"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib сығарылыштары (төп тасуирлау биттәре)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "1-се сығарылыш (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Башланғыс көҙгө 2021 һәм 2022 йылдар дауамында ентекләп алынған. Был мәлдә ул бер аҙ иҫкергән: ул коллекцияның 2021 йылдың июнендәге хәлен сағылдыра. Киләсәктә беҙ быны яңыртырбыҙ. Әлеге ваҡытта беҙ был тәүге сығарылышты сығарыуға йүнәлтелгәнбеҙ."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Library Genesis инде асыҡ торренттар менән һаҡланған һәм Z-Library-ға индерелгәнлектән, беҙ 2022 йылдың июнендә Library Genesis менән ябай дубликаттарҙы бөтөрөү үткәрҙек. Бының өсөн беҙ MD5 хэштарын ҡулландык. Китаптың бер үк форматта бер нисә файл кеүек китапханала күп дубликат контент булыуы ихтимал. Был теүәл асыҡлау ауыр, шуға күрә беҙ быны эшләмәйбеҙ. Дубликаттарҙы бөтөргәндән һуң, беҙҙә 2 миллиондан ашыу файл ҡалды, дөйөм күләме 7TB-тан саҡ ҡына кәмерәк."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Коллекция ике өлөштән тора: метадатаның MySQL “.sql.gz” дампы һәм 72 торрент файл, һәр береһе яҡынса 50-100GB. Метадата Z-Library веб-сайты тарафынан хәбәр ителгән мәғлүмәтте (исем, автор, тасуирлау, файл төрө), шулай уҡ беҙ күҙәткән фактик файл ҙурлығын һәм md5sum-ды үҙ эсенә ала, сөнки ҡайһы берҙә улар тап килмәй. Z-Library үҙе дөрөҫ булмаған метадатаға эйә булған файлдар диапазоны бар кеүек. Шулай уҡ ҡайһы бер айырым осраҡтарҙа беҙ дөрөҫ булмаған файлдарҙы күсереп алғанбыҙ, уларҙы киләсәктә асыҡларға һәм төҙәтергә тырышасаҡбыҙ."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Зур торрент файлдарында фактик китап мәғлүмәттәре бар, Z-Library ID-һы файл исеме булараҡ ҡулланыла. Файл киңәйтмәләре метадата дампы ҡулланып тергеҙелергә мөмкин."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Коллекция фәнни һәм нәфис әҙәбиәт контентының ҡатнашмаһы (Library Genesis-тағы кеүек айырымланмаған). Сифаты ла төрлөсә."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Был тәүге сығарылыш хәҙер тулыһынса ҡулланыуға әҙер. Торрент файлдары тик беҙҙең Tor көҙгө аша ғына ҡарарға мөмкин."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "2-се сығарылыш (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Беҙ Z-Library-ға һуңғы көҙгө һәм 2022 йылдың авгусына тиклем өҫтәлгән бөтә китаптарҙы алдыҡ. Шулай уҡ беҙ тәүге тапҡыр үткәреп ебәргән ҡайһы бер китаптарҙы кире алып ҡараныҡ. Дөйөм алғанда, был яңы коллекция яҡынса 24TB. Тағы ла, был коллекция Library Genesis менән дубликаттарҙы бөтөрөү үткәрелгән, сөнки был коллекция өсөн торренттар инде бар."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Мәғлүмәттәр беренсе сығарылышҡа оҡшаш итеп ойошторолған. Метадатаның MySQL “.sql.gz” дампы бар, ул шулай уҡ беренсе сығарылыштың бөтә метадатаһын үҙ эсенә ала, шуның менән уны алмаштыра. Шулай уҡ ҡайһы бер яңы бағаналар өҫтәлде:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: был файл Library Genesis-та, йә фәнни, йә нәфис әҙәбиәт коллекцияһында (md5 буйынса тап килгән) бармы."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: был файл ҡайһы торрентта."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: китапты күсереп ала алмаған ваҡытта ҡуйыла."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Беҙ быны һуңғы тапҡыр телгә алдыҡ, ләкин аныҡлыҡ өсөн: “файл исеме” һәм “md5” файлдың ысын үҙенсәлектәре, ә “файл исеме_хәбәр ителгән” һәм “md5_хәбәр ителгән” Z-Library-ҙан йыйылған мәғлүмәттәр. Ҡайһы саҡта былар бер-береһенә тап килмәй, шуға күрә беҙ икеһен дә индерҙек."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Был сығарылыш өсөн беҙ колляцияны “utf8mb4_unicode_ci” итеп үҙгәрттек, был MySQL-дың иҫке версиялары менән яраҡлы булырға тейеш."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Мәғлүмәт файлдары һуңғы тапҡыр менән оҡшаш, ләкин улар күпкә ҙурыраҡ. Беҙ күп һанлы бәләкәй торрент файлдары булдырыу менән мәшғүл булманыҡ. “pilimi-zlib2-0-14679999-extra.torrent” һуңғы сығарылышта етешмәгән бөтә файлдарҙы үҙ эсенә ала, ә башҡа торренттар яңы ID диапазондары. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Яңыртыу %(date)s:</strong> Беҙҙең торренттарҙың күбеһе бик ҙур булды, был торрент клиенттарына ҡыйынлыҡ тыуҙырҙы. Беҙ уларҙы алып ташланыҡ һәм яңы торренттар сығарҙыҡ."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Яңыртыу %(date)s:</strong> Файлдар һаман да бик күп ине, шуға күрә беҙ уларҙы tar файлдарына төрҙөк һәм йәнә яңы торренттар сығарҙыҡ."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Сығарылыш 2 өҫтәмәһе (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Был бер генә өҫтәмә торрент файлы. Ул бер ниндәй ҙә яңы мәғлүмәт үҙ эсенә алмай, ләкин унда иҫәпләү өсөн ваҡыт талап ителгән ҡайһы бер мәғлүмәт бар. Был уны уңайлы итә, сөнки был торрентты күсереп алыу уны баштан иҫәпләүгә ҡарағанда йышыраҡ тиҙерәк була. Айырыуса, ул tar файлдары өсөн SQLite индекстарын үҙ эсенә ала, <a %(a_href)s>ratarmount</a> менән ҡулланыу өсөн."

#, fuzzy
msgid "page.faq.title"
msgstr "Йыш бирелгән һорауҙар (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Аннаның Архивы нимә ул?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Аннаның Архивы</span> ике маҡсатлы коммерцияға ҡарамаған проект:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Һаҡлау:</strong> Кешелек белеме һәм мәҙәниәтен һаҡлау.</li><li><strong>Асыҡлыҡ:</strong> Был белем һәм мәҙәниәтте бөтә донъяға таратыу.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Бөтә <a %(a_code)s>кодтарыбыҙ</a> һәм <a %(a_datasets)s>мәғлүмәттәребеҙ</a> тулыһынса асыҡ сығанаҡлы."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Һаҡлау"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Беҙ китаптарҙы, мәҡәләләрҙе, комикстарҙы, журналдарҙы һәм башҡа материалдарҙы төрлө <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">күңелһеҙ китапханаларҙан</a>, рәсми китапханаларҙан һәм башҡа коллекцияларҙан бер урынға йыйып һаҡлайбыҙ. Был мәғлүмәттәрҙе мәңгелеккә һаҡлау өсөн уларҙы күпләп күсереп алыуҙы еңеләйтәбеҙ — торренттар ҡулланып — донъя буйлап күп нөсхәләр булдырабыҙ. Ҡайһы бер күңелһеҙ китапханалар үҙҙәре үк был эште башҡара (мәҫәлән, Sci-Hub, Library Genesis), ә Аннаның Архивы күпләп таратыуҙы тәҡдим итмәгән китапханаларҙы “азат итә” (мәҫәлән, Z-Library) йәки бөтөнләй күңелһеҙ китапханалар булмағандарын (мәҫәлән, Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Был киң таратыу, асыҡ сығанаҡлы код менән бергә, беҙҙең веб-сайтты юҡҡа сығарыуҙан һаҡлай һәм кешелек белеме һәм мәҙәниәтен оҙайлы һаҡлауҙы тәьмин итә. <a href=\"/datasets\">мәғлүмәттәребеҙ тураһында</a> күберәк белегеҙ."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Беҙ донъяның китаптарының яҡынса <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% процентын һаҡлағанбыҙ тип иҫәпләйбеҙ.</a>"

#, fuzzy
msgid "page.home.access.header"
msgstr "Асыҡлыҡ"

#, fuzzy
msgid "page.home.access.text"
msgstr "Беҙҙең коллекцияларҙы һәр кемгә еңел һәм бушлай ҡулланыу мөмкинлеген биреү өсөн партнерҙар менән эшләйбеҙ. Беҙ һәр кемдең кешелек аҡылына хоҡуғы бар тип иҫәпләйбеҙ. Һәм <a %(a_search)s>авторҙар иҫәбенә түгел</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Һуңғы 30 көндә сәғәт һайын күсереп алыуҙар. Сәғәтлек уртаса: %(hourly)s. Көндәлек уртаса: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Беҙ мәғлүмәт ағымының ирекле булыуына һәм белем һәм мәҙәниәтте һаҡлауға ныҡлы ышанабыҙ. Был эҙләү системаһы менән беҙ гиганттарҙың иңенә баҫабыҙ. Күңелһеҙ китапханаларҙы булдырған кешеләрҙең ауыр хеҙмәтен тәрән хөрмәт итәбеҙ һәм был эҙләү системаһы уларҙың ҡулланыу даирәһен киңәйтер тип өмөтләнәбеҙ."

#, fuzzy
msgid "page.about.text3"
msgstr "Прогресс тураһында яңыртыуҙарҙы белеп тороу өсөн Аннаны <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> йәки <a href=\"https://t.me/annasarchiveorg\">Telegram</a> аша күҙәтегеҙ. Һорауҙар һәм фекерҙәр өсөн Аннаға %(email)s адресы буйынса мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Нисек ярҙам итә алам?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Беҙҙе <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> йәки <a href=\"https://t.me/annasarchiveorg\">Telegram</a> аша күҙәтегеҙ.</li><li>2. Аннаның Архивы тураһында Twitter, Reddit, Tiktok, Instagram, урындағы кафе йәки китапханала, йәки ҡайҙа ғына барһағыҙ ҙа һөйләгеҙ! Беҙ ҡапҡасыларға ышанмайбыҙ — беҙҙе юҡҡа сығарһалар, бөтә кодтарыбыҙ һәм мәғлүмәттәребеҙ тулыһынса асыҡ сығанаҡлы булғанлыҡтан, беҙ башҡа урында яңынан ҡалҡып сығырбыҙ.</li><li>3. Мөмкин булһа, <a href=\"/donate\">иғәнә</a> яһағыҙ.</li><li>4. Веб-сайтыбыҙҙы төрлө телдәргә <a href=\"https://translate.annas-software.org/\">тәржемә итешегеҙ</a>.</li><li>5. Әгәр һеҙ программалаусы булһағыҙ, беҙҙең <a href=\"https://annas-software.org/\">асыҡ сығанаҡҡа</a> өлөш индерегеҙ, йәки беҙҙең <a href=\"/datasets\">торренттарҙы</a> таратығыҙ.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Беҙ хәҙер %(matrix)s адресы буйынса синхронлаштырылған Matrix каналына эйә."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Әгәр һеҙ хәүефһеҙлек тикшеренеүсеһе булһағыҙ, һеҙҙең һәләттәрегеҙҙе һөжүм һәм һаҡланыу өсөн ҡуллана алабыҙ. <a %(a_security)s>Хәүефһеҙлек</a> битен ҡарағыҙ."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Аноним сауҙагәрҙәр өсөн түләүҙәр буйынса эксперттар эҙләйбеҙ. Донаттарҙы уңайлыраҡ итеү өсөн ярҙам итә алаһығыҙмы? PayPal, WeChat, бүләк карталары. Кемде булһа ла белһәгеҙ, беҙгә мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Беҙ һәр ваҡыт күберәк сервер ҡеүәте эҙләйбеҙ."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Файлдарҙағы хаталарҙы хәбәр итеп, комментарийҙар ҡалдырып һәм был веб-сайтта исемлектәр төҙөп ярҙам итә алаһығыҙ. Шулай уҡ <a %(a_upload)s>күберәк китаптарҙы йөкләп</a>, йәки булған китаптарҙың файл хаталарын йәки форматтарын төҙәтеп ярҙам итә алаһығыҙ."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Аннаның Архивы өсөн Википедия битен үҙ телегеҙҙә булдырығыҙ йәки ярҙам итегеҙ."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Беҙ бәләкәй, зауыҡлы рекламалар урынлаштырырға теләйбеҙ. Әгәр һеҙ Аннаның Архивында реклама урынлаштырырға теләһәгеҙ, зинһар, беҙгә хәбәр итегеҙ."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Беҙ кешеләрҙең <a %(a_mirrors)s>күҙгөләр</a> булдырыуын теләр инек, һәм беҙ быны финанс яҡтан хуплаясаҡбыҙ."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Для получения более подробной информации о том, как стать волонтером, см. нашу страницу <a %(a_volunteering)s>Волонтерство и Награды</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Ни өсөн әкрен күсереп алыуҙар шул тиклем әкрен?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Беҙ бөтә донъяға юғары тиҙлектәге йөкләүҙәр биреү өсөн етерлек ресурстарға эйә түгелбеҙ, нисек кенә теләһәк тә. Әгәр бай берәй хәйриәсе быны беҙгә тәьмин итергә теләһә, был ғәжәйеп булыр ине, ләкин шул ваҡытҡа тиклем, беҙ ҡулдан килгәнде эшләйбеҙ. Беҙ донорҙар ярҙамында саҡ-саҡ йәшәп килгән коммерцияға ҡарамаған проект."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Шуның өсөн беҙ партнерҙарыбыҙ менән бушлай йөкләүҙәр өсөн ике система индерҙек: әкрен йөкләүҙәр менән уртаҡ серверҙар, һәм көтөү исемлеге менән бер аҙ тиҙерәк серверҙар (бер үк ваҡытта йөкләүселәр һанын кәметеү өсөн)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Беҙ шулай уҡ әкрен йөкләүҙәр өсөн <a %(a_verification)s>браузер тикшереүе</a> индерҙек, сөнки башҡа осраҡта боттар һәм скреперҙар уларҙы файҙаланыр ине, был ысын ҡулланыусылар өсөн эште тағы ла әкренәйтер ине."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Tor браузерын ҡулланғанда, хәүефһеҙлек көйләүҙәрен көйләү кәрәк булыуы мөмкин. Иң түбән вариантта, “Стандарт” тип аталған, Cloudflare turnstile һынауы уңышлы үтә. Юғары варианттарҙа, “Хәүефһеҙерәк” һәм “Иң хәүефһеҙ”, һынау уңышһыҙ үтә."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Зур файлдар өсөн ҡайһы саҡта әкрен тиҙлектәге күсереп алыуҙар уртала өҙөлә ала. Беҙ JDownloader кеүек күсереп алыу менеджерын ҡулланыуҙы тәҡдим итәбеҙ, ул ҙур күсереп алыуҙарҙы автоматик рәүештә яңынан башлай ала."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Иғәнә буйынса йыш бирелгән һорауҙар"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Әғзалыҡ автоматик рәүештә яңыртыламы?</div> Әғзалыҡ <strong>автоматик рәүештә яңыртылмай</strong>. Һеҙ теләгәнсә оҙаҡ йәки ҡыҫҡа ваҡытҡа ҡушыла алаһығыҙ."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Мин ағзалыҡты яңырта аламмы йәки бер нисә ағзалыҡ ала аламмы?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Башҡа түләү ысулдары бармы?</div> Әлегә юҡ. Күп кешеләр бындай архивтарҙың булыуын теләмәй, шуға күрә беҙ һаҡ булырға тейешбеҙ. Әгәр һеҙ беҙгә башҡа (күберәк уңайлы) түләү ысулдарын хәүефһеҙ рәүештә булдырырға ярҙам итә алаһығыҙ икән, зинһар, %(email)s адресы буйынса беҙгә мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Айына диапазондар нимәне аңлата?</div> Һеҙ диапазондың түбән яғына бөтә ташламаларҙы ҡулланып, мәҫәлән, айҙан оҙонораҡ осор һайлап, барып етә алаһығыҙ."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Иғәнәләрҙе нимәгә сарыф итәһегеҙ?</div> 100%% донъяның белем һәм мәҙәниәтен һаҡлауға һәм уларҙы ҡулланыуға биреүгә китә. Әлеге ваҡытта беҙ уны күбеһенсә серверҙарға, һаҡлауға һәм киңлеккә сарыф итәбеҙ. Бер ниндәй ҙә аҡса шәхсән берәй команда ағзаһына китмәй."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Мин ҙур иғәнә яһай аламмы?</div> Был ғәжәйеп булыр ине! Бер нисә мең долларҙан ашыу иғәнәләр өсөн, зинһар, %(email)s адресы буйынса беҙгә тура мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Мин әғза булмайынса иғәнә яһай аламмы?</div> Әлбиттә. Беҙ теләһә ниндәй күләмдәге иғәнәләрҙе был Monero (XMR) адресы буйынса ҡабул итәбеҙ: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Мин яңы китаптарҙы нисек йөкләй алам?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Альтернатива булараҡ, һеҙ уларҙы Z-Library-ға <a %(a_upload)s>бында</a> йөкләй алаһығыҙ."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Бәләкәй йөкләүҙәр өсөн (10,000 файлға тиклем) уларҙы %(first)s һәм %(second)s икеһенә лә йөкләгеҙ."

#, fuzzy
msgid "page.upload.text1"
msgstr "Әлегә, беҙ яңы китаптарҙы Library Genesis форктарына йөкләргә тәҡдим итәбеҙ. Бына <a %(a_guide)s>ҡулланма</a>. Иғтибар итегеҙ, был сайтта индексацияланған ике форк та ошо уҡ йөкләү системаһынан файҙалана."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.li өсөн, <a %(a_forum)s >уларҙың форумына</a> %(username)s ҡулланыусы исеме һәм %(password)s пароль менән инегеҙ, һәм шунан уларҙың <a %(a_upload_page)s >йөкләү битенә</a> кире ҡайтығыҙ."

#, fuzzy
msgid "common.libgen.email"
msgstr "Әгәр һеҙҙең электрон почта адресығыҙ Libgen форумдарында эшләмәй икән, беҙ <a %(a_mail)s>Proton Mail</a> (бушлай) ҡулланыуҙы тәҡдим итәбеҙ. Шулай уҡ һеҙҙең аккаунтты активлаштырыу өсөн <a %(a_manual)s>ҡулдан һорау</a> яһай алаһығыҙ."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Иғтибар итегеҙ, mhut.org ҡайһы бер IP диапазондарын блоклай, шуға күрә VPN кәрәк булыуы мөмкин."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Зур йөкләүҙәр өсөн (10,000 файлдан ашыу) Libgen йәки Z-Library ҡабул итмәһә, зинһар, %(a_email)s адресы буйынса беҙгә мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Академик мәҡәләләрҙе йөкләү өсөн, зинһар, (Library Genesis-тан тыш) <a %(a_stc_nexus)s>STC Nexus</a>-ҡа ла йөкләгеҙ. Улар яңы мәҡәләләр өсөн иң яҡшы күләгә китапханаһы. Беҙ уларҙы әлегә интеграцияламағанбыҙ, ләкин бер мәлдә был эште башҡарасаҡбыҙ. Һеҙ уларҙың <a %(a_telegram)s>Telegram-дағы йөкләү ботын</a> ҡуллана алаһығыҙ, йәки был ысул менән йөкләргә файлығыҙ күп булһа, уларҙың пинләнгән хәбәрендә күрһәтелгән адресҡа мөрәжәғәт итә алаһығыҙ."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Мин китаптарҙы нисек һорай алам?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Әлеге ваҡытта, беҙ китап һорауҙарын ҡабул итә алмайбыҙ."

#, fuzzy
msgid "page.request.forums"
msgstr "Зинһар, үҙегеҙҙең һорауҙарығыҙҙы Z-Library йәки Libgen форумдарына ебәрегеҙ."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Беҙгә китап һорауҙарығыҙҙы электрон почта аша ебәрмәгеҙ."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Һеҙ метадата йыяһығыҙмы?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Эйе, беҙ йыябыҙ."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Мин Джордж Оруэллдың \"1984\" китабын күсереп алдым, полиция минең ишеккә килерме?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Бик борсолмағыҙ, беҙҙең менән бәйле сайттарҙан күп кеше күсереп ала, һәм проблемаларға эләгеү бик һирәк осрай. Әммә хәүефһеҙ булыу өсөн VPN (түленгән) йәки <a %(a_tor)s>Tor</a> (бушлай) ҡулланыуҙы тәҡдим итәбеҙ."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Эҙләү көйләүҙәрен нисек һаҡларға?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Үҙегеҙгә оҡшаған көйләүҙәрҙе һайлағыҙ, эҙләү ҡумтаһын буш ҡалдырығыҙ, \"Эҙләү\" төймәһенә баҫығыҙ, һәм битте браузерығыҙҙың закладка функцияһы ярҙамында һаҡлағыҙ."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Һеҙҙең мобиль ҡушымтағыҙ бармы?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Беҙҙең рәсми мобиль ҡушымта юҡ, әммә был сайтты ҡушымта итеп ҡуйырға мөмкин."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Өҫкө уң яҡтағы өс нөктәле менюға баҫығыҙ, һәм \"Өй экранына өҫтәргә\" һайлағыҙ."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Түбәндәге \"Уртаҡлашыу\" төймәһенә баҫығыҙ, һәм \"Өй экранына өҫтәргә\" һайлағыҙ."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Һеҙҙең API бармы?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Беҙҙең ағзалар өсөн тиҙ күсереп алыу URL-адресын алыу өсөн бер тотороҡло JSON API бар: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON эсендә документация)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Башҡа ҡулланыу осраҡтары өсөн, мәҫәлән, бөтә файлдарыбыҙҙы ҡарап сығыу, махсус эҙләү төҙөү һәм башҡалар өсөн, беҙ <a %(a_generate)s>генерациялауҙы</a> йәки <a %(a_download)s>ElasticSearch һәм MariaDB базаларын күсереп алыуҙы</a> тәҡдим итәбеҙ. Сығанаҡ мәғлүмәттәрҙе ҡулдан <a %(a_explore)s>JSON файлдары аша</a> өйрәнергә мөмкин."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Беҙҙең сығанаҡ торренты исемлеген <a %(a_torrents)s>JSON</a> форматында күсереп алырға мөмкин."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Торренттар буйынса йыш бирелгән һорауҙар"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Мин ярҙам итергә теләйем, ләкин миндә күп диск урыны юҡ."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "<a %(a_list)s>Торрент исемлеге генераторын</a> ҡулланып, үҙегеҙҙең һаҡлау урыны сиктәрендә иң кәрәкле торренты исемлеген төҙөгөҙ."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Торренттар бик әкрен; мәғлүмәтте тура һеҙҙән күсереп ала аламмы?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Эйе, <a %(a_llm)s>LLM мәғлүмәттәре</a> битен ҡарағыҙ."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Мин файлдарҙың бер өлөшөн генә, мәҫәлән, билдәле бер тел йәки тема буйынса ғына күсереп ала аламмы?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Ҡыҫҡа яуап: еңел түгел."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Оҙон яуап:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Күпселек торренттарҙа файлдар туранан-тура бар, тимәк, һеҙ торрент клиенттарына кәрәкле файлдарҙы ғына күсереп алырға күрһәтмә бирә алаһығыҙ. Ҡайһы файлдарҙы күсереп алырға кәрәклеген билдәләү өсөн, беҙҙең метадата <a %(a_generate)s>генерациялай</a> алаһығыҙ, йәки беҙҙең ElasticSearch һәм MariaDB базаларын <a %(a_download)s>күсереп ала</a> алаһығыҙ. Ҡыҙғанысҡа ҡаршы, ҡайһы бер торрент коллекцияларының төп директорияһында .zip йәки .tar файлдары бар, был осраҡта һеҙ айырым файлдарҙы һайлап алыр алдынан бөтә торрентты күсереп алырға кәрәк."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Беҙҙә һуңғы осраҡ өсөн <a %(a_ideas)s>ҡайһы бер идеялар</a> бар.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Әлегә торренттарҙы фильтрлау өсөн ҡулланыу уңайлы ҡоралдар юҡ, ләкин беҙ өлөш индереүҙәрҙе хуплайбыҙ."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Торренттарҙа дубликаттарҙы нисек эшкәртәһегеҙ?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Беҙ был исемлектәге торренттар араһында минималь дубликаттар йәки ҡабатланыуҙар булмаҫҡа тырышабыҙ, ләкин был һәр ваҡыт мөмкин түгел, һәм сығанаҡ китапханаларҙың сәйәсәттәренә ныҡ бәйле. Үҙ торренттарын сығарған китапханалар өсөн, был беҙҙең ҡулда түгел. Анна архивы тарафынан сығарылған торренттар өсөн, беҙ дубликаттарҙы тик MD5 хэш нигеҙендә генә эшкәртәбеҙ, тимәк, бер үк китаптың төрлө версиялары дубликатланмай."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Торрент исемлеген JSON форматында ала аламмы?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Эйе."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Мин торренттарҙа PDF йәки EPUB файлдарын күрмәйем, тик бинар файлдар ғына бар? Нимә эшләргә?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Былар ысынлап та PDF һәм EPUB файлдары, уларҙың күп торренттарҙа киңәйтеүе генә юҡ. Торрент файлдары өсөн метадата таба алған ике урын бар, шул иҫәптән файл төрҙәре/киңәйтеүҙәре:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Һәр коллекция йәки сығарылыштың үҙ метадатаһы бар. Мәҫәлән, <a %(a_libgen_nonfic)s>Libgen.rs торренттары</a>ның Libgen.rs сайтында урынлаштырылған метадата базаһы бар. Беҙ ғәҙәттә һәр коллекцияның <a %(a_datasets)s>мәғлүмәттәр битенән</a> тейешле метадата ресурстарына һылтанмалар бирәбеҙ."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Беҙҙең ElasticSearch һәм MariaDB базаларын <a %(a_generate)s>генерациялауҙы</a> йәки <a %(a_download)s>күсереп алыуҙы</a> тәҡдим итәбеҙ. Был базаларҙа Анна архивындағы һәр яҙма өсөн уның торрент файлдарына (әгәр бар икән) тура килгән маппинг бар, ElasticSearch JSON-да “torrent_paths” аҫтында."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Ни өсөн минең торрент клиент ҡайһы бер торрент файлдарығыҙҙы / магнит һылтанмаларығыҙҙы аса алмай?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Ҡайһы бер торрент клиенттары ҙур киҫәк ҙурлыҡтарын хупламай, ә күп торренттарыбыҙҙа улар бар (яңыраҡтары өсөн беҙ быны башҡармайбыҙ — спецификациялар буйынса был дөрөҫ булһа ла!). Шуға күрә, әгәр был проблемаға осраһағыҙ, башҡа клиентты һынап ҡарағыҙ йәки торрент клиенттарығыҙҙың етештереүселәренә ялыу итегеҙ."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Һеҙҙең яуаплы асыҡлау программаһы бармы?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Беҙ хәүефһеҙлек тикшеренеүселәрен беҙҙең системаларҙағы етешһеҙлектәрҙе эҙләргә саҡырабыҙ. Беҙ яуаплы асыҡлауҙың ҙур яҡлыларыбыҙ. Беҙҙең менән <a %(a_contact)s>бында</a> бәйләнешкә инегеҙ."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Беҙ хәҙерге ваҡытта хаталар өсөн бүләктәр бирә алмайбыҙ, тик беҙҙең анонимлыҡты <a %(a_link)s>компрометациялау мөмкинлегенә</a> эйә булған етешһеҙлектәр өсөн $10k-50k диапазонында бүләктәр тәҡдим итәбеҙ. Киләсәктә хаталар өсөн киңерәк бүләкләү мөмкинлеген тәҡдим итергә теләйбеҙ! Иғтибар итегеҙ, социаль инженерия һөжүмдәре программаға инмәй."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Әгәр һеҙ һөжүм итеү хәүефһеҙлегендә ҡыҙыҡһынаһығыҙ һәм донъяның белем һәм мәҙәниәтен архивларға ярҙам итергә теләһәгеҙ, беҙҙең менән бәйләнешкә инегеҙ. Һеҙ ярҙам итә алған күп ысулдар бар."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Анна архивы тураһында күберәк ресурстар бармы?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Анна блогы</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — даими яңыртыуҙар"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Анна программаһы</a> — беҙҙең асыҡ сығанаҡ коды"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Анна программаһында тәржемә</a> — беҙҙең тәржемә системаһы"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — мәғлүмәттәр тураһында"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтернатив домендар"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — беҙ тураһында күберәк мәғлүмәт (был битте яңыртыуҙа ярҙам итегеҙ, йәки үҙ телегеҙҙә яңынан булдырығыҙ!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Мин авторлыҡ хоҡуҡтарын боҙоу тураһында нисек хәбәр итә алам?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Беҙ бында авторлыҡ хоҡуҡтары һаҡланған материалдарҙы урынлаштырмайбыҙ. Беҙ эҙләү системаһы булып торабыҙ һәм шуға күрә тик асыҡтан-асыҡ булған метадата ғына индексациялайбыҙ. Был тышҡы сығанаҡтарҙан йөкләгәндә, һеҙҙең юрисдикцияла нимә рөхсәт ителгәнен тикшерергә тәҡдим итәбеҙ. Беҙ башҡалар тарафынан урынлаштырылған контент өсөн яуаплы түгелбеҙ."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Әгәр һеҙ бында күргәнегеҙ тураһында ялыуҙарығыҙ булһа, иң яҡшыһы - төп сайт менән бәйләнешкә инеү. Беҙ уларҙың үҙгәрештәрен даими рәүештә базаға индереп торабыҙ. Әгәр һеҙ ысынлап та беҙ яуап бирергә тейешле дөрөҫ DMCA ялыуығыҙ бар тип уйлаһағыҙ, зинһар, <a %(a_copyright)s>DMCA / Авторлыҡ хоҡуҡтары ялыу формаһын</a> тултырығыҙ. Беҙ һеҙҙең ялыуҙарығыҙҙы етди ҡабул итәбеҙ һәм мөмкин тиклем тиҙерәк яуап бирәсәкбеҙ."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Мин һеҙҙең был проектты нисек алып барыуығыҙҙы яратмайым!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Беҙ шулай уҡ бөтә код һәм мәғлүмәттәребеҙҙең тулыһынса асыҡ сығанаҡлы булыуын иҫегеҙгә төшөрөргә теләйбеҙ. Беҙҙең кеүек проекттар өсөн был уникаль — беҙ шулай уҡ тулыһынса асыҡ сығанаҡлы булған ошондай ҙур каталоглы башҡа проекттарҙы белмәйбеҙ. Беҙ проектты насар алып барабыҙ тип уйлаған һәр кемдең беҙҙең код һәм мәғлүмәттәрҙе алып, үҙҙәренең күләгә китапханаһын булдырыуын бик хуплайбыҙ! Беҙ быны асыуҙан йәки башҡа нәмәнән әйтмәйбеҙ — беҙ ысынлап та был бик шәп булыр ине тип уйлайбыҙ, сөнки был барыһы өсөн дә стандартты күтәрер ине һәм кешелек мираҫын яҡшыраҡ һаҡлар ине."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Һеҙҙең эшләү ваҡытын күҙәтеүсе бармы?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Зинһар, <a %(a_href)s>был бик яҡшы проектты</a> ҡарағыҙ."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Мин нисек китаптар йәки башҡа физик материалдар бүләк итә алам?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Зинһар, уларҙы <a %(a_archive)s>Интернет Архивына</a> ебәрегеҙ. Уларҙы тейешенсә һаҡлаясаҡтар."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Анна кем ул?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Һеҙ Анна!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Һеҙҙең яратҡан китаптарығыҙ ниндәй?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Бына күләгә китапханалары һәм цифрлы һаҡлау донъяһы өсөн айырыуса әһәмиәтле булған ҡайһы бер китаптар:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Бөгөн һеҙҙең тиҙ йөкләүҙәрегеҙ бөттө."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Тиҙ йөкләүҙәрҙе ҡулланыу өсөн ағза булығыҙ."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Беҙ хәҙер Amazon бүләк карталарын, кредит һәм дебет карталарын, крипто, Alipay һәм WeChat ҡабул итәбеҙ."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Тулы база"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Китаптар, мәҡәләләр, журналдар, комикстар, китапхана яҙмалары, метадата, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Эҙләү"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "бета"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub яңы мәҡәләләрҙе йөкләүҙе <a %(a_paused)s>туҡтатты</a>."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB Sci-Hub дауамы булып тора."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "%(count)s академик мәҡәләләргә тура инеү"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Асҡас"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Әгәр һеҙ <a %(a_member)s>ағза</a> булһағыҙ, браузер тикшереүе талап ителмәй."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Оҙайлы ваҡытлы архив"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Аннаның Архивында ҡулланылған datasets тулыһынса асыҡ, һәм уларҙы торренттар ярҙамында күпләп күсереп алырға мөмкин. <a %(a_datasets)s>Тулыраҡ белергә…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Торренттарҙы таратыуҙа ярҙам итеүегеҙ ҙур ярҙам булыр ине. <a %(a_torrents)s>Тулыраҡ белергә…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s таратыусылар"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s таратыусылар"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s таратыусылар"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM уҡытыу мәғлүмәттәре"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Беҙҙә донъялағы иң ҙур юғары сифатлы текст мәғлүмәттәре коллекцияһы бар. <a %(a_llm)s>Тулыраҡ белергә…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Көҙгөләр: ирекмәндәргә саҡырыу"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Ирекмәндәр эҙләйбеҙ"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Коммерцияға ҡарамаған, асыҡ сығанаҡлы проект булараҡ, беҙ һәр ваҡыт ярҙам итергә теләгән кешеләрҙе эҙләйбеҙ."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Әгәр һеҙ юғары хәүефле аноним түләү процессорын эшләтһәгеҙ, беҙгә мөрәжәғәт итегеҙ. Шулай уҡ зәвыҡлы бәләкәй реклама урынлаштырырға теләгән кешеләрҙе лә эҙләйбеҙ. Бөтә килемдәр беҙҙең һаҡлау тырышлыҡтарына китә."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Аннаның Блогы ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS күсереп алыуҙар"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Был файл өсөн бөтә күсереп алыу һылтанмалары: <a %(a_main)s>Файлдың төп битенә</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Шлюзы #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(IPFS менән бер нисә тапҡыр һынап ҡарарға кәрәк булыуы мөмкин)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Тиҙерәк күсереп алыуҙар һәм браузер тикшереүҙәрен урап үтеү өсөн, <a %(a_membership)s>ағза булығыҙ</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Беҙҙең коллекцияны күсереп алыу өсөн, <a %(a_datasets)s>Datasets</a> һәм <a %(a_torrents)s>Torrents</a> биттәрен ҡарағыҙ."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM мәғлүмәттәре"

#, fuzzy
msgid "page.llm.intro"
msgstr "LLM-дарҙың юғары сифатлы мәғлүмәттәрҙә уңышлы эшләүе яҡшы аңлашыла. Беҙҙә донъялағы иң ҙур китаптар, мәҡәләләр, журналдар коллекцияһы бар, улар иң юғары сифатлы текст сығанаҡтары булып тора."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Үҙенсәлекле масштаб һәм диапазон"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Беҙҙең коллекцияла йөҙ миллиондан ашыу файл бар, шул иҫәптән академик журналдар, дәреслектәр һәм журналдар. Беҙ был масштабҡа ҙур булған бар репозиторийҙарҙы берләштереү аша ирешәбеҙ."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Беҙҙең сығанаҡтарҙың ҡайһы бер коллекциялары күпләп алыу өсөн инде бар (Sci-Hub һәм Libgen өлөштәре). Башҡа сығанаҡтарҙы беҙ үҙебеҙ азат иттек. <a %(a_datasets)s>Datasets</a> тулы күҙәтеүҙе күрһәтә."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Беҙҙең коллекцияға электрон китаптар дәүеренә тиклемге миллионлаған китаптар, мәҡәләләр һәм журналдар инә. Был коллекцияның ҙур өлөшө инде OCR-ланған, һәм эске ҡабатланыуҙар бик аҙ."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Беҙ нисек ярҙам итә алабыҙ"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Беҙ тулы коллекцияларҙы, шулай уҡ сығарылмаған коллекцияларҙы юғары тиҙлектә ҡулланыу мөмкинлеген тәҡдим итә алабыҙ."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Был предприятиелар кимәлендәге ҡулланыу мөмкинлеге, беҙ уны ун меңдәрсә доллар күләмендәге иғәнәләр өсөн тәҡдим итә алабыҙ. Шулай уҡ беҙ быларҙы беҙҙә булмаған юғары сифатлы коллекциялар менән алмаштырырға әҙербеҙ."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Әгәр һеҙ беҙҙең мәғлүмәттәрҙе байытыу менән тәьмин итә алһағыҙ, беҙ һеҙгә аҡсаны кире ҡайтара алабыҙ, мәҫәлән:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Ҡабатланыуҙы бөтөрөү (дедупликация)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Текст һәм метадата сығарыу"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Кеше белеменең оҙайлы ваҡытҡа һаҡланыуын тәьмин итегеҙ, шул уҡ ваҡытта үҙегеҙҙең моделегеҙ өсөн яҡшыраҡ мәғлүмәт алығыҙ!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Беҙҙең менән бәйләнешкә сығығыҙ</a>, нисек бергә эшләй аласағыбыҙҙы тикшерәйек."

#, fuzzy
msgid "page.login.continue"
msgstr "Дауам итегеҙ"

#, fuzzy
msgid "page.login.please"
msgstr "Зинһар, был битте ҡарау өсөн <a %(a_account)s>логин</a> яһағыҙ.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Аннаның Архивы ваҡытлыса техник хеҙмәтләндереү өсөн ябылған. Бер сәғәттән һуң кире килегеҙ."

#, fuzzy
msgid "page.metadata.header"
msgstr "Метадата яҡшыртыу"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Һеҙ китаптарҙы һаҡлауға ярҙам итә алаһығыҙ, метадатаны яҡшыртып! Башта, Аннаның Архивында метадата тураһында мәғлүмәтте уҡығыҙ, һәм Open Library менән бәйләнеш аша метадатаны нисек яҡшыртырға өйрәнегеҙ, һәм Аннаның Архивында бушлай ағзалыҡ алығыҙ."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Фон"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Аннаның Архивында китапҡа ҡарағанда, һеҙ төрлө өлкәләрҙе күрә алаһығыҙ: исем, автор, нәшриәт, баҫма, йыл, тасуирлама, файл исеме, һәм башҡалар. Был мәғлүмәт өлөштәре <em>метадата</em> тип атала."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Беҙ китаптарҙы төрлө <em>сығанаҡ китапханаларынан</em> берләштергәнгә күрә, беҙ шул сығанаҡ китапханала булған метадатаны күрһәтәбез. Мәҫәлән, Library Genesis-тан алынған китап өсөн, беҙ Library Genesis базаһынан алынған исемде күрһәтәсәкбеҙ."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Ҡайһы берҙә китап <em>бер нисә</em> сығанаҡ китапханала була, уларҙа төрлө метадата өлкәләре булыуы мөмкин. Был осраҡта, беҙ һәр өлкә өсөн иң оҙон версияны ғына күрһәтәсәкбеҙ, сөнки ул иң файҙалы мәғлүмәтте үҙ эсенә ала тип өмөтләнелә! Беҙ барыбер башҡа өлкәләрҙе тасуирлама аҫтында күрһәтәсәкбеҙ, мәҫәлән, ”альтернатив исем” булараҡ (әммә улар төрлө булһа ғына)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Беҙ шулай уҡ сығанаҡ китапхананан <em>идентификаторҙар һәм классификаторҙар</em> кеүек кодтарҙы алабыҙ. <em>Идентификаторҙар</em> китаптың билдәле баҫмаһын уникаль рәүештә күрһәтә; миҫалдар: ISBN, DOI, Open Library ID, Google Books ID, йәки Amazon ID. <em>Классификаторҙар</em> бер нисә оҡшаш китапты берләштерә; миҫалдар: Dewey Decimal (DCC), UDC, LCC, RVK, йәки GOST. Был кодтар ҡайһы берҙә сығанаҡ китапханаларҙа асыҡ бәйләнгән, һәм ҡайһы берҙә беҙ уларҙы файл исеменән йәки тасуирламанан ала алабыҙ (төпсән ISBN һәм DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Беҙ идентификаторҙарҙы <em>метадата ғына булған коллекцияларҙа</em> яҙмалар табыу өсөн ҡуллана алабыҙ, мәҫәлән, OpenLibrary, ISBNdb, йәки WorldCat/OCLC. Беҙҙең эҙләү системаһында махсус <em>метадата вкладкаһы</em> бар, әгәр һеҙ был коллекцияларға ҡарарға теләһәгеҙ. Беҙ ярашлы яҙмаларҙы юғалған метадата өлкәләрен тултырыу өсөн ҡулланабыҙ (мәҫәлән, исем юғалған булһа), йәки мәҫәлән, “альтернатив исем” булараҡ (әгәр бар исем булһа)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Китаптың метадатаһы ҡайҙан килгәнен теүәл күреү өсөн, китап битендә <em>“Техник детальдар” вкладкаһын</em> ҡарағыҙ. Унда был китап өсөн сығанаҡ JSON-ға һылтанма бар, төп яҙмаларҙың сығанаҡ JSON-дарына күрһәткестәр менән."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Тулыраҡ мәғлүмәт өсөн, түбәндәге биттәрҙе ҡарағыҙ: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, һәм <a %(a_example)s>Example metadata JSON</a>. Ниһайәт, бөтә метадатабыҙҙы <a %(a_generated)s>генерацияларға</a> йәки <a %(a_downloaded)s>күсереп алырға</a> мөмкин ElasticSearch һәм MariaDB базалары булараҡ."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library бәйләнеше"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Шулай итеп, әгәр һеҙ насар метадата менән файл тапһағыҙ, уны нисек төҙәтергә кәрәк? Һеҙ сығанаҡ китапханаға барып, уның метадата төҙәтеү процедураларын үтәй алаһығыҙ, әммә файл бер нисә сығанаҡ китапханала булһа, нимә эшләргә?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Аннаның Архивында бер идентификатор махсус ҡарала. <strong>Open Library-ҙағы annas_archive md5 өлкәһе һәр ваҡыт башҡа бөтә метадатаны уҙып китә!</strong> Башта бер аҙ артҡа ҡайтайыҡ һәм Open Library тураһында өйрәнәйек."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library 2006 йылда Аарон Шварц тарафынан “башҡа баҫылған һәр китап өсөн бер веб-бит” маҡсаты менән нигеҙләнгән. Был китап метадатаһы өсөн Википедия кеүек: һәр кем уны мөхәррирләй ала, ул ирекле лицензияланған, һәм күсереп алырға мөмкин. Был беҙҙең миссияға иң ярашлы китап базаһы — ысынлап та, Аннаның Архивы Аарон Шварцтың күренеше һәм тормошо менән илһамланған."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Тәгәрмәс уйлап сығарыу урынына, беҙ ирекмәндәребеҙҙе Open Library-ға йүнәлтергә ҡарар иттек. Әгәр һеҙ дөрөҫ булмаған метадата менән китап күрһәгеҙ, түбәндәге юл менән ярҙам итә алаһығыҙ:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " <a %(a_openlib)s>Open Library веб-сайтына</a> барығыҙ."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Дөрөҫ китап яҙмаһын табығыҙ. <strong>ИҒТИБАР:</strong> дөрөҫ <strong>баҫманы</strong> һайлағанығыҙға инанығыҙ. Open Library-ҙа, һеҙҙә “эштәр” һәм “баҫмалар” бар."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "“Эш” “Гарри Поттер һәм Философ Ташы” булыуы мөмкин."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "“Баҫма” булыуы мөмкин:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997 йылда Bloomsbery нәшриәтендә 256 битле тәүге баҫма."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003 йылда Raincoast Books нәшриәтендә 223 битле йомшаҡ тышлы баҫма."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000 йылда Media Rodzina нәшриәтендә 328 битле полякса тәржемә “Harry Potter I Kamie Filozoficzn”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Был баҫмаларҙың барыһының да ISBN-дары һәм эстәлектәре төрлө, шуға күрә дөрөҫөн һайлағыҙ!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Яҙманы төҙәтегеҙ (йәки булмаһа, яңынан булдырығыҙ) һәм мөмкин тиклем күберәк файҙалы мәғлүмәт өҫтәгеҙ! Һеҙ бында булғас, яҙманы ысынлап та иҫ киткес итеп эшләгеҙ."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "“ID Numbers” бүлегендә “Anna’s Archive” һайлағыҙ һәм Anna’s Archive сайтынан китаптың MD5 өҫтәгеҙ. Был URL-дағы “/md5/” һүҙенән һуң килгән оҙон хәрефтәр һәм һандар теҙмәһе."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Anna’s Archive сайтында был яҙмаға тап килгән башҡа файлдарҙы табырға тырышығыҙ һәм уларҙы ла өҫтәгеҙ. Киләсәктә беҙ уларҙы Anna’s Archive эҙләү битендә дубликаттар итеп төркөмләй аласаҡбыҙ."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Эшегеҙҙе тамамлағас, яңыртҡан URL-ды яҙып ҡуйығыҙ. Anna’s Archive MD5-тары менән кәм тигәндә 30 яҙманы яңыртҡас, беҙгә <a %(a_contact)s>электрон хат</a> ебәрегеҙ һәм исемлекте ебәрегеҙ. Беҙ һеҙгә Anna’s Archive өсөн бушлай ағзалыҡ бирәсәкбеҙ, был эште еңелерәк башҡара алһын өсөн (һәм ярҙамығыҙ өсөн рәхмәт йөҙөнән). Былар юғары сифатлы төҙәтмәләр булырға тейеш, күп мәғлүмәт өҫтәгән, юғиһә һеҙҙең һорауығыҙ кире ҡағыласаҡ. Һорауығыҙ шулай уҡ Open Library модераторҙары тарафынан төҙәтмәләр кире ҡағылһа йәки төҙәтелһә кире ҡағыласаҡ."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Был китаптар өсөн генә эшләй, академик мәҡәләләр йәки башҡа төр файлдар өсөн түгел. Башҡа төр файлдар өсөн сығанаҡ китапхананы табырға тәҡдим итәбеҙ. Anna’s Archive үҙгәрештәрҙе индереү өсөн бер нисә аҙна талап ителеүе мөмкин, сөнки беҙгә һуңғы Open Library мәғлүмәт базаһын күсереп алырға һәм эҙләү индексыбыҙҙы яңынан булдырырға кәрәк."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Көҙгөләр: ирекмәндәргә саҡырыу"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Anna’s Archive ныҡлығын арттырыу өсөн, беҙ көҙгөләр эшләтеп ебәреү өсөн ирекмәндәр эҙләйбеҙ."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Мы ищем следующее:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Вы запускаете открытый исходный код Anna’s Archive и регулярно обновляете как код, так и данные."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Ваша версия четко обозначена как зеркало, например, «Архив Боба, зеркало Anna’s Archive»."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Вы готовы принять на себя риски, связанные с этой работой, которые значительны. У вас есть глубокое понимание необходимой операционной безопасности. Содержание <a %(a_shadow)s>этих</a> <a %(a_pirate)s>постов</a> очевидно для вас."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Вы готовы внести свой вклад в наш <a %(a_codebase)s>исходный код</a> — в сотрудничестве с нашей командой — чтобы это произошло."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Сначала мы не дадим вам доступ к загрузкам с серверов партнеров, но если все пойдет хорошо, мы можем поделиться этим с вами."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Расходы на хостинг"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Мы готовы покрыть расходы на хостинг и VPN, первоначально до $200 в месяц. Этого достаточно для базового поискового сервера и прокси, защищенного DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Мы будем оплачивать хостинг только после того, как вы все настроите и продемонстрируете, что можете поддерживать архив в актуальном состоянии с обновлениями. Это означает, что вам придется оплачивать первые 1-2 месяца из своего кармана."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Ваше время не будет компенсировано (и наше тоже), так как это чисто волонтерская работа."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Әгәр ҙә һеҙ беҙҙең эшмәкәрлеккә һәм операцияларға ныҡлап йәлеп ителһәгеҙ, беҙ һеҙҙең менән күберәк иғәнә килемдәрен бүлешеү тураһында һөйләшә алабыҙ, кәрәк булғанда ҡулланыу өсөн."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Башлау"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Зинһар, <strong>беҙҙең менән бәйләнешкә инмәгеҙ</strong> рөхсәт һорау йәки ябай һорауҙар өсөн. Эш һүҙҙәрҙән көслөрәк! Бөтә мәғлүмәт бар, шуға күрә көҙгөңдө ҡуйыу менән шөғөлләнегеҙ."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Проблемаларға осрағанда, беҙҙең Gitlab-ҡа тикшереү йәки берләштереү һорауҙарын ебәреүҙән тартынмағыҙ. Беҙ һеҙҙең менән бергә ҡайһы бер көҙгөгә хас функцияларҙы төҙөргә кәрәк булыуы мөмкин, мәҫәлән, “Аннаның Архивы”н һеҙҙең сайт исеменә үҙгәртеү, (башта) ҡулланыусы иҫәп яҙмаларын һүндереү йәки китап биттәренән төп сайтыбыҙға һылтанма ҡуйыу."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Көҙгөңөҙ эшләп киткәс, беҙгә хәбәр итегеҙ. Беҙ һеҙҙең OpSec-ты тикшерергә яратабыҙ, һәм ул ныҡлы булғас, һеҙҙең көҙгөгә һылтанма ҡуйырбыҙ һәм һеҙҙең менән тығыҙыраҡ эш башларбыҙ."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Бындай ысул менән ярҙам итергә теләгән һәр кемгә алдан уҡ рәхмәт! Был йөрәкһеҙҙәр өсөн түгел, әммә был кешелек тарихындағы иң ҙур ысын асыҡ китапхананың оҙайлығын нығытыр ине."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Партнер сайтынан күсереп алыу"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Аҡрын күсереп алыуҙар тик рәсми сайт аша ғына мөмкин. %(websites)s сайтына барығыҙ."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Аҡрын күсереп алыуҙар Cloudflare VPN-дары йәки Cloudflare IP-адрестары аша мөмкин түгел."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Зинһар, был файлды күсереп алыу өсөн <span %(span_countdown)s>%(wait_seconds)s</span> секунд көтөгөҙ."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Күсереп алыу өсөн түбәндәге URL-ды ҡулланығыҙ: <a %(a_download)s>Хәҙер күсереп алырға</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Көтөп торғанығыҙ өсөн рәхмәт, был сайтты бөтә кешегә бушлай ҡулланыу мөмкинлеген бирә! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Иғтибар: Һуңғы 24 сәғәттә һеҙҙең IP-адрестан күп күсереп алыуҙар булған. Күсереп алыуҙар ғәҙәттәгегә ҡарағанда әкренерәк булыуы мөмкин."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Һуңғы 24 сәғәттә һеҙҙең IP-адрестан күсереп алыуҙар: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Әгәр VPN, уртаҡ интернет тоташтырыу, йәки провайдер IP-адрестарҙы уртаҡлашһа, был иҫкәртеү шуға бәйле булыуы мөмкин."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Файлдарҙы бушлай күсереп алыу мөмкинлеген һәр кемгә биреү өсөн, был файлды күсереп алыр алдынан көтөргә кәрәк."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Көтөп торғанда, Аннаның Архивын башҡа вкладкала ҡарай алаһығыҙ (әгәр браузерығыҙ фон вкладкаларын яңыртыуҙы хуплаһа)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Бер үк ваҡытта бер нисә күсереп алыу битен көтөргә мөмкин (әммә бер серверҙан бер үк ваҡытта тик бер файлды ғына күсереп алығыҙ)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Күсереп алыу һылтанмаһы бер нисә сәғәткә ғәмәлдә була."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Аннаның Архивы"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Аннаның Архивында яҙма"

#, fuzzy
msgid "page.scidb.download"
msgstr "Йөкләп алыу"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Кешелек белеменең ҡулланыу мөмкинлеген һәм оҙайлы һаҡланышын тәьмин итеү өсөн, <a %(a_donate)s>ағза</a> булығыҙ."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Бонус булараҡ, 🧬&nbsp;SciDB ағзалар өсөн тиҙерәк йөкләнә, сикләүҙәрһеҙ."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Эшләмәйме? <a %(a_refresh)s>Яңыртыу</a>ҙы һынап ҡарағыҙ."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Әлегә ҡарау мөмкинлеге юҡ. Файлды <a %(a_path)s>Аннаның Архивынан</a> йөкләп алығыҙ."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB - Sci-Hub дауамы, таныш интерфейс һәм PDF-тарҙы тура ҡарау мөмкинлеге менән. DOI-ҙы индереп ҡарағыҙ."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Беҙҙә Sci-Hub коллекцияһының тулыһы бар, шулай уҡ яңы мәҡәләләр ҙә. Күпселеген таныш интерфейс менән тура ҡарарға мөмкин, Sci-Hub кеүек. Ҡайһы берҙәрен тышҡы сығанаҡтар аша йөкләргә мөмкин, был осраҡта беҙ уларға һылтанмалар күрһәтәбеҙ."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Эҙләү"

#, fuzzy
msgid "page.search.title.new"
msgstr "Яңы эҙләү"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Тик индерегеҙ"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Сығарып ташлағыҙ"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Тикшерелмәгән"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Йөкләп алыу"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Журнал мәҡәләләре"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Цифрлы ҡуртым"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Метадата"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Исем, автор, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Эҙләү"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Эзләү көйләүләре"

#, fuzzy
msgid "page.search.submit"
msgstr "Эзләү"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Эзләү озакка сузылды, бу киң сораулар өчен гадәти хәл. Фильтр саннары төгәл булмаска мөмкин."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Эзләү озакка сузылды, бу төгәл булмаган нәтиҗәләр күрсәтергә мөмкин. Кайвакыт <a %(a_reload)s>битне яңарту</a> ярдәм итә."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Күрһәтеү"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Исемлек"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Таблица"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Алга киткән"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Тасуирламалар һәм метадата аңлатмаларын эҙләү"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Конкрет эзләү кырын өстәү"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(конкрет эзләү кыры)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Нәшер ителү елы"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Эстәлек"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Файл төрө"

#, fuzzy
msgid "page.search.more"
msgstr "тағы ла…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Керү"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Чыганак"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "AA тарафынан йыйылған һәм асыҡ сығанаҡлы"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Тел"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Тәртип буенча"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Иң мөһиме"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Иң яңалары"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(нәшер ителү елы)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Иң искеләре"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Иң зурлары"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(файл зурлыгы)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Иң кечкенәләре"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(ачык чыганаклы)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Осраҡлы"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Эҙләү индексы ай һайын яңыртыла. Әлеге ваҡытта ул %(last_data_refresh_date)s тиклем яҙмаларҙы үҙ эсенә ала. Техник мәғлүмәт өсөн, %(link_open_tag)sDatasets битенә</a> ҡарағыҙ."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Кодтар буйынса эҙләү индексын өйрәнеү өсөн <a %(a_href)s>Кодтарҙы өйрәнеүсе</a> ҡулланығыҙ."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Тура эҙләү өсөн, беҙҙең каталогтағы %(count)s тура йөкләп алыу файлдарын эҙләү өсөн, ҡумтаға яҙығыҙ, уларҙы беҙ <a %(a_preserve)s>мәңге һаҡлайбыҙ</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Ысынында, һәр кем был файлдарҙы һаҡлауға ярҙам итә ала, беҙҙең <a %(a_torrents)s>берләштерелгән торренттар исемлеген</a> таратып."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Беҙҙә әлеге ваҡытта донъялағы иң тулы асыҡ китаптар, мәҡәләләр һәм башҡа яҙма эштәр каталогы бар. Беҙ Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>һәм башҡаларҙы</a> күсерәбеҙ."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Әгәр һеҙ башҡа “күңел күләгәһендәге китапханаларҙы” табаһығыҙ икән, уларҙы күсерергә кәрәк тип иҫәпләйһегеҙ, йәки ниндәй ҙә булһа һорауҙарығыҙ бар икән, беҙгә %(email)s адресы буйынса мөрәжәғәт итегеҙ."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "DMCA / авторлыҡ хоҡуҡтары дәғүәләре өсөн <a %(a_copyright)s>бында баҫығыҙ</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Киңәш: тиҙ навигация өсөн “/” (эҙләүгә баҫым), “enter” (эҙләү), “j” (өҫкә), “k” (аҫҡа), “<” (алдағы бит), “>” (киләһе бит) клавиатура ҡыҫҡа юлдарын ҡулланығыҙ."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Мәҡәләләр эҙләйһегеҙме?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Тура эҙләү өсөн, беҙҙең каталогтағы %(count)s академик мәҡәләләр һәм журнал мәҡәләләрен эҙләү өсөн, ҡумтаға яҙығыҙ, уларҙы беҙ <a %(a_preserve)s>мәңге һаҡлайбыҙ</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Цифрлы китапханаларҙағы файлдарҙы эҙләү өсөн, ҡумтаға яҙығыҙ."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Был эҙләү индексы әлеге ваҡытта Internet Archive’s Controlled Digital Lending китапханаһынан метаданные үҙ эсенә ала. <a %(a_datasets)s>Беҙҙең Datasets тураһында күберәк</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Күберәк цифрлы китапханалар өсөн, <a %(a_wikipedia)s>Википедия</a> һәм <a %(a_mobileread)s>MobileRead Wiki</a> ҡарағыҙ."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Китапханаларҙан метаданные эҙләү өсөн, ҡумтаға яҙығыҙ. Был <a %(a_request)s>файл һорағанда</a> файҙалы булыуы мөмкин."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Был эҙләү индексы әлеге ваҡытта төрлө метаданные сығанаҡтарынан метаданные үҙ эсенә ала. <a %(a_datasets)s>Беҙҙең Datasets тураһында күберәк</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Метаданные өсөн, беҙ оригиналь яҙмаларҙы күрһәтәбез. Беҙ яҙмаларҙы берләштермәйбеҙ."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Донъяла яҙма эштәр өсөн бик күп, бик күп метаданные сығанаҡтары бар. <a %(a_wikipedia)s>Был Википедия бит</a> яҡшы башланғыс, ләкин әгәр һеҙ башҡа яҡшы исемлектәрҙе беләһегеҙ икән, беҙгә хәбәр итегеҙ."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Эҙләү өсөн, ҡумтаға яҙығыҙ."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Былар метадата яҙмалары, <span %(classname)s>йөкләп алырлыҡ файлдар түгел</span>."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Эҙләү ваҡытында хата."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "<a %(a_reload)s>Битте яңынан загрузить итегеҙ</a>. Проблема дауам итһә, беҙгә %(email)s адресы буйынса электрон почта ебәрегеҙ."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Файлдар табылманы.</span> Һүҙҙәрҙе йәки фильтрҙарҙы үҙгәртеп ҡарағыҙ."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Иногда это происходит некорректно, когда сервер поиска медленный. В таких случаях <a %(a_attrs)s>перезагрузка</a> может помочь."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Беҙ тапҡан тура килеүҙәр: %(in)s. <a %(a_request)s>Файл һорағанда</a> ундағы URL-ды ҡуллана алаһығыҙ."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Журнал мәҡәләләре (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Цифрлы ҡуртым (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Метадата (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Нәтижәләр %(from)s-%(to)s (%(total)s барлығы)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ өлөшләтә тура килеүҙәр"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d өлөшләтә тура килеүҙәр"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Ирекмәнлек һәм Бүләктәр"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Аннаның Архивы һеҙҙең кеүек ирекмәндәргә таяна. Беҙ бөтә йөкләмәләр кимәлен ҡабул итәбеҙ һәм беҙгә ярҙам кәрәк булған ике төп категория бар:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Еңел ирекмәнлек эше:</span> әгәр һеҙ бер нисә сәғәт кенә бүлә алаһығыҙ икән, барыбер ярҙам итә алырлыҡ күп ысулдар бар. Беҙ даими ирекмәндәрҙе <span %(bold)s>🤝 Аннаның Архивы ағзалығы менән бүләкләйбеҙ</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Тяжёлая волонтёрская работа (вознаграждения от 50 до 5000 долларов США):</span> если вы можете посвятить много времени и/или ресурсов нашей миссии, мы будем рады работать с вами более тесно. В конечном итоге вы можете присоединиться к внутренней команде. Хотя у нас ограниченный бюджет, мы можем награждать <span %(bold)s>💰 денежными вознаграждениями</span> за самую интенсивную работу."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Если вы не можете посвятить своё время волонтёрству, вы всё равно можете нам очень помочь, <a %(a_donate)s>пожертвовав деньги</a>, <a %(a_torrents)s>раздавая наши торренты</a>, <a %(a_uploading)s>загружая книги</a> или <a %(a_help)s>рассказывая друзьям о Архиве Анны</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Компании:</span> мы предлагаем высокоскоростной прямой доступ к нашим коллекциям в обмен на корпоративные пожертвования или обмен на новые коллекции (например, новые сканы, OCR’ированные наборы данных, обогащение наших данных). <a %(a_contact)s>Свяжитесь с нами</a>, если это про вас. Также смотрите нашу <a %(a_llm)s>страницу LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Лёгкое волонтёрство"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Если у вас есть несколько свободных часов, вы можете помочь различными способами. Обязательно присоединяйтесь к <a %(a_telegram)s>чату волонтёров в Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "В знак признательности мы обычно даём 6 месяцев статуса «Удачливый библиотекарь» за базовые достижения и больше за продолжительную волонтёрскую работу. Все достижения требуют высококачественной работы — небрежная работа вредит нам больше, чем помогает, и мы её отклоним. Пожалуйста, <a %(a_contact)s>напишите нам по электронной почте</a>, когда достигнете какого-либо рубежа."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Задача"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Этап"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Аннаның Архивы тураһында һүҙ таратыу. Мәҫәлән, AA-ла китаптар тәҡдим итеү, блог яҙмаларыбыҙға һылтанмалар биреү йәки дөйөм алғанда кешеләрҙе беҙҙең веб-сайтҡа йүнәлтеү."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s һылтанмалар йәки скриншоттар."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Былар һеҙҙең Аннаның Архивы тураһында кемгәлер хәбәр итеүегеҙҙе һәм уларҙың һеҙгә рәхмәт әйтеүен күрһәтергә тейеш."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Улучшение метаданных путём <a %(a_metadata)s>связывания</a> с Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Башланғыс нөктә итеп <a %(a_list)s >осраҡлы metadata мәсьәләләре исемлеген</a> ҡуллана алаһығыҙ."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Төҙәткән мәсьәләләрегеҙгә аңлатма ҡалдырырға онотмағыҙ, башҡалар һеҙҙең эшегеҙҙе ҡабатламаһын өсөн."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s яҡшыртылған яҙмаларҙың һылтанмалары."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Перевод</a> сайта."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Полный перевод языка (если он не был близок к завершению)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Улучшение страницы Википедии о Архиве Анны на вашем языке. Включите информацию со страницы Википедии AA на других языках, а также с нашего сайта и блога. Добавьте ссылки на AA на других релевантных страницах."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Ссылка на историю правок, показывающая, что вы внесли значительные изменения."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Выполнение запросов на книги (или статьи и т.д.) на форумах Z-Library или Library Genesis. У нас нет собственной системы запросов на книги, но мы зеркалим эти библиотеки, поэтому улучшение их делает лучше и Архив Анны."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s һылынған һорауҙарҙың һылтанмалары йәки скриншоттары."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Маленькие задачи, размещённые в нашем <a %(a_telegram)s>чате волонтёров в Telegram</a>. Обычно для членства, иногда для небольших вознаграждений."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Ирекмәндәр чаты төркөмөндәге бәләкәй бурыстар."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Зависит от задачи."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Бүләктәр"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Беҙ һәр ваҡыт ныҡлы программалау йәки һөжүм итеү хәүефһеҙлеге күнекмәләре булған кешеләрҙе йәлеп итергә тырышабыҙ. Һеҙ кешелек мираҫын һаҡлауға етди өлөш индерә алаһығыҙ."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Рәхмәт йөҙөнән, беҙ ныҡлы өлөш индереүселәргә ағзалыҡ бирәбеҙ. Ҙур рәхмәт йөҙөнән, айырыуса мөһим һәм ҡатмарлы бурыстар өсөн аҡсалата бүләктәр бирәбеҙ. Был эш урынына алмаш булараҡ ҡаралырға тейеш түгел, әммә был өҫтәмә стимул һәм сығымдарҙы ҡаплауға ярҙам итә ала."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Күпселек кодыбыҙ асыҡ сығанаҡлы, һәм бүләк биргәндә һеҙҙең кодығыҙҙың да шулай булыуын һорайбыҙ. Айырым осраҡтар бар, уларҙы шәхсән тикшереп була."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Бүләктәр бурысты беренсе булып тамамлаған кешегә бирелә. Бүләк билетына аңлатма яҙығыҙ, башҡалар һеҙҙең нимә өҫтөндә эшләгәнегеҙҙе белһен, һәм башҡалар туҡтап торһон йәки һеҙгә ҡушылһын. Әммә башҡаларҙың да эшләргә ирекле булыуын һәм һеҙҙе уҙып китергә тырышыуын иҫегеҙҙә тотоғоҙ. Әммә беҙ насар эш өсөн бүләктәр бирмәйбеҙ. Әгәр ике юғары сифатлы эш бер-бер артлы (бер-ике көн эсендә) тапшырылһа, беҙ үҙ ҡарамағыбыҙ буйынса икеһенә лә бүләк бирергә мөмкинбеҙ, мәҫәлән, беренсе эш өсөн 100%%, икенсе эш өсөн 50%% (йәмғеһе 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Ҙур бүләктәр өсөн (айырыуса мәғлүмәт йыйыу бүләктәре), эштең ~5%% тамамлағас, һәм ысулығыҙҙың тулы этапҡа яраҡлы булыуына ышанғас, беҙгә мөрәжәғәт итегеҙ. Ысулығыҙҙы беҙгә күрһәтергә тура киләсәк, беҙ фекер алышыр өсөн. Шулай уҡ, бер нисә кеше бүләккә яҡынлашһа, беҙ нимә эшләргә ҡарар итә алабыҙ, мәҫәлән, бер нисә кешегә бүләк биреү, кешеләрҙе берләшергә дәртләндереү һ.б."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ИҒТИБАР: юғары бүләкле бурыстар <span %(bold)s>ауыр</span> — башта еңелерәк бурыстарҙан башлау аҡыллы булыр."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Беҙҙең <a %(a_gitlab)s>Gitlab мәсьәләләр исемлегенә</a> күсегеҙ һәм “Мөһимлек ярлығы” буйынса сортлағыҙ. Был беҙгә мөһим булған бурыстарҙың тәртибен яҡынса күрһәтә. Асыҡ бүләктәре булмаған бурыстар ҙа ағзалыҡҡа яраҡлы, айырыуса “Ҡабул ителгән” һәм “Аннаның яратҡан” тип билдәләнгәндәр. “Башланғыс проект” менән башларға теләүегеҙ мөмкин."

#, fuzzy
msgid "blog.template.subheading"
msgstr "<a %(wikipedia_annas_archive)s>Аннаның Архивы</a> тураһында яңыртыуҙар, кешелек тарихындағы иң ҙур ысын асыҡ китапхана."

#, fuzzy
msgid "layout.index.title"
msgstr "Аннаның Архивы"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Донъяның иң ҙур асыҡ сығанаҡлы асыҡ мәғлүмәт китапханаһы. Sci-Hub, Library Genesis, Z-Library һәм башҡаларҙың көҙгөләре."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Аннаның Архивында эҙләү"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Аннаның Архивына һеҙҙең ярҙам кәрәк!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Күптәр беҙҙе юҡ итергә тырыша, ләкин беҙ ҡаршы торабыҙ."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Әгәр ҙә һеҙ хәҙер иғәнә индерһәгеҙ, һеҙ <strong>ике тапҡыр</strong> күберәк тиҙ йөкләүҙәр алаһығыҙ."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Был ай аҙағына тиклем ғәмәлдә."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Иғәнә"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Кешелек белемдәрен һаҡлау: ҙур байрам бүләге!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Яҡынығыҙҙы ғәжәпләндерегеҙ, уларға ағзалыҡ менән аккаунт бүләк итегеҙ."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Аннаның Архивының тотороҡлоғон арттырыу өсөн, беҙ көҙгөләр эшләтеү өсөн ирекмәндәр эҙләйбеҙ."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Мөхәббәт көнөнә иң яҡшы бүләк!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Беҙҙә яңы иғәнә яһау ысулы бар: %(method_name)s. Зинһар, %(donate_link_open_tag)sиғәнә яһағыҙ</a> — был сайтты эшләтеп ебәреү арзан түгел, һәм һеҙҙең иғәнә ысынлап та ҙур роль уйнай. Бик ҙур рәхмәт."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Беҙ донъялағы иң ҙур комикстар күләгә китапханаһын <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">резервлау</a> өсөн аҡса йыйыу кампанияһы үткәрәбеҙ. Ярҙамығыҙ өсөн рәхмәт! <a href=\"/donate\">Иғәнә яһағыҙ.</a> Әгәр ҙә иғәнә яһай алмаһағыҙ, дуҫтарығыҙға һөйләп һәм <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> йәки <a href=\"https://t.me/annasarchiveorg\">Telegram</a> аша беҙҙе күҙәтеп ярҙам итә алаһығыҙ."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Һуңғы күсереп алыуҙар:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Эҙләү"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Йыш бирелгән һорауҙар"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Метадата яҡшыртыу"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Ирекмәнлек һәм бүләктәр"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Торренттар"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Әүҙемлек"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Кодтар тикшереүсеһе"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM мәғлүмәттәре"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Өй"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Аннаның программаһы ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Тәржемә ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Керергә / Теркәлергә"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Аккаунт"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Аннаның архивы"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Бәйләнештә ҡалығыҙ"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / авторлыҡ хоҡуҡтары дәғүәләре"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Алдынғы"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Хәүефһеҙлек"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Альтернативалар"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "бәйһеҙ"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Был файлда проблемалар булыуы ихтимал."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Тиҙ күсереп алыу"

#, fuzzy
msgid "page.donate.copy"
msgstr "күсереп алыу"

#, fuzzy
msgid "page.donate.copied"
msgstr "күсерелде!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Алдағы"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Киләһе"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "тик был айҙа!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub яңы мәҡәләләрҙе йөкләүҙе <a %(a_closed)s>туҡтатты</a>."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Түләү вариантын һайлағыҙ. Беҙ крипто-түләүҙәр өсөн ташламалар бирәбеҙ %(bitcoin_icon)s, сөнки беҙгә (бик) аҙ комиссиялар килә."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Түләү вариантын һайлағыҙ. Әлеге ваҡытта беҙҙә тик крипто-түләүҙәр генә бар %(bitcoin_icon)s, сөнки традицион түләү процессорҙары беҙ менән эшләүҙән баш тарталар."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Беҙ кредит/дебет карталарын туранан-тура хуплай алмайбыҙ, сөнки банктар беҙ менән эшләргә теләмәй. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Шулай ҙа, беҙҙең башҡа түләү ысулдарын ҡулланып, кредит/дебет карталарын ҡулланыуҙың бер нисә ысулы бар:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Аҡрын һәм тышҡы тейәүҙәр"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Тейәүҙәр"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Әгәр һеҙ криптовалютаны тәүге тапҡыр ҡулланаһығыҙ икән, Bitcoin (иң тәүге һәм иң киң ҡулланылған криптовалюта) һатып алып иғәнә яһау өсөн %(option1)s, %(option2)s, йәки %(option3)s ҡулланыуҙы тәҡдим итәбеҙ."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 ссылок на записи, которые вы улучшили."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 ссылок или скриншотов."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 ссылок или скриншотов выполненных запросов."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Әгәр һеҙ был datasets-ты <a %(a_faq)s>архивлау</a> йәки <a %(a_llm)s>LLM уҡытыу</a> маҡсатында күсерергә теләһәгеҙ, беҙгә мөрәжәғәт итегеҙ."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Әгәр һеҙ был мәғлүмәттәр йыйылмаһын <a %(a_archival)s>архивлау</a> йәки <a %(a_llm)s>LLM уҡытыу</a> маҡсатында күсерергә теләһәгеҙ, беҙгә мөрәжәғәт итегеҙ."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Төп веб-сайт"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN ил мәғлүмәттәре"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Әгәр һеҙ был мәғлүмәттәр йыйылмаһын <a %(a_archival)s>архивлау</a> йәки <a %(a_llm)s>LLM уҡытыу</a> маҡсатында күсереп алырға теләһәгеҙ, беҙгә мөрәжәғәт итегеҙ."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Халыҡ-ара ISBN агентлығы даими рәүештә милли ISBN агентлыҡтарына бүленгән диапазондарҙы сығара. Бынан беҙ был ISBN ниндәй илгә, төбәккә йәки тел төркөмөнә ҡарағанын сығара алабыҙ. Әлеге ваҡытта беҙ был мәғлүмәттәрҙе <a %(a_isbnlib)s>isbnlib</a> Python китапханаһы аша ҡулланабыҙ."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ресурстар"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Һуңғы яңыртыу: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN веб-сайты"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Метадата"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "“scimag”-ты иҫәпкә алмай"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Метадата йыйыу өсөн беҙҙең илһам сығанағы Аарон Свартцтың “бер ваҡытта ла баҫылған һәр китап өсөн бер веб-бит” маҡсаты булды, ул <a %(a_openlib)s>Open Library</a> булдырҙы."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ул проект яҡшы эшләне, ләкин беҙҙең уникаль позициябыҙ улар ала алмаған метадатаны алырға мөмкинлек бирә."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Тағы бер илһам сығанағы беҙҙең <a %(a_blog)s>донъяла нисә китап барлығын</a> белеү теләге булды, шуның өсөн беҙ һаҡлап ҡалырға тейеш китаптар һанын иҫәпләй алабыҙ."

#~ msgid "page.partner_download.text1"
#~ msgstr "Бөтә кешегә файлдарҙы бушлай күсереп алыу мөмкинлеген биреү өсөн, был файлды күсереп алыр алдынан <strong>%(wait_seconds)s секунд</strong> көтөргә кәрәк."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Битте автоматик яңыртыу. Әгәр һеҙ йөкләү тәҙрәһен үткәреп ебәрһәгеҙ, таймер яңынан башланасаҡ, шуға күрә автоматик яңыртыу тәҡдим ителә."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Хәҙер күсереп алырға"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Конвертациялау: форматтар араһында конвертациялау өсөн онлайн ҡоралдар ҡулланығыҙ. Мәҫәлән, epub һәм pdf араһында конвертациялау өсөн <a %(a_cloudconvert)s>CloudConvert</a> ҡулланығыҙ."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: файлды (pdf йәки epub форматтары яраҡлы) тейәгеҙ, һуңынан <a %(a_kindle)s>Kindle-ға ебәрегеҙ</a> веб, ҡушымта йәки электрон почта аша. Файҙалы ҡоралдар: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Авторҙарҙы хуплағыҙ: Әгәр был һеҙгә оҡшай һәм мөмкинлегегеҙ бар икән, оригиналды һатып алыуҙы йәки авторҙарҙы туранан-тура хуплауҙы уйлағыҙ."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Китапханаларҙы хуплағыҙ: Әгәр был һеҙҙең урындағы китапханала бар икән, уны шунда бушлай алып тороуҙы уйлағыҙ."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Тура күмәртәләп булмай, тик түләүле стена артында ярым күмәртәләп"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Аннаның Архивы <a %(isbndb)s>ISBNdb метадатаһын</a> идара итә"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb — это компания, которая собирает метаданные ISBN из различных онлайн-книжных магазинов. Архив Анны делает резервные копии метаданных книг ISBNdb. Эти метаданные доступны через Архив Анны (хотя в настоящее время не в поиске, за исключением случаев, когда вы явно ищете номер ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Для технических деталей см. ниже. В какой-то момент мы можем использовать это для определения, какие книги все еще отсутствуют в теневых библиотеках, чтобы приоритетно найти и/или отсканировать их."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Наш блог-пост об этих данных"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Сбор данных ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "В настоящее время у нас есть один торрент, который содержит 4,4 ГБ сжатый файл <a %(a_jsonl)s>JSON Lines</a> (20 ГБ в распакованном виде): «isbndb_2022_09.jsonl.gz». Чтобы импортировать файл «.jsonl» в PostgreSQL, вы можете использовать что-то вроде <a %(a_script)s>этого скрипта</a>. Вы даже можете передать его напрямую, используя что-то вроде %(example_code)s, чтобы он распаковывался на лету."

#~ msgid "page.donate.wait"
#~ msgstr "Пожалуйста, подождите как минимум <span %(span_hours)s>два часа</span> (и обновите эту страницу) перед тем, как связаться с нами."

#~ msgid "page.codes.search_archive"
#~ msgstr "“%(term)s” өсөн Аннаның Архивында эҙләү"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Alipay йәки WeChat ҡулланып бүләк итегеҙ. Киләһе биттә былар араһынан һайлай алаһығыҙ."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Распространение информации о Архиве Анны в социальных сетях и онлайн-форумах, рекомендация книг или списков на AA, или ответы на вопросы."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Фикшн коллекцияһы айырылып киткән, ләкин <a %(libgenli)s>торрендар</a> бар, 2022 йылдан яңыртылмаған (тура күсереп алыуҙар бар)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Аннаның Архивы һәм Libgen.li комикстар һәм журналдар коллекцияларын берлектә идара итә."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Русса фикшн һәм стандарт документтар коллекциялары өсөн торрендар юҡ."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Өҫтәмә контент өсөн торренттар юҡ. Libgen.li сайтындағы торренттар бында күрһәтелгән башҡа торренттарҙың көҙгөһө булып тора. Берҙән-бер исключение - %(fiction_starting_point)s башланған фантастика торренттары. Комикстар һәм журналдар торренттары Anna’s Archive һәм Libgen.li араһында хеҙмәттәшлек һөҙөмтәһендә сығарыла."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "<a %(a_href)s><q>Александрия китапханаһы,</q></a> коллекцияһынан, аныҡ сығанағы билдәһеҙ. Бер өлөшө the-eye.eu сайтынан, бер өлөшө башҡа сығанаҡтарҙан."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

