#, fuzzy
msgid "layout.index.invalid_request"
msgstr "D<PERSON>e nén valide. Allez vey %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " et "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "et pus"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nos mirons %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Nos scrapons et mettons a l'ovrî %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Tos nos codes et dnéyes sont totavå open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Li pus grande librêye totavå ovèrte dins l'histwere di l'umanité."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;livres, %(paper_count)s&nbsp;papîs — préservés po todi."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Li pus grande librêye totavå ovèrte et open source do monde. ⭐️&nbsp;Mirons Sci-Hub, Library Genesis, Z-Library, et pus. 📈&nbsp;%(book_any)s livres, %(journal_article)s papîs, %(book_comic)s comics, %(magazine)s magazinnes — préservés po todi."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Li pus grande librêye totavå ovèrte et open source do monde.<br>⭐️ Mirons Scihub, Libgen, Zlib, et pus."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Mètadoneye incorecte (p.ex. titro, descripcion, imådje di cwårte)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemes di tèlèchargement (p.ex. nén saveu s'ralyî, messaedje d'érreu, très linde)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Fitchî nén saveu esse drovê (p.ex. fitchî corompu, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Måvaize qualité (p.ex. problames di formåtaedje, måvaize scan, pådjes mankantes)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fitchî dût esse disfacé (p.ex. pub, conteu abusif)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamåcion di droets d'ôteu"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Ôte"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Tèlèchargements bonus"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Magnîfike Minsîre"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Hureusse Bibliotèkêre"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Éclatante Emmagazinneuse"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Étonnante Archiviste"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s totå"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totå"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "nén payî"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "payî"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "anulé"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "expiré"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "waitant l'confirmåcion d'Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "nén valide"

#, fuzzy
msgid "page.donate.title"
msgstr "Donaedje"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Vos avez ene <a %(a_donation)s>donaedje egzistante</a> en cour. Volêz bin finir ou anuler cisse donaedje divant d' fére ene novele donaedje."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Vey tos mes donaedjes</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "L' Archive d' Anna est on projet sins but lucratif, a source ouverte, et a dnêyes ouvertes. En donaedjant et dvintant membre, vos sostindrez nosse fonctinnemint et nosse disvelopmint. Ås tos nosse membres: mêrci po nos fé continou! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Po pus d' informåcions, veyez l' <a %(a_donate)s>FAQ des donaedjes</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Po rçu d' pus d' télechargemints, <a %(a_refer)s>racommandéz vos amis</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Vos rçûrîz %(percentage)s%% télechargemints rapîdes di bonus, pask' vos avoz stî racommandé pa l' uzeu %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Ceci s' apliche a tot l' période di membre."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s télechargemints rapîdes par djoû"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "si vos donez cisse mês!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mês"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Rjondre"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Tchoezi"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "djusqu' a %(percentage)s%% rabais"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Papîs SciDB <strong>ilimités</strong> sins vérificåcion"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Accès a l' <a %(a_api)s>API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Rimpre <strong>%(percentage)s%% télechargemints di bonus</strong> en <a %(a_refer)s>racommandant des amis</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Vosse no d' uzeu ou mention anonyme dins les crèdits"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Avantages précedints, et:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Accès anticipé a des noveas fonctinnalités"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram exclusif avou des mîses a djoûr des coulisses"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adoptez un torrent” : votre nom d'utilisateur ou message dans un nom de fichier torrent <div %(div_months)s>une fois tous les 12 mois d'adhésion</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Statut légendaire dans la préservation des connaissances et de la culture de l'humanité"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Accès Expert"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contactez-nous"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Nous sommes une petite équipe de bénévoles. Cela peut nous prendre 1 à 2 semaines pour répondre."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Accès illimité</strong> à haute vitesse"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Serveurs <strong>SFTP</strong> directs"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Don ou échange de niveau entreprise pour de nouvelles collections (par exemple, nouveaux scans, datasets OCRisés)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Nous accueillons les grandes donations de la part de personnes ou d'institutions fortunées. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pour les dons de plus de 5000 $, veuillez nous contacter directement à %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Faites atencion qu'bin qu'les membresyes so cisse pådje sont \"par mois\", i sont des donåcions a ene seule fwa (nén récurintes). Veyoz l' <a %(faq)s>FAQ des donåcions</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Si vous souhaitez faire un don (de n'importe quel montant) sans adhésion, n'hésitez pas à utiliser cette adresse Monero (XMR) : %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Veuillez sélectionner un mode de paiement."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(provizwèrement nén d' disponibe)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s carte-cadea"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carte di banke (eployant l' app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carte di crèdite/dèbete"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (régulî)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Carte / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Crèdite/dèbète/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Carte di banke"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carte di crèdite/dèbete (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carte di crèdite/dèbete 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Avou des cryptomonnèyes, vos pôroz doner avou BTC, ETH, XMR, et SOL. Eployî cisse ôption si vos-oz ddja l' abitude des cryptomonnèyes."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Avou des cryptomonnèyes, vos pôroz doner avou BTC, ETH, XMR, et d' ôtes."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si vos eployîz des crypto pour l' prumîre feis, nos vos s'gestans d' eployî %(options)s po achtey et doner des Bitcoins (l' crypto-moneye originale et l' pus eployeye)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Po doner avou PayPal US, nos allons eployî PayPal Crypto, k' nos permet d' rister anonyme. Nos vos rmercyans d' prinde l' tins d' aprinde cmint doner avou cisse môde, pask' çoula nos aidêye fort."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Doner avou PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Doner avou Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si vos-oz Cash App, c' est l' môde l' pus åjhe po doner!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Notîz k' po des transactions d' mwins di %(amount)s, Cash App pout prinde ene fee di %(fee)s. Po %(amount)s ou pus, c' est gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donaedje avou Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Si vos avoz Revolut, c' est l' måyire manire di donaedje!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Doner avou ene carte di crèdite ou dèbete."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay et Apple Pay pôront ossu foncionner."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Notîz ki po les ptitès d' onêyes les coûts di carte di crédit pôront eliminer nos %(discount)s%% rabè, don on vos consèyîre des abomints pus longs."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Notîz ki po les ptitès d' onêyes les coûts sont hôtès, don on vos consèyîre des abomints pus longs."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Avou Binance, vos achtez des Bitcoins avou ene carte di crédit/débit ou on compte bancåre, et pi vos donroz ces Bitcoins a nos. D' cisse manîre, nos poumons rister sécures et anonymes tot en rçevant vosse don."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance est dîsponibe dins preske tot lès payis, et siporte lès pus grands banques et cartes di crédit/débit. C' est asteure nosse prumîre consèy. Nos vos rmerçions d' prinde l' tins d' aprinde come doner avou cisse manîre, çou nos aidèye fort."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donaedje avou vosse conte PayPal ranto."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Doner avou ene carte di crédit/débit, PayPal, ou Venmo. Vos pouroz tchoezi dins l' pådje ki vén."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Doner avou ene carte-cadea Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Notîz ki nos dwin ronder a des montants acceptés pa nos revindeus (minimom %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> Nos siportans k' Amazon.com, nén les ôtes setins Amazon. Par egzimpe, .de, .co.uk, .ca, n' sont nén siportés."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANT:</strong> Cisse ôpcion est po %(amazon)s. Si vos voloz eployî on ôte site Amazon, tchôsiz-le divins."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Cisse manîre eploye on founissou di criptomonnêye come on intermédiare po l' convèrsion. Çoula pout esse on pô d' confuz, don n' eployîz cisse manîre k' si les ôtes metodes di payement n' vont nén. Çoula n' va nén dins tos les payis."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Faites ene don eployant ene carte di crédit/débit, avou l' app Alipay (super facile a installer)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installez l' app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installez l' app Alipay dès l' <a %(a_app_store)s>Apple App Store</a> ou l' <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Enregistrez-vos eployant vos numer di téléphone."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nole ôtesse informåcion personåle n' est nésseçaire."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Adjouter ene carte di banke"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Soutnou: Visa, MasterCard, JCB, Diners Club et Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Veyoz <a %(a_alipay)s>ci gide-ci</a> po pus d' informåcion."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nos n' savans nén sopoirtî les crèdits/dèbèts direcminte, paskè les banques n' vout nén cwårti avou nos. ☹ Nénmintins, i gn a sacwants manires d' eployî des crèdits/dèbèts, avou d' ôtes metodes di payement:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Carte-cadeau Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Evoyîz nos des cartes-cadea Amazon.com avou vosse carte di crédit/débit."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay sopoirte les crèdits/dèbèts inteRNåcionås. Veyoz <a %(a_alipay)s>ci sudjet</a> po des infôrmåcions di pus."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) siporte les cartes di crédit/débit internåcionåles. Dins l' app WeChat, alîz a “Me => Services => Wallet => Add a Card”. Si vos n' veyoz nén çoula, enabliyîz l' avou “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Vos pouroz achte des criptomonnêyes avou des cartes di crédit/débit."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Sèrvices crypto èxpress"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Les sèrvices èxpress sont comodes, mins ont des côts pus hôts."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Vos poloz eployer çoula a l' plèce d' ene èchange crypto si vos vlez fé ene don pus grand rapidmint et n' mindez nén ene côtaedje di $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assurez-vos d' evoyî l' montånt crypto èxact montrî so l' pådje di don, nén l' montånt en $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Ôtrumint, l' côtaedje s' ôtera et nos n' polons nén trètî automåtikemint vos mimbreûté."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s suivant l’ payis, nén d’ vérification po l’ prumîre tranzaccion)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, nén d’ vérification po l’ prumîre tranzaccion)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, nén d’ vérification po l’ prumîre tranzaccion)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si cisse informåcion ci est d'valêye, voloz-bén nos evoyî on imél po nos l' fé saveur."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Po les cartes di crédit, cartes di débit, Apple Pay, et Google Pay, nos eployans “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Dins lor sistinme, on “café” est égal a $5, don vosse don serè ronder al pus proche multiple di 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Tchoezix comint longtimps vos voloz vosse abomint."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mês"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 mês"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 mês"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 mês"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 moes"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 moes"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 moes"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>après <span %(span_discount)s></span> rabais</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Ce mode de paiement nécessite un minimum de %(amount)s. Veuillez sélectionner une autre durée ou un autre mode de paiement."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Faire un don"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Ce mode de paiement permet seulement un maximum de %(amount)s. Veuillez sélectionner une autre durée ou un autre mode de paiement."

#, fuzzy
msgid "page.donate.login2"
msgstr "Pour devenir membre, veuillez <a %(a_login)s>vous connecter ou vous inscrire</a>. Merci pour votre soutien !"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Sélectionnez votre crypto-monnaie préférée :"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(montant minimum le plus bas)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(eployî quant vos evoyîz Ethereum dès Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(avertissement : montant minimum élevé)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Cliquez sur le bouton de don pour confirmer ce don."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Faire un don <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Vous pouvez encore annuler le don lors du paiement."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirection vers la page de don…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Une erreur s'est produite. Veuillez recharger la page et réessayer."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mois"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "pour 1 mois"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "pour 3 mois"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "po 6 moes"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "po 12 moes"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "po 24 moes"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "po 48 moes"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "po 96 moes"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "po 1 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "po 3 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "po 6 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "po 12 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "po 24 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "po 48 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "po 96 moes “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donaedje"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Date: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Totå: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moes po %(duration)s moes, avou %(discounts)s%% rabè)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Totå: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moes po %(duration)s moes)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Statut: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifieu: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Anuler"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Estoz vs seur di voleur anuler? N'anulez nén si vs avoz ddja payî."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Oui, annulez s'il vous plaît"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Votre don a été annulé."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Faire un nouveau don"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Quelque chose s'est mal passé. Veuillez recharger la page et réessayer."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Commander à nouveau"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Vous avez déjà payé. Si vous souhaitez revoir les instructions de paiement, cliquez ici :"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Afficher les anciennes instructions de paiement"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Merci pour votre don !"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Si vous ne l'avez pas encore fait, notez votre clé secrète pour vous connecter :"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Sinon, vous risquez d'être verrouillé hors de ce compte !"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Les instructions de paiement sont maintenant obsolètes. Si vous souhaitez faire un autre don, utilisez le bouton « Commander à nouveau » ci-dessus."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Note importante :</strong> Les prix des cryptomonnaies peuvent fluctuer énormément, parfois même jusqu'à 20%% en quelques minutes. C'est encore moins que les frais que nous encourons avec de nombreux prestataires de paiement, qui facturent souvent 50-60%% pour travailler avec une « charité de l'ombre » comme la nôtre. <u>Si vous nous envoyez le reçu avec le prix original que vous avez payé, nous créditerons toujours votre compte pour l'adhésion choisie</u> (tant que le reçu n'est pas plus vieux de quelques heures). Nous apprécions vraiment que vous soyez prêt à supporter des choses comme celle-ci pour nous soutenir ! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Ce don a expiré. Veuillez annuler et en créer un nouveau."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Instructions Crypto"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transférez vers l'un de nos comptes crypto"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Faites un don du montant total de %(total)s à l'une de ces adresses :"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Achetez du Bitcoin sur Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Trouvez la page « Crypto » dans votre application ou site web PayPal. Cela se trouve généralement sous « Finances »."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Suivez les instructions pour acheter du Bitcoin (BTC). Vous n'avez besoin d'acheter que le montant que vous souhaitez donner, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfere li Bitcoin a no adresse"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Allez a l’pådje “Bitcoin” dins vosse app PayPal ou site web. Pressez l’boton “Transfere” %(transfer_icon)s, et pi “Envoyer”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Indiquez no adresse Bitcoin (BTC) come destinatair, et suivez les instrucçons po evoyer vosse don di %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrucçons po carte di crédit / débit"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Faites on don via no pådje di carte di crédit / débit"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Faites on don %(amount)s so <a %(a_page)s>cisse pådje</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Veyez l’gid passo-a-passo ci-après."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Statut:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Atinde confirmåcion (rafrechi l’pådje po veyî)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Atinde transfere (rafrechi l’pådje po veyî)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Temps ki rès:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(vous pouroûz viker et fé on novea don)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Po rmette li timer a zéro, fé on novea don."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Mete a djoû l’statut"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Si vos aveûz des problins, plait contaktiz nos a %(email)s et mete comeut d’infoirmåcions ki possibe (come des captures d’ecrin)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Si vos avoz ddja payî:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Dins l'cå, l'confirmåcion pout prinde djusque 24 eures, adon n'oubliyez nén d'rafraîchir cisse pådje (même si ele est expirêye)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Achtez des pyèces PYUSD so PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Suivez les instrucçons po achtez des pyèces PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Achtez on ptit peu pus (nos recmandans %(more)s pus) ki l’montant ki vos donreûz (%(amount)s), po coimbrer les côts di transacçon. Vos rindez tot çou ki rès."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Allez a l’pådje “PYUSD” dins vosse app PayPal ou site web. Pressez l’boton “Transfere” %(icon)s, et pi “Envoyer”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transfere %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Achtez des Bitcoins (BTC) avou Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Allez a l’ pådje “Bitcoin” (BTC) dins Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Achtez on ptit peu d’pus (nos-ôtes vos r’comandans %(more)s d’pus) ki l’ montin ki vos donroz (%(amount)s), po payî les fraix di tranzaccion. Vos r’steréz avou l’ r’ste."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transferey les Bitcoins a no adresse"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clitchîz sol boton “Envoyer des bitcoins” po fé on “retrait”. Passîz des dollars a BTC en clitchant sol %(icon)s icon. Intrer l’ montin BTC ci dzo et clitchîz “Envoyer”. Veyoz <a %(help_video)s>ci video</a> si vos estoz bloqué."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Po ptites donations (d’less di $25), vos dvozîz p’t-êt’ eployî Rush ou Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Achtez des Bitcoins (BTC) avou Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Allez a l’ pådje “Crypto” dins Revolut po achtez des Bitcoins (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Achtez on ptit peu d’pus (nos-ôtes vos r’comandans %(more)s d’pus) ki l’ montin ki vos donroz (%(amount)s), po payî les fraix di tranzaccion. Vos r’steréz avou l’ r’ste."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transferey les Bitcoins a no adresse"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Clitchîz sol boton “Envoyer des bitcoins” po fé on “retrait”. Passîz des euros a BTC en clitchant sol %(icon)s icon. Intrer l’ montin BTC ci dzo et clitchîz “Envoyer”. Veyoz <a %(help_video)s>ci video</a> si vos estoz bloqué."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Si vos plait d' eployî l' montânt en BTC ci dzo, <em>NENNI</em> eûros ou dollars, ôtrumint nos n' rçevrons nén l' montânt juste et nos n' pourons nén confirmer otomaticmint vosse mimbreye."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Po ptites donations (d’less di $25) vos dvozîz p’t-êt’ eployî Rush ou Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Eployîz on des sîvices “carte di crédit a Bitcoin” ci-après, ki n’ prindèt k’ onk mins:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Rimplixhoz les detays ci-après dins l' formulêre :"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Montant BTC / Bitcoin :"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Voloz-bén eployî cist <span %(underline)s>montant egzact</span>. Vost cost totå peut esse pus hôt a cause des côts di carte di crédit. Po des ptits montants, ci pout esse pus hôt ki nosse rabè, måleureus'mint."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Adresse BTC / Bitcoin (pôrtfèy extérnå) :"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instruktions"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Nosse suportans solum li standard version del crypto monnès, nén des réseaux exotiques ou versions des monnès. Ça pôrti prinde on hure po confirmer li transaction, suivant li monnè."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scaner l' code QR po payî"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scanez ci code QR avou vosse app di potchete crypto po vitmint rimpli les detays do payemint"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Carte-cadeau Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "S'il vos plait, eployîz li <a %(a_form)s>formulaire officiel Amazon.com</a> po nos evoyî on carte-cadeau di %(amount)s a l'adresse email ci-dessous."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Nosse n'acceptans nén d'ôtes méthodes di cartes-cadeaux, <strong>solum evoyîs directmint dès l'formulaire officiel d'Amazon.com</strong>. Nosse n'pôrons nén rinde vosse carte-cadeau si vos n'eployîz nén cist formulaire."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Dinez l' montant egzact: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "S'il vos plait, n'écrîz nén vosse message."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“À” adresse email dins l'formulaire:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unique a vosse compte, n'partagîz nén."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "N' eployîz k' ene fwa."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Atindant l'carte-cadeau… (rafraîchîz l'page po veyî)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Après avou evoyî vosse carte-cadeau, nosse système automatisé l'confirméra dins dès minutes. Si ça n'fonctonne nén, essayîz d'evoyî vosse carte-cadeau on côp d'pus (<a %(a_instr)s>instruktions</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Si ça n'fonctonne toudis nén, s'il vos plait, evoyîz nosse on email et Anna l'regardera manuellement (ça pôrti prinde dès jours), et mentionnîz bin si vosse avou djà essayé d'evoyî on côp d'pus."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Exemple:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Notez qu'li nom du compte ou l'image pôrti parèyî étrindje. Nén d'panique! Ces comptes sont gérés pa nosse partenaires di donations. Nosse comptes n'ont nén stî hackés."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Instructions Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donner su Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donner l'montant total di %(total)s eployant <a %(a_account)s>cist compte Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Si l' pådje di dener gets bloquée, sayîz ene ôte coonexhion a l' internet (p.ex. VPN ou l' internet do téléphone)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Malheureusement, l'page Alipay est sovint solum accessible dès <strong>Chine continentale</strong>. Vos pôrti avou besoin di désactiver temporairement vosse VPN, ou eployî on VPN po la Chine continentale (ou Hong Kong pôrti fonctonner dès côps ossi)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faites ene don (scannez l' code QR ou pressez l' bouton)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Ouvrez l' <a %(a_href)s>pådje di don par code QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scannez l' code QR avou l' app Alipay, ou pressez l' bouton po ovri l' app Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Paciincez-vos; l' pådje pout prinde on ptit tins a s' tcherdjî come ele est en Chine."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instruktions WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donner su WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donner l'montant total di %(total)s eployant <a %(a_account)s>cist compte WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Instruktions Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Faitès on Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Faitès li montânt totâl di %(total)s eployant <a %(a_account)s>cisse compte Pix"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Evoyîz nos l' reçû"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Evoyîz on reçu ou on screenshot a vost adresse di vérification personåle. N' eployîz nén cisse adresse imél po vosse don PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Evoyîz on reçû ou ene capture d' écran a vosse adresse di verification personåle:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Si l' taux d' échandje di crypto a fluctué pendant l' transaction, soyes seur d' inclure l' reçû montrant l' taux d' échandje originål. Nos vos rmercyons d' prinde l' peine d' eployî des cryptomonnèyes, çoula nos aidêye fort!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Quand vos avoz evoyî vos reçû, clitchîz cisse bouton, po qu' Anna pôye l' revouer manuellement (ça pôre prinde kékès djoûs):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Oyi, j' a evoyî mon reçû"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Meci po vosse don! Anna activrè vosse mimbreûté manuellement dins kékès djoûs."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Qqch a mål tourné. Rilodîz l' pådje et sayîz èncor."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Gaid po passêye dès-å-dès"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Dins kékès dès-å-dès, on mentionne dès portefeuilles crypto, mins n' vos inkiétez nén, vos n' dvoz nén aprinde èn-åk di crypto po çoula."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Intrer vosse émile."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Tchoezi vosse môde di payement."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Tchoezi vosse môde di payement èncor."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Tchoezi “Portefeuille auto-hôsté”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Clitchîz “J' confirm l' possèssion”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Vos dvoz rçûre on reçû d' émile. Evoyîz çoula a nos, et nos confirmrins vosse don dès qu' possibe."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Si vos plait, atindez pol moens <span %(span_hours)s>24 eures</span> (et rafraichîz cisse pådje) divant d' nos contakter."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Si vos avoz fwait on måkî dins l' payement, nos n' pôvans nén fwait dès rimbours, mins nos sayrons d' rmette çoula a bin."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Mes dons"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Les dètays des donations n'sont nén montrés publeye."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nole donation pol moumint. <a %(a_donate)s>Fai mi prumîre donation.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fai ene ôte donation."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Fitchîs djichargés"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Les djichargemints dès Servers Rapides sont markés pa %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Si vos avoz djichargé on fitchî avou des djichargemints rapîdes et lints, i s' montrera deus côps."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Les djichargemints rapîds dins les 24 hures passêyes contètèt po l' limète di djournêye."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Totes les hures sont en UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Les fitchîs djichargés n'sont nén montrés publeye."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nole fitchî djichargé pol moumint."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Dins les 18 dieres heûres"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Davant"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Conte"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "S' loguer / S' enrejestre"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID di conte: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profil publeye: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clé secrète (nén partadjer!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "montrer"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Membri: <strong>%(tier_name)s</strong> tot %(until_date)s <a %(a_extend)s>(prolonger)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Membri: <strong>None</strong> <a %(a_become)s>(devenir membri)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Djichargemints rapîds eployés (24 hures passêyes): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "quels djichargemints?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Groupe exclusif Telegram : %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Rejoignez-nous ici !"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Passez à un <a %(a_tier)s>niveau supérieur</a> pour rejoindre notre groupe."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contactez Anna à %(email)s si vous êtes intéressé par la mise à niveau de votre adhésion à un niveau supérieur."

#, fuzzy
msgid "page.contact.title"
msgstr "Email de contact"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Vous pouvez combiner plusieurs adhésions (les téléchargements rapides par 24 heures seront additionnés)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profil public"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Fichiers téléchargés"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Mes dons"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Déconnexion"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Vous êtes maintenant déconnecté. Rechargez la page pour vous reconnecter."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Quelque chose a mal tourné. Veuillez recharger la page et réessayer."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Inscription réussie ! Votre clé secrète est : <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Gardez cette clé précieusement. Si vous la perdez, vous perdrez l'accès à votre compte."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Marque-page.</strong> Vous pouvez ajouter cette page à vos favoris pour retrouver votre clé.</li><li %(li_item)s><strong>Télécharger.</strong> Cliquez <a %(a_download)s>sur ce lien</a> pour télécharger votre clé.</li><li %(li_item)s><strong>Gestionnaire de mots de passe.</strong> Utilisez un gestionnaire de mots de passe pour enregistrer la clé lorsque vous la saisissez ci-dessous.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Entrez votre clé secrète pour vous connecter :"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clé secrète"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Connexion"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clé secrète invalide. Vérifiez votre clé et réessayez, ou inscrivez-vous ci-dessous pour créer un nouveau compte."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Ne perdez pas votre clé !"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Vous n'avez pas encore de compte ?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Riwandjî novea conte"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Si vos avoz perdu vosse clé, voloz bén <a %(a_contact)s>nos contakter</a> et dner l' pus d' informåcions possibe."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Vos dvoz p't-êt fé on novea conte po nos contakter."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vî conte basî so l' emile? Intrer vosse <a %(a_open)s>emile ici</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Djivêye"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "candjî"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Sauvâ"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Sauvé. Voloz bén rloadî l' pådje."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Qqch a stî må. Voloz bén rissayî."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Djivêye pa %(by)s, fwait <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Djivêye est vude."

#, fuzzy
msgid "page.list.new_item"
msgstr "Radjouter ou oister di cisse djivêye en troevant on fitchî et en ouvant l' tab \"Djivêyes\"."

#, fuzzy
msgid "page.profile.title"
msgstr "Profil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profil nén trové."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "candjî"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Candjî vosse no di mostrer. Vosse identifiyant (l' part après “#”) n' pout nén esse candjî."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Sauvâ"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Sauvé. Voloz bén rloadî l' pådje."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Qqch a stî må. Voloz bén rissayî."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profil fwait <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Listes"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Pas encore de listes"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Créez une nouvelle liste en trouvant un fichier et en ouvrant l’onglet “Listes”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Li riforme des droets d'ôteu est nècèssaire po l'sûreté nacionale."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Les LLM chinwès (dont DeepSeek) sont formés sol mon archîve d'livres et d'papîs illégale — li pus grand del monde. L'Occident a bèsoin d'ovrî les droets d'ôteu come ene question d'sûreté nacionale."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "årticles d'acompagnemint pa TorrentFreak: <a %(torrentfreak)s>prumî</a>, <a %(torrentfreak_2)s>deusî</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "I n'a nén longtemps, les \"bibiotèques d'ombre\" ètént å bout d'leur vie. Sci-Hub, l'énorme archîve illégale d'papîs académiques, aveut aresté d'prinde des noveas ôves, à cause des procès. \"Z-Library\", li pus grand bibiotèque illégale d'livres, a veyou ses prétindous créateurs èsté arestés po des accusations criminèles d'droets d'ôteu. I sont incroyabl'mint rivés à s'eschaper, mins leur bibiotèque n'est nén mwin en denger."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quand Z-Library a fêt l'face a l'fermeture, j'aveu dja copîye tot l'archîve et j'cherchive ene plate-forme po l'héberger. C'ètot l'motîf po cminçer l'Archîve d'Anna: ene continouhance del mission d'ces initiatives d'vant. Nos avans dès lors grandî po dv'nir li pus grand bibiotèque d'ombre del monde, héberjant pus di 140 milion d'textes avou droets d'ôteu dins dès formats divèrs — livres, papîs académiques, magazinnes, djournals, et au-delà."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mi-èquipe et mi-ême sommes des idéologues. Nos croyans qu'preserver et héberger ces fichîs est moral'mint juste. Les bibiotèques arond l'monde veyèt dès coupes dins l'financement, et nos n'pouvans nén fièr l'héritådje d'l'humanité aux corporations non pus."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Pu vint l'IA. Preske totes les grosses sôciétés qui construhèt des LLM nos ont contactés po s'entrainer sol nos dônêyes. La plupart (mins nén totes!) des sôciétés basées aux États-Unis ont révisé leur position dès qu'i s'sont rindu conte del nature illégale d'notr' travå. Par contrast, les firmes chinwèses ont acceuilli avou enthousiasme nos collections, s'én souciant peu d'la légalité. C'est notable, vu l'role d'la Chine come signataire d'preske tos les grands traités internacionaux d'droets d'ôteu."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Nos avons douné accès à haute vitesse à pus ou mwin 30 sôciétés. La plupart sont des sôciétés d'LLM, et d'ôtes sont dès courtiers en dônêyes, qui revendront nos collections. La plupart sont chinwèses, mins nos avans ossu travayé avou dès sôciétés des États-Unis, d'Europe, de Russie, de Corée du Sud, et du Japon. DeepSeek <a %(arxiv)s>a admis</a> qu'ene version antérieure a stî formée sol ene partie d'notr' collection, mins i sont discrets à propos d'leur modèle l'pus récent (probabl'mint ossu formé sol nos dônêyes)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si l'Occident vout rister d'vant dins l'course des LLM, et en fin d'compte, l'AGI, i faut qu'i reconsidère sa position sol les droets d'ôteu, et vite. Qu'vos soyîz d'acord ou nén avou nos sol notr' argument moral, c'est asteure dv'nu ene question d'économie, et mêm d'sûreté nacionale. Tos les blocs de pouvoér construhèt dès super-scientifiques, super-pirates, et super-militaires artificiels. Li liberté d'l'informåcion dvint ene question d'survie po ces payis — mêm ene question d'sûreté nacionale."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Notr' èquipe provint d'tot l'monde, et nos n'avons nén d'alignement particulî. Mins nos encouragons les payis avou dès droets d'ôteu forts à sèrvir d'c'threat existenciel po les réformer. Donc, kè fère?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Notr' prumîre recommandåcion est simple: racourcir li terme des droets d'ôteu. Aux États-Unis, les droets d'ôteu sont accordés po 70 ans après l'mort d'l'ôteu. C'est absurde. Nos pouvans aligner çoula avou les brevèts, qui sont accordés po 20 ans après l'dépôt. Çoula d'vrait êt' pus qu'suffisant po qu'les ôteus d'livres, papîs, musique, art, et ôtes ôves créatives, soient pleinement rétribués po leurs efforts (y compris dès projets à long terme come les adaptations cinématographiques)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Pu, au minme, les décideurs politiques d'vraient inclure dès exceptions po la préservation et la diffusion de masse des textes. Si li perte d'revenus dès clients individuels est l'principal souci, la distribution à l'niveau personnel pout rister interdite. En retour, ceux capables d'gérer dès vastes répertoires — les sôciétés qui forment des LLM, ainsi qu'les bibiotèques et ôtes archîves — s'raient couverts pa ces exceptions."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Dès payis font dja ene version d'çoula. TorrentFreak <a %(torrentfreak)s>a rapporté</a> qu'la Chine et l'Japon ont introduit dès exceptions IA dins leurs droets d'ôteu. I n'est nén clair po nos come çoula interagit avou les traités internacionaux, mins çoula donne certainement d'la couverture à leurs sôciétés domestiques, çoula explique c'qu'nos avans veyou."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quant à l'Archîve d'Anna — nos continuerons notr' travå souterrain ancrî dins l'conviction morale. Mins notr' pus grand souhait est d'entrer dins l'lumîre, et d'amplifier notr' impact légal'mint. S'vplait, réformez les droets d'ôteu."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lîz les årticles d'acompagnemint pa TorrentFreak: <a %(torrentfreak)s>prumî</a>, <a %(torrentfreak_2)s>deusî</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Gagnants d'li prix d'visualisation ISBN d'10 000 $"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Nos avans reçu dès soumissions incroyables po li prix d'visualisation ISBN d'10 000 $."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "I n'a quèlques mois, nos avans annoncé ene <a %(all_isbns)s>récompense d'10 000 $</a> po fère li pus bonne visualisation possib' d'notr' dônêye montrant l'espace ISBN. Nos avans mis l'accent sol montrer quî fichîs nos avans/d'avans nén dja archivé, et nos avans plus tard ajouté ene base de dônêyes décrivant combien d'bibiotèques détiennent des ISBN (ene mesure d'rareté)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Nos avans stî submergés pa l'réponse. I a eu tant d'créativité. Ene grand mèrci a tertos qui ont participé: vosse énergie et enthusiasme sont contagieux!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "A la fin, nos volans risspondre å les cwestions ci-après : <strong>quels livres egzistèt dins l'monde, combien avans-nos djà archivé, et a quels livres dvrins-nos nos concentrer après ?</strong> C'est magnifik di vey tant di djins s'inquiéter di ces cwestions."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Nos avans cmincé avou ene visualisåcion basike nos-minmes. En mwins di 300kb, c'te imådje riprésinte succinctemint l'pus grand \"listêye di livres\" plinmint ouver ki n'a djamåy stî rassemblé dins l'histwere di l'humanité :"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Totes les ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Fitchîs dins l'Archive d'Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuite di dônees CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Index eBook d'EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archive d'Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registre Mondial des Éditeurs ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Bibliothèque d'État Russe"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Bibliothèque Impériale de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Dins"

#, fuzzy
msgid "common.forward"
msgstr "Divant"

#, fuzzy
msgid "common.last"
msgstr "Dierin"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Veyez l’ <a %(all_isbns)s>årtike oridjinal do blog</a> po avou d’ pus amples infôrmåcions."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Nos-ôtes avans dné èn défi po amiyorer çoula. Nos-ôtes avans promîs ene råye di prumîre plaece di $6,000, ene diusime plaece di $3,000, et ene troesime plaece di $1,000. A cause del rèsponse ènorme et des somissions èxtraordinaires, nos-ôtes avans dicidé d'augminter on ptit l'fond des prix, et d'awårder ene troesime plaece a quatre, avou $500 a tchaeke. Les gangnants sont divins, mins n'oubliyez nén d'vêye totes les somissions <a %(annas_archive)s>ci-après</a>, ou d'baixher nos-ôtes <a %(a_2025_01_isbn_visualization_files)s>torrint combiné</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Prumîre plaece $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Cisse <a %(phiresky_github)s>somission</a> (<a %(annas_archive_note_2951)s>comentåre Gitlab</a>) est totafèt tot çou k' nos-ôtes volyons, et pus co! Nos-ôtes avans spècialmint amé les ôptions di visualizåcion ènormemint flexibes (même avou des shaders costomizés), mins avou ene liste compréhinsive di présèts. Nos-ôtes avans ossi amé come tot est vite et doux, l'implémentåcion simplète (ki n'a même nén d'backend), l'minimap astûceus, et l'explicåcion ètindue dins leur <a %(phiresky_github)s>årtike do blog</a>. Tchôl èvô, et l'gangnant bin mérité!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Diusime plaece $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "On ôte <a %(annas_archive_note_2913)s>somission</a> èxtraordinåre. Nén si flexibe k' l'prumîre plaece, mins nos-ôtes avans d'vraimint préferé s'visualizåcion a l'nivo macro al prumîre plaece (courbe ki rimplît l'èspece, bortures, étiquetåge, mètans en évidence, panage, et zoom). On <a %(annas_archive_note_2971)s>comentåre</a> di Joe Davis a résoné avou nos-ôtes:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Bin k' les cårrés et les rectångles parfaits sont plêzants mathématicmint, i n'fornèt nén ene supériure localité dins on contexte di cartografeye. Dj'croeye k' l'asymétrie inhérinte dins ces Hilbert ou Morton classiques n'est nén ene dèfaut mins on caractèristique. Come l'outline en forme di botte d'Italie ki l'fé r'connaissib dès l'prumîre veye su on mappe, les \"particularités\" di ces courbes pôront servir di repères cognitifs. Cisse distinctivité pôreût amélioyer l'mémwêre spatiale et aidî les usåjheus a s'orienter, possiblimint féyant l'localizåcion di régions spécifiques ou l'notåcion di patrons pus åjhe.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Et todi des ôptions po visualizer et rendre, ossi bén k' on UI ènormemint doux et intuitif. On solide diusime plaece!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Troesime plaece $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Dins cisse <a %(annas_archive_note_2940)s>somission</a> nos-ôtes avans vraimint amé les diférins sôrs di veyes, en particulî lès veyes di comparêson et d'éditeu."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Troesime plaece $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Bin k' nén l'UI l'pus polie, cisse <a %(annas_archive_note_2917)s>somission</a> rimplît on bon måss di critêres. Nos-ôtes avans particulîrmint amé s'caractèristique di comparêson."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Troesime plaece $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Come l'prumîre plaece, cisse <a %(annas_archive_note_2975)s>somission</a> nos-ôtes a imprèssioné avou s'flexibilité. A l'fin, c'est çoula ki fé on grand outil di visualizåcion: ene flexibilité maxîmale po les usåjheus avou des pôvèrs, tot an r'tinant çoula simplète po les usåjheus moyéns."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Troesime plaece $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "L'dèrnîre <a %(annas_archive_note_2947)s>somission</a> a r'cevoir on prix est assez basique, mins a des caractèristiques uniques ki nos-ôtes avans vraimint amé. Nos-ôtes avans amé come i montrèt combien di datasets cåvrit on ISBN particulî come mesur di popularité/fiabilité. Nos-ôtes avans ossi vraimint amé l'simplèté mins l'efficacité d'utiliser on curseur d'opacité po les comparêsons."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idêyes notåbes"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "D'ôtes idêyes et implémentåcions ki nos-ôtes avans particulîrmint amé:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Gratte-ciels po l'rarité"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistiques en live"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotations, et ossu statistiques en live"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Veye unique di carte et filtres"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schéma di coloeur par defaut et heatmap."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Toggling facile des datasets po des comparaisons rapides."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Belles étiquettes."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barre d'échelle avou l' nombre di livres."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Plein di curseurs po comparer des datasets, come si vos-étos on DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Nos pourrions continuer po on moment, mais arrêtons-nous ici. Assurez-vos di r'garder totes les soumissions <a %(annas_archive)s>ci</a>, ou di télécharger nos <a %(a_2025_01_isbn_visualization_files)s>torrent combiné</a>. Totes ces soumissions, et chasconne apporte on perspective unique, qu'ce soit dins l'UI ou l'implémentation."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Nos intégrerons au moins l' soumission di première place dins nos site web principal, et p't-êt' d'ôtes. Nos avans ossu commencé a penser a come organiser l'processus d'identification, confirmation, et puis archivage des livres les pus rares. Davantage a v'nir su c' front."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Mierci a tertos qui ont participé. C'est incroyable qu'tant di djins s'inquiètèt."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nos cœurs sont remplis di gratitude."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualiser tos les ISBNs — prime di $10,000 po 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "C'te imâge représente l' pus grande “liste di livres” complètement ouverte jamais assemblée dins l'histoire di l'humanité."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "C'te imâge est di 1000×800 pixels. Chascon pixel représente 2,500 ISBNs. Si nos avans on fichier po on ISBN, nos rendons c' pixel pus vèrt. Si nos savons qu'on ISBN a stî émis, mais qu'nos n'avans nén d'fichier correspondant, nos l'rendons pus rouge."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En mwin di 300kb, c'te imâge représente succinctement l' pus grande “liste di livres” complètement ouverte jamais assemblée dins l'histoire di l'humanité (quauques centaines di GB compressés en entier)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Elle montre ossu : il reste beaucoup di travail po sauvegarder les livres (nos n'avans qu'16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Fond"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Come pout bén l'Archive d'Anna riyî d'ahiver s' mission d'backupî tot l'connaissance d'l'humanité, sins savou quîs livres sont co bén d'vins? Nos avans d'besoin d'ene liste TODO. Ene manîre d'planter çoula c'est avou des numéros ISBN, qu' dès années 1970 ont stî assignés a chas livre publyî (din l'plupârt des payis)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "I gn a nén d'autorité centrale qui sait tertous les assignations ISBN. A l'plaece, c'est on sistinme distribué, où les payis rçûvèt des renges di numéros, qui assignèt après des p'tites renges a des grands éditeurs, qui pôrèt co diviser les renges po des p'tits éditeurs. A l'fin, des numéros individuels sont assignés a des livres."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Nos avons cmincî a mapper les ISBNs <a %(blog)s>djius d'ans</a> avou nos scrapes d'ISBNdb. D'pwis, nos avons scrapé d'pus d'sources di metadata, come <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, et d'ôtès. Ene liste complète pout esse trovée so les pådjes “Datasets” et “Torrents” dins l'Archive d'Anna. Nos avans asteure d'lon la pus grande coleccion d'metadata di livres, toltalement ouverete et aisément téléchargeåve (et donc des ISBNs) dins l'monde."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Nos avons <a %(blog)s>ritchî abondamment</a> a propôs d'poquoi nos s'coucî d'la préservation, et poquoi nos sommes asteure dins ene fenêtre critique. Nos d'vons asteure identifier des livres rares, sous-focalisés, et uniquemint a risk et les préservî. Avoir bon metadata so tertous les livres dins l'monde aidèt avou çoula."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisåcion"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "A costé d'l'image d'vue d'ensemble, nos poumons ossi r'gardî a des datasets individuels qu'nos avons acqueris. Utilisez l'déroulant et les boutons po switchî entre zels."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "I gn a des tas d'patrons intéressants a vey dins ces imådjes. Poquoi i gn a ene régularité di lignes et di blocs, qui s'passèt a des échelles diférintes? Qu'est-ce qu'les zônes vides? Poquoi cèrtes datasets sont-i si rassemblés? Nos l'laissrons ces questions come ene exercise po l'lecteur."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 d'prîme"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "I gn a beaucoup a explorer ici, donc nos annonçons ene prîme po amélioressî l'visualisåcion ci-dessus. A l'contrère d'la plupârt d'nos prîmes, cisse-ci est limitée dins l'tin. Vos d'vîz <a %(annas_archive)s>soumette</a> vos code open source divant l'2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "L'meilleur soumission rçûvra $6,000, l'deuxième place est $3,000, et l'troisième place est $1,000. Totes les prîmes seront attribuées avou Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Ci-dessous sont les critères minimaux. Si nén-ene soumission n'atteint les critères, nos poumons co attribuer des prîmes, mais çoula s'ra a nosse discrétion."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forkez cisse repo, et éditez cisse pådje HTML d'blog (nén-ôte backend qu'nos backend Flask n'sont permis)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faites qu'l'image ci-dessus s'zoomme doucemint, po qu'vos pouhîz zoomer tot l'chemin divins les ISBNs individuels. Cliquî les ISBNs d'vra vos amener a ene pådje di metadata ou a ene recherche dins l'Archive d'Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Vos d'vîz ossi esse capåble di switchî entre tertous les datasets diférints."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Les renges d'payis et les renges d'éditeurs d'vraient esse mis en évidence quand vos survolez. Vos pouhîz utiliser par exemple <a %(github_xlcnd_isbnlib)s>data4info.py dins isbnlib</a> po les infos d'payis, et nosse “isbngrp” scrape po les éditeurs (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Çoula d'vra bin fonccionner so desktop et mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Po des points d'bonus (cisse sont justes des idées — laissez vosse créativité s'déchaîner):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "On d'vra accorder ene forte considération a l'utilisabilité et a l'esthétique."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Montrer l'metadata réel po les ISBNs individuels quand vos zoomez, come l'titre et l'auteur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Mieulle courbe d'remplissage d'espace. Par exemple, ene zig-zag, allant d'0 a 4 so l'premier rang et après d'retour (a l'envers) d'5 a 9 so l'deuxième rang — appliqué récursivemint."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Diférints ou personnalisåves schémes di coloeur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vues spéciales po comparez les Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Moyéns po debuguer des problémes, come des ôtes metadata qui n’ s’accordèt nén bén (p.ex. des titlès fort diférints)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annoter des imådjes avou des comintêyes so les ISBN ou des renges."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Totes heuristiques po identifîyî des livres rares ou a risk."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Totes idées créatives qu' vos pouvez avou!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Vos pouvez totafwait s' écarter des critéres minimås, et fére ene visualisåcion totafwait diférinte. Si c' est vréyimint spectaculêre, al qualifeye po l' bounty, mins a no discretîon."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faites vos soumissions en postant on comintêye a <a %(annas_archive)s>cisse issue</a> avou on lîen po vos repo forké, demande di fusion, ou diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Li code po générér cès imådjes, ossi bén qu' d' ôtes exemples, pout esse trové dins <a %(annas_archive)s>cisse directory</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Nos avans avougné on format di dônees compact, avou liquî tot lès informåcions ISBN nécéssaires sont d' envirin 75MB (compressé). Li descripcîon do format di dônees et li code po l' générér pout esse trové <a %(annas_archive_l1244_1319)s>ci</a>. Po l' bounty vos n' estoz nén oblidjîs d' eployî cisse, mins c' est probåbilmint li format li pus convnint po cmincî. Vos pouvez transformer nos metadata come vos voloz (mins tot vos code dût esse open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nos n' pouvans nén atinde di vey çou qu' vos avoz avougné. Bonne chance!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Les Conteneus d' l' Archive d' Anna (CAA) : standardiser les rilêyes di l' pus grandès librérîyes d' ômbre do monde"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "L' Archive d' Anna est dvnû l' pus grande librérîye d' ômbre do monde, nos oblidjant a standardiser nos rilêyes."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>L' Archive d' Anna</a> est dvnû d' lon l' pus grande librérîye d' ômbre do monde, et l' sole librérîye d' ômbre di s' échelle qui est totåfwait open-source et open-data. Ci dzo-dessous est ene table di no page Datasets (légérement modifiée):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Nos avans accompli çoula di troes manéres:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Miroir les librérîyes d' ômbre open-data existantes (come Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Aidant les librérîyes d' ômbre qui veyèt esse pus ouvertes, mins n' avèt nén l' tins ou les ressources po l' fére (come li collection di comics di Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scrapant les librérîyes qui n' veyèt nén partadjer en gros (come Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Po (2) et (3), nos gérons asteure ene colection consideråve di torrents nos-êmes (100ènes di TBs). Djusque ci, nos avans abordé ces colections come des cas uniques, c' qui volèt dire ene infrastructure et ene organizåcion des dnéyes sur mesure po chaque colection. Ça radjoute ene surcharge significative a chaque sortidje, et ça rend particulîrmint difficile di fé des sortidjes plus incrémentales."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "C'est po çoula qu'on a decidé di standardizer nos sortidjes. Cist ci est on post di blog tecnike dins l'quî nos prézintans nos standard: <strong>Les Conteneurs d'Anna’s Archive</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Buts di design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Nos cas d'usage principal c'est l'distribucion des fitchîs et des metadata associés a dès colections existantes. Nos considerations les pus importants sont:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Fitchîs et metadata hétérogènes, dins on format l'pus proche possibe del original."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identifiants hétérogènes dins les bibliotèques sourdjes, ou mêm l'absence d'identifiants."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Sortidjes séparées des metadata vs dnéyes di fitchîs, ou sortidjes di metadata seulmint (p.ex. nos sortidje ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribucion via torrents, mais avou l'possibilité d'ôtes méthodes di distribucion (p.ex. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Records immutables, car nos devrîons assumer qu'nos torrents vivront po todi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Sortidjes incrémentales / sortidjes appendables."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Lisibte et écrive par machines, d'façon pratique et rapide, surtout po nos stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Assez facile d'inspecter par l'ome, mêm si c'est secondaire a l'lisibleté par machines."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile di semer nos colections avou ene seedbox standard louwée."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dnéyes binaires peuvent esse servies directement par des serveurs web come Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Dès non-buts:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nos n'cairons nén qu'les fitchîs soient faciles a naviguer manuellement su l'disque, ou qu'ils soient rechèrchables sans prétraitement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nos n'cairons nén d'esse directement compatibles avou l'logiciel de bibliotèque existant."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Mêm s' il dût esse facile po n'importe qui di semer nos colections avou des torrents, nos n'attendons nén qu'les fitchîs soient utilisables sans ene connaissance tecnike significative et ene engagement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Pisk'Anna’s Archive est open source, nos volans utiliser nos format directement. Quand nos rafraîchissons nos index di recherche, nos accédons seulmint a des tchmins publiquemint disponibles, po qu' n'importe qui qu'fourche nos bibliotèque puisse s'mettre en route rapidmint."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "L'standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "A la fin, nos avans tchoezi d' s' raler a ene norme relativemint sinple. Ele est assez lache, nénormative, et c' est ene ovraedje en cours."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Conteneu d' l' Archive d' Anna) est ene seule intrêye consistant en <strong>metadata</strong>, et opcionalmint <strong>données binaires</strong>, les deus estant imutåves. I gn a on identifieu unique mondiel, apelé <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Colêye.</strong> Chaske AAC est dins ene colêye, qui, d' s' difinicion, est ene liste d' AACs qui sont sémantiquement consistants. Ça vout dire qu' si vos fêt ene candjance significative al format des metadata, vos dvoz creér ene novele colêye."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Colêyes di “records” et di “fichîs”.</strong> Par convintion, c' est sovint comode di diviner les “records” et les “fichîs” come des colêyes diférintes, po qu' i poyèt esse divinés a des moments diférints, p. ex. basés sol rîtme di scrappadje. Ene “record” est ene colêye avou des metadata solumint, contnant des infôrmåcions come les titès des livres, les ôteurs, les ISBNs, etc., tandis qu' les “fichîs” sont les colêyes qui contnèt les fichîs réels (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Li format d' l' AACID est ci: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Par egzimpe, on AACID réel qu' nos avans diviné est <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: li no d' colêye, qui pout contni des letes ASCII, des nombeus, et des tîs d' soulî (mais nén des dobles tîs d' soulî)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: ene vèrsion court d' l' ISO 8601, todi en UTC, p. ex. <code>20220723T194746Z</code>. Cisse no d'vot montî d' façon monotone po chaske divinêye, mins ses sémantiques exactès pout candjî po chaske colêye. Nos sugierans d' eployî li tins di scrappadje ou di generadje d' l' ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: on identifieu spéciq à l' colêye, s' i est aplicåve, p. ex. l' ID di Z-Library. Pout esse omis ou tronqué. D'vot esse omis ou tronqué si l' AACID autremint d'passerait 150 caractéres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: ene UUID mins compressêye a ASCII, p. ex. eployant base57. Nos eployans asteure li librêye Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Rangêye d' AACID.</strong> Come les AACIDs contnèt des timestamps qui montèt monotoniquemint, nos poyans eployî ça po d'noter des rangêyes dins ene colêye particulîre. Nos eployans ci format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, où les timestamps sont inclus. C' est consistant avou l' notåcion ISO 8601. Les rangêyes sont continûes, et poutèt s' chevauchî, mins en cas d' chevauchement, d'vot contni des intrêyes identiques a celes divinêyes préalablimint dins cisse colêye (pisk' les AACs sont imutåves). Les intrêyes manquantes sont nén permisès."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Fitchî di metadata.</strong> On fitchî di metadata contint les metadata d' on rangêye d' AACs, po ene colêye particulîre. Cels ont les propriytés ci-après:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Li no do fitchî d'vot esse on rangêye d' AACID, prefissêye avou <code style=\"color: red\">annas_archive_meta__</code> et suivou d' <code>.jsonl.zstd</code>. Par egzimpe, ene di nos divinêyes s' apel<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Come indicåve pa l' estinsion do fitchî, li tipe do fitchî est <a %(jsonlines)s>JSON Lines</a> compressé avou <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Chaske ôbjet JSON d'vot contni les champs ci-après al nivea do dessus: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcionel). Nén ôte champ n' est permis."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> est des metadata arbitraires, solon les sémantiques d' l' colêye. I d'vot esse sémantiquement consistant dins l' colêye."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> est opcionel, et est li no do ridant di données binaires qui contint les données binaires correspondantes. Li no do fitchî di données binaires correspondantes dins cisse ridant est l' AACID do record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Li prefisse <code style=\"color: red\">annas_archive_meta__</code> pout esse adaptêye al no d' voste institucion, p. ex. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Ridant di données binaires.</strong> Ene ridant avou les données binaires d' on rangêye d' AACs, po ene colêye particulîre. Cels ont les propriytés ci-après:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Li no do ridant d'vot esse on rangêye d' AACID, prefissêye avou <code style=\"color: green\">annas_archive_data__</code>, et nén sufixe. Par egzimpe, ene di nos divinêyes réelles a on ridant apelé<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Li ridant d'vot contni des fitchîs di données po tos les AACs dins li rangêye spéciq. Chaske fitchî di données d'vot aveur son AACID come no do fitchî (nén d' estinsions)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "I fåt rcmmander di fé des ridantchîs ki n' sont nén trop grandès, p.ex. nén pus grandès ki 100GB-1TB chacune, mins cisse rcmmandåcion ci pout candjî avou l' tins."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Les fitchîs metadata et les ridantchîs di dnêyes binaires pôront esse amonçnêyes dins des torrents, avou on torrent par fitchî metadata, ou on torrent par ridantchî di dnêyes binaires. Les torrents dètchèt avou l' no d' origne do fitchî/directory et on suffixe <code>.torrent</code> come no di fitchî."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Egzimpe"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Veyans a nosse dèrnîre sortidje Z-Library come egzimpe. Ele consistêye di deus coleccions : “<span style=\"background: #fffaa3\">zlib3_records</span>” et “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Çoula nos permet di racler et sortîr séparémint les records metadata des fitchîs d' livro. Come tèl, nos avans sortou deus torrents avou des fitchîs metadata :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Nos avans ossu sortou on tas di torrents avou des ridantchîs di dnêyes binaires, mins solmint po l' coleccion “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 totå :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "En rouwant <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> nos pôvans vey ki çou k' i gn a ddins :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Dins cisse cas-ci, c' est l' metadata d' on livro rapoirtêye pa Z-Library. Å nivê do hôt, nos n' avans ki “aacid” et “metadata”, mins nén “data_folder”, paski gn a nén d' dnêyes binaires correspondantes. L' AACID contint “22430000” come ID principal, ki nos pôvans vey k' est prindou di “zlibrary_id”. Nos pôvans s' atinde a çou k' les ôtès AAC dins cisse coleccion ont l' minme structûre."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Asteure, rouwans <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Cisse ci est on ptit metadata AAC, mins l' gros d' cisse AAC est situêye ôtans dins on fitchî binaire ! Å fin, nos avans on “data_folder” cisse feye, çoula nos permet d' s' atinde a çou k' les dnêyes binaires correspondantes sont situêyes a <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. L' “metadata” contint l' “zlibrary_id”, çoula nos permet d' l' associer a l' AAC correspondante dins l' coleccion “zlib_records”. Nos pôrions l' associer di diférintes manières, p.ex. avou l' AACID — l' standard n' l' prescrit nén."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Notîz k' i n' est nén nécéssaire k' l' champ “metadata” soye lu minme on JSON. I pout esse ene tchinne contnant XML ou n' impoirté kel ôte format di dnêyes. Vos pôrîz mêm stoker l' infôrmåcion metadata dins l' blob binaire associé, p.ex. s' i gn a on tas di dnêyes."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Concluzion"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Avou cisse standard, nos pôvans fé des sortidjes pus incrémentales, et d' manîre pus åseye d' radjouter des noûs sôces di dnêyes. Nos avans ddja onk des sortidjes palpitantes dins l' pipeline !"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Nos espérons ossu k' i dvinne pus åseye po les ôtes bibliotèques d' ombrer di mirer nosse coleccions. Å fin, nosse but est di préserver l' connoissance et l' cultur' umène po todi, çoula pus d' redondance, miyeu c' est."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Mise a djoûr d' Anna : ridantchî d' archîves totålmint åvri, ElasticSearch, 300GB+ di couvertures di livro"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Nos avans stîs a l' ôvra tot l' tins po founir ene bone alternative avou l' Archîve d' Anna. Veyez k' est çoula k' nos avans ritchî récemint."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Avou Z-Library ki s' est alé et ses (prétindous) fondatous ki sont stîs arestés, nos avans stîs a l' ôvra tot l' tins po founir ene bone alternative avou l' Archîve d' Anna (nos n' l' lierons nén ci, mins vos pôvîz l' tcherchî avou Google). Veyez k' est çoula k' nos avans ritchî récemint."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L' Archîve d' Anna est totålmint åvri"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Nos croyans k' l' infôrmåcion dèt esse liye, et nosse code a nos n' est nén ene èxception. Nos avans sortou tot nosse code sol nosse instance Gitlab privêye : <a %(annas_archive)s>L' Sofware d' Anna</a>. Nos eployans ossu l' suiveu d' problinmes po organizer nosse trawå. Si vos voloz vos impliquer dins nosse divelopmint, c' est on bon cminçmint."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Po vos dner on avant-gout di çoula k' nos trawayans, veyez nosse trawå récent sol l' amélioressance di l' performance du costé client. Come nos n' avans nén encoére impleminté l' pagination, nos rendons sovint des pådjes di tcherete bén longuès, avou 100-200 résultats. Nos n' volions nén couper les résultats di tcherete trop tôt, mins çoula volêye dire k' i ralintisseut des dispostifs. Po çoula, nos avans impleminté on ptit truc : nos avans envelopé l' plupårt des résultats di tcherete dins des comentaires HTML (<code><!-- --></code>), et adon nos avans scrît on ptit Javascript ki détecte kan on résultat dèt dviner vizib, å moment nos desenvlopons l' commentaire :"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "Li \"virtualization\" do DOM implimintê en 23 lin.es, nén miete d' bibliotèques sophistikêyes! C' est l' sôre di code pragmatike et rapid ki vos avourez cwand vos avoz d' tins limité et des problins réels a rissoudre. I gn a stî rapoirté k' asteure nosse rechèrche fonce bén so les dispostifs lints!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "On a fêt on grand efôrt po automatizer l' construccion del baste di données. Cwand nos avans lanchî, nos avans rassemblé des sôces divèrtes sins trop d' ôrdre. Asteure, nos volans les mete a djoû, don nos avans scrît on tas d' scripts po tèlèdjiçhî des noveas metadata des deus forks di Library Genesis, et les intégrer. L' but c' est nén sôlement d' fêre çoula utile po nosse archive, mais d' fêre çoula aîse po tertous ki volrènt s' amuser avou les metadata des bibliotèques d' ômbre. L' but serè d' aveur on cahî d' Jupyter avou totes sôres di metadata intêressantes, po k' nos pouhons fére pus d' rechèrches come d' s' rinde conte di k' <a %(blog)s>pourcintadje d' ISBNs sont préservés po todi</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "A l' fin, nos avans réformé nosse sistinme di d' nêyes. Vos poloz asteure eployî ene carte di crédit po d' positer des sôs direktemint dins nosse portefeuilles crypto, sins avou d' nén k' çoula des cryptomonnayes. Nos continurans a veyî cossi çoula fonce en pratique, mais c' est on grand pas."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Passer a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "On des nos <a %(annas_archive)s>tickets</a> c' têt ene collection di problins avou nosse sistinme di rechèrche. Nos eployî MySQL po la rechèrche en teskse plin, paski nos avans totes nosse données dins MySQL d' tôte manîre. Mais i gn aveut des limites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Certaenes requêtes prindént on tins fou, a l' point k' i prindént totes les connêxions ouveres."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Par défôt, MySQL a ene longuèr minimum po les mots, ou vosse index polot dvinsse onk' grand. Les djins rapoirtént k' i n' poloz nén rechèrchi \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La rechèrche n' têt rapid k' cwand i n' têt plinmint en mèmwêre, k' nos avans d' eployî ene machine pus costéye po fêre tourner çoula, et des commands po précharger l' index a l' démarache."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nos n' poloz nén l' étendre aîsement po construere des noveas fonctinnalités, come ene miyeure <a %(wikipedia_cjk_characters)s>tokenization po les langues sins espâces</a>, filtrer/faceter, trier, suggestions \"vos voloz dire\", autocomplétion, et ainsin di suite."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Après avou parlé avou on tas d' èksperts, nos avans choisî ElasticSearch. Çoula n' a nén stî parfait (leurs suggestions \"vos voloz dire\" et les fonctinnalités d' autocomplétion sont nuls), mais en général çoula a stî bén miyeu k' MySQL po la rechèrche. Nos n' sommes nén <a %(youtube)s>trop sûrs</a> d' l' eployî po des données critiques (même s' i gn a fêt on tas d' <a %(elastic_co)s>progrès</a>), mais en général nos sommes assez contents d' l' changement."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Po l' moment, nos avans impliminté ene rechèrche bén pus rapide, miyeure support des langues, miyeure tri par pertinince, diférintes ôptions di tri, et filtrer so langue/tipe di livre/tipe di fitchî. Si vos estoz curieux di cossi çoula fonce, <a %(annas_archive_l140)s>avéz</a> <a %(annas_archive_l1115)s>on</a> <a %(annas_archive_l1635)s>coup d' oeil</a>. Çoula est assez accèssibe, même si çoula polot aveur besoin di pus di commentaires…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ di couvertures di livres libérés"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "A l' fin, nos sommes contents d' anoncer on ptit lâchî. En colåboration avou les djins ki opérèt l' fork Libgen.rs, nos partagons totes leurs couvertures di livres avou des torrents et IPFS. Çoula va distribuer l' charge di veyî les couvertures su pus di machines, et les préservér miyeu. Dins on bon nombre (mais nén tertous) di cas, les couvertures di livres sont inclues dins les fitchîs minmes, donc çoula est onk' \"données dérivées\". Mais aveur çoula dins IPFS est todi bén utile po l' fonctinnemint djournali d' l' Archive d' Anna et les diférintes forks di Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Come d' habitude, vos poloz trover çoula a l' Pirate Library Mirror (EDIT: déplacé a <a %(wikipedia_annas_archive)s>l' Archive d' Anna</a>). Nos n' lierons nén çoula ici, mais vos poloz l' trover aîsement."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Espérons k' nos poloz ralinter on ptit l' rithme, asteure k' nos avans onne alternative décente a Z-Library. Cisse charge di travail n' est nén particulîrmint soutenåbe. Si vos estoz intêressé a aidî avou l' programmation, l' opéracion des serveurs, ou l' travail di préservation, n' hésitîz nén a nos contakter. I gn a todi on tas d' <a %(annas_archive)s>travail a fêre</a>. Mierci po vosse intêret et vosse soutien."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna et l' èquipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "L' Archive d' Anna a sauvegardé l' pus grande bibliotèque d' ômbre di comics do monde (95TB) — vos poloz aidî a l' semer"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La pus grande bibliotèque d' ômbre di comics do monde aveut on seul point di défaillance.. jusqu' a asteure."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discuter so Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La pus grande bibliotèque d' ômbre di comics est probablimint cisse d' on fork particulî di Library Genesis: Libgen.li. L' seul administrateur ki gère çisse site a rassemblé onne collection di comics insensée di pus di 2 millions di fitchîs, totalisant pus di 95TB. Cepandant, a diférince des ôtres collections di Library Genesis, cisse-ci n' têt nén disponibe en vrac avou des torrents. Vos poloz sôlement accéder a ces comics individuellement avou son serveur personnel lent — on seul point di défaillance. Jusqu' a asteure!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Dins cisse poste, vos rascantans pus d' coledjeye, et d' nosse levêye di fonds po sostni pus d' cisse ovra."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Li doteûre Barbara Gordon esseye di s' pèrde dins l' monde banal del librêreye…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fourkes di Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "D' prumî, on pô d' fond. Vos-z-avez p't-êt k'nowance di Library Genesis po leus coledjeye épike di livrès. Mins d' gnès ki savent k' les volontaires di Library Genesis ont creé des ôtes projès, come ene grande coledjeye di magazinnes et di documents standards, ene copîye complète di Sci-Hub (en colåboråcion avou l' fondatrice di Sci-Hub, Alexandra Elbakyan), et d' fait, ene vaste coledjeye di comics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "A on momint, des opérateurs di mirroirs di Library Genesis ont prindou des voyes differentes, çou ki a d'né l' sitwècion actuelle avou on bonome di differentes \"fourkes\", totes portant todi l' no Library Genesis. Li fourke Libgen.li a uniqmint cisse coledjeye di comics, ossi bén k' ene grande coledjeye di magazinnes (ki nosse-omes ossi d' tchoer)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colåboråcion"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Donné sa taille, cisse coledjeye a longtimps stî dins nosse liste di sohaits, alor après nosse succè avou l' copîye di Z-Library, nosse-omes fixî nosse-omes su cisse coledjeye. A l' cominmint, nosse-omes l' avans gratté dirèctemint, çou ki fût on vrai défi, pask' leus serveur n' esteut nén dins l' miyeû condicions. Nosse-omes avans obtinu on trentene di 15TB d' cisse manîre, mins ç' esteut linde."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Heureus'mint, nosse-omes avans r'jondou l' opérateur del librêreye, ki a consenti di nosse-omes evoyî totes les dnéyes dirèctemint, çou ki fût bén pus vite. Çou a todi prindou pus d' on dmwin d' an po transferer et traiter totes les dnéyes, et nosse-omes avans presk' tot pèrdu a cause d' on corrompou di disque, çou ki vosseut d'mandé di r'cominçer a zéro."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Cisse expérience nosse-omes a fêt creure k' c' est important d' met' cisses dnéyes a l' dispôzicion des ôtes vite, po k' eleus pusseut esse mirroirîyes l' long et l' lård. Nosse-omes n' estans k' a on ou deux incidents mal-timés d' pèrde cisse coledjeye po todi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Li coledjeye"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Aler vite vout dire k' li coledjeye est on pô désorganisêye… Veyans çou. Imaginans k' nosse-omes a on sistinme di fitchîs (ki en réalité nosse-omes divizans a travèrs des torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Li prumîre r'pertwêre, <code>/repository</code>, est li pus structuréye. Cisse r'pertwêre contint des \"mîles dirs\": des r'pertwêres avou chaskeûne on mîle fitchîs, ki sont numérôtés incrémental'mint dins l' base di dnéyes. Li r'pertwêre <code>0</code> contint des fitchîs avou comic_id 0–999, et ainsin di suite."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "C' est li mêm schème k' Library Genesis a stîyant po ses coledjeyes di fiction et di non-fiction. L' idée est k' chaske \"mîle dir\" est automatiquement transformé en torrent dès k' il est rimplî."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Mins, l' opérateur di Libgen.li n' a jamês fêt des torrents po cisse coledjeye, et donc les mîles dirs sont probabl'mint dvins inconvéniants, et ont cédé l' place a des \"dirs nén classîyes\". Cisses sont <code>/comics0</code> a <code>/comics4</code>. Eleus contint totes des structures di r'pertwêres uniques, ki fessèt probabl'mint sins po r'cweri les fitchîs, mins n' fessèt nén trop di sins po nosse-omes asteure. Heureus'mint, li metadata réfère todi dirèctemint a tos cisses fitchîs, donc leus organizåcion di stocådje su disque n' a nén d' importince!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Li metadata est disponibe en forme d' on database MySQL. Cisse polèt esse tèlèchargêye dirèctemint del site web di Libgen.li, mins nosse-omes l' metrons ossi a l' dispôzicion dins on torrent, avou nosse-ome table avou tos les MD5 hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analîse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quand vos recevoz 95TB versés dins vosse cluster di stocådje, vosse-omes esseye di fêt sins di çou k' i gn a d' dins… Nosse-omes avans fêt on pô d' analîse po vey si nosse-omes polrî reduire li taille on pô, come en r'tirant les doublons. Veyans on pô di nosse-ome trouvåyes:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Les doublons sémantiqs (differins scans d' on mêm livre) polèt théorétiquemint esse filtrés, mins c' est malaisé. En r'gardant manuellement dins les comics, nosse-omes avans trové trop di faux positifs."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "I gn a des doublons pur'mint par MD5, çou ki est relativ'mint gaspilleux, mins filtrer cisses vosseut nosse d'né k' on 1% in d' économies. A cisse échelle, çou fêt todi on TB, mins ossi, a cisse échelle, on TB n' a nén vrêmint d' importince. Nosse-omes prèfèront nén risquer di détrure des dnéyes par accident dins cisse procèss."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Nosse-omes avans trové on tas di dnéyes nén-livrès, come des cinémas basés su des comics. Çou semble ossi gaspilleux, pask' cisses sont djå largemint disponibes par des ôtes moyéns. Mins, nosse-omes avans r'alisé k' nosse-omes n' pouvans nén simplement filtrer les fitchîs di cinémas, pask' i gn a ossi des <em>livrès comics interactifs</em> ki ont stî r'lisés su l' computer, ki onk a r'cordé et sôvê come cinémas."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "A la fin, tot çou qu'on pôre disfacer del coleccion n' saveurît ki des p'tits pourcints. Adon, nos r'memrî qu'on est des amasseus d' dnêyes, et les djins qui vont mirer çoula sont ossi des amasseus d' dnêyes, et don, “CO PENSEZ-VOS, DISFACER?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Nos vos prezintans donk li coleccion inteire, sins modifiaedje. C'est ene grosse quantiteye di dnêyes, mins nos espérons qu' assez di djins s'inquièteront poûr l'ensemencer d' tôte manîre."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Rimontêye di fon"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Nos r'lashans ces dnêyes en des gros pèkets. Li prumî torrent est di <code>/comics0</code>, k' nos avans metou en ene grosse fitchî .tar di 12TB. C'est miyeu po vosse disque dur et vosse logicele di torrent k' ene multitude di p'tits fitchîs."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Come pårt di cisse r'lashance, nos faysans ene rimontêye di fon. Nos tchèrchans a r'cwerî 20.000$ po coover les coûts d' fonctinnemint et di contrat po cisse coleccion, et ossi po permete des projets continoues et a v'nir. Nos avans des <em>massîfs</em> en préparation."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Ki est-ce k' vos sopoirtîz avou vosse don?</em> En r'sumint: nos r'servans tot l' connoxhance et l' culture di l' humanité, et nos l' rendans aîse a accesseure. Tot nosse code et dnêyes sont a source ouverte, nos estans on projet géré onk par des volontaires, et nos avans dja sôvê 125TB di livres (en plus des torrents di Libgen et Scihub). A la fin, nos construhons on volan ki permet et incite les djins a trover, scanner, et sôver tot les livres do monde. Nos scrirons a prôpos di nosse plan mestre dins on post a v'nir. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si vos donêz po ene mambèrté di 12 mois “Amazing Archivist” (780$), vos pôvez <strong>“adopter on torrent”</strong>, çou vout dire k' nos metrons vosse no ou messadje dins l' nom d' on des torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Vos pôvez doner en allant a <a %(wikipedia_annas_archive)s>L' Archive d' Anna</a> et en clitchant l' bouton “Doner”. Nos tchèrchans ossi pus di volontaires: ingeniors di logicele, r'cercoures di sécureté, èxperts en marchands anonyms, et traducteurs. Vos pôvez ossi nos sopoirtî en fournissant des sèrvices d' hôtiaedje. Et d' course, s'il vos plait, ensemençez nos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Mierci a tertous ki nos ont dja sopoirtî si généreus'mint! Vos fêtès on vré diférince."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Voci les torrents r'lashés djiça asteure (nos estans co a traitî l' rès):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tos les torrents pôvèt esse trovos su <a %(wikipedia_annas_archive)s>L' Archive d' Anna</a> dzo “Datasets” (nos n' lîons nén dirèct'mint la, po k' les lîens di cisse blogue n' s' fassèt nén oter di Reddit, Twitter, etc). D' la, suivez l' lîen po l' site web Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Cwè ç' ki v'ni?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "On tas di torrents sont bén po l' préservation a long tèrme, mins nén tant po l' accèss d' tchaeke djoû. Nos travayrons avou des partenaires d' hôtiaedje po mète totes ces dnêyes su l' web (pisk' L' Archive d' Anna n' hôte nén rin dirèct'mint). D' course vos pôrrez trover ces lîens di tèlèchargemint su L' Archive d' Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Nos invîtans ossi tertous a fêre des tchôses avou ces dnêyes! Aidîz-nos a miyeu les analijî, les dédoublî, les mète su IPFS, les remixer, formî vos modêles d' IA avou, et ainsin d' suite. C'est tot a vos, et nos n' pôvans nén atinde di vey ki vos fêtès avou."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "A la fin, come dji dit d'vant, nos avans co des r'lashances massîves ki v'ni (si <em>quîqu'un</em> pôre <em>accidintalmint</em> nos evoyî on dump d' onne <em>certaine</em> base di dnêyes ACS4, vos savoz ou nos trover…), et ossi a construhre l' volan po sôver tos les livres do monde."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Adon r'mequez vos, nos n' fêtans k' c'mincî."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x noveas livres apondoues al Miroir di la Biblioteke Pirate (+24TB, 3,8 milion di livres)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Dins l' r'lashance origénale do Miroir di la Biblioteke Pirate (EDIT: displaece a <a %(wikipedia_annas_archive)s>L' Archive d' Anna</a>), nos avans fêt on miroir di Z-Library, ene grosse coleccion di livres ilégale. Come rapèl, voci çou k' nos avans scrît dins cisse blogue origénale:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library est ene biblioteke populêre (et ilégale). I ont prindou la coleccion di Library Genesis et l' ont rendou aîse a tchèrchi. En plus di çoula, i sont dvins très èfècaces a solliciter des noveas contributions di livres, en incitant les contributeurs avou divers avantages. I n' contribou nén ces noveas livres a Library Genesis. Et a diférince di Library Genesis, i n' renden nén leur coleccion aîse a mirer, çou ki empêche ene large préservation. Çoula est important po leur modêle d' afêre, pask' i fêtèt payî po accèder a leur coleccion en gros (pus di 10 livres par djoû)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nos n' fwait nén djihaedje moråls so les cayis d' argent po-z aveur accè a ene coledjhaedje d' livans d' manîre ilégale. C' est s'ns doute ki l' Z-Library a stî on grand sucés po amener l' accè a l' cnoxhance, et po trover pus d' livans. Nos sommes simplement ci po fére nosse part : assurer li long-térmene préservation di cisse coledjhaedje priveye."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Cisse coledjhaedje date del mi-2021. D' tchin, l' Z-Library a grandît a ene vitesse étourdixhante : i ont radjouté près di 3,8 milion di noveas livans. I gn a des doublons la-dins, c' est vrai, mins li majorité d' cisse coledjhaedje semble esse des noveas livans légitimes, ou des scans di pus hôte qualité di livans ki ont stî radjoutés divant. C' est en grande part grâce a l' nombre croxhant di modérateurs volontaires a l' Z-Library, et a leur sistinme d' tèlèchargement en masse avou dédoublonnadje. Nos volans les féliciter po ces réalisations."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Nos sommes contents d' anoncer ki nos avons rçu tos les livans ki ont stî radjoutés a l' Z-Library divins nosse dèrnîre copie et awousse 2022. Nos avons ossu rmoné et raclé des livans ki nos avans raté l' prumîre feis. Tot en tot, cisse novele coledjhaedje est d' près di 24TB, ki est bén pus grand ki l' dèrnîre (7TB). Nosse copie est asteure 31TB tot en tot. D' novea, nos avans dédoublonné avou Library Genesis, paski i gn a ddja des torrents disponibes po cisse coledjhaedje."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Allez a l' Pirate Library Mirror po vey l' novele coledjhaedje (EDIT: displaidjé a <a %(wikipedia_annas_archive)s>Les Archîves d' Anna</a>). I gn a pus d' informåcions la-bas so come les fitchîs sont structuréts, et çou ki a candjî divins l' temps. Nos n' lierans nén a cisse coledjhaedje d' ci, paski c' est simplement on sîte di blog ki n' hoste nén d' matèriels ilégals."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Biensûr, sèmer est ossu ene magnîre fantastike di nos aidî. Mèrci a tertos ki sèment nosse dèrnîre série di torrents. Nos sommes rconnaissants po l' rèsponse positive, et contents ki i gn a tantes di djins ki s' inquètèt po l' préservation di l' cnoxhance et di l' culture d' cisse magnîre inabitwèye."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna et l' èquipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Come dvinir on archivisse pirate"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Li prumîr défi pout esse on surprînant. C' n' est nén on problinme tècnique, ou on problinme légal. C' est on problinme psicologique."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Divant ki nos n' plonxhons dins l' sujèt, deus mîses a djoû so l' Pirate Library Mirror (EDIT: displaidjé a <a %(wikipedia_annas_archive)s>Les Archîves d' Anna</a>) :"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Nos avans rçu des djonåcions fort généreuses. Li prumîr est $10k d' on individu anonyme ki a ossu soutenu \"bookwarrior\", li fondatour originel di Library Genesis. On grand mèrci a bookwarrior po avou facilitî cisse djonåcion. Li deusinme est on ôte $10k d' on djonor anonyme, ki s' est rcontacté après nosse dèrnîre sortidje, et ki a stî inspiré po aidî. Nos avans ossu rçu on bon nombre di p'tites djonåcions. Mèrci fort po vosse soutyin généreux. Nos avans des noveas projèts excitants dins l' pipeline ki cisse djonåcion va soutyinir, alor rstez a l' ècoute."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Nos avans avou des difficoltés tècniques avou li taille di nosse deusinme sortidje, mins nosse torrents sont asteure en ligne et sèment. Nos avans ossu rçu on offre généreux d' on individu anonyme po sèmer nosse coledjhaedje so leurs serveurs a très hôte vitesse, alor nos fèsans on tèlèchargement spècial so leurs machines, après çou, tertos les ôtès ki tèlèchargent la coledjhaedje d'vraient vey on grand amélioressî d' vitesse."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Des livans intîrs poutèt esse scrits so li <em>pourkwè</em> di la préservation digitale en général, et l' archivisse pirate en particulier, mins laissons nos vos dner on p'tit apèrçu po cels ki n' sont nén trop famîliers. Li monde prodjût pus di cnoxhance et di culture ki djamê divant, mins ossu pus di çoula est pèrdu ki djamê divant. L' humanité s' fie grandemint a des corporations come les éditeurs académiques, les sèrvices di streaming, et les compagnes di médias sociaux avou cisse héritadje, et i n' ont s'ns doute nén prouvé esse des grands gardiens. Veyz li documentaire Digital Amnesia, ou vraimint nénk on discourt di Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "I gn a des institutions ki fèsèt on bon boulot d' archiver tot çou ki i poutèt, mins i sont liès par li loi. Come pirates, nos sommes dins onne position unique po archiver des coledjhaedjes ki i n' poutèt nén toucher, a cause di l' enforcement di li copyright ou d' ôtes restricçions. Nos poutans ossu mirer des coledjhaedjes pus d' onne feis, tot a travèrs li monde, çou ki augmente les chances di onne préservation correcte."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Po l' moment, nos n' entrans nén dins des discussions so les avantages et les inconvénients di l' propriété intellectuelle, li moralité di briser li loi, des réflexions so li cinsure, ou li problinme d' accè a l' cnoxhance et a la culture. Avou tot çoula d' hors, plonxhons dins li <em>come</em>. Nos partagérons come nosse èquipe est dvintue des archivisses pirates, et les leçons ki nos avans aprises tot a long di l' tchmin. I gn a des défis nombreux cand vos vos lancîz dins cisse aventure, et nos espérons ki nos poutans vos aidî a traverser quèlkes-unes di ces défis."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Communauté"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Li prumîr défi pout esse on surprînant. C' n' est nén on problinme tècnique, ou on problinme légal. C' est on problinme psicologique : fére cisse ovraedje dins l' ombre pout esse incroyablimint solitêre. D'pendant di çou ki vos plannîz di fére, et di vosse modêle di mènace, vos poutîz d'vwar esse fort prundint. A l' ôte bout di l' spectre, nos avans des djins come Alexandra Elbakyan*, li fondatour di Sci-Hub, ki est fort ouverete so ses activitès. Mins ele est a hôte risc d' esse arristêye si ele visitreut on payis occidental a cisse heure, et poutît risquer des décennies di tèmps d' prîson. Est çoula on risc ki vos serîz prêt a prinde ? Nos sommes a l' ôte bout di l' spectre ; esse fort prundint po nén l'isser d' trace, et avou ene forte sécurité opérationnelle."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Come mentionné so HN pa \"ynno\", Alexandra n' vout nén esse connue a l' oridjin : \"Ses serveurs ont stî configurés po émettre des messages d' erreur détaillés di PHP, inclouant li tchimin complet di l' fitchî source en faute, ki stîve s'ns li répertoire /home/<USER>' utilisateur ki ele avot en ligne so on sîte nén rlié, attaché a son vrai nom. Divant cisse révélation, ele stîve anonyme.\" Alor, eployîz des noms d' utilisateur aléatoires so les ordinateurs ki vos eployîz po cisse afére, au cas ou vos configurîz k'k chose d' manîre incorrecte."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Cisse secrèteté, cepandant, vén avou on coût psicologique. La plupârt des djins aimèt esse rconnu po l' ovraedje ki i fèsèt, et mins vos n' poutîz nén prinde d' crédit po çoula dins l' vî realité. Minsme des tchoses simples poutèt esse des défis, come des amis ki vos dmåndèt çou ki vos avîz stî fêt (a on moment \"bricoler avou m' NAS / homelab\" dvint vî)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "C' est po çoula ki c' est si important di trover onne communauté. Vos poutîz abandonner k'k sécurité opérationnelle en vos confiant a des amis fort proches, ki vos savîz ki vos poutîz avou ene confiance profonde. Minsme alor, essez prundint po nén mète k'k chose par écrit, au cas ou i d'vrît rmettre leurs emails a l' autorité, ou si leurs dispositifs sont compromis d' onne ôte manîre."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Minsme miyeu est di trover des ôtes pirates. Si vosse proches amis sont intéressés a vos rjoindre, fantastike ! Sinons, vos poutîz trover des ôtes en ligne. Malheureusement, c' est toudi onne communauté niche. Tot avou nos avans trové k'k ôtes ki sont actifs dins cisse spèce. Des bons endroets po cmincer semblent esse les forums di Library Genesis, et r/DataHoarder. L' Archive Team a ossu des individus avou des idées similaires, mins i opérèt divins li loi (minsme si dins k'kes zones grises di li loi). Les scènes traditionnelles di \"warez\" et di piratadje ont ossu des djins ki pensent d' manîre similaire."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Nosse sins avou des idêyes po fére crexhe li comunåté et explorer des idêyes. N'hésitîz nén a nos messaedjî so Twitter ou Reddit. P't-êt ki nos pourrîhons houter on forum ou on groupe di tchatches. On des défis c'est ki çoula pout esse censurêz foirt åyîmint quand on eploye des plate-formes courantes, don nos d'vra nos-memes l'houter. I gn a ossi on compromis åd fwait d'aveur ces discutions totålement publiques (avou pus d'engadjîmint possibe) ou d'les fére privêyes (nén lîyî les \"cibes\" possibes ki nos s'preparans a les scrapper). Nos d'vra penser a çoula. Fait-le nos saveur si vos estoz intrêssé dins çoula!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projets"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quand nos faysans on projet, i gn a onk des phases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Tchoeix do domêne / philosophie: Où voloz-vos vos concentrer grosso modo, et pourquoi? Quelles sont vos passions, compétences, et circonstances uniques ki vos pouhez eployî a vosse profit?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Tchoeix do cible: Quèle collection spècefike voloz-vos mirer?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Scrapî les metadata: Cataloguer les infôrmåcions a propô des fitchîs, sins les djiçharger (ki sont sovint pus grands)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Tchoeix des dônêyes: Basé so les metadata, rétrécî l'choix des dônêyes les pus relévantes a archiver asteure. Çoula pout esse tot, mins sovint i gn a on môyén résonåbe di sôvri l'plèce et l'bandwidth."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Scrapî les dônêyes: Djiçharger les dônêyes."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribucion: Embaler çoula dins des torrents, l'anoncer a on endroet, fére les djins l'partager."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ces phases n'sont nén totålement indépendantes, et sovint des insîts d'onne phase pus tard vos r'viyint a onne phase pus tôt. Par egzimpe, pendant l'scrapî des metadata vos pout realizeur ki l'cible ki vos avoz tchoezi a des mécanismes défensifs ki sont d'pus hôt ki vosse nivêye di compétence (come des bloks IP), don vos r'viyint et tchoezîz onne ôte cible."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Tchoeix do domêne / philosophie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "I n'manke nén d'connaissances et d'patrimoine culturel a sôvri, çoula pout esse accablant. C'est pour çoula ki c'est sovint utile di prinde on moment et penser a c'ki vosse contribution pout esse."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Chaskeun a onne manîre différente di penser a çoula, mins v'la kékès questions ki vos pouhez vos poser:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Pourquoi estoz-vos intrêssé dins çoula? Qu'est-ce ki vos passionne? Si nos pouhons avouner on tas di djins ki archivent les tchôzes ki les intéressèt spécifiquement, çoula couvrirèt on grand tot! Vos savrîz onk pus ki l'persone moyene a propô d'vosse passion, come qu'est-ce ki c'est des dônêyes importantes a sôvri, qu'est-ce ki sont les meyeures collections et comunåtés en ligne, et ainsin di suite."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quelles compétences avoz-vos ki vos pouhez eployî a vosse profit? Par egzimpe, si vos estoz on expert en sécurité en ligne, vos pouhez trover des môyéns di déjouer des bloks IP pour des cibles sécurisées. Si vos estoz bon a organiser des comunåtés, p't-êt ki vos pouhez rassembler des djins autour d'on but. C'est utile di saveur on p'tit d'programacion, mêm s'c'est just pour garder onne bone sécurité opérationnelle tot au long d'ce process."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Combinne di tins avoz-vos pour çoula? Nosse conseil serèt di cminçer ptit et fére des projets pus grands come vos prindez l'coup, mins çoula pout d'vni tot-absorbant."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Qu'est-ce ki serèt on domêne a hôte levier a s'concentrer? Si vos estoz a passer X heures a archiver des pirates, don comment pouhez-vos avouner l'pus grand \"bang pour vosse buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quelles sont les manîres uniques ki vos pensez a çoula? Vos pouhez aveur des idêyes ou approches intéressantes ki les ôtès ont p't-êt raté."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Dins nosse cas, nos étions particulièrement concernés par l'préservation a long terme d'la science. Nos savions a propô d'Library Genesis, et comment i stî miré tot a fwait pus d'onne feye avou des torrents. Nos aimions çoula. Pu on djoû, on d'nos a essayé di trover des manuels scientifiques so Library Genesis, mins n'a nén pu les trover, mettant en doute comment complet i stî réelement. Nos avans djiçhargé ces manuels en ligne, et les avans trové a d'ôtes endroets, çoula a planté l'graine pour nosse projet. Mêm divant ki nos savions a propô d'la Z-Library, nos avans l'idêye di nén essayer di rassembler tos ces livres manuellement, mins di s'concentrer a mirer des collections existantes, et d'les contribuer a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Tchoeix do cible"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Donc, nos avons no prézone ki nos estans åyant, asteure kelès colèccion spécifike nos mirerans? I gn a deus troes cayets ki fêt on bon targe:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unique: nén djà bén couvert par d'ôtes projès."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accèssibe: n' eploye nén d' tches d' strates di proteccion po vos empitchî d' scraper leus metadata et d' données."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Insegnemint spècial: vos avoz des infôrmåcions spèciales so ç' targe, come si vos avoz on accèss spècial a cisse colèccion, ou bin vos avoz trové come dèfendre leus défenses. Çoula n' est nén nècèssaire (nosse projèt ki vén n' fêt nén d' çoula), mins çoula aidêye!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quand nos avans trové nosse manuels di sciense so des setes web ôtes ki Library Genesis, nos avans essayé d' comprinde come il ont fêt po s' r'trouver so l' inteurnete. Nos avans alor trové l' Z-Library, et nos avans comprindou ki mins des livres n' fêt nén leus prumîre aparicion là, il s' r'trouvèt totavåy là. Nos avans aprindou so s' rapôrt a Library Genesis, et l' structûre d' incitåcion (financière) et l' infèrface d' usêyeu supèrieure, totes deus ki l' ont fêt on colèccion pus complète. Nos avans alor fêt on scrapping préliminêre di metadata et di données, et nos avans comprindou ki nos pouvans passer les limites di tèlèchargement d' IP, en profitant d' l' accèss spècial d' on des nosse mambes a on grand nombre di serveurs proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Come vos explorez des targes diférintes, c' est djà important d' cacher vos traces en eployant des VPNs et des adresses email jetåblles, dont nos parlerans pus târd."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Scrapping di metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Rindons-nos on ptit peu pus tècnique ici. Po scrapper les metadata dès setes web, nos avans tins les cayets assez simples. Nos eployans des scripts Python, des côps curl, et ene base di données MySQL po stôker les résultats dins. Nos n' avans nén eployé d' logiciêls di scrapping sophistiqués ki pôrèt mapper des setes web complexes, dès ki nos n' avans ki besoin di scrapper on ou deus cayets di pages en énumérant les ids et en parsant l' HTML. S' i gn a nén des pages facilement énuméråves, alor vos pôrîz avouèr besoin d' on crawler propre ki tcheye a trover totes les pages."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Avånt d' cômincer a scrapper on sete web inteur, èssayîz d' l' fêre a l' måye po on ptit tchin. Passez vos-méme a tru des douzènes di pages, po vos d'ner on sinsi po come çoula fonce. D' côp vos r'trouverîz djà des blokes d' IP ou d' ôtes comportements intèressants d' cisse manîre. Li minme va po le scrapping di données: avånt d' s' plonger trop prondément dins ç' targe, assurez-vos ki vos pôvez èfèctivemint tèlècharger ses données."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Po passer les restriccions, i gn a deus troes cayets ki vos pôrîz èssayer. I gn a-t-i des ôtes adresses IP ou serveurs ki hôtent les minmes données mins n' ont nén les minmes restriccions? I gn a-t-i des points d' accèss API ki n' ont nén di restriccions, mins d' ôtes i gn a? A kel râte di tèlèchargement est-ce ki vosse IP est bloquée, et po kel lonktemps? Ou bin vos n' est nén bloqué mins raletî? Et si vos crêyez on conte d' usêyeu, come çoula tcheye-t-i? Pôvez-vos eployî li HTTP/2 po tinir les connèxions ouveres, et çoula augmente-t-i li râte a kel vos pôvez d'mander des pages? I gn a-t-i des pages ki listèt des fichiers multiples a l' côp, et les infôrmåcions listêyes là sont-eles sufisantes?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Les cayets ki vos volrîz probåbilmint stôker inclut:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Tite"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nom di fichier / locåcion"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pôrèt esse on ID interne, mins des IDs come ISBN ou DOI sont utiles ossi."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Taille: po kalkuler comint d' plèce di disk vos avoz besoin."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): po confirmer ki vos avoz tèlèchargé li fichier proprement."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Date d' apondou/modifié: po ki vos pôvez r'vni pus târd et tèlècharger des fichiers ki vos n' avîz nén tèlèchargé divant (bin ki vos pôvez ossi eployî l' ID ou li hash po çoula)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrîcion, catégorie, mots-clés, ôteurs, lingaedje, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Nos fêtans çoula d' habitude en deus étapes. Prumî, nos tèlèchargons les fichiers HTML bruts, d' habitude divins MySQL (po èviter on grand nombre di ptits fichiers, dont nos parlerans pus bas). Alor, dins on pas séparé, nos passons a tru ces fichiers HTML et les parsans dins des tables MySQL réelles. D' cisse manîre vos n' avoz nén besoin di r'tèlècharger tot d' zéro si vos d'couvrez on èrreu dins vosse code di parsing, dès ki vos pôvez r'traiter les fichiers HTML avou l' novea code. Çoula est ossi d' habitude pus fâcile a paralleliser li pas di traitement, çoula sauve dès côps du tins (et vos pôvez scrire li code di traitement pândant ki li scrapping est en court, au lieu d' d'vni scrire les deus étapes a l' côp)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "A la find, notez ki po d' certins cayets, l' scrijhaedje di metadata c' est tot ki gn a. I gn a des grandès colecs di metadata ki n' sont nén bin prezervêyes."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Tchoezi des dnêyes"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Sovint, vos poloz eployî l' metadata po trover ene bonna sôrt di dnêyes a djihe. Mins, mêm si vos voloz djihe totes les dnêyes a l' fin, c' est bén d' prumîrîzî les pus importants, po l' cas k' vos seriez djihaedjîs et ki les définses s' améliores, ou bén pask' vos d'vrîz achyeter pus d' disques, ou simplèmint pask' on ôte cayet s' passereut dins vosse veye divant k' vos n' ayez djihaedjî tot."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Par egzimpe, ene coleccion pout aveur des editions multiples d' l' minme ressource di fond (come on live ou on film), avou ene ki c' est markêye come l' miyeure qualité. Djiher cès editions la d' prumîr fereut bén du sens. Vos voloz p't-êt djiher totes les editions a l' fin, pask' dins d' cèrts cas, l' metadata pout esse mal taguêye, ou i pout aveur des compromis inconnus inte les editions (par egzimpe, l' \"miyeure edition\" pout esse l' miyeure dins l' pus d' manîres mins pus måvêye dins d' ôtes, come on film avou ene pus hôte résolution mins sans sôstitres)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Vos poloz ossu tcherter dins vosse database di metadata po trover des cayets d' intêret. Cwè c' est l' pus grand fitchî ki c' est hôsté, et po kwa c' est si grand? Cwè c' est l' pus ptit fitchî? I gn a-t-i des modêles d' intêret ou d' surprîse po c' ki c' est d' cèrtes categories, lingaedjes, et ainsin d' suite? I gn a-t-i des titlès doublants ou foirt simblants? I gn a-t-i des modêles po l' djoû k' les dnêyes ont stî radjoutêyes, come on djoû ou on grand nombre di fitchîs ont stî radjoutêys d' on côp? Vos poloz sovint aprinde on grand côp en r'gardant l' dataset d' diférintes manîres."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Dins nosse cas, nos avons dédoublé les livres di Z-Library avou les hashes md5 dins Library Genesis, çou ki nos a fêt r'côper on grand côp d' tins di djihaedje et d' plaece di disque. C' est on cas foirt unique, mins. Dins l' pus d' cas, i gn a nén d' databases complètes di cwès fitchîs sont dja bin prezervés pa d' ôtes pirates. C' est ene grand' chance po k' quîqu' onk a l' ôte. C' serè grand d' aveur on r'côpî d' tchin a tchin d' cayets come l' musique et les films ki sont dja foirt semêyes so les setes di torrent, et ki sont d' pus basse priorité po inclure dins les mirroirs pirates."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scrijhaedje des dnêyes"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Asteure vos estoz pret a djiher les dnêyes en gros. Come dit divant, a cisse étape, vos d'vrîz dja aveur djihaedjî a la mwin on bon nombre di fitchîs a la man, po miye comprinde l' comportemint et les ristrixhons di l' cayet. Mins, i gn a toudi des surprîses ki vos atindront onk vos djihaedjroz on grand nombre di fitchîs a l' côp."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nosse conseil ci c' est d' r'ster simpl' come bon djoû. Cmincez pa djiher on bon nombre di fitchîs. Vos poloz eployî Python, et pîs elargî a des fils multiples. Mins, d' côp, c' est mêm pus simpl' di générér des fitchîs Bash direktemint a partir di l' database, et pîs d' les r'côrer en multiples fênetes di terminal po agrandir. On ptit truc teknik ki vout l' pène d' esse mensionné ci c' est d' eployî OUTFILE dins MySQL, ki vos poloz scrire n' importa ou si vos désactivîz \"secure_file_priv\" dins mysqld.cnf (et n' oublîz nén d' désactivî/contourner AppArmor si vos estoz so Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Nos stockans les dnêyes so des simples disques durs. Cmincez avou çou k' vos avez, et agrandîz ptit a ptit. C' pout esse accablant d' penser a stocker des centaines di TBs di dnêyes. Si c' est l' situacion k' vos affrontez, mettez d' hors ene bonna sôrt d' prumîr, et dins vosse anoncîye dimandez d' aidance po stocker l' r'côp. Si vos voloz aveur pus d' disques durs vos-mêm, r/DataHoarder a des bons rissources po trover des bons marchés."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Tâchez d' nén trop vos tracasser avou des systèmes di fitchîs sophistiqués. C' est aisé di tomber dins l' piège di metter en plaece des cayets come ZFS. On ptit détail teknik a savouèr, c' est k' on grand nombre di systèmes di fitchîs n' gèrent nén bén on grand nombre di fitchîs. Nos avans trové k' on simple contournemint c' est d' créer des directories multiples, p. ex. po des renges d' ID diférintes ou des préfixes di hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Après aveur djihaedjî les dnêyes, n' oublîz nén di vérer l' intégrité des fitchîs avou les hashes dins l' metadata, si i gn a."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribucion"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Vos avez les dnêyes, çou ki vos dône l' possession du prumîr mirroir pirate du cayet (probablèmint). D' on côp, l' pus dur c' est passé, mins l' pus riské c' est d' vant vos. Après tot, d' tchin a tchin, vos avîz stî discret; volé d' sô l' radar. Tot çou k' vos avîz a fêre c' est d' eployî on bon VPN tot l' tins, nén rimplî vosseus dnêyes personèles dins des formes (évidint), et p't-êt eployî ene session di naviguêye spèciale (ou mêm on ôte ordinatheur)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Asteure vos d'vîz distribuer les dnêyes. Dins nosse cas, nos volîz d' prumîr contribuwer les livres a Library Genesis, mins nos avans vîte dîcouvert les difficoltés dins çoula (tri fiction vs non-fiction). Dji nos avans dîcidé di distribuer avou des torrents a l' môde Library Genesis. Si vos avez l' chance di contribuwer a on projet dja existant, çoula pout vos fêre r'côper on grand côp d' tins. Mins, i gn a nén foirt d' mirroirs pirates bin organisés a l' ôte."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Admettons k' vos dîcidîz di distribuer des torrents vos-mêm. Tâchez d' r'nder cès fitchîs ptits, po k' i sont aîsîs a mirroir so d' ôtes setes. Vos d'vîz pîs semer les torrents vos-mêm, tot en r'stant anonyme. Vos poloz eployî on VPN (avou ou sans redirijhaedje di port), ou payî avou des Bitcoins mélangés po on Seedbox. Si vos n' savoz nén cwè k' cèrts d' cès termes vout dire, vos aurez on bonne dose di lèture a fêre, pask' c' est important k' vos comprenîz les compromis di risk ci."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Vos poloz hôster les fitchîs di torrent a l' minme so des setes di torrent existants. Dins nosse cas, nos avans dîcidé d' hôster on sete, pask' nos volîz ossu r'pendre nosse philosophie d' onne manîre clêre. Vos poloz fêre çoula vos-mêm d' onne manîre simblante (nos eployans Njalla po nosse domains et hôstî, payî avou des Bitcoins mélangés), mins n' hésitîz nén di nos contakter po k' nos hôstîz vosse torrents. Nos espérons bâtir on index compréhensif di mirroirs pirates a l' long, si cisse idée prind."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Po l' tchoezi d' VPN, i gn a dja stî scrît on grand côp a çou sujet, donc nos r'pètons l' conseil général di tchoezi pa réputation. Des politiques di nén-loger testêyes en court avou des longes histwères di proteccion di l' privacité c' est l' pus bas risk, a nosse avis. Notez k' mêm quand vos fêt tot bén, vos n' poloz jamês atindre on risk zéro. Par egzimpe, quand vos semîz vosse torrents, on acteur d' l' état-nation foirt motivé pout probablèmint r'gardî les flux di dnêyes entrants et sortants po les serveurs VPN, et d' ducer ki vos estoz. Ou vos poloz simplèmint fêre ene érwère. Nos l' avans probablèmint dja fêt, et nos l' ferons d' nûviau. Heureus'mint, les états-nations n' s' tracassèt nén <em>trop</em> d' l' pirat'te."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Onne dîcision a prinde po chas projet, c' est si vos l' publîroz avou l' minme identité k' divant, ou nén. Si vos continouhez a eployî l' minme no, les érwères dins l' sécurité opérationnelle di projets divant pôront vos r'v'nîr. Mins publîyer d' sô l' ôtes nos vout dire k' vos n' bâtîz nén ene réputation ki dure pus longtin. Nos avans tchoezi d' aveur ene forte sécurité opérationnelle dès l' cmincemint po k' nos poloz continouher a eployî l' minme identité, mins nos n' hésiterons nén a publîyer d' sô l' ôte no si nos fêt onne érwère ou si les circonstances l' d'mandèt."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Fêre savouèr l' cayet pout esse difficil. Come nos l' avans dit, c' est toudi ene comunauté niche. Nos avans posté d' prumîr so Reddit, mins nos avans r'çu d' l' traction so Hacker News. Po l' momenet, nosse recommandacion c' est di l' poster a cèrts endroets et d' vey k' çou ki s' passera. Et d' nûviau, contaktez nos. Nos aimerîz r'pendre l' mot d' pus d' efforts d' archivisme pirate."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Concluzion"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Espérons que ceci soit utile pour les archivistes pirates débutants. Nous sommes ravis de vous accueillir dans ce monde, alors n'hésitez pas à nous contacter. Préservons autant que possible les connaissances et la culture du monde, et diffusons-les largement."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna et l' èquipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Présentation du Miroir de la Bibliothèque Pirate : Préserver 7 To de livres (qui ne sont pas dans Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Ce projet (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>) vise à contribuer à la préservation et à la libération des connaissances humaines. Nous apportons notre petite et humble contribution, dans les pas des grands qui nous ont précédés."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "L'objectif de ce projet est illustré par son nom :"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirate</strong> - Nous violons délibérément la loi sur le droit d'auteur dans la plupart des pays. Cela nous permet de faire quelque chose que les entités légales ne peuvent pas faire : s'assurer que les livres sont diffusés largement."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliothèque</strong> - Comme la plupart des bibliothèques, nous nous concentrons principalement sur les matériaux écrits comme les livres. Nous pourrions nous étendre à d'autres types de médias à l'avenir."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Miroir</strong> - Nous sommes strictement un miroir de bibliothèques existantes. Nous nous concentrons sur la préservation, pas sur la facilité de recherche et de téléchargement des livres (accès) ou sur la création d'une grande communauté de personnes qui contribuent de nouveaux livres (approvisionnement)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La première bibliothèque que nous avons reproduite est Z-Library. C'est une bibliothèque populaire (et illégale). Ils ont pris la collection de Library Genesis et l'ont rendue facilement consultable. En plus de cela, ils sont devenus très efficaces pour solliciter de nouvelles contributions de livres, en incitant les utilisateurs contributeurs avec divers avantages. Actuellement, ils ne contribuent pas ces nouveaux livres à Library Genesis. Et contrairement à Library Genesis, ils ne rendent pas leur collection facilement reproductible, ce qui empêche une large préservation. Cela est important pour leur modèle économique, car ils facturent l'accès à leur collection en gros (plus de 10 livres par jour)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nos n' fwait nén djihaedje moråls so les cayis d' argent po-z aveur accè a ene coledjhaedje d' livans d' manîre ilégale. C' est s'ns doute ki l' Z-Library a stî on grand sucés po amener l' accè a l' cnoxhance, et po trover pus d' livans. Nos sommes simplement ci po fére nosse part : assurer li long-térmene préservation di cisse coledjhaedje priveye."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Nous aimerions vous inviter à aider à préserver et libérer les connaissances humaines en téléchargeant et en partageant nos torrents. Consultez la page du projet pour plus d'informations sur la façon dont les données sont organisées."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Nous vous invitons également à contribuer avec vos idées sur les collections à reproduire ensuite, et comment procéder. Ensemble, nous pouvons accomplir beaucoup. Ceci n'est qu'une petite contribution parmi tant d'autres. Merci, pour tout ce que vous faites."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna et l' èquipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nous ne faisons pas de lien vers les fichiers depuis ce blog. Veuillez les trouver vous-même.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump ISBNdb, ou Combien de Livres Sont Préservés Pour Toujours ?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si nous devions correctement dédupliquer les fichiers des bibliothèques de l'ombre, quel pourcentage de tous les livres du monde avons-nous préservé ?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Avec le Miroir de la Bibliothèque Pirate (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>), notre objectif est de prendre tous les livres du monde, et de les préserver pour toujours.<sup>1</sup> Entre nos torrents Z-Library, et les torrents originaux de Library Genesis, nous avons 11 783 153 fichiers. Mais combien cela représente-t-il vraiment ? Si nous dédupliquions correctement ces fichiers, quel pourcentage de tous les livres du monde avons-nous préservé ? Nous aimerions vraiment avoir quelque chose comme ceci :"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of l'héritage écrit de l'humanité préservé pour toujours"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Pour un pourcentage, nous avons besoin d'un dénominateur : le nombre total de livres jamais publiés.<sup>2</sup> Avant la disparition de Google Books, un ingénieur du projet, Leonid Taycher, <a %(booksearch_blogspot)s>a essayé d'estimer</a> ce nombre. Il est arrivé — avec humour — à 129 864 880 (« au moins jusqu'à dimanche »). Il a estimé ce nombre en construisant une base de données unifiée de tous les livres du monde. Pour cela, il a rassemblé différents ensembles de données et les a ensuite fusionnés de diverses manières."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Come ene ene, i gn a ene ki a vouté d' rascantchî tos les live del monde: Aaron Swartz, l' activisse numerike ki n' est pus et co-fondåve di Reddit.<sup>3</sup> Il a <a %(youtube)s>cmincî l' Open Library</a> avou l' but d' aveur “ene pådje web po tot lès live ki ont stî publyîs”, en rascantchant des dnêyes di sacwants sôces differintes. Il a feni pa payî l' prix ultime po s' ovraedje di prumîcion numerike cand il a stî porsuivi po avou tèlchargî on grand nombre di papîs academikes, çou ki l' a mené a s' suicider. Inutile di dire, c' est ene des rasons po lesquelles nosse groupe est pseudonime, et po lesquelles nos-omes très prudints. L' Open Library est todi heroikement géré pa des djins a l' Internet Archive, continouant l' héritådje d' Aaron. Nos rvinrins a çoula pus tard dins cisse pådje."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Dins l' pådje di blog di Google, Taycher disribe sacwants des défis avou l' estimåcion di cisse tchifre. D' abord, k' est-ce ki constite on live? I gn a sacwants definicions possibes:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copies fysikes.</strong> D' evidince, çoula n' est nén très aidant, paski c' n' sont ki des duplicats do minme matérial. Ç' serèst magnifike si nos pouvans prumîr totes les anotåcions ki les djins fêt dins les live, come les fameus “gribouillès dins les mårges” di Fermat. Mès hélas, çoula ristera on rêve d' archivisse."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Ovraedjes”.</strong> Come “Harry Potter et la Chambre des Secrets” come on concèpte logike, ki englobe totes les vèrsions, come les differintes traducs et réimpressions. Çoula est onk a ene definicion utile, mès çoula pout esse difficille di dîre k' est-ce ki conte. Par egzimpe, nos volrîens prumîr les differintes traducs, mès les réimpressions avou des p'tites differinces n' sont p't-ête nén si importåntes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Édicions”.</strong> Ci vos contez tchaeke vèrsion unique d' on live. Si k' k' chose est differint, come ene coverture differinte ou ene préface differinte, çoula conte come ene ôte édicion."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Fitchîs.</strong> Cand vos travayîz avou des bibliotèques d' ômbre come Library Genesis, Sci-Hub, ou Z-Library, i gn a ene ôte considération. I pout aveur sacwants scans di l' minme édicion. Et les djins poutèt fé des mîyès vèrsions des fitchîs ki egzistèt, en scannant l' tecs avou l' OCR, ou en rectifiant des pådjes ki ont stî scannêyes avou on angle. Nos volrîens contez ces fitchîs come ene seule édicion, çoula demanderèst ene bone metadata, ou ene déduplication avou des mesures di similitude di documint."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Les “Édicions” s' avrant l' definicion l' pus pratique di çou ki sont les “live”. Par bonhure, cisse definicion est ossi eployêye po assigner des numéros ISBN uniques. On ISBN, ou Numéro Standard Internåcionål di Live, est sovint eployé po l' commerce internåcionål, paski c' est integré avou l' sistinme di code-barres internåcionål (“Numéro d' Article Internåcionål”). Si vos voloz vinde on live dins les magasins, i l' faut on code-barres, donc vos obtindrez on ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "L' pådje di blog di Taycher mentionne ki bin ki les ISBNs sont utiles, i n' sont nén universels, paski i n' ont stî vrêment adôptés ki dins les années septante, et nén partot åtour do monde. Mins, l' ISBN est p't-ête l' identifiant l' pus largement eployé po les édicions di live, donc c' est nosse mîyur point di départ. Si nos pouvans trover tos les ISBNs do monde, nos obtindrons ene liste utile di çou ki live ont stî prumîs."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Donc, d' ou provint les dnêyes? I gn a ene série d' efforts ki tentèt di compiler ene liste di tos les live do monde:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Après tot, i ont fé cisse recheurche po Google Books. Mins, leus metadata n' est nén accèssibe en gros et est assez difficille a gratter."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Come mentionné divant, c' est leus mission entière. I ont sourcé ene grande quantité di dnêyes di bibliotèques di bibliotèques coopérantes et d' archives nationales, et continouèt a fé çoula. I ont ossi des bibliotéquaires volontaires et ene èquipe tecnique ki tentèt di dédupliquer les records, et di les taguer avou totes sortes di metadata. L' pus biau, c' est ki leus dataset est complètement ouver. Vos poloz simplement <a %(openlibrary)s>tèlcharger çoula</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> C' est on site web géré pa l' OCLC, ene a.s.b.l., ki vinde des sistinmes di gestion di bibliotèques. I agrégèt des metadata di live di sacwants bibliotèques, et les rinde disponibes via l' site web WorldCat. Mins, i fêt ossi des sous avou çoula, donc çoula n' est nén disponibe po tèlcharger en gros. I ont sacwants datasets en gros pus limités disponibes po tèlcharger, en coopéracion avou des bibliotèques spécifiques."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> C' est l' sujèt di cisse pådje di blog. ISBNdb gratte sacwants sites web po des metadata di live, en particulier des dnêyes di prix, ki i vindèt après a des vindeus di live, po ki i pôz fixer leus prix en accôrdance avou l' rès do marché. Paski les ISBNs sont assez universels asteure, i ont effictivmint construit ene “pådje web po tot lès live”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Diveres sistinmes di bibliotèques individuels et archives.</strong> I gn a des bibliotèques et archives ki n' ont nén stî indexêyes et agrégêyes pa nén des ci-dessus, sovint paski i sont sous-financêyes, ou po d' ôtes rasons n' volèt nén partadjer leus dnêyes avou l' Open Library, l' OCLC, Google, et ainsin d' suite. Ene grande partie di çoula a des records numerikes accèssibes via l' internet, et i sont sovint nén très bin protejés, donc si vos voloz aidî et aveur on p'tit plaeir a aprinde des sistinmes di bibliotèques bizarres, çoula sont des bons points di départ."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Dins cisse pådje, nos-omes contents d' anoncer ene p'tite sortidje (comparé a nosse sortidje précedinte di Z-Library). Nos avans gratté l' pus grand partie di ISBNdb, et avans rindu les dnêyes disponibes po tèlcharger en torrent so l' site web di Pirate Library Mirror (EDIT: déplacé a <a %(wikipedia_annas_archive)s>l' Archive d' Anna</a>; nos n' l' lierons nén directemint ici, cherchez çoula). Çoula sont a peu près 30,9 millions di records (20GB come <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzipped). So leus site web i prétindèt ki i ont en fait 32,6 millions di records, donc nos avans p't-ête raté sacwants, ou <em>i</em> pôz fé onk a ene érè. D' tôte manîre, po l' moment nos n' partadjerons nén precisemint come nos avans fé çoula — nos l' l' risterons come on exèrcice po l' lîjeur. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Çou ki nos partadjerons, c' est ene analîse préliminaire, po tenter di s' raprocher di l' estimåcion do nombre di live do monde. Nos avans rguindé a troes datasets: cisse novele dataset di ISBNdb, nosse sortidje originale di metadata ki nos avans gratté di la bibliotèque d' ômbre Z-Library (ki inclut Library Genesis), et l' dump di dnêyes di l' Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Comminçons avou sacwants tchifres approximatifs:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Dins Z-Library/Libgen et Open Library, i gn a pus di live ki d' ISBNs uniques. Est-ce ki çoula vout dire ki ene grande partie di ces live n' ont nén d' ISBNs, ou est-ce ki les metadata d' ISBNs sont simplement manquants? Nos pôz probablement rpondre a cisse question avou ene combinåcion di matchinge automatike basêye so d' ôtes attributs (titro, ôteur, éditeur, etc.), en rascantchant pus di sôces di dnêyes, et en extrayant les ISBNs des scans di live minmes (dins l' cas di Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Combin di ces ISBNs sont uniques? Çoula est mîyès illustré avou on diagramme de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Po esse pus précis:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Nos a stî rmetous del ptit nombra d'aspougnîs! ISBNdb a ene grandèsse d'ISBNs ki n' s' trovèt nén dins Z-Library ni dins Open Library, et c' est l'même (a ene måjheure mès todi bén grandèsse) po les deus ôtes. Çoula djihe ene tchôke di noveas cwistions. Combin ki l' matchaedje ôtomatisé aidreût a metter des tches d'ISBNs so les live k' n' avént nén d'ISBNs? I gn arogneut-î ene grandèsse d' matchaedjes et d'aspougnîs? Et ossi, k' est-ce ki s' passeréye si nos amonrîyons ene 4e ou 5e dataset? Combin d'aspougnîs nos veyrîyons adon?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Çoula nos dône ene poyinte di départ. Nos polans asteure r'viyî totes les ISBNs ki n' étént nén dins l'dataset di Z-Library, et ki n' matchèt nén les tchamps di titro/ôteu. Çoula nos dône ene manire di préservî tos les live do monde: d' prumî, en scraptchant l' inteurnete po des scans, et pî en-aleut en realité po scanner des live. L' dnier polèt mêm esse crowd-fundé, ou bin mené pa des \"bounties\" di djins ki vourént vey des live particulîs digitizés. Tote çoula c' est ene ôte istwere po on ôte moment."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si vos voloz aidî avou çoula — pus d'analyses; scraptchî pus di metadata; trover pus di live; OCRî des live; fé çoula po des ôtes domènes (come des papîs, des audiobooks, des cinés, des émissions di tèlè, des magazinnes) ou mêm metter onk des d' cisse dnéyes a dispôzicion po des tchôses come ML / grandèsse modéle di lingaedje — plait m' contakter (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si vos estoz particulîrmint intressé pa l' analyse di dnéyes, nos estans a tcherchî a metter nos datasets et scripts a dispôzicion dins on format pus aîse a eployî. Ç' s'rait magnîfique si vos pouvîz djuste fork on notebook et cminçî a djouer avou çoula."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "A l' fin, si vos voloz sopoirtî çisse ovraedje, plait a penser a fé on doun. C' est on projet k' est tot a volontîres, et vosse contribucion fwait on grand diférince. Tchaeke p'tit côp aidî. Po l' moment nos prindans des douns en crypto; veyîz l' pådje di Doun a l' Archive d'Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna et l' èquipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Po ene difinicion résonåb di \"po todi\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. D' sûr, l' héritådje scrît d' l' umanité c' est bén pus ki des live, sirtou asteure. Po l' bût di cisse publecåcion et nos dnierès sortidjes nos s' focalizans so les live, mès nos intérèts vont pus loin."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. I gn a ene grandèsse di pus ki nos polans dire a pwé d'Aaron Swartz, mès nos volans djuste l' mensionner brèvemint, pask' i djoue on rouwé central dins cisse istwere. Come l' tins passe, pus di djins poutèt tombî so s' no prumîre feye, et pî s' plonger dins l' trou di lapin el-mêm."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Li fénetre criticåle des bibliotèques d' ômbre"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Comint polans nos prétinde préservî nos colèctes po todi, k' i sont dja a r'prôchî 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Version chinwèsse 中文版</a>, discutaedje so <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "A l' Archive d'Anna, nos estans sovint dmindés comint nos polans prétinde préservî nos colèctes po todi, k' l' grandèsse totåle est dja a r'prôchî 1 Petabyte (1000 TB), et k' i cresse todi. Dins cisse årtike nos r'viyrons nosse filosofeye, et veyrons poqwè l' dîcène ki vént est criticåle po nosse mîssion di préservî l' cnoxhance et l' cultur d' l' umanité."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Li <a %(annas_archive_stats)s>grandèsse totåle</a> di nos colèctes, des dnierès måyes, divizêye pa l' nombra di seeders di torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorités"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Poqwè nos s' inportans tant des papîs et des live? Mettons a costé nosse croyance fondamintåle dins l' préservåcion en général — nos pourrins scrire on ôte publecåcion a çoula. Adon poqwè des papîs et des live particulîrmint? Li réponsse est simpl' : <strong>densité d' informåcion</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Po megabyte di stocådje, l' tecs scrît stoke li pus grandèsse d' informåcion di turtos les médias. Bin k' nos s' inportans des deus, cnoxhance et cultur, nos s' inportans pus d' l' prumî. D' manîre generåle, nos trovans ene iérarchie di densité d' informåcion et d' importince di préservåcion ki r'semble a çoula:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Papîs académiques, djournals, rapôrts"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dnéyes organikes come des séquences d' ADN, des sémences di plantes, ou des samples microbiens"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Live di nén-fictîon"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Code di programe di sciense & ingenierye"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Données di mesure come les mesures scientifiques, données économiques, rapôrts corporatifs"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sicîtes web di sciense & ingenierye, discussions en ligne"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Magazines, djournals, manuels di non-ficcion"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcripts di non-ficcion di causeries, documentaires, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Données internes di corporacions ou gouvernemints (fuites)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Records di metadata en général (di non-ficcion et ficcion; d'ôtes médias, art, djins, etc.; incluzant les revues)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Données géographiques (p.ex. cartes, enquêtes géologiques)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripts di procédures légales ou judiciaires"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versions fictives ou di divertissement di tot çou ci-dessus"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Li classement dins cisse liste est on pô arbitraire — diès categories sont ex-aequo ou i gn a des désacords dins nosse équipe — et nos-ôt's oublions p't-êt des categories importants. Mins c'est grosso modo come nos-ôt's priorizons."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Certins di ces items sont trop differins des ôtes po nos-ôt's s'inquiéter (ou sont djå pris en charge pa d'ôtes institutions), come les données organiques ou géographiques. Mins la plupârt des items dins cisse liste sont vraimint importants po nos-ôt's."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "On ôte grand facteur dins nosse priorizåcion c'est l'risque qu'on certain ôvraedje est à. Nos-ôt's préferons s'focuser solès ôvraedjes qui sont :"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rares"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniquement sous-focusés"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniquement à risque di destruction (p.ex. pa guerre, coupes di financement, procès, ou persécution politique)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmint, nos-ôt's s'inquiètont di l'ampleur. Nos-ôt's avans on temps et des moyens limités, donc nos-ôt's préfererions passer on mois à sauver 10,000 livres qu'1,000 livres — s'ils sont à peu près d'valeur égale et à risque."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibiotèques d'ombre"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "I gn a-st-onk des organisåcions ki ont des missions et des priorities ki s'raportèt. D'vraiy, i gn a des bibiotèques, des archives, des labos, des musêyes, et d'ôtes instituts k' ont l'charge di préservåcion d' cisse manîre. Mins, i gn a èn grand måke: li sistinme légål."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "C' est la k' les bibiotèques d'ombre ont on rôle uniq, et l' r'zon d' l' èxistence d' l' Archive d' Anna. Nos pôvans fé des tchôses k' les ôtes instituts n' sont nén permis d' fé. Nén, c' n' est nén (sovint) k' nos pôvans archiver des matérèls k' sont ilégåls a préservî a d' ôtes plåces. Nén, c' est légål dins måy d' plåces d' fèr èn archive avou n'importe quîs livres, papîs, magazinnes, et ainsin d' suite."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Mais çou ki manke sovint dins les archives légales, c' est <strong>redondance et longevité</strong>. I gn a des livres dont ene seule copie egziste dins ene bibliothèque physique k'k'part. I gn a des records di metadata ki sont gardés pa ene seule corporation. I gn a des journaux ki sont onk distrus sol microfilm dins ene seule archive. Les bibliothèques polèt aveur des coupes di fond, les corporations polèt fèr falyite, les archives polèt esse bombées et brûlées. C' n' est nén ene hypothèse — ça s' passe tot l' tins."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Çou k' on sait fé uniqmint a l' Archive d' Anna, c' est stoker des copies multiples des ouves, a grand échelle. Nos savons rassembler des papiers, livres, magazines, et pus, et les distribuer en masse. Nos féçons ça pour l' instant avou des torrents, mais les technologies précises n' ont nén d' importance et vont changer avou l' tins. L' pus important, c' est d' aveur des copies multiples distribuyées a travèrs l' monde. Cisse citation d' i gn a pus di 200 ans est todi vraie:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Çou k' est perdu n' pout nén esse récupéré; mais sauvos çou k' reste: nén avou des voûtes et des serrures ki les cachèt des yeux et de l' usance du public, en les consignant a l' oubli, mais avou ene multiplication di copies, ki les mettra hors d' atteinte des accidents.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Ene note rapide a propôs du domaine public. Puisk' l' Archive d' Anna s' concentre uniqmint sol des activités ki sont illégales dins di nombreux endroits a travèrs l' monde, nos n' s' occupons nén des collections largement disponibles, come les livres du domaine public. Les entités légales s' en occupèt sovint dèjà bin. Cepandant, i gn a des considérations ki nos fêt parfois travailler sol des collections publiquement disponibles:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Les records di metadata polèt esse veyous librmint sol l' site Worldcat, mais nén téléchargés en masse (jusqu' a ç' ki nos les <a %(worldcat_scrape)s>scrapions</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Li code pout esse open source sol Github, mais Github come ene totalité n' pout nén esse facilement miré et donc préservé (bin k' dins cisse cas précis, i gn a des copies suffisamment distribuées di la plupârt des dépôts de code)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit est gratuit a l' usance, mais a récemment mis en place des mesures anti-scraping strictes, a cause di l' appétit di données pour l' entraînement des LLM (pus d' infos là-dessus après)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Ene multiplication di copies"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Ritournons a nosse question originale: come polèt nos prétendre préserver nosse collections pour l' éternité? Li problème principal ici, c' est k' nosse collection a <a %(torrents_stats)s>grandi</a> a ene vitesse rapide, en scrapant et en open-sourçant des collections massives (en plus du travail incroyable déjà fait par d' ôtes bibliothèques d' ombre de données ouvertes come Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Cisse croissance di données rend li mirage des collections a travèrs l' monde pus difficile. Li stokèdje di données coûte cher! Mais nos sommes optimistes, surtout en observant les trois tendances suivantes."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Nos avons cueilli les fruits faciles"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Cisse-ci suit directement des nosse priorités discutées ci-dessus. Nos préférons travailler a libérer des grandes collections d' abord. Mins k' nos avons sécurisé k'kes-unes des pus grandes collections du monde, nos attendons a k' nosse croissance soit bin pus lente."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "I gn a todi ene longue traîne di petites collections, et des nûs livres sont scannés ou publiés chaque jour, mais li râte sera probablement bin pus lent. Nos pourrions todi doubler ou même tripler en taille, mais sur ene période di tins pus longue."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Les coûts di stokèdje continuèt a baisser exponentiellement"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "A l' heure d' écrive, les <a %(diskprices)s>prix des disques</a> par TB sont autour di $12 pour des disques neufs, $8 pour des disques usés, et $4 pour des bandes. Si nos sommes conservateurs et regardons seulement les disques neufs, ça signifie k' stoker un petabyte coûte environ $12,000. Si nos supposons k' nosse bibliothèque va tripler de 900TB a 2.7PB, ça voudrait dire $32,400 pour mirer nosse bibliothèque entière. En ajoutant l' électricité, li coût d' ôte matériel, et ainsi d' suite, arrondissons a $40,000. Ou avou des bandes, plutot $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "D' un côté <strong>$15,000–$40,000 pour l' somme di toutes les connaissances humaines, c' est ene aubaine</strong>. D' l' ôte côté, c' est on pô cher d' espérer des tonnes di copies complètes, surtout si nos voudrions ossi k' ces gens continuèt a semer leurs torrents pour l' bénéfice des ôtes."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "C' est aujourd' hui. Mais li progrès avance:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Les coûts des disques durs par TB ont été réduits d' un tiers environ au cours des 10 dernières années, et vont probablement continuer a baisser a un rythme similaire. Les bandes semblent suivre une trajectoire similaire. Les prix des SSD baissent encore plus vite, et pourraient surpasser les prix des HDD d' ici la fin de la décennie."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendances des prix des HDD de différentes sources (cliquez pour voir l' étude)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si ça tient, alors dans 10 ans, nos pourrions envisager seulement $5,000–$13,000 pour mirer nosse collection entière (1/3), ou même moins si nos grandissons moins en taille. Bin k' c' est todi beaucoup d' argent, ça sera accessible pour beaucoup di gens. Et ça pourrait être encore mieux grâce au point suivant…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Amelioråcions dins l' densité d' informåcion"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Nos oistans les liveas dins les formats bruts ki nos sont dné. C' est vey, ils sont comprimés, mins sovint c' est-sti todi des scans ou des fotos di pådjes ki sont gråsses."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Djusque asteure, les sole håyettes po rétchîr li volime totål di nole coleccion ont stî ene comprèssion pus agressive ou ene déduplication. Mins, po fé des épargnes significatives, les deus sont trop pèrdants po nos gouts. Ene forte comprèssion des fotos pout fé li tecse à pène lîbåve. Et l' déduplication dimande ene hôte confianche dins l' fait ki les liveas sont egzactemint les minmes, ki n' est sovint nén assez precis, surtou si les contnus sont les minmes mins ki les scans ont stî fêts a des moments differints."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "I gn a todi eû ene troesime håyette, mins sa qualité a stî si måvåye ki nos n' l' avans jamåis considerêye: <strong>OCR, ou Riconoissance Optike des Caractères</strong>. C' est l' process po convier des fotos en tecse plin, en eployant l' AI po riconohe les caractères dins les fotos. Des usteyes po çoula ont stî longtimps, et ont stî assez bons, mins \"assez bons\" n' est nén assez po des prôyes di préservation."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Mins, les modèles di multi-modalité d' aprindijhe profon ont fêt des progrès rapidès, mins todi a hôtes côts. Nos s' atindans a ene amélioration dramatike tant dins l' précision ki dins les côts dins les ans ki vinront, a l' point ki çoula deviendrat réalistike di l' aplicer a nole librêre totål."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Amelioråcions di l' OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quand çoula arivera, nos prôverans todi les fichîs originåls, mins en plus nos pourrins aveur ene version pus ptite di nole librêre ki l' plupart des djins voudront mirer. Li clou est ki li tecse brut lu-minme se comprime èncor mié, et est èncor pus fâcile a dédupliquer, nos dnant èncor pus d' épargnes."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "D' manîre generåle, c' est nén irréaliste d' s' atinde a ene réduction di 5-10x dins li volime totål des fichîs, p't-êt èncor pus. Mins avou ene réduction conservatîve di 5x, nos s' rouverins avou <strong>$1,000–$3,000 dins 10 ans mins mwinme si nole librêre triplera di volime</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Fénetre criticåle"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si ces prévisions sont precisès, nos <strong>avans justumint a tinde deus ans</strong> divant ki nole coleccion totål serat largement mirée. D' manîre ki, come l' a dit Thomas Jefferson, \"placée hors d' atinte di l' accident.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Malheureusement, l' avènement des LLMs, et leur soif di dnéyes, a mètou èn r'cûl èn grand nombre di détinteurs di droets d' ôteur. Mins ki çoula n' l' était dèja. D' nombreux sîtes web rinde çoula pus difficile di scrapper et d' archiver, des procès volètot, et tot l' tchinps les librêres et archives physiques continuèt d' esse négligêyes."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Nos n' pûvans ki s' atinde a çoula di continouwer a s' ampiworer, et d' nombreux ôvradjes d' esse perdus divant ki n' entrèt dins l' domaine public."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Nos sommes a l' veye d' ene révolution dins l' préservation, mins <q>l' perdu n' pout nén esse r'cuperé.</q></strong> Nos avans ene fénetre criticåle di 5-10 ans pendant liquèle çoula est todi assez costéus d' operer ene librêre d' ombråje et di créer d' nombreux mires a travèrs l' monde, et pendant liquèle l' accès n' a nén èncor stî complètement fermé."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si nos pûvans passer cisse fénetre, alor nos avans vrêmint préservé l' connoissance et l' culture d' l' humanité po todi. Nos n' d'vons nén l' lîssî cisse période s' gaspîyer. Nos n' d'vons nén l' lîssî cisse fénetre criticåle s' fermer po nos."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Allons-y."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accès exclusif po les sociéts LLM a l' pus grande coleccion di liveas non-fictifs chinois dins l' monde"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Version chinoise 中文版</a>, <a %(news_ycombinator)s>Discuter sur Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> L' Archive d' Anna a acqweri ene coleccion unique di 7,5 millions / 350TB di liveas non-fictifs chinois — pus grande ki Library Genesis. Nos sommes prêts a dner a ene sociét LLM un accès exclusif, en èchange po ene OCR di hôte qualité et ene extraction di tecse.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "C' est ene ptite note di blog. Nos cherchons ene sociét ou institution po nos aidî avou l' OCR et l' extraction di tecse po ene vaste coleccion ki nos avans acqweri, en èchange po un accès exclusif en avance. Après li période d' embargo, nos libérerons, bin sûr, l' coleccion totål."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Les tecsse di scoleye di hôte qualité est d'ine grande utilité po l'ahivnaedje des LLMs. Mins si nosse coleccion est tchinoise, ci d'vra esse e-meme utile po l'ahivnaedje des LLMs en anglais : les modèles semblent encoder des concepts et des connaissances sans s'coer del lingaedje d'origene."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Po çoula, les tecsse doût esse estracté des scans. Tchôf qu'est-ce qu'Anna’s Archive gnèye avou çoula ? Ene tchèrche tecsse integre des livres po ses uzeus."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Come nosse butes s'alignèt avou cels des développeus di LLM, nos tchèrchans on collaborateur. Nos estans d'accoird di vos dner <strong>ene accè exclusif et précoce a cisse coleccion en gros po 1 an</strong>, si vos pôvez fére on OCR et on estraction di tecsse propre. Si vos estans d'accoird di partadjer tot l'code di vosse pipeline avou nos, nos estans d'accoird di metter ene embargo sol coleccion po pus longtimps."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pâdjes d'exemple"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Po nos prouver qu'vos avez on bon pipeline, v'ci des pâdjes d'exemple po cminçer, d'on livre sol superconducteurs. Vosse pipeline doût gérer proprement les maths, les tables, les grafikes, les notes di bas di pâdje, et ainsin di suite."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envoyîz vos pâdjes traitêyes a nosse email. S'elez sont bin, nos vos enverrons pus en privé, et nos s'attendons a c'vos pôvez rapiédmint fére tourner vosse pipeline solz-ozî. Onc qu'nos estans satisfaits, nos pôrons fére on accôrd."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Coleccion"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "D'infoirmåcion sol coleccion. <a %(duxiu)s>Duxiu</a> est ene vaste base di données di livres scannés, créée pa l' <a %(chaoxing)s>SuperStar Digital Library Group</a>. La plupârt sont des livres di scoleye, scannés po les rinde disponibles digitalmint a des universités et des bibliotèques. Po nosse public anglès, <a %(library_princeton)s>Princeton</a> et l' <a %(guides_lib_uw)s>University of Washington</a> ont des bons apers. I gn a ossi on excellent article qui dné pus d'context : <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (tchîrchi-l' dins Anna’s Archive)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Les livres di Duxiu ont longtimps stî piratés sol l'internet tchinois. D'costume, i sont vindeus po mins d'on dollar pa des revindeus. I sont tipikemint distrubués avou l'équivalent tchinois di Google Drive, qui a sovint stî piraté po permete pus d'espâs di stocådje. D'etails tecniques pôront esse trové <a %(github_duty_machine)s>ci</a> et <a %(github_821_github_io)s>ci</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Mins si les livres ont stî distrubués semi-publiquemint, i est assez difficile di les obténîr en gros. Nos avans metou çoula hôte sol nosse liste di TCHOF-FAIRE, et avans aloué plusieurs mois di trawé a temps plein po çoula. Mins, récement, on voluntaire incroyable, épatant et talentueux s'est raproché di nos, nos dîsant qu'i avot dja fé tot çoula — a grand côut. I ont partadgé l'coleccion complète avou nos, sins rîen atendre en retour, sauf l'garantie di préservation a long terme. Vraimint remarquable. I ont accôrdé di dmender d'aide d'cisse manîre po fé OCR l'coleccion."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "L'coleccion est di 7,543,702 fitchîs. Çoula est pus qu'Library Genesis non-fiction (a peu prés 5.3 millions). L'taille totale des fitchîs est d'environ 359TB (326TiB) dins s'forme actuelle."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Nos estans ouverts a d'ôtes propôses et idées. Sufît d'no contakter. Veyîz Anna’s Archive po pus d'infoirmåcion solz nosse coleccions, nosse efforts di préservation, et come vos pôvez aidî. Meci!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avertixhmint : cisse poste di blog a stî dépréciée. Nos avans décidé qu'IPFS n'est nén co prêt po l'grande scène. Nos lierons toudi a des fitchîs sol IPFS a partir d'Anna’s Archive quand c'est possibe, mins nos n'les hébergerons pus nos-memes, ni nos recommandons a d'ôtes di mirer avou IPFS. Veyîz nosse pâdje di Torrents si vos voloz aidî a préservî nosse coleccion."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Aidîz a semer Z-Library sol IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Come fére tourner ene bibliotèque fantôme : opérations a Anna’s Archive"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "I n'a nén d' <q>AWS po les charités fantômes,</q> alor come nos fére tourner Anna’s Archive ?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Dj'fé tourner <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, l'pus grand moteur di tchèrche open-source et sans but lucratif po les <a %(wikipedia_shadow_library)s>bibliotèques fantômes</a>, come Sci-Hub, Library Genesis, et Z-Library. Nosse but est di rendre l'connaissance et l'culture facilement accessibles, et a l'fin di constitchî on communauté di djins qui enssemble archivèt et préservèt <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>totes les livres do monde</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Dins cisse article, dj'vos montrerai come nos fére tourner cisse site web, et les défis uniques qui vénèt avou l'opération d'on site web avou ene status légal douteux, pisk'i n'a nén d'“AWS po les charités fantômes”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Veyîz ossi l'article sœur <a %(blog_how_to_become_a_pirate_archivist)s>Come dvinî on archiviste pirate</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokes d' inovåcion"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Cominceons avou nosse stack di tecno. I est voloument ennuyeus. Nos eployans Flask, MariaDB, et ElasticSearch. C' est tot. Li rechèrche est en grande partie ene problinme rèsolu, et nos n' avans nén l' entintion d' el r'inventer. D' ôtans, nos d'vans eployî nos <a %(mcfunley)s>tokes d' inovåcion</a> po d' ôtes tchoses: nén esse abatus pa les ôtorités."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Alor, cwand est-ce qu' l' Archive d' Anna est lîgal ou nén? Çoula d'pend principalmint del juridiction lîgale. Li pus grand des payis croet en ene forme di droot d' ôteur, çou qui vout dire qu' des djins ou des comagnies ont on monopole exclusif so des tipes di travås po on certain temps. A l' Archive d' Anna, nos croet qu' i gn a des bénfices, mins en gros li droot d' ôteur est on nèt-négatif po l' socièté — mins çoula est ene ôte istwere."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ç' monopole exclusif so des travås vout dire qu' i est ilîgal po tertous foû d' ç' monopole di distribuer çes travås — inclou nos. Mins l' Archive d' Anna est on moteur di rechèrche qui n' distribue nén dirèctemint çes travås (al måns nén so nosse site web en clernet), alor nos d'vraimint esse bon, nén? Nén d' tot. Dins di moult juridictions, i n' est nén sôlement ilîgal di distribuer des travås copîyîs, mins ossi di lier a des plåces qui l' fêt. On èxemple clåssique di çoula est li droot DMCA des États-Unis."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Çoula est l' bout l' pus strèt del spectre. A l' ôte bout, i pout théorèticmint avou des payis avou nén droot d' ôteur, mins çes payis n' èxistèt nén vrèymint. Preske tertous les payis ont on droot d' ôteur dins les livès. L' èxécution est ene ôte istwere. I gn a des payis avou des govèrnmints qui s' en fitchèt d' l' èxécution del droot d' ôteur. I gn a ossi des payis entre les deus èxtremes, qui prohibèt di distribuer des travås copîyîs, mins qui n' prohibèt nén di lier a çes travås."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "On ôte considération est al nivô del comagnie. Si ene comagnie opère dins on juridiction qui s' en fitch del droot d' ôteur, mins qu' l' comagnie èl-méme n' est nén prête a purner des risks, alor i pout fermer vosse site web dès qu' on s' plind d' çoula."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmint, ene grande considération est les payemints. Comince nos d'vans rister anonymes, nos n' poumons nén eployî des méthodes di payemint traditionèles. Çoula nos lèsse avou les cryptomonaies, et sôlement ene p'tite partie des comagnies les sopoirtèt (i gn a des cartes dèbites virtuelles payîyes avou des cryptos, mins elles sont sovint nén acceptêyes)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architecure del sistinme"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Alor, disons qu' vos avoz trové des comagnies qui sont prêtes a hôster vosse site web sins vos fermer — apelons çes comagnies “fournisseus amants d' liberté” 😄. Vos d'couvrirez vite qu' hôster tot avou zels est assez costéus, alor vos v'lez trover des “fournisseus bon mårchî” et fêt l' hôstîre là, en passant pa les fournisseus amants d' liberté. Si vos l' fêt bén, les fournisseus bon mårchî n' s' rindeont jamès conte di çou qu' vos hôstîz, et n' recevront jamès d' plindes."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Avou tertous çes fournisseus, i gn a on risk qu' ils vos fermèt d' tôte manîre, alor vos d'vans ossi avou l' redondance. Nos d'vans çoula a tertous les nivôs del nosse stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Onne comagnie qui aime on p'tit peu l' liberté et qui s' est mètowe dins onne position intèressante est Cloudflare. Ils ont <a %(blog_cloudflare)s>arguminté</a> qu' ils n' sont nén on fournissor d' hôstîre, mins onne utilité, come on ISP. Ils n' sont d' lors nén sômis a DMCA ou ôtes demandes d' abatisse, et ils transmettèt tertoute demande a vosse fournissor d' hôstîre réel. Ils ont mème été juske al court po protéger ç' structure. Nos poumons d' lors les eployî come onne ôte couche di cache et di protection."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare n' acceptèt nén les payemints anonymes, alor nos poumons sôlement eployî leur plan gratu. Çoula vout dire qu' nos n' poumons nén eployî leur balançage di charge ou leurs fonctinnalités di bascule. Nos avons d' lors <a %(annas_archive_l255)s>impliminté çoula nos-èmes</a> al nivô del domaine. A l' chargemint del pådje, li naviguateur vérifie si li domaine courint est toudi disponibe, et si nén, il réécrit tertous les URLs a on ôte domaine. Comince Cloudflare cache di moult pådjes, çoula vout dire qu' on uzeu pout atterî so nosse domaine principal, mème si li serveur proxy est abatu, et pu al clic suivant, esse déplacé a on ôte domaine."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Nos avons toudi ossi des concèrns opérationnels normåls a gérer, come li monitorâge del santé des serveurs, li journalisâge des erreurs del backend et del frontend, et ainsin d' suite. Nosse architecure di bascule permet pus di robustesse a ç' nivô ossi, come en fêt tournî onne série di serveurs complètement diférinte so on des domaines. Nos poumons mème fêt tournî des vèrsions pus vîyes del code et des Datasets so ç' domaine séparé, au cas où on bug critique dins li vèrsion principale passe inaperçu."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Nos poumons ossi nous protéger contre Cloudflare qui s' tournerait contre nos, en l' r'tirant d' on des domaines, come ç' domaine séparé. Diférintes permutations di çes idées sont possibes."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Outils"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Veyons les outils qu' nos eployans po acopli çoula. Çoula èvolue fort come nos rintrovans des noveas problinmes et des noveas solutions."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Serveur d' application: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Serveur proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestion des serveurs: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Dèvelopmint: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hôstiaedje static Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "I gn-a des dicijhons k' on a fwait et refwait. Ene di celes-ci c' est l' comunicåcion inte di les serveus: nos eployans d' costé l' Wireguard po çoula, mins on a trové k' i s' arestêye di tchinme des côps, ou k' i n' tchinme ki dins on seul costé. Çoula s' est passé avou des diférins montåjhes di Wireguard k' nos avans eployîs, come <a %(github_costela_wesher)s>wesher</a> et <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Nos avans ossu eployî l' tunneling des ports avou SSH, eployant autossh et sshuttle, mins on a rencountré des <a %(github_sshuttle)s>problèmes là</a> (minme s' i n' est nén clair po mi si autossh sofe des problèmes TCP-sur-TCP ou nén — çoula m' sémble ene solucion bricådêye mins ptete k' i n' est nén si måvå?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "A l' plaece, nos avans r'vni a des lîens direcs inte di les serveus, cachiant k' on serveu est en fwait eployî so des founissous bon mårcî avou l' filtråjhe IP avou UFW. Çoula a l' desavådje k' Docker n' va nén bén avou UFW, a mwins d' eployî <code>network_mode: \"host\"</code>. Tot çoula est on pô d' pus a rîsque, pask' vos r'vniroz vosse serveu a l' internet avou on ptit mål configuråcion. P'tete k' nos devrîyons r'vni a autossh — vos avis sont totafwait binv'nous ci."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Nos avans ossu fwait et refwait l' comparêye di Varnish et Nginx. Nos aimons for l' Varnish asteure, mins i gn-a des quirks et des coins bruts. Li minme s' aplike a Checkmk: nos n' l' aimons nén for, mins çoula fonccionne po l' moment. Weblate a stî bin mins nén incroyable — d' côps, dj' a pôre di perde mes dnéyes tot l' tins k' dj' tryî d' l' sync avou nosse repo git. Flask a stî bin d' manîre djeneråle, mins i gn-a des quirks bizarres k' ont costé on tas di tins a débugger, come li configuråcion des dominnes costoumés, ou des problèmes avou l' integrecion avou SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Djusqu' asteure, les ôtes usteyes ont stî super: nos n' avans nén d' plintes seryeuses avou MariaDB, ElasticSearch, Gitlab, Zulip, Docker, et Tor. Tot çoula a stî avou des problèmes, mins nén d' côps trop seryeus ou ki prindèt trop d' tins."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Concluzion"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Çoula a stî ene esperience inteyressante d' aprinde come installer on moteur di rechèrche di biblioteke d' ombråedje robuste et résilient. I gn-a on tas d' ôtes detays a partadjer dins des pôstes a vni, dîz-m' çou k' vos vourîz savouêr pus!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Come todi, nos r'cwerans des d'neres po sopoirtî ç' ovraedje, adon k' vos n' oublîz nén d' vey l' pådje di d'neres so l' Archive d' Anna. Nos r'cwerans ossu d' ôtes sôres di sopoirt, come des bourses, des sponsors a long tèrme, des founissous di payemint a hôt rîsque, ptete minme des annonces (avou bon gou!)! Et si vos v'lez contribuwer avou vosse tins et vosse talints, nos r'cwerans todi des developpeus, des traducteurs, et ainsin di suite. Mierci po vosse intêret et sopoirt."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna et l'êquipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Bondjoû, dj' m' apel Anna. Dj' a creé <a %(wikipedia_annas_archive)s>l' Archive d' Anna</a>, li pus grande biblioteke d' ombråedje do monde. Ci est mi blogu personål, dins liquî mi et mes coéquipîs scrivans a prop di pirat'te, di preservåcion numerike, et pus."

#, fuzzy
msgid "blog.index.text2"
msgstr "Racordez-vos avou mi so <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Notez k' ci site est just on blogu. Nos hôstans ki nosse propes mots ci. Nén di torrents ou d' ôtes fitchîs copyrîyés sont hôstés ou racordés ci."

#, fuzzy
msgid "blog.index.heading"
msgstr "Pôstes di blogu"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B Scrape di WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Mete 5,998,794 livres so IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avertixhmint : cisse poste di blog a stî dépréciée. Nos avans décidé qu'IPFS n'est nén co prêt po l'grande scène. Nos lierons toudi a des fitchîs sol IPFS a partir d'Anna’s Archive quand c'est possibe, mins nos n'les hébergerons pus nos-memes, ni nos recommandons a d'ôtes di mirer avou IPFS. Veyîz nosse pâdje di Torrents si vos voloz aidî a préservî nosse coleccion."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> L' Archive d' Anna a scrappé tot WorldCat (li pus grande coleccion di metadata di biblioteke do monde) po fwait ene liste TODO di livres k' ont d' n' esse preservés.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "I gn a on an, nos avans <a %(blog)s>cmincî</a> a r'pondre a cisse cwestion: <strong>Kel pourcintådje di livres ont stî preservés a tchin d' biblioteks d' ombråedje?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Onk on livre rintre dins ene biblioteke d' ombråedje avou des dnêyes ouveres come <a %(wikipedia_library_genesis)s>Library Genesis</a>, et asteure <a %(wikipedia_annas_archive)s>l' Archive d' Anna</a>, i s' r'trôve miré tot autour do monde (avou des torrents), çoula l' preservant pràtikeum po todi."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Po r'pondre a cisse cwestion di kel pourcintådje di livres ont stî preservés, nos d'vans savouêr li dénominateur: kel est li nombe di livres ki egzistèt totålement? Et d' manîre idéale, nos n' avans nén ki on nombe, mins des metadata réels. Adon nos pouhons nén soulement les matcher avou les biblioteks d' ombråedje, mins ossu <strong>créer ene liste TODO des livres ki r'cwerèt d' esse preservés!</strong> Nos pouhons minme cmincî a rêver d' on efôrt participatif po descinde cisse liste TODO."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Nos avans scritchî <a %(wikipedia_isbndb_com)s>ISBNdb</a>, et avans djichargî li <a %(openlibrary)s>Open Library dataset</a>, mins les rèsultats n' étaient nén satisfaisants. Li problinme principal c' esteut qu' i n' aveut nén ene grande superposicion d' ISBNs. Veyoz cisse diagramme de Venn dins <a %(blog)s>noste poste di blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Nos avans stî fortèssez surprinds d' come li p'tit superposicion i aveut entre ISBNdb et Open Library, les deux inclouhant libèrèmint des dnêyes di diférins sourdjes, come des scritchîs di web et des records di librêyes. S' i fêtèt on bon boulo a trover li pus grand nombre d' ISBNs ki gn a, leus cercles d'vraient aveur ene superposicion substanciele, ou bin l' on d'vrait esse on sous-ensemble di l' ôte. Ça nos a fêt s' d'mander, comint d' livans tchèyes <em>completemint foû d' cès cercles</em>? Nos avans d' besoin d' ene pus grande basse di dnêyes."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "C' est l' moment qu' nos avans visé li pus grand basse di dnêyes di livans al monde: <a %(wikipedia_worldcat)s>WorldCat</a>. C' est ene basse di dnêyes proprètêre par l' a.s.b.l. <a %(wikipedia_oclc)s>OCLC</a>, ki agrèdje des records di metadata di librêyes di tot l' monde, en èchang d' l' accès a l' basse complète po les librêyes, et d' les fêt aparexhe dins les rèsultats di tchèrche des us'teûrs finaux."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Mins, mêm si OCLC est ene a.s.b.l., leu modéle d' afêres rèquiert di protéjer leu basse di dnêyes. Bin, nos s'cuse, amis d' OCLC, mins nos l' djonne tot. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Dins l' anêye passêye, nos avans scritchî avou grand minuciosité totes les records di WorldCat. A l' cominchement, nos avans avou on coup d' chance. WorldCat esteut just a r'lancer leu redesign complet d' leu site web (en awousse 2022). Ça inclouhèt ene refonte substanciele di leu sistêmes backend, introduhant des fêyes di sécureté. Nos avans s'zi d' l' opportunité, et nos avans stî capabls di scritchî des centaines di millions (!) di records en mwins d' onès djournêyes."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redesign di WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Après ça, les fêyes di sécureté ont stî fixêyes ptit a ptit, jusqu' al dèrniére ki nos avans trové ki a stî patchéye i gn a on moes. A c' moment-là, nos avans prumî totès les records, et nos n' allions ki po des records avou on ptit pus hôt qualité. Donc nos avans sintou ki c' esteut l' moment di l' r'lâchi!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Veyons on pô d' informåcions basiques so les dnêyes:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Les Conteneus d' l' Archive d' Anna (AAC)</a>, ki sont en fait <a %(jsonlines)s>JSON Lines</a> comprèssé avou <a %(zstd)s>Zstandard</a>, avou des sémantiques standardizêyes. Cès conteneus enveloppent diférints types di records, basés so les diférints scritchîs ki nos avans deployîs."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dnêyes"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Une erreur inconnue s’est produite. Veuillez nous contacter à %(email)s avec une capture d’écran."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Cette pièce a un minimum plus élevé que d’habitude. Veuillez sélectionner une autre durée ou une autre pièce."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "La demande n’a pas pu être complétée. Veuillez réessayer dans quelques minutes, et si cela continue, contactez-nous à %(email)s avec une capture d’écran."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Erreur dans le traitement du paiement. Veuillez patienter un moment et réessayer. Si le problème persiste pendant plus de 24 heures, veuillez nous contacter à %(email)s avec une capture d’écran."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentêre cåssî"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Probleme d'fitchî: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Meyeu vèrsion"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Voloz vs rapoirtî cisse uzeu po comportemint abusif ou nén aproprié?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Rapoirter l'abus"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abus rapoirtyî:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Vos avoz rapoirtyî cisse uzeu po abus."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Rèpondre"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Veuillez <a %(a_login)s>vous connecter</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Vous avez laissé un commentaire. Cela peut prendre une minute pour qu'il apparaisse."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Quelque chose s'est mal passé. Veuillez recharger la page et réessayer."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s pages affectées"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visible dans Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visible dans Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visible dans Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marqué comme cassé dans Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Manquant de Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Måqué come “spam” dins Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Måqué come “mauvais fitchî” dins Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Toutes les pages n’ont pas pu être converties en PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "L’exécution de exiftool a échoué sur ce fichier"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Livre (inconnu)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Livre (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Livre (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Article de journal"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Document de normes"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Magazine"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Bande dessinée"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partition musicale"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Ådjoûbooke"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Autre"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Téléchargement depuis le serveur partenaire"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Téléchargement externe"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Emprunt externe"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Emprunt externe (impression désactivée)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Explorer les métadonnées"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Contenu dans les torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library tchinoès"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Tcharget di AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Mètadoneyes tchèkes"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Bibioteke d' l' Etat Russe"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titrea"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Oteur"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Editeu"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edicion"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Anêye di publicacion"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "No di fitchî oridjinal"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Discrijhaedje et rimarques di metadata"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Les téléchargements du serveur partenaire ne sont temporairement pas disponibles pour ce fichier."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Sîrvou rapîde di patner #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomandî)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(none vérifiaedje di naviguetrece ou waitlistes)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Sîrvou linde di patner #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(légérement rapîde mins avou waitliste)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(none waitliste, mins pout esse très linde)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub : %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(cliquez aussi sur « OBTENIR » en haut)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(cliquez sur « OBTENIR » en haut)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "leurs publicités sont connues pour contenir des logiciels malveillants, utilisez donc un bloqueur de publicités ou ne cliquez pas sur les publicités"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Les fitchîs Nexus/STC pôrèt esse dîhåb' å télécharger)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library so l’ Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(èl fåt l’ Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Emprunter à l'Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(pour les utilisateurs handicapés d'impression uniquement)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(le DOI associé pourrait ne pas être disponible dans Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "collection"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Téléchargements en masse par torrent"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(experts uniquement)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Rechercher dans l'Archive d'Anna pour ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Rechercher dans diverses autres bases de données pour ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Trover l' enregistrement original dins ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Cercar dins l' Archîve d' Anna pol ID Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Trover l' enregistrement original dins Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Cercar dins l' Archîve d' Anna pol numéro OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Trover l' enregistrement original dins WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Cercar dins l' Archîve d' Anna pol numéro SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Cercar a mwin dins DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Cercar dins l' Archîve d' Anna pol numéro SSNO CADAL"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Trover l' enregistrement original dins CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Cercar dins l' Archîve d' Anna pol numéro DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "L' Archîve d' Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(neninde vérification du navigatore n' est nècèssaire)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata tchize %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Mètadoneyes"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "discrijhaedje"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nom alternatif"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Titrea alternativ"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Oteur alternativ"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editeu alternativ"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edicion alternativ"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extension alternative"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "rimarques di metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Discrijhaedje alternativ"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "date de mise en source ouverte"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Fitchî Sci-Hub “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Fitchî de Prêt Numérique Contrôlé de l' Internet Archive “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Ceci est un enregistrement d' un fitchî de l' Internet Archive, nén un fitchî téléchargeåve dirèctement. Vos pôvez essayer d' emprunter l' livre (lien çi-dessous), ou utiliser cisse URL quand vos <a %(a_request)s>demandîz on fitchî</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Si vos avoz cisse fitchî et qu' il n' est nén cor disponibe dins l' Archîve d' Anna, pensez a <a %(a_request)s>l' uploade</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Enregistrement metadata ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Enregistrement metadata Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Enregistrement metadata numéro OCLC (WorldCat) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Enregistrement metadata SSID DuXiu %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadoneye"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "C' est on metadoneye, nén on fitchî d' tèlèchargement. Vos poloz eployî cisse URL k' <a %(a_request)s>vos dmandoz on fitchî</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata del record lié"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Améliorer les métadonnées sur Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Attention : plusieurs records liés :"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Améliorer les metadoneyes"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Signaler la qualité du fichier"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Temps de téléchargement"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Sitouaije:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Cweri dins l' Archive d' Anna po “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Côdes Explorer:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vey sol Côdes Explorer “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lére pus…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Tèlèchargements (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Emprunter (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Explorer les metadoneyes (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentêres (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listes (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistiques (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Détails tècniques"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Cisse fitchî pout aveur des problins, et a stî catchî d' on bibioteke sourdant.</span> D' feis c' est a l' dmande d' on dètinteu d' dreûts d' ôteur, d' feis c' est pask' i gn a onmeilleur altèrnative, mins d' feis c' est pask' i gn a on problin avou l' fitchî lu-méme. I pout co esse bon po tèlècharger, mins nos r'comandans d' cwèri onmeilleur fitchî. Pus d' détails:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Onmeilleure vèrsion d' cisse fitchî pout esse d' disponibe a %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Si vos voloz co tèlècharger cisse fitchî, fwaites bén d' n' eployî ki des programes di fiançé, metous a djoû."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Téléchargements rapides"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Tèlèchargements rapîds</strong> Dvins on <a %(a_membership)s>mimbe</a> po sopoirtî l' prèzervation a long tèrme des livres, papîs, et pus. Po vos rmerkiî d' vô sopoirt, vos rçevroz des tèlèchargements rapîds. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si vous faites un don ce mois-ci, vous obtenez <strong>le double</strong> du nombre de téléchargements rapides."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Tèlèchargemints rapîds</strong> I vs avez %(remaining)s d' rèsidu po l' djoû. Meci d' esse èn mimbre! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Tèlèchargemints rapîds</strong> Vos n' avoz pus d' tèlèchargemints rapîds po l' djoû."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Tèlèchargemints rapîds</strong> Vos avoz tèlèchargé ç' fitchîr ci récement. Les lîens rèsont validès po on ptit tins."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opsion #%(num)d : %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nén rihidjha)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(ovrir dins l'viseu)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Racontéz a on côpî, et vos deus vos rçûrhoz %(percentage)s%% tèlèchargemints rapîds di bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Aprindez pus…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Tèlèchargemints lints"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "D' ptits nûmèros di confiance."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Pus d' informåcions dins l' <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(çoula pout nècèssiter <a %(a_browser)s>on contrôle di l' naviguêyeu</a> — tèlèchargemints ilimités!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Après avou downloadé:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Ovrir dins no viseu"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "afficher les téléchargements externes"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Tèlèchargemints èstèrnès"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nole tèlèchargemints trovés."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Totes les opsions di tèlèchargemint ont l' minme fitchîr, et d'vraît esse sûrs a eployî. Çoula dit, soyes todi prudzint cand vos tèlèchargez des fitchîs d' l' intèrnèt, sårtout dès sîtes èstèrnès a l' Archive d' Anna. Pår èjhemp, soyes seur di mete vos èndjin a djoû."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Po des gros fitchîs, nos r'comandans d' eployî on djusseu d' tèlèchargement po prépî d' interrupcions."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Djusseus d' tèlèchargement r'comandés: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Vos avoz d' besoin d' on lîzeu d' e-books ou PDF po drovi l' fitchî, suivant l' format do fitchî."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Lîzeus d' e-books r'comandés: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Viseur en ligne d'Anna’s Archive"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Eployî des usteyes en linne po convier d' on format a l' ôte."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Usteyes di convèrsion r'comandés: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Vos poloz evoyî tant les fitchîs PDF come EPUB a vos Kindle ou Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Usteyes r'comandés: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon « Evoyî a Kindle »"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz « Evoyî a Kobo/Kindle »"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Soutindrè les ôteurs et les bibiotèques"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si vos l' amez et qu' vos l' pôtez, pinsîz a cachter l' origénal, ou a soutindrè les ôteurs d' manîre directe."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si c' est d' disponibe a vosse bibiotèque locåle, pinsîz a l' emprintî gratuitemint là."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Qualité du fichier"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Aidez la communauté en signalant la qualité de ce fichier ! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Signaler un problème de fichier (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Excellente qualité de fichier (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Ajouter un commentaire (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Quel est le problème avec ce fichier ?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Veuillez utiliser le <a %(a_copyright)s>formulaire de réclamation DMCA / Droits d'auteur</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Décrire le problème (obligatoire)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Description du problème"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 d'une meilleure version de ce fichier (si applicable)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Remplissez ceci s'il existe un autre fichier qui correspond étroitement à ce fichier (même édition, même extension de fichier si vous pouvez en trouver un), que les gens devraient utiliser à la place de ce fichier. Si vous connaissez une meilleure version de ce fichier en dehors de l'Archive d'Anna, alors veuillez <a %(a_upload)s>la télécharger</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Vous pouvez obtenir le md5 à partir de l'URL, par exemple"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Soumettre le rapport"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Apreindez come <a %(a_metadata)s>améliorer les métadonnées</a> pour ce fichier vous-même."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Merci d'avoir soumis votre rapport. Il sera affiché sur cette page, ainsi que révisé manuellement par Anna (jusqu'à ce que nous ayons un système de modération approprié)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Quelque chose s'est mal passé. Veuillez recharger la page et réessayer."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si ce fichier est de grande qualité, vous pouvez discuter de tout à son sujet ici ! Sinon, veuillez utiliser le bouton « Signaler un problème de fichier »."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "J'ai adoré ce livre !"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Laisser un commentaire"

#, fuzzy
msgid "common.english_only"
msgstr "Li tèkse ci-après continou en inglès."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total des téléchargements : %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un « fichier MD5 » est un hash qui est calculé à partir du contenu du fichier, et est raisonnablement unique en fonction de ce contenu. Toutes les bibliothèques fantômes que nous avons indexées ici utilisent principalement les MD5 pour identifier les fichiers."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un fichier peut apparaître dans plusieurs bibliothèques fantômes. Pour des informations sur les différents datasets que nous avons compilés, consultez la <a %(a_datasets)s>page des Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Ceci est un fichier géré par la <a %(a_ia)s>bibliothèque de Prêt Numérique Contrôlé de l'IA</a>, et indexé par Anna’s Archive pour la recherche. Pour des informations sur les différents datasets que nous avons compilés, consultez la <a %(a_datasets)s>page des Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pour des informations sur ce fichier particulier, consultez son <a %(a_href)s>fichier JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problème de chargement de cette page"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Veuillez actualiser pour réessayer. <a %(a_contact)s>Contactez-nous</a> si le problème persiste pendant plusieurs heures."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Non trouvé"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” n'a pas été trouvé dans notre base de données."

#, fuzzy
msgid "page.login.title"
msgstr "Connexion / Inscription"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Vérification du navigateur"

#, fuzzy
msgid "page.login.text1"
msgstr "Pour empêcher les robots de spam de créer de nombreux comptes, nous devons d'abord vérifier votre navigateur."

#, fuzzy
msgid "page.login.text2"
msgstr "Si vous êtes pris dans une boucle infinie, nous vous recommandons d'installer <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Il peut également être utile de désactiver les bloqueurs de publicités et autres extensions de navigateur."

#, fuzzy
msgid "page.codes.title"
msgstr "Codes"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorator di codes"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explorîz les codes avou lesquels les records sont måqués, pa préfixe. Li colone “records” montre li nombre di records måqués avou des codes avou l' préfixe dné, come veyou dins l' motî d' rechèrche (y compris les records avou onk des metadata). Li colone “codes” montre combien d' codes avou l' préfixe dné."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Cisse pådje pout prinde on pô d' tinp po s' generî, c' est po çoula k' ele dimande on captcha Cloudflare. <a %(a_donate)s>Les Mimbes</a> pôront eviter l' captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "S' il vos plait, n' scrapez nén cisses pådjes. A l' plaece, nos vos r'comandans <a %(a_import)s>di generî</a> ou <a %(a_download)s>d' alever</a> nos databases ElasticSearch et MariaDB, et di r'côre nos <a %(a_software)s>codes a source ouverete</a>. Les rås dnéyes poutèt esse explorées a mwin avou des fitchîs JSON come <a %(a_json_file)s>cisse ci</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Préfixe"

#, fuzzy
msgid "common.form.go"
msgstr "Aler"

#, fuzzy
msgid "common.form.reset"
msgstr "Rinizî"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Cweri Årchiye d'Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Årtinde: li code a des caractéres Unicode incorècts, et pout mal s' comporter dins diférins cas. Li rå binaire pout esse décodé a part di l' représentation base64 dins l' URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Préfixe di code connu “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Préfixe"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Titchî"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Discrijhaedje"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL po ene code spécifike"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "Les “%%s” seront remplacés avou l'valixhance do code"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL djenerike"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Sitouaetchin"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] ""
msgstr[1] "%(count)s records ki matchèt “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL po ene code spécifike: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Pus d'…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes k'comintèt avou “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indèxe di"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "records"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codes"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Mins di %(count)s records"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pour les réclamations DMCA / droits d'auteur, utilisez <a %(a_copyright)s>ce formulaire</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Toute autre manière de nous contacter concernant les réclamations de droits d'auteur sera automatiquement supprimée."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Nous accueillons très volontiers vos commentaires et questions !"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Cependant, en raison de la quantité de spam et d'emails absurdes que nous recevons, veuillez cocher les cases pour confirmer que vous comprenez ces conditions pour nous contacter."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Les réclamations de droits d'auteur envoyées à cet email seront ignorées ; utilisez plutôt le formulaire."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Les sèrveu d' partners sont nén dsponibes à cause des fèrmetures d' hôstî. Ils d'vraient esse rétablis d'vins nén lontin."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Les mimbresyes seront prolongêyes come i faut."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Ne nous envoyez pas d'email pour <a %(a_request)s>demander des livres</a><br>ou de petits <a %(a_upload)s>uploads (<10k)</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Lorsque vous posez des questions sur les comptes ou les dons, ajoutez votre identifiant de compte, des captures d'écran, des reçus, autant d'informations que possible. Nous ne vérifions notre email que toutes les 1-2 semaines, donc ne pas inclure ces informations retardera toute résolution."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Afficher l'email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulaire di réclame di DMCA / Droit d'ôteu"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si vos avoz on réclame DMCA ou ôte réclame di droit d'ôteu, s' i vos plait, rimplixhoz cisse formulaire come precisemint ki possibe. Si vos rencotrez des problinnes, contaktez nos a no adresse DMCA d'dicåce: %(email)s. Notez ki les réclames envoyîyes a cisse adresse n' seront nén traitêyes, çoula est k' po des cwistions. S' i vos plait, eployîz l'formulaire ci dzo-dessous po s'ubmitter vos réclames."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs so l’ Archive d’ Anna (obligatwêre). Onk ene per linne. Volêz n’ metter ki des URLs ki discribèt l’ edicion egzacte d’ on live. Si vos volêz fêre ene plinte po des live ou des edicions multiples, volêz rimplî cist fitchî plinte divins foûs."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Les plintes ki rassemblèt des live ou des edicions multiples seront refusêyes."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Vô nom (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adresse (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Numer di telefone (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Discrijha clêre do materiyal sourdant (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs do materiyal sourdant (si aplicåve). Onk ene per linne. Volêz n’ metter ki cels ki matchèt egzactemint l’ edicion po li kike vos raportez ene plinte di droet d’ ôteur."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs do materiyal sourdant, onk ene per linne. Volêz prinde on moment po cwèri Open Library po vô materiyal sourdant. Çoula nos aidrè a verifier vô plinte."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs do materiyal sourdant, onk ene per linne (obligatwêre). Volêz n’ metter k’ i fåt po nos aidrî a verifier vô plinte (p.ex. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Discrijha ey sinatoure (obligatwêre)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Sotmette plinte"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Meci d’ avou rimplî vô plinte di droet d’ ôteur. Nos l’ r’vèrons dès k’ i fåt. Volêz r’cargî l’ pådje po fêre ene ôte plinte."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Onk cinse a stî må. Volêz r’cargî l’ pådje ey sayî d’ noû."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si vos estoz d' intereût a mirrer cist set di donees po <a %(a_archival)s>archivådje</a> ou po <a %(a_llm)s>formåcion LLM</a>, voloz bén nos contakter."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Notre mission est d'archiver tous les livres du monde (ainsi que les articles, magazines, etc.), et de les rendre largement accessibles. Nous croyons que tous les livres devraient être largement dupliqués, pour assurer la redondance et la résilience. C'est pourquoi nous rassemblons des fichiers provenant de diverses sources. Certaines sources sont complètement ouvertes et peuvent être dupliquées en masse (comme Sci-Hub). D'autres sont fermées et protectrices, donc nous essayons de les scraper pour « libérer » leurs livres. D'autres encore se situent quelque part entre les deux."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Toutes nos données peuvent être <a %(a_torrents)s>téléchargées via torrent</a>, et toutes nos métadonnées peuvent être <a %(a_anna_software)s>générées</a> ou <a %(a_elasticsearch)s>téléchargées</a> sous forme de bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement via des fichiers JSON tels que <a %(a_dbrecord)s>celui-ci</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Aperçu"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Ci-dessous un aperçu rapide des sources des fichiers sur Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sourcêye"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Grandeu"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% miré pa AA / torrents disponibes"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Pourcintadjes do nombre di fitchîs"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Dnier mete a djoû"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Nén-Fiction et Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] ""
msgstr[1] "%(count)s fichiers"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Atruvers Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: gelé dès 2021; l'minme di fitchîs sont disponibes avou des torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: ptitès apondoues d' puis"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Sins “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Les torrents di fictions sont en retar (même si les IDs ~4-6M n'ont nén stî torentés k' i s' recovrèt avou nos torrents Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Li ramexhe “chinoise” dins Z-Library a l' air d' esse li mwinme k' nosse ramexhe DuXiu, mins avou des MD5 differins. Nos-ôtès n' metans nén cès fitchîs la dins les torrents po n' nén n' fé des dobles, mins nos les montrans totavå dins nosse index di rechèrche."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Prêt Numérique Contrôlé"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ des fitchîs sont réchèrchåves."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totå"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Hors dobles"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Come les bibiotèques d' ombråje syncronizèt sovint les dnéyes di l' one a l' ôte, i gn a ene grånde superposicion inte les bibiotèques. C' est po çoula ki les nûmres n' s' adjonèt nén å totå."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Li pourcintådje “miroré et sémé pa l' Archive d' Anna” montre comint di fitchîs nos mirorans nos-mémes. Nos sémans ces fitchîs en gros avou des torrents, et les rendans djihaedjes direcminte avou des setins di patners."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibiotèques sourdants"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Certins bibiotèques sourdants promouxhèt l'partaedje en gros d' leurs dnêyes avou des torrents, tandis qu' d' ôtes n' partaedjèt nén volontî volinmeus leus colèccion. Dins cisse ci, l' Archive d' Anna saye d' scraper leus colèccions, et d' les rinde disponibes (vêye nosse pådje <a %(a_torrents)s>Torrents</a>). I gn a ossi des situations intermèdiyes, come, par èjhemp, les bibiotèques sourdants qu' sont volontîs d' partaedjî, mins n' ont nén les rissources po l' fé. Dins ces cas-là, nos sayans ossi d' aidî."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Ci-après, vos trovroz ene pådje d' come nos interfèçans avou les diférintes bibiotèques sourdants."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sourdant"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Fitchîs"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Quotidien <a %(dbdumps)s>dépôts HTTP di dônees</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizés po <a %(nonfiction)s>Non-Fictif</a> et <a %(fiction)s>Fictif</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s L' Archive d' Anna gère ene colection di <a %(covers)s>torrents di couvertures di livrès</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub a gelé les noveas dnêyes d' puis 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dumps di metadata disponibes <a %(scihub1)s>ci</a> et <a %(scihub2)s>ci</a>, tot come come ene ptite del <a %(libgenli)s>database Libgen.li</a> (ki nos eployons)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torints di donees disponibes <a %(scihub1)s>ci</a>, <a %(scihub2)s>ci</a>, et <a %(libgenli)s>ci</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Dji fitchîs noveas sont <a %(libgenrs)s>metous</a> <a %(libgenli)s>a</a> Libgen \"scimag\", mins nén assez po mériter des noveas torints"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dumpes di basse di donees HTTP</a> trimèstrîles"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Les torints di Non-Fiction sont partagîs avou Libgen.rs (et mirroirîs <a %(libgenli)s>ci</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s L' Archîve d' Anna et Libgen.li gèrèt ensemble des coleccion d' <a %(comics)s>bandes dessinêyes</a>, <a %(magazines)s>magazinnes</a>, <a %(standarts)s>documints stanndards</a>, et <a %(fiction)s>fictions (diverjîs di Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Leus coleccion \"fiction_rus\" (fiction rûsse) n' a nén d' torrints d' dièdîs, mins est coevrêye pa des torrints d' ôtes, et nos rinde èn <a %(fiction_rus)s>mirou</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s L' Archive d' Anna et Z-Library gèront en collaboracion ene colection di <a %(metadata)s>metadata Z-Library</a> et <a %(files)s>fitchîs Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s D' metadata disponibe a travèrs <a %(openlib)s>dumps del database Open Library</a>, mins cels n' couvrèt nén l' inteyre colection IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nén di dumps di metadata aisement accèssibes po l' inteyre colection"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s L’ Archîve d’ Anna gère on colècte di <a %(ia)s>mètadonnées IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Fitchîs solmint disponibes po imprinter a ene base limitêye, avou diversès restricçons d' accès"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s L' Archive d' Anna gère ene colection di <a %(ia)s>fitchîs IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Divinses basses de dnêyes metadoneyes esparpiyîyes al copete del rantoet chinwès; mins sovint des basses payantes"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nole mètadonnées aîsîment accèssibes po l’ inteyre colècte."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s L’ Archîve d’ Anna gère on colècte di <a %(duxiu)s>mètadonnées DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Divinses basses di donees di fitchîs éparpiyîyes al intèrnèt cinwès; mins sovint des basses di donees payantes"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s L'pus grand dji fitchîs n'sont accesibes ki avou des contes BaiduYun prémium; vitesse di tèlèchargement lente."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s L'Archive d'Anna gère ene coleccion di <a %(duxiu)s>fitchîs DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Divinses ptites ou uniques sôces. Nos encorajhans les djins a tèlècharger dins des ôtes librêyes d'ombre divant, mins des côps les djins ont des coleccions trop grandès po qu'ôtes les trîyèt, mins nén assez grandès po mériter ene catégorie a-els."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Sourdants a mètadoneyes seulmint"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Nos anrichissons ossi nosse colèccion avou des sourdants a mètadoneyes seulmint, qu' nos poumons matcher avou des fitchîs, p.ex. en uzant des numéros ISBN ou d' ôtes champs. Ci-après, vos trovroz ene pådje d' ces sourdants. D' novea, cèrts d' ces sourdants sont totålement ouverts, tandis qu' po d' ôtes nos d'vans les scraper."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Nosse inspiråcion po colècter des mètadonnées est l’ but d’ Aaron Swartz di “on pådje web po chaske livre k’ a stî publîyî”, po l’ kel i a créé <a %(a_openlib)s>Open Library</a>. C’ projèt a bén réyussi, mins nosse position uniqe nos permet d’ avou des mètadonnées k’ i n’ pôrèt nén. On ôte inspiråcion fut nosse dèsir d’ saveur <a %(a_blog)s>combiin d’ livres i gn’ a dins l’ monde</a>, po nosse calculer combiin d’ livres nos avans co a sôver."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Notîz qu' dins l' rechèrche a mètadoneyes, nos montrons les records originåls. Nos n' fèsans nén d' fusion d' records."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Dèrnîre mize a djoû"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Mensuel <a %(dbdumps)s>dépôts di dônees</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nén disponibe di rascance, protejî conte l'aspiyadje"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s L’ Archîve d’ Anna gère on colècte di <a %(worldcat)s>mètadonnées OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Båze di dnêyes unifeye"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Nos combinons tos les sourdants ci-dessus dins ene måye di dnêyes unifeye qu' nos uzons po sèrvi cisse pådje-web. Cisse måye di dnêyes unifeye n' est nén disponibe direktemint, mins come l' Archive d' Anna est totålement open source, ele pout esse fêtîz aîzîmint <a %(a_generated)s>générêye</a> ou <a %(a_downloaded)s>dèscargêye</a> come måyes ElasticSearch et MariaDB. Les scripts so cisse pådje vont otomatikemint dèscargî totes les mètadoneyes nècèssaires des sourdants mentionnêyes ci-dessus."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si vos voloz explorer nos dnêyes divant d' rontchî ces scripts locålemint, vos pouvoz ritchî nos fitchîs JSON, qu' lîhèt pus loin a d' ôtes fitchîs JSON. <a %(a_json)s>Cisse fitchî</a> est on bon pwin di départ."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adapté di nosse <a %(a_href)s>pådje di blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> est onne base di dnêyes massîve di livres scannés, creeye pa l' <a %(superstar_link)s>SuperStar Digital Library Group</a>. Li pus grand d' cisse livres sont des livres academikes, scannés po les meteure a disponibilitê digitale po les universitês et les bibiotêkes. Po nosse public anglès, <a %(princeton_link)s>Princeton</a> et l' <a %(uw_link)s>University of Washington</a> ont des bons rèsumés. I gn a ossi on bon articlê ki dône pus d' contexte: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Les livres di Duxiu ont stî piratés longtimps so l' inteRNèt chinwès. D' habitude, i sont vindi po mwins d' on d'jondje pa des revindeus. I sont tipikemint disbribouyî avou l' équivalent chinwès di Google Drive, ki a sovint stî hacké po permete pus d' plaece di stockadje. D' detays tecnikes sont dispolnibles <a %(link1)s>ci</a> et <a %(link2)s>ci</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Mins les livres ont stî disbribouyî a moité-piblikmint, c' est foirt difficile di les rçure a grand cop. Nos avans metou çoula hôt dins nosse liste di faires, et avans dedicå des mois d' trawes a plin-tins po çoula. Mins, a l' fin di 2023, on volontair incroyable, estonant, et talintreus nos a contacî, nos dîzant k' i avot dja fwait tot ç' trawes — a grand côut. I n' a partagî l' coleccion complète avou nos, sins rwaiter rin en r'tour, a l' exception d' l' garantîye di preservation a long tèrme. Vraimint remarkable."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Ressources"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total di fitchîs: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Total di taille di fitchîs: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Fitchîs mirés pa l'Archive d'Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Dierin mîs a djoû: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrints pa l'Archive d'Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Egzamp di rikord dins l'Archive d'Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Noste pådje di blog so ç' data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripts po importî des metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Format des conteneus di l'Archive d'Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "D' pus d' informåcion di nos volontaires (notes brutes):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Cist set di dnêyes est fort raprôché do <a %(a_datasets_openlib)s>set di dnêyes Open Library</a>. I contint on scrijhaedje di tot lès metadoneyes ey on grand måjon di fitchîs do CDI (Controlled Digital Lending Library) di l’ IA. Les metes a djoû sont rlachêyes dins l’ <a %(a_aac)s>format des Conteneus d’ l’ Archive d’ Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Ces records sont raprôchîs dirèctemint do set di dnêyes Open Library, mins contint ossi des records ki n’ sont nén dins Open Library. Nos avans ossi on måjon di fitchîs di dnêyes scrijhous pa des mambes di l’ comunåté divins les ans."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Li coleccion s’ compôse di deus pårts. Vos dvoz avou lès deus pårts po r’cure totes les dnêyes (sauf les torrents dispassés, ki sont barrés sol pådje des torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "no prumî rlachî, divant k’ nos avans standardizé sol <a %(a_aac)s>format des Conteneus d’ l’ Archive d’ Anna (AAC)</a>. Contint des metadoneyes (come json ey xml), des pdfs (do sistinme di prèt digital acsm ey lcpdf), ey des vignettes di couvire."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "rlachîs noûs incrémentåls, eployant l’ AAC. Contint nén k’ des metadoneyes avou des timestamps après 2023-01-01, pask’ li rès est djà couvert pa “ia”. Ossî tos les fitchîs pdf, cisse fwa ci do sistinme di prèt acsm ey “bookreader” (li lîveus web di l’ IA). Målgré k’ li nom n’ est nén egzactemint bon, nos rimplissans tot d’ même les fitchîs bookreader dins li coleccion ia2_acsmpdf_files, pask’ i sont mutwèlmint exclusîfs."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sicrin %(source)s sitweb"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteke di prèt numerike"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dokumintåcion di metadata (li pus di tchamps)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informåcion do payis ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "L' Agince Internåcionale ISBN relache sovint les renges k' el a allocåves a des aginces nacionåles ISBN. D' ça, nos poumons dedurre a k' payis, région ou groupe di lingaedje ç' ISBN-apartint. Nos eployans asteure cistès donees d' ene manire indirete, avou l' bibioteke Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Ressources"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Dierin mete a djoû: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sitweb ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Po l' histoère des diférints forks d' Library Genesis, veyîz l' page po <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "L' Libgen.li contient l' plupârt des mèmes contenus et métadonnées qu' l' Libgen.rs, mais a quèque collections en plus, nommément des comics, des magazines, et des documents standards. Il a ossi intégré <a %(a_scihub)s>Sci-Hub</a> dins ses métadonnées et moteur d' recherche, qu' c' est c' qu' nos utilisons po nosse base de dôneye."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Les métadonnées po cisse librèrie sont libremint disponibles <a %(a_libgen_li)s>a libgen.li</a>. Cepandant, cisse serveur est lent et n' supporte nén l' reprise des connexions coupées. Les mèmes fichîs sont ossi disponibles so <a %(a_ftp)s>on serveur FTP</a>, qu' fonctionne miyeu."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Les torrents sont ddisponibes po l'pluss des contnus d'ahousse, avou l'pluss d'notabls torrents po des comics, magasines, et documents stanndårds ki ont stî rlachîs en colåboråcion avou l'Årchiye d'Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Li colèccion d'fiction a ses prôpres torrents (differin d' <a %(a_href)s>Libgen.rs</a>) k'comince a %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "D'après l'administråteur di Libgen.li, li colèccion “fiction_rus” (fiction rouske) dût esse couverte pa des torrents rlachîs régulîrmint di <a %(a_booktracker)s>booktracker.org</a>, avou l'pluss d'notabls les torrents <a %(a_flibusta)s>flibusta</a> et <a %(a_librusec)s>lib.rus.ec</a> (ki nos mirons <a %(a_torrents)s>ci</a>, mins nos n'avons nén co stî ki torrents correspondèt a ki fitchîs)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Les statistiques po totes les colèccions pôrèt esse trovées <a %(a_href)s>so l'site di libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Li non-fictif a l'air d'aveur diverjî, mins sins noveas tôrints. I s'pareut ki c'çi a stî l'cas d'pwis l'debut 2022, mins nos n'avons nén vérifiî c'la."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Cértins rangs sins torrents (come les rangs d'fiction f_3463000 a f_4260000) sont probåblemint des fitchîs di Z-Library (ou d'ôtes doublons), mins nos pourrîs volrî fêre on ptit d'dédoublonnådje et fêre des torrents po les fitchîs uniques di lgli dins ces rangs."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Notez qu' les fichîs torrent qu' réfèrent a “libgen.is” sont explicitement des mirroirs de <a %(a_libgen)s>Libgen.rs</a> (“.is” est on ôte domaine utilisé par Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Onne ressource utile po utiliser les métadonnées est <a %(a_href)s>cisse page</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de fiction so l' Archive d' Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de comics so l' Archive d' Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de magazines so l' Archive d' Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents di documents stanndårds so l'Årchiye d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents di fiction rouske so l'Årchiye d'Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Métadonnées"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Métadonnées via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informations des champs de métadonnées"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Miroir d'autres torrents (et torrents uniques de fiction et de bandes dessinées)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum de discussion"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Notre article de blog sur la sortie des bandes dessinées"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "L'histoire rapide des différents forks de Library Genesis (ou “Libgen”) est qu'au fil du temps, les différentes personnes impliquées dans Library Genesis se sont disputées et ont pris des chemins séparés."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La version “.fun” a été créée par le fondateur original. Elle est en cours de refonte en faveur d'une nouvelle version plus distribuée."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La version “.rs” a des données très similaires et publie le plus souvent sa collection en torrents groupés. Elle est grossièrement divisée en une section “fiction” et une section “non-fiction”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "A l'origene a “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>version “.li”</a> a une collection massive de bandes dessinées, ainsi que d'autres contenus, qui ne sont pas (encore) disponibles pour le téléchargement en masse via torrents. Elle a une collection de torrents séparée pour les livres de fiction, et elle contient les métadonnées de <a %(a_scihub)s>Sci-Hub</a> dans sa base de données."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "D'après cisse <a %(a_mhut)s>poste di forum</a>, Libgen.li a stî hosté a l'origene a “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> est en quelque sorte aussi un fork de Library Genesis, bien qu'ils aient utilisé un nom différent pour leur projet."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Cette page concerne la version “.rs”. Elle est connue pour publier de manière cohérente à la fois ses métadonnées et le contenu complet de son catalogue de livres. Sa collection de livres est divisée entre une partie fiction et une partie non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Une ressource utile pour utiliser les métadonnées est <a %(a_metadata)s>cette page</a> (bloque les plages IP, un VPN peut être nécessaire)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "À partir de 2024-03, de nouveaux torrents sont publiés dans <a %(a_href)s>ce fil de discussion du forum</a> (bloque les plages IP, un VPN peut être nécessaire)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de non-fiction sur Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de fiction sur Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Métadonnées de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informations sur les champs de métadonnées de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de non-fiction de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de fiction de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum de discussion de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents par Anna’s Archive (couvertures de livres)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Noste blogu a l'propos des couvertures d'livres"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis est connu pour déjà mettre généreusement leurs données à disposition en vrac via des torrents. Nos collections Libgen consistent en des données auxiliaires qu'ils ne publient pas directement, en partenariat avec eux. Un grand merci à tous ceux impliqués avec Library Genesis pour travailler avec nous !"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Sortie 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "C'te <a %(blog_post)s>première sortie</a> est assez petite : environ 300GB de couvertures de livres de la branche Libgen.rs, tant de fiction que de non-fiction. Elles sont organisées de la même manière qu'elles apparaissent sur libgen.rs, par exemple :"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pour un livre de non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pour un livre de fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Tout comme avec la collection Z-Library, nous les avons toutes mises dans un gros fichier .tar, qui peut être monté en utilisant <a %(a_ratarmount)s>ratarmount</a> si vous voulez servir les fichiers directement."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> est onne base di dnêyes propriyêye pa l' a.s.b.l. <a %(a_oclc)s>OCLC</a>, ki rasamblo les rikôds di metadata des bibiotêkes di tot l' monde. C' est poyone li pus grande coleccion di metadata di bibiotêkes å monde."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octôbe 2023, prumîre vèrsion:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "En octôbe 2023, nos avans <a %(a_scrape)s>sôrtou</a> on scrapotchî do database OCLC (WorldCat), dins l' <a %(a_aac)s>format des Conteneus di l' Archive d' Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrints pa l' Archive d' Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Noste pådje di blog so ç' data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library est un projet open source par l'Internet Archive pour cataloguer chaque livre dans le monde. Il a une des plus grandes opérations de numérisation de livres au monde, et a beaucoup de livres disponibles pour le prêt numérique. Son catalogue de métadonnées de livres est librement disponible pour téléchargement, et est inclus sur Anna’s Archive (bien que pas actuellement dans la recherche, sauf si vous cherchez explicitement un ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Métadonnées"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rilêye 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Cisse-ci est on dump d' on grand nombre d' apêls a isbndb.com pendant setimbe 2022. Nos avons essayé d' couvrir totes les renges ISBN. Cisse sont a peu prés 30,9 millions d' enregistremints. So leur site, ils prétindèt qu' ils ont en fait 32,6 millions d' enregistremints, donc nos avans pû manquer quèque-chose, ou <em>ils</em> pôront fére quèque-chose d' fô."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Les réponses JSON sont presqu' brutes de leur serveur. On problème d' qualité d' dôneye qu' nos avans remarqué, c' est qu' po les numéros ISBN-13 qui commencent avou on ôte préfixe qu' “978-”, ils inclut totjous on champ “isbn” qui est simplement l' numéro ISBN-13 avou les trois premiers chiffres coupés (et l' chiffre de contrôle recalculé). Cisse est visiblimint fô, mais c' est come ça qu' ils semblèt fére, donc nos n' l' avons nén altéré."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "On ôte problème potentiel qu' vos pôrriéz rencontrer, c' est l' fait qu' l' champ “isbn13” a des duplicats, donc vos n' pôréz nén l' utiliser come clé primaire dins onne base de dôneye. Les champs “isbn13”+“isbn” combinés semblèt être uniques."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pour un contexte sur Sci-Hub, veuillez vous référer à son <a %(a_scihub)s>site officiel</a>, <a %(a_wikipedia)s>page Wikipedia</a>, et cet <a %(a_radiolab)s>entretien de podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Notez que Sci-Hub a été <a %(a_reddit)s>gelé depuis 2021</a>. Il a été gelé avant, mais en 2021 quelques millions d'articles ont été ajoutés. Cependant, un nombre limité d'articles continue d'être ajouté aux collections “scimag” de Libgen, bien que pas assez pour justifier de nouveaux torrents en vrac."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Nous utilisons les métadonnées de Sci-Hub fournies par <a %(a_libgen_li)s>Libgen.li</a> dans sa collection “scimag”. Nous utilisons également le dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Notez que les torrents “smarch” sont <a %(a_smarch)s>dépréciés</a> et donc pas inclus dans notre liste de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents sur Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Métadonnées et torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents sur Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents sur Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Mises à jour sur Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia pådje"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast inteviouwe"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Tchargetes sol l' Archive d' Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Aperçu del <a %(a1)s>pådje des Datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Diveres ptites ou uniques sôces. Nos ancoradjans les djins a tcharger dins d' ôtes bibiotèques d' omes, mins des côps les djins ont des coleccions trop grandès po les ôtes po les trîyer, mins nén assez grandès po mériter ene cateyegorie a-els."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Li coleccion \"tchargete\" est dividêye en ptites sô-coleccions, ki sont indicêyes dins les AACIDs et les nooms des torrents. Tote les sô-coleccions ont stî dedoublêyes avou l' coleccion principåle, mins les fitchîs JSON di metadoneye \"tchargete_records\" continut co ene sacwêye di referinces ås fitchîs originåls. Les fitchîs ki n' sont nén des livès ont stî oci enlevés di pus d' sô-coleccions, et sont tipikement <em>nén</em> notés dins les JSON \"tchargete_records\"."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Bokés di sô-coleccions sont a-els-memes compozêyes di sô-sô-coleccions (p.ex. di diveres sôces originåles), ki sont represetêyes come ridants dins les tchamps \"filepath\"."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Les soucoleccions sont :"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Soubcollection"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notes"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "flantchî"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cwèri"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "D' <a %(a_href)s>aaaaarg.fail</a>. Sémble esse assez complèt. D' nosse volontêre \"cgiym\"."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "D'ine <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. I gn a eneutreye di pus d'papers ki egzistêye, mins ritchîre di matches MD5, donk nos avans dicidé di l'conserver tot."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raclotche di <q>iRead eBooks</q> (= fonetike <q>ai rit i-books</q>; airitibooks.com), pa volontaire <q>j</q>. Coorespond å <q>airitibooks</q> metadata dins <a %(a1)s><q>Ôtes raclotches di metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Dins ene collection <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. D'parti del sourdjinne origénale, d'parti del the-eye.eu, d'parti d'ôtes mirwès."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "D'ine waibe di torrents di livans privêye, <a %(a_href)s>Bibliotik</a> (souvent apelêye “Bib”), dont les livans ont stî rassemblés dins des torrents solon l'no (A.torrent, B.torrent) et distrubués avou the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "D'notr' volonter “bpb9v”. Po pus d'informåcion sol <a %(a_href)s>CADAL</a>, veyoz les notes dins nosse <a %(a_duxiu)s>pådje di dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "D'pus di nosse volonter “bpb9v”, surtout des fitchîs DuXiu, ossi bén qu'on folder “WenQu” et “SuperStar_Journals” (SuperStar c'est l'compegnie dèrriére DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "D' nosse volontêre \"cgiym\", tecses chinwès di diveres sôces (represetêyes come sô-ridants), incluzant d' <a %(a_href)s>China Machine Press</a> (ene grosse éditeû chinwès)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleccions nén tchinoises (represintêyes come sô-sictoires) di nosse volonter “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raclotche di livans so l'architekture cinwèsse, pa volontaire <q>cm</q>: <q>I l'ai obténou en exploitant ene vulnerabilité di rantoele al maison d'édition, mins cisse brèche est stî fermêye d'pwis</q>. Coorespond å <q>chinese_architecture</q> metadata dins <a %(a1)s><q>Ôtes raclotches di metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Livans di l'måjhon d'édition académike <a %(a_href)s>De Gruyter</a>, rassemblés a partir di pusîs grands torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrap di <a %(a_href)s>docer.pl</a>, on waibe polonêye di partaedje di fitchîs focalizé sol les livans et ôtes ovres scrites. Scrapé a l'fin 2023 pa volonter “p”. Nos n'avans nén di bons métadonnées di l'waibe originnal (même nén les extensions di fitchîs), mins nos avans filtré po des fitchîs ki r'semblèt a des livans et nos avans sovint stî capåbles d'extraire des métadonnées dès fitchîs minmes."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, dirèctement di DuXiu, rassemblés pa volonter “w”. Nolemint les livans DuXiu réçints sont disponibes dirèctement avou des ebooks, donk l'most d'cès-i dèt esse réçints."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Rimindants fitchîs DuXiu di volonter “m”, ki n'étént nén dins l'format propriétaire PDG di DuXiu (l'principal <a %(a_href)s>dataset DuXiu</a>). Rassemblés a partir di pusîs sôces originnales, måheureusement sins conserver cès sôces dins l'chemin d'accès."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raclotche di livans érotiques, pa volontaire <q>do no harm</q>. Coorespond å <q>hentai</q> metadata dins <a %(a1)s><q>Ôtes raclotches di metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Coleccion scrapée d'on éditeur di Manga djaponês pa volonter “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archives judiciaires choisies de Longquan</a>, fournies pa volonter “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrap di <a %(a_href)s>magzdb.org</a>, on alyé di Library Genesis (c'est lié sol l'page d'accueil di libgen.rs) mins ki n'voulèt nén fournir leurs fitchîs dirèctement. Obtenu pa volonter “p” a l'fin 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "D'våriès ptites montêyes, trop ptites po esse des sô-coleccions a-els minmes, mins represintêyes come sictoires."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks d'AvaxHome, on site di partaedje di fitchîs rouske."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archive di djournåls et magazinnes. Coorespond å <q>newsarch_magz</q> metadata dins <a %(a1)s><q>Ôtes raclotches di metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raclotche del <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Coleccion di volonter “o” ki a rassemblé des livans polonês dirèctement di waibes d'originnal “scene”."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coleccions combinêyes di <a %(a_href)s>shuge.org</a> pa volontaires “cgiym” et “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Bibliothèque Impériale de Trantor”</a> (nommêye d'après l'bibliothèque fictive), scrapée en 2022 pa volonter “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sô-sô-coleccions (represintêyes come sictoires) di volonter “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (pa <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taïwan), mebook (mebook.cc, 我的小书屋, min ptite bibliothèque — woz9ts: “Cisse site est focalizé sol l'partaedje di fitchîs ebooks di hôte qualité, dèsquels l'propriétaire a minme fait l'mise en page. L'propriétaire a stî <a %(a_arrested)s>arresté</a> en 2019 et onk a fait on coleccion di fitchîs k'il a partaijé.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Rimindants fitchîs DuXiu di volonter “woz9ts”, ki n'étént nén dins l'format propriétaire PDG di DuXiu (ki dèt esse converti a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents pa l'Archive d'Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Grattache di Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library a ses racines dins l'comunåté <a %(a_href)s>Library Genesis</a>, et a d'abord été démarré avou leurs donees. D'pwis, i s'a professionnalisé considéråbl'mint, et a ene inteface pus modèrne. I sont donc capåbes d'obtenir pus d'donåcions, tant monétairès po continouwer a mieteu ll' site, come des donåcions di noveas livrès. I ont amassé ene grand coleccion en plus di Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Mete a djoû come di fevrî 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "A l' fin di 2022, les pretindous fondatêus di Z-Library ont stî arestés, et les dominnes ont stî saisîs pa les ôtorités des Estats-Unis. D' puis, l' setis web a lentement r'vnu en linne. On n' sait nén ki l' djusse gère."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Li coleccion est compozêye di troes ptis. Les pådjes di descrijhaedje originåles po les deus prumîs ptis sont consèrvêyes ci dzo. Vos dvoz totes les troes ptis po r'cuvrer totes les dnêyes (sauf les torrents remplacés, ki sont barrés sol pådje des torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nosse prumîre sortidje. Cisse ci fut l' prumîre sortidje di c' ki fut alowes apelêye li \"Pirate Library Mirror\" (\"pilimi\")."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s : deujhinme rilêye, cisse fwa avou tos les fitchîs envelopés dins des fitchîs .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s : noveas rilêyes incrémentales, avou l'format <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, asteure rilêyîs en colåboråcion avou l'équipe di Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torints par l'Archive d'Anna (métadonees + contnou)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exempte d'record dins l'Archive d'Anna (coleccion originåle)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Egzample d' enregistrement sol l' Archive d' Anna (coleccion \"zlib3\")"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sît principå"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domaine Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Billet di blog a propôs di Rilêye 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Billet di blog a propôs di Rilêye 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Rilêyes Zlib (pådjes di descripcions originåles)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Sortidje 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Li mirroer initial a stî obtinu avou grand' peinne tot l' long di 2021 et 2022. A cisse pîre, il est on pô d'passé: il reflète l' eta di l' coleccion en djun 2021. Nos l' metrons a djoû dins l' avenir. A cisse pîre, nos s' concentrans sol l' sortidje di cisse prumîre version."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Piske Library Genesis est ddja consèrvêye avou des torrents publyics, et est incluzou dins li Z-Library, nos avans fêt ene dedoublêye basike avou Library Genesis en djun 2022. Po cisse ci, nos avans eployî des hashes MD5. I gn a probåblement ene sacwêye di contnou dedoublé dins l' bibiotèque, come des formats di fitchîs multiples avou li minme live. Cisse ci est d'jheye a dtecter avou precis, don nos n' l' fêt nén. Après l' dedoublêye, nos r'stans avou pus di 2 milion di fitchîs, totalizant djusse en dzo di 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Li colection est fwait di deus ptis: ene dump MySQL “.sql.gz” del metadata, et les 72 fitchîs torrent di pus o mins 50-100GB chas. Li metadata contint les dnêyes rapoirtêyes pa l' sitweb Z-Library (titro, ôteur, descripcions, tipe di fitchî), tot come li taille di fitchî et md5sum ki nos avans veyou, paskes des côps, celes n' sont nén d' acwèrd. I s' parest k' i gn a des renges di fitchîs po lesquels li Z-Library minme a des metadata fôsses. Nos avans ossu pôt-étre des fitchîs mal djichargés dins des cas isolés, k' nos essayerans di detecter et d' rimetre a l' avenir."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Les gros fitchîs torrent contnèt l' dnêye di live efetive, avou l' ID di Z-Library come noom di fitchî. Les extensions di fitchîs pôront esse r'constituêyes eployant li metadoneye dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Li colection est ene mêye di contnou non-fictif et fictif (nén séparé come dins Library Genesis). Li qualité est ossu fort divèrse."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Cisse prumîre vèrsion est asteure totavåye disponibe. R'marquîz k' les fitchîs torrent sont solmint disponibes a travèrs nosse mirroir Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Vèrsion 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Nos avans r'côlté tos les livrès ki ont stî radjoutés a Z-Library inte nosse dèrnî mirroir et awousse 2022. Nos avans ossu r'vnu en arîre et r'côlté des livrès ki nos avans raté la prumîre feis. Tot conte, cisse novele colection est d' aprôsimatif 24TB. D' novea, cisse colection est dédupliquée avou Library Genesis, paski i gn a ddja des torrents disponibes po cisse colection."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Les dnêyes sont organizêyes come dins li prumîre vèrsion. I gn a ene dump MySQL “.sql.gz” del metadata, ki inclut ossu tot li metadata del prumîre vèrsion, et ki l' rimplaçe. Nos avans ossu radjouté des noveas colones:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: si cisse fitchî est ddja dins Library Genesis, dins l' colection non-fictif ou fictif (correspondou avou md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: dins kel torrent cisse fitchî est."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: metou cand nos n' avans nén stî capåbles di tcharger li live."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Nos avans mentionné ci-st-ont, mins po bén clar: “filename” et “md5” sont les propriytés réeles do fitchî, tandis qu’ “filename_reported” et “md5_reported” sont c’ qu’on a raclî di Z-Library. A-st-ont, ces deus n’ s’acordèt nén, don on a inclou les deus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Po cisse vèrsion, nos avans candjî l’ordinnance a “utf8mb4_unicode_ci”, qui dût esse comtatibe avou des vèrsions pus vîyes di MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Les fitchîs di dônees sont similes a ci-st-ont, mins sont bén pus grand. Nos n’ pouvans nén s’ tracasser a fé des p’tits fitchîs torrent. “pilimi-zlib2-0-14679999-extra.torrent” contint tos les fitchîs qu’on a raté dins l’ dèrnîre vèrsion, tandis qu’ les ôtes torrents sont totes des noveas tranches d’ ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Mise a djoû %(date)s:</strong> Nos avans fé la plupârt d’ nos torrents trop grands, çou qu’ a fêt des diffilcultés po les cliyints torrent. Nos les avans enlevés et relachî des noveas torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Mise a djoû %(date)s:</strong> I gn’ aveut co trop d’ fitchîs, don nos les avans envelopés dins des fitchîs tar et relachî des noveas torrents oncor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Vèrsion 2 adjonction (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "C’ est on seul fitchî torrent d’ supla. I n’ contint nén d’ noveas infôrmåcions, mins i a des dônees dins çoula qui pôrèt prinde on p’tit tins a calculer. Çoula l’ rend comode a avou, paské d’ tèlcharger cisse torrent est sovint pus vite ki d’ l’ calculer dès l’ comince. En particulî, i contint des index SQLite po les fitchîs tar, po l’ uzance avou <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Questions fréquemment posées (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Qu’est-ce que les Archives d’Anna ?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Les Archives d’Anna</span> est un projet à but non lucratif avec deux objectifs :"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Préservation :</strong> Sauvegarder toutes les connaissances et la culture de l’humanité.</li><li><strong>Accès :</strong> Rendre ces connaissances et cette culture accessibles à tous dans le monde.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tout notre <a %(a_code)s>code</a> et nos <a %(a_datasets)s>données</a> sont entièrement open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Préservation"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Nous préservons des livres, articles, bandes dessinées, magazines, et plus encore, en rassemblant ces matériaux provenant de diverses <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothèques de l’ombre</a>, bibliothèques officielles, et autres collections en un seul endroit. Toutes ces données sont préservées pour toujours en facilitant leur duplication en masse — en utilisant des torrents — ce qui entraîne de nombreuses copies à travers le monde. Certaines bibliothèques de l’ombre le font déjà elles-mêmes (par exemple Sci-Hub, Library Genesis), tandis que les Archives d’Anna “libèrent” d’autres bibliothèques qui n’offrent pas de distribution en masse (par exemple Z-Library) ou ne sont pas du tout des bibliothèques de l’ombre (par exemple Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Cette large distribution, combinée avec du code open source, rend notre site web résistant aux suppressions, et assure la préservation à long terme des connaissances et de la culture de l’humanité. En savoir plus sur <a href=\"/datasets\">nos ensembles de données</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Nous estimons avoir préservé environ <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% des livres du monde</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Accès"

#, fuzzy
msgid "page.home.access.text"
msgstr "Nous travaillons avec des partenaires pour rendre nos collections facilement et librement accessibles à tous. Nous croyons que tout le monde a droit à la sagesse collective de l’humanité. Et <a %(a_search)s>pas au détriment des auteurs</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Téléchargements horaires au cours des 30 derniers jours. Moyenne horaire : %(hourly)s. Moyenne quotidienne : %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Nous croyons fermement en la libre circulation de l’information, et en la préservation des connaissances et de la culture. Avec ce moteur de recherche, nous nous appuyons sur les épaules de géants. Nous respectons profondément le travail acharné des personnes qui ont créé les diverses bibliothèques de l’ombre, et nous espérons que ce moteur de recherche élargira leur portée."

#, fuzzy
msgid "page.about.text3"
msgstr "Pour rester informé de nos progrès, suivez Anna sur <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pour toute question ou commentaire, veuillez contacter Anna à %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Comment puis-je aider ?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Suivez-nous sur <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Parlez des Archives d’Anna sur Twitter, Reddit, Tiktok, Instagram, dans votre café ou bibliothèque locale, ou partout où vous allez ! Nous ne croyons pas à la rétention d’information — si nous sommes supprimés, nous réapparaîtrons ailleurs, puisque tout notre code et nos données sont entièrement open source.</li><li>3. Si vous le pouvez, envisagez de <a href=\"/donate\">faire un don</a>.</li><li>4. Aidez à <a href=\"https://translate.annas-software.org/\">traduire</a> notre site web dans différentes langues.</li><li>5. Si vous êtes ingénieur logiciel, envisagez de contribuer à notre <a href=\"https://annas-software.org/\">open source</a>, ou de partager nos <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Nos avons asteure ossi on tchénal Matrix syncronizé a %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Si vous êtes chercheur en sécurité, nous pouvons utiliser vos compétences à la fois pour l’attaque et la défense. Consultez notre page <a %(a_security)s>Sécurité</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Nous recherchons des experts en paiements pour marchands anonymes. Pouvez-vous nous aider à ajouter des moyens de don plus pratiques ? PayPal, WeChat, cartes cadeaux. Si vous connaissez quelqu’un, veuillez nous contacter."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Nous recherchons toujours plus de capacité serveur."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Vous pouvez aider en signalant des problèmes de fichiers, en laissant des commentaires, et en créant des listes directement sur ce site web. Vous pouvez également aider en <a %(a_upload)s>téléchargeant plus de livres</a>, ou en corrigeant des problèmes de fichiers ou de formatage des livres existants."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Créer ou aider à maintenir la page Wikipédia pour Anna’s Archive dans votre langue."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Nous cherchons à placer de petites publicités discrètes. Si vous souhaitez faire de la publicité sur Anna’s Archive, veuillez nous le faire savoir."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Nous aimerions que des personnes mettent en place des <a %(a_mirrors)s>miroirs</a>, et nous les soutiendrons financièrement."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Po pus d' informåcions sol coince di volontair, veyoz nosse pådje <a %(a_volunteering)s>Volontair & Prîmes</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Po tcho k' les tchargets lints sont-i si lints?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Nous n'avons littéralement pas assez de ressources pour offrir à tout le monde dans le monde des téléchargements à haute vitesse, même si nous le souhaitons. Si un bienfaiteur riche souhaite intervenir et nous fournir cela, ce serait incroyable, mais en attendant, nous faisons de notre mieux. Nous sommes un projet à but non lucratif qui peut à peine se maintenir grâce aux dons."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "C'est pourquoi nous avons mis en place deux systèmes pour les téléchargements gratuits, avec nos partenaires : des serveurs partagés avec des téléchargements lents, et des serveurs légèrement plus rapides avec une liste d'attente (pour réduire le nombre de personnes téléchargeant en même temps)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Nous avons également une <a %(a_verification)s>vérification du navigateur</a> pour nos téléchargements lents, car sinon les bots et les scrapers en abuseraient, rendant les choses encore plus lentes pour les utilisateurs légitimes."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Notîz qu', en uzant l' Tor Browser, vos d'vroz adjuwer vosse configuråcion di sécureté. Avou l' pus bas des ôptions, apelêye “Standard”, l' défi Cloudflare rissit. Avou les ôptions pus hôtes, apelêyes “Safer” et “Safest”, l' défi échout."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Po les grands fitchîs, les djihets lentes polèt cassî al miyeu. Nos racomandans d' eployî on djèstionaire di djihets (come JDownloader) po riprinde ôtomaticmint les grands djihets."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "FAQ sur les dons"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Les adhésions se renouvellent-elles automatiquement ?</div> Les adhésions <strong>ne se renouvellent pas</strong> automatiquement. Vous pouvez adhérer pour la durée que vous souhaitez."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Est-åke pôreye amélioyer mi abonmint ou bén n' aveur di pus d' on abonmint ?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Avez-vous d'autres méthodes de paiement ?</div> Actuellement non. Beaucoup de gens ne veulent pas que des archives comme celle-ci existent, donc nous devons être prudents. Si vous pouvez nous aider à mettre en place d'autres méthodes de paiement (plus pratiques) en toute sécurité, veuillez nous contacter à %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Cwè signifient les renges par mois?</div> Vos poloz ariver a l' costé bas d' ene renge en aplicant tos les rabais, come tchoezi ene période pus longe qu' on mois."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>À quoi servent les dons ?</div> 100%% est destiné à préserver et rendre accessible les connaissances et la culture du monde. Actuellement, nous dépensons principalement pour les serveurs, le stockage et la bande passante. Aucun argent ne va personnellement aux membres de l'équipe."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Puis-je faire un don important ?</div> Ce serait incroyable ! Pour les dons de plus de quelques milliers de dollars, veuillez nous contacter directement à %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Puis-je faire un don sans devenir membre ?</div> Bien sûr. Nous acceptons les dons de tout montant à cette adresse Monero (XMR) : %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Comment puis-je télécharger de nouveaux livres ?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativement, vous pouvez les télécharger sur Z-Library <a %(a_upload)s>ici</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Po ptits uploads (djusque 10.000 fitchîs) plait avoz les uploadî a l'deus %(first)s et %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Pour l'instant, nous suggérons de télécharger de nouveaux livres sur les forks de Library Genesis. Voici un <a %(a_guide)s>guide pratique</a>. Notez que les deux forks que nous indexons sur ce site web tirent de ce même système de téléchargement."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Po Libgen.li, assurez-vos d'prumî s'loger su <a %(a_forum)s >leur forum</a> avou l'uzername %(username)s et l'mot d'pass %(password)s, et adon r'vni a leur <a %(a_upload_page)s >pådje d'upload</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Si votre adresse e-mail ne fonctionne pas sur les forums de Libgen, nous recommandons d'utiliser <a %(a_mail)s>Proton Mail</a> (gratuit). Vous pouvez également <a %(a_manual)s>demander manuellement</a> l'activation de votre compte."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Notez que mhut.org bloque certaines plages d'IP, donc un VPN pourrait être nécessaire."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pour les téléchargements volumineux (plus de 10 000 fichiers) qui ne sont pas acceptés par Libgen ou Z-Library, veuillez nous contacter à %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Pour télécharger des articles académiques, veuillez également (en plus de Library Genesis) les télécharger sur <a %(a_stc_nexus)s>STC Nexus</a>. Ils sont la meilleure bibliothèque de l'ombre pour les nouveaux articles. Nous ne les avons pas encore intégrés, mais nous le ferons à un moment donné. Vous pouvez utiliser leur <a %(a_telegram)s>bot de téléchargement sur Telegram</a>, ou contacter l'adresse indiquée dans leur message épinglé si vous avez trop de fichiers à télécharger de cette manière."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Comment puis-je demander des livres ?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Pour l'instant, nous ne pouvons pas répondre aux demandes de livres."

#, fuzzy
msgid "page.request.forums"
msgstr "Veuillez faire vos demandes sur les forums de Z-Library ou Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Ne nous envoyez pas vos demandes de livres par email."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Collectez-vous des métadonnées ?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Nous le faisons effectivement."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "J'ai téléchargé 1984 de George Orwell, la police va-t-elle venir chez moi ?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Ne vous inquiétez pas trop, il y a beaucoup de gens qui téléchargent à partir des sites que nous recommandons, et il est extrêmement rare d'avoir des problèmes. Cependant, pour rester en sécurité, nous recommandons d'utiliser un VPN (payant), ou <a %(a_tor)s>Tor</a> (gratuit)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Comment puis-je sauvegarder mes paramètres de recherche ?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Sélectionnez les paramètres que vous aimez, laissez la boîte de recherche vide, cliquez sur \"Rechercher\", puis ajoutez la page aux favoris en utilisant la fonction de favoris de votre navigateur."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Avez-vous une application mobile ?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nous n'avons pas d'application mobile officielle, mais vous pouvez installer ce site web comme une application."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Cliquez sur le menu à trois points en haut à droite, et sélectionnez \"Ajouter à l'écran d'accueil\"."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Cliquez sur le bouton \"Partager\" en bas, et sélectionnez \"Ajouter à l'écran d'accueil\"."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Avez-vous une API ?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Nous avons une API JSON stable pour les membres, pour obtenir une URL de téléchargement rapide : <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation dans le JSON lui-même)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pour d'autres cas d'utilisation, comme parcourir tous nos fichiers, construire une recherche personnalisée, etc., nous recommandons <a %(a_generate)s>de générer</a> ou <a %(a_download)s>de télécharger</a> nos bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement <a %(a_explore)s>via des fichiers JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Notre liste de torrents bruts peut également être téléchargée en <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ sur les torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Je voudrais aider à partager, mais je n'ai pas beaucoup d'espace disque."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Utilisez le <a %(a_list)s>générateur de liste de torrents</a> pour générer une liste de torrents qui ont le plus besoin d'être partagés, dans les limites de votre espace de stockage."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Les torrents sont trop lents ; puis-je télécharger les données directement depuis chez vous ?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Oui, consultez la page <a %(a_llm)s>données LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Puis-je télécharger seulement un sous-ensemble des fichiers, comme une langue ou un sujet particulier ?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Rèponse court: nén åjhe."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Rèponse long:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "La plupart des torrents contiennent les fichiers directement, ce qui signifie que vous pouvez demander aux clients torrent de ne télécharger que les fichiers nécessaires. Pour déterminer quels fichiers télécharger, vous pouvez <a %(a_generate)s>générer</a> nos métadonnées, ou <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Malheureusement, un certain nombre de collections de torrents contiennent des fichiers .zip ou .tar à la racine, auquel cas vous devez télécharger l'intégralité du torrent avant de pouvoir sélectionner des fichiers individuels."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Nos avoz <a %(a_ideas)s>quérques idêyes</a> po cisse dèrmîre cå.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nole di fitchîs aisse di eployî po fitchîs des torrents n' sont nén ddisponibes asteure, mins nos r'cwerans les contributions."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Comment gérez-vous les doublons dans les torrents ?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Nous essayons de minimiser la duplication ou le chevauchement entre les torrents de cette liste, mais cela ne peut pas toujours être réalisé et dépend fortement des politiques des bibliothèques sources. Pour les bibliothèques qui publient leurs propres torrents, cela ne dépend pas de nous. Pour les torrents publiés par Anna’s Archive, nous dédupliquons uniquement en fonction du hachage MD5, ce qui signifie que différentes versions du même livre ne sont pas dédupliquées."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Puis-je obtenir la liste des torrents en JSON ?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Oui."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Je ne vois pas de PDFs ou d’EPUBs dans les torrents, seulement des fichiers binaires ? Que dois-je faire ?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Ce sont en fait des PDFs et des EPUBs, ils n'ont tout simplement pas d'extension dans beaucoup de nos torrents. Il y a deux endroits où vous pouvez trouver les métadonnées des fichiers torrent, y compris les types/extensions de fichiers :"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Chaque collection ou publication a ses propres métadonnées. Par exemple, les <a %(a_libgen_nonfic)s>torrents Libgen.rs</a> ont une base de données de métadonnées correspondante hébergée sur le site Libgen.rs. Nous lions généralement les ressources de métadonnées pertinentes à partir de la <a %(a_datasets)s>page de dataset</a> de chaque collection."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Nous recommandons de <a %(a_generate)s>générer</a> ou de <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Celles-ci contiennent une correspondance pour chaque enregistrement dans Anna’s Archive avec ses fichiers torrent correspondants (si disponibles), sous \"torrent_paths\" dans le JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Porké mi clint di torrent n' sait nén drovi des fitchîs torrent / des lîens magnete di vosse?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Certins clints di torrent n' suportèt nén les grandès tailles di pîces, k' onk a-bén des nos torrents ont (po les pus nûvs, nos n' faysans pus çoula — mêm s' c' est valide solon les spécifications!). Dji vos consèy d' eprover on ôte clint si vos avou des problins, ou d' s' plindre ôs fabeûrs di vosse clint di torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Avez-vous un programme de divulgation responsable ?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Nous invitons les chercheurs en sécurité à rechercher des vulnérabilités dans nos systèmes. Nous sommes de grands partisans de la divulgation responsable. Contactez-nous <a %(a_contact)s>ici</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Nous ne sommes actuellement pas en mesure d'offrir des primes de bogues, sauf pour les vulnérabilités qui ont le <a %(a_link)s>potentiel de compromettre notre anonymat</a>, pour lesquelles nous offrons des primes allant de 10 000 à 50 000 $. Nous aimerions offrir une portée plus large pour les primes de bogues à l'avenir ! Veuillez noter que les attaques d'ingénierie sociale sont hors de portée."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si vous êtes intéressé par la sécurité offensive et souhaitez aider à archiver les connaissances et la culture du monde, assurez-vous de nous contacter. Il y a de nombreuses façons dont vous pouvez aider."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Y a-t-il plus de ressources sur Anna’s Archive ?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog d’Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — mises à jour régulières"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Logiciel d’Anna</a> — notre code open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduire sur le logiciel d’Anna</a> — notre système de traduction"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — å propôs des dnêyes"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominnes alternatives"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — pus d' informåcion sol nos-ôtes (s' il vos plait, aidîz a mete cisse pådje a djoû, ou bén creyez ene dins vosse langaedje!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Come est-ce ki dj' pôreye rapoirtî ene infraction des droets d' ôteur?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nos n' hôtans nén d' materiyels copîyîs ci. Nos-ôtes sommes on motîr di rechèrche, et come tèl, nos indexans k' les metadoneyes ki sont djà dispolnibles al publyec. Quand vos djiheut d' ces sôres esterneyes, nos vos consèyans d' veyî les lwès dins vosse djuridiction a propôs di çou ki est permis. Nos n' estans nén responsåbles do contnou hôté pa d' ôtes."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si vos avoz des plintes a fere a propôs di çou k' vos veyoz ci, li miyeu est d' contakter l' sitwèbe originål. Nos ratchapans régulîrmint leus candjmints dins nosse basse di dnêyes. Si vos croyez vrêrmint k' vos avoz ene plinte DMCA valide a nosse rapôrter, voloz bin rimplî l' <a %(a_copyright)s>fôrme di plinte DMCA / droets d' ôteur</a>. Nos prindans vosse plinte a cwer, et vos ritcherans tot d' suite."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Dj' n' aime nén come vos-ôtes gîdîz cisse prôjèt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Nos volrîyons ossu rapelî a tertos k' tot nosse code et dnêyes sont totålemint åvèrts. C' est unique po des prôjèts come li nôte — nos n' connessans nén d' ôte prôjèt avou on catålogue si massîf ki est ossu totålemint åvèrt. Nos acceyans volintîrs tertos k' i croheut k' nos gîdans mal nosse prôjèt di prinde nosse code et dnêyes et d' metter en-ôte leus prôpe bibiotèkès d' ombrådje! Nos n' disans nén çoula pa rencoeur ou bén-ôte — nos pensans vrêrmint k' çoula serèst magnîfique come çoula r'hausserèst l' nivêye po tertos, et mîy preserve l' héritådje di l' umanité."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Vos-avez on monitor d'uptime?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Veyoz s'il vos plait <a %(a_href)s>cisse magnîfique projet</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Comint d'jheûne doner des livres ou d' ôtes matérèls fysikes?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Veyez les evoyî a l' <a %(a_archive)s>Internet Archive</a>. Ils les prumèteront come i fåt."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Ki est-ce qu'Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Vos estoz Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Quåls sont vosseus livres préferés?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Volaey des livres k' ont ene signification spèciale po l' monde des bibiotèkès d' ombrådje et di l' préservation numerike:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Vos avoz-étout eployî vosseus tèlèchargemints rapîdes po asteure."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Dèviné mimbre po eployî des tèlèchargemints rapîds."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Nos sopoirtans asteure les cartes-cadeaux Amazon, les cartes di crédit et dèbit, les cryptomonnèyes, Alipay, et WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Basse di dnêyes complète"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Livres, papîs, magazinnes, comics, records di bibiotèkès, metadoneyes, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Rechèrche"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "béta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub a <a %(a_paused)s>metou en pause</a> l' tèlèchargement des noveas papîs."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB est on continou di Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accès direct a %(count)s papîs academikes"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Aber"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Si vos estoz <a %(a_member)s>membre</a>, verification del naviguêre n' est nén dmandêye."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archive a long tèrme"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Les datasets eployîs dins l' Archive d' Anna sont totafait ouverts, et pôront esse mirés en gros avou des torrents. <a %(a_datasets)s>En savou més…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Vos pôroz aidî grandemint en semant des torrents. <a %(a_torrents)s>En savou més…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s semeûs"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s semeûs"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s semeûs"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Données d' apprentissaedje LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Nos avans l' pus grand colèccion do monde di données di tecse di hôte qualité. <a %(a_llm)s>En savou més…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Miroirs: apel a volontaires"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 On cwèrt des volontaires"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Come on projet a nén-lucratif, open-source, nos cwèrans todi des djins po aidî."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Si vos gerdîz on processeu di payement anonyme a hôte risk, plait-îz nos contakter. Nos cwèrans ossi des djins po metter des p'tites anonces avou bon gou. Tos les bénèfices vont a nos efforts di préservation."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog d' Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Tèlèchargemints IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Tos les lîens di tèlèchargement po cist fitchî: <a %(a_main)s>Pâge principale do fitchî</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "Passadje IPFS #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(vos dvoz essayer sacwants côps avou IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Po fåre des tchatches pus vite ey skaper les controles do navigatore, <a %(a_membership)s>devenez membre</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Po mirer ene grosse part di nosse coleccion, voeyz les pådjes <a %(a_datasets)s>Datasets</a> ey <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Données LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "I gn' est bén conneu qu' les LLMs s' amuzèt bén avou des données di hôte qualité. Nos avans l' pus grandès coleccion di livres, papîs, magazinnes, etc. al monde, ki sont dès sôces di tecses di hôte qualité."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Escale et gamme uniqe"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Noste coleccion contint pus di cinsant millions di fitchîs, incllant dès djournals academikes, dès manuels, et dès magazinnes. Nos rivans a cisse escale la en combinant dès grands repôsitwaires ki egzistèt ddja."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Quaukes-unes di nosse sôces di coleccion sont ddja dispolnibles en gros (Sci-Hub, et dès ptites ptches di Libgen). D' ôtès sôces nos-ôtes les avans libèrêyes. <a %(a_datasets)s>Datasets</a> montre on coplet resumê."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Noste coleccion inclut dès millions di livres, papîs, et magazinnes d' avå l' èyê d' e-books. Grands ptches di cisse coleccion ont ddja stî OCRîyes, et ont ddja pô d' doublons."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Come nos pôrans aidî"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Nos-ôtes nos pôrans fornir on accès a hôte velocité a nosse coleccions copletes, mins ossi a dès coleccions nén stîsîyes."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "C' est on accès a l' nivêye d' entreprise ki nos pôrans fornir po dès dinnêyes dins l' ranteye di dès dizinnes di milliers d' USD. Nos-ôtes nos volans ossi échandjî cisse accès la po dès coleccions di hôte qualité ki nos n' avans nén co."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Nos pôrans vos rinde vosse plait si vos-ôtes pôrîz nos fornir avou dès enrichissemints di nosse données, come:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Oister les doublons (dédoublonnache)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Èxtraction di tecses et metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Sopoirtî l' archivaedje a long tèrme di l' connoissance umane, tot en avou des meilleures données po vosse modèle!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contacter nos-ôtes</a> po discutaer come nos pôrans cwoperer."

#, fuzzy
msgid "page.login.continue"
msgstr "Continouwer"

#, fuzzy
msgid "page.login.please"
msgstr "Voloz bén <a %(a_account)s>vos lôguer</a> po vey solle pådje.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "L'Archive d'Anna est temporairmint hors service po des entretiens. Riwindez dins ene eure."

#, fuzzy
msgid "page.metadata.header"
msgstr "Améliorer les métadonnées"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Vos pouvez aidî a préserver les livres en améliorant les métadonnées! D'abord, lîz l'fond d'cène des métadonnées su l'Archive d'Anna, et puis aprindez come améliorer les métadonnées en les liant avou l'Open Library, et gagnez ene mimbreût gratis su l'Archive d'Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Fond d'cène"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Quand vos rguindez on livre su l'Archive d'Anna, vos pouvez vey des tchamps divèrs: titro, ôteur, éditeu, édition, année, description, nom d'fi, et d'ôtès. Tos ces pîces d'informåcion sont apelêyes <em>métadonnées</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Come nos rassemblans des livres divins des <em>sourcès di bibliotèques</em> divèrsses, nos montrans les métadonnées ki sont dispolnibles dins cisse sourcè. Par èjhemp, po on livre k' nos avans d'la Library Genesis, nos montrans l'titro di l'basede donêyes di Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "D'fos, on livre est présent dins <em>plusors</em> sourcès di bibliotèques, k' pôront aveur des tchamps di métadonnées divèrsses. Dins cisse cas, nos montrans tot simplay l'vèrsion l'plu longuèye di chascon tchamps, pask' cisse la contint, nos l'espérons, l'informåcion l'plu utile! Nos montrans tot d'même les ôtès tchamps en dzo l'description, come ”titro alternatif” (mès nén k' s'ils sont divèrts)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Nos extrayans ossi des <em>codes</em> come des identifiants et des classifiants dès sourcès di bibliotèques. <em>Les identifiants</em> représentent uniqment ene édition particulêre d'on livre; des èjhemples sont ISBN, DOI, Open Library ID, Google Books ID, ou Amazon ID. <em>Les classifiants</em> rassemblèt des livres simillès; des èjhemples sont Dewey Decimal (DCC), UDC, LCC, RVK, ou GOST. D'fos, ces codes sont lîyés explicitemint dins les sourcès di bibliotèques, et d'fos nos pôvans les extraire du nom d'fi ou d'la description (principalemint ISBN et DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Nos pôvans eployî les identifiants po trover des records dins des <em>collections di métadonnées</em>, come OpenLibrary, ISBNdb, ou WorldCat/OCLC. I gn a on <em>tchampe di métadonnées</em> spécifike dins nos moteur di rechèrche si vos voloz rguindî ces collections. Nos eployans les records correspondants po rimplî les tchamps di métadonnées mankants (p.ex. si on titro manke), ou p.ex. come “titro alternatif” (s'il y a on titro existant)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Po veyî tot just d'où viennent les métadonnées d'on livre, veyîz l' <em>“Détails tècniques” tab</em> su l'page d'on livre. I gn a on lîen vèrs l'JSON brut po cisse livre, avou des pointeurs vèrs l'JSON brut des records originaux."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Po pus d'informåcion, veyîz les pagnes ci-après: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Rechèrche (tchampe di métadonnées)</a>, <a %(a_codes)s>Explorateur di codes</a>, et <a %(a_example)s>Èjhemple di JSON di métadonnées</a>. Finalemint, totes nos métadonnées pôvèt esse <a %(a_generated)s>générées</a> ou <a %(a_downloaded)s>téléchargées</a> come bases di donêyes ElasticSearch et MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Liaison avou l'Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Donc, si vos rencotrez on fi avou des mauvaises métadonnées, come vos dvoz-i l'fixer? Vos pôvans aler a l'bibliotèque sourcè et siuvre ses procèdures po fixer les métadonnées, mès k' fere si on fi est présent dins plusors bibliotèques sourcès?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "I gn a on identifiant k' est traté d'façon spécifike su l'Archive d'Anna. <strong>L' champ annas_archive md5 su l'Open Library surpasse toudi totes les ôtès métadonnées!</strong> R'culons on ptit pou et aprindans a propôs d'Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library a stî fondé en 2006 pa Aaron Swartz avou l'but di “ene page web po chascon livre k' a stî publyî”. C'est on ptit come Wikipedia po les métadonnées di livres: tot l'monde pôrèt l'édjî, c'est licencî librimint, et pôrèt esse téléchargé en vrac. C'est ene base di donêyes di livres k' est l'plu alignêye avou nos mission — en fait, l'Archive d'Anna a stî inspirêye pa l'vision et l'vie d'Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Au lieu di réinventer l'roue, nos avans décidî di rediriger nos volontaires vèrs l'Open Library. Si vos veyîz on livre avou des métadonnées incorrectes, vos pôvans aidî d'la façon ci-après:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Allez su l' <a %(a_openlib)s>site web d'Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Trovez l'record correct du livre. <strong>ATTINÇON:</strong> soyes sûrs di tchuzî l' <strong>édition</strong> correcte. Dins l'Open Library, vos avans des “oeuvres” et des “éditions”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "On “oeuvre” pôrèt esse “Harry Potter and the Philosopher's Stone”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "On “édition” pôrèt esse:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Li prumîre édition di 1997 publiceye pa Bloomsbery avou 256 pådjes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Li édition di 2003 en papî publiceye pa Raincoast Books avou 223 pådjes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Li traduction polone di 2000 “Harry Potter I Kamie Filozoficzn” pa Media Rodzina avou 328 pådjes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Totes cès éditions ont des ISBNs et des contnus differins, donk soeye sûr di tchôzi l’bonne!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Editez li ripoirt (ou creyez-l’ s’ i n’ egzist nén), et apondixhîz l’ pus d’ informåcions ki vos pôroz! Vos estoz la asteure, autans fwait on ripoirt tot magnîfique."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Dzo “Nûméro d’ ID” tchoezixhîz “Anna’s Archive” et apondixhîz l’ MD5 do live di Anna’s Archive. C’ est l’ longue tchène di letes et di nûméro après “/md5/” dins l’ URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Sayîz d’ trover des ôtes fitchîs dins Anna’s Archive ki matchèt ossi cisse ripoirt, et apondixhîz-oz ossi. Ås-îs, nos pôrons rassember cixhins come doublons sol pådje di rechèrche di Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Quand vos avoz fini, scrijhoz l’ URL ki vos avoz just mete a djoû. Onk vos avoz mete a djoû asto 30 ripoirts avou des MD5s di Anna’s Archive, evoyîz-nos on <a %(a_contact)s>email</a> et evoyîz-nos l’ lisse. Nos vos dnerons on mimbreût gratis po Anna’s Archive, po ki vos pôroz fere cisse ovraedje pus aîzîment (et come on grand mèrci po voste aidance). Cixhins dètchîz esse des edicions di hôte qualité ki apondèt des grandès quantités d’ informåcions, ôtrumint voste dimande serè refusêye. Voste dimande serè ossi refusêye si onk des edicions sont r’vokêyes ou coridjêyes pa les modératours di l’ Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Notîz ki cisse manîre la fonce k’ po des lives, nén po des papîs academikes ou d’ ôtes sôres di fitchîs. Po d’ ôtes sôres di fitchîs, nos r’comandans todi di trover l’ biblioteke d’ ôrigyne. Ça pôrèt prinde kékes samwinne po ki les candjmints sont a djoû dins Anna’s Archive, pask’ nos d’vons d’vins d’ télécharger l’ dèrnî dump di l’ Open Library, et r’generer no r’cèrche index."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Miroirs: apel a volontaires"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Po amélioyer l' résilience di l' Archive d' Anna, nos-ôtes nos cerchans dès volontaires po fêre tourner dès miroirs."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Nos cwèrmans cisse:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Vos rouwinez li code source d'Anna’s Archive, et vos meteîz a djoû régulîrement tant li code ki les dnêyes."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Vôtre vèrsion est clèrminte distinguêye come on mirroer, p.ex. “L'Archive di Bob, on mirroer di l'Archive d'Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Vos estoz d' acwèrd di prinde les riskes ki vont avou cisse ovraedje, ki sont sinsîfes. Vos avoz ene comprinde profonde di l' sécuressance operåcionale ki s' dimande. Li contnou di <a %(a_shadow)s>cistès</a> <a %(a_pirate)s>pôstes</a> vos sont evîdints."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Vos estoz d'acwèrd di contribuwer a no <a %(a_codebase)s>code source</a> — en colåborant avou no team — po fé çoula."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "A l'cominmint, nos vos dnerans nén l'acwèrd d' accèder a les teledjårdjes di no server di patners, mins si çoula va bén, nos polrons les partadjer avou vos."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Frais d'hôsté"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Nos estans d'acwèrd di payî les costes di l'hôsté et VPN, a l'cominmint divins $200 par mês. Çoula est sufîsant po on server di rechèrche basike et on proxy protejî DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Nos payrons l'hôtêye ki èn fwa k' vos avoz tot metou en plaece, et k' vos avoz dmontré k' vos estoz capåbe di mete l'archive a djoû avou des djoûs. Çoula vout dire k' vos dvoz payî les 1-2 prumîs mois di vôs poche."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Vô timps n'sérè nén payî (et l'noûtrès nén pus), pask' c' est onkî do volontariat."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si vos s'impliquez fortimint dins l'developmint et les operåcions di no travå, nos polrons disctchî di partadjer pus di r'cettes di d'nonåcions avou vos, po vos d'ployî come n'cessaire."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Comincî"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "S' i vos plait <strong>nén nos contakter</strong> po dimander l'permission, ou po des cwistions basikes. Les actions parlent pus hôt ki les mots! Tote l'informåcion est la, alor va-z-y avou l'mise en plèce di vos mirroir."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "N' hésite nén di poster des tickets ou des dmindes di fusion a no Gitlab quand vos rencotrez des problinnes. Nos avrons p't-êt' d'besoin di fé des fonctiionalités spècifiques po vos mirroirs, come r'brander di “Anna’s Archive” a l' no d' vos site, (a l'cominmint) disåbler les contes d' uzeu, ou lier a no site principal dès les pådjes di livres."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Onk vos mirroir est en fonctiion, s' i vos plait, contaktez nos. Nos aimerions r'viyî vos OpSec, et onk çoula est solide, nos lierons a vos mirroir, et nos cmincerons a travayer pus prôche avou vos."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Mierci d' avåce a tertos ki vout r'contribuer d' cisse manîre! Çoula n' est nén po les mols d'cuer, mins çoula solidifierait l'longévité di l' pus grand librêye vraimint ouverte dins l' istwere di l' umanité."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Tchatcher a partir d' on sit dazî"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Les tchatches lentes n' sont k' dispolnibles sol sit ofitchiel. Vizitez %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Les tchatches lentes n' sont nén dispolnibles avou les VPNs di Cloudflare ou d' ôtresses adresses IP di Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Volîz bin atindez <span %(span_countdown)s>%(wait_seconds)s</span> secondes po djicharger cisse fitchî."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Eployîz l' URL ci-après po tchèter: <a %(a_download)s>Tchatcher asteure</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Mierci d' atinde, çoula permet d' rinde l' sit dispolnible gratis po tertos! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Avertixhmint: i gn a stî on grand nombre di tchatches a partir di vosse adresse IP dins les 24 dierinès eures. Les tchatches polèt esse pus lentes ki d' costé."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Tchatches a partir di vosse adresse IP dins les 24 dierinès eures: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si vos eployîz on VPN, ene conneccion internet partadje, ou si vosse ISP partadje les IPs, ç' avertixhmint pout esse dû a çoula."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Po dner a turtchîhene ene chance di djicharger des fitchîs po nén-gnon, vos dvoz atindez divant di pôre djicharger cisse fitchî."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Vos poloz continouwer a rwinner dins ene ôte tabe di l' Archive d'Anna tot en atindant (si vosse navigatore siporte les tabeus ki s' rafraîchixhèt a l' fond)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Vos poloz atinde ki des pådjes di tchatches se tchargèt a l' minme tins (mès voloz bén tchèter on fitchî a l' minme tins par serveu)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Onk vos avoz on lîen di tchatche, il est valide po sacwants eures."

#, fuzzy
msgid "layout.index.header.title"
msgstr "L'Archive d'Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rècord dins l'Archive d'Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Tèlèchargî"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Po sostni l'accessibilté et l'presèrvåcion a long tèrme do sçavoir umain, dvins on <a %(a_donate)s>membre</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Come boni, 🧬&nbsp;SciDB s'charge pus vite po les membres, sins no limites."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Ça n' va nén? Sayîz <a %(a_refresh)s>d' rfrèchir</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Nole prévisualisåcion n' est dji ava. Tèlèchargez l' fitchî d' <a %(a_path)s>l'Archive d'Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB est on continou di Sci-Hub, avou s'interface bin connou et l'vijhaedje dirèct des PDFs. Intrer vos DOI po vey."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Nos avons l'coleccion complète di Sci-Hub, tot come des noveas papîs. L'pus grand part polèt esse veyou dirèctemint avou on' interface connou, come Sci-Hub. D' ôtes polèt esse tèlèchargîs via des sôres extèrnes, et dins c' cas ci nos montrons des lîens vers cès sôres."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Cweri"

#, fuzzy
msgid "page.search.title.new"
msgstr "Novea cwerêye"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Inclure seulmint"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Exclure"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nén tchèkî"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Tèlèchargî"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Artikes di djournå"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Emprunt Digitå"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Mètadoneyes"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titro, ôteur, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Cweri"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Paramètres de recherche"

#, fuzzy
msgid "page.search.submit"
msgstr "Rechercher"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "La recherche a pris trop de temps, ce qui est courant pour les requêtes larges. Les comptes de filtre peuvent ne pas être précis."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "La recherche a pris trop de temps, ce qui signifie que vous pourriez voir des résultats inexacts. Parfois, <a %(a_reload)s>recharger</a> la page aide."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Mostrer"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Djivêye"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tåvele"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avancé"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Cweri descripcions et comentêyes di mètadoneyes"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Ajouter un champ de recherche spécifique"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(recherche de champ spécifique)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Anêye di pus"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Contenu"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Type de fichier"

#, fuzzy
msgid "page.search.more"
msgstr "plu d’…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Accès"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Source"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "raclotî et ouver a l’ sourdant pa AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Langue"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Trier par"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Li pus relévant"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Le plus récent"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(année de publication)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Le plus ancien"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Le plus grand"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(taille du fichier)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Le plus petit"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(source ouverte)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Hasard"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "L’ endèxe di rechèrche est mete a djoû tchaeke moes. Il inclut d’ gnolêyes d’ djusque %(last_data_refresh_date)s. Po des infôrmåcions tecnikes, veyîz l’ pådje des %(link_open_tag)sdatasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Po explorer l’ index di r’cèrche pa codes, eployîz l’ <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Tapez dins l’ boesse po cwèri dins no catalogu di %(count)s fitchîs tèlèdjiheyes dirèctemint, k’ nos ôtans <a %(a_preserve)s>po todi</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "D’ fait, tot l’ monde pout aidî a ôter cistès fitchîs en semant no <a %(a_torrents)s>listêye unifeye di torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Nos avans asteure li pus grand catalogu ouver do monde di livres, papîs, et ôtes ovres scrites. Nos mirons Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>et d’ ôtes</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Si vos trovez d’ ôtes “bibiotèques d’ omes” k’ nos dvrîons mirer, ou bin si vos avoz des cwistions, plait contaktez nos a %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Po les réclames DMCA / d’ droets d’ ôteur <a %(a_copyright)s>clitchîz ci</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: eployîz les racourcis di claviér “/” (focus di rechèrche), “enter” (rechèrche), “j” (hôt), “k” (bas), “<” (pådje précedinte), “>” (pådje siwante) po ene navigåcion pus rapîde."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Vos cwèroz des papîs?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Tapez dins l’ boesse po cwèri dins no catalogu di %(count)s papîs academikes et artikes di djournåls, k’ nos ôtans <a %(a_preserve)s>po todi</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Tapez dins l’ boesse po cwèri des fitchîs dins les bibiotèques di prèt numerike."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Cist endèxe di rechèrche inclut asteure des metadonnées di l’ bibiotèque di prèt numerike di l’ Internet Archive. <a %(a_datasets)s>D’ pus a propôs di nos datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Po des bibiotèques di prèt numerike, veyîz <a %(a_wikipedia)s>Wikipedia</a> et l’ <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Tapez dins l’ boesse po cwèri des metadonnées di bibiotèques. Cist pout esse utile cand vos <a %(a_request)s>dimandez on fitchî</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Cist endèxe di rechèrche inclut asteure des metadonnées di divins sourdants di metadonnées. <a %(a_datasets)s>D’ pus a propôs di nos datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Po les metadonnées, nos montrons les gnolêyes originåles. Nos n’ feyans nén d’ fusions di gnolêyes."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Il y a de nombreuses sources de métadonnées pour les œuvres écrites à travers le monde. <a %(a_wikipedia)s>Cette page Wikipédia</a> est un bon début, mais si vous connaissez d'autres bonnes listes, veuillez nous en informer."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Tapez dans la boîte pour rechercher."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "C' est des records di metadata, <span %(classname)s>nén</span> des fitchîs djihaedjes."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Erreur lors de la recherche."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Essayez de <a %(a_reload)s>recharger la page</a>. Si le problème persiste, veuillez nous envoyer un e-mail à %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Aucun fichier trouvé.</span> Essayez avec moins de termes de recherche ou des filtres différents."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Ça riv' a-st-insi candjî cand l' server di rechèrche est linde. Dins ces cas-la, <a %(a_attrs)s>rtcharget</a> pout aidî."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Nous avons trouvé des correspondances dans : %(in)s. Vous pouvez vous référer à l'URL trouvée là-bas lorsque vous <a %(a_request)s>demandez un fichier</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Articles de journaux (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Prêt numérique (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Métadonnées (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Résultats %(from)s-%(to)s (%(total)s au total)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ correspondances partielles"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d matchès parciès"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Volontariat & Prîmes"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "L' Archive d' Anna s' baste so des volontaires come vos. Nos r'cwèroz totes les nivêyes d' angadjî, et nos avoz deus categoreyes principåles d' aidance ki nos cwèroz:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Légîre volontariat:</span> si vos n' sôrz k' a fewes eures di ci et la, i gn a todi sacwants manires d' aidî. Nos r'compinsans les volontaires continous avou des <span %(bold)s>🤝 membreships a l' Archive d' Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Tchåjhe di volontariat (bounties di USD$50-USD$5,000):</span> si vossez capåve di dedier on grand djoû et/ou des rissources a nosse mission, nos-åmerions d' cwiter pus d' près avou vos. A l' long, vos pouroûz rinte dins l' équipe interne. Mins nos-aveons on budget serré, nos-èsse capåves di rinde <span %(bold)s>💰 des bounties monetaires</span> po l' pus grand tchåjhe."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si vossez nén capåve di volontariser vosse tins, vos pouroûz totavå mintche aidî nosse grandmint en <a %(a_donate)s>donnant des sôus</a>, <a %(a_torrents)s>seedeant nosse torrents</a>, <a %(a_uploading)s>tcherdant des livres</a>, ou <a %(a_help)s>dîzant a vosse amis d' Anna’s Archive</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Compagnies:</span> nos-èsse offrans ene accèss direct a hôte vitesse a nosse colècs en èchange d' on don d' entreprise ou en èchange di noveas colècs (p.ex. noveas scans, datasets OCRisés, enrechis nosse dnêyes). <a %(a_contact)s>Contactez nos</a> si c' est vos. Veyoz ossi nosse <a %(a_llm)s>pådje LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Léjhe volontariat"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si vossez k' åyîz kékes heûres di trop, vos pouroûz aidî d' on bon lombe di manières. Assurez vos di rinte dins l' <a %(a_telegram)s>tchat des volontaires so Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Come token d' apressiation, nos djonne d' habitude 6 mois di “Bibliotèkère Bénî” po des p'tits milestones, et pus po continou volontariat. Tos les milestones d'mandèt des traveas d' hôte qualité — des traveas bâclés nos fwait pus d' må que d' bén et nos-è rejèterans. S' il vos plait, <a %(a_contact)s>envoyez nos on e-mail</a> quand vos-aveoz ritchî on milestone."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tåche"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Milestone"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Répandant l'parôle di l'Årchiye d'Anna. Par èjhemple, en r'comandant des livres so AA, en liant nos postes di blog, ou d'maneire gènèrale en dirigant les djins vèrs nosse site."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s lîens ou bin captures d' ecran."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Cis-i dût vos monstrer ki vos dîd a k'kèqu'un a propôs di l'Årchiye d'Anna, et k'î vos r'merci."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Améliorer les metadata en <a %(a_metadata)s>liant</a> avou Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Vos poloz eployî l' <a %(a_list)s >liste des problins di metadata a l' hasârd</a> come on point di départ."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assurez-vos d' lîre on comentêye sol les problins k' vos rèsolvîz, po k' les ôtes n' fassèt nén l' mêm travea."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s lîens di records k' vos a ameyorés."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traduire</a> l' sitweb."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traduire on lingaedje totafwait (si c' n' esteut nén djà près d' l' fin)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Améliorer l' pådje Wikipedia d' Anna’s Archive dins vosse lingaedje. Inclure des informåcions di l' pådje Wikipedia d' AA dins des ôtes lingaedjes, et di nosse sitweb et blog. Radjouter des référinces a AA so d' ôtes pådjes pertinintes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Lîen di l' istwere d' édicion montrant k' vos-aveoz fwait des contributions significatives."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Rimplir des dmindes di livres (ou papîs, etc) so les forums di Z-Library ou di Library Genesis. Nos n' aveons nén nosse propre sistinme di dminde di livres, mins nos mirons ces bibliotèques, don rinde les miyeus fwait ossi Anna’s Archive miyeur."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s lîens ou bin captures d' ecran des dimandes k' vos a rimplîyes."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Petites tåches postées so nosse <a %(a_telegram)s>tchat des volontaires so Telegram</a>. D' habitude po des memberships, dès foys po des p'tits bounties."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Petites tâches postêyes dins nosse groupe di tchatches di volontaires."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dépend di l' tåche."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Primes"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Nos-oz adon k' nosseus avou des djins avou des bonès compétences en programmation ou en sécurité offensive po s'involer. Vos pouvez fé on grand trou dins l'préservation do l'héritådje d'l'humanité."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Po vos rimerciyî, nos donans des membresyes po des contributions solides. Po vos rimerciyî grandement, nos donans des primes monetaires po des taches particulièrement importantes et difficiles. Çoula n'dout nén esse veyou come on remplacement d'on metî, mins c'est ene incitåcion d'pus et çoula pout aidî avou les costes incurrus."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "L'pus grand part di nosse code est open source, et nos vos dmandrans d'fé l'minme avou vosse code quand vos r'cevoz l'prime. I gn a des exceptions k' nos pouvans discuter a l'cas par cas."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Les primes sont r'cevoz pa l'prumî k' acomplît ene tache. N'hésitîz nén a comentî on ticket di prime po fé saveur a l'ôtes k' vos estoz d'jà dsu, po k' les ôtes pôront s'ralindjî ou vos contakter po s'associer. Mins soyiz conscient k' les ôtes sont todi libes di tchoezi dsu et d' essayer di vos bouter. Mins, nos n'donans nén des primes po on trawet bâclé. Si deus submissions di haute qualité sont fêtès proche l'une di l'ôte (dins on djoû ou deus), nos pouvans tchoezi d' rimerciyî les deus, a nosse discrétion, come 100%% po l'prumî submission et 50%% po l'deusse (don 150%% totå)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Po les grosses primes (surtout les primes di scraping), vos dvoz nos contakter quand vos acomplîz ~5%% d'la tache, et vos estoz sûr k' vosse méthode s'ra bon po l'milestone complet. Vos dvoz partagî vosse méthode avou nos po k' nos pouvans vos dner des avis. Ossu, çoula nos permet d'decider k'fé s'i gn a plusieurs djins k' s'raprochent d'ene prime, come possiblement l'donner a plusieurs djins, encourager les djins a s'associer, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVERTISSEMENT: les taches a haute prime sont <span %(bold)s>difficiles</span> — ç' pourrait esse sage di cminçer avou des pus faciles."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Allez a nosse <a %(a_gitlab)s>liste di problèmes Gitlab</a> et triyîz pa “Label priority”. Çoula montre grosso modo l'ordre des taches k' nos importans. Les taches sans primes explicites sont todi éligibles po des membresyes, surtout celles marquées “Accepted” et “Anna’s favorite”. Vos voudrîz ptêt cminçer avou on “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Mises a djoû so <a %(wikipedia_annas_archive)s>l' Archive d' Anna</a>, li pus grand librêye vraimint ouverete dins l' istwere di l' umanité."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "La plus grande bibliothèque open-source et open-data du monde. Miroirs de Sci-Hub, Library Genesis, Z-Library, et plus encore."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Rechercher dans les Archives d’Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Les Archives d’Anna ont besoin de votre aide !"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Beaucoup essaient de nous faire tomber, mais nous ripostons."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si vos donez asteure, vos rçevroz <strong>doubler</strong> l' nombre di tèlèchargemints rapîds."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valåbe divins l' fin d' cisse mois."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Faire un don"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Sauvegarder le savoir humain : un excellent cadeau de fête !"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Surprenez un être cher, offrez-lui un compte avec adhésion."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Pour augmenter la résilience des Archives d’Anna, nous recherchons des volontaires pour gérer des miroirs."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Li bon kado di Sint-Vincent!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Nos avons ene nove metod di donasion disponib: %(method_name)s. S'il vous plaît considerer %(donate_link_open_tag)sdonner</a> — pa fer marse sa sit-la pa bon marse, ek ou donasion fer enn vre diferans. Mersi boukou."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Nou pe fer enn kolekt pou <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">soutenir</a> pli gran bibliotèk lonbraz komik dan lemond. Mersi pou ou sipor! <a href=\"/donate\">Donner.</a> Si ou pa kapav donner, considerer soutenir nou par dir ou bann kamarad, ek swiv nou lor <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Telechargement resan:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Rechercher"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Ameliorer metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Volontariat & Prime"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activité"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorater Codes"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Donné LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Akèy"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Software Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Tradwir ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Konekte / Enregistrer"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Kont"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Archive Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Reste an kontak"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / revandikasyon copyright"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avancé"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Securité"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatives"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nén affilî"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Ce fichier pourrait avoir des problèmes."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Téléchargement rapide"

#, fuzzy
msgid "page.donate.copy"
msgstr "copier"

#, fuzzy
msgid "page.donate.copied"
msgstr "copié !"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Précédent"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Suivant"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "nén k' cisse mês!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub a <a %(a_closed)s>sospindu</a> l'upload di noveas papîs."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Sélectionnez une option de paiement. Nous offrons des réductions pour les paiements en crypto-monnaie %(bitcoin_icon)s, car nous avons (beaucoup) moins de frais."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Sélectionnez une option de paiement. Nous n'avons actuellement que des paiements en crypto-monnaie %(bitcoin_icon)s, car les processeurs de paiement traditionnels refusent de travailler avec nous."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nos n' poumons nén siporter les cartes di crédit/débit direktemint, paskè les banques n' veynt nén tchover avou nos. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "D' tôte manîre, i gn a sacwants môyéns d' eployî des cartes di crèdite/dèbete, eployant nos ôtès môyéns di payement:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Tèlèchargemints lints & èstèrnès"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Tèlèchargemints"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si c' est l' prumîre fwa k' vos eployîz des cryptomonnèyes, nos vos consèyans d' eployî %(option1)s, %(option2)s, ou %(option3)s po achte et doner des Bitcoins (l' prumîre et l' pus eployeye des cryptomonnèyes)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 lîens di records k' vos-aveoz améliorés."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 lîens ou captures d' écran."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 lîens ou captures d' écran di dmindes k' vos-aveoz rimplies."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si vous êtes intéressé par le mirroring de ces datasets pour des <a %(a_faq)s>fins d'archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si vos estoz intrêssîs di mirer cist set di dnêyes po <a %(a_archival)s>l’ archivaedje</a> ou <a %(a_llm)s>l’ eployaedje LLM</a>, volêz nos contakter."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sît principâl"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Dnêyes di payis ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si vos estoz intrêssé a mirrer cisse måye di dnêyes po des <a %(a_archival)s>buts d' archivaedje</a> ou <a %(a_llm)s>d' èprindijhe LLM</a>, voloz bén nos contakter."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L' Agince Internåcionale ISBN rèlache régulîrmint les renges qu' ele a allocåyîs a des aginces nacionåles ISBN. D' ça, nos poumons dèrivé a k' payis, région, ou groupe di lingaedje cisse ISBN-apartint. Nos uzons cisse dnêye a l' heure d' asteure endîrèctemint, avou l' bibiotêke Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Rissources"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Dèrniére mize a djoû: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Pådje-web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Mètadoneyes"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Hors “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Notre inspiration pour collecter des métadonnées est l'objectif d'Aaron Swartz de “une page web pour chaque livre jamais publié”, pour lequel il a créé <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ce projet a bien réussi, mais notre position unique nous permet d'obtenir des métadonnées qu'ils ne peuvent pas."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Une autre inspiration était notre désir de savoir <a %(a_blog)s>combien de livres il y a dans le monde</a>, afin que nous puissions calculer combien de livres il nous reste à sauver."

#~ msgid "page.partner_download.text1"
#~ msgstr "Po donner a tertos ene chance di tchèter des fitchîs gratis, vos dvoz atinde <strong>%(wait_seconds)s seconds</strong> divant di poter tchèter ç' fitchî."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Rinfressî l' pådje otomaticmint. Si vos ratîz l' féniesse di djihe, li timer r'comincera, don l' rinfressidje otomatic est råcomandé."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Tchatcher asteure"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Convertixh: eployîz des usteyes en linne po convertixh des formats. Pår èjhemp, po convertixh d' epub a pdf, eployîz <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: tèlèchargez l' fitchîr (pdf ou epub sont sopoirtés), et pi <a %(a_kindle)s>evoyîz-l' a Kindle</a> eployant l' web, l' app ou l' emile. Usteyes utiles: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Soutnîz les ôteurs: Si çoula vos plait et qu' vos l' pôde, pensez a asteur l' originål, ou a soutnîr les ôteurs dirèctemint."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Soutnîz les bibiotèques: Si çoula est dîspô a vôtre bibiotèque locåle, pensez a l' prinde a prèt gråtchîmint la."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nén disponibe direcctement en gros, mins en semi-gros d’ l’ ôte costé d’ on payant"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s L’ Archîve d’ Anna gère on colècte di <a %(isbndb)s>mètadonnées ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb est ene companeye k' scrape divinsès librêyes en linne po trover des metadata ISBN. L' Archive d' Anna a fwait des backups des metadata des liveys ISBNdb. Cist metadata est dispolonle a travèrs l' Archive d' Anna (mès nén asteure dins les rechertes, s' no n' rechertche nén on numere d' ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Po des detays tecnikes, veyoz çoula dzo. A on momen, nos poumons l' eployî po determiner k' liveys manquent co dins les bibiotêkes d' ombre, po prioritizer k' liveys trover et/ou scanner."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Noste blogu a propôs d' cisse dôneye"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb scrape"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actuellement, nos avans on seul torrent, qu' contient on fichî 4,4GB gzipped <a %(a_jsonl)s>JSON Lines</a> (20GB décompressé): “isbndb_2022_09.jsonl.gz”. Po importer on fichî “.jsonl” dins PostgreSQL, vos pôréz utiliser quèque-chose come <a %(a_script)s>cisse script</a>. Vos pôréz mêm l' pipeter directement avou quèque-chose come %(example_code)s donc il se décompresse en vol."

#~ msgid "page.donate.wait"
#~ msgstr "Soyeis pacient et atindez a mwins <span %(span_hours)s>dès heures</span> (et rfrêchîz cisse pådje) divant d' nos contakter."

#~ msgid "page.codes.search_archive"
#~ msgstr "Tchérchi dins l'Archive d'Anna po “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Doner avou Alipay ou WeChat. Vos pouroz tchoezi dins l' pådje ki vén."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Répandre l' mot d' Anna’s Archive so les médias sociaux et les forums en linne, en r'commandant des livres ou listes so AA, ou en répondant a des questions."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s L'coleccion di Fiction a diverjî, mins a todi des <a %(libgenli)s>torints</a>, mès nén mete a djoû d'pus 2022 (nos avans des tèlèchargemints direcs)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s L’ Archîve d’ Anna et Libgen.li gèrèt des colèctes di <a %(comics)s>bandes dessinêyes</a> et <a %(magazines)s>magazines</a> en collaborant."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nén di torrents po les colections di fictif rouske et documents standards."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Il n' y a nén de torrents disponibles po les contenus supplémentaires. Les torrents qu' sont so l' site Libgen.li sont des mirroirs d' ôtes torrents listés ici. L' seule exception est les torrents de fiction qu' commencent a %(fiction_starting_point)s. Les torrents de comics et de magazines sont rilêyés come onne collaboration entre l' Archive d' Anna et Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "D'ine coleccion <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> originne egzacte nén clêre. D'part di the-eye.eu, d'part d'ôtes sôces."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

