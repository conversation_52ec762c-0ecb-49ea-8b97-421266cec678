��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b /  ld S  �f )   �g .  h   Ii �  Ok �  *m   �n S   �o r   0p C   �p i   �p �  Qq S  0s �   �t �  vu �   [w   Px �  nz   =| �   R~ �  1 �  � 7   {� �   �� N   ��   у    م :  �� C   2� !   v�    ��    �� G   ̈ $   �    9� 5   K� *   �� -   ��    ډ @   � <  0�   m� J   �� I   ֌ 
    �    .�    =�    X�    g�    �� 	   ��    ��    �� !   ��    ۍ "   � 	   � 
   �    �    (� #   C�    g� Q  �� g  Ԑ �   <� )   ڒ K  � �   P� �   � �   ��     �� �   ��     K� �   l� %   �   @�     V� �  w� 4   �� O   0� 	   �� 9  �� h  Ĝ �  -� F  �    9� Y   I� _   �� �   � 2   �� 6   � g   � H   �� M   ͣ +   �    G� x   K� �  Ĥ P   �� �   ئ �   _� ;   � :  O� ,   �� -  �� �   � �   k� _   � �   x� �   :� C   ѭ �   � �   � `   ¯ L   #� Z   p�    ˰ �   ܰ   �� C   ��    β �   ղ �   x�    g� �  n� �   �   �� �  �� �   (� `  � �   w� K   b� M   �� [   �� w   X� u   п _   F� .   �� �   �� y   ~� K   ��    D� s   W� M   �� �   � �   ��    x�    �� !  �� [  �� A  � �   I� �   �� ?  �� �   �� �   �� �   `� h  �� �   g� �    �     � �   	� �   �� �   }� �  :� �   �� �   L� �   �� �  l� �   L� >  �� !  �    1� X   P� �   �� O   A� b  �� �   �� v   �� �   !� "   �� ]  �� �   $� h  �    j� V   �� �   �� �   }� �   b� �   b� �  ?� �  �� )   �� o  �� ~  $�    �� �  �� /  ^�   �� _   �� �   �    �� �   ��   ]� ^  q� �   �� �   �� �   ��    O� e  \� V  �� �     
    �    G  � �   	    � �   � [   �    	 �   ]  �    
 �   "
 (   3  8 �   l �  P Y   � Q   , '  ~ 6   �    � R  � 0  @ 6  q >   � C   �   + �   E �   � i   w �  �     �    � �  � �   S �    \   � �   
  +   �  5  �    "    #    &# $   2# 5   W# ?   �# G   �# P   $ p   f$    �$ .   �$ H   % M   f% &   �% =   �% 0   & x   J&    �& $   �&   �& -  ( �   <) �   * �   + �   �+   �,    �- �   �- �   �. �   +/ O  �/ {  1 �  �2   �4 �  �6 C   (8 <   l8 '   �8 �   �8 )  �9 �  �; 3  6= k  j>   �?   �@   C '   D   =D 0  UE k  �F    �G �   H ?  �H |   2J ?  �J �   �K    �L (   �L q   �L 
   aM �  lM �  =P z  �Q �   dT �   :U    )V   9V   @W C   PX q   �X )  Y   0Z �   D[ p  �[ c   S] '  �]    �^   �^ �    a �  �a �  �c �  (f �   �g    �h �   �h �  li    Ek n  Xk �  �l V  �n   q �   !s B  t C   Xu �  �u �   �w �   _x V   �x f   7y    �y 9   �y $   �y (   z '   /z    Wz �   pz �  {   �}   � �   	�    � �  
� 3  �� M  � h  A� b  �� Z  
�    h� 
  o�    z�    �� �  �� n  � �  �� �  ~� 
   � �  � �  � +  ��   ՞   � O  �� \  H�    �� �   �� u   ~� �  �� �  �� �   h� �   � �   �� V  6�    �� �   �� P   Q� W   ��    �� 3   
� G   A� 3  �� �  �� �  e� 8   � �  ;� �   �    �� �   �� <   h� }   �� �   #� *   � X   �    u� G  �� l  �� �  :� �  �� �   h�    �� j    �   k�    s� �   {� �  � �   �� ?   ^� 6   �� p  �� )   F� T  p� �  ��   P�    Y� k   w� �   �� 3  �� �  ��    9�   I� =   O� �   �� �   i�   � �   %� L   � )   Q�   {� 6   �� �  �� S  ^� �   �� .  �� X   �� =   � �   Y� B  �� �  =� �  �� �  �� :   �� �   ��   �� N  �� �   � �  �� �  �� )   m� �  �� #   �� �  �� O   ��    �� *  �� �  � �  � �   �  G  �      % �   @ �  � �  �
 z   � E  
 9  c 7   � �   � �   a $   �     G  ! �   i 1      O �  m   C   O �  ` �    �  � 1  b    �  �   �     �! �  �! 3   8#    l#    �#    �# +   �#    �#    �#    �#    $    	$ 	   $    %$    -$ 
   :$    H$ 	   g$ 4   q$    �$ 	   �$    �$    �$ �   �$ T   �%     �%    & ,   &     L& 3   m& 1   �&    �&    �&    �& 
   '    '    0'    C'    R'    X'    g'    n' (   �' &   �'    �' 4   �' D   &( 4   k( 3   �(    �( +   �( +   )    G) E   [) :   �)    �) X   �) C   <*    �*    �*     �*    �*    �*    �*    +    "+    4+    <+    L+    Y+    q+ 	   �+ 
   �+    �+ $   �+    �+    �+ 	   �+    �+ 	   �+    �+    
,    , 	   ,    $,    4,    @,    [, '   c,    �,    �,    �, 
   �,    �, $   �,    -    
- !   -    5-    <-    P-    \-    q- 
   z-    �- t   �- �   . �   �. �   C/ �  0 |   �1    i2    {2 
   �2    �2 
   �2    �2    �2 :   �2 h    3 K   �3 \   �3 "   24 6   U4 &   �4 l   �4 r    5 �   �5 |   l6 >   �6 %   (7    N7    b7    h7 
   q7    |7    �7    �7    �7    �7    �7    �7    �7    �7    �7 
   8    8    08    ?8    U8 	   [8 
   e8 
   p8    {8    �8   �8    �9    �9    �9 -   �9    : O   : _   b: ;   �: 5   �: >   4;    s;    {;    �; x   �;    �;    < 4   < �   L<    �<    �< �   �< (   �= �  �= l   >A �   �A �   .B �   �B 4   �C   �C }   �D R  tE "  �F    �G 	   	H M   H U   aH U   �H [   
I f   iI @   �I }   J $   �J .   �J    �J    �J Q   �J    MK    lK    rK 
   �K    �K �   �K 
   1L 0   ?L F   pL    �L    �L J   �L \   1M �  �M    xO    �O �   �O 
   SP    ^P    eP    lP 
   �P +   �P {  �P    9R 	   PR    ZR    fR 8  lR !   �S 
   �S    �S 5   �S    T 	   T +   #T    OT    kT (   rT �   �T    9U    XU b   jU    �U    �U    �U    �U ;   V g   WV    �V C   �V �   W s   �W N   6X    �X   �X O   �Y    Z (   Z    AZ �   SZ �   [    �[ Q   �[ �    \ �   �\    �]    �]    �] �  �]     �_ $   �_ (   �_ )   �_    "` �   B`    �`    a &   a P   Da 	   �a     �a    �a     �a �  �a �  �d �  If 3   )h 5   ]h    �h    �h A  �h �   �i �   �j $   �k �   �k �  �l $   }n    �n Q  �n Q  q    er    nr 1   ~r 	   �r @  �r    �s t  t �  �u    5w    6x p   Mx <   �x 2   �x z   .y ?  �y   �z �   �{ �  �| �   M~ $  �~ [   � �   P� �   � K   ځ �   &� /   � +   �    D�    V�    _� +   w�    �� L   �� 2   � 	   9� =   C� J  �� -   ̅ �   �� ~   ن �   X� (   �    �    .�    J�    ]� "   }� 0   �� "   ш �   � *   ҉ �   ��    �� �   Պ �   �� �   !� 4  �� �   ۍ @   �� �   � 	   p� 2  z� m   ��    � �  7�    ��    �    �    +� 2   I�    |�    �� h   �� �   � $  �� 
   ӕ    � $   � �   
� )  Ж �   �� �   ��    O�    i�    ��    ��    ��    ə    ܙ N   � 3   3� �  g� u   _�    ՜ p   � r   \� X   ϝ s   (� ]   �� W   ��    R� p   [� U   ̟ �   "� \   �� S   �    n� �   ~� k   y� D   � b   *� d   �� A   �    4� :   =� �   x� �   �� 9   � �   ��    ~� -  �� J   �� d   �� �   c�    � !  � �   1�    � (   1�    Z�    b� $  f� 2   �� �   �� �   B� �   � �   �� �   �� �   p� �   � �   ̲ ]   �� .  � l   � �  �� �   � �   ڷ 
   �� 
   ͸ 
   ۸ �   � 
   �� 
   �� T   �� o   �    |� 
   }� y   �� 4   � �   :� 7   ż �   �� }   ~� 
   �� �   
� 
   �� 
   ̾ 
   ھ =  � �   &� �  ��    {�    ��    �� �   �� "   e�     �� 
  �� �   ��    a�    y� $   �� ;   �� ?   �� =   *� !   h� !   ��   �� $   �� �  �� �   �� �   �� P   ,� �   }�   -� �  :� N  1� �   �� �   3� �   ��    {� e  �� !   �� Y  � �   r� -  f�    ��   � �   3� �   �� �   �� 9   � *   T�    � 1   �� 
   ��    ��    �� �   ��    �� h   �� &   T�    {�    ��    �� #   �� x   �� X   5� @   �� 
  �� .   ��    �    �    !�     6�    W�    i�    o�    t�    z�    �    ��    �� -   �� �   ��    ]�    n� 
   z�    �� 
   ��    �� 
   ��    ��    �� 
   �� +   �� �   %�    �� 4   �� f   �� �   S� �   �� �   ��   w� �   �� 	  V�    `� �   m� 5   � m   9� e   �� �   
�   �� H   �� f   ��    Z� p   i� x   �� �   S�    ��    ��    �    �    3� "   @�    c� ,   k�    ��    ��    ��    ��    ��    ��    �    *�    1�    C�    R�     Z�    {� (   �� 0   �� �   �� �   c� %   ��    � o   1� b   �� z   � )   � &   �� 7   �� ?   � ?   H� ,   �� �   ��   �� '  �� !   �� F   �� �   -� ,   �� �   �� �   �� �   �� L   C�    �� �   �� �   Y� }   �� ,   r� �   �� �   $�    
  4   *  #   _  8   �  j   �  i   '    �    �    � #   � �   � R   b    � <   � #       6 "   R >   u    � m   � M   =   � ]   � X   � �   U C   %    i    �    �    � *   �     *   1 9   \ 2   � S  � I    2   g ?   � +   �     x    y   � �   
 �   �
    � �   � "   2    U �   j    > 4   X D   � 5   � `    a   i M   �     '   7 �   _ 1   + "   ] ]   � �   � K   � �   " �   � _   G }   �    % 0   E �   v    
 q   %     � �   � 9   � M   � !    )   0 �   Z J   & T   q �   � i   q N   � �   * �   � 	   9    C J   R G   �    �    �        )    @ ,   U �   � e        n     �  %   �  8   �  7   ! |   ?! ;   �! �   �! m   �" #   �" x   #   �# 4   �$ `   �$    0% K   D% B   �%    �% �   �% �   n& 1   �& X   #'    |' 7   �' `   �'    &( z   :( &   �(    �( )   �( W   ) �   v)    P*    f*    {* 8   �*    �* Y   �* <   8+ �   u+ �   &,    �, K   �, �   )- -   �- �   &. J   �. "   @/ ]   c/ �   �/    �0    �0    �0    �0 (   �0 3   1 L   81 #   �1    �1    �1 (   �1 ;   �1 E   $2 
   j2 @   u2 D   �2    �2    3 B   #3    f3    �3 \   �3 �   �3 =   �4    )5    :5 �   G5 &  �5 H   7    f7 n  w7 �  �8 (   �: e   �:    ; Y  9; 2   �= ]   �=    $>    B> �  T>    "@ ^   =@ r   �@ e   A _   uA    �A i   �A C   [B    �B ~   �B G   >C 4   �C [   �C 8   D W   PD �   �D �   KE %    F �   &F �  �F �   |H )   PI <  zI �   �J �   �K T  �L �   �M (   �N    �N �   �N 6   �O   �O �   �Q F   ]R    �R    �R �  �R    �T �   �T F  �U   �V Q  �W E   GY D   �Y V   �Y 2   )Z -   \Z T   �Z `   �Z    @[ !   O[ 6   q[ +   �[    �[ .   �[ c   \ +   �\    �\ ]   �\   ] �   )^    �^    �^    �^ V    _ c   W_ `   �_ �   ` t   �`    Na $   `a �   �a    kb �   sb �  %c   f M   g )   ag    �g    �g    �g D   �g +   �g �   
h �   �h t   |i    �i    j .    j    Oj _   lj    �j W   �j    5k 7   <k +   tk    �k    �k W   �k    
l    l &   2l    Yl    ul m   yl �   �l `   �m h   n h   �n �   �n    �o !   �o �   �o �   �p �   zq    <r �   Er Z   �r Q   &s h   xs d   �s \   Ft    �t Q   �t    	u    u    0u    Bu    Uu    ku    �u    �u 
   �u 
   �u 3   �u 6   �u 3   2v !   fv ;   �v 1   �v 0   �v    'w    Dw ?   Xw    �w 
   �w R   �w .   x h   ?x *   �x    �x    �x -   �x #   +y    Oy ]   ay 1   �y Z   �y   Lz    l{    �{     �{    �{ 0   �{ 	   |    |    !| �   9| $   �| (   �|    } 
   } 	   "} 7   ,}    d} �   z}    K~    ^~    x~ %   �~    �~    �~    �~ !    #   3 /   W /   �    � k   � )   *�    T�    q� *   �� S   ��    � *   � )   J� �   t� o   �� D   h�    ��    �� 	   ͂    ׂ    �    �� x  � �   ��    �    (� ,   ,�    Y� +   i�    ��    �� v   �� H   � �   f�    X� !   m� �   �� !   �    6� $   T� '   y� 0   �� #   ҈    ��    � 2    �    S� &   j� 6   �� @  ȉ M   	� ;   W� K   ��    ߋ �   �� !   ��    �� R   ��    � )   &�    P� C   f� M   ��    �� �   �    Ŏ    Ҏ     �    �    �    /�    G�    f�    {� R   �� #  �     
� %   .� �   T� �   A�    �    �    �    2�    B�    X�    k�    ��    ��    ��    ��    Г 
   �    �    ��    �    �    )� *   9� �   d�   N� �  W�   � �  � �   �� q  �� 	   *� 9  4�    n�    �� �   �� �  �� �   � �  ٣ 6   i� �   �� =   M�    �� H   �� Q   � v   E� ~   �� �   ;� �   ۨ �   �� a  X�    �� �   լ �   �� m   x� �   �    �� �   �� �  w� �   � �   �� 
   � s   � ~   e� �   � }   �� �   6� �   ��    B�    R�    r� G   �� B   ӷ    � �   +� =   �� o   ��    d� �   x� �   
� R   �� a   � W   U� l   �� c   � U   ~� o   Լ d   D� w   �� �   !�    �� 7   �� +   � d   � <   {�    ��    �� N   ʿ    �    *�    >� /   E� 2   u� A   ��    ��    	�    �    � 	   #� \   -� t   �� g   �� :   g�    ��    �� -   �� 
   �� 
   ��    	�    �    �    "�    (� 
   .�    9�    =� 
   E�    P�    Y�    a�    p�    ��    ��    ��    ��    �� ?   �� "   ��    � y   7�    �� W   �� �   � 
   ��    ��    �� 
   �    �    �    � �   &� �   �� M   s�    ��    �� �   ��    l� �   |� �   ��     ��    �� �   �� �   d� Y   ?� �   �� �   ^� ,   �� �   �    ��    �� K   �� �   9�    �� �   �� z   �� �   � ]   ��    ,�    <�    @�    W�    ^�    o� 
   x�    �� �   �� d   ;� �   �� �   ?� �   � p   � V   �� �  �� f  ��   �� �   � 2  �� z  �� 	   Y� �   c� $  � �   1� �  �� �  }� w   ]� w  ��    M� >   i� �   �� �  A� T   �   X�    a�    r�    y� �   �� @   '� b   h� :   �� i   � R   p� k   �� 2   /� �   b� E   � -   a� P   �� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: tr
Language-Team: tr <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library popüler (ve yasadışı) bir kütüphanedir. Library Genesis koleksiyonunu alıp kolayca aranabilir hale getirdiler. Bunun üzerine, yeni kitap katkılarını teşvik ederek çok etkili hale geldiler. Şu anda bu yeni kitapları Library Genesis’e geri katkıda bulunmuyorlar. Ve Library Genesis’in aksine, koleksiyonlarını kolayca yansıtılabilir hale getirmiyorlar, bu da geniş çapta korunmayı engelliyor. Bu, iş modelleri için önemlidir, çünkü koleksiyonlarına toplu erişim için (günde 10 kitaptan fazla) para talep ediyorlar. Para karşılığında yasadışı bir kitap koleksiyonuna toplu erişim sağlama konusunda ahlaki yargılarda bulunmuyoruz. Z-Library'nin bilgiye erişimi genişletmede ve daha fazla kitap temin etmede başarılı olduğu şüphesizdir. Biz sadece üzerimize düşeni yapıyoruz: bu özel koleksiyonun uzun vadeli korunmasını sağlamak. - Anna ve ekip (<a %(reddit)s>Reddit</a>) Korsan Kütüphane Yansıtması’nın orijinal yayımında (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı), Z-Library’nin, büyük bir yasadışı kitap koleksiyonunun bir yansımasını yaptık. Hatırlatma olarak, bu orijinal blog yazısında yazdıklarımız: Bu koleksiyon 2021'in ortalarına kadar uzanıyor. Bu arada, Z-Library şaşırtıcı bir hızla büyüyor: yaklaşık 3.8 milyon yeni kitap eklediler. Elbette bazı kopyalar var, ancak çoğunluğu gerçekten yeni kitaplar veya daha önce gönderilen kitapların daha yüksek kaliteli taramaları gibi görünüyor. Bu büyük ölçüde Z-Library'deki gönüllü moderatör sayısının artması ve kopyaları önleyen toplu yükleme sistemleri sayesinde. Bu başarılarından dolayı onları tebrik etmek istiyoruz. Z-Library'ye son yansıtma işlemimiz ile Ağustos 2022 arasında eklenen tüm kitapları aldığımızı duyurmaktan mutluluk duyuyoruz. Ayrıca ilk seferde kaçırdığımız bazı kitapları da geri dönüp topladık. Sonuç olarak, bu yeni koleksiyon yaklaşık 24TB, bu da önceki (7TB) koleksiyondan çok daha büyük. Yansıtma işlemimiz şimdi toplamda 31TB. Yine, Library Genesis'e karşı kopyaları önledik, çünkü bu koleksiyon için zaten torrentler mevcut. Lütfen yeni koleksiyonu kontrol etmek için Korsan Kütüphane Yansıtması'na gidin (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı). Orada dosyaların nasıl yapılandırıldığı ve son seferden bu yana nelerin değiştiği hakkında daha fazla bilgi var. Buradan bağlantı vermeyeceğiz, çünkü bu sadece yasadışı materyaller barındırmayan bir blog sitesi. Elbette, tohumlama da bize yardımcı olmanın harika bir yoludur. Önceki torrent setimizi tohumlayan herkese teşekkürler. Olumlu tepkiler için minnettarız ve bu alışılmadık şekilde bilgi ve kültürün korunmasına önem veren bu kadar çok insanın olmasından mutluyuz. Korsan Kütüphane Yansıtması’na 3 yeni kitap eklendi (+24TB, 3.8 milyon kitap) TorrentFreak'in eşlik eden makalelerini okuyun: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a> - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak'in eşlik eden makaleleri: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a> Çok uzun zaman önce değil, "gölge kütüphaneler" yok oluyordu. Akademik makalelerin devasa yasadışı arşivi Sci-Hub, davalar nedeniyle yeni eserler kabul etmeyi bırakmıştı. Kitapların en büyük yasadışı kütüphanesi "Z-Library", iddia edilen yaratıcılarının telif hakkı ihlalleri nedeniyle tutuklanmasıyla karşı karşıya kaldı. İnanılmaz bir şekilde tutuklanmaktan kaçmayı başardılar, ancak kütüphaneleri tehdit altında olmaya devam ediyor. Bazı ülkeler bunun bir versiyonunu zaten yapıyor. TorrentFreak, Çin ve Japonya'nın telif hakkı yasalarına AI istisnaları eklediğini <a %(torrentfreak)s>bildirdi</a>. Bunun uluslararası anlaşmalarla nasıl etkileşime girdiği belirsiz, ancak kesinlikle yerli şirketlerine koruma sağlıyor, bu da gördüklerimizi açıklıyor. Anna’nın Arşivi'ne gelince — ahlaki inançla kök salmış yeraltı çalışmalarımıza devam edeceğiz. Ancak en büyük dileğimiz, ışığa çıkmak ve etkilerimizi yasal olarak artırmaktır. Lütfen telif hakkını reforme edin. Z-Library kapanma tehlikesiyle karşı karşıya kaldığında, ben zaten tüm kütüphanesini yedeklemiştim ve barındıracak bir platform arıyordum. Bu, Anna’nın Arşivi’ni başlatma motivasyonumdu: önceki girişimlerin arkasındaki misyonun devamı. O zamandan beri, kitaplar, akademik makaleler, dergiler, gazeteler ve daha fazlasını içeren çeşitli formatlarda 140 milyondan fazla telifli metni barındırarak dünyanın en büyük gölge kütüphanesi haline geldik. Ekibim ve ben ideologlarız. Bu dosyaları korumanın ve barındırmanın ahlaki olarak doğru olduğuna inanıyoruz. Dünyadaki kütüphaneler bütçe kesintileriyle karşı karşıya ve insanlığın mirasını şirketlere de emanet edemeyiz. Sonra yapay zeka geldi. Neredeyse tüm büyük LLM şirketleri, verilerimiz üzerinde eğitim almak için bizimle iletişime geçti. Çoğu (ama hepsi değil!) ABD merkezli şirketler, işimizin yasadışı doğasını fark ettiklerinde yeniden düşündüler. Buna karşılık, Çinli firmalar koleksiyonumuzu coşkuyla benimsedi, yasallığı konusunda görünüşte endişelenmediler. Bu, Çin'in neredeyse tüm büyük uluslararası telif hakkı anlaşmalarına imza atan bir ülke olması göz önüne alındığında dikkat çekicidir. Yaklaşık 30 şirkete yüksek hızlı erişim sağladık. Çoğu LLM şirketi ve bazıları da koleksiyonumuzu yeniden satacak olan veri brokerları. Çoğu Çinli, ancak ABD, Avrupa, Rusya, Güney Kore ve Japonya'dan şirketlerle de çalıştık. DeepSeek, önceki bir versiyonunun koleksiyonumuzun bir kısmı üzerinde eğitildiğini <a %(arxiv)s>kabul etti</a>, ancak en son modelleri hakkında ketumlar (muhtemelen yine verilerimiz üzerinde eğitildiler). Batı, LLM yarışında ve nihayetinde AGI'de önde kalmak istiyorsa, telif hakkı konusundaki duruşunu yeniden gözden geçirmeli ve bunu yakında yapmalıdır. Ahlaki duruşumuz konusunda bizimle aynı fikirde olsanız da olmasanız da, bu artık bir ekonomi ve hatta ulusal güvenlik meselesi haline geliyor. Tüm güç blokları yapay süper bilim insanları, süper hackerlar ve süper ordular inşa ediyor. Bilgi özgürlüğü, bu ülkeler için bir hayatta kalma meselesi haline geliyor — hatta ulusal güvenlik meselesi. Ekibimiz dünyanın dört bir yanından geliyor ve belirli bir hizipimiz yok. Ancak güçlü telif hakkı yasalarına sahip ülkeleri, bu varoluşsal tehdidi reform yapmak için kullanmaya teşvik ederiz. Peki ne yapmalı? İlk önerimiz basit: telif hakkı süresini kısaltın. ABD'de, telif hakkı yazarın ölümünden sonra 70 yıl boyunca verilir. Bu saçma. Bunu, başvurudan sonra 20 yıl boyunca verilen patentlerle uyumlu hale getirebiliriz. Bu, kitap, makale, müzik, sanat ve diğer yaratıcı eserlerin yazarlarının çabaları için tam olarak tazmin edilmesi için fazlasıyla yeterli olmalıdır (film uyarlamaları gibi uzun vadeli projeler dahil). Daha sonra, en azından, politika yapıcılar metinlerin kitlesel korunması ve yayılması için istisnalar eklemelidir. Bireysel müşterilerden kaybedilen gelir ana endişe ise, kişisel düzeyde dağıtım yasaklı kalabilir. Buna karşılık, geniş depoları yönetebilenler — LLM'leri eğiten şirketler, kütüphaneler ve diğer arşivler ile birlikte — bu istisnalarla kapsanacaktır. Telif hakkı reformu ulusal güvenlik için gereklidir. Özet: Çin LLM'leri (DeepSeek dahil) dünyanın en büyük yasadışı kitap ve makale arşivim üzerinde eğitildi. Batı, ulusal güvenlik meselesi olarak telif hakkı yasasını yeniden düzenlemelidir. Daha fazla bilgi için <a %(all_isbns)s>orijinal blog gönderisine</a> bakın. Bunu geliştirmek için bir meydan okuma başlattık. Birinci yer için 6.000 $, ikinci yer için 3.000 $ ve üçüncü yer için 1.000 $ ödül verecektik. Gelen yoğun yanıt ve inanılmaz gönderimler nedeniyle ödül havuzunu biraz artırmaya ve dört kişiye üçüncülük ödülü olarak 500 $ vermeye karar verdik. Kazananlar aşağıda, ancak tüm gönderimlere <a %(annas_archive)s>buradan</a> göz atmayı veya <a %(a_2025_01_isbn_visualization_files)s>birleştirilmiş torrentimizi</a> indirmeyi unutmayın. Birinci yer 6.000 $: phiresky Bu <a %(phiresky_github)s>gönderim</a> (<a %(annas_archive_note_2951)s>Gitlab yorumu</a>) tam olarak istediğimiz her şey ve daha fazlası! Özellikle inanılmaz esnek görselleştirme seçeneklerini (özel gölgelendiricileri bile destekleyen) ve kapsamlı ön ayar listesini çok beğendik. Ayrıca her şeyin ne kadar hızlı ve sorunsuz olduğunu, basit uygulamayı (arka ucu bile yok), akıllı mini haritayı ve <a %(phiresky_github)s>blog gönderilerinde</a> yer alan kapsamlı açıklamayı beğendik. İnanılmaz bir çalışma ve hak edilmiş bir birincilik! - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Kalplerimiz minnettarlıkla dolu. Dikkate değer fikirler Nadirlik için gökdelenler Veri kümelerini karşılaştırmak için DJ gibi birçok kaydırıcı. Kitap sayısı ile ölçek çubuğu. Güzel etiketler. Havalı varsayılan renk şeması ve ısı haritası. Benzersiz harita görünümü ve filtreler Açıklamalar ve ayrıca canlı istatistikler Canlı istatistikler Özellikle beğendiğimiz bazı daha fazla fikir ve uygulamalar: Bir süre daha devam edebilirdik, ama burada duralım. Tüm gönderimleri <a %(annas_archive)s>buradan</a> inceleyin veya <a %(a_2025_01_isbn_visualization_files)s>birleştirilmiş torrentimizi</a> indirin. O kadar çok gönderim var ki, her biri UI veya uygulama açısından benzersiz bir bakış açısı sunuyor. En azından birinci olan gönderimi ana web sitemize entegre edeceğiz ve belki bazı diğerlerini de. Ayrıca en nadir kitapları tanımlama, doğrulama ve ardından arşivleme sürecini nasıl organize edeceğimiz üzerine düşünmeye başladık. Bu konuda daha fazla bilgi gelecek. Katılan herkese teşekkürler. Bu kadar çok insanın ilgilenmesi harika. Hızlı karşılaştırmalar için veri kümelerini kolayca değiştirme. Tüm ISBN'ler CADAL SSNO'lar CERLALC veri sızıntısı DuXiu SSID'ler EBSCOhost’un eKitap İndeksi Google Kitaplar Goodreads İnternet Arşivi ISBNdb ISBN Küresel Yayıncılar Kaydı Libby Anna’nın Arşivi'ndeki Dosyalar Nexus/STC OCLC/Worldcat OpenLibrary Rusya Devlet Kütüphanesi Trantor İmparatorluk Kütüphanesi İkinci yer 3.000 $: hypha “Mükemmel kareler ve dikdörtgenler matematiksel olarak hoş olsa da, haritalama bağlamında üstün bir yerellik sağlamazlar. Bu Hilbert veya klasik Morton'da bulunan asimetrinin bir kusur değil, bir özellik olduğuna inanıyorum. İtalya'nın ünlü çizme şeklindeki silueti gibi, bu eğrilerin benzersiz "tuhaflıkları" bilişsel işaretler olarak hizmet edebilir. Bu ayırt edicilik, mekansal hafızayı geliştirebilir ve kullanıcıların kendilerini yönlendirmelerine yardımcı olabilir, bu da belirli bölgeleri bulmayı veya kalıpları fark etmeyi kolaylaştırabilir.” Başka bir inanılmaz <a %(annas_archive_note_2913)s>gönderim</a>. Birinci yer kadar esnek değil, ancak aslında makro düzeydeki görselleştirmesini birinci yerden daha çok beğendik (alan doldurma eğrisi, sınırlar, etiketleme, vurgulama, kaydırma ve yakınlaştırma). Joe Davis'in bir <a %(annas_archive_note_2971)s>yorumu</a> bizimle yankılandı: Ve hala görselleştirme ve render için birçok seçenek, ayrıca inanılmaz derecede sorunsuz ve sezgisel bir kullanıcı arayüzü. Sağlam bir ikincilik! - Anna ve ekip (<a %(reddit)s>Reddit</a>) Birkaç ay önce, ISBN alanını gösteren verilerimizin en iyi görselleştirmesini yapmak için <a %(all_isbns)s>10.000 $ ödül</a> duyurduk. Hangi dosyaları arşivleyip arşivlemediğimizi göstermeye vurgu yaptık ve daha sonra kaç kütüphanenin ISBN'leri tuttuğunu (nadirlik ölçüsü) açıklayan bir veri seti ekledik. Tepkiler karşısında şaşkına döndük. Çok fazla yaratıcılık vardı. Katılan herkese büyük teşekkürler: enerjiniz ve coşkunuz bulaşıcı! Sonunda şu soruları yanıtlamak istedik: <strong>dünyada hangi kitaplar var, hangilerini zaten arşivledik ve hangi kitaplara odaklanmalıyız?</strong> Bu sorularla ilgilenen bu kadar çok insan görmek harika. Kendimiz temel bir görselleştirme ile başladık. 300kb'den daha az bir boyutta, bu resim insanlık tarihindeki en büyük tamamen açık "kitap listesi"ni özlü bir şekilde temsil ediyor: Üçüncü yer 500 $ #1: maxlion Bu <a %(annas_archive_note_2940)s>gönderimde</a> özellikle karşılaştırma ve yayıncı görünümleri olmak üzere farklı türdeki görünümleri çok beğendik. Üçüncü yer 500 $ #2: abetusk En cilalı kullanıcı arayüzü olmasa da, bu <a %(annas_archive_note_2917)s>gönderim</a> birçok kutuyu işaretliyor. Özellikle karşılaştırma özelliğini beğendik. Üçüncü yer 500 $ #3: conundrumer0 Birinci yer gibi, bu <a %(annas_archive_note_2975)s>gönderim</a> esnekliği ile bizi etkiledi. Sonuçta bu, harika bir görselleştirme aracını oluşturan şeydir: güç kullanıcıları için maksimum esneklik sağlarken, ortalama kullanıcılar için işleri basit tutmak. Üçüncü yer 500 $ #4: charelf Ödül alan son <a %(annas_archive_note_2947)s>gönderim</a> oldukça basit, ancak gerçekten beğendiğimiz bazı benzersiz özelliklere sahip. Belirli bir ISBN'yi kapsayan kaç dataset olduğunu popülerlik/güvenilirlik ölçüsü olarak göstermelerini beğendik. Ayrıca karşılaştırmalar için opaklık kaydırıcısını kullanmanın basit ama etkili olmasını çok beğendik. 10.000 $ ISBN görselleştirme ödülü kazananları Özet: 10.000 $ ISBN görselleştirme ödülüne inanılmaz başvurular aldık. Arka plan Anna’nın Arşivi, insanlığın tüm bilgisini yedekleme misyonunu, hangi kitapların hala mevcut olduğunu bilmeden nasıl gerçekleştirebilir? Bir YAPILACAKLAR listesine ihtiyacımız var. Bunu haritalamanın bir yolu, 1970'lerden bu yana (çoğu ülkede) yayımlanan her kitaba atanan ISBN numaralarıdır. Tüm ISBN atamalarını bilen merkezi bir otorite yoktur. Bunun yerine, ülkelerin numara aralıkları aldığı, ardından büyük yayınevlerine daha küçük aralıklar atadığı, bu yayınevlerinin de daha küçük yayınevlerine aralıkları alt bölümlere ayırabileceği dağıtılmış bir sistemdir. Son olarak, bireysel numaralar kitaplara atanır. ISBN'leri <a %(blog)s>iki yıl önce</a> ISBNdb'yi tarayarak haritalamaya başladık. O zamandan beri, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ve daha fazlası gibi birçok metadata kaynağını taradık. Tam liste, Anna’nın Arşivi'ndeki “Datasets” ve “Torrents” sayfalarında bulunabilir. Şu anda, dünyadaki en büyük tamamen açık, kolayca indirilebilir kitap metadata (ve dolayısıyla ISBN) koleksiyonuna sahibiz. Koruma neden önemli ve neden şu anda kritik bir dönemde olduğumuz hakkında <a %(blog)s>geniş çapta yazılar yazdık</a>. Şimdi nadir, odaklanılmamış ve benzersiz şekilde risk altındaki kitapları tanımlayıp korumalıyız. Dünyadaki tüm kitaplar hakkında iyi metadata'ya sahip olmak bu konuda yardımcı olur. 10.000 $ ödül Kullanılabilirlik ve görünüm açısından güçlü bir değerlendirme yapılacaktır. Yakınlaştırırken bireysel ISBN'ler için başlık ve yazar gibi gerçek metadata gösterin. Daha iyi bir alan doldurma eğrisi. Örneğin, ilk satırda 0'dan 4'e ve ardından ikinci satırda (tersine) 5'ten 9'a giden bir zikzak — özyinelemeli olarak uygulanmış. Farklı veya özelleştirilebilir renk şemaları. Datasets karşılaştırmak için özel görünümler. Diğer metadata ile iyi uyuşmayan sorunları (örneğin, çok farklı başlıklar) ayıklama yolları. ISBN'ler veya aralıklar hakkında yorumlarla görüntüleri açıklama. Nadir veya risk altındaki kitapları tanımlamak için herhangi bir yöntem. Aklınıza gelebilecek yaratıcı fikirler! Kod Bu görüntüleri oluşturmak için kullanılan kod ve diğer örnekler <a %(annas_archive)s>bu dizinde</a> bulunabilir. Tüm gerekli ISBN bilgilerini içeren, yaklaşık 75MB (sıkıştırılmış) olan kompakt bir veri formatı geliştirdik. Veri formatının açıklaması ve onu oluşturmak için kullanılan kod <a %(annas_archive_l1244_1319)s>burada</a> bulunabilir. Ödül için bunu kullanmanız gerekmiyor, ancak başlamak için muhtemelen en uygun format. Metadata'mızı istediğiniz gibi dönüştürebilirsiniz (ancak tüm kodunuz açık kaynak olmalıdır). Neler ortaya koyacağınızı görmek için sabırsızlanıyoruz. İyi şanslar! Bu depo'yu çatallayın ve bu blog gönderisi HTML'sini düzenleyin (Flask backend'imiz dışında başka backend'lere izin verilmez). Yukarıdaki resmi, bireysel ISBN'lere kadar yakınlaştırılabilir hale getirin. ISBN'lere tıklamak, Anna’nın Arşivi'nde bir metadata sayfasına veya aramaya götürmelidir. Tüm farklı datasetler arasında geçiş yapabilmelisiniz. Ülke aralıkları ve yayınevi aralıkları üzerine gelindiğinde vurgulanmalıdır. Örneğin, ülke bilgisi için <a %(github_xlcnd_isbnlib)s>isbnlib'deki data4info.py</a> ve yayınevleri için “isbngrp” taramamızı kullanabilirsiniz (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Masaüstü ve mobilde iyi çalışmalıdır. Burada keşfedilecek çok şey var, bu yüzden yukarıdaki görselleştirmeyi geliştirmek için bir ödül duyuruyoruz. Çoğu ödülümüzden farklı olarak, bu ödül zaman sınırlıdır. Açık kaynak kodunuzu 2025-01-31 (23:59 UTC) tarihine kadar <a %(annas_archive)s>göndermeniz</a> gerekiyor. En iyi gönderi 6.000 $, ikinci yer 3.000 $ ve üçüncü yer 1.000 $ alacak. Tüm ödüller Monero (XMR) kullanılarak verilecektir. Aşağıda minimum kriterler bulunmaktadır. Hiçbir gönderi kriterleri karşılamazsa, yine de bazı ödüller verebiliriz, ancak bu bizim takdirimize bağlı olacaktır. Ekstra puanlar için (bunlar sadece fikirlerdir — yaratıcılığınızı serbest bırakın): Minimum kriterlerden tamamen sapabilir ve tamamen farklı bir görselleştirme yapabilirsiniz. Eğer gerçekten muhteşemse, bu ödül için nitelikli olur, ancak bizim takdirimize bağlıdır. <a %(annas_archive)s>Bu soruna</a> bir yorum göndererek, çatalladığınız depo, birleştirme isteği veya fark bağlantısı ile gönderim yapın. - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Bu resim 1000×800 piksel. Her piksel 2,500 ISBN'yi temsil ediyor. Bir ISBN için dosyamız varsa, o pikseli daha yeşil yapıyoruz. Bir ISBN'nin verildiğini biliyorsak, ancak eşleşen bir dosyamız yoksa, onu daha kırmızı yapıyoruz. 300kb'den daha az bir boyutta, bu resim insanlık tarihindeki en büyük tamamen açık "kitap listesi"ni özlü bir şekilde temsil ediyor (tam sıkıştırılmış hali birkaç yüz GB). Ayrıca gösteriyor ki: kitapları yedekleme konusunda yapılacak çok iş var (sadece 16% var). Tüm ISBN'leri Görselleştirme — 2025-01-31 tarihine kadar $10,000 ödül Bu resim, insanlık tarihindeki en büyük tamamen açık "kitap listesi"ni temsil ediyor. Görselleştirme Genel görünüm resminin yanı sıra, edindiğimiz bireysel datasetlere de bakabiliriz. Aralarında geçiş yapmak için açılır menüyü ve düğmeleri kullanın. Bu resimlerde görülecek birçok ilginç desen var. Neden farklı ölçeklerde meydana gelen bazı düzenli çizgi ve bloklar var? Boş alanlar nedir? Neden belirli datasetler bu kadar kümelenmiş? Bu soruları okuyucuya bir alıştırma olarak bırakacağız. - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Sonuç Bu standartla, yayınları daha kademeli olarak yapabilir ve yeni veri kaynaklarını daha kolay ekleyebiliriz. Zaten birkaç heyecan verici yayınımız sırada! Diğer gölge kütüphanelerin koleksiyonlarımızı yansıtmasının daha kolay hale gelmesini de umuyoruz. Sonuçta, amacımız insan bilgisini ve kültürünü sonsuza dek korumak, bu yüzden ne kadar çok yedekleme olursa o kadar iyi. Örnek Son Z-Library yayınımıza bir örnek olarak bakalım. İki koleksiyondan oluşuyor: “<span style="background: #fffaa3">zlib3_records</span>” ve “<span style="background: #ffd6fe">zlib3_files</span>”. Bu, metadata kayıtlarını gerçek kitap dosyalarından ayrı olarak kazımamıza ve yayınlamamıza olanak tanır. Bu nedenle, metadata dosyalarıyla iki torrent yayınladık: Ayrıca, “<span style="background: #ffd6fe">zlib3_files</span>” koleksiyonu için toplamda 62 adet ikili veri klasörü içeren bir dizi torrent yayınladık: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> çalıştırarak içindekileri görebiliriz: Bu durumda, Z-Library tarafından bildirilen bir kitabın metadata'sı. Üst düzeyde sadece “aacid” ve “metadata” var, ancak “data_folder” yok, çünkü karşılık gelen ikili veri yok. AACID, birincil kimlik olarak “22430000” içeriyor ve bunun “zlibrary_id”den alındığını görebiliriz. Bu koleksiyondaki diğer AAC'lerin de aynı yapıya sahip olmasını bekleyebiliriz. Şimdi <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> çalıştıralım: Bu, çok daha küçük bir AAC metadata'sı, ancak bu AAC'nin büyük kısmı başka bir yerde bir ikili dosyada bulunuyor! Sonuçta, bu sefer bir “data_folder” var, bu yüzden karşılık gelen ikili verinin <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> konumunda bulunmasını bekleyebiliriz. “Metadata”, “zlibrary_id” içerdiğinden, bunu “zlib_records” koleksiyonundaki karşılık gelen AAC ile kolayca ilişkilendirebiliriz. Bunu farklı şekillerde de ilişkilendirebilirdik, örneğin AACID aracılığıyla — standart bunu zorunlu kılmaz. “Metadata” alanının kendisinin JSON olması da gerekli değildir. XML veya başka bir veri formatı içeren bir dize olabilir. Hatta, eğer çok fazla veri varsa, metadata bilgisini ilişkili ikili blobda bile saklayabilirsiniz. Orijinal formata mümkün olduğunca yakın heterojen dosyalar ve metadata. İkili veriler, Nginx gibi web sunucuları tarafından doğrudan sunulabilir. Kaynak kütüphanelerdeki heterojen tanımlayıcılar veya tanımlayıcıların eksikliği. Metadata ile dosya verilerinin ayrı sürümleri veya yalnızca metadata sürümleri (örneğin, ISBNdb sürümümüz). Torrentler aracılığıyla dağıtım, ancak diğer dağıtım yöntemlerinin de mümkün olması (örneğin, IPFS). Değişmez kayıtlar, çünkü torrentlerimizin sonsuza kadar yaşayacağını varsaymalıyız. Artımlı sürümler / eklenebilir sürümler. Makine tarafından okunabilir ve yazılabilir, özellikle yığınımız için (Python, MySQL, ElasticSearch, Transmission, Debian, ext4) uygun ve hızlı bir şekilde. İnsan tarafından bir dereceye kadar kolay incelenebilir, ancak bu, makine tarafından okunabilirliğe göre ikincildir. Koleksiyonlarımızı standart kiralık bir seedbox ile kolayca tohumlamak. Tasarım hedefleri Dosyaların diskte manuel olarak kolayca gezilebilir veya ön işleme olmadan aranabilir olmasını umursamıyoruz. Mevcut kütüphane yazılımlarıyla doğrudan uyumlu olmayı umursamıyoruz. Koleksiyonumuzu torrentler kullanarak tohumlamanın herkes için kolay olması gerekirken, dosyaların önemli teknik bilgi ve bağlılık olmadan kullanılabilir olmasını beklemiyoruz. Birincil kullanım amacımız, farklı mevcut koleksiyonlardan dosyaların ve ilişkili metadata'nın dağıtımıdır. En önemli dikkate aldığımız hususlar: Bazı hedefler: Anna’nın Arşivi açık kaynak olduğundan, formatımızı doğrudan kullanmak istiyoruz. Arama dizinimizi yenilediğimizde, yalnızca herkese açık yolları erişiyoruz, böylece kütüphanemizi çatallayan herkes hızlıca çalışmaya başlayabilir. <strong>AAC.</strong> AAC (Anna’nın Arşiv Konteyneri), <strong>metadata</strong> ve isteğe bağlı olarak <strong>ikili veri</strong> içeren tek bir öğedir ve her ikisi de değiştirilemez. Küresel olarak benzersiz bir tanımlayıcıya sahiptir, buna <strong>AACID</strong> denir. <strong>AACID.</strong> AACID formatı şu şekildedir: <code style="color: #0093ff">aacid__{koleksiyon}__{ISO 8601 zaman damgası}__{koleksiyon-özel ID}__{shortuuid}</code>. Örneğin, yayınladığımız gerçek bir AACID <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code> şeklindedir. <strong>AACID aralığı.</strong> AACID'ler monotonik olarak artan zaman damgaları içerdiğinden, belirli bir koleksiyon içinde aralıkları belirtmek için bunu kullanabiliriz. Bu formatı kullanıyoruz: <code style="color: blue">aacid__{koleksiyon}__{from_timestamp}--{to_timestamp}</code>, burada zaman damgaları dahil. Bu, ISO 8601 notasyonu ile tutarlıdır. Aralıklar süreklidir ve çakışabilir, ancak çakışma durumunda, o koleksiyonda daha önce yayınlananla aynı kayıtları içermelidir (çünkü AAC'ler değiştirilemez). Eksik kayıtlar kabul edilmez. <code>{koleksiyon}</code>: ASCII harfleri, sayılar ve alt çizgiler içerebilen (ancak çift alt çizgi içermeyen) koleksiyon adı. <code>{koleksiyon-özel ID}</code>: varsa, örneğin Z-Library ID'si gibi koleksiyon-özel bir tanımlayıcı. Atlanabilir veya kısaltılabilir. AACID'nin 150 karakteri aşması durumunda atlanmalı veya kısaltılmalıdır. <code>{ISO 8601 zaman damgası}</code>: her zaman UTC'de olan ISO 8601'in kısa bir versiyonu, örneğin <code>20220723T194746Z</code>. Bu sayı her yayın için monotonik olarak artmalıdır, ancak tam anlamı koleksiyona göre farklılık gösterebilir. Kazıma veya ID oluşturma zamanını kullanmanızı öneririz. <code>{shortuuid}</code>: bir UUID ancak ASCII'ye sıkıştırılmış, örneğin base57 kullanılarak. Şu anda <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python kütüphanesini kullanıyoruz. <strong>İkili veri klasörü.</strong> Belirli bir koleksiyon için bir AAC aralığının ikili verilerini içeren bir klasör. Bunlar şu özelliklere sahiptir: Dizin, belirtilen aralıktaki tüm AAC'ler için veri dosyalarını içermelidir. Her veri dosyası, dosya adı olarak AACID'sine sahip olmalıdır (ek yok). Dizin adı bir AACID aralığı olmalı, <code style="color: green">annas_archive_data__</code> ile başlamalı ve ek olmamalıdır. Örneğin, gerçek yayınlarımızdan biri<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> adlı bir dizine sahiptir. Bu klasörlerin boyutunu yönetilebilir tutmak önerilir, örneğin her biri 100GB-1TB'den büyük olmamalıdır, ancak bu öneri zamanla değişebilir. <strong>Koleksiyon.</strong> Her AAC bir koleksiyona aittir, bu tanım gereği anlamsal olarak tutarlı AAC'lerin bir listesidir. Bu, metadata formatında önemli bir değişiklik yaparsanız, yeni bir koleksiyon oluşturmanız gerektiği anlamına gelir. Standart <strong>Metadata dosyası.</strong> Bir metadata dosyası, belirli bir koleksiyon için bir AAC aralığının metadata'sını içerir. Bunlar şu özelliklere sahiptir: <code>data_folder</code> isteğe bağlıdır ve karşılık gelen ikili verileri içeren ikili veri klasörünün adıdır. O klasördeki karşılık gelen ikili verinin dosya adı kaydın AACID'sidir. Her JSON nesnesi üst düzeyde şu alanları içermelidir: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (isteğe bağlı). Başka alanlara izin verilmez. Dosya adı bir AACID aralığı olmalı, <code style="color: red">annas_archive_meta__</code> ile başlamalı ve <code>.jsonl.zstd</code> ile bitmelidir. Örneğin, yayınlarımızdan biri<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> olarak adlandırılmıştır. Dosya uzantısının belirttiği gibi, dosya türü <a %(jsonlines)s>JSON Lines</a> ve <a %(zstd)s>Zstandard</a> ile sıkıştırılmıştır. <code>metadata</code>, koleksiyonun anlamına göre keyfi metadata'dır. Koleksiyon içinde anlamsal olarak tutarlı olmalıdır. <code style="color: red">annas_archive_meta__</code> öneki, kurumunuzun adına uyarlanabilir, örneğin <code style="color: red">my_institute_meta__</code>. <strong>“kayıtlar” ve “dosyalar” koleksiyonları.</strong> Geleneksel olarak, “kayıtlar” ve “dosyalar”ı farklı koleksiyonlar olarak yayınlamak genellikle uygundur, böylece farklı zamanlamalarla yayınlanabilirler, örneğin kazıma oranlarına göre. Bir “kayıt”, kitap başlıkları, yazarlar, ISBN'ler gibi bilgileri içeren yalnızca metadata koleksiyonudur, “dosyalar” ise gerçek dosyaların kendilerini (pdf, epub) içeren koleksiyonlardır. Sonunda, nispeten basit bir standart üzerinde karar kıldık. Oldukça gevşek, normatif olmayan ve devam eden bir çalışmadır. <strong>Torrents.</strong> Metadata dosyaları ve ikili veri klasörleri torrentlerde paketlenebilir, her metadata dosyası için bir torrent veya her ikili veri klasörü için bir torrent olacak şekilde. Torrentler, orijinal dosya/dizin adı artı <code>.torrent</code> uzantısını dosya adı olarak içermelidir. <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>, açık ara dünyanın en büyük gölge kütüphanesi haline geldi ve ölçeğinde tamamen açık kaynak ve açık veri olan tek gölge kütüphane. Aşağıda, Datasets sayfamızdan (biraz değiştirilmiş) bir tablo bulunmaktadır: Bunu üç şekilde başardık: Mevcut açık veri gölge kütüphanelerini yansıtma (Sci-Hub ve Library Genesis gibi). Daha açık olmak isteyen ancak bunu yapacak zaman veya kaynakları olmayan gölge kütüphanelere yardım etme (Libgen çizgi roman koleksiyonu gibi). Toplu paylaşımda bulunmak istemeyen kütüphaneleri kazıma (Z-Library gibi). (2) ve (3) için artık kendimiz oldukça büyük bir torrent koleksiyonunu yönetiyoruz (yüzlerce TB). Şimdiye kadar bu koleksiyonlara tek seferlik işler olarak yaklaştık, yani her koleksiyon için özel altyapı ve veri organizasyonu. Bu, her sürüme önemli bir ek yük getiriyor ve daha kademeli sürümler yapmayı özellikle zorlaştırıyor. Bu yüzden sürümlerimizi standartlaştırmaya karar verdik. Bu, standartımızı tanıttığımız teknik bir blog yazısıdır: <strong>Anna’nın Arşiv Konteynerleri</strong>. Anna’nın Arşivi Konteynerleri (AAC): Dünyanın en büyük gölge kütüphanesinden sürümleri standartlaştırma Anna’nın Arşivi, dünyanın en büyük gölge kütüphanesi haline geldi ve sürümlerimizi standartlaştırmamız gerekiyor. 300GB+ kitap kapağı yayınlandı Son olarak, küçük bir sürüm duyurmaktan mutluluk duyuyoruz. Libgen.rs çatalını işleten kişilerle işbirliği içinde, tüm kitap kapaklarını torrentler ve IPFS aracılığıyla paylaşıyoruz. Bu, kapakların görüntülenmesi yükünü daha fazla makine arasında dağıtacak ve onları daha iyi koruyacak. Birçok (ama hepsi değil) durumda, kitap kapakları dosyaların içinde yer alıyor, bu yüzden bu bir tür "türetilmiş veri". Ancak IPFS'de bulunması, hem Anna’nın Arşivi hem de çeşitli Library Genesis çatalları için günlük operasyonlar açısından hala çok faydalı. Her zamanki gibi, bu sürümü Korsan Kütüphane Yansıtma'da bulabilirsiniz (DÜZENLE: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne taşındı). Burada bağlantı vermeyeceğiz, ama kolayca bulabilirsiniz. Umarız Z-Library'e iyi bir alternatif bulduğumuz için biraz tempomuzu yavaşlatabiliriz. Bu iş yükü pek sürdürülebilir değil. Programlama, sunucu operasyonları veya koruma çalışmalarıyla ilgileniyorsanız, kesinlikle bizimle iletişime geçin. Hâlâ yapılacak çok <a %(annas_archive)s>iş var</a>. İlginiz ve desteğiniz için teşekkürler. ElasticSearch'e Geçiş Bazı sorgular çok uzun sürdü, açık bağlantıların tamamını kaplayacak kadar. Varsayılan olarak MySQL, minimum kelime uzunluğuna sahiptir veya dizininiz gerçekten büyük olabilir. İnsanlar "Ben Hur" araması yapamadıklarını bildirdi. Arama, yalnızca tamamen belleğe yüklendiğinde biraz hızlıydı, bu da bunu çalıştırmak için daha pahalı bir makine almamızı ve başlangıçta dizini önceden yüklemek için bazı komutlar kullanmamızı gerektirdi. Yeni özellikler oluşturmak için kolayca genişletemezdik, örneğin daha iyi <a %(wikipedia_cjk_characters)s>boşluksuz diller için ayrıştırma</a>, filtreleme/faseting, sıralama, "bunu mu demek istediniz" önerileri, otomatik tamamlama ve benzeri. <a %(annas_archive)s>Biletlerimizden</a> biri, arama sistemimizle ilgili bir dizi sorunu içeriyordu. Zaten tüm verilerimiz MySQL'de olduğu için MySQL tam metin aramasını kullandık. Ancak bunun sınırları vardı: Bir dizi uzmanla konuştuktan sonra ElasticSearch'te karar kıldık. Mükemmel olmadı (varsayılan "bunu mu demek istediniz" önerileri ve otomatik tamamlama özellikleri berbat), ama genel olarak MySQL'den çok daha iyi oldu. Hâlâ herhangi bir kritik veri için kullanmaya <a %(youtube)s>çok istekli değiliz</a> (gerçi çok <a %(elastic_co)s>ilerleme</a> kaydettiler), ama genel olarak değişiklikten oldukça memnunuz. Şimdilik, çok daha hızlı arama, daha iyi dil desteği, daha iyi alaka sıralaması, farklı sıralama seçenekleri ve dil/kitap türü/dosya türü üzerinde filtreleme uyguladık. Nasıl çalıştığını merak ediyorsanız, <a %(annas_archive_l140)s>bir</a> <a %(annas_archive_l1115)s>göz</a> <a %(annas_archive_l1635)s>atın</a>. Oldukça erişilebilir, ancak biraz daha fazla yoruma ihtiyacı olabilir… Anna’nın Arşivi tamamen açık kaynak Bilginin özgür olması gerektiğine inanıyoruz ve kendi kodumuz da bir istisna değil. Tüm kodumuzu özel olarak barındırılan Gitlab örneğimizde yayınladık: <a %(annas_archive)s>Anna’nın Yazılımı</a>. Çalışmamızı organize etmek için sorun izleyicisini de kullanıyoruz. Geliştirmemizle ilgilenmek istiyorsanız, başlamak için harika bir yer. Üzerinde çalıştığımız şeylerin bir tadına varmanız için, istemci tarafı performans iyileştirmeleri üzerindeki son çalışmamıza bir göz atın. Henüz sayfalama uygulamadığımız için, genellikle 100-200 sonuç içeren çok uzun arama sayfaları döndürürdük. Arama sonuçlarını çok erken kesmek istemedik, ancak bu bazı cihazları yavaşlatacağı anlamına geliyordu. Bunun için küçük bir numara uyguladık: Çoğu arama sonucunu HTML yorumlarına (<code><!-- --></code>) sardık ve ardından bir sonucun görünür olması gerektiğini tespit eden küçük bir Javascript yazdık, bu anda yorumu açtık: DOM "sanallaştırma" 23 satırda uygulandı, süslü kütüphanelere gerek yok! Bu, sınırlı zamanınız olduğunda ve çözülmesi gereken gerçek sorunlar olduğunda ortaya çıkan hızlı ve pragmatik bir koddur. Aramamızın artık yavaş cihazlarda iyi çalıştığı bildirildi! Bir diğer büyük çaba, veritabanını otomatik olarak oluşturmaktı. Başladığımızda, farklı kaynakları rastgele bir araya getirdik. Şimdi onları güncel tutmak istiyoruz, bu yüzden iki Library Genesis çatalından yeni metadata indirmek ve entegre etmek için bir dizi betik yazdık. Amacımız sadece arşivimiz için faydalı hale getirmek değil, gölge kütüphane metadata'sı ile oynamak isteyen herkes için işleri kolaylaştırmak. Hedef, her türlü ilginç metadata'nın mevcut olduğu bir Jupyter defteri oluşturmak, böylece <a %(blog)s>ISBN'lerin hangi yüzdesinin sonsuza kadar korunduğunu</a> gibi daha fazla araştırma yapabiliriz. Son olarak, bağış sistemimizi yeniledik. Artık kredi kartı kullanarak doğrudan kripto cüzdanlarımıza para yatırabilirsiniz, kripto paralar hakkında gerçekten bir şey bilmenize gerek kalmadan. Bunun pratikte ne kadar iyi çalıştığını izlemeye devam edeceğiz, ama bu büyük bir adım. Z-Library'nin kapanması ve (iddia edilen) kurucularının tutuklanmasıyla, Anna’nın Arşivi ile iyi bir alternatif sunmak için gece gündüz çalışıyoruz (burada bağlantı vermeyeceğiz, ancak Google'da arayabilirsiniz). İşte son zamanlarda başardığımız bazı şeyler. Anna’nın Güncellemesi: tamamen açık kaynak arşiv, ElasticSearch, 300GB+ kitap kapakları Anna’nın Arşivi ile iyi bir alternatif sunmak için gece gündüz çalışıyoruz. İşte son zamanlarda başardığımız bazı şeyler. Analiz Anlamsal kopyalar (aynı kitabın farklı taramaları) teorik olarak filtrelenebilir, ancak bu zordur. Çizgi romanları manuel olarak incelediğimizde çok fazla yanlış pozitif bulduk. Sadece MD5 ile kopyalar var, bu nispeten israf, ancak bunları filtrelemek bize sadece yaklaşık 1% in tasarruf sağlar. Bu ölçekte bu hala yaklaşık 1TB, ancak bu ölçekte 1TB gerçekten önemli değil. Bu süreçte yanlışlıkla veri yok etme riskini almak istemeyiz. Çizgi romanlara dayanan filmler gibi bir dizi kitap dışı veri bulduk. Bu da israf gibi görünüyor, çünkü bunlar zaten başka yollarla geniş çapta mevcut. Ancak, bilgisayarda yayınlanan ve birinin kaydedip film olarak kaydettiği <em>etkileşimli çizgi romanlar</em> da olduğu için film dosyalarını filtreleyemeyeceğimizi fark ettik. Sonuçta, koleksiyondan sileceğimiz herhangi bir şey sadece birkaç yüzde tasarruf sağlayacaktır. Sonra hatırladık ki biz veri biriktiricileriz ve bunu yansıtacak olanlar da veri biriktiriciler, bu yüzden, “SİLMEK Mİ?!” :) Depolama kümenize 95TB döküldüğünde, içinde ne olduğunu anlamaya çalışırsınız… Boyutu biraz azaltıp azaltamayacağımızı görmek için bazı analizler yaptık, örneğin kopyaları kaldırarak. İşte bazı bulgularımız: Bu nedenle, size tam ve değiştirilmemiş koleksiyonu sunuyoruz. Bu çok fazla veri, ancak yine de yeterince insanın bunu paylaşmak isteyeceğini umuyoruz. İşbirliği Büyüklüğü göz önüne alındığında, bu koleksiyon uzun zamandır istek listemizdeydi, bu yüzden Z-Library'yi yedekleme konusundaki başarımızdan sonra, bu koleksiyona odaklandık. Başlangıçta doğrudan kazıdık, bu oldukça zordu çünkü sunucuları en iyi durumda değildi. Bu şekilde yaklaşık 15TB elde ettik, ancak yavaş ilerliyordu. Neyse ki, kütüphanenin operatörüyle iletişime geçmeyi başardık ve tüm verileri doğrudan bize göndermeyi kabul etti, bu çok daha hızlıydı. Yine de tüm verileri aktarmak ve işlemek yarım yıldan fazla sürdü ve disk bozulması nedeniyle neredeyse hepsini kaybediyorduk, bu da her şeye yeniden başlamamız anlamına gelirdi. Bu deneyim, bu verileri mümkün olan en kısa sürede yaymanın önemli olduğuna inanmamıza neden oldu, böylece geniş çapta yansıtılabilir. Bu koleksiyonu sonsuza kadar kaybetmemize sadece bir veya iki şanssız olay uzaktayız! Koleksiyon Hızlı hareket etmek, koleksiyonun biraz düzensiz olduğu anlamına geliyor… Bir göz atalım. Gerçekte torrentler arasında böldüğümüz bir dosya sistemi hayal edin: İlk dizin, <code>/repository</code>, bunun daha yapılandırılmış kısmıdır. Bu dizin, her biri veritabanında artan numaralarla numaralandırılmış binlerce dosya içeren "bin dizinleri" olarak adlandırılan dizinleri içerir. <code>0</code> dizini, comic_id 0–999 olan dosyaları içerir ve bu şekilde devam eder. Bu, Library Genesis'in kurgu ve kurgu dışı koleksiyonları için kullandığı aynı şemadır. Fikir, her "bin dizinin" dolduğunda otomatik olarak bir torrente dönüştürülmesidir. Ancak, Libgen.li operatörü bu koleksiyon için hiçbir zaman torrent yapmadı, bu yüzden bin dizinleri muhtemelen elverişsiz hale geldi ve "sıralanmamış dizinlere" yol açtı. Bunlar <code>/comics0</code> ile <code>/comics4</code> arasındadır. Hepsi, dosyaları toplamak için muhtemelen mantıklı olan, ancak şimdi bizim için pek anlam ifade etmeyen benzersiz dizin yapıları içerir. Neyse ki, metadata hala doğrudan bu dosyalara atıfta bulunuyor, bu yüzden disk üzerindeki depolama organizasyonları aslında önemli değil! Metadata, bir MySQL veritabanı biçiminde mevcuttur. Bu, doğrudan Libgen.li web sitesinden indirilebilir, ancak biz de kendi MD5 hash'lerimizi içeren tabloyla birlikte bir torrentte sunacağız. <q>Dr. Barbara Gordon, kütüphanenin sıradan dünyasında kaybolmaya çalışıyor…</q> Libgen çatalları Öncelikle, biraz arka plan bilgisi. Library Genesis'i muhteşem kitap koleksiyonu ile tanıyor olabilirsiniz. Daha az kişi, Library Genesis gönüllülerinin, Sci-Hub'un kurucusu Alexandra Elbakyan ile işbirliği içinde Sci-Hub'un tam bir yedeği, büyük bir dergi ve standart belgeler koleksiyonu ve gerçekten de devasa bir çizgi roman koleksiyonu gibi diğer projeleri oluşturduğunu biliyor. Bir noktada, Library Genesis aynalarının farklı operatörleri yollarını ayırdı ve bu, hala Library Genesis adını taşıyan bir dizi farklı "çatalın" ortaya çıkmasına neden oldu. Libgen.li çatalı, bu çizgi roman koleksiyonuna ve ayrıca büyük bir dergi koleksiyonuna (üzerinde de çalışıyoruz) benzersiz bir şekilde sahiptir. Bağış Kampanyası Bu veriyi büyük parçalar halinde yayımlıyoruz. İlk torrent <code>/comics0</code> içindir ve bunu devasa bir 12TB .tar dosyasına koyduk. Bu, sabit diskiniz ve torrent yazılımınız için sayısız küçük dosyadan daha iyidir. Bu yayının bir parçası olarak bir bağış kampanyası düzenliyoruz. Bu koleksiyonun operasyonel ve sözleşme maliyetlerini karşılamak ve devam eden ve gelecekteki projeleri mümkün kılmak için 20.000 $ toplamayı hedefliyoruz. Bazı <em>devasa</em> projeler üzerinde çalışıyoruz. <em>Bağışımla kimi destekliyorum?</em> Kısaca: Tüm insanlık bilgisini ve kültürünü yedekliyoruz ve bunu kolayca erişilebilir hale getiriyoruz. Tüm kodlarımız ve verilerimiz açık kaynaklıdır, tamamen gönüllüler tarafından yürütülen bir projeyiz ve şu ana kadar 125TB değerinde kitap kurtardık (Libgen ve Scihub’un mevcut torrentlerine ek olarak). Sonuçta, dünyadaki tüm kitapları bulmayı, taramayı ve yedeklemeyi sağlayan ve teşvik eden bir döngü oluşturuyoruz. Ana planımızı gelecekteki bir yazıda paylaşacağız. :) 12 aylık “Amazing Archivist” üyeliği için bağış yaparsanız ($780), bir torrent “evlat edinme” hakkına sahip olursunuz, yani kullanıcı adınızı veya mesajınızı torrentlerden birinin dosya adına koyarız! <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne gidip “Bağış Yap” butonuna tıklayarak bağış yapabilirsiniz. Ayrıca daha fazla gönüllü arıyoruz: yazılım mühendisleri, güvenlik araştırmacıları, anonim ticaret uzmanları ve çevirmenler. Bize barındırma hizmetleri sağlayarak da destek olabilirsiniz. Ve tabii ki, lütfen torrentlerimizi paylaşın! Bize zaten cömertçe destek olan herkese teşekkürler! Gerçekten fark yaratıyorsunuz. İşte şimdiye kadar yayımlanan torrentler (geri kalanını hâlâ işliyoruz): Tüm torrentler <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’nde “Datasets” altında bulunabilir (oraya doğrudan bağlantı vermiyoruz, böylece bu bloga olan bağlantılar Reddit, Twitter vb. yerlerden kaldırılmasın). Oradan, Tor web sitesine giden bağlantıyı takip edin. <a %(news_ycombinator)s>Hacker News'te Tartışın</a> Sırada ne var? Bir dizi torrent uzun vadeli koruma için harika, ancak günlük erişim için pek uygun değil. Tüm bu verileri web üzerinde almak için barındırma ortaklarıyla çalışacağız (çünkü Anna’nın Arşivi doğrudan hiçbir şey barındırmıyor). Tabii ki bu indirme bağlantılarını Anna’nın Arşivi’nde bulabileceksiniz. Herkesi bu verilerle bir şeyler yapmaya davet ediyoruz! Bize daha iyi analiz etmemize, yinelenenleri ayıklamamıza, IPFS’ye yüklememize, remix yapmamıza, AI modellerinizi bununla eğitmenize ve daha fazlasına yardımcı olun. Hepsi sizin, ve ne yapacağınızı görmek için sabırsızlanıyoruz. Son olarak, daha önce de söylediğimiz gibi, hâlâ bazı devasa yayınlarımız var (eğer <em>biri</em> <em>yanlışlıkla</em> bir <em>belirli</em> ACS4 veritabanı dökümü gönderirse, nerede bulacağınızı biliyorsunuz…), ayrıca dünyadaki tüm kitapları yedeklemek için döngü oluşturuyoruz. Bu yüzden bizi izlemeye devam edin, henüz yeni başlıyoruz. - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Çizgi romanların en büyük gölge kütüphanesi muhtemelen belirli bir Library Genesis çatalına aittir: Libgen.li. O siteyi yöneten tek yönetici, 2 milyondan fazla dosya içeren çılgın bir çizgi roman koleksiyonu toplamayı başardı, toplamda 95TB'den fazla. Ancak, diğer Library Genesis koleksiyonlarının aksine, bu koleksiyon torrentler aracılığıyla toplu olarak mevcut değildi. Bu çizgi romanlara yalnızca yavaş kişisel sunucusu üzerinden tek tek erişebiliyordunuz — tek bir arıza noktası. Bugüne kadar! Bu yazıda, bu koleksiyon hakkında ve bu çalışmayı daha fazla desteklemek için düzenlediğimiz bağış kampanyası hakkında daha fazla bilgi vereceğiz. Anna’nın Arşivi, dünyanın en büyük çizgi roman gölge kütüphanesini (95TB) yedekledi — siz de tohumlamaya yardımcı olabilirsiniz Dünyanın en büyük çizgi roman gölge kütüphanesi, bugüne kadar tek bir arıza noktasına sahipti. Uyarı: bu blog yazısı artık geçerli değildir. IPFS'nin henüz ana akım için hazır olmadığına karar verdik. Mümkün olduğunda Anna’nın Arşivi'nden IPFS üzerindeki dosyalara bağlantı vermeye devam edeceğiz, ancak artık kendimiz barındırmayacağız ve başkalarına IPFS kullanarak yansıtmayı önermiyoruz. Koleksiyonumuzu korumaya yardımcı olmak istiyorsanız, Lütfen Torrents sayfamıza bakın. 5.998.794 kitabı IPFS'ye koymak Kopyaların çoğaltılması Orijinal sorumuza geri dönersek: Koleksiyonlarımızı sonsuza kadar nasıl koruyabileceğimizi iddia edebiliriz? Buradaki ana sorun, koleksiyonumuzun <a %(torrents_stats)s>hızla büyümesidir</a>, bazı büyük koleksiyonları kazıyarak ve açık kaynak yaparak (Sci-Hub ve Library Genesis gibi diğer açık veri gölge kütüphanelerinin zaten yaptığı harika çalışmaların üzerine). Bu veri büyümesi, koleksiyonların dünya çapında yansıtılmasını zorlaştırıyor. Veri depolama pahalıdır! Ancak, özellikle aşağıdaki üç eğilimi gözlemlediğimizde iyimseriz. Koleksiyonlarımızın <a %(annas_archive_stats)s>toplam boyutu</a>, son birkaç ayda, torrent tohumlayıcılarının sayısına göre ayrılmıştır. Farklı kaynaklardan HDD fiyat eğilimleri (çalışmayı görüntülemek için tıklayın). <a %(critical_window_chinese)s>Çince versiyonu 中文版</a>, <a %(reddit)s>Reddit</a> üzerinde tartışın, <a %(news_ycombinator)s>Hacker News</a> 1. Kolay ulaşılabilir meyveleri topladık Bu, yukarıda tartışılan önceliklerimizden doğrudan takip eder. Öncelikle büyük koleksiyonları özgürleştirmeye çalışmayı tercih ediyoruz. Şimdi dünyanın en büyük koleksiyonlarından bazılarını güvence altına aldığımıza göre, büyümemizin çok daha yavaş olmasını bekliyoruz. Hala daha küçük koleksiyonların uzun bir kuyruğu var ve her gün yeni kitaplar taranıyor veya yayınlanıyor, ancak oran muhtemelen çok daha yavaş olacak. Hala boyut olarak iki katına veya hatta üç katına çıkabiliriz, ancak daha uzun bir zaman diliminde. OCR iyileştirmeleri. Öncelikler Bilim ve mühendislik yazılım kodu Yukarıdakilerin kurgusal veya eğlence versiyonları Coğrafi veriler (örneğin haritalar, jeolojik araştırmalar) Şirketlerden veya hükümetlerden gelen dahili veriler (sızıntılar) Bilimsel ölçümler, ekonomik veriler, kurumsal raporlar gibi ölçüm verileri Genel olarak metadata kayıtları (kurgu dışı ve kurgu; diğer medya, sanat, insanlar vb.; incelemeler dahil) Kurgu dışı kitaplar Kurgu dışı dergiler, gazeteler, kılavuzlar Konuşmaların, belgesellerin, podcastlerin kurgu dışı transkriptleri DNA dizileri, bitki tohumları veya mikrobiyal örnekler gibi organik veriler Akademik makaleler, dergiler, raporlar Bilim ve mühendislik web siteleri, çevrimiçi tartışmalar Hukuki veya mahkeme işlemlerinin transkriptleri Benzersiz şekilde yok olma riski altında (örneğin savaş, bütçe kesintileri, davalar veya siyasi baskı nedeniyle) Nadir Benzersiz şekilde odaklanılmamış Neden makaleler ve kitaplar hakkında bu kadar çok önemsiyoruz? Genel olarak koruma konusundaki temel inancımızı bir kenara bırakalım — bunun hakkında başka bir gönderi yazabiliriz. Peki neden özellikle makaleler ve kitaplar? Cevap basit: <strong>bilgi yoğunluğu</strong>. Depolama başına megabayt başına, yazılı metin tüm medya türleri arasında en fazla bilgiyi depolar. Hem bilgiye hem de kültüre önem veriyoruz, ancak daha çok bilgiye önem veriyoruz. Genel olarak, bilgi yoğunluğu ve korumanın önemi açısından kabaca şöyle bir hiyerarşi buluyoruz: Bu listedeki sıralama biraz keyfi — bazı maddeler eşit veya ekibimiz içinde anlaşmazlıklar var — ve muhtemelen bazı önemli kategorileri unutuyoruz. Ancak bu, önceliklerimizi kabaca nasıl belirlediğimizdir. Bu maddelerden bazıları, bizim için endişe verici derecede farklı (veya diğer kurumlar tarafından zaten ele alınmış) olduğu için, organik veriler veya coğrafi veriler gibi, çoğu madde aslında bizim için önemlidir. Önceliklerimizi belirlerken bir diğer büyük faktör, belirli bir eserin ne kadar risk altında olduğudur. Odaklanmayı tercih ettiğimiz eserler şunlardır: Son olarak, ölçek bizim için önemlidir. Sınırlı zamanımız ve paramız var, bu yüzden 10.000 kitabı kurtarmak için bir ay harcamayı, 1.000 kitabı kurtarmaktan daha çok tercih ederiz — eğer eşit derecede değerli ve risk altındalarsa. <em><q>Kayıp olan geri kazanılamaz; ama kalanları kurtaralım: onları zamanın israfına teslim ederek, kamuoyundan ve kullanımdan koruyan kasalar ve kilitlerle değil, kazaların erişemeyeceği kadar çok kopya çoğaltarak.</q></em><br>— Thomas Jefferson, 1791 Gölge kütüphaneler Kod Github'da açık kaynak olabilir, ancak Github'ın tamamı kolayca yansıtılamaz ve bu nedenle korunamaz (ancak bu özel durumda çoğu kod deposunun yeterince dağıtılmış kopyaları vardır) Metadata kayıtları Worldcat web sitesinde serbestçe görüntülenebilir, ancak toplu olarak indirilemez (biz <a %(worldcat_scrape)s>kazıyana</a> kadar) Reddit kullanımı ücretsizdir, ancak son zamanlarda veri aç LLM eğitimi nedeniyle katı anti-kazıma önlemleri aldı (daha sonra bu konuda daha fazla bilgi) Benzer misyonlara ve önceliklere sahip birçok kuruluş var. Gerçekten de, bu tür koruma görevleri olan kütüphaneler, arşivler, laboratuvarlar, müzeler ve diğer kurumlar var. Bunların birçoğu, hükümetler, bireyler veya şirketler tarafından iyi finanse edilmektedir. Ancak büyük bir kör noktaları var: hukuk sistemi. Burada gölge kütüphanelerin benzersiz rolü ve Anna’nın Arşivi’nin var olma nedeni yatıyor. Diğer kurumların yapmasına izin verilmeyen şeyleri yapabiliriz. Şimdi, başka yerlerde korunması yasadışı olan materyalleri arşivleyebileceğimizden değil (genellikle). Hayır, birçok yerde herhangi bir kitap, makale, dergi vb. ile bir arşiv oluşturmak yasaldır. Ancak, yasal arşivlerin genellikle eksik olduğu şey <strong>yedeklilik ve uzun ömürlülüktür</strong>. Bazı fiziksel kütüphanelerde yalnızca bir kopyası bulunan kitaplar vardır. Tek bir şirket tarafından korunan metadata kayıtları vardır. Sadece tek bir arşivde mikrofilm üzerinde korunan gazeteler vardır. Kütüphaneler bütçe kesintileri alabilir, şirketler iflas edebilir, arşivler bombalanabilir ve yakılıp yok edilebilir. Bu varsayımsal bir durum değil — bu her zaman olur. Anna’nın Arşivi’nde benzersiz bir şekilde yapabileceğimiz şey, eserlerin birçok kopyasını büyük ölçekte depolamaktır. Makaleler, kitaplar, dergiler ve daha fazlasını toplayabilir ve toplu olarak dağıtabiliriz. Şu anda bunu torrentler aracılığıyla yapıyoruz, ancak kullanılan teknolojilerin önemi yoktur ve zamanla değişecektir. Önemli olan, birçok kopyanın dünya çapında dağıtılmasıdır. 200 yıldan fazla bir süre önce söylenen bu alıntı hala geçerliliğini koruyor: Kamu malı hakkında kısa bir not. Anna’nın Arşivi, dünya genelinde birçok yerde yasadışı olan faaliyetlere benzersiz bir şekilde odaklandığı için, kamu malı kitaplar gibi yaygın olarak bulunan koleksiyonlarla ilgilenmiyoruz. Yasal kuruluşlar genellikle bununla zaten iyi ilgilenir. Ancak, bazen kamuya açık koleksiyonlar üzerinde çalışmamızı gerektiren durumlar vardır: - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Depolama maliyetleri üstel olarak düşmeye devam ediyor 3. Bilgi yoğunluğunda iyileştirmeler Şu anda kitapları bize verildiği ham formatlarda saklıyoruz. Elbette sıkıştırılmış durumdalar, ancak genellikle hala büyük taramalar veya sayfa fotoğraflarıdır. Şimdiye kadar, koleksiyonumuzun toplam boyutunu küçültmenin tek seçenekleri daha agresif sıkıştırma veya yinelenenleri kaldırma olmuştur. Ancak, yeterince önemli tasarruflar elde etmek için her ikisi de bizim için fazla kayıplı. Fotoğrafların ağır sıkıştırılması metni neredeyse okunamaz hale getirebilir. Ve yinelenenleri kaldırmak, kitapların tam olarak aynı olduğuna dair yüksek bir güven gerektirir, bu da genellikle çok yanlış olur, özellikle içerikler aynıysa ancak taramalar farklı zamanlarda yapılmışsa. Her zaman üçüncü bir seçenek vardı, ancak kalitesi o kadar kötüydü ki hiç düşünmedik: <strong>OCR veya Optik Karakter Tanıma</strong>. Bu, fotoğrafları düz metne dönüştürme sürecidir, fotoğraflardaki karakterleri algılamak için yapay zeka kullanarak. Bunun için araçlar uzun zamandır var ve oldukça iyi, ancak "oldukça iyi" koruma amaçları için yeterli değil. Ancak, son zamanlardaki çok modlu derin öğrenme modelleri son derece hızlı ilerleme kaydetti, ancak hala yüksek maliyetlerle. Önümüzdeki yıllarda hem doğruluğun hem de maliyetlerin dramatik bir şekilde iyileşmesini bekliyoruz, bu noktada tüm kütüphanemize uygulamak gerçekçi hale gelecek. Bu gerçekleştiğinde, muhtemelen orijinal dosyaları yine de saklayacağız, ancak ek olarak çoğu kişinin yansıtmak isteyeceği çok daha küçük bir kütüphane versiyonuna sahip olabiliriz. İşin püf noktası, ham metnin kendisinin daha da iyi sıkıştırılması ve yinelenenlerin daha kolay kaldırılması, bize daha fazla tasarruf sağlamasıdır. Genel olarak, toplam dosya boyutunda en az 5-10 kat azalma beklemek gerçekçi değil, belki daha da fazla. Muhafazakar bir 5 kat azalma ile bile, kütüphanemiz üç katına çıksa bile 10 yıl içinde <strong>1.000–3.000 $ arasında</strong> bir maliyetle karşılaşacağız. Yazının yazıldığı sırada, <a %(diskprices)s>disk fiyatları</a> TB başına yeni diskler için yaklaşık 12$, kullanılmış diskler için 8$ ve teyp için 4$ civarındadır. Sadece yeni diskleri dikkate alırsak, bir petabayt depolamanın maliyeti yaklaşık 12.000$'dır. Kütüphanemizin 900TB'den 2.7PB'ye üç katına çıkacağını varsayarsak, tüm kütüphanemizi yansıtmak 32.400$'a mal olacaktır. Elektrik, diğer donanım maliyetleri vb. ekleyerek, bunu 40.000$'a yuvarlayalım. Veya teyp ile daha çok 15.000$–20.000$. Bir yandan <strong>tüm insan bilgisinin toplamı için 15.000$–40.000$ bir fırsat</strong>. Öte yandan, özellikle bu kişilerin başkalarının yararına torrentlerini tohumlamaya devam etmelerini de istiyorsak, tonlarca tam kopya beklemek biraz pahalı. Bu bugün. Ancak ilerleme devam ediyor: Sabit disk maliyetleri TB başına son 10 yılda kabaca üçte bir oranında azaldı ve muhtemelen benzer bir hızda düşmeye devam edecek. Teyp de benzer bir yörüngede görünüyor. SSD fiyatları daha da hızlı düşüyor ve on yılın sonunda HDD fiyatlarını geçebilir. Eğer bu devam ederse, 10 yıl içinde tüm koleksiyonumuzu yansıtmak için sadece 5.000$–13.000$'a (1/3) bakıyor olabiliriz, veya boyut olarak daha az büyürsek daha da az. Hala çok para olsa da, bu birçok kişi için ulaşılabilir olacak. Ve bir sonraki noktadan dolayı daha da iyi olabilir… Anna’nın Arşivi'nde, koleksiyonlarımızı sonsuza kadar koruyabileceğimizi nasıl iddia edebileceğimiz sıkça soruluyor, toplam boyut zaten 1 Petabayt'a (1000 TB) yaklaşıyor ve hala büyüyor. Bu makalede felsefemize bakacağız ve insanlığın bilgi ve kültürünü koruma misyonumuz için neden önümüzdeki on yılın kritik olduğunu göreceğiz. Kritik pencere Bu tahminler doğruysa, <strong>sadece birkaç yıl beklememiz gerekecek</strong> ve tüm koleksiyonumuz geniş çapta yansıtılacak. Böylece, Thomas Jefferson'un sözleriyle, "kazaların erişemeyeceği bir yere" yerleştirilmiş olacak. Ne yazık ki, LLM'lerin ortaya çıkışı ve veri açlığı eğitimi, birçok telif hakkı sahibini savunmaya geçirdi. Zaten olduklarından daha fazla. Birçok web sitesi kazıma ve arşivlemeyi zorlaştırıyor, davalar havada uçuşuyor ve bu arada fiziksel kütüphaneler ve arşivler ihmal edilmeye devam ediyor. Bu eğilimlerin kötüleşmeye devam etmesini ve birçok eserin kamu malı olmadan çok önce kaybolmasını bekleyebiliriz. <strong>Koruma konusunda bir devrimin eşiğindeyiz, ancak <q>kaybedilen geri kazanılamaz.</q></strong> Gölge kütüphane işletmenin ve dünya çapında birçok yansıtma oluşturmanın hala oldukça pahalı olduğu ve erişimin henüz tamamen kapatılmadığı yaklaşık 5-10 yıllık kritik bir pencereye sahibiz. Bu pencereyi aşabilirsek, insanlığın bilgi ve kültürünü sonsuza dek korumuş olacağız. Bu zamanı boşa harcamamalıyız. Bu kritik pencerenin üzerimize kapanmasına izin vermemeliyiz. Hadi başlayalım. Gölge kütüphanelerin kritik penceresi Koleksiyonlarımızı sonsuza kadar koruyabileceğimizi nasıl iddia edebiliriz, zaten 1 PB'ye yaklaşıyorlarsa? Koleksiyon Koleksiyon hakkında daha fazla bilgi. <a %(duxiu)s>Duxiu</a>, <a %(chaoxing)s>SuperStar Dijital Kütüphane Grubu</a> tarafından oluşturulan taranmış kitapların devasa bir veritabanıdır. Çoğu akademik kitaptır ve üniversitelere ve kütüphanelere dijital olarak sunulmak üzere taranmıştır. İngilizce konuşan izleyicilerimiz için, <a %(library_princeton)s>Princeton</a> ve <a %(guides_lib_uw)s>Washington Üniversitesi</a> iyi genel bakışlar sunmaktadır. Ayrıca daha fazla arka plan bilgisi veren mükemmel bir makale de bulunmaktadır: <a %(doi)s>“Çin Kitaplarını Dijitalleştirme: SuperStar DuXiu Scholar Arama Motoru Üzerine Bir Vaka Çalışması”</a> (Anna’nın Arşivi'nde arayın). Duxiu'dan gelen kitaplar uzun süredir Çin internetinde korsan olarak dağıtılmaktadır. Genellikle satıcılar tarafından bir dolardan daha ucuza satılmaktadırlar. Genellikle daha fazla depolama alanı sağlamak için hacklenmiş olan Google Drive'ın Çin eşdeğeri kullanılarak dağıtılmaktadırlar. Bazı teknik detaylar <a %(github_duty_machine)s>burada</a> ve <a %(github_821_github_io)s>burada</a> bulunabilir. Kitaplar yarı kamuya açık bir şekilde dağıtılmış olmasına rağmen, toplu olarak elde etmek oldukça zordur. Bunu yapılacaklar listemizin en üstüne koyduk ve bunun için tam zamanlı çalışmaya birkaç ay ayırdık. Ancak, yakın zamanda inanılmaz, harika ve yetenekli bir gönüllü bize ulaştı ve tüm bu çalışmayı zaten büyük bir maliyetle yaptıklarını söyledi. Koleksiyonun tamamını bizimle paylaştılar, karşılığında hiçbir şey beklemeden, sadece uzun vadeli koruma garantisi dışında. Gerçekten olağanüstü. Koleksiyonun OCR yapılması için bu şekilde yardım istemeyi kabul ettiler. Koleksiyon 7.543.702 dosyadan oluşmaktadır. Bu, Library Genesis'in kurgusal olmayan kitaplarından (yaklaşık 5.3 milyon) daha fazladır. Toplam dosya boyutu, mevcut haliyle yaklaşık 359TB (326TiB) kadardır. Başka önerilere ve fikirlere açığız. Sadece bizimle iletişime geçin. Koleksiyonlarımız, koruma çabalarımız ve nasıl yardımcı olabileceğiniz hakkında daha fazla bilgi için Anna’nın Arşivi'ni inceleyin. Teşekkürler! Örnek sayfalar Bize iyi bir boru hattınız olduğunu kanıtlamak için, süper iletkenler hakkında bir kitaptan başlamak üzere bazı örnek sayfalar burada. Boru hattınız matematik, tablolar, grafikler, dipnotlar ve benzeri şeyleri düzgün bir şekilde ele almalıdır. İşlenmiş sayfalarınızı e-posta adresimize gönderin. İyi görünürlerse, size özel olarak daha fazlasını göndereceğiz ve bu sayfalarda da boru hattınızı hızlı bir şekilde çalıştırabilmenizi bekliyoruz. Tatmin olduğumuzda, bir anlaşma yapabiliriz. - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Çince versiyon 中文版</a>, <a %(news_ycombinator)s>Hacker News'te Tartış</a> Bu kısa bir blog yazısıdır. Edindiğimiz devasa koleksiyon için OCR ve metin çıkarımı konusunda bize yardımcı olacak bir şirket veya kurum arıyoruz, karşılığında özel erken erişim sağlanacaktır. Ambargo süresi sona erdikten sonra, elbette tüm koleksiyonu yayınlayacağız. Yüksek kaliteli akademik metinler, LLM'lerin eğitimi için son derece faydalıdır. Koleksiyonumuz Çincedir, ancak bu, İngilizce LLM'lerin eğitimi için bile faydalı olabilir: Modeller, kaynak dilden bağımsız olarak kavramları ve bilgiyi kodluyor gibi görünüyor. Bunun için, metnin taramalardan çıkarılması gerekiyor. Anna’nın Arşivi bundan ne elde ediyor? Kullanıcıları için kitapların tam metin araması. Hedeflerimiz LLM geliştiricileriyle örtüştüğü için bir işbirlikçi arıyoruz. Eğer doğru OCR ve metin çıkarımı yapabilirseniz, size bu koleksiyona toplu olarak 1 yıl boyunca <strong>özel erken erişim</strong> vermeye hazırız. Eğer tüm boru hattı kodunuzu bizimle paylaşmaya istekliyseniz, koleksiyonu daha uzun süre ambargo altına alabiliriz. Dünyanın en büyük Çin kurgusal olmayan kitap koleksiyonuna LLM şirketleri için özel erişim <em><strong>Özet:</strong> Anna’nın Arşivi, Library Genesis'ten daha büyük, 7,5 milyon / 350TB Çin kurgusal olmayan kitaplardan oluşan benzersiz bir koleksiyon edindi. Yüksek kaliteli OCR ve metin çıkarımı karşılığında bir LLM şirketine özel erişim vermeye istekliyiz.</em> Sistem mimarisi Diyelim ki web sitenizi kapatmadan barındırmaya istekli bazı şirketler buldunuz — bunlara “özgürlüksever sağlayıcılar” diyelim 😄. Onlarla her şeyi barındırmanın oldukça pahalı olduğunu çabucak fark edeceksiniz, bu yüzden bazı “ucuz sağlayıcılar” bulmak ve gerçek barındırmayı orada yapmak isteyebilirsiniz, özgürlüksever sağlayıcılar aracılığıyla proxy yaparak. Doğru yaparsanız, ucuz sağlayıcılar neyi barındırdığınızı asla bilmeyecek ve asla şikayet almayacaklar. Tüm bu sağlayıcılarla, yine de sizi kapatma riski vardır, bu yüzden yedekliliğe de ihtiyacınız var. Yığınımızın tüm seviyelerinde buna ihtiyacımız var. Kendini ilginç bir konuma koymuş olan bir özgürlüksever şirket Cloudflare'dir. <a %(blog_cloudflare)s>İddia ettiler</a> ki bir barındırma sağlayıcısı değil, bir hizmet sağlayıcı, bir ISP gibi. Bu nedenle DMCA veya diğer kaldırma taleplerine tabi değiller ve talepleri gerçek barındırma sağlayıcınıza yönlendiriyorlar. Bu yapıyı korumak için mahkemeye gitmeye kadar gittiler. Bu nedenle onları başka bir önbellekleme ve koruma katmanı olarak kullanabiliriz. Cloudflare anonim ödemeleri kabul etmez, bu yüzden yalnızca ücretsiz planlarını kullanabiliriz. Bu, yük dengeleme veya failover özelliklerini kullanamayacağımız anlamına gelir. Bu nedenle <a %(annas_archive_l255)s>bunu kendimiz uyguladık</a> alan adı düzeyinde. Sayfa yüklendiğinde, tarayıcı mevcut alan adının hala kullanılabilir olup olmadığını kontrol eder ve değilse, tüm URL'leri farklı bir alan adına yeniden yazar. Cloudflare birçok sayfayı önbelleğe aldığı için, kullanıcı ana alan adımıza inebilir, proxy sunucusu kapalı olsa bile, ve ardından bir sonraki tıklamada başka bir alan adına taşınabilir. Ayrıca, sunucu sağlığını izleme, arka uç ve ön uç hatalarını kaydetme gibi normal operasyonel endişelerle de ilgilenmemiz gerekiyor. Failover mimarimiz, örneğin alan adlarından birinde tamamen farklı bir sunucu seti çalıştırarak bu cephede daha fazla sağlamlık sağlar. Ana sürümdeki kritik bir hata fark edilmeden kalırsa, bu ayrı alanda kodun ve verisetlerinin eski sürümlerini bile çalıştırabiliriz. Cloudflare'ın bize karşı dönmesine karşı da önlem alabiliriz, bu ayrı alan adı gibi bir alan adından kaldırarak. Bu fikirlerin farklı permütasyonları mümkündür. Sonuç Sağlam ve dayanıklı bir gölge kütüphane arama motoru kurmayı öğrenmek ilginç bir deneyim oldu. Daha sonra paylaşılacak tonlarca detay var, bu yüzden daha fazla ne öğrenmek istediğinizi bana bildirin! Her zaman olduğu gibi, bu çalışmayı desteklemek için bağış arıyoruz, bu yüzden Anna’nın Arşivi'ndeki Bağış sayfasını mutlaka kontrol edin. Ayrıca hibe, uzun vadeli sponsorlar, yüksek riskli ödeme sağlayıcıları, belki de (zevkli!) reklamlar gibi diğer destek türlerini de arıyoruz. Zamanınızı ve becerilerinizi katkıda bulunmak isterseniz, her zaman geliştiriciler, çevirmenler vb. arıyoruz. İlginiz ve desteğiniz için teşekkürler. Yenilik jetonları Teknoloji yığınımızla başlayalım. Bilerek sıkıcı. Flask, MariaDB ve ElasticSearch kullanıyoruz. Kelimenin tam anlamıyla bu kadar. Arama büyük ölçüde çözülmüş bir sorun ve onu yeniden icat etmeyi düşünmüyoruz. Ayrıca, <a %(mcfunley)s>yenilik jetonlarımızı</a> başka bir şeye harcamamız gerekiyor: yetkililer tarafından kapatılmamak. Peki Anna’nın Arşivi ne kadar yasal veya yasa dışı? Bu büyük ölçüde yasal yargı yetkisine bağlıdır. Çoğu ülke bir tür telif hakkına inanır, bu da belirli türdeki eserler üzerinde belirli bir süre için kişilere veya şirketlere münhasır bir tekel verildiği anlamına gelir. Yan not olarak, Anna’nın Arşivi'nde bazı faydalar olmasına rağmen, genel olarak telif hakkının toplum için net bir olumsuzluk olduğuna inanıyoruz — ama bu başka bir zamanın hikayesi. Belirli eserler üzerindeki bu münhasır tekel, bu tekelin dışındaki herhangi birinin bu eserleri doğrudan dağıtmasının yasa dışı olduğu anlamına gelir — biz de dahil. Ancak Anna’nın Arşivi, bu eserleri doğrudan dağıtmayan bir arama motorudur (en azından açık ağ sitemizde değil), bu yüzden sorun olmamalı, değil mi? Tam olarak değil. Birçok yargı alanında, telif hakkıyla korunan eserleri dağıtmak yasa dışı olduğu gibi, bu tür eserlere bağlantı vermek de yasa dışıdır. Bunun klasik bir örneği Amerika Birleşik Devletleri’nin DMCA yasasıdır. Bu spektrumun en katı ucudur. Spektrumun diğer ucunda teorik olarak hiç telif hakkı yasası olmayan ülkeler olabilir, ancak bunlar gerçekten yoktur. Hemen hemen her ülkenin kitaplarında bir tür telif hakkı yasası vardır. Uygulama farklı bir hikayedir. Telif hakkı yasasını uygulamakla ilgilenmeyen hükümetlere sahip birçok ülke vardır. Ayrıca, telif hakkıyla korunan eserlerin dağıtılmasını yasaklayan, ancak bu tür eserlere bağlantı vermeyi yasaklamayan iki uç arasında ülkeler de vardır. Bir diğer husus şirket düzeyindedir. Bir şirket, telif hakkıyla ilgilenmeyen bir yargı alanında faaliyet gösteriyorsa, ancak şirket kendisi herhangi bir risk almak istemiyorsa, birisi şikayet ettiği anda web sitenizi kapatabilirler. Son olarak, büyük bir husus ödemelerdir. Anonim kalmamız gerektiğinden, geleneksel ödeme yöntemlerini kullanamayız. Bu da bizi kripto para birimlerine bırakıyor ve yalnızca küçük bir şirket alt kümesi bunları destekliyor (kripto ile ödenen sanal banka kartları var, ancak genellikle kabul edilmiyorlar). - Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ni, Sci-Hub, Library Genesis ve Z-Library gibi <a %(wikipedia_shadow_library)s>gölge kütüphaneler</a> için dünyanın en büyük açık kaynaklı kâr amacı gütmeyen arama motorunu işletiyorum. Amacımız, bilgi ve kültürü kolayca erişilebilir hale getirmek ve nihayetinde <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>dünyadaki tüm kitapları</a> arşivleyen ve koruyan bir topluluk oluşturmaktır. Bu makalede, bu web sitesini nasıl çalıştırdığımızı ve yasal durumu şüpheli bir web sitesi işletmenin getirdiği benzersiz zorlukları göstereceğim, çünkü gölge hayır kurumları için bir “AWS” yok. <em>Kardeş makale <a %(blog_how_to_become_a_pirate_archivist)s>Nasıl korsan arşivci olunur</a> makalesine de göz atın.</em> Bir gölge kütüphane nasıl çalıştırılır: Anna’nın Arşivi'nde operasyonlar Gölge hayır kurumları için <q>AWS yok,</q> peki Anna’nın Arşivi'ni nasıl çalıştırıyoruz? Araçlar Uygulama sunucusu: Flask, MariaDB, ElasticSearch, Docker. Geliştirme: Gitlab, Weblate, Zulip. Sunucu yönetimi: Ansible, Checkmk, UFW. Soğan statik barındırma: Tor, Nginx. Proxy sunucusu: Varnish. Bütün bunları başarmak için hangi araçları kullandığımıza bir bakalım. Bu, yeni sorunlarla karşılaştıkça ve yeni çözümler buldukça çok gelişiyor. Bazı kararlar üzerinde gidip geldik. Bunlardan biri sunucular arasındaki iletişim: Bunun için eskiden Wireguard kullanıyorduk, ancak bazen veri iletimini tamamen durdurduğunu veya verileri yalnızca tek yönlü ilettiğini fark ettik. Bu, denediğimiz birkaç farklı Wireguard kurulumunda, örneğin <a %(github_costela_wesher)s>wesher</a> ve <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a> ile oldu. Ayrıca autossh ve sshuttle kullanarak SSH üzerinden port tünelleme denedik, ancak <a %(github_sshuttle)s>orada sorunlarla</a> karşılaştık (autossh'un TCP-over-TCP sorunlarından muzdarip olup olmadığı hala net değil — bana biraz garip bir çözüm gibi geliyor ama belki de aslında sorun yoktur?). Bunun yerine, sunucular arasında doğrudan bağlantılara geri döndük ve ucuz sağlayıcılarda çalışan bir sunucuyu IP filtreleme ile UFW kullanarak gizledik. Bunun dezavantajı, Docker'ın UFW ile iyi çalışmaması, <code>network_mode: "host"</code> kullanmadığınız sürece. Tüm bunlar biraz daha hataya açık, çünkü sadece küçük bir yanlış yapılandırma ile sunucunuzu internete maruz bırakabilirsiniz. Belki de autossh'a geri dönmeliyiz — burada geri bildirim çok memnuniyetle karşılanır. Varnish ve Nginx arasında da gidip geldik. Şu anda Varnish'i seviyoruz, ancak bazı tuhaflıkları ve pürüzlü kenarları var. Aynı şey Checkmk için de geçerli: onu sevmiyoruz ama şimdilik işe yarıyor. Weblate fena değil ama harika da değil — onu git repo'muzla senkronize etmeye çalıştığımda verilerimi kaybedeceğinden bazen korkuyorum. Flask genel olarak iyi oldu, ancak özel alan adlarını yapılandırmak veya SqlAlchemy entegrasyonu ile ilgili sorunlar gibi bazı tuhaflıklar çok zaman kaybettirdi. Şu ana kadar diğer araçlar harika oldu: MariaDB, ElasticSearch, Gitlab, Zulip, Docker ve Tor hakkında ciddi bir şikayetimiz yok. Bunların hepsinde bazı sorunlar yaşadık, ancak hiçbir şey aşırı derecede ciddi veya zaman alıcı değildi. Topluluk İlk zorluk şaşırtıcı olabilir. Bu ne teknik bir sorun ne de yasal bir sorun. Bu psikolojik bir sorun: bu işi gölgelerde yapmak inanılmaz derecede yalnız olabilir. Ne yapmayı planladığınıza ve tehdit modelinize bağlı olarak çok dikkatli olmanız gerekebilir. Spektrumun bir ucunda, faaliyetleri hakkında çok açık olan Sci-Hub'un kurucusu Alexandra Elbakyan* gibi insanlar var. Ancak şu anda bir batı ülkesini ziyaret ederse tutuklanma riski yüksek ve onlarca yıl hapis cezasıyla karşı karşıya kalabilir. Bu riski almaya istekli olur muydunuz? Biz spektrumun diğer ucundayız; iz bırakmamaya çok dikkat ediyoruz ve güçlü operasyonel güvenliğe sahibiz. * HN'de "ynno" tarafından belirtildiği gibi, Alexandra başlangıçta tanınmak istemiyordu: "Sunucuları, PHP'den ayrıntılı hata mesajları yayacak şekilde ayarlanmıştı, bu da hatalı kaynak dosyasının tam yolunu içeriyordu, bu da çevrimiçi bir sitede kullanıcı adı olarak izlenebilecek bir dizin altındaydı, gerçek adıyla ilişkilendirilmişti. Bu ifşadan önce anonim kalmıştı." Bu yüzden, bu tür şeyler için kullandığınız bilgisayarlarda rastgele kullanıcı adları kullanın, yanlış bir yapılandırma yapmanız durumunda. Ancak bu gizlilik, psikolojik bir maliyetle gelir. Çoğu insan yaptığı iş için tanınmayı sever, ancak gerçek hayatta bunun için herhangi bir kredi alamazsınız. Arkadaşlarınızın size ne yaptığınızı sorması gibi basit şeyler bile zorlayıcı olabilir (bir noktada "NAS / homelab ile uğraşıyorum" demek eskir). Bu yüzden bir topluluk bulmak çok önemlidir. Çok yakın arkadaşlarınıza güvenerek bazı operasyonel güvenlikten vazgeçebilirsiniz, derinlemesine güvenebileceğinizi bildiğiniz. Yine de, yazılı bir şey koymamaya dikkat edin, çünkü yetkililere e-postalarını teslim etmeleri gerekebilir veya cihazları başka bir şekilde tehlikeye girebilir. Daha iyisi, bazı korsan arkadaşlar bulmaktır. Yakın arkadaşlarınız size katılmakla ilgileniyorsa, harika! Aksi takdirde, çevrimiçi olarak başkalarını bulabilirsiniz. Ne yazık ki, bu hala niş bir topluluk. Şu ana kadar bu alanda aktif olan sadece bir avuç insan bulduk. İyi başlangıç noktaları Library Genesis forumları ve r/DataHoarder gibi görünüyor. Arşiv Ekibi de benzer düşünen bireyler içeriyor, ancak onlar yasalara uygun olarak çalışıyorlar (yasanın gri alanlarında olsa bile). Geleneksel "warez" ve korsan sahneleri de benzer şekilde düşünen insanlara sahiptir. Topluluğu geliştirmek ve fikirleri keşfetmek için fikirlere açığız. Bize Twitter veya Reddit üzerinden mesaj atmaktan çekinmeyin. Belki bir forum veya sohbet grubu düzenleyebiliriz. Bir zorluk, yaygın platformlar kullanıldığında bunun kolayca sansürlenebilmesidir, bu yüzden bunu kendimiz barındırmamız gerekecek. Bu tartışmaları tamamen halka açık yapmak (daha fazla potansiyel etkileşim) ile özel hale getirmek (potansiyel "hedeflerin" onları kazıyacağımızı bilmemesi) arasında bir denge var. Bunu düşünmemiz gerekecek. Bu konuda ilgileniyorsanız bize bildirin! Sonuç Umarız bu, yeni başlayan korsan arşivciler için faydalı olur. Sizi bu dünyaya davet etmekten heyecan duyuyoruz, bu yüzden bizimle iletişime geçmekten çekinmeyin. Dünyanın bilgisini ve kültürünü olabildiğince koruyalım ve geniş çapta yansıtalım. Projeler 4. Veri seçimi Çoğu zaman, indirilecek makul bir veri alt kümesini belirlemek için metadataları kullanabilirsiniz. Sonunda tüm verileri indirmek isteseniz bile, tespit edilme ve savunmaların güçlendirilmesi durumunda veya daha fazla disk satın almanız gerektiği için ya da hayatınızda başka bir şey ortaya çıkmadan önce en önemli öğeleri önceliklendirmeniz faydalı olabilir. Örneğin, bir koleksiyon aynı temel kaynağın (bir kitap veya film gibi) birden fazla baskısına sahip olabilir ve bunlardan biri en iyi kalite olarak işaretlenmiş olabilir. Öncelikle bu baskıları kaydetmek mantıklı olacaktır. Sonunda tüm baskıları kaydetmek isteyebilirsiniz, çünkü bazı durumlarda metadata yanlış etiketlenmiş olabilir veya baskılar arasında bilinmeyen ödünleşimler olabilir (örneğin, "en iyi baskı" çoğu açıdan en iyi olabilir ancak diğer açılardan daha kötü olabilir, örneğin bir film daha yüksek çözünürlükte olabilir ancak altyazıları eksik olabilir). Metadata veritabanınızı ilginç şeyler bulmak için de arayabilirsiniz. Barındırılan en büyük dosya nedir ve neden bu kadar büyük? En küçük dosya nedir? Belirli kategoriler, diller vb. söz konusu olduğunda ilginç veya beklenmedik desenler var mı? Çift veya çok benzer başlıklar var mı? Verilerin ne zaman eklendiğine dair desenler var mı, örneğin bir günde birçok dosyanın birden eklendiği gibi? Verisetine farklı açılardan bakarak genellikle çok şey öğrenebilirsiniz. Bizim durumumuzda, Z-Library kitaplarını Library Genesis'teki md5 hash'lerine karşı deduplikasyon yaparak, çok fazla indirme süresi ve disk alanı tasarrufu sağladık. Ancak bu oldukça benzersiz bir durum. Çoğu durumda, hangi dosyaların zaten diğer korsanlar tarafından düzgün bir şekilde korunduğuna dair kapsamlı veritabanları yoktur. Bu, birileri için büyük bir fırsattır. Torrent sitelerinde zaten yaygın olarak tohumlanan müzik ve filmler gibi şeylerin düzenli olarak güncellenen bir genel bakışına sahip olmak harika olurdu ve bu nedenle korsan yansıtmalara dahil edilmesi daha düşük öncelikli olurdu. 6. Dağıtım Verilere sahipsiniz, böylece muhtemelen hedefinizin dünyanın ilk korsan yansıtmasını elde ettiniz. Birçok yönden en zor kısım geride kaldı, ancak en riskli kısım hala önünüzde. Sonuçta, şimdiye kadar gizliydiniz; radarın altında uçuyordunuz. Tek yapmanız gereken, iyi bir VPN kullanmak, herhangi bir formda kişisel bilgilerinizi doldurmamak (tabii ki) ve belki de özel bir tarayıcı oturumu (veya hatta farklı bir bilgisayar) kullanmaktı. Şimdi verileri dağıtmanız gerekiyor. Bizim durumumuzda, kitapları önce Library Genesis'e geri katkıda bulunmak istedik, ancak bunun zorluklarını (kurgu ve kurgu dışı sıralama) hızla keşfettik. Bu yüzden Library Genesis tarzı torrentler kullanarak dağıtıma karar verdik. Mevcut bir projeye katkıda bulunma fırsatınız varsa, bu size çok zaman kazandırabilir. Ancak, şu anda iyi organize edilmiş çok fazla korsan yansıtma yok. Diyelim ki torrentleri kendiniz dağıtmaya karar verdiniz. Bu dosyaları küçük tutmaya çalışın, böylece diğer web sitelerinde kolayca yansıtılabilirler. Torrentleri kendiniz tohumlamanız gerekecek, ancak anonim kalmaya devam ederken. Bir VPN (port yönlendirmeli veya yönlendirmesiz) kullanabilir veya bir Seedbox için karıştırılmış Bitcoinlerle ödeme yapabilirsiniz. Bu terimlerin bazılarının ne anlama geldiğini bilmiyorsanız, burada risk ödünleşimlerini anlamanız önemli olduğu için bir dizi okuma yapmanız gerekecek. Torrent dosyalarını mevcut torrent sitelerinde barındırabilirsiniz. Bizim durumumuzda, aslında bir web sitesi barındırmayı seçtik, çünkü felsefemizi net bir şekilde yaymak istedik. Bunu benzer bir şekilde kendiniz yapabilirsiniz (alan adlarımız ve barındırmamız için Njalla kullanıyoruz, karıştırılmış Bitcoinlerle ödeniyor), ancak torrentlerinizi barındırmamız için bizimle iletişime geçmekten çekinmeyin. Bu fikir tutarsa, zamanla kapsamlı bir korsan yansıtma dizini oluşturmayı hedefliyoruz. VPN seçimi konusunda, bu konuda zaten çok şey yazılmıştır, bu yüzden sadece itibara göre seçim yapma genel tavsiyesini tekrarlayacağız. Mahkemede test edilmiş, uzun süreli gizlilik koruma geçmişine sahip gerçek kayıt tutmama politikaları, bizim görüşümüze göre en düşük riskli seçenektir. Her şeyi doğru yapsanız bile, sıfır risk seviyesine ulaşamayacağınızı unutmayın. Örneğin, torrentlerinizi tohumlarken, son derece motive olmuş bir ulus-devlet aktörü muhtemelen VPN sunucuları için gelen ve giden veri akışlarına bakabilir ve kim olduğunuzu çıkarabilir. Ya da bir şekilde basitçe hata yapabilirsiniz. Muhtemelen biz zaten yaptık ve tekrar yapacağız. Neyse ki, ulus devletler korsanlıkla <em>o kadar</em> ilgilenmiyor. Her proje için bir karar vermeniz gerekiyor, daha önceki kimlikle mi yayınlayacaksınız yoksa değil mi. Aynı ismi kullanmaya devam ederseniz, önceki projelerdeki operasyonel güvenlik hataları geri dönüp sizi ısırabilir. Ancak farklı isimler altında yayınlamak, daha uzun süreli bir itibar oluşturamayacağınız anlamına gelir. Başlangıçtan itibaren güçlü bir operasyonel güvenliğe sahip olmayı seçtik, böylece aynı kimliği kullanmaya devam edebiliriz, ancak hata yaparsak veya koşullar gerektirirse farklı bir isim altında yayınlamaktan çekinmeyeceğiz. Sözü yaymak zor olabilir. Dediğimiz gibi, bu hala niş bir topluluk. Başlangıçta Reddit'te yayınladık, ancak gerçekten Hacker News'te ilgi gördük. Şimdilik önerimiz, birkaç yerde yayınlamak ve ne olacağını görmek. Ve tekrar, bizimle iletişime geçin. Daha fazla korsan arşivcilik çabalarının sözünü yaymayı çok isteriz. 1. Alan seçimi / felsefe Kurtarılacak bilgi ve kültürel miras sıkıntısı yoktur, bu da bunaltıcı olabilir. Bu yüzden, katkınızın ne olabileceğini düşünmek için bir an durmak genellikle faydalıdır. Herkesin bu konuda farklı bir düşünme şekli vardır, ancak kendinize sorabileceğiniz bazı sorular şunlardır: Bizim durumumuzda, özellikle bilimin uzun vadeli korunmasına önem verdik. Library Genesis hakkında bilgi sahibiydik ve bunun torrentler kullanılarak birçok kez tamamen yansıtıldığını biliyorduk. Bu fikri çok sevdik. Sonra bir gün, birimiz Library Genesis'te bazı bilimsel ders kitaplarını bulmaya çalıştı, ancak bulamadı, bu da ne kadar eksiksiz olduğunu sorgulattı. Daha sonra bu ders kitaplarını çevrimiçi aradık ve başka yerlerde bulduk, bu da projemizin tohumunu ekti. Z-Library hakkında bilgi sahibi olmadan önce bile, tüm bu kitapları manuel olarak toplamaya çalışmak yerine, mevcut koleksiyonları yansıtıp Library Genesis'e geri katkıda bulunma fikrimiz vardı. Kendi yararınıza kullanabileceğiniz hangi becerilere sahipsiniz? Örneğin, çevrimiçi güvenlik uzmanıysanız, güvenli hedefler için IP bloklarını aşmanın yollarını bulabilirsiniz. Toplulukları organize etmede harikaysanız, belki de bir hedef etrafında bazı insanları bir araya getirebilirsiniz. Ancak, bu süreç boyunca iyi bir operasyonel güvenliği sürdürmek için biraz programlama bilmek faydalıdır. Odaklanmak için yüksek kaldıraçlı bir alan ne olurdu? Korsan arşivlemeye X saat harcayacaksanız, en büyük "karşılığını" nasıl alabilirsiniz? Bu konuda düşündüğünüz benzersiz yollar nelerdir? Başkalarının kaçırmış olabileceği ilginç fikirler veya yaklaşımlarınız olabilir. Buna ne kadar zaman ayırabilirsiniz? Tavsiyemiz, küçük başlayıp alıştıkça daha büyük projeler yapmanız, ancak bu tamamen tüketici olabilir. Neden bununla ilgileniyorsunuz? Neye tutkulusunuz? Eğer herkesin özellikle önemsediği şeyleri arşivlediği bir grup insan toplayabilirsek, bu çok şeyi kapsar! Tutkunuz hakkında ortalama bir kişiden çok daha fazla şey bileceksiniz, hangi verilerin kaydedilmesi önemli, en iyi koleksiyonlar ve çevrimiçi topluluklar nelerdir, vb. 3. Metadata kazıma Eklenme/değiştirilme tarihi: daha sonra geri dönüp daha önce indirmediğiniz dosyaları indirmeniz için (ancak genellikle bunun için ID veya hash de kullanabilirsiniz). Hash (md5, sha1): dosyayı doğru bir şekilde indirdiğinizi doğrulamak için. ID: bazı dahili ID'ler olabilir, ancak ISBN veya DOI gibi ID'ler de kullanışlıdır. Dosya adı / konum Açıklama, kategori, etiketler, yazarlar, dil, vb. Boyut: ne kadar disk alanına ihtiyacınız olduğunu hesaplamak için. Burada biraz daha teknik olalım. Web sitelerinden metadata kazımak için işleri oldukça basit tuttuk. Sonuçları depolamak için Python betikleri, bazen curl ve bir MySQL veritabanı kullanıyoruz. Karmaşık web sitelerini haritalayabilen herhangi bir gelişmiş kazıma yazılımı kullanmadık, çünkü şimdiye kadar sadece id'leri numaralandırarak ve HTML'yi ayrıştırarak bir veya iki tür sayfayı kazımamız gerekti. Kolayca numaralandırılabilen sayfalar yoksa, tüm sayfaları bulmaya çalışan uygun bir tarayıcıya ihtiyacınız olabilir. Tüm bir web sitesini kazımaya başlamadan önce, bunu bir süre manuel olarak yapmayı deneyin. Nasıl çalıştığını anlamak için birkaç düzine sayfayı kendiniz inceleyin. Bazen bu şekilde IP blokları veya diğer ilginç davranışlarla karşılaşabilirsiniz. Veri kazıma için de aynı şey geçerli: Bu hedefe çok fazla dalmadan önce, verilerini etkili bir şekilde indirip indiremediğinizden emin olun. Kısıtlamaları aşmak için deneyebileceğiniz birkaç şey var. Aynı veriyi barındıran ancak aynı kısıtlamalara sahip olmayan başka IP adresleri veya sunucular var mı? Bazı API uç noktaları kısıtlamalara sahip değilken, diğerleri var mı? İndirme hızınızda IP'niz ne zaman engelleniyor ve ne kadar süreyle? Yoksa engellenmiyor ama yavaşlatılıyor musunuz? Bir kullanıcı hesabı oluşturursanız, o zaman işler nasıl değişiyor? HTTP/2 kullanarak bağlantıları açık tutabilir misiniz ve bu, sayfa talep etme hızınızı artırıyor mu? Aynı anda birden fazla dosya listeleyen sayfalar var mı ve orada listelenen bilgiler yeterli mi? Muhtemelen kaydetmek isteyeceğiniz şeyler şunlardır: Bunu genellikle iki aşamada yapıyoruz. İlk olarak, ham HTML dosyalarını indiriyoruz, genellikle doğrudan MySQL'e (çok sayıda küçük dosyadan kaçınmak için, bu konuyu aşağıda daha fazla konuşacağız). Daha sonra, ayrı bir adımda, bu HTML dosyalarını gerçek MySQL tablolarına ayrıştırıyoruz. Bu şekilde, ayrıştırma kodunuzda bir hata keşfederseniz her şeyi baştan indirmenize gerek kalmaz, çünkü yeni kodla HTML dosyalarını yeniden işleyebilirsiniz. Ayrıca, işleme adımını paralel hale getirmek genellikle daha kolaydır, böylece biraz zaman kazanırsınız (ve kazıma çalışırken işleme kodunu yazabilirsiniz, her iki adımı birden yazmak zorunda kalmadan). Son olarak, bazı hedefler için metadata kazımanın tek seçenek olduğunu unutmayın. Orada düzgün bir şekilde korunmayan bazı büyük metadata koleksiyonları var. Başlık Alan seçimi / felsefe: Hangi alana odaklanmak istiyorsunuz ve neden? Kendi yararınıza kullanabileceğiniz benzersiz tutkularınız, becerileriniz ve koşullarınız nelerdir? Hedef seçimi: Hangi belirli koleksiyonu yansıtacaksınız? Metadata kazıma: Dosyalar hakkında bilgi kataloglama, genellikle çok daha büyük olan dosyaların kendilerini indirmeden. Veri seçimi: Metadata'ya dayanarak, şu anda arşivlemek için en alakalı verilerin daraltılması. Her şey olabilir, ancak genellikle alan ve bant genişliğini tasarruf etmenin makul bir yolu vardır. Veri kazıma: Veriyi gerçekten elde etme. Dağıtım: Torrentlerde paketleme, bir yerde duyurma, insanların yaymasını sağlama. 5. Veri kazıma Artık verileri toplu olarak indirmeye hazırsınız. Daha önce belirtildiği gibi, bu noktada hedefin davranışını ve kısıtlamalarını daha iyi anlamak için zaten manuel olarak bir dizi dosya indirmiş olmalısınız. Ancak, bir kerede birçok dosya indirmeye başladığınızda sizi hala sürprizler bekliyor olacak. Buradaki tavsiyemiz, işleri basit tutmaktır. Başlangıçta sadece bir dizi dosya indirin. Python kullanabilir ve ardından birden fazla iş parçacığına genişletebilirsiniz. Ancak bazen daha basit olan, doğrudan veritabanından Bash dosyaları oluşturarak ve ardından bunları birden fazla terminal penceresinde çalıştırarak ölçeklendirmektir. Burada bahsetmeye değer hızlı bir teknik hile, MySQL'de OUTFILE kullanmaktır; "secure_file_priv"i mysqld.cnf'de devre dışı bırakırsanız her yere yazabilirsiniz (ve Linux'taysanız AppArmor'u da devre dışı bırakmayı/üzerine yazmayı unutmayın). Verileri basit sabit disklerde saklıyoruz. Elinizde ne varsa onunla başlayın ve yavaşça genişletin. Yüzlerce TB veri depolamayı düşünmek bunaltıcı olabilir. Karşı karşıya olduğunuz durum buysa, önce iyi bir alt küme koyun ve duyurunuzda geri kalanını depolamak için yardım isteyin. Kendiniz daha fazla sabit disk almak istiyorsanız, r/DataHoarder'da iyi fırsatlar bulmak için bazı iyi kaynaklar var. Çok fazla dosya içeren dosya sistemleri hakkında fazla endişelenmemeye çalışın. ZFS gibi şeyler kurma tuzağına düşmek kolaydır. Ancak dikkat edilmesi gereken bir teknik detay, birçok dosya içeren dosya sistemlerinin iyi çalışmamasıdır. Basit bir çözüm olarak, farklı ID aralıkları veya hash ön ekleri için birden fazla dizin oluşturmak olduğunu gördük. Verileri indirdikten sonra, varsa, metadatalardaki hash'leri kullanarak dosyaların bütünlüğünü kontrol ettiğinizden emin olun. 2. Hedef seçimi Erişilebilir: Metadata ve verilerini kazımanızı engellemek için tonlarca koruma katmanı kullanmayan. Özel içgörü: Bu hedef hakkında özel bilgilere sahipsiniz, örneğin bu koleksiyona özel erişiminiz var ya da savunmalarını nasıl aşacağınızı buldunuz. Bu gerekli değil (yaklaşan projemiz özel bir şey yapmıyor), ama kesinlikle yardımcı olur! Büyük Yani, üzerinde çalıştığımız alanı belirledik, şimdi hangi özel koleksiyonu yansıtacağız? İyi bir hedef oluşturan birkaç şey var: Bilim ders kitaplarımızı Library Genesis dışındaki web sitelerinde bulduğumuzda, internete nasıl ulaştıklarını anlamaya çalıştık. Sonra Z-Library'yi bulduk ve çoğu kitabın ilk olarak orada görünmese de, sonunda oraya ulaştığını fark ettik. Library Genesis ile olan ilişkisini ve (mali) teşvik yapısını ve üstün kullanıcı arayüzünü öğrendik, bunların her ikisi de çok daha eksiksiz bir koleksiyon oluşturdu. Daha sonra bazı ön metadata ve veri kazıma işlemleri yaptık ve üyelerimizden birinin çok sayıda proxy sunucusuna özel erişimini kullanarak IP indirme sınırlarını aşabileceğimizi fark ettik. Farklı hedefleri keşfederken, izlerinizi gizlemek için VPN'ler ve geçici e-posta adresleri kullanmanın zaten önemli olduğunu unutmayın, bu konuyu daha sonra daha fazla konuşacağız. Benzersiz: Diğer projeler tarafından zaten iyi kapsanmamış. Bir proje yaptığımızda, birkaç aşaması vardır: Bunlar tamamen bağımsız aşamalar değildir ve genellikle sonraki bir aşamadan gelen içgörüler sizi önceki bir aşamaya geri gönderir. Örneğin, metadata kazıma sırasında seçtiğiniz hedefin beceri seviyenizin ötesinde savunma mekanizmalarına (IP blokları gibi) sahip olduğunu fark edebilirsiniz, bu yüzden geri dönüp farklı bir hedef bulursunuz. - Anna ve ekip (<a %(reddit)s>Reddit</a>) Dijital koruma genel olarak ve korsan arşivcilik özelinde <em>neden</em> hakkında tüm kitaplar yazılabilir, ancak çok aşina olmayanlar için hızlı bir giriş yapalım. Dünya her zamankinden daha fazla bilgi ve kültür üretiyor, ancak aynı zamanda her zamankinden daha fazlası kayboluyor. İnsanlık, bu mirası akademik yayıncılar, akış hizmetleri ve sosyal medya şirketleri gibi şirketlere büyük ölçüde emanet ediyor ve bunlar genellikle iyi koruyucular olduklarını kanıtlamadılar. Dijital Amnezi belgeselini veya Jason Scott'ın herhangi bir konuşmasını izleyin. Bazı kurumlar ellerinden geldiğince arşivleme konusunda iyi bir iş çıkarıyor, ancak yasalara bağlılar. Korsanlar olarak, telif hakkı uygulaması veya diğer kısıtlamalar nedeniyle dokunamayacakları koleksiyonları arşivleme konusunda benzersiz bir konumdayız. Ayrıca, koleksiyonları dünya genelinde birçok kez yansıtabiliriz, böylece uygun koruma şansını artırabiliriz. Şimdilik, fikri mülkiyetin artıları ve eksileri, yasaları çiğnemenin ahlakı, sansür üzerine düşünceler veya bilgi ve kültüre erişim sorunu hakkında tartışmalara girmeyeceğiz. Tüm bunları bir kenara bırakarak, <em>nasıl</em> dalalım. Ekibimizin nasıl korsan arşivciler haline geldiğini ve bu süreçte öğrendiğimiz dersleri paylaşacağız. Bu yolculuğa çıktığınızda birçok zorlukla karşılaşacaksınız ve umarız bunlardan bazılarıyla başa çıkmanıza yardımcı olabiliriz. Nasıl korsan arşivci olunur İlk zorluk şaşırtıcı olabilir. Bu ne teknik bir sorun ne de yasal bir sorun. Bu psikolojik bir sorun. Dalmadan önce, Korsan Kütüphane Yansıtması hakkında iki güncelleme (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı): Çok cömert bağışlar aldık. İlki, Library Genesis'in orijinal kurucusu "bookwarrior"u da destekleyen anonim bir bireyden 10 bin dolardı. Bu bağışı kolaylaştırdığı için bookwarrior'a özel teşekkürler. İkincisi, son yayınımızdan sonra bizimle iletişime geçen ve yardım etmeye ilham alan başka bir anonim bağışçıdan 10 bin dolardı. Ayrıca bir dizi daha küçük bağış aldık. Tüm cömert desteğiniz için çok teşekkür ederiz. Bu, destekleyeceğimiz heyecan verici yeni projelerimiz var, bu yüzden bizi izlemeye devam edin. İkinci yayınımızın boyutuyla ilgili bazı teknik zorluklar yaşadık, ancak torrentlerimiz şimdi yayında ve tohumlanıyor. Ayrıca, koleksiyonumuzu çok yüksek hızlı sunucularında tohumlamak için anonim bir bireyden cömert bir teklif aldık, bu yüzden makinelerine özel bir yükleme yapıyoruz, ardından koleksiyonu indiren herkes hızda büyük bir iyileşme görecek. Blog yazıları Merhaba, ben Anna. Dünyanın en büyük gölge kütüphanesi olan <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ni yarattım. Bu benim kişisel blogum, burada ben ve ekip arkadaşlarım korsanlık, dijital koruma ve daha fazlası hakkında yazıyoruz. <a %(reddit)s>Reddit</a> üzerinden benimle bağlantı kurun. Bu web sitesinin sadece bir blog olduğunu unutmayın. Burada sadece kendi sözlerimizi barındırıyoruz. Burada hiçbir torrent veya başka telif hakkıyla korunan dosya barındırılmıyor veya bağlantı verilmiyor. <strong>Kütüphane</strong> - Çoğu kütüphane gibi, öncelikle kitaplar gibi yazılı materyallere odaklanıyoruz. Gelecekte diğer medya türlerine de genişleyebiliriz. <strong>Yansıtmak</strong> - Mevcut kütüphanelerin yalnızca bir yansımasıyız. Odak noktamız koruma, kitapların kolayca aranabilir ve indirilebilir hale getirilmesi (erişim) veya yeni kitaplar ekleyen büyük bir topluluk oluşturmak (kaynak sağlama) değil. <strong>Korsan</strong> - Çoğu ülkede telif hakkı yasasını kasten ihlal ediyoruz. Bu, yasal varlıkların yapamayacağı bir şeyi yapmamıza olanak tanır: kitapların geniş çapta yansıtıldığından emin olmak. <em>Bu blogdan dosyalara bağlantı vermiyoruz. Lütfen kendiniz bulun.</em> - Anna ve ekip (<a %(reddit)s>Reddit</a>) Bu proje (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı) insan bilgisinin korunmasına ve özgürleştirilmesine katkıda bulunmayı amaçlamaktadır. Bizden önceki büyüklerin izinden giderek küçük ve mütevazı katkımızı yapıyoruz. Bu projenin odak noktası, ismiyle açıklanmaktadır: Yansıttığımız ilk kütüphane Z-Library oldu. Bu popüler (ve yasadışı) bir kütüphanedir. Library Genesis koleksiyonunu alıp kolayca aranabilir hale getirdiler. Bunun yanı sıra, katkıda bulunan kullanıcılara çeşitli avantajlar sunarak yeni kitap katkılarını teşvik etmede çok etkili oldular. Şu anda bu yeni kitapları Library Genesis'e geri katkıda bulunmuyorlar. Ve Library Genesis'in aksine, koleksiyonlarını kolayca yansıtılabilir hale getirmiyorlar, bu da geniş çapta korunmayı engelliyor. Bu, iş modelleri için önemlidir, çünkü koleksiyonlarına toplu erişim (günde 10'dan fazla kitap) için para talep ediyorlar. Para karşılığında yasadışı bir kitap koleksiyonuna toplu erişim sağlama konusunda ahlaki yargılarda bulunmuyoruz. Z-Library'nin bilgiye erişimi genişletmede ve daha fazla kitap temin etmede başarılı olduğu şüphesizdir. Biz sadece üzerimize düşeni yapıyoruz: bu özel koleksiyonun uzun vadeli korunmasını sağlamak. İnsan bilgisini korumaya ve özgürleştirmeye yardımcı olmanız için sizi torrentlerimizi indirip paylaşmaya davet ediyoruz. Verilerin nasıl organize edildiği hakkında daha fazla bilgi için proje sayfasına bakın. Ayrıca, hangi koleksiyonların bir sonraki yansıtılacağı ve bunun nasıl yapılacağı konusunda fikirlerinizi paylaşmanızı çok isteriz. Birlikte çok şey başarabiliriz. Bu, sayısız diğer katkılar arasında sadece küçük bir katkıdır. Yaptığınız her şey için teşekkür ederiz. Korsan Kütüphane Yansıtması Tanıtımı: Libgen'de olmayan 7TB kitap koruma altında 10% of insanlığın yazılı mirası sonsuza kadar korunmuş <strong>Google.</strong> Sonuçta, Google Books için bu araştırmayı yaptılar. Ancak, metadata toplu olarak erişilebilir değil ve kazıması oldukça zor. <strong>Çeşitli bireysel kütüphane sistemleri ve arşivler.</strong> Yukarıdakiler tarafından indekslenmemiş ve toplanmamış kütüphaneler ve arşivler vardır, genellikle yetersiz finanse edildikleri için veya diğer nedenlerle verilerini Open Library, OCLC, Google vb. ile paylaşmak istemezler. Bunların çoğu, internet üzerinden erişilebilir dijital kayıtlara sahiptir ve genellikle çok iyi korunmamaktadırlar, bu yüzden yardım etmek ve tuhaf kütüphane sistemleri hakkında eğlenerek öğrenmek istiyorsanız, bunlar harika başlangıç noktalarıdır. <strong>ISBNdb.</strong> Bu, bu blog yazısının konusudur. ISBNdb, özellikle fiyatlandırma verileri olmak üzere çeşitli web sitelerinden kitap metadata'sını kazır, ardından bu verileri kitap satıcılarına satar, böylece kitaplarını piyasanın geri kalanına uygun şekilde fiyatlandırabilirler. ISBN'ler günümüzde oldukça evrensel olduğundan, etkili bir şekilde “her kitap için bir web sayfası” oluşturmuşlardır. <strong>Open Library.</strong> Daha önce belirtildiği gibi, bu onların tüm misyonu. İşbirliği yapan kütüphanelerden ve ulusal arşivlerden büyük miktarda kütüphane verisi topladılar ve toplamaya devam ediyorlar. Ayrıca, kayıtları yinelenenlerden arındırmaya ve her türlü metadata ile etiketlemeye çalışan gönüllü kütüphaneciler ve teknik bir ekipleri var. En iyi yanı, veri setlerinin tamamen açık olması. Basitçe <a %(openlibrary)s>indirebilirsiniz</a>. <strong>WorldCat.</strong> Bu, kâr amacı gütmeyen OCLC tarafından işletilen bir web sitesidir ve kütüphane yönetim sistemleri satmaktadır. Birçok kütüphaneden kitap metadata'sını toplar ve WorldCat web sitesi aracılığıyla erişilebilir hale getirir. Ancak, bu verileri satarak para kazandıkları için toplu indirme için mevcut değildir. Belirli kütüphanelerle işbirliği içinde daha sınırlı toplu veri setleri indirilebilir durumda. 1. "Sonsuza kadar" ifadesinin makul bir tanımı için. ;) 2. Elbette, insanlığın yazılı mirası günümüzde kitaplardan çok daha fazlasını içeriyor. Bu gönderi ve son yayınlarımız için kitaplara odaklanıyoruz, ancak ilgi alanlarımız daha geniş. 3. Aaron Swartz hakkında söylenecek çok daha fazla şey var, ancak bu hikayede önemli bir rol oynadığı için onu kısaca anmak istedik. Zaman geçtikçe, daha fazla insan onun adını ilk kez duyabilir ve ardından kendileri bu konunun derinliklerine dalabilir. <strong>Fiziksel kopyalar.</strong> Açıkçası bu pek yardımcı değil, çünkü bunlar sadece aynı materyalin kopyaları. İnsanların kitaplarda yaptığı tüm notları, Fermat’ın ünlü “kenar boşluklarındaki karalamaları” gibi, koruyabilsek harika olurdu. Ama ne yazık ki, bu bir arşivcinin hayali olarak kalacak. <strong>“Baskılar”.</strong> Burada bir kitabın her benzersiz versiyonunu sayarsınız. Kapak veya önsöz gibi herhangi bir şey farklıysa, bu farklı bir baskı olarak sayılır. <strong>Dosyalar.</strong> Library Genesis, Sci-Hub veya Z-Library gibi gölge kütüphanelerle çalışırken ek bir husus vardır. Aynı baskının birden fazla taraması olabilir. Ve insanlar mevcut dosyaların daha iyi versiyonlarını, metni OCR kullanarak tarayarak veya açılı taranmış sayfaları düzelterek yapabilirler. Bu dosyaları yalnızca bir baskı olarak saymak istiyoruz, bu da iyi metadata veya belge benzerlik ölçütleri kullanarak yinelenenleri kaldırmayı gerektirir. <strong>“Eserler”.</strong> Örneğin, “Harry Potter ve Sırlar Odası” gibi bir mantıksal kavram, farklı çeviriler ve yeniden baskılar gibi tüm versiyonlarını kapsar. Bu, bir anlamda kullanışlı bir tanım, ancak neyin sayılacağına karar vermek zor olabilir. Örneğin, farklı çevirileri korumak isteyebiliriz, ancak sadece küçük farklılıklar içeren yeniden baskılar o kadar önemli olmayabilir. - Anna ve ekip (<a %(reddit)s>Reddit</a>) Korsan Kütüphane Yansıtması ile (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı), amacımız dünyadaki tüm kitapları alıp sonsuza kadar korumaktır.<sup>1</sup> Z-Library torrentlerimiz ve orijinal Library Genesis torrentlerimiz arasında 11.783.153 dosyamız var. Ama bu gerçekten ne kadar? Bu dosyaları düzgün bir şekilde çoğaltmasaydık, dünyadaki tüm kitapların yüzde kaçını korumuş olurduk? Gerçekten böyle bir şeye sahip olmak isteriz: Bazı kaba sayılarla başlayalım: Hem Z-Library/Libgen hem de Open Library'de benzersiz ISBN'lerden daha fazla kitap var. Bu, bu kitapların çoğunun ISBN'lerinin olmadığı anlamına mı geliyor, yoksa ISBN metadata'sı basitçe eksik mi? Bu soruyu muhtemelen diğer özelliklere (başlık, yazar, yayıncı vb.) dayalı otomatik eşleştirme, daha fazla veri kaynağı çekme ve ISBN'leri gerçek kitap taramalarından çıkarma kombinasyonu ile yanıtlayabiliriz (Z-Library/Libgen durumunda). Bu ISBN'lerin kaçı benzersiz? Bu en iyi bir Venn diyagramı ile açıklanır: Daha kesin olmak gerekirse: Bu kadar az örtüşme olmasına şaşırdık! ISBNdb'de, ne Z-Library'de ne de Open Library'de yer almayan çok sayıda ISBN var ve aynı durum (daha küçük ama yine de önemli bir ölçüde) diğer ikisi için de geçerli. Bu durum birçok yeni soruyu gündeme getiriyor. Otomatik eşleştirme, ISBN'lerle etiketlenmemiş kitapların etiketlenmesine ne kadar yardımcı olurdu? Çok sayıda eşleşme olur ve dolayısıyla örtüşme artar mıydı? Ayrıca, 4. veya 5. bir veri setini dahil edersek ne olurdu? O zaman ne kadar örtüşme görürdük? Bu bize bir başlangıç noktası veriyor. Artık Z-Library veri setinde olmayan ve başlık/yazar alanlarıyla da eşleşmeyen tüm ISBN'lere bakabiliriz. Bu, dünyadaki tüm kitapları koruma konusunda bize bir yol gösterebilir: önce internetten taramalar yaparak, ardından gerçek hayatta kitapları tarayarak. İkincisi, hatta kitle fonlamasıyla veya belirli kitapların dijitalleştirilmesini isteyen kişilerden gelen "ödüller" ile desteklenebilir. Tüm bunlar başka bir zamanın hikayesi. Eğer bu konulardan herhangi birine yardımcı olmak istiyorsanız — daha fazla analiz; daha fazla metadata toplama; daha fazla kitap bulma; kitapların OCR'lanması; diğer alanlar için bunu yapma (örneğin makaleler, sesli kitaplar, filmler, TV şovları, dergiler) veya hatta bu verilerin bir kısmını ML / büyük dil modeli eğitimi gibi şeyler için kullanılabilir hale getirme — lütfen benimle iletişime geçin (<a %(reddit)s>Reddit</a>). Özellikle veri analiziyle ilgileniyorsanız, veri setlerimizi ve scriptlerimizi daha kolay kullanılabilir bir formatta sunmak için çalışıyoruz. Bir not defterini çatallayıp bununla oynamaya başlamanız harika olurdu. Son olarak, bu çalışmayı desteklemek istiyorsanız, lütfen bağış yapmayı düşünün. Bu tamamen gönüllüler tarafından yürütülen bir operasyon ve katkınız büyük bir fark yaratıyor. Her katkı önemlidir. Şu an için kripto ile bağış kabul ediyoruz; Anna’nın Arşivi'ndeki Bağış sayfasına bakın. Bir yüzde için bir payda gerekir: şimdiye kadar yayımlanan toplam kitap sayısı.<sup>2</sup> Google Books'un sona ermesinden önce, projede bir mühendis olan Leonid Taycher, <a %(booksearch_blogspot)s>bu sayıyı tahmin etmeye çalıştı</a>. Dilinde bir şakayla 129.864.880 (“en azından Pazar gününe kadar”) sayısına ulaştı. Bu sayıyı, dünyadaki tüm kitapların birleşik bir veritabanını oluşturarak tahmin etti. Bunun için farklı veri setlerini bir araya getirdi ve ardından çeşitli şekillerde birleştirdi. Kısa bir ara verelim, dünyadaki tüm kitapları kataloglamaya çalışan bir başka kişi daha vardı: Merhum dijital aktivist ve Reddit'in kurucu ortağı Aaron Swartz.<sup>3</sup> O, <a %(youtube)s>Open Library</a>'yi “yayınlanmış her kitap için bir web sayfası” hedefiyle başlattı ve farklı kaynaklardan gelen verileri birleştirdi. Akademik makaleleri toplu olarak indirdiği için yargılandığında, dijital koruma çalışmaları için en yüksek bedeli ödedi ve intihar etti. Bu, grubumuzun takma adlar kullanmasının ve çok dikkatli olmamızın nedenlerinden biri. Open Library, Aaron’un mirasını devam ettirerek, Internet Archive'deki kişiler tarafından kahramanca yönetilmeye devam ediyor. Bu konuya bu yazının ilerleyen kısımlarında tekrar döneceğiz. Google blog yazısında, Taycher bu sayıyı tahmin etmenin zorluklarından bazılarını anlatıyor. İlk olarak, bir kitap nedir? Birkaç olası tanım vardır: “Baskılar”, “kitapların” ne olduğuna dair en pratik tanım gibi görünüyor. Bu tanım, benzersiz ISBN numaralarının atanması için de kullanılıyor. ISBN, veya Uluslararası Standart Kitap Numarası, uluslararası ticarette yaygın olarak kullanılır, çünkü uluslararası barkod sistemiyle entegre edilmiştir (“Uluslararası Ürün Numarası”). Bir kitabı mağazalarda satmak istiyorsanız, bir barkoda ihtiyacınız vardır, bu yüzden bir ISBN alırsınız. Taycher’ın blog yazısı, ISBN'lerin faydalı olmasına rağmen evrensel olmadığını, çünkü gerçekten sadece yetmişlerin ortalarında benimsendiğini ve dünyanın her yerinde kullanılmadığını belirtiyor. Yine de, ISBN muhtemelen kitap baskılarının en yaygın kullanılan tanımlayıcısıdır, bu yüzden en iyi başlangıç noktamızdır. Dünyadaki tüm ISBN'leri bulabilirsek, hala korunması gereken kitapların yararlı bir listesini elde ederiz. Peki, verileri nereden alıyoruz? Dünyadaki tüm kitapların bir listesini derlemeye çalışan birkaç mevcut çaba var: Bu yazıda, önceki Z-Library sürümlerimize kıyasla küçük bir sürümü duyurmaktan mutluluk duyuyoruz. ISBNdb'nin çoğunu kazıdık ve verileri Korsan Kütüphane Yansıtma web sitesinde torrent olarak erişilebilir hale getirdik (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne taşındı; burada doğrudan bağlantı vermeyeceğiz, sadece arayın). Bunlar yaklaşık 30.9 milyon kayıt (20GB olarak <a %(jsonlines)s>JSON Satırları</a>; 4.4GB sıkıştırılmış). Web sitelerinde aslında 32.6 milyon kayıtları olduğunu iddia ediyorlar, bu yüzden bir şekilde bazılarını kaçırmış olabiliriz veya <em>onlar</em> bir şeyleri yanlış yapıyor olabilir. Her durumda, şimdilik tam olarak nasıl yaptığımızı paylaşmayacağız — bunu okuyucuya bir alıştırma olarak bırakacağız. ;-) Paylaşacağımız şey, dünyadaki kitap sayısını tahmin etmeye daha da yaklaşmak için bazı ön analizlerdir. Üç veri setine baktık: bu yeni ISBNdb veri seti, Z-Library gölge kütüphanesinden kazıdığımız metadata'nın orijinal sürümü (Library Genesis'i içerir) ve Open Library veri dökümü. ISBNdb dökümü veya Kaç Kitap Sonsuza Kadar Korunur? Gölge kütüphanelerden dosyaları düzgün bir şekilde çoğaltmasaydık, dünyadaki tüm kitapların yüzde kaçını korumuş olurduk? İnsanlık tarihindeki en büyük gerçekten açık kütüphane olan <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a> hakkında güncellemeler. <em>WorldCat yeniden tasarımı</em> Veri <strong>Format?</strong> <a %(blog)s>Anna’nın Arşiv Konteynerleri (AAC)</a>, esasen <a %(jsonlines)s>JSON Lines</a> ile sıkıştırılmış <a %(zstd)s>Zstandard</a>, artı bazı standartlaştırılmış semantikler. Bu konteynerler, uyguladığımız farklı kazımalara dayalı olarak çeşitli kayıt türlerini kapsar. Bir yıl önce, bu soruyu <a %(blog)s>cevaplamak için yola çıktık</a>: <strong>Gölge kütüphaneler tarafından kalıcı olarak korunmuş kitapların yüzdesi nedir?</strong> Veriler hakkında bazı temel bilgilere bakalım: Bir kitap <a %(wikipedia_library_genesis)s>Library Genesis</a> gibi açık veri gölge kütüphanesine ve şimdi <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne girdiğinde, tüm dünyada (torrentler aracılığıyla) yansıtılır ve böylece pratikte sonsuza kadar korunur. Kitapların hangi yüzdesinin korunduğu sorusunu yanıtlamak için payda bilmemiz gerekiyor: toplamda kaç kitap var? Ve ideal olarak sadece bir sayı değil, gerçek metadata da olmalı. Böylece onları sadece gölge kütüphanelerle eşleştirmekle kalmaz, aynı zamanda <strong>korunacak kalan kitapların bir yapılacaklar listesi oluşturabiliriz!</strong> Hatta bu yapılacaklar listesini tamamlamak için bir kitle kaynaklı çaba hayal etmeye başlayabiliriz. <a %(wikipedia_isbndb_com)s>ISBNdb</a>'yi kazıdık ve <a %(openlibrary)s>Open Library veri setini</a> indirdik, ancak sonuçlar tatmin edici değildi. Ana sorun, ISBN'lerin çok fazla örtüşmemesiydi. <a %(blog)s>blog yazımızdaki</a> bu Venn diyagramına bakın: ISBNdb ve Open Library arasında ne kadar az örtüşme olduğunu görünce çok şaşırdık, her ikisi de çeşitli kaynaklardan, web kazımaları ve kütüphane kayıtları gibi, verileri cömertçe dahil ediyor. Eğer her ikisi de dışarıdaki çoğu ISBN'i bulmada iyi bir iş çıkarıyorsa, daireleri kesinlikle önemli ölçüde örtüşmeli ya da biri diğerinin alt kümesi olmalıydı. Bizi düşündürdü, bu dairelerin tamamen dışında kaç kitap var <em></em>? Daha büyük bir veritabanına ihtiyacımız var. O zaman gözlerimizi dünyanın en büyük kitap veritabanına diktik: <a %(wikipedia_worldcat)s>WorldCat</a>. Bu, kâr amacı gütmeyen <a %(wikipedia_oclc)s>OCLC</a> tarafından sağlanan bir özel veritabanıdır ve dünya çapındaki kütüphanelerden metadata kayıtlarını toplar, karşılığında bu kütüphanelere tam veri setine erişim sağlar ve son kullanıcıların arama sonuçlarında görünmelerini sağlar. OCLC kâr amacı gütmeyen bir kuruluş olmasına rağmen, iş modeli veritabanlarını korumayı gerektiriyor. Üzgünüz OCLC'deki arkadaşlar, hepsini paylaşıyoruz. :-) Geçtiğimiz yıl boyunca, tüm WorldCat kayıtlarını titizlikle kazıdık. İlk başta şanslı bir mola verdik. WorldCat, web sitelerinin tam yeniden tasarımını (Ağustos 2022'de) yeni başlatıyordu. Bu, arka uç sistemlerinde önemli bir revizyon içeriyordu ve birçok güvenlik açığı ortaya çıkardı. Fırsatı hemen değerlendirdik ve sadece birkaç gün içinde yüz milyonlarca (!) kaydı kazıyabildik. Bundan sonra, güvenlik açıkları birer birer yavaşça düzeltildi, son bulduğumuz açık yaklaşık bir ay önce yamalandı. O zamana kadar neredeyse tüm kayıtları almıştık ve sadece biraz daha yüksek kaliteli kayıtlar için gidiyorduk. Bu yüzden yayınlama zamanının geldiğini hissettik! 1.3B WorldCat kazıma <em><strong>Özetle:</strong> Anna’nın Arşivi, korunması gereken kitapların bir yapılacaklar listesi oluşturmak için WorldCat'in (dünyanın en büyük kütüphane metadata koleksiyonu) tamamını kazıdı.</em> WorldCat Uyarı: bu blog yazısı artık geçerli değildir. IPFS'nin henüz ana akım için hazır olmadığına karar verdik. Mümkün olduğunda Anna’nın Arşivi'nden IPFS üzerindeki dosyalara bağlantı vermeye devam edeceğiz, ancak artık kendimiz barındırmayacağız ve başkalarına IPFS kullanarak yansıtmayı önermiyoruz. Koleksiyonumuzu korumaya yardımcı olmak istiyorsanız, Lütfen Torrents sayfamıza bakın. Z-Library'i IPFS üzerinde tohumlamaya yardım edin Dost Sunucu indirme SciDB Harici ödünç alma Harici ödünç alma (yazdırma engellendi) Harici indirme Üstveriyi keşfedin Torrentlerde yer alıyor Geri  (+%(num)s bonus) ödenmedi ödendi iptal edildi süresi doldu Anna'nın onaylamasını bekle geçersiz Metin aşağıda İngilizce olarak devam etmektedir. Git Sıfırla İleri Son Eğer e-posta adresiniz Libgen forumlarında çalışmazsa, <a %(a_mail)s>Proton Mail</a> (ücretsiz) kullanmanızı öneriyoruz. Ayrıca hesabınızın aktifleştirilmesini <a %(a_manual)s>bizzat talep edebilirsiniz</a>. (<a %(a_browser)s>tarayıcı doğrulama</a> gerektirebilir — sınırsız indirme!) Hızlı Ortak Sunucu #%(number)s (önerilen) (biraz daha hızlı ama bekleme listesi var) (tarayıcı doğrulama gerekmez) (tarayıcı doğrulaması veya bekleme listesi yok) (bekleme listesi yok, ancak çok yavaş olabilir) Yavaş Ortak Sunucu #%(number)s Sesli Kitap Çizgi roman Kitap (kurgu) Kitap (kurgu dışı) Kitap (bilinmeyen) Dergi makalesi Dergi Müzik notası Diğer Standart dokümanı Tüm sayfalar PDF'e dönüştürülemedi Libgen.li'de bozuk olarak işaretlendi Libgen.li'de görünür değil Library Genesis Kurgu ".rs-fork"da görünür değil Library Genesis'e ait Kurgu Dışı ".rs-fork"unda görünür değil Bu dosyada exiftool çalıştırma başarısız oldu Z-Library'de “kötü dosya” olarak işaretlendi Z-library'de mevcut değil Z-Library'de “spam” olarak işaretlendi Dosya açılamıyor (ör. bozuk dosya, DRM) Telif hakkı talebi İndirme problemleri (ör. bağlanamıyor, hata mesajı, çok yavaş) Hatalı üst veri (ör. başlık, açıklama, kapak resmi) Diğer Düşük kalite (ör. biçimlendirme hataları, kötü tarama kalitesi, kayıp sayfalar) Spam / dosya kaldırılmalı (ör. reklam, rahatsız edici içerik) %(amount)s (%(amount_usd)s) toplam %(amount)s toplam %(amount)s %(amount_usd)s Zeki Kitap Kurdu Şanslı Kütüphaneci Muhteşem Veri İstifçisi Harika Arşivci İlave indirmeler Cerlalc Çek üstverisi DuXiu 读秀 EBSCOhost eKitap Dizini Google Kitaplar Goodreads HathiTrust IA IA Kontrollü Dijital Ödünç Verme ISBNdb ISBN GRP Libgen.li “scimag” hariç Libgen.rs Kurgu Dışı ve Kurgu Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Rusya Devlet Kütüphanesi Sci-Hub Libgen.li “scimag” aracılığıyla Sci-Hub / Libgen “scimag” Trantor AA'ya Yüklemeler Z-Kütüphane Z-Library Çince Başlık, yazar, DOI, ISBN, MD5, … Arama Yazar Açıklama ve üstveri yorumları Baskı Orijinal dosya adı Yayınlayan (belirli alanı ara) Başlık Yayın yılı Teknik ayrıntılar Bu paranın minimum (işlem) limiti normalden daha yüksek. Lütfen farklı bir süre ya da farklı bir para seçin. İstek tamamlanamadı. Lütfen birkaç dakika içinde tekrar deneyin ve eğer sorun devam ederse, bir ekran görüntüsüyle birlikte %(email)s adresinden iletişime geçin. Bilinmeyen bir hata oluştu. Lütfen %(email)s e-mail hesabı ile iletişime geçin ve bu adrese bir ekran görüntüsü gönderin. Ödeme işlemi hatası. Lütfen bir süre bekleyin ve tekrar deneyin. Sorun 24 saatten fazla devam ederse lütfen %(email)s e-mail adresine bir ekran görüntüsü ile birlikte bizimle iletişime geçin. We’re running a fundraiser for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">backing up</a> the largest comics shadow library in the world. Thanks for your support! <a href="/donate">Bağış yap.</a> Eğer bağış yapamıyorsanız, arkadaşlarınıza söyleyerek ve bizi <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>'te veya <a href="https://t.me/annasarchiveorg">Telegram</a>'da takip ederek bizi destekleyebilirsiniz. <a %(a_request)s>Kitap talep etmek</a> veya küçük (<10k) <a %(a_upload)s>yüklemeler</a> için bize e-posta göndermeyin. Anna'nın Arşivi DMCA / telif hakkı talepleri İletişim Reddit Alternatifler GECEKONDU (%(unaffiliated)s) bağlantısız Anna’nın Arşivi sizin yardımınıza ihtiyaç duyuyor! Şimdi bağış yaparsanız, hızlı indirme sayınızı <strong>iki katına</strong> çıkarırsınız. Birçok kişi bizi durdurmaya çalışıyor, ama biz karşılık veriyoruz. Bu ay bağış yaparsanız, <strong>iki kat</strong> hızlı indirme hakkı kazanırsınız. Bu ayın sonuna kadar geçerlidir. İnsan bilgisini kurtarmak: harika bir tatil hediyesi! Üyelikler buna göre uzatılacaktır. Dost sunucular, sunucu servisinin kapatılması nedeniyle kullanılamıyor. Yakında tekrar aktif olacaklar. Anna’nın Arşivi'nin dayanıklılığını artırmak için, aynaları çalıştıracak gönüllüler arıyoruz. Yeni bir bağış yapma yöntemi mevcut: %(method_name)s Lütfen %(donate_link_open_tag)sbağış</a> yapmayı düşünün. Yaptığınız bağışlar sayesinde bu siteyi ayakta tutabiliyoruz. Çok teşekkür ederiz. Bir arkadaşınızı sitemize yönlendirin ve hem siz hem de arkadaşınız %(percentage)s%% bonus hızlı indirme kazanın! Sevdiklerinize sürpriz yapın, ona üyelikli bir hesap verin. Mükemmel Sevgililer Günü hediyesi! Daha fazla bilgi… Hesap Etkinlik Gelişmiş Anna'nın Blogu ↗ Anna'nın Yazılımı ↗ beta Kod Gezgini Veri kümeleri Bağış yapın İndirilen dosyalar SSS Anasayfa Üstveriyi iyileştir LLM verisi Giriş yap / Kaydol Bağışlarım Herkese açık profil Arama Güvenlik Torrentler Çevir ↗ Gönüllülük ve Ödüller Son indirmeler: 📚&nbsp;Dünyanın en büyük açık kaynak açık veri kütüphanesi. ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library ve daha fazlasını içerir. 📈&nbsp;%(book_any)s kitap, %(journal_article)s makale, %(book_comic)s çizgi roman, %(magazine)s dergi — sonsuza kadar saklı.  ve  ve daha fazlası DuXiu Internet Archive Ödünç Verme Kütüphanesi LibGen 📚&nbsp;İnsanlık tarihinin gerçek anlamda en büyük açık kütüphanesi. 📈&nbsp;%(book_count)s&nbsp;kitaplar, %(paper_count)s&nbsp;makaleler — sonsuza dek korunur. ⭐️&nbsp;%(libraries)s kütüphanelerini yansıtıyoruz. %(scraped)s'i kazıyıp açık kaynağa çeviriyoruz. Tüm kodlarımız ve verilerimiz tamamen açık kaynaklıdır. OpenLib Sci-Hub ,  📚 Dünyanın en büyük açık kaynaklı açık-veri kütüphanesi. <br>⭐️ Scihub, Libgen, Zlib ve daha fazlası. Z-Lib Anna'nın Arşivi Geçersiz istek. %(websites)s adresini ziyaret edin. Dünyanın en büyük açık-kaynaklı açık-veri kütüphanesi. Sci-Hub, Library Genesis, Z-Library ve daha fazlasını kopyalar. Anna’nın Arşivi'ni Ara Anna'nın Arşivi Lütfen tekrar denemek için sayfayı yenileyin. Sorun birkaç saat boyunca devam ederse <a %(a_contact)s>bizimle iletişime geçin</a>. 🔥 Bu sayfa yüklenirken sorun oluştu <li>1. Bizi <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> veya <a href="https://t.me/annasarchiveorg">Telegram</a>'da takip edin.</li><li>2. Twitter'da, Reddit'te, Tiktok'ta, Instagram'da, yerel kafe ya da kütüphanenizde veya canınız nerede isterse Anna'nın Arşivi hakkında birkaç kelime söyleyin! "Bekçilik" kavramına inanmıyoruz - eğer kapatılırsak tüm kodlarımızın ve verilerimizin açık kaynak olması sayesinde başka bir yerde ortaya çıkacağız.</li><li>3. Eğer imkanınız varsa, <a href="/donate">bağış</a> yapmayı bir düşünün.</li><li>4. Sitemizi farklı dillere <a href="https://translate.annas-software.org/">çevirmemize</a> yardım edin.</li><li>5. Eğer yazılım mühendisiyseniz, <a href="https://annas-software.org/">açık kaynak kodumuza</a> katkı yapmayı ya da <a href="/datasets">torrent</a>lerimizi paylaşmayı (seeding) düşünün.</li> 10. Kendi dilinizde Anna’nın Arşivi için Wikipedia sayfası oluşturun veya bakımına yardımcı olun. 11. Küçük, zevkli reklamlar yerleştirmek istiyoruz. Anna’nın Arşivi’nde reklam vermek isterseniz, lütfen bize bildirin. 6. Güvenlik araştırmacısı iseniz becerilerinizi hem hücum hem de savunma amaçlı kullanabiliriz. <a %(a_security)s>Güvenlik</a> sayfamıza göz atın. 7. Anonim tüccarlar için ödeme uzmanları arıyoruz. Daha uygun bağış yolları eklememize yardımcı olabilir misiniz? PayPal, WeChat, hediye kartları. Birini tanıyorsanız, lütfen bizimle iletişime geçin. 8. Her zaman daha fazla sunucu kapasitesi arıyoruz. 9. Dosya sorunlarını bildirerek, yorum bırakarak ve bu web sitesinde listeler oluşturarak yardımcı olabilirsiniz. Ayrıca <a %(a_upload)s>daha fazla kitap yükleyerek</a> veya mevcut kitapların dosya sorunlarını ya da formatlarını düzelterek de yardımcı olabilirsiniz. Gönüllü olma hakkında daha kapsamlı bilgi için <a %(a_volunteering)s>Gönüllülük ve Ödüller</a> sayfamıza bakın. Bilginin özgür akışına, bilgi ve kültürün korunmasına kuvvetle inanıyoruz. Bu arama motorunu devlerin omuzları üzerine inşa ediyoruz. Çeşitli gölge kütüphaneleri yaratan insanların sıkı çalışmalarına derinden saygı duyuyoruz ve bu arama motorunun gölge kütüphanelerin yayılmasını artıracağını umuyoruz. Kaydettiğimiz aşamalardan haberdar olmak için, Anna'yı <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> veya <a href="https://t.me/annasarchiveorg">Telegram</a>. 'te takip edin. Sorular ve geri bildirimler için lütfen Anna ile %(email)s aracılığıyla iletişime geçin. Hesap Kimliği: %(account_id)s Çıkış ❌ Bir şeyler yanlış gitti. Lütfen sayfayı yenileyin ve tekrar deneyin. ✅ Başarıyla çıkış yaptınız. Yeniden giriş yapmak için sayfayı yenileyin. Kullanılan hızlı indirmeler (son 24 saatte): <strong>%(used)s / %(total)s</strong> Üyelik: <strong>%(tier_name)s</strong> %(until_date)s'a kadar <a %(a_extend)s>(extend)</a> Birden fazla üyeliği birleştirebilirsiniz (24 saat içinde hızlı indirmeler birleştirilecektir). Üyelik: <strong>Hiçbiri</strong> <a %(a_become)s>(üye ol)</a> Eğer üyeliğinizi daha yüksek bir kademeye yükseltmekle ilgileniyorsanız Anna ile %(email)s adresinde iletişime geçin. Gösterilen profil: %(profile_link)s Gizli anahtar (paylaşmayın!): %(secret_key)s göster Bize katılın! Grubumuza katılmak için <a %(a_tier)s>daha yüksek bir seviyeye</a> yükseltin. Özel Telegram grubu: %(link)s Hesap hangi indirmeler? Giriş yap Anahtarınızı kaybetmeyin! Geçersiz özel anahtar. Anahtarınızı doğrulayın ve tekrar deneyin, ya da alternatif olarak aşağıda yeni hesap ile kaydolun. Özel anahtar Giriş yapmak için özel anahtarınızı girin: Eski e-posta tabanlı hesap mı? E-postanı %(a_open)s>buraya gir</a>. Yeni hesap oluştur Henüz hesabınız yok mu? Kayıt başarılı! Özel anahtarınız: <span %(span_key)s>%(key)s</span> Bu anahtarı dikkatlice saklayın. Eğer kaybederseniz, hesabınıza erişimi kaybedersiniz. <li %(li_item)s><strong>Yer işaretlerine ekleyin.</strong>Anahtarınızı hatırlamak için bu sayfayı yer işaretlerine ekleyebilirsiniz.</li><li %(li_item)s><strong>İndir.</strong> Anahtarınızı indirmek için <a %(a_download)s>bu linke</a> tıklayın.</li><li %(li_item)s><strong>Şifre yöneticisi.</strong> Kendi kolaylığınız için, anahtar aşağıda önceden doldurulmuş biçimde bulunur, bunun aracılığıyla giriş yapınca şifre yöneticinize kaydedebilirsiniz.</li> Giriş yap / Kayıt ol Tarayıcı doğrulaması Uyarı: Kodda hatalı Unicode karakterleri var ve çeşitli durumlarda yanlış çalışabilir. Ham ikili veri (raw binary), URL'deki base64 gösterimiyle çözülebilir. Açıklama Etiket Ön ek Belirli bir kod için URL Web Sitesi “%(prefix_label)s” ile başlayan kodlar Lütfen bu sayfalardan otomatik olarak veri çekmeyin. Bunun yerine, ElasticSearch ve MariaDB veri tabanlarımızı <a %(a_import)s>oluşturmanızı</a> veya <a %(a_download)s>indirmenizi</a> ve <a %(a_software)s>açık kaynak kodumuzu</a> çalıştırmanızı öneririz. Ham veriler, <a %(a_json_file)s>bu gibi</a> JSON dosyaları aracılığıyla manuel olarak keşfedilebilir. %(count)s kayıttan az Genel URL Kod Kaşifi Dizin Kayıtların etiketlendiği kodları, öneklerine göre keşfedin. “Kayıtlar” sütunu, arama motorunda görüldüğü gibi (sadece üstveri kayıtları dahil) verilen ön ekle etiketlenmiş kayıtların sayısını gösterir. “Kodlar” sütunu, verilen ön eke sahip kaç gerçek kod olduğunu gösterir. Bilinen kod ön eki “%(key)s” Daha fazla… Ön ek “%(prefix_label)s” ile eşleşen %(count)s kayıt kodlar kayıtlar “%%” kodun değeri ile değiştirilecek Anna’nın Arşivi'nde Ara Kodlar Belirli bir kod için URL: “%(url)s” Bu sayfanın oluşturulması biraz zaman alabilir, bu yüzden bir Cloudflare doğrulaması gerektirir. <a %(a_donate)s>Üyeler</a> doğrulamayı atlayabilir. Bildirilen kötüye kullanım: Daha iyi versiyon Bu kullanıcıyı kötüye kullanım veya uygunsuz davranış nedeniyle bildirmek istiyor musunuz? Dosya sorunu: %(file_issue)s gizli yorum Yanıtla Kötüye kullanımı bildir Bu kullanıcıyı kötüye kullanım nedeniyle bildirdiniz. Bu e-postaya gönderilen telif hakkı talepleri dikkate alınmayacaktır; bunun yerine formu kullanın. E-postayı göster Geri bildiriminizi ve sorularınızı memnuniyetle karşılıyoruz! Ancak, aldığımız spam ve anlamsız e-postaların miktarı nedeniyle, bizimle iletişime geçme koşullarını anladığınızı onaylamak için kutuları işaretleyin. Herhangi bir başka yolla bize ulaşmaya çalışırsanız, telif hakkı talepleriniz otomatik olarak silinecektir. DMCA / telif hakkı talepleri için <a %(a_copyright)s>bu formu</a> kullanın. İletişim e-postası Anna’nın Arşivi'ndeki URL'ler (gerekli). Her satıra bir tane. Lütfen yalnızca bir kitabın tam olarak aynı baskısını tanımlayan URL'leri ekleyin. Birden fazla kitap veya birden fazla baskı için iddiada bulunmak istiyorsanız, lütfen bu formu birden fazla kez gönderin. Birden fazla kitabı veya baskıyı bir araya getiren iddialar reddedilecektir. Adres (gerekli) Kaynak materyalin net tanımı (gerekli) E-posta (gerekli) Kaynak materyal URL'leri, her satıra bir tane (gerekli). İddianızı doğrulamamıza yardımcı olmak için mümkün olduğunca çok sayıda ekleyin (örneğin Amazon, WorldCat, Google Books, DOI). Kaynak materyalin ISBN'leri (varsa). Her satıra bir tane. Lütfen yalnızca telif hakkı iddiasında bulunduğunuz baskıyla tam olarak eşleşenleri ekleyin. Adınız (gerekli) ❌ Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin. ✅ Telif hakkı iddianızı gönderdiğiniz için teşekkür ederiz. En kısa sürede inceleyeceğiz. Başka bir tane göndermek için lütfen sayfayı yeniden yükleyin. <a %(a_openlib)s>Open Library</a> kaynak materyal URL'leri, her satıra bir tane. Lütfen kaynak materyalinizi Open Library'de aramak için bir dakikanızı ayırın. Bu, iddianızı doğrulamamıza yardımcı olacaktır. Telefon numarası (gerekli) Beyan ve imza (gerekli) İddia gönder Bir DCMA veya başka bir telif hakkı iddianız varsa, lütfen bu formu mümkün olduğunca kesin bir şekilde doldurun. Herhangi bir sorunla karşılaşırsanız, lütfen özel DMCA adresimizden bizimle iletişime geçin: %(email)s. Bu adrese gönderilen iddiaların işleme alınmayacağını, sadece sorular için olduğunu unutmayın. İddialarınızı göndermek için lütfen aşağıdaki formu kullanın. DMCA / Telif Hakkı İddia Formu Anna’nın Arşivinde örnek kayıt Anna’nın Arşivi tarafından Torrents Anna’nın Arşiv Konteynerleri formatı Üstveri içe aktarma betikleri Bu veri setini <a %(a_archival)s>arşivleme</a> veya <a %(a_llm)s>LLM eğitimi</a> amaçları için yansıtmakla ilgileniyorsanız, lütfen bizimle iletişime geçin. Son güncelleme: %(date)s Ana %(source)s web sitesi Üstveri dokümantasyonu (çoğu alan) Anna’nın Arşivi tarafından yansıtılan dosyalar: %(count)s (%(percent)s%%) Kaynaklar Toplam dosya sayısı: %(count)s Toplam dosya boyutu: %(size)s Bu veri hakkında blog yazımız <a %(duxiu_link)s>Duxiu</a>, <a %(superstar_link)s>SuperStar Dijital Kütüphane Grubu</a> tarafından oluşturulan taranmış kitapların devasa bir veritabanıdır. Çoğu akademik kitap olup, üniversiteler ve kütüphaneler için dijital olarak erişilebilir hale getirilmek üzere taranmıştır. İngilizce konuşan izleyicilerimiz için, <a %(princeton_link)s>Princeton</a> ve <a %(uw_link)s>Washington Üniversitesi</a> iyi genel bakışlar sunmaktadır. Ayrıca daha fazla arka plan bilgisi veren mükemmel bir makale de bulunmaktadır: <a %(article_link)s>“Çin Kitaplarını Dijitalleştirme: SuperStar DuXiu Bilimsel Arama Motoru Üzerine Bir Vaka Çalışması”</a>. Duxiu'dan gelen kitaplar uzun süredir Çin internetinde korsan olarak dağıtılmaktadır. Genellikle satıcılar tarafından bir dolardan daha az bir fiyata satılmaktadırlar. Genellikle Google Drive'ın Çin eşdeğeri kullanılarak dağıtılmakta olup, daha fazla depolama alanı sağlamak için sık sık hacklenmiştir. Bazı teknik detaylar <a %(link1)s>burada</a> ve <a %(link2)s>burada</a> bulunabilir. Kitaplar yarı kamuya açık olarak dağıtılmış olsa da, toplu olarak elde etmek oldukça zordur. Bu, yapılacaklar listemizde yüksek bir önceliğe sahipti ve tam zamanlı olarak birkaç ay ayırdık. Ancak, 2023'ün sonlarında inanılmaz, harika ve yetenekli bir gönüllü bize ulaştı ve bu işi zaten büyük bir maliyetle yaptığını söyledi. Bize tam koleksiyonu paylaştı, karşılığında sadece uzun vadeli koruma garantisi istedi. Gerçekten olağanüstü. Gönüllülerimizden daha fazla bilgi (ham notlar): <a %(a_href)s>Blog yazımızdan</a> uyarlanmıştır. DuXiu 读秀 %(count)s dosya Bu veri seti <a %(a_datasets_openlib)s>Open Library veri seti</a> ile yakından ilişkilidir. IA’nın Kontrollü Dijital Ödünç Verme Kütüphanesi'nden tüm üstveri ve dosyaların büyük bir kısmının kazınmasını içerir. Güncellemeler <a %(a_aac)s>Anna’nın Arşivi Konteyner formatında</a> yayınlanır. Bu kayıtlar doğrudan Open Library veri setinden alınmaktadır, ancak Open Library'de bulunmayan kayıtları da içermektedir. Ayrıca, yıllar içinde topluluk üyeleri tarafından kazınan bir dizi veri dosyamız da bulunmaktadır. Koleksiyon iki bölümden oluşmaktadır. Tüm verileri almak için her iki bölüme de ihtiyacınız var (torrents sayfasında üstü çizili olan eski torrentler hariç). Dijital Ödünç Verme Kütüphanesi ilk sürümümüz, <a %(a_aac)s>Anna’nın Arşiv Konteynerleri (AAC) formatı</a> üzerinde standartlaşmadan önce. Üstveri (json ve xml olarak), pdfler (acsm ve lcpdf dijital ödünç verme sistemlerinden) ve kapak küçük resimlerini içerir. AAC kullanılarak artımlı yeni sürümler. Sadece 2023-01-01 tarihinden sonraki zaman damgalarıyla üstveri içerir, çünkü geri kalan zaten “ia” tarafından kapsanmıştır. Ayrıca bu sefer acsm ve “bookreader” (IA’nın web okuyucusu) ödünç verme sistemlerinden tüm pdf dosyaları. İsmin tam olarak doğru olmamasına rağmen, bookreader dosyalarını ia2_acsmpdf_files koleksiyonuna eklemeye devam ediyoruz, çünkü bunlar birbirini dışlar. IA Kontrollü Dijital Ödünç Verme Dosyaların %%98'i aranabilir. Misyonumuz, dünyadaki tüm kitapları (ve makaleleri, dergileri vb.) arşivlemek ve geniş çapta erişilebilir hale getirmektir. Tüm kitapların geniş çapta insanlara sunulması gerektiğine inanıyoruz, böylece yedeklilik ve dayanıklılık sağlanır. Bu nedenle, çeşitli kaynaklardan dosyaları bir araya getiriyoruz. Bazı kaynaklar tamamen açıktır ve toplu olarak yansıtılabilir (örneğin Sci-Hub). Diğerleri kapalı ve korumacıdır, bu yüzden onların kitaplarını “özgürleştirmek” için çalışıyoruz. Diğerleri ise bu ikisinin arasında bir yerde yer alır. Tüm verilerimiz <a %(a_torrents)s>torrent</a> olarak indirilebilir ve tüm üstverilerimiz <a %(a_anna_software)s>oluşturulabilir</a> veya <a %(a_elasticsearch)s>ElasticSearch ve MariaDB veri tabanları</a> olarak indirilebilir. Ham veriler, <a %(a_dbrecord)s>bu</a> gibi JSON dosyaları aracılığıyla manuel olarak keşfedilebilir. Üstveri ISBN web sitesi Son güncelleme: %(isbn_country_date)s (%(link)s) Kaynaklar Uluslararası ISBN Ajansı, ulusal ISBN ajanslarına tahsis ettiği aralıkları düzenli olarak yayınlar. Bu sayede bu ISBN'nin hangi ülkeye, bölgeye veya dil grubuna ait olduğunu belirleyebiliriz. Şu anda bu veriyi dolaylı olarak, <a %(a_isbnlib)s>isbnlib</a> Python kütüphanesi aracılığıyla kullanıyoruz. ISBN ülke bilgisi Bu, Eylül 2022 boyunca isbndb.com'a yapılan birçok çağrının bir dökümüdür. Tüm ISBN aralıklarını kapsamaya çalıştık. Bu yaklaşık 30.9 milyon kayıttır. Web sitelerinde aslında 32.6 milyon kayıtları olduğunu iddia ediyorlar, bu yüzden bir şekilde bazılarını kaçırmış olabiliriz veya <em>onlar</em> bir şeyleri yanlış yapıyor olabilir. JSON yanıtları, sunucularından neredeyse ham olarak gelmektedir. Fark ettiğimiz bir veri kalitesi sorunu, “978-” ile başlamayan ISBN-13 numaraları için, hala “isbn” alanını içermeleri ve bu alanın sadece ilk 3 numaranın kesilip (ve kontrol rakamının yeniden hesaplanarak) ISBN-13 numarası olmasıdır. Bu açıkça yanlıştır, ancak onların bu şekilde yaptıkları görünüyor, bu yüzden değiştirmedik. Karşılaşabileceğiniz bir diğer potansiyel sorun, “isbn13” alanının kopyaları olmasıdır, bu yüzden bir veritabanında birincil anahtar olarak kullanamazsınız. “isbn13”+“isbn” alanları birleştirildiğinde benzersiz görünmektedir. Sürüm 1 (2022-10-31) Kurgu torrentleri geride (ancak kimlikler ~4-6M, Zlib torrentlerimizle örtüştükleri için torrentlenmemiş). Çizgi romanların yayınlanması hakkındaki blog yazımız Anna’nın Arşivi’nde çizgi roman torrentleri Farklı Library Genesis çatalları hakkında arka plan bilgisi için, <a %(a_libgen_rs)s>Libgen.rs</a> sayfasına bakın. Libgen.li, Libgen.rs ile aynı içerik ve üstverinin çoğunu içerir, ancak bunun üzerine bazı koleksiyonlar eklenmiştir, yani çizgi romanlar, dergiler ve standart belgeler. Ayrıca <a %(a_scihub)s>Sci-Hub</a>'ı üstveri ve arama motoruna entegre etmiştir, bu da veritabanımız için kullandığımız şeydir. Bu kütüphanenin üstverisi <a %(a_libgen_li)s>libgen.li'de</a> ücretsiz olarak mevcuttur. Ancak, bu sunucu yavaştır ve kesilen bağlantıları devam ettirmeyi desteklemez. Aynı dosyalar <a %(a_ftp)s>bir FTP sunucusunda</a> da mevcuttur, bu daha iyi çalışır. Kurgu dışı kitaplar da farklılaşmış görünüyor, ancak yeni torrentler olmadan. Bu durumun 2022'nin başlarından beri meydana geldiği anlaşılıyor, ancak bunu doğrulamadık. Libgen.li yöneticisine göre, “fiction_rus” (Rus kurgusu) koleksiyonu, düzenli olarak yayınlanan <a %(a_booktracker)s>booktracker.org</a> torrentleri, özellikle <a %(a_flibusta)s>flibusta</a> ve <a %(a_librusec)s>lib.rus.ec</a> torrentleri ile kapsanmalıdır (biz bunları <a %(a_torrents)s>burada</a> yansıtıyoruz, ancak hangi torrentlerin hangi dosyalara karşılık geldiğini henüz belirlemedik). Kurgusal koleksiyonun kendi torrentleri vardır (<a %(a_href)s>Libgen.rs</a>'den farklı) ve %(start)s tarihinde başlamaktadır. Torrentleri olmayan belirli aralıklar (örneğin, f_3463000'den f_4260000'e kadar olan kurgu aralıkları) muhtemelen Z-Library (veya diğer kopya) dosyalarıdır, ancak bu aralıklardaki lgli-özel dosyalar için bazı kopya kaldırma işlemleri yapıp torrentler oluşturmak isteyebiliriz. Tüm koleksiyonların istatistikleri <a %(a_href)s>libgen'in web sitesinde</a> bulunabilir. Ek içeriğin çoğu için torrentler mevcuttur, özellikle çizgi romanlar, dergiler ve standart belgeler için torrentler Anna’nın Arşivi ile işbirliği içinde yayınlanmıştır. “libgen.is”e atıfta bulunan torrent dosyalarının açıkça <a %(a_libgen)s>Libgen.rs</a>’nin aynaları olduğunu unutmayın (“.is” Libgen.rs tarafından kullanılan farklı bir alan adıdır). Üstveriyi kullanmada yardımcı bir kaynak <a %(a_href)s>bu sayfadır</a>. %(icon)s Onların “fiction_rus” koleksiyonu (Rus kurgusu) için özel torrentler yoktur, ancak başkalarının torrentleriyle kapsanır ve biz bir <a %(fiction_rus)s>yansıtma</a> tutarız. Anna’nın Arşivi'nde Rus kurgusu torrentleri Anna’nın Arşivi’nde kurgu torrentleri Tartışma forumu Üstveri FTP üzerinden üstveri Anna’nın Arşivi’nde dergi torrentleri Üstveri alan bilgisi Diğer torrentlerin aynası (ve benzersiz kurgu ve çizgi roman torrentleri) Anna’nın Arşivi'nde standart belge torrentleri Libgen.li Anna’nın Arşivi tarafından torrentler (kitap kapakları) Library Genesis, verilerini toplu olarak torrentler aracılığıyla cömertçe sunmasıyla bilinir. Libgen koleksiyonumuz, doğrudan yayınlamadıkları yardımcı verilerden oluşur ve onlarla ortaklık içinde sunulmaktadır. Library Genesis ile çalışan herkese bizimle işbirliği yaptıkları için çok teşekkür ederiz! Kitap kapakları sürümü hakkında blogumuz Bu sayfa “.rs” versiyonu hakkındadır. Hem üstverisini hem de kitap kataloğunun tam içeriğini tutarlı bir şekilde yayınlamasıyla bilinir. Kitap koleksiyonu kurgu ve kurgu dışı bölümlerine ayrılmıştır. Üstveriyi kullanmada yardımcı bir kaynak <a %(a_metadata)s>bu sayfadır</a> (IP aralıklarını engeller, VPN gerekebilir). 2024-03 itibarıyla, yeni torrentler <a %(a_href)s>bu forum başlığında</a> yayınlanmaktadır (IP aralıklarını engeller, VPN gerekebilir). Anna’nın Arşivi'nde Kurgu torrentler Libgen.rs Kurgu torrentler Libgen.rs Tartışma forumu Libgen.rs Üstveri Libgen.rs üstveri alan bilgisi Libgen.rs Kurgu Dışı torrentler Anna’nın Arşivi'nde Kurgu Dışı torrentler Kurgu bir kitap için %(example)s. Bu <a %(blog_post)s>ilk sürüm</a> oldukça küçük: Libgen.rs çatalından yaklaşık 300GB kitap kapağı, hem kurgu hem de kurgu dışı. Bunlar, libgen.rs'de göründükleri şekilde organize edilmiştir, örneğin: Kurgu dışı bir kitap için %(example)s. Z-Library koleksiyonunda olduğu gibi, hepsini büyük bir .tar dosyasına koyduk, eğer dosyaları doğrudan sunmak isterseniz <a %(a_ratarmount)s>ratarmount</a> kullanarak monte edebilirsiniz. Sürüm 1 (%(date)s) Farklı Library Genesis (veya “Libgen”) çatallarının kısa hikayesi, zamanla Library Genesis ile ilgilenen farklı kişilerin anlaşmazlığa düşmesi ve yollarını ayırmasıdır. Bu <a %(a_mhut)s>forum gönderisine</a> göre, Libgen.li başlangıçta “http://free-books.dontexist.com” adresinde barındırılıyordu. “.fun” versiyonu, orijinal kurucu tarafından oluşturuldu. Daha dağıtılmış yeni bir versiyon lehine yeniden düzenleniyor. <a %(a_li)s>“.li” versiyonu</a> büyük bir çizgi roman koleksiyonuna ve henüz torrentler aracılığıyla toplu indirme için mevcut olmayan diğer içeriklere sahiptir. Ayrı bir kurgu kitapları torrent koleksiyonuna sahiptir ve veritabanında <a %(a_scihub)s>Sci-Hub</a>’ın üstverisini içerir. “.rs” versiyonu çok benzer verilere sahiptir ve koleksiyonlarını toplu torrentler halinde en tutarlı şekilde yayınlar. Kabaca “kurgu” ve “kurgu dışı” bölümlerine ayrılmıştır. İlk olarak “http://gen.lib.rus.ec” adresinde yer alıyordu. <a %(a_zlib)s>Z-Library</a> bir anlamda Library Genesis’in bir çatalıdır, ancak projeleri için farklı bir isim kullanmışlardır. Libgen.rs Koleksiyonumuzu, ISBN numaraları veya diğer alanları kullanarak dosyalarla eşleştirebileceğimiz sadece üstveri kaynaklarıyla da zenginleştiriyoruz. Aşağıda bu kaynakların bir genel bakışı bulunmaktadır. Yine, bu kaynakların bazıları tamamen açıkken, diğerlerini kazımak zorundayız. Üstveri aramasında, orijinal kayıtları gösterdiğimizi unutmayın. Kayıtları birleştirme yapmıyoruz. Sadece üstveri kaynakları Open Library, Internet Archive tarafından dünyanın her kitabını kataloglamak için başlatılan açık kaynaklı bir projedir. Dünyanın en büyük kitap tarama operasyonlarından birine sahiptir ve dijital ödünç verme için birçok kitabı mevcuttur. Kitap üstveri kataloğu ücretsiz olarak indirilebilir ve Anna’nın Arşivi'nde yer almaktadır (ancak şu anda aramada mevcut değildir, yalnızca bir Open Library ID'si ararsanız hariç). Open Library Çiftler hariç Son güncelleme Dosya sayısının yüzdeleri AA tarafından yansıtılan %% / mevcut torrentler Boyut Kaynak Aşağıda Anna’nın Arşivi'ndeki dosyaların kaynaklarına hızlı bir genel bakış bulunmaktadır. Gölge kütüphaneler genellikle birbirlerinden veri senkronize ettikleri için, kütüphaneler arasında önemli bir örtüşme vardır. Bu yüzden sayılar tam olarak toplamı vermiyor. “Anna’nın Arşivi tarafından yansıtılan ve tohumlanan” yüzdesi, kaç dosyayı kendimiz yansıttığımızı gösterir. Bu dosyaları toplu olarak torrentler aracılığıyla tohumluyoruz ve doğrudan indirme için ortak web siteleri aracılığıyla erişilebilir hale getiriyoruz. Genel Bakış Toplam Anna’nın Arşivi'ndeki Torrentler Sci-Hub hakkında daha fazla bilgi için <a %(a_scihub)s>resmi web sitesine</a>, <a %(a_wikipedia)s>Wikipedia sayfasına</a> ve bu <a %(a_radiolab)s>podcast röportajına</a> başvurabilirsiniz. Sci-Hub'ın <a %(a_reddit)s>2021'den beri dondurulduğunu</a> unutmayın. Daha önce de dondurulmuştu, ancak 2021'de birkaç milyon makale eklendi. Yine de, Libgen'in “scimag” koleksiyonlarına sınırlı sayıda makale ekleniyor, ancak yeni toplu torrentleri haklı çıkaracak kadar değil. Sci-Hub üstverisini, <a %(a_libgen_li)s>Libgen.li</a> tarafından sağlanan “scimag” koleksiyonunda kullanıyoruz. Ayrıca <a %(a_dois)s>dois-2022-02-12.7z</a> veri setini de kullanıyoruz. “smarch” torrentlerinin <a %(a_smarch)s>kullanımdan kaldırıldığını</a> ve bu nedenle torrent listemize dahil edilmediğini unutmayın. Libgen.li'deki Torrentler Libgen.rs'deki Torrentler Üstveri ve torrentler Reddit'teki Güncellemeler Podcast röportajı Wikipedia sayfası Sci-Hub Sci-Hub: 2021'den beri dondurulmuş; çoğu torrentler aracılığıyla mevcut Libgen.li: o zamandan beri küçük eklemeler</div> Bazı kaynak kütüphaneler, verilerini torrentler aracılığıyla toplu olarak paylaşmayı teşvik ederken, diğerleri koleksiyonlarını kolayca paylaşmaz. İkinci durumda, Anna’nın Arşivi koleksiyonlarını kazımaya ve erişilebilir hale getirmeye çalışır (bkz. <a %(a_torrents)s>Torrentler</a> sayfamız). Ayrıca, kaynak kütüphanelerin paylaşmaya istekli olduğu ancak bunu yapacak kaynaklara sahip olmadığı durumlar da vardır. Bu durumlarda da yardımcı olmaya çalışıyoruz. Aşağıda, farklı kaynak kütüphanelerle nasıl etkileşimde bulunduğumuza dair bir genel bakış bulunmaktadır. Kaynak kütüphaneler %(icon)s Çin internetinde dağılmış çeşitli dosya veritabanları; ancak genellikle ücretli veritabanları %(icon)s Çoğu dosya yalnızca premium BaiduYun hesapları kullanılarak erişilebilir; yavaş indirme hızları. %(icon)s Anna’nın Arşivi, <a %(duxiu)s>DuXiu dosyaları</a> koleksiyonunu yönetiyor %(icon)s Çin internetinde dağılmış çeşitli üstveri veritabanları; ancak genellikle ücretli veritabanları %(icon)s Tüm koleksiyonları için kolayca erişilebilir üstveri dökümleri mevcut değil. %(icon)s Anna’nın Arşivi bir <a %(duxiu)s>DuXiu üstveri</a> koleksiyonunu yönetir Dosyalar %(icon)s Sadece sınırlı bir süre için ödünç alınabilir dosyalar, çeşitli erişim kısıtlamaları ile %(icon)s Anna’nın Arşivi bir <a %(ia)s>IA dosyaları</a> koleksiyonunu yönetiyor %(icon)s <a %(openlib)s>Open Library veritabanı dökümleri</a> aracılığıyla bazı üstveriler mevcut, ancak bunlar tüm IA koleksiyonunu kapsamıyor %(icon)s Tüm koleksiyonları için kolayca erişilebilir üstveri dökümleri mevcut değil %(icon)s Anna’nın Arşivi bir <a %(ia)s>IA üstveri</a> koleksiyonunu yönetiyor Son güncelleme %(icon)s Anna'nın Arşivi ve Libgen.li, <a %(comics)s>çizgi romanlar</a>, <a %(magazines)s>dergiler</a>, <a %(standarts)s>standart belgeler</a> ve <a %(fiction)s>kurgu (Libgen.rs'den ayrılmış)</a> koleksiyonlarını işbirliği içinde yönetir. %(icon)s Kurgu Dışı torrentler Libgen.rs ile paylaşılıyor (ve <a %(libgenli)s>ile kopyalanıyor</a>). %(icon)s Üç aylık <a %(dbdumps)s>HTTP veritabanı dökümleri</a> %(icon)s <a %(nonfiction)s>Kurgu Dışı</a> ve <a %(fiction)s>Kurgu</a> için otomatik torrentler %(icon)s Anna’nın Arşivi bir <a %(covers)s>kitap kapağı torrentleri</a> koleksiyonunu yönetir %(icon)s Günlük <a %(dbdumps)s>HTTP veritabanı dökümleri</a> Üstveri %(icon)s Aylık <a %(dbdumps)s>veritabanı dökümleri</a> %(icon)s Veri torrentlerine <a %(scihub1)s>buradan</a>, <a %(scihub2)s>buradan</a> ve <a %(libgenli)s>buradan</a> ulaşabilirsiniz. %(icon)s Libgen’in “scimag”ine bazı yeni dosyalar <a %(libgenrs)s>ekleniyor</a>, ancak yeni torrentler için yeterli değil %(icon)s Sci-Hub, 2021'den beri yeni dosyaları dondurdu. %(icon)s Üstveri dökümleri <a %(scihub1)s>burada</a> ve <a %(scihub2)s>burada</a> mevcuttur, ayrıca <a %(libgenli)s>Libgen.li veritabanı</a>nın bir parçası olarak (bizim kullandığımız) Kaynak %(icon)s Çeşitli daha küçük veya tek seferlik kaynaklar. İnsanları önce diğer gölge kütüphanelere yükleme yapmaya teşvik ediyoruz, ancak bazen insanların diğerlerinin ayıklaması için çok büyük, ancak kendi kategorilerini oluşturacak kadar büyük olmayan koleksiyonları oluyor. %(icon)s Toplu olarak doğrudan mevcut değil, kazımaya karşı korumalı %(icon)s Anna’nın Arşivi bir <a %(worldcat)s>OCLC (WorldCat) üstveri</a> koleksiyonunu yönetir %(icon)s Anna’nın Arşivi ve Z-Library, <a %(metadata)s>Z-Library üstveri</a> ve <a %(files)s>Z-Library dosyaları</a> koleksiyonunu ortaklaşa yönetiyor Veri Setleri Yukarıdaki tüm kaynakları birleştirerek bu web sitesine hizmet etmek için kullandığımız birleştirilmiş bir veritabanı oluşturuyoruz. Bu birleştirilmiş veritabanı doğrudan erişilebilir değildir, ancak Anna’nın Arşivi tamamen açık kaynak olduğundan, ElasticSearch ve MariaDB veritabanları olarak oldukça kolay bir şekilde <a %(a_generated)s>oluşturulabilir</a> veya <a %(a_downloaded)s>indirilebilir</a>. Bu sayfadaki betikler, yukarıda belirtilen kaynaklardan gerekli tüm üstverileri otomatik olarak indirecektir. Bu betikleri yerel olarak çalıştırmadan önce verilerimizi keşfetmek isterseniz, diğer JSON dosyalarına bağlantı veren JSON dosyalarımıza bakabilirsiniz. <a %(a_json)s>Bu dosya</a> iyi bir başlangıç noktasıdır. Birleştirilmiş veritabanı Anna’nın Arşivi tarafından Torrents göz at ara Çeşitli daha küçük veya tek seferlik kaynaklar. İnsanları önce diğer gölge kütüphanelere yükleme yapmaya teşvik ediyoruz, ancak bazen insanların diğerlerinin ayıklaması için çok büyük, ancak kendi kategorilerini oluşturacak kadar büyük olmayan koleksiyonları oluyor. Genel Bakış <a %(a1)s>veri setleri sayfası</a>. <a %(a_href)s>aaaaarg.fail</a> adresinden. Oldukça eksiksiz görünüyor. Gönüllümüz “cgiym” tarafından sağlanmıştır. <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentinden. Mevcut makale koleksiyonlarıyla oldukça yüksek bir örtüşme var, ancak çok az MD5 eşleşmesi var, bu yüzden tamamen tutmaya karar verdik. Gönüllü <q>j</q> tarafından <q>iRead eBooks</q> (fonetik olarak <q>ai rit i-books</q>; airitibooks.com) taraması. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>airitibooks</q> metadata'sına karşılık gelir. Bir koleksiyondan <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Kısmen orijinal kaynaktan, kısmen the-eye.eu'dan, kısmen diğer aynalardan. Özel bir kitap torrent sitesi olan <a %(a_href)s>Bibliotik</a>’ten (genellikle “Bib” olarak anılır), kitaplar isimlerine göre (A.torrent, B.torrent) torrentlere paketlenmiş ve the-eye.eu üzerinden dağıtılmıştır. Gönüllümüz “bpb9v” tarafından. <a %(a_href)s>CADAL</a> hakkında daha fazla bilgi için <a %(a_duxiu)s>DuXiu veri seti sayfamızdaki</a> notlara bakın. Gönüllümüz “bpb9v”den daha fazlası, çoğunlukla DuXiu dosyaları, ayrıca “WenQu” ve “SuperStar_Journals” klasörleri (SuperStar, DuXiu’nun arkasındaki şirkettir). Gönüllümüz “cgiym” tarafından, çeşitli kaynaklardan (alt dizinler olarak temsil edilen) Çin metinleri, <a %(a_href)s>China Machine Press</a> (önemli bir Çinli yayıncı) dahil. Gönüllümüz “cgiym”den Çin dışı koleksiyonlar (alt dizinler olarak temsil edilen). Gönüllü <q>cm</q> tarafından Çin mimarisi hakkında kitapların taraması: <q>Yayın evindeki bir ağ açığını kullanarak elde ettim, ancak o açık artık kapatıldı</q>. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>chinese_architecture</q> metadata'sına karşılık gelir. Akademik yayın evi <a %(a_href)s>De Gruyter</a>’den kitaplar, birkaç büyük torrentten toplanmıştır. <a %(a_href)s>docer.pl</a>’nin kazınması, kitaplar ve diğer yazılı eserler üzerine odaklanan bir Polonya dosya paylaşım sitesi. 2023’ün sonlarında gönüllü “p” tarafından kazınmıştır. Orijinal siteden iyi üstverimiz yok (dosya uzantıları bile yok), ancak kitap benzeri dosyaları filtreledik ve genellikle üstveriyi dosyaların kendisinden çıkarabildik. DuXiu epubları, doğrudan DuXiu'dan, gönüllü “w” tarafından toplanmıştır. Yalnızca son DuXiu kitapları doğrudan e-kitaplar aracılığıyla mevcuttur, bu yüzden bunların çoğu yeni olmalıdır. Gönüllü “m”den kalan DuXiu dosyaları, DuXiu’nun özel PDG formatında olmayanlar (ana <a %(a_href)s>DuXiu veri seti</a>). Ne yazık ki, bu kaynakları dosya yolunda korumadan birçok orijinal kaynaktan toplanmıştır. <span></span> <span></span> <span></span> Gönüllü <q>do no harm</q> tarafından erotik kitapların taraması. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>hentai</q> metadata'sına karşılık gelir. <span></span> <span></span> Bir Japon Manga yayıncısından gönüllü “t” tarafından kazınan koleksiyon. <a %(a_href)s>Longquan’ın seçilmiş yargı arşivleri</a>, gönüllü “c” tarafından sağlanmıştır. <a %(a_href)s>magzdb.org</a>’nin kazınması, Library Genesis’in bir müttefiki (libgen.rs ana sayfasında bağlantılıdır) ancak dosyalarını doğrudan sağlamak istememiştir. 2023’ün sonlarında gönüllü “p” tarafından elde edilmiştir. <span></span> Çeşitli küçük yüklemeler, kendi alt koleksiyonları olarak çok küçük, ancak dizinler olarak temsil edilmiştir. Rus dosya paylaşım sitesi AvaxHome'dan e-kitaplar. Gazete ve dergi arşivi. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>newsarch_magz</q> metadata'sına karşılık gelir. <a %(a1)s>Felsefe Dokümantasyon Merkezi</a> taraması. Gönüllü “o” tarafından orijinal yayın (“sahne”) web sitelerinden doğrudan toplanan Polonya kitapları koleksiyonu. <a %(a_href)s>shuge.org</a> sitesinin gönüllüler “cgiym” ve “woz9ts” tarafından birleştirilmiş koleksiyonları. <span></span> <a %(a_href)s>“Trantor İmparatorluk Kütüphanesi”</a> (kurgusal kütüphaneden esinlenerek adlandırılmış), 2022 yılında gönüllü “t” tarafından kazınmıştır. <span></span> <span></span> <span></span> Gönüllü “woz9ts”den alt-alt koleksiyonlar (dizinler olarak temsil edilen): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (Tayvan’da <a %(a_sikuquanshu)s>Dizhi(迪志)</a> tarafından), mebook (mebook.cc, 我的小书屋, benim küçük kitap odam — woz9ts: “Bu site, bazıları sahibi tarafından kendisi tarafından dizilmiş yüksek kaliteli e-kitap dosyalarını paylaşmaya odaklanıyor. Sahibi 2019’da <a %(a_arrested)s>tutuklandı</a> ve biri onun paylaştığı dosyaların bir koleksiyonunu yaptı.”). Gönüllü “woz9ts” tarafından sağlanan ve DuXiu özel PDG formatında olmayan kalan DuXiu dosyaları (hala PDF'e dönüştürülmesi gerekiyor). “Yükleme” koleksiyonu, AACID'ler ve torrent adlarında belirtilen daha küçük alt koleksiyonlara bölünmüştür. Tüm alt koleksiyonlar önce ana koleksiyona karşı yinelenmiştir, ancak üstveri “upload_records” JSON dosyaları hala orijinal dosyalara birçok referans içermektedir. Çoğu alt koleksiyondan kitap dışı dosyalar da kaldırılmıştır ve genellikle “upload_records” JSON'da <em>not</em> belirtilmemiştir. Alt koleksiyonlar şunlardır: Notlar Alt Koleksiyon Birçok alt koleksiyon, kendileri alt-alt koleksiyonlardan (örneğin, farklı orijinal kaynaklardan) oluşur ve bunlar “dosya yolu” alanlarında dizinler olarak temsil edilir. Anna’nın Arşivi'ne yüklemeler Bu veri hakkında blog yazımız <a %(a_worldcat)s>WorldCat</a>, dünya çapındaki kütüphanelerden üstveri kayıtlarını toplayan kar amacı gütmeyen <a %(a_oclc)s>OCLC</a> tarafından oluşturulmuş özel bir veritabanıdır. Muhtemelen dünyanın en büyük kütüphane üstveri koleksiyonudur. Ekim 2023'te OCLC (WorldCat) veritabanının kapsamlı bir taramasını <a %(a_scrape)s>yayınladık</a>, <a %(a_aac)s>Anna’nın Arşivi Konteynerleri formatında</a>. Ekim 2023, ilk sürüm: OCLC (WorldCat) Anna’nın Arşivi'ndeki Torrentler Anna’nın Arşivi'nde örnek kayıt (orijinal koleksiyon) Anna’nın Arşivi'nde örnek kayıt (“zlib3” koleksiyonu) Anna’nın Arşivi tarafından Torrents (üstveri + içerik) Sürüm 1 hakkında blog yazısı Sürüm 2 hakkında blog yazısı 2022’nin sonlarında, Z-Kütüphane’nin iddia edilen kurucuları tutuklandı ve alan adları Amerika Birleşik Devletleri yetkilileri tarafından ele geçirildi. O zamandan beri web sitesi yavaş yavaş çevrimiçi hale gelmeye başladı. Şu anda kimin işlettiği bilinmiyor. Şubat 2023 itibarıyla güncelleme. Z-Kütüphane, <a %(a_href)s>Library Genesis</a> topluluğunda kök salmıştır ve başlangıçta verileriyle başlatılmıştır. O zamandan beri oldukça profesyonelleşmiş ve çok daha modern bir arayüze sahip olmuştur. Bu nedenle, hem web sitelerini geliştirmeye devam etmek için maddi olarak hem de yeni kitap bağışları alarak çok daha fazla bağış alabilmektedirler. Library Genesis’e ek olarak büyük bir koleksiyon oluşturmuşlardır. Koleksiyon üç bölümden oluşmaktadır. İlk iki bölümün orijinal açıklama sayfaları aşağıda korunmuştur. Tüm verileri almak için üç bölüme de ihtiyacınız var (torrents sayfasında üstü çizili olanlar hariç). %(title)s: ilk yayınımız. Bu, o zamanlar “Korsan Kütüphane Aynası” (“pilimi”) olarak adlandırılan şeyin ilk yayınıydı. %(title)s: ikinci sürüm, bu sefer tüm dosyalar .tar dosyalarına sarılmış. %(title)s: Z-Library ekibiyle işbirliği içinde şimdi yayınlanan <a %(a_href)s>Anna’nın Arşiv Konteynerleri (AAC) formatı</a> kullanılarak artımlı yeni sürümler. İlk ayna, 2021 ve 2022 yılları boyunca titizlikle elde edildi. Bu noktada biraz güncelliğini yitirmiş durumda: koleksiyonun Haziran 2021'deki durumunu yansıtıyor. Bunu gelecekte güncelleyeceğiz. Şu anda bu ilk sürümü çıkarmaya odaklanmış durumdayız. Library Genesis zaten kamuya açık torrentlerle korunduğu ve Z-Library'e dahil edildiği için, Haziran 2022'de Library Genesis'e karşı temel bir yinelenen dosya temizliği yaptık. Bunun için MD5 hashlerini kullandık. Kütüphanede muhtemelen aynı kitapla birden fazla dosya formatı gibi daha fazla yinelenen içerik var. Bu, doğru bir şekilde tespit edilmesi zor, bu yüzden yapmıyoruz. Yinelenen dosya temizliğinden sonra, toplamda 7TB'ın biraz altında, 2 milyondan fazla dosya kaldı. Koleksiyon iki bölümden oluşur: üstverinin MySQL “.sql.gz” dökümü ve her biri yaklaşık 50-100GB olan 72 torrent dosyası. Üstveri, Z-Library web sitesi tarafından bildirilen verileri (başlık, yazar, açıklama, dosya türü) içerir, ayrıca gözlemlediğimiz gerçek dosya boyutu ve md5sum'u da içerir, çünkü bazen bunlar uyuşmaz. Z-Library'nin kendisinin yanlış üstveriye sahip olduğu dosya aralıkları var gibi görünüyor. Ayrıca bazı izole durumlarda yanlış indirilen dosyalarımız olabilir, bunları tespit edip gelecekte düzeltmeye çalışacağız. Büyük torrent dosyaları, dosya adı olarak Z-Library ID'si ile gerçek kitap verilerini içerir. Dosya uzantıları, üstveri dökümü kullanılarak yeniden oluşturulabilir. Koleksiyon, kurgusal olmayan ve kurgusal içeriklerin bir karışımıdır (Library Genesis'teki gibi ayrılmamıştır). Kalite de geniş ölçüde değişkenlik göstermektedir. Bu ilk sürüm artık tamamen kullanılabilir durumda. Torrent dosyalarının yalnızca Tor aynamız üzerinden erişilebilir olduğunu unutmayın. Sürüm 1 (%(date)s) Bu tek bir ekstra torrent dosyasıdır. Yeni bir bilgi içermez, ancak hesaplanması zaman alabilecek bazı veriler içerir. Bu, sahip olmayı uygun hale getirir çünkü bu torrentin indirilmesi genellikle sıfırdan hesaplamaktan daha hızlıdır. Özellikle, <a %(a_href)s>ratarmount</a> ile kullanım için tar dosyaları için SQLite dizinleri içerir. Sürüm 2 ek bölümü (%(date)s) Son aynadan Ağustos 2022'ye kadar Z-Library'ye eklenen tüm kitapları aldık. Ayrıca, ilk seferde kaçırdığımız bazı kitapları da geri dönüp kazıdık. Sonuç olarak, bu yeni koleksiyon yaklaşık 24TB boyutunda. Yine, bu koleksiyon Library Genesis'e karşı yinelenmiş durumda, çünkü bu koleksiyon için zaten torrentler mevcut. Veriler, ilk sürüme benzer şekilde organize edilmiştir. Üstverinin MySQL “.sql.gz” dökümü bulunmaktadır ve bu döküm, ilk sürümden tüm üstveriyi de içerir, böylece onu geçersiz kılar. Ayrıca bazı yeni sütunlar ekledik: Bunu geçen sefer de belirtmiştik, ancak açıklığa kavuşturmak için: “filename” ve “md5” dosyanın gerçek özellikleridir, oysa “filename_reported” ve “md5_reported” Z-Library'den kazıdıklarımızdır. Bazen bu ikisi birbiriyle uyuşmaz, bu yüzden her ikisini de dahil ettik. Bu sürüm için, daha eski MySQL sürümleriyle uyumlu olması gereken “utf8mb4_unicode_ci” sıralamasını değiştirdik. Veri dosyaları geçen seferkine benzer, ancak çok daha büyük. Daha küçük torrent dosyaları oluşturmakla uğraşamadık. “pilimi-zlib2-0-14679999-extra.torrent” son sürümde kaçırdığımız tüm dosyaları içerirken, diğer torrentler tamamen yeni ID aralıklarıdır.  <strong>Güncelleme %(date)s:</strong> Çoğu torrentimizi çok büyük yaptık, bu da torrent istemcilerinin zorlanmasına neden oldu. Onları kaldırdık ve yeni torrentler yayınladık. <strong>Güncelleme %(date)s:</strong> Hâlâ çok fazla dosya vardı, bu yüzden onları tar dosyalarına sardık ve yeni torrentleri tekrar yayınladık. %(key)s: Bu dosyanın, kurgusal olmayan veya kurgu koleksiyonunda (md5 ile eşleştirilmiş) Library Genesis'te zaten bulunup bulunmadığı. %(key)s: bu dosyanın hangi torrentte olduğunu belirtir. %(key)s: kitabı indiremeyince ayarlanır. Sürüm 2 (%(date)s) Zlib sürümleri (orijinal açıklama sayfaları) Tor alan adı Ana web sitesi Z-Kütüphane kazıması Z-Library'deki “Çince” koleksiyonu, farklı MD5'lerle bizim DuXiu koleksiyonumuzla aynı görünüyor. Çoğaltmayı önlemek için bu dosyaları torrentlerden hariç tutuyoruz, ancak yine de arama dizinimizde gösteriyoruz. Üstveri %(percentage)s ilave hızlı indirme edindiniz, çünkü %(profile_link)s tarafından yönlendirildiniz. Bu bütün üyelik süresine geçerli. Bağış Yap Katıl Seçildi %(percentage)s%%'e varan indirimler Alipay uluslararası kredi/banka kartlarını destekler. Daha fazla bilgi için <a %(a_alipay)s>bu kılavuza</a> bakın. Kredi/banka kartınızı kullanarak bize Amazon.com hediye kartları gönderebilirsiniz. Kredi/banka kartlarını kullanarak kripto satın alabilirsiniz. WeChat (Weixin Pay) uluslararası kredi/banka kartlarını destekler. WeChat uygulamasında, “Ben => Hizmetler => Cüzdan => Kart Ekle"ye gidin. Bunu görmüyorsanız, “Ben => Ayarlar => Genel => Araçlar => Weixin Pay => Etkinleştir”i kullanarak etkinleştirin. (Coinbase'den Ethereum gönderirken kullanın) kopyalandı! kopyala (en düşük miktar) (uyarı: yüksek minimum miktar) -%(percentage)s%% 12 ay 1 ay 24 ay 3 ay 48 ay 6 ay 96 ay Ne kadar süre için abone olacağını seç. <div %(div_monthly_cost)s></div><span %(span_discount)s></span> <div %(div_after)s>indirimden sonra</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 ay için 1 ay için 24 ay için 3 ay için 48 ay boyunca geçerli 6 ay için 96 ay boyunca geçerli %(monthly_cost)s / ay bize ulaşın Doğrudan <strong>SFTP</strong> sunucuları Kurumsal düzeyde bağış yapın veya yeni koleksiyonlar için takas yapın (örneğin, yeni taramalar, OCR edilmiş datasetler). Uzman Erişimi <strong>Sınırsız</strong> yüksek hızlı erişim <div %(div_question)s>Üyeliğimi yükseltebilir miyim veya birden fazla üyelik alabilir miyim?</div> <div %(div_question)s>Üye olmadan bağış yapabilir miyim?</div> Tabii ki. Herhangi bir miktarda bağışı bu Monero (XMR) adresine kabul ediyoruz: %(address)s. <div %(div_question)s>Aylık aralıklar ne anlama geliyor?</div> Tüm indirimleri uygulayarak, örneğin bir aydan daha uzun bir süre seçerek, bir aralığın alt tarafına ulaşabilirsiniz. <div %(div_question)s>Üyelikler otomatik olarak yenileniyor mu?</div> Üyelikler otomatik olarak <strong>yenilenmiyor</strong>. İstediğiniz kadar uzun ya da kısa süre katılabilirsiniz. <div %(div_question)s>Bağışları neye harcıyorsunuz?</div> Bağışların %%100'ü dünyanın bilgisi ve kültürünü korumaya ve erişilebilir hale getirmeye gidiyor. An itibariyle çoğunlukla sunucular, depolama ve bant genişliği. Hiçbir ekip üyesine bizzat para gitmiyor. <div %(div_question)s>Büyük miktarda bağış yapabilir miyim?</div> Harika olur! Bin doların üstündeki bağışlarınız için lütfen bize, %(email)s adresinden direkt iletişim kurunuz. <div %(div_question)s>Başka ödeme yöntemleri mevcut mu?</div> Şimdilik hayır. Arşivlerimizin var olmasını istemeyen bir çok kişi olduğu için dikkatli davranmamız gerekiyor. Eğer daha iyi bir yöntemi biliyorsanız bizimle iletişime geçin: %(email)s. Bağış SSS Halihazırda devam eden <a %(a_donation)s>var olan bağış</a>'ın var. Lütfen yeni bir bağış yapmadan önce bu bağışı bitir ya da iptal et. <a %(a_all_donations)s>Tüm bağışlarımı gör</a> 5000$'ın üstündeki bağışlar için lütfen bizimle direkt olarak %(email)s üzerinden iletişime geçin. Varlıklı bireylerden ya da enstitülerden büyük bağışları başımızın üstünde tutuyoruz.  Bu sayfadaki üyeliklerin “aylık” olduğuna dikkat edin, bunlar tek seferlik (tekrarlanmayan) bağışlardır. <a %(faq)s>Bağış SSS</a>ye bakın. Anna’s Archive; kâr amacı gütmeyen, açık kaynaklı, açık verili bir projedir. Bağış yaparak ve üyemiz olarak işlemlerimize ve geliştirme sürecimize destek olursun. Tüm üyelerimize: Devam etmemizi sağladığınız için teşekkürler!❤️ Daha fazla bilgi için <a %(a_donate)s>Bağış SSS</a>'e göz atınız. Üye olmak için, lütfen <a %(a_login)s>Giriş yap ya da Kaydol</a>. Desteğiniz için teşekkürler! $%(cost)s / ay Ödeme sırasında bir hata yaptıysanız, geri ödeme yapamayız, ancak bu durumu düzeltmeye çalışacağız. Paypal uygulamanızda ya da websitesinde "Crypto" sayfasını bulun. Bu genelde "Finans" bölümünün altında bulunur. Paypal uygulamanızda ya da websitesinde “Bitcoin” sayfasına gidin. “Para gönder” butonuna %(transfer_icon)s, basın ve ardından “Gönder”. Alipay Alipay / WeChat Amazon Hediye Kartı %(amazon)s hediye kartı Banka kartı Banka kartı (uygulama kullanarak) Binance Kredi kartı/banka kartı/Apple/Google (BMC) Cash App Kredi/banka kartı Kredi/banka kartı 2 Kredi/banka kartı (yedek) Kripto %(bitcoin_icon)s Kart / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (düzenli) Pix (Brezilya) Revolut (geçici olarak ulaşılamıyor) WeChat Tercih ettiğiniz kripto parayı seçin: Amazon Hediye Kartı kullanarak bağış yapın. <strong>ÖNEMLİ:</strong> Bu seçenek %(amazon)s içindir. Başka bir Amazon web sitesini kullanmak istiyorsanız, yukarıdan seçin. <strong>ÖNEMLİ</strong> Sadece Amazon.com'u destekliyoruz, diğer Amazon sitelerini değil. Mesela, .tr, .de, .co.uk destekli DEĞİL. Lütfen kendi mesajınızı YAZMAYIN. Tam miktarı girin: %(amount)s Bayilerimiz tarafından kabul edilen miktarlara (minimum %(minimum)slar) yuvarlamamız gerektiğini unutmayın. Alipay uygulaması üzerinden kredi/banka kartı kullanarak bağış yapın (kurulumu çok kolay). Alipay uygulamasını <a %(a_app_store)s>Apple App Store</a> veya <a %(a_play_store)s>Google Play Store</a>'dan yükleyin. Telefon numaranızı kullanarak kaydolun. Başka kişisel bilgi gerekmemektedir. <span %(style)s>1</span>Alipay uygulamasını yükleyin Desteklenenler: Visa, MasterCard, JCB, Diners Club ve Discover. Daha fazla bilgi için <a %(a_alipay)s>bu kılavuza</a> bakın. <span %(style)s>2</span>Banka kartı ekleyin Binance ile kredi/banka kartı veya banka hesabı kullanarak Bitcoin satın alabilir ve ardından bu Bitcoin'i bize bağışlayabilirsiniz. Bu şekilde bağışınızı kabul ederken güvenli ve anonim kalabiliriz. Binance neredeyse her ülkede mevcuttur ve çoğu banka ve kredi/banka kartını destekler. Şu anda ana önerimiz bu yöntemdir. Bu yöntemi kullanarak bağış yapmayı öğrenmek için zaman ayırdığınız için minnettarız, çünkü bu bize çok yardımcı oluyor. Kredi kartları, banka kartları, Apple Pay ve Google Pay için “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) kullanıyoruz. Onların sisteminde bir “kahve” 5$'a eşittir, bu yüzden bağışınız en yakın 5'in katına yuvarlanacaktır. Cash App kullanarak bağış yap. Cash App kullanıyorsanız, bağış yapmak için en kolay yöntem bu! %(amount)s altındaki transferler için Cash App'in %(fee)s ücret kesebileceği aklınızda bulunsun. %(amount)s ve üstü için ücret yok! Kredi veya banka kartı ile bağış yapın. Bu yöntem, bir kripto para sağlayıcısını ara dönüştürücü olarak kullanır. Bu biraz kafa karıştırıcı olabilir, bu yüzden lütfen diğer ödeme yöntemleri çalışmazsa bu yöntemi kullanın. Ayrıca her ülkede çalışmaz. Bankalar bizimle çalışmak istemediği için kredi/banka kartlarını doğrudan destekleyemiyoruz. ☹ Ancak, kredi/banka kartlarını kullanmanın birkaç yolu var, bunlar da diğer ödeme yöntemlerini kullanmaktır: Kripto kullanarak BTC, ETH, XMR ve SOL ile bağış yapabilirsin. Bu seçeneği halihazırda kripto para birimlerine alışkınsan kullan. Kripto ile BTC, ETH, XMR ve diğerlerini kullanarak bağış yapabilirsiniz. Hızlı kripto hizmetleri Kriptoyu ilk kez kullanıyorsanız, Bitcoin (orijinal ve en çok kullanılan kripto para birimi) satın almak ve bağış yapmak için %(options)s'i kullanmanızı öneririz. Daha küçük bağışlarda kredi kartında kesilen ücretler bizim %(discount)s%% indirimimizi kaldırabilir, bu yüzden daha uzun abonelikler öneririz. Kredi/banka kartı, PayPal veya Venmo kullanarak bağış yapın. Bir sonraki sayfada bunlar arasında seçim yapabilirsiniz. Google Pay ve Apple Pay de işe yarayabilir. Küçük bağışlar için kesilen ücretlerin yüksek olduğunu unutmayın, bu nedenle daha uzun abonelikler yapmanızı öneririz. PayPal ABD ile bağışta bulunmak için, anonim olmamıza izin veren PayPal Kripto kullanacağı. Bu metotla nasıl bağış yapılacağını öğrenmeye vakit ayırdığınız için müteşekkiriz, bu bize çok yardımcı olur. PayPal kullanarak bağış yap. Ana PayPal hesabınızı kullanarak bağış yapın. Revolut kullanarak bağış yapın. Revolut'unuz varsa, bağış yapmanın en kolay yolu bu! Bu ödeme yöntemi maksimum %(amount)s destekliyor. Lütfen başka bir tutar ya da ödeme yöntemi seçin. Bu ödeme yöntemi minimum %(amount)s destekliyor. Lütfen başka bir tutar ya da ödeme yöntemi seçin. Binance Coinbase Kraken Lütfen bir ödeme yöntemi seçin. “Bir torrent sahiplen”: <div %(div_months)s>her 12 aylık üyelikte bir defa</div> bir torrent dosyasında kullanıcı adınız veya mesajınız Katkıda Bulunanlar kısmında kullanıcı adınızla ya da anonim olarak değinme Yeni özelliklere erken erişim Sahne arkası güncellemeleri içeren özel Telegram kanalı Her gün %(number)s hızlı indirme bu ay bağış yaparsanız! <a %(a_api)s>JSON API</a> erişimi İnsanlığın bilgisi ve kültürünü korumakta efsanevi rol Önceki avantajlar, artı: <a %(a_refer)s>Arkadaşlarınızı davet ederek</a> <strong>%(percentage)s%% bonus indirme</strong> kazanın. Doğrulama olmaksızın <strong>sınırsız</strong> SciDB sayfasına erişim Hesap veya bağış soruları sorarken, hesap kimliğinizi, ekran görüntülerini, makbuzları ve mümkün olduğunca fazla bilgiyi ekleyin. E-postamızı yalnızca 1-2 haftada bir kontrol ediyoruz, bu nedenle bu bilgileri eklememek herhangi bir çözümü geciktirecektir. Daha fazla indirme hakkı kazanmak için, <a %(a_refer)s>arkadaşlarınıza tavsiye edin</a>! Biz gönüllülerden oluşan küçük bir ekibiz. Cevap vermemiz 1-2 haftayı bulabilir. Hesap adının veya resminin garip görünebileceğini aklınızda bulundurun. Endişelenmenize gerek yok! Bu hesaplar bağış ortaklarımız tarafından yönetilmektedir. Hesaplarımız çalınmamıştır. Bağışla <span %(span_cost)s></span> <span %(span_label)s></span> 12 ay için “%(tier_name)s” 1 ay için “%(tier_name)s” 24 ay için “%(tier_name)s” 3 ay için “%(tier_name)s” 48 ay boyunca geçerli “%(tier_name)s” 6 ay için “%(tier_name)s” 96 ay boyunca geçerli “%(tier_name)s” Ödeme sırasında bağışı hâlâ iptal edebilirsiniz. Bağışı onaylama için bağış tuşuna tıkla. <strong>Önemli not:</strong> Kripto fiyatları bazen birkaç dakikada 20%% olacak kadar çılgınca dalgalanabilir. Bu yine de çoğu ödeme sağlayıcısına ödediğimiz komisyonlardan düşüktür; genellikle bizim gibi bir "gölge hayır işi" ile çalışırken 50-60%% komisyon keserler. <u>Eğer bize ödediğiniz asıl fiyatla birlikte bir makbuz gönderirseniz, sizin hesabınıza seçilen üyeliği tanımlayacağız </u> (makbuzun birkaç saatten daha eski olmaması koşuluyla). Bizi desteklemek için böyle şeylere katlanmaya niyetli olmanızı gerçekten takdir ediyoruz! ❤️ ❌ Bir şeyler ters gitti. Lütfen sayfayı yenileyin ve tekrar deneyin. <span %(span_circle)s>1</span>PayPal'da Bitcoin al <span %(span_circle)s>2</span>Bitcoini adresimize transfer edin ✅ Bağış sayfasına yönlendiriliyor… Bağış yap Lütfen bizimle iletişime geçmeden önce en az <span%(span_hours)s>24 saat</span> bekleyin (ve bu sayfayı yenileyin). Üyelik olmadan (herhangi bir miktarda) bağış yapmak isterseniz, Monero (XMR) adresini kullanabilirsiniz: %(address)s. Hediye kartınızı gönderdikten sonra sistemimiz birkaç dakika içinde onaylayacaktır. Eğer bu işe yaramazsa hediye kartını tekrar göndermeyi deneyin. (<a %(a_instr)s>talimatlar</a>). Bu hala işe yaramazsa lütfen bize e-posta gönderin; Anna bunu manuel olarak inceleyecektir (bu birkaç gün sürebilir) daha öncesinde yeniden göndermeyi deneyip denemediğinizi bahsetmeyi unutmayın. Örnek: Lütfen aşağıdaki e-posta adresine %(amount)s tutarında bir hediye kartı göndermek için <a %(a_form)s>resmi Amazon.com formunu</a> kullanın. Formdaki "Alıcı" e-posta adresi: Amazon hediye kartı Diğer hediye kartı yöntemlerini kabul edemiyoruz, <strong>yalnızca Amazon.com'daki resmi formdan doğrudan gönderilenleri kabul ediyoruz</strong>. Bu formu kullanmazsanız hediye kartınızı iade edemeyiz. Sadece bir kez kullanın. Sizin hesabınıza özeldir, kimseyle paylaşmayın. Hediye kartı bekleniyor... (kontrol etmek için sayfayı yenileyin) <a %(a_href)s>QR kod bağış sayfasını</a> açın. Alipay uygulaması ile QR kodunu tarayın veya Alipay uygulamasını açmak için butona basın. Lütfen sabırlı olun; içerik Çin'de olduğu için sayfanın yüklenmesi biraz zaman alabilir. <span %(style)s>3</span>Bağış yapın (QR kodu tarayın veya butona basın) PayPal'dan PYUSD satın alın Cash App ile Bitcoin (BTC) Satın Alın Bağışladığınız miktarı (%(amount)s) karşılamak için işlem ücretlerini kapsayacak şekilde biraz daha fazla (biz %(more)s daha fazla öneriyoruz) satın alın. Kalan miktar sizde kalacaktır. Cash App'te “Bitcoin” (BTC) sayfasına gidin. Bitcoin'i adresimize transfer edin Küçük bağışlar için (25$'ın altındaki), Rush veya Priority kullanmanız gerekebilir. “Bitcoin gönder” butonuna tıklayarak bir “çekim” yapın. %(icon)s simgesine basarak dolardan BTC'ye geçiş yapın. Aşağıya BTC miktarını girin ve “Gönder”e tıklayın. Takılırsanız <a %(help_video)s>bu videoyu</a> izleyin. Hızlı hizmetler kullanışlıdır, ancak daha yüksek ücretler alınır. Yüksek miktardaki bağışlarınızı daha hızlı yapmak istiyorsanız ve $5-10'luk bir ücreti önemsemiyorsanız, bu yöntemi kripto yerine kullanabilirsiniz. Bağış sayfasında gösterilen kripto miktarını gönderdiğinizden emin olun, $USD miktarını gönderirseniz sorun olabilir. Aksi takdirde komisyon ücreti düşülecek ve üyeliğinizi otomatik olarak işleyemeyeceğiz. Bazen onay süreci 24 saate kadar sürebilir, bu yüzden bu sayfayı yenilediğinizden emin olun (süresi dolmuş olsa bile). Kredi/banka kartı talimatları Kredi/ banka kartı sayfamızdan bağış yapın Adımlardan bazıları kripto cüzdanlarından bahsediyor ancak endişelenmeyin, bunun için kripto hakkında hiçbir şey öğrenmenize gerek yok. %(coin_name)s talimatları Ödeme ayrıntılarını hızlı bir şekilde doldurmak için bu QR kodunu kripto cüzdan uygulamanızla tarayın Ödemek için QR kodunu tarayın Yalnızca standart kripto para birimlerini destekliyoruz, egzotik ağlar veya para birimi versiyonları desteklenmemektedir. İşlemin onaylanması, para birimine bağlı olarak bir saate kadar sürebilir. <a %(a_page)s>Bu sayfadan</a> %(amount)s bağış yapın. Bu bağışın süresi doldu. Lütfen iptal edin ve yeni bir tane başlatın. Eğer zaten ödeme yaptıysanız: Evet, makbuzumu e-posta olarak gönderdim Eğer kripto kuru işlem sırasında dalgalandıysa, makbuzun orijinal kuru gösterdiğinden emin olun. Kriptoyu kullanma zahmetine katlanmanızı gerçekten takdir ediyoruz, bize çok yardımcı oluyor! ❌ Bir şey yanlış gitti. Lütfen sayfayı yenileyin ve tekrar deneyin. <span %(span_circle)s>%(circle_number)s</span>Makbuzu bize e-posta yoluyla gönderin Eğer herhangi bir sorunla karşılaşırsanız, lütfen bizle %(email)s adresinde iletişime geçin ve olabildiğince fazla bilgi ilave edin (ekran görüntüleri gibi). ✅ Bağışınız için teşekkürler! Anna şahsen üyeliğinizi birkaç gün içinde etkinleştirecek. Şahsi doğrulama adresinize bir makbuz veya ekran görüntüsünü gönderin: Makbuzu e-posta olarak gönderdikten sonra, bu butona tıklayın, bu sayede Anna bizzat inceleyebilir (bu birkaç gün sürebilir): Kişisel doğrulama adresinize bir makbuz veya ekran görüntüsü gönderin. Bu e-posta adresini PayPal bağışınız için KULLANMAYIN. İptal Et Evet, iptal et İptal etmek istediğine emin misin? Zaten ödemeyi yaptıysan iptal etme. ❌ Bir şeyler yanlış gitti. Lütfen sayfayı yenile ve tekrar dene. Yeni bir bağış yap ✅ Bağışın iptal edildi. Tarih: %(date)s Tanımlayıcı: %(id)s Yeniden sipariş ver Durum: <span %(span_label)s>%(label)s</span> Toplam: %(total)s <span %(span_details)s>(%(discounts)s%% indirim dâhil, %(duration)s ay için aylık %(monthly_amount_usd)s)</span> Toplam: %(total)s <span %(span_details)s>(%(duration)s ay için aylık %(monthly_amount_usd)s)</span> 1. E-posta adresinizi girin. 2. Ödeme yöntemini seçin. 3. Ödeme yönteminizi tekrar seçin. 4. “Kendi kendine barındırılan” cüzdanı seçin. 5. "Sahipliğimi onaylıyorum" seçeneğine tıklayın. 6. Bir e-posta makbuzu almalısınız. Lütfen bu makbuzu bize gönderin, bağışınızı en kısa sürede onaylayacağız. (iptal edip yeni bir bağış oluşturmak isteyebilirsiniz) Ödeme yönergeleri şu anda eskimiş durumda. Eğer başka bir bağış yapmak isterseniz, yukarıdaki "Yeniden sipariş ver" butonunu kullanın. Zaten ödemeyi yaptın. Eğer yine de ödeme talimatlarını tekrar gözden geçirmek istersen buraya tıkla: Eski ödeme talimatlarını göster Eğer bağış sayfası engellenirse farklı bir internet bağlantısı deneyin (örneğin, VPN veya telefon interneti). Ne yazık ki, Alipay sayfası genellikle yalnızca <strong>anakara Çin</strong>'den erişilebilir. VPN'inizi geçici olarak devre dışı bırakmanız veya konumu anakara Çin olarak gösteren (bazen Hong Kong da işe yarar) bir VPN kullanmanız gerekebilir. <span %(span_circle)s>1</span>Alipay'de bağış yap Toplam %(total)s tutarını <a %(a_account)s>bu Alipay hesabını</a> kullanarak bağışlayın. Alipay talimatları <span %(span_circle)s>1</span>Kripto hesaplarımızdan birine transfer edin Bu adreslerden birine toplam %(total)s miktarını bağışlayın: Kripto talimatları Bitcoin (BTC) almak için talimatları takip edin. Yalnızca bağış yapmak istediğiniz miktarı satın almanız gerekli, %(total)s. Bitcoin (BTC) adresimizi alıcı olarak girin ve %(total)s tutarındaki bağışınızı göndermek için talimatları takip edin: <span %(span_circle)s>1</span>Pix'te bağış yap Toplam %(total)s tutarını <a %(a_account)s>bu Pix hesabını kullanarak bağışlayın Pix talimatları <span %(span_circle)s>1</span>WeChat'te bağış yapın Toplam %(total)s tutarını <a %(a_account)s>bu WeChat hesabını</a> kullanarak bağışlayın. WeChat talimatları Sadece birkaç dakika süren aşağıdaki “kredi kartından Bitcoin'e” hızlı hizmetlerden herhangi birini kullanın: BTC / Bitcoin adresi (harici cüzdan): BTC / Bitcoin miktarı: Formdaki aşağıdaki bilgileri doldurun: Bu bilgilerin herhangi biri güncel değilse lütfen bize e-posta göndererek bildirin. Lütfen bu <span %(underline)s>tam miktarı</span> kullanın. Kredi kartı ücretleri nedeniyle toplam maliyetiniz daha yüksek olabilir. Maalesef, küçük miktarlar için bu bizim indirimimizden daha fazla olabilir. (en az: %(minimum)s ) (en az: %(minimum)s) (en az: %(minimum)s) (en az: %(minimum)s, ilk işlem için doğrulama yoktur) (en az: %(minimum)s) (en az: %(minimum)s ülkeye bağlı olarak değişir, ilk işlem için doğrulama yoktur) PYUSD coin (PayPal USD) almak için talimatları takip edin. Transfer ücretlerini karşılamak için, bağış yapacağınız tutardan(%(amount)s) biraz daha fazla (%(more)s daha tavsiye ediyoruz) satın alın. Artan para sizde kalacak. PayPal uygulamanızda veya web sitenizde “PYUSD” sayfasına gidin. “Transfer” düğmesine %(icon)s basın ve ardından “Gönder” seçeneğine tıklayın. Durumu güncelle Zamanlayıcıyı sıfırlamak için, basitçe yeni bir bağış oluşturun. Aşağıda yazan BTC miktarını girdiğinizden emin olun, euro veya dolar olarak <em>GİRMEYİN</em>, aksi takdirde doğru miktarı alamayacağımızdan üyeliğinizi otomatik olarak doğrulayamayacağız. Revolut üzerinden Bitcoin (BTC) satın alın İşlem ücretlerini karşılamak için bağışladığınız miktardan biraz daha fazlasını satın alın (%(amount)s) (%(more)s fazlasını öneririz). Geriye kalan her şeyi kendinize saklayacaksınız. Bitcoin (BTC) satın almak için Revolut'ta “Kripto” sayfasına gidin. Bitcoin'i adresimize transfer edin Küçük bağışlar için (25$'ın altındaki), Rush veya Priority kullanmanız gerekebilir. “Bitcoin gönder” butonuna tıklayarak bir “çekim” yapın. %(icon)s simgesine basarak Euro'dan BTC'ye geçiş yapın. Aşağıdaki BTC miktarını girin ve “Gönder”e tıklayın. Takılırsanız <a %(help_video)s>bu videoyu</a> izleyin. Durum: 1 2 Adım adım yönerge Aşağıdaki adım-adım rehbere bakın. Aksi takdirde bu hesaba erişiminiz kilitlenebilir! Eğer hala yapmadıysanız, giriş yapmak için gizli anahtarınızı girin: Bağışınız için teşekkürler! Kalan zaman: Bağış %(account)s'a %(amount)s bağış yapın Onay bekleniyor (kontrol etmek için sayfayı yenileyin)… Transfer için bekleniyor (kontrol etmek için sayfayı yenileyin)… Daha önce Son 24 saatteki hızlı indirmeler günlük limite dahil edilir. Hızlı Dost Sunuculardan indirmeler %(icon)s ile işaretlenmiştir. Son 18 saat Henüz indirilen dosya yok. İndirilen dosyalar halka açık bir şekilde gösterilmemektedir. Tüm saatler UTC cinsindendir. İndirilen dosyalar Hem hızlı hem de yavaş indirmelerle bir dosya indirdiyseniz dosya iki kez görünecektir. Çok fazla endişelenmeyin, bizim bağlantı verdiğimiz web sitelerinden birçok kişi indiriyor ve sorun yaşamak son derece nadirdir. Ancak, güvende kalmak için ücretli bir VPN veya ücretsiz <a %(a_tor)s>Tor</a> kullanmanızı öneririz. George Orwell'in 1984'ünü indirdim, polis kapıma gelir mi? Siz Annasınız! Anna kimdir? Üyeler için hızlı indirme URL'si almak amacıyla tek bir sabit JSON API'miz var: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON içinde belgeler mevcuttur). Diğer kullanım durumları için, örneğin tüm dosyalarımızı yinelemek, özel arama oluşturmak ve benzeri, <a %(a_generate)s>oluşturmayı</a> veya <a %(a_download)s>indirmeyi</a> öneririz. Ham veriler manuel olarak <a %(a_explore)s>JSON dosyaları aracılığıyla</a> keşfedilebilir. Ham torrent listemiz de <a %(a_torrents)s>JSON</a> olarak indirilebilir. API'niz var mı? Burada herhangi bir telif hakkıyla korunan materyali barındırmıyoruz. Biz bir arama motoruyuz ve bu nedenle yalnızca zaten kamuya açık olan üstveriyi indeksliyoruz. Bu dış kaynaklardan indirirken, izin verilenler konusunda kendi yargı bölgenizdeki yasaları kontrol etmenizi öneririz. Başkaları tarafından barındırılan içerikten sorumlu değiliz. Burada gördüğünüz şeylerle ilgili şikayetleriniz varsa, en iyi seçeneğiniz orijinal web sitesiyle iletişime geçmektir. Değişikliklerini düzenli olarak veritabanımıza çekiyoruz. Gerçekten geçerli bir DMCA şikayetiniz olduğunu düşünüyorsanız, lütfen <a %(a_copyright)s>DMCA / Telif hakkı talep formunu</a> doldurun. Şikayetlerinizi ciddiye alıyoruz ve en kısa sürede size geri döneceğiz. Telif hakkı ihlalini nasıl bildiririm? İşte gölge kütüphaneler ve dijital koruma dünyası için özel bir öneme sahip bazı kitaplar: Favori kitaplarınız nelerdir? Ayrıca, tüm kod ve verilerimizin tamamen açık kaynak olduğunu herkese hatırlatmak isteriz. Bu, bizim gibi projeler için benzersizdir — benzer şekilde büyük bir kataloğa sahip olup da tamamen açık kaynak olan başka bir proje bilmiyoruz. Projemizi kötü yönettiğimizi düşünen herkesi kodumuzu ve verilerimizi alıp kendi gölge kütüphanelerini kurmaya davet ediyoruz! Bunu kin veya başka bir şeyden dolayı söylemiyoruz — gerçekten bunun harika olacağını düşünüyoruz çünkü bu herkes için çıtayı yükseltecek ve insanlığın mirasını daha iyi koruyacaktır. Bu projeyi nasıl yönettiğinizi nefret ediyorum! <a %(a_mirrors)s>Aynalar</a> kurmalarını çok isteriz ve bunu mali olarak destekleyeceğiz. Nasıl yardımcı olabilirim? Evet, topluyoruz. Üstveri toplama ilhamımız, Aaron Swartz’ın “yayınlanmış her kitap için bir web sayfası” hedefidir, bunun için <a %(a_openlib)s>Open Library</a>'yi oluşturdu. Bu proje iyi ilerledi, ancak benzersiz konumumuz, onların elde edemediği üstverileri elde etmemizi sağlıyor. Diğer bir ilham kaynağımız ise dünyada <a %(a_blog)s>kaç kitap olduğunu</a> bilme arzumuzdu, böylece kurtarmamız gereken kaç kitap kaldığını hesaplayabiliriz. Üstveri topluyor musunuz? Mhut.org belirli IP aralıklarını engellediğini unutmayın, bu yüzden bir VPN gerekebilir. <strong>Android:</strong> Sağ üst köşedeki üç noktalı menüye tıklayın ve “Ana Ekrana Ekle”yi seçin. <strong>iOS:</strong> Alttaki “Paylaş” düğmesine tıklayın ve “Ana Ekrana Ekle”yi seçin. Resmi bir mobil uygulamamız yok, ancak bu web sitesini bir uygulama olarak yükleyebilirsiniz. Mobil uygulamanız var mı? Lütfen onları <a %(a_archive)s>Internet Archive</a>'e gönderin. Onlar uygun şekilde koruyacaklardır. Kitap veya diğer fiziksel materyalleri nasıl bağışlayabilirim? Kitap talebini nasıl yaparım? <a %(a_blog)s>Anna’nın Blogu</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — düzenli güncellemeler <a %(a_software)s>Anna’nın Yazılımı</a> — açık kaynak kodumuz <a %(a_datasets)s>Datasets</a> — veriler hakkında <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatif alan adları Anna’nın Arşivi hakkında daha fazla kaynak var mı? <a %(a_translate)s>Anna’nın Yazılımında Çeviri Yapın</a> — çeviri sistemimiz <a %(a_wikipedia)s>Wikipedia</a> — hakkımızda daha fazla bilgi (lütfen bu sayfayı güncel tutmaya yardımcı olun veya kendi dilinizde bir tane oluşturun!) Beğendiğiniz ayarları seçin, arama kutusunu boş bırakın, “Ara”ya tıklayın ve ardından tarayıcınızın yer imi özelliğini kullanarak sayfayı yer imlerine ekleyin. Arama ayarlarımı nasıl kaydederim? Sistemlerimizdeki güvenlik açıklarını aramak için güvenlik araştırmacılarını memnuniyetle karşılıyoruz. Sorumlu açıklamanın büyük savunucularıyız. Bize <a %(a_contact)s>buradan</a> ulaşın. Şu anda, <a %(a_link)s>anonimliğimizi tehlikeye atma potansiyeline sahip</a> güvenlik açıkları dışında hata ödülleri veremiyoruz. Bu tür güvenlik açıkları için 10.000-50.000 $ aralığında ödüller sunuyoruz. Gelecekte hata ödülleri için daha geniş bir kapsam sunmak istiyoruz! Sosyal mühendislik saldırılarının kapsam dışı olduğunu lütfen unutmayın. Saldırgan güvenlik ile ilgileniyorsanız ve dünyanın bilgi ve kültürünü arşivlemeye yardımcı olmak istiyorsanız, bizimle iletişime geçtiğinizden emin olun. Yardım edebileceğiniz birçok yol var. Sorumlu açıklama programınız var mı? Dünyadaki herkese yüksek hızlı indirme sağlamak için yeterli kaynağımız gerçekten yok, ne kadar istesek de. Zengin bir hayırseverin bunu bizim için sağlaması harika olurdu, ama o zamana kadar elimizden gelenin en iyisini yapıyoruz. Bağışlarla zar zor ayakta duran kar amacı gütmeyen bir projeyiz. Bu yüzden, ortaklarımızla birlikte iki ücretsiz indirme sistemi uyguladık: yavaş indirmeler için paylaşılan sunucular ve bekleme listesi ile biraz daha hızlı sunucular (aynı anda indiren kişi sayısını azaltmak için). Yavaş indirmelerimiz için <a %(a_verification)s>tarayıcı doğrulaması</a> da yapıyoruz, çünkü aksi takdirde botlar ve kazıyıcılar bunları kötüye kullanarak meşru kullanıcılar için işleri daha da yavaşlatır. Tor Tarayıcıyı kullanırken, güvenlik ayarlarınızı ayarlamanız gerekebileceğini unutmayın. “Standart” olarak adlandırılan en düşük seçeneklerde, Cloudflare turnike meydan okuması başarılı olur. “Daha Güvenli” ve “En Güvenli” olarak adlandırılan daha yüksek seçeneklerde, meydan okuma başarısız olur. Büyük dosyalar için bazen yavaş indirmeler ortasında kesilebilir. Büyük indirmeleri otomatik olarak devam ettirmek için bir indirme yöneticisi (örneğin JDownloader) kullanmanızı öneririz. Neden yavaş indirmeler bu kadar yavaş? Sıkça Sorulan Sorular (SSS) Depolama alanı sınırlarınız dahilinde en çok torrent ihtiyacı olan torrentlerin listesini oluşturmak için <a %(a_list)s>torrent listesi oluşturucuyu</a> kullanın. Evet, <a %(a_llm)s>LLM verileri</a> sayfasına bakın. Çoğu torrent dosyaları doğrudan içerir, bu da torrent istemcilerine yalnızca gerekli dosyaları indirmelerini söyleyebileceğiniz anlamına gelir. Hangi dosyaları indireceğinizi belirlemek için üstverimizi <a %(a_generate)s>oluşturabilir</a> veya ElasticSearch ve MariaDB veritabanlarımızı <a %(a_download)s>indirebilirsiniz</a>. Ne yazık ki, bazı torrent koleksiyonları kök dizinde .zip veya .tar dosyaları içerir, bu durumda bireysel dosyaları seçmeden önce tüm torrent'i indirmeniz gerekir. Henüz torrentleri filtrelemek için kullanımı kolay araçlar mevcut değil, ancak katkılarınızı memnuniyetle karşılıyoruz. (Ancak, ikinci durum için <a %(a_ideas)s>bazı fikirlerimiz</a> var.) Uzun cevap: Kısa cevap: kolay değil. Bu listedeki torrentler arasında minimum kopyalama veya örtüşme olmasını sağlamaya çalışıyoruz, ancak bu her zaman mümkün olamaz ve kaynak kütüphanelerin politikalarına büyük ölçüde bağlıdır. Kendi torrentlerini yayınlayan kütüphaneler için bu bizim elimizde değil. Anna’nın Arşivi tarafından yayınlanan torrentler için, yalnızca MD5 hash'e dayalı olarak kopyaları kaldırıyoruz, bu da aynı kitabın farklı sürümlerinin kopyalanmadığı anlamına gelir. Evet. Bunlar aslında PDF ve EPUB dosyalarıdır, sadece birçok torrentimizde uzantıları yoktur. Torrent dosyalarının üstverilerini, dosya türleri/uzantıları dahil olmak üzere bulabileceğiniz iki yer vardır: 1. Her koleksiyon veya sürümün kendi üstverisi vardır. Örneğin, <a %(a_libgen_nonfic)s>Libgen.rs torrentleri</a> Libgen.rs web sitesinde barındırılan ilgili bir üstveri veritabanına sahiptir. Genellikle her koleksiyonun <a %(a_datasets)s>veri seti sayfasından</a> ilgili üstveri kaynaklarına bağlantı veririz. 2. ElasticSearch ve MariaDB veritabanlarımızı <a %(a_generate)s>oluşturmayı</a> veya <a %(a_download)s>indirmeyi</a> öneririz. Bunlar, Anna’nın Arşivi'ndeki her kaydın karşılık gelen torrent dosyalarına (varsa) "torrent_paths" altında bir eşlemesini içerir. Bazı torrent istemcileri büyük parça boyutlarını desteklemez, ki birçok torrentimizde bu durum söz konusudur (yeni olanlarda bunu artık yapmıyoruz — spesifikasyonlara göre geçerli olmasına rağmen!). Bu durumla karşılaşırsanız farklı bir istemci deneyin veya torrent istemcinizin yapımcılarına şikayette bulunun. Seed etmeye yardımcı olmak istiyorum, ancak fazla disk alanım yok. Torrentler çok yavaş; verileri doğrudan sizden indirebilir miyim? Sadece belirli bir dil veya konu gibi dosyaların bir alt kümesini indirebilir miyim? Torrentlerdeki kopyaları nasıl ele alıyorsunuz? Torrent listesini JSON olarak alabilir miyim? Torrentlerde PDF veya EPUB görmüyorum, sadece ikili dosyalar mı? Ne yapmalıyım? Torrent istemcim neden bazı torrent dosyalarınızı / magnet bağlantılarınızı açamıyor? Torrentler SSS Yeni kitapları nasıl yüklerim? Lütfen <a %(a_href)s>bu mükemmel projeye</a> bakın. Bir çalışma süresi izleyiciniz var mı? Anna’nın Arşivi nedir? Hızlı indirmeleri kullanmak için üye olun. Artık Amazon hediye kartları, kredi ve banka kartları, kripto, Alipay ve WeChat'i destekliyoruz. Bugün hızlı indirme hakkınız tükendi. Erişim Son 30 günde saatlik indirmeler. Saatlik ortalama: %(hourly)s. Günlük ortalama: %(daily)s. Koleksiyonlarımızın herkese kolayca ve özgürce erişilebilir olmasını sağlamak için ortaklarla birlikte çalışıyoruz. Herkesin insanlığın toplu bilgeliğine erişme hakkına sahip olduğuna ve <a %(a_search)s>yazarların zararına olmadığına</a> inanıyoruz. Anna’nın Arşivi'nde kullanılan verisetleri tamamen açıktır ve torrentler kullanılarak toplu olarak yansıtılabilir. <a %(a_datasets)s>Daha fazla bilgi edinin…</a> Uzun vadeli arşiv Tüm veritabanı Ara Kitaplar, makaleler, dergiler, çizgi romanlar, kütüphane kayıtları, metadata, … Tüm <a %(a_code)s>kodlarımız</a> ve <a %(a_datasets)s>verilerimiz</a> tamamen açık kaynaktır. <span %(span_anna)s>Anna'nın Arşivi</span> iki amacı olan kâr amacı gütmeyen bir projedir: <li><strong>Saklama:</strong> İnsanlığım tüm bilgi ve kültürünü yedekleme.</li><li><strong>Erişim:</strong> Bu bilgiyi ve kültürü dünyadaki herkese erişilebilir kılma.</li> Dünyanın en büyük yüksek kaliteli metin veri koleksiyonuna sahibiz. <a %(a_llm)s>Daha fazla bilgi edinin…</a> LLM training data 🪩 Aynalar: gönüllü çağrısı Yüksek riskli anonim bir ödeme işlemcisi çalıştırıyorsanız, lütfen bizimle iletişime geçin. Ayrıca, zarif küçük reklamlar yerleştirmek isteyen kişiler arıyoruz. Tüm gelirler koruma çabalarımıza gitmektedir. Saklama <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">Dünyadaki bütün kitapların 5%% kadarını</a> sakladığımızı tahmin ediyoruz. Kitapları, makaleleri, çizgi romanları, dergileri ve daha fazlasını, bu materyalleri çeşitli <a href="https://en.wikipedia.org/wiki/Shadow_library">gölge kütüphanelerinden</a>, resmi kütüphanelerden ve diğer koleksiyonlardan bir araya getirerek koruyoruz. Tüm bu veriler, toplu olarak çoğaltılmasını kolaylaştırarak — torrentler kullanarak — dünya çapında birçok kopya oluşturulmasıyla sonsuza kadar korunur. Bazı gölge kütüphaneler bunu zaten kendileri yapıyor (örneğin Sci-Hub, Library Genesis), Anna’nın Arşivi ise toplu dağıtım sunmayan diğer kütüphaneleri “özgürleştiriyor” (örneğin Z-Library) veya hiç gölge kütüphane olmayanları (örneğin Internet Archive, DuXiu). Bu geniş dağıtım, açık kaynak kodu ile birleştiğinde, web sitemizi kapatmalara karşı dayanıklı hale getirir ve insanlığın bilgi ve kültürünün uzun vadeli korunmasını sağlar. <a href="/datasets">veri setlerimiz</a> hakkında daha fazla bilgi edinin. Eğer bir <a %(a_member)s>üye</a> iseniz, tarayıcı doğrulaması gerekmez. 🧬&nbsp;SciDB, Sci-Hub'ın devamıdır. SciDB Aç DOI Sci-Hub yeni makalelerin yüklenmesini <a %(a_paused)s>durdurdu</a>. %(count)s akademik makaleye direkt ulaşım 🧬&nbsp;SciDB, Sci-Hub'un devamıdır, tanıdık arayüzü ve PDF'lerin doğrudan görüntülenmesi ile. Görüntülemek için DOI'nizi girin. Tam Sci-Hub koleksiyonuna ve yeni makalelere sahibiz. Çoğu, Sci-Hub'a benzer tanıdık bir arayüzle doğrudan görüntülenebilir. Bazıları harici kaynaklardan indirilebilir, bu durumda bu bağlantıları gösteriyoruz. Torrentleri paylaşarak büyük ölçüde yardımcı olabilirsiniz. <a %(a_torrents)s>Daha fazla bilgi edinin…</a> >%(count)s seeders <%(count)s tohumlayıcılar %(count_min)s–%(count_max)s tohumlayıcılar 🤝 Gönüllüler aranıyor Kâr amacı gütmeyen, açık kaynaklı bir proje olarak, her zaman yardıma ihtiyacımız var. IPFS indirmeleri Listeleme ölçütü %(by)s, <span %(span_time)s>%(time)s</span> tarihinde oluşturuldu Kaydet ❌ Bir şeyler yanlış gitti. Lütfen tekrar deneyin. ✅ Kaydedildi. Lütfen sayfayı yenileyin. Liste boş. düzenle Bir dosya bularak ve "Listeler" sekmesini açarak bu listeye ekle veya bu listeden sil. Sırala Nasıl yardımcı olabiliriz Örtüşmeyi kaldırma (deduplikasyon) Metin ve üstveri çıkarma OCR Tam koleksiyonlarımıza ve henüz yayınlanmamış koleksiyonlara yüksek hızlı erişim sağlayabiliyoruz. Bu, on binlerce USD bağış aralığında sağlayabileceğimiz kurumsal düzeyde bir erişimdir. Ayrıca, henüz sahip olmadığımız yüksek kaliteli koleksiyonlarla bu erişimi takas etmeye de istekliyiz. Verilerimizi zenginleştirerek bize sağlayabilirseniz, size geri ödeme yapabiliriz, örneğin: Modeliniz için daha iyi veriler elde ederken, insan bilgisinin uzun vadeli arşivlenmesini destekleyin! <a %(a_contact)s>Bizimle iletişime geçin</a> nasıl birlikte çalışabileceğimizi tartışmak için. LLM'lerin yüksek kaliteli verilerle beslendiği iyi bilinmektedir. Dünyanın en büyük kitap, makale, dergi vb. koleksiyonuna sahibiz ve bunlar en yüksek kaliteli metin kaynaklarından bazılarıdır. LLM verileri Benzersiz ölçek ve çeşitlilik Koleksiyonumuz, akademik dergiler, ders kitapları ve dergiler dahil olmak üzere yüz milyondan fazla dosya içermektedir. Bu ölçeğe, büyük mevcut depoları birleştirerek ulaşıyoruz. Bazı kaynak koleksiyonlarımız zaten toplu olarak mevcuttur (Sci-Hub ve Libgen'in bazı bölümleri). Diğer kaynakları kendimiz özgürleştirdik. <a %(a_datasets)s>Datasets</a> tam bir genel bakış sunar. Koleksiyonumuz, e-kitap döneminden önceki milyonlarca kitap, makale ve dergiyi içermektedir. Bu koleksiyonun büyük bölümleri zaten OCR ile taranmış olup, içsel örtüşme çok azdır. Devam et Anahtarınızı kaybettiyseniz, lütfen <a %(a_contact)s>bizimle iletişime geçin</a> ve mümkün olduğunca fazla bilgi sağlayın. Bizimle iletişime geçmek için geçici olarak yeni bir hesap oluşturmanız gerekebilir. Bu sayfayı görüntülemek için lütfen <a %(a_account)s>giriş yapın</a>.</a> Spambotların birçok hesap açmasını önlemek için önce tarayıcınızı doğrulamamız gerekmekte. Sonsuz bir döngüye yakalanırsanız, <a %(a_privacypass)s>Privacy Pass</a> yüklemenizi öneririz. Reklam engelleyicilerini ve diğer tarayıcı eklentilerini kapatmak da yardımcı olabilir. Giriş yap / Kaydol Anna’nın Arşivi geçici olarak bakımda. Lütfen bir saat sonra tekrar gelin. Alternatif yazar Alternatif açıklama Alternatif baskı Alternatif uzantı Alternatif dosya adı Alternatif yayıncı Alternatif başlık açık kaynak olma tarihi Daha fazla… açıklama Anna’nın Arşivi'nde CADAL SSNO numarasını ara DuXiu SSID numarası için Anna’nın Arşivi'nde Ara Anna’nın Arşivi'nde DuXiu DXID numarasını ara Anna'nın Arşivinde ISBN aratın OCLC (WorldCat) numarası için Anna’nın Arşivi'nde Ara Open Library ID için Anna’nın Arşivi'nde ara Anna’nın Arşivi çevrimiçi görüntüleyici %(count)s etkilenen sayfalar İndirdikten sonra: %(link)s adresinde bu dosyanın daha iyi bir versiyonu olabilir Toplu torrent indirmeleri koleksiyon Formatlar arasında dönüştürme yapmak için çevrim içi araçları kullanın. Önerilen dönüştürme araçları: %(links)s Büyük dosyalar için, kesintileri önlemek amacıyla bir indirme yöneticisi kullanmanızı öneririz. Önerilen indirme yöneticileri: %(links)s EBSCOhost eKitap Dizini (sadece uzmanlar) (yine üst kısımdaki “GET”e tıklayın) (en üstteki “GET”e tıklayın) Harici indirmeler Bugünlük %(remaining)s kadar hakkınız kaldı. Üye olduğunuz için teşekkürler! ❤️ Bugün için hızlı indirme hakkınız tükendi. Bu dosyayı yakın bir zamanda indirdiniz. Bağlantılar bir süreliğine geçerli kalır. <strong>🚀 Hızlı indirmeler</strong> Kitapların, makalelerin ve daha fazlasının uzun zamanlı saklanmasını desteklemek için bir <a %(a_membership)s>üye</a> olun. Desteğinizden ötürü şükranlarımızı göstermek amacıyla size hızlı indirme imkanı sağlıyoruz. ❤️ 🚀 Hızlı indirmeler 🐢 Yavaş indirmeler Internet Archive'den ödünç al IPFS Ağ Geçidi #%(num)d (IPFS ile birden çok kez denemeniz gerekebilir) Libgen.li Libgen.rs Kurgu Libgen.rs Kurgu Dışı reklamlarının kötü amaçlı yazılım içerdiği biliniyor, bu yüzden bir reklam engelleyici kullanın veya reklamlara tıklamayın Amazon’un “Kindle’a Gönder” djazz’in “Kobo/Kindle’a Gönder” MagzDB ManualsLib Nexus/STC (Nexus/STC dosyalarını indirmek güvenli olmayabilir) İndirme bulunamadı. Tüm aynalarda aynı dosya vardır ve kullanımları güvenli olmalıdır. Bununla birlikte, internetten dosya indirirken her zaman dikkatli olun. Örneğin, cihazlarınızı güncel tuttuğunuzdan emin olun. (yönlendirme yok) Görüntüleyicimizde aç (görüntüleyicide aç) Seçenek #%(num)d: %(link)s %(extra)s CADAL'da orijinal kaydı bulun DuXiu'da manuel olarak ara ISBNdb'de orijinal kayıt bulun WorldCat'te orijinal kaydı bulun Open Library'de orijinal kaydı bul Diğer çeşitli veritabanlarında ISBN aratın (sadece yazdırma engelli kullanıcılar için) PubMed Dosyayı açmak için, dosya formatına bağlı olarak bir e-kitap veya PDF okuyucuya ihtiyacınız olacak. Önerilen e-kitap okuyucuları: %(links)s Anna'nın Arşivi 🧬 SciDB Sci-Hub: %(doi)s (ilgili DOI Sci-Hub'da mevcut olmayabilir) Hem PDF hem de EPUB dosyalarını Kindle veya Kobo eOkuyucunuza gönderebilirsiniz. Önerilen araçlar: %(links)s Daha fazla bilgi <a %(a_slow)s>SSS</a>'de. Yazarları ve kütüphaneleri destekleyin Bunu beğendiyseniz ve maddi durumunuz elveriyorsa, orijinalini satın almayı veya doğrudan yazarlara destek olmayı düşünün. Eğer bu kitabı yerel kütüphanenizde bulabiliyorsanız oradan ücretsiz olarak ödünç almayı düşünün. Dost Sunucu indirmeleri bu dosya için geçici olarak mevcut değil. torrent Güvenilir ortaklardan. Z-Library Tor'da Z-Library (Tor Browser gerekli) harici indirmeleri göster <span class="font-bold">❌ Bu dosya sorunlar içerebilir ve kaynak kütüphanesinden gizlenmiştir.</span> Bu bazen telif hakkı sahibi tarafından istenir, bazen daha iyi bir alternatif mevcutsa, bazen de dosyanın kendisiyle ilgili bir sorun nedeniyle olur. İndirmek hala uygun olabilir ancak öncelikle alternatif bir dosya aramayı öneririz. Daha fazla ayrıntı için: Eğer hala bu dosyayı indirmek istiyorsanız, dosyayı açmak için güvenilir ve güncel yazılımları kullanmaya dikkat edin. üstveri yorumları AA: Anna’nın Arşivi’nde “%(name)s” ara Kodlar Gezgini: Kod Gezgini'nde Görüntüle “%(name)s” URL: Web Sitesi: Bu dosyaya sahipseniz ve Anna’nın Arşivi’nde henüz mevcut değilse <a %(a_request)s>yüklemeyi</a> düşünün. Internet Archive Kontrollü Dijital Ödünç Verme dosyası “%(id)s” Bu, Internet Archive'den bir dosyanın kaydıdır, doğrudan indirilebilir bir dosya değildir. Kitabı ödünç almaya çalışabilirsiniz (aşağıdaki bağlantı), veya <a %(a_request)s>dosya talep ederken</a> bu URL'yi kullanabilirsiniz. Üstveriyi geliştir CADAL SSNO %(id)s üstveri kaydı Bu bir üstveri kaydıdır, indirilebilir bir dosya değildir. <a %(a_request)s>Dosya talep ederken</a> bu URL'yi kullanabilirsiniz. DuXiu SSID %(id)s üstveri kaydı ISBNdb %(id)s üstveri kaydı MagzDB Kimlik %(id)s üstveri kaydı Nexus/STC Kimlik %(id)s üstveri kaydı OCLC (WorldCat) numarası %(id)s üstveri kaydı Open Library %(id)s üstveri kaydı Sci-Hub dosyası “%(id)s” Bulunamadı “%(md5_input)s” veritabanımızda bulunamadı. Yorum ekle (%(count)s) MD5'i URL'den alabilirsiniz, örneğin Bu dosyanın daha iyi bir sürümünün MD5'i (varsa). Eğer bu dosyayla yakından eşleşen (aynı baskı, mümkünse aynı dosya uzantısı) başka bir dosya varsa ve insanların bu dosya yerine onu kullanması gerekiyorsa, lütfen burayı doldurun. Anna’nın Arşivi dışında bu dosyanın daha iyi bir sürümünü biliyorsanız, lütfen <a %(a_upload)s>yükleyin</a>. Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin. Bir yorum bıraktınız. Görünmesi bir dakika sürebilir. Lütfen <a %(a_copyright)s>DMCA / Telif hakkı talep formunu</a> kullanın. Sorunu açıklayın (gerekli) Bu dosya yüksek kalitedeyse, burada hakkında her şeyi tartışabilirsiniz! Değilse, lütfen “Dosya sorunu bildir” düğmesini kullanın. Harika dosya kalitesi (%(count)s) Dosya kalitesi <a %(a_metadata)s>Bu dosyanın üstverisini iyileştirmeyi</a> kendiniz öğrenin. Sorun açıklaması Lütfen <a %(a_login)s>giriş yapın</a>. Bu kitaba bayıldım! Bu dosyanın kalitesini bildirerek topluluğa yardımcı olun! 🙌 Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin. Dosya sorunu bildir (%(count)s) Raporunuzu gönderdiğiniz için teşekkür ederiz. Bu sayfada gösterilecek ve Anna tarafından manuel olarak incelenecektir (müsait bir inceleme ekibimiz olduğu vakit). Yorum bırak Raporu gönder Bu dosyada nasıl bir sorun var? Ödünç Al (%(count)s) Yorumlar (%(count)s) İndirmeler (%(count)s) Üstveriyi keşfet (%(count)s) Listeler (%(count)s) İstatistikler (%(count)s) Bu özel dosya hakkında bilgi için <a %(a_href)s>JSON dosyasına</a> göz atın. Bu, <a %(a_ia)s>IA’nın Kontrollü Dijital Ödünç Verme(CDL)</a> kütüphanesi tarafından yönetilen ve Anna’nın Arşivi tarafından arama için listelenen bir dosyadır. Derlediğimiz çeşitli veri setleri hakkında bilgi için <a %(a_datasets)s>Veri Setleri sayfasına</a> bakın. Bağlantılı kayıttan üstveri Open Library'de üstveriyi iyileştir Bir “Dosya MD5”i dosya içeriğinden hesaplanan ve o içeriğe dayalı olarak makul derecede benzersiz olan bir hash'tir. Burada listelediğimiz tüm gölge kütüphaneler, dosyaları tanımlamak için öncelikle MD5'leri kullanır. Bir dosya birden fazla gölge kütüphanede görünebilir. Derlediğimiz çeşitli veri setleri hakkında bilgi için <a %(a_datasets)s>Veri Setleri sayfasına</a> bakın. Dosya kalitesini bildir Toplam indirme: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Çek üstveri %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Kitaplar %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Uyarı: birden fazla bağlantılı kayıt: Anna’nın Arşivi'nde bir kitaba baktığınızda, çeşitli alanlar görebilirsiniz: başlık, yazar, yayınevi, baskı, yıl, açıklama, dosya adı ve daha fazlası. Bu bilgilerin tümü <em>üstveri</em> olarak adlandırılır. Çeşitli <em>kaynak kütüphanelerden</em> kitapları birleştirdiğimiz için, o kaynak kütüphanede mevcut olan üstveriyi gösteriyoruz. Örneğin, Library Genesis'ten aldığımız bir kitap için, Library Genesis'in veritabanındaki başlığı gösteririz. Bazen bir kitap <em>birden fazla</em> kaynak kütüphanede bulunabilir ve bu kütüphaneler farklı üstveri alanlarına sahip olabilir. Bu durumda, her alanın en uzun versiyonunu gösteririz, çünkü bu versiyonun en faydalı bilgileri içerdiğini umarız! Yine de diğer alanları açıklamanın altında, örneğin "alternatif başlık" olarak gösteririz (ancak yalnızca farklılarsa). Ayrıca kaynak kütüphaneden <em>kimlikler ve sınıflandırıcılar</em> gibi kodları da çıkarırız. <em>Kimlikler</em> belirli bir kitabın baskısını benzersiz şekilde temsil eder; örnekler ISBN, DOI, Open Library ID, Google Books ID veya Amazon ID'dir. <em>Sınıflandırıcılar</em> benzer kitapları bir araya getirir; örnekler Dewey Decimal (DCC), UDC, LCC, RVK veya GOST'tur. Bazen bu kodlar kaynak kütüphanelerde açıkça bağlantılıdır ve bazen dosya adı veya açıklamadan çıkarabiliriz (öncelikle ISBN ve DOI). Kimlikleri, OpenLibrary, ISBNdb veya WorldCat/OCLC gibi <em>sadece üstveri koleksiyonlarında</em> kayıtları bulmak için kullanabiliriz. Bu koleksiyonları göz atmak isterseniz, arama motorumuzda belirli bir <em>üstveri sekmesi</em> vardır. Eşleşen kayıtları eksik üstveri alanlarını doldurmak için kullanırız (örneğin, bir başlık eksikse) veya örneğin "alternatif başlık" olarak (eğer mevcut bir başlık varsa). Bir kitabın üstverisinin tam olarak nereden geldiğini görmek için, bir kitap sayfasındaki <em>“Teknik detaylar” sekmesine</em> bakın. Bu sekmede, o kitaba ait ham JSON'a ve orijinal kayıtların ham JSON'una işaret eden bağlantılar bulunur. Daha fazla bilgi için şu sayfalara bakın: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> ve <a %(a_example)s>Example metadata JSON</a>. Son olarak, tüm üstverimiz <a %(a_generated)s>oluşturulabilir</a> veya <a %(a_downloaded)s>indirilebilir</a> ElasticSearch ve MariaDB veritabanları olarak. Arka Plan Üstveriyi iyileştirerek kitapların korunmasına yardımcı olabilirsiniz! Öncelikle, Anna’nın Arşivi'nde üstveri hakkında arka plan bilgilerini okuyun ve ardından Open Library ile bağlantı kurarak üstveriyi nasıl iyileştireceğinizi öğrenin ve Anna’nın Arşivi'nde ücretsiz üyelik kazanın. Üstveriyi iyileştirin Peki, kötü üstveriye sahip bir dosya ile karşılaşırsanız nasıl düzeltmelisiniz? Kaynak kütüphaneye gidip üstveriyi düzeltme prosedürlerini takip edebilirsiniz, ancak bir dosya birden fazla kaynak kütüphanede bulunuyorsa ne yapmalısınız? Anna’nın Arşivi'nde özel olarak ele alınan bir kimlik vardır. <strong>Open Library'deki annas_archive md5 alanı her zaman diğer tüm üstverilerin önüne geçer!</strong> Önce biraz geri adım atalım ve Open Library hakkında bilgi edinelim. Open Library, 2006 yılında Aaron Swartz tarafından “yayınlanmış her kitap için bir web sayfası” hedefiyle kuruldu. Kitap üstverisi için bir tür Wikipedia gibidir: herkes düzenleyebilir, serbest lisanslıdır ve toplu olarak indirilebilir. Misyonumuzla en çok örtüşen kitap veritabanıdır — aslında, Anna’nın Arşivi, Aaron Swartz’ın vizyonu ve hayatından ilham almıştır. Tekerleği yeniden icat etmek yerine, gönüllülerimizi Open Library'ye yönlendirmeye karar verdik. Yanlış üstveriye sahip bir kitap görürseniz, şu şekilde yardımcı olabilirsiniz: Bunun yalnızca kitaplar için geçerli olduğunu unutmayın, akademik makaleler veya diğer dosya türleri için geçerli değildir. Diğer dosya türleri için kaynak kütüphaneyi bulmanızı öneririz. Değişikliklerin Anna’nın Arşivi'ne dahil edilmesi birkaç hafta sürebilir, çünkü en son Open Library veri dökümünü indirmemiz ve arama dizinimizi yeniden oluşturmamız gerekiyor.  <a %(a_openlib)s>Open Library web sitesine</a> gidin. Doğru kitap kaydını bulun. <strong>UYARI:</strong> doğru <strong>baskıyı</strong> seçtiğinizden emin olun. Open Library'de “eserler” ve “baskılar” vardır. Bir “eser” “Harry Potter ve Felsefe Taşı” olabilir. Bir “baskı” şu olabilir: 1997 yılında Bloomsbery tarafından yayımlanan ilk baskı, 256 sayfa. 2003 yılında Raincoast Books tarafından yayımlanan ciltsiz baskı, 223 sayfa. 2000 yılında Media Rodzina tarafından yayımlanan Lehçe çeviri “Harry Potter I Kamie Filozoficzn”, 328 sayfa. Bu baskıların hepsinin farklı ISBN'leri ve farklı içerikleri vardır, bu yüzden doğru olanı seçtiğinizden emin olun! Kaydı düzenleyin (veya mevcut değilse oluşturun) ve mümkün olduğunca faydalı bilgi ekleyin! Madem buradasınız, kaydı gerçekten harika hale getirin. “ID Numaraları” altında “Anna’nın Arşivi”ni seçin ve Anna’nın Arşivi'nden kitabın MD5'ini ekleyin. Bu, URL'deki “/md5/”den sonraki uzun harf ve rakam dizisidir. Bu kayda da uyan diğer dosyaları Anna’nın Arşivi'nde bulmaya çalışın ve onları da ekleyin. Gelecekte bunları Anna’nın Arşivi arama sayfasında kopyalar olarak gruplandırabiliriz. İşiniz bittiğinde, güncellediğiniz URL'yi yazın. Anna’nın Arşivi MD5'leri ile en az 30 kaydı güncelledikten sonra, bize bir <a %(a_contact)s>e-posta</a> gönderin ve listeyi bize iletin. Size Anna’nın Arşivi için ücretsiz bir üyelik vereceğiz, böylece bu işi daha kolay yapabilirsiniz (ve yardımınız için teşekkür ederiz). Bunlar, önemli miktarda bilgi ekleyen yüksek kaliteli düzenlemeler olmalıdır, aksi takdirde talebiniz reddedilecektir. Talebiniz, düzenlemelerden herhangi biri Open Library moderatörleri tarafından geri alınır veya düzeltilirse de reddedilecektir. Open Library bağlantısı Eğer işimizin geliştirilmesi ve operasyonlarına önemli ölçüde dahil olursanız, gerekli gördüğünüzde kullanmanız için bağış gelirlerinin daha fazlasını sizinle paylaşmayı konuşabiliriz. Her şeyi kurduktan ve arşivi güncellemelerle güncel tutabileceğinizi gösterdikten sonra yalnızca barındırma için ödeme yapacağız. Bu, ilk 1-2 ayı cebinizden ödemeniz gerekeceği anlamına gelir. Zamanınız için ödeme yapılmayacak (bizimki de öyle), çünkü bu tamamen gönüllü bir çalışmadır. Barındırma ve VPN giderlerini karşılamaya hazırız, başlangıçta ayda 200$'a kadar. Bu, temel bir arama sunucusu ve DMCA korumalı bir proxy için yeterlidir. Barındırma giderleri Lütfen <strong>bizimle iletişime geçmeyin</strong> izin istemek veya temel sorular sormak için. Eylemler sözlerden daha yüksek sesle konuşur! Tüm bilgiler orada, bu yüzden aynanızı kurmaya devam edin. Sorunlarla karşılaştığınızda Gitlab'ımıza bilet veya birleştirme isteği göndermekten çekinmeyin. Sizinle birlikte bazı ayna-spesifik özellikler geliştirmemiz gerekebilir, örneğin “Anna’nın Arşivi” adını web sitenizin adıyla yeniden markalaştırmak, (başlangıçta) kullanıcı hesaplarını devre dışı bırakmak veya kitap sayfalarından ana sitemize bağlantı vermek gibi. Aynanızı çalıştırmaya başladığınızda, lütfen bizimle iletişime geçin. OpSec'inizi gözden geçirmeyi çok isteriz ve bu sağlam olduğunda, aynanıza bağlantı vereceğiz ve sizinle daha yakın çalışmaya başlayacağız. Bu şekilde katkıda bulunmaya istekli olan herkese şimdiden teşekkürler! Bu, cesaret gerektiren bir iş, ancak insanlık tarihindeki en büyük gerçekten açık kütüphanenin uzun ömürlülüğünü sağlamlaştıracaktır. Başlarken Anna’nın Arşivi’nin dayanıklılığını artırmak için aynaları çalıştıracak gönüllüler arıyoruz. Sürümünüz, örneğin “Bob’un Arşivi, bir Anna’nın Arşivi aynası” gibi açıkça bir ayna olarak ayırt edilir. Bu işin getirdiği önemli riskleri almaya hazırsınız. Gerekli operasyonel güvenliği derinlemesine anlıyorsunuz. <a %(a_shadow)s>bu</a> <a %(a_pirate)s>gönderilerin</a> içeriği sizin için aşikardır. Başlangıçta ortak sunucu indirmelerimize erişim vermeyeceğiz, ancak işler yolunda giderse bunu sizinle paylaşabiliriz. Anna’nın Arşivi açık kaynak kod tabanını çalıştırıyorsunuz ve hem kodu hem de verileri düzenli olarak güncelliyorsunuz. Bunu gerçekleştirmek için ekibimizle işbirliği içinde <a %(a_codebase)s>kod tabanımıza</a> katkıda bulunmaya hazırsınız. Bunu arıyoruz: Aynalar: gönüllü çağrısı Başka bir bağış yap. Henüz bağış yapılmadı <a %(a_donate)s>İlk bağışımı yap.</a> Bağışların detayları halka açık olarak gösterilmemektedir. Benim bağışlarım 📡 Koleksiyonumuzun toplu yansıtması için <a %(a_datasets)s>Veri Setleri</a> ve <a %(a_torrents)s>Torrentler</a> sayfalarına bakın. Son 24 saatte IP adresinizden yapılan indirmeler: %(count)s. 🚀 Daha hızlı indirmeler almak ve tarayıcı kontrollerini atlamak için <a %(a_membership)s>üye olun</a>. Ortak siteden indir Beklerken Anna’nın Arşivi'nde farklı bir sekmede gezinmeye devam edebilirsiniz (tarayıcınız arka plan sekmelerini yenilemeyi destekliyorsa). Birden fazla indirme sayfasının aynı anda yüklenmesini bekleyebilirsiniz (ancak lütfen her sunucudan aynı anda yalnızca bir dosya indirin). Bir indirme bağlantısı aldığınızda, bu bağlantı birkaç saat geçerlidir. Beklediğiniz için teşekkürler, bu siteyi herkes için ücretsiz erişilebilir kılıyor! 😊 🔗 Bu dosya için tüm indirme bağlantıları: <a %(a_main)s>Dosya ana sayfası</a>. ❌ Yavaş indirmeler Cloudflare VPN'leri veya Cloudflare IP adreslerinden başka yollarla mevcut değildir. ❌ Yavaş indirmeler yalnızca resmi web sitesi üzerinden mevcuttur. %(websites)s'i ziyaret edin. 📚 İndirmek için şu bağlantıyı kullanın: <a %(a_download)s>Şimdi indir</a>. Herkese dosyaları ücretsiz indirme fırsatı vermek için, bu dosyayı indirmeden önce beklemeniz gerekiyor. Bu dosyayı indirmek için lütfen <span %(span_countdown)s>%(wait_seconds)s</span> saniye bekleyin. Uyarı: Son 24 saat içinde IP adresinizden çok sayıda indirme yapıldı. İndirmeler normalden daha yavaş olabilir. VPN, paylaşılan internet bağlantısı kullanıyorsanız veya ISS'niz IP'leri paylaşıyorsa, bu uyarı bundan kaynaklanıyor olabilir. Kaydet ❌ Bir şeyler yanlış gitti. Lütfen tekrar deneyin. ✅ Kaydedildi. Lütfen sayfayı yenileyin. Görünen adınızı değiştirin. Kimliğiniz ( "#" işaretinden sonraki kısım) değiştirilemez. Profil <span %(span_time)s>%(time)s</span> önce yaratıldı düzenle Listeler Bir dosya bularak ve “Listeler” sekmesini açarak yeni bir liste oluştur. Henüz liste yok Profil bulunamadı. Profil Şu anda kitap isteklerini karşılayamıyoruz. Kitap taleplerinizi bize e-posta ile göndermeyin. Lütfen taleplerinizi Z-Library veya Libgen forumlarında yapın. Anna’nın Arşivi'nde kayıt DOI: %(doi)s İndir SciDB Nexus/STC Henüz önizleme mevcut değil. Dosyayı <a %(a_path)s>Anna’nın Arşivi</a>'nden indirin. İnsan bilgisinin erişilebilirliğini ve uzun vadeli korunmasını desteklemek için <a %(a_donate)s>üye</a> olun. Bonus olarak, 🧬&nbsp;SciDB üyeler için daha hızlı yüklenir ve herhangi bir sınırlama olmadan. Çalışmıyor mu? <a %(a_refresh)s>yenileyin</a> deneyin. Sci-Hub Özel arama alanı ekle Açıklamaları ve üstveri yorumlarını ara Yayın yılı Gelişmiş Erişim İçerik Göster Liste Tablo Dosya tipi Dil Sırala En büyük En uygun En yeni (dosya boyutu) (açık kaynaklı) (yayın yılı) En eski Rastgele En küçük Kaynak AA tarafından kazınmış ve açık kaynaklı hale getirilmiş Dijital Ödünç Verme (%(count)s) Dergi Makaleleri (%(count)s) %(in)s içinde eşleşmeler bulduk. Bir dosya <a %(a_request)s>talep ederken</a> orada bulunan URL'ye başvurabilirsiniz. Üstveri (%(count)s) Kodlarla arama dizinini keşfetmek için <a %(a_href)s>Kodlar Gezgini</a>'ni kullanın. Arama dizini aylık olarak güncellenir. Şu anda %(last_data_refresh_date)s kadar öğe içeriyor. Daha fazla teknik bilgi için %(link_open_tag)sveri kümeleri sayfasına</a> bakın. Hariç tut Sadece dahil et İşaretlenmemiş daha fazla… Sonraki … Önceki Bu arama dizini şu anda Internet Archive’ın Kontrollü Dijital Ödünç Verme kütüphanesinden üstveri içermektedir. <a %(a_datasets)s>Veri kümelerimiz hakkında daha fazla bilgi</a>. Daha fazla dijital ödünç verme kütüphanesi için <a %(a_wikipedia)s>Wikipedia</a> ve <a %(a_mobileread)s>MobileRead Wiki</a>'ye bakın. DMCA / telif hakkı talepleri için <a %(a_copyright)s>buraya tıklayın</a>. İndirme süresi Arama sırasında hata. <a %(a_reload)s>Sayfayı yeniden yüklemeyi</a> deneyin. Sorun devam ederse, lütfen bize %(email)s adresinden e-posta gönderin. Hızlı indirme Aslında, herkes <a %(a_torrents)s>birleşik torrent listemizi</a> tohumlayarak bu dosyaların korunmasına yardımcı olabilir. ➡️ Bazen arama sunucusu yavaş olduğunda bu yanlışlıkla gerçekleşir. Bu gibi durumlarda, <a %(a_attrs)s>yeniden yükleme</a> yardımcı olabilir. ❌ Bu dosyada hatalar olabilir. Makaleler mi arıyorsunuz? Bu arama dizini şu anda çeşitli üstveri kaynaklarından üstveri içermektedir. <a %(a_datasets)s>Datasets hakkında daha fazla bilgi</a>. Dünya genelinde yazılı eserler için birçok, birçok üstveri kaynağı vardır. <a %(a_wikipedia)s>Bu Wikipedia sayfası</a> iyi bir başlangıçtır, ancak başka iyi listeler biliyorsanız lütfen bize bildirin. Üstveri için, orijinal kayıtları gösteriyoruz. Kayıtları birleştirme yapmıyoruz. Şu anda dünyanın en kapsamlı açık kitap, makale ve diğer yazılı eserler kataloğuna sahibiz. Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ve daha fazlasını</a> yansıtıyoruz. <span class="font-bold">Hiç kayıt bulunamadı.</span> Aradığınız kelimeleri veya filtreleri değiştirerek tekrar deneyin. Sonuçlar %(from)s-%(to)s (%(total)s toplam) Başka “gölge kütüphaneler” bulursanız ve bunları yansıtmalıyız ya da herhangi bir sorunuz varsa, lütfen %(email)s adresinden bizimle iletişime geçin. %(num)d yaklaşık eşleşme %(num)d+ yaklaşık eşleşme Dijital ödünç verme kütüphanelerinde dosya aramak için kutuya yazın. %(count)s doğrudan indirilebilir dosyalar kataloğumuzu aramak için kutuya yazın, biz bu dosyaları <a %(a_preserve)s>sonsuzluğa kadar koruyoruz</a>. Aramak için kutuya yazın. %(count)s akademik makale ve dergi makalesi kataloğumuzu aramak için kutuya yazın, biz bu makaleleri <a %(a_preserve)s>sonsuzluğa kadar koruyoruz</a>. Kütüphanelerden üstveri aramak için kutuya yazın. Bu, <a %(a_request)s>bir dosya talep ederken</a> faydalı olabilir. İpucu: Daha hızlı gezinme için klavye kısayollarını kullanın “/” (arama odak), “enter” (arama), “j” (yukarı), “k” (aşağı), “<” (önceki sayfa), “>” (sonraki sayfa). Bunlar üstveri kayıtlarıdır, <span %(classname)s>indirilebilir dosyalar</span> değildir. Arama ayarları Ara Dijital Ödünç Verme İndir Dergi makaleleri Üstveri Yeni Arama %(search_input)s - Arama Arama çok uzun sürdü, bu da yanlış sonuçlar görebileceğiniz anlamına gelir. Bazen sayfayı <a %(a_reload)s>yeniden yüklemek</a> yardımcı olabilir. Arama çok uzun sürdü, bu geniş sorgular için yaygındır. Filtre sayıları doğru olmayabilir. Libgen veya Z-Library tarafından kabul edilmeyen büyük yüklemeler (10.000'den fazla dosya) için lütfen %(a_email)s adresinden bizimle iletişime geçin. Libgen.li için, önce <a %(a_forum)s>forumlarına</a> kullanıcı adı %(username)s ve şifre %(password)s ile giriş yaptığınızdan emin olun ve ardından <a %(a_upload_page)s>yükleme sayfalarına</a> geri dönün. Şimdilik, yeni kitapları Library Genesis forklarına yüklemeyi tavsiye ediyoruz. Burada <a %(a_guide)s>yararlı bir rehber</a> bulabilirsiniz. Bu websitede dizinlediğimiz her iki forkun aynı karşıya yükleme sisteminden çektiğini dikkate alın. Küçük yüklemeler için (10.000 dosyaya kadar) lütfen hem %(first)s hem de %(second)s adreslerine yükleyin. Alternatif olarak, onları Z-Library'e <a %(a_upload)s>buradan</a> yükleyebilirsiniz. Akademik makaleleri yüklemek için lütfen (Library Genesis'e ek olarak) <a %(a_stc_nexus)s>STC Nexus</a>'a da yükleyin. Yeni makaleler için en iyi gölge kütüphanedir. Henüz onları entegre etmedik, ama bir noktada edeceğiz. <a %(a_telegram)s>Telegram'daki yükleme botlarını</a> kullanabilir veya bu şekilde yüklemek için çok fazla dosyanız varsa sabit mesajlarında listelenen adresle iletişime geçebilirsiniz. <span %(label)s>Yoğun gönüllü çalışma (USD$50-USD$5,000 ödüller):</span> eğer görevimize çok zaman ve/veya kaynak ayırabiliyorsanız, sizinle daha yakından çalışmak isteriz. Sonunda iç ekibe katılabilirsiniz. Sıkı bir bütçemiz olmasına rağmen, en yoğun çalışmalar için <span %(bold)s>💰 parasal ödüller</span> verebiliyoruz. <span %(label)s>Hafif gönüllü çalışması:</span> sadece burada ve orada birkaç saat ayırabiliyorsanız, yine de yardımcı olabileceğiniz birçok yol var. Tutarlı gönüllüleri <span %(bold)s>🤝 Anna’nın Arşivi üyelikleri</span> ile ödüllendiriyoruz. Anna’nın Arşivi sizin gibi gönüllülere dayanır. Tüm bağlılık seviyelerini memnuniyetle karşılıyoruz ve aradığımız iki ana yardım kategorisi var: Zamanınızı gönüllü olarak ayıramıyorsanız, yine de <a %(a_donate)s>para bağışlayarak</a>, <a %(a_torrents)s>torrentlerimizi paylaşarak</a>, <a %(a_uploading)s>kitap yükleyerek</a> veya <a %(a_help)s>Anna’nın Arşivi’ni arkadaşlarınıza anlatarak</a> bize çok yardımcı olabilirsiniz. <span %(bold)s>Şirketler:</span> koleksiyonlarımıza yüksek hızlı doğrudan erişim sunuyoruz, karşılığında kurumsal düzeyde bağış veya yeni koleksiyonlar (örneğin yeni taramalar, OCR edilmiş datasets, verilerimizi zenginleştirme) talep ediyoruz. Eğer bu sizseniz <a %(a_contact)s>bizimle iletişime geçin</a>. Ayrıca <a %(a_llm)s>LLM sayfamıza</a> bakın. Ödüller Sağlam programlama veya saldırgan güvenlik becerilerine sahip insanları her zaman arıyoruz. İnsanlığın mirasını korumada ciddi bir katkı sağlayabilirsiniz. Teşekkür olarak, sağlam katkılar için üyelik veriyoruz. Büyük bir teşekkür olarak, özellikle önemli ve zor görevler için parasal ödüller veriyoruz. Bu, bir işin yerine geçecek bir şey olarak görülmemelidir, ancak ek bir teşvik ve oluşan maliyetlere yardımcı olabilir. Kodlarımızın çoğu açık kaynaklıdır ve ödül verirken sizin kodunuzun da açık kaynaklı olmasını isteyeceğiz. Bireysel olarak tartışabileceğimiz bazı istisnalar vardır. Ödüller, bir görevi ilk tamamlayan kişiye verilir. Başkalarına bir şey üzerinde çalıştığınızı bildirmek için bir ödül biletine yorum yapmaktan çekinmeyin, böylece başkaları bekleyebilir veya sizinle işbirliği yapabilir. Ancak, başkalarının da üzerinde çalışmakta ve sizi geçmeye çalışmakta özgür olduğunu unutmayın. Ancak, özensiz işler için ödül vermiyoruz. İki yüksek kaliteli gönderim birbirine yakın zamanda yapılırsa (bir veya iki gün içinde), takdirimize bağlı olarak her iki gönderime de ödül verebiliriz, örneğin ilk gönderim için 100%% ve ikinci gönderim için 50%% (toplamda 150%%). Daha büyük ödüller (özellikle scraping ödülleri) için, yaklaşık ~5%% tamamladığınızda ve yönteminizin tam hedefe ulaşacağını düşündüğünüzde bizimle iletişime geçin. Yönteminizi bizimle paylaşmanız gerekecek, böylece geri bildirim verebiliriz. Ayrıca, bu şekilde birden fazla kişinin ödüle yaklaşması durumunda ne yapacağımıza karar verebiliriz, örneğin ödülü birden fazla kişiye vermek, insanları işbirliğine teşvik etmek gibi. UYARI: yüksek ödüllü görevler <span %(bold)s>zordur</span> — daha kolay olanlarla başlamak akıllıca olabilir. <a %(a_gitlab)s>Gitlab sorun listemize</a> gidin ve “Etiket önceliği”ne göre sıralayın. Bu, önemsediğimiz görevlerin yaklaşık sırasını gösterir. Açıkça ödül belirtilmeyen görevler hala üyelik için uygundur, özellikle “Kabul Edildi” ve “Anna’nın favorisi” olarak işaretlenenler. “Başlangıç projesi” ile başlamak isteyebilirsiniz. Hafif gönüllü çalışma Artık %(matrix)s'te senkronize bir Matrix kanalımız da var. Boş zamanınız varsa, birçok şekilde yardımcı olabilirsiniz. <a %(a_telegram)s>Telegram’daki gönüllüler sohbetine</a> katılmayı unutmayın. Bir takdir göstergesi olarak, genellikle temel kilometre taşları için 6 aylık “Şanslı Kütüphaneci” üyeliği veriyoruz ve devam eden gönüllü çalışmalar için daha fazlasını sunuyoruz. Tüm kilometre taşları yüksek kaliteli çalışma gerektirir — özensiz çalışma bize yardımcı olmaktan çok zarar verir ve bunu reddederiz. Bir kilometre taşına ulaştığınızda lütfen <a %(a_contact)s>bize e-posta gönderin</a>. %(links)s yerine getirdiğiniz isteklerin bağlantıları veya ekran görüntüleri. Z-Library veya Library Genesis forumlarında kitap (veya makale vb.) isteklerini yerine getirme. Kendi kitap istek sistemimiz yok, ancak bu kütüphaneleri yansıtıyoruz, bu yüzden onları daha iyi hale getirmek Anna’nın Arşivi’ni de daha iyi hale getirir. Kilometre Taşı Görev Göreve bağlıdır. <a %(a_telegram)s>Telegram’daki gönüllüler sohbetinde</a> yayınlanan küçük görevler. Genellikle üyelik için, bazen küçük ödüller için. Gönüllü sohbet grubumuzda küçük görevler paylaşılıyor. Düzelttiğiniz sorunlara yorum bırakmayı unutmayın, böylece başkaları işinizi tekrarlamaz. %(links)s iyileştirdiğiniz kayıtların bağlantıları. Başlangıç noktası olarak <a %(a_list)s >rastgele metadata sorunları listesini</a> kullanabilirsiniz. Üstveriyi <a %(a_metadata)s>Open Library ile bağlantı kurarak</a> iyileştirin. Bunlar, birine Anna'nın Arşivi'nden bahsettiğinizi ve onların size teşekkür ettiğini göstermelidir. %(links)s bağlantılar veya ekran görüntüleri. Anna'nın Arşivi'nin duyurusunu yapmak. Örneğin, AA'deki kitapları tavsiye ederek, blog yazılarımıza bağlantı vererek veya genel olarak insanları web sitemize yönlendirerek. Bir dili tamamen çevirin (eğer zaten tamamlanmaya yakın değilse). <a %(a_translate)s>Web sitesini çevirme</a>. Önemli katkılar yaptığınızı gösteren düzenleme geçmişi bağlantısı. Kendi dilinizde Anna’nın Arşivi için Wikipedia sayfasını iyileştirin. Diğer dillerdeki AA’nın Wikipedia sayfasından ve web sitemizden ve blogumuzdan bilgi ekleyin. AA’ya diğer ilgili sayfalarda referanslar ekleyin. Gönüllülük ve Ödüller 