msgid "layout.index.invalid_request"
msgstr "Geçersiz istek. %(websites)s adresini ziyaret edin."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Ödünç Verme Kütüphanesi"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " ve "

msgid "layout.index.header.tagline_and_more"
msgstr "ve daha fazlası"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;%(libraries)s kütüphanelerini yansıtıyoruz."

msgid "layout.index.header.tagline_newnew2b"
msgstr "%(scraped)s'i kazıyıp açık kaynağa çeviriyoruz."

msgid "layout.index.header.tagline_open_source"
msgstr "Tüm kodlarımız ve verilerimiz tamamen açık kaynaklıdır."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;İnsanlık tarihinin gerçek anlamda en büyük açık kütüphanesi."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;kitaplar, %(paper_count)s&nbsp;makaleler — sonsuza dek korunur."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Dünyanın en büyük açık kaynak açık veri kütüphanesi. ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library ve daha fazlasını içerir. 📈&nbsp;%(book_any)s kitap, %(journal_article)s makale, %(book_comic)s çizgi roman, %(magazine)s dergi — sonsuza kadar saklı."

msgid "layout.index.header.tagline_short"
msgstr "📚 Dünyanın en büyük açık kaynaklı açık-veri kütüphanesi. <br>⭐️ Scihub, Libgen, Zlib ve daha fazlası."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Hatalı üst veri (ör. başlık, açıklama, kapak resmi)"

msgid "common.md5_report_type_mapping.download"
msgstr "İndirme problemleri (ör. bağlanamıyor, hata mesajı, çok yavaş)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Dosya açılamıyor (ör. bozuk dosya, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Düşük kalite (ör. biçimlendirme hataları, kötü tarama kalitesi, kayıp sayfalar)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / dosya kaldırılmalı (ör. reklam, rahatsız edici içerik)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Telif hakkı talebi"

msgid "common.md5_report_type_mapping.other"
msgstr "Diğer"

msgid "common.membership.tier_name.bonus"
msgstr "İlave indirmeler"

msgid "common.membership.tier_name.2"
msgstr "Zeki Kitap Kurdu"

msgid "common.membership.tier_name.3"
msgstr "Şanslı Kütüphaneci"

msgid "common.membership.tier_name.4"
msgstr "Muhteşem Veri İstifçisi"

msgid "common.membership.tier_name.5"
msgstr "Harika Arşivci"

msgid "common.membership.format_currency.total"
msgstr "toplam %(amount)s"

msgid "common.membership.format_currency.total_with_usd"
msgstr "toplam %(amount)s %(amount_usd)s"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "ödenmedi"

msgid "common.donation.order_processing_status_labels.1"
msgstr "ödendi"

msgid "common.donation.order_processing_status_labels.2"
msgstr "iptal edildi"

msgid "common.donation.order_processing_status_labels.3"
msgstr "süresi doldu"

msgid "common.donation.order_processing_status_labels.4"
msgstr "Anna'nın onaylamasını bekle"

msgid "common.donation.order_processing_status_labels.5"
msgstr "geçersiz"

msgid "page.donate.title"
msgstr "Bağış yap"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Halihazırda devam eden <a %(a_donation)s>var olan bağış</a>'ın var. Lütfen yeni bir bağış yapmadan önce bu bağışı bitir ya da iptal et."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Tüm bağışlarımı gör</a>"

msgid "page.donate.header.text1"
msgstr "Anna’s Archive; kâr amacı gütmeyen, açık kaynaklı, açık verili bir projedir. Bağış yaparak ve üyemiz olarak işlemlerimize ve geliştirme sürecimize destek olursun. Tüm üyelerimize: Devam etmemizi sağladığınız için teşekkürler!❤️"

msgid "page.donate.header.text2"
msgstr "Daha fazla bilgi için <a %(a_donate)s>Bağış SSS</a>'e göz atınız."

msgid "page.donate.refer.text1"
msgstr "Daha fazla indirme hakkı kazanmak için, <a %(a_refer)s>arkadaşlarınıza tavsiye edin</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "%(percentage)s ilave hızlı indirme edindiniz, çünkü %(profile_link)s tarafından yönlendirildiniz."

msgid "page.donate.bonus_downloads.period"
msgstr "Bu bütün üyelik süresine geçerli."

msgid "page.donate.perks.fast_downloads"
msgstr "Her gün %(number)s hızlı indirme"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "bu ay bağış yaparsanız!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / ay"

msgid "page.donate.buttons.join"
msgstr "Katıl"

msgid "page.donate.buttons.selected"
msgstr "Seçildi"

msgid "page.donate.buttons.up_to_discounts"
msgstr "%(percentage)s%%'e varan indirimler"

msgid "page.donate.perks.scidb"
msgstr "Doğrulama olmaksızın <strong>sınırsız</strong> SciDB sayfasına erişim"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> erişimi"

msgid "page.donate.perks.refer"
msgstr "<a %(a_refer)s>Arkadaşlarınızı davet ederek</a> <strong>%(percentage)s%% bonus indirme</strong> kazanın."

msgid "page.donate.perks.credits"
msgstr "Katkıda Bulunanlar kısmında kullanıcı adınızla ya da anonim olarak değinme"

msgid "page.donate.perks.previous_plus"
msgstr "Önceki avantajlar, artı:"

msgid "page.donate.perks.early_access"
msgstr "Yeni özelliklere erken erişim"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Sahne arkası güncellemeleri içeren özel Telegram kanalı"

msgid "page.donate.perks.adopt"
msgstr "“Bir torrent sahiplen”: <div %(div_months)s>her 12 aylık üyelikte bir defa</div> bir torrent dosyasında kullanıcı adınız veya mesajınız"

msgid "page.donate.perks.legendary"
msgstr "İnsanlığın bilgisi ve kültürünü korumakta efsanevi rol"

msgid "page.donate.expert.title"
msgstr "Uzman Erişimi"

msgid "page.donate.expert.contact_us"
msgstr "bize ulaşın"

msgid "page.donate.small_team"
msgstr "Biz gönüllülerden oluşan küçük bir ekibiz. Cevap vermemiz 1-2 haftayı bulabilir."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Sınırsız</strong> yüksek hızlı erişim"

msgid "page.donate.expert.direct_sftp"
msgstr "Doğrudan <strong>SFTP</strong> sunucuları"

msgid "page.donate.expert.enterprise_donation"
msgstr "Kurumsal düzeyde bağış yapın veya yeni koleksiyonlar için takas yapın (örneğin, yeni taramalar, OCR edilmiş datasetler)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Varlıklı bireylerden ya da enstitülerden büyük bağışları başımızın üstünde tutuyoruz. "

msgid "page.donate.header.large_donations"
msgstr "5000$'ın üstündeki bağışlar için lütfen bizimle direkt olarak %(email)s üzerinden iletişime geçin."

msgid "page.donate.header.recurring"
msgstr "Bu sayfadaki üyeliklerin “aylık” olduğuna dikkat edin, bunlar tek seferlik (tekrarlanmayan) bağışlardır. <a %(faq)s>Bağış SSS</a>ye bakın."

msgid "page.donate.without_membership"
msgstr "Üyelik olmadan (herhangi bir miktarda) bağış yapmak isterseniz, Monero (XMR) adresini kullanabilirsiniz: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Lütfen bir ödeme yöntemi seçin."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(geçici olarak ulaşılamıyor)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s hediye kartı"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Banka kartı (uygulama kullanarak)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredi/banka kartı"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (düzenli)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Kart / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Kredi kartı/banka kartı/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brezilya)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Banka kartı"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredi/banka kartı (yedek)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredi/banka kartı 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay / WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "Kripto kullanarak BTC, ETH, XMR ve SOL ile bağış yapabilirsin. Bu seçeneği halihazırda kripto para birimlerine alışkınsan kullan."

msgid "page.donate.payment.desc.crypto2"
msgstr "Kripto ile BTC, ETH, XMR ve diğerlerini kullanarak bağış yapabilirsiniz."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Kriptoyu ilk kez kullanıyorsanız, Bitcoin (orijinal ve en çok kullanılan kripto para birimi) satın almak ve bağış yapmak için %(options)s'i kullanmanızı öneririz."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "PayPal ABD ile bağışta bulunmak için, anonim olmamıza izin veren PayPal Kripto kullanacağı. Bu metotla nasıl bağış yapılacağını öğrenmeye vakit ayırdığınız için müteşekkiriz, bu bize çok yardımcı olur."

msgid "page.donate.payment.desc.paypal_short"
msgstr "PayPal kullanarak bağış yap."

msgid "page.donate.payment.desc.cashapp"
msgstr "Cash App kullanarak bağış yap."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Cash App kullanıyorsanız, bağış yapmak için en kolay yöntem bu!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "%(amount)s altındaki transferler için Cash App'in %(fee)s ücret kesebileceği aklınızda bulunsun. %(amount)s ve üstü için ücret yok!"

msgid "page.donate.payment.desc.revolut"
msgstr "Revolut kullanarak bağış yapın."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Revolut'unuz varsa, bağış yapmanın en kolay yolu bu!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Kredi veya banka kartı ile bağış yapın."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay ve Apple Pay de işe yarayabilir."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Daha küçük bağışlarda kredi kartında kesilen ücretler bizim %(discount)s%% indirimimizi kaldırabilir, bu yüzden daha uzun abonelikler öneririz."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Küçük bağışlar için kesilen ücretlerin yüksek olduğunu unutmayın, bu nedenle daha uzun abonelikler yapmanızı öneririz."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Binance ile kredi/banka kartı veya banka hesabı kullanarak Bitcoin satın alabilir ve ardından bu Bitcoin'i bize bağışlayabilirsiniz. Bu şekilde bağışınızı kabul ederken güvenli ve anonim kalabiliriz."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance neredeyse her ülkede mevcuttur ve çoğu banka ve kredi/banka kartını destekler. Şu anda ana önerimiz bu yöntemdir. Bu yöntemi kullanarak bağış yapmayı öğrenmek için zaman ayırdığınız için minnettarız, çünkü bu bize çok yardımcı oluyor."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Ana PayPal hesabınızı kullanarak bağış yapın."

msgid "page.donate.payment.desc.givebutter"
msgstr "Kredi/banka kartı, PayPal veya Venmo kullanarak bağış yapın. Bir sonraki sayfada bunlar arasında seçim yapabilirsiniz."

msgid "page.donate.payment.desc.amazon"
msgstr "Amazon Hediye Kartı kullanarak bağış yapın."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Bayilerimiz tarafından kabul edilen miktarlara (minimum %(minimum)slar) yuvarlamamız gerektiğini unutmayın."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>ÖNEMLİ</strong> Sadece Amazon.com'u destekliyoruz, diğer Amazon sitelerini değil. Mesela, .tr, .de, .co.uk destekli DEĞİL."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>ÖNEMLİ:</strong> Bu seçenek %(amazon)s içindir. Başka bir Amazon web sitesini kullanmak istiyorsanız, yukarıdan seçin."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Bu yöntem, bir kripto para sağlayıcısını ara dönüştürücü olarak kullanır. Bu biraz kafa karıştırıcı olabilir, bu yüzden lütfen diğer ödeme yöntemleri çalışmazsa bu yöntemi kullanın. Ayrıca her ülkede çalışmaz."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Alipay uygulaması üzerinden kredi/banka kartı kullanarak bağış yapın (kurulumu çok kolay)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Alipay uygulamasını yükleyin"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Alipay uygulamasını <a %(a_app_store)s>Apple App Store</a> veya <a %(a_play_store)s>Google Play Store</a>'dan yükleyin."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Telefon numaranızı kullanarak kaydolun."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Başka kişisel bilgi gerekmemektedir."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Banka kartı ekleyin"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Desteklenenler: Visa, MasterCard, JCB, Diners Club ve Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Daha fazla bilgi için <a %(a_alipay)s>bu kılavuza</a> bakın."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Bankalar bizimle çalışmak istemediği için kredi/banka kartlarını doğrudan destekleyemiyoruz. ☹ Ancak, kredi/banka kartlarını kullanmanın birkaç yolu var, bunlar da diğer ödeme yöntemlerini kullanmaktır:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Hediye Kartı"

msgid "page.donate.ccexp.amazon_com"
msgstr "Kredi/banka kartınızı kullanarak bize Amazon.com hediye kartları gönderebilirsiniz."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay uluslararası kredi/banka kartlarını destekler. Daha fazla bilgi için <a %(a_alipay)s>bu kılavuza</a> bakın."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) uluslararası kredi/banka kartlarını destekler. WeChat uygulamasında, “Ben => Hizmetler => Cüzdan => Kart Ekle\"ye gidin. Bunu görmüyorsanız, “Ben => Ayarlar => Genel => Araçlar => Weixin Pay => Etkinleştir”i kullanarak etkinleştirin."

msgid "page.donate.ccexp.crypto"
msgstr "Kredi/banka kartlarını kullanarak kripto satın alabilirsiniz."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Hızlı kripto hizmetleri"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Hızlı hizmetler kullanışlıdır, ancak daha yüksek ücretler alınır."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Yüksek miktardaki bağışlarınızı daha hızlı yapmak istiyorsanız ve $5-10'luk bir ücreti önemsemiyorsanız, bu yöntemi kripto yerine kullanabilirsiniz."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Bağış sayfasında gösterilen kripto miktarını gönderdiğinizden emin olun, $USD miktarını gönderirseniz sorun olabilir."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Aksi takdirde komisyon ücreti düşülecek ve üyeliğinizi otomatik olarak işleyemeyeceğiz."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(en az: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(en az: %(minimum)s ülkeye bağlı olarak değişir, ilk işlem için doğrulama yoktur)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(en az: %(minimum)s, ilk işlem için doğrulama yoktur)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(en az: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(en az: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(en az: %(minimum)s )"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Bu bilgilerin herhangi biri güncel değilse lütfen bize e-posta göndererek bildirin."

msgid "page.donate.payment.desc.bmc"
msgstr "Kredi kartları, banka kartları, Apple Pay ve Google Pay için “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>) kullanıyoruz. Onların sisteminde bir “kahve” 5$'a eşittir, bu yüzden bağışınız en yakın 5'in katına yuvarlanacaktır."

msgid "page.donate.duration.intro"
msgstr "Ne kadar süre için abone olacağını seç."

msgid "page.donate.duration.1_mo"
msgstr "1 ay"

msgid "page.donate.duration.3_mo"
msgstr "3 ay"

msgid "page.donate.duration.6_mo"
msgstr "6 ay"

msgid "page.donate.duration.12_mo"
msgstr "12 ay"

msgid "page.donate.duration.24_mo"
msgstr "24 ay"

msgid "page.donate.duration.48_mo"
msgstr "48 ay"

msgid "page.donate.duration.96_mo"
msgstr "96 ay"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><span %(span_discount)s></span> <div %(div_after)s>indirimden sonra</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Bu ödeme yöntemi minimum %(amount)s destekliyor. Lütfen başka bir tutar ya da ödeme yöntemi seçin."

msgid "page.donate.buttons.donate"
msgstr "Bağış Yap"

msgid "page.donate.payment.maximum_method"
msgstr "Bu ödeme yöntemi maksimum %(amount)s destekliyor. Lütfen başka bir tutar ya da ödeme yöntemi seçin."

msgid "page.donate.login2"
msgstr "Üye olmak için, lütfen <a %(a_login)s>Giriş yap ya da Kaydol</a>. Desteğiniz için teşekkürler!"

msgid "page.donate.payment.crypto_select"
msgstr "Tercih ettiğiniz kripto parayı seçin:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(en düşük miktar)"

msgid "page.donate.coinbase_eth"
msgstr "(Coinbase'den Ethereum gönderirken kullanın)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(uyarı: yüksek minimum miktar)"

msgid "page.donate.submit.confirm"
msgstr "Bağışı onaylama için bağış tuşuna tıkla."

msgid "page.donate.submit.button"
msgstr "Bağışla <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Ödeme sırasında bağışı hâlâ iptal edebilirsiniz."

msgid "page.donate.submit.success"
msgstr "✅ Bağış sayfasına yönlendiriliyor…"

msgid "page.donate.submit.failure"
msgstr "❌ Bir şeyler ters gitti. Lütfen sayfayı yenileyin ve tekrar deneyin."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / ay"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "1 ay için"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "3 ay için"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "6 ay için"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "12 ay için"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "24 ay için"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "48 ay boyunca geçerli"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "96 ay boyunca geçerli"

msgid "page.donate.submit.button.label.1_mo"
msgstr "1 ay için “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "3 ay için “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "6 ay için “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "12 ay için “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "24 ay için “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "48 ay boyunca geçerli “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "96 ay boyunca geçerli “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Bağış"

msgid "page.donation.header.date"
msgstr "Tarih: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Toplam: %(total)s <span %(span_details)s>(%(discounts)s%% indirim dâhil, %(duration)s ay için aylık %(monthly_amount_usd)s)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Toplam: %(total)s <span %(span_details)s>(%(duration)s ay için aylık %(monthly_amount_usd)s)</span>"

msgid "page.donation.header.status"
msgstr "Durum: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Tanımlayıcı: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "İptal Et"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "İptal etmek istediğine emin misin? Zaten ödemeyi yaptıysan iptal etme."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Evet, iptal et"

msgid "page.donation.header.cancel.success"
msgstr "✅ Bağışın iptal edildi."

msgid "page.donation.header.cancel.new_donation"
msgstr "Yeni bir bağış yap"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Bir şeyler yanlış gitti. Lütfen sayfayı yenile ve tekrar dene."

msgid "page.donation.header.reorder"
msgstr "Yeniden sipariş ver"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Zaten ödemeyi yaptın. Eğer yine de ödeme talimatlarını tekrar gözden geçirmek istersen buraya tıkla:"

msgid "page.donation.old_instructions.show_button"
msgstr "Eski ödeme talimatlarını göster"

msgid "page.donation.thank_you_donation"
msgstr "Bağışınız için teşekkürler!"

msgid "page.donation.thank_you.secret_key"
msgstr "Eğer hala yapmadıysanız, giriş yapmak için gizli anahtarınızı girin:"

msgid "page.donation.thank_you.locked_out"
msgstr "Aksi takdirde bu hesaba erişiminiz kilitlenebilir!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Ödeme yönergeleri şu anda eskimiş durumda. Eğer başka bir bağış yapmak isterseniz, yukarıdaki \"Yeniden sipariş ver\" butonunu kullanın."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Önemli not:</strong> Kripto fiyatları bazen birkaç dakikada 20%% olacak kadar çılgınca dalgalanabilir. Bu yine de çoğu ödeme sağlayıcısına ödediğimiz komisyonlardan düşüktür; genellikle bizim gibi bir \"gölge hayır işi\" ile çalışırken 50-60%% komisyon keserler. <u>Eğer bize ödediğiniz asıl fiyatla birlikte bir makbuz gönderirseniz, sizin hesabınıza seçilen üyeliği tanımlayacağız </u> (makbuzun birkaç saatten daha eski olmaması koşuluyla). Bizi desteklemek için böyle şeylere katlanmaya niyetli olmanızı gerçekten takdir ediyoruz! ❤️"

msgid "page.donation.expired"
msgstr "Bu bağışın süresi doldu. Lütfen iptal edin ve yeni bir tane başlatın."

msgid "page.donation.payment.crypto.top_header"
msgstr "Kripto talimatları"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Kripto hesaplarımızdan birine transfer edin"

msgid "page.donation.payment.crypto.text1"
msgstr "Bu adreslerden birine toplam %(total)s miktarını bağışlayın:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>PayPal'da Bitcoin al"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Paypal uygulamanızda ya da websitesinde \"Crypto\" sayfasını bulun. Bu genelde \"Finans\" bölümünün altında bulunur."

msgid "page.donation.payment.paypal.text3"
msgstr "Bitcoin (BTC) almak için talimatları takip edin. Yalnızca bağış yapmak istediğiniz miktarı satın almanız gerekli, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Bitcoini adresimize transfer edin"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Paypal uygulamanızda ya da websitesinde “Bitcoin” sayfasına gidin. “Para gönder” butonuna %(transfer_icon)s, basın ve ardından “Gönder”."

msgid "page.donation.payment.paypal.text5"
msgstr "Bitcoin (BTC) adresimizi alıcı olarak girin ve %(total)s tutarındaki bağışınızı göndermek için talimatları takip edin:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredi/banka kartı talimatları"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Kredi/ banka kartı sayfamızdan bağış yapın"

msgid "page.donation.donate_on_this_page"
msgstr "<a %(a_page)s>Bu sayfadan</a> %(amount)s bağış yapın."

msgid "page.donation.stepbystep_below"
msgstr "Aşağıdaki adım-adım rehbere bakın."

msgid "page.donation.status_header"
msgstr "Durum:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Onay bekleniyor (kontrol etmek için sayfayı yenileyin)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Transfer için bekleniyor (kontrol etmek için sayfayı yenileyin)…"

msgid "page.donation.time_left_header"
msgstr "Kalan zaman:"

msgid "page.donation.might_want_to_cancel"
msgstr "(iptal edip yeni bir bağış oluşturmak isteyebilirsiniz)"

msgid "page.donation.reset_timer"
msgstr "Zamanlayıcıyı sıfırlamak için, basitçe yeni bir bağış oluşturun."

msgid "page.donation.refresh_status"
msgstr "Durumu güncelle"

msgid "page.donation.footer.issues_contact"
msgstr "Eğer herhangi bir sorunla karşılaşırsanız, lütfen bizle %(email)s adresinde iletişime geçin ve olabildiğince fazla bilgi ilave edin (ekran görüntüleri gibi)."

msgid "page.donation.expired_already_paid"
msgstr "Eğer zaten ödeme yaptıysanız:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Bazen onay süreci 24 saate kadar sürebilir, bu yüzden bu sayfayı yenilediğinizden emin olun (süresi dolmuş olsa bile)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "PayPal'dan PYUSD satın alın"

msgid "page.donation.pyusd.instructions"
msgstr "PYUSD coin (PayPal USD) almak için talimatları takip edin."

msgid "page.donation.pyusd.more"
msgstr "Transfer ücretlerini karşılamak için, bağış yapacağınız tutardan(%(amount)s) biraz daha fazla (%(more)s daha tavsiye ediyoruz) satın alın. Artan para sizde kalacak."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "PayPal uygulamanızda veya web sitenizde “PYUSD” sayfasına gidin. “Transfer” düğmesine %(icon)s basın ve ardından “Gönder” seçeneğine tıklayın."

msgid "page.donation.transfer_amount_to"
msgstr "%(account)s'a %(amount)s bağış yapın"

msgid "page.donation.cash_app_btc.step1"
msgstr "Cash App ile Bitcoin (BTC) Satın Alın"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Cash App'te “Bitcoin” (BTC) sayfasına gidin."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Bağışladığınız miktarı (%(amount)s) karşılamak için işlem ücretlerini kapsayacak şekilde biraz daha fazla (biz %(more)s daha fazla öneriyoruz) satın alın. Kalan miktar sizde kalacaktır."

msgid "page.donation.cash_app_btc.step2"
msgstr "Bitcoin'i adresimize transfer edin"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "“Bitcoin gönder” butonuna tıklayarak bir “çekim” yapın. %(icon)s simgesine basarak dolardan BTC'ye geçiş yapın. Aşağıya BTC miktarını girin ve “Gönder”e tıklayın. Takılırsanız <a %(help_video)s>bu videoyu</a> izleyin."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Küçük bağışlar için (25$'ın altındaki), Rush veya Priority kullanmanız gerekebilir."

msgid "page.donation.revolut.step1"
msgstr "Revolut üzerinden Bitcoin (BTC) satın alın"

msgid "page.donation.revolut.step1.text1"
msgstr "Bitcoin (BTC) satın almak için Revolut'ta “Kripto” sayfasına gidin."

msgid "page.donation.revolut.step1.more"
msgstr "İşlem ücretlerini karşılamak için bağışladığınız miktardan biraz daha fazlasını satın alın (%(amount)s) (%(more)s fazlasını öneririz). Geriye kalan her şeyi kendinize saklayacaksınız."

msgid "page.donation.revolut.step2"
msgstr "Bitcoin'i adresimize transfer edin"

msgid "page.donation.revolut.step2.transfer"
msgstr "“Bitcoin gönder” butonuna tıklayarak bir “çekim” yapın. %(icon)s simgesine basarak Euro'dan BTC'ye geçiş yapın. Aşağıdaki BTC miktarını girin ve “Gönder”e tıklayın. Takılırsanız <a %(help_video)s>bu videoyu</a> izleyin."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Aşağıda yazan BTC miktarını girdiğinizden emin olun, euro veya dolar olarak <em>GİRMEYİN</em>, aksi takdirde doğru miktarı alamayacağımızdan üyeliğinizi otomatik olarak doğrulayamayacağız."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Küçük bağışlar için (25$'ın altındaki), Rush veya Priority kullanmanız gerekebilir."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Sadece birkaç dakika süren aşağıdaki “kredi kartından Bitcoin'e” hızlı hizmetlerden herhangi birini kullanın:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Formdaki aşağıdaki bilgileri doldurun:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin miktarı:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Lütfen bu <span %(underline)s>tam miktarı</span> kullanın. Kredi kartı ücretleri nedeniyle toplam maliyetiniz daha yüksek olabilir. Maalesef, küçük miktarlar için bu bizim indirimimizden daha fazla olabilir."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adresi (harici cüzdan):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s talimatları"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Yalnızca standart kripto para birimlerini destekliyoruz, egzotik ağlar veya para birimi versiyonları desteklenmemektedir. İşlemin onaylanması, para birimine bağlı olarak bir saate kadar sürebilir."

msgid "page.donation.crypto_qr_code_title"
msgstr "Ödemek için QR kodunu tarayın"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Ödeme ayrıntılarını hızlı bir şekilde doldurmak için bu QR kodunu kripto cüzdan uygulamanızla tarayın"

msgid "page.donation.amazon.header"
msgstr "Amazon hediye kartı"

msgid "page.donation.amazon.form_instructions"
msgstr "Lütfen aşağıdaki e-posta adresine %(amount)s tutarında bir hediye kartı göndermek için <a %(a_form)s>resmi Amazon.com formunu</a> kullanın."

msgid "page.donation.amazon.only_official"
msgstr "Diğer hediye kartı yöntemlerini kabul edemiyoruz, <strong>yalnızca Amazon.com'daki resmi formdan doğrudan gönderilenleri kabul ediyoruz</strong>. Bu formu kullanmazsanız hediye kartınızı iade edemeyiz."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Tam miktarı girin: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Lütfen kendi mesajınızı YAZMAYIN."

msgid "page.donation.amazon.form_to"
msgstr "Formdaki \"Alıcı\" e-posta adresi:"

msgid "page.donation.amazon.unique"
msgstr "Sizin hesabınıza özeldir, kimseyle paylaşmayın."

msgid "page.donation.amazon.only_use_once"
msgstr "Sadece bir kez kullanın."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Hediye kartı bekleniyor... (kontrol etmek için sayfayı yenileyin)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Hediye kartınızı gönderdikten sonra sistemimiz birkaç dakika içinde onaylayacaktır. Eğer bu işe yaramazsa hediye kartını tekrar göndermeyi deneyin. (<a %(a_instr)s>talimatlar</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Bu hala işe yaramazsa lütfen bize e-posta gönderin; Anna bunu manuel olarak inceleyecektir (bu birkaç gün sürebilir) daha öncesinde yeniden göndermeyi deneyip denemediğinizi bahsetmeyi unutmayın."

msgid "page.donation.amazon.example"
msgstr "Örnek:"

msgid "page.donate.strange_account"
msgstr "Hesap adının veya resminin garip görünebileceğini aklınızda bulundurun. Endişelenmenize gerek yok! Bu hesaplar bağış ortaklarımız tarafından yönetilmektedir. Hesaplarımız çalınmamıştır."

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay talimatları"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Alipay'de bağış yap"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Toplam %(total)s tutarını <a %(a_account)s>bu Alipay hesabını</a> kullanarak bağışlayın."

msgid "page.donation.page_blocked"
msgstr "Eğer bağış sayfası engellenirse farklı bir internet bağlantısı deneyin (örneğin, VPN veya telefon interneti)."

msgid "page.donation.payment.alipay.error"
msgstr "Ne yazık ki, Alipay sayfası genellikle yalnızca <strong>anakara Çin</strong>'den erişilebilir. VPN'inizi geçici olarak devre dışı bırakmanız veya konumu anakara Çin olarak gösteren (bazen Hong Kong da işe yarar) bir VPN kullanmanız gerekebilir."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Bağış yapın (QR kodu tarayın veya butona basın)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "<a %(a_href)s>QR kod bağış sayfasını</a> açın."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Alipay uygulaması ile QR kodunu tarayın veya Alipay uygulamasını açmak için butona basın."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Lütfen sabırlı olun; içerik Çin'de olduğu için sayfanın yüklenmesi biraz zaman alabilir."

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat talimatları"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>WeChat'te bağış yapın"

msgid "page.donation.payment.wechat.text1"
msgstr "Toplam %(total)s tutarını <a %(a_account)s>bu WeChat hesabını</a> kullanarak bağışlayın."

msgid "page.donation.payment.pix.top_header"
msgstr "Pix talimatları"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Pix'te bağış yap"

msgid "page.donation.payment.pix.text1"
msgstr "Toplam %(total)s tutarını <a %(a_account)s>bu Pix hesabını kullanarak bağışlayın"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Makbuzu bize e-posta yoluyla gönderin"

msgid "page.donation.footer.verification"
msgstr "Kişisel doğrulama adresinize bir makbuz veya ekran görüntüsü gönderin. Bu e-posta adresini PayPal bağışınız için KULLANMAYIN."

msgid "page.donation.footer.text1"
msgstr "Şahsi doğrulama adresinize bir makbuz veya ekran görüntüsünü gönderin:"

msgid "page.donation.footer.crypto_note"
msgstr "Eğer kripto kuru işlem sırasında dalgalandıysa, makbuzun orijinal kuru gösterdiğinden emin olun. Kriptoyu kullanma zahmetine katlanmanızı gerçekten takdir ediyoruz, bize çok yardımcı oluyor!"

msgid "page.donation.footer.text2"
msgstr "Makbuzu e-posta olarak gönderdikten sonra, bu butona tıklayın, bu sayede Anna bizzat inceleyebilir (bu birkaç gün sürebilir):"

msgid "page.donation.footer.button"
msgstr "Evet, makbuzumu e-posta olarak gönderdim"

msgid "page.donation.footer.success"
msgstr "✅ Bağışınız için teşekkürler! Anna şahsen üyeliğinizi birkaç gün içinde etkinleştirecek."

msgid "page.donation.footer.failure"
msgstr "❌ Bir şey yanlış gitti. Lütfen sayfayı yenileyin ve tekrar deneyin."

msgid "page.donation.stepbystep"
msgstr "Adım adım yönerge"

msgid "page.donation.crypto_dont_worry"
msgstr "Adımlardan bazıları kripto cüzdanlarından bahsediyor ancak endişelenmeyin, bunun için kripto hakkında hiçbir şey öğrenmenize gerek yok."

msgid "page.donation.hoodpay.step1"
msgstr "1. E-posta adresinizi girin."

msgid "page.donation.hoodpay.step2"
msgstr "2. Ödeme yöntemini seçin."

msgid "page.donation.hoodpay.step3"
msgstr "3. Ödeme yönteminizi tekrar seçin."

msgid "page.donation.hoodpay.step4"
msgstr "4. “Kendi kendine barındırılan” cüzdanı seçin."

msgid "page.donation.hoodpay.step5"
msgstr "5. \"Sahipliğimi onaylıyorum\" seçeneğine tıklayın."

msgid "page.donation.hoodpay.step6"
msgstr "6. Bir e-posta makbuzu almalısınız. Lütfen bu makbuzu bize gönderin, bağışınızı en kısa sürede onaylayacağız."

msgid "page.donate.wait_new"
msgstr "Lütfen bizimle iletişime geçmeden önce en az <span%(span_hours)s>24 saat</span> bekleyin (ve bu sayfayı yenileyin)."

msgid "page.donate.mistake"
msgstr "Ödeme sırasında bir hata yaptıysanız, geri ödeme yapamayız, ancak bu durumu düzeltmeye çalışacağız."

msgid "page.my_donations.title"
msgstr "Benim bağışlarım"

msgid "page.my_donations.not_shown"
msgstr "Bağışların detayları halka açık olarak gösterilmemektedir."

msgid "page.my_donations.no_donations"
msgstr "Henüz bağış yapılmadı <a %(a_donate)s>İlk bağışımı yap.</a>"

msgid "page.my_donations.make_another"
msgstr "Başka bir bağış yap."

msgid "page.downloaded.title"
msgstr "İndirilen dosyalar"

msgid "page.downloaded.fast_partner_star"
msgstr "Hızlı Dost Sunuculardan indirmeler %(icon)s ile işaretlenmiştir."

msgid "page.downloaded.twice"
msgstr "Hem hızlı hem de yavaş indirmelerle bir dosya indirdiyseniz dosya iki kez görünecektir."

msgid "page.downloaded.fast_download_time"
msgstr "Son 24 saatteki hızlı indirmeler günlük limite dahil edilir."

msgid "page.downloaded.times_utc"
msgstr "Tüm saatler UTC cinsindendir."

msgid "page.downloaded.not_public"
msgstr "İndirilen dosyalar halka açık bir şekilde gösterilmemektedir."

msgid "page.downloaded.no_files"
msgstr "Henüz indirilen dosya yok."

msgid "page.downloaded.last_18_hours"
msgstr "Son 18 saat"

msgid "page.downloaded.earlier"
msgstr "Daha önce"

msgid "page.account.logged_in.title"
msgstr "Hesap"

msgid "page.account.logged_out.title"
msgstr "Giriş yap / Kayıt ol"

msgid "page.account.logged_in.account_id"
msgstr "Hesap Kimliği: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Gösterilen profil: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Gizli anahtar (paylaşmayın!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "göster"

msgid "page.account.logged_in.membership_has_some"
msgstr "Üyelik: <strong>%(tier_name)s</strong> %(until_date)s'a kadar <a %(a_extend)s>(extend)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Üyelik: <strong>Hiçbiri</strong> <a %(a_become)s>(üye ol)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Kullanılan hızlı indirmeler (son 24 saatte): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "hangi indirmeler?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Özel Telegram grubu: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Bize katılın!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Grubumuza katılmak için <a %(a_tier)s>daha yüksek bir seviyeye</a> yükseltin."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Eğer üyeliğinizi daha yüksek bir kademeye yükseltmekle ilgileniyorsanız Anna ile %(email)s adresinde iletişime geçin."

msgid "page.contact.title"
msgstr "İletişim e-postası"

msgid "page.account.logged_in.membership_multiple"
msgstr "Birden fazla üyeliği birleştirebilirsiniz (24 saat içinde hızlı indirmeler birleştirilecektir)."

msgid "layout.index.header.nav.public_profile"
msgstr "Herkese açık profil"

msgid "layout.index.header.nav.downloaded_files"
msgstr "İndirilen dosyalar"

msgid "layout.index.header.nav.my_donations"
msgstr "Bağışlarım"

msgid "page.account.logged_in.logout.button"
msgstr "Çıkış"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Başarıyla çıkış yaptınız. Yeniden giriş yapmak için sayfayı yenileyin."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Bir şeyler yanlış gitti. Lütfen sayfayı yenileyin ve tekrar deneyin."

msgid "page.account.logged_out.registered.text1"
msgstr "Kayıt başarılı! Özel anahtarınız: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Bu anahtarı dikkatlice saklayın. Eğer kaybederseniz, hesabınıza erişimi kaybedersiniz."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Yer işaretlerine ekleyin.</strong>Anahtarınızı hatırlamak için bu sayfayı yer işaretlerine ekleyebilirsiniz.</li><li %(li_item)s><strong>İndir.</strong> Anahtarınızı indirmek için <a %(a_download)s>bu linke</a> tıklayın.</li><li %(li_item)s><strong>Şifre yöneticisi.</strong> Kendi kolaylığınız için, anahtar aşağıda önceden doldurulmuş biçimde bulunur, bunun aracılığıyla giriş yapınca şifre yöneticinize kaydedebilirsiniz.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Giriş yapmak için özel anahtarınızı girin:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Özel anahtar"

msgid "page.account.logged_out.key_form.button"
msgstr "Giriş yap"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Geçersiz özel anahtar. Anahtarınızı doğrulayın ve tekrar deneyin, ya da alternatif olarak aşağıda yeni hesap ile kaydolun."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Anahtarınızı kaybetmeyin!"

msgid "page.account.logged_out.register.header"
msgstr "Henüz hesabınız yok mu?"

msgid "page.account.logged_out.register.button"
msgstr "Yeni hesap oluştur"

msgid "page.login.lost_key"
msgstr "Anahtarınızı kaybettiyseniz, lütfen <a %(a_contact)s>bizimle iletişime geçin</a> ve mümkün olduğunca fazla bilgi sağlayın."

msgid "page.login.lost_key_contact"
msgstr "Bizimle iletişime geçmek için geçici olarak yeni bir hesap oluşturmanız gerekebilir."

msgid "page.account.logged_out.old_email.button"
msgstr "Eski e-posta tabanlı hesap mı? E-postanı %(a_open)s>buraya gir</a>."

msgid "page.list.title"
msgstr "Sırala"

msgid "page.list.header.edit.link"
msgstr "düzenle"

msgid "page.list.edit.button"
msgstr "Kaydet"

msgid "page.list.edit.success"
msgstr "✅ Kaydedildi. Lütfen sayfayı yenileyin."

msgid "page.list.edit.failure"
msgstr "❌ Bir şeyler yanlış gitti. Lütfen tekrar deneyin."

msgid "page.list.by_and_date"
msgstr "Listeleme ölçütü %(by)s, <span %(span_time)s>%(time)s</span> tarihinde oluşturuldu"

msgid "page.list.empty"
msgstr "Liste boş."

msgid "page.list.new_item"
msgstr "Bir dosya bularak ve \"Listeler\" sekmesini açarak bu listeye ekle veya bu listeden sil."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil bulunamadı."

msgid "page.profile.header.edit"
msgstr "düzenle"

msgid "page.profile.change_display_name.text"
msgstr "Görünen adınızı değiştirin. Kimliğiniz ( \"#\" işaretinden sonraki kısım) değiştirilemez."

msgid "page.profile.change_display_name.button"
msgstr "Kaydet"

msgid "page.profile.change_display_name.success"
msgstr "✅ Kaydedildi. Lütfen sayfayı yenileyin."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Bir şeyler yanlış gitti. Lütfen tekrar deneyin."

msgid "page.profile.created_time"
msgstr "Profil <span %(span_time)s>%(time)s</span> önce yaratıldı"

msgid "page.profile.lists.header"
msgstr "Listeler"

msgid "page.profile.lists.no_lists"
msgstr "Henüz liste yok"

msgid "page.profile.lists.new_list"
msgstr "Bir dosya bularak ve “Listeler” sekmesini açarak yeni bir liste oluştur."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Telif hakkı reformu ulusal güvenlik için gereklidir."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Özet: Çin LLM'leri (DeepSeek dahil) dünyanın en büyük yasadışı kitap ve makale arşivim üzerinde eğitildi. Batı, ulusal güvenlik meselesi olarak telif hakkı yasasını yeniden düzenlemelidir."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "TorrentFreak'in eşlik eden makaleleri: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Çok uzun zaman önce değil, \"gölge kütüphaneler\" yok oluyordu. Akademik makalelerin devasa yasadışı arşivi Sci-Hub, davalar nedeniyle yeni eserler kabul etmeyi bırakmıştı. Kitapların en büyük yasadışı kütüphanesi \"Z-Library\", iddia edilen yaratıcılarının telif hakkı ihlalleri nedeniyle tutuklanmasıyla karşı karşıya kaldı. İnanılmaz bir şekilde tutuklanmaktan kaçmayı başardılar, ancak kütüphaneleri tehdit altında olmaya devam ediyor."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Z-Library kapanma tehlikesiyle karşı karşıya kaldığında, ben zaten tüm kütüphanesini yedeklemiştim ve barındıracak bir platform arıyordum. Bu, Anna’nın Arşivi’ni başlatma motivasyonumdu: önceki girişimlerin arkasındaki misyonun devamı. O zamandan beri, kitaplar, akademik makaleler, dergiler, gazeteler ve daha fazlasını içeren çeşitli formatlarda 140 milyondan fazla telifli metni barındırarak dünyanın en büyük gölge kütüphanesi haline geldik."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Ekibim ve ben ideologlarız. Bu dosyaları korumanın ve barındırmanın ahlaki olarak doğru olduğuna inanıyoruz. Dünyadaki kütüphaneler bütçe kesintileriyle karşı karşıya ve insanlığın mirasını şirketlere de emanet edemeyiz."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Sonra yapay zeka geldi. Neredeyse tüm büyük LLM şirketleri, verilerimiz üzerinde eğitim almak için bizimle iletişime geçti. Çoğu (ama hepsi değil!) ABD merkezli şirketler, işimizin yasadışı doğasını fark ettiklerinde yeniden düşündüler. Buna karşılık, Çinli firmalar koleksiyonumuzu coşkuyla benimsedi, yasallığı konusunda görünüşte endişelenmediler. Bu, Çin'in neredeyse tüm büyük uluslararası telif hakkı anlaşmalarına imza atan bir ülke olması göz önüne alındığında dikkat çekicidir."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Yaklaşık 30 şirkete yüksek hızlı erişim sağladık. Çoğu LLM şirketi ve bazıları da koleksiyonumuzu yeniden satacak olan veri brokerları. Çoğu Çinli, ancak ABD, Avrupa, Rusya, Güney Kore ve Japonya'dan şirketlerle de çalıştık. DeepSeek, önceki bir versiyonunun koleksiyonumuzun bir kısmı üzerinde eğitildiğini <a %(arxiv)s>kabul etti</a>, ancak en son modelleri hakkında ketumlar (muhtemelen yine verilerimiz üzerinde eğitildiler)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Batı, LLM yarışında ve nihayetinde AGI'de önde kalmak istiyorsa, telif hakkı konusundaki duruşunu yeniden gözden geçirmeli ve bunu yakında yapmalıdır. Ahlaki duruşumuz konusunda bizimle aynı fikirde olsanız da olmasanız da, bu artık bir ekonomi ve hatta ulusal güvenlik meselesi haline geliyor. Tüm güç blokları yapay süper bilim insanları, süper hackerlar ve süper ordular inşa ediyor. Bilgi özgürlüğü, bu ülkeler için bir hayatta kalma meselesi haline geliyor — hatta ulusal güvenlik meselesi."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Ekibimiz dünyanın dört bir yanından geliyor ve belirli bir hizipimiz yok. Ancak güçlü telif hakkı yasalarına sahip ülkeleri, bu varoluşsal tehdidi reform yapmak için kullanmaya teşvik ederiz. Peki ne yapmalı?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "İlk önerimiz basit: telif hakkı süresini kısaltın. ABD'de, telif hakkı yazarın ölümünden sonra 70 yıl boyunca verilir. Bu saçma. Bunu, başvurudan sonra 20 yıl boyunca verilen patentlerle uyumlu hale getirebiliriz. Bu, kitap, makale, müzik, sanat ve diğer yaratıcı eserlerin yazarlarının çabaları için tam olarak tazmin edilmesi için fazlasıyla yeterli olmalıdır (film uyarlamaları gibi uzun vadeli projeler dahil)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Daha sonra, en azından, politika yapıcılar metinlerin kitlesel korunması ve yayılması için istisnalar eklemelidir. Bireysel müşterilerden kaybedilen gelir ana endişe ise, kişisel düzeyde dağıtım yasaklı kalabilir. Buna karşılık, geniş depoları yönetebilenler — LLM'leri eğiten şirketler, kütüphaneler ve diğer arşivler ile birlikte — bu istisnalarla kapsanacaktır."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Bazı ülkeler bunun bir versiyonunu zaten yapıyor. TorrentFreak, Çin ve Japonya'nın telif hakkı yasalarına AI istisnaları eklediğini <a %(torrentfreak)s>bildirdi</a>. Bunun uluslararası anlaşmalarla nasıl etkileşime girdiği belirsiz, ancak kesinlikle yerli şirketlerine koruma sağlıyor, bu da gördüklerimizi açıklıyor."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Anna’nın Arşivi'ne gelince — ahlaki inançla kök salmış yeraltı çalışmalarımıza devam edeceğiz. Ancak en büyük dileğimiz, ışığa çıkmak ve etkilerimizi yasal olarak artırmaktır. Lütfen telif hakkını reforme edin."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "TorrentFreak'in eşlik eden makalelerini okuyun: <a %(torrentfreak)s>birinci</a>, <a %(torrentfreak_2)s>ikinci</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "10.000 $ ISBN görselleştirme ödülü kazananları"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Özet: 10.000 $ ISBN görselleştirme ödülüne inanılmaz başvurular aldık."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Birkaç ay önce, ISBN alanını gösteren verilerimizin en iyi görselleştirmesini yapmak için <a %(all_isbns)s>10.000 $ ödül</a> duyurduk. Hangi dosyaları arşivleyip arşivlemediğimizi göstermeye vurgu yaptık ve daha sonra kaç kütüphanenin ISBN'leri tuttuğunu (nadirlik ölçüsü) açıklayan bir veri seti ekledik."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Tepkiler karşısında şaşkına döndük. Çok fazla yaratıcılık vardı. Katılan herkese büyük teşekkürler: enerjiniz ve coşkunuz bulaşıcı!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Sonunda şu soruları yanıtlamak istedik: <strong>dünyada hangi kitaplar var, hangilerini zaten arşivledik ve hangi kitaplara odaklanmalıyız?</strong> Bu sorularla ilgilenen bu kadar çok insan görmek harika."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Kendimiz temel bir görselleştirme ile başladık. 300kb'den daha az bir boyutta, bu resim insanlık tarihindeki en büyük tamamen açık \"kitap listesi\"ni özlü bir şekilde temsil ediyor:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tüm ISBN'ler"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Anna’nın Arşivi'ndeki Dosyalar"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO'lar"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC veri sızıntısı"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID'ler"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’un eKitap İndeksi"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Kitaplar"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "İnternet Arşivi"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Küresel Yayıncılar Kaydı"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Rusya Devlet Kütüphanesi"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Trantor İmparatorluk Kütüphanesi"

#, fuzzy
msgid "common.back"
msgstr "Geri"

#, fuzzy
msgid "common.forward"
msgstr "İleri"

#, fuzzy
msgid "common.last"
msgstr "Son"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Daha fazla bilgi için <a %(all_isbns)s>orijinal blog gönderisine</a> bakın."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Bunu geliştirmek için bir meydan okuma başlattık. Birinci yer için 6.000 $, ikinci yer için 3.000 $ ve üçüncü yer için 1.000 $ ödül verecektik. Gelen yoğun yanıt ve inanılmaz gönderimler nedeniyle ödül havuzunu biraz artırmaya ve dört kişiye üçüncülük ödülü olarak 500 $ vermeye karar verdik. Kazananlar aşağıda, ancak tüm gönderimlere <a %(annas_archive)s>buradan</a> göz atmayı veya <a %(a_2025_01_isbn_visualization_files)s>birleştirilmiş torrentimizi</a> indirmeyi unutmayın."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Birinci yer 6.000 $: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Bu <a %(phiresky_github)s>gönderim</a> (<a %(annas_archive_note_2951)s>Gitlab yorumu</a>) tam olarak istediğimiz her şey ve daha fazlası! Özellikle inanılmaz esnek görselleştirme seçeneklerini (özel gölgelendiricileri bile destekleyen) ve kapsamlı ön ayar listesini çok beğendik. Ayrıca her şeyin ne kadar hızlı ve sorunsuz olduğunu, basit uygulamayı (arka ucu bile yok), akıllı mini haritayı ve <a %(phiresky_github)s>blog gönderilerinde</a> yer alan kapsamlı açıklamayı beğendik. İnanılmaz bir çalışma ve hak edilmiş bir birincilik!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "İkinci yer 3.000 $: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Başka bir inanılmaz <a %(annas_archive_note_2913)s>gönderim</a>. Birinci yer kadar esnek değil, ancak aslında makro düzeydeki görselleştirmesini birinci yerden daha çok beğendik (alan doldurma eğrisi, sınırlar, etiketleme, vurgulama, kaydırma ve yakınlaştırma). Joe Davis'in bir <a %(annas_archive_note_2971)s>yorumu</a> bizimle yankılandı:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Mükemmel kareler ve dikdörtgenler matematiksel olarak hoş olsa da, haritalama bağlamında üstün bir yerellik sağlamazlar. Bu Hilbert veya klasik Morton'da bulunan asimetrinin bir kusur değil, bir özellik olduğuna inanıyorum. İtalya'nın ünlü çizme şeklindeki silueti gibi, bu eğrilerin benzersiz \"tuhaflıkları\" bilişsel işaretler olarak hizmet edebilir. Bu ayırt edicilik, mekansal hafızayı geliştirebilir ve kullanıcıların kendilerini yönlendirmelerine yardımcı olabilir, bu da belirli bölgeleri bulmayı veya kalıpları fark etmeyi kolaylaştırabilir.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Ve hala görselleştirme ve render için birçok seçenek, ayrıca inanılmaz derecede sorunsuz ve sezgisel bir kullanıcı arayüzü. Sağlam bir ikincilik!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Üçüncü yer 500 $ #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Bu <a %(annas_archive_note_2940)s>gönderimde</a> özellikle karşılaştırma ve yayıncı görünümleri olmak üzere farklı türdeki görünümleri çok beğendik."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Üçüncü yer 500 $ #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "En cilalı kullanıcı arayüzü olmasa da, bu <a %(annas_archive_note_2917)s>gönderim</a> birçok kutuyu işaretliyor. Özellikle karşılaştırma özelliğini beğendik."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Üçüncü yer 500 $ #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Birinci yer gibi, bu <a %(annas_archive_note_2975)s>gönderim</a> esnekliği ile bizi etkiledi. Sonuçta bu, harika bir görselleştirme aracını oluşturan şeydir: güç kullanıcıları için maksimum esneklik sağlarken, ortalama kullanıcılar için işleri basit tutmak."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Üçüncü yer 500 $ #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Ödül alan son <a %(annas_archive_note_2947)s>gönderim</a> oldukça basit, ancak gerçekten beğendiğimiz bazı benzersiz özelliklere sahip. Belirli bir ISBN'yi kapsayan kaç dataset olduğunu popülerlik/güvenilirlik ölçüsü olarak göstermelerini beğendik. Ayrıca karşılaştırmalar için opaklık kaydırıcısını kullanmanın basit ama etkili olmasını çok beğendik."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Dikkate değer fikirler"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Özellikle beğendiğimiz bazı daha fazla fikir ve uygulamalar:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Nadirlik için gökdelenler"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Canlı istatistikler"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Açıklamalar ve ayrıca canlı istatistikler"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Benzersiz harita görünümü ve filtreler"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Havalı varsayılan renk şeması ve ısı haritası."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Hızlı karşılaştırmalar için veri kümelerini kolayca değiştirme."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Güzel etiketler."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Kitap sayısı ile ölçek çubuğu."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Veri kümelerini karşılaştırmak için DJ gibi birçok kaydırıcı."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Bir süre daha devam edebilirdik, ama burada duralım. Tüm gönderimleri <a %(annas_archive)s>buradan</a> inceleyin veya <a %(a_2025_01_isbn_visualization_files)s>birleştirilmiş torrentimizi</a> indirin. O kadar çok gönderim var ki, her biri UI veya uygulama açısından benzersiz bir bakış açısı sunuyor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "En azından birinci olan gönderimi ana web sitemize entegre edeceğiz ve belki bazı diğerlerini de. Ayrıca en nadir kitapları tanımlama, doğrulama ve ardından arşivleme sürecini nasıl organize edeceğimiz üzerine düşünmeye başladık. Bu konuda daha fazla bilgi gelecek."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Katılan herkese teşekkürler. Bu kadar çok insanın ilgilenmesi harika."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Kalplerimiz minnettarlıkla dolu."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Tüm ISBN'leri Görselleştirme — 2025-01-31 tarihine kadar $10,000 ödül"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Bu resim, insanlık tarihindeki en büyük tamamen açık \"kitap listesi\"ni temsil ediyor."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Bu resim 1000×800 piksel. Her piksel 2,500 ISBN'yi temsil ediyor. Bir ISBN için dosyamız varsa, o pikseli daha yeşil yapıyoruz. Bir ISBN'nin verildiğini biliyorsak, ancak eşleşen bir dosyamız yoksa, onu daha kırmızı yapıyoruz."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "300kb'den daha az bir boyutta, bu resim insanlık tarihindeki en büyük tamamen açık \"kitap listesi\"ni özlü bir şekilde temsil ediyor (tam sıkıştırılmış hali birkaç yüz GB)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ayrıca gösteriyor ki: kitapları yedekleme konusunda yapılacak çok iş var (sadece 16% var)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Arka plan"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Anna’nın Arşivi, insanlığın tüm bilgisini yedekleme misyonunu, hangi kitapların hala mevcut olduğunu bilmeden nasıl gerçekleştirebilir? Bir YAPILACAKLAR listesine ihtiyacımız var. Bunu haritalamanın bir yolu, 1970'lerden bu yana (çoğu ülkede) yayımlanan her kitaba atanan ISBN numaralarıdır."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Tüm ISBN atamalarını bilen merkezi bir otorite yoktur. Bunun yerine, ülkelerin numara aralıkları aldığı, ardından büyük yayınevlerine daha küçük aralıklar atadığı, bu yayınevlerinin de daha küçük yayınevlerine aralıkları alt bölümlere ayırabileceği dağıtılmış bir sistemdir. Son olarak, bireysel numaralar kitaplara atanır."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "ISBN'leri <a %(blog)s>iki yıl önce</a> ISBNdb'yi tarayarak haritalamaya başladık. O zamandan beri, <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ve daha fazlası gibi birçok metadata kaynağını taradık. Tam liste, Anna’nın Arşivi'ndeki “Datasets” ve “Torrents” sayfalarında bulunabilir. Şu anda, dünyadaki en büyük tamamen açık, kolayca indirilebilir kitap metadata (ve dolayısıyla ISBN) koleksiyonuna sahibiz."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Koruma neden önemli ve neden şu anda kritik bir dönemde olduğumuz hakkında <a %(blog)s>geniş çapta yazılar yazdık</a>. Şimdi nadir, odaklanılmamış ve benzersiz şekilde risk altındaki kitapları tanımlayıp korumalıyız. Dünyadaki tüm kitaplar hakkında iyi metadata'ya sahip olmak bu konuda yardımcı olur."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Görselleştirme"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Genel görünüm resminin yanı sıra, edindiğimiz bireysel datasetlere de bakabiliriz. Aralarında geçiş yapmak için açılır menüyü ve düğmeleri kullanın."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Bu resimlerde görülecek birçok ilginç desen var. Neden farklı ölçeklerde meydana gelen bazı düzenli çizgi ve bloklar var? Boş alanlar nedir? Neden belirli datasetler bu kadar kümelenmiş? Bu soruları okuyucuya bir alıştırma olarak bırakacağız."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "10.000 $ ödül"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Burada keşfedilecek çok şey var, bu yüzden yukarıdaki görselleştirmeyi geliştirmek için bir ödül duyuruyoruz. Çoğu ödülümüzden farklı olarak, bu ödül zaman sınırlıdır. Açık kaynak kodunuzu 2025-01-31 (23:59 UTC) tarihine kadar <a %(annas_archive)s>göndermeniz</a> gerekiyor."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "En iyi gönderi 6.000 $, ikinci yer 3.000 $ ve üçüncü yer 1.000 $ alacak. Tüm ödüller Monero (XMR) kullanılarak verilecektir."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Aşağıda minimum kriterler bulunmaktadır. Hiçbir gönderi kriterleri karşılamazsa, yine de bazı ödüller verebiliriz, ancak bu bizim takdirimize bağlı olacaktır."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Bu depo'yu çatallayın ve bu blog gönderisi HTML'sini düzenleyin (Flask backend'imiz dışında başka backend'lere izin verilmez)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Yukarıdaki resmi, bireysel ISBN'lere kadar yakınlaştırılabilir hale getirin. ISBN'lere tıklamak, Anna’nın Arşivi'nde bir metadata sayfasına veya aramaya götürmelidir."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Tüm farklı datasetler arasında geçiş yapabilmelisiniz."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Ülke aralıkları ve yayınevi aralıkları üzerine gelindiğinde vurgulanmalıdır. Örneğin, ülke bilgisi için <a %(github_xlcnd_isbnlib)s>isbnlib'deki data4info.py</a> ve yayınevleri için “isbngrp” taramamızı kullanabilirsiniz (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Masaüstü ve mobilde iyi çalışmalıdır."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Ekstra puanlar için (bunlar sadece fikirlerdir — yaratıcılığınızı serbest bırakın):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Kullanılabilirlik ve görünüm açısından güçlü bir değerlendirme yapılacaktır."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Yakınlaştırırken bireysel ISBN'ler için başlık ve yazar gibi gerçek metadata gösterin."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Daha iyi bir alan doldurma eğrisi. Örneğin, ilk satırda 0'dan 4'e ve ardından ikinci satırda (tersine) 5'ten 9'a giden bir zikzak — özyinelemeli olarak uygulanmış."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Farklı veya özelleştirilebilir renk şemaları."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Datasets karşılaştırmak için özel görünümler."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Diğer metadata ile iyi uyuşmayan sorunları (örneğin, çok farklı başlıklar) ayıklama yolları."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "ISBN'ler veya aralıklar hakkında yorumlarla görüntüleri açıklama."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Nadir veya risk altındaki kitapları tanımlamak için herhangi bir yöntem."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Aklınıza gelebilecek yaratıcı fikirler!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Minimum kriterlerden tamamen sapabilir ve tamamen farklı bir görselleştirme yapabilirsiniz. Eğer gerçekten muhteşemse, bu ödül için nitelikli olur, ancak bizim takdirimize bağlıdır."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "<a %(annas_archive)s>Bu soruna</a> bir yorum göndererek, çatalladığınız depo, birleştirme isteği veya fark bağlantısı ile gönderim yapın."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Bu görüntüleri oluşturmak için kullanılan kod ve diğer örnekler <a %(annas_archive)s>bu dizinde</a> bulunabilir."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Tüm gerekli ISBN bilgilerini içeren, yaklaşık 75MB (sıkıştırılmış) olan kompakt bir veri formatı geliştirdik. Veri formatının açıklaması ve onu oluşturmak için kullanılan kod <a %(annas_archive_l1244_1319)s>burada</a> bulunabilir. Ödül için bunu kullanmanız gerekmiyor, ancak başlamak için muhtemelen en uygun format. Metadata'mızı istediğiniz gibi dönüştürebilirsiniz (ancak tüm kodunuz açık kaynak olmalıdır)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Neler ortaya koyacağınızı görmek için sabırsızlanıyoruz. İyi şanslar!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna’nın Arşivi Konteynerleri (AAC): Dünyanın en büyük gölge kütüphanesinden sürümleri standartlaştırma"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’nın Arşivi, dünyanın en büyük gölge kütüphanesi haline geldi ve sürümlerimizi standartlaştırmamız gerekiyor."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>, açık ara dünyanın en büyük gölge kütüphanesi haline geldi ve ölçeğinde tamamen açık kaynak ve açık veri olan tek gölge kütüphane. Aşağıda, Datasets sayfamızdan (biraz değiştirilmiş) bir tablo bulunmaktadır:"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Bunu üç şekilde başardık:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Mevcut açık veri gölge kütüphanelerini yansıtma (Sci-Hub ve Library Genesis gibi)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Daha açık olmak isteyen ancak bunu yapacak zaman veya kaynakları olmayan gölge kütüphanelere yardım etme (Libgen çizgi roman koleksiyonu gibi)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Toplu paylaşımda bulunmak istemeyen kütüphaneleri kazıma (Z-Library gibi)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "(2) ve (3) için artık kendimiz oldukça büyük bir torrent koleksiyonunu yönetiyoruz (yüzlerce TB). Şimdiye kadar bu koleksiyonlara tek seferlik işler olarak yaklaştık, yani her koleksiyon için özel altyapı ve veri organizasyonu. Bu, her sürüme önemli bir ek yük getiriyor ve daha kademeli sürümler yapmayı özellikle zorlaştırıyor."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Bu yüzden sürümlerimizi standartlaştırmaya karar verdik. Bu, standartımızı tanıttığımız teknik bir blog yazısıdır: <strong>Anna’nın Arşiv Konteynerleri</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Tasarım hedefleri"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Birincil kullanım amacımız, farklı mevcut koleksiyonlardan dosyaların ve ilişkili metadata'nın dağıtımıdır. En önemli dikkate aldığımız hususlar:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Orijinal formata mümkün olduğunca yakın heterojen dosyalar ve metadata."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Kaynak kütüphanelerdeki heterojen tanımlayıcılar veya tanımlayıcıların eksikliği."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Metadata ile dosya verilerinin ayrı sürümleri veya yalnızca metadata sürümleri (örneğin, ISBNdb sürümümüz)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Torrentler aracılığıyla dağıtım, ancak diğer dağıtım yöntemlerinin de mümkün olması (örneğin, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Değişmez kayıtlar, çünkü torrentlerimizin sonsuza kadar yaşayacağını varsaymalıyız."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Artımlı sürümler / eklenebilir sürümler."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Makine tarafından okunabilir ve yazılabilir, özellikle yığınımız için (Python, MySQL, ElasticSearch, Transmission, Debian, ext4) uygun ve hızlı bir şekilde."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "İnsan tarafından bir dereceye kadar kolay incelenebilir, ancak bu, makine tarafından okunabilirliğe göre ikincildir."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Koleksiyonlarımızı standart kiralık bir seedbox ile kolayca tohumlamak."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "İkili veriler, Nginx gibi web sunucuları tarafından doğrudan sunulabilir."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Bazı hedefler:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Dosyaların diskte manuel olarak kolayca gezilebilir veya ön işleme olmadan aranabilir olmasını umursamıyoruz."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Mevcut kütüphane yazılımlarıyla doğrudan uyumlu olmayı umursamıyoruz."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Koleksiyonumuzu torrentler kullanarak tohumlamanın herkes için kolay olması gerekirken, dosyaların önemli teknik bilgi ve bağlılık olmadan kullanılabilir olmasını beklemiyoruz."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Anna’nın Arşivi açık kaynak olduğundan, formatımızı doğrudan kullanmak istiyoruz. Arama dizinimizi yenilediğimizde, yalnızca herkese açık yolları erişiyoruz, böylece kütüphanemizi çatallayan herkes hızlıca çalışmaya başlayabilir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standart"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Sonunda, nispeten basit bir standart üzerinde karar kıldık. Oldukça gevşek, normatif olmayan ve devam eden bir çalışmadır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’nın Arşiv Konteyneri), <strong>metadata</strong> ve isteğe bağlı olarak <strong>ikili veri</strong> içeren tek bir öğedir ve her ikisi de değiştirilemez. Küresel olarak benzersiz bir tanımlayıcıya sahiptir, buna <strong>AACID</strong> denir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Koleksiyon.</strong> Her AAC bir koleksiyona aittir, bu tanım gereği anlamsal olarak tutarlı AAC'lerin bir listesidir. Bu, metadata formatında önemli bir değişiklik yaparsanız, yeni bir koleksiyon oluşturmanız gerektiği anlamına gelir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“kayıtlar” ve “dosyalar” koleksiyonları.</strong> Geleneksel olarak, “kayıtlar” ve “dosyalar”ı farklı koleksiyonlar olarak yayınlamak genellikle uygundur, böylece farklı zamanlamalarla yayınlanabilirler, örneğin kazıma oranlarına göre. Bir “kayıt”, kitap başlıkları, yazarlar, ISBN'ler gibi bilgileri içeren yalnızca metadata koleksiyonudur, “dosyalar” ise gerçek dosyaların kendilerini (pdf, epub) içeren koleksiyonlardır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> AACID formatı şu şekildedir: <code style=\"color: #0093ff\">aacid__{koleksiyon}__{ISO 8601 zaman damgası}__{koleksiyon-özel ID}__{shortuuid}</code>. Örneğin, yayınladığımız gerçek bir AACID <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code> şeklindedir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{koleksiyon}</code>: ASCII harfleri, sayılar ve alt çizgiler içerebilen (ancak çift alt çizgi içermeyen) koleksiyon adı."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 zaman damgası}</code>: her zaman UTC'de olan ISO 8601'in kısa bir versiyonu, örneğin <code>20220723T194746Z</code>. Bu sayı her yayın için monotonik olarak artmalıdır, ancak tam anlamı koleksiyona göre farklılık gösterebilir. Kazıma veya ID oluşturma zamanını kullanmanızı öneririz."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{koleksiyon-özel ID}</code>: varsa, örneğin Z-Library ID'si gibi koleksiyon-özel bir tanımlayıcı. Atlanabilir veya kısaltılabilir. AACID'nin 150 karakteri aşması durumunda atlanmalı veya kısaltılmalıdır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: bir UUID ancak ASCII'ye sıkıştırılmış, örneğin base57 kullanılarak. Şu anda <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python kütüphanesini kullanıyoruz."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID aralığı.</strong> AACID'ler monotonik olarak artan zaman damgaları içerdiğinden, belirli bir koleksiyon içinde aralıkları belirtmek için bunu kullanabiliriz. Bu formatı kullanıyoruz: <code style=\"color: blue\">aacid__{koleksiyon}__{from_timestamp}--{to_timestamp}</code>, burada zaman damgaları dahil. Bu, ISO 8601 notasyonu ile tutarlıdır. Aralıklar süreklidir ve çakışabilir, ancak çakışma durumunda, o koleksiyonda daha önce yayınlananla aynı kayıtları içermelidir (çünkü AAC'ler değiştirilemez). Eksik kayıtlar kabul edilmez."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata dosyası.</strong> Bir metadata dosyası, belirli bir koleksiyon için bir AAC aralığının metadata'sını içerir. Bunlar şu özelliklere sahiptir:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Dosya adı bir AACID aralığı olmalı, <code style=\"color: red\">annas_archive_meta__</code> ile başlamalı ve <code>.jsonl.zstd</code> ile bitmelidir. Örneğin, yayınlarımızdan biri<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> olarak adlandırılmıştır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Dosya uzantısının belirttiği gibi, dosya türü <a %(jsonlines)s>JSON Lines</a> ve <a %(zstd)s>Zstandard</a> ile sıkıştırılmıştır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Her JSON nesnesi üst düzeyde şu alanları içermelidir: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (isteğe bağlı). Başka alanlara izin verilmez."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code>, koleksiyonun anlamına göre keyfi metadata'dır. Koleksiyon içinde anlamsal olarak tutarlı olmalıdır."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> isteğe bağlıdır ve karşılık gelen ikili verileri içeren ikili veri klasörünün adıdır. O klasördeki karşılık gelen ikili verinin dosya adı kaydın AACID'sidir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code> öneki, kurumunuzun adına uyarlanabilir, örneğin <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>İkili veri klasörü.</strong> Belirli bir koleksiyon için bir AAC aralığının ikili verilerini içeren bir klasör. Bunlar şu özelliklere sahiptir:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Dizin adı bir AACID aralığı olmalı, <code style=\"color: green\">annas_archive_data__</code> ile başlamalı ve ek olmamalıdır. Örneğin, gerçek yayınlarımızdan biri<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> adlı bir dizine sahiptir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Dizin, belirtilen aralıktaki tüm AAC'ler için veri dosyalarını içermelidir. Her veri dosyası, dosya adı olarak AACID'sine sahip olmalıdır (ek yok)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Bu klasörlerin boyutunu yönetilebilir tutmak önerilir, örneğin her biri 100GB-1TB'den büyük olmamalıdır, ancak bu öneri zamanla değişebilir."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Metadata dosyaları ve ikili veri klasörleri torrentlerde paketlenebilir, her metadata dosyası için bir torrent veya her ikili veri klasörü için bir torrent olacak şekilde. Torrentler, orijinal dosya/dizin adı artı <code>.torrent</code> uzantısını dosya adı olarak içermelidir."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Örnek"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Son Z-Library yayınımıza bir örnek olarak bakalım. İki koleksiyondan oluşuyor: “<span style=\"background: #fffaa3\">zlib3_records</span>” ve “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Bu, metadata kayıtlarını gerçek kitap dosyalarından ayrı olarak kazımamıza ve yayınlamamıza olanak tanır. Bu nedenle, metadata dosyalarıyla iki torrent yayınladık:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Ayrıca, “<span style=\"background: #ffd6fe\">zlib3_files</span>” koleksiyonu için toplamda 62 adet ikili veri klasörü içeren bir dizi torrent yayınladık:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "<code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> çalıştırarak içindekileri görebiliriz:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Bu durumda, Z-Library tarafından bildirilen bir kitabın metadata'sı. Üst düzeyde sadece “aacid” ve “metadata” var, ancak “data_folder” yok, çünkü karşılık gelen ikili veri yok. AACID, birincil kimlik olarak “22430000” içeriyor ve bunun “zlibrary_id”den alındığını görebiliriz. Bu koleksiyondaki diğer AAC'lerin de aynı yapıya sahip olmasını bekleyebiliriz."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Şimdi <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> çalıştıralım:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Bu, çok daha küçük bir AAC metadata'sı, ancak bu AAC'nin büyük kısmı başka bir yerde bir ikili dosyada bulunuyor! Sonuçta, bu sefer bir “data_folder” var, bu yüzden karşılık gelen ikili verinin <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> konumunda bulunmasını bekleyebiliriz. “Metadata”, “zlibrary_id” içerdiğinden, bunu “zlib_records” koleksiyonundaki karşılık gelen AAC ile kolayca ilişkilendirebiliriz. Bunu farklı şekillerde de ilişkilendirebilirdik, örneğin AACID aracılığıyla — standart bunu zorunlu kılmaz."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "“Metadata” alanının kendisinin JSON olması da gerekli değildir. XML veya başka bir veri formatı içeren bir dize olabilir. Hatta, eğer çok fazla veri varsa, metadata bilgisini ilişkili ikili blobda bile saklayabilirsiniz."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Sonuç"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Bu standartla, yayınları daha kademeli olarak yapabilir ve yeni veri kaynaklarını daha kolay ekleyebiliriz. Zaten birkaç heyecan verici yayınımız sırada!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Diğer gölge kütüphanelerin koleksiyonlarımızı yansıtmasının daha kolay hale gelmesini de umuyoruz. Sonuçta, amacımız insan bilgisini ve kültürünü sonsuza dek korumak, bu yüzden ne kadar çok yedekleme olursa o kadar iyi."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna’nın Güncellemesi: tamamen açık kaynak arşiv, ElasticSearch, 300GB+ kitap kapakları"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Anna’nın Arşivi ile iyi bir alternatif sunmak için gece gündüz çalışıyoruz. İşte son zamanlarda başardığımız bazı şeyler."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Z-Library'nin kapanması ve (iddia edilen) kurucularının tutuklanmasıyla, Anna’nın Arşivi ile iyi bir alternatif sunmak için gece gündüz çalışıyoruz (burada bağlantı vermeyeceğiz, ancak Google'da arayabilirsiniz). İşte son zamanlarda başardığımız bazı şeyler."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna’nın Arşivi tamamen açık kaynak"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Bilginin özgür olması gerektiğine inanıyoruz ve kendi kodumuz da bir istisna değil. Tüm kodumuzu özel olarak barındırılan Gitlab örneğimizde yayınladık: <a %(annas_archive)s>Anna’nın Yazılımı</a>. Çalışmamızı organize etmek için sorun izleyicisini de kullanıyoruz. Geliştirmemizle ilgilenmek istiyorsanız, başlamak için harika bir yer."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Üzerinde çalıştığımız şeylerin bir tadına varmanız için, istemci tarafı performans iyileştirmeleri üzerindeki son çalışmamıza bir göz atın. Henüz sayfalama uygulamadığımız için, genellikle 100-200 sonuç içeren çok uzun arama sayfaları döndürürdük. Arama sonuçlarını çok erken kesmek istemedik, ancak bu bazı cihazları yavaşlatacağı anlamına geliyordu. Bunun için küçük bir numara uyguladık: Çoğu arama sonucunu HTML yorumlarına (<code><!-- --></code>) sardık ve ardından bir sonucun görünür olması gerektiğini tespit eden küçük bir Javascript yazdık, bu anda yorumu açtık:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"sanallaştırma\" 23 satırda uygulandı, süslü kütüphanelere gerek yok! Bu, sınırlı zamanınız olduğunda ve çözülmesi gereken gerçek sorunlar olduğunda ortaya çıkan hızlı ve pragmatik bir koddur. Aramamızın artık yavaş cihazlarda iyi çalıştığı bildirildi!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Bir diğer büyük çaba, veritabanını otomatik olarak oluşturmaktı. Başladığımızda, farklı kaynakları rastgele bir araya getirdik. Şimdi onları güncel tutmak istiyoruz, bu yüzden iki Library Genesis çatalından yeni metadata indirmek ve entegre etmek için bir dizi betik yazdık. Amacımız sadece arşivimiz için faydalı hale getirmek değil, gölge kütüphane metadata'sı ile oynamak isteyen herkes için işleri kolaylaştırmak. Hedef, her türlü ilginç metadata'nın mevcut olduğu bir Jupyter defteri oluşturmak, böylece <a %(blog)s>ISBN'lerin hangi yüzdesinin sonsuza kadar korunduğunu</a> gibi daha fazla araştırma yapabiliriz."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Son olarak, bağış sistemimizi yeniledik. Artık kredi kartı kullanarak doğrudan kripto cüzdanlarımıza para yatırabilirsiniz, kripto paralar hakkında gerçekten bir şey bilmenize gerek kalmadan. Bunun pratikte ne kadar iyi çalıştığını izlemeye devam edeceğiz, ama bu büyük bir adım."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "ElasticSearch'e Geçiş"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "<a %(annas_archive)s>Biletlerimizden</a> biri, arama sistemimizle ilgili bir dizi sorunu içeriyordu. Zaten tüm verilerimiz MySQL'de olduğu için MySQL tam metin aramasını kullandık. Ancak bunun sınırları vardı:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Bazı sorgular çok uzun sürdü, açık bağlantıların tamamını kaplayacak kadar."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Varsayılan olarak MySQL, minimum kelime uzunluğuna sahiptir veya dizininiz gerçekten büyük olabilir. İnsanlar \"Ben Hur\" araması yapamadıklarını bildirdi."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Arama, yalnızca tamamen belleğe yüklendiğinde biraz hızlıydı, bu da bunu çalıştırmak için daha pahalı bir makine almamızı ve başlangıçta dizini önceden yüklemek için bazı komutlar kullanmamızı gerektirdi."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Yeni özellikler oluşturmak için kolayca genişletemezdik, örneğin daha iyi <a %(wikipedia_cjk_characters)s>boşluksuz diller için ayrıştırma</a>, filtreleme/faseting, sıralama, \"bunu mu demek istediniz\" önerileri, otomatik tamamlama ve benzeri."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Bir dizi uzmanla konuştuktan sonra ElasticSearch'te karar kıldık. Mükemmel olmadı (varsayılan \"bunu mu demek istediniz\" önerileri ve otomatik tamamlama özellikleri berbat), ama genel olarak MySQL'den çok daha iyi oldu. Hâlâ herhangi bir kritik veri için kullanmaya <a %(youtube)s>çok istekli değiliz</a> (gerçi çok <a %(elastic_co)s>ilerleme</a> kaydettiler), ama genel olarak değişiklikten oldukça memnunuz."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Şimdilik, çok daha hızlı arama, daha iyi dil desteği, daha iyi alaka sıralaması, farklı sıralama seçenekleri ve dil/kitap türü/dosya türü üzerinde filtreleme uyguladık. Nasıl çalıştığını merak ediyorsanız, <a %(annas_archive_l140)s>bir</a> <a %(annas_archive_l1115)s>göz</a> <a %(annas_archive_l1635)s>atın</a>. Oldukça erişilebilir, ancak biraz daha fazla yoruma ihtiyacı olabilir…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ kitap kapağı yayınlandı"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Son olarak, küçük bir sürüm duyurmaktan mutluluk duyuyoruz. Libgen.rs çatalını işleten kişilerle işbirliği içinde, tüm kitap kapaklarını torrentler ve IPFS aracılığıyla paylaşıyoruz. Bu, kapakların görüntülenmesi yükünü daha fazla makine arasında dağıtacak ve onları daha iyi koruyacak. Birçok (ama hepsi değil) durumda, kitap kapakları dosyaların içinde yer alıyor, bu yüzden bu bir tür \"türetilmiş veri\". Ancak IPFS'de bulunması, hem Anna’nın Arşivi hem de çeşitli Library Genesis çatalları için günlük operasyonlar açısından hala çok faydalı."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Her zamanki gibi, bu sürümü Korsan Kütüphane Yansıtma'da bulabilirsiniz (DÜZENLE: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne taşındı). Burada bağlantı vermeyeceğiz, ama kolayca bulabilirsiniz."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Umarız Z-Library'e iyi bir alternatif bulduğumuz için biraz tempomuzu yavaşlatabiliriz. Bu iş yükü pek sürdürülebilir değil. Programlama, sunucu operasyonları veya koruma çalışmalarıyla ilgileniyorsanız, kesinlikle bizimle iletişime geçin. Hâlâ yapılacak çok <a %(annas_archive)s>iş var</a>. İlginiz ve desteğiniz için teşekkürler."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna’nın Arşivi, dünyanın en büyük çizgi roman gölge kütüphanesini (95TB) yedekledi — siz de tohumlamaya yardımcı olabilirsiniz"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Dünyanın en büyük çizgi roman gölge kütüphanesi, bugüne kadar tek bir arıza noktasına sahipti."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Hacker News'te Tartışın</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Çizgi romanların en büyük gölge kütüphanesi muhtemelen belirli bir Library Genesis çatalına aittir: Libgen.li. O siteyi yöneten tek yönetici, 2 milyondan fazla dosya içeren çılgın bir çizgi roman koleksiyonu toplamayı başardı, toplamda 95TB'den fazla. Ancak, diğer Library Genesis koleksiyonlarının aksine, bu koleksiyon torrentler aracılığıyla toplu olarak mevcut değildi. Bu çizgi romanlara yalnızca yavaş kişisel sunucusu üzerinden tek tek erişebiliyordunuz — tek bir arıza noktası. Bugüne kadar!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Bu yazıda, bu koleksiyon hakkında ve bu çalışmayı daha fazla desteklemek için düzenlediğimiz bağış kampanyası hakkında daha fazla bilgi vereceğiz."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon, kütüphanenin sıradan dünyasında kaybolmaya çalışıyor…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen çatalları"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Öncelikle, biraz arka plan bilgisi. Library Genesis'i muhteşem kitap koleksiyonu ile tanıyor olabilirsiniz. Daha az kişi, Library Genesis gönüllülerinin, Sci-Hub'un kurucusu Alexandra Elbakyan ile işbirliği içinde Sci-Hub'un tam bir yedeği, büyük bir dergi ve standart belgeler koleksiyonu ve gerçekten de devasa bir çizgi roman koleksiyonu gibi diğer projeleri oluşturduğunu biliyor."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Bir noktada, Library Genesis aynalarının farklı operatörleri yollarını ayırdı ve bu, hala Library Genesis adını taşıyan bir dizi farklı \"çatalın\" ortaya çıkmasına neden oldu. Libgen.li çatalı, bu çizgi roman koleksiyonuna ve ayrıca büyük bir dergi koleksiyonuna (üzerinde de çalışıyoruz) benzersiz bir şekilde sahiptir."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "İşbirliği"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Büyüklüğü göz önüne alındığında, bu koleksiyon uzun zamandır istek listemizdeydi, bu yüzden Z-Library'yi yedekleme konusundaki başarımızdan sonra, bu koleksiyona odaklandık. Başlangıçta doğrudan kazıdık, bu oldukça zordu çünkü sunucuları en iyi durumda değildi. Bu şekilde yaklaşık 15TB elde ettik, ancak yavaş ilerliyordu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Neyse ki, kütüphanenin operatörüyle iletişime geçmeyi başardık ve tüm verileri doğrudan bize göndermeyi kabul etti, bu çok daha hızlıydı. Yine de tüm verileri aktarmak ve işlemek yarım yıldan fazla sürdü ve disk bozulması nedeniyle neredeyse hepsini kaybediyorduk, bu da her şeye yeniden başlamamız anlamına gelirdi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Bu deneyim, bu verileri mümkün olan en kısa sürede yaymanın önemli olduğuna inanmamıza neden oldu, böylece geniş çapta yansıtılabilir. Bu koleksiyonu sonsuza kadar kaybetmemize sadece bir veya iki şanssız olay uzaktayız!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Koleksiyon"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Hızlı hareket etmek, koleksiyonun biraz düzensiz olduğu anlamına geliyor… Bir göz atalım. Gerçekte torrentler arasında böldüğümüz bir dosya sistemi hayal edin:"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "İlk dizin, <code>/repository</code>, bunun daha yapılandırılmış kısmıdır. Bu dizin, her biri veritabanında artan numaralarla numaralandırılmış binlerce dosya içeren \"bin dizinleri\" olarak adlandırılan dizinleri içerir. <code>0</code> dizini, comic_id 0–999 olan dosyaları içerir ve bu şekilde devam eder."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Bu, Library Genesis'in kurgu ve kurgu dışı koleksiyonları için kullandığı aynı şemadır. Fikir, her \"bin dizinin\" dolduğunda otomatik olarak bir torrente dönüştürülmesidir."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Ancak, Libgen.li operatörü bu koleksiyon için hiçbir zaman torrent yapmadı, bu yüzden bin dizinleri muhtemelen elverişsiz hale geldi ve \"sıralanmamış dizinlere\" yol açtı. Bunlar <code>/comics0</code> ile <code>/comics4</code> arasındadır. Hepsi, dosyaları toplamak için muhtemelen mantıklı olan, ancak şimdi bizim için pek anlam ifade etmeyen benzersiz dizin yapıları içerir. Neyse ki, metadata hala doğrudan bu dosyalara atıfta bulunuyor, bu yüzden disk üzerindeki depolama organizasyonları aslında önemli değil!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata, bir MySQL veritabanı biçiminde mevcuttur. Bu, doğrudan Libgen.li web sitesinden indirilebilir, ancak biz de kendi MD5 hash'lerimizi içeren tabloyla birlikte bir torrentte sunacağız."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analiz"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Depolama kümenize 95TB döküldüğünde, içinde ne olduğunu anlamaya çalışırsınız… Boyutu biraz azaltıp azaltamayacağımızı görmek için bazı analizler yaptık, örneğin kopyaları kaldırarak. İşte bazı bulgularımız:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Anlamsal kopyalar (aynı kitabın farklı taramaları) teorik olarak filtrelenebilir, ancak bu zordur. Çizgi romanları manuel olarak incelediğimizde çok fazla yanlış pozitif bulduk."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Sadece MD5 ile kopyalar var, bu nispeten israf, ancak bunları filtrelemek bize sadece yaklaşık 1% in tasarruf sağlar. Bu ölçekte bu hala yaklaşık 1TB, ancak bu ölçekte 1TB gerçekten önemli değil. Bu süreçte yanlışlıkla veri yok etme riskini almak istemeyiz."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Çizgi romanlara dayanan filmler gibi bir dizi kitap dışı veri bulduk. Bu da israf gibi görünüyor, çünkü bunlar zaten başka yollarla geniş çapta mevcut. Ancak, bilgisayarda yayınlanan ve birinin kaydedip film olarak kaydettiği <em>etkileşimli çizgi romanlar</em> da olduğu için film dosyalarını filtreleyemeyeceğimizi fark ettik."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Sonuçta, koleksiyondan sileceğimiz herhangi bir şey sadece birkaç yüzde tasarruf sağlayacaktır. Sonra hatırladık ki biz veri biriktiricileriz ve bunu yansıtacak olanlar da veri biriktiriciler, bu yüzden, “SİLMEK Mİ?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Bu nedenle, size tam ve değiştirilmemiş koleksiyonu sunuyoruz. Bu çok fazla veri, ancak yine de yeterince insanın bunu paylaşmak isteyeceğini umuyoruz."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Bağış Kampanyası"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Bu veriyi büyük parçalar halinde yayımlıyoruz. İlk torrent <code>/comics0</code> içindir ve bunu devasa bir 12TB .tar dosyasına koyduk. Bu, sabit diskiniz ve torrent yazılımınız için sayısız küçük dosyadan daha iyidir."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Bu yayının bir parçası olarak bir bağış kampanyası düzenliyoruz. Bu koleksiyonun operasyonel ve sözleşme maliyetlerini karşılamak ve devam eden ve gelecekteki projeleri mümkün kılmak için 20.000 $ toplamayı hedefliyoruz. Bazı <em>devasa</em> projeler üzerinde çalışıyoruz."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Bağışımla kimi destekliyorum?</em> Kısaca: Tüm insanlık bilgisini ve kültürünü yedekliyoruz ve bunu kolayca erişilebilir hale getiriyoruz. Tüm kodlarımız ve verilerimiz açık kaynaklıdır, tamamen gönüllüler tarafından yürütülen bir projeyiz ve şu ana kadar 125TB değerinde kitap kurtardık (Libgen ve Scihub’un mevcut torrentlerine ek olarak). Sonuçta, dünyadaki tüm kitapları bulmayı, taramayı ve yedeklemeyi sağlayan ve teşvik eden bir döngü oluşturuyoruz. Ana planımızı gelecekteki bir yazıda paylaşacağız. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "12 aylık “Amazing Archivist” üyeliği için bağış yaparsanız ($780), bir torrent “evlat edinme” hakkına sahip olursunuz, yani kullanıcı adınızı veya mesajınızı torrentlerden birinin dosya adına koyarız!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "<a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne gidip “Bağış Yap” butonuna tıklayarak bağış yapabilirsiniz. Ayrıca daha fazla gönüllü arıyoruz: yazılım mühendisleri, güvenlik araştırmacıları, anonim ticaret uzmanları ve çevirmenler. Bize barındırma hizmetleri sağlayarak da destek olabilirsiniz. Ve tabii ki, lütfen torrentlerimizi paylaşın!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Bize zaten cömertçe destek olan herkese teşekkürler! Gerçekten fark yaratıyorsunuz."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "İşte şimdiye kadar yayımlanan torrentler (geri kalanını hâlâ işliyoruz):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tüm torrentler <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’nde “Datasets” altında bulunabilir (oraya doğrudan bağlantı vermiyoruz, böylece bu bloga olan bağlantılar Reddit, Twitter vb. yerlerden kaldırılmasın). Oradan, Tor web sitesine giden bağlantıyı takip edin."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Sırada ne var?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Bir dizi torrent uzun vadeli koruma için harika, ancak günlük erişim için pek uygun değil. Tüm bu verileri web üzerinde almak için barındırma ortaklarıyla çalışacağız (çünkü Anna’nın Arşivi doğrudan hiçbir şey barındırmıyor). Tabii ki bu indirme bağlantılarını Anna’nın Arşivi’nde bulabileceksiniz."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Herkesi bu verilerle bir şeyler yapmaya davet ediyoruz! Bize daha iyi analiz etmemize, yinelenenleri ayıklamamıza, IPFS’ye yüklememize, remix yapmamıza, AI modellerinizi bununla eğitmenize ve daha fazlasına yardımcı olun. Hepsi sizin, ve ne yapacağınızı görmek için sabırsızlanıyoruz."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Son olarak, daha önce de söylediğimiz gibi, hâlâ bazı devasa yayınlarımız var (eğer <em>biri</em> <em>yanlışlıkla</em> bir <em>belirli</em> ACS4 veritabanı dökümü gönderirse, nerede bulacağınızı biliyorsunuz…), ayrıca dünyadaki tüm kitapları yedeklemek için döngü oluşturuyoruz."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Bu yüzden bizi izlemeye devam edin, henüz yeni başlıyoruz."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "Korsan Kütüphane Yansıtması’na 3 yeni kitap eklendi (+24TB, 3.8 milyon kitap)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Korsan Kütüphane Yansıtması’nın orijinal yayımında (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı), Z-Library’nin, büyük bir yasadışı kitap koleksiyonunun bir yansımasını yaptık. Hatırlatma olarak, bu orijinal blog yazısında yazdıklarımız:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library popüler (ve yasadışı) bir kütüphanedir. Library Genesis koleksiyonunu alıp kolayca aranabilir hale getirdiler. Bunun üzerine, yeni kitap katkılarını teşvik ederek çok etkili hale geldiler. Şu anda bu yeni kitapları Library Genesis’e geri katkıda bulunmuyorlar. Ve Library Genesis’in aksine, koleksiyonlarını kolayca yansıtılabilir hale getirmiyorlar, bu da geniş çapta korunmayı engelliyor. Bu, iş modelleri için önemlidir, çünkü koleksiyonlarına toplu erişim için (günde 10 kitaptan fazla) para talep ediyorlar."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Para karşılığında yasadışı bir kitap koleksiyonuna toplu erişim sağlama konusunda ahlaki yargılarda bulunmuyoruz. Z-Library'nin bilgiye erişimi genişletmede ve daha fazla kitap temin etmede başarılı olduğu şüphesizdir. Biz sadece üzerimize düşeni yapıyoruz: bu özel koleksiyonun uzun vadeli korunmasını sağlamak."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Bu koleksiyon 2021'in ortalarına kadar uzanıyor. Bu arada, Z-Library şaşırtıcı bir hızla büyüyor: yaklaşık 3.8 milyon yeni kitap eklediler. Elbette bazı kopyalar var, ancak çoğunluğu gerçekten yeni kitaplar veya daha önce gönderilen kitapların daha yüksek kaliteli taramaları gibi görünüyor. Bu büyük ölçüde Z-Library'deki gönüllü moderatör sayısının artması ve kopyaları önleyen toplu yükleme sistemleri sayesinde. Bu başarılarından dolayı onları tebrik etmek istiyoruz."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Z-Library'ye son yansıtma işlemimiz ile Ağustos 2022 arasında eklenen tüm kitapları aldığımızı duyurmaktan mutluluk duyuyoruz. Ayrıca ilk seferde kaçırdığımız bazı kitapları da geri dönüp topladık. Sonuç olarak, bu yeni koleksiyon yaklaşık 24TB, bu da önceki (7TB) koleksiyondan çok daha büyük. Yansıtma işlemimiz şimdi toplamda 31TB. Yine, Library Genesis'e karşı kopyaları önledik, çünkü bu koleksiyon için zaten torrentler mevcut."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Lütfen yeni koleksiyonu kontrol etmek için Korsan Kütüphane Yansıtması'na gidin (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı). Orada dosyaların nasıl yapılandırıldığı ve son seferden bu yana nelerin değiştiği hakkında daha fazla bilgi var. Buradan bağlantı vermeyeceğiz, çünkü bu sadece yasadışı materyaller barındırmayan bir blog sitesi."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Elbette, tohumlama da bize yardımcı olmanın harika bir yoludur. Önceki torrent setimizi tohumlayan herkese teşekkürler. Olumlu tepkiler için minnettarız ve bu alışılmadık şekilde bilgi ve kültürün korunmasına önem veren bu kadar çok insanın olmasından mutluyuz."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Nasıl korsan arşivci olunur"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "İlk zorluk şaşırtıcı olabilir. Bu ne teknik bir sorun ne de yasal bir sorun. Bu psikolojik bir sorun."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Dalmadan önce, Korsan Kütüphane Yansıtması hakkında iki güncelleme (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Çok cömert bağışlar aldık. İlki, Library Genesis'in orijinal kurucusu \"bookwarrior\"u da destekleyen anonim bir bireyden 10 bin dolardı. Bu bağışı kolaylaştırdığı için bookwarrior'a özel teşekkürler. İkincisi, son yayınımızdan sonra bizimle iletişime geçen ve yardım etmeye ilham alan başka bir anonim bağışçıdan 10 bin dolardı. Ayrıca bir dizi daha küçük bağış aldık. Tüm cömert desteğiniz için çok teşekkür ederiz. Bu, destekleyeceğimiz heyecan verici yeni projelerimiz var, bu yüzden bizi izlemeye devam edin."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "İkinci yayınımızın boyutuyla ilgili bazı teknik zorluklar yaşadık, ancak torrentlerimiz şimdi yayında ve tohumlanıyor. Ayrıca, koleksiyonumuzu çok yüksek hızlı sunucularında tohumlamak için anonim bir bireyden cömert bir teklif aldık, bu yüzden makinelerine özel bir yükleme yapıyoruz, ardından koleksiyonu indiren herkes hızda büyük bir iyileşme görecek."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Dijital koruma genel olarak ve korsan arşivcilik özelinde <em>neden</em> hakkında tüm kitaplar yazılabilir, ancak çok aşina olmayanlar için hızlı bir giriş yapalım. Dünya her zamankinden daha fazla bilgi ve kültür üretiyor, ancak aynı zamanda her zamankinden daha fazlası kayboluyor. İnsanlık, bu mirası akademik yayıncılar, akış hizmetleri ve sosyal medya şirketleri gibi şirketlere büyük ölçüde emanet ediyor ve bunlar genellikle iyi koruyucular olduklarını kanıtlamadılar. Dijital Amnezi belgeselini veya Jason Scott'ın herhangi bir konuşmasını izleyin."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Bazı kurumlar ellerinden geldiğince arşivleme konusunda iyi bir iş çıkarıyor, ancak yasalara bağlılar. Korsanlar olarak, telif hakkı uygulaması veya diğer kısıtlamalar nedeniyle dokunamayacakları koleksiyonları arşivleme konusunda benzersiz bir konumdayız. Ayrıca, koleksiyonları dünya genelinde birçok kez yansıtabiliriz, böylece uygun koruma şansını artırabiliriz."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Şimdilik, fikri mülkiyetin artıları ve eksileri, yasaları çiğnemenin ahlakı, sansür üzerine düşünceler veya bilgi ve kültüre erişim sorunu hakkında tartışmalara girmeyeceğiz. Tüm bunları bir kenara bırakarak, <em>nasıl</em> dalalım. Ekibimizin nasıl korsan arşivciler haline geldiğini ve bu süreçte öğrendiğimiz dersleri paylaşacağız. Bu yolculuğa çıktığınızda birçok zorlukla karşılaşacaksınız ve umarız bunlardan bazılarıyla başa çıkmanıza yardımcı olabiliriz."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Topluluk"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "İlk zorluk şaşırtıcı olabilir. Bu ne teknik bir sorun ne de yasal bir sorun. Bu psikolojik bir sorun: bu işi gölgelerde yapmak inanılmaz derecede yalnız olabilir. Ne yapmayı planladığınıza ve tehdit modelinize bağlı olarak çok dikkatli olmanız gerekebilir. Spektrumun bir ucunda, faaliyetleri hakkında çok açık olan Sci-Hub'un kurucusu Alexandra Elbakyan* gibi insanlar var. Ancak şu anda bir batı ülkesini ziyaret ederse tutuklanma riski yüksek ve onlarca yıl hapis cezasıyla karşı karşıya kalabilir. Bu riski almaya istekli olur muydunuz? Biz spektrumun diğer ucundayız; iz bırakmamaya çok dikkat ediyoruz ve güçlü operasyonel güvenliğe sahibiz."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* HN'de \"ynno\" tarafından belirtildiği gibi, Alexandra başlangıçta tanınmak istemiyordu: \"Sunucuları, PHP'den ayrıntılı hata mesajları yayacak şekilde ayarlanmıştı, bu da hatalı kaynak dosyasının tam yolunu içeriyordu, bu da çevrimiçi bir sitede kullanıcı adı olarak izlenebilecek bir dizin altındaydı, gerçek adıyla ilişkilendirilmişti. Bu ifşadan önce anonim kalmıştı.\" Bu yüzden, bu tür şeyler için kullandığınız bilgisayarlarda rastgele kullanıcı adları kullanın, yanlış bir yapılandırma yapmanız durumunda."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Ancak bu gizlilik, psikolojik bir maliyetle gelir. Çoğu insan yaptığı iş için tanınmayı sever, ancak gerçek hayatta bunun için herhangi bir kredi alamazsınız. Arkadaşlarınızın size ne yaptığınızı sorması gibi basit şeyler bile zorlayıcı olabilir (bir noktada \"NAS / homelab ile uğraşıyorum\" demek eskir)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Bu yüzden bir topluluk bulmak çok önemlidir. Çok yakın arkadaşlarınıza güvenerek bazı operasyonel güvenlikten vazgeçebilirsiniz, derinlemesine güvenebileceğinizi bildiğiniz. Yine de, yazılı bir şey koymamaya dikkat edin, çünkü yetkililere e-postalarını teslim etmeleri gerekebilir veya cihazları başka bir şekilde tehlikeye girebilir."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Daha iyisi, bazı korsan arkadaşlar bulmaktır. Yakın arkadaşlarınız size katılmakla ilgileniyorsa, harika! Aksi takdirde, çevrimiçi olarak başkalarını bulabilirsiniz. Ne yazık ki, bu hala niş bir topluluk. Şu ana kadar bu alanda aktif olan sadece bir avuç insan bulduk. İyi başlangıç noktaları Library Genesis forumları ve r/DataHoarder gibi görünüyor. Arşiv Ekibi de benzer düşünen bireyler içeriyor, ancak onlar yasalara uygun olarak çalışıyorlar (yasanın gri alanlarında olsa bile). Geleneksel \"warez\" ve korsan sahneleri de benzer şekilde düşünen insanlara sahiptir."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Topluluğu geliştirmek ve fikirleri keşfetmek için fikirlere açığız. Bize Twitter veya Reddit üzerinden mesaj atmaktan çekinmeyin. Belki bir forum veya sohbet grubu düzenleyebiliriz. Bir zorluk, yaygın platformlar kullanıldığında bunun kolayca sansürlenebilmesidir, bu yüzden bunu kendimiz barındırmamız gerekecek. Bu tartışmaları tamamen halka açık yapmak (daha fazla potansiyel etkileşim) ile özel hale getirmek (potansiyel \"hedeflerin\" onları kazıyacağımızı bilmemesi) arasında bir denge var. Bunu düşünmemiz gerekecek. Bu konuda ilgileniyorsanız bize bildirin!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projeler"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Bir proje yaptığımızda, birkaç aşaması vardır:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Alan seçimi / felsefe: Hangi alana odaklanmak istiyorsunuz ve neden? Kendi yararınıza kullanabileceğiniz benzersiz tutkularınız, becerileriniz ve koşullarınız nelerdir?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Hedef seçimi: Hangi belirli koleksiyonu yansıtacaksınız?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata kazıma: Dosyalar hakkında bilgi kataloglama, genellikle çok daha büyük olan dosyaların kendilerini indirmeden."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Veri seçimi: Metadata'ya dayanarak, şu anda arşivlemek için en alakalı verilerin daraltılması. Her şey olabilir, ancak genellikle alan ve bant genişliğini tasarruf etmenin makul bir yolu vardır."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Veri kazıma: Veriyi gerçekten elde etme."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Dağıtım: Torrentlerde paketleme, bir yerde duyurma, insanların yaymasını sağlama."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Bunlar tamamen bağımsız aşamalar değildir ve genellikle sonraki bir aşamadan gelen içgörüler sizi önceki bir aşamaya geri gönderir. Örneğin, metadata kazıma sırasında seçtiğiniz hedefin beceri seviyenizin ötesinde savunma mekanizmalarına (IP blokları gibi) sahip olduğunu fark edebilirsiniz, bu yüzden geri dönüp farklı bir hedef bulursunuz."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Alan seçimi / felsefe"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Kurtarılacak bilgi ve kültürel miras sıkıntısı yoktur, bu da bunaltıcı olabilir. Bu yüzden, katkınızın ne olabileceğini düşünmek için bir an durmak genellikle faydalıdır."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Herkesin bu konuda farklı bir düşünme şekli vardır, ancak kendinize sorabileceğiniz bazı sorular şunlardır:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Neden bununla ilgileniyorsunuz? Neye tutkulusunuz? Eğer herkesin özellikle önemsediği şeyleri arşivlediği bir grup insan toplayabilirsek, bu çok şeyi kapsar! Tutkunuz hakkında ortalama bir kişiden çok daha fazla şey bileceksiniz, hangi verilerin kaydedilmesi önemli, en iyi koleksiyonlar ve çevrimiçi topluluklar nelerdir, vb."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Kendi yararınıza kullanabileceğiniz hangi becerilere sahipsiniz? Örneğin, çevrimiçi güvenlik uzmanıysanız, güvenli hedefler için IP bloklarını aşmanın yollarını bulabilirsiniz. Toplulukları organize etmede harikaysanız, belki de bir hedef etrafında bazı insanları bir araya getirebilirsiniz. Ancak, bu süreç boyunca iyi bir operasyonel güvenliği sürdürmek için biraz programlama bilmek faydalıdır."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Buna ne kadar zaman ayırabilirsiniz? Tavsiyemiz, küçük başlayıp alıştıkça daha büyük projeler yapmanız, ancak bu tamamen tüketici olabilir."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Odaklanmak için yüksek kaldıraçlı bir alan ne olurdu? Korsan arşivlemeye X saat harcayacaksanız, en büyük \"karşılığını\" nasıl alabilirsiniz?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Bu konuda düşündüğünüz benzersiz yollar nelerdir? Başkalarının kaçırmış olabileceği ilginç fikirler veya yaklaşımlarınız olabilir."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Bizim durumumuzda, özellikle bilimin uzun vadeli korunmasına önem verdik. Library Genesis hakkında bilgi sahibiydik ve bunun torrentler kullanılarak birçok kez tamamen yansıtıldığını biliyorduk. Bu fikri çok sevdik. Sonra bir gün, birimiz Library Genesis'te bazı bilimsel ders kitaplarını bulmaya çalıştı, ancak bulamadı, bu da ne kadar eksiksiz olduğunu sorgulattı. Daha sonra bu ders kitaplarını çevrimiçi aradık ve başka yerlerde bulduk, bu da projemizin tohumunu ekti. Z-Library hakkında bilgi sahibi olmadan önce bile, tüm bu kitapları manuel olarak toplamaya çalışmak yerine, mevcut koleksiyonları yansıtıp Library Genesis'e geri katkıda bulunma fikrimiz vardı."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Hedef seçimi"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Yani, üzerinde çalıştığımız alanı belirledik, şimdi hangi özel koleksiyonu yansıtacağız? İyi bir hedef oluşturan birkaç şey var:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Büyük"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Benzersiz: Diğer projeler tarafından zaten iyi kapsanmamış."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Erişilebilir: Metadata ve verilerini kazımanızı engellemek için tonlarca koruma katmanı kullanmayan."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Özel içgörü: Bu hedef hakkında özel bilgilere sahipsiniz, örneğin bu koleksiyona özel erişiminiz var ya da savunmalarını nasıl aşacağınızı buldunuz. Bu gerekli değil (yaklaşan projemiz özel bir şey yapmıyor), ama kesinlikle yardımcı olur!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Bilim ders kitaplarımızı Library Genesis dışındaki web sitelerinde bulduğumuzda, internete nasıl ulaştıklarını anlamaya çalıştık. Sonra Z-Library'yi bulduk ve çoğu kitabın ilk olarak orada görünmese de, sonunda oraya ulaştığını fark ettik. Library Genesis ile olan ilişkisini ve (mali) teşvik yapısını ve üstün kullanıcı arayüzünü öğrendik, bunların her ikisi de çok daha eksiksiz bir koleksiyon oluşturdu. Daha sonra bazı ön metadata ve veri kazıma işlemleri yaptık ve üyelerimizden birinin çok sayıda proxy sunucusuna özel erişimini kullanarak IP indirme sınırlarını aşabileceğimizi fark ettik."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Farklı hedefleri keşfederken, izlerinizi gizlemek için VPN'ler ve geçici e-posta adresleri kullanmanın zaten önemli olduğunu unutmayın, bu konuyu daha sonra daha fazla konuşacağız."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata kazıma"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Burada biraz daha teknik olalım. Web sitelerinden metadata kazımak için işleri oldukça basit tuttuk. Sonuçları depolamak için Python betikleri, bazen curl ve bir MySQL veritabanı kullanıyoruz. Karmaşık web sitelerini haritalayabilen herhangi bir gelişmiş kazıma yazılımı kullanmadık, çünkü şimdiye kadar sadece id'leri numaralandırarak ve HTML'yi ayrıştırarak bir veya iki tür sayfayı kazımamız gerekti. Kolayca numaralandırılabilen sayfalar yoksa, tüm sayfaları bulmaya çalışan uygun bir tarayıcıya ihtiyacınız olabilir."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Tüm bir web sitesini kazımaya başlamadan önce, bunu bir süre manuel olarak yapmayı deneyin. Nasıl çalıştığını anlamak için birkaç düzine sayfayı kendiniz inceleyin. Bazen bu şekilde IP blokları veya diğer ilginç davranışlarla karşılaşabilirsiniz. Veri kazıma için de aynı şey geçerli: Bu hedefe çok fazla dalmadan önce, verilerini etkili bir şekilde indirip indiremediğinizden emin olun."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Kısıtlamaları aşmak için deneyebileceğiniz birkaç şey var. Aynı veriyi barındıran ancak aynı kısıtlamalara sahip olmayan başka IP adresleri veya sunucular var mı? Bazı API uç noktaları kısıtlamalara sahip değilken, diğerleri var mı? İndirme hızınızda IP'niz ne zaman engelleniyor ve ne kadar süreyle? Yoksa engellenmiyor ama yavaşlatılıyor musunuz? Bir kullanıcı hesabı oluşturursanız, o zaman işler nasıl değişiyor? HTTP/2 kullanarak bağlantıları açık tutabilir misiniz ve bu, sayfa talep etme hızınızı artırıyor mu? Aynı anda birden fazla dosya listeleyen sayfalar var mı ve orada listelenen bilgiler yeterli mi?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Muhtemelen kaydetmek isteyeceğiniz şeyler şunlardır:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Başlık"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Dosya adı / konum"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: bazı dahili ID'ler olabilir, ancak ISBN veya DOI gibi ID'ler de kullanışlıdır."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Boyut: ne kadar disk alanına ihtiyacınız olduğunu hesaplamak için."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): dosyayı doğru bir şekilde indirdiğinizi doğrulamak için."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Eklenme/değiştirilme tarihi: daha sonra geri dönüp daha önce indirmediğiniz dosyaları indirmeniz için (ancak genellikle bunun için ID veya hash de kullanabilirsiniz)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Açıklama, kategori, etiketler, yazarlar, dil, vb."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Bunu genellikle iki aşamada yapıyoruz. İlk olarak, ham HTML dosyalarını indiriyoruz, genellikle doğrudan MySQL'e (çok sayıda küçük dosyadan kaçınmak için, bu konuyu aşağıda daha fazla konuşacağız). Daha sonra, ayrı bir adımda, bu HTML dosyalarını gerçek MySQL tablolarına ayrıştırıyoruz. Bu şekilde, ayrıştırma kodunuzda bir hata keşfederseniz her şeyi baştan indirmenize gerek kalmaz, çünkü yeni kodla HTML dosyalarını yeniden işleyebilirsiniz. Ayrıca, işleme adımını paralel hale getirmek genellikle daha kolaydır, böylece biraz zaman kazanırsınız (ve kazıma çalışırken işleme kodunu yazabilirsiniz, her iki adımı birden yazmak zorunda kalmadan)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Son olarak, bazı hedefler için metadata kazımanın tek seçenek olduğunu unutmayın. Orada düzgün bir şekilde korunmayan bazı büyük metadata koleksiyonları var."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Veri seçimi"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Çoğu zaman, indirilecek makul bir veri alt kümesini belirlemek için metadataları kullanabilirsiniz. Sonunda tüm verileri indirmek isteseniz bile, tespit edilme ve savunmaların güçlendirilmesi durumunda veya daha fazla disk satın almanız gerektiği için ya da hayatınızda başka bir şey ortaya çıkmadan önce en önemli öğeleri önceliklendirmeniz faydalı olabilir."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Örneğin, bir koleksiyon aynı temel kaynağın (bir kitap veya film gibi) birden fazla baskısına sahip olabilir ve bunlardan biri en iyi kalite olarak işaretlenmiş olabilir. Öncelikle bu baskıları kaydetmek mantıklı olacaktır. Sonunda tüm baskıları kaydetmek isteyebilirsiniz, çünkü bazı durumlarda metadata yanlış etiketlenmiş olabilir veya baskılar arasında bilinmeyen ödünleşimler olabilir (örneğin, \"en iyi baskı\" çoğu açıdan en iyi olabilir ancak diğer açılardan daha kötü olabilir, örneğin bir film daha yüksek çözünürlükte olabilir ancak altyazıları eksik olabilir)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Metadata veritabanınızı ilginç şeyler bulmak için de arayabilirsiniz. Barındırılan en büyük dosya nedir ve neden bu kadar büyük? En küçük dosya nedir? Belirli kategoriler, diller vb. söz konusu olduğunda ilginç veya beklenmedik desenler var mı? Çift veya çok benzer başlıklar var mı? Verilerin ne zaman eklendiğine dair desenler var mı, örneğin bir günde birçok dosyanın birden eklendiği gibi? Verisetine farklı açılardan bakarak genellikle çok şey öğrenebilirsiniz."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Bizim durumumuzda, Z-Library kitaplarını Library Genesis'teki md5 hash'lerine karşı deduplikasyon yaparak, çok fazla indirme süresi ve disk alanı tasarrufu sağladık. Ancak bu oldukça benzersiz bir durum. Çoğu durumda, hangi dosyaların zaten diğer korsanlar tarafından düzgün bir şekilde korunduğuna dair kapsamlı veritabanları yoktur. Bu, birileri için büyük bir fırsattır. Torrent sitelerinde zaten yaygın olarak tohumlanan müzik ve filmler gibi şeylerin düzenli olarak güncellenen bir genel bakışına sahip olmak harika olurdu ve bu nedenle korsan yansıtmalara dahil edilmesi daha düşük öncelikli olurdu."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Veri kazıma"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Artık verileri toplu olarak indirmeye hazırsınız. Daha önce belirtildiği gibi, bu noktada hedefin davranışını ve kısıtlamalarını daha iyi anlamak için zaten manuel olarak bir dizi dosya indirmiş olmalısınız. Ancak, bir kerede birçok dosya indirmeye başladığınızda sizi hala sürprizler bekliyor olacak."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Buradaki tavsiyemiz, işleri basit tutmaktır. Başlangıçta sadece bir dizi dosya indirin. Python kullanabilir ve ardından birden fazla iş parçacığına genişletebilirsiniz. Ancak bazen daha basit olan, doğrudan veritabanından Bash dosyaları oluşturarak ve ardından bunları birden fazla terminal penceresinde çalıştırarak ölçeklendirmektir. Burada bahsetmeye değer hızlı bir teknik hile, MySQL'de OUTFILE kullanmaktır; \"secure_file_priv\"i mysqld.cnf'de devre dışı bırakırsanız her yere yazabilirsiniz (ve Linux'taysanız AppArmor'u da devre dışı bırakmayı/üzerine yazmayı unutmayın)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Verileri basit sabit disklerde saklıyoruz. Elinizde ne varsa onunla başlayın ve yavaşça genişletin. Yüzlerce TB veri depolamayı düşünmek bunaltıcı olabilir. Karşı karşıya olduğunuz durum buysa, önce iyi bir alt küme koyun ve duyurunuzda geri kalanını depolamak için yardım isteyin. Kendiniz daha fazla sabit disk almak istiyorsanız, r/DataHoarder'da iyi fırsatlar bulmak için bazı iyi kaynaklar var."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Çok fazla dosya içeren dosya sistemleri hakkında fazla endişelenmemeye çalışın. ZFS gibi şeyler kurma tuzağına düşmek kolaydır. Ancak dikkat edilmesi gereken bir teknik detay, birçok dosya içeren dosya sistemlerinin iyi çalışmamasıdır. Basit bir çözüm olarak, farklı ID aralıkları veya hash ön ekleri için birden fazla dizin oluşturmak olduğunu gördük."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Verileri indirdikten sonra, varsa, metadatalardaki hash'leri kullanarak dosyaların bütünlüğünü kontrol ettiğinizden emin olun."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Dağıtım"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Verilere sahipsiniz, böylece muhtemelen hedefinizin dünyanın ilk korsan yansıtmasını elde ettiniz. Birçok yönden en zor kısım geride kaldı, ancak en riskli kısım hala önünüzde. Sonuçta, şimdiye kadar gizliydiniz; radarın altında uçuyordunuz. Tek yapmanız gereken, iyi bir VPN kullanmak, herhangi bir formda kişisel bilgilerinizi doldurmamak (tabii ki) ve belki de özel bir tarayıcı oturumu (veya hatta farklı bir bilgisayar) kullanmaktı."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Şimdi verileri dağıtmanız gerekiyor. Bizim durumumuzda, kitapları önce Library Genesis'e geri katkıda bulunmak istedik, ancak bunun zorluklarını (kurgu ve kurgu dışı sıralama) hızla keşfettik. Bu yüzden Library Genesis tarzı torrentler kullanarak dağıtıma karar verdik. Mevcut bir projeye katkıda bulunma fırsatınız varsa, bu size çok zaman kazandırabilir. Ancak, şu anda iyi organize edilmiş çok fazla korsan yansıtma yok."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Diyelim ki torrentleri kendiniz dağıtmaya karar verdiniz. Bu dosyaları küçük tutmaya çalışın, böylece diğer web sitelerinde kolayca yansıtılabilirler. Torrentleri kendiniz tohumlamanız gerekecek, ancak anonim kalmaya devam ederken. Bir VPN (port yönlendirmeli veya yönlendirmesiz) kullanabilir veya bir Seedbox için karıştırılmış Bitcoinlerle ödeme yapabilirsiniz. Bu terimlerin bazılarının ne anlama geldiğini bilmiyorsanız, burada risk ödünleşimlerini anlamanız önemli olduğu için bir dizi okuma yapmanız gerekecek."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Torrent dosyalarını mevcut torrent sitelerinde barındırabilirsiniz. Bizim durumumuzda, aslında bir web sitesi barındırmayı seçtik, çünkü felsefemizi net bir şekilde yaymak istedik. Bunu benzer bir şekilde kendiniz yapabilirsiniz (alan adlarımız ve barındırmamız için Njalla kullanıyoruz, karıştırılmış Bitcoinlerle ödeniyor), ancak torrentlerinizi barındırmamız için bizimle iletişime geçmekten çekinmeyin. Bu fikir tutarsa, zamanla kapsamlı bir korsan yansıtma dizini oluşturmayı hedefliyoruz."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "VPN seçimi konusunda, bu konuda zaten çok şey yazılmıştır, bu yüzden sadece itibara göre seçim yapma genel tavsiyesini tekrarlayacağız. Mahkemede test edilmiş, uzun süreli gizlilik koruma geçmişine sahip gerçek kayıt tutmama politikaları, bizim görüşümüze göre en düşük riskli seçenektir. Her şeyi doğru yapsanız bile, sıfır risk seviyesine ulaşamayacağınızı unutmayın. Örneğin, torrentlerinizi tohumlarken, son derece motive olmuş bir ulus-devlet aktörü muhtemelen VPN sunucuları için gelen ve giden veri akışlarına bakabilir ve kim olduğunuzu çıkarabilir. Ya da bir şekilde basitçe hata yapabilirsiniz. Muhtemelen biz zaten yaptık ve tekrar yapacağız. Neyse ki, ulus devletler korsanlıkla <em>o kadar</em> ilgilenmiyor."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Her proje için bir karar vermeniz gerekiyor, daha önceki kimlikle mi yayınlayacaksınız yoksa değil mi. Aynı ismi kullanmaya devam ederseniz, önceki projelerdeki operasyonel güvenlik hataları geri dönüp sizi ısırabilir. Ancak farklı isimler altında yayınlamak, daha uzun süreli bir itibar oluşturamayacağınız anlamına gelir. Başlangıçtan itibaren güçlü bir operasyonel güvenliğe sahip olmayı seçtik, böylece aynı kimliği kullanmaya devam edebiliriz, ancak hata yaparsak veya koşullar gerektirirse farklı bir isim altında yayınlamaktan çekinmeyeceğiz."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Sözü yaymak zor olabilir. Dediğimiz gibi, bu hala niş bir topluluk. Başlangıçta Reddit'te yayınladık, ancak gerçekten Hacker News'te ilgi gördük. Şimdilik önerimiz, birkaç yerde yayınlamak ve ne olacağını görmek. Ve tekrar, bizimle iletişime geçin. Daha fazla korsan arşivcilik çabalarının sözünü yaymayı çok isteriz."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Sonuç"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Umarız bu, yeni başlayan korsan arşivciler için faydalı olur. Sizi bu dünyaya davet etmekten heyecan duyuyoruz, bu yüzden bizimle iletişime geçmekten çekinmeyin. Dünyanın bilgisini ve kültürünü olabildiğince koruyalım ve geniş çapta yansıtalım."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Korsan Kütüphane Yansıtması Tanıtımı: Libgen'de olmayan 7TB kitap koruma altında"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Bu proje (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı) insan bilgisinin korunmasına ve özgürleştirilmesine katkıda bulunmayı amaçlamaktadır. Bizden önceki büyüklerin izinden giderek küçük ve mütevazı katkımızı yapıyoruz."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Bu projenin odak noktası, ismiyle açıklanmaktadır:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Korsan</strong> - Çoğu ülkede telif hakkı yasasını kasten ihlal ediyoruz. Bu, yasal varlıkların yapamayacağı bir şeyi yapmamıza olanak tanır: kitapların geniş çapta yansıtıldığından emin olmak."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Kütüphane</strong> - Çoğu kütüphane gibi, öncelikle kitaplar gibi yazılı materyallere odaklanıyoruz. Gelecekte diğer medya türlerine de genişleyebiliriz."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Yansıtmak</strong> - Mevcut kütüphanelerin yalnızca bir yansımasıyız. Odak noktamız koruma, kitapların kolayca aranabilir ve indirilebilir hale getirilmesi (erişim) veya yeni kitaplar ekleyen büyük bir topluluk oluşturmak (kaynak sağlama) değil."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Yansıttığımız ilk kütüphane Z-Library oldu. Bu popüler (ve yasadışı) bir kütüphanedir. Library Genesis koleksiyonunu alıp kolayca aranabilir hale getirdiler. Bunun yanı sıra, katkıda bulunan kullanıcılara çeşitli avantajlar sunarak yeni kitap katkılarını teşvik etmede çok etkili oldular. Şu anda bu yeni kitapları Library Genesis'e geri katkıda bulunmuyorlar. Ve Library Genesis'in aksine, koleksiyonlarını kolayca yansıtılabilir hale getirmiyorlar, bu da geniş çapta korunmayı engelliyor. Bu, iş modelleri için önemlidir, çünkü koleksiyonlarına toplu erişim (günde 10'dan fazla kitap) için para talep ediyorlar."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Para karşılığında yasadışı bir kitap koleksiyonuna toplu erişim sağlama konusunda ahlaki yargılarda bulunmuyoruz. Z-Library'nin bilgiye erişimi genişletmede ve daha fazla kitap temin etmede başarılı olduğu şüphesizdir. Biz sadece üzerimize düşeni yapıyoruz: bu özel koleksiyonun uzun vadeli korunmasını sağlamak."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "İnsan bilgisini korumaya ve özgürleştirmeye yardımcı olmanız için sizi torrentlerimizi indirip paylaşmaya davet ediyoruz. Verilerin nasıl organize edildiği hakkında daha fazla bilgi için proje sayfasına bakın."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Ayrıca, hangi koleksiyonların bir sonraki yansıtılacağı ve bunun nasıl yapılacağı konusunda fikirlerinizi paylaşmanızı çok isteriz. Birlikte çok şey başarabiliriz. Bu, sayısız diğer katkılar arasında sadece küçük bir katkıdır. Yaptığınız her şey için teşekkür ederiz."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Bu blogdan dosyalara bağlantı vermiyoruz. Lütfen kendiniz bulun.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dökümü veya Kaç Kitap Sonsuza Kadar Korunur?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Gölge kütüphanelerden dosyaları düzgün bir şekilde çoğaltmasaydık, dünyadaki tüm kitapların yüzde kaçını korumuş olurduk?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Korsan Kütüphane Yansıtması ile (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>’ne taşındı), amacımız dünyadaki tüm kitapları alıp sonsuza kadar korumaktır.<sup>1</sup> Z-Library torrentlerimiz ve orijinal Library Genesis torrentlerimiz arasında 11.783.153 dosyamız var. Ama bu gerçekten ne kadar? Bu dosyaları düzgün bir şekilde çoğaltmasaydık, dünyadaki tüm kitapların yüzde kaçını korumuş olurduk? Gerçekten böyle bir şeye sahip olmak isteriz:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of insanlığın yazılı mirası sonsuza kadar korunmuş"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Bir yüzde için bir payda gerekir: şimdiye kadar yayımlanan toplam kitap sayısı.<sup>2</sup> Google Books'un sona ermesinden önce, projede bir mühendis olan Leonid Taycher, <a %(booksearch_blogspot)s>bu sayıyı tahmin etmeye çalıştı</a>. Dilinde bir şakayla 129.864.880 (“en azından Pazar gününe kadar”) sayısına ulaştı. Bu sayıyı, dünyadaki tüm kitapların birleşik bir veritabanını oluşturarak tahmin etti. Bunun için farklı veri setlerini bir araya getirdi ve ardından çeşitli şekillerde birleştirdi."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Kısa bir ara verelim, dünyadaki tüm kitapları kataloglamaya çalışan bir başka kişi daha vardı: Merhum dijital aktivist ve Reddit'in kurucu ortağı Aaron Swartz.<sup>3</sup> O, <a %(youtube)s>Open Library</a>'yi “yayınlanmış her kitap için bir web sayfası” hedefiyle başlattı ve farklı kaynaklardan gelen verileri birleştirdi. Akademik makaleleri toplu olarak indirdiği için yargılandığında, dijital koruma çalışmaları için en yüksek bedeli ödedi ve intihar etti. Bu, grubumuzun takma adlar kullanmasının ve çok dikkatli olmamızın nedenlerinden biri. Open Library, Aaron’un mirasını devam ettirerek, Internet Archive'deki kişiler tarafından kahramanca yönetilmeye devam ediyor. Bu konuya bu yazının ilerleyen kısımlarında tekrar döneceğiz."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Google blog yazısında, Taycher bu sayıyı tahmin etmenin zorluklarından bazılarını anlatıyor. İlk olarak, bir kitap nedir? Birkaç olası tanım vardır:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fiziksel kopyalar.</strong> Açıkçası bu pek yardımcı değil, çünkü bunlar sadece aynı materyalin kopyaları. İnsanların kitaplarda yaptığı tüm notları, Fermat’ın ünlü “kenar boşluklarındaki karalamaları” gibi, koruyabilsek harika olurdu. Ama ne yazık ki, bu bir arşivcinin hayali olarak kalacak."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Eserler”.</strong> Örneğin, “Harry Potter ve Sırlar Odası” gibi bir mantıksal kavram, farklı çeviriler ve yeniden baskılar gibi tüm versiyonlarını kapsar. Bu, bir anlamda kullanışlı bir tanım, ancak neyin sayılacağına karar vermek zor olabilir. Örneğin, farklı çevirileri korumak isteyebiliriz, ancak sadece küçük farklılıklar içeren yeniden baskılar o kadar önemli olmayabilir."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Baskılar”.</strong> Burada bir kitabın her benzersiz versiyonunu sayarsınız. Kapak veya önsöz gibi herhangi bir şey farklıysa, bu farklı bir baskı olarak sayılır."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Dosyalar.</strong> Library Genesis, Sci-Hub veya Z-Library gibi gölge kütüphanelerle çalışırken ek bir husus vardır. Aynı baskının birden fazla taraması olabilir. Ve insanlar mevcut dosyaların daha iyi versiyonlarını, metni OCR kullanarak tarayarak veya açılı taranmış sayfaları düzelterek yapabilirler. Bu dosyaları yalnızca bir baskı olarak saymak istiyoruz, bu da iyi metadata veya belge benzerlik ölçütleri kullanarak yinelenenleri kaldırmayı gerektirir."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Baskılar”, “kitapların” ne olduğuna dair en pratik tanım gibi görünüyor. Bu tanım, benzersiz ISBN numaralarının atanması için de kullanılıyor. ISBN, veya Uluslararası Standart Kitap Numarası, uluslararası ticarette yaygın olarak kullanılır, çünkü uluslararası barkod sistemiyle entegre edilmiştir (“Uluslararası Ürün Numarası”). Bir kitabı mağazalarda satmak istiyorsanız, bir barkoda ihtiyacınız vardır, bu yüzden bir ISBN alırsınız."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher’ın blog yazısı, ISBN'lerin faydalı olmasına rağmen evrensel olmadığını, çünkü gerçekten sadece yetmişlerin ortalarında benimsendiğini ve dünyanın her yerinde kullanılmadığını belirtiyor. Yine de, ISBN muhtemelen kitap baskılarının en yaygın kullanılan tanımlayıcısıdır, bu yüzden en iyi başlangıç noktamızdır. Dünyadaki tüm ISBN'leri bulabilirsek, hala korunması gereken kitapların yararlı bir listesini elde ederiz."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Peki, verileri nereden alıyoruz? Dünyadaki tüm kitapların bir listesini derlemeye çalışan birkaç mevcut çaba var:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Sonuçta, Google Books için bu araştırmayı yaptılar. Ancak, metadata toplu olarak erişilebilir değil ve kazıması oldukça zor."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Daha önce belirtildiği gibi, bu onların tüm misyonu. İşbirliği yapan kütüphanelerden ve ulusal arşivlerden büyük miktarda kütüphane verisi topladılar ve toplamaya devam ediyorlar. Ayrıca, kayıtları yinelenenlerden arındırmaya ve her türlü metadata ile etiketlemeye çalışan gönüllü kütüphaneciler ve teknik bir ekipleri var. En iyi yanı, veri setlerinin tamamen açık olması. Basitçe <a %(openlibrary)s>indirebilirsiniz</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Bu, kâr amacı gütmeyen OCLC tarafından işletilen bir web sitesidir ve kütüphane yönetim sistemleri satmaktadır. Birçok kütüphaneden kitap metadata'sını toplar ve WorldCat web sitesi aracılığıyla erişilebilir hale getirir. Ancak, bu verileri satarak para kazandıkları için toplu indirme için mevcut değildir. Belirli kütüphanelerle işbirliği içinde daha sınırlı toplu veri setleri indirilebilir durumda."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Bu, bu blog yazısının konusudur. ISBNdb, özellikle fiyatlandırma verileri olmak üzere çeşitli web sitelerinden kitap metadata'sını kazır, ardından bu verileri kitap satıcılarına satar, böylece kitaplarını piyasanın geri kalanına uygun şekilde fiyatlandırabilirler. ISBN'ler günümüzde oldukça evrensel olduğundan, etkili bir şekilde “her kitap için bir web sayfası” oluşturmuşlardır."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Çeşitli bireysel kütüphane sistemleri ve arşivler.</strong> Yukarıdakiler tarafından indekslenmemiş ve toplanmamış kütüphaneler ve arşivler vardır, genellikle yetersiz finanse edildikleri için veya diğer nedenlerle verilerini Open Library, OCLC, Google vb. ile paylaşmak istemezler. Bunların çoğu, internet üzerinden erişilebilir dijital kayıtlara sahiptir ve genellikle çok iyi korunmamaktadırlar, bu yüzden yardım etmek ve tuhaf kütüphane sistemleri hakkında eğlenerek öğrenmek istiyorsanız, bunlar harika başlangıç noktalarıdır."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Bu yazıda, önceki Z-Library sürümlerimize kıyasla küçük bir sürümü duyurmaktan mutluluk duyuyoruz. ISBNdb'nin çoğunu kazıdık ve verileri Korsan Kütüphane Yansıtma web sitesinde torrent olarak erişilebilir hale getirdik (DÜZENLEME: <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne taşındı; burada doğrudan bağlantı vermeyeceğiz, sadece arayın). Bunlar yaklaşık 30.9 milyon kayıt (20GB olarak <a %(jsonlines)s>JSON Satırları</a>; 4.4GB sıkıştırılmış). Web sitelerinde aslında 32.6 milyon kayıtları olduğunu iddia ediyorlar, bu yüzden bir şekilde bazılarını kaçırmış olabiliriz veya <em>onlar</em> bir şeyleri yanlış yapıyor olabilir. Her durumda, şimdilik tam olarak nasıl yaptığımızı paylaşmayacağız — bunu okuyucuya bir alıştırma olarak bırakacağız. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Paylaşacağımız şey, dünyadaki kitap sayısını tahmin etmeye daha da yaklaşmak için bazı ön analizlerdir. Üç veri setine baktık: bu yeni ISBNdb veri seti, Z-Library gölge kütüphanesinden kazıdığımız metadata'nın orijinal sürümü (Library Genesis'i içerir) ve Open Library veri dökümü."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Bazı kaba sayılarla başlayalım:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Hem Z-Library/Libgen hem de Open Library'de benzersiz ISBN'lerden daha fazla kitap var. Bu, bu kitapların çoğunun ISBN'lerinin olmadığı anlamına mı geliyor, yoksa ISBN metadata'sı basitçe eksik mi? Bu soruyu muhtemelen diğer özelliklere (başlık, yazar, yayıncı vb.) dayalı otomatik eşleştirme, daha fazla veri kaynağı çekme ve ISBN'leri gerçek kitap taramalarından çıkarma kombinasyonu ile yanıtlayabiliriz (Z-Library/Libgen durumunda)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Bu ISBN'lerin kaçı benzersiz? Bu en iyi bir Venn diyagramı ile açıklanır:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Daha kesin olmak gerekirse:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Bu kadar az örtüşme olmasına şaşırdık! ISBNdb'de, ne Z-Library'de ne de Open Library'de yer almayan çok sayıda ISBN var ve aynı durum (daha küçük ama yine de önemli bir ölçüde) diğer ikisi için de geçerli. Bu durum birçok yeni soruyu gündeme getiriyor. Otomatik eşleştirme, ISBN'lerle etiketlenmemiş kitapların etiketlenmesine ne kadar yardımcı olurdu? Çok sayıda eşleşme olur ve dolayısıyla örtüşme artar mıydı? Ayrıca, 4. veya 5. bir veri setini dahil edersek ne olurdu? O zaman ne kadar örtüşme görürdük?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Bu bize bir başlangıç noktası veriyor. Artık Z-Library veri setinde olmayan ve başlık/yazar alanlarıyla da eşleşmeyen tüm ISBN'lere bakabiliriz. Bu, dünyadaki tüm kitapları koruma konusunda bize bir yol gösterebilir: önce internetten taramalar yaparak, ardından gerçek hayatta kitapları tarayarak. İkincisi, hatta kitle fonlamasıyla veya belirli kitapların dijitalleştirilmesini isteyen kişilerden gelen \"ödüller\" ile desteklenebilir. Tüm bunlar başka bir zamanın hikayesi."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Eğer bu konulardan herhangi birine yardımcı olmak istiyorsanız — daha fazla analiz; daha fazla metadata toplama; daha fazla kitap bulma; kitapların OCR'lanması; diğer alanlar için bunu yapma (örneğin makaleler, sesli kitaplar, filmler, TV şovları, dergiler) veya hatta bu verilerin bir kısmını ML / büyük dil modeli eğitimi gibi şeyler için kullanılabilir hale getirme — lütfen benimle iletişime geçin (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Özellikle veri analiziyle ilgileniyorsanız, veri setlerimizi ve scriptlerimizi daha kolay kullanılabilir bir formatta sunmak için çalışıyoruz. Bir not defterini çatallayıp bununla oynamaya başlamanız harika olurdu."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Son olarak, bu çalışmayı desteklemek istiyorsanız, lütfen bağış yapmayı düşünün. Bu tamamen gönüllüler tarafından yürütülen bir operasyon ve katkınız büyük bir fark yaratıyor. Her katkı önemlidir. Şu an için kripto ile bağış kabul ediyoruz; Anna’nın Arşivi'ndeki Bağış sayfasına bakın."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. \"Sonsuza kadar\" ifadesinin makul bir tanımı için. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Elbette, insanlığın yazılı mirası günümüzde kitaplardan çok daha fazlasını içeriyor. Bu gönderi ve son yayınlarımız için kitaplara odaklanıyoruz, ancak ilgi alanlarımız daha geniş."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Aaron Swartz hakkında söylenecek çok daha fazla şey var, ancak bu hikayede önemli bir rol oynadığı için onu kısaca anmak istedik. Zaman geçtikçe, daha fazla insan onun adını ilk kez duyabilir ve ardından kendileri bu konunun derinliklerine dalabilir."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Gölge kütüphanelerin kritik penceresi"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Koleksiyonlarımızı sonsuza kadar koruyabileceğimizi nasıl iddia edebiliriz, zaten 1 PB'ye yaklaşıyorlarsa?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Çince versiyonu 中文版</a>, <a %(reddit)s>Reddit</a> üzerinde tartışın, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Anna’nın Arşivi'nde, koleksiyonlarımızı sonsuza kadar koruyabileceğimizi nasıl iddia edebileceğimiz sıkça soruluyor, toplam boyut zaten 1 Petabayt'a (1000 TB) yaklaşıyor ve hala büyüyor. Bu makalede felsefemize bakacağız ve insanlığın bilgi ve kültürünü koruma misyonumuz için neden önümüzdeki on yılın kritik olduğunu göreceğiz."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Koleksiyonlarımızın <a %(annas_archive_stats)s>toplam boyutu</a>, son birkaç ayda, torrent tohumlayıcılarının sayısına göre ayrılmıştır."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Öncelikler"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Neden makaleler ve kitaplar hakkında bu kadar çok önemsiyoruz? Genel olarak koruma konusundaki temel inancımızı bir kenara bırakalım — bunun hakkında başka bir gönderi yazabiliriz. Peki neden özellikle makaleler ve kitaplar? Cevap basit: <strong>bilgi yoğunluğu</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Depolama başına megabayt başına, yazılı metin tüm medya türleri arasında en fazla bilgiyi depolar. Hem bilgiye hem de kültüre önem veriyoruz, ancak daha çok bilgiye önem veriyoruz. Genel olarak, bilgi yoğunluğu ve korumanın önemi açısından kabaca şöyle bir hiyerarşi buluyoruz:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademik makaleler, dergiler, raporlar"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "DNA dizileri, bitki tohumları veya mikrobiyal örnekler gibi organik veriler"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Kurgu dışı kitaplar"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Bilim ve mühendislik yazılım kodu"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Bilimsel ölçümler, ekonomik veriler, kurumsal raporlar gibi ölçüm verileri"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Bilim ve mühendislik web siteleri, çevrimiçi tartışmalar"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Kurgu dışı dergiler, gazeteler, kılavuzlar"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Konuşmaların, belgesellerin, podcastlerin kurgu dışı transkriptleri"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Şirketlerden veya hükümetlerden gelen dahili veriler (sızıntılar)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Genel olarak metadata kayıtları (kurgu dışı ve kurgu; diğer medya, sanat, insanlar vb.; incelemeler dahil)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Coğrafi veriler (örneğin haritalar, jeolojik araştırmalar)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Hukuki veya mahkeme işlemlerinin transkriptleri"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Yukarıdakilerin kurgusal veya eğlence versiyonları"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Bu listedeki sıralama biraz keyfi — bazı maddeler eşit veya ekibimiz içinde anlaşmazlıklar var — ve muhtemelen bazı önemli kategorileri unutuyoruz. Ancak bu, önceliklerimizi kabaca nasıl belirlediğimizdir."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Bu maddelerden bazıları, bizim için endişe verici derecede farklı (veya diğer kurumlar tarafından zaten ele alınmış) olduğu için, organik veriler veya coğrafi veriler gibi, çoğu madde aslında bizim için önemlidir."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Önceliklerimizi belirlerken bir diğer büyük faktör, belirli bir eserin ne kadar risk altında olduğudur. Odaklanmayı tercih ettiğimiz eserler şunlardır:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Nadir"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Benzersiz şekilde odaklanılmamış"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Benzersiz şekilde yok olma riski altında (örneğin savaş, bütçe kesintileri, davalar veya siyasi baskı nedeniyle)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Son olarak, ölçek bizim için önemlidir. Sınırlı zamanımız ve paramız var, bu yüzden 10.000 kitabı kurtarmak için bir ay harcamayı, 1.000 kitabı kurtarmaktan daha çok tercih ederiz — eğer eşit derecede değerli ve risk altındalarsa."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Gölge kütüphaneler"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Benzer misyonlara ve önceliklere sahip birçok kuruluş var. Gerçekten de, bu tür koruma görevleri olan kütüphaneler, arşivler, laboratuvarlar, müzeler ve diğer kurumlar var. Bunların birçoğu, hükümetler, bireyler veya şirketler tarafından iyi finanse edilmektedir. Ancak büyük bir kör noktaları var: hukuk sistemi."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Burada gölge kütüphanelerin benzersiz rolü ve Anna’nın Arşivi’nin var olma nedeni yatıyor. Diğer kurumların yapmasına izin verilmeyen şeyleri yapabiliriz. Şimdi, başka yerlerde korunması yasadışı olan materyalleri arşivleyebileceğimizden değil (genellikle). Hayır, birçok yerde herhangi bir kitap, makale, dergi vb. ile bir arşiv oluşturmak yasaldır."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ancak, yasal arşivlerin genellikle eksik olduğu şey <strong>yedeklilik ve uzun ömürlülüktür</strong>. Bazı fiziksel kütüphanelerde yalnızca bir kopyası bulunan kitaplar vardır. Tek bir şirket tarafından korunan metadata kayıtları vardır. Sadece tek bir arşivde mikrofilm üzerinde korunan gazeteler vardır. Kütüphaneler bütçe kesintileri alabilir, şirketler iflas edebilir, arşivler bombalanabilir ve yakılıp yok edilebilir. Bu varsayımsal bir durum değil — bu her zaman olur."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Anna’nın Arşivi’nde benzersiz bir şekilde yapabileceğimiz şey, eserlerin birçok kopyasını büyük ölçekte depolamaktır. Makaleler, kitaplar, dergiler ve daha fazlasını toplayabilir ve toplu olarak dağıtabiliriz. Şu anda bunu torrentler aracılığıyla yapıyoruz, ancak kullanılan teknolojilerin önemi yoktur ve zamanla değişecektir. Önemli olan, birçok kopyanın dünya çapında dağıtılmasıdır. 200 yıldan fazla bir süre önce söylenen bu alıntı hala geçerliliğini koruyor:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Kayıp olan geri kazanılamaz; ama kalanları kurtaralım: onları zamanın israfına teslim ederek, kamuoyundan ve kullanımdan koruyan kasalar ve kilitlerle değil, kazaların erişemeyeceği kadar çok kopya çoğaltarak.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Kamu malı hakkında kısa bir not. Anna’nın Arşivi, dünya genelinde birçok yerde yasadışı olan faaliyetlere benzersiz bir şekilde odaklandığı için, kamu malı kitaplar gibi yaygın olarak bulunan koleksiyonlarla ilgilenmiyoruz. Yasal kuruluşlar genellikle bununla zaten iyi ilgilenir. Ancak, bazen kamuya açık koleksiyonlar üzerinde çalışmamızı gerektiren durumlar vardır:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata kayıtları Worldcat web sitesinde serbestçe görüntülenebilir, ancak toplu olarak indirilemez (biz <a %(worldcat_scrape)s>kazıyana</a> kadar)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kod Github'da açık kaynak olabilir, ancak Github'ın tamamı kolayca yansıtılamaz ve bu nedenle korunamaz (ancak bu özel durumda çoğu kod deposunun yeterince dağıtılmış kopyaları vardır)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit kullanımı ücretsizdir, ancak son zamanlarda veri aç LLM eğitimi nedeniyle katı anti-kazıma önlemleri aldı (daha sonra bu konuda daha fazla bilgi)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Kopyaların çoğaltılması"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Orijinal sorumuza geri dönersek: Koleksiyonlarımızı sonsuza kadar nasıl koruyabileceğimizi iddia edebiliriz? Buradaki ana sorun, koleksiyonumuzun <a %(torrents_stats)s>hızla büyümesidir</a>, bazı büyük koleksiyonları kazıyarak ve açık kaynak yaparak (Sci-Hub ve Library Genesis gibi diğer açık veri gölge kütüphanelerinin zaten yaptığı harika çalışmaların üzerine)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Bu veri büyümesi, koleksiyonların dünya çapında yansıtılmasını zorlaştırıyor. Veri depolama pahalıdır! Ancak, özellikle aşağıdaki üç eğilimi gözlemlediğimizde iyimseriz."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Kolay ulaşılabilir meyveleri topladık"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Bu, yukarıda tartışılan önceliklerimizden doğrudan takip eder. Öncelikle büyük koleksiyonları özgürleştirmeye çalışmayı tercih ediyoruz. Şimdi dünyanın en büyük koleksiyonlarından bazılarını güvence altına aldığımıza göre, büyümemizin çok daha yavaş olmasını bekliyoruz."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Hala daha küçük koleksiyonların uzun bir kuyruğu var ve her gün yeni kitaplar taranıyor veya yayınlanıyor, ancak oran muhtemelen çok daha yavaş olacak. Hala boyut olarak iki katına veya hatta üç katına çıkabiliriz, ancak daha uzun bir zaman diliminde."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Depolama maliyetleri üstel olarak düşmeye devam ediyor"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Yazının yazıldığı sırada, <a %(diskprices)s>disk fiyatları</a> TB başına yeni diskler için yaklaşık 12$, kullanılmış diskler için 8$ ve teyp için 4$ civarındadır. Sadece yeni diskleri dikkate alırsak, bir petabayt depolamanın maliyeti yaklaşık 12.000$'dır. Kütüphanemizin 900TB'den 2.7PB'ye üç katına çıkacağını varsayarsak, tüm kütüphanemizi yansıtmak 32.400$'a mal olacaktır. Elektrik, diğer donanım maliyetleri vb. ekleyerek, bunu 40.000$'a yuvarlayalım. Veya teyp ile daha çok 15.000$–20.000$."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Bir yandan <strong>tüm insan bilgisinin toplamı için 15.000$–40.000$ bir fırsat</strong>. Öte yandan, özellikle bu kişilerin başkalarının yararına torrentlerini tohumlamaya devam etmelerini de istiyorsak, tonlarca tam kopya beklemek biraz pahalı."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Bu bugün. Ancak ilerleme devam ediyor:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Sabit disk maliyetleri TB başına son 10 yılda kabaca üçte bir oranında azaldı ve muhtemelen benzer bir hızda düşmeye devam edecek. Teyp de benzer bir yörüngede görünüyor. SSD fiyatları daha da hızlı düşüyor ve on yılın sonunda HDD fiyatlarını geçebilir."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Farklı kaynaklardan HDD fiyat eğilimleri (çalışmayı görüntülemek için tıklayın)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Eğer bu devam ederse, 10 yıl içinde tüm koleksiyonumuzu yansıtmak için sadece 5.000$–13.000$'a (1/3) bakıyor olabiliriz, veya boyut olarak daha az büyürsek daha da az. Hala çok para olsa da, bu birçok kişi için ulaşılabilir olacak. Ve bir sonraki noktadan dolayı daha da iyi olabilir…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Bilgi yoğunluğunda iyileştirmeler"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Şu anda kitapları bize verildiği ham formatlarda saklıyoruz. Elbette sıkıştırılmış durumdalar, ancak genellikle hala büyük taramalar veya sayfa fotoğraflarıdır."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Şimdiye kadar, koleksiyonumuzun toplam boyutunu küçültmenin tek seçenekleri daha agresif sıkıştırma veya yinelenenleri kaldırma olmuştur. Ancak, yeterince önemli tasarruflar elde etmek için her ikisi de bizim için fazla kayıplı. Fotoğrafların ağır sıkıştırılması metni neredeyse okunamaz hale getirebilir. Ve yinelenenleri kaldırmak, kitapların tam olarak aynı olduğuna dair yüksek bir güven gerektirir, bu da genellikle çok yanlış olur, özellikle içerikler aynıysa ancak taramalar farklı zamanlarda yapılmışsa."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Her zaman üçüncü bir seçenek vardı, ancak kalitesi o kadar kötüydü ki hiç düşünmedik: <strong>OCR veya Optik Karakter Tanıma</strong>. Bu, fotoğrafları düz metne dönüştürme sürecidir, fotoğraflardaki karakterleri algılamak için yapay zeka kullanarak. Bunun için araçlar uzun zamandır var ve oldukça iyi, ancak \"oldukça iyi\" koruma amaçları için yeterli değil."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Ancak, son zamanlardaki çok modlu derin öğrenme modelleri son derece hızlı ilerleme kaydetti, ancak hala yüksek maliyetlerle. Önümüzdeki yıllarda hem doğruluğun hem de maliyetlerin dramatik bir şekilde iyileşmesini bekliyoruz, bu noktada tüm kütüphanemize uygulamak gerçekçi hale gelecek."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR iyileştirmeleri."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Bu gerçekleştiğinde, muhtemelen orijinal dosyaları yine de saklayacağız, ancak ek olarak çoğu kişinin yansıtmak isteyeceği çok daha küçük bir kütüphane versiyonuna sahip olabiliriz. İşin püf noktası, ham metnin kendisinin daha da iyi sıkıştırılması ve yinelenenlerin daha kolay kaldırılması, bize daha fazla tasarruf sağlamasıdır."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Genel olarak, toplam dosya boyutunda en az 5-10 kat azalma beklemek gerçekçi değil, belki daha da fazla. Muhafazakar bir 5 kat azalma ile bile, kütüphanemiz üç katına çıksa bile 10 yıl içinde <strong>1.000–3.000 $ arasında</strong> bir maliyetle karşılaşacağız."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritik pencere"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Bu tahminler doğruysa, <strong>sadece birkaç yıl beklememiz gerekecek</strong> ve tüm koleksiyonumuz geniş çapta yansıtılacak. Böylece, Thomas Jefferson'un sözleriyle, \"kazaların erişemeyeceği bir yere\" yerleştirilmiş olacak."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Ne yazık ki, LLM'lerin ortaya çıkışı ve veri açlığı eğitimi, birçok telif hakkı sahibini savunmaya geçirdi. Zaten olduklarından daha fazla. Birçok web sitesi kazıma ve arşivlemeyi zorlaştırıyor, davalar havada uçuşuyor ve bu arada fiziksel kütüphaneler ve arşivler ihmal edilmeye devam ediyor."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Bu eğilimlerin kötüleşmeye devam etmesini ve birçok eserin kamu malı olmadan çok önce kaybolmasını bekleyebiliriz."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Koruma konusunda bir devrimin eşiğindeyiz, ancak <q>kaybedilen geri kazanılamaz.</q></strong> Gölge kütüphane işletmenin ve dünya çapında birçok yansıtma oluşturmanın hala oldukça pahalı olduğu ve erişimin henüz tamamen kapatılmadığı yaklaşık 5-10 yıllık kritik bir pencereye sahibiz."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Bu pencereyi aşabilirsek, insanlığın bilgi ve kültürünü sonsuza dek korumuş olacağız. Bu zamanı boşa harcamamalıyız. Bu kritik pencerenin üzerimize kapanmasına izin vermemeliyiz."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Hadi başlayalım."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Dünyanın en büyük Çin kurgusal olmayan kitap koleksiyonuna LLM şirketleri için özel erişim"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Çince versiyon 中文版</a>, <a %(news_ycombinator)s>Hacker News'te Tartış</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Özet:</strong> Anna’nın Arşivi, Library Genesis'ten daha büyük, 7,5 milyon / 350TB Çin kurgusal olmayan kitaplardan oluşan benzersiz bir koleksiyon edindi. Yüksek kaliteli OCR ve metin çıkarımı karşılığında bir LLM şirketine özel erişim vermeye istekliyiz.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Bu kısa bir blog yazısıdır. Edindiğimiz devasa koleksiyon için OCR ve metin çıkarımı konusunda bize yardımcı olacak bir şirket veya kurum arıyoruz, karşılığında özel erken erişim sağlanacaktır. Ambargo süresi sona erdikten sonra, elbette tüm koleksiyonu yayınlayacağız."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Yüksek kaliteli akademik metinler, LLM'lerin eğitimi için son derece faydalıdır. Koleksiyonumuz Çincedir, ancak bu, İngilizce LLM'lerin eğitimi için bile faydalı olabilir: Modeller, kaynak dilden bağımsız olarak kavramları ve bilgiyi kodluyor gibi görünüyor."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Bunun için, metnin taramalardan çıkarılması gerekiyor. Anna’nın Arşivi bundan ne elde ediyor? Kullanıcıları için kitapların tam metin araması."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Hedeflerimiz LLM geliştiricileriyle örtüştüğü için bir işbirlikçi arıyoruz. Eğer doğru OCR ve metin çıkarımı yapabilirseniz, size bu koleksiyona toplu olarak 1 yıl boyunca <strong>özel erken erişim</strong> vermeye hazırız. Eğer tüm boru hattı kodunuzu bizimle paylaşmaya istekliyseniz, koleksiyonu daha uzun süre ambargo altına alabiliriz."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Örnek sayfalar"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Bize iyi bir boru hattınız olduğunu kanıtlamak için, süper iletkenler hakkında bir kitaptan başlamak üzere bazı örnek sayfalar burada. Boru hattınız matematik, tablolar, grafikler, dipnotlar ve benzeri şeyleri düzgün bir şekilde ele almalıdır."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "İşlenmiş sayfalarınızı e-posta adresimize gönderin. İyi görünürlerse, size özel olarak daha fazlasını göndereceğiz ve bu sayfalarda da boru hattınızı hızlı bir şekilde çalıştırabilmenizi bekliyoruz. Tatmin olduğumuzda, bir anlaşma yapabiliriz."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Koleksiyon"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Koleksiyon hakkında daha fazla bilgi. <a %(duxiu)s>Duxiu</a>, <a %(chaoxing)s>SuperStar Dijital Kütüphane Grubu</a> tarafından oluşturulan taranmış kitapların devasa bir veritabanıdır. Çoğu akademik kitaptır ve üniversitelere ve kütüphanelere dijital olarak sunulmak üzere taranmıştır. İngilizce konuşan izleyicilerimiz için, <a %(library_princeton)s>Princeton</a> ve <a %(guides_lib_uw)s>Washington Üniversitesi</a> iyi genel bakışlar sunmaktadır. Ayrıca daha fazla arka plan bilgisi veren mükemmel bir makale de bulunmaktadır: <a %(doi)s>“Çin Kitaplarını Dijitalleştirme: SuperStar DuXiu Scholar Arama Motoru Üzerine Bir Vaka Çalışması”</a> (Anna’nın Arşivi'nde arayın)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Duxiu'dan gelen kitaplar uzun süredir Çin internetinde korsan olarak dağıtılmaktadır. Genellikle satıcılar tarafından bir dolardan daha ucuza satılmaktadırlar. Genellikle daha fazla depolama alanı sağlamak için hacklenmiş olan Google Drive'ın Çin eşdeğeri kullanılarak dağıtılmaktadırlar. Bazı teknik detaylar <a %(github_duty_machine)s>burada</a> ve <a %(github_821_github_io)s>burada</a> bulunabilir."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Kitaplar yarı kamuya açık bir şekilde dağıtılmış olmasına rağmen, toplu olarak elde etmek oldukça zordur. Bunu yapılacaklar listemizin en üstüne koyduk ve bunun için tam zamanlı çalışmaya birkaç ay ayırdık. Ancak, yakın zamanda inanılmaz, harika ve yetenekli bir gönüllü bize ulaştı ve tüm bu çalışmayı zaten büyük bir maliyetle yaptıklarını söyledi. Koleksiyonun tamamını bizimle paylaştılar, karşılığında hiçbir şey beklemeden, sadece uzun vadeli koruma garantisi dışında. Gerçekten olağanüstü. Koleksiyonun OCR yapılması için bu şekilde yardım istemeyi kabul ettiler."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Koleksiyon 7.543.702 dosyadan oluşmaktadır. Bu, Library Genesis'in kurgusal olmayan kitaplarından (yaklaşık 5.3 milyon) daha fazladır. Toplam dosya boyutu, mevcut haliyle yaklaşık 359TB (326TiB) kadardır."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Başka önerilere ve fikirlere açığız. Sadece bizimle iletişime geçin. Koleksiyonlarımız, koruma çabalarımız ve nasıl yardımcı olabileceğiniz hakkında daha fazla bilgi için Anna’nın Arşivi'ni inceleyin. Teşekkürler!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Uyarı: bu blog yazısı artık geçerli değildir. IPFS'nin henüz ana akım için hazır olmadığına karar verdik. Mümkün olduğunda Anna’nın Arşivi'nden IPFS üzerindeki dosyalara bağlantı vermeye devam edeceğiz, ancak artık kendimiz barındırmayacağız ve başkalarına IPFS kullanarak yansıtmayı önermiyoruz. Koleksiyonumuzu korumaya yardımcı olmak istiyorsanız, Lütfen Torrents sayfamıza bakın."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Z-Library'i IPFS üzerinde tohumlamaya yardım edin"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Bir gölge kütüphane nasıl çalıştırılır: Anna’nın Arşivi'nde operasyonlar"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Gölge hayır kurumları için <q>AWS yok,</q> peki Anna’nın Arşivi'ni nasıl çalıştırıyoruz?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ni, Sci-Hub, Library Genesis ve Z-Library gibi <a %(wikipedia_shadow_library)s>gölge kütüphaneler</a> için dünyanın en büyük açık kaynaklı kâr amacı gütmeyen arama motorunu işletiyorum. Amacımız, bilgi ve kültürü kolayca erişilebilir hale getirmek ve nihayetinde <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>dünyadaki tüm kitapları</a> arşivleyen ve koruyan bir topluluk oluşturmaktır."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Bu makalede, bu web sitesini nasıl çalıştırdığımızı ve yasal durumu şüpheli bir web sitesi işletmenin getirdiği benzersiz zorlukları göstereceğim, çünkü gölge hayır kurumları için bir “AWS” yok."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Kardeş makale <a %(blog_how_to_become_a_pirate_archivist)s>Nasıl korsan arşivci olunur</a> makalesine de göz atın.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Yenilik jetonları"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Teknoloji yığınımızla başlayalım. Bilerek sıkıcı. Flask, MariaDB ve ElasticSearch kullanıyoruz. Kelimenin tam anlamıyla bu kadar. Arama büyük ölçüde çözülmüş bir sorun ve onu yeniden icat etmeyi düşünmüyoruz. Ayrıca, <a %(mcfunley)s>yenilik jetonlarımızı</a> başka bir şeye harcamamız gerekiyor: yetkililer tarafından kapatılmamak."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Peki Anna’nın Arşivi ne kadar yasal veya yasa dışı? Bu büyük ölçüde yasal yargı yetkisine bağlıdır. Çoğu ülke bir tür telif hakkına inanır, bu da belirli türdeki eserler üzerinde belirli bir süre için kişilere veya şirketlere münhasır bir tekel verildiği anlamına gelir. Yan not olarak, Anna’nın Arşivi'nde bazı faydalar olmasına rağmen, genel olarak telif hakkının toplum için net bir olumsuzluk olduğuna inanıyoruz — ama bu başka bir zamanın hikayesi."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Belirli eserler üzerindeki bu münhasır tekel, bu tekelin dışındaki herhangi birinin bu eserleri doğrudan dağıtmasının yasa dışı olduğu anlamına gelir — biz de dahil. Ancak Anna’nın Arşivi, bu eserleri doğrudan dağıtmayan bir arama motorudur (en azından açık ağ sitemizde değil), bu yüzden sorun olmamalı, değil mi? Tam olarak değil. Birçok yargı alanında, telif hakkıyla korunan eserleri dağıtmak yasa dışı olduğu gibi, bu tür eserlere bağlantı vermek de yasa dışıdır. Bunun klasik bir örneği Amerika Birleşik Devletleri’nin DMCA yasasıdır."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Bu spektrumun en katı ucudur. Spektrumun diğer ucunda teorik olarak hiç telif hakkı yasası olmayan ülkeler olabilir, ancak bunlar gerçekten yoktur. Hemen hemen her ülkenin kitaplarında bir tür telif hakkı yasası vardır. Uygulama farklı bir hikayedir. Telif hakkı yasasını uygulamakla ilgilenmeyen hükümetlere sahip birçok ülke vardır. Ayrıca, telif hakkıyla korunan eserlerin dağıtılmasını yasaklayan, ancak bu tür eserlere bağlantı vermeyi yasaklamayan iki uç arasında ülkeler de vardır."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Bir diğer husus şirket düzeyindedir. Bir şirket, telif hakkıyla ilgilenmeyen bir yargı alanında faaliyet gösteriyorsa, ancak şirket kendisi herhangi bir risk almak istemiyorsa, birisi şikayet ettiği anda web sitenizi kapatabilirler."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Son olarak, büyük bir husus ödemelerdir. Anonim kalmamız gerektiğinden, geleneksel ödeme yöntemlerini kullanamayız. Bu da bizi kripto para birimlerine bırakıyor ve yalnızca küçük bir şirket alt kümesi bunları destekliyor (kripto ile ödenen sanal banka kartları var, ancak genellikle kabul edilmiyorlar)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistem mimarisi"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Diyelim ki web sitenizi kapatmadan barındırmaya istekli bazı şirketler buldunuz — bunlara “özgürlüksever sağlayıcılar” diyelim 😄. Onlarla her şeyi barındırmanın oldukça pahalı olduğunu çabucak fark edeceksiniz, bu yüzden bazı “ucuz sağlayıcılar” bulmak ve gerçek barındırmayı orada yapmak isteyebilirsiniz, özgürlüksever sağlayıcılar aracılığıyla proxy yaparak. Doğru yaparsanız, ucuz sağlayıcılar neyi barındırdığınızı asla bilmeyecek ve asla şikayet almayacaklar."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Tüm bu sağlayıcılarla, yine de sizi kapatma riski vardır, bu yüzden yedekliliğe de ihtiyacınız var. Yığınımızın tüm seviyelerinde buna ihtiyacımız var."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Kendini ilginç bir konuma koymuş olan bir özgürlüksever şirket Cloudflare'dir. <a %(blog_cloudflare)s>İddia ettiler</a> ki bir barındırma sağlayıcısı değil, bir hizmet sağlayıcı, bir ISP gibi. Bu nedenle DMCA veya diğer kaldırma taleplerine tabi değiller ve talepleri gerçek barındırma sağlayıcınıza yönlendiriyorlar. Bu yapıyı korumak için mahkemeye gitmeye kadar gittiler. Bu nedenle onları başka bir önbellekleme ve koruma katmanı olarak kullanabiliriz."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare anonim ödemeleri kabul etmez, bu yüzden yalnızca ücretsiz planlarını kullanabiliriz. Bu, yük dengeleme veya failover özelliklerini kullanamayacağımız anlamına gelir. Bu nedenle <a %(annas_archive_l255)s>bunu kendimiz uyguladık</a> alan adı düzeyinde. Sayfa yüklendiğinde, tarayıcı mevcut alan adının hala kullanılabilir olup olmadığını kontrol eder ve değilse, tüm URL'leri farklı bir alan adına yeniden yazar. Cloudflare birçok sayfayı önbelleğe aldığı için, kullanıcı ana alan adımıza inebilir, proxy sunucusu kapalı olsa bile, ve ardından bir sonraki tıklamada başka bir alan adına taşınabilir."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ayrıca, sunucu sağlığını izleme, arka uç ve ön uç hatalarını kaydetme gibi normal operasyonel endişelerle de ilgilenmemiz gerekiyor. Failover mimarimiz, örneğin alan adlarından birinde tamamen farklı bir sunucu seti çalıştırarak bu cephede daha fazla sağlamlık sağlar. Ana sürümdeki kritik bir hata fark edilmeden kalırsa, bu ayrı alanda kodun ve verisetlerinin eski sürümlerini bile çalıştırabiliriz."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Cloudflare'ın bize karşı dönmesine karşı da önlem alabiliriz, bu ayrı alan adı gibi bir alan adından kaldırarak. Bu fikirlerin farklı permütasyonları mümkündür."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Araçlar"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Bütün bunları başarmak için hangi araçları kullandığımıza bir bakalım. Bu, yeni sorunlarla karşılaştıkça ve yeni çözümler buldukça çok gelişiyor."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Uygulama sunucusu: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy sunucusu: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Sunucu yönetimi: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Geliştirme: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Soğan statik barındırma: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Bazı kararlar üzerinde gidip geldik. Bunlardan biri sunucular arasındaki iletişim: Bunun için eskiden Wireguard kullanıyorduk, ancak bazen veri iletimini tamamen durdurduğunu veya verileri yalnızca tek yönlü ilettiğini fark ettik. Bu, denediğimiz birkaç farklı Wireguard kurulumunda, örneğin <a %(github_costela_wesher)s>wesher</a> ve <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a> ile oldu. Ayrıca autossh ve sshuttle kullanarak SSH üzerinden port tünelleme denedik, ancak <a %(github_sshuttle)s>orada sorunlarla</a> karşılaştık (autossh'un TCP-over-TCP sorunlarından muzdarip olup olmadığı hala net değil — bana biraz garip bir çözüm gibi geliyor ama belki de aslında sorun yoktur?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Bunun yerine, sunucular arasında doğrudan bağlantılara geri döndük ve ucuz sağlayıcılarda çalışan bir sunucuyu IP filtreleme ile UFW kullanarak gizledik. Bunun dezavantajı, Docker'ın UFW ile iyi çalışmaması, <code>network_mode: \"host\"</code> kullanmadığınız sürece. Tüm bunlar biraz daha hataya açık, çünkü sadece küçük bir yanlış yapılandırma ile sunucunuzu internete maruz bırakabilirsiniz. Belki de autossh'a geri dönmeliyiz — burada geri bildirim çok memnuniyetle karşılanır."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Varnish ve Nginx arasında da gidip geldik. Şu anda Varnish'i seviyoruz, ancak bazı tuhaflıkları ve pürüzlü kenarları var. Aynı şey Checkmk için de geçerli: onu sevmiyoruz ama şimdilik işe yarıyor. Weblate fena değil ama harika da değil — onu git repo'muzla senkronize etmeye çalıştığımda verilerimi kaybedeceğinden bazen korkuyorum. Flask genel olarak iyi oldu, ancak özel alan adlarını yapılandırmak veya SqlAlchemy entegrasyonu ile ilgili sorunlar gibi bazı tuhaflıklar çok zaman kaybettirdi."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Şu ana kadar diğer araçlar harika oldu: MariaDB, ElasticSearch, Gitlab, Zulip, Docker ve Tor hakkında ciddi bir şikayetimiz yok. Bunların hepsinde bazı sorunlar yaşadık, ancak hiçbir şey aşırı derecede ciddi veya zaman alıcı değildi."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Sonuç"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Sağlam ve dayanıklı bir gölge kütüphane arama motoru kurmayı öğrenmek ilginç bir deneyim oldu. Daha sonra paylaşılacak tonlarca detay var, bu yüzden daha fazla ne öğrenmek istediğinizi bana bildirin!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Her zaman olduğu gibi, bu çalışmayı desteklemek için bağış arıyoruz, bu yüzden Anna’nın Arşivi'ndeki Bağış sayfasını mutlaka kontrol edin. Ayrıca hibe, uzun vadeli sponsorlar, yüksek riskli ödeme sağlayıcıları, belki de (zevkli!) reklamlar gibi diğer destek türlerini de arıyoruz. Zamanınızı ve becerilerinizi katkıda bulunmak isterseniz, her zaman geliştiriciler, çevirmenler vb. arıyoruz. İlginiz ve desteğiniz için teşekkürler."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna ve ekip (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Merhaba, ben Anna. Dünyanın en büyük gölge kütüphanesi olan <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ni yarattım. Bu benim kişisel blogum, burada ben ve ekip arkadaşlarım korsanlık, dijital koruma ve daha fazlası hakkında yazıyoruz."

#, fuzzy
msgid "blog.index.text2"
msgstr "<a %(reddit)s>Reddit</a> üzerinden benimle bağlantı kurun."

#, fuzzy
msgid "blog.index.text3"
msgstr "Bu web sitesinin sadece bir blog olduğunu unutmayın. Burada sadece kendi sözlerimizi barındırıyoruz. Burada hiçbir torrent veya başka telif hakkıyla korunan dosya barındırılmıyor veya bağlantı verilmiyor."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blog yazıları"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat kazıma"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5.998.794 kitabı IPFS'ye koymak"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Uyarı: bu blog yazısı artık geçerli değildir. IPFS'nin henüz ana akım için hazır olmadığına karar verdik. Mümkün olduğunda Anna’nın Arşivi'nden IPFS üzerindeki dosyalara bağlantı vermeye devam edeceğiz, ancak artık kendimiz barındırmayacağız ve başkalarına IPFS kullanarak yansıtmayı önermiyoruz. Koleksiyonumuzu korumaya yardımcı olmak istiyorsanız, Lütfen Torrents sayfamıza bakın."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Özetle:</strong> Anna’nın Arşivi, korunması gereken kitapların bir yapılacaklar listesi oluşturmak için WorldCat'in (dünyanın en büyük kütüphane metadata koleksiyonu) tamamını kazıdı.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Bir yıl önce, bu soruyu <a %(blog)s>cevaplamak için yola çıktık</a>: <strong>Gölge kütüphaneler tarafından kalıcı olarak korunmuş kitapların yüzdesi nedir?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Bir kitap <a %(wikipedia_library_genesis)s>Library Genesis</a> gibi açık veri gölge kütüphanesine ve şimdi <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a>'ne girdiğinde, tüm dünyada (torrentler aracılığıyla) yansıtılır ve böylece pratikte sonsuza kadar korunur."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Kitapların hangi yüzdesinin korunduğu sorusunu yanıtlamak için payda bilmemiz gerekiyor: toplamda kaç kitap var? Ve ideal olarak sadece bir sayı değil, gerçek metadata da olmalı. Böylece onları sadece gölge kütüphanelerle eşleştirmekle kalmaz, aynı zamanda <strong>korunacak kalan kitapların bir yapılacaklar listesi oluşturabiliriz!</strong> Hatta bu yapılacaklar listesini tamamlamak için bir kitle kaynaklı çaba hayal etmeye başlayabiliriz."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "<a %(wikipedia_isbndb_com)s>ISBNdb</a>'yi kazıdık ve <a %(openlibrary)s>Open Library veri setini</a> indirdik, ancak sonuçlar tatmin edici değildi. Ana sorun, ISBN'lerin çok fazla örtüşmemesiydi. <a %(blog)s>blog yazımızdaki</a> bu Venn diyagramına bakın:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "ISBNdb ve Open Library arasında ne kadar az örtüşme olduğunu görünce çok şaşırdık, her ikisi de çeşitli kaynaklardan, web kazımaları ve kütüphane kayıtları gibi, verileri cömertçe dahil ediyor. Eğer her ikisi de dışarıdaki çoğu ISBN'i bulmada iyi bir iş çıkarıyorsa, daireleri kesinlikle önemli ölçüde örtüşmeli ya da biri diğerinin alt kümesi olmalıydı. Bizi düşündürdü, bu dairelerin tamamen dışında kaç kitap var <em></em>? Daha büyük bir veritabanına ihtiyacımız var."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "O zaman gözlerimizi dünyanın en büyük kitap veritabanına diktik: <a %(wikipedia_worldcat)s>WorldCat</a>. Bu, kâr amacı gütmeyen <a %(wikipedia_oclc)s>OCLC</a> tarafından sağlanan bir özel veritabanıdır ve dünya çapındaki kütüphanelerden metadata kayıtlarını toplar, karşılığında bu kütüphanelere tam veri setine erişim sağlar ve son kullanıcıların arama sonuçlarında görünmelerini sağlar."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "OCLC kâr amacı gütmeyen bir kuruluş olmasına rağmen, iş modeli veritabanlarını korumayı gerektiriyor. Üzgünüz OCLC'deki arkadaşlar, hepsini paylaşıyoruz. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Geçtiğimiz yıl boyunca, tüm WorldCat kayıtlarını titizlikle kazıdık. İlk başta şanslı bir mola verdik. WorldCat, web sitelerinin tam yeniden tasarımını (Ağustos 2022'de) yeni başlatıyordu. Bu, arka uç sistemlerinde önemli bir revizyon içeriyordu ve birçok güvenlik açığı ortaya çıkardı. Fırsatı hemen değerlendirdik ve sadece birkaç gün içinde yüz milyonlarca (!) kaydı kazıyabildik."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat yeniden tasarımı</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Bundan sonra, güvenlik açıkları birer birer yavaşça düzeltildi, son bulduğumuz açık yaklaşık bir ay önce yamalandı. O zamana kadar neredeyse tüm kayıtları almıştık ve sadece biraz daha yüksek kaliteli kayıtlar için gidiyorduk. Bu yüzden yayınlama zamanının geldiğini hissettik!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Veriler hakkında bazı temel bilgilere bakalım:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Anna’nın Arşiv Konteynerleri (AAC)</a>, esasen <a %(jsonlines)s>JSON Lines</a> ile sıkıştırılmış <a %(zstd)s>Zstandard</a>, artı bazı standartlaştırılmış semantikler. Bu konteynerler, uyguladığımız farklı kazımalara dayalı olarak çeşitli kayıt türlerini kapsar."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Veri"

msgid "dyn.buy_membership.error.unknown"
msgstr "Bilinmeyen bir hata oluştu. Lütfen %(email)s e-mail hesabı ile iletişime geçin ve bu adrese bir ekran görüntüsü gönderin."

msgid "dyn.buy_membership.error.minimum"
msgstr "Bu paranın minimum (işlem) limiti normalden daha yüksek. Lütfen farklı bir süre ya da farklı bir para seçin."

msgid "dyn.buy_membership.error.try_again"
msgstr "İstek tamamlanamadı. Lütfen birkaç dakika içinde tekrar deneyin ve eğer sorun devam ederse, bir ekran görüntüsüyle birlikte %(email)s adresinden iletişime geçin."

msgid "dyn.buy_membership.error.wait"
msgstr "Ödeme işlemi hatası. Lütfen bir süre bekleyin ve tekrar deneyin. Sorun 24 saatten fazla devam ederse lütfen %(email)s e-mail adresine bir ekran görüntüsü ile birlikte bizimle iletişime geçin."

msgid "page.comments.hidden_comment"
msgstr "gizli yorum"

msgid "page.comments.file_issue"
msgstr "Dosya sorunu: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Daha iyi versiyon"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Bu kullanıcıyı kötüye kullanım veya uygunsuz davranış nedeniyle bildirmek istiyor musunuz?"

msgid "page.comments.report_abuse"
msgstr "Kötüye kullanımı bildir"

msgid "page.comments.abuse_reported"
msgstr "Bildirilen kötüye kullanım:"

msgid "page.comments.reported_abuse_this_user"
msgstr "Bu kullanıcıyı kötüye kullanım nedeniyle bildirdiniz."

msgid "page.comments.reply_button"
msgstr "Yanıtla"

msgid "page.md5.quality.logged_out_login"
msgstr "Lütfen <a %(a_login)s>giriş yapın</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Bir yorum bıraktınız. Görünmesi bir dakika sürebilir."

msgid "page.md5.quality.comment_error"
msgstr "Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s etkilenen sayfalar"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Library Genesis'e ait Kurgu Dışı \".rs-fork\"unda görünür değil"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Library Genesis Kurgu \".rs-fork\"da görünür değil"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Libgen.li'de görünür değil"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Libgen.li'de bozuk olarak işaretlendi"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Z-library'de mevcut değil"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Z-Library'de “spam” olarak işaretlendi"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Z-Library'de “kötü dosya” olarak işaretlendi"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Tüm sayfalar PDF'e dönüştürülemedi"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Bu dosyada exiftool çalıştırma başarısız oldu"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Kitap (bilinmeyen)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Kitap (kurgu dışı)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Kitap (kurgu)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Dergi makalesi"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standart dokümanı"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Dergi"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Çizgi roman"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Müzik notası"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Sesli Kitap"

msgid "common.md5_content_type_mapping.other"
msgstr "Diğer"

msgid "common.access_types_mapping.aa_download"
msgstr "Dost Sunucu indirme"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Harici indirme"

msgid "common.access_types_mapping.external_borrow"
msgstr "Harici ödünç alma"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Harici ödünç alma (yazdırma engellendi)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Üstveriyi keşfedin"

msgid "common.access_types_mapping.torrents_available"
msgstr "Torrentlerde yer alıyor"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Kütüphane"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Çince"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "AA'ya Yüklemeler"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eKitap Dizini"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Çek üstverisi"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Kitaplar"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Rusya Devlet Kütüphanesi"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Başlık"

msgid "common.specific_search_fields.author"
msgstr "Yazar"

msgid "common.specific_search_fields.publisher"
msgstr "Yayınlayan"

msgid "common.specific_search_fields.edition_varia"
msgstr "Baskı"

msgid "common.specific_search_fields.year"
msgstr "Yayın yılı"

msgid "common.specific_search_fields.original_filename"
msgstr "Orijinal dosya adı"

msgid "common.specific_search_fields.description_comments"
msgstr "Açıklama ve üstveri yorumları"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Dost Sunucu indirmeleri bu dosya için geçici olarak mevcut değil."

msgid "common.md5.servers.fast_partner"
msgstr "Hızlı Ortak Sunucu #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(önerilen)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(tarayıcı doğrulaması veya bekleme listesi yok)"

msgid "common.md5.servers.slow_partner"
msgstr "Yavaş Ortak Sunucu #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(biraz daha hızlı ama bekleme listesi var)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(bekleme listesi yok, ancak çok yavaş olabilir)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Kurgu Dışı"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Kurgu"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(yine üst kısımdaki “GET”e tıklayın)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(en üstteki “GET”e tıklayın)"

msgid "page.md5.box.download.libgen_ads"
msgstr "reklamlarının kötü amaçlı yazılım içerdiği biliniyor, bu yüzden bir reklam engelleyici kullanın veya reklamlara tıklamayın"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC dosyalarını indirmek güvenli olmayabilir)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Tor'da Z-Library"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(Tor Browser gerekli)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Internet Archive'den ödünç al"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(sadece yazdırma engelli kullanıcılar için)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(ilgili DOI Sci-Hub'da mevcut olmayabilir)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "koleksiyon"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Toplu torrent indirmeleri"

msgid "page.md5.box.download.experts_only"
msgstr "(sadece uzmanlar)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Anna'nın Arşivinde ISBN aratın"

msgid "page.md5.box.download.other_isbn"
msgstr "Diğer çeşitli veritabanlarında ISBN aratın"

msgid "page.md5.box.download.original_isbndb"
msgstr "ISBNdb'de orijinal kayıt bulun"

msgid "page.md5.box.download.aa_openlib"
msgstr "Open Library ID için Anna’nın Arşivi'nde ara"

msgid "page.md5.box.download.original_openlib"
msgstr "Open Library'de orijinal kaydı bul"

msgid "page.md5.box.download.aa_oclc"
msgstr "OCLC (WorldCat) numarası için Anna’nın Arşivi'nde Ara"

msgid "page.md5.box.download.original_oclc"
msgstr "WorldCat'te orijinal kaydı bulun"

msgid "page.md5.box.download.aa_duxiu"
msgstr "DuXiu SSID numarası için Anna’nın Arşivi'nde Ara"

msgid "page.md5.box.download.original_duxiu"
msgstr "DuXiu'da manuel olarak ara"

msgid "page.md5.box.download.aa_cadal"
msgstr "Anna’nın Arşivi'nde CADAL SSNO numarasını ara"

msgid "page.md5.box.download.original_cadal"
msgstr "CADAL'da orijinal kaydı bulun"

msgid "page.md5.box.download.aa_dxid"
msgstr "Anna’nın Arşivi'nde DuXiu DXID numarasını ara"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eKitap Dizini"

msgid "page.md5.box.download.scidb"
msgstr "Anna'nın Arşivi 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(tarayıcı doğrulama gerekmez)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Çek üstveri %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Kitaplar %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Üstveri"

msgid "page.md5.box.descr_title"
msgstr "açıklama"

msgid "page.md5.box.alternative_filename"
msgstr "Alternatif dosya adı"

msgid "page.md5.box.alternative_title"
msgstr "Alternatif başlık"

msgid "page.md5.box.alternative_author"
msgstr "Alternatif yazar"

msgid "page.md5.box.alternative_publisher"
msgstr "Alternatif yayıncı"

msgid "page.md5.box.alternative_edition"
msgstr "Alternatif baskı"

msgid "page.md5.box.alternative_extension"
msgstr "Alternatif uzantı"

msgid "page.md5.box.metadata_comments_title"
msgstr "üstveri yorumları"

msgid "page.md5.box.alternative_description"
msgstr "Alternatif açıklama"

msgid "page.md5.box.date_open_sourced_title"
msgstr "açık kaynak olma tarihi"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub dosyası “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Kontrollü Dijital Ödünç Verme dosyası “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Bu, Internet Archive'den bir dosyanın kaydıdır, doğrudan indirilebilir bir dosya değildir. Kitabı ödünç almaya çalışabilirsiniz (aşağıdaki bağlantı), veya <a %(a_request)s>dosya talep ederken</a> bu URL'yi kullanabilirsiniz."

msgid "page.md5.header.consider_upload"
msgstr "Bu dosyaya sahipseniz ve Anna’nın Arşivi’nde henüz mevcut değilse <a %(a_request)s>yüklemeyi</a> düşünün."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s üstveri kaydı"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s üstveri kaydı"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) numarası %(id)s üstveri kaydı"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s üstveri kaydı"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s üstveri kaydı"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB Kimlik %(id)s üstveri kaydı"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC Kimlik %(id)s üstveri kaydı"

msgid "page.md5.header.meta_desc"
msgstr "Bu bir üstveri kaydıdır, indirilebilir bir dosya değildir. <a %(a_request)s>Dosya talep ederken</a> bu URL'yi kullanabilirsiniz."

msgid "page.md5.text.linked_metadata"
msgstr "Bağlantılı kayıttan üstveri"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Open Library'de üstveriyi iyileştir"

msgid "page.md5.warning.multiple_links"
msgstr "Uyarı: birden fazla bağlantılı kayıt:"

msgid "page.md5.header.improve_metadata"
msgstr "Üstveriyi geliştir"

msgid "page.md5.text.report_quality"
msgstr "Dosya kalitesini bildir"

msgid "page.search.results.download_time"
msgstr "İndirme süresi"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Web Sitesi:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Anna’nın Arşivi’nde “%(name)s” ara"

msgid "page.md5.codes.code_explorer"
msgstr "Kodlar Gezgini:"

msgid "page.md5.codes.code_search"
msgstr "Kod Gezgini'nde Görüntüle “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Daha fazla…"

msgid "page.md5.tabs.downloads"
msgstr "İndirmeler (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Ödünç Al (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Üstveriyi keşfet (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Yorumlar (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listeler (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "İstatistikler (%(count)s)"

msgid "common.tech_details"
msgstr "Teknik ayrıntılar"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Bu dosya sorunlar içerebilir ve kaynak kütüphanesinden gizlenmiştir.</span> Bu bazen telif hakkı sahibi tarafından istenir, bazen daha iyi bir alternatif mevcutsa, bazen de dosyanın kendisiyle ilgili bir sorun nedeniyle olur. İndirmek hala uygun olabilir ancak öncelikle alternatif bir dosya aramayı öneririz. Daha fazla ayrıntı için:"

msgid "page.md5.box.download.better_file"
msgstr "%(link)s adresinde bu dosyanın daha iyi bir versiyonu olabilir"

msgid "page.md5.box.issues.text2"
msgstr "Eğer hala bu dosyayı indirmek istiyorsanız, dosyayı açmak için güvenilir ve güncel yazılımları kullanmaya dikkat edin."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Hızlı indirmeler"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Hızlı indirmeler</strong> Kitapların, makalelerin ve daha fazlasının uzun zamanlı saklanmasını desteklemek için bir <a %(a_membership)s>üye</a> olun. Desteğinizden ötürü şükranlarımızı göstermek amacıyla size hızlı indirme imkanı sağlıyoruz. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Bu ay bağış yaparsanız, <strong>iki kat</strong> hızlı indirme hakkı kazanırsınız."

msgid "page.md5.box.download.header_fast_member"
msgstr "Bugünlük %(remaining)s kadar hakkınız kaldı. Üye olduğunuz için teşekkürler! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Bugün için hızlı indirme hakkınız tükendi."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Bu dosyayı yakın bir zamanda indirdiniz. Bağlantılar bir süreliğine geçerli kalır."

msgid "page.md5.box.download.option"
msgstr "Seçenek #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(yönlendirme yok)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(görüntüleyicide aç)"

msgid "layout.index.header.banner.refer"
msgstr "Bir arkadaşınızı sitemize yönlendirin ve hem siz hem de arkadaşınız %(percentage)s%% bonus hızlı indirme kazanın!"

msgid "layout.index.header.learn_more"
msgstr "Daha fazla bilgi…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Yavaş indirmeler"

msgid "page.md5.box.download.trusted_partners"
msgstr "Güvenilir ortaklardan."

msgid "page.md5.box.download.slow_faq"
msgstr "Daha fazla bilgi <a %(a_slow)s>SSS</a>'de."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(<a %(a_browser)s>tarayıcı doğrulama</a> gerektirebilir — sınırsız indirme!)"

msgid "page.md5.box.download.after_downloading"
msgstr "İndirdikten sonra:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Görüntüleyicimizde aç"

msgid "page.md5.box.external_downloads"
msgstr "harici indirmeleri göster"

msgid "page.md5.box.download.header_external"
msgstr "Harici indirmeler"

msgid "page.md5.box.download.no_found"
msgstr "İndirme bulunamadı."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Tüm aynalarda aynı dosya vardır ve kullanımları güvenli olmalıdır. Bununla birlikte, internetten dosya indirirken her zaman dikkatli olun. Örneğin, cihazlarınızı güncel tuttuğunuzdan emin olun."

msgid "page.md5.box.download.dl_managers"
msgstr "Büyük dosyalar için, kesintileri önlemek amacıyla bir indirme yöneticisi kullanmanızı öneririz."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Önerilen indirme yöneticileri: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Dosyayı açmak için, dosya formatına bağlı olarak bir e-kitap veya PDF okuyucuya ihtiyacınız olacak."

msgid "page.md5.box.download.readers.links"
msgstr "Önerilen e-kitap okuyucuları: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Anna’nın Arşivi çevrimiçi görüntüleyici"

msgid "page.md5.box.download.conversion"
msgstr "Formatlar arasında dönüştürme yapmak için çevrim içi araçları kullanın."

msgid "page.md5.box.download.conversion.links"
msgstr "Önerilen dönüştürme araçları: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Hem PDF hem de EPUB dosyalarını Kindle veya Kobo eOkuyucunuza gönderebilirsiniz."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Önerilen araçlar: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon’un “Kindle’a Gönder”"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz’in “Kobo/Kindle’a Gönder”"

msgid "page.md5.box.download.support"
msgstr "Yazarları ve kütüphaneleri destekleyin"

msgid "page.md5.box.download.support.authors"
msgstr "Bunu beğendiyseniz ve maddi durumunuz elveriyorsa, orijinalini satın almayı veya doğrudan yazarlara destek olmayı düşünün."

msgid "page.md5.box.download.support.libraries"
msgstr "Eğer bu kitabı yerel kütüphanenizde bulabiliyorsanız oradan ücretsiz olarak ödünç almayı düşünün."

msgid "page.md5.quality.header"
msgstr "Dosya kalitesi"

msgid "page.md5.quality.report"
msgstr "Bu dosyanın kalitesini bildirerek topluluğa yardımcı olun! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Dosya sorunu bildir (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Harika dosya kalitesi (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Yorum ekle (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Bu dosyada nasıl bir sorun var?"

msgid "page.md5.quality.copyright"
msgstr "Lütfen <a %(a_copyright)s>DMCA / Telif hakkı talep formunu</a> kullanın."

msgid "page.md5.quality.describe_the_issue"
msgstr "Sorunu açıklayın (gerekli)"

msgid "page.md5.quality.issue_description"
msgstr "Sorun açıklaması"

msgid "page.md5.quality.better_md5.text1"
msgstr "Bu dosyanın daha iyi bir sürümünün MD5'i (varsa)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Eğer bu dosyayla yakından eşleşen (aynı baskı, mümkünse aynı dosya uzantısı) başka bir dosya varsa ve insanların bu dosya yerine onu kullanması gerekiyorsa, lütfen burayı doldurun. Anna’nın Arşivi dışında bu dosyanın daha iyi bir sürümünü biliyorsanız, lütfen <a %(a_upload)s>yükleyin</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "MD5'i URL'den alabilirsiniz, örneğin"

msgid "page.md5.quality.submit_report"
msgstr "Raporu gönder"

msgid "page.md5.quality.improve_the_metadata"
msgstr "<a %(a_metadata)s>Bu dosyanın üstverisini iyileştirmeyi</a> kendiniz öğrenin."

msgid "page.md5.quality.report_thanks"
msgstr "Raporunuzu gönderdiğiniz için teşekkür ederiz. Bu sayfada gösterilecek ve Anna tarafından manuel olarak incelenecektir (müsait bir inceleme ekibimiz olduğu vakit)."

msgid "page.md5.quality.report_error"
msgstr "Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin."

msgid "page.md5.quality.great.summary"
msgstr "Bu dosya yüksek kalitedeyse, burada hakkında her şeyi tartışabilirsiniz! Değilse, lütfen “Dosya sorunu bildir” düğmesini kullanın."

msgid "page.md5.quality.loved_the_book"
msgstr "Bu kitaba bayıldım!"

msgid "page.md5.quality.submit_comment"
msgstr "Yorum bırak"

msgid "common.english_only"
msgstr "Metin aşağıda İngilizce olarak devam etmektedir."

msgid "page.md5.text.stats.total_downloads"
msgstr "Toplam indirme: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Bir “Dosya MD5”i dosya içeriğinden hesaplanan ve o içeriğe dayalı olarak makul derecede benzersiz olan bir hash'tir. Burada listelediğimiz tüm gölge kütüphaneler, dosyaları tanımlamak için öncelikle MD5'leri kullanır."

msgid "page.md5.text.md5_info.text2"
msgstr "Bir dosya birden fazla gölge kütüphanede görünebilir. Derlediğimiz çeşitli veri setleri hakkında bilgi için <a %(a_datasets)s>Veri Setleri sayfasına</a> bakın."

msgid "page.md5.text.ia_info.text1"
msgstr "Bu, <a %(a_ia)s>IA’nın Kontrollü Dijital Ödünç Verme(CDL)</a> kütüphanesi tarafından yönetilen ve Anna’nın Arşivi tarafından arama için listelenen bir dosyadır. Derlediğimiz çeşitli veri setleri hakkında bilgi için <a %(a_datasets)s>Veri Setleri sayfasına</a> bakın."

msgid "page.md5.text.file_info.text1"
msgstr "Bu özel dosya hakkında bilgi için <a %(a_href)s>JSON dosyasına</a> göz atın."

msgid "page.aarecord_issue.title"
msgstr "🔥 Bu sayfa yüklenirken sorun oluştu"

msgid "page.aarecord_issue.text"
msgstr "Lütfen tekrar denemek için sayfayı yenileyin. Sorun birkaç saat boyunca devam ederse <a %(a_contact)s>bizimle iletişime geçin</a>."

msgid "page.md5.invalid.header"
msgstr "Bulunamadı"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” veritabanımızda bulunamadı."

msgid "page.login.title"
msgstr "Giriş yap / Kaydol"

msgid "page.browserverification.header"
msgstr "Tarayıcı doğrulaması"

msgid "page.login.text1"
msgstr "Spambotların birçok hesap açmasını önlemek için önce tarayıcınızı doğrulamamız gerekmekte."

msgid "page.login.text2"
msgstr "Sonsuz bir döngüye yakalanırsanız, <a %(a_privacypass)s>Privacy Pass</a> yüklemenizi öneririz."

msgid "page.login.text3"
msgstr "Reklam engelleyicilerini ve diğer tarayıcı eklentilerini kapatmak da yardımcı olabilir."

msgid "page.codes.title"
msgstr "Kodlar"

msgid "page.codes.heading"
msgstr "Kod Kaşifi"

#, fuzzy
msgid "page.codes.intro"
msgstr "Kayıtların etiketlendiği kodları, öneklerine göre keşfedin. “Kayıtlar” sütunu, arama motorunda görüldüğü gibi (sadece üstveri kayıtları dahil) verilen ön ekle etiketlenmiş kayıtların sayısını gösterir. “Kodlar” sütunu, verilen ön eke sahip kaç gerçek kod olduğunu gösterir."

msgid "page.codes.why_cloudflare"
msgstr "Bu sayfanın oluşturulması biraz zaman alabilir, bu yüzden bir Cloudflare doğrulaması gerektirir. <a %(a_donate)s>Üyeler</a> doğrulamayı atlayabilir."

msgid "page.codes.dont_scrape"
msgstr "Lütfen bu sayfalardan otomatik olarak veri çekmeyin. Bunun yerine, ElasticSearch ve MariaDB veri tabanlarımızı <a %(a_import)s>oluşturmanızı</a> veya <a %(a_download)s>indirmenizi</a> ve <a %(a_software)s>açık kaynak kodumuzu</a> çalıştırmanızı öneririz. Ham veriler, <a %(a_json_file)s>bu gibi</a> JSON dosyaları aracılığıyla manuel olarak keşfedilebilir."

msgid "page.codes.prefix"
msgstr "Ön ek"

msgid "common.form.go"
msgstr "Git"

msgid "common.form.reset"
msgstr "Sıfırla"

msgid "page.codes.search_archive_start"
msgstr "Anna’nın Arşivi'nde Ara"

msgid "page.codes.bad_unicode"
msgstr "Uyarı: Kodda hatalı Unicode karakterleri var ve çeşitli durumlarda yanlış çalışabilir. Ham ikili veri (raw binary), URL'deki base64 gösterimiyle çözülebilir."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bilinen kod ön eki “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Ön ek"

msgid "page.codes.code_label"
msgstr "Etiket"

msgid "page.codes.code_description"
msgstr "Açıklama"

msgid "page.codes.code_url"
msgstr "Belirli bir kod için URL"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%” kodun değeri ile değiştirilecek"

msgid "page.codes.generic_url"
msgstr "Genel URL"

msgid "page.codes.code_website"
msgstr "Web Sitesi"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "“%(prefix_label)s” ile eşleşen %(count)s kayıt"

msgid "page.codes.url_link"
msgstr "Belirli bir kod için URL: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Daha fazla…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "“%(prefix_label)s” ile başlayan kodlar"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Dizin"

msgid "page.codes.records_prefix"
msgstr "kayıtlar"

msgid "page.codes.records_codes"
msgstr "kodlar"

msgid "page.codes.fewer_than"
msgstr "%(count)s kayıttan az"

msgid "page.contact.dmca.form"
msgstr "DMCA / telif hakkı talepleri için <a %(a_copyright)s>bu formu</a> kullanın."

msgid "page.contact.dmca.delete"
msgstr "Herhangi bir başka yolla bize ulaşmaya çalışırsanız, telif hakkı talepleriniz otomatik olarak silinecektir."

msgid "page.contact.checkboxes.text1"
msgstr "Geri bildiriminizi ve sorularınızı memnuniyetle karşılıyoruz!"

msgid "page.contact.checkboxes.text2"
msgstr "Ancak, aldığımız spam ve anlamsız e-postaların miktarı nedeniyle, bizimle iletişime geçme koşullarını anladığınızı onaylamak için kutuları işaretleyin."

msgid "page.contact.checkboxes.copyright"
msgstr "Bu e-postaya gönderilen telif hakkı talepleri dikkate alınmayacaktır; bunun yerine formu kullanın."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Dost sunucular, sunucu servisinin kapatılması nedeniyle kullanılamıyor. Yakında tekrar aktif olacaklar."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Üyelikler buna göre uzatılacaktır."

msgid "layout.index.footer.dont_email"
msgstr "<a %(a_request)s>Kitap talep etmek</a> veya küçük (<10k) <a %(a_upload)s>yüklemeler</a> için bize e-posta göndermeyin."

msgid "page.donate.please_include"
msgstr "Hesap veya bağış soruları sorarken, hesap kimliğinizi, ekran görüntülerini, makbuzları ve mümkün olduğunca fazla bilgiyi ekleyin. E-postamızı yalnızca 1-2 haftada bir kontrol ediyoruz, bu nedenle bu bilgileri eklememek herhangi bir çözümü geciktirecektir."

msgid "page.contact.checkboxes.show_email_button"
msgstr "E-postayı göster"

msgid "page.copyright.title"
msgstr "DMCA / Telif Hakkı İddia Formu"

msgid "page.copyright.intro"
msgstr "Bir DCMA veya başka bir telif hakkı iddianız varsa, lütfen bu formu mümkün olduğunca kesin bir şekilde doldurun. Herhangi bir sorunla karşılaşırsanız, lütfen özel DMCA adresimizden bizimle iletişime geçin: %(email)s. Bu adrese gönderilen iddiaların işleme alınmayacağını, sadece sorular için olduğunu unutmayın. İddialarınızı göndermek için lütfen aşağıdaki formu kullanın."

msgid "page.copyright.form.aa_urls"
msgstr "Anna’nın Arşivi'ndeki URL'ler (gerekli). Her satıra bir tane. Lütfen yalnızca bir kitabın tam olarak aynı baskısını tanımlayan URL'leri ekleyin. Birden fazla kitap veya birden fazla baskı için iddiada bulunmak istiyorsanız, lütfen bu formu birden fazla kez gönderin."

msgid "page.copyright.form.aa_urls.note"
msgstr "Birden fazla kitabı veya baskıyı bir araya getiren iddialar reddedilecektir."

msgid "page.copyright.form.name"
msgstr "Adınız (gerekli)"

msgid "page.copyright.form.address"
msgstr "Adres (gerekli)"

msgid "page.copyright.form.phone"
msgstr "Telefon numarası (gerekli)"

msgid "page.copyright.form.email"
msgstr "E-posta (gerekli)"

msgid "page.copyright.form.description"
msgstr "Kaynak materyalin net tanımı (gerekli)"

msgid "page.copyright.form.isbns"
msgstr "Kaynak materyalin ISBN'leri (varsa). Her satıra bir tane. Lütfen yalnızca telif hakkı iddiasında bulunduğunuz baskıyla tam olarak eşleşenleri ekleyin."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> kaynak materyal URL'leri, her satıra bir tane. Lütfen kaynak materyalinizi Open Library'de aramak için bir dakikanızı ayırın. Bu, iddianızı doğrulamamıza yardımcı olacaktır."

msgid "page.copyright.form.external_urls"
msgstr "Kaynak materyal URL'leri, her satıra bir tane (gerekli). İddianızı doğrulamamıza yardımcı olmak için mümkün olduğunca çok sayıda ekleyin (örneğin Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Beyan ve imza (gerekli)"

msgid "page.copyright.form.submit_claim"
msgstr "İddia gönder"

msgid "page.copyright.form.on_success"
msgstr "✅ Telif hakkı iddianızı gönderdiğiniz için teşekkür ederiz. En kısa sürede inceleyeceğiz. Başka bir tane göndermek için lütfen sayfayı yeniden yükleyin."

msgid "page.copyright.form.on_failure"
msgstr "❌ Bir şeyler ters gitti. Lütfen sayfayı yeniden yükleyin ve tekrar deneyin."

msgid "page.datasets.title"
msgstr "Veri Setleri"

msgid "page.datasets.common.intro"
msgstr "Bu veri setini <a %(a_archival)s>arşivleme</a> veya <a %(a_llm)s>LLM eğitimi</a> amaçları için yansıtmakla ilgileniyorsanız, lütfen bizimle iletişime geçin."

msgid "page.datasets.intro.text2"
msgstr "Misyonumuz, dünyadaki tüm kitapları (ve makaleleri, dergileri vb.) arşivlemek ve geniş çapta erişilebilir hale getirmektir. Tüm kitapların geniş çapta insanlara sunulması gerektiğine inanıyoruz, böylece yedeklilik ve dayanıklılık sağlanır. Bu nedenle, çeşitli kaynaklardan dosyaları bir araya getiriyoruz. Bazı kaynaklar tamamen açıktır ve toplu olarak yansıtılabilir (örneğin Sci-Hub). Diğerleri kapalı ve korumacıdır, bu yüzden onların kitaplarını “özgürleştirmek” için çalışıyoruz. Diğerleri ise bu ikisinin arasında bir yerde yer alır."

msgid "page.datasets.intro.text3"
msgstr "Tüm verilerimiz <a %(a_torrents)s>torrent</a> olarak indirilebilir ve tüm üstverilerimiz <a %(a_anna_software)s>oluşturulabilir</a> veya <a %(a_elasticsearch)s>ElasticSearch ve MariaDB veri tabanları</a> olarak indirilebilir. Ham veriler, <a %(a_dbrecord)s>bu</a> gibi JSON dosyaları aracılığıyla manuel olarak keşfedilebilir."

msgid "page.datasets.overview.title"
msgstr "Genel Bakış"

msgid "page.datasets.overview.text1"
msgstr "Aşağıda Anna’nın Arşivi'ndeki dosyaların kaynaklarına hızlı bir genel bakış bulunmaktadır."

msgid "page.datasets.overview.source.header"
msgstr "Kaynak"

msgid "page.datasets.overview.size.header"
msgstr "Boyut"

msgid "page.datasets.overview.mirrored.header"
msgstr "AA tarafından yansıtılan %% / mevcut torrentler"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Dosya sayısının yüzdeleri"

msgid "page.datasets.overview.last_updated.header"
msgstr "Son güncelleme"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Kurgu Dışı ve Kurgu"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s dosya"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Libgen.li “scimag” aracılığıyla"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: 2021'den beri dondurulmuş; çoğu torrentler aracılığıyla mevcut"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: o zamandan beri küçük eklemeler</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "“scimag” hariç"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Kurgu torrentleri geride (ancak kimlikler ~4-6M, Zlib torrentlerimizle örtüştükleri için torrentlenmemiş)."

msgid "page.datasets.zlibzh.searchable"
msgstr "Z-Library'deki “Çince” koleksiyonu, farklı MD5'lerle bizim DuXiu koleksiyonumuzla aynı görünüyor. Çoğaltmayı önlemek için bu dosyaları torrentlerden hariç tutuyoruz, ancak yine de arama dizinimizde gösteriyoruz."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrollü Dijital Ödünç Verme"

msgid "page.datasets.iacdl.searchable"
msgstr "Dosyaların %%98'i aranabilir."

msgid "page.datasets.overview.total"
msgstr "Toplam"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Çiftler hariç"

msgid "page.datasets.overview.text4"
msgstr "Gölge kütüphaneler genellikle birbirlerinden veri senkronize ettikleri için, kütüphaneler arasında önemli bir örtüşme vardır. Bu yüzden sayılar tam olarak toplamı vermiyor."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "“Anna’nın Arşivi tarafından yansıtılan ve tohumlanan” yüzdesi, kaç dosyayı kendimiz yansıttığımızı gösterir. Bu dosyaları toplu olarak torrentler aracılığıyla tohumluyoruz ve doğrudan indirme için ortak web siteleri aracılığıyla erişilebilir hale getiriyoruz."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Kaynak kütüphaneler"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Bazı kaynak kütüphaneler, verilerini torrentler aracılığıyla toplu olarak paylaşmayı teşvik ederken, diğerleri koleksiyonlarını kolayca paylaşmaz. İkinci durumda, Anna’nın Arşivi koleksiyonlarını kazımaya ve erişilebilir hale getirmeye çalışır (bkz. <a %(a_torrents)s>Torrentler</a> sayfamız). Ayrıca, kaynak kütüphanelerin paylaşmaya istekli olduğu ancak bunu yapacak kaynaklara sahip olmadığı durumlar da vardır. Bu durumlarda da yardımcı olmaya çalışıyoruz."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Aşağıda, farklı kaynak kütüphanelerle nasıl etkileşimde bulunduğumuza dair bir genel bakış bulunmaktadır."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Kaynak"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Dosyalar"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Günlük <a %(dbdumps)s>HTTP veritabanı dökümleri</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s <a %(nonfiction)s>Kurgu Dışı</a> ve <a %(fiction)s>Kurgu</a> için otomatik torrentler"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna’nın Arşivi bir <a %(covers)s>kitap kapağı torrentleri</a> koleksiyonunu yönetir"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub, 2021'den beri yeni dosyaları dondurdu."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Üstveri dökümleri <a %(scihub1)s>burada</a> ve <a %(scihub2)s>burada</a> mevcuttur, ayrıca <a %(libgenli)s>Libgen.li veritabanı</a>nın bir parçası olarak (bizim kullandığımız)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Veri torrentlerine <a %(scihub1)s>buradan</a>, <a %(scihub2)s>buradan</a> ve <a %(libgenli)s>buradan</a> ulaşabilirsiniz."

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Libgen’in “scimag”ine bazı yeni dosyalar <a %(libgenrs)s>ekleniyor</a>, ancak yeni torrentler için yeterli değil"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Üç aylık <a %(dbdumps)s>HTTP veritabanı dökümleri</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Kurgu Dışı torrentler Libgen.rs ile paylaşılıyor (ve <a %(libgenli)s>ile kopyalanıyor</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna'nın Arşivi ve Libgen.li, <a %(comics)s>çizgi romanlar</a>, <a %(magazines)s>dergiler</a>, <a %(standarts)s>standart belgeler</a> ve <a %(fiction)s>kurgu (Libgen.rs'den ayrılmış)</a> koleksiyonlarını işbirliği içinde yönetir."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Onların “fiction_rus” koleksiyonu (Rus kurgusu) için özel torrentler yoktur, ancak başkalarının torrentleriyle kapsanır ve biz bir <a %(fiction_rus)s>yansıtma</a> tutarız."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna’nın Arşivi ve Z-Library, <a %(metadata)s>Z-Library üstveri</a> ve <a %(files)s>Z-Library dosyaları</a> koleksiyonunu ortaklaşa yönetiyor"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s <a %(openlib)s>Open Library veritabanı dökümleri</a> aracılığıyla bazı üstveriler mevcut, ancak bunlar tüm IA koleksiyonunu kapsamıyor"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Tüm koleksiyonları için kolayca erişilebilir üstveri dökümleri mevcut değil"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna’nın Arşivi bir <a %(ia)s>IA üstveri</a> koleksiyonunu yönetiyor"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Sadece sınırlı bir süre için ödünç alınabilir dosyalar, çeşitli erişim kısıtlamaları ile"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna’nın Arşivi bir <a %(ia)s>IA dosyaları</a> koleksiyonunu yönetiyor"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Çin internetinde dağılmış çeşitli üstveri veritabanları; ancak genellikle ücretli veritabanları"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Tüm koleksiyonları için kolayca erişilebilir üstveri dökümleri mevcut değil."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’nın Arşivi bir <a %(duxiu)s>DuXiu üstveri</a> koleksiyonunu yönetir"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Çin internetinde dağılmış çeşitli dosya veritabanları; ancak genellikle ücretli veritabanları"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Çoğu dosya yalnızca premium BaiduYun hesapları kullanılarak erişilebilir; yavaş indirme hızları."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’nın Arşivi, <a %(duxiu)s>DuXiu dosyaları</a> koleksiyonunu yönetiyor"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Çeşitli daha küçük veya tek seferlik kaynaklar. İnsanları önce diğer gölge kütüphanelere yükleme yapmaya teşvik ediyoruz, ancak bazen insanların diğerlerinin ayıklaması için çok büyük, ancak kendi kategorilerini oluşturacak kadar büyük olmayan koleksiyonları oluyor."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Sadece üstveri kaynakları"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Koleksiyonumuzu, ISBN numaraları veya diğer alanları kullanarak dosyalarla eşleştirebileceğimiz sadece üstveri kaynaklarıyla da zenginleştiriyoruz. Aşağıda bu kaynakların bir genel bakışı bulunmaktadır. Yine, bu kaynakların bazıları tamamen açıkken, diğerlerini kazımak zorundayız."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Üstveri toplama ilhamımız, Aaron Swartz’ın “yayınlanmış her kitap için bir web sayfası” hedefidir, bunun için <a %(a_openlib)s>Open Library</a>'yi oluşturdu. Bu proje iyi ilerledi, ancak benzersiz konumumuz, onların elde edemediği üstverileri elde etmemizi sağlıyor. Diğer bir ilham kaynağımız ise dünyada <a %(a_blog)s>kaç kitap olduğunu</a> bilme arzumuzdu, böylece kurtarmamız gereken kaç kitap kaldığını hesaplayabiliriz."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Üstveri aramasında, orijinal kayıtları gösterdiğimizi unutmayın. Kayıtları birleştirme yapmıyoruz."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Son güncelleme"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Aylık <a %(dbdumps)s>veritabanı dökümleri</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Toplu olarak doğrudan mevcut değil, kazımaya karşı korumalı"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’nın Arşivi bir <a %(worldcat)s>OCLC (WorldCat) üstveri</a> koleksiyonunu yönetir"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Birleştirilmiş veritabanı"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Yukarıdaki tüm kaynakları birleştirerek bu web sitesine hizmet etmek için kullandığımız birleştirilmiş bir veritabanı oluşturuyoruz. Bu birleştirilmiş veritabanı doğrudan erişilebilir değildir, ancak Anna’nın Arşivi tamamen açık kaynak olduğundan, ElasticSearch ve MariaDB veritabanları olarak oldukça kolay bir şekilde <a %(a_generated)s>oluşturulabilir</a> veya <a %(a_downloaded)s>indirilebilir</a>. Bu sayfadaki betikler, yukarıda belirtilen kaynaklardan gerekli tüm üstverileri otomatik olarak indirecektir."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Bu betikleri yerel olarak çalıştırmadan önce verilerimizi keşfetmek isterseniz, diğer JSON dosyalarına bağlantı veren JSON dosyalarımıza bakabilirsiniz. <a %(a_json)s>Bu dosya</a> iyi bir başlangıç noktasıdır."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "<a %(a_href)s>Blog yazımızdan</a> uyarlanmıştır."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a>, <a %(superstar_link)s>SuperStar Dijital Kütüphane Grubu</a> tarafından oluşturulan taranmış kitapların devasa bir veritabanıdır. Çoğu akademik kitap olup, üniversiteler ve kütüphaneler için dijital olarak erişilebilir hale getirilmek üzere taranmıştır. İngilizce konuşan izleyicilerimiz için, <a %(princeton_link)s>Princeton</a> ve <a %(uw_link)s>Washington Üniversitesi</a> iyi genel bakışlar sunmaktadır. Ayrıca daha fazla arka plan bilgisi veren mükemmel bir makale de bulunmaktadır: <a %(article_link)s>“Çin Kitaplarını Dijitalleştirme: SuperStar DuXiu Bilimsel Arama Motoru Üzerine Bir Vaka Çalışması”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Duxiu'dan gelen kitaplar uzun süredir Çin internetinde korsan olarak dağıtılmaktadır. Genellikle satıcılar tarafından bir dolardan daha az bir fiyata satılmaktadırlar. Genellikle Google Drive'ın Çin eşdeğeri kullanılarak dağıtılmakta olup, daha fazla depolama alanı sağlamak için sık sık hacklenmiştir. Bazı teknik detaylar <a %(link1)s>burada</a> ve <a %(link2)s>burada</a> bulunabilir."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Kitaplar yarı kamuya açık olarak dağıtılmış olsa da, toplu olarak elde etmek oldukça zordur. Bu, yapılacaklar listemizde yüksek bir önceliğe sahipti ve tam zamanlı olarak birkaç ay ayırdık. Ancak, 2023'ün sonlarında inanılmaz, harika ve yetenekli bir gönüllü bize ulaştı ve bu işi zaten büyük bir maliyetle yaptığını söyledi. Bize tam koleksiyonu paylaştı, karşılığında sadece uzun vadeli koruma garantisi istedi. Gerçekten olağanüstü."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Kaynaklar"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Toplam dosya sayısı: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Toplam dosya boyutu: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Anna’nın Arşivi tarafından yansıtılan dosyalar: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Son güncelleme: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Anna’nın Arşivi tarafından Torrents"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Anna’nın Arşivinde örnek kayıt"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Bu veri hakkında blog yazımız"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Üstveri içe aktarma betikleri"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’nın Arşiv Konteynerleri formatı"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Gönüllülerimizden daha fazla bilgi (ham notlar):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Kontrollü Dijital Ödünç Verme"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Bu veri seti <a %(a_datasets_openlib)s>Open Library veri seti</a> ile yakından ilişkilidir. IA’nın Kontrollü Dijital Ödünç Verme Kütüphanesi'nden tüm üstveri ve dosyaların büyük bir kısmının kazınmasını içerir. Güncellemeler <a %(a_aac)s>Anna’nın Arşivi Konteyner formatında</a> yayınlanır."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Bu kayıtlar doğrudan Open Library veri setinden alınmaktadır, ancak Open Library'de bulunmayan kayıtları da içermektedir. Ayrıca, yıllar içinde topluluk üyeleri tarafından kazınan bir dizi veri dosyamız da bulunmaktadır."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Koleksiyon iki bölümden oluşmaktadır. Tüm verileri almak için her iki bölüme de ihtiyacınız var (torrents sayfasında üstü çizili olan eski torrentler hariç)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "ilk sürümümüz, <a %(a_aac)s>Anna’nın Arşiv Konteynerleri (AAC) formatı</a> üzerinde standartlaşmadan önce. Üstveri (json ve xml olarak), pdfler (acsm ve lcpdf dijital ödünç verme sistemlerinden) ve kapak küçük resimlerini içerir."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "AAC kullanılarak artımlı yeni sürümler. Sadece 2023-01-01 tarihinden sonraki zaman damgalarıyla üstveri içerir, çünkü geri kalan zaten “ia” tarafından kapsanmıştır. Ayrıca bu sefer acsm ve “bookreader” (IA’nın web okuyucusu) ödünç verme sistemlerinden tüm pdf dosyaları. İsmin tam olarak doğru olmamasına rağmen, bookreader dosyalarını ia2_acsmpdf_files koleksiyonuna eklemeye devam ediyoruz, çünkü bunlar birbirini dışlar."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Ana %(source)s web sitesi"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Dijital Ödünç Verme Kütüphanesi"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Üstveri dokümantasyonu (çoğu alan)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN ülke bilgisi"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Uluslararası ISBN Ajansı, ulusal ISBN ajanslarına tahsis ettiği aralıkları düzenli olarak yayınlar. Bu sayede bu ISBN'nin hangi ülkeye, bölgeye veya dil grubuna ait olduğunu belirleyebiliriz. Şu anda bu veriyi dolaylı olarak, <a %(a_isbnlib)s>isbnlib</a> Python kütüphanesi aracılığıyla kullanıyoruz."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Kaynaklar"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Son güncelleme: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN web sitesi"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Üstveri"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Farklı Library Genesis çatalları hakkında arka plan bilgisi için, <a %(a_libgen_rs)s>Libgen.rs</a> sayfasına bakın."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li, Libgen.rs ile aynı içerik ve üstverinin çoğunu içerir, ancak bunun üzerine bazı koleksiyonlar eklenmiştir, yani çizgi romanlar, dergiler ve standart belgeler. Ayrıca <a %(a_scihub)s>Sci-Hub</a>'ı üstveri ve arama motoruna entegre etmiştir, bu da veritabanımız için kullandığımız şeydir."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Bu kütüphanenin üstverisi <a %(a_libgen_li)s>libgen.li'de</a> ücretsiz olarak mevcuttur. Ancak, bu sunucu yavaştır ve kesilen bağlantıları devam ettirmeyi desteklemez. Aynı dosyalar <a %(a_ftp)s>bir FTP sunucusunda</a> da mevcuttur, bu daha iyi çalışır."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Ek içeriğin çoğu için torrentler mevcuttur, özellikle çizgi romanlar, dergiler ve standart belgeler için torrentler Anna’nın Arşivi ile işbirliği içinde yayınlanmıştır."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Kurgusal koleksiyonun kendi torrentleri vardır (<a %(a_href)s>Libgen.rs</a>'den farklı) ve %(start)s tarihinde başlamaktadır."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Libgen.li yöneticisine göre, “fiction_rus” (Rus kurgusu) koleksiyonu, düzenli olarak yayınlanan <a %(a_booktracker)s>booktracker.org</a> torrentleri, özellikle <a %(a_flibusta)s>flibusta</a> ve <a %(a_librusec)s>lib.rus.ec</a> torrentleri ile kapsanmalıdır (biz bunları <a %(a_torrents)s>burada</a> yansıtıyoruz, ancak hangi torrentlerin hangi dosyalara karşılık geldiğini henüz belirlemedik)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Tüm koleksiyonların istatistikleri <a %(a_href)s>libgen'in web sitesinde</a> bulunabilir."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Kurgu dışı kitaplar da farklılaşmış görünüyor, ancak yeni torrentler olmadan. Bu durumun 2022'nin başlarından beri meydana geldiği anlaşılıyor, ancak bunu doğrulamadık."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Torrentleri olmayan belirli aralıklar (örneğin, f_3463000'den f_4260000'e kadar olan kurgu aralıkları) muhtemelen Z-Library (veya diğer kopya) dosyalarıdır, ancak bu aralıklardaki lgli-özel dosyalar için bazı kopya kaldırma işlemleri yapıp torrentler oluşturmak isteyebiliriz."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "“libgen.is”e atıfta bulunan torrent dosyalarının açıkça <a %(a_libgen)s>Libgen.rs</a>’nin aynaları olduğunu unutmayın (“.is” Libgen.rs tarafından kullanılan farklı bir alan adıdır)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Üstveriyi kullanmada yardımcı bir kaynak <a %(a_href)s>bu sayfadır</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Anna’nın Arşivi’nde kurgu torrentleri"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Anna’nın Arşivi’nde çizgi roman torrentleri"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Anna’nın Arşivi’nde dergi torrentleri"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Anna’nın Arşivi'nde standart belge torrentleri"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Anna’nın Arşivi'nde Rus kurgusu torrentleri"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Üstveri"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "FTP üzerinden üstveri"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Üstveri alan bilgisi"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Diğer torrentlerin aynası (ve benzersiz kurgu ve çizgi roman torrentleri)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Tartışma forumu"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Çizgi romanların yayınlanması hakkındaki blog yazımız"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Farklı Library Genesis (veya “Libgen”) çatallarının kısa hikayesi, zamanla Library Genesis ile ilgilenen farklı kişilerin anlaşmazlığa düşmesi ve yollarını ayırmasıdır."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "“.fun” versiyonu, orijinal kurucu tarafından oluşturuldu. Daha dağıtılmış yeni bir versiyon lehine yeniden düzenleniyor."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "“.rs” versiyonu çok benzer verilere sahiptir ve koleksiyonlarını toplu torrentler halinde en tutarlı şekilde yayınlar. Kabaca “kurgu” ve “kurgu dışı” bölümlerine ayrılmıştır."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "İlk olarak “http://gen.lib.rus.ec” adresinde yer alıyordu."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>“.li” versiyonu</a> büyük bir çizgi roman koleksiyonuna ve henüz torrentler aracılığıyla toplu indirme için mevcut olmayan diğer içeriklere sahiptir. Ayrı bir kurgu kitapları torrent koleksiyonuna sahiptir ve veritabanında <a %(a_scihub)s>Sci-Hub</a>’ın üstverisini içerir."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Bu <a %(a_mhut)s>forum gönderisine</a> göre, Libgen.li başlangıçta “http://free-books.dontexist.com” adresinde barındırılıyordu."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> bir anlamda Library Genesis’in bir çatalıdır, ancak projeleri için farklı bir isim kullanmışlardır."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Bu sayfa “.rs” versiyonu hakkındadır. Hem üstverisini hem de kitap kataloğunun tam içeriğini tutarlı bir şekilde yayınlamasıyla bilinir. Kitap koleksiyonu kurgu ve kurgu dışı bölümlerine ayrılmıştır."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Üstveriyi kullanmada yardımcı bir kaynak <a %(a_metadata)s>bu sayfadır</a> (IP aralıklarını engeller, VPN gerekebilir)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "2024-03 itibarıyla, yeni torrentler <a %(a_href)s>bu forum başlığında</a> yayınlanmaktadır (IP aralıklarını engeller, VPN gerekebilir)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Anna’nın Arşivi'nde Kurgu Dışı torrentler"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Anna’nın Arşivi'nde Kurgu torrentler"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Üstveri"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs üstveri alan bilgisi"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Kurgu Dışı torrentler"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Kurgu torrentler"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Tartışma forumu"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Anna’nın Arşivi tarafından torrentler (kitap kapakları)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Kitap kapakları sürümü hakkında blogumuz"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis, verilerini toplu olarak torrentler aracılığıyla cömertçe sunmasıyla bilinir. Libgen koleksiyonumuz, doğrudan yayınlamadıkları yardımcı verilerden oluşur ve onlarla ortaklık içinde sunulmaktadır. Library Genesis ile çalışan herkese bizimle işbirliği yaptıkları için çok teşekkür ederiz!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Sürüm 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Bu <a %(blog_post)s>ilk sürüm</a> oldukça küçük: Libgen.rs çatalından yaklaşık 300GB kitap kapağı, hem kurgu hem de kurgu dışı. Bunlar, libgen.rs'de göründükleri şekilde organize edilmiştir, örneğin:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "Kurgu dışı bir kitap için %(example)s."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "Kurgu bir kitap için %(example)s."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Z-Library koleksiyonunda olduğu gibi, hepsini büyük bir .tar dosyasına koyduk, eğer dosyaları doğrudan sunmak isterseniz <a %(a_ratarmount)s>ratarmount</a> kullanarak monte edebilirsiniz."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a>, dünya çapındaki kütüphanelerden üstveri kayıtlarını toplayan kar amacı gütmeyen <a %(a_oclc)s>OCLC</a> tarafından oluşturulmuş özel bir veritabanıdır. Muhtemelen dünyanın en büyük kütüphane üstveri koleksiyonudur."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Ekim 2023, ilk sürüm:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Ekim 2023'te OCLC (WorldCat) veritabanının kapsamlı bir taramasını <a %(a_scrape)s>yayınladık</a>, <a %(a_aac)s>Anna’nın Arşivi Konteynerleri formatında</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Anna’nın Arşivi'ndeki Torrentler"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Bu veri hakkında blog yazımız"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library, Internet Archive tarafından dünyanın her kitabını kataloglamak için başlatılan açık kaynaklı bir projedir. Dünyanın en büyük kitap tarama operasyonlarından birine sahiptir ve dijital ödünç verme için birçok kitabı mevcuttur. Kitap üstveri kataloğu ücretsiz olarak indirilebilir ve Anna’nın Arşivi'nde yer almaktadır (ancak şu anda aramada mevcut değildir, yalnızca bir Open Library ID'si ararsanız hariç)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Üstveri"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Sürüm 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Bu, Eylül 2022 boyunca isbndb.com'a yapılan birçok çağrının bir dökümüdür. Tüm ISBN aralıklarını kapsamaya çalıştık. Bu yaklaşık 30.9 milyon kayıttır. Web sitelerinde aslında 32.6 milyon kayıtları olduğunu iddia ediyorlar, bu yüzden bir şekilde bazılarını kaçırmış olabiliriz veya <em>onlar</em> bir şeyleri yanlış yapıyor olabilir."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON yanıtları, sunucularından neredeyse ham olarak gelmektedir. Fark ettiğimiz bir veri kalitesi sorunu, “978-” ile başlamayan ISBN-13 numaraları için, hala “isbn” alanını içermeleri ve bu alanın sadece ilk 3 numaranın kesilip (ve kontrol rakamının yeniden hesaplanarak) ISBN-13 numarası olmasıdır. Bu açıkça yanlıştır, ancak onların bu şekilde yaptıkları görünüyor, bu yüzden değiştirmedik."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Karşılaşabileceğiniz bir diğer potansiyel sorun, “isbn13” alanının kopyaları olmasıdır, bu yüzden bir veritabanında birincil anahtar olarak kullanamazsınız. “isbn13”+“isbn” alanları birleştirildiğinde benzersiz görünmektedir."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Sci-Hub hakkında daha fazla bilgi için <a %(a_scihub)s>resmi web sitesine</a>, <a %(a_wikipedia)s>Wikipedia sayfasına</a> ve bu <a %(a_radiolab)s>podcast röportajına</a> başvurabilirsiniz."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Sci-Hub'ın <a %(a_reddit)s>2021'den beri dondurulduğunu</a> unutmayın. Daha önce de dondurulmuştu, ancak 2021'de birkaç milyon makale eklendi. Yine de, Libgen'in “scimag” koleksiyonlarına sınırlı sayıda makale ekleniyor, ancak yeni toplu torrentleri haklı çıkaracak kadar değil."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Sci-Hub üstverisini, <a %(a_libgen_li)s>Libgen.li</a> tarafından sağlanan “scimag” koleksiyonunda kullanıyoruz. Ayrıca <a %(a_dois)s>dois-2022-02-12.7z</a> veri setini de kullanıyoruz."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "“smarch” torrentlerinin <a %(a_smarch)s>kullanımdan kaldırıldığını</a> ve bu nedenle torrent listemize dahil edilmediğini unutmayın."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Anna’nın Arşivi'ndeki Torrentler"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Üstveri ve torrentler"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Libgen.rs'deki Torrentler"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Libgen.li'deki Torrentler"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Reddit'teki Güncellemeler"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia sayfası"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast röportajı"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Anna’nın Arşivi'ne yüklemeler"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Genel Bakış <a %(a1)s>veri setleri sayfası</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Çeşitli daha küçük veya tek seferlik kaynaklar. İnsanları önce diğer gölge kütüphanelere yükleme yapmaya teşvik ediyoruz, ancak bazen insanların diğerlerinin ayıklaması için çok büyük, ancak kendi kategorilerini oluşturacak kadar büyük olmayan koleksiyonları oluyor."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "“Yükleme” koleksiyonu, AACID'ler ve torrent adlarında belirtilen daha küçük alt koleksiyonlara bölünmüştür. Tüm alt koleksiyonlar önce ana koleksiyona karşı yinelenmiştir, ancak üstveri “upload_records” JSON dosyaları hala orijinal dosyalara birçok referans içermektedir. Çoğu alt koleksiyondan kitap dışı dosyalar da kaldırılmıştır ve genellikle “upload_records” JSON'da <em>not</em> belirtilmemiştir."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Birçok alt koleksiyon, kendileri alt-alt koleksiyonlardan (örneğin, farklı orijinal kaynaklardan) oluşur ve bunlar “dosya yolu” alanlarında dizinler olarak temsil edilir."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Alt koleksiyonlar şunlardır:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Alt Koleksiyon"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notlar"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "göz at"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "ara"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "<a %(a_href)s>aaaaarg.fail</a> adresinden. Oldukça eksiksiz görünüyor. Gönüllümüz “cgiym” tarafından sağlanmıştır."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "<a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentinden. Mevcut makale koleksiyonlarıyla oldukça yüksek bir örtüşme var, ancak çok az MD5 eşleşmesi var, bu yüzden tamamen tutmaya karar verdik."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Gönüllü <q>j</q> tarafından <q>iRead eBooks</q> (fonetik olarak <q>ai rit i-books</q>; airitibooks.com) taraması. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>airitibooks</q> metadata'sına karşılık gelir."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Bir koleksiyondan <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Kısmen orijinal kaynaktan, kısmen the-eye.eu'dan, kısmen diğer aynalardan."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Özel bir kitap torrent sitesi olan <a %(a_href)s>Bibliotik</a>’ten (genellikle “Bib” olarak anılır), kitaplar isimlerine göre (A.torrent, B.torrent) torrentlere paketlenmiş ve the-eye.eu üzerinden dağıtılmıştır."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Gönüllümüz “bpb9v” tarafından. <a %(a_href)s>CADAL</a> hakkında daha fazla bilgi için <a %(a_duxiu)s>DuXiu veri seti sayfamızdaki</a> notlara bakın."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Gönüllümüz “bpb9v”den daha fazlası, çoğunlukla DuXiu dosyaları, ayrıca “WenQu” ve “SuperStar_Journals” klasörleri (SuperStar, DuXiu’nun arkasındaki şirkettir)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Gönüllümüz “cgiym” tarafından, çeşitli kaynaklardan (alt dizinler olarak temsil edilen) Çin metinleri, <a %(a_href)s>China Machine Press</a> (önemli bir Çinli yayıncı) dahil."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Gönüllümüz “cgiym”den Çin dışı koleksiyonlar (alt dizinler olarak temsil edilen)."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Gönüllü <q>cm</q> tarafından Çin mimarisi hakkında kitapların taraması: <q>Yayın evindeki bir ağ açığını kullanarak elde ettim, ancak o açık artık kapatıldı</q>. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>chinese_architecture</q> metadata'sına karşılık gelir."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Akademik yayın evi <a %(a_href)s>De Gruyter</a>’den kitaplar, birkaç büyük torrentten toplanmıştır."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "<a %(a_href)s>docer.pl</a>’nin kazınması, kitaplar ve diğer yazılı eserler üzerine odaklanan bir Polonya dosya paylaşım sitesi. 2023’ün sonlarında gönüllü “p” tarafından kazınmıştır. Orijinal siteden iyi üstverimiz yok (dosya uzantıları bile yok), ancak kitap benzeri dosyaları filtreledik ve genellikle üstveriyi dosyaların kendisinden çıkarabildik."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubları, doğrudan DuXiu'dan, gönüllü “w” tarafından toplanmıştır. Yalnızca son DuXiu kitapları doğrudan e-kitaplar aracılığıyla mevcuttur, bu yüzden bunların çoğu yeni olmalıdır."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Gönüllü “m”den kalan DuXiu dosyaları, DuXiu’nun özel PDG formatında olmayanlar (ana <a %(a_href)s>DuXiu veri seti</a>). Ne yazık ki, bu kaynakları dosya yolunda korumadan birçok orijinal kaynaktan toplanmıştır."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Gönüllü <q>do no harm</q> tarafından erotik kitapların taraması. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>hentai</q> metadata'sına karşılık gelir."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Bir Japon Manga yayıncısından gönüllü “t” tarafından kazınan koleksiyon."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Longquan’ın seçilmiş yargı arşivleri</a>, gönüllü “c” tarafından sağlanmıştır."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "<a %(a_href)s>magzdb.org</a>’nin kazınması, Library Genesis’in bir müttefiki (libgen.rs ana sayfasında bağlantılıdır) ancak dosyalarını doğrudan sağlamak istememiştir. 2023’ün sonlarında gönüllü “p” tarafından elde edilmiştir."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Çeşitli küçük yüklemeler, kendi alt koleksiyonları olarak çok küçük, ancak dizinler olarak temsil edilmiştir."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Rus dosya paylaşım sitesi AvaxHome'dan e-kitaplar."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Gazete ve dergi arşivi. <a %(a1)s><q>Diğer metadata taramaları</q></a> içindeki <q>newsarch_magz</q> metadata'sına karşılık gelir."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Felsefe Dokümantasyon Merkezi</a> taraması."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Gönüllü “o” tarafından orijinal yayın (“sahne”) web sitelerinden doğrudan toplanan Polonya kitapları koleksiyonu."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "<a %(a_href)s>shuge.org</a> sitesinin gönüllüler “cgiym” ve “woz9ts” tarafından birleştirilmiş koleksiyonları."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Trantor İmparatorluk Kütüphanesi”</a> (kurgusal kütüphaneden esinlenerek adlandırılmış), 2022 yılında gönüllü “t” tarafından kazınmıştır."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Gönüllü “woz9ts”den alt-alt koleksiyonlar (dizinler olarak temsil edilen): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (Tayvan’da <a %(a_sikuquanshu)s>Dizhi(迪志)</a> tarafından), mebook (mebook.cc, 我的小书屋, benim küçük kitap odam — woz9ts: “Bu site, bazıları sahibi tarafından kendisi tarafından dizilmiş yüksek kaliteli e-kitap dosyalarını paylaşmaya odaklanıyor. Sahibi 2019’da <a %(a_arrested)s>tutuklandı</a> ve biri onun paylaştığı dosyaların bir koleksiyonunu yaptı.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Gönüllü “woz9ts” tarafından sağlanan ve DuXiu özel PDG formatında olmayan kalan DuXiu dosyaları (hala PDF'e dönüştürülmesi gerekiyor)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Anna’nın Arşivi tarafından Torrents"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Kütüphane kazıması"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Kütüphane, <a %(a_href)s>Library Genesis</a> topluluğunda kök salmıştır ve başlangıçta verileriyle başlatılmıştır. O zamandan beri oldukça profesyonelleşmiş ve çok daha modern bir arayüze sahip olmuştur. Bu nedenle, hem web sitelerini geliştirmeye devam etmek için maddi olarak hem de yeni kitap bağışları alarak çok daha fazla bağış alabilmektedirler. Library Genesis’e ek olarak büyük bir koleksiyon oluşturmuşlardır."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Şubat 2023 itibarıyla güncelleme."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "2022’nin sonlarında, Z-Kütüphane’nin iddia edilen kurucuları tutuklandı ve alan adları Amerika Birleşik Devletleri yetkilileri tarafından ele geçirildi. O zamandan beri web sitesi yavaş yavaş çevrimiçi hale gelmeye başladı. Şu anda kimin işlettiği bilinmiyor."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Koleksiyon üç bölümden oluşmaktadır. İlk iki bölümün orijinal açıklama sayfaları aşağıda korunmuştur. Tüm verileri almak için üç bölüme de ihtiyacınız var (torrents sayfasında üstü çizili olanlar hariç)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: ilk yayınımız. Bu, o zamanlar “Korsan Kütüphane Aynası” (“pilimi”) olarak adlandırılan şeyin ilk yayınıydı."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: ikinci sürüm, bu sefer tüm dosyalar .tar dosyalarına sarılmış."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: Z-Library ekibiyle işbirliği içinde şimdi yayınlanan <a %(a_href)s>Anna’nın Arşiv Konteynerleri (AAC) formatı</a> kullanılarak artımlı yeni sürümler."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Anna’nın Arşivi tarafından Torrents (üstveri + içerik)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Anna’nın Arşivi'nde örnek kayıt (orijinal koleksiyon)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Anna’nın Arşivi'nde örnek kayıt (“zlib3” koleksiyonu)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Ana web sitesi"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor alan adı"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Sürüm 1 hakkında blog yazısı"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Sürüm 2 hakkında blog yazısı"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib sürümleri (orijinal açıklama sayfaları)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Sürüm 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "İlk ayna, 2021 ve 2022 yılları boyunca titizlikle elde edildi. Bu noktada biraz güncelliğini yitirmiş durumda: koleksiyonun Haziran 2021'deki durumunu yansıtıyor. Bunu gelecekte güncelleyeceğiz. Şu anda bu ilk sürümü çıkarmaya odaklanmış durumdayız."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Library Genesis zaten kamuya açık torrentlerle korunduğu ve Z-Library'e dahil edildiği için, Haziran 2022'de Library Genesis'e karşı temel bir yinelenen dosya temizliği yaptık. Bunun için MD5 hashlerini kullandık. Kütüphanede muhtemelen aynı kitapla birden fazla dosya formatı gibi daha fazla yinelenen içerik var. Bu, doğru bir şekilde tespit edilmesi zor, bu yüzden yapmıyoruz. Yinelenen dosya temizliğinden sonra, toplamda 7TB'ın biraz altında, 2 milyondan fazla dosya kaldı."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Koleksiyon iki bölümden oluşur: üstverinin MySQL “.sql.gz” dökümü ve her biri yaklaşık 50-100GB olan 72 torrent dosyası. Üstveri, Z-Library web sitesi tarafından bildirilen verileri (başlık, yazar, açıklama, dosya türü) içerir, ayrıca gözlemlediğimiz gerçek dosya boyutu ve md5sum'u da içerir, çünkü bazen bunlar uyuşmaz. Z-Library'nin kendisinin yanlış üstveriye sahip olduğu dosya aralıkları var gibi görünüyor. Ayrıca bazı izole durumlarda yanlış indirilen dosyalarımız olabilir, bunları tespit edip gelecekte düzeltmeye çalışacağız."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Büyük torrent dosyaları, dosya adı olarak Z-Library ID'si ile gerçek kitap verilerini içerir. Dosya uzantıları, üstveri dökümü kullanılarak yeniden oluşturulabilir."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Koleksiyon, kurgusal olmayan ve kurgusal içeriklerin bir karışımıdır (Library Genesis'teki gibi ayrılmamıştır). Kalite de geniş ölçüde değişkenlik göstermektedir."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Bu ilk sürüm artık tamamen kullanılabilir durumda. Torrent dosyalarının yalnızca Tor aynamız üzerinden erişilebilir olduğunu unutmayın."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Sürüm 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Son aynadan Ağustos 2022'ye kadar Z-Library'ye eklenen tüm kitapları aldık. Ayrıca, ilk seferde kaçırdığımız bazı kitapları da geri dönüp kazıdık. Sonuç olarak, bu yeni koleksiyon yaklaşık 24TB boyutunda. Yine, bu koleksiyon Library Genesis'e karşı yinelenmiş durumda, çünkü bu koleksiyon için zaten torrentler mevcut."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Veriler, ilk sürüme benzer şekilde organize edilmiştir. Üstverinin MySQL “.sql.gz” dökümü bulunmaktadır ve bu döküm, ilk sürümden tüm üstveriyi de içerir, böylece onu geçersiz kılar. Ayrıca bazı yeni sütunlar ekledik:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: Bu dosyanın, kurgusal olmayan veya kurgu koleksiyonunda (md5 ile eşleştirilmiş) Library Genesis'te zaten bulunup bulunmadığı."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: bu dosyanın hangi torrentte olduğunu belirtir."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: kitabı indiremeyince ayarlanır."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Bunu geçen sefer de belirtmiştik, ancak açıklığa kavuşturmak için: “filename” ve “md5” dosyanın gerçek özellikleridir, oysa “filename_reported” ve “md5_reported” Z-Library'den kazıdıklarımızdır. Bazen bu ikisi birbiriyle uyuşmaz, bu yüzden her ikisini de dahil ettik."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Bu sürüm için, daha eski MySQL sürümleriyle uyumlu olması gereken “utf8mb4_unicode_ci” sıralamasını değiştirdik."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Veri dosyaları geçen seferkine benzer, ancak çok daha büyük. Daha küçük torrent dosyaları oluşturmakla uğraşamadık. “pilimi-zlib2-0-14679999-extra.torrent” son sürümde kaçırdığımız tüm dosyaları içerirken, diğer torrentler tamamen yeni ID aralıklarıdır. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Güncelleme %(date)s:</strong> Çoğu torrentimizi çok büyük yaptık, bu da torrent istemcilerinin zorlanmasına neden oldu. Onları kaldırdık ve yeni torrentler yayınladık."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Güncelleme %(date)s:</strong> Hâlâ çok fazla dosya vardı, bu yüzden onları tar dosyalarına sardık ve yeni torrentleri tekrar yayınladık."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Sürüm 2 ek bölümü (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Bu tek bir ekstra torrent dosyasıdır. Yeni bir bilgi içermez, ancak hesaplanması zaman alabilecek bazı veriler içerir. Bu, sahip olmayı uygun hale getirir çünkü bu torrentin indirilmesi genellikle sıfırdan hesaplamaktan daha hızlıdır. Özellikle, <a %(a_href)s>ratarmount</a> ile kullanım için tar dosyaları için SQLite dizinleri içerir."

#, fuzzy
msgid "page.faq.title"
msgstr "Sıkça Sorulan Sorular (SSS)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Anna’nın Arşivi nedir?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna'nın Arşivi</span> iki amacı olan kâr amacı gütmeyen bir projedir:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Saklama:</strong> İnsanlığım tüm bilgi ve kültürünü yedekleme.</li><li><strong>Erişim:</strong> Bu bilgiyi ve kültürü dünyadaki herkese erişilebilir kılma.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tüm <a %(a_code)s>kodlarımız</a> ve <a %(a_datasets)s>verilerimiz</a> tamamen açık kaynaktır."

msgid "page.home.preservation.header"
msgstr "Saklama"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Kitapları, makaleleri, çizgi romanları, dergileri ve daha fazlasını, bu materyalleri çeşitli <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">gölge kütüphanelerinden</a>, resmi kütüphanelerden ve diğer koleksiyonlardan bir araya getirerek koruyoruz. Tüm bu veriler, toplu olarak çoğaltılmasını kolaylaştırarak — torrentler kullanarak — dünya çapında birçok kopya oluşturulmasıyla sonsuza kadar korunur. Bazı gölge kütüphaneler bunu zaten kendileri yapıyor (örneğin Sci-Hub, Library Genesis), Anna’nın Arşivi ise toplu dağıtım sunmayan diğer kütüphaneleri “özgürleştiriyor” (örneğin Z-Library) veya hiç gölge kütüphane olmayanları (örneğin Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Bu geniş dağıtım, açık kaynak kodu ile birleştiğinde, web sitemizi kapatmalara karşı dayanıklı hale getirir ve insanlığın bilgi ve kültürünün uzun vadeli korunmasını sağlar. <a href=\"/datasets\">veri setlerimiz</a> hakkında daha fazla bilgi edinin."

msgid "page.home.preservation.label"
msgstr "<a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">Dünyadaki bütün kitapların 5%% kadarını</a> sakladığımızı tahmin ediyoruz."

msgid "page.home.access.header"
msgstr "Erişim"

msgid "page.home.access.text"
msgstr "Koleksiyonlarımızın herkese kolayca ve özgürce erişilebilir olmasını sağlamak için ortaklarla birlikte çalışıyoruz. Herkesin insanlığın toplu bilgeliğine erişme hakkına sahip olduğuna ve <a %(a_search)s>yazarların zararına olmadığına</a> inanıyoruz."

msgid "page.home.access.label"
msgstr "Son 30 günde saatlik indirmeler. Saatlik ortalama: %(hourly)s. Günlük ortalama: %(daily)s."

msgid "page.about.text2"
msgstr "Bilginin özgür akışına, bilgi ve kültürün korunmasına kuvvetle inanıyoruz. Bu arama motorunu devlerin omuzları üzerine inşa ediyoruz. Çeşitli gölge kütüphaneleri yaratan insanların sıkı çalışmalarına derinden saygı duyuyoruz ve bu arama motorunun gölge kütüphanelerin yayılmasını artıracağını umuyoruz."

msgid "page.about.text3"
msgstr "Kaydettiğimiz aşamalardan haberdar olmak için, Anna'yı <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> veya <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. 'te takip edin. Sorular ve geri bildirimler için lütfen Anna ile %(email)s aracılığıyla iletişime geçin."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Nasıl yardımcı olabilirim?"

msgid "page.about.help.text"
msgstr "<li>1. Bizi <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> veya <a href=\"https://t.me/annasarchiveorg\">Telegram</a>'da takip edin.</li><li>2. Twitter'da, Reddit'te, Tiktok'ta, Instagram'da, yerel kafe ya da kütüphanenizde veya canınız nerede isterse Anna'nın Arşivi hakkında birkaç kelime söyleyin! \"Bekçilik\" kavramına inanmıyoruz - eğer kapatılırsak tüm kodlarımızın ve verilerimizin açık kaynak olması sayesinde başka bir yerde ortaya çıkacağız.</li><li>3. Eğer imkanınız varsa, <a href=\"/donate\">bağış</a> yapmayı bir düşünün.</li><li>4. Sitemizi farklı dillere <a href=\"https://translate.annas-software.org/\">çevirmemize</a> yardım edin.</li><li>5. Eğer yazılım mühendisiyseniz, <a href=\"https://annas-software.org/\">açık kaynak kodumuza</a> katkı yapmayı ya da <a href=\"/datasets\">torrent</a>lerimizi paylaşmayı (seeding) düşünün.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Artık %(matrix)s'te senkronize bir Matrix kanalımız da var."

msgid "page.about.help.text6"
msgstr "6. Güvenlik araştırmacısı iseniz becerilerinizi hem hücum hem de savunma amaçlı kullanabiliriz. <a %(a_security)s>Güvenlik</a> sayfamıza göz atın."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Anonim tüccarlar için ödeme uzmanları arıyoruz. Daha uygun bağış yolları eklememize yardımcı olabilir misiniz? PayPal, WeChat, hediye kartları. Birini tanıyorsanız, lütfen bizimle iletişime geçin."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Her zaman daha fazla sunucu kapasitesi arıyoruz."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Dosya sorunlarını bildirerek, yorum bırakarak ve bu web sitesinde listeler oluşturarak yardımcı olabilirsiniz. Ayrıca <a %(a_upload)s>daha fazla kitap yükleyerek</a> veya mevcut kitapların dosya sorunlarını ya da formatlarını düzelterek de yardımcı olabilirsiniz."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Kendi dilinizde Anna’nın Arşivi için Wikipedia sayfası oluşturun veya bakımına yardımcı olun."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Küçük, zevkli reklamlar yerleştirmek istiyoruz. Anna’nın Arşivi’nde reklam vermek isterseniz, lütfen bize bildirin."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "<a %(a_mirrors)s>Aynalar</a> kurmalarını çok isteriz ve bunu mali olarak destekleyeceğiz."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Gönüllü olma hakkında daha kapsamlı bilgi için <a %(a_volunteering)s>Gönüllülük ve Ödüller</a> sayfamıza bakın."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Neden yavaş indirmeler bu kadar yavaş?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Dünyadaki herkese yüksek hızlı indirme sağlamak için yeterli kaynağımız gerçekten yok, ne kadar istesek de. Zengin bir hayırseverin bunu bizim için sağlaması harika olurdu, ama o zamana kadar elimizden gelenin en iyisini yapıyoruz. Bağışlarla zar zor ayakta duran kar amacı gütmeyen bir projeyiz."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Bu yüzden, ortaklarımızla birlikte iki ücretsiz indirme sistemi uyguladık: yavaş indirmeler için paylaşılan sunucular ve bekleme listesi ile biraz daha hızlı sunucular (aynı anda indiren kişi sayısını azaltmak için)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Yavaş indirmelerimiz için <a %(a_verification)s>tarayıcı doğrulaması</a> da yapıyoruz, çünkü aksi takdirde botlar ve kazıyıcılar bunları kötüye kullanarak meşru kullanıcılar için işleri daha da yavaşlatır."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Tor Tarayıcıyı kullanırken, güvenlik ayarlarınızı ayarlamanız gerekebileceğini unutmayın. “Standart” olarak adlandırılan en düşük seçeneklerde, Cloudflare turnike meydan okuması başarılı olur. “Daha Güvenli” ve “En Güvenli” olarak adlandırılan daha yüksek seçeneklerde, meydan okuma başarısız olur."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Büyük dosyalar için bazen yavaş indirmeler ortasında kesilebilir. Büyük indirmeleri otomatik olarak devam ettirmek için bir indirme yöneticisi (örneğin JDownloader) kullanmanızı öneririz."

msgid "page.donate.faq.title"
msgstr "Bağış SSS"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Üyelikler otomatik olarak yenileniyor mu?</div> Üyelikler otomatik olarak <strong>yenilenmiyor</strong>. İstediğiniz kadar uzun ya da kısa süre katılabilirsiniz."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Üyeliğimi yükseltebilir miyim veya birden fazla üyelik alabilir miyim?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Başka ödeme yöntemleri mevcut mu?</div> Şimdilik hayır. Arşivlerimizin var olmasını istemeyen bir çok kişi olduğu için dikkatli davranmamız gerekiyor. Eğer daha iyi bir yöntemi biliyorsanız bizimle iletişime geçin: %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Aylık aralıklar ne anlama geliyor?</div> Tüm indirimleri uygulayarak, örneğin bir aydan daha uzun bir süre seçerek, bir aralığın alt tarafına ulaşabilirsiniz."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Bağışları neye harcıyorsunuz?</div> Bağışların %%100'ü dünyanın bilgisi ve kültürünü korumaya ve erişilebilir hale getirmeye gidiyor. An itibariyle çoğunlukla sunucular, depolama ve bant genişliği. Hiçbir ekip üyesine bizzat para gitmiyor."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Büyük miktarda bağış yapabilir miyim?</div> Harika olur! Bin doların üstündeki bağışlarınız için lütfen bize, %(email)s adresinden direkt iletişim kurunuz."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Üye olmadan bağış yapabilir miyim?</div> Tabii ki. Herhangi bir miktarda bağışı bu Monero (XMR) adresine kabul ediyoruz: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Yeni kitapları nasıl yüklerim?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternatif olarak, onları Z-Library'e <a %(a_upload)s>buradan</a> yükleyebilirsiniz."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Küçük yüklemeler için (10.000 dosyaya kadar) lütfen hem %(first)s hem de %(second)s adreslerine yükleyin."

msgid "page.upload.text1"
msgstr "Şimdilik, yeni kitapları Library Genesis forklarına yüklemeyi tavsiye ediyoruz. Burada <a %(a_guide)s>yararlı bir rehber</a> bulabilirsiniz. Bu websitede dizinlediğimiz her iki forkun aynı karşıya yükleme sisteminden çektiğini dikkate alın."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.li için, önce <a %(a_forum)s>forumlarına</a> kullanıcı adı %(username)s ve şifre %(password)s ile giriş yaptığınızdan emin olun ve ardından <a %(a_upload_page)s>yükleme sayfalarına</a> geri dönün."

msgid "common.libgen.email"
msgstr "Eğer e-posta adresiniz Libgen forumlarında çalışmazsa, <a %(a_mail)s>Proton Mail</a> (ücretsiz) kullanmanızı öneriyoruz. Ayrıca hesabınızın aktifleştirilmesini <a %(a_manual)s>bizzat talep edebilirsiniz</a>."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Mhut.org belirli IP aralıklarını engellediğini unutmayın, bu yüzden bir VPN gerekebilir."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Libgen veya Z-Library tarafından kabul edilmeyen büyük yüklemeler (10.000'den fazla dosya) için lütfen %(a_email)s adresinden bizimle iletişime geçin."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Akademik makaleleri yüklemek için lütfen (Library Genesis'e ek olarak) <a %(a_stc_nexus)s>STC Nexus</a>'a da yükleyin. Yeni makaleler için en iyi gölge kütüphanedir. Henüz onları entegre etmedik, ama bir noktada edeceğiz. <a %(a_telegram)s>Telegram'daki yükleme botlarını</a> kullanabilir veya bu şekilde yüklemek için çok fazla dosyanız varsa sabit mesajlarında listelenen adresle iletişime geçebilirsiniz."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Kitap talebini nasıl yaparım?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Şu anda kitap isteklerini karşılayamıyoruz."

#, fuzzy
msgid "page.request.forums"
msgstr "Lütfen taleplerinizi Z-Library veya Libgen forumlarında yapın."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Kitap taleplerinizi bize e-posta ile göndermeyin."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Üstveri topluyor musunuz?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Evet, topluyoruz."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "George Orwell'in 1984'ünü indirdim, polis kapıma gelir mi?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Çok fazla endişelenmeyin, bizim bağlantı verdiğimiz web sitelerinden birçok kişi indiriyor ve sorun yaşamak son derece nadirdir. Ancak, güvende kalmak için ücretli bir VPN veya ücretsiz <a %(a_tor)s>Tor</a> kullanmanızı öneririz."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Arama ayarlarımı nasıl kaydederim?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Beğendiğiniz ayarları seçin, arama kutusunu boş bırakın, “Ara”ya tıklayın ve ardından tarayıcınızın yer imi özelliğini kullanarak sayfayı yer imlerine ekleyin."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Mobil uygulamanız var mı?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Resmi bir mobil uygulamamız yok, ancak bu web sitesini bir uygulama olarak yükleyebilirsiniz."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Sağ üst köşedeki üç noktalı menüye tıklayın ve “Ana Ekrana Ekle”yi seçin."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Alttaki “Paylaş” düğmesine tıklayın ve “Ana Ekrana Ekle”yi seçin."

#, fuzzy
msgid "page.faq.api.title"
msgstr "API'niz var mı?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Üyeler için hızlı indirme URL'si almak amacıyla tek bir sabit JSON API'miz var: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON içinde belgeler mevcuttur)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Diğer kullanım durumları için, örneğin tüm dosyalarımızı yinelemek, özel arama oluşturmak ve benzeri, <a %(a_generate)s>oluşturmayı</a> veya <a %(a_download)s>indirmeyi</a> öneririz. Ham veriler manuel olarak <a %(a_explore)s>JSON dosyaları aracılığıyla</a> keşfedilebilir."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Ham torrent listemiz de <a %(a_torrents)s>JSON</a> olarak indirilebilir."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrentler SSS"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Seed etmeye yardımcı olmak istiyorum, ancak fazla disk alanım yok."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Depolama alanı sınırlarınız dahilinde en çok torrent ihtiyacı olan torrentlerin listesini oluşturmak için <a %(a_list)s>torrent listesi oluşturucuyu</a> kullanın."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrentler çok yavaş; verileri doğrudan sizden indirebilir miyim?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Evet, <a %(a_llm)s>LLM verileri</a> sayfasına bakın."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Sadece belirli bir dil veya konu gibi dosyaların bir alt kümesini indirebilir miyim?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kısa cevap: kolay değil."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Uzun cevap:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Çoğu torrent dosyaları doğrudan içerir, bu da torrent istemcilerine yalnızca gerekli dosyaları indirmelerini söyleyebileceğiniz anlamına gelir. Hangi dosyaları indireceğinizi belirlemek için üstverimizi <a %(a_generate)s>oluşturabilir</a> veya ElasticSearch ve MariaDB veritabanlarımızı <a %(a_download)s>indirebilirsiniz</a>. Ne yazık ki, bazı torrent koleksiyonları kök dizinde .zip veya .tar dosyaları içerir, bu durumda bireysel dosyaları seçmeden önce tüm torrent'i indirmeniz gerekir."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Ancak, ikinci durum için <a %(a_ideas)s>bazı fikirlerimiz</a> var.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Henüz torrentleri filtrelemek için kullanımı kolay araçlar mevcut değil, ancak katkılarınızı memnuniyetle karşılıyoruz."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Torrentlerdeki kopyaları nasıl ele alıyorsunuz?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Bu listedeki torrentler arasında minimum kopyalama veya örtüşme olmasını sağlamaya çalışıyoruz, ancak bu her zaman mümkün olamaz ve kaynak kütüphanelerin politikalarına büyük ölçüde bağlıdır. Kendi torrentlerini yayınlayan kütüphaneler için bu bizim elimizde değil. Anna’nın Arşivi tarafından yayınlanan torrentler için, yalnızca MD5 hash'e dayalı olarak kopyaları kaldırıyoruz, bu da aynı kitabın farklı sürümlerinin kopyalanmadığı anlamına gelir."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Torrent listesini JSON olarak alabilir miyim?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Evet."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Torrentlerde PDF veya EPUB görmüyorum, sadece ikili dosyalar mı? Ne yapmalıyım?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Bunlar aslında PDF ve EPUB dosyalarıdır, sadece birçok torrentimizde uzantıları yoktur. Torrent dosyalarının üstverilerini, dosya türleri/uzantıları dahil olmak üzere bulabileceğiniz iki yer vardır:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Her koleksiyon veya sürümün kendi üstverisi vardır. Örneğin, <a %(a_libgen_nonfic)s>Libgen.rs torrentleri</a> Libgen.rs web sitesinde barındırılan ilgili bir üstveri veritabanına sahiptir. Genellikle her koleksiyonun <a %(a_datasets)s>veri seti sayfasından</a> ilgili üstveri kaynaklarına bağlantı veririz."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. ElasticSearch ve MariaDB veritabanlarımızı <a %(a_generate)s>oluşturmayı</a> veya <a %(a_download)s>indirmeyi</a> öneririz. Bunlar, Anna’nın Arşivi'ndeki her kaydın karşılık gelen torrent dosyalarına (varsa) \"torrent_paths\" altında bir eşlemesini içerir."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Torrent istemcim neden bazı torrent dosyalarınızı / magnet bağlantılarınızı açamıyor?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Bazı torrent istemcileri büyük parça boyutlarını desteklemez, ki birçok torrentimizde bu durum söz konusudur (yeni olanlarda bunu artık yapmıyoruz — spesifikasyonlara göre geçerli olmasına rağmen!). Bu durumla karşılaşırsanız farklı bir istemci deneyin veya torrent istemcinizin yapımcılarına şikayette bulunun."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Sorumlu açıklama programınız var mı?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Sistemlerimizdeki güvenlik açıklarını aramak için güvenlik araştırmacılarını memnuniyetle karşılıyoruz. Sorumlu açıklamanın büyük savunucularıyız. Bize <a %(a_contact)s>buradan</a> ulaşın."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Şu anda, <a %(a_link)s>anonimliğimizi tehlikeye atma potansiyeline sahip</a> güvenlik açıkları dışında hata ödülleri veremiyoruz. Bu tür güvenlik açıkları için 10.000-50.000 $ aralığında ödüller sunuyoruz. Gelecekte hata ödülleri için daha geniş bir kapsam sunmak istiyoruz! Sosyal mühendislik saldırılarının kapsam dışı olduğunu lütfen unutmayın."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Saldırgan güvenlik ile ilgileniyorsanız ve dünyanın bilgi ve kültürünü arşivlemeye yardımcı olmak istiyorsanız, bizimle iletişime geçtiğinizden emin olun. Yardım edebileceğiniz birçok yol var."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Anna’nın Arşivi hakkında daha fazla kaynak var mı?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’nın Blogu</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — düzenli güncellemeler"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’nın Yazılımı</a> — açık kaynak kodumuz"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Anna’nın Yazılımında Çeviri Yapın</a> — çeviri sistemimiz"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — veriler hakkında"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatif alan adları"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — hakkımızda daha fazla bilgi (lütfen bu sayfayı güncel tutmaya yardımcı olun veya kendi dilinizde bir tane oluşturun!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Telif hakkı ihlalini nasıl bildiririm?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Burada herhangi bir telif hakkıyla korunan materyali barındırmıyoruz. Biz bir arama motoruyuz ve bu nedenle yalnızca zaten kamuya açık olan üstveriyi indeksliyoruz. Bu dış kaynaklardan indirirken, izin verilenler konusunda kendi yargı bölgenizdeki yasaları kontrol etmenizi öneririz. Başkaları tarafından barındırılan içerikten sorumlu değiliz."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Burada gördüğünüz şeylerle ilgili şikayetleriniz varsa, en iyi seçeneğiniz orijinal web sitesiyle iletişime geçmektir. Değişikliklerini düzenli olarak veritabanımıza çekiyoruz. Gerçekten geçerli bir DMCA şikayetiniz olduğunu düşünüyorsanız, lütfen <a %(a_copyright)s>DMCA / Telif hakkı talep formunu</a> doldurun. Şikayetlerinizi ciddiye alıyoruz ve en kısa sürede size geri döneceğiz."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Bu projeyi nasıl yönettiğinizi nefret ediyorum!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Ayrıca, tüm kod ve verilerimizin tamamen açık kaynak olduğunu herkese hatırlatmak isteriz. Bu, bizim gibi projeler için benzersizdir — benzer şekilde büyük bir kataloğa sahip olup da tamamen açık kaynak olan başka bir proje bilmiyoruz. Projemizi kötü yönettiğimizi düşünen herkesi kodumuzu ve verilerimizi alıp kendi gölge kütüphanelerini kurmaya davet ediyoruz! Bunu kin veya başka bir şeyden dolayı söylemiyoruz — gerçekten bunun harika olacağını düşünüyoruz çünkü bu herkes için çıtayı yükseltecek ve insanlığın mirasını daha iyi koruyacaktır."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Bir çalışma süresi izleyiciniz var mı?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Lütfen <a %(a_href)s>bu mükemmel projeye</a> bakın."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Kitap veya diğer fiziksel materyalleri nasıl bağışlayabilirim?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Lütfen onları <a %(a_archive)s>Internet Archive</a>'e gönderin. Onlar uygun şekilde koruyacaklardır."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Anna kimdir?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Siz Annasınız!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Favori kitaplarınız nelerdir?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "İşte gölge kütüphaneler ve dijital koruma dünyası için özel bir öneme sahip bazı kitaplar:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Bugün hızlı indirme hakkınız tükendi."

msgid "page.fast_downloads.no_member"
msgstr "Hızlı indirmeleri kullanmak için üye olun."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Artık Amazon hediye kartları, kredi ve banka kartları, kripto, Alipay ve WeChat'i destekliyoruz."

msgid "page.home.full_database.header"
msgstr "Tüm veritabanı"

msgid "page.home.full_database.subtitle"
msgstr "Kitaplar, makaleler, dergiler, çizgi romanlar, kütüphane kayıtları, metadata, …"

msgid "page.home.full_database.search"
msgstr "Ara"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub yeni makalelerin yüklenmesini <a %(a_paused)s>durdurdu</a>."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB, Sci-Hub'ın devamıdır."

msgid "page.home.scidb.subtitle"
msgstr "%(count)s akademik makaleye direkt ulaşım"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Aç"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Eğer bir <a %(a_member)s>üye</a> iseniz, tarayıcı doğrulaması gerekmez."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Uzun vadeli arşiv"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Anna’nın Arşivi'nde kullanılan verisetleri tamamen açıktır ve torrentler kullanılarak toplu olarak yansıtılabilir. <a %(a_datasets)s>Daha fazla bilgi edinin…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Torrentleri paylaşarak büyük ölçüde yardımcı olabilirsiniz. <a %(a_torrents)s>Daha fazla bilgi edinin…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s tohumlayıcılar"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s tohumlayıcılar"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Dünyanın en büyük yüksek kaliteli metin veri koleksiyonuna sahibiz. <a %(a_llm)s>Daha fazla bilgi edinin…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Aynalar: gönüllü çağrısı"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Gönüllüler aranıyor"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Kâr amacı gütmeyen, açık kaynaklı bir proje olarak, her zaman yardıma ihtiyacımız var."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Yüksek riskli anonim bir ödeme işlemcisi çalıştırıyorsanız, lütfen bizimle iletişime geçin. Ayrıca, zarif küçük reklamlar yerleştirmek isteyen kişiler arıyoruz. Tüm gelirler koruma çabalarımıza gitmektedir."

msgid "layout.index.header.nav.annasblog"
msgstr "Anna'nın Blogu ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS indirmeleri"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Bu dosya için tüm indirme bağlantıları: <a %(a_main)s>Dosya ana sayfası</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Ağ Geçidi #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(IPFS ile birden çok kez denemeniz gerekebilir)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Daha hızlı indirmeler almak ve tarayıcı kontrollerini atlamak için <a %(a_membership)s>üye olun</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Koleksiyonumuzun toplu yansıtması için <a %(a_datasets)s>Veri Setleri</a> ve <a %(a_torrents)s>Torrentler</a> sayfalarına bakın."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM verileri"

#, fuzzy
msgid "page.llm.intro"
msgstr "LLM'lerin yüksek kaliteli verilerle beslendiği iyi bilinmektedir. Dünyanın en büyük kitap, makale, dergi vb. koleksiyonuna sahibiz ve bunlar en yüksek kaliteli metin kaynaklarından bazılarıdır."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Benzersiz ölçek ve çeşitlilik"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Koleksiyonumuz, akademik dergiler, ders kitapları ve dergiler dahil olmak üzere yüz milyondan fazla dosya içermektedir. Bu ölçeğe, büyük mevcut depoları birleştirerek ulaşıyoruz."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Bazı kaynak koleksiyonlarımız zaten toplu olarak mevcuttur (Sci-Hub ve Libgen'in bazı bölümleri). Diğer kaynakları kendimiz özgürleştirdik. <a %(a_datasets)s>Datasets</a> tam bir genel bakış sunar."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Koleksiyonumuz, e-kitap döneminden önceki milyonlarca kitap, makale ve dergiyi içermektedir. Bu koleksiyonun büyük bölümleri zaten OCR ile taranmış olup, içsel örtüşme çok azdır."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Nasıl yardımcı olabiliriz"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Tam koleksiyonlarımıza ve henüz yayınlanmamış koleksiyonlara yüksek hızlı erişim sağlayabiliyoruz."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Bu, on binlerce USD bağış aralığında sağlayabileceğimiz kurumsal düzeyde bir erişimdir. Ayrıca, henüz sahip olmadığımız yüksek kaliteli koleksiyonlarla bu erişimi takas etmeye de istekliyiz."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Verilerimizi zenginleştirerek bize sağlayabilirseniz, size geri ödeme yapabiliriz, örneğin:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Örtüşmeyi kaldırma (deduplikasyon)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Metin ve üstveri çıkarma"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Modeliniz için daha iyi veriler elde ederken, insan bilgisinin uzun vadeli arşivlenmesini destekleyin!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Bizimle iletişime geçin</a> nasıl birlikte çalışabileceğimizi tartışmak için."

msgid "page.login.continue"
msgstr "Devam et"

#, fuzzy
msgid "page.login.please"
msgstr "Bu sayfayı görüntülemek için lütfen <a %(a_account)s>giriş yapın</a>.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’nın Arşivi geçici olarak bakımda. Lütfen bir saat sonra tekrar gelin."

#, fuzzy
msgid "page.metadata.header"
msgstr "Üstveriyi iyileştirin"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Üstveriyi iyileştirerek kitapların korunmasına yardımcı olabilirsiniz! Öncelikle, Anna’nın Arşivi'nde üstveri hakkında arka plan bilgilerini okuyun ve ardından Open Library ile bağlantı kurarak üstveriyi nasıl iyileştireceğinizi öğrenin ve Anna’nın Arşivi'nde ücretsiz üyelik kazanın."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Arka Plan"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Anna’nın Arşivi'nde bir kitaba baktığınızda, çeşitli alanlar görebilirsiniz: başlık, yazar, yayınevi, baskı, yıl, açıklama, dosya adı ve daha fazlası. Bu bilgilerin tümü <em>üstveri</em> olarak adlandırılır."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Çeşitli <em>kaynak kütüphanelerden</em> kitapları birleştirdiğimiz için, o kaynak kütüphanede mevcut olan üstveriyi gösteriyoruz. Örneğin, Library Genesis'ten aldığımız bir kitap için, Library Genesis'in veritabanındaki başlığı gösteririz."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Bazen bir kitap <em>birden fazla</em> kaynak kütüphanede bulunabilir ve bu kütüphaneler farklı üstveri alanlarına sahip olabilir. Bu durumda, her alanın en uzun versiyonunu gösteririz, çünkü bu versiyonun en faydalı bilgileri içerdiğini umarız! Yine de diğer alanları açıklamanın altında, örneğin \"alternatif başlık\" olarak gösteririz (ancak yalnızca farklılarsa)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Ayrıca kaynak kütüphaneden <em>kimlikler ve sınıflandırıcılar</em> gibi kodları da çıkarırız. <em>Kimlikler</em> belirli bir kitabın baskısını benzersiz şekilde temsil eder; örnekler ISBN, DOI, Open Library ID, Google Books ID veya Amazon ID'dir. <em>Sınıflandırıcılar</em> benzer kitapları bir araya getirir; örnekler Dewey Decimal (DCC), UDC, LCC, RVK veya GOST'tur. Bazen bu kodlar kaynak kütüphanelerde açıkça bağlantılıdır ve bazen dosya adı veya açıklamadan çıkarabiliriz (öncelikle ISBN ve DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Kimlikleri, OpenLibrary, ISBNdb veya WorldCat/OCLC gibi <em>sadece üstveri koleksiyonlarında</em> kayıtları bulmak için kullanabiliriz. Bu koleksiyonları göz atmak isterseniz, arama motorumuzda belirli bir <em>üstveri sekmesi</em> vardır. Eşleşen kayıtları eksik üstveri alanlarını doldurmak için kullanırız (örneğin, bir başlık eksikse) veya örneğin \"alternatif başlık\" olarak (eğer mevcut bir başlık varsa)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Bir kitabın üstverisinin tam olarak nereden geldiğini görmek için, bir kitap sayfasındaki <em>“Teknik detaylar” sekmesine</em> bakın. Bu sekmede, o kitaba ait ham JSON'a ve orijinal kayıtların ham JSON'una işaret eden bağlantılar bulunur."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Daha fazla bilgi için şu sayfalara bakın: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> ve <a %(a_example)s>Example metadata JSON</a>. Son olarak, tüm üstverimiz <a %(a_generated)s>oluşturulabilir</a> veya <a %(a_downloaded)s>indirilebilir</a> ElasticSearch ve MariaDB veritabanları olarak."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library bağlantısı"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Peki, kötü üstveriye sahip bir dosya ile karşılaşırsanız nasıl düzeltmelisiniz? Kaynak kütüphaneye gidip üstveriyi düzeltme prosedürlerini takip edebilirsiniz, ancak bir dosya birden fazla kaynak kütüphanede bulunuyorsa ne yapmalısınız?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Anna’nın Arşivi'nde özel olarak ele alınan bir kimlik vardır. <strong>Open Library'deki annas_archive md5 alanı her zaman diğer tüm üstverilerin önüne geçer!</strong> Önce biraz geri adım atalım ve Open Library hakkında bilgi edinelim."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library, 2006 yılında Aaron Swartz tarafından “yayınlanmış her kitap için bir web sayfası” hedefiyle kuruldu. Kitap üstverisi için bir tür Wikipedia gibidir: herkes düzenleyebilir, serbest lisanslıdır ve toplu olarak indirilebilir. Misyonumuzla en çok örtüşen kitap veritabanıdır — aslında, Anna’nın Arşivi, Aaron Swartz’ın vizyonu ve hayatından ilham almıştır."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Tekerleği yeniden icat etmek yerine, gönüllülerimizi Open Library'ye yönlendirmeye karar verdik. Yanlış üstveriye sahip bir kitap görürseniz, şu şekilde yardımcı olabilirsiniz:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " <a %(a_openlib)s>Open Library web sitesine</a> gidin."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Doğru kitap kaydını bulun. <strong>UYARI:</strong> doğru <strong>baskıyı</strong> seçtiğinizden emin olun. Open Library'de “eserler” ve “baskılar” vardır."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Bir “eser” “Harry Potter ve Felsefe Taşı” olabilir."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Bir “baskı” şu olabilir:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997 yılında Bloomsbery tarafından yayımlanan ilk baskı, 256 sayfa."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003 yılında Raincoast Books tarafından yayımlanan ciltsiz baskı, 223 sayfa."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000 yılında Media Rodzina tarafından yayımlanan Lehçe çeviri “Harry Potter I Kamie Filozoficzn”, 328 sayfa."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Bu baskıların hepsinin farklı ISBN'leri ve farklı içerikleri vardır, bu yüzden doğru olanı seçtiğinizden emin olun!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Kaydı düzenleyin (veya mevcut değilse oluşturun) ve mümkün olduğunca faydalı bilgi ekleyin! Madem buradasınız, kaydı gerçekten harika hale getirin."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "“ID Numaraları” altında “Anna’nın Arşivi”ni seçin ve Anna’nın Arşivi'nden kitabın MD5'ini ekleyin. Bu, URL'deki “/md5/”den sonraki uzun harf ve rakam dizisidir."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Bu kayda da uyan diğer dosyaları Anna’nın Arşivi'nde bulmaya çalışın ve onları da ekleyin. Gelecekte bunları Anna’nın Arşivi arama sayfasında kopyalar olarak gruplandırabiliriz."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "İşiniz bittiğinde, güncellediğiniz URL'yi yazın. Anna’nın Arşivi MD5'leri ile en az 30 kaydı güncelledikten sonra, bize bir <a %(a_contact)s>e-posta</a> gönderin ve listeyi bize iletin. Size Anna’nın Arşivi için ücretsiz bir üyelik vereceğiz, böylece bu işi daha kolay yapabilirsiniz (ve yardımınız için teşekkür ederiz). Bunlar, önemli miktarda bilgi ekleyen yüksek kaliteli düzenlemeler olmalıdır, aksi takdirde talebiniz reddedilecektir. Talebiniz, düzenlemelerden herhangi biri Open Library moderatörleri tarafından geri alınır veya düzeltilirse de reddedilecektir."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Bunun yalnızca kitaplar için geçerli olduğunu unutmayın, akademik makaleler veya diğer dosya türleri için geçerli değildir. Diğer dosya türleri için kaynak kütüphaneyi bulmanızı öneririz. Değişikliklerin Anna’nın Arşivi'ne dahil edilmesi birkaç hafta sürebilir, çünkü en son Open Library veri dökümünü indirmemiz ve arama dizinimizi yeniden oluşturmamız gerekiyor."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Aynalar: gönüllü çağrısı"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Anna’nın Arşivi’nin dayanıklılığını artırmak için aynaları çalıştıracak gönüllüler arıyoruz."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Bunu arıyoruz:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Anna’nın Arşivi açık kaynak kod tabanını çalıştırıyorsunuz ve hem kodu hem de verileri düzenli olarak güncelliyorsunuz."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Sürümünüz, örneğin “Bob’un Arşivi, bir Anna’nın Arşivi aynası” gibi açıkça bir ayna olarak ayırt edilir."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Bu işin getirdiği önemli riskleri almaya hazırsınız. Gerekli operasyonel güvenliği derinlemesine anlıyorsunuz. <a %(a_shadow)s>bu</a> <a %(a_pirate)s>gönderilerin</a> içeriği sizin için aşikardır."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Bunu gerçekleştirmek için ekibimizle işbirliği içinde <a %(a_codebase)s>kod tabanımıza</a> katkıda bulunmaya hazırsınız."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Başlangıçta ortak sunucu indirmelerimize erişim vermeyeceğiz, ancak işler yolunda giderse bunu sizinle paylaşabiliriz."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Barındırma giderleri"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Barındırma ve VPN giderlerini karşılamaya hazırız, başlangıçta ayda 200$'a kadar. Bu, temel bir arama sunucusu ve DMCA korumalı bir proxy için yeterlidir."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Her şeyi kurduktan ve arşivi güncellemelerle güncel tutabileceğinizi gösterdikten sonra yalnızca barındırma için ödeme yapacağız. Bu, ilk 1-2 ayı cebinizden ödemeniz gerekeceği anlamına gelir."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Zamanınız için ödeme yapılmayacak (bizimki de öyle), çünkü bu tamamen gönüllü bir çalışmadır."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Eğer işimizin geliştirilmesi ve operasyonlarına önemli ölçüde dahil olursanız, gerekli gördüğünüzde kullanmanız için bağış gelirlerinin daha fazlasını sizinle paylaşmayı konuşabiliriz."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Başlarken"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Lütfen <strong>bizimle iletişime geçmeyin</strong> izin istemek veya temel sorular sormak için. Eylemler sözlerden daha yüksek sesle konuşur! Tüm bilgiler orada, bu yüzden aynanızı kurmaya devam edin."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Sorunlarla karşılaştığınızda Gitlab'ımıza bilet veya birleştirme isteği göndermekten çekinmeyin. Sizinle birlikte bazı ayna-spesifik özellikler geliştirmemiz gerekebilir, örneğin “Anna’nın Arşivi” adını web sitenizin adıyla yeniden markalaştırmak, (başlangıçta) kullanıcı hesaplarını devre dışı bırakmak veya kitap sayfalarından ana sitemize bağlantı vermek gibi."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Aynanızı çalıştırmaya başladığınızda, lütfen bizimle iletişime geçin. OpSec'inizi gözden geçirmeyi çok isteriz ve bu sağlam olduğunda, aynanıza bağlantı vereceğiz ve sizinle daha yakın çalışmaya başlayacağız."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Bu şekilde katkıda bulunmaya istekli olan herkese şimdiden teşekkürler! Bu, cesaret gerektiren bir iş, ancak insanlık tarihindeki en büyük gerçekten açık kütüphanenin uzun ömürlülüğünü sağlamlaştıracaktır."

msgid "page.partner_download.header"
msgstr "Ortak siteden indir"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Yavaş indirmeler yalnızca resmi web sitesi üzerinden mevcuttur. %(websites)s'i ziyaret edin."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Yavaş indirmeler Cloudflare VPN'leri veya Cloudflare IP adreslerinden başka yollarla mevcut değildir."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Bu dosyayı indirmek için lütfen <span %(span_countdown)s>%(wait_seconds)s</span> saniye bekleyin."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 İndirmek için şu bağlantıyı kullanın: <a %(a_download)s>Şimdi indir</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Beklediğiniz için teşekkürler, bu siteyi herkes için ücretsiz erişilebilir kılıyor! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Uyarı: Son 24 saat içinde IP adresinizden çok sayıda indirme yapıldı. İndirmeler normalden daha yavaş olabilir."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Son 24 saatte IP adresinizden yapılan indirmeler: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "VPN, paylaşılan internet bağlantısı kullanıyorsanız veya ISS'niz IP'leri paylaşıyorsa, bu uyarı bundan kaynaklanıyor olabilir."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Herkese dosyaları ücretsiz indirme fırsatı vermek için, bu dosyayı indirmeden önce beklemeniz gerekiyor."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Beklerken Anna’nın Arşivi'nde farklı bir sekmede gezinmeye devam edebilirsiniz (tarayıcınız arka plan sekmelerini yenilemeyi destekliyorsa)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Birden fazla indirme sayfasının aynı anda yüklenmesini bekleyebilirsiniz (ancak lütfen her sunucudan aynı anda yalnızca bir dosya indirin)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Bir indirme bağlantısı aldığınızda, bu bağlantı birkaç saat geçerlidir."

msgid "layout.index.header.title"
msgstr "Anna'nın Arşivi"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Anna’nın Arşivi'nde kayıt"

#, fuzzy
msgid "page.scidb.download"
msgstr "İndir"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "İnsan bilgisinin erişilebilirliğini ve uzun vadeli korunmasını desteklemek için <a %(a_donate)s>üye</a> olun."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Bonus olarak, 🧬&nbsp;SciDB üyeler için daha hızlı yüklenir ve herhangi bir sınırlama olmadan."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Çalışmıyor mu? <a %(a_refresh)s>yenileyin</a> deneyin."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Henüz önizleme mevcut değil. Dosyayı <a %(a_path)s>Anna’nın Arşivi</a>'nden indirin."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB, Sci-Hub'un devamıdır, tanıdık arayüzü ve PDF'lerin doğrudan görüntülenmesi ile. Görüntülemek için DOI'nizi girin."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Tam Sci-Hub koleksiyonuna ve yeni makalelere sahibiz. Çoğu, Sci-Hub'a benzer tanıdık bir arayüzle doğrudan görüntülenebilir. Bazıları harici kaynaklardan indirilebilir, bu durumda bu bağlantıları gösteriyoruz."

msgid "page.search.title.results"
msgstr "%(search_input)s - Arama"

msgid "page.search.title.new"
msgstr "Yeni Arama"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Sadece dahil et"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Hariç tut"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "İşaretlenmemiş"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "İndir"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Dergi makaleleri"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Dijital Ödünç Verme"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Üstveri"

msgid "common.search.placeholder"
msgstr "Başlık, yazar, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Arama"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Arama ayarları"

#, fuzzy
msgid "page.search.submit"
msgstr "Ara"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Arama çok uzun sürdü, bu geniş sorgular için yaygındır. Filtre sayıları doğru olmayabilir."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Arama çok uzun sürdü, bu da yanlış sonuçlar görebileceğiniz anlamına gelir. Bazen sayfayı <a %(a_reload)s>yeniden yüklemek</a> yardımcı olabilir."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Göster"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Liste"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tablo"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Gelişmiş"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Açıklamaları ve üstveri yorumlarını ara"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Özel arama alanı ekle"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(belirli alanı ara)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Yayın yılı"

msgid "page.search.filters.content.header"
msgstr "İçerik"

msgid "page.search.filters.filetype.header"
msgstr "Dosya tipi"

#, fuzzy
msgid "page.search.more"
msgstr "daha fazla…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Erişim"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Kaynak"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "AA tarafından kazınmış ve açık kaynaklı hale getirilmiş"

msgid "page.search.filters.language.header"
msgstr "Dil"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Sırala"

msgid "page.search.filters.sorting.most_relevant"
msgstr "En uygun"

msgid "page.search.filters.sorting.newest"
msgstr "En yeni"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(yayın yılı)"

msgid "page.search.filters.sorting.oldest"
msgstr "En eski"

msgid "page.search.filters.sorting.largest"
msgstr "En büyük"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(dosya boyutu)"

msgid "page.search.filters.sorting.smallest"
msgstr "En küçük"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(açık kaynaklı)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Rastgele"

msgid "page.search.header.update_info"
msgstr "Arama dizini aylık olarak güncellenir. Şu anda %(last_data_refresh_date)s kadar öğe içeriyor. Daha fazla teknik bilgi için %(link_open_tag)sveri kümeleri sayfasına</a> bakın."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Kodlarla arama dizinini keşfetmek için <a %(a_href)s>Kodlar Gezgini</a>'ni kullanın."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "%(count)s doğrudan indirilebilir dosyalar kataloğumuzu aramak için kutuya yazın, biz bu dosyaları <a %(a_preserve)s>sonsuzluğa kadar koruyoruz</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Aslında, herkes <a %(a_torrents)s>birleşik torrent listemizi</a> tohumlayarak bu dosyaların korunmasına yardımcı olabilir."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Şu anda dünyanın en kapsamlı açık kitap, makale ve diğer yazılı eserler kataloğuna sahibiz. Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ve daha fazlasını</a> yansıtıyoruz."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Başka “gölge kütüphaneler” bulursanız ve bunları yansıtmalıyız ya da herhangi bir sorunuz varsa, lütfen %(email)s adresinden bizimle iletişime geçin."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "DMCA / telif hakkı talepleri için <a %(a_copyright)s>buraya tıklayın</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "İpucu: Daha hızlı gezinme için klavye kısayollarını kullanın “/” (arama odak), “enter” (arama), “j” (yukarı), “k” (aşağı), “<” (önceki sayfa), “>” (sonraki sayfa)."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Makaleler mi arıyorsunuz?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "%(count)s akademik makale ve dergi makalesi kataloğumuzu aramak için kutuya yazın, biz bu makaleleri <a %(a_preserve)s>sonsuzluğa kadar koruyoruz</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Dijital ödünç verme kütüphanelerinde dosya aramak için kutuya yazın."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Bu arama dizini şu anda Internet Archive’ın Kontrollü Dijital Ödünç Verme kütüphanesinden üstveri içermektedir. <a %(a_datasets)s>Veri kümelerimiz hakkında daha fazla bilgi</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Daha fazla dijital ödünç verme kütüphanesi için <a %(a_wikipedia)s>Wikipedia</a> ve <a %(a_mobileread)s>MobileRead Wiki</a>'ye bakın."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Kütüphanelerden üstveri aramak için kutuya yazın. Bu, <a %(a_request)s>bir dosya talep ederken</a> faydalı olabilir."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Bu arama dizini şu anda çeşitli üstveri kaynaklarından üstveri içermektedir. <a %(a_datasets)s>Datasets hakkında daha fazla bilgi</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Üstveri için, orijinal kayıtları gösteriyoruz. Kayıtları birleştirme yapmıyoruz."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Dünya genelinde yazılı eserler için birçok, birçok üstveri kaynağı vardır. <a %(a_wikipedia)s>Bu Wikipedia sayfası</a> iyi bir başlangıçtır, ancak başka iyi listeler biliyorsanız lütfen bize bildirin."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Aramak için kutuya yazın."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Bunlar üstveri kayıtlarıdır, <span %(classname)s>indirilebilir dosyalar</span> değildir."

msgid "page.search.results.error.header"
msgstr "Arama sırasında hata."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "<a %(a_reload)s>Sayfayı yeniden yüklemeyi</a> deneyin. Sorun devam ederse, lütfen bize %(email)s adresinden e-posta gönderin."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Hiç kayıt bulunamadı.</span> Aradığınız kelimeleri veya filtreleri değiştirerek tekrar deneyin."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Bazen arama sunucusu yavaş olduğunda bu yanlışlıkla gerçekleşir. Bu gibi durumlarda, <a %(a_attrs)s>yeniden yükleme</a> yardımcı olabilir."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "%(in)s içinde eşleşmeler bulduk. Bir dosya <a %(a_request)s>talep ederken</a> orada bulunan URL'ye başvurabilirsiniz."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Dergi Makaleleri (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Dijital Ödünç Verme (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Üstveri (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Sonuçlar %(from)s-%(to)s (%(total)s toplam)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ yaklaşık eşleşme"

msgid "page.search.results.partial"
msgstr "%(num)d yaklaşık eşleşme"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Gönüllülük ve Ödüller"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’nın Arşivi sizin gibi gönüllülere dayanır. Tüm bağlılık seviyelerini memnuniyetle karşılıyoruz ve aradığımız iki ana yardım kategorisi var:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Hafif gönüllü çalışması:</span> sadece burada ve orada birkaç saat ayırabiliyorsanız, yine de yardımcı olabileceğiniz birçok yol var. Tutarlı gönüllüleri <span %(bold)s>🤝 Anna’nın Arşivi üyelikleri</span> ile ödüllendiriyoruz."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Yoğun gönüllü çalışma (USD$50-USD$5,000 ödüller):</span> eğer görevimize çok zaman ve/veya kaynak ayırabiliyorsanız, sizinle daha yakından çalışmak isteriz. Sonunda iç ekibe katılabilirsiniz. Sıkı bir bütçemiz olmasına rağmen, en yoğun çalışmalar için <span %(bold)s>💰 parasal ödüller</span> verebiliyoruz."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Zamanınızı gönüllü olarak ayıramıyorsanız, yine de <a %(a_donate)s>para bağışlayarak</a>, <a %(a_torrents)s>torrentlerimizi paylaşarak</a>, <a %(a_uploading)s>kitap yükleyerek</a> veya <a %(a_help)s>Anna’nın Arşivi’ni arkadaşlarınıza anlatarak</a> bize çok yardımcı olabilirsiniz."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Şirketler:</span> koleksiyonlarımıza yüksek hızlı doğrudan erişim sunuyoruz, karşılığında kurumsal düzeyde bağış veya yeni koleksiyonlar (örneğin yeni taramalar, OCR edilmiş datasets, verilerimizi zenginleştirme) talep ediyoruz. Eğer bu sizseniz <a %(a_contact)s>bizimle iletişime geçin</a>. Ayrıca <a %(a_llm)s>LLM sayfamıza</a> bakın."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Hafif gönüllü çalışma"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Boş zamanınız varsa, birçok şekilde yardımcı olabilirsiniz. <a %(a_telegram)s>Telegram’daki gönüllüler sohbetine</a> katılmayı unutmayın."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Bir takdir göstergesi olarak, genellikle temel kilometre taşları için 6 aylık “Şanslı Kütüphaneci” üyeliği veriyoruz ve devam eden gönüllü çalışmalar için daha fazlasını sunuyoruz. Tüm kilometre taşları yüksek kaliteli çalışma gerektirir — özensiz çalışma bize yardımcı olmaktan çok zarar verir ve bunu reddederiz. Bir kilometre taşına ulaştığınızda lütfen <a %(a_contact)s>bize e-posta gönderin</a>."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Görev"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Kilometre Taşı"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Anna'nın Arşivi'nin duyurusunu yapmak. Örneğin, AA'deki kitapları tavsiye ederek, blog yazılarımıza bağlantı vererek veya genel olarak insanları web sitemize yönlendirerek."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s bağlantılar veya ekran görüntüleri."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Bunlar, birine Anna'nın Arşivi'nden bahsettiğinizi ve onların size teşekkür ettiğini göstermelidir."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Üstveriyi <a %(a_metadata)s>Open Library ile bağlantı kurarak</a> iyileştirin."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Başlangıç noktası olarak <a %(a_list)s >rastgele metadata sorunları listesini</a> kullanabilirsiniz."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Düzelttiğiniz sorunlara yorum bırakmayı unutmayın, böylece başkaları işinizi tekrarlamaz."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s iyileştirdiğiniz kayıtların bağlantıları."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Web sitesini çevirme</a>."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Bir dili tamamen çevirin (eğer zaten tamamlanmaya yakın değilse)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Kendi dilinizde Anna’nın Arşivi için Wikipedia sayfasını iyileştirin. Diğer dillerdeki AA’nın Wikipedia sayfasından ve web sitemizden ve blogumuzdan bilgi ekleyin. AA’ya diğer ilgili sayfalarda referanslar ekleyin."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Önemli katkılar yaptığınızı gösteren düzenleme geçmişi bağlantısı."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Z-Library veya Library Genesis forumlarında kitap (veya makale vb.) isteklerini yerine getirme. Kendi kitap istek sistemimiz yok, ancak bu kütüphaneleri yansıtıyoruz, bu yüzden onları daha iyi hale getirmek Anna’nın Arşivi’ni de daha iyi hale getirir."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s yerine getirdiğiniz isteklerin bağlantıları veya ekran görüntüleri."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "<a %(a_telegram)s>Telegram’daki gönüllüler sohbetinde</a> yayınlanan küçük görevler. Genellikle üyelik için, bazen küçük ödüller için."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Gönüllü sohbet grubumuzda küçük görevler paylaşılıyor."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Göreve bağlıdır."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Ödüller"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Sağlam programlama veya saldırgan güvenlik becerilerine sahip insanları her zaman arıyoruz. İnsanlığın mirasını korumada ciddi bir katkı sağlayabilirsiniz."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Teşekkür olarak, sağlam katkılar için üyelik veriyoruz. Büyük bir teşekkür olarak, özellikle önemli ve zor görevler için parasal ödüller veriyoruz. Bu, bir işin yerine geçecek bir şey olarak görülmemelidir, ancak ek bir teşvik ve oluşan maliyetlere yardımcı olabilir."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Kodlarımızın çoğu açık kaynaklıdır ve ödül verirken sizin kodunuzun da açık kaynaklı olmasını isteyeceğiz. Bireysel olarak tartışabileceğimiz bazı istisnalar vardır."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Ödüller, bir görevi ilk tamamlayan kişiye verilir. Başkalarına bir şey üzerinde çalıştığınızı bildirmek için bir ödül biletine yorum yapmaktan çekinmeyin, böylece başkaları bekleyebilir veya sizinle işbirliği yapabilir. Ancak, başkalarının da üzerinde çalışmakta ve sizi geçmeye çalışmakta özgür olduğunu unutmayın. Ancak, özensiz işler için ödül vermiyoruz. İki yüksek kaliteli gönderim birbirine yakın zamanda yapılırsa (bir veya iki gün içinde), takdirimize bağlı olarak her iki gönderime de ödül verebiliriz, örneğin ilk gönderim için 100%% ve ikinci gönderim için 50%% (toplamda 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Daha büyük ödüller (özellikle scraping ödülleri) için, yaklaşık ~5%% tamamladığınızda ve yönteminizin tam hedefe ulaşacağını düşündüğünüzde bizimle iletişime geçin. Yönteminizi bizimle paylaşmanız gerekecek, böylece geri bildirim verebiliriz. Ayrıca, bu şekilde birden fazla kişinin ödüle yaklaşması durumunda ne yapacağımıza karar verebiliriz, örneğin ödülü birden fazla kişiye vermek, insanları işbirliğine teşvik etmek gibi."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "UYARI: yüksek ödüllü görevler <span %(bold)s>zordur</span> — daha kolay olanlarla başlamak akıllıca olabilir."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "<a %(a_gitlab)s>Gitlab sorun listemize</a> gidin ve “Etiket önceliği”ne göre sıralayın. Bu, önemsediğimiz görevlerin yaklaşık sırasını gösterir. Açıkça ödül belirtilmeyen görevler hala üyelik için uygundur, özellikle “Kabul Edildi” ve “Anna’nın favorisi” olarak işaretlenenler. “Başlangıç projesi” ile başlamak isteyebilirsiniz."

#, fuzzy
msgid "blog.template.subheading"
msgstr "İnsanlık tarihindeki en büyük gerçekten açık kütüphane olan <a %(wikipedia_annas_archive)s>Anna’nın Arşivi</a> hakkında güncellemeler."

msgid "layout.index.title"
msgstr "Anna'nın Arşivi"

msgid "layout.index.meta.description"
msgstr "Dünyanın en büyük açık-kaynaklı açık-veri kütüphanesi. Sci-Hub, Library Genesis, Z-Library ve daha fazlasını kopyalar."

msgid "layout.index.meta.opensearch"
msgstr "Anna’nın Arşivi'ni Ara"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’nın Arşivi sizin yardımınıza ihtiyaç duyuyor!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Birçok kişi bizi durdurmaya çalışıyor, ama biz karşılık veriyoruz."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Şimdi bağış yaparsanız, hızlı indirme sayınızı <strong>iki katına</strong> çıkarırsınız."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Bu ayın sonuna kadar geçerlidir."

msgid "layout.index.header.nav.donate"
msgstr "Bağış yapın"

msgid "layout.index.header.banner.holiday_gift"
msgstr "İnsan bilgisini kurtarmak: harika bir tatil hediyesi!"

msgid "layout.index.header.banner.surprise"
msgstr "Sevdiklerinize sürpriz yapın, ona üyelikli bir hesap verin."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Anna’nın Arşivi'nin dayanıklılığını artırmak için, aynaları çalıştıracak gönüllüler arıyoruz."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Mükemmel Sevgililer Günü hediyesi!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Yeni bir bağış yapma yöntemi mevcut: %(method_name)s Lütfen %(donate_link_open_tag)sbağış</a> yapmayı düşünün. Yaptığınız bağışlar sayesinde bu siteyi ayakta tutabiliyoruz. Çok teşekkür ederiz."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "We’re running a fundraiser for <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">backing up</a> the largest comics shadow library in the world. Thanks for your support! <a href=\"/donate\">Bağış yap.</a> Eğer bağış yapamıyorsanız, arkadaşlarınıza söyleyerek ve bizi <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>'te veya <a href=\"https://t.me/annasarchiveorg\">Telegram</a>'da takip ederek bizi destekleyebilirsiniz."

msgid "layout.index.header.recent_downloads"
msgstr "Son indirmeler:"

msgid "layout.index.header.nav.search"
msgstr "Arama"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "SSS"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Üstveriyi iyileştir"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Gönüllülük ve Ödüller"

msgid "layout.index.header.nav.datasets"
msgstr "Veri kümeleri"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrentler"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Etkinlik"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Kod Gezgini"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM verisi"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Anasayfa"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna'nın Yazılımı ↗"

msgid "layout.index.header.nav.translate"
msgstr "Çevir ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Giriş yap / Kaydol"

msgid "layout.index.header.nav.account"
msgstr "Hesap"

msgid "layout.index.footer.list1.header"
msgstr "Anna'nın Arşivi"

msgid "layout.index.footer.list2.header"
msgstr "İletişim"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / telif hakkı talepleri"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Gelişmiş"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Güvenlik"

msgid "layout.index.footer.list3.header"
msgstr "Alternatifler"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "GECEKONDU (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "bağlantısız"

msgid "page.search.results.issues"
msgstr "❌ Bu dosyada hatalar olabilir."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Hızlı indirme"

msgid "page.donate.copy"
msgstr "kopyala"

msgid "page.donate.copied"
msgstr "kopyalandı!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Önceki"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Sonraki"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Ayna #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "İnsanlığın yazılı mirasının %%5'i sonsuza dek korunuyor %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Veri Kümeleri ▶ Dosyalar ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Şuradan ücretsiz e-kitap/dosya %(extension)s'u indirin:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Bağlantılardan birinin çalışmaması durumunda birden fazla indirme seçeneğimiz mevcuttur. Hepsi aynı dosyaya sahiptir."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Anna'nın Arşivi'nin buradaki içeriğin hiçbirini barındırmadığını unutmayın. Sadece başkalarının web sitelerine bağlantı veriyoruz. Geçerli bir telif hakkı şikayetiniz olduğunu düşünüyorsanız, lütfen %(about_link)shakkında sayfasına</a> bakın."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Anonim Mirror #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Bağış yapın"

#~ msgid "page.donate.header"
#~ msgstr "Bağış yapın"

#~ msgid "page.donate.text1"
#~ msgstr "Anna'nın arşivi, kâr amacı gütmeyen, açık kaynaklı bir projedir ve tamamen gönüllüler tarafından işletilmektedir. Barındırma, alan adları, geliştirme ve diğer masrafları içeren maliyetlerimizi karşılamak için bağış topluyoruz."

#~ msgid "page.donate.text2"
#~ msgstr "Katkılarınız sayesinde bu siteyi çalışır halde tutabiliyor, özelliklerini geliştirebiliyor ve daha fazla koleksiyonu muhafaza edebiliyoruz."

#~ msgid "page.donate.text3"
#~ msgstr "Son bağışlar: %(donations)s. Cömertliğiniz için hepinize teşekkür ederiz. Bize güvendiğiniz için, ayırabileceğiniz miktar ne olursa olsun, gerçekten minnettarız."

#~ msgid "page.donate.text4"
#~ msgstr "Bağış yapmak için aşağıdan tercih ettiğiniz yöntemi seçin. Herhangi bir sorunla karşılaşırsanız, lütfen %(email)s adresinden bizimle iletişime geçin."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Kredi/banka kartı"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Kripto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Sorular"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "%(link_open_tag)sthis page</a> adresine gidin ve QR kodunu tarayarak veya \"paypal.me\" bağlantısına tıklayarak verilen talimatları uygulayın. Çalışmazsa, sayfayı yenilemeyi deneyin, zira bu size farklı bir hesap sunabilir."

#~ msgid "page.donate.cc.header"
#~ msgstr "Kredi/banka kartı"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Parayı doğrudan Bitcoin (BTC) cüzdanımıza yatırmak için Sendwyre kullanıyoruz. Bu işlemin tamamlanması yaklaşık 5 dakika kadar sürebilir."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Bu yöntemin minimum işlem tutarı 30 ABD doları ve işlem ücreti yaklaşık 5 ABD dolarıdır."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Adımlar:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Bitcoin (BTC) cüzdan adresimizi kopyalayın: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. %(link_open_tag)s Bu sayfaya</a> gidin ve \"buy crypto instantly\" butonuna tıklayın"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Cüzdan adresimizi yapıştırın ve talimatları izleyin"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Kripto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(BCH için de çalışır)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Lütfen bağışınızı göndermek için bu %(link_open_tag)sAlipay</a> hesabını kullanın. Eğer çalışmıyorsa, sayfayı yenileyip tekrar deneyin."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Bu bağış seçeneği şimdilik çalışmıyor. Lütfen daha sonra tekrar kontrol edin. Bağış yapmak istediğiniz için çok teşekkür ederiz!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Bağış yapmak için lütfen %(link_open_tag)sbu Pix sayfasını</a> kullanın. Eğer çalışmıyorsa, sayfayı yenilemeyi deneyin, böylece size farklı bir hesap verilebilir."

#~ msgid "page.donate.faq.header"
#~ msgstr "Sıkça sorulan sorular"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna'nın Arşivi</span>, var olan tüm kitapları, çeşitli kaynaklardan toplayarak kataloglamayı hedefleyen bir projedir. Aynı zamanda tüm bu kitaplara dijital erişimi <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">saklı kütüphaneler </a> aracılığı ile gerçekleştirmek istiyoruz. <a href=\"/about\">Daha fazla bilgi için...</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Kitap (tümü)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Ana Sayfa"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Veri Kümeleri ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Bulunamadı"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "\"%(isbn_input)s\" geçerli bir ISBN numarası değil. ISBN'ler kesme işaretini veya kısa çizgileri saymazsanız 10 veya 13 karakterden oluşur. Son karakter hariç, bütün karakterler sayı olmalıdır, son karakter \"X\" olabilir. Son karakter kontrol sayısı olarak geçmektedir. Bu bir checksum üzerinden diğer sayıların kontrolünü yapılmasını sağlar. Ayrıca bu sayı Uluslararası ISBN Ajansının belirttiği uygun bir aralıkta olmalıdır."

#~ msgid "page.isbn.results.text"
#~ msgstr "Veritabamızda eşleşen kayıtlar:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Veritabanımızda eşleşen kayıt bulunamadı."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Arama ▶ <span class=\"italic\">%(search_input)s</span> için %(num)d+ sonuç bulundu (gölge kütüphane üstverisinde)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Arama ▶ <span class=\"italic\">%(search_input)s</span> için %(num)d sonuç bulundu (gölge kütüphane üstverisinde)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Arama ▶ <span class=\"italic\">%(search_input)s</span> için arama hatası"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Arama ▶ Yeni arama"

#~ msgid "page.donate.header.text3"
#~ msgstr "Ayrıca bir hesap oluşturmadan da bağış yapabilirsin:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Tek seferlik bağışlar (avantajlar yok)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Bir ödeme seçeneği seçin. Lütfen kripto-bazlı bir ödeme kullanmayı dikkate alın %(bitcoin_icon)s, çünkü (çok daha) az ücret kaybına uğrarız."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Zaten kripto paranız varsa adreslerimiz şunlardır."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Yardımlarınız için çok teşekkür ederiz! Bu proje, sizler olmadan mümkün olmazdı."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Paypal ABD kullanarak bağış yapmak için, Paypal Crypto kullanacağız, bu bizim anonim kalmamızı sağlıyor. Bu bağış metodu bize çok yardımcı olduğundan, öğrenmeye zaman ayırmanıza minnettarız."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Bitcoin (BTC) almak için talimatları takip edin. Yalnızca bağış yapmak istediğiniz miktarı satın almanız lazım."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Eğer dalgalanmalardan ya da harçlardan dolayı biraz Bitcoin kaybederseniz, <em>lütfen endişelenmeyin</em>. Bu kripto para için normal, ancak bizim anonim şekilde işlememize izin veriyor."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Alıcı olarak Bitcoin (BTC) adresimizi girin ve bağışınızı göndermek için adımları takip edin:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Bağışınızı göndermek için lütfen <a %(a_account)s>bu Alipay hesabını</a> kullanın."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Bağışınızı göndermek için lütfen <a %(a_account)s>bu Pix hesabını</a> kullanın."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Eğer ödeme metodunuz listede değil ise, yapabileceğiniz en basit aktarım şekli telefonunuza <a href=\"https://paypal.com/\">PayPal</a> veya <a href=\"https://coinbase.com/\">Coinbase</a> 'i indirip bir miktar Bitcoin(BTC) satın almanız. Daha sonra %(address)s hesabımıza gönderebilirsiniz. Ülkelerin çoğunda bu şekilde para aktarımı dakikalar içerisinde gerçekleşir."

#~ msgid "page.search.results.error.text"
#~ msgstr "<a href=\"javascript:location.reload()\">Sayfayı yenilemeyi deneyin.</a> Eğer hata hala devam ediyorsa lütfen bize haber verin: <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> veya <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Üye olmak için lütfen <a href=\"/login\">Giriş Hap ya da Hesap Oluştur</a>. Eğer hesap açmamayı tercih editorsan, üstten \"Tek seferlik anonim bağış yap\" seçeneğini seç. Desteğin için teşekkürler!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Ana Sayfa"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Hakkında"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Bağış yapın"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Veri kümeleri"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Mobil uygulama"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna'nın Blog'u"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna'nın Yazılımı"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Çevir"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr "Kitapları, makaleleri, çizgi romanları, dergileri ve daha fazlasını; bu materyalleri çeşitli <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">saklı kütüphanelerden</a> tek bir yerde bir araya getirerek saklarız. Tüm bu veri toplu olarak çoğaltmayı kolaylaştırarak sonsuza kadar saklanır ve tüm dünyada birçok kopyayla sonuçlanır. Bu geniş dağıtım, açık kaynak kod ile birleştiğinde, ayrıca sitemizi ele geçirmelere karşı dayanıklı yapar. <a href=\"/datasets\">Veri setlerimiz</a> hakkında daha fazlasını öğrenin."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Veri Kümeleri ▶ DOI'ler ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Bulunamadı"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" bir DOI gibi görünmüyor. \"10\" ile başlamalı ve eğik çizgi (slaş) içermeli."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Kanonik URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Bu dosya %(link_open_tag)sSci-Hub</a> sayfasında mevcut olabilir."

#~ msgid "page.doi.results.text"
#~ msgstr "Veri tabanımızda eşleşen dosyalar:"

#~ msgid "page.doi.results.none"
#~ msgstr "Veri tabanımızda eşleşen dosya bulunamadı."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Hızlı indirmeler</strong> Bugünlük hızlı indirmeleri tükettiniz. Eğer üyeliğinizi yükseltmekle ilgileniyorsanız %(email)s adresinden Anna ile iletişime geçin."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Bugünlük hızlı indirmeleri tükettiniz. Eğer üyeliğinizi yükseltmekle ilgileniyorsanız, %(email)s adresinden Anna ile iletişime geçin."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Başka yollardan yardımcı olabilir miyim?</div> Evet! Lütfen <a href=\"/about\">hakkımızda sayfasında</a> \"Nasıl yardım edebilirim?\" kısmına bakınız."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Anna'nın Arşivini “paralandırmanızdan” hoşlanmıyorum!</div> Eğer projemizi nasıl işlettiğimiz hoşunuza gitmiyorsa, kendi gölge kütüphanenizi işletin! Kodumuzun ve verimizintamamı açık kaynaklı, yani sizi durduran bir şey yok. ;)"

#~ msgid "page.request.title"
#~ msgstr "Kitap talep et"

#~ msgid "page.request.text1"
#~ msgstr "Şimdilik, lütfen eKitapları <a %(a_forum)s>Libgen.rs forumunda</a> talep edebilir misin? Orada hesap oluşturup bu başlıkların altında paylaşabilirsiniz:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>eKitaplar için, <a %(a_ebook)s>bu başlığı</a> kullanın.</li><li %(li_item)s>eKitap olarak mevcut olmayan kitaplar için, <a %(a_regular)s>bu başlığı</a> kullanın.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Her iki durumda, başlıklarda bahsedilen kurallara uyduğunuzdan emin olun."

#~ msgid "page.upload.title"
#~ msgstr "Yükle"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Hakkında"

#~ msgid "page.about.header"
#~ msgstr "Hakkında"

#~ msgid "page.home.search.header"
#~ msgstr "Arama"

#~ msgid "page.home.search.intro"
#~ msgstr "Saklı kütüphane kataloglarımızda arama yapın."

#~ msgid "page.home.random_book.header"
#~ msgstr "Rastgele Kitap"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Katalogdan rastgele bir kitaba gidin."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Rastgele Kitap"

#~ msgid "page.about.text1"
#~ msgstr "Anna'nın Arşivi kâr amacı gütmeyen, açık kaynaklı bir <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">gölge kütüphane </a>arama motorudur. Kitaplar, makaleler, çizgi romanlar, dergiler ve diğer belgeler için merkezi bir arama noktası olması gerektiğini hisseden biri olan <a href=\"http://annas-blog.org\">Anna</a> tarafından yaratıldı."

#~ msgid "page.about.text4"
#~ msgstr "Geçerli bir telif hakkı şikayetiniz varsa, bu sayfanın alt kısmına bakın veya %(email)s adresinden bize ulaşın."

#~ msgid "page.home.explore.header"
#~ msgstr "Kitaplara göz atın"

#~ msgid "page.home.explore.intro"
#~ msgstr "Bu kitap seçkisi popüler kitaplar ile saklı kütüphane dünyasında büyük önem taşıyan ve dijital olarak korunması gereken kitaplardan oluşur."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Hakkında"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Mobil uygulama"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Unofficial WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Kitap talep et"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Yükle"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Nasıl yardım edebilirim"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Toplam %(total)s tutarını <a %(a_account)s>bu Alipay hesabını kullanarak bağışlayın"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "sadece bu ay!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub yeni makalelerin yüklenmesini <a %(a_closed)s>durdurdu</a>."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Bir ödeme yöntemi seçin. Kripto-bazlı ödemelerde %(bitcoin_icon)s indirim yapıyoruz çünkü daha az komisyona maruz kalıyoruz."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Bir ödeme seçeneği seçin. Şu anda sadece kripto bazlı ödeme yöntemlerimiz var %(bitcoin_icon)s, geleneksel ödeme işleyicileri bizle çalışmayı reddetmekte."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Bankalar bizimle çalışmak istemediği için kredi/banka kartlarını doğrudan destekleyemiyoruz. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Ancak, diğer ödeme yöntemlerimizi kullanarak yine de kredi/banka kartlarını kullanmanın birkaç yolu vardır:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Yavaş ve dış indirmeler"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "İndirmeler"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Eğer kripto parayı ilk kez kullanıyorsanız, Bitcoin (orijinal ve en çok kullanılan kripto para) satın almak ve bağışlamak için %(option1)s, %(option2)s ya da %(option3)s kullanmanızı öneririz."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "İyileştirdiğiniz 30 kayıt bağlantısı."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 bağlantı veya ekran görüntüsü."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "Yerine getirdiğiniz isteklerin 30 bağlantısı veya ekran görüntüsü."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Bu datasets'i <a %(a_faq)s>arşivleme</a> veya <a %(a_llm)s>LLM eğitimi</a> amaçları için yansıtmakla ilgileniyorsanız, lütfen bizimle iletişime geçin."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Bu veri setini <a %(a_archival)s>arşivleme</a> veya <a %(a_llm)s>LLM eğitimi</a> amaçları için yansıtmakla ilgileniyorsanız, lütfen bizimle iletişime geçin."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Ana web sitesi"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN ülke bilgisi"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Bu veri setini <a %(a_archival)s>arşivleme</a> veya <a %(a_llm)s>LLM eğitimi</a> amaçları için yansıtmakla ilgileniyorsanız, lütfen bizimle iletişime geçin."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Uluslararası ISBN Ajansı, ulusal ISBN ajanslarına tahsis ettiği aralıkları düzenli olarak yayınlar. Bu verilerden, bu ISBN'nin hangi ülkeye, bölgeye veya dil grubuna ait olduğunu çıkarabiliriz. Şu anda bu verileri dolaylı olarak, <a %(a_isbnlib)s>isbnlib</a> Python kütüphanesi aracılığıyla kullanıyoruz."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Kaynaklar"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Son güncelleme: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN web sitesi"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Üstveri"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "“scimag” hariç"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Üstveri toplama ilhamımız, Aaron Swartz’ın “yayınlanmış her kitap için bir web sayfası” hedefidir; bu amaçla <a %(a_openlib)s>Open Library</a>'yi oluşturdu."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "O proje iyi iş çıkardı, ancak benzersiz konumumuz onların elde edemediği üstverileri almamıza olanak tanıyor."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Bir diğer ilham kaynağımız, <a %(a_blog)s>dünyada kaç kitap olduğunu</a> bilme arzumuzdu, böylece kurtarmamız gereken kaç kitap kaldığını hesaplayabiliriz."

#~ msgid "page.partner_download.text1"
#~ msgstr "Herkese dosyaları ücretsiz indirme fırsatı vermek için, bu dosyayı indirmeden önce <strong>%(wait_seconds)s saniye</strong> beklemeniz gerekiyor."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Sayfayı otomatik olarak yenileyin. İndirme penceresini kaçırırsanız, zamanlayıcı yeniden başlayacaktır, bu yüzden otomatik yenileme önerilir."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Şimdi indir"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Dönüştür: formatlar arasında dönüştürmek için çevrimiçi araçları kullanın. Örneğin, epub ve pdf arasında dönüştürmek için <a %(a_cloudconvert)s>CloudConvert</a>'i kullanın."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: dosyayı indirin (pdf veya epub desteklenir), ardından web, uygulama veya e-posta kullanarak <a %(a_kindle)s>Kindle'a gönderin</a>. Yardımcı araçlar: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Yazarları destekleyin: Eğer bunu beğendiyseniz ve maddi gücünüz yetiyorsa, orijinalini satın almayı veya doğrudan yazarları desteklemeyi düşünün."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Kütüphaneleri destekleyin: Bu yerel kütüphanenizde mevcutsa, oradan ücretsiz ödünç almayı düşünün."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Doğrudan toplu olarak mevcut değil, yalnızca bir ödeme duvarının arkasında yarı toplu olarak mevcut"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’nın Arşivi bir <a %(isbndb)s>ISBNdb üstveri</a> koleksiyonunu yönetiyor"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb, çeşitli çevrimiçi kitapçılardan ISBN üstverilerini toplayan bir şirkettir. Anna’nın Arşivi, ISBNdb kitap üstverilerinin yedeklerini almaktadır. Bu üstveri Anna’nın Arşivi aracılığıyla erişilebilir (ancak şu anda aramada değil, yalnızca bir ISBN numarası ararsanız)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Teknik detaylar için aşağıya bakın. Bir noktada, gölge kütüphanelerde hangi kitapların eksik olduğunu belirlemek için kullanabiliriz, böylece hangi kitapları bulup/taramamız gerektiğine öncelik verebiliriz."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Bu veri hakkında blog yazımız"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb kazıma"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Şu anda tek bir torrentimiz var, 4.4GB sıkıştırılmış <a %(a_jsonl)s>JSON Lines</a> dosyasını (20GB sıkıştırılmamış) içermektedir: “isbndb_2022_09.jsonl.gz”. Bir “.jsonl” dosyasını PostgreSQL'e aktarmak için, <a %(a_script)s>bu script</a> gibi bir şey kullanabilirsiniz. Hatta %(example_code)s gibi bir şey kullanarak doğrudan boru hattı ile sıkıştırmayı anında açabilirsiniz."

#~ msgid "page.donate.wait"
#~ msgstr "Lütfen bize ulaşmadan önce en az <span %(span_hours)s>iki saat</span> bekleyin (ve bu sayfayı yenileyin)."

#~ msgid "page.codes.search_archive"
#~ msgstr "“%(term)s” için Anna’nın Arşivi'nde ara"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Alipay veya WeChat'i kullanarak bağış yapın. Bir sonraki sayfada bunlar arasında seçim yapabilirsiniz."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Anna’nın Arşivi’ni sosyal medyada ve çevrimiçi forumlarda yaymak, AA’da kitap veya listeler önermek veya soruları yanıtlamak."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Kurgu koleksiyonu farklılaştı ama hala <a %(libgenli)s>torrentler</a> var, ancak 2022'den beri güncellenmedi (doğrudan indirmelerimiz var)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna’nın Arşivi ve Libgen.li, <a %(comics)s>çizgi romanlar</a> ve <a %(magazines)s>dergiler</a> koleksiyonlarını işbirliği içinde yönetir."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Rus kurgu ve standart belge koleksiyonları için torrent yok."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Ek içerik için mevcut torrent yok. Libgen.li web sitesindeki torrentler, burada listelenen diğer torrentlerin aynalarıdır. Tek istisna, %(fiction_starting_point)s’den başlayan kurgu torrentleridir. Çizgi roman ve dergi torrentleri, Anna’nın Arşivi ve Libgen.li arasında bir iş birliği olarak yayınlanmaktadır."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "<a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> koleksiyonundan, kesin kökeni belirsiz. Kısmen the-eye.eu'dan, kısmen diğer kaynaklardan."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

