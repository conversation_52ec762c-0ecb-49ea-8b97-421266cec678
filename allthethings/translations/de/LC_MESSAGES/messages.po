msgid "layout.index.invalid_request"
msgstr "Ungültige Anfrage. Besuche %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Leihbibliothek"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " und "

msgid "layout.index.header.tagline_and_more"
msgstr "und mehr"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Wir spiegeln %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Wir scrapen und frei veröffentlichen %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Alle unsere Daten und unser Code sind quelloffen."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Die weltweit größte, frei verfügbare Open-Source-Bibliothek."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;Bücher, %(paper_count)s&nbsp;wissenschaftliche Aufsätze — für immer erhalten."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Die weltweit größte, frei verfügbare Open-Source-Bibliothek. ⭐️&nbsp; Enthält Sci-Hub, Library Genesis, Z-Library und mehr. 📈&nbsp;%(book_any)s Bücher, %(journal_article)s wissenschaftliche Aufsätze, %(book_comic)s Comics, %(magazine)s Zeitschriften - für immer erhalten."

msgid "layout.index.header.tagline_short"
msgstr "📚 Die weltweit größte, frei verfügbare Open-Source-Bibliothek. ⭐️ Enthält Sci-Hub, Libgen, Zlib, und mehr."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Inkorrekte Metadaten (z.B. Titel, Beschreibung, Cover)"

msgid "common.md5_report_type_mapping.download"
msgstr "Probleme mit dem Download (z.B Verbindungsprobleme, Fehlermeldung, sehr langsames Internet)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Datei kann nicht geöffnet werden (z.B weil sie korrumpiert ist oder wegen eines Kopierschutzes)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Schlechte Qualität (z.B. Probleme mit dem Format, schlechter Scan, fehlende Seiten)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / Datei sollte entfernt werden (z.B. wegen Werbung, missbräuchliche Inhalte)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Urheberrechtsanspruch"

msgid "common.md5_report_type_mapping.other"
msgstr "Anderes"

msgid "common.membership.tier_name.bonus"
msgstr "Bonusdownloads"

msgid "common.membership.tier_name.2"
msgstr "Brillanter Bücherwurm"

msgid "common.membership.tier_name.3"
msgstr "Glücklicher Bibliothekar"

msgid "common.membership.tier_name.4"
msgstr "Schillernder Datenschützer"

msgid "common.membership.tier_name.5"
msgstr "Beeindruckender Archivar"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s Gesamt"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) Gesamt"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s Bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "unbezahlt"

msgid "common.donation.order_processing_status_labels.1"
msgstr "bezahlt"

msgid "common.donation.order_processing_status_labels.2"
msgstr "abgebrochen"

msgid "common.donation.order_processing_status_labels.3"
msgstr "abgelaufen"

msgid "common.donation.order_processing_status_labels.4"
msgstr "wartet auf Annas Bestätigung"

msgid "common.donation.order_processing_status_labels.5"
msgstr "ungültig"

msgid "page.donate.title"
msgstr "Spenden"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Es existiert bereits eine <a %(a_donation)s>offene Spende</>. Bitte diese Spende abschließen, bevor du eine neue tätigst."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Alle meine Spenden ansehen</a>"

msgid "page.donate.header.text1"
msgstr "Annas Archiv ist ein gemeinnütziges Open-Source und Open-Data Projekt. Durch deine Spende wirst du ein Mitglied und unterstützt den Betrieb und die Entwicklung von Annas Archiv. An alle Mitglieder: Vielen Dank, dass ihr das Projekt am Laufen haltet! ❤️"

msgid "page.donate.header.text2"
msgstr "Für weitere Informationen, siehe <a %(a_donate)s>Spenden FAQ</a>."

msgid "page.donate.refer.text1"
msgstr "Um noch mehr Downloads zu bekommen, <a %(a_refer)s>lade Freunde ein</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Du erhältst %(percentage)s%% schnelle Bonusdownloads, weil du von %(profile_link)s eingeladen wurdest."

msgid "page.donate.bonus_downloads.period"
msgstr "Dies gilt für den gesamten Zeitraum der Mitgliedschaft."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s schnelle Downloads pro Tag"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "wenn du diesen Monat spendest!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / Monat"

msgid "page.donate.buttons.join"
msgstr "Beitreten"

msgid "page.donate.buttons.selected"
msgstr "Selektiert"

msgid "page.donate.buttons.up_to_discounts"
msgstr "bis zu %(percentage)s%% Ermäßigung"

msgid "page.donate.perks.scidb"
msgstr "SciDB-Aufsätze <strong>unbegrenzt</strong> ohne Überprüfung"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API-Zugang</a>"

msgid "page.donate.perks.refer"
msgstr "Erhalte <strong>%(percentage)s%% Bonusdownloads</strong> wenn du <a %(a_refer)s>Freunde einlädst</a>."

msgid "page.donate.perks.credits"
msgstr "Dein Benutzername oder anonyme Nennung in den Credits"

msgid "page.donate.perks.previous_plus"
msgstr "Vorherige Mitgliedervorteile, plus:"

msgid "page.donate.perks.early_access"
msgstr "Frühzeitiger Zugang zu neuen Features"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Exklusiver Telegram-Zugriff mit hinter den Kulissen Updates"

msgid "page.donate.perks.adopt"
msgstr "“Adoptiere ein Torrent”: Dein Benutzername oder eine Nachricht deiner Wahl im Dateinamen <div %(div_months)s>einmal pro 12 Monate Mitgliedschaft</div>"

msgid "page.donate.perks.legendary"
msgstr "Legendenstatus für die Bewahrung des Wissens und der Kultur der Menschheit"

msgid "page.donate.expert.title"
msgstr "Expertenzugang"

msgid "page.donate.expert.contact_us"
msgstr "Kontaktiere uns"

msgid "page.donate.small_team"
msgstr "Wir sind ein kleines Team an Freiwilligen. Deshalb könnte es durchaus 1-2 Wochen dauern, bis wir antworten."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Unbeschränkter</strong> Highspeed-Zugang"

msgid "page.donate.expert.direct_sftp"
msgstr "Direktzugang <strong>SFTP</strong> Server"

msgid "page.donate.expert.enterprise_donation"
msgstr "Gewerbliche Spende oder im Austausch für neue Sammlungen (z. B. neue Scans, OCR-generierte Datensätze)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Wir freuen uns über großzügige Spenden von wohlhabenden Einzelpersonen oder Institutionen. "

msgid "page.donate.header.large_donations"
msgstr "Für Spenden von mehr als 5000$ kontaktiere uns bitte direkt über %(email)s."

msgid "page.donate.header.recurring"
msgstr "Beachte, dass die Mitgliedschaften auf dieser Seite „pro Monat“ angeführt sind, es sich jedoch um einmalige Spenden (nicht wiederkehrend) handelt. Siehe dazu die <a %(faq)s>Spenden FAQ</a>."

msgid "page.donate.without_membership"
msgstr "Wenn du eine Spende (in beliebiger Höhe) ohne Mitgliedschaft tätigen möchtest, kannst du die folgende Monero (XMR)-Adresse verwenden: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Bitte wähle eine Zahlungsmethode."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporär nicht verfügbar)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s Geschenkkarte"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkarte (mit App)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kryptowährungen %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit/Debitkarte"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regulär)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Karte / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit-/Debitkarte/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkarte"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit-/Debitkarte (Backup)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit/Debitkarte 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Spenden in Kryptowährungen kannst du mit BTC, ETH, XMR, und SOL machen. Verwende diese Option wenn du dich mit Kryptowährungen auskennst."

msgid "page.donate.payment.desc.crypto2"
msgstr "Mit Kryptowährung kannst du mit BTC, ETH, XMR, und anderen bezahlen."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Wenn du zum ersten Mal Krypto verwendest, empfehlen wir dir, %(options)s zu verwenden, um Bitcoin (die ursprüngliche und am häufigsten verwendete Kryptowährung) zu kaufen und zu spenden."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Für PayPal-Spenden verwenden wir PayPal Crypto, was uns erlaubt anonym zu bleiben. Wir sind sehr dankbar, dass du dir die Zeit nimmst auf diese Weise zu spenden, da uns das eine grosse Hilfe ist."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Spende mit PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Spende mit Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Falls du die Cash App hast, wäre das der einfachste Weg um zu spenden!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Für Transaktionen unter %(amount)s, nimmt Cash App eine %(fee)s Gebühr. Alles über %(amount)s ist kostenlos!"

msgid "page.donate.payment.desc.revolut"
msgstr "Spenden mit Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Wenn du Revolut hast, ist dies der einfachste Weg zu spenden!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Spende mit Kredit oder Debitkarte."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay oder Apple Pay können auch funktionieren."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Für kleinere Beträge eliminieren die Kreditkartengebühren eventuell unseren %(discount)s%% Rabatt, deshalb schlagen wir längere Abos vor."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Bei kleineren Spenden sind die Gebühren hoch, also empfehlen wir längere Abos."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Bei Binance kaufst du Bitcoin mit einer Kredit-/Debitkarte oder einem Bankkonto und spendest diese Bitcoin dann an uns. Auf diese Weise können wir bei der Annahme deiner Spende sicher und anonym bleiben."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance ist in fast jedem Land verfügbar und unterstützt die meisten Banken und Kredit-/Debitkarten. Dies ist derzeit unsere Empfehlung. Wir freuen uns, dass du dir die Zeit nimmst, zu lernen mit dieser Methode zu spenden, da sie uns sehr hilft."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Spenden mit einem regulären PayPal-Konto."

msgid "page.donate.payment.desc.givebutter"
msgstr "Spende mit Kredit-/Debitkarte, PayPal oder Venmo. Zwischen diesen Optionen kannst du auf der nächsten Seite wählen."

msgid "page.donate.payment.desc.amazon"
msgstr "Spende mit einem Amazon Gutschein."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Beachte, dass wir auf Beträge aufrunden müssen, die unsere Reseller akzeptieren ((Minimum %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>WICHTIG:</strong> Es funktioniert ausschließlich mit Amazon.com, nicht mit anderen Amazon Websites, z.B .de, .co.uk, oder .ca."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>WICHTIG:</strong> Diese Option ist für %(amazon)s. Wenn du eine andere Amazon-Website verwenden möchtest, wählen diese oben aus."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Bei dieser Methode wird ein Kryptowährungsanbieter als Zwischenkonvertierung verwendet. Dies kann etwas verwirrend sein, daher verwende diese Methode bitte nur, wenn andere Zahlungsmethoden nicht funktionieren. Es funktioniert auch nicht in allen Ländern."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Spenden mit einer Kredit-/Debitkarte über die Alipay-App (super einfach einzurichten)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Alipay-App installieren"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installiere die Alipay-App aus dem <a %(a_app_store)s>Apple App Store</a> oder dem <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registriere dich mit deiner Telefonnummer."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Weitere persönliche Daten sind nicht erforderlich."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Bankkarte hinzufügen"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Unterstützt: Visa, MasterCard, JCB, Diners Club und Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Siehe <a %(a_alipay)s>diesen Leitfaden</a> für weitere Informationen."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Wir können Kredit-/Debitkarten nicht direkt unterstützen, da Banken nicht mit uns zusammenarbeiten wollen. ☹ Es gibt jedoch mehrere Möglichkeiten, Kredit-/Debitkarten trotzdem zu verwenden, indem du andere Zahlungsmethoden nutzt:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Gutschein"

msgid "page.donate.ccexp.amazon_com"
msgstr "Sende uns Amazon.com Geschenkkarten mit deiner Kredit-/Debitkarte."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay unterstützt internationale Kredit-/Debitkarten. Weitere Informationen findest du in <a %(a_alipay)s>diesem Leitfaden</a>."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) unterstützt internationale Kredit-/Debitkarten. Gehe in der WeChat-App zu \"Ich => Dienste => Wallet => Karte hinzufügen\". Solltest du nichts finden, aktiviere es mit \"Ich => Einstellungen => Allgemein => Tools => Weixin Pay => Aktivieren\"."

msgid "page.donate.ccexp.crypto"
msgstr "Du kannst Crypto mit Kredit-/Debitkarten kaufen."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Krypto-Expressdienste"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Expressdienste sind praktisch, erheben jedoch höhere Gebühren."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Du kannst dies anstelle einer Krypto-Börse verwenden, wenn du schnell eine größere Spende tätigen möchtest und eine Gebühr von $5-10 dich nicht stört."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Achte darauf, den genauen Krypto-Betrag zu senden, der auf der Spendenseite angezeigt wird, nicht den Betrag in $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Andernfalls wird die Gebühr abgezogen und wir können deine Mitgliedschaft nicht automatisch verarbeiten."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(Mindestbetrag: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(Mindestbetrag: %(minimum)s je nach Land, keine Verifizierung für die erste Transaktion)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(Mindestbetrag: %(minimum)s, keine Verifizierung für die erste Transaktion)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(Mindestbetrag: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(Mindestbetrag: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(Mindestbetrag: %(minimum)s, keine Verifizierung für die erste Transaktion)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Wenn eine dieser Informationen veraltet ist, sende uns bitte eine E-Mail, um uns dies mitzuteilen."

msgid "page.donate.payment.desc.bmc"
msgstr "Bei Kreditkarten, Debitkarten, Apple Pay und Google Pay verwenden wir \"Buy Me a Coffee\" (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In deren System entspricht ein \"Kaffee\" 5 US-Dollar, sodass deine Spende auf das nächste Vielfache von fünf gerundet wird."

msgid "page.donate.duration.intro"
msgstr "Wähle, wie lange du Mitglied werden willst."

msgid "page.donate.duration.1_mo"
msgstr "1 Monat"

msgid "page.donate.duration.3_mo"
msgstr "3 Monate"

msgid "page.donate.duration.6_mo"
msgstr "6 Monate"

msgid "page.donate.duration.12_mo"
msgstr "12 Monate"

msgid "page.donate.duration.24_mo"
msgstr "24 Monate"

msgid "page.donate.duration.48_mo"
msgstr "48 Monate"

msgid "page.donate.duration.96_mo"
msgstr "96 Monate"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>nach <span %(span_discount)s></span> Ermäßigungen</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Diese Bezahlmethode benötigt einen Spendenbetrag von mindestens %(amount)s. Bitte wähle eine andere Spendenmethode oder einen höheren Betrag aus."

msgid "page.donate.buttons.donate"
msgstr "Spenden"

msgid "page.donate.payment.maximum_method"
msgstr "Diese Bezahlmethode lässt einen Spendenbetrag von höchstens%(amount)s zu. Bitte wähle eine andere Spendenmethode oder einen niedrigeren Betrag aus."

msgid "page.donate.login2"
msgstr "Um Mitglied zu werden, <a %(a_login)s log dich bitte ein oder registriere dich</a>. Danke für deine Unterstützung!"

msgid "page.donate.payment.crypto_select"
msgstr "Wähle deine Kryptowährung aus:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(geringster Minimalbetrag)"

msgid "page.donate.coinbase_eth"
msgstr "(verwende das beim Senden von Ethereum von Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(achtung: hoher Mindestbetrag)"

msgid "page.donate.submit.confirm"
msgstr "Klicke die Spenden Schaltfläche um die Spende zu bestätigen."

msgid "page.donate.submit.button"
msgstr "Spenden <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Du kannst die Spende an der Kasse immer noch abbrechen."

msgid "page.donate.submit.success"
msgstr "✅ Weiterleitung zur Spende-Seite…"

msgid "page.donate.submit.failure"
msgstr "❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / Monat"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "für 1 Monat"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "für 3 Monate"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "für 6 Monate"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "für 12 Monate"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "für 24 Monate"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "für 48 Monate"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "für 96 Monate"

msgid "page.donate.submit.button.label.1_mo"
msgstr "für 1 Monat “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "für 3 Monate “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "für 6 Monate “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "für 12 Monate “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "für 24 Monate “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "für 48 Monate“%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "für 96 Monate “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Spende"

msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Monat für %(duration)s Monate, inklusive %(discounts)s%% Ermäßigung)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Monat für %(duration)s Monate)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identifizierung: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Abbrechen"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Bist Du sicher dass du abbrechen willst? Nicht abbrechen, falls du schon bezahlt hast."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, bitte abbrechen"

msgid "page.donation.header.cancel.success"
msgstr "✅ Deine Spende wurde abgebrochen."

msgid "page.donation.header.cancel.new_donation"
msgstr "Mache eine neue Spende"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut."

msgid "page.donation.header.reorder"
msgstr "Erneut bestellen"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Du hast schon bezahlt. Falls du die Anleitung zur Bezahlung trotzdem anzeigen willst, klicke hier:"

msgid "page.donation.old_instructions.show_button"
msgstr "Zeige die alte Bezahlanleitung an"

msgid "page.donation.thank_you_donation"
msgstr "Danke für deine Spende!"

msgid "page.donation.thank_you.secret_key"
msgstr "Falls du es noch nicht getan hast, notiere dir deinen Geheimcode für die Anmeldung:"

msgid "page.donation.thank_you.locked_out"
msgstr "Andernfalls kann es sein, dass du aus deinem Konto ausgesperrt wirst!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Die Bezahlungsdaten sind veraltet. Falls du eine weitere Spende machen willst, verwende den \"Erneut Spenden\" Button oben."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Wichtiger Hinweis:</strong> Der Preis von Kryptowährungen kann stark fluktuieren, manchmal bis zu 20%% in wenigen Minuten. Das ist immer noch weniger als die Gebühren von 50-60%%, welche viele Zahlungsanbieter für die Zusammenarbeit mit \"Schatten Wohltätigkeitsorganisationen\" wie uns verlangen. <u>Wenn du uns den Beleg mit dem originalen Preis den du bezahlt hast schickst, werden wir dir die gewählte Mitgliedschaft freischalten (so lange der Beleg nicht älter als ein paar Stunden ist)</u>. Wir sind sehr dankbar, dass du solche Unannehmlichkeiten auf dich nimmst, um uns zu unterstützen! ❤️"

msgid "page.donation.expired"
msgstr "Diese Spende ist abgelaufen. Bitte storniere und erstelle eine neue."

msgid "page.donation.payment.crypto.top_header"
msgstr "Krypto Anleitung"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Überweise zu einem unserer Krypto-Konten"

msgid "page.donation.payment.crypto.text1"
msgstr "Spende den kompletten Betrag von %(total)s auf eine dieser Adressen:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Kaufe Bitcoin mit Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Finde die „Crypto“ Seite in deiner PayPal App oder Webseite. Diese ist normalerwise unter „Finanzen“ zu finden."

msgid "page.donation.payment.paypal.text3"
msgstr "Folge der Anleitung, um Bitcoin (BTC) zu kaufen. Du brauchst nur den Wert den du spenden möchtest zu kaufen, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Überweise die Bitcoins an unsere Adresse"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gehe zur „Bitcoin“ Seite in deiner PayPal App oder Webseite. Klicke den \"Überweisen\" Button %(transfer_icon)s, und dann „Senden“."

msgid "page.donation.payment.paypal.text5"
msgstr "Gebe unsere Bitcoin (BTC) Adresse als Empfänger an und folge der Anleitung, um uns die Spende von %(total)s zu senden:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredit/Debitkarten Anleitung"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Spende über unsere Kredit/Debitkartenseite"

msgid "page.donation.donate_on_this_page"
msgstr "Spende %(amount)s auf <a %(a_page)s>dieser Seite</a>."

msgid "page.donation.stepbystep_below"
msgstr "Siehe die Anleitung unten."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Warte auf Bestätigung (zum Prüfen aktualisiere die Seite )…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Warte auf Transfer (zum Prüfen aktualisiere die Seite )…"

msgid "page.donation.time_left_header"
msgstr "Restliche Zeit:"

msgid "page.donation.might_want_to_cancel"
msgstr "(du solltest vielleicht stornieren und eine neue Spende erstellen)"

msgid "page.donation.reset_timer"
msgstr "Um den Timer zu resetten, erstelle eine neue Spende."

msgid "page.donation.refresh_status"
msgstr "Update Status"

msgid "page.donation.footer.issues_contact"
msgstr "Wenn du auf Probleme stoßen solltest, kontaktiere uns bitte per %(email)s und gib so viele Informationen wie möglich an (z. B. Screenshots)."

msgid "page.donation.expired_already_paid"
msgstr "Wenn du bereits bezahlt hast:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Manchmal kann die Bestätigung bis zu 24 Stunden dauern, also stelle sicher, dass du diese Seite aktualisierst (auch wenn sie abgelaufen ist)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Kaufe PYUSD coins auf PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Folge den Anweisungen um PYUSD coins zu kaufen (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Kaufe ein bisschen mehr (wir empfehlen %(more)s mehr) als den Spendenbetrag (%(amount)s), um Transaktionskosten zu decken. Du behälst alles was übrig bleibt."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Gehe auf die „PYUSD“ Seite in deiner PayPal App oder Webseite. Drücke auf den “Transfer” Knopf %(icon)s, und dann „Senden“."

msgid "page.donation.transfer_amount_to"
msgstr "Sende %(amount)s zu %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Bitcoin (BTC) auf Cash App kaufen"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gehen zur „Bitcoin“ (BTC) Seite in der Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Kaufe etwas mehr (wir empfehlen %(more)s mehr) als den Betrag, den du spenden willst (%(amount)s), um Transaktionsgebühren zu decken. Du behältst den Restbetrag."

msgid "page.donation.cash_app_btc.step2"
msgstr "Überweise den Bitcoin an unsere Adresse"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klicke auf die Schaltfläche „Bitcoin senden“, um eine „Abhebung“ vorzunehmen. Wechsel dann von Dollar zu BTC, indem du auf das %(icon)s Symbol drückst. Gib den BTC-Betrag unten ein und klicke auf „Senden“. Sie dir <a %(help_video)s>dieses Video</a> an, wenn du nicht weiterkommst."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Für kleine Spenden (unter $25) musst du möglicherweise Rush oder Priority verwenden."

msgid "page.donation.revolut.step1"
msgstr "Bitcoin (BTC) auf Revolut kaufen"

msgid "page.donation.revolut.step1.text1"
msgstr "Gehe zur „Crypto“ Seite in Revolut, um Bitcoin (BTC) zu kaufen."

msgid "page.donation.revolut.step1.more"
msgstr "Kaufen etwas mehr (wir empfehlen %(more)s mehr) als den Betrag, den du spenden willst (%(amount)s), um Transaktionsgebühren zu decken. Du behältst den Restbetrag."

msgid "page.donation.revolut.step2"
msgstr "Überweise den Bitcoin an unsere Adresse"

msgid "page.donation.revolut.step2.transfer"
msgstr "Klicke auf die Schaltfläche „Bitcoin senden“, um eine „Abhebung“ vorzunehmen. Wechsel von Euro zu BTC, indem du auf das %(icon)s Symbol drückst. Gib den BTC-Betrag unten ein und klicke auf „Senden“. Sieh dir <a %(help_video)s>dieses Video</a> an, wenn du nicht weiterkommst."

msgid "page.donation.revolut.btc_amount_below"
msgstr "Stelle sicher, dass du den BTC-Betrag unten verwendest, <em>NICHT</em> Euro oder Dollar, da wir sonst den korrekten Betrag nicht erhalten und deine Mitgliedschaft nicht automatisch bestätigen können."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Für kleine Spenden (unter $25) musst du möglicherweise Rush oder Priority verwenden."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Verwende einen der folgenden „Kreditkarte zu Bitcoin“ Express-Dienste, die nur wenige Minuten dauern:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Fülle die folgenden Angaben im Formular aus:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin-Betrag:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Bitte verwende diesen <span %(underline)s>genauen Betrag</span>. Deine abschließenden Gesamtkosten könnten aufgrund von Kreditkartengebühren höher sein. Bei kleinen Beträgen kann dies leider mehr als unser Rabatt sein."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin-Adresse (externes Wallet):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s Anleitung"

msgid "page.donation.crypto_standard"
msgstr "Wir unterstützen nur die Standardversion von Kryptocoins, keine exotischen Netzwerke oder sonstige exotische Versionen von Coins. Je nach Coin kann es bis zu einer Stunde dauern, bis die Transaktion bestätigt ist."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scannen Sie den QR -Code, um sie zu bezahlen"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scannen Sie diesen QR -Code mit Ihrer Crypto -Wallet -App, um die Zahlungsdetails schnell einzugeben"

msgid "page.donation.amazon.header"
msgstr "Amazon Gutschein"

msgid "page.donation.amazon.form_instructions"
msgstr "Bitte nutze das <a %(a_form)s>offizielle Amazon.com Formular</a> um uns einen Gutschein über %(amount)s an die E-Mail-Adresse unten zu schicken."

msgid "page.donation.amazon.only_official"
msgstr "Wir können keine anderen Gutscheine annehmen, <strong>bitte verwende auschließlich das offizielle Formular auf Amazon.com</strong>. Wenn du dieses Formular nicht verwendest, können wir deine Geschenkkarte nicht zurückgeben."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Gib den genauen Betrag ein: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Bitte schreibe NICHT deine eigene Nachricht."

msgid "page.donation.amazon.form_to"
msgstr "\"An\" Empfänger-E-Mail im Formular:"

msgid "page.donation.amazon.unique"
msgstr "Spezifisch zu deinem Account, gib es an niemandem weiter."

msgid "page.donation.amazon.only_use_once"
msgstr "Nur einmal verwenden."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Warte auf Gutschein (zum Prüfen aktualisiere die Seite)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Ein paar Minuten nach abschicken des Gutscheincodes wirst du eine automatisierte Bestätigung erhalten. Wenn es nicht funktioniert, schicke deinen Code nochmal (<a %(a_instr)s>Anweisungen</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Wenn das auch nicht funktioniert, schicke uns (*nachdem* du es ein zweites Mal versucht hast) eine E-Mail und Anna wird sich manuell darum kümmern. Dies kann ein paar Tage dauern."

msgid "page.donation.amazon.example"
msgstr "Beispiel:"

msgid "page.donate.strange_account"
msgstr "Beachte, dass der Accountname oder das Bild etwas eigenartig aussehen kann! Kein Grund zur Sorge! Diese Accounts werden von unseren Spenden-Partnern betreut. Unsere Accounts wurden nicht gehackt."

msgid "page.donation.payment.alipay.top_header"
msgstr "Anleitung Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Spende mit Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Spende den Gesamtbetrag von %(total)s über <a %(a_account)s>dieses Alipay-Konto</a>"

msgid "page.donation.page_blocked"
msgstr "Wenn die Spendenseite blockiert wird, versuchen es mit einer anderen Internetverbindung (z. B. VPN oder Handy-Internet)."

msgid "page.donation.payment.alipay.error"
msgstr "Leider ist die Alipay-Seite oft nur vom <strong>chinesischen Festland aus zugänglich</strong>. Möglicherweise musst du dein VPN vorübergehend deaktivieren oder ein VPN für das chinesische Festland verwenden (Hongkong funktioniert manchmal)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Spende tätigen (QR-Code scannen oder Knopf drücken)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Öffne die <a %(a_href)s>QR-Code-Spendenseite</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scanne den QR-Code mit der Alipay-App oder drücke den Knopf, um die Alipay-App zu öffnen."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Bitte habe etwas Geduld, da die Seite etwas Zeit zum Laden benötigen könnte, da sie sich in China befindet."

msgid "page.donation.payment.wechat.top_header"
msgstr "Anleitung WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Spende mit WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Spende den Gesamtbetrag von %(total)s mit<a %(a_account)s>diesem WeChat-Konto</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix Anleitung"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Spende mit Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Spende den kompletten Betrag von %(total)s auf <a %(a_account)s>dieses Pix Konto"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Sende uns den Beleg per E-Mail"

msgid "page.donation.footer.verification"
msgstr "Sende eine Quittung oder einen Screenshot an deine persönliche Verifizierungsadresse. Verwende diese E-Mail-Adresse NICHT für deine PayPal-Spende."

msgid "page.donation.footer.text1"
msgstr "Sende einen Beleg oder Screenshot an deine persönliche Verifikationsadresse:"

msgid "page.donation.footer.crypto_note"
msgstr "Falls der Wechselkurs der Kryptowährung während der Übertragung fluktuiert bitte den Beleg mit dem relevanten Wechselkurs mitschicken. Wir sind dir sehr dankbar, dass du dir die Mühe machst, mit Kryptowährungen zu spenden, es hilft uns sehr!"

msgid "page.donation.footer.text2"
msgstr "Wenn du den Beleg per E-Mail verschickt hast, klicke bitte diesen Button, damit Anna den Beleg manuell überprüfen kann (dies kann einige Tage dauern):"

msgid "page.donation.footer.button"
msgstr "Ja, ich habe den Beleg per E-Mail verschickt"

msgid "page.donation.footer.success"
msgstr "✅ Danke für deine Spende! Anna wird deine Mitgliedschaft in den nächsten Tagen manuell freischalten."

msgid "page.donation.footer.failure"
msgstr "❌ Etwas ist schief gelaufen. Bitte aktualisiere die Webseite und versuche es erneut."

msgid "page.donation.stepbystep"
msgstr "Schritt-für-Schritt-Anleitung"

msgid "page.donation.crypto_dont_worry"
msgstr "In einigen der Punkte werden Krypto-Wallets erwähnt, aber keine Sorge, du musst dafür nichts über Krypto lernen."

msgid "page.donation.hoodpay.step1"
msgstr "1. Gib deine E-Mail an."

msgid "page.donation.hoodpay.step2"
msgstr "2. Wähle deine Zahlungsmethode."

msgid "page.donation.hoodpay.step3"
msgstr "3. Wähle deine Zahlungsmethode erneut."

msgid "page.donation.hoodpay.step4"
msgstr "4. Wähle das selbstgehostete (“Self-hosted” ) Wallet."

msgid "page.donation.hoodpay.step5"
msgstr "5. Klicke auf \"Ich bestätige den Besitz\" (“I confirm ownership”)."

msgid "page.donation.hoodpay.step6"
msgstr "6. Du solltest einen Beleg per E-Mail erhalten haben. Bitte sende ihn an uns und wir werden deine Spende so schnell wie möglich bestätigen."

msgid "page.donate.wait_new"
msgstr "Bitte warte mindestens <span %(span_hours)s>24 Stunden</span> (und aktualisiere diese Seite), bevor du uns kontaktierst."

msgid "page.donate.mistake"
msgstr "Wenn du bei der Zahlung einen Fehler gemacht hast, können wir keine Rückerstattungen vornehmen, aber wir werden versuchen, es zu korrigieren."

msgid "page.my_donations.title"
msgstr "Meine Spenden"

msgid "page.my_donations.not_shown"
msgstr "Spendendetails werden nicht öffentlich angezeigt."

msgid "page.my_donations.no_donations"
msgstr "Noch keine Spenden. <a %(a_donate)s>Mache meine erste Spende.</a>"

msgid "page.my_donations.make_another"
msgstr "Mache eine neue Spende."

msgid "page.downloaded.title"
msgstr "Heruntergeladene Dateien"

msgid "page.downloaded.fast_partner_star"
msgstr "Downloads von schnellen Partnerservern sind mit %(icon)s gekennzeichnet."

msgid "page.downloaded.twice"
msgstr "Wenn du eine Datei sowohl als schnellen als auch als langsamen Downloads heruntergeladen hast, wird sie doppelt angezeigt."

msgid "page.downloaded.fast_download_time"
msgstr "Schnelle Downloads in den letzten 24 Stunden werden auf das Tageslimit angerechnet."

msgid "page.downloaded.times_utc"
msgstr "Alle Zeitangaben sind in UTC."

msgid "page.downloaded.not_public"
msgstr "Heruntergeladene Dateien werden nicht öffentlich angezeigt."

msgid "page.downloaded.no_files"
msgstr "Es wurden keine Dateien heruntergeladen."

msgid "page.downloaded.last_18_hours"
msgstr "Letzte 18 Stunden"

msgid "page.downloaded.earlier"
msgstr "Früher"

msgid "page.account.logged_in.title"
msgstr "Account"

msgid "page.account.logged_out.title"
msgstr "Anmelden / Registrieren"

msgid "page.account.logged_in.account_id"
msgstr "Account-ID: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Öffentliches Profil: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Geheimcode (nicht teilen!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "anzeigen"

msgid "page.account.logged_in.membership_has_some"
msgstr "Mitgliedschaft: <strong>%(tier_name)s</strong> bis %(until_date)s <a %(a_extend)s>(verlängern)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Mitgliedschaft: <strong>Keine</strong> <a %(a_become)s>(Werde Mitglied)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Verwendete schnelle Downloads (letzte 24 Stunden): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "welche Downloads?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Exklusive Telegram-Gruppe: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Begleite uns!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Upgrade auf eine <a %(a_tier)s> höhere Stufe </a>, um dich unserer Gruppe anzuschließen."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontaktiere Anna unter %(email)s wenn du deine Mitgliedschaft auf eine höhere Stufe upgraden willst."

msgid "page.contact.title"
msgstr "E-Mail"

msgid "page.account.logged_in.membership_multiple"
msgstr "Du kannst mehrere Mitgliedschaften miteinander kombinieren (schnelle Downloads alle 24 Stunden werden dann zusammenaddiert)."

msgid "layout.index.header.nav.public_profile"
msgstr "Öffentliches Profil"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Heruntergeladene Dateien"

msgid "layout.index.header.nav.my_donations"
msgstr "Meine Spenden"

msgid "page.account.logged_in.logout.button"
msgstr "Abmelden"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Du bist jetzt abgemeldet. Aktualisiere die Webseite um dich erneut anzumelden."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut."

msgid "page.account.logged_out.registered.text1"
msgstr "Registrierung erfolgreich! Dein Geheimcode ist: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Speichere den Geheimcode sorgfältig ab. Wenn du diesen nicht mehr hast, verlierst du den Zugang zu deinem Konto."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Lesezeichen.</strong> Du kannst diese Seite als Lesezeichen speichern, um zu deinem Geheimcode zurückzukehren.</li><li %(li_item)s><strong>Herunterladen.</strong> Klicke <a %(a_download)s>diesen Link</a> um deinen Code herunterzuladen.</li><li %(li_item)s><strong>Passwort-Manager.</strong> Der Einfachheit halber ist der Geheimcode oben vorab ausgefüllt, sodass du ihn beim Anmelden im Passwort-Manager speichern kannst.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Gib deinen Geheimcode ein, um dich anzumelden:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Geheimcode"

msgid "page.account.logged_out.key_form.button"
msgstr "Anmelden"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ungültiger Geheimcode. Verfiziere deinen Geheimcode und versuche es erneut oder registriere ein neues Benutzerkonto."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Verlier deinen Geheimcode nicht!"

msgid "page.account.logged_out.register.header"
msgstr "Du hast noch keinen Account?"

msgid "page.account.logged_out.register.button"
msgstr "Registriere einen neuen Account"

msgid "page.login.lost_key"
msgstr "Wenn dein Geheimcode verloren gegangen ist, <a %(a_contact)s>kontaktiere</a> uns bitte und gib so viele Informationen wie möglich an."

msgid "page.login.lost_key_contact"
msgstr "Lege wenn nötig ein temporäres Konto an, um uns zu kontaktieren."

msgid "page.account.logged_out.old_email.button"
msgstr "Alter E-Mail basierter Account? Gib deine <a %(a_open)s>E-Mail hier</a> an."

msgid "page.list.title"
msgstr "Liste"

msgid "page.list.header.edit.link"
msgstr "Editieren"

msgid "page.list.edit.button"
msgstr "Speichern"

msgid "page.list.edit.success"
msgstr "✅ Gespeichert. Bitte Seite neu laden."

msgid "page.list.edit.failure"
msgstr "❌ Es ist etwas schief gelaufen. Bitte erneut versuchen."

msgid "page.list.by_and_date"
msgstr "Liste von %(by)s, erstellt <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Liste ist leer."

msgid "page.list.new_item"
msgstr "Füge Einträge hinzu oder entferne sie indem du eine Datei findest und die „Liste“ Registerkarte öffnest."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil nicht gefunden."

msgid "page.profile.header.edit"
msgstr "Editieren"

msgid "page.profile.change_display_name.text"
msgstr "Ändere deinen Anzeigename. Der Identifier (der Teil nach “#”) kann nicht geändert werden."

msgid "page.profile.change_display_name.button"
msgstr "Speichern"

msgid "page.profile.change_display_name.success"
msgstr "✅ Gespeichert. Bitte Seite neu laden."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Es ist etwas schief gelaufen. Bitte erneut versuchen."

msgid "page.profile.created_time"
msgstr "Profil erstellt <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listen"

msgid "page.profile.lists.no_lists"
msgstr "Keine Listen"

msgid "page.profile.lists.new_list"
msgstr "Erstelle eine neue Liste indem du eine Datei findest und die „Liste“ Registerkarte öffnest."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Urheberrechtsreform ist notwendig für die nationale Sicherheit"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Kurz gesagt: Chinesische LLMs (einschließlich DeepSeek) werden mit meinem illegalen Archiv von Büchern und wissenschaftlichen Aufsätzen trainiert — dem größten der Welt. Der Westen muss das Urheberrecht im Interesse der nationalen Sicherheit überarbeiten."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "Begleitartikel von TorrentFreak: <a %(torrentfreak)s>erster</a>, <a %(torrentfreak_2)s>zweiter</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Vor nicht allzu langer Zeit waren „Schattenbibliotheken“ vom Aussterben bedroht. Sci-Hub, das riesige illegale Archiv akademischer Aufsätze, hatte aufgrund von Klagen aufgehört, neue Werke aufzunehmen. Die „Z-Library“, die größte illegale Bibliothek von Büchern, sah sich mit der Verhaftung ihrer angeblichen Schöpfer wegen strafrechtlicher Urheberrechtsverletzungen konfrontiert. Sie schafften es unglaublich, ihrer Verhaftung zu entkommen, aber ihre Bibliothek ist dennoch bedroht."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Als die Z-Library vor der Schließung stand, hatte ich bereits ihre gesamte Bibliothek gesichert und suchte nach einer Plattform, um sie zu beherbergen. Das war meine Motivation, Anna’s Archiv zu starten: eine Fortsetzung der Mission hinter diesen früheren Initiativen. Seitdem sind wir zur größten Schattenbibliothek der Welt gewachsen und beherbergen mehr als 140 Millionen urheberrechtlich geschützte Texte in zahlreichen Formaten — Bücher, wissenschaftliche Aufsätze, Zeitschriften, Zeitungen und mehr."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mein Team und ich sind Ideologen. Wir glauben, dass das Bewahren und Bereitstellen dieser Dateien moralisch richtig ist. Bibliotheken auf der ganzen Welt sehen sich mit Budgetkürzungen konfrontiert, und wir können das Erbe der Menschheit auch nicht den Konzernen anvertrauen."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Dann kam die KI. Praktisch alle großen Unternehmen, die LLMs entwickeln, kontaktierten uns, um mit unseren Daten zu trainieren. Die meisten (aber nicht alle!) US-amerikanischen Unternehmen überdachten ihre Entscheidung, als sie die illegale Natur unserer Arbeit erkannten. Im Gegensatz dazu haben chinesische Firmen unsere Sammlung begeistert angenommen, offenbar unbeeindruckt von ihrer Legalität. Dies ist bemerkenswert, da China Unterzeichner fast aller wichtigen internationalen Urheberrechtsverträge ist."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Wir haben etwa 30 Unternehmen Hochgeschwindigkeitszugang gewährt. Die meisten von ihnen sind LLM-Unternehmen, und einige sind Datenbroker, die unsere Sammlung weiterverkaufen werden. Die meisten sind chinesisch, obwohl wir auch mit Unternehmen aus den USA, Europa, Russland, Südkorea und Japan zusammengearbeitet haben. DeepSeek <a %(arxiv)s>gab zu</a>, dass eine frühere Version mit einem Teil unserer Sammlung trainiert wurde, obwohl sie über ihr neuestes Modell schweigen (wahrscheinlich auch mit unseren Daten trainiert)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Wenn der Westen im Rennen um LLMs und letztendlich AGI die Führung behalten will, muss er seine Haltung zum Urheberrecht überdenken, und zwar bald. Ob Sie uns in unserem moralischen Anliegen zustimmen oder nicht, dies wird nun zu einer Frage der Wirtschaft und sogar der nationalen Sicherheit. Alle Machtblöcke bauen künstliche Superwissenschaftler, Superhacker und Supermilitärs. Informationsfreiheit wird für diese Länder zu einer Überlebensfrage — sogar zu einer Frage der nationalen Sicherheit."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Unser Team kommt aus der ganzen Welt, und wir haben keine besondere Ausrichtung. Aber wir würden Länder mit strengen Urheberrechtsgesetzen ermutigen, diese existenzielle Bedrohung zu nutzen, um sie zu reformieren. Was also tun?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Unsere erste Empfehlung ist einfach: Verkürzen Sie die Urheberrechtsdauer. In den USA wird das Urheberrecht für 70 Jahre nach dem Tod des Autors gewährt. Das ist absurd. Wir können dies in Einklang mit Patenten bringen, die für 20 Jahre nach der Anmeldung gewährt werden. Dies sollte mehr als genug Zeit für Autoren von Büchern, Aufsätzen, Musik, Kunst und anderen kreativen Werken sein, um vollständig für ihre Bemühungen entschädigt zu werden (einschließlich langfristiger Projekte wie Filmadaptionen)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Dann sollten die politischen Entscheidungsträger zumindest Ausnahmen für die Massenbewahrung und -verbreitung von Texten einbeziehen. Wenn entgangene Einnahmen von einzelnen Kunden die Hauptsorge sind, könnte die Verteilung auf persönlicher Ebene weiterhin verboten bleiben. Im Gegenzug würden diejenigen, die in der Lage sind, riesige Sammlungen zu verwalten — Unternehmen, die LLMs trainieren, zusammen mit Bibliotheken und anderen Archiven — von diesen Ausnahmen abgedeckt."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Einige Länder machen bereits eine Version davon. TorrentFreak <a %(torrentfreak)s>berichtete</a>, dass China und Japan KI-Ausnahmen in ihre Urheberrechtsgesetze eingeführt haben. Es ist unklar, wie dies mit internationalen Verträgen interagiert, aber es bietet sicherlich Schutz für ihre inländischen Unternehmen, was erklärt, was wir beobachtet haben."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Was Anna’s Archiv betrifft — wir werden unsere Untergrundarbeit aus moralischer Überzeugung fortsetzen. Doch unser größter Wunsch ist es, ans Licht zu treten und unseren Einfluss legal zu verstärken. Bitte reformieren Sie das Urheberrecht."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lesen Sie die Begleitartikel von TorrentFreak: <a %(torrentfreak)s>erster</a>, <a %(torrentfreak_2)s>zweiter</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Gewinner des $10.000 ISBN-Visualisierungswettbewerbs"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Kurz gesagt: Wir haben einige unglaubliche Einsendungen für den $10.000 ISBN-Visualisierungswettbewerb erhalten."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Vor ein paar Monaten haben wir ein <a %(all_isbns)s>$10.000 Kopfgeld</a> ausgeschrieben, um die bestmögliche Visualisierung unserer Daten zu erstellen, die den ISBN-Raum zeigt. Wir betonten, welche Dateien wir bereits archiviert haben und welche nicht, und später ein Datensatz, der beschreibt, wie viele Bibliotheken ISBNs besitzen (ein Maß für die Seltenheit)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Wir sind von der Resonanz überwältigt. Es gab so viel Kreativität. Ein großes Dankeschön an alle, die teilgenommen haben: Ihre Energie und Begeisterung sind ansteckend!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Letztendlich wollten wir die folgenden Fragen beantworten: <strong>Welche Bücher gibt es auf der Welt, wie viele haben wir bereits archiviert und auf welche Bücher sollten wir uns als nächstes konzentrieren?</strong> Es ist großartig zu sehen, dass so viele Menschen sich für diese Fragen interessieren."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Wir haben selbst mit einer grundlegenden Visualisierung begonnen. In weniger als 300kb stellt dieses Bild die größte vollständig offene „Liste von Büchern“ dar, die jemals in der Geschichte der Menschheit zusammengestellt wurde:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Dateien in Annas Archiv"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC-Datenleck"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook-Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Globales Verzeichnis der Verlage"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russische Staatsbibliothek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperiale Bibliothek von Trantor"

#, fuzzy
msgid "common.back"
msgstr "Zurück"

#, fuzzy
msgid "common.forward"
msgstr "Vorwärts"

#, fuzzy
msgid "common.last"
msgstr "Letzte"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Bitte sehen Sie sich den <a %(all_isbns)s>ursprünglichen Blogbeitrag</a> für weitere Informationen an."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Wir haben eine Herausforderung gestellt, um dies zu verbessern. Wir würden ein Preisgeld von 6.000 $ für den ersten Platz, 3.000 $ für den zweiten Platz und 1.000 $ für den dritten Platz vergeben. Aufgrund der überwältigenden Resonanz und der unglaublichen Einsendungen haben wir uns entschieden, den Preispool leicht zu erhöhen und vier dritte Plätze mit jeweils 500 $ zu vergeben. Die Gewinner sind unten aufgeführt, aber schauen Sie sich unbedingt alle Einsendungen <a %(annas_archive)s>hier</a> an oder laden Sie unseren <a %(a_2025_01_isbn_visualization_files)s>kombinierten Torrent</a> herunter."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Erster Platz 6.000 $: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Diese <a %(phiresky_github)s>Einsendung</a> (<a %(annas_archive_note_2951)s>Gitlab-Kommentar</a>) ist einfach alles, was wir wollten, und mehr! Besonders gefallen haben uns die unglaublich flexiblen Visualisierungsoptionen (sogar mit Unterstützung für benutzerdefinierte Shader), aber mit einer umfassenden Liste von Voreinstellungen. Wir mochten auch, wie schnell und reibungslos alles ist, die einfache Implementierung (die nicht einmal ein Backend hat), die clevere Minikarte und die ausführliche Erklärung in ihrem <a %(phiresky_github)s>Blogbeitrag</a>. Unglaubliche Arbeit und der wohlverdiente Gewinner!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Zweiter Platz 3.000 $: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Eine weitere unglaubliche <a %(annas_archive_note_2913)s>Einsendung</a>. Nicht so flexibel wie der erste Platz, aber wir bevorzugten tatsächlich die Makroebenen-Visualisierung gegenüber dem ersten Platz (raumfüllende Kurve, Grenzen, Beschriftung, Hervorhebung, Schwenken und Zoomen). Ein <a %(annas_archive_note_2971)s>Kommentar</a> von Joe Davis hat uns besonders angesprochen:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Während perfekte Quadrate und Rechtecke mathematisch ansprechend sind, bieten sie in einem Kartierungskontext keine überlegene Lokalität. Ich glaube, dass die Asymmetrie, die diesen Hilbert- oder klassischen Morton-Kurven innewohnt, kein Fehler, sondern ein Merkmal ist. Genau wie Italiens berühmte stiefelförmige Umrisse es auf einer Karte sofort erkennbar machen, können die einzigartigen „Eigenheiten“ dieser Kurven als kognitive Orientierungspunkte dienen. Diese Unverwechselbarkeit kann das räumliche Gedächtnis verbessern und den Benutzern helfen, sich zu orientieren, was möglicherweise das Auffinden bestimmter Regionen oder das Erkennen von Mustern erleichtert.“"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Und immer noch viele Optionen zur Visualisierung und Darstellung sowie eine unglaublich flüssige und intuitive Benutzeroberfläche. Ein solider zweiter Platz!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Dritter Platz 500 $ #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In dieser <a %(annas_archive_note_2940)s>Einsendung</a> haben uns die verschiedenen Ansichtsarten besonders gefallen, insbesondere die Vergleichs- und Verlagsansichten."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Dritter Platz 500 $ #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Obwohl die Benutzeroberfläche nicht die ausgereifteste ist, erfüllt diese <a %(annas_archive_note_2917)s>Einsendung</a> viele Kriterien. Besonders gefallen hat uns die Vergleichsfunktion."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Dritter Platz 500 $ #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Wie der erste Platz hat uns diese <a %(annas_archive_note_2975)s>Einsendung</a> mit ihrer Flexibilität beeindruckt. Letztendlich ist es diese Flexibilität, die ein großartiges Visualisierungstool ausmacht: maximale Flexibilität für Power-User, während es für durchschnittliche Benutzer einfach bleibt."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Dritter Platz 500 $ #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Die letzte <a %(annas_archive_note_2947)s>Einsendung</a>, die ein Preisgeld erhält, ist ziemlich einfach, hat aber einige einzigartige Funktionen, die uns wirklich gefallen haben. Wir mochten, wie sie zeigen, wie viele Datasets eine bestimmte ISBN abdecken, als Maß für Popularität/Zuverlässigkeit. Wir mochten auch die Einfachheit, aber Effektivität der Verwendung eines Opazitätsschiebers für Vergleiche."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Bemerkenswerte Ideen"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Einige weitere Ideen und Implementierungen, die uns besonders gefallen haben:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Wolkenkratzer für Seltenheit"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live-Statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anmerkungen und auch Live-Statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Einzigartige Kartenansicht und Filter"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Cooles Standardfarbschema und Heatmap."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Einfaches Umschalten von Datasets für schnelle Vergleiche."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Hübsche Beschriftungen."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Maßstabsleiste mit Anzahl der Bücher."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Viele Schieberegler zum Vergleichen von Datasets, als ob Sie ein DJ wären."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Wir könnten noch eine Weile weitermachen, aber lassen Sie uns hier aufhören. Schauen Sie sich unbedingt alle Einsendungen <a %(annas_archive)s>hier</a> an oder laden Sie unseren <a %(a_2025_01_isbn_visualization_files)s>kombinierten Torrent</a> herunter. So viele Einsendungen, und jede bringt eine einzigartige Perspektive, sei es in der Benutzeroberfläche oder der Implementierung."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Wir werden zumindest die Einsendung des ersten Platzes in unsere Hauptwebsite integrieren und vielleicht einige andere. Wir haben auch begonnen, darüber nachzudenken, wie wir den Prozess der Identifizierung, Bestätigung und Archivierung der seltensten Bücher organisieren können. Mehr dazu in Kürze."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Danke an alle, die teilgenommen haben. Es ist erstaunlich, dass so viele Menschen sich dafür interessieren."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Unsere Herzen sind voller Dankbarkeit."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisierung aller ISBNs — 10.000 $ Belohnung bis 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dieses Bild repräsentiert die größte vollständig offene „Liste von Büchern“, die jemals in der Geschichte der Menschheit zusammengestellt wurde."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dieses Bild ist 1000×800 Pixel groß. Jeder Pixel repräsentiert 2.500 ISBNs. Wenn wir eine Datei für eine ISBN haben, machen wir diesen Pixel grüner. Wenn wir wissen, dass eine ISBN vergeben wurde, aber keine passende Datei haben, machen wir ihn röter."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In weniger als 300kb repräsentiert dieses Bild prägnant die größte vollständig offene „Liste von Büchern“, die jemals in der Geschichte der Menschheit zusammengestellt wurde (einige hundert GB vollständig komprimiert)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Es zeigt auch: Es gibt noch viel Arbeit beim Sichern von Büchern (wir haben nur 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Hintergrund"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Wie kann Annas Archiv seine Mission erfüllen, das gesamte Wissen der Menschheit zu sichern, ohne zu wissen, welche Bücher noch existieren? Wir brauchen eine TODO-Liste. Eine Möglichkeit, dies zu kartieren, ist durch ISBN-Nummern, die seit den 1970er Jahren jedem veröffentlichten Buch (in den meisten Ländern) zugewiesen wurden."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Es gibt keine zentrale Behörde, die alle ISBN-Zuweisungen kennt. Stattdessen ist es ein verteiltes System, bei dem Länder Zahlenbereiche erhalten, die dann kleinere Bereiche an große Verlage vergeben, die diese Bereiche möglicherweise weiter an kleinere Verlage unterteilen. Schließlich werden einzelne Nummern den Büchern zugewiesen."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Wir haben vor <a %(blog)s>zwei Jahren</a> mit unserer Erfassung von ISBNdb begonnen, ISBNs zu kartieren. Seitdem haben wir viele weitere Metadatenquellen erfasst, wie <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby und mehr. Eine vollständige Liste finden Sie auf den Seiten „Datasets“ und „Torrents“ in Annas Archiv. Wir haben jetzt bei weitem die größte vollständig offene, leicht herunterladbare Sammlung von Buchmetadaten (und damit ISBNs) der Welt."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Wir haben <a %(blog)s>ausführlich darüber geschrieben</a>, warum uns die Erhaltung wichtig ist und warum wir uns derzeit in einem kritischen Zeitfenster befinden. Wir müssen jetzt seltene, wenig beachtete und einzigartig gefährdete Bücher identifizieren und bewahren. Gute Metadaten zu allen Büchern der Welt helfen dabei."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisierung"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Neben dem Übersichtsbild können wir uns auch einzelne Datasets ansehen, die wir erworben haben. Verwenden Sie das Dropdown-Menü und die Schaltflächen, um zwischen ihnen zu wechseln."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "In diesen Bildern gibt es viele interessante Muster zu entdecken. Warum gibt es eine gewisse Regelmäßigkeit von Linien und Blöcken, die auf verschiedenen Skalen zu passieren scheint? Was sind die leeren Bereiche? Warum sind bestimmte Datasets so stark gebündelt? Wir überlassen diese Fragen dem Leser als Übung."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "10.000 $ Belohnung"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Es gibt hier viel zu erkunden, daher kündigen wir eine Belohnung für die Verbesserung der obigen Visualisierung an. Im Gegensatz zu den meisten unserer Belohnungen ist diese zeitlich begrenzt. Sie müssen Ihren Open-Source-Code bis zum 31.01.2025 (23:59 UTC) <a %(annas_archive)s>einreichen</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Die beste Einreichung erhält 6.000 $, der zweite Platz 3.000 $ und der dritte Platz 1.000 $. Alle Belohnungen werden in Monero (XMR) ausgezahlt."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Unten sind die Mindestkriterien aufgeführt. Wenn keine Einreichung die Kriterien erfüllt, könnten wir dennoch einige Belohnungen vergeben, aber das liegt in unserem Ermessen."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forken Sie dieses Repository und bearbeiten Sie diesen Blogpost-HTML (keine anderen Backends außer unserem Flask-Backend sind erlaubt)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Machen Sie das obige Bild nahtlos zoombar, sodass Sie bis zu einzelnen ISBNs hineinzoomen können. Ein Klick auf ISBNs sollte Sie zu einer Metadatenseite oder einer Suche in Annas Archiv führen."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Sie müssen weiterhin zwischen allen verschiedenen Datasets wechseln können."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Länderbereiche und Verlagsbereiche sollten beim Überfahren hervorgehoben werden. Sie können z.B. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> für Länderinformationen und unseren „isbngrp“-Scrape für Verlage (<a %(annas_archive)s>Dataset</a>, <a %(annas_archive_2)s>Torrent</a>) verwenden."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Es muss sowohl auf Desktop als auch auf Mobilgeräten gut funktionieren."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Für Bonuspunkte (dies sind nur Ideen — lassen Sie Ihrer Kreativität freien Lauf):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Besonderes Augenmerk wird auf die Benutzerfreundlichkeit und das Aussehen gelegt."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Zeigen Sie beim Hineinzoomen tatsächliche Metadaten für einzelne ISBNs an, wie Titel und Autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Bessere Raumfüllkurve. Z.B. ein Zickzack, der in der ersten Reihe von 0 bis 4 geht und dann (umgekehrt) in der zweiten Reihe von 5 bis 9 zurück — rekursiv angewendet."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Unterschiedliche oder anpassbare Farbschemata."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Spezielle Ansichten zum Vergleichen von Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Möglichkeiten zur Fehlerbehebung bei Problemen, wie z. B. andere metadata, die nicht gut übereinstimmen (z. B. stark abweichende Titel)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Bilder mit Kommentaren zu ISBNs oder Bereichen annotieren."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Jegliche Heuristiken zur Identifizierung seltener oder gefährdeter Bücher."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Welche kreativen Ideen Ihnen auch einfallen!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Sie DÜRFEN vollständig von den minimalen Kriterien abweichen und eine völlig andere Visualisierung erstellen. Wenn sie wirklich spektakulär ist, qualifiziert sie sich für die Prämie, aber nach unserem Ermessen."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Reichen Sie Beiträge ein, indem Sie einen Kommentar zu <a %(annas_archive)s>diesem Problem</a> mit einem Link zu Ihrem geforkten Repository, Merge-Request oder Diff posten."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Der Code zur Erstellung dieser Bilder sowie weitere Beispiele finden Sie in <a %(annas_archive)s>diesem Verzeichnis</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Wir haben ein kompaktes Datenformat entwickelt, mit dem alle erforderlichen ISBN-Informationen etwa 75 MB (komprimiert) umfassen. Die Beschreibung des Datenformats und der Code zu dessen Erstellung finden Sie <a %(annas_archive_l1244_1319)s>hier</a>. Für die Prämie sind Sie nicht verpflichtet, dies zu verwenden, aber es ist wahrscheinlich das bequemste Format, um damit zu beginnen. Sie können unsere metadata nach Belieben transformieren (obwohl Ihr gesamter Code Open Source sein muss)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Wir können es kaum erwarten zu sehen, was Sie sich einfallen lassen. Viel Glück!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annas Archiv-Container (AAC): Standardisierung von Veröffentlichungen aus der weltweit größten Schattenbibliothek"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annas Archiv ist zur größten Schattenbibliothek der Welt geworden, was uns dazu zwingt, unsere Veröffentlichungen zu standardisieren."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annas Archiv</a> ist mit Abstand die größte Schattenbibliothek der Welt geworden und die einzige Schattenbibliothek dieser Größenordnung, die vollständig Open Source und Open Data ist. Unten ist eine Tabelle von unserer Datasets-Seite (leicht modifiziert):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Wir haben dies auf drei Arten erreicht:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Spiegelung bestehender Open-Data-Schattenbibliotheken (wie Sci-Hub und Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Unterstützung von Schattenbibliotheken, die offener sein möchten, aber nicht die Zeit oder Ressourcen dazu hatten (wie die Libgen-Comics-Sammlung)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scraping von Bibliotheken, die nicht in großen Mengen teilen möchten (wie Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Für (2) und (3) verwalten wir nun selbst eine beträchtliche Sammlung von Torrents (Hunderte von TBs). Bisher haben wir diese Sammlungen als Einzelstücke behandelt, was bedeutet, dass für jede Sammlung eine maßgeschneiderte Infrastruktur und Datenorganisation erforderlich ist. Dies führt zu einem erheblichen Mehraufwand bei jeder Veröffentlichung und erschwert insbesondere inkrementelle Veröffentlichungen."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Deshalb haben wir uns entschieden, unsere Veröffentlichungen zu standardisieren. Dies ist ein technischer Blogbeitrag, in dem wir unseren Standard vorstellen: <strong>Annas Archiv-Container</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Designziele"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Unser primärer Anwendungsfall ist die Verteilung von Dateien und zugehörigen Metadaten aus verschiedenen bestehenden Sammlungen. Unsere wichtigsten Überlegungen sind:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene Dateien und Metadaten, so nah wie möglich am Originalformat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene Identifikatoren in den Quellbibliotheken oder sogar das Fehlen von Identifikatoren."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separate Veröffentlichungen von Metadaten vs. Dateidaten oder nur Metadaten-Veröffentlichungen (z. B. unsere ISBNdb-Veröffentlichung)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Verteilung über Torrents, jedoch mit der Möglichkeit anderer Verteilungsmethoden (z. B. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Unveränderliche Aufzeichnungen, da wir davon ausgehen sollten, dass unsere Torrents für immer bestehen bleiben."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementelle Veröffentlichungen / anhängbare Veröffentlichungen."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Maschinenlesbar und -schreibbar, bequem und schnell, insbesondere für unseren Stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Einigermaßen einfache menschliche Inspektion, obwohl dies sekundär zur Maschinenlesbarkeit ist."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Einfach, unsere Sammlungen mit einer standardmäßig gemieteten Seedbox zu seeden."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binärdaten können direkt von Webservern wie Nginx bereitgestellt werden."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Einige Nicht-Ziele:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Es ist uns egal, ob Dateien manuell auf der Festplatte leicht zu navigieren sind oder ohne Vorverarbeitung durchsuchbar sind."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Es ist uns egal, ob sie direkt mit bestehender Bibliothekssoftware kompatibel sind."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Obwohl es einfach sein sollte, unsere Sammlung mit Torrents zu seeden, erwarten wir nicht, dass die Dateien ohne erhebliches technisches Wissen und Engagement nutzbar sind."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Da Annas Archiv Open Source ist, möchten wir unser Format direkt selbst nutzen. Wenn wir unseren Suchindex aktualisieren, greifen wir nur auf öffentlich zugängliche Pfade zu, damit jeder, der unsere Bibliothek forkt, schnell loslegen kann."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Der Standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Letztendlich haben wir uns auf einen relativ einfachen Standard geeinigt. Er ist ziemlich locker, nicht normativ und ein fortlaufendes Projekt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archiv Container) ist ein einzelnes Element, das aus <strong>Metadaten</strong> und optional <strong>Binärdaten</strong> besteht, die beide unveränderlich sind. Es hat einen weltweit eindeutigen Bezeichner, genannt <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Sammlung.</strong> Jedes AAC gehört zu einer Sammlung, die per Definition eine Liste von AACs ist, die semantisch konsistent sind. Das bedeutet, dass wenn Sie eine wesentliche Änderung am Format der Metadaten vornehmen, Sie eine neue Sammlung erstellen müssen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>„Datensätze“ und „Dateien“ Sammlungen.</strong> Üblicherweise ist es oft praktisch, „Datensätze“ und „Dateien“ als verschiedene Sammlungen zu veröffentlichen, damit sie zu unterschiedlichen Zeitplänen veröffentlicht werden können, z. B. basierend auf Scraping-Raten. Ein „Datensatz“ ist eine Sammlung, die nur Metadaten enthält, wie Buchtitel, Autoren, ISBNs usw., während „Dateien“ die Sammlungen sind, die die eigentlichen Dateien selbst enthalten (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Das Format von AACID ist folgendes: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Zum Beispiel ist ein tatsächlicher AACID, den wir veröffentlicht haben, <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: der Sammlungsname, der ASCII-Buchstaben, Zahlen und Unterstriche enthalten kann (aber keine doppelten Unterstriche)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: eine kurze Version des ISO 8601, immer in UTC, z. B. <code>20220723T194746Z</code>. Diese Zahl muss für jede Veröffentlichung monoton ansteigen, obwohl ihre genauen Semantiken je nach Sammlung unterschiedlich sein können. Wir empfehlen, die Zeit des Scrapings oder der ID-Erstellung zu verwenden."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: ein sammlungsspezifischer Bezeichner, falls zutreffend, z. B. die Z-Library ID. Kann weggelassen oder gekürzt werden. Muss weggelassen oder gekürzt werden, wenn der AACID sonst 150 Zeichen überschreiten würde."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: eine UUID, aber komprimiert zu ASCII, z. B. mit base57. Wir verwenden derzeit die <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-Bibliothek."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID-Bereich.</strong> Da AACIDs monoton ansteigende Zeitstempel enthalten, können wir diese verwenden, um Bereiche innerhalb einer bestimmten Sammlung zu kennzeichnen. Wir verwenden dieses Format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wobei die Zeitstempel inklusive sind. Dies ist konsistent mit der ISO 8601-Notation. Bereiche sind kontinuierlich und können sich überlappen, müssen im Falle einer Überlappung jedoch identische Datensätze wie die zuvor in dieser Sammlung veröffentlichten enthalten (da AACs unveränderlich sind). Fehlende Datensätze sind nicht erlaubt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadatendatei.</strong> Eine Metadatendatei enthält die Metadaten eines Bereichs von AACs für eine bestimmte Sammlung. Diese haben die folgenden Eigenschaften:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Der Dateiname muss ein AACID-Bereich sein, der mit <code style=\"color: red\">annas_archive_meta__</code> beginnt und mit <code>.jsonl.zstd</code> endet. Zum Beispiel heißt eine unserer Veröffentlichungen<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Wie durch die Dateierweiterung angegeben, ist der Dateityp <a %(jsonlines)s>JSON Lines</a>, komprimiert mit <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Jedes JSON-Objekt muss die folgenden Felder auf der obersten Ebene enthalten: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). Keine anderen Felder sind erlaubt."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> sind beliebige Metadaten, gemäß den Semantiken der Sammlung. Sie müssen innerhalb der Sammlung semantisch konsistent sein."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> ist optional und ist der Name des Binärdatenordners, der die entsprechenden Binärdaten enthält. Der Dateiname der entsprechenden Binärdaten innerhalb dieses Ordners ist der AACID des Datensatzes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Das <code style=\"color: red\">annas_archive_meta__</code> Präfix kann an den Namen Ihrer Institution angepasst werden, z. B. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binärdatenordner.</strong> Ein Ordner mit den Binärdaten eines Bereichs von AACs für eine bestimmte Sammlung. Diese haben die folgenden Eigenschaften:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Der Verzeichnisname muss ein AACID-Bereich sein, der mit <code style=\"color: green\">annas_archive_data__</code> beginnt und keinen Suffix hat. Zum Beispiel hat eine unserer tatsächlichen Veröffentlichungen ein Verzeichnis namens<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Das Verzeichnis muss Datendateien für alle AACs innerhalb des angegebenen Bereichs enthalten. Jede Datendatei muss ihren AACID als Dateinamen haben (keine Erweiterungen)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Es wird empfohlen, diese Ordner in einer einigermaßen handhabbaren Größe zu halten, z. B. nicht größer als 100GB-1TB pro Ordner, obwohl sich diese Empfehlung im Laufe der Zeit ändern kann."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Die Metadaten-Dateien und Binärdaten-Ordner können in Torrents gebündelt werden, mit einem Torrent pro Metadaten-Datei oder einem Torrent pro Binärdaten-Ordner. Die Torrents müssen den ursprünglichen Datei-/Verzeichnisnamen plus ein <code>.torrent</code> Suffix als Dateinamen haben."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Beispiel"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Schauen wir uns als Beispiel unsere aktuelle Z-Library-Veröffentlichung an. Sie besteht aus zwei Sammlungen: „<span style=\"background: #fffaa3\">zlib3_records</span>“ und „<span style=\"background: #ffd6fe\">zlib3_files</span>“. Dies ermöglicht es uns, Metadaten-Datensätze separat von den eigentlichen Buchdateien zu extrahieren und zu veröffentlichen. Daher haben wir zwei Torrents mit Metadaten-Dateien veröffentlicht:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Wir haben auch eine Reihe von Torrents mit Binärdaten-Ordnern veröffentlicht, jedoch nur für die Sammlung „<span style=\"background: #ffd6fe\">zlib3_files</span>“, insgesamt 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Durch Ausführen von <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> können wir sehen, was sich darin befindet:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In diesem Fall handelt es sich um Metadaten eines Buches, wie von Z-Library gemeldet. Auf der obersten Ebene haben wir nur „aacid“ und „metadata“, aber keinen „data_folder“, da es keine entsprechenden Binärdaten gibt. Das AACID enthält „22430000“ als primäre ID, die wir sehen können, dass sie von „zlibrary_id“ übernommen wurde. Wir können erwarten, dass andere AACs in dieser Sammlung die gleiche Struktur haben."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Lassen Sie uns nun <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> ausführen:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dies ist ein viel kleineres AAC-Metadaten, obwohl der Großteil dieses AAC anderswo in einer Binärdatei gespeichert ist! Schließlich haben wir diesmal einen „data_folder“, sodass wir erwarten können, dass die entsprechenden Binärdaten unter <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> zu finden sind. Die „metadata“ enthält die „zlibrary_id“, sodass wir sie leicht mit dem entsprechenden AAC in der „zlib_records“-Sammlung verknüpfen können. Wir hätten auf verschiedene Weise verknüpfen können, z. B. über AACID — der Standard schreibt das nicht vor."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Beachten Sie, dass es auch nicht notwendig ist, dass das „metadata“-Feld selbst JSON ist. Es könnte ein String sein, der XML oder ein anderes Datenformat enthält. Sie könnten sogar Metadateninformationen im zugehörigen Binärblob speichern, z. B. wenn es sich um eine große Datenmenge handelt."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Fazit"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Mit diesem Standard können wir Veröffentlichungen schrittweise vornehmen und leichter neue Datenquellen hinzufügen. Wir haben bereits einige spannende Veröffentlichungen in der Pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Wir hoffen auch, dass es für andere Schattenbibliotheken einfacher wird, unsere Sammlungen zu spiegeln. Schließlich ist es unser Ziel, menschliches Wissen und Kultur für immer zu bewahren, daher gilt: Je mehr Redundanz, desto besser."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annas Update: vollständig quelloffenes Archiv, ElasticSearch, über 300GB an Buchcovern"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Wir haben rund um die Uhr gearbeitet, um mit Annas Archiv eine gute Alternative bereitzustellen. Hier sind einige der Dinge, die wir kürzlich erreicht haben."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Da Z-Library offline gegangen ist und seine (angeblichen) Gründer verhaftet wurden, haben wir rund um die Uhr gearbeitet, um mit Annas Archiv eine gute Alternative bereitzustellen (wir werden es hier nicht verlinken, aber Sie können es googeln). Hier sind einige der Dinge, die wir kürzlich erreicht haben."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annas Archiv ist vollständig quelloffen"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Wir glauben, dass Informationen frei sein sollten, und unser eigener Code ist da keine Ausnahme. Wir haben unseren gesamten Code auf unserer privat gehosteten Gitlab-Instanz veröffentlicht: <a %(annas_archive)s>Annas Software</a>. Wir nutzen auch den Issue-Tracker, um unsere Arbeit zu organisieren. Wenn Sie sich an unserer Entwicklung beteiligen möchten, ist dies ein großartiger Ausgangspunkt."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Um Ihnen einen Vorgeschmack auf die Dinge zu geben, an denen wir arbeiten, nehmen Sie unsere jüngsten Arbeiten zur Verbesserung der Client-seitigen Leistung. Da wir noch keine Paginierung implementiert haben, würden wir oft sehr lange Suchseiten mit 100-200 Ergebnissen zurückgeben. Wir wollten die Suchergebnisse nicht zu früh abschneiden, aber das bedeutete, dass es einige Geräte verlangsamen würde. Dafür haben wir einen kleinen Trick implementiert: Wir haben die meisten Suchergebnisse in HTML-Kommentare (<code><!-- --></code>) eingewickelt und dann ein kleines Javascript geschrieben, das erkennt, wann ein Ergebnis sichtbar werden sollte, und in diesem Moment den Kommentar auspackt:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM-\"Virtualisierung\" in 23 Zeilen implementiert, keine Notwendigkeit für ausgefallene Bibliotheken! Dies ist die Art von pragmatischem Code, die entsteht, wenn man wenig Zeit hat und reale Probleme gelöst werden müssen. Es wurde berichtet, dass unsere Suche jetzt auch auf langsamen Geräten gut funktioniert!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Ein weiterer großer Aufwand war die Automatisierung des Datenbankaufbaus. Als wir starteten, zogen wir einfach wahllos verschiedene Quellen zusammen. Jetzt wollen wir sie aktuell halten, also haben wir eine Reihe von Skripten geschrieben, um neue Metadata von den beiden Library Genesis-Forks herunterzuladen und zu integrieren. Das Ziel ist es, dies nicht nur für unser Archiv nützlich zu machen, sondern es auch jedem zu erleichtern, der mit Schattenbibliothek-Metadata experimentieren möchte. Das Ziel wäre ein Jupyter-Notebook, das alle möglichen interessanten Metadata enthält, damit wir mehr Forschung betreiben können, wie zum Beispiel herauszufinden, welcher <a %(blog)s>Prozentsatz der ISBNs für immer erhalten bleibt</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Schließlich haben wir unser Spenden-System überarbeitet. Sie können jetzt eine Kreditkarte verwenden, um direkt Geld in unsere Krypto-Wallets einzuzahlen, ohne wirklich etwas über Kryptowährungen wissen zu müssen. Wir werden weiterhin beobachten, wie gut das in der Praxis funktioniert, aber das ist ein großer Schritt."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Wechsel zu ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Eines unserer <a %(annas_archive)s>Tickets</a> war eine Sammlung von Problemen mit unserem Suchsystem. Wir verwendeten die MySQL-Volltextsuche, da wir alle unsere Daten ohnehin in MySQL hatten. Aber es hatte seine Grenzen:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Einige Abfragen dauerten extrem lange, bis zu dem Punkt, an dem sie alle offenen Verbindungen blockierten."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Standardmäßig hat MySQL eine Mindestwortlänge, oder Ihr Index kann wirklich groß werden. Es wurde berichtet, dass man nicht nach „Ben Hur“ suchen konnte."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Die Suche war nur dann einigermaßen schnell, wenn sie vollständig im Speicher geladen war, was uns dazu zwang, eine teurere Maschine zu verwenden, um dies auszuführen, plus einige Befehle, um den Index beim Start vorzuladen."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Wir hätten es nicht leicht erweitern können, um neue Funktionen zu entwickeln, wie bessere <a %(wikipedia_cjk_characters)s>Tokenisierung für nicht-weißraumgetrennte Sprachen</a>, Filterung/Facettierung, Sortierung, \"Meinten Sie\"-Vorschläge, Autovervollständigung und so weiter."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Nach Gesprächen mit einer Reihe von Experten haben wir uns für ElasticSearch entschieden. Es war nicht perfekt (ihre Standard-\"Meinten Sie\"-Vorschläge und Autovervollständigungsfunktionen sind schlecht), aber insgesamt war es viel besser als MySQL für die Suche. Wir sind immer noch nicht <a %(youtube)s>allzu begeistert</a>, es für mission-kritische Daten zu verwenden (obwohl sie viel <a %(elastic_co)s>Fortschritte</a> gemacht haben), aber insgesamt sind wir mit dem Wechsel recht zufrieden."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Für den Moment haben wir eine viel schnellere Suche, bessere Sprachunterstützung, bessere Relevanzsortierung, verschiedene Sortieroptionen und Filterung nach Sprache/Buchtyp/Dateityp implementiert. Wenn Sie neugierig sind, wie es funktioniert, <a %(annas_archive_l140)s>schauen</a> <a %(annas_archive_l1115)s>Sie</a> <a %(annas_archive_l1635)s>es sich an</a>. Es ist ziemlich zugänglich, obwohl es noch einige Kommentare gebrauchen könnte…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ an Buchcovern veröffentlicht"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Schließlich freuen wir uns, eine kleine Veröffentlichung bekannt zu geben. In Zusammenarbeit mit den Leuten, die den Libgen.rs-Fork betreiben, teilen wir alle ihre Buchcover über Torrents und IPFS. Dies wird die Last des Betrachtens der Cover auf mehr Maschinen verteilen und sie besser bewahren. In vielen (aber nicht allen) Fällen sind die Buchcover in den Dateien selbst enthalten, sodass dies eine Art „abgeleitete Daten“ ist. Aber sie in IPFS zu haben, ist immer noch sehr nützlich für den täglichen Betrieb sowohl von Annas Archiv als auch der verschiedenen Library Genesis-Forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Wie üblich finden Sie diese Veröffentlichung im Pirate Library Mirror (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Wir werden hier nicht darauf verlinken, aber Sie können es leicht finden."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hoffentlich können wir unser Tempo ein wenig entspannen, jetzt, da wir eine anständige Alternative zur Z-Library haben. Diese Arbeitsbelastung ist nicht besonders nachhaltig. Wenn Sie daran interessiert sind, bei der Programmierung, dem Serverbetrieb oder der Erhaltungsarbeit zu helfen, kontaktieren Sie uns unbedingt. Es gibt noch viel <a %(annas_archive)s>zu tun</a>. Vielen Dank für Ihr Interesse und Ihre Unterstützung."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annas Archiv hat die weltweit größte Comics-Schattenbibliothek (95TB) gesichert — Sie können helfen, sie zu verbreiten"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Die größte Comics-Schattenbibliothek der Welt hatte einen einzigen Schwachpunkt... bis heute."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskutieren Sie auf Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Die größte Schattenbibliothek für Comics ist wahrscheinlich die eines bestimmten Library Genesis-Forks: Libgen.li. Der eine Administrator, der diese Seite betreibt, hat es geschafft, eine unglaubliche Comics-Sammlung von über 2 Millionen Dateien zu sammeln, die insgesamt über 95TB umfassen. Im Gegensatz zu anderen Library Genesis-Sammlungen war diese jedoch nicht in großen Mengen über Torrents verfügbar. Sie konnten auf diese Comics nur einzeln über seinen langsamen persönlichen Server zugreifen — ein einziger Schwachpunkt. Bis heute!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In diesem Beitrag erzählen wir Ihnen mehr über diese Sammlung und über unsere Spendenaktion, um mehr von dieser Arbeit zu unterstützen."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon versucht, sich in der alltäglichen Welt der Bibliothek zu verlieren…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-Forks"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Zunächst ein wenig Hintergrundwissen. Sie kennen Library Genesis vielleicht wegen ihrer epischen Büchersammlung. Weniger Menschen wissen, dass die Freiwilligen von Library Genesis andere Projekte ins Leben gerufen haben, wie zum Beispiel eine umfangreiche Sammlung von Zeitschriften und Standarddokumenten, ein vollständiges Backup von Sci-Hub (in Zusammenarbeit mit der Gründerin von Sci-Hub, Alexandra Elbakyan) und tatsächlich eine riesige Sammlung von Comics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Irgendwann gingen die verschiedenen Betreiber der Library Genesis-Spiegel getrennte Wege, was zur aktuellen Situation führte, in der es eine Reihe verschiedener „Forks“ gibt, die alle noch den Namen Library Genesis tragen. Der Libgen.li-Fork hat einzigartig diese Comics-Sammlung sowie eine umfangreiche Zeitschriftensammlung (an der wir ebenfalls arbeiten)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Zusammenarbeit"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Aufgrund ihrer Größe stand diese Sammlung schon lange auf unserer Wunschliste, also nahmen wir sie nach unserem Erfolg mit dem Backup von Z-Library ins Visier. Zunächst haben wir sie direkt gescraped, was eine ziemliche Herausforderung war, da ihr Server nicht in bestem Zustand war. Auf diese Weise erhielten wir etwa 15TB, aber es ging nur langsam voran."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Glücklicherweise gelang es uns, den Betreiber der Bibliothek zu kontaktieren, der sich bereit erklärte, uns alle Daten direkt zu senden, was viel schneller war. Es dauerte dennoch mehr als ein halbes Jahr, um alle Daten zu übertragen und zu verarbeiten, und wir hätten fast alles durch Festplattenkorruption verloren, was bedeutet hätte, von vorne zu beginnen."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Diese Erfahrung hat uns glauben lassen, dass es wichtig ist, diese Daten so schnell wie möglich zu verbreiten, damit sie weit und breit gespiegelt werden können. Wir sind nur ein oder zwei unglücklich getimte Vorfälle davon entfernt, diese Sammlung für immer zu verlieren!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Die Sammlung"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Schnelles Handeln bedeutet jedoch, dass die Sammlung ein wenig unorganisiert ist… Schauen wir uns das mal an. Stellen Sie sich vor, wir haben ein Dateisystem (das wir in Wirklichkeit über Torrents aufteilen):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Das erste Verzeichnis, <code>/repository</code>, ist der strukturiertere Teil davon. Dieses Verzeichnis enthält sogenannte „Tausender-Verzeichnisse“: Verzeichnisse, die jeweils tausend Dateien enthalten, die in der Datenbank fortlaufend nummeriert sind. Verzeichnis <code>0</code> enthält Dateien mit comic_id 0–999 und so weiter."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dies ist das gleiche Schema, das Library Genesis für seine Belletristik- und Sachbuchsammlungen verwendet hat. Die Idee ist, dass jedes „Tausender-Verzeichnis“ automatisch in einen Torrent umgewandelt wird, sobald es gefüllt ist."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Der Libgen.li-Betreiber hat jedoch nie Torrents für diese Sammlung erstellt, und so wurden die Tausender-Verzeichnisse wahrscheinlich unpraktisch und machten Platz für „unsortierte Verzeichnisse“. Diese sind <code>/comics0</code> bis <code>/comics4</code>. Sie alle enthalten einzigartige Verzeichnisstrukturen, die wahrscheinlich beim Sammeln der Dateien sinnvoll waren, aber für uns jetzt nicht mehr viel Sinn ergeben. Glücklicherweise verweist das metadata immer noch direkt auf all diese Dateien, sodass ihre Speicherorganisation auf der Festplatte eigentlich keine Rolle spielt!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Das metadata ist in Form einer MySQL-Datenbank verfügbar. Diese kann direkt von der Libgen.li-Website heruntergeladen werden, aber wir werden sie auch in einem Torrent verfügbar machen, zusammen mit unserer eigenen Tabelle mit allen MD5-Hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Wenn Sie 95TB in Ihr Speichersystem geworfen bekommen, versuchen Sie herauszufinden, was überhaupt darin ist… Wir haben einige Analysen durchgeführt, um zu sehen, ob wir die Größe ein wenig reduzieren könnten, zum Beispiel durch das Entfernen von Duplikaten. Hier sind einige unserer Erkenntnisse:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantische Duplikate (verschiedene Scans desselben Buches) können theoretisch herausgefiltert werden, aber es ist knifflig. Beim manuellen Durchsehen der Comics fanden wir zu viele Fehlalarme."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Es gibt einige Duplikate rein nach MD5, was relativ verschwenderisch ist, aber das Herausfiltern dieser würde uns nur etwa 1% in Einsparungen bringen. In diesem Maßstab sind das immer noch etwa 1TB, aber auch in diesem Maßstab spielt 1TB nicht wirklich eine Rolle. Wir möchten nicht riskieren, versehentlich Daten in diesem Prozess zu zerstören."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Wir fanden eine Menge nicht-buchbezogener Daten, wie Filme, die auf Comics basieren. Das scheint auch verschwenderisch, da diese bereits auf andere Weise weit verbreitet sind. Wir erkannten jedoch, dass wir Filmdateien nicht einfach herausfiltern konnten, da es auch <em>interaktive Comics</em> gibt, die auf dem Computer veröffentlicht wurden und die jemand aufgenommen und als Filme gespeichert hat."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Letztendlich würde das Löschen von Teilen der Sammlung nur ein paar Prozent einsparen. Dann erinnerten wir uns daran, dass wir Datenhorter sind, und die Leute, die dies spiegeln werden, sind ebenfalls Datenhorter, und so: „WAS MEINST DU MIT LÖSCHEN?!“ :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Wir präsentieren Ihnen daher die vollständige, unveränderte Sammlung. Es sind viele Daten, aber wir hoffen, dass sich genug Leute finden, die sie trotzdem seeden."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Spendenaktion"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Wir veröffentlichen diese Daten in einigen großen Paketen. Der erste Torrent ist von <code>/comics0</code>, den wir in eine riesige 12TB .tar-Datei gepackt haben. Das ist besser für Ihre Festplatte und Torrent-Software als unzählige kleinere Dateien."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Im Rahmen dieser Veröffentlichung führen wir eine Spendenaktion durch. Wir möchten 20.000 $ sammeln, um die Betriebs- und Vertragskosten für diese Sammlung zu decken und laufende sowie zukünftige Projekte zu ermöglichen. Wir haben einige <em>riesige</em> in Arbeit."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Wen unterstütze ich mit meiner Spende?</em> Kurz gesagt: Wir sichern das gesamte Wissen und die Kultur der Menschheit und machen es leicht zugänglich. All unser Code und unsere Daten sind Open Source, wir sind ein komplett ehrenamtlich geführtes Projekt und haben bisher Bücher im Wert von 125TB gerettet (zusätzlich zu den bestehenden Torrents von Libgen und Scihub). Letztendlich bauen wir ein Schwungrad, das Menschen dazu befähigt und motiviert, alle Bücher der Welt zu finden, zu scannen und zu sichern. Wir werden in einem zukünftigen Beitrag über unseren Masterplan schreiben. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Wenn Sie für eine 12-monatige „Amazing Archivist“-Mitgliedschaft (780 $) spenden, können Sie <strong>„einen Torrent adoptieren“</strong>, was bedeutet, dass wir Ihren Benutzernamen oder Ihre Nachricht im Dateinamen eines der Torrents platzieren!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Sie können spenden, indem Sie zu <a %(wikipedia_annas_archive)s>Annas Archiv</a> gehen und auf die Schaltfläche „Spenden“ klicken. Wir suchen auch nach weiteren Freiwilligen: Software-Ingenieure, Sicherheitsforscher, Experten für anonyme Händler und Übersetzer. Sie können uns auch unterstützen, indem Sie Hosting-Dienste bereitstellen. Und natürlich, bitte seeden Sie unsere Torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Vielen Dank an alle, die uns bereits so großzügig unterstützt haben! Sie machen wirklich einen Unterschied."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hier sind die bisher veröffentlichten Torrents (wir verarbeiten den Rest noch):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alle Torrents finden Sie auf <a %(wikipedia_annas_archive)s>Annas Archiv</a> unter „Datasets“ (wir verlinken dort nicht direkt, damit Links zu diesem Blog nicht von Reddit, Twitter usw. entfernt werden). Von dort aus folgen Sie dem Link zur Tor-Website."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Was kommt als Nächstes?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Eine Menge Torrents sind großartig für die langfristige Bewahrung, aber nicht so sehr für den täglichen Zugriff. Wir werden mit Hosting-Partnern zusammenarbeiten, um all diese Daten im Web verfügbar zu machen (da Annas Archiv nichts direkt hostet). Natürlich werden Sie diese Download-Links auf Annas Archiv finden können."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Wir laden auch alle ein, etwas mit diesen Daten zu machen! Helfen Sie uns, sie besser zu analysieren, zu deduplizieren, auf IPFS zu stellen, sie zu remixen, Ihre KI-Modelle damit zu trainieren und so weiter. Es gehört alles Ihnen, und wir können es kaum erwarten zu sehen, was Sie damit machen."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Schließlich, wie bereits gesagt, haben wir noch einige massive Veröffentlichungen in Vorbereitung (wenn <em>jemand</em> uns <em>versehentlich</em> einen Dump einer <em>bestimmten</em> ACS4-Datenbank senden könnte, wissen Sie, wo Sie uns finden…), sowie den Aufbau des Schwungrads zur Sicherung aller Bücher der Welt."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Bleiben Sie also dran, wir fangen gerade erst an."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x neue Bücher zur Piratenbibliotheksspiegelung hinzugefügt (+24TB, 3,8 Millionen Bücher)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In der ursprünglichen Veröffentlichung des Piratenbibliotheksspiegels (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) haben wir einen Spiegel von Z-Library erstellt, einer großen illegalen Büchersammlung. Zur Erinnerung, dies schrieben wir in diesem ursprünglichen Blogbeitrag:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library ist eine beliebte (und illegale) Bibliothek. Sie haben die Library Genesis-Sammlung übernommen und durchsuchbar gemacht. Darüber hinaus sind sie sehr effektiv darin geworden, neue Buchbeiträge zu erbitten, indem sie beitragende Nutzer mit verschiedenen Vorteilen belohnen. Derzeit tragen sie diese neuen Bücher nicht zurück zu Library Genesis bei. Und im Gegensatz zu Library Genesis machen sie ihre Sammlung nicht leicht spiegelbar, was eine breite Bewahrung verhindert. Dies ist wichtig für ihr Geschäftsmodell, da sie Geld dafür verlangen, auf ihre Sammlung in großen Mengen zuzugreifen (mehr als 10 Bücher pro Tag)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Wir fällen keine moralischen Urteile darüber, Geld für den Massen-Zugang zu einer illegalen Büchersammlung zu verlangen. Es steht außer Zweifel, dass die Z-Library erfolgreich den Zugang zu Wissen erweitert und mehr Bücher beschafft hat. Wir sind einfach hier, um unseren Teil beizutragen: die langfristige Bewahrung dieser privaten Sammlung zu gewährleisten."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Diese Sammlung stammt aus der Mitte des Jahres 2021. In der Zwischenzeit ist die Z-Library in einem atemberaubenden Tempo gewachsen: Sie haben etwa 3,8 Millionen neue Bücher hinzugefügt. Es gibt dort sicherlich einige Duplikate, aber der Großteil scheint tatsächlich neue Bücher oder qualitativ hochwertigere Scans von zuvor eingereichten Büchern zu sein. Dies ist größtenteils auf die gestiegene Anzahl von freiwilligen Moderatoren bei der Z-Library und ihr Massen-Upload-System mit Duplikaterkennung zurückzuführen. Wir möchten ihnen zu diesen Errungenschaften gratulieren."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Wir freuen uns, bekannt zu geben, dass wir alle Bücher erhalten haben, die zwischen unserem letzten Spiegel und August 2022 zur Z-Library hinzugefügt wurden. Wir haben auch einige Bücher nachgeholt, die wir beim ersten Mal verpasst haben. Insgesamt umfasst diese neue Sammlung etwa 24TB, was viel größer ist als die letzte (7TB). Unser Spiegel umfasst jetzt insgesamt 31TB. Erneut haben wir gegen Library Genesis dedupliziert, da für diese Sammlung bereits Torrents verfügbar sind."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Bitte besuchen Sie den Pirate Library Mirror, um die neue Sammlung zu überprüfen (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Dort gibt es mehr Informationen darüber, wie die Dateien strukturiert sind und was sich seit dem letzten Mal geändert hat. Wir werden von hier aus nicht darauf verlinken, da dies nur eine Blog-Website ist, die keine illegalen Materialien hostet."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Natürlich ist auch das Seeden eine großartige Möglichkeit, uns zu unterstützen. Vielen Dank an alle, die unser vorheriges Set von Torrents seeden. Wir sind dankbar für die positive Resonanz und freuen uns, dass es so viele Menschen gibt, die sich auf diese ungewöhnliche Weise um die Bewahrung von Wissen und Kultur kümmern."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Wie man ein Piratenarchivar wird"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Die erste Herausforderung könnte eine überraschende sein. Es ist kein technisches Problem oder ein rechtliches Problem. Es ist ein psychologisches Problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Bevor wir eintauchen, zwei Updates zum Pirate Library Mirror (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Wir haben einige äußerst großzügige Spenden erhalten. Die erste war 10.000 $ von einer anonymen Person, die auch \"bookwarrior\", den ursprünglichen Gründer von Library Genesis, unterstützt hat. Besonderer Dank an bookwarrior für die Vermittlung dieser Spende. Die zweite war eine weitere Spende von 10.000 $ von einem anonymen Spender, der nach unserer letzten Veröffentlichung Kontakt aufnahm und inspiriert war zu helfen. Wir hatten auch eine Reihe kleinerer Spenden. Vielen Dank für all Ihre großzügige Unterstützung. Wir haben einige spannende neue Projekte in der Pipeline, die dadurch unterstützt werden, also bleiben Sie dran."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Wir hatten einige technische Schwierigkeiten mit der Größe unserer zweiten Veröffentlichung, aber unsere Torrents sind jetzt online und werden gehostet. Wir haben auch ein großzügiges Angebot von einer anonymen Person erhalten, unsere Sammlung auf ihren sehr schnellen Servern zu hosten, also machen wir einen speziellen Upload auf ihre Maschinen, nach dem alle anderen, die die Sammlung herunterladen, eine große Verbesserung der Geschwindigkeit sehen sollten."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Es könnten ganze Bücher über das <em>Warum</em> der digitalen Bewahrung im Allgemeinen und des Piratenarchivismus im Besonderen geschrieben werden, aber lassen Sie uns eine kurze Einführung für diejenigen geben, die nicht allzu vertraut sind. Die Welt produziert mehr Wissen und Kultur als je zuvor, aber auch mehr davon geht verloren als je zuvor. Die Menschheit vertraut weitgehend Unternehmen wie akademischen Verlagen, Streaming-Diensten und sozialen Medienunternehmen dieses Erbe an, und sie haben sich oft nicht als großartige Verwalter erwiesen. Schauen Sie sich die Dokumentation Digital Amnesia an oder wirklich jeden Vortrag von Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Es gibt einige Institutionen, die gute Arbeit leisten, so viel wie möglich zu archivieren, aber sie sind an das Gesetz gebunden. Als Piraten sind wir in einer einzigartigen Position, Sammlungen zu archivieren, die sie aufgrund von Urheberrechtsdurchsetzung oder anderen Einschränkungen nicht berühren können. Wir können auch Sammlungen weltweit mehrfach spiegeln, wodurch die Chancen auf eine ordnungsgemäße Bewahrung erhöht werden."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Für den Moment werden wir nicht in Diskussionen über die Vor- und Nachteile des geistigen Eigentums, die Moral des Gesetzesbruchs, Überlegungen zur Zensur oder die Frage des Zugangs zu Wissen und Kultur einsteigen. Mit all dem aus dem Weg, lassen Sie uns in das <em>Wie</em> eintauchen. Wir werden teilen, wie unser Team zu Piratenarchivaren wurde und die Lektionen, die wir auf dem Weg gelernt haben. Es gibt viele Herausforderungen, wenn Sie sich auf diese Reise begeben, und hoffentlich können wir Ihnen bei einigen davon helfen."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Gemeinschaft"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Die erste Herausforderung könnte eine überraschende sein. Es ist kein technisches Problem oder ein rechtliches Problem. Es ist ein psychologisches Problem: Diese Arbeit im Verborgenen zu tun, kann unglaublich einsam sein. Je nachdem, was Sie vorhaben und welches Bedrohungsmodell Sie haben, müssen Sie möglicherweise sehr vorsichtig sein. Am einen Ende des Spektrums haben wir Menschen wie Alexandra Elbakyan*, die Gründerin von Sci-Hub, die sehr offen über ihre Aktivitäten ist. Aber sie ist einem hohen Risiko ausgesetzt, verhaftet zu werden, wenn sie zu diesem Zeitpunkt ein westliches Land besuchen würde, und könnte Jahrzehnte im Gefängnis verbringen. Ist das ein Risiko, das Sie bereit wären einzugehen? Wir sind am anderen Ende des Spektrums; wir sind sehr darauf bedacht, keine Spuren zu hinterlassen und eine starke operative Sicherheit zu haben."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Wie von \"ynno\" auf HN erwähnt, wollte Alexandra anfangs nicht bekannt sein: \"Ihre Server waren so eingerichtet, dass sie detaillierte Fehlermeldungen von PHP ausgaben, einschließlich des vollständigen Pfads der fehlerhaften Quelldatei, die sich im Verzeichnis /home/<USER>" Verwenden Sie also zufällige Benutzernamen auf den Computern, die Sie für diese Dinge verwenden, falls Sie etwas falsch konfigurieren."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Diese Geheimhaltung hat jedoch einen psychologischen Preis. Die meisten Menschen lieben es, für die Arbeit, die sie leisten, anerkannt zu werden, und doch können Sie dafür im wirklichen Leben keine Anerkennung erhalten. Selbst einfache Dinge können herausfordernd sein, wie Freunde, die Sie fragen, was Sie so gemacht haben (irgendwann wird \"mit meinem NAS / Homelab herumspielen\" alt)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Deshalb ist es so wichtig, eine Gemeinschaft zu finden. Sie können etwas von der operativen Sicherheit aufgeben, indem Sie sich einigen sehr engen Freunden anvertrauen, von denen Sie wissen, dass Sie ihnen tief vertrauen können. Selbst dann sollten Sie darauf achten, nichts schriftlich festzuhalten, falls sie ihre E-Mails an die Behörden übergeben müssen oder ihre Geräte auf andere Weise kompromittiert werden."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Noch besser ist es, einige Mitpiraten zu finden. Wenn Ihre engen Freunde daran interessiert sind, sich Ihnen anzuschließen, großartig! Andernfalls könnten Sie online andere finden. Leider ist dies immer noch eine Nischengemeinschaft. Bisher haben wir nur eine Handvoll anderer gefunden, die in diesem Bereich aktiv sind. Gute Ausgangspunkte scheinen die Library Genesis-Foren und r/DataHoarder zu sein. Das Archive Team hat auch gleichgesinnte Personen, obwohl sie innerhalb des Gesetzes operieren (auch wenn in einigen Grauzonen des Gesetzes). Die traditionellen \"Warez\"- und Piraterie-Szenen haben auch Leute, die ähnlich denken."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Wir sind offen für Ideen, wie wir die Gemeinschaft fördern und Ideen erkunden können. Fühlen Sie sich frei, uns auf Twitter oder Reddit zu kontaktieren. Vielleicht könnten wir eine Art Forum oder Chatgruppe einrichten. Eine Herausforderung besteht darin, dass dies auf gängigen Plattformen leicht zensiert werden kann, sodass wir es selbst hosten müssten. Es gibt auch einen Kompromiss zwischen der vollständigen öffentlichen Diskussion (mehr potenzielle Beteiligung) und der privaten Diskussion (um potenziellen „Zielen“ nicht zu verraten, dass wir sie gleich scrapen werden). Darüber müssen wir nachdenken. Lassen Sie uns wissen, ob Sie daran interessiert sind!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekte"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Wenn wir ein Projekt durchführen, hat es mehrere Phasen:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domänenauswahl / Philosophie: Worauf möchten Sie sich grob konzentrieren und warum? Welche einzigartigen Leidenschaften, Fähigkeiten und Umstände können Sie zu Ihrem Vorteil nutzen?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Zielauswahl: Welche spezifische Sammlung werden Sie spiegeln?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata-Scraping: Katalogisierung von Informationen über die Dateien, ohne die (oft viel größeren) Dateien selbst herunterzuladen."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Datenauswahl: Basierend auf den Metadaten wird eingegrenzt, welche Daten derzeit am relevantesten für das Archiv sind. Es könnte alles sein, aber oft gibt es eine vernünftige Möglichkeit, Platz und Bandbreite zu sparen."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Daten-Scraping: Tatsächliches Abrufen der Daten."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Verteilung: Verpackung in Torrents, Ankündigung irgendwo, Leute dazu bringen, es zu verbreiten."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Diese Phasen sind nicht völlig unabhängig voneinander, und oft führen Erkenntnisse aus einer späteren Phase dazu, dass man zu einer früheren Phase zurückkehrt. Zum Beispiel könnten Sie während des Metadata-Scrapings feststellen, dass das von Ihnen ausgewählte Ziel über Abwehrmechanismen verfügt, die über Ihr Können hinausgehen (wie IP-Sperren), sodass Sie zurückgehen und ein anderes Ziel finden."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domänenauswahl / Philosophie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Es mangelt nicht an Wissen und kulturellem Erbe, das gerettet werden muss, was überwältigend sein kann. Deshalb ist es oft nützlich, einen Moment innezuhalten und darüber nachzudenken, was Ihr Beitrag sein kann."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Jeder hat eine andere Art, darüber nachzudenken, aber hier sind einige Fragen, die Sie sich stellen könnten:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Warum interessiert Sie das? Wofür brennen Sie? Wenn wir eine Gruppe von Menschen zusammenbringen können, die alle die Arten von Dingen archivieren, die ihnen besonders am Herzen liegen, würde das viel abdecken! Sie werden viel mehr wissen als der Durchschnittsmensch über Ihre Leidenschaft, wie welche wichtigen Daten zu speichern sind, welche die besten Sammlungen und Online-Communities sind und so weiter."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Welche Fähigkeiten haben Sie, die Sie zu Ihrem Vorteil nutzen können? Zum Beispiel, wenn Sie ein Experte für Online-Sicherheit sind, können Sie Wege finden, IP-Sperren für sichere Ziele zu überwinden. Wenn Sie großartig darin sind, Gemeinschaften zu organisieren, dann können Sie vielleicht einige Leute um ein Ziel versammeln. Es ist jedoch nützlich, etwas Programmierung zu kennen, wenn auch nur, um während dieses Prozesses eine gute Betriebssicherheit zu gewährleisten."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Wie viel Zeit haben Sie dafür? Unser Rat wäre, klein anzufangen und größere Projekte zu machen, wenn Sie den Dreh raus haben, aber es kann alles verzehrend werden."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Was wäre ein Bereich mit hohem Hebel, auf den Sie sich konzentrieren könnten? Wenn Sie X Stunden mit Piratenarchivierung verbringen werden, wie können Sie dann das größte „Ergebnis für Ihren Einsatz“ erzielen?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Welche einzigartigen Wege denken Sie darüber nach? Sie könnten einige interessante Ideen oder Ansätze haben, die andere möglicherweise übersehen haben."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In unserem Fall lag uns besonders die langfristige Bewahrung der Wissenschaft am Herzen. Wir wussten von Library Genesis und wie es viele Male vollständig über Torrents gespiegelt wurde. Diese Idee gefiel uns. Eines Tages versuchte einer von uns, einige wissenschaftliche Lehrbücher auf Library Genesis zu finden, konnte sie aber nicht finden, was die Vollständigkeit wirklich in Frage stellte. Wir suchten dann diese Lehrbücher online und fanden sie an anderen Orten, was den Samen für unser Projekt pflanzte. Schon bevor wir von der Z-Library wussten, hatten wir die Idee, nicht zu versuchen, all diese Bücher manuell zu sammeln, sondern uns darauf zu konzentrieren, bestehende Sammlungen zu spiegeln und sie zurück zu Library Genesis beizutragen."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Zielauswahl"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Also, wir haben unser Zielgebiet festgelegt, aber welche spezifische Sammlung spiegeln wir? Es gibt ein paar Dinge, die ein gutes Ziel ausmachen:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Groß"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Einzigartig: nicht bereits gut von anderen Projekten abgedeckt."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Zugänglich: verwendet nicht viele Schutzschichten, um zu verhindern, dass Sie ihre metadata und Daten scrapen."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Besonderer Einblick: Sie haben spezielle Informationen über dieses Ziel, wie z. B. besonderen Zugang zu dieser Sammlung oder Sie haben herausgefunden, wie man ihre Abwehrmechanismen überwindet. Dies ist nicht erforderlich (unser bevorstehendes Projekt macht nichts Besonderes), aber es hilft sicherlich!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Als wir unsere Wissenschaftslehrbücher auf anderen Websites als Library Genesis fanden, versuchten wir herauszufinden, wie sie ihren Weg ins Internet gefunden hatten. Dann entdeckten wir die Z-Library und erkannten, dass, obwohl die meisten Bücher dort nicht zuerst erscheinen, sie schließlich dort landen. Wir erfuhren von ihrer Beziehung zu Library Genesis und der (finanziellen) Anreizstruktur sowie der überlegenen Benutzeroberfläche, die beide zu einer viel vollständigeren Sammlung führten. Wir führten dann einige Voruntersuchungen zum metadata- und Datenscraping durch und erkannten, dass wir ihre IP-Download-Beschränkungen umgehen konnten, indem wir den speziellen Zugang eines unserer Mitglieder zu vielen Proxy-Servern nutzten."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Während Sie verschiedene Ziele erkunden, ist es bereits wichtig, Ihre Spuren zu verwischen, indem Sie VPNs und Wegwerf-E-Mail-Adressen verwenden, worüber wir später noch mehr sprechen werden."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata-Scraping"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Lassen Sie uns hier etwas technischer werden. Um tatsächlich metadata von Websites zu scrapen, haben wir die Dinge ziemlich einfach gehalten. Wir verwenden Python-Skripte, manchmal curl, und eine MySQL-Datenbank, um die Ergebnisse zu speichern. Wir haben keine ausgeklügelte Scraping-Software verwendet, die komplexe Websites abbilden kann, da wir bisher nur eine oder zwei Arten von Seiten scrapen mussten, indem wir einfach IDs durchlaufen und das HTML parsen. Wenn es keine leicht aufzählbaren Seiten gibt, benötigen Sie möglicherweise einen richtigen Crawler, der versucht, alle Seiten zu finden."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Bevor Sie eine ganze Website scrapen, versuchen Sie es eine Weile manuell. Gehen Sie selbst durch ein paar Dutzend Seiten, um ein Gefühl dafür zu bekommen, wie das funktioniert. Manchmal stoßen Sie auf diese Weise bereits auf IP-Sperren oder anderes interessantes Verhalten. Das Gleiche gilt für das Datenscraping: Bevor Sie sich zu tief in dieses Ziel vertiefen, stellen Sie sicher, dass Sie seine Daten tatsächlich effektiv herunterladen können."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Um Einschränkungen zu umgehen, gibt es ein paar Dinge, die Sie ausprobieren können. Gibt es andere IP-Adressen oder Server, die dieselben Daten hosten, aber nicht dieselben Einschränkungen haben? Gibt es API-Endpunkte, die keine Einschränkungen haben, während andere dies tun? Bei welcher Download-Rate wird Ihre IP blockiert und wie lange? Oder werden Sie nicht blockiert, sondern gedrosselt? Was passiert, wenn Sie ein Benutzerkonto erstellen, wie ändern sich die Dinge dann? Können Sie HTTP/2 verwenden, um Verbindungen offen zu halten, und erhöht das die Rate, mit der Sie Seiten anfordern können? Gibt es Seiten, die mehrere Dateien auf einmal auflisten, und sind die dort aufgeführten Informationen ausreichend?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dinge, die Sie wahrscheinlich speichern möchten, sind:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Dateiname / Speicherort"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kann eine interne ID sein, aber IDs wie ISBN oder DOI sind ebenfalls nützlich."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Größe: um zu berechnen, wie viel Speicherplatz Sie benötigen."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): um zu bestätigen, dass Sie die Datei ordnungsgemäß heruntergeladen haben."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Hinzugefügtes/Geändertes Datum: damit Sie später zurückkehren und Dateien herunterladen können, die Sie zuvor nicht heruntergeladen haben (obwohl Sie dafür oft auch die ID oder den Hash verwenden können)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beschreibung, Kategorie, Tags, Autoren, Sprache usw."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Wir machen dies typischerweise in zwei Stufen. Zuerst laden wir die rohen HTML-Dateien herunter, normalerweise direkt in MySQL (um viele kleine Dateien zu vermeiden, worüber wir weiter unten mehr sprechen). Dann gehen wir in einem separaten Schritt durch diese HTML-Dateien und parsen sie in tatsächliche MySQL-Tabellen. Auf diese Weise müssen Sie nicht alles von Grund auf neu herunterladen, wenn Sie einen Fehler in Ihrem Parsing-Code entdecken, da Sie die HTML-Dateien einfach mit dem neuen Code erneut verarbeiten können. Es ist auch oft einfacher, den Verarbeitungsschritt zu parallelisieren, was Zeit spart (und Sie können den Verarbeitungscode schreiben, während das Scraping läuft, anstatt beide Schritte gleichzeitig schreiben zu müssen)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Schließlich sei darauf hingewiesen, dass für einige Ziele das Scraping von Metadaten alles ist, was es gibt. Es gibt einige riesige Metadatensammlungen, die nicht ordnungsgemäß erhalten sind."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Datenauswahl"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Oft kann man die Metadaten nutzen, um einen vernünftigen Datenausschnitt zum Herunterladen zu bestimmen. Selbst wenn Sie letztendlich alle Daten herunterladen möchten, kann es nützlich sein, die wichtigsten Elemente zuerst zu priorisieren, falls Sie entdeckt werden und die Abwehrmaßnahmen verbessert werden, oder weil Sie mehr Festplatten kaufen müssten, oder einfach weil etwas anderes in Ihrem Leben passiert, bevor Sie alles herunterladen können."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Zum Beispiel könnte eine Sammlung mehrere Ausgaben derselben zugrunde liegenden Ressource (wie ein Buch oder ein Film) haben, wobei eine als die beste Qualität markiert ist. Diese Ausgaben zuerst zu speichern, wäre sehr sinnvoll. Möglicherweise möchten Sie schließlich alle Ausgaben speichern, da in einigen Fällen die Metadaten falsch markiert sein könnten oder es unbekannte Kompromisse zwischen den Ausgaben geben könnte (zum Beispiel könnte die \"beste Ausgabe\" in den meisten Aspekten die beste sein, aber in anderen schlechter, wie ein Film mit höherer Auflösung, aber ohne Untertitel)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Sie können auch Ihre Metadatendatenbank durchsuchen, um interessante Dinge zu finden. Was ist die größte Datei, die gehostet wird, und warum ist sie so groß? Was ist die kleinste Datei? Gibt es interessante oder unerwartete Muster in Bezug auf bestimmte Kategorien, Sprachen und so weiter? Gibt es doppelte oder sehr ähnliche Titel? Gibt es Muster, wann Daten hinzugefügt wurden, wie ein Tag, an dem viele Dateien auf einmal hinzugefügt wurden? Man kann oft viel lernen, indem man den Datensatz auf verschiedene Weise betrachtet."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In unserem Fall haben wir Z-Library-Bücher gegen die md5-Hashes in Library Genesis dedupliziert und dadurch viel Downloadzeit und Speicherplatz gespart. Dies ist jedoch eine ziemlich einzigartige Situation. In den meisten Fällen gibt es keine umfassenden Datenbanken darüber, welche Dateien bereits von anderen Piraten ordnungsgemäß erhalten werden. Dies ist an sich eine große Chance für jemanden da draußen. Es wäre großartig, eine regelmäßig aktualisierte Übersicht über Dinge wie Musik und Filme zu haben, die bereits weit verbreitet auf Torrent-Websites gesät werden und daher eine geringere Priorität haben, in Piraten-Spiegeln aufgenommen zu werden."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Daten-Scraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Jetzt sind Sie bereit, die Daten tatsächlich in großen Mengen herunterzuladen. Wie bereits erwähnt, sollten Sie zu diesem Zeitpunkt bereits manuell eine Reihe von Dateien heruntergeladen haben, um das Verhalten und die Einschränkungen des Ziels besser zu verstehen. Es wird jedoch immer noch Überraschungen geben, wenn Sie tatsächlich viele Dateien auf einmal herunterladen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Unser Rat hier ist hauptsächlich, es einfach zu halten. Beginnen Sie einfach damit, eine Reihe von Dateien herunterzuladen. Sie können Python verwenden und dann auf mehrere Threads erweitern. Aber manchmal ist es noch einfacher, Bash-Dateien direkt aus der Datenbank zu generieren und dann mehrere davon in mehreren Terminalfenstern auszuführen, um zu skalieren. Ein schneller technischer Trick, der hier erwähnenswert ist, ist die Verwendung von OUTFILE in MySQL, das Sie überall schreiben können, wenn Sie \"secure_file_priv\" in mysqld.cnf deaktivieren (und stellen Sie sicher, dass Sie auch AppArmor deaktivieren/überschreiben, wenn Sie Linux verwenden)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Wir speichern die Daten auf einfachen Festplatten. Beginnen Sie mit dem, was Sie haben, und erweitern Sie langsam. Es kann überwältigend sein, über die Speicherung von Hunderten von TBs an Daten nachzudenken. Wenn das die Situation ist, der Sie gegenüberstehen, stellen Sie einfach zuerst einen guten Ausschnitt bereit und bitten Sie in Ihrer Ankündigung um Hilfe bei der Speicherung des Rests. Wenn Sie selbst mehr Festplatten erwerben möchten, hat r/DataHoarder einige gute Ressourcen, um gute Angebote zu finden."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Versuchen Sie, sich nicht zu sehr um ausgefallene Dateisysteme zu kümmern. Es ist leicht, in das Kaninchenloch zu fallen, Dinge wie ZFS einzurichten. Ein technisches Detail, dessen Sie sich bewusst sein sollten, ist jedoch, dass viele Dateisysteme nicht gut mit vielen Dateien umgehen. Wir haben festgestellt, dass eine einfache Lösung darin besteht, mehrere Verzeichnisse zu erstellen, z. B. für verschiedene ID-Bereiche oder Hash-Präfixe."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Nach dem Herunterladen der Daten sollten Sie die Integrität der Dateien mit Hilfe von Hashes in den Metadaten überprüfen, falls verfügbar."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Verteilung"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Sie haben die Daten und besitzen damit wahrscheinlich den weltweit ersten Piraten-Spiegel Ihres Ziels. In vielerlei Hinsicht ist der schwierigste Teil vorbei, aber der riskanteste Teil steht Ihnen noch bevor. Schließlich waren Sie bisher unauffällig; unter dem Radar fliegend. Alles, was Sie tun mussten, war, währenddessen ein gutes VPN zu verwenden, keine persönlichen Daten in irgendwelche Formulare einzutragen (klar), und vielleicht eine spezielle Browsersitzung (oder sogar einen anderen Computer) zu verwenden."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Jetzt müssen Sie die Daten verteilen. In unserem Fall wollten wir zuerst die Bücher an Library Genesis zurückgeben, stellten jedoch schnell die Schwierigkeiten dabei fest (Sortierung von Fiktion vs. Sachbuch). Also entschieden wir uns für die Verteilung über Torrents im Library Genesis-Stil. Wenn Sie die Möglichkeit haben, zu einem bestehenden Projekt beizutragen, könnte das Ihnen viel Zeit sparen. Es gibt jedoch derzeit nicht viele gut organisierte Piraten-Spiegel."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Angenommen, Sie entscheiden sich dafür, Torrents selbst zu verteilen. Versuchen Sie, diese Dateien klein zu halten, damit sie leicht auf anderen Websites gespiegelt werden können. Sie müssen dann die Torrents selbst seeden, während Sie anonym bleiben. Sie können ein VPN verwenden (mit oder ohne Portweiterleitung) oder mit getumblten Bitcoins für eine Seedbox bezahlen. Wenn Sie nicht wissen, was einige dieser Begriffe bedeuten, haben Sie eine Menge zu lesen, da es wichtig ist, dass Sie die Risikokompromisse hier verstehen."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Sie können die Torrent-Dateien selbst auf bestehenden Torrent-Websites hosten. In unserem Fall haben wir uns entschieden, tatsächlich eine Website zu hosten, da wir auch unsere Philosophie klar verbreiten wollten. Sie können dies auf ähnliche Weise selbst tun (wir verwenden Njalla für unsere Domains und das Hosting, bezahlt mit getumblten Bitcoins), aber zögern Sie nicht, uns zu kontaktieren, damit wir Ihre Torrents hosten. Wir möchten im Laufe der Zeit ein umfassendes Verzeichnis von Piraten-Spiegeln aufbauen, wenn diese Idee Anklang findet."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Was die VPN-Auswahl betrifft, so wurde darüber bereits viel geschrieben, daher wiederholen wir nur den allgemeinen Rat, nach Ruf zu wählen. Tatsächlich gerichtlich getestete No-Log-Richtlinien mit langjähriger Erfahrung im Schutz der Privatsphäre sind unserer Meinung nach die risikoärmste Option. Beachten Sie, dass Sie selbst dann, wenn Sie alles richtig machen, niemals ein Null-Risiko erreichen können. Zum Beispiel kann ein hochmotivierter staatlicher Akteur beim Seeden Ihrer Torrents wahrscheinlich die ein- und ausgehenden Datenströme für VPN-Server betrachten und herausfinden, wer Sie sind. Oder Sie können einfach irgendwie einen Fehler machen. Wir haben das wahrscheinlich schon getan und werden es wieder tun. Glücklicherweise kümmern sich Staaten nicht <em>so</em> sehr um Piraterie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Eine Entscheidung, die für jedes Projekt zu treffen ist, ist, ob es unter derselben Identität wie zuvor veröffentlicht werden soll oder nicht. Wenn Sie denselben Namen weiter verwenden, könnten Fehler in der Betriebssicherheit aus früheren Projekten Sie einholen. Aber unter verschiedenen Namen zu veröffentlichen bedeutet, dass Sie keinen länger anhaltenden Ruf aufbauen. Wir haben uns entschieden, von Anfang an eine starke Betriebssicherheit zu haben, damit wir dieselbe Identität weiter verwenden können, aber wir zögern nicht, unter einem anderen Namen zu veröffentlichen, wenn wir einen Fehler machen oder die Umstände es erfordern."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Die Bekanntmachung kann knifflig sein. Wie gesagt, dies ist immer noch eine Nischen-Community. Ursprünglich haben wir auf Reddit gepostet, aber wirklich Anklang fanden wir auf Hacker News. Für den Moment empfehlen wir, es an ein paar Stellen zu posten und zu sehen, was passiert. Und nochmals, kontaktieren Sie uns. Wir würden gerne das Wort über mehr Piratenarchivierungsbemühungen verbreiten."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Fazit"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hoffentlich ist dies hilfreich für neu startende Piratenarchivare. Wir freuen uns, Sie in dieser Welt willkommen zu heißen, also zögern Sie nicht, uns zu kontaktieren. Lassen Sie uns so viel Wissen und Kultur der Welt bewahren, wie wir können, und es weit und breit spiegeln."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Einführung des Piratenbibliothek-Spiegels: Bewahrung von 7TB an Büchern (die nicht in Libgen sind)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dieses Projekt (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) zielt darauf ab, zur Bewahrung und Befreiung des menschlichen Wissens beizutragen. Wir leisten unseren kleinen und bescheidenen Beitrag, in den Fußstapfen der Großen vor uns."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Der Fokus dieses Projekts wird durch seinen Namen veranschaulicht:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirat</strong> - Wir verletzen bewusst das Urheberrecht in den meisten Ländern. Dies ermöglicht es uns, etwas zu tun, was legale Einrichtungen nicht können: sicherzustellen, dass Bücher weit und breit gespiegelt werden."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliothek</strong> - Wie die meisten Bibliotheken konzentrieren wir uns hauptsächlich auf schriftliche Materialien wie Bücher. Möglicherweise erweitern wir uns in Zukunft auf andere Medienarten."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spiegel</strong> - Wir sind streng genommen ein Spiegel bestehender Bibliotheken. Wir konzentrieren uns auf die Bewahrung, nicht darauf, Bücher leicht durchsuchbar und herunterladbar zu machen (Zugang) oder eine große Gemeinschaft von Menschen zu fördern, die neue Bücher beitragen (Beschaffung)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Die erste Bibliothek, die wir gespiegelt haben, ist Z-Library. Dies ist eine beliebte (und illegale) Bibliothek. Sie haben die Library Genesis-Sammlung übernommen und sie leicht durchsuchbar gemacht. Darüber hinaus sind sie sehr effektiv darin geworden, neue Buchbeiträge zu erbitten, indem sie beitragende Nutzer mit verschiedenen Vorteilen belohnen. Derzeit tragen sie diese neuen Bücher nicht zurück zu Library Genesis bei. Und im Gegensatz zu Library Genesis machen sie ihre Sammlung nicht leicht spiegelbar, was eine breite Bewahrung verhindert. Dies ist wichtig für ihr Geschäftsmodell, da sie Geld für den Zugriff auf ihre Sammlung in großen Mengen (mehr als 10 Bücher pro Tag) verlangen."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Wir fällen keine moralischen Urteile darüber, Geld für den Massen-Zugang zu einer illegalen Büchersammlung zu verlangen. Es steht außer Zweifel, dass die Z-Library erfolgreich den Zugang zu Wissen erweitert und mehr Bücher beschafft hat. Wir sind einfach hier, um unseren Teil beizutragen: die langfristige Bewahrung dieser privaten Sammlung zu gewährleisten."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Wir möchten Sie einladen, dabei zu helfen, menschliches Wissen zu bewahren und zu befreien, indem Sie unsere Torrents herunterladen und seeden. Weitere Informationen zur Organisation der Daten finden Sie auf der Projektseite."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Wir laden Sie auch herzlich ein, Ihre Ideen dazu beizutragen, welche Sammlungen als nächstes gespiegelt werden sollen und wie wir dies angehen können. Gemeinsam können wir viel erreichen. Dies ist nur ein kleiner Beitrag unter unzähligen anderen. Danke für alles, was Sie tun."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Wir verlinken nicht auf die Dateien von diesem Blog. Bitte finden Sie es selbst.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb-Dump, oder Wie viele Bücher sind für immer bewahrt?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Wenn wir die Dateien aus Schattenbibliotheken richtig deduplizieren würden, welchen Prozentsatz aller Bücher der Welt haben wir bewahrt?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Mit dem Piratenbibliothek-Spiegel (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) ist es unser Ziel, alle Bücher der Welt zu nehmen und sie für immer zu bewahren.<sup>1</sup> Zwischen unseren Z-Library-Torrents und den originalen Library Genesis-Torrents haben wir 11.783.153 Dateien. Aber wie viele sind das wirklich? Wenn wir diese Dateien richtig deduplizieren würden, welchen Prozentsatz aller Bücher der Welt haben wir bewahrt? Wir hätten wirklich gerne so etwas wie dies:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of des schriftlichen Erbes der Menschheit für immer bewahrt"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Für einen Prozentsatz benötigen wir einen Nenner: die Gesamtzahl der jemals veröffentlichten Bücher.<sup>2</sup> Vor dem Ende von Google Books versuchte ein Ingenieur des Projekts, Leonid Taycher, <a %(booksearch_blogspot)s>diese Zahl zu schätzen</a>. Er kam — scherzhaft — auf 129.864.880 („zumindest bis Sonntag“). Er schätzte diese Zahl, indem er eine einheitliche Datenbank aller Bücher der Welt erstellte. Dafür zog er verschiedene Datasets zusammen und fusionierte sie auf verschiedene Weise."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Als kurze Randbemerkung: Es gibt eine weitere Person, die versucht hat, alle Bücher der Welt zu katalogisieren: Aaron Swartz, der verstorbene digitale Aktivist und Mitbegründer von Reddit.<sup>3</sup> Er <a %(youtube)s>gründete Open Library</a> mit dem Ziel, „eine Webseite für jedes jemals veröffentlichte Buch“ zu schaffen, indem er Daten aus vielen verschiedenen Quellen kombinierte. Er bezahlte den ultimativen Preis für seine Arbeit zur digitalen Bewahrung, als er wegen des massenhaften Herunterladens wissenschaftlicher Aufsätze strafrechtlich verfolgt wurde, was zu seinem Suizid führte. Es versteht sich von selbst, dass dies einer der Gründe ist, warum unsere Gruppe pseudonym ist und warum wir sehr vorsichtig sind. Open Library wird immer noch heldenhaft von den Leuten beim Internet Archive betrieben und setzt Aarons Vermächtnis fort. Wir werden später in diesem Beitrag darauf zurückkommen."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "In dem Google-Blogbeitrag beschreibt Taycher einige der Herausforderungen bei der Schätzung dieser Zahl. Zunächst einmal: Was ist ein Buch? Es gibt einige mögliche Definitionen:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Physische Exemplare.</strong> Offensichtlich ist das nicht sehr hilfreich, da sie nur Duplikate desselben Materials sind. Es wäre cool, wenn wir alle Anmerkungen, die Menschen in Büchern machen, bewahren könnten, wie Fermats berühmte „Kritzeleien am Rand“. Aber leider wird das ein Traum für Archivare bleiben."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Werke“.</strong> Zum Beispiel „Harry Potter und die Kammer des Schreckens“ als logisches Konzept, das alle Versionen davon umfasst, wie verschiedene Übersetzungen und Nachdrucke. Dies ist eine Art nützliche Definition, aber es kann schwierig sein, die Grenze zu ziehen, was zählt. Zum Beispiel möchten wir wahrscheinlich verschiedene Übersetzungen bewahren, obwohl Nachdrucke mit nur geringfügigen Unterschieden möglicherweise nicht so wichtig sind."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Ausgaben“.</strong> Hier zählt man jede einzigartige Version eines Buches. Wenn irgendetwas daran anders ist, wie ein anderes Cover oder ein anderes Vorwort, zählt es als eine andere Ausgabe."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Dateien.</strong> Bei der Arbeit mit Schattenbibliotheken wie Library Genesis, Sci-Hub oder Z-Library gibt es eine zusätzliche Überlegung. Es kann mehrere Scans derselben Ausgabe geben. Und Menschen können bessere Versionen bestehender Dateien erstellen, indem sie den Text mit OCR scannen oder Seiten korrigieren, die schräg gescannt wurden. Wir möchten diese Dateien nur als eine Ausgabe zählen, was gute metadata oder eine Deduplizierung mit Dokumentähnlichkeitsmaßen erfordern würde."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Ausgaben“ scheinen die praktischste Definition dessen zu sein, was „Bücher“ sind. Bequemerweise wird diese Definition auch zur Vergabe einzigartiger ISBN-Nummern verwendet. Eine ISBN, oder Internationale Standardbuchnummer, wird häufig für den internationalen Handel verwendet, da sie in das internationale Barcode-System („International Article Number“) integriert ist. Wenn Sie ein Buch in Geschäften verkaufen möchten, benötigt es einen Barcode, also erhalten Sie eine ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taychers Blogbeitrag erwähnt, dass, obwohl ISBNs nützlich sind, sie nicht universell sind, da sie erst Mitte der siebziger Jahre wirklich eingeführt wurden und nicht überall auf der Welt. Dennoch ist die ISBN wahrscheinlich der am weitesten verbreitete Identifikator für Buchausgaben, daher ist sie unser bester Ausgangspunkt. Wenn wir alle ISBNs der Welt finden können, erhalten wir eine nützliche Liste, welche Bücher noch bewahrt werden müssen."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Woher bekommen wir also die Daten? Es gibt eine Reihe bestehender Bemühungen, die versuchen, eine Liste aller Bücher der Welt zu erstellen:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Schließlich haben sie diese Forschung für Google Books durchgeführt. Allerdings sind ihre metadata nicht in großen Mengen zugänglich und ziemlich schwer zu scrapen."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Wie bereits erwähnt, ist dies ihre gesamte Mission. Sie haben massive Mengen an Bibliotheksdaten von kooperierenden Bibliotheken und nationalen Archiven bezogen und tun dies weiterhin. Sie haben auch freiwillige Bibliothekare und ein technisches Team, das versucht, Datensätze zu deduplizieren und sie mit allen möglichen metadata zu versehen. Am besten ist, dass ihr Datensatz vollständig offen ist. Sie können ihn einfach <a %(openlibrary)s>herunterladen</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dies ist eine Website, die von der gemeinnützigen OCLC betrieben wird, die Bibliotheksverwaltungssysteme verkauft. Sie aggregieren Buchmetadata aus vielen Bibliotheken und stellen sie über die WorldCat-Website zur Verfügung. Allerdings verdienen sie auch Geld mit dem Verkauf dieser Daten, sodass sie nicht für den Massen-Download verfügbar sind. Sie haben einige begrenztere Massendatensätze zum Download verfügbar, in Zusammenarbeit mit bestimmten Bibliotheken."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dies ist das Thema dieses Blogbeitrags. ISBNdb scrapt verschiedene Websites nach Buchmetadata, insbesondere Preisdaten, die sie dann an Buchhändler verkaufen, damit diese ihre Bücher im Einklang mit dem Rest des Marktes bepreisen können. Da ISBNs heutzutage ziemlich universell sind, haben sie effektiv eine „Webseite für jedes Buch“ erstellt."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Verschiedene individuelle Bibliothekssysteme und Archive.</strong> Es gibt Bibliotheken und Archive, die von keiner der oben genannten indiziert und aggregiert wurden, oft weil sie unterfinanziert sind oder aus anderen Gründen ihre Daten nicht mit Open Library, OCLC, Google usw. teilen möchten. Viele davon haben digitale Aufzeichnungen, die über das Internet zugänglich sind, und sie sind oft nicht sehr gut geschützt. Wenn Sie also helfen und Spaß daran haben möchten, seltsame Bibliothekssysteme kennenzulernen, sind dies großartige Ausgangspunkte."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In diesem Beitrag freuen wir uns, eine kleine Veröffentlichung (im Vergleich zu unseren vorherigen Z-Library-Veröffentlichungen) anzukündigen. Wir haben den Großteil von ISBNdb gescrapt und die Daten zum Torrenting auf der Website des Pirate Library Mirror verfügbar gemacht (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>; wir werden es hier nicht direkt verlinken, suchen Sie einfach danach). Dies sind etwa 30,9 Millionen Datensätze (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gezippt). Auf ihrer Website behaupten sie, tatsächlich 32,6 Millionen Datensätze zu haben, also haben wir möglicherweise einige übersehen, oder <em>sie</em> könnten etwas falsch machen. In jedem Fall werden wir vorerst nicht genau mitteilen, wie wir es gemacht haben — wir überlassen das als Übung dem Leser. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Was wir teilen werden, ist eine vorläufige Analyse, um der Schätzung der Anzahl der Bücher in der Welt näher zu kommen. Wir haben uns drei Datensätze angesehen: diesen neuen ISBNdb-Datensatz, unsere ursprüngliche Veröffentlichung von metadata, die wir aus der Z-Library Schattenbibliothek (die Library Genesis einschließt) gescrapt haben, und den Open Library Daten-Dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Lassen Sie uns mit einigen groben Zahlen beginnen:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In sowohl Z-Library/Libgen als auch Open Library gibt es viel mehr Bücher als einzigartige ISBNs. Bedeutet das, dass viele dieser Bücher keine ISBNs haben, oder fehlen die ISBN metadata einfach? Wir können diese Frage wahrscheinlich mit einer Kombination aus automatisiertem Abgleich basierend auf anderen Attributen (Titel, Autor, Verlag usw.), dem Einbeziehen weiterer Datenquellen und dem Extrahieren von ISBNs aus den tatsächlichen Buchscans selbst (im Fall von Z-Library/Libgen) beantworten."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Wie viele dieser ISBNs sind einzigartig? Dies wird am besten mit einem Venn-Diagramm veranschaulicht:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Um genauer zu sein:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Wir waren überrascht, wie wenig Überschneidungen es gibt! ISBNdb hat eine riesige Menge an ISBNs, die weder in der Z-Library noch in der Open Library auftauchen, und das Gleiche gilt (in einem kleineren, aber immer noch erheblichen Ausmaß) für die anderen beiden. Das wirft viele neue Fragen auf. Wie sehr würde automatisches Matching helfen, die Bücher zu kennzeichnen, die nicht mit ISBNs versehen wurden? Würde es viele Übereinstimmungen und damit eine erhöhte Überschneidung geben? Und was würde passieren, wenn wir ein viertes oder fünftes Dataset hinzuziehen? Wie viel Überschneidung würden wir dann sehen?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Das gibt uns einen Ausgangspunkt. Wir können nun alle ISBNs betrachten, die nicht im Z-Library-Dataset enthalten sind und die auch nicht mit Titel-/Autorenfeldern übereinstimmen. Das kann uns helfen, alle Bücher der Welt zu bewahren: zuerst durch das Scraping des Internets nach Scans, dann durch das Scannen von Büchern im echten Leben. Letzteres könnte sogar durch Crowdfunding finanziert oder durch „Prämien“ von Personen angetrieben werden, die bestimmte Bücher digitalisiert sehen möchten. All das ist eine Geschichte für eine andere Zeit."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Wenn Sie bei einem dieser Themen helfen möchten — weitere Analysen; mehr metadata scrapen; mehr Bücher finden; Bücher OCRen; dies für andere Bereiche tun (z. B. wissenschaftliche Aufsätze, Hörbücher, Filme, Fernsehsendungen, Zeitschriften) oder sogar einige dieser Daten für Dinge wie ML / Training von großen Sprachmodellen verfügbar machen — kontaktieren Sie mich bitte (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Wenn Sie sich speziell für die Datenanalyse interessieren, arbeiten wir daran, unsere Datasets und Skripte in einem benutzerfreundlicheren Format verfügbar zu machen. Es wäre großartig, wenn Sie einfach ein Notebook forken und damit herumspielen könnten."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Wenn Sie diese Arbeit unterstützen möchten, ziehen Sie bitte eine Spende in Betracht. Dies ist eine vollständig ehrenamtlich geführte Operation, und Ihr Beitrag macht einen großen Unterschied. Jeder Beitrag hilft. Derzeit nehmen wir Spenden in Kryptowährung an; siehe die Spendenseite auf Annas Archiv."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Für eine vernünftige Definition von „für immer“. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Natürlich ist das schriftliche Erbe der Menschheit viel mehr als Bücher, besonders heutzutage. Für diesen Beitrag und unsere jüngsten Veröffentlichungen konzentrieren wir uns auf Bücher, aber unsere Interessen reichen weiter."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Es gibt viel mehr über Aaron Swartz zu sagen, aber wir wollten ihn nur kurz erwähnen, da er eine zentrale Rolle in dieser Geschichte spielt. Mit der Zeit könnten mehr Menschen seinen Namen zum ersten Mal hören und sich dann selbst in das Thema vertiefen."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Das kritische Fenster der Schattenbibliotheken"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Wie können wir behaupten, unsere Sammlungen auf ewig zu bewahren, wenn sie bereits 1 PB erreichen?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Chinesische Version 中文版</a>, diskutieren Sie auf <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "In Annas Archiv werden wir oft gefragt, wie wir behaupten können, unsere Sammlungen auf ewig zu bewahren, wenn die Gesamtgröße bereits 1 Petabyte (1000 TB) erreicht und weiter wächst. In diesem Artikel werden wir unsere Philosophie betrachten und sehen, warum das nächste Jahrzehnt für unsere Mission, das Wissen und die Kultur der Menschheit zu bewahren, entscheidend ist."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Die <a %(annas_archive_stats)s>Gesamtgröße</a> unserer Sammlungen, in den letzten Monaten aufgeschlüsselt nach der Anzahl der Torrent-Seeder."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritäten"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Warum kümmern wir uns so sehr um wissenschaftliche Aufsätze und Bücher? Lassen Sie uns unseren grundlegenden Glauben an die Bewahrung im Allgemeinen beiseitelegen — wir könnten einen weiteren Beitrag darüber schreiben. Warum also speziell wissenschaftliche Aufsätze und Bücher? Die Antwort ist einfach: <strong>Informationsdichte</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Pro Megabyte Speicherplatz speichert geschriebener Text die meiste Information aller Medien. Während uns sowohl Wissen als auch Kultur wichtig sind, liegt unser Schwerpunkt mehr auf Ersterem. Insgesamt finden wir eine Hierarchie der Informationsdichte und der Wichtigkeit der Bewahrung, die ungefähr so aussieht:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Wissenschaftliche Aufsätze, Zeitschriften, Berichte"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organische Daten wie DNA-Sequenzen, Pflanzensamen oder mikrobielle Proben"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Sachbücher"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Wissenschafts- und Ingenieursoftware-Code"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Messdaten wie wissenschaftliche Messungen, Wirtschaftsdaten, Unternehmensberichte"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Wissenschafts- und Ingenieurwebsites, Online-Diskussionen"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Sachzeitschriften, Zeitungen, Handbücher"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Sachtranskripte von Vorträgen, Dokumentationen, Podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Interne Daten von Unternehmen oder Regierungen (Lecks)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadatenaufzeichnungen im Allgemeinen (von Sach- und Belletristik; von anderen Medien, Kunst, Personen usw.; einschließlich Rezensionen)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografische Daten (z. B. Karten, geologische Erhebungen)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkripte von rechtlichen oder gerichtlichen Verfahren"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiktionale oder unterhaltende Versionen all dieser Kategorien"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Die Rangfolge in dieser Liste ist etwas willkürlich – mehrere Punkte sind gleichwertig oder es gibt Meinungsverschiedenheiten innerhalb unseres Teams – und wir vergessen wahrscheinlich einige wichtige Kategorien. Aber so priorisieren wir grob."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Einige dieser Punkte sind zu unterschiedlich von den anderen, um uns Sorgen zu machen (oder werden bereits von anderen Institutionen abgedeckt), wie organische Daten oder geografische Daten. Aber die meisten der Punkte in dieser Liste sind tatsächlich wichtig für uns."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Ein weiterer großer Faktor bei unserer Priorisierung ist, wie gefährdet ein bestimmtes Werk ist. Wir konzentrieren uns lieber auf Werke, die:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Selten"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Einzigartig unterfokussiert"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Einzigartig gefährdet sind (z. B. durch Krieg, Budgetkürzungen, Klagen oder politische Verfolgung)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Schließlich ist uns der Maßstab wichtig. Wir haben begrenzte Zeit und Geld, also würden wir lieber einen Monat damit verbringen, 10.000 Bücher zu retten als 1.000 Bücher – wenn sie ungefähr gleich wertvoll und gefährdet sind."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Schattenbibliotheken"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Es gibt viele Organisationen mit ähnlichen Missionen und Prioritäten. Tatsächlich gibt es Bibliotheken, Archive, Labore, Museen und andere Institutionen, die mit der Erhaltung dieser Art von Materialien beauftragt sind. Viele davon sind gut finanziert, von Regierungen, Einzelpersonen oder Unternehmen. Aber sie haben einen massiven blinden Fleck: das Rechtssystem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hierin liegt die einzigartige Rolle der Schattenbibliotheken und der Grund, warum Annas Archiv existiert. Wir können Dinge tun, die anderen Institutionen nicht erlaubt sind. Nun, es ist nicht (oft) so, dass wir Materialien archivieren können, die anderswo illegal zu bewahren sind. Nein, es ist in vielen Orten legal, ein Archiv mit beliebigen Büchern, wissenschaftlichen Aufsätzen, Zeitschriften usw. zu erstellen."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Aber was legale Archive oft fehlt, ist <strong>Redundanz und Langlebigkeit</strong>. Es gibt Bücher, von denen nur ein Exemplar in irgendeiner physischen Bibliothek existiert. Es gibt Metadatensätze, die von einem einzigen Unternehmen bewacht werden. Es gibt Zeitungen, die nur auf Mikrofilm in einem einzigen Archiv erhalten sind. Bibliotheken können Finanzkürzungen erleiden, Unternehmen können bankrottgehen, Archive können bombardiert und niedergebrannt werden. Das ist nicht hypothetisch – das passiert ständig."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Das Einzigartige, was wir bei Annas Archiv tun können, ist, viele Kopien von Werken in großem Maßstab zu speichern. Wir können wissenschaftliche Aufsätze, Bücher, Zeitschriften und mehr sammeln und in großen Mengen verteilen. Derzeit tun wir dies über Torrents, aber die genauen Technologien sind nicht entscheidend und werden sich im Laufe der Zeit ändern. Der wichtige Teil ist, viele Kopien weltweit zu verteilen. Dieses Zitat von vor über 200 Jahren ist immer noch aktuell:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Das Verlorene kann nicht wiederhergestellt werden; aber lasst uns bewahren, was bleibt: nicht durch Tresore und Schlösser, die sie dem öffentlichen Auge und Gebrauch entziehen und sie dem Zahn der Zeit überlassen, sondern durch eine solche Vervielfältigung von Kopien, dass sie außerhalb der Reichweite von Unfällen sind.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Ein kurzer Hinweis zum öffentlichen Bereich. Da sich Annas Archiv einzigartig auf Aktivitäten konzentriert, die in vielen Teilen der Welt illegal sind, kümmern wir uns nicht um weit verbreitete Sammlungen, wie Bücher im öffentlichen Bereich. Rechtliche Einrichtungen kümmern sich oft bereits gut darum. Es gibt jedoch Überlegungen, die uns manchmal dazu bringen, an öffentlich zugänglichen Sammlungen zu arbeiten:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadatensätze können auf der Worldcat-Website frei eingesehen, aber nicht in großen Mengen heruntergeladen werden (bis wir sie <a %(worldcat_scrape)s>gescrapt</a> haben)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Code kann auf Github Open Source sein, aber Github als Ganzes kann nicht leicht gespiegelt und somit erhalten werden (obwohl es in diesem speziellen Fall ausreichend verteilte Kopien der meisten Code-Repositories gibt)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit ist kostenlos nutzbar, hat aber kürzlich strenge Anti-Scraping-Maßnahmen ergriffen, im Zuge der datenhungrigen LLM-Trainings (mehr dazu später)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Eine Vervielfältigung von Kopien"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Zurück zu unserer ursprünglichen Frage: Wie können wir behaupten, unsere Sammlungen auf Dauer zu bewahren? Das Hauptproblem hier ist, dass unsere Sammlung <a %(torrents_stats)s>schnell wächst</a>, indem wir einige massive Sammlungen scrapen und als Open Source bereitstellen (zusätzlich zu der großartigen Arbeit, die bereits von anderen Open-Data-Schattenbibliotheken wie Sci-Hub und Library Genesis geleistet wurde)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Dieses Datenwachstum erschwert es, die Sammlungen weltweit zu spiegeln. Datenspeicherung ist teuer! Aber wir sind optimistisch, besonders wenn wir die folgenden drei Trends beobachten."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Wir haben die leicht zugänglichen Früchte gepflückt"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dies folgt direkt aus unseren oben diskutierten Prioritäten. Wir ziehen es vor, zuerst an der Befreiung großer Sammlungen zu arbeiten. Jetzt, da wir einige der größten Sammlungen der Welt gesichert haben, erwarten wir, dass unser Wachstum viel langsamer sein wird."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Es gibt immer noch einen langen Schwanz kleinerer Sammlungen, und jeden Tag werden neue Bücher gescannt oder veröffentlicht, aber die Rate wird wahrscheinlich viel langsamer sein. Wir könnten uns immer noch verdoppeln oder sogar verdreifachen, aber über einen längeren Zeitraum."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Die Speicherkosten sinken weiterhin exponentiell"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Zum Zeitpunkt des Schreibens liegen die <a %(diskprices)s>Festplattenpreise</a> pro TB bei etwa 12 $ für neue Festplatten, 8 $ für gebrauchte Festplatten und 4 $ für Bänder. Wenn wir konservativ sind und nur neue Festplatten betrachten, bedeutet das, dass die Speicherung eines Petabytes etwa 12.000 $ kostet. Wenn wir annehmen, dass unsere Bibliothek von 900 TB auf 2,7 PB anwächst, würde das bedeuten, dass es 32.400 $ kostet, unsere gesamte Bibliothek zu spiegeln. Unter Berücksichtigung von Strom, Kosten für andere Hardware und so weiter, runden wir es auf 40.000 $ auf. Oder mit Bändern eher 15.000–20.000 $."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Einerseits sind <strong>15.000–40.000 $ für die Summe allen menschlichen Wissens ein Schnäppchen</strong>. Andererseits ist es etwas hoch, zu erwarten, dass viele vollständige Kopien existieren, besonders wenn wir auch möchten, dass diese Personen ihre Torrents weiterhin für andere bereitstellen."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Das ist heute. Aber der Fortschritt schreitet voran:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Die Kosten für Festplatten pro TB wurden in den letzten 10 Jahren ungefähr um ein Drittel gesenkt und werden wahrscheinlich in einem ähnlichen Tempo weiter sinken. Bänder scheinen sich auf einem ähnlichen Weg zu befinden. SSD-Preise sinken noch schneller und könnten bis zum Ende des Jahrzehnts die HDD-Preise überholen."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-Preistrends aus verschiedenen Quellen (zum Ansehen der Studie klicken)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Wenn dies zutrifft, könnten wir in 10 Jahren nur noch 5.000–13.000 $ benötigen, um unsere gesamte Sammlung zu spiegeln (1/3), oder sogar weniger, wenn wir weniger wachsen. Während es immer noch viel Geld ist, wird dies für viele Menschen erreichbar sein. Und es könnte noch besser werden wegen des nächsten Punktes…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Verbesserungen in der Informationsdichte"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Derzeit speichern wir Bücher in den Rohformaten, in denen sie uns vorliegen. Sicher, sie sind komprimiert, aber oft sind es immer noch große Scans oder Fotografien von Seiten."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Bisher waren die einzigen Möglichkeiten, die Gesamtgröße unserer Sammlung zu verkleinern, eine aggressivere Komprimierung oder Deduplizierung. Um jedoch signifikante Einsparungen zu erzielen, sind beide für unseren Geschmack zu verlustbehaftet. Eine starke Komprimierung von Fotos kann den Text kaum lesbar machen. Und Deduplizierung erfordert ein hohes Maß an Sicherheit, dass Bücher genau gleich sind, was oft zu ungenau ist, insbesondere wenn der Inhalt derselbe ist, die Scans jedoch zu unterschiedlichen Anlässen gemacht wurden."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Es gab immer eine dritte Option, aber ihre Qualität war so miserabel, dass wir sie nie in Betracht gezogen haben: <strong>OCR oder optische Zeichenerkennung</strong>. Dies ist der Prozess der Umwandlung von Fotos in reinen Text, indem KI verwendet wird, um die Zeichen in den Fotos zu erkennen. Werkzeuge dafür existieren schon lange und sind ziemlich anständig, aber „ziemlich anständig“ reicht für Erhaltungszwecke nicht aus."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Allerdings haben jüngste multimodale Deep-Learning-Modelle extrem schnelle Fortschritte gemacht, wenn auch noch zu hohen Kosten. Wir erwarten, dass sich sowohl die Genauigkeit als auch die Kosten in den kommenden Jahren dramatisch verbessern werden, bis zu dem Punkt, an dem es realistisch wird, sie auf unsere gesamte Bibliothek anzuwenden."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Verbesserungen bei der OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Wenn das passiert, werden wir wahrscheinlich immer noch die Originaldateien aufbewahren, aber zusätzlich könnten wir eine viel kleinere Version unserer Bibliothek haben, die die meisten Menschen spiegeln möchten. Der Clou ist, dass sich reiner Text selbst noch besser komprimieren lässt und viel einfacher zu deduplizieren ist, was uns noch mehr Einsparungen bringt."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Insgesamt ist es nicht unrealistisch, eine Reduzierung der Gesamtdateigröße um mindestens das 5- bis 10-fache zu erwarten, vielleicht sogar mehr. Selbst bei einer konservativen Reduzierung um das 5-fache würden wir in 10 Jahren mit <strong>1.000–3.000 $ rechnen, selbst wenn sich unsere Bibliothek verdreifacht</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritisches Zeitfenster"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Wenn diese Prognosen zutreffen, müssen wir <strong>nur ein paar Jahre warten</strong>, bevor unsere gesamte Sammlung weit verbreitet gespiegelt wird. So wird sie, in den Worten von Thomas Jefferson, „außerhalb der Reichweite von Unfällen“ platziert."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Leider hat das Aufkommen von LLMs und deren datenhungrigem Training viele Urheberrechtsinhaber in die Defensive gedrängt. Noch mehr als sie es ohnehin schon waren. Viele Websites machen es schwieriger, Daten zu scrapen und zu archivieren, Klagen fliegen umher, und währenddessen werden physische Bibliotheken und Archive weiterhin vernachlässigt."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Wir können nur erwarten, dass sich diese Trends weiter verschlechtern und viele Werke verloren gehen, lange bevor sie gemeinfrei werden."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Wir stehen am Vorabend einer Revolution in der Erhaltung, aber <q>das Verlorene kann nicht wiederhergestellt werden.</q></strong> Wir haben ein kritisches Zeitfenster von etwa 5-10 Jahren, in dem es noch ziemlich teuer ist, eine Schattenbibliothek zu betreiben und viele Spiegel auf der ganzen Welt zu erstellen, und in dem der Zugang noch nicht vollständig abgeschaltet wurde."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Wenn wir dieses Zeitfenster überbrücken können, dann werden wir tatsächlich das Wissen und die Kultur der Menschheit für die Ewigkeit bewahrt haben. Wir sollten diese Zeit nicht ungenutzt verstreichen lassen. Wir sollten nicht zulassen, dass sich dieses kritische Zeitfenster schließt."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Lassen Sie uns loslegen."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Exklusiver Zugang für LLM-Unternehmen zur größten chinesischen Sachbuchsammlung der Welt"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Chinesische Version 中文版</a>, <a %(news_ycombinator)s>Diskutieren Sie auf Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Annas Archiv hat eine einzigartige Sammlung von 7,5 Millionen / 350 TB chinesischer Sachbücher erworben – größer als Library Genesis. Wir sind bereit, einem LLM-Unternehmen exklusiven Zugang zu gewähren, im Austausch für hochwertige OCR und Textextraktion.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dies ist ein kurzer Blogbeitrag. Wir suchen ein Unternehmen oder eine Institution, die uns bei der OCR und Textextraktion für eine massive Sammlung, die wir erworben haben, unterstützt, im Austausch für exklusiven frühen Zugang. Nach der Embargoperiode werden wir natürlich die gesamte Sammlung freigeben."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Hochwertige akademische Texte sind äußerst nützlich für das Training von LLMs. Obwohl unsere Sammlung chinesisch ist, sollte sie auch für das Training englischer LLMs nützlich sein: Modelle scheinen Konzepte und Wissen unabhängig von der Quellsprache zu kodieren."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Dafür müssen Texte aus den Scans extrahiert werden. Was hat Annas Archiv davon? Volltextsuche der Bücher für seine Nutzer."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Da unsere Ziele mit denen der LLM-Entwickler übereinstimmen, suchen wir einen Kollaborator. Wir sind bereit, Ihnen <strong>exklusiven frühen Zugang zu dieser Sammlung in großen Mengen für 1 Jahr</strong> zu gewähren, wenn Sie eine ordnungsgemäße OCR und Textextraktion durchführen können. Wenn Sie bereit sind, uns den gesamten Code Ihrer Pipeline zu teilen, wären wir bereit, die Sammlung länger zu sperren."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Beispielseiten"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Um uns zu beweisen, dass Sie eine gute Pipeline haben, hier einige Beispielseiten zum Einstieg, aus einem Buch über Supraleiter. Ihre Pipeline sollte Mathematik, Tabellen, Diagramme, Fußnoten usw. korrekt verarbeiten."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Senden Sie Ihre bearbeiteten Seiten an unsere E-Mail. Wenn sie gut aussehen, senden wir Ihnen privat mehr, und wir erwarten, dass Sie Ihre Pipeline auch schnell darauf ausführen können. Sobald wir zufrieden sind, können wir einen Deal machen."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Sammlung"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Einige weitere Informationen über die Sammlung. <a %(duxiu)s>Duxiu</a> ist eine riesige Datenbank gescannter Bücher, erstellt von der <a %(chaoxing)s>SuperStar Digital Library Group</a>. Die meisten sind akademische Bücher, die gescannt wurden, um sie Universitäten und Bibliotheken digital zugänglich zu machen. Für unser englischsprachiges Publikum haben <a %(library_princeton)s>Princeton</a> und die <a %(guides_lib_uw)s>University of Washington</a> gute Übersichten. Es gibt auch einen ausgezeichneten Artikel, der mehr Hintergrund bietet: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine“</a> (suchen Sie ihn in Annas Archiv)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Die Bücher von Duxiu wurden lange Zeit im chinesischen Internet piratiert. Normalerweise werden sie von Wiederverkäufern für weniger als einen Dollar verkauft. Sie werden typischerweise mit dem chinesischen Äquivalent von Google Drive verteilt, das oft gehackt wurde, um mehr Speicherplatz zu ermöglichen. Einige technische Details finden Sie <a %(github_duty_machine)s>hier</a> und <a %(github_821_github_io)s>hier</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Obwohl die Bücher halböffentlich verteilt wurden, ist es ziemlich schwierig, sie in großen Mengen zu erhalten. Wir hatten dies hoch auf unserer TODO-Liste und mehrere Monate Vollzeitarbeit dafür eingeplant. Doch kürzlich hat sich ein unglaublicher, erstaunlicher und talentierter Freiwilliger an uns gewandt und uns mitgeteilt, dass er all diese Arbeit bereits erledigt hat — mit großem Aufwand. Sie teilten die gesamte Sammlung mit uns, ohne etwas im Gegenzug zu erwarten, außer der Garantie für eine langfristige Erhaltung. Wirklich bemerkenswert. Sie stimmten zu, auf diese Weise um Hilfe zu bitten, um die Sammlung OCR-fähig zu machen."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Die Sammlung umfasst 7.543.702 Dateien. Das ist mehr als Library Genesis Sachbücher (etwa 5,3 Millionen). Die Gesamtgröße der Dateien beträgt etwa 359TB (326TiB) in ihrer aktuellen Form."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Wir sind offen für andere Vorschläge und Ideen. Kontaktieren Sie uns einfach. Schauen Sie sich Annas Archiv an, um mehr Informationen über unsere Sammlungen, Erhaltungsbemühungen und wie Sie helfen können, zu erhalten. Danke!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Warnung: Dieser Blogbeitrag ist veraltet. Wir haben entschieden, dass IPFS noch nicht bereit für den Einsatz ist. Wir werden weiterhin auf Dateien auf IPFS von Annas Archiv verlinken, wenn möglich, aber wir werden es nicht mehr selbst hosten, noch empfehlen wir anderen, mit IPFS zu spiegeln. Bitte sehen Sie sich unsere Torrents-Seite an, wenn Sie helfen möchten, unsere Sammlung zu erhalten."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Helfen Sie, Z-Library auf IPFS zu seeden"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Wie man eine Schattenbibliothek betreibt: Betrieb bei Annas Archiv"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Es gibt kein <q>AWS für Schatten-Wohltätigkeitsorganisationen,</q> also wie betreiben wir Annas Archiv?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ich betreibe <a %(wikipedia_annas_archive)s>Annas Archiv</a>, die weltweit größte Open-Source-Gemeinnützige Suchmaschine für <a %(wikipedia_shadow_library)s>Schattenbibliotheken</a> wie Sci-Hub, Library Genesis und Z-Library. Unser Ziel ist es, Wissen und Kultur leicht zugänglich zu machen und letztendlich eine Gemeinschaft von Menschen aufzubauen, die zusammen <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle Bücher der Welt</a> archivieren und bewahren."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In diesem Artikel zeige ich, wie wir diese Website betreiben und die einzigartigen Herausforderungen, die mit dem Betrieb einer Website mit fragwürdigem rechtlichen Status einhergehen, da es kein „AWS für Schatten-Wohltätigkeitsorganisationen“ gibt."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Sehen Sie sich auch den Schwesterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Wie man ein Piratenarchivar wird</a> an.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovationstoken"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Beginnen wir mit unserem Tech-Stack. Er ist absichtlich langweilig. Wir verwenden Flask, MariaDB und ElasticSearch. Das war's im Grunde. Die Suche ist weitgehend ein gelöstes Problem, und wir haben nicht vor, es neu zu erfinden. Außerdem müssen wir unsere <a %(mcfunley)s>Innovationstoken</a> für etwas anderes ausgeben: nicht von den Behörden abgeschaltet zu werden."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Wie legal oder illegal ist Annas Archiv genau? Das hängt größtenteils von der rechtlichen Zuständigkeit ab. Die meisten Länder glauben an irgendeine Form von Urheberrecht, was bedeutet, dass Personen oder Unternehmen ein exklusives Monopol auf bestimmte Arten von Werken für einen bestimmten Zeitraum zugewiesen wird. Nebenbei bemerkt, glauben wir bei Annas Archiv, dass es zwar einige Vorteile gibt, das Urheberrecht insgesamt jedoch ein Netto-Nachteil für die Gesellschaft ist – aber das ist eine Geschichte für ein anderes Mal."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dieses exklusive Monopol auf bestimmte Werke bedeutet, dass es illegal ist, dass jemand außerhalb dieses Monopols diese Werke direkt verbreitet – einschließlich uns. Aber Annas Archiv ist eine Suchmaschine, die diese Werke nicht direkt verbreitet (zumindest nicht auf unserer Clearnet-Website), also sollten wir in Ordnung sein, oder? Nicht ganz. In vielen Rechtsordnungen ist es nicht nur illegal, urheberrechtlich geschützte Werke zu verbreiten, sondern auch, auf Orte zu verlinken, die dies tun. Ein klassisches Beispiel dafür ist das DMCA-Gesetz der Vereinigten Staaten."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Das ist das strengste Ende des Spektrums. Am anderen Ende des Spektrums könnte es theoretisch Länder ohne jegliche Urheberrechtsgesetze geben, aber diese existieren in Wirklichkeit nicht. So ziemlich jedes Land hat irgendeine Form von Urheberrechtsgesetz. Die Durchsetzung ist eine andere Geschichte. Es gibt viele Länder mit Regierungen, die sich nicht darum kümmern, das Urheberrecht durchzusetzen. Es gibt auch Länder zwischen den beiden Extremen, die die Verbreitung urheberrechtlich geschützter Werke verbieten, aber das Verlinken zu solchen Werken nicht verbieten."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Ein weiterer Aspekt ist die Unternehmensebene. Wenn ein Unternehmen in einer Rechtsordnung tätig ist, die sich nicht um das Urheberrecht kümmert, das Unternehmen selbst jedoch kein Risiko eingehen möchte, könnte es Ihre Website schließen, sobald sich jemand darüber beschwert."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Schließlich ist ein großer Aspekt die Zahlungen. Da wir anonym bleiben müssen, können wir keine traditionellen Zahlungsmethoden verwenden. Das lässt uns mit Kryptowährungen, und nur eine kleine Anzahl von Unternehmen unterstützt diese (es gibt virtuelle Debitkarten, die mit Krypto bezahlt werden, aber sie werden oft nicht akzeptiert)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systemarchitektur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Angenommen, Sie haben einige Unternehmen gefunden, die bereit sind, Ihre Website zu hosten, ohne Sie abzuschalten – nennen wir sie „freiheitsliebende Anbieter“ 😄. Sie werden schnell feststellen, dass das Hosting von allem bei ihnen ziemlich teuer ist, also möchten Sie vielleicht einige „günstige Anbieter“ finden und das eigentliche Hosting dort durchführen, indem Sie über die freiheitsliebenden Anbieter proxyen. Wenn Sie es richtig machen, werden die günstigen Anbieter nie wissen, was Sie hosten, und nie Beschwerden erhalten."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Bei all diesen Anbietern besteht das Risiko, dass sie Sie trotzdem abschalten, daher benötigen Sie auch Redundanz. Wir brauchen dies auf allen Ebenen unseres Stacks."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Ein etwas freiheitsliebendes Unternehmen, das sich in eine interessante Position gebracht hat, ist Cloudflare. Sie haben <a %(blog_cloudflare)s>argumentiert</a>, dass sie kein Hosting-Anbieter, sondern ein Versorgungsunternehmen wie ein ISP sind. Sie unterliegen daher nicht den DMCA- oder anderen Abschaltanfragen und leiten alle Anfragen an Ihren tatsächlichen Hosting-Anbieter weiter. Sie sind sogar so weit gegangen, vor Gericht zu gehen, um diese Struktur zu schützen. Wir können sie daher als eine weitere Schicht von Caching und Schutz verwenden."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare akzeptiert keine anonymen Zahlungen, daher können wir nur ihren kostenlosen Plan nutzen. Das bedeutet, dass wir ihre Load-Balancing- oder Failover-Funktionen nicht nutzen können. Wir haben dies daher <a %(annas_archive_l255)s>selbst auf Domain-Ebene implementiert</a>. Beim Laden der Seite überprüft der Browser, ob die aktuelle Domain noch verfügbar ist, und wenn nicht, werden alle URLs auf eine andere Domain umgeschrieben. Da Cloudflare viele Seiten zwischenspeichert, bedeutet dies, dass ein Benutzer auf unserer Hauptdomain landen kann, selbst wenn der Proxy-Server ausgefallen ist, und dann beim nächsten Klick auf eine andere Domain verschoben wird."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Wir haben auch weiterhin normale betriebliche Anliegen zu bewältigen, wie die Überwachung der Servergesundheit, das Protokollieren von Backend- und Frontend-Fehlern und so weiter. Unsere Failover-Architektur ermöglicht auch mehr Robustheit in diesem Bereich, zum Beispiel durch den Betrieb eines völlig anderen Satzes von Servern auf einer der Domains. Wir können sogar ältere Versionen des Codes und der Datasets auf dieser separaten Domain ausführen, falls ein kritischer Fehler in der Hauptversion unbemerkt bleibt."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Wir können uns auch gegen eine mögliche Abkehr von Cloudflare absichern, indem wir es von einer der Domains entfernen, wie zum Beispiel dieser separaten Domain. Verschiedene Permutationen dieser Ideen sind möglich."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Werkzeuge"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Schauen wir uns an, welche Werkzeuge wir verwenden, um all dies zu erreichen. Dies entwickelt sich sehr stark weiter, da wir auf neue Probleme stoßen und neue Lösungen finden."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Anwendungsserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy-Server: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serververwaltung: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Entwicklung: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion-Hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Es gibt einige Entscheidungen, bei denen wir hin und her überlegt haben. Eine davon ist die Kommunikation zwischen den Servern: Früher haben wir dafür Wireguard verwendet, aber festgestellt, dass es gelegentlich aufhört, Daten zu übertragen, oder nur in eine Richtung Daten überträgt. Dies geschah bei mehreren verschiedenen Wireguard-Konfigurationen, die wir ausprobiert haben, wie <a %(github_costela_wesher)s>wesher</a> und <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Wir haben auch versucht, Ports über SSH zu tunneln, indem wir autossh und sshuttle verwendet haben, sind dabei jedoch auf <a %(github_sshuttle)s>Probleme gestoßen</a> (obwohl mir immer noch nicht klar ist, ob autossh unter TCP-over-TCP-Problemen leidet oder nicht – es fühlt sich für mich einfach wie eine wackelige Lösung an, aber vielleicht ist es tatsächlich in Ordnung?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Stattdessen sind wir zu direkten Verbindungen zwischen den Servern zurückgekehrt und haben dabei die Tatsache verborgen, dass ein Server bei günstigen Anbietern läuft, indem wir IP-Filterung mit UFW verwenden. Der Nachteil dabei ist, dass Docker nicht gut mit UFW funktioniert, es sei denn, Sie verwenden <code>network_mode: \"host\"</code>. All dies ist etwas fehleranfälliger, da Sie Ihren Server mit nur einer kleinen Fehlkonfiguration dem Internet aussetzen. Vielleicht sollten wir zu autossh zurückkehren – Feedback wäre hier sehr willkommen."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Wir haben auch zwischen Varnish und Nginx hin und her überlegt. Derzeit mögen wir Varnish, aber es hat seine Eigenheiten und Ecken. Dasselbe gilt für Checkmk: Wir lieben es nicht, aber es funktioniert vorerst. Weblate war in Ordnung, aber nicht unglaublich – ich habe manchmal Angst, dass es meine Daten verliert, wann immer ich versuche, es mit unserem Git-Repo zu synchronisieren. Flask war insgesamt gut, aber es hat einige seltsame Eigenheiten, die viel Zeit zum Debuggen gekostet haben, wie das Konfigurieren benutzerdefinierter Domains oder Probleme mit seiner SqlAlchemy-Integration."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Bisher waren die anderen Tools großartig: Wir haben keine ernsthaften Beschwerden über MariaDB, ElasticSearch, Gitlab, Zulip, Docker und Tor. Alle diese hatten einige Probleme, aber nichts allzu Ernstes oder Zeitaufwändiges."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Fazit"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Es war eine interessante Erfahrung, zu lernen, wie man eine robuste und widerstandsfähige Suchmaschine für eine Schattenbibliothek einrichtet. Es gibt noch viele weitere Details, die in späteren Beiträgen geteilt werden können, also lassen Sie mich wissen, worüber Sie mehr erfahren möchten!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Wie immer suchen wir nach Spenden, um diese Arbeit zu unterstützen, also schauen Sie sich unbedingt die Spendenseite auf Annas Archiv an. Wir suchen auch nach anderen Arten von Unterstützung, wie Stipendien, langfristige Sponsoren, Hochrisiko-Zahlungsanbieter, vielleicht sogar (geschmackvolle!) Anzeigen. Und wenn Sie Ihre Zeit und Fähigkeiten einbringen möchten, suchen wir immer nach Entwicklern, Übersetzern und so weiter. Vielen Dank für Ihr Interesse und Ihre Unterstützung."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hallo, ich bin Anna. Ich habe <a %(wikipedia_annas_archive)s>Annas Archiv</a> erstellt, die größte Schattenbibliothek der Welt. Dies ist mein persönlicher Blog, in dem ich und mein Team über Piraterie, digitale Bewahrung und mehr schreiben."

#, fuzzy
msgid "blog.index.text2"
msgstr "Verbinden Sie sich mit mir auf <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Beachten Sie, dass diese Website nur ein Blog ist. Wir hosten hier nur unsere eigenen Worte. Keine Torrents oder andere urheberrechtlich geschützte Dateien werden hier gehostet oder verlinkt."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogbeiträge"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 Milliarden WorldCat-Scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5.998.794 Bücher auf IPFS stellen"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Warnung: Dieser Blogbeitrag ist veraltet. Wir haben entschieden, dass IPFS noch nicht bereit für den Einsatz ist. Wir werden weiterhin auf Dateien auf IPFS von Annas Archiv verlinken, wenn möglich, aber wir werden es nicht mehr selbst hosten, noch empfehlen wir anderen, mit IPFS zu spiegeln. Bitte sehen Sie sich unsere Torrents-Seite an, wenn Sie helfen möchten, unsere Sammlung zu erhalten."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Annas Archiv hat den gesamten WorldCat (die größte Bibliothek-Metadatensammlung der Welt) gescrapt, um eine TODO-Liste von Büchern zu erstellen, die bewahrt werden müssen.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Vor einem Jahr haben wir uns <a %(blog)s>auf den Weg gemacht</a>, um diese Frage zu beantworten: <strong>Welcher Prozentsatz der Bücher wurde dauerhaft von Schattenbibliotheken bewahrt?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Sobald ein Buch in eine Open-Data-Schattenbibliothek wie <a %(wikipedia_library_genesis)s>Library Genesis</a> und jetzt <a %(wikipedia_annas_archive)s>Annas Archiv</a> gelangt, wird es weltweit gespiegelt (durch Torrents) und damit praktisch für immer bewahrt."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Um die Frage zu beantworten, welcher Prozentsatz der Bücher bewahrt wurde, müssen wir den Nenner kennen: Wie viele Bücher gibt es insgesamt? Und idealerweise haben wir nicht nur eine Zahl, sondern tatsächliche Metadaten. Dann können wir sie nicht nur mit Schattenbibliotheken abgleichen, sondern auch <strong>eine TODO-Liste der verbleibenden Bücher erstellen, die bewahrt werden müssen!</strong> Wir könnten sogar davon träumen, eine Crowdsourcing-Initiative zu starten, um diese TODO-Liste abzuarbeiten."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Wir haben <a %(wikipedia_isbndb_com)s>ISBNdb</a> gescraped und den <a %(openlibrary)s>Open Library-Datensatz</a> heruntergeladen, aber die Ergebnisse waren unbefriedigend. Das Hauptproblem war, dass es nicht viele Überschneidungen der ISBNs gab. Sehen Sie sich dieses Venn-Diagramm aus <a %(blog)s>unserem Blogbeitrag</a> an:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Wir waren sehr überrascht, wie wenig Überschneidungen es zwischen ISBNdb und Open Library gab, obwohl beide großzügig Daten aus verschiedenen Quellen, wie Web-Scrapes und Bibliotheksaufzeichnungen, einbeziehen. Wenn beide gute Arbeit leisten, die meisten ISBNs da draußen zu finden, würden ihre Kreise sicherlich erhebliche Überschneidungen aufweisen, oder einer wäre eine Teilmenge des anderen. Es ließ uns darüber nachdenken, wie viele Bücher <em>vollständig außerhalb dieser Kreise</em> fallen? Wir brauchen eine größere Datenbank."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Da richteten wir unseren Blick auf die größte Buchdatenbank der Welt: <a %(wikipedia_worldcat)s>WorldCat</a>. Dies ist eine proprietäre Datenbank der gemeinnützigen <a %(wikipedia_oclc)s>OCLC</a>, die Metadatenaufzeichnungen von Bibliotheken aus der ganzen Welt aggregiert, im Austausch dafür, dass diese Bibliotheken Zugang zum vollständigen Datensatz erhalten und in den Suchergebnissen der Endnutzer angezeigt werden."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Obwohl OCLC eine gemeinnützige Organisation ist, erfordert ihr Geschäftsmodell den Schutz ihrer Datenbank. Nun, es tut uns leid, Freunde bei OCLC, wir geben alles preis. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Im vergangenen Jahr haben wir akribisch alle WorldCat-Aufzeichnungen gescraped. Zuerst hatten wir einen Glücksfall. WorldCat führte gerade ihr komplettes Website-Redesign ein (im August 2022). Dies beinhaltete eine umfassende Überarbeitung ihrer Backend-Systeme, die viele Sicherheitslücken einführte. Wir nutzten sofort die Gelegenheit und konnten in nur wenigen Tagen Hunderte von Millionen (!) von Aufzeichnungen scrapen."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat-Redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Danach wurden die Sicherheitslücken nach und nach einzeln behoben, bis die letzte, die wir gefunden hatten, vor etwa einem Monat geschlossen wurde. Zu diesem Zeitpunkt hatten wir so gut wie alle Aufzeichnungen und strebten nur noch nach etwas höherwertigen Aufzeichnungen. Also fühlten wir, dass es Zeit ist, zu veröffentlichen!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Lassen Sie uns einige grundlegende Informationen zu den Daten ansehen:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Annas Archiv-Container (AAC)</a>, die im Wesentlichen <a %(jsonlines)s>JSON Lines</a> sind, komprimiert mit <a %(zstd)s>Zstandard</a>, plus einige standardisierte Semantiken. Diese Container umfassen verschiedene Arten von Aufzeichnungen, basierend auf den unterschiedlichen Scrapes, die wir eingesetzt haben."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Daten"

msgid "dyn.buy_membership.error.unknown"
msgstr "Ein unbekannter Fehler ist aufgetreten. Bitte kontaktiere uns per %(email)s mit einem Screenshot."

msgid "dyn.buy_membership.error.minimum"
msgstr "Diese Coin hat ein höheres Minimum als üblich. Bitte wähle eine andere Laufzeit oder eine andere Coin."

msgid "dyn.buy_membership.error.try_again"
msgstr "Die Anfrage konnte nicht abgeschlossen werden. Bitte versuche es in ein paar Minuten erneut und kontaktiere uns per %(email)s mit einem Screenshot, falls das Problem weiterhin auftritt."

msgid "dyn.buy_membership.error.wait"
msgstr "Fehler in der Zahlungsabwicklung. Bitte warten einen Moment und versuche es erneut. Wenn das Problem länger als 24 Stunden bestehen bleibt, kontaktiere uns bitte mit einem Screenshot per %(email)s."

msgid "page.comments.hidden_comment"
msgstr "versteckter Kommentar"

msgid "page.comments.file_issue"
msgstr "Dateifehler: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Bessere Version"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Möchtest du diesen Benutzer wegen missbräuchlichen oder unangemessenen Verhaltens melden?"

msgid "page.comments.report_abuse"
msgstr "Missbrauch melden"

msgid "page.comments.abuse_reported"
msgstr "Missbrauch gemeldet:"

msgid "page.comments.reported_abuse_this_user"
msgstr "Du hast diesen Benutzer wegen Missbrauchs gemeldet."

msgid "page.comments.reply_button"
msgstr "Antworten"

msgid "page.md5.quality.logged_out_login"
msgstr "Bitte <a %(a_login)s>melde dich an</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Du hast einen Kommentar hinterlassen. Es kann eine Minute dauern, bis er angezeigt wird."

msgid "page.md5.quality.comment_error"
msgstr "Etwas ist schiefgelaufen. Bitte lade die Seite neu und versuche es erneut."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s betroffene Seiten"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nicht sichtbar in Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nicht sichtbar in Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nicht sichtbar in Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Als beschädigt markiert in Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Fehlt in Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Als „Spam“ in Z-Library markiert"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Als „schlechte Datei“ in Z-Library markiert"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nicht alle Seiten konnten ins PDF Format konvertiert werden"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Fehler beim Ausführen von exiftool für diese Datei"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Buch (unbekannt)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Buch (Sachbuch)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Buch (Belletristik)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "wissenschaftliche Aufsätze"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Normen-Dokument"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Zeitschrift"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Comicbuch"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitur"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Hörbuch"

msgid "common.md5_content_type_mapping.other"
msgstr "Andere"

msgid "common.access_types_mapping.aa_download"
msgstr "Partnerserver Download"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Externer Download"

msgid "common.access_types_mapping.external_borrow"
msgstr "Extern Ausleihen"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Extern Ausleihen (Lesebehinderung)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Metadaten erkunden"

msgid "common.access_types_mapping.torrents_available"
msgstr "In den Torrents enthalten"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinesisch"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Uploads auf AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tschechische Metadaten"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Russische Staatsbibliothek"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Titel"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Herausgeber"

msgid "common.specific_search_fields.edition_varia"
msgstr "Auflage"

msgid "common.specific_search_fields.year"
msgstr "Publikationsjahr"

msgid "common.specific_search_fields.original_filename"
msgstr "Ursprünglicher Dateiname"

msgid "common.specific_search_fields.description_comments"
msgstr "Beschreibung und Kommentare in Metadaten"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads von Partnerservern sind derzeit nicht verfügbar für diese Datei."

msgid "common.md5.servers.fast_partner"
msgstr "Schneller Partnererver #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(empfohlen)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(keine Browser-Verifizierung oder Wartelisten)"

msgid "common.md5.servers.slow_partner"
msgstr "Langsamer Partnerserver #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(etwas schneller, aber mit Warteliste)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(keine Warteliste, kann aber sehr langsam sein)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(oben \"GET\" anklicken)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(oben \"GET\" anklicken)"

msgid "page.md5.box.download.libgen_ads"
msgstr "Es ist bekannt, dass ihre Anzeigen bösartige Software enthalten, also verwende einen Werbeblocker oder klicke auf keine Anzeigen"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC-Dateien können beim Herunterladen unzuverlässig sein)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library auf Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(benötigt den Tor-Browser)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "aus Internet Archive ausleihen"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(nur für Gäste mit Lesebehinderung)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(der zugehörige DOI ist möglicherweise nicht in Sci-Hub verfügbar)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "Sammlung"

msgid "page.md5.box.download.torrent"
msgstr "Torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Massen-Torrent-Downloads"

msgid "page.md5.box.download.experts_only"
msgstr "(nur für Experten)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Durchsuche Annas Archiv nach ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Durchsuche verschiedene andere Datenbanken nach ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Originaldatensatz in ISBNdb suchen"

msgid "page.md5.box.download.aa_openlib"
msgstr "Durchsuche Annas Archiv nach Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Den Originaldatensatz in der Open Library suchen"

msgid "page.md5.box.download.aa_oclc"
msgstr "Annas Archiv durchsuchen nach OCLC (WorldCat) Nummer"

msgid "page.md5.box.download.original_oclc"
msgstr "Finde der ursprünglich Datensatz in WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Annas Archiv nach DuXiu SSID-Nummer durchsuchen"

msgid "page.md5.box.download.original_duxiu"
msgstr "Manuell auf DuXiu suchen"

msgid "page.md5.box.download.aa_cadal"
msgstr "Annas Archiv nach CADAL SSNO-Nummer durchsuchen"

msgid "page.md5.box.download.original_cadal"
msgstr "Den Originaleintrag in CADAL suchen"

msgid "page.md5.box.download.aa_dxid"
msgstr "Annas Archiv nach DuXiu DXID-Nummer durchsuchen"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Annas Archiv 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(keine Browserüberprüfung erforderlich)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tschechische Metadaten %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metadaten"

msgid "page.md5.box.descr_title"
msgstr "Beschreibung"

msgid "page.md5.box.alternative_filename"
msgstr "Alternativer Dateiname"

msgid "page.md5.box.alternative_title"
msgstr "Alternativtitel"

msgid "page.md5.box.alternative_author"
msgstr "Alternativer Autor"

msgid "page.md5.box.alternative_publisher"
msgstr "Alternativer Verlag"

msgid "page.md5.box.alternative_edition"
msgstr "Alternative Ausgabe"

msgid "page.md5.box.alternative_extension"
msgstr "Alternative Dateierweiterung"

msgid "page.md5.box.metadata_comments_title"
msgstr "Kommentare in Metadaten"

msgid "page.md5.box.alternative_description"
msgstr "Alternative Beschreibung"

msgid "page.md5.box.date_open_sourced_title"
msgstr "frei veröffentlicht am"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub Datei “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled-Digital-Lending-Datei „%(id)s“"

msgid "page.md5.header.ia_desc"
msgstr "Hierbei handelt es sich um den Eintrag einer Datei aus dem Internet Archiv, nicht um eine direkt herunterladbare Datei. Du kannst versuchen, das Buch auszuleihen (Link unten) oder diese URL verwenden, wenn du <a %(a_request)s>eine Datei anfragst</a>."

msgid "page.md5.header.consider_upload"
msgstr "Falls du die Datei selbst besitzst und sie noch nicht in Annas Archiv verfügbar ist, ziehe in Betracht sie <a %(a_request)s>hochzuladen</a>."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s Metadatensatz"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s Metadatensatz"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) Nummer %(id)s Metadatensatz"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Duxiu SSID %(id)s Metadatensatz"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s Metadatensatz"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s Metadaten-Datensatz"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s Metadaten-Datensatz"

msgid "page.md5.header.meta_desc"
msgstr "Dies ist ein Metadatensatz, keine herunterladbare Datei. Du kannst diese URL verwenden, wenn du <a %(a_request)s>eine Datei anfragen willst</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Metadaten aus verknüpftem Datensatz"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Metadaten in der Open Library verbessern"

msgid "page.md5.warning.multiple_links"
msgstr "Warnung: Mehrere verknüpfte Datensätze:"

msgid "page.md5.header.improve_metadata"
msgstr "Metadaten Verbessern"

msgid "page.md5.text.report_quality"
msgstr "Dateiqualität melden"

msgid "page.search.results.download_time"
msgstr "Downloadzeit"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Webseite:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Durchsuche Annas Archiv nach “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Codeexplorer:"

msgid "page.md5.codes.code_search"
msgstr "“%(name)s” in Codeexplorer anzeigen"

msgid "page.md5.box.descr_read_more"
msgstr "Weiterlesen…"

msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Ausleihen (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Erkunde Metadaten (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Kommentare (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listen (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistiken (%(count)s)"

msgid "common.tech_details"
msgstr "Zeige technische Details (auf Englisch)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Diese Datei hat womöglich Probleme und wurde daher in unserer Quellbibliothek versteckt.</span> Dies passiert manchmal, weil der Inhaber der Rechte darum gebeten hat, weil wir eine bessere Alternative gefunden haben oder wegen eines Problems mit der Datei selbst. Es kann sein, dass der Download trotzdem funktioniert, wir empfehlen aber zuerst nach einer anderen Datei zu suchen. Mehr Infos:"

msgid "page.md5.box.download.better_file"
msgstr "Eine bessere Version dieser Datei ist möglicherweise unter %(link)s verfügbar"

msgid "page.md5.box.issues.text2"
msgstr "Falls du die Datei dennoch herunterladen möchtest, öffne die Datei bitte nur mit aktueller und vertrauenswürdiger Software."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Schnelle Downloads"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Werde <a %(a_membership)s>Mitglied</a>, um die langfristige Aufbewahrung von Büchern, Dokumenten und mehr zu unterstützen. Als Dank für deine Unterstützung erhältst du schnellere Downloads. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Wenn du diesen Monat spendest, erhältst du die <strong>doppelte</strong> Anzahl an schnellen Downloads."

msgid "page.md5.box.download.header_fast_member"
msgstr "Du hast heute noch %(remaining)s übrig. Danke, dass du Mitglied bist! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Schnelle Downloads sind für heute aufgebraucht."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Du hast diese Datei kürzlich heruntergeladen. Die Links bleiben eine Zeit lang gültig."

msgid "page.md5.box.download.option"
msgstr "Option #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(keine Weiterleitung)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(im Viewer öffnen)"

msgid "layout.index.header.banner.refer"
msgstr "Lade einen Freund zu uns ein und ihr beide bekommt %(percentage)s%% schnelle Bonusdownloads!"

msgid "layout.index.header.learn_more"
msgstr "Mehr Erfahren…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Langsame Downloads"

msgid "page.md5.box.download.trusted_partners"
msgstr "Von vertrauenswürdigen Partnern."

msgid "page.md5.box.download.slow_faq"
msgstr "Mehr Infos dazu bei den <a %(a_slow)s>FAQs</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kann <a %(a_browser)s>Browser-Verifizierung</a> erfordern - unbegrenzte Downloads!)"

msgid "page.md5.box.download.after_downloading"
msgstr "Nach dem Herunterladen:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "In unserem Viewer öffnen"

msgid "page.md5.box.external_downloads"
msgstr "externe Downloads anzeigen"

msgid "page.md5.box.download.header_external"
msgstr "Externe Downloads"

msgid "page.md5.box.download.no_found"
msgstr "Keine Downloads gefunden."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Alle Mirrors verwenden dieselbe Datei und sollten daher sicher sein. Sei bitte trotzdem immer vorsichtig, wenn du Dateien aus dem Internet herunterlädst, insbesondere von Seiten abseits von Annas Archiv. Achte auch darauf, dass deine Geräte und Software auf dem neuesten Stand sind."

msgid "page.md5.box.download.dl_managers"
msgstr "Für große Dateien empfehlen wir die Verwendung eines Download-Managers, um Unterbrechungen zu vermeiden."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Empfohlene Download-Manager: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Du benötigst einen E-Book- oder PDF-Reader, um die Datei zu öffnen, je nach Dateiformat."

msgid "page.md5.box.download.readers.links"
msgstr "Empfohlene E-Book-Reader: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Annas Archiv Online-Viewer"

msgid "page.md5.box.download.conversion"
msgstr "Verwende Online-Tools, um zwischen Formaten zu konvertieren."

msgid "page.md5.box.download.conversion.links"
msgstr "Empfohlene Konvertierungstools: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Du kannst sowohl PDF- als auch EPUB-Dateien an deinen Kindle oder Kobo eReader senden."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Empfohlene Tools: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazons „Send to Kindle“"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazzs „Send to Kobo/Kindle“"

msgid "page.md5.box.download.support"
msgstr "Unterstütze Autoren und Bibliotheken"

msgid "page.md5.box.download.support.authors"
msgstr "Wenn dir das Werk gefällt und du es dir leisten kannst, dann ziehe in Betracht, das Original zu kaufen oder die Autoren direkt zu unterstützen."

msgid "page.md5.box.download.support.libraries"
msgstr "Wenn es in deiner örtlichen Bibliothek verfügbar ist, ziehe in Betracht, es dort kostenlos auszuleihen."

msgid "page.md5.quality.header"
msgstr "Dateiqualität"

msgid "page.md5.quality.report"
msgstr "Hilf der Community, indem du die Qualität dieser Datei meldest! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Dateiproblem melden (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Großartige Dateiqualität (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Kommentar hinzufügen (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Was ist mit dieser Datei nicht in Ordnung?"

msgid "page.md5.quality.copyright"
msgstr "Bitte verwenden das <a %(a_copyright)s>DMCA / Copyright-Anspruchsformular</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Beschreibe das Problem (erforderlich)"

msgid "page.md5.quality.issue_description"
msgstr "Problembeschreibung"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 einer besseren Version dieser Datei (falls zutreffend)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Füllen dies aus, wenn es eine andere Datei gibt, die dieser Datei sehr ähnlich ist (gleiche Ausgabe, gleiche Dateierweiterung, wenn du eine finden kannst), die anstelle dieser Datei verwendet werden sollte. Wenn du eine bessere Version dieser Datei außerhalb von Annas Archiv kennst, dann <a %(a_upload)s>lade sie bitte hoch</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Du kannst die md5 aus der URL auslesen, z.B."

msgid "page.md5.quality.submit_report"
msgstr "Bericht einreichen"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Erfahre, wie du die <a %(a_metadata)s>Metadaten für diese Datei selbst verbessern</a> kannst."

msgid "page.md5.quality.report_thanks"
msgstr "Vielen Dank für das Einreichen deines Berichts. Er wird auf dieser Seite angezeigt und manuell von Anna überprüft (bis wir ein richtiges Moderationssystem haben)."

msgid "page.md5.quality.report_error"
msgstr "Etwas ist schiefgelaufen. Bitte lade die Seite neu und versuche es erneut."

msgid "page.md5.quality.great.summary"
msgstr "Wenn diese Datei von hoher Qualität ist, kannst du hier darüber diskutieren! Wenn nicht, verwende bitte die Schaltfläche „Dateiproblem melden“."

msgid "page.md5.quality.loved_the_book"
msgstr "Ich habe dieses Buch geliebt!"

msgid "page.md5.quality.submit_comment"
msgstr "Kommentar hinterlassen"

msgid "common.english_only"
msgstr "Der folgende Text ist nur auf Englisch verfügbar."

msgid "page.md5.text.stats.total_downloads"
msgstr "Gesamte Downloads: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Ein „MD5“ ist ein Hash, der aus den Dateiinhalten berechnet wird und basierend auf diesen Inhalten einigermaßen einzigartig ist. Alle hier indexierten Schattenbibliotheken verwenden hauptsächlich MD5s zur Identifizierung von Dateien."

msgid "page.md5.text.md5_info.text2"
msgstr "Eine Datei kann in mehreren Schattenbibliotheken erscheinen. Für Informationen über die verschiedenen Datensätze, die wir zusammengestellt haben, siehe die <a %(a_datasets)s>Datensätze-Seite</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Dies ist eine Datei, die von der <a %(a_ia)s>IA’s Controlled Digital Lending</a> Bibliothek verwaltet und von Annas Archiv zur Suche indexiert wird. Für Informationen über die verschiedenen Datensätze, die wir zusammengestellt haben, siehe die <a %(a_datasets)s>Datensätze-Seite</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Für Informationen über diese spezielle Datei, schau dir die zugehörige <a %(a_href)s>JSON-Datei</a> an."

msgid "page.aarecord_issue.title"
msgstr "🔥 Problem beim Laden dieser Seite"

msgid "page.aarecord_issue.text"
msgstr "Bitte aktualisieren und erneut versuchen. <a %(a_contact)s>Kontaktiere uns, wenn das Problem mehrere Stunden lang andauert."

msgid "page.md5.invalid.header"
msgstr "Nicht gefunden"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” wurde nicht in unserer Datenbank gefunden."

msgid "page.login.title"
msgstr "Anmelden / Registrieren"

msgid "page.browserverification.header"
msgstr "Browser-Verifizierung"

msgid "page.login.text1"
msgstr "Um zu verhindern, dass Spam-Bots viele Accounts erstellen, müssen wir zuerst deinen Browser überprüfen."

msgid "page.login.text2"
msgstr "Falls du in einer Endlosschleife stecken bleibst, schlagen wir vor, dass du <a %(a_privacypass)s>Privacy Pass</a> installierst."

msgid "page.login.text3"
msgstr "Es kann auch hilfreich sein, Werbeblocker und andere Browsererweiterungen zu deaktivieren."

msgid "page.codes.title"
msgstr "Codes"

msgid "page.codes.heading"
msgstr "Codes-Explorer"

#, fuzzy
msgid "page.codes.intro"
msgstr "Untersuche die Codes, mit denen die Datensätze gekennzeichnet sind, nach dem jeweiligen Präfix. Die Spalte „Einträge“ zeigt die Anzahl der Datensätze, die mit Codes mit dem angegebenen Präfix gekennzeichnet sind, wie sie auch in der Suchmaschine angezeigt werden (einschließlich reiner Metadatensätze). In der Spalte „Codes“ wird angezeigt, wie viele tatsächliche Codes ein gegebenes Präfix hat."

msgid "page.codes.why_cloudflare"
msgstr "Der Aufbau dieser Seite kann einige Zeit in Anspruch nehmen, weshalb sie ein Cloudflare-Captcha erfordert. <a %(a_donate)s>Mitglieder</a> können das Captcha überspringen."

msgid "page.codes.dont_scrape"
msgstr "Bitte scrape diese Seiten nicht. Stattdessen empfehlen wir, unsere ElasticSearch- und MariaDB-Datenbanken <a %(a_import)s>zu generieren</a> oder <a %(a_download)s>herunterzuladen</a> und unseren <a %(a_software)s>Open-Source-Code</a> auszuführen. Die Rohdaten können manuell durch JSON-Dateien wie <a %(a_json_file)s>diese hier</a> erkundet werden."

msgid "page.codes.prefix"
msgstr "Präfix"

msgid "common.form.go"
msgstr "Los"

msgid "common.form.reset"
msgstr "Zurücksetzen"

msgid "page.codes.search_archive_start"
msgstr "Annas Archiv durchsuchen"

msgid "page.codes.bad_unicode"
msgstr "Warnung: Der Code enthält fehlerhafte Unicode-Zeichen und könnte in verschiedenen Situationen falsch funktionieren. Die Rohdaten können aus der base64-Darstellung in der URL dekodiert werden."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bekanntes Code-Präfix „%(key)s“"

msgid "page.codes.code_prefix"
msgstr "Präfix"

msgid "page.codes.code_label"
msgstr "Label"

msgid "page.codes.code_description"
msgstr "Beschreibung"

msgid "page.codes.code_url"
msgstr "URL für einen spezifischen Code"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "„%%s“ wird durch den Wert des Codes ersetzt"

msgid "page.codes.generic_url"
msgstr "Generische URL"

msgid "page.codes.code_website"
msgstr "Website"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s Datensatz, der „%(prefix_label)s“ entspricht"
msgstr[1] "%(count)s Datensätze, die „%(prefix_label)s“ entsprechen"

msgid "page.codes.url_link"
msgstr "URL für spezifischen Code: „%(url)s“"

#, fuzzy
msgid "page.codes.more"
msgstr "Mehr…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes, die mit „%(prefix_label)s“ beginnen"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index von"

msgid "page.codes.records_prefix"
msgstr "Einträge"

msgid "page.codes.records_codes"
msgstr "Codes"

msgid "page.codes.fewer_than"
msgstr "Weniger als %(count)s Aufzeichnungen"

msgid "page.contact.dmca.form"
msgstr "Für DMCA / Urheberrechtsansprüche nutze <a %(a_copyright)s>dieses Formular</a>."

msgid "page.contact.dmca.delete"
msgstr "Jeder Versuch, uns auf andere Weise bezüglich Urheberrechtsansprüchen zu kontaktieren, wird automatisch gelöscht."

msgid "page.contact.checkboxes.text1"
msgstr "Wir freuen uns sehr auf Ihr Feedback und Ihre Fragen!"

msgid "page.contact.checkboxes.text2"
msgstr "Trotzdem müssen wir dich aufgrund des hohen Volumens an Spam und unsinnigen E-Mails darum bitten, mit diesen Kästchen zu bestätigen, dass du die Bedingungen für Kontakt mit uns zur Kenntnis nimmst."

msgid "page.contact.checkboxes.copyright"
msgstr "Urheberrechtsansprüche an diese E-Mail-Adresse werden ignoriert werden, nutze stattdessen das Formular."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner-Server sind aufgrund von Hosting-Schließungen nicht verfügbar. Sie sollten bald wieder online sein."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Mitgliedschaften werden entsprechend verlängert."

msgid "layout.index.footer.dont_email"
msgstr "Bitte kontaktiere uns nicht bei <a %(a_request)s>Bücheranfragen</a> oder kleinen (<10.000) <a %(a_upload)s>Uploads</a>."

msgid "page.donate.please_include"
msgstr "Wenn du Fragen zum Konto oder zur Spende stellst, füge deine Konto-ID, Screenshots, Belege und so viele Informationen wie möglich hinzu. Wir überprüfen unsere E-Mails nur alle 1-2 Wochen, daher kann die Nichtangabe dieser Informationen eine Lösung verzögern."

msgid "page.contact.checkboxes.show_email_button"
msgstr "E-Mail-Adresse anzeigen"

msgid "page.copyright.title"
msgstr "DMCA / Urheberrechtsanspruch-Formular"

msgid "page.copyright.intro"
msgstr "Wenn du einen DMCA- oder anderen Urheberrechtsanspruch hast, fülle bitte dieses Formular so genau wie möglich aus. Wenn du auf Probleme stoßt, kontaktiere uns bitte unter unserer speziellen DMCA-Adresse: %(email)s. Beachte, dass an diese Adresse gesendete Ansprüche nicht bearbeitet werden, sie ist nur für Fragen vorgesehen. Bitte verwenden das untenstehende Formular, um Ansprüche einzureichen."

msgid "page.copyright.form.aa_urls"
msgstr "URLs auf Annas Archiv (erforderlich). Eine pro Zeile. Bitte nur URLs angeben, die genau dieselbe Ausgabe eines Buches beschreiben. Wenn du einen Anspruch für mehrere Bücher oder mehrere Ausgaben geltend machen möchtest, reiche dieses Formular bitte mehrfach ein."

msgid "page.copyright.form.aa_urls.note"
msgstr "Ansprüche, die mehrere Bücher oder Ausgaben zusammenfassen, werden abgelehnt."

msgid "page.copyright.form.name"
msgstr "Dein Name (erforderlich)"

msgid "page.copyright.form.address"
msgstr "Adresse (erforderlich)"

msgid "page.copyright.form.phone"
msgstr "Telefonnummer (erforderlich)"

msgid "page.copyright.form.email"
msgstr "E-Mail (erforderlich)"

msgid "page.copyright.form.description"
msgstr "Klare Beschreibung des Ausgangsmaterials (erforderlich)"

msgid "page.copyright.form.isbns"
msgstr "ISBNs des Ausgangsmaterials (falls zutreffend). Eine pro Zeile. Bitte nur solche angeben, die genau mit der Ausgabe übereinstimmen, für die du einen Urheberrechtsanspruch meldest."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs des Ausgangsmaterials, eine pro Zeile. Bitte nimm dir einen Moment Zeit, um in der Open Library nach dem Ausgangsmaterial zu suchen. Dies wird uns helfen, deinen Anspruch zu überprüfen."

msgid "page.copyright.form.external_urls"
msgstr "URLs zum Ausgangsmaterial, eine pro Zeile (erforderlich). Bitte gib so viele wie möglich an, um uns bei der Überprüfung deines Anspruchs zu helfen (z.B. Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Erklärung und Unterschrift (erforderlich)"

msgid "page.copyright.form.submit_claim"
msgstr "Anspruch einreichen"

msgid "page.copyright.form.on_success"
msgstr "✅ Vielen Dank für die Einreichung des Urheberrechtsanspruchs. Wir werden ihn so schnell wie möglich überprüfen. Bitte lade die Seite neu, um einen weiteren einzureichen."

msgid "page.copyright.form.on_failure"
msgstr "❌ Etwas ist schief gelaufen. Bitte lade die Seite neu und versuche es erneut."

msgid "page.datasets.title"
msgstr "Datensätze"

msgid "page.datasets.common.intro"
msgstr "Wenn du daran interessiert bist, diesen Datensatz für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainingszwecke </a> zu spiegeln, kontaktiere uns bitte."

msgid "page.datasets.intro.text2"
msgstr "Unsere Mission ist es, alle Bücher der Welt (sowie wissenschaftliche Aufsätze, Zeitschriften usw.) zu archivieren und offen zugänglich zu machen. Wir sind der Meinung, dass alle Bücher weiträumig gespiegelt werden sollten, um Redundanz und eine hohe Verfügbarkeit zu gewährleisten. Deshalb sammeln wir Dateien aus verschiedenen Quellen. Einige Quellen sind völlig offen und können in großen Mengen gespiegelt werden (wie Sci-Hub). Andere sind geschlossen und geschützt, daher versuchen wir sie zu scrapen, um die Bücher zu „befreien“. Wieder andere liegen irgendwo dazwischen."

msgid "page.datasets.intro.text3"
msgstr "Alle unsere Daten können <a %(a_torrents)s>getorrentet</a> werden, und alle unsere Metadaten können als ElasticSearch- und MariaDB-Datenbanken <a %(a_anna_software)s>generiert</a> oder <a %(a_elasticsearch)s>heruntergeladen</a> werden. Die Rohdaten können manuell durch JSON-Dateien wie <a %(a_dbrecord)s>diese</a> erkundet werden."

msgid "page.datasets.overview.title"
msgstr "Überblick"

msgid "page.datasets.overview.text1"
msgstr "Unten findest du einen kurzen Überblick über die Quellen der Dateien in Annas Archiv."

msgid "page.datasets.overview.source.header"
msgstr "Quelle"

msgid "page.datasets.overview.size.header"
msgstr "Größe"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% gespiegelt von AA / Torrents verfügbar"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Prozentsätze der Dateianzahl"

msgid "page.datasets.overview.last_updated.header"
msgstr "Zuletzt aktualisiert"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Sachbücher und Belletristik"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s Datei"
msgstr[1] "%(count)s Dateien"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Über Libgen.li „scimag“"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: seit 2021 eingefroren; das meiste ist über Torrents verfügbar"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: seitdem nur geringfügige Ergänzungen</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Ausgenommen „scimag“"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Belletristik-Torrents sind im Rückstand (obwohl IDs ~4-6M nicht getorrented wurden, da sie sich mit unseren Zlib-Torrents überschneiden)."

msgid "page.datasets.zlibzh.searchable"
msgstr "Die „chinesische“ Sammlung in der Z-Library scheint dieselbe wie unsere DuXiu-Sammlung zu sein, jedoch mit unterschiedlichen MD5s. Wir schließen diese Dateien aus den Torrents aus, um Duplikate zu vermeiden, zeigen sie aber dennoch in unserem Suchindex an."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ der Dateien sind durchsuchbar."

msgid "page.datasets.overview.total"
msgstr "Gesamt"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Duplikate ausgeschlossen"

msgid "page.datasets.overview.text4"
msgstr "Da die Schattenbibliotheken oft Daten untereinander abgleichen und synchronisieren, gibt es erhebliche Überschneidungen zwischen den Bibliotheken. Deshalb summieren sich die Zahlen nicht zur Gesamtzahl."

msgid "page.datasets.overview.text5"
msgstr "Der Prozentsatz „gespiegelt und geseeded von Annas Archive“ zeigt, wie viele Dateien wir selbst spiegeln. Wir seeden diese Dateien in großen Mengen über Torrents und stellen sie über Partner-Websites zum direkten Download zur Verfügung."

msgid "page.datasets.source_libraries.title"
msgstr "Quellbibliotheken"

msgid "page.datasets.source_libraries.text1"
msgstr "Einige Quellbibliotheken unterstützten das massenhafte Teilen ihrer Daten über Torrents, während andere ihre Sammlung nicht so bereitwillig teilen. Im letzteren Fall versucht Annas Archiv, die Sammlungen zu scrapen und verfügbar zu machen (siehe unsere <a %(a_torrents)s>Torrents</a>-Seite). Es gibt auch Grenzfälle, zum Beispiel, wenn Quellbibliotheken bereit sind, ihre Ressourcen zu teilen, aber nicht über die nötigen Mittel verfügen. In solchen Fällen versuchen wir ebenfalls zu helfen."

msgid "page.datasets.source_libraries.text2"
msgstr "Unten findest du eine Übersicht darüber, wie wir mit den verschiedenen Quellbibliotheken interagieren."

msgid "page.datasets.sources.source.header"
msgstr "Quelle"

msgid "page.datasets.sources.files.header"
msgstr "Dateien"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Tägliche <a %(dbdumps)s>HTTP-Datenbank-Dumps</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatisierte Torrents für <a %(nonfiction)s>Sachbücher</a> und <a %(fiction)s>Belletristik</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(covers)s>Buchcover-Torrents</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen „scimag“"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub hat seit 2021 keine neuen Dateien mehr hinzugefügt."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadaten-Dumps sind <a %(scihub1)s>hier</a> und <a %(scihub2)s>hier</a> verfügbar , sowie als Teil der <a %(libgenli)s>Libgen.li-Datenbank</a> (die wir verwenden)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Datentorrents sind <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a> und <a %(libgenli)s>hier</a> verfügbar"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Einige neue Dateien <a%(libgenrs)s>werden</a> weiterhin zu Libgens „scimag“ <a %(libgenli)s>hinzugefügt</a>, aber nicht genug, um neue Torrents zu rechtfertigen"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Vierteljährliche <a %(dbdumps)s>HTTP-Datenbank-Dumps</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Sachbuch-Torrents werden mit Libgen.rs geteilt (und <a %(libgenli)s>hier</a> gespiegelt)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annas Archiv und Libgen.li verwalten gemeinsam Sammlungen von <a %(comics)s>Comics</a>, <a %(magazines)s>Zeitschriften</a>, <a %(standarts)s>Normen-Dokumenten</a> und <a %(fiction)s>Belletristik (abgeleitet von Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Ihre „fiction_rus“-Sammlung (russische Fiktion) hat keine eigenen Torrents, wird aber durch Torrents von anderen abgedeckt, und wir besitzen einen <a %(fiction_rus)s>Mirror</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annas Archiv und Z-Library verwalten gemeinsam eine Sammlung von <a %(metadata)s>Z-Library-Metadaten</a> und <a %(files)s>Z-Library-Dateien</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Einige Metadaten sind über <a %(openlib)s>Open Library-Datenbank-Dumps</a> verfügbar, aber diese decken nicht die gesamte IA-Sammlung ab"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Keine leicht zugänglichen Metadaten-Dumps für ihre gesamte Sammlung verfügbar"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(ia)s>IA-Metadaten</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Dateien nur eingeschränkt zum Ausleihen verfügbar, mit verschiedenen Zugriffsrestriktionen"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(ia)s>IA-Dateien</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Verschiedene Metadaten-Datenbanken, die im chinesischen Internet verstreut sind; oft kostenpflichtige Datenbanken"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Keine leicht zugänglichen Metadaten-Dumps für ihre gesamte Sammlung verfügbar."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(duxiu)s>DuXiu-Metadaten</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Verschiedene Dateidatenbanken, die im chinesischen Internet verstreut sind; oft kostenpflichtige Datenbanken."

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Die meisten Dateien sind nur mit Premium-BaiduYun-Konten zugänglich; langsame Download-Geschwindigkeiten."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(duxiu)s>DuXiu-Dateien</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Verschiedene kleinere oder einmalige Quellen. Wir ermutigen die Leute, Dateien zuerst in andere Schattenbibliotheken hochzuladen, aber manchmal haben Leute Sammlungen, die zu groß sind, um von anderen sortiert zu werden und nicht groß genug, um eine eigene Kategorie zu rechtfertigen."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Nur-Metadaten-Quellen"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Wir bereichern unsere Sammlung auch mit Nur-Metadaten-Quellen, die wir mit Dateien abgleichen können, z.B. durch ISBN-Nummern oder andere Felder. Unten findest du eine Übersicht dieser Quellen. Auch hier sind einige dieser Quellen vollständig offen, während wir andere scrapen müssen."

msgid "page.faq.metadata.inspiration"
msgstr "Unsere Inspiration für das Sammeln von Metadaten ist Aaron Swartz' Ziel, „eine Website für jedes jemals veröffentlichte Buch“ zu erstellen, wofür er die <a %(a_openlib)s>Open Library</a> ins Leben rief. Dieses Projekt hat sich gut entwickelt. Unsere einzigartige Position ermöglicht es uns jedoch, Metadaten zu erhalten, die der Open Library nicht zur Verfügung stehen. Eine weitere Inspiration war unser Wunsch zu wissen, <a %(a_blog)s>wie viele Bücher es auf der Welt gibt</a>, damit wir berechnen können, wie viele Bücher wir noch retten müssen."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Beachte, dass wir bei der Metadatensuche die Originaldatensätze anzeigen. Wir führen keine Zusammenführung von Datensätzen durch."

msgid "page.datasets.sources.last_updated.header"
msgstr "Zuletzt aktualisiert"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Monatliche <a %(dbdumps)s>Datenbank-Dumps</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nicht direkt in großen Mengen verfügbar, gegen Scraping geschützt"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(worldcat)s>OCLC (WorldCat)-Metadaten</a>"

msgid "page.datasets.unified_database.title"
msgstr "Vereinheitlichte Datenbank"

msgid "page.datasets.unified_database.text1"
msgstr "Wir kombinieren alle oben genannten Quellen zu einer einheitlichen Datenbank, die wir zur Bereitstellung dieser Website verwenden. Diese einheitliche Datenbank ist nicht direkt verfügbar, aber da Annas Archiv vollständig Open Source ist, kann sie relativ einfach als ElasticSearch- und MariaDB-Datenbanken <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>heruntergeladen</a> werden. Die Skripte auf dieser Seite laden automatisch alle erforderlichen Metadaten von den oben genannten Quellen herunter."

msgid "page.datasets.unified_database.text2"
msgstr "Wenn du unsere Daten erkunden möchtest, bevor du diese Skripte lokal ausführst, kannst du auch unsere JSON-Dateien ansehen, die weiter zu anderen JSON-Dateien verlinken. <a %(a_json)s>Diese Datei</a> ist ein guter Ausgangspunkt."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Angepasst von unserem <a %(a_href)s>Blogbeitrag</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> ist eine riesige Datenbank gescannter Bücher, erstellt von der <a %(superstar_link)s>SuperStar Digital Library Group</a>. Die meisten sind akademische Bücher, die gescannt wurden, um sie Universitäten und Bibliotheken digital zur Verfügung zu stellen. Für unser englischsprachiges Publikum haben <a %(princeton_link)s>Princeton</a> und die <a %(uw_link)s>University of Washington</a> gute Übersichten. Es gibt auch einen ausgezeichneten Artikel, der mehr Hintergrundinformationen bietet: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine“</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Die Bücher von Duxiu wurden lange Zeit im chinesischen Internet über Schwarzkopien verbreitet. Normalerweise werden sie von Wiederverkäufern für weniger als einen Dollar verkauft. Sie werden typischerweise mit dem chinesischen Äquivalent von Google Drive verteilt, das oft gehackt wurde, um mehr Speicherplatz zu erhalten. Einige technische Details findest du <a %(link1)s>hier</a> und <a %(link2)s>hier</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Obwohl die Bücher halböffentlich verteilt wurden, ist es ziemlich schwierig, sie in großen Mengen zu erhalten. Wir hatten dies ganz oben auf unserer TODO-Liste und mehrere Monate Vollzeitarbeit dafür eingeplant. Doch Ende 2023 meldete sich ein unglaublich toller und talentierter Freiwilliger bei uns und teilte uns mit, dass er all diese Arbeit bereits erledigt hatte — und das zu hohen Kosten. Er teilte die gesamte Sammlung mit uns, ohne etwas im Gegenzug zu erwarten – abgesehen von der Zusicherung einer langfristigen Bewahrung. Das ist wirklich bemerkenswert."

msgid "page.datasets.common.resources"
msgstr "Ressourcen"

msgid "page.datasets.common.total_files"
msgstr "Gesamtdateien: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Gesamtdateigröße: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Dateien gespiegelt von Annas Archive: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Zuletzt aktualisiert: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrents von Annas Archive"

msgid "page.datasets.common.aa_example_record"
msgstr "Beispieldatensatz auf Annas Archive"

msgid "page.datasets.duxiu.blog_post"
msgstr "Unser Blogbeitrag über diese Daten"

msgid "page.datasets.common.import_scripts"
msgstr "Skripte zum Importieren von Metadaten"

msgid "page.datasets.common.aac"
msgstr "Annas Archive Containers Format"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Weitere Informationen von unseren Freiwilligen (ursprüngliche Anmerkungen):"

msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.ia.description"
msgstr "Dieser Datensatz steht in engem Zusammenhang mit dem <a %(a_datasets_openlib)s>Open Library Dataset</a>. Er enthält einen Scrape aller Metadaten und einen großen Teil der Dateien aus der Controlled Digital Lending Library der IA. Updates werden im <a %(a_aac)s>Annas Archive Containers Format</a> veröffentlicht."

msgid "page.datasets.ia.description2"
msgstr "Diese Datensätze stammen direkt aus dem Open Library-Datensatz, enthalten aber auch Einträge, die nicht in der Open Library enthalten sind. Wir haben auch eine Reihe von Dateien, die im Laufe der Jahre von Community-Mitgliedern gescrapt wurden."

msgid "page.datasets.ia.description3"
msgstr "Die Sammlung besteht aus zwei Teilen. Du benötigst beide Teile, um alle Daten zu erhalten (außer veralteten Torrents, die auf der Torrents-Seite durchgestrichen sind)."

msgid "page.datasets.ia.part1"
msgstr "unser erster Release, bevor wir auf das standardisierte <a %(a_aac)s>Annas Archive Containers (AAC) Format</a> umgestellt haben. Enthält Metadaten (als JSON und XML), PDFs (aus den digitalen Leihsystemen acsm und lcpdf) und Cover-Thumbnails."

msgid "page.datasets.ia.part2"
msgstr "inkrementelle neue Releases, die AAC verwenden. Enthält nur Metadaten mit Zeitstempeln nach dem 01.01.2023, da der Rest bereits von „ia“ abgedeckt ist. Außerdem alle PDF-Dateien, diesmal aus den Leihsystemen acsm und „bookreader“ (IAs Web-Reader). Trotz des nicht ganz passenden Namens fügen wir weiterhin bookreader-Dateien in die Sammlung ia2_acsmpdf_files ein, da sie sich gegenseitig ausschließen."

msgid "page.datasets.common.main_website"
msgstr "Hauptwebsite %(source)s"

msgid "page.datasets.ia.ia_lending"
msgstr "Digitale Leihbibliothek"

msgid "page.datasets.common.metadata_docs"
msgstr "Metadaten-Dokumentation (die meisten Felder)"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN-Länderinformationen"

msgid "page.datasets.isbn_ranges.text1"
msgstr "Die Internationale ISBN-Agentur veröffentlicht regelmäßig die Bereiche, die sie den nationalen ISBN-Agenturen zugewiesen hat. Daraus können wir ableiten, zu welchem Land, welcher Region oder welcher Sprachgruppe diese ISBN gehört. Derzeit nutzen wir diese Daten indirekt über die <a %(a_isbnlib)s>isbnlib</a> Python-Bibliothek."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Ressourcen"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Zuletzt aktualisiert: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN-Website"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadaten"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Die Hintergrundgeschichte der verschiedenen Library Genesis-Forks findest du auf der Seite für <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li enthält die meisten der gleichen Inhalte und Metadaten wie Libgen.rs, hat jedoch einige zusätzliche Sammlungen, nämlich Comics, Zeitschriften und Normen-Dokumente. Es hat auch <a %(a_scihub)s>Sci-Hub</a> in seine Metadaten und Suchmaschine integriert, was wir für unsere Datenbank verwenden."

msgid "page.datasets.libgen_li.description3"
msgstr "Die Metadaten für diese Bibliothek sind frei verfügbar <a %(a_libgen_li)s>bei libgen.li</a>. Dieser Server ist jedoch langsam und unterstützt keine Wiederaufnahme unterbrochener Verbindungen. Die gleichen Dateien sind auch auf <a %(a_ftp)s>einem FTP-Server</a> verfügbar, der besser funktioniert."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents sind für die meisten zusätzlichen Inhalte verfügbar, insbesondere wurden Torrents für Comics, Zeitschriften und Normen-Dokumente in Zusammenarbeit mit Annas Archiv veröffentlicht."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Die Belletristiksammlung hat eigene Torrents (abweichend von <a %(a_href)s>Libgen.rs</a>), beginnend bei %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Laut dem Administrator von Libgen.li sollte die „fiction_rus“-Sammlung (russische Belletristik) durch regelmäßig veröffentlichte Torrents von <a %(a_booktracker)s>booktracker.org</a> abgedeckt sein, insbesondere die <a %(a_flibusta)s>flibusta</a> und <a %(a_librusec)s>lib.rus.ec</a> Torrents (die wir <a %(a_torrents)s>hier</a> spiegeln, obwohl wir noch nicht festgestellt haben, welche Torrents zu welchen Dateien gehören)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistiken für alle Sammlungen findest du <a %(a_href)s>auf der Website von Libgen</a>."

msgid "page.datasets.libgen_li.description4.1"
msgstr "Sachbücher scheinen sich ebenfalls verändert zu haben, jedoch ohne neue Torrents. Es scheint, dass dies seit Anfang 2022 geschehen ist, obwohl wir dies nicht überprüft haben."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Bestimmte Bereiche ohne Torrents (wie Belletristikbereiche f_3463000 bis f_4260000) sind wahrscheinlich Z-Library (oder andere doppelte) Dateien, obwohl wir möglicherweise eine Deduplizierung durchführen und Torrents für lgli-einzigartige Dateien in diesen Bereichen erstellen möchten."

msgid "page.datasets.libgen_li.description5"
msgstr "Beachte, dass die Torrent-Dateien, die sich auf „libgen.is“ beziehen, ausdrücklich Spiegelungen von <a %(a_libgen)s>Libgen.rs</a> sind („.is“ ist eine andere Domain, die von Libgen.rs verwendet wird)."

msgid "page.datasets.libgen_li.description6"
msgstr "Eine hilfreiche Ressource zur Nutzung der Metadaten ist <a %(a_href)s>diese Seite</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Belletristik-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Comics-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Zeitschriften-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Normen-Dokumente-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russische Belletristik-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadaten"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadaten via FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informationen zu Metadatenfeldern"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Spiegelung anderer Torrents (und einzigartiger Belletristik- und Comics-Torrents)"

msgid "page.datasets.libgen_li.forum"
msgstr "Diskussionsforum"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Unser Blogbeitrag über die Veröffentlichung der Comics"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "Kurz gesagt, der Hintergrund der verschiedenen Library Genesis (oder „Libgen“) Forks ist, dass im Laufe der Zeit die verschiedenen Beteiligten von Library Genesis sich zerstritten und getrennte Wege gingen."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Die „.fun“-Version wurde vom ursprünglichen Gründer erstellt. Sie wird zugunsten einer neuen, stärker verteilten Version überarbeitet."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Die „.rs“-Version hat sehr ähnliche Daten und veröffentlicht ihre Sammlung in Bulk-Torrents. Sie ist grob in einen „Belletristik“- und einen „Sachbuch“-Bereich unterteilt."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Ursprünglich unter „http://gen.lib.rus.ec“."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Die <a %(a_li)s>„.li“-Version</a> hat eine riesige Sammlung von Comics sowie andere Inhalte, die (noch) nicht als Bulk-Download über Torrents verfügbar sind. Sie hat eine separate Torrent-Sammlung von Belletristik-Büchern und enthält die Metadaten von <a %(a_scihub)s>Sci-Hub</a> in ihrer Datenbank."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Laut diesem <a %(a_mhut)s>Forenbeitrag</a> wurde Libgen.li ursprünglich unter „http://free-books.dontexist.com“ gehostet."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> ist in gewisser Weise auch ein Fork von Library Genesis, obwohl sie einen anderen Namen für ihr Projekt verwendet haben."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Diese Seite bezieht sich auf die „.rs“-Version. Sie ist dafür bekannt, sowohl ihre Metadaten als auch den vollständigen Inhalt ihres Buchkatalogs konsequent zu veröffentlichen. Ihre Buchsammlung ist in einen Belletristik- und einen Sachbuch-Teil unterteilt."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Eine hilfreiche Ressource zur Nutzung der Metadaten ist <a %(a_metadata)s>diese Seite</a> (blockiert IP-Bereiche, VPN könnte erforderlich sein)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Ab März 2024 werden neue Torrents in <a %(a_href)s>diesem Forenthread</a> gepostet (blockiert IP-Bereiche, VPN könnte erforderlich sein)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Sachbuch-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Belletristik-Torrents auf Annas Archiv"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadaten"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs Metadatenfeldinformationen"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Sachbuch-Torrents"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Belletristik-Torrents"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskussionsforum"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents von Annas Archiv (Buchcover)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Unser Blog über die Veröffentlichung der Buchcover"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis ist dafür bekannt, ihre Daten bereits großzügig in großen Mengen über Torrents verfügbar zu machen. Unsere Libgen-Sammlung besteht aus zusätzlichen Daten, die sie nicht direkt veröffentlichen, in Zusammenarbeit mit ihnen. Vielen Dank an alle Beteiligten von Library Genesis für die Zusammenarbeit mit uns!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Veröffentlichung 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Diese <a %(blog_post)s>erste Veröffentlichung</a> ist ziemlich klein: etwa 300GB an Buchcovern aus dem Libgen.rs-Fork, sowohl Belletristik als auch Sachbuch. Sie sind in der gleichen Weise organisiert, wie sie auf libgen.rs erscheinen, z.B.:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s für ein Sachbuch."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s für ein Belletristikbuch."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Genau wie bei der Z-Library-Sammlung haben wir sie alle in eine große .tar-Datei gepackt, die mit <a %(a_ratarmount)s>ratarmount</a> gemountet werden kann, falls du die Dateien direkt bereitstellen möchtest."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> ist eine proprietäre Datenbank der gemeinnützigen Organisation <a %(a_oclc)s>OCLC</a>, die Metadatenaufzeichnungen von Bibliotheken aus der ganzen Welt aggregiert. Es ist wahrscheinlich die größte Bibliotheks-Metadatensammlung der Welt."

msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, erste Veröffentlichung:"

msgid "page.datasets.worldcat.description2"
msgstr "Im Oktober 2023 haben wir einen umfassenden Scrape der OCLC (WorldCat)-Datenbank im <a %(a_aac)s>Annas Archiv Containers-Format</a> <a %(a_scrape)s>veröffentlicht</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrents von Annas Archiv"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Unser Blogbeitrag über diese Daten"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Die Open Library ist ein Open-Source-Projekt des Internet Archive, das darauf abzielt, alle Bücher der Welt zu katalogisieren. Es verfügt über eine der größten Buchscannoperationen weltweit und viele Bücher stehen zum digitalen Ausleihen bereit. Der Buchmetadatakatalog von Open Library ist frei zum Download verfügbar und in Annas Archiv enthalten (derzeit jedoch nicht in der Suche, außer wenn du explizit nach einer Open-Library-ID suchst)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metadaten"

msgid "page.datasets.isbndb.release1.title"
msgstr "Veröffentlichung 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Dies ist ein Dump von vielen Anfragen an isbndb.com im September 2022. Wir haben versucht, alle ISBN-Bereiche abzudecken. Dies sind etwa 30,9 Millionen Datensätze. Auf ihrer Website behaupten sie, tatsächlich 32,6 Millionen Datensätze zu haben, also haben wir möglicherweise einige irgendwie übersehen, oder <em>sie</em> machen eventuell etwas falsch."

msgid "page.datasets.isbndb.release1.text2"
msgstr "Die JSON-Antworten von Ihrem Server sind ziemlich roh. Ein Datenqualitätsproblem, das wir bemerkt haben, ist, dass für ISBN-13-Nummern, die mit einem anderen Präfix als „978-“ beginnen, immer noch ein „isbn“-Feld enthalten ist. Dieses enthält einfach die ISBN-13-Nummer mit den ersten drei abgeschnittenen Zahlen (und der neu berechneten Prüfziffer). Das ist offensichtlich falsch, aber da sie es offensichtlich so handhaben, haben wir es nicht geändert."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Ein weiteres potenzielles Problem, auf das du stoßen könntest, ist die Tatsache, dass das „isbn13“-Feld Duplikate enthält, sodass du es nicht als Primärschlüssel in einer Datenbank verwenden kannst. Die Kombination der Felder „isbn13“+„isbn“ scheint jedoch eindeutig zu sein."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Für Hintergrundinformationen zu Sci-Hub besuche bitte die <a %(a_scihub)s>offizielle Website</a>, die <a %(a_wikipedia)s>Wikipedia-Seite</a> oder höre dieses <a %(a_radiolab)s>Podcast-Interview</a> an."

msgid "page.datasets.scihub.description2"
msgstr "Beachte, dass Sci-Hub seit <a %(a_reddit)s>2021 eingefroren</a> ist. Es war schon früher eingefroren, aber 2021 wurden ein paar Millionen wissenschaftliche Aufsätze hinzugefügt. Dennoch werden einige wenige wissenschaftliche Aufsätze zu den Libgen „scimag“-Sammlungen noch immer hinzugefügt, jedoch nicht genug, um neue Bulk-Torrents zu rechtfertigen."

msgid "page.datasets.scihub.description3"
msgstr "Wir verwenden die Sci-Hub-Metadaten, die von <a %(a_libgen_li)s>Libgen.li</a> in seiner „scimag“-Sammlung bereitgestellt werden. Wir verwenden auch den <a %(a_dois)s>dois-2022-02-12.7z</a>-Datensatz."

msgid "page.datasets.scihub.description4"
msgstr "Beachte, dass die „smarch“-Torrents <a %(a_smarch)s>veraltet</a> sind und daher nicht in unserer Torrent-Liste enthalten sind."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents auf Annas Archiv"

msgid "page.datasets.scihub.link_metadata"
msgstr "Metadaten und Torrents"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents auf Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents auf Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Updates auf Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-Seite"

msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast-Interview"

msgid "page.datasets.upload.title"
msgstr "Uploads zu Annas Archiv"

msgid "page.datasets.upload.overview"
msgstr "Übersicht von der <a %(a1)s>Datensätze-Seite</a>."

msgid "page.datasets.upload.description"
msgstr "Verschiedene kleinere oder einmalige Quellen. Wir ermutigen die Leute, Dateien zuerst in andere Schattenbibliotheken hochzuladen, aber manchmal haben Leute Sammlungen, die zu groß sind, um von anderen sortiert zu werden und nicht groß genug, um eine eigene Kategorie zu rechtfertigen."

msgid "page.datasets.upload.subcollections"
msgstr "Die <q>Upload</q> -Sammlung ist in kleinere Unterkollektionen aufgeteilt, die in den AACIDs und Torrent-Namen angegeben sind. Alle Unterkollektionen wurden zuerst gegen die Hauptsammlung dedupliziert, obwohl die Metadaten-<q> upload_records</q> -JSON-Dateien immer noch viele Verweise auf die Originaldateien enthalten. Nicht-Buch-Dateien wurden auch aus den meisten Unterkollektionen entfernt und sind typischerweise <em>nicht</em> in der <q> upload_records</q> -JSON vermerkt."

msgid "page.datasets.upload.subsubcollections"
msgstr "Viele Unterkollektionen bestehen selbst aus Unter-Unterkollektionen (z.B. von verschiedenen Originalquellen), die als Verzeichnisse in den <q> filepath</q> -Feldern dargestellt sind."

msgid "page.datasets.upload.subs.heading"
msgstr "Die Unterkollektionen sind:"

msgid "page.datasets.upload.subs.subcollection"
msgstr "Unterkollektion"

msgid "page.datasets.upload.subs.notes"
msgstr "Anmerkungen"

msgid "page.datasets.upload.action.browse"
msgstr "durchsuchen"

msgid "page.datasets.upload.action.search"
msgstr "suchen"

msgid "page.datasets.upload.source.aaaaarg"
msgstr "Von <a %(a_href)s>aaaaarg.fail</a>. Scheint ziemlich vollständig zu sein. Von unserem Freiwilligen <q>cgiym</q>."

msgid "page.datasets.upload.source.acm"
msgstr "Von einem <a %(a_href)s><q>ACM Digital Library 2020</q></a> Torrent. Hat eine ziemlich hohe Überschneidung mit bestehenden wissenschaftlichen Aufsatzsammlungen, aber sehr wenige MD5-Übereinstimmungen, daher haben wir beschlossen, es vollständig zu behalten."

msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape von <q>iRead eBooks</q> (= phonetisch <q>ai rit i-books</q>; airitibooks.com), durch den Freiwilligen <q>j</q>. Entspricht den <q>airitibooks</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

msgid "page.datasets.upload.source.alexandrina"
msgstr "Aus der Sammlung <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Teilweise aus der Originalquelle, teilweise von the-eye.eu, teilweise von anderen Mirrors."

msgid "page.datasets.upload.source.bibliotik"
msgstr "Von einer privaten Bücher-Torrent-Website namens <a %(a_href)s>Bibliotik</a> (oft als <q>Bib</q> bezeichnet), deren Bücher nach Namen (A.torrent, B.torrent) gebündelt und über the-eye.eu verteilt wurden."

msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Von unserem Freiwilligen <q>bpb9v</q>. Für weitere Informationen über <a %(a_href)s>CADAL</a> siehe die Anmerkungen auf unserer <a %(a_duxiu)s>DuXiu-Datensatzseite</a>."

msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mehr von unserem Freiwilligen <q>bpb9v</q>, hauptsächlich DuXiu-Dateien sowie ein Ordner <q>WenQu</q> und <q>SuperStar_Journals</q> (SuperStar ist die Firma hinter DuXiu)."

msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Von unserem Freiwilligen <q>cgiym</q>, chinesische Texte aus verschiedenen Quellen (als Unterverzeichnisse dargestellt), einschließlich von <a %(a_href)s>China Machine Press</a> (ein großer chinesischer Verlag)."

msgid "page.datasets.upload.source.cgiym_more"
msgstr "Nicht-chinesische Sammlungen (als Unterverzeichnisse dargestellt) von unserem Freiwilligen <q>cgiym</q>."

msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape von Büchern über chinesische Architektur, durch den Freiwilligen <q>cm</q>: <q>Ich habe die Daten durch Ausnutzung einer Netzwerkschwachstelle beim Verlag bekommen, aber diese Lücke wurde inzwischen geschlossen</q>. Entspricht den <q>chinese_architecture</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Bücher vom akademischen Verlag <a %(a_href)s>De Gruyter</a>, gesammelt aus einigen großen Torrents."

msgid "page.datasets.upload.source.docer"
msgstr "Scrape von <a %(a_href)s>docer.pl</a>, einer polnischen File-Sharing-Website, die sich auf Bücher und andere schriftliche Werke konzentriert. Gescrapt Ende 2023 vom Freiwilligen <q>p</q>. Wir haben keine guten Metadaten von der ursprünglichen Webseite (nicht einmal Dateierweiterungen), aber wir haben nach buchähnlichen Dateien gefiltert und konnten oft Metadaten aus den Dateien selbst extrahieren."

msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu EPUBs, direkt von DuXiu, gesammelt vom Freiwilligen <q>w</q>. Nur aktuelle DuXiu-Bücher sind direkt als E-Books verfügbar, daher müssen die meisten davon neueren Datums sein."

msgid "page.datasets.upload.source.duxiu_main"
msgstr "Verbleibende DuXiu-Dateien vom Freiwilligen <q>m</q>, die nicht im proprietären PDG-Format von DuXiu waren (der Haupt-<a %(a_href)s>DuXiu-Datensatz</a>). Gesammelt aus vielen Originalquellen, leider ohne diese Quellen im Dateipfad zu nennen."

msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

msgid "page.datasets.upload.source.hentai"
msgstr "Scrape von erotischen Büchern, durch den Freiwilligen <q>do no harm</q>. Entspricht den <q>hentai</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

msgid "page.datasets.upload.source.japanese_manga"
msgstr "Sammlung, gescrapt von einem japanischen Manga-Verlag vom Freiwilligen <q>t</q>."

msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Ausgewählte gerichtliche Archive von Longquan</a>, bereitgestellt vom Freiwilligen <q>c</q>."

msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape von <a %(a_href)s>magzdb.org</a>, einem Verbündeten von Library Genesis (ist auf der libgen.rs-Homepage verlinkt), der seine Dateien jedoch nicht direkt bereitstellen wollte. Erhalten vom Freiwilligen <q>p</q> Ende 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

msgid "page.datasets.upload.source.misc"
msgstr "Verschiedene kleine Uploads, zu klein, um als eigene Unterkollektion zu gelten, aber als Verzeichnis dargestellt. Das <q>oo42hcksBxZYAOjqwGWu</q> Verzeichnis entspricht den <q>czech_oo42hcks</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-Books von AvaxHome, einer russischen Filesharing-Webseite."

msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archiv von Zeitungen und Zeitschriften. Entspricht den <q>newsarch_magz</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape des <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Sammlung vom Freiwilligen <q>o</q>, der polnische Bücher direkt von Originalveröffentlichungs-Websites (<q>Szene<q>) gesammelt hat."

msgid "page.datasets.upload.source.shuge"
msgstr "Kombinierte Sammlungen von <a %(a_href)s>shuge.org</a> von den Freiwilligen <q>cgiym</q> und <q>woz9ts</q>."

msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>„Imperial Library of Trantor“</a> (benannt nach der fiktiven Bibliothek), gescrapt 2022 vom Freiwilligen „t“. Entspricht den <q>trantor</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Unter-Unter-Sammlungen (als Verzeichnisse dargestellt) vom Freiwilligen „woz9ts“: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (von <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan) und mebook (mebook.cc, 我的小书屋, mein kleines Bücherzimmer — woz9ts: „Diese Seite konzentriert sich hauptsächlich auf das Teilen hochwertiger E-Book-Dateien, von denen einige vom Besitzer selbst gesetzt wurden. Der Besitzer wurde <a %(a_arrested)s>2019 verhaftet</a> und jemand hat eine Sammlung der von ihm geteilten Dateien erstellt.“)."

msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Verbleibende DuXiu-Dateien vom Freiwilligen <q>woz9ts</q>, die nicht im proprietären DuXiu-PDG-Format vorlagen (müssen noch in PDF konvertiert werden)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents von Annas Archiv"

msgid "page.datasets.zlib.title"
msgstr "Z-Library-Scrape"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library hat seine Wurzeln in der <a %(a_href)s>Library Genesis</a>-Community und wurde ursprünglich ausgehend von deren Daten gestartet. Seitdem hat es sich erheblich professionalisiert und verfügt über eine viel modernere Benutzeroberfläche. Daher erhalten sie viel mehr Spenden, sowohl monetär zur Verbesserung ihrer Website als auch neue Bücherspenden. Sie haben eine große Sammlung zusätzlich zu Library Genesis aufgebaut."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Aktualisierung ab Februar 2023."

msgid "page.datasets.zlib.description.allegations"
msgstr "Ende 2022 wurden die mutmaßlichen Gründer der Z-Library verhaftet und die Domains von den US-Behörden beschlagnahmt. Seitdem ist die Website langsam wieder online gegangen. Es ist unbekannt, wer sie derzeit betreibt."

msgid "page.datasets.zlib.description.three_parts"
msgstr "Die Sammlung besteht aus drei Teilen. Die ursprünglichen Beschreibungsseiten für die ersten beiden Teile sind unten angegeben. Du benötigst alle drei Teile, um alle Daten zu erhalten (außer veraltete Torrents, die auf der Torrents-Seite durchgestrichen sind)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: unsere erste Veröffentlichung. Dies war die allererste Veröffentlichung dessen, was damals „Pirate Library Mirror“ („pilimi“) genannt wurde."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: zweite Veröffentlichung, diesmal mit allen Dateien in .tar-Dateien verpackt."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementelle neue Veröffentlichungen, die das <a %(a_href)s>Annas Archiv Container (AAC) Format</a> verwenden, jetzt in Zusammenarbeit mit dem Z-Library-Team veröffentlicht."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents von Annas Archiv (Metadaten + Inhalt)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Beispiel-Datensatz in Annas Archiv (Originalsammlung)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Beispiel-Datensatz in Annas Archiv („zlib3“-Sammlung)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Hauptwebsite"

msgid "page.datasets.zlib.link.onion"
msgstr "Tor-Domain"

msgid "page.datasets.zlib.blog.release1"
msgstr "Blogbeitrag über Veröffentlichung 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Blogbeitrag über Veröffentlichung 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-Veröffentlichungen (ursprüngliche Beschreibungsseiten)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Veröffentlichung 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Der ursprüngliche Mirror wurde im Laufe der Jahre 2021 und 2022 mühsam erstellt. Zu diesem Zeitpunkt ist er leicht veraltet: Er spiegelt den Stand der Sammlung im Juni 2021 wider. Wir werden dies in Zukunft aktualisieren. Im Moment konzentrieren wir uns darauf, diese erste Veröffentlichung herauszubringen."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Da Library Genesis bereits mit öffentlichen Torrents gesichert ist und in der Z-Library enthalten ist, haben wir im Juni 2022 eine grundlegende Duplikatsbereinigung gegen Library Genesis durchgeführt. Dafür haben wir MD5-Hashes verwendet. Es gibt wahrscheinlich noch viel mehr doppelte Inhalte in der Bibliothek, wie z. B. mehrere Dateiformate desselben Buches. Dies ist schwer genau zu erkennen, daher tun wir es nicht. Nach der Duplikatsbereinigung bleiben uns über 2 Millionen Dateien, die insgesamt knapp 7 TB umfassen."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Die Sammlung besteht aus zwei Teilen: einem MySQL-“.sql.gz”-Dump der Metadaten und den 72 Torrent-Dateien von jeweils etwa 50-100 GB. Die Metadaten enthalten die Daten, wie sie von der Z-Library-Website gemeldet wurden (Titel, Autor, Beschreibung, Dateityp), sowie die tatsächliche Dateigröße und md5sum, die wir beobachtet haben, da diese manchmal nicht übereinstimmen. Es scheint Bereiche von Dateien zu geben, für die die Z-Library selbst falsche Metadaten hat. In einigen Einzelfällen haben wir möglicherweise auch Dateien falsch heruntergeladen, die wir in Zukunft zu erkennen und zu beheben versuchen werden."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Die großen Torrent-Dateien enthalten die eigentlichen Buchdaten, wobei die Z-Library-ID als Dateiname verwendet wird. Die Dateierweiterungen können mithilfe des Metadaten-Dumps rekonstruiert werden."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Die Sammlung ist eine Mischung aus Sachbuch- und Belletristik-Inhalten (nicht wie in Library Genesis getrennt). Die Qualität variiert ebenfalls stark."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Diese erste Veröffentlichung ist jetzt vollständig verfügbar. Beachte, dass die Torrent-Dateien nur über unseren Tor-Mirror verfügbar sind."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Veröffentlichung 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Wir haben alle Bücher erhalten, die zwischen unserem letzten Mirror und August 2022 zur Z-Library hinzugefügt wurden. Wir haben auch noch einige Bücher geholt, die wir beim ersten Mal übersehen haben. Insgesamt umfasst diese neue Sammlung etwa 24 TB. Auch diese Sammlung ist gegen Library Genesis dedupliziert, da für diese Sammlung bereits Torrents verfügbar sind."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Die Daten sind ähnlich wie bei der ersten Veröffentlichung organisiert. Es gibt einen MySQL-“.sql.gz”-Dump der Metadaten, der auch alle Metadaten der ersten Veröffentlichung enthält und diese somit ersetzt. Wir haben auch einige neue Spalten hinzugefügt:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ob diese Datei bereits in Library Genesis enthalten ist, entweder in der Sachbuch- oder Belletristik-Sammlung (abgeglichen durch md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in welchem Torrent sich diese Datei befindet."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: gesetzt, wenn wir das Buch nicht herunterladen konnten."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Wir haben dies beim letzten Mal erwähnt, aber nur zur Klarstellung: „filename“ und „md5“ sind die tatsächlichen Eigenschaften der Datei, während „filename_reported“ und „md5_reported“ das ist, was wir von Z-Library gescrapt haben. Manchmal stimmen diese beiden nicht überein, daher haben wir beide aufgenommen."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Für diese Veröffentlichung haben wir die Kollation auf „utf8mb4_unicode_ci“ geändert, die mit älteren Versionen von MySQL kompatibel sein sollte."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Die Dateien sind ähnlich wie beim letzten Mal, obwohl sie viel größer sind. Wir konnten uns einfach nicht dazu durchringen, viele kleinere Torrent-Dateien zu erstellen. „pilimi-zlib2-0-14679999-extra.torrent“ enthält alle Dateien, die wir in der letzten Veröffentlichung verpasst haben, während die anderen Torrents alle neuen ID-Bereiche enthalten. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> Wir haben die meisten unserer Torrents zu groß gemacht, was dazu führte, dass Torrent-Clients Schwierigkeiten hatten. Wir haben sie entfernt und neue Torrents veröffentlicht."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> Es waren immer noch zu viele Dateien, also haben wir sie in Tar-Dateien verpackt und erneut neue Torrents veröffentlicht."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Veröffentlichung 2 Nachtrag (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dies ist eine einzelne zusätzliche Torrent-Datei. Sie enthält keine neuen Informationen, aber sie enthält einige Daten, deren Berechnung eine Weile dauern kann. Das macht sie praktisch, da das Herunterladen dieses Torrents oft schneller ist, als ihn von Grund auf neu zu berechnen. Insbesondere enthält sie SQLite-Indizes für die Tar-Dateien zur Verwendung mit <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Häufig gestellte Fragen (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Was ist Annas Archiv?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Annas Archiv</span> ist ein gemeinnütziges Projekt mit den folgenden zwei Zielen:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Bewahrung:</strong> Sicherung des Wissens und der Kultur der Menschheit.</li><li><strong>Bereitstellung:</strong> Dieses Wissen und Kultur allen Menschen auf der Welt zugänglich machen.</li>"

msgid "page.home.intro.open_source"
msgstr "Der gesamte <a %(a_code)s>Code</a> und alle <a %(a_datasets)s>Daten</a> sind quelloffen."

msgid "page.home.preservation.header"
msgstr "Bewahrung"

msgid "page.home.preservation.text1"
msgstr "Wir bewahren Bücher, wissenschaftliche Aufsätze, Comics, Zeitschriften und vieles mehr, indem wir diese Materialien aus verschiedenen <a href=\"https://de.wikipedia.org/wiki/Schattenbibliothek\">Schattenbibliotheken</a>, offiziellen Bibliotheken und anderen Sammlungen an einem Ort zusammenführen. All diese Daten werden für immer erhalten bleiben, weil wir es einfach machen, sie in großen Mengen zu vervielfältigen: nämlich mit Hilfe von Torrents, was zu vielen Kopien auf der ganzen Welt führt. Einige Schattenbibliotheken tun dies bereits selbst (z. B. Sci-Hub, Library Genesis), während Annas Archiv andere Bibliotheken \"befreit\", die keine Massenverteilung anbieten (z. B. Z-Library) oder gar keine Schattenbibliotheken sind (z. B. Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Diese weitläufige Verbreitung in Kombination mit Open-Source-Code macht unsere Website unempfindlich gegen Takedowns und gewährleistet die langfristige Bewahrung des Wissens und der Kultur der Menschheit. Erfahre mehr über <a href=\"/datasets\">unsere Datensätze</a>."

msgid "page.home.preservation.label"
msgstr "Wir schätzen, dass wir den Fortbestand von ungefähr <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% der Bücher auf der Welt</a> sichern."

msgid "page.home.access.header"
msgstr "Bereitstellung"

msgid "page.home.access.text"
msgstr "Wir arbeiten mit Partnern zusammen, um unsere Sammlung für jedermann einfach und kostenlos zugänglich zu machen. Wir sind der Überzeugung, dass jeder das Recht hat, auf das Wissen der Menschheit zuzugreifen. <a %(a_search)s>Und das sollte nicht auf Kosten der Autoren gehen</a>."

msgid "page.home.access.label"
msgstr "Stündliche Downloads in den letzten 30 Tagen. Stündlicher Durchschnitt: %(hourly)s. Tagesdurchschnitt: %(daily)s."

msgid "page.about.text2"
msgstr "Wir glauben fest an Informationsfreiheit und die Bewahrung von Wissen und Kultur. Mit dieser Suchmaschine bauen wir auf den Schultern von Riesen auf. Wir respektieren die harte Arbeit der Menschen zutiefst, welche die verschiedenen Schattenbibliotheken erstellt haben und wir hoffen, dass diese Suchmaschine ihre Reichweite noch vergrößert."

msgid "page.about.text3"
msgstr "Folge Anna auf <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Für Fragen und Rückmeldungen wende dich bitte via %(email)s an Anna."

msgid "page.faq.help.title"
msgstr "Wie kann ich helfen?"

msgid "page.about.help.text"
msgstr "<li>1. Folge uns auf <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Verbreite die Nachricht von Annas Archiv auf Twitter, Reddit, Tiktok, Instagram, in deinem örtlichen Café, in Bibliotheken oder wohin du auch gehst! Wir glauben nicht an Gatekeeping – wenn wir abgeschaltet werden, tauchen wir einfach woanders wieder auf, da unser gesamter Code und unsere Daten vollständig quelloffen sind.</li><li>3. Wenn du dazu in der Lage bist, ziehe eine <a href=\"/donate\">Spende</a> in Betracht.</li><li>4. Hilf mit, unsere Website in <a href=\"https://translate.annas-software.org/\">verschiedene Sprachen zu übersetzen</a>.</li><li>5. Wenn du ein Softwareentwickler bist, erwäge zu unserer <a href=\"https://annas-software.org/\">Open Source Software</a> beizutragen oder unsere <a href=\"https://en.wikipedia.org/wiki/Pirate_Library_Mirror\">Torrents und IPFS</a> zu teilen.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "Wir haben jetzt auch einen synchronisierten Matrix-Kanal unter %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Wenn du Security-Forscher bist, können wir deine Fähigkeiten für Angriffe und Verteidigung gebrauchen. Sieh dir dazu die Seite <a %(a_security)s>Sicherheit</a> an."

msgid "page.about.help.text7"
msgstr "7. Wir suchen Experten für Zahlungen an anonyme Händler. Kannst du uns auch dabei helfen mehr Spendenmöglichkeiten zu integrieren (z.B. PayPal, WeChat, Geschenkkarten)? Wenn du jemanden kennst, kontaktiere uns."

msgid "page.about.help.text8"
msgstr "8. Wir suchen immer nach mehr Serverkapazitäten."

msgid "page.about.help.text9"
msgstr "9. Du kannst dabei helfen Dateiprobleme zu melden, Kommentare zu verfassen und Listen hier direkt auf der Website erstellen. Du kannst auch weitere <a %(a_upload)s>Bücher hochladen</a> oder beim Formatieren der Bücher helfen."

msgid "page.about.help.text10"
msgstr "10. Hilf dabei die Wikipediaseite für Annas Archiv in deiner Sprache zu erstellen bzw. zu übersetzen."

msgid "page.about.help.text11"
msgstr "11. Wir würden gerne kleine, geschmackvolle Anzeigen auf der Seite platzieren. Wenn du daran interessiert bist hier zu werben, lass es uns wissen."

msgid "page.faq.help.mirrors"
msgstr "Wir würden die Erstellung von <a %(a_mirrors)s>Mirrorservern</a> sehr begrüßen und würden dies auch finanziell unterstützen."

msgid "page.about.help.volunteer"
msgstr "Für ausführlichere Informationen darüber, wie du freiwillig mithelfen könntest, besuche unsere <a %(a_volunteering)s>Seite für Freiwilligenarbeit & Prämien</a>."

msgid "page.faq.slow.title"
msgstr "Warum sind die langsamen Downloads so langsam?"

msgid "page.faq.slow.text1"
msgstr "Wir haben buchstäblich nicht genug Ressourcen, um jedem auf der Welt Highspeed-Downloads zu ermöglichen, so gerne wir auch würden. Wenn ein reicher Wohltäter sich engagieren und uns dies zur Verfügung stellen möchte, wäre das unglaublich, aber bis dahin versuchen wir unser Bestes. Wir sind ein gemeinnütziges Projekt, das sich mit Spenden kaum selbst versorgen kann."

msgid "page.faq.slow.text2"
msgstr "Aus diesem Grund haben wir mit unseren Partnern zwei Systeme für kostenlose Downloads eingeführt: gemeinsam genutzte Server mit langsamen Downloads und etwas schnellere Server mit einer Warteliste (um die Anzahl der gleichzeitig herunterladenden Personen zu reduzieren)."

msgid "page.faq.slow.text3"
msgstr "Wir haben auch eine <a %(a_verification)s>Browser-Verifizierung</a> für unsere langsamen Downloads, da sie sonst von Bots und Scrapern missbraucht werden, was es für legitime Benutzer noch langsamer machen würde."

msgid "page.faq.slow.text4"
msgstr "Beachte, dass beim Verwenden des Tor-Browsers möglicherweise deine Sicherheitseinstellungen angepasst werden müssen. Bei der niedrigsten Option, genannt „Standard“, gelingt die Cloudflare-Turnstile-Herausforderung. Bei den höheren Optionen, genannt „Sicherer“ und „Am sichersten“, schlägt die Herausforderung fehl."

msgid "page.faq.slow.text5"
msgstr "Für große Dateien können langsame Downloads manchmal mitten im Prozess abbrechen. Wir empfehlen die Verwendung eines Download-Managers (wie JDownloader), um große Downloads automatisch fortzusetzen."

msgid "page.donate.faq.title"
msgstr "Spenden FAQ"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Wird die Mitgliedschaft automatisch erneuert?</div> Mitgliedschaften <strong>werden nicht</strong> automatisch erneuert. Du kannst für so lange beitreten wie du willst."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kann ich meine Mitgliedschaft upgraden oder mehrere Mitgliedschaften erwerben?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Habt ihr auch andere Zahlungsmethoden?</div> Momentan nicht. Viele Menschen wollen nicht, dass Archive wie dieses existieren, darum müssen wir sehr vorsichtig sein. Falls du uns helfen kannst andere (bequemere) Zahlungsmethoden sicher bereitzustellen, melde dich bitte bei uns unter %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Was bedeuten die Kostenspannen, die pro Monat angegeben werden?</div> Du kannst den unteren Kostenbereich erreichen, indem du alle Rabatte anwendest, z. B. indem du einen Zeitraum wählst, der länger als ein Monat dauert."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Wofür werden die Spenden verwendet?</div> 100%% wird zur Bewahrung und Bereitstellung des Wissens und der Kultur der Menschheit verwendet. Momentan werden die Spenden hauptsächlich für Server, Speicher und Bandbreite verwendet. Es fließt kein Geld zu den Teammitgliedern persönlich. Das wäre ohnehin zu gefährlich."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kann ich große Beträge spenden?</div> Natürlich, und wir würden uns sehr darüber freuen! Für Spenden über ein paar Tausend Dollar, melde dich bitte direkt bei uns unter %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kann ich spenden, ohne Mitglied zu werden?</div> Na klar. Wir akzeptieren Spenden in beliebiger Höhe an diese Monero (XMR) Adresse: %(address)s."

msgid "page.faq.upload.title"
msgstr "Wie kann ich neue Bücher hochladen?"

msgid "page.upload.zlib.text1"
msgstr "Alternativ kannst du sie auch in die Z-Library <a %(a_upload)s>hier</a> hochladen."

msgid "page.upload.upload_to_both"
msgstr "Für kleine Uploads (bis zu 10.000 Dateien) laden diese bitte sowohl auf %(first)s als auch auf %(second)s hoch."

msgid "page.upload.text1"
msgstr "Derzeit schlagen wir vor, neue Bücher bei den Library Genesis Forks hochzuladen. Hier ist eine <a %(a_guide)s>nützliche Anleitung</a>. Beachte, dass beide Forks die wir auf dieser Webseite indexieren Daten vom selben Upload System beziehen."

msgid "page.upload.libgenli_login_instructions"
msgstr "Für Libgen.li stelle sicher, dass du dich zuerst im <a %(a_forum)s>entsprechendem Forum</a> mit dem Benutzernamen %(username)s und dem Passwort %(password)s anmeldest und dann zur <a %(a_upload_page)s>Upload-Seite</a> zurückkehrst."

msgid "common.libgen.email"
msgstr "Falls deine E-Mail-Adresse auf den Libgen Foren nicht funktioniert, schlagen wir vor eine E-Mail-Adresse von <a %(a_mail)s>Proton</a> (kostenlos) zu verwenden. Du kannst auch die <a %(a_manual)s>manuelle Freischaltung</a> deines Accounts beantragen."

msgid "page.faq.mhut_upload"
msgstr "Beachten, dass mhut.org bestimmte IP-Bereiche blockiert, sodass möglicherweise ein VPN erforderlich ist."

msgid "page.upload.large.text"
msgstr "Für große Uploads (über 10.000 Dateien), welche nicht von Libgen oder Z-Library akzeptiert werden, schreib uns bitte an %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Lädst du wissenschaftliche Arbeiten hoch, dann lade sie bitte auch (zusätzlich zu Library Genesis) auf <a %(a_stc_nexus)s>STC Nexus</a> hoch. Sie ist die beste Schattenbibliothek für neue Arbeiten. Wir haben sie noch nicht integriert, aber wir werden sie irgendwann einbinden. Du kannst den <a %(a_telegram)s>Upload-Bot</a> auf Telegram verwenden oder dich an die Adresse wenden, die in der angehefteten Nachricht aufgeführt ist, falls du zu viele Dateien auf diese Weise hochladen musst."

msgid "page.faq.request.title"
msgstr "Wie kann ich Bücher anfragen?"

msgid "page.request.cannot_accomodate"
msgstr "Zu diesem Zeitpunkt können wir keine Buchanfragen erfüllen."

msgid "page.request.forums"
msgstr "Bitte stelle deine Anfragen auf Z-Library oder den Libgen-Foren."

msgid "page.request.dont_email"
msgstr "Sende uns keine Buchanfragen per E-Mail."

msgid "page.faq.metadata.title"
msgstr "Sammelt ihr Metadaten?"

msgid "page.faq.metadata.indeed"
msgstr "Wir jedenfalls schon."

msgid "page.faq.1984.title"
msgstr "Ich habe 1984 von George Orwell heruntergeladen, wird die Polizei nun vor meiner Tür stehen?"

msgid "page.faq.1984.text"
msgstr "Mach dir keine Sorgen, es gibt viele Leute, die von derartigen Webseiten Daten herunterladen und ist es äußerst selten, dass man in Schwierigkeiten gerät. Um jedoch sicher zu gehen, empfehlen wir die Verwendung eines VPN (kostenpflichtig) oder <a %(a_tor)s>Tor</a> (kostenlos)."

msgid "page.faq.save_search.title"
msgstr "Wie speichere ich meine Sucheinstellungen?"

msgid "page.faq.save_search.text1"
msgstr "Wähle die gewünschten Einstellungen aus, lass das Suchfeld leer, klicke auf „Suchen“ und setze dann ein Lesezeichen für die Seite mit der Lesezeichenfunktion deines Browsers."

msgid "page.faq.mobile.title"
msgstr "Habt ihr eine mobile App?"

msgid "page.faq.mobile.text1"
msgstr "Wir haben keine offizielle mobile App, aber du kannst diese Website als App installieren."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klicke auf das Drei-Punkte-Menü oben rechts und wähle „Zum Startbildschirm hinzufügen“."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klicke unten auf die Schaltfläche „Teilen“ und wähle „Zum Startbildschirm hinzufügen“."

msgid "page.faq.api.title"
msgstr "Habt ihr eine API?"

msgid "page.faq.api.text1"
msgstr "Wir haben eine stabile JSON-API für Mitglieder, um eine schnelle Download-URL zu erhalten: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (Dokumentation innerhalb der JSON selbst)."

msgid "page.faq.api.text2"
msgstr "Für andere Anwendungsfälle, wie z. B. das Durchlaufen all unserer Dateien, das Erstellen einer benutzerdefinierten Suche usw., empfehlen wir unsere ElasticSearch- und MariaDB-Datenbanken neu zu <a %(a_generate)s>generieren</a> oder <a %(a_download)s>runterzuladen</a>. Die Rohdaten können manuell als <a %(a_explore)s>JSON-Dateien durchsucht werden</a>."

msgid "page.faq.api.text3"
msgstr "Unsere Rohdaten Torrent-Liste kann auch als <a %(a_torrents)s>JSON</a> heruntergeladen werden."

msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

msgid "page.faq.torrents.q1"
msgstr "Ich würde gerne beim Seeden helfen, habe aber nicht viel Speicherplatz."

msgid "page.faq.torrents.a1"
msgstr "Verwende den <a %(a_list)s>Torrent-Listengenerator</a>, um im Rahmen deiner Speicherkapazitäten eine Liste von Torrents zu erstellen, die am dringendsten ein Torrenting benötigen."

msgid "page.faq.torrents.q2"
msgstr "Die Torrents sind zu langsam; Kann ich die Daten direkt bei euch herunterladen?"

msgid "page.faq.torrents.a2"
msgstr "Ja, siehe dazu die <a %(a_llm)s>LLM-Daten</a> Seite."

msgid "page.faq.torrents.q3"
msgstr "Kann ich nur eine Teilmenge der Daten herunterladen, z. B. nur eine bestimmte Sprache oder ein bestimmtes Thema?"

msgid "page.faq.torrents.a3_short_answer"
msgstr "Kurze Antwort: nicht einfach."

msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Lange Antwort:"

msgid "page.faq.torrents.a3"
msgstr "Die meisten Torrents enthalten die Dateien direkt, was bedeutet, dass du die Torrent-Clients dazu anweisen kannst, nur die erforderlichen Daten herunterzuladen. Um festzulegen, welche Daten heruntergeladen werden sollen, kannst du unsere Metadaten neu <a %(a_generate)s>generieren</a> oder unsere ElasticSearch- und MariaDB-Datenbanken <a%(a_download)s>herunterladen</a>. Leider enthalten einige Torrent-Sammlungen .zip oder .tar Dateien im Stammverzeichnis, in diesem Fall musst du den gesamten Torrent herunterladen, bevor du einzelne Daten auswählen kannst."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Wir haben allerdings <a %(a_ideas)s>einige Ideen</a> für den letzteren Fall.)"

msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Es sind noch keine einfach zu bedienenden Werkzeuge zum Filtern von Torrents verfügbar, aber wir freuen uns über Beiträge."

msgid "page.faq.torrents.q4"
msgstr "Wie geht ihr mit Duplikaten in den Torrents um?"

msgid "page.faq.torrents.a4"
msgstr "Wir versuchen, minimale Duplizierungen oder Überschneidungen zwischen den Torrents in dieser Liste zu vermeiden, aber dies kann nicht immer erreicht werden und hängt stark von den Richtlinien der Quellbibliotheken ab. Für Bibliotheken, die ihre eigenen Torrents veröffentlichen, liegt es nicht in unserer Hand. Bei Torrents, die von Annas Archiv veröffentlicht wurden, deduplizieren wir nur auf der Grundlage des MD5-Hashes, was bedeutet, dass verschiedene Versionen desselben Buches nicht dedupliziert werden."

msgid "page.faq.torrents.q5"
msgstr "Kann ich die Torrent-Liste als JSON erhalten?"

msgid "page.faq.torrents.a5"
msgstr "Ja."

msgid "page.faq.torrents.q6"
msgstr "Ich sehe keine PDFs oder EPUBs in den Torrents, sondern nur Binärdateien? Was soll ich tun?"

msgid "page.faq.torrents.a6"
msgstr "Dies sind eigentlich PDFs und EPUBs, sie haben nur keine Erweiterung in vielen unserer Torrents. Es gibt zwei Stellen, an denen du die Metadaten für Torrent-Dateien finden kannst, einschließlich der Dateitypen/-erweiterungen:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Jede Sammlung oder Veröffentlichung hat ihre eigenen Metadaten. Zum Beispiel haben <a %(a_libgen_nonfic)s>Libgen.rs Torrents</a> eine entsprechende Metadatendatenbank, die auf der Libgen.rs-Website gehostet wird. In der Regel verlinken wir auf relevante Metadatenressourcen von der <a %(a_datasets)s>Datensatz-Seite</a> jeder Sammlung."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Wir empfehlen unsere ElasticSearch- und MariaDB-Datenbanken neu zu <a %(a_generate)s>generieren</a> oder <a %(a_download)s>runterzuladen</a>. Diese enthalten eine Zuordnung für jeden Datensatz in Annas Archiv zu den entsprechenden Torrent-Dateien (falls verfügbar) unter \"torrent_paths\" im ElasticSearch-JSON."

msgid "page.faq.torrents.q7"
msgstr "Warum kann mein Torrent-Client einige eurer Torrent-Dateien / Magnet-Links nicht öffnen?"

msgid "page.faq.torrents.a7"
msgstr "Einige Torrent-Clients unterstützen keine großen Stückgrößen, die viele unserer Torrents haben (bei neueren machen wir das nicht mehr – obwohl es laut Spezifikationen gültig ist!). Versuche es mit einem anderen Client, wenn du auf dieses Problem stößt, oder beschwere dich bei den Entwicklern deines Torrent-Clients."

msgid "page.faq.security.title"
msgstr "Habt ihr ein Programm zur verantwortungsvollen Offenlegung?"

msgid "page.faq.security.text1"
msgstr "Wir begrüßen Sicherheitsforscher, die nach Schwachstellen in unseren Systemen suchen. Wir sind große Befürworter einer verantwortungsvollen Offenlegung. Kontaktiere uns bitte <a %(a_contact)s>hier</a>."

msgid "page.faq.security.text2"
msgstr "Wir sind derzeit nicht in der Lage, Bug Bounties zu vergeben. Einzige Ausnahme sind Schwachstellen, die das <a %(a_link)s >Potenzial haben, unsere Anonymität zu gefährden</a>, für die wir Prämien im Bereich von 10.000 bis 50.000 US-Dollar anbieten. Wir möchten in Zukunft jedenfalls mehr Spielraum für Bug Bounties anbieten! Bitte beachte, dass Social-Engineering-Angriffe nicht in den Geltungsbereich fallen."

msgid "page.faq.security.text3"
msgstr "Wenn du dich für offensive Sicherheit interessierst und dabei helfen möchtest, das Wissen und die Kultur der Welt zu archivieren, kontaktiere uns bitte. Es gibt viele Möglichkeiten, wie du helfen kannst."

msgid "page.faq.resources.title"
msgstr "Gibt es noch andere Ressourcen über und von Annas Archiv?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Annas Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmäßige Neuigkeiten"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Annas Software</a> — unser Open-Source-Code"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Translate auf Annas Software</a> — unser Übersetzungssystem"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datensätze</a> — Näheres zu unseren Daten"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — Alternativ-Domains"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mehr über uns (hilf bitte dabei, den Wikipediaartikel aktuell zu halten oder einen neuen in deiner Sprache zu erstellen!)"

msgid "page.faq.copyright.title"
msgstr "Wie melde ich Urheberrechtsverletzungen?"

msgid "page.faq.copyright.text1"
msgstr "Wir hosten hier keine urheberrechtlich geschützten Materialien. Wir sind eine Suchmaschine und indizieren als solche nur Metadaten, die bereits öffentlich zugänglich sind. Wenn du von diesen externen Quellen Materialien herunterlädst, empfehlen wir dir, die Gesetze in deiner Gerichtsbarkeit in Bezug auf das Erlaubte zu überprüfen. Wir sind nicht verantwortlich für Inhalte, die von anderen gehostet werden."

msgid "page.faq.copyright.text2"
msgstr "Wenn du Beschwerden über etwas hast, was du hier siehst, dann wende dich am besten an die ursprüngliche Webseite. Wir ziehen ihre Änderungen regelmäßig in unsere Datenbank. Wenn du wirklich der Meinung bist, dass du eine gültige DMCA-Beschwerde verfügst, auf die wir antworten sollten, fülle bitte das <a %(a_copyright)s>DMCA- / Urherberrechtsanspruchs-Formular</a> aus. Wir nehmen deine Beschwerden ernst und werden uns so schnell wie möglich bei dir melden."

msgid "page.faq.hate.title"
msgstr "Ich finde es nicht gut, wie ihr das Projekt betreibt!"

msgid "page.faq.hate.text1"
msgstr "Wir möchten alle daran erinnern, dass unser gesamter Code und unsere Daten vollständig Open Source sind. Das ist einzigartig für Projekte wie unseres – uns ist kein anderes Projekt mit einem ähnlich umfangreichen Katalog bekannt, das ebenfalls vollständig quelloffen ist. Wir würden es begrüßen, wenn jeder, der der Meinung ist, dass wir unser Projekt schlecht betreiben, unseren Code und unsere Daten nutzt, um seine eigene Schattenbibliothek einzurichten! Wir sagen das nicht aus Trotz oder so – wir glauben wirklich, dass es großartig wäre, da es die Messlatte für alle höher legen und das Vermächtnis der Menschheit besser bewahren würde."

msgid "page.faq.uptime.title"
msgstr "Habt ihr einen Uptime-Monitor?"

msgid "page.faq.uptime.text1"
msgstr "Bitte sieh dir dazu <a %(a_href)s>dieses ausgezeichnete Projekt</a> an."

msgid "page.faq.physical.title"
msgstr "Wie spende ich Bücher oder andere physische Materialien?"

msgid "page.faq.physical.text1"
msgstr "Bitte sende sie an das <a %(a_archive)s>Internet Archive</a>. Sie werden dort ordnungsgemäß aufbewahrt."

msgid "page.faq.anna.title"
msgstr "Wer ist Anna?"

msgid "page.faq.anna.text1"
msgstr "Ihr alle seid Anna!"

msgid "page.faq.favorite.title"
msgstr "Was sind eure Lieblingsbücher?"

msgid "page.faq.favorite.text1"
msgstr "Hier sind einige Bücher, die für die Welt der Schattenbibliotheken und der digitalen Langzeitarchivierung eine besondere Bedeutung spielen:"

msgid "page.fast_downloads.no_more_new"
msgstr "Schnelle Downloads sind für heute aufgebraucht."

msgid "page.fast_downloads.no_member"
msgstr "Werde eine Mitglied um schnelle Downloads zu verwenden."

msgid "page.fast_downloads.no_member_2"
msgstr "Wir unterstützen jetzt Amazon-Geschenkkarten, Kredit- und Debitkarten, Krypto, Alipay und WeChat."

msgid "page.home.full_database.header"
msgstr "Vollständige Datenbank"

msgid "page.home.full_database.subtitle"
msgstr "Bücher, wissenschaftliche Aufsätze, Zeitschriften, Comics, Bibliotheksdatensätze, Metadaten, …"

msgid "page.home.full_database.search"
msgstr "Suchen"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub hat das Hochladen neuer Aufsätze <a %(a_paused)s>pausiert</a>."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB ist die Fortführung von Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Direkter Zugriff auf %(count)s wissenschaftliche Aufsätze"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Anzeigen"

msgid "page.home.scidb.browser_verification"
msgstr "Wenn du ein <a %(a_member)s>Mitglied</a> bist, ist keine Browserüberprüfung erforderlich."

msgid "page.home.archive.header"
msgstr "Langzeitarchiv"

msgid "page.home.archive.body"
msgstr "Die in Annas Archiv verwendeten Datensätze sind völlig frei zugänglich und können mithilfe von Torrents in großen Mengen gespiegelt werden. <a %(a_datasets)s>Mehr Erfahren…</a>"

msgid "page.home.torrents.body"
msgstr "Du wärst uns eine große Unterstützung, wenn du dabei hilfst Torrents zu seeden. <a %(a_torrents)s>Mehr Erfahren…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s Seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s Seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s Seeders"

msgid "page.home.llm.header"
msgstr "LLM-Trainingsdaten"

msgid "page.home.llm.body"
msgstr "Wir verfügen über die weltweit größte Sammlung hochwertiger Textdaten. <a %(a_llm)s>Mehr Erfahren…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: Freiwillige gesucht"

msgid "page.home.volunteering.header"
msgstr "🤝 Auf der Suche nach Freiwilligen"

msgid "page.home.volunteering.help_out"
msgstr "Als gemeinnütziges Open-Source-Projekt sind wir immer auf der Suche nach Menschen, die uns helfen."

msgid "page.home.payment_processor.body"
msgstr "Wenn du eine anonyme Hochrisiko-Zahlungsabwicklung betreibst, kontaktiere uns bitte. Wir suchen auch nach Leuten, die geschmackvolle kleine Anzeigen platzieren möchten. Alle Einnahmen dienen unseren Bemühungen zur weiteren Bewahrung."

msgid "layout.index.header.nav.annasblog"
msgstr "Annas Blog ↗"

msgid "page.ipfs_downloads.title"
msgstr "IPFS Downloads"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Alle Downloadlinks für diese Datei</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS-Gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(mit IPFS musst du es möglicherweise mehrmals versuchen)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Um schnellere Downloads zu erhalten und die Browserprüfungen zu umgehen, <a %(a_membership)s>werde Mitglied</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Informationen zur Massenspiegelung unserer Sammlung findest du auf den Seiten <a %(a_datasets)s>Datensätze</a> und <a %(a_torrents)s>Torrents</a>."

msgid "page.llm.title"
msgstr "LLM-Daten"

msgid "page.llm.intro"
msgstr "Es ist allgemein bekannt, dass LLMs von hochwertigem Datenmaterial profitieren. Wir haben die größte Sammlung von Büchern, wissenschaftlichen Aufsätzen, Zeitschriften usw. der Welt, die einige der qualitativ hochwertigsten Textquellen darstellen."

msgid "page.llm.unique_scale"
msgstr "Einzigartige Größe und Vielfalt"

msgid "page.llm.unique_scale.text1"
msgstr "Unsere Sammlung enthält über hundert Millionen Dateien, darunter wissenschaftliche Zeitschriften, Lehrbücher und Zeitschriften. Wir erreichen diese Größe, indem wir große bestehende Repositories kombinieren."

msgid "page.llm.unique_scale.text2"
msgstr "Einige unserer Quellsammlungen sind bereits in großen Mengen verfügbar (Sci-Hub und Teile von Libgen). Andere Quellen haben wir selbst befreit. <a %(a_datasets)s>Datensätze</a> zeigt eine vollständige Übersicht."

msgid "page.llm.unique_scale.text3"
msgstr "Unsere Sammlung umfasst Millionen von Büchern, wissenschaftlichen Aufsätzen und Zeitschriften aus der Zeit vor dem E-Book-Zeitalter. Große Teile dieser Sammlung wurden bereits OCR-erfasst und weisen bereits wenig interne Überschneidungen auf."

msgid "page.llm.how_we_can_help"
msgstr "Wie wir helfen können"

msgid "page.llm.how_we_can_help.text1"
msgstr "Wir können einen Highspeed-Zugang zu unseren vollständigen Sammlungen sowie zu unveröffentlichten Sammlungen anbieten."

msgid "page.llm.how_we_can_help.text2"
msgstr "Dies ist ein Zugang auf Unternehmensebene, den wir für Spenden im Bereich von Zehntausenden USD bereitstellen können. Wir sind auch bereit, dies gegen hochwertige Sammlungen zu tauschen, die wir noch nicht haben."

msgid "page.llm.how_we_can_help.text3"
msgstr "Wir können dir eine Preiserstattung der Kosten anbieten, wenn du dafür unsere Datenbank weiter bereichern und verbessern würdest. Dazu gehören unter anderem:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Entfernung von Überschneidungen (Deduplikation)"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Text- und Metadatenextraktion"

msgid "page.llm.how_we_can_help.text4"
msgstr "Unterstütze die langfristige Archivierung menschlichen Wissens und erhalte gleichzeitig bessere Daten für dein Modell!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontaktiere uns</a>, um zu besprechen, wie wir zusammenarbeiten können."

msgid "page.login.continue"
msgstr "Fortfahren"

msgid "page.login.please"
msgstr "Bitte <a %(a_account)s>anmelden</a>, um diese Seite anzuzeigen.</a>"

msgid "page.maintenance.header"
msgstr "Annas Archiv ist kurzfristig aufgrund Wartungsarbeiten nicht erreichbar. Schau in einer Stunde wieder vorbei."

msgid "page.metadata.header"
msgstr "Metadaten verbessern"

msgid "page.metadata.body1"
msgstr "Du kannst zur Erhaltung von Büchern beitragen, indem du Metadaten verbesserst! Lies zunächst die Hintergrundinfos zu Metadaten auf Annas Archiv und lerne wie du Metadaten durch Verlinkung mit der Open Library verbessern kannst. Dafür erhältst du eine kostenlose Mitgliedschaft für Annas Archiv."

msgid "page.metadata.background.title"
msgstr "Hintergrund"

msgid "page.metadata.background.body1"
msgstr "Wenn du ein Buch auf Annas Archiv ansiehst, kannst du verschiedene Felder sehen: Titel, Autor, Verlag, Ausgabe, Jahr, Beschreibung, Dateiname und mehr. All diese Informationen werden als <em>Metadaten</em> bezeichnet."

msgid "page.metadata.background.body2"
msgstr "Da wir Bücher aus verschiedenen <em>Ursprungsbibliotheken</em> kombinieren, zeigen wir die Metadaten an, die in der jeweiligen Quellenbibliothek verfügbar sind. Zum Beispiel zeigen wir für ein Buch, das wir von Library Genesis haben, den Titel aus der Datenbank von Library Genesis an."

msgid "page.metadata.background.body3"
msgstr "Manchmal ist ein Buch in <em>mehreren</em> Ursprungsbibliotheken vorhanden, die unterschiedliche Metadatenfelder haben könnten. In diesem Fall zeigen wir einfach die längste Version jedes Feldes an, da diese hoffentlich die nützlichsten Informationen enthält! Wir zeigen die anderen Felder weiterhin unter der Beschreibung an, z.B. als „alternativer Titel“ (aber nur, wenn sie unterschiedlich sind)."

msgid "page.metadata.background.body4"
msgstr "Wir extrahieren auch <em>Codes</em> wie Identifikatoren und Klassifikatoren aus der Urprungsbibliothek. <em>Identifikatoren</em> repräsentieren eindeutig eine bestimmte Ausgabe eines Buches; Beispiele sind ISBN, DOI, Open Library ID, Google Books ID oder Amazon ID. <em>Klassifikatoren</em> gruppieren mehrere ähnliche Bücher; Beispiele sind Dewey Decimal (DCC), UDC, LCC, RVK oder GOST. Manchmal sind diese Codes in den Ursprungsbibliotheken explizit verlinkt, und manchmal können wir sie aus dem Dateinamen oder der Beschreibung extrahieren (hauptsächlich ISBN und DOI)."

msgid "page.metadata.background.body5"
msgstr "Wir können Identifikatoren verwenden, um Datensätze in <em>reinen Metadatensammlungen</em> zu finden, wie z.B. OpenLibrary, ISBNdb oder WorldCat/OCLC. Es gibt einen speziellen <em>Metadaten-Tab</em> in unserer Suchmaschine, falls du diese Sammlungen durchsuchen möchtest. Wir verwenden übereinstimmende Datensätze, um fehlende Metadatenfelder zu ergänzen (z.B. wenn ein Titel fehlt) oder z.B. als „alternativer Titel“ (falls ein vorhandener Titel existiert)."

msgid "page.metadata.background.body6"
msgstr "Um genau zu sehen, woher die Metadaten eines Buches stammen, sieh dir den <em>„Technische Details“-Tab</em> auf einer Buchseite an. Er enthält einen Link zum Roh-JSON für dieses Buch, mit Verweisen auf das Roh-JSON der Originaldatensätze."

msgid "page.metadata.background.body7"
msgstr "Für weitere Informationen siehe die folgenden Seiten: <a %(a_datasets)s>Datensätze</a>, <a %(a_search_metadata)s>Suche (Metadaten-Tab)</a>, <a %(a_codes)s>Codes Explorer</a> und <a %(a_example)s>Beispiel-Metadaten-JSON</a>. Schließlich können alle unsere Metadaten als ElasticSearch- und MariaDB-Datenbanken <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>heruntergeladen</a> werden."

msgid "page.metadata.openlib.title"
msgstr "Open Library Verlinkung"

msgid "page.metadata.openlib.body1"
msgstr "Was solltest du also tun, wenn du eine Datei mit schlechten Metadaten findest? Du kannst dann zur Ursprungsbibliothek gehen und deren Verfahren zur Korrektur der Metadaten befolgen. Aber was tun, wenn eine Datei in mehreren Ursprungsbibliotheken vorhanden ist?"

msgid "page.metadata.openlib.body2"
msgstr "Es gibt einen Identifikator, der auf Annas Archiv besonders behandelt wird. <strong>Das annas_archive md5-Feld in der Open Library überschreibt immer alle anderen Metadaten!</strong> Lass uns zunächst einen Schritt zurückgehen und etwas über die Open Library lernen."

msgid "page.metadata.openlib.body3"
msgstr "Die Open Library wurde 2006 von Aaron Swartz mit dem Ziel gegründet, „eine Website für jedes jemals veröffentlichte Buch“ zu erstellen. Es ist eine Art Wikipedia für Buchmetadaten: Jeder kann sie bearbeiten, sie ist frei lizenziert und kann in großen Mengen heruntergeladen werden. Es ist eine Buchdatenbank, die am besten mit unserer Mission übereinstimmt – tatsächlich wurde Annas Archiv von Aaron Swartz' Vision und Leben inspiriert."

msgid "page.metadata.openlib.body4"
msgstr "Anstatt das Rad neu zu erfinden, haben wir beschlossen, unsere Freiwilligen zur Open Library zu leiten. Wenn du ein Buch mit falschen Metadaten siehst, kannst du auf folgende Weise helfen:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Gehe zur <a %(a_openlib)s>Open Library Webseite</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Finde den richtigen Buchdatensatz. <strong>WARNUNG:</strong> Stelle sicher, dass du die richtige <strong>Ausgabe</strong> auswählst. In der Open Library gibt es „Werke“ und „Ausgaben“."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Ein „Werk“ könnte „Harry Potter und der Stein der Weisen“ sein."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Eine „Ausgabe“ könnte sein:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Die Erstausgabe von 1997, veröffentlicht von Bloomsbery mit 256 Seiten."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Die Taschenbuchausgabe von 2003, veröffentlicht von Raincoast Books mit 223 Seiten."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Die polnische Übersetzung von 2000 „Harry Potter I Kamie Filozoficzn“ von Media Rodzina mit 328 Seiten."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Alle diese Ausgaben haben unterschiedliche ISBNs und unterschiedliche Inhalte, also stelle sicher, dass du die richtige auswählst!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Bearbeite den Datensatz (oder erstelle ihn, falls keiner existiert) und füge so viele nützliche Informationen wie möglich hinzu! Du bist jetzt sowieso schon hier, also erstell wirklich einen sorgfältigen Datensatz."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Wähle unter „ID-Nummern“ „Anna’s Archive“ und füge die MD5 des Buches aus Annas Archiv hinzu. Dies ist die lange Zeichenfolge aus Buchstaben und Zahlen nach „/md5/“ in der URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Versuche andere Dateien in Annas Archiv zu finden, die ebenfalls zu diesem Datensatz passen, und füge diese ebenfalls hinzu. In Zukunft können wir diese als Duplikate auf der Suchseite von Annas Archiv gruppieren."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Wenn du fertig bist, notiere die URL, die du gerade aktualisiert hast. Sobald du mindestens 30 Datensätze mit Annas Archiv MD5s aktualisiert hast, sende uns eine <a %(a_contact)s>E-Mail</a> und sende uns die Liste. Wir geben dir eine kostenlose Mitgliedschaft für Annas Archiv, damit du diese Arbeit leichter erledigen kannst (und als Dankeschön für deine Hilfe). Es müssen qualitativ hochwertige Bearbeitungen sein, die erhebliche Mengen an Informationen hinzufügen, andernfalls wird deine Anfrage abgelehnt. Deine Anfrage wird auch abgelehnt, wenn eine der Bearbeitungen von Open Library-Moderatoren zurückgesetzt oder korrigiert wird."

msgid "page.metadata.openlib.body5"
msgstr "Beachte, dass dies nur für Bücher gilt, nicht für wissenschaftliche Aufsätze oder andere Dateitypen. Für andere Dateitypen empfehlen wir weiterhin, die Ursprungsbibliothek zu finden. Es kann einige Wochen dauern, bis Änderungen in Annas Archiv aufgenommen werden, da wir den neuesten Open Library-Daten-Dump herunterladen und unseren Suchindex neu generieren müssen."

msgid "page.mirrors.title"
msgstr "Mirrors: Aufruf an Freiwillige"

msgid "page.mirrors.intro"
msgstr "Um die Widerstandsfähigkeit von Annas Archiv zu erhöhen, suchen wir Freiwillige, die Mirrorserver betreiben."

msgid "page.mirrors.text1"
msgstr "Wir suchen Folgendes:"

msgid "page.mirrors.list.run_anna"
msgstr "Du betreibst den Open-Source-Code von Annas Archiv und aktualisierst regelmäßig sowohl den Code als auch die Daten."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Deine Version ist klar als Mirror gekennzeichnet, z. B. „Bobs Archiv, eine Spiegelung von Annas Archiv“."

msgid "page.mirrors.list.know_the_risks"
msgstr "Du bist bereit, die mit dieser Arbeit verbundenen Risiken einzugehen, die erheblich sind. Du hast außerdem ein tiefes Verständnis für die erforderliche Betriebssicherheit. Der Inhalt <a %(a_shadow)s>dieser</a> <a %(a_pirate)s>Beiträge</a> ist dir selbsterklärend."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "Du bist bereit, zu unserem <a %(a_codebase)s>Codebase</a> beizutragen – in Zusammenarbeit mit unserem Team."

msgid "page.mirrors.list.maybe_partner"
msgstr "Anfangs werden wir dir keinen Zugang zu den Downloads unserer Partner-Server gewähren, aber wenn alles gut läuft, können wir ihn mit dir teilen."

msgid "page.mirrors.expenses.title"
msgstr "Hosting-Kosten"

msgid "page.mirrors.expenses.text1"
msgstr "Wir sind bereit, die Hosting- und VPN-Kosten zu übernehmen, zunächst bis zu 200 $ pro Monat. Dies ist ausreichend für einen grundlegenden Suchserver und einen DMCA-geschützten Proxy."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Wir werden erst für das Hosting bezahlen, wenn alles eingerichtet ist und du nachgewiesen hast, dass du das Archiv mit Updates auf dem neuesten Stand halten kannst. Das bedeutet, dass du die ersten 1-2 Monate aus eigener Tasche bezahlen musst."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Deine Zeit wird nicht entschädigt (und unsere auch nicht), da dies reine Freiwilligenarbeit ist."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Wenn du dich signifikant an der Entwicklung und den Aktivitäten unserer Arbeit beteiligst, können wir darüber sprechen, mehr von den Spendeneinnahmen mit dir zu teilen, damit du diese nach Bedarf einsetzen kannst."

msgid "page.mirrors.getting_started.title"
msgstr "Erste Schritte"

msgid "page.mirrors.getting_started.text1"
msgstr "Bitte <strong>kontaktiere uns nicht</strong>, um um Erlaubnis zu bitten oder grundlegende Fragen zu stellen. Taten sagen mehr als Worte! Alle Informationen sind verfügbar, also leg einfach los und richte deinen Mirror ein."

msgid "page.mirrors.getting_started.text2"
msgstr "Sei so frei, Tickets oder Merge-Anfragen auf unserem Gitlab zu posten, wenn du auf Probleme stoßt. Wir müssen möglicherweise einige Mirror-spezifische Funktionen mit dir entwickeln, wie z.B. das Rebranding von „Annas Archiv“ auf den Namen deiner Website, (anfänglich) das Deaktivieren von Benutzerkonten oder das Verlinken zu unserer Hauptseite von Buchseiten."

msgid "page.mirrors.getting_started.text3"
msgstr "Sobald dein Mirror läuft, kontaktiere uns bitte. Wir würden gerne deine OpSec überprüfen, und sobald diese solide ist, werden wir auf deinen Mirror verlinken und enger mit dir zusammenarbeiten."

msgid "page.mirrors.getting_started.text4"
msgstr "Vielen Dank im Voraus an alle, die bereit sind, auf diese Weise beizutragen! Es ist nichts für schwache Nerven, aber es würde die Langlebigkeit der größten wirklich offenen Bibliothek in der Geschichte der Menschheit festigen."

msgid "page.partner_download.header"
msgstr "Download von einer Partner-Website"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Langsame Downloads sind nur verfügbar über die offizielle Website. Besuche %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Langsame Downloads sind nicht über Cloudflare-VPNs oder anderweitig über Cloudflare-IP-Adressen verfügbar."

msgid "page.partner_download.wait_banner"
msgstr "Bitte warte <span %(span_countdown)s>%(wait_seconds)s</span> Sekunden, um diese Datei herunterzuladen."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Jetzt herunterladen</a>"

msgid "page.partner_download.li4"
msgstr "Danke fürs Warten, so bleibt die Webseite für alle frei zugänglich! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Achtung: Es gab die letzten 24 Stunden viele Downloads von deiner IP-Adresse. Downloads könnten deshalb langsamer als üblich sein."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads von deiner IP-Adresse in den letzten 24 Stunden: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Wenn du ein VPN oder eine gemeinsam genutzte Internetverbindung verwendest oder dein Internetanbieter IP-Adressen teilt, kann diese Warnung darauf zurückzuführen sein."

msgid "page.partner_download.wait"
msgstr "Um jedem die Möglichkeit zu geben, Dateien kostenlos herunterzuladen, musst du etwas warten, bevor du diese Datei herunterladen kannst."

msgid "page.partner_download.li1"
msgstr "Fühl dich frei, Annas Archiv in einem anderen Tab weiter zu durchsuchen, während du wartest (sofern dein Browser das Aktualisieren von Hintergrund-Tabs unterstützt)."

msgid "page.partner_download.li2"
msgstr "Warte ruhig, bis mehrere Download-Seiten gleichzeitig geladen sind (aber bitte lade nur eine Datei gleichzeitig pro Server herunter)."

msgid "page.partner_download.li3"
msgstr "Sobald du einen Download-Link erhältst, ist er mehrere Stunden lang gültig."

msgid "layout.index.header.title"
msgstr "Annas Archiv"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Dateneintrag in Annas Archiv"

msgid "page.scidb.download"
msgstr "Download"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Um die Zugänglichkeit und langfristige Bewahrung menschlichen Wissens zu unterstützen, werde <a %(a_donate)s>Mitglied</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "Als Bonus 🧬 lädt&nbsp;SciDB für Mitglieder schneller und ohne Beschränkungen."

msgid "page.scidb.refresh"
msgstr "Funktioniert etwas nicht? Versuche die Seite <a %(a_refresh)s>neuzuladen</a>."

msgid "page.scidb.no_preview_new"
msgstr "Keine Vorschau verfügbar. Lade die Datei von <a %(a_path)s>Annas Archiv</a> herunter."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB ist eine Fortsetzung von Sci-Hub mit seiner vertrauten Benutzeroberfläche und der direkten Anzeige von PDFs. Gib einen DOI ein, um Aufsätze anzuzeigen."

msgid "page.home.scidb.text3"
msgstr "Wir haben die gesamte Sci-Hub-Kollektion sowie neue Aufsätze. Die meisten können direkt mit einer vertrauten Benutzeroberfläche angezeigt werden, ähnlich wie bei Sci-Hub. Einige können über externe Quellen heruntergeladen werden, in diesem Fall zeigen wir Links zu diesen."

msgid "page.search.title.results"
msgstr "%(search_input)s - Suche"

msgid "page.search.title.new"
msgstr "Neue Suche"

msgid "page.search.icon.include_only"
msgstr "Nur einschließen"

msgid "page.search.icon.exclude"
msgstr "Ausschließen"

msgid "page.search.icon.unchecked"
msgstr "Ungeprüft"

msgid "page.search.tabs.download"
msgstr "Download"

msgid "page.search.tabs.journals"
msgstr "wissenschaftliche Aufsätze"

msgid "page.search.tabs.digital_lending"
msgstr "Digitale Ausleihe"

msgid "page.search.tabs.metadata"
msgstr "Metadaten"

msgid "common.search.placeholder"
msgstr "Suche nach Titel, Autor, Sprache, Dateityp, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Suche"

msgid "page.search.search_settings"
msgstr "Suchoptionen"

msgid "page.search.submit"
msgstr "Suchen"

msgid "page.search.too_long_broad_query"
msgstr "Die Suche hat zu lange gedauert, das ist normal bei vagen Anfragen. Die Filterzahlen sind möglicherweise ungenau."

msgid "page.search.too_inaccurate"
msgstr "Die Suche hat zu lange gedauert, was bedeutet, dass du möglicherweise ungenaue Ergebnisse siehst. Manchmal hilft es, die Seite<a %(a_reload)s>neu zu laden</a>."

msgid "page.search.filters.display.header"
msgstr "Anzeigen"

msgid "page.search.filters.display.list"
msgstr "Liste"

msgid "page.search.filters.display.table"
msgstr "Tabelle"

msgid "page.search.advanced.header"
msgstr "Fortgeschritten"

msgid "page.search.advanced.description_comments"
msgstr "Suchbeschreibungen und Metadatenkommentare"

msgid "page.search.advanced.add_specific"
msgstr "Eigenes Suchfeld hinzufügen"

msgid "common.specific_search_fields.select"
msgstr "(nach bestimmtem Feld suchen)"

msgid "page.search.advanced.field.year_published"
msgstr "Publikationsjahr"

msgid "page.search.filters.content.header"
msgstr "Inhalt"

msgid "page.search.filters.filetype.header"
msgstr "Dateityp"

msgid "page.search.more"
msgstr "Mehr…"

msgid "page.search.filters.access.header"
msgstr "Zugang"

msgid "page.search.filters.source.header"
msgstr "Quelle"

msgid "page.search.filters.source.scraped"
msgstr "gescrapt und frei veröffentlicht von AA"

msgid "page.search.filters.language.header"
msgstr "Sprache"

msgid "page.search.filters.order_by.header"
msgstr "Sortieren nach"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Am relevantesten"

msgid "page.search.filters.sorting.newest"
msgstr "Neueste"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(Publikationsjahr)"

msgid "page.search.filters.sorting.oldest"
msgstr "Älteste"

msgid "page.search.filters.sorting.largest"
msgstr "Größte"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(Dateigröße)"

msgid "page.search.filters.sorting.smallest"
msgstr "Kleinste"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(frei veröffentlicht)"

msgid "page.search.filters.sorting.random"
msgstr "Zufällig"

msgid "page.search.header.update_info"
msgstr "Der Suchindex wird monatlich aktualisiert. Er umfasst derzeit Einträge bis zum %(last_data_refresh_date)s. Für weitere technische Informationen, siehe die Seite %(link_open_tag)sDatensätze</a>."

msgid "page.search.header.codes_explorer"
msgstr "Um den Suchindex nach Codes zu durchsuchen, verwende den <a %(a_href)s>Codes Explorer</a>."

msgid "page.search.results.search_downloads"
msgstr "Nutze das Textfeld, um unseren Katalog mit %(count)s direkt herunterladbaren Dateien zu durchsuchen, die wir <a %(a_preserve)s>für immer aufbewahren</a>."

msgid "page.search.results.help_preserve"
msgstr "Tatsächlich kann jeder dazu beitragen, diese Daten zu bewahren, indem er unsere <a %(a_torrents)s>Liste an Torrents</a> seedet."

msgid "page.search.results.most_comprehensive"
msgstr "Wir verfügen derzeit über den weltweit umfassendsten offenen Katalog an Büchern, wissenschaftlichen Aufsätzen und anderen schriftlichen Werken. Er enthält Werke von Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>und mehr</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Wenn du andere „Schattenbibliotheken“ kennst, die wir spiegeln sollten, oder wenn du Fragen haben solltest, kontaktiere uns bitte per %(email)s."

msgid "page.search.results.dmca"
msgstr "Für DMCA / Urheberrechtsansprüche <a %(a_copyright)s>klicke hier</a>."

msgid "page.search.results.shortcuts"
msgstr "Tipp: Verwende für eine schnellere Navigation die Tastenkombinationen „/“ (Suchfokus), „Enter“ (Suche), „j“ (nach oben), „k“ (nach unten)."

msgid "page.search.results.looking_for_papers"
msgstr "Suchst du nach wissenschaftlichen Aufsätzen?"

msgid "page.search.results.search_journals"
msgstr "Verwende das Suchfeld um unseren Katalog an %(count)s wissenschaftlichen Arbeiten und Aufsätzen zu durchsuchen, welche wir für immer <a %(a_preserve)s>bewahrt haben</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Nutze das Textfeld, um in digitalen Leihbibliotheken nach Dateien zu suchen."

msgid "page.search.results.digital_lending_info"
msgstr "Dieser Suchindex enthält derzeit Metadaten aus der Controlled-Digital-Lending-Bibliothek des Internet Archive. Mehr über unsere Datensätze <a %(a_datasets)s>hier</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Weitere digitale Leihbibliotheken findest du auf <a %(a_wikipedia)s>Wikipedia</a> und im <a %(a_mobileread)s>MobileRead-Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Nutze das Textfeld, um nach Metadaten aus Bibliotheken zu suchen. Dies kann nützlich sein, wenn du<a %(a_request)s>eine Datei anfragen willst</a>."

msgid "page.search.results.metadata_info"
msgstr "Dieser Suchindex umfasst derzeit Metadaten von ISBNdb und der Open Library. Mehr über unsere Datensätze <a %(a_datasets)s>hier</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Für Metadaten zeigen wir die Originaleinträge. Wir führen Einträge nicht zusammen."

msgid "page.search.results.metadata_info_more"
msgstr "Weltweit gibt es viele, viele Metadatenquellen für schriftliche Werke. <a %(a_wikipedia)s>Diese Wikipedia-Seite</a> ist ein guter Anfang, aber wenn du andere gute Listen kennst, lasse es uns bitte wissen."

msgid "page.search.results.search_generic"
msgstr "Nutze zum Suchen das Textfeld."

msgid "page.search.results.these_are_records"
msgstr "Dies sind Metadaten-Datensätze, <span %(classname)s>keine</span> herunterladbaren Dateien."

msgid "page.search.results.error.header"
msgstr "Fehler während der Suche."

msgid "page.search.results.error.unknown"
msgstr "Versuche <a %(a_reload)s>die Seite neu zu laden</a>. Wenn das Problem weiterhin besteht, sende uns bitte eine E-Mail an %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Keine Dateien gefunden.</span> Versuche weniger oder andere Suchbegriffe und Filter."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Manchmal passiert dies fälschlicherweise, wenn der Suchserver langsam ist. In solchen Fällen kann ein <a %(a_attrs)s>erneutes Laden</a> helfen."

msgid "page.search.found_matches.main"
msgstr "Wir haben Übereinstimmungen gefunden in: %(in)s. Du kannst auf die dort gefundene URL verweisen, wenn du eine Datei <a %(a_request)s>anfragst</a>."

msgid "page.search.found_matches.journals"
msgstr "wissenschaftliche Aufsätze (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Digitales Ausleihen (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadaten (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Ergebnis %(from)s-%(to)s (von %(total)s)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ Teilweise Übereinstimmung"

msgid "page.search.results.partial"
msgstr "%(num)d teilweise Übereinstimmungen"

msgid "page.volunteering.title"
msgstr "Freiwilligenarbeit & Prämien"

msgid "page.volunteering.intro.text1"
msgstr "Annas Archiv ist auf Freiwillige wie dich angewiesen. Wir begrüßen jedes Engagement und suchen vor allem Unterstützung in zwei Hauptkategorien:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Leichte Freiwilligenarbeit:</span> Wenn du nur ein paar Stunden hier und da erübrigen kannst, gibt es immer noch viele Möglichkeiten, wie du helfen könntest. Wir belohnen engagierte Freiwillige mit <span %(bold)s>🤝 Mitgliedschaften bei Annas Archiv</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Schwere Freiwilligenarbeit (USD$50-USD$5,000 Prämien):</span> Wenn du viel Zeit und/oder Ressourcen für unsere Mission aufwenden kannst, würden wir gerne enger mit dir zusammenarbeiten. Anschließend kannst du dem inneren Team beitreten. Obwohl wir ein knappes Budget haben, können wir <span %(bold)s>💰 monetäre Prämien</span> für die intensivste Arbeit vergeben."

msgid "page.volunteering.intro.text2"
msgstr "Wenn du keine Zeit für Freiwilligenarbeit hast, kannst du uns trotzdem sehr helfen, indem du<a %(a_donate)s>Geld spendest</a>, <a %(a_torrents)s>unsere Torrents seedest</a>, <a %(a_uploading)s>Bücher hochlädst</a> oder <a %(a_help)s>deinen Freunden von Annas Archiv erzählst</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Unternehmen:</span> Wir bieten Hochgeschwindigkeitszugriff auf unsere Sammlungen im Austausch gegen Unternehmensspenden oder den Austausch neuer Sammlungen (z.B. neue Scans, OCR-Datasets, Bereicherung unserer Daten). <a %(a_contact)s>Kontaktiere uns</a>, wenn dies auf dich zutrifft. Siehe auch unsere <a %(a_llm)s>LLM Seite</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Leichte Freiwilligenarbeit"

msgid "page.volunteering.section.light.text1"
msgstr "Wenn du ein paar Stunden Zeit hast, kannst du auf verschiedene Weise helfen. Trete unbedingt dem <a %(a_telegram)s>Freiwilligen-Chat auf Telegram</a> bei."

msgid "page.volunteering.section.light.text2"
msgstr "Als Zeichen der Wertschätzung vergeben wir in der Regel 6 Monate „Glücklicher Bibliothekar“ für grundlegende Milestones, und mehr für weitere Freiwilligenarbeit. Alle Milestones erfordern qualitativ hochwertige Arbeit – schlampige Arbeit schadet uns mehr, als sie hilft, und wir werden sie ablehnen. Bitte <a %(a_contact)s>maile uns</a>, wenn du einen Milestone erreicht hast."

msgid "page.volunteering.table.header.task"
msgstr "Aufgabe"

msgid "page.volunteering.table.header.milestone"
msgstr "Milestone"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Die Botschaft von Annas Archiv verbreiten. Zum Beispiel, indem du Bücher auf AA empfiehlst, auf unsere Blogbeiträge verlinkst oder allgemein Menschen auf unsere Website hinweist."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s Links oder Screenshots."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Diese sollten zeigen, dass du jemandem von Annas Archiv erzählt hast und er oder sie dir entsprechend dankt."

msgid "page.volunteering.table.open_library.task"
msgstr "Verbessere Metadaten durch <a %(a_metadata)s>Verlinkungen</a> mit der Open Library."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Du kannst die <a %(a_list)s>Liste zufälliger Metadatenprobleme</a> als Ausgangspunkt verwenden."

msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Stelle sicher, dass du einen Kommentar zu den von dir behobenen Problemen hinterlässt, damit andere deine Arbeit nicht duplizieren."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s Links zu Datensätzen, die du verbessert hast."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Übersetzen</a> der Website."

msgid "page.volunteering.table.translate.milestone"
msgstr "Eine Sprache vollständig übersetzen (wenn sie nicht schon fast fertig war)."

msgid "page.volunteering.table.wikipedia.task"
msgstr "Verbessere die Wikipedia-Seite von Annas Archiv in deiner Sprache. Füge Informationen von der Wikipedia-Seite von AA in anderen Sprachen sowie von unserer Website und unserem Blog hinzu. Setze außerdem Verweise auf AA auf anderen relevanten Seiten."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link zur Bearbeitungshistorie, die zeigt, dass du wesentliche Beiträge geleistet hast."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Erfülle Buch- (oder Zeitschriften- usw.) Anfragen in den Z-Library- oder Library Genesis-Foren. Wir haben kein eigenes Buchanfragesystem, aber wir spiegeln diese Bibliotheken, daher machst du auch Annas Archiv besser, wenn du diese verbesserst."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s Links oder Screenshots von erfüllten Anfragen."

msgid "page.volunteering.table.misc.task"
msgstr "Kleine Aufgaben, die in unserem <a %(a_telegram)s>Freiwilligen-Chat auf Telegram</a> gepostet werden. Normalerweise für Mitgliedschaften, manchmal für kleine Prämien."

msgid "page.volunteering.table.misc.task2"
msgstr "Kleine Aufgaben, die in unserer Freiwilligen-Chatgruppe gepostet werden."

msgid "page.volunteering.table.misc.milestone"
msgstr "Hängt von der Aufgabe ab."

msgid "page.volunteering.section.bounties.heading"
msgstr "Prämien"

msgid "page.volunteering.section.bounties.text1"
msgstr "Wir suchen immer nach Menschen mit soliden Programmier- oder offensiven Sicherheitsfähigkeiten, die sich engagieren möchten. Du kannst einen ernsthaften Beitrag zur Bewahrung des Erbes der Menschheit leisten."

msgid "page.volunteering.section.bounties.text2"
msgstr "Als Dankeschön vergeben wir Mitgliedschaften für solide Beiträge. Als großes Dankeschön vergeben wir Geldprämien für besonders wichtige und schwierige Aufgaben. Dies sollte nicht als Ersatz für einen Job betrachtet werden, aber es ist ein zusätzlicher Anreiz und kann bei anfallenden Kosten helfen."

msgid "page.volunteering.section.bounties.text3"
msgstr "Der Großteil unseres Codes ist Open Source, und wir werden auch von deinem Code verlangen, dass er Open Source ist, wenn wir die Prämie vergeben. Es gibt einige Ausnahmen, die wir individuell besprechen können."

msgid "page.volunteering.section.bounties.text4"
msgstr "Prämien werden an die erste Person vergeben, die eine Aufgabe abschließt. Kommentiere gerne ein Prämienticket, um anderen mitzuteilen, dass du an etwas arbeitest, damit andere warten oder dich kontaktieren können, um sich zusammenzuschließen. Sei dir jedoch bewusst, dass andere weiterhin daran arbeiten und versuchen können, dir zuvorzukommen. Wir vergeben jedoch keine Prämien für schlampige Arbeit. Wenn zwei hochwertige Einreichungen kurz nacheinander erfolgen (innerhalb eines Tages oder zwei), könnten wir nach unserem Ermessen beschließen, Prämien an beide zu vergeben, zum Beispiel 100%% für die erste Einreichung und 50%% für die zweite Einreichung (also insgesamt 150%%)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Für die größeren Prämien (insbesondere Scraping-Prämien) kontaktiere uns bitte, wenn du ~5%% davon abgeschlossen hast und sicher bist, dass deine Methode auf den gesamten Milestone skalierbar ist. Du musst deine Methode mit uns teilen, damit wir dir Feedback geben können. Auf diese Weise können wir auch entscheiden, was zu tun ist, wenn mehrere Personen potentiell einen Anspruch auf eine Prämie haben, wie z.B. die Prämie möglicherweise an mehrere Personen zu vergeben, die Zusammenarbeit zu fördern usw."

msgid "page.volunteering.section.bounties.text6"
msgstr "WARNUNG: Die Aufgaben mit hohen Prämien sind <span %(bold)s>schwierig</span> — es könnte klug sein, mit einfacheren zu beginnen."

msgid "page.volunteering.section.bounties.text7"
msgstr "Geh zu unserer <a %(a_gitlab)s>Gitlab-Issueliste</a> und sortiere nach „Label priority“. Dies zeigt ungefähr die Reihenfolge der Aufgaben, die uns wichtig sind. Aufgaben ohne explizite Prämien sind weiterhin für eine Mitgliedschaft berechtigt, insbesondere diejenigen, die mit „Accepted“ und „Anna’s favorite“ markiert sind. Du könntest am besten mit einem „Starter project“ beginnen."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates über <a %(wikipedia_annas_archive)s>Annas Archiv</a>, die größte wirklich offene Bibliothek in der Geschichte der Menschheit."

msgid "layout.index.title"
msgstr "Annas Archiv"

msgid "layout.index.meta.description"
msgstr "Die weltweit größte, frei verfügbare Open-Source-Bibliothek. Enthält Sci-Hub, Library Genesis, Z-Library, und mehr."

msgid "layout.index.meta.opensearch"
msgstr "Durchsuche Annas Archiv"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annas Archiv braucht deine Hilfe!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Viele versuchen, uns zu Fall zu bringen, aber wir wehren uns."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Wenn du jetzt spendest, erhältst du <strong>doppelt</strong> so viele schnelle Downloads."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Gültig bis zum Ende dieses Monats."

msgid "layout.index.header.nav.donate"
msgstr "Spenden"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Menschliches Wissen retten: ein tolles Urlaubsgeschenk!"

msgid "layout.index.header.banner.surprise"
msgstr "Überrasche einen geliebten Menschen, schenk ihm eine Mitgliedschaft."

msgid "layout.index.header.banner.mirrors"
msgstr "Um die Widerstandsfähigkeit von Annas Archiv zu erhöhen, suchen wir Freiwillige für Mirrors."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Das perfekte Valentinstagsgeschenk!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Wir haben eine neue Spendenmethode zur Verfügung: %(method_name)s. Bitte erwäge zu %(donate_link_open_tag)sspenden</a> - der Betrieb dieser Webseite ist nicht billig, und deine Spende macht wirklich einen Unterschied. Vielen Dank."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Wir führen eine Spendenaktion zur <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">Sicherung</a> der größten Comics Schattenbibliothek der Welt durch. Danke für deine Unterstützung! <a href=\"/donate\">Spende.</a> Wenn Du nicht spenden kannst, denke darüber nach, uns zu unterstützen, indem Du es Deinen Freunden erzählst und uns auf <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a> folgst."

msgid "layout.index.header.recent_downloads"
msgstr "Kürzlich heruntergeladen:"

msgid "layout.index.header.nav.search"
msgstr "Suche"

msgid "layout.index.header.nav.faq"
msgstr "FAQs"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Metadaten Verbessern"

msgid "layout.index.header.nav.volunteering"
msgstr "Freiwilligenarbeit & Prämien"

msgid "layout.index.header.nav.datasets"
msgstr "Datensätze"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

msgid "layout.index.header.nav.activity"
msgstr "Aktivität"

msgid "layout.index.header.nav.codes"
msgstr "Codeexplorer"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM-Daten"

msgid "layout.index.header.nav.home"
msgstr "Startseite"

msgid "layout.index.header.nav.annassoftware"
msgstr "Annas Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Übersetzen ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Anmelden / Registrieren"

msgid "layout.index.header.nav.account"
msgstr "Account"

msgid "layout.index.footer.list1.header"
msgstr "Annas Archiv"

msgid "layout.index.footer.list2.header"
msgstr "Bleib in Kontakt"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / Urheberrechtsansprüche"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Fortgeschritten"

msgid "layout.index.header.nav.security"
msgstr "Sicherheit"

msgid "layout.index.footer.list3.header"
msgstr "Alternativen"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "unabhängig"

msgid "page.search.results.issues"
msgstr "❌ Diese Datei hat womöglich Fehler."

msgid "page.search.results.fast_download"
msgstr "Schneller Download"

msgid "page.donate.copy"
msgstr "kopieren"

msgid "page.donate.copied"
msgstr "kopiert!"

msgid "page.search.pagination.prev"
msgstr "Vorherige"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Nächste"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Mirror #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% des schriftlichen Erbes der Menschheit für immer bewahrt %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datasets ▶ Dateien ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Downloade von:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Wir haben mehrere Download-Optionen für den Fall, dass eine davon nicht funktioniert. Alle haben exakt die gleiche Datei."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Bitte bedenke, dass Anna's Archive keine der hier dargestellten Inhalte hostet. Wir verlinken lediglich auf die Webseiten anderer Leute. Falls du glaubst, dass du eine gültige DMCA-Beschwerde hast, schau bitte auf die %(about_link)sÜber Uns Seite</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Anonymer Z-Library Mirror #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Spenden"

#~ msgid "page.donate.header"
#~ msgstr "Spenden"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive ist ein gemeinnütziges Open-Source-Projekt, das vollständig von Freiwilligen betrieben wird. Wir nehmen Spenden entgegen um unsere Kosten zu decken, welche Hosting, Domainnamen, Entwicklung und andere Ausgaben umfassen."

#~ msgid "page.donate.text2"
#~ msgstr "Mit euren Beiträgen können wir diese Website am Laufen halten, die Funktionen verbessern und mehr Sammlungen archivieren."

#~ msgid "page.donate.text3"
#~ msgstr "Kürzliche Spenden: %(donations)s. Danke für eure Großzügigkeit. Wir schätzen es sehr, dass du uns vertraust - egal welchem Betrag du erübrigen kannst."

#~ msgid "page.donate.text4"
#~ msgstr "Um zu Spenden, wähle bitte unten deine bevorzugte Bezahlmethode. Solltest du auf Probleme stoßen, kontaktiere uns bitte unter %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Kredit-/Debit-Karte"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Crypto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Fragen"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Gehe zu %(link_open_tag)sdieser Seite</a> und folge den Anweisungen, entweder durch Scannen des QR-Codes oder durch Klicken des \"paypal.me\" Links. Falls das nicht funktionieren sollten, versuche die Seite zu aktualisieren, da du dadurch möglicherweise ein anderes Konto angezeigt bekommst."

#~ msgid "page.donate.cc.header"
#~ msgstr "Kredit-/Debit-Karte"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Wir verwenden Sendwyre, um Geld direkt in unsere Bitcoin (BTC) Wallet zu transferieren. Das dauert ca. 5 Minuten."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Diese Methode hat einen Mindesttransaktionsbetrag von 30$ und eine Gebühr von 5$."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Schritte:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Kopiere unsere Bitcoin (BTC) Wallet Adresse: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Gehe zu %(link_open_tag)sdieser Seite</a> und klicke auf \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Füge unsere Wallet Adresse ein und folge den weiteren Anweisungen"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Kryptographisch"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(funktioniert auch mit BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Bitte verwende diesen %(link_open_tag)sAlipay Account</a>, um deine Spende zu betätigen. Wenn das nicht funktioniert, versuche die Seite zu aktualisieren, da du dann vielleicht ein anderes Konto bekommst."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Diese Bezahlmethode funktioniert im Moment leider nicht. Bitte versuchen Sie es später noch einmal. Vielen Dank, dass du spenden möchtest, wir wissen das wirklich zu schätzen!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Bitte benutze %(link_open_tag)sdiese Pix-Seite</a>, um deine Spende zu senden. Wenn das nicht funktioniert, versuche die Seite zu aktualisieren, da dir dadurch möglicherweise ein anderes Konto angezeigt wird."

#~ msgid "page.donate.faq.header"
#~ msgstr "Häufig gestellte Fragen"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> ist ein Projekt, welches sich als Ziel setzt alle existierenden Bücher mithilfe von Datenaggregation aus verschiedenen Quellen zu katalogisieren. Wir verfolgen außerdem den Fortschritt der Menschheit, all diese Bücher einfach und in digitaler Form durch “<a href=\"https://de.wikipedia.org/wiki/Schattenbibliothek\"> Schattenbibliotheken</a>” verfügbar zu machen. Erfahre mehr unter <a href=\"/about\">Über uns.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr "Mitgliedschaft: <strong>%s(tier_name)</strong> bis %(until_date) <a %(a_extend)s>(erweitern)</a>"

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr "🚀 Schnelle Downloads von unseren Partnern (benötigt <a %(a_login)s>Anmeldung</a>)"

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr "🚀 Schnelle Downloads (Du bist angemeldet!)"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Buch (alle)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Startseite"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datensatz ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Nicht gefunden"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” ist keine valide ISBN Nummer. ISBNs sind 10 bis 13 Zeichen lang, ohne die optionalen Querstriche. Alle Zeichen müssen Nummern sein, außer dem letzten, dieses kann auch ein \"X\" sein. Das letzte Zeichen ist die Prüfziffer, welche einem Wert entsprechen muss, der durch die anderen Ziffern berechnet wird. Außerdem muss die Nummer in einem validen Bereich liegen, der von der internationalen ISBN Agentur zugewiesen wird."

#~ msgid "page.isbn.results.text"
#~ msgstr "Passende Dateien in unserer Datenbank:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Keine passenden Dateien in unserer Datenbank gefunden."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Suche ▶ %(num)d+ Ergebnisse für <span class=\"italic\">%(search_input)s</span> (in Schattenbibliothek Metadaten)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Suche ▶ %(num)d Ergebnisse für<span class=\"italic\">%(search_input)s</span> (in Schattenbibliothek Metadaten)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Suche ▶ Suchfehler für <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Suche ▶ Neue Suche"

#~ msgid "page.donate.header.text3"
#~ msgstr "Du kannst auch ohne Account spenden (für einmalige Spenden und die Mitgliedschaft werden die selben Bezahlungsmethoden unterstützt):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Eine einmalige anonyme Spende machen (keine Mitgliedervorteile)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Wähle eine Bezahloption. Erwäge bitte eine Spende in Kryptowährung %(bitcoin_icon)s, da wir dabei viel weniger Gebühren bezahlen müssen."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Falls du schon Crypto Währungen besitzt, sind hier sind unsere Adressen."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Vielen Dank, dass du uns hilfst! Dieses Projekt würde ohne dich nicht möglich sein."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Für PayPal-Spenden verwenden wir PayPal Crypto, was uns erlaubt anonym zu bleiben. Wir sind sehr dankbar, dass du dir die Zeit nimmst auf diese Weise zu spenden, da uns das eine grosse Hilfe ist."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Folge den Instruktionen um Bitcoin (BTC) zu kaufen. Du brauchst nur den Betrag zu kaufen den du Spenden willst."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Sollten Sie manche Bitcoins durch Preisveränderung oder Gebühren verlieren, <em>machen Sie sich bitte keine Sorgen</em>. Das ist normal mit Kryptowährungen, aber es erlaubt uns anonym zu arbeiten."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Füge unsere Bitcoin (BTC) Adresse als Empfänger ein und folge der Anleitung um deine Spende zu senden:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Verwende bitte <a %(a_account)s>diesen Alipay Account</a> um deine Spende zu senden."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Verwende bitte <a %(a_account)s>diesen Pix Account</a> um deine Spende zu senden."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Falls deine Zahlungsmethode nicht in der Liste ist, wäre das einfachste <a href=\"https://paypal.com/\">Paypal</a> oder <a href=\"https://coinbase.com/\">Coinbase</a> auf deinem Handy zu installieren und dort Bitcoin (BTC) zu kaufen. Danach kannst du es an unsere Adressen senden: %(address)s. In den meisten Ländern sollte das nur ein paar Minuten dauern."

#~ msgid "page.search.results.error.text"
#~ msgstr "Versuche <a href=\"javascript:location.reload()\">die Seite neu zu laden</a>. Falls das Problem weiterhin auftritt, lass es uns bitte auf <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, oder <a href=\"https://t.me/annasarchiveorg\">Telegram</a> wissen."

#~ msgid "page.donate.login"
#~ msgstr "Um ein Mitglied zu werden bitte <a href=\"/login\">Anmelden oder Registrieren</a>. Falls du keinen Account erstellen möchtest, wähle bitte oben \"Eine einmalige anonyme Spende machen\". Vielen Dank für deine Unterstützung!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Startseite"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Über uns"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Spenden"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datensätze"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "App"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Übersetzen"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s Torrents"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Enthält %(libraries)s, und mehr."

#~ msgid "page.home.preservation.text"
#~ msgstr "Wir konservieren Bücher, wissenschaftliche Aufsätze, Comics, Magazine und mehr, indem wir diese Materialien von verschiedenen <a href=\"https://de.wikipedia.org/wiki/Schattenbibliothek\">Schattenbibliotheken</a> an einer Stelle zusammenbringen. Der Fortbestand aller Daten wird sichergestellt, indem diese einfach zur massenhaften Vervielfältigung zur Verfügung gestellt werden. Die weite Verbreitung in Kombination mit quelloffenem Code macht unsere Webseite robust gegen Stilllegungen. Erfahre mehr über <a href=\"/datasets\">unsere Datensätze</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datensatz ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Nicht gefunden"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" sieht nicht wie ein DOI aus. Es sollte mit \"10.\" starten und einen Schrägstrich beinhalten."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Kanonische URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Diese Datei befindet sich möglicherweise auf %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Passende Dateien in unserer Datenbank:"

#~ msgid "page.doi.results.none"
#~ msgstr "Keine passenden Dateien in unserer Datenbank gefunden."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Schnelle Downloads</strong> Du hast für heute keine schnellen Downloads mehr verfügbar. Bitte kontaktiere Anna unter %(email)s, wenn du an einem Upgrade deiner Mitgliedschaft interessiert bist."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Schnelle Downloads für heute verbraucht. Kontaktiere Anna unter %(email)s wenn du an einem Upgrades deines Benutzerkontos interessiert bist."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Kann ich mich anders beteiligen?</div> Ja! Du findest mehr dazu unter <a href=\"/about\">„Über uns„</a> im Abschnitt „Wie kann ich helfen?“."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Ich finde es nicht gut, dass ihr Annas Archiv monetarisiert!</div> Wenn dir nicht passt wie wir das Projekt führen, kannst du deine eigene Schattenbibliothek eröffnen! Der ganze Quellcode ist Open-Source, niemand hält dich davon ab. ;)"

#~ msgid "page.request.title"
#~ msgstr "Bücher anfragen"

#~ msgid "page.request.text1"
#~ msgstr "Kannst du eBooks bitte beim <a %(a_forum)s>Libgen.rs forum</a> anfragen? Du kannst dort ein Account erstellen und dich in einem der Threads melden:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Für eBooks, verwende <a %(a_ebook)s>diesen Thread</a>.</li><li %(li_item)s>Für Bücher, die nicht als eBook verfügbar sind, verwende <a %(a_regular)s>diesen Thread</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Folge den Regeln des jeweiligen Threads."

#~ msgid "page.upload.title"
#~ msgstr "Hochladen"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Große Uploads"

#~ msgid "page.about.title"
#~ msgstr "Über uns"

#~ msgid "page.about.header"
#~ msgstr "Über uns"

#~ msgid "page.home.search.header"
#~ msgstr "Suche"

#~ msgid "page.home.search.intro"
#~ msgstr "Durchsuche unseren Katalog von Schattenbibliotheken."

#~ msgid "page.home.random_book.header"
#~ msgstr "Zufälliges Buch"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Zu einem zufälligen Buch aus dem Katalog gehen."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Zufälliges Buch"

#~ msgid "page.about.text1"
#~ msgstr "Annas Archiv ist eine gemeinnützige Open-Source-Suchmaschine für „<a href=\"https://de.wikipedia.org/wiki/Schattenbibliothek\">Schattenbibliotheken</a>“. Sie wurde von <a href=\"http://annas-blog.org\">Anna</a> ins Leben gerufen, die der Meinung war, dass es einen zentralen Ort für die Suche nach Büchern, wissenschaftlichen Aufsätzen, Comics, Zeitschriften und anderen Dokumenten geben müsse."

#~ msgid "page.about.text4"
#~ msgstr "Wenn du eine gültige DMCA-Beschwerde hast, schau unten auf dieser Seite weiter oder kontaktiere uns unter %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Entdecke Bücher"

#~ msgid "page.home.explore.intro"
#~ msgstr "Hier ist eine Kombination von beliebten Büchern und Büchern, die eine große Rolle in der Welt von Schattenbibliotheken und digitaler Bewahrung spielen."

#~ msgid "page.wechat.header"
#~ msgstr "Inoffizieller WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "Wir haben eine inoffizielle WeChat-Seite, die von einem Community-Mitglied gepflegt wird. Verwende den folgenden Code, um darauf zuzugreifen."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Über uns"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "App"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Inoffizieller WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Buch anfragen"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Hochladen"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Lade Freunde ein"

#~ msgid "page.about.help.header"
#~ msgstr "Wie kann ich helfen?"

#~ msgid "page.refer.title"
#~ msgstr "Lade deine Freunde zu uns ein um schnelle Bonusdownloads zu erhalten"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Mitglieder können ihre Freunde einladen um Bonusdownloads zu erhalten."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Für jeden Freund, der Mitglied wird:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Deine Freunde</strong> bekommen %(percentage)s%% Bonusdownloads zusätzlich zu den regulären täglichen Downloads für die Dauer ihrer Mitgliedschaft."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Du</strong> erhältst die gleiche Anzahl an Bonusdownloads zusätzlich zu deinen regulären täglichen Downloads für die gleiche Dauer, für die sich dein Freund angemeldet hat (bis zu einer Gesamtanzahl an %(max)s Bonusdownloads zu jedem beliebigen Zeitpunkt). Um deine Bonusdownloads nutzen zu können, musst du eine aktive Mitgliedschaft haben."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Beispiel:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Dein Freund nutzt deinen Einladungslink und meldet sich für eine dreimonatige „Glücklicher Bibliothekar“ Mitgliedschaft an, welche mit %(num)s schnellen Downloads einhergeht."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Er kriegt %(num)s Bonusdownloads jeden Tag, für drei Monate lang."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Du bekommst ebenfalls %(num)s Bonusdownloads jeden Tag, für drei Monate lang."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Einladungslink:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Melde dich an</a> und werde Mitglied um auch deine Freunde einzuladen."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Werde Mitglied</a> und lade auch deine Freunde dazu ein."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Oder füge %(referral_suffix)s am Ende jedes anderen Links ein, und die Einladung wird gespeichert, wenn sie ein Mitglied werden."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Spende den kompletten Betrag von %(total)s auf <a %(a_account)s>dieses Alipay Konto"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Alternativ kannst du sie auch auf Z-Library <a %(a_upload)s>hier</a> hochladen."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Um die Resilienz von Annas Archiv zu erhöhen, suchen wir nach Freiwilligen, die bereit sind eigene Mirrors zu hosten. <a href=\"/mirrors\">Mehr Erfahren…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Mirrors: Freiwillige gesucht"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "nur diesen Monat!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub hat das Hochladen neuer wissenschaftlicher Aufsätze<a %(a_closed)s>pausiert</a>."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Wähle eine Bezahlmethode. Wir geben Ermäßigungen für Spenden in Crypto-Währungen %(bitcoin_icon)s, weil dabei (viel) weniger Gebühren entstehen."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Wähle eine Zahlungsmethode. Wir akzeptieren derzeit nur kryptobasierte Zahlungen %(bitcoin_icon)s, da herkömmliche Zahlungsabwickler ablehnen, mit uns zusammenzuarbeiten."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Wir können Kredit-/Debitkarten nicht direkt unterstützen, da Banken nicht mit uns zusammenarbeiten wollen. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Es gibt jedoch mehrere Möglichkeiten, Kredit-/Debitkarten trotzdem mit unseren anderen Zahlungsmethoden zu verwenden:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Langsame & externe Downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Wenn du Kryptowährung zum ersten Mal benutzt, schlagen wir vor, mit %(option1)s, %(option2)s, oder %(option3)s Bitcoin zu kaufen und zu überweisen (Bitcoin ist die am weitesten verbreitete Kryptowährung)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 Links zu Datensätzen, die Sie verbessert haben."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 Links oder Screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 Links oder Screenshots von erfüllten Anfragen."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Wenn Sie daran interessiert sind, diese Datasets für <a %(a_faq)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainings</a>-Zwecke zu spiegeln, kontaktieren Sie uns bitte."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Wenn Sie daran interessiert sind, dieses Dataset für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainings</a>-Zwecke zu spiegeln, kontaktieren Sie uns bitte."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Hauptwebsite"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN-Länderinformationen"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Wenn Sie daran interessiert sind, dieses Datenset für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainings</a>-Zwecke zu spiegeln, kontaktieren Sie uns bitte."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Die Internationale ISBN-Agentur veröffentlicht regelmäßig die Bereiche, die sie den nationalen ISBN-Agenturen zugewiesen hat. Daraus können wir ableiten, zu welchem Land, welcher Region oder welcher Sprachgruppe diese ISBN gehört. Derzeit nutzen wir diese Daten indirekt über die <a %(a_isbnlib)s>isbnlib</a>-Python-Bibliothek."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ressourcen"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Zuletzt aktualisiert: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN-Website"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadaten"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Ohne „scimag“"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Unsere Inspiration für das Sammeln von Metadaten beruht auf dem Ziel von Aaron Swartz: „eine Website für jedes jemals veröffentlichte Buch“ zu erstellen. Für diesen Zweck hat er die <a %(a_openlib)s>Open Library</a> geschaffen."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dieses Projekt lief zwar gut, doch unsere einmalige Position ermöglicht es uns Metadaten zu sammeln, welche die Open Library nicht erhalten würde."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Eine weitere Inspiration war unser Wunsch zu wissen, <a %(a_blog)s>wie viele Bücher es auf der Welt gibt</a>, damit wir berechnen können, wie viele Bücher wir noch retten müssen."

#~ msgid "page.partner_download.text1"
#~ msgstr "Um jedem die Möglichkeit zu geben, Daten kostenlos herunterzuladen, musst du <strong>%(wait_seconds)s Sekunden</strong> warten, bevor du diese Datei herunterladen kannst."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Seite automatisch aktualisieren. Wenn Sie das Download-Fenster verpassen, wird der Timer neu gestartet, daher wird eine automatische Aktualisierung empfohlen."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Jetzt herunterladen"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Formatkonvertierung: Verwende Onlinetools um das Dateiformat zu wechseln. Um etwa zwischen EBUP und PDF zu wechseln, verwende <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: Lade die Datei herunter (pdf oder epub werden unterstützt), <a %(a_kindle)s>sende sie anschließend an Kindle</a> über das Web, die App oder per E-Mail. Nützliche Tool: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Unterstütze die Autoren: Wenn du das Werk magst und es dir leisten kannst, ziehe in Betracht das Originalwerk zu kaufen oder die Autoren direkt zu unterstützen."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Unterstütze deine Bibliothek vor Ort: Wenn das Werk auch in deiner lokalen Bibliothek verfügbar ist, ziehe in Betracht es dort kostenlos auszuleihen."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nicht direkt in großen Mengen verfügbar, nur in semi-großen Mengen hinter einer Paywall."

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annas Archiv verwaltet eine Sammlung von <a %(isbndb)s>ISBNdb-Metadaten</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb ist ein Unternehmen, das verschiedene Online-Buchhandlungen durchsucht, um ISBN-Metadaten zu finden. Annas Archiv hat Sicherungskopien der ISBNdb-Buchmetadaten erstellt. Diese Metadaten sind über Annas Archiv verfügbar (derzeit jedoch nicht in der Suche, es sei denn, Sie suchen explizit nach einer ISBN-Nummer)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Technische Details finden Sie unten. Irgendwann können wir diese Daten nutzen, um festzustellen, welche Bücher in Schattenbibliotheken noch fehlen, um zu priorisieren, welche Bücher gefunden und/oder gescannt werden sollen."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Unser Blogbeitrag über diese Daten"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb-Scrape"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Derzeit haben wir einen einzigen Torrent, der eine 4,4 GB große gzipped <a %(a_jsonl)s>JSON Lines</a>-Datei (20 GB entpackt) enthält: „isbndb_2022_09.jsonl.gz“. Um eine „.jsonl“-Datei in PostgreSQL zu importieren, können Sie etwas wie <a %(a_script)s>dieses Skript</a> verwenden. Sie können es sogar direkt pipen, indem Sie etwas wie %(example_code)s verwenden, sodass es on-the-fly dekomprimiert wird."

#~ msgid "page.donate.wait"
#~ msgstr "Bitte mindestens <span %(span_hours)s>zwei Stunden</span> warten (und diese Seite neu laden), bevor du uns kontaktierst."

#~ msgid "page.codes.search_archive"
#~ msgstr "Suche in Annas Archiv nach „%(term)s“"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Spende mit Alipay oder WeChat. Du kannst zwischen diesen auf der nächsten Seite auswählen."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Verbreiten Sie das Wort über Annas Archiv in sozialen Medien und Online-Foren, indem Sie Bücher oder Listen auf AA empfehlen oder Fragen beantworten."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Die Belletristik-Sammlung hat sich verändert, hat aber immer noch <a %(libgenli)s>Torrents</a>, obwohl sie seit 2022 nicht mehr aktualisiert wurde (wir haben jedoch direkte Downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annas Archiv und Libgen.li verwalten gemeinsam Sammlungen von <a %(comics)s>Comics</a> und <a %(magazines)s>Zeitschriften</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Keine Torrents für russische Belletristik und Standarddokumentensammlungen."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Es sind keine Torrents für die zusätzlichen Inhalte verfügbar. Die Torrents auf der Libgen.li-Website sind Spiegelungen anderer hier aufgeführter Torrents. Die einzige Ausnahme sind Fiction-Torrents ab %(fiction_starting_point)s. Die Comics- und Zeitschriften-Torrents werden in Zusammenarbeit zwischen Annas Archiv und Libgen.li veröffentlicht."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Von einer Sammlung <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> genaue Herkunft unklar. Teilweise von the-eye.eu, teilweise von anderen Quellen."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

