��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b   sd n  �f .   bh 0  �h J  �i �  
l �  �m K  �o \   �p p   1q H   �q b   �q �  Nr f  @t �   �u   �v   �x   �y   �{ �  �} �   �   �� �  �� ?   ��   ބ h   � b  O�    �� f  ш H   8� &   ��    ��    �� K   ۋ '   '�    O� &   h� %   �� %   ��    ی M   � �  :� 0  �� l   � ;   [� 
   ��    ��    ��    ��    ̐    � 	   �    ��    � %   �    ;�    A� 	   Y� 
   c�    q�    }�     ��    �� �  ֑ }  �� �   � .   �� n  Ֆ �   D� 4  � �   '�    � �   4�    ݛ �   �� $   �� 4  ��    � �  5� 4   ԟ q   	�    {� M  �� T  ա �  *� H  �    P� Q   c� a   �� �   � .   § 1   � �   #� :   �� L   � ,   6�    c� x   h� �  � R   ϫ �   "� �   �� M   o� 5  �� H   � )  <� �   f� �   �� U   �� �    � �   ٲ H   ��   г �   Ҵ V   �� @   � �   P�    � �   �� =  �� H   �    :� �   @� �   ��    � �  �� �   ��   \� �  s� �   ,� o  � .  �� H   �� J   � ^   R� �   �� `   ;� q   �� D   � �   S� a   �� R   F�    �� }   �� S   #� �   w� �   $�    �� �   ��   �� i  ��   T� �   �� �   d� N  a� �   �� �   c� �   � �  �� �   7�   ��    
� �   � �   �� �   �� u  p� �   �� �   m� �   � �  �� �   �� <  ;� $  x� '   �� X   �� �   � W   �� �  � �   �� t   v� �   �� $   t� U  �� �   �� �  ��    v� j   �� �   �� �   ��   �� �   �� �  {� �  p� (   .� �  W� �  �� 9  �� �  �� F  �� 5    X   > �   �    6 �   > ^   �  `   � 0  � �   *    � g  � m  G
   �    � �   � R  �
 �   � O  � �   ; a   2    � �  � k  v 
   � �   �   � W  � �   W �  W n   � P   T   � ;   �    � I  � (  F! B  o" 1   �# H   �# (  -$ �   V& {   �& _   ^' �  �' "   K) !   n) �  �) �   9+ �   �+ K   �, �   �, 9   g-   �-   �.    �/    �/ )   �/ =   0 9   [0 6   �0 Q   �0 �   1    �1 )   �1 9   �1 I   2 4   c2 9   �2 8   �2 d   3    p3    w3 \  �3 :  �4 �   +6   $7 �   38 �   �8 u  �9    $; �   9; �   < �   �< p  \= �  �> 
  q@ �  B �  gD H   F 3   WF +   �F �   �F   iG �  �I V  ;K r  �L E  N q  KO 0  �Q 4   �R G  #S E  kT {  �U    -W    DW ]  EX �   �Y �  -Z #  �[    �\ .   �\ c   ]    �] �  �] �  <` �  �a �   qd �   0e    f �   &f �   g H   �g }   Ah 6  �h   �i ~   k �  �k [   (m (  �m    �n %  �n �   �p ,  �q �  �s 
  \v �   jx    Dy *  Jy �  uz    ]| t  n|   �} D   � @  E�   �� W  �� H   �� �  B�    $� �   %� B   �� i   �� 	   Z� 8   d� $   �� (       �    � �   � c  ό )  3� S  ]� �   ��    �� b  �� n  � �  t� �  �� {  �� �  �    £   ȣ    �    � �  �� [  ¦   � �  7� 
   ح 	  � �  �   α +  � )  � �  :� �  Ż     U� �   v� n   N� �  �� �  �� �   �� �   t� �   � �  ��    V� �   k� ^   ?� S   ��    �� 4   
� @   ?� ]  �� �  �� �  �� 7   |� �  �� �   ��    l� �   r� =   -� �   k� �   �� 1   �� `   �    e� |  w� �  �� 	  �� �  �� �   S�    �� o   �� 1  a�    �� �   �� �  +� �   � ?   �� 9   � �  U� .   �� �  !� �  ��   l�     �� �   �� �   D� �  �� �  O� 
   #� �   1� 8   &� �   _� �    � 4  �� �   $� Y   � .   f�   �� B   �� �  �� n  �� �   �   �� d    @   q �   � 8  u x  � �  ' �   =   
 �   I
   4 G  : �   �
 �  Q �  J .   $ �  S 2   L �   e   t    � r  � -  a �  �   - 4  1   f �  i! �   % �  �% �  �' �   r) A   * {  B- <   �. �   �. �   �/    0    *0 Z  00 �   �1 F   O2   �2   �3 F  �5 %  �6 �  9 �   �: �  g; L  =    b> �   �>    R? �  [? (   �@    A    (A    .A "   ?A    bA    tA    �A    �A    �A 	   �A    �A    �A 
   �A    �A 	   B 2   B    ?B 
   CB 	   QB    [B �   bB T   \C "   �C    �C &   �C )   D .   1D /   `D #   �D    �D 	   �D    �D    �D    �D    �D    E    $E    -E    4E ;   DE %   �E    �E #   �E '   �E 4   F /   CF    sF $   �F `   �F    G [   "G 6   ~G    �G T   �G R   H    eH    �H "   �H    �H    �H    �H    I    I    +I    3I    JI    WI    mI 	   zI 
   �I    �I    �I    �I    �I 	   �I    �I 	   �I    �I    
J    J 	   J    !J    1J    =J    XJ    `J    }J    �J    �J 	   �J    �J :   �J    K    K (   K    AK    IK    cK    oK    �K    �K '   �K i   �K �   6L a   �L �   RM �  N x   P    �P    �P    �P    �P    �P    �P    �P !   �P Z   Q =   yQ h   �Q #    R 7   DR 1   |R m   �R _   S �   |S \   eT E   �T #   U    ,U    =U 
   EU    PU    `U    oU    �U    �U    �U    �U    �U    �U 
   �U    �U 	   �U    �U 
   V    V    +V 
   1V    <V    EV    UV    sV '  �V    �W    �W    �W    �W    �W I   �W p   <X '   �X 2   �X 1   Y    :Y    BY    JY u   MY    �Y    �Y )   �Y w    Z    xZ    �Z {   �Z $   [ �  >[ g   �^ �   d_ �   �_ �   �` 1   xa �   �a �   �b V  5c �   �d    Ye    te V   }e R   �e X   'f c   �f |   �f K   ag e   �g &   h *   :h    eh 
   nh Z   |h #   �h    �h    i    i     i u   ?i 
   �i .   �i K   �i    ;j    [j Q   xj q   �j �  <k    m    m �   /m    �m    �m    n     
n    .n .   6n ^  en $   �o    �o    �o 	   p �  p $   �q    �q    �q x   �q    \r 	   br /   lr    �r    �r )   �r �   �r    �s    �s [   �s    t    /t 	   Et    Ot 3   at h   �t    �t 5   u �   Lu t   v Q   �v    �v 	  �v O   �w    >x 7   Ux    �x �   �x �   ey    z O   4z �   �z �   4{    | *   4|    _| �  s| %   ~ #   ,~    P~    k~ %   �~ �   �~    \    { ,   � ?   � 
    �    �    $� #   A� |  e� �  � =  �� L   �� 4   �    @� !   M� ;  o� �   �� �   ��    L� �   d� �  W�    �� $   � P  8� N  �� 	   ؐ    � 6   � 
   &� M  1�    � d  �� �  �� $  ӕ     �� �   � 8   ��     ޗ �   �� 1  �� ,  �� �   �� �  �� s   E� !  �� Y   ۞ �   5� �   �� V   ɠ �    � 0   ߡ &   �    7� 	   H�    R� '   d� !   �� Q   �� *    � 	   +� %   5� J  [� 4   ��   ۤ �   � �   v� &   �    )�    I�    d� $   x�    �� "   �� &   ܧ �   �    �� �   �    � �   � ~   ٪ �   X� 3  � �   � 0   խ �   � 	   �� !  �� �   ȯ    N� �  d�    (�    5�    N�    c� *   ��    ��    �� W   �� �   � �   ߳ 
   Դ    ߴ    � �    � h  ̵ �   5� �   �    ��    ��    ��    ɸ    ܸ    �    �� H   � 7   O� �  �� h   |�    � v   �� s   n� P   � z   3� Z   �� R   	�    \� e   d� J   ʾ �   � Y   �� L   �    P� �   e� b   Q� B   �� k   �� V   c� :   �� 	   �� 6   �� t   6� �   �� E   Z� �   ��    N� '  U� M   }� _   �� �   +�    �� �  �� �   ��    ��    ��    ��    ��   �� 3   � q   R�   �� �   �� �   �� �   8� �   � �   �� �   `� h   6� H  �� e   �� �  N� �   �� �   �� 
   �� 
   �� 
   �� �   �� 
   Z� 
   h� P   v� k   �� �   3� 
   � �   &� <   %� �   b� 9   �� �   $� k   �� 
   � �   $� 
   � 
   � 
   � K  ,� �   x� �  �    ��    
�    � �   )�    �� #   ��   � �   ,� &   ��    ��    � 5   &� 9   \� .   �� %   �� %   �� �   �    �� �  
�   �� �   �� X   l� �   �� 6  ��   �� q  �� �   :� �   � �   ��    ,� �  K� '   �� s  ��   s� J  {� �   �� h  `� �   �� �   �� �   :� 6   �� @   �    B� =   a� 
   ��    ��    ��   �� 	   �� g   �� 8   @     y  	   �  
   �  $   �  �   �  B   = 0   �   � 4   �    �    �    �        8 	   J    T 	   \    f 	   o    y 	   � ,   � �   �    [    l    {    � 
   �    � 
   �    �    �    � )   � i   $    � 1   � j   � �   : �   � �   � W  � �   �	 <  �
    � {   	 5   � M   � ^   	
 �   h
   + B   . t   q    � �   � w   � �        �     �    �    � 	   �    �    � %       )    2    D    X !   t    �    �    �    �    �    �    �          "   8 �   [ �   � ,   o &   � i   � W   - }   � *    3   . /   b >   � F   � -    �   F �    "      . G   C o   � "   �    �     �    E   �    � �   � �   � u   ? 5   � P   � �   <     ! *   !    ?! =   T! �   �! �   )"    �"    �"    �" "   �" �   �" 5   �# &   �# ;   �# %   -$    S$     r$ K   �$ #   �$ f   % >   j%   �% H   �& l   �& �   h' @   ,( "   m(     �( "   �( !   �( !   �( !   ) "   :) 7   ]) >   �) f  �) V   ;, 6   �, G   �, %   -    7- x   ?- �   �- �   N. �   / 	   �/ �   �/ #   a0    �0 �   �0    z1 9   �1 8   �1 2   2 [   62 m   �2 M    3    N3 !   k3 �   �3 4   14 (   f4 V   �4 &  �4 @   
6 �   N6 u   �6 j   b7 �   �7    \8 +   y8 s   �8    9 d   19 ,   �9 �   �9 5   �: D   �:    ; ,   4; �   a; V   X< L   �< �   �< h   �= M   �= �   B> �   �> 	   p?    z? V   �? V   �?    <@ #   S@    w@    �@    �@ -   �@ �   �@ h   oA    �A     �A '   B :   9B F   tB �   �B B   IC y   �C b   D !   iD x   �D �   E /   �E T   )F    ~F G   �F D   �F    G x   -G w   �G ,   H P   KH 
   �H /   �H Q   �H    ,I i   =I (   �I    �I -   �I b   J �   wJ L   WK    �K    �K L   �K    +L Y   HL <   �L �   �L �   M 
   N 4   N �   KN     O �   6O C   �O (   P V   HP    �P    �Q    �Q    �Q    �Q    �Q E   R T   LR    �R    �R    �R    �R ?   �R ;   1S    mS S   uS H   �S    T (   $T <   MT    �T    �T z   �T   <U ]   UV    �V 
   �V �   �V d  �W ^   �X    WY �  jY �  
[ (   �\ �   ]    �] �  �] 5   K` �   �`    a    a 2  .a    ac i   xc x   �c w   [d Y   �d    -e i   Ge 9   �e    �e y   
f ?   �f ?   �f V   g :   [g Q   �g �   �g �   �h *   >i �   ii �  7j �   �k ;   �l w  �l   Zn �   ko J  Cp �   �q .   Yr    �r �   �r 4   ]s 1  �s }   �u O   Bv    �v    �v   �v    �x �   �x S  �y :  �z F  :| H   �} O   �} p   ~ /   �~ -   �~ \   �~ Y   F    � $   � G   �    �    9� 7   O� b   �� 0   �    � s   *�   �� �   ��    p�    �    �� c   �� X   � f   [� �    l   ��    �� !   � �   0� 	   � �   &�   � 
  � [   � 1   O�    ��    ��    �� G   �� :   ܌ �   �   �� y   ؎    R�    e� %   x� $   �� c   Ï    '� >   6� 	   u� 9   � '   ��    � 	   � o   ��    k�    q� 0   ��    ��    ב y   ۑ �   U� �   ,� x   Γ Y   G� �   �� 	   �� !   �� �   ȕ �   �� �   w� 
   n� �   y� B    � C   C� j   ��    � Z   r�    ͚ m   �    S�    f�    �    ��    ��    Ǜ    ۛ    �    �    � /   � /   O� /   � !   �� 4   ќ ,   �    3�    N�    j� O   ��    ҝ    � <   �� )   1� j   [� &   ƞ    �    �    �    .�    E� M   W� 0   �� X   ֟ �   /�    ��    �    )�    H� 9   ^� 	   ��    ��    �� �   ʡ    L�     i�    �� 
   �� 	   �� B   ��    �   �     �    6�    P� #   d� #   ��    �� "   Ť -   � 0   � 4   G� %   |�    �� Z   �� #   �    (�    @� E   Q� V   ��    � /   
� %   :� �   `� i   � L   \�    �� !   �� 	   Ө    ݨ    �    � �  &� ~   ̪    K�    c� +   g� 
   �� '   ��    ɫ 	   Ϋ �   ث >   f� �   ��    ��    �� �   խ    f�    �� $   �� '   Ǯ +   � !   �    =�    X� >   g� !   �� ,   ȯ ;   �� L  1� J   ~� X   ɱ N   "� %   q� �   �� &   .�    U� ^   d�    ó '   ׳    �� E   � J   c�    �� �   δ    t�    �� *   ��    ɵ    ߵ    ��    �    *�    =� j   U� !  �� $   � (   � �   0� �    �    �    ��    �    .�    >�    ]�    p�    ��    ��    ��    ú    Һ 
   �    �     �    �    �    +� )   ;� �   e�    ?� �  `� A  �� �  :� �   � �  �    �� +  ��    ��   ��   �� �  �� �   �� u  s� 5   �� �   � H   ��     *� H   K� T   �� l   �� �   V� �   �� �   �� �   v� �  N�    �� �   �� �   �� a   �� �   �    �� �   �� p  �� �   6� �   ��    �� n   �� l   a�   �� �   �� u   o� m   ��    S�    i�    �� A   �� 2   �� 
   � �   #� E   �� v   � "   y� �   �� �   E� M   �� K   � :   e� q   �� ^   � .   q� �   �� f   )� �   �� �   � 	   �� 9   �� '   � _   +� 3   �� 	   ��    �� `   ��    1�    >�    U� =   \� (   �� @   ��    �    !�    .�    7� 	   =� V   G� |   �� S   � M   o�    ��    �� *   ��    
�    �    .�    5�    <�    E�    K�    S�    \�    d�    s�    |�    ��    ��    ��    ��    �� 	   ��    ��    �� (   ��    � '   :� �   b�    �� Z   � �   g� 
   ,�    :� 
   L�    W�    _�    h� 	   l� �   v� �    � G   ��    ��    �� �   �    �� �   �� �   )� &   �� -   �� �   � �   �� V   k� �   �� h   �� (   � �   C� $   �� #   �� L   !� �   n�    	� �   (� �   �� �   h� [   �    `�    m�    t�    ��    �� 	   �� 
   ��    �� �   �� r   z� �   �� �   u� �   _� p   R� R   �� �  � �      � �   �   3 X  O    � �   � 3  � �   � �  �   E �   L
 �  �
    h J   � �   � �  i 9   � �   & 	       &    . �   I H   � �   < 8   � `   � S   [ m   � !    �   ? M   � /   B W   r �   �    �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: de
Language-Team: de <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library ist eine beliebte (und illegale) Bibliothek. Sie haben die Library Genesis-Sammlung übernommen und durchsuchbar gemacht. Darüber hinaus sind sie sehr effektiv darin geworden, neue Buchbeiträge zu erbitten, indem sie beitragende Nutzer mit verschiedenen Vorteilen belohnen. Derzeit tragen sie diese neuen Bücher nicht zurück zu Library Genesis bei. Und im Gegensatz zu Library Genesis machen sie ihre Sammlung nicht leicht spiegelbar, was eine breite Bewahrung verhindert. Dies ist wichtig für ihr Geschäftsmodell, da sie Geld dafür verlangen, auf ihre Sammlung in großen Mengen zuzugreifen (mehr als 10 Bücher pro Tag). Wir fällen keine moralischen Urteile darüber, Geld für den Massen-Zugang zu einer illegalen Büchersammlung zu verlangen. Es steht außer Zweifel, dass die Z-Library erfolgreich den Zugang zu Wissen erweitert und mehr Bücher beschafft hat. Wir sind einfach hier, um unseren Teil beizutragen: die langfristige Bewahrung dieser privaten Sammlung zu gewährleisten. - Anna und das Team (<a %(reddit)s>Reddit</a>) In der ursprünglichen Veröffentlichung des Piratenbibliotheksspiegels (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) haben wir einen Spiegel von Z-Library erstellt, einer großen illegalen Büchersammlung. Zur Erinnerung, dies schrieben wir in diesem ursprünglichen Blogbeitrag: Diese Sammlung stammt aus der Mitte des Jahres 2021. In der Zwischenzeit ist die Z-Library in einem atemberaubenden Tempo gewachsen: Sie haben etwa 3,8 Millionen neue Bücher hinzugefügt. Es gibt dort sicherlich einige Duplikate, aber der Großteil scheint tatsächlich neue Bücher oder qualitativ hochwertigere Scans von zuvor eingereichten Büchern zu sein. Dies ist größtenteils auf die gestiegene Anzahl von freiwilligen Moderatoren bei der Z-Library und ihr Massen-Upload-System mit Duplikaterkennung zurückzuführen. Wir möchten ihnen zu diesen Errungenschaften gratulieren. Wir freuen uns, bekannt zu geben, dass wir alle Bücher erhalten haben, die zwischen unserem letzten Spiegel und August 2022 zur Z-Library hinzugefügt wurden. Wir haben auch einige Bücher nachgeholt, die wir beim ersten Mal verpasst haben. Insgesamt umfasst diese neue Sammlung etwa 24TB, was viel größer ist als die letzte (7TB). Unser Spiegel umfasst jetzt insgesamt 31TB. Erneut haben wir gegen Library Genesis dedupliziert, da für diese Sammlung bereits Torrents verfügbar sind. Bitte besuchen Sie den Pirate Library Mirror, um die neue Sammlung zu überprüfen (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Dort gibt es mehr Informationen darüber, wie die Dateien strukturiert sind und was sich seit dem letzten Mal geändert hat. Wir werden von hier aus nicht darauf verlinken, da dies nur eine Blog-Website ist, die keine illegalen Materialien hostet. Natürlich ist auch das Seeden eine großartige Möglichkeit, uns zu unterstützen. Vielen Dank an alle, die unser vorheriges Set von Torrents seeden. Wir sind dankbar für die positive Resonanz und freuen uns, dass es so viele Menschen gibt, die sich auf diese ungewöhnliche Weise um die Bewahrung von Wissen und Kultur kümmern. 3x neue Bücher zur Piratenbibliotheksspiegelung hinzugefügt (+24TB, 3,8 Millionen Bücher) Lesen Sie die Begleitartikel von TorrentFreak: <a %(torrentfreak)s>erster</a>, <a %(torrentfreak_2)s>zweiter</a> - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Begleitartikel von TorrentFreak: <a %(torrentfreak)s>erster</a>, <a %(torrentfreak_2)s>zweiter</a> Vor nicht allzu langer Zeit waren „Schattenbibliotheken“ vom Aussterben bedroht. Sci-Hub, das riesige illegale Archiv akademischer Aufsätze, hatte aufgrund von Klagen aufgehört, neue Werke aufzunehmen. Die „Z-Library“, die größte illegale Bibliothek von Büchern, sah sich mit der Verhaftung ihrer angeblichen Schöpfer wegen strafrechtlicher Urheberrechtsverletzungen konfrontiert. Sie schafften es unglaublich, ihrer Verhaftung zu entkommen, aber ihre Bibliothek ist dennoch bedroht. Einige Länder machen bereits eine Version davon. TorrentFreak <a %(torrentfreak)s>berichtete</a>, dass China und Japan KI-Ausnahmen in ihre Urheberrechtsgesetze eingeführt haben. Es ist unklar, wie dies mit internationalen Verträgen interagiert, aber es bietet sicherlich Schutz für ihre inländischen Unternehmen, was erklärt, was wir beobachtet haben. Was Anna’s Archiv betrifft — wir werden unsere Untergrundarbeit aus moralischer Überzeugung fortsetzen. Doch unser größter Wunsch ist es, ans Licht zu treten und unseren Einfluss legal zu verstärken. Bitte reformieren Sie das Urheberrecht. Als die Z-Library vor der Schließung stand, hatte ich bereits ihre gesamte Bibliothek gesichert und suchte nach einer Plattform, um sie zu beherbergen. Das war meine Motivation, Anna’s Archiv zu starten: eine Fortsetzung der Mission hinter diesen früheren Initiativen. Seitdem sind wir zur größten Schattenbibliothek der Welt gewachsen und beherbergen mehr als 140 Millionen urheberrechtlich geschützte Texte in zahlreichen Formaten — Bücher, wissenschaftliche Aufsätze, Zeitschriften, Zeitungen und mehr. Mein Team und ich sind Ideologen. Wir glauben, dass das Bewahren und Bereitstellen dieser Dateien moralisch richtig ist. Bibliotheken auf der ganzen Welt sehen sich mit Budgetkürzungen konfrontiert, und wir können das Erbe der Menschheit auch nicht den Konzernen anvertrauen. Dann kam die KI. Praktisch alle großen Unternehmen, die LLMs entwickeln, kontaktierten uns, um mit unseren Daten zu trainieren. Die meisten (aber nicht alle!) US-amerikanischen Unternehmen überdachten ihre Entscheidung, als sie die illegale Natur unserer Arbeit erkannten. Im Gegensatz dazu haben chinesische Firmen unsere Sammlung begeistert angenommen, offenbar unbeeindruckt von ihrer Legalität. Dies ist bemerkenswert, da China Unterzeichner fast aller wichtigen internationalen Urheberrechtsverträge ist. Wir haben etwa 30 Unternehmen Hochgeschwindigkeitszugang gewährt. Die meisten von ihnen sind LLM-Unternehmen, und einige sind Datenbroker, die unsere Sammlung weiterverkaufen werden. Die meisten sind chinesisch, obwohl wir auch mit Unternehmen aus den USA, Europa, Russland, Südkorea und Japan zusammengearbeitet haben. DeepSeek <a %(arxiv)s>gab zu</a>, dass eine frühere Version mit einem Teil unserer Sammlung trainiert wurde, obwohl sie über ihr neuestes Modell schweigen (wahrscheinlich auch mit unseren Daten trainiert). Wenn der Westen im Rennen um LLMs und letztendlich AGI die Führung behalten will, muss er seine Haltung zum Urheberrecht überdenken, und zwar bald. Ob Sie uns in unserem moralischen Anliegen zustimmen oder nicht, dies wird nun zu einer Frage der Wirtschaft und sogar der nationalen Sicherheit. Alle Machtblöcke bauen künstliche Superwissenschaftler, Superhacker und Supermilitärs. Informationsfreiheit wird für diese Länder zu einer Überlebensfrage — sogar zu einer Frage der nationalen Sicherheit. Unser Team kommt aus der ganzen Welt, und wir haben keine besondere Ausrichtung. Aber wir würden Länder mit strengen Urheberrechtsgesetzen ermutigen, diese existenzielle Bedrohung zu nutzen, um sie zu reformieren. Was also tun? Unsere erste Empfehlung ist einfach: Verkürzen Sie die Urheberrechtsdauer. In den USA wird das Urheberrecht für 70 Jahre nach dem Tod des Autors gewährt. Das ist absurd. Wir können dies in Einklang mit Patenten bringen, die für 20 Jahre nach der Anmeldung gewährt werden. Dies sollte mehr als genug Zeit für Autoren von Büchern, Aufsätzen, Musik, Kunst und anderen kreativen Werken sein, um vollständig für ihre Bemühungen entschädigt zu werden (einschließlich langfristiger Projekte wie Filmadaptionen). Dann sollten die politischen Entscheidungsträger zumindest Ausnahmen für die Massenbewahrung und -verbreitung von Texten einbeziehen. Wenn entgangene Einnahmen von einzelnen Kunden die Hauptsorge sind, könnte die Verteilung auf persönlicher Ebene weiterhin verboten bleiben. Im Gegenzug würden diejenigen, die in der Lage sind, riesige Sammlungen zu verwalten — Unternehmen, die LLMs trainieren, zusammen mit Bibliotheken und anderen Archiven — von diesen Ausnahmen abgedeckt. Urheberrechtsreform ist notwendig für die nationale Sicherheit Kurz gesagt: Chinesische LLMs (einschließlich DeepSeek) werden mit meinem illegalen Archiv von Büchern und wissenschaftlichen Aufsätzen trainiert — dem größten der Welt. Der Westen muss das Urheberrecht im Interesse der nationalen Sicherheit überarbeiten. Bitte sehen Sie sich den <a %(all_isbns)s>ursprünglichen Blogbeitrag</a> für weitere Informationen an. Wir haben eine Herausforderung gestellt, um dies zu verbessern. Wir würden ein Preisgeld von 6.000 $ für den ersten Platz, 3.000 $ für den zweiten Platz und 1.000 $ für den dritten Platz vergeben. Aufgrund der überwältigenden Resonanz und der unglaublichen Einsendungen haben wir uns entschieden, den Preispool leicht zu erhöhen und vier dritte Plätze mit jeweils 500 $ zu vergeben. Die Gewinner sind unten aufgeführt, aber schauen Sie sich unbedingt alle Einsendungen <a %(annas_archive)s>hier</a> an oder laden Sie unseren <a %(a_2025_01_isbn_visualization_files)s>kombinierten Torrent</a> herunter. Erster Platz 6.000 $: phiresky Diese <a %(phiresky_github)s>Einsendung</a> (<a %(annas_archive_note_2951)s>Gitlab-Kommentar</a>) ist einfach alles, was wir wollten, und mehr! Besonders gefallen haben uns die unglaublich flexiblen Visualisierungsoptionen (sogar mit Unterstützung für benutzerdefinierte Shader), aber mit einer umfassenden Liste von Voreinstellungen. Wir mochten auch, wie schnell und reibungslos alles ist, die einfache Implementierung (die nicht einmal ein Backend hat), die clevere Minikarte und die ausführliche Erklärung in ihrem <a %(phiresky_github)s>Blogbeitrag</a>. Unglaubliche Arbeit und der wohlverdiente Gewinner! - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Unsere Herzen sind voller Dankbarkeit. Bemerkenswerte Ideen Wolkenkratzer für Seltenheit Viele Schieberegler zum Vergleichen von Datasets, als ob Sie ein DJ wären. Maßstabsleiste mit Anzahl der Bücher. Hübsche Beschriftungen. Cooles Standardfarbschema und Heatmap. Einzigartige Kartenansicht und Filter Anmerkungen und auch Live-Statistiken Live-Statistiken Einige weitere Ideen und Implementierungen, die uns besonders gefallen haben: Wir könnten noch eine Weile weitermachen, aber lassen Sie uns hier aufhören. Schauen Sie sich unbedingt alle Einsendungen <a %(annas_archive)s>hier</a> an oder laden Sie unseren <a %(a_2025_01_isbn_visualization_files)s>kombinierten Torrent</a> herunter. So viele Einsendungen, und jede bringt eine einzigartige Perspektive, sei es in der Benutzeroberfläche oder der Implementierung. Wir werden zumindest die Einsendung des ersten Platzes in unsere Hauptwebsite integrieren und vielleicht einige andere. Wir haben auch begonnen, darüber nachzudenken, wie wir den Prozess der Identifizierung, Bestätigung und Archivierung der seltensten Bücher organisieren können. Mehr dazu in Kürze. Danke an alle, die teilgenommen haben. Es ist erstaunlich, dass so viele Menschen sich dafür interessieren. Einfaches Umschalten von Datasets für schnelle Vergleiche. Alle ISBNs CADAL SSNOs CERLALC-Datenleck DuXiu SSIDs EBSCOhost’s eBook-Index Google Books Goodreads Internet Archive ISBNdb ISBN Globales Verzeichnis der Verlage Libby Dateien in Annas Archiv Nexus/STC OCLC/Worldcat OpenLibrary Russische Staatsbibliothek Imperiale Bibliothek von Trantor Zweiter Platz 3.000 $: hypha „Während perfekte Quadrate und Rechtecke mathematisch ansprechend sind, bieten sie in einem Kartierungskontext keine überlegene Lokalität. Ich glaube, dass die Asymmetrie, die diesen Hilbert- oder klassischen Morton-Kurven innewohnt, kein Fehler, sondern ein Merkmal ist. Genau wie Italiens berühmte stiefelförmige Umrisse es auf einer Karte sofort erkennbar machen, können die einzigartigen „Eigenheiten“ dieser Kurven als kognitive Orientierungspunkte dienen. Diese Unverwechselbarkeit kann das räumliche Gedächtnis verbessern und den Benutzern helfen, sich zu orientieren, was möglicherweise das Auffinden bestimmter Regionen oder das Erkennen von Mustern erleichtert.“ Eine weitere unglaubliche <a %(annas_archive_note_2913)s>Einsendung</a>. Nicht so flexibel wie der erste Platz, aber wir bevorzugten tatsächlich die Makroebenen-Visualisierung gegenüber dem ersten Platz (raumfüllende Kurve, Grenzen, Beschriftung, Hervorhebung, Schwenken und Zoomen). Ein <a %(annas_archive_note_2971)s>Kommentar</a> von Joe Davis hat uns besonders angesprochen: Und immer noch viele Optionen zur Visualisierung und Darstellung sowie eine unglaublich flüssige und intuitive Benutzeroberfläche. Ein solider zweiter Platz! - Anna und das Team (<a %(reddit)s>Reddit</a>) Vor ein paar Monaten haben wir ein <a %(all_isbns)s>$10.000 Kopfgeld</a> ausgeschrieben, um die bestmögliche Visualisierung unserer Daten zu erstellen, die den ISBN-Raum zeigt. Wir betonten, welche Dateien wir bereits archiviert haben und welche nicht, und später ein Datensatz, der beschreibt, wie viele Bibliotheken ISBNs besitzen (ein Maß für die Seltenheit). Wir sind von der Resonanz überwältigt. Es gab so viel Kreativität. Ein großes Dankeschön an alle, die teilgenommen haben: Ihre Energie und Begeisterung sind ansteckend! Letztendlich wollten wir die folgenden Fragen beantworten: <strong>Welche Bücher gibt es auf der Welt, wie viele haben wir bereits archiviert und auf welche Bücher sollten wir uns als nächstes konzentrieren?</strong> Es ist großartig zu sehen, dass so viele Menschen sich für diese Fragen interessieren. Wir haben selbst mit einer grundlegenden Visualisierung begonnen. In weniger als 300kb stellt dieses Bild die größte vollständig offene „Liste von Büchern“ dar, die jemals in der Geschichte der Menschheit zusammengestellt wurde: Dritter Platz 500 $ #1: maxlion In dieser <a %(annas_archive_note_2940)s>Einsendung</a> haben uns die verschiedenen Ansichtsarten besonders gefallen, insbesondere die Vergleichs- und Verlagsansichten. Dritter Platz 500 $ #2: abetusk Obwohl die Benutzeroberfläche nicht die ausgereifteste ist, erfüllt diese <a %(annas_archive_note_2917)s>Einsendung</a> viele Kriterien. Besonders gefallen hat uns die Vergleichsfunktion. Dritter Platz 500 $ #3: conundrumer0 Wie der erste Platz hat uns diese <a %(annas_archive_note_2975)s>Einsendung</a> mit ihrer Flexibilität beeindruckt. Letztendlich ist es diese Flexibilität, die ein großartiges Visualisierungstool ausmacht: maximale Flexibilität für Power-User, während es für durchschnittliche Benutzer einfach bleibt. Dritter Platz 500 $ #4: charelf Die letzte <a %(annas_archive_note_2947)s>Einsendung</a>, die ein Preisgeld erhält, ist ziemlich einfach, hat aber einige einzigartige Funktionen, die uns wirklich gefallen haben. Wir mochten, wie sie zeigen, wie viele Datasets eine bestimmte ISBN abdecken, als Maß für Popularität/Zuverlässigkeit. Wir mochten auch die Einfachheit, aber Effektivität der Verwendung eines Opazitätsschiebers für Vergleiche. Gewinner des $10.000 ISBN-Visualisierungswettbewerbs Kurz gesagt: Wir haben einige unglaubliche Einsendungen für den $10.000 ISBN-Visualisierungswettbewerb erhalten. Hintergrund Wie kann Annas Archiv seine Mission erfüllen, das gesamte Wissen der Menschheit zu sichern, ohne zu wissen, welche Bücher noch existieren? Wir brauchen eine TODO-Liste. Eine Möglichkeit, dies zu kartieren, ist durch ISBN-Nummern, die seit den 1970er Jahren jedem veröffentlichten Buch (in den meisten Ländern) zugewiesen wurden. Es gibt keine zentrale Behörde, die alle ISBN-Zuweisungen kennt. Stattdessen ist es ein verteiltes System, bei dem Länder Zahlenbereiche erhalten, die dann kleinere Bereiche an große Verlage vergeben, die diese Bereiche möglicherweise weiter an kleinere Verlage unterteilen. Schließlich werden einzelne Nummern den Büchern zugewiesen. Wir haben vor <a %(blog)s>zwei Jahren</a> mit unserer Erfassung von ISBNdb begonnen, ISBNs zu kartieren. Seitdem haben wir viele weitere Metadatenquellen erfasst, wie <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby und mehr. Eine vollständige Liste finden Sie auf den Seiten „Datasets“ und „Torrents“ in Annas Archiv. Wir haben jetzt bei weitem die größte vollständig offene, leicht herunterladbare Sammlung von Buchmetadaten (und damit ISBNs) der Welt. Wir haben <a %(blog)s>ausführlich darüber geschrieben</a>, warum uns die Erhaltung wichtig ist und warum wir uns derzeit in einem kritischen Zeitfenster befinden. Wir müssen jetzt seltene, wenig beachtete und einzigartig gefährdete Bücher identifizieren und bewahren. Gute Metadaten zu allen Büchern der Welt helfen dabei. 10.000 $ Belohnung Besonderes Augenmerk wird auf die Benutzerfreundlichkeit und das Aussehen gelegt. Zeigen Sie beim Hineinzoomen tatsächliche Metadaten für einzelne ISBNs an, wie Titel und Autor. Bessere Raumfüllkurve. Z.B. ein Zickzack, der in der ersten Reihe von 0 bis 4 geht und dann (umgekehrt) in der zweiten Reihe von 5 bis 9 zurück — rekursiv angewendet. Unterschiedliche oder anpassbare Farbschemata. Spezielle Ansichten zum Vergleichen von Datasets. Möglichkeiten zur Fehlerbehebung bei Problemen, wie z. B. andere metadata, die nicht gut übereinstimmen (z. B. stark abweichende Titel). Bilder mit Kommentaren zu ISBNs oder Bereichen annotieren. Jegliche Heuristiken zur Identifizierung seltener oder gefährdeter Bücher. Welche kreativen Ideen Ihnen auch einfallen! Code Der Code zur Erstellung dieser Bilder sowie weitere Beispiele finden Sie in <a %(annas_archive)s>diesem Verzeichnis</a>. Wir haben ein kompaktes Datenformat entwickelt, mit dem alle erforderlichen ISBN-Informationen etwa 75 MB (komprimiert) umfassen. Die Beschreibung des Datenformats und der Code zu dessen Erstellung finden Sie <a %(annas_archive_l1244_1319)s>hier</a>. Für die Prämie sind Sie nicht verpflichtet, dies zu verwenden, aber es ist wahrscheinlich das bequemste Format, um damit zu beginnen. Sie können unsere metadata nach Belieben transformieren (obwohl Ihr gesamter Code Open Source sein muss). Wir können es kaum erwarten zu sehen, was Sie sich einfallen lassen. Viel Glück! Forken Sie dieses Repository und bearbeiten Sie diesen Blogpost-HTML (keine anderen Backends außer unserem Flask-Backend sind erlaubt). Machen Sie das obige Bild nahtlos zoombar, sodass Sie bis zu einzelnen ISBNs hineinzoomen können. Ein Klick auf ISBNs sollte Sie zu einer Metadatenseite oder einer Suche in Annas Archiv führen. Sie müssen weiterhin zwischen allen verschiedenen Datasets wechseln können. Länderbereiche und Verlagsbereiche sollten beim Überfahren hervorgehoben werden. Sie können z.B. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> für Länderinformationen und unseren „isbngrp“-Scrape für Verlage (<a %(annas_archive)s>Dataset</a>, <a %(annas_archive_2)s>Torrent</a>) verwenden. Es muss sowohl auf Desktop als auch auf Mobilgeräten gut funktionieren. Es gibt hier viel zu erkunden, daher kündigen wir eine Belohnung für die Verbesserung der obigen Visualisierung an. Im Gegensatz zu den meisten unserer Belohnungen ist diese zeitlich begrenzt. Sie müssen Ihren Open-Source-Code bis zum 31.01.2025 (23:59 UTC) <a %(annas_archive)s>einreichen</a>. Die beste Einreichung erhält 6.000 $, der zweite Platz 3.000 $ und der dritte Platz 1.000 $. Alle Belohnungen werden in Monero (XMR) ausgezahlt. Unten sind die Mindestkriterien aufgeführt. Wenn keine Einreichung die Kriterien erfüllt, könnten wir dennoch einige Belohnungen vergeben, aber das liegt in unserem Ermessen. Für Bonuspunkte (dies sind nur Ideen — lassen Sie Ihrer Kreativität freien Lauf): Sie DÜRFEN vollständig von den minimalen Kriterien abweichen und eine völlig andere Visualisierung erstellen. Wenn sie wirklich spektakulär ist, qualifiziert sie sich für die Prämie, aber nach unserem Ermessen. Reichen Sie Beiträge ein, indem Sie einen Kommentar zu <a %(annas_archive)s>diesem Problem</a> mit einem Link zu Ihrem geforkten Repository, Merge-Request oder Diff posten. - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dieses Bild ist 1000×800 Pixel groß. Jeder Pixel repräsentiert 2.500 ISBNs. Wenn wir eine Datei für eine ISBN haben, machen wir diesen Pixel grüner. Wenn wir wissen, dass eine ISBN vergeben wurde, aber keine passende Datei haben, machen wir ihn röter. In weniger als 300kb repräsentiert dieses Bild prägnant die größte vollständig offene „Liste von Büchern“, die jemals in der Geschichte der Menschheit zusammengestellt wurde (einige hundert GB vollständig komprimiert). Es zeigt auch: Es gibt noch viel Arbeit beim Sichern von Büchern (wir haben nur 16%). Visualisierung aller ISBNs — 10.000 $ Belohnung bis 2025-01-31 Dieses Bild repräsentiert die größte vollständig offene „Liste von Büchern“, die jemals in der Geschichte der Menschheit zusammengestellt wurde. Visualisierung Neben dem Übersichtsbild können wir uns auch einzelne Datasets ansehen, die wir erworben haben. Verwenden Sie das Dropdown-Menü und die Schaltflächen, um zwischen ihnen zu wechseln. In diesen Bildern gibt es viele interessante Muster zu entdecken. Warum gibt es eine gewisse Regelmäßigkeit von Linien und Blöcken, die auf verschiedenen Skalen zu passieren scheint? Was sind die leeren Bereiche? Warum sind bestimmte Datasets so stark gebündelt? Wir überlassen diese Fragen dem Leser als Übung. - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Fazit Mit diesem Standard können wir Veröffentlichungen schrittweise vornehmen und leichter neue Datenquellen hinzufügen. Wir haben bereits einige spannende Veröffentlichungen in der Pipeline! Wir hoffen auch, dass es für andere Schattenbibliotheken einfacher wird, unsere Sammlungen zu spiegeln. Schließlich ist es unser Ziel, menschliches Wissen und Kultur für immer zu bewahren, daher gilt: Je mehr Redundanz, desto besser. Beispiel Schauen wir uns als Beispiel unsere aktuelle Z-Library-Veröffentlichung an. Sie besteht aus zwei Sammlungen: „<span style="background: #fffaa3">zlib3_records</span>“ und „<span style="background: #ffd6fe">zlib3_files</span>“. Dies ermöglicht es uns, Metadaten-Datensätze separat von den eigentlichen Buchdateien zu extrahieren und zu veröffentlichen. Daher haben wir zwei Torrents mit Metadaten-Dateien veröffentlicht: Wir haben auch eine Reihe von Torrents mit Binärdaten-Ordnern veröffentlicht, jedoch nur für die Sammlung „<span style="background: #ffd6fe">zlib3_files</span>“, insgesamt 62: Durch Ausführen von <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> können wir sehen, was sich darin befindet: In diesem Fall handelt es sich um Metadaten eines Buches, wie von Z-Library gemeldet. Auf der obersten Ebene haben wir nur „aacid“ und „metadata“, aber keinen „data_folder“, da es keine entsprechenden Binärdaten gibt. Das AACID enthält „22430000“ als primäre ID, die wir sehen können, dass sie von „zlibrary_id“ übernommen wurde. Wir können erwarten, dass andere AACs in dieser Sammlung die gleiche Struktur haben. Lassen Sie uns nun <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> ausführen: Dies ist ein viel kleineres AAC-Metadaten, obwohl der Großteil dieses AAC anderswo in einer Binärdatei gespeichert ist! Schließlich haben wir diesmal einen „data_folder“, sodass wir erwarten können, dass die entsprechenden Binärdaten unter <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> zu finden sind. Die „metadata“ enthält die „zlibrary_id“, sodass wir sie leicht mit dem entsprechenden AAC in der „zlib_records“-Sammlung verknüpfen können. Wir hätten auf verschiedene Weise verknüpfen können, z. B. über AACID — der Standard schreibt das nicht vor. Beachten Sie, dass es auch nicht notwendig ist, dass das „metadata“-Feld selbst JSON ist. Es könnte ein String sein, der XML oder ein anderes Datenformat enthält. Sie könnten sogar Metadateninformationen im zugehörigen Binärblob speichern, z. B. wenn es sich um eine große Datenmenge handelt. Heterogene Dateien und Metadaten, so nah wie möglich am Originalformat. Binärdaten können direkt von Webservern wie Nginx bereitgestellt werden. Heterogene Identifikatoren in den Quellbibliotheken oder sogar das Fehlen von Identifikatoren. Separate Veröffentlichungen von Metadaten vs. Dateidaten oder nur Metadaten-Veröffentlichungen (z. B. unsere ISBNdb-Veröffentlichung). Verteilung über Torrents, jedoch mit der Möglichkeit anderer Verteilungsmethoden (z. B. IPFS). Unveränderliche Aufzeichnungen, da wir davon ausgehen sollten, dass unsere Torrents für immer bestehen bleiben. Inkrementelle Veröffentlichungen / anhängbare Veröffentlichungen. Maschinenlesbar und -schreibbar, bequem und schnell, insbesondere für unseren Stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Einigermaßen einfache menschliche Inspektion, obwohl dies sekundär zur Maschinenlesbarkeit ist. Einfach, unsere Sammlungen mit einer standardmäßig gemieteten Seedbox zu seeden. Designziele Es ist uns egal, ob Dateien manuell auf der Festplatte leicht zu navigieren sind oder ohne Vorverarbeitung durchsuchbar sind. Es ist uns egal, ob sie direkt mit bestehender Bibliothekssoftware kompatibel sind. Obwohl es einfach sein sollte, unsere Sammlung mit Torrents zu seeden, erwarten wir nicht, dass die Dateien ohne erhebliches technisches Wissen und Engagement nutzbar sind. Unser primärer Anwendungsfall ist die Verteilung von Dateien und zugehörigen Metadaten aus verschiedenen bestehenden Sammlungen. Unsere wichtigsten Überlegungen sind: Einige Nicht-Ziele: Da Annas Archiv Open Source ist, möchten wir unser Format direkt selbst nutzen. Wenn wir unseren Suchindex aktualisieren, greifen wir nur auf öffentlich zugängliche Pfade zu, damit jeder, der unsere Bibliothek forkt, schnell loslegen kann. <strong>AAC.</strong> AAC (Anna’s Archiv Container) ist ein einzelnes Element, das aus <strong>Metadaten</strong> und optional <strong>Binärdaten</strong> besteht, die beide unveränderlich sind. Es hat einen weltweit eindeutigen Bezeichner, genannt <strong>AACID</strong>. <strong>AACID.</strong> Das Format von AACID ist folgendes: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Zum Beispiel ist ein tatsächlicher AACID, den wir veröffentlicht haben, <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-Bereich.</strong> Da AACIDs monoton ansteigende Zeitstempel enthalten, können wir diese verwenden, um Bereiche innerhalb einer bestimmten Sammlung zu kennzeichnen. Wir verwenden dieses Format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wobei die Zeitstempel inklusive sind. Dies ist konsistent mit der ISO 8601-Notation. Bereiche sind kontinuierlich und können sich überlappen, müssen im Falle einer Überlappung jedoch identische Datensätze wie die zuvor in dieser Sammlung veröffentlichten enthalten (da AACs unveränderlich sind). Fehlende Datensätze sind nicht erlaubt. <code>{collection}</code>: der Sammlungsname, der ASCII-Buchstaben, Zahlen und Unterstriche enthalten kann (aber keine doppelten Unterstriche). <code>{collection-specific ID}</code>: ein sammlungsspezifischer Bezeichner, falls zutreffend, z. B. die Z-Library ID. Kann weggelassen oder gekürzt werden. Muss weggelassen oder gekürzt werden, wenn der AACID sonst 150 Zeichen überschreiten würde. <code>{ISO 8601 timestamp}</code>: eine kurze Version des ISO 8601, immer in UTC, z. B. <code>20220723T194746Z</code>. Diese Zahl muss für jede Veröffentlichung monoton ansteigen, obwohl ihre genauen Semantiken je nach Sammlung unterschiedlich sein können. Wir empfehlen, die Zeit des Scrapings oder der ID-Erstellung zu verwenden. <code>{shortuuid}</code>: eine UUID, aber komprimiert zu ASCII, z. B. mit base57. Wir verwenden derzeit die <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-Bibliothek. <strong>Binärdatenordner.</strong> Ein Ordner mit den Binärdaten eines Bereichs von AACs für eine bestimmte Sammlung. Diese haben die folgenden Eigenschaften: Das Verzeichnis muss Datendateien für alle AACs innerhalb des angegebenen Bereichs enthalten. Jede Datendatei muss ihren AACID als Dateinamen haben (keine Erweiterungen). Der Verzeichnisname muss ein AACID-Bereich sein, der mit <code style="color: green">annas_archive_data__</code> beginnt und keinen Suffix hat. Zum Beispiel hat eine unserer tatsächlichen Veröffentlichungen ein Verzeichnis namens<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Es wird empfohlen, diese Ordner in einer einigermaßen handhabbaren Größe zu halten, z. B. nicht größer als 100GB-1TB pro Ordner, obwohl sich diese Empfehlung im Laufe der Zeit ändern kann. <strong>Sammlung.</strong> Jedes AAC gehört zu einer Sammlung, die per Definition eine Liste von AACs ist, die semantisch konsistent sind. Das bedeutet, dass wenn Sie eine wesentliche Änderung am Format der Metadaten vornehmen, Sie eine neue Sammlung erstellen müssen. Der Standard <strong>Metadatendatei.</strong> Eine Metadatendatei enthält die Metadaten eines Bereichs von AACs für eine bestimmte Sammlung. Diese haben die folgenden Eigenschaften: <code>data_folder</code> ist optional und ist der Name des Binärdatenordners, der die entsprechenden Binärdaten enthält. Der Dateiname der entsprechenden Binärdaten innerhalb dieses Ordners ist der AACID des Datensatzes. Jedes JSON-Objekt muss die folgenden Felder auf der obersten Ebene enthalten: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). Keine anderen Felder sind erlaubt. Der Dateiname muss ein AACID-Bereich sein, der mit <code style="color: red">annas_archive_meta__</code> beginnt und mit <code>.jsonl.zstd</code> endet. Zum Beispiel heißt eine unserer Veröffentlichungen<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Wie durch die Dateierweiterung angegeben, ist der Dateityp <a %(jsonlines)s>JSON Lines</a>, komprimiert mit <a %(zstd)s>Zstandard</a>. <code>metadata</code> sind beliebige Metadaten, gemäß den Semantiken der Sammlung. Sie müssen innerhalb der Sammlung semantisch konsistent sein. Das <code style="color: red">annas_archive_meta__</code> Präfix kann an den Namen Ihrer Institution angepasst werden, z. B. <code style="color: red">my_institute_meta__</code>. <strong>„Datensätze“ und „Dateien“ Sammlungen.</strong> Üblicherweise ist es oft praktisch, „Datensätze“ und „Dateien“ als verschiedene Sammlungen zu veröffentlichen, damit sie zu unterschiedlichen Zeitplänen veröffentlicht werden können, z. B. basierend auf Scraping-Raten. Ein „Datensatz“ ist eine Sammlung, die nur Metadaten enthält, wie Buchtitel, Autoren, ISBNs usw., während „Dateien“ die Sammlungen sind, die die eigentlichen Dateien selbst enthalten (pdf, epub). Letztendlich haben wir uns auf einen relativ einfachen Standard geeinigt. Er ist ziemlich locker, nicht normativ und ein fortlaufendes Projekt. <strong>Torrents.</strong> Die Metadaten-Dateien und Binärdaten-Ordner können in Torrents gebündelt werden, mit einem Torrent pro Metadaten-Datei oder einem Torrent pro Binärdaten-Ordner. Die Torrents müssen den ursprünglichen Datei-/Verzeichnisnamen plus ein <code>.torrent</code> Suffix als Dateinamen haben. <a %(wikipedia_annas_archive)s>Annas Archiv</a> ist mit Abstand die größte Schattenbibliothek der Welt geworden und die einzige Schattenbibliothek dieser Größenordnung, die vollständig Open Source und Open Data ist. Unten ist eine Tabelle von unserer Datasets-Seite (leicht modifiziert): Wir haben dies auf drei Arten erreicht: Spiegelung bestehender Open-Data-Schattenbibliotheken (wie Sci-Hub und Library Genesis). Unterstützung von Schattenbibliotheken, die offener sein möchten, aber nicht die Zeit oder Ressourcen dazu hatten (wie die Libgen-Comics-Sammlung). Scraping von Bibliotheken, die nicht in großen Mengen teilen möchten (wie Z-Library). Für (2) und (3) verwalten wir nun selbst eine beträchtliche Sammlung von Torrents (Hunderte von TBs). Bisher haben wir diese Sammlungen als Einzelstücke behandelt, was bedeutet, dass für jede Sammlung eine maßgeschneiderte Infrastruktur und Datenorganisation erforderlich ist. Dies führt zu einem erheblichen Mehraufwand bei jeder Veröffentlichung und erschwert insbesondere inkrementelle Veröffentlichungen. Deshalb haben wir uns entschieden, unsere Veröffentlichungen zu standardisieren. Dies ist ein technischer Blogbeitrag, in dem wir unseren Standard vorstellen: <strong>Annas Archiv-Container</strong>. Annas Archiv-Container (AAC): Standardisierung von Veröffentlichungen aus der weltweit größten Schattenbibliothek Annas Archiv ist zur größten Schattenbibliothek der Welt geworden, was uns dazu zwingt, unsere Veröffentlichungen zu standardisieren. 300GB+ an Buchcovern veröffentlicht Schließlich freuen wir uns, eine kleine Veröffentlichung bekannt zu geben. In Zusammenarbeit mit den Leuten, die den Libgen.rs-Fork betreiben, teilen wir alle ihre Buchcover über Torrents und IPFS. Dies wird die Last des Betrachtens der Cover auf mehr Maschinen verteilen und sie besser bewahren. In vielen (aber nicht allen) Fällen sind die Buchcover in den Dateien selbst enthalten, sodass dies eine Art „abgeleitete Daten“ ist. Aber sie in IPFS zu haben, ist immer noch sehr nützlich für den täglichen Betrieb sowohl von Annas Archiv als auch der verschiedenen Library Genesis-Forks. Wie üblich finden Sie diese Veröffentlichung im Pirate Library Mirror (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Wir werden hier nicht darauf verlinken, aber Sie können es leicht finden. Hoffentlich können wir unser Tempo ein wenig entspannen, jetzt, da wir eine anständige Alternative zur Z-Library haben. Diese Arbeitsbelastung ist nicht besonders nachhaltig. Wenn Sie daran interessiert sind, bei der Programmierung, dem Serverbetrieb oder der Erhaltungsarbeit zu helfen, kontaktieren Sie uns unbedingt. Es gibt noch viel <a %(annas_archive)s>zu tun</a>. Vielen Dank für Ihr Interesse und Ihre Unterstützung. Wechsel zu ElasticSearch Einige Abfragen dauerten extrem lange, bis zu dem Punkt, an dem sie alle offenen Verbindungen blockierten. Standardmäßig hat MySQL eine Mindestwortlänge, oder Ihr Index kann wirklich groß werden. Es wurde berichtet, dass man nicht nach „Ben Hur“ suchen konnte. Die Suche war nur dann einigermaßen schnell, wenn sie vollständig im Speicher geladen war, was uns dazu zwang, eine teurere Maschine zu verwenden, um dies auszuführen, plus einige Befehle, um den Index beim Start vorzuladen. Wir hätten es nicht leicht erweitern können, um neue Funktionen zu entwickeln, wie bessere <a %(wikipedia_cjk_characters)s>Tokenisierung für nicht-weißraumgetrennte Sprachen</a>, Filterung/Facettierung, Sortierung, "Meinten Sie"-Vorschläge, Autovervollständigung und so weiter. Eines unserer <a %(annas_archive)s>Tickets</a> war eine Sammlung von Problemen mit unserem Suchsystem. Wir verwendeten die MySQL-Volltextsuche, da wir alle unsere Daten ohnehin in MySQL hatten. Aber es hatte seine Grenzen: Nach Gesprächen mit einer Reihe von Experten haben wir uns für ElasticSearch entschieden. Es war nicht perfekt (ihre Standard-"Meinten Sie"-Vorschläge und Autovervollständigungsfunktionen sind schlecht), aber insgesamt war es viel besser als MySQL für die Suche. Wir sind immer noch nicht <a %(youtube)s>allzu begeistert</a>, es für mission-kritische Daten zu verwenden (obwohl sie viel <a %(elastic_co)s>Fortschritte</a> gemacht haben), aber insgesamt sind wir mit dem Wechsel recht zufrieden. Für den Moment haben wir eine viel schnellere Suche, bessere Sprachunterstützung, bessere Relevanzsortierung, verschiedene Sortieroptionen und Filterung nach Sprache/Buchtyp/Dateityp implementiert. Wenn Sie neugierig sind, wie es funktioniert, <a %(annas_archive_l140)s>schauen</a> <a %(annas_archive_l1115)s>Sie</a> <a %(annas_archive_l1635)s>es sich an</a>. Es ist ziemlich zugänglich, obwohl es noch einige Kommentare gebrauchen könnte… Annas Archiv ist vollständig quelloffen Wir glauben, dass Informationen frei sein sollten, und unser eigener Code ist da keine Ausnahme. Wir haben unseren gesamten Code auf unserer privat gehosteten Gitlab-Instanz veröffentlicht: <a %(annas_archive)s>Annas Software</a>. Wir nutzen auch den Issue-Tracker, um unsere Arbeit zu organisieren. Wenn Sie sich an unserer Entwicklung beteiligen möchten, ist dies ein großartiger Ausgangspunkt. Um Ihnen einen Vorgeschmack auf die Dinge zu geben, an denen wir arbeiten, nehmen Sie unsere jüngsten Arbeiten zur Verbesserung der Client-seitigen Leistung. Da wir noch keine Paginierung implementiert haben, würden wir oft sehr lange Suchseiten mit 100-200 Ergebnissen zurückgeben. Wir wollten die Suchergebnisse nicht zu früh abschneiden, aber das bedeutete, dass es einige Geräte verlangsamen würde. Dafür haben wir einen kleinen Trick implementiert: Wir haben die meisten Suchergebnisse in HTML-Kommentare (<code><!-- --></code>) eingewickelt und dann ein kleines Javascript geschrieben, das erkennt, wann ein Ergebnis sichtbar werden sollte, und in diesem Moment den Kommentar auspackt: DOM-"Virtualisierung" in 23 Zeilen implementiert, keine Notwendigkeit für ausgefallene Bibliotheken! Dies ist die Art von pragmatischem Code, die entsteht, wenn man wenig Zeit hat und reale Probleme gelöst werden müssen. Es wurde berichtet, dass unsere Suche jetzt auch auf langsamen Geräten gut funktioniert! Ein weiterer großer Aufwand war die Automatisierung des Datenbankaufbaus. Als wir starteten, zogen wir einfach wahllos verschiedene Quellen zusammen. Jetzt wollen wir sie aktuell halten, also haben wir eine Reihe von Skripten geschrieben, um neue Metadata von den beiden Library Genesis-Forks herunterzuladen und zu integrieren. Das Ziel ist es, dies nicht nur für unser Archiv nützlich zu machen, sondern es auch jedem zu erleichtern, der mit Schattenbibliothek-Metadata experimentieren möchte. Das Ziel wäre ein Jupyter-Notebook, das alle möglichen interessanten Metadata enthält, damit wir mehr Forschung betreiben können, wie zum Beispiel herauszufinden, welcher <a %(blog)s>Prozentsatz der ISBNs für immer erhalten bleibt</a>. Schließlich haben wir unser Spenden-System überarbeitet. Sie können jetzt eine Kreditkarte verwenden, um direkt Geld in unsere Krypto-Wallets einzuzahlen, ohne wirklich etwas über Kryptowährungen wissen zu müssen. Wir werden weiterhin beobachten, wie gut das in der Praxis funktioniert, aber das ist ein großer Schritt. Da Z-Library offline gegangen ist und seine (angeblichen) Gründer verhaftet wurden, haben wir rund um die Uhr gearbeitet, um mit Annas Archiv eine gute Alternative bereitzustellen (wir werden es hier nicht verlinken, aber Sie können es googeln). Hier sind einige der Dinge, die wir kürzlich erreicht haben. Annas Update: vollständig quelloffenes Archiv, ElasticSearch, über 300GB an Buchcovern Wir haben rund um die Uhr gearbeitet, um mit Annas Archiv eine gute Alternative bereitzustellen. Hier sind einige der Dinge, die wir kürzlich erreicht haben. Analyse Semantische Duplikate (verschiedene Scans desselben Buches) können theoretisch herausgefiltert werden, aber es ist knifflig. Beim manuellen Durchsehen der Comics fanden wir zu viele Fehlalarme. Es gibt einige Duplikate rein nach MD5, was relativ verschwenderisch ist, aber das Herausfiltern dieser würde uns nur etwa 1% in Einsparungen bringen. In diesem Maßstab sind das immer noch etwa 1TB, aber auch in diesem Maßstab spielt 1TB nicht wirklich eine Rolle. Wir möchten nicht riskieren, versehentlich Daten in diesem Prozess zu zerstören. Wir fanden eine Menge nicht-buchbezogener Daten, wie Filme, die auf Comics basieren. Das scheint auch verschwenderisch, da diese bereits auf andere Weise weit verbreitet sind. Wir erkannten jedoch, dass wir Filmdateien nicht einfach herausfiltern konnten, da es auch <em>interaktive Comics</em> gibt, die auf dem Computer veröffentlicht wurden und die jemand aufgenommen und als Filme gespeichert hat. Letztendlich würde das Löschen von Teilen der Sammlung nur ein paar Prozent einsparen. Dann erinnerten wir uns daran, dass wir Datenhorter sind, und die Leute, die dies spiegeln werden, sind ebenfalls Datenhorter, und so: „WAS MEINST DU MIT LÖSCHEN?!“ :) Wenn Sie 95TB in Ihr Speichersystem geworfen bekommen, versuchen Sie herauszufinden, was überhaupt darin ist… Wir haben einige Analysen durchgeführt, um zu sehen, ob wir die Größe ein wenig reduzieren könnten, zum Beispiel durch das Entfernen von Duplikaten. Hier sind einige unserer Erkenntnisse: Wir präsentieren Ihnen daher die vollständige, unveränderte Sammlung. Es sind viele Daten, aber wir hoffen, dass sich genug Leute finden, die sie trotzdem seeden. Zusammenarbeit Aufgrund ihrer Größe stand diese Sammlung schon lange auf unserer Wunschliste, also nahmen wir sie nach unserem Erfolg mit dem Backup von Z-Library ins Visier. Zunächst haben wir sie direkt gescraped, was eine ziemliche Herausforderung war, da ihr Server nicht in bestem Zustand war. Auf diese Weise erhielten wir etwa 15TB, aber es ging nur langsam voran. Glücklicherweise gelang es uns, den Betreiber der Bibliothek zu kontaktieren, der sich bereit erklärte, uns alle Daten direkt zu senden, was viel schneller war. Es dauerte dennoch mehr als ein halbes Jahr, um alle Daten zu übertragen und zu verarbeiten, und wir hätten fast alles durch Festplattenkorruption verloren, was bedeutet hätte, von vorne zu beginnen. Diese Erfahrung hat uns glauben lassen, dass es wichtig ist, diese Daten so schnell wie möglich zu verbreiten, damit sie weit und breit gespiegelt werden können. Wir sind nur ein oder zwei unglücklich getimte Vorfälle davon entfernt, diese Sammlung für immer zu verlieren! Die Sammlung Schnelles Handeln bedeutet jedoch, dass die Sammlung ein wenig unorganisiert ist… Schauen wir uns das mal an. Stellen Sie sich vor, wir haben ein Dateisystem (das wir in Wirklichkeit über Torrents aufteilen): Das erste Verzeichnis, <code>/repository</code>, ist der strukturiertere Teil davon. Dieses Verzeichnis enthält sogenannte „Tausender-Verzeichnisse“: Verzeichnisse, die jeweils tausend Dateien enthalten, die in der Datenbank fortlaufend nummeriert sind. Verzeichnis <code>0</code> enthält Dateien mit comic_id 0–999 und so weiter. Dies ist das gleiche Schema, das Library Genesis für seine Belletristik- und Sachbuchsammlungen verwendet hat. Die Idee ist, dass jedes „Tausender-Verzeichnis“ automatisch in einen Torrent umgewandelt wird, sobald es gefüllt ist. Der Libgen.li-Betreiber hat jedoch nie Torrents für diese Sammlung erstellt, und so wurden die Tausender-Verzeichnisse wahrscheinlich unpraktisch und machten Platz für „unsortierte Verzeichnisse“. Diese sind <code>/comics0</code> bis <code>/comics4</code>. Sie alle enthalten einzigartige Verzeichnisstrukturen, die wahrscheinlich beim Sammeln der Dateien sinnvoll waren, aber für uns jetzt nicht mehr viel Sinn ergeben. Glücklicherweise verweist das metadata immer noch direkt auf all diese Dateien, sodass ihre Speicherorganisation auf der Festplatte eigentlich keine Rolle spielt! Das metadata ist in Form einer MySQL-Datenbank verfügbar. Diese kann direkt von der Libgen.li-Website heruntergeladen werden, aber wir werden sie auch in einem Torrent verfügbar machen, zusammen mit unserer eigenen Tabelle mit allen MD5-Hashes. <q>Dr. Barbara Gordon versucht, sich in der alltäglichen Welt der Bibliothek zu verlieren…</q> Libgen-Forks Zunächst ein wenig Hintergrundwissen. Sie kennen Library Genesis vielleicht wegen ihrer epischen Büchersammlung. Weniger Menschen wissen, dass die Freiwilligen von Library Genesis andere Projekte ins Leben gerufen haben, wie zum Beispiel eine umfangreiche Sammlung von Zeitschriften und Standarddokumenten, ein vollständiges Backup von Sci-Hub (in Zusammenarbeit mit der Gründerin von Sci-Hub, Alexandra Elbakyan) und tatsächlich eine riesige Sammlung von Comics. Irgendwann gingen die verschiedenen Betreiber der Library Genesis-Spiegel getrennte Wege, was zur aktuellen Situation führte, in der es eine Reihe verschiedener „Forks“ gibt, die alle noch den Namen Library Genesis tragen. Der Libgen.li-Fork hat einzigartig diese Comics-Sammlung sowie eine umfangreiche Zeitschriftensammlung (an der wir ebenfalls arbeiten). Spendenaktion Wir veröffentlichen diese Daten in einigen großen Paketen. Der erste Torrent ist von <code>/comics0</code>, den wir in eine riesige 12TB .tar-Datei gepackt haben. Das ist besser für Ihre Festplatte und Torrent-Software als unzählige kleinere Dateien. Im Rahmen dieser Veröffentlichung führen wir eine Spendenaktion durch. Wir möchten 20.000 $ sammeln, um die Betriebs- und Vertragskosten für diese Sammlung zu decken und laufende sowie zukünftige Projekte zu ermöglichen. Wir haben einige <em>riesige</em> in Arbeit. <em>Wen unterstütze ich mit meiner Spende?</em> Kurz gesagt: Wir sichern das gesamte Wissen und die Kultur der Menschheit und machen es leicht zugänglich. All unser Code und unsere Daten sind Open Source, wir sind ein komplett ehrenamtlich geführtes Projekt und haben bisher Bücher im Wert von 125TB gerettet (zusätzlich zu den bestehenden Torrents von Libgen und Scihub). Letztendlich bauen wir ein Schwungrad, das Menschen dazu befähigt und motiviert, alle Bücher der Welt zu finden, zu scannen und zu sichern. Wir werden in einem zukünftigen Beitrag über unseren Masterplan schreiben. :) Wenn Sie für eine 12-monatige „Amazing Archivist“-Mitgliedschaft (780 $) spenden, können Sie <strong>„einen Torrent adoptieren“</strong>, was bedeutet, dass wir Ihren Benutzernamen oder Ihre Nachricht im Dateinamen eines der Torrents platzieren! Sie können spenden, indem Sie zu <a %(wikipedia_annas_archive)s>Annas Archiv</a> gehen und auf die Schaltfläche „Spenden“ klicken. Wir suchen auch nach weiteren Freiwilligen: Software-Ingenieure, Sicherheitsforscher, Experten für anonyme Händler und Übersetzer. Sie können uns auch unterstützen, indem Sie Hosting-Dienste bereitstellen. Und natürlich, bitte seeden Sie unsere Torrents! Vielen Dank an alle, die uns bereits so großzügig unterstützt haben! Sie machen wirklich einen Unterschied. Hier sind die bisher veröffentlichten Torrents (wir verarbeiten den Rest noch): Alle Torrents finden Sie auf <a %(wikipedia_annas_archive)s>Annas Archiv</a> unter „Datasets“ (wir verlinken dort nicht direkt, damit Links zu diesem Blog nicht von Reddit, Twitter usw. entfernt werden). Von dort aus folgen Sie dem Link zur Tor-Website. <a %(news_ycombinator)s>Diskutieren Sie auf Hacker News</a> Was kommt als Nächstes? Eine Menge Torrents sind großartig für die langfristige Bewahrung, aber nicht so sehr für den täglichen Zugriff. Wir werden mit Hosting-Partnern zusammenarbeiten, um all diese Daten im Web verfügbar zu machen (da Annas Archiv nichts direkt hostet). Natürlich werden Sie diese Download-Links auf Annas Archiv finden können. Wir laden auch alle ein, etwas mit diesen Daten zu machen! Helfen Sie uns, sie besser zu analysieren, zu deduplizieren, auf IPFS zu stellen, sie zu remixen, Ihre KI-Modelle damit zu trainieren und so weiter. Es gehört alles Ihnen, und wir können es kaum erwarten zu sehen, was Sie damit machen. Schließlich, wie bereits gesagt, haben wir noch einige massive Veröffentlichungen in Vorbereitung (wenn <em>jemand</em> uns <em>versehentlich</em> einen Dump einer <em>bestimmten</em> ACS4-Datenbank senden könnte, wissen Sie, wo Sie uns finden…), sowie den Aufbau des Schwungrads zur Sicherung aller Bücher der Welt. Bleiben Sie also dran, wir fangen gerade erst an. - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Die größte Schattenbibliothek für Comics ist wahrscheinlich die eines bestimmten Library Genesis-Forks: Libgen.li. Der eine Administrator, der diese Seite betreibt, hat es geschafft, eine unglaubliche Comics-Sammlung von über 2 Millionen Dateien zu sammeln, die insgesamt über 95TB umfassen. Im Gegensatz zu anderen Library Genesis-Sammlungen war diese jedoch nicht in großen Mengen über Torrents verfügbar. Sie konnten auf diese Comics nur einzeln über seinen langsamen persönlichen Server zugreifen — ein einziger Schwachpunkt. Bis heute! In diesem Beitrag erzählen wir Ihnen mehr über diese Sammlung und über unsere Spendenaktion, um mehr von dieser Arbeit zu unterstützen. Annas Archiv hat die weltweit größte Comics-Schattenbibliothek (95TB) gesichert — Sie können helfen, sie zu verbreiten Die größte Comics-Schattenbibliothek der Welt hatte einen einzigen Schwachpunkt... bis heute. Warnung: Dieser Blogbeitrag ist veraltet. Wir haben entschieden, dass IPFS noch nicht bereit für den Einsatz ist. Wir werden weiterhin auf Dateien auf IPFS von Annas Archiv verlinken, wenn möglich, aber wir werden es nicht mehr selbst hosten, noch empfehlen wir anderen, mit IPFS zu spiegeln. Bitte sehen Sie sich unsere Torrents-Seite an, wenn Sie helfen möchten, unsere Sammlung zu erhalten. 5.998.794 Bücher auf IPFS stellen Eine Vervielfältigung von Kopien Zurück zu unserer ursprünglichen Frage: Wie können wir behaupten, unsere Sammlungen auf Dauer zu bewahren? Das Hauptproblem hier ist, dass unsere Sammlung <a %(torrents_stats)s>schnell wächst</a>, indem wir einige massive Sammlungen scrapen und als Open Source bereitstellen (zusätzlich zu der großartigen Arbeit, die bereits von anderen Open-Data-Schattenbibliotheken wie Sci-Hub und Library Genesis geleistet wurde). Dieses Datenwachstum erschwert es, die Sammlungen weltweit zu spiegeln. Datenspeicherung ist teuer! Aber wir sind optimistisch, besonders wenn wir die folgenden drei Trends beobachten. Die <a %(annas_archive_stats)s>Gesamtgröße</a> unserer Sammlungen, in den letzten Monaten aufgeschlüsselt nach der Anzahl der Torrent-Seeder. HDD-Preistrends aus verschiedenen Quellen (zum Ansehen der Studie klicken). <a %(critical_window_chinese)s>Chinesische Version 中文版</a>, diskutieren Sie auf <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Wir haben die leicht zugänglichen Früchte gepflückt Dies folgt direkt aus unseren oben diskutierten Prioritäten. Wir ziehen es vor, zuerst an der Befreiung großer Sammlungen zu arbeiten. Jetzt, da wir einige der größten Sammlungen der Welt gesichert haben, erwarten wir, dass unser Wachstum viel langsamer sein wird. Es gibt immer noch einen langen Schwanz kleinerer Sammlungen, und jeden Tag werden neue Bücher gescannt oder veröffentlicht, aber die Rate wird wahrscheinlich viel langsamer sein. Wir könnten uns immer noch verdoppeln oder sogar verdreifachen, aber über einen längeren Zeitraum. Verbesserungen bei der OCR. Prioritäten Wissenschafts- und Ingenieursoftware-Code Fiktionale oder unterhaltende Versionen all dieser Kategorien Geografische Daten (z. B. Karten, geologische Erhebungen) Interne Daten von Unternehmen oder Regierungen (Lecks) Messdaten wie wissenschaftliche Messungen, Wirtschaftsdaten, Unternehmensberichte Metadatenaufzeichnungen im Allgemeinen (von Sach- und Belletristik; von anderen Medien, Kunst, Personen usw.; einschließlich Rezensionen) Sachbücher Sachzeitschriften, Zeitungen, Handbücher Sachtranskripte von Vorträgen, Dokumentationen, Podcasts Organische Daten wie DNA-Sequenzen, Pflanzensamen oder mikrobielle Proben Wissenschaftliche Aufsätze, Zeitschriften, Berichte Wissenschafts- und Ingenieurwebsites, Online-Diskussionen Transkripte von rechtlichen oder gerichtlichen Verfahren Einzigartig gefährdet sind (z. B. durch Krieg, Budgetkürzungen, Klagen oder politische Verfolgung) Selten Einzigartig unterfokussiert Warum kümmern wir uns so sehr um wissenschaftliche Aufsätze und Bücher? Lassen Sie uns unseren grundlegenden Glauben an die Bewahrung im Allgemeinen beiseitelegen — wir könnten einen weiteren Beitrag darüber schreiben. Warum also speziell wissenschaftliche Aufsätze und Bücher? Die Antwort ist einfach: <strong>Informationsdichte</strong>. Pro Megabyte Speicherplatz speichert geschriebener Text die meiste Information aller Medien. Während uns sowohl Wissen als auch Kultur wichtig sind, liegt unser Schwerpunkt mehr auf Ersterem. Insgesamt finden wir eine Hierarchie der Informationsdichte und der Wichtigkeit der Bewahrung, die ungefähr so aussieht: Die Rangfolge in dieser Liste ist etwas willkürlich – mehrere Punkte sind gleichwertig oder es gibt Meinungsverschiedenheiten innerhalb unseres Teams – und wir vergessen wahrscheinlich einige wichtige Kategorien. Aber so priorisieren wir grob. Einige dieser Punkte sind zu unterschiedlich von den anderen, um uns Sorgen zu machen (oder werden bereits von anderen Institutionen abgedeckt), wie organische Daten oder geografische Daten. Aber die meisten der Punkte in dieser Liste sind tatsächlich wichtig für uns. Ein weiterer großer Faktor bei unserer Priorisierung ist, wie gefährdet ein bestimmtes Werk ist. Wir konzentrieren uns lieber auf Werke, die: Schließlich ist uns der Maßstab wichtig. Wir haben begrenzte Zeit und Geld, also würden wir lieber einen Monat damit verbringen, 10.000 Bücher zu retten als 1.000 Bücher – wenn sie ungefähr gleich wertvoll und gefährdet sind. <em><q>Das Verlorene kann nicht wiederhergestellt werden; aber lasst uns bewahren, was bleibt: nicht durch Tresore und Schlösser, die sie dem öffentlichen Auge und Gebrauch entziehen und sie dem Zahn der Zeit überlassen, sondern durch eine solche Vervielfältigung von Kopien, dass sie außerhalb der Reichweite von Unfällen sind.</q></em><br>— Thomas Jefferson, 1791 Schattenbibliotheken Code kann auf Github Open Source sein, aber Github als Ganzes kann nicht leicht gespiegelt und somit erhalten werden (obwohl es in diesem speziellen Fall ausreichend verteilte Kopien der meisten Code-Repositories gibt) Metadatensätze können auf der Worldcat-Website frei eingesehen, aber nicht in großen Mengen heruntergeladen werden (bis wir sie <a %(worldcat_scrape)s>gescrapt</a> haben) Reddit ist kostenlos nutzbar, hat aber kürzlich strenge Anti-Scraping-Maßnahmen ergriffen, im Zuge der datenhungrigen LLM-Trainings (mehr dazu später) Es gibt viele Organisationen mit ähnlichen Missionen und Prioritäten. Tatsächlich gibt es Bibliotheken, Archive, Labore, Museen und andere Institutionen, die mit der Erhaltung dieser Art von Materialien beauftragt sind. Viele davon sind gut finanziert, von Regierungen, Einzelpersonen oder Unternehmen. Aber sie haben einen massiven blinden Fleck: das Rechtssystem. Hierin liegt die einzigartige Rolle der Schattenbibliotheken und der Grund, warum Annas Archiv existiert. Wir können Dinge tun, die anderen Institutionen nicht erlaubt sind. Nun, es ist nicht (oft) so, dass wir Materialien archivieren können, die anderswo illegal zu bewahren sind. Nein, es ist in vielen Orten legal, ein Archiv mit beliebigen Büchern, wissenschaftlichen Aufsätzen, Zeitschriften usw. zu erstellen. Aber was legale Archive oft fehlt, ist <strong>Redundanz und Langlebigkeit</strong>. Es gibt Bücher, von denen nur ein Exemplar in irgendeiner physischen Bibliothek existiert. Es gibt Metadatensätze, die von einem einzigen Unternehmen bewacht werden. Es gibt Zeitungen, die nur auf Mikrofilm in einem einzigen Archiv erhalten sind. Bibliotheken können Finanzkürzungen erleiden, Unternehmen können bankrottgehen, Archive können bombardiert und niedergebrannt werden. Das ist nicht hypothetisch – das passiert ständig. Das Einzigartige, was wir bei Annas Archiv tun können, ist, viele Kopien von Werken in großem Maßstab zu speichern. Wir können wissenschaftliche Aufsätze, Bücher, Zeitschriften und mehr sammeln und in großen Mengen verteilen. Derzeit tun wir dies über Torrents, aber die genauen Technologien sind nicht entscheidend und werden sich im Laufe der Zeit ändern. Der wichtige Teil ist, viele Kopien weltweit zu verteilen. Dieses Zitat von vor über 200 Jahren ist immer noch aktuell: Ein kurzer Hinweis zum öffentlichen Bereich. Da sich Annas Archiv einzigartig auf Aktivitäten konzentriert, die in vielen Teilen der Welt illegal sind, kümmern wir uns nicht um weit verbreitete Sammlungen, wie Bücher im öffentlichen Bereich. Rechtliche Einrichtungen kümmern sich oft bereits gut darum. Es gibt jedoch Überlegungen, die uns manchmal dazu bringen, an öffentlich zugänglichen Sammlungen zu arbeiten: - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Die Speicherkosten sinken weiterhin exponentiell 3. Verbesserungen in der Informationsdichte Derzeit speichern wir Bücher in den Rohformaten, in denen sie uns vorliegen. Sicher, sie sind komprimiert, aber oft sind es immer noch große Scans oder Fotografien von Seiten. Bisher waren die einzigen Möglichkeiten, die Gesamtgröße unserer Sammlung zu verkleinern, eine aggressivere Komprimierung oder Deduplizierung. Um jedoch signifikante Einsparungen zu erzielen, sind beide für unseren Geschmack zu verlustbehaftet. Eine starke Komprimierung von Fotos kann den Text kaum lesbar machen. Und Deduplizierung erfordert ein hohes Maß an Sicherheit, dass Bücher genau gleich sind, was oft zu ungenau ist, insbesondere wenn der Inhalt derselbe ist, die Scans jedoch zu unterschiedlichen Anlässen gemacht wurden. Es gab immer eine dritte Option, aber ihre Qualität war so miserabel, dass wir sie nie in Betracht gezogen haben: <strong>OCR oder optische Zeichenerkennung</strong>. Dies ist der Prozess der Umwandlung von Fotos in reinen Text, indem KI verwendet wird, um die Zeichen in den Fotos zu erkennen. Werkzeuge dafür existieren schon lange und sind ziemlich anständig, aber „ziemlich anständig“ reicht für Erhaltungszwecke nicht aus. Allerdings haben jüngste multimodale Deep-Learning-Modelle extrem schnelle Fortschritte gemacht, wenn auch noch zu hohen Kosten. Wir erwarten, dass sich sowohl die Genauigkeit als auch die Kosten in den kommenden Jahren dramatisch verbessern werden, bis zu dem Punkt, an dem es realistisch wird, sie auf unsere gesamte Bibliothek anzuwenden. Wenn das passiert, werden wir wahrscheinlich immer noch die Originaldateien aufbewahren, aber zusätzlich könnten wir eine viel kleinere Version unserer Bibliothek haben, die die meisten Menschen spiegeln möchten. Der Clou ist, dass sich reiner Text selbst noch besser komprimieren lässt und viel einfacher zu deduplizieren ist, was uns noch mehr Einsparungen bringt. Insgesamt ist es nicht unrealistisch, eine Reduzierung der Gesamtdateigröße um mindestens das 5- bis 10-fache zu erwarten, vielleicht sogar mehr. Selbst bei einer konservativen Reduzierung um das 5-fache würden wir in 10 Jahren mit <strong>1.000–3.000 $ rechnen, selbst wenn sich unsere Bibliothek verdreifacht</strong>. Zum Zeitpunkt des Schreibens liegen die <a %(diskprices)s>Festplattenpreise</a> pro TB bei etwa 12 $ für neue Festplatten, 8 $ für gebrauchte Festplatten und 4 $ für Bänder. Wenn wir konservativ sind und nur neue Festplatten betrachten, bedeutet das, dass die Speicherung eines Petabytes etwa 12.000 $ kostet. Wenn wir annehmen, dass unsere Bibliothek von 900 TB auf 2,7 PB anwächst, würde das bedeuten, dass es 32.400 $ kostet, unsere gesamte Bibliothek zu spiegeln. Unter Berücksichtigung von Strom, Kosten für andere Hardware und so weiter, runden wir es auf 40.000 $ auf. Oder mit Bändern eher 15.000–20.000 $. Einerseits sind <strong>15.000–40.000 $ für die Summe allen menschlichen Wissens ein Schnäppchen</strong>. Andererseits ist es etwas hoch, zu erwarten, dass viele vollständige Kopien existieren, besonders wenn wir auch möchten, dass diese Personen ihre Torrents weiterhin für andere bereitstellen. Das ist heute. Aber der Fortschritt schreitet voran: Die Kosten für Festplatten pro TB wurden in den letzten 10 Jahren ungefähr um ein Drittel gesenkt und werden wahrscheinlich in einem ähnlichen Tempo weiter sinken. Bänder scheinen sich auf einem ähnlichen Weg zu befinden. SSD-Preise sinken noch schneller und könnten bis zum Ende des Jahrzehnts die HDD-Preise überholen. Wenn dies zutrifft, könnten wir in 10 Jahren nur noch 5.000–13.000 $ benötigen, um unsere gesamte Sammlung zu spiegeln (1/3), oder sogar weniger, wenn wir weniger wachsen. Während es immer noch viel Geld ist, wird dies für viele Menschen erreichbar sein. Und es könnte noch besser werden wegen des nächsten Punktes… In Annas Archiv werden wir oft gefragt, wie wir behaupten können, unsere Sammlungen auf ewig zu bewahren, wenn die Gesamtgröße bereits 1 Petabyte (1000 TB) erreicht und weiter wächst. In diesem Artikel werden wir unsere Philosophie betrachten und sehen, warum das nächste Jahrzehnt für unsere Mission, das Wissen und die Kultur der Menschheit zu bewahren, entscheidend ist. Kritisches Zeitfenster Wenn diese Prognosen zutreffen, müssen wir <strong>nur ein paar Jahre warten</strong>, bevor unsere gesamte Sammlung weit verbreitet gespiegelt wird. So wird sie, in den Worten von Thomas Jefferson, „außerhalb der Reichweite von Unfällen“ platziert. Leider hat das Aufkommen von LLMs und deren datenhungrigem Training viele Urheberrechtsinhaber in die Defensive gedrängt. Noch mehr als sie es ohnehin schon waren. Viele Websites machen es schwieriger, Daten zu scrapen und zu archivieren, Klagen fliegen umher, und währenddessen werden physische Bibliotheken und Archive weiterhin vernachlässigt. Wir können nur erwarten, dass sich diese Trends weiter verschlechtern und viele Werke verloren gehen, lange bevor sie gemeinfrei werden. <strong>Wir stehen am Vorabend einer Revolution in der Erhaltung, aber <q>das Verlorene kann nicht wiederhergestellt werden.</q></strong> Wir haben ein kritisches Zeitfenster von etwa 5-10 Jahren, in dem es noch ziemlich teuer ist, eine Schattenbibliothek zu betreiben und viele Spiegel auf der ganzen Welt zu erstellen, und in dem der Zugang noch nicht vollständig abgeschaltet wurde. Wenn wir dieses Zeitfenster überbrücken können, dann werden wir tatsächlich das Wissen und die Kultur der Menschheit für die Ewigkeit bewahrt haben. Wir sollten diese Zeit nicht ungenutzt verstreichen lassen. Wir sollten nicht zulassen, dass sich dieses kritische Zeitfenster schließt. Lassen Sie uns loslegen. Das kritische Fenster der Schattenbibliotheken Wie können wir behaupten, unsere Sammlungen auf ewig zu bewahren, wenn sie bereits 1 PB erreichen? Sammlung Einige weitere Informationen über die Sammlung. <a %(duxiu)s>Duxiu</a> ist eine riesige Datenbank gescannter Bücher, erstellt von der <a %(chaoxing)s>SuperStar Digital Library Group</a>. Die meisten sind akademische Bücher, die gescannt wurden, um sie Universitäten und Bibliotheken digital zugänglich zu machen. Für unser englischsprachiges Publikum haben <a %(library_princeton)s>Princeton</a> und die <a %(guides_lib_uw)s>University of Washington</a> gute Übersichten. Es gibt auch einen ausgezeichneten Artikel, der mehr Hintergrund bietet: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine“</a> (suchen Sie ihn in Annas Archiv). Die Bücher von Duxiu wurden lange Zeit im chinesischen Internet piratiert. Normalerweise werden sie von Wiederverkäufern für weniger als einen Dollar verkauft. Sie werden typischerweise mit dem chinesischen Äquivalent von Google Drive verteilt, das oft gehackt wurde, um mehr Speicherplatz zu ermöglichen. Einige technische Details finden Sie <a %(github_duty_machine)s>hier</a> und <a %(github_821_github_io)s>hier</a>. Obwohl die Bücher halböffentlich verteilt wurden, ist es ziemlich schwierig, sie in großen Mengen zu erhalten. Wir hatten dies hoch auf unserer TODO-Liste und mehrere Monate Vollzeitarbeit dafür eingeplant. Doch kürzlich hat sich ein unglaublicher, erstaunlicher und talentierter Freiwilliger an uns gewandt und uns mitgeteilt, dass er all diese Arbeit bereits erledigt hat — mit großem Aufwand. Sie teilten die gesamte Sammlung mit uns, ohne etwas im Gegenzug zu erwarten, außer der Garantie für eine langfristige Erhaltung. Wirklich bemerkenswert. Sie stimmten zu, auf diese Weise um Hilfe zu bitten, um die Sammlung OCR-fähig zu machen. Die Sammlung umfasst 7.543.702 Dateien. Das ist mehr als Library Genesis Sachbücher (etwa 5,3 Millionen). Die Gesamtgröße der Dateien beträgt etwa 359TB (326TiB) in ihrer aktuellen Form. Wir sind offen für andere Vorschläge und Ideen. Kontaktieren Sie uns einfach. Schauen Sie sich Annas Archiv an, um mehr Informationen über unsere Sammlungen, Erhaltungsbemühungen und wie Sie helfen können, zu erhalten. Danke! Beispielseiten Um uns zu beweisen, dass Sie eine gute Pipeline haben, hier einige Beispielseiten zum Einstieg, aus einem Buch über Supraleiter. Ihre Pipeline sollte Mathematik, Tabellen, Diagramme, Fußnoten usw. korrekt verarbeiten. Senden Sie Ihre bearbeiteten Seiten an unsere E-Mail. Wenn sie gut aussehen, senden wir Ihnen privat mehr, und wir erwarten, dass Sie Ihre Pipeline auch schnell darauf ausführen können. Sobald wir zufrieden sind, können wir einen Deal machen. - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Chinesische Version 中文版</a>, <a %(news_ycombinator)s>Diskutieren Sie auf Hacker News</a> Dies ist ein kurzer Blogbeitrag. Wir suchen ein Unternehmen oder eine Institution, die uns bei der OCR und Textextraktion für eine massive Sammlung, die wir erworben haben, unterstützt, im Austausch für exklusiven frühen Zugang. Nach der Embargoperiode werden wir natürlich die gesamte Sammlung freigeben. Hochwertige akademische Texte sind äußerst nützlich für das Training von LLMs. Obwohl unsere Sammlung chinesisch ist, sollte sie auch für das Training englischer LLMs nützlich sein: Modelle scheinen Konzepte und Wissen unabhängig von der Quellsprache zu kodieren. Dafür müssen Texte aus den Scans extrahiert werden. Was hat Annas Archiv davon? Volltextsuche der Bücher für seine Nutzer. Da unsere Ziele mit denen der LLM-Entwickler übereinstimmen, suchen wir einen Kollaborator. Wir sind bereit, Ihnen <strong>exklusiven frühen Zugang zu dieser Sammlung in großen Mengen für 1 Jahr</strong> zu gewähren, wenn Sie eine ordnungsgemäße OCR und Textextraktion durchführen können. Wenn Sie bereit sind, uns den gesamten Code Ihrer Pipeline zu teilen, wären wir bereit, die Sammlung länger zu sperren. Exklusiver Zugang für LLM-Unternehmen zur größten chinesischen Sachbuchsammlung der Welt <em><strong>TL;DR:</strong> Annas Archiv hat eine einzigartige Sammlung von 7,5 Millionen / 350 TB chinesischer Sachbücher erworben – größer als Library Genesis. Wir sind bereit, einem LLM-Unternehmen exklusiven Zugang zu gewähren, im Austausch für hochwertige OCR und Textextraktion.</em> Systemarchitektur Angenommen, Sie haben einige Unternehmen gefunden, die bereit sind, Ihre Website zu hosten, ohne Sie abzuschalten – nennen wir sie „freiheitsliebende Anbieter“ 😄. Sie werden schnell feststellen, dass das Hosting von allem bei ihnen ziemlich teuer ist, also möchten Sie vielleicht einige „günstige Anbieter“ finden und das eigentliche Hosting dort durchführen, indem Sie über die freiheitsliebenden Anbieter proxyen. Wenn Sie es richtig machen, werden die günstigen Anbieter nie wissen, was Sie hosten, und nie Beschwerden erhalten. Bei all diesen Anbietern besteht das Risiko, dass sie Sie trotzdem abschalten, daher benötigen Sie auch Redundanz. Wir brauchen dies auf allen Ebenen unseres Stacks. Ein etwas freiheitsliebendes Unternehmen, das sich in eine interessante Position gebracht hat, ist Cloudflare. Sie haben <a %(blog_cloudflare)s>argumentiert</a>, dass sie kein Hosting-Anbieter, sondern ein Versorgungsunternehmen wie ein ISP sind. Sie unterliegen daher nicht den DMCA- oder anderen Abschaltanfragen und leiten alle Anfragen an Ihren tatsächlichen Hosting-Anbieter weiter. Sie sind sogar so weit gegangen, vor Gericht zu gehen, um diese Struktur zu schützen. Wir können sie daher als eine weitere Schicht von Caching und Schutz verwenden. Cloudflare akzeptiert keine anonymen Zahlungen, daher können wir nur ihren kostenlosen Plan nutzen. Das bedeutet, dass wir ihre Load-Balancing- oder Failover-Funktionen nicht nutzen können. Wir haben dies daher <a %(annas_archive_l255)s>selbst auf Domain-Ebene implementiert</a>. Beim Laden der Seite überprüft der Browser, ob die aktuelle Domain noch verfügbar ist, und wenn nicht, werden alle URLs auf eine andere Domain umgeschrieben. Da Cloudflare viele Seiten zwischenspeichert, bedeutet dies, dass ein Benutzer auf unserer Hauptdomain landen kann, selbst wenn der Proxy-Server ausgefallen ist, und dann beim nächsten Klick auf eine andere Domain verschoben wird. Wir haben auch weiterhin normale betriebliche Anliegen zu bewältigen, wie die Überwachung der Servergesundheit, das Protokollieren von Backend- und Frontend-Fehlern und so weiter. Unsere Failover-Architektur ermöglicht auch mehr Robustheit in diesem Bereich, zum Beispiel durch den Betrieb eines völlig anderen Satzes von Servern auf einer der Domains. Wir können sogar ältere Versionen des Codes und der Datasets auf dieser separaten Domain ausführen, falls ein kritischer Fehler in der Hauptversion unbemerkt bleibt. Wir können uns auch gegen eine mögliche Abkehr von Cloudflare absichern, indem wir es von einer der Domains entfernen, wie zum Beispiel dieser separaten Domain. Verschiedene Permutationen dieser Ideen sind möglich. Fazit Es war eine interessante Erfahrung, zu lernen, wie man eine robuste und widerstandsfähige Suchmaschine für eine Schattenbibliothek einrichtet. Es gibt noch viele weitere Details, die in späteren Beiträgen geteilt werden können, also lassen Sie mich wissen, worüber Sie mehr erfahren möchten! Wie immer suchen wir nach Spenden, um diese Arbeit zu unterstützen, also schauen Sie sich unbedingt die Spendenseite auf Annas Archiv an. Wir suchen auch nach anderen Arten von Unterstützung, wie Stipendien, langfristige Sponsoren, Hochrisiko-Zahlungsanbieter, vielleicht sogar (geschmackvolle!) Anzeigen. Und wenn Sie Ihre Zeit und Fähigkeiten einbringen möchten, suchen wir immer nach Entwicklern, Übersetzern und so weiter. Vielen Dank für Ihr Interesse und Ihre Unterstützung. Innovationstoken Beginnen wir mit unserem Tech-Stack. Er ist absichtlich langweilig. Wir verwenden Flask, MariaDB und ElasticSearch. Das war's im Grunde. Die Suche ist weitgehend ein gelöstes Problem, und wir haben nicht vor, es neu zu erfinden. Außerdem müssen wir unsere <a %(mcfunley)s>Innovationstoken</a> für etwas anderes ausgeben: nicht von den Behörden abgeschaltet zu werden. Wie legal oder illegal ist Annas Archiv genau? Das hängt größtenteils von der rechtlichen Zuständigkeit ab. Die meisten Länder glauben an irgendeine Form von Urheberrecht, was bedeutet, dass Personen oder Unternehmen ein exklusives Monopol auf bestimmte Arten von Werken für einen bestimmten Zeitraum zugewiesen wird. Nebenbei bemerkt, glauben wir bei Annas Archiv, dass es zwar einige Vorteile gibt, das Urheberrecht insgesamt jedoch ein Netto-Nachteil für die Gesellschaft ist – aber das ist eine Geschichte für ein anderes Mal. Dieses exklusive Monopol auf bestimmte Werke bedeutet, dass es illegal ist, dass jemand außerhalb dieses Monopols diese Werke direkt verbreitet – einschließlich uns. Aber Annas Archiv ist eine Suchmaschine, die diese Werke nicht direkt verbreitet (zumindest nicht auf unserer Clearnet-Website), also sollten wir in Ordnung sein, oder? Nicht ganz. In vielen Rechtsordnungen ist es nicht nur illegal, urheberrechtlich geschützte Werke zu verbreiten, sondern auch, auf Orte zu verlinken, die dies tun. Ein klassisches Beispiel dafür ist das DMCA-Gesetz der Vereinigten Staaten. Das ist das strengste Ende des Spektrums. Am anderen Ende des Spektrums könnte es theoretisch Länder ohne jegliche Urheberrechtsgesetze geben, aber diese existieren in Wirklichkeit nicht. So ziemlich jedes Land hat irgendeine Form von Urheberrechtsgesetz. Die Durchsetzung ist eine andere Geschichte. Es gibt viele Länder mit Regierungen, die sich nicht darum kümmern, das Urheberrecht durchzusetzen. Es gibt auch Länder zwischen den beiden Extremen, die die Verbreitung urheberrechtlich geschützter Werke verbieten, aber das Verlinken zu solchen Werken nicht verbieten. Ein weiterer Aspekt ist die Unternehmensebene. Wenn ein Unternehmen in einer Rechtsordnung tätig ist, die sich nicht um das Urheberrecht kümmert, das Unternehmen selbst jedoch kein Risiko eingehen möchte, könnte es Ihre Website schließen, sobald sich jemand darüber beschwert. Schließlich ist ein großer Aspekt die Zahlungen. Da wir anonym bleiben müssen, können wir keine traditionellen Zahlungsmethoden verwenden. Das lässt uns mit Kryptowährungen, und nur eine kleine Anzahl von Unternehmen unterstützt diese (es gibt virtuelle Debitkarten, die mit Krypto bezahlt werden, aber sie werden oft nicht akzeptiert). - Anna und das Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ich betreibe <a %(wikipedia_annas_archive)s>Annas Archiv</a>, die weltweit größte Open-Source-Gemeinnützige Suchmaschine für <a %(wikipedia_shadow_library)s>Schattenbibliotheken</a> wie Sci-Hub, Library Genesis und Z-Library. Unser Ziel ist es, Wissen und Kultur leicht zugänglich zu machen und letztendlich eine Gemeinschaft von Menschen aufzubauen, die zusammen <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle Bücher der Welt</a> archivieren und bewahren. In diesem Artikel zeige ich, wie wir diese Website betreiben und die einzigartigen Herausforderungen, die mit dem Betrieb einer Website mit fragwürdigem rechtlichen Status einhergehen, da es kein „AWS für Schatten-Wohltätigkeitsorganisationen“ gibt. <em>Sehen Sie sich auch den Schwesterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Wie man ein Piratenarchivar wird</a> an.</em> Wie man eine Schattenbibliothek betreibt: Betrieb bei Annas Archiv Es gibt kein <q>AWS für Schatten-Wohltätigkeitsorganisationen,</q> also wie betreiben wir Annas Archiv? Werkzeuge Anwendungsserver: Flask, MariaDB, ElasticSearch, Docker. Entwicklung: Gitlab, Weblate, Zulip. Serververwaltung: Ansible, Checkmk, UFW. Onion-Hosting: Tor, Nginx. Proxy-Server: Varnish. Schauen wir uns an, welche Werkzeuge wir verwenden, um all dies zu erreichen. Dies entwickelt sich sehr stark weiter, da wir auf neue Probleme stoßen und neue Lösungen finden. Es gibt einige Entscheidungen, bei denen wir hin und her überlegt haben. Eine davon ist die Kommunikation zwischen den Servern: Früher haben wir dafür Wireguard verwendet, aber festgestellt, dass es gelegentlich aufhört, Daten zu übertragen, oder nur in eine Richtung Daten überträgt. Dies geschah bei mehreren verschiedenen Wireguard-Konfigurationen, die wir ausprobiert haben, wie <a %(github_costela_wesher)s>wesher</a> und <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Wir haben auch versucht, Ports über SSH zu tunneln, indem wir autossh und sshuttle verwendet haben, sind dabei jedoch auf <a %(github_sshuttle)s>Probleme gestoßen</a> (obwohl mir immer noch nicht klar ist, ob autossh unter TCP-over-TCP-Problemen leidet oder nicht – es fühlt sich für mich einfach wie eine wackelige Lösung an, aber vielleicht ist es tatsächlich in Ordnung?). Stattdessen sind wir zu direkten Verbindungen zwischen den Servern zurückgekehrt und haben dabei die Tatsache verborgen, dass ein Server bei günstigen Anbietern läuft, indem wir IP-Filterung mit UFW verwenden. Der Nachteil dabei ist, dass Docker nicht gut mit UFW funktioniert, es sei denn, Sie verwenden <code>network_mode: "host"</code>. All dies ist etwas fehleranfälliger, da Sie Ihren Server mit nur einer kleinen Fehlkonfiguration dem Internet aussetzen. Vielleicht sollten wir zu autossh zurückkehren – Feedback wäre hier sehr willkommen. Wir haben auch zwischen Varnish und Nginx hin und her überlegt. Derzeit mögen wir Varnish, aber es hat seine Eigenheiten und Ecken. Dasselbe gilt für Checkmk: Wir lieben es nicht, aber es funktioniert vorerst. Weblate war in Ordnung, aber nicht unglaublich – ich habe manchmal Angst, dass es meine Daten verliert, wann immer ich versuche, es mit unserem Git-Repo zu synchronisieren. Flask war insgesamt gut, aber es hat einige seltsame Eigenheiten, die viel Zeit zum Debuggen gekostet haben, wie das Konfigurieren benutzerdefinierter Domains oder Probleme mit seiner SqlAlchemy-Integration. Bisher waren die anderen Tools großartig: Wir haben keine ernsthaften Beschwerden über MariaDB, ElasticSearch, Gitlab, Zulip, Docker und Tor. Alle diese hatten einige Probleme, aber nichts allzu Ernstes oder Zeitaufwändiges. Gemeinschaft Die erste Herausforderung könnte eine überraschende sein. Es ist kein technisches Problem oder ein rechtliches Problem. Es ist ein psychologisches Problem: Diese Arbeit im Verborgenen zu tun, kann unglaublich einsam sein. Je nachdem, was Sie vorhaben und welches Bedrohungsmodell Sie haben, müssen Sie möglicherweise sehr vorsichtig sein. Am einen Ende des Spektrums haben wir Menschen wie Alexandra Elbakyan*, die Gründerin von Sci-Hub, die sehr offen über ihre Aktivitäten ist. Aber sie ist einem hohen Risiko ausgesetzt, verhaftet zu werden, wenn sie zu diesem Zeitpunkt ein westliches Land besuchen würde, und könnte Jahrzehnte im Gefängnis verbringen. Ist das ein Risiko, das Sie bereit wären einzugehen? Wir sind am anderen Ende des Spektrums; wir sind sehr darauf bedacht, keine Spuren zu hinterlassen und eine starke operative Sicherheit zu haben. * Wie von "ynno" auf HN erwähnt, wollte Alexandra anfangs nicht bekannt sein: "Ihre Server waren so eingerichtet, dass sie detaillierte Fehlermeldungen von PHP ausgaben, einschließlich des vollständigen Pfads der fehlerhaften Quelldatei, die sich im Verzeichnis /home/<USER>" Verwenden Sie also zufällige Benutzernamen auf den Computern, die Sie für diese Dinge verwenden, falls Sie etwas falsch konfigurieren. Diese Geheimhaltung hat jedoch einen psychologischen Preis. Die meisten Menschen lieben es, für die Arbeit, die sie leisten, anerkannt zu werden, und doch können Sie dafür im wirklichen Leben keine Anerkennung erhalten. Selbst einfache Dinge können herausfordernd sein, wie Freunde, die Sie fragen, was Sie so gemacht haben (irgendwann wird "mit meinem NAS / Homelab herumspielen" alt). Deshalb ist es so wichtig, eine Gemeinschaft zu finden. Sie können etwas von der operativen Sicherheit aufgeben, indem Sie sich einigen sehr engen Freunden anvertrauen, von denen Sie wissen, dass Sie ihnen tief vertrauen können. Selbst dann sollten Sie darauf achten, nichts schriftlich festzuhalten, falls sie ihre E-Mails an die Behörden übergeben müssen oder ihre Geräte auf andere Weise kompromittiert werden. Noch besser ist es, einige Mitpiraten zu finden. Wenn Ihre engen Freunde daran interessiert sind, sich Ihnen anzuschließen, großartig! Andernfalls könnten Sie online andere finden. Leider ist dies immer noch eine Nischengemeinschaft. Bisher haben wir nur eine Handvoll anderer gefunden, die in diesem Bereich aktiv sind. Gute Ausgangspunkte scheinen die Library Genesis-Foren und r/DataHoarder zu sein. Das Archive Team hat auch gleichgesinnte Personen, obwohl sie innerhalb des Gesetzes operieren (auch wenn in einigen Grauzonen des Gesetzes). Die traditionellen "Warez"- und Piraterie-Szenen haben auch Leute, die ähnlich denken. Wir sind offen für Ideen, wie wir die Gemeinschaft fördern und Ideen erkunden können. Fühlen Sie sich frei, uns auf Twitter oder Reddit zu kontaktieren. Vielleicht könnten wir eine Art Forum oder Chatgruppe einrichten. Eine Herausforderung besteht darin, dass dies auf gängigen Plattformen leicht zensiert werden kann, sodass wir es selbst hosten müssten. Es gibt auch einen Kompromiss zwischen der vollständigen öffentlichen Diskussion (mehr potenzielle Beteiligung) und der privaten Diskussion (um potenziellen „Zielen“ nicht zu verraten, dass wir sie gleich scrapen werden). Darüber müssen wir nachdenken. Lassen Sie uns wissen, ob Sie daran interessiert sind! Fazit Hoffentlich ist dies hilfreich für neu startende Piratenarchivare. Wir freuen uns, Sie in dieser Welt willkommen zu heißen, also zögern Sie nicht, uns zu kontaktieren. Lassen Sie uns so viel Wissen und Kultur der Welt bewahren, wie wir können, und es weit und breit spiegeln. Projekte 4. Datenauswahl Oft kann man die Metadaten nutzen, um einen vernünftigen Datenausschnitt zum Herunterladen zu bestimmen. Selbst wenn Sie letztendlich alle Daten herunterladen möchten, kann es nützlich sein, die wichtigsten Elemente zuerst zu priorisieren, falls Sie entdeckt werden und die Abwehrmaßnahmen verbessert werden, oder weil Sie mehr Festplatten kaufen müssten, oder einfach weil etwas anderes in Ihrem Leben passiert, bevor Sie alles herunterladen können. Zum Beispiel könnte eine Sammlung mehrere Ausgaben derselben zugrunde liegenden Ressource (wie ein Buch oder ein Film) haben, wobei eine als die beste Qualität markiert ist. Diese Ausgaben zuerst zu speichern, wäre sehr sinnvoll. Möglicherweise möchten Sie schließlich alle Ausgaben speichern, da in einigen Fällen die Metadaten falsch markiert sein könnten oder es unbekannte Kompromisse zwischen den Ausgaben geben könnte (zum Beispiel könnte die "beste Ausgabe" in den meisten Aspekten die beste sein, aber in anderen schlechter, wie ein Film mit höherer Auflösung, aber ohne Untertitel). Sie können auch Ihre Metadatendatenbank durchsuchen, um interessante Dinge zu finden. Was ist die größte Datei, die gehostet wird, und warum ist sie so groß? Was ist die kleinste Datei? Gibt es interessante oder unerwartete Muster in Bezug auf bestimmte Kategorien, Sprachen und so weiter? Gibt es doppelte oder sehr ähnliche Titel? Gibt es Muster, wann Daten hinzugefügt wurden, wie ein Tag, an dem viele Dateien auf einmal hinzugefügt wurden? Man kann oft viel lernen, indem man den Datensatz auf verschiedene Weise betrachtet. In unserem Fall haben wir Z-Library-Bücher gegen die md5-Hashes in Library Genesis dedupliziert und dadurch viel Downloadzeit und Speicherplatz gespart. Dies ist jedoch eine ziemlich einzigartige Situation. In den meisten Fällen gibt es keine umfassenden Datenbanken darüber, welche Dateien bereits von anderen Piraten ordnungsgemäß erhalten werden. Dies ist an sich eine große Chance für jemanden da draußen. Es wäre großartig, eine regelmäßig aktualisierte Übersicht über Dinge wie Musik und Filme zu haben, die bereits weit verbreitet auf Torrent-Websites gesät werden und daher eine geringere Priorität haben, in Piraten-Spiegeln aufgenommen zu werden. 6. Verteilung Sie haben die Daten und besitzen damit wahrscheinlich den weltweit ersten Piraten-Spiegel Ihres Ziels. In vielerlei Hinsicht ist der schwierigste Teil vorbei, aber der riskanteste Teil steht Ihnen noch bevor. Schließlich waren Sie bisher unauffällig; unter dem Radar fliegend. Alles, was Sie tun mussten, war, währenddessen ein gutes VPN zu verwenden, keine persönlichen Daten in irgendwelche Formulare einzutragen (klar), und vielleicht eine spezielle Browsersitzung (oder sogar einen anderen Computer) zu verwenden. Jetzt müssen Sie die Daten verteilen. In unserem Fall wollten wir zuerst die Bücher an Library Genesis zurückgeben, stellten jedoch schnell die Schwierigkeiten dabei fest (Sortierung von Fiktion vs. Sachbuch). Also entschieden wir uns für die Verteilung über Torrents im Library Genesis-Stil. Wenn Sie die Möglichkeit haben, zu einem bestehenden Projekt beizutragen, könnte das Ihnen viel Zeit sparen. Es gibt jedoch derzeit nicht viele gut organisierte Piraten-Spiegel. Angenommen, Sie entscheiden sich dafür, Torrents selbst zu verteilen. Versuchen Sie, diese Dateien klein zu halten, damit sie leicht auf anderen Websites gespiegelt werden können. Sie müssen dann die Torrents selbst seeden, während Sie anonym bleiben. Sie können ein VPN verwenden (mit oder ohne Portweiterleitung) oder mit getumblten Bitcoins für eine Seedbox bezahlen. Wenn Sie nicht wissen, was einige dieser Begriffe bedeuten, haben Sie eine Menge zu lesen, da es wichtig ist, dass Sie die Risikokompromisse hier verstehen. Sie können die Torrent-Dateien selbst auf bestehenden Torrent-Websites hosten. In unserem Fall haben wir uns entschieden, tatsächlich eine Website zu hosten, da wir auch unsere Philosophie klar verbreiten wollten. Sie können dies auf ähnliche Weise selbst tun (wir verwenden Njalla für unsere Domains und das Hosting, bezahlt mit getumblten Bitcoins), aber zögern Sie nicht, uns zu kontaktieren, damit wir Ihre Torrents hosten. Wir möchten im Laufe der Zeit ein umfassendes Verzeichnis von Piraten-Spiegeln aufbauen, wenn diese Idee Anklang findet. Was die VPN-Auswahl betrifft, so wurde darüber bereits viel geschrieben, daher wiederholen wir nur den allgemeinen Rat, nach Ruf zu wählen. Tatsächlich gerichtlich getestete No-Log-Richtlinien mit langjähriger Erfahrung im Schutz der Privatsphäre sind unserer Meinung nach die risikoärmste Option. Beachten Sie, dass Sie selbst dann, wenn Sie alles richtig machen, niemals ein Null-Risiko erreichen können. Zum Beispiel kann ein hochmotivierter staatlicher Akteur beim Seeden Ihrer Torrents wahrscheinlich die ein- und ausgehenden Datenströme für VPN-Server betrachten und herausfinden, wer Sie sind. Oder Sie können einfach irgendwie einen Fehler machen. Wir haben das wahrscheinlich schon getan und werden es wieder tun. Glücklicherweise kümmern sich Staaten nicht <em>so</em> sehr um Piraterie. Eine Entscheidung, die für jedes Projekt zu treffen ist, ist, ob es unter derselben Identität wie zuvor veröffentlicht werden soll oder nicht. Wenn Sie denselben Namen weiter verwenden, könnten Fehler in der Betriebssicherheit aus früheren Projekten Sie einholen. Aber unter verschiedenen Namen zu veröffentlichen bedeutet, dass Sie keinen länger anhaltenden Ruf aufbauen. Wir haben uns entschieden, von Anfang an eine starke Betriebssicherheit zu haben, damit wir dieselbe Identität weiter verwenden können, aber wir zögern nicht, unter einem anderen Namen zu veröffentlichen, wenn wir einen Fehler machen oder die Umstände es erfordern. Die Bekanntmachung kann knifflig sein. Wie gesagt, dies ist immer noch eine Nischen-Community. Ursprünglich haben wir auf Reddit gepostet, aber wirklich Anklang fanden wir auf Hacker News. Für den Moment empfehlen wir, es an ein paar Stellen zu posten und zu sehen, was passiert. Und nochmals, kontaktieren Sie uns. Wir würden gerne das Wort über mehr Piratenarchivierungsbemühungen verbreiten. 1. Domänenauswahl / Philosophie Es mangelt nicht an Wissen und kulturellem Erbe, das gerettet werden muss, was überwältigend sein kann. Deshalb ist es oft nützlich, einen Moment innezuhalten und darüber nachzudenken, was Ihr Beitrag sein kann. Jeder hat eine andere Art, darüber nachzudenken, aber hier sind einige Fragen, die Sie sich stellen könnten: In unserem Fall lag uns besonders die langfristige Bewahrung der Wissenschaft am Herzen. Wir wussten von Library Genesis und wie es viele Male vollständig über Torrents gespiegelt wurde. Diese Idee gefiel uns. Eines Tages versuchte einer von uns, einige wissenschaftliche Lehrbücher auf Library Genesis zu finden, konnte sie aber nicht finden, was die Vollständigkeit wirklich in Frage stellte. Wir suchten dann diese Lehrbücher online und fanden sie an anderen Orten, was den Samen für unser Projekt pflanzte. Schon bevor wir von der Z-Library wussten, hatten wir die Idee, nicht zu versuchen, all diese Bücher manuell zu sammeln, sondern uns darauf zu konzentrieren, bestehende Sammlungen zu spiegeln und sie zurück zu Library Genesis beizutragen. Welche Fähigkeiten haben Sie, die Sie zu Ihrem Vorteil nutzen können? Zum Beispiel, wenn Sie ein Experte für Online-Sicherheit sind, können Sie Wege finden, IP-Sperren für sichere Ziele zu überwinden. Wenn Sie großartig darin sind, Gemeinschaften zu organisieren, dann können Sie vielleicht einige Leute um ein Ziel versammeln. Es ist jedoch nützlich, etwas Programmierung zu kennen, wenn auch nur, um während dieses Prozesses eine gute Betriebssicherheit zu gewährleisten. Was wäre ein Bereich mit hohem Hebel, auf den Sie sich konzentrieren könnten? Wenn Sie X Stunden mit Piratenarchivierung verbringen werden, wie können Sie dann das größte „Ergebnis für Ihren Einsatz“ erzielen? Welche einzigartigen Wege denken Sie darüber nach? Sie könnten einige interessante Ideen oder Ansätze haben, die andere möglicherweise übersehen haben. Wie viel Zeit haben Sie dafür? Unser Rat wäre, klein anzufangen und größere Projekte zu machen, wenn Sie den Dreh raus haben, aber es kann alles verzehrend werden. Warum interessiert Sie das? Wofür brennen Sie? Wenn wir eine Gruppe von Menschen zusammenbringen können, die alle die Arten von Dingen archivieren, die ihnen besonders am Herzen liegen, würde das viel abdecken! Sie werden viel mehr wissen als der Durchschnittsmensch über Ihre Leidenschaft, wie welche wichtigen Daten zu speichern sind, welche die besten Sammlungen und Online-Communities sind und so weiter. 3. Metadata-Scraping Hinzugefügtes/Geändertes Datum: damit Sie später zurückkehren und Dateien herunterladen können, die Sie zuvor nicht heruntergeladen haben (obwohl Sie dafür oft auch die ID oder den Hash verwenden können). Hash (md5, sha1): um zu bestätigen, dass Sie die Datei ordnungsgemäß heruntergeladen haben. ID: kann eine interne ID sein, aber IDs wie ISBN oder DOI sind ebenfalls nützlich. Dateiname / Speicherort Beschreibung, Kategorie, Tags, Autoren, Sprache usw. Größe: um zu berechnen, wie viel Speicherplatz Sie benötigen. Lassen Sie uns hier etwas technischer werden. Um tatsächlich metadata von Websites zu scrapen, haben wir die Dinge ziemlich einfach gehalten. Wir verwenden Python-Skripte, manchmal curl, und eine MySQL-Datenbank, um die Ergebnisse zu speichern. Wir haben keine ausgeklügelte Scraping-Software verwendet, die komplexe Websites abbilden kann, da wir bisher nur eine oder zwei Arten von Seiten scrapen mussten, indem wir einfach IDs durchlaufen und das HTML parsen. Wenn es keine leicht aufzählbaren Seiten gibt, benötigen Sie möglicherweise einen richtigen Crawler, der versucht, alle Seiten zu finden. Bevor Sie eine ganze Website scrapen, versuchen Sie es eine Weile manuell. Gehen Sie selbst durch ein paar Dutzend Seiten, um ein Gefühl dafür zu bekommen, wie das funktioniert. Manchmal stoßen Sie auf diese Weise bereits auf IP-Sperren oder anderes interessantes Verhalten. Das Gleiche gilt für das Datenscraping: Bevor Sie sich zu tief in dieses Ziel vertiefen, stellen Sie sicher, dass Sie seine Daten tatsächlich effektiv herunterladen können. Um Einschränkungen zu umgehen, gibt es ein paar Dinge, die Sie ausprobieren können. Gibt es andere IP-Adressen oder Server, die dieselben Daten hosten, aber nicht dieselben Einschränkungen haben? Gibt es API-Endpunkte, die keine Einschränkungen haben, während andere dies tun? Bei welcher Download-Rate wird Ihre IP blockiert und wie lange? Oder werden Sie nicht blockiert, sondern gedrosselt? Was passiert, wenn Sie ein Benutzerkonto erstellen, wie ändern sich die Dinge dann? Können Sie HTTP/2 verwenden, um Verbindungen offen zu halten, und erhöht das die Rate, mit der Sie Seiten anfordern können? Gibt es Seiten, die mehrere Dateien auf einmal auflisten, und sind die dort aufgeführten Informationen ausreichend? Dinge, die Sie wahrscheinlich speichern möchten, sind: Wir machen dies typischerweise in zwei Stufen. Zuerst laden wir die rohen HTML-Dateien herunter, normalerweise direkt in MySQL (um viele kleine Dateien zu vermeiden, worüber wir weiter unten mehr sprechen). Dann gehen wir in einem separaten Schritt durch diese HTML-Dateien und parsen sie in tatsächliche MySQL-Tabellen. Auf diese Weise müssen Sie nicht alles von Grund auf neu herunterladen, wenn Sie einen Fehler in Ihrem Parsing-Code entdecken, da Sie die HTML-Dateien einfach mit dem neuen Code erneut verarbeiten können. Es ist auch oft einfacher, den Verarbeitungsschritt zu parallelisieren, was Zeit spart (und Sie können den Verarbeitungscode schreiben, während das Scraping läuft, anstatt beide Schritte gleichzeitig schreiben zu müssen). Schließlich sei darauf hingewiesen, dass für einige Ziele das Scraping von Metadaten alles ist, was es gibt. Es gibt einige riesige Metadatensammlungen, die nicht ordnungsgemäß erhalten sind. Titel Domänenauswahl / Philosophie: Worauf möchten Sie sich grob konzentrieren und warum? Welche einzigartigen Leidenschaften, Fähigkeiten und Umstände können Sie zu Ihrem Vorteil nutzen? Zielauswahl: Welche spezifische Sammlung werden Sie spiegeln? Metadata-Scraping: Katalogisierung von Informationen über die Dateien, ohne die (oft viel größeren) Dateien selbst herunterzuladen. Datenauswahl: Basierend auf den Metadaten wird eingegrenzt, welche Daten derzeit am relevantesten für das Archiv sind. Es könnte alles sein, aber oft gibt es eine vernünftige Möglichkeit, Platz und Bandbreite zu sparen. Daten-Scraping: Tatsächliches Abrufen der Daten. Verteilung: Verpackung in Torrents, Ankündigung irgendwo, Leute dazu bringen, es zu verbreiten. 5. Daten-Scraping Jetzt sind Sie bereit, die Daten tatsächlich in großen Mengen herunterzuladen. Wie bereits erwähnt, sollten Sie zu diesem Zeitpunkt bereits manuell eine Reihe von Dateien heruntergeladen haben, um das Verhalten und die Einschränkungen des Ziels besser zu verstehen. Es wird jedoch immer noch Überraschungen geben, wenn Sie tatsächlich viele Dateien auf einmal herunterladen. Unser Rat hier ist hauptsächlich, es einfach zu halten. Beginnen Sie einfach damit, eine Reihe von Dateien herunterzuladen. Sie können Python verwenden und dann auf mehrere Threads erweitern. Aber manchmal ist es noch einfacher, Bash-Dateien direkt aus der Datenbank zu generieren und dann mehrere davon in mehreren Terminalfenstern auszuführen, um zu skalieren. Ein schneller technischer Trick, der hier erwähnenswert ist, ist die Verwendung von OUTFILE in MySQL, das Sie überall schreiben können, wenn Sie "secure_file_priv" in mysqld.cnf deaktivieren (und stellen Sie sicher, dass Sie auch AppArmor deaktivieren/überschreiben, wenn Sie Linux verwenden). Wir speichern die Daten auf einfachen Festplatten. Beginnen Sie mit dem, was Sie haben, und erweitern Sie langsam. Es kann überwältigend sein, über die Speicherung von Hunderten von TBs an Daten nachzudenken. Wenn das die Situation ist, der Sie gegenüberstehen, stellen Sie einfach zuerst einen guten Ausschnitt bereit und bitten Sie in Ihrer Ankündigung um Hilfe bei der Speicherung des Rests. Wenn Sie selbst mehr Festplatten erwerben möchten, hat r/DataHoarder einige gute Ressourcen, um gute Angebote zu finden. Versuchen Sie, sich nicht zu sehr um ausgefallene Dateisysteme zu kümmern. Es ist leicht, in das Kaninchenloch zu fallen, Dinge wie ZFS einzurichten. Ein technisches Detail, dessen Sie sich bewusst sein sollten, ist jedoch, dass viele Dateisysteme nicht gut mit vielen Dateien umgehen. Wir haben festgestellt, dass eine einfache Lösung darin besteht, mehrere Verzeichnisse zu erstellen, z. B. für verschiedene ID-Bereiche oder Hash-Präfixe. Nach dem Herunterladen der Daten sollten Sie die Integrität der Dateien mit Hilfe von Hashes in den Metadaten überprüfen, falls verfügbar. 2. Zielauswahl Zugänglich: verwendet nicht viele Schutzschichten, um zu verhindern, dass Sie ihre metadata und Daten scrapen. Besonderer Einblick: Sie haben spezielle Informationen über dieses Ziel, wie z. B. besonderen Zugang zu dieser Sammlung oder Sie haben herausgefunden, wie man ihre Abwehrmechanismen überwindet. Dies ist nicht erforderlich (unser bevorstehendes Projekt macht nichts Besonderes), aber es hilft sicherlich! Groß Also, wir haben unser Zielgebiet festgelegt, aber welche spezifische Sammlung spiegeln wir? Es gibt ein paar Dinge, die ein gutes Ziel ausmachen: Als wir unsere Wissenschaftslehrbücher auf anderen Websites als Library Genesis fanden, versuchten wir herauszufinden, wie sie ihren Weg ins Internet gefunden hatten. Dann entdeckten wir die Z-Library und erkannten, dass, obwohl die meisten Bücher dort nicht zuerst erscheinen, sie schließlich dort landen. Wir erfuhren von ihrer Beziehung zu Library Genesis und der (finanziellen) Anreizstruktur sowie der überlegenen Benutzeroberfläche, die beide zu einer viel vollständigeren Sammlung führten. Wir führten dann einige Voruntersuchungen zum metadata- und Datenscraping durch und erkannten, dass wir ihre IP-Download-Beschränkungen umgehen konnten, indem wir den speziellen Zugang eines unserer Mitglieder zu vielen Proxy-Servern nutzten. Während Sie verschiedene Ziele erkunden, ist es bereits wichtig, Ihre Spuren zu verwischen, indem Sie VPNs und Wegwerf-E-Mail-Adressen verwenden, worüber wir später noch mehr sprechen werden. Einzigartig: nicht bereits gut von anderen Projekten abgedeckt. Wenn wir ein Projekt durchführen, hat es mehrere Phasen: Diese Phasen sind nicht völlig unabhängig voneinander, und oft führen Erkenntnisse aus einer späteren Phase dazu, dass man zu einer früheren Phase zurückkehrt. Zum Beispiel könnten Sie während des Metadata-Scrapings feststellen, dass das von Ihnen ausgewählte Ziel über Abwehrmechanismen verfügt, die über Ihr Können hinausgehen (wie IP-Sperren), sodass Sie zurückgehen und ein anderes Ziel finden. - Anna und das Team (<a %(reddit)s>Reddit</a>) Es könnten ganze Bücher über das <em>Warum</em> der digitalen Bewahrung im Allgemeinen und des Piratenarchivismus im Besonderen geschrieben werden, aber lassen Sie uns eine kurze Einführung für diejenigen geben, die nicht allzu vertraut sind. Die Welt produziert mehr Wissen und Kultur als je zuvor, aber auch mehr davon geht verloren als je zuvor. Die Menschheit vertraut weitgehend Unternehmen wie akademischen Verlagen, Streaming-Diensten und sozialen Medienunternehmen dieses Erbe an, und sie haben sich oft nicht als großartige Verwalter erwiesen. Schauen Sie sich die Dokumentation Digital Amnesia an oder wirklich jeden Vortrag von Jason Scott. Es gibt einige Institutionen, die gute Arbeit leisten, so viel wie möglich zu archivieren, aber sie sind an das Gesetz gebunden. Als Piraten sind wir in einer einzigartigen Position, Sammlungen zu archivieren, die sie aufgrund von Urheberrechtsdurchsetzung oder anderen Einschränkungen nicht berühren können. Wir können auch Sammlungen weltweit mehrfach spiegeln, wodurch die Chancen auf eine ordnungsgemäße Bewahrung erhöht werden. Für den Moment werden wir nicht in Diskussionen über die Vor- und Nachteile des geistigen Eigentums, die Moral des Gesetzesbruchs, Überlegungen zur Zensur oder die Frage des Zugangs zu Wissen und Kultur einsteigen. Mit all dem aus dem Weg, lassen Sie uns in das <em>Wie</em> eintauchen. Wir werden teilen, wie unser Team zu Piratenarchivaren wurde und die Lektionen, die wir auf dem Weg gelernt haben. Es gibt viele Herausforderungen, wenn Sie sich auf diese Reise begeben, und hoffentlich können wir Ihnen bei einigen davon helfen. Wie man ein Piratenarchivar wird Die erste Herausforderung könnte eine überraschende sein. Es ist kein technisches Problem oder ein rechtliches Problem. Es ist ein psychologisches Problem. Bevor wir eintauchen, zwei Updates zum Pirate Library Mirror (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>): Wir haben einige äußerst großzügige Spenden erhalten. Die erste war 10.000 $ von einer anonymen Person, die auch "bookwarrior", den ursprünglichen Gründer von Library Genesis, unterstützt hat. Besonderer Dank an bookwarrior für die Vermittlung dieser Spende. Die zweite war eine weitere Spende von 10.000 $ von einem anonymen Spender, der nach unserer letzten Veröffentlichung Kontakt aufnahm und inspiriert war zu helfen. Wir hatten auch eine Reihe kleinerer Spenden. Vielen Dank für all Ihre großzügige Unterstützung. Wir haben einige spannende neue Projekte in der Pipeline, die dadurch unterstützt werden, also bleiben Sie dran. Wir hatten einige technische Schwierigkeiten mit der Größe unserer zweiten Veröffentlichung, aber unsere Torrents sind jetzt online und werden gehostet. Wir haben auch ein großzügiges Angebot von einer anonymen Person erhalten, unsere Sammlung auf ihren sehr schnellen Servern zu hosten, also machen wir einen speziellen Upload auf ihre Maschinen, nach dem alle anderen, die die Sammlung herunterladen, eine große Verbesserung der Geschwindigkeit sehen sollten. Blogbeiträge Hallo, ich bin Anna. Ich habe <a %(wikipedia_annas_archive)s>Annas Archiv</a> erstellt, die größte Schattenbibliothek der Welt. Dies ist mein persönlicher Blog, in dem ich und mein Team über Piraterie, digitale Bewahrung und mehr schreiben. Verbinden Sie sich mit mir auf <a %(reddit)s>Reddit</a>. Beachten Sie, dass diese Website nur ein Blog ist. Wir hosten hier nur unsere eigenen Worte. Keine Torrents oder andere urheberrechtlich geschützte Dateien werden hier gehostet oder verlinkt. <strong>Bibliothek</strong> - Wie die meisten Bibliotheken konzentrieren wir uns hauptsächlich auf schriftliche Materialien wie Bücher. Möglicherweise erweitern wir uns in Zukunft auf andere Medienarten. <strong>Spiegel</strong> - Wir sind streng genommen ein Spiegel bestehender Bibliotheken. Wir konzentrieren uns auf die Bewahrung, nicht darauf, Bücher leicht durchsuchbar und herunterladbar zu machen (Zugang) oder eine große Gemeinschaft von Menschen zu fördern, die neue Bücher beitragen (Beschaffung). <strong>Pirat</strong> - Wir verletzen bewusst das Urheberrecht in den meisten Ländern. Dies ermöglicht es uns, etwas zu tun, was legale Einrichtungen nicht können: sicherzustellen, dass Bücher weit und breit gespiegelt werden. <em>Wir verlinken nicht auf die Dateien von diesem Blog. Bitte finden Sie es selbst.</em> - Anna und das Team (<a %(reddit)s>Reddit</a>) Dieses Projekt (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) zielt darauf ab, zur Bewahrung und Befreiung des menschlichen Wissens beizutragen. Wir leisten unseren kleinen und bescheidenen Beitrag, in den Fußstapfen der Großen vor uns. Der Fokus dieses Projekts wird durch seinen Namen veranschaulicht: Die erste Bibliothek, die wir gespiegelt haben, ist Z-Library. Dies ist eine beliebte (und illegale) Bibliothek. Sie haben die Library Genesis-Sammlung übernommen und sie leicht durchsuchbar gemacht. Darüber hinaus sind sie sehr effektiv darin geworden, neue Buchbeiträge zu erbitten, indem sie beitragende Nutzer mit verschiedenen Vorteilen belohnen. Derzeit tragen sie diese neuen Bücher nicht zurück zu Library Genesis bei. Und im Gegensatz zu Library Genesis machen sie ihre Sammlung nicht leicht spiegelbar, was eine breite Bewahrung verhindert. Dies ist wichtig für ihr Geschäftsmodell, da sie Geld für den Zugriff auf ihre Sammlung in großen Mengen (mehr als 10 Bücher pro Tag) verlangen. Wir fällen keine moralischen Urteile darüber, Geld für den Massen-Zugang zu einer illegalen Büchersammlung zu verlangen. Es steht außer Zweifel, dass die Z-Library erfolgreich den Zugang zu Wissen erweitert und mehr Bücher beschafft hat. Wir sind einfach hier, um unseren Teil beizutragen: die langfristige Bewahrung dieser privaten Sammlung zu gewährleisten. Wir möchten Sie einladen, dabei zu helfen, menschliches Wissen zu bewahren und zu befreien, indem Sie unsere Torrents herunterladen und seeden. Weitere Informationen zur Organisation der Daten finden Sie auf der Projektseite. Wir laden Sie auch herzlich ein, Ihre Ideen dazu beizutragen, welche Sammlungen als nächstes gespiegelt werden sollen und wie wir dies angehen können. Gemeinsam können wir viel erreichen. Dies ist nur ein kleiner Beitrag unter unzähligen anderen. Danke für alles, was Sie tun. Einführung des Piratenbibliothek-Spiegels: Bewahrung von 7TB an Büchern (die nicht in Libgen sind) 10% of des schriftlichen Erbes der Menschheit für immer bewahrt <strong>Google.</strong> Schließlich haben sie diese Forschung für Google Books durchgeführt. Allerdings sind ihre metadata nicht in großen Mengen zugänglich und ziemlich schwer zu scrapen. <strong>Verschiedene individuelle Bibliothekssysteme und Archive.</strong> Es gibt Bibliotheken und Archive, die von keiner der oben genannten indiziert und aggregiert wurden, oft weil sie unterfinanziert sind oder aus anderen Gründen ihre Daten nicht mit Open Library, OCLC, Google usw. teilen möchten. Viele davon haben digitale Aufzeichnungen, die über das Internet zugänglich sind, und sie sind oft nicht sehr gut geschützt. Wenn Sie also helfen und Spaß daran haben möchten, seltsame Bibliothekssysteme kennenzulernen, sind dies großartige Ausgangspunkte. <strong>ISBNdb.</strong> Dies ist das Thema dieses Blogbeitrags. ISBNdb scrapt verschiedene Websites nach Buchmetadata, insbesondere Preisdaten, die sie dann an Buchhändler verkaufen, damit diese ihre Bücher im Einklang mit dem Rest des Marktes bepreisen können. Da ISBNs heutzutage ziemlich universell sind, haben sie effektiv eine „Webseite für jedes Buch“ erstellt. <strong>Open Library.</strong> Wie bereits erwähnt, ist dies ihre gesamte Mission. Sie haben massive Mengen an Bibliotheksdaten von kooperierenden Bibliotheken und nationalen Archiven bezogen und tun dies weiterhin. Sie haben auch freiwillige Bibliothekare und ein technisches Team, das versucht, Datensätze zu deduplizieren und sie mit allen möglichen metadata zu versehen. Am besten ist, dass ihr Datensatz vollständig offen ist. Sie können ihn einfach <a %(openlibrary)s>herunterladen</a>. <strong>WorldCat.</strong> Dies ist eine Website, die von der gemeinnützigen OCLC betrieben wird, die Bibliotheksverwaltungssysteme verkauft. Sie aggregieren Buchmetadata aus vielen Bibliotheken und stellen sie über die WorldCat-Website zur Verfügung. Allerdings verdienen sie auch Geld mit dem Verkauf dieser Daten, sodass sie nicht für den Massen-Download verfügbar sind. Sie haben einige begrenztere Massendatensätze zum Download verfügbar, in Zusammenarbeit mit bestimmten Bibliotheken. 1. Für eine vernünftige Definition von „für immer“. ;) 2. Natürlich ist das schriftliche Erbe der Menschheit viel mehr als Bücher, besonders heutzutage. Für diesen Beitrag und unsere jüngsten Veröffentlichungen konzentrieren wir uns auf Bücher, aber unsere Interessen reichen weiter. 3. Es gibt viel mehr über Aaron Swartz zu sagen, aber wir wollten ihn nur kurz erwähnen, da er eine zentrale Rolle in dieser Geschichte spielt. Mit der Zeit könnten mehr Menschen seinen Namen zum ersten Mal hören und sich dann selbst in das Thema vertiefen. <strong>Physische Exemplare.</strong> Offensichtlich ist das nicht sehr hilfreich, da sie nur Duplikate desselben Materials sind. Es wäre cool, wenn wir alle Anmerkungen, die Menschen in Büchern machen, bewahren könnten, wie Fermats berühmte „Kritzeleien am Rand“. Aber leider wird das ein Traum für Archivare bleiben. <strong>„Ausgaben“.</strong> Hier zählt man jede einzigartige Version eines Buches. Wenn irgendetwas daran anders ist, wie ein anderes Cover oder ein anderes Vorwort, zählt es als eine andere Ausgabe. <strong>Dateien.</strong> Bei der Arbeit mit Schattenbibliotheken wie Library Genesis, Sci-Hub oder Z-Library gibt es eine zusätzliche Überlegung. Es kann mehrere Scans derselben Ausgabe geben. Und Menschen können bessere Versionen bestehender Dateien erstellen, indem sie den Text mit OCR scannen oder Seiten korrigieren, die schräg gescannt wurden. Wir möchten diese Dateien nur als eine Ausgabe zählen, was gute metadata oder eine Deduplizierung mit Dokumentähnlichkeitsmaßen erfordern würde. <strong>„Werke“.</strong> Zum Beispiel „Harry Potter und die Kammer des Schreckens“ als logisches Konzept, das alle Versionen davon umfasst, wie verschiedene Übersetzungen und Nachdrucke. Dies ist eine Art nützliche Definition, aber es kann schwierig sein, die Grenze zu ziehen, was zählt. Zum Beispiel möchten wir wahrscheinlich verschiedene Übersetzungen bewahren, obwohl Nachdrucke mit nur geringfügigen Unterschieden möglicherweise nicht so wichtig sind. - Anna und das Team (<a %(reddit)s>Reddit</a>) Mit dem Piratenbibliothek-Spiegel (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>) ist es unser Ziel, alle Bücher der Welt zu nehmen und sie für immer zu bewahren.<sup>1</sup> Zwischen unseren Z-Library-Torrents und den originalen Library Genesis-Torrents haben wir 11.783.153 Dateien. Aber wie viele sind das wirklich? Wenn wir diese Dateien richtig deduplizieren würden, welchen Prozentsatz aller Bücher der Welt haben wir bewahrt? Wir hätten wirklich gerne so etwas wie dies: Lassen Sie uns mit einigen groben Zahlen beginnen: In sowohl Z-Library/Libgen als auch Open Library gibt es viel mehr Bücher als einzigartige ISBNs. Bedeutet das, dass viele dieser Bücher keine ISBNs haben, oder fehlen die ISBN metadata einfach? Wir können diese Frage wahrscheinlich mit einer Kombination aus automatisiertem Abgleich basierend auf anderen Attributen (Titel, Autor, Verlag usw.), dem Einbeziehen weiterer Datenquellen und dem Extrahieren von ISBNs aus den tatsächlichen Buchscans selbst (im Fall von Z-Library/Libgen) beantworten. Wie viele dieser ISBNs sind einzigartig? Dies wird am besten mit einem Venn-Diagramm veranschaulicht: Um genauer zu sein: Wir waren überrascht, wie wenig Überschneidungen es gibt! ISBNdb hat eine riesige Menge an ISBNs, die weder in der Z-Library noch in der Open Library auftauchen, und das Gleiche gilt (in einem kleineren, aber immer noch erheblichen Ausmaß) für die anderen beiden. Das wirft viele neue Fragen auf. Wie sehr würde automatisches Matching helfen, die Bücher zu kennzeichnen, die nicht mit ISBNs versehen wurden? Würde es viele Übereinstimmungen und damit eine erhöhte Überschneidung geben? Und was würde passieren, wenn wir ein viertes oder fünftes Dataset hinzuziehen? Wie viel Überschneidung würden wir dann sehen? Das gibt uns einen Ausgangspunkt. Wir können nun alle ISBNs betrachten, die nicht im Z-Library-Dataset enthalten sind und die auch nicht mit Titel-/Autorenfeldern übereinstimmen. Das kann uns helfen, alle Bücher der Welt zu bewahren: zuerst durch das Scraping des Internets nach Scans, dann durch das Scannen von Büchern im echten Leben. Letzteres könnte sogar durch Crowdfunding finanziert oder durch „Prämien“ von Personen angetrieben werden, die bestimmte Bücher digitalisiert sehen möchten. All das ist eine Geschichte für eine andere Zeit. Wenn Sie bei einem dieser Themen helfen möchten — weitere Analysen; mehr metadata scrapen; mehr Bücher finden; Bücher OCRen; dies für andere Bereiche tun (z. B. wissenschaftliche Aufsätze, Hörbücher, Filme, Fernsehsendungen, Zeitschriften) oder sogar einige dieser Daten für Dinge wie ML / Training von großen Sprachmodellen verfügbar machen — kontaktieren Sie mich bitte (<a %(reddit)s>Reddit</a>). Wenn Sie sich speziell für die Datenanalyse interessieren, arbeiten wir daran, unsere Datasets und Skripte in einem benutzerfreundlicheren Format verfügbar zu machen. Es wäre großartig, wenn Sie einfach ein Notebook forken und damit herumspielen könnten. Wenn Sie diese Arbeit unterstützen möchten, ziehen Sie bitte eine Spende in Betracht. Dies ist eine vollständig ehrenamtlich geführte Operation, und Ihr Beitrag macht einen großen Unterschied. Jeder Beitrag hilft. Derzeit nehmen wir Spenden in Kryptowährung an; siehe die Spendenseite auf Annas Archiv. Für einen Prozentsatz benötigen wir einen Nenner: die Gesamtzahl der jemals veröffentlichten Bücher.<sup>2</sup> Vor dem Ende von Google Books versuchte ein Ingenieur des Projekts, Leonid Taycher, <a %(booksearch_blogspot)s>diese Zahl zu schätzen</a>. Er kam — scherzhaft — auf 129.864.880 („zumindest bis Sonntag“). Er schätzte diese Zahl, indem er eine einheitliche Datenbank aller Bücher der Welt erstellte. Dafür zog er verschiedene Datasets zusammen und fusionierte sie auf verschiedene Weise. Als kurze Randbemerkung: Es gibt eine weitere Person, die versucht hat, alle Bücher der Welt zu katalogisieren: Aaron Swartz, der verstorbene digitale Aktivist und Mitbegründer von Reddit.<sup>3</sup> Er <a %(youtube)s>gründete Open Library</a> mit dem Ziel, „eine Webseite für jedes jemals veröffentlichte Buch“ zu schaffen, indem er Daten aus vielen verschiedenen Quellen kombinierte. Er bezahlte den ultimativen Preis für seine Arbeit zur digitalen Bewahrung, als er wegen des massenhaften Herunterladens wissenschaftlicher Aufsätze strafrechtlich verfolgt wurde, was zu seinem Suizid führte. Es versteht sich von selbst, dass dies einer der Gründe ist, warum unsere Gruppe pseudonym ist und warum wir sehr vorsichtig sind. Open Library wird immer noch heldenhaft von den Leuten beim Internet Archive betrieben und setzt Aarons Vermächtnis fort. Wir werden später in diesem Beitrag darauf zurückkommen. In dem Google-Blogbeitrag beschreibt Taycher einige der Herausforderungen bei der Schätzung dieser Zahl. Zunächst einmal: Was ist ein Buch? Es gibt einige mögliche Definitionen: „Ausgaben“ scheinen die praktischste Definition dessen zu sein, was „Bücher“ sind. Bequemerweise wird diese Definition auch zur Vergabe einzigartiger ISBN-Nummern verwendet. Eine ISBN, oder Internationale Standardbuchnummer, wird häufig für den internationalen Handel verwendet, da sie in das internationale Barcode-System („International Article Number“) integriert ist. Wenn Sie ein Buch in Geschäften verkaufen möchten, benötigt es einen Barcode, also erhalten Sie eine ISBN. Taychers Blogbeitrag erwähnt, dass, obwohl ISBNs nützlich sind, sie nicht universell sind, da sie erst Mitte der siebziger Jahre wirklich eingeführt wurden und nicht überall auf der Welt. Dennoch ist die ISBN wahrscheinlich der am weitesten verbreitete Identifikator für Buchausgaben, daher ist sie unser bester Ausgangspunkt. Wenn wir alle ISBNs der Welt finden können, erhalten wir eine nützliche Liste, welche Bücher noch bewahrt werden müssen. Woher bekommen wir also die Daten? Es gibt eine Reihe bestehender Bemühungen, die versuchen, eine Liste aller Bücher der Welt zu erstellen: In diesem Beitrag freuen wir uns, eine kleine Veröffentlichung (im Vergleich zu unseren vorherigen Z-Library-Veröffentlichungen) anzukündigen. Wir haben den Großteil von ISBNdb gescrapt und die Daten zum Torrenting auf der Website des Pirate Library Mirror verfügbar gemacht (EDIT: verschoben zu <a %(wikipedia_annas_archive)s>Annas Archiv</a>; wir werden es hier nicht direkt verlinken, suchen Sie einfach danach). Dies sind etwa 30,9 Millionen Datensätze (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gezippt). Auf ihrer Website behaupten sie, tatsächlich 32,6 Millionen Datensätze zu haben, also haben wir möglicherweise einige übersehen, oder <em>sie</em> könnten etwas falsch machen. In jedem Fall werden wir vorerst nicht genau mitteilen, wie wir es gemacht haben — wir überlassen das als Übung dem Leser. ;-) Was wir teilen werden, ist eine vorläufige Analyse, um der Schätzung der Anzahl der Bücher in der Welt näher zu kommen. Wir haben uns drei Datensätze angesehen: diesen neuen ISBNdb-Datensatz, unsere ursprüngliche Veröffentlichung von metadata, die wir aus der Z-Library Schattenbibliothek (die Library Genesis einschließt) gescrapt haben, und den Open Library Daten-Dump. ISBNdb-Dump, oder Wie viele Bücher sind für immer bewahrt? Wenn wir die Dateien aus Schattenbibliotheken richtig deduplizieren würden, welchen Prozentsatz aller Bücher der Welt haben wir bewahrt? Updates über <a %(wikipedia_annas_archive)s>Annas Archiv</a>, die größte wirklich offene Bibliothek in der Geschichte der Menschheit. <em>WorldCat-Redesign</em> Daten <strong>Format?</strong> <a %(blog)s>Annas Archiv-Container (AAC)</a>, die im Wesentlichen <a %(jsonlines)s>JSON Lines</a> sind, komprimiert mit <a %(zstd)s>Zstandard</a>, plus einige standardisierte Semantiken. Diese Container umfassen verschiedene Arten von Aufzeichnungen, basierend auf den unterschiedlichen Scrapes, die wir eingesetzt haben. Vor einem Jahr haben wir uns <a %(blog)s>auf den Weg gemacht</a>, um diese Frage zu beantworten: <strong>Welcher Prozentsatz der Bücher wurde dauerhaft von Schattenbibliotheken bewahrt?</strong> Lassen Sie uns einige grundlegende Informationen zu den Daten ansehen: Sobald ein Buch in eine Open-Data-Schattenbibliothek wie <a %(wikipedia_library_genesis)s>Library Genesis</a> und jetzt <a %(wikipedia_annas_archive)s>Annas Archiv</a> gelangt, wird es weltweit gespiegelt (durch Torrents) und damit praktisch für immer bewahrt. Um die Frage zu beantworten, welcher Prozentsatz der Bücher bewahrt wurde, müssen wir den Nenner kennen: Wie viele Bücher gibt es insgesamt? Und idealerweise haben wir nicht nur eine Zahl, sondern tatsächliche Metadaten. Dann können wir sie nicht nur mit Schattenbibliotheken abgleichen, sondern auch <strong>eine TODO-Liste der verbleibenden Bücher erstellen, die bewahrt werden müssen!</strong> Wir könnten sogar davon träumen, eine Crowdsourcing-Initiative zu starten, um diese TODO-Liste abzuarbeiten. Wir haben <a %(wikipedia_isbndb_com)s>ISBNdb</a> gescraped und den <a %(openlibrary)s>Open Library-Datensatz</a> heruntergeladen, aber die Ergebnisse waren unbefriedigend. Das Hauptproblem war, dass es nicht viele Überschneidungen der ISBNs gab. Sehen Sie sich dieses Venn-Diagramm aus <a %(blog)s>unserem Blogbeitrag</a> an: Wir waren sehr überrascht, wie wenig Überschneidungen es zwischen ISBNdb und Open Library gab, obwohl beide großzügig Daten aus verschiedenen Quellen, wie Web-Scrapes und Bibliotheksaufzeichnungen, einbeziehen. Wenn beide gute Arbeit leisten, die meisten ISBNs da draußen zu finden, würden ihre Kreise sicherlich erhebliche Überschneidungen aufweisen, oder einer wäre eine Teilmenge des anderen. Es ließ uns darüber nachdenken, wie viele Bücher <em>vollständig außerhalb dieser Kreise</em> fallen? Wir brauchen eine größere Datenbank. Da richteten wir unseren Blick auf die größte Buchdatenbank der Welt: <a %(wikipedia_worldcat)s>WorldCat</a>. Dies ist eine proprietäre Datenbank der gemeinnützigen <a %(wikipedia_oclc)s>OCLC</a>, die Metadatenaufzeichnungen von Bibliotheken aus der ganzen Welt aggregiert, im Austausch dafür, dass diese Bibliotheken Zugang zum vollständigen Datensatz erhalten und in den Suchergebnissen der Endnutzer angezeigt werden. Obwohl OCLC eine gemeinnützige Organisation ist, erfordert ihr Geschäftsmodell den Schutz ihrer Datenbank. Nun, es tut uns leid, Freunde bei OCLC, wir geben alles preis. :-) Im vergangenen Jahr haben wir akribisch alle WorldCat-Aufzeichnungen gescraped. Zuerst hatten wir einen Glücksfall. WorldCat führte gerade ihr komplettes Website-Redesign ein (im August 2022). Dies beinhaltete eine umfassende Überarbeitung ihrer Backend-Systeme, die viele Sicherheitslücken einführte. Wir nutzten sofort die Gelegenheit und konnten in nur wenigen Tagen Hunderte von Millionen (!) von Aufzeichnungen scrapen. Danach wurden die Sicherheitslücken nach und nach einzeln behoben, bis die letzte, die wir gefunden hatten, vor etwa einem Monat geschlossen wurde. Zu diesem Zeitpunkt hatten wir so gut wie alle Aufzeichnungen und strebten nur noch nach etwas höherwertigen Aufzeichnungen. Also fühlten wir, dass es Zeit ist, zu veröffentlichen! 1,3 Milliarden WorldCat-Scrape <em><strong>TL;DR:</strong> Annas Archiv hat den gesamten WorldCat (die größte Bibliothek-Metadatensammlung der Welt) gescrapt, um eine TODO-Liste von Büchern zu erstellen, die bewahrt werden müssen.</em> WorldCat Warnung: Dieser Blogbeitrag ist veraltet. Wir haben entschieden, dass IPFS noch nicht bereit für den Einsatz ist. Wir werden weiterhin auf Dateien auf IPFS von Annas Archiv verlinken, wenn möglich, aber wir werden es nicht mehr selbst hosten, noch empfehlen wir anderen, mit IPFS zu spiegeln. Bitte sehen Sie sich unsere Torrents-Seite an, wenn Sie helfen möchten, unsere Sammlung zu erhalten. Helfen Sie, Z-Library auf IPFS zu seeden Partnerserver Download SciDB Extern Ausleihen Extern Ausleihen (Lesebehinderung) Externer Download Metadaten erkunden In den Torrents enthalten Zurück  (+%(num)s Bonus) unbezahlt bezahlt abgebrochen abgelaufen wartet auf Annas Bestätigung ungültig Der folgende Text ist nur auf Englisch verfügbar. Los Zurücksetzen Vorwärts Letzte Falls deine E-Mail-Adresse auf den Libgen Foren nicht funktioniert, schlagen wir vor eine E-Mail-Adresse von <a %(a_mail)s>Proton</a> (kostenlos) zu verwenden. Du kannst auch die <a %(a_manual)s>manuelle Freischaltung</a> deines Accounts beantragen. (kann <a %(a_browser)s>Browser-Verifizierung</a> erfordern - unbegrenzte Downloads!) Schneller Partnererver #%(number)s (empfohlen) (etwas schneller, aber mit Warteliste) (keine Browserüberprüfung erforderlich) (keine Browser-Verifizierung oder Wartelisten) (keine Warteliste, kann aber sehr langsam sein) Langsamer Partnerserver #%(number)s Hörbuch Comicbuch Buch (Belletristik) Buch (Sachbuch) Buch (unbekannt) wissenschaftliche Aufsätze Zeitschrift Partitur Andere Normen-Dokument Nicht alle Seiten konnten ins PDF Format konvertiert werden Als beschädigt markiert in Libgen.li Nicht sichtbar in Libgen.li Nicht sichtbar in Libgen.rs Fiction Nicht sichtbar in Libgen.rs Non-Fiction Fehler beim Ausführen von exiftool für diese Datei Als „schlechte Datei“ in Z-Library markiert Fehlt in Z-Library Als „Spam“ in Z-Library markiert Datei kann nicht geöffnet werden (z.B weil sie korrumpiert ist oder wegen eines Kopierschutzes) Urheberrechtsanspruch Probleme mit dem Download (z.B Verbindungsprobleme, Fehlermeldung, sehr langsames Internet) Inkorrekte Metadaten (z.B. Titel, Beschreibung, Cover) Anderes Schlechte Qualität (z.B. Probleme mit dem Format, schlechter Scan, fehlende Seiten) Spam / Datei sollte entfernt werden (z.B. wegen Werbung, missbräuchliche Inhalte) %(amount)s (%(amount_usd)s) %(amount)s Gesamt %(amount)s (%(amount_usd)s) Gesamt Brillanter Bücherwurm Glücklicher Bibliothekar Schillernder Datenschützer Beeindruckender Archivar Bonusdownloads Cerlalc Tschechische Metadaten DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Ausgenommen „scimag“ Libgen.rs Sachbücher und Belletristik Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russische Staatsbibliothek Sci-Hub Über Libgen.li „scimag“ Sci-Hub / Libgen „scimag“ Trantor Uploads auf AA Z-Library Z-Library Chinesisch Suche nach Titel, Autor, Sprache, Dateityp, ISBN, MD5, … Suche Autor Beschreibung und Kommentare in Metadaten Auflage Ursprünglicher Dateiname Herausgeber (nach bestimmtem Feld suchen) Titel Publikationsjahr Zeige technische Details (auf Englisch) Diese Coin hat ein höheres Minimum als üblich. Bitte wähle eine andere Laufzeit oder eine andere Coin. Die Anfrage konnte nicht abgeschlossen werden. Bitte versuche es in ein paar Minuten erneut und kontaktiere uns per %(email)s mit einem Screenshot, falls das Problem weiterhin auftritt. Ein unbekannter Fehler ist aufgetreten. Bitte kontaktiere uns per %(email)s mit einem Screenshot. Fehler in der Zahlungsabwicklung. Bitte warten einen Moment und versuche es erneut. Wenn das Problem länger als 24 Stunden bestehen bleibt, kontaktiere uns bitte mit einem Screenshot per %(email)s. Wir führen eine Spendenaktion zur <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">Sicherung</a> der größten Comics Schattenbibliothek der Welt durch. Danke für deine Unterstützung! <a href="/donate">Spende.</a> Wenn Du nicht spenden kannst, denke darüber nach, uns zu unterstützen, indem Du es Deinen Freunden erzählst und uns auf <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> oder <a href="https://t.me/annasarchiveorg">Telegram</a> folgst. Bitte kontaktiere uns nicht bei <a %(a_request)s>Bücheranfragen</a> oder kleinen (<10.000) <a %(a_upload)s>Uploads</a>. Annas Archiv DMCA / Urheberrechtsansprüche Bleib in Kontakt Reddit Alternativen SLUM (%(unaffiliated)s) unabhängig Annas Archiv braucht deine Hilfe! Wenn du jetzt spendest, erhältst du <strong>doppelt</strong> so viele schnelle Downloads. Viele versuchen, uns zu Fall zu bringen, aber wir wehren uns. Wenn du diesen Monat spendest, erhältst du die <strong>doppelte</strong> Anzahl an schnellen Downloads. Gültig bis zum Ende dieses Monats. Menschliches Wissen retten: ein tolles Urlaubsgeschenk! Mitgliedschaften werden entsprechend verlängert. Partner-Server sind aufgrund von Hosting-Schließungen nicht verfügbar. Sie sollten bald wieder online sein. Um die Widerstandsfähigkeit von Annas Archiv zu erhöhen, suchen wir Freiwillige für Mirrors. Wir haben eine neue Spendenmethode zur Verfügung: %(method_name)s. Bitte erwäge zu %(donate_link_open_tag)sspenden</a> - der Betrieb dieser Webseite ist nicht billig, und deine Spende macht wirklich einen Unterschied. Vielen Dank. Lade einen Freund zu uns ein und ihr beide bekommt %(percentage)s%% schnelle Bonusdownloads! Überrasche einen geliebten Menschen, schenk ihm eine Mitgliedschaft. Das perfekte Valentinstagsgeschenk! Mehr Erfahren… Account Aktivität Fortgeschritten Annas Blog ↗ Annas Software ↗ beta Codeexplorer Datensätze Spenden Heruntergeladene Dateien FAQs Startseite Metadaten Verbessern LLM-Daten Anmelden / Registrieren Meine Spenden Öffentliches Profil Suche Sicherheit Torrents Übersetzen ↗ Freiwilligenarbeit & Prämien Kürzlich heruntergeladen: 📚&nbsp;Die weltweit größte, frei verfügbare Open-Source-Bibliothek. ⭐️&nbsp; Enthält Sci-Hub, Library Genesis, Z-Library und mehr. 📈&nbsp;%(book_any)s Bücher, %(journal_article)s wissenschaftliche Aufsätze, %(book_comic)s Comics, %(magazine)s Zeitschriften - für immer erhalten.  und  und mehr DuXiu Internet Archive Leihbibliothek LibGen 📚&nbsp;Die weltweit größte, frei verfügbare Open-Source-Bibliothek. 📈&nbsp;%(book_count)s&nbsp;Bücher, %(paper_count)s&nbsp;wissenschaftliche Aufsätze — für immer erhalten. ⭐️&nbsp;Wir spiegeln %(libraries)s. Wir scrapen und frei veröffentlichen %(scraped)s. Alle unsere Daten und unser Code sind quelloffen. OpenLib Sci-Hub ,  📚 Die weltweit größte, frei verfügbare Open-Source-Bibliothek. ⭐️ Enthält Sci-Hub, Libgen, Zlib, und mehr. Z-Lib Annas Archiv Ungültige Anfrage. Besuche %(websites)s. Die weltweit größte, frei verfügbare Open-Source-Bibliothek. Enthält Sci-Hub, Library Genesis, Z-Library, und mehr. Durchsuche Annas Archiv Annas Archiv Bitte aktualisieren und erneut versuchen. <a %(a_contact)s>Kontaktiere uns, wenn das Problem mehrere Stunden lang andauert. 🔥 Problem beim Laden dieser Seite <li>1. Folge uns auf <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> oder <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Verbreite die Nachricht von Annas Archiv auf Twitter, Reddit, Tiktok, Instagram, in deinem örtlichen Café, in Bibliotheken oder wohin du auch gehst! Wir glauben nicht an Gatekeeping – wenn wir abgeschaltet werden, tauchen wir einfach woanders wieder auf, da unser gesamter Code und unsere Daten vollständig quelloffen sind.</li><li>3. Wenn du dazu in der Lage bist, ziehe eine <a href="/donate">Spende</a> in Betracht.</li><li>4. Hilf mit, unsere Website in <a href="https://translate.annas-software.org/">verschiedene Sprachen zu übersetzen</a>.</li><li>5. Wenn du ein Softwareentwickler bist, erwäge zu unserer <a href="https://annas-software.org/">Open Source Software</a> beizutragen oder unsere <a href="https://en.wikipedia.org/wiki/Pirate_Library_Mirror">Torrents und IPFS</a> zu teilen.</li> 10. Hilf dabei die Wikipediaseite für Annas Archiv in deiner Sprache zu erstellen bzw. zu übersetzen. 11. Wir würden gerne kleine, geschmackvolle Anzeigen auf der Seite platzieren. Wenn du daran interessiert bist hier zu werben, lass es uns wissen. 6. Wenn du Security-Forscher bist, können wir deine Fähigkeiten für Angriffe und Verteidigung gebrauchen. Sieh dir dazu die Seite <a %(a_security)s>Sicherheit</a> an. 7. Wir suchen Experten für Zahlungen an anonyme Händler. Kannst du uns auch dabei helfen mehr Spendenmöglichkeiten zu integrieren (z.B. PayPal, WeChat, Geschenkkarten)? Wenn du jemanden kennst, kontaktiere uns. 8. Wir suchen immer nach mehr Serverkapazitäten. 9. Du kannst dabei helfen Dateiprobleme zu melden, Kommentare zu verfassen und Listen hier direkt auf der Website erstellen. Du kannst auch weitere <a %(a_upload)s>Bücher hochladen</a> oder beim Formatieren der Bücher helfen. Für ausführlichere Informationen darüber, wie du freiwillig mithelfen könntest, besuche unsere <a %(a_volunteering)s>Seite für Freiwilligenarbeit & Prämien</a>. Wir glauben fest an Informationsfreiheit und die Bewahrung von Wissen und Kultur. Mit dieser Suchmaschine bauen wir auf den Schultern von Riesen auf. Wir respektieren die harte Arbeit der Menschen zutiefst, welche die verschiedenen Schattenbibliotheken erstellt haben und wir hoffen, dass diese Suchmaschine ihre Reichweite noch vergrößert. Folge Anna auf <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> oder <a href="https://t.me/annasarchiveorg">Telegram</a>. Für Fragen und Rückmeldungen wende dich bitte via %(email)s an Anna. Account-ID: %(account_id)s Abmelden ❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut. ✅ Du bist jetzt abgemeldet. Aktualisiere die Webseite um dich erneut anzumelden. Verwendete schnelle Downloads (letzte 24 Stunden): <strong>%(used)s / %(total)s</strong> Mitgliedschaft: <strong>%(tier_name)s</strong> bis %(until_date)s <a %(a_extend)s>(verlängern)</a> Du kannst mehrere Mitgliedschaften miteinander kombinieren (schnelle Downloads alle 24 Stunden werden dann zusammenaddiert). Mitgliedschaft: <strong>Keine</strong> <a %(a_become)s>(Werde Mitglied)</a> Kontaktiere Anna unter %(email)s wenn du deine Mitgliedschaft auf eine höhere Stufe upgraden willst. Öffentliches Profil: %(profile_link)s Geheimcode (nicht teilen!): %(secret_key)s anzeigen Begleite uns! Upgrade auf eine <a %(a_tier)s> höhere Stufe </a>, um dich unserer Gruppe anzuschließen. Exklusive Telegram-Gruppe: %(link)s Account welche Downloads? Anmelden Verlier deinen Geheimcode nicht! Ungültiger Geheimcode. Verfiziere deinen Geheimcode und versuche es erneut oder registriere ein neues Benutzerkonto. Geheimcode Gib deinen Geheimcode ein, um dich anzumelden: Alter E-Mail basierter Account? Gib deine <a %(a_open)s>E-Mail hier</a> an. Registriere einen neuen Account Du hast noch keinen Account? Registrierung erfolgreich! Dein Geheimcode ist: <span %(span_key)s>%(key)s</span> Speichere den Geheimcode sorgfältig ab. Wenn du diesen nicht mehr hast, verlierst du den Zugang zu deinem Konto. <li %(li_item)s><strong>Lesezeichen.</strong> Du kannst diese Seite als Lesezeichen speichern, um zu deinem Geheimcode zurückzukehren.</li><li %(li_item)s><strong>Herunterladen.</strong> Klicke <a %(a_download)s>diesen Link</a> um deinen Code herunterzuladen.</li><li %(li_item)s><strong>Passwort-Manager.</strong> Der Einfachheit halber ist der Geheimcode oben vorab ausgefüllt, sodass du ihn beim Anmelden im Passwort-Manager speichern kannst.</li> Anmelden / Registrieren Browser-Verifizierung Warnung: Der Code enthält fehlerhafte Unicode-Zeichen und könnte in verschiedenen Situationen falsch funktionieren. Die Rohdaten können aus der base64-Darstellung in der URL dekodiert werden. Beschreibung Label Präfix URL für einen spezifischen Code Website Codes, die mit „%(prefix_label)s“ beginnen Bitte scrape diese Seiten nicht. Stattdessen empfehlen wir, unsere ElasticSearch- und MariaDB-Datenbanken <a %(a_import)s>zu generieren</a> oder <a %(a_download)s>herunterzuladen</a> und unseren <a %(a_software)s>Open-Source-Code</a> auszuführen. Die Rohdaten können manuell durch JSON-Dateien wie <a %(a_json_file)s>diese hier</a> erkundet werden. Weniger als %(count)s Aufzeichnungen Generische URL Codes-Explorer Index von Untersuche die Codes, mit denen die Datensätze gekennzeichnet sind, nach dem jeweiligen Präfix. Die Spalte „Einträge“ zeigt die Anzahl der Datensätze, die mit Codes mit dem angegebenen Präfix gekennzeichnet sind, wie sie auch in der Suchmaschine angezeigt werden (einschließlich reiner Metadatensätze). In der Spalte „Codes“ wird angezeigt, wie viele tatsächliche Codes ein gegebenes Präfix hat. Bekanntes Code-Präfix „%(key)s“ Mehr… Präfix %(count)s Datensatz, der „%(prefix_label)s“ entspricht %(count)s Datensätze, die „%(prefix_label)s“ entsprechen Codes Einträge „%%s“ wird durch den Wert des Codes ersetzt Annas Archiv durchsuchen Codes URL für spezifischen Code: „%(url)s“ Der Aufbau dieser Seite kann einige Zeit in Anspruch nehmen, weshalb sie ein Cloudflare-Captcha erfordert. <a %(a_donate)s>Mitglieder</a> können das Captcha überspringen. Missbrauch gemeldet: Bessere Version Möchtest du diesen Benutzer wegen missbräuchlichen oder unangemessenen Verhaltens melden? Dateifehler: %(file_issue)s versteckter Kommentar Antworten Missbrauch melden Du hast diesen Benutzer wegen Missbrauchs gemeldet. Urheberrechtsansprüche an diese E-Mail-Adresse werden ignoriert werden, nutze stattdessen das Formular. E-Mail-Adresse anzeigen Wir freuen uns sehr auf Ihr Feedback und Ihre Fragen! Trotzdem müssen wir dich aufgrund des hohen Volumens an Spam und unsinnigen E-Mails darum bitten, mit diesen Kästchen zu bestätigen, dass du die Bedingungen für Kontakt mit uns zur Kenntnis nimmst. Jeder Versuch, uns auf andere Weise bezüglich Urheberrechtsansprüchen zu kontaktieren, wird automatisch gelöscht. Für DMCA / Urheberrechtsansprüche nutze <a %(a_copyright)s>dieses Formular</a>. E-Mail URLs auf Annas Archiv (erforderlich). Eine pro Zeile. Bitte nur URLs angeben, die genau dieselbe Ausgabe eines Buches beschreiben. Wenn du einen Anspruch für mehrere Bücher oder mehrere Ausgaben geltend machen möchtest, reiche dieses Formular bitte mehrfach ein. Ansprüche, die mehrere Bücher oder Ausgaben zusammenfassen, werden abgelehnt. Adresse (erforderlich) Klare Beschreibung des Ausgangsmaterials (erforderlich) E-Mail (erforderlich) URLs zum Ausgangsmaterial, eine pro Zeile (erforderlich). Bitte gib so viele wie möglich an, um uns bei der Überprüfung deines Anspruchs zu helfen (z.B. Amazon, WorldCat, Google Books, DOI). ISBNs des Ausgangsmaterials (falls zutreffend). Eine pro Zeile. Bitte nur solche angeben, die genau mit der Ausgabe übereinstimmen, für die du einen Urheberrechtsanspruch meldest. Dein Name (erforderlich) ❌ Etwas ist schief gelaufen. Bitte lade die Seite neu und versuche es erneut. ✅ Vielen Dank für die Einreichung des Urheberrechtsanspruchs. Wir werden ihn so schnell wie möglich überprüfen. Bitte lade die Seite neu, um einen weiteren einzureichen. <a %(a_openlib)s>Open Library</a> URLs des Ausgangsmaterials, eine pro Zeile. Bitte nimm dir einen Moment Zeit, um in der Open Library nach dem Ausgangsmaterial zu suchen. Dies wird uns helfen, deinen Anspruch zu überprüfen. Telefonnummer (erforderlich) Erklärung und Unterschrift (erforderlich) Anspruch einreichen Wenn du einen DMCA- oder anderen Urheberrechtsanspruch hast, fülle bitte dieses Formular so genau wie möglich aus. Wenn du auf Probleme stoßt, kontaktiere uns bitte unter unserer speziellen DMCA-Adresse: %(email)s. Beachte, dass an diese Adresse gesendete Ansprüche nicht bearbeitet werden, sie ist nur für Fragen vorgesehen. Bitte verwenden das untenstehende Formular, um Ansprüche einzureichen. DMCA / Urheberrechtsanspruch-Formular Beispieldatensatz auf Annas Archive Torrents von Annas Archive Annas Archive Containers Format Skripte zum Importieren von Metadaten Wenn du daran interessiert bist, diesen Datensatz für <a %(a_archival)s>Archivierungs</a>- oder <a %(a_llm)s>LLM-Trainingszwecke </a> zu spiegeln, kontaktiere uns bitte. Zuletzt aktualisiert: %(date)s Hauptwebsite %(source)s Metadaten-Dokumentation (die meisten Felder) Dateien gespiegelt von Annas Archive: %(count)s (%(percent)s%%) Ressourcen Gesamtdateien: %(count)s Gesamtdateigröße: %(size)s Unser Blogbeitrag über diese Daten <a %(duxiu_link)s>Duxiu</a> ist eine riesige Datenbank gescannter Bücher, erstellt von der <a %(superstar_link)s>SuperStar Digital Library Group</a>. Die meisten sind akademische Bücher, die gescannt wurden, um sie Universitäten und Bibliotheken digital zur Verfügung zu stellen. Für unser englischsprachiges Publikum haben <a %(princeton_link)s>Princeton</a> und die <a %(uw_link)s>University of Washington</a> gute Übersichten. Es gibt auch einen ausgezeichneten Artikel, der mehr Hintergrundinformationen bietet: <a %(article_link)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine“</a>. Die Bücher von Duxiu wurden lange Zeit im chinesischen Internet über Schwarzkopien verbreitet. Normalerweise werden sie von Wiederverkäufern für weniger als einen Dollar verkauft. Sie werden typischerweise mit dem chinesischen Äquivalent von Google Drive verteilt, das oft gehackt wurde, um mehr Speicherplatz zu erhalten. Einige technische Details findest du <a %(link1)s>hier</a> und <a %(link2)s>hier</a>. Obwohl die Bücher halböffentlich verteilt wurden, ist es ziemlich schwierig, sie in großen Mengen zu erhalten. Wir hatten dies ganz oben auf unserer TODO-Liste und mehrere Monate Vollzeitarbeit dafür eingeplant. Doch Ende 2023 meldete sich ein unglaublich toller und talentierter Freiwilliger bei uns und teilte uns mit, dass er all diese Arbeit bereits erledigt hatte — und das zu hohen Kosten. Er teilte die gesamte Sammlung mit uns, ohne etwas im Gegenzug zu erwarten – abgesehen von der Zusicherung einer langfristigen Bewahrung. Das ist wirklich bemerkenswert. Weitere Informationen von unseren Freiwilligen (ursprüngliche Anmerkungen): Angepasst von unserem <a %(a_href)s>Blogbeitrag</a>. DuXiu 读秀 %(count)s Datei %(count)s Dateien Dieser Datensatz steht in engem Zusammenhang mit dem <a %(a_datasets_openlib)s>Open Library Dataset</a>. Er enthält einen Scrape aller Metadaten und einen großen Teil der Dateien aus der Controlled Digital Lending Library der IA. Updates werden im <a %(a_aac)s>Annas Archive Containers Format</a> veröffentlicht. Diese Datensätze stammen direkt aus dem Open Library-Datensatz, enthalten aber auch Einträge, die nicht in der Open Library enthalten sind. Wir haben auch eine Reihe von Dateien, die im Laufe der Jahre von Community-Mitgliedern gescrapt wurden. Die Sammlung besteht aus zwei Teilen. Du benötigst beide Teile, um alle Daten zu erhalten (außer veralteten Torrents, die auf der Torrents-Seite durchgestrichen sind). Digitale Leihbibliothek unser erster Release, bevor wir auf das standardisierte <a %(a_aac)s>Annas Archive Containers (AAC) Format</a> umgestellt haben. Enthält Metadaten (als JSON und XML), PDFs (aus den digitalen Leihsystemen acsm und lcpdf) und Cover-Thumbnails. inkrementelle neue Releases, die AAC verwenden. Enthält nur Metadaten mit Zeitstempeln nach dem 01.01.2023, da der Rest bereits von „ia“ abgedeckt ist. Außerdem alle PDF-Dateien, diesmal aus den Leihsystemen acsm und „bookreader“ (IAs Web-Reader). Trotz des nicht ganz passenden Namens fügen wir weiterhin bookreader-Dateien in die Sammlung ia2_acsmpdf_files ein, da sie sich gegenseitig ausschließen. IA Controlled Digital Lending 98%%+ der Dateien sind durchsuchbar. Unsere Mission ist es, alle Bücher der Welt (sowie wissenschaftliche Aufsätze, Zeitschriften usw.) zu archivieren und offen zugänglich zu machen. Wir sind der Meinung, dass alle Bücher weiträumig gespiegelt werden sollten, um Redundanz und eine hohe Verfügbarkeit zu gewährleisten. Deshalb sammeln wir Dateien aus verschiedenen Quellen. Einige Quellen sind völlig offen und können in großen Mengen gespiegelt werden (wie Sci-Hub). Andere sind geschlossen und geschützt, daher versuchen wir sie zu scrapen, um die Bücher zu „befreien“. Wieder andere liegen irgendwo dazwischen. Alle unsere Daten können <a %(a_torrents)s>getorrentet</a> werden, und alle unsere Metadaten können als ElasticSearch- und MariaDB-Datenbanken <a %(a_anna_software)s>generiert</a> oder <a %(a_elasticsearch)s>heruntergeladen</a> werden. Die Rohdaten können manuell durch JSON-Dateien wie <a %(a_dbrecord)s>diese</a> erkundet werden. Metadaten ISBN-Website Zuletzt aktualisiert: %(isbn_country_date)s (%(link)s) Ressourcen Die Internationale ISBN-Agentur veröffentlicht regelmäßig die Bereiche, die sie den nationalen ISBN-Agenturen zugewiesen hat. Daraus können wir ableiten, zu welchem Land, welcher Region oder welcher Sprachgruppe diese ISBN gehört. Derzeit nutzen wir diese Daten indirekt über die <a %(a_isbnlib)s>isbnlib</a> Python-Bibliothek. ISBN-Länderinformationen Dies ist ein Dump von vielen Anfragen an isbndb.com im September 2022. Wir haben versucht, alle ISBN-Bereiche abzudecken. Dies sind etwa 30,9 Millionen Datensätze. Auf ihrer Website behaupten sie, tatsächlich 32,6 Millionen Datensätze zu haben, also haben wir möglicherweise einige irgendwie übersehen, oder <em>sie</em> machen eventuell etwas falsch. Die JSON-Antworten von Ihrem Server sind ziemlich roh. Ein Datenqualitätsproblem, das wir bemerkt haben, ist, dass für ISBN-13-Nummern, die mit einem anderen Präfix als „978-“ beginnen, immer noch ein „isbn“-Feld enthalten ist. Dieses enthält einfach die ISBN-13-Nummer mit den ersten drei abgeschnittenen Zahlen (und der neu berechneten Prüfziffer). Das ist offensichtlich falsch, aber da sie es offensichtlich so handhaben, haben wir es nicht geändert. Ein weiteres potenzielles Problem, auf das du stoßen könntest, ist die Tatsache, dass das „isbn13“-Feld Duplikate enthält, sodass du es nicht als Primärschlüssel in einer Datenbank verwenden kannst. Die Kombination der Felder „isbn13“+„isbn“ scheint jedoch eindeutig zu sein. Veröffentlichung 1 (2022-10-31) Belletristik-Torrents sind im Rückstand (obwohl IDs ~4-6M nicht getorrented wurden, da sie sich mit unseren Zlib-Torrents überschneiden). Unser Blogbeitrag über die Veröffentlichung der Comics Comics-Torrents auf Annas Archiv Die Hintergrundgeschichte der verschiedenen Library Genesis-Forks findest du auf der Seite für <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li enthält die meisten der gleichen Inhalte und Metadaten wie Libgen.rs, hat jedoch einige zusätzliche Sammlungen, nämlich Comics, Zeitschriften und Normen-Dokumente. Es hat auch <a %(a_scihub)s>Sci-Hub</a> in seine Metadaten und Suchmaschine integriert, was wir für unsere Datenbank verwenden. Die Metadaten für diese Bibliothek sind frei verfügbar <a %(a_libgen_li)s>bei libgen.li</a>. Dieser Server ist jedoch langsam und unterstützt keine Wiederaufnahme unterbrochener Verbindungen. Die gleichen Dateien sind auch auf <a %(a_ftp)s>einem FTP-Server</a> verfügbar, der besser funktioniert. Sachbücher scheinen sich ebenfalls verändert zu haben, jedoch ohne neue Torrents. Es scheint, dass dies seit Anfang 2022 geschehen ist, obwohl wir dies nicht überprüft haben. Laut dem Administrator von Libgen.li sollte die „fiction_rus“-Sammlung (russische Belletristik) durch regelmäßig veröffentlichte Torrents von <a %(a_booktracker)s>booktracker.org</a> abgedeckt sein, insbesondere die <a %(a_flibusta)s>flibusta</a> und <a %(a_librusec)s>lib.rus.ec</a> Torrents (die wir <a %(a_torrents)s>hier</a> spiegeln, obwohl wir noch nicht festgestellt haben, welche Torrents zu welchen Dateien gehören). Die Belletristiksammlung hat eigene Torrents (abweichend von <a %(a_href)s>Libgen.rs</a>), beginnend bei %(start)s. Bestimmte Bereiche ohne Torrents (wie Belletristikbereiche f_3463000 bis f_4260000) sind wahrscheinlich Z-Library (oder andere doppelte) Dateien, obwohl wir möglicherweise eine Deduplizierung durchführen und Torrents für lgli-einzigartige Dateien in diesen Bereichen erstellen möchten. Statistiken für alle Sammlungen findest du <a %(a_href)s>auf der Website von Libgen</a>. Torrents sind für die meisten zusätzlichen Inhalte verfügbar, insbesondere wurden Torrents für Comics, Zeitschriften und Normen-Dokumente in Zusammenarbeit mit Annas Archiv veröffentlicht. Beachte, dass die Torrent-Dateien, die sich auf „libgen.is“ beziehen, ausdrücklich Spiegelungen von <a %(a_libgen)s>Libgen.rs</a> sind („.is“ ist eine andere Domain, die von Libgen.rs verwendet wird). Eine hilfreiche Ressource zur Nutzung der Metadaten ist <a %(a_href)s>diese Seite</a>. %(icon)s Ihre „fiction_rus“-Sammlung (russische Fiktion) hat keine eigenen Torrents, wird aber durch Torrents von anderen abgedeckt, und wir besitzen einen <a %(fiction_rus)s>Mirror</a>. Russische Belletristik-Torrents auf Annas Archiv Belletristik-Torrents auf Annas Archiv Diskussionsforum Metadaten Metadaten via FTP Zeitschriften-Torrents auf Annas Archiv Informationen zu Metadatenfeldern Spiegelung anderer Torrents (und einzigartiger Belletristik- und Comics-Torrents) Normen-Dokumente-Torrents auf Annas Archiv Libgen.li Torrents von Annas Archiv (Buchcover) Library Genesis ist dafür bekannt, ihre Daten bereits großzügig in großen Mengen über Torrents verfügbar zu machen. Unsere Libgen-Sammlung besteht aus zusätzlichen Daten, die sie nicht direkt veröffentlichen, in Zusammenarbeit mit ihnen. Vielen Dank an alle Beteiligten von Library Genesis für die Zusammenarbeit mit uns! Unser Blog über die Veröffentlichung der Buchcover Diese Seite bezieht sich auf die „.rs“-Version. Sie ist dafür bekannt, sowohl ihre Metadaten als auch den vollständigen Inhalt ihres Buchkatalogs konsequent zu veröffentlichen. Ihre Buchsammlung ist in einen Belletristik- und einen Sachbuch-Teil unterteilt. Eine hilfreiche Ressource zur Nutzung der Metadaten ist <a %(a_metadata)s>diese Seite</a> (blockiert IP-Bereiche, VPN könnte erforderlich sein). Ab März 2024 werden neue Torrents in <a %(a_href)s>diesem Forenthread</a> gepostet (blockiert IP-Bereiche, VPN könnte erforderlich sein). Belletristik-Torrents auf Annas Archiv Libgen.rs Belletristik-Torrents Libgen.rs Diskussionsforum Libgen.rs Metadaten Libgen.rs Metadatenfeldinformationen Libgen.rs Sachbuch-Torrents Sachbuch-Torrents auf Annas Archiv %(example)s für ein Belletristikbuch. Diese <a %(blog_post)s>erste Veröffentlichung</a> ist ziemlich klein: etwa 300GB an Buchcovern aus dem Libgen.rs-Fork, sowohl Belletristik als auch Sachbuch. Sie sind in der gleichen Weise organisiert, wie sie auf libgen.rs erscheinen, z.B.: %(example)s für ein Sachbuch. Genau wie bei der Z-Library-Sammlung haben wir sie alle in eine große .tar-Datei gepackt, die mit <a %(a_ratarmount)s>ratarmount</a> gemountet werden kann, falls du die Dateien direkt bereitstellen möchtest. Veröffentlichung 1 (%(date)s) Kurz gesagt, der Hintergrund der verschiedenen Library Genesis (oder „Libgen“) Forks ist, dass im Laufe der Zeit die verschiedenen Beteiligten von Library Genesis sich zerstritten und getrennte Wege gingen. Laut diesem <a %(a_mhut)s>Forenbeitrag</a> wurde Libgen.li ursprünglich unter „http://free-books.dontexist.com“ gehostet. Die „.fun“-Version wurde vom ursprünglichen Gründer erstellt. Sie wird zugunsten einer neuen, stärker verteilten Version überarbeitet. Die <a %(a_li)s>„.li“-Version</a> hat eine riesige Sammlung von Comics sowie andere Inhalte, die (noch) nicht als Bulk-Download über Torrents verfügbar sind. Sie hat eine separate Torrent-Sammlung von Belletristik-Büchern und enthält die Metadaten von <a %(a_scihub)s>Sci-Hub</a> in ihrer Datenbank. Die „.rs“-Version hat sehr ähnliche Daten und veröffentlicht ihre Sammlung in Bulk-Torrents. Sie ist grob in einen „Belletristik“- und einen „Sachbuch“-Bereich unterteilt. Ursprünglich unter „http://gen.lib.rus.ec“. <a %(a_zlib)s>Z-Library</a> ist in gewisser Weise auch ein Fork von Library Genesis, obwohl sie einen anderen Namen für ihr Projekt verwendet haben. Libgen.rs Wir bereichern unsere Sammlung auch mit Nur-Metadaten-Quellen, die wir mit Dateien abgleichen können, z.B. durch ISBN-Nummern oder andere Felder. Unten findest du eine Übersicht dieser Quellen. Auch hier sind einige dieser Quellen vollständig offen, während wir andere scrapen müssen. Beachte, dass wir bei der Metadatensuche die Originaldatensätze anzeigen. Wir führen keine Zusammenführung von Datensätzen durch. Nur-Metadaten-Quellen Die Open Library ist ein Open-Source-Projekt des Internet Archive, das darauf abzielt, alle Bücher der Welt zu katalogisieren. Es verfügt über eine der größten Buchscannoperationen weltweit und viele Bücher stehen zum digitalen Ausleihen bereit. Der Buchmetadatakatalog von Open Library ist frei zum Download verfügbar und in Annas Archiv enthalten (derzeit jedoch nicht in der Suche, außer wenn du explizit nach einer Open-Library-ID suchst). Open Library Duplikate ausgeschlossen Zuletzt aktualisiert Prozentsätze der Dateianzahl %% gespiegelt von AA / Torrents verfügbar Größe Quelle Unten findest du einen kurzen Überblick über die Quellen der Dateien in Annas Archiv. Da die Schattenbibliotheken oft Daten untereinander abgleichen und synchronisieren, gibt es erhebliche Überschneidungen zwischen den Bibliotheken. Deshalb summieren sich die Zahlen nicht zur Gesamtzahl. Der Prozentsatz „gespiegelt und geseeded von Annas Archive“ zeigt, wie viele Dateien wir selbst spiegeln. Wir seeden diese Dateien in großen Mengen über Torrents und stellen sie über Partner-Websites zum direkten Download zur Verfügung. Überblick Gesamt Torrents auf Annas Archiv Für Hintergrundinformationen zu Sci-Hub besuche bitte die <a %(a_scihub)s>offizielle Website</a>, die <a %(a_wikipedia)s>Wikipedia-Seite</a> oder höre dieses <a %(a_radiolab)s>Podcast-Interview</a> an. Beachte, dass Sci-Hub seit <a %(a_reddit)s>2021 eingefroren</a> ist. Es war schon früher eingefroren, aber 2021 wurden ein paar Millionen wissenschaftliche Aufsätze hinzugefügt. Dennoch werden einige wenige wissenschaftliche Aufsätze zu den Libgen „scimag“-Sammlungen noch immer hinzugefügt, jedoch nicht genug, um neue Bulk-Torrents zu rechtfertigen. Wir verwenden die Sci-Hub-Metadaten, die von <a %(a_libgen_li)s>Libgen.li</a> in seiner „scimag“-Sammlung bereitgestellt werden. Wir verwenden auch den <a %(a_dois)s>dois-2022-02-12.7z</a>-Datensatz. Beachte, dass die „smarch“-Torrents <a %(a_smarch)s>veraltet</a> sind und daher nicht in unserer Torrent-Liste enthalten sind. Torrents auf Libgen.li Torrents auf Libgen.rs Metadaten und Torrents Updates auf Reddit Podcast-Interview Wikipedia-Seite Sci-Hub Sci-Hub: seit 2021 eingefroren; das meiste ist über Torrents verfügbar Libgen.li: seitdem nur geringfügige Ergänzungen</div> Einige Quellbibliotheken unterstützten das massenhafte Teilen ihrer Daten über Torrents, während andere ihre Sammlung nicht so bereitwillig teilen. Im letzteren Fall versucht Annas Archiv, die Sammlungen zu scrapen und verfügbar zu machen (siehe unsere <a %(a_torrents)s>Torrents</a>-Seite). Es gibt auch Grenzfälle, zum Beispiel, wenn Quellbibliotheken bereit sind, ihre Ressourcen zu teilen, aber nicht über die nötigen Mittel verfügen. In solchen Fällen versuchen wir ebenfalls zu helfen. Unten findest du eine Übersicht darüber, wie wir mit den verschiedenen Quellbibliotheken interagieren. Quellbibliotheken %(icon)s Verschiedene Dateidatenbanken, die im chinesischen Internet verstreut sind; oft kostenpflichtige Datenbanken. %(icon)s Die meisten Dateien sind nur mit Premium-BaiduYun-Konten zugänglich; langsame Download-Geschwindigkeiten. %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(duxiu)s>DuXiu-Dateien</a> %(icon)s Verschiedene Metadaten-Datenbanken, die im chinesischen Internet verstreut sind; oft kostenpflichtige Datenbanken %(icon)s Keine leicht zugänglichen Metadaten-Dumps für ihre gesamte Sammlung verfügbar. %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(duxiu)s>DuXiu-Metadaten</a> Dateien %(icon)s Dateien nur eingeschränkt zum Ausleihen verfügbar, mit verschiedenen Zugriffsrestriktionen %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(ia)s>IA-Dateien</a> %(icon)s Einige Metadaten sind über <a %(openlib)s>Open Library-Datenbank-Dumps</a> verfügbar, aber diese decken nicht die gesamte IA-Sammlung ab %(icon)s Keine leicht zugänglichen Metadaten-Dumps für ihre gesamte Sammlung verfügbar %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(ia)s>IA-Metadaten</a> Zuletzt aktualisiert %(icon)s Annas Archiv und Libgen.li verwalten gemeinsam Sammlungen von <a %(comics)s>Comics</a>, <a %(magazines)s>Zeitschriften</a>, <a %(standarts)s>Normen-Dokumenten</a> und <a %(fiction)s>Belletristik (abgeleitet von Libgen.rs)</a>. %(icon)s Sachbuch-Torrents werden mit Libgen.rs geteilt (und <a %(libgenli)s>hier</a> gespiegelt). %(icon)s Vierteljährliche <a %(dbdumps)s>HTTP-Datenbank-Dumps</a> %(icon)s Automatisierte Torrents für <a %(nonfiction)s>Sachbücher</a> und <a %(fiction)s>Belletristik</a> %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(covers)s>Buchcover-Torrents</a> %(icon)s Tägliche <a %(dbdumps)s>HTTP-Datenbank-Dumps</a> Metadaten %(icon)s Monatliche <a %(dbdumps)s>Datenbank-Dumps</a> %(icon)s Datentorrents sind <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a> und <a %(libgenli)s>hier</a> verfügbar %(icon)s Einige neue Dateien <a%(libgenrs)s>werden</a> weiterhin zu Libgens „scimag“ <a %(libgenli)s>hinzugefügt</a>, aber nicht genug, um neue Torrents zu rechtfertigen %(icon)s Sci-Hub hat seit 2021 keine neuen Dateien mehr hinzugefügt. %(icon)s Metadaten-Dumps sind <a %(scihub1)s>hier</a> und <a %(scihub2)s>hier</a> verfügbar , sowie als Teil der <a %(libgenli)s>Libgen.li-Datenbank</a> (die wir verwenden) Quelle %(icon)s Verschiedene kleinere oder einmalige Quellen. Wir ermutigen die Leute, Dateien zuerst in andere Schattenbibliotheken hochzuladen, aber manchmal haben Leute Sammlungen, die zu groß sind, um von anderen sortiert zu werden und nicht groß genug, um eine eigene Kategorie zu rechtfertigen. %(icon)s Nicht direkt in großen Mengen verfügbar, gegen Scraping geschützt %(icon)s Annas Archiv verwaltet eine Sammlung von <a %(worldcat)s>OCLC (WorldCat)-Metadaten</a> %(icon)s Annas Archiv und Z-Library verwalten gemeinsam eine Sammlung von <a %(metadata)s>Z-Library-Metadaten</a> und <a %(files)s>Z-Library-Dateien</a> Datensätze Wir kombinieren alle oben genannten Quellen zu einer einheitlichen Datenbank, die wir zur Bereitstellung dieser Website verwenden. Diese einheitliche Datenbank ist nicht direkt verfügbar, aber da Annas Archiv vollständig Open Source ist, kann sie relativ einfach als ElasticSearch- und MariaDB-Datenbanken <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>heruntergeladen</a> werden. Die Skripte auf dieser Seite laden automatisch alle erforderlichen Metadaten von den oben genannten Quellen herunter. Wenn du unsere Daten erkunden möchtest, bevor du diese Skripte lokal ausführst, kannst du auch unsere JSON-Dateien ansehen, die weiter zu anderen JSON-Dateien verlinken. <a %(a_json)s>Diese Datei</a> ist ein guter Ausgangspunkt. Vereinheitlichte Datenbank Torrents von Annas Archiv durchsuchen suchen Verschiedene kleinere oder einmalige Quellen. Wir ermutigen die Leute, Dateien zuerst in andere Schattenbibliotheken hochzuladen, aber manchmal haben Leute Sammlungen, die zu groß sind, um von anderen sortiert zu werden und nicht groß genug, um eine eigene Kategorie zu rechtfertigen. Übersicht von der <a %(a1)s>Datensätze-Seite</a>. Von <a %(a_href)s>aaaaarg.fail</a>. Scheint ziemlich vollständig zu sein. Von unserem Freiwilligen <q>cgiym</q>. Von einem <a %(a_href)s><q>ACM Digital Library 2020</q></a> Torrent. Hat eine ziemlich hohe Überschneidung mit bestehenden wissenschaftlichen Aufsatzsammlungen, aber sehr wenige MD5-Übereinstimmungen, daher haben wir beschlossen, es vollständig zu behalten. Scrape von <q>iRead eBooks</q> (= phonetisch <q>ai rit i-books</q>; airitibooks.com), durch den Freiwilligen <q>j</q>. Entspricht den <q>airitibooks</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. Aus der Sammlung <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Teilweise aus der Originalquelle, teilweise von the-eye.eu, teilweise von anderen Mirrors. Von einer privaten Bücher-Torrent-Website namens <a %(a_href)s>Bibliotik</a> (oft als <q>Bib</q> bezeichnet), deren Bücher nach Namen (A.torrent, B.torrent) gebündelt und über the-eye.eu verteilt wurden. Von unserem Freiwilligen <q>bpb9v</q>. Für weitere Informationen über <a %(a_href)s>CADAL</a> siehe die Anmerkungen auf unserer <a %(a_duxiu)s>DuXiu-Datensatzseite</a>. Mehr von unserem Freiwilligen <q>bpb9v</q>, hauptsächlich DuXiu-Dateien sowie ein Ordner <q>WenQu</q> und <q>SuperStar_Journals</q> (SuperStar ist die Firma hinter DuXiu). Von unserem Freiwilligen <q>cgiym</q>, chinesische Texte aus verschiedenen Quellen (als Unterverzeichnisse dargestellt), einschließlich von <a %(a_href)s>China Machine Press</a> (ein großer chinesischer Verlag). Nicht-chinesische Sammlungen (als Unterverzeichnisse dargestellt) von unserem Freiwilligen <q>cgiym</q>. Scrape von Büchern über chinesische Architektur, durch den Freiwilligen <q>cm</q>: <q>Ich habe die Daten durch Ausnutzung einer Netzwerkschwachstelle beim Verlag bekommen, aber diese Lücke wurde inzwischen geschlossen</q>. Entspricht den <q>chinese_architecture</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. Bücher vom akademischen Verlag <a %(a_href)s>De Gruyter</a>, gesammelt aus einigen großen Torrents. Scrape von <a %(a_href)s>docer.pl</a>, einer polnischen File-Sharing-Website, die sich auf Bücher und andere schriftliche Werke konzentriert. Gescrapt Ende 2023 vom Freiwilligen <q>p</q>. Wir haben keine guten Metadaten von der ursprünglichen Webseite (nicht einmal Dateierweiterungen), aber wir haben nach buchähnlichen Dateien gefiltert und konnten oft Metadaten aus den Dateien selbst extrahieren. DuXiu EPUBs, direkt von DuXiu, gesammelt vom Freiwilligen <q>w</q>. Nur aktuelle DuXiu-Bücher sind direkt als E-Books verfügbar, daher müssen die meisten davon neueren Datums sein. Verbleibende DuXiu-Dateien vom Freiwilligen <q>m</q>, die nicht im proprietären PDG-Format von DuXiu waren (der Haupt-<a %(a_href)s>DuXiu-Datensatz</a>). Gesammelt aus vielen Originalquellen, leider ohne diese Quellen im Dateipfad zu nennen. <span></span> <span></span> <span></span> Scrape von erotischen Büchern, durch den Freiwilligen <q>do no harm</q>. Entspricht den <q>hentai</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. <span></span> <span></span> Sammlung, gescrapt von einem japanischen Manga-Verlag vom Freiwilligen <q>t</q>. <a %(a_href)s>Ausgewählte gerichtliche Archive von Longquan</a>, bereitgestellt vom Freiwilligen <q>c</q>. Scrape von <a %(a_href)s>magzdb.org</a>, einem Verbündeten von Library Genesis (ist auf der libgen.rs-Homepage verlinkt), der seine Dateien jedoch nicht direkt bereitstellen wollte. Erhalten vom Freiwilligen <q>p</q> Ende 2023. <span></span> Verschiedene kleine Uploads, zu klein, um als eigene Unterkollektion zu gelten, aber als Verzeichnis dargestellt. Das <q>oo42hcksBxZYAOjqwGWu</q> Verzeichnis entspricht den <q>czech_oo42hcks</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. E-Books von AvaxHome, einer russischen Filesharing-Webseite. Archiv von Zeitungen und Zeitschriften. Entspricht den <q>newsarch_magz</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. Scrape des <a %(a1)s>Philosophy Documentation Center</a>. Sammlung vom Freiwilligen <q>o</q>, der polnische Bücher direkt von Originalveröffentlichungs-Websites (<q>Szene<q>) gesammelt hat. Kombinierte Sammlungen von <a %(a_href)s>shuge.org</a> von den Freiwilligen <q>cgiym</q> und <q>woz9ts</q>. <span></span> <a %(a_href)s>„Imperial Library of Trantor“</a> (benannt nach der fiktiven Bibliothek), gescrapt 2022 vom Freiwilligen „t“. Entspricht den <q>trantor</q> Metadaten in <a %(a1)s><q>Andere Metadaten Scrapes</q></a>. <span></span> <span></span> <span></span> Unter-Unter-Sammlungen (als Verzeichnisse dargestellt) vom Freiwilligen „woz9ts“: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (von <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan) und mebook (mebook.cc, 我的小书屋, mein kleines Bücherzimmer — woz9ts: „Diese Seite konzentriert sich hauptsächlich auf das Teilen hochwertiger E-Book-Dateien, von denen einige vom Besitzer selbst gesetzt wurden. Der Besitzer wurde <a %(a_arrested)s>2019 verhaftet</a> und jemand hat eine Sammlung der von ihm geteilten Dateien erstellt.“). Verbleibende DuXiu-Dateien vom Freiwilligen <q>woz9ts</q>, die nicht im proprietären DuXiu-PDG-Format vorlagen (müssen noch in PDF konvertiert werden). Die <q>Upload</q> -Sammlung ist in kleinere Unterkollektionen aufgeteilt, die in den AACIDs und Torrent-Namen angegeben sind. Alle Unterkollektionen wurden zuerst gegen die Hauptsammlung dedupliziert, obwohl die Metadaten-<q> upload_records</q> -JSON-Dateien immer noch viele Verweise auf die Originaldateien enthalten. Nicht-Buch-Dateien wurden auch aus den meisten Unterkollektionen entfernt und sind typischerweise <em>nicht</em> in der <q> upload_records</q> -JSON vermerkt. Die Unterkollektionen sind: Anmerkungen Unterkollektion Viele Unterkollektionen bestehen selbst aus Unter-Unterkollektionen (z.B. von verschiedenen Originalquellen), die als Verzeichnisse in den <q> filepath</q> -Feldern dargestellt sind. Uploads zu Annas Archiv Unser Blogbeitrag über diese Daten <a %(a_worldcat)s>WorldCat</a> ist eine proprietäre Datenbank der gemeinnützigen Organisation <a %(a_oclc)s>OCLC</a>, die Metadatenaufzeichnungen von Bibliotheken aus der ganzen Welt aggregiert. Es ist wahrscheinlich die größte Bibliotheks-Metadatensammlung der Welt. Im Oktober 2023 haben wir einen umfassenden Scrape der OCLC (WorldCat)-Datenbank im <a %(a_aac)s>Annas Archiv Containers-Format</a> <a %(a_scrape)s>veröffentlicht</a>. Oktober 2023, erste Veröffentlichung: OCLC (WorldCat) Torrents von Annas Archiv Beispiel-Datensatz in Annas Archiv (Originalsammlung) Beispiel-Datensatz in Annas Archiv („zlib3“-Sammlung) Torrents von Annas Archiv (Metadaten + Inhalt) Blogbeitrag über Veröffentlichung 1 Blogbeitrag über Veröffentlichung 2 Ende 2022 wurden die mutmaßlichen Gründer der Z-Library verhaftet und die Domains von den US-Behörden beschlagnahmt. Seitdem ist die Website langsam wieder online gegangen. Es ist unbekannt, wer sie derzeit betreibt. Aktualisierung ab Februar 2023. Z-Library hat seine Wurzeln in der <a %(a_href)s>Library Genesis</a>-Community und wurde ursprünglich ausgehend von deren Daten gestartet. Seitdem hat es sich erheblich professionalisiert und verfügt über eine viel modernere Benutzeroberfläche. Daher erhalten sie viel mehr Spenden, sowohl monetär zur Verbesserung ihrer Website als auch neue Bücherspenden. Sie haben eine große Sammlung zusätzlich zu Library Genesis aufgebaut. Die Sammlung besteht aus drei Teilen. Die ursprünglichen Beschreibungsseiten für die ersten beiden Teile sind unten angegeben. Du benötigst alle drei Teile, um alle Daten zu erhalten (außer veraltete Torrents, die auf der Torrents-Seite durchgestrichen sind). %(title)s: unsere erste Veröffentlichung. Dies war die allererste Veröffentlichung dessen, was damals „Pirate Library Mirror“ („pilimi“) genannt wurde. %(title)s: zweite Veröffentlichung, diesmal mit allen Dateien in .tar-Dateien verpackt. %(title)s: inkrementelle neue Veröffentlichungen, die das <a %(a_href)s>Annas Archiv Container (AAC) Format</a> verwenden, jetzt in Zusammenarbeit mit dem Z-Library-Team veröffentlicht. Der ursprüngliche Mirror wurde im Laufe der Jahre 2021 und 2022 mühsam erstellt. Zu diesem Zeitpunkt ist er leicht veraltet: Er spiegelt den Stand der Sammlung im Juni 2021 wider. Wir werden dies in Zukunft aktualisieren. Im Moment konzentrieren wir uns darauf, diese erste Veröffentlichung herauszubringen. Da Library Genesis bereits mit öffentlichen Torrents gesichert ist und in der Z-Library enthalten ist, haben wir im Juni 2022 eine grundlegende Duplikatsbereinigung gegen Library Genesis durchgeführt. Dafür haben wir MD5-Hashes verwendet. Es gibt wahrscheinlich noch viel mehr doppelte Inhalte in der Bibliothek, wie z. B. mehrere Dateiformate desselben Buches. Dies ist schwer genau zu erkennen, daher tun wir es nicht. Nach der Duplikatsbereinigung bleiben uns über 2 Millionen Dateien, die insgesamt knapp 7 TB umfassen. Die Sammlung besteht aus zwei Teilen: einem MySQL-“.sql.gz”-Dump der Metadaten und den 72 Torrent-Dateien von jeweils etwa 50-100 GB. Die Metadaten enthalten die Daten, wie sie von der Z-Library-Website gemeldet wurden (Titel, Autor, Beschreibung, Dateityp), sowie die tatsächliche Dateigröße und md5sum, die wir beobachtet haben, da diese manchmal nicht übereinstimmen. Es scheint Bereiche von Dateien zu geben, für die die Z-Library selbst falsche Metadaten hat. In einigen Einzelfällen haben wir möglicherweise auch Dateien falsch heruntergeladen, die wir in Zukunft zu erkennen und zu beheben versuchen werden. Die großen Torrent-Dateien enthalten die eigentlichen Buchdaten, wobei die Z-Library-ID als Dateiname verwendet wird. Die Dateierweiterungen können mithilfe des Metadaten-Dumps rekonstruiert werden. Die Sammlung ist eine Mischung aus Sachbuch- und Belletristik-Inhalten (nicht wie in Library Genesis getrennt). Die Qualität variiert ebenfalls stark. Diese erste Veröffentlichung ist jetzt vollständig verfügbar. Beachte, dass die Torrent-Dateien nur über unseren Tor-Mirror verfügbar sind. Veröffentlichung 1 (%(date)s) Dies ist eine einzelne zusätzliche Torrent-Datei. Sie enthält keine neuen Informationen, aber sie enthält einige Daten, deren Berechnung eine Weile dauern kann. Das macht sie praktisch, da das Herunterladen dieses Torrents oft schneller ist, als ihn von Grund auf neu zu berechnen. Insbesondere enthält sie SQLite-Indizes für die Tar-Dateien zur Verwendung mit <a %(a_href)s>ratarmount</a>. Veröffentlichung 2 Nachtrag (%(date)s) Wir haben alle Bücher erhalten, die zwischen unserem letzten Mirror und August 2022 zur Z-Library hinzugefügt wurden. Wir haben auch noch einige Bücher geholt, die wir beim ersten Mal übersehen haben. Insgesamt umfasst diese neue Sammlung etwa 24 TB. Auch diese Sammlung ist gegen Library Genesis dedupliziert, da für diese Sammlung bereits Torrents verfügbar sind. Die Daten sind ähnlich wie bei der ersten Veröffentlichung organisiert. Es gibt einen MySQL-“.sql.gz”-Dump der Metadaten, der auch alle Metadaten der ersten Veröffentlichung enthält und diese somit ersetzt. Wir haben auch einige neue Spalten hinzugefügt: Wir haben dies beim letzten Mal erwähnt, aber nur zur Klarstellung: „filename“ und „md5“ sind die tatsächlichen Eigenschaften der Datei, während „filename_reported“ und „md5_reported“ das ist, was wir von Z-Library gescrapt haben. Manchmal stimmen diese beiden nicht überein, daher haben wir beide aufgenommen. Für diese Veröffentlichung haben wir die Kollation auf „utf8mb4_unicode_ci“ geändert, die mit älteren Versionen von MySQL kompatibel sein sollte. Die Dateien sind ähnlich wie beim letzten Mal, obwohl sie viel größer sind. Wir konnten uns einfach nicht dazu durchringen, viele kleinere Torrent-Dateien zu erstellen. „pilimi-zlib2-0-14679999-extra.torrent“ enthält alle Dateien, die wir in der letzten Veröffentlichung verpasst haben, während die anderen Torrents alle neuen ID-Bereiche enthalten.  <strong>Update %(date)s:</strong> Wir haben die meisten unserer Torrents zu groß gemacht, was dazu führte, dass Torrent-Clients Schwierigkeiten hatten. Wir haben sie entfernt und neue Torrents veröffentlicht. <strong>Update %(date)s:</strong> Es waren immer noch zu viele Dateien, also haben wir sie in Tar-Dateien verpackt und erneut neue Torrents veröffentlicht. %(key)s: ob diese Datei bereits in Library Genesis enthalten ist, entweder in der Sachbuch- oder Belletristik-Sammlung (abgeglichen durch md5). %(key)s: in welchem Torrent sich diese Datei befindet. %(key)s: gesetzt, wenn wir das Buch nicht herunterladen konnten. Veröffentlichung 2 (%(date)s) Zlib-Veröffentlichungen (ursprüngliche Beschreibungsseiten) Tor-Domain Hauptwebsite Z-Library-Scrape Die „chinesische“ Sammlung in der Z-Library scheint dieselbe wie unsere DuXiu-Sammlung zu sein, jedoch mit unterschiedlichen MD5s. Wir schließen diese Dateien aus den Torrents aus, um Duplikate zu vermeiden, zeigen sie aber dennoch in unserem Suchindex an. Metadaten Du erhältst %(percentage)s%% schnelle Bonusdownloads, weil du von %(profile_link)s eingeladen wurdest. Dies gilt für den gesamten Zeitraum der Mitgliedschaft. Spenden Beitreten Selektiert bis zu %(percentage)s%% Ermäßigung Alipay unterstützt internationale Kredit-/Debitkarten. Weitere Informationen findest du in <a %(a_alipay)s>diesem Leitfaden</a>. Sende uns Amazon.com Geschenkkarten mit deiner Kredit-/Debitkarte. Du kannst Crypto mit Kredit-/Debitkarten kaufen. WeChat (Weixin Pay) unterstützt internationale Kredit-/Debitkarten. Gehe in der WeChat-App zu "Ich => Dienste => Wallet => Karte hinzufügen". Solltest du nichts finden, aktiviere es mit "Ich => Einstellungen => Allgemein => Tools => Weixin Pay => Aktivieren". (verwende das beim Senden von Ethereum von Coinbase) kopiert! kopieren (geringster Minimalbetrag) (achtung: hoher Mindestbetrag) -%(percentage)s%% 12 Monate 1 Monat 24 Monate 3 Monate 48 Monate 6 Monate 96 Monate Wähle, wie lange du Mitglied werden willst. <div %(div_monthly_cost)s></div><div %(div_after)s>nach <span %(span_discount)s></span> Ermäßigungen</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% für 12 Monate für 1 Monat für 24 Monate für 3 Monate für 48 Monate für 6 Monate für 96 Monate %(monthly_cost)s / Monat Kontaktiere uns Direktzugang <strong>SFTP</strong> Server Gewerbliche Spende oder im Austausch für neue Sammlungen (z. B. neue Scans, OCR-generierte Datensätze). Expertenzugang <strong>Unbeschränkter</strong> Highspeed-Zugang <div %(div_question)s>Kann ich meine Mitgliedschaft upgraden oder mehrere Mitgliedschaften erwerben?</div> <div %(div_question)s>Kann ich spenden, ohne Mitglied zu werden?</div> Na klar. Wir akzeptieren Spenden in beliebiger Höhe an diese Monero (XMR) Adresse: %(address)s. <div %(div_question)s>Was bedeuten die Kostenspannen, die pro Monat angegeben werden?</div> Du kannst den unteren Kostenbereich erreichen, indem du alle Rabatte anwendest, z. B. indem du einen Zeitraum wählst, der länger als ein Monat dauert. <div %(div_question)s>Wird die Mitgliedschaft automatisch erneuert?</div> Mitgliedschaften <strong>werden nicht</strong> automatisch erneuert. Du kannst für so lange beitreten wie du willst. <div %(div_question)s>Wofür werden die Spenden verwendet?</div> 100%% wird zur Bewahrung und Bereitstellung des Wissens und der Kultur der Menschheit verwendet. Momentan werden die Spenden hauptsächlich für Server, Speicher und Bandbreite verwendet. Es fließt kein Geld zu den Teammitgliedern persönlich. Das wäre ohnehin zu gefährlich. <div %(div_question)s>Kann ich große Beträge spenden?</div> Natürlich, und wir würden uns sehr darüber freuen! Für Spenden über ein paar Tausend Dollar, melde dich bitte direkt bei uns unter %(email)s. <div %(div_question)s>Habt ihr auch andere Zahlungsmethoden?</div> Momentan nicht. Viele Menschen wollen nicht, dass Archive wie dieses existieren, darum müssen wir sehr vorsichtig sein. Falls du uns helfen kannst andere (bequemere) Zahlungsmethoden sicher bereitzustellen, melde dich bitte bei uns unter %(email)s. Spenden FAQ Es existiert bereits eine <a %(a_donation)s>offene Spende</>. Bitte diese Spende abschließen, bevor du eine neue tätigst. <a %(a_all_donations)s>Alle meine Spenden ansehen</a> Für Spenden von mehr als 5000$ kontaktiere uns bitte direkt über %(email)s. Wir freuen uns über großzügige Spenden von wohlhabenden Einzelpersonen oder Institutionen.  Beachte, dass die Mitgliedschaften auf dieser Seite „pro Monat“ angeführt sind, es sich jedoch um einmalige Spenden (nicht wiederkehrend) handelt. Siehe dazu die <a %(faq)s>Spenden FAQ</a>. Annas Archiv ist ein gemeinnütziges Open-Source und Open-Data Projekt. Durch deine Spende wirst du ein Mitglied und unterstützt den Betrieb und die Entwicklung von Annas Archiv. An alle Mitglieder: Vielen Dank, dass ihr das Projekt am Laufen haltet! ❤️ Für weitere Informationen, siehe <a %(a_donate)s>Spenden FAQ</a>. Um Mitglied zu werden, <a %(a_login)s log dich bitte ein oder registriere dich</a>. Danke für deine Unterstützung! $%(cost)s / Monat Wenn du bei der Zahlung einen Fehler gemacht hast, können wir keine Rückerstattungen vornehmen, aber wir werden versuchen, es zu korrigieren. Finde die „Crypto“ Seite in deiner PayPal App oder Webseite. Diese ist normalerwise unter „Finanzen“ zu finden. Gehe zur „Bitcoin“ Seite in deiner PayPal App oder Webseite. Klicke den "Überweisen" Button %(transfer_icon)s, und dann „Senden“. Alipay Alipay 支付宝 / WeChat 微信 Amazon Gutschein %(amazon)s Geschenkkarte Bankkarte Bankkarte (mit App) Binance Kredit-/Debitkarte/Apple/Google (BMC) Cash App Kredit/Debitkarte Kredit/Debitkarte 2 Kredit-/Debitkarte (Backup) Kryptowährungen %(bitcoin_icon)s Karte / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (regulär) Pix (Brazil) Revolut (temporär nicht verfügbar) WeChat Wähle deine Kryptowährung aus: Spende mit einem Amazon Gutschein. <strong>WICHTIG:</strong> Diese Option ist für %(amazon)s. Wenn du eine andere Amazon-Website verwenden möchtest, wählen diese oben aus. <strong>WICHTIG:</strong> Es funktioniert ausschließlich mit Amazon.com, nicht mit anderen Amazon Websites, z.B .de, .co.uk, oder .ca. Bitte schreibe NICHT deine eigene Nachricht. Gib den genauen Betrag ein: %(amount)s Beachte, dass wir auf Beträge aufrunden müssen, die unsere Reseller akzeptieren ((Minimum %(minimum)s). Spenden mit einer Kredit-/Debitkarte über die Alipay-App (super einfach einzurichten). Installiere die Alipay-App aus dem <a %(a_app_store)s>Apple App Store</a> oder dem <a %(a_play_store)s>Google Play Store</a>. Registriere dich mit deiner Telefonnummer. Weitere persönliche Daten sind nicht erforderlich. <span %(style)s>1</span>Alipay-App installieren Unterstützt: Visa, MasterCard, JCB, Diners Club und Discover. Siehe <a %(a_alipay)s>diesen Leitfaden</a> für weitere Informationen. <span %(style)s>2</span>Bankkarte hinzufügen Bei Binance kaufst du Bitcoin mit einer Kredit-/Debitkarte oder einem Bankkonto und spendest diese Bitcoin dann an uns. Auf diese Weise können wir bei der Annahme deiner Spende sicher und anonym bleiben. Binance ist in fast jedem Land verfügbar und unterstützt die meisten Banken und Kredit-/Debitkarten. Dies ist derzeit unsere Empfehlung. Wir freuen uns, dass du dir die Zeit nimmst, zu lernen mit dieser Methode zu spenden, da sie uns sehr hilft. Bei Kreditkarten, Debitkarten, Apple Pay und Google Pay verwenden wir "Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). In deren System entspricht ein "Kaffee" 5 US-Dollar, sodass deine Spende auf das nächste Vielfache von fünf gerundet wird. Spende mit Cash App. Falls du die Cash App hast, wäre das der einfachste Weg um zu spenden! Für Transaktionen unter %(amount)s, nimmt Cash App eine %(fee)s Gebühr. Alles über %(amount)s ist kostenlos! Spende mit Kredit oder Debitkarte. Bei dieser Methode wird ein Kryptowährungsanbieter als Zwischenkonvertierung verwendet. Dies kann etwas verwirrend sein, daher verwende diese Methode bitte nur, wenn andere Zahlungsmethoden nicht funktionieren. Es funktioniert auch nicht in allen Ländern. Wir können Kredit-/Debitkarten nicht direkt unterstützen, da Banken nicht mit uns zusammenarbeiten wollen. ☹ Es gibt jedoch mehrere Möglichkeiten, Kredit-/Debitkarten trotzdem zu verwenden, indem du andere Zahlungsmethoden nutzt: Spenden in Kryptowährungen kannst du mit BTC, ETH, XMR, und SOL machen. Verwende diese Option wenn du dich mit Kryptowährungen auskennst. Mit Kryptowährung kannst du mit BTC, ETH, XMR, und anderen bezahlen. Krypto-Expressdienste Wenn du zum ersten Mal Krypto verwendest, empfehlen wir dir, %(options)s zu verwenden, um Bitcoin (die ursprüngliche und am häufigsten verwendete Kryptowährung) zu kaufen und zu spenden. Für kleinere Beträge eliminieren die Kreditkartengebühren eventuell unseren %(discount)s%% Rabatt, deshalb schlagen wir längere Abos vor. Spende mit Kredit-/Debitkarte, PayPal oder Venmo. Zwischen diesen Optionen kannst du auf der nächsten Seite wählen. Google Pay oder Apple Pay können auch funktionieren. Bei kleineren Spenden sind die Gebühren hoch, also empfehlen wir längere Abos. Für PayPal-Spenden verwenden wir PayPal Crypto, was uns erlaubt anonym zu bleiben. Wir sind sehr dankbar, dass du dir die Zeit nimmst auf diese Weise zu spenden, da uns das eine grosse Hilfe ist. Spende mit PayPal. Spenden mit einem regulären PayPal-Konto. Spenden mit Revolut. Wenn du Revolut hast, ist dies der einfachste Weg zu spenden! Diese Bezahlmethode lässt einen Spendenbetrag von höchstens%(amount)s zu. Bitte wähle eine andere Spendenmethode oder einen niedrigeren Betrag aus. Diese Bezahlmethode benötigt einen Spendenbetrag von mindestens %(amount)s. Bitte wähle eine andere Spendenmethode oder einen höheren Betrag aus. Binance Coinbase Kraken Bitte wähle eine Zahlungsmethode. “Adoptiere ein Torrent”: Dein Benutzername oder eine Nachricht deiner Wahl im Dateinamen <div %(div_months)s>einmal pro 12 Monate Mitgliedschaft</div> Dein Benutzername oder anonyme Nennung in den Credits Frühzeitiger Zugang zu neuen Features Exklusiver Telegram-Zugriff mit hinter den Kulissen Updates %(number)s schnelle Downloads pro Tag wenn du diesen Monat spendest! <a %(a_api)s>JSON API-Zugang</a> Legendenstatus für die Bewahrung des Wissens und der Kultur der Menschheit Vorherige Mitgliedervorteile, plus: Erhalte <strong>%(percentage)s%% Bonusdownloads</strong> wenn du <a %(a_refer)s>Freunde einlädst</a>. SciDB-Aufsätze <strong>unbegrenzt</strong> ohne Überprüfung Wenn du Fragen zum Konto oder zur Spende stellst, füge deine Konto-ID, Screenshots, Belege und so viele Informationen wie möglich hinzu. Wir überprüfen unsere E-Mails nur alle 1-2 Wochen, daher kann die Nichtangabe dieser Informationen eine Lösung verzögern. Um noch mehr Downloads zu bekommen, <a %(a_refer)s>lade Freunde ein</a>! Wir sind ein kleines Team an Freiwilligen. Deshalb könnte es durchaus 1-2 Wochen dauern, bis wir antworten. Beachte, dass der Accountname oder das Bild etwas eigenartig aussehen kann! Kein Grund zur Sorge! Diese Accounts werden von unseren Spenden-Partnern betreut. Unsere Accounts wurden nicht gehackt. Spenden <span %(span_cost)s></span> <span %(span_label)s></span> für 12 Monate “%(tier_name)s” für 1 Monat “%(tier_name)s” für 24 Monate “%(tier_name)s” für 3 Monate “%(tier_name)s” für 48 Monate“%(tier_name)s” für 6 Monate “%(tier_name)s” für 96 Monate “%(tier_name)s” Du kannst die Spende an der Kasse immer noch abbrechen. Klicke die Spenden Schaltfläche um die Spende zu bestätigen. <strong>Wichtiger Hinweis:</strong> Der Preis von Kryptowährungen kann stark fluktuieren, manchmal bis zu 20%% in wenigen Minuten. Das ist immer noch weniger als die Gebühren von 50-60%%, welche viele Zahlungsanbieter für die Zusammenarbeit mit "Schatten Wohltätigkeitsorganisationen" wie uns verlangen. <u>Wenn du uns den Beleg mit dem originalen Preis den du bezahlt hast schickst, werden wir dir die gewählte Mitgliedschaft freischalten (so lange der Beleg nicht älter als ein paar Stunden ist)</u>. Wir sind sehr dankbar, dass du solche Unannehmlichkeiten auf dich nimmst, um uns zu unterstützen! ❤️ ❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut. <span %(span_circle)s>1</span>Kaufe Bitcoin mit Paypal <span %(span_circle)s>2</span>Überweise die Bitcoins an unsere Adresse ✅ Weiterleitung zur Spende-Seite… Spenden Bitte warte mindestens <span %(span_hours)s>24 Stunden</span> (und aktualisiere diese Seite), bevor du uns kontaktierst. Wenn du eine Spende (in beliebiger Höhe) ohne Mitgliedschaft tätigen möchtest, kannst du die folgende Monero (XMR)-Adresse verwenden: %(address)s. Ein paar Minuten nach abschicken des Gutscheincodes wirst du eine automatisierte Bestätigung erhalten. Wenn es nicht funktioniert, schicke deinen Code nochmal (<a %(a_instr)s>Anweisungen</a>). Wenn das auch nicht funktioniert, schicke uns (*nachdem* du es ein zweites Mal versucht hast) eine E-Mail und Anna wird sich manuell darum kümmern. Dies kann ein paar Tage dauern. Beispiel: Bitte nutze das <a %(a_form)s>offizielle Amazon.com Formular</a> um uns einen Gutschein über %(amount)s an die E-Mail-Adresse unten zu schicken. "An" Empfänger-E-Mail im Formular: Amazon Gutschein Wir können keine anderen Gutscheine annehmen, <strong>bitte verwende auschließlich das offizielle Formular auf Amazon.com</strong>. Wenn du dieses Formular nicht verwendest, können wir deine Geschenkkarte nicht zurückgeben. Nur einmal verwenden. Spezifisch zu deinem Account, gib es an niemandem weiter. Warte auf Gutschein (zum Prüfen aktualisiere die Seite) Öffne die <a %(a_href)s>QR-Code-Spendenseite</a>. Scanne den QR-Code mit der Alipay-App oder drücke den Knopf, um die Alipay-App zu öffnen. Bitte habe etwas Geduld, da die Seite etwas Zeit zum Laden benötigen könnte, da sie sich in China befindet. <span %(style)s>3</span>Spende tätigen (QR-Code scannen oder Knopf drücken) Kaufe PYUSD coins auf PayPal Bitcoin (BTC) auf Cash App kaufen Kaufe etwas mehr (wir empfehlen %(more)s mehr) als den Betrag, den du spenden willst (%(amount)s), um Transaktionsgebühren zu decken. Du behältst den Restbetrag. Gehen zur „Bitcoin“ (BTC) Seite in der Cash App. Überweise den Bitcoin an unsere Adresse Für kleine Spenden (unter $25) musst du möglicherweise Rush oder Priority verwenden. Klicke auf die Schaltfläche „Bitcoin senden“, um eine „Abhebung“ vorzunehmen. Wechsel dann von Dollar zu BTC, indem du auf das %(icon)s Symbol drückst. Gib den BTC-Betrag unten ein und klicke auf „Senden“. Sie dir <a %(help_video)s>dieses Video</a> an, wenn du nicht weiterkommst. Expressdienste sind praktisch, erheben jedoch höhere Gebühren. Du kannst dies anstelle einer Krypto-Börse verwenden, wenn du schnell eine größere Spende tätigen möchtest und eine Gebühr von $5-10 dich nicht stört. Achte darauf, den genauen Krypto-Betrag zu senden, der auf der Spendenseite angezeigt wird, nicht den Betrag in $USD. Andernfalls wird die Gebühr abgezogen und wir können deine Mitgliedschaft nicht automatisch verarbeiten. Manchmal kann die Bestätigung bis zu 24 Stunden dauern, also stelle sicher, dass du diese Seite aktualisierst (auch wenn sie abgelaufen ist). Kredit/Debitkarten Anleitung Spende über unsere Kredit/Debitkartenseite In einigen der Punkte werden Krypto-Wallets erwähnt, aber keine Sorge, du musst dafür nichts über Krypto lernen. %(coin_name)s Anleitung Scannen Sie diesen QR -Code mit Ihrer Crypto -Wallet -App, um die Zahlungsdetails schnell einzugeben Scannen Sie den QR -Code, um sie zu bezahlen Wir unterstützen nur die Standardversion von Kryptocoins, keine exotischen Netzwerke oder sonstige exotische Versionen von Coins. Je nach Coin kann es bis zu einer Stunde dauern, bis die Transaktion bestätigt ist. Spende %(amount)s auf <a %(a_page)s>dieser Seite</a>. Diese Spende ist abgelaufen. Bitte storniere und erstelle eine neue. Wenn du bereits bezahlt hast: Ja, ich habe den Beleg per E-Mail verschickt Falls der Wechselkurs der Kryptowährung während der Übertragung fluktuiert bitte den Beleg mit dem relevanten Wechselkurs mitschicken. Wir sind dir sehr dankbar, dass du dir die Mühe machst, mit Kryptowährungen zu spenden, es hilft uns sehr! ❌ Etwas ist schief gelaufen. Bitte aktualisiere die Webseite und versuche es erneut. <span %(span_circle)s>%(circle_number)s</span>Sende uns den Beleg per E-Mail Wenn du auf Probleme stoßen solltest, kontaktiere uns bitte per %(email)s und gib so viele Informationen wie möglich an (z. B. Screenshots). ✅ Danke für deine Spende! Anna wird deine Mitgliedschaft in den nächsten Tagen manuell freischalten. Sende einen Beleg oder Screenshot an deine persönliche Verifikationsadresse: Wenn du den Beleg per E-Mail verschickt hast, klicke bitte diesen Button, damit Anna den Beleg manuell überprüfen kann (dies kann einige Tage dauern): Sende eine Quittung oder einen Screenshot an deine persönliche Verifizierungsadresse. Verwende diese E-Mail-Adresse NICHT für deine PayPal-Spende. Abbrechen Ja, bitte abbrechen Bist Du sicher dass du abbrechen willst? Nicht abbrechen, falls du schon bezahlt hast. ❌ Es ist etwas schief gelaufen. Bitte aktualisiere die Seite und versuche es erneut. Mache eine neue Spende ✅ Deine Spende wurde abgebrochen. Datum: %(date)s Identifizierung: %(id)s Erneut bestellen Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Monat für %(duration)s Monate, inklusive %(discounts)s%% Ermäßigung)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Monat für %(duration)s Monate)</span> 1. Gib deine E-Mail an. 2. Wähle deine Zahlungsmethode. 3. Wähle deine Zahlungsmethode erneut. 4. Wähle das selbstgehostete (“Self-hosted” ) Wallet. 5. Klicke auf "Ich bestätige den Besitz" (“I confirm ownership”). 6. Du solltest einen Beleg per E-Mail erhalten haben. Bitte sende ihn an uns und wir werden deine Spende so schnell wie möglich bestätigen. (du solltest vielleicht stornieren und eine neue Spende erstellen) Die Bezahlungsdaten sind veraltet. Falls du eine weitere Spende machen willst, verwende den "Erneut Spenden" Button oben. Du hast schon bezahlt. Falls du die Anleitung zur Bezahlung trotzdem anzeigen willst, klicke hier: Zeige die alte Bezahlanleitung an Wenn die Spendenseite blockiert wird, versuchen es mit einer anderen Internetverbindung (z. B. VPN oder Handy-Internet). Leider ist die Alipay-Seite oft nur vom <strong>chinesischen Festland aus zugänglich</strong>. Möglicherweise musst du dein VPN vorübergehend deaktivieren oder ein VPN für das chinesische Festland verwenden (Hongkong funktioniert manchmal). <span %(span_circle)s>1</span>Spende mit Alipay Spende den Gesamtbetrag von %(total)s über <a %(a_account)s>dieses Alipay-Konto</a> Anleitung Alipay <span %(span_circle)s>1</span>Überweise zu einem unserer Krypto-Konten Spende den kompletten Betrag von %(total)s auf eine dieser Adressen: Krypto Anleitung Folge der Anleitung, um Bitcoin (BTC) zu kaufen. Du brauchst nur den Wert den du spenden möchtest zu kaufen, %(total)s. Gebe unsere Bitcoin (BTC) Adresse als Empfänger an und folge der Anleitung, um uns die Spende von %(total)s zu senden: <span %(span_circle)s>1</span>Spende mit Pix Spende den kompletten Betrag von %(total)s auf <a %(a_account)s>dieses Pix Konto Pix Anleitung <span %(span_circle)s>1</span>Spende mit WeChat Spende den Gesamtbetrag von %(total)s mit<a %(a_account)s>diesem WeChat-Konto</a> Anleitung WeChat Verwende einen der folgenden „Kreditkarte zu Bitcoin“ Express-Dienste, die nur wenige Minuten dauern: BTC / Bitcoin-Adresse (externes Wallet): BTC / Bitcoin-Betrag: Fülle die folgenden Angaben im Formular aus: Wenn eine dieser Informationen veraltet ist, sende uns bitte eine E-Mail, um uns dies mitzuteilen. Bitte verwende diesen <span %(underline)s>genauen Betrag</span>. Deine abschließenden Gesamtkosten könnten aufgrund von Kreditkartengebühren höher sein. Bei kleinen Beträgen kann dies leider mehr als unser Rabatt sein. (Mindestbetrag: %(minimum)s, keine Verifizierung für die erste Transaktion) (Mindestbetrag: %(minimum)s) (Mindestbetrag: %(minimum)s) (Mindestbetrag: %(minimum)s, keine Verifizierung für die erste Transaktion) (Mindestbetrag: %(minimum)s) (Mindestbetrag: %(minimum)s je nach Land, keine Verifizierung für die erste Transaktion) Folge den Anweisungen um PYUSD coins zu kaufen (PayPal USD). Kaufe ein bisschen mehr (wir empfehlen %(more)s mehr) als den Spendenbetrag (%(amount)s), um Transaktionskosten zu decken. Du behälst alles was übrig bleibt. Gehe auf die „PYUSD“ Seite in deiner PayPal App oder Webseite. Drücke auf den “Transfer” Knopf %(icon)s, und dann „Senden“. Update Status Um den Timer zu resetten, erstelle eine neue Spende. Stelle sicher, dass du den BTC-Betrag unten verwendest, <em>NICHT</em> Euro oder Dollar, da wir sonst den korrekten Betrag nicht erhalten und deine Mitgliedschaft nicht automatisch bestätigen können. Bitcoin (BTC) auf Revolut kaufen Kaufen etwas mehr (wir empfehlen %(more)s mehr) als den Betrag, den du spenden willst (%(amount)s), um Transaktionsgebühren zu decken. Du behältst den Restbetrag. Gehe zur „Crypto“ Seite in Revolut, um Bitcoin (BTC) zu kaufen. Überweise den Bitcoin an unsere Adresse Für kleine Spenden (unter $25) musst du möglicherweise Rush oder Priority verwenden. Klicke auf die Schaltfläche „Bitcoin senden“, um eine „Abhebung“ vorzunehmen. Wechsel von Euro zu BTC, indem du auf das %(icon)s Symbol drückst. Gib den BTC-Betrag unten ein und klicke auf „Senden“. Sieh dir <a %(help_video)s>dieses Video</a> an, wenn du nicht weiterkommst. Status: 1 2 Schritt-für-Schritt-Anleitung Siehe die Anleitung unten. Andernfalls kann es sein, dass du aus deinem Konto ausgesperrt wirst! Falls du es noch nicht getan hast, notiere dir deinen Geheimcode für die Anmeldung: Danke für deine Spende! Restliche Zeit: Spende Sende %(amount)s zu %(account)s Warte auf Bestätigung (zum Prüfen aktualisiere die Seite )… Warte auf Transfer (zum Prüfen aktualisiere die Seite )… Früher Schnelle Downloads in den letzten 24 Stunden werden auf das Tageslimit angerechnet. Downloads von schnellen Partnerservern sind mit %(icon)s gekennzeichnet. Letzte 18 Stunden Es wurden keine Dateien heruntergeladen. Heruntergeladene Dateien werden nicht öffentlich angezeigt. Alle Zeitangaben sind in UTC. Heruntergeladene Dateien Wenn du eine Datei sowohl als schnellen als auch als langsamen Downloads heruntergeladen hast, wird sie doppelt angezeigt. Mach dir keine Sorgen, es gibt viele Leute, die von derartigen Webseiten Daten herunterladen und ist es äußerst selten, dass man in Schwierigkeiten gerät. Um jedoch sicher zu gehen, empfehlen wir die Verwendung eines VPN (kostenpflichtig) oder <a %(a_tor)s>Tor</a> (kostenlos). Ich habe 1984 von George Orwell heruntergeladen, wird die Polizei nun vor meiner Tür stehen? Ihr alle seid Anna! Wer ist Anna? Wir haben eine stabile JSON-API für Mitglieder, um eine schnelle Download-URL zu erhalten: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (Dokumentation innerhalb der JSON selbst). Für andere Anwendungsfälle, wie z. B. das Durchlaufen all unserer Dateien, das Erstellen einer benutzerdefinierten Suche usw., empfehlen wir unsere ElasticSearch- und MariaDB-Datenbanken neu zu <a %(a_generate)s>generieren</a> oder <a %(a_download)s>runterzuladen</a>. Die Rohdaten können manuell als <a %(a_explore)s>JSON-Dateien durchsucht werden</a>. Unsere Rohdaten Torrent-Liste kann auch als <a %(a_torrents)s>JSON</a> heruntergeladen werden. Habt ihr eine API? Wir hosten hier keine urheberrechtlich geschützten Materialien. Wir sind eine Suchmaschine und indizieren als solche nur Metadaten, die bereits öffentlich zugänglich sind. Wenn du von diesen externen Quellen Materialien herunterlädst, empfehlen wir dir, die Gesetze in deiner Gerichtsbarkeit in Bezug auf das Erlaubte zu überprüfen. Wir sind nicht verantwortlich für Inhalte, die von anderen gehostet werden. Wenn du Beschwerden über etwas hast, was du hier siehst, dann wende dich am besten an die ursprüngliche Webseite. Wir ziehen ihre Änderungen regelmäßig in unsere Datenbank. Wenn du wirklich der Meinung bist, dass du eine gültige DMCA-Beschwerde verfügst, auf die wir antworten sollten, fülle bitte das <a %(a_copyright)s>DMCA- / Urherberrechtsanspruchs-Formular</a> aus. Wir nehmen deine Beschwerden ernst und werden uns so schnell wie möglich bei dir melden. Wie melde ich Urheberrechtsverletzungen? Hier sind einige Bücher, die für die Welt der Schattenbibliotheken und der digitalen Langzeitarchivierung eine besondere Bedeutung spielen: Was sind eure Lieblingsbücher? Wir möchten alle daran erinnern, dass unser gesamter Code und unsere Daten vollständig Open Source sind. Das ist einzigartig für Projekte wie unseres – uns ist kein anderes Projekt mit einem ähnlich umfangreichen Katalog bekannt, das ebenfalls vollständig quelloffen ist. Wir würden es begrüßen, wenn jeder, der der Meinung ist, dass wir unser Projekt schlecht betreiben, unseren Code und unsere Daten nutzt, um seine eigene Schattenbibliothek einzurichten! Wir sagen das nicht aus Trotz oder so – wir glauben wirklich, dass es großartig wäre, da es die Messlatte für alle höher legen und das Vermächtnis der Menschheit besser bewahren würde. Ich finde es nicht gut, wie ihr das Projekt betreibt! Wir würden die Erstellung von <a %(a_mirrors)s>Mirrorservern</a> sehr begrüßen und würden dies auch finanziell unterstützen. Wie kann ich helfen? Wir jedenfalls schon. Unsere Inspiration für das Sammeln von Metadaten ist Aaron Swartz' Ziel, „eine Website für jedes jemals veröffentlichte Buch“ zu erstellen, wofür er die <a %(a_openlib)s>Open Library</a> ins Leben rief. Dieses Projekt hat sich gut entwickelt. Unsere einzigartige Position ermöglicht es uns jedoch, Metadaten zu erhalten, die der Open Library nicht zur Verfügung stehen. Eine weitere Inspiration war unser Wunsch zu wissen, <a %(a_blog)s>wie viele Bücher es auf der Welt gibt</a>, damit wir berechnen können, wie viele Bücher wir noch retten müssen. Sammelt ihr Metadaten? Beachten, dass mhut.org bestimmte IP-Bereiche blockiert, sodass möglicherweise ein VPN erforderlich ist. <strong>Android:</strong> Klicke auf das Drei-Punkte-Menü oben rechts und wähle „Zum Startbildschirm hinzufügen“. <strong>iOS:</strong> Klicke unten auf die Schaltfläche „Teilen“ und wähle „Zum Startbildschirm hinzufügen“. Wir haben keine offizielle mobile App, aber du kannst diese Website als App installieren. Habt ihr eine mobile App? Bitte sende sie an das <a %(a_archive)s>Internet Archive</a>. Sie werden dort ordnungsgemäß aufbewahrt. Wie spende ich Bücher oder andere physische Materialien? Wie kann ich Bücher anfragen? <a %(a_blog)s>Annas Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmäßige Neuigkeiten <a %(a_software)s>Annas Software</a> — unser Open-Source-Code <a %(a_datasets)s>Datensätze</a> — Näheres zu unseren Daten <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — Alternativ-Domains Gibt es noch andere Ressourcen über und von Annas Archiv? <a %(a_translate)s>Translate auf Annas Software</a> — unser Übersetzungssystem <a %(a_wikipedia)s>Wikipedia</a> — mehr über uns (hilf bitte dabei, den Wikipediaartikel aktuell zu halten oder einen neuen in deiner Sprache zu erstellen!) Wähle die gewünschten Einstellungen aus, lass das Suchfeld leer, klicke auf „Suchen“ und setze dann ein Lesezeichen für die Seite mit der Lesezeichenfunktion deines Browsers. Wie speichere ich meine Sucheinstellungen? Wir begrüßen Sicherheitsforscher, die nach Schwachstellen in unseren Systemen suchen. Wir sind große Befürworter einer verantwortungsvollen Offenlegung. Kontaktiere uns bitte <a %(a_contact)s>hier</a>. Wir sind derzeit nicht in der Lage, Bug Bounties zu vergeben. Einzige Ausnahme sind Schwachstellen, die das <a %(a_link)s >Potenzial haben, unsere Anonymität zu gefährden</a>, für die wir Prämien im Bereich von 10.000 bis 50.000 US-Dollar anbieten. Wir möchten in Zukunft jedenfalls mehr Spielraum für Bug Bounties anbieten! Bitte beachte, dass Social-Engineering-Angriffe nicht in den Geltungsbereich fallen. Wenn du dich für offensive Sicherheit interessierst und dabei helfen möchtest, das Wissen und die Kultur der Welt zu archivieren, kontaktiere uns bitte. Es gibt viele Möglichkeiten, wie du helfen kannst. Habt ihr ein Programm zur verantwortungsvollen Offenlegung? Wir haben buchstäblich nicht genug Ressourcen, um jedem auf der Welt Highspeed-Downloads zu ermöglichen, so gerne wir auch würden. Wenn ein reicher Wohltäter sich engagieren und uns dies zur Verfügung stellen möchte, wäre das unglaublich, aber bis dahin versuchen wir unser Bestes. Wir sind ein gemeinnütziges Projekt, das sich mit Spenden kaum selbst versorgen kann. Aus diesem Grund haben wir mit unseren Partnern zwei Systeme für kostenlose Downloads eingeführt: gemeinsam genutzte Server mit langsamen Downloads und etwas schnellere Server mit einer Warteliste (um die Anzahl der gleichzeitig herunterladenden Personen zu reduzieren). Wir haben auch eine <a %(a_verification)s>Browser-Verifizierung</a> für unsere langsamen Downloads, da sie sonst von Bots und Scrapern missbraucht werden, was es für legitime Benutzer noch langsamer machen würde. Beachte, dass beim Verwenden des Tor-Browsers möglicherweise deine Sicherheitseinstellungen angepasst werden müssen. Bei der niedrigsten Option, genannt „Standard“, gelingt die Cloudflare-Turnstile-Herausforderung. Bei den höheren Optionen, genannt „Sicherer“ und „Am sichersten“, schlägt die Herausforderung fehl. Für große Dateien können langsame Downloads manchmal mitten im Prozess abbrechen. Wir empfehlen die Verwendung eines Download-Managers (wie JDownloader), um große Downloads automatisch fortzusetzen. Warum sind die langsamen Downloads so langsam? Häufig gestellte Fragen (FAQ) Verwende den <a %(a_list)s>Torrent-Listengenerator</a>, um im Rahmen deiner Speicherkapazitäten eine Liste von Torrents zu erstellen, die am dringendsten ein Torrenting benötigen. Ja, siehe dazu die <a %(a_llm)s>LLM-Daten</a> Seite. Die meisten Torrents enthalten die Dateien direkt, was bedeutet, dass du die Torrent-Clients dazu anweisen kannst, nur die erforderlichen Daten herunterzuladen. Um festzulegen, welche Daten heruntergeladen werden sollen, kannst du unsere Metadaten neu <a %(a_generate)s>generieren</a> oder unsere ElasticSearch- und MariaDB-Datenbanken <a%(a_download)s>herunterladen</a>. Leider enthalten einige Torrent-Sammlungen .zip oder .tar Dateien im Stammverzeichnis, in diesem Fall musst du den gesamten Torrent herunterladen, bevor du einzelne Daten auswählen kannst. Es sind noch keine einfach zu bedienenden Werkzeuge zum Filtern von Torrents verfügbar, aber wir freuen uns über Beiträge. (Wir haben allerdings <a %(a_ideas)s>einige Ideen</a> für den letzteren Fall.) Lange Antwort: Kurze Antwort: nicht einfach. Wir versuchen, minimale Duplizierungen oder Überschneidungen zwischen den Torrents in dieser Liste zu vermeiden, aber dies kann nicht immer erreicht werden und hängt stark von den Richtlinien der Quellbibliotheken ab. Für Bibliotheken, die ihre eigenen Torrents veröffentlichen, liegt es nicht in unserer Hand. Bei Torrents, die von Annas Archiv veröffentlicht wurden, deduplizieren wir nur auf der Grundlage des MD5-Hashes, was bedeutet, dass verschiedene Versionen desselben Buches nicht dedupliziert werden. Ja. Dies sind eigentlich PDFs und EPUBs, sie haben nur keine Erweiterung in vielen unserer Torrents. Es gibt zwei Stellen, an denen du die Metadaten für Torrent-Dateien finden kannst, einschließlich der Dateitypen/-erweiterungen: 1. Jede Sammlung oder Veröffentlichung hat ihre eigenen Metadaten. Zum Beispiel haben <a %(a_libgen_nonfic)s>Libgen.rs Torrents</a> eine entsprechende Metadatendatenbank, die auf der Libgen.rs-Website gehostet wird. In der Regel verlinken wir auf relevante Metadatenressourcen von der <a %(a_datasets)s>Datensatz-Seite</a> jeder Sammlung. 2. Wir empfehlen unsere ElasticSearch- und MariaDB-Datenbanken neu zu <a %(a_generate)s>generieren</a> oder <a %(a_download)s>runterzuladen</a>. Diese enthalten eine Zuordnung für jeden Datensatz in Annas Archiv zu den entsprechenden Torrent-Dateien (falls verfügbar) unter "torrent_paths" im ElasticSearch-JSON. Einige Torrent-Clients unterstützen keine großen Stückgrößen, die viele unserer Torrents haben (bei neueren machen wir das nicht mehr – obwohl es laut Spezifikationen gültig ist!). Versuche es mit einem anderen Client, wenn du auf dieses Problem stößt, oder beschwere dich bei den Entwicklern deines Torrent-Clients. Ich würde gerne beim Seeden helfen, habe aber nicht viel Speicherplatz. Die Torrents sind zu langsam; Kann ich die Daten direkt bei euch herunterladen? Kann ich nur eine Teilmenge der Daten herunterladen, z. B. nur eine bestimmte Sprache oder ein bestimmtes Thema? Wie geht ihr mit Duplikaten in den Torrents um? Kann ich die Torrent-Liste als JSON erhalten? Ich sehe keine PDFs oder EPUBs in den Torrents, sondern nur Binärdateien? Was soll ich tun? Warum kann mein Torrent-Client einige eurer Torrent-Dateien / Magnet-Links nicht öffnen? Torrents FAQ Wie kann ich neue Bücher hochladen? Bitte sieh dir dazu <a %(a_href)s>dieses ausgezeichnete Projekt</a> an. Habt ihr einen Uptime-Monitor? Was ist Annas Archiv? Werde eine Mitglied um schnelle Downloads zu verwenden. Wir unterstützen jetzt Amazon-Geschenkkarten, Kredit- und Debitkarten, Krypto, Alipay und WeChat. Schnelle Downloads sind für heute aufgebraucht. Bereitstellung Stündliche Downloads in den letzten 30 Tagen. Stündlicher Durchschnitt: %(hourly)s. Tagesdurchschnitt: %(daily)s. Wir arbeiten mit Partnern zusammen, um unsere Sammlung für jedermann einfach und kostenlos zugänglich zu machen. Wir sind der Überzeugung, dass jeder das Recht hat, auf das Wissen der Menschheit zuzugreifen. <a %(a_search)s>Und das sollte nicht auf Kosten der Autoren gehen</a>. Die in Annas Archiv verwendeten Datensätze sind völlig frei zugänglich und können mithilfe von Torrents in großen Mengen gespiegelt werden. <a %(a_datasets)s>Mehr Erfahren…</a> Langzeitarchiv Vollständige Datenbank Suchen Bücher, wissenschaftliche Aufsätze, Zeitschriften, Comics, Bibliotheksdatensätze, Metadaten, … Der gesamte <a %(a_code)s>Code</a> und alle <a %(a_datasets)s>Daten</a> sind quelloffen. <span %(span_anna)s>Annas Archiv</span> ist ein gemeinnütziges Projekt mit den folgenden zwei Zielen: <li><strong>Bewahrung:</strong> Sicherung des Wissens und der Kultur der Menschheit.</li><li><strong>Bereitstellung:</strong> Dieses Wissen und Kultur allen Menschen auf der Welt zugänglich machen.</li> Wir verfügen über die weltweit größte Sammlung hochwertiger Textdaten. <a %(a_llm)s>Mehr Erfahren…</a> LLM-Trainingsdaten 🪩 Mirrors: Freiwillige gesucht Wenn du eine anonyme Hochrisiko-Zahlungsabwicklung betreibst, kontaktiere uns bitte. Wir suchen auch nach Leuten, die geschmackvolle kleine Anzeigen platzieren möchten. Alle Einnahmen dienen unseren Bemühungen zur weiteren Bewahrung. Bewahrung Wir schätzen, dass wir den Fortbestand von ungefähr <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% der Bücher auf der Welt</a> sichern. Wir bewahren Bücher, wissenschaftliche Aufsätze, Comics, Zeitschriften und vieles mehr, indem wir diese Materialien aus verschiedenen <a href="https://de.wikipedia.org/wiki/Schattenbibliothek">Schattenbibliotheken</a>, offiziellen Bibliotheken und anderen Sammlungen an einem Ort zusammenführen. All diese Daten werden für immer erhalten bleiben, weil wir es einfach machen, sie in großen Mengen zu vervielfältigen: nämlich mit Hilfe von Torrents, was zu vielen Kopien auf der ganzen Welt führt. Einige Schattenbibliotheken tun dies bereits selbst (z. B. Sci-Hub, Library Genesis), während Annas Archiv andere Bibliotheken "befreit", die keine Massenverteilung anbieten (z. B. Z-Library) oder gar keine Schattenbibliotheken sind (z. B. Internet Archive, DuXiu). Diese weitläufige Verbreitung in Kombination mit Open-Source-Code macht unsere Website unempfindlich gegen Takedowns und gewährleistet die langfristige Bewahrung des Wissens und der Kultur der Menschheit. Erfahre mehr über <a href="/datasets">unsere Datensätze</a>. Wenn du ein <a %(a_member)s>Mitglied</a> bist, ist keine Browserüberprüfung erforderlich. 🧬&nbsp;SciDB ist die Fortführung von Sci-Hub. SciDB Anzeigen DOI Sci-Hub hat das Hochladen neuer Aufsätze <a %(a_paused)s>pausiert</a>. Direkter Zugriff auf %(count)s wissenschaftliche Aufsätze 🧬&nbsp;SciDB ist eine Fortsetzung von Sci-Hub mit seiner vertrauten Benutzeroberfläche und der direkten Anzeige von PDFs. Gib einen DOI ein, um Aufsätze anzuzeigen. Wir haben die gesamte Sci-Hub-Kollektion sowie neue Aufsätze. Die meisten können direkt mit einer vertrauten Benutzeroberfläche angezeigt werden, ähnlich wie bei Sci-Hub. Einige können über externe Quellen heruntergeladen werden, in diesem Fall zeigen wir Links zu diesen. Du wärst uns eine große Unterstützung, wenn du dabei hilfst Torrents zu seeden. <a %(a_torrents)s>Mehr Erfahren…</a> >%(count)s Seeders <%(count)s Seeders %(count_min)s–%(count_max)s Seeders 🤝 Auf der Suche nach Freiwilligen Als gemeinnütziges Open-Source-Projekt sind wir immer auf der Suche nach Menschen, die uns helfen. IPFS Downloads Liste von %(by)s, erstellt <span %(span_time)s>%(time)s</span> Speichern ❌ Es ist etwas schief gelaufen. Bitte erneut versuchen. ✅ Gespeichert. Bitte Seite neu laden. Liste ist leer. Editieren Füge Einträge hinzu oder entferne sie indem du eine Datei findest und die „Liste“ Registerkarte öffnest. Liste Wie wir helfen können Entfernung von Überschneidungen (Deduplikation) Text- und Metadatenextraktion OCR Wir können einen Highspeed-Zugang zu unseren vollständigen Sammlungen sowie zu unveröffentlichten Sammlungen anbieten. Dies ist ein Zugang auf Unternehmensebene, den wir für Spenden im Bereich von Zehntausenden USD bereitstellen können. Wir sind auch bereit, dies gegen hochwertige Sammlungen zu tauschen, die wir noch nicht haben. Wir können dir eine Preiserstattung der Kosten anbieten, wenn du dafür unsere Datenbank weiter bereichern und verbessern würdest. Dazu gehören unter anderem: Unterstütze die langfristige Archivierung menschlichen Wissens und erhalte gleichzeitig bessere Daten für dein Modell! <a %(a_contact)s>Kontaktiere uns</a>, um zu besprechen, wie wir zusammenarbeiten können. Es ist allgemein bekannt, dass LLMs von hochwertigem Datenmaterial profitieren. Wir haben die größte Sammlung von Büchern, wissenschaftlichen Aufsätzen, Zeitschriften usw. der Welt, die einige der qualitativ hochwertigsten Textquellen darstellen. LLM-Daten Einzigartige Größe und Vielfalt Unsere Sammlung enthält über hundert Millionen Dateien, darunter wissenschaftliche Zeitschriften, Lehrbücher und Zeitschriften. Wir erreichen diese Größe, indem wir große bestehende Repositories kombinieren. Einige unserer Quellsammlungen sind bereits in großen Mengen verfügbar (Sci-Hub und Teile von Libgen). Andere Quellen haben wir selbst befreit. <a %(a_datasets)s>Datensätze</a> zeigt eine vollständige Übersicht. Unsere Sammlung umfasst Millionen von Büchern, wissenschaftlichen Aufsätzen und Zeitschriften aus der Zeit vor dem E-Book-Zeitalter. Große Teile dieser Sammlung wurden bereits OCR-erfasst und weisen bereits wenig interne Überschneidungen auf. Fortfahren Wenn dein Geheimcode verloren gegangen ist, <a %(a_contact)s>kontaktiere</a> uns bitte und gib so viele Informationen wie möglich an. Lege wenn nötig ein temporäres Konto an, um uns zu kontaktieren. Bitte <a %(a_account)s>anmelden</a>, um diese Seite anzuzeigen.</a> Um zu verhindern, dass Spam-Bots viele Accounts erstellen, müssen wir zuerst deinen Browser überprüfen. Falls du in einer Endlosschleife stecken bleibst, schlagen wir vor, dass du <a %(a_privacypass)s>Privacy Pass</a> installierst. Es kann auch hilfreich sein, Werbeblocker und andere Browsererweiterungen zu deaktivieren. Anmelden / Registrieren Annas Archiv ist kurzfristig aufgrund Wartungsarbeiten nicht erreichbar. Schau in einer Stunde wieder vorbei. Alternativer Autor Alternative Beschreibung Alternative Ausgabe Alternative Dateierweiterung Alternativer Dateiname Alternativer Verlag Alternativtitel frei veröffentlicht am Weiterlesen… Beschreibung Annas Archiv nach CADAL SSNO-Nummer durchsuchen Annas Archiv nach DuXiu SSID-Nummer durchsuchen Annas Archiv nach DuXiu DXID-Nummer durchsuchen Durchsuche Annas Archiv nach ISBN Annas Archiv durchsuchen nach OCLC (WorldCat) Nummer Durchsuche Annas Archiv nach Open Library ID Annas Archiv Online-Viewer %(count)s betroffene Seiten Nach dem Herunterladen: Eine bessere Version dieser Datei ist möglicherweise unter %(link)s verfügbar Massen-Torrent-Downloads Sammlung Verwende Online-Tools, um zwischen Formaten zu konvertieren. Empfohlene Konvertierungstools: %(links)s Für große Dateien empfehlen wir die Verwendung eines Download-Managers, um Unterbrechungen zu vermeiden. Empfohlene Download-Manager: %(links)s EBSCOhost eBook Index (nur für Experten) (oben "GET" anklicken) (oben "GET" anklicken) Externe Downloads Du hast heute noch %(remaining)s übrig. Danke, dass du Mitglied bist! ❤️ Schnelle Downloads sind für heute aufgebraucht. Du hast diese Datei kürzlich heruntergeladen. Die Links bleiben eine Zeit lang gültig. Werde <a %(a_membership)s>Mitglied</a>, um die langfristige Aufbewahrung von Büchern, Dokumenten und mehr zu unterstützen. Als Dank für deine Unterstützung erhältst du schnellere Downloads. ❤️ 🚀 Schnelle Downloads 🐢 Langsame Downloads aus Internet Archive ausleihen IPFS-Gateway #%(num)d (mit IPFS musst du es möglicherweise mehrmals versuchen) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction Es ist bekannt, dass ihre Anzeigen bösartige Software enthalten, also verwende einen Werbeblocker oder klicke auf keine Anzeigen Amazons „Send to Kindle“ djazzs „Send to Kobo/Kindle“ MagzDB ManualsLib Nexus/STC (Nexus/STC-Dateien können beim Herunterladen unzuverlässig sein) Keine Downloads gefunden. Alle Mirrors verwenden dieselbe Datei und sollten daher sicher sein. Sei bitte trotzdem immer vorsichtig, wenn du Dateien aus dem Internet herunterlädst, insbesondere von Seiten abseits von Annas Archiv. Achte auch darauf, dass deine Geräte und Software auf dem neuesten Stand sind. (keine Weiterleitung) In unserem Viewer öffnen (im Viewer öffnen) Option #%(num)d: %(link)s %(extra)s Den Originaleintrag in CADAL suchen Manuell auf DuXiu suchen Originaldatensatz in ISBNdb suchen Finde der ursprünglich Datensatz in WorldCat Den Originaldatensatz in der Open Library suchen Durchsuche verschiedene andere Datenbanken nach ISBN (nur für Gäste mit Lesebehinderung) PubMed Du benötigst einen E-Book- oder PDF-Reader, um die Datei zu öffnen, je nach Dateiformat. Empfohlene E-Book-Reader: %(links)s Annas Archiv 🧬 SciDB Sci-Hub: %(doi)s (der zugehörige DOI ist möglicherweise nicht in Sci-Hub verfügbar) Du kannst sowohl PDF- als auch EPUB-Dateien an deinen Kindle oder Kobo eReader senden. Empfohlene Tools: %(links)s Mehr Infos dazu bei den <a %(a_slow)s>FAQs</a>. Unterstütze Autoren und Bibliotheken Wenn dir das Werk gefällt und du es dir leisten kannst, dann ziehe in Betracht, das Original zu kaufen oder die Autoren direkt zu unterstützen. Wenn es in deiner örtlichen Bibliothek verfügbar ist, ziehe in Betracht, es dort kostenlos auszuleihen. Downloads von Partnerservern sind derzeit nicht verfügbar für diese Datei. Torrent Von vertrauenswürdigen Partnern. Z-Library Z-Library auf Tor (benötigt den Tor-Browser) externe Downloads anzeigen <span class="font-bold">❌ Diese Datei hat womöglich Probleme und wurde daher in unserer Quellbibliothek versteckt.</span> Dies passiert manchmal, weil der Inhaber der Rechte darum gebeten hat, weil wir eine bessere Alternative gefunden haben oder wegen eines Problems mit der Datei selbst. Es kann sein, dass der Download trotzdem funktioniert, wir empfehlen aber zuerst nach einer anderen Datei zu suchen. Mehr Infos: Falls du die Datei dennoch herunterladen möchtest, öffne die Datei bitte nur mit aktueller und vertrauenswürdiger Software. Kommentare in Metadaten AA: Durchsuche Annas Archiv nach “%(name)s” Codeexplorer: “%(name)s” in Codeexplorer anzeigen URL: Webseite: Falls du die Datei selbst besitzst und sie noch nicht in Annas Archiv verfügbar ist, ziehe in Betracht sie <a %(a_request)s>hochzuladen</a>. Internet Archive Controlled-Digital-Lending-Datei „%(id)s“ Hierbei handelt es sich um den Eintrag einer Datei aus dem Internet Archiv, nicht um eine direkt herunterladbare Datei. Du kannst versuchen, das Buch auszuleihen (Link unten) oder diese URL verwenden, wenn du <a %(a_request)s>eine Datei anfragst</a>. Metadaten Verbessern CADAL SSNO %(id)s Metadatensatz Dies ist ein Metadatensatz, keine herunterladbare Datei. Du kannst diese URL verwenden, wenn du <a %(a_request)s>eine Datei anfragen willst</a>. Duxiu SSID %(id)s Metadatensatz ISBNdb %(id)s Metadatensatz MagzDB ID %(id)s Metadaten-Datensatz Nexus/STC ID %(id)s Metadaten-Datensatz OCLC (WorldCat) Nummer %(id)s Metadatensatz Open Library %(id)s Metadatensatz Sci-Hub Datei “%(id)s” Nicht gefunden “%(md5_input)s” wurde nicht in unserer Datenbank gefunden. Kommentar hinzufügen (%(count)s) Du kannst die md5 aus der URL auslesen, z.B. MD5 einer besseren Version dieser Datei (falls zutreffend). Füllen dies aus, wenn es eine andere Datei gibt, die dieser Datei sehr ähnlich ist (gleiche Ausgabe, gleiche Dateierweiterung, wenn du eine finden kannst), die anstelle dieser Datei verwendet werden sollte. Wenn du eine bessere Version dieser Datei außerhalb von Annas Archiv kennst, dann <a %(a_upload)s>lade sie bitte hoch</a>. Etwas ist schiefgelaufen. Bitte lade die Seite neu und versuche es erneut. Du hast einen Kommentar hinterlassen. Es kann eine Minute dauern, bis er angezeigt wird. Bitte verwenden das <a %(a_copyright)s>DMCA / Copyright-Anspruchsformular</a>. Beschreibe das Problem (erforderlich) Wenn diese Datei von hoher Qualität ist, kannst du hier darüber diskutieren! Wenn nicht, verwende bitte die Schaltfläche „Dateiproblem melden“. Großartige Dateiqualität (%(count)s) Dateiqualität Erfahre, wie du die <a %(a_metadata)s>Metadaten für diese Datei selbst verbessern</a> kannst. Problembeschreibung Bitte <a %(a_login)s>melde dich an</a>. Ich habe dieses Buch geliebt! Hilf der Community, indem du die Qualität dieser Datei meldest! 🙌 Etwas ist schiefgelaufen. Bitte lade die Seite neu und versuche es erneut. Dateiproblem melden (%(count)s) Vielen Dank für das Einreichen deines Berichts. Er wird auf dieser Seite angezeigt und manuell von Anna überprüft (bis wir ein richtiges Moderationssystem haben). Kommentar hinterlassen Bericht einreichen Was ist mit dieser Datei nicht in Ordnung? Ausleihen (%(count)s) Kommentare (%(count)s) Downloads (%(count)s) Erkunde Metadaten (%(count)s) Listen (%(count)s) Statistiken (%(count)s) Für Informationen über diese spezielle Datei, schau dir die zugehörige <a %(a_href)s>JSON-Datei</a> an. Dies ist eine Datei, die von der <a %(a_ia)s>IA’s Controlled Digital Lending</a> Bibliothek verwaltet und von Annas Archiv zur Suche indexiert wird. Für Informationen über die verschiedenen Datensätze, die wir zusammengestellt haben, siehe die <a %(a_datasets)s>Datensätze-Seite</a>. Metadaten aus verknüpftem Datensatz Metadaten in der Open Library verbessern Ein „MD5“ ist ein Hash, der aus den Dateiinhalten berechnet wird und basierend auf diesen Inhalten einigermaßen einzigartig ist. Alle hier indexierten Schattenbibliotheken verwenden hauptsächlich MD5s zur Identifizierung von Dateien. Eine Datei kann in mehreren Schattenbibliotheken erscheinen. Für Informationen über die verschiedenen Datensätze, die wir zusammengestellt haben, siehe die <a %(a_datasets)s>Datensätze-Seite</a>. Dateiqualität melden Gesamte Downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tschechische Metadaten %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Warnung: Mehrere verknüpfte Datensätze: Wenn du ein Buch auf Annas Archiv ansiehst, kannst du verschiedene Felder sehen: Titel, Autor, Verlag, Ausgabe, Jahr, Beschreibung, Dateiname und mehr. All diese Informationen werden als <em>Metadaten</em> bezeichnet. Da wir Bücher aus verschiedenen <em>Ursprungsbibliotheken</em> kombinieren, zeigen wir die Metadaten an, die in der jeweiligen Quellenbibliothek verfügbar sind. Zum Beispiel zeigen wir für ein Buch, das wir von Library Genesis haben, den Titel aus der Datenbank von Library Genesis an. Manchmal ist ein Buch in <em>mehreren</em> Ursprungsbibliotheken vorhanden, die unterschiedliche Metadatenfelder haben könnten. In diesem Fall zeigen wir einfach die längste Version jedes Feldes an, da diese hoffentlich die nützlichsten Informationen enthält! Wir zeigen die anderen Felder weiterhin unter der Beschreibung an, z.B. als „alternativer Titel“ (aber nur, wenn sie unterschiedlich sind). Wir extrahieren auch <em>Codes</em> wie Identifikatoren und Klassifikatoren aus der Urprungsbibliothek. <em>Identifikatoren</em> repräsentieren eindeutig eine bestimmte Ausgabe eines Buches; Beispiele sind ISBN, DOI, Open Library ID, Google Books ID oder Amazon ID. <em>Klassifikatoren</em> gruppieren mehrere ähnliche Bücher; Beispiele sind Dewey Decimal (DCC), UDC, LCC, RVK oder GOST. Manchmal sind diese Codes in den Ursprungsbibliotheken explizit verlinkt, und manchmal können wir sie aus dem Dateinamen oder der Beschreibung extrahieren (hauptsächlich ISBN und DOI). Wir können Identifikatoren verwenden, um Datensätze in <em>reinen Metadatensammlungen</em> zu finden, wie z.B. OpenLibrary, ISBNdb oder WorldCat/OCLC. Es gibt einen speziellen <em>Metadaten-Tab</em> in unserer Suchmaschine, falls du diese Sammlungen durchsuchen möchtest. Wir verwenden übereinstimmende Datensätze, um fehlende Metadatenfelder zu ergänzen (z.B. wenn ein Titel fehlt) oder z.B. als „alternativer Titel“ (falls ein vorhandener Titel existiert). Um genau zu sehen, woher die Metadaten eines Buches stammen, sieh dir den <em>„Technische Details“-Tab</em> auf einer Buchseite an. Er enthält einen Link zum Roh-JSON für dieses Buch, mit Verweisen auf das Roh-JSON der Originaldatensätze. Für weitere Informationen siehe die folgenden Seiten: <a %(a_datasets)s>Datensätze</a>, <a %(a_search_metadata)s>Suche (Metadaten-Tab)</a>, <a %(a_codes)s>Codes Explorer</a> und <a %(a_example)s>Beispiel-Metadaten-JSON</a>. Schließlich können alle unsere Metadaten als ElasticSearch- und MariaDB-Datenbanken <a %(a_generated)s>generiert</a> oder <a %(a_downloaded)s>heruntergeladen</a> werden. Hintergrund Du kannst zur Erhaltung von Büchern beitragen, indem du Metadaten verbesserst! Lies zunächst die Hintergrundinfos zu Metadaten auf Annas Archiv und lerne wie du Metadaten durch Verlinkung mit der Open Library verbessern kannst. Dafür erhältst du eine kostenlose Mitgliedschaft für Annas Archiv. Metadaten verbessern Was solltest du also tun, wenn du eine Datei mit schlechten Metadaten findest? Du kannst dann zur Ursprungsbibliothek gehen und deren Verfahren zur Korrektur der Metadaten befolgen. Aber was tun, wenn eine Datei in mehreren Ursprungsbibliotheken vorhanden ist? Es gibt einen Identifikator, der auf Annas Archiv besonders behandelt wird. <strong>Das annas_archive md5-Feld in der Open Library überschreibt immer alle anderen Metadaten!</strong> Lass uns zunächst einen Schritt zurückgehen und etwas über die Open Library lernen. Die Open Library wurde 2006 von Aaron Swartz mit dem Ziel gegründet, „eine Website für jedes jemals veröffentlichte Buch“ zu erstellen. Es ist eine Art Wikipedia für Buchmetadaten: Jeder kann sie bearbeiten, sie ist frei lizenziert und kann in großen Mengen heruntergeladen werden. Es ist eine Buchdatenbank, die am besten mit unserer Mission übereinstimmt – tatsächlich wurde Annas Archiv von Aaron Swartz' Vision und Leben inspiriert. Anstatt das Rad neu zu erfinden, haben wir beschlossen, unsere Freiwilligen zur Open Library zu leiten. Wenn du ein Buch mit falschen Metadaten siehst, kannst du auf folgende Weise helfen: Beachte, dass dies nur für Bücher gilt, nicht für wissenschaftliche Aufsätze oder andere Dateitypen. Für andere Dateitypen empfehlen wir weiterhin, die Ursprungsbibliothek zu finden. Es kann einige Wochen dauern, bis Änderungen in Annas Archiv aufgenommen werden, da wir den neuesten Open Library-Daten-Dump herunterladen und unseren Suchindex neu generieren müssen.  Gehe zur <a %(a_openlib)s>Open Library Webseite</a>. Finde den richtigen Buchdatensatz. <strong>WARNUNG:</strong> Stelle sicher, dass du die richtige <strong>Ausgabe</strong> auswählst. In der Open Library gibt es „Werke“ und „Ausgaben“. Ein „Werk“ könnte „Harry Potter und der Stein der Weisen“ sein. Eine „Ausgabe“ könnte sein: Die Erstausgabe von 1997, veröffentlicht von Bloomsbery mit 256 Seiten. Die Taschenbuchausgabe von 2003, veröffentlicht von Raincoast Books mit 223 Seiten. Die polnische Übersetzung von 2000 „Harry Potter I Kamie Filozoficzn“ von Media Rodzina mit 328 Seiten. Alle diese Ausgaben haben unterschiedliche ISBNs und unterschiedliche Inhalte, also stelle sicher, dass du die richtige auswählst! Bearbeite den Datensatz (oder erstelle ihn, falls keiner existiert) und füge so viele nützliche Informationen wie möglich hinzu! Du bist jetzt sowieso schon hier, also erstell wirklich einen sorgfältigen Datensatz. Wähle unter „ID-Nummern“ „Anna’s Archive“ und füge die MD5 des Buches aus Annas Archiv hinzu. Dies ist die lange Zeichenfolge aus Buchstaben und Zahlen nach „/md5/“ in der URL. Versuche andere Dateien in Annas Archiv zu finden, die ebenfalls zu diesem Datensatz passen, und füge diese ebenfalls hinzu. In Zukunft können wir diese als Duplikate auf der Suchseite von Annas Archiv gruppieren. Wenn du fertig bist, notiere die URL, die du gerade aktualisiert hast. Sobald du mindestens 30 Datensätze mit Annas Archiv MD5s aktualisiert hast, sende uns eine <a %(a_contact)s>E-Mail</a> und sende uns die Liste. Wir geben dir eine kostenlose Mitgliedschaft für Annas Archiv, damit du diese Arbeit leichter erledigen kannst (und als Dankeschön für deine Hilfe). Es müssen qualitativ hochwertige Bearbeitungen sein, die erhebliche Mengen an Informationen hinzufügen, andernfalls wird deine Anfrage abgelehnt. Deine Anfrage wird auch abgelehnt, wenn eine der Bearbeitungen von Open Library-Moderatoren zurückgesetzt oder korrigiert wird. Open Library Verlinkung Wenn du dich signifikant an der Entwicklung und den Aktivitäten unserer Arbeit beteiligst, können wir darüber sprechen, mehr von den Spendeneinnahmen mit dir zu teilen, damit du diese nach Bedarf einsetzen kannst. Wir werden erst für das Hosting bezahlen, wenn alles eingerichtet ist und du nachgewiesen hast, dass du das Archiv mit Updates auf dem neuesten Stand halten kannst. Das bedeutet, dass du die ersten 1-2 Monate aus eigener Tasche bezahlen musst. Deine Zeit wird nicht entschädigt (und unsere auch nicht), da dies reine Freiwilligenarbeit ist. Wir sind bereit, die Hosting- und VPN-Kosten zu übernehmen, zunächst bis zu 200 $ pro Monat. Dies ist ausreichend für einen grundlegenden Suchserver und einen DMCA-geschützten Proxy. Hosting-Kosten Bitte <strong>kontaktiere uns nicht</strong>, um um Erlaubnis zu bitten oder grundlegende Fragen zu stellen. Taten sagen mehr als Worte! Alle Informationen sind verfügbar, also leg einfach los und richte deinen Mirror ein. Sei so frei, Tickets oder Merge-Anfragen auf unserem Gitlab zu posten, wenn du auf Probleme stoßt. Wir müssen möglicherweise einige Mirror-spezifische Funktionen mit dir entwickeln, wie z.B. das Rebranding von „Annas Archiv“ auf den Namen deiner Website, (anfänglich) das Deaktivieren von Benutzerkonten oder das Verlinken zu unserer Hauptseite von Buchseiten. Sobald dein Mirror läuft, kontaktiere uns bitte. Wir würden gerne deine OpSec überprüfen, und sobald diese solide ist, werden wir auf deinen Mirror verlinken und enger mit dir zusammenarbeiten. Vielen Dank im Voraus an alle, die bereit sind, auf diese Weise beizutragen! Es ist nichts für schwache Nerven, aber es würde die Langlebigkeit der größten wirklich offenen Bibliothek in der Geschichte der Menschheit festigen. Erste Schritte Um die Widerstandsfähigkeit von Annas Archiv zu erhöhen, suchen wir Freiwillige, die Mirrorserver betreiben. Deine Version ist klar als Mirror gekennzeichnet, z. B. „Bobs Archiv, eine Spiegelung von Annas Archiv“. Du bist bereit, die mit dieser Arbeit verbundenen Risiken einzugehen, die erheblich sind. Du hast außerdem ein tiefes Verständnis für die erforderliche Betriebssicherheit. Der Inhalt <a %(a_shadow)s>dieser</a> <a %(a_pirate)s>Beiträge</a> ist dir selbsterklärend. Anfangs werden wir dir keinen Zugang zu den Downloads unserer Partner-Server gewähren, aber wenn alles gut läuft, können wir ihn mit dir teilen. Du betreibst den Open-Source-Code von Annas Archiv und aktualisierst regelmäßig sowohl den Code als auch die Daten. Du bist bereit, zu unserem <a %(a_codebase)s>Codebase</a> beizutragen – in Zusammenarbeit mit unserem Team. Wir suchen Folgendes: Mirrors: Aufruf an Freiwillige Mache eine neue Spende. Noch keine Spenden. <a %(a_donate)s>Mache meine erste Spende.</a> Spendendetails werden nicht öffentlich angezeigt. Meine Spenden 📡 Informationen zur Massenspiegelung unserer Sammlung findest du auf den Seiten <a %(a_datasets)s>Datensätze</a> und <a %(a_torrents)s>Torrents</a>. Downloads von deiner IP-Adresse in den letzten 24 Stunden: %(count)s. 🚀 Um schnellere Downloads zu erhalten und die Browserprüfungen zu umgehen, <a %(a_membership)s>werde Mitglied</a>. Download von einer Partner-Website Fühl dich frei, Annas Archiv in einem anderen Tab weiter zu durchsuchen, während du wartest (sofern dein Browser das Aktualisieren von Hintergrund-Tabs unterstützt). Warte ruhig, bis mehrere Download-Seiten gleichzeitig geladen sind (aber bitte lade nur eine Datei gleichzeitig pro Server herunter). Sobald du einen Download-Link erhältst, ist er mehrere Stunden lang gültig. Danke fürs Warten, so bleibt die Webseite für alle frei zugänglich! 😊 <a %(a_main)s>&lt; Alle Downloadlinks für diese Datei</a> ❌ Langsame Downloads sind nicht über Cloudflare-VPNs oder anderweitig über Cloudflare-IP-Adressen verfügbar. ❌ Langsame Downloads sind nur verfügbar über die offizielle Website. Besuche %(websites)s. <a %(a_download)s>📚 Jetzt herunterladen</a> Um jedem die Möglichkeit zu geben, Dateien kostenlos herunterzuladen, musst du etwas warten, bevor du diese Datei herunterladen kannst. Bitte warte <span %(span_countdown)s>%(wait_seconds)s</span> Sekunden, um diese Datei herunterzuladen. Achtung: Es gab die letzten 24 Stunden viele Downloads von deiner IP-Adresse. Downloads könnten deshalb langsamer als üblich sein. Wenn du ein VPN oder eine gemeinsam genutzte Internetverbindung verwendest oder dein Internetanbieter IP-Adressen teilt, kann diese Warnung darauf zurückzuführen sein. Speichern ❌ Es ist etwas schief gelaufen. Bitte erneut versuchen. ✅ Gespeichert. Bitte Seite neu laden. Ändere deinen Anzeigename. Der Identifier (der Teil nach “#”) kann nicht geändert werden. Profil erstellt <span %(span_time)s>%(time)s</span> Editieren Listen Erstelle eine neue Liste indem du eine Datei findest und die „Liste“ Registerkarte öffnest. Keine Listen Profil nicht gefunden. Profil Zu diesem Zeitpunkt können wir keine Buchanfragen erfüllen. Sende uns keine Buchanfragen per E-Mail. Bitte stelle deine Anfragen auf Z-Library oder den Libgen-Foren. Dateneintrag in Annas Archiv DOI: %(doi)s Download SciDB Nexus/STC Keine Vorschau verfügbar. Lade die Datei von <a %(a_path)s>Annas Archiv</a> herunter. Um die Zugänglichkeit und langfristige Bewahrung menschlichen Wissens zu unterstützen, werde <a %(a_donate)s>Mitglied</a>. Als Bonus 🧬 lädt&nbsp;SciDB für Mitglieder schneller und ohne Beschränkungen. Funktioniert etwas nicht? Versuche die Seite <a %(a_refresh)s>neuzuladen</a>. Sci-Hub Eigenes Suchfeld hinzufügen Suchbeschreibungen und Metadatenkommentare Publikationsjahr Fortgeschritten Zugang Inhalt Anzeigen Liste Tabelle Dateityp Sprache Sortieren nach Größte Am relevantesten Neueste (Dateigröße) (frei veröffentlicht) (Publikationsjahr) Älteste Zufällig Kleinste Quelle gescrapt und frei veröffentlicht von AA Digitales Ausleihen (%(count)s) wissenschaftliche Aufsätze (%(count)s) Wir haben Übereinstimmungen gefunden in: %(in)s. Du kannst auf die dort gefundene URL verweisen, wenn du eine Datei <a %(a_request)s>anfragst</a>. Metadaten (%(count)s) Um den Suchindex nach Codes zu durchsuchen, verwende den <a %(a_href)s>Codes Explorer</a>. Der Suchindex wird monatlich aktualisiert. Er umfasst derzeit Einträge bis zum %(last_data_refresh_date)s. Für weitere technische Informationen, siehe die Seite %(link_open_tag)sDatensätze</a>. Ausschließen Nur einschließen Ungeprüft Mehr… Nächste … Vorherige Dieser Suchindex enthält derzeit Metadaten aus der Controlled-Digital-Lending-Bibliothek des Internet Archive. Mehr über unsere Datensätze <a %(a_datasets)s>hier</a>. Weitere digitale Leihbibliotheken findest du auf <a %(a_wikipedia)s>Wikipedia</a> und im <a %(a_mobileread)s>MobileRead-Wiki</a>. Für DMCA / Urheberrechtsansprüche <a %(a_copyright)s>klicke hier</a>. Downloadzeit Fehler während der Suche. Versuche <a %(a_reload)s>die Seite neu zu laden</a>. Wenn das Problem weiterhin besteht, sende uns bitte eine E-Mail an %(email)s. Schneller Download Tatsächlich kann jeder dazu beitragen, diese Daten zu bewahren, indem er unsere <a %(a_torrents)s>Liste an Torrents</a> seedet. ➡️ Manchmal passiert dies fälschlicherweise, wenn der Suchserver langsam ist. In solchen Fällen kann ein <a %(a_attrs)s>erneutes Laden</a> helfen. ❌ Diese Datei hat womöglich Fehler. Suchst du nach wissenschaftlichen Aufsätzen? Dieser Suchindex umfasst derzeit Metadaten von ISBNdb und der Open Library. Mehr über unsere Datensätze <a %(a_datasets)s>hier</a>. Weltweit gibt es viele, viele Metadatenquellen für schriftliche Werke. <a %(a_wikipedia)s>Diese Wikipedia-Seite</a> ist ein guter Anfang, aber wenn du andere gute Listen kennst, lasse es uns bitte wissen. Für Metadaten zeigen wir die Originaleinträge. Wir führen Einträge nicht zusammen. Wir verfügen derzeit über den weltweit umfassendsten offenen Katalog an Büchern, wissenschaftlichen Aufsätzen und anderen schriftlichen Werken. Er enthält Werke von Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>und mehr</a>. <span %(classname)s>Keine Dateien gefunden.</span> Versuche weniger oder andere Suchbegriffe und Filter. Ergebnis %(from)s-%(to)s (von %(total)s) Wenn du andere „Schattenbibliotheken“ kennst, die wir spiegeln sollten, oder wenn du Fragen haben solltest, kontaktiere uns bitte per %(email)s. %(num)d teilweise Übereinstimmungen %(num)d+ Teilweise Übereinstimmung Nutze das Textfeld, um in digitalen Leihbibliotheken nach Dateien zu suchen. Nutze das Textfeld, um unseren Katalog mit %(count)s direkt herunterladbaren Dateien zu durchsuchen, die wir <a %(a_preserve)s>für immer aufbewahren</a>. Nutze zum Suchen das Textfeld. Verwende das Suchfeld um unseren Katalog an %(count)s wissenschaftlichen Arbeiten und Aufsätzen zu durchsuchen, welche wir für immer <a %(a_preserve)s>bewahrt haben</a>. Nutze das Textfeld, um nach Metadaten aus Bibliotheken zu suchen. Dies kann nützlich sein, wenn du<a %(a_request)s>eine Datei anfragen willst</a>. Tipp: Verwende für eine schnellere Navigation die Tastenkombinationen „/“ (Suchfokus), „Enter“ (Suche), „j“ (nach oben), „k“ (nach unten). Dies sind Metadaten-Datensätze, <span %(classname)s>keine</span> herunterladbaren Dateien. Suchoptionen Suchen Digitale Ausleihe Download wissenschaftliche Aufsätze Metadaten Neue Suche %(search_input)s - Suche Die Suche hat zu lange gedauert, was bedeutet, dass du möglicherweise ungenaue Ergebnisse siehst. Manchmal hilft es, die Seite<a %(a_reload)s>neu zu laden</a>. Die Suche hat zu lange gedauert, das ist normal bei vagen Anfragen. Die Filterzahlen sind möglicherweise ungenau. Für große Uploads (über 10.000 Dateien), welche nicht von Libgen oder Z-Library akzeptiert werden, schreib uns bitte an %(a_email)s. Für Libgen.li stelle sicher, dass du dich zuerst im <a %(a_forum)s>entsprechendem Forum</a> mit dem Benutzernamen %(username)s und dem Passwort %(password)s anmeldest und dann zur <a %(a_upload_page)s>Upload-Seite</a> zurückkehrst. Derzeit schlagen wir vor, neue Bücher bei den Library Genesis Forks hochzuladen. Hier ist eine <a %(a_guide)s>nützliche Anleitung</a>. Beachte, dass beide Forks die wir auf dieser Webseite indexieren Daten vom selben Upload System beziehen. Für kleine Uploads (bis zu 10.000 Dateien) laden diese bitte sowohl auf %(first)s als auch auf %(second)s hoch. Alternativ kannst du sie auch in die Z-Library <a %(a_upload)s>hier</a> hochladen. Lädst du wissenschaftliche Arbeiten hoch, dann lade sie bitte auch (zusätzlich zu Library Genesis) auf <a %(a_stc_nexus)s>STC Nexus</a> hoch. Sie ist die beste Schattenbibliothek für neue Arbeiten. Wir haben sie noch nicht integriert, aber wir werden sie irgendwann einbinden. Du kannst den <a %(a_telegram)s>Upload-Bot</a> auf Telegram verwenden oder dich an die Adresse wenden, die in der angehefteten Nachricht aufgeführt ist, falls du zu viele Dateien auf diese Weise hochladen musst. <span %(label)s>Schwere Freiwilligenarbeit (USD$50-USD$5,000 Prämien):</span> Wenn du viel Zeit und/oder Ressourcen für unsere Mission aufwenden kannst, würden wir gerne enger mit dir zusammenarbeiten. Anschließend kannst du dem inneren Team beitreten. Obwohl wir ein knappes Budget haben, können wir <span %(bold)s>💰 monetäre Prämien</span> für die intensivste Arbeit vergeben. <span %(label)s>Leichte Freiwilligenarbeit:</span> Wenn du nur ein paar Stunden hier und da erübrigen kannst, gibt es immer noch viele Möglichkeiten, wie du helfen könntest. Wir belohnen engagierte Freiwillige mit <span %(bold)s>🤝 Mitgliedschaften bei Annas Archiv</span>. Annas Archiv ist auf Freiwillige wie dich angewiesen. Wir begrüßen jedes Engagement und suchen vor allem Unterstützung in zwei Hauptkategorien: Wenn du keine Zeit für Freiwilligenarbeit hast, kannst du uns trotzdem sehr helfen, indem du<a %(a_donate)s>Geld spendest</a>, <a %(a_torrents)s>unsere Torrents seedest</a>, <a %(a_uploading)s>Bücher hochlädst</a> oder <a %(a_help)s>deinen Freunden von Annas Archiv erzählst</a>. <span %(bold)s>Unternehmen:</span> Wir bieten Hochgeschwindigkeitszugriff auf unsere Sammlungen im Austausch gegen Unternehmensspenden oder den Austausch neuer Sammlungen (z.B. neue Scans, OCR-Datasets, Bereicherung unserer Daten). <a %(a_contact)s>Kontaktiere uns</a>, wenn dies auf dich zutrifft. Siehe auch unsere <a %(a_llm)s>LLM Seite</a>. Prämien Wir suchen immer nach Menschen mit soliden Programmier- oder offensiven Sicherheitsfähigkeiten, die sich engagieren möchten. Du kannst einen ernsthaften Beitrag zur Bewahrung des Erbes der Menschheit leisten. Als Dankeschön vergeben wir Mitgliedschaften für solide Beiträge. Als großes Dankeschön vergeben wir Geldprämien für besonders wichtige und schwierige Aufgaben. Dies sollte nicht als Ersatz für einen Job betrachtet werden, aber es ist ein zusätzlicher Anreiz und kann bei anfallenden Kosten helfen. Der Großteil unseres Codes ist Open Source, und wir werden auch von deinem Code verlangen, dass er Open Source ist, wenn wir die Prämie vergeben. Es gibt einige Ausnahmen, die wir individuell besprechen können. Prämien werden an die erste Person vergeben, die eine Aufgabe abschließt. Kommentiere gerne ein Prämienticket, um anderen mitzuteilen, dass du an etwas arbeitest, damit andere warten oder dich kontaktieren können, um sich zusammenzuschließen. Sei dir jedoch bewusst, dass andere weiterhin daran arbeiten und versuchen können, dir zuvorzukommen. Wir vergeben jedoch keine Prämien für schlampige Arbeit. Wenn zwei hochwertige Einreichungen kurz nacheinander erfolgen (innerhalb eines Tages oder zwei), könnten wir nach unserem Ermessen beschließen, Prämien an beide zu vergeben, zum Beispiel 100%% für die erste Einreichung und 50%% für die zweite Einreichung (also insgesamt 150%%). Für die größeren Prämien (insbesondere Scraping-Prämien) kontaktiere uns bitte, wenn du ~5%% davon abgeschlossen hast und sicher bist, dass deine Methode auf den gesamten Milestone skalierbar ist. Du musst deine Methode mit uns teilen, damit wir dir Feedback geben können. Auf diese Weise können wir auch entscheiden, was zu tun ist, wenn mehrere Personen potentiell einen Anspruch auf eine Prämie haben, wie z.B. die Prämie möglicherweise an mehrere Personen zu vergeben, die Zusammenarbeit zu fördern usw. WARNUNG: Die Aufgaben mit hohen Prämien sind <span %(bold)s>schwierig</span> — es könnte klug sein, mit einfacheren zu beginnen. Geh zu unserer <a %(a_gitlab)s>Gitlab-Issueliste</a> und sortiere nach „Label priority“. Dies zeigt ungefähr die Reihenfolge der Aufgaben, die uns wichtig sind. Aufgaben ohne explizite Prämien sind weiterhin für eine Mitgliedschaft berechtigt, insbesondere diejenigen, die mit „Accepted“ und „Anna’s favorite“ markiert sind. Du könntest am besten mit einem „Starter project“ beginnen. Leichte Freiwilligenarbeit Wir haben jetzt auch einen synchronisierten Matrix-Kanal unter %(matrix)s. Wenn du ein paar Stunden Zeit hast, kannst du auf verschiedene Weise helfen. Trete unbedingt dem <a %(a_telegram)s>Freiwilligen-Chat auf Telegram</a> bei. Als Zeichen der Wertschätzung vergeben wir in der Regel 6 Monate „Glücklicher Bibliothekar“ für grundlegende Milestones, und mehr für weitere Freiwilligenarbeit. Alle Milestones erfordern qualitativ hochwertige Arbeit – schlampige Arbeit schadet uns mehr, als sie hilft, und wir werden sie ablehnen. Bitte <a %(a_contact)s>maile uns</a>, wenn du einen Milestone erreicht hast. %(links)s Links oder Screenshots von erfüllten Anfragen. Erfülle Buch- (oder Zeitschriften- usw.) Anfragen in den Z-Library- oder Library Genesis-Foren. Wir haben kein eigenes Buchanfragesystem, aber wir spiegeln diese Bibliotheken, daher machst du auch Annas Archiv besser, wenn du diese verbesserst. Milestone Aufgabe Hängt von der Aufgabe ab. Kleine Aufgaben, die in unserem <a %(a_telegram)s>Freiwilligen-Chat auf Telegram</a> gepostet werden. Normalerweise für Mitgliedschaften, manchmal für kleine Prämien. Kleine Aufgaben, die in unserer Freiwilligen-Chatgruppe gepostet werden. Stelle sicher, dass du einen Kommentar zu den von dir behobenen Problemen hinterlässt, damit andere deine Arbeit nicht duplizieren. %(links)s Links zu Datensätzen, die du verbessert hast. Du kannst die <a %(a_list)s>Liste zufälliger Metadatenprobleme</a> als Ausgangspunkt verwenden. Verbessere Metadaten durch <a %(a_metadata)s>Verlinkungen</a> mit der Open Library. Diese sollten zeigen, dass du jemandem von Annas Archiv erzählt hast und er oder sie dir entsprechend dankt. %(links)s Links oder Screenshots. Die Botschaft von Annas Archiv verbreiten. Zum Beispiel, indem du Bücher auf AA empfiehlst, auf unsere Blogbeiträge verlinkst oder allgemein Menschen auf unsere Website hinweist. Eine Sprache vollständig übersetzen (wenn sie nicht schon fast fertig war). <a %(a_translate)s>Übersetzen</a> der Website. Link zur Bearbeitungshistorie, die zeigt, dass du wesentliche Beiträge geleistet hast. Verbessere die Wikipedia-Seite von Annas Archiv in deiner Sprache. Füge Informationen von der Wikipedia-Seite von AA in anderen Sprachen sowie von unserer Website und unserem Blog hinzu. Setze außerdem Verweise auf AA auf anderen relevanten Seiten. Freiwilligenarbeit & Prämien 