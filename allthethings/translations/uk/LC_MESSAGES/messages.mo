��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  �d   ^h 9   uj �  �j 1  ]l �  �o a  zr �  �t �   �v �   0w S   �w    x �  �x   I{ �  f}   �~ �  � _  �� !  � [  .� }  �� .  � �  7� r   � n  f� �   Ֆ �  �� &   ~� h  �� S   � 8   b�    �� 0   �� a   � *   F�    q� X   �� @   � =   "�    `� n   ~� �  � �  ڢ �   �� [   0�    ��    ��    ��    ¥ 6   Υ    � 	   �    �    8� 7   ?�    w� #   }� 	   �� 
   ��    �� 8   Ŧ 8   �� #   7�   [� H  d�    �� 9   ��   � �   � �  � @  u� &   �� �   ݴ &   µ   � +   �� �  &� &   ָ   �� a   }� �   ߻    z� �  ��   {� |  �� �  �    �� �   �� �   �� >  � C   ]� C   �� �   �� l   �� �   � S   ��    �� �   �� �  �� q   �� �   �� +  �� �   �� �  � �   +� �  �� �   P�   .� v   A� D  �� �   �� S   �� �  Q� ?  �� �   =� S   �� �   @�    �   '� �  E� S   �    m�   ~� j  ��    ��   �� �   
� (  �� <  � �   [� N  N� �  �� z   p� �   �� �   �� �   /� �   �� �   � P   "� �   s� �   9� �   ��    G� �   _� �   � 1  ��   ��    � �  � �  �� o  �� �    �   � �  z �   �   � �   � �   �	 �  �
 
  � z  �
    3 �   D 5     O �  e �   0 �   � �   � �  � �   d �  G �  7 ?    �   Q �   � �   � �  }    # �   $ �   �$ B   �% c  �% I  0) h  z* !   �, �   - �   �- q  �. �  '0 D  �1 �  3 p  �5 K   a8 m  �8 (  ; �  D? 2  ?A   rE �  �G �   EI �   �I    �J S  �J   L u  "N �  �P �  .R   �S    �T L  �T   3W d  <Y    �Z   �Z �  �[ �  �] <  >_ N  {b �   �c    _d Z  qd �  �f    �h �  �h �  �j '  Bl Q  jo h  �p �   %s d   �s �  
t A   �u    �u <  �u �  !x �  �y L   �{ S   �{ R  @| �   � �   _� �   '� �  Á 1   S�    �� �  �� >  L� �   �� �   ]� �   � <   �� �  Ҋ �  ��    \�    v� E   �� e   ю m   7� Z   �� �    � �   ��    U� E   r� s   �� �   ,� =   �� U   �� N   O� �   ��    �� -   �� �  ʔ �  y� �  `� �  �   �� H  �� &  ߝ #   � �  *� �   ˡ ?  �� (  � [  � X  t� �  ͫ �  �� S   [� t   �� ?   $�   d� >  t� �  ��   �� 1  �� �  ν �  �� �  �� M   I� �  ��   y� T  ��    �� �  �� 4  ��   ��   �� �  �� 
   a� ?   o� �   ��    e� �  v� R  p� �  �� (  �� w  ��    /� j  O� �  �� S   �� �   �� �  n� �  � �   �� X  �� �    � �  �� %   M� U  s�   ��   ��   �� j  �� E  c�    �� �  �� �  ^ #   N +  r �  � �  �	 t  X
 �  � &  T S   { �  � _  { �   � h   � �   �    � E   � )   � ?    4   K #   � >  � �  � /  x! �  �$ `  G(    �) �  �) �  �. �  �2 �  5 �  �7 V  j;    �? �  �?    ]A    lA   �A �  �D h  PH �  �K    �O �  �O   1S h  6V z  �Y �  ] +  �a �  f /   �h >  �h �   (j �  �j �  \o O  r �   `s �   (t    (u    Iw 5  fw p   �x s   
y -   �y H   �y x   �y *  qz �  �~ �  7� _   �� x  �   �� 
   �� 5  �� g   � �   M� ;  %� L   a� �   ��    H�   g� �  {� 8  R� �  �� �   f�    @� �   W� �  �    ��   � L  �   a� \   v� W   Ӧ -  +� 9   Y� �  �� �  �� F  B� 9   �� �   ó �   �� �  c� �  �� "   �� Z  ̻ @   '� I  h� D  �� �  �� ~  �� �   $� 9   �� �  �� T   ��   ��   �� �  � �  �� �   � ]   �� �   � a  �� 8  _� 
  �� 	  �� W   �� m  � �  s� �  /� =   �   ^� �  m� 9   � �  @� ?   /� �  o� �   2� !   �� F  �� `  8� s  �� �  
�   �� �  ��   |� �   � 1  ` �  � �   ; �  	   � d   	 �   n �   < "   �     �  $ �   � h   � �  Q �   �  �   � f  �!   $ }  % �  �' 0   �)   �)    �* �  �* A   ^- ?   �-    �- '   �- @   . )   O. #   y. &   �. 
   �.    �.    �.    �.    
/ (    / @   I/    �/ I   �/    �/    �/    0    0 ~  #0 �   �1 :   K2    �2 1   �2 ?   �2 W   3 b   m3 >   �3    4    $4 0   14 6   b4 *   �4    �4    �4    �4    5 7   5 K   M5 }   �5 E   6 i   ]6 m   �6 D   57 @   z7 (   �7 1   �7 e   8 9   |8 �   �8 X   e9    �9 �   �9 q   b:    �:    �: (   ; !   1; )   S; '   };    �; #   �;    �;    �;    < 6   <    O< 	   a< 
   k<    v< D   y<    �<    �< 	   �< !   �< 	   �< :   =    ?=    E= 	   L=    V=    f= 8   r=    �=    �=    �=    �=     �= 	   >    $> =   C> 
   �> 
   �> 3   �>    �> ,   �>    ? '   ? 
   >?    I? 6   _? �   �? 6  ,@ �   cA @  �A �  6C �   F    �F 6   �F    �F    G    
G    #G    ;G A   YG �   �G _   :H �   �H 5   EI d   {I D   �I �   %J �   �J   tK �   �L z   M V   �M "   �M    N    /N    DN    YN <   nN    �N    �N    �N    �N !   �N    O    "O #   BO    fO -   sO    �O 1   �O 
   �O    �O    P    P .   -P ,   \P �  �P    6R    ;R    MR 3   SR    �R �   �R �   S :   �S "   �S E   �S    4T    <T    DT �   GT    U    U ?   3U �   sU #   EV    iV �   }V M   qW �  �W �   �\ %  0] -  V^ r  �_ [   �` �  Sa �   c �  �c q  �f L   �g 
   Bh �   Mh �   �h r   ci m   �i �   Dj g   �j �   Mk C   �k D   ,l    ql %   �l {   �l 4   $m    Ym     wm    �m (   �m �   �m    �n W   �n �   o C   �o <   �o �   8p �   �p �  pq -   3t #   at )  �t    �u 
   �u    �u *   �u    �u ?   v �  Lv *   Hx    sx    �x    �x �  �x 4   �z    �z    �z �   {    �{    �{ B   �{ #   B|    f| 9   o| �   �| .   �}    �} �   �} 0   h~ %   �~    �~ 2   �~ _    �   g 6   V� M   �� ,  ۀ �   � {   ҂ 8   N� �  �� z   <� $   �� S   ܅ 7   0� @  h� 4  �� (   ވ �   �   �� >  �� 3   � 4   �    N� �  n� F   l� 4   �� -   � 9   � 7   P� �   �� +   �� *   �� K   ݑ Y   )�    �� ;   �� 6   Β -   � �  3�   �� U  ژ c   0� K   ��    �� >   � �  ,� _  � %  G� .   m� j  �� e  � E   m� 8   �� q  � �  ^�    &�    7� C   K�    �� �  �� -   ��   ­ �  � �  ��    � �   2� =   
� <   K� �   �� �  � �  � �   v� H  \� �   �� �  [� }   &� +  ��   �� }   �� 2  T� f   �� S   �� &   B�    i�    z� <   �� 7   �� �   � U   �� 	   �� U   � �  [� A   .� l  p� �   �� �   �� [   t� B   �� )   �    =� A   X� ,   �� E   �� 1   
� k  ?� 0   �� E  ��    "� 
  <� �   G� �   �� �  �� Q  �� 2   �� �   � 	   �� �  �� �   �� 3   �� �  ��    �� 0   �� !   �� 0   �� D   &�    k�    x� u   ��   �� �  � 
   ��    �� 3   �� J   �   K� �   Q� �   O�    �    2� &   R�    y�    �� &   ��    �� o   �� I   V�   �� �   �� !   O� �   q� �   � ^   �� �   4� �   �� d   n� 
   �� �   �� X   ~� �   �� �   �� ^   � !   |� Y  �� �   �� X   |� �   �� }   ^� N   ��    +� K   <� �   �� �   � I   �� �   �    � �  � �   �� q   ~� �   ��    ��   �� g  �� *   7  -   b     �  
   �  �  �  8   | �   � !  6   X �   ] "  A 
  d �   o   P	 �   h
 �  �
 �   � X  ?
 3  � k  � 
   8 
   F 
   T �   b 
   5 
   C z   Q u   � O  B 
   � �   � r   S �   � [   b �   � n   s 
   � �   � 
   � 
   � 
   � .  � �   * �  �    �     �     �    �  3   �! -   " c  ?" �   �# 7   �$    �$ 5   �$ ^   % Q   o% Q   �% 6   & 6   J& w  �& >   �' �  8( �  �* �   d, �   6- �   �- �  �. +  ]0 �  �3   %7 �   =8 �   
9    �9 7  �9 &   ,< @  S< g  �> �  �? �   �A �  UB 0  &D �   WE �   8F O   �F c   :G    �G F   �G    �G    H    ,H l  =H    �I �   �I G   sJ    �J    �J    �J "   �J �   K �   �K �   �L i  M M   �N    �N    �N 0   O 8   4O    mO    O    �O    �O    �O    �O    �O    �O Y   �O �   NP    �P    Q    Q    -Q    BQ    VQ    mQ    �Q    �Q    �Q /   �Q �   
R &   �R K   	S �   US �   �S $  �T   �U �  W    Y �  
Z F   �[ �   \ L   �\ �   %] {   �] !  8^ �  Z_ �   �` �   �a    /b �   Hb �   �b �   �c    �d     �d *   �d .   e !   1e O   Se    �e 4   �e    �e .   �e 0   f R   Nf )   �f    �f     �f    
g    g    -g    Dg )   Lg    vg W   }g _   �g �   5h �   �h I   �i .   j �   Bj �   �j �   �k `   3l C   �l B   �l K   m �   gm H   �m y  2n �  �o �  oq 7   s b   Is �   �s d   �t �  �t u  �v   x �   y *   �y !  �y P  �z �   G| G   } �   e} �  F~ 5   � }   1� 5   �� d   � �   J� �   
�    ��    Ȃ    т >   ؂ �   � f   �� 8   e� J   �� >   � F   (� +   o� i   �� <   � �   B� Z   ֆ �  1� w   /� �   �� K  ;� M   �� *   Ջ '    � (   (� '   Q� *   y� )   �� *   Ό f   �� l   `� �  ͍ �   ˑ >   V� S   �� J   �    4� �   I� �   � X  ϔ _  (�    �� �   �� j   �� (   �� �  %� 0   �� Q   ߚ {   1� V   �� �   � �   �� u   j� -   �� &   �   5� G   H� 7   �� �   ȟ c  d� \   ȡ �   %� �   �� �   �� �   >� _   � |   o� �   � "   Ǧ �   � 1   �� l  �� K   (� o   t� &   � S   � T  _� �   ��    ?� �   �� �   �� �   Y�   �   �    � -   � �   /� �   �� &   D� )   k�    �� "   ��    ˲ 3   � �   � ~   ѳ ;   P� 3   �� >   �� 5   �� I   5� �   � R   F� �   �� �   y� =   � �   T� i  � D   �� �   ˺ 9   \� |   �� \   � K   p� �   �� �   �� A   `� n   �� 6   � I   H� �   �� "   #� �   F� A   �    Q� ;   i� �   �� _  i� a   ��    +�    I� a   g�    �� �   �� [   l�   �� �   ��    �� d   ��    B� %   c�   �� b   �� 7   �� �   7� ]  �� 
   1�    ?�    A� '   C� D   k� �   �� {   6� &   ��    �� 
   �� 0   � j   4� c   ��    � �   � q   ��    � >    � k   _� &   �� !   �� �   � �  �� z   X�    ��    ��    � �  � �   �    �� b  �� �  
� R   �� �   � ,   �� �  �� W   �� �   �� %   ��    ��   �� ,   � �   4� �   �� �   �� �   '� 4   �� �   � g   �� /   � �   3� o   �� A   0� k   r� A   �� �    �   �� ,  �� <   �� @  !� }  b� <  �� j   � R  �� �  �� l  t�   �� v  �� <   h� $   �� 1  �� J   �� 1  G  �   y m       � <   � �  �    � B  � �  � �  �
 �  } r   r �   � �   r F    W   c p   � �   , 1   � 0    a   6 >   � $   � h   � �   e X       t �   � Q  y "  � '   �     
   5 t   @ m   � y   # <  � �   �    � 6   � v  �    X �   m G  D �  �" s   $ 4   �$    �$    �$    �$ b   �$ F   B% �   �% �  S& �   �'    �(    �( 2   �( (   ) �   8) (   �) M   �)    F* Y   W* [   �*    
+    ,+ �   A+    �+ )   �+ B   , /   X,    �, �   �, I  D- �   �. �   7/ �   �/ *  j0    �1 7   �1 3  �1 �   3 C  
4    Q5 �   f5 �   6 s   �6 �   37 �   !8 �   �8 -   Y9 �   �9 '   D: %   l: )   �: /   �: -   �: -   ; %   H; ,   n;    �;    �; >   �; G    < >   H< ,   �< K   �< 7    = 7   8= -   p= $   �= s   �= Z   7>    �> y   �> Z   ? �   x? O   /@ 6   @ "   �@ 2   �@ '   A )   4A �   ^A �   !B �   �B �  �C *   0E .   [E 1   �E    �E i   �E "   CF H   fF H   �F 
  �F    H     H    >H 
   EH 	   PH c   ZH >   �H �  �H '   �J 7   �J ,   7K '   dK 9   �K $   �K :   �K <   &L @   cL H   �L /   �L    M �   $M Y   �M    -N    LN K   ]N t   �N <   O @   [O ?   �O �   �O �   �P �   �Q    R +    R 	   LR    VR 4   sR :   �R �  �R �   �U *   �V    �V 6   �V    0W H   OW    �W    �W �   �W A   yX z  �X #   6Z /   ZZ a  �Z /   �[ +   \ .   H\ 1   w\ D   �\ 1   �\     ]    >] U   T] )   �] A   �] T   ^   k^ �   x` {   �` �   {a 7   b   @b 4   Cc    xc �   �c    d 9   2d 9   ld o   �d �   e H   �e   �e !   �f    g (   5g    ^g    }g $   �g /   �g    �g     
h �   +h u  �h 6   >j 5   uj q  �j 
  l 3   +m E   _m    �m    �m %   �m    �m    n    n    /n    An    Tn    cn 
   tn    �n    �n    �n    �n    �n J   �n A  o �  Yp t  �q   Xt �  iw x  iz 7  �{    ~ �  /~ #   � �   � ~  �� �  .� I  � X  K� K   �� �   �� d   � %   G� j   m� �   ؋ �   a� �   � 	  �� �   �� L  d� �  ��    t� L  �� �  � �   v�   � "   
� �  0� y  �� �  3� O  ʞ    � �   6� �   ɠ O  l� �   �� �   �� �   L�    � 8   ,�    e� c   �� >   �    #� �   7� f   !� �   �� >   � �   O� �   K� �   >� �   � �   �� �   � �   �� �   0� �   �� �   �� �   9� �   /�    $� Y   5� [   �� �   � C   ��    ۲    � z   �� &   x� ?   ��    ߳ Z   �� k   X� j   Ĵ #   /�    S�    `�    w� 	   }� �   �� �   � �   ƶ Z   L�    �� 5   �� G   �    -�    I�    ^� 
   k�    v�    ��    ��    ��    ��    Ƹ    ڸ %   �    �    &� $   @�    e�    }�    ��    ��    �� V   ǹ -   � +   L� �   x�    1� �   N� C  �    /�    B�    \�    v�    ��    ��    ��   �� �   �� �   n�    �� *   � �   H� %   
� �   0� �   �� ;   ��    )� �   F� ]  �� �   R�   �� �   �� 7   ��   �� '   � (   =� �   f� �   �� /   �� �   	� �   �� �   �� q   z� %   �� 
   � !   �    ?�    V�    v�     ��    ��   �� �   �� �   �� N  y� �  �� �   e� t   �� �  m� �  N� �  �� �   ~� �  }�   )�    F� P  [� �  �� )  j�   �� !  �� �   �� U  h� 0   �� d   �� �   T� �  Q� d   �� j  =�    ��    �� )   �� �   �� d   �� �   M� O   �� �   F� s   �� �   _� 7   ��    1� e   R� 9   �� t   �� |  g� .   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: uk
Language-Team: uk <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library — це популярна (і нелегальна) бібліотека. Вони взяли колекцію Library Genesis і зробили її легкою для пошуку. Крім того, вони стали дуже ефективними у залученні нових внесків книг, стимулюючи користувачів різними привілеями. Наразі вони не повертають ці нові книги до Library Genesis. І на відміну від Library Genesis, вони не роблять свою колекцію легко дзеркальною, що ускладнює широке збереження. Це важливо для їхньої бізнес-моделі, оскільки вони стягують плату за доступ до своєї колекції в обсязі (більше 10 книг на день). Ми не робимо моральних суджень щодо стягнення грошей за масовий доступ до незаконної колекції книг. Безсумнівно, що Z-Library досягла успіху в розширенні доступу до знань і пошуку нових книг. Ми просто тут, щоб виконати свою частину: забезпечити довгострокове збереження цієї приватної колекції. - Анна та команда (<a %(reddit)s>Reddit</a>) У початковому випуску Дзеркала Піратської Бібліотеки (РЕДАГУВАНО: переміщено до <a %(wikipedia_annas_archive)s>Архів Анни</a>) ми створили дзеркало Z-Library, великої нелегальної колекції книг. Як нагадування, ось що ми написали в тому початковому блозі: Ця колекція датується серединою 2021 року. Тим часом Z-Library зростала вражаючими темпами: вони додали близько 3,8 мільйона нових книг. Звісно, там є деякі дублікати, але більшість з них, здається, є справді новими книгами або якіснішими сканами раніше поданих книг. Це значною мірою завдяки збільшенню кількості волонтерів-модераторів у Z-Library та їхній системі масового завантаження з видаленням дублікатів. Ми хотіли б привітати їх з цими досягненнями. Ми раді оголосити, що отримали всі книги, які були додані до Z-Library між нашим останнім дзеркалом і серпнем 2022 року. Ми також повернулися і зібрали деякі книги, які пропустили вперше. Загалом, ця нова колекція становить близько 24 ТБ, що набагато більше, ніж попередня (7 ТБ). Наше дзеркало тепер становить 31 ТБ загалом. Знову ж таки, ми видалили дублікати з Library Genesis, оскільки для цієї колекції вже доступні торренти. Будь ласка, перейдіть до Pirate Library Mirror, щоб ознайомитися з новою колекцією (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>). Там є більше інформації про те, як структуровані файли, і що змінилося з минулого разу. Ми не будемо посилатися на це звідси, оскільки це просто блог-сайт, який не розміщує жодних незаконних матеріалів. Звісно, роздача також є чудовим способом допомогти нам. Дякуємо всім, хто роздає наш попередній набір торрентів. Ми вдячні за позитивний відгук і раді, що є так багато людей, які піклуються про збереження знань і культури в такий незвичайний спосіб. 3x нових книг додано до Дзеркала Піратської Бібліотеки (+24 ТБ, 3,8 мільйона книг) Читайте супровідні статті від TorrentFreak: <a %(torrentfreak)s>перша</a>, <a %(torrentfreak_2)s>друга</a> - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) супровідні статті від TorrentFreak: <a %(torrentfreak)s>перша</a>, <a %(torrentfreak_2)s>друга</a> Не так давно «тіньові бібліотеки» були на межі зникнення. Sci-Hub, величезний незаконний архів наукових статей, припинив приймати нові роботи через судові позови. «Z-Library», найбільша незаконна бібліотека книг, побачила, як її ймовірних творців заарештували за кримінальні порушення авторського права. Вони неймовірно змогли уникнути арешту, але їхня бібліотека все ще під загрозою. Деякі країни вже роблять версію цього. TorrentFreak <a %(torrentfreak)s>повідомив</a>, що Китай і Японія ввели винятки для ШІ у свої закони про авторське право. Нам не зрозуміло, як це взаємодіє з міжнародними договорами, але це, безумовно, дає захист їхнім внутрішнім компаніям, що пояснює те, що ми спостерігаємо. Що стосується Архіву Анни — ми продовжимо нашу підпільну роботу, засновану на моральних переконаннях. Проте наше найбільше бажання — вийти на світло і легально посилити наш вплив. Будь ласка, реформуйте авторське право. Коли Z-Library зіткнулася з закриттям, я вже зробив резервну копію всієї її бібліотеки і шукав платформу для її розміщення. Це стало моєю мотивацією для створення Архіву Анни: продовження місії, що стоїть за цими попередніми ініціативами. Відтоді ми виросли до найбільшої тіньової бібліотеки у світі, розміщуючи понад 140 мільйонів текстів, захищених авторським правом, у різних форматах — книги, наукові статті, журнали, газети та інше. Моя команда і я — ідеологи. Ми вважаємо, що збереження та розміщення цих файлів є морально правильним. Бібліотеки по всьому світу стикаються зі скороченням фінансування, і ми не можемо довірити спадщину людства корпораціям. Потім з'явився штучний інтелект. Практично всі великі компанії, що створюють LLM, звернулися до нас для навчання на наших даних. Більшість (але не всі!) компаній зі США переглянули своє рішення, коли зрозуміли незаконний характер нашої роботи. На відміну від цього, китайські фірми з ентузіазмом прийняли нашу колекцію, очевидно, не турбуючись про її законність. Це примітно, враховуючи роль Китаю як підписанта майже всіх основних міжнародних договорів про авторське право. Ми надали високошвидкісний доступ приблизно 30 компаніям. Більшість з них — компанії LLM, а деякі — брокери даних, які перепродаватимуть нашу колекцію. Більшість з них китайські, хоча ми також працювали з компаніями зі США, Європи, Росії, Південної Кореї та Японії. DeepSeek <a %(arxiv)s>зізнався</a>, що попередня версія була навчена на частині нашої колекції, хоча вони неохоче говорять про свою останню модель (ймовірно, також навчена на наших даних). Якщо Захід хоче залишатися попереду в гонці LLM, а в кінцевому підсумку і AGI, йому потрібно переглянути свою позицію щодо авторського права, і швидко. Незалежно від того, чи погоджуєтеся ви з нами щодо нашої моральної позиції, це тепер стає питанням економіки, а навіть національної безпеки. Всі блоки влади будують штучних супер-науковців, супер-хакерів і супер-військових. Свобода інформації стає питанням виживання для цих країн — навіть питанням національної безпеки. Наша команда з усього світу, і ми не маємо конкретної орієнтації. Але ми б закликали країни з сильними законами про авторське право використовувати цю екзистенційну загрозу для їх реформування. Тож що робити? Наша перша рекомендація проста: скоротити термін дії авторського права. У США авторське право надається на 70 років після смерті автора. Це абсурдно. Ми можемо привести це у відповідність з патентами, які надаються на 20 років після подання. Це має бути більш ніж достатньо часу для авторів книг, статей, музики, мистецтва та інших творчих робіт, щоб отримати повну компенсацію за свої зусилля (включаючи довгострокові проекти, такі як екранізації). Тоді, як мінімум, політики повинні включити винятки для масового збереження та поширення текстів. Якщо втрата доходу від окремих клієнтів є основною проблемою, розповсюдження на особистому рівні може залишатися забороненим. Натомість ті, хто здатний керувати великими сховищами — компанії, що навчають LLM, разом з бібліотеками та іншими архівами — будуть охоплені цими винятками. Реформа авторського права необхідна для національної безпеки Коротко: Китайські LLM (включаючи DeepSeek) навчаються на моєму незаконному архіві книг і статей — найбільшому у світі. Захід повинен переглянути закон про авторське право як питання національної безпеки. Будь ласка, перегляньте <a %(all_isbns)s>оригінальний допис у блозі</a> для отримання додаткової інформації. Ми оголосили виклик для покращення цього. Ми мали нагородити перше місце премією в $6,000, друге місце — $3,000, а третє місце — $1,000. Через величезний відгук та неймовірні подання, ми вирішили трохи збільшити призовий фонд і нагородити чотири третіх місця по $500 кожне. Переможці наведені нижче, але обов’язково перегляньте всі подання <a %(annas_archive)s>тут</a>, або завантажте наш <a %(a_2025_01_isbn_visualization_files)s>об'єднаний торрент</a>. Перше місце $6,000: phiresky Це <a %(phiresky_github)s>подання</a> (<a %(annas_archive_note_2951)s>коментар на Gitlab</a>) є саме тим, чого ми хотіли, і навіть більше! Нам особливо сподобалися неймовірно гнучкі варіанти візуалізації (навіть з підтримкою користувацьких шейдерів), але з повним списком пресетів. Нам також сподобалося, наскільки все швидко і плавно працює, проста реалізація (яка навіть не має бекенду), розумна мінікарта та детальне пояснення в їхньому <a %(phiresky_github)s>дописі в блозі</a>. Неймовірна робота і заслужена перемога! - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Наші серця сповнені вдячності. Помітні ідеї Хмарочоси для рідкісності Багато слайдерів для порівняння Datasets, наче ви діджей. Шкала з кількістю книг. Гарні мітки. Крута стандартна кольорова схема та теплокарта. Унікальний вигляд карти та фільтри Анотації, а також живі статистики Живі статистики Деякі інші ідеї та реалізації, які нам особливо сподобалися: Ми могли б продовжувати ще довго, але зупинимося тут. Обов’язково перегляньте всі подання <a %(annas_archive)s>тут</a>, або завантажте наш <a %(a_2025_01_isbn_visualization_files)s>об’єднаний торрент</a>. Так багато подань, і кожне приносить унікальну перспективу, чи то в інтерфейсі, чи в реалізації. Ми принаймні інтегруємо подання, що зайняло перше місце, на наш основний вебсайт, а можливо, і деякі інші. Ми також почали думати про те, як організувати процес ідентифікації, підтвердження та архівування найрідкісніших книг. Більше інформації згодом. Дякуємо всім, хто взяв участь. Це дивовижно, що так багато людей піклуються. Легке перемикання між Datasets для швидких порівнянь. Усі ISBN CADAL SSNOs Витік даних CERLALC DuXiu SSIDs Індекс електронних книг EBSCOhost Google Books Goodreads Інтернет-архів ISBNdb Глобальний реєстр видавців ISBN Libby Файли в Архіві Анни Nexus/STC OCLC/Worldcat OpenLibrary Російська державна бібліотека Імперська бібліотека Трантора Друге місце $3,000: hypha «Хоча ідеальні квадрати та прямокутники математично приємні, вони не забезпечують кращої локальності в контексті картографування. Я вважаю, що асиметрія, властива цим кривим Гільберта або класичним Мортону, не є недоліком, а особливістю. Так само, як відома форма чобота Італії робить її миттєво впізнаваною на карті, унікальні "особливості" цих кривих можуть слугувати когнітивними орієнтирами. Ця відмінність може покращити просторову пам’ять і допомогти користувачам орієнтуватися, потенційно полегшуючи пошук конкретних регіонів або виявлення шаблонів». Ще одне неймовірне <a %(annas_archive_note_2913)s>подання</a>. Не таке гнучке, як перше місце, але нам насправді більше сподобалася його макрорівнева візуалізація порівняно з першим місцем (крива заповнення простору, межі, маркування, виділення, панорамування та масштабування). <a %(annas_archive_note_2971)s>Коментар</a> Джо Девіса відгукнувся нам: І все ще багато варіантів для візуалізації та рендерингу, а також неймовірно плавний та інтуїтивно зрозумілий інтерфейс. Міцне друге місце! - Анна та команда (<a %(reddit)s>Reddit</a>) Кілька місяців тому ми оголосили <a %(all_isbns)s>конкурс з призом у $10,000</a> на створення найкращої можливої візуалізації наших даних, що показує простір ISBN. Ми наголосили на показі, які файли ми вже архівували, а які ні, і пізніше додали набір даних, що описує, скільки бібліотек мають ISBN (міра рідкості). Ми були вражені відповіддю. Було стільки креативності. Велике спасибі всім, хто взяв участь: ваша енергія та ентузіазм заразливі! Зрештою, ми хотіли відповісти на такі питання: <strong>які книги існують у світі, скільки ми вже архівували, і на яких книгах слід зосередитися далі?</strong> Приємно бачити, що так багато людей цікавляться цими питаннями. Ми самі почали з базової візуалізації. Менше ніж у 300 кб, це зображення стисло представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства: Третє місце $500 #1: maxlion У цьому <a %(annas_archive_note_2940)s>поданні</a> нам дуже сподобалися різні види перегляду, зокрема порівняльний та видавничий перегляди. Третє місце $500 #2: abetusk Хоча інтерфейс не найвідшліфованіший, це <a %(annas_archive_note_2917)s>подання</a> відповідає багатьом вимогам. Нам особливо сподобалася його функція порівняння. Третє місце $500 #3: conundrumer0 Як і перше місце, це <a %(annas_archive_note_2975)s>подання</a> вразило нас своєю гнучкістю. Зрештою, це те, що робить інструмент візуалізації чудовим: максимальна гнучкість для досвідчених користувачів, зберігаючи простоту для середніх користувачів. Третє місце $500 #4: charelf Останнє <a %(annas_archive_note_2947)s>подання</a>, яке отримало нагороду, досить базове, але має деякі унікальні функції, які нам дуже сподобалися. Нам сподобалося, як вони показують, скільки наборів даних охоплюють певний ISBN як міру популярності/надійності. Нам також дуже сподобалася простота, але ефективність використання повзунка непрозорості для порівнянь. Переможці конкурсу на візуалізацію ISBN з призом у $10,000 Коротко: Ми отримали неймовірні заявки на конкурс на візуалізацію ISBN з призом у $10,000. Передумови Як Анна Архів може досягти своєї місії збереження всіх знань людства, не знаючи, які книги ще існують? Нам потрібен список завдань. Один із способів скласти карту — це використання номерів ISBN, які з 1970-х років присвоюються кожній опублікованій книзі (у більшості країн). Не існує центрального органу, який знає всі призначення ISBN. Натомість це розподілена система, де країни отримують діапазони номерів, які потім призначають меншим діапазонам великим видавцям, які можуть далі поділити діапазони на менших видавців. Нарешті, окремі номери присвоюються книгам. Ми почали складати карту ISBN <a %(blog)s>два роки тому</a> з нашого збору даних ISBNdb. З того часу ми зібрали багато інших джерел metadata, таких як <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby та інші. Повний список можна знайти на сторінках “Datasets” та “Torrents” на Анна Архів. Зараз у нас найбільша повністю відкрита, легко завантажувана колекція metadata книг (а отже, і ISBN) у світі. Ми <a %(blog)s>багато писали</a> про те, чому ми дбаємо про збереження, і чому ми зараз у критичному вікні. Ми повинні ідентифікувати рідкісні, недооцінені та унікально вразливі книги та зберегти їх. Наявність хорошої metadata на всі книги у світі допомагає в цьому. Винагорода $10,000 Сильна увага буде приділена зручності використання та зовнішньому вигляду. Показуйте фактичну metadata для окремих ISBN при збільшенні, наприклад, назву та автора. Краща крива заповнення простору. Наприклад, зигзаг, що йде від 0 до 4 на першому рядку, а потім назад (у зворотному напрямку) від 5 до 9 на другому рядку — рекурсивно застосовано. Різні або настроювані колірні схеми. Спеціальні види для порівняння Datasets. Способи налагодження проблем, таких як інші metadata, які не дуже узгоджуються (наприклад, значно різні назви). Анотування зображень з коментарями щодо ISBN або діапазонів. Будь-які евристики для ідентифікації рідкісних або під загрозою книг. Будь-які творчі ідеї, які ви можете придумати! Код Код для створення цих зображень, а також інші приклади, можна знайти в <a %(annas_archive)s>цьому каталозі</a>. Ми розробили компактний формат даних, з яким вся необхідна інформація про ISBN займає близько 75 МБ (у стиснутому вигляді). Опис формату даних і код для його створення можна знайти <a %(annas_archive_l1244_1319)s>тут</a>. Для винагороди вам не потрібно використовувати це, але це, ймовірно, найзручніший формат для початку. Ви можете трансформувати наші metadata як завгодно (хоча весь ваш код має бути з відкритим вихідним кодом). Ми не можемо дочекатися, щоб побачити, що ви придумаєте. Удачі! Форкніть цей репозиторій і відредагуйте HTML цього блогу (жодні інші бекенди, окрім нашого Flask бекенду, не дозволені). Зробіть зображення вище плавно масштабованим, щоб ви могли збільшувати до окремих ISBN. Натискання на ISBN має перенаправляти на сторінку metadata або пошук на Анна Архів. Ви все ще повинні мати можливість перемикатися між усіма різними datasets. Діапазони країн та видавців повинні підсвічуватися при наведенні. Ви можете використовувати, наприклад, <a %(github_xlcnd_isbnlib)s>data4info.py в isbnlib</a> для інформації про країни та наш збір “isbngrp” для видавців (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Це повинно добре працювати на настільних комп’ютерах і мобільних пристроях. Тут є багато чого дослідити, тому ми оголошуємо винагороду за покращення візуалізації вище. На відміну від більшості наших винагород, ця має обмежений час. Ви повинні <a %(annas_archive)s>подати</a> свій відкритий код до 2025-01-31 (23:59 UTC). Найкраща подача отримає $6,000, друге місце — $3,000, а третє місце — $1,000. Всі винагороди будуть виплачені за допомогою Monero (XMR). Нижче наведені мінімальні критерії. Якщо жодна подача не відповідає критеріям, ми все ще можемо присудити деякі винагороди, але це буде на наш розсуд. Для додаткових балів (це лише ідеї — дайте волю своїй творчості): Ви МОЖЕТЕ повністю відхилитися від мінімальних критеріїв і зробити абсолютно іншу візуалізацію. Якщо це дійсно вражаюче, то це кваліфікується для винагороди, але на наш розсуд. Зробіть подання, залишивши коментар до <a %(annas_archive)s>цього питання</a> з посиланням на ваш форкований репозиторій, запит на злиття або різницю. - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Це зображення має розмір 1000×800 пікселів. Кожен піксель представляє 2,500 ISBN. Якщо у нас є файл для ISBN, ми робимо цей піксель більш зеленим. Якщо ми знаємо, що ISBN було видано, але у нас немає відповідного файлу, ми робимо його більш червоним. Менше ніж у 300 кб, це зображення стисло представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства (кілька сотень ГБ у повному стисненні). Воно також показує: ще багато роботи залишилося для резервного копіювання книг (у нас є лише 16%). Візуалізація всіх ISBN — нагорода $10,000 до 2025-01-31 Це зображення представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства. Візуалізація Окрім загального зображення, ми також можемо переглянути окремі datasets, які ми отримали. Використовуйте випадаюче меню та кнопки, щоб переключатися між ними. У цих зображеннях можна побачити багато цікавих візерунків. Чому існує певна регулярність ліній і блоків, яка, здається, відбувається на різних масштабах? Що таке порожні області? Чому певні datasets так згруповані? Ми залишимо ці питання як вправу для читача. - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Висновок З цим стандартом ми можемо робити релізи більш поступово і легше додавати нові джерела даних. У нас вже є кілька захоплюючих релізів у процесі! Ми також сподіваємося, що іншим тіньовим бібліотекам стане легше дзеркалити наші колекції. Зрештою, наша мета — зберегти людські знання та культуру назавжди, тому чим більше надмірності, тим краще. Приклад Розглянемо наш нещодавній реліз Z-Library як приклад. Він складається з двох колекцій: “<span style="background: #fffaa3">zlib3_records</span>” та “<span style="background: #ffd6fe">zlib3_files</span>”. Це дозволяє нам окремо збирати та випускати записи metadata з фактичних файлів книг. Таким чином, ми випустили два торренти з файлами metadata: Ми також випустили кілька торрентів з папками бінарних даних, але тільки для колекції “<span style="background: #ffd6fe">zlib3_files</span>”, всього 62: Запустивши <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, ми можемо побачити, що всередині: У цьому випадку це metadata книги, як повідомляє Z-Library. На верхньому рівні у нас є лише “aacid” та “metadata”, але немає “data_folder”, оскільки немає відповідних бінарних даних. AACID містить “22430000” як основний ID, який ми можемо побачити, взятий з “zlibrary_id”. Ми можемо очікувати, що інші AAC у цій колекції матимуть таку ж структуру. Тепер запустимо <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Це набагато менше metadata AAC, хоча основна частина цього AAC знаходиться в іншому місці у бінарному файлі! Зрештою, у нас є “data_folder” цього разу, тому ми можемо очікувати, що відповідні бінарні дані будуть розташовані за адресою <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” містить “zlibrary_id”, тому ми можемо легко асоціювати його з відповідним AAC у колекції “zlib_records”. Ми могли б асоціювати різними способами, наприклад, через AACID — стандарт цього не вимагає. Зверніть увагу, що також не обов'язково, щоб поле “metadata” саме по собі було JSON. Це може бути рядок, що містить XML або будь-який інший формат даних. Ви навіть можете зберігати інформацію про metadata у відповідному бінарному блоці, наприклад, якщо це багато даних. Різнорідні файли та metadata, якомога ближче до оригінального формату. Бінарні дані можуть бути безпосередньо обслуговувані веб-серверами, такими як Nginx. Різнорідні ідентифікатори в джерельних бібліотеках або навіть відсутність ідентифікаторів. Окремі випуски metadata проти даних файлів або випуски лише metadata (наприклад, наш випуск ISBNdb). Розповсюдження через торренти, хоча з можливістю інших методів розповсюдження (наприклад, IPFS). Незмінні записи, оскільки ми повинні припускати, що наші торренти будуть існувати вічно. Інкрементальні випуски / додаткові випуски. Машинозчитувані та записувані, зручно та швидко, особливо для нашого стеку (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Досить легка перевірка людиною, хоча це вторинне до машинозчитуваності. Легко засівати наші колекції за допомогою стандартного орендованого seedbox. Цілі дизайну Нам не важливо, щоб файли було легко переглядати вручну на диску або шукати без попередньої обробки. Нам не важливо бути безпосередньо сумісними з існуючим бібліотечним програмним забезпеченням. Хоча будь-хто повинен легко засівати нашу колекцію за допомогою торрентів, ми не очікуємо, що файли будуть використовувані без значних технічних знань та зобов'язань. Наш основний випадок використання — це розповсюдження файлів та пов'язаних з ними metadata з різних існуючих колекцій. Наші найважливіші міркування: Деякі нецілі: Оскільки Архів Анни є відкритим кодом, ми хочемо безпосередньо використовувати наш формат. Коли ми оновлюємо наш пошуковий індекс, ми отримуємо доступ лише до загальнодоступних шляхів, щоб будь-хто, хто форкне нашу бібліотеку, міг швидко почати роботу. <strong>AAC.</strong> AAC (Контейнер Архіву Анни) — це один елемент, що складається з <strong>metadata</strong>, і, за бажанням, <strong>бінарних даних</strong>, обидва з яких є незмінними. Він має глобально унікальний ідентифікатор, який називається <strong>AACID</strong>. <strong>AACID.</strong> Формат AACID такий: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Наприклад, фактичний AACID, який ми випустили, це <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Діапазон AACID.</strong> Оскільки AACID містять монотонно зростаючі часові мітки, ми можемо використовувати це для позначення діапазонів у межах певної колекції. Ми використовуємо цей формат: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, де часові мітки включені. Це узгоджується з нотацією ISO 8601. Діапазони є безперервними і можуть перекриватися, але у випадку перекриття повинні містити ідентичні записи, як і ті, що були раніше випущені в цій колекції (оскільки AAC є незмінними). Пропущені записи не допускаються. <code>{collection}</code>: назва колекції, яка може містити ASCII літери, цифри та підкреслення (але не подвійні підкреслення). <code>{collection-specific ID}</code>: специфічний для колекції ідентифікатор, якщо застосовно, наприклад, ID Z-Library. Може бути опущено або скорочено. Повинен бути опущений або скорочений, якщо AACID в іншому випадку перевищить 150 символів. <code>{ISO 8601 timestamp}</code>: коротка версія ISO 8601, завжди в UTC, наприклад, <code>20220723T194746Z</code>. Це число повинно монотонно збільшуватися для кожного випуску, хоча його точна семантика може відрізнятися для кожної колекції. Ми пропонуємо використовувати час скрапінгу або генерації ID. <code>{shortuuid}</code>: UUID, але стиснутий до ASCII, наприклад, з використанням base57. Ми наразі використовуємо бібліотеку Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Папка з бінарними даними.</strong> Папка з бінарними даними діапазону AAC для однієї конкретної колекції. Вони мають такі властивості: Каталог повинен містити файли даних для всіх AAC у вказаному діапазоні. Кожен файл даних повинен мати AACID як ім'я файлу (без розширень). Назва каталогу повинна бути діапазоном AACID, з префіксом <code style="color: green">annas_archive_data__</code>, і без суфікса. Наприклад, один з наших фактичних випусків має каталог під назвою<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Рекомендується робити ці папки досить керованими за розміром, наприклад, не більше 100 ГБ-1 ТБ кожна, хоча ця рекомендація може змінюватися з часом. <strong>Колекція.</strong> Кожен AAC належить до колекції, яка за визначенням є списком AAC, що є семантично узгодженими. Це означає, що якщо ви вносите значні зміни у формат metadata, то вам потрібно створити нову колекцію. Стандарт <strong>Файл metadata.</strong> Файл metadata містить metadata діапазону AAC для однієї конкретної колекції. Вони мають такі властивості: <code>data_folder</code> є необов'язковим і є назвою папки з бінарними даними, яка містить відповідні бінарні дані. Ім'я файлу відповідних бінарних даних у цій папці — це AACID запису. Кожен об'єкт JSON повинен містити наступні поля на верхньому рівні: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (необов'язково). Інші поля не допускаються. Ім'я файлу повинно бути діапазоном AACID, з префіксом <code style="color: red">annas_archive_meta__</code> і закінчуватися на <code>.jsonl.zstd</code>. Наприклад, один з наших випусків називається<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Як вказано у розширенні файлу, тип файлу — це <a %(jsonlines)s>JSON Lines</a>, стиснутий за допомогою <a %(zstd)s>Zstandard</a>. <code>metadata</code> — це довільні metadata, відповідно до семантики колекції. Вони повинні бути семантично узгодженими в межах колекції. Префікс <code style="color: red">annas_archive_meta__</code> може бути адаптований до назви вашої установи, наприклад, <code style="color: red">my_institute_meta__</code>. <strong>Колекції “записів” та “файлів”.</strong> За звичаєм, часто зручно випускати “записи” та “файли” як різні колекції, щоб їх можна було випускати за різними графіками, наприклад, на основі швидкості скрапінгу. “Запис” — це колекція, що містить лише metadata, яка включає інформацію, таку як назви книг, автори, ISBN тощо, тоді як “файли” — це колекції, що містять самі файли (pdf, epub). Зрештою, ми зупинилися на відносно простому стандарті. Він досить гнучкий, не нормативний і знаходиться в процесі розробки. <strong>Торренти.</strong> Файли metadata та папки з бінарними даними можуть бути об'єднані в торренти, з одним торрентом на файл metadata або одним торрентом на папку з бінарними даними. Торренти повинні мати оригінальну назву файлу/каталогу плюс суфікс <code>.torrent</code> як їхню назву файлу. <a %(wikipedia_annas_archive)s>Архів Анни</a> став безперечно найбільшою тіньовою бібліотекою у світі і єдиною тіньовою бібліотекою такого масштабу, яка є повністю з відкритим вихідним кодом і відкритими даними. Нижче наведено таблицю з нашої сторінки Datasets (трохи змінена): Ми досягли цього трьома способами: Віддзеркалення існуючих тіньових бібліотек з відкритими даними (як Sci-Hub і Library Genesis). Допомога тіньовим бібліотекам, які хочуть бути більш відкритими, але не мали часу або ресурсів для цього (як колекція коміксів Libgen). Збирання даних з бібліотек, які не бажають ділитися даними в масовому порядку (як Z-Library). Для (2) та (3) ми тепер самостійно керуємо значною колекцією торрентів (сотні терабайтів). До цього часу ми підходили до цих колекцій як до унікальних, тобто створювали індивідуальну інфраструктуру та організацію даних для кожної колекції. Це додає значних накладних витрат до кожного випуску і робить особливо складним здійснення більш поступових випусків. Ось чому ми вирішили стандартизувати наші випуски. Це технічний блог-пост, у якому ми представляємо наш стандарт: <strong>Контейнери Архіву Анни</strong>. Контейнери Архіву Анни (AAC): стандартизація випусків з найбільшої у світі тіньової бібліотеки Архів Анни став найбільшою тіньовою бібліотекою у світі, що вимагає від нас стандартизації наших випусків. Випущено понад 300 ГБ обкладинок книг Нарешті, ми раді оголосити про невеликий випуск. У співпраці з людьми, які керують форком Libgen.rs, ми ділимося всіма їхніми обкладинками книг через торренти та IPFS. Це розподілить навантаження на перегляд обкладинок серед більшої кількості машин і краще їх зберігатиме. У багатьох (але не у всіх) випадках обкладинки книг включені в самі файли, тому це свого роду «похідні дані». Але мати їх в IPFS все ще дуже корисно для щоденної роботи як Архіву Анни, так і різних форків Library Genesis. Як завжди, ви можете знайти цей випуск у Pirate Library Mirror (РЕДАГУВАНО: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>). Ми не будемо посилатися на нього тут, але ви можете легко його знайти. Сподіваємося, що ми зможемо трохи знизити темп, тепер, коли у нас є гідна альтернатива Z-Library. Це навантаження не є особливо стійким. Якщо ви зацікавлені в допомозі з програмуванням, обслуговуванням серверів або роботою збереження, обов'язково зв'яжіться з нами. Ще багато <a %(annas_archive)s>роботи</a> попереду. Дякуємо за ваш інтерес та підтримку. Перехід на ElasticSearch Деякі запити займали дуже багато часу, до того моменту, коли вони захоплювали всі відкриті з'єднання. За замовчуванням MySQL має мінімальну довжину слова, або ваш індекс може стати дуже великим. Люди повідомляли, що не можуть шукати «Ben Hur». Пошук був лише дещо швидким, коли повністю завантажувався в пам'ять, що вимагало від нас отримати дорожчу машину для запуску цього, плюс деякі команди для попереднього завантаження індексу при запуску. Ми не змогли б легко розширити його для створення нових функцій, таких як краща <a %(wikipedia_cjk_characters)s>токенізація для мов без пробілів</a>, фільтрація/фасетування, сортування, пропозиції «чи ви мали на увазі», автозаповнення тощо. Один з наших <a %(annas_archive)s>квитків</a> був збіркою проблем з нашою системою пошуку. Ми використовували MySQL full-text search, оскільки всі наші дані були в MySQL. Але у нього були свої обмеження: Після розмови з багатьма експертами ми зупинилися на ElasticSearch. Це не було ідеально (їхні стандартні пропозиції «чи ви мали на увазі» та функції автозаповнення не дуже), але загалом це було набагато краще, ніж MySQL для пошуку. Ми все ще не <a %(youtube)s>надто захоплені</a> використанням його для будь-яких критично важливих даних (хоча вони зробили багато <a %(elastic_co)s>прогресу</a>), але загалом ми дуже задоволені переходом. На даний момент ми реалізували набагато швидший пошук, кращу підтримку мов, краще сортування за релевантністю, різні варіанти сортування та фільтрацію за мовою/типом книги/типом файлу. Якщо вам цікаво, як це працює, <a %(annas_archive_l140)s>подивіться</a> <a %(annas_archive_l1115)s>на</a> <a %(annas_archive_l1635)s>це</a>. Це досить доступно, хоча не завадило б більше коментарів… Архів Анни є повністю відкритим джерелом Ми вважаємо, що інформація повинна бути вільною, і наш власний код не є винятком. Ми випустили весь наш код на нашому приватно розміщеному екземплярі Gitlab: <a %(annas_archive)s>Програмне забезпечення Анни</a>. Ми також використовуємо трекер проблем для організації нашої роботи. Якщо ви хочете долучитися до нашої розробки, це чудове місце для початку. Щоб дати вам уявлення про те, над чим ми працюємо, візьміть нашу нещодавню роботу над покращенням продуктивності на стороні клієнта. Оскільки ми ще не реалізували пагінацію, ми часто повертали дуже довгі сторінки пошуку з 100-200 результатами. Ми не хотіли занадто рано обрізати результати пошуку, але це означало, що це уповільнювало деякі пристрої. Для цього ми реалізували невеликий трюк: ми обгорнули більшість результатів пошуку в HTML-коментарі (<code><!-- --></code>), а потім написали невеликий Javascript, який виявляв, коли результат повинен стати видимим, у цей момент ми розгортали коментар: DOM «віртуалізація» реалізована в 23 рядках, без потреби у складних бібліотеках! Це той тип швидкого прагматичного коду, який ви отримуєте, коли маєте обмежений час і реальні проблеми, які потрібно вирішити. Повідомляється, що наш пошук тепер добре працює на повільних пристроях! Ще одним великим зусиллям було автоматизувати створення бази даних. Коли ми запускалися, ми просто безладно збирали різні джерела разом. Тепер ми хочемо тримати їх оновленими, тому ми написали кілька скриптів для завантаження нових metadata з двох форків Library Genesis і інтегруємо їх. Мета полягає не лише в тому, щоб зробити це корисним для нашого архіву, але й полегшити роботу для всіх, хто хоче пограти з metadata тіньової бібліотеки. Метою буде Jupyter notebook, який має всілякі цікаві metadata, щоб ми могли проводити більше досліджень, наприклад, з'ясувати, який <a %(blog)s>відсоток ISBN зберігається назавжди</a>. Нарешті, ми оновили нашу систему пожертвувань. Тепер ви можете використовувати кредитну картку, щоб безпосередньо вносити гроші на наші криптогаманці, не потребуючи знань про криптовалюти. Ми будемо продовжувати стежити за тим, наскільки добре це працює на практиці, але це велика справа. З падінням Z-Library та арештом його (ймовірних) засновників, ми працюємо цілодобово, щоб надати гарну альтернативу з Архівом Анни (ми не будемо посилатися на нього тут, але ви можете знайти його в Google). Ось деякі з досягнень, яких ми досягли нещодавно. Оновлення Анни: повністю відкрите джерело архіву, ElasticSearch, понад 300 ГБ обкладинок книг Ми працюємо цілодобово, щоб надати гарну альтернативу з Архівом Анни. Ось деякі з досягнень, яких ми досягли нещодавно. Аналіз Семантичні дублікати (різні скани однієї і тієї ж книги) теоретично можна відфільтрувати, але це складно. При ручному перегляді коміксів ми знайшли занадто багато хибних спрацьовувань. Є деякі дублікати лише за MD5, що є відносно марнотратним, але їхнє фільтрування дало б нам лише близько 1% in економії. У такому масштабі це все ще близько 1 ТБ, але також у такому масштабі 1 ТБ не має великого значення. Ми б не хотіли ризикувати випадковим знищенням даних у цьому процесі. Ми знайшли купу даних, які не є книгами, таких як фільми на основі коміксів. Це також здається марнотратним, оскільки вони вже широко доступні іншими способами. Однак ми зрозуміли, що не можемо просто відфільтрувати файли фільмів, оскільки є також <em>інтерактивні комікси</em>, які були випущені на комп’ютері, які хтось записав і зберіг як фільми. Зрештою, все, що ми могли б видалити з колекції, зекономило б лише кілька відсотків. Тоді ми згадали, що ми — зберігачі даних, і люди, які будуть дзеркалити це, також зберігачі даних, і тому: «ЩО ВИ МАЄТЕ НА УВАЗІ, ВИДАЛИТИ?!» :) Коли ви отримуєте 95 ТБ у ваш кластер зберігання, ви намагаєтеся зрозуміти, що там взагалі є… Ми провели деякий аналіз, щоб побачити, чи можемо ми трохи зменшити розмір, наприклад, видаливши дублікати. Ось деякі з наших висновків: Тому ми представляємо вам повну, немодифіковану колекцію. Це багато даних, але ми сподіваємося, що достатньо людей захоче їх розповсюджувати. Співпраця З огляду на її розмір, ця колекція давно була в нашому списку бажань, тому після нашого успіху з резервним копіюванням Z-Library ми націлилися на цю колекцію. Спочатку ми знімали її безпосередньо, що було досить складно, оскільки їхній сервер був не в найкращому стані. Таким чином ми отримали близько 15 ТБ, але це було повільно. На щастя, нам вдалося зв’язатися з оператором бібліотеки, який погодився надіслати нам усі дані безпосередньо, що було набагато швидше. Все одно знадобилося більше півроку, щоб передати та обробити всі дані, і ми майже втратили їх через пошкодження диска, що означало б початок з нуля. Цей досвід змусив нас повірити, що важливо якомога швидше поширити ці дані, щоб їх можна було дзеркалювати широко. Ми лише за один-два невдало вчасних інциденти від втрати цієї колекції назавжди! Колекція Швидкий рух означає, що колекція трохи неорганізована… Давайте подивимося. Уявіть, що у нас є файлова система (яку насправді ми розділяємо на торренти): Перша директорія, <code>/repository</code>, є більш структурованою частиною цього. Ця директорія містить так звані «тисячні каталоги»: каталоги, кожен з яких містить тисячу файлів, які поступово нумеруються в базі даних. Директорія <code>0</code> містить файли з comic_id від 0 до 999 і так далі. Це та ж схема, яку Library Genesis використовує для своїх колекцій художньої та нехудожньої літератури. Ідея полягає в тому, що кожен «тисячний каталог» автоматично перетворюється на торрент, як тільки він заповнюється. Однак оператор Libgen.li ніколи не створював торренти для цієї колекції, тому тисячні каталоги, ймовірно, стали незручними і поступилися місцем «несортованим каталогам». Це <code>/comics0</code> до <code>/comics4</code>. Вони всі містять унікальні структури каталогів, які, ймовірно, мали сенс для збору файлів, але зараз нам не дуже зрозумілі. На щастя, metadata все ще безпосередньо посилається на всі ці файли, тому їхня організація зберігання на диску насправді не має значення! Metadata доступна у формі бази даних MySQL. Її можна завантажити безпосередньо з вебсайту Libgen.li, але ми також зробимо її доступною в торренті разом з нашою власною таблицею з усіма хешами MD5. <q>Доктор Барбара Гордон намагається загубитися у буденному світі бібліотеки…</q> Форки Libgen Спочатку трохи передісторії. Ви, можливо, знаєте Library Genesis за їхню епічну колекцію книг. Менше людей знають, що волонтери Library Genesis створили інші проєкти, такі як значна колекція журналів і стандартних документів, повна резервна копія Sci-Hub (у співпраці з засновницею Sci-Hub, Олександрою Елбакян), і, дійсно, величезна колекція коміксів. У певний момент різні оператори дзеркал Library Genesis пішли своїми шляхами, що призвело до поточної ситуації з кількома різними «форками», які все ще носять назву Library Genesis. Форк Libgen.li унікально має цю колекцію коміксів, а також значну колекцію журналів (над якою ми також працюємо). Збір коштів Ми випускаємо ці дані у великих частинах. Перший торрент — це <code>/comics0</code>, який ми зібрали в один величезний файл .tar обсягом 12 ТБ. Це краще для вашого жорсткого диска та програмного забезпечення для торентів, ніж безліч менших файлів. У рамках цього випуску ми проводимо збір коштів. Ми прагнемо зібрати 20 000 доларів, щоб покрити операційні та контрактні витрати на цю колекцію, а також забезпечити поточні та майбутні проекти. У нас є кілька <em>масштабних</em> проектів у розробці. <em>Кого я підтримую своїм пожертвуванням?</em> Коротко: ми зберігаємо всі знання та культуру людства і робимо їх легко доступними. Весь наш код і дані є відкритими, ми повністю волонтерський проект, і ми вже зберегли 125 ТБ книг (на додаток до існуючих торентів Libgen та Scihub). Зрештою, ми створюємо маховик, який дозволяє та стимулює людей знаходити, сканувати та зберігати всі книги у світі. Ми напишемо про наш генеральний план у майбутньому пості. :) Якщо ви пожертвуєте на 12-місячне членство “Amazing Archivist” ($780), ви зможете <strong>“усиновити торрент”</strong>, тобто ми додамо ваше ім’я користувача або повідомлення у назву одного з торентів! Ви можете пожертвувати, перейшовши на <a %(wikipedia_annas_archive)s>Архів Анни</a> і натиснувши кнопку «Пожертвувати». Ми також шукаємо більше волонтерів: програмістів, дослідників безпеки, експертів з анонімної торгівлі та перекладачів. Ви також можете підтримати нас, надаючи послуги хостингу. І, звичайно, будь ласка, розповсюджуйте наші торренти! Дякуємо всім, хто вже так щедро нас підтримав! Ви дійсно робите різницю. Ось торренти, які вже випущені (ми ще обробляємо решту): Усі торренти можна знайти на <a %(wikipedia_annas_archive)s>Архів Анни</a> у розділі «Datasets» (ми не посилаємося туди безпосередньо, щоб посилання на цей блог не видалялися з Reddit, Twitter тощо). Звідти перейдіть за посиланням на сайт Tor. <a %(news_ycombinator)s>Обговорити на Hacker News</a> Що далі? Купа торентів чудово підходить для довгострокового зберігання, але не так для повсякденного доступу. Ми будемо працювати з партнерами з хостингу, щоб розмістити всі ці дані в Інтернеті (оскільки Архів Анни нічого не хостить безпосередньо). Звичайно, ви зможете знайти ці посилання для завантаження в Архіві Анни. Ми також запрошуємо всіх працювати з цими даними! Допоможіть нам краще їх аналізувати, видаляти дублікати, розміщувати на IPFS, реміксувати, тренувати ваші моделі ШІ з ними тощо. Вони всі ваші, і ми не можемо дочекатися, щоб побачити, що ви з ними зробите. Нарешті, як вже було сказано, у нас ще є кілька масштабних випусків (якщо <em>хтось</em> міг би <em>випадково</em> надіслати нам дамп <em>певної</em> бази даних ACS4, ви знаєте, де нас знайти…), а також створення маховика для збереження всіх книг у світі. Тож залишайтеся з нами, ми лише починаємо. - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Найбільша тіньова бібліотека коміксів, ймовірно, належить певному форку Library Genesis: Libgen.li. Один адміністратор, який керує цим сайтом, зумів зібрати неймовірну колекцію коміксів з понад 2 мільйонами файлів, загальною вагою понад 95 ТБ. Однак, на відміну від інших колекцій Library Genesis, ця не була доступна в масовому порядку через торренти. Ви могли отримати доступ до цих коміксів лише індивідуально через його повільний особистий сервер — одну точку відмови. До сьогодні! У цій публікації ми розповімо вам більше про цю колекцію та про наш збір коштів для підтримки подальшої роботи. Архів Анни зберіг найбільшу у світі тіньову бібліотеку коміксів (95 ТБ) — ви можете допомогти її розповсюдити Найбільша у світі тіньова бібліотека коміксів мала одну точку відмови... до сьогодні. Попередження: цей блог-пост застарів. Ми вирішили, що IPFS ще не готовий для широкого використання. Ми все ще будемо посилатися на файли на IPFS з Архіву Анни, коли це можливо, але ми більше не будемо їх хостити самі, і не рекомендуємо іншим дзеркалити за допомогою IPFS. Будь ласка, перегляньте нашу сторінку Торентів, якщо ви хочете допомогти зберегти нашу колекцію. Розміщення 5,998,794 книг на IPFS Множення копій Повертаючись до нашого початкового питання: як ми можемо стверджувати, що зберігаємо наші колекції назавжди? Основна проблема тут полягає в тому, що наша колекція <a %(torrents_stats)s>зростає</a> швидкими темпами, завдяки вилученню та відкриттю деяких величезних колекцій (на додаток до дивовижної роботи, вже виконаної іншими бібліотеками тіньових даних, такими як Sci-Hub і Library Genesis). Це зростання даних ускладнює дзеркалізацію колекцій по всьому світу. Зберігання даних дороге! Але ми оптимістичні, особливо спостерігаючи за наступними трьома тенденціями. <a %(annas_archive_stats)s>Загальний обсяг</a> наших колекцій за останні кілька місяців, розбитий за кількістю торрент-сідерів. Тенденції цін на HDD з різних джерел (натисніть, щоб переглянути дослідження). <a %(critical_window_chinese)s>Китайська версія 中文版</a>, обговорення на <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Ми зібрали легкодоступні плоди Це безпосередньо випливає з наших пріоритетів, обговорених вище. Ми віддаємо перевагу роботі над звільненням великих колекцій спочатку. Тепер, коли ми забезпечили деякі з найбільших колекцій у світі, ми очікуємо, що наше зростання буде набагато повільнішим. Існує ще довгий хвіст менших колекцій, і нові книги скануються або публікуються щодня, але швидкість, ймовірно, буде набагато повільнішою. Ми можемо ще подвоїтися або навіть потроїтися в розмірах, але протягом тривалішого періоду часу. Покращення OCR. Пріоритети Програмний код для науки та інженерії Художні або розважальні версії всього вищезазначеного Географічні дані (наприклад, карти, геологічні дослідження) Внутрішні дані від корпорацій або урядів (витоки) Дані вимірювань, такі як наукові вимірювання, економічні дані, корпоративні звіти Записи metadata загалом (як нехудожні, так і художні; інших медіа, мистецтва, людей тощо; включаючи рецензії) Нон-фікшн книги Нехудожні журнали, газети, інструкції Нехудожні стенограми виступів, документальні фільми, подкасти Органічні дані, такі як послідовності ДНК, насіння рослин або мікробні зразки Академічні статті, журнали, звіти Вебсайти з науки та інженерії, онлайн-дискусії Стенограми юридичних або судових процесів Унікально під загрозою знищення (наприклад, через війну, скорочення фінансування, судові позови або політичні переслідування) Рідкісними Унікально недооціненими Чому ми так піклуємося про статті та книги? Відкладемо вбік наше фундаментальне переконання в збереженні в цілому — ми можемо написати про це інший пост. Отже, чому саме статті та книги? Відповідь проста: <strong>щільність інформації</strong>. На мегабайт зберігання, письмовий текст зберігає найбільше інформації з усіх медіа. Хоча ми піклуємося про знання та культуру, ми більше піклуємося про перше. Загалом, ми знаходимо ієрархію щільності інформації та важливості збереження, яка виглядає приблизно так: Рейтинг у цьому списку є дещо довільним — кілька пунктів є рівнозначними або викликають розбіжності в нашій команді — і ми, ймовірно, забуваємо про деякі важливі категорії. Але приблизно так ми визначаємо пріоритети. Деякі з цих пунктів занадто відрізняються від інших, щоб ми турбувалися про них (або вже опікуються іншими установами), такі як органічні дані або географічні дані. Але більшість пунктів у цьому списку насправді важливі для нас. Ще одним важливим фактором у нашій пріоритизації є те, наскільки ризикованим є певний твір. Ми віддаємо перевагу зосередженню на творах, які є: Нарешті, ми дбаємо про масштаб. У нас обмежений час і гроші, тому ми краще витратимо місяць на збереження 10 000 книг, ніж 1 000 книг — якщо вони приблизно однаково цінні та під загрозою. <em><q>Втрачено не можна відновити; але давайте збережемо те, що залишилося: не за допомогою сховищ і замків, які захищають їх від очей і використання громадськості, віддаючи їх на знищення часу, а шляхом такого множення копій, яке поставить їх поза досяжністю випадковостей.</q></em><br>— Томас Джефферсон, 1791 Тіньові бібліотеки Код може бути з відкритим вихідним кодом на Github, але Github в цілому не може бути легко дзеркалізований і, таким чином, збережений (хоча в цьому конкретному випадку існує достатньо розповсюджених копій більшості репозиторіїв коду) Записи metadata можна вільно переглядати на вебсайті Worldcat, але не можна завантажити оптом (поки ми їх не <a %(worldcat_scrape)s>вилучили</a>) Reddit безкоштовний у використанні, але нещодавно запровадив суворі заходи проти вилучення даних, у зв'язку з тренуванням LLM, що потребує багато даних (докладніше про це пізніше) Існує багато організацій, які мають схожі місії та пріоритети. Дійсно, є бібліотеки, архіви, лабораторії, музеї та інші установи, які займаються збереженням такого роду. Багато з них добре фінансуються урядами, приватними особами або корпораціями. Але у них є одна величезна сліпа зона: правова система. У цьому полягає унікальна роль тіньових бібліотек і причина існування Архіву Анни. Ми можемо робити те, що іншим установам не дозволено. Ні, це не (часто) означає, що ми можемо архівувати матеріали, які незаконно зберігати в інших місцях. Ні, у багатьох місцях законно створювати архів з будь-якими книгами, статтями, журналами тощо. Але чого часто бракує юридичним архівам, так це <strong>резервування та довговічності</strong>. Існують книги, з яких є лише один екземпляр у якійсь фізичній бібліотеці. Існують записи metadata, які охороняє лише одна корпорація. Існують газети, збережені лише на мікрофільмах в одному архіві. Бібліотеки можуть зазнавати скорочення фінансування, корпорації можуть збанкрутувати, архіви можуть бути знищені бомбами та спалені дощенту. Це не гіпотеза — це відбувається постійно. Те, що ми можемо унікально робити в Архіві Анни, — це зберігати багато копій творів у великому масштабі. Ми можемо збирати статті, книги, журнали та інше, і розповсюджувати їх оптом. Наразі ми робимо це через торренти, але точні технології не мають значення і змінюватимуться з часом. Важливо, щоб багато копій було розповсюджено по всьому світу. Ця цитата з понад 200 років тому досі залишається актуальною: Коротка примітка про суспільне надбання. Оскільки Архів Анни унікально зосереджується на діяльності, яка є незаконною в багатьох місцях світу, ми не переймаємося широко доступними колекціями, такими як книги суспільного надбання. Юридичні організації часто вже добре дбають про це. Однак є міркування, які іноді змушують нас працювати над загальнодоступними колекціями: - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Витрати на зберігання продовжують експоненційно знижуватися 3. Покращення щільності інформації Наразі ми зберігаємо книги у тих форматах, в яких вони нам надаються. Звісно, вони стиснуті, але часто це все ще великі скани або фотографії сторінок. До цього часу єдиними варіантами зменшення загального розміру нашої колекції були більш агресивне стиснення або дедуплікація. Однак, щоб досягти значних заощаджень, обидва методи занадто втрачають якість для нас. Сильне стиснення фотографій може зробити текст ледве читабельним. А дедуплікація вимагає високої впевненості в тому, що книги є точно однаковими, що часто є занадто неточним, особливо якщо вміст однаковий, але скани зроблені в різний час. Завжди був третій варіант, але його якість була настільки жахливою, що ми ніколи не розглядали його: <strong>OCR, або оптичне розпізнавання символів</strong>. Це процес перетворення фотографій у звичайний текст за допомогою штучного інтелекту для розпізнавання символів на фотографіях. Інструменти для цього існують вже давно і є досить пристойними, але «досить пристойними» недостатньо для цілей збереження. Однак, останні мультимодальні моделі глибокого навчання зробили надзвичайно швидкий прогрес, хоча все ще за високих витрат. Ми очікуємо, що точність і витрати значно покращаться в найближчі роки, до того моменту, коли це стане реалістичним для застосування до всієї нашої бібліотеки. Коли це станеться, ми, ймовірно, все ще зберігатимемо оригінальні файли, але додатково ми могли б мати набагато меншу версію нашої бібліотеки, яку більшість людей захоче дзеркалити. Родзинка в тому, що сам сирий текст стискається ще краще і його набагато легше дедуплікувати, що дає нам ще більше заощаджень. Загалом, не є нереалістичним очікувати принаймні 5-10-кратного зменшення загального розміру файлів, можливо, навіть більше. Навіть при консервативному 5-кратному зменшенні, ми б розглядали <strong>$1,000–$3,000 за 10 років, навіть якщо наша бібліотека збільшиться втричі</strong>. На момент написання <a %(diskprices)s>ціни на диски</a> за терабайт становлять близько 12 доларів за нові диски, 8 доларів за вживані диски та 4 долари за стрічку. Якщо ми будемо консервативними і розглянемо лише нові диски, це означає, що зберігання петабайта коштує близько 12 000 доларів. Якщо ми припустимо, що наша бібліотека потроїться з 900 ТБ до 2,7 ПБ, це означатиме 32 400 доларів для дзеркалізації всієї нашої бібліотеки. Додаючи електроенергію, вартість іншого обладнання тощо, округлимо до 40 000 доларів. Або зі стрічкою більше як 15 000–20 000 доларів. З одного боку, <strong>15 000–40 000 доларів за суму всіх людських знань — це вигідна угода</strong>. З іншого боку, це трохи круто очікувати тонни повних копій, особливо якщо ми також хочемо, щоб ці люди продовжували роздавати свої торренти на користь інших. Це сьогодні. Але прогрес рухається вперед: Вартість жорстких дисків за терабайт приблизно зменшилася втричі за останні 10 років і, ймовірно, продовжить знижуватися в такому ж темпі. Стрічка, здається, йде по схожій траєкторії. Ціни на SSD падають ще швидше і можуть перевершити ціни на HDD до кінця десятиліття. Якщо це збережеться, то через 10 років ми можемо розглядати лише 5 000–13 000 доларів для дзеркалізації всієї нашої колекції (1/3), або навіть менше, якщо ми зростемо менше в розмірах. Хоча це все ще багато грошей, це буде досяжно для багатьох людей. І це може бути ще краще завдяки наступному пункту… В Архіві Анни нас часто запитують, як ми можемо стверджувати, що зберігаємо наші колекції назавжди, коли загальний обсяг вже наближається до 1 Петабайта (1000 ТБ) і продовжує зростати. У цій статті ми розглянемо нашу філософію і побачимо, чому наступне десятиліття є критичним для нашої місії збереження знань і культури людства. Критичне вікно Якщо ці прогнози точні, нам <strong>потрібно лише почекати кілька років</strong>, перш ніж вся наша колекція буде широко дзеркалена. Таким чином, за словами Томаса Джефферсона, «поміщена поза досяжністю випадковості». На жаль, поява LLM та їхнє навчання, яке потребує багато даних, змусила багатьох правовласників зайняти оборонну позицію. Ще більше, ніж вони вже були. Багато вебсайтів ускладнюють скрапінг та архівування, судові позови літають навколо, і в той же час фізичні бібліотеки та архіви продовжують бути занедбаними. Ми можемо лише очікувати, що ці тенденції продовжать погіршуватися, і багато робіт буде втрачено задовго до того, як вони увійдуть у суспільне надбання. <strong>Ми на порозі революції у збереженні, але <q>втрачене не можна відновити.</q></strong> У нас є критичне вікно приблизно 5-10 років, протягом якого все ще досить дорого утримувати тіньову бібліотеку та створювати багато дзеркал по всьому світу, і протягом якого доступ ще не був повністю закритий. Якщо ми зможемо подолати це вікно, то дійсно збережемо знання та культуру людства назавжди. Ми не повинні дозволити цьому часу бути змарнованим. Ми не повинні дозволити цьому критичному вікну закритися для нас. Вперед. Критичне вікно тіньових бібліотек Як ми можемо стверджувати, що зберігаємо наші колекції назавжди, коли вони вже наближаються до 1 ПБ? Колекція Деяка додаткова інформація про колекцію. <a %(duxiu)s>Duxiu</a> — це величезна база даних відсканованих книг, створена <a %(chaoxing)s>SuperStar Digital Library Group</a>. Більшість з них — академічні книги, відскановані для того, щоб зробити їх доступними в цифровому вигляді для університетів та бібліотек. Для нашої англомовної аудиторії <a %(library_princeton)s>Прінстон</a> та <a %(guides_lib_uw)s>Університет Вашингтона</a> мають хороші огляди. Також є чудова стаття, що надає більше інформації: <a %(doi)s>“Оцифровка китайських книг: дослідження пошукової системи SuperStar DuXiu Scholar”</a> (знайдіть її в Архіві Анни). Книги з Duxiu давно піратські на китайському інтернеті. Зазвичай їх продають за менше ніж долар перепродавці. Вони зазвичай розповсюджуються за допомогою китайського аналога Google Drive, який часто зламують, щоб дозволити більше місця для зберігання. Деякі технічні деталі можна знайти <a %(github_duty_machine)s>тут</a> і <a %(github_821_github_io)s>тут</a>. Хоча книги були напівпублічно розповсюджені, їх досить важко отримати в обсязі. Ми мали це високо в нашому списку справ, і виділили кілька місяців повноцінної роботи для цього. Однак нещодавно до нас звернувся неймовірний, дивовижний і талановитий волонтер, повідомивши, що вони вже виконали всю цю роботу — за великі витрати. Вони поділилися з нами повною колекцією, не очікуючи нічого взамін, окрім гарантії довгострокового збереження. Справді вражаюче. Вони погодилися попросити допомоги таким чином, щоб отримати колекцію OCR. Колекція містить 7,543,702 файлів. Це більше, ніж Library Genesis нон-фікшн (близько 5,3 мільйона). Загальний розмір файлів становить близько 359 ТБ (326 ТіБ) у його поточній формі. Ми відкриті для інших пропозицій та ідей. Просто зв'яжіться з нами. Перегляньте Архів Анни для отримання додаткової інформації про наші колекції, зусилля щодо збереження та як ви можете допомогти. Дякуємо! Приклад сторінок Щоб довести нам, що у вас є хороший конвеєр, ось кілька прикладів сторінок для початку, з книги про надпровідники. Ваш конвеєр повинен правильно обробляти математику, таблиці, графіки, примітки тощо. Надішліть оброблені сторінки на нашу електронну пошту. Якщо вони виглядають добре, ми надішлемо вам більше в приватному порядку, і ми очікуємо, що ви зможете швидко запустити свій конвеєр на них також. Коли ми будемо задоволені, ми зможемо укласти угоду. - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Китайська версія 中文版</a>, <a %(news_ycombinator)s>Обговорити на Hacker News</a> Це короткий блог-пост. Ми шукаємо компанію або установу, яка допоможе нам з OCR та вилученням тексту для величезної колекції, яку ми придбали, в обмін на ексклюзивний ранній доступ. Після періоду ембарго ми, звісно, випустимо всю колекцію. Високоякісні академічні тексти надзвичайно корисні для навчання LLM. Хоча наша колекція китайська, це може бути корисним навіть для навчання англійських LLM: моделі, здається, кодують концепції та знання незалежно від мови джерела. Для цього текст потрібно витягти зі сканів. Що отримує з цього Архів Анни? Повнотекстовий пошук книг для своїх користувачів. Оскільки наші цілі збігаються з цілями розробників LLM, ми шукаємо співпрацівника. Ми готові надати вам <strong>ексклюзивний ранній доступ до цієї колекції в обсязі на 1 рік</strong>, якщо ви зможете виконати належне OCR та витяг тексту. Якщо ви готові поділитися з нами всім кодом вашого конвеєра, ми готові продовжити ембарго на колекцію. Ексклюзивний доступ для компаній LLM до найбільшої у світі колекції китайських нон-фікшн книг <em><strong>Коротко:</strong> Архів Анни придбав унікальну колекцію з 7,5 мільйонів / 350 ТБ китайських нон-фікшн книг — більшу, ніж Library Genesis. Ми готові надати компанії LLM ексклюзивний доступ в обмін на високоякісний OCR та вилучення тексту.</em> Архітектура системи Отже, припустимо, ви знайшли кілька компаній, які готові розмістити ваш сайт, не закриваючи його — назвемо їх “провайдерами, що люблять свободу” 😄. Ви швидко виявите, що розміщення всього у них досить дороге, тому ви можете захотіти знайти “дешевих провайдерів” і фактично розміщувати там, проксіруючи через провайдерів, що люблять свободу. Якщо ви зробите це правильно, дешеві провайдери ніколи не дізнаються, що ви розміщуєте, і ніколи не отримають жодних скарг. З усіма цими провайдерами існує ризик, що вони все одно закриють вас, тому вам також потрібна надмірність. Нам це потрібно на всіх рівнях нашого стеку. Одна з компаній, що дещо любить свободу, яка поставила себе в цікаве становище, — це Cloudflare. Вони <a %(blog_cloudflare)s>стверджували</a>, що вони не є хостинг-провайдером, а утилітою, як ISP. Тому вони не підлягають DMCA або іншим запитам на видалення, і пересилають будь-які запити вашому фактичному хостинг-провайдеру. Вони навіть пішли до суду, щоб захистити цю структуру. Тому ми можемо використовувати їх як ще один шар кешування та захисту. Cloudflare не приймає анонімні платежі, тому ми можемо використовувати лише їх безкоштовний план. Це означає, що ми не можемо використовувати їх функції балансування навантаження або резервування. Тому ми <a %(annas_archive_l255)s>реалізували це самі</a> на рівні домену. При завантаженні сторінки браузер перевіряє, чи доступний поточний домен, і якщо ні, переписує всі URL-адреси на інший домен. Оскільки Cloudflare кешує багато сторінок, це означає, що користувач може потрапити на наш основний домен, навіть якщо проксі-сервер не працює, а потім при наступному кліку перейти на інший домен. Ми також маємо справлятися з нормальними операційними питаннями, такими як моніторинг стану серверів, ведення журналів помилок бекенду та фронтенду тощо. Наша архітектура резервування дозволяє більшої надійності в цьому відношенні, наприклад, шляхом запуску повністю іншого набору серверів на одному з доменів. Ми навіть можемо запускати старі версії коду та наборів даних на цьому окремому домені, у випадку, якщо критична помилка в основній версії залишиться непоміченою. Ми також можемо захиститися від того, що Cloudflare може обернутися проти нас, видаливши його з одного з доменів, наприклад, з цього окремого домену. Можливі різні комбінації цих ідей. Висновок Це був цікавий досвід навчитися налаштовувати надійний і стійкий пошуковий механізм тіньової бібліотеки. Є ще багато деталей, якими можна поділитися в наступних публікаціях, тому дайте знати, про що ви хотіли б дізнатися більше! Як завжди, ми шукаємо пожертвування для підтримки цієї роботи, тому обов’язково відвідайте сторінку Пожертвувань на Архіві Анни. Ми також шукаємо інші види підтримки, такі як гранти, довгострокові спонсори, провайдери високоризикових платежів, можливо, навіть (зі смаком!) рекламу. І якщо ви хочете внести свій час і навички, ми завжди шукаємо розробників, перекладачів тощо. Дякуємо за ваш інтерес і підтримку. Інноваційні токени Почнемо з нашого технічного стеку. Він навмисно нудний. Ми використовуємо Flask, MariaDB та ElasticSearch. І це буквально все. Пошук в основному є вирішеною проблемою, і ми не маємо наміру його винаходити заново. Крім того, ми повинні витратити наші <a %(mcfunley)s>інноваційні токени</a> на щось інше: не бути закритими владою. Отже, наскільки легальний чи нелегальний є Архів Анни? Це здебільшого залежить від правової юрисдикції. Більшість країн вірять у певну форму авторського права, що означає, що людям або компаніям надається виключна монополія на певні види робіт на певний період часу. До речі, в Архіві Анни ми вважаємо, що, хоча є деякі переваги, загалом авторське право є негативним для суспільства — але це історія для іншого разу. Ця виключна монополія на певні роботи означає, що незаконно для будь-кого поза цією монополією безпосередньо розповсюджувати ці роботи — включаючи нас. Але Архів Анни є пошуковою системою, яка не розповсюджує ці роботи безпосередньо (принаймні не на нашому сайті в звичайному інтернеті), тож ми повинні бути в порядку, так? Не зовсім. У багатьох юрисдикціях незаконно не лише розповсюджувати захищені авторським правом роботи, але й посилатися на місця, де це роблять. Класичним прикладом цього є закон DMCA у Сполучених Штатах. Це найсуворіший кінець спектру. На іншому кінці спектру теоретично можуть бути країни без жодних законів про авторське право, але такі насправді не існують. Практично кожна країна має певну форму закону про авторське право. Виконання — це інша історія. Є багато країн з урядами, які не дбають про виконання закону про авторське право. Є також країни між двома крайнощами, які забороняють розповсюдження захищених авторським правом робіт, але не забороняють посилання на такі роботи. Ще один аспект — на рівні компанії. Якщо компанія працює в юрисдикції, яка не дбає про авторське право, але сама компанія не готова ризикувати, то вони можуть закрити ваш сайт, як тільки хтось поскаржиться на нього. Нарешті, велике питання — це платежі. Оскільки ми повинні залишатися анонімними, ми не можемо використовувати традиційні методи оплати. Це залишає нам криптовалюти, і лише невелика частина компаній підтримує їх (існують віртуальні дебетові картки, оплачені криптовалютою, але їх часто не приймають). - Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Я керую <a %(wikipedia_annas_archive)s>Архівом Анни</a>, найбільшою у світі відкритою некомерційною пошуковою системою для <a %(wikipedia_shadow_library)s>тіньових бібліотек</a>, таких як Sci-Hub, Library Genesis та Z-Library. Наша мета — зробити знання та культуру легко доступними, і в кінцевому підсумку створити спільноту людей, які разом архівують та зберігають <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>всі книги у світі</a>. У цій статті я покажу, як ми керуємо цим вебсайтом, і унікальні виклики, які виникають при управлінні вебсайтом з сумнівним правовим статусом, оскільки немає “AWS для тіньових благодійностей”. <em>Також перегляньте сестринську статтю <a %(blog_how_to_become_a_pirate_archivist)s>Як стати піратським архівістом</a>.</em> Як керувати тіньовою бібліотекою: операції в Архіві Анни Немає <q>AWS для тіньових благодійностей,</q> тож як ми керуємо Архівом Анни? Інструменти Сервер додатків: Flask, MariaDB, ElasticSearch, Docker. Розробка: Gitlab, Weblate, Zulip. Управління серверами: Ansible, Checkmk, UFW. Статичний хостинг Onion: Tor, Nginx. Проксі-сервер: Varnish. Давайте подивимося, які інструменти ми використовуємо для досягнення всього цього. Це дуже змінюється, оскільки ми стикаємося з новими проблемами та знаходимо нові рішення. Є деякі рішення, які ми переглядали кілька разів. Одне з них — це комунікація між серверами: раніше ми використовували для цього Wireguard, але виявили, що він іноді перестає передавати будь-які дані або передає дані лише в одному напрямку. Це траплялося з кількома різними налаштуваннями Wireguard, які ми пробували, такими як <a %(github_costela_wesher)s>wesher</a> і <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ми також пробували тунелювати порти через SSH, використовуючи autossh і sshuttle, але зіткнулися з <a %(github_sshuttle)s>проблемами там</a> (хоча мені досі не зрозуміло, чи страждає autossh від проблем TCP-over-TCP чи ні — це просто здається мені ненадійним рішенням, але, можливо, воно насправді добре?). Замість цього ми повернулися до прямих з'єднань між серверами, приховуючи, що сервер працює на дешевих провайдерах, використовуючи фільтрацію IP з UFW. Це має недолік, що Docker не працює добре з UFW, якщо ви не використовуєте <code>network_mode: "host"</code>. Все це трохи більш схильне до помилок, тому що ви можете виставити свій сервер в інтернет через невелику помилку в конфігурації. Можливо, нам слід повернутися до autossh — зворотний зв'язок тут буде дуже доречним. Ми також переглядали Varnish проти Nginx. Наразі нам подобається Varnish, але він має свої особливості та недоліки. Те ж саме стосується Checkmk: ми не в захваті від нього, але він працює на даний момент. Weblate був непоганим, але не вражаючим — я іноді боюся, що він втратить мої дані, коли я намагаюся синхронізувати його з нашим git-репозиторієм. Flask загалом був хорошим, але має деякі дивні особливості, які коштували багато часу на налагодження, такі як налаштування користувацьких доменів або проблеми з інтеграцією SqlAlchemy. Поки що інші інструменти були чудовими: у нас немає серйозних скарг на MariaDB, ElasticSearch, Gitlab, Zulip, Docker і Tor. Усі вони мали деякі проблеми, але нічого надто серйозного або такого, що забирає багато часу. Спільнота Перша проблема може бути несподіваною. Це не технічна проблема чи юридична проблема. Це психологічна проблема: робота в тіні може бути неймовірно самотньою. Залежно від того, що ви плануєте робити, і вашої моделі загроз, вам, можливо, доведеться бути дуже обережними. На одному кінці спектра ми маємо людей, як Олександра Елбакян*, засновниця Sci-Hub, яка дуже відкрита щодо своєї діяльності. Але вона під великим ризиком бути заарештованою, якщо відвідає західну країну на даний момент, і може зіткнутися з десятиліттями ув'язнення. Чи готові ви взяти на себе такий ризик? Ми знаходимося на іншому кінці спектра; дуже обережні, щоб не залишити жодного сліду, і маємо сильну операційну безпеку. * Як зазначено на HN "ynno", Олександра спочатку не хотіла бути відомою: "Її сервери були налаштовані на видачу детальних повідомлень про помилки з PHP, включаючи повний шлях до файлу з помилкою, який знаходився в каталозі /home/<USER>'язаному сайті, прикріпленому до її справжнього імені. До цього розкриття вона була анонімною." Тому використовуйте випадкові імена користувачів на комп'ютерах, які ви використовуєте для цього, на випадок, якщо ви неправильно налаштуєте щось. Однак ця таємність має психологічну ціну. Більшість людей люблять, коли їх визнають за роботу, яку вони виконують, і все ж ви не можете отримати жодного визнання за це в реальному житті. Навіть прості речі можуть бути складними, наприклад, коли друзі запитують вас, чим ви займалися (в якийсь момент "грався з моїм NAS / домашньою лабораторією" стає старим). Ось чому так важливо знайти якусь спільноту. Ви можете пожертвувати частиною операційної безпеки, довіряючи деяким дуже близьким друзям, яким ви знаєте, що можете глибоко довіряти. Навіть тоді будьте обережні, щоб нічого не записувати, на випадок, якщо їм доведеться передати свої електронні листи владі, або якщо їхні пристрої будуть скомпрометовані якимось іншим чином. Ще краще знайти кількох однодумців-піратів. Якщо ваші близькі друзі зацікавлені приєднатися до вас, чудово! В іншому випадку, ви можете знайти інших в Інтернеті. На жаль, це все ще нішова спільнота. Поки що ми знайшли лише кілька інших, хто активний у цій сфері. Хорошими відправними точками здаються форуми Library Genesis і r/DataHoarder. Команда Archive Team також має однодумців, хоча вони діють у межах закону (навіть якщо в деяких сірих зонах закону). Традиційні сцени "warez" і піратства також мають людей, які думають подібним чином. Ми відкриті до ідей щодо того, як розвивати спільноту та досліджувати ідеї. Не соромтеся писати нам у Twitter або Reddit. Можливо, ми могли б організувати якийсь форум або групу для спілкування. Однією з проблем є те, що це легко може бути піддано цензурі при використанні загальних платформ, тому нам доведеться розміщувати це самостійно. Також є компроміс між тим, щоб ці обговорення були повністю публічними (більше потенційної взаємодії) і тим, щоб зробити їх приватними (не дозволяючи потенційним "цілям" знати, що ми збираємося їх сканувати). Нам доведеться подумати про це. Дайте нам знати, якщо вас це цікавить! Висновок Сподіваємося, це буде корисно для нових піратських архівістів. Ми раді вітати вас у цьому світі, тож не соромтеся звертатися. Давайте збережемо якомога більше знань і культури світу та розповсюдимо їх якомога ширше. Проєкти 4. Вибір даних Часто ви можете використовувати метадані, щоб визначити розумну підмножину даних для завантаження. Навіть якщо ви в кінцевому підсумку хочете завантажити всі дані, може бути корисно спочатку пріоритизувати найважливіші елементи, на випадок, якщо вас виявлять і захисти будуть покращені, або тому, що вам потрібно буде купити більше дисків, або просто тому, що щось інше з’явиться у вашому житті, перш ніж ви зможете завантажити все. Наприклад, колекція може мати кілька видань одного й того ж ресурсу (наприклад, книги чи фільму), де одне позначено як найкраща якість. Збереження цих видань спочатку було б дуже розумним. Зрештою, ви можете захотіти зберегти всі видання, оскільки в деяких випадках метадані можуть бути неправильно позначені, або можуть бути невідомі компроміси між виданнями (наприклад, "найкраще видання" може бути найкращим у більшості аспектів, але гіршим в інших, наприклад, фільм з вищою роздільною здатністю, але без субтитрів). Ви також можете шукати у своїй базі даних метаданих, щоб знайти цікаві речі. Який найбільший файл, що розміщений, і чому він такий великий? Який найменший файл? Чи є цікаві або несподівані закономірності, коли мова йде про певні категорії, мови тощо? Чи є дублікати або дуже схожі назви? Чи є закономірності в тому, коли дані були додані, наприклад, один день, коли багато файлів було додано одночасно? Ви часто можете багато чого дізнатися, дивлячись на набір даних з різних сторін. У нашому випадку ми видалили дублікати книг Z-Library за допомогою хешів md5 у Library Genesis, тим самим заощадивши багато часу на завантаження та місця на диску. Однак це досить унікальна ситуація. У більшості випадків немає всеосяжних баз даних, які файли вже належним чином збережені іншими піратами. Це саме по собі є величезною можливістю для когось. Було б чудово мати регулярно оновлюваний огляд таких речей, як музика та фільми, які вже широко розповсюджені на торрент-сайтах, і тому мають нижчий пріоритет для включення в піратські дзеркала. 6. Розповсюдження У вас є дані, тим самим ви отримали у володіння перше у світі піратське дзеркало вашої цілі (швидше за все). У багатьох відношеннях найважча частина позаду, але найризикованіша частина ще попереду. Зрештою, до цього часу ви були непомітними; літали під радаром. Все, що вам потрібно було зробити, це використовувати хороший VPN протягом усього часу, не заповнювати свої особисті дані в жодних формах (очевидно), і, можливо, використовувати спеціальну сесію браузера (або навіть інший комп’ютер). Тепер ви повинні розповсюдити дані. У нашому випадку ми спочатку хотіли повернути книги до Library Genesis, але швидко виявили труднощі в цьому (сортування художньої та нехудожньої літератури). Тому ми вирішили розповсюджувати за допомогою торрентів у стилі Library Genesis. Якщо у вас є можливість зробити внесок у існуючий проект, це може заощадити вам багато часу. Однак наразі існує небагато добре організованих піратських дзеркал. Отже, припустимо, ви вирішили розповсюджувати торренти самостійно. Намагайтеся зберігати ці файли невеликими, щоб їх було легко дзеркалити на інших вебсайтах. Тоді вам доведеться самостійно роздавати торренти, залишаючись анонімним. Ви можете використовувати VPN (з переадресацією портів або без неї) або платити за Seedbox за допомогою оброблених біткоїнів. Якщо ви не знаєте, що означають деякі з цих термінів, вам доведеться багато читати, оскільки важливо розуміти ризики тут. Ви можете розмістити самі торрент-файли на існуючих торрент-сайтах. У нашому випадку ми вирішили фактично розмістити вебсайт, оскільки ми також хотіли чітко поширити нашу філософію. Ви можете зробити це самостійно подібним чином (ми використовуємо Njalla для наших доменів і хостингу, оплачуючи обробленими біткоїнами), але також не соромтеся зв’язатися з нами, щоб ми розмістили ваші торренти. Ми прагнемо створити всеосяжний індекс піратських дзеркал з часом, якщо ця ідея приживеться. Що стосується вибору VPN, про це вже багато написано, тому ми просто повторимо загальну пораду вибирати за репутацією. Фактичні політики без журналів, перевірені в суді, з тривалим досвідом захисту конфіденційності є найменш ризикованим варіантом, на нашу думку. Зверніть увагу, що навіть якщо ви робите все правильно, ви ніколи не зможете досягти нульового ризику. Наприклад, під час роздачі ваших торрентів, високо мотивований актор держави може, ймовірно, переглядати вхідні та вихідні потоки даних для серверів VPN і визначити, хто ви. Або ви можете просто якось помилитися. Ми, мабуть, вже зробили це і зробимо знову. На щастя, держави не дуже <em>турбуються</em> про піратство. Одне з рішень, яке потрібно прийняти для кожного проекту, — це чи публікувати його під тим самим ім’ям, що й раніше, чи ні. Якщо ви продовжуєте використовувати те саме ім’я, то помилки в операційній безпеці з попередніх проектів можуть повернутися, щоб вас вкусити. Але публікація під різними іменами означає, що ви не створюєте довготривалу репутацію. Ми вирішили мати сильну операційну безпеку з самого початку, щоб ми могли продовжувати використовувати те саме ім’я, але ми не будемо вагатися публікувати під іншим ім’ям, якщо ми помилимося або якщо обставини цього вимагатимуть. Поширення інформації може бути складним. Як ми вже сказали, це все ще нішеве співтовариство. Спочатку ми публікували на Reddit, але насправді отримали популярність на Hacker News. Наразі наша рекомендація — опублікувати це в кількох місцях і подивитися, що станеться. І знову ж таки, зв’яжіться з нами. Ми хотіли б поширити інформацію про більше зусиль піратського архівування. 1. Вибір домену / філософія Немає нестачі знань і культурної спадщини, яку потрібно зберегти, що може бути приголомшливим. Ось чому часто корисно взяти паузу і подумати про те, яким може бути ваш внесок. У кожного є свій спосіб мислення про це, але ось деякі питання, які ви могли б собі поставити: У нашому випадку ми особливо дбали про довгострокове збереження науки. Ми знали про Library Genesis і те, як він був повністю дзеркалений багато разів за допомогою торрентів. Нам сподобалася ця ідея. Потім одного дня хтось із нас спробував знайти деякі наукові підручники на Library Genesis, але не зміг їх знайти, що поставило під сумнів, наскільки він насправді повний. Ми потім шукали ці підручники в Інтернеті і знайшли їх в інших місцях, що посіяло зерно для нашого проєкту. Ще до того, як ми дізналися про Z-Library, у нас була ідея не намагатися зібрати всі ці книги вручну, а зосередитися на дзеркалюванні існуючих колекцій і поверненні їх до Library Genesis. Які навички у вас є, які ви можете використати на свою користь? Наприклад, якщо ви експерт з онлайн-безпеки, ви можете знайти способи обійти блокування IP для захищених цілей. Якщо ви чудово організовуєте спільноти, то, можливо, ви зможете зібрати людей навколо мети. Однак корисно знати трохи програмування, хоча б для підтримки гарної операційної безпеки протягом цього процесу. На чому варто зосередитися, щоб отримати максимальну віддачу? Якщо ви збираєтеся витратити X годин на піратське архівування, то як ви можете отримати найбільшу "віддачу за свої гроші"? Які унікальні способи ви думаєте про це? У вас можуть бути цікаві ідеї або підходи, які інші могли пропустити. Скільки часу у вас є для цього? Наша порада — починати з малого і робити більші проєкти, коли ви освоїтеся, але це може стати всепоглинаючим. Чому вас це цікавить? Що вас захоплює? Якщо ми зможемо зібрати групу людей, які архівують ті речі, які їм особливо важливі, це покриє багато! Ви будете знати набагато більше, ніж середня людина, про вашу пристрасть, наприклад, які дані важливо зберегти, які найкращі колекції та онлайн-спільноти тощо. 3. Скрапінг metadata Дата додавання/зміни: щоб ви могли повернутися пізніше і завантажити файли, які ви не завантажили раніше (хоча ви часто також можете використовувати ID або хеш для цього). Хеш (md5, sha1): щоб підтвердити, що ви правильно завантажили файл. ID: може бути внутрішнім ID, але ID, такі як ISBN або DOI, також корисні. Ім'я файлу / розташування Опис, категорія, теги, автори, мова тощо. Розмір: щоб розрахувати, скільки дискового простору вам потрібно. Давайте трохи заглибимося в технічні деталі. Для фактичного скрапінгу metadata з вебсайтів ми зберегли все досить простим. Ми використовуємо скрипти на Python, іноді curl, і базу даних MySQL для зберігання результатів. Ми не використовували жодного складного програмного забезпечення для скрапінгу, яке може відображати складні вебсайти, оскільки поки що нам потрібно було скрапити лише один або два типи сторінок, просто перераховуючи ідентифікатори та розбираючи HTML. Якщо немає легко перерахованих сторінок, то вам може знадобитися справжній краулер, який намагається знайти всі сторінки. Перш ніж почати скрапити весь вебсайт, спробуйте зробити це вручну трохи. Пройдіть кілька десятків сторінок самостійно, щоб зрозуміти, як це працює. Іноді ви вже таким чином зіткнетеся з блокуванням IP або іншою цікавою поведінкою. Те ж саме стосується скрапінгу даних: перш ніж зануритися в цю ціль, переконайтеся, що ви дійсно можете ефективно завантажити її дані. Щоб обійти обмеження, є кілька речей, які ви можете спробувати. Чи є інші IP-адреси або сервери, які хостять ті ж дані, але не мають таких самих обмежень? Чи є якісь API-ендпоінти, які не мають обмежень, тоді як інші мають? З якою швидкістю завантаження ваш IP блокується і на скільки часу? Або вас не блокують, але знижують швидкість? Що, якщо ви створите обліковий запис користувача, як тоді змінюються речі? Чи можете ви використовувати HTTP/2, щоб зберігати з'єднання відкритими, і чи збільшує це швидкість, з якою ви можете запитувати сторінки? Чи є сторінки, які перераховують кілька файлів одночасно, і чи достатньо інформації, наведеної там? Речі, які ви, ймовірно, захочете зберегти, включають: Зазвичай ми робимо це в два етапи. Спочатку ми завантажуємо сирі HTML-файли, зазвичай безпосередньо в MySQL (щоб уникнути великої кількості малих файлів, про що ми говоримо нижче). Потім, на окремому етапі, ми проходимо ці HTML-файли і розбираємо їх у фактичні таблиці MySQL. Таким чином, вам не потрібно повторно завантажувати все з нуля, якщо ви виявите помилку у вашому коді розбору, оскільки ви можете просто перепроцесувати HTML-файли з новим кодом. Це також часто легше паралелізувати етап обробки, таким чином заощаджуючи час (і ви можете писати код обробки, поки скрапінг виконується, замість того, щоб писати обидва етапи одночасно). Нарешті, зауважте, що для деяких цілей збирання метаданих — це все, що є. Існують величезні колекції метаданих, які не зберігаються належним чином. Назва Вибір домену / філософія: На чому ви приблизно хочете зосередитися і чому? Які ваші унікальні пристрасті, навички та обставини, які ви можете використати на свою користь? Вибір цілі: Яку конкретну колекцію ви будете дзеркалити? Збирання metadata: Каталогізація інформації про файли без фактичного завантаження самих (часто набагато більших) файлів. Вибір даних: На основі metadata звуження, які дані найбільш актуальні для архівування зараз. Це може бути все, але часто є розумний спосіб заощадити місце і пропускну здатність. Збирання даних: Фактичне отримання даних. Розповсюдження: Упаковка в торренти, оголошення десь, залучення людей до поширення. 5. Збирання даних Тепер ви готові фактично завантажити дані оптом. Як згадувалося раніше, на цьому етапі ви вже повинні були вручну завантажити купу файлів, щоб краще зрозуміти поведінку та обмеження цілі. Однак, коли ви фактично почнете завантажувати багато файлів одночасно, на вас все ще чекають сюрпризи. Наша порада тут полягає в тому, щоб зберігати все просто. Почніть із завантаження купи файлів. Ви можете використовувати Python, а потім розширити до кількох потоків. Але іноді навіть простіше — це генерувати файли Bash безпосередньо з бази даних, а потім запускати кілька з них у кількох вікнах терміналу для масштабування. Швидкий технічний трюк, який варто згадати тут, — це використання OUTFILE у MySQL, який ви можете написати будь-де, якщо вимкнете "secure_file_priv" у mysqld.cnf (і обов’язково також вимкніть/перевизначте AppArmor, якщо ви використовуєте Linux). Ми зберігаємо дані на простих жорстких дисках. Почніть з того, що у вас є, і розширюйтеся повільно. Може бути приголомшливо думати про зберігання сотень терабайтів даних. Якщо це ситуація, з якою ви стикаєтеся, просто спочатку викладіть хороший підмножину, а в своєму оголошенні попросіть допомоги у зберіганні решти. Якщо ви хочете отримати більше жорстких дисків самостійно, то r/DataHoarder має кілька хороших ресурсів для отримання вигідних пропозицій. Намагайтеся не надто турбуватися про складні файлові системи. Легко потрапити в кролячу нору налаштування таких речей, як ZFS. Однак одна технічна деталь, про яку слід знати, полягає в тому, що багато файлових систем не справляються з великою кількістю файлів. Ми виявили, що простим обхідним шляхом є створення кількох каталогів, наприклад, для різних діапазонів ідентифікаторів або префіксів хешів. Після завантаження даних обов’язково перевірте цілісність файлів за допомогою хешів у метаданих, якщо вони доступні. 2. Вибір цілі Доступна: не використовує безліч шарів захисту, щоб запобігти скрапінгу їхніх metadata та даних. Особливе розуміння: у вас є якась особлива інформація про цю ціль, наприклад, ви якимось чином маєте спеціальний доступ до цієї колекції, або ви зрозуміли, як обійти їхній захист. Це не є обов'язковим (наш майбутній проєкт не робить нічого особливого), але це, безумовно, допомагає! Велика Отже, ми визначили нашу область, яку ми розглядаємо, тепер яку конкретну колекцію ми будемо дзеркалити? Є кілька речей, які роблять ціль хорошою: Коли ми знайшли наші підручники з науки на вебсайтах, окрім Library Genesis, ми намагалися зрозуміти, як вони потрапили в інтернет. Потім ми знайшли Z-Library і зрозуміли, що хоча більшість книг не з'являються там першими, вони зрештою там опиняються. Ми дізналися про його зв'язок з Library Genesis, а також про (фінансову) структуру стимулів та кращий інтерфейс користувача, які зробили його набагато повнішою колекцією. Потім ми провели попередній скрапінг metadata та даних і зрозуміли, що можемо обійти їхні обмеження на завантаження IP, використовуючи спеціальний доступ одного з наших членів до багатьох проксі-серверів. Під час дослідження різних цілей вже важливо приховувати свої сліди, використовуючи VPN та одноразові електронні адреси, про які ми поговоримо пізніше. Унікальна: не вже добре охоплена іншими проєктами. Коли ми виконуємо проєкт, він має кілька етапів: Це не повністю незалежні етапи, і часто інсайти з пізнішого етапу повертають вас до попереднього етапу. Наприклад, під час збирання metadata ви можете зрозуміти, що обрана вами ціль має захисні механізми, які перевищують ваш рівень навичок (наприклад, блокування IP), тому ви повертаєтеся і знаходите іншу ціль. - Анна та команда (<a %(reddit)s>Reddit</a>) Цілі книги можуть бути написані про <em>чому</em> цифрове збереження загалом і піратське архівування зокрема, але давайте дамо короткий вступ для тих, хто не дуже знайомий. Світ виробляє більше знань і культури, ніж будь-коли раніше, але також більше з них втрачається, ніж будь-коли раніше. Людство в основному довіряє корпораціям, таким як академічні видавці, стрімінгові сервіси та компанії соціальних медіа, цю спадщину, і вони часто не виявлялися великими хранителями. Перегляньте документальний фільм "Цифрова амнезія" або будь-яку лекцію Джейсона Скотта. Існують деякі установи, які добре архівують стільки, скільки можуть, але вони обмежені законом. Як пірати, ми знаходимося в унікальному положенні, щоб архівувати колекції, до яких вони не можуть доторкнутися через дотримання авторських прав або інші обмеження. Ми також можемо дзеркалити колекції багато разів по всьому світу, тим самим збільшуючи шанси на належне збереження. Поки що ми не будемо заглиблюватися в обговорення переваг і недоліків інтелектуальної власності, моральності порушення закону, роздумів про цензуру чи питання доступу до знань і культури. З усім цим позаду, давайте заглибимося в <em>як</em>. Ми поділимося тим, як наша команда стала піратськими архівістами, і уроками, які ми засвоїли на цьому шляху. Є багато викликів, коли ви вирушаєте в цю подорож, і, сподіваємося, ми зможемо допомогти вам подолати деякі з них. Як стати піратським архівістом Перша проблема може бути несподіваною. Це не технічна проблема чи юридична проблема. Це психологічна проблема. Перш ніж ми заглибимося, два оновлення щодо Pirate Library Mirror (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>): Ми отримали надзвичайно щедрі пожертви. Перша була $10 тис. від анонімної особи, яка також підтримувала "bookwarrior", оригінального засновника Library Genesis. Особлива подяка bookwarrior за сприяння цій пожертві. Друга була ще $10 тис. від анонімного донора, який зв’язався з нами після нашого останнього випуску і був натхненний допомогти. Ми також отримали кілька менших пожертв. Дуже дякуємо за всю вашу щедру підтримку. У нас є кілька захоплюючих нових проектів у розробці, які це підтримає, тому залишайтеся з нами. У нас були деякі технічні труднощі з розміром нашого другого випуску, але наші торренти зараз активні і роздаються. Ми також отримали щедру пропозицію від анонімної особи роздавати нашу колекцію на їхніх дуже швидкісних серверах, тому ми робимо спеціальне завантаження на їхні машини, після чого всі інші, хто завантажує колекцію, повинні побачити значне покращення швидкості. Публікації в блозі Привіт, я Анна. Я створила <a %(wikipedia_annas_archive)s>Архів Анни</a>, найбільшу у світі тіньову бібліотеку. Це мій особистий блог, у якому я та мої колеги пишемо про піратство, цифрове збереження та інше. Зв'яжіться зі мною на <a %(reddit)s>Reddit</a>. Зверніть увагу, що цей вебсайт — лише блог. Ми розміщуємо тут лише наші власні слова. Жодні торренти чи інші файли, захищені авторським правом, тут не розміщуються і не посилаються. <strong>Бібліотека</strong> - Як і більшість бібліотек, ми зосереджуємося в першу чергу на письмових матеріалах, таких як книги. Можливо, в майбутньому ми розширимося на інші види медіа. <strong>Дзеркало</strong> - Ми є виключно дзеркалом існуючих бібліотек. Ми зосереджуємося на збереженні, а не на забезпеченні легкого пошуку та завантаження книг (доступ) або створенні великої спільноти людей, які додають нові книги (джерела). <strong>Пірат</strong> - Ми свідомо порушуємо закон про авторське право в більшості країн. Це дозволяє нам робити те, що легальні організації не можуть: забезпечувати, щоб книги були дзеркальними та широко доступними. <em>Ми не надаємо посилання на файли з цього блогу. Будь ласка, знайдіть їх самостійно.</em> - Анна та команда (<a %(reddit)s>Reddit</a>) Цей проєкт (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>) має на меті сприяти збереженню та звільненню людських знань. Ми робимо свій невеликий і скромний внесок, йдучи слідами великих попередників. Фокус цього проєкту ілюструється його назвою: Перша бібліотека, яку ми дзеркалювали, це Z-Library. Це популярна (і незаконна) бібліотека. Вони взяли колекцію Library Genesis і зробили її легкою для пошуку. Крім того, вони стали дуже ефективними у залученні нових внесків книг, заохочуючи користувачів, які вносять внески, різними привілеями. Наразі вони не повертають ці нові книги до Library Genesis. І на відміну від Library Genesis, вони не роблять свою колекцію легко дзеркальною, що перешкоджає широкому збереженню. Це важливо для їхньої бізнес-моделі, оскільки вони стягують плату за доступ до своєї колекції в обсязі (більше 10 книг на день). Ми не робимо моральних суджень щодо стягнення грошей за масовий доступ до незаконної колекції книг. Безсумнівно, що Z-Library досягла успіху в розширенні доступу до знань і пошуку нових книг. Ми просто тут, щоб виконати свою частину: забезпечити довгострокове збереження цієї приватної колекції. Ми хотіли б запросити вас допомогти зберегти та звільнити людські знання, завантажуючи та розповсюджуючи наші торренти. Дивіться сторінку проєкту для отримання додаткової інформації про те, як організовані дані. Ми також дуже запрошуємо вас внести свої ідеї щодо того, які колекції дзеркалювати далі, і як це зробити. Разом ми можемо досягти багато. Це лише невеликий внесок серед безлічі інших. Дякуємо за все, що ви робите. Представляємо Дзеркало Піратської Бібліотеки: Збереження 7 ТБ книг (які не входять до Libgen) 10% of людської писемної спадщини збережено назавжди <strong>Google.</strong> Зрештою, вони провели це дослідження для Google Books. Однак їх metadata недоступні в масовому обсязі і досить важко скрапити. <strong>Різні індивідуальні бібліотечні системи та архіви.</strong> Є бібліотеки та архіви, які не були індексовані та агреговані жодним з вищезгаданих, часто тому, що вони недофінансовані або з інших причин не хочуть ділитися своїми даними з Open Library, OCLC, Google тощо. Багато з них мають цифрові записи, доступні через інтернет, і вони часто не дуже добре захищені, тому якщо ви хочете допомогти і отримати задоволення від вивчення дивних бібліотечних систем, це чудові відправні точки. <strong>ISBNdb.</strong> Це тема цього блогу. ISBNdb скрапить різні вебсайти для metadata книг, зокрема дані про ціни, які вони потім продають книготорговцям, щоб вони могли встановлювати ціни на свої книги відповідно до решти ринку. Оскільки ISBN зараз досить універсальні, вони фактично створили “веб-сторінку для кожної книги”. <strong>Open Library.</strong> Як вже згадувалося, це їхня основна місія. Вони зібрали величезну кількість бібліотечних даних від співпрацюючих бібліотек і національних архівів і продовжують це робити. Вони також мають волонтерів-бібліотекарів і технічну команду, яка намагається видалити дублікати записів і позначити їх всілякими metadata. Найкраще, що їхній набір даних повністю відкритий. Ви можете просто <a %(openlibrary)s>завантажити його</a>. <strong>WorldCat.</strong> Це вебсайт, керований некомерційною організацією OCLC, яка продає системи управління бібліотеками. Вони агрегують metadata книг з багатьох бібліотек і роблять їх доступними через вебсайт WorldCat. Однак вони також заробляють гроші, продаючи ці дані, тому вони недоступні для масового завантаження. Вони мають деякі більш обмежені масові набори даних, доступні для завантаження, у співпраці з конкретними бібліотеками. 1. Для деякого розумного визначення "назавжди". ;) 2. Звичайно, письмова спадщина людства набагато більше, ніж книги, особливо в наші дні. Для цілей цього посту і наших останніх релізів ми зосереджуємося на книгах, але наші інтереси простягаються далі. 3. Є багато чого, що можна сказати про Аарона Шварца, але ми просто хотіли згадати його коротко, оскільки він відіграє ключову роль у цій історії. З часом більше людей можуть вперше натрапити на його ім'я і згодом самостійно зануритися в цю тему. <strong>Фізичні копії.</strong> Очевидно, це не дуже корисно, оскільки вони просто дублікати одного й того ж матеріалу. Було б круто, якби ми могли зберегти всі анотації, які люди роблять у книгах, як відомі “написання на полях” Ферма. Але, на жаль, це залишиться мрією архіваріуса. <strong>“Видання”.</strong> Тут ви рахуєте кожну унікальну версію книги. Якщо щось у ній відрізняється, наприклад, інша обкладинка або інший передмов, це вважається іншим виданням. <strong>Файли.</strong> Працюючи з тіньовими бібліотеками, такими як Library Genesis, Sci-Hub або Z-Library, є додатковий аспект. Може бути кілька сканів одного й того ж видання. І люди можуть створювати кращі версії існуючих файлів, скануючи текст за допомогою OCR або виправляючи сторінки, які були скановані під кутом. Ми хочемо рахувати ці файли як одне видання, що вимагатиме хорошого metadata або дедуплікації за допомогою вимірів схожості документів. <strong>“Твори”.</strong> Наприклад, “Гаррі Поттер і Таємна кімната” як логічна концепція, що охоплює всі його версії, такі як різні переклади та перевидання. Це досить корисне визначення, але може бути важко визначити, що вважається. Наприклад, ми, ймовірно, хочемо зберегти різні переклади, хоча перевидання з незначними відмінностями можуть бути не такими важливими. - Анна та команда (<a %(reddit)s>Reddit</a>) З Дзеркалом Піратської Бібліотеки (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>), наша мета - взяти всі книги у світі та зберегти їх назавжди.<sup>1</sup> Між нашими торрентами Z-Library та оригінальними торрентами Library Genesis у нас є 11,783,153 файлів. Але скільки це насправді? Якщо б ми правильно видалили дублікати цих файлів, який відсоток усіх книг у світі ми зберегли? Ми б дуже хотіли мати щось на кшталт цього: Почнемо з деяких приблизних чисел: У Z-Library/Libgen та Open Library є набагато більше книг, ніж унікальних ISBN. Чи означає це, що багато з цих книг не мають ISBN, чи просто відсутня metadata ISBN? Ми, ймовірно, можемо відповісти на це питання за допомогою комбінації автоматичного зіставлення на основі інших атрибутів (назва, автор, видавець тощо), залучення більшої кількості джерел даних і вилучення ISBN з самих сканів книг (у випадку Z-Library/Libgen). Скільки з цих ISBN є унікальними? Це найкраще ілюструється за допомогою діаграми Венна: Щоб бути точнішим: Ми були здивовані, наскільки мало є збігів! ISBNdb має величезну кількість ISBN, які не з'являються ні в Z-Library, ні в Open Library, і те ж саме стосується (в меншій, але все ж значній мірі) інших двох. Це піднімає багато нових питань. Наскільки автоматизоване зіставлення допомогло б у маркуванні книг, які не були позначені ISBN? Чи було б багато збігів і, отже, збільшення перекриття? Також, що станеться, якщо ми додамо 4-й або 5-й набір даних? Скільки перекриття ми побачимо тоді? Це дає нам відправну точку. Тепер ми можемо подивитися на всі ISBN, які не були в наборі даних Z-Library, і які не збігаються з полями назви/автора. Це може дати нам можливість зберегти всі книги у світі: спочатку шляхом сканування інтернету для пошуку сканів, потім виходячи в реальне життя для сканування книг. Останнє навіть може бути профінансовано спільнотою або керуватися "винагородами" від людей, які хотіли б бачити певні книги оцифрованими. Все це історія для іншого разу. Якщо ви хочете допомогти з будь-яким з цього — подальший аналіз; збирання більше metadata; пошук більше книг; OCR книг; виконання цього для інших доменів (наприклад, статті, аудіокниги, фільми, телешоу, журнали) або навіть надання деяких з цих даних для таких речей, як ML / навчання великих мовних моделей — будь ласка, зв'яжіться зі мною (<a %(reddit)s>Reddit</a>). Якщо вас особливо цікавить аналіз даних, ми працюємо над тим, щоб зробити наші набори даних і скрипти доступними у більш зручному форматі. Було б чудово, якби ви могли просто форкнути блокнот і почати грати з цим. Нарешті, якщо ви хочете підтримати цю роботу, будь ласка, розгляньте можливість зробити пожертву. Це повністю волонтерська операція, і ваш внесок має величезне значення. Кожна частинка допомагає. Поки що ми приймаємо пожертви в криптовалюті; дивіться сторінку Пожертвувань на Архіві Анни. Для відсотка нам потрібен знаменник: загальна кількість книг, коли-небудь опублікованих.<sup>2</sup> Перед закриттям Google Books, інженер проєкту, Леонід Тайчер, <a %(booksearch_blogspot)s>спробував оцінити</a> цю кількість. Він дійшов — жартома — до 129,864,880 (“принаймні до неділі”). Він оцінив цю кількість, створивши єдину базу даних усіх книг у світі. Для цього він зібрав різні Datasets і потім об'єднав їх різними способами. Як короткий відступ, є ще одна людина, яка намагалася каталогізувати всі книги у світі: Аарон Шварц, покійний цифровий активіст і співзасновник Reddit.<sup>3</sup> Він <a %(youtube)s>заснував Open Library</a> з метою створення “однієї веб-сторінки для кожної книги, яка коли-небудь була опублікована”, об'єднуючи дані з багатьох різних джерел. Він заплатив найвищу ціну за свою роботу з цифрового збереження, коли його переслідували за масове завантаження наукових статей, що призвело до його самогубства. Не потрібно говорити, що це одна з причин, чому наша група є псевдонімною, і чому ми дуже обережні. Open Library все ще героїчно керується людьми з Internet Archive, продовжуючи спадщину Аарона. Ми повернемося до цього пізніше в цьому пості. У блозі Google, Тайчер описує деякі виклики з оцінкою цього числа. По-перше, що таке книга? Є кілька можливих визначень: “Видання” здаються найбільш практичним визначенням того, що таке “книги”. Зручно, що це визначення також використовується для присвоєння унікальних номерів ISBN. ISBN, або Міжнародний стандартний книжковий номер, зазвичай використовується для міжнародної торгівлі, оскільки він інтегрований з міжнародною системою штрих-кодів (“Міжнародний номер статті”). Якщо ви хочете продавати книгу в магазинах, їй потрібен штрих-код, тому ви отримуєте ISBN. У блозі Тайчера згадується, що хоча ISBN корисні, вони не є універсальними, оскільки їх дійсно прийняли лише в середині сімдесятих, і не скрізь у світі. Проте, ISBN, ймовірно, є найбільш широко використовуваним ідентифікатором видань книг, тому це наш найкращий відправний пункт. Якщо ми зможемо знайти всі ISBN у світі, ми отримаємо корисний список книг, які ще потрібно зберегти. Отже, де ми можемо отримати дані? Існує кілька існуючих зусиль, які намагаються скласти список всіх книг у світі: У цьому пості ми раді оголосити про невеликий реліз (порівняно з нашими попередніми релізами Z-Library). Ми скрапили більшість ISBNdb і зробили дані доступними для торрентування на вебсайті Pirate Library Mirror (РЕДАКЦІЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>; ми не будемо посилатися на нього тут безпосередньо, просто знайдіть його). Це близько 30,9 мільйонів записів (20 ГБ у форматі <a %(jsonlines)s>JSON Lines</a>; 4,4 ГБ у стиснутому вигляді). На їхньому вебсайті вони стверджують, що насправді мають 32,6 мільйонів записів, тому ми, можливо, якось пропустили деякі, або <em>вони</em> могли щось зробити неправильно. У будь-якому випадку, наразі ми не будемо ділитися тим, як ми це зробили — залишимо це як вправу для читача. ;-) Те, чим ми поділимося, це деякий попередній аналіз, щоб спробувати наблизитися до оцінки кількості книг у світі. Ми розглянули три набори даних: цей новий набір даних ISBNdb, наш оригінальний реліз metadata, який ми скрапили з тіньової бібліотеки Z-Library (яка включає Library Genesis), і дамп даних Open Library. Вивантаження ISBNdb, або Скільки Книг Збережено Назавжди? Якщо б ми правильно видалили дублікати файлів з тіньових бібліотек, який відсоток усіх книг у світі ми зберегли? Оновлення про <a %(wikipedia_annas_archive)s>Архів Анни</a>, найбільшу справді відкриту бібліотеку в історії людства. <em>Редизайн WorldCat</em> Дані <strong>Формат?</strong> <a %(blog)s>Контейнери Архіву Анни (AAC)</a>, які по суті є <a %(jsonlines)s>JSON Lines</a>, стиснутими за допомогою <a %(zstd)s>Zstandard</a>, плюс деякі стандартизовані семантики. Ці контейнери обгортають різні типи записів, на основі різних скрапінгів, які ми розгорнули. Рік тому ми <a %(blog)s>почали</a> відповідати на це питання: <strong>Який відсоток книг був назавжди збережений тіньовими бібліотеками?</strong> Давайте подивимося на деяку основну інформацію про дані: Як тільки книга потрапляє в тіньову бібліотеку з відкритими даними, таку як <a %(wikipedia_library_genesis)s>Library Genesis</a>, а тепер і <a %(wikipedia_annas_archive)s>Архів Анни</a>, вона віддзеркалюється по всьому світу (через торренти), тим самим практично зберігаючи її назавжди. Щоб відповісти на питання, який відсоток книг був збережений, нам потрібно знати знаменник: скільки книг існує загалом? Ідеально, якщо ми маємо не лише число, але й фактичні metadata. Тоді ми можемо не лише зіставити їх з тіньовими бібліотеками, але й <strong>створити список книг, які ще потрібно зберегти!</strong> Ми навіть могли б почати мріяти про краудсорсингову ініціативу, щоб пройтися по цьому списку. Ми зібрали дані з <a %(wikipedia_isbndb_com)s>ISBNdb</a> та завантажили <a %(openlibrary)s>набір даних Open Library</a>, але результати були незадовільними. Основною проблемою було те, що не було великого перетину ISBN. Подивіться на цю діаграму Венна з <a %(blog)s>нашого блогу</a>: Ми були дуже здивовані, наскільки мало було перетину між ISBNdb та Open Library, обидва з яких щедро включають дані з різних джерел, таких як веб-скрапінг та бібліотечні записи. Якщо б вони обидва добре виконували свою роботу з пошуку більшості ISBN, їхні кола, безумовно, мали б значний перетин, або одне було б підмножиною іншого. Це змусило нас задуматися, скільки книг випадає <em>повністю за межі цих кіл</em>? Нам потрібна більша база даних. Саме тоді ми звернули увагу на найбільшу базу даних книг у світі: <a %(wikipedia_worldcat)s>WorldCat</a>. Це власна база даних некомерційної організації <a %(wikipedia_oclc)s>OCLC</a>, яка агрегує metadata записи з бібліотек по всьому світу, в обмін на надання цим бібліотекам доступу до повного набору даних та відображення їх у результатах пошуку кінцевих користувачів. Хоча OCLC є некомерційною організацією, їхня бізнес-модель вимагає захисту їхньої бази даних. Ну, ми шкодуємо, друзі з OCLC, але ми віддаємо все. :-) Протягом минулого року ми ретельно зібрали всі записи WorldCat. Спочатку нам пощастило. WorldCat щойно розгорнув повний редизайн свого вебсайту (у серпні 2022 року). Це включало значну перебудову їхніх бекенд-систем, що призвело до багатьох вразливостей у безпеці. Ми негайно скористалися можливістю і змогли зібрати сотні мільйонів (!) записів за лічені дні. Після цього вразливості безпеки поступово виправлялися одна за одною, поки остання, яку ми знайшли, не була виправлена близько місяця тому. До того часу у нас були майже всі записи, і ми лише прагнули отримати трохи якісніші записи. Тож ми відчули, що настав час випустити! 1,3 мільярда сканувань WorldCat <em><strong>Коротко:</strong> Архів Анни сканував весь WorldCat (найбільшу у світі колекцію бібліотечних metadata), щоб створити список книг, які потрібно зберегти.</em> WorldCat Попередження: цей блог-пост застарів. Ми вирішили, що IPFS ще не готовий для широкого використання. Ми все ще будемо посилатися на файли на IPFS з Архіву Анни, коли це можливо, але ми більше не будемо їх хостити самі, і не рекомендуємо іншим дзеркалити за допомогою IPFS. Будь ласка, перегляньте нашу сторінку Торентів, якщо ви хочете допомогти зберегти нашу колекцію. Допоможіть розповсюдити Z-Library на IPFS Завантажування з серверу партнера SciDB Зовнішнє запозичення Зовнішнє запозичення (не для друку) Зовнішнє завантаження Дослідіть метадані Міститься в торентах Назад  (+%(num)s бонус) не оплачено оплачено скасовано закінчився термін дії очікуємо на підтвердження від Анни недійсний Текст нижче доступний лише англійською. Перейти Скинути Вперед Останній Якщо ваша електронна адреса не працює на форумах Libgen, ми рекомендуємо використовувати <a %(a_mail)s>Proton Mail</a> (безкоштовно). Ви також можете <a %(a_manual)s>особисто надіслати запит</a> на активацію вашого облікового запису. (можливо знадобиться <a %(a_browser)s>верифікація браузера</a> — необмежена кількість завантажень!) Швидкий сервер партнерів #%(number)s (рекомендовано) (трохи швидше, але з чергою) (не потрібна верифікація браузера) (без перевірки браузера або списків очікування) (немає списку очікування, але може бути дуже повільно) Повільний сервер партнерів #%(number)s Аудіокнига Комікс Книга (художня література) Книга (академічна література) Книга (деталі невідомі) Стаття з журналу Журнал Музичний запис Інше Документ із описом стандартів Не всі сторінки вдалося перетворити на PDF Помічено як "зламаний файл" у .li-версії академічного розділу Library Genesis Не відображається у .li-версії Library Genesis Не відображається у .rs-версії художнього розділу Library Genesis Не відображається у .rs-версії академічного розділу Library Genesis Запуск exiftool не вдався для цього файлу Позначено як «поганий файл» у Z-Library Файл відсутній у Z-Library Позначено як «спам» у Z-Library Файл не може бути відкритим (напр. пошкодження файлу, DRM) Претензія щодо авторських прав Проблеми зі скачуванням (напр. відсутність з'єднання, повідомлення щодо помилки, дуже повільно) Невірні метаданні (напр. назва, опис, оюкладинка) Other Погана якість (напр. проблеми з форматуванням, погана якість скану, відсутні сторінки) Спам / файл потрібно видалити (напр. реклама, образливий вміст) %(amount)s (%(amount_usd)s) %(amount)s всього %(amount)s (%(amount_usd)s) всього Блискучий Буквоїд Блаженний Бібліотекар Засліплюючий Знавець Дивовижний Дяк Бонусні скачування Cerlalc Чеські метадані DuXiu 读秀 Індекс електронних книг EBSCOhost Google Книги Goodreads HathiTrust IA Цифрове кредитування під контролем IA ISBNdb ISBN GRP Libgen.li Виключаючи “scimag” Libgen.rs Нон-фікшн та художня література Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Російська державна бібліотека Sci-Hub Через Libgen.li «scimag» Sci-Hub / Libgen “scimag” Trantor Завантаження до AA Z-Library Z-Library китайською Пошук по назві, автору, DOi, ISBN, MD5, … Пошук Автор Опис та метадані коментарів Версія Оригінальна назва файлу Видавець (пошук у певному полі) Назва Рік видання Технічні деталі (англійською) Ця монета вища за типовий мінімум. Будь ласка, оберіть іншу тривалість або монету. Не вдалося виконати запит. Будь ласка, спробуйте ще раз через кілька хвилин, і якщо це не вдасться зробити, зв'яжіться з нами за адресою %(email)s та надішліть скріншот екрана. Сталась невідома помилка. Будь ласка, напишіть на %(email)s і додайте знімок екрану. Помилка в обробці платежу. Будь ласка, зачекайте і спробуйте ще раз. Якщо проблема не зникає більше 24 годин, будь ласка, зв'яжіться з нами за адресою %(email)s та надішліть скріншот. Ми проводимо збір коштів на <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">створення резервної копії</a> найбільшої тіньової бібліотеки коміксів у світі. Дякуємо за вашу підтримку! <a href="/donate">Зробити благодійний внесок.</a> Якщо ви не можете зробити благодійний внесок, підтримайте нас, розказавши друзям та підписавшись на нас у <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, або <a href="https://t.me/annasarchiveorg">Telegram</a>. Не надсилайте нам <a %(a_request)s>запити на книги</a><br>або невеликі (<10k) <a %(a_upload)s>завантаження</a>. Архів Анни DMCA / Закон про авторське право Контакти Reddit Альтернативи SLUM (%(unaffiliated)s) не афілійований Архів Анни потребує вашої допомоги! Якщо ви пожертвуєте зараз, ви отримаєте <strong>подвійно</strong> більше швидких завантажень. Багато хто намагається нас знищити, але ми боремося. Якщо ви зробите донат цього місяця, ви отримаєте <strong>вдвічі</strong> більше швидких завантажень. Дійсно до кінця цього місяця. Збереження людських знань: чудовий подарунок до свята! Членство буде відповідно продовжено. Партнерські сервери недоступні через закриття хостингу. Вони мають знову запрацювати незабаром. Щоб підвищити стійкість "Архіву Анни", ми шукаємо волонтерів для роботи з дзеркалами. Ми додали новий метод для донацій: %(method_name)s. Обслуговування сайту є дорогим та ми були б дуже вдячні вам за %(donate_link_open_tag)sпідтримку</a>. Дуже вам дякуємо. Запросіть друга, і ви і ваш друг отримаєте %(percentage)s%% бонусних швидких завантажень! Здивуйте кохану людину, подаруйте їй обліковий запис із членством. Ідеальний подарунок на День Святого Валентина! Дізнатися більше… Обліковий запис Активність Розширений Блог Анни↗ Аннине програмне забезпечення ↗ beta Дослідник кодів Бази даних Задонатити Завантажені файли ЧаПи Головна сторінка Покращити метадані LLM дані Увійти / Зареєструватися Мої донати Загальнодоступний профіль Пошук Безпека Торренти Перекладати ↗ Волонтерство та нагороди Нещодавні завантаження: 📚&nbsp;Найбільша у світі бібліотека відкритих даних з відкритим вихідним кодом. ⭐️&nbsp;Віддзеркалює Sci-Hub, Library Genesis, Z-Library та інші ресурси. 📈&nbsp;%(book_any)s книги, %(journal_article)s статті, %(book_comic)s комікси, %(magazine)s журнали — все це збережено назавжди.  і  та більше DuXiu Бібліотека Інтернет- архіву LibGen 📚&nbsp;Найбільша по-справжньому відкрита бібліотека в історії людства. 📈&nbsp;%(book_count)s&nbsp;книги, %(paper_count)s&nbsp;статей — все це збережено назавжди. ⭐️&nbsp;Ми відзеркалюємо %(libraries)s. Ми збираємо %(scraped)s. Весь наш код і дані повністю відкриті. OpenLib Sci-Hub ,  📚Найбільша у світі бібліотека відкритих даних з відкритим вихідним кодом.<br>⭐️ Віддзеркалює Scihub, Libgen, Zlib та інші. Z-Lib Архів Анни Невірний запит. Відвідайте %(websites)s. Найбільша у світі бібліотека з відкритим вихідним кодом та відкритими даними. Включає Sci-Hub, Library Genesis, Z-Library та інші. Пошук в Архіві Анни Архів Анни Будь ласка, оновіть сторінку, щоб спробувати ще раз. <a %(a_contact)s>Зв'яжіться з нами</a>, якщо проблема зберігається протягом кількох годин. 🔥 Проблема з завантаженням цієї сторінки <li>1. Слідкуйте за нами у <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, чи <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Розкажіть про Архів Анни у Twitter, Reddit, Tiktok, Instagram, у вашому місцевому кафе чи бібліотеці, чи будь-де, куди б ви не пішли! Ми не віримо в закриття - якщо нас прикриють, ми просто з'явимося в іншому місці, оскільки весь наш код і дані повністю з відкритим вихідним кодом.</li><li>3. Якщо у вас є змога, розгляньте можливість <a href="/donate">донату</a>.</li><li>4. Допомогайте <a href="https://translate.annas-software.org/">перекладати</a> нашу сторінку різними мовами.</li><li>5. Якщо ви інженер-програміст, розгляньте можливість долучитися до розробки нашого <a href="https://annas-software.org/">відкритого коду</a>, чи роздачі <a href="/datasets">торрентів</a>.</li> 10. Створіть або допоможіть підтримувати сторінку Вікіпедії Архіву Анни вашою мовою. 11. Ми шукаємо можливість розміщувати невеликі, зі смаком оформлені оголошення. Якщо ви хочете розмістити рекламу в Архіві Анни, будь ласка, повідомте нам про це. 6. Якщо ви дослідник інформаційної безпеки, ми можемо використовувати ваші навички як для нападу, так і для захисту. Погляньте на нашу сторінку з <a %(a_security)s>Безпеки</a>. 7. Ми шукаємо фахівців з платежів для анонімних продавців. Чи можете ви допомогти нам додати зручніші способи для донатів? PayPal, WeChat, подарункові картки. Якщо ви знаєте когось, будь ласка, зв'яжіться з нами. 8. Ми завжди шукаємо більше серверних потужностей. 9. Ви можете допомогти, повідомляючи про проблеми з файлами, залишаючи коментарі та створюючи списки прямо на цьому сайті. Ви також можете допомогти <a %(a_upload)s>завантажити більше книг</a>, або виправити проблеми з файлами чи форматуванням існуючих книг. Для більш детальної інформації про те, як стати волонтером, дивіться нашу сторінку <a %(a_volunteering)s>Волонтерство та нагороди</a>. Ми віримо, що досвід поколінь має бути надійно збереженим та доступним кожному. Створюючи цю пошукову систему, ми зібрали докупи гігантські інформаційні витвори, створені в результаті кропіткої роботи розробників тіньових бібліотек. І ми сподіваємося, що наша робота буде продовженням їхніх старань по звільненню інформації та донесенню її до людей. Щоб бути в курсі нашого прогресу, слідкуйте за Анною у <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> or <a href="https://t.me/annasarchiveorg">Telegram</a>. Для запитань та зворотного зв'язку, будь ласка, звертайтеся до Анни за адресою %(email)s. Ідентифікатор облікового запису: %(account_id)s Вийти ❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. ✅ Ви вийшли з облікового запису. Перезавантажте сторінку, щоб увійти знову. Швидкі завантаження (за останні 24 години): <strong>%(used)s / %(total)s</strong> Підписка: <strong>%(tier_name)s</strong> до %(until_date)s <a %(a_extend)s>(подовжити)</a> Ви можете об’єднати кілька членств (швидке завантаження за 24 години буде додано разом). Підписка: <strong>Відсутня</strong> <a %(a_become)s>(стати учасником)</a> Зв'яжіться з Анною за адресою %(email)s, якщо ви зацікавлені в покращенні вашої підписки. Загальнодоступний профіль: %(profile_link)s Секретний ключ (не діліться!): %(secret_key)s показати Приєднуйтесь до нас! Перейдіть на <a %(a_tier)s>вищий рівень</a> щоб приєднатися до нашої групи. Ексклюзивна Telegram-група: %(link)s Обліковий запис які завантаження? Увійти Не загубіть свій ключ! Невірний секретний код. Перевірте ваш код і спробуйте ще раз, або зареєструйте новий обліковий запис нижче. Секретний код Введіть ваш секретний код, щоб увійти в систему: Старий обліковий запис на основі електронної пошти? Введіть свою <a %(a_open)s>електронну пошту тут</a>. Зареєструвати новий обліковий запис У вас ще немає облікового запису? Реєстрацію успішно завершено! Ось ваш секретний код: <span %(span_key)s>%(key)s</span> Надійно збережіть цей код. Якщо ви його загубите, то ви втратите доступ до свого облікового запису. <li %(li_item)s><strong>Закладки.</strong> Ви можете додати цю сторінку до закладок, щоб отримати ваш код.</li><li %(li_item)s><strong>Завантажити.</strong> Натисніть на <a %(a_download)s>це посилання</a>, щоб завантажити ваш код.</li><li %(li_item)s><strong>Менеджер паролів.</strong> Для вашої зручності ми заздалегідь створили попередньо записаний файл з вашим кодом, щоб ви могли зберегти його у своєму менеджері паролів для входу на сайт.</li> Увійти / Зареєструватися Перевірка браузера Попередження: код містить некоректні символи Unicode і може працювати неправильно в різних ситуаціях. Сировий бінарний код можна декодувати з base64 представлення в URL. Опис Мітка Префікс URL для конкретного коду Вебсайт Коди, що починаються з “%(prefix_label)s” Будь ласка, не скануйте ці сторінки. Натомість ми рекомендуємо <a %(a_import)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB, і запускати наш <a %(a_software)s>відкритий вихідний код</a>. Сирові дані можна вручну досліджувати через файли JSON, такі як <a %(a_json_file)s>цей</a>. Менше ніж %(count)s записів Загальний URL Дослідник кодів Індекс Досліджуйте коди, якими позначені записи, за префіксом. Стовпець «записи» показує кількість записів, позначених кодами з даним префіксом, як це видно в пошуковій системі (включаючи записи лише з метаданими). Стовпець «коди» показує, скільки фактичних кодів мають даний префікс. Відомий префікс коду “%(key)s” Більше… Префікс %(count)s запис, що відповідає “%(prefix_label)s” %(count)s записи, що відповідають “%(prefix_label)s” %(count)s записів, що відповідають “%(prefix_label)s” коди записи “%%s” буде замінено на значення коду Пошук в Архіві Анни Коди URL для конкретного коду: “%(url)s” Ця сторінка може зайняти деякий час для генерації, тому вона вимагає капчу Cloudflare. <a %(a_donate)s>Члени</a> можуть пропустити капчу. Зловживання повідомлено: Краща версія Ви хочете повідомити про цього користувача за образливу або недоречну поведінку? Проблема з файлом: %(file_issue)s прихований коментар Відповісти Повідомити про зловживання Ви повідомили про цього користувача за зловживання. Претензії щодо авторських прав відправленні на цю електронну скриньку будуть проігноровані, замість цього скористайтеся формою. Показати електронну скриньку Ми дуже раді вашим відгукам та запитанням! Однак через велику кількість спаму та безглуздих листів, які ми отримуємо, будь ласка, поставте галочку, щоб підтвердити, що ви розумієте ці умови для зв'язку з нами. Будь-які інші способи зв'язатися з нами щодо претензій з приводу авторських прав будуть автоматично видалені. Для DMCA / авторських претензій, використовуйте <a %(a_copyright)s>цю форму</a>. Контактна електронна скринька URL-адреси на Архіві Анни (обов'язково). Одна на рядок. Будь ласка, включайте лише URL-адреси, які описують точно таке ж видання книги. Якщо ви хочете подати претензію на кілька книг або кілька видань, будь ласка, подавайте цю форму кілька разів. Претензії, які об'єднують кілька книг або видань, будуть відхилені. Адреса (обов'язково) Чіткий опис вихідного матеріалу (обов'язково) Електронна пошта (обов'язково) URL-адреси до вихідного матеріалу, одна на рядок (обов'язково). Будь ласка, включайте якомога більше, щоб допомогти нам перевірити вашу претензію (наприклад, Amazon, WorldCat, Google Books, DOI). ISBN вихідного матеріалу (якщо застосовно). Одна на рядок. Будь ласка, включайте лише ті, що точно відповідають виданню, на яке ви подаєте претензію щодо авторського права. Ваше ім'я (обов'язково) ❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. ✅ Дякуємо за подання вашої претензії щодо авторського права. Ми розглянемо її якомога швидше. Будь ласка, перезавантажте сторінку, щоб подати ще одну. <a %(a_openlib)s>Open Library</a> URL-адреси вихідного матеріалу, одна на рядок. Будь ласка, знайдіть час, щоб знайти ваш вихідний матеріал в Open Library. Це допоможе нам перевірити вашу претензію. Номер телефону (обов'язково) Заява та підпис (обов'язково) Подати претензію Якщо у вас є претензія DMCA або інша претензія щодо авторського права, будь ласка, заповніть цю форму якомога точніше. Якщо у вас виникнуть будь-які проблеми, будь ласка, зв'яжіться з нами за нашою спеціальною адресою DMCA: %(email)s. Зверніть увагу, що претензії, надіслані на цю адресу електронної пошти, не будуть оброблені, вона призначена лише для запитань. Будь ласка, використовуйте форму нижче для подання ваших претензій. Форма претензії DMCA / Авторського права Приклад запису в Архіві Анни Торренти від Архіву Анни Формат Контейнерів Архіву Анни Скрипти для імпорту метаданих Якщо ви зацікавлені у дзеркалюванні цього набору даних для <a %(a_archival)s>архівування</a> або <a %(a_llm)s>навчання LLM</a>, будь ласка, зв'яжіться з нами. Останнє оновлення: %(date)s Основний %(source)s вебсайт Документація метаданих (більшість полів) Файли, віддзеркалені Архівом Анни: %(count)s (%(percent)s%%) Ресурси Загальна кількість файлів: %(count)s Загальний розмір файлів: %(size)s Наш блог-пост про ці дані <a %(duxiu_link)s>Duxiu</a> — це величезна база даних відсканованих книг, створена <a %(superstar_link)s>SuperStar Digital Library Group</a>. Більшість з них — академічні книги, відскановані для того, щоб зробити їх доступними в цифровому форматі для університетів та бібліотек. Для нашої англомовної аудиторії <a %(princeton_link)s>Прінстон</a> та <a %(uw_link)s>Вашингтонський університет</a> мають хороші огляди. Також є чудова стаття, яка надає більше інформації: <a %(article_link)s>“Оцифровка китайських книг: приклад пошукової системи SuperStar DuXiu Scholar”</a>. Книги з Duxiu давно піратяться в китайському інтернеті. Зазвичай їх продають за менше ніж долар перекупники. Вони зазвичай розповсюджуються за допомогою китайського аналога Google Drive, який часто зламують для збільшення обсягу сховища. Деякі технічні деталі можна знайти <a %(link1)s>тут</a> і <a %(link2)s>тут</a>. Хоча книги були напівпублічно розповсюджені, їх досить важко отримати у великій кількості. Ми мали це високо у нашому списку справ, і виділили на це кілька місяців повноцінної роботи. Однак, наприкінці 2023 року до нас звернувся неймовірний, дивовижний і талановитий волонтер, який повідомив, що вже виконав всю цю роботу — за великі витрати. Він поділився з нами повною колекцією, не очікуючи нічого взамін, окрім гарантії довгострокового збереження. Дійсно вражаюче. Більше інформації від наших волонтерів (сирі нотатки): Адаптовано з нашого <a %(a_href)s>блог-посту</a>. DuXiu 读秀 %(count)s файл %(count)s файли %(count)s файлів Цей набір даних тісно пов'язаний з <a %(a_datasets_openlib)s>набором даних Open Library</a>. Він містить скрапінг усіх метаданих і значну частину файлів з Контрольованої цифрової бібліотеки IA. Оновлення випускаються у <a %(a_aac)s>форматі контейнерів Архіву Анни</a>. Ці записи безпосередньо посилаються на набір даних Open Library, але також містять записи, яких немає в Open Library. У нас також є кілька файлів даних, зібраних членами спільноти протягом багатьох років. Колекція складається з двох частин. Вам потрібні обидві частини, щоб отримати всі дані (за винятком застарілих торрентів, які викреслені на сторінці торрентів). Цифрова бібліотека позик наш перший випуск, до того як ми стандартизували формат <a %(a_aac)s>Контейнери Архіву Анни (AAC)</a>. Містить метадані (у форматах json та xml), pdf-файли (з цифрових систем позики acsm та lcpdf) та мініатюри обкладинок. інкрементальні нові випуски, використовуючи AAC. Містить лише метадані з часовими мітками після 2023-01-01, оскільки решта вже покрита "ia". Також всі pdf-файли, цього разу з систем позики acsm та "bookreader" (веб-читач IA). Незважаючи на те, що назва не зовсім правильна, ми все одно додаємо файли bookreader до колекції ia2_acsmpdf_files, оскільки вони взаємовиключні. IA Контрольоване цифрове кредитування 98%%+ файлів доступні для пошуку. Наша місія — архівувати всі книги у світі (а також статті, журнали тощо) і зробити їх широко доступними. Ми вважаємо, що всі книги повинні бути дзеркальними в багатьох місцях, щоб забезпечити їхню надмірність і стійкість. Ось чому ми збираємо файли з різних джерел. Деякі джерела повністю відкриті і можуть бути дзеркальними в масовому порядку (наприклад, Sci-Hub). Інші закриті і захищені, тому ми намагаємося скрапити їх, щоб «звільнити» їхні книги. Ще інші знаходяться десь посередині. Всі наші дані можна <a %(a_torrents)s>завантажити через торрент</a>, а всі наші метадані можна <a %(a_anna_software)s>згенерувати</a> або <a %(a_elasticsearch)s>завантажити</a> як бази даних ElasticSearch і MariaDB. Сирові дані можна вручну досліджувати через JSON файли, такі як <a %(a_dbrecord)s>цей</a>. Метадані Вебсайт ISBN Останнє оновлення: %(isbn_country_date)s (%(link)s) Ресурси Міжнародне агентство ISBN регулярно випускає діапазони, які воно виділило національним агентствам ISBN. З цього ми можемо визначити, до якої країни, регіону або мовної групи належить цей ISBN. Наразі ми використовуємо ці дані опосередковано, через бібліотеку Python <a %(a_isbnlib)s>isbnlib</a>. Інформація про країну ISBN Це дамп великої кількості запитів до isbndb.com у вересні 2022 року. Ми намагалися охопити всі діапазони ISBN. Це близько 30,9 мільйонів записів. На їхньому вебсайті вони стверджують, що насправді мають 32,6 мільйонів записів, тому ми могли якось пропустити деякі, або <em>вони</em> могли щось зробити неправильно. Відповіді JSON майже не змінені з їхнього сервера. Однією з проблем якості даних, яку ми помітили, є те, що для номерів ISBN-13, які починаються з іншого префікса, ніж «978-», вони все одно включають поле «isbn», яке просто є номером ISBN-13 з відрізаними першими 3 цифрами (і перерахованою контрольною цифрою). Це явно неправильно, але вони, здається, так роблять, тому ми не змінювали це. Ще однією потенційною проблемою, з якою ви можете зіткнутися, є те, що поле «isbn13» має дублікати, тому ви не можете використовувати його як первинний ключ у базі даних. Поля «isbn13»+«isbn» у поєднанні, здається, є унікальними. Випуск 1 (2022-10-31) Торренти художньої літератури відстають (хоча ID ~4-6M не торентовані, оскільки вони перетинаються з нашими Zlib торентами). Наш блог-пост про випуск коміксів Торренти коміксів на Архіві Анни Для історії різних форків Library Genesis дивіться сторінку <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li містить більшість того ж контенту та метаданих, що й Libgen.rs, але має деякі додаткові колекції, а саме комікси, журнали та стандартні документи. Він також інтегрував <a %(a_scihub)s>Sci-Hub</a> у свої метадані та пошукову систему, що ми використовуємо для нашої бази даних. Метадані для цієї бібліотеки доступні безкоштовно <a %(a_libgen_li)s>на libgen.li</a>. Однак цей сервер повільний і не підтримує відновлення перерваних з'єднань. Ті ж файли також доступні на <a %(a_ftp)s>FTP-сервері</a>, який працює краще. Нон-фікшн також, здається, відхилився, але без нових торентів. Схоже, це сталося з початку 2022 року, хоча ми цього не перевіряли. За словами адміністратора Libgen.li, колекція “fiction_rus” (російська художня література) повинна бути покрита регулярно випущеними торрентами з <a %(a_booktracker)s>booktracker.org</a>, зокрема торрентами <a %(a_flibusta)s>flibusta</a> та <a %(a_librusec)s>lib.rus.ec</a> (які ми дзеркалимо <a %(a_torrents)s>тут</a>, хоча ми ще не встановили, які торренти відповідають яким файлам). Колекція художньої літератури має власні торренти (відмінні від <a %(a_href)s>Libgen.rs</a>), починаючи з %(start)s. Певні діапазони без торрентів (такі як діапазони художньої літератури f_3463000 до f_4260000) ймовірно є файлами Z-Library (або іншими дублікатами), хоча ми можемо захотіти провести деяку дедуплікацію і створити торренти для унікальних файлів lgli в цих діапазонах. Статистику для всіх колекцій можна знайти <a %(a_href)s>на вебсайті libgen</a>. Торренти доступні для більшості додаткового контенту, зокрема торренти для коміксів, журналів та стандартних документів були випущені у співпраці з Архівом Анни. Зверніть увагу, що торрент-файли, які стосуються “libgen.is”, є явними дзеркалами <a %(a_libgen)s>Libgen.rs</a> (“.is” — це інший домен, який використовує Libgen.rs). Корисний ресурс для використання метаданих — <a %(a_href)s>ця сторінка</a>. %(icon)s Їхня колекція “fiction_rus” (російська художня література) не має спеціальних торрентів, але покривається торрентами від інших, і ми зберігаємо <a %(fiction_rus)s>дзеркало</a>. Торренти російської художньої літератури в Архіві Анни Торренти художньої літератури на Архіві Анни Форум для обговорень Метадані Метадані через FTP Торренти журналів на Архіві Анни Інформація про поля метаданих Дзеркало інших торрентів (та унікальні торренти художньої літератури і коміксів) Торренти стандартних документів в Архіві Анни Libgen.li Торренти від Анниного Архіву (обкладинки книг) Library Genesis відомий тим, що вже щедро надає свої дані у великих обсягах через торренти. Наша колекція Libgen складається з додаткових даних, які вони не випускають безпосередньо, у партнерстві з ними. Велика подяка всім, хто працює з Library Genesis, за співпрацю з нами! Наш блог про випуск обкладинок книг Ця сторінка про версію “.rs”. Вона відома тим, що постійно публікує як свої метадані, так і повний вміст свого каталогу книг. Її колекція книг розділена на частини художньої та нехудожньої літератури. Корисний ресурс для використання метаданих — <a %(a_metadata)s>ця сторінка</a> (блокує діапазони IP, може знадобитися VPN). Станом на 2024-03, нові торренти публікуються в <a %(a_href)s>цій темі форуму</a> (блокує діапазони IP, може знадобитися VPN). Торренти художньої літератури на Анниному Архіві Торренти художньої літератури Libgen.rs Форум обговорень Libgen.rs Метадані Libgen.rs Інформація про поля метаданих Libgen.rs Торренти нон-фікшн Libgen.rs Торренти нон-фікшн на Анниному Архіві %(example)s для художньої книги. Цей <a %(blog_post)s>перший випуск</a> досить невеликий: близько 300 ГБ обкладинок книг з форку Libgen.rs, як художньої, так і нон-фікшн літератури. Вони організовані так само, як вони з'являються на libgen.rs, наприклад: %(example)s для нон-фікшн книги. Так само, як і з колекцією Z-Library, ми зібрали їх усі в один великий .tar файл, який можна змонтувати за допомогою <a %(a_ratarmount)s>ratarmount</a>, якщо ви хочете безпосередньо обслуговувати файли. Випуск 1 (%(date)s) Коротка історія різних форків Library Genesis (або “Libgen”) полягає в тому, що з часом різні люди, залучені до Library Genesis, посварилися і пішли своїми шляхами. Згідно з цим <a %(a_mhut)s>дописом на форумі</a>, Libgen.li спочатку був розміщений на “http://free-books.dontexist.com”. Версія “.fun” була створена оригінальним засновником. Вона оновлюється на користь нової, більш розподіленої версії. <a %(a_li)s>Версія “.li”</a> має величезну колекцію коміксів, а також інший контент, який (поки що) недоступний для масового завантаження через торренти. Вона має окрему колекцію торрентів художніх книг і містить метадані <a %(a_scihub)s>Sci-Hub</a> у своїй базі даних. Версія “.rs” має дуже схожі дані і найчастіше випускає свою колекцію у вигляді масових торрентів. Вона приблизно розділена на секції “художня література” та “нехудожня література”. Спочатку на “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> в певному сенсі також є форком Library Genesis, хоча вони використовували іншу назву для свого проєкту. Libgen.rs Ми також збагачуємо нашу колекцію джерелами тільки з метаданими, які ми можемо зіставити з файлами, наприклад, використовуючи номери ISBN або інші поля. Нижче наведено огляд таких джерел. Знову ж таки, деякі з цих джерел є повністю відкритими, тоді як інші ми змушені скрапити. Зверніть увагу, що в пошуку метаданих ми показуємо оригінальні записи. Ми не об'єднуємо записи. Джерела тільки з метаданими Open Library — це проект з відкритим кодом від Internet Archive, спрямований на каталогізацію кожної книги у світі. Він має одну з найбільших у світі операцій зі сканування книг і багато книг доступні для цифрового позичання. Його каталог метаданих книг доступний для безкоштовного завантаження і включений до Анниного Архіву (хоча наразі не в пошуку, за винятком випадків, коли ви явно шукаєте за ID Open Library). Open Library Без урахування дублікатів Останнє оновлення Відсотки кількості файлів %% віддзеркалено AA / доступні торренти Розмір Джерело Нижче наведено короткий огляд джерел файлів на Анниному Архіві. Оскільки тіньові бібліотеки часто синхронізують дані одна з одною, між бібліотеками є значний збіг. Ось чому цифри не складаються до загальної суми. Відсоток «дзеркалюється та розповсюджується Архівом Анни» показує, скільки файлів ми дзеркалимо самі. Ми розповсюджуємо ці файли оптом через торренти та робимо їх доступними для прямого завантаження через партнерські вебсайти. Огляд Всього Торренти на Анниному Архіві Для отримання додаткової інформації про Sci-Hub, будь ласка, зверніться до його <a %(a_scihub)s>офіційного вебсайту</a>, <a %(a_wikipedia)s>сторінки у Вікіпедії</a> та цього <a %(a_radiolab)s>подкаст-інтерв'ю</a>. Зверніть увагу, що Sci-Hub було <a %(a_reddit)s>заморожено з 2021 року</a>. Раніше його вже заморожували, але у 2021 році було додано кілька мільйонів статей. Проте, деяка обмежена кількість статей все ще додається до колекцій Libgen “scimag”, хоча цього недостатньо для створення нових великих торрентів. Ми використовуємо метадані Sci-Hub, надані <a %(a_libgen_li)s>Libgen.li</a> у його колекції “scimag”. Ми також використовуємо набір даних <a %(a_dois)s>dois-2022-02-12.7z</a>. Зверніть увагу, що торренти “smarch” є <a %(a_smarch)s>застарілими</a> і тому не включені до нашого списку торрентів. Торренти на Libgen.li Торренти на Libgen.rs Метадані та торренти Оновлення на Reddit Подкаст-інтерв'ю Сторінка у Вікіпедії Sci-Hub Sci-Hub: заморожено з 2021 року; більшість доступна через торренти Libgen.li: незначні додавання з того часу</div> Деякі бібліотеки-джерела сприяють масовому обміну своїми даними через торренти, тоді як інші неохоче діляться своєю колекцією. У останньому випадку, Архів Анни намагається скрапити їхні колекції та зробити їх доступними (дивіться нашу сторінку <a %(a_torrents)s>Торренти</a>). Існують також проміжні ситуації, наприклад, коли бібліотеки-джерела готові ділитися, але не мають ресурсів для цього. У таких випадках ми також намагаємося допомогти. Нижче наведено огляд того, як ми взаємодіємо з різними бібліотеками-джерелами. Бібліотеки джерел %(icon)s Різні бази даних файлів, розкидані по китайському інтернету; часто платні бази даних %(icon)s Більшість файлів доступні лише за допомогою преміум-акаунтів BaiduYun; низька швидкість завантаження. %(icon)s Архів Анни керує колекцією <a %(duxiu)s>файлів DuXiu</a> %(icon)s Різні бази даних метаданих розкидані по китайському інтернету; хоча часто це платні бази даних %(icon)s Немає легкодоступних дампів метаданих для всієї їхньої колекції. %(icon)s Архів Анни керує колекцією <a %(duxiu)s>метаданих DuXiu</a> Файли %(icon)s Файли доступні для позики лише на обмеженій основі, з різними обмеженнями доступу %(icon)s Архів Анни керує колекцією <a %(ia)s>файлів IA</a> %(icon)s Деякі метадані доступні через <a %(openlib)s>дампи бази даних Open Library</a>, але вони не охоплюють всю колекцію IA %(icon)s Немає легко доступних дампів метаданих для всієї їхньої колекції %(icon)s Архів Анни керує колекцією <a %(ia)s>метаданих IA</a> Останнє оновлення %(icon)s Архів Анни та Libgen.li спільно керують колекціями <a %(comics)s>коміксів</a>, <a %(magazines)s>журналів</a>, <a %(standarts)s>стандартних документів</a> та <a %(fiction)s>художньої літератури (відокремленої від Libgen.rs)</a>. %(icon)s Торренти нон-фікшн діляться з Libgen.rs (і дзеркаляться <a %(libgenli)s>тут</a>). %(icon)s Щоквартальні <a %(dbdumps)s>дампи бази даних HTTP</a> %(icon)s Автоматизовані торренти для <a %(nonfiction)s>Нон-фікшн</a> та <a %(fiction)s>Фікшн</a> %(icon)s Архів Анни керує колекцією <a %(covers)s>торрентів обкладинок книг</a> %(icon)s Щоденні <a %(dbdumps)s>дампи бази даних HTTP</a> Метадані %(icon)s Щомісячні <a %(dbdumps)s>дампи баз даних</a> %(icon)s Торренти даних доступні <a %(scihub1)s>тут</a>, <a %(scihub2)s>тут</a> і <a %(libgenli)s>тут</a> %(icon)s Деякі нові файли <a %(libgenrs)s>додаються</a> до “scimag” Libgen, але їх недостатньо для створення нових торрентів %(icon)s Sci-Hub заморозив нові файли з 2021 року. %(icon)s Дампи метаданих доступні <a %(scihub1)s>тут</a> і <a %(scihub2)s>тут</a>, а також як частина <a %(libgenli)s>бази даних Libgen.li</a> (яку ми використовуємо) Джерело %(icon)s Різні менші або одноразові джерела. Ми заохочуємо людей спочатку завантажувати до інших тіньових бібліотек, але іноді люди мають колекції, які занадто великі, щоб інші могли їх переглянути, але недостатньо великі, щоб заслуговувати на власну категорію. %(icon)s Недоступні безпосередньо в масовому порядку, захищені від скрапінгу %(icon)s Архів Анни керує колекцією <a %(worldcat)s>метаданих OCLC (WorldCat)</a> %(icon)s Архів Анни та Z-Library спільно керують колекцією <a %(metadata)s>метаданих Z-Library</a> та <a %(files)s>файлів Z-Library</a> Datasets Ми об'єднуємо всі вищезазначені джерела в одну уніфіковану базу даних, яку використовуємо для обслуговування цього вебсайту. Ця уніфікована база даних недоступна безпосередньо, але оскільки Архів Анни є повністю відкритим кодом, її можна досить легко <a %(a_generated)s>згенерувати</a> або <a %(a_downloaded)s>завантажити</a> як бази даних ElasticSearch та MariaDB. Скрипти на цій сторінці автоматично завантажать всі необхідні метадані з вищезазначених джерел. Якщо ви хочете дослідити наші дані перед тим, як запускати ці скрипти локально, ви можете переглянути наші JSON файли, які посилаються на інші JSON файли. <a %(a_json)s>Цей файл</a> є хорошою відправною точкою. Уніфікована база даних Торренти від Архіву Анни перегляд пошук Різні менші або одноразові джерела. Ми заохочуємо людей спочатку завантажувати до інших тіньових бібліотек, але іноді люди мають колекції, які занадто великі, щоб інші могли їх переглянути, але недостатньо великі, щоб заслуговувати на власну категорію. Огляд зі сторінки <a %(a1)s>Datasets</a>. З <a %(a_href)s>aaaaarg.fail</a>. Виглядає досить повним. Від нашого волонтера «cgiym». З <a %(a_href)s><q>ACM Digital Library 2020</q></a> торрента. Має досить високий збіг з існуючими колекціями статей, але дуже мало збігів MD5, тому ми вирішили зберегти його повністю. Скрапінг <q>iRead eBooks</q> (фоне́тично <q>ai rit i-books</q>; airitibooks.com), виконаний волонтером <q>j</q>. Відповідає <q>airitibooks</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>. З колекції <a %(a1)s><q>Бібліотека Олександрії</q></a>. Частково з оригінального джерела, частково з the-eye.eu, частково з інших дзеркал. З приватного торент-сайту книг, <a %(a_href)s>Bibliotik</a> (часто називається «Bib»), книги якого були зібрані в торренти за назвою (A.torrent, B.torrent) і розповсюджені через the-eye.eu. Від нашого волонтера «bpb9v». Для отримання додаткової інформації про <a %(a_href)s>CADAL</a>, дивіться примітки на нашій <a %(a_duxiu)s>сторінці набору даних DuXiu</a>. Більше від нашого волонтера «bpb9v», переважно файли DuXiu, а також папки «WenQu» і «SuperStar_Journals» (SuperStar — компанія, що стоїть за DuXiu). Від нашого волонтера «cgiym», китайські тексти з різних джерел (представлені як підкаталоги), включаючи <a %(a_href)s>China Machine Press</a> (великий китайський видавець). Некитайські колекції (представлені як підкаталоги) від нашого волонтера «cgiym». Скрапінг книг про китайську архітектуру, виконаний волонтером <q>cm</q>: <q>Я отримав це, використовуючи вразливість мережі у видавництві, але ця лазівка вже закрита</q>. Відповідає <q>chinese_architecture</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>. Книги від академічного видавництва <a %(a_href)s>De Gruyter</a>, зібрані з кількох великих торентів. Скрапінг <a %(a_href)s>docer.pl</a>, польського сайту обміну файлами, орієнтованого на книги та інші письмові роботи. Скрапінг виконано наприкінці 2023 року волонтером «p». Ми не маємо хороших метаданих з оригінального сайту (навіть розширень файлів), але ми відфільтрували файли, схожі на книги, і часто могли витягти метадані з самих файлів. DuXiu epubs, безпосередньо з DuXiu, зібрані волонтером «w». Лише нещодавні книги DuXiu доступні безпосередньо через електронні книги, тому більшість з них мають бути нещодавніми. Залишкові файли DuXiu від волонтера «m», які не були у власному форматі PDG DuXiu (основний <a %(a_href)s>набір даних DuXiu</a>). Зібрані з багатьох оригінальних джерел, на жаль, без збереження цих джерел у шляху файлу. <span></span> <span></span> <span></span> Скрапінг еротичних книг, виконаний волонтером <q>do no harm</q>. Відповідає <q>hentai</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>. <span></span> <span></span> Колекція, скрапінгована з японського видавця манги волонтером «t». <a %(a_href)s>Вибрані судові архіви Лунцюань</a>, надані волонтером «c». Скрапінг <a %(a_href)s>magzdb.org</a>, союзника Library Genesis (він пов'язаний на головній сторінці libgen.rs), але який не хотів надавати свої файли безпосередньо. Отримано волонтером «p» наприкінці 2023 року. <span></span> Різні невеликі завантаження, занадто малі для власної підколекції, але представлені як каталоги. Електронні книги з AvaxHome, російського сайту для обміну файлами. Архів газет і журналів. Відповідає <q>newsarch_magz</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>. Скрапінг <a %(a1)s>Центру документації з філософії</a>. Колекція волонтера «o», який збирав польські книги безпосередньо з оригінальних релізів («сцена»). Об'єднані колекції <a %(a_href)s>shuge.org</a> волонтерами «cgiym» і «woz9ts». <span></span> <a %(a_href)s>«Імперська бібліотека Трантора»</a> (названа на честь вигаданої бібліотеки), скрапінгована у 2022 році волонтером «t». <span></span> <span></span> <span></span> Підпідколекції (представлені як каталоги) від волонтера «woz9ts»: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (від <a %(a_sikuquanshu)s>Dizhi(迪志)</a> на Тайвані), mebook (mebook.cc, 我的小书屋, моя маленька книжкова кімната — woz9ts: «Цей сайт головним чином зосереджений на обміні високоякісними файлами електронних книг, деякі з яких були набрані самим власником. Власник був <a %(a_arrested)s>заарештований</a> у 2019 році, і хтось зробив колекцію файлів, якими він поділився.»). Залишилися файли DuXiu від волонтера «woz9ts», які не були у власному форматі PDG DuXiu (ще потрібно конвертувати в PDF). Колекція «завантаження» розділена на менші підколекції, які вказані в AACID та назвах торрентів. Усі підколекції спочатку були дедупліковані з основною колекцією, хоча метадані JSON файлів «upload_records» все ще містять багато посилань на оригінальні файли. Не книжкові файли також були видалені з більшості підколекцій і зазвичай <em>не</em> зазначені в JSON файлах «upload_records». Підколекції: Примітки Підколекція Багато підколекцій самі по собі складаються з під-підколекцій (наприклад, з різних оригінальних джерел), які представлені як каталоги в полях «filepath». Завантаження до Архіву Анни Наш блог-пост про ці дані <a %(a_worldcat)s>WorldCat</a> — це власна база даних некомерційної організації <a %(a_oclc)s>OCLC</a>, яка агрегує метадані з бібліотек по всьому світу. Ймовірно, це найбільша колекція бібліотечних метаданих у світі. У жовтні 2023 року ми <a %(a_scrape)s>випустили</a> комплексний скрапінг бази даних OCLC (WorldCat) у <a %(a_aac)s>форматі контейнерів Анниного Архіву</a>. Жовтень 2023, початковий випуск: OCLC (WorldCat) Торренти від Анниного Архіву Приклад запису в Архіві Анни (оригінальна колекція) Приклад запису в Архіві Анни (колекція «zlib3») Торренти від Архіву Анни (метадані + контент) Публікація в блозі про Реліз 1 Публікація в блозі про Реліз 2 Наприкінці 2022 року передбачуваних засновників Z-Library було заарештовано, а домени були конфісковані владою Сполучених Штатів. Відтоді вебсайт повільно повертається в онлайн. Невідомо, хто наразі керує ним. Оновлення станом на лютий 2023 року. Z-Library має свої корені в спільноті <a %(a_href)s>Library Genesis</a> і спочатку використовувала їхні дані. Відтоді вона значно професіоналізувалася і має набагато сучасніший інтерфейс. Тому вони можуть отримувати набагато більше пожертв, як грошових для подальшого покращення свого вебсайту, так і пожертв нових книг. Вони зібрали велику колекцію на додаток до Library Genesis. Колекція складається з трьох частин. Оригінальні сторінки опису для перших двох частин збережені нижче. Вам потрібні всі три частини, щоб отримати всі дані (за винятком застарілих торентів, які викреслені на сторінці торентів). %(title)s: наш перший реліз. Це був самий перший реліз того, що тоді називалося «Піратське дзеркало бібліотеки» («pilimi»). %(title)s: другий реліз, цього разу з усіма файлами, упакованими в .tar файли. %(title)s: інкрементні нові релізи, використовуючи <a %(a_href)s>формат контейнерів Архіву Анни (AAC)</a>, тепер випущені у співпраці з командою Z-Library. Початкове дзеркало було ретельно отримано протягом 2021 і 2022 років. На даний момент воно трохи застаріле: воно відображає стан колекції в червні 2021 року. Ми оновимо це в майбутньому. Зараз ми зосереджені на випуску цього першого релізу. Оскільки Library Genesis вже збережено за допомогою публічних торрентів і включено до Z-Library, ми провели базову дедуплікацію проти Library Genesis у червні 2022 року. Для цього ми використовували MD5-хеші. Ймовірно, у бібліотеці є багато дубльованого контенту, наприклад, кілька форматів файлів з однією і тією ж книгою. Це важко точно виявити, тому ми цього не робимо. Після дедуплікації у нас залишилося понад 2 мільйони файлів, загальним обсягом трохи менше 7 ТБ. Колекція складається з двох частин: дампу метаданих MySQL у форматі “.sql.gz” та 72 торрент-файлів обсягом приблизно 50-100 ГБ кожен. Метадані містять дані, як повідомляється на вебсайті Z-Library (назва, автор, опис, тип файлу), а також фактичний розмір файлу та md5sum, які ми спостерігали, оскільки іноді ці дані не збігаються. Здається, є діапазони файлів, для яких Z-Library має неправильні метадані. У деяких окремих випадках ми також могли неправильно завантажити файли, що ми спробуємо виявити та виправити в майбутньому. Великі торрент-файли містять фактичні дані книг, з ідентифікатором Z-Library як ім'ям файлу. Розширення файлів можна відновити за допомогою дампу метаданих. Колекція є сумішшю нон-фікшн та художнього контенту (не розділеного, як у Library Genesis). Якість також дуже різниться. Цей перший реліз тепер повністю доступний. Зверніть увагу, що торрент-файли доступні лише через наше дзеркало в Tor. Реліз 1 (%(date)s) Це один додатковий торрент-файл. Він не містить нової інформації, але має деякі дані, які можуть зайняти деякий час для обчислення. Це робить його зручним, оскільки завантаження цього торрента часто швидше, ніж обчислення з нуля. Зокрема, він містить індекси SQLite для tar-файлів, для використання з <a %(a_href)s>ratarmount</a>. Реліз 2 додаток (%(date)s) Ми отримали всі книги, які були додані до Z-Library між нашим останнім дзеркалом і серпнем 2022 року. Ми також повернулися і зібрали деякі книги, які пропустили вперше. Загалом, ця нова колекція становить близько 24 ТБ. Знову ж таки, ця колекція дедуплікована проти Library Genesis, оскільки для цієї колекції вже доступні торренти. Дані організовані подібно до першого релізу. Є дамп метаданих MySQL у форматі “.sql.gz”, який також включає всі метадані з першого релізу, тим самим замінюючи його. Ми також додали кілька нових колонок: Ми згадували про це минулого разу, але для уточнення: “filename” і “md5” є фактичними властивостями файлу, тоді як “filename_reported” і “md5_reported” — це те, що ми зібрали з Z-Library. Іноді ці два значення не збігаються, тому ми включили обидва. Для цього релізу ми змінили кодування на “utf8mb4_unicode_ci”, що має бути сумісним зі старішими версіями MySQL. Файли даних подібні до минулого разу, хоча вони набагато більші. Ми просто не могли створювати безліч менших торрент-файлів. “pilimi-zlib2-0-14679999-extra.torrent” містить усі файли, які ми пропустили в останньому релізі, тоді як інші торренти містять нові діапазони ID.  <strong>Оновлення %(date)s:</strong> Ми зробили більшість наших торрентів занадто великими, що спричинило проблеми з торрент-клієнтами. Ми видалили їх і випустили нові торренти. <strong>Оновлення %(date)s:</strong> Файлів все ще було занадто багато, тому ми обгорнули їх у tar-файли і знову випустили нові торренти. %(key)s: чи є цей файл вже в Library Genesis, у колекції нон-фікшн або художньої літератури (зіставлено за md5). %(key)s: у якому торренті знаходиться цей файл. %(key)s: встановлено, коли ми не змогли завантажити книгу. Реліз 2 (%(date)s) Релізи Zlib (оригінальні сторінки опису) Tor-домен Основний вебсайт Z-Library scrape «Китайська» колекція в Z-Library, здається, така ж, як і наша колекція DuXiu, але з різними MD5. Ми виключаємо ці файли з торентів, щоб уникнути дублювання, але все одно показуємо їх у нашому пошуковому індексі. Метадані Ви отримали %(percentage)s%% бонус у вигляді швидких завантажень, тому що вас направив користувач %(profile_link)s. Це стосується всього періоду підписки. Зробіть донат Приєднатися Вибрано до %(percentage)s%% знижки Alipay підтримує міжнародні кредитні/дебетові картки. Дивіться <a %(a_alipay)s>цей посібник</a> для отримання додаткової інформації. Надсилайте нам подарункові картки Amazon.com за допомогою кредитної/дебетової картки. Ви можете придбати криптовалюту за допомогою кредитної/дебетової картки. WeChat (Weixin Pay) підтримує міжнародні кредитні/дебетові картки. У додатку WeChat перейдіть в “Me => Services => Wallet => Add a Card”. Якщо ви не бачите цього пунткту, то увімкніть його в “Me => Settings => General => Tools => Weixin Pay => Enable”. (використовуйте при відправці Ethereum з Coinbase) скопійовано! скопіювати (найменша мінімальна сума) (увага: висока мінімальна сума) -%(percentage)s%% 12 місяців 1 місяць 24 місяці 3 місяці 48 місяців 6 місяців 96 місяців Оберіть тривалість на яку ви хочете підписатися. <div %(div_monthly_cost)s></div><div %(div_after)s>після <span %(span_discount)s></span> discounts</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% на 12 місяців на 1 місяць на 24 місяці на 3 місяці на 48 місяців на 6 місяців на 96 місяців %(monthly_cost)s / місяць зв'яжіться з нами Прямі <strong>SFTP</strong> сервери Донати корпоративного рівня або обмін на нові збірки (наприклад: нові скани, набори даних, розпізнані за допомогою OCR). Доступ для експертів Безлімітні<strong></strong> швидкі завантаження <div %(div_question)s>Чи можу я оновити своє членство або отримати кілька членств?</div> <div %(div_question)s>Чи можу я зробити пожертву, не стаючи членом?</div> Звичайно. Ми приймаємо пожертви будь-якої суми на цю адресу Monero (XMR): %(address)s. <div %(div_question)s>Що означають діапазони на місяць?</div> Ви можете досягти нижньої межі діапазону, застосувавши всі знижки, наприклад, обравши період довший за місяць. <div %(div_question)s>Чи поновлюється підписка автоматично?</div> Підписка на сайт <strong>не поновлюється</strong> автоматично. Ви можете підписатися на будь-який період часу. <div %(div_question)s>На що ви витрачаєте донати?</div> 100%% коштів йдуть на збереження та поширення знань і культури світу. Наразі ми витрачаємо їх здебільшого на сервери, засоби зберігання даних та збільшення каналів зв'язку. Жодні гроші не йдуть особисто членам нашої команди. <div %(div_question)s>Чи можу я надіслати велику суму?</div> Так, звичайно! Якщо ви збираєтеся надіслати декілька тисяч доларів чи більше, напишіть нам на %(email)s. <div %(div_question)s>Чи підтримуєте ви інші методи оплати?</div> Наразі ні, адже тіньові архіви, яким являється наш, потребують додаткової безпеки та анонімності. Якщо у вас є ідеї щодо альтернативних безпечних та зручних методів оплати, пишіть нам на %(email)s. Часті запитання про благодійні внески У вас вже є <a %(a_donation)s>існуючий донат</a>. Будь ласка, завершіть або скасуйте його перед тим, як зробити новий. <a %(a_all_donations)s>Переглянути всі мої донати</a> Для донатів понад $5000, будь ласка, зв'яжіться з нами безпосередньо за адресою %(email)s. Ми вітаємо великі донати від спроміжних приватних осіб чи установ.  Зверніть увагу, що хоча членства на цій сторінці є «щомісячними», вони є одноразовими донатами (не повторюються). Дивіться <a %(faq)s>Часті запитання про донати</a>. Архів Анни - це некомерційний проєкт з відкритим кодом і відкритими даними. Задонативши та стаючи учасником, ви підтримуєте нашу діяльність та розвиток. Усім нашим учасникам: дякуємо, що продовжуєте нас підтримувати! ❤️ Для отримання додаткової інформації перегляньте <a %(a_donate)s>Часті запитання про донати</a>. Щоб стати учасником <a %(a_login)s>Увійдіть або Зареєструйтесь</a>. Дякуємо за вашу підтримку! $%(cost)s / місяць Якщо ви зробили помилку під час оплати, ми не можемо повернути кошти, але спробуємо це вирішити. Знайдіть сторінку "Криптовалюта" у вашому додатку PayPal або на веб-сайті. Зазвичай вона знаходиться у розділі "Фінанси". Перейдіть на сторінку "Bitcoin" у вашому додатку PayPal або на сайті. Натисніть кнопку "Переказати" %(transfer_icon)s, а потім "Відправити". Alipay Alipay 支付宝 / WeChat 微信 Подарункува картка Amazon Подарункова картка %(amazon)s Банківська картка Банківська картка (використовуючи додаток) Бінанс Кредитна/дебетова/Apple/Google (BMC) Cash App Кредитна/дебетова картка Кредитна/дебетова картка 2 Кредитна/дебетова картка (резервний варіант) Криптовалюта %(bitcoin_icon)s Картка / PayPal / Venmo PayPal (США) %(bitcoin_icon)s PayPal PayPal (звичайний) Pix (Бразилія) Revolut (тимчасово недоступно) WeChat Оберіть криптовалюту, якій ви надаєте перевагу: Донатьте використовуючи подарункову картку Амазон. <strong>ВАЖЛИВО:</strong> Цей варіант для %(amazon)s. Якщо ви хочете використовувати інший вебсайт Amazon, виберіть його вище. <strong>ВАЖЛИВО:</strong> Ми підтримуємо тільки Amazon.com, інші вебсторінки Amazon, для прикладу: .de, .co.uk, .ca, не підтримуються. Будь ласка, НЕ пишіть своїх повідомлень. Введіть точну суму: %(amount)s Зауважте, що нам треба округляти до значень, що приймаються нашими посередниками (мінімум %(minimum)s). Зробіть донат, використовуючи кредитну/дебетову картку, через додаток Alipay (дуже легко налаштувати). Встановіть додаток Alipay з <a %(a_app_store)s>Apple App Store</a> або <a %(a_play_store)s>Google Play Store</a>. Зареєструйтесь, використовуючи свій номер телефону. Додаткові особисті дані не потрібні. <span %(style)s>1</span>Встановіть додаток Alipay Підтримуються: Visa, MasterCard, JCB, Diners Club і Discover. Дивіться <a %(a_alipay)s>цей посібник</a> для отримання додаткової інформації. <span %(style)s>2</span>Додайте банківську картку З Binance ви купуєте Bitcoin за допомогою кредитної/дебетової картки або банківського рахунку, а потім донатите цей Bitcoin нам. Таким чином, ми можемо залишатися захищеними та анонімними при прийнятті вашого донату. Binance доступний майже в кожній країні та підтримує більшість банків і кредитних/дебетових карток. Це наразі наша основна рекомендація. Ми вдячні вам за те, що ви витратили час на вивчення цього способу зробити донат, оскільки це дуже допомагає нам. Для кредитних карток, дебетових карток, Apple Pay та Google Pay ми використовуємо “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). У їхній системі одна “кава” дорівнює $5, тому ваша пожертва буде округлена до найближчого кратного 5. Донатьне використовуючи Cash App. Якщо вас є Cash App, то це найпростіший спосіб задонатити! Зауважте, що для транзакцій менше %(amount)s, Cash App може стягувати додаткову платню в розмірі %(fee)s . Для %(amount)s чи вище це безкоштовно! Донатьте використовуючи кредитну або дебетову картку. Цей метод використовує постачальника криптовалют як проміжну конверсію. Це може бути трохи заплутано, тому, будь ласка, використовуйте цей метод лише якщо інші способи оплати не працюють. Він також не працює у всіх країнах. Ми не можемо підтримувати кредитні/дебетові картки безпосередньо, тому що банки не хочуть з нами працювати. ☹ Однак, є кілька способів використання кредитних/дебетових карток через інші платіжні методи: За допомогою криптовалюти ви можете задонатити використовуючи BTC, ETH, XMR та SOL. Використовуйте цей варіант, якщо ви вже добре обізнані щодо криптовалюти. Ви можете донатити за допомогою криптовалют використовуючи BTC, ETH, XMR та інші. Крипто експрес-послуги Якщо ви використовуєте криптовалюту вперше, ми рекомендуємо скористатися %(options)s щоб купити та задонатити Bitcoin (оригінальної та найпоширенішої криптовалюти). Зауважте, додаткова плата, що стягується при оплаті кредитною карткою, при оплаті маленьких донатів, може знівелювати нашу %(discount)s%% знижку, тому ми рекомендуємо довготривалі підписки. Зробіть донат за допомогою кредитної/дебетової картки, PayPal або Venmo. Ви можете вибрати між ними на наступній сторінці. Google Pay та Apple Pay також можуть спрацювати. Зауважте, додаткова плата, що стягується при оплаті маленьких донатів велика, тому ми рекомендуємо довготривалі підписки. Щоб задонатити за допомогою PayPal (США), ми будемо використовувати PayPal Crypto, який дозволяє нам залишатися анонімними. Ми вдячні вам за те, що ви знайшли час, аби навчитися робити внески за допомогою цього методу, оскільки це дуже допомагає нам. Донатьте використовуючи PayPal. Зробіть донат за допомогою вашого звичайного облікового запису PayPal. Задонатьте за допомогою Revolut. Якщо у вас є Revolut, це найпростіший спосіб зробити донат! Цей спосіб оплати дозволяє тільки максимум %(amount)s. Будь ласка, оберіть іншу тривалість або спосіб оплати. Цей спосіб оплати потребує мінімум %(amount)s. Будь ласка, оберіть іншу тривалість або спосіб оплати. Бінанс Coinbase Kraken Будь ласка, оберіть спосіб оплати. "Підписати торрент": ваше ім'я користувача або повідомлення у назві торрент-файлу <div %(div_months)s>один раз на 12 місяців підписки</div> Ваше ім'я користувача або анонімне згадування в подяках Ранній доступ до нових функцій Приватний Telegram про найсвіжіші оновлення %(number)s швидких завантажень на день якщо ви зробите пожертву цього місяця! <a %(a_api)s>Доступ до JSON API</a> Легендарний статус у збереженні знань і культури людства Попередні переваги та додатково: Отримайте <strong>%(percentage)s%% бонусних завантажень</strong>, <a %(a_refer)s>запросивши друзів</a>. SciDB папери <strong>безлімітні</strong> без підтвердження При зверненні з питаннями щодо облікового запису або пожертв, додайте свій ідентифікатор облікового запису, скріншоти, квитанції, якомога більше інформації. Ми перевіряємо нашу електронну пошту лише кожні 1-2 тижні, тому відсутність цієї інформації затримає будь-яке вирішення. Щоб отримати ще більше завантажень, <a %(a_refer)s>запрошуйте друзів</a>! У нас невелика команда волонтерів. Нам може знадобитися 1-2 тижні, щоб відповісти. Зверніть увагу, що назва акаунта або його зображення можуть виглядати дещо дивно. Не хвилюйтеся! Цими акаунтами керують наші партнери по збору внесків. Наші акаунти не були зламані. Задонатити <span %(span_cost)s></span> <span %(span_label)s></span> на 12 місяців “%(tier_name)s” на 1 місяць “%(tier_name)s” на 24 місяці “%(tier_name)s” на 3 місяці “%(tier_name)s” на 48 місяців “%(tier_name)s” на 6 місяців “%(tier_name)s” на 96 місяців “%(tier_name)s” Ви все ще можете скасувати цей донат під час оформлення. Натисніть кнопку «Задонатити», щоб підтвердити цей внесок. <strong>Важлива інформація:</strong> Ціни на криптовалюту можуть сильно коливатися, іноді навіть на 20%% за кілька хвилин. Проте це все-таки значно менше, ніж комісія багатьох платіжних провайдерів, які часто стягують 50-60%% за роботу з такими "тіньовими благодійними організаціями", як ми. <u>Якщо ви надішлете нам квитанцію з оригінальною сумою, яку ви сплатили, ми нарахуємо на ваш рахунок кошти за обрану вами підписку</u> (за умови, що квитанція не була виписана раніше, ніж кілька годин тому). Ми дуже цінуємо, що ви готові миритися з такими речами, аби підтримати нас! ❤️ ❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. <span %(span_circle)s>1</span>Купити Bitcoin на Paypal <span %(span_circle)s>2</span>Надіслати Bitcoin на нашу адресу ✅ Перенаправлення на сторінку донатів… Задонатити Будь ласка, зачекайте принаймні <span %(span_hours)s>24 години</span> (і оновіть цю сторінку), перш ніж зв’язуватися з нами. Якщо ви хочете зробити пожертву (будь-яку суму) без членства, скористайтеся цією адресою Monero (XMR): %(address)s. Після відправки вашої подарункової картки, наша система підтвердить це за декілька хвилин. Якщо це не спрацює, спробуйте відправити вашу подарункову картку знову– (<a %(a_instr)s>інструкція</a>). Якщо це все ще не буде працювати, будь ласка, напишіть нам електронного листа і Анна вручну перевірить (це може зайняти декілька днів), а також зазначте, якщо ви вже спробували відправити знову. Приклад: Будь ласка, використовуйте <a %(a_form)s>офіційну форму Amazon.com form</a> щоб відправити нам подарункову картку %(amount)s на електронну скриньку нижче. У поле “Кому” введіть цю електронну скриньку отримувача: Подарункова карта Amazon Ми не приймаємо інші способи розрахунку подарунковими картками, <strong> надсилайте лише безпосередньо з офіційної форми на Amazon.com</strong>. Ми не можемо повернути вашу подарункову картку, якщо ви не використайте цю форму. Використати лише один раз. Унікальне для вашого аккаунту, не поширюйте. Очікуємо на подарункову картку … (оновіть сторінку щоб перевірити) Відкрийте <a %(a_href)s>сторінку донатів з QR-кодом</a>. Скануйте QR-код за допомогою додатка Alipay або натисніть кнопку, щоб відкрити додаток Alipay. Будь ласка, будьте терплячими; сторінка може завантажуватися деякий час, оскільки вона знаходиться в Китаї. <span %(style)s>3</span>Зробіть донат (скануйте QR-код або натисніть кнопку) Купуйте PYUSD монети на PayPal Купити Bitcoin (BTC) у Cash App Купіть трохи більше (ми рекомендуємо %(more)s більше), ніж сума, яку ви донатите (%(amount)s), щоб покрити комісії за транзакції. Ви збережете все, що залишиться. Перейдіть на сторінку «Bitcoin» (BTC) у Cash App. Переведіть Bitcoin на нашу адресу Для невеликих донатів (менше $25) вам, можливо, доведеться використовувати Rush або Priority. Натисніть кнопку «Send bitcoin», щоб зробити «виведення». Перемкніть з доларів на BTC, натиснувши на іконку %(icon)s. Введіть суму BTC нижче та натисніть «Send». Дивіться <a %(help_video)s>це відео</a>, якщо застрягнете. Експрес-послуги зручні, але стягують вищі комісії. Ви можете використовувати це замість криптообміну, якщо хочете швидко зробити більший донат і не проти комісії в $5-10. Обов’язково надішліть точну суму криптовалюти, вказану на сторінці донатів, а не суму в $USD. Інакше комісія буде вирахувана, і ми не зможемо автоматично обробити ваше членство. Іноді підтвердження може зайняти до 24 годин, тому обов’язково оновіть цю сторінку (навіть якщо вона закінчилася). Вказівки щодо донатів кредитною / дебетовою карткою Донатьте через нашу сторінку оплати кредитною чи дебетовою карткою Деякі з кроків містять згадки про криптогаманці, не хвилюйтесь, вам не треба нічого вивчати про криптовалюти для цього. %(coin_name)s інструкції Скануйте цей QR -код за допомогою програми Crypto Wallet, щоб швидко заповнити дані про платіж Сканувати QR -код для оплати Ми підтримуємо тільки стандартні варіанти криптовалют, а не екзотичні варіанти криптовалют чи версії коїнів. Підтвердження транзакції може зайняти до години, в залежності від обраної криптовалюти. Донатьте %(amount)s на <a %(a_page)s>цій сторінці</a>. Цей донат закінчився. Будь ласка, скасуйте та створіть новий. Якщо ви вже сплатили: Так, я надіслав квитанцію на електронну пошту Обов'язково додайте квитанцію з початковим курсом обміну криптовалюти, якщо курс коливався під час транзакції. Ми дуже цінуємо, що ви використовуєте криптовалюту, це дуже допомагає нам! ❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. <span %(span_circle)s>%(circle_number)s</span>Надішліть нам квитанцію на електронну пошту Якщо ви зіткнулися з проблемами, будь ласка, напишіть на %(email)s та прикрипіть якомога більше інформації (наприклад, знімки екрану). ✅ Дякуємо за ваш внесок! Анна особисто активує вашу підписку на сайті в найближчі кілька днів. Надішліть для підтверждення квитанцію або скріншот на вашу особисту адресу: Коли ви надішлете квитанцію на електронну пошту, натисніть цю кнопку, аби Анна могла особисто переглянути її (це може зайняти декілька днів): Надішліть квитанцію або скріншот на вашу особисту адресу для верифікації. НЕ використовуйте цю електронну адресу для вашого донату через PayPal. Скасувати Так, скасуйте, будь ласка Ви впевнені, що хочете скасувати? Не скасовуйте, якщо ви вже зробили оплату. ❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. Створити новий донат ✅ Ваш донат скасовано. Дата: %(date)s Ідентифікатор: %(id)s Повторний платіж Статус: <span %(span_label)s>%(label)s</span> Разом: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / місяць на %(duration)s місяці(в), враховуючи %(discounts)s%% знижки)</span> Разом: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / місяць на %(duration)s місяці(в))</span> 1. Введіть вашу електронну пошту. 2. Оберіть ваш спосіб оплати. 3. Оберіть ваш спосіб оплати знову. 4. Оберіть “Self-hosted” гаманець. 5. Натистіть “Я підтверджую власність”. 6. Ви отримуєте електронного листа. Будь ласка, відправте його нам і ми підтвердимо ваш донат, якомога швидче. (ви можете скасувати та створити новий донат) Наразі вказівки по оплаті застарілі. Якщо ви хочете зробити ще один внесок, скористайтеся кнопкою "Повторний платіж" вище. Ви вже здійснили оплату. Якщо ви хочете переглянути платіжні вказівки, натисніть тут: Показати старі платіжні вказівки Якщо сторінка донатів заблокована, спробуйте інше інтернет-з’єднання (наприклад, VPN або мобільний інтернет). На жаль, сторінка Alipay часто доступна лише з <strong>материкового Китаю</strong>. Можливо, вам доведеться тимчасово вимкнути свій VPN або використовувати VPN до материкового Китаю (іноді також працює Гонконг). <span %(span_circle)s>1</span>Задонатити через Alipay Зробіть донат на загальну суму %(total)s за допомогою <a %(a_account)s>цього Alipay акаунту</a> Вказівки щодо використання Alipay <span %(span_circle)s>1</span>Переказати на один з наших криптовалютних рахунків Переказати загальну суму %(total)s на одну з цих адрес: Вказівки щодо використання криптовалюти Дотримуйтесь вказівок, аби купити Bitcoin (BTC). Вам потрібно купити лише ту суму, на яку ви бажаєте зробити внесок, %(total)s. Введіть нашу Bitcoin (BTC) адресу в якості одержувача і дотримуйтесь вказівок, аби відправити ваш донат у розмірі %(total)s: <span %(span_circle)s>1</span>Задонатити через Pix Переказати загальну суму %(total)s через <a %(a_account)s>цей Pix рахунок Вказівки щодо використання Pix <span %(span_circle)s>1</span>Зробіть донат через WeChat Зробіть донат на загальну суму %(total)s за допомогою <a %(a_account)s>цього WeChat акаунту</a> Інструкції для WeChat Використовуйте будь-яку з наступних експрес-служб «кредитна картка до Bitcoin», які займають лише кілька хвилин: Адреса BTC / Bitcoin (зовнішній гаманець): Сума BTC / Bitcoin: Заповніть наступні дані у формі: Якщо будь-яка з цієї інформації застаріла, будь ласка, напишіть нам електронного листа, щоб повідомити нас. Будь ласка, використовуйте цю <span %(underline)s>точну суму</span>. Ваша загальна вартість може бути вищою через комісії за кредитні картки. Для невеликих сум це може бути більше, ніж наша знижка, на жаль. (мінімум: %(minimum)s, без перевірки для першої транзакції) (мінімум: %(minimum)s) (мінімум: %(minimum)s) (мінімум: %(minimum)s, без перевірки для першої транзакції) (мінімум: %(minimum)s) (мінімум: %(minimum)s залежно від країни, без перевірки для першої транзакції) Дотримуйтесь інструкцій, щоб купити PYUSD coin (PayPal USD). Купуйте трохи більше (ми рекомендуємо на %(more)s більше), ніж сума яку ви донатите (%(amount)s), щоб покрити додаткову комісію. Ви збережете все, що залишилося. Перейдіть на “PYUSD” сторінку вашого PayPal додатку чи вебсторінки. Натисніть на кнопку “Переказ” %(icon)s, а потім “Відправити”. Статус оновлення Щоб перезапустити таймер, просто створіть новий донат. Обов’язково використовуйте суму в BTC нижче, <em>НЕ</em> євро чи долари, інакше ми не отримаємо правильну суму і не зможемо автоматично підтвердити ваше членство. Купити Bitcoin (BTC) у Revolut Купіть трохи більше (ми рекомендуємо %(more)s більше), ніж сума, яку ви донатите (%(amount)s), щоб покрити комісії за транзакції. Ви збережете все, що залишиться. Перейдіть на сторінку «Crypto» у Revolut, щоб купити Bitcoin (BTC). Переведіть Bitcoin на нашу адресу Для невеликих донатів (менше $25) вам, можливо, доведеться використовувати Rush або Priority. Натисніть кнопку «Send bitcoin», щоб зробити «виведення». Перемкніть з євро на BTC, натиснувши на іконку %(icon)s. Введіть суму BTC нижче та натисніть «Send». Дивіться <a %(help_video)s>це відео</a>, якщо застрягнете. Статус: 1 2 Покрокова інструкція Дивіться покрокову інструкцію нижче. В іншому випадку ви можете бути заблоковані від цього облікового запису! Якщо ви цього ще не зробили, запишіть свій секретний ключ для входу: Дякуємо за ваш донат! Часу залишилось: Донат Перекажіть %(amount)s на %(account)s Очікуємо підтвердження (оновіть сторінку для перевірки)… Очікуємо на переказ (оновіть сторінку для перевірки)… Раніше Швидкі завантаження за останні 24 години зараховуються у денний ліміт. Завантаження з швидких партнерських серверів позначені %(icon)s. Останні 18 годин Наразі немає завантажених файлів. Завантажені файли не відображаються у відкритому доступі. Весь час вказано в UTC. Завантажені файли Якщо ви завантажили файл одночасно з швидким і повільним завантаженням, він буде показаний двічі. Не хвилюйтеся занадто, багато людей завантажують з вебсайтів, на які ми посилаємося, і дуже рідко виникають проблеми. Однак, щоб бути в безпеці, ми рекомендуємо використовувати VPN (платний) або <a %(a_tor)s>Tor</a> (безкоштовний). Я завантажив "1984" Джорджа Орвелла, чи прийде поліція до мене додому? Ви — Анна! Хто така Анна? У нас є стабільний JSON API для членів, щоб отримати швидке посилання для завантаження: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (документація всередині самого JSON). Для інших випадків використання, таких як ітерація через всі наші файли, створення власного пошуку тощо, ми рекомендуємо <a %(a_generate)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB. Сирові дані можна вручну досліджувати <a %(a_explore)s>через файли JSON</a>. Наш список сирих торрентів також можна завантажити у форматі <a %(a_torrents)s>JSON</a>. Чи є у вас API? Ми не розміщуємо жодних матеріалів, захищених авторським правом. Ми є пошуковою системою і, як така, індексуємо лише метадані, які вже є у відкритому доступі. Завантажуючи з цих зовнішніх джерел, ми рекомендуємо перевірити закони у вашій юрисдикції щодо того, що дозволено. Ми не несемо відповідальності за контент, розміщений іншими. Якщо у вас є скарги на те, що ви бачите тут, найкраще звернутися до оригінального вебсайту. Ми регулярно оновлюємо нашу базу даних їхніми змінами. Якщо ви дійсно вважаєте, що у вас є обґрунтована скарга DMCA, на яку ми повинні відповісти, будь ласка, заповніть <a %(a_copyright)s>форму скарги DMCA / Авторське право</a>. Ми серйозно ставимося до ваших скарг і відповімо вам якомога швидше. Як повідомити про порушення авторських прав? Ось кілька книг, які мають особливе значення для світу тіньових бібліотек і цифрового збереження: Які ваші улюблені книги? Ми також хочемо нагадати всім, що весь наш код і дані є повністю відкритими. Це унікально для проєктів, подібних до нашого — ми не знаємо жодного іншого проєкту з таким же масивним каталогом, який також є повністю відкритим. Ми дуже раді будь-кому, хто вважає, що ми погано керуємо нашим проєктом, взяти наш код і дані та створити власну тіньову бібліотеку! Ми не говоримо це зі злістю чи чимось подібним — ми щиро вважаємо, що це було б чудово, оскільки це підвищило б планку для всіх і краще зберегло б спадщину людства. Мені не подобається, як ви керуєте цим проєктом! Ми б хотіли, щоб люди встановлювали <a %(a_mirrors)s>дзеркала</a>, і ми будемо фінансово підтримувати цю ініціативу. Чим можна допомогти? Так, ми це робимо. Наше натхнення для збору метаданих — це мета Аарона Шварца «одна веб-сторінка для кожної книги, яка коли-небудь була опублікована», для якої він створив <a %(a_openlib)s>Open Library</a>. Цей проєкт досяг успіху, але наша унікальна позиція дозволяє нам отримувати метадані, які вони не можуть. Ще одним натхненням було наше бажання дізнатися <a %(a_blog)s>скільки книг існує у світі</a>, щоб ми могли підрахувати, скільки книг нам ще потрібно врятувати. Чи збираєте ви метадані? Зверніть увагу, що mhut.org блокує певні діапазони IP, тому може знадобитися VPN. <strong>Android:</strong> Натисніть меню з трьома крапками у верхньому правому куті та виберіть "Додати на головний екран". <strong>iOS:</strong> Натисніть кнопку "Поділитися" внизу та виберіть "Додати на головний екран". У нас немає офіційного мобільного додатку, але ви можете встановити цей вебсайт як додаток. Чи є у вас мобільний додаток? Будь ласка, надішліть їх до <a %(a_archive)s>Internet Archive</a>. Вони належним чином збережуть їх. Як я можу пожертвувати книги або інші фізичні матеріали? Як зробити запит на книги? <a %(a_blog)s>Блог Анни</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — регулярні оновлення <a %(a_software)s>Програмне забезпечення Анни</a> — наш відкритий код <a %(a_datasets)s>Набори даних</a> — про дані <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтернативні домени Чи є більше ресурсів про Архів Анни? <a %(a_translate)s>Переклад на Програмне забезпечення Анни</a> — наша система перекладу <a %(a_wikipedia)s>Вікіпедія</a> — більше про нас (будь ласка, допоможіть підтримувати цю сторінку в актуальному стані або створіть одну для своєї мови!) Виберіть потрібні налаштування, залиште поле пошуку порожнім, натисніть "Пошук", а потім додайте сторінку в закладки за допомогою функції закладок вашого браузера. Як зберегти налаштування пошуку? Ми вітаємо дослідників безпеки, які шукають вразливості в наших системах. Ми є великими прихильниками відповідального розкриття інформації. Зв'яжіться з нами <a %(a_contact)s>тут</a>. Наразі ми не можемо надавати винагороди за знайдені помилки, за винятком вразливостей, які мають <a %(a_link)s>потенціал скомпрометувати нашу анонімність</a>, за які ми пропонуємо винагороди в діапазоні $10k-50k. Ми хотіли б у майбутньому розширити сферу дії винагород за знайдені помилки! Зверніть увагу, що атаки соціальної інженерії не входять у сферу дії. Якщо ви зацікавлені в наступальній безпеці та хочете допомогти архівувати знання та культуру світу, обов'язково зв'яжіться з нами. Є багато способів, як ви можете допомогти. Чи є у вас програма відповідального розкриття інформації? Ми буквально не маємо достатньо ресурсів, щоб надати всім у світі високошвидкісні завантаження, як би нам цього не хотілося. Якщо багатий благодійник захоче допомогти нам у цьому, це буде неймовірно, але до того часу ми намагаємося робити все можливе. Ми є неприбутковим проєктом, який ледве підтримується за рахунок пожертв. Ось чому ми впровадили дві системи для безкоштовних завантажень з нашими партнерами: спільні сервери з повільними завантаженнями та трохи швидші сервери з чергою (щоб зменшити кількість людей, які завантажують одночасно). Ми також маємо <a %(a_verification)s>перевірку браузера</a> для наших повільних завантажень, тому що інакше боти та скрапери будуть зловживати ними, що ще більше уповільнить роботу для легітимних користувачів. Зверніть увагу, що при використанні браузера Tor вам може знадобитися налаштувати параметри безпеки. На найнижчому з варіантів, який називається «Стандартний», виклик Cloudflare turnstile проходить успішно. На вищих варіантах, які називаються «Безпечніший» і «Найбезпечніший», виклик не вдається. Для великих файлів іноді повільні завантаження можуть перериватися посередині. Ми рекомендуємо використовувати менеджер завантажень (такий як JDownloader) для автоматичного відновлення великих завантажень. Чому завантаження такі повільні? Часті Питання (ЧаПи) Використовуйте <a %(a_list)s>генератор списку торрентів</a>, щоб створити список торрентів, які найбільше потребують роздачі, в межах ваших обмежень по місцю для зберігання. Так, дивіться сторінку <a %(a_llm)s>даних LLM</a>. Більшість торрентів містять файли безпосередньо, що означає, що ви можете вказати торрент-клієнтам завантажувати лише потрібні файли. Щоб визначити, які файли завантажувати, ви можете <a %(a_generate)s>генерувати</a> наші метадані або <a %(a_download)s>завантажити</a> наші бази даних ElasticSearch та MariaDB. На жаль, деякі колекції торрентів містять файли .zip або .tar у корені, у такому випадку вам потрібно завантажити весь торрент, перш ніж ви зможете вибрати окремі файли. Поки що немає зручних інструментів для фільтрації торрентів, але ми вітаємо ваші внески. (У нас є <a %(a_ideas)s>деякі ідеї</a> для останнього випадку, проте.) Довга відповідь: Коротка відповідь: не так просто. Ми намагаємося мінімізувати дублювання або перекриття між торентами в цьому списку, але це не завжди можливо і сильно залежить від політики бібліотек-джерел. Для бібліотек, які випускають свої власні торенти, це поза нашою компетенцією. Для торентів, випущених Архівом Анни, ми видаляємо дублікати лише на основі MD5-хешу, що означає, що різні версії однієї і тієї ж книги не видаляються. Так. Це насправді PDF та EPUB, вони просто не мають розширення у багатьох наших торрентах. Є два місця, де ви можете знайти метадані для торрент-файлів, включаючи типи/розширення файлів: 1. Кожна колекція або реліз має свої власні метадані. Наприклад, <a %(a_libgen_nonfic)s>торренти Libgen.rs</a> мають відповідну базу метаданих, розміщену на вебсайті Libgen.rs. Ми зазвичай посилаємося на відповідні ресурси метаданих з <a %(a_datasets)s>сторінки набору даних</a> кожної колекції. 2. Ми рекомендуємо <a %(a_generate)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB. Вони містять мапінг для кожного запису в Архіві Анни до відповідних торрент-файлів (якщо доступні), під “torrent_paths” у JSON ElasticSearch. Деякі торрент-клієнти не підтримують великі розміри частин, які мають багато наших торрентів (для нових ми цього більше не робимо — хоча це і відповідає специфікаціям!). Тому спробуйте інший клієнт, якщо зіткнетеся з цим, або поскаржтеся розробникам вашого торрент-клієнта. Я хотів би допомогти з роздачею, але у мене мало місця на диску. Торренти занадто повільні; чи можу я завантажити дані безпосередньо від вас? Чи можу я завантажити лише підмножину файлів, наприклад, лише певною мовою або на певну тему? Як ви обробляєте дублікати в торентах? Чи можу я отримати список торентів у форматі JSON? Я не бачу PDF або EPUB у торентах, тільки бінарні файли? Що робити? Чому мій торрент-клієнт не може відкрити деякі з ваших торрент-файлів / магнітних посилань? Часті питання про торренти Як завантажити нові книги? Будь ласка, перегляньте <a %(a_href)s>цей чудовий проєкт</a>. Чи є у вас моніторинг доступності? Що таке "Архів Анни"? Підпишіться, аби користуватися швидкими завантаженнями. Тепер ми підтримуємо подарункові картки Amazon, кредитні та дебетові картки, криптовалюту, Alipay та WeChat. Сьогодні у вас закінчилися швидкі завантаження. Доступ Погодинні завантаження за останні 30 днів. Середня кількість завантажень за годину: %(hourly)s. Середня кількість завантажень за день: %(daily)s. Ми працюємо з партнерами, аби зробити наші колекції легко та вільно доступними для всіх. Ми віримо, що кожен має право на колективну мудрість людства. І <a %(a_search)s>не за рахунок авторів</a>. Набори даних, що використовуються в Архіві Анни, є повністю відкритими, і їх можна масово ввідтворювати за допомогою торрентів. <a %(a_datasets)s>Дізнатись більше…</a> Довгостроковий архів Повна база даних Пошук Книги, статті, журнали, комікси, бібліотечні записи, метадані, … Весь наш <a %(a_code)s>код</a> і <a %(a_datasets)s>дані</a> повністю відкриті. <span %(span_anna)s>Архів Анни</span> - це некомерційний проєкт з двома цілями: <li><strong>Збереження:</strong> Резервне копіювання всієї інформації та культури людства.</li><li><strong>Доступ:</strong> Зробити цю інформацію та культуру доступною для будь-кого у світі.</li> Ми маємо найбільшу у світі колекцію високоякісних текстових даних. <a %(a_llm)s>Дізнатись більше…</a> Навчальні дані LLM 🪩 Дзеркала: набір волонтерів Якщо ви є власником анонімної платіжної системи, будь ласка, зв'яжіться з нами. Ми також шукаємо людей, які бажають розмістити невеликі оголошення. Усі виручені кошти йдуть на наші зусилля щодо збереження. Збереження За нашими підрахунками, ми зберегли близько <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% усіх книжок світу</a>. Ми зберігаємо книги, газети, комікси, журнали та багато іншого, збираючи ці матеріали з різних <a href="https://en.wikipedia.org/wiki/Shadow_library">тіньових бібліотек</a>, офіційних бібліотек та інших колекцій в одному місці. Всі ці дані зберігаються назавжди, оскільки їх можна легко дублювати масово за допомогою торрентів, що призводить до створення багатьох копій по всьому світу. Деякі тіньові бібліотеки вже роблять це самі (наприклад, Sci-Hub, Library Genesis), тоді як Архів Анни "звільняє" інші бібліотеки, які не пропонують масового поширення (наприклад, Z-Library) або взагалі не є тіньовими бібліотеками (наприклад, Internet Archive, DuXiu). Це широке розповсюдження в поєднанні з відкритим вихідним кодом робить наш веб-сайт стійким до видалення та забезпечує довготривале збереження людських знань і культури. Дізнайтеся більше про <a href="/datasets">наші дані</a>. Якщо ви є <a %(a_member)s>учасником</a>, перевірка браузера не потрібна. 🧬&nbsp;SciDB є продовженням Sci-Hub. SciDB Відкрити DOI Sci-Hub <a %(a_paused)s>призупинив</a> завантаження нових статей. Прямий доступ до %(count)s наукових статей 🧬&nbsp;SciDB є продовженням Sci-Hub, з його знайомим інтерфейсом і прямим переглядом PDF. Введіть свій DOI для перегляду. Ми маємо повну колекцію Sci-Hub, а також нові статті. Більшість можна переглянути безпосередньо через знайомий інтерфейс, подібний до Sci-Hub. Деякі можна завантажити через зовнішні джерела, у такому разі ми показуємо посилання на них. Ви можете дуже допомогти, якщо зможете роздавати торренти. <a %(a_torrents)s>Дізнатись більше …</a> >%(count)s роздавачів <%(count)s роздавачів %(count_min)s–%(count_max)s роздавачів 🤝 Шукаємо волонтерів Як неприбутковий, відкритий проєкт, ми завжди шукаємо людей, які можуть допомогти. Завантаження через IPFS Список за %(by)s, створено<span %(span_time)s>%(time)s</span> Зберегти ❌ Щось пішло не так. Будь ласка, спробуйте ще раз. ✅ Збережено. Будь ласка, перезавантажте сторінку. Список порожній. редагувати Додайте або видаліть файл з цього списку, знайшовши його і відкривши вкладку "Списки". Список Як ми можемо допомогти Видалення дублювання (дедуплікація) Витяг тексту та метаданих OCR Ми можемо надати високошвидкісний доступ до наших повних колекцій, а також до невипущених колекцій. Це доступ на рівні підприємства, який ми можемо надати за пожертви в діапазоні десятків тисяч доларів США. Ми також готові обміняти це на високоякісні колекції, яких у нас ще немає. Ми можемо повернути вам кошти, якщо ви зможете надати нам збагачення наших даних, наприклад: Підтримуйте довгострокове архівування людських знань, отримуючи кращі дані для своєї моделі! <a %(a_contact)s>Зв'яжіться з нами</a>, щоб обговорити, як ми можемо співпрацювати. Добре відомо, що LLM процвітають на високоякісних даних. У нас є найбільша колекція книг, статей, журналів тощо у світі, які є одними з найякісніших текстових джерел. Дані LLM Унікальний масштаб і діапазон Наша колекція містить понад сто мільйонів файлів, включаючи наукові журнали, підручники та журнали. Ми досягаємо цього масштабу, поєднуючи великі існуючі репозиторії. Деякі з наших джерел колекцій вже доступні оптом (Sci-Hub і частини Libgen). Інші джерела ми звільнили самі. <a %(a_datasets)s>Datasets</a> показує повний огляд. Наша колекція включає мільйони книг, статей і журналів до епохи електронних книг. Великі частини цієї колекції вже були розпізнані OCR і вже мають невелике внутрішнє перекриття. Продовжити Якщо ви загубили ключ, будь ласка, <a %(a_contact)s>зв'яжіться з нами</a> і надайте якомога більше інформації. Можливо, вам доведеться тимчасово створити новий обліковий запис, щоб зв'язатися з нами. Будь ласка, <a %(a_account)s>увійдіть</a> щоб переглянути цю сторінку.</a> Аби запобігти створенню спам-ботами великої кількості облікових записів, нам потрібно спочатку перевірити ваш інтернет-браузер. Якщо ви потрапили в нескінченний цикл, рекомендуємо встановити <a %(a_privacypass)s>Privacy Pass</a>. Це також може допомогти вимкнути блокувальники реклами та інші розширення браузера. Увійти / Зареєструватися Архів Анни тимчасово недоступний через технічне обслуговування. Будь ласка, поверніться через годину. Альтернативний автор Альтернативний опис Альтернативне видання Альтернативне розширення Альтернативне ім'я файлу Альтернативний видавець Альтернативна назва дата відкритого джерела Читати більше… опис Шукайте в архіві Анни номер SSNO, CADAL Шукайте в архіві Анни за номером SSID, DuXiu Шукайте в архіві Анни номер DXID, DuXiu Шукайте ISBN в Архіві Анни Шукайте в архіві Анни за номером OCLC (WorldCat) Шукайте Open Library ID в Архіві Анни Онлайн-переглядач Архіву Анни %(count)s зачеплені сторінки Після завантаження: Краща версія цього файлу може бути доступна за посиланням %(link)s Одночасне завантаження декількох торрент-файлів колекція Використовуйте онлайн-інструменти для конвертації між форматами. Рекомендовані інструменти для конвертації: %(links)s Для великих файлів ми рекомендуємо використовувати менеджер завантажень, щоб уникнути переривань. Рекомендовані менеджери завантажень: %(links)s Індекс електронних книг EBSCOhost (лише для фахівців) (також натисніть "GET" зверху) (натисніть "GET" зверху) Зовнішні завантаження <strong>🚀 Швидкі завантаження</strong> На сьогодні у вас залишилося %(remaining)s. Дякуємо за те, що ви підписалися! ❤️ <strong>🚀 Швидкі завантаження</strong> У вас закінчилися швидкі завантаження на сьогодні. <strong>🚀 Швидкі завантаження</strong> Ви вже завантажили цей файл нещодавно. Посилання залишаються дійсними протягом деякого часу. <strong>🚀 Швидкі завантаження</strong> <a %(a_membership)s>Підпишіться</a>, щоб підтримати процес довготривалого збереження книг, документів та іншого. На знак нашої вдячності за вашу підтримку, ви отримаєте швидкі завантаження. ❤️ 🚀 Швидке завантаження 🐢 Повільні завантаження Позичити з Інтернет Архіву IPFS-портал №%(num)d (спробуйте ще раз, якщо завантаження через IPFS не почалося) Library Genesis (.li-версія) Library Genesis (.rs-версія) - художня література Library Genesis (.rs-версія) - академічний розділ їхні оголошення відомі тим, що містять шкідливе програмне забезпечення, тому використовуйте блокувальник реклами або не натискайте на оголошення Amazon «Send to Kindle» djazz «Send to Kobo/Kindle» MagzDB ManualsLib Nexus/STC (Файли Nexus/STC можуть бути ненадійними для завантаження) Не знайдено жодного завантаження. Всі варіанти завантаження мають один і той самий файл і мають бути безпечними у використанні. Тим не менш, завжди будьте обережні, завантажуючи файли з інтернету, особливо з сайтів, що не належать до Архіву Анни. Наприклад, обов'язково оновлюйте свої пристрої. (без перенаправлення) Відкрити у нашому переглядачі (відкрити у переглядачі) Опція #%(num)d: %(link)s %(extra)s Знайти оригінальний запис у CADAL Шукати вручну на DuXiu Знайти оригінальний запис в ISBNdb Знайти оригінальний запис у WorldCat Знайти оригінальний запис в Open Library Шукайте ISBN в багатьох інших базах даних (друк тільки для патронів) PubMed Вам знадобиться рідер для електронних книг або PDF, щоб відкрити файл, залежно від формату файлу. Рекомендовані рідери для електронних книг: %(links)s Архів Анни 🧬 SciDB Sci-Hub: %(doi)s (пов'язане DOI може бути недоступний у Sci-Hub) Ви можете відправляти як PDF, так і EPUB файли на ваш Kindle або Kobo eReader. Рекомендовані інструменти: %(links)s Більше інформації у <a %(a_slow)s>ЧаПи</a>. Підтримуйте авторів та бібліотеки Якщо вам це подобається і ви можете собі це дозволити, розгляньте можливість придбання оригіналу або підтримки авторів безпосередньо. Якщо це доступно у вашій місцевій бібліотеці, розгляньте можливість безкоштовно взяти його там. Завантаження з партнерського сервера тимчасово недоступні для цього файлу. торрент Від надійних партнерів. Z-Library Z-Library (TOR-версія) (потребує входу через TOR Browser) показати зовнішні завантаження <span class="font-bold">❌ Цей файл має певні проблеми та був прихований в оригінальному джерелі.</span> Іноді таке стається через запит на видалення файлу через конфлікт авторських прав, іноді через наявність кращої альтернативи, але іноді це стається через неправильне форматування чи підозрілість самого файлу. Ми рекомендуємо пошукати альтернативний файл, але ви можете завантажити цей при бажанні. Більше інформації: Якщо ви все ж таки хочете завантажити цей файл, використовуйте перевірене програмне забезпечення останньої версії для його відкриття. коментарі до метаданих AA: Шукайте в архіві Анни “%(name)s” Дослідник кодів: Переглянути в Досліднику кодів “%(name)s” URL-адреса: Веб-сайт: Якщо у вас є цей файл, але його ще немає в архіві Анни, подумайте про те, щоб <a %(a_request)s>завантажити його</a>. Internet Archive Controlled Digital Lending файл “%(id)s” Це запис файлу з Інтернет-архіву, а не файл, який можна завантажити безпосередньо. Ви можете спробувати позичити книгу (посилання нижче) або використовувати цю URL-адресу, щоб <a %(a_request)s>зробити запит на файл</a>. Покращити метадані CADAL SSNO %(id)s запис метаданих Це запис метаданих, а не файл, який можна завантажити безпосередньо. Ви можете спробувати позичити книгу (посилання нижче) або використовувати цю URL-адресу, щоб <a %(a_request)s>зробити запит на файл</a>. DuXiu SSID %(id)s запис метаданих Запис метаданих ISBNdb %(id)s Запис метаданих MagzDB ID %(id)s Запис метаданих Nexus/STC ID %(id)s Номер OCLC (WorldCat) %(id)s запис про метадані Запис метаданих Open Library %(id)s Sci-Hub файл “%(id)s” Не знайдено MD5-суму "%(md5_input)s" не знайдено у нашій базі даних. Додати коментар (%(count)s) Ви можете отримати md5 з URL, наприклад MD5 кращої версії цього файлу (якщо застосовно). Заповніть це, якщо є інший файл, який точно відповідає цьому файлу (те саме видання, те саме розширення файлу, якщо ви можете знайти), який люди повинні використовувати замість цього файлу. Якщо ви знаєте кращу версію цього файлу за межами Архіву Анни, будь ласка, <a %(a_upload)s>завантажте її</a>. Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. Ви залишили коментар. Може знадобитися деякий час, щоб він з'явився. Будь ласка, скористайтеся <a %(a_copyright)s>формою претензії DMCA / авторських прав</a>. Опишіть проблему (обов'язково) Якщо цей файл має високу якість, ви можете обговорити його тут! Якщо ні, будь ласка, скористайтеся кнопкою «Повідомити про проблему з файлом». Відмінна якість файлу (%(count)s) Якість файлу Дізнайтеся, як <a %(a_metadata)s>покращити метадані</a> для цього файлу самостійно. Опис проблеми Будь ласка, <a %(a_login)s>увійдіть</a>. Мені дуже сподобалася ця книга! Допоможіть спільноті, повідомивши про якість цього файлу! 🙌 Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз. Повідомити про проблему з файлом (%(count)s) Дякуємо за подання вашого звіту. Він буде показаний на цій сторінці, а також перевірений вручну Анною (поки у нас не буде належної системи модерації). Залишити коментар Надіслати звіт Що не так з цим файлом? Позичання (%(count)s) Коментарі (%(count)s) Завантаження (%(count)s) Дослідити метадані (%(count)s) Списки (%(count)s) Статистика (%(count)s) Для отримання інформації про цей конкретний файл, перегляньте його <a %(a_href)s>JSON файл</a>. Це файл, керований бібліотекою <a %(a_ia)s>Цифрового Контрольованого Позичання IA</a> і індексований Анною для пошуку. Для отримання інформації про різні Datasets, які ми зібрали, дивіться <a %(a_datasets)s>сторінку Datasets</a>. Метадані з пов'язаного запису Покращити метадані на Open Library «MD5 файлу» — це хеш, який обчислюється з вмісту файлу і є досить унікальним на основі цього вмісту. Усі тіньові бібліотеки, які ми індексували тут, в основному використовують MD5 для ідентифікації файлів. Файл може з'являтися в декількох тіньових бібліотеках. Для отримання інформації про різні Datasets, які ми зібрали, дивіться <a %(a_datasets)s>сторінку Datasets</a>. Повідомити про якість файлу Загальна кількість завантажень: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Чеські метадані %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Попередження: кілька пов'язаних записів: Коли ви дивитеся на книгу в Архіві Анни, ви можете побачити різні поля: назва, автор, видавець, видання, рік, опис, ім'я файлу та інше. Вся ця інформація називається <em>метадані</em>. Оскільки ми об'єднуємо книги з різних <em>бібліотек-джерел</em>, ми показуємо будь-які метадані, доступні в цій бібліотеці-джерелі. Наприклад, для книги, яку ми отримали з Library Genesis, ми покажемо назву з бази даних Library Genesis. Іноді книга присутня в <em>декількох</em> бібліотеках-джерелах, які можуть мати різні поля метаданих. У такому випадку ми просто показуємо найдовшу версію кожного поля, оскільки вона, ймовірно, містить найбільш корисну інформацію! Ми все одно покажемо інші поля під описом, наприклад, як «альтернативна назва» (але тільки якщо вони відрізняються). Ми також витягуємо <em>коди</em>, такі як ідентифікатори та класифікатори, з бібліотеки-джерела. <em>Ідентифікатори</em> унікально представляють конкретне видання книги; прикладами є ISBN, DOI, Open Library ID, Google Books ID або Amazon ID. <em>Класифікатори</em> групують разом кілька схожих книг; прикладами є Dewey Decimal (DCC), UDC, LCC, RVK або GOST. Іноді ці коди явно пов'язані в бібліотеках-джерелах, а іноді ми можемо витягти їх з імені файлу або опису (переважно ISBN та DOI). Ми можемо використовувати ідентифікатори для пошуку записів у <em>колекціях, що містять лише метадані</em>, таких як OpenLibrary, ISBNdb або WorldCat/OCLC. У нашій пошуковій системі є спеціальна <em>вкладка метаданих</em>, якщо ви хочете переглянути ці колекції. Ми використовуємо відповідні записи для заповнення відсутніх полів метаданих (наприклад, якщо відсутня назва), або, наприклад, як «альтернативна назва» (якщо існує існуюча назва). Щоб побачити, звідки саме взялися метадані книги, дивіться <em>вкладку «Технічні деталі»</em> на сторінці книги. Вона містить посилання на сирий JSON для цієї книги з вказівками на сирий JSON оригінальних записів. Для отримання додаткової інформації дивіться наступні сторінки: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Пошук (вкладка метаданих)</a>, <a %(a_codes)s>Дослідник кодів</a> та <a %(a_example)s>Приклад метаданих JSON</a>. Нарешті, всі наші метадані можуть бути <a %(a_generated)s>згенеровані</a> або <a %(a_downloaded)s>завантажені</a> як бази даних ElasticSearch та MariaDB. Інформація Ви можете допомогти збереженню книг, покращуючи метадані! Спочатку прочитайте інформацію про метадані на Архіві Анни, а потім дізнайтеся, як покращити метадані через зв’язок з Open Library, і отримайте безкоштовне членство на Архіві Анни. Покращити метадані Отже, якщо ви натрапили на файл з поганими метаданими, як його виправити? Ви можете перейти до бібліотеки-джерела і дотримуватися її процедур для виправлення метаданих, але що робити, якщо файл присутній у кількох бібліотеках-джерелах? Є один ідентифікатор, який має особливе значення в Архіві Анни. <strong>Поле annas_archive md5 в Open Library завжди переважає над усіма іншими метаданими!</strong> Давайте спочатку трохи повернемося назад і дізнаємося про Open Library. Open Library була заснована у 2006 році Аароном Шварцем з метою «одна веб-сторінка для кожної книги, яка коли-небудь була опублікована». Це свого роду Вікіпедія для метаданих книг: кожен може її редагувати, вона вільно ліцензована і може бути завантажена в масовому порядку. Це база даних книг, яка найбільше відповідає нашій місії — насправді, Архів Анни був натхненний баченням і життям Аарона Шварца. Замість того, щоб винаходити колесо заново, ми вирішили направити наших волонтерів до Open Library. Якщо ви бачите книгу з неправильними метаданими, ви можете допомогти наступним чином: Зверніть увагу, що це працює лише для книг, а не для наукових статей чи інших типів файлів. Для інших типів файлів ми все ще рекомендуємо знайти вихідну бібліотеку. Може знадобитися кілька тижнів, щоб зміни були включені в Архів Анни, оскільки нам потрібно завантажити останній дамп даних Open Library і відновити наш пошуковий індекс.  Перейдіть на <a %(a_openlib)s>веб-сайт Open Library</a>. Знайдіть правильний запис книги. <strong>УВАГА:</strong> обов’язково виберіть правильне <strong>видання</strong>. В Open Library є «твори» та «видання». «Твір» може бути «Гаррі Поттер і філософський камінь». «Видання» може бути: Перше видання 1997 року, опубліковане Bloomsbery, має 256 сторінок. Видання у м'якій обкладинці 2003 року, опубліковане Raincoast Books, має 223 сторінки. Польський переклад 2000 року «Harry Potter I Kamie Filozoficzn» від Media Rodzina має 328 сторінок. Усі ці видання мають різні ISBN та різний вміст, тому обов’язково виберіть правильне! Редагуйте запис (або створіть його, якщо він не існує), і додайте якомога більше корисної інформації! Ви вже тут, тож зробіть запис дійсно чудовим. У розділі «ID Numbers» виберіть «Архів Анни» і додайте MD5 книги з Архіву Анни. Це довгий рядок літер і цифр після «/md5/» в URL. Спробуйте знайти інші файли в Архіві Анни, які також відповідають цьому запису, і додайте їх також. У майбутньому ми зможемо групувати їх як дублікати на сторінці пошуку Архіву Анни. Коли закінчите, запишіть URL, який ви щойно оновили. Після того, як ви оновите принаймні 30 записів з MD5 з Архіву Анни, надішліть нам <a %(a_contact)s>електронний лист</a> і надішліть нам список. Ми надамо вам безкоштовне членство в Архіві Анни, щоб ви могли легше виконувати цю роботу (і як подяку за вашу допомогу). Ці редагування мають бути високої якості та додавати значну кількість інформації, інакше ваш запит буде відхилено. Ваш запит також буде відхилено, якщо будь-яке з редагувань буде скасовано або виправлено модераторами Open Library. Зв’язок з Open Library Якщо ви значно залучитеся до розробки та операцій нашої роботи, ми можемо обговорити розподіл більшої частини доходів від пожертв з вами, щоб ви могли використовувати їх за потреби. Ми будемо платити за хостинг лише після того, як ви все налаштуєте та продемонструєте, що можете підтримувати архів в актуальному стані з оновленнями. Це означає, що перші 1-2 місяці вам доведеться платити з власної кишені. Ваш час не буде компенсовано (і наш також), оскільки це чисто волонтерська робота. Ми готові покрити витрати на хостинг і VPN, спочатку до $200 на місяць. Цього достатньо для базового пошукового сервера та проксі, захищеного DMCA. Витрати на хостинг Будь ласка, <strong>не зв’язуйтеся з нами</strong> для отримання дозволу або з базовими питаннями. Дії говорять голосніше за слова! Вся інформація є у відкритому доступі, тому просто починайте налаштовувати своє дзеркало. Не соромтеся створювати тікети або запити на злиття в нашому Gitlab, коли стикаєтеся з проблемами. Можливо, нам доведеться створити деякі функції, специфічні для дзеркал, разом з вами, такі як ребрендинг з «Anna’s Archive» на назву вашого вебсайту, (спочатку) відключення облікових записів користувачів або посилання на наш основний сайт зі сторінок книг. Коли ваше дзеркало буде працювати, будь ласка, зв’яжіться з нами. Ми будемо раді переглянути вашу операційну безпеку, і коли вона буде надійною, ми додамо посилання на ваше дзеркало та почнемо тісніше співпрацювати з вами. Заздалегідь дякуємо всім, хто готовий внести свій внесок у цей спосіб! Це не для слабкодухих, але це зміцнить довговічність найбільшої справді відкритої бібліотеки в історії людства. Початок роботи Щоб підвищити стійкість Архіву Анни, ми шукаємо волонтерів для запуску дзеркал. Ваша версія чітко відрізняється як дзеркало, наприклад, «Архів Боба, дзеркало Anna’s Archive». Ви готові прийняти ризики, пов’язані з цією роботою, які є значними. Ви глибоко розумієте необхідну операційну безпеку. Зміст <a %(a_shadow)s>цих</a> <a %(a_pirate)s>публікацій</a> є для вас очевидним. Спочатку ми не надамо вам доступ до завантажень з серверів наших партнерів, але якщо все піде добре, ми можемо поділитися цим з вами. Ви керуєте відкритим вихідним кодом Anna’s Archive і регулярно оновлюєте як код, так і дані. Ви готові внести свій внесок у наш <a %(a_codebase)s>вихідний код</a> — у співпраці з нашою командою — щоб це сталося. Ми шукаємо це: Дзеркала: заклик до волонтерів Задонатити ще. Наразі донатів ще немає. <a %(a_donate)s>Задонатити вперше.</a> Деталі донатів не оприлюднюються. Мої донати 📡 Для створення масових резервних копій нашої колекції відвідайте сторінки <a %(a_datasets)s>"Бази Даних"</a> та <a %(a_torrents)s>"Торренти"</a> . Завантаження з вашої IP-адреси за останні 24 години: %(count)s. 🚀 Аби збільшити швидкість завантажень, <a %(a_membership)s>підпишіться на сайт</a>. Завантажити з веб-сайту партнерів Ви можете продовжити перегляд Архіву Анни в іншій вкладці, поки чекаєте (якщо ваш браузер підтримує оновлення вкладок у фоновому режимі). Ви можете чекати завантаження кількох сторінок одночасно (але, будь ласка, завантажуйте лише один файл одночасно з кожного сервера). Як тільки ви отримаєте посилання для завантаження, воно буде дійсним протягом кількох годин. Дякуємо за очікування, це дозволяє зберігати доступ до сайту безкоштовним для всіх! 😊 🔗 Всі посилання для завантаження цього файлу: <a %(a_main)s>Головна сторінка файлу</a>. ❌ Повільні завантаження недоступні через VPN Cloudflare або з IP-адрес Cloudflare. ❌ Повільне завантаження доступне лише через офіційний сайт. Відвідайте %(websites)s. 📚 Використовуйте цю URL-адресу, щоб завантажити: <a %(a_download)s>Завантажити зараз</a>. Щоб дати всім можливість завантажувати файли безкоштовно, вам потрібно зачекати, перш ніж ви зможете завантажити цей файл. Будь ласка, зачекайте <span %(span_countdown)s>%(wait_seconds)s</span> секунд, щоб завантажити цей файл. Попередження: за останні 24 години з вашої IP-адреси було багато завантажень. Завантаження можуть відбуватися повільніше, ніж зазвичай. Якщо ви використовуєте VPN, спільне інтернет-з'єднання або ваш провайдер інтернету ділить IP-адреси, це попередження може бути через це. Зберегти ❌ Щось пішло не так. Будь ласка, спробуйте ще раз. ✅ Збережено. Будь ласка, перезавантажте сторінку. Змініть своє ім'я для відображення. Ваш ідентифікатор (частина після "#") не може бути змінений. Профіль створено <span %(span_time)s>%(time)s</span> редагувати Списки Створіть новий список, знайшовши файл і відкривши вкладку "Списки". Наразі немає списків Такий обліковий запис не знайдено. Особистий запис Наразі ми не можемо задовольнити запити на книги. Не надсилайте нам запити щодо книг на електронну скриньку. Будь ласка, залишайте свої запити на форумах Z-Library або Libgen. Запис в Архіві Анни DOI: %(doi)s Завантажити SciDB Nexus/STC Попередній перегляд ще недоступний. Завантажте файл з <a %(a_path)s>Архіву Анни</a>. Щоб підтримати доступність і довгострокове збереження знань людства, станьте <a %(a_donate)s>членом</a>. Як бонус, 🧬&nbsp;SciDB завантажується швидше для членів, без жодних обмежень. Не працює? Спробуйте <a %(a_refresh)s>перезавантажити</a>. Sci-Hub Додати конкретне поле пошуку Шукати описи та коментарі до метаданих Рік публікації Розширений Доступ Зміст Відображення Список Таблиця Тип файлу Мова Порядок за Найбільші Найбільш релевантні Найновіші (розмір файлу) (з відкритих джерел) (рік видання) Найстаріші Випадковий Найменші Джерело зібрано та опубліковано у відкритому доступі AA Цифрове позичання (%(count)s) Журнальні статті (%(count)s) Ми знайшли збіги: %(in)s. Ви можете посилатися на знайдену там URL-адресу <a %(a_request)s>під час запиту файлу</a>. Метадані (%(count)s) Щоб дослідити пошуковий індекс за кодами, скористайтеся <a %(a_href)s>Дослідником кодів</a>. Пошуковий індекс оновлюється щомісяця. Наразі він включає записи до %(last_data_refresh_date)s. Для отримання додаткової технічної інформації дивіться %(link_open_tag)sсторінку про базу даних</a>. Виключити Включити лише Не перевірено більше… Наступна … Попередня Цей пошуковий індекс наразі включає метадані з бібліотеки керованого цифрового позичання Інтернет-архіву. <a %(a_datasets)s>Більше про наші даніs</a>. Щоб дізнатися більше про цифрові бібліотеки, дивіться <a %(a_wikipedia)s>Вікіпедію</a> та <a %(a_mobileread)s>MobileRead Wiki</a>. Для отримання інформації щодо DMCA / авторських прав <a %(a_copyright)s>натистіть тут</a>. Час завантаження Помилка під час пошуку. Спробуйте <a %(a_reload)s>перезавантажити сторінку</a>. Якщо проблема не зникає, будь ласка, напишіть нам на %(email)s. Швидке завантаження Насправді, кожен може допомогти зберегти ці файли, роздаючи наш <a %(a_torrents)s>уніфікований список торрентів</a>. ➡️ Іноді це трапляється неправильно, коли сервер пошуку працює повільно. У таких випадках <a %(a_attrs)s>перезавантаження</a> може допомогти. ❌ Цей файл може бути проблемним. Шукаєте статті? Цей пошуковий індекс наразі включає метадані з ISBNdb та Open Library. <a %(a_datasets)s>Більше про наші дані</a>. Існує дуже багато джерел метаданих для письмових творів по всьому світу. <a %(a_wikipedia)s>Ця сторінка Вікіпедії</a> є гарним початком, але якщо ви знаєте інші хороші списки, будь ласка, повідомте нам. Для метаданих ми показуємо оригінальні записи. Ми не робимо об'єднання записів. Наразі ми маємо найповніший у світі відкритий каталог книг, статей та інших письмових робіт. Ми віддзеркалюємо Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>та інших</a>. <span class="font-bold">Нічого не знайдено.</span> Спробуйте скоротити запит чи використайте інші ключові слова чи фільтри. Результати %(from)s-%(to)s (%(total)s всі) Якщо ви знайдете інші "тіньові бібліотеки", які нам слід віддзеркалити, або якщо у вас виникнуть запитання, будь ласка, зв'яжіться з нами за адресою %(email)s. %(num)d часткових збігів %(num)d+ часткових збігів Введіть у поле для пошуку файлів в цифрових бібліотеках, що позичають. Введіть у поле для пошуку в нашому каталозі %(count)s файли, які можна завантажити безпосередньо, які ми <a %(a_preserve)s>збережемо назавжди</a>. Введіть у поле для пошуку. Введіть у поле для пошуку в нашому каталозі %(count)s наукові роботи та журнальні статті, які ми <a %(a_preserve)s>зберігаємо назавжди</a>. Введіть у поле для пошуку метаданих з бібліотек. Це може бути корисно, при <a %(a_request)s>запиті файлу</a>. Порада: використовуйте клавіатурні скорочення "/" (фокус пошуку), "enter" (пошук), "j" (вгору), "k" (вниз) для швидкої навігації. Це записи метаданих, <span %(classname)s>не</span> файли для завантаження. Налаштування пошуку Пошук Цифрове позичання Завантажити Журнальні статті Метадані Шукати нові книги %(search_input)s – Пошук Пошук зайняв занадто багато часу, а це означає, що ви можете побачити неточні результати. Іноді <a %(a_reload)s>перезавантаження</a> сторінки допомагає. Пошук зайняв занадто багато часу, що характерно для великих запитів. Кількість фільтрів може бути неточною. Щодо великих завантажень (понад 10 000 файлів), які не приймаються Libgen або Z-Library, будь ласка, зв'яжіться з нами за адресою %(a_email)s. Для Libgen.li, переконайтеся, що спочатку увійшли на <a %(a_forum)s >їхній форум</a> з ім’ям користувача %(username)s та паролем %(password)s, а потім поверніться на їхню <a %(a_upload_page)s >сторінку завантаження</a>. Наразі ми рекомендуємо додавати нові книги на гілки Library Genesis. Ось <a %(a_guide)s>зручна інструкція</a>. Зверніть увагу, що обидві гілки, які ми індексуємо на цьому сайті, беруть інформацію з однієї і тієї ж системи завантаження файлів. Для невеликих завантажень (до 10 000 файлів) завантажте їх на обидва %(first)s та %(second)s. Альтернативно, ви можете завантажити їх на Z-Library <a %(a_upload)s>тут</a>. Для завантаження наукових статей, будь ласка, також (крім Library Genesis) завантажуйте на <a %(a_stc_nexus)s>STC Nexus</a>. Вони є найкращою тіньовою бібліотекою для нових статей. Ми ще не інтегрували їх, але зробимо це згодом. Ви можете скористатися їх <a %(a_telegram)s>ботом для завантаження в Telegram</a>, або зв'язатися з адресою, вказаною в їх закріпленому повідомленні, якщо у вас занадто багато файлів для завантаження таким чином. <span %(label)s>Інтенсивна волонтерська робота (винагороди від 50 до 5000 доларів США):</span> якщо ви можете присвятити багато часу та/або ресурсів нашій місії, ми будемо раді тісніше співпрацювати з вами. З часом ви зможете приєднатися до внутрішньої команди. Хоча наш бюджет обмежений, ми можемо нагороджувати <span %(bold)s>💰 грошовими винагородами</span> за найінтенсивнішу роботу. <span %(label)s>Легка волонтерська робота:</span> якщо ви можете виділити лише кілька годин тут і там, все одно є багато способів, як ви можете допомогти. Ми винагороджуємо постійних волонтерів <span %(bold)s>🤝 членствами в Anna’s Archive</span>. Anna’s Archive покладається на волонтерів, таких як ви. Ми вітаємо всі рівні залученості та маємо дві основні категорії допомоги, яку ми шукаємо: Якщо ви не можете волонтерити свій час, ви все одно можете дуже допомогти нам, <a %(a_donate)s>пожертвувавши гроші</a>, <a %(a_torrents)s>роздаючи наші торренти</a>, <a %(a_uploading)s>завантажуючи книги</a> або <a %(a_help)s>розповідаючи своїм друзям про Архів Анни</a>. <span %(bold)s>Компанії:</span> ми пропонуємо високошвидкісний прямий доступ до наших колекцій в обмін на корпоративні пожертви або обмін на нові колекції (наприклад, нові скани, OCR’овані datasets, збагачення наших даних). <a %(a_contact)s>Зв'яжіться з нами</a>, якщо це про вас. Дивіться також нашу <a %(a_llm)s>сторінку LLM</a>. Винагороди Ми завжди шукаємо людей з міцними навичками програмування або наступальної безпеки, щоб залучити їх до нашої роботи. Ви можете зробити серйозний внесок у збереження спадщини людства. Як подяку, ми надаємо членство за вагомі внески. Як велику подяку, ми надаємо грошові винагороди за особливо важливі та складні завдання. Це не повинно розглядатися як заміна роботи, але це додатковий стимул і може допомогти з покриттям витрат. Більшість нашого коду є відкритим, і ми попросимо, щоб ваш код також був відкритим при наданні винагороди. Є деякі винятки, які ми можемо обговорити індивідуально. Винагороди надаються першій людині, яка виконає завдання. Не соромтеся коментувати завдання з винагородою, щоб інші знали, що ви працюєте над чимось, щоб вони могли утриматися або зв'язатися з вами для співпраці. Але майте на увазі, що інші також можуть працювати над цим і намагатися випередити вас. Однак ми не надаємо винагороди за недбалу роботу. Якщо дві високоякісні подачі зроблені близько одна до одної (протягом одного-двох днів), ми можемо вирішити надати винагороди обом, на наш розсуд, наприклад, 100%% за першу подачу і 50%% за другу подачу (тобто всього 150%%). Для більших винагород (особливо за скрапінг), будь ласка, зв'яжіться з нами, коли ви завершите ~5%% завдання, і ви впевнені, що ваш метод масштабуватиметься до повного етапу. Вам доведеться поділитися своїм методом з нами, щоб ми могли надати зворотний зв'язок. Також, таким чином, ми можемо вирішити, що робити, якщо кілька людей наближаються до винагороди, наприклад, потенційно надавати її кільком людям, заохочувати людей до співпраці тощо. УВАГА: завдання з високими винагородами <span %(bold)s>складні</span> — можливо, варто почати з легших. Перейдіть до нашого <a %(a_gitlab)s>списку завдань на Gitlab</a> і сортуйте за “Пріоритет мітки”. Це приблизно показує порядок завдань, які нас цікавлять. Завдання без явних винагород все ще можуть претендувати на членство, особливо ті, що позначені як “Прийнято” та “Улюблене Анни”. Можливо, вам варто почати з “Початкового проекту”. Легка волонтерська робота Тепер у нас також є синхронізований канал Matrix на %(matrix)s. Якщо у вас є кілька вільних годин, ви можете допомогти різними способами. Обов’язково приєднуйтесь до <a %(a_telegram)s>чату волонтерів у Telegram</a>. Як знак вдячності, ми зазвичай надаємо 6 місяців статусу “Щасливий Бібліотекар” за базові досягнення, і більше за продовження волонтерської роботи. Усі досягнення вимагають високоякісної роботи — недбала робота шкодить нам більше, ніж допомагає, і ми її відхилимо. Будь ласка, <a %(a_contact)s>напишіть нам електронного листа</a>, коли досягнете певного етапу. %(links)s посилання або скріншоти запитів, які ви виконали. Виконання запитів на книги (або статті тощо) на форумах Z-Library або Library Genesis. У нас немає власної системи запитів на книги, але ми дзеркалимо ці бібліотеки, тому покращення їх робить кращим і Архів Анни. Досягнення Завдання Залежить від завдання. Невеликі завдання, опубліковані в нашому <a %(a_telegram)s>чаті волонтерів у Telegram</a>. Зазвичай для членства, іноді для невеликих винагород. Невеликі завдання, розміщені в нашій групі волонтерів. Обов’язково залишайте коментарі до виправлених проблем, щоб інші не дублювали вашу роботу. %(links)s посилання на записи, які ви покращили. Ви можете використовувати <a %(a_list)s >список випадкових проблем з metadata</a> як відправну точку. Покращення метаданих шляхом <a %(a_metadata)s>зв’язування</a> з Open Library. Це повинно показати, як ви повідомляєте комусь про Архів Анни, і як вони дякують вам. %(links)s посилання або скріншоти. Поширення інформації про Архів Анни. Наприклад, рекомендування книг на AA, посилання на наші публікації в блозі або загальне направлення людей на наш вебсайт. Повний переклад мови (якщо він не був майже завершений). <a %(a_translate)s>Переклад</a> вебсайту. Посилання на історію редагувань, що показує ваші значні внески. Покращення сторінки Вікіпедії про Архів Анни вашою мовою. Включіть інформацію зі сторінки Вікіпедії AA іншими мовами, а також з нашого вебсайту та блогу. Додайте посилання на AA на інших відповідних сторінках. Волонтерство та нагороди 