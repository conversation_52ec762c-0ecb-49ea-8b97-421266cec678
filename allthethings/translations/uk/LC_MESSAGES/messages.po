msgid "layout.index.invalid_request"
msgstr "Невірний запит. Відвідайте %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Бібліотека Інтернет- архіву"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " і "

msgid "layout.index.header.tagline_and_more"
msgstr "та більше"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Ми відзеркалюємо %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Ми збираємо %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Весь наш код і дані повністю відкриті."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Найбільша по-справжньому відкрита бібліотека в історії людства."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;книги, %(paper_count)s&nbsp;статей — все це збережено назавжди."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Найбільша у світі бібліотека відкритих даних з відкритим вихідним кодом. ⭐️&nbsp;Віддзеркалює Sci-Hub, Library Genesis, Z-Library та інші ресурси. 📈&nbsp;%(book_any)s книги, %(journal_article)s статті, %(book_comic)s комікси, %(magazine)s журнали — все це збережено назавжди."

msgid "layout.index.header.tagline_short"
msgstr "📚Найбільша у світі бібліотека відкритих даних з відкритим вихідним кодом.<br>⭐️ Віддзеркалює Scihub, Libgen, Zlib та інші."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Невірні метаданні (напр. назва, опис, оюкладинка)"

msgid "common.md5_report_type_mapping.download"
msgstr "Проблеми зі скачуванням (напр. відсутність з'єднання, повідомлення щодо помилки, дуже повільно)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Файл не може бути відкритим (напр. пошкодження файлу, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Погана якість (напр. проблеми з форматуванням, погана якість скану, відсутні сторінки)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Спам / файл потрібно видалити (напр. реклама, образливий вміст)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Претензія щодо авторських прав"

msgid "common.md5_report_type_mapping.other"
msgstr "Other"

msgid "common.membership.tier_name.bonus"
msgstr "Бонусні скачування"

msgid "common.membership.tier_name.2"
msgstr "Блискучий Буквоїд"

msgid "common.membership.tier_name.3"
msgstr "Блаженний Бібліотекар"

msgid "common.membership.tier_name.4"
msgstr "Засліплюючий Знавець"

msgid "common.membership.tier_name.5"
msgstr "Дивовижний Дяк"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s всього"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) всього"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s бонус)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "не оплачено"

msgid "common.donation.order_processing_status_labels.1"
msgstr "оплачено"

msgid "common.donation.order_processing_status_labels.2"
msgstr "скасовано"

msgid "common.donation.order_processing_status_labels.3"
msgstr "закінчився термін дії"

msgid "common.donation.order_processing_status_labels.4"
msgstr "очікуємо на підтвердження від Анни"

msgid "common.donation.order_processing_status_labels.5"
msgstr "недійсний"

msgid "page.donate.title"
msgstr "Задонатити"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "У вас вже є <a %(a_donation)s>існуючий донат</a>. Будь ласка, завершіть або скасуйте його перед тим, як зробити новий."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Переглянути всі мої донати</a>"

msgid "page.donate.header.text1"
msgstr "Архів Анни - це некомерційний проєкт з відкритим кодом і відкритими даними. Задонативши та стаючи учасником, ви підтримуєте нашу діяльність та розвиток. Усім нашим учасникам: дякуємо, що продовжуєте нас підтримувати! ❤️"

msgid "page.donate.header.text2"
msgstr "Для отримання додаткової інформації перегляньте <a %(a_donate)s>Часті запитання про донати</a>."

msgid "page.donate.refer.text1"
msgstr "Щоб отримати ще більше завантажень, <a %(a_refer)s>запрошуйте друзів</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Ви отримали %(percentage)s%% бонус у вигляді швидких завантажень, тому що вас направив користувач %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Це стосується всього періоду підписки."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s швидких завантажень на день"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "якщо ви зробите пожертву цього місяця!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / місяць"

msgid "page.donate.buttons.join"
msgstr "Приєднатися"

msgid "page.donate.buttons.selected"
msgstr "Вибрано"

msgid "page.donate.buttons.up_to_discounts"
msgstr "до %(percentage)s%% знижки"

msgid "page.donate.perks.scidb"
msgstr "SciDB папери <strong>безлімітні</strong> без підтвердження"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Доступ до JSON API</a>"

msgid "page.donate.perks.refer"
msgstr "Отримайте <strong>%(percentage)s%% бонусних завантажень</strong>, <a %(a_refer)s>запросивши друзів</a>."

msgid "page.donate.perks.credits"
msgstr "Ваше ім'я користувача або анонімне згадування в подяках"

msgid "page.donate.perks.previous_plus"
msgstr "Попередні переваги та додатково:"

msgid "page.donate.perks.early_access"
msgstr "Ранній доступ до нових функцій"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Приватний Telegram про найсвіжіші оновлення"

msgid "page.donate.perks.adopt"
msgstr "\"Підписати торрент\": ваше ім'я користувача або повідомлення у назві торрент-файлу <div %(div_months)s>один раз на 12 місяців підписки</div>"

msgid "page.donate.perks.legendary"
msgstr "Легендарний статус у збереженні знань і культури людства"

msgid "page.donate.expert.title"
msgstr "Доступ для експертів"

msgid "page.donate.expert.contact_us"
msgstr "зв'яжіться з нами"

msgid "page.donate.small_team"
msgstr "У нас невелика команда волонтерів. Нам може знадобитися 1-2 тижні, щоб відповісти."

msgid "page.donate.expert.unlimited_access"
msgstr "Безлімітні<strong></strong> швидкі завантаження"

msgid "page.donate.expert.direct_sftp"
msgstr "Прямі <strong>SFTP</strong> сервери"

msgid "page.donate.expert.enterprise_donation"
msgstr "Донати корпоративного рівня або обмін на нові збірки (наприклад: нові скани, набори даних, розпізнані за допомогою OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Ми вітаємо великі донати від спроміжних приватних осіб чи установ. "

msgid "page.donate.header.large_donations"
msgstr "Для донатів понад $5000, будь ласка, зв'яжіться з нами безпосередньо за адресою %(email)s."

msgid "page.donate.header.recurring"
msgstr "Зверніть увагу, що хоча членства на цій сторінці є «щомісячними», вони є одноразовими донатами (не повторюються). Дивіться <a %(faq)s>Часті запитання про донати</a>."

msgid "page.donate.without_membership"
msgstr "Якщо ви хочете зробити пожертву (будь-яку суму) без членства, скористайтеся цією адресою Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Будь ласка, оберіть спосіб оплати."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(тимчасово недоступно)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Подарункова картка %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Банківська картка (використовуючи додаток)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Криптовалюта %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Кредитна/дебетова картка"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (США) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (звичайний)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Картка / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Кредитна/дебетова/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Бразилія)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Банківська картка"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Кредитна/дебетова картка (резервний варіант)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Кредитна/дебетова картка 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Бінанс"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "За допомогою криптовалюти ви можете задонатити використовуючи BTC, ETH, XMR та SOL. Використовуйте цей варіант, якщо ви вже добре обізнані щодо криптовалюти."

msgid "page.donate.payment.desc.crypto2"
msgstr "Ви можете донатити за допомогою криптовалют використовуючи BTC, ETH, XMR та інші."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Якщо ви використовуєте криптовалюту вперше, ми рекомендуємо скористатися %(options)s щоб купити та задонатити Bitcoin (оригінальної та найпоширенішої криптовалюти)."

msgid "page.donate.payment.processor.binance"
msgstr "Бінанс"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Щоб задонатити за допомогою PayPal (США), ми будемо використовувати PayPal Crypto, який дозволяє нам залишатися анонімними. Ми вдячні вам за те, що ви знайшли час, аби навчитися робити внески за допомогою цього методу, оскільки це дуже допомагає нам."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Донатьте використовуючи PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Донатьне використовуючи Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Якщо вас є Cash App, то це найпростіший спосіб задонатити!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Зауважте, що для транзакцій менше %(amount)s, Cash App може стягувати додаткову платню в розмірі %(fee)s . Для %(amount)s чи вище це безкоштовно!"

msgid "page.donate.payment.desc.revolut"
msgstr "Задонатьте за допомогою Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Якщо у вас є Revolut, це найпростіший спосіб зробити донат!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Донатьте використовуючи кредитну або дебетову картку."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay та Apple Pay також можуть спрацювати."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Зауважте, додаткова плата, що стягується при оплаті кредитною карткою, при оплаті маленьких донатів, може знівелювати нашу %(discount)s%% знижку, тому ми рекомендуємо довготривалі підписки."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Зауважте, додаткова плата, що стягується при оплаті маленьких донатів велика, тому ми рекомендуємо довготривалі підписки."

msgid "page.donate.payment.desc.binance_p1"
msgstr "З Binance ви купуєте Bitcoin за допомогою кредитної/дебетової картки або банківського рахунку, а потім донатите цей Bitcoin нам. Таким чином, ми можемо залишатися захищеними та анонімними при прийнятті вашого донату."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance доступний майже в кожній країні та підтримує більшість банків і кредитних/дебетових карток. Це наразі наша основна рекомендація. Ми вдячні вам за те, що ви витратили час на вивчення цього способу зробити донат, оскільки це дуже допомагає нам."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Зробіть донат за допомогою вашого звичайного облікового запису PayPal."

msgid "page.donate.payment.desc.givebutter"
msgstr "Зробіть донат за допомогою кредитної/дебетової картки, PayPal або Venmo. Ви можете вибрати між ними на наступній сторінці."

msgid "page.donate.payment.desc.amazon"
msgstr "Донатьте використовуючи подарункову картку Амазон."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Зауважте, що нам треба округляти до значень, що приймаються нашими посередниками (мінімум %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>ВАЖЛИВО:</strong> Ми підтримуємо тільки Amazon.com, інші вебсторінки Amazon, для прикладу: .de, .co.uk, .ca, не підтримуються."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>ВАЖЛИВО:</strong> Цей варіант для %(amazon)s. Якщо ви хочете використовувати інший вебсайт Amazon, виберіть його вище."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Цей метод використовує постачальника криптовалют як проміжну конверсію. Це може бути трохи заплутано, тому, будь ласка, використовуйте цей метод лише якщо інші способи оплати не працюють. Він також не працює у всіх країнах."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Зробіть донат, використовуючи кредитну/дебетову картку, через додаток Alipay (дуже легко налаштувати)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Встановіть додаток Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Встановіть додаток Alipay з <a %(a_app_store)s>Apple App Store</a> або <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Зареєструйтесь, використовуючи свій номер телефону."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Додаткові особисті дані не потрібні."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Додайте банківську картку"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Підтримуються: Visa, MasterCard, JCB, Diners Club і Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Дивіться <a %(a_alipay)s>цей посібник</a> для отримання додаткової інформації."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Ми не можемо підтримувати кредитні/дебетові картки безпосередньо, тому що банки не хочуть з нами працювати. ☹ Однак, є кілька способів використання кредитних/дебетових карток через інші платіжні методи:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Подарункува картка Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Надсилайте нам подарункові картки Amazon.com за допомогою кредитної/дебетової картки."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay підтримує міжнародні кредитні/дебетові картки. Дивіться <a %(a_alipay)s>цей посібник</a> для отримання додаткової інформації."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) підтримує міжнародні кредитні/дебетові картки. У додатку WeChat перейдіть в “Me => Services => Wallet => Add a Card”. Якщо ви не бачите цього пунткту, то увімкніть його в “Me => Settings => General => Tools => Weixin Pay => Enable”."

msgid "page.donate.ccexp.crypto"
msgstr "Ви можете придбати криптовалюту за допомогою кредитної/дебетової картки."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Крипто експрес-послуги"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Експрес-послуги зручні, але стягують вищі комісії."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Ви можете використовувати це замість криптообміну, якщо хочете швидко зробити більший донат і не проти комісії в $5-10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Обов’язково надішліть точну суму криптовалюти, вказану на сторінці донатів, а не суму в $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Інакше комісія буде вирахувана, і ми не зможемо автоматично обробити ваше членство."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(мінімум: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(мінімум: %(minimum)s залежно від країни, без перевірки для першої транзакції)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(мінімум: %(minimum)s, без перевірки для першої транзакції)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(мінімум: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(мінімум: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(мінімум: %(minimum)s, без перевірки для першої транзакції)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Якщо будь-яка з цієї інформації застаріла, будь ласка, напишіть нам електронного листа, щоб повідомити нас."

msgid "page.donate.payment.desc.bmc"
msgstr "Для кредитних карток, дебетових карток, Apple Pay та Google Pay ми використовуємо “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). У їхній системі одна “кава” дорівнює $5, тому ваша пожертва буде округлена до найближчого кратного 5."

msgid "page.donate.duration.intro"
msgstr "Оберіть тривалість на яку ви хочете підписатися."

msgid "page.donate.duration.1_mo"
msgstr "1 місяць"

msgid "page.donate.duration.3_mo"
msgstr "3 місяці"

msgid "page.donate.duration.6_mo"
msgstr "6 місяців"

msgid "page.donate.duration.12_mo"
msgstr "12 місяців"

msgid "page.donate.duration.24_mo"
msgstr "24 місяці"

msgid "page.donate.duration.48_mo"
msgstr "48 місяців"

msgid "page.donate.duration.96_mo"
msgstr "96 місяців"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>після <span %(span_discount)s></span> discounts</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Цей спосіб оплати потребує мінімум %(amount)s. Будь ласка, оберіть іншу тривалість або спосіб оплати."

msgid "page.donate.buttons.donate"
msgstr "Зробіть донат"

msgid "page.donate.payment.maximum_method"
msgstr "Цей спосіб оплати дозволяє тільки максимум %(amount)s. Будь ласка, оберіть іншу тривалість або спосіб оплати."

msgid "page.donate.login2"
msgstr "Щоб стати учасником <a %(a_login)s>Увійдіть або Зареєструйтесь</a>. Дякуємо за вашу підтримку!"

msgid "page.donate.payment.crypto_select"
msgstr "Оберіть криптовалюту, якій ви надаєте перевагу:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(найменша мінімальна сума)"

msgid "page.donate.coinbase_eth"
msgstr "(використовуйте при відправці Ethereum з Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(увага: висока мінімальна сума)"

msgid "page.donate.submit.confirm"
msgstr "Натисніть кнопку «Задонатити», щоб підтвердити цей внесок."

msgid "page.donate.submit.button"
msgstr "Задонатити <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Ви все ще можете скасувати цей донат під час оформлення."

msgid "page.donate.submit.success"
msgstr "✅ Перенаправлення на сторінку донатів…"

msgid "page.donate.submit.failure"
msgstr "❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / місяць"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "на 1 місяць"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "на 3 місяці"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "на 6 місяців"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "на 12 місяців"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "на 24 місяці"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "на 48 місяців"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "на 96 місяців"

msgid "page.donate.submit.button.label.1_mo"
msgstr "на 1 місяць “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "на 3 місяці “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "на 6 місяців “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "на 12 місяців “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "на 24 місяці “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "на 48 місяців “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "на 96 місяців “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Донат"

msgid "page.donation.header.date"
msgstr "Дата: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Разом: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / місяць на %(duration)s місяці(в), враховуючи %(discounts)s%% знижки)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Разом: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / місяць на %(duration)s місяці(в))</span>"

msgid "page.donation.header.status"
msgstr "Статус: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Ідентифікатор: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Скасувати"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Ви впевнені, що хочете скасувати? Не скасовуйте, якщо ви вже зробили оплату."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Так, скасуйте, будь ласка"

msgid "page.donation.header.cancel.success"
msgstr "✅ Ваш донат скасовано."

msgid "page.donation.header.cancel.new_donation"
msgstr "Створити новий донат"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.donation.header.reorder"
msgstr "Повторний платіж"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Ви вже здійснили оплату. Якщо ви хочете переглянути платіжні вказівки, натисніть тут:"

msgid "page.donation.old_instructions.show_button"
msgstr "Показати старі платіжні вказівки"

msgid "page.donation.thank_you_donation"
msgstr "Дякуємо за ваш донат!"

msgid "page.donation.thank_you.secret_key"
msgstr "Якщо ви цього ще не зробили, запишіть свій секретний ключ для входу:"

msgid "page.donation.thank_you.locked_out"
msgstr "В іншому випадку ви можете бути заблоковані від цього облікового запису!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Наразі вказівки по оплаті застарілі. Якщо ви хочете зробити ще один внесок, скористайтеся кнопкою \"Повторний платіж\" вище."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Важлива інформація:</strong> Ціни на криптовалюту можуть сильно коливатися, іноді навіть на 20%% за кілька хвилин. Проте це все-таки значно менше, ніж комісія багатьох платіжних провайдерів, які часто стягують 50-60%% за роботу з такими \"тіньовими благодійними організаціями\", як ми. <u>Якщо ви надішлете нам квитанцію з оригінальною сумою, яку ви сплатили, ми нарахуємо на ваш рахунок кошти за обрану вами підписку</u> (за умови, що квитанція не була виписана раніше, ніж кілька годин тому). Ми дуже цінуємо, що ви готові миритися з такими речами, аби підтримати нас! ❤️"

msgid "page.donation.expired"
msgstr "Цей донат закінчився. Будь ласка, скасуйте та створіть новий."

msgid "page.donation.payment.crypto.top_header"
msgstr "Вказівки щодо використання криптовалюти"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Переказати на один з наших криптовалютних рахунків"

msgid "page.donation.payment.crypto.text1"
msgstr "Переказати загальну суму %(total)s на одну з цих адрес:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Купити Bitcoin на Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Знайдіть сторінку \"Криптовалюта\" у вашому додатку PayPal або на веб-сайті. Зазвичай вона знаходиться у розділі \"Фінанси\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Дотримуйтесь вказівок, аби купити Bitcoin (BTC). Вам потрібно купити лише ту суму, на яку ви бажаєте зробити внесок, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Надіслати Bitcoin на нашу адресу"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Перейдіть на сторінку \"Bitcoin\" у вашому додатку PayPal або на сайті. Натисніть кнопку \"Переказати\" %(transfer_icon)s, а потім \"Відправити\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Введіть нашу Bitcoin (BTC) адресу в якості одержувача і дотримуйтесь вказівок, аби відправити ваш донат у розмірі %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Вказівки щодо донатів кредитною / дебетовою карткою"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Донатьте через нашу сторінку оплати кредитною чи дебетовою карткою"

msgid "page.donation.donate_on_this_page"
msgstr "Донатьте %(amount)s на <a %(a_page)s>цій сторінці</a>."

msgid "page.donation.stepbystep_below"
msgstr "Дивіться покрокову інструкцію нижче."

msgid "page.donation.status_header"
msgstr "Статус:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Очікуємо підтвердження (оновіть сторінку для перевірки)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Очікуємо на переказ (оновіть сторінку для перевірки)…"

msgid "page.donation.time_left_header"
msgstr "Часу залишилось:"

msgid "page.donation.might_want_to_cancel"
msgstr "(ви можете скасувати та створити новий донат)"

msgid "page.donation.reset_timer"
msgstr "Щоб перезапустити таймер, просто створіть новий донат."

msgid "page.donation.refresh_status"
msgstr "Статус оновлення"

msgid "page.donation.footer.issues_contact"
msgstr "Якщо ви зіткнулися з проблемами, будь ласка, напишіть на %(email)s та прикрипіть якомога більше інформації (наприклад, знімки екрану)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Якщо ви вже сплатили:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Іноді підтвердження може зайняти до 24 годин, тому обов’язково оновіть цю сторінку (навіть якщо вона закінчилася)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Купуйте PYUSD монети на PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Дотримуйтесь інструкцій, щоб купити PYUSD coin (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Купуйте трохи більше (ми рекомендуємо на %(more)s більше), ніж сума яку ви донатите (%(amount)s), щоб покрити додаткову комісію. Ви збережете все, що залишилося."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Перейдіть на “PYUSD” сторінку вашого PayPal додатку чи вебсторінки. Натисніть на кнопку “Переказ” %(icon)s, а потім “Відправити”."

msgid "page.donation.transfer_amount_to"
msgstr "Перекажіть %(amount)s на %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Купити Bitcoin (BTC) у Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Перейдіть на сторінку «Bitcoin» (BTC) у Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Купіть трохи більше (ми рекомендуємо %(more)s більше), ніж сума, яку ви донатите (%(amount)s), щоб покрити комісії за транзакції. Ви збережете все, що залишиться."

msgid "page.donation.cash_app_btc.step2"
msgstr "Переведіть Bitcoin на нашу адресу"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Натисніть кнопку «Send bitcoin», щоб зробити «виведення». Перемкніть з доларів на BTC, натиснувши на іконку %(icon)s. Введіть суму BTC нижче та натисніть «Send». Дивіться <a %(help_video)s>це відео</a>, якщо застрягнете."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Для невеликих донатів (менше $25) вам, можливо, доведеться використовувати Rush або Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Купити Bitcoin (BTC) у Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Перейдіть на сторінку «Crypto» у Revolut, щоб купити Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Купіть трохи більше (ми рекомендуємо %(more)s більше), ніж сума, яку ви донатите (%(amount)s), щоб покрити комісії за транзакції. Ви збережете все, що залишиться."

msgid "page.donation.revolut.step2"
msgstr "Переведіть Bitcoin на нашу адресу"

msgid "page.donation.revolut.step2.transfer"
msgstr "Натисніть кнопку «Send bitcoin», щоб зробити «виведення». Перемкніть з євро на BTC, натиснувши на іконку %(icon)s. Введіть суму BTC нижче та натисніть «Send». Дивіться <a %(help_video)s>це відео</a>, якщо застрягнете."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Обов’язково використовуйте суму в BTC нижче, <em>НЕ</em> євро чи долари, інакше ми не отримаємо правильну суму і не зможемо автоматично підтвердити ваше членство."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Для невеликих донатів (менше $25) вам, можливо, доведеться використовувати Rush або Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Використовуйте будь-яку з наступних експрес-служб «кредитна картка до Bitcoin», які займають лише кілька хвилин:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Заповніть наступні дані у формі:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Сума BTC / Bitcoin:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Будь ласка, використовуйте цю <span %(underline)s>точну суму</span>. Ваша загальна вартість може бути вищою через комісії за кредитні картки. Для невеликих сум це може бути більше, ніж наша знижка, на жаль."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Адреса BTC / Bitcoin (зовнішній гаманець):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s інструкції"

msgid "page.donation.crypto_standard"
msgstr "Ми підтримуємо тільки стандартні варіанти криптовалют, а не екзотичні варіанти криптовалют чи версії коїнів. Підтвердження транзакції може зайняти до години, в залежності від обраної криптовалюти."

msgid "page.donation.crypto_qr_code_title"
msgstr "Сканувати QR -код для оплати"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Скануйте цей QR -код за допомогою програми Crypto Wallet, щоб швидко заповнити дані про платіж"

msgid "page.donation.amazon.header"
msgstr "Подарункова карта Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Будь ласка, використовуйте <a %(a_form)s>офіційну форму Amazon.com form</a> щоб відправити нам подарункову картку %(amount)s на електронну скриньку нижче."

msgid "page.donation.amazon.only_official"
msgstr "Ми не приймаємо інші способи розрахунку подарунковими картками, <strong> надсилайте лише безпосередньо з офіційної форми на Amazon.com</strong>. Ми не можемо повернути вашу подарункову картку, якщо ви не використайте цю форму."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Введіть точну суму: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Будь ласка, НЕ пишіть своїх повідомлень."

msgid "page.donation.amazon.form_to"
msgstr "У поле “Кому” введіть цю електронну скриньку отримувача:"

msgid "page.donation.amazon.unique"
msgstr "Унікальне для вашого аккаунту, не поширюйте."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Використати лише один раз."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Очікуємо на подарункову картку … (оновіть сторінку щоб перевірити)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Після відправки вашої подарункової картки, наша система підтвердить це за декілька хвилин. Якщо це не спрацює, спробуйте відправити вашу подарункову картку знову– (<a %(a_instr)s>інструкція</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Якщо це все ще не буде працювати, будь ласка, напишіть нам електронного листа і Анна вручну перевірить (це може зайняти декілька днів), а також зазначте, якщо ви вже спробували відправити знову."

msgid "page.donation.amazon.example"
msgstr "Приклад:"

msgid "page.donate.strange_account"
msgstr "Зверніть увагу, що назва акаунта або його зображення можуть виглядати дещо дивно. Не хвилюйтеся! Цими акаунтами керують наші партнери по збору внесків. Наші акаунти не були зламані."

msgid "page.donation.payment.alipay.top_header"
msgstr "Вказівки щодо використання Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Задонатити через Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Зробіть донат на загальну суму %(total)s за допомогою <a %(a_account)s>цього Alipay акаунту</a>"

msgid "page.donation.page_blocked"
msgstr "Якщо сторінка донатів заблокована, спробуйте інше інтернет-з’єднання (наприклад, VPN або мобільний інтернет)."

msgid "page.donation.payment.alipay.error"
msgstr "На жаль, сторінка Alipay часто доступна лише з <strong>материкового Китаю</strong>. Можливо, вам доведеться тимчасово вимкнути свій VPN або використовувати VPN до материкового Китаю (іноді також працює Гонконг)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Зробіть донат (скануйте QR-код або натисніть кнопку)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Відкрийте <a %(a_href)s>сторінку донатів з QR-кодом</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Скануйте QR-код за допомогою додатка Alipay або натисніть кнопку, щоб відкрити додаток Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Будь ласка, будьте терплячими; сторінка може завантажуватися деякий час, оскільки вона знаходиться в Китаї."

msgid "page.donation.payment.wechat.top_header"
msgstr "Інструкції для WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Зробіть донат через WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Зробіть донат на загальну суму %(total)s за допомогою <a %(a_account)s>цього WeChat акаунту</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Вказівки щодо використання Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Задонатити через Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Переказати загальну суму %(total)s через <a %(a_account)s>цей Pix рахунок"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Надішліть нам квитанцію на електронну пошту"

msgid "page.donation.footer.verification"
msgstr "Надішліть квитанцію або скріншот на вашу особисту адресу для верифікації. НЕ використовуйте цю електронну адресу для вашого донату через PayPal."

msgid "page.donation.footer.text1"
msgstr "Надішліть для підтверждення квитанцію або скріншот на вашу особисту адресу:"

msgid "page.donation.footer.crypto_note"
msgstr "Обов'язково додайте квитанцію з початковим курсом обміну криптовалюти, якщо курс коливався під час транзакції. Ми дуже цінуємо, що ви використовуєте криптовалюту, це дуже допомагає нам!"

msgid "page.donation.footer.text2"
msgstr "Коли ви надішлете квитанцію на електронну пошту, натисніть цю кнопку, аби Анна могла особисто переглянути її (це може зайняти декілька днів):"

msgid "page.donation.footer.button"
msgstr "Так, я надіслав квитанцію на електронну пошту"

msgid "page.donation.footer.success"
msgstr "✅ Дякуємо за ваш внесок! Анна особисто активує вашу підписку на сайті в найближчі кілька днів."

msgid "page.donation.footer.failure"
msgstr "❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.donation.stepbystep"
msgstr "Покрокова інструкція"

msgid "page.donation.crypto_dont_worry"
msgstr "Деякі з кроків містять згадки про криптогаманці, не хвилюйтесь, вам не треба нічого вивчати про криптовалюти для цього."

msgid "page.donation.hoodpay.step1"
msgstr "1. Введіть вашу електронну пошту."

msgid "page.donation.hoodpay.step2"
msgstr "2. Оберіть ваш спосіб оплати."

msgid "page.donation.hoodpay.step3"
msgstr "3. Оберіть ваш спосіб оплати знову."

msgid "page.donation.hoodpay.step4"
msgstr "4. Оберіть “Self-hosted” гаманець."

msgid "page.donation.hoodpay.step5"
msgstr "5. Натистіть “Я підтверджую власність”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Ви отримуєте електронного листа. Будь ласка, відправте його нам і ми підтвердимо ваш донат, якомога швидче."

msgid "page.donate.wait_new"
msgstr "Будь ласка, зачекайте принаймні <span %(span_hours)s>24 години</span> (і оновіть цю сторінку), перш ніж зв’язуватися з нами."

msgid "page.donate.mistake"
msgstr "Якщо ви зробили помилку під час оплати, ми не можемо повернути кошти, але спробуємо це вирішити."

msgid "page.my_donations.title"
msgstr "Мої донати"

msgid "page.my_donations.not_shown"
msgstr "Деталі донатів не оприлюднюються."

msgid "page.my_donations.no_donations"
msgstr "Наразі донатів ще немає. <a %(a_donate)s>Задонатити вперше.</a>"

msgid "page.my_donations.make_another"
msgstr "Задонатити ще."

msgid "page.downloaded.title"
msgstr "Завантажені файли"

msgid "page.downloaded.fast_partner_star"
msgstr "Завантаження з швидких партнерських серверів позначені %(icon)s."

msgid "page.downloaded.twice"
msgstr "Якщо ви завантажили файл одночасно з швидким і повільним завантаженням, він буде показаний двічі."

msgid "page.downloaded.fast_download_time"
msgstr "Швидкі завантаження за останні 24 години зараховуються у денний ліміт."

msgid "page.downloaded.times_utc"
msgstr "Весь час вказано в UTC."

msgid "page.downloaded.not_public"
msgstr "Завантажені файли не відображаються у відкритому доступі."

msgid "page.downloaded.no_files"
msgstr "Наразі немає завантажених файлів."

msgid "page.downloaded.last_18_hours"
msgstr "Останні 18 годин"

msgid "page.downloaded.earlier"
msgstr "Раніше"

msgid "page.account.logged_in.title"
msgstr "Обліковий запис"

msgid "page.account.logged_out.title"
msgstr "Увійти / Зареєструватися"

msgid "page.account.logged_in.account_id"
msgstr "Ідентифікатор облікового запису: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Загальнодоступний профіль: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Секретний ключ (не діліться!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "показати"

msgid "page.account.logged_in.membership_has_some"
msgstr "Підписка: <strong>%(tier_name)s</strong> до %(until_date)s <a %(a_extend)s>(подовжити)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Підписка: <strong>Відсутня</strong> <a %(a_become)s>(стати учасником)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Швидкі завантаження (за останні 24 години): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "які завантаження?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Ексклюзивна Telegram-група: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Приєднуйтесь до нас!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Перейдіть на <a %(a_tier)s>вищий рівень</a> щоб приєднатися до нашої групи."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Зв'яжіться з Анною за адресою %(email)s, якщо ви зацікавлені в покращенні вашої підписки."

msgid "page.contact.title"
msgstr "Контактна електронна скринька"

msgid "page.account.logged_in.membership_multiple"
msgstr "Ви можете об’єднати кілька членств (швидке завантаження за 24 години буде додано разом)."

msgid "layout.index.header.nav.public_profile"
msgstr "Загальнодоступний профіль"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Завантажені файли"

msgid "layout.index.header.nav.my_donations"
msgstr "Мої донати"

msgid "page.account.logged_in.logout.button"
msgstr "Вийти"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Ви вийшли з облікового запису. Перезавантажте сторінку, щоб увійти знову."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.account.logged_out.registered.text1"
msgstr "Реєстрацію успішно завершено! Ось ваш секретний код: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Надійно збережіть цей код. Якщо ви його загубите, то ви втратите доступ до свого облікового запису."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Закладки.</strong> Ви можете додати цю сторінку до закладок, щоб отримати ваш код.</li><li %(li_item)s><strong>Завантажити.</strong> Натисніть на <a %(a_download)s>це посилання</a>, щоб завантажити ваш код.</li><li %(li_item)s><strong>Менеджер паролів.</strong> Для вашої зручності ми заздалегідь створили попередньо записаний файл з вашим кодом, щоб ви могли зберегти його у своєму менеджері паролів для входу на сайт.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Введіть ваш секретний код, щоб увійти в систему:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Секретний код"

msgid "page.account.logged_out.key_form.button"
msgstr "Увійти"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Невірний секретний код. Перевірте ваш код і спробуйте ще раз, або зареєструйте новий обліковий запис нижче."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Не загубіть свій ключ!"

msgid "page.account.logged_out.register.header"
msgstr "У вас ще немає облікового запису?"

msgid "page.account.logged_out.register.button"
msgstr "Зареєструвати новий обліковий запис"

msgid "page.login.lost_key"
msgstr "Якщо ви загубили ключ, будь ласка, <a %(a_contact)s>зв'яжіться з нами</a> і надайте якомога більше інформації."

msgid "page.login.lost_key_contact"
msgstr "Можливо, вам доведеться тимчасово створити новий обліковий запис, щоб зв'язатися з нами."

msgid "page.account.logged_out.old_email.button"
msgstr "Старий обліковий запис на основі електронної пошти? Введіть свою <a %(a_open)s>електронну пошту тут</a>."

msgid "page.list.title"
msgstr "Список"

msgid "page.list.header.edit.link"
msgstr "редагувати"

msgid "page.list.edit.button"
msgstr "Зберегти"

msgid "page.list.edit.success"
msgstr "✅ Збережено. Будь ласка, перезавантажте сторінку."

msgid "page.list.edit.failure"
msgstr "❌ Щось пішло не так. Будь ласка, спробуйте ще раз."

msgid "page.list.by_and_date"
msgstr "Список за %(by)s, створено<span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Список порожній."

msgid "page.list.new_item"
msgstr "Додайте або видаліть файл з цього списку, знайшовши його і відкривши вкладку \"Списки\"."

msgid "page.profile.title"
msgstr "Особистий запис"

msgid "page.profile.not_found"
msgstr "Такий обліковий запис не знайдено."

msgid "page.profile.header.edit"
msgstr "редагувати"

msgid "page.profile.change_display_name.text"
msgstr "Змініть своє ім'я для відображення. Ваш ідентифікатор (частина після \"#\") не може бути змінений."

msgid "page.profile.change_display_name.button"
msgstr "Зберегти"

msgid "page.profile.change_display_name.success"
msgstr "✅ Збережено. Будь ласка, перезавантажте сторінку."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Щось пішло не так. Будь ласка, спробуйте ще раз."

msgid "page.profile.created_time"
msgstr "Профіль створено <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Списки"

msgid "page.profile.lists.no_lists"
msgstr "Наразі немає списків"

msgid "page.profile.lists.new_list"
msgstr "Створіть новий список, знайшовши файл і відкривши вкладку \"Списки\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Реформа авторського права необхідна для національної безпеки"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Коротко: Китайські LLM (включаючи DeepSeek) навчаються на моєму незаконному архіві книг і статей — найбільшому у світі. Захід повинен переглянути закон про авторське право як питання національної безпеки."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "супровідні статті від TorrentFreak: <a %(torrentfreak)s>перша</a>, <a %(torrentfreak_2)s>друга</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Не так давно «тіньові бібліотеки» були на межі зникнення. Sci-Hub, величезний незаконний архів наукових статей, припинив приймати нові роботи через судові позови. «Z-Library», найбільша незаконна бібліотека книг, побачила, як її ймовірних творців заарештували за кримінальні порушення авторського права. Вони неймовірно змогли уникнути арешту, але їхня бібліотека все ще під загрозою."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Коли Z-Library зіткнулася з закриттям, я вже зробив резервну копію всієї її бібліотеки і шукав платформу для її розміщення. Це стало моєю мотивацією для створення Архіву Анни: продовження місії, що стоїть за цими попередніми ініціативами. Відтоді ми виросли до найбільшої тіньової бібліотеки у світі, розміщуючи понад 140 мільйонів текстів, захищених авторським правом, у різних форматах — книги, наукові статті, журнали, газети та інше."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Моя команда і я — ідеологи. Ми вважаємо, що збереження та розміщення цих файлів є морально правильним. Бібліотеки по всьому світу стикаються зі скороченням фінансування, і ми не можемо довірити спадщину людства корпораціям."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Потім з'явився штучний інтелект. Практично всі великі компанії, що створюють LLM, звернулися до нас для навчання на наших даних. Більшість (але не всі!) компаній зі США переглянули своє рішення, коли зрозуміли незаконний характер нашої роботи. На відміну від цього, китайські фірми з ентузіазмом прийняли нашу колекцію, очевидно, не турбуючись про її законність. Це примітно, враховуючи роль Китаю як підписанта майже всіх основних міжнародних договорів про авторське право."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Ми надали високошвидкісний доступ приблизно 30 компаніям. Більшість з них — компанії LLM, а деякі — брокери даних, які перепродаватимуть нашу колекцію. Більшість з них китайські, хоча ми також працювали з компаніями зі США, Європи, Росії, Південної Кореї та Японії. DeepSeek <a %(arxiv)s>зізнався</a>, що попередня версія була навчена на частині нашої колекції, хоча вони неохоче говорять про свою останню модель (ймовірно, також навчена на наших даних)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Якщо Захід хоче залишатися попереду в гонці LLM, а в кінцевому підсумку і AGI, йому потрібно переглянути свою позицію щодо авторського права, і швидко. Незалежно від того, чи погоджуєтеся ви з нами щодо нашої моральної позиції, це тепер стає питанням економіки, а навіть національної безпеки. Всі блоки влади будують штучних супер-науковців, супер-хакерів і супер-військових. Свобода інформації стає питанням виживання для цих країн — навіть питанням національної безпеки."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Наша команда з усього світу, і ми не маємо конкретної орієнтації. Але ми б закликали країни з сильними законами про авторське право використовувати цю екзистенційну загрозу для їх реформування. Тож що робити?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Наша перша рекомендація проста: скоротити термін дії авторського права. У США авторське право надається на 70 років після смерті автора. Це абсурдно. Ми можемо привести це у відповідність з патентами, які надаються на 20 років після подання. Це має бути більш ніж достатньо часу для авторів книг, статей, музики, мистецтва та інших творчих робіт, щоб отримати повну компенсацію за свої зусилля (включаючи довгострокові проекти, такі як екранізації)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Тоді, як мінімум, політики повинні включити винятки для масового збереження та поширення текстів. Якщо втрата доходу від окремих клієнтів є основною проблемою, розповсюдження на особистому рівні може залишатися забороненим. Натомість ті, хто здатний керувати великими сховищами — компанії, що навчають LLM, разом з бібліотеками та іншими архівами — будуть охоплені цими винятками."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Деякі країни вже роблять версію цього. TorrentFreak <a %(torrentfreak)s>повідомив</a>, що Китай і Японія ввели винятки для ШІ у свої закони про авторське право. Нам не зрозуміло, як це взаємодіє з міжнародними договорами, але це, безумовно, дає захист їхнім внутрішнім компаніям, що пояснює те, що ми спостерігаємо."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Що стосується Архіву Анни — ми продовжимо нашу підпільну роботу, засновану на моральних переконаннях. Проте наше найбільше бажання — вийти на світло і легально посилити наш вплив. Будь ласка, реформуйте авторське право."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Читайте супровідні статті від TorrentFreak: <a %(torrentfreak)s>перша</a>, <a %(torrentfreak_2)s>друга</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Переможці конкурсу на візуалізацію ISBN з призом у $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Коротко: Ми отримали неймовірні заявки на конкурс на візуалізацію ISBN з призом у $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Кілька місяців тому ми оголосили <a %(all_isbns)s>конкурс з призом у $10,000</a> на створення найкращої можливої візуалізації наших даних, що показує простір ISBN. Ми наголосили на показі, які файли ми вже архівували, а які ні, і пізніше додали набір даних, що описує, скільки бібліотек мають ISBN (міра рідкості)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Ми були вражені відповіддю. Було стільки креативності. Велике спасибі всім, хто взяв участь: ваша енергія та ентузіазм заразливі!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Зрештою, ми хотіли відповісти на такі питання: <strong>які книги існують у світі, скільки ми вже архівували, і на яких книгах слід зосередитися далі?</strong> Приємно бачити, що так багато людей цікавляться цими питаннями."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Ми самі почали з базової візуалізації. Менше ніж у 300 кб, це зображення стисло представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Усі ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Файли в Архіві Анни"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Витік даних CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Індекс електронних книг EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Інтернет-архів"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Глобальний реєстр видавців ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Російська державна бібліотека"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Імперська бібліотека Трантора"

#, fuzzy
msgid "common.back"
msgstr "Назад"

#, fuzzy
msgid "common.forward"
msgstr "Вперед"

#, fuzzy
msgid "common.last"
msgstr "Останній"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Будь ласка, перегляньте <a %(all_isbns)s>оригінальний допис у блозі</a> для отримання додаткової інформації."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Ми оголосили виклик для покращення цього. Ми мали нагородити перше місце премією в $6,000, друге місце — $3,000, а третє місце — $1,000. Через величезний відгук та неймовірні подання, ми вирішили трохи збільшити призовий фонд і нагородити чотири третіх місця по $500 кожне. Переможці наведені нижче, але обов’язково перегляньте всі подання <a %(annas_archive)s>тут</a>, або завантажте наш <a %(a_2025_01_isbn_visualization_files)s>об'єднаний торрент</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Перше місце $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Це <a %(phiresky_github)s>подання</a> (<a %(annas_archive_note_2951)s>коментар на Gitlab</a>) є саме тим, чого ми хотіли, і навіть більше! Нам особливо сподобалися неймовірно гнучкі варіанти візуалізації (навіть з підтримкою користувацьких шейдерів), але з повним списком пресетів. Нам також сподобалося, наскільки все швидко і плавно працює, проста реалізація (яка навіть не має бекенду), розумна мінікарта та детальне пояснення в їхньому <a %(phiresky_github)s>дописі в блозі</a>. Неймовірна робота і заслужена перемога!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Друге місце $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Ще одне неймовірне <a %(annas_archive_note_2913)s>подання</a>. Не таке гнучке, як перше місце, але нам насправді більше сподобалася його макрорівнева візуалізація порівняно з першим місцем (крива заповнення простору, межі, маркування, виділення, панорамування та масштабування). <a %(annas_archive_note_2971)s>Коментар</a> Джо Девіса відгукнувся нам:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "«Хоча ідеальні квадрати та прямокутники математично приємні, вони не забезпечують кращої локальності в контексті картографування. Я вважаю, що асиметрія, властива цим кривим Гільберта або класичним Мортону, не є недоліком, а особливістю. Так само, як відома форма чобота Італії робить її миттєво впізнаваною на карті, унікальні \"особливості\" цих кривих можуть слугувати когнітивними орієнтирами. Ця відмінність може покращити просторову пам’ять і допомогти користувачам орієнтуватися, потенційно полегшуючи пошук конкретних регіонів або виявлення шаблонів»."

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "І все ще багато варіантів для візуалізації та рендерингу, а також неймовірно плавний та інтуїтивно зрозумілий інтерфейс. Міцне друге місце!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Третє місце $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "У цьому <a %(annas_archive_note_2940)s>поданні</a> нам дуже сподобалися різні види перегляду, зокрема порівняльний та видавничий перегляди."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Третє місце $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Хоча інтерфейс не найвідшліфованіший, це <a %(annas_archive_note_2917)s>подання</a> відповідає багатьом вимогам. Нам особливо сподобалася його функція порівняння."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Третє місце $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Як і перше місце, це <a %(annas_archive_note_2975)s>подання</a> вразило нас своєю гнучкістю. Зрештою, це те, що робить інструмент візуалізації чудовим: максимальна гнучкість для досвідчених користувачів, зберігаючи простоту для середніх користувачів."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Третє місце $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Останнє <a %(annas_archive_note_2947)s>подання</a>, яке отримало нагороду, досить базове, але має деякі унікальні функції, які нам дуже сподобалися. Нам сподобалося, як вони показують, скільки наборів даних охоплюють певний ISBN як міру популярності/надійності. Нам також дуже сподобалася простота, але ефективність використання повзунка непрозорості для порівнянь."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Помітні ідеї"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Деякі інші ідеї та реалізації, які нам особливо сподобалися:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Хмарочоси для рідкісності"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Живі статистики"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Анотації, а також живі статистики"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Унікальний вигляд карти та фільтри"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Крута стандартна кольорова схема та теплокарта."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Легке перемикання між Datasets для швидких порівнянь."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Гарні мітки."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Шкала з кількістю книг."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Багато слайдерів для порівняння Datasets, наче ви діджей."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Ми могли б продовжувати ще довго, але зупинимося тут. Обов’язково перегляньте всі подання <a %(annas_archive)s>тут</a>, або завантажте наш <a %(a_2025_01_isbn_visualization_files)s>об’єднаний торрент</a>. Так багато подань, і кожне приносить унікальну перспективу, чи то в інтерфейсі, чи в реалізації."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Ми принаймні інтегруємо подання, що зайняло перше місце, на наш основний вебсайт, а можливо, і деякі інші. Ми також почали думати про те, як організувати процес ідентифікації, підтвердження та архівування найрідкісніших книг. Більше інформації згодом."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Дякуємо всім, хто взяв участь. Це дивовижно, що так багато людей піклуються."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Наші серця сповнені вдячності."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Візуалізація всіх ISBN — нагорода $10,000 до 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Це зображення представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Це зображення має розмір 1000×800 пікселів. Кожен піксель представляє 2,500 ISBN. Якщо у нас є файл для ISBN, ми робимо цей піксель більш зеленим. Якщо ми знаємо, що ISBN було видано, але у нас немає відповідного файлу, ми робимо його більш червоним."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Менше ніж у 300 кб, це зображення стисло представляє найбільший повністю відкритий «список книг», коли-небудь зібраний в історії людства (кілька сотень ГБ у повному стисненні)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Воно також показує: ще багато роботи залишилося для резервного копіювання книг (у нас є лише 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Передумови"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Як Анна Архів може досягти своєї місії збереження всіх знань людства, не знаючи, які книги ще існують? Нам потрібен список завдань. Один із способів скласти карту — це використання номерів ISBN, які з 1970-х років присвоюються кожній опублікованій книзі (у більшості країн)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Не існує центрального органу, який знає всі призначення ISBN. Натомість це розподілена система, де країни отримують діапазони номерів, які потім призначають меншим діапазонам великим видавцям, які можуть далі поділити діапазони на менших видавців. Нарешті, окремі номери присвоюються книгам."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Ми почали складати карту ISBN <a %(blog)s>два роки тому</a> з нашого збору даних ISBNdb. З того часу ми зібрали багато інших джерел metadata, таких як <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby та інші. Повний список можна знайти на сторінках “Datasets” та “Torrents” на Анна Архів. Зараз у нас найбільша повністю відкрита, легко завантажувана колекція metadata книг (а отже, і ISBN) у світі."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Ми <a %(blog)s>багато писали</a> про те, чому ми дбаємо про збереження, і чому ми зараз у критичному вікні. Ми повинні ідентифікувати рідкісні, недооцінені та унікально вразливі книги та зберегти їх. Наявність хорошої metadata на всі книги у світі допомагає в цьому."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Візуалізація"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Окрім загального зображення, ми також можемо переглянути окремі datasets, які ми отримали. Використовуйте випадаюче меню та кнопки, щоб переключатися між ними."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "У цих зображеннях можна побачити багато цікавих візерунків. Чому існує певна регулярність ліній і блоків, яка, здається, відбувається на різних масштабах? Що таке порожні області? Чому певні datasets так згруповані? Ми залишимо ці питання як вправу для читача."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Винагорода $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Тут є багато чого дослідити, тому ми оголошуємо винагороду за покращення візуалізації вище. На відміну від більшості наших винагород, ця має обмежений час. Ви повинні <a %(annas_archive)s>подати</a> свій відкритий код до 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Найкраща подача отримає $6,000, друге місце — $3,000, а третє місце — $1,000. Всі винагороди будуть виплачені за допомогою Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Нижче наведені мінімальні критерії. Якщо жодна подача не відповідає критеріям, ми все ще можемо присудити деякі винагороди, але це буде на наш розсуд."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Форкніть цей репозиторій і відредагуйте HTML цього блогу (жодні інші бекенди, окрім нашого Flask бекенду, не дозволені)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Зробіть зображення вище плавно масштабованим, щоб ви могли збільшувати до окремих ISBN. Натискання на ISBN має перенаправляти на сторінку metadata або пошук на Анна Архів."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Ви все ще повинні мати можливість перемикатися між усіма різними datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Діапазони країн та видавців повинні підсвічуватися при наведенні. Ви можете використовувати, наприклад, <a %(github_xlcnd_isbnlib)s>data4info.py в isbnlib</a> для інформації про країни та наш збір “isbngrp” для видавців (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Це повинно добре працювати на настільних комп’ютерах і мобільних пристроях."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Для додаткових балів (це лише ідеї — дайте волю своїй творчості):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Сильна увага буде приділена зручності використання та зовнішньому вигляду."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Показуйте фактичну metadata для окремих ISBN при збільшенні, наприклад, назву та автора."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Краща крива заповнення простору. Наприклад, зигзаг, що йде від 0 до 4 на першому рядку, а потім назад (у зворотному напрямку) від 5 до 9 на другому рядку — рекурсивно застосовано."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Різні або настроювані колірні схеми."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Спеціальні види для порівняння Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Способи налагодження проблем, таких як інші metadata, які не дуже узгоджуються (наприклад, значно різні назви)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Анотування зображень з коментарями щодо ISBN або діапазонів."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Будь-які евристики для ідентифікації рідкісних або під загрозою книг."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Будь-які творчі ідеї, які ви можете придумати!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Ви МОЖЕТЕ повністю відхилитися від мінімальних критеріїв і зробити абсолютно іншу візуалізацію. Якщо це дійсно вражаюче, то це кваліфікується для винагороди, але на наш розсуд."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Зробіть подання, залишивши коментар до <a %(annas_archive)s>цього питання</a> з посиланням на ваш форкований репозиторій, запит на злиття або різницю."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Код"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Код для створення цих зображень, а також інші приклади, можна знайти в <a %(annas_archive)s>цьому каталозі</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Ми розробили компактний формат даних, з яким вся необхідна інформація про ISBN займає близько 75 МБ (у стиснутому вигляді). Опис формату даних і код для його створення можна знайти <a %(annas_archive_l1244_1319)s>тут</a>. Для винагороди вам не потрібно використовувати це, але це, ймовірно, найзручніший формат для початку. Ви можете трансформувати наші metadata як завгодно (хоча весь ваш код має бути з відкритим вихідним кодом)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Ми не можемо дочекатися, щоб побачити, що ви придумаєте. Удачі!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Контейнери Архіву Анни (AAC): стандартизація випусків з найбільшої у світі тіньової бібліотеки"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Архів Анни став найбільшою тіньовою бібліотекою у світі, що вимагає від нас стандартизації наших випусків."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Архів Анни</a> став безперечно найбільшою тіньовою бібліотекою у світі і єдиною тіньовою бібліотекою такого масштабу, яка є повністю з відкритим вихідним кодом і відкритими даними. Нижче наведено таблицю з нашої сторінки Datasets (трохи змінена):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Ми досягли цього трьома способами:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Віддзеркалення існуючих тіньових бібліотек з відкритими даними (як Sci-Hub і Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Допомога тіньовим бібліотекам, які хочуть бути більш відкритими, але не мали часу або ресурсів для цього (як колекція коміксів Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Збирання даних з бібліотек, які не бажають ділитися даними в масовому порядку (як Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Для (2) та (3) ми тепер самостійно керуємо значною колекцією торрентів (сотні терабайтів). До цього часу ми підходили до цих колекцій як до унікальних, тобто створювали індивідуальну інфраструктуру та організацію даних для кожної колекції. Це додає значних накладних витрат до кожного випуску і робить особливо складним здійснення більш поступових випусків."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Ось чому ми вирішили стандартизувати наші випуски. Це технічний блог-пост, у якому ми представляємо наш стандарт: <strong>Контейнери Архіву Анни</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Цілі дизайну"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Наш основний випадок використання — це розповсюдження файлів та пов'язаних з ними metadata з різних існуючих колекцій. Наші найважливіші міркування:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Різнорідні файли та metadata, якомога ближче до оригінального формату."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Різнорідні ідентифікатори в джерельних бібліотеках або навіть відсутність ідентифікаторів."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Окремі випуски metadata проти даних файлів або випуски лише metadata (наприклад, наш випуск ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Розповсюдження через торренти, хоча з можливістю інших методів розповсюдження (наприклад, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Незмінні записи, оскільки ми повинні припускати, що наші торренти будуть існувати вічно."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Інкрементальні випуски / додаткові випуски."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Машинозчитувані та записувані, зручно та швидко, особливо для нашого стеку (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Досить легка перевірка людиною, хоча це вторинне до машинозчитуваності."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Легко засівати наші колекції за допомогою стандартного орендованого seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Бінарні дані можуть бути безпосередньо обслуговувані веб-серверами, такими як Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Деякі нецілі:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Нам не важливо, щоб файли було легко переглядати вручну на диску або шукати без попередньої обробки."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Нам не важливо бути безпосередньо сумісними з існуючим бібліотечним програмним забезпеченням."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Хоча будь-хто повинен легко засівати нашу колекцію за допомогою торрентів, ми не очікуємо, що файли будуть використовувані без значних технічних знань та зобов'язань."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Оскільки Архів Анни є відкритим кодом, ми хочемо безпосередньо використовувати наш формат. Коли ми оновлюємо наш пошуковий індекс, ми отримуємо доступ лише до загальнодоступних шляхів, щоб будь-хто, хто форкне нашу бібліотеку, міг швидко почати роботу."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Стандарт"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Зрештою, ми зупинилися на відносно простому стандарті. Він досить гнучкий, не нормативний і знаходиться в процесі розробки."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Контейнер Архіву Анни) — це один елемент, що складається з <strong>metadata</strong>, і, за бажанням, <strong>бінарних даних</strong>, обидва з яких є незмінними. Він має глобально унікальний ідентифікатор, який називається <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Колекція.</strong> Кожен AAC належить до колекції, яка за визначенням є списком AAC, що є семантично узгодженими. Це означає, що якщо ви вносите значні зміни у формат metadata, то вам потрібно створити нову колекцію."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Колекції “записів” та “файлів”.</strong> За звичаєм, часто зручно випускати “записи” та “файли” як різні колекції, щоб їх можна було випускати за різними графіками, наприклад, на основі швидкості скрапінгу. “Запис” — це колекція, що містить лише metadata, яка включає інформацію, таку як назви книг, автори, ISBN тощо, тоді як “файли” — це колекції, що містять самі файли (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Формат AACID такий: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Наприклад, фактичний AACID, який ми випустили, це <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: назва колекції, яка може містити ASCII літери, цифри та підкреслення (але не подвійні підкреслення)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: коротка версія ISO 8601, завжди в UTC, наприклад, <code>20220723T194746Z</code>. Це число повинно монотонно збільшуватися для кожного випуску, хоча його точна семантика може відрізнятися для кожної колекції. Ми пропонуємо використовувати час скрапінгу або генерації ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: специфічний для колекції ідентифікатор, якщо застосовно, наприклад, ID Z-Library. Може бути опущено або скорочено. Повинен бути опущений або скорочений, якщо AACID в іншому випадку перевищить 150 символів."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, але стиснутий до ASCII, наприклад, з використанням base57. Ми наразі використовуємо бібліотеку Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Діапазон AACID.</strong> Оскільки AACID містять монотонно зростаючі часові мітки, ми можемо використовувати це для позначення діапазонів у межах певної колекції. Ми використовуємо цей формат: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, де часові мітки включені. Це узгоджується з нотацією ISO 8601. Діапазони є безперервними і можуть перекриватися, але у випадку перекриття повинні містити ідентичні записи, як і ті, що були раніше випущені в цій колекції (оскільки AAC є незмінними). Пропущені записи не допускаються."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Файл metadata.</strong> Файл metadata містить metadata діапазону AAC для однієї конкретної колекції. Вони мають такі властивості:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Ім'я файлу повинно бути діапазоном AACID, з префіксом <code style=\"color: red\">annas_archive_meta__</code> і закінчуватися на <code>.jsonl.zstd</code>. Наприклад, один з наших випусків називається<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Як вказано у розширенні файлу, тип файлу — це <a %(jsonlines)s>JSON Lines</a>, стиснутий за допомогою <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Кожен об'єкт JSON повинен містити наступні поля на верхньому рівні: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (необов'язково). Інші поля не допускаються."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> — це довільні metadata, відповідно до семантики колекції. Вони повинні бути семантично узгодженими в межах колекції."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> є необов'язковим і є назвою папки з бінарними даними, яка містить відповідні бінарні дані. Ім'я файлу відповідних бінарних даних у цій папці — це AACID запису."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Префікс <code style=\"color: red\">annas_archive_meta__</code> може бути адаптований до назви вашої установи, наприклад, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Папка з бінарними даними.</strong> Папка з бінарними даними діапазону AAC для однієї конкретної колекції. Вони мають такі властивості:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Назва каталогу повинна бути діапазоном AACID, з префіксом <code style=\"color: green\">annas_archive_data__</code>, і без суфікса. Наприклад, один з наших фактичних випусків має каталог під назвою<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Каталог повинен містити файли даних для всіх AAC у вказаному діапазоні. Кожен файл даних повинен мати AACID як ім'я файлу (без розширень)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Рекомендується робити ці папки досить керованими за розміром, наприклад, не більше 100 ГБ-1 ТБ кожна, хоча ця рекомендація може змінюватися з часом."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Торренти.</strong> Файли metadata та папки з бінарними даними можуть бути об'єднані в торренти, з одним торрентом на файл metadata або одним торрентом на папку з бінарними даними. Торренти повинні мати оригінальну назву файлу/каталогу плюс суфікс <code>.torrent</code> як їхню назву файлу."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Приклад"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Розглянемо наш нещодавній реліз Z-Library як приклад. Він складається з двох колекцій: “<span style=\"background: #fffaa3\">zlib3_records</span>” та “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Це дозволяє нам окремо збирати та випускати записи metadata з фактичних файлів книг. Таким чином, ми випустили два торренти з файлами metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Ми також випустили кілька торрентів з папками бінарних даних, але тільки для колекції “<span style=\"background: #ffd6fe\">zlib3_files</span>”, всього 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Запустивши <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, ми можемо побачити, що всередині:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "У цьому випадку це metadata книги, як повідомляє Z-Library. На верхньому рівні у нас є лише “aacid” та “metadata”, але немає “data_folder”, оскільки немає відповідних бінарних даних. AACID містить “22430000” як основний ID, який ми можемо побачити, взятий з “zlibrary_id”. Ми можемо очікувати, що інші AAC у цій колекції матимуть таку ж структуру."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Тепер запустимо <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Це набагато менше metadata AAC, хоча основна частина цього AAC знаходиться в іншому місці у бінарному файлі! Зрештою, у нас є “data_folder” цього разу, тому ми можемо очікувати, що відповідні бінарні дані будуть розташовані за адресою <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” містить “zlibrary_id”, тому ми можемо легко асоціювати його з відповідним AAC у колекції “zlib_records”. Ми могли б асоціювати різними способами, наприклад, через AACID — стандарт цього не вимагає."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Зверніть увагу, що також не обов'язково, щоб поле “metadata” саме по собі було JSON. Це може бути рядок, що містить XML або будь-який інший формат даних. Ви навіть можете зберігати інформацію про metadata у відповідному бінарному блоці, наприклад, якщо це багато даних."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Висновок"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "З цим стандартом ми можемо робити релізи більш поступово і легше додавати нові джерела даних. У нас вже є кілька захоплюючих релізів у процесі!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Ми також сподіваємося, що іншим тіньовим бібліотекам стане легше дзеркалити наші колекції. Зрештою, наша мета — зберегти людські знання та культуру назавжди, тому чим більше надмірності, тим краще."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Оновлення Анни: повністю відкрите джерело архіву, ElasticSearch, понад 300 ГБ обкладинок книг"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Ми працюємо цілодобово, щоб надати гарну альтернативу з Архівом Анни. Ось деякі з досягнень, яких ми досягли нещодавно."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "З падінням Z-Library та арештом його (ймовірних) засновників, ми працюємо цілодобово, щоб надати гарну альтернативу з Архівом Анни (ми не будемо посилатися на нього тут, але ви можете знайти його в Google). Ось деякі з досягнень, яких ми досягли нещодавно."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Архів Анни є повністю відкритим джерелом"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Ми вважаємо, що інформація повинна бути вільною, і наш власний код не є винятком. Ми випустили весь наш код на нашому приватно розміщеному екземплярі Gitlab: <a %(annas_archive)s>Програмне забезпечення Анни</a>. Ми також використовуємо трекер проблем для організації нашої роботи. Якщо ви хочете долучитися до нашої розробки, це чудове місце для початку."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Щоб дати вам уявлення про те, над чим ми працюємо, візьміть нашу нещодавню роботу над покращенням продуктивності на стороні клієнта. Оскільки ми ще не реалізували пагінацію, ми часто повертали дуже довгі сторінки пошуку з 100-200 результатами. Ми не хотіли занадто рано обрізати результати пошуку, але це означало, що це уповільнювало деякі пристрої. Для цього ми реалізували невеликий трюк: ми обгорнули більшість результатів пошуку в HTML-коментарі (<code><!-- --></code>), а потім написали невеликий Javascript, який виявляв, коли результат повинен стати видимим, у цей момент ми розгортали коментар:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM «віртуалізація» реалізована в 23 рядках, без потреби у складних бібліотеках! Це той тип швидкого прагматичного коду, який ви отримуєте, коли маєте обмежений час і реальні проблеми, які потрібно вирішити. Повідомляється, що наш пошук тепер добре працює на повільних пристроях!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Ще одним великим зусиллям було автоматизувати створення бази даних. Коли ми запускалися, ми просто безладно збирали різні джерела разом. Тепер ми хочемо тримати їх оновленими, тому ми написали кілька скриптів для завантаження нових metadata з двох форків Library Genesis і інтегруємо їх. Мета полягає не лише в тому, щоб зробити це корисним для нашого архіву, але й полегшити роботу для всіх, хто хоче пограти з metadata тіньової бібліотеки. Метою буде Jupyter notebook, який має всілякі цікаві metadata, щоб ми могли проводити більше досліджень, наприклад, з'ясувати, який <a %(blog)s>відсоток ISBN зберігається назавжди</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Нарешті, ми оновили нашу систему пожертвувань. Тепер ви можете використовувати кредитну картку, щоб безпосередньо вносити гроші на наші криптогаманці, не потребуючи знань про криптовалюти. Ми будемо продовжувати стежити за тим, наскільки добре це працює на практиці, але це велика справа."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Перехід на ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Один з наших <a %(annas_archive)s>квитків</a> був збіркою проблем з нашою системою пошуку. Ми використовували MySQL full-text search, оскільки всі наші дані були в MySQL. Але у нього були свої обмеження:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Деякі запити займали дуже багато часу, до того моменту, коли вони захоплювали всі відкриті з'єднання."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "За замовчуванням MySQL має мінімальну довжину слова, або ваш індекс може стати дуже великим. Люди повідомляли, що не можуть шукати «Ben Hur»."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Пошук був лише дещо швидким, коли повністю завантажувався в пам'ять, що вимагало від нас отримати дорожчу машину для запуску цього, плюс деякі команди для попереднього завантаження індексу при запуску."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Ми не змогли б легко розширити його для створення нових функцій, таких як краща <a %(wikipedia_cjk_characters)s>токенізація для мов без пробілів</a>, фільтрація/фасетування, сортування, пропозиції «чи ви мали на увазі», автозаповнення тощо."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Після розмови з багатьма експертами ми зупинилися на ElasticSearch. Це не було ідеально (їхні стандартні пропозиції «чи ви мали на увазі» та функції автозаповнення не дуже), але загалом це було набагато краще, ніж MySQL для пошуку. Ми все ще не <a %(youtube)s>надто захоплені</a> використанням його для будь-яких критично важливих даних (хоча вони зробили багато <a %(elastic_co)s>прогресу</a>), але загалом ми дуже задоволені переходом."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "На даний момент ми реалізували набагато швидший пошук, кращу підтримку мов, краще сортування за релевантністю, різні варіанти сортування та фільтрацію за мовою/типом книги/типом файлу. Якщо вам цікаво, як це працює, <a %(annas_archive_l140)s>подивіться</a> <a %(annas_archive_l1115)s>на</a> <a %(annas_archive_l1635)s>це</a>. Це досить доступно, хоча не завадило б більше коментарів…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Випущено понад 300 ГБ обкладинок книг"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Нарешті, ми раді оголосити про невеликий випуск. У співпраці з людьми, які керують форком Libgen.rs, ми ділимося всіма їхніми обкладинками книг через торренти та IPFS. Це розподілить навантаження на перегляд обкладинок серед більшої кількості машин і краще їх зберігатиме. У багатьох (але не у всіх) випадках обкладинки книг включені в самі файли, тому це свого роду «похідні дані». Але мати їх в IPFS все ще дуже корисно для щоденної роботи як Архіву Анни, так і різних форків Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Як завжди, ви можете знайти цей випуск у Pirate Library Mirror (РЕДАГУВАНО: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>). Ми не будемо посилатися на нього тут, але ви можете легко його знайти."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Сподіваємося, що ми зможемо трохи знизити темп, тепер, коли у нас є гідна альтернатива Z-Library. Це навантаження не є особливо стійким. Якщо ви зацікавлені в допомозі з програмуванням, обслуговуванням серверів або роботою збереження, обов'язково зв'яжіться з нами. Ще багато <a %(annas_archive)s>роботи</a> попереду. Дякуємо за ваш інтерес та підтримку."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Архів Анни зберіг найбільшу у світі тіньову бібліотеку коміксів (95 ТБ) — ви можете допомогти її розповсюдити"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Найбільша у світі тіньова бібліотека коміксів мала одну точку відмови... до сьогодні."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Обговорити на Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Найбільша тіньова бібліотека коміксів, ймовірно, належить певному форку Library Genesis: Libgen.li. Один адміністратор, який керує цим сайтом, зумів зібрати неймовірну колекцію коміксів з понад 2 мільйонами файлів, загальною вагою понад 95 ТБ. Однак, на відміну від інших колекцій Library Genesis, ця не була доступна в масовому порядку через торренти. Ви могли отримати доступ до цих коміксів лише індивідуально через його повільний особистий сервер — одну точку відмови. До сьогодні!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "У цій публікації ми розповімо вам більше про цю колекцію та про наш збір коштів для підтримки подальшої роботи."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Доктор Барбара Гордон намагається загубитися у буденному світі бібліотеки…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Форки Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Спочатку трохи передісторії. Ви, можливо, знаєте Library Genesis за їхню епічну колекцію книг. Менше людей знають, що волонтери Library Genesis створили інші проєкти, такі як значна колекція журналів і стандартних документів, повна резервна копія Sci-Hub (у співпраці з засновницею Sci-Hub, Олександрою Елбакян), і, дійсно, величезна колекція коміксів."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "У певний момент різні оператори дзеркал Library Genesis пішли своїми шляхами, що призвело до поточної ситуації з кількома різними «форками», які все ще носять назву Library Genesis. Форк Libgen.li унікально має цю колекцію коміксів, а також значну колекцію журналів (над якою ми також працюємо)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Співпраця"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "З огляду на її розмір, ця колекція давно була в нашому списку бажань, тому після нашого успіху з резервним копіюванням Z-Library ми націлилися на цю колекцію. Спочатку ми знімали її безпосередньо, що було досить складно, оскільки їхній сервер був не в найкращому стані. Таким чином ми отримали близько 15 ТБ, але це було повільно."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "На щастя, нам вдалося зв’язатися з оператором бібліотеки, який погодився надіслати нам усі дані безпосередньо, що було набагато швидше. Все одно знадобилося більше півроку, щоб передати та обробити всі дані, і ми майже втратили їх через пошкодження диска, що означало б початок з нуля."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Цей досвід змусив нас повірити, що важливо якомога швидше поширити ці дані, щоб їх можна було дзеркалювати широко. Ми лише за один-два невдало вчасних інциденти від втрати цієї колекції назавжди!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Колекція"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Швидкий рух означає, що колекція трохи неорганізована… Давайте подивимося. Уявіть, що у нас є файлова система (яку насправді ми розділяємо на торренти):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Перша директорія, <code>/repository</code>, є більш структурованою частиною цього. Ця директорія містить так звані «тисячні каталоги»: каталоги, кожен з яких містить тисячу файлів, які поступово нумеруються в базі даних. Директорія <code>0</code> містить файли з comic_id від 0 до 999 і так далі."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Це та ж схема, яку Library Genesis використовує для своїх колекцій художньої та нехудожньої літератури. Ідея полягає в тому, що кожен «тисячний каталог» автоматично перетворюється на торрент, як тільки він заповнюється."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Однак оператор Libgen.li ніколи не створював торренти для цієї колекції, тому тисячні каталоги, ймовірно, стали незручними і поступилися місцем «несортованим каталогам». Це <code>/comics0</code> до <code>/comics4</code>. Вони всі містять унікальні структури каталогів, які, ймовірно, мали сенс для збору файлів, але зараз нам не дуже зрозумілі. На щастя, metadata все ще безпосередньо посилається на всі ці файли, тому їхня організація зберігання на диску насправді не має значення!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata доступна у формі бази даних MySQL. Її можна завантажити безпосередньо з вебсайту Libgen.li, але ми також зробимо її доступною в торренті разом з нашою власною таблицею з усіма хешами MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Аналіз"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Коли ви отримуєте 95 ТБ у ваш кластер зберігання, ви намагаєтеся зрозуміти, що там взагалі є… Ми провели деякий аналіз, щоб побачити, чи можемо ми трохи зменшити розмір, наприклад, видаливши дублікати. Ось деякі з наших висновків:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Семантичні дублікати (різні скани однієї і тієї ж книги) теоретично можна відфільтрувати, але це складно. При ручному перегляді коміксів ми знайшли занадто багато хибних спрацьовувань."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Є деякі дублікати лише за MD5, що є відносно марнотратним, але їхнє фільтрування дало б нам лише близько 1% in економії. У такому масштабі це все ще близько 1 ТБ, але також у такому масштабі 1 ТБ не має великого значення. Ми б не хотіли ризикувати випадковим знищенням даних у цьому процесі."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Ми знайшли купу даних, які не є книгами, таких як фільми на основі коміксів. Це також здається марнотратним, оскільки вони вже широко доступні іншими способами. Однак ми зрозуміли, що не можемо просто відфільтрувати файли фільмів, оскільки є також <em>інтерактивні комікси</em>, які були випущені на комп’ютері, які хтось записав і зберіг як фільми."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Зрештою, все, що ми могли б видалити з колекції, зекономило б лише кілька відсотків. Тоді ми згадали, що ми — зберігачі даних, і люди, які будуть дзеркалити це, також зберігачі даних, і тому: «ЩО ВИ МАЄТЕ НА УВАЗІ, ВИДАЛИТИ?!» :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Тому ми представляємо вам повну, немодифіковану колекцію. Це багато даних, але ми сподіваємося, що достатньо людей захоче їх розповсюджувати."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Збір коштів"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Ми випускаємо ці дані у великих частинах. Перший торрент — це <code>/comics0</code>, який ми зібрали в один величезний файл .tar обсягом 12 ТБ. Це краще для вашого жорсткого диска та програмного забезпечення для торентів, ніж безліч менших файлів."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "У рамках цього випуску ми проводимо збір коштів. Ми прагнемо зібрати 20 000 доларів, щоб покрити операційні та контрактні витрати на цю колекцію, а також забезпечити поточні та майбутні проекти. У нас є кілька <em>масштабних</em> проектів у розробці."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Кого я підтримую своїм пожертвуванням?</em> Коротко: ми зберігаємо всі знання та культуру людства і робимо їх легко доступними. Весь наш код і дані є відкритими, ми повністю волонтерський проект, і ми вже зберегли 125 ТБ книг (на додаток до існуючих торентів Libgen та Scihub). Зрештою, ми створюємо маховик, який дозволяє та стимулює людей знаходити, сканувати та зберігати всі книги у світі. Ми напишемо про наш генеральний план у майбутньому пості. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Якщо ви пожертвуєте на 12-місячне членство “Amazing Archivist” ($780), ви зможете <strong>“усиновити торрент”</strong>, тобто ми додамо ваше ім’я користувача або повідомлення у назву одного з торентів!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Ви можете пожертвувати, перейшовши на <a %(wikipedia_annas_archive)s>Архів Анни</a> і натиснувши кнопку «Пожертвувати». Ми також шукаємо більше волонтерів: програмістів, дослідників безпеки, експертів з анонімної торгівлі та перекладачів. Ви також можете підтримати нас, надаючи послуги хостингу. І, звичайно, будь ласка, розповсюджуйте наші торренти!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Дякуємо всім, хто вже так щедро нас підтримав! Ви дійсно робите різницю."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Ось торренти, які вже випущені (ми ще обробляємо решту):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Усі торренти можна знайти на <a %(wikipedia_annas_archive)s>Архів Анни</a> у розділі «Datasets» (ми не посилаємося туди безпосередньо, щоб посилання на цей блог не видалялися з Reddit, Twitter тощо). Звідти перейдіть за посиланням на сайт Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Що далі?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Купа торентів чудово підходить для довгострокового зберігання, але не так для повсякденного доступу. Ми будемо працювати з партнерами з хостингу, щоб розмістити всі ці дані в Інтернеті (оскільки Архів Анни нічого не хостить безпосередньо). Звичайно, ви зможете знайти ці посилання для завантаження в Архіві Анни."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Ми також запрошуємо всіх працювати з цими даними! Допоможіть нам краще їх аналізувати, видаляти дублікати, розміщувати на IPFS, реміксувати, тренувати ваші моделі ШІ з ними тощо. Вони всі ваші, і ми не можемо дочекатися, щоб побачити, що ви з ними зробите."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Нарешті, як вже було сказано, у нас ще є кілька масштабних випусків (якщо <em>хтось</em> міг би <em>випадково</em> надіслати нам дамп <em>певної</em> бази даних ACS4, ви знаєте, де нас знайти…), а також створення маховика для збереження всіх книг у світі."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Тож залишайтеся з нами, ми лише починаємо."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x нових книг додано до Дзеркала Піратської Бібліотеки (+24 ТБ, 3,8 мільйона книг)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "У початковому випуску Дзеркала Піратської Бібліотеки (РЕДАГУВАНО: переміщено до <a %(wikipedia_annas_archive)s>Архів Анни</a>) ми створили дзеркало Z-Library, великої нелегальної колекції книг. Як нагадування, ось що ми написали в тому початковому блозі:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library — це популярна (і нелегальна) бібліотека. Вони взяли колекцію Library Genesis і зробили її легкою для пошуку. Крім того, вони стали дуже ефективними у залученні нових внесків книг, стимулюючи користувачів різними привілеями. Наразі вони не повертають ці нові книги до Library Genesis. І на відміну від Library Genesis, вони не роблять свою колекцію легко дзеркальною, що ускладнює широке збереження. Це важливо для їхньої бізнес-моделі, оскільки вони стягують плату за доступ до своєї колекції в обсязі (більше 10 книг на день)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Ми не робимо моральних суджень щодо стягнення грошей за масовий доступ до незаконної колекції книг. Безсумнівно, що Z-Library досягла успіху в розширенні доступу до знань і пошуку нових книг. Ми просто тут, щоб виконати свою частину: забезпечити довгострокове збереження цієї приватної колекції."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Ця колекція датується серединою 2021 року. Тим часом Z-Library зростала вражаючими темпами: вони додали близько 3,8 мільйона нових книг. Звісно, там є деякі дублікати, але більшість з них, здається, є справді новими книгами або якіснішими сканами раніше поданих книг. Це значною мірою завдяки збільшенню кількості волонтерів-модераторів у Z-Library та їхній системі масового завантаження з видаленням дублікатів. Ми хотіли б привітати їх з цими досягненнями."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Ми раді оголосити, що отримали всі книги, які були додані до Z-Library між нашим останнім дзеркалом і серпнем 2022 року. Ми також повернулися і зібрали деякі книги, які пропустили вперше. Загалом, ця нова колекція становить близько 24 ТБ, що набагато більше, ніж попередня (7 ТБ). Наше дзеркало тепер становить 31 ТБ загалом. Знову ж таки, ми видалили дублікати з Library Genesis, оскільки для цієї колекції вже доступні торренти."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Будь ласка, перейдіть до Pirate Library Mirror, щоб ознайомитися з новою колекцією (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>). Там є більше інформації про те, як структуровані файли, і що змінилося з минулого разу. Ми не будемо посилатися на це звідси, оскільки це просто блог-сайт, який не розміщує жодних незаконних матеріалів."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Звісно, роздача також є чудовим способом допомогти нам. Дякуємо всім, хто роздає наш попередній набір торрентів. Ми вдячні за позитивний відгук і раді, що є так багато людей, які піклуються про збереження знань і культури в такий незвичайний спосіб."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Як стати піратським архівістом"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Перша проблема може бути несподіваною. Це не технічна проблема чи юридична проблема. Це психологічна проблема."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Перш ніж ми заглибимося, два оновлення щодо Pirate Library Mirror (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Ми отримали надзвичайно щедрі пожертви. Перша була $10 тис. від анонімної особи, яка також підтримувала \"bookwarrior\", оригінального засновника Library Genesis. Особлива подяка bookwarrior за сприяння цій пожертві. Друга була ще $10 тис. від анонімного донора, який зв’язався з нами після нашого останнього випуску і був натхненний допомогти. Ми також отримали кілька менших пожертв. Дуже дякуємо за всю вашу щедру підтримку. У нас є кілька захоплюючих нових проектів у розробці, які це підтримає, тому залишайтеся з нами."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "У нас були деякі технічні труднощі з розміром нашого другого випуску, але наші торренти зараз активні і роздаються. Ми також отримали щедру пропозицію від анонімної особи роздавати нашу колекцію на їхніх дуже швидкісних серверах, тому ми робимо спеціальне завантаження на їхні машини, після чого всі інші, хто завантажує колекцію, повинні побачити значне покращення швидкості."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Цілі книги можуть бути написані про <em>чому</em> цифрове збереження загалом і піратське архівування зокрема, але давайте дамо короткий вступ для тих, хто не дуже знайомий. Світ виробляє більше знань і культури, ніж будь-коли раніше, але також більше з них втрачається, ніж будь-коли раніше. Людство в основному довіряє корпораціям, таким як академічні видавці, стрімінгові сервіси та компанії соціальних медіа, цю спадщину, і вони часто не виявлялися великими хранителями. Перегляньте документальний фільм \"Цифрова амнезія\" або будь-яку лекцію Джейсона Скотта."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Існують деякі установи, які добре архівують стільки, скільки можуть, але вони обмежені законом. Як пірати, ми знаходимося в унікальному положенні, щоб архівувати колекції, до яких вони не можуть доторкнутися через дотримання авторських прав або інші обмеження. Ми також можемо дзеркалити колекції багато разів по всьому світу, тим самим збільшуючи шанси на належне збереження."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Поки що ми не будемо заглиблюватися в обговорення переваг і недоліків інтелектуальної власності, моральності порушення закону, роздумів про цензуру чи питання доступу до знань і культури. З усім цим позаду, давайте заглибимося в <em>як</em>. Ми поділимося тим, як наша команда стала піратськими архівістами, і уроками, які ми засвоїли на цьому шляху. Є багато викликів, коли ви вирушаєте в цю подорож, і, сподіваємося, ми зможемо допомогти вам подолати деякі з них."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Спільнота"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Перша проблема може бути несподіваною. Це не технічна проблема чи юридична проблема. Це психологічна проблема: робота в тіні може бути неймовірно самотньою. Залежно від того, що ви плануєте робити, і вашої моделі загроз, вам, можливо, доведеться бути дуже обережними. На одному кінці спектра ми маємо людей, як Олександра Елбакян*, засновниця Sci-Hub, яка дуже відкрита щодо своєї діяльності. Але вона під великим ризиком бути заарештованою, якщо відвідає західну країну на даний момент, і може зіткнутися з десятиліттями ув'язнення. Чи готові ви взяти на себе такий ризик? Ми знаходимося на іншому кінці спектра; дуже обережні, щоб не залишити жодного сліду, і маємо сильну операційну безпеку."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Як зазначено на HN \"ynno\", Олександра спочатку не хотіла бути відомою: \"Її сервери були налаштовані на видачу детальних повідомлень про помилки з PHP, включаючи повний шлях до файлу з помилкою, який знаходився в каталозі /home/<USER>'язаному сайті, прикріпленому до її справжнього імені. До цього розкриття вона була анонімною.\" Тому використовуйте випадкові імена користувачів на комп'ютерах, які ви використовуєте для цього, на випадок, якщо ви неправильно налаштуєте щось."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Однак ця таємність має психологічну ціну. Більшість людей люблять, коли їх визнають за роботу, яку вони виконують, і все ж ви не можете отримати жодного визнання за це в реальному житті. Навіть прості речі можуть бути складними, наприклад, коли друзі запитують вас, чим ви займалися (в якийсь момент \"грався з моїм NAS / домашньою лабораторією\" стає старим)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Ось чому так важливо знайти якусь спільноту. Ви можете пожертвувати частиною операційної безпеки, довіряючи деяким дуже близьким друзям, яким ви знаєте, що можете глибоко довіряти. Навіть тоді будьте обережні, щоб нічого не записувати, на випадок, якщо їм доведеться передати свої електронні листи владі, або якщо їхні пристрої будуть скомпрометовані якимось іншим чином."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Ще краще знайти кількох однодумців-піратів. Якщо ваші близькі друзі зацікавлені приєднатися до вас, чудово! В іншому випадку, ви можете знайти інших в Інтернеті. На жаль, це все ще нішова спільнота. Поки що ми знайшли лише кілька інших, хто активний у цій сфері. Хорошими відправними точками здаються форуми Library Genesis і r/DataHoarder. Команда Archive Team також має однодумців, хоча вони діють у межах закону (навіть якщо в деяких сірих зонах закону). Традиційні сцени \"warez\" і піратства також мають людей, які думають подібним чином."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Ми відкриті до ідей щодо того, як розвивати спільноту та досліджувати ідеї. Не соромтеся писати нам у Twitter або Reddit. Можливо, ми могли б організувати якийсь форум або групу для спілкування. Однією з проблем є те, що це легко може бути піддано цензурі при використанні загальних платформ, тому нам доведеться розміщувати це самостійно. Також є компроміс між тим, щоб ці обговорення були повністю публічними (більше потенційної взаємодії) і тим, щоб зробити їх приватними (не дозволяючи потенційним \"цілям\" знати, що ми збираємося їх сканувати). Нам доведеться подумати про це. Дайте нам знати, якщо вас це цікавить!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Проєкти"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Коли ми виконуємо проєкт, він має кілька етапів:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Вибір домену / філософія: На чому ви приблизно хочете зосередитися і чому? Які ваші унікальні пристрасті, навички та обставини, які ви можете використати на свою користь?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Вибір цілі: Яку конкретну колекцію ви будете дзеркалити?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Збирання metadata: Каталогізація інформації про файли без фактичного завантаження самих (часто набагато більших) файлів."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Вибір даних: На основі metadata звуження, які дані найбільш актуальні для архівування зараз. Це може бути все, але часто є розумний спосіб заощадити місце і пропускну здатність."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Збирання даних: Фактичне отримання даних."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Розповсюдження: Упаковка в торренти, оголошення десь, залучення людей до поширення."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Це не повністю незалежні етапи, і часто інсайти з пізнішого етапу повертають вас до попереднього етапу. Наприклад, під час збирання metadata ви можете зрозуміти, що обрана вами ціль має захисні механізми, які перевищують ваш рівень навичок (наприклад, блокування IP), тому ви повертаєтеся і знаходите іншу ціль."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Вибір домену / філософія"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Немає нестачі знань і культурної спадщини, яку потрібно зберегти, що може бути приголомшливим. Ось чому часто корисно взяти паузу і подумати про те, яким може бути ваш внесок."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "У кожного є свій спосіб мислення про це, але ось деякі питання, які ви могли б собі поставити:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Чому вас це цікавить? Що вас захоплює? Якщо ми зможемо зібрати групу людей, які архівують ті речі, які їм особливо важливі, це покриє багато! Ви будете знати набагато більше, ніж середня людина, про вашу пристрасть, наприклад, які дані важливо зберегти, які найкращі колекції та онлайн-спільноти тощо."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Які навички у вас є, які ви можете використати на свою користь? Наприклад, якщо ви експерт з онлайн-безпеки, ви можете знайти способи обійти блокування IP для захищених цілей. Якщо ви чудово організовуєте спільноти, то, можливо, ви зможете зібрати людей навколо мети. Однак корисно знати трохи програмування, хоча б для підтримки гарної операційної безпеки протягом цього процесу."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Скільки часу у вас є для цього? Наша порада — починати з малого і робити більші проєкти, коли ви освоїтеся, але це може стати всепоглинаючим."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "На чому варто зосередитися, щоб отримати максимальну віддачу? Якщо ви збираєтеся витратити X годин на піратське архівування, то як ви можете отримати найбільшу \"віддачу за свої гроші\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Які унікальні способи ви думаєте про це? У вас можуть бути цікаві ідеї або підходи, які інші могли пропустити."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "У нашому випадку ми особливо дбали про довгострокове збереження науки. Ми знали про Library Genesis і те, як він був повністю дзеркалений багато разів за допомогою торрентів. Нам сподобалася ця ідея. Потім одного дня хтось із нас спробував знайти деякі наукові підручники на Library Genesis, але не зміг їх знайти, що поставило під сумнів, наскільки він насправді повний. Ми потім шукали ці підручники в Інтернеті і знайшли їх в інших місцях, що посіяло зерно для нашого проєкту. Ще до того, як ми дізналися про Z-Library, у нас була ідея не намагатися зібрати всі ці книги вручну, а зосередитися на дзеркалюванні існуючих колекцій і поверненні їх до Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Вибір цілі"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Отже, ми визначили нашу область, яку ми розглядаємо, тепер яку конкретну колекцію ми будемо дзеркалити? Є кілька речей, які роблять ціль хорошою:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Велика"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Унікальна: не вже добре охоплена іншими проєктами."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Доступна: не використовує безліч шарів захисту, щоб запобігти скрапінгу їхніх metadata та даних."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Особливе розуміння: у вас є якась особлива інформація про цю ціль, наприклад, ви якимось чином маєте спеціальний доступ до цієї колекції, або ви зрозуміли, як обійти їхній захист. Це не є обов'язковим (наш майбутній проєкт не робить нічого особливого), але це, безумовно, допомагає!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Коли ми знайшли наші підручники з науки на вебсайтах, окрім Library Genesis, ми намагалися зрозуміти, як вони потрапили в інтернет. Потім ми знайшли Z-Library і зрозуміли, що хоча більшість книг не з'являються там першими, вони зрештою там опиняються. Ми дізналися про його зв'язок з Library Genesis, а також про (фінансову) структуру стимулів та кращий інтерфейс користувача, які зробили його набагато повнішою колекцією. Потім ми провели попередній скрапінг metadata та даних і зрозуміли, що можемо обійти їхні обмеження на завантаження IP, використовуючи спеціальний доступ одного з наших членів до багатьох проксі-серверів."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Під час дослідження різних цілей вже важливо приховувати свої сліди, використовуючи VPN та одноразові електронні адреси, про які ми поговоримо пізніше."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Скрапінг metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Давайте трохи заглибимося в технічні деталі. Для фактичного скрапінгу metadata з вебсайтів ми зберегли все досить простим. Ми використовуємо скрипти на Python, іноді curl, і базу даних MySQL для зберігання результатів. Ми не використовували жодного складного програмного забезпечення для скрапінгу, яке може відображати складні вебсайти, оскільки поки що нам потрібно було скрапити лише один або два типи сторінок, просто перераховуючи ідентифікатори та розбираючи HTML. Якщо немає легко перерахованих сторінок, то вам може знадобитися справжній краулер, який намагається знайти всі сторінки."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Перш ніж почати скрапити весь вебсайт, спробуйте зробити це вручну трохи. Пройдіть кілька десятків сторінок самостійно, щоб зрозуміти, як це працює. Іноді ви вже таким чином зіткнетеся з блокуванням IP або іншою цікавою поведінкою. Те ж саме стосується скрапінгу даних: перш ніж зануритися в цю ціль, переконайтеся, що ви дійсно можете ефективно завантажити її дані."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Щоб обійти обмеження, є кілька речей, які ви можете спробувати. Чи є інші IP-адреси або сервери, які хостять ті ж дані, але не мають таких самих обмежень? Чи є якісь API-ендпоінти, які не мають обмежень, тоді як інші мають? З якою швидкістю завантаження ваш IP блокується і на скільки часу? Або вас не блокують, але знижують швидкість? Що, якщо ви створите обліковий запис користувача, як тоді змінюються речі? Чи можете ви використовувати HTTP/2, щоб зберігати з'єднання відкритими, і чи збільшує це швидкість, з якою ви можете запитувати сторінки? Чи є сторінки, які перераховують кілька файлів одночасно, і чи достатньо інформації, наведеної там?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Речі, які ви, ймовірно, захочете зберегти, включають:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Назва"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Ім'я файлу / розташування"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: може бути внутрішнім ID, але ID, такі як ISBN або DOI, також корисні."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Розмір: щоб розрахувати, скільки дискового простору вам потрібно."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Хеш (md5, sha1): щоб підтвердити, що ви правильно завантажили файл."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Дата додавання/зміни: щоб ви могли повернутися пізніше і завантажити файли, які ви не завантажили раніше (хоча ви часто також можете використовувати ID або хеш для цього)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Опис, категорія, теги, автори, мова тощо."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Зазвичай ми робимо це в два етапи. Спочатку ми завантажуємо сирі HTML-файли, зазвичай безпосередньо в MySQL (щоб уникнути великої кількості малих файлів, про що ми говоримо нижче). Потім, на окремому етапі, ми проходимо ці HTML-файли і розбираємо їх у фактичні таблиці MySQL. Таким чином, вам не потрібно повторно завантажувати все з нуля, якщо ви виявите помилку у вашому коді розбору, оскільки ви можете просто перепроцесувати HTML-файли з новим кодом. Це також часто легше паралелізувати етап обробки, таким чином заощаджуючи час (і ви можете писати код обробки, поки скрапінг виконується, замість того, щоб писати обидва етапи одночасно)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Нарешті, зауважте, що для деяких цілей збирання метаданих — це все, що є. Існують величезні колекції метаданих, які не зберігаються належним чином."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Вибір даних"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Часто ви можете використовувати метадані, щоб визначити розумну підмножину даних для завантаження. Навіть якщо ви в кінцевому підсумку хочете завантажити всі дані, може бути корисно спочатку пріоритизувати найважливіші елементи, на випадок, якщо вас виявлять і захисти будуть покращені, або тому, що вам потрібно буде купити більше дисків, або просто тому, що щось інше з’явиться у вашому житті, перш ніж ви зможете завантажити все."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Наприклад, колекція може мати кілька видань одного й того ж ресурсу (наприклад, книги чи фільму), де одне позначено як найкраща якість. Збереження цих видань спочатку було б дуже розумним. Зрештою, ви можете захотіти зберегти всі видання, оскільки в деяких випадках метадані можуть бути неправильно позначені, або можуть бути невідомі компроміси між виданнями (наприклад, \"найкраще видання\" може бути найкращим у більшості аспектів, але гіршим в інших, наприклад, фільм з вищою роздільною здатністю, але без субтитрів)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Ви також можете шукати у своїй базі даних метаданих, щоб знайти цікаві речі. Який найбільший файл, що розміщений, і чому він такий великий? Який найменший файл? Чи є цікаві або несподівані закономірності, коли мова йде про певні категорії, мови тощо? Чи є дублікати або дуже схожі назви? Чи є закономірності в тому, коли дані були додані, наприклад, один день, коли багато файлів було додано одночасно? Ви часто можете багато чого дізнатися, дивлячись на набір даних з різних сторін."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "У нашому випадку ми видалили дублікати книг Z-Library за допомогою хешів md5 у Library Genesis, тим самим заощадивши багато часу на завантаження та місця на диску. Однак це досить унікальна ситуація. У більшості випадків немає всеосяжних баз даних, які файли вже належним чином збережені іншими піратами. Це саме по собі є величезною можливістю для когось. Було б чудово мати регулярно оновлюваний огляд таких речей, як музика та фільми, які вже широко розповсюджені на торрент-сайтах, і тому мають нижчий пріоритет для включення в піратські дзеркала."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Збирання даних"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Тепер ви готові фактично завантажити дані оптом. Як згадувалося раніше, на цьому етапі ви вже повинні були вручну завантажити купу файлів, щоб краще зрозуміти поведінку та обмеження цілі. Однак, коли ви фактично почнете завантажувати багато файлів одночасно, на вас все ще чекають сюрпризи."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Наша порада тут полягає в тому, щоб зберігати все просто. Почніть із завантаження купи файлів. Ви можете використовувати Python, а потім розширити до кількох потоків. Але іноді навіть простіше — це генерувати файли Bash безпосередньо з бази даних, а потім запускати кілька з них у кількох вікнах терміналу для масштабування. Швидкий технічний трюк, який варто згадати тут, — це використання OUTFILE у MySQL, який ви можете написати будь-де, якщо вимкнете \"secure_file_priv\" у mysqld.cnf (і обов’язково також вимкніть/перевизначте AppArmor, якщо ви використовуєте Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Ми зберігаємо дані на простих жорстких дисках. Почніть з того, що у вас є, і розширюйтеся повільно. Може бути приголомшливо думати про зберігання сотень терабайтів даних. Якщо це ситуація, з якою ви стикаєтеся, просто спочатку викладіть хороший підмножину, а в своєму оголошенні попросіть допомоги у зберіганні решти. Якщо ви хочете отримати більше жорстких дисків самостійно, то r/DataHoarder має кілька хороших ресурсів для отримання вигідних пропозицій."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Намагайтеся не надто турбуватися про складні файлові системи. Легко потрапити в кролячу нору налаштування таких речей, як ZFS. Однак одна технічна деталь, про яку слід знати, полягає в тому, що багато файлових систем не справляються з великою кількістю файлів. Ми виявили, що простим обхідним шляхом є створення кількох каталогів, наприклад, для різних діапазонів ідентифікаторів або префіксів хешів."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Після завантаження даних обов’язково перевірте цілісність файлів за допомогою хешів у метаданих, якщо вони доступні."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Розповсюдження"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "У вас є дані, тим самим ви отримали у володіння перше у світі піратське дзеркало вашої цілі (швидше за все). У багатьох відношеннях найважча частина позаду, але найризикованіша частина ще попереду. Зрештою, до цього часу ви були непомітними; літали під радаром. Все, що вам потрібно було зробити, це використовувати хороший VPN протягом усього часу, не заповнювати свої особисті дані в жодних формах (очевидно), і, можливо, використовувати спеціальну сесію браузера (або навіть інший комп’ютер)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Тепер ви повинні розповсюдити дані. У нашому випадку ми спочатку хотіли повернути книги до Library Genesis, але швидко виявили труднощі в цьому (сортування художньої та нехудожньої літератури). Тому ми вирішили розповсюджувати за допомогою торрентів у стилі Library Genesis. Якщо у вас є можливість зробити внесок у існуючий проект, це може заощадити вам багато часу. Однак наразі існує небагато добре організованих піратських дзеркал."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Отже, припустимо, ви вирішили розповсюджувати торренти самостійно. Намагайтеся зберігати ці файли невеликими, щоб їх було легко дзеркалити на інших вебсайтах. Тоді вам доведеться самостійно роздавати торренти, залишаючись анонімним. Ви можете використовувати VPN (з переадресацією портів або без неї) або платити за Seedbox за допомогою оброблених біткоїнів. Якщо ви не знаєте, що означають деякі з цих термінів, вам доведеться багато читати, оскільки важливо розуміти ризики тут."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Ви можете розмістити самі торрент-файли на існуючих торрент-сайтах. У нашому випадку ми вирішили фактично розмістити вебсайт, оскільки ми також хотіли чітко поширити нашу філософію. Ви можете зробити це самостійно подібним чином (ми використовуємо Njalla для наших доменів і хостингу, оплачуючи обробленими біткоїнами), але також не соромтеся зв’язатися з нами, щоб ми розмістили ваші торренти. Ми прагнемо створити всеосяжний індекс піратських дзеркал з часом, якщо ця ідея приживеться."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Що стосується вибору VPN, про це вже багато написано, тому ми просто повторимо загальну пораду вибирати за репутацією. Фактичні політики без журналів, перевірені в суді, з тривалим досвідом захисту конфіденційності є найменш ризикованим варіантом, на нашу думку. Зверніть увагу, що навіть якщо ви робите все правильно, ви ніколи не зможете досягти нульового ризику. Наприклад, під час роздачі ваших торрентів, високо мотивований актор держави може, ймовірно, переглядати вхідні та вихідні потоки даних для серверів VPN і визначити, хто ви. Або ви можете просто якось помилитися. Ми, мабуть, вже зробили це і зробимо знову. На щастя, держави не дуже <em>турбуються</em> про піратство."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Одне з рішень, яке потрібно прийняти для кожного проекту, — це чи публікувати його під тим самим ім’ям, що й раніше, чи ні. Якщо ви продовжуєте використовувати те саме ім’я, то помилки в операційній безпеці з попередніх проектів можуть повернутися, щоб вас вкусити. Але публікація під різними іменами означає, що ви не створюєте довготривалу репутацію. Ми вирішили мати сильну операційну безпеку з самого початку, щоб ми могли продовжувати використовувати те саме ім’я, але ми не будемо вагатися публікувати під іншим ім’ям, якщо ми помилимося або якщо обставини цього вимагатимуть."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Поширення інформації може бути складним. Як ми вже сказали, це все ще нішеве співтовариство. Спочатку ми публікували на Reddit, але насправді отримали популярність на Hacker News. Наразі наша рекомендація — опублікувати це в кількох місцях і подивитися, що станеться. І знову ж таки, зв’яжіться з нами. Ми хотіли б поширити інформацію про більше зусиль піратського архівування."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Висновок"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Сподіваємося, це буде корисно для нових піратських архівістів. Ми раді вітати вас у цьому світі, тож не соромтеся звертатися. Давайте збережемо якомога більше знань і культури світу та розповсюдимо їх якомога ширше."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Представляємо Дзеркало Піратської Бібліотеки: Збереження 7 ТБ книг (які не входять до Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Цей проєкт (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>) має на меті сприяти збереженню та звільненню людських знань. Ми робимо свій невеликий і скромний внесок, йдучи слідами великих попередників."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Фокус цього проєкту ілюструється його назвою:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Пірат</strong> - Ми свідомо порушуємо закон про авторське право в більшості країн. Це дозволяє нам робити те, що легальні організації не можуть: забезпечувати, щоб книги були дзеркальними та широко доступними."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Бібліотека</strong> - Як і більшість бібліотек, ми зосереджуємося в першу чергу на письмових матеріалах, таких як книги. Можливо, в майбутньому ми розширимося на інші види медіа."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Дзеркало</strong> - Ми є виключно дзеркалом існуючих бібліотек. Ми зосереджуємося на збереженні, а не на забезпеченні легкого пошуку та завантаження книг (доступ) або створенні великої спільноти людей, які додають нові книги (джерела)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Перша бібліотека, яку ми дзеркалювали, це Z-Library. Це популярна (і незаконна) бібліотека. Вони взяли колекцію Library Genesis і зробили її легкою для пошуку. Крім того, вони стали дуже ефективними у залученні нових внесків книг, заохочуючи користувачів, які вносять внески, різними привілеями. Наразі вони не повертають ці нові книги до Library Genesis. І на відміну від Library Genesis, вони не роблять свою колекцію легко дзеркальною, що перешкоджає широкому збереженню. Це важливо для їхньої бізнес-моделі, оскільки вони стягують плату за доступ до своєї колекції в обсязі (більше 10 книг на день)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Ми не робимо моральних суджень щодо стягнення грошей за масовий доступ до незаконної колекції книг. Безсумнівно, що Z-Library досягла успіху в розширенні доступу до знань і пошуку нових книг. Ми просто тут, щоб виконати свою частину: забезпечити довгострокове збереження цієї приватної колекції."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Ми хотіли б запросити вас допомогти зберегти та звільнити людські знання, завантажуючи та розповсюджуючи наші торренти. Дивіться сторінку проєкту для отримання додаткової інформації про те, як організовані дані."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Ми також дуже запрошуємо вас внести свої ідеї щодо того, які колекції дзеркалювати далі, і як це зробити. Разом ми можемо досягти багато. Це лише невеликий внесок серед безлічі інших. Дякуємо за все, що ви робите."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Ми не надаємо посилання на файли з цього блогу. Будь ласка, знайдіть їх самостійно.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Вивантаження ISBNdb, або Скільки Книг Збережено Назавжди?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Якщо б ми правильно видалили дублікати файлів з тіньових бібліотек, який відсоток усіх книг у світі ми зберегли?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "З Дзеркалом Піратської Бібліотеки (РЕДАГУВАННЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>), наша мета - взяти всі книги у світі та зберегти їх назавжди.<sup>1</sup> Між нашими торрентами Z-Library та оригінальними торрентами Library Genesis у нас є 11,783,153 файлів. Але скільки це насправді? Якщо б ми правильно видалили дублікати цих файлів, який відсоток усіх книг у світі ми зберегли? Ми б дуже хотіли мати щось на кшталт цього:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of людської писемної спадщини збережено назавжди"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Для відсотка нам потрібен знаменник: загальна кількість книг, коли-небудь опублікованих.<sup>2</sup> Перед закриттям Google Books, інженер проєкту, Леонід Тайчер, <a %(booksearch_blogspot)s>спробував оцінити</a> цю кількість. Він дійшов — жартома — до 129,864,880 (“принаймні до неділі”). Він оцінив цю кількість, створивши єдину базу даних усіх книг у світі. Для цього він зібрав різні Datasets і потім об'єднав їх різними способами."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Як короткий відступ, є ще одна людина, яка намагалася каталогізувати всі книги у світі: Аарон Шварц, покійний цифровий активіст і співзасновник Reddit.<sup>3</sup> Він <a %(youtube)s>заснував Open Library</a> з метою створення “однієї веб-сторінки для кожної книги, яка коли-небудь була опублікована”, об'єднуючи дані з багатьох різних джерел. Він заплатив найвищу ціну за свою роботу з цифрового збереження, коли його переслідували за масове завантаження наукових статей, що призвело до його самогубства. Не потрібно говорити, що це одна з причин, чому наша група є псевдонімною, і чому ми дуже обережні. Open Library все ще героїчно керується людьми з Internet Archive, продовжуючи спадщину Аарона. Ми повернемося до цього пізніше в цьому пості."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "У блозі Google, Тайчер описує деякі виклики з оцінкою цього числа. По-перше, що таке книга? Є кілька можливих визначень:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Фізичні копії.</strong> Очевидно, це не дуже корисно, оскільки вони просто дублікати одного й того ж матеріалу. Було б круто, якби ми могли зберегти всі анотації, які люди роблять у книгах, як відомі “написання на полях” Ферма. Але, на жаль, це залишиться мрією архіваріуса."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Твори”.</strong> Наприклад, “Гаррі Поттер і Таємна кімната” як логічна концепція, що охоплює всі його версії, такі як різні переклади та перевидання. Це досить корисне визначення, але може бути важко визначити, що вважається. Наприклад, ми, ймовірно, хочемо зберегти різні переклади, хоча перевидання з незначними відмінностями можуть бути не такими важливими."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Видання”.</strong> Тут ви рахуєте кожну унікальну версію книги. Якщо щось у ній відрізняється, наприклад, інша обкладинка або інший передмов, це вважається іншим виданням."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Файли.</strong> Працюючи з тіньовими бібліотеками, такими як Library Genesis, Sci-Hub або Z-Library, є додатковий аспект. Може бути кілька сканів одного й того ж видання. І люди можуть створювати кращі версії існуючих файлів, скануючи текст за допомогою OCR або виправляючи сторінки, які були скановані під кутом. Ми хочемо рахувати ці файли як одне видання, що вимагатиме хорошого metadata або дедуплікації за допомогою вимірів схожості документів."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Видання” здаються найбільш практичним визначенням того, що таке “книги”. Зручно, що це визначення також використовується для присвоєння унікальних номерів ISBN. ISBN, або Міжнародний стандартний книжковий номер, зазвичай використовується для міжнародної торгівлі, оскільки він інтегрований з міжнародною системою штрих-кодів (“Міжнародний номер статті”). Якщо ви хочете продавати книгу в магазинах, їй потрібен штрих-код, тому ви отримуєте ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "У блозі Тайчера згадується, що хоча ISBN корисні, вони не є універсальними, оскільки їх дійсно прийняли лише в середині сімдесятих, і не скрізь у світі. Проте, ISBN, ймовірно, є найбільш широко використовуваним ідентифікатором видань книг, тому це наш найкращий відправний пункт. Якщо ми зможемо знайти всі ISBN у світі, ми отримаємо корисний список книг, які ще потрібно зберегти."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Отже, де ми можемо отримати дані? Існує кілька існуючих зусиль, які намагаються скласти список всіх книг у світі:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Зрештою, вони провели це дослідження для Google Books. Однак їх metadata недоступні в масовому обсязі і досить важко скрапити."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Як вже згадувалося, це їхня основна місія. Вони зібрали величезну кількість бібліотечних даних від співпрацюючих бібліотек і національних архівів і продовжують це робити. Вони також мають волонтерів-бібліотекарів і технічну команду, яка намагається видалити дублікати записів і позначити їх всілякими metadata. Найкраще, що їхній набір даних повністю відкритий. Ви можете просто <a %(openlibrary)s>завантажити його</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Це вебсайт, керований некомерційною організацією OCLC, яка продає системи управління бібліотеками. Вони агрегують metadata книг з багатьох бібліотек і роблять їх доступними через вебсайт WorldCat. Однак вони також заробляють гроші, продаючи ці дані, тому вони недоступні для масового завантаження. Вони мають деякі більш обмежені масові набори даних, доступні для завантаження, у співпраці з конкретними бібліотеками."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Це тема цього блогу. ISBNdb скрапить різні вебсайти для metadata книг, зокрема дані про ціни, які вони потім продають книготорговцям, щоб вони могли встановлювати ціни на свої книги відповідно до решти ринку. Оскільки ISBN зараз досить універсальні, вони фактично створили “веб-сторінку для кожної книги”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Різні індивідуальні бібліотечні системи та архіви.</strong> Є бібліотеки та архіви, які не були індексовані та агреговані жодним з вищезгаданих, часто тому, що вони недофінансовані або з інших причин не хочуть ділитися своїми даними з Open Library, OCLC, Google тощо. Багато з них мають цифрові записи, доступні через інтернет, і вони часто не дуже добре захищені, тому якщо ви хочете допомогти і отримати задоволення від вивчення дивних бібліотечних систем, це чудові відправні точки."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "У цьому пості ми раді оголосити про невеликий реліз (порівняно з нашими попередніми релізами Z-Library). Ми скрапили більшість ISBNdb і зробили дані доступними для торрентування на вебсайті Pirate Library Mirror (РЕДАКЦІЯ: переміщено до <a %(wikipedia_annas_archive)s>Архіву Анни</a>; ми не будемо посилатися на нього тут безпосередньо, просто знайдіть його). Це близько 30,9 мільйонів записів (20 ГБ у форматі <a %(jsonlines)s>JSON Lines</a>; 4,4 ГБ у стиснутому вигляді). На їхньому вебсайті вони стверджують, що насправді мають 32,6 мільйонів записів, тому ми, можливо, якось пропустили деякі, або <em>вони</em> могли щось зробити неправильно. У будь-якому випадку, наразі ми не будемо ділитися тим, як ми це зробили — залишимо це як вправу для читача. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Те, чим ми поділимося, це деякий попередній аналіз, щоб спробувати наблизитися до оцінки кількості книг у світі. Ми розглянули три набори даних: цей новий набір даних ISBNdb, наш оригінальний реліз metadata, який ми скрапили з тіньової бібліотеки Z-Library (яка включає Library Genesis), і дамп даних Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Почнемо з деяких приблизних чисел:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "У Z-Library/Libgen та Open Library є набагато більше книг, ніж унікальних ISBN. Чи означає це, що багато з цих книг не мають ISBN, чи просто відсутня metadata ISBN? Ми, ймовірно, можемо відповісти на це питання за допомогою комбінації автоматичного зіставлення на основі інших атрибутів (назва, автор, видавець тощо), залучення більшої кількості джерел даних і вилучення ISBN з самих сканів книг (у випадку Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Скільки з цих ISBN є унікальними? Це найкраще ілюструється за допомогою діаграми Венна:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Щоб бути точнішим:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Ми були здивовані, наскільки мало є збігів! ISBNdb має величезну кількість ISBN, які не з'являються ні в Z-Library, ні в Open Library, і те ж саме стосується (в меншій, але все ж значній мірі) інших двох. Це піднімає багато нових питань. Наскільки автоматизоване зіставлення допомогло б у маркуванні книг, які не були позначені ISBN? Чи було б багато збігів і, отже, збільшення перекриття? Також, що станеться, якщо ми додамо 4-й або 5-й набір даних? Скільки перекриття ми побачимо тоді?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Це дає нам відправну точку. Тепер ми можемо подивитися на всі ISBN, які не були в наборі даних Z-Library, і які не збігаються з полями назви/автора. Це може дати нам можливість зберегти всі книги у світі: спочатку шляхом сканування інтернету для пошуку сканів, потім виходячи в реальне життя для сканування книг. Останнє навіть може бути профінансовано спільнотою або керуватися \"винагородами\" від людей, які хотіли б бачити певні книги оцифрованими. Все це історія для іншого разу."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Якщо ви хочете допомогти з будь-яким з цього — подальший аналіз; збирання більше metadata; пошук більше книг; OCR книг; виконання цього для інших доменів (наприклад, статті, аудіокниги, фільми, телешоу, журнали) або навіть надання деяких з цих даних для таких речей, як ML / навчання великих мовних моделей — будь ласка, зв'яжіться зі мною (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Якщо вас особливо цікавить аналіз даних, ми працюємо над тим, щоб зробити наші набори даних і скрипти доступними у більш зручному форматі. Було б чудово, якби ви могли просто форкнути блокнот і почати грати з цим."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Нарешті, якщо ви хочете підтримати цю роботу, будь ласка, розгляньте можливість зробити пожертву. Це повністю волонтерська операція, і ваш внесок має величезне значення. Кожна частинка допомагає. Поки що ми приймаємо пожертви в криптовалюті; дивіться сторінку Пожертвувань на Архіві Анни."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Для деякого розумного визначення \"назавжди\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Звичайно, письмова спадщина людства набагато більше, ніж книги, особливо в наші дні. Для цілей цього посту і наших останніх релізів ми зосереджуємося на книгах, але наші інтереси простягаються далі."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Є багато чого, що можна сказати про Аарона Шварца, але ми просто хотіли згадати його коротко, оскільки він відіграє ключову роль у цій історії. З часом більше людей можуть вперше натрапити на його ім'я і згодом самостійно зануритися в цю тему."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Критичне вікно тіньових бібліотек"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Як ми можемо стверджувати, що зберігаємо наші колекції назавжди, коли вони вже наближаються до 1 ПБ?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Китайська версія 中文版</a>, обговорення на <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "В Архіві Анни нас часто запитують, як ми можемо стверджувати, що зберігаємо наші колекції назавжди, коли загальний обсяг вже наближається до 1 Петабайта (1000 ТБ) і продовжує зростати. У цій статті ми розглянемо нашу філософію і побачимо, чому наступне десятиліття є критичним для нашої місії збереження знань і культури людства."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Загальний обсяг</a> наших колекцій за останні кілька місяців, розбитий за кількістю торрент-сідерів."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Пріоритети"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Чому ми так піклуємося про статті та книги? Відкладемо вбік наше фундаментальне переконання в збереженні в цілому — ми можемо написати про це інший пост. Отже, чому саме статті та книги? Відповідь проста: <strong>щільність інформації</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "На мегабайт зберігання, письмовий текст зберігає найбільше інформації з усіх медіа. Хоча ми піклуємося про знання та культуру, ми більше піклуємося про перше. Загалом, ми знаходимо ієрархію щільності інформації та важливості збереження, яка виглядає приблизно так:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Академічні статті, журнали, звіти"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Органічні дані, такі як послідовності ДНК, насіння рослин або мікробні зразки"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Нон-фікшн книги"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Програмний код для науки та інженерії"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Дані вимірювань, такі як наукові вимірювання, економічні дані, корпоративні звіти"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Вебсайти з науки та інженерії, онлайн-дискусії"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Нехудожні журнали, газети, інструкції"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Нехудожні стенограми виступів, документальні фільми, подкасти"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Внутрішні дані від корпорацій або урядів (витоки)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Записи metadata загалом (як нехудожні, так і художні; інших медіа, мистецтва, людей тощо; включаючи рецензії)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Географічні дані (наприклад, карти, геологічні дослідження)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Стенограми юридичних або судових процесів"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Художні або розважальні версії всього вищезазначеного"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Рейтинг у цьому списку є дещо довільним — кілька пунктів є рівнозначними або викликають розбіжності в нашій команді — і ми, ймовірно, забуваємо про деякі важливі категорії. Але приблизно так ми визначаємо пріоритети."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Деякі з цих пунктів занадто відрізняються від інших, щоб ми турбувалися про них (або вже опікуються іншими установами), такі як органічні дані або географічні дані. Але більшість пунктів у цьому списку насправді важливі для нас."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Ще одним важливим фактором у нашій пріоритизації є те, наскільки ризикованим є певний твір. Ми віддаємо перевагу зосередженню на творах, які є:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Рідкісними"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Унікально недооціненими"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Унікально під загрозою знищення (наприклад, через війну, скорочення фінансування, судові позови або політичні переслідування)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Нарешті, ми дбаємо про масштаб. У нас обмежений час і гроші, тому ми краще витратимо місяць на збереження 10 000 книг, ніж 1 000 книг — якщо вони приблизно однаково цінні та під загрозою."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Тіньові бібліотеки"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Існує багато організацій, які мають схожі місії та пріоритети. Дійсно, є бібліотеки, архіви, лабораторії, музеї та інші установи, які займаються збереженням такого роду. Багато з них добре фінансуються урядами, приватними особами або корпораціями. Але у них є одна величезна сліпа зона: правова система."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "У цьому полягає унікальна роль тіньових бібліотек і причина існування Архіву Анни. Ми можемо робити те, що іншим установам не дозволено. Ні, це не (часто) означає, що ми можемо архівувати матеріали, які незаконно зберігати в інших місцях. Ні, у багатьох місцях законно створювати архів з будь-якими книгами, статтями, журналами тощо."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Але чого часто бракує юридичним архівам, так це <strong>резервування та довговічності</strong>. Існують книги, з яких є лише один екземпляр у якійсь фізичній бібліотеці. Існують записи metadata, які охороняє лише одна корпорація. Існують газети, збережені лише на мікрофільмах в одному архіві. Бібліотеки можуть зазнавати скорочення фінансування, корпорації можуть збанкрутувати, архіви можуть бути знищені бомбами та спалені дощенту. Це не гіпотеза — це відбувається постійно."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Те, що ми можемо унікально робити в Архіві Анни, — це зберігати багато копій творів у великому масштабі. Ми можемо збирати статті, книги, журнали та інше, і розповсюджувати їх оптом. Наразі ми робимо це через торренти, але точні технології не мають значення і змінюватимуться з часом. Важливо, щоб багато копій було розповсюджено по всьому світу. Ця цитата з понад 200 років тому досі залишається актуальною:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Втрачено не можна відновити; але давайте збережемо те, що залишилося: не за допомогою сховищ і замків, які захищають їх від очей і використання громадськості, віддаючи їх на знищення часу, а шляхом такого множення копій, яке поставить їх поза досяжністю випадковостей.</q></em><br>— Томас Джефферсон, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Коротка примітка про суспільне надбання. Оскільки Архів Анни унікально зосереджується на діяльності, яка є незаконною в багатьох місцях світу, ми не переймаємося широко доступними колекціями, такими як книги суспільного надбання. Юридичні організації часто вже добре дбають про це. Однак є міркування, які іноді змушують нас працювати над загальнодоступними колекціями:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Записи metadata можна вільно переглядати на вебсайті Worldcat, але не можна завантажити оптом (поки ми їх не <a %(worldcat_scrape)s>вилучили</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Код може бути з відкритим вихідним кодом на Github, але Github в цілому не може бути легко дзеркалізований і, таким чином, збережений (хоча в цьому конкретному випадку існує достатньо розповсюджених копій більшості репозиторіїв коду)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit безкоштовний у використанні, але нещодавно запровадив суворі заходи проти вилучення даних, у зв'язку з тренуванням LLM, що потребує багато даних (докладніше про це пізніше)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Множення копій"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Повертаючись до нашого початкового питання: як ми можемо стверджувати, що зберігаємо наші колекції назавжди? Основна проблема тут полягає в тому, що наша колекція <a %(torrents_stats)s>зростає</a> швидкими темпами, завдяки вилученню та відкриттю деяких величезних колекцій (на додаток до дивовижної роботи, вже виконаної іншими бібліотеками тіньових даних, такими як Sci-Hub і Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Це зростання даних ускладнює дзеркалізацію колекцій по всьому світу. Зберігання даних дороге! Але ми оптимістичні, особливо спостерігаючи за наступними трьома тенденціями."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Ми зібрали легкодоступні плоди"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Це безпосередньо випливає з наших пріоритетів, обговорених вище. Ми віддаємо перевагу роботі над звільненням великих колекцій спочатку. Тепер, коли ми забезпечили деякі з найбільших колекцій у світі, ми очікуємо, що наше зростання буде набагато повільнішим."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Існує ще довгий хвіст менших колекцій, і нові книги скануються або публікуються щодня, але швидкість, ймовірно, буде набагато повільнішою. Ми можемо ще подвоїтися або навіть потроїтися в розмірах, але протягом тривалішого періоду часу."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Витрати на зберігання продовжують експоненційно знижуватися"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "На момент написання <a %(diskprices)s>ціни на диски</a> за терабайт становлять близько 12 доларів за нові диски, 8 доларів за вживані диски та 4 долари за стрічку. Якщо ми будемо консервативними і розглянемо лише нові диски, це означає, що зберігання петабайта коштує близько 12 000 доларів. Якщо ми припустимо, що наша бібліотека потроїться з 900 ТБ до 2,7 ПБ, це означатиме 32 400 доларів для дзеркалізації всієї нашої бібліотеки. Додаючи електроенергію, вартість іншого обладнання тощо, округлимо до 40 000 доларів. Або зі стрічкою більше як 15 000–20 000 доларів."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "З одного боку, <strong>15 000–40 000 доларів за суму всіх людських знань — це вигідна угода</strong>. З іншого боку, це трохи круто очікувати тонни повних копій, особливо якщо ми також хочемо, щоб ці люди продовжували роздавати свої торренти на користь інших."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Це сьогодні. Але прогрес рухається вперед:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Вартість жорстких дисків за терабайт приблизно зменшилася втричі за останні 10 років і, ймовірно, продовжить знижуватися в такому ж темпі. Стрічка, здається, йде по схожій траєкторії. Ціни на SSD падають ще швидше і можуть перевершити ціни на HDD до кінця десятиліття."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Тенденції цін на HDD з різних джерел (натисніть, щоб переглянути дослідження)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Якщо це збережеться, то через 10 років ми можемо розглядати лише 5 000–13 000 доларів для дзеркалізації всієї нашої колекції (1/3), або навіть менше, якщо ми зростемо менше в розмірах. Хоча це все ще багато грошей, це буде досяжно для багатьох людей. І це може бути ще краще завдяки наступному пункту…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Покращення щільності інформації"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Наразі ми зберігаємо книги у тих форматах, в яких вони нам надаються. Звісно, вони стиснуті, але часто це все ще великі скани або фотографії сторінок."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "До цього часу єдиними варіантами зменшення загального розміру нашої колекції були більш агресивне стиснення або дедуплікація. Однак, щоб досягти значних заощаджень, обидва методи занадто втрачають якість для нас. Сильне стиснення фотографій може зробити текст ледве читабельним. А дедуплікація вимагає високої впевненості в тому, що книги є точно однаковими, що часто є занадто неточним, особливо якщо вміст однаковий, але скани зроблені в різний час."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Завжди був третій варіант, але його якість була настільки жахливою, що ми ніколи не розглядали його: <strong>OCR, або оптичне розпізнавання символів</strong>. Це процес перетворення фотографій у звичайний текст за допомогою штучного інтелекту для розпізнавання символів на фотографіях. Інструменти для цього існують вже давно і є досить пристойними, але «досить пристойними» недостатньо для цілей збереження."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Однак, останні мультимодальні моделі глибокого навчання зробили надзвичайно швидкий прогрес, хоча все ще за високих витрат. Ми очікуємо, що точність і витрати значно покращаться в найближчі роки, до того моменту, коли це стане реалістичним для застосування до всієї нашої бібліотеки."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Покращення OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Коли це станеться, ми, ймовірно, все ще зберігатимемо оригінальні файли, але додатково ми могли б мати набагато меншу версію нашої бібліотеки, яку більшість людей захоче дзеркалити. Родзинка в тому, що сам сирий текст стискається ще краще і його набагато легше дедуплікувати, що дає нам ще більше заощаджень."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Загалом, не є нереалістичним очікувати принаймні 5-10-кратного зменшення загального розміру файлів, можливо, навіть більше. Навіть при консервативному 5-кратному зменшенні, ми б розглядали <strong>$1,000–$3,000 за 10 років, навіть якщо наша бібліотека збільшиться втричі</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Критичне вікно"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Якщо ці прогнози точні, нам <strong>потрібно лише почекати кілька років</strong>, перш ніж вся наша колекція буде широко дзеркалена. Таким чином, за словами Томаса Джефферсона, «поміщена поза досяжністю випадковості»."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "На жаль, поява LLM та їхнє навчання, яке потребує багато даних, змусила багатьох правовласників зайняти оборонну позицію. Ще більше, ніж вони вже були. Багато вебсайтів ускладнюють скрапінг та архівування, судові позови літають навколо, і в той же час фізичні бібліотеки та архіви продовжують бути занедбаними."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Ми можемо лише очікувати, що ці тенденції продовжать погіршуватися, і багато робіт буде втрачено задовго до того, як вони увійдуть у суспільне надбання."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Ми на порозі революції у збереженні, але <q>втрачене не можна відновити.</q></strong> У нас є критичне вікно приблизно 5-10 років, протягом якого все ще досить дорого утримувати тіньову бібліотеку та створювати багато дзеркал по всьому світу, і протягом якого доступ ще не був повністю закритий."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Якщо ми зможемо подолати це вікно, то дійсно збережемо знання та культуру людства назавжди. Ми не повинні дозволити цьому часу бути змарнованим. Ми не повинні дозволити цьому критичному вікну закритися для нас."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Вперед."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ексклюзивний доступ для компаній LLM до найбільшої у світі колекції китайських нон-фікшн книг"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Китайська версія 中文版</a>, <a %(news_ycombinator)s>Обговорити на Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Коротко:</strong> Архів Анни придбав унікальну колекцію з 7,5 мільйонів / 350 ТБ китайських нон-фікшн книг — більшу, ніж Library Genesis. Ми готові надати компанії LLM ексклюзивний доступ в обмін на високоякісний OCR та вилучення тексту.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Це короткий блог-пост. Ми шукаємо компанію або установу, яка допоможе нам з OCR та вилученням тексту для величезної колекції, яку ми придбали, в обмін на ексклюзивний ранній доступ. Після періоду ембарго ми, звісно, випустимо всю колекцію."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Високоякісні академічні тексти надзвичайно корисні для навчання LLM. Хоча наша колекція китайська, це може бути корисним навіть для навчання англійських LLM: моделі, здається, кодують концепції та знання незалежно від мови джерела."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Для цього текст потрібно витягти зі сканів. Що отримує з цього Архів Анни? Повнотекстовий пошук книг для своїх користувачів."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Оскільки наші цілі збігаються з цілями розробників LLM, ми шукаємо співпрацівника. Ми готові надати вам <strong>ексклюзивний ранній доступ до цієї колекції в обсязі на 1 рік</strong>, якщо ви зможете виконати належне OCR та витяг тексту. Якщо ви готові поділитися з нами всім кодом вашого конвеєра, ми готові продовжити ембарго на колекцію."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Приклад сторінок"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Щоб довести нам, що у вас є хороший конвеєр, ось кілька прикладів сторінок для початку, з книги про надпровідники. Ваш конвеєр повинен правильно обробляти математику, таблиці, графіки, примітки тощо."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Надішліть оброблені сторінки на нашу електронну пошту. Якщо вони виглядають добре, ми надішлемо вам більше в приватному порядку, і ми очікуємо, що ви зможете швидко запустити свій конвеєр на них також. Коли ми будемо задоволені, ми зможемо укласти угоду."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Колекція"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Деяка додаткова інформація про колекцію. <a %(duxiu)s>Duxiu</a> — це величезна база даних відсканованих книг, створена <a %(chaoxing)s>SuperStar Digital Library Group</a>. Більшість з них — академічні книги, відскановані для того, щоб зробити їх доступними в цифровому вигляді для університетів та бібліотек. Для нашої англомовної аудиторії <a %(library_princeton)s>Прінстон</a> та <a %(guides_lib_uw)s>Університет Вашингтона</a> мають хороші огляди. Також є чудова стаття, що надає більше інформації: <a %(doi)s>“Оцифровка китайських книг: дослідження пошукової системи SuperStar DuXiu Scholar”</a> (знайдіть її в Архіві Анни)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Книги з Duxiu давно піратські на китайському інтернеті. Зазвичай їх продають за менше ніж долар перепродавці. Вони зазвичай розповсюджуються за допомогою китайського аналога Google Drive, який часто зламують, щоб дозволити більше місця для зберігання. Деякі технічні деталі можна знайти <a %(github_duty_machine)s>тут</a> і <a %(github_821_github_io)s>тут</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Хоча книги були напівпублічно розповсюджені, їх досить важко отримати в обсязі. Ми мали це високо в нашому списку справ, і виділили кілька місяців повноцінної роботи для цього. Однак нещодавно до нас звернувся неймовірний, дивовижний і талановитий волонтер, повідомивши, що вони вже виконали всю цю роботу — за великі витрати. Вони поділилися з нами повною колекцією, не очікуючи нічого взамін, окрім гарантії довгострокового збереження. Справді вражаюче. Вони погодилися попросити допомоги таким чином, щоб отримати колекцію OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Колекція містить 7,543,702 файлів. Це більше, ніж Library Genesis нон-фікшн (близько 5,3 мільйона). Загальний розмір файлів становить близько 359 ТБ (326 ТіБ) у його поточній формі."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Ми відкриті для інших пропозицій та ідей. Просто зв'яжіться з нами. Перегляньте Архів Анни для отримання додаткової інформації про наші колекції, зусилля щодо збереження та як ви можете допомогти. Дякуємо!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Попередження: цей блог-пост застарів. Ми вирішили, що IPFS ще не готовий для широкого використання. Ми все ще будемо посилатися на файли на IPFS з Архіву Анни, коли це можливо, але ми більше не будемо їх хостити самі, і не рекомендуємо іншим дзеркалити за допомогою IPFS. Будь ласка, перегляньте нашу сторінку Торентів, якщо ви хочете допомогти зберегти нашу колекцію."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Допоможіть розповсюдити Z-Library на IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Як керувати тіньовою бібліотекою: операції в Архіві Анни"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Немає <q>AWS для тіньових благодійностей,</q> тож як ми керуємо Архівом Анни?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Я керую <a %(wikipedia_annas_archive)s>Архівом Анни</a>, найбільшою у світі відкритою некомерційною пошуковою системою для <a %(wikipedia_shadow_library)s>тіньових бібліотек</a>, таких як Sci-Hub, Library Genesis та Z-Library. Наша мета — зробити знання та культуру легко доступними, і в кінцевому підсумку створити спільноту людей, які разом архівують та зберігають <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>всі книги у світі</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "У цій статті я покажу, як ми керуємо цим вебсайтом, і унікальні виклики, які виникають при управлінні вебсайтом з сумнівним правовим статусом, оскільки немає “AWS для тіньових благодійностей”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Також перегляньте сестринську статтю <a %(blog_how_to_become_a_pirate_archivist)s>Як стати піратським архівістом</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Інноваційні токени"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Почнемо з нашого технічного стеку. Він навмисно нудний. Ми використовуємо Flask, MariaDB та ElasticSearch. І це буквально все. Пошук в основному є вирішеною проблемою, і ми не маємо наміру його винаходити заново. Крім того, ми повинні витратити наші <a %(mcfunley)s>інноваційні токени</a> на щось інше: не бути закритими владою."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Отже, наскільки легальний чи нелегальний є Архів Анни? Це здебільшого залежить від правової юрисдикції. Більшість країн вірять у певну форму авторського права, що означає, що людям або компаніям надається виключна монополія на певні види робіт на певний період часу. До речі, в Архіві Анни ми вважаємо, що, хоча є деякі переваги, загалом авторське право є негативним для суспільства — але це історія для іншого разу."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ця виключна монополія на певні роботи означає, що незаконно для будь-кого поза цією монополією безпосередньо розповсюджувати ці роботи — включаючи нас. Але Архів Анни є пошуковою системою, яка не розповсюджує ці роботи безпосередньо (принаймні не на нашому сайті в звичайному інтернеті), тож ми повинні бути в порядку, так? Не зовсім. У багатьох юрисдикціях незаконно не лише розповсюджувати захищені авторським правом роботи, але й посилатися на місця, де це роблять. Класичним прикладом цього є закон DMCA у Сполучених Штатах."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Це найсуворіший кінець спектру. На іншому кінці спектру теоретично можуть бути країни без жодних законів про авторське право, але такі насправді не існують. Практично кожна країна має певну форму закону про авторське право. Виконання — це інша історія. Є багато країн з урядами, які не дбають про виконання закону про авторське право. Є також країни між двома крайнощами, які забороняють розповсюдження захищених авторським правом робіт, але не забороняють посилання на такі роботи."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Ще один аспект — на рівні компанії. Якщо компанія працює в юрисдикції, яка не дбає про авторське право, але сама компанія не готова ризикувати, то вони можуть закрити ваш сайт, як тільки хтось поскаржиться на нього."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Нарешті, велике питання — це платежі. Оскільки ми повинні залишатися анонімними, ми не можемо використовувати традиційні методи оплати. Це залишає нам криптовалюти, і лише невелика частина компаній підтримує їх (існують віртуальні дебетові картки, оплачені криптовалютою, але їх часто не приймають)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Архітектура системи"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Отже, припустимо, ви знайшли кілька компаній, які готові розмістити ваш сайт, не закриваючи його — назвемо їх “провайдерами, що люблять свободу” 😄. Ви швидко виявите, що розміщення всього у них досить дороге, тому ви можете захотіти знайти “дешевих провайдерів” і фактично розміщувати там, проксіруючи через провайдерів, що люблять свободу. Якщо ви зробите це правильно, дешеві провайдери ніколи не дізнаються, що ви розміщуєте, і ніколи не отримають жодних скарг."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "З усіма цими провайдерами існує ризик, що вони все одно закриють вас, тому вам також потрібна надмірність. Нам це потрібно на всіх рівнях нашого стеку."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Одна з компаній, що дещо любить свободу, яка поставила себе в цікаве становище, — це Cloudflare. Вони <a %(blog_cloudflare)s>стверджували</a>, що вони не є хостинг-провайдером, а утилітою, як ISP. Тому вони не підлягають DMCA або іншим запитам на видалення, і пересилають будь-які запити вашому фактичному хостинг-провайдеру. Вони навіть пішли до суду, щоб захистити цю структуру. Тому ми можемо використовувати їх як ще один шар кешування та захисту."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare не приймає анонімні платежі, тому ми можемо використовувати лише їх безкоштовний план. Це означає, що ми не можемо використовувати їх функції балансування навантаження або резервування. Тому ми <a %(annas_archive_l255)s>реалізували це самі</a> на рівні домену. При завантаженні сторінки браузер перевіряє, чи доступний поточний домен, і якщо ні, переписує всі URL-адреси на інший домен. Оскільки Cloudflare кешує багато сторінок, це означає, що користувач може потрапити на наш основний домен, навіть якщо проксі-сервер не працює, а потім при наступному кліку перейти на інший домен."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ми також маємо справлятися з нормальними операційними питаннями, такими як моніторинг стану серверів, ведення журналів помилок бекенду та фронтенду тощо. Наша архітектура резервування дозволяє більшої надійності в цьому відношенні, наприклад, шляхом запуску повністю іншого набору серверів на одному з доменів. Ми навіть можемо запускати старі версії коду та наборів даних на цьому окремому домені, у випадку, якщо критична помилка в основній версії залишиться непоміченою."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Ми також можемо захиститися від того, що Cloudflare може обернутися проти нас, видаливши його з одного з доменів, наприклад, з цього окремого домену. Можливі різні комбінації цих ідей."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Інструменти"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Давайте подивимося, які інструменти ми використовуємо для досягнення всього цього. Це дуже змінюється, оскільки ми стикаємося з новими проблемами та знаходимо нові рішення."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Сервер додатків: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Проксі-сервер: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Управління серверами: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Розробка: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Статичний хостинг Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Є деякі рішення, які ми переглядали кілька разів. Одне з них — це комунікація між серверами: раніше ми використовували для цього Wireguard, але виявили, що він іноді перестає передавати будь-які дані або передає дані лише в одному напрямку. Це траплялося з кількома різними налаштуваннями Wireguard, які ми пробували, такими як <a %(github_costela_wesher)s>wesher</a> і <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ми також пробували тунелювати порти через SSH, використовуючи autossh і sshuttle, але зіткнулися з <a %(github_sshuttle)s>проблемами там</a> (хоча мені досі не зрозуміло, чи страждає autossh від проблем TCP-over-TCP чи ні — це просто здається мені ненадійним рішенням, але, можливо, воно насправді добре?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Замість цього ми повернулися до прямих з'єднань між серверами, приховуючи, що сервер працює на дешевих провайдерах, використовуючи фільтрацію IP з UFW. Це має недолік, що Docker не працює добре з UFW, якщо ви не використовуєте <code>network_mode: \"host\"</code>. Все це трохи більш схильне до помилок, тому що ви можете виставити свій сервер в інтернет через невелику помилку в конфігурації. Можливо, нам слід повернутися до autossh — зворотний зв'язок тут буде дуже доречним."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Ми також переглядали Varnish проти Nginx. Наразі нам подобається Varnish, але він має свої особливості та недоліки. Те ж саме стосується Checkmk: ми не в захваті від нього, але він працює на даний момент. Weblate був непоганим, але не вражаючим — я іноді боюся, що він втратить мої дані, коли я намагаюся синхронізувати його з нашим git-репозиторієм. Flask загалом був хорошим, але має деякі дивні особливості, які коштували багато часу на налагодження, такі як налаштування користувацьких доменів або проблеми з інтеграцією SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Поки що інші інструменти були чудовими: у нас немає серйозних скарг на MariaDB, ElasticSearch, Gitlab, Zulip, Docker і Tor. Усі вони мали деякі проблеми, але нічого надто серйозного або такого, що забирає багато часу."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Висновок"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Це був цікавий досвід навчитися налаштовувати надійний і стійкий пошуковий механізм тіньової бібліотеки. Є ще багато деталей, якими можна поділитися в наступних публікаціях, тому дайте знати, про що ви хотіли б дізнатися більше!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Як завжди, ми шукаємо пожертвування для підтримки цієї роботи, тому обов’язково відвідайте сторінку Пожертвувань на Архіві Анни. Ми також шукаємо інші види підтримки, такі як гранти, довгострокові спонсори, провайдери високоризикових платежів, можливо, навіть (зі смаком!) рекламу. І якщо ви хочете внести свій час і навички, ми завжди шукаємо розробників, перекладачів тощо. Дякуємо за ваш інтерес і підтримку."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Анна та команда (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Привіт, я Анна. Я створила <a %(wikipedia_annas_archive)s>Архів Анни</a>, найбільшу у світі тіньову бібліотеку. Це мій особистий блог, у якому я та мої колеги пишемо про піратство, цифрове збереження та інше."

#, fuzzy
msgid "blog.index.text2"
msgstr "Зв'яжіться зі мною на <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Зверніть увагу, що цей вебсайт — лише блог. Ми розміщуємо тут лише наші власні слова. Жодні торренти чи інші файли, захищені авторським правом, тут не розміщуються і не посилаються."

#, fuzzy
msgid "blog.index.heading"
msgstr "Публікації в блозі"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 мільярда сканувань WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Розміщення 5,998,794 книг на IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Попередження: цей блог-пост застарів. Ми вирішили, що IPFS ще не готовий для широкого використання. Ми все ще будемо посилатися на файли на IPFS з Архіву Анни, коли це можливо, але ми більше не будемо їх хостити самі, і не рекомендуємо іншим дзеркалити за допомогою IPFS. Будь ласка, перегляньте нашу сторінку Торентів, якщо ви хочете допомогти зберегти нашу колекцію."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Коротко:</strong> Архів Анни сканував весь WorldCat (найбільшу у світі колекцію бібліотечних metadata), щоб створити список книг, які потрібно зберегти.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Рік тому ми <a %(blog)s>почали</a> відповідати на це питання: <strong>Який відсоток книг був назавжди збережений тіньовими бібліотеками?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Як тільки книга потрапляє в тіньову бібліотеку з відкритими даними, таку як <a %(wikipedia_library_genesis)s>Library Genesis</a>, а тепер і <a %(wikipedia_annas_archive)s>Архів Анни</a>, вона віддзеркалюється по всьому світу (через торренти), тим самим практично зберігаючи її назавжди."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Щоб відповісти на питання, який відсоток книг був збережений, нам потрібно знати знаменник: скільки книг існує загалом? Ідеально, якщо ми маємо не лише число, але й фактичні metadata. Тоді ми можемо не лише зіставити їх з тіньовими бібліотеками, але й <strong>створити список книг, які ще потрібно зберегти!</strong> Ми навіть могли б почати мріяти про краудсорсингову ініціативу, щоб пройтися по цьому списку."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Ми зібрали дані з <a %(wikipedia_isbndb_com)s>ISBNdb</a> та завантажили <a %(openlibrary)s>набір даних Open Library</a>, але результати були незадовільними. Основною проблемою було те, що не було великого перетину ISBN. Подивіться на цю діаграму Венна з <a %(blog)s>нашого блогу</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ми були дуже здивовані, наскільки мало було перетину між ISBNdb та Open Library, обидва з яких щедро включають дані з різних джерел, таких як веб-скрапінг та бібліотечні записи. Якщо б вони обидва добре виконували свою роботу з пошуку більшості ISBN, їхні кола, безумовно, мали б значний перетин, або одне було б підмножиною іншого. Це змусило нас задуматися, скільки книг випадає <em>повністю за межі цих кіл</em>? Нам потрібна більша база даних."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Саме тоді ми звернули увагу на найбільшу базу даних книг у світі: <a %(wikipedia_worldcat)s>WorldCat</a>. Це власна база даних некомерційної організації <a %(wikipedia_oclc)s>OCLC</a>, яка агрегує metadata записи з бібліотек по всьому світу, в обмін на надання цим бібліотекам доступу до повного набору даних та відображення їх у результатах пошуку кінцевих користувачів."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Хоча OCLC є некомерційною організацією, їхня бізнес-модель вимагає захисту їхньої бази даних. Ну, ми шкодуємо, друзі з OCLC, але ми віддаємо все. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Протягом минулого року ми ретельно зібрали всі записи WorldCat. Спочатку нам пощастило. WorldCat щойно розгорнув повний редизайн свого вебсайту (у серпні 2022 року). Це включало значну перебудову їхніх бекенд-систем, що призвело до багатьох вразливостей у безпеці. Ми негайно скористалися можливістю і змогли зібрати сотні мільйонів (!) записів за лічені дні."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Редизайн WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Після цього вразливості безпеки поступово виправлялися одна за одною, поки остання, яку ми знайшли, не була виправлена близько місяця тому. До того часу у нас були майже всі записи, і ми лише прагнули отримати трохи якісніші записи. Тож ми відчули, що настав час випустити!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Давайте подивимося на деяку основну інформацію про дані:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Формат?</strong> <a %(blog)s>Контейнери Архіву Анни (AAC)</a>, які по суті є <a %(jsonlines)s>JSON Lines</a>, стиснутими за допомогою <a %(zstd)s>Zstandard</a>, плюс деякі стандартизовані семантики. Ці контейнери обгортають різні типи записів, на основі різних скрапінгів, які ми розгорнули."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Дані"

msgid "dyn.buy_membership.error.unknown"
msgstr "Сталась невідома помилка. Будь ласка, напишіть на %(email)s і додайте знімок екрану."

msgid "dyn.buy_membership.error.minimum"
msgstr "Ця монета вища за типовий мінімум. Будь ласка, оберіть іншу тривалість або монету."

msgid "dyn.buy_membership.error.try_again"
msgstr "Не вдалося виконати запит. Будь ласка, спробуйте ще раз через кілька хвилин, і якщо це не вдасться зробити, зв'яжіться з нами за адресою %(email)s та надішліть скріншот екрана."

msgid "dyn.buy_membership.error.wait"
msgstr "Помилка в обробці платежу. Будь ласка, зачекайте і спробуйте ще раз. Якщо проблема не зникає більше 24 годин, будь ласка, зв'яжіться з нами за адресою %(email)s та надішліть скріншот."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "прихований коментар"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Проблема з файлом: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Краща версія"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ви хочете повідомити про цього користувача за образливу або недоречну поведінку?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Повідомити про зловживання"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Зловживання повідомлено:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Ви повідомили про цього користувача за зловживання."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Відповісти"

msgid "page.md5.quality.logged_out_login"
msgstr "Будь ласка, <a %(a_login)s>увійдіть</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Ви залишили коментар. Може знадобитися деякий час, щоб він з'явився."

msgid "page.md5.quality.comment_error"
msgstr "Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s зачеплені сторінки"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Не відображається у .rs-версії академічного розділу Library Genesis"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Не відображається у .rs-версії художнього розділу Library Genesis"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Не відображається у .li-версії Library Genesis"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Помічено як \"зламаний файл\" у .li-версії академічного розділу Library Genesis"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Файл відсутній у Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Позначено як «спам» у Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Позначено як «поганий файл» у Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Не всі сторінки вдалося перетворити на PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Запуск exiftool не вдався для цього файлу"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Книга (деталі невідомі)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Книга (академічна література)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Книга (художня література)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Стаття з журналу"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Документ із описом стандартів"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Журнал"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Комікс"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Музичний запис"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Аудіокнига"

msgid "common.md5_content_type_mapping.other"
msgstr "Інше"

msgid "common.access_types_mapping.aa_download"
msgstr "Завантажування з серверу партнера"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Зовнішнє завантаження"

msgid "common.access_types_mapping.external_borrow"
msgstr "Зовнішнє запозичення"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Зовнішнє запозичення (не для друку)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Дослідіть метадані"

msgid "common.access_types_mapping.torrents_available"
msgstr "Міститься в торентах"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library китайською"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Завантаження до AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "Індекс електронних книг EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Чеські метадані"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Книги"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Російська державна бібліотека"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Назва"

msgid "common.specific_search_fields.author"
msgstr "Автор"

msgid "common.specific_search_fields.publisher"
msgstr "Видавець"

msgid "common.specific_search_fields.edition_varia"
msgstr "Версія"

msgid "common.specific_search_fields.year"
msgstr "Рік видання"

msgid "common.specific_search_fields.original_filename"
msgstr "Оригінальна назва файлу"

msgid "common.specific_search_fields.description_comments"
msgstr "Опис та метадані коментарів"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Завантаження з партнерського сервера тимчасово недоступні для цього файлу."

msgid "common.md5.servers.fast_partner"
msgstr "Швидкий сервер партнерів #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(рекомендовано)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(без перевірки браузера або списків очікування)"

msgid "common.md5.servers.slow_partner"
msgstr "Повільний сервер партнерів #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(трохи швидше, але з чергою)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(немає списку очікування, але може бути дуже повільно)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Library Genesis (.rs-версія) - академічний розділ"

msgid "page.md5.box.download.lgrsfic"
msgstr "Library Genesis (.rs-версія) - художня література"

msgid "page.md5.box.download.lgli"
msgstr "Library Genesis (.li-версія)"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(також натисніть \"GET\" зверху)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(натисніть \"GET\" зверху)"

msgid "page.md5.box.download.libgen_ads"
msgstr "їхні оголошення відомі тим, що містять шкідливе програмне забезпечення, тому використовуйте блокувальник реклами або не натискайте на оголошення"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Файли Nexus/STC можуть бути ненадійними для завантаження)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library (TOR-версія)"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(потребує входу через TOR Browser)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Позичити з Інтернет Архіву"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(друк тільки для патронів)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(пов'язане DOI може бути недоступний у Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "колекція"

msgid "page.md5.box.download.torrent"
msgstr "торрент"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Одночасне завантаження декількох торрент-файлів"

msgid "page.md5.box.download.experts_only"
msgstr "(лише для фахівців)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Шукайте ISBN в Архіві Анни"

msgid "page.md5.box.download.other_isbn"
msgstr "Шукайте ISBN в багатьох інших базах даних"

msgid "page.md5.box.download.original_isbndb"
msgstr "Знайти оригінальний запис в ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Шукайте Open Library ID в Архіві Анни"

msgid "page.md5.box.download.original_openlib"
msgstr "Знайти оригінальний запис в Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Шукайте в архіві Анни за номером OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Знайти оригінальний запис у WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Шукайте в архіві Анни за номером SSID, DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Шукати вручну на DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Шукайте в архіві Анни номер SSNO, CADAL"

msgid "page.md5.box.download.original_cadal"
msgstr "Знайти оригінальний запис у CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Шукайте в архіві Анни номер DXID, DuXiu"

msgid "page.md5.box.download.edsebk"
msgstr "Індекс електронних книг EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Архів Анни 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(не потрібна верифікація браузера)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Чеські метадані %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Метадані"

msgid "page.md5.box.descr_title"
msgstr "опис"

msgid "page.md5.box.alternative_filename"
msgstr "Альтернативне ім'я файлу"

msgid "page.md5.box.alternative_title"
msgstr "Альтернативна назва"

msgid "page.md5.box.alternative_author"
msgstr "Альтернативний автор"

msgid "page.md5.box.alternative_publisher"
msgstr "Альтернативний видавець"

msgid "page.md5.box.alternative_edition"
msgstr "Альтернативне видання"

msgid "page.md5.box.alternative_extension"
msgstr "Альтернативне розширення"

msgid "page.md5.box.metadata_comments_title"
msgstr "коментарі до метаданих"

msgid "page.md5.box.alternative_description"
msgstr "Альтернативний опис"

msgid "page.md5.box.date_open_sourced_title"
msgstr "дата відкритого джерела"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub файл “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending файл “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Це запис файлу з Інтернет-архіву, а не файл, який можна завантажити безпосередньо. Ви можете спробувати позичити книгу (посилання нижче) або використовувати цю URL-адресу, щоб <a %(a_request)s>зробити запит на файл</a>."

msgid "page.md5.header.consider_upload"
msgstr "Якщо у вас є цей файл, але його ще немає в архіві Анни, подумайте про те, щоб <a %(a_request)s>завантажити його</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Запис метаданих ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Запис метаданих Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Номер OCLC (WorldCat) %(id)s запис про метадані"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s запис метаданих"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s запис метаданих"

msgid "page.md5.header.meta_magzdb_id"
msgstr "Запис метаданих MagzDB ID %(id)s"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Запис метаданих Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Це запис метаданих, а не файл, який можна завантажити безпосередньо. Ви можете спробувати позичити книгу (посилання нижче) або використовувати цю URL-адресу, щоб <a %(a_request)s>зробити запит на файл</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Метадані з пов'язаного запису"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Покращити метадані на Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Попередження: кілька пов'язаних записів:"

msgid "page.md5.header.improve_metadata"
msgstr "Покращити метадані"

msgid "page.md5.text.report_quality"
msgstr "Повідомити про якість файлу"

msgid "page.search.results.download_time"
msgstr "Час завантаження"

msgid "page.md5.codes.url"
msgstr "URL-адреса:"

msgid "page.md5.codes.website"
msgstr "Веб-сайт:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Шукайте в архіві Анни “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Дослідник кодів:"

msgid "page.md5.codes.code_search"
msgstr "Переглянути в Досліднику кодів “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Читати більше…"

msgid "page.md5.tabs.downloads"
msgstr "Завантаження (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Позичання (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Дослідити метадані (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Коментарі (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Списки (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Статистика (%(count)s)"

msgid "common.tech_details"
msgstr "Технічні деталі (англійською)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Цей файл має певні проблеми та був прихований в оригінальному джерелі.</span> Іноді таке стається через запит на видалення файлу через конфлікт авторських прав, іноді через наявність кращої альтернативи, але іноді це стається через неправильне форматування чи підозрілість самого файлу. Ми рекомендуємо пошукати альтернативний файл, але ви можете завантажити цей при бажанні. Більше інформації:"

msgid "page.md5.box.download.better_file"
msgstr "Краща версія цього файлу може бути доступна за посиланням %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Якщо ви все ж таки хочете завантажити цей файл, використовуйте перевірене програмне забезпечення останньої версії для його відкриття."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Швидке завантаження"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Швидкі завантаження</strong> <a %(a_membership)s>Підпишіться</a>, щоб підтримати процес довготривалого збереження книг, документів та іншого. На знак нашої вдячності за вашу підтримку, ви отримаєте швидкі завантаження. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Якщо ви зробите донат цього місяця, ви отримаєте <strong>вдвічі</strong> більше швидких завантажень."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Швидкі завантаження</strong> На сьогодні у вас залишилося %(remaining)s. Дякуємо за те, що ви підписалися! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Швидкі завантаження</strong> У вас закінчилися швидкі завантаження на сьогодні."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Швидкі завантаження</strong> Ви вже завантажили цей файл нещодавно. Посилання залишаються дійсними протягом деякого часу."

msgid "page.md5.box.download.option"
msgstr "Опція #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(без перенаправлення)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(відкрити у переглядачі)"

msgid "layout.index.header.banner.refer"
msgstr "Запросіть друга, і ви і ваш друг отримаєте %(percentage)s%% бонусних швидких завантажень!"

msgid "layout.index.header.learn_more"
msgstr "Дізнатися більше…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Повільні завантаження"

msgid "page.md5.box.download.trusted_partners"
msgstr "Від надійних партнерів."

msgid "page.md5.box.download.slow_faq"
msgstr "Більше інформації у <a %(a_slow)s>ЧаПи</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(можливо знадобиться <a %(a_browser)s>верифікація браузера</a> — необмежена кількість завантажень!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Після завантаження:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Відкрити у нашому переглядачі"

msgid "page.md5.box.external_downloads"
msgstr "показати зовнішні завантаження"

msgid "page.md5.box.download.header_external"
msgstr "Зовнішні завантаження"

msgid "page.md5.box.download.no_found"
msgstr "Не знайдено жодного завантаження."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Всі варіанти завантаження мають один і той самий файл і мають бути безпечними у використанні. Тим не менш, завжди будьте обережні, завантажуючи файли з інтернету, особливо з сайтів, що не належать до Архіву Анни. Наприклад, обов'язково оновлюйте свої пристрої."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Для великих файлів ми рекомендуємо використовувати менеджер завантажень, щоб уникнути переривань."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Рекомендовані менеджери завантажень: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Вам знадобиться рідер для електронних книг або PDF, щоб відкрити файл, залежно від формату файлу."

msgid "page.md5.box.download.readers.links"
msgstr "Рекомендовані рідери для електронних книг: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Онлайн-переглядач Архіву Анни"

msgid "page.md5.box.download.conversion"
msgstr "Використовуйте онлайн-інструменти для конвертації між форматами."

msgid "page.md5.box.download.conversion.links"
msgstr "Рекомендовані інструменти для конвертації: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Ви можете відправляти як PDF, так і EPUB файли на ваш Kindle або Kobo eReader."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Рекомендовані інструменти: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon «Send to Kindle»"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz «Send to Kobo/Kindle»"

msgid "page.md5.box.download.support"
msgstr "Підтримуйте авторів та бібліотеки"

msgid "page.md5.box.download.support.authors"
msgstr "Якщо вам це подобається і ви можете собі це дозволити, розгляньте можливість придбання оригіналу або підтримки авторів безпосередньо."

msgid "page.md5.box.download.support.libraries"
msgstr "Якщо це доступно у вашій місцевій бібліотеці, розгляньте можливість безкоштовно взяти його там."

msgid "page.md5.quality.header"
msgstr "Якість файлу"

msgid "page.md5.quality.report"
msgstr "Допоможіть спільноті, повідомивши про якість цього файлу! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Повідомити про проблему з файлом (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Відмінна якість файлу (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Додати коментар (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Що не так з цим файлом?"

msgid "page.md5.quality.copyright"
msgstr "Будь ласка, скористайтеся <a %(a_copyright)s>формою претензії DMCA / авторських прав</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Опишіть проблему (обов'язково)"

msgid "page.md5.quality.issue_description"
msgstr "Опис проблеми"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 кращої версії цього файлу (якщо застосовно)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Заповніть це, якщо є інший файл, який точно відповідає цьому файлу (те саме видання, те саме розширення файлу, якщо ви можете знайти), який люди повинні використовувати замість цього файлу. Якщо ви знаєте кращу версію цього файлу за межами Архіву Анни, будь ласка, <a %(a_upload)s>завантажте її</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Ви можете отримати md5 з URL, наприклад"

msgid "page.md5.quality.submit_report"
msgstr "Надіслати звіт"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Дізнайтеся, як <a %(a_metadata)s>покращити метадані</a> для цього файлу самостійно."

msgid "page.md5.quality.report_thanks"
msgstr "Дякуємо за подання вашого звіту. Він буде показаний на цій сторінці, а також перевірений вручну Анною (поки у нас не буде належної системи модерації)."

msgid "page.md5.quality.report_error"
msgstr "Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.md5.quality.great.summary"
msgstr "Якщо цей файл має високу якість, ви можете обговорити його тут! Якщо ні, будь ласка, скористайтеся кнопкою «Повідомити про проблему з файлом»."

msgid "page.md5.quality.loved_the_book"
msgstr "Мені дуже сподобалася ця книга!"

msgid "page.md5.quality.submit_comment"
msgstr "Залишити коментар"

msgid "common.english_only"
msgstr "Текст нижче доступний лише англійською."

msgid "page.md5.text.stats.total_downloads"
msgstr "Загальна кількість завантажень: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "«MD5 файлу» — це хеш, який обчислюється з вмісту файлу і є досить унікальним на основі цього вмісту. Усі тіньові бібліотеки, які ми індексували тут, в основному використовують MD5 для ідентифікації файлів."

msgid "page.md5.text.md5_info.text2"
msgstr "Файл може з'являтися в декількох тіньових бібліотеках. Для отримання інформації про різні Datasets, які ми зібрали, дивіться <a %(a_datasets)s>сторінку Datasets</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Це файл, керований бібліотекою <a %(a_ia)s>Цифрового Контрольованого Позичання IA</a> і індексований Анною для пошуку. Для отримання інформації про різні Datasets, які ми зібрали, дивіться <a %(a_datasets)s>сторінку Datasets</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Для отримання інформації про цей конкретний файл, перегляньте його <a %(a_href)s>JSON файл</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Проблема з завантаженням цієї сторінки"

msgid "page.aarecord_issue.text"
msgstr "Будь ласка, оновіть сторінку, щоб спробувати ще раз. <a %(a_contact)s>Зв'яжіться з нами</a>, якщо проблема зберігається протягом кількох годин."

msgid "page.md5.invalid.header"
msgstr "Не знайдено"

msgid "page.md5.invalid.text"
msgstr "MD5-суму \"%(md5_input)s\" не знайдено у нашій базі даних."

msgid "page.login.title"
msgstr "Увійти / Зареєструватися"

msgid "page.browserverification.header"
msgstr "Перевірка браузера"

msgid "page.login.text1"
msgstr "Аби запобігти створенню спам-ботами великої кількості облікових записів, нам потрібно спочатку перевірити ваш інтернет-браузер."

msgid "page.login.text2"
msgstr "Якщо ви потрапили в нескінченний цикл, рекомендуємо встановити <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Це також може допомогти вимкнути блокувальники реклами та інші розширення браузера."

msgid "page.codes.title"
msgstr "Коди"

msgid "page.codes.heading"
msgstr "Дослідник кодів"

#, fuzzy
msgid "page.codes.intro"
msgstr "Досліджуйте коди, якими позначені записи, за префіксом. Стовпець «записи» показує кількість записів, позначених кодами з даним префіксом, як це видно в пошуковій системі (включаючи записи лише з метаданими). Стовпець «коди» показує, скільки фактичних кодів мають даний префікс."

msgid "page.codes.why_cloudflare"
msgstr "Ця сторінка може зайняти деякий час для генерації, тому вона вимагає капчу Cloudflare. <a %(a_donate)s>Члени</a> можуть пропустити капчу."

msgid "page.codes.dont_scrape"
msgstr "Будь ласка, не скануйте ці сторінки. Натомість ми рекомендуємо <a %(a_import)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB, і запускати наш <a %(a_software)s>відкритий вихідний код</a>. Сирові дані можна вручну досліджувати через файли JSON, такі як <a %(a_json_file)s>цей</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Префікс"

msgid "common.form.go"
msgstr "Перейти"

msgid "common.form.reset"
msgstr "Скинути"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Пошук в Архіві Анни"

msgid "page.codes.bad_unicode"
msgstr "Попередження: код містить некоректні символи Unicode і може працювати неправильно в різних ситуаціях. Сировий бінарний код можна декодувати з base64 представлення в URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Відомий префікс коду “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Префікс"

msgid "page.codes.code_label"
msgstr "Мітка"

msgid "page.codes.code_description"
msgstr "Опис"

msgid "page.codes.code_url"
msgstr "URL для конкретного коду"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” буде замінено на значення коду"

msgid "page.codes.generic_url"
msgstr "Загальний URL"

msgid "page.codes.code_website"
msgstr "Вебсайт"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s запис, що відповідає “%(prefix_label)s”"
msgstr[1] "%(count)s записи, що відповідають “%(prefix_label)s”"
msgstr[2] "%(count)s записів, що відповідають “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL для конкретного коду: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Більше…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Коди, що починаються з “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Індекс"

msgid "page.codes.records_prefix"
msgstr "записи"

msgid "page.codes.records_codes"
msgstr "коди"

msgid "page.codes.fewer_than"
msgstr "Менше ніж %(count)s записів"

msgid "page.contact.dmca.form"
msgstr "Для DMCA / авторських претензій, використовуйте <a %(a_copyright)s>цю форму</a>."

msgid "page.contact.dmca.delete"
msgstr "Будь-які інші способи зв'язатися з нами щодо претензій з приводу авторських прав будуть автоматично видалені."

msgid "page.contact.checkboxes.text1"
msgstr "Ми дуже раді вашим відгукам та запитанням!"

msgid "page.contact.checkboxes.text2"
msgstr "Однак через велику кількість спаму та безглуздих листів, які ми отримуємо, будь ласка, поставте галочку, щоб підтвердити, що ви розумієте ці умови для зв'язку з нами."

msgid "page.contact.checkboxes.copyright"
msgstr "Претензії щодо авторських прав відправленні на цю електронну скриньку будуть проігноровані, замість цього скористайтеся формою."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Партнерські сервери недоступні через закриття хостингу. Вони мають знову запрацювати незабаром."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Членство буде відповідно продовжено."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Не надсилайте нам <a %(a_request)s>запити на книги</a><br>або невеликі (<10k) <a %(a_upload)s>завантаження</a>."

msgid "page.donate.please_include"
msgstr "При зверненні з питаннями щодо облікового запису або пожертв, додайте свій ідентифікатор облікового запису, скріншоти, квитанції, якомога більше інформації. Ми перевіряємо нашу електронну пошту лише кожні 1-2 тижні, тому відсутність цієї інформації затримає будь-яке вирішення."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Показати електронну скриньку"

msgid "page.copyright.title"
msgstr "Форма претензії DMCA / Авторського права"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Якщо у вас є претензія DMCA або інша претензія щодо авторського права, будь ласка, заповніть цю форму якомога точніше. Якщо у вас виникнуть будь-які проблеми, будь ласка, зв'яжіться з нами за нашою спеціальною адресою DMCA: %(email)s. Зверніть увагу, що претензії, надіслані на цю адресу електронної пошти, не будуть оброблені, вона призначена лише для запитань. Будь ласка, використовуйте форму нижче для подання ваших претензій."

msgid "page.copyright.form.aa_urls"
msgstr "URL-адреси на Архіві Анни (обов'язково). Одна на рядок. Будь ласка, включайте лише URL-адреси, які описують точно таке ж видання книги. Якщо ви хочете подати претензію на кілька книг або кілька видань, будь ласка, подавайте цю форму кілька разів."

msgid "page.copyright.form.aa_urls.note"
msgstr "Претензії, які об'єднують кілька книг або видань, будуть відхилені."

msgid "page.copyright.form.name"
msgstr "Ваше ім'я (обов'язково)"

msgid "page.copyright.form.address"
msgstr "Адреса (обов'язково)"

msgid "page.copyright.form.phone"
msgstr "Номер телефону (обов'язково)"

msgid "page.copyright.form.email"
msgstr "Електронна пошта (обов'язково)"

msgid "page.copyright.form.description"
msgstr "Чіткий опис вихідного матеріалу (обов'язково)"

msgid "page.copyright.form.isbns"
msgstr "ISBN вихідного матеріалу (якщо застосовно). Одна на рядок. Будь ласка, включайте лише ті, що точно відповідають виданню, на яке ви подаєте претензію щодо авторського права."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL-адреси вихідного матеріалу, одна на рядок. Будь ласка, знайдіть час, щоб знайти ваш вихідний матеріал в Open Library. Це допоможе нам перевірити вашу претензію."

msgid "page.copyright.form.external_urls"
msgstr "URL-адреси до вихідного матеріалу, одна на рядок (обов'язково). Будь ласка, включайте якомога більше, щоб допомогти нам перевірити вашу претензію (наприклад, Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Заява та підпис (обов'язково)"

msgid "page.copyright.form.submit_claim"
msgstr "Подати претензію"

msgid "page.copyright.form.on_success"
msgstr "✅ Дякуємо за подання вашої претензії щодо авторського права. Ми розглянемо її якомога швидше. Будь ласка, перезавантажте сторінку, щоб подати ще одну."

msgid "page.copyright.form.on_failure"
msgstr "❌ Щось пішло не так. Будь ласка, перезавантажте сторінку і спробуйте ще раз."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "Якщо ви зацікавлені у дзеркалюванні цього набору даних для <a %(a_archival)s>архівування</a> або <a %(a_llm)s>навчання LLM</a>, будь ласка, зв'яжіться з нами."

msgid "page.datasets.intro.text2"
msgstr "Наша місія — архівувати всі книги у світі (а також статті, журнали тощо) і зробити їх широко доступними. Ми вважаємо, що всі книги повинні бути дзеркальними в багатьох місцях, щоб забезпечити їхню надмірність і стійкість. Ось чому ми збираємо файли з різних джерел. Деякі джерела повністю відкриті і можуть бути дзеркальними в масовому порядку (наприклад, Sci-Hub). Інші закриті і захищені, тому ми намагаємося скрапити їх, щоб «звільнити» їхні книги. Ще інші знаходяться десь посередині."

msgid "page.datasets.intro.text3"
msgstr "Всі наші дані можна <a %(a_torrents)s>завантажити через торрент</a>, а всі наші метадані можна <a %(a_anna_software)s>згенерувати</a> або <a %(a_elasticsearch)s>завантажити</a> як бази даних ElasticSearch і MariaDB. Сирові дані можна вручну досліджувати через JSON файли, такі як <a %(a_dbrecord)s>цей</a>."

msgid "page.datasets.overview.title"
msgstr "Огляд"

msgid "page.datasets.overview.text1"
msgstr "Нижче наведено короткий огляд джерел файлів на Анниному Архіві."

msgid "page.datasets.overview.source.header"
msgstr "Джерело"

msgid "page.datasets.overview.size.header"
msgstr "Розмір"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% віддзеркалено AA / доступні торренти"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Відсотки кількості файлів"

msgid "page.datasets.overview.last_updated.header"
msgstr "Останнє оновлення"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Нон-фікшн та художня література"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s файл"
msgstr[1] "%(count)s файли"
msgstr[2] "%(count)s файлів"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Через Libgen.li «scimag»"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: заморожено з 2021 року; більшість доступна через торренти"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: незначні додавання з того часу</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Виключаючи “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Торренти художньої літератури відстають (хоча ID ~4-6M не торентовані, оскільки вони перетинаються з нашими Zlib торентами)."

msgid "page.datasets.zlibzh.searchable"
msgstr "«Китайська» колекція в Z-Library, здається, така ж, як і наша колекція DuXiu, але з різними MD5. Ми виключаємо ці файли з торентів, щоб уникнути дублювання, але все одно показуємо їх у нашому пошуковому індексі."

msgid "common.record_sources_mapping.iacdl"
msgstr "Цифрове кредитування під контролем IA"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ файлів доступні для пошуку."

msgid "page.datasets.overview.total"
msgstr "Всього"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Без урахування дублікатів"

msgid "page.datasets.overview.text4"
msgstr "Оскільки тіньові бібліотеки часто синхронізують дані одна з одною, між бібліотеками є значний збіг. Ось чому цифри не складаються до загальної суми."

msgid "page.datasets.overview.text5"
msgstr "Відсоток «дзеркалюється та розповсюджується Архівом Анни» показує, скільки файлів ми дзеркалимо самі. Ми розповсюджуємо ці файли оптом через торренти та робимо їх доступними для прямого завантаження через партнерські вебсайти."

msgid "page.datasets.source_libraries.title"
msgstr "Бібліотеки джерел"

msgid "page.datasets.source_libraries.text1"
msgstr "Деякі бібліотеки-джерела сприяють масовому обміну своїми даними через торренти, тоді як інші неохоче діляться своєю колекцією. У останньому випадку, Архів Анни намагається скрапити їхні колекції та зробити їх доступними (дивіться нашу сторінку <a %(a_torrents)s>Торренти</a>). Існують також проміжні ситуації, наприклад, коли бібліотеки-джерела готові ділитися, але не мають ресурсів для цього. У таких випадках ми також намагаємося допомогти."

msgid "page.datasets.source_libraries.text2"
msgstr "Нижче наведено огляд того, як ми взаємодіємо з різними бібліотеками-джерелами."

msgid "page.datasets.sources.source.header"
msgstr "Джерело"

msgid "page.datasets.sources.files.header"
msgstr "Файли"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Щоденні <a %(dbdumps)s>дампи бази даних HTTP</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Автоматизовані торренти для <a %(nonfiction)s>Нон-фікшн</a> та <a %(fiction)s>Фікшн</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Архів Анни керує колекцією <a %(covers)s>торрентів обкладинок книг</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub заморозив нові файли з 2021 року."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Дампи метаданих доступні <a %(scihub1)s>тут</a> і <a %(scihub2)s>тут</a>, а також як частина <a %(libgenli)s>бази даних Libgen.li</a> (яку ми використовуємо)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Торренти даних доступні <a %(scihub1)s>тут</a>, <a %(scihub2)s>тут</a> і <a %(libgenli)s>тут</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Деякі нові файли <a %(libgenrs)s>додаються</a> до “scimag” Libgen, але їх недостатньо для створення нових торрентів"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Щоквартальні <a %(dbdumps)s>дампи бази даних HTTP</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Торренти нон-фікшн діляться з Libgen.rs (і дзеркаляться <a %(libgenli)s>тут</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Архів Анни та Libgen.li спільно керують колекціями <a %(comics)s>коміксів</a>, <a %(magazines)s>журналів</a>, <a %(standarts)s>стандартних документів</a> та <a %(fiction)s>художньої літератури (відокремленої від Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Їхня колекція “fiction_rus” (російська художня література) не має спеціальних торрентів, але покривається торрентами від інших, і ми зберігаємо <a %(fiction_rus)s>дзеркало</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Архів Анни та Z-Library спільно керують колекцією <a %(metadata)s>метаданих Z-Library</a> та <a %(files)s>файлів Z-Library</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Деякі метадані доступні через <a %(openlib)s>дампи бази даних Open Library</a>, але вони не охоплюють всю колекцію IA"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Немає легко доступних дампів метаданих для всієї їхньої колекції"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Архів Анни керує колекцією <a %(ia)s>метаданих IA</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Файли доступні для позики лише на обмеженій основі, з різними обмеженнями доступу"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Архів Анни керує колекцією <a %(ia)s>файлів IA</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Різні бази даних метаданих розкидані по китайському інтернету; хоча часто це платні бази даних"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Немає легкодоступних дампів метаданих для всієї їхньої колекції."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Архів Анни керує колекцією <a %(duxiu)s>метаданих DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Різні бази даних файлів, розкидані по китайському інтернету; часто платні бази даних"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Більшість файлів доступні лише за допомогою преміум-акаунтів BaiduYun; низька швидкість завантаження."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Архів Анни керує колекцією <a %(duxiu)s>файлів DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Різні менші або одноразові джерела. Ми заохочуємо людей спочатку завантажувати до інших тіньових бібліотек, але іноді люди мають колекції, які занадто великі, щоб інші могли їх переглянути, але недостатньо великі, щоб заслуговувати на власну категорію."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Джерела тільки з метаданими"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Ми також збагачуємо нашу колекцію джерелами тільки з метаданими, які ми можемо зіставити з файлами, наприклад, використовуючи номери ISBN або інші поля. Нижче наведено огляд таких джерел. Знову ж таки, деякі з цих джерел є повністю відкритими, тоді як інші ми змушені скрапити."

msgid "page.faq.metadata.inspiration"
msgstr "Наше натхнення для збору метаданих — це мета Аарона Шварца «одна веб-сторінка для кожної книги, яка коли-небудь була опублікована», для якої він створив <a %(a_openlib)s>Open Library</a>. Цей проєкт досяг успіху, але наша унікальна позиція дозволяє нам отримувати метадані, які вони не можуть. Ще одним натхненням було наше бажання дізнатися <a %(a_blog)s>скільки книг існує у світі</a>, щоб ми могли підрахувати, скільки книг нам ще потрібно врятувати."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Зверніть увагу, що в пошуку метаданих ми показуємо оригінальні записи. Ми не об'єднуємо записи."

msgid "page.datasets.sources.last_updated.header"
msgstr "Останнє оновлення"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Щомісячні <a %(dbdumps)s>дампи баз даних</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Недоступні безпосередньо в масовому порядку, захищені від скрапінгу"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Архів Анни керує колекцією <a %(worldcat)s>метаданих OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Уніфікована база даних"

msgid "page.datasets.unified_database.text1"
msgstr "Ми об'єднуємо всі вищезазначені джерела в одну уніфіковану базу даних, яку використовуємо для обслуговування цього вебсайту. Ця уніфікована база даних недоступна безпосередньо, але оскільки Архів Анни є повністю відкритим кодом, її можна досить легко <a %(a_generated)s>згенерувати</a> або <a %(a_downloaded)s>завантажити</a> як бази даних ElasticSearch та MariaDB. Скрипти на цій сторінці автоматично завантажать всі необхідні метадані з вищезазначених джерел."

msgid "page.datasets.unified_database.text2"
msgstr "Якщо ви хочете дослідити наші дані перед тим, як запускати ці скрипти локально, ви можете переглянути наші JSON файли, які посилаються на інші JSON файли. <a %(a_json)s>Цей файл</a> є хорошою відправною точкою."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Адаптовано з нашого <a %(a_href)s>блог-посту</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> — це величезна база даних відсканованих книг, створена <a %(superstar_link)s>SuperStar Digital Library Group</a>. Більшість з них — академічні книги, відскановані для того, щоб зробити їх доступними в цифровому форматі для університетів та бібліотек. Для нашої англомовної аудиторії <a %(princeton_link)s>Прінстон</a> та <a %(uw_link)s>Вашингтонський університет</a> мають хороші огляди. Також є чудова стаття, яка надає більше інформації: <a %(article_link)s>“Оцифровка китайських книг: приклад пошукової системи SuperStar DuXiu Scholar”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Книги з Duxiu давно піратяться в китайському інтернеті. Зазвичай їх продають за менше ніж долар перекупники. Вони зазвичай розповсюджуються за допомогою китайського аналога Google Drive, який часто зламують для збільшення обсягу сховища. Деякі технічні деталі можна знайти <a %(link1)s>тут</a> і <a %(link2)s>тут</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Хоча книги були напівпублічно розповсюджені, їх досить важко отримати у великій кількості. Ми мали це високо у нашому списку справ, і виділили на це кілька місяців повноцінної роботи. Однак, наприкінці 2023 року до нас звернувся неймовірний, дивовижний і талановитий волонтер, який повідомив, що вже виконав всю цю роботу — за великі витрати. Він поділився з нами повною колекцією, не очікуючи нічого взамін, окрім гарантії довгострокового збереження. Дійсно вражаюче."

msgid "page.datasets.common.resources"
msgstr "Ресурси"

msgid "page.datasets.common.total_files"
msgstr "Загальна кількість файлів: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Загальний розмір файлів: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Файли, віддзеркалені Архівом Анни: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Останнє оновлення: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Торренти від Архіву Анни"

msgid "page.datasets.common.aa_example_record"
msgstr "Приклад запису в Архіві Анни"

msgid "page.datasets.duxiu.blog_post"
msgstr "Наш блог-пост про ці дані"

msgid "page.datasets.common.import_scripts"
msgstr "Скрипти для імпорту метаданих"

msgid "page.datasets.common.aac"
msgstr "Формат Контейнерів Архіву Анни"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Більше інформації від наших волонтерів (сирі нотатки):"

msgid "page.datasets.ia.title"
msgstr "IA Контрольоване цифрове кредитування"

msgid "page.datasets.ia.description"
msgstr "Цей набір даних тісно пов'язаний з <a %(a_datasets_openlib)s>набором даних Open Library</a>. Він містить скрапінг усіх метаданих і значну частину файлів з Контрольованої цифрової бібліотеки IA. Оновлення випускаються у <a %(a_aac)s>форматі контейнерів Архіву Анни</a>."

msgid "page.datasets.ia.description2"
msgstr "Ці записи безпосередньо посилаються на набір даних Open Library, але також містять записи, яких немає в Open Library. У нас також є кілька файлів даних, зібраних членами спільноти протягом багатьох років."

msgid "page.datasets.ia.description3"
msgstr "Колекція складається з двох частин. Вам потрібні обидві частини, щоб отримати всі дані (за винятком застарілих торрентів, які викреслені на сторінці торрентів)."

msgid "page.datasets.ia.part1"
msgstr "наш перший випуск, до того як ми стандартизували формат <a %(a_aac)s>Контейнери Архіву Анни (AAC)</a>. Містить метадані (у форматах json та xml), pdf-файли (з цифрових систем позики acsm та lcpdf) та мініатюри обкладинок."

msgid "page.datasets.ia.part2"
msgstr "інкрементальні нові випуски, використовуючи AAC. Містить лише метадані з часовими мітками після 2023-01-01, оскільки решта вже покрита \"ia\". Також всі pdf-файли, цього разу з систем позики acsm та \"bookreader\" (веб-читач IA). Незважаючи на те, що назва не зовсім правильна, ми все одно додаємо файли bookreader до колекції ia2_acsmpdf_files, оскільки вони взаємовиключні."

msgid "page.datasets.common.main_website"
msgstr "Основний %(source)s вебсайт"

msgid "page.datasets.ia.ia_lending"
msgstr "Цифрова бібліотека позик"

msgid "page.datasets.common.metadata_docs"
msgstr "Документація метаданих (більшість полів)"

msgid "page.datasets.isbn_ranges.title"
msgstr "Інформація про країну ISBN"

msgid "page.datasets.isbn_ranges.text1"
msgstr "Міжнародне агентство ISBN регулярно випускає діапазони, які воно виділило національним агентствам ISBN. З цього ми можемо визначити, до якої країни, регіону або мовної групи належить цей ISBN. Наразі ми використовуємо ці дані опосередковано, через бібліотеку Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Ресурси"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Останнє оновлення: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Вебсайт ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Метадані"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Для історії різних форків Library Genesis дивіться сторінку <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li містить більшість того ж контенту та метаданих, що й Libgen.rs, але має деякі додаткові колекції, а саме комікси, журнали та стандартні документи. Він також інтегрував <a %(a_scihub)s>Sci-Hub</a> у свої метадані та пошукову систему, що ми використовуємо для нашої бази даних."

msgid "page.datasets.libgen_li.description3"
msgstr "Метадані для цієї бібліотеки доступні безкоштовно <a %(a_libgen_li)s>на libgen.li</a>. Однак цей сервер повільний і не підтримує відновлення перерваних з'єднань. Ті ж файли також доступні на <a %(a_ftp)s>FTP-сервері</a>, який працює краще."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Торренти доступні для більшості додаткового контенту, зокрема торренти для коміксів, журналів та стандартних документів були випущені у співпраці з Архівом Анни."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Колекція художньої літератури має власні торренти (відмінні від <a %(a_href)s>Libgen.rs</a>), починаючи з %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "За словами адміністратора Libgen.li, колекція “fiction_rus” (російська художня література) повинна бути покрита регулярно випущеними торрентами з <a %(a_booktracker)s>booktracker.org</a>, зокрема торрентами <a %(a_flibusta)s>flibusta</a> та <a %(a_librusec)s>lib.rus.ec</a> (які ми дзеркалимо <a %(a_torrents)s>тут</a>, хоча ми ще не встановили, які торренти відповідають яким файлам)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Статистику для всіх колекцій можна знайти <a %(a_href)s>на вебсайті libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Нон-фікшн також, здається, відхилився, але без нових торентів. Схоже, це сталося з початку 2022 року, хоча ми цього не перевіряли."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Певні діапазони без торрентів (такі як діапазони художньої літератури f_3463000 до f_4260000) ймовірно є файлами Z-Library (або іншими дублікатами), хоча ми можемо захотіти провести деяку дедуплікацію і створити торренти для унікальних файлів lgli в цих діапазонах."

msgid "page.datasets.libgen_li.description5"
msgstr "Зверніть увагу, що торрент-файли, які стосуються “libgen.is”, є явними дзеркалами <a %(a_libgen)s>Libgen.rs</a> (“.is” — це інший домен, який використовує Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Корисний ресурс для використання метаданих — <a %(a_href)s>ця сторінка</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Торренти художньої літератури на Архіві Анни"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Торренти коміксів на Архіві Анни"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Торренти журналів на Архіві Анни"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Торренти стандартних документів в Архіві Анни"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Торренти російської художньої літератури в Архіві Анни"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Метадані"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Метадані через FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Інформація про поля метаданих"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Дзеркало інших торрентів (та унікальні торренти художньої літератури і коміксів)"

msgid "page.datasets.libgen_li.forum"
msgstr "Форум для обговорень"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Наш блог-пост про випуск коміксів"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "Коротка історія різних форків Library Genesis (або “Libgen”) полягає в тому, що з часом різні люди, залучені до Library Genesis, посварилися і пішли своїми шляхами."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Версія “.fun” була створена оригінальним засновником. Вона оновлюється на користь нової, більш розподіленої версії."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Версія “.rs” має дуже схожі дані і найчастіше випускає свою колекцію у вигляді масових торрентів. Вона приблизно розділена на секції “художня література” та “нехудожня література”."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Спочатку на “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Версія “.li”</a> має величезну колекцію коміксів, а також інший контент, який (поки що) недоступний для масового завантаження через торренти. Вона має окрему колекцію торрентів художніх книг і містить метадані <a %(a_scihub)s>Sci-Hub</a> у своїй базі даних."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Згідно з цим <a %(a_mhut)s>дописом на форумі</a>, Libgen.li спочатку був розміщений на “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> в певному сенсі також є форком Library Genesis, хоча вони використовували іншу назву для свого проєкту."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Ця сторінка про версію “.rs”. Вона відома тим, що постійно публікує як свої метадані, так і повний вміст свого каталогу книг. Її колекція книг розділена на частини художньої та нехудожньої літератури."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Корисний ресурс для використання метаданих — <a %(a_metadata)s>ця сторінка</a> (блокує діапазони IP, може знадобитися VPN)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Станом на 2024-03, нові торренти публікуються в <a %(a_href)s>цій темі форуму</a> (блокує діапазони IP, може знадобитися VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Торренти нон-фікшн на Анниному Архіві"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Торренти художньої літератури на Анниному Архіві"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Метадані Libgen.rs"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Інформація про поля метаданих Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Торренти нон-фікшн Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Торренти художньої літератури Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Форум обговорень Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Торренти від Анниного Архіву (обкладинки книг)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Наш блог про випуск обкладинок книг"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis відомий тим, що вже щедро надає свої дані у великих обсягах через торренти. Наша колекція Libgen складається з додаткових даних, які вони не випускають безпосередньо, у партнерстві з ними. Велика подяка всім, хто працює з Library Genesis, за співпрацю з нами!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Випуск 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Цей <a %(blog_post)s>перший випуск</a> досить невеликий: близько 300 ГБ обкладинок книг з форку Libgen.rs, як художньої, так і нон-фікшн літератури. Вони організовані так само, як вони з'являються на libgen.rs, наприклад:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s для нон-фікшн книги."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s для художньої книги."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Так само, як і з колекцією Z-Library, ми зібрали їх усі в один великий .tar файл, який можна змонтувати за допомогою <a %(a_ratarmount)s>ratarmount</a>, якщо ви хочете безпосередньо обслуговувати файли."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> — це власна база даних некомерційної організації <a %(a_oclc)s>OCLC</a>, яка агрегує метадані з бібліотек по всьому світу. Ймовірно, це найбільша колекція бібліотечних метаданих у світі."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Жовтень 2023, початковий випуск:"

msgid "page.datasets.worldcat.description2"
msgstr "У жовтні 2023 року ми <a %(a_scrape)s>випустили</a> комплексний скрапінг бази даних OCLC (WorldCat) у <a %(a_aac)s>форматі контейнерів Анниного Архіву</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Торренти від Анниного Архіву"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Наш блог-пост про ці дані"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library — це проект з відкритим кодом від Internet Archive, спрямований на каталогізацію кожної книги у світі. Він має одну з найбільших у світі операцій зі сканування книг і багато книг доступні для цифрового позичання. Його каталог метаданих книг доступний для безкоштовного завантаження і включений до Анниного Архіву (хоча наразі не в пошуку, за винятком випадків, коли ви явно шукаєте за ID Open Library)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Метадані"

msgid "page.datasets.isbndb.release1.title"
msgstr "Випуск 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Це дамп великої кількості запитів до isbndb.com у вересні 2022 року. Ми намагалися охопити всі діапазони ISBN. Це близько 30,9 мільйонів записів. На їхньому вебсайті вони стверджують, що насправді мають 32,6 мільйонів записів, тому ми могли якось пропустити деякі, або <em>вони</em> могли щось зробити неправильно."

msgid "page.datasets.isbndb.release1.text2"
msgstr "Відповіді JSON майже не змінені з їхнього сервера. Однією з проблем якості даних, яку ми помітили, є те, що для номерів ISBN-13, які починаються з іншого префікса, ніж «978-», вони все одно включають поле «isbn», яке просто є номером ISBN-13 з відрізаними першими 3 цифрами (і перерахованою контрольною цифрою). Це явно неправильно, але вони, здається, так роблять, тому ми не змінювали це."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Ще однією потенційною проблемою, з якою ви можете зіткнутися, є те, що поле «isbn13» має дублікати, тому ви не можете використовувати його як первинний ключ у базі даних. Поля «isbn13»+«isbn» у поєднанні, здається, є унікальними."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Для отримання додаткової інформації про Sci-Hub, будь ласка, зверніться до його <a %(a_scihub)s>офіційного вебсайту</a>, <a %(a_wikipedia)s>сторінки у Вікіпедії</a> та цього <a %(a_radiolab)s>подкаст-інтерв'ю</a>."

msgid "page.datasets.scihub.description2"
msgstr "Зверніть увагу, що Sci-Hub було <a %(a_reddit)s>заморожено з 2021 року</a>. Раніше його вже заморожували, але у 2021 році було додано кілька мільйонів статей. Проте, деяка обмежена кількість статей все ще додається до колекцій Libgen “scimag”, хоча цього недостатньо для створення нових великих торрентів."

msgid "page.datasets.scihub.description3"
msgstr "Ми використовуємо метадані Sci-Hub, надані <a %(a_libgen_li)s>Libgen.li</a> у його колекції “scimag”. Ми також використовуємо набір даних <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Зверніть увагу, що торренти “smarch” є <a %(a_smarch)s>застарілими</a> і тому не включені до нашого списку торрентів."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Торренти на Анниному Архіві"

msgid "page.datasets.scihub.link_metadata"
msgstr "Метадані та торренти"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Торренти на Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Торренти на Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Оновлення на Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Сторінка у Вікіпедії"

msgid "page.datasets.scihub.link_podcast"
msgstr "Подкаст-інтерв'ю"

msgid "page.datasets.upload.title"
msgstr "Завантаження до Архіву Анни"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Огляд зі сторінки <a %(a1)s>Datasets</a>."

msgid "page.datasets.upload.description"
msgstr "Різні менші або одноразові джерела. Ми заохочуємо людей спочатку завантажувати до інших тіньових бібліотек, але іноді люди мають колекції, які занадто великі, щоб інші могли їх переглянути, але недостатньо великі, щоб заслуговувати на власну категорію."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Колекція «завантаження» розділена на менші підколекції, які вказані в AACID та назвах торрентів. Усі підколекції спочатку були дедупліковані з основною колекцією, хоча метадані JSON файлів «upload_records» все ще містять багато посилань на оригінальні файли. Не книжкові файли також були видалені з більшості підколекцій і зазвичай <em>не</em> зазначені в JSON файлах «upload_records»."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Багато підколекцій самі по собі складаються з під-підколекцій (наприклад, з різних оригінальних джерел), які представлені як каталоги в полях «filepath»."

msgid "page.datasets.upload.subs.heading"
msgstr "Підколекції:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Підколекція"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Примітки"

msgid "page.datasets.upload.action.browse"
msgstr "перегляд"

msgid "page.datasets.upload.action.search"
msgstr "пошук"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "З <a %(a_href)s>aaaaarg.fail</a>. Виглядає досить повним. Від нашого волонтера «cgiym»."

msgid "page.datasets.upload.source.acm"
msgstr "З <a %(a_href)s><q>ACM Digital Library 2020</q></a> торрента. Має досить високий збіг з існуючими колекціями статей, але дуже мало збігів MD5, тому ми вирішили зберегти його повністю."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Скрапінг <q>iRead eBooks</q> (фоне́тично <q>ai rit i-books</q>; airitibooks.com), виконаний волонтером <q>j</q>. Відповідає <q>airitibooks</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "З колекції <a %(a1)s><q>Бібліотека Олександрії</q></a>. Частково з оригінального джерела, частково з the-eye.eu, частково з інших дзеркал."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "З приватного торент-сайту книг, <a %(a_href)s>Bibliotik</a> (часто називається «Bib»), книги якого були зібрані в торренти за назвою (A.torrent, B.torrent) і розповсюджені через the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Від нашого волонтера «bpb9v». Для отримання додаткової інформації про <a %(a_href)s>CADAL</a>, дивіться примітки на нашій <a %(a_duxiu)s>сторінці набору даних DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Більше від нашого волонтера «bpb9v», переважно файли DuXiu, а також папки «WenQu» і «SuperStar_Journals» (SuperStar — компанія, що стоїть за DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Від нашого волонтера «cgiym», китайські тексти з різних джерел (представлені як підкаталоги), включаючи <a %(a_href)s>China Machine Press</a> (великий китайський видавець)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Некитайські колекції (представлені як підкаталоги) від нашого волонтера «cgiym»."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Скрапінг книг про китайську архітектуру, виконаний волонтером <q>cm</q>: <q>Я отримав це, використовуючи вразливість мережі у видавництві, але ця лазівка вже закрита</q>. Відповідає <q>chinese_architecture</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Книги від академічного видавництва <a %(a_href)s>De Gruyter</a>, зібрані з кількох великих торентів."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Скрапінг <a %(a_href)s>docer.pl</a>, польського сайту обміну файлами, орієнтованого на книги та інші письмові роботи. Скрапінг виконано наприкінці 2023 року волонтером «p». Ми не маємо хороших метаданих з оригінального сайту (навіть розширень файлів), але ми відфільтрували файли, схожі на книги, і часто могли витягти метадані з самих файлів."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, безпосередньо з DuXiu, зібрані волонтером «w». Лише нещодавні книги DuXiu доступні безпосередньо через електронні книги, тому більшість з них мають бути нещодавніми."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Залишкові файли DuXiu від волонтера «m», які не були у власному форматі PDG DuXiu (основний <a %(a_href)s>набір даних DuXiu</a>). Зібрані з багатьох оригінальних джерел, на жаль, без збереження цих джерел у шляху файлу."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Скрапінг еротичних книг, виконаний волонтером <q>do no harm</q>. Відповідає <q>hentai</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Колекція, скрапінгована з японського видавця манги волонтером «t»."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Вибрані судові архіви Лунцюань</a>, надані волонтером «c»."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Скрапінг <a %(a_href)s>magzdb.org</a>, союзника Library Genesis (він пов'язаний на головній сторінці libgen.rs), але який не хотів надавати свої файли безпосередньо. Отримано волонтером «p» наприкінці 2023 року."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Різні невеликі завантаження, занадто малі для власної підколекції, але представлені як каталоги."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Електронні книги з AvaxHome, російського сайту для обміну файлами."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Архів газет і журналів. Відповідає <q>newsarch_magz</q> metadata у <a %(a1)s><q>Інші скрапінги metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Скрапінг <a %(a1)s>Центру документації з філософії</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Колекція волонтера «o», який збирав польські книги безпосередньо з оригінальних релізів («сцена»)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Об'єднані колекції <a %(a_href)s>shuge.org</a> волонтерами «cgiym» і «woz9ts»."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>«Імперська бібліотека Трантора»</a> (названа на честь вигаданої бібліотеки), скрапінгована у 2022 році волонтером «t»."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Підпідколекції (представлені як каталоги) від волонтера «woz9ts»: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (від <a %(a_sikuquanshu)s>Dizhi(迪志)</a> на Тайвані), mebook (mebook.cc, 我的小书屋, моя маленька книжкова кімната — woz9ts: «Цей сайт головним чином зосереджений на обміні високоякісними файлами електронних книг, деякі з яких були набрані самим власником. Власник був <a %(a_arrested)s>заарештований</a> у 2019 році, і хтось зробив колекцію файлів, якими він поділився.»)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Залишилися файли DuXiu від волонтера «woz9ts», які не були у власному форматі PDG DuXiu (ще потрібно конвертувати в PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Торренти від Архіву Анни"

msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library має свої корені в спільноті <a %(a_href)s>Library Genesis</a> і спочатку використовувала їхні дані. Відтоді вона значно професіоналізувалася і має набагато сучасніший інтерфейс. Тому вони можуть отримувати набагато більше пожертв, як грошових для подальшого покращення свого вебсайту, так і пожертв нових книг. Вони зібрали велику колекцію на додаток до Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Оновлення станом на лютий 2023 року."

msgid "page.datasets.zlib.description.allegations"
msgstr "Наприкінці 2022 року передбачуваних засновників Z-Library було заарештовано, а домени були конфісковані владою Сполучених Штатів. Відтоді вебсайт повільно повертається в онлайн. Невідомо, хто наразі керує ним."

msgid "page.datasets.zlib.description.three_parts"
msgstr "Колекція складається з трьох частин. Оригінальні сторінки опису для перших двох частин збережені нижче. Вам потрібні всі три частини, щоб отримати всі дані (за винятком застарілих торентів, які викреслені на сторінці торентів)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: наш перший реліз. Це був самий перший реліз того, що тоді називалося «Піратське дзеркало бібліотеки» («pilimi»)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: другий реліз, цього разу з усіма файлами, упакованими в .tar файли."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: інкрементні нові релізи, використовуючи <a %(a_href)s>формат контейнерів Архіву Анни (AAC)</a>, тепер випущені у співпраці з командою Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Торренти від Архіву Анни (метадані + контент)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Приклад запису в Архіві Анни (оригінальна колекція)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Приклад запису в Архіві Анни (колекція «zlib3»)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Основний вебсайт"

msgid "page.datasets.zlib.link.onion"
msgstr "Tor-домен"

msgid "page.datasets.zlib.blog.release1"
msgstr "Публікація в блозі про Реліз 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Публікація в блозі про Реліз 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Релізи Zlib (оригінальні сторінки опису)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Реліз 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Початкове дзеркало було ретельно отримано протягом 2021 і 2022 років. На даний момент воно трохи застаріле: воно відображає стан колекції в червні 2021 року. Ми оновимо це в майбутньому. Зараз ми зосереджені на випуску цього першого релізу."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Оскільки Library Genesis вже збережено за допомогою публічних торрентів і включено до Z-Library, ми провели базову дедуплікацію проти Library Genesis у червні 2022 року. Для цього ми використовували MD5-хеші. Ймовірно, у бібліотеці є багато дубльованого контенту, наприклад, кілька форматів файлів з однією і тією ж книгою. Це важко точно виявити, тому ми цього не робимо. Після дедуплікації у нас залишилося понад 2 мільйони файлів, загальним обсягом трохи менше 7 ТБ."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Колекція складається з двох частин: дампу метаданих MySQL у форматі “.sql.gz” та 72 торрент-файлів обсягом приблизно 50-100 ГБ кожен. Метадані містять дані, як повідомляється на вебсайті Z-Library (назва, автор, опис, тип файлу), а також фактичний розмір файлу та md5sum, які ми спостерігали, оскільки іноді ці дані не збігаються. Здається, є діапазони файлів, для яких Z-Library має неправильні метадані. У деяких окремих випадках ми також могли неправильно завантажити файли, що ми спробуємо виявити та виправити в майбутньому."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Великі торрент-файли містять фактичні дані книг, з ідентифікатором Z-Library як ім'ям файлу. Розширення файлів можна відновити за допомогою дампу метаданих."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Колекція є сумішшю нон-фікшн та художнього контенту (не розділеного, як у Library Genesis). Якість також дуже різниться."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Цей перший реліз тепер повністю доступний. Зверніть увагу, що торрент-файли доступні лише через наше дзеркало в Tor."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Реліз 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Ми отримали всі книги, які були додані до Z-Library між нашим останнім дзеркалом і серпнем 2022 року. Ми також повернулися і зібрали деякі книги, які пропустили вперше. Загалом, ця нова колекція становить близько 24 ТБ. Знову ж таки, ця колекція дедуплікована проти Library Genesis, оскільки для цієї колекції вже доступні торренти."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Дані організовані подібно до першого релізу. Є дамп метаданих MySQL у форматі “.sql.gz”, який також включає всі метадані з першого релізу, тим самим замінюючи його. Ми також додали кілька нових колонок:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: чи є цей файл вже в Library Genesis, у колекції нон-фікшн або художньої літератури (зіставлено за md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: у якому торренті знаходиться цей файл."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: встановлено, коли ми не змогли завантажити книгу."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Ми згадували про це минулого разу, але для уточнення: “filename” і “md5” є фактичними властивостями файлу, тоді як “filename_reported” і “md5_reported” — це те, що ми зібрали з Z-Library. Іноді ці два значення не збігаються, тому ми включили обидва."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Для цього релізу ми змінили кодування на “utf8mb4_unicode_ci”, що має бути сумісним зі старішими версіями MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Файли даних подібні до минулого разу, хоча вони набагато більші. Ми просто не могли створювати безліч менших торрент-файлів. “pilimi-zlib2-0-14679999-extra.torrent” містить усі файли, які ми пропустили в останньому релізі, тоді як інші торренти містять нові діапазони ID. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Оновлення %(date)s:</strong> Ми зробили більшість наших торрентів занадто великими, що спричинило проблеми з торрент-клієнтами. Ми видалили їх і випустили нові торренти."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Оновлення %(date)s:</strong> Файлів все ще було занадто багато, тому ми обгорнули їх у tar-файли і знову випустили нові торренти."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Реліз 2 додаток (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Це один додатковий торрент-файл. Він не містить нової інформації, але має деякі дані, які можуть зайняти деякий час для обчислення. Це робить його зручним, оскільки завантаження цього торрента часто швидше, ніж обчислення з нуля. Зокрема, він містить індекси SQLite для tar-файлів, для використання з <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Часті Питання (ЧаПи)"

msgid "page.faq.what_is.title"
msgstr "Що таке \"Архів Анни\"?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Архів Анни</span> - це некомерційний проєкт з двома цілями:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Збереження:</strong> Резервне копіювання всієї інформації та культури людства.</li><li><strong>Доступ:</strong> Зробити цю інформацію та культуру доступною для будь-кого у світі.</li>"

msgid "page.home.intro.open_source"
msgstr "Весь наш <a %(a_code)s>код</a> і <a %(a_datasets)s>дані</a> повністю відкриті."

msgid "page.home.preservation.header"
msgstr "Збереження"

msgid "page.home.preservation.text1"
msgstr "Ми зберігаємо книги, газети, комікси, журнали та багато іншого, збираючи ці матеріали з різних <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">тіньових бібліотек</a>, офіційних бібліотек та інших колекцій в одному місці. Всі ці дані зберігаються назавжди, оскільки їх можна легко дублювати масово за допомогою торрентів, що призводить до створення багатьох копій по всьому світу. Деякі тіньові бібліотеки вже роблять це самі (наприклад, Sci-Hub, Library Genesis), тоді як Архів Анни \"звільняє\" інші бібліотеки, які не пропонують масового поширення (наприклад, Z-Library) або взагалі не є тіньовими бібліотеками (наприклад, Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Це широке розповсюдження в поєднанні з відкритим вихідним кодом робить наш веб-сайт стійким до видалення та забезпечує довготривале збереження людських знань і культури. Дізнайтеся більше про <a href=\"/datasets\">наші дані</a>."

msgid "page.home.preservation.label"
msgstr "За нашими підрахунками, ми зберегли близько <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% усіх книжок світу</a>."

msgid "page.home.access.header"
msgstr "Доступ"

msgid "page.home.access.text"
msgstr "Ми працюємо з партнерами, аби зробити наші колекції легко та вільно доступними для всіх. Ми віримо, що кожен має право на колективну мудрість людства. І <a %(a_search)s>не за рахунок авторів</a>."

msgid "page.home.access.label"
msgstr "Погодинні завантаження за останні 30 днів. Середня кількість завантажень за годину: %(hourly)s. Середня кількість завантажень за день: %(daily)s."

msgid "page.about.text2"
msgstr "Ми віримо, що досвід поколінь має бути надійно збереженим та доступним кожному. Створюючи цю пошукову систему, ми зібрали докупи гігантські інформаційні витвори, створені в результаті кропіткої роботи розробників тіньових бібліотек. І ми сподіваємося, що наша робота буде продовженням їхніх старань по звільненню інформації та донесенню її до людей."

msgid "page.about.text3"
msgstr "Щоб бути в курсі нашого прогресу, слідкуйте за Анною у <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Для запитань та зворотного зв'язку, будь ласка, звертайтеся до Анни за адресою %(email)s."

msgid "page.faq.help.title"
msgstr "Чим можна допомогти?"

msgid "page.about.help.text"
msgstr "<li>1. Слідкуйте за нами у <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, чи <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Розкажіть про Архів Анни у Twitter, Reddit, Tiktok, Instagram, у вашому місцевому кафе чи бібліотеці, чи будь-де, куди б ви не пішли! Ми не віримо в закриття - якщо нас прикриють, ми просто з'явимося в іншому місці, оскільки весь наш код і дані повністю з відкритим вихідним кодом.</li><li>3. Якщо у вас є змога, розгляньте можливість <a href=\"/donate\">донату</a>.</li><li>4. Допомогайте <a href=\"https://translate.annas-software.org/\">перекладати</a> нашу сторінку різними мовами.</li><li>5. Якщо ви інженер-програміст, розгляньте можливість долучитися до розробки нашого <a href=\"https://annas-software.org/\">відкритого коду</a>, чи роздачі <a href=\"/datasets\">торрентів</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Тепер у нас також є синхронізований канал Matrix на %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Якщо ви дослідник інформаційної безпеки, ми можемо використовувати ваші навички як для нападу, так і для захисту. Погляньте на нашу сторінку з <a %(a_security)s>Безпеки</a>."

msgid "page.about.help.text7"
msgstr "7. Ми шукаємо фахівців з платежів для анонімних продавців. Чи можете ви допомогти нам додати зручніші способи для донатів? PayPal, WeChat, подарункові картки. Якщо ви знаєте когось, будь ласка, зв'яжіться з нами."

msgid "page.about.help.text8"
msgstr "8. Ми завжди шукаємо більше серверних потужностей."

msgid "page.about.help.text9"
msgstr "9. Ви можете допомогти, повідомляючи про проблеми з файлами, залишаючи коментарі та створюючи списки прямо на цьому сайті. Ви також можете допомогти <a %(a_upload)s>завантажити більше книг</a>, або виправити проблеми з файлами чи форматуванням існуючих книг."

msgid "page.about.help.text10"
msgstr "10. Створіть або допоможіть підтримувати сторінку Вікіпедії Архіву Анни вашою мовою."

msgid "page.about.help.text11"
msgstr "11. Ми шукаємо можливість розміщувати невеликі, зі смаком оформлені оголошення. Якщо ви хочете розмістити рекламу в Архіві Анни, будь ласка, повідомте нам про це."

msgid "page.faq.help.mirrors"
msgstr "Ми б хотіли, щоб люди встановлювали <a %(a_mirrors)s>дзеркала</a>, і ми будемо фінансово підтримувати цю ініціативу."

msgid "page.about.help.volunteer"
msgstr "Для більш детальної інформації про те, як стати волонтером, дивіться нашу сторінку <a %(a_volunteering)s>Волонтерство та нагороди</a>."

msgid "page.faq.slow.title"
msgstr "Чому завантаження такі повільні?"

msgid "page.faq.slow.text1"
msgstr "Ми буквально не маємо достатньо ресурсів, щоб надати всім у світі високошвидкісні завантаження, як би нам цього не хотілося. Якщо багатий благодійник захоче допомогти нам у цьому, це буде неймовірно, але до того часу ми намагаємося робити все можливе. Ми є неприбутковим проєктом, який ледве підтримується за рахунок пожертв."

msgid "page.faq.slow.text2"
msgstr "Ось чому ми впровадили дві системи для безкоштовних завантажень з нашими партнерами: спільні сервери з повільними завантаженнями та трохи швидші сервери з чергою (щоб зменшити кількість людей, які завантажують одночасно)."

msgid "page.faq.slow.text3"
msgstr "Ми також маємо <a %(a_verification)s>перевірку браузера</a> для наших повільних завантажень, тому що інакше боти та скрапери будуть зловживати ними, що ще більше уповільнить роботу для легітимних користувачів."

msgid "page.faq.slow.text4"
msgstr "Зверніть увагу, що при використанні браузера Tor вам може знадобитися налаштувати параметри безпеки. На найнижчому з варіантів, який називається «Стандартний», виклик Cloudflare turnstile проходить успішно. На вищих варіантах, які називаються «Безпечніший» і «Найбезпечніший», виклик не вдається."

msgid "page.faq.slow.text5"
msgstr "Для великих файлів іноді повільні завантаження можуть перериватися посередині. Ми рекомендуємо використовувати менеджер завантажень (такий як JDownloader) для автоматичного відновлення великих завантажень."

msgid "page.donate.faq.title"
msgstr "Часті запитання про благодійні внески"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Чи поновлюється підписка автоматично?</div> Підписка на сайт <strong>не поновлюється</strong> автоматично. Ви можете підписатися на будь-який період часу."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Чи можу я оновити своє членство або отримати кілька членств?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Чи підтримуєте ви інші методи оплати?</div> Наразі ні, адже тіньові архіви, яким являється наш, потребують додаткової безпеки та анонімності. Якщо у вас є ідеї щодо альтернативних безпечних та зручних методів оплати, пишіть нам на %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Що означають діапазони на місяць?</div> Ви можете досягти нижньої межі діапазону, застосувавши всі знижки, наприклад, обравши період довший за місяць."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>На що ви витрачаєте донати?</div> 100%% коштів йдуть на збереження та поширення знань і культури світу. Наразі ми витрачаємо їх здебільшого на сервери, засоби зберігання даних та збільшення каналів зв'язку. Жодні гроші не йдуть особисто членам нашої команди."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Чи можу я надіслати велику суму?</div> Так, звичайно! Якщо ви збираєтеся надіслати декілька тисяч доларів чи більше, напишіть нам на %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Чи можу я зробити пожертву, не стаючи членом?</div> Звичайно. Ми приймаємо пожертви будь-якої суми на цю адресу Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "Як завантажити нові книги?"

msgid "page.upload.zlib.text1"
msgstr "Альтернативно, ви можете завантажити їх на Z-Library <a %(a_upload)s>тут</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Для невеликих завантажень (до 10 000 файлів) завантажте їх на обидва %(first)s та %(second)s."

msgid "page.upload.text1"
msgstr "Наразі ми рекомендуємо додавати нові книги на гілки Library Genesis. Ось <a %(a_guide)s>зручна інструкція</a>. Зверніть увагу, що обидві гілки, які ми індексуємо на цьому сайті, беруть інформацію з однієї і тієї ж системи завантаження файлів."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Для Libgen.li, переконайтеся, що спочатку увійшли на <a %(a_forum)s >їхній форум</a> з ім’ям користувача %(username)s та паролем %(password)s, а потім поверніться на їхню <a %(a_upload_page)s >сторінку завантаження</a>."

msgid "common.libgen.email"
msgstr "Якщо ваша електронна адреса не працює на форумах Libgen, ми рекомендуємо використовувати <a %(a_mail)s>Proton Mail</a> (безкоштовно). Ви також можете <a %(a_manual)s>особисто надіслати запит</a> на активацію вашого облікового запису."

msgid "page.faq.mhut_upload"
msgstr "Зверніть увагу, що mhut.org блокує певні діапазони IP, тому може знадобитися VPN."

msgid "page.upload.large.text"
msgstr "Щодо великих завантажень (понад 10 000 файлів), які не приймаються Libgen або Z-Library, будь ласка, зв'яжіться з нами за адресою %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Для завантаження наукових статей, будь ласка, також (крім Library Genesis) завантажуйте на <a %(a_stc_nexus)s>STC Nexus</a>. Вони є найкращою тіньовою бібліотекою для нових статей. Ми ще не інтегрували їх, але зробимо це згодом. Ви можете скористатися їх <a %(a_telegram)s>ботом для завантаження в Telegram</a>, або зв'язатися з адресою, вказаною в їх закріпленому повідомленні, якщо у вас занадто багато файлів для завантаження таким чином."

msgid "page.faq.request.title"
msgstr "Як зробити запит на книги?"

msgid "page.request.cannot_accomodate"
msgstr "Наразі ми не можемо задовольнити запити на книги."

msgid "page.request.forums"
msgstr "Будь ласка, залишайте свої запити на форумах Z-Library або Libgen."

msgid "page.request.dont_email"
msgstr "Не надсилайте нам запити щодо книг на електронну скриньку."

msgid "page.faq.metadata.title"
msgstr "Чи збираєте ви метадані?"

msgid "page.faq.metadata.indeed"
msgstr "Так, ми це робимо."

msgid "page.faq.1984.title"
msgstr "Я завантажив \"1984\" Джорджа Орвелла, чи прийде поліція до мене додому?"

msgid "page.faq.1984.text"
msgstr "Не хвилюйтеся занадто, багато людей завантажують з вебсайтів, на які ми посилаємося, і дуже рідко виникають проблеми. Однак, щоб бути в безпеці, ми рекомендуємо використовувати VPN (платний) або <a %(a_tor)s>Tor</a> (безкоштовний)."

msgid "page.faq.save_search.title"
msgstr "Як зберегти налаштування пошуку?"

msgid "page.faq.save_search.text1"
msgstr "Виберіть потрібні налаштування, залиште поле пошуку порожнім, натисніть \"Пошук\", а потім додайте сторінку в закладки за допомогою функції закладок вашого браузера."

msgid "page.faq.mobile.title"
msgstr "Чи є у вас мобільний додаток?"

msgid "page.faq.mobile.text1"
msgstr "У нас немає офіційного мобільного додатку, але ви можете встановити цей вебсайт як додаток."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Натисніть меню з трьома крапками у верхньому правому куті та виберіть \"Додати на головний екран\"."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Натисніть кнопку \"Поділитися\" внизу та виберіть \"Додати на головний екран\"."

msgid "page.faq.api.title"
msgstr "Чи є у вас API?"

msgid "page.faq.api.text1"
msgstr "У нас є стабільний JSON API для членів, щоб отримати швидке посилання для завантаження: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (документація всередині самого JSON)."

msgid "page.faq.api.text2"
msgstr "Для інших випадків використання, таких як ітерація через всі наші файли, створення власного пошуку тощо, ми рекомендуємо <a %(a_generate)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB. Сирові дані можна вручну досліджувати <a %(a_explore)s>через файли JSON</a>."

msgid "page.faq.api.text3"
msgstr "Наш список сирих торрентів також можна завантажити у форматі <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "Часті питання про торренти"

msgid "page.faq.torrents.q1"
msgstr "Я хотів би допомогти з роздачею, але у мене мало місця на диску."

msgid "page.faq.torrents.a1"
msgstr "Використовуйте <a %(a_list)s>генератор списку торрентів</a>, щоб створити список торрентів, які найбільше потребують роздачі, в межах ваших обмежень по місцю для зберігання."

msgid "page.faq.torrents.q2"
msgstr "Торренти занадто повільні; чи можу я завантажити дані безпосередньо від вас?"

msgid "page.faq.torrents.a2"
msgstr "Так, дивіться сторінку <a %(a_llm)s>даних LLM</a>."

msgid "page.faq.torrents.q3"
msgstr "Чи можу я завантажити лише підмножину файлів, наприклад, лише певною мовою або на певну тему?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Коротка відповідь: не так просто."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Довга відповідь:"

msgid "page.faq.torrents.a3"
msgstr "Більшість торрентів містять файли безпосередньо, що означає, що ви можете вказати торрент-клієнтам завантажувати лише потрібні файли. Щоб визначити, які файли завантажувати, ви можете <a %(a_generate)s>генерувати</a> наші метадані або <a %(a_download)s>завантажити</a> наші бази даних ElasticSearch та MariaDB. На жаль, деякі колекції торрентів містять файли .zip або .tar у корені, у такому випадку вам потрібно завантажити весь торрент, перш ніж ви зможете вибрати окремі файли."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(У нас є <a %(a_ideas)s>деякі ідеї</a> для останнього випадку, проте.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Поки що немає зручних інструментів для фільтрації торрентів, але ми вітаємо ваші внески."

msgid "page.faq.torrents.q4"
msgstr "Як ви обробляєте дублікати в торентах?"

msgid "page.faq.torrents.a4"
msgstr "Ми намагаємося мінімізувати дублювання або перекриття між торентами в цьому списку, але це не завжди можливо і сильно залежить від політики бібліотек-джерел. Для бібліотек, які випускають свої власні торенти, це поза нашою компетенцією. Для торентів, випущених Архівом Анни, ми видаляємо дублікати лише на основі MD5-хешу, що означає, що різні версії однієї і тієї ж книги не видаляються."

msgid "page.faq.torrents.q5"
msgstr "Чи можу я отримати список торентів у форматі JSON?"

msgid "page.faq.torrents.a5"
msgstr "Так."

msgid "page.faq.torrents.q6"
msgstr "Я не бачу PDF або EPUB у торентах, тільки бінарні файли? Що робити?"

msgid "page.faq.torrents.a6"
msgstr "Це насправді PDF та EPUB, вони просто не мають розширення у багатьох наших торрентах. Є два місця, де ви можете знайти метадані для торрент-файлів, включаючи типи/розширення файлів:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Кожна колекція або реліз має свої власні метадані. Наприклад, <a %(a_libgen_nonfic)s>торренти Libgen.rs</a> мають відповідну базу метаданих, розміщену на вебсайті Libgen.rs. Ми зазвичай посилаємося на відповідні ресурси метаданих з <a %(a_datasets)s>сторінки набору даних</a> кожної колекції."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Ми рекомендуємо <a %(a_generate)s>генерувати</a> або <a %(a_download)s>завантажувати</a> наші бази даних ElasticSearch та MariaDB. Вони містять мапінг для кожного запису в Архіві Анни до відповідних торрент-файлів (якщо доступні), під “torrent_paths” у JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Чому мій торрент-клієнт не може відкрити деякі з ваших торрент-файлів / магнітних посилань?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Деякі торрент-клієнти не підтримують великі розміри частин, які мають багато наших торрентів (для нових ми цього більше не робимо — хоча це і відповідає специфікаціям!). Тому спробуйте інший клієнт, якщо зіткнетеся з цим, або поскаржтеся розробникам вашого торрент-клієнта."

msgid "page.faq.security.title"
msgstr "Чи є у вас програма відповідального розкриття інформації?"

msgid "page.faq.security.text1"
msgstr "Ми вітаємо дослідників безпеки, які шукають вразливості в наших системах. Ми є великими прихильниками відповідального розкриття інформації. Зв'яжіться з нами <a %(a_contact)s>тут</a>."

msgid "page.faq.security.text2"
msgstr "Наразі ми не можемо надавати винагороди за знайдені помилки, за винятком вразливостей, які мають <a %(a_link)s>потенціал скомпрометувати нашу анонімність</a>, за які ми пропонуємо винагороди в діапазоні $10k-50k. Ми хотіли б у майбутньому розширити сферу дії винагород за знайдені помилки! Зверніть увагу, що атаки соціальної інженерії не входять у сферу дії."

msgid "page.faq.security.text3"
msgstr "Якщо ви зацікавлені в наступальній безпеці та хочете допомогти архівувати знання та культуру світу, обов'язково зв'яжіться з нами. Є багато способів, як ви можете допомогти."

msgid "page.faq.resources.title"
msgstr "Чи є більше ресурсів про Архів Анни?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Блог Анни</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — регулярні оновлення"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Програмне забезпечення Анни</a> — наш відкритий код"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Переклад на Програмне забезпечення Анни</a> — наша система перекладу"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Набори даних</a> — про дані"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — альтернативні домени"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Вікіпедія</a> — більше про нас (будь ласка, допоможіть підтримувати цю сторінку в актуальному стані або створіть одну для своєї мови!)"

msgid "page.faq.copyright.title"
msgstr "Як повідомити про порушення авторських прав?"

msgid "page.faq.copyright.text1"
msgstr "Ми не розміщуємо жодних матеріалів, захищених авторським правом. Ми є пошуковою системою і, як така, індексуємо лише метадані, які вже є у відкритому доступі. Завантажуючи з цих зовнішніх джерел, ми рекомендуємо перевірити закони у вашій юрисдикції щодо того, що дозволено. Ми не несемо відповідальності за контент, розміщений іншими."

msgid "page.faq.copyright.text2"
msgstr "Якщо у вас є скарги на те, що ви бачите тут, найкраще звернутися до оригінального вебсайту. Ми регулярно оновлюємо нашу базу даних їхніми змінами. Якщо ви дійсно вважаєте, що у вас є обґрунтована скарга DMCA, на яку ми повинні відповісти, будь ласка, заповніть <a %(a_copyright)s>форму скарги DMCA / Авторське право</a>. Ми серйозно ставимося до ваших скарг і відповімо вам якомога швидше."

msgid "page.faq.hate.title"
msgstr "Мені не подобається, як ви керуєте цим проєктом!"

msgid "page.faq.hate.text1"
msgstr "Ми також хочемо нагадати всім, що весь наш код і дані є повністю відкритими. Це унікально для проєктів, подібних до нашого — ми не знаємо жодного іншого проєкту з таким же масивним каталогом, який також є повністю відкритим. Ми дуже раді будь-кому, хто вважає, що ми погано керуємо нашим проєктом, взяти наш код і дані та створити власну тіньову бібліотеку! Ми не говоримо це зі злістю чи чимось подібним — ми щиро вважаємо, що це було б чудово, оскільки це підвищило б планку для всіх і краще зберегло б спадщину людства."

msgid "page.faq.uptime.title"
msgstr "Чи є у вас моніторинг доступності?"

msgid "page.faq.uptime.text1"
msgstr "Будь ласка, перегляньте <a %(a_href)s>цей чудовий проєкт</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Як я можу пожертвувати книги або інші фізичні матеріали?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Будь ласка, надішліть їх до <a %(a_archive)s>Internet Archive</a>. Вони належним чином збережуть їх."

msgid "page.faq.anna.title"
msgstr "Хто така Анна?"

msgid "page.faq.anna.text1"
msgstr "Ви — Анна!"

msgid "page.faq.favorite.title"
msgstr "Які ваші улюблені книги?"

msgid "page.faq.favorite.text1"
msgstr "Ось кілька книг, які мають особливе значення для світу тіньових бібліотек і цифрового збереження:"

msgid "page.fast_downloads.no_more_new"
msgstr "Сьогодні у вас закінчилися швидкі завантаження."

msgid "page.fast_downloads.no_member"
msgstr "Підпишіться, аби користуватися швидкими завантаженнями."

msgid "page.fast_downloads.no_member_2"
msgstr "Тепер ми підтримуємо подарункові картки Amazon, кредитні та дебетові картки, криптовалюту, Alipay та WeChat."

msgid "page.home.full_database.header"
msgstr "Повна база даних"

msgid "page.home.full_database.subtitle"
msgstr "Книги, статті, журнали, комікси, бібліотечні записи, метадані, …"

msgid "page.home.full_database.search"
msgstr "Пошук"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>призупинив</a> завантаження нових статей."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB є продовженням Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Прямий доступ до %(count)s наукових статей"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Відкрити"

msgid "page.home.scidb.browser_verification"
msgstr "Якщо ви є <a %(a_member)s>учасником</a>, перевірка браузера не потрібна."

msgid "page.home.archive.header"
msgstr "Довгостроковий архів"

msgid "page.home.archive.body"
msgstr "Набори даних, що використовуються в Архіві Анни, є повністю відкритими, і їх можна масово ввідтворювати за допомогою торрентів. <a %(a_datasets)s>Дізнатись більше…</a>"

msgid "page.home.torrents.body"
msgstr "Ви можете дуже допомогти, якщо зможете роздавати торренти. <a %(a_torrents)s>Дізнатись більше …</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s роздавачів"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s роздавачів"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s роздавачів"

msgid "page.home.llm.header"
msgstr "Навчальні дані LLM"

msgid "page.home.llm.body"
msgstr "Ми маємо найбільшу у світі колекцію високоякісних текстових даних. <a %(a_llm)s>Дізнатись більше…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Дзеркала: набір волонтерів"

msgid "page.home.volunteering.header"
msgstr "🤝 Шукаємо волонтерів"

msgid "page.home.volunteering.help_out"
msgstr "Як неприбутковий, відкритий проєкт, ми завжди шукаємо людей, які можуть допомогти."

msgid "page.home.payment_processor.body"
msgstr "Якщо ви є власником анонімної платіжної системи, будь ласка, зв'яжіться з нами. Ми також шукаємо людей, які бажають розмістити невеликі оголошення. Усі виручені кошти йдуть на наші зусилля щодо збереження."

msgid "layout.index.header.nav.annasblog"
msgstr "Блог Анни↗"

msgid "page.ipfs_downloads.title"
msgstr "Завантаження через IPFS"

msgid "page.partner_download.main_page"
msgstr "🔗 Всі посилання для завантаження цього файлу: <a %(a_main)s>Головна сторінка файлу</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS-портал №%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(спробуйте ще раз, якщо завантаження через IPFS не почалося)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Аби збільшити швидкість завантажень, <a %(a_membership)s>підпишіться на сайт</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Для створення масових резервних копій нашої колекції відвідайте сторінки <a %(a_datasets)s>\"Бази Даних\"</a> та <a %(a_torrents)s>\"Торренти\"</a> ."

msgid "page.llm.title"
msgstr "Дані LLM"

msgid "page.llm.intro"
msgstr "Добре відомо, що LLM процвітають на високоякісних даних. У нас є найбільша колекція книг, статей, журналів тощо у світі, які є одними з найякісніших текстових джерел."

msgid "page.llm.unique_scale"
msgstr "Унікальний масштаб і діапазон"

msgid "page.llm.unique_scale.text1"
msgstr "Наша колекція містить понад сто мільйонів файлів, включаючи наукові журнали, підручники та журнали. Ми досягаємо цього масштабу, поєднуючи великі існуючі репозиторії."

msgid "page.llm.unique_scale.text2"
msgstr "Деякі з наших джерел колекцій вже доступні оптом (Sci-Hub і частини Libgen). Інші джерела ми звільнили самі. <a %(a_datasets)s>Datasets</a> показує повний огляд."

msgid "page.llm.unique_scale.text3"
msgstr "Наша колекція включає мільйони книг, статей і журналів до епохи електронних книг. Великі частини цієї колекції вже були розпізнані OCR і вже мають невелике внутрішнє перекриття."

msgid "page.llm.how_we_can_help"
msgstr "Як ми можемо допомогти"

msgid "page.llm.how_we_can_help.text1"
msgstr "Ми можемо надати високошвидкісний доступ до наших повних колекцій, а також до невипущених колекцій."

msgid "page.llm.how_we_can_help.text2"
msgstr "Це доступ на рівні підприємства, який ми можемо надати за пожертви в діапазоні десятків тисяч доларів США. Ми також готові обміняти це на високоякісні колекції, яких у нас ще немає."

msgid "page.llm.how_we_can_help.text3"
msgstr "Ми можемо повернути вам кошти, якщо ви зможете надати нам збагачення наших даних, наприклад:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Видалення дублювання (дедуплікація)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Витяг тексту та метаданих"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Підтримуйте довгострокове архівування людських знань, отримуючи кращі дані для своєї моделі!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Зв'яжіться з нами</a>, щоб обговорити, як ми можемо співпрацювати."

msgid "page.login.continue"
msgstr "Продовжити"

msgid "page.login.please"
msgstr "Будь ласка, <a %(a_account)s>увійдіть</a> щоб переглянути цю сторінку.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Архів Анни тимчасово недоступний через технічне обслуговування. Будь ласка, поверніться через годину."

#, fuzzy
msgid "page.metadata.header"
msgstr "Покращити метадані"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Ви можете допомогти збереженню книг, покращуючи метадані! Спочатку прочитайте інформацію про метадані на Архіві Анни, а потім дізнайтеся, як покращити метадані через зв’язок з Open Library, і отримайте безкоштовне членство на Архіві Анни."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Інформація"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Коли ви дивитеся на книгу в Архіві Анни, ви можете побачити різні поля: назва, автор, видавець, видання, рік, опис, ім'я файлу та інше. Вся ця інформація називається <em>метадані</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Оскільки ми об'єднуємо книги з різних <em>бібліотек-джерел</em>, ми показуємо будь-які метадані, доступні в цій бібліотеці-джерелі. Наприклад, для книги, яку ми отримали з Library Genesis, ми покажемо назву з бази даних Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Іноді книга присутня в <em>декількох</em> бібліотеках-джерелах, які можуть мати різні поля метаданих. У такому випадку ми просто показуємо найдовшу версію кожного поля, оскільки вона, ймовірно, містить найбільш корисну інформацію! Ми все одно покажемо інші поля під описом, наприклад, як «альтернативна назва» (але тільки якщо вони відрізняються)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Ми також витягуємо <em>коди</em>, такі як ідентифікатори та класифікатори, з бібліотеки-джерела. <em>Ідентифікатори</em> унікально представляють конкретне видання книги; прикладами є ISBN, DOI, Open Library ID, Google Books ID або Amazon ID. <em>Класифікатори</em> групують разом кілька схожих книг; прикладами є Dewey Decimal (DCC), UDC, LCC, RVK або GOST. Іноді ці коди явно пов'язані в бібліотеках-джерелах, а іноді ми можемо витягти їх з імені файлу або опису (переважно ISBN та DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Ми можемо використовувати ідентифікатори для пошуку записів у <em>колекціях, що містять лише метадані</em>, таких як OpenLibrary, ISBNdb або WorldCat/OCLC. У нашій пошуковій системі є спеціальна <em>вкладка метаданих</em>, якщо ви хочете переглянути ці колекції. Ми використовуємо відповідні записи для заповнення відсутніх полів метаданих (наприклад, якщо відсутня назва), або, наприклад, як «альтернативна назва» (якщо існує існуюча назва)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Щоб побачити, звідки саме взялися метадані книги, дивіться <em>вкладку «Технічні деталі»</em> на сторінці книги. Вона містить посилання на сирий JSON для цієї книги з вказівками на сирий JSON оригінальних записів."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Для отримання додаткової інформації дивіться наступні сторінки: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Пошук (вкладка метаданих)</a>, <a %(a_codes)s>Дослідник кодів</a> та <a %(a_example)s>Приклад метаданих JSON</a>. Нарешті, всі наші метадані можуть бути <a %(a_generated)s>згенеровані</a> або <a %(a_downloaded)s>завантажені</a> як бази даних ElasticSearch та MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Зв’язок з Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Отже, якщо ви натрапили на файл з поганими метаданими, як його виправити? Ви можете перейти до бібліотеки-джерела і дотримуватися її процедур для виправлення метаданих, але що робити, якщо файл присутній у кількох бібліотеках-джерелах?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Є один ідентифікатор, який має особливе значення в Архіві Анни. <strong>Поле annas_archive md5 в Open Library завжди переважає над усіма іншими метаданими!</strong> Давайте спочатку трохи повернемося назад і дізнаємося про Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library була заснована у 2006 році Аароном Шварцем з метою «одна веб-сторінка для кожної книги, яка коли-небудь була опублікована». Це свого роду Вікіпедія для метаданих книг: кожен може її редагувати, вона вільно ліцензована і може бути завантажена в масовому порядку. Це база даних книг, яка найбільше відповідає нашій місії — насправді, Архів Анни був натхненний баченням і життям Аарона Шварца."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Замість того, щоб винаходити колесо заново, ми вирішили направити наших волонтерів до Open Library. Якщо ви бачите книгу з неправильними метаданими, ви можете допомогти наступним чином:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Перейдіть на <a %(a_openlib)s>веб-сайт Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Знайдіть правильний запис книги. <strong>УВАГА:</strong> обов’язково виберіть правильне <strong>видання</strong>. В Open Library є «твори» та «видання»."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "«Твір» може бути «Гаррі Поттер і філософський камінь»."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "«Видання» може бути:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Перше видання 1997 року, опубліковане Bloomsbery, має 256 сторінок."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Видання у м'якій обкладинці 2003 року, опубліковане Raincoast Books, має 223 сторінки."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Польський переклад 2000 року «Harry Potter I Kamie Filozoficzn» від Media Rodzina має 328 сторінок."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Усі ці видання мають різні ISBN та різний вміст, тому обов’язково виберіть правильне!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Редагуйте запис (або створіть його, якщо він не існує), і додайте якомога більше корисної інформації! Ви вже тут, тож зробіть запис дійсно чудовим."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "У розділі «ID Numbers» виберіть «Архів Анни» і додайте MD5 книги з Архіву Анни. Це довгий рядок літер і цифр після «/md5/» в URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Спробуйте знайти інші файли в Архіві Анни, які також відповідають цьому запису, і додайте їх також. У майбутньому ми зможемо групувати їх як дублікати на сторінці пошуку Архіву Анни."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Коли закінчите, запишіть URL, який ви щойно оновили. Після того, як ви оновите принаймні 30 записів з MD5 з Архіву Анни, надішліть нам <a %(a_contact)s>електронний лист</a> і надішліть нам список. Ми надамо вам безкоштовне членство в Архіві Анни, щоб ви могли легше виконувати цю роботу (і як подяку за вашу допомогу). Ці редагування мають бути високої якості та додавати значну кількість інформації, інакше ваш запит буде відхилено. Ваш запит також буде відхилено, якщо будь-яке з редагувань буде скасовано або виправлено модераторами Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Зверніть увагу, що це працює лише для книг, а не для наукових статей чи інших типів файлів. Для інших типів файлів ми все ще рекомендуємо знайти вихідну бібліотеку. Може знадобитися кілька тижнів, щоб зміни були включені в Архів Анни, оскільки нам потрібно завантажити останній дамп даних Open Library і відновити наш пошуковий індекс."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Дзеркала: заклик до волонтерів"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Щоб підвищити стійкість Архіву Анни, ми шукаємо волонтерів для запуску дзеркал."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Ми шукаємо це:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Ви керуєте відкритим вихідним кодом Anna’s Archive і регулярно оновлюєте як код, так і дані."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Ваша версія чітко відрізняється як дзеркало, наприклад, «Архів Боба, дзеркало Anna’s Archive»."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Ви готові прийняти ризики, пов’язані з цією роботою, які є значними. Ви глибоко розумієте необхідну операційну безпеку. Зміст <a %(a_shadow)s>цих</a> <a %(a_pirate)s>публікацій</a> є для вас очевидним."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Ви готові внести свій внесок у наш <a %(a_codebase)s>вихідний код</a> — у співпраці з нашою командою — щоб це сталося."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Спочатку ми не надамо вам доступ до завантажень з серверів наших партнерів, але якщо все піде добре, ми можемо поділитися цим з вами."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Витрати на хостинг"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Ми готові покрити витрати на хостинг і VPN, спочатку до $200 на місяць. Цього достатньо для базового пошукового сервера та проксі, захищеного DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Ми будемо платити за хостинг лише після того, як ви все налаштуєте та продемонструєте, що можете підтримувати архів в актуальному стані з оновленнями. Це означає, що перші 1-2 місяці вам доведеться платити з власної кишені."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Ваш час не буде компенсовано (і наш також), оскільки це чисто волонтерська робота."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Якщо ви значно залучитеся до розробки та операцій нашої роботи, ми можемо обговорити розподіл більшої частини доходів від пожертв з вами, щоб ви могли використовувати їх за потреби."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Початок роботи"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Будь ласка, <strong>не зв’язуйтеся з нами</strong> для отримання дозволу або з базовими питаннями. Дії говорять голосніше за слова! Вся інформація є у відкритому доступі, тому просто починайте налаштовувати своє дзеркало."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Не соромтеся створювати тікети або запити на злиття в нашому Gitlab, коли стикаєтеся з проблемами. Можливо, нам доведеться створити деякі функції, специфічні для дзеркал, разом з вами, такі як ребрендинг з «Anna’s Archive» на назву вашого вебсайту, (спочатку) відключення облікових записів користувачів або посилання на наш основний сайт зі сторінок книг."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Коли ваше дзеркало буде працювати, будь ласка, зв’яжіться з нами. Ми будемо раді переглянути вашу операційну безпеку, і коли вона буде надійною, ми додамо посилання на ваше дзеркало та почнемо тісніше співпрацювати з вами."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Заздалегідь дякуємо всім, хто готовий внести свій внесок у цей спосіб! Це не для слабкодухих, але це зміцнить довговічність найбільшої справді відкритої бібліотеки в історії людства."

msgid "page.partner_download.header"
msgstr "Завантажити з веб-сайту партнерів"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Повільне завантаження доступне лише через офіційний сайт. Відвідайте %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Повільні завантаження недоступні через VPN Cloudflare або з IP-адрес Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Будь ласка, зачекайте <span %(span_countdown)s>%(wait_seconds)s</span> секунд, щоб завантажити цей файл."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Використовуйте цю URL-адресу, щоб завантажити: <a %(a_download)s>Завантажити зараз</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Дякуємо за очікування, це дозволяє зберігати доступ до сайту безкоштовним для всіх! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Попередження: за останні 24 години з вашої IP-адреси було багато завантажень. Завантаження можуть відбуватися повільніше, ніж зазвичай."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Завантаження з вашої IP-адреси за останні 24 години: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Якщо ви використовуєте VPN, спільне інтернет-з'єднання або ваш провайдер інтернету ділить IP-адреси, це попередження може бути через це."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Щоб дати всім можливість завантажувати файли безкоштовно, вам потрібно зачекати, перш ніж ви зможете завантажити цей файл."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Ви можете продовжити перегляд Архіву Анни в іншій вкладці, поки чекаєте (якщо ваш браузер підтримує оновлення вкладок у фоновому режимі)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Ви можете чекати завантаження кількох сторінок одночасно (але, будь ласка, завантажуйте лише один файл одночасно з кожного сервера)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Як тільки ви отримаєте посилання для завантаження, воно буде дійсним протягом кількох годин."

msgid "layout.index.header.title"
msgstr "Архів Анни"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Запис в Архіві Анни"

msgid "page.scidb.download"
msgstr "Завантажити"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Щоб підтримати доступність і довгострокове збереження знань людства, станьте <a %(a_donate)s>членом</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Як бонус, 🧬&nbsp;SciDB завантажується швидше для членів, без жодних обмежень."

msgid "page.scidb.refresh"
msgstr "Не працює? Спробуйте <a %(a_refresh)s>перезавантажити</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Попередній перегляд ще недоступний. Завантажте файл з <a %(a_path)s>Архіву Анни</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB є продовженням Sci-Hub, з його знайомим інтерфейсом і прямим переглядом PDF. Введіть свій DOI для перегляду."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Ми маємо повну колекцію Sci-Hub, а також нові статті. Більшість можна переглянути безпосередньо через знайомий інтерфейс, подібний до Sci-Hub. Деякі можна завантажити через зовнішні джерела, у такому разі ми показуємо посилання на них."

msgid "page.search.title.results"
msgstr "%(search_input)s – Пошук"

msgid "page.search.title.new"
msgstr "Шукати нові книги"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Включити лише"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Виключити"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Не перевірено"

msgid "page.search.tabs.download"
msgstr "Завантажити"

msgid "page.search.tabs.journals"
msgstr "Журнальні статті"

msgid "page.search.tabs.digital_lending"
msgstr "Цифрове позичання"

msgid "page.search.tabs.metadata"
msgstr "Метадані"

msgid "common.search.placeholder"
msgstr "Пошук по назві, автору, DOi, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Пошук"

msgid "page.search.search_settings"
msgstr "Налаштування пошуку"

msgid "page.search.submit"
msgstr "Пошук"

msgid "page.search.too_long_broad_query"
msgstr "Пошук зайняв занадто багато часу, що характерно для великих запитів. Кількість фільтрів може бути неточною."

msgid "page.search.too_inaccurate"
msgstr "Пошук зайняв занадто багато часу, а це означає, що ви можете побачити неточні результати. Іноді <a %(a_reload)s>перезавантаження</a> сторінки допомагає."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Відображення"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Список"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Таблиця"

msgid "page.search.advanced.header"
msgstr "Розширений"

msgid "page.search.advanced.description_comments"
msgstr "Шукати описи та коментарі до метаданих"

msgid "page.search.advanced.add_specific"
msgstr "Додати конкретне поле пошуку"

msgid "common.specific_search_fields.select"
msgstr "(пошук у певному полі)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Рік публікації"

msgid "page.search.filters.content.header"
msgstr "Зміст"

msgid "page.search.filters.filetype.header"
msgstr "Тип файлу"

msgid "page.search.more"
msgstr "більше…"

msgid "page.search.filters.access.header"
msgstr "Доступ"

msgid "page.search.filters.source.header"
msgstr "Джерело"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "зібрано та опубліковано у відкритому доступі AA"

msgid "page.search.filters.language.header"
msgstr "Мова"

msgid "page.search.filters.order_by.header"
msgstr "Порядок за"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Найбільш релевантні"

msgid "page.search.filters.sorting.newest"
msgstr "Найновіші"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(рік видання)"

msgid "page.search.filters.sorting.oldest"
msgstr "Найстаріші"

msgid "page.search.filters.sorting.largest"
msgstr "Найбільші"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(розмір файлу)"

msgid "page.search.filters.sorting.smallest"
msgstr "Найменші"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(з відкритих джерел)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Випадковий"

msgid "page.search.header.update_info"
msgstr "Пошуковий індекс оновлюється щомісяця. Наразі він включає записи до %(last_data_refresh_date)s. Для отримання додаткової технічної інформації дивіться %(link_open_tag)sсторінку про базу даних</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Щоб дослідити пошуковий індекс за кодами, скористайтеся <a %(a_href)s>Дослідником кодів</a>."

msgid "page.search.results.search_downloads"
msgstr "Введіть у поле для пошуку в нашому каталозі %(count)s файли, які можна завантажити безпосередньо, які ми <a %(a_preserve)s>збережемо назавжди</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Насправді, кожен може допомогти зберегти ці файли, роздаючи наш <a %(a_torrents)s>уніфікований список торрентів</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Наразі ми маємо найповніший у світі відкритий каталог книг, статей та інших письмових робіт. Ми віддзеркалюємо Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>та інших</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Якщо ви знайдете інші \"тіньові бібліотеки\", які нам слід віддзеркалити, або якщо у вас виникнуть запитання, будь ласка, зв'яжіться з нами за адресою %(email)s."

msgid "page.search.results.dmca"
msgstr "Для отримання інформації щодо DMCA / авторських прав <a %(a_copyright)s>натистіть тут</a>."

msgid "page.search.results.shortcuts"
msgstr "Порада: використовуйте клавіатурні скорочення \"/\" (фокус пошуку), \"enter\" (пошук), \"j\" (вгору), \"k\" (вниз) для швидкої навігації."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Шукаєте статті?"

msgid "page.search.results.search_journals"
msgstr "Введіть у поле для пошуку в нашому каталозі %(count)s наукові роботи та журнальні статті, які ми <a %(a_preserve)s>зберігаємо назавжди</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Введіть у поле для пошуку файлів в цифрових бібліотеках, що позичають."

msgid "page.search.results.digital_lending_info"
msgstr "Цей пошуковий індекс наразі включає метадані з бібліотеки керованого цифрового позичання Інтернет-архіву. <a %(a_datasets)s>Більше про наші даніs</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Щоб дізнатися більше про цифрові бібліотеки, дивіться <a %(a_wikipedia)s>Вікіпедію</a> та <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Введіть у поле для пошуку метаданих з бібліотек. Це може бути корисно, при <a %(a_request)s>запиті файлу</a>."

msgid "page.search.results.metadata_info"
msgstr "Цей пошуковий індекс наразі включає метадані з ISBNdb та Open Library. <a %(a_datasets)s>Більше про наші дані</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Для метаданих ми показуємо оригінальні записи. Ми не робимо об'єднання записів."

msgid "page.search.results.metadata_info_more"
msgstr "Існує дуже багато джерел метаданих для письмових творів по всьому світу. <a %(a_wikipedia)s>Ця сторінка Вікіпедії</a> є гарним початком, але якщо ви знаєте інші хороші списки, будь ласка, повідомте нам."

msgid "page.search.results.search_generic"
msgstr "Введіть у поле для пошуку."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Це записи метаданих, <span %(classname)s>не</span> файли для завантаження."

msgid "page.search.results.error.header"
msgstr "Помилка під час пошуку."

msgid "page.search.results.error.unknown"
msgstr "Спробуйте <a %(a_reload)s>перезавантажити сторінку</a>. Якщо проблема не зникає, будь ласка, напишіть нам на %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Нічого не знайдено.</span> Спробуйте скоротити запит чи використайте інші ключові слова чи фільтри."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Іноді це трапляється неправильно, коли сервер пошуку працює повільно. У таких випадках <a %(a_attrs)s>перезавантаження</a> може допомогти."

msgid "page.search.found_matches.main"
msgstr "Ми знайшли збіги: %(in)s. Ви можете посилатися на знайдену там URL-адресу <a %(a_request)s>під час запиту файлу</a>."

msgid "page.search.found_matches.journals"
msgstr "Журнальні статті (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Цифрове позичання (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Метадані (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Результати %(from)s-%(to)s (%(total)s всі)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ часткових збігів"

msgid "page.search.results.partial"
msgstr "%(num)d часткових збігів"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Волонтерство та нагороди"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive покладається на волонтерів, таких як ви. Ми вітаємо всі рівні залученості та маємо дві основні категорії допомоги, яку ми шукаємо:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Легка волонтерська робота:</span> якщо ви можете виділити лише кілька годин тут і там, все одно є багато способів, як ви можете допомогти. Ми винагороджуємо постійних волонтерів <span %(bold)s>🤝 членствами в Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Інтенсивна волонтерська робота (винагороди від 50 до 5000 доларів США):</span> якщо ви можете присвятити багато часу та/або ресурсів нашій місії, ми будемо раді тісніше співпрацювати з вами. З часом ви зможете приєднатися до внутрішньої команди. Хоча наш бюджет обмежений, ми можемо нагороджувати <span %(bold)s>💰 грошовими винагородами</span> за найінтенсивнішу роботу."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Якщо ви не можете волонтерити свій час, ви все одно можете дуже допомогти нам, <a %(a_donate)s>пожертвувавши гроші</a>, <a %(a_torrents)s>роздаючи наші торренти</a>, <a %(a_uploading)s>завантажуючи книги</a> або <a %(a_help)s>розповідаючи своїм друзям про Архів Анни</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Компанії:</span> ми пропонуємо високошвидкісний прямий доступ до наших колекцій в обмін на корпоративні пожертви або обмін на нові колекції (наприклад, нові скани, OCR’овані datasets, збагачення наших даних). <a %(a_contact)s>Зв'яжіться з нами</a>, якщо це про вас. Дивіться також нашу <a %(a_llm)s>сторінку LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Легка волонтерська робота"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Якщо у вас є кілька вільних годин, ви можете допомогти різними способами. Обов’язково приєднуйтесь до <a %(a_telegram)s>чату волонтерів у Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Як знак вдячності, ми зазвичай надаємо 6 місяців статусу “Щасливий Бібліотекар” за базові досягнення, і більше за продовження волонтерської роботи. Усі досягнення вимагають високоякісної роботи — недбала робота шкодить нам більше, ніж допомагає, і ми її відхилимо. Будь ласка, <a %(a_contact)s>напишіть нам електронного листа</a>, коли досягнете певного етапу."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Завдання"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Досягнення"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Поширення інформації про Архів Анни. Наприклад, рекомендування книг на AA, посилання на наші публікації в блозі або загальне направлення людей на наш вебсайт."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s посилання або скріншоти."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Це повинно показати, як ви повідомляєте комусь про Архів Анни, і як вони дякують вам."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Покращення метаданих шляхом <a %(a_metadata)s>зв’язування</a> з Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Ви можете використовувати <a %(a_list)s >список випадкових проблем з metadata</a> як відправну точку."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Обов’язково залишайте коментарі до виправлених проблем, щоб інші не дублювали вашу роботу."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s посилання на записи, які ви покращили."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Переклад</a> вебсайту."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Повний переклад мови (якщо він не був майже завершений)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Покращення сторінки Вікіпедії про Архів Анни вашою мовою. Включіть інформацію зі сторінки Вікіпедії AA іншими мовами, а також з нашого вебсайту та блогу. Додайте посилання на AA на інших відповідних сторінках."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Посилання на історію редагувань, що показує ваші значні внески."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Виконання запитів на книги (або статті тощо) на форумах Z-Library або Library Genesis. У нас немає власної системи запитів на книги, але ми дзеркалимо ці бібліотеки, тому покращення їх робить кращим і Архів Анни."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s посилання або скріншоти запитів, які ви виконали."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Невеликі завдання, опубліковані в нашому <a %(a_telegram)s>чаті волонтерів у Telegram</a>. Зазвичай для членства, іноді для невеликих винагород."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Невеликі завдання, розміщені в нашій групі волонтерів."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Залежить від завдання."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Винагороди"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Ми завжди шукаємо людей з міцними навичками програмування або наступальної безпеки, щоб залучити їх до нашої роботи. Ви можете зробити серйозний внесок у збереження спадщини людства."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Як подяку, ми надаємо членство за вагомі внески. Як велику подяку, ми надаємо грошові винагороди за особливо важливі та складні завдання. Це не повинно розглядатися як заміна роботи, але це додатковий стимул і може допомогти з покриттям витрат."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Більшість нашого коду є відкритим, і ми попросимо, щоб ваш код також був відкритим при наданні винагороди. Є деякі винятки, які ми можемо обговорити індивідуально."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Винагороди надаються першій людині, яка виконає завдання. Не соромтеся коментувати завдання з винагородою, щоб інші знали, що ви працюєте над чимось, щоб вони могли утриматися або зв'язатися з вами для співпраці. Але майте на увазі, що інші також можуть працювати над цим і намагатися випередити вас. Однак ми не надаємо винагороди за недбалу роботу. Якщо дві високоякісні подачі зроблені близько одна до одної (протягом одного-двох днів), ми можемо вирішити надати винагороди обом, на наш розсуд, наприклад, 100%% за першу подачу і 50%% за другу подачу (тобто всього 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Для більших винагород (особливо за скрапінг), будь ласка, зв'яжіться з нами, коли ви завершите ~5%% завдання, і ви впевнені, що ваш метод масштабуватиметься до повного етапу. Вам доведеться поділитися своїм методом з нами, щоб ми могли надати зворотний зв'язок. Також, таким чином, ми можемо вирішити, що робити, якщо кілька людей наближаються до винагороди, наприклад, потенційно надавати її кільком людям, заохочувати людей до співпраці тощо."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "УВАГА: завдання з високими винагородами <span %(bold)s>складні</span> — можливо, варто почати з легших."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Перейдіть до нашого <a %(a_gitlab)s>списку завдань на Gitlab</a> і сортуйте за “Пріоритет мітки”. Це приблизно показує порядок завдань, які нас цікавлять. Завдання без явних винагород все ще можуть претендувати на членство, особливо ті, що позначені як “Прийнято” та “Улюблене Анни”. Можливо, вам варто почати з “Початкового проекту”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Оновлення про <a %(wikipedia_annas_archive)s>Архів Анни</a>, найбільшу справді відкриту бібліотеку в історії людства."

msgid "layout.index.title"
msgstr "Архів Анни"

msgid "layout.index.meta.description"
msgstr "Найбільша у світі бібліотека з відкритим вихідним кодом та відкритими даними. Включає Sci-Hub, Library Genesis, Z-Library та інші."

msgid "layout.index.meta.opensearch"
msgstr "Пошук в Архіві Анни"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Архів Анни потребує вашої допомоги!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Багато хто намагається нас знищити, але ми боремося."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Якщо ви пожертвуєте зараз, ви отримаєте <strong>подвійно</strong> більше швидких завантажень."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Дійсно до кінця цього місяця."

msgid "layout.index.header.nav.donate"
msgstr "Задонатити"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Збереження людських знань: чудовий подарунок до свята!"

msgid "layout.index.header.banner.surprise"
msgstr "Здивуйте кохану людину, подаруйте їй обліковий запис із членством."

msgid "layout.index.header.banner.mirrors"
msgstr "Щоб підвищити стійкість \"Архіву Анни\", ми шукаємо волонтерів для роботи з дзеркалами."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Ідеальний подарунок на День Святого Валентина!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Ми додали новий метод для донацій: %(method_name)s. Обслуговування сайту є дорогим та ми були б дуже вдячні вам за %(donate_link_open_tag)sпідтримку</a>. Дуже вам дякуємо."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Ми проводимо збір коштів на <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">створення резервної копії</a> найбільшої тіньової бібліотеки коміксів у світі. Дякуємо за вашу підтримку! <a href=\"/donate\">Зробити благодійний внесок.</a> Якщо ви не можете зробити благодійний внесок, підтримайте нас, розказавши друзям та підписавшись на нас у <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, або <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Нещодавні завантаження:"

msgid "layout.index.header.nav.search"
msgstr "Пошук"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "ЧаПи"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Покращити метадані"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Волонтерство та нагороди"

msgid "layout.index.header.nav.datasets"
msgstr "Бази даних"

msgid "layout.index.header.nav.torrents"
msgstr "Торренти"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Активність"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Дослідник кодів"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM дані"

msgid "layout.index.header.nav.home"
msgstr "Головна сторінка"

msgid "layout.index.header.nav.annassoftware"
msgstr "Аннине програмне забезпечення ↗"

msgid "layout.index.header.nav.translate"
msgstr "Перекладати ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Увійти / Зареєструватися"

msgid "layout.index.header.nav.account"
msgstr "Обліковий запис"

msgid "layout.index.footer.list1.header"
msgstr "Архів Анни"

msgid "layout.index.footer.list2.header"
msgstr "Контакти"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / Закон про авторське право"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Розширений"

msgid "layout.index.header.nav.security"
msgstr "Безпека"

msgid "layout.index.footer.list3.header"
msgstr "Альтернативи"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "не афілійований"

msgid "page.search.results.issues"
msgstr "❌ Цей файл може бути проблемним."

msgid "page.search.results.fast_download"
msgstr "Швидке завантаження"

msgid "page.donate.copy"
msgstr "скопіювати"

msgid "page.donate.copied"
msgstr "скопійовано!"

msgid "page.search.pagination.prev"
msgstr "Попередня"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Наступна"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Дзеркало №%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Сабреддіт"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% письмового людського насліддя збережено назавжди %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Бази даних ▶ Файли ▶MD5-суми: %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Завантажити книгу/файл %(extension)s безкоштовно з:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr ""

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library - анонімне дзеркало №%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Підтримати проєкт"

#~ msgid "page.donate.header"
#~ msgstr "Підтримати проєкт"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive – це неприбутковий проєкт, що підтримується волонтерами. Донації допомагають нам покрити витрати на хостинг, доменні імена, розробку та інші ситуаційні розходи."

#~ msgid "page.donate.text2"
#~ msgstr "З вашою допомогою ми продовжуємо обслуговувати сайт, додавати нові функції та зберігати більше книжкових колекцій."

#~ msgid "page.donate.text3"
#~ msgstr "Останні донації: %(donations)s. Ми дуже вдячні вам за вашу щедрість та довіру, в якій би сумі вона не виражалася."

#~ msgid "page.donate.text4"
#~ msgstr "Щоб задонатити, оберіть один з методів нижче. Якщо у вас виникли якісь проблеми, напишіть нам на %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Банківська картка"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Криптовалюти"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Часті питання"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Пройдіть за %(link_open_tag)sпосиланням</a> та дотримуйтеся інструкцій, що будуть доступні за QR-кодом чи посиланням. Якщо щось не спрацювало, спробуйте оновити сторінку – це може переключити вас на інший акаунт."

#~ msgid "page.donate.cc.header"
#~ msgstr "Банківська картка"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Ми співпрацюємо із Sendwyre для конвертації вашого платежу у Bitcoin (BTC). Операція зачислення може зайняти до п'яти хвилин."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Мінімальна сума транзакції цим методом складає $30 та матиме комісію у розмірі приблизно $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Наступні кроки:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Скопіюйте нашу Bitcoin-адресу: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Перейдіть за %(link_open_tag)sпосиланням</a> та натисніть на \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Вставте нашу Bitcoin-адресу та дотримуйтеся подальших інструкцій"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Криптовалюти"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(також працює для BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "%(link_open_tag)sНадішліть гроші на акаунт нижче. Якщо щось не працює, спробуйте оновити сторінку – це може дати вам іншу адресу для оплати.</a>"

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Цей варіант платежу тимчасово не працює. Проте дякуємо за ваш інтерес!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.header"
#~ msgstr "Часті питання"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "Ціль проєкту <span class=\"italic font-bold\">Anna's Archive</span> – створити каталог всіх книжок на планеті шляхом об'єднання даних з різних джерел. Також ми відслідковуємо кількість книжок, доступних у цифровій формі за допомогою їх наявності у \"<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">тіньових бібліотеках</a>\". Дізнайтеся більше про нас <a href=\"/about\">тут.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Книга (категорію не визначено)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Головна"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN-код: %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Бази даних ▶ Бази ISBN ▶ ISBN-код: %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Не знайдено"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "\"%(isbn_input)s\" не є правильним ISBN. ISBN-коди містять 10-13 символів та, опціонально, дефіси. Всі символи, крім останнього мають бути цифрами. Останній символ може бути англійською літерою X та є \"контрольним символом\", тобто контрольною сумою попередніх символів. Він також має бути у проміжку символів, затвердженому International ISBN Agency."

#~ msgid "page.isbn.results.text"
#~ msgstr "Результати з нашої бази даних:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Файлів з даною назвою не знайдено."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Пошук ▶ %(num)d+ результатів по запиту <span class=\"italic\">%(search_input)s</span> знайдено в метаданих тіньових бібліотек"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Пошук ▶ %(num)d результатів по запиту <span class=\"italic\">%(search_input)s</span> знайдено в метаданих тіньових бібліотек"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Пошук ▶ Помилка пошуку для <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Пошук ▶ Шукати нові книги"

#~ msgid "page.donate.header.text3"
#~ msgstr "Ви також можете зробити внесок без створення облікового запису:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Одноразовий внесок (без жодних переваг)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Виберіть спосіб оплати. Будь ласка, розгляньте можливість використання криптовалютного платежу %(bitcoin_icon)s, тому що це значно зменшує комісійний збір."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Якщо у вас вже є криптовалюта, ось наші адреси."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Велике вам дякую за допомогу! Без вас цей проєкт не був би можливий."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Щоб зробити внесок через PayPal (США), ми будемо використовувати PayPal Crypto, який дозволяє нам залишатися анонімними. Ми вдячні вам за те, що ви знайшли час, аби навчитися робити внески за допомогою цього методу, оскільки це дуже допомагає нам."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Слідуйте вказівкам, аби купити Bitcoin (BTC). Вам потрібно купити лише ту кількість, на яку ви хочете зробити внесок."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Якщо ви втратили частину Bitcoin через коливання курсу або комісії, <em>не хвилюйтеся</em>. Це нормально для криптовалюти, проте це дозволяє нам працювати анонімно."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Введіть нашу Bitcoin (BTC) адресу в розділі \"Одержувач\" і слідуйте інструкціям, аби надіслати ваш внесок:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Будь ласка, використовуйте <a %(a_account)s>цей рахунок Alipay</a>, щоб надіслати ваш внесок."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Будь ласка, використовуйте <a %(a_account)s>цей рахунок Pix</a>, щоб надіслати ваш внесок."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Якщо зручний для вас метод оплати не підтримується, ми рекомендуємо завантажити <a href=\"https://paypal.com/\">PayPal</a> або <a href=\"https://coinbase.com/\">Coinbase</a> на ваш смартфон, купити Bitcoin в ньому та надіслати на нашу адресу (%(address)s). В більшості країн повний процес налаштування має зайняти не більше ніж декілька хвилин."

#~ msgid "page.search.results.error.text"
#~ msgstr "Спробуйте <a href=\"javascript:location.reload()\">перезавантажити</a> сторінку. Якщо проблема не вирішилася, повідомте нас у <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> чи <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Щоб стати учасником, будь ласка, <a href=\"/login\">Увійдіть або зареєструйтесь</a>. Якщо ви не бажаєте створювати обліковий запис, виберіть \"Зробити одноразовий анонімний внесок\" вище. Дякуємо за вашу підтримку!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Головна"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Про нас"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Зробити благодійний внесок"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Бази даних"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Мобільний додаток"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Блог Anna"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Програмне забезпечення Anna"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Перекласти"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s торрентів"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Відзеркалює %(libraries)s, та інші."

#~ msgid "page.home.preservation.text"
#~ msgstr "Ми зберігаємо в одному місці книги, статті, комікси, журнали та багато іншого, збираючи ці матеріали з різних <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">тіньових бібліотек</a>. Уся ця інформація зберігається назавжди, оскільки її легко дублювати у великій кількості, що призводить до створення численних копій по всьому світу. Таке широке розповсюдження в поєднанні з відкритим вихідним кодом також робить наш веб-сайт стійким до спроб закриття. Дізнайтеся більше про <a href=\"/datasets\">наші бази даних</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Бази даних ▶ Бази DOI ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Нічого не знайдено"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" не є правильним DOI-кодом. Він повинен починатися на \"10\" та мати хоча б одну скісну риску."

#~ msgid "page.doi.box.header"
#~ msgstr "DOI-код:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Посилання на DOI: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Потенційна локація файлу: %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Результати з нашої бази даних:"

#~ msgid "page.doi.results.none"
#~ msgstr "Файлів з даною назвою не знайдено."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Швидкі завантаження</strong> У вас закінчилися швидкі завантаження на сьогодні. Будь ласка, зв'яжіться з Анною за адресою %(email)s , якщо ви зацікавлені в покращенні вашої підписки."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Ваш ліміт швидких завантажень на сьогодні вичерпано. Зв'яжіться з Анною за адресою %(email)s, якщо ви зацікавлені в покращенні вашої підписки."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Чи можу я вам ще якось допомогти?</div> Так! Подивіться нашу <a href=\"/about\">інформаційну сторінку</a>, розділ \"Як допомогти\"."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Мені не подобається, що ви \"монетизуєте\" Архів Анни!</div> Якщо вам не подобається, як ми керуємо нашим проєктом- створіть свою власну тіньову бібліотеку! Весь наш код і дані є у відкритому доступі, тож вас ніщо не зупиняє ;)"

#~ msgid "page.request.title"
#~ msgstr "Зробити запит на книги"

#~ msgid "page.request.text1"
#~ msgstr "Наразі, будь ласка, залишайте ваші запити на електронні книги на форумі <a %(a_forum)s>Libgen.rs</a>. Ви можете створити обліковий запис і написати в одній з цих тем:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Для електронних книг використовуйте <a %(a_ebook)s>цю тему</a>.</li><li %(li_item)s> Для книг, які не доступні в електронному форматі, використовуйте <a %(a_regular)s>цю тему</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "В обох випадках обов'язково дотримуйтесь правил зазначених і цих темах."

#~ msgid "page.upload.title"
#~ msgstr "Надіслати файл"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr "Великі завантаження"

#~ msgid "page.about.title"
#~ msgstr "Про проєкт"

#~ msgid "page.about.header"
#~ msgstr "Про проєкт"

#~ msgid "page.home.search.header"
#~ msgstr "Пошук"

#~ msgid "page.home.search.intro"
#~ msgstr "Пошук у нашому каталозі тіньових бібліотек."

#~ msgid "page.home.random_book.header"
#~ msgstr "Книга Навмання"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Перейти до книги вибраної навмання з каталогу."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Книга Навмання"

#~ msgid "page.about.text1"
#~ msgstr "Архів Анни - це некомерційна пошукова система з відкритим вихідним кодом для “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">тіньових бібліотек</a>”. Вона була створена <a href=\"http://annas-blog.org\">Анною</a>, яка відчула потребу в центральному місці для пошуку книг, паперів, коміксів, журналів та інших документів."

#~ msgid "page.about.text4"
#~ msgstr "Якщо у вас є обґрунтована скарга за законом DMCA (закон про авторське право), подивіться внизу цієї сторінки або зв'яжіться з нами за адресою %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Популярні книги"

#~ msgid "page.home.explore.intro"
#~ msgstr "Цей список містить популярні книги та книги що становлять велику цінність для тіньових бібліотек та цифрового збереження."

#~ msgid "page.wechat.header"
#~ msgstr "Неофіційний WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "У нас є неофіційна сторінка у WeChat, яку веде один з членів спільноти. Використовуйте код нижче, щоб отримати доступ."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Про нас"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Мобільний додаток"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Неофіційний WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Зробити запит на книгу"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Надіслати файли"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Допомога проєктові"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Переказати загальну суму %(total)s використовуючи <a %(a_account)s>цей Alipay-рахунок"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Крім того, ви можете завантажити на Z-Library <a %(a_upload)s>тут</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Щоб підвищити стійкість \"Архіву Анни\", ми шукаємо волонтерів для роботи з дзеркалами. <a href=\"/mirrors\">Дізнатись більше…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Дзеркала: набір волонтерів"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "тільки цього місяця!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>призупинив</a> завантаження нових статей."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Виберіть спосіб оплати. Ми надаємо знижки на криптовалютні платежі %(bitcoin_icon)s, тому що це значно зменшує для нас комісійний збір."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Виберіть спосіб оплати. Наразі ми приймаємо лише криптовалютні платежі %(bitcoin_icon)s, оскільки традиційні платіжні системи відмовляються з нами працювати."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Ми не можемо підтримувати кредитні/дебетові картки безпосередньо, тому що банки не хочуть з нами працювати. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Однак, є кілька способів використання кредитних/дебетових карток через наші інші способи оплати:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Повільні та сторонні завантаження"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Завантаження"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Якщо ви користуєтесь криптовалютою вперше, ми пропонуємо використати %(option1)s, %(option2)s, чи %(option3)s щоб придбати та задонатити Біткоін (найпоширеніша криптовалюта)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 посилань на записи, які ви покращили."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 посилань або скріншотів."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 посилань або скріншотів виконаних запитів."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Якщо ви зацікавлені в дзеркалюванні цих Datasets для <a %(a_faq)s>архівування</a> або <a %(a_llm)s>навчання LLM</a>, будь ласка, зв'яжіться з нами."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Якщо ви зацікавлені у дзеркалюванні цього набору даних для <a %(a_archival)s>архівування</a> або <a %(a_llm)s>навчання LLM</a>, будь ласка, зв'яжіться з нами."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Головний вебсайт"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Інформація про країни ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Якщо ви зацікавлені в дзеркалюванні цього набору даних для <a %(a_archival)s>архівування</a> або <a %(a_llm)s>навчання LLM</a>, будь ласка, зв'яжіться з нами."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Міжнародне агентство ISBN регулярно випускає діапазони, які воно виділило національним агентствам ISBN. З цього ми можемо визначити, до якої країни, регіону або мовної групи належить цей ISBN. Наразі ми використовуємо ці дані опосередковано, через бібліотеку Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ресурси"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Останнє оновлення: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Вебсайт ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Метадані"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Виключаючи «scimag»"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Наше натхнення для збору метаданих — це мета Аарона Шварца “одна веб-сторінка для кожної книги, яка коли-небудь була опублікована”, для якої він створив <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Цей проєкт добре справляється, але наша унікальна позиція дозволяє нам отримувати метадані, які вони не можуть."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Ще одним натхненням було наше бажання дізнатися <a %(a_blog)s>скільки книг існує у світі</a>, щоб ми могли підрахувати, скільки книг нам ще потрібно зберегти."

#~ msgid "page.partner_download.text1"
#~ msgstr "Щоб дати всім можливість безкоштовно завантажувати файли, вам потрібно почекати <strong>%(wait_seconds)s секунд</strong>, перш ніж ви зможете завантажити цей файл."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Автоматично оновлювати сторінку. Якщо ви пропустите вікно завантаження, таймер перезапуститься, тому автоматичне оновлення рекомендується."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Завантажити зараз"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Конвертувати: використовуйте онлайн-інструменти для конвертації між форматами. Наприклад, для конвертації між epub і pdf використовуйте <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: завантажте файл (підтримуються pdf або epub), потім <a %(a_kindle)s>надішліть його на Kindle</a> за допомогою вебу, додатку або електронної пошти. Корисні інструменти: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Підтримайте авторів: якщо вам подобається і ви можете собі це дозволити, подумайте про те, щоб купити оригінал або підтримати авторів напряму."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Підтримуйте бібліотеки: якщо така книга є у вашій місцевій бібліотеці, візьміть її там безкоштовно."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Недоступні безпосередньо в масовому порядку, лише в напівмасовому за платним доступом"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Архів Анни керує колекцією <a %(isbndb)s>метаданих ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb — це компанія, яка збирає метадані ISBN з різних онлайн-книгарень. Архів Анни робить резервні копії метаданих книг ISBNdb. Ці метадані доступні через Архів Анни (хоча наразі не в пошуку, за винятком випадків, коли ви явно шукаєте номер ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Для технічних деталей дивіться нижче. З часом ми можемо використовувати це для визначення, яких книг ще не вистачає в тіньових бібліотеках, щоб пріоритезувати, які книги знайти та/або відсканувати."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Наш блог-пост про ці дані"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Збір даних ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Наразі у нас є єдиний торрент, який містить 4,4 ГБ стиснений файл <a %(a_jsonl)s>JSON Lines</a> (20 ГБ у розпакованому вигляді): «isbndb_2022_09.jsonl.gz». Щоб імпортувати файл «.jsonl» у PostgreSQL, ви можете використовувати щось на кшталт <a %(a_script)s>цього скрипта</a>. Ви навіть можете передавати його безпосередньо, використовуючи щось на кшталт %(example_code)s, щоб він розпаковувався на льоту."

#~ msgid "page.donate.wait"
#~ msgstr "Будь ласка, зачекайте хоча б <span %(span_hours)s>дві години</span> (та оновіть цю сторінку), перш ніж зв'язуватися з нами."

#~ msgid "page.codes.search_archive"
#~ msgstr "Шукати в Архіві Анни “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Донатьте використовуючи Alipay or WeChat. Ви можете обрати між ними на наступній сторінці."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Поширення інформації про Архів Анни в соціальних мережах та онлайн-форумах, рекомендуючи книги або списки на AA, або відповідаючи на запитання."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Колекція фікшн розійшлася, але все ще має <a %(libgenli)s>торренти</a>, хоча не оновлювалася з 2022 року (у нас є прямі завантаження)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Архів Анни та Libgen.li спільно керують колекціями <a %(comics)s>коміксів</a> та <a %(magazines)s>журналів</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Немає торрентів для колекцій російської художньої літератури та стандартних документів."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Немає доступних торрентів для додаткового контенту. Торренти, які є на сайті Libgen.li, є дзеркалами інших торрентів, перелічених тут. Єдиним винятком є торренти художньої літератури, починаючи з %(fiction_starting_point)s. Торренти коміксів та журналів випускаються у співпраці між Архівом Анни та Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "З колекції <a %(a_href)s><q>Бібліотека Олександріна,</q></a> точне походження невідоме. Частково з the-eye.eu, частково з інших джерел."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

