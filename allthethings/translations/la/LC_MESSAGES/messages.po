#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Petitum invalidum. Visita %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "Libgen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "Open Library"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " et "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "et plura"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nos speculum %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Nos radimus et aperimus %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Omnis noster codex et data sunt omnino aperta."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Maxima vere aperta bibliotheca in historia humana."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libri, %(paper_count)s&nbsp;articuli — conservati in perpetuum."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Maxima bibliotheca apertae fontis et apertae datae in mundo. ⭐️&nbsp;Specula Sci-Hub, Library Genesis, Z-Library, et plura. 📈&nbsp;%(book_any)s libri, %(journal_article)s articuli, %(book_comic)s comici, %(magazine)s ephemerides — conservati in perpetuum."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Maxima bibliotheca apertae fontis et apertae datae in mundo.<br>⭐️ Specula Sci-Hub, Libgen, Z-Lib, et plura."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata incorrecta (e.g. titulus, descriptio, imago operculi)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemata de download (e.g. coniungere non potest, nuntius erroris, valde tardus)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Fasciculus aperiri non potest (e.g. fasciculus corruptus, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Qualitas pauper (e.g. quaestiones formatae, qualitas scan pauper, paginae desunt)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fasciculus removendus est (e.g. publicitas, contentus abusus)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamatio iuris auctoris"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Aliud"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Downloads boni"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Librorum Lector Lucidus"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Bibliothecarius Beatus"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Data Collector Splendidus"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Archivarius Admirabilis"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s totales"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totales"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "non solutum"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "solutum"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "revocatum"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "expletum"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "exspectans confirmationem Annae"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "invalidum"

#, fuzzy
msgid "page.donate.title"
msgstr "Dona"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Habes <a %(a_donation)s>donationem exsistentem</a> in progressu. Quaeso perficias aut revoces illam donationem antequam novam donationem facias."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Omnes donationes meas vide</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Archivum Annae est projectus non lucrativus, aperti codicis, apertorum datorum. Donando et socius factus, operationes nostras et progressionem sustines. Omnibus sociis nostris: gratias agimus quod nos sustinetis! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Pro pluribus informationibus, consule <a %(a_donate)s>Donationes FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Ut plures downloads accipias, <a %(a_refer)s>amicos tuos refer</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Accipis %(percentage)s%% celeres downloads boni, quia a usore %(profile_link)s refereris."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Hoc valet per totum tempus societatis."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s celeres downloads per diem"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "si hoc mense donaveris!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mense"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Adscribe"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selectum"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "usque ad %(percentage)s%% infracta"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB scripta <strong>illimitata</strong> sine verificatione"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> accessus"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Merere <strong>%(percentage)s%% bonus downloads</strong> per <a %(a_refer)s>amicos referendo</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Tuum usoris nomen vel anonymum mentio in creditis"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Praemia priora, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Praematurus accessus ad novas functiones"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Exclusivus Telegram cum post-scenicas renovationes"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Torrentem adoptare”: tuum usoris nomen vel nuntius in nomine torrentis <div %(div_months)s>semel omni 12 mensibus sodalitatis</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legenda status in conservatione scientiae et culturae humanitatis"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Peritus Accessus"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "continge nos"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Parva turma voluntariorum sumus. Potest nos 1-2 septimanas respondere."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Illimitatus</strong> accessus altae velocitatis"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Directi <strong>SFTP</strong> servers"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donatio vel commutatio ad novam collectionem (e.g. nova scans, OCR’ed datasets) in gradu enterprise."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Magnas donationes ab opulentis individuis vel institutis excipimus. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pro donationibus supra $5000 quaeso directe nos continge ad %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Scito te dum membra in hac pagina sunt \"per mensem\", sunt donationes unice (non recurrentes). Vide <a %(faq)s>Donation FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Si vis donationem facere (quolibet quantitate) sine sodalitate, libenter utere hoc Monero (XMR) inscriptio: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Quaeso selige methodum solutionis."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporarily unavailable)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s doni charta"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Charta bancaria (utens app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Credit/debit card"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regularis)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Charta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Creditum/debitum/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Charta bancaria"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Credit/debit card (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Credit/debit card 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Cum crypto donare potes utendo BTC, ETH, XMR, et SOL. Utere hac optionem si iam cryptocurrency usus es."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Cum crypto donare potes utendo BTC, ETH, XMR, et plus."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si primum crypto uteris, suademus utendo %(options)s ad emendum et donandum Bitcoin (originalis et maxime usitata cryptocurrency)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Ad donandum utendo PayPal US, PayPal Crypto utimur, quod nobis anonymis manere permittit. Grati sumus quod tempus sumis ut discas quomodo hac methodo donare, quoniam multum nobis adiuvat."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donare utendo PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Dona utendo Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si habes Cash App, hoc est facillimus modus donandi!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Nota quod pro transactionibus infra %(amount)s, Cash App potest exigere %(fee)s mercedem. Pro %(amount)s vel supra, est gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Dona per Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Si habes Revolut, hoc est facillimus modus donandi!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Dona cum charta creditaria vel debitoria."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay et Apple Pay quoque operari possunt."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Nota quod pro parvis donationibus chartae creditariae mercedibus nostram %(discount)s%% remissionem tollere possunt, itaque longiores subscriptiones commendamus."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Nota quod pro parvis donationibus mercedes altae sunt, itaque longiores subscriptiones commendamus."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Cum Binance, emes Bitcoin cum charta creditaria/debitoria vel ratione bancaria, et deinde donabis illud Bitcoin nobis. Ita possumus manere securi et anonymi cum donationem tuam accipimus."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance praesto est in fere omni terra, et sustinet plerasque chartas creditarias/debitorias et rationes bancarias. Hoc est nunc nostra principalis commendatio. Grati sumus quod tempus sumis ut discas quomodo donare per hunc modum, quia multum nos adiuvat."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Dona per rationem tuam regularem PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Dona utendo charta creditaria/debitoria, PayPal, vel Venmo. Potes eligere inter haec in pagina proxima."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Dona utendo Amazon doni charta."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Nota quod necesse est rotundare ad summas acceptas a nostris venditoribus (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTISSIMUM:</strong> Solum Amazon.com sustinemus, non alias Amazon paginas. Exempli gratia, .de, .co.uk, .ca, NON sustinentur."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>MAXIME: </strong> Haec optio est pro %(amazon)s. Si alium locum Amazonis uti vis, elige eum supra."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Hic modus utitur cryptocurrency provisore ut conversionem intermediam. Hoc potest esse paulo confusum, itaque quaeso tantum utere hoc modo si alii modi solutionis non operantur. Etiam non operatur in omnibus terris."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona utens charta crediti/debiti, per app Alipay (facillime instituere)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installa app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installa app Alipay ex <a %(a_app_store)s>Apple App Store</a> vel <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Adscribe utens numerum telephonicum tuum."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nulla ulterior personalis notitia requiritur."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Addere chartam bancariam"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Sustentata: Visa, MasterCard, JCB, Diners Club et Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Vide <a %(a_alipay)s>hoc ducem</a> pro pluribus informationibus."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Non possumus directe chartas creditas/debitas sustinere, quia ripae nobiscum operari nolunt. ☹ Tamen, sunt plures modi utaris chartis creditis/debitis, per alias solutiones:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Donum Chartula Amazonica"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Mitte nobis Amazon.com doni chartas utendo tua charta creditaria/debitoria."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay chartas creditas/debitas internationales sustinet. Vide <a %(a_alipay)s>hunc ducem</a> pro pluribus informationibus."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) sustinet internationales chartas creditarias/debitorias. In app WeChat, vade ad “Me => Services => Wallet => Add a Card”. Si id non vides, enable id utendo “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Potes emere crypto utendo chartis creditariis/debitariis."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Crypto express servitia"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Express servitia sunt commoda, sed altiores sumptus habent."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Hoc uti potes loco crypto commutationis si celeriter maiorem donationem facere vis et non curas de feodo $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Certe mitte exactam quantitatem crypto in pagina donationis ostensam, non quantitatem in $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Alioquin feodus subtrahetur et non possumus automatice processum tuam membrationem."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s secundum patriam, nulla verificatio pro prima transactione)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, nulla verificatio pro prima transactione)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, nulla verificatio pro prima transactione)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si qua ex his informationibus obsoleta est, quaeso nobis per electronicam epistulam notifica."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Pro chartis crediticiis, chartis debitoriis, Apple Pay, et Google Pay, utimur “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In eorum systemate, una “cafea” aequivalet $5, ita donatio tua ad proximam multiplicitatem 5 rotundabitur."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Selige quamdiu subscribere vis."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mensis"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 menses"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 menses"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 menses"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 menses"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 menses"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 menses"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>post <span %(span_discount)s></span> deductiones</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Haec ratio solutionis minimum %(amount)s requirit. Quaeso selige aliam durationem vel rationem solutionis."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Dona"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Haec ratio solutionis maximum %(amount)s permittit. Quaeso selige aliam durationem vel rationem solutionis."

#, fuzzy
msgid "page.donate.login2"
msgstr "Ut membrum fias, quaeso <a %(a_login)s>Intra vel Registrare</a>. Gratias pro tuo auxilio!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Selige nummum crypto dilectum:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(minimum quantitas minima)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(utendum cum mittendo Ethereum ex Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(monitio: alta quantitas minima)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Preme puga donationis ut hanc donationem confirmes."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Dona <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Potes adhuc donationem in checkout cancellare."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Ad paginam donationis redirigitur…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Aliquid erravit. Pagina iterum onera et rursus conare."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mense"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "per 1 mensem"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "per 3 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "per 6 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "per 12 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "per 24 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "per 48 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "per 96 menses"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "per 1 mensem “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "per 3 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "per 6 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "per 12 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "per 24 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "per 48 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "per 96 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donatio"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Dies: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Summa: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mensem per %(duration)s menses, includens %(discounts)s%% remissio)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Summa: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mensem per %(duration)s menses)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifier: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Cancella"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Certene vis cancellare? Noli cancellare si iam solvis."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Ita, quaeso cancella"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Donatio tua cancellata est."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Fac novam donationem"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Aliquid erravit. Quaeso reload paginam et iterum tenta."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reordina"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Iam solvis. Si vis inspicere instructiones solutionis, hic preme:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Monstra veteres solutionis instructiones"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Gratias tibi pro donatione tua!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Si nondum fecisti, scribe clavem secretam ad logandum:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Aliter potes excludi ex hoc rationem!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Instructiones solutionis nunc obsoletae sunt. Si vis facere aliam donationem, utere puga \"Reordina\" supra."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota magna:</strong> Pretia crypto valde variare possunt, interdum etiam usque ad 20%% in paucis minutis. Hoc tamen minus est quam sumptus quos incurrimus cum multis solutionis provisoribus, qui saepe 50-60%% exigunt pro operando cum \"umbra caritate\" sicut nos. <u>Si nobis mittere potes receptum cum pretio originali quod solvis, adhuc rationem tuam pro electo sodalitate</u> accredemus (dummodo receptum non sit vetustior quam paucae horae). Valde gratum habemus quod paratus es talia tolerare ut nos sustineas! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Haec donatio exspiravit. Quaeso cancella et novam crea."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Crypto instructiones"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfere ad unum ex nostris rationibus crypto"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Dona summam %(total)s ad unum ex his inscriptionibus:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Emere Bitcoin in Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Invenias paginam “Crypto” in tua app vel website PayPal. Haec plerumque sub “Finances” est."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Sequere instructiones ad emendum Bitcoin (BTC). Tantum emere debes quantitatem quam donare vis, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfere Bitcoin ad nostram inscriptionem"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "I ad paginam “Bitcoin” in tua app vel website PayPal. Preme “Transfer” puga %(transfer_icon)s, et deinde “Send”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Inscribe nostram inscriptionem Bitcoin (BTC) ut recipientem, et sequere instructiones ad mittendam donationem tuam %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instructiones pro credit / debit card"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona per nostram paginam credit / debit card"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Dona %(amount)s in <a %(a_page)s>hac pagina</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Vide gradatim ducem infra."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Exspectans confirmationem (refice paginam ad reprimendum)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Exspectans translationem (refice paginam ad reprimendum)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tempus reliquum:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(forsitan vis cancelare et novam donationem creare)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Ad resetandum timer, simpliciter novam donationem crea."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Status renovare"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Si quas difficultates habes, quaeso contactum nos apud %(email)s et quam plurimas informationes includas (ut screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Si iam solvis:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Interdum confirmatio usque ad 24 horas capere potest, ita certus esto ut hanc paginam reficias (etiam si exspiravit)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Emere nummum PYUSD in PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Sequere instructiones ut emere nummum PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Emere paulo plus (commendamus %(more)s plus) quam quantitatem quam donas (%(amount)s), ut transactionis feoda operias. Quidquid reliquum est, retinebis."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "I ad paginam “PYUSD” in app vel website PayPal. Preme puga “Transfer” %(icon)s, deinde “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transfere %(amount)s ad %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Emere Bitcoin (BTC) in App Pecuniae"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Ad paginam \"Bitcoin\" (BTC) in App Pecuniae vade."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Emere paulo plus (commendamus %(more)s plus) quam quantitatem quam donas (%(amount)s), ut transactionis fees operias. Quidquid superest retinebis."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfer Bitcoin ad nostram inscriptionem"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Preme \"Mitte bitcoin\" puga ut \"retractationem\" facias. Ex dollaris ad BTC commuta premendo %(icon)s iconem. Intra quantitatem BTC infra et preme \"Mitte\". Vide <a %(help_video)s>hoc video</a> si haeres."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Pro parvis donationibus (infra $25), fortasse debes uti Rush vel Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Emere Bitcoin (BTC) in Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Ad paginam \"Crypto\" in Revolut vade ut Bitcoin (BTC) emas."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Emere paulo plus (commendamus %(more)s plus) quam quantitatem quam donas (%(amount)s), ut transactionis fees operias. Quidquid superest retinebis."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfer Bitcoin ad nostram inscriptionem"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Preme \"Mitte bitcoin\" puga ut \"retractationem\" facias. Ex euronibus ad BTC commuta premendo %(icon)s iconem. Intra quantitatem BTC infra et preme \"Mitte\". Vide <a %(help_video)s>hoc video</a> si haeres."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Certe utaris quantitate BTC infra, <em>NON</em> euronibus aut dollaris, alioquin non accipiemus quantitatem rectam et non possumus automatice confirmare tuam sodalitatem."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Pro parvis donationibus (infra $25) fortasse debes uti Rush vel Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Utere aliquo ex sequentibus \"credit card to Bitcoin\" expressis servitiis, quae tantum pauca minuta capiunt:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Haec singula in forma imple:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin quantitas:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Quaeso utere hoc <span %(underline)s>exacto importo</span>. Sumptus tuus totalis altior esse potest propter chartae creditae commissiones. Pro parvis quantitatibus hoc potest esse plus quam nostra remissio, proh dolor."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin inscriptio (externum loculum):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instructiones"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Solum versionem standardam nummorum crypto sustinemus, non exoticas retia vel versiones nummorum. Confirmatio transactionis usque ad horam capere potest, secundum nummum."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR code ut stipendium"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan hoc QR code cum Crypto Wallet App ad cito imple in mercedem Details"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon doni tessera"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Utere <a %(a_form)s>officiali forma Amazon.com</a> ut nobis doni tesseram %(amount)s ad electronicam inscriptionem infra mittas."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Non possumus alias methodos doni tesserae accipere, <strong>solum directe missas ex forma officiali in Amazon.com</strong>. Non possumus tuam doni tesseram reddere si hanc formam non uteris."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Intra exactam quantitatem: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Noli tuum nuntium scribere."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“In” recipiens email in forma:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unicum tuo rationi, noli communicare."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Semel tantum utere."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Exspectans doni tesseram… (paginae refice ut inspicias)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Postquam tuam doni tesseram misisti, systema automaticum nostrum eam intra paucos minutos confirmabit. Si hoc non operatur, tenta iterum doni tesseram mittere (<a %(a_instr)s>instructiones</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Si id adhuc non operatur, quaeso nobis email mitte et Anna eam manualiter recognoscet (hoc paucos dies capere potest), et certus esto mentionem facere si iam iterum mittere conatus es."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Exemplum:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Nota quod nomen rationis vel imago mirum videri potest. Non opus est sollicitari! Hae rationes a nostris donationis sociis administrantur. Rationes nostrae non sunt hackatae."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay instructiones"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donare per Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Dona totam summam %(total)s utendo <a %(a_account)s>hoc Alipay ratione</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Si pagina donationis impeditur, conare aliam connexionem interretialem (e.g. VPN aut interrete telephonicum)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Infeliciter, pagina Alipay saepe tantum accessibilis est ex <strong>continente Sinensi</strong>. Fortasse necesse erit VPN tuum temporarie disable, aut VPN ad continentem Sinam uti (vel interdum Hong Kong etiam operatur)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Fac donationem (scana QR codicem vel preme puga)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Aperi <a %(a_href)s>QR-codicis donationis paginam</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scana QR codicem cum app Alipay, vel preme puga ut app Alipay aperias."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Quaeso patientiam habe; pagina aliquantulum temporis capere potest ut oneret, cum in Sinis sit."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat instructiones"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donare per WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Dona totam summam %(total)s utendo <a %(a_account)s>hoc WeChat ratione</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix instructiones"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donare per Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Dona totam summam %(total)s utendo <a %(a_account)s>hoc Pix ratione"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Mittite nobis receptum"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Mitte acceptilam vel imaginem ad tuam verificationis personalem inscriptionem. NOLI hanc inscriptionem electronicam pro tua donatione PayPal uti."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Mitte receptum vel imaginem ad personalem verificationis inscriptionem:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Si crypto commutatio rate fluctuavit durante transactione, certus esto includere receptum ostendentem originalem commutationis ratem. Valde gratum habemus te molestiam sumere ut crypto utaris, multum nobis adiuvat!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Cum receptum tuum misisti, preme hunc puga, ut Anna manualiter id recognoscat (hoc paucos dies capere potest):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Ita, receptum meum misi"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Gratias tibi pro donatione tua! Anna manualiter activabit tuam sodalitatem intra paucos dies."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Aliquid erravit. Paginae reload et iterum tenta."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Dux gradatim"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Nonnulli gradus mentionem faciunt crypto marsupia, sed noli solliciti esse, nihil de crypto discere debes pro hoc."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Intra tuam inscriptionem electronicam."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Selige modum solutionis tuae."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selige modum solutionis tuae iterum."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Selige “Self-hosted” marsupium."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Clicca “Confirmo possessionem”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Recipies epistulam electronicam. Mitte eam nobis, et donationem tuam quam primum confirmabimus."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Exspecta saltem <span %(span_hours)s>24 horas</span> (et hanc paginam refrica) antequam nos contactes."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Si errorem fecisti in solutione, non possumus refundere, sed conabimur id rectificare."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Donationes meae"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donationum singularia publice non ostenduntur."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nondum donationes. <a %(a_donate)s>Fac primam donationem meam.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fac aliam donationem."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Files recepti"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Downloads ex Fast Partner Servers notantur per %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Si filem cum utrisque celeribus et tardis downloads recepisti, bis apparebit."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Celeres downloads in ultimis 24 horis ad terminum cotidianum pertinent."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Omnes tempora sunt in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Files recepti publice non ostenduntur."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nondum files recepti."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ultimae 18 horae"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Prius"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Rationem"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Intra / Registrare"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Rationis ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profilum publicum: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clavis secreta (noli communicare!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "monstra"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Sodalitas: <strong>%(tier_name)s</strong> usque ad %(until_date)s <a %(a_extend)s>(extendere)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Sodalitas: <strong>Nihil</strong> <a %(a_become)s>(socius fies)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Celeres descensiones usae (ultimas 24 horas): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "quae descensiones?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Exclusivus grex Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Nobis iunge hic!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Ad gradum <a %(a_tier)s>superiorem</a> promove ut nostro grege iungas."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contactum habe cum Anna apud %(email)s si interesse habes ut sodalitatem tuam ad gradum superiorem promoveas."

#, fuzzy
msgid "page.contact.title"
msgstr "Contactus inscriptio electronica"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Potes plures sodalitates coniungere (celeres descensiones per 24 horas simul addentur)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Publicum profile"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Descensa files"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Meae donationes"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Exire"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Nunc es exitus. Pagina reonera ut iterum introeas."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Aliquid erravit. Pagina reonera et iterum tenta."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registratio felix! Tua clavis secreta est: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Hanc clavem diligenter serva. Si eam perdas, accessum ad rationem tuam amittes."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Notatio.</strong> Potes hanc paginam notare ut clavem tuam recipias.</li><li %(li_item)s><strong>Descensio.</strong> Clicca <a %(a_download)s>hunc nexum</a> ut clavem tuam descendas.</li><li %(li_item)s><strong>Gestor tesserae.</strong> Utere gestore tesserae ut clavem servas cum eam infra inseras.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Inserere clavem tuam secretam ut introeas:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clavis secreta"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Intra"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clavis secreta invalida. Clavem tuam verifica et iterum tenta, aut novum rationem infra registra."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Clavem tuam ne perdas!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Rationem nondum habes?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Novam rationem registra"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Si clavem tuam amisisti, quaeso <a %(a_contact)s>nobis contactum</a> et quam plurimas informationes praebe."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Fortasse necesse erit temporariam rationem creare ut nobis contactum."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vetus ratio per email? Intra tuum <a %(a_open)s>email hic</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Index"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "edita"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Serva"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Servatum. Quaeso paginam recarga."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Aliquid erravit. Quaeso iterum tenta."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Index per %(by)s, creatus <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Index vacuus est."

#, fuzzy
msgid "page.list.new_item"
msgstr "Adde aut remove ex hoc indice inveniendo fasciculum et aperiendo tabulam “Indices”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profilum"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilum non inventum."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "edita"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Nomen tuum muta. Identificator tuus (pars post “#”) mutari non potest."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Serva"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Servatum est. Paginam iterum onera."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Aliquid erravit. Iterum tenta."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profilum creatum <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Indices"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Nulli indices adhuc"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Novum indicem crea inveniendi fasciculum et aperiendi tabulam “Indices”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reformatio iuris auctoris necessaria est pro securitate nationali"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Sinenses LLMs (incluso DeepSeek) in meo illegali archivio librorum et chartarum — maximo in mundo — exercitantur. Occidens legem iuris auctoris reformare debet pro securitate nationali."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articuli comites a TorrentFreak: <a %(torrentfreak)s>primus</a>, <a %(torrentfreak_2)s>secundus</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Non ita pridem, “bibliothecae umbraticae” moriebantur. Sci-Hub, ingens illegalis archivum chartarum academicarum, novas opera recipere desierat, ob lites. “Z-Library”, maxima illegalis bibliotheca librorum, creatores suos in criminibus iuris auctoris comprehensos vidit. Mirabiliter evaserunt comprehensionem, sed bibliotheca eorum non minus sub minatione est."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Cum Z-Library clausuram minaretur, totam eius bibliothecam iam servaveram et quaerebam suggestum ad eam collocandam. Hoc erat motivum meum ad inchoandum Archivum Annae: continuatio missionis post illas priores incepta. Ex illo tempore crevimus ut maxima bibliotheca umbratica in mundo essemus, plus quam 140 miliones textuum iuribus auctoris protectorum in variis formatis — libros, chartas academicas, ephemerides, diurna, et ultra — hospitantes."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Ego et turma mea sumus ideologi. Credimus quod conservare et hospitari hos fasciculos moraliter rectum est. Bibliothecae per orbem terrarum vident reductiones in fundis, et non possumus confidere hereditatem humanitatis societatibus."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Deinde venit AI. Fere omnes maiores societates LLMs aedificantes nos contigerunt ut in nostris datis se exercerent. Plurimae (sed non omnes!) societates in US fundatae retractaverunt cum intellexerunt illegalem naturam operis nostri. E contra, societates Sinenses nostra collectione alacriter usi sunt, apparenter non turbati de eius legalitate. Hoc notabile est, cum Sina sit signataria fere omnium maiorum tractatuum internationalium de iure auctorum."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Accessum celerem dedimus circiter 30 societatibus. Plurimae earum sunt societates LLM, et nonnullae sunt mediatores datorum, qui nostram collectionem revendent. Plurimae sunt Sinenses, quamquam etiam cum societatibus ex US, Europa, Russia, Corea Meridiana, et Iaponia laboravimus. DeepSeek <a %(arxiv)s>admisit</a> quod prior versio in parte nostrae collectionis exercitata est, quamquam de suo recentissimo exemplo taciti sunt (probabiliter etiam in nostris datis exercitata)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si Occidens vult praecedere in cursu LLMs, et tandem, AGI, necesse est suam positionem de iure auctorum reconsiderare, et cito. Sive nobiscum consentis sive non de nostra causa morali, hoc nunc fit causa oeconomica, et etiam securitatis nationalis. Omnes potentiae blocae aedificant artificiales super-scientistas, super-hackers, et super-milites. Libertas informationis fit res salutis pro his nationibus — etiam res securitatis nationalis."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Turma nostra ex toto orbe terrarum est, et non habemus particularis alligantiam. Sed hortaremur nationes cum legibus de iure auctorum fortibus ut hoc periculum existentiale ad eas reformandas utantur. Quid ergo faciendum est?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Prima nostra commendatio est simplex: terminum iuris auctorum breviare. In US, ius auctorum conceditur per 70 annos post mortem auctoris. Hoc absurdum est. Possumus hoc adducere in lineam cum patentibus, quae conceduntur per 20 annos post submissionem. Hoc debet esse plus quam satis temporis pro auctoribus librorum, chartarum, musicae, artis, et aliorum operum creativorum, ut plene compensentur pro suis conatibus (inclusis diuturnis propositis ut adaptationes cinematographicae)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Deinde, saltem, legislatores debent includere exceptiones pro massiva conservatione et disseminatione textuum. Si amissa reditus ex singulis clientibus est praecipua cura, distributio ad personam prohiberi posset. Vicissim, ii qui capaces sunt gestire ingentes repositoria — societates LLMs exercitantes, una cum bibliothecis et aliis archivis — his exceptionibus tegerentur."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Nonnullae nationes iam versionem huius faciunt. TorrentFreak <a %(torrentfreak)s>retulit</a> quod Sina et Iaponia exceptiones AI suis legibus de iure auctorum introduxerunt. Nobis non liquet quomodo hoc cum tractatibus internationalibus interagant, sed certe praebet tegumentum suis societatibus domesticis, quod explicat quid viderimus."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quod ad Archivum Annae pertinet — pergamus nostrum subterraneum opus radicatum in morali convictione. Tamen maximum votum nostrum est ut in lucem veniamus, et nostrum impactum legaliter amplificemus. Quaeso reformate ius auctorum."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lege articulos comitantes a TorrentFreak: <a %(torrentfreak)s>primus</a>, <a %(torrentfreak_2)s>secundus</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Victores praemii visualizationis ISBN $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Habemus incredibiles submissiones ad praemium visualizationis ISBN $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Paucis mensibus ante nuntiavimus <a %(all_isbns)s>praemium $10,000</a> ad optimam visualizationem nostrorum datorum ostendentem spatium ISBN faciendam. Emphasizavimus ostendere quae fasciculi iam archivavimus, et postea dataset describens quot bibliothecae ISBNs tenent (mensura raritatis)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Responsione oppressi sumus. Tantum fuit creativitatis. Magnum gratias omnibus qui participaverunt: vestra energia et studium sunt contagiosa!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Ultimo, voluimus respondere sequentibus quaestionibus: <strong>quae libri in mundo existunt, quot iam archivavimus, et quibus libris debemus deinceps intendere?</strong> Iucundum est videre tot homines curare de his quaestionibus."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Nosmet ipsi cum simplici visualizatione incepimus. Minus quam 300kb, haec imago succincte repraesentat maximam plenam apertam “indicem librorum” umquam in historia humanitatis collectam:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Omnes ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Tabellae in Archivum Annae"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC data leak"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Bibliotheca Publica Russica"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Bibliotheca Imperialis Trantoris"

#, fuzzy
msgid "common.back"
msgstr "Retro"

#, fuzzy
msgid "common.forward"
msgstr "Progredi"

#, fuzzy
msgid "common.last"
msgstr "Ultimus"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Quaeso vide <a %(all_isbns)s>originalem blog post</a> pro pluribus informationibus."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Provocavimus provocationem ad hoc emendandum. Primum locum praemium $6,000, secundum locum $3,000, et tertium locum $1,000 tribueremus. Propter responsionem ingentem et submissiones incredibiles, decrevimus leviter augere praemiorum summam, et quattuor tertia loca $500 singulis tribuere. Victores infra sunt, sed certissime omnes submissiones <a %(annas_archive)s>hic</a> inspicite, aut nostrum <a %(a_2025_01_isbn_visualization_files)s>torrentem coniunctum</a> deponite."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primum locum $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Haec <a %(phiresky_github)s>submissio</a> (<a %(annas_archive_note_2951)s>commentarium Gitlab</a>) est simpliciter omnia quae volebamus, et plus! Praesertim placuerunt nobis optiones visualizationis incredibiliter flexibiles (etiam shaders proprios sustinentes), sed cum comprehensivo indice praesetorum. Etiam placuit quam celeriter et leniter omnia sunt, simplex exsecutio (quae ne backend quidem habet), callida minimappa, et ampla explicatio in eorum <a %(phiresky_github)s>blog post</a>. Opus incredibile, et bene meritus victor!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Secundum locum $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Alia incredibilis <a %(annas_archive_note_2913)s>submissio</a>. Non tam flexibilis quam primus locus, sed re vera eius visualizationem macro-livelli praeferimus super primum locum (curva spatii implens, limites, titulus, illustratio, panning, et zooming). A <a %(annas_archive_note_2971)s>commentario</a> a Joe Davis nobis resonavit:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Dum quadrata et rectangula perfecta mathematicam voluptatem praebent, non superiorem localitatem in contextu mappae praebent. Credo asymmetriam in his Hilbert vel classicis Morton non esse vitium sed proprietatem. Sicut Italiae outline calceamenti formae famosum eam in mappa statim recognoscibilem facit, singulares \"peculiaritates\" harum curvarum possunt ut cognitivi termini servire. Haec distinctio memoriam spatialem augere potest et adiuvare utentes ad se orientandos, potentia faciens locare regiones specificas vel notare exemplaria facilius.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Et adhuc multae optiones ad visualizandum et reddendum, necnon incredibiliter lenis et intuitiva UI. Solida secunda locus!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tertium locum $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In hac <a %(annas_archive_note_2940)s>submissione</a> valde placuerunt nobis variae visiones, praesertim comparationis et editoris visiones."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tertium locum $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Dum non est politissima UI, haec <a %(annas_archive_note_2917)s>submissio</a> multas exspectationes implet. Praesertim placuit nobis eius comparationis proprietas."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tertium locum $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Sicut primus locus, haec <a %(annas_archive_note_2975)s>submissio</a> nos cum sua flexibilitate impressit. Ultimo hoc est quod magnum instrumentum visualizationis facit: maxima flexibilitas pro potentibus usoribus, dum res simplices pro mediocribus usoribus servat."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tertium locum $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Ultima <a %(annas_archive_note_2947)s>submissio</a> quae praemium accipit est satis simplex, sed habet quasdam proprietates unicas quae nobis valde placuerunt. Placuit nobis quomodo ostendunt quot datasets particularem ISBN tegant ut mensura popularitatis/affidabilitatis. Etiam valde placuit nobis simplicitas sed efficacia usus opacitatis labii pro comparationibus."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Notabiles ideae"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Aliquae plures ideae et exsecutiones quae nobis praesertim placuerunt:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Caelum scrapers pro raritate"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistica viva"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotationes, et etiam statistica viva"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unicum tabulae visum et filtra"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Frigidum colorum schema et caloris tabula."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facilis alternatio datasetorum ad celeres comparationes."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Pulchrae tituli."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Scala cum numero librorum."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Multae labellae ad comparanda datasets, quasi DJ sis."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Possumus diutius pergere, sed hic sistamus. Certo inspice omnes submissiones <a %(annas_archive)s>hic</a>, aut deprime nostrum <a %(a_2025_01_isbn_visualization_files)s>torrentem coniunctum</a>. Tot submissiones, et unaquaeque singularem prospectum affert, sive in UI sive in exsecutione."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Saltem primam submissionem in nostrum principale locum incorporabimus, et fortasse alias. Coepimus etiam cogitare quomodo processum disponere possimus ad libros rarissimos identificandos, confirmandos, et deinde archiviandos. Plus in hoc fronte venturum est."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Gratias omnibus qui participaverunt. Mirum est quod tot homines curant."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Corda nostra gratitudine plena sunt."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizatio Omnium ISBNs — $10,000 praemium per 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Haec imago maximam plenam apertam “librorum indicem” in historia humanitatis umquam collectam repraesentat."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Haec imago est 1000×800 pixelorum. Unusquisque pixel 2,500 ISBNs repraesentat. Si fasciculum pro ISBN habemus, illum pixel viridiorem facimus. Si scimus ISBN emissum esse, sed fasciculum congruentem non habemus, illum rubicundum facimus."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In minus quam 300kb, haec imago breviter maximam plenam apertam “librorum indicem” in historia humanitatis umquam collectam repraesentat (pauca centena GB compressa in pleno)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Etiam ostendit: multum laboris restat in libris servandis (solum 16% habemus)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Background"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Quomodo Archiva Annae suam missionem ad omnem humanitatis scientiam servandam assequi potest, sine sciendo qui libri adhuc exstant? Opus est nobis TODO indice. Unus modus ad hoc mappandum est per ISBN numeros, qui ab annis 1970 singulis libris editis (in plerisque nationibus) assignati sunt."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nulla est auctoritas centralis quae omnes ISBN assignationes novit. Immo, est systema distributum, ubi nationes numerorum intervalla accipiunt, quae deinde maioribus editoribus minora intervalla assignant, qui fortasse ulterius intervalla minoribus editoribus dividunt. Tandem singuli numeri libris assignantur."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Coepimus ISBNs mappare <a %(blog)s>duobus annis ante</a> cum nostra scrutatio ISBNdb. Ex illo tempore, multos alios metadata fontes scrutati sumus, ut <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, et plures. Plena index inveniri potest in paginis “Datasets” et “Torrents” in Archiva Annae. Nunc longe maximam plenam apertam, facilem ad deponendum collectionem metadata librorum (et sic ISBNs) in mundo habemus."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Scripsimus <a %(blog)s>late</a> de causa cur de conservatione curamus, et cur nunc in fenestra critica simus. Nunc libros rariores, minus notatos, et singulariter in periculo positos cognoscere et conservare debemus. Habere bonam metadata omnium librorum in mundo adiuvat ad hoc."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizatio"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Praeter imaginem overview, possumus etiam spectare ad singula Datasets quae acquisivimus. Utere dropdown et bullis ad inter eos mutandum."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Multae sunt figurae iucundae in his imaginibus videndae. Cur aliqua regularitas linearum et quadratorum, quae in diversis scalis fieri videtur? Quae sunt areae vacuae? Cur quaedam Datasets ita coacervata sunt? Has quaestiones lectori exercitio relinquimus."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 praemium"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Multum est hic explorandum, itaque praemium pro melioratione visualizationis supra annuntiamus. Dissimile plerisque praemiorum nostrorum, hoc temporis limite definitur. Codicem tuum apertum submittere debes ante diem 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Optima submissio $6,000 accipiet, secunda locus $3,000, et tertia locus $1,000. Omnia praemia Monero (XMR) erunt."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Infra sunt criteria minima. Si nulla submissio criteria occurrit, adhuc aliqua praemia dare possumus, sed id arbitrio nostro erit."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Hunc repositorium forca, et hunc blog post HTML edita (nulla alia backenda praeter nostrum Flask backend permittuntur)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Fac imaginem supra leniter zoomabilem, ut possis zoomare usque ad singula ISBNs. Cliccando ISBNs te ad paginam metadata vel quaerere in Annae Archivum ducat."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Adhuc potes inter omnes Datasets diversos mutare."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Rangia nationum et editorum in hover evidenter ostendi debent. Potes uti e.g. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> pro informatione nationum, et nostram “isbngrp” scrutare pro editoribus (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Bene operari debet in desktop et mobili."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pro punctis bonus (haec sunt tantum ideae — sinite vestram creativitatem effrenari):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Magna consideratio dabitur usabilitati et quam bene spectat."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Ostende actualem metadata pro singulis ISBNs cum zoomando, sicut titulum et auctorem."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Melior curva spatii replens. E.g. zig-zag, eundo a 0 ad 4 in prima linea et deinde retro (inverso) a 5 ad 9 in secunda linea — recursive applicata."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Diversae vel customizable schemata colorum."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Visus speciales pro comparandis Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modi ad debug quaestiones, sicut alia metadata quae bene non conveniunt (e.g. tituli valde diversi)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotare imagines cum commentariis de ISBNs vel intervallis."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Quaelibet heuristica ad libros rarios vel in periculo agnoscendos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Quaelibet ideae creativae quas excogitare potes!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "POTES omnino a minimis criteriis discedere, et omnino diversam visualizationem facere. Si vere spectabilis est, tunc id pro praemio qualificat, sed ad nostrum arbitrium."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fac submissiones per commentarium ponendo ad <a %(annas_archive)s>hoc quaestionem</a> cum nexu ad tuum forcatum repositorium, merge petitionem, vel differentiam."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Codex"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Codex ad has imagines generandas, necnon alia exempla, inveniri potest in <a %(annas_archive)s>hoc directorio</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Formatum datae compactum excogitavimus, quo omnis necessaria informatio ISBN circiter 75MB (compressa) est. Descriptio formati datae et codex ad id generandum inveniri potest <a %(annas_archive_l1244_1319)s>hic</a>. Pro praemio non requiritur ut hoc utaris, sed probabiliter est formatus commodissimus ad incipiendum. Potes metadata nostra quomodolibet transformare (quamquam omnis codex tuus apertus esse debet)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Non possumus exspectare quid excogitaveris. Bonam fortunam!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annae Archivum Containera (AAC): emissiones ex maxima bibliotheca umbrae mundi standardizans"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annae Archivum facta est maxima bibliotheca umbrae in mundo, nos requirens ut emissiones nostras standardizemus."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annae Archivum</a> facta est longe maxima bibliotheca umbrae in mundo, et sola bibliotheca umbrae huius magnitudinis quae omnino aperta est et aperta data. Infra est tabula ex nostra pagina Datasets (paulum modificata):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Hoc tribus modis perfecimus:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Specula bibliothecarum umbrae apertae datae existentium (sicut Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Adiuvando bibliothecas umbrae quae magis apertae esse volunt, sed tempus vel facultates ad id faciendum non habebant (sicut collectio comicorum Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scrapando bibliothecas quae nolunt in mole communicare (sicut Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Pro (2) et (3) nunc collectionem torrentium considerabilem ipsi administramus (100s TBs). Hactenus has collectiones ut singulares tractavimus, significans infrastructuram et organizationem datae propriam pro singulis collectionibus. Hoc significativum onus ad singulas emissiones addit, et praesertim difficile facit emissiones incrementales facere."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Ideo decrevimus emissiones nostras standardizare. Hoc est blogum technicum in quo nostrum standardum introducimus: <strong>Annae Archivum Containera</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Metas designandi"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Usus noster principalis est distributio fasciculorum et metadatae associatae ex diversis collectionibus existentibus. Considerationes nostrae maximi momenti sunt:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Fasciculi et metadata heterogenei, quam proxime ad formam originalem."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatores heterogenei in bibliothecis fontis, vel etiam absentia identifierum."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separationes metadatae et fasciculorum, vel solum metadatae (e.g. nostra emissio ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distributio per torrents, cum possibilitate aliorum modorum distributionis (e.g. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Recorda immutabilia, cum assumamus nostros torrents perpetuo victuros esse."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Emissiones incrementales / emissiones appendibiles."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Machinae legibilis et scribibilis, commode et celeriter, praesertim pro nostra catasta (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Facilis inspectio humana, quamvis haec secundaria sit ad machinae legibilitatem."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile est nostras collectiones seminare cum standardi locato seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Data binaria directe a webservers sicut Nginx serviri possunt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Non-objecit quaedam:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Non curamus de fasciculis facile navigandis manualiter in disco, vel quaerendis sine praeprocessu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Non curamus de compatibilitate directa cum software bibliothecae existentis."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Dum facile sit cuiquam nostram collectionem seminare per torrents, non expectamus fasciculos usui esse sine notabili scientia technica et dedicatione."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Cum Annae Archivum sit apertum fontem, volumus nostram formam directe uti. Cum indicem quaestionis nostram renovamus, solum vias publice praesto accessimus, ut quilibet qui nostram bibliothecam derivat celeriter incipere possit."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standardum"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Denique, in standardum relative simplicem consedimus. Est satis laxum, non-normativum, et opus in progressu."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Annae Archivum Contenitor) est unum item constans ex <strong>metadata</strong>, et optative <strong>data binaria</strong>, quae utraque immutabilia sunt. Habet identifier globaliter unicum, vocatum <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collectio.</strong> Quaelibet AAC ad collectionem pertinet, quae per definitionem est index AACs semantice consistentium. Id significat si mutationem significantem in forma metadatae facis, tunc novam collectionem creare debes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” et “files” collectiones.</strong> Ex more, saepe convenit “records” et “files” ut diversas collectiones emittere, ut possint diversis temporibus emittere, exempli gratia, secundum rates scraping. “Record” est collectio solum metadata, continens informationes sicut titulos librorum, auctores, ISBNs, etc., dum “files” sunt collectiones quae actualia files ipsas continent (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Forma AACID est haec: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Exempli gratia, actualis AACID quem emisimus est <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: nomen collectionis, quod potest continere litteras ASCII, numeros, et underscores (sed non duplices underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: brevis versio ISO 8601, semper in UTC, exempli gratia <code>20220723T194746Z</code>. Hic numerus debet crescere monotone pro omni emissione, quamquam eius exacta semantica potest differre per collectionem. Suademus utendo tempore scraping vel generandi ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: identifier specificus collectioni, si applicabile, exempli gratia Z-Library ID. Potest omitti vel truncari. Debet omitti vel truncari si AACID aliter excederet 150 characteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID sed compressus ad ASCII, exempli gratia utendo base57. Nunc utimur <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python bibliotheca."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID range.</strong> Cum AACIDs continere timestamps crescentes monotone, possumus uti hoc ad denotandum intervalla intra collectionem particularem. Uti sumus hoc forma: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, ubi timestamps sunt inclusiva. Hoc est consistent cum notatione ISO 8601. Intervalla sunt continua, et possunt superponi, sed in casu superpositionis debent continere identica records sicut illa prius emissa in illa collectione (cum AACs sint immutabiles). Desiderata records non permittuntur."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata file.</strong> Metadata file continet metadata intervalli AACs, pro una collectione particulari. Haec habent sequentes proprietates:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Nomen fasciculi debet esse intervallum AACID, praefixum cum <code style=\"color: red\">annas_archive_meta__</code> et sequitur <code>.jsonl.zstd</code>. Exempli gratia, una ex nostris emissionibus vocatur<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Ut indicatur per extensionem fasciculi, typus fasciculi est <a %(jsonlines)s>JSON Lines</a> compressus cum <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Quisque JSON objectum debet continere sequentes campos in summo gradu: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). Nulli alii campi permittuntur."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> est metadata arbitraria, secundum semantica collectionis. Debet esse semantice consistent intra collectionem."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> est optionalis, et est nomen folder datae binariae quae continet data binaria correspondentia. Nomen fasciculi datae binariae correspondentis intra illam folder est AACID recordi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Praefixum <code style=\"color: red\">annas_archive_meta__</code> potest adaptari ad nomen institutionis tuae, exempli gratia <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binary data folder.</strong> Folder cum data binaria intervalli AACs, pro una collectione particulari. Haec habent sequentes proprietates:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Nomen directorii debet esse intervallum AACID, praefixum cum <code style=\"color: green\">annas_archive_data__</code>, et nullum suffixum. Exempli gratia, una ex nostris actualibus emissionibus habet directorium vocatum<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Directorium debet continere fasciculos datae pro omnibus AACs intra intervallum specificatum. Quisque fasciculus datae debet habere suum AACID ut nomen fasciculi (nullae extensiones)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Commendatur ut haec folder sint aliquantum administrabilia in magnitudine, exempli gratia non maiores quam 100GB-1TB singulae, quamquam haec commendatio potest mutare per tempus."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Metadata files et folder datae binariae possunt conglobari in torrents, cum uno torrent per metadata file, vel uno torrent per folder datae binariae. Torrents debent habere originale nomen fasciculi/directorii plus <code>.torrent</code> suffixum ut nomen fasciculi."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplum"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Exemplum recentis emissionis Z-Library inspiciamus. Constat ex duabus collectionibus: “<span style=\"background: #fffaa3\">zlib3_records</span>” et “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Hoc nobis permittit ut metadata records a librorum actualium fasciculis separatim colligamus et emittamus. Itaque, duo torrents cum metadata fasciculis emisimus:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Etiam fasciculos cum data binaria in “<span style=\"background: #ffd6fe\">zlib3_files</span>” collectione, 62 in summa, emisimus:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Currendo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> videre possumus quid intus sit:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In hoc casu, metadata libri ut a Z-Library relatum est. In summo gradu tantum habemus “aacid” et “metadata”, sed non “data_folder”, quia nulla data binaria correspondet. AACID continet “22430000” ut ID principale, quod videmus ex “zlibrary_id” sumptum esse. Exspectare possumus alios AACs in hac collectione eandem structuram habere."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nunc curramus <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Hoc est multo minor AAC metadata, quamquam massa huius AAC alibi in archivo binario sita est! Post omnes, habemus “data_folder” hoc tempore, ita expectare possumus data binaria correspondentia sita esse ad <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” continet “zlibrary_id”, ita facile possumus eam cum AAC correspondente in collectione “zlib_records” associare. Potuimus associare multis modis diversis, e.g. per AACID — norma id non praescribit."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nota quod etiam non necesse est ut “metadata” ager ipse JSON sit. Potest esse stringa continens XML vel aliquem alium data formatum. Potes etiam metadata informationem in globo binario associato condere, e.g. si multum data est."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusio"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Cum hac norma, possumus emissiones magis incrementales facere, et facilius novas fontes datae addere. Iam habemus nonnullas emissiones excitantes in pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Speramus etiam facilius fieri ut aliae bibliothecae umbrae collectiones nostras speculentur. Post omnes, propositum nostrum est humanam scientiam et culturam in perpetuum conservare, ita quo plus redundantiae, eo melius."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annae Renovatio: plene aperta fons archivum, ElasticSearch, 300GB+ librorum opercula"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Laboramus sine intermissione ut bonam alternativam cum Annae Archivo praebeamus. Hic sunt quaedam quae nuper perfecimus."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Cum Z-Bibliotheca deiecta sit et eius (allegati) fundatores comprehensi sint, laboramus sine intermissione ut bonam alternativam cum Annae Archivo praebeamus (non hic coniungemus, sed potes id Googlere). Hic sunt quaedam quae nuper perfecimus."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annae Archivum plene aperta fons est"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Credimus informationem liberam esse debere, et nostrum codicem non esse exceptionem. Omnem nostrum codicem in nostra privata Gitlab instantia edidimus: <a %(annas_archive)s>Annae Software</a>. Etiam utimur indice quaestionum ad opus nostrum ordinandum. Si vis cum nostro progressu coniungere, hic est locus optimus ad incipiendum."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Ut gustum tibi praebeamus rerum quas agimus, accipe nostrum recentem laborem in clientis parte perficiendi meliorationibus. Cum paginationem nondum impleverimus, saepe longas paginas quaerendi reddebamus, cum 100-200 eventibus. Nolumus quaerendi eventus nimis cito intercludere, sed hoc significabat quod aliquando tardabat quaedam machinas. Ad hoc, parvum dolum implevimus: plerosque quaerendi eventus in HTML commentariis involvimus (<code><!-- --></code>), et deinde parvum Javascript scripsimus quod deprehenderet quando eventus visibilis fieret, quo momento commentarium evolveremus:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualization\" in 23 lineis impletum, nulla necessitas ad elegantia bibliothecas! Hoc genus celeris pragmaticae codicis est quod habes cum tempus limitatum est, et vera problemata quae solvi debent. Nuntiatum est quaerendi nunc bene operari in machinis tardis!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Alius magnus conatus fuit ad automatice construendum database. Cum incepimus, diversas fontes temere coniunximus. Nunc volumus eas renovatas servare, itaque scripta scripsimus ad novum metadata ex duobus Library Genesis forcipibus detrahendum, et eas integramus. Propositum est non solum hoc utile facere pro nostro archivo, sed etiam facilem facere pro quolibet qui metadata bibliothecae umbrae explorare vult. Propositum esset Jupyter notebook quod omne genus metadata interesting praesto haberet, ut plus investigationis facere possimus sicut determinare quid <a %(blog)s>procentum ISBNs in perpetuum conservetur</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Denique, systema donationis nostram renovavimus. Nunc potes uti chartam creditam ad directe pecuniam in nostras crypto marsupia deponere, sine re vera aliquid de cryptocurrency scire. Observabimus quomodo hoc in praxi bene operetur, sed hoc magnum est."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Transitus ad ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Unus ex nostris <a %(annas_archive)s>tesseris</a> erat saccus quaestionum cum nostro quaerendi systemate. Usus sumus MySQL quaerendi plenam-textum, cum omnes data nostra in MySQL habuimus. Sed habuit limites suos:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Quaedam quaestiones super longum tempus ceperunt, ad punctum ubi omnes apertae coniunctiones occupabant."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Per defaltam MySQL habet minimum longitudinem verbi, aut index tuus potest vere magnus fieri. Relatum est homines non posse quaerere \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Quaerendi erat tantum aliquantulum celeris cum plene in memoria oneratus, quod requirebat nos ad machinam pretiosiorem ad hoc currendum, plus aliquas iussiones ad index in initio praeonustum."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Non potuissemus facile extendere ad novas functiones aedificandas, sicut melius <a %(wikipedia_cjk_characters)s>tokenizationem pro linguis non-spatiatis</a>, filtrum/faceting, ordinatio, \"did you mean\" suggestiones, autocomplete, et cetera."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Postquam cum multis peritis locuti sumus, in ElasticSearch consedimus. Non fuit perfectum (eorum defalta \"did you mean\" suggestiones et autocomplete functiones male se habent), sed in summa melius fuit quam MySQL pro quaerendo. Non sumus adhuc <a %(youtube)s>nimis studiosi</a> utendum pro ullis missionibus criticis (quamquam multum <a %(elastic_co)s>progressus</a> fecerunt), sed in summa satis laeti sumus cum transitu."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Pro nunc, multo celerius quaerendi, melius linguarum subsidium, melius relevantiae ordinatio, diversae ordinationis optiones, et filtrum in lingua/genus libri/genus file implevimus. Si curiosus es quomodo operatur, <a %(annas_archive_l140)s>inspice</a> <a %(annas_archive_l1115)s>hoc</a> <a %(annas_archive_l1635)s>aspectum</a>. Satis accessibile est, quamquam aliquot commentariis plus uti posset…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ librorum opercula edita"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Denique, laeti sumus nuntiare parvam editionem. In cooperatione cum hominibus qui Libgen.rs forcipem operantur, omnes eorum librorum opercula per torrents et IPFS communicamus. Hoc onus videndi opercula inter plures machinas distribuet, et melius ea conservabit. In multis (sed non omnibus) casibus, librorum opercula in ipsis file inclusa sunt, itaque hoc genus \"data derivata\" est. Sed habere in IPFS adhuc valde utile est pro operatione cotidiana tam Annae Archivum quam variis Library Genesis forcipibus."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Ut solet, hanc editionem invenire potes apud Speculum Bibliothecae Piratae (EDIT: translatum ad <a %(wikipedia_annas_archive)s>Archivum Annae</a>). Hic non coniungemus, sed facile invenire potes."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Speramus nos paulum relaxare posse, nunc cum decentem Z-Bibliothecae alternativam habemus. Hoc onus non est praecipue sustinendum. Si interesse habes in adiuvando cum programmatibus, operationibus servientibus, vel operibus conservationis, certe nos contingas. Multum adhuc <a %(annas_archive)s>operis faciendum est</a>. Gratias pro tuo studio et auxilio."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Archivum Annae maximam bibliothecam umbraticam comicorum mundi (95TB) sublevavit — potes adiuvare ad eam disseminandam"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Maxima bibliotheca umbratica librorum comicorum in mundo unum punctum defectus habuit... usque ad hodie."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Disputare in Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Maxima bibliotheca umbratica librorum comicorum probabiliter est illa cuiusdam forci Library Genesis: Libgen.li. Unus administrator qui illum locum curat, insaniam collectionem comicorum plus quam 2 miliones fasciculorum, totaliter plus quam 95TB, collegit. Tamen, dissimile aliis collectionibus Library Genesis, haec non erat in mole per torrents praesto. Solum poteras hos comicos singillatim per eius servientem personalem tardum accedere — unum punctum defectus. Usque ad hodie!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In hoc post nos tibi plura de hac collectione narrabimus, et de nostra pecunia colligenda ad plus huius operis sustinendum."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon conatur se perdere in mundo communi bibliothecae…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen forci"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primum, aliqua notitia. Fortasse Library Genesis nosti propter eorum epicam collectionem librorum. Pauciores sciunt voluntarios Library Genesis alias incepta creavisse, ut magnam collectionem ephemeridum et documentorum standardium, plenam Sci-Hub subsidium (in cooperatione cum fundatrice Sci-Hub, Alexandra Elbakyan), et quidem, magnam collectionem comicorum."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Aliquando diversi operatores speculorum Library Genesis vias suas separaverunt, quod dedit ortum ad praesentem statum habendi numerum diversorum “forcorum”, omnes adhuc nomen Library Genesis ferentes. Libgen.li forc unice hanc collectionem comicorum habet, necnon magnam collectionem ephemeridum (quam etiam laboramus)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaboratio"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Data magnitudine, haec collectio diu in nostro desiderio fuit, itaque post successum nostrum cum subsidio Z-Bibliothecae, hanc collectionem petivimus. Primo eam directe scrutati sumus, quod erat satis provocatio, cum eorum servus non in optima condicione esset. Hoc modo circa 15TB adepti sumus, sed lente procedebat."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Fortunate, contigimus operatorem bibliothecae, qui consensit ut nobis omnia data directe mitteret, quod multo celerius erat. Plus quam dimidium anni adhuc cepit ut omnia data transferremus et processeremus, et paene omnia amisimus propter corruptionem disci, quod significasset incipere ab initio."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Haec experientia nos fecit credere magni momenti esse ut haec data quam celerrime divulgentur, ut late et late speculentur. Unum vel duo inopportune temporis incidentia a perdendo hanc collectionem in perpetuum sumus!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Collectio"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Celeriter movendo significat collectionem paulum inordinatam esse… Videamus. Imaginare habemus systema fasciculorum (quod re vera per torrents dividimus):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Primum directorium, <code>/repository</code>, est pars magis structa huius. Hoc directorium continet sic dictas “mille dirs”: directorium singula cum mille fasciculis, quae in database incrementale numerantur. Directorium <code>0</code> continet fasciculos cum comic_id 0–999, et sic deinceps."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Hoc idem schema est quod Library Genesis adhibuit pro suis collectionibus fictionis et non-fictionis. Idea est ut omnis “mille dir” automatice vertatur in torrentem simul ac impletur."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tamen, operator Libgen.li numquam fecit torrents pro hac collectione, et sic mille dirs verisimiliter factae sunt incommodae, et dederunt locum \"inordinatis dirs\". Hae sunt <code>/comics0</code> per <code>/comics4</code>. Omnes continent singulares structuras directoriorum, quae probabiliter sensum habuerunt ad colligendas tabulas, sed nunc nobis non multum sensum faciunt. Feliciter, metadata adhuc directe ad omnes has tabulas refert, ita eorum ordo in disco re vera non refert!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata praesto est in forma MySQL database. Hoc directe ex Libgen.li website potest downloadari, sed etiam in torrentem praebebimus, una cum nostra tabula cum omnibus MD5 hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analysis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Cum 95TB in clustro tuo repono effunditur, conaris intellegere quid etiam ibi sit... Nonnullam analysim fecimus ut videremus si magnitudinem paulum minuere possemus, ut duplicata removendo. Hic sunt nonnullae inventiones nostrae:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplicata semantica (diversae scans eiusdem libri) theoretice excludi possunt, sed id est difficile. Cum manualiter per comics inspiceremus, nimis multa falsa positiva invenimus."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Sunt quaedam duplicata pure per MD5, quod relative prodigum est, sed excludendo illa tantum nobis circa 1% in salutem daret. Ad hanc magnitudinem id est adhuc circa 1TB, sed etiam, ad hanc magnitudinem 1TB re vera non refert. Malo non periculum accidere data in hoc processu destruendo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Invenimus fasciculum non-librorum data, ut pelliculas ex comicis libris. Id etiam videtur prodigum, cum haec iam late per alia media praesto sint. Tamen, intelleximus nos non posse simpliciter pellicularum fasciculos excludere, cum etiam sint <em>comici libri interactivi</em> qui in computatro editi sunt, quos aliquis recordatus et servatus est ut pelliculas."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Denique, quidquid ex collectione delere possemus, tantum paucos centesimas servaret. Tum meminimus nos esse datae congerentes, et homines qui hoc speculum facient etiam sunt datae congerentes, et sic, \"QUID SIGNIFICAT, DELE?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Itaque vobis praesentamus plenam, immutatam collectionem. Multum est data, sed speramus satis homines curare ut eam seminare."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Fundraiser"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Hoc data in nonnullis magnis fragmentis dimittimus. Primus torrent est de <code>/comics0</code>, quod in unum ingens 12TB .tar fasciculum posuimus. Id melius est pro tuo duro disco et torrent software quam mille minores fasciculi."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Ut pars huius emissionis, fundraiser facimus. Quaerimus $20,000 colligere ad sumptus operationis et contractus pro hac collectione, necnon ad permittendum continuos et futuros proiectus. Habemus nonnulla <em>ingentia</em> in operibus."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Quem sustineo cum mea donatione?</em> In brevi: omnes scientiam et culturam humanitatis subsidimus, et facilem accessum facimus. Omnis noster codex et data sunt aperta fonte, sumus omnino voluntariis administratus projectus, et servavimus 125TB librorum usque nunc (praeter Libgen et Scihub’s existentes torrents). Denique, rotam volantem aedificamus quae permittit et incitat homines ad inveniendum, scannendum, et omnes libros in mundo servandum. Scribemus de nostro magno consilio in futuro post. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si donas pro 12 mensium \"Amazing Archivist\" sodalitate ($780), potes <strong>“torrentem adoptare”</strong>, significans quod ponemus tuum usoris nomen vel nuntium in nomine fasciculi unius torrentium!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Potes donare per <a %(wikipedia_annas_archive)s>Annae Archivum</a> et premendo \"Donate\" puga. Quaerimus etiam plures voluntarios: programmatores, investigatores securitatis, peritos mercatorum anonymorum, et interpretes. Potes etiam nos sustinere praebendo officia hospitandi. Et sane, quaeso seminare nostros torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Gratias omnibus qui tam generose nos iam sustinuerunt! Vere differentiam facitis."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hic sunt torrents usque nunc dimissi (adhuc reliqua processimus):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Omnes torrents inveni possunt in <a %(wikipedia_annas_archive)s>Annae Archivum</a> sub \"Datasets\" (non directe ibi coniungimus, ut nexus ad hunc blogum non removeantur ex Reddit, Twitter, etc). Inde, sequere nexum ad Tor website."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Quid deinde?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Fasciculus torrentium magnus est ad diuturnam conservationem, sed non tam ad cotidianum accessum. Collaborabimus cum sociis hospitandi ut totum hoc data in interrete ponamus (cum Annae Archivum nihil directe hospitatur). Sane poteris invenire hos nexus download in Annae Archivum."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Nos quoque omnes invitamus ut cum hisce datis agant! Adiuva nos ut melius ea analysare, deduplicare, in IPFS ponere, remiscere, AI exempla tua cum eis instituere, et cetera. Tua sunt omnia, et non possumus exspectare quid cum eis facias."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Denique, ut antea dictum est, adhuc aliquas magnas emissiones habemus (si <em>aliquis</em> nobis <em>fortuito</em> ACS4 database <em>quoddam</em> mittere posset, scitis ubi nos invenire…), necnon rotam volantem aedificantes ad omnes libros mundi servandos."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Itaque manete intenti, modo incipimus."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novi libri additi ad Speculum Bibliothecae Piraticae (+24TB, 3.8 miliones librorum)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In prima emissione Speculi Bibliothecae Piraticae (EDIT: mota ad <a %(wikipedia_annas_archive)s>Annae Archivum</a>), speculum Z-Bibliothecae fecimus, magnae collectionis librorum illegalium. Ut moneamus, hoc scripsimus in illo primo blog post:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Bibliotheca est popularis (et illegalis) bibliotheca. Collectionem Library Genesis ceperunt et facile quaeribilem fecerunt. Insuper, valde efficaces facti sunt in novas contributiones librorum sollicitando, utentes contribuentes variis beneficiis alliciendo. Nunc autem has novas libros ad Library Genesis non referunt. Et dissimile Library Genesis, collectionem suam facile speculabilem non faciunt, quod latam conservationem impedit. Hoc ad eorum negotium exemplar pertinet, cum pecuniam exigant pro accessu ad collectionem suam in mole (plus quam 10 libros per diem)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Non iudicamus moraliter de pecunia exigenda pro accessu in mole ad collectionem librorum illegalium. Indubitanter Z-Bibliotheca in expandendo accessu ad scientiam et in plus librorum fontibus felix fuit. Nos hic sumus ut partem nostram agamus: longum tempus conservationem huius privatae collectionis curare."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Illa collectio ad medium 2021 redibat. Interea, Z-Bibliotheca ad mirabilem celeritatem crevit: circiter 3.8 miliones novorum librorum addiderunt. Sunt ibi quaedam duplicata, certe, sed maior pars videtur esse legitime novi libri, vel scansiones melioris qualitatis librorum prius submissorum. Hoc magna ex parte est propter auctum numerum moderatorum voluntariorum in Z-Bibliotheca, et eorum systema massae-upload cum deduplicatione. Gratulamur eis de hisce rebus gestis."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Laeti nuntiamus nos omnes libros accepisse qui ad Z-Bibliothecam additi sunt inter nostrum ultimum speculum et Augustum 2022. Etiam retrogressi sumus et quosdam libros scrutati sumus quos primo tempore omisimus. Omnino, haec nova collectio est circiter 24TB, quae multo maior est quam ultima (7TB). Nunc speculum nostrum est 31TB in toto. Iterum, deduplicavimus contra Library Genesis, cum iam sint torrents pro illa collectione praesto."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Quaeso ad Speculum Bibliothecae Piraticae accede ut novam collectionem inspicias (EDIT: mota ad <a %(wikipedia_annas_archive)s>Annae Archivum</a>). Ibi plus informationis est de structura fasciculorum, et quid mutatum sit ab ultimo tempore. Non hinc ad eam ligabimus, cum hoc sit tantum blog situs qui nullas materias illegales hospitat."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Certe, seminare etiam magnus modus est ut nos adiuvetis. Gratias omnibus qui priorem nostrum torrentium set seminant. Grati sumus pro positiva responsione, et laeti quod tam multi sunt qui de conservatione scientiae et culturae in hoc insolito modo curant."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Quomodo fieri pirata archivista"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Primum certamen mirum esse potest. Non est problema technicum, nec problema legale. Est problema psychologicum."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antequam incipiamus, duo nuntii de Speculo Bibliothecae Piraticae (EDIT: mota ad <a %(wikipedia_annas_archive)s>Annae Archivum</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Quaedam donationes valde generosae accepimus. Prima fuit $10k ab individuo anonymo qui etiam \"bookwarrior\", originalem fundatorem Library Genesis, sustinuit. Speciales gratiae bookwarrior pro hac donatione facilianda. Secunda fuit alia $10k ab donatore anonymo, qui post nostram ultimam emissionem in contactum venit, et inspiratus est ad adiuvandum. Etiam plures donationes minores habuimus. Gratias maximas pro omni vestra generosa sustentatione. Habemus nonnulla nova excitantia incepta in pipeline quae hoc sustinebit, ita manete intenti."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Difficultates technicas habuimus cum magnitudine nostrae secundae emissionis, sed nostri torrents nunc sursum sunt et seminantur. Etiam generosam oblationem ab individuo anonymo accepimus ut collectionem nostram in suis servientibus altissimae celeritatis seminet, ita specialem upload ad suas machinas facimus, post quod omnes alii qui collectionem nostram deponunt magnam celeritatis emendationem videre debent."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Toti libri scribi possunt de <em>cur</em> de conservatione digitali in genere, et piratica archivistica in specie, sed celerem introductionem damus iis qui non sunt nimis familiares. Mundus plus scientiae et culturae quam umquam antea producit, sed etiam plus eius quam umquam antea amittitur. Humanitas magna ex parte corporibus sicut editores academici, servitia streaming, et societates socialis mediae hoc patrimonium committit, et saepe non probati sunt magni custodes. Inspice documentarium Digital Amnesia, vel revera quemlibet sermonem a Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Sunt quaedam instituta quae bonum opus faciunt archivi quantum possunt, sed lege tenentur. Ut piratae, in singulari positione sumus ad archivi collectiones quas tangere non possunt, propter iura auctorum enforcementem vel alias restrictiones. Possumus etiam collectiones multoties speculare, per totum mundum, ita augentes casus propriae conservationis."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Nunc, non disputabimus de commodis et incommodis proprietatis intellectualis, de moralitate legis violandae, de cogitationibus de censura, vel de quaestione accessus ad scientiam et culturam. His omnibus remotis, in <em>quomodo</em> immergamus. Communicabimus quomodo turma nostra facta sit piratae archivistae, et quae lectiones didicerimus in itinere. Multae sunt provocationes cum hoc iter incipis, et speramus nos te per aliquas earum adiuvare posse."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Communitas"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Prima provocatio fortasse mirabilis erit. Non est problema technicum, nec problema legale. Est problema psychologicum: hoc opus in umbris agere potest incredibiliter solitarium esse. Secundum quid agere cogitas, et tuum periculum, fortasse valde cautus esse debes. In uno fine spectrum habemus personas sicut Alexandra Elbakyan*, fundatrix Sci-Hub, quae de suis actionibus valde aperta est. Sed in magno periculo est capiendi si nunc ad occidentem regionem visitaret, et decennia carceris pati posset. Estne hoc periculum quod suscipere velles? Nos sumus in altero fine spectrum; valde cauti ne ullum vestigium relinquamus, et fortem securitatem operationalem habemus."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Ut in HN a \"ynno\" dictum est, Alexandra initio nesciri volebat: \"Servitores eius erant dispositi ad emittendas nuntios erroris ex PHP, incluso pleno itinere fontis vitiosi, quod erat sub directorio /home/<USER>" Ergo, utere nominibus usoris fortuitis in computatoribus quos ad haec opera uteris, ne quid male configuraveris."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Sed illa secretum, tamen, cum psychologico pretio venit. Plerique amant agnoscere pro opere quod faciunt, et tamen non potes ullum meritum pro hoc in vita reali accipere. Etiam res simplices provocare possunt, sicut amici rogantes quid feceris (aliquando \"cum NAS / homelab meo ludere\" vetus fit)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Hoc est cur tam magni momenti sit aliquam communitatem invenire. Potes aliquam securitatem operationalem remittere confidendo in aliquos amicissimos, quos scis te penitus confidere posse. Etiam tunc cave ne quid in scriptis ponas, ne forte eorum epistulae ad auctoritates tradendae sint, aut si eorum machinae alio modo compromissae sint."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Melius tamen est aliquos piratas socios invenire. Si amici tui propinqui interesse volunt, optime! Aliter, fortasse alios online invenire potes. Triste est quod haec adhuc communitas angusta est. Hactenus paucos alios invenimus qui in hoc spatio activi sunt. Bona initia videntur esse fora Library Genesis, et r/DataHoarder. Turma Archive etiam habet homines similes, quamvis intra legem operentur (etiam si in aliquibus areis legis obscuris). Scenae traditionales \"warez\" et piraticae etiam habent homines qui simili modo cogitant."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Aperimus ad ideas quomodo communitatem fovere et ideas explorare possimus. Libenter nobis nuntium mitte in Twitter vel Reddit. Fortasse aliquod forum vel chat group hospitari possemus. Una provocatio est quod hoc facile censurari potest cum communibus suggestis utimur, ita nos ipsi hospitari deberemus. Est etiam commercium inter habendas has disputationes plene publicas (plus potentialis commercii) versus privatas (ne potentiales \"metas\" sciant nos eos scrutare). De hoc cogitare debemus. Scire nos fac si hoc interesse tibi est!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proiecta"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Cum proiectum facimus, habet aliquot phases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Electio / philosophia domain: Ubi fere vis te intendere, et cur? Quae sunt tuae singularis passiones, artes, et circumstantiae quas ad utilitatem tuam uti potes?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Electio meta: Quam specificam collectionem speculum facies?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Scraping metadata: Catalogatio informationis de fasciculis, sine actu fasciculos (saepe multo maiores) ipsos deponendo."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Electio data: Ex metadata, restringendo quae data maxime pertinent ad archivum nunc. Potest esse omnia, sed saepe est ratio rationabilis ad spatium et latitudinem servandam."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Scraping data: Re vera data obtinens."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distributio: Eam in torrents packaging, alicubi nuntians, homines ad eam diffundendam accipiens."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Hae non sunt omnino independentes phases, et saepe perceptiones ex posteriore phase te ad priorem phase remittunt. Exempli gratia, durante metadata scraping potes animadvertere quod meta quam elegisti habet mechanismos defensivos ultra tuum gradum artis (sicut IP clausuras), ita revertis et aliam metam invenis."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Electio / philosophia domain"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nulla est penuria scientiae et hereditatis culturalis servandae, quae opprimere potest. Ideo saepe utile est momentum capere et cogitare quid tua contributio esse possit."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Omnes aliter de hoc cogitant, sed hic sunt quaestiones quas te ipsum rogare potes:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Cur id tibi hoc interest? Quid te pascit? Si possumus coetum hominum congregare qui omnes res archivant quae ipsis curae sunt, multum operiemus! Multo plus quam mediocris homo de tua passione scies, sicut quae data servanda sint, quae optimae collectiones et communitates online sint, et cetera."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quae artes habes quas ad utilitatem tuam adhibere potes? Exempli gratia, si es peritus securitatis online, vias invenire potes ad vincenda IP claustra pro securis metis. Si es peritus in communitatibus ordinandis, fortasse potes aliquos homines circa propositum congregare. Utilis est scire aliquantulum programmare, etiam si solum ad bonam securitatem operationalem per hunc processum servandam."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quantum temporis ad hoc habes? Consilium nostrum esset ut parva incipias et maiora incepta facias cum id assequaris, sed potest totum consumere."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Quae esset area magni momenti ad intendendum? Si X horas in piratica archivi impendes, quomodo potes maximum \"ictum pro pecunia tua\" obtinere?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quae sunt singulares modi quibus de hoc cogitas? Fortasse habes aliquas ideas vel accessus interessantes quos alii praeterierunt."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In nostro casu, praesertim de diuturna scientiae conservatione curavimus. De Library Genesis sciebamus, et quomodo pluries per torrents plene speculata esset. Illa idea nobis placuit. Tum uno die, unus ex nobis conatus est quaedam scientifica manualia in Library Genesis invenire, sed ea invenire non potuit, dubium faciens quomodo completa revera esset. Tum illa manualia online quaesivimus, et ea in aliis locis invenimus, quod semen pro nostro incepto plantavit. Etiam antequam de Z-Library sciremus, ideam habuimus non conari omnes illos libros manualiter colligere, sed intendere in speculando collectiones existentes, et eas ad Library Genesis contribuere."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Meta electio"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Ita, habemus aream quam spectamus, nunc quam specificam collectionem speculabimur? Sunt pauca quae bonam metam faciunt:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Magna"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unica: non iam bene operta ab aliis inceptis."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessibilis: non utitur multis stratis protectionis ut te impediat a metadata et data eorum radendo."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Specialis perspicacia: habes aliquam specialem informationem de hac meta, sicut quomodo specialem accessum ad hanc collectionem habes, aut quomodo eorum defensionem vincere didicisti. Hoc non requiritur (nostrum inceptum venturum nihil speciale facit), sed certe adiuvat!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Cum nostra scientifica manualia in websites aliis quam Library Genesis invenimus, conati sumus quomodo in interrete pervenerint intellegere. Tum Z-Library invenimus, et intelleximus quod dum plerique libri non primum ibi apparent, tandem ibi finiunt. De eius relatione ad Library Genesis didicimus, et structurae incitamentorum (financialium) et superioris interfaciei usoris, quae utrumque eam multo completiorem collectionem fecerunt. Tum aliquod metadata et data radendi praeliminaris fecimus, et intelleximus nos posse circumire eorum IP limites download, utentes speciali accessu unius ex nostris membris ad multos servitores procuratores."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Dum varias metas exploratis, iam magni momenti est vestigia vestra celare utendo VPNs et email inscriptionibus abiciendis, de quibus postea plura loquemur."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata radendi"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Paulo technicius hic fiamus. Ad metadata ex websites radenda, res satis simplices servavimus. Utimur Python scriptis, interdum curl, et MySQL database ad reponenda eventa. Nullum software radendi elegantem usi sumus quae websites complexas mappare potest, quia adhuc solum opus erat radere unum vel duo genera paginarum per enumerationem per ids et parsing HTML. Si non sunt facile enumeratae paginae, tum fortasse opus est recto crawler qui omnes paginas invenire conatur."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antequam totum website radere incipias, conare id manualiter paulisper facere. Perge per aliquot paginas tu ipse, ut sensum capias quomodo id operetur. Interdum iam in IP claustra vel aliam interesting agendi rationem hoc modo incides. Idem valet pro data radendi: antequam nimis alte in hanc metam ingrediaris, fac ut eius data efficaciter downloadere possis."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Ad circumveniendas restrictiones, sunt pauca quae conari potes. Suntne aliae IP inscriptiones vel servitores qui eadem data hospitantur sed non easdem restrictiones habent? Suntne aliqua API endpoints quae restrictiones non habent, dum aliae habent? Quo rate downloadendi tua IP clauditur, et quamdiu? Aut nonne clauditur sed retardatur? Quid si rationem usoris creas, quomodo res tunc mutantur? Potesne HTTP/2 uti ad coniunctiones apertas servandas, et idne auget ratem qua paginas petere potes? Suntne paginae quae plures fasciculos simul enumerant, et estne informatio ibi sufficiens?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Res quas verisimiliter servare vis includunt:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titulus"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nomen / locus"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: potest esse aliquod internum ID, sed ID sicut ISBN vel DOI sunt etiam utiles."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Magnitudo: ad computandum quantum spatii disci tibi opus sit."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): ad confirmandum quod fasciculum recte detraxisti."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Dies additus/modificatus: ut postea reverti possis et fasciculos detrahere quos antea non detraxisti (quamquam saepe etiam ID vel hash ad hoc uti potes)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descriptio, categoria, tituli, auctores, lingua, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Hoc typice in duobus gradibus facimus. Primum, rudia HTML fascicula detrahimus, plerumque directe in MySQL (ut vitentur multi parvi fasciculi, de quibus infra plura loquimur). Deinde, in separato gradu, per illa HTML fascicula transimus et ea in veras MySQL tabulas resolvimus. Hoc modo non opus est omnia a principio iterum detrahere si errorem in codice tuo resolvendo detegis, cum possis HTML fascicula cum novo codice iterum processare. Facilius etiam saepe est gradum processandi parallelizare, ita tempus servans (et potes codicem processandi scribere dum scrutatio currit, pro eo quod utrumque gradum simul scribere debeas)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Denique, nota quod pro quibusdam metadatis scrutatio omnia quae sunt. Sunt quaedam ingentes metadata collectiones quae non recte conservantur."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Electio datae"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Saepe potes metadata uti ad rationabilem subiectum datae detrahendae determinandum. Etiam si tandem omnem datam detrahere vis, utile esse potest ut res maximi momenti prius praeponas, in casu quod deprehendaris et defensiones meliorentur, vel quia plus disci emere debes, vel simpliciter quia aliquid aliud in vita tua accidit antequam omnia detrahere possis."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Exempli gratia, collectio potest habere plures editiones eiusdem rei (sicut libri vel pelliculae), ubi una notata est ut optima qualitas. Servare illas editiones primum multum sensus faceret. Tandem omnes editiones servare velles, cum in quibusdam casibus metadata male notata esse possit, vel ignotae commutationes inter editiones esse possint (exempli gratia, \"optima editio\" potest esse optima in plerisque modis sed peior in aliis, sicut pellicula habens maiorem resolutionem sed carens subtitulis)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Potes etiam tuam metadata basem quaerere ut res interesantes invenias. Quis est maximus fasciculus qui hospitatur, et cur tam magnus est? Quis est minimus fasciculus? Suntne interestinges vel inopinatae formae cum ad certas categorias, linguas, et cetera venit? Suntne duplicata vel valde similia tituli? Suntne formae ad quando data addita sunt, sicut unus dies in quo multi fasciculi simul additi sunt? Saepe multum discere potes per dataset in diversis modis inspiciendo."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In nostro casu, libros Z-Library contra md5 hashes in Library Genesis deduplicavimus, ita multum temporis detrahendi et spatii disci servantes. Haec est satis singularis condicio tamen. In plerisque casibus non sunt comprehensivae bases datorum quae fasciculi iam recte conservati sunt a sociis piratis. Hoc per se est ingens occasio alicui ibi. Magnificum esset habere regulariter renovatam summam rerum sicut musica et pelliculae quae iam late seminatae sunt in torrentibus locis, et ideo minoris momenti sunt ad includendum in speculis piratarum."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scrutatio datae"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nunc paratus es ad data in mole actu detrahenda. Ut ante dictum est, hoc puncto iam manualiter fasciculos detraxisse debes, ut melius intellegas mores et restrictiones scopi. Tamen, adhuc erunt tibi mirabilia in promptu cum ad multos fasciculos simul detrahendos perveneris."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nostra consilia hic sunt simplicia servare. Incipe a fasciculis detrahendis. Potes Python uti, et deinde ad plures fila expandere. Sed interdum etiam simplicius est Bash fasciculos directe ex base datorum generare, et deinde plures ex eis in plures fenestras terminales currere ad amplitudinem augendam. Celeris technica dolus hic memoranda est usus OUTFILE in MySQL, quod scribere potes ubicumque si \"secure_file_priv\" in mysqld.cnf disabilitas (et certus esto etiam AppArmor disabilitare/overridere si in Linux es)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Data in simplicibus disci duris reponimus. Incipe cum quocumque habes, et lente expande. Potest esse opprimens cogitare de centenis TBs datae reponendis. Si haec est condicio quam tu facis, tantum bonum subiectum primo pone, et in tua annuntiatione auxilium in reponendo reliquum pete. Si plures disci duri teipsum emere vis, tunc r/DataHoarder habet nonnullas bonas facultates de bonis pretiis obtinendis."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Conare non nimis de systematibus fasciculorum elegantibus curare. Facile est in cuniculum incidere res sicut ZFS constituendi. Unum technicum detail quod scire debes tamen, est quod multi systemata fasciculorum non bene cum multis fasciculis agunt. Simplex solutio quam invenimus est creare plures directorios, e.g. pro diversis ID intervallis vel hash praefixis."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Postquam data detraxisti, certus esto integritatem fasciculorum per hashes in metadata, si praesto sunt, inspicere."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distributio"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Habes data, ita tibi possessionem primi speculi piratae mundi tui (verisimiliter) praebens. Multis modis pars difficillima iam peracta est, sed pars periculosissima adhuc ante te est. Post omnia, adhuc furtim egisti; sub radar volans. Omnia quae facere debuisti erant bonum VPN uti per totum, non implere tua personalia in ullis formis (scilicet), et fortasse sessionem navigatoris specialem uti (vel etiam computatrum diversum)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nunc data distribuere debes. In nostro casu primo libros ad Library Genesis reddere volebamus, sed cito difficultates in eo (fiction vs non-fiction ordinatio) invenimus. Itaque distributionem per Library Genesis-styli torrents decrevimus. Si facultatem habes ad propositum exsistens contribuere, id tibi multum temporis servare potest. Tamen, non sunt multae bene ordinatae specula piratae hodie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Ita dicamus te decernere ut torrents ipse distribuas. Conare illos fasciculos parvos servare, ut facile in aliis paginis speculari possint. Tunc debes torrents ipse seminare, dum adhuc anonymus manes. Potes VPN uti (cum vel sine portu transmittendo), vel cum Bitcoins tumbled pro Seedbox solvere. Si quidam ex his terminis tibi ignoti sunt, multum legendi habebis, quia magni momenti est ut pericula hic intelligas."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Potes fasciculos torrentium ipsos in paginis torrentium exsistentibus hospitari. In nostro casu, decrevimus ut paginam interretialem actu hospitaremus, quia etiam philosophiam nostram clare diffundere volebamus. Hoc ipse simili modo facere potes (nos Njalla pro nostris domainibus et hospitio utimur, cum Bitcoins tumbled soluti), sed etiam libenter nos contacta ut nos tuos torrents hospitemus. Quaerimus ut comprehensivum indicem speculorum piratarum per tempus aedificemus, si haec idea capiat."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Quod ad VPN electionem attinet, multum iam de hoc scriptum est, itaque solum generalem consilium de eligendo per famam repetemus. Verae politiae sine logis a iudiciis probatae cum longis curriculis ad tuendam secretum sunt optio minimi periculi, nostro iudicio. Nota quod etiam cum omnia recte facias, numquam ad nullum periculum pervenire potes. Exempli gratia, cum tuos torrents seminans, actor nationis valde motivatus probabiliter potest inspicere data influentia et effluentia pro servientibus VPN, et deducere quis sis. Vel simpliciter errare potes. Nos probabiliter iam erravimus, et iterum errabimus. Fortunate, nationes non curant <em>tantum</em> de pirateria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una decisio faciendi pro unoquoque proposito, est utrum illud sub eadem identitate ac antea publicare, necne. Si eodem nomine uti pergis, tunc errores in securitate operationis ex prioribus propositis te laedere possunt. Sed sub diversis nominibus publicare significat te non aedificare diuturnam famam. Nos elegimus ut fortem securitatem operationis ab initio haberemus ut eadem identitate uti pergamus, sed non dubitabimus sub alio nomine publicare si erramus aut si circumstantiae id postulant."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Verbum divulgare potest esse difficile. Ut diximus, haec adhuc est communitas angusta. Initio in Reddit posuimus, sed vere tractionem in Hacker News accepimus. Nunc nostra commendatio est ut in paucis locis ponas et videas quid accidat. Et iterum, nos contacta. Amaremus verbum de magis conatibus archivismi piratae diffundere."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusio"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Speramus hoc utile esse pro archivistis piratis nuper incipientibus. Excitamur te in hunc mundum recipere, itaque non dubita nos contingere. Conservemus quantum possumus scientiae et culturae mundi, et speculemus late."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introducens Speculum Bibliothecae Piratae: Conservans 7TB librorum (qui non sunt in Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Hoc propositum (EDIT: motum ad <a %(wikipedia_annas_archive)s>Annae Archivum</a>) intendit ad conservationem et liberationem scientiae humanae conferre. Parvam et humilem nostram contributionem facimus, in vestigiis magnorum ante nos."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Focus huius propositi illustratur nomine suo:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Deliberate legem de iure auctoris in plerisque nationibus violamus. Hoc nobis permittit facere quod entitates legales facere non possunt: curare ut libri late speculentur."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotheca</strong> - Sicut pleraeque bibliothecae, imprimis in materiis scriptis sicut libris intendimus. Fortasse in futurum in alias mediae species expandemus."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Speculum</strong> - Stricte speculum bibliothecarum exsistentium sumus. In conservatione intendimus, non in faciendo libros facile quaeribiles et downloadabiles (accessus) vel in magna communitate hominum qui novos libros conferunt (fontes) fovendo."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Prima bibliotheca quam speculati sumus est Z-Bibliotheca. Haec est popularis (et illegalis) bibliotheca. Collectionem Library Genesis ceperunt et facile quaeribilem fecerunt. Super hoc, valde efficaces facti sunt in novas contributiones librorum sollicitando, utentes conferentes variis beneficiis incentivando. Nunc non conferunt hos novos libros ad Library Genesis. Et dissimile Library Genesis, collectionem suam facile speculabilem non faciunt, quod latam conservationem impedit. Hoc est magni momenti ad eorum negotium exemplar, quia pecuniam exigunt pro accessu ad collectionem suam in mole (plus quam 10 libros per diem)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Non iudicamus moraliter de pecunia exigenda pro accessu in mole ad collectionem librorum illegalium. Indubitanter Z-Bibliotheca in expandendo accessu ad scientiam et in plus librorum fontibus felix fuit. Nos hic sumus ut partem nostram agamus: longum tempus conservationem huius privatae collectionis curare."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Te invitare volumus ut adiuvetis ad conservandam et liberandam scientiam humanam per download et seminando nostros torrents. Vide paginam propositi pro pluribus informationibus de quomodo data ordinata sint."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Etiam valde te invitamus ut tuas ideas conferas pro quibus collectionibus speculandis proximis, et quomodo id agere. Simul multum assequi possumus. Haec est sed parva contributio inter innumerabiles alias. Gratias tibi, pro omnibus quae facis."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Non coniungimus ad fasciculos ex hoc blog. Quaeso, invenias te ipsum.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb extractio, vel Quot Libri In Aeternum Conservantur?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si fasciculos ex bibliothecis umbrae recte deduplicaremus, quotam partem omnium librorum in mundo conservavimus?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Cum Speculo Bibliothecae Piratae (EDIT: translatum ad <a %(wikipedia_annas_archive)s>Annae Archivum</a>), propositum nostrum est omnes libros in mundo capere et eos in aeternum conservare.<sup>1</sup> Inter nostra Z-Bibliotheca torrentia et originalia Library Genesis torrentia, habemus 11,783,153 fasciculos. Sed quot sunt revera? Si illos fasciculos recte deduplicaremus, quotam partem omnium librorum in mundo conservavimus? Vere vellemus habere aliquid huiusmodi:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of hereditatis scriptae humanitatis in aeternum conservata"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Ad centesimas, opus est denominatore: numerus totus librorum umquam editorum.<sup>2</sup> Ante exitium Google Books, ingeniarius in proposito, Leonid Taycher, <a %(booksearch_blogspot)s>conatus est aestimare</a> hunc numerum. Venit — iocose — cum 129,864,880 (“saltem usque ad Dominicam”). Hunc numerum aestimavit construendo unam basem datorum omnium librorum in mundo. Ad hoc, varias datasets coniunxit et deinde eas variis modis coniunxit."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Ut celeriter digrediamur, est alius qui conatus est catalogare omnes libros in mundo: Aaron Swartz, activista digitalis defunctus et Reddit co-conditor.<sup>3</sup> <a %(youtube)s>Incepit Open Library</a> cum proposito “una pagina interretiali pro omni libro umquam edito”, coniungens data ex multis diversis fontibus. Ultimum pretium pro suo opere digitali conservationis solvit cum accusatus est pro massivo-downloadando chartas academicas, ducens ad suum suicidium. Sine dicere, haec est una ex causis cur nostra coetus pseudonymus sit, et cur valde cauti simus. Open Library adhuc heroice curritur a hominibus in Internet Archive, Aaronis legatum continuans. Ad hoc postea in hoc post revertamur."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "In Google blog post, Taycher describit nonnullas provocationes cum aestimando hunc numerum. Primum, quid constituit librum? Sunt paucae definitiones possibiles:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Exemplaria physica.</strong> Manifestum est hoc non valde utile, cum sint tantum duplicata eiusdem materiae. Iucundum esset si omnes annotationes quas homines in libris faciunt conservare possemus, sicut “scribbles in the margins” Fermati celebres. Sed heu, hoc manebit somnium archivistae."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Opera”.</strong> Exempli gratia “Harry Potter et Camera Secretorum” ut conceptus logicus, omnes versiones eius comprehendens, sicut diversae translationes et reimpressiones. Haec est quaedam utilis definitio, sed difficile potest esse lineam ducere quid computet. Exempli gratia, probabiliter volumus diversas translationes conservare, quamquam reimpressiones cum tantum minoribus differentiis non tam magni momenti esse possunt."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Editiones”.</strong> Hic numeras omnem versionem unicam libri. Si quid de eo diversum est, sicut alia tegumentum vel alia praefatio, computatur ut alia editio."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Fasciculi.</strong> Cum laboramus cum bibliothecis umbrae sicut Library Genesis, Sci-Hub, vel Z-Library, est consideratio addita. Possunt esse multiplices scans eiusdem editionis. Et homines possunt facere meliores versiones fasciculorum existentium, per scanning textum utentes OCR, vel rectificando paginas quae ad angulum scannatae sunt. Volumus tantum numerare hos fasciculos ut una editio, quod requireret bonum metadata, vel deduplicationem utens mensuris similitudinis documenti."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Editiones” videntur esse definitio maxime practica quid “libri” sint. Convenienter, haec definitio etiam adhibetur ad assignandos unicos numeros ISBN. ISBN, vel Numerus Internationalis Standardis Libri, communiter adhibetur pro commercio internationali, cum sit integratus cum systemate internationali barcode (”Numerus Articuli Internationalis”). Si vis vendere librum in tabernis, opus est barcode, ergo obtines ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher’s blog post mentionat dum ISBNs utiles sunt, non sunt universales, cum tantum vere adoptati sint in medio septuagesimo, et non ubique circum mundum. Tamen, ISBN probabiliter est identifier librorum editionum maxime late usus, ergo est nostrum optimum initium. Si possumus invenire omnes ISBNs in mundo, habemus utilem indicem quibus libri adhuc conservandi sunt."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Ita, ubi data accipimus? Sunt nonnullae conatus existentes qui conantur componere indicem omnium librorum in mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Post omnes, hanc investigationem pro Google Books fecerunt. Tamen, eorum metadata non est accessibilis in mole et potius difficile est radere."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Ut ante dictum est, hoc est totum eorum propositum. Magnas copias datae bibliothecae ex bibliothecis cooperantibus et archivis nationalibus collegerunt, et id facere pergunt. Habent etiam voluntarios bibliothecarios et turmam technicam quae conatur deduplicare records, et eas cum omnibus generibus metadata tagere. Optime, eorum dataset est omnino apertum. Potes simpliciter <a %(openlibrary)s>eam downloadare</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Haec est pagina interretialis a non-lucrativo OCLC administrata, quae systemata administrationis bibliothecae vendit. Aggregant metadata librorum ex multis bibliothecis, et eam per WorldCat paginam interretialem praebent. Tamen, etiam pecuniam faciunt vendendo haec data, ergo non est praesto pro mole download. Habent tamen aliquas datasets limitatiores in mole praesto pro download, in cooperatione cum specificis bibliothecis."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Hoc est argumentum huius blog post. ISBNdb variis paginis interretialibus metadata librorum scrutat, in particulari data pretiorum, quae deinde vendunt librariis, ut possint libros suos pretiari secundum reliquum mercatum. Cum ISBNs nunc satis universales sint, efficaciter aedificaverunt “paginam interretialem pro omni libro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Varii singuli systemata bibliothecarum et archivorum.</strong> Sunt bibliothecae et archiva quae a nullo ex supradictis indicata et aggregata sunt, saepe quia subfinanciata sunt, vel aliis de causis nolunt sua data cum Open Library, OCLC, Google, et similibus communicare. Multa ex his habent records digitales per interrete accessibiles, et saepe non bene proteguntur, ita si vis adiuvare et aliquid discere de miris systematibus bibliothecarum, haec sunt optima initia."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In hoc nuntio, laeti sumus nuntiare parvam emissionem (comparatam cum nostris prioribus emissionibus Z-Library). Plurimum ISBNdb exscrapavimus, et data in promptu fecimus ad torrentem in situ Pirate Library Mirror (EDIT: mota ad <a %(wikipedia_annas_archive)s>Archivum Annae</a>; non directe hic coniungemus, quaere modo). Haec sunt circa 30.9 miliones recordorum (20GB ut <a %(jsonlines)s>JSON Lineae</a>; 4.4GB compressa). In suo situ affirmant se actu habere 32.6 miliones recordorum, ita fortasse aliquas omisimus, aut <em>illi</em> aliquid male faciunt. Quocumque modo, nunc non communicabimus quomodo id fecimus — id relinquemus ut exercitium lectori. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Quod communicabimus est aliqua analysis praeliminaris, ut conemur propius aestimare numerum librorum in mundo. Tres datasets spectavimus: hoc novum dataset ISBNdb, nostram originalem emissionem metadata quam ex Z-Library umbra bibliotheca exscrapavimus (quae includit Library Genesis), et Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Incipiamus cum nonnullis numeris approximatis:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In utroque Z-Library/Libgen et Open Library sunt multo plures libri quam unici ISBNs. Significatne hoc multos ex illis libris non habere ISBNs, an metadata ISBNs simpliciter deest? Probabiliter hanc quaestionem respondere possumus cum combinatione automaticae coniunctionis fundatae in aliis attributis (titulo, auctore, editore, etc), plus fontes datae trahendo, et extrahendo ISBNs ex ipsis librorum scansionibus (in casu Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quot ex illis ISBNs sunt unici? Hoc optime illustratur cum diagrammate Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Ut magis precise dicam:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Mirati sumus quam parum sit superpositio! ISBNdb habet ingentem numerum ISBNs qui non apparent in Z-Library vel Open Library, et idem valet (ad minorem sed adhuc substantialem gradum) pro aliis duobus. Hoc multae novae quaestiones oritur. Quantum adiuvaret automatica coniunctio in taggando libros qui non fuerunt taggati cum ISBNs? Multae coniunctiones essent et ideo aucta superpositio? Etiam, quid accideret si quartum vel quintum dataset adduceremus? Quantum superpositionis tunc videremus?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Hoc nobis initium dat. Nunc spectare possumus omnes ISBNs qui non erant in dataset Z-Library, et qui non coniungunt campos tituli/auctoris. Hoc nobis potestatem dat ad conservandos omnes libros in mundo: primum per exscrapationem interretis pro scansionibus, deinde per egressum in vita reali ad libros scansionandos. Hoc etiam potest esse fundatum a turba, vel a \"praemiis\" a populo qui velint certos libros digitalizari. Omnia haec sunt fabula pro alio tempore."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si vis adiuvare cum aliquo ex his — ulteriore analysi; plus metadata exscrapando; plus libros inveniendi; libros OCR'endi; hoc faciendo pro aliis domainibus (exempli gratia chartis, audiolibris, pelliculis, spectaculis televisificis, ephemeridibus) vel etiam faciendo aliqua ex his data in promptu pro rebus sicut ML / magnis linguis modelis exercendis — quaeso contactum meum (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si specialiter in analysi datae interest, laboramus ut nostra datasets et scripta in forma faciliore ad usum praesto faciamus. Magnificum esset si posses simpliciter forcare notitiam et incipere ludere cum hoc."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Denique, si vis hanc operam sustinere, quaeso considera donationem faciendam. Haec est operatio omnino voluntaria, et tua contributio magnam differentiam facit. Omnis pars adiuvat. Nunc donationes in crypto accipimus; vide paginam Donate in Archivum Annae."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pro aliqua rationabili definitione \"in perpetuum\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Certe, hereditas scripta humanitatis multo plus est quam libri, praesertim hodie. Pro hoc nuntio et nostris recentibus emissionibus in libris focus sumus, sed nostrae utilitates ulterius extenduntur."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Multo plus dici potest de Aarone Swartz, sed tantum voluimus eum breviter mentionem facere, cum partem pivotalem in hac fabula agat. Cum tempus transit, plures homines eius nomen primum invenire possunt, et deinde in cuniculum se immergere."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Critica fenestra umbra bibliothecarum"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Quomodo possumus affirmare nos nostras collectiones in perpetuum conservare, cum iam 1 PB appropinquant?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versio Sinica 中文版</a>, discepta in <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "In Archivo Annae, saepe rogati sumus quomodo possumus affirmare nos nostras collectiones in perpetuum conservare, cum magnitudo totalis iam 1 Petabyte (1000 TB) appropinquet, et adhuc crescat. In hoc articulo inspiciemus nostram philosophiam, et videbimus cur proximus decennium criticus sit pro nostra missione conservandi scientiam et culturam humanitatis."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Magnitudo <a %(annas_archive_stats)s>totalis</a> collectionum nostrarum, per paucos menses praeteritos, secundum numerum torrentium seminantium distributa."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritates"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Cur tam multum de scriptis et libris curamus? Ponamus fundamentum nostrum in conservatione generali — fortasse alium articulum de hoc scribemus. Cur igitur scripta et libri? Responsum est simplex: <strong>densitas informationis</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte repositi, textus scriptus maximam informationem omnium mediorum continet. Dum de scientia et cultura curamus, magis de priore curamus. In summa, hierarchiam densitatis informationis et momenti conservationis invenimus quae fere sic se habet:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Scripturae academicae, ephemerides, relationes"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Data organica sicut sequentiae DNA, semina plantarum, vel exempla microbialia"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libri non-fictionales"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Codex software scientiae et ingeniariae"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Data mensurae sicut mensurae scientificae, data oeconomica, relationes corporatae"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Situs interretiales scientiae et ingeniariae, disputationes online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Ephemerides non-fictionales, diurnaria, manualia"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcripta non-fictionalia sermonum, documentariorum, podcastorum"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Data interna ex societatibus vel gubernationibus (effluvia)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata records generaliter (de non-fictione et fictione; de aliis mediis, arte, hominibus, etc.; inclusis recensionibus)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Data geographica (e.g. mappae, percontationes geologicae)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripta iudicialium vel forensium processuum"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versiones ficticiae vel ludicrae omnium praedictorum"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Ordo in hoc indice aliquantum arbitrarius est — plura item sunt aequalia vel dissentiones intra nostrum coetum habent — et probabiliter aliquas categorias importantes obliviscimur. Sed hoc fere est quomodo prioritates statuimus."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Quaedam ex his rebus nimis dissimiles sunt ab aliis ut de eis curemus (vel iam ab aliis institutis curantur), sicut data organica vel data geographica. Sed pleraque item in hoc indice nobis revera sunt momenti."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Alius magnus factor in nostris prioritatis est quantum in periculo sit opus certum. Malumus operibus intendere quae sunt:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rara"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Singulariter neglecta"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Singulariter in periculo destructionis (e.g. bello, sumptibus decurtatis, litibus, vel persecutione politica)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Denique, de magnitudine curamus. Tempus et pecuniam limitata habemus, itaque malumus mensem impendere ad 10,000 libros servandos quam 1,000 libros — si aeque pretiosi et in periculo sunt."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliothecae umbrae"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Multae sunt institutiones quae similes missiones et similes prioritates habent. Revera, sunt bibliothecae, archiva, laboratoria, musea, et aliae institutiones ad huiusmodi conservationem destinatae. Multae ex his bene fundatae sunt, a gubernationibus, individuis, vel societatibus. Sed unum magnum caecum punctum habent: systema legale."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hic iacet singularis munus bibliothecarum umbrarum, et ratio cur Annae Archivum existat. Possumus facere quae aliae institutiones facere non possunt. Nunc, non est (saepe) quod possumus archiva condere quae alibi servare illicitum est. Non, in multis locis licet archivum cum quibuslibet libris, chartis, ephemeridibus, et cetera aedificare."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Sed quod archiva legalia saepe desunt est <strong>redundantia et diuturnitas</strong>. Exstant libri quorum una tantum copia in aliqua bibliotheca physica alicubi exstat. Exstant metadata records a sola societate custodita. Exstant diurnaria tantum in microfilmo in uno archivo conservata. Bibliothecae possunt sumptibus decurtari, societates possunt decoquere, archiva possunt bombardari et ad solum comburi. Hoc non est hypotheticum — hoc semper accidit."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Quod singulariter facere possumus in Annae Archivo est multae copiae operum condere, in magnitudine. Possumus chartas, libros, ephemerides, et plura colligere, et in mole distribuere. Hoc tempore per torrents facimus, sed technologiae exactae non refert et mutabuntur per tempus. Pars momenti est multae copiae per orbem terrarum distribui. Haec sententia ex plus quam 200 annis adhuc vera sonat:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Perdita recuperari non possunt; sed quod reliquum est servemus: non per cryptas et seras quae eos ab oculis et usu publicis secludunt, in tempus consumendum tradentes, sed per talem multiplicationem copiarum, quae eos extra casus periculum ponat.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Nota celeris de dominio publico. Cum Annae Archivum singulariter in actionibus quae in multis locis circum orbem illicitum sunt focus, non curamus de collectionibus late praesto, ut libri in dominio publico. Entitates legales saepe iam bene curant de hoc. Tamen, sunt considerationes quae nos interdum in collectionibus publice praesto laborare faciunt:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata records libere videri possunt in situ Worldcat, sed non in mole downloadari (donec eos <a %(worldcat_scrape)s>scrapimus</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Codex potest esse aperta fons in Github, sed Github ut totum non facile mirari potest et sic servari (quamquam in hoc casu particulari sunt satis distributae copiae plerorumque repositoriorum codicis)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit gratis uti potest, sed nuper strictas mensuras anti-scraping posuit, in luce datae esurientis LLM disciplinae (plus de hoc postea)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Multiplicatio copiarum"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Ad quaestionem originalem revertentes: quomodo possumus affirmare nos collectiones nostras in perpetuum servare? Praecipuum problema hic est quod nostra collectio <a %(torrents_stats)s>celeriter</a> crevit, per scraping et aperta fonte aliquas magnas collectiones (praeter mirabile opus iam factum ab aliis bibliothecis umbrae apertae datae sicut Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Haec datae incrementum facit difficilius collectiones circum orbem mirari. Datae repositio pretiosa est! Sed sumus optimi, praesertim cum sequentes tres trends observamus."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Fructus humiles decerpsimus"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Hoc directe sequitur ex nostris prioritates supra discussis. Malumus laborare in liberandis magnis collectionibus primum. Nunc quod quasdam ex maximis collectionibus in mundo securavimus, exspectamus nostrum incrementum multo tardius fore."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Adhuc longus cauda minorum collectionum est, et novi libri cotidie scanni vel eduntur, sed rate verisimiliter multo tardior erit. Possumus adhuc duplicare vel etiam triplicare in magnitudine, sed per longius tempus."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Sumptus repositi continue exponensialiter decrescunt"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Tempore scriptionis, <a %(diskprices)s>pretium disci</a> per TB circa $12 pro novis discis, $8 pro usitatis discis, et $4 pro taeniis est. Si cauti sumus et solum novos discos spectamus, hoc significat quod petabyte reponere circiter $12,000 constat. Si bibliothecam nostram a 900TB ad 2.7PB triplicare assumimus, hoc significaret $32,400 ad totam bibliothecam nostram speculari. Addendo electricitatem, sumptus aliorum instrumentorum, et cetera, rotundemus ad $40,000. Vel cum taeniis magis $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Ex una parte <strong>$15,000–$40,000 pro summa omnium humanarum cognitionum est furtum</strong>. Ex altera parte, paulum arduum est expectare copias plenas, praesertim si volumus etiam illos homines seminare eorum torrents pro aliorum beneficio."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Hoc est hodie. Sed progressus procedit:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Sumptus disci duri per TB fere in tertiam partem decurtati sunt per ultimos 10 annos, et verisimile est simili celeritate decrescere. Taenia similem trajectoriam videtur habere. Pretia SSD etiam citius decrescunt, et fortasse pretia HDD decennium finiente superabunt."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendencies pretiorum HDD ex diversis fontibus (click ut studium videas)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si hoc tenet, tunc in 10 annis fortasse spectabimus solum $5,000–$13,000 ad totam collectionem nostram speculari (1/3), vel etiam minus si minus in magnitudine crescimus. Dum adhuc multum pecuniae est, hoc multis hominibus assequibile erit. Et fortasse etiam melius erit propter proximam rem…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Meliorationes in densitate informationis"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Nunc libros in formatis rudibus reponimus, quae nobis data sunt. Certe, compressi sunt, sed saepe adhuc magnae scansiones vel photographiae paginarum sunt."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Usque nunc, solae optiones ad totam magnitudinem collectionis nostrae minuendam fuerunt per compressionem magis aggressivam, vel deduplicationem. Tamen, ut satis significantes compendia obtineamus, utraque nimis amissiva sunt pro nostro gustu. Compressio gravis photographiarum textum vix legibilem facere potest. Et deduplicatio altam fiduciam librorum prorsus eorumdem requirit, quae saepe nimis inexacta est, praesertim si contenta eadem sunt sed scansiones diversis occasionibus factae sunt."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Semper fuit tertia optio, sed eius qualitas tam abominabilis fuit ut numquam eam consideraverimus: <strong>OCR, vel Optica Characterum Recognitio</strong>. Hoc est processus convertendi photographias in textum planum, per AI utendo ad characteres in photographiis detegendos. Instrumenta pro hoc diu exstiterunt, et satis decentes fuerunt, sed “satis decentes” non sufficit pro propositis conservationis."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tamen, recentia exempla profundae doctrinae multi-modalis progressum rapidissimum fecerunt, quamvis adhuc magnis sumptibus. Exspectamus et accuratiam et sumptus dramatico modo meliorari in annis venturis, ad punctum ubi fiet realisticum ad totam bibliothecam nostram applicare."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Meliorationes OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Cum id acciderit, verisimile est nos adhuc originalia files conservare, sed praeterea versionem multo minorem bibliothecae nostrae habere posse, quam plerique homines speculari volent. Praemium est quod textus rudis ipse etiam melius comprimitur, et multo facilius deduplicatur, nobis etiam magis compendia praebens."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "In summa, non est inverisimile exspectare saltem 5-10x reductionem in tota magnitudine file, fortasse etiam plus. Etiam cum conservativa 5x reductione, spectaremus <strong>$1,000–$3,000 in 10 annis etiam si bibliotheca nostra triplicet in magnitudine</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Fenestra critica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si haec praedictiones accuratae sunt, <strong>solum opus est exspectare paucos annos</strong> antequam tota collectio nostra late speculabitur. Ita, verbis Thomae Jefferson, “extra casus potestatem posita.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Infeliciter, adventus LLMs, et eorum data-esurientia disciplina, multos possessores iuris auctorum in defensionem posuit. Etiam plus quam iam erant. Multae paginae interretiales difficilius faciunt ad radendum et archivandum, lites circumvolant, et interea bibliothecae physicae et archiva negleguntur."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Possumus solum exspectare hos trends peiorari, et multa opera amitti bene antequam in publicum dominium intrent."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>In vespere sumus revolutionis in conservatione, sed <q>amissa recuperari non possunt.</q></strong> Habemus fenestram criticam circiter 5-10 annorum in qua adhuc satis carum est operari bibliothecam umbram et creare multos speculos circum orbem, et in qua accessus nondum omnino clausus est."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si hanc fenestram transire possumus, scientiam et culturam humanitatis in perpetuum servabimus. Hoc tempus non debemus perdere. Hanc fenestram criticam nobis claudere non debemus."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Eamus."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accessus exclusivus pro societatibus LLM ad maximam collectionem librorum non-fictionis Sinensium in mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versio Sinica 中文版</a>, <a %(news_ycombinator)s>Disputatio in Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Annae Archivum collectionem singularem 7.5 miliones / 350TB librorum non-fictionis Sinensium acquisivit — maiorem quam Library Genesis. Parati sumus societati LLM accessum exclusivum dare, pro OCR altae qualitatis et extractione textus.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Hoc est breve blogum. Quaerimus societatem vel institutionem quae nos adiuvet cum OCR et extractione textus pro ingenti collectione quam acquisivimus, pro accessu exclusivo primo. Post tempus embargo, totam collectionem certe dimittemus."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Textus academicus altae qualitatis valde utilis est ad LLMs exercendos. Dum nostra collectio Sinica est, hoc etiam utile esse debet ad LLMs Anglicos exercendos: exempla videntur conceptus et scientiam codificare, quocumque sermone fonte."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Ad hoc, textus ex imaginibus extrahi debet. Quid Annae Archivum ex eo accipit? Quaerere textum integrum librorum pro suis usoribus."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Quia nostra proposita cum illis LLM excultorum congruunt, collaboratorem quaerimus. Parati sumus tibi <strong>accessum exclusivum primum ad hanc collectionem in mole per 1 annum</strong> dare, si recte OCR et extractionem textus facere potes. Si totum codicem tuae pipeline nobiscum communicare vis, parati sumus collectionem diutius embargare."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Paginae exemplares"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Ut nobis probetis vos bonam pipeline habere, hic sunt nonnullae paginae exemplares ad incipiendum, ex libro de superconductors. Tua pipeline debet recte tractare mathematicas, tabulas, chartas, notas, et cetera."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Mitte paginas processas ad nostrum electronicam. Si bene spectant, plures tibi privatim mittemus, et exspectamus te celeriter tuam pipeline in illis currere posse. Cum satisfacti erimus, pactum facere possumus."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collectio"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Aliqua notitia de collectione. <a %(duxiu)s>Duxiu</a> est ingens database librorum scannatorum, creata a <a %(chaoxing)s>SuperStar Digital Library Group</a>. Plerique sunt libri academici, scannati ut digitaliter ad universitates et bibliothecas praesto sint. Pro nostra audientia Anglice loquente, <a %(library_princeton)s>Princeton</a> et <a %(guides_lib_uw)s>University of Washington</a> bonas recensionis habent. Est etiam articulus excellens qui plus contextus praebet: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (quaere in Annae Archivo)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Libri ex Duxiu diu in interrete Sinico piratati sunt. Solent vendi minus quam dollario a venditoribus. Typice distribuuntur utens Sinico aequivalente Google Drive, quod saepe hackatum est ut plus spatii repono permittat. Quaedam technicae notitiae inveniri possunt <a %(github_duty_machine)s>hic</a> et <a %(github_821_github_io)s>hic</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Quamvis libri semi-publice distributi sint, difficile est eos in mole obtinere. Hoc in nostro TODO-elencho alte posuimus, et plures menses operis pleni temporis ad id destinavimus. Tamen, nuper incredibilis, mirabilis, et ingeniosus voluntarius nos adiit, dicens se totum hoc opus iam perfecisse — magno impendio. Totam collectionem nobiscum communicaverunt, nihil in reditu exspectantes, nisi longi temporis conservationis cautionem. Vere mirabile. Consenserunt auxilium hoc modo petere ut collectio OCR'etur."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Collectio est 7,543,702 fasciculi. Hoc plus est quam Library Genesis non-fictionis (circa 5.3 miliones). Totalis magnitudo fasciculorum est circa 359TB (326TiB) in sua forma currenti."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Aperimus ad alias propositiones et ideas. Modo nos contacta. Vide Annae Archivum pro plura informatione de nostris collectionibus, conatibus conservationis, et quomodo adiuvare possis. Gratias!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Monitio: haec blogi postea deprecata est. Statuitur IPFS nondum paratum esse ad tempus primum. Adhuc coniungemus ad fasciculos in IPFS ex Annae Archivum cum possibile, sed non amplius ipsi hospitabimur, nec aliis commendamus ut speculum utendo IPFS. Quaeso vide paginam Torrents si vis adiuvare nostram collectionem conservare."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Auxilium ad seminarium Z-Library in IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Quomodo bibliothecam umbraticam administrare: operationes apud Archivum Annae"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nulla est <q>AWS pro caritatibus umbraticis,</q> quomodo ergo Archivum Annae administramus?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ego <a %(wikipedia_annas_archive)s>Archivum Annae</a> curo, maximum mundi machinam quaesitionis apertam et non-lucrativam pro <a %(wikipedia_shadow_library)s>bibliothecis umbraticis</a>, sicut Sci-Hub, Library Genesis, et Z-Library. Propositum nostrum est scientiam et culturam facilem praebere, et tandem communitatem hominum aedificare qui simul omnes <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>libros mundi</a> archivant et conservant."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In hoc articulo ostendam quomodo hunc locum interretialem administramus, et singulares provocationes quae cum operatione situs interretialis cum dubio statu legali veniunt, cum nulla sit “AWS pro caritatibus umbraticis”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Etiam inspice articulum sororem <a %(blog_how_to_become_a_pirate_archivist)s>Quomodo fieri archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovationis tesserae"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Incipiamus cum nostra technologiae structura. Deliberate taediosa est. Utimur Flask, MariaDB, et ElasticSearch. Hoc est literaliter totum. Quaesitio problema plerumque solutum est, et non intendimus id reinventare. Praeterea, innovationis <a %(mcfunley)s>tesserae</a> in aliud expendere debemus: ne ab auctoritatibus deiciamur."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Quam legalis vel illegalis est Annae Archivum exacte? Hoc maxime dependet a iurisdictione legali. Pleraque nationes in aliquo genere iuris auctoris credunt, quod significat homines vel societates monopolium exclusivum in certis operibus per certum tempus assignari. Ut in transitu dicamus, apud Annae Archivum credimus dum aliqua beneficia sunt, in summa ius auctoris societati detrimentum est — sed haec est fabula pro alio tempore."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Hoc monopolium exclusivum in certis operibus significat quod illegalis est pro quovis extra hoc monopolium directe illa opera distribuere — nos inclusos. Sed Annae Archivum est machina quaesitionis quae illa opera directe non distribuit (saltem non in nostro situ clearnet), ergo debemus esse bene, recte? Non exacte. In multis iurisdictionibus non solum illegalis est opera auctoris iuris distribuere, sed etiam ad loca quae id faciunt coniungere. Exemplum classicum huius est lex DMCA Civitatum Foederatarum."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Hoc est strictissimum finem spectrum. In altero fine spectrum theoretice esse possent nationes sine ullis legibus auctoris iuris, sed hae revera non existunt. Fere omnis natio aliquam formam legis auctoris iuris in libris habet. Exsecutio est alia fabula. Sunt multae nationes cum guberniis quae non curant legem auctoris iuris exsequi. Sunt etiam nationes inter duo extrema, quae opera auctoris iuris distribuere prohibent, sed non prohibent ad talia opera coniungere."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Alia consideratio est in gradu societatis. Si societas operatur in iurisdictione quae non curat de iure auctoris, sed ipsa societas non vult ullum periculum accipere, tunc possunt tuum situm interretialem claudere statim ut quis de eo queratur."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Denique, magna consideratio est solutiones. Cum anonymi manere debemus, non possumus uti methodis solutionum traditis. Hoc nos relinquit cum cryptomonedis, et solum parva pars societatum eas sustinet (sunt chartae debiti virtuales a crypto solutae, sed saepe non accipiuntur)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architectura systematis"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Ita dicamus te invenisse aliquas societates quae paratae sunt tuum situm interretialem hospitari sine te claudendo — vocemus has “libertatem amantibus praebitoribus” 😄. Cito invenies quod omnia cum eis hospitari satis sumptuosum est, ita fortasse vis invenire aliquos “praebitores vilis” et ibi actualem hospitationem facere, per libertatem amantibus praebitoribus proxiante. Si recte facis, praebitores vilis numquam sciant quid hospitaris, nec umquam querelas accipiant."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Cum omnibus his praebitoribus est periculum quod te claudant tamen, ita etiam redundantiam opus est. Hoc opus est in omnibus gradibus nostri aggeris."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Una societas aliquantum libertatem amans quae se in positione interesting posuit est Cloudflare. Ipsi <a %(blog_cloudflare)s>arguerunt</a> se non esse praebitorem hospitationis, sed utilitatem, sicut ISP. Ergo non sunt subiecti DMCA vel aliis petitionibus amotionis, et petitiones ad tuum actualem praebitorem hospitationis transmittunt. Ipsi usque ad iudicium pervenerunt ut hanc structuram protegerent. Ergo eos uti possumus ut alium stratum caching et protectionis."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare solutiones anonymas non accipit, ita solum eorum consilium liberum uti possumus. Hoc significat quod non possumus uti eorum aequilibrio oneris vel facultatibus failover. Ergo <a %(annas_archive_l255)s>hoc ipsi implevimus</a> in gradu dominii. In pagina onere, navigatrum reprehendet si dominium praesens adhuc praesto est, et si non, omnes URLs ad aliud dominium rescribit. Cum Cloudflare multas paginas cachet, hoc significat quod usor potest in nostro principali dominio terram, etiam si servitor proxius deorsum est, et tunc in proximo click ad aliud dominium moveri."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Etiam normales curas operationales habemus, ut monitoratio sanitatis servientis, errores backend et frontend loggendi, et cetera. Nostra architectura failover plus robustitudinis in hoc fronte permittit, exempli gratia per currendum omnino diversum set servientium in uno ex dominis. Possumus etiam versiones antiquiores codicis et Datasets in hoc dominio separato currere, in casu bug critica in versione principali non animadvertitur."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Possumus etiam contra Cloudflare se vertentem contra nos sepi, removendo eam ex uno ex dominis, ut hoc dominio separato. Diversae permutationes harum idearum possibilia sunt."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Instrumenta"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Videamus quae instrumenta utimur ad omnia haec perficienda. Hoc valde evolvitur dum in novas quaestiones incidimus et novas solutiones invenimus."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servitor applicationis: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servitor proxius: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Administratio servientis: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Progressio: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hospitatio statica Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Sunt quaedam decisiones quas iterum atque iterum retractavimus. Una est communicatio inter servitores: olim Wireguard adhibuimus ad hoc, sed invenimus quod interdum desinit transmittere ullos datos, aut solum in una directione transmittit. Hoc accidit cum variis Wireguard configurationibus quas tentavimus, ut <a %(github_costela_wesher)s>wesher</a> et <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Etiam conati sumus portus tunnellare per SSH, utens autossh et sshuttle, sed in <a %(github_sshuttle)s>difficultates incidimus</a> (quamquam adhuc non liquet mihi si autossh laborat cum quaestionibus TCP-super-TCP necne — mihi solum videtur solutio instabilis sed fortasse revera bene est?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Proinde, rediimus ad directas coniunctiones inter servitores, occultantes quod servitor currit in vilibus provisoribus utendo IP-filtratione cum UFW. Hoc habet incommodum quod Docker non bene operatur cum UFW, nisi utaris <code>network_mode: \"host\"</code>. Omnia haec sunt paulo magis errori obnoxia, quia servitorem tuum ad interrete expones cum sola parva misconfiguratione. Fortasse debemus redire ad autossh — responsa hic valde grata essent."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Etiam de Varnish et Nginx disceptavimus. Nunc Varnish placet, sed habet suas difficultates et asperitates. Idem valet de Checkmk: non amamus, sed nunc operatur. Weblate satis bene fuit sed non incredibile — interdum timeo ne data mea amittat cum eam cum nostro git repo synchronizare conor. Flask generatim bene fuit, sed habet quaedam miras difficultates quae multum temporis ad debug consumpsit, ut configuratio dominii consuetudinis, vel quaestiones cum integratione SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Hactenus alia instrumenta optime se habuerunt: nullas graves querelas de MariaDB, ElasticSearch, Gitlab, Zulip, Docker, et Tor habemus. Omnia haec quaedam problemata habuerunt, sed nihil nimis grave aut tempus consumens."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusio"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Fuit experientia interesting discere quomodo robustum et resilientem umbram bibliothecae machinam quaerendi constituere. Multa plura singula communicanda sunt in postibus futuris, ita me certiorem fac quid amplius discere velis!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Ut semper, quaerimus donationes ad hanc operam sustinendam, ita certus esto ut paginam Donationum in Annae Archivum inspicias. Etiam quaerimus alias formas subsidii, ut subsidia, longum tempus patronos, solutiones periculosas solutiones, fortasse etiam (gustuosas!) ads. Et si tempus et artes tuas conferre vis, semper quaerimus programmatores, interpretes, et cetera. Gratias pro tuo studio et auxilio."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna et turma (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Salve, ego sum Anna. Creavi <a %(wikipedia_annas_archive)s>Annae Archivum</a>, maximum umbram bibliothecam mundi. Hoc est blogum personale, in quo ego et socii mei scribimus de piratica, digitali conservatione, et aliis."

#, fuzzy
msgid "blog.index.text2"
msgstr "Coniunge mecum in <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Nota hunc locum esse tantum blogum. Solum nostra verba hic hospitamur. Nulli torrents aut alii fasciculi iuris reservati hic hospitantur aut coniunguntur."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogi postae"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat colligatio"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5,998,794 libros in IPFS ponere"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Monitio: haec blogi postea deprecata est. Statuitur IPFS nondum paratum esse ad tempus primum. Adhuc coniungemus ad fasciculos in IPFS ex Annae Archivum cum possibile, sed non amplius ipsi hospitabimur, nec aliis commendamus ut speculum utendo IPFS. Quaeso vide paginam Torrents si vis adiuvare nostram collectionem conservare."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Annae Archivum omnem WorldCat (maximam collectionem metadata bibliothecae mundi) colligavit ut indicem TODO librorum qui conservandi sunt faceret.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Ante annum, <a %(blog)s>proposuimus</a> hanc quaestionem respondere: <strong>Quot libri a bibliothecis umbrae permanentur conservati sunt?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Cum liber in bibliothecam umbram data aperta sicut <a %(wikipedia_library_genesis)s>Library Genesis</a> et nunc <a %(wikipedia_annas_archive)s>Annae Archivum</a> pervenerit, per totum orbem terrarum (per torrentia) speculatur, ita ut fere in perpetuum conservetur."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Ad quaestionem respondendam de qua percentage librorum conservata sit, scire debemus denominatorem: quot libri in toto existunt? Et optime non solum numerum habemus, sed etiam metadata vera. Tum non solum eos contra bibliothecas umbras conferre possumus, sed etiam <strong>creare possumus indicem TODO librorum reliquorum ad conservandum!</strong> Etiam somniare possumus de conatu multitudinis ad hunc indicem TODO peragendum."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Scrutati sumus <a %(wikipedia_isbndb_com)s>ISBNdb</a>, et dataset <a %(openlibrary)s>Open Library</a> deprompsimus, sed eventus non satisfecerunt. Praecipua difficultas erat quod non multum superpositio ISBNs erat. Vide hanc diagramma Venn ex <a %(blog)s>nostro blog post</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Valde mirati sumus quam parum superpositio esset inter ISBNdb et Open Library, quae utraque data ex variis fontibus liberaliter includunt, sicut scrutamina web et tabulas bibliothecarum. Si bene faciunt in inveniendis plerisque ISBNs ibi, circuli eorum certe substantialem superpositionem haberent, aut unus alterius subsectio esset. Nos fecit mirari, quot libri <em>omnino extra hos circulos cadunt</em>? Maiorem databasem opus est."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Tunc oculos nostros in maximam librorum databasem in mundo intendimus: <a %(wikipedia_worldcat)s>WorldCat</a>. Haec est database proprietaria a non-lucrativo <a %(wikipedia_oclc)s>OCLC</a>, quae metadata tabulas ex bibliothecis toto orbe aggregat, in commutatione pro datione illis bibliothecis accessus ad plenam dataset, et ut in quaesitis usorum finalium appareant."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Etsi OCLC non-lucrativa est, eorum negotium exemplar requirit ut database suam protegat. Bene, dolemus dicere, amici apud OCLC, omnia donamus. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Praeterito anno, omnes WorldCat tabulas diligenter scrutati sumus. Primo, fortunam bonam nacti sumus. WorldCat suum totum redesignum situs interretialis (in Aug 2022) modo evolvit. Hoc includebat substantialem renovationem systematum backend, introducens multas securitatis vitia. Opportunitatem statim arripuimus, et centena milia (!) tabularum in paucis diebus scrutare potuimus."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Postea, vitia securitatis paulatim unum post unum reparata sunt, donec ultimum quod invenimus circiter mensem abhinc emendatum est. Eo tempore fere omnes tabulas habuimus, et solum pro paulo altioris qualitatis tabulis ibamus. Itaque tempus esse sensimus ad dimittendum!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Aspiciamus ad aliqua informationes fundamentales de data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formatum?</strong> <a %(blog)s>Annae Archivum Containera (AAC)</a>, quod est essentialiter <a %(jsonlines)s>JSON Lineae</a> compressae cum <a %(zstd)s>Zstandard</a>, plus nonnullae semanticae normatae. Haec containera varias tabularum species involvunt, secundum diversas scrutamina quae deployavimus."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Error ignotus accidit. Quaeso contactum nobis ad %(email)s cum imagine."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Haec moneta minimum maius solito habet. Quaeso aliam durationem vel aliam monetam selige."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Petitio compleri non potuit. Quaeso iterum tenta post paucos minutos, et si iterum accidit, contactum nobis ad %(email)s cum imagine."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Error in processu solutionis. Quaeso paulisper exspecta et iterum tenta. Si quaestio plus quam 24 horas perseverat, quaeso contactum nobis ad %(email)s cum imagine."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "commentarium occultum"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Quaestio de archivo: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Melior versio"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Visne hunc usorem referre ob abusivam vel inappropriatam actionem?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Referre abusum"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abusus relatus:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Hunc usorem ob abusum rettulisti."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responde"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Quaeso <a %(a_login)s>intra</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Commentarium reliquisti. Potest paulisper capere ut appareat."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Aliquid erravit. Paginae reload fac et iterum conare."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s paginae affectae"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visibilis in Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visibilis in Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visibile in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Segnato come rotto in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Assente da Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Notatum ut “spam” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Notatum ut “malus file” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Non tutte le pagine sono state convertite in PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "L'esecuzione di exiftool è fallita su questo file"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Liber (ignotus)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Liber (non fictus)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Liber (fictus)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Articulus ephemeridis"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentum normarum"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Ephemeris"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Liber nubeculatus"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musicae"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audioliber"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Aliud"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Download ex Servo Socio"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Externus download"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Externus mutuum"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Externus mutuum (impressio impedita)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Explora metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Contentum in torrentibus"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Bibliotheca Sinica"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads to AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadata Bohemica"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Libri"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Bibliotheca Publica Russica"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titulus"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Auctor"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Editor"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Editio"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Annus editus"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nomen originale fasciculi"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Descriptio et metadata commentarii"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads Socii Serveris ad tempus non praesto sunt pro hoc fasciculo."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Celer Socius Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recommendatur)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sine verificatione navigatri aut exspectatione)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Tardus Socius Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(paulo celerior sed cum exspectatione)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sine exspectatione, sed potest esse valde tardus)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fictio"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fictio"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(etiam preme “GET” in summo)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(preme “GET” in summo)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "eorum tabulae notae sunt continere malitiosum software, ita utere ad blocker vel non preme tabulas"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Fasciculi Nexus/STC possunt esse inaffidabiles ad detrahendum)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Bibliotheca in Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requirit Navigatrum Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Mutua ab Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(solum pro patronis cum defectu typographico)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI associatus fortasse non praesto in Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "collectio"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Torrentium massivae descensiones"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(solum peritis)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Quaere Archivum Annae pro ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Quaere varias alias databases pro ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Inveni recordum originale in ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Quaere Archivum Annae pro ID Bibliothecae Apertae"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Inveni recordum originale in Bibliotheca Aperta"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Quaere Archivum Annae pro numero OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Inveni recordum originale in WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Quaere Archivum Annae pro numero SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Quaere manualiter in DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Quaere Archivum Annae pro numero SSNO CADAL"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Inveni recordum originale in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Quaere Archivum Annae pro numero DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Archivum Annae 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(nulla verificatione navigatri necessaria)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata Cechica %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "descriptio"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nomen fasciculi alternum"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Titulus alternus"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Auctor alternus"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editor alternus"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Editio alterna"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extensio alterna"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata commentarii"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descriptio alterna"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "dies apertae fontis"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub fasciculus “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending fasciculus “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Hoc est recordum fasciculi ex Internet Archive, non fasciculus directe deponibilis. Librum mutuari potes (nexus infra), vel hunc URL utere cum <a %(a_request)s>fasciculum petis</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Si hunc fasciculum habes et nondum in Archivo Annae praesto est, considera <a %(a_request)s>eum onerare</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata recordum"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata recordum"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) numerus %(id)s metadata recordum"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata recordum"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Hoc est metadata recordum, non fasciculus downloadable. Hac URL uti potes cum <a %(a_request)s>fasciculum petis</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata ex archivo coniuncto"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Meliorem metadata in Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Monitio: plura coniuncta documenta:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Meliorem metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Qualitatem documenti refer"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Tempus download"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Pagina interretialis:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Quaere Archivum Annae pro “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorator Codicum:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vide in Exploratore Codicum “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lege plura…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Mutua (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Explora metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Commentarii (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Indices (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistica (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Singula technica"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Hic fasciculus quaestiones habere potest, et ex bibliotheca fonte absconditus est.</span> Interdum hoc fit rogatu possessoris iuris, interdum quia melior optio praesto est, sed interdum propter quaestionem ipsam fasciculi. Forsitan adhuc bene sit ad detrahendum, sed primum commendamus quaerere fasciculum alternum. Plura singula:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Melior versio huius fasciculi praesto esse potest apud %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Si adhuc vis hunc fasciculum detrahere, certus esto ut solum programmatibus fidis, recentibus utaris ad aperiendum."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Celeres descensiones"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Celeres detractiones</strong> Fiere <a %(a_membership)s>socius</a> ad sustentationem diuturnam librorum, chartarum, et plus. Ad gratias nostras pro tuo auxilio ostendendas, celeres detractiones accipis. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si hoc mense donaveris, accipies <strong>duplum</strong> numerum celerium downloadium."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Celeres detractiones</strong> Tibi %(remaining)s hodie remanent. Gratias quod socius es! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Celeres detractiones</strong> Hodie celeres detractiones exhauserunt."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Celeres detractiones</strong> Hunc fasciculum nuper detraxisti. Nexus manent validi per aliquod tempus."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Optio #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nulla redirectio)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(apere in visore)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Refer amicum, et uterque tu et amicus tuus accipitis %(percentage)s%% bonum celerium downloadium!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Disce plura…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Tarda downloadia"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Ex sociis fidelibus."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Plura informationes in <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(forsitan requirat <a %(a_browser)s>verificationem navigatoris</a> — infinita downloadia!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Postquam descenderis:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Aperire in nostro visore"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "ostende descensiones externas"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Externa downloadia"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nulla downloadia inventa."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Omnes optiones downloadii idem fasciculum habent, et tutum esse debent ad usum. Id dictum, semper cave cum fasciculos ex interrete downloadas, praesertim ex locis externis ad Archivum Annae. Exempli gratia, cura ut machinas tuas renovatas habeas."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Pro magnis fasciculis, commendamus utendi gestore download ad vitanda interruptiones."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Commendati gestores download: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Opus erit lectori ebook vel PDF ad fasciculum aperiendum, secundum formam fasciculi."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Commendati lectores ebook: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Annae Archivum visore online"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Utere instrumentis online ad convertendum inter formas."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Commendata instrumenta conversionis: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Potes mittere tam PDF quam EPUB fasciculos ad tuum Kindle vel Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Commendata instrumenta: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazonis “Mitte ad Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazzis “Mitte ad Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Sustine auctores et bibliothecas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si hoc tibi placet et id praestare potes, considera emere originale, vel directe auctores sustinere."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si hoc in bibliotheca tua locali praesto est, considera illud ibi gratis mutuari."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Qualitas documenti"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Auxilia communitati referendo qualitatem huius documenti! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Refer de problema documenti (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Magna qualitas documenti (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Adde commentarium (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Quid est iniurium cum hoc documento?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Quaeso utere <a %(a_copyright)s>DMCA / Formulario de iure auctoris</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Describere quaestionem (necessarium)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descriptio quaestionis"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 melioris versionis huius documenti (si applicabile)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Hoc imple si aliud documentum est quod huic documento proxime convenit (eadem editio, eadem extensio documenti si invenire potes), quod homines uti debent loco huius documenti. Si meliorem versionem huius documenti extra Archivum Annae nosti, quaeso <a %(a_upload)s>illud uploada</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "MD5 ex URL accipere potes, e.g."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Mitte relationem"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Disce quomodo <a %(a_metadata)s>metadata emendare</a> pro hoc archivo ipse."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Gratias tibi ago pro relatione tua submittenda. Ea in hac pagina ostendetur, atque manualiter a Anna recognoscetur (donec systema moderationis proprium habeamus)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Aliquid erravit. Paginae reload fac et iterum conare."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si hoc archivum magnam qualitatem habet, hic de eo quidvis disserere potes! Si non, quaeso utere puga \"Problema archivi referre\"."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Hunc librum amavi!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Commentarium relinquere"

#, fuzzy
msgid "common.english_only"
msgstr "Textus infra Anglice continuatur."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totalis downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "\"File MD5\" est hash qui ex contentis archivi computatur, et est rationabiliter unicus ex illo contento. Omnes bibliothecae umbrae quas hic indicavimus primario utuntur MD5s ad archivos identificandos."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Archivum potest apparere in multiplicibus bibliothecis umbrae. Pro informatione de variis datasets quos compilavimus, vide <a %(a_datasets)s>paginam Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Hoc est archivum a <a %(a_ia)s>IA’s Controlled Digital Lending</a> bibliotheca gestum, et a Anna’s Archive pro quaesitione indicatum. Pro informatione de variis datasets quos compilavimus, vide <a %(a_datasets)s>paginam Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pro informatione de hoc particulari archivo, inspice eius <a %(a_href)s>archivum JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Quaestio paginae huius onus"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Quaeso refricare ut iterum coneris. <a %(a_contact)s>Nobis contactum</a> si quaestio per plures horas perseverat."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Non inventum"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” in nostra database non inventum est."

#, fuzzy
msgid "page.login.title"
msgstr "Intra / Registrare"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Navigatrum verificatio"

#, fuzzy
msgid "page.login.text1"
msgstr "Ut spam-bots ne multa rationes creent, primum navigatrum tuum verificare debemus."

#, fuzzy
msgid "page.login.text2"
msgstr "Si in infinitum circulum capieris, commendamus <a %(a_privacypass)s>Privacy Pass</a> instituere."

#, fuzzy
msgid "page.login.text3"
msgstr "Etiam adiuvare potest ad blockers et alias extensiones navigatri exstinguere."

#, fuzzy
msgid "page.codes.title"
msgstr "Codices"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorator Codicum"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explora codices quibus tabulae notatae sunt, per praefixum. Columna “tabulae” ostendit numerum tabularum notatarum cum codicibus cum dato praefixo, ut visum est in machina quaerendi (inclusis tabulis solum metadata). Columna “codices” ostendit quot codices actu habent datum praefixum."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Haec pagina aliquantulum temporis generare potest, quare requirit Cloudflare captcha. <a %(a_donate)s>Membra</a> possunt captcham praeterire."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Quaeso ne has paginas radere. Proinde commendamus <a %(a_import)s>generare</a> vel <a %(a_download)s>downloadere</a> nostras ElasticSearch et MariaDB databases, et currere nostrum <a %(a_software)s>codicem apertum</a>. Data rudia manualiter explorari possunt per JSON files sicut <a %(a_json_file)s>hoc unum</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Praefixum"

#, fuzzy
msgid "common.form.go"
msgstr "I"

#, fuzzy
msgid "common.form.reset"
msgstr "Reponere"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Quaerere Annae Archivum"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Monitio: codex habet characteres Unicode incorrectos, et potest se male gerere in variis condicionibus. Binarium rudis potest decodificari ex base64 repraesentatione in URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Notum praefixum codicis “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Praefixum"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Titulus"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descriptio"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL pro specifico codice"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” substituentur cum valore codicis"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generalis URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s recordum congruentium “%(prefix_label)s”"
msgstr[1] "%(count)s recorda congruentia “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL pro codice specifico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Plus…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codices incipientes cum “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index of"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "recorda"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codices"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Minus quam %(count)s recorda"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pro DMCA / iuribus auctorum petitionibus, utere <a %(a_copyright)s>hoc forma</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Alii modi nos contactandi de petitionibus iurium auctorum statim delebuntur."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Valde gratum habemus tuas opiniones et quaestiones!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tamen, propter quantitatem spam et nugarum epistularum quas accipimus, quaeso cistas tange ut condiciones has contactandi nos intellegas."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Petitiones iurium auctorum ad hanc epistulam ignorabuntur; forma utere proinde."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Socii servitores non praesto sunt ob clausuras hospitii. Mox iterum praesto erunt."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Sodalitates proinde prorogabuntur."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Noli nobis epistulam mittere ad <a %(a_request)s>libros petendos</a><br>vel parvas (<10k) <a %(a_upload)s>uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Cum quaerendo rationem vel donationem quaestiones, addas ID rationis tuae, imagines, recepta, quantum informationis fieri potest. Epistulas nostras solum singulis 1-2 septimanis inspicimus, ita non includens hanc informationem moram in solutione faciet."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Monstra epistulam"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Formula iuris petitionis"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si habes petitionem DCMA vel aliam petitionem iuris, quaeso hanc formam quam accuratissime imple. Si quaestiones occurrunt, quaeso nos contacta ad nostram dedicatam DMCA inscriptionem: %(email)s. Nota quod petitiones ad hanc inscriptionem missae non processentur, solum ad quaestiones est. Quaeso utere forma infra ad petitiones tuas subiciendas."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs in Annae Archivis (necessaria). Una per lineam. Quaeso tantum includas URLs quae eandem editionem libri exacte describunt. Si vis petitionem pro pluribus libris vel pluribus editionibus facere, quaeso hanc formam pluries subice."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Petitiones quae plures libros vel editiones simul coniungunt reicientur."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Nomen tuum (necessarium)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Oratio (necessaria)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Numerus telephonicus (necessarius)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (necessarius)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Clara descriptio materiae fontis (necessaria)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs materiae fontis (si applicabile). Unus per lineam. Quaeso tantum includas illos qui editionem exacte respondent pro qua petitionem de iure auctoris referis."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs materiae fontis, unus per lineam. Quaeso paulisper quaere in Open Library pro tua materia fonte. Hoc nobis adiuvabit ut petitionem tuam confirmemus."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs ad materiam fontis, unus per lineam (necessarius). Quaeso quam plurimos includas, ut petitionem tuam confirmemus (e.g. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaratio et signatura (necessaria)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Petitionem submittere"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Gratias tibi agimus pro petitione tua de iure auctoris submissa. Illam quam primum recensere conabimur. Quaeso paginam recarga ut aliam submittere possis."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Aliquid erravit. Quaeso paginam recarga et iterum conare."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si datasetum speculum facere cupis pro <a %(a_archival)s>archivis</a> vel <a %(a_llm)s>LLM disciplina</a> causis, quaeso nos contacta."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Nostra missio est omnes libros in mundo (necnon chartas, ephemerides, etc.) archiviare, et eos late accessibiles facere. Credimus omnes libros late speculari debere, ut redundantiam et resilientiam curent. Hoc est cur archivos ex variis fontibus congerimus. Nonnulli fontes omnino aperti sunt et in mole speculari possunt (ut Sci-Hub). Alii clausi et protecti sunt, itaque eos radere conamur ut libros eorum \"liberemus\". Alii alicubi inter haec cadunt."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Omnia nostra data possunt <a %(a_torrents)s>torrentari</a>, et omnia nostra metadata possunt <a %(a_anna_software)s>generari</a> vel <a %(a_elasticsearch)s>downloadari</a> ut ElasticSearch et MariaDB databases. Data rudia manualiter explorari possunt per JSON archivos ut <a %(a_dbrecord)s>hoc</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Overview"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Infra est celer overview fontium archivorum in Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fons"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Magnitudo"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% speculatum ab AA / torrents praesto"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentationes numeri fileorum"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ultima renovatio"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fictio et Fictio"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s archivum"
msgstr[1] "%(count)s archiva"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Per Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelatum ab anno 2021; plerumque per torrents praesto"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: minores additiones ex illo tempore</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excludens “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fictionis torrents post tergum sunt (quamquam IDs ~4-6M non torrentur quia cum nostris Zlib torrents coincidunt)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Collectio “Sinica” in Z-Library videtur eadem esse ac nostra collectio DuXiu, sed cum diversis MD5s. Excludimus hos files a torrents ne duplicatio fiat, sed adhuc ostendimus eos in nostro indice quaestionis."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Imperium Digitalis Mutuatio"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ fasciculorum quaeribiles sunt."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Summa"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excludens duplicata"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Cum bibliothecae umbrae saepe data inter se synchronizant, magna est superpositio inter bibliothecas. Ideo numeri non ad summam addunt."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Procentatio “speculata et seminata ab Archivo Annae” ostendit quot fasciculi ipsi speculamur. Hos fasciculos in mole per torrents seminamus, et eos per websites socios directe ad download praebemus."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliothecae fontium"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Nonnullae bibliothecae fontes promovent communicationem massam suorum datorum per torrentes, dum aliae non facile communicant collectionem suam. In hoc casu, Archivum Annae conatur colligere collectiones eorum, et eas praebere (vide paginam nostram <a %(a_torrents)s>Torrentes</a>). Sunt etiam casus intermediarii, exempli gratia, ubi bibliothecae fontes sunt paratae communicare, sed non habent facultates ad id faciendum. In his casibus, nos quoque conamur adiuvare."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Infra est synopsis quomodo interfaciemus cum diversis bibliothecis fontibus."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fons"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Files"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Cotidiana <a %(dbdumps)s>HTTP database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automata torrentium pro <a %(nonfiction)s>Non-Fiction</a> et <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annae Archivum collectionem <a %(covers)s>torrentium operculorum librorum</a> administrat"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub novos files ab anno 2021 congelavit."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadatae congeries praesto <a %(scihub1)s>hic</a> et <a %(scihub2)s>hic</a>, necnon pars <a %(libgenli)s>Libgen.li database</a> (quam utimur)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Datae torrents praesto <a %(scihub1)s>hic</a>, <a %(scihub2)s>hic</a>, et <a %(libgenli)s>hic</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Nonnulli novi files <a %(libgenrs)s>adduntur</a> ad Libgen’s “scimag”, sed non satis ad novos torrents faciendos"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Trimestrales <a %(dbdumps)s>HTTP database congeries</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiction torrents cum Libgen.rs communicantur (et speculum <a %(libgenli)s>hic</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annae Archivum et Libgen.li collaborative collectiones <a %(comics)s>librorum comicorum</a>, <a %(magazines)s>ephemeridum</a>, <a %(standarts)s>documentorum standardium</a>, et <a %(fiction)s>fictionis (a Libgen.rs divisae)</a> administrant."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Eorum collectio “fiction_rus” (fictionis Russicae) nullos torrentes dedicatos habet, sed a torrentibus aliorum tegitur, et speculum <a %(fiction_rus)s>tenemus</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annae Archivum et Z-Library collaborative collectionem <a %(metadata)s>Z-Library metadatae</a> et <a %(files)s>Z-Library files</a> administrant"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Nonnulla metadata praesto per <a %(openlib)s>Open Library database congeries</a>, sed non totam collectionem IA comprehendunt"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nullae metadatae congeries facile accessibiles pro tota eorum collectione"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annae Archivum collectionem <a %(ia)s>IA metadatae</a> administrat"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Files tantum ad mutuum dandum limitate praesto, cum variis restrictionibus accessus"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annae Archivum collectionem <a %(ia)s>IA files</a> administrat"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Variae metadatae databases per interrete Sinense dispersae; saepe tamen databases solutae"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nullae metadatae congeries facile accessibiles pro tota eorum collectione."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annae Archivum collectionem <a %(duxiu)s>DuXiu metadatae</a> administrat"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Variae file databases per interrete Sinense dispersae; saepe tamen databases solutae"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Plerique files tantum accessibiles utendo rationibus premium BaiduYun; tardae velocitates downloadendi."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annae Archivum collectionem <a %(duxiu)s>DuXiu fasciculorum</a> administrat"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Varii minores vel unici fontes. Hortamur homines ut primum ad alias bibliothecas umbraticas fasciculos suos mittant, sed interdum homines collectiones habent quae nimis magnae sunt ut alii percurrant, quamquam non satis magnae ut propriam categoriam mereantur."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fontes metadata tantum"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Nos quoque locupletamus collectionem nostram cum fontibus metadata tantum, quos possumus coniungere ad files, e.g. utendo numeris ISBN vel aliis campis. Infra est synopsis eorum. Iterum, nonnulli ex his fontibus sunt omnino aperti, dum alii colligendi sunt."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Inspiratio nostra ad metadata colligenda est propositum Aaronis Swartz de “una pagina interretiali pro unoquoque libro umquam edito”, pro quo creavit <a %(a_openlib)s>Open Library</a>. Illud propositum bene successit, sed nostra singularis positio nobis permittit metadata obtinere quae illi non possunt. Alia inspiratio fuit desiderium nostrum scire <a %(a_blog)s>quot libri in mundo sint</a>, ut computare possimus quot libros adhuc servare debeamus."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nota quod in quaerendo metadata, ostendimus records originales. Non facimus ullam coniunctionem recordorum."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ultima renovatio"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Menstrua <a %(dbdumps)s>databasis effusionum</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Non directe in mole praesto, contra scrutinium protecta"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annae Archivum collectionem <a %(worldcat)s>OCLC (WorldCat) metadata</a> administrat"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Database unificata"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Coniungimus omnes fontes supra in unam database unificatam quam utimur ad servandum hunc locum. Haec database unificata non est directe praesto, sed cum Archivum Annae sit omnino aperta fons, potest satis facile <a %(a_generated)s>generari</a> vel <a %(a_downloaded)s>downloadari</a> ut databases ElasticSearch et MariaDB. Scripturae in illa pagina automatice downloadabunt omnia metadata necessaria ex fontibus supra memoratis."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si vis explorare data nostra antequam illa scriptura localiter curras, potes inspicere files JSON nostros, qui ulterius coniungunt ad alios files JSON. <a %(a_json)s>Hic file</a> est bonum initium."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptatum ex nostro <a %(a_href)s>blogo post</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> est ingens database librorum scannatorum, creatus a <a %(superstar_link)s>SuperStar Digital Library Group</a>. Plerique sunt libri academici, scannati ut digitaliter praesto sint universitatibus et bibliothecis. Pro nostra Anglice loquenti audientia, <a %(princeton_link)s>Princeton</a> et <a %(uw_link)s>Universitas Washington</a> bonas recensiones habent. Est etiam praeclarus articulus plus contextum praebens: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Libri ex Duxiu diu in interrete Sinensi piratati sunt. Saepe venduntur minus quam dollario a venditoribus. Typice distribuuntur utentes Sinensi aequivalente Google Drive, quod saepe hackatum est ut plus spatii recondendi permittat. Quaedam technicae notitiae inveniri possunt <a %(link1)s>hic</a> et <a %(link2)s>hic</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Quamvis libri semi-publice distributi sint, difficile est eos in mole obtinere. Hoc in nostro TODO-elenco alte posuimus, et plures menses operis pleni temporis ad id destinavimus. Tamen, in fine 2023 incredibilis, mirabilis, et ingeniosus voluntarius nobis accessit, dicens se totum hoc opus iam perfecisse — magno impendio. Totam collectionem nobiscum communicavit, nihil expectans nisi longaeva conservatio. Vere mirabile."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resources"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totalis fasciculorum: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totalis magnitudo fasciculorum: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Fasciculi speculati ab Annae Archivis: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ultima renovatio: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrentes ab Annae Archivis"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplum monumenti in Annae Archivis"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Noster blogo post de hac data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripturae ad metadata importanda"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Annae Archivorum Containera forma"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Plura notitiae a nostris voluntariis (crudae notae):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Hoc dataset arcte coniunctum est cum <a %(a_datasets_openlib)s>Open Library dataset</a>. Continet scrutinium omnium metadatorum et magnam partem fasciculorum ex Bibliotheca Digitali Controllata IA. Renovationes emittuntur in <a %(a_aac)s>Annae Archivorum Containera forma</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Haec monumenta directe referuntur ex Open Library dataset, sed etiam continet monumenta quae non sunt in Open Library. Etiam habemus numerum fasciculorum datarum a communitatis membris per annos scrutatorum."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Collectio constat ex duabus partibus. Utrumque partes necessariae sunt ad omnes datos obtinendos (exceptis torrentibus obsoletis, qui in pagina torrentium excluduntur)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "nostra prima emissio, antequam in <a %(a_aac)s>Annae Archivorum Containera (AAC) forma</a> standardizavimus. Continet metadata (ut json et xml), pdfs (ex acsm et lcpdf systematibus digitalis mutui), et thumbnails operculorum."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "incrementales novae emissiones, utentes AAC. Tantum continet metadata cum timestamps post 2023-01-01, cum reliqua iam a \"ia\" tegantur. Etiam omnes fasciculi pdf, hoc tempore ex acsm et \"bookreader\" (IA's web reader) systematibus mutui. Quamvis nomen non sit prorsus rectum, adhuc fasciculos bookreader in collectionem ia2_acsmpdf_files includimus, cum sint mutuo exclusivi."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Main %(source)s website"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Bibliotheca Digitalis Mutui"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentatio metadata (pleraque campi)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN patriae notitia"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Internationalis ISBN Agency regulariter emittit intervalla quae nationalibus ISBN agenciis attribuit. Ex hoc derivare possumus cuius patriae, regionis, vel coetus linguarum hic ISBN pertineat. Hoc tempore hanc notitiam indirecte utimur, per <a %(a_isbnlib)s>isbnlib</a> Python bibliothecam."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Opes"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultima renovatio: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN situs"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Pro historia diversorum Library Genesis ramorum, vide paginam pro <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li continet plerumque eadem contenta et metadata sicut Libgen.rs, sed habet nonnullas collectiones insuper, scilicet fumetti, ephemerides, et documenta standard. Etiam integravit <a %(a_scihub)s>Sci-Hub</a> in suum metadata et machinam quaerendi, quod utimur pro nostra database."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadata huius bibliothecae libere praesto est <a %(a_libgen_li)s>ad libgen.li</a>. Tamen, hic servitor tardus est et non sustinet resumptiones connexiones interruptas. Eadem files etiam praesto sunt in <a %(a_ftp)s>FTP servo</a>, qui melius operatur."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrentes pro plerisque contentis additis praesto sunt, notabiliter torrentes pro comicis, ephemeridibus, et documentis standardibus in collaboratione cum Annae Archivo dimissi sunt."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Collectio fictionis proprias habet torrentes (divergentes ab <a %(a_href)s>Libgen.rs</a>) incipientes a %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Secundum administratorem Libgen.li, collectio \"fiction_rus\" (fictionis Russicae) debet comprehendi per torrentes regulariter emissos ex <a %(a_booktracker)s>booktracker.org</a>, praesertim <a %(a_flibusta)s>flibusta</a> et <a %(a_librusec)s>lib.rus.ec</a> torrentes (quos speculamus <a %(a_torrents)s>hic</a>, quamquam nondum constitutum est qui torrentes quibus filebus respondeant)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistica omnium collectionum inveniri possunt <a %(a_href)s>in libgen's website</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fiction quoque videtur discessisse, sed sine novis torrentibus. Videtur hoc accidisse ab initio anni 2022, quamquam hoc non confirmavimus."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certae amplitudines sine torrentibus (ut fictionis amplitudines f_3463000 ad f_4260000) verisimiliter sunt Z-Library (vel aliae duplicatae) files, quamquam fortasse deduplicationem facere volumus et torrentes pro lgli-uniquis filebus in his amplitudinibus creare."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Nota quod torrentium fasciculi referentes ad “libgen.is” sunt explicite specula <a %(a_libgen)s>Libgen.rs</a> (“.is” est diversum dominium quod Libgen.rs utitur)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Utilis res in usu metadata est <a %(a_href)s>haec pagina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fictionis torrentes in Archivo Annae"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Comicorum torrentes in Archivo Annae"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Ephemeridum torrentes in Archivo Annae"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrentes documentorum standardium in Annae Archivum"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrentes fictionis Russicae in Annae Archivum"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata per FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata campi informationes"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Speculum aliorum torrentium (et unici fictionis et comicorum torrentes)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Disputationis forum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Noster blog post de comicis libris emissione"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Brevis fabula de diversis Library Genesis (vel “Libgen”) ramis, est quod per tempus, diversi homines implicati cum Library Genesis dissensum habuerunt, et separatim iverunt."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Versio “.fun” creata est ab originali conditore. Renovatur in favorem novae, magis distributae versionis."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Versio “.rs” habet data valde similia, et constanter emittit collectionem suam in torrentibus massis. Dividitur fere in sectionem “fictionis” et “non-fictionis”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originaliter apud “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Versio “.li”</a> habet ingentem collectionem comicorum, necnon aliorum contentorum, quae non (adhuc) praesto sunt pro massa download per torrentes. Habent separatum collectionem torrentium librorum fictionis, et continet metadata <a %(a_scihub)s>Sci-Hub</a> in sua database."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Secundum hunc <a %(a_mhut)s>forum post</a>, Libgen.li originaliter hospitatus est apud “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> quodammodo etiam est ramus Library Genesis, quamvis diverso nomine pro suo proposito usi sint."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Haec pagina est de versione “.rs”. Nota est pro constanter edendo et metadata et plenam contenta sui catalogi librorum. Collectio librorum dividitur inter fictionis et non-fictionis partem."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Utilis res in usu metadata est <a %(a_metadata)s>haec pagina</a> (intercludit IP intervalla, VPN fortasse requiritur)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Ab anno 2024-03, novi torrents in <a %(a_href)s>hoc filo fori</a> ponuntur (intervalla IP obstruit, VPN necessarius esse potest)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrentes Non-Fictionis in Archivum Annae"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrentes Fictionis in Archivum Annae"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Metadata Libgen.rs campi informationes"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrentes Non-Fictionis Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrentes Fictionis Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum Disputationis Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrentes per Archivum Annae (tegumenta librorum)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Noster blog de emissione tegumentorum librorum"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis notum est quod iam data sua generose in mole per torrents praebet. Collectio nostra Libgen consistit ex data auxiliaribus quae directe non emittunt, in societate cum eis. Multas gratias omnibus qui cum Library Genesis operantur!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Emissio 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Haec <a %(blog_post)s>prima emissio</a> parva est: circa 300GB tegumentorum librorum ex Libgen.rs ramificatione, tam fictionis quam non-fictionis. Eodem modo ordinantur quo in libgen.rs apparent, e.g.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pro libro non-fictionis."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pro libro fictionis."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Sicut cum collectione Z-Library, omnes in magno .tar archivo posuimus, quod uti <a %(a_ratarmount)s>ratarmount</a> montari potest si vis files directe servire."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> est database proprietarium a non-lucrativo <a %(a_oclc)s>OCLC</a>, quod metadata records ex bibliothecis toto orbe aggregat. Verisimile est maxima collectio metadata bibliothecarum in mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octobris 2023, initium emissionis:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Mense Octobri 2023 <a %(a_scrape)s>dimisimus</a> comprehensivam scrutinium database OCLC (WorldCat), in <a %(a_aac)s>formato Containerae Archivi Annae</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrentes a Archivo Annae"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Noster blog post de hac data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library est projectum apertum a Internet Archive ad catalogandum omnem librum in mundo. Unam ex maximis operationibus scansionis librorum in mundo habet, et multos libros ad praestitum digitalem praesto habet. Catalogus metadata librorum eius libere ad download praesto est, et in Archivum Annae includitur (quamvis non in quaesitione, nisi si expresse quaeras pro Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Emissio 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Hoc est effusio multarum vocum ad isbndb.com per Septembrem 2022. Conati sumus omnes ISBN intervalla comprehendere. Sunt circiter 30.9 miliones recordationum. In eorum situ affirmant se actu habere 32.6 miliones recordationum, ita fortasse aliquas omisimus, vel <em>illi</em> aliquid male faciunt."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Responsa JSON fere cruda sunt ex eorum servo. Unum problema qualitatis notitiarum quod animadvertimus, est quod pro numeris ISBN-13 qui incipiunt cum praefixo diverso ab “978-”, adhuc includunt campum “isbn” qui simpliciter est numerus ISBN-13 cum primis 3 numeris abscissis (et digito verificationis recalculato). Hoc manifeste erratum est, sed sic videntur facere, ita non mutavimus."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Aliud potentiale problema quod occurrere potes, est quod campus “isbn13” duplicata habet, ita non potes uti ut clavem primariam in database. Campi “isbn13”+“isbn” coniuncti videntur unici esse."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pro informatione de Sci-Hub, quaeso refer ad eius <a %(a_scihub)s>situm officialem</a>, <a %(a_wikipedia)s>paginam Vicipaediae</a>, et hanc <a %(a_radiolab)s>interview in podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Nota quod Sci-Hub <a %(a_reddit)s>congelatum est ab anno 2021</a>. Antea congelatum erat, sed anno 2021 pauca milia chartarum addita sunt. Tamen, aliquot chartae limitatae ad collectiones “scimag” Libgen adduntur, quamquam non satis ad novas massivas torrentes merendas."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Metadata Sci-Hub utimur sicut a <a %(a_libgen_li)s>Libgen.li</a> in eius collectione “scimag” provisum est. Etiam dataset <a %(a_dois)s>dois-2022-02-12.7z</a> utimur."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Nota quod “smarch” torrentes <a %(a_smarch)s>depreciati sunt</a> et ideo in nostro indice torrentium non includuntur."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrentes in Archivum Annae"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata et torrentes"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrentes in Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrentes in Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Renovationes in Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pagina Vicipaediae"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Interview in podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Missa ad Annae Archivum"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Overview ex <a %(a1)s>pagina datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Varii minores vel unici fontes. Hortamur homines ut primum ad alias bibliothecas umbraticas fasciculos suos mittant, sed interdum homines collectiones habent quae nimis magnae sunt ut alii percurrant, quamquam non satis magnae ut propriam categoriam mereantur."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Collectio “missarum” in minores subcollectiones divisa est, quae in AACIDs et torrentium nominibus indicantur. Omnes subcollectiones primum contra collectionem principalem deduplicatae sunt, quamquam metadata “upload_records” JSON fasciculi adhuc multas referentias ad fasciculos originales continent. Non-libri fasciculi etiam ex plerisque subcollectionibus remoti sunt, et typice <em>non</em> notantur in “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Multae subcollectiones ipsae ex sub-sub-collectionibus constant (e.g. ex diversis fontibus originalibus), quae ut directoria in campis “filepath” repraesentantur."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Subcollectiones sunt:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcollectio"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notae"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "perlustrare"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "quaerere"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Ex <a %(a_href)s>aaaaarg.fail</a>. Videtur satis completa esse. Ex voluntario nostro “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Ex <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrente. Habent satis magnam superpositionem cum collectionibus chartarum existentium, sed paucae MD5 congruentiae, itaque totam servavimus."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape ex <q>iRead eBooks</q> (= phonetice <q>ai rit i-books</q>; airitibooks.com), a voluntario <q>j</q>. Respondet ad <q>airitibooks</q> metadata in <a %(a1)s><q>Alia metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Ex collectione <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Partim ex fonte originali, partim ex the-eye.eu, partim ex aliis speculis."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Ex privato website torrentium librorum, <a %(a_href)s>Bibliotik</a> (saepe “Bib” appellatur), cuius libri in torrentibus per nomen (A.torrent, B.torrent) conglobati et per the-eye.eu distributi sunt."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Ex voluntario nostro “bpb9v”. Pro magis informationibus de <a %(a_href)s>CADAL</a>, vide notas in nostra <a %(a_duxiu)s>pagina dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Plus ex voluntario nostro “bpb9v”, plerumque DuXiu files, necnon folder “WenQu” et “SuperStar_Journals” (SuperStar est societas post DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Ex voluntario nostro “cgiym”, textus Sinenses ex variis fontibus (subdirectoriis repraesentati), inclusis ex <a %(a_href)s>China Machine Press</a> (magnus editor Sinensis)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Collectiones non-Sinenses (subdirectoriis repraesentati) ex voluntario nostro “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape librorum de architectura Sinica, a voluntario <q>cm</q>: <q>Accepi id per vulnerabilitatem retis in domo editoria, sed illa foramen iam clausum est</q>. Respondet ad <q>chinese_architecture</q> metadata in <a %(a1)s><q>Alia metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libri ex domo editoria academica <a %(a_href)s>De Gruyter</a>, ex paucis magnis torrentibus collecti."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrapta ex <a %(a_href)s>docer.pl</a>, website Polonicae file sharing libris et aliis operibus scriptis dicata. Scrapta in fine 2023 a voluntario “p”. Metadata bona ex website originali non habemus (ne file extensiones quidem), sed filtravimus pro file librorum similes et saepe metadata ex file ipsis extrahere potuimus."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, directe ex DuXiu, collecta a voluntario “w”. Solum recentia DuXiu libri directe per ebooks praesto sunt, itaque plerique ex his recentia esse debent."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Reliqua DuXiu files ex voluntario “m”, quae non erant in DuXiu proprietario formato PDG (principale <a %(a_href)s>dataset DuXiu</a>). Ex multis fontibus originalibus collecta, infeliciter sine conservando illos fontes in filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape librorum eroticorum, a voluntario <q>do no harm</q>. Respondet ad <q>hentai</q> metadata in <a %(a1)s><q>Alia metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collectio scrapta ex editorio Iaponico Manga a voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Selecta iudicialia archiva Longquan</a>, provisa a voluntario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrapta <a %(a_href)s>magzdb.org</a>, socii Library Genesis (in pagina libgen.rs coniuncta) sed qui non voluerunt fasciculos suos directe praebere. Obtenta a voluntario “p” in fine 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Varii minores missae, nimis parvae ut propriam subcollectionem mereantur, sed ut directoria repraesentatae."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks ex AvaxHome, Russica pagina ad fasciculos communicandos."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivum diariorum et ephemeridum. Respondet ad <q>newsarch_magz</q> metadata in <a %(a1)s><q>Alia metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape ex <a %(a1)s>Philosophiae Documentorum Centro</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collectio voluntarii “o” qui libros Polonicos directe ex originalibus emissionibus (“scena”) websites collegit."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coniunctae collectiones <a %(a_href)s>shuge.org</a> a voluntariis “cgiym” et “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperialis Bibliotheca Trantoris”</a> (nomine ficticiae bibliothecae), scrutata in 2022 a voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-collectiones (ut directoria repraesentatae) a voluntario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (a <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, parva bibliotheca mea — woz9ts: “Hoc situs maxime intendit in communicandis altissimae qualitatis ebook fasciculis, quorum nonnulli a possessore ipso typis compositi sunt. Possessor <a %(a_arrested)s>captus est</a> in 2019 et aliquis collectionem fasciculorum quos communicavit fecit.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Reliquiae DuXiu fasciculi a voluntario “woz9ts”, quae non erant in forma proprietaria PDG DuXiu (adhuc convertenda ad PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrentes ab Archivum Annae"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library extractio"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library radices habet in communitate <a %(a_href)s>Library Genesis</a>, et initio cum eorum data incepit. Ex illo tempore, valde professionalizata est, et interfaciem multo recentiorem habet. Ideo plures donationes accipere possunt, tam pecuniarias ad suum locum interretialem emendandum, quam novas librorum donationes. Magnum collectionem, praeter Library Genesis, collegerunt."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Renovatio Februarii 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Nuper anno 2022, conditores Z-Library allegati comprehensi sunt, et dominiis auctoritates Civitatum Foederatarum capta sunt. Ex illo tempore, situs interretialis lente iterum online fit. Ignoratur quis nunc eam administrat."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Collectio ex tribus partibus constat. Originales paginae descriptionis pro primis duabus partibus infra conservantur. Omnes tres partes tibi necessariae sunt ut omnia data accipias (exceptis torrentibus superatis, quae in pagina torrentium exaratae sunt)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nostra prima emissio. Haec erat prima emissio quae tunc vocata est “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: secunda emissio, hoc tempore cum omnibus fasciculis in fasciculis .tar involutis."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: incrementales novae emissiones, utens <a %(a_href)s>Annae Archivum Containera (AAC) forma</a>, nunc in collaboratione cum Z-Library team emissae."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrentes ab Archivum Annae (metadata + contentum)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplum recordationis in Archivum Annae (originalis collectio)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplum recordationis in Archivum Annae (“zlib3” collectio)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Situs principalis"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor dominium"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blog post de Emissione 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blog post de Emissione 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib emissiones (originales paginae descriptionis)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Emissio 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Speculum initiale laboriose per annos 2021 et 2022 obtentum est. Hoc tempore paulum obsoletum est: statum collectionis in Iunio 2021 reflectit. Hoc in futuro renovabimus. Nunc in prima emissione edenda intendimus."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Cum Library Genesis iam conservatur cum torrentibus publicis, et includitur in Z-Library, deduplicationem basicam contra Library Genesis fecimus mense Iunio 2022. Ad hoc usi sumus MD5 hashes. Verisimile est multum duplicatum contentum in bibliotheca, sicut multiplices formae fasciculorum cum eodem libro. Hoc accurate deprehendere difficile est, itaque non facimus. Post deduplicationem remanemus cum plus quam 2 miliones fasciculorum, totum paene 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Collectio constat ex duabus partibus: MySQL “.sql.gz” dump metadatae, et 72 torrentibus fasciculis circiter 50-100GB singulis. Metadata continet data sicut relata a Z-Library website (titulus, auctor, descriptio, filetype), necnon actualem magnitudinem fasciculi et md5sum quem observavimus, quia interdum haec non congruunt. Videntur esse ranges fasciculorum pro quibus Z-Library ipsa metadata incorrecta habet. Possumus etiam fasciculos incorrecte downloadatos habere in aliquibus casibus isolatis, quos in futuro deprehendere et corrigere conabimur."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Magni torrentium fasciculi continent actualem datam librorum, cum Z-Library ID ut nomen fasciculi. Extensiones fasciculorum possunt reconstructi uti metadata dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Collectio est mixtura contenti non-fictionis et fictionis (non separata sicut in Library Genesis). Qualitas etiam late variat."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Haec prima emissio nunc plene praesto est. Nota quod torrentium fasciculi tantum per nostrum Tor speculum praesto sunt."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Emissio 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Omnes libros accepimus qui additi sunt ad Z-Library inter nostrum ultimum speculum et Augustum 2022. Etiam retrogressi sumus et aliquos libros scrutati sumus quos primo tempore omisimus. Omnino, haec nova collectio est circiter 24TB. Iterum, haec collectio deduplicata est contra Library Genesis, quia iam sunt torrentia praesto pro illa collectione."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Data simili modo ordinata sunt ad primam emissionem. Est MySQL “.sql.gz” dump metadatae, quae etiam includit omnia metadata ex prima emissione, ita superans eam. Etiam addidimus nonnullas novas columnas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: utrum hic fasciculus iam sit in Library Genesis, in collectione non-fictionis vel fictionis (congruens per md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in quo torrentio hic fasciculus sit."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: positum cum non potuimus librum downloadare."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Hoc ultimum tempore mentionem fecimus, sed ad clarificandum: “nomen fasciculi” et “md5” sunt actuales proprietates fasciculi, dum “nomen_fasciculi_relata” et “md5_relata” sunt quae scrutati sumus ex Z-Library. Interdum haec duo non congruunt, itaque utrumque inclusimus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pro hac emissione, collationem mutavimus ad “utf8mb4_unicode_ci”, quae compatibilis esse debet cum vetustioribus versionibus MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Data fasciculi similes sunt ad ultimum tempus, quamquam multo maiores sunt. Non potuimus creare multos minores torrentium fasciculos. “pilimi-zlib2-0-14679999-extra.torrent” continet omnes fasciculos quos in ultima emissione omisimus, dum alii torrentia sunt omnes novi ID ranges. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> Plurimos nostros torrentia nimis magnos fecimus, causando clientes torrentium laborare. Eos removimus et novos torrentia emisimus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> Adhuc nimis multi fasciculi erant, itaque eos in tar fasciculos involvimus et novos torrentia iterum emisimus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Emissio 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Hoc est unicus extra torrentium fasciculus. Non continet ullam novam informationem, sed habet nonnulla data quae computare potest aliquantulum temporis. Hoc facit commodum habere, quia downloadare hunc torrentium saepe citius est quam computare ab initio. Praesertim, continet SQLite indices pro tar fasciculis, ad usum cum <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Frequenter Interrogationes (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Quid est Archivum Annae?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Archivum Annae</span> est consilium non-lucrativum cum duobus propositis:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Conservatio:</strong> Omnem scientiam et culturam humanitatis servare.</li><li><strong>Accessus:</strong> Hanc scientiam et culturam omnibus in mundo praebere.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Omnis noster <a %(a_code)s>codex</a> et <a %(a_datasets)s>data</a> sunt omnino aperta fons."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Conservatio"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Libros, chartas, comoedias, ephemerides, et plura conservamus, haec materia ex variis <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothecis umbrae</a>, bibliothecis officialibus, et aliis collectionibus in unum locum colligendo. Omnia haec data in perpetuum conservantur, facili duplicandi modo in mole — per torrentes — resultans in multis exemplis circum orbem terrarum. Quaedam bibliothecae umbrae hoc iam faciunt (e.g. Sci-Hub, Library Genesis), dum Archivum Annae alias bibliothecas \"liberat\" quae distributionem molem non offerunt (e.g. Z-Library) aut omnino non sunt bibliothecae umbrae (e.g. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Haec lata distributio, cum codice aperto fonte coniuncta, nostrum locum interretialem ad deiectiones resilientem facit, et longam temporis conservationem scientiae et culturae humanitatis praestat. Plura de <a href=\"/datasets\">nostris datasetis</a> disce."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Existimamus nos circiter <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% librorum mundi conservasse</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Accessus"

#, fuzzy
msgid "page.home.access.text"
msgstr "Cum sociis laboramus ut nostrae collectiones facile et libere omnibus praesto sint. Credimus omnes ius habere ad sapientiam collectivam humanitatis. Et <a %(a_search)s>non ad detrimentum auctorum</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Horae downloadationes in ultimis 30 diebus. Horae mediocris: %(hourly)s. Diurnus mediocris: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Valde credimus in liberum fluxum informationis, et conservationem scientiae et culturae. Cum hoc motore quaerendi, in humeris gigantum aedificamus. Profunde laboramus hominum qui varias bibliothecas umbrae creaverunt, et speramus hunc motorem quaerendi eorum amplitudinem dilaturum esse."

#, fuzzy
msgid "page.about.text3"
msgstr "Ut de nostro progressu certiores fiatis, sequimini Annam in <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> aut <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pro quaestionibus et responsionibus, quaeso contactate Annam apud %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Quomodo adiuvare possum?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Sequimini nos in <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, aut <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Nuntiate de Archivo Annae in Twitter, Reddit, Tiktok, Instagram, apud tuum localem cafe aut bibliothecam, aut ubicumque vadis! Non credimus in custodiam portarum — si deicimur, alibi statim resurget, cum omnis noster codex et data omnino aperta fons sint.</li><li>3. Si potes, considera <a href=\"/donate\">donationem</a>.</li><li>4. Adiuvate <a href=\"https://translate.annas-software.org/\">translationem</a> nostri situs interretialis in diversas linguas.</li><li>5. Si es ingeniarius software, considera contribuere ad nostrum <a href=\"https://annas-software.org/\">apertum fontem</a>, aut seminare nostros <a href=\"/datasets\">torrentes</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Nunc etiam habemus canalem Matrix synchronum apud %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Si es investigator securitatis, possumus tuis artibus uti et pro offensione et defensione. Vide nostram <a %(a_security)s>Securitatem</a> paginam."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Quaerimus peritos in solutionibus pro mercatoribus anonymis. Potesne adiuvare nos addere modos donationis magis commodos? PayPal, WeChat, chartae doni. Si aliquem nosti, quaeso contacta nos."

#, fuzzy
msgid "page.about.help.text8"
msgstr "Semper quaerimus maiorem capacitatem servientis."

#, fuzzy
msgid "page.about.help.text9"
msgstr "Potes adiuvare nuntiando problemata fasciculorum, relinquendo commentarios, et creandi indices in hoc ipso situ. Potes etiam adiuvare <a %(a_upload)s>plures libros caricando</a>, vel corrigendo problemata fasciculorum vel formata librorum existentium."

#, fuzzy
msgid "page.about.help.text10"
msgstr "Crea vel adiuvare ad conservandum paginam Vicipaediae pro Archivum Annae in tua lingua."

#, fuzzy
msgid "page.about.help.text11"
msgstr "Quaerimus parvas, elegantes praeconia ponere. Si vis praeconium ponere in Archivum Annae, quaeso nos certiores fac."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Cupimus ut homines <a %(a_mirrors)s>specula</a> constituant, et nos hoc pecuniarie sustinebimus."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pro informatione ampliore de voluntariatu, vide nostram <a %(a_volunteering)s>Paginam de Voluntariatu & Bounties</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Cur downloads tam lentus sunt?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Literatim non habemus satis opes ut omnibus in mundo celeres descensiones praebeamus, quantumvis velimus. Si dives benefactor vellet hoc nobis praebere, id esset incredibile, sed usque ad tunc, conamur optime. Sumus non-lucrativus projectus qui vix se sustentare potest per donationes."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Hoc est cur duas systemata pro gratuitis descensionibus cum sociis nostris implementavimus: servientes communes cum tardis descensionibus, et paulo celeriores servientes cum exspectatione (ut numerum hominum simul descendentium minuant)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Habemus etiam <a %(a_verification)s>verificationem navigatoris</a> pro tardis descensionibus, quia aliter bots et scrapers abutentur eis, facientes res etiam tardiores pro legitimis usoribus."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Nota quod, cum utendo Tor Navigatro, fortasse debes accommodare tuas securitatis optiones. In infima optionum, quae vocatur “Standard”, provocatio Cloudflare turnstile succedit. In superioribus optionibus, quae vocantur “Safer” et “Safest”, provocatio deficit."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Ad magnas tabellas interdum tardae descensiones in medio frangere possunt. Suademus utendi administratore descensionis (ut JDownloader) ad magnas descensiones automatice resumendas."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donationes FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Num sodalitia automatice renovantur?</div> Sodalitia <strong>non</strong> automatice renovantur. Potes iungere quamdiu vel quam brevi tempore vis."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Possumne meam sodalitatem augere aut plures sodalitates obtinere?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Habesne alias solutiones solutionis?</div> Nunc non. Multi homines nolunt ut talia archiva exsistant, itaque cauti esse debemus. Si potes adiuvare nos ad alias (commodiores) solutiones solutionis tuto constituendas, quaeso contacta nos ad %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Quid significant intervalla per mensem?</div> Ad partem inferiorem intervalli pervenire potes omnes solutiones applicando, ut eligendo periodum longiorem quam mensem."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Quid facitis cum donationibus?</div> 100%% ad conservandum et faciendum accessibilem mundi scientiam et culturam. Nunc maxime expendimus in servientibus, repono, et latitudine. Nullus pecunia ad aliquos sodales personaliter it."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Possumne magnam donationem facere?</div> Id esset mirabile! Pro donationibus supra pauca milia dollariorum, quaeso directe contacta nos ad %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Possumne donationem facere sine sodalitate fieri?</div> Certe. Acceptamus donationes cuiuslibet quantitatis ad hunc Monero (XMR) locum: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Quomodo novos libros carico?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Aliter, potes eas ad Z-Library <a %(a_upload)s>hic</a> mittere."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pro parvis onerationibus (usque ad 10,000 fasciculos) quaeso eos ad utrumque %(first)s et %(second)s onera."

#, fuzzy
msgid "page.upload.text1"
msgstr "Nunc, suademus novos libros caricare ad Library Genesis forcas. Hic est <a %(a_guide)s>utilis dux</a>. Nota quod ambae forcae quas indicamus in hoc situ ex eodem systemate caricandi trahunt."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pro Libgen.li, certus esto ut primum in <a %(a_forum)s >foro eorum</a> cum nomine usoris %(username)s et tessera %(password)s logineris, et deinde ad eorum <a %(a_upload_page)s >paginam onerationis</a> redeas."

#, fuzzy
msgid "common.libgen.email"
msgstr "Si inscriptio tua electronica non operatur in foris Libgen, suademus utaris <a %(a_mail)s>Proton Mail</a> (gratis). Potes etiam <a %(a_manual)s>manu petere</a> ut ratio tua activetur."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Nota quod mhut.org certas IP intervalla obstruit, itaque VPN necessarius esse potest."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pro magnis oneribus (super 10,000 documenta) quae non accipiuntur a Libgen aut Z-Library, quaeso contactate nos apud %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Ad chartas academicas onerandas, quaeso etiam (praeter Library Genesis) onera ad <a %(a_stc_nexus)s>STC Nexus</a>. Optimae sunt bibliothecae umbrae pro novis chartis. Nondum eas integravimus, sed aliquando faciemus. Potes uti eorum <a %(a_telegram)s>bot onera in Telegram</a>, aut contactare inscriptionem in eorum nuntio affixo si nimis multa documenta habes ut hoc modo oneres."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Quomodo libros peto?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Hoc tempore, non possumus petitiones librorum accommodare."

#, fuzzy
msgid "page.request.forums"
msgstr "Rogationes tuas fac in foris Z-Library vel Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Noli nobis per epistulam electronicae libros tuos rogare."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Metadatasne colligis?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Certe colligimus."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Librum \"1984\" a Georgio Orwell delevi, venientne vigiles ad ianuam meam?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Noli nimis sollicitus esse, multi homines ex paginis a nobis coniunctis deponunt, et rarissime in difficultates incidunt. Tamen, ut tutus maneas, commendamus ut VPN (solutum) vel <a %(a_tor)s>Tor</a> (gratuitum) utaris."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Quomodo optiones quaestionis meae servo?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Optiones quas vis elige, cistam quaestionis vacuam serva, \"Quaere\" preme, et deinde paginam utens notae navigatri tui nota."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Habesne applicationem mobilem?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Applicationem mobilem officialem non habemus, sed hanc paginam ut applicationem instituere potes."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Menu trium punctorum in summo dextro angulo preme, et \"Adde ad Domum\" elige."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> \"Communica\" puga in imo preme, et \"Adde ad Domum\" elige."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Habesne API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Habemus unum stabilem JSON API pro membris, ad URL celerem deponendi: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentatio intra JSON ipsum)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pro aliis usibus, ut per omnes fasciculos nostros iterandum, quaestionem customatam aedificandum, et cetera, commendamus <a %(a_generate)s>generare</a> vel <a %(a_download)s>deponere</a> nostras databases ElasticSearch et MariaDB. Data rudia manualiter explorari possunt <a %(a_explore)s>per fasciculos JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Nostra lista torrentium rudium deponi potest ut <a %(a_torrents)s>JSON</a> quoque."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Volo adiuvare seminare, sed non multum spatii disci habeo."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Utere <a %(a_list)s>generatore lista torrentium</a> ut listam torrentium generes quae maxime indigent torrentibus, intra limites spatii tui."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrentes nimis lentes sunt; possumne data directe a te deponere?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ita, vide paginam <a %(a_llm)s>LLM data</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Possumne tantum subset fasciculorum, sicut tantum linguam vel argumentum particulare, deponere?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Responsum breve: non facile."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Responsum longum:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Plurimi torrentes fasciculos directe continent, quod significat te posse clientibus torrentium praecipere ut tantum necessarios fasciculos deponant. Ut determinare quos fasciculos deponere, potes <a %(a_generate)s>generare</a> metadata nostra, vel <a %(a_download)s>deponere</a> databases nostras ElasticSearch et MariaDB. Infeliciter, nonnullae collectiones torrentium .zip vel .tar fasciculos in radice continent, quo in casu totum torrentem deponere debes antequam singulos fasciculos seligere possis."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Habemus <a %(a_ideas)s>nonnullas ideas</a> pro casu posteriore tamen.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nulla instrumenta facilia adhibenda ad eliquandas torrentes adhuc praesto sunt, sed contributiones excipimus."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Quomodo duplicata in torrentibus tractatis?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Conamur minimam duplicationem vel superpositionem inter torrentes in hoc indice servare, sed hoc non semper effici potest, et valde dependet a consiliis bibliothecarum fontium. Pro bibliothecis quae suos torrentes emittunt, extra potestatem nostram est. Pro torrentibus a Archivo Annae emissis, deduplicamus tantum secundum MD5 hash, quod significat diversas versiones eiusdem libri non deduplicari."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Possumne indicem torrentium ut JSON accipere?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ita."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Non video PDFs vel EPUBs in torrentibus, tantum binarios fasciculos? Quid faciam?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Hi re vera sunt PDFs et EPUBs, tantum extensionem non habent in multis torrentibus nostris. Duo loca sunt ubi metadata pro torrentium fasciculis invenire potes, inclusis generibus/extensionibus fasciculorum:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Quaelibet collectio vel emissio suam metadata habet. Exempli gratia, <a %(a_libgen_nonfic)s>Libgen.rs torrentes</a> habent correspondens metadata database in situ Libgen.rs hospitatum. Typice ad pertinentes metadata fontes ex quaque collectionis <a %(a_datasets)s>pagina dataset</a> coniungimus."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Commendamus <a %(a_generate)s>generare</a> vel <a %(a_download)s>deponere</a> databases nostras ElasticSearch et MariaDB. Hae continent mappam pro quolibet recordo in Archivo Annae ad suos correspondentes torrentium fasciculos (si praesto sunt), sub “torrent_paths” in JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Cur non potest meus torrent clientem aperire nonnullos ex vestris torrentibus / magnetis nexus?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Nonnulli torrent clientem non sustinent magnas partes magnitudines, quas multi ex nostris torrentibus habent (pro recentioribus hoc non facimus amplius — quamvis secundum specificationes validum sit!). Itaque alium clientem experire si hoc occurras, aut querere ad creatores tui torrent clientem."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Habesne programmatum responsabilem divulgationis?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Gratum habemus investigatores securitatis vulnerabilitates in systematibus nostris quaerere. Magni fautores responsalis divulgationis sumus. Continge nos <a %(a_contact)s>hic</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Nunc non possumus praemia pro bugis offerre, nisi pro vulnerabilitatibus quae <a %(a_link)s>potentiam ad anonymitatem nostram compromittendam</a> habent, pro quibus praemia in $10k-50k range offerimus. Vellemus latiorem ambitum pro bugis praemiis in futuro offerre! Nota quod impetus socialis machinationis extra ambitum sunt."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si securitatem offensivam curas, et vis adiuvare ad archivandum scientiam et culturam mundi, certus esto nos contingere. Multae viae sunt quibus adiuvare potes."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Suntne plura subsidia de Archivo Annae?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog Annae</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regularia nuntia"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software Annae</a> — nostrum apertum fontem codicem"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Translate on Software Annae</a> — nostrum systema translationis"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — de data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominiis alternativis"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — plura de nobis (quaesumus adiuvate ut hanc paginam renovetis, aut creetis unam pro vestra lingua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Quomodo referam de violatione iuris auctoris?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nulla opera iuribus auctoris tuta hic hospitamus. Nos sumus machina quaerendi, et sic tantum indicem metadata quae iam publice praesto sunt. Cum ex his fontibus externis detrahitis, suademus ut leges in vestra iurisdictione consulatis respectu quid liceat. Non sumus responsabiles pro contentis ab aliis hospitatis."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si querelas habetis de eo quod hic videtis, optima optio est contactare originale website. Regulariter mutationes eorum in nostram datorum basem trahimus. Si vere putatis vos habere validam querelam DMCA quam respondere debemus, quaesumus implete <a %(a_copyright)s>formam querelae DMCA / Iuris auctoris</a>. Querelas vestras serio accipimus, et quam primum respondebimus."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Odio quomodo hoc propositum agitis!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Etiam omnes monere volumus quod totus noster codex et data sunt omnino aperta fonte. Hoc unicum est pro propositis sicut nostrum — nescimus ullum aliud propositum cum simili catalogo magno quod etiam plene aperta fonte sit. Valde gratum habemus quemlibet qui putat nos male agere nostrum propositum ut codicem et data nostra capiat et suam bibliothecam umbram constituat! Non dicimus hoc ex ira aut aliquid — vere putamus hoc fore mirabile quia elevaret omnes, et melius conservaret legatum humanitatis."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Habesne monitorium temporis activi?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Vide <a %(a_href)s>hoc praeclarum opus</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Quomodo libros vel alia materialia physica donem?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Mitte eos ad <a %(a_archive)s>Internet Archive</a>. Illi ea rite conservabunt."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Quis est Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Tu es Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Qui sunt libri tui dilecti?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Hic sunt nonnulli libri qui specialem significationem habent mundo bibliothecarum umbrarum et digitalis conservationis:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Celeres descensiones hodie exhauserunt."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Fiam membrum ut celeres descensiones utar."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Nunc sustinemus chartas doni Amazon, chartas crediti et debiti, crypto, Alipay, et WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Plena datorum basis"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libri, chartae, ephemerides, comica, tabulae bibliothecae, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Quaerere"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>suspendit</a> novorum chartarum uploadium."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB est continuatio Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accessus directus ad %(count)s chartas academicas"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Aperta"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Si es <a %(a_member)s>membrum</a>, verificatio navigatoris non requiritur."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archivum diuturnum"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Datasets in Archivum Annae adhibita sunt omnino aperta, et possunt in mole per torrentes speculari. <a %(a_datasets)s>Plura disce…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Magnopere adiuvare potes seminando torrentes. <a %(a_torrents)s>Plura disce…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seminatores"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seminatores"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seminatores"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Data disciplinae LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Habemus maximam collectionem textuum altae qualitatis in mundo. <a %(a_llm)s>Plura disce…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Specula: vocatio ad voluntarios"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Voluntarios quaerimus"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Ut projectum non-lucrativum et aperti fontis, semper quaerimus homines ad adiuvandum."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Si processorem solutionis anonymum altum periculum geris, quaeso contactum nobis. Etiam quaerimus homines qui parvas et elegantes tabulas collocare volunt. Omnes proventus ad conatus nostros conservandos destinantur."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog Annae ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS downloads"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Omnes nexus ad hunc fasciculum deponendum: <a %(a_main)s>Pagina principalis fasciculi</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Porta #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(forsitan necesse erit pluries conari cum IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Ad celerius deponenda et ad probationes navigatoris praetereundas, <a %(a_membership)s>socius fies</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Ad speculandum molem collectionis nostrae, inspice <a %(a_datasets)s>Datasets</a> et <a %(a_torrents)s>Torrentes</a> paginas."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM data"

#, fuzzy
msgid "page.llm.intro"
msgstr "Bene intellectum est LLMs in data altae qualitatis florere. Habemus maximam collectionem librorum, chartarum, ephemeridum, etc. in mundo, quae sunt ex fontibus textus altissimae qualitatis."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Singularis magnitudo et amplitudo"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Nostra collectio continet plus centum miliones tabellarum, inclusis actis academicis, libris scholaribus, et ephemeridibus. Hanc magnitudinem assequimur coniungendo magnas repositoria exsistentia."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Nonnullae ex nostris collectionibus fontium iam in mole praesto sunt (Sci-Hub, et partes Libgen). Alios fontes nos ipsi liberavimus. <a %(a_datasets)s>Datasets</a> plenam recensionem ostendit."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Nostra collectio includit milia librorum, chartarum, et ephemeridum ante aetatem e-libri. Magna pars huius collectionis iam OCR facta est, et iam parum internum superfluum habet."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Quomodo adiuvare possumus"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Possumus praebere accessum celerem ad nostras plenarias collectiones, necnon ad collectiones nondum emissas."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Hoc est accessus ad gradum negotii quem possumus praebere pro donationibus in ordine decem milium USD. Parati sumus etiam hoc commutare pro collectionibus altae qualitatis quas nondum habemus."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Possumus te refundere si nobis praebere potes cum locupletatione nostrae datae, ut:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Removendo superfluum (deduplicatio)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extractio textus et metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Sustine longum tempus archivum scientiae humanae, dum meliora data pro tuo exemplari obtines!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contacta nos</a> ut discutiamus quomodo simul operari possimus."

#, fuzzy
msgid "page.login.continue"
msgstr "Perge"

#, fuzzy
msgid "page.login.please"
msgstr "Quaeso <a %(a_account)s>intra</a> ut hanc paginam videas.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Archivum Annae ad tempus ob conservationem clausum est. Redi post horam."

#, fuzzy
msgid "page.metadata.header"
msgstr "Metadata emendare"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Potes adiuvare conservationem librorum metadata emendando! Primum, lege de metadata in Archivum Annae, deinde disce quomodo metadata emendare per coniunctionem cum Open Library, et merere liberam sodalitatem in Archivum Annae."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Contextus"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Cum librum in Archivum Annae spectas, varias agros videre potes: titulum, auctorem, editorem, editionem, annum, descriptionem, nomen fasciculi, et plura. Omnes illae informationes appellantur <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Cum libros ex variis <em>bibliothecis fontibus</em> coniungimus, ostendimus quaecumque metadata in illa bibliotheca fonte praesto sunt. Exempli gratia, pro libro quem ex Library Genesis accepimus, titulum ex database Library Genesis ostendemus."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Interdum liber in <em>multis</em> bibliothecis fontibus praesens est, quae diversa metadata agros habere possunt. In eo casu, simpliciter ostendimus longissimam versionem cuiusque agri, quia illa probabiliter utilissimam informationem continet! Alia tamen agros infra descriptionem ostendemus, e.g. ut \"titulus alternus\" (sed tantum si diversa sunt)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Etiam <em>codices</em> ut identificatores et classifiers ex bibliotheca fonte extrahimus. <em>Identificatores</em> unice repraesentant particularem editionem libri; exempla sunt ISBN, DOI, Open Library ID, Google Books ID, vel Amazon ID. <em>Classificatores</em> plures similes libros coniungunt; exempla sunt Dewey Decimal (DCC), UDC, LCC, RVK, vel GOST. Interdum hi codices explicite in bibliothecis fontibus coniunguntur, et interdum eos ex nomine fasciculi vel descriptione extrahere possumus (praesertim ISBN et DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Possumus identificatores uti ad inventa in <em>metadata tantum collectionibus</em> invenienda, ut OpenLibrary, ISBNdb, vel WorldCat/OCLC. Est specifica <em>metadata tab</em> in nostro quaerendi machina si vis illas collectiones perlustrare. Utemur recordis congruentibus ad agros metadata carentes implendos (e.g. si titulus deest), vel e.g. ut \"titulus alternus\" (si titulus exsistit)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Ut videas exacte unde metadata libri venerit, vide <em>“Technical details” tab</em> in pagina libri. Habet nexum ad raw JSON pro illo libro, cum indiciis ad raw JSON originalium recordorum."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Pro plura informatione, vide sequentes paginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, et <a %(a_example)s>Example metadata JSON</a>. Denique, omnia nostra metadata possunt <a %(a_generated)s>generari</a> vel <a %(a_downloaded)s>downloadari</a> ut ElasticSearch et MariaDB databases."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library coniunctio"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Si ergo fasciculum cum mala metadata invenis, quomodo id corrigere debes? Potes ad bibliothecam fontem ire et eius proceduras ad metadata corrigenda sequi, sed quid si fasciculus in multis bibliothecis fontibus praesens est?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Est unus identifier qui specialiter in Archivum Annae tractatur. <strong>Annarum_archivum md5 ager in Open Library semper omnia alia metadata superat!</strong> Recedamus paulum et discamus de Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library condita est anno 2006 ab Aaron Swartz cum proposito \"una pagina interretialis pro omni libro umquam edito\". Est quasi Wikipedia pro metadata librorum: omnes eam emendare possunt, libere licentiata est, et in mole downloadari potest. Est database librorum quae maxime cum nostra missione congruit — re vera, Archivum Annae inspiratum est a visione et vita Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Pro rota non reinventanda, decrevimus nostros voluntarios ad Open Library dirigere. Si librum vides qui metadata incorrecta habet, potes adiuvare hoc modo:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Vade ad <a %(a_openlib)s>Open Library website</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Inveni rectum recordum libri. <strong>MONITUM:</strong> certus esto ut rectam <strong>editionem</strong> seligas. In Open Library, habes \"opera\" et \"editiones\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "\"Opus\" esse potest \"Harry Potter et Philosophi Lapis\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "\"Editio\" esse potest:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Editio prima anni 1997 a Bloomsbery edita cum 256 paginis."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Editio anni 2003 in charta molli a Raincoast Books edita cum 223 paginis."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Translatio Polonica anni 2000 “Harry Potter I Kamie Filozoficzn” a Media Rodzina cum 328 paginis."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Omnes hae editiones diversos ISBNs et diversos contentus habent, ergo certus esto ut rectam eligas!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Recensere recordum (vel creare si nullus existit), et addere quantum utilem informationem potes! Hic es nunc, ergo fac recordum vere mirabilem."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sub “ID Numbers” selige “Anna’s Archive” et adde MD5 libri ex Anna’s Archive. Haec est longa series litterarum et numerorum post “/md5/” in URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Conare invenire alias tabulas in Anna’s Archive quae etiam huic recordo congruunt, et eas quoque adde. In futuro possumus eas ut duplicata in pagina quaestionis Anna’s Archive coetus."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Cum perfeceris, scribe URL quem modo renovasti. Cum saltem 30 records cum Anna’s Archive MD5s renovaveris, mitte nobis <a %(a_contact)s>epistulam electronicam</a> et mitte nobis indicem. Tibi dabimus liberam sodalitatem pro Anna’s Archive, ut facilius hoc opus facere possis (et ut gratias tibi agamus pro auxilio tuo). Haec debent esse emendationes altae qualitatis quae substantialem quantitatem informationis addunt, alioquin petitio tua reicietur. Petitio tua etiam reicietur si aliqua emendationum a moderatoribus Open Library revocetur vel corrigatur."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Nota hoc tantum operari pro libris, non pro scriptis academicis vel aliis generibus tabularum. Pro aliis generibus tabularum adhuc commendamus ut fontem bibliothecam invenias. Potest capere paucos hebdomades ut mutationes in Anna’s Archive includantur, quia necesse est ut novissimum Open Library data dump downloadamus, et indicem quaestionis nostram regeneremus."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Specula: vocatio ad voluntarios"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Ad augendam resilientiam Annae Archivum, quaerimus voluntarios ad specula currenda."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Hoc quaerimus:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Tu curris codicem apertum Annae Archivum, et regulariter tam codicem quam data renovas."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Versio tua clare distinguitur ut speculum, e.g. “Archivum Bobi, speculum Archivi Annae”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Paratus es pericula huius operis suscipere, quae significantia sunt. Habes profundam intelligentiam securitatis operationalis necessariae. Contenta <a %(a_shadow)s>horum</a> <a %(a_pirate)s>postium</a> tibi sunt manifesta."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Paratus es ad nostram <a %(a_codebase)s>codicem</a> conferre — in collaboratione cum nostro team — ut hoc fiat."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Initio accessum ad nostros socii servientis downloads non dabimus, sed si res bene se habent, id tecum communicare possumus."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Expensae hospitii"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Parati sumus expensas hospitii et VPN tegere, initio usque ad $200 per mensem. Hoc sufficit pro serviente quaerendi basico et pro procuratore DMCA-protecto."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Solum pro hospitio solvemus cum omnia parata habueris, et demonstraveris te archivum cum renovationibus ad diem servare posse. Hoc significat te pro primis 1-2 mensibus ex tuo sumptu solvere debere."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Tempus tuum non compensabitur (nec nostrum), cum hoc sit opus voluntarium purum."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si significanter in evolutione et operationibus nostri operis implicaris, possumus de plus reditus donationum tecum communicando agere, ut necessaria deployes."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Incipiens"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Quaeso <strong>noli nos contactare</strong> ut licentiam petas, aut pro quaestionibus basicis. Actiones loquuntur clarius quam verba! Omnes informationes ibi sunt, itaque procedas cum speculo tuo instituendo."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Libenter tesseras vel petitiones coniunctionis ad nostrum Gitlab postas cum difficultatibus occurras. Fortasse necesse erit aliquas functiones speculo-specificas tecum aedificare, ut rebranding ab “Anna’s Archive” ad nomen tui situs, (initio) rationes usoris disabilitans, aut ad nostrum situm principale ex paginis librorum coniungens."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Cum speculum tuum currens habueris, quaeso nos contacta. Amamus tuam OpSec recensere, et cum id solidum sit, ad speculum tuum coniungemus, et arctius tecum laborare incipiemus."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Gratias in antecessum omnibus qui hoc modo contribuere volunt! Non est pro infirmis cordis, sed solidaret diuturnitatem maximae vere apertae bibliothecae in historia humana."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Ex website socio download"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Tardae descensiones tantum per situm officialem praesto sunt. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Tardae descensiones per VPNs Cloudflare vel aliter ex IPs Cloudflare non praesto sunt."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Exspecta quaeso <span %(span_countdown)s>%(wait_seconds)s</span> secundas ut hunc fasciculum deponas."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Utere sequenti URL ad deponendum: <a %(a_download)s>Nunc deponere</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Gratias quod exspectas, hoc situm omnibus gratis accessibilem servat! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Monitio: multa deponenda ex IP tua in ultimis 24 horis fuerunt. Descensiones tardiores esse possunt quam solitum."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descensiones ex IP tua in ultimis 24 horis: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si VPN uteris, nexum interretialem communem, vel ISP tuus IPs communicat, haec monitio ex eo fieri potest."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Ut omnibus facultas detur fasciculos gratis deponendi, necesse est te exspectare antequam hunc fasciculum deponere possis."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Libere perge navigare Archivum Annae in alia scheda dum exspectas (si navigatrum tuum tabulas in fundo reficere sustinet)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Libere exspecta ut plures paginae descensionis simul onerentur (sed quaeso tantum unum fasciculum simul per servitorem deponas)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Cum nexum descensionis obtines, is valet per plures horas."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Archivum Annae"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Recordum in Archivo Annae"

#, fuzzy
msgid "page.scidb.download"
msgstr "Deponere"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Ad humanam cognitionem accessibilem et diuturnam servandam, fiam <a %(a_donate)s>socius</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Praemium, 🧬&nbsp;SciDB citius pro sociis onerat, sine ullis limitibus."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Non operatur? Conare <a %(a_refresh)s>reficere</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Nondum praevia praesto est. Filem de <a %(a_path)s>Archivum Annae</a> deprime."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB est continuatio Sci-Hub, cum eius interfacie familiari et directa visione PDF. Intra tuum DOI ad videndum."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Habemus plenam collectionem Sci-Hub, necnon novas chartas. Pleraque directe videri possunt cum interfacie familiari, simili Sci-Hub. Quaedam per fontes externos deponi possunt, quibus casibus nexus ad illos ostendimus."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Quaerere"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nova quaestio"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Includere tantum"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Excludere"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Non inspectum"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Deponere"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Articuli ephemeridum"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Mutuum Digitalis"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titulus, auctor, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Quaerere"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Optiones quaerendi"

#, fuzzy
msgid "page.search.submit"
msgstr "Quaerere"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Quaestio nimis diu duravit, quod commune est pro quaestionibus latioribus. Filtra numeri non accurate esse possunt."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Quaestio nimis diu duravit, quod significat te videre posse eventus inaccuratos. Interdum <a %(a_reload)s>reloading</a> paginae adiuvat."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Display"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Index"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabula"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Provectus"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Descriptiones et commentarii metadatae quaerere"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Add specific search field"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(search specific field)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Annus editus"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Contentus"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Typus fasciculi"

#, fuzzy
msgid "page.search.more"
msgstr "plus…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Accessus"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Fons"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scraped and open-sourced by AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Lingua"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordina per"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Maxime pertinens"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Novissimum"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(annus publicationis)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Antiquissimum"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Maximus"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(magnitudo fasciculi)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Minimus"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(fons apertus)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Fortuitus"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Index quaestionis mensibus renovatur. Nunc comprehendit inscriptiones usque ad %(last_data_refresh_date)s. Pro magis technicis informationibus, vide %(link_open_tag)spagina de datasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Ad explorandum indicem quaestionis per codices, utere <a %(a_href)s>Exploratore Codicum</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Intra in cistam ut quaeras nostrum catalogum %(count)s fasciculorum directe deponendorum, quos <a %(a_preserve)s>in perpetuum conservamus</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Re vera, quisque potest adiuvare ad hos fasciculos conservandos per seminarium nostrum <a %(a_torrents)s>unificatum indicem torrentium</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Nunc habemus catalogum apertum librorum, chartarum, et aliorum operum scripturalium comprehensivissimum in mundo. Speculum Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>et plura</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Si alias “bibliothecas umbrae” inveneris quas speculare debemus, aut si quaestiones habes, quaeso contacta nos apud %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Pro DMCA / iuribus auctorum petitionibus <a %(a_copyright)s>hic preme</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Consilium: utere clavium compendiis “/” (focus quaestionis), “enter” (quaerere), “j” (surge), “k” (descende), “<” (pagina praecedens), “>” (pagina sequens) ad celerem navigationem."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Quaeris chartas?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Intra in cistam ut quaeras nostrum catalogum %(count)s chartarum academicarum et articulorum diurnorum, quos <a %(a_preserve)s>in perpetuum conservamus</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Intra in cistam ut quaeras fasciculos in bibliothecis digitalibus commodatis."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Hic index quaestionis nunc comprehendit metadata ex Bibliotheca Digitali Controllata Internet Archive. <a %(a_datasets)s>Plus de nostris datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Pro plures bibliothecas digitales commodatas, vide <a %(a_wikipedia)s>Wikipedia</a> et <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Intra in cistam ut quaeras metadata ex bibliothecis. Hoc utile esse potest cum <a %(a_request)s>petendo fasciculum</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Hic index quaestionis nunc comprehendit metadata ex variis fontibus metadata. <a %(a_datasets)s>Plus de nostris datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Pro metadata, ostendimus tabulas originales. Non facimus ullum coniunctionem tabularum."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Multae, multae sunt fontes metadata pro operibus scriptis per orbem terrarum. <a %(a_wikipedia)s>Haec pagina Vicipaediae</a> est bonum initium, sed si alias bonas indices nosti, quaeso nos certiores fac."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Intra in cistam ad quaerendum."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Haec sunt metadata records, <span %(classname)s>non</span> files downloadabiles."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Error in quaerendo."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Tenta <a %(a_reload)s>paginam reficere</a>. Si problema perseverat, quaeso nobis scribas ad %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nulla tabula inventa.</span> Tenta pauciora vel diversa verba quaerendi et filtra."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Interdum hoc accidit perperam cum server quaerens tardus est. In talibus casibus, <a %(a_attrs)s>reloading</a> adiuvare potest."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Invenimus congruentias in: %(in)s. Potes referre ad URL ibi inventum cum <a %(a_request)s>petens tabulam</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Articuli Ephemeridum (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Mutuum Digitalis (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultata %(from)s-%(to)s (%(total)s total)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ partiales congruentiae"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d partiales congruentiae"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariatus & Praemia"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Annae Archivum in voluntariis sicut tu nititur. Omnes gradus commitmenti excipimus, et duas principales categorias auxilii quaerimus:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Levis voluntaria opera:</span> si tantum paucas horas hic et illic impendere potes, adhuc multae sunt viae quibus adiuvare potes. Consistentibus voluntariis praemia damus cum <span %(bold)s>🤝 sodalitatibus Annae Archivi</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Gravis labor voluntarius (USD$50-USD$5,000 praemia):</span> si multum temporis et/vel opum nostrae missioni dedicare potes, libenter te arctius cooperari volumus. Tandem potes ad interiorem turmam accedere. Quamquam parvum budgetum habemus, possumus <span %(bold)s>💰 pecuniaria praemia</span> pro labore intensissimo tribuere."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si tempus tuum voluntarie dare non potes, adhuc multum adiuvare potes <a %(a_donate)s>pecuniam donando</a>, <a %(a_torrents)s>torrentibus nostris seminando</a>, <a %(a_uploading)s>libros caricando</a>, vel <a %(a_help)s>amicis tuis de Archivio Annae narrando</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Societates:</span> celerem accessum directum ad collectiones nostras offerimus pro donatione ad gradum enterprise vel pro commutatione novarum collectionum (e.g. novae scansiones, datasets OCR’ati, data nostra locupletantes). <a %(a_contact)s>Contacta nos</a> si hoc tibi convenit. Vide etiam nostram <a %(a_llm)s>paginam LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Levis labor voluntarius"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si paucas horas habes, variis modis adiuvare potes. Certus esto ut <a %(a_telegram)s>sermonem voluntariorum in Telegram</a> iungas."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Ut signum gratitudinis, typice sex menses “Bibliothecarii Fortunati” pro basicis gradibus damus, et plus pro continuo labore voluntario. Omnes gradus opus altae qualitatis requirunt — neglegens labor nos magis laedit quam adiuvat et eum reiciemus. Quaeso <a %(a_contact)s>nobis email mitte</a> cum gradum attingis."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Munus"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Gradus"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Verbum Annae Archivum divulgare. Exempli gratia, libros in AA commendando, ad nostros blog posts ligando, vel generaliter homines ad nostrum locum directando."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s nexus vel imagines."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Haec tibi ostendere debent aliquem de Annae Archivo certiorem facientem, et illum tibi gratias agentem."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Metadata emenda per <a %(a_metadata)s>coniunctionem</a> cum Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Potes uti <a %(a_list)s >indice fortuitorum metadata quaestionum</a> ut initium."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Certe commentarium relinque in quaestionibus quas solvis, ut alii opus tuum non duplicent."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s nexus tabularum quas emendavisti."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Translatio</a> situs web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Linguam plene translate (si non iam prope completionem erat)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Paginam Vicipaediae pro Archivio Annae in tua lingua emenda. Informationem ex pagina Vicipaediae AA in aliis linguis, et ex nostro situ et blogo include. Referentias ad AA in aliis paginis pertinentibus adde."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Coniunctio ad historiam emendationum ostendens te significantem contributionem fecisse."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Petitiones librorum (vel chartarum, etc.) in foris Z-Library vel Library Genesis exsequendo. Systema petitionum librorum proprium non habemus, sed illas bibliothecas speculamus, ita eas meliores faciendo, Archivum Annae quoque melius facimus."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s nexus vel imagines petitionum quas explevisti."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Parva munera in nostro <a %(a_telegram)s>sermone voluntariorum in Telegram</a> posita. Solent pro sodalitate, interdum pro parvis praemiis."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Parvae operae in nostro voluntariorum chat coetu positae."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dependet a munere."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Praemia"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Semper quaerimus homines cum solidis programmatis vel securitatis offensivae artibus ad implicandum. Potestatis gravem ictum facere in legatum humanitatis conservandum."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Ut gratias agamus, sodalitatem pro solidis contributionibus donamus. Ut magnum gratias agamus, praemia pecuniaria pro praecipuis et difficilibus operibus donamus. Hoc non debet videri ut substitutio pro opere, sed est extra incitamentum et potest adiuvare cum impensis."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Plurimae codicis nostri partes sunt apertae, et rogamus ut vestri codicis quoque sint cum praemium donamus. Sunt quaedam exceptiones quas singillatim discutere possumus."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Praemia donantur primae personae quae opus perficit. Libere commentarios in tessera praemii scribere potes ut alii sciant te aliquid operari, ut alii se abstineant vel te contactent ut cooperentur. Sed scito alios adhuc libere operari et te superare conari. Tamen, praemia non donamus pro opere negligenti. Si duo submissiones altae qualitatis prope se fiunt (intra diem vel duos), possumus eligere praemia utrique donare, nostro arbitrio, exempli gratia 100%% pro prima submissione et 50%% pro secunda submissione (ita 150%% totaliter)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Pro maioribus praemiis (praesertim praemiis scrutatoriis), quaeso contacta nos cum ~5%% operis compleveris, et confidis methodum tuam ad totum scopum pervenire posse. Methodum tuam nobiscum communicare debes ut feedback dare possimus. Etiam, hoc modo possumus statuere quid faciendum sit si plures homines prope praemium sunt, ut fortasse praemium pluribus hominibus donare, homines ad cooperandum hortari, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "MONITUM: opera altae praemii sunt <span %(bold)s>difficilia</span> — sapienter est incipere cum facilioribus."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "I ad <a %(a_gitlab)s>indicem quaestionum Gitlab</a> et ordina per “Label priority”. Hoc ostendit fere ordinem operum quae curamus. Opera sine praemiis explicitis adhuc sunt eligibilia pro sodalitate, praesertim ea quae signata sunt “Acceptata” et “Annae favoritum”. Potes incipere cum “Proiecto initio”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates de <a %(wikipedia_annas_archive)s>Annae Archivum</a>, maxima vere aperta bibliotheca in historia humana."

#, fuzzy
msgid "layout.index.title"
msgstr "Archivum Annae"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Maxima bibliotheca mundi apertae fontis et apertae datae. Specula Sci-Hub, Library Genesis, Z-Library, et plura."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Quaerere Archivum Annae"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Archivum Annae auxilium tuum necessarium habet!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Multi conantur nos deicere, sed resistimus."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si nunc donas, accipis <strong>duplicem</strong> numerum celerium downloadorum."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valet usque ad finem huius mensis."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Dona"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Servare humanam scientiam: magnum donum feriarum!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Mirare dilectum, da ei rationem cum sodalitate."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Ad augendam resilientiam Archivi Annae, quaerimus voluntarios ad specula currenda."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Perfectum donum Valentini!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Novum modum donationis habemus: %(method_name)s. Quaeso considera %(donate_link_open_tag)sdonandum</a> — non est vile currere hunc situm, et tua donatio vere facit differentiam. Gratias tibi valde."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Fundationem agimus pro <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">subsidio</a> maximae bibliothecae umbrae comicorum in mundo. Gratias pro tuo auxilio! <a href=\"/donate\">Dona.</a> Si donare non potes, considera nos adiuvare narrando amicis tuis, et sequendo nos in <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, aut <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Recentes downloadia:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Quaerere"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Metadata emendare"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariatus & Praemia"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrentes"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Actio"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Codices Explorator"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Domus"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Software Annae ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Translate ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Coniungere / Registrare"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Rationem"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Archivum Annae"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "In tactu mane"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / iura auctorum"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Provectus"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Securitas"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativa"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non affinis"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Hic fasciculus quaestiones habere potest."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Celer download"

#, fuzzy
msgid "page.donate.copy"
msgstr "exemplum"

#, fuzzy
msgid "page.donate.copied"
msgstr "exemplatum!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Praecedens"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Proximus"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "solum hoc mense!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>suspendit</a> novorum chartarum uploadationem."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selige optionem solutionis. Pro solutionibus crypto-pecuniariis %(bitcoin_icon)s infracta damus, quia (multo) pauciores sumptus habemus."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selige optionem solutionis. Nunc solum solutiones crypto-fundatas habemus %(bitcoin_icon)s, quoniam processus solutionis traditionales nobiscum operari recusant."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Non possumus directe chartas creditarias/debitorias sustinere, quia ripae nolunt nobiscum operari. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tamen, sunt plures modi utendi chartis creditariis/debitariis, utendo nostris aliis modis solutionis:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Tarda & externa downloadia"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloadia"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si primum crypto uteris, suademus ut %(option1)s, %(option2)s, vel %(option3)s utaris ad emendum et donandum Bitcoin (originalis et maxime usitata cryptocurrency)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 coniunctiones recordorum quae emendasti."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 coniunctiones vel screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 coniunctiones vel screenshots petitionum quas explevisti."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si interest te in his datasets speculis pro <a %(a_faq)s>archivis</a> vel <a %(a_llm)s>LLM disciplina</a> propositis, quaeso nos contacta."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si interesse habes in speculo huius dataset pro <a %(a_archival)s>archivis</a> vel <a %(a_llm)s>LLM institutione</a> causis, quaeso nos contacta."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Situs principalis"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informatiunculae de nationibus ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si interest te speculum huius dataset pro <a %(a_archival)s>archivis</a> vel <a %(a_llm)s>LLM disciplina</a> propositis, quaeso contacta nos."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Agenzia Internationalis ISBN regulariter emittit intervalla quae assignavit ad agenzias nationales ISBN. Ex hoc possumus derivare cuius nationis, regionis, vel coetus linguistici hic ISBN pertinet. Hoc tempore utimur his datis indirecte, per <a %(a_isbnlib)s>isbnlib</a> bibliothecam Python."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resources"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultima renovatio: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Situs ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excludens “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Inspiratio nostra ad metadata colligenda est propositum Aaronis Swartz de “una pagina interretiali pro omni libro umquam edito”, pro quo creavit <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Illa projectio bene successit, sed nostra singularis positio nobis permittit ut metadata accipiamus quae illi non possunt."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Alia inspiratio fuit desiderium nostrum scire <a %(a_blog)s>quot libri in mundo sint</a>, ut computare possimus quot libros adhuc servare debeamus."

#~ msgid "page.partner_download.text1"
#~ msgstr "Ut omnibus facultatem praebeas ut gratis fasciculos deponant, debes exspectare <strong>%(wait_seconds)s secundas</strong> antequam hunc fasciculum deponere possis."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Pagina automatice reficiatur. Si fenestram downloadi deessis, timer iterum incipiet, itaque reficiatio automatica commendatur."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Nunc deponere"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converte: utere instrumentis online ad convertendum inter formatos. Exempli gratia, ad convertendum inter epub et pdf, utere <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: download fasciculum (pdf vel epub sustinentur), deinde <a %(a_kindle)s>mitte ad Kindle</a> utens web, app, vel email. Utilia instrumenta: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Auctores sustine: Si hoc tibi placet et potes, considera emere originale, vel directe auctores sustinere."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Bibliothecas sustine: Si hoc in bibliotheca locali tua praesto est, considera illud ibi gratis mutuari."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Non directe in mole praesto, tantum in semi-mole post murum solvendum"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annae Archivum collectionem <a %(isbndb)s>ISBNdb metadata</a> administrat"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb est societas quae varias bibliothecas online scrutat ut ISBN metadata inveniat. Archivum Annae metadata librorum ISBNdb subsidia fecit. Hoc metadata per Archivum Annae praesto est (quamvis non in quaesitione, nisi si expresse quaeras numerum ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Pro technicis singulis, vide infra. Aliquando uti possumus ad determinandum qui libri adhuc desunt ex bibliothecis umbrae, ut prioritizemus quos libros invenire et/vel scannare."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Noster blog post de hac notitia"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb scrutatio"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Hoc tempore habemus unicum torrentem, qui continet 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> file (20GB unzipped): “isbndb_2022_09.jsonl.gz”. Ad importandum file “.jsonl” in PostgreSQL, uti potes aliquid simile <a %(a_script)s>hoc scriptum</a>. Potes etiam directe pipare ut aliquid simile %(example_code)s ut decomprimat in volatu."

#~ msgid "page.donate.wait"
#~ msgstr "Exspecta saltem <span %(span_hours)s>duas horas</span> (et hanc paginam refrica) antequam nos contactes."

#~ msgid "page.codes.search_archive"
#~ msgstr "Quaerere Archivum Annae pro “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Dona utendo Alipay vel WeChat. Potes eligere inter haec in pagina proxima."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Verbum de Archivio Annae in socialibus instrumentis et foris online spargendo, libros vel indices in AA commendando, vel quaestiones respondendo."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiction collectio discrevit sed adhuc habet <a %(libgenli)s>torrents</a>, quamquam non ab anno 2022 renovata (directas downloads habemus)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annae Archivum et Libgen.li collaborative collectiones <a %(comics)s>comicorum librorum</a> et <a %(magazines)s>ephemeridum</a> administrant."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nulli torrents pro collectionibus fictionis Russicae et documentorum standardium."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Non sunt praesto torrentes pro contentis additis. Torrentes qui sunt in situ Libgen.li sunt specula aliorum torrentium hic enumeratorum. Una exceptio est fictionis torrentes incipientes a %(fiction_starting_point)s. Torrentes comicorum et ephemeridum eduntur ut collaboratio inter Archivum Annae et Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Ex collectione <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origo exacta incerta. Partim ex the-eye.eu, partim ex aliis fontibus."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

