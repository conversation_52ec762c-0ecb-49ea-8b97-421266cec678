��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b   ud    ~f -   g �   �g �  �h   Gj >  �k �   m B   �m k   .n H   �n e   �n T  Io ,  �p �   �q {  �r �   t �  �t �  ev �   x �   �y o  qz J  �{ '   ,} �   T} H   &~ �  o~    � �  5� H   *�    s�    ��    �� 7   �� !   �    � $   %�    J�    i�    �� -   �� %  �� �   � B   Ņ )   � 
   2�    =�    I�    ]�    i�    �� 	   ��    ��    ��    ��    Ά    Ԇ 	   � 
   ��    �    �    1�    H� �  `� -  K� h   y� -   �   � �   ,� �   �� �   ~�    #� �   >�    ˎ �   �    {� �   ��    �� A  �� '   �� Z   �    w� ,  ~�    �� �  ��   H�    `� '   o� E   �� �   ݗ *   `� #   �� \   �� +   � <   8� -   u�    �� a   �� u  � 6   �� n   �� �   (� >   �� *  �� -   !� �   O� r   N� �   �� E   C� �   �� �   � H   �� �   ֡ �   Ƣ Q   R� 2   �� K   ף 	   #� }   -� �   �� H   ��    ۥ �   � �   y�    "� Q  )� �   {�   � ]  #� �   ��   j� �   }� 8   _� >   �� ?   װ d   � R   |� Q   ϱ    !� �   A� E   Ͳ ?   �    S� ]   `� 6   �� �   �� w   ��    � �   �   �� K  � �  N� }   )� �   ��   d� �   l� �   � �   �� W  -� �   �� �   �    � �   � �   �� �   ?� ]  �� t   O� j   �� �   /� �  �� �   X�   �� 
  �� 0   � R   9� i   �� ?   �� C  6� �   z� O   � j   f�    �� �  �� �   �� 0  ��    �� T   �� u   +� �   �� �   ;� �   	� �  �� }  [� "   �� C  �� �  @� �   ?� (  � �   F� �   @� U   8�    ��    � �   �   �� :  �� �   �� �   �� �   ��    .� <  5�   r� �   ��    E� �   L� 	  �� �   �� �  �� �   H� M   �� 
   G� D  U� !  ��    �� �   �� �   �� �  �� �   \� (  9� N   b� K   �� �   �� 3   ��    1�   D� �   P�   8� 3   L� H   �� �  �� ~   d� i   �� Q   M� >  ��    ��    �� 5    �   J T   � ?   9 |   y '   � �    �   �    �    �    � $   � -   � '    ?   E c   �    � !   � 3    A   O    � $   � $   � ]   �    X    _ �   r �   J �   #	 �   �	 c   �
 �   -   �    � �   
 �   �
 �   :   � *  � �  � S  ~ 0  � H    !   L    n �   � n  $ C  � �   � �   � �   � �  x �   , *    �   H    :  +     f! �   s! �   " r   # �   �# �   z$    ;%    H% `   g%    �% 0  �% M   (   N) �   _+ �   �+    �, �   �, �   �- H   d. ^   �. �   / �   �/ �   �0 .  >1 ?   m2 �   �2    x3 ~  �3 �   5 �  �5 !  07 �  R9 �   �:    s; �   z; {  D<    �= )  �= |  �> �  t@ e  YB �   �C   �D H   �E �  �E �   �G y   AH =   �H N   �H    HI =   OI $   �I ,   �I #   �I    J ~    J 2  �J �  �L �  hN �   P    �P `  �P �  ;S :  �T A  5V   wW /  �Y    �[ �   �[    �\    �\ H  �\ �  �] �  �_ 
  Ha 	   Vc �  `c q  e �  xf �  �g Y  �i �  9l G  �m    )o �   Bo W   �o T  (p @  }r �   �s c   Dt �   �t   Eu    cv �   uv <   w C   @w    �w 3   �w 3   �w �  �w   �y �  �z !   �| /  �| �        � x   � 3   	� b   =� �   �� $   3� D   X�    ��   �� �  �� n  |� ,  � a   �    z� S   ��   އ    � �   � G  g� �   �� 6   4� 9   k� 	  �� -   �� �  ݍ /  �� �  � !   s� r   �� �   �   �� b  ��    
� �   � -   �� �   (� �   Ș �   j� �   <� H   � -   *� �   X� -   (� 0  V�    �� �   �� �   4� H   
� #   S� �   w� �  �   ˣ �  � `  n� /   ϧ �   �� �   ��    �� �   Ȫ �  �� A  %� -   g� �  �� *   Z� w  �� F   ��    D� �  W� �  � 6  �� �   ̶   �� �  �� �  i� �   � M  �� �  ߾ c   m� �  ��   �� 6   �� l   �� u   B�    ��    ��   �� �   �� $   }�   �� �  �� �   @� �  <� i  �� �   F� G  �� �   0�    $� �   8�    �� >  ��    7�    W�    m�    s�    ��    ��    ��    ��    ��    �� 	   �� 	   �� 	   �    �    �    (�    /�    N�    R�    Y�    `� �   g� J   � !   b�    ��    ��    �� *   �� *   �� !   "� 	   D� 	   N�    X�    k�    ��    ��    ��    ��    ��    �� (   ��    ��    
�     "� #   C� %   g� &   ��    �� )   �� 3   ��    '� B   4� ?   w�    �� ?   �� H   ��    G�    f� $   w�    ��    ��    ��    ��    ��    ��    ��    
�    � 
   4� 	   B� 
   L�    W�    Z�    o�    v� 	   �    �� 	   ��    ��    ��    �� 	   ��    ��    ��    ��    �    
�    *�    H�    P� 	   ]�    g� (   x�    ��    ��    ��    ��    �� 	   ��    ��    �    	�    � Z   #� z   ~� A   �� �   ;� �  �� p   k�    ��    ��    �    �    �    "� 	   :� &   D� S   k� 0   �� V   ��    G� 3   `�    �� T   �� O   � �   X� [   � 3   d�    ��    ��    ��    ��    ��    ��    �� 	    �    
� 	   �    !�    (�    8�    E�    L� 	   \�    f�    v�    ��    ��    ��    �� 
   ��    ��    ��   ��    ��    ��    ��     ��    � :   � Z   T� )   �� '   �� 6   �    8�    @�    H� d   M�    ��    �� (   �� c   ��    V�    n� T   � #   �� "  �� P   � z   l� �   �� �   j� 0   � �   G� w   � �   �� �   ��    o�    �� :   �� :   �� H   � `   N� \   �� F   � O   S�    �� 2   ��    ��    �� B   �    U�    u�    |�    ��    �� i   ��    � $   )� E   N�    ��    �� H   �� ]    � K  ^�    ��    �� �   ��    _�    f�    m�    t�    �� (   ��   ��    �� 	   ��    ��    � �   �    �� 	   �    � P   %�    v�    }� !   ��    ��    �� "   �� }   ��    e�    x� 3   ��    ��    ��    ��    �� '   �� B    �    c� *   p� {   �� N   � J   f�    �� �   �� B   x�    �� $   ��    �� �     p   �      :   / j   j �   �    �    �    � .  �    �    
    - !   G    i �   �    
    # $   7 <   \    �    �    � '   � �  � <  � �  (	 9   �
 3   &    Z $   g �   � �   � �   *
    �
 �   �
 c  �          �  ?    	   # 
   - 3   8    l �   s    Z   l y  ~ �   �    �    � *   Q     | h   �    �    �   
 Y  � `   � �   R N   ) �   x �   � H   � �   � +   v      �  	   �  	   �     �      �     ! <   '! (   d! 	   �! +   �! �   �! '   �" �   �" l   |# r   �#    \$    |$    �$    �$    �$    �$ "   �$ $   % �   8% '   & �   3&    �& �   ' q   �' s   7(    �( �   �) '   I* �   q* 	   +   + ]   ,    v, j  �,    �-    
.    .    *.     @.    a.    h. :   o. �   �. �   2/     0    0     0 �   /0 	  �0 �   �1 u   �2    
3    '3    A3    W3    k3    x3    �3 >   �3 6   �3 �  	4 ?   �5    �5 W   	6 T   a6 L   �6 Z   7 N   ^7 Q   �7    �7 B   8 B   I8 }   �8 K   
9 E   V9    �9 �   �9 ]   x: :   �: ]   ; U   o; 7   �; 	   �; 2   < w   :< �   �< 2   ;= �   n=    "> �   )> 6   �> ^   -? �   �?    @ �  "@ �   �A    yB    �B    �B    �B �   �B ,   wC `   �C �   D �   �D �   gE �   �E �   �F �   AG �   �G P   �H �   �H a   �I <  OJ �   �K �   0L 
   M 
   M 
   M ~   -M 
   �M 
   �M @   �M F   	N �   PN 
   O W   "O 8   zO t   �O )   (P Z   RP R   �P 
    Q u   Q 
   �Q 
   �Q 
   �Q �  �Q g   �S K  T    PU    cU 	   jU �   tU    �U '   V �   ;V �   *W    �W    �W    �W 1   X 6   DX 1   {X    �X    �X �   �X    �Y _  �Y �   [ ~   �[ L   H\ �   �\ �   ] �  �] �  �_ �   ca q   �a q   Yb    �b   �b    �c D  d �   Xe �   f t   g �   �g �   uh �   i z   �i .   j 1   Fj    xj "   �j 	   �j 	   �j    �j �   �j 	   �k _   �k !   l    4l    ;l 	   Bl    Ll d   il C   �l 1   m �   Dm +   ?n    kn    xn    n    �n    �n    �n    �n    �n    �n    �n    �n    �n     o �   o    �o 	   �o    �o 	   �o    �o 	   �o    �o 	   p    p    )p $   6p K   [p    �p #   �p a   �p �   =q �   �q �   �r �   s �   �s �   st    lu v   u 3   �u >   *v =   iv �   �v �   Aw J   x P   Zx    �x W   �x l   y �   �y 	   z #   z    /z    Bz 	   Wz !   az    �z )   �z    �z    �z    �z    �z    {    %{    @{    ]{    d{    w{    �{    �{    �{ !   �{    �{ {   �{ �   h| !   �|    } h   4} U   �} s   �}    g~ $   �~ 3   �~ <   �~ ;    '   X �   � �   1�   �    <� 9   V� �   �� $   !� �   F� �   � {    >   >�    }� �   �� �    � ]   �� +   � ]   4� �   ��    ?� &   W�    ~� ;   �� S   Ј P   $�    u�    |�    ��    �� �   �� 9   7�    q� )   ��    ��    ъ    � 3   
�    A� ^   ]� .   �� �   � @   �� H   � �   5� ?   Ս    �    3�    O�    m�    ��    ��    Ŏ *   � $   � �  3� 4   �� 3   4� ?   h� %   ��    Α Z   Ց q   0� �   �� �   :� 	   �� h   ˓ &   4�    [� �   n�    1� '   A� *   i� ,   �� S   �� Z   � A   p�    ��    ǖ �   � .   l� !   �� L   �� �   
� '   ژ }   � U   �� H   ֙ _   �    � 0   �� f   ̚    3� g   H�    �� �   ě 4   ^� <   ��    М    � �   � :   �� @   � o   '� M   �� 6   � p   � i   ��    ��    �� ?   � :   N�    ��    ��    ��    Ǡ    ٠ -   � �   � o   ��    �    -� $   L� !   q� $   �� i   �� 3   "� c   V� K   ��    � b   � �   �� 0   D� H   u�    �� N   Υ 6   �    T� T   g� c   �� *    � <   K� 	   �� -   �� ?   ��     � R   
� )   `�    �� !   �� <   Ĩ �   � 5   ��    �    
� 5   (�    ^� D   y� *   �� f   � ~   P�    ϫ 6   ܫ �   �    �� �   ͬ 7   S� !   �� L   �� �   �� 	   ʮ    Ԯ    ֮    خ    � *   � -   ,�    Z�    p�    �� !   �� '   �� '   ѯ    �� 8    � D   9�    ~�    �� '   ��    Ѱ    � W   �� �   U� E   $�    j�    {� �   ��    � M   1�    � /  �� N  µ    � K   -�    y� �  �� *   �� ]   ��    �    0� �  C�    ׻ F   �� ^   =� ]   �� W   �� !   R� I   t� 0   ��    � q   � A   z� /   �� R   � .   ?� O   n� �   �� �   S� !   �� �   ��   �� �   �� $   !�   F� �   R� �   � �   �� �   ��    0�    F� x   Y� /   �� �  � N   �� X   ��    L�    \� l  x� 	   �� �   �� 	  ��   �� &  �� 3   �� ?   � T   N� -   �� .   �� [    � I   \� 	   ��    �� 3   �� '   ��    %� $   ?� h   d� '   ��    �� Z   �� �   W� y   &�    ��    ��    �� E   �� U   
� X   `� �   �� Y   c�    ��    �� �   ��    �� �   �� ;  ;� �   w� D   5� %   z�    ��    ��    �� <   �� $   �� m   � �   �� O   H�    ��    �� '   ��    �� H   ��    H� >   T�    �� (   �� (   ��    ��    �� B   �    I�    P�    f�    ��    �� Q   �� �   �� N   �� N   �� @   &� �   g� 
   	�    � �   -� �   �� �   n�    � c   � <   �� 7   �� T   �� Y   K� B   ��    �� ;   ��    4�    A�    N�    [�    k�    {�    ��    ��    ��    �� -   �� -   �� -   �     F� 2   g� +   ��     ��    ��    � 1   �    @�    S� !   Z�    |� H   �� !   ��    �     � $   3�    X�    w� e   �� J   �� h   5� �   ��    W�    i�    {�    �� +   �� 	   ��    ��    �� ]   �    i� !   ��    �� 
   �� 	   �� +   ��    �� �   �    ��    ��    � %   /�    U�    o�    ��    ��     �� #   ��    �    "� W   )� $   ��    ��    �� +   �� M   ��    M� 6   f�    �� Z   �� E   � '   W�    � !   �� 	   ��    ��    ��    �� ,  �� ]   #�    ��    �� )   ��    �� #   �� 	   �� 	   � p   � 5   }� �   ��    w� !   �� u   �� !   �    A�     _� #   �� -   �� #   ��    �� 	   � 8   �    T� !   m� 3   ��   �� 6   �� <   �� =   ;�    y� �   ��    �    3� B   @�    ��     ��    �� .   �� 6   ��    0� �   O�    ��    ��    ��    �    �    1�    G�    f�    |� R   �� �   ��    ��    �� �   �� �   ��    ;�    N�    j�    }�    ��    ��    ��    ��    ��    ��           
   +     9     H     Z     g     s     �  �   �  �   p h  ^ +  � �  � �   z n  .    �	 �   �	    �
 �   �
 �   t m  D �   �
 R  [ .   � �   � <   �    � 5   � :    g   O O   � �    �   � �   = 	  �    � �    �   � ]   K �   �    K �   X !  � �    �   �    b T   o [   � �     r   � O   h v   �    /    E    ^ G   n !   �    � v   � D   \ Y   �    � �     i   �  6   ! C   9! G   }! P   �! G   " D   ^" ]   �" P   # e   R# y   �#    2$ (   9$ (   b$ X   �$ 9   �$    %    %% B   ,%    o%    �%    �% *   �% '   �% 3   �%    /&    G&    T&    [& 	   a& F   k& Y   �& I   ' 3   V'    �'    �'    �'    �'    �'    �'    �'    �'    �'    �'    (    (    (    "( 	   )(    3(    :(    M(    Z(    m(    t(    {(    �(    �(    �(    �( r   �(    C) Q   Y) �   �)    K* 	   R* 	   \* 	   f* 	   p*    z* 	   ~* �   �* w   + ?   �+    �+    �+ h   �+    _, y   l, �   �, "   y-    �-    �- �   ,. H   �. �   5/ k   �/ +   O0 x   {0    �0    	1 9   1 s   Y1 !   �1 |   �1 l   l2 �   �2 N   �3    �3    �3    �3    4    4 	   4 	   #4    -4 w   G4 Z   �4 q   5 �   �5 �   L6 ^   7 J   v7 j  �7 [  ,9 �   �: s   P; �   �; 8  �<    �= �   >   �> �   �?   8@ t  KB v   �C 7  7D    oE 7   |E �   �E E  EF 7   �G �   �G 	   �H    �H    �H �   �H 9   CI Q   }I (   �I G   �I C   @J L   �J "   �J �   �J <   �K &   �K 6   �K �   &L    �L  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: yue
Language-Team: yue <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library 係一個受歡迎（同非法）嘅圖書館。佢哋攞咗 Library Genesis 嘅收藏，並令其易於搜索。除此之外，佢哋喺吸引新書貢獻方面變得非常有效，通過提供各種福利來激勵貢獻者。佢哋目前冇將呢啲新書返貢獻俾 Library Genesis。與 Library Genesis 唔同，佢哋冇令佢哋嘅收藏易於鏡像，呢阻止咗廣泛嘅保存。呢對佢哋嘅商業模式好重要，因為佢哋收費俾人批量訪問佢哋嘅收藏（每日超過 10 本書）。 我哋唔會對於收費提供非法書籍收藏嘅批量訪問作出道德判斷。無可否認，Z-Library 喺擴大知識訪問同獲取更多書籍方面取得咗成功。我哋只係喺度做我哋嘅部分：確保呢個私人收藏嘅長期保存。 - Anna同團隊（<a %(reddit)s>Reddit</a>） 喺海盜圖書館鏡像嘅原始發佈中（編輯：已移至 <a %(wikipedia_annas_archive)s>Anna’s Archive</a>），我哋製作咗 Z-Library 嘅鏡像，一個大型非法書籍收藏。提醒一下，呢係我哋喺原始博客文章中寫嘅： 呢個收藏可以追溯到2021年中。喺此期間，Z-Library 以驚人嘅速度增長：佢哋新增咗大約380萬本新書。當中有啲係重複嘅，無錯，但大部分似乎係真正嘅新書，或者係之前提交書籍嘅更高質量掃描。呢大部分係因為 Z-Library 嘅志願版主數量增加，仲有佢哋嘅批量上傳系統同去重功能。我哋想祝賀佢哋取得呢啲成就。 我哋好高興宣佈，我哋已經獲得咗所有喺我哋上次鏡像同2022年8月之間新增到 Z-Library 嘅書籍。我哋仲返去刮取咗啲第一次錯過嘅書籍。總體嚟講，呢個新收藏大約有24TB，比上次嘅7TB大好多。我哋嘅鏡像而家總共有31TB。再次，我哋對 Library Genesis 進行咗去重，因為嗰個收藏已經有種子可用。 請去到 Pirate Library Mirror 睇下新收藏（編輯：已移至 <a %(wikipedia_annas_archive)s>Anna’s Archive</a>）。嗰度有更多關於文件結構嘅資訊，仲有自上次以嚟有咩改變。我哋唔會喺呢度連結到嗰度，因為呢度只係一個博客網站，唔會托管任何非法材料。 當然，做種都係幫助我哋嘅一個好方法。多謝所有為我哋之前嘅種子做種嘅人。我哋對正面嘅回應感到感激，亦都好高興有咁多人以呢種特別嘅方式關心知識同文化嘅保存。 3x 新書加入到海盜圖書館鏡像（+24TB，380 萬本書） 閱讀TorrentFreak嘅相關文章：<a %(torrentfreak)s>第一篇</a>，<a %(torrentfreak_2)s>第二篇</a> - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） TorrentFreak嘅相關文章：<a %(torrentfreak)s>第一篇</a>，<a %(torrentfreak_2)s>第二篇</a> 唔係好耐之前，「影子圖書館」仲係衰落緊。Sci-Hub，呢個龐大嘅非法學術論文檔案，因為訴訟而停止接收新作品。「Z-Library」，呢個最大嘅非法書籍圖書館，佢哋嘅創辦人被指控犯上刑事版權罪而被捕。佢哋竟然成功逃脫咗，但佢哋嘅圖書館依然面臨威脅。 有啲國家已經喺做類似嘅事情。TorrentFreak <a %(torrentfreak)s>報導</a>話中國同日本已經喺佢哋嘅版權法中引入咗AI例外。我哋唔清楚呢個點樣同國際條約互動，但呢個肯定畀佢哋嘅國內公司提供咗保護，呢個解釋咗我哋所見嘅情況。 至於Anna’s Archive——我哋會繼續喺道德信念嘅基礎上進行地下工作。但我哋最大嘅願望係能夠合法地進入光明，並擴大我哋嘅影響力。請改革版權。 當Z-Library面臨關閉時，我已經備份咗佢嘅全部圖書館，並尋找一個平台去存放佢。呢個就係我創立Anna’s Archive嘅動機：延續之前嗰啲計劃嘅使命。自此以來，我哋已經成為全球最大嘅影子圖書館，擁有超過1.4億本受版權保護嘅文本，涵蓋多種格式——書籍、學術論文、雜誌、報紙等等。 我同我嘅團隊係理想主義者。我哋相信保存同托管呢啲文件係道德上正確嘅。全球嘅圖書館都面臨資金削減，我哋亦唔能夠信任人類嘅遺產交畀企業。 然後AI出現咗。幾乎所有主要公司建設LLM都聯絡咗我哋，想用我哋嘅數據訓練。大多數（但唔係全部！）美國公司一旦意識到我哋工作嘅非法性質，就重新考慮咗。相比之下，中國公司熱情地接受咗我哋嘅收藏，似乎唔受其合法性影響。考慮到中國係幾乎所有主要國際版權條約嘅簽署國，呢點值得注意。 我哋提供咗高速訪問畀大約30間公司。大多數係LLM公司，有啲係數據經紀人，佢哋會轉售我哋嘅收藏。大多數係中國公司，但我哋亦同來自美國、歐洲、俄羅斯、南韓同日本嘅公司合作過。DeepSeek <a %(arxiv)s>承認</a>佢哋嘅早期版本係用我哋嘅部分收藏訓練出嚟嘅，雖然佢哋對最新模型保持緘默（但可能亦係用我哋嘅數據訓練出嚟嘅）。 如果西方想喺LLM甚至AGI嘅競賽中保持領先，就需要重新考慮佢哋對版權嘅立場，並且要快。無論你同唔同意我哋嘅道德立場，呢個問題而家已經成為經濟問題，甚至係國家安全問題。所有權力集團都喺建設人工超級科學家、超級黑客同超級軍隊。信息自由對呢啲國家嚟講已經成為生存問題——甚至係國家安全問題。 我哋嘅團隊來自世界各地，冇特定嘅立場。但我哋會鼓勵擁有強大版權法嘅國家利用呢個生存威脅去改革佢哋嘅法律。咁應該點做？ 我哋嘅第一個建議係簡單直接：縮短版權期限。喺美國，版權係喺作者死後70年授予嘅。呢個係荒謬嘅。我哋可以將呢個同專利對齊，專利係喺申請後20年授予嘅。呢個應該足夠畀書籍、論文、音樂、藝術同其他創作作品嘅作者獲得充分嘅補償（包括較長期嘅項目如電影改編）。 然後，至少，政策制定者應該包括大規模保存同傳播文本嘅例外。如果失去個別客戶嘅收入係主要擔憂，個人層面嘅分發可以繼續禁止。反過來，能夠管理龐大資料庫嘅公司——訓練LLM嘅公司，以及圖書館同其他檔案館——將會受到呢啲例外嘅保護。 版權改革對國家安全係必要嘅 簡而言之：中國嘅LLM（包括DeepSeek）係用我非法嘅書籍同論文檔案訓練出嚟嘅——呢個係全球最大嘅檔案。西方需要徹底改革版權法，作為國家安全嘅一部分。 請參閱<a %(all_isbns)s>原始博客文章</a>以獲取更多資訊。 我哋發出咗一個挑戰去改進呢個項目。我哋會頒發第一名獎金$6,000，第二名$3,000，第三名$1,000。由於反應熱烈同埋提交嘅作品非常出色，我哋決定稍微增加獎金池，並頒發四個第三名，每個$500。得獎者如下，但記得<a %(annas_archive)s>喺呢度</a>睇晒所有提交作品，或者下載我哋嘅<a %(a_2025_01_isbn_visualization_files)s>合併種子</a>。 第一名 $6,000: phiresky 呢個<a %(phiresky_github)s>提交作品</a>（<a %(annas_archive_note_2951)s>Gitlab評論</a>）簡直係我哋想要嘅一切，仲有更多！我哋特別鍾意佢嘅靈活可視化選項（甚至支持自定義著色器），但同時有一個全面嘅預設列表。我哋亦鍾意佢嘅速度同流暢度，簡單嘅實施（甚至冇後端），聰明嘅小地圖，仲有佢哋<a %(phiresky_github)s>博客文章</a>入面詳細嘅解釋。真係好出色嘅作品，實至名歸嘅冠軍！ - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 我哋心中充滿感激。 值得注意嘅想法 摩天大廈稀有度 好多滑桿用嚟比較Datasets，好似你係DJ咁。 帶有書籍數量嘅比例尺。 靚靚標籤。 酷炫預設色彩方案同熱圖。 獨特地圖視圖同篩選器 註釋，仲有即時統計 即時統計 我哋特別鍾意嘅更多想法同實施： 我哋可以繼續講落去，但係不如停喺呢度。記得睇哂所有提交嘅作品<a %(annas_archive)s>喺呢度</a>，或者下載我哋嘅<a %(a_2025_01_isbn_visualization_files)s>合併torrent</a>。咁多提交作品，每一個都帶嚟獨特嘅視角，無論係UI定實施上。 我哋至少會將第一名嘅提交作品整合到我哋嘅主網站，可能仲有其他作品。我哋亦開始諗緊點樣去組織識別、確認，然後存檔最稀有嘅書籍嘅過程。更多資訊即將公佈。 多謝所有參加嘅人。咁多人關心真係好令人感動。 輕鬆切換Datasets以便快速比較。 所有ISBN CADAL SSNOs CERLALC數據洩漏 DuXiu SSIDs EBSCOhost嘅電子書索引 Google Books Goodreads Internet Archive ISBNdb ISBN全球出版商註冊 Libby Anna’s Archive入面嘅檔案 Nexus/STC OCLC/Worldcat OpenLibrary 俄羅斯國家圖書館 Trantor帝國圖書館 第二名 $3,000: hypha 「雖然完美嘅正方形同長方形喺數學上令人滿意，但佢哋喺映射環境中唔提供更好嘅局部性。我相信呢啲Hilbert或經典Morton曲線固有嘅不對稱性唔係缺陷，而係一個特點。就好似意大利著名嘅靴形輪廓令佢喺地圖上即時可識別，呢啲曲線獨特嘅『特點』可能成為認知地標。呢種獨特性可以增強空間記憶，幫助用戶定位自己，可能令定位特定區域或注意模式變得更容易。」 另一個出色嘅<a %(annas_archive_note_2913)s>提交作品</a>。雖然冇第一名咁靈活，但我哋其實更鍾意佢嘅宏觀層次可視化（空間填充曲線、邊界、標籤、突出顯示、平移同縮放）。Joe Davis嘅<a %(annas_archive_note_2971)s>評論</a>令我哋有共鳴： 仲有好多可視化同渲染嘅選項，以及一個非常流暢同直觀嘅UI。穩固嘅第二名！ - Anna同團隊（<a %(reddit)s>Reddit</a>） 幾個月前，我哋宣佈咗一個<a %(all_isbns)s>$10,000懸賞</a>，以製作出最好嘅ISBN空間數據可視化。我哋強調顯示我哋已經/未曾存檔嘅文件，並且後來提供咗一個數據集，描述有幾多圖書館擁有ISBN（稀有性嘅衡量標準）。 我哋對呢個反應感到不勝感激。創意無限。非常感謝所有參與者：你哋嘅活力同熱情係有感染力嘅！ 最終我哋想解答以下問題：<strong>世界上有啲乜嘢書存在，我哋已經存檔咗幾多，下一步應該專注於邊啲書？</strong> 見到咁多人關心呢啲問題真係好開心。 我哋自己開始咗一個基本嘅可視化。在少於300kb嘅情況下，呢幅圖簡潔地代表咗人類歷史上最大嘅完全開放嘅「書籍清單」： 第三名 $500 #1: maxlion 喺呢個<a %(annas_archive_note_2940)s>提交作品</a>中，我哋真係鍾意唔同類型嘅視圖，特別係比較同出版商視圖。 第三名 $500 #2: abetusk 雖然唔係最精緻嘅UI，但呢個<a %(annas_archive_note_2917)s>提交作品</a>滿足咗好多要求。我哋特別鍾意佢嘅比較功能。 第三名 $500 #3: conundrumer0 就好似第一名，呢個<a %(annas_archive_note_2975)s>提交作品</a>以其靈活性令我哋印象深刻。最終呢個就係一個出色可視化工具嘅關鍵：為高級用戶提供最大靈活性，同時保持對普通用戶嘅簡單性。 第三名 $500 #4: charelf 最後一個獲得獎金嘅<a %(annas_archive_note_2947)s>提交作品</a>相當基本，但有啲我哋真係鍾意嘅獨特功能。我哋鍾意佢哋展示幾多個Datasets覆蓋某個ISBN作為受歡迎程度/可靠性嘅指標。我哋亦非常鍾意使用不透明度滑塊進行比較嘅簡單但有效嘅方法。 $10,000 ISBN可視化懸賞嘅得獎者 簡而言之：我哋收到咗啲令人難以置信嘅$10,000 ISBN可視化懸賞提交。 背景 安娜嘅檔案點樣可以喺唔知道仲有邊啲書嘅情況下，實現備份全人類知識嘅使命呢？我哋需要一個待辦事項清單。將呢個計劃出嚟嘅一個方法係透過ISBN號碼，自1970年代以嚟，ISBN號碼已經分配畀每本出版嘅書（喺大多數國家）。 冇一個中央機構知道所有ISBN分配。相反，呢係一個分佈式系統，國家獲得一系列號碼，然後分配較小範圍畀主要出版商，佢哋可能進一步將範圍細分畀次要出版商。最後，個別號碼分配畀書籍。 我哋喺<a %(blog)s>兩年前</a>開始用我哋嘅ISBNdb爬取去繪製ISBN地圖。自此以來，我哋爬取咗更多metadata來源，例如<a %(blog_2)s>Worldcat</a>、Google Books、Goodreads、Libby等等。完整列表可以喺安娜嘅檔案嘅“Datasets”同“Torrents”頁面搵到。我哋而家擁有全球最大、完全開放、易於下載嘅書籍metadata（因此亦包括ISBN）收藏。 我哋<a %(blog)s>詳細寫過</a>點解我哋關心保存，亦解釋咗點解我哋而家處於一個關鍵窗口。我哋而家必須識別稀有、未受重視同獨特地面臨風險嘅書籍並保存佢哋。擁有全球所有書籍嘅良好metadata有助於呢個目標。 $10,000 懸賞 將會高度考慮可用性同外觀。 放大時顯示個別ISBN嘅實際metadata，例如書名同作者。 更好嘅空間填充曲線。例如一個之字形，喺第一行從0到4，然後喺第二行反向從5到9——遞歸應用。 唔同或者可自定義嘅色彩方案。 特別視圖用嚟比較Datasets。 解決問題嘅方法，例如其他metadata唔太一致（例如：標題差異好大）。 用評論標註ISBN或者範圍嘅圖片。 識別罕見或者有風險書籍嘅任何啟發式方法。 你可以發揮創意，諗出任何點子！ 代碼 生成呢啲圖片嘅代碼同其他例子可以喺<a %(annas_archive)s>呢個目錄</a>搵到。 我哋設計咗一個緊湊嘅數據格式，所有所需嘅ISBN信息大約係75MB（壓縮後）。數據格式嘅描述同生成代碼可以喺<a %(annas_archive_l1244_1319)s>呢度</a>搵到。為咗獎勵，你唔需要使用呢個，但呢個可能係最方便嘅格式嚟開始。你可以隨意轉換我哋嘅metadata（不過你嘅所有代碼都要係開源）。 我哋等唔切想睇你有咩創意。祝你好運！ Fork呢個repo，並編輯呢篇博客文章HTML（除咗我哋嘅Flask後端外，唔允許其他後端）。 令上面嘅圖片可以平滑放大，咁你可以放大到個別ISBN。點擊ISBN應該帶你去安娜嘅檔案嘅metadata頁面或搜索。 你仍然必須能夠喺所有唔同嘅Datasets之間切換。 國家範圍同出版商範圍應該喺懸停時突出顯示。你可以使用例如<a %(github_xlcnd_isbnlib)s>isbnlib中嘅data4info.py</a>獲取國家信息，並使用我哋嘅“isbngrp”爬取獲取出版商信息（<a %(annas_archive)s>dataset</a>，<a %(annas_archive_2)s>torrent</a>）。 佢必須喺桌面同手機上運行良好。 呢度有好多嘢可以探索，所以我哋宣佈一個懸賞去改善上面嘅可視化。唔同於我哋大多數嘅懸賞，呢個係有時間限制嘅。你必須喺2025-01-31（23:59 UTC）之前<a %(annas_archive)s>提交</a>你嘅開源代碼。 最佳提交將獲得$6,000，第二名係$3,000，第三名係$1,000。所有懸賞將使用Monero (XMR)支付。 以下係最低標準。如果冇提交符合標準，我哋可能仍然會頒發某些懸賞，但呢將由我哋自行決定。 加分項（呢啲只係想法——讓你嘅創意自由發揮）： 你可以完全偏離最低標準，做一個完全唔同嘅可視化。如果真係好精彩，我哋會酌情考慮俾獎勵。 通過發表評論到<a %(annas_archive)s>呢個問題</a>，附上你fork嘅repo、合併請求或者差異嘅鏈接嚟提交。 - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 呢幅圖係1000×800像素。每個像素代表2,500個ISBN。如果我哋有ISBN嘅文件，我哋會將嗰個像素變得更加綠色。如果我哋知道ISBN已經發行，但係冇匹配嘅文件，我哋會將佢變得更加紅色。 喺少於300kb嘅情況下，呢幅圖簡潔地代表咗人類歷史上最大嘅完全開放“書籍清單”（幾百GB壓縮完整）。 佢仲顯示咗：備份書籍仲有好多工作要做（我哋淨係得16%）。 可視化所有ISBN — 2025-01-31前$10,000懸賞 呢幅圖代表咗人類歷史上最大嘅完全開放“書籍清單”。 可視化 除咗概覽圖像，我哋仲可以睇吓我哋獲得嘅個別Datasets。使用下拉選單同按鈕喺佢哋之間切換。 喺呢啲圖片中有好多有趣嘅模式可以睇到。點解喺唔同規模上似乎有啲線條同方塊嘅規律性？空白區域係乜嘢？點解某啲Datasets咁集中？我哋會將呢啲問題留畀讀者作為練習。 - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 結論 有咗呢個標準，我哋可以更逐步咁發佈，並更容易添加新嘅數據來源。我哋已經有幾個令人興奮嘅發佈喺籌備中！ 我哋仲希望其他影子圖書館可以更容易咁鏡像我哋嘅收藏。畢竟，我哋嘅目標係永遠保存人類知識同文化，所以越多冗餘越好。 例子 我哋以最近嘅Z-Library發佈作為例子。佢由兩個集合組成：“<span style="background: #fffaa3">zlib3_records</span>”同“<span style="background: #ffd6fe">zlib3_files</span>”。咁樣我哋可以分開抓取同發佈metadata記錄同實際書籍文件。因此，我哋發佈咗兩個有metadata文件嘅torrents： 我哋仲發佈咗一堆有二進制數據資料夾嘅torrents，但只係針對“<span style="background: #ffd6fe">zlib3_files</span>”集合，總共62個： 通過運行<code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>我哋可以睇到入面嘅內容： 喺呢個情況下，呢係一本書嘅metadata，由Z-Library報告。喺頂層我哋只有“aacid”同“metadata”，但冇“data_folder”，因為冇對應嘅二進制數據。AACID包含“22430000”作為主要ID，我哋可以睇到係從“zlibrary_id”攞嚟嘅。我哋可以預期呢個集合中嘅其他AAC都有相同嘅結構。 而家我哋運行<code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>： 呢個係一個細好多嘅AAC metadata，雖然呢個AAC嘅大部分位於其他地方嘅二進制文件中！畢竟，今次我哋有一個“data_folder”，所以我哋可以預期對應嘅二進制數據位於<code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>。呢個“metadata”包含“zlibrary_id”，所以我哋可以輕鬆將佢同“zlib_records”集合中嘅對應AAC聯繫起來。我哋可以用唔同嘅方法聯繫，例如通過AACID—標準冇規定一定要咁做。 請注意，“metadata”字段本身唔一定要係JSON。佢可以係一個包含XML或者其他數據格式嘅字符串。你甚至可以將metadata信息存喺相關嘅二進制blob中，例如如果佢係大量數據。 異質文件同metadata，盡可能接近原始格式。 二進制數據可以由Nginx等網絡伺服器直接提供。 來源圖書館中嘅異質識別符，甚至缺乏識別符。 metadata同文件數據嘅單獨發佈，或者只發佈metadata（例如我哋嘅ISBNdb發佈）。 通過種子檔案分發，但有其他分發方法嘅可能性（例如IPFS）。 不可變記錄，因為我哋應該假設我哋嘅種子檔案會永久存在。 增量發佈/可追加發佈。 機器可讀同可寫，方便快捷，特別係對我哋嘅技術棧（Python、MySQL、ElasticSearch、Transmission、Debian、ext4）。 某程度上容易人類檢查，但呢個係次於機器可讀性。 容易用標準租用嘅種子伺服器播種我哋嘅集合。 設計目標 我哋唔在乎文件喺磁碟上容易手動導航，或者唔經預處理就可以搜索。 我哋唔在乎同現有圖書館軟件直接兼容。 雖然任何人都應該容易用種子檔案播種我哋嘅集合，但我哋唔期望文件喺冇顯著技術知識同承諾嘅情況下可用。 我哋嘅主要使用情況係分發來自唔同現有集合嘅文件同相關metadata。我哋最重要嘅考慮係： 一些非目標： 由於Anna’s Archive係開源嘅，我哋希望直接使用我哋嘅格式。當我哋刷新我哋嘅搜索索引時，我哋只會訪問公開可用嘅路徑，咁樣任何人fork我哋嘅圖書館都可以快速上手。 <strong>AAC.</strong> AAC（Anna’s Archive 容器）係一個單一項目，由<strong>metadata</strong>組成，並可選擇性包含<strong>二進制數據</strong>，兩者都係不可變嘅。佢有一個全球唯一嘅識別碼，叫做<strong>AACID</strong>。 <strong>AACID.</strong> AACID 嘅格式係咁嘅：<code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>。例如，我哋發佈過嘅一個實際 AACID 係 <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>。 <strong>AACID 範圍。</strong> 由於 AACID 包含單調增加嘅時間戳，我哋可以用嚟表示特定集合內嘅範圍。我哋使用呢個格式：<code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>，時間戳係包含嘅。呢個同 ISO 8601 記法一致。範圍係連續嘅，可以重疊，但如果重疊，必須包含之前喺該集合中發佈嘅相同記錄（因為 AAC 係不可變嘅）。唔允許有缺失記錄。 <code>{collection}</code>：集合名稱，可以包含 ASCII 字母、數字同下劃線（但唔可以有雙下劃線）。 <code>{collection-specific ID}</code>：集合特定嘅識別碼，如果適用，例如 Z-Library ID。可以省略或截短。如果 AACID 超過 150 個字符，必須省略或截短。 <code>{ISO 8601 timestamp}</code>：ISO 8601 嘅簡短版本，永遠係 UTC，例如 <code>20220723T194746Z</code>。呢個數字喺每次發佈時都要單調增加，雖然其具體語義可以因集合而異。我哋建議使用抓取或生成 ID 嘅時間。 <code>{shortuuid}</code>：一個 UUID，但壓縮到 ASCII，例如使用 base57。我哋目前使用 <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python 庫。 <strong>二進制數據文件夾。</strong> 一個包含特定集合中一系列 AAC 嘅二進制數據嘅文件夾。佢哋有以下屬性： 目錄必須包含指定範圍內所有 AAC 嘅數據文件。每個數據文件必須以其 AACID 作為文件名（冇擴展名）。 目錄名稱必須係一個 AACID 範圍，前綴係 <code style="color: green">annas_archive_data__</code>，冇後綴。例如，我哋嘅一個實際發佈有一個叫做<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>嘅目錄。 建議將呢啲資料夾嘅大小控制喺一個可管理嘅範圍內，例如每個唔大過100GB至1TB，雖然呢個建議可能會隨時間改變。 <strong>Collection.</strong> 每個 AAC 都屬於一個集合，根據定義，呢個集合係一個語義一致嘅 AAC 列表。即係話，如果你對 metadata 嘅格式作出重大更改，你就要創建一個新嘅集合。 標準 <strong>Metadata 文件。</strong> Metadata 文件包含一個特定集合中一系列 AAC 嘅 metadata。佢哋有以下屬性： <code>data_folder</code> 係可選嘅，係包含相應二進制數據嘅二進制數據文件夾名稱。該文件夾內相應二進制數據嘅文件名係記錄嘅 AACID。 每個 JSON 對象必須喺頂層包含以下字段：<strong>aacid</strong>、<strong>metadata</strong>、<strong>data_folder</strong>（可選）。唔允許有其他字段。 文件名必須係一個 AACID 範圍，前綴係 <code style="color: red">annas_archive_meta__</code>，後綴係 <code>.jsonl.zstd</code>。例如，我哋嘅一個發佈叫做<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>。 如文件擴展名所示，文件類型係用 <a %(jsonlines)s>JSON Lines</a> 壓縮嘅 <a %(zstd)s>Zstandard</a>。 <code>metadata</code> 係任意嘅 metadata，根據集合嘅語義。佢必須喺集合內語義一致。 <code style="color: red">annas_archive_meta__</code> 前綴可以根據你嘅機構名稱進行調整，例如 <code style="color: red">my_institute_meta__</code>。 <strong>“records” 同 “files” 集合。</strong> 按慣例，將 “records” 同 “files” 作為唔同嘅集合發佈通常比較方便，咁樣佢哋可以喺唔同嘅時間表發佈，例如根據抓取速率。 “record” 係一個只包含 metadata 嘅集合，包含書名、作者、ISBN 等信息，而 “files” 係包含實際文件（pdf、epub）嘅集合。 最終，我哋決定採用一個相對簡單嘅標準。呢個標準比較寬鬆，唔具規範性，仲係一個進行中嘅工作。 <strong>Torrents.</strong> metadata文件同埋二進制數據資料夾可以打包成torrents，每個metadata文件一個torrent，或者每個二進制數據資料夾一個torrent。torrents必須用原始文件/目錄名加上<code>.torrent</code>後綴作為佢哋嘅文件名。 <a %(wikipedia_annas_archive)s>Anna嘅檔案館</a>已經成為目前為止世界上最大嘅影子圖書館，亦係唯一一個規模如此龐大而且完全開源同開放數據嘅影子圖書館。以下係我哋Datasets頁面嘅一個表格（稍作修改）： 我哋通過三種方式實現咗呢個目標： 鏡像現有嘅開放數據影子圖書館（例如Sci-Hub同Library Genesis）。 幫助想要更加開放但冇時間或者資源去做嘅影子圖書館（例如Libgen漫畫收藏）。 抓取唔願意批量分享嘅圖書館（例如Z-Library）。 對於 (2) 同 (3)，我哋而家自己管理住一個相當大嘅種子檔案集合（幾百TB）。到目前為止，我哋將呢啲集合視為一次性處理，意味住為每個集合定制基礎設施同數據組織。呢個為每次發佈增加咗顯著嘅開銷，並且令到進行更多增量發佈特別困難。 所以我哋決定標準化我哋嘅發佈。呢篇係技術博客文章，我哋會介紹我哋嘅標準：<strong>Anna’s Archive Containers</strong>。 Anna嘅檔案館容器（AAC）：標準化世界最大影子圖書館嘅發佈 Anna嘅檔案館已經成為世界上最大嘅影子圖書館，令我哋需要標準化我哋嘅發佈。 300GB+嘅書封面已經發佈 最後，我哋好高興宣佈一個小發佈。喺同運行Libgen.rs分支嘅人合作下，我哋通過torrents同IPFS分享佢哋所有嘅書封面。呢個會將睇封面嘅負載分配到更多機器上，並會更好地保存佢哋。喺好多（但唔係全部）情況下，書封面已經包含喺文件本身，所以呢個係一種「衍生數據」。但將佢放喺IPFS對於Anna’s Archive同各個Library Genesis分支嘅日常運作仍然好有用。 一如以往，你可以喺Pirate Library Mirror（編輯：已移至<a %(wikipedia_annas_archive)s>Anna’s Archive</a>）搵到呢個發佈。我哋唔會喺呢度鏈接佢，但你可以輕鬆搵到。 希望我哋可以放慢步伐，因為我哋而家有一個體面嘅Z-Library替代品。呢個工作量唔係特別可持續。如果你有興趣幫手編程、伺服器操作或保存工作，絕對可以聯絡我哋。仲有好多<a %(annas_archive)s>工作要做</a>。多謝你嘅興趣同支持。 轉用ElasticSearch 有啲查詢用咗好長時間，去到佢哋會霸佔所有開放連接嘅地步。 MySQL預設有一個最小字長，否則你嘅索引會變得好大。有人報告話唔能夠搜尋「Ben Hur」。 搜尋只係喺完全載入內存時稍微快啲，呢個要求我哋要用一部更貴嘅機器去運行，仲要啲命令去喺啟動時預載索引。 我哋唔能夠輕易擴展佢去建立新功能，例如更好嘅<a %(wikipedia_cjk_characters)s>非空格語言嘅分詞</a>、過濾/分面、排序、「你係咪想講」建議、自動完成等等。 我哋其中一個<a %(annas_archive)s>票據</a>係我哋搜尋系統嘅一堆問題。我哋用MySQL全文搜索，因為我哋所有數據都喺MySQL入面。不過佢有佢嘅限制： 同一堆專家傾過之後，我哋決定用ElasticSearch。佢唔係完美（佢哋預設嘅「你係咪想講」建議同自動完成功能好差），但總體嚟講佢比MySQL喺搜尋方面好好多。我哋仍然唔係<a %(youtube)s>太熱衷</a>於用佢去處理任何關鍵數據（雖然佢哋已經做咗好多<a %(elastic_co)s>進步</a>），但總體嚟講我哋對呢個轉變好滿意。 目前，我哋已經實施咗更快嘅搜尋、更好嘅語言支持、更好嘅相關性排序、唔同嘅排序選項，仲有語言/書籍類型/文件類型嘅過濾。如果你對佢點樣運作感到好奇，<a %(annas_archive_l140)s>睇下</a> <a %(annas_archive_l1115)s>呢度</a> <a %(annas_archive_l1635)s>睇下</a>。佢相當易接觸，雖然可以用多啲註釋… Anna嘅檔案庫係完全開源嘅 我哋相信信息應該係免費嘅，我哋自己嘅代碼都唔例外。我哋已經喺我哋私有托管嘅Gitlab實例上發佈咗我哋所有嘅代碼：<a %(annas_archive)s>Anna嘅軟件</a>。我哋仲使用問題追蹤器嚟組織我哋嘅工作。如果你想參與我哋嘅開發，呢度係一個好嘅開始。 為咗畀你一個我哋工作內容嘅概念，睇下我哋最近喺客戶端性能改進方面嘅工作。由於我哋仲未實施分頁，我哋經常會返回好長嘅搜索頁面，有100-200個結果。我哋唔想太早截斷搜索結果，但呢意味住會拖慢某啲設備。為咗解決呢個問題，我哋實施咗一個小技巧：我哋將大部分搜索結果包喺HTML註釋中（<code><!-- --></code>），然後寫咗一個小Javascript，當結果應該顯示時，佢會將註釋解開： DOM「虛擬化」用23行代碼實現，唔需要花巧嘅庫！呢種係喺有限時間同需要解決實際問題時會出現嘅快速務實代碼。有報告話我哋嘅搜尋而家喺慢速設備上運行得好好！ 另一個重大努力係自動化建立數據庫。當我哋啟動時，我哋只係隨意咁將唔同來源拉埋一齊。依家我哋想保持佢哋更新，所以我哋寫咗一堆腳本去下載來自兩個Library Genesis分支嘅新metadata，並整合佢哋。目標唔單止係令我哋嘅檔案有用，仲係令任何想玩轉影子圖書館metadata嘅人都容易。目標係一個Jupyter notebook，入面有各種有趣嘅metadata可用，咁我哋可以做更多研究，例如搞清楚<a %(blog)s>ISBN嘅百分比係咪永遠保存</a>。 最後，我哋重新設計咗我哋嘅捐款系統。你而家可以用信用卡直接將錢存入我哋嘅加密貨幣錢包，唔需要真係了解加密貨幣。我哋會繼續監察呢個喺實際中運行得幾好，但呢個係一件大事。 隨住Z-Library嘅倒閉同佢嘅（據稱）創辦人被捕，我哋一直夜以繼日咁努力，為Anna嘅檔案庫提供一個好嘅替代方案（我哋唔會喺呢度鏈接，但你可以Google佢）。以下係我哋最近取得嘅成就。 Anna嘅更新：完全開源嘅檔案庫，ElasticSearch，超過300GB嘅書籍封面 我哋一直夜以繼日咁努力，為Anna嘅檔案庫提供一個好嘅替代方案。以下係我哋最近取得嘅成就。 分析 語義重複（同一本書嘅唔同掃描）理論上可以被過濾，但呢個幾棘手。當手動查看漫畫時，我哋發現太多誤報。 有啲純粹按 MD5 重複，呢個相對浪費，但過濾掉呢啲只會俾我哋大約 1% in 嘅節省。喺呢個規模上，呢個仍然係大約 1TB，但亦都，喺呢個規模上 1TB 其實唔係好重要。我哋寧願唔冒險喺呢個過程中意外破壞數據。 我哋發現咗一堆非書籍數據，例如基於漫畫書嘅電影。呢個亦都似乎係浪費，因為呢啲已經可以通過其他方式廣泛獲得。不過，我哋意識到唔可以直接過濾掉電影文件，因為仲有 <em>互動漫畫書</em> 喺電腦上發佈，有人錄製並保存為電影。 最終，我哋發現即使刪除收藏中嘅任何嘢，都只係可以節省幾個百分比。然後我哋記得我哋係數據收藏狂，而會鏡像呢啲嘢嘅人都係數據收藏狂，所以，「你講咩，刪除？！」 當你有 95TB 嘅數據倒入你嘅存儲集群時，你會嘗試理解入面有啲咩… 我哋做咗啲分析，睇下可唔可以減少啲大小，例如移除重複。以下係我哋嘅發現： 所以我哋向你哋呈現完整、未經修改嘅收藏。呢啲數據好多，但我哋希望有足夠嘅人會願意去分享。 合作 考慮到佢嘅規模，呢個收藏一直喺我哋嘅願望清單上，所以喺我哋成功備份 Z-Library 之後，我哋將目標放喺呢個收藏上。起初我哋直接抓取，呢個幾有挑戰性，因為佢哋嘅伺服器狀況唔係最好。我哋用呢個方法獲得咗大約 15TB，但進度好慢。 幸運地，我哋成功聯絡到圖書館嘅操作員，佢同意直接將所有數據傳送俾我哋，呢個快好多。即使如此，傳輸同處理所有數據都花咗超過半年時間，我哋差啲因為磁碟損壞而失去所有數據，呢樣會意味住要重新開始。 呢次經歷令我哋相信，將呢啲數據盡快公開係好重要嘅，咁佢可以被廣泛鏡像。我哋只係一兩次唔幸嘅事件就可能會永遠失去呢個收藏！ 收藏 快速行動意味住呢個收藏有啲唔夠組織… 一齊睇下。想像下我哋有個文件系統（實際上我哋將佢分拆成多個 torrent）： 第一個目錄，<code>/repository</code>，係呢個比較有結構嘅部分。呢個目錄包含所謂嘅“千目錄”：每個目錄有一千個文件，喺數據庫中按順序編號。目錄 <code>0</code> 包含 comic_id 0–999 嘅文件，如此類推。 呢個係 Library Genesis 用喺佢嘅小說同非小說收藏上嘅相同方案。呢個概念係每個“千目錄”一旦填滿就會自動變成一個 torrent。 然而，Libgen.li 嘅操作員從未為呢個收藏製作 torrent，所以千目錄可能變得唔方便，然後變成“未排序目錄”。呢啲係 <code>/comics0</code> 到 <code>/comics4</code>。佢哋都有獨特嘅目錄結構，可能對收集文件有意義，但對我哋而家就唔太有意義。幸運地，metadata 仍然直接指向所有呢啲文件，所以佢哋喺磁碟上嘅存儲組織其實唔重要！ metadata 以 MySQL 數據庫嘅形式提供。呢個可以直接喺 Libgen.li 網站下載，但我哋亦會喺 torrent 中提供，連同我哋自己嘅所有 MD5 哈希表。 <q>Dr. Barbara Gordon 嘗試喺圖書館嘅平凡世界中迷失自己…</q> Libgen 分支 首先，講下背景。你可能識 Library Genesis 因為佢哋龐大嘅書籍收藏。較少人知道 Library Genesis 嘅義工仲創建咗其他項目，例如大量嘅雜誌同標準文件收藏、一個 Sci-Hub 嘅完整備份（同 Sci-Hub 創辦人 Alexandra Elbakyan 合作），仲有一個龐大嘅漫畫收藏。 喺某個時候，Library Genesis 鏡像嘅唔同操作員各自分道揚鑣，呢個情況導致咗而家有唔同嘅“分支”，但都仲用緊 Library Genesis 呢個名。Libgen.li 呢個分支獨有呢個漫畫收藏，仲有一個龐大嘅雜誌收藏（我哋都喺度做緊）。 籌款活動 我哋會將呢啲數據分成幾大塊發佈。第一個種子係 <code>/comics0</code>，我哋將佢放入一個巨大嘅 12TB .tar 文件。呢個比你嘅硬碟同種子軟件好過無數細文件。 作為呢次發佈嘅一部分，我哋會進行籌款活動。我哋希望籌集 20,000 美元，以支付呢個收藏嘅運營同合同費用，並支持持續同未來嘅項目。我哋有啲 <em>龐大</em> 嘅項目正在進行中。 <em>我嘅捐款支持緊邊個？</em> 簡單嚟講：我哋係備份人類所有知識同文化，並令其易於獲取。我哋所有嘅代碼同數據都係開源嘅，我哋係一個完全由志願者運行嘅項目，至今已經保存咗 125TB 嘅書籍（除咗 Libgen 同 Scihub 現有嘅種子）。最終我哋係建立一個飛輪，令到人哋可以發現、掃描同備份世界上所有嘅書籍。我哋會喺未來嘅文章中寫我哋嘅大計劃。:) 如果你捐款獲得 12 個月嘅「Amazing Archivist」會員資格（$780），你可以 <strong>「領養一個種子」</strong>，即係我哋會將你嘅用戶名或信息放喺其中一個種子嘅文件名中！ 你可以去 <a %(wikipedia_annas_archive)s>Anna’s Archive</a> 點擊「捐款」按鈕進行捐款。我哋亦都尋找更多志願者：軟件工程師、安全研究員、匿名商家專家同翻譯員。你亦可以通過提供託管服務支持我哋。當然，請分享我哋嘅種子！ 多謝已經咁慷慨支持我哋嘅每一位！你哋真係帶嚟咗改變。 以下係到目前為止發佈嘅種子（我哋仲喺處理其餘嘅）： 所有種子都可以喺 <a %(wikipedia_annas_archive)s>Anna’s Archive</a> 嘅「Datasets」下搵到（我哋唔會直接連結到嗰度，所以呢篇博客嘅連結唔會喺 Reddit、Twitter 等被移除）。從嗰度，跟住連結去 Tor 網站。 <a %(news_ycombinator)s>喺Hacker News上討論</a> 下一步係咩？ 一堆種子對長期保存係好，但對日常訪問就唔係咁好。我哋會同託管夥伴合作，將呢啲數據全部上傳到網絡上（因為 Anna’s Archive 唔會直接託管任何嘢）。當然你可以喺 Anna’s Archive 搵到呢啲下載連結。 我哋亦邀請大家用呢啲數據做嘢！幫我哋更好地分析佢，去重複化佢，放佢上 IPFS，重新混合佢，用佢訓練你嘅 AI 模型，等等。呢啲都係你嘅，我哋等唔切想睇你會點樣用佢。 最後，如之前所講，我哋仲有啲龐大嘅發佈即將到來（如果 <em>有人</em> 可以 <em>意外</em> 發送一個 <em>特定</em> ACS4 數據庫嘅轉儲俾我哋，你知道喺邊度搵到我哋……），以及建立備份世界上所有書籍嘅飛輪。 所以請繼續關注，我哋只係啱啱開始。 - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 最大嘅漫畫書影子圖書館可能係某個Library Genesis分支：Libgen.li。運行嗰個網站嘅唯一管理員成功收集咗一個瘋狂嘅漫畫收藏，超過200萬個文件，總共超過95TB。然而，唔同於其他Library Genesis收藏，呢個唔可以通過torrents批量獲取。你只能夠通過佢慢速嘅個人伺服器逐個訪問呢啲漫畫 — 一個單點故障。直到今日！ 喺呢篇文章入面，我哋會同你講多啲關於呢個收藏，仲有我哋嘅籌款活動，支持更多呢類工作。 Anna’s Archive已經備份咗世界上最大嘅漫畫影子圖書館（95TB）— 你可以幫手種子 世界上最大嘅漫畫書影子圖書館有一個單點故障…直到今日。 警告：呢篇博客文章已經被棄用。我哋決定IPFS仲未準備好。我哋仍然會喺可能嘅情況下從Anna’s Archive鏈接到IPFS上嘅文件，但我哋唔會再自己托管，亦唔建議其他人使用IPFS進行鏡像。請查看我哋嘅Torrents頁面，如果你想幫助保存我哋嘅收藏。 將5,998,794本書放喺IPFS上 多份副本嘅複製 回到我哋最初嘅問題：我哋點樣可以聲稱永久保存我哋嘅收藏？主要問題係我哋嘅收藏通過刮取同開源某啲龐大嘅收藏（喺其他開放數據影子圖書館如Sci-Hub同Library Genesis已經做咗嘅驚人工作之上）以快速速度<a %(torrents_stats)s>增長</a>。 呢個數據增長令收藏喺世界各地鏡像變得更加困難。數據存儲係昂貴嘅！但我哋樂觀，特別係觀察到以下三個趨勢。 我哋嘅收藏總大小，喺過去幾個月中，按 torrent 種子數量劃分。 來自不同來源嘅HDD價格趨勢（點擊查看研究）。 <a %(critical_window_chinese)s>中文版</a>，喺 <a %(reddit)s>Reddit</a> 討論，<a %(news_ycombinator)s>Hacker News</a> 1. 我哋已經摘取咗低垂嘅果實 呢個直接跟隨我哋上面討論嘅優先事項。我哋偏好首先解放大型收藏。依家我哋已經確保咗世界上最大嘅某啲收藏，我哋預計增長會慢好多。 仲有好多細小收藏嘅長尾，仲有新書每日都會被掃描或出版，但速度可能會慢好多。我哋可能仲會翻倍甚至三倍增長，但係喺較長時間內。 OCR嘅改善。 優先事項 科學同工程軟件代碼 以上所有嘅虛構或娛樂版本 地理數據（例如地圖、地質調查） 公司或政府內部數據（洩漏） 測量數據，例如科學測量、經濟數據、公司報告 metadata 記錄（無論係非小說定小說；其他媒體、藝術、人物等；包括評論） 非小說書籍 非小說雜誌、報紙、手冊 非小說講座、紀錄片、播客嘅文字記錄 有機數據，例如 DNA 序列、植物種子或微生物樣本 學術論文、期刊、報告 科學同工程網站、網上討論 法律或法庭程序嘅文字記錄 獨特地面臨毀滅風險（例如因為戰爭、資金削減、訴訟或者政治迫害） 罕見 獨特地被忽視 點解我哋咁關心論文同書籍？我哋撇開對保存嘅基本信念——我哋可能會寫另一篇文章講呢個。咁點解特別係論文同書籍呢？答案好簡單：<strong>信息密度</strong>。 每兆字節存儲中，書面文字喺所有媒體中存儲最多信息。雖然我哋關心知識同文化，但我哋更關心前者。總體上，我哋發現信息密度同保存重要性嘅層次大致如下： 呢個列表嘅排名係有啲隨意嘅——有啲項目係平手或者我哋團隊內部有分歧——而且我哋可能會忘記咗啲重要嘅類別。但大致上呢個係我哋嘅優先次序。 呢啲項目入面有啲同其他嘅太唔同，唔使我哋擔心（或者已經有其他機構處理），例如有機數據或者地理數據。但呢個列表入面大部分項目對我哋嚟講其實係重要嘅。 我哋優先次序嘅另一個大因素係某個作品有幾大風險。我哋偏向集中喺啲： 最後，我哋關心規模。我哋嘅時間同金錢有限，所以如果啲書嘅價值同風險差唔多，我哋寧願用一個月時間拯救 10,000 本書，而唔係 1,000 本。 <em><q>失去嘅無法挽回；但係讓我哋保存剩低嘅：唔係用保險庫同鎖頭將佢哋隔離喺公眾視線同使用之外，將佢哋交畀時間嘅浪費，而係通過多份副本嘅複製，將佢哋置於意外之外。</q></em><br>— Thomas Jefferson, 1791 影子圖書館 代碼可以喺Github上開源，但Github整體唔容易被鏡像，從而保存（雖然喺呢個特定情況下，大多數代碼庫有足夠分佈嘅副本） Metadata記錄可以喺Worldcat網站上自由查看，但唔可以批量下載（直到我哋<a %(worldcat_scrape)s>刮取</a>佢哋） Reddit可以免費使用，但最近因為數據飢渴嘅LLM訓練而設立咗嚴格嘅反刮取措施（稍後會詳細講解） 有好多機構有類似嘅使命同優先次序。的確，有圖書館、檔案館、實驗室、博物館同其他機構負責呢類保存工作。好多都係由政府、個人或者公司資助得好好。但佢哋有一個重大盲點：法律系統。 呢度就係影子圖書館嘅獨特角色，亦係「Anna嘅檔案」存在嘅原因。我哋可以做其他機構唔可以做嘅嘢。唔係話我哋可以保存其他地方非法保存嘅材料。唔係，喺好多地方，建立一個有任何書籍、論文、雜誌等嘅檔案係合法嘅。 但係法律檔案通常缺乏嘅係<strong>冗餘同持久性</strong>。有啲書只係喺某個實體圖書館入面得一份。有啲metadata記錄係由一間公司獨自保管。有啲報紙只係喺一個檔案館入面用微縮膠卷保存。圖書館可能會被削減資金，公司可能會破產，檔案館可能會被炸毀同燒毀。呢啲唔係假設——呢啲係經常發生嘅事。 Anna’s Archive獨特可以做到嘅係大規模儲存多份作品。我哋可以收集論文、書籍、雜誌等等，並大批量分發。我哋目前係通過torrents做到呢一點，但係具體技術唔重要，會隨時間改變。重要嘅係將多份副本分發到世界各地。呢句超過200年前嘅名言依然係真實： 關於公有領域嘅快速說明。由於Anna’s Archive獨特專注於世界上好多地方非法嘅活動，我哋唔會理會廣泛可用嘅收藏，例如公有領域書籍。法律實體通常已經好好咁照顧呢啲。不過，有啲考慮因素令我哋有時會處理公開可用嘅收藏： - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 2. 存儲成本繼續指數下降 3. 資訊密度嘅改善 我哋而家儲存書籍係用佢哋畀我哋嘅原始格式。當然，佢哋係有壓縮過，但通常佢哋仲係大嘅掃描或者係頁面嘅相片。 到目前為止，縮小我哋收藏總體積嘅唯一選擇係更激進嘅壓縮或者去重。不過，要獲得足夠顯著嘅節省，兩者對我哋嚟講都太過失真。相片嘅重度壓縮可以令文字幾乎唔可讀。而去重需要對書籍完全相同有高度信心，呢個通常太唔準確，特別係內容相同但掃描係唔同場合進行。 一直以嚟都有第三個選擇，但佢嘅質量咁差，我哋從未考慮過：OCR，或者光學字符識別。呢個係將相片轉換成純文本嘅過程，通過使用AI去檢測相片入面嘅字符。呢啲工具已經存在咗好耐，並且相當唔錯，但“相當唔錯”對於保存目的嚟講唔夠。 然而，最近嘅多模態深度學習模型取得咗極快嘅進展，雖然成本仲係好高。我哋預期未來幾年準確性同成本都會大幅改善，直到可以實際應用到我哋整個圖書館。 當呢個發生時，我哋可能仲會保存原始文件，但另外我哋可以有一個細好多嘅圖書館版本，大多數人都會想要鏡像。關鍵係原始文本本身壓縮得更好，並且更容易去重，畀我哋帶嚟更多嘅節省。 總體嚟講，預期總文件大小至少可以減少5-10倍，甚至更多。即使係保守嘅5倍減少，我哋都會睇到$1,000–$3,000喺10年內，即使我哋嘅圖書館規模增加三倍。 截至撰寫時，<a %(diskprices)s>硬碟價格</a>每TB約為$12，用過嘅硬碟約為$8，磁帶約為$4。如果我哋保守啲，只睇新硬碟，意味著存儲一個PB約需$12,000。如果我哋假設我哋嘅圖書館會從900TB增長到2.7PB，意味著需要$32,400來鏡像我哋整個圖書館。加上電力、其他硬件成本等等，我哋將其四捨五入到$40,000。或者用磁帶大約係$15,000–$20,000。 一方面<strong>$15,000–$40,000對於所有人類知識嘅總和係一個好抵嘅價錢</strong>。但另一方面，期望大量完整副本有啲高，特別係如果我哋仲希望嗰啲人繼續為其他人種子佢哋嘅torrents。 呢個係今日。但進步係向前行： 過去10年，硬碟每TB嘅成本大約減少咗三分之一，並可能會繼續以類似速度下降。磁帶似乎亦喺類似軌跡上。SSD價格下降得更快，可能喺十年結束時超過HDD價格。 如果呢個成立，咁喺10年內我哋可能只需$5,000–$13,000就可以鏡像我哋整個收藏（1/3），或者如果我哋增長較少，甚至更少。雖然仍然係好多錢，但呢個對於好多人成為可能。而且可能會更好，因為下一點… 喺 Anna’s Archive，我哋經常被問到當總大小已經接近 1 Petabyte（1000 TB）而且仲喺增長中時，我哋點樣可以聲稱永久保存我哋嘅收藏。喺呢篇文章中，我哋會探討我哋嘅理念，並睇下點解未來十年對我哋保存人類知識同文化嘅使命至關重要。 關鍵窗口 如果呢啲預測準確，我哋只需要等幾年，我哋整個收藏就會被廣泛鏡像。因此，用湯瑪斯·傑斐遜嘅話嚟講，“放喺意外之外”。 不幸嘅係，LLM嘅出現同佢哋對數據嘅渴求訓練，令好多版權持有人變得防禦性更強。比佢哋之前更加。好多網站令刮取同存檔變得更難，訴訟四起，而實體圖書館同檔案館繼續被忽視。 我哋只能預期呢啲趨勢會繼續惡化，並且好多作品會喺進入公有領域之前就已經失去。 我哋正處於保存革命嘅前夕，但失去嘅無法挽回。我哋有一個大約5-10年嘅關鍵窗口，期間運營一個影子圖書館同喺世界各地創建多個鏡像仲係相當昂貴，並且期間訪問仲未完全被關閉。 如果我哋可以跨越呢個窗口，咁我哋確實會永久保存人類嘅知識同文化。我哋唔應該浪費呢段時間。我哋唔應該畀呢個關鍵窗口喺我哋面前關閉。 出發啦。 影子圖書館嘅關鍵窗口 當我哋嘅收藏已經接近 1 PB 時，我哋點樣可以聲稱永久保存我哋嘅收藏？ 收藏 關於呢個收藏嘅更多信息。<a %(duxiu)s>讀秀</a>係一個龐大嘅掃描書籍數據庫，由<a %(chaoxing)s>超星數字圖書館集團</a>創建。大多數係學術書籍，掃描係為咗讓大學同圖書館可以數字化訪問。對於我哋嘅英語讀者，<a %(library_princeton)s>普林斯頓</a>同<a %(guides_lib_uw)s>華盛頓大學</a>有好好嘅概述。仲有一篇出色嘅文章提供更多背景：<a %(doi)s>“數字化中國書籍：超星讀秀學者搜索引擎嘅案例研究”</a>（可以喺Anna’s Archive搵到）。 讀秀嘅書籍喺中國互聯網上長期被盜版。通常佢哋被轉售商以少於一美元嘅價格出售。佢哋通常使用中國版嘅Google Drive分發，呢啲平台經常被黑客攻擊以增加存儲空間。可以喺<a %(github_duty_machine)s>呢度</a>同<a %(github_821_github_io)s>呢度</a>搵到啲技術細節。 雖然呢啲書籍已經半公開分發，但要大批量獲得佢哋係相當困難嘅。我哋將呢個列喺我哋嘅待辦事項清單上，並分配咗多個月嘅全職工作去做。然而，最近一位令人難以置信、驚人同有才華嘅志願者聯繫咗我哋，話佢哋已經完成咗所有工作——付出咗巨大代價。佢哋同我哋分享咗完整嘅收藏，無期望任何回報，除咗長期保存嘅保證。真係好值得讚賞。佢哋同意以呢種方式尋求幫助，將收藏進行OCR。 呢個收藏有7,543,702個文件。呢個數量比Library Genesis嘅非小說類（大約5.3百萬）仲多。總文件大小大約係359TB（326TiB）。 我哋對其他提案同想法持開放態度。只需聯繫我哋。查看Anna’s Archive以獲取更多關於我哋收藏、保存工作同你可以點樣幫助嘅信息。多謝！ 示例頁面 為咗向我哋證明你有一個好嘅流程，呢度有啲示例頁面可以開始，來自一本關於超導體嘅書。你嘅流程應該能夠正確處理數學、表格、圖表、註腳等等。 將你處理過嘅頁面發送到我哋嘅電郵。如果佢哋睇起嚟唔錯，我哋會私下再發送更多畀你，我哋期望你能夠快速運行你嘅流程。一旦我哋滿意，我哋可以達成協議。 - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） <a %(duxiu_exclusive_chinese)s>中文版</a>, <a %(news_ycombinator)s>喺Hacker News討論</a> 呢係一篇短嘅博客文章。我哋尋找一間公司或者機構幫助我哋進行OCR同文本提取，為我哋獲得嘅龐大收藏提供獨家早期訪問。喺禁運期之後，我哋當然會釋放整個收藏。 高質量嘅學術文本對於訓練LLM非常有用。雖然我哋嘅收藏係中文，但呢啲文本對於訓練英文LLM都應該有用：模型似乎無論來源語言都能夠編碼概念同知識。 為咗做到呢點，文本需要從掃描中提取出嚟。Anna’s Archive可以得到咩？為用戶提供書籍嘅全文搜索。 因為我哋嘅目標同LLM開發者一致，我哋尋求一位合作夥伴。如果你能夠進行適當嘅OCR同文本提取，我哋願意畀你<strong>獨家提前訪問呢個收藏一年</strong>。如果你願意同我哋分享你嘅整個流程代碼，我哋願意將收藏禁運更長時間。 LLM公司獨家獲得全球最大嘅中文非小說書籍收藏 Anna’s Archive獲得咗一個獨特嘅7.5百萬/350TB中文非小說書籍收藏——比Library Genesis仲大。我哋願意畀一間LLM公司獨家訪問，以換取高質量嘅OCR同文本提取。 系統架構 假設你搵到啲願意托管你網站而唔會關閉你嘅公司——我哋叫佢哋做“愛自由嘅供應商”😄。你會好快發現同佢哋托管一切係幾貴，所以你可能想搵啲“平價供應商”喺嗰度做實際托管，通過愛自由嘅供應商代理。如果你做得啱，平價供應商永遠唔會知道你托管咩，亦唔會收到任何投訴。 即使有咁多供應商，佢哋隨時可能關閉你，所以你仲需要冗餘。我哋需要喺我哋技術棧嘅所有層面做到呢點。 一間有啲愛自由嘅公司將自己放喺一個有趣嘅位置就係Cloudflare。佢哋<a %(blog_cloudflare)s>聲稱</a>佢哋唔係托管供應商，而係一個公用事業，好似ISP。因此佢哋唔受DMCA或者其他下架請求影響，會將任何請求轉發到你實際嘅托管供應商。佢哋甚至去到法庭保護呢個結構。因此我哋可以用佢哋作為另一層緩存同保護。 Cloudflare唔接受匿名支付，所以我哋只能用佢哋嘅免費計劃。呢意味住我哋唔可以用佢哋嘅負載平衡或者故障轉移功能。因此我哋喺域名層面<a %(annas_archive_l255)s>自己實現咗呢個功能</a>。喺頁面加載時，瀏覽器會檢查當前域名仲可唔可以用，如果唔得，就會重寫所有URL到另一個域名。由於Cloudflare緩存咗好多頁面，呢意味住用戶可以登陸我哋嘅主域名，即使代理伺服器停咗，然後喺下一次點擊時轉到另一個域名。 我哋仲有正常嘅運營問題要處理，例如監控伺服器健康狀況，記錄後端同前端錯誤等等。我哋嘅故障轉移架構喺呢方面提供咗更多嘅穩定性，例如喺其中一個域名上運行一套完全唔同嘅伺服器。我哋甚至可以喺呢個獨立域名上運行舊版本嘅代碼同Datasets，以防主版本出現未被發現嘅嚴重錯誤。 我哋仲可以防範Cloudflare反對我哋，通過喺其中一個域名上移除佢，例如呢個獨立域名。呢啲想法嘅唔同組合係可能嘅。 結論 學習點樣設置一個穩健同有韌性嘅影子圖書館搜索引擎係一個有趣嘅經歷。仲有好多細節會喺之後嘅文章中分享，所以如果你想了解更多，請話俾我知！ 一如以往，我哋尋求捐款以支持呢項工作，所以記得去Anna’s Archive嘅捐款頁面睇睇。我哋亦尋求其他類型嘅支持，例如資助、長期贊助商、高風險支付供應商，甚至可能係（有品味嘅！）廣告。如果你想貢獻你嘅時間同技能，我哋一直都需要開發者、翻譯員等等。多謝你嘅關注同支持。 創新代幣 我哋嘅技術棧係故意設計得好悶。我哋用Flask、MariaDB同ElasticSearch。就係咁簡單。搜尋基本上係一個已經解決嘅問題，我哋唔打算重新發明。除此之外，我哋要將我哋嘅<a %(mcfunley)s>創新代幣</a>用喺其他嘢上面：唔畀當局封殺。 咁Anna’s Archive究竟係幾合法或者非法呢？呢個主要取決於法律管轄區。大多數國家相信某種形式嘅版權，意味住人或者公司會被分配某類作品嘅獨家壟斷權，持續一段時間。順帶一提，喺Anna’s Archive，我哋相信雖然版權有啲好處，但整體嚟講對社會係負面影響——但呢個係另一個故事。 呢個對某些作品嘅獨家壟斷意味住，除咗呢個壟斷以外嘅任何人直接分發呢啲作品都係非法嘅——包括我哋。但Anna’s Archive係一個搜尋引擎，唔會直接分發呢啲作品（至少唔係喺我哋嘅clearnet網站上），所以我哋應該冇問題，係咪？唔係咁簡單。喺好多管轄區，唔單止分發版權作品係非法，連連結到分發呢啲作品嘅地方都係非法。美國嘅DMCA法律就係一個典型例子。 呢個係最嚴格嘅一端。喺另一端，理論上可能有啲國家完全冇版權法，但呢啲國家其實唔存在。幾乎每個國家都有某種形式嘅版權法。執法係另一回事。有好多國家嘅政府唔在乎執行版權法。仲有啲國家介乎兩個極端之間，禁止分發版權作品，但唔禁止連結到呢啲作品。 另一個考慮係公司層面。如果一間公司喺一個唔在乎版權嘅管轄區運作，但公司本身唔願意冒任何風險，咁佢哋可能會喺有人投訴你嘅網站時即刻關閉。 最後，一個大考慮係支付。由於我哋需要保持匿名，我哋唔可以用傳統支付方式。呢個令我哋只能用加密貨幣，而只有少數公司支持呢啲（有啲虛擬借記卡係用加密貨幣支付，但佢哋通常唔被接受）。 - Anna同團隊（<a %(reddit)s>Reddit</a>，<a %(t_me)s>Telegram</a>） 我運行<a %(wikipedia_annas_archive)s>Anna’s Archive</a>，呢個係世界上最大嘅開源非牟利搜索引擎，專注於<a %(wikipedia_shadow_library)s>影子圖書館</a>，例如Sci-Hub、Library Genesis同Z-Library。我哋嘅目標係令知識同文化易於獲得，最終建立一個社區，讓人們一齊存檔同保存<a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>世界上所有嘅書籍</a>。 喺呢篇文章中，我會展示我哋點樣運行呢個網站，以及運行一個法律地位有問題嘅網站所帶來嘅獨特挑戰，因為冇“影子慈善機構嘅AWS”。 <em>仲可以睇下姊妹文章<a %(blog_how_to_become_a_pirate_archivist)s>點樣成為一個海盜檔案員</a>。</em> 點樣運行一個影子圖書館：Anna’s Archive嘅運作 冇<q>AWS for影子慈善機構，</q>咁我哋點樣運行Anna’s Archive？ 工具 應用伺服器：Flask、MariaDB、ElasticSearch、Docker。 開發：Gitlab、Weblate、Zulip。 伺服器管理：Ansible、Checkmk、UFW。 洋蔥靜態託管：Tor，Nginx。 代理伺服器：Varnish。 睇下我哋用咩工具去完成呢一切。隨住我哋遇到新問題同搵到新解決方案，呢個係不斷演變嘅。 有啲決定我哋反覆考慮過。其一係伺服器之間嘅通訊：我哋以前用Wireguard，但發現佢有時會停止傳輸數據，或者只係單向傳輸數據。我哋試過幾個唔同嘅Wireguard設置，例如<a %(github_costela_wesher)s>wesher</a>同<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>。我哋亦試過用SSH隧道端口，使用autossh同sshuttle，但遇到<a %(github_sshuttle)s>問題</a>（雖然我仲未清楚autossh係咪有TCP-over-TCP問題—我覺得呢個解決方案有啲唔穩定，但可能其實冇問題？）。 相反，我哋改返用伺服器之間嘅直接連接，通過UFW嘅IP過濾隱藏伺服器係廉價供應商上運行。呢個方法嘅缺點係Docker同UFW唔係好配合，除非你用<code>network_mode: "host"</code>。呢啲都比較容易出錯，因為只要有少少配置錯誤，你就會將伺服器暴露喺互聯網上。可能我哋應該返去用autossh—非常歡迎大家提供意見。 我哋亦反覆考慮過Varnish同Nginx。我哋而家鍾意Varnish，但佢都有啲怪癖同粗糙嘅地方。Checkmk亦係咁：我哋唔係好鍾意，但而家用得住。Weblate都算OK但唔係好出色—我有時擔心佢會喺我同步git repo時丟失數據。Flask整體上不錯，但有啲怪癖令我哋花咗好多時間去調試，例如配置自定義域名，或者同SqlAlchemy集成嘅問題。 到目前為止，其他工具都表現得非常好：我哋對MariaDB、ElasticSearch、Gitlab、Zulip、Docker同Tor冇乜嚴重嘅投訴。呢啲都有啲問題，但冇乜太嚴重或者耗時嘅。 社群 第一個挑戰可能會令你驚訝。佢唔係技術問題，亦唔係法律問題。佢係一個心理問題：喺陰影中做呢份工作可以非常孤獨。視乎你計劃做咩，仲有你嘅威脅模型，你可能需要非常小心。喺光譜嘅一端，我哋有啲人好似 Sci-Hub 嘅創辦人 Alexandra Elbakyan*，佢對自己嘅活動非常公開。但如果佢喺呢個時候訪問西方國家，佢有好大機會被捕，可能面臨數十年嘅監禁。呢係你願意承擔嘅風險嗎？我哋喺光譜嘅另一端；非常小心唔留下任何痕跡，並擁有強大嘅操作安全。 * 正如 HN 上 "ynno" 所提到，Alexandra 最初唔想被人知道：「佢嘅伺服器設置為發出詳細嘅 PHP 錯誤信息，包括故障源文件嘅完整路徑，呢啲路徑喺 /home/<USER>/ homelab」會變得老套）。 呢就係點解搵到一個社群係咁重要。你可以通過向啲非常親密嘅朋友傾訴，放棄啲操作安全，呢啲朋友係你知道可以深深信任嘅。即使咁樣都要小心唔好將任何嘢寫低，以防佢哋需要將佢哋嘅電郵交畀當局，或者佢哋嘅設備以其他方式被入侵。 更好嘅係搵到啲同道中人。如果你嘅親密朋友有興趣加入你，咁就太好啦！否則，你可能可以喺網上搵到其他人。可惜呢個仍然係一個小眾社群。到目前為止，我哋只係搵到少數其他活躍喺呢個領域嘅人。好嘅起點似乎係 Library Genesis 論壇同 r/DataHoarder。Archive Team 亦都有志同道合嘅人，雖然佢哋喺法律範圍內運作（即使喺法律嘅灰色地帶）。傳統嘅「warez」同盜版場景亦都有啲人有類似嘅想法。 我哋歡迎大家提供點子，點樣促進社群同探索新嘅想法。隨時可以喺Twitter或者Reddit上面搵我哋傾偈。可能我哋可以搞一個論壇或者傾偈群組。不過有一個挑戰就係，喺常用平台上面呢啲嘢好容易畀人審查，所以我哋可能要自己搞。仲有一個取捨就係，呢啲討論係咪要完全公開（可以有更多人參與）定係要私密啲（唔畀潛在嘅「目標」知道我哋準備要刮佢哋）。我哋要諗下呢個問題。如果你對呢啲有興趣，記得話我哋知！ 結論 希望呢個對啱啱開始嘅海盜檔案管理員有幫助。我哋好期待歡迎你加入呢個世界，所以唔好猶豫，隨時聯絡我哋。讓我哋盡可能保存世界嘅知識同文化，並廣泛地鏡像佢。 項目 4. 數據選擇 通常你可以用 metadata 去揀選一個合理嘅數據子集嚟下載。即使你最終想下載所有數據，優先下載最重要嘅項目都係有用嘅，因為你可能會被發現而令防禦加強，或者因為你需要買多啲硬碟，或者因為你生活中有其他事情發生，令你未能下載所有嘢。 例如，一個集合可能有多個版本嘅同一資源（例如一本書或者一部電影），其中一個被標記為最佳質量。首先保存呢啲版本係好合理嘅。你可能最終想保存所有版本，因為有時 metadata 可能標記錯誤，或者版本之間可能有未知嘅取捨（例如，「最佳版本」可能喺大多數方面係最好，但喺其他方面較差，例如電影有更高嘅解析度但冇字幕）。 你亦可以搜索你嘅 metadata 數據庫去搵有趣嘅嘢。最大嘅文件係乜嘢，點解咁大？最細嘅文件係乜嘢？喺某啲類別、語言等方面有冇有趣或者意想不到嘅模式？有冇重複或者非常相似嘅標題？數據添加嘅時間有冇模式，例如某日有大量文件一次過添加？你可以通過以唔同方式查看數據集學到好多嘢。 喺我哋嘅情況下，我哋將 Z-Library 嘅書籍同 Library Genesis 嘅 md5 哈希值進行去重，從而節省咗大量下載時間同硬碟空間。不過呢個係一個相當獨特嘅情況。喺大多數情況下，冇全面嘅數據庫記錄住已經被其他人妥善保存嘅文件。呢個本身對某啲人嚟講係一個巨大嘅機會。擁有一個定期更新嘅概覽，顯示已經喺 torrent 網站廣泛分享嘅音樂同電影，從而降低佢哋喺盜版鏡像中嘅優先級，會係一件好事。 6. 分發 你有咗數據，從而擁有咗世界上第一個你目標嘅盜版鏡像（大概）。喺好多方面，最難嘅部分已經過去，但最危險嘅部分仲喺前面。畢竟，到目前為止你一直都係隱秘嘅；喺雷達下飛行。你所要做嘅就係全程使用好嘅 VPN，唔喺任何表格中填寫你嘅個人資料（當然），或者使用特別嘅瀏覽器會話（甚至係唔同嘅電腦）。 而家你要分發數據。喺我哋嘅情況下，我哋首先想將書籍返還到 Library Genesis，但很快發現咗其中嘅困難（小說同非小說分類）。所以我哋決定用 Library Genesis 風格嘅 torrent 進行分發。如果你有機會參與現有項目，咁可以幫你節省好多時間。不過，目前冇好多組織良好嘅盜版鏡像。 所以假設你決定自己分發 torrent。試下保持文件細小，咁樣容易喺其他網站上鏡像。然後你要自己播種 torrent，同時保持匿名。你可以用 VPN（有或者冇端口轉發），或者用洗過嘅比特幣支付 Seedbox。如果你唔知道其中啲術語係乜意思，你會有一堆嘢要讀，因為了解呢度嘅風險取捨係好重要嘅。 你可以喺現有嘅 torrent 網站上托管 torrent 文件。喺我哋嘅情況下，我哋選擇咗實際上托管一個網站，因為我哋亦想以清晰嘅方式傳播我哋嘅理念。你可以用類似嘅方式自己做呢件事（我哋用 Njalla 進行域名同托管，並用洗過嘅比特幣支付），但亦可以隨時聯繫我哋，讓我哋幫你托管你嘅 torrent。如果呢個想法流行起來，我哋希望隨時間建立一個全面嘅盜版鏡像索引。 至於 VPN 選擇，呢方面已經有好多文章，所以我哋只係重複一般建議：按聲譽選擇。我哋認為，經過法院測試嘅無日誌政策同長期保護隱私記錄係最低風險嘅選擇。請注意，即使你做咗所有正確嘅事情，你都唔可能將風險降到零。例如，當你播種你嘅 torrent 時，一個高度動機嘅國家行為者可能會查看 VPN 服務器嘅進出數據流，並推斷出你係邊個。或者你可能會簡單地搞錯咗。我哋可能已經搞錯過，將來亦會再搞錯。幸運地，國家唔係好關心<em>盜版</em>。 每個項目都要做一個決定，係咪用之前嘅身份發佈。如果你繼續用同一個名，咁早期項目中嘅操作安全錯誤可能會反過來咬你。但用唔同名發佈意味住你唔會建立一個持久嘅聲譽。我哋選擇咗從一開始就有強大嘅操作安全，所以我哋可以繼續用同一個身份，但如果我哋搞錯咗或者情況需要，我哋唔會猶豫用唔同名發佈。 傳播消息可能會有啲棘手。如我哋所講，呢個仲係一個小眾社區。我哋最初喺 Reddit 發佈，但真正喺 Hacker News 上獲得咗關注。暫時我哋嘅建議係喺幾個地方發佈，睇下會發生乜嘢。再一次，聯繫我哋。我哋好樂意傳播更多盜版檔案保存努力嘅消息。 1. 領域選擇 / 哲學 知識同文化遺產需要保存嘅嘢唔少，呢個可以好壓力大。所以通常有用嘅係停一停，諗下你可以有咩貢獻。 每個人對呢個都有唔同嘅諗法，但呢度有啲問題你可以問下自己： 喺我哋嘅情況，我哋特別關心科學嘅長期保存。我哋知道Library Genesis，佢用torrents多次完全鏡像。我哋好鍾意呢個點子。然後有一日，我哋其中一個試圖喺Library Genesis搵啲科學教科書，但搵唔到，令我哋懷疑佢係咪真係咁完整。然後我哋喺網上搵嗰啲教科書，發現佢哋喺其他地方，呢個就種下咗我哋項目嘅種子。即使喺我哋知道Z-Library之前，我哋已經有唔想手動收集所有嗰啲書嘅想法，而係專注喺鏡像現有嘅收藏，然後將佢哋貢獻返畀Library Genesis。 你有咩技能可以幫到你？例如，如果你係網上安全專家，你可以搵到方法去破解安全目標嘅IP封鎖。如果你擅長組織社群，咁可能你可以召集一班人圍住一個目標。雖然咁，但識啲編程都係有用嘅，至少可以喺呢個過程中保持良好嘅操作安全。 有咩高槓桿嘅領域可以專注？如果你打算花X小時喺海盜存檔上面，咁點樣可以獲得最大嘅「回報」？ 你有咩獨特嘅諗法？你可能有啲有趣嘅點子或者方法，其他人可能會錯過。 你有幾多時間可以用喺呢個上面？我哋嘅建議係由細開始，當你熟悉咗之後再做大啲嘅項目，但呢個可以好消耗時間。 點解你對呢個有興趣？你熱衷於咩？如果我哋可以搵到一班人，佢哋都存檔佢哋特別關心嘅嘢，咁就可以覆蓋好多！你會比一般人更加了解你嘅熱情，例如咩係重要嘅數據要保存，咩係最好嘅收藏同網上社群，等等。 3. Metadata抓取 添加/修改日期：咁你可以之後返嚟下載之前冇下載嘅文件（雖然你通常都可以用ID或者哈希值嚟做呢個）。 哈希值（md5, sha1）：確認你正確下載咗文件。 ID：可以係內部ID，但係ISBN或者DOI呢啲ID都好有用。 文件名 / 位置 描述、類別、標籤、作者、語言等等。 大小：用嚟計算你需要幾多磁碟空間。 我哋而家講得技術性啲。其實喺網站上抓取metadata，我哋保持得好簡單。我哋用Python腳本，有時用curl，仲有一個MySQL數據庫嚟儲存結果。我哋冇用過啲可以映射複雜網站嘅高級抓取軟件，因為到目前為止，我哋只需要通過枚舉ID同解析HTML嚟抓取一兩種頁面。如果冇容易枚舉嘅頁面，你可能需要一個適當嘅爬蟲嚟試圖搵到所有頁面。 喺你開始抓取整個網站之前，試下手動做一陣。自己睇幾十頁，了解下點樣運作。有時你會喺呢個過程中遇到IP封鎖或者其他有趣嘅行為。數據抓取都係一樣：喺深入呢個目標之前，確保你可以有效下載佢嘅數據。 為咗繞過限制，你可以試下幾樣嘢。有冇其他IP地址或者伺服器托管相同數據但冇相同限制？有冇API端點冇限制，而其他有？喺咩下載速度下你嘅IP會被封鎖，封鎖幾耐？或者你唔係被封鎖而係被限速？如果你創建一個用戶賬戶，情況會點改變？你可以用HTTP/2保持連接開啟，咁會唔會增加你請求頁面嘅速度？有冇頁面列出多個文件一次，嗰度列出嘅信息夠唔夠？ 你可能想保存嘅嘢包括： 我哋通常分兩個階段做呢個。首先我哋下載原始HTML文件，通常直接進入MySQL（避免大量小文件，呢個我哋喺下面會詳細講）。然後，喺一個獨立步驟中，我哋會通過嗰啲HTML文件並將佢哋解析成實際嘅MySQL表。咁如果你發現解析代碼有錯誤，你唔需要重新下載所有嘢，因為你可以用新代碼重新處理HTML文件。呢個步驟通常都容易並行化處理，從而節省時間（而且你可以喺抓取運行時寫處理代碼，而唔需要一次寫兩個步驟）。 最後，請注意，對於某啲目標嚟講，metadata 擷取就係全部。外面有啲巨大嘅 metadata 集合冇被妥善保存。 標題 領域選擇 / 哲學：你大概想專注喺邊度，點解？有咩獨特嘅熱情、技能同環境可以幫到你？ 目標選擇：你會鏡像邊個具體嘅收藏？ Metadata刮取：記錄文件嘅信息，但唔係實際下載嗰啲（通常大得多）文件。 數據選擇：根據metadata，縮窄而家最相關嘅數據去存檔。可能係全部，但通常有合理嘅方法去節省空間同帶寬。 數據刮取：實際獲取數據。 分發：將佢打包成torrents，喺某處宣佈，讓人傳播。 5. 數據擷取 而家你準備好實際上批量下載數據。正如之前提到嘅，喺呢個階段你應該已經手動下載咗一堆文件，以更好理解目標嘅行為同限制。不過，當你實際上一次過下載大量文件時，仲會有驚喜等住你。 我哋嘅建議主要係保持簡單。首先只係下載一堆文件。你可以用 Python，然後擴展到多線程。但有時甚至更簡單嘅方法係直接從數據庫生成 Bash 文件，然後喺多個終端窗口中運行多個文件以擴展規模。喺呢度值得一提嘅一個快速技術技巧係喺 MySQL 中使用 OUTFILE，如果你喺 mysqld.cnf 中禁用 "secure_file_priv"（如果你用 Linux，記得亦要禁用/覆蓋 AppArmor）。 我哋將數據存喺簡單嘅硬碟上。用你有嘅嘢開始，然後慢慢擴展。諗住存儲幾百 TB 嘅數據可能會令人不知所措。如果你面對呢個情況，首先放出一個好嘅子集，然後喺你嘅公告中請求幫助存儲其餘嘅數據。如果你想自己買多啲硬碟，r/DataHoarder 有啲好資源可以幫你搵到好價錢。 盡量唔好太擔心花俏嘅文件系統。設置 ZFS 呢啲嘢好容易會掉入兔子洞。不過有一個技術細節要注意，就係好多文件系統唔係好處理大量文件。我哋發現一個簡單嘅解決方法係創建多個目錄，例如用唔同嘅 ID 範圍或者哈希前綴。 下載數據之後，記得用 metadata 中嘅哈希值檢查文件嘅完整性（如果有）。 2. 目標選擇 可接觸：唔會用好多層保護去防止你抓取佢哋嘅metadata同數據。 特別見解：你對呢個目標有啲特別嘅資訊，例如你有特別嘅途徑可以接觸到呢個收藏，或者你搵到點樣破解佢哋嘅防禦。呢個唔係必須嘅（我哋即將推出嘅項目冇做啲特別嘢），但係絕對有幫助！ 大 咁，我哋有咗我哋要睇嘅範圍，咁而家要鏡像邊個特定嘅收藏呢？有幾樣嘢可以令目標變得理想： 當我哋喺Library Genesis以外嘅網站搵到我哋嘅科學教科書時，我哋試圖了解佢哋點樣上到網絡。然後我哋發現咗Z-Library，並了解到雖然大多數書唔係首先喺嗰度出現，但最終都會喺嗰度出現。我哋了解咗佢同Library Genesis嘅關係，仲有（財務）激勵結構同優越嘅用戶界面，呢啲都令佢成為一個更加完整嘅收藏。然後我哋做咗啲初步嘅metadata同數據抓取，發現我哋可以繞過佢哋嘅IP下載限制，利用我哋其中一個成員對大量代理伺服器嘅特別接觸。 當你探索唔同目標時，已經好重要要用VPN同一次性電郵地址嚟隱藏你嘅行蹤，我哋會喺後面詳細講。 獨特：唔係已經被其他項目覆蓋得好好。 當我哋做一個項目嘅時候，會有幾個階段： 呢啲唔係完全獨立嘅階段，通常後期階段嘅見解會令你返去早期階段。例如，喺metadata刮取期間，你可能會發現你選擇嘅目標有超出你技能水平嘅防禦機制（例如IP封鎖），所以你要返去搵另一個目標。 - Anna同團隊（<a %(reddit)s>Reddit</a>） 可以寫成整本書去講解數碼保存嘅原因，尤其係海盜檔案保存，但我哋會為唔太熟悉嘅人提供一個簡單嘅介紹。世界正喺生產比以往更多嘅知識同文化，但同時亦有更多嘅被遺失。人類大多數將呢啲遺產交託畀學術出版商、串流服務同社交媒體公司等企業，但佢哋往往未能證明自己係好嘅管理者。可以睇下紀錄片《數碼失憶症》，或者任何 Jason Scott 嘅演講。 有啲機構喺盡力保存佢哋能夠保存嘅一切，但佢哋受法律約束。作為海盜，我哋處於一個獨特嘅位置，可以保存佢哋無法觸及嘅收藏，因為版權執法或者其他限制。我哋仲可以喺世界各地多次鏡像收藏，從而增加適當保存嘅機會。 而家，我哋唔會討論知識產權嘅利弊、違法嘅道德性、對審查制度嘅思考，或者知識同文化訪問嘅問題。擺脫咗呢啲之後，讓我哋深入探討點樣做。我哋會分享我哋嘅團隊點樣成為海盜檔案員，仲有喺呢過程中學到嘅教訓。當你踏上呢段旅程時會遇到好多挑戰，希望我哋可以幫助你解決其中一啲。 點樣成為一個海盜檔案員 第一個挑戰可能會令你驚訝。佢唔係技術問題，亦唔係法律問題。佢係一個心理問題。 喺我哋深入探討之前，兩個關於 Pirate Library Mirror 嘅更新（編輯：已移至 <a %(wikipedia_annas_archive)s>Anna’s Archive</a>）： 我哋收到咗啲非常慷慨嘅捐款。第一筆係來自一位匿名人士嘅$10k，佢亦都一直支持 "bookwarrior"，Library Genesis 嘅原創創辦人。特別感謝 bookwarrior 促成呢次捐款。第二筆係來自另一位匿名捐贈者嘅$10k，佢喺我哋上次發佈後聯絡我哋，並受到啟發去幫助。我哋仲收到咗好多較小嘅捐款。非常感謝你哋嘅慷慨支持。我哋有啲令人興奮嘅新項目喺籌備中，呢啲捐款會支持呢啲項目，所以請繼續關注。 我哋喺第二次發佈嘅大小上遇到咗啲技術困難，但我哋嘅種子而家已經上線並開始做種。我哋仲收到咗一位匿名人士嘅慷慨提議，喺佢哋嘅超高速伺服器上做種我哋嘅收藏，所以我哋正喺佢哋嘅機器上進行特別上傳，之後其他下載收藏嘅人應該會見到速度有大幅提升。 博客文章 你好，我係Anna。我創建咗<a %(wikipedia_annas_archive)s>Anna’s Archive</a>，世界上最大嘅影子圖書館。呢個係我嘅個人博客，我同我嘅團隊成員喺度寫關於盜版、數字保存等等。 喺<a %(reddit)s>Reddit</a>上同我聯繫。 請注意呢個網站只係一個博客。我哋只喺度託管我哋自己嘅文字。冇託管或者鏈接任何種子或者其他受版權保護嘅文件。 <strong>圖書館</strong> - 好似大多數圖書館一樣，我哋主要集中喺書籍等書面材料。未來我哋可能會擴展到其他類型嘅媒體。 <strong>鏡像</strong> - 我哋嚴格嚟講係現有圖書館嘅鏡像。我哋專注於保存，而唔係令書籍容易搜尋同下載（訪問）或者培養一個大嘅社群去貢獻新書（來源）。 <strong>海盜</strong> - 我哋故意違反大多數國家嘅版權法。呢樣嘢令我哋可以做法律實體做唔到嘅事：確保書籍被廣泛地鏡像。 <em>我哋唔會喺呢個博客連結到文件。請自行尋找。</em> - Anna同團隊（<a %(reddit)s>Reddit</a>） 呢個項目（編輯：已移至<a %(wikipedia_annas_archive)s>Anna’s Archive</a>）旨在為保存同解放人類知識作出貢獻。我哋喺前人嘅腳步下，作出我哋細微而謙卑嘅貢獻。 呢個項目嘅重點由其名稱所展示： 我哋鏡像嘅第一個圖書館係Z-Library。呢個係一個受歡迎（同非法）嘅圖書館。佢哋攞咗Library Genesis嘅收藏，並令其容易搜尋。除此之外，佢哋喺吸引新書貢獻方面變得非常有效，通過提供各種福利去激勵貢獻者。佢哋目前冇將呢啲新書貢獻返畀Library Genesis。與Library Genesis唔同，佢哋冇令其收藏容易被鏡像，呢樣嘢阻止咗廣泛嘅保存。呢樣嘢對佢哋嘅商業模式好重要，因為佢哋收費畀人批量訪問其收藏（每日超過10本書）。 我哋唔會對於收費提供非法書籍收藏嘅批量訪問作出道德判斷。無可否認，Z-Library 喺擴大知識訪問同獲取更多書籍方面取得咗成功。我哋只係喺度做我哋嘅部分：確保呢個私人收藏嘅長期保存。 我哋想邀請你通過下載同分享我哋嘅種子檔案，幫助保存同解放人類知識。請參閱項目頁面以獲取更多關於數據如何組織嘅信息。 我哋亦非常歡迎你貢獻你嘅想法，關於下一步應該鏡像邊啲收藏，點樣去做。齊心協力，我哋可以達成好多。呢只係無數貢獻中嘅一小部分。多謝你所做嘅一切。 介紹海盜圖書館鏡像：保存7TB嘅書籍（唔喺Libgen入面） 10% o人類書面遺產永久保存 <strong>Google。</strong> 畢竟，佢哋為Google Books做過呢個研究。不過，佢哋嘅metadata唔可以批量訪問，而且相當難以抓取。 <strong>各種個別圖書館系統同檔案館。</strong> 有啲圖書館同檔案館冇被上面提到嘅任何一個索引同聚合，通常因為佢哋資金不足，或者因為其他原因唔想同Open Library、OCLC、Google等分享佢哋嘅數據。好多呢啲都有數碼記錄可以通過互聯網訪問，佢哋通常唔係好受保護，所以如果你想幫手同學習奇怪嘅圖書館系統，呢啲係好好嘅起點。 <strong>ISBNdb。</strong> 呢個係呢篇博客文章嘅主題。ISBNdb從各種網站抓取書籍metadata，特別係定價數據，然後賣畀書商，咁佢哋可以根據市場其他書籍定價。由於ISBN而家相當普遍，佢哋有效地建立咗「每本書嘅網頁」。 <strong>Open Library。</strong> 如前所述，呢個係佢哋嘅全部使命。佢哋從合作嘅圖書館同國家檔案中獲得咗大量嘅圖書館數據，並繼續咁做。佢哋仲有志願圖書館員同技術團隊，試圖去重記錄，並用各種metadata標記佢哋。最好嘅係，佢哋嘅數據集係完全開放嘅。你可以簡單<a %(openlibrary)s>下載佢</a>。 <strong>WorldCat。</strong> 呢個係由非牟利OCLC運行嘅網站，佢哋賣圖書館管理系統。佢哋從好多圖書館聚合書籍metadata，並通過WorldCat網站提供。不過，佢哋亦通過賣呢啲數據賺錢，所以唔可以批量下載。佢哋確實有一啲更有限嘅批量數據集可以下載，與特定圖書館合作。 1. 喺某啲合理嘅「永遠」定義下。;) 2. 當然，人類嘅書面遺產遠不止於書籍，尤其係而家。為咗呢篇文章同我哋最近嘅發佈，我哋專注於書籍，但我哋嘅興趣延伸得更遠。 3. 關於 Aaron Swartz 可以講嘅仲有好多，但我哋只係想簡單提及佢，因為佢喺呢個故事中扮演咗關鍵角色。隨住時間推移，可能會有更多人第一次接觸到佢嘅名字，然後可以自己深入了解。 <strong>實體副本。</strong> 顯然呢個唔係好有幫助，因為佢哋只係同一材料嘅重複品。如果我哋可以保存人哋喺書中做嘅所有註釋，例如Fermat著名嘅「邊緣塗鴉」，咁就好正。但可惜，呢個將會係一個檔案管理員嘅夢想。 <strong>「版本」。</strong> 喺呢度你會計算每一個獨特版本嘅書。如果有任何唔同，例如唔同嘅封面或者唔同嘅序言，都算係一個唔同嘅版本。 <strong>「文件」。</strong> 當處理影子圖書館如Library Genesis、Sci-Hub或者Z-Library時，仲有一個考慮。有可能有多個同一版本嘅掃描。而人哋可以通過使用OCR掃描文本，或者修正角度掃描嘅頁面，製作更好嘅現有文件版本。我哋希望只將呢啲文件計算為一個版本，呢個需要好嘅metadata，或者使用文件相似度測量進行去重。 <strong>「作品」。</strong> 例如「哈利波特與密室」作為一個邏輯概念，包含咗所有版本，例如唔同嘅翻譯同重印。呢個係一個幾有用嘅定義，但係好難界定咩算係。舉個例，我哋可能想保存唔同嘅翻譯，但係只有細微差異嘅重印可能冇咁重要。 - Anna同團隊（<a %(reddit)s>Reddit</a>） 通過海盜圖書館鏡像（編輯：已移至<a %(wikipedia_annas_archive)s>Anna’s Archive</a>），我哋嘅目標係攞世界上所有嘅書，並永久保存佢哋。<sup>1</sup> 喺我哋嘅Z-Library種子檔案同原始Library Genesis種子檔案之間，我哋有11,783,153個文件。但實際上有幾多呢？如果我哋正確地去重複呢啲文件，我哋保存咗世界上幾多百分比嘅書？我哋真係想有咁樣嘅數據： 讓我哋從一啲粗略嘅數字開始： 喺Z-Library/Libgen同Open Library中，有好多書比獨特嘅ISBN多。呢個係咪意味住好多呢啲書冇ISBN，定係ISBN metadata簡單缺失？我哋可能可以通過基於其他屬性（標題、作者、出版商等）嘅自動匹配，拉入更多數據來源，並從實際書籍掃描中提取ISBN（喺Z-Library/Libgen嘅情況下）嚟回答呢個問題。 有幾多呢啲ISBN係獨特嘅？呢個最好用文氏圖嚟說明： 更準確啲講： 我哋對於重疊咁少感到驚訝！ISBNdb 有大量嘅 ISBN 喺 Z-Library 或 Open Library 都冇出現，而其他兩個情況亦然（雖然程度較細但仍然顯著）。呢個引發咗好多新問題。自動匹配喺標記冇 ISBN 嘅書籍上有幾大幫助？會唔會有好多匹配從而增加重疊？另外，如果我哋引入第四或第五個數據集，會見到幾多重疊呢？ 呢個畀咗我哋一個起點。我哋而家可以睇下所有唔喺 Z-Library 數據集入面嘅 ISBN，亦唔符合書名/作者欄位嘅 ISBN。呢個可以畀我哋一個方法去保存世界上所有嘅書：首先喺互聯網上刮取掃描，然後喺現實生活中掃描書籍。後者甚至可以眾籌，或者由想見到特定書籍數碼化嘅人提供「懸賞」推動。呢啲都係另一個時間嘅故事。 如果你想幫手做呢啲工作——進一步分析；刮取更多 metadata；搵更多書；OCR 書籍；喺其他領域做呢啲（例如論文、有聲書、電影、電視節目、雜誌）甚至將呢啲數據用喺例如 ML / 大型語言模型訓練——請聯絡我（<a %(reddit)s>Reddit</a>）。 如果你特別對數據分析有興趣，我哋正努力將我哋嘅數據集同腳本以更易用嘅格式提供。你可以 fork 一個 notebook 然後開始玩呢啲數據，咁就最好不過啦。 最後，如果你想支持呢項工作，請考慮捐款。呢個係完全由志願者運行嘅項目，你嘅貢獻會帶來巨大嘅改變。每一點幫助都好重要。暫時我哋接受加密貨幣捐款；請參閱 Anna’s Archive 嘅捐款頁面。 要計算百分比，我哋需要一個分母：有史以來出版嘅書嘅總數。<sup>2</sup> 喺Google Books消亡之前，項目嘅一位工程師Leonid Taycher<a %(booksearch_blogspot)s>嘗試估算</a>呢個數字。佢得出咗一個數字——129,864,880（“至少直到星期日”）。佢通過建立一個世界上所有書籍嘅統一數據庫嚟估算呢個數字。為此，佢將不同嘅數據集整合起來，然後以各種方式合併。 順帶一提，仲有一個人曾經試過將全世界嘅書籍編目：Aaron Swartz，呢位已故嘅數碼活動家同Reddit聯合創辦人。<sup>3</sup> 佢<a %(youtube)s>創立咗Open Library</a>，目標係「為每本出版過嘅書創建一個網頁」，結合咗好多唔同來源嘅數據。佢因為大量下載學術論文而被起訴，最終為佢嘅數碼保存工作付出咗生命嘅代價，導致佢自殺。唔使講，呢個係我哋小組使用假名嘅原因之一，亦係我哋非常小心嘅原因。Open Library依然由互聯網檔案館嘅人英雄般地運行，繼續Aaron嘅遺產。我哋會喺呢篇文章後面再講返呢個話題。 喺Google嘅博客文章中，Taycher描述咗估算呢個數字嘅一啲挑戰。首先，咩係一本書？有幾個可能嘅定義： 「版本」似乎係對「書籍」最實際嘅定義。方便嘅係，呢個定義亦用於分配獨特嘅ISBN號碼。ISBN，即國際標準書號，通常用於國際商業，因為佢與國際條碼系統（「國際商品編號」）集成。如果你想喺商店賣書，佢需要一個條碼，所以你會獲得一個ISBN。 Taycher嘅博客文章提到，雖然ISBN有用，但佢哋唔係普遍適用，因為佢哋只係喺七十年代中期真正被採用，並唔係全球各地都採用。不過，ISBN可能係最廣泛使用嘅書籍版本識別符，所以係我哋最好嘅起點。如果我哋可以找到全世界嘅ISBN，我哋就可以獲得一個有用嘅書籍清單，知道邊啲書仲需要被保存。 咁，我哋喺邊度獲得數據？有幾個現有嘅努力試圖編制全世界書籍嘅清單： 喺呢篇文章中，我哋好高興宣佈一個小型發佈（相比我哋之前嘅Z-Library發佈）。我哋抓取咗大部分ISBNdb，並將數據喺Pirate Library Mirror網站上提供torrent下載（編輯：已移至<a %(wikipedia_annas_archive)s>Anna嘅檔案</a>；我哋唔會直接喺呢度鏈接，直接搜索佢）。呢啲大約有3090萬條記錄（20GB作為<a %(jsonlines)s>JSON Lines</a>；4.4GB壓縮）。喺佢哋網站上佢哋聲稱佢哋實際上有3260萬條記錄，所以我哋可能某種程度上錯過咗一啲，或者<em>佢哋</em>可能做錯咗啲嘢。無論如何，暫時我哋唔會分享我哋係點做嘅 — 我哋會留畀讀者作為一個練習。;-) 我哋會分享嘅係一啲初步分析，試圖更接近估算世界上書籍嘅數量。我哋睇咗三個數據集：呢個新嘅ISBNdb數據集，我哋從Z-Library影子圖書館（包括Library Genesis）抓取嘅metadata原始發佈，仲有Open Library數據轉儲。 ISBNdb數據庫，或者有幾多書被永久保存？ 如果我哋要正確地去重複影子圖書館嘅文件，我哋保存咗世界上幾多百分比嘅書？ 關於<a %(wikipedia_annas_archive)s>Anna’s Archive</a>，人類歷史上最大嘅真正開放圖書館嘅更新。 <em>WorldCat redesign</em> 數據 <strong>格式？</strong> <a %(blog)s>Anna’s Archive Containers (AAC)</a>，基本上係用<a %(jsonlines)s>JSON Lines</a>壓縮成<a %(zstd)s>Zstandard</a>，加上一啲標準化語義。呢啲容器包裹住唔同類型嘅記錄，基於我哋部署嘅唔同爬取。 一年前，我哋<a %(blog)s>開始</a>回答呢個問題：<strong>有幾多百分比嘅書籍已經被影子圖書館永久保存？</strong> 我哋睇吓數據嘅基本信息： 一旦一本書進入咗開放數據影子圖書館，例如<a %(wikipedia_library_genesis)s>Library Genesis</a>，而家仲有<a %(wikipedia_annas_archive)s>Anna’s Archive</a>，佢就會喺全世界被鏡像（通過種子），從而實際上永久保存。 要回答有幾多百分比嘅書籍已經被保存，我哋需要知道分母：總共有幾多本書存在？理想情況下，我哋唔只係有個數字，而係有實際嘅metadata。咁我哋唔單止可以同影子圖書館匹配，仲可以<strong>創建需要保存嘅剩餘書籍嘅待辦事項清單！</strong>我哋甚至可以開始夢想一個眾包努力去完成呢個待辦事項清單。 我哋爬取咗<a %(wikipedia_isbndb_com)s>ISBNdb</a>，同埋下載咗<a %(openlibrary)s>Open Library dataset</a>，但係結果唔係好理想。主要問題係ISBN重疊唔多。睇吓呢個<a %(blog)s>我哋嘅博客文章</a>入面嘅文氏圖： 我哋對ISBNdb同Open Library之間重疊咁少感到好驚訝，因為佢哋都係從唔同來源，包括網絡爬取同圖書館記錄，廣泛收集數據。如果佢哋都能夠做好搵到大部分ISBN嘅工作，佢哋嘅圈應該會有大量重疊，或者其中一個會係另一個嘅子集。呢個令我哋諗，究竟有幾多書係完全喺呢啲圈之外呢？我哋需要一個更大嘅數據庫。 嗰陣時我哋將目光投向世界上最大嘅書籍數據庫：<a %(wikipedia_worldcat)s>WorldCat</a>。呢個係由非牟利機構<a %(wikipedia_oclc)s>OCLC</a>擁有嘅專有數據庫，佢哋從世界各地嘅圖書館收集metadata記錄，作為回報，佢哋會畀嗰啲圖書館訪問完整數據集，並且喺最終用戶嘅搜索結果中顯示。 即使OCLC係非牟利機構，佢哋嘅商業模式需要保護佢哋嘅數據庫。好啦，我哋要話畀OCLC嘅朋友知，我哋會將所有嘢公開。:-) 過去一年，我哋仔細爬取咗所有WorldCat記錄。起初，我哋撞到一個好運。WorldCat啱啱推出佢哋完整網站嘅重新設計（喺2022年8月）。呢個包括佢哋後端系統嘅重大改革，引入咗好多安全漏洞。我哋即刻把握機會，喺短短幾日內爬取咗數以億計嘅記錄！ 之後，安全漏洞逐一被修復，直到大約一個月前我哋發現嘅最後一個漏洞被修補。到嗰陣時，我哋已經基本上有晒所有記錄，只係追求稍微高質量嘅記錄。所以我哋覺得係時候發佈啦！ 1.3B WorldCat抓取 <em><strong>簡單來講：</strong> Anna’s Archive抓取咗所有WorldCat（世界上最大嘅圖書館metadata集合）以製作需要保存嘅書籍嘅待辦事項清單。</em> WorldCat 警告：呢篇博客文章已經被棄用。我哋決定IPFS仲未準備好。我哋仍然會喺可能嘅情況下從Anna’s Archive鏈接到IPFS上嘅文件，但我哋唔會再自己托管，亦唔建議其他人使用IPFS進行鏡像。請查看我哋嘅Torrents頁面，如果你想幫助保存我哋嘅收藏。 幫助喺IPFS上播種Z-Library 合作伺服器下載 SciDB 外部借閱 外部借閱（打印禁用） 外部下載 探索元數據 包含喺種子文件中 返回  （+%(num)s額外） 未付款 已付款 已取消 過期 等緊Anna確認 無效 以下文字繼續用英文。 去 重設 前進 最後 如果你嘅電郵地址喺Libgen論壇唔得，我哋建議使用<a %(a_mail)s>Proton Mail</a>（免費）。你亦可以<a %(a_manual)s>手動請求</a>激活你嘅帳戶。 （可能需要<a %(a_browser)s>瀏覽器驗證</a> — 無限下載！） 快速合作伺服器 #%(number)s （推薦） （稍快但有等候名單） （無需瀏覽器驗證） （無需瀏覽器驗證或等候名單） （無等候名單，但可能非常慢） 慢速合作伺服器 #%(number)s 有聲書 漫畫書 書籍（小說） 書籍（非小說） 書籍（未知） 期刊文章 雜誌 樂譜 其他 標準文件 唔係所有頁面都可以轉換成 PDF 喺 Libgen.li 標記為損壞 喺 Libgen.li 唔可見 喺 Libgen.rs 小說類唔可見 喺 Libgen.rs 非小說類唔可見 運行 exiftool 喺呢個文件失敗 喺 Z-Library 標記為「壞文件」 喺 Z-Library 缺失 喺 Z-Library 標記為「垃圾郵件」 文件無法打開（例如：文件損壞、DRM） 版權聲明 下載問題（例如：無法連接、錯誤信息、非常慢） 錯誤嘅元數據（例如：標題、描述、封面圖片） 其他 質量差（例如：格式問題、掃描質量差、缺頁） 垃圾信息 / 文件應該被移除（例如：廣告、濫用內容） %(amount)s（%(amount_usd)s） %(amount)s總計 %(amount)s（%(amount_usd)s）總計 聰明書蟲 幸運圖書館員 閃亮數據收藏家 驚人檔案管理員 額外下載 Cerlalc 捷克元數據 读秀 DuXiu EBSCOhost 電子書索引 Google 圖書 Goodreads HathiTrust IA IA受控數字借閱 ISBNdb ISBN GRP Libgen.li 排除“scimag” Libgen.rs 非小說同小說 Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary 俄羅斯國家圖書館 Sci-Hub 通過Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor 上傳到 AA Z-Library Z-Library 中文 標題、作者、DOI、ISBN、MD5、… 搜尋 作者 描述同元數據評論 版本 原始檔案名 出版社 （搜索特定字段） 書名 出版年份 技術詳情 呢個硬幣嘅最低要求高過平時。請選擇唔同嘅時長或者唔同嘅硬幣。 請求無法完成。請幾分鐘後再試一次，如果問題持續發生，請聯絡我哋 %(email)s 並附上截圖。 發生未知錯誤。請聯絡我哋 %(email)s 並附上截圖。 支付處理出錯。請稍等片刻再試一次。如果問題持續超過24小時，請聯絡我哋 %(email)s 並附上截圖。 我哋正為<a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">備份</a>世界上最大嘅漫畫影子圖書館籌款。多謝你嘅支持！<a href="/donate">捐款。</a>如果你唔能夠捐款，請考慮通過告訴你嘅朋友，或者喺<a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>或者<a href="https://t.me/annasarchiveorg">Telegram</a>上關注我哋嚟支持我哋。 唔好發郵件俾我哋<a %(a_request)s>請求書籍</a><br>或者小型（<10k）<a %(a_upload)s>上傳</a>。 Anna’s Archive DMCA / 版權投訴 保持聯繫 Reddit 替代方案 SLUM (%(unaffiliated)s) 無關聯 Anna’s Archive 需要你嘅幫助！ 如果你而家捐款，你會獲得<strong>雙倍</strong>嘅快速下載次數。 好多嘗試打低我哋，但我哋會反擊。 如果你今個月捐款，你會獲得<strong>雙倍</strong>嘅快速下載次數。 有效期至本月底。 保存人類知識：一份偉大嘅節日禮物！ 會員資格會相應延長。 合作伺服器因為託管關閉而無法使用。佢哋應該好快會再上線。 為咗增加Anna’s Archive嘅韌性，我哋正尋找志願者運行鏡像。 我哋有一個新嘅捐款方法：%(method_name)s。請考慮%(donate_link_open_tag)s捐款</a>——運行呢個網站唔平，你嘅捐款真係好有幫助。多謝晒。 推薦朋友，您同朋友都可以獲得%(percentage)s%%額外快速下載次數獎勵！ 驚喜你嘅摰愛，送佢哋一個會員賬戶。 完美嘅情人節禮物！ 了解更多… 帳戶 活動 進階 安娜博客 ↗ Anna’s Software ↗ 測試版 代碼探索 數據集 捐款 已下載文件 常見問題 主頁 改善元數據 LLM數據 登入 / 註冊 我嘅捐款 公開檔案 搜尋 安全 種子 轉去 ↗ 志願服務及賞金 最近下載： 📚&nbsp;世界上最大嘅開源開數據圖書館。⭐️&nbsp;鏡像 Sci-Hub、Library Genesis、Z-Library 等等。📈&nbsp;%(book_any)s 本書， %(journal_article)s 篇論文， %(book_comic)s 本漫畫， %(magazine)s 本雜誌 — 永遠保存。  同  同埋更多 DuXiu Internet Archive 借閱圖書館 LibGen 📚&nbsp;人類歷史上最大嘅真正開放圖書館。 📈&nbsp;%(book_count)s&nbsp;本書， %(paper_count)s&nbsp;篇論文 — 永遠保存。 ⭐️&nbsp;我哋鏡像 %(libraries)s。 我哋會抓取同開源 %(scraped)s。 我哋嘅所有代碼同數據都係完全開源嘅。 OpenLib Sci-Hub ，  📚 世界上最大嘅開源開數據圖書館。<br>⭐️ 鏡像 Scihub、Libgen、Zlib 等等。 Z-Lib Anna’s Archive 無效請求。請訪問 %(websites)s。 世界上最大嘅開源開數據圖書館。鏡像 Sci-Hub、Library Genesis、Z-Library 等等。 搜尋 Anna’s Archive Anna’s Archive 請刷新重試。<a %(a_contact)s>聯絡我們</a>如果問題持續多個小時。 🔥 加載此頁面時出現問題 <li>1. 喺<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>或者<a href="https://t.me/annasarchiveorg">Telegram</a>上關注我哋。</li><li>2. 喺Twitter、Reddit、Tiktok、Instagram、本地咖啡店或者圖書館，或者你去嘅任何地方宣傳Anna’s Archive！我哋唔相信設限——如果我哋被下架，我哋會喺其他地方重新出現，因為我哋所有代碼同數據都係完全開源嘅。</li><li>3. 如果你有能力，請考慮<a href="/donate">捐款</a>。</li><li>4. 幫手<a href="https://translate.annas-software.org/">翻譯</a>我哋嘅網站成為唔同語言。</li><li>5. 如果你係軟件工程師，請考慮貢獻我哋嘅<a href="https://annas-software.org/">開源項目</a>，或者做我哋嘅<a href="/datasets">種子檔案</a>。</li> 10. 創建或者幫手維護你語言嘅Anna’s Archive嘅維基百科頁面。 11. 我哋尋找放置小而雅緻廣告嘅機會。如果你想喺Anna’s Archive上投放廣告，請讓我哋知道。 6. 如果你係安全研究員，我哋可以利用你嘅技能進行攻防。查看我哋嘅<a %(a_security)s>安全</a>頁面。 7. 我哋尋找支付專家幫助匿名商家。你可以幫我哋增加更多方便嘅捐款方式嗎？PayPal、微信、禮品卡。如果你識人，請聯繫我哋。 8. 我哋一直尋找更多嘅伺服器容量。 9. 你可以通過報告文件問題、留言同喺呢個網站上創建列表嚟幫手。你亦可以通過<a %(a_upload)s>上傳更多書籍</a>，或者修正現有書籍嘅文件問題或者格式嚟幫手。 想了解更多關於點樣做義工嘅詳盡資訊，請睇我哋嘅<a %(a_volunteering)s>義工同懸賞</a>頁面。 我哋堅信信息自由流通，同保存知識同文化。通過呢個搜索引擎，我哋係站喺巨人嘅肩膀上。我哋深深尊重創建各種影子圖書館嘅人嘅辛勤工作，希望呢個搜索引擎可以擴大佢哋嘅影響力。 要了解我哋嘅最新進展，請喺<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>或者<a href="https://t.me/annasarchiveorg">Telegram</a>上關注Anna。如有問題同反饋，請聯繫Anna：%(email)s。 帳戶ID: %(account_id)s 登出 ❌ 出咗啲問題。請重新載入頁面再試一次。 ✅ 您已經登出。重新載入頁面以再次登錄。 過去24小時快速下載次數：<strong>%(used)s / %(total)s</strong> 會員資格：<strong>%(tier_name)s</strong> 直到 %(until_date)s <a %(a_extend)s>(延長)</a> 您可以合併多個會員資格（每24小時嘅快速下載次數會加埋一齊計）。 會員資格：<strong>無</strong> <a %(a_become)s>(成為會員)</a> 如果您有興趣升級到更高級別嘅會員，請聯絡Anna：%(email)s。 公開檔案: %(profile_link)s 秘密鑰匙（唔好分享！）：%(secret_key)s 顯示 即刻加入我哋！ 升級到<a %(a_tier)s>更高級別</a>以加入我哋嘅群組。 獨家Telegram群組：%(link)s 帳戶 邊啲下載？ 登錄 唔好唔見咗你嘅鎖匙！ 無效嘅秘密鑰匙。請核實您嘅鑰匙再試一次，或者喺下面重新註冊一個新帳戶。 秘密鑰匙 輸入您嘅秘密鑰匙以登錄： 舊嘅電郵帳戶？喺<a %(a_open)s>呢度輸入你嘅電郵</a>。 註冊新帳戶 仲未有帳戶？ 註冊成功！您嘅秘密鑰匙係：<span %(span_key)s>%(key)s</span> 請小心保存呢條鑰匙。如果您遺失咗，您將會失去訪問您帳戶嘅權限。 <li %(li_item)s><strong>書籤。</strong> 您可以將此頁面加為書籤以檢索您嘅鑰匙。</li><li %(li_item)s><strong>下載。</strong> 點擊<a %(a_download)s>呢條鏈接</a>下載您嘅鑰匙。</li><li %(li_item)s><strong>密碼管理器。</strong> 當您輸入鑰匙時，使用密碼管理器保存鑰匙。</li> 登入 / 註冊 瀏覽器驗證 警告：代碼中有錯誤嘅Unicode字符，可能會喺唔同情況下出現錯誤行為。可以從URL中嘅base64表示解碼原始二進制。 描述 標籤 前綴 特定代碼嘅URL 網站 以“%(prefix_label)s”開頭嘅代碼 請唔好爬取呢啲頁面。建議你<a %(a_import)s>生成</a>或者<a %(a_download)s>下載</a>我哋嘅ElasticSearch同MariaDB數據庫，並運行我哋嘅<a %(a_software)s>開源代碼</a>。原始數據可以通過JSON文件手動探索，例如<a %(a_json_file)s>呢個</a>。 少於%(count)s條記錄 通用URL 代碼探索者 索引 按前綴探索記錄標籤嘅代碼。「記錄」欄顯示咗用指定前綴標籤嘅記錄數量，喺搜索引擎中可見（包括只含元數據嘅記錄）。「代碼」欄顯示咗實際有指定前綴嘅代碼數量。 已知代碼前綴“%(key)s” 更多… 前綴 %(count)s 條記錄符合“%(prefix_label)s” page.codes.records_starting_with 代碼 記錄 “%%s”會被代碼嘅值取代 搜尋Anna’s Archive 代碼 特定代碼嘅URL：“%(url)s” 呢頁可能需要一段時間生成，所以需要 Cloudflare 驗證碼。<a %(a_donate)s>會員</a>可以跳過驗證碼。 濫用已舉報： 更好版本 您想舉報此用戶的濫用或不當行為嗎？ 文件問題：%(file_issue)s 隱藏評論 回覆 舉報濫用 您已舉報此用戶的濫用行為。 發送到此郵箱嘅版權申訴將被忽略；請使用表格。 顯示郵箱 我哋非常歡迎您嘅反饋同問題！ 然而，由於我哋收到大量垃圾郵件同無意義郵件，請勾選方框以確認您明白聯絡我哋嘅條件。 任何其他方式聯絡我哋關於版權申訴嘅信息將會自動刪除。 對於 DMCA / 版權申訴，請使用 <a %(a_copyright)s>此表格</a>。 聯絡電郵 Anna’s Archive 上嘅URL（必填）。每行一個。請只包括描述同一版本書籍嘅URL。如果你想對多本書或多個版本提出申訴，請多次提交呢個表格。 將多本書或多個版本捆綁一齊嘅申訴將會被拒絕。 地址（必填） 清晰描述來源材料（必填） 電郵（必填） 來源材料嘅URL，每行一個（必填）。請盡可能多包括，以幫助我哋驗證你嘅申訴（例如Amazon，WorldCat，Google Books，DOI）。 來源材料嘅ISBN（如果適用）。每行一個。請只包括完全匹配你報告版權申訴嘅版本。 你嘅姓名（必填） ❌ 出咗啲問題。請重新加載頁面再試一次。 ✅ 多謝你提交版權申訴。我哋會盡快審核。請重新加載頁面以提交另一個申訴。 <a %(a_openlib)s>Open Library</a> 來源材料嘅URL，每行一個。請花一啲時間喺Open Library搜索你嘅來源材料。呢個將幫助我哋驗證你嘅申訴。 電話號碼（必填） 聲明及簽名（必填） 提交申訴 如果你有DCMA或其他版權申訴，請盡量準確填寫呢個表格。如果遇到任何問題，請聯絡我哋嘅專用DMCA電郵地址：%(email)s。請注意，發送到呢個地址嘅申訴將唔會被處理，呢個地址只係用嚟解答問題。請使用以下表格提交你嘅申訴。 DMCA / 版權申訴表格 Anna’s Archive嘅示例記錄 Anna’s Archive嘅種子 Anna’s Archive Containers格式 導入元數據嘅腳本 如果你有興趣鏡像呢個數據集用作<a %(a_archival)s>存檔</a>或者<a %(a_llm)s>LLM訓練</a>嘅用途，請聯絡我哋。 最後更新： %(date)s 主%(source)s網站 元數據文檔（大部分字段） Anna’s Archive鏡像文件： %(count)s（%(percent)s%%） 資源 總文件數： %(count)s 總文件大小： %(size)s 我哋關於呢個數據嘅博客文章 <a %(duxiu_link)s>讀秀</a>係一個龐大嘅掃描書籍數據庫，由<a %(superstar_link)s>超星數字圖書館集團</a>創建。大部分係學術書籍，掃描後數字化提供俾大學同圖書館。我哋嘅英語讀者可以參考<a %(princeton_link)s>普林斯頓大學</a>同<a %(uw_link)s>華盛頓大學</a>嘅概述。仲有一篇出色嘅文章提供更多背景資料：<a %(article_link)s>“數字化中國書籍：超星讀秀學者搜索引擎嘅案例研究”</a>。 讀秀嘅書籍長期以來喺中國互聯網上被盜版。通常佢哋會被轉售商以少於一美元嘅價格出售。佢哋通常會用中國版嘅Google Drive分發，呢啲平台經常被黑客攻擊以增加存儲空間。一啲技術細節可以喺<a %(link1)s>呢度</a>同<a %(link2)s>呢度</a>搵到。 雖然呢啲書籍已經半公開分發，但要大批量獲取佢哋係相當困難嘅。我哋將呢個任務列喺我哋嘅待辦事項清單上，並分配咗幾個月嘅全職工作時間。不過，喺2023年尾，一位令人難以置信、驚人同才華橫溢嘅志願者聯絡咗我哋，話佢哋已經完成咗所有工作——費用巨大。佢哋無償分享咗全部收藏俾我哋，只要求長期保存。真係非常值得敬佩。 更多來自我哋志願者嘅信息（原始筆記）： 改編自我哋嘅<a %(a_href)s>博客文章</a>。 DuXiu 讀秀 %(count)s 檔案 page.datasets.files 呢個數據集同<a %(a_datasets_openlib)s>Open Library數據集</a>密切相關。佢包含咗IA嘅受控數字借閱圖書館嘅所有元數據同大部分文件嘅抓取。更新會喺<a %(a_aac)s>Anna’s Archive Containers格式</a>中發布。 呢啲記錄直接引用自Open Library數據集，但亦包含唔喺Open Library中嘅記錄。我哋仲有一啲由社區成員多年來抓取嘅數據文件。 呢個收藏由兩部分組成。你需要兩部分先可以獲得所有數據（除咗喺種子頁面上劃掉嘅已被取代嘅種子）。 數碼借閱圖書館 我哋嘅第一次發佈，喺我哋標準化<a %(a_aac)s>Anna’s Archive Containers (AAC)格式</a>之前。包含元數據（json同xml格式）、pdf（來自acsm同lcpdf數碼借閱系統）同封面縮圖。 增量新發佈，使用AAC。只包含2023-01-01之後嘅帶有時間戳嘅元數據，因為其餘嘅已經由“ia”覆蓋咗。仲有所有pdf文件，今次來自acsm同“bookreader”（IA嘅網上閱讀器）借閱系統。雖然個名唔係完全啱，但我哋仍然將bookreader文件放入ia2_acsmpdf_files集合，因為佢哋係互斥嘅。 IA 受控數字借閱 98%%+嘅檔案係可搜尋嘅。 我哋嘅使命係存檔全世界嘅書籍（以及論文、雜誌等），並令佢哋廣泛可及。我哋相信所有書籍都應該被廣泛鏡像，以確保冗餘同韌性。呢個係我哋將來自多個來源嘅檔案聚集埋一齊嘅原因。有啲來源係完全開放嘅，可以批量鏡像（例如Sci-Hub）。有啲係封閉同保護嘅，所以我哋嘗試刮取佢哋以“解放”佢哋嘅書籍。仲有啲係介乎兩者之間。 我哋所有嘅數據都可以<a %(a_torrents)s>torrent</a>，而我哋所有嘅metadata都可以<a %(a_anna_software)s>生成</a>或<a %(a_elasticsearch)s>下載</a>作ElasticSearch同MariaDB數據庫。原始數據可以通過JSON檔案例如<a %(a_dbrecord)s>呢個</a>手動探索。 元數據 ISBN網站 最後更新： %(isbn_country_date)s（%(link)s） 資源 國際ISBN機構定期發布分配俾國家ISBN機構嘅範圍。由此我哋可以推斷呢個ISBN屬於邊個國家、地區或者語言組。我哋目前間接使用呢啲數據，通過<a %(a_isbnlib)s>isbnlib</a> Python庫。 ISBN 國家信息 呢個係2022年9月期間大量調用isbndb.com嘅數據轉儲。我哋嘗試覆蓋所有ISBN範圍。呢啲數據大約有3090萬條記錄。佢哋喺網站上聲稱實際上有3260萬條記錄，所以我哋可能漏咗啲，或者<em>佢哋</em>可能做錯咗啲嘢。 JSON回應基本上係佢哋伺服器嘅原始數據。我哋注意到嘅一個數據質量問題係，對於以“978-”以外嘅前綴開頭嘅ISBN-13號碼，佢哋仍然包括一個“isbn”字段，呢個字段只係將ISBN-13號碼嘅前三個數字刪除（並重新計算校驗位）。呢個顯然係錯嘅，但佢哋似乎就係咁做嘅，所以我哋冇改動。 你可能會遇到嘅另一個潛在問題係，“isbn13”字段有重複，所以你唔可以喺數據庫中用佢作為主鍵。“isbn13”+“isbn”字段結合起嚟似乎係唯一嘅。 版本 1 (2022-10-31) 小說嘅種子檔案係落後嘅（雖然ID大約4-6M未有種子檔案，因為佢哋同我哋嘅Zlib種子檔案重疊）。 我哋嘅博客文章關於漫畫書發佈 Anna’s Archive嘅漫畫torrent 關於唔同Library Genesis分叉嘅背景故事，請參閱<a %(a_libgen_rs)s>Libgen.rs</a>嘅頁面。 Libgen.li包含大部分同Libgen.rs相同嘅內容同元數據，但喺此基礎上增加咗啲集合，即漫畫、雜誌同標準文件。佢仲將<a %(a_scihub)s>Sci-Hub</a>整合到佢嘅元數據同搜索引擎中，呢個係我哋用嚟做我哋數據庫嘅。 呢個圖書館嘅元數據可以免費喺<a %(a_libgen_li)s>libgen.li</a>獲得。不過，呢個伺服器好慢，唔支持恢復中斷嘅連接。相同嘅文件亦可以喺<a %(a_ftp)s>一個FTP伺服器</a>上獲得，呢個效果會好啲。 非小說類書籍似乎都已經分流，但冇新嘅torrent。呢個情況似乎係自2022年初開始發生，不過我哋未有核實。 根據Libgen.li管理員所講，“fiction_rus”（俄羅斯小說）收藏應該由<a %(a_booktracker)s>booktracker.org</a>定期發佈嘅種子覆蓋，尤其係<a %(a_flibusta)s>flibusta</a>同<a %(a_librusec)s>lib.rus.ec</a>嘅種子（我哋喺<a %(a_torrents)s>呢度</a>鏡像，但我哋仲未確定邊啲種子對應邊啲文件）。 小說收藏有自己嘅種子（同<a %(a_href)s>Libgen.rs</a>唔同），由%(start)s開始。 某啲冇種子嘅範圍（例如小說範圍f_3463000到f_4260000）可能係Z-Library（或者其他重複）文件，但我哋可能想做啲去重複處理，為呢啲範圍內lgli獨有嘅文件製作種子。 所有收藏嘅統計數據可以喺<a %(a_href)s>libgen嘅網站</a>搵到。 大部分額外內容都有提供種子，尤其係漫畫、雜誌同標準文件嘅種子，係同Anna’s Archive合作發佈嘅。 請注意，指向“libgen.is”嘅torrent文件係<a %(a_libgen)s>Libgen.rs</a>嘅鏡像（“.is”係Libgen.rs用嘅另一個域名）。 使用元數據嘅一個有用資源係<a %(a_href)s>呢個頁面</a>。 %(icon)s 佢哋嘅“fiction_rus”收藏（俄羅斯小說）冇專屬種子，但係由其他人嘅種子覆蓋，我哋有個<a %(fiction_rus)s>鏡像</a>。 Anna’s Archive上嘅俄羅斯小說種子 Anna’s Archive嘅小說torrent 討論區 元數據 通過FTP獲取元數據 Anna’s Archive嘅雜誌torrent 元數據字段信息 其他種子嘅鏡像（同獨特嘅小說同漫畫種子） Anna’s Archive上嘅標準文件種子 Libgen.li Anna’s Archive嘅種子（書籍封面） Library Genesis 已經以慷慨嘅方式通過 torrents 大量提供佢哋嘅數據。我哋嘅 Libgen 收藏包括佢哋唔直接發佈嘅輔助數據，並與佢哋合作。非常感謝所有參與 Library Genesis 嘅人同我哋合作！ 我哋嘅博客關於書籍封面發佈 呢頁係關於“.rs”版本。佢以穩定發佈佢嘅元數據同完整嘅書籍目錄內容而聞名。佢嘅書籍收藏分為小說同非小說部分。 使用元數據嘅一個有用資源係<a %(a_metadata)s>呢頁</a>（封鎖IP範圍，可能需要VPN）。 截至2024-03，新嘅種子喺<a %(a_href)s>呢個論壇線程</a>發佈（封鎖IP範圍，可能需要VPN）。 Anna’s Archive嘅小說種子 Libgen.rs小說種子 Libgen.rs討論區 Libgen.rs元數據 Libgen.rs元數據字段信息 Libgen.rs非小說種子 Anna’s Archive嘅非小說種子 %(example)s 對於一本小說書。 呢個 <a %(blog_post)s>首次發佈</a>比較細：大約 300GB 嘅 Libgen.rs 分支嘅書籍封面，包括小說同非小說。佢哋嘅組織方式同佢哋喺 libgen.rs 上出現嘅方式一樣，例如： %(example)s 對於一本非小說書。 就好似 Z-Library 收藏一樣，我哋將佢哋全部放喺一個大嘅 .tar 文件入面，如果你想直接提供文件，可以使用 <a %(a_ratarmount)s>ratarmount</a> 來掛載。 發佈 1 (%(date)s) 唔同嘅Library Genesis（或者叫“Libgen”）分支嘅簡單故事係，隨住時間嘅推移，參與Library Genesis嘅唔同人之間出現咗分歧，然後佢哋各自分道揚鑣。 根據呢篇<a %(a_mhut)s>論壇文章</a>，Libgen.li 原本係喺「http://free-books.dontexist.com」上架。 “.fun”版本係由原創創辦人創建嘅。佢而家正喺重整，轉向一個新嘅、更分散嘅版本。 <a %(a_li)s>“.li”版本</a>有大量嘅漫畫收藏，仲有其他內容，呢啲內容（仲未）可以通過種子批量下載。佢有一個獨立嘅小說書籍種子收藏，並且佢嘅數據庫中包含<a %(a_scihub)s>Sci-Hub</a>嘅元數據。 “.rs”版本有非常相似嘅數據，並且最穩定地以大批量種子發佈佢哋嘅收藏。佢大致分為“小說”同“非小說”部分。 原本喺「http://gen.lib.rus.ec」。 <a %(a_zlib)s>Z-Library</a>喺某種意義上都係Library Genesis嘅一個分支，雖然佢哋為佢哋嘅項目用咗一個唔同嘅名字。 Libgen.rs 我哋仲會用只提供元數據嘅來源嚟豐富我哋嘅收藏，呢啲元數據可以同文件匹配，例如用 ISBN 號碼或者其他欄位。以下係呢啲來源嘅概覽。再講一次，有啲來源係完全開放嘅，而有啲我哋就要爬取佢哋。 請注意，喺元數據搜索中，我哋會顯示原始記錄。我哋唔會合併記錄。 只提供元數據嘅來源 Open Library 係 Internet Archive 嘅一個開源項目，目標係編目世界上每一本書。佢有世界上最大嘅書籍掃描操作之一，並且有好多書可以進行數字借閱。佢嘅書籍元數據目錄可以免費下載，並且已經包含喺 Anna’s Archive 入面（雖然目前唔喺搜索中，除非你明確搜索 Open Library ID）。 Open Library 唔包括重複嘅 最後更新 文件數量百分比 %% 由AA鏡像 / 可用嘅種子 大小 來源 以下係Anna’s Archive上檔案來源嘅快速概覽。 由於影子圖書館經常互相同步數據，所以圖書館之間有相當大嘅重疊。因此，數字加埋唔會等於總數。 「由Anna’s Archive鏡像同種子檔案」嘅百分比顯示咗我哋自己鏡像咗幾多檔案。我哋會通過種子檔案大批量地種子呢啲檔案，並通過合作網站提供直接下載。 概覽 總數 Anna’s Archive 上嘅 torrents 關於 Sci-Hub 嘅背景，請參考佢嘅 <a %(a_scihub)s>官方網站</a>、<a %(a_wikipedia)s>維基百科頁面</a>，同埋呢個 <a %(a_radiolab)s>播客訪談</a>。 請注意 Sci-Hub 自 2021 年以來已經 <a %(a_reddit)s>凍結</a>。佢之前都凍結過，但喺 2021 年增加咗幾百萬篇論文。不過，仍然有少量論文被添加到 Libgen 嘅 “scimag” 收藏中，但唔夠多去值得新嘅批量 torrents。 我哋使用由 <a %(a_libgen_li)s>Libgen.li</a> 喺佢嘅 “scimag” 收藏中提供嘅 Sci-Hub 元數據。我哋亦使用 <a %(a_dois)s>dois-2022-02-12.7z</a> 數據集。 請注意 “smarch” torrents 已經 <a %(a_smarch)s>棄用</a>，因此唔包含喺我哋嘅 torrents 列表中。 Libgen.li 上嘅 torrents Libgen.rs 上嘅 torrents 元數據同 torrents Reddit 上嘅更新 播客訪問 維基百科頁面 Sci-Hub Sci-Hub：自2021年起凍結；大部分可通過種子下載 Libgen.li：自嗰陣以嚟有啲細微嘅新增</div> 有啲來源圖書館會透過 torrents 大量分享佢哋嘅數據，而有啲就唔會咁容易分享佢哋嘅收藏品。喺後者嘅情況下，Anna’s Archive 會嘗試爬取佢哋嘅收藏品，並將佢哋提供出嚟（睇吓我哋嘅 <a %(a_torrents)s>Torrents</a> 頁面）。仲有啲介乎兩者之間嘅情況，例如來源圖書館願意分享，但冇資源去做。喺呢啲情況下，我哋都會嘗試幫手。 以下係我哋點樣同唔同來源圖書館互動嘅概覽。 來源圖書館 %(icon)s 各種文件數據庫散佈喺中國互聯網上；但通常係收費數據庫 %(icon)s 大部分文件只可以用高級百度雲賬戶訪問；下載速度慢。 %(icon)s Anna’s Archive 管理住一個 <a [X51XX]>DuXiu 文件</a> 收藏 %(icon)s 各種元數據數據庫散佈在中國互聯網上；但通常是付費數據庫 %(icon)s 冇容易獲取嘅元數據轉儲可供佢哋嘅全部收藏使用。 %(icon)s Anna’s Archive 管理住一個 <a %(duxiu)s>DuXiu 元數據</a> 收藏 文件 %(icon)s 文件僅限於有限的借閱，並有各種訪問限制 %(icon)s Anna’s Archive 管理 <a %(ia)s>IA 文件</a> 的收藏 %(icon)s 一些元數據可通過 <a %(openlib)s>Open Library 數據庫轉儲</a> 獲得，但這些不涵蓋整個 IA 收藏 %(icon)s 沒有易於訪問的元數據轉儲可用於他們的整個收藏 %(icon)s Anna’s Archive 管理 <a %(ia)s>IA 元數據</a> 的收藏 最後更新 %(icon)s Anna’s Archive同Libgen.li合作管理<a %(comics)s>漫畫書</a>、<a %(magazines)s>雜誌</a>、<a %(standarts)s>標準文件</a>同<a %(fiction)s>小說（同Libgen.rs分開）</a>嘅收藏。 %(icon)s 非小說種子與 Libgen.rs 共享（並在 <a %(libgenli)s>這裡</a> 鏡像）。 %(icon)s 每季度 <a %(dbdumps)s>HTTP 數據庫轉儲</a> %(icon)s 自動化的 <a %(nonfiction)s>非小說</a> 同 <a %(fiction)s>小說</a> 的種子 %(icon)s Anna’s Archive 管理一個 <a %(covers)s>書籍封面種子</a> 的收藏 %(icon)s 每日 <a %(dbdumps)s>HTTP 數據庫轉儲</a> 元數據 %(icon)s 每月 <a %(dbdumps)s>數據庫轉儲</a> %(icon)s 數據種子可在 <a %(scihub1)s>這裡</a>、<a %(scihub2)s>這裡</a> 和 <a %(libgenli)s>這裡</a> 獲得 %(icon)s 一些新文件正 <a %(libgenrs)s>被</a> <a %(libgenli)s>添加</a> 到 Libgen 的 “scimag”，但不足以生成新種子 %(icon)s Sci-Hub 自 2021 年起凍結新文件。 %(icon)s 元數據轉儲可在 <a %(scihub1)s>這裡</a> 和 <a %(scihub2)s>這裡</a> 獲得，亦可作為 <a %(libgenli)s>Libgen.li 數據庫</a> 的一部分（我們使用） 來源 %(icon)s 各種較小或者一次性嘅來源。我哋鼓勵人哋先上傳到其他影子圖書館，但有時人哋嘅收藏太大，其他人無法整理，但又唔夠大去值得自己一個類別。 %(icon)s 唔可以直接大批量獲取，防止抓取 %(icon)s Anna’s Archive 管理住一個 <a %(worldcat)s>OCLC (WorldCat) 元數據</a> 收藏 %(icon)s Anna’s Archive 和 Z-Library 共同管理 <a %(metadata)s>Z-Library 元數據</a> 和 <a %(files)s>Z-Library 文件</a> 的收藏 Datasets 我哋會將以上所有來源合併成一個統一數據庫，用嚟服務呢個網站。呢個統一數據庫唔會直接提供，但由於 Anna’s Archive 係完全開源嘅，可以相當容易咁 <a %(a_generated)s>生成</a> 或者 <a %(a_downloaded)s>下載</a> 成為 ElasticSearch 同 MariaDB 數據庫。嗰頁面嘅腳本會自動下載所有上述來源嘅必要元數據。 如果你想喺本地運行嗰啲腳本之前探索我哋嘅數據，你可以睇吓我哋嘅 JSON 文件，佢哋會進一步鏈接到其他 JSON 文件。<a %(a_json)s>呢個文件</a>係一個好嘅起點。 統一數據庫 Anna’s Archive嘅Torrents 瀏覽 搜索 各種較小或者一次性嘅來源。我哋鼓勵人哋先上傳到其他影子圖書館，但有時人哋嘅收藏太大，其他人無法整理，但又唔夠大去值得自己一個類別。 概覽來自<a %(a1)s>datasets 頁面</a>。 來自 <a %(a_href)s>aaaaarg.fail</a>。睇嚟幾齊全。由我哋嘅義工“cgiym”提供。 來自 <a %(a_href)s><q>ACM Digital Library 2020</q></a> 嘅 torrent。與現有嘅論文集合有較高重疊，但好少 MD5 匹配，所以我哋決定完全保留。 義工<q>j</q>爬取<q>iRead eBooks</q>（即發音<q>ai rit i-books</q>；airitibooks.com）。對應於<a %(a1)s><q>其他 metadata 爬取</q></a>中<q>airitibooks</q>的metadata。 來自<a %(a1)s><q>亞歷山大圖書館</q></a>的收藏。部分來自原始來源，部分來自the-eye.eu，部分來自其他鏡像。 來自私人書籍 torrent 網站 <a %(a_href)s>Bibliotik</a>（通常稱為“Bib”），書籍按名稱（A.torrent, B.torrent）打包成 torrents，並通過 the-eye.eu 分發。 由我哋嘅義工“bpb9v”提供。更多關於 <a %(a_href)s>CADAL</a> 嘅資料，請參閱我哋嘅 <a %(a_duxiu)s>DuXiu 數據集頁面</a>。 更多由我哋嘅義工“bpb9v”提供，主要係 DuXiu 文件，以及一個“WenQu”同“SuperStar_Journals”文件夾（SuperStar 係 DuXiu 背後嘅公司）。 由我哋嘅義工“cgiym”提供，來自各種來源嘅中文文本（以子目錄表示），包括來自 <a %(a_href)s>China Machine Press</a>（一個主要嘅中國出版社）。 由我哋嘅義工“cgiym”提供嘅非中文收藏（以子目錄表示）。 義工<q>cm</q>爬取關於中國建築的書籍：<q>我係通過利用出版社嘅網絡漏洞獲得嘅，但嗰個漏洞已經被封咗</q>。對應於<a %(a1)s><q>其他 metadata 爬取</q></a>中<q>chinese_architecture</q>的metadata。 來自學術出版社 <a %(a_href)s>De Gruyter</a> 嘅書籍，從幾個大型 torrents 收集。 刮取自 <a %(a_href)s>docer.pl</a>，一個專注於書籍同其他書面作品嘅波蘭文件分享網站。由義工“p”喺 2023 年底刮取。我哋冇從原網站獲得好嘅元數據（甚至冇文件擴展名），但我哋篩選咗類似書籍嘅文件，並經常能夠從文件本身提取元數據。 DuXiu epubs，直接來自 DuXiu，由義工“w”收集。只有最近嘅 DuXiu 書籍可以直接通過電子書獲得，所以大部分都應該係最近嘅。 來自義工“m”嘅剩餘 DuXiu 文件，唔係 DuXiu 專有嘅 PDG 格式（主要嘅 <a %(a_href)s>DuXiu 數據集</a>）。從多個原始來源收集，不幸地冇喺文件路徑中保留嗰啲來源。 <span></span> <span></span> <span></span> 義工<q>do no harm</q>爬取色情書籍。對應於<a %(a1)s><q>其他 metadata 爬取</q></a>中<q>hentai</q>的metadata。 <span></span> <span></span> 由義工“t”從一個日本漫畫出版社刮取嘅收藏。 <a %(a_href)s>龍泉司法檔案精選</a>，由義工“c”提供。 刮取自 <a %(a_href)s>magzdb.org</a>，Library Genesis 嘅盟友（佢喺 libgen.rs 主頁上有鏈接），但佢哋唔想直接提供佢哋嘅文件。由義工“p”喺 2023 年底獲得。 <span></span> 各種小型上傳，太細唔夠成為自己嘅子集合，但以目錄形式表示。 來自俄羅斯文件分享網站AvaxHome的電子書。 報紙同雜誌嘅存檔。對應於<a %(a1)s><q>其他 metadata 爬取</q></a>中<q>newsarch_magz</q>的metadata。 爬取<a %(a1)s>哲學文獻中心</a>。 由義工“o”收集嘅波蘭書籍，直接來自原始發佈（“scene”）網站。 由義工“cgiym”同“woz9ts”合併嘅 <a %(a_href)s>shuge.org</a> 收藏。 <span></span> <a %(a_href)s>“Trantor 帝國圖書館”</a>（以虛構圖書館命名），由義工“t”喺 2022 年刮取。 <span></span> <span></span> <span></span> 由義工“woz9ts”提供嘅子子集合（以目錄表示）：<a %(a_program_think)s>program-think</a>，<a %(a_haodoo)s>haodoo</a>，<a %(a_skqs)s>skqs</a>（由台灣嘅 <a %(a_sikuquanshu)s>Dizhi(迪志)</a> 提供），mebook（mebook.cc，我的小書屋，woz9ts：“呢個網站主要專注於分享高質量嘅電子書文件，其中一些係由網站擁有者自己排版嘅。擁有者喺 2019 年被 <a %(a_arrested)s>逮捕</a>，有人整理咗佢分享嘅文件。”）。 剩餘來自志願者“woz9ts”的DuXiu文件，唔係DuXiu專有PDG格式（仲要轉換成PDF）。 「上傳」收藏被分成較小嘅子收藏，喺 AACIDs 同 torrent 名稱中標示。所有子收藏首先同主收藏進行重複數據刪除，但元數據「upload_records」JSON 文件仲包含好多原始文件嘅引用。大部分子收藏中非書籍文件亦被移除，通常喺「upload_records」JSON 中冇標示。 子收藏包括： 備註 子收藏 好多子收藏本身都由子子收藏組成（例如來自唔同嘅原始來源），喺「filepath」字段中表示為目錄。 上傳到 Anna’s Archive 我哋關於呢啲數據嘅博客文章 <a %(a_worldcat)s>WorldCat</a> 係一個由非牟利機構 <a %(a_oclc)s>OCLC</a> 擁有嘅專有數據庫，佢會聚合來自世界各地圖書館嘅元數據記錄。呢個數據庫好可能係全球最大嘅圖書館元數據集合。 喺2023年10月，我哋<a %(a_scrape)s>發佈</a>咗一個全面嘅OCLC (WorldCat) 數據庫抓取，係<a %(a_aac)s>Anna’s Archive Containers格式</a>。 2023年10月，初次發佈： OCLC (WorldCat) Anna’s Archive嘅種子 Anna’s Archive嘅示例記錄（原始收藏） Anna’s Archive嘅示例記錄（“zlib3”收藏） Anna’s Archive嘅Torrents（元數據+內容） 關於Release 1嘅博客文章 關於Release 2嘅博客文章 喺2022年尾，Z-Library嘅涉嫌創辦人被捕，域名被美國當局查封。自此之後，網站慢慢重新上線。現時唔知係邊個運營。 截至2023年2月嘅更新。 Z-Library起源於<a %(a_href)s>Library Genesis</a>社區，最初係用佢哋嘅數據啟動。自此之後，佢哋已經專業化咗好多，界面亦都現代化咗好多。因此，佢哋能夠獲得更多嘅捐款，無論係金錢上用嚟改進網站，定係新書嘅捐贈。佢哋除咗Library Genesis之外，仲積累咗大量嘅收藏。 呢個收藏分為三部分。前兩部分嘅原始描述頁面保留喺下面。你需要所有三部分先可以獲得所有數據（除咗喺torrents頁面上劃掉嘅被取代嘅torrents）。 %(title)s：我哋嘅第一次發佈。呢個係當時叫做“Pirate Library Mirror”（“pilimi”）嘅第一次發佈。 %(title)s：第二次發佈，今次所有文件都包喺.tar文件入面。 %(title)s：增量新發佈，使用<a %(a_href)s>Anna’s Archive Containers (AAC)格式</a>，而家同Z-Library團隊合作發佈。 最初嘅鏡像係喺2021年同2022年期間辛苦獲得嘅。喺呢個時候，佢有啲過時：佢反映咗2021年6月嘅收藏狀態。我哋將來會更新呢個。依家我哋專注於發佈呢個第一次發佈。 由於 Library Genesis 已經用公共種子保存，並且已經包含喺 Z-Library 入面，我哋喺 2022 年 6 月用 MD5 雜湊值做咗基本嘅重複數據刪除。圖書館入面可能仲有好多重複內容，例如同一本書嘅多種文件格式。呢啲好難準確檢測，所以我哋冇做。重複數據刪除之後，我哋仲有超過 200 萬個文件，總共接近 7TB。 呢個收藏由兩部分組成：一個 MySQL “.sql.gz” 元數據轉儲，仲有 72 個大約 50-100GB 嘅種子文件。元數據包含 Z-Library 網站報告嘅數據（標題、作者、描述、文件類型），以及我哋觀察到嘅實際文件大小同 md5sum，因為有時呢啲數據唔一致。似乎有啲文件範圍 Z-Library 自己嘅元數據都唔正確。我哋可能喺某啲孤立嘅情況下載錯咗文件，我哋會喺未來嘗試檢測同修正。 大嘅種子文件包含實際嘅書籍數據，以 Z-Library ID 作為文件名。文件擴展名可以用元數據轉儲重建。 呢個收藏係非小說同小說內容嘅混合（唔似 Library Genesis 咁分開）。質量亦都好參差。 呢個首次發佈而家已經完全可用。請注意，種子文件只可以通過我哋嘅 Tor 鏡像獲得。 Release 1（%(date)s） 呢個係單一嘅額外種子文件。佢唔包含任何新信息，但係有啲數據需要計算一段時間。呢個好方便，因為下載呢個種子通常比從頭計算快。特別係，佢包含咗 tar 文件嘅 SQLite 索引，用於 <a %(a_href)s>ratarmount</a>。 發佈 2 附錄 (%(date)s) 我哋已經獲得咗所有喺我哋上次鏡像同 2022 年 8 月之間添加到 Z-Library 嘅書籍。我哋亦都回頭抓取咗第一次錯過嘅書籍。總體嚟講，呢個新收藏大約有 24TB。同樣，呢個收藏已經對 Library Genesis 進行咗重複數據刪除，因為嗰個收藏已經有種子可用。 數據組織同第一次發佈類似。有一個 MySQL “.sql.gz” 元數據轉儲，亦都包括第一次發佈嘅所有元數據，從而取代咗佢。我哋仲添加咗啲新列： 我哋上次提過呢點，但係為咗澄清：“filename” 同 “md5” 係文件嘅實際屬性，而 “filename_reported” 同 “md5_reported” 係我哋從 Z-Library 抓取嘅數據。有時呢兩者唔一致，所以我哋包括咗兩者。 喺呢次發佈中，我哋將排序規則更改為 “utf8mb4_unicode_ci”，應該同舊版本嘅 MySQL 兼容。 數據文件同上次類似，但係大咗好多。我哋實在唔想創建大量較細嘅種子文件。“pilimi-zlib2-0-14679999-extra.torrent” 包含咗上次發佈中錯過嘅所有文件，而其他種子都係新嘅 ID 範圍。  <strong>更新 %(date)s：</strong> 我哋做咗大部分種子太大，導致種子客戶端難以處理。我哋已經移除佢哋並發佈咗新嘅種子。 <strong>更新 %(date)s：</strong> 文件仲係太多，所以我哋將佢哋包喺 tar 文件入面，再次發佈咗新嘅種子。 %(key)s：呢個文件是否已經喺 Library Genesis 入面，無論係非小說定小說收藏（通過 md5 匹配）。 %(key)s：呢個文件喺邊個種子入面。 %(key)s：當我哋無法下載書籍時設置。 發佈 2 (%(date)s) Zlib發佈（原始描述頁面） Tor域名 主網站 Z-Library抓取 Z-Library入面嘅「中文」收藏睇嚟同我哋嘅DuXiu收藏一樣，但MD5唔同。為咗避免重複，我哋會將呢啲檔案排除喺種子檔案之外，但仍然會喺我哋嘅搜尋索引中顯示。 元數據 您獲得%(percentage)s%%次額外快速下載，因為您係由用戶%(profile_link)s介紹。 呢個適用於整個會員期。 捐款 加入 已選擇 高達%(percentage)s%%折扣 Alipay 支持國際信用卡/借記卡。睇吓<a %(a_alipay)s>呢個指南</a>了解更多資訊。 用你嘅信用卡/借記卡發送 Amazon.com 禮品卡畀我哋。 你可以用信用卡/借記卡買加密貨幣。 WeChat（Weixin Pay）支持國際信用卡/借記卡。喺 WeChat 應用程式中，去“我 => 服務 => 錢包 => 添加卡”。如果你睇唔到呢個選項，請通過“我 => 設置 => 通用 => 工具 => Weixin Pay => 啟用”啟用佢。 （當從Coinbase發送Ethereum時使用） 已複製！ 複製 （最低金額） （警告：高最低金額） -%(percentage)s%% 12個月 1 個月 24個月 3個月 48個月 6個月 96個月 選擇你想訂閱嘅時長。 <div %(div_monthly_cost)s></div><div %(div_after)s>扣除 <span %(span_discount)s></span> 折扣後</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 個月 1 個月 24 個月 3 個月 48 個月 6 個月 96 個月 %(monthly_cost)s / 月 聯絡我哋 直接<strong>SFTP</strong>伺服器 企業級捐款或者交換新收藏（例如新掃描，OCR數據集）。 專家級訪問 <strong>無限</strong>高速訪問 <div %(div_question)s>我可以升級我嘅會員資格或者獲得多個會員資格嗎？</div> <div %(div_question)s>我可以唔成為會員而捐款嗎？</div> 當然可以。我哋接受任何金額嘅捐款，呢個係Monero (XMR)地址：%(address)s。 <div %(div_question)s>每月範圍代表咩意思？</div> 你可以通過應用所有折扣，例如選擇超過一個月嘅時期，嚟達到範圍嘅低端。 <div %(div_question)s>會員資格會自動續期嗎？</div> 會員資格<strong>唔會</strong>自動續期。你可以選擇加入嘅時間長短。 <div %(div_question)s>你哋會點樣使用捐款？</div> 100%%用嚟保存同提供世界知識同文化。目前我哋主要用喺伺服器、存儲同帶寬上。冇錢會畀到任何團隊成員。 <div %(div_question)s>我可以做大額捐款嗎？</div> 太好啦！如果捐款超過幾千美元，請直接聯絡我哋：%(email)s。 <div %(div_question)s>你哋有其他付款方法嗎？</div> 目前冇。好多人成日唔想呢啲檔案存在，所以我哋要小心。如果你可以幫我哋安全地設置其他（更方便）嘅付款方法，請聯絡我哋：%(email)s。 捐款常見問題 您有一個<a %(a_donation)s>現有嘅捐款</a>進行中。請完成或取消該捐款先，再進行新嘅捐款。 <a %(a_all_donations)s>查看我所有嘅捐款</a> 如果捐款超過$5000，請直接聯絡我哋：%(email)s。 我哋歡迎來自富裕個人或者機構嘅大額捐款。  請注意，雖然呢頁面嘅會員資格係「每月」，但係佢哋係一次性捐款（唔會重複）。睇睇<a %(faq)s>捐款常見問題</a>。 Anna’s Archive係一個非牟利、開源、開放數據嘅項目。通過捐款同成為會員，您支持我哋嘅運營同發展。多謝所有會員：多謝您哋令我哋可以繼續運作！❤️ 想了解更多資訊，請查看<a %(a_donate)s>捐款常見問題</a>。 要成為會員，請 <a %(a_login)s>登入或註冊</a>。多謝您嘅支持！ $%(cost)s / 月 如果你喺付款過程中出錯，我哋唔能夠退款，但我哋會盡力解決。 喺你嘅PayPal應用程式或者網站搵到“加密貨幣”頁面。呢個通常喺“財務”下面。 去你嘅PayPal應用程式或者網站嘅“比特幣”頁面。按“轉移”按鈕%(transfer_icon)s，然後“發送”。 支付寶 支付寶 支付宝 / 微信 微信 亞馬遜禮品卡 %(amazon)s 禮品卡 銀行卡 銀行卡（使用應用程式） 幣安 信用卡/借記卡/Apple/Google（BMC） Cash App 信用卡/借記卡 信用卡/借記卡 2 信用卡/借記卡（備用） 加密貨幣 %(bitcoin_icon)s 信用卡 / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal（常規） Pix (Brazil) Revolut （暫時無法使用） 微信 選擇您偏好嘅加密貨幣： 用 Amazon 禮品卡捐款。 <strong>重要：</strong> 呢個選項係俾 %(amazon)s 用嘅。如果你想用其他Amazon網站，請喺上面揀選。 <strong>重要提示：</strong> 我哋只支持 Amazon.com，唔支持其他 Amazon 網站。例如，.de、.co.uk、.ca 都唔支持。 請唔好寫你自己嘅信息。 輸入準確金額：%(amount)s 請注意，我哋需要將金額四捨五入到我哋轉售商接受嘅金額（最低%(minimum)s）。 使用信用卡/借記卡捐款，通過支付寶應用程式（設置超簡單）。 從<a %(a_app_store)s>Apple App Store</a>或<a %(a_play_store)s>Google Play Store</a>安裝支付寶應用程式。 用你嘅電話號碼註冊。 唔需要進一步嘅個人資料。 <span %(style)s>1</span>安裝支付寶應用程式 支持：Visa、MasterCard、JCB、Diners Club同Discover。 睇睇<a %(a_alipay)s>呢個指南</a>了解更多資訊。 <span %(style)s>2</span>添加銀行卡 用 Binance，你可以用信用卡/借記卡或者銀行賬戶買 Bitcoin，然後將 Bitcoin 捐畀我哋。咁樣我哋可以喺接受你嘅捐款時保持安全同匿名。 Binance 幾乎喺每個國家都可以用，並且支持大部分銀行同信用卡/借記卡。呢個係我哋目前嘅主要推薦方法。我哋感謝你花時間學習點樣用呢個方法捐款，因為呢個對我哋幫助好大。 對於信用卡、借記卡、Apple Pay 同 Google Pay，我哋使用“Buy Me a Coffee”（BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>）。喺佢哋嘅系統中，一“咖啡”等於 $5，所以你嘅捐款會四捨五入到最接近嘅 5 倍數。 使用 Cash App 捐款。 如果您有 Cash App，這是最簡單的捐款方式！ 請注意，對於低於 %(amount)s 的交易，Cash App 可能會收取 %(fee)s 費用。對於 %(amount)s 或以上的交易，是免費的！ 使用信用卡或借記卡捐款。 呢個方法使用加密貨幣供應商作為中間轉換。呢個可能有啲混亂，所以請只喺其他支付方法唔適用嘅情況下使用呢個方法。呢個方法亦唔適用於所有國家。 我哋唔能夠直接支持信用卡/借記卡，因為銀行唔想同我哋合作。☹ 不過，仲有幾個方法可以用信用卡/借記卡，通過其他支付方式： 用加密貨幣捐款，您可以使用 BTC、ETH、XMR 和 SOL。如果您已經熟悉加密貨幣，請使用此選項。 用加密貨幣捐款，您可以使用 BTC、ETH、XMR 等。 加密貨幣快速服務 如果您係第一次使用加密貨幣，我哋建議使用%(options)s嚟買同捐比特幣（最原始同最常用嘅加密貨幣）。 請注意，小額捐款嘅信用卡手續費可能會抵消我哋嘅%(discount)s%%折扣，所以我哋建議選擇較長嘅訂閱期。 用信用卡/借記卡、PayPal 或 Venmo 捐款。你可以喺下一頁選擇其中一個。 Google Pay 同 Apple Pay 都可能適用。 請注意，小額捐款嘅手續費比較高，所以我哋建議選擇較長嘅訂閱期。 要使用 PayPal US 捐款，我們會使用 PayPal Crypto，這樣可以保持匿名。感謝您花時間學習如何使用這種方法捐款，這對我們幫助很大。 使用 PayPal 捐款。 用你嘅常規 PayPal 賬戶捐款。 用 Revolut 捐款。 如果你有 Revolut，呢個係最簡單嘅捐款方法！ 此付款方式只允許最高 %(amount)s。請選擇其他時長或付款方式。 此付款方式需要最低 %(amount)s。請選擇其他時長或付款方式。 幣安 Coinbase Kraken 請選擇一個付款方式。 「認養一個torrent」：你嘅用戶名或者訊息會出現在torrent文件名中 <div %(div_months)s>每12個月會員期一次</div> 您的用戶名或匿名提及會顯示喺鳴謝名單上 優先體驗新功能 獨家Telegram頻道，提供幕後更新 每日%(number)s次快速下載 如果你今個月捐款！ <a %(a_api)s>JSON API</a>訪問 在人類知識同文化保存方面嘅傳奇地位 之前嘅福利，加埋： 通過<a %(a_refer)s>介紹朋友</a>賺取<strong>%(percentage)s%%次額外下載</strong>。 SciDB論文<strong>無限</strong>無需驗證 當詢問帳戶或捐款問題時，請添加您嘅帳戶ID、截圖、收據，盡可能多嘅信息。我哋每1-2星期只檢查一次郵件，所以唔包括呢啲信息會延遲解決。 想獲得更多下載次數，<a %(a_refer)s>介紹朋友</a>！ 我哋係一個細小嘅義工團隊。可能需要1-2星期先回覆。 請注意，賬戶名稱或圖片可能會顯得奇怪。唔需要擔心！呢啲賬戶係由我哋嘅捐贈夥伴管理。我哋嘅賬戶冇被黑客入侵。 捐款 <span %(span_cost)s></span> <span %(span_label)s></span> 12 個月 “%(tier_name)s” 1 個月“%(tier_name)s” 24 個月 “%(tier_name)s” 3 個月 “%(tier_name)s” 48 個月 “%(tier_name)s” 6 個月 “%(tier_name)s” 96 個月 “%(tier_name)s” 您仍然可以在結帳時取消捐款。 點擊捐款按鈕確認此捐款。 <strong>重要提示：</strong> 加密貨幣價格波動好大，有時幾分鐘內可以波動20%%。呢個仲少過我哋同好多支付提供商合作時嘅手續費，佢哋通常會收50-60%%，因為我哋係“影子慈善機構”。<u>如果你將原價嘅收據發俾我哋，我哋會照樣將你選擇嘅會員資格記入你嘅帳戶</u>（只要收據唔超過幾個鐘）。我哋真係好感激你願意忍受呢啲嘢嚟支持我哋！❤️ ❌ 出現錯誤。請重新加載頁面並重試。 <span %(span_circle)s>1</span>喺Paypal買比特幣 <span %(span_circle)s>2</span>轉移比特幣到我哋嘅地址 ✅ 正在重定向到捐款頁面… 捐款 請等至少<span %(span_hours)s>24小時</span>（並刷新此頁面）再聯絡我哋。 如果你想捐款（任何金額）但唔想成為會員，可以使用呢個Monero (XMR)地址：%(address)s。 發送禮品卡後，我哋嘅自動系統會喺幾分鐘內確認。如果唔成功，請嘗試重新發送禮品卡（<a %(a_instr)s>指示</a>）。 如果仲係唔成功，請電郵我哋，Anna 會手動審查（可能需要幾日），並確保提及你已經嘗試重新發送。 例子： 請使用 <a %(a_form)s>官方 Amazon.com 表格</a> 發送 %(amount)s 禮品卡到以下電郵地址。 表格中嘅 “收件人” 電郵： 亞馬遜禮品卡 我哋唔接受其他方法嘅禮品卡，<strong>只接受直接從 Amazon.com 官方表格發送嘅禮品卡</strong>。如果你唔使用呢個表格，我哋唔能夠退還你嘅禮品卡。 只用一次。 獨一無二嘅賬戶，唔好分享。 等待禮品卡…（刷新頁面檢查） 打開<a %(a_href)s>QR碼捐款頁面</a>。 用支付寶應用程式掃描QR碼，或者按按鈕打開支付寶應用程式。 請耐心等候，因為頁面可能需要一段時間才能加載，因為係喺中國。 <span %(style)s>3</span>進行捐款（掃描QR碼或按按鈕） 喺PayPal買PYUSD幣 喺 Cash App 買 Bitcoin (BTC) 買多啲（我哋建議多買 %(more)s）過你捐嘅金額 (%(amount)s)，以支付交易費用。剩低嘅你可以自己留返。 去 Cash App 嘅 “Bitcoin” (BTC) 頁面。 將 Bitcoin 轉到我哋嘅地址 對於少量捐款（少過 $25），你可能需要用 Rush 或 Priority。 撳 “Send bitcoin” 鈕嚟做 “withdrawal”。撳 %(icon)s 圖標由美元轉去 BTC。輸入下面嘅 BTC 金額，然後撳 “Send”。如果有問題，可以睇 <a %(help_video)s>呢條片</a>。 快速服務方便，但收費較高。 如果你想快速進行較大額捐款，並且唔介意支付$5-10嘅費用，可以用呢個代替加密貨幣交易所。 一定要發送捐款頁面顯示嘅準確加密貨幣數量，而唔係$USD金額。 否則費用會被扣除，我哋無法自動處理你嘅會員資格。 有時確認可能需要多達24小時，所以請務必刷新此頁面（即使已過期）。 信用卡 / 借記卡指示 通過我哋嘅信用卡 / 借記卡頁面捐款 有啲步驟提到加密錢包，但唔使擔心，你唔需要學任何關於加密貨幣嘅知識。 %(coin_name)s 指示 使用你的加密貨幣錢包應用程式掃描這個 QR 二維碼，以快速填寫付款詳細資料 掃描 QR 碼付款 我哋只支持標準版本嘅加密貨幣，唔支持其他網絡或版本嘅貨幣。根據貨幣嘅不同，確認交易可能需要最多一小時。 喺<a %(a_page)s>呢個頁面</a>捐款%(amount)s。 呢個捐款已經過期。請取消並創建一個新嘅。 如果您已經付款： 係，我已經電郵咗收據 如果交易期間加密貨幣匯率波動，請務必包括顯示原始匯率嘅收據。我哋非常感激你用加密貨幣捐款，呢對我哋幫助好大！ ❌ 出咗啲問題。請重新加載頁面再試一次。 <span %(span_circle)s>%(circle_number)s</span>電郵我哋收據 如果你遇到任何問題，請聯繫我哋喺%(email)s，並盡可能提供多啲信息（例如截圖）。 ✅ 多謝你嘅捐款！Anna會喺幾日內手動激活你嘅會員資格。 將收據或截圖發送到你嘅個人驗證地址： 當你電郵咗收據之後，請點擊呢個按鈕，咁Anna可以手動審核（可能需要幾日時間）： 請將收據或截圖發送到你嘅個人驗證地址。唔好用呢個電郵地址進行PayPal捐款。 取消 係，請取消 你確定要取消嗎？如果你已經付款，請勿取消。 ❌ 出咗啲問題。請重新載入頁面再試一次。 重新捐款 ✅ 你嘅捐款已經取消。 日期: %(date)s 識別碼: %(id)s 重新訂購 狀態: <span %(span_label)s>%(label)s</span> 總計: %(total)s <span %(span_details)s>（%(monthly_amount_usd)s / 月，為期 %(duration)s 個月，包括 %(discounts)s%% 折扣）</span> 總計: %(total)s <span %(span_details)s>（%(monthly_amount_usd)s / 月，為期 %(duration)s 個月）</span> 1. 輸入你嘅電郵。 2. 選擇你嘅付款方式。 3. 再次選擇你嘅付款方式。 4. 選擇“自託管”錢包。 5. 點擊“我確認擁有權”。 6. 你應該會收到一封電郵收據。請將其發送俾我哋，我哋會盡快確認你嘅捐款。 （你可能想取消並創建一個新嘅捐款） 付款指示已經過期。如果你想再捐一次，請使用上面嘅“重新訂購”按鈕。 你已經付款。如果你仍然想查看付款指示，請點擊這裡： 顯示舊嘅付款指示 如果捐款頁面被封鎖，請嘗試使用其他網絡連接（例如 VPN 或手機網絡）。 不幸嘅係，支付寶頁面通常只喺 <strong>中國大陸</strong> 可訪問。你可能需要暫時禁用你嘅 VPN，或者使用 VPN 連接到中國大陸（有時香港都可以）。 <span %(span_circle)s>1</span>喺支付寶捐款 使用 <a %(a_account)s>呢個支付寶賬戶</a> 捐款總額 %(total)s 支付寶指示 <span %(span_circle)s>1</span>轉賬到我哋嘅其中一個加密貨幣帳戶 將總金額%(total)s捐到以下其中一個地址： 加密貨幣指示 跟住指示買比特幣（BTC）。你只需要買你想捐嘅數量，%(total)s。 輸入我哋嘅比特幣（BTC）地址作為收件人，跟住指示發送你嘅捐款%(total)s： <span %(span_circle)s>1</span>用Pix捐款 用<a %(a_account)s>呢個Pix賬戶</a>捐款總額%(total)s Pix指引 <span %(span_circle)s>1</span>用微信捐款 用<a %(a_account)s>呢個微信賬戶</a>捐款總額%(total)s 微信指示 用以下任何一個 “信用卡轉 Bitcoin” 快速服務，只需幾分鐘： BTC / Bitcoin 地址（外部錢包）： BTC / Bitcoin 金額： 請喺表格填寫以下資料： 如果以上資料有任何更新，請電郵通知我哋。 請使用呢個<span %(underline)s>準確金額</span>。由於信用卡手續費，你嘅總費用可能會較高。對於小額金額，呢個可能會超過我哋嘅折扣，不過。 （最低：%(minimum)s，首次交易無需驗證） （最低：%(minimum)s） （最低：%(minimum)s） （最低：%(minimum)s，首次交易無需驗證） （最低：%(minimum)s） （最低：%(minimum)s，視乎國家，首次交易無需驗證） 跟住指示買PYUSD幣（PayPal USD）。 買多少少（我哋建議多買 %(more)s），以覆蓋交易費用。你會保留剩餘嘅金額。 去你嘅 PayPal 應用程式或網站嘅 “PYUSD” 頁面。按 “轉賬” 按鈕 %(icon)s，然後選擇 “發送”。 更新狀態 要重設計時器，只需創建一個新嘅捐款。 請確保使用以下嘅 BTC 金額，<em>唔係</em>歐元或美元，否則我哋唔會收到正確嘅金額，亦無法自動確認你嘅會員資格。 喺 Revolut 買 Bitcoin (BTC) 買多啲（我哋建議多買 %(more)s）過你捐嘅金額 (%(amount)s)，以支付交易費用。剩低嘅你可以自己留返。 去 Revolut 嘅 “Crypto” 頁面買 Bitcoin (BTC)。 將 Bitcoin 轉到我哋嘅地址 對於少量捐款（少過 $25），你可能需要用 Rush 或 Priority。 撳 “Send bitcoin” 鈕嚟做 “withdrawal”。撳 %(icon)s 圖標由歐元轉去 BTC。輸入下面嘅 BTC 金額，然後撳 “Send”。如果有問題，可以睇 <a %(help_video)s>呢條片</a>。 狀態： 1 2 步驟指南 睇下面嘅逐步指南。 否則你可能會被鎖出呢個帳戶！ 如果你仲未寫低，你嘅登入密鑰： 多謝你嘅捐款！ 剩餘時間： 捐款 轉賬 %(amount)s 到 %(account)s 等待確認（刷新頁面檢查）… 等待轉移（刷新頁面檢查）… 更早 過去24小時內嘅快速下載會計入每日限額。 從快速合作夥伴伺服器下載嘅文件會標記為%(icon)s。 過去18小時 暫時未有下載文件。 已下載嘅文件唔會公開顯示。 所有時間都係UTC。 已下載嘅文件 如果你下載咗一個文件，並且有快速同慢速下載，佢會顯示兩次。 唔使太擔心，有好多其他人都係從我哋連結嘅網站下載，出事嘅機會極低。不過，為咗安全起見，我哋建議使用VPN（付費），或者<a %(a_tor)s>Tor</a>（免費）。 我下載咗喬治·奧威爾嘅《1984》，警察會唔會上門？ 你就係Anna！ Anna係邊個？ 我哋有一個穩定嘅JSON API俾會員用，攞快速下載URL：<a %(a_fast_download)s>/dyn/api/fast_download.json</a>（文檔喺JSON內）。 對於其他用途，例如遍歷我哋所有文件、建立自定義搜尋等等，我哋建議<a %(a_generate)s>生成</a>或者<a %(a_download)s>下載</a>我哋嘅ElasticSearch同MariaDB數據庫。原始數據可以手動通過<a %(a_explore)s>JSON文件</a>探索。 我哋嘅原始種子列表亦可以作為<a %(a_torrents)s>JSON</a>下載。 你哋有冇API？ 我哋唔會喺呢度寄存任何受版權保護嘅材料。我哋係一個搜索引擎，所以只會索引已經公開嘅元數據。喺下載呢啲外部來源嘅時候，我哋建議你檢查你所在司法管轄區嘅法律，睇下乜嘢係允許嘅。我哋唔對其他人寄存嘅內容負責。 如果你對喺呢度見到嘅內容有投訴，最好嘅方法係聯絡原始網站。我哋定期將佢哋嘅更改拉入我哋嘅數據庫。如果你真係認為你有有效嘅DMCA投訴，我哋應該回應，請填寫<a %(a_copyright)s>DMCA / 版權投訴表格</a>。我哋會認真對待你嘅投訴，並會盡快回覆你。 點樣舉報版權侵權？ 呢度有啲對影子圖書館同數字保存世界有特別意義嘅書： 你最鍾意嘅書係乜嘢？ 我哋亦想提醒大家，我哋所有嘅代碼同數據都係完全開源嘅。對於類似我哋嘅項目嚟講，呢個係獨一無二嘅——我哋唔知道有其他項目有咁大規模嘅目錄同時係完全開源嘅。我哋非常歡迎任何認為我哋運行項目唔好嘅人，攞我哋嘅代碼同數據，設立自己嘅影子圖書館！我哋唔係出於惡意或者乜嘢——我哋真心認為呢個會好棒，因為呢個會提高大家嘅標準，更好咁保存人類嘅遺產。 我好憎你哋點樣運行呢個項目！ 我哋好希望大家可以設置<a %(a_mirrors)s>鏡像</a>，我哋會提供財政支援。 我可以點幫手？ 我哋確實會。 我哋收集元數據嘅靈感係來自 Aaron Swartz 嘅目標：「為每本出版過嘅書創建一個網頁」，佢創建咗 <a %(a_openlib)s>Open Library</a>。嗰個項目做得唔錯，但我哋嘅獨特位置令我哋可以獲取佢哋無法獲取嘅元數據。另一個靈感係我哋想知道 <a %(a_blog)s>世界上有幾多本書</a>，咁我哋可以計算仲有幾多本書需要拯救。 你哋會收集元數據嗎？ 請注意，mhut.org會封鎖某啲IP範圍，所以可能需要VPN。 <strong>Android：</strong> 點擊右上角嘅三點菜單，然後揀“加到主畫面”。 <strong>iOS：</strong> 點擊底部嘅“分享”按鈕，然後揀“加到主畫面”。 我哋冇官方手機應用程式，但你可以將呢個網站安裝成應用程式。 你哋有冇手機應用程式？ 請寄去<a %(a_archive)s>Internet Archive</a>。佢哋會妥善保存。 我點樣捐贈書籍或者其他實體材料？ 我點樣請求書籍？ <a %(a_blog)s>Anna’s Blog</a>，<a %(a_reddit_u)s>Reddit</a>，<a %(a_reddit_r)s>Subreddit</a> — 定期更新 <a %(a_software)s>Anna’s Software</a> — 我哋嘅開源代碼 <a %(a_datasets)s>Datasets</a> — 關於數據 <a %(a_li)s>.li</a>，<a %(a_se)s>.se</a>，<a %(a_org)s>.org</a> — 替代域名 有冇更多關於Anna’s Archive嘅資源？ <a %(a_translate)s>Translate on Anna’s Software</a> — 我哋嘅翻譯系統 <a %(a_wikipedia)s>Wikipedia</a> — 更多關於我哋嘅資訊（請幫助保持呢個頁面更新，或者為你自己嘅語言創建一個！） 揀你鍾意嘅設定，保持搜尋框空白，點擊“搜尋”，然後用你嘅瀏覽器書籤功能將頁面加到書籤。 點樣儲存我嘅搜尋設定？ 我哋歡迎安全研究員搜索我哋系統入面嘅漏洞。我哋係負責任披露嘅大力支持者。聯絡我哋<a %(a_contact)s>呢度</a>。 我哋目前無法提供漏洞賞金，除咗有<a %(a_link)s>潛在威脅我哋匿名性</a>嘅漏洞，我哋會提供$10k-50k範圍內嘅賞金。我哋希望將來可以提供更廣泛嘅漏洞賞金範圍！請注意，社交工程攻擊唔喺範圍內。 如果你對攻擊性安全有興趣，並且想幫助存檔世界嘅知識同文化，記得聯絡我哋。你有好多方法可以幫助。 你哋有負責任披露計劃嗎？ 我哋真係冇足夠資源畀全世界每個人高速下載，雖然我哋好想。如果有個富有嘅贊助人願意幫手提供呢啲資源，真係太好啦，但係喺嗰之前，我哋會盡力而為。我哋係一個非牟利項目，靠捐款勉強維持。 呢個就係點解我哋同合作夥伴實施咗兩個免費下載系統：共享伺服器提供慢速下載，仲有稍快啲嘅伺服器有等候名單（減少同時下載嘅人數）。 我哋仲有<a %(a_verification)s>瀏覽器驗證</a>喺慢速下載度，因為如果唔係，機械人同爬蟲會濫用，令合法用戶嘅下載速度更加慢。 請注意，使用 Tor 瀏覽器時，你可能需要調整你嘅安全設置。喺最低嘅選項，即“標準”中，Cloudflare turnstile 挑戰會成功。喺較高嘅選項，即“更安全”同“最安全”中，挑戰會失敗。 有時下載大文件會中途中斷。我哋建議使用下載管理器（例如 JDownloader）來自動恢復大文件嘅下載。 點解下載咁慢？ 常見問題 (FAQ) 使用<a %(a_list)s>種子列表生成器</a>生成一個喺你存儲空間限制內最需要做種子嘅種子列表。 可以，睇<a %(a_llm)s>LLM數據</a>頁面。 大部分嘅種子直接包含文件，意思係你可以指示種子客戶端只下載所需嘅文件。要決定下載邊啲文件，你可以<a %(a_generate)s>生成</a>我哋嘅元數據，或者<a %(a_download)s>下載</a>我哋嘅ElasticSearch同MariaDB數據庫。不幸嘅係，有啲種子集合喺根目錄包含.zip或者.tar文件，呢種情況下你需要下載整個種子先可以揀選個別文件。 暫時未有易用嘅工具可以用嚟篩選種子，但我哋歡迎貢獻。 （不過，我們確實有<a %(a_ideas)s>一些想法</a>來解決後者的情況。） 詳細回答： 簡短回答：不容易。 我哋嘗試保持呢個列表入面嘅種子重複或者重疊最少，但呢個唔係每次都可以做到，仲好大程度上取決於來源圖書館嘅政策。對於自己發佈種子嘅圖書館，我哋無法控制。對於Anna’s Archive發佈嘅種子，我哋只會基於MD5哈希值去重複，意思係唔同版本嘅同一本書唔會被去重複。 可以。 呢啲其實係PDF同EPUB，只係喺我哋好多種子入面冇副檔名。你可以喺兩個地方搵到種子文件嘅元數據，包括文件類型/副檔名： 1. 每個集合或者發佈都有自己嘅元數據。例如，<a %(a_libgen_nonfic)s>Libgen.rs種子</a>有對應嘅元數據數據庫喺Libgen.rs網站上。我哋通常會喺每個集合嘅<a %(a_datasets)s>數據集頁面</a>鏈接到相關嘅元數據資源。 2. 我哋建議<a %(a_generate)s>生成</a>或者<a %(a_download)s>下載</a>我哋嘅ElasticSearch同MariaDB數據庫。呢啲數據庫包含Anna’s Archive每條記錄對應嘅種子文件嘅映射（如果有），喺ElasticSearch JSON入面嘅“torrent_paths”字段。 有啲種子客戶端唔支持大塊嘅片段，而我哋好多種子都有呢啲片段（對於新嘅種子，我哋已經唔再咁做——雖然根據規範係有效嘅！）。所以如果遇到呢個問題，可以試下用其他客戶端，或者向你嘅種子客戶端製作者投訴。 我想幫手做種子，但我冇咩硬碟空間。 種子太慢啦；我可以直接從你哋度下載數據嗎？ 我可以只下載部分文件，例如某個特定語言或者主題嘅文件嗎？ 你點樣處理種子入面嘅重複文件？ 我可以以JSON格式獲取種子列表嗎？ 我喺種子入面睇唔到PDF或者EPUB，只係見到二進制文件？我應該點做？ 點解我嘅種子客戶端開唔到你哋嘅種子文件/磁力鏈接？ 種子FAQ 我點樣上傳新書？ 請參閱<a %(a_href)s>呢個出色嘅項目</a>。 你有冇使用運行時間監控器？ 咩係Anna’s Archive？ 成為會員以使用快速下載。 我哋而家支持 Amazon 禮品卡、信用卡同借記卡、加密貨幣、支付寶同微信支付。 你今日嘅快速下載已經用完。 存取 過去30日每小時下載量。每小時平均：%(hourly)s。每日平均：%(daily)s。 我哋同合作夥伴一齊，令我哋嘅收藏可以輕鬆同免費畀任何人使用。我哋相信每個人都有權利獲得人類集體智慧。而且<a %(a_search)s>唔會犧牲作者嘅利益</a>。 安娜檔案館使用嘅數據集係完全開放嘅，可以用種子批量鏡像。<a %(a_datasets)s>了解更多…</a> 長期存檔 完整數據庫 搜索 書籍、論文、雜誌、漫畫、圖書館記錄、元數據，… 我哋嘅<a %(a_code)s>代碼</a>同<a %(a_datasets)s>數據</a>係完全開源嘅。 <span %(span_anna)s>Anna’s Archive</span>係一個非牟利項目，有兩個目標： <li><strong>保存：</strong>備份人類所有嘅知識同文化。</li><li><strong>存取：</strong>令呢啲知識同文化可以畀世界上任何人使用。</li> 我哋擁有世界上最大嘅高質量文本數據集。<a %(a_llm)s>了解更多…</a> LLM訓練數據 🪩 鏡像：志願者招募 如果您運營高風險匿名支付處理器，請聯繫我哋。我哋亦都喺尋找願意放置雅緻小廣告嘅人。所有收益都會用於我哋嘅保存工作。 保存 我哋估計我哋已經保存咗大約<a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%%嘅世界書籍</a>。 我哋保存書籍、論文、漫畫、雜誌等等，將呢啲材料從各種<a href="https://en.wikipedia.org/wiki/Shadow_library">影子圖書館</a>、官方圖書館同其他收藏中集中喺一齊。所有呢啲數據都會永久保存，通過使用種子檔案大規模複製，令世界各地有好多副本。一啲影子圖書館已經自己咁做（例如Sci-Hub、Library Genesis），而Anna’s Archive會“解放”其他唔提供大規模分發嘅圖書館（例如Z-Library）或者根本唔係影子圖書館嘅（例如Internet Archive、DuXiu）。 呢種廣泛分發，加上開源代碼，令我哋嘅網站可以抵抗下架，確保人類知識同文化嘅長期保存。了解更多關於<a href="/datasets">我哋嘅數據集</a>。 如果你係<a %(a_member)s>會員</a>，唔需要瀏覽器驗證。 🧬&nbsp;SciDB係Sci-Hub嘅延續。 SciDB 開放 DOI Sci-Hub已經<a %(a_paused)s>暫停</a>上傳新嘅論文。 直接訪問%(count)s篇學術論文 🧬&nbsp;SciDB 係 Sci-Hub 嘅延續，擁有熟悉嘅界面同直接睇 PDF。輸入你嘅 DOI 以查看。 我哋有完整嘅 Sci-Hub 收藏，仲有新論文。大部分可以用熟悉嘅界面直接睇，好似 Sci-Hub 咁。有啲可以通過外部來源下載，呢啲情況下我哋會顯示鏈接。 您可以通過做種子來幫助我們。<a %(a_torrents)s>了解更多…</a> >%(count)s 做種者 <%(count)s 做種者 %(count_min)s–%(count_max)s 做種者 🤝 尋找志願者 作為一個非牟利、開源項目，我哋一直都喺尋找幫手。 IPFS 下載 按%(by)s列表，創建於<span %(span_time)s>%(time)s</span> 儲存 ❌ 出咗啲問題。請再試一次。 ✅ 已儲存。請重新載入頁面。 列表係空嘅。 編輯 通過搵到文件並打開“列表”標籤嚟添加或移除。 列表 我哋可以點幫你 去重（重複數據刪除） 文本同元數據提取 OCR 我哋可以提供高速訪問我哋嘅全部收藏，仲有未發布嘅收藏。 呢個係企業級別嘅訪問，我哋可以提供，捐款範圍係幾萬美元。我哋亦願意用呢啲換取我哋未有嘅高質量收藏。 如果你可以為我哋提供數據豐富化，我哋可以退款，例如： 支持長期保存人類知識，同時為你嘅模型獲得更好嘅數據！ <a %(a_contact)s>聯絡我哋</a> 討論我哋可以點合作。 大家都知道 LLM 需要高質量嘅數據。我哋擁有世界上最大嘅書籍、論文、雜誌等收藏，呢啲都係最高質量嘅文本來源之一。 LLM 數據 獨特嘅規模同範圍 我哋嘅收藏包含超過一億個文件，包括學術期刊、教科書同雜誌。我哋通過結合大型現有庫存達到呢個規模。 我哋嘅部分來源收藏已經可以批量獲取（Sci-Hub 同部分 Libgen）。其他來源係我哋自己解放嘅。<a %(a_datasets)s>Datasets</a> 顯示完整概覽。 我哋嘅收藏包括數百萬本電子書時代之前嘅書籍、論文同雜誌。呢個收藏嘅大部分已經進行咗 OCR 處理，內部重複度已經好低。 繼續 如果你唔見咗鎖匙，請<a %(a_contact)s>聯絡我哋</a>，並提供盡可能多嘅資料。 你可能需要暫時創建一個新帳戶嚟聯絡我哋。 請<a %(a_account)s>登錄</a>以查看此頁面。</a> 為防止垃圾機械人創建大量帳戶，我哋需要先驗證您嘅瀏覽器。 如果您陷入無限循環，我哋建議安裝 <a %(a_privacypass)s>Privacy Pass</a>。 關閉廣告攔截器同其他瀏覽器擴展可能亦有幫助。 登入 / 註冊 Anna’s Archive 暫時維護中。請一個鐘後再嚟。 替代作者 替代描述 替代版次 替代扩展名 替代文件名 替代出版社 替代标题 开源日期 了解更多… 描述 搜尋 Anna’s Archive 嘅 CADAL SSNO 編號 搜尋 Anna’s Archive 嘅 DuXiu SSID 編號 搜尋 Anna’s Archive 嘅 DuXiu DXID 編號 搜尋 Anna’s Archive 嘅 ISBN 搜尋 Anna’s Archive 嘅 OCLC (WorldCat) 編號 搜尋 Anna’s Archive 嘅 Open Library ID Anna’s Archive 在線查看器 %(count)s 受影響頁面 下載後： 呢個文件嘅更好版本可能喺 %(link)s 有 批量種子下載 收藏 使用在線工具轉換格式。 推薦轉換工具：%(links)s 對於大型文件，我哋建議使用下載管理器以防止中斷。 推薦下載管理器：%(links)s EBSCOhost 電子書索引 （僅限專家） （亦可点击顶部“获取”） （点击顶部“获取”） 外部下載 <strong>🚀 快速下載</strong> 你今日仲有 %(remaining)s 次。多謝你成為會員！❤️ <strong>🚀 快速下載</strong> 你今日嘅快速下載已經用晒。 <strong>🚀 快速下載</strong> 你最近下載過呢個文件。連結會保持有效一段時間。 <strong>🚀 快速下載</strong> 成為 <a %(a_membership)s>會員</a> 以支持長期保存書籍、論文等。為咗表示感謝你嘅支持，你可以享受快速下載。❤️ 🚀 快速下載 🐢 慢速下載 从Internet Archive借阅 IPFS 閘道 #%(num)d （您可能需要多次嘗試使用IPFS） Libgen.li Libgen.rs 小说类 Libgen.rs 非小说类 他们的广告已知包含恶意软件，所以请使用广告拦截器或不要点击广告 Amazon嘅「Send to Kindle」 djazz嘅「Send to Kobo/Kindle」 MagzDB ManualsLib Nexus/STC （Nexus/STC 文件可能唔可靠下載） 未找到下載。 所有下載選項都係同一個文件，應該係安全使用。不過，從互聯網下載文件時，特別係從Anna’s Archive外部網站下載時，請務必小心。例如，請確保您的設備保持更新。 （無重定向） 在我們的查看器中打開 （在查看器中打開） 選項 #%(num)d： %(link)s %(extra)s 喺 CADAL 搵原始記錄 喺 DuXiu 手動搜尋 喺 ISBNdb 搵原始記錄 喺 WorldCat 搵原始記錄 喺 Open Library 搵原始記錄 搜尋其他各種數據庫嘅 ISBN （仅限打印残障人士） PubMed 你需要一個電子書或者PDF閱讀器嚟打開文件，視乎文件格式而定。 推薦電子書閱讀器：%(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s （相关DOI可能在Sci-Hub中不可用） 你可以將PDF同EPUB文件發送到你嘅Kindle或者Kobo電子閱讀器。 推薦工具：%(links)s 更多資訊請參閱<a %(a_slow)s>常見問題</a>。 支持作者同圖書館 如果你鍾意呢個而且負擔得起，考慮購買原版，或者直接支持作者。 如果你嘅本地圖書館有呢本書，可以考慮免費借閱。 合作服务器下载暂时不可用。 種子 來自可信賴嘅合作夥伴。 Z-Library Z-Library 喺 Tor 上 （需要 Tor 瀏覽器） 顯示外部下載 <span class="font-bold">❌ 呢個文件可能有問題，已經喺來源圖書館隱藏咗。</span> 有時係版權持有人要求，有時係因為有更好嘅替代品，但有時係因為文件本身有問題。可能仍然可以下載，但我哋建議首先搜尋替代文件。更多詳情： 如果你仍然想下載呢個文件，請確保只使用可信、更新嘅軟件去打開。 元数据评论 AA： 搜尋 Anna’s Archive 嘅「%(name)s」 代碼探索器： 喺代碼探索器睇「%(name)s」 網址： 網站： 如果你有呢個文件而且佢仲未喺 Anna’s Archive 上提供，考慮 <a %(a_request)s>上傳佢</a>。 Internet Archive 受控數字借閱文件「%(id)s」 呢個係來自 Internet Archive 嘅文件記錄，唔係直接下載文件。你可以試下借呢本書（下面有連結），或者喺 <a %(a_request)s>請求文件</a> 時使用呢個 URL。 改善元數據 CADAL SSNO %(id)s 元數據記錄 呢個係元數據記錄，唔係下載文件。你可以喺 <a %(a_request)s>請求文件</a> 時使用呢個 URL。 DuXiu SSID %(id)s 元數據記錄 ISBNdb %(id)s 元數據記錄 MagzDB ID %(id)s 元數據記錄 Nexus/STC ID %(id)s 元數據記錄 OCLC (WorldCat) 編號 %(id)s 元數據記錄 Open Library %(id)s 元數據記錄 Sci-Hub 文件「%(id)s」 未找到 “%(md5_input)s” 喺我哋嘅數據庫中未找到。 添加評論 (%(count)s) 你可以從URL獲取md5，例如 呢個文件更好版本嘅MD5（如果適用）。 如果有另一個文件同呢個文件好接近（同一版本，同一文件擴展名，如果可以搵到），人哋應該用嗰個文件代替呢個文件。如果你知道Anna’s Archive以外有更好版本嘅文件，請 <a %(a_upload)s>上傳</a>。 出咗啲問題。請重新載入頁面再試一次。 你已經留咗言。可能需要一分鐘先顯示出嚟。 請使用 <a %(a_copyright)s>DMCA / 版權申訴表格</a>。 描述問題（必填） 如果呢個檔案質量好，你可以喺度討論任何關於佢嘅嘢！如果唔係，請用“報告檔案問題”按鈕。 文件質量優秀 (%(count)s) 文件質量 學習點樣<a %(a_metadata)s>改善呢個檔案嘅metadata</a>。 問題描述 請 <a %(a_login)s>登入</a>。 我好鍾意呢本書！ 幫助社群報告呢個文件嘅質量！🙌 出咗啲問題。請重新載入頁面再試一次。 報告文件問題 (%(count)s) 多謝你提交報告。報告會顯示喺呢個頁面，並且由Anna手動審核（直到我哋有一個正式嘅審核系統）。 留言 提交報告 呢個文件有咩問題？ 借閱（%(count)s） 評論（%(count)s） 下載（%(count)s） 探索元數據（%(count)s） 清單（%(count)s） 統計數據（%(count)s） 關於呢個特定檔案嘅信息，請查閱佢嘅<a %(a_href)s>JSON檔案</a>。 呢個係由<a %(a_ia)s>IA嘅受控數字借閱</a>圖書館管理嘅檔案，並由Anna’s Archive索引以供搜索。關於我哋編輯嘅各種datasets嘅信息，請睇<a %(a_datasets)s>Datasets頁面</a>。 連結記錄嘅元數據 改善Open Library嘅元數據 “檔案MD5”係一個從檔案內容計算出嚟嘅哈希值，根據內容嚟講係相當獨特嘅。所有我哋索引嘅影子圖書館主要都用MD5嚟識別檔案。 一個檔案可能會出現喺多個影子圖書館。關於我哋編輯嘅各種datasets嘅信息，請睇<a %(a_datasets)s>Datasets頁面</a>。 報告文件質量 總下載次數：%(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} 捷克元數據 %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} 警告：多個連結記錄： 當你喺Anna’s Archive睇一本書嘅時候，你可以睇到唔同嘅欄位：書名、作者、出版社、版本、年份、描述、文件名等等。所有呢啲資訊都叫做<em>元數據</em>。 由於我哋將書籍從唔同嘅<em>來源圖書館</em>合併，我哋會顯示來源圖書館提供嘅任何元數據。例如，對於一本我哋從Library Genesis獲得嘅書，我哋會顯示Library Genesis數據庫中嘅書名。 有時一本書會喺<em>多個</em>來源圖書館出現，呢啲圖書館可能有唔同嘅元數據欄位。喺呢種情況下，我哋會簡單顯示每個欄位最長嘅版本，因為嗰個版本希望包含最多有用嘅資訊！我哋仍然會喺描述下面顯示其他欄位，例如作為“替代書名”（但只係當佢哋唔同嘅時候）。 我哋亦會從來源圖書館提取<em>代碼</em>，例如標識符同分類器。<em>標識符</em>唯一代表一本書嘅特定版本；例子包括ISBN、DOI、Open Library ID、Google Books ID或者Amazon ID。<em>分類器</em>將多本相似嘅書籍分組；例子包括杜威十進制分類法（DCC）、國際十進制分類法（UDC）、美國國會圖書館分類法（LCC）、RVK或者GOST。有時呢啲代碼喺來源圖書館中係明確連結嘅，有時我哋可以從文件名或者描述中提取佢哋（主要係ISBN同DOI）。 我哋可以使用標識符喺<em>僅有元數據嘅集合</em>中找到記錄，例如OpenLibrary、ISBNdb或者WorldCat/OCLC。如果你想瀏覽呢啲集合，我哋嘅搜索引擎中有一個特定嘅<em>元數據標籤</em>。我哋使用匹配嘅記錄嚟填補缺失嘅元數據欄位（例如如果缺少書名），或者例如作為“替代書名”（如果已經有書名）。 要睇一本書嘅元數據嚟自邊度，請睇書頁面上嘅<em>“技術詳情”標籤</em>。佢有一個鏈接到該書嘅原始JSON，並指向原始記錄嘅原始JSON。 欲了解更多資訊，請參閱以下頁面：<a %(a_datasets)s>數據集</a>、<a %(a_search_metadata)s>搜索（元數據標籤）</a>、<a %(a_codes)s>代碼探索器</a>同<a %(a_example)s>示例元數據JSON</a>。最後，我哋所有嘅元數據都可以<a %(a_generated)s>生成</a>或者<a %(a_downloaded)s>下載</a>為ElasticSearch同MariaDB數據庫。 背景 你可以通過改善元數據嚟幫助保存書籍！首先，喺Anna’s Archive睇吓關於元數據嘅背景資料，然後學習點樣通過連結Open Library嚟改善元數據，並獲得Anna’s Archive嘅免費會員資格。 改善元數據 咁如果你遇到一個元數據差嘅文件，應該點樣修復佢呢？你可以去來源圖書館並按照佢嘅程序修復元數據，但如果一個文件喺多個來源圖書館出現，應該點做呢？ 喺Anna’s Archive有一個標識符係特別處理嘅。<strong>Open Library上嘅annas_archive md5欄位總係會覆蓋所有其他元數據！</strong> 讓我哋先退一步，了解一下Open Library。 Open Library喺2006年由Aaron Swartz創立，目標係“為每本曾經出版過嘅書創建一個網頁”。佢有啲似書籍元數據嘅Wikipedia：每個人都可以編輯，佢係免費授權嘅，並且可以批量下載。佢係一個最符合我哋使命嘅書籍數據庫——事實上，Anna’s Archive係受Aaron Swartz嘅願景同生活啟發嘅。 與其重新發明輪子，我哋決定將我哋嘅志願者引導到Open Library。如果你見到一本書嘅元數據有錯誤，你可以通過以下方式幫助： 請注意，呢個只適用於書籍，唔適用於學術論文或者其他類型嘅文件。對於其他類型嘅文件，我哋仍然建議搵來源圖書館。因為我哋需要下載最新嘅Open Library數據dump，並重新生成我哋嘅搜尋索引，所以變更可能需要幾個星期先會被包括喺Anna’s Archive入面。  去<a %(a_openlib)s>Open Library網站</a>。 找到正確嘅書籍記錄。<strong>警告：</strong> 一定要選擇正確嘅<strong>版本</strong>。喺Open Library中，你有“作品”同“版本”。 一個“作品”可以係“哈利波特與魔法石”。 一個“版本”可以係： 1997年由Bloomsbery出版嘅第一版，有256頁。 2003年由Raincoast Books出版嘅平裝版，有223頁。 2000年由Media Rodzina出版嘅波蘭文翻譯版《Harry Potter I Kamie Filozoficzn》，有328頁。 呢啲版本都有唔同嘅ISBN同唔同嘅內容，所以記得揀啱嗰個！ 編輯記錄（或者如果無記錄就創建一個），加多啲有用嘅資訊！你而家都喺度，不如將記錄整得真係好勁。 喺“ID Numbers”嗰度揀“Anna’s Archive”，然後加上Anna’s Archive入面本書嘅MD5。呢個係URL入面“/md5/”之後嘅一長串字母同數字。 試下搵下Anna’s Archive入面其他同呢個記錄相符嘅文件，然後都加埋。將來我哋可以喺Anna’s Archive搜尋頁面將呢啲文件分組為重複項。 當你完成咗，寫低你啱啱更新嘅URL。當你更新咗至少30個有Anna’s Archive MD5嘅記錄之後，send我哋一封<a %(a_contact)s>電郵</a>，send埋個list俾我哋。我哋會俾你一個免費嘅Anna’s Archive會員資格，咁你可以更加容易咁做呢啲工作（同埋多謝你嘅幫助）。呢啲必須係高質量嘅編輯，加入大量有用嘅資訊，否則你嘅請求會被拒絕。如果有任何編輯被Open Library嘅管理員撤回或者更正，你嘅請求亦會被拒絕。 Open Library連結 如果你喺我哋嘅工作發展同運作中有重大參與，我哋可以討論分享更多嘅捐款收入畀你，讓你按需要使用。 我哋只會喺你設置好一切，並證明你能夠保持檔案庫隨時更新之後，先支付託管費用。呢意味住你需要自掏腰包支付頭1-2個月嘅費用。 你嘅時間唔會有補償（我哋嘅時間都唔會），因為呢個係純義工工作。 我哋願意支付託管同VPN費用，初期每月最多$200。呢個數額足夠支付一個基本嘅搜尋伺服器同一個受DMCA保護嘅代理伺服器。 託管費用 請 <strong>唔好聯絡我哋</strong>問許可或者基本問題。行動勝於言語！所有資料都喺度，所以直接開始設置你嘅鏡像。 如果你遇到問題，隨時可以喺我哋嘅Gitlab提交票據或者合併請求。我哋可能需要同你一齊建立啲鏡像專屬功能，例如將“Anna’s Archive”重新命名為你嘅網站名，（初期）禁用用戶賬戶，或者從書頁鏈接返我哋嘅主網站。 一旦你嘅鏡像運行起來，請聯絡我哋。我哋會樂意審查你嘅OpSec，一旦確認無問題，我哋會鏈接到你嘅鏡像，並開始同你更緊密合作。 提前多謝任何願意以呢種方式貢獻嘅人！呢個唔係膽小嘅人做嘅，但會鞏固人類歷史上最大真正開放圖書館嘅長久性。 開始使用 為咗增加 Anna’s Archive 嘅韌性，我哋而家招募志願者運行鏡像。 你嘅版本明顯區分為鏡像，例如「Bob’s Archive，Anna’s Archive鏡像」。 你願意承擔與呢項工作相關嘅風險，呢啲風險係相當大嘅。你對操作安全有深刻嘅理解。<a %(a_shadow)s>呢啲</a> <a %(a_pirate)s>帖子</a>嘅內容對你嚟講係顯而易見嘅。 初期我哋唔會俾你訪問我哋嘅合作伺服器下載，但如果情況順利，我哋可以同你分享。 你運行Anna’s Archive嘅開源代碼庫，並定期更新代碼同數據。 你願意同我哋嘅團隊合作，為我哋嘅<a %(a_codebase)s>代碼庫</a>作出貢獻，以實現呢個目標。 我哋搵緊呢啲： 鏡像：招募志願者 再捐一次。 暫時未有捐款。<a %(a_donate)s>進行我嘅第一次捐款。</a> 捐款詳情唔會公開顯示。 我嘅捐款 📡 想批量鏡像我哋嘅收藏，請查看<a %(a_datasets)s>數據集</a>同<a %(a_torrents)s>種子</a>頁面。 您嘅 IP 地址喺過去 24 小時內嘅下載次數：%(count)s。 🚀 為咗更快下載同跳過瀏覽器檢查，<a %(a_membership)s>成為會員</a>。 從合作網站下載 等緊嘅時候，您可以繼續喺另一個標籤頁瀏覽 Anna’s Archive（如果您嘅瀏覽器支持背景標籤頁刷新）。 您可以等多個下載頁面同時加載（但請每個伺服器同一時間只下載一個文件）。 一旦您獲得下載鏈接，佢會有效幾個鐘。 多謝等候，呢個可以保持網站免費俾大家使用！😊 🔗 此文件嘅所有下載鏈接：<a %(a_main)s>文件主頁</a>。 ❌ 慢速下載唔可以通過 Cloudflare VPN 或其他 Cloudflare IP 地址。 ❌ 慢速下載只可以通過官方網站。請訪問 %(websites)s。 📚 使用以下 URL 下載：<a %(a_download)s>立即下載</a>。 為咗俾大家有機會免費下載文件，您需要等一陣先可以下載呢個文件。 請等 <span %(span_countdown)s>%(wait_seconds)s</span> 秒下載呢個文件。 警告：您嘅 IP 地址喺過去 24 小時內有大量下載。下載速度可能會比平時慢。 如果您使用 VPN、共享網絡連接，或者您嘅 ISP 共享 IP 地址，呢個警告可能係因為呢啲原因。 儲存 ❌ 出咗啲問題。請再試一次。 ✅ 已儲存。請重新載入頁面。 更改你嘅顯示名稱。你嘅識別碼（“#”後面嘅部分）唔可以更改。 已建立個人檔案 <span %(span_time)s>%(time)s</span> 編輯 清單 透過搵到文件並打開「清單」標籤嚟創建新清單。 暫時未有清單 未搵到個人資料。 個人資料 目前，我哋無法滿足書籍請求。 唔好電郵我哋你嘅書籍請求。 請喺Z-Library或Libgen論壇提出你嘅請求。 Anna’s Archive 記錄 DOI: %(doi)s 下載 SciDB Nexus/STC 暫時無預覽。從<a %(a_path)s>Anna’s Archive</a>下載文件。 為咗支持人類知識嘅可及性同長期保存，成為<a %(a_donate)s>會員</a>。 額外獎勵，🧬&nbsp;SciDB 對會員加快載入，無任何限制。 唔得？試下<a %(a_refresh)s>重新整理</a>。 Sci-Hub 添加特定搜索字段 搜尋描述同元數據評論 出版年份 進階 訪問 內容 顯示 列表 表格 文件類型 語言 排序方式 最大 最相關 最新 （文件大小） （開源） （出版年份） 最舊 隨機 最細 來源 由AA抓取並開源 數碼借閱 (%(count)s) 期刊文章 (%(count)s) 我哋喺以下位置搵到匹配：%(in)s。你可以喺<a %(a_request)s>請求文件</a>時參考嗰度嘅URL。 元數據 (%(count)s) 要通過代碼探索搜尋索引，請使用<a %(a_href)s>代碼探索器</a>。 搜尋索引每月更新一次。目前包括到%(last_data_refresh_date)s嘅條目。更多技術信息，請參閱<a %(link_open_tag)s>數據集頁面</a>。 排除 只包括 未選中 更多… 下一頁 … 上一頁 呢個搜尋索引目前包括來自Internet Archive受控數碼借閱圖書館嘅元數據。<a %(a_datasets)s>更多關於我哋嘅數據集</a>。 更多數碼借閱圖書館，請參閱<a %(a_wikipedia)s>維基百科</a>同<a %(a_mobileread)s>MobileRead Wiki</a>。 對於DMCA / 版權聲明<a %(a_copyright)s>點擊這裡</a>。 下載時間 搜索時出錯。 試下<a %(a_reload)s>重新加載頁面</a>。如果問題持續，請發電郵俾我哋：%(email)s。 快速下載 事實上，任何人都可以通過播種我哋嘅<a %(a_torrents)s>統一種子列表</a>嚟幫助保存呢啲文件。 ➡️ 有時當搜索伺服器慢嘅時候，呢個情況會錯誤發生。喺呢啲情況下，<a %(a_attrs)s>重新加載</a>可以幫到手。 ❌ 呢個文件可能有問題。 尋找論文？ 呢個搜尋索引目前包括來自各種元數據來源嘅元數據。<a %(a_datasets)s>更多關於我哋嘅數據集</a>。 全世界有好多好多來源提供寫作作品嘅元數據。<a %(a_wikipedia)s>呢個維基百科頁面</a>係一個好嘅開始，但如果你知道其他好嘅列表，請通知我哋。 對於元數據，我哋顯示原始記錄。我哋唔會合併記錄。 我哋目前擁有世界上最全面嘅開放書籍、論文同其他書面作品目錄。我哋鏡像Sci-Hub、Library Genesis、Z-Library、<a %(a_datasets)s>等等</a>。 <span class="font-bold">未搵到文件。</span> 試下用少啲或者唔同嘅搜索詞同篩選條件。 結果 %(from)s-%(to)s（總共%(total)s） 如果你發現其他我哋應該鏡像嘅“影子圖書館”，或者有任何問題，請聯繫我哋：%(email)s。 %(num)d 部分匹配 %(num)d+ 部分匹配 喺框中輸入以搜尋數碼借閱圖書館嘅文件。 喺框中輸入以搜尋我哋嘅%(count)s個直接下載文件目錄，我哋<a %(a_preserve)s>永久保存</a>。 喺框中輸入以進行搜索。 喺框中輸入以搜尋我哋嘅%(count)s個學術論文同期刊文章目錄，我哋<a %(a_preserve)s>永久保存</a>。 喺框中輸入以搜尋圖書館嘅元數據。呢個喺<a %(a_request)s>請求文件</a>時可能有用。 提示：使用鍵盤快捷鍵“/”（搜索焦點）、“enter”（搜索）、“j”（上）、“k”（下）、“<”（上一頁）、“>”（下一頁）以更快導航。 呢啲係元數據記錄，<span %(classname)s>唔係</span>可下載文件。 搜尋設置 搜尋 數碼借閱 下載 期刊文章 元數據 新搜尋 %(search_input)s - 搜尋 搜尋時間太長，可能會出現唔準確嘅結果。有時候<a %(a_reload)s>重新載入</a>頁面會有幫助。 搜尋時間太長，呢個係廣泛查詢嘅常見情況。篩選計數可能唔準確。 對於大規模上傳（超過10,000個文件）而Libgen或Z-Library唔接受，請聯絡我哋：%(a_email)s。 對於Libgen.li，請確保首先使用用戶名%(username)s和密碼%(password)s在<a %(a_forum)s>他們的論壇</a>上登錄，然後返回他們的<a %(a_upload_page)s>上傳頁面</a>。 目前，我哋建議你將新書上傳到Library Genesis分支。呢度有個<a %(a_guide)s>方便嘅指南</a>。請注意，我哋網站索引嘅兩個分支都係從呢個上傳系統拉取數據。 對於小型上傳（最多10,000個文件），請將它們上傳到%(first)s和%(second)s。 或者，你可以將佢哋上傳到Z-Library<a %(a_upload)s>呢度</a>。 要上傳學術論文，請同時（除咗Library Genesis）上傳到<a %(a_stc_nexus)s>STC Nexus</a>。佢哋係新論文嘅最佳影子圖書館。我哋仲未整合佢哋，但係將來會。你可以使用佢哋嘅<a %(a_telegram)s>Telegram上傳機械人</a>，或者如果你有太多文件要上傳，可以聯絡佢哋釘住嘅訊息中列出嘅地址。 <span %(label)s>繁重嘅義工工作（USD$50-USD$5,000 獎金）：</span>如果你可以投入大量時間同/或資源去支持我哋嘅使命，我哋好樂意同你更緊密合作。最終你可以加入我哋嘅核心團隊。雖然我哋嘅預算好緊，但我哋可以為最繁重嘅工作頒發<span %(bold)s>💰 金錢獎金</span>。 <span %(label)s>輕量義工工作：</span>如果你只能偶爾抽出幾個鐘，仲有好多方法可以幫手。我哋會獎勵持續嘅義工 <span %(bold)s>🤝 Anna’s Archive會員</span>。 Anna’s Archive依賴你哋呢啲義工。我哋歡迎所有程度嘅承諾，並有兩個主要嘅幫助類別： 如果你無法抽出時間做義工，你仍然可以通過<a %(a_donate)s>捐款</a>、<a %(a_torrents)s>做種我哋嘅種子</a>、<a %(a_uploading)s>上傳書籍</a>或者<a %(a_help)s>告訴你嘅朋友關於Anna’s Archive</a>嚟幫助我哋。 <span %(bold)s>公司：</span>我哋提供高速直接訪問我哋嘅收藏，作為企業級捐款或者交換新收藏（例如新掃描、OCR數據集、豐富我哋嘅數據）嘅回報。如果你係呢類公司，請<a %(a_contact)s>聯絡我哋</a>。亦可以睇睇我哋嘅<a %(a_llm)s>LLM頁面</a>。 懸賞 我哋一直都喺搵有紮實編程或者攻擊性安全技能嘅人參與。你可以喺保存人類遺產方面做出重大貢獻。 作為感謝，我哋會為有實質貢獻嘅人提供會員資格。作為重大感謝，我哋會為特別重要同困難嘅任務提供金錢懸賞。呢個唔應該被視為工作嘅替代品，但係係一個額外嘅激勵，亦可以幫助支付相關費用。 我哋大部分嘅代碼都係開源嘅，當頒發懸賞時，我哋亦會要求你嘅代碼係開源嘅。呢度有啲例外情況，我哋可以單獨討論。 懸賞會頒發俾第一個完成任務嘅人。你可以喺懸賞票據上留言，讓其他人知道你喺做緊啲咩，咁其他人就可以暫時唔做或者聯絡你一齊合作。但係要注意，其他人仍然可以自由地做呢個任務，並嘗試搶先完成。不過，我哋唔會為草率嘅工作頒發懸賞。如果有兩個高質量嘅提交喺接近時間內完成（例如一兩日內），我哋可能會選擇頒發懸賞俾兩個人，例如第一個提交100%%，第二個提交50%%（總共150%%）。 對於較大嘅懸賞（特別係爬蟲懸賞），請喺你完成咗大約5%%時聯絡我哋，並且你有信心你嘅方法可以擴展到全部里程碑。你需要同我哋分享你嘅方法，咁我哋可以提供反饋。咁樣，我哋可以決定如果有多個人接近完成懸賞時應該點做，例如可能會頒發俾多個人，鼓勵人哋合作等等。 警告：高懸賞任務係<span %(bold)s>困難</span>嘅——可能開始做啲簡單啲嘅任務會比較明智。 去我哋嘅<a %(a_gitlab)s>Gitlab 問題列表</a>，按“標籤優先級”排序。呢個大致顯示咗我哋關心嘅任務順序。冇明確懸賞嘅任務仍然有資格獲得會員資格，特別係標記為“已接受”同“Anna’s favorite”嘅任務。你可能想從“入門項目”開始。 輕量義工 我哋而家喺%(matrix)s有個同步嘅Matrix頻道。 如果你有幾個鐘頭嘅空閒時間，你可以用多種方式幫助我哋。記得加入<a %(a_telegram)s>Telegram嘅義工聊天群</a>。 作為感謝嘅象徵，我哋通常會為基本里程碑頒發6個月嘅「幸運圖書館員」，而持續嘅義工工作會有更多獎勵。所有里程碑都需要高質量嘅工作——草率嘅工作對我哋嘅傷害大過幫助，我哋會拒絕。當你達到里程碑時，請<a %(a_contact)s>電郵我哋</a>。 %(links)s 條連結或者截圖你完成咗嘅請求。 喺Z-Library或者Library Genesis論壇上滿足書籍（或者論文等）嘅請求。我哋無自己嘅書籍請求系統，但我哋鏡像咗嗰啲圖書館，所以改善佢哋就係改善Anna’s Archive。 里程碑 任務 視乎任務而定。 喺我哋<a %(a_telegram)s>Telegram嘅義工聊天群</a>上發佈嘅小任務。通常係為咗會員，有時係為咗小獎金。 喺我哋嘅志願者聊天群組中發佈嘅小任務。 記得喺你解決嘅問題上留言，咁其他人就唔會重複你嘅工作。 %(links)s 條記錄連結你改進咗。 你可以用<a %(a_list)s>隨機metadata問題列表</a>作為起點。 通過<a %(a_metadata)s>連結</a>Open Library嚟改善元數據。 呢啲應該顯示你通知某人關於Anna’s Archive，佢哋感謝你。 %(links)s 條連結或者截圖。 傳播Anna’s Archive嘅訊息。例如，推薦AA上嘅書籍，鏈接到我哋嘅博客文章，或者一般性地引導人哋去我哋嘅網站。 完全翻譯一種語言（如果之前未接近完成）。 <a %(a_translate)s>翻譯</a>網站。 連結到編輯歷史，顯示你做咗重大貢獻。 改善你語言嘅Anna’s Archive嘅Wikipedia頁面。包括來自其他語言嘅AA Wikipedia頁面嘅信息，仲有我哋網站同博客嘅信息。喺其他相關頁面加入AA嘅參考資料。 義工同懸賞 