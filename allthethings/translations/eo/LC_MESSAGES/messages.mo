��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b 8  sd 1  �f .   �g   
h �  i �  �j n  �l   n Y   "o j   |o H   �o `   0p �  �p ?  )r �   is �  ?t �   �u �  �v �  �x �  �z �   _| �  } �  �~ 4   �� �   �� N   �� �  �    ��   Ń H   х #   �    >�    M� B   i� "   ��    φ .   ߆    � #   -�    Q� ;   c� %  �� �   ň H   É 2   � 
   ?� 
   M�    [� 
   o�    }�    �� 	   ��    ��    �� $   Ǌ    �    � 	   � 
   �    (�    4� !   K�    m�   �� ;  �� u   ގ .   T� @  �� �   Đ �   V� �   <�    �� �   �    �� �   ��    ]� �   }�    y� S  �� /   � W   �    p� !  u� =  �� �  ՙ   ��    �� D   �� Q   � �   B� ,   �� *   
� j   8� :   �� ;   ޞ (   �    C� {   H� �  ğ 9   z� �   �� �   C� =   � &  *� 7   Q� �   �� �   �� �   � K   �� �   �� �   �� H   ?� �   �� �   y� ]   >� C   �� �   �    a� �   h�   �� H   �    W� �   `� �   ��    �� l  ɮ �   6�   ԰ �  ٱ �   f� "  G� �   j� S   Y� C   �� O   � i   A� Q   �� H   �� (   F� �   o� O   �� 7   E�    }� i   �� H   �� �   ?� �   ޼ 
   j� �   x� �   X� I  V� *  �� �   �� �   T� +  0� �   \� �   � �   �� W  =� �   �� �   J�    7� �   @� �   �� �   �� Z  �� �   �� �   `� �   �� �  �� l   N� ?  ��   �� %   � `   *� �   �� C   � U  ]� �   �� ^   O� m   �� #   � !  @� �   b� g  0�    �� Z   ��    � �   �� �   Q� �   7� �  
� �  �� ,   r� V  �� C  ��   :� q  J�   ��   �� W   ��    �    �� �   �� '  g� t  �� �   � �   �� �   �� 	   X� =  b� 8  �� �   �� 
   �� �   �� 0  X� �   �� �  I� �   F� T   � 
   t� q  �� P  �� 
   E�   P� �   S  �  I �   8 P   P   W I   � �   � 3   �        �   4   4	 #   K
 H   o
 �  �
 v   � z   
 m   �
 Q   !   V    x Z  � �   � �   � @   ( �   i )   � �      
        ! !   - (   O 2   x 6   � K   � o   .    � )   � @   � B    (   ] 5   � .   � ]   �    I    O �   b 
  Y �   g �   D v   ; �   � 2  n    � �   � �   � �     Z  �  h  " �  q# �  Q% T  �& H   R( (   �(    �( �   �( �  �) m  W+   �, B  �- �   / �  0 �   2 1   3   43   :4 W  Z5    �6 �   �6 !  �7 �   �8 M  R9 �   �:    {; (   �; [   �;    < v  < r  �>   �? �   B �   �B    C �   �C �   bD H   LE m   �E   F �   G �   �G \  �H c   �I   FJ    IK �  ]K �   EM �  �M S  �O �  �Q �   �S    �T �   �T �  kU    W O   W �  pX �  :Z �  (\ �   �]   �^ H   �_ �  B` �   	b |   �b B   Rc W   �c    �c 6   �c !   )d ,   Kd )   xd    �d �   �d �  Ee �  �g   �i �   �k    �l �  �l �  io V  ^q V  �r B  t   Ov    lx �   ux 	   _y    iy i  zy   �z   �| 0      K� �  Z� �  ?� �  �� �  �� �  ��   � I  !� "   k� �   �� d   +� �  �� v  � �   �� �   6� �   �� T  d�    �� �   ϖ B   r� N   ��    � 6   � 2   M�   �� }  �� �   � 5   �� �  �� �   >�    ܡ �   � 5   �� ~   Ȣ �   G� 0   �� T   .�    �� /  ��   ¥ �  ֧ U  �� �   �    a� e   u�   ۫    �    � e  g� �   ͯ .   `� 2   �� (  ° .   � =  � _  X� �  ��    p� r   �� �   �   �� u  �� 	   � �   )� .   "� �   Q� �   �   �� �   �� M   �� .   �� �   � <   � r  B� 1  �� �   �� �   �� d   �� <   �� �   '�   �� e  �� �  =� �  � )   �� �   ��   �� 1  �� �   � �  �� �  �� .   T� �  �� "   N� �  q� W   8�    ��   �� �  �� {  �� �   2�   � �  �   �� �   � �  �� �  W� y   �� �  j� L  3� <   �� s   ��    1�    ��    �� =  �� �   � 0   �� 
  �� �  ��   �� �  �� s  �� �   � �  ��   4�    C� �   Z�    � Q  (� "   z     �     �     �  $   �     �             -    2 	   E    O    V 
   _    m    � #   �    � 	   �    �    � �   � O   � #   � 
   	 $    !   < (   ^ /   � &   � 	   �    �    �    �        !    0    6    G    L -   [ !   �    �    �     � 4    /   9    i %   } >   �    � F   � 9   <    v O   | C   �        , "   >    a    r    �    �    �    �    �    �    �      	   
 
       " "   %    H    O 	   X    b 	   {    �    �    � 	   �    �    �    �    �    �    	    $	    ,	 	   ;	    E	 $   U	    z	    �	 "   �	    �	    �	    �	    �	    �	 	   �	    �	 c   
 �   p
 I   �
 �   E �  � q   �
        !    >    P    W    d 
   | &   � V   � 1    \   7 &   � &   � $   � i    b   q �   � `   � 1   
 $   ?    d    q    w 	   �    �    �    �    �    �    �    �    �    �            &    :    G    W    ^    g    p    |    �   �    �    �    �     �    � 8    [   > -   � (   � 3   �    %    -    5 {   8    �    � #   � {   �    l    � l   � (    �  0 U   + q   � �   � �   � ,   E �   r r   r 2  � �        !    ! 9   '! F   a! P   �! ]   �! X   W" B   �" b   �" !   V# 0   x#    �#    �# K   �# #   $    8$    >$    M$    V$ p   o$    �$ +   �$ I   %    g%     }% Q   �% T   �% t  E&    �'    �' �   �' 	   �(    �(    �(    �(    �( ,   �( J  �(    C* 
   a*    o* 
   * ;  �* "   �+    �+    �+ m   �+    g, 	   m, 7   w,    �,    �, #   �, �   �,    �-    �- G   �-     �-    .    !.    *. (   :. P   c.    �. .   �. �   �. O   �/ N   �/    >0 �   Q0 H   ;1    �1 .   �1    �1 �   �1 �   �2    +3 9   =3 �   w3 �   �3    �4    �4 
   �4 f  5 )   h6 %   �6    �6 #   �6 !   �6 �   7    �7    �7 ,   �7 B   8    S8    [8    w8 %   �8 8  �8 U  �: �  L< 2   #> /   V>    �> $   �> ?  �> �   �? �   �@    wA �   �A �  hB    �C #   D �  7D B  F    GG    SG 4   bG    �G #  �G    �H <  �H �  J �   �K    �L p   �L *   1M &   \M m   �M $  �M "  O �   9P �  �P y   JR   �R R   �S �    T �   �T K   �U �   �U +   ~V %   �V    �V    �V    �V '   �V    %W D   EW /   �W 	   �W -   �W   �W (   �X �   (Y t   Z {   {Z %   �Z    [    8[    O[ ,   e[    �[ '   �[    �[ �   �[    �\ �   �\    �] �   �] x   �^ x   �^ /  r_ �   �` *   \a ~   �a 	   b 	  b m   c    �c �  �c    He    Ue    me    �e )   �e    �e    �e P   �e �   (f �   �f    �g    �g    �g �   �g /  }h �   �i v   bj    �j    �j    k    k    4k    Gk    Wk @   _k 0   �k �  �k Q   �m    �m g   �m r   Xn R   �n b   o V   �o U   �o    .p ^   7p I   �p �   �p Z   fq O   �q    r �   $r h   
s ?   vs b   �s X   t >   rt    �t 7   �t z   �t �   pu 6   �u �   .v    �v �   �v C   �w b   &x �   �x    &y �  /y �   {    �{    �{    �{    | �   | 0   �| _   /} �   �} �   W~ �   ! �   � �   m� �   �� �   �� S   Q�   �� `   �� N  � �   g� �   � 
   
� 
   � 
   )� �   7� 
   ͇ 
   ۇ @   � W   *� �   �� 
   R� n   `� /   ω z   �� ;   z� k   �� ^   "� 
   �� ~   �� 
   � 
   � 
   *�   8� �   P� �  ֎    ��    �� 
   �� �   ��    X� %   v� �   �� �   ��    ,�    J�    Z� 7   x� 9   �� 1   �    �    6� �   P�    $� u  A� �   �� �   �� P   8� �   �� �   $� �  � )  � �   � �   Ğ u   J�    �� q  ԟ    F� e  d� �   ʢ #  �� �   դ <  X� �   �� �   D� s   ۧ .   O� 5   ~�    �� +   Ȩ    ��     �    
� �   �    �� k   � -   q�    �� 
   ��    ��    �� r   ت G   K� 3   �� �   ǫ &   ��    �    �    ��    �    3� 
   E�    P� 
   Y� 	   d� 
   n� 	   y� 
   �� !   �� �   ��    K�    \�    k�    x� 
   ��    �� 
   ��    ��    ��    ۮ %   � l   �    {� (   �� `   �� �   � �   �� �   p� �   � �   � 
  �� 
   �� �   ʴ 5   M� A   �� F   ŵ �   � �   �� @   }� `   ��    � R   2� e   �� �   �    x�     �    ��    ��    ȹ    Թ    �� )   ��    &�    /�    A�    X�    t�    ��    ��    ��    Ǻ    ׺    �    �    � #   � !   /� w   Q�    ɻ (   I� "   r� [   �� O   � x   A� '   �� /   � 4   � <   G� ?   �� *   ľ �   �   ��   ��    �� B   �� z   � &   �� �   �� �   �� l   ?� 9   ��    �� �   �� �   �� g   � .   w� ]   �� �   �    �� )   ��    � A   � g   `� b   ��    +�    3�    <�    C� �   _� /   ��    � 8   3�     l�    ��    �� A   ��    	� h   $� 5   �� �   �� G   �� P   � �   R� ?   �� "   9�     \� "   }� !   �� "   �� !   �� "   � 0   *� 6   [�   �� 9   �� 7   �� @   � $   \�    �� v   �� m   �� �   m� �   � 	   �� �   �� %   O�    u� �   ��    P� "   a� <   �� 1   �� b   �� _   V� G   ��    ��     � �   >� 0   �� "   � F   )� �   p� @   N� �   �� Y   � S   j� k   �� $   *� -   O� t   }�    �� N   �    [� �   t� 5   (� <   ^�    �� +   �� �   �� 9   �� J   �� �   � R   �� =   �� �   ,� t   ��    )�    0� =   D� 9   ��    ��    ��    ��    ��    � .   � �   H� h   ��    ?�    Y� "   u� %   �� &   �� x   �� -   ^� i   �� P   ��     G� b   h� �   �� /   �� U   ��    � E   2� >   x�    �� p   �� s   <� ,   �� N   ��    ,� /   @� U   p�    �� h   �� &   F�    m� +   �� R   �� �   � >   ��    
�    "� >   :�    y� O   �� :   �� �   �    ��    3� 7   G� �   �    � �   5� A   �� "   � E   1� �   w�    T�    ]�    _�    a� (   z� -   �� C   ��    �    ,�    <� #   C� :   g� =   ��    �� G   �� C   1�    u� $   �� /   ��    ��    �� S   � �   \� D   -�    r�    �� �   �� =  6� T   t�    �� N  �� �  *� '   �� k   �� %   K� =  q� 2   �� Y   ��    <�    Q� �  ^�    	 E   ' |   m o   � f   Z &   � Z   � =   C    � v   � B    4   R Y   � /   � M    �   _ �   � *   { �   � G  R �   � /   5	 =  e	 �   �
 �   �   E �   T
 2       7 �   L 1   � �   c   � E   X    �    � �  �    j �   o *  D !  o   � <   � O   � T   . +   � 0   � V   � U   7    �     � :   � $       & *   C [   n (   �    � V   � �   R �   (    �    �    � M   � \   . V   � �   � j   �    
 $    �   A 
   � �   
  �  �  �   C# M   -$ *   {$    �$    �$    �$ E   �$ ,   �$ x   ,% �   �% O   �&    �&    �& &   '    ,' L   G' 
   �' ;   �'    �' "   �' +   
(    6(    L( ^   T(    �(    �( !   �( "   �(    ) d   ) �   {) P   P* ^   �* F    + �   G+    ,    , �   3, �   �, �   �-    f. t   o. ;   �. C    / W   d/ _   �/ O   0    l0 [   �0    �0    �0    	1    1    /1    F1    [1    n1    �1 	   �1 1   �1 1   �1 1   �1 $   02 6   U2 /   �2     �2    �2 
   �2 @   3    F3    _3 2   g3 &   �3 V   �3 .   4    G4    _4 '   o4    �4    �4 e   �4 Y   .5 y   �5 �   6    �6    �6 "   7    87 '   N7 	   v7    �7    �7 j   �7    8 !   -8    O8 
   V8 	   a8 6   k8    �8 �   �8    �9    �9    �9 "   �9 "   :    A: #   Y: %   }: *   �: ,   �: *   �:    &; ^   -; (   �;    �;    �; -   �; M   <    _< 7   |< "   �< l   �< V   D= J   �=    �=    �= 	   >    >    #>    ?> k  Z> v   �?    =@    R@ .   V@    �@ %   �@    �@    �@ �   �@ B   NA �   �A    `B #   wB �   �B #   C    AC    aC     C /   �C %   �C    �C 
   D 6   D    UD %   qD 9   �D 6  �D 7   F 6   @F G   wF    �F �   �F     iG    �G S   �G    �G $   H    &H B   ?H 7   �H &   �H �   �H 
   sI    �I (   �I    �I    �I    �I     �I    J    /J Y   GJ   �J    �K #   �K �   �K �   �L    ^M    xM    �M    �M    �M    �M    �M    �M    N    %N    8N    GN 
   XN    fN    uN    �N    �N    �N $   �N �   �N �   �O h  �P �  R �  	T �   �U w  rV    �W �   �W    �X �   �X �   �Y �  �Z �   `\ f  	] 7   p^ �   �^ ?   M_    �_ =   �_ G   �_ _   (` a   �` �   �` �   �a �   Ob C  c    ^e �   ve �   f \   �f �   Jg    �g �   �g .  �h �   �i �   �j    Tk ^   \k j   �k �   &l {   m r   �m v   n    �n    �n    �n E   �n +   o    >o    Ko ;   �o n   p    vp �   �p ~   q A   �q E   �q W   r e   tr Z   �r F   5s r   |s h   �s s   Xt k   �t    8u "   Au +   du Y   �u 2   �u    v    %v M   ,v    zv    �v    �v +   �v &   �v :    w    ;w    Xw    ew    mw 	   sw ^   }w h   �w N   Ex 3   �x    �x    �x -   �x    y 	   +y    5y    ;y    By    Iy    Oy 
   Vy    ay    hy    ty    �y 	   �y    �y    �y    �y    �y    �y    �y    �y    �y !   z    3z u   Oz    �z R   �z �   0{    �{ 
   �{    |    |    |    |    "| �   *| }   �| C   J}    �}    �} n   �}    "~ y   1~ �   �~ *   0    [    v �   � `   ŀ �   &� p   � +   Z� }   ��    �    � G   5� �   }�    � �   (� �   Ą �   E� X   ��    W�    g�    n�    ��    ��    ��    ��    �� �   Ɇ m   U� �   Ç �   I� �   � g   މ L   F� �  �� Z  >�   �� �   ��   8� =  L�    �� �   ��   A� �   [� P  � �  U� v   � b  x�    ۙ @   � �   -� Q   >   � �   S�    8�    D�    J� �   `� 6   � ^   &� 3   �� Y   �� I   � V   ]� "   �� �   ן B   v� .   �� F   � �   /�    �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: eo
Language-Team: eo <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library estas populara (kaj kontraŭleĝa) biblioteko. Ili prenis la kolekton de Library Genesis kaj faris ĝin facile serĉebla. Krome, ili fariĝis tre efikaj en petado de novaj libro-kontribuoj, instigante kontribuantajn uzantojn per diversaj avantaĝoj. Ili nuntempe ne kontribuas ĉi tiujn novajn librojn reen al Library Genesis. Kaj male al Library Genesis, ili ne faras sian kolekton facile spegulebla, kio malhelpas vastan konservadon. Ĉi tio estas grava por ilia komerca modelo, ĉar ili ŝargas monon por aliri sian kolekton amase (pli ol 10 libroj tage). Ni ne faras moralajn juĝojn pri postulado de mono por amasa aliro al kontraŭleĝa kolekto de libroj. Estas sendube, ke la Z-Biblioteko sukcesis plivastigi aliron al scio kaj akiri pli da libroj. Ni estas simple ĉi tie por fari nian parton: certigi la longdaŭran konservadon de ĉi tiu privata kolekto. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>) En la originala eldono de la Pirata Biblioteka Spegulo (EDIT: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>), ni faris spegulon de Z-Library, granda kontraŭleĝa librokolekto. Kiel memorigilo, jen kion ni skribis en tiu originala bloga afiŝo: Tiu kolekto datiĝas de meze de 2021. Dume, la Z-Biblioteko kreskis je miriga rapideco: ili aldonis ĉirkaŭ 3.8 milionojn da novaj libroj. Estas iuj duplikatoj tie, certe, sed la plimulto ŝajnas esti legitime novaj libroj aŭ pli altkvalitaj skanaĵoj de antaŭe senditaj libroj. Ĉi tio estas grandparte pro la pliigita nombro de volontulaj moderigantoj ĉe la Z-Biblioteko kaj ilia amasa alŝuta sistemo kun deduplikado. Ni ŝatus gratuli ilin pri ĉi tiuj atingoj. Ni ĝojas anonci, ke ni akiris ĉiujn librojn, kiuj estis aldonitaj al la Z-Biblioteko inter nia lasta spegulo kaj aŭgusto 2022. Ni ankaŭ revenis kaj skrapis iujn librojn, kiujn ni maltrafis la unuan fojon. Ĉiukaze, ĉi tiu nova kolekto estas ĉirkaŭ 24TB, kio estas multe pli granda ol la lasta (7TB). Nia spegulo nun estas 31TB entute. Denove, ni deduplikis kontraŭ Library Genesis, ĉar jam estas torentoj disponeblaj por tiu kolekto. Bonvolu iri al la Pirata Biblioteka Spegulo por kontroli la novan kolekton (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>). Estas pli da informoj tie pri kiel la dosieroj estas strukturitaj kaj kio ŝanĝiĝis ekde la lasta fojo. Ni ne ligos al ĝi de ĉi tie, ĉar ĉi tio estas nur bloga retejo, kiu ne gastigas iujn kontraŭleĝajn materialojn. Kompreneble, semado ankaŭ estas bonega maniero helpi nin. Dankon al ĉiuj, kiuj semas nian antaŭan aron de torentoj. Ni estas dankemaj pro la pozitiva respondo kaj ĝojas, ke estas tiom da homoj, kiuj zorgas pri konservado de scio kaj kulturo en ĉi tiu nekutima maniero. 3x novaj libroj aldonitaj al la Pirata Biblioteka Spegulo (+24TB, 3.8 milionoj da libroj) Legu la akompanajn artikolojn de TorrentFreak: <a %(torrentfreak)s>unua</a>, <a %(torrentfreak_2)s>dua</a> - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) akompanaj artikoloj de TorrentFreak: <a %(torrentfreak)s>unua</a>, <a %(torrentfreak_2)s>dua</a> Ne tro longe antaŭe, “ombro-bibliotekoj” estis malaperantaj. Sci-Hub, la granda kontraŭleĝa arkivo de akademiaj artikoloj, ĉesis akcepti novajn verkojn pro procesoj. “Z-Library”, la plej granda kontraŭleĝa biblioteko de libroj, vidis siajn supozitajn kreintojn arestitaj pro krimaj kopirajtaj akuzoj. Ili nekredeble sukcesis eskapi sian areston, sed ilia biblioteko ne estas malpli sub minaco. Iuj landoj jam faras version de ĉi tio. TorrentFreak <a %(torrentfreak)s>raportis</a> ke Ĉinio kaj Japanio enkondukis AI-esceptojn al siaj kopirajtaj leĝoj. Estas neklare al ni kiel ĉi tio interagas kun internaciaj traktatoj, sed ĝi certe donas kovron al iliaj enlandaj kompanioj, kio klarigas tion, kion ni vidis. Kiel por la Arĥivo de Anna — ni daŭrigos nian subteran laboron radikitan en morala konvinko. Tamen nia plej granda deziro estas eniri la lumon, kaj plifortigi nian efikon laŭleĝe. Bonvolu reformi kopirajton. Kiam Z-Library alfrontis fermon, mi jam subtenis ĝian tutan bibliotekon kaj serĉis platformon por gastigi ĝin. Tio estis mia motivo por komenci la Arĥivon de Anna: daŭrigo de la misio malantaŭ tiuj pli fruaj iniciatoj. Ni ekde tiam kreskis por esti la plej granda ombro-biblioteko en la mondo, gastigante pli ol 140 milionojn da kopirajtitaj tekstoj tra diversaj formatoj — libroj, akademiaj artikoloj, magazinoj, gazetoj, kaj pli. Mia teamo kaj mi estas ideologoj. Ni kredas, ke konservi kaj gastigi ĉi tiujn dosierojn estas morale ĝuste. Bibliotekoj tra la mondo vidas buĝetajn tranĉojn, kaj ni ne povas fidi la heredaĵon de la homaro al korporacioj ankaŭ. Tiam venis AI. Preskaŭ ĉiuj grandaj kompanioj konstruantaj LLM-ojn kontaktis nin por trejni sur niaj datumoj. Plej multaj (sed ne ĉiuj!) usonaj kompanioj rekonsideris post kiam ili ekkomprenis la kontraŭleĝan naturon de nia laboro. Kontraste, ĉinaj firmaoj entuziasme akceptis nian kolekton, ŝajne ne ĝenataj de ĝia laŭleĝeco. Ĉi tio estas rimarkinda donita la rolon de Ĉinio kiel subskribinto de preskaŭ ĉiuj gravaj internaciaj kopirajtaj traktatoj. Ni donis altrapidan aliron al ĉirkaŭ 30 kompanioj. Plej multaj el ili estas LLM-kompanioj, kaj kelkaj estas datumaj perantoj, kiuj revendos nian kolekton. Plej multaj estas ĉinaj, kvankam ni ankaŭ laboris kun kompanioj el Usono, Eŭropo, Rusio, Sud-Koreio, kaj Japanio. DeepSeek <a %(arxiv)s>konfesis</a> ke pli frua versio estis trejnita sur parto de nia kolekto, kvankam ili estas silentemaj pri sia plej nova modelo (verŝajne ankaŭ trejnita sur niaj datumoj tamen). Se la Okcidento volas resti antaŭe en la vetkuro de LLM-oj, kaj finfine, AGI, ĝi devas rekonsideri sian pozicion pri kopirajto, kaj baldaŭ. Ĉu vi konsentas kun ni aŭ ne pri nia morala kazo, ĉi tio nun fariĝas kazo de ekonomiko, kaj eĉ de nacia sekureco. Ĉiuj potencoblokoj konstruas artefaritajn super-sciencistojn, super-hakistojn, kaj super-militistojn. Libereco de informo fariĝas afero de supervivo por ĉi tiuj landoj — eĉ afero de nacia sekureco. Nia teamo estas el la tuta mondo, kaj ni ne havas apartan alineon. Sed ni kuraĝigus landojn kun fortaj kopirajtaj leĝoj uzi ĉi tiun ekzistadan minacon por reformi ilin. Do kion fari? Nia unua rekomendo estas simpla: mallongigi la kopirajtan periodon. En Usono, kopirajto estas donita por 70 jaroj post la morto de la aŭtoro. Ĉi tio estas absurda. Ni povas alporti ĉi tion en linion kun patentoj, kiuj estas donitaj por 20 jaroj post la dosierado. Ĉi tio devus esti pli ol sufiĉa tempo por aŭtoroj de libroj, artikoloj, muziko, arto, kaj aliaj kreaj verkoj, por esti plene kompensitaj por siaj klopodoj (inkluzive de pli longtempaj projektoj kiel filmadaptadoj). Tiam, minimume, politikofarantoj devus inkluzivi esceptojn por la amasa konservado kaj disvastigo de tekstoj. Se perdita enspezo de individuaj klientoj estas la ĉefa zorgo, persona-nivela distribuo povus resti malpermesita. Siaflanke, tiuj kapablaj administri vastajn deponejojn — kompanioj trejnantaj LLM-ojn, kune kun bibliotekoj kaj aliaj arĥivoj — estus kovritaj de ĉi tiuj esceptoj. Reformo de kopirajto estas necesa por nacia sekureco TL;DR: Ĉinaj LLM-oj (inkluzive de DeepSeek) estas trejnitaj sur mia kontraŭleĝa arkivo de libroj kaj artikoloj — la plej granda en la mondo. La Okcidento devas reformi kopirajtleĝon kiel afero de nacia sekureco. Bonvolu vidi la <a %(all_isbns)s>originalan blogaĵon</a> por pli da informoj. Ni eldonis defion por plibonigi tion. Ni aljuĝus unuan lokon kun premio de $6,000, duan lokon de $3,000, kaj trian lokon de $1,000. Pro la superforta respondo kaj nekredeblaj submetoj, ni decidis iomete pliigi la premion, kaj aljuĝi kvaran lokon kun premio de $500 ĉiu. La gajnintoj estas sube, sed nepre rigardu ĉiujn submetojn <a %(annas_archive)s>ĉi tie</a>, aŭ elŝutu nian <a %(a_2025_01_isbn_visualization_files)s>kombinitan torenton</a>. Unua loko $6,000: phiresky Ĉi tiu <a %(phiresky_github)s>submeto</a> (<a %(annas_archive_note_2951)s>Gitlab-komento</a>) estas simple ĉio, kion ni volis, kaj pli! Ni aparte ŝatis la nekredeble flekseblajn vizualigajn opciojn (eĉ subtenantajn kutimajn ombrojn), sed kun ampleksa listo de antaŭagordoj. Ni ankaŭ ŝatis kiel rapide kaj glate ĉio funkcias, la simpla efektivigo (kiu eĉ ne havas malantaŭan finon), la lerta minimapo, kaj ampleksa klarigo en ilia <a %(phiresky_github)s>blogaĵo</a>. Nekredebla laboro, kaj bone meritita gajninto! - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Niaj koroj estas plenaj de dankemo. Notindaj ideoj Nubskrapuloj por maloftaĵo Multaj glitiloj por kompari datasets, kvazaŭ vi estus diskĵokeo. Skaldrinkejo kun nombro de libroj. Belaj etikedoj. Malvarmeta defaŭlta kolorskemo kaj varmomapo. Unika mapvidaĵo kaj filtriloj Notoj, kaj ankaŭ vivaj statistikoj Vivaj statistikoj Kelkaj pliaj ideoj kaj efektivigoj, kiujn ni aparte ŝatis: Ni povus daŭrigi dum iom da tempo, sed ni haltu ĉi tie. Nepre rigardu ĉiujn alsendojn <a %(annas_archive)s>ĉi tie</a>, aŭ elŝutu nian <a %(a_2025_01_isbn_visualization_files)s>kombinitan torenton</a>. Tiel multaj alsendoj, kaj ĉiu alportas unikan perspektivon, ĉu en UI aŭ efektivigo. Ni almenaŭ integrigos la unuan lokon alsendon en nian ĉefan retejon, kaj eble kelkajn aliajn. Ni ankaŭ komencis pensi pri kiel organizi la procezon de identigado, konfirmado, kaj tiam arkivado de la plej maloftaj libroj. Pli venos pri ĉi tiu fronto. Dankon al ĉiuj kiuj partoprenis. Estas mirinde ke tiom da homoj zorgas. Facila ŝanĝado de datasets por rapidaj komparoj. Ĉiuj ISBN-oj CADAL SSNO-oj CERLALC-datuma liko DuXiu SSID-oj EBSCOhost-a eLibro-Indekso Google Books Goodreads Interreta Arkivo ISBNdb ISBN Tutmonda Registro de Eldonistoj Libby Dosieroj en la Arkivo de Anna Nexus/STC OCLC/Worldcat OpenLibrary Rusa Ŝtata Biblioteko Imperiestra Biblioteko de Trantor Dua loko $3,000: hypha “Dum perfektaj kvadratoj kaj rektanguloj estas matematike plaĉaj, ili ne provizas superan lokan proksimecon en mapiga kunteksto. Mi kredas, ke la asimetrio eneca en ĉi tiuj Hilbert aŭ klasikaj Morton ne estas difekto sed trajto. Same kiel la fame boto-forma konturo de Italio igas ĝin tuj rekonebla sur mapo, la unikaj "kapricoj" de ĉi tiuj kurboj povas servi kiel kognaj orientiloj. Ĉi tiu distingivo povas plibonigi spacan memoron kaj helpi uzantojn orientiĝi, eble faciligante trovi specifajn regionojn aŭ rimarki ŝablonojn.” Alia nekredebla <a %(annas_archive_note_2913)s>submeto</a>. Ne tiel fleksebla kiel la unua loko, sed ni fakte preferis ĝian makronivelan vizualigon super la unua loko (spaco-pleniga kurbo, limoj, etikedado, elstarigo, panoramado, kaj zomado). Komento de <a %(annas_archive_note_2971)s>Joe Davis</a> resonis kun ni: Kaj ankoraŭ multaj opcioj por vizualigado kaj redonado, same kiel nekredeble glata kaj intuicia UI. Solida dua loko! - Anna kaj la teamo (<a %(reddit)s>Reddit</a>) Antaŭ kelkaj monatoj ni anoncis <a %(all_isbns)s>$10,000 rekompencon</a> por fari la plej bonan eblan vidiĝon de niaj datumoj montrante la ISBN-spacon. Ni emfazis montri kiujn dosierojn ni jam arĥivis/ne arĥivis, kaj ni poste aldonis datumaron priskribantan kiom da bibliotekoj tenas ISBN-ojn (mezuro de maloftaĵo). Ni estis superŝutitaj de la respondo. Estis tiom da kreemo. Granda dankon al ĉiuj, kiuj partoprenis: via energio kaj entuziasmo estas infektaj! Finfine, ni volis respondi la jenajn demandojn: <strong>kiuj libroj ekzistas en la mondo, kiom ni jam arkivis, kaj sur kiuj libroj ni devus fokusiĝi poste?</strong> Estas bone vidi, ke tiom da homoj zorgas pri ĉi tiuj demandoj. Ni mem komencis per baza bildigo. En malpli ol 300kb, ĉi tiu bildo koncize reprezentas la plej grandan plene malferman "liston de libroj" iam ajn kunmetitan en la historio de la homaro: Tria loko $500 #1: maxlion En ĉi tiu <a %(annas_archive_note_2940)s>submeto</a> ni vere ŝatis la diversajn specojn de vidoj, precipe la komparajn kaj eldonistajn vidojn. Tria loko $500 #2: abetusk Kvankam ne la plej polurita UI, ĉi tiu <a %(annas_archive_note_2917)s>submeto</a> kontrolas multajn el la skatoloj. Ni aparte ŝatis ĝian komparan funkcion. Tria loko $500 #3: conundrumer0 Kiel la unua loko, ĉi tiu <a %(annas_archive_note_2975)s>submeto</a> impresis nin per sia fleksebleco. Finfine tio estas kio faras bonegan vizualigan ilon: maksimuma fleksebleco por potencaj uzantoj, dum konservante aferojn simplaj por mezaj uzantoj. Tria loko $500 #4: charelf La fina <a %(annas_archive_note_2947)s>submeto</a> por ricevi premion estas sufiĉe baza, sed havas kelkajn unikajn trajtojn, kiujn ni vere ŝatis. Ni ŝatis kiel ili montras kiom da Datasets kovras specifan ISBN kiel mezuron de populareco/fiableco. Ni ankaŭ vere ŝatis la simplecon sed efikecon de uzado de opakeca glitilo por komparoj. Gajnintoj de la $10,000 ISBN-vidiĝa rekompenco TL;DR: Ni ricevis kelkajn nekredeblajn submetojn al la $10,000 ISBN-vidiĝa rekompenco. Fono Kiel povas Arkivo de Anna atingi sian mision subteni la tutan scion de la homaro, sen scii kiuj libroj ankoraŭ ekzistas? Ni bezonas TODO-liston. Unu maniero por mapi tion estas per ISBN-nombroj, kiuj ekde la 1970-aj jaroj estis asignitaj al ĉiu publikigita libro (en plej multaj landoj). Ne ekzistas centra aŭtoritato kiu scias ĉiujn ISBN-asignojn. Anstataŭe, ĝi estas distribuita sistemo, kie landoj ricevas nombro-gamojn, kiuj poste asignas pli malgrandajn gamojn al ĉefaj eldonistoj, kiuj eble plu subdividos gamojn al malpli grandaj eldonistoj. Fine, individuaj nombroj estas asignitaj al libroj. Ni komencis mapi ISBN-ojn <a %(blog)s>antaŭ du jaroj</a> per nia skrapado de ISBNdb. Ekde tiam, ni skrapis multajn pli da fontoj de metadata, kiel <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, kaj pli. Plena listo troveblas en la paĝoj “Datasets” kaj “Torrents” en Arkivo de Anna. Ni nun havas longe la plej grandan plene malferman, facile elŝuteblan kolekton de libro-metadata (kaj tiel ISBN-oj) en la mondo. Ni <a %(blog)s>amplekse verkis</a> pri kial ni zorgas pri konservado, kaj kial ni nuntempe estas en kritika fenestro. Ni devas nun identigi rarajn, malfokusitajn, kaj unike riskatajn librojn kaj konservi ilin. Havi bonan metadata pri ĉiuj libroj en la mondo helpas tion. $10,000 rekompenco Forta konsidero estos donita al uzebleco kaj kiel bone ĝi aspektas. Montru faktan metadata por individuaj ISBN-oj kiam zomi, kiel titolo kaj aŭtoro. Pli bona spaco-pleniganta kurbo. Ekz. zigzago, irante de 0 ĝis 4 en la unua vico kaj poste reen (inverse) de 5 ĝis 9 en la dua vico — rekursive aplikita. Diversaj aŭ personecigeblaj koloraj skemoj. Specialaj vidpunktoj por kompari datasets. Manieroj por senararigi problemojn, kiel aliaj metadata kiuj ne bone kongruas (ekz. tre malsamaj titoloj). Anotado de bildoj kun komentoj pri ISBN-oj aŭ intervaloj. Ajna heuristiko por identigi maloftajn aŭ riskajn librojn. Kiaj ajn kreemaj ideoj vi povas elpensi! Kodo La kodo por generi ĉi tiujn bildojn, same kiel aliajn ekzemplojn, troviĝas en <a %(annas_archive)s>ĉi tiu dosierujo</a>. Ni elpensis kompaktan datumformaton, kun kiu ĉiuj bezonataj ISBN-informoj estas ĉirkaŭ 75MB (kunpremitaj). La priskribo de la datumformato kaj kodo por generi ĝin troviĝas <a %(annas_archive_l1244_1319)s>ĉi tie</a>. Por la rekompenco vi ne estas devigita uzi ĉi tion, sed ĝi estas probable la plej oportuna formato por komenci. Vi povas transformi niajn metadata kiel ajn vi volas (kvankam via tuta kodo devas esti malfermfonta). Ni ne povas atendi por vidi kion vi elpensas. Bonŝancon! Forku ĉi tiun repo, kaj redaktu ĉi tiun bloga afiŝo HTML (neniu alia malantaŭa sistemo krom nia Flask malantaŭa sistemo estas permesata). Faru la bildon supre glate zomebla, tiel ke vi povas zomi ĝis individuaj ISBN-oj. Alklakante ISBN-ojn devus konduki vin al metadata paĝo aŭ serĉo en Arkivo de Anna. Vi ankoraŭ devas povi ŝanĝi inter ĉiuj malsamaj datasets. Landaj gamoj kaj eldonistaj gamoj devus esti elstarigitaj kiam oni pasas super ili. Vi povas uzi ekz. <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> por landa informo, kaj nia “isbngrp” skrapado por eldonistoj (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Ĝi devas bone funkcii sur labortablo kaj poŝtelefono. Estas multe por esplori ĉi tie, do ni anoncas rekompencon por plibonigi la vidigon supre. Male al plej multaj el niaj rekompencoj, ĉi tiu estas tempolima. Vi devas <a %(annas_archive)s>sendi</a> vian malfermitkoda programo antaŭ 2025-01-31 (23:59 UTC). La plej bona submeto ricevos $6,000, dua loko estas $3,000, kaj tria loko estas $1,000. Ĉiuj rekompencoj estos aljuĝitaj uzante Monero (XMR). Sube estas la minimumaj kriterioj. Se neniu submeto plenumas la kriteriojn, ni eble ankoraŭ aljuĝos iujn rekompencojn, sed tio estos laŭ nia bontrovo. Por bonusaj punktoj (ĉi tiuj estas nur ideoj — lasu vian kreemon flugi): Vi POVAS tute foriri de la minimumaj kriterioj, kaj fari tute malsaman vizualigon. Se ĝi estas vere spektakla, tiam tio kvalifikiĝas por la rekompenco, sed laŭ nia diskreteco. Faru submetojn per afiŝado de komento al <a %(annas_archive)s>ĉi tiu afero</a> kun ligilo al via forkita repo, kuniga peto, aŭ diferenco. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ĉi tiu bildo estas 1000×800 pikseloj. Ĉiu pikselo reprezentas 2,500 ISBN-ojn. Se ni havas dosieron por ISBN, ni faras tiun pikselo pli verda. Se ni scias ke ISBN estis eldonita, sed ni ne havas kongruan dosieron, ni faras ĝin pli ruĝa. En malpli ol 300kb, ĉi tiu bildo koncize reprezentas la plej grandan plene malferman “liston de libroj” iam ajn kunmetitan en la historio de la homaro (kelkaj centoj da GB kunpremitaj plene). Ĝi ankaŭ montras: estas multe da laboro restanta en subtenado de libroj (ni nur havas 16%). Vizualigante Ĉiujn ISBN-ojn — $10,000 rekompenco ĝis 2025-01-31 Ĉi tiu bildo reprezentas la plej grandan plene malferman “liston de libroj” iam ajn kunmetitan en la historio de la homaro. Vidigi Krom la superrigarda bildo, ni ankaŭ povas rigardi individuajn datasets kiujn ni akiris. Uzu la falmenuon kaj butonojn por ŝanĝi inter ili. Estas multaj interesaj ŝablonoj por vidi en ĉi tiuj bildoj. Kial estas iu reguleco de linioj kaj blokoj, kiu ŝajnas okazi ĉe malsamaj skaloj? Kio estas la malplenaj areoj? Kial certaj datasets estas tiel grupigitaj? Ni lasos ĉi tiujn demandojn kiel ekzercon por la leganto. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konkludo Kun ĉi tiu normo, ni povas fari eldonojn pli laŭgrade, kaj pli facile aldoni novajn datumfontojn. Ni jam havas kelkajn ekscitajn eldonojn en la tubaro! Ni ankaŭ esperas, ke fariĝos pli facile por aliaj ombrobibliotekoj speguli niajn kolektojn. Finfine, nia celo estas konservi homan scion kaj kulturon por ĉiam, do ju pli da redundo des pli bone. Ekzemplo Rigardu nian lastatempan eldonon de Z-Library kiel ekzemplon. Ĝi konsistas el du kolektoj: “<span style="background: #fffaa3">zlib3_records</span>” kaj “<span style="background: #ffd6fe">zlib3_files</span>”. Ĉi tio permesas al ni aparte skrapi kaj eldoni metadata rekordojn de la realaj librodosieroj. Tiel, ni eldonis du torentojn kun metadata dosieroj: Ni ankaŭ eldonis aron da torentoj kun binaraj datumdosierujoj, sed nur por la “<span style="background: #ffd6fe">zlib3_files</span>” kolekto, entute 62: Per rulado de <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> ni povas vidi kio estas interne: En ĉi tiu kazo, ĝi estas metadata de libro kiel raportite de Z-Library. Sur la plej alta nivelo ni nur havas “aacid” kaj “metadata”, sed neniu “data_folder”, ĉar ne estas respondaj binaraj datumoj. La AACID enhavas “22430000” kiel la ĉefa ID, kiun ni povas vidi estas prenita de “zlibrary_id”. Ni povas atendi, ke aliaj AAC-oj en ĉi tiu kolekto havos la saman strukturon. Nun ni rulu <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Ĉi tio estas multe pli malgranda AAC metadata, kvankam la plejparto de ĉi tiu AAC troviĝas aliloke en binara dosiero! Finfine, ni havas “data_folder” ĉi-foje, do ni povas atendi, ke la respondaj binaraj datumoj troviĝos ĉe <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. La “metadata” enhavas la “zlibrary_id”, do ni povas facile asocii ĝin kun la responda AAC en la “zlib_records” kolekto. Ni povus asocii laŭ diversaj manieroj, ekzemple per AACID — la normo ne preskribas tion. Notu, ke ankaŭ ne necesas, ke la “metadata” kampo mem estu JSON. Ĝi povus esti ĉeno enhavanta XML aŭ ajnan alian datumformaton. Vi povus eĉ stoki metadata informojn en la asociita binara blobo, ekzemple se estas multe da datumoj. Heterogenaj dosieroj kaj metadata, kiel eble plej proksime al la originala formato. Binara datumaro povas esti servata rekte de retserviloj kiel Nginx. Heterogenaj identigiloj en la fontaj bibliotekoj, aŭ eĉ manko de identigiloj. Apartaj eldonoj de metadata kontraŭ dosieraj datumoj, aŭ nur-metadata eldonoj (ekz. nia ISBNdb eldono). Distribuo tra torentoj, kvankam kun la eblo de aliaj distribumetodoj (ekz. IPFS). Nemoveblaj rekordoj, ĉar ni devas supozi ke niaj torentoj vivos eterne. Laŭpaŝaj eldonoj / aldonendaj eldonoj. Maŝinlegeblaj kaj skribeblaj, oportune kaj rapide, precipe por nia stako (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Iom facile homa inspektado, kvankam ĉi tio estas sekundara al maŝinlegebleco. Facile semigi niajn kolektojn per norma luita semkesto. Dezajnaj celoj Ni ne zorgas pri dosieroj estantaj facile navigeblaj permane sur disko, aŭ serĉeblaj sen antaŭpretigo. Ni ne zorgas pri esti rekte kongruaj kun ekzistanta bibliotekprogramaro. Dum ĝi devus esti facila por iu ajn semigi nian kolekton uzante torentojn, ni ne atendas ke la dosieroj estu uzeblaj sen signifa teknika scio kaj sindediĉo. Nia ĉefa uzokazo estas la distribuo de dosieroj kaj asociita metadata el diversaj ekzistantaj kolektoj. Niaj plej gravaj konsideroj estas: Iuj ne-celoj: Ĉar Arkivo de Anna estas malfermfonta, ni volas rekte uzi nian formaton. Kiam ni refreŝigas nian serĉindekson, ni nur aliras publike disponeblajn vojojn, tiel ke iu ajn kiu forkas nian bibliotekon povas rapide ekfunkcii. <strong>AAC.</strong> AAC (Anna Arkivo Ujo) estas unuopa ero konsistanta el <strong>metadata</strong>, kaj laŭvole <strong>binaraj datumoj</strong>, ambaŭ el kiuj estas nemoveblaj. Ĝi havas tutmonde unikan identigilon, nomatan <strong>AACID</strong>. <strong>AACID.</strong> La formato de AACID estas ĉi tio: <code style="color: #0093ff">aacid__{kolekto}__{ISO 8601 tempstampo}__{kolekto-specifa ID}__{mallonguuid}</code>. Ekzemple, efektiva AACID kiun ni eldonis estas <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID gamo.</strong> Ĉar AACID-oj enhavas monotone kreskantajn tempstampojn, ni povas uzi tion por indiki gamojn ene de aparta kolekto. Ni uzas ĉi tiun formaton: <code style="color: blue">aacid__{kolekto}__{de_tempstampo}--{ĝis_tempstampo}</code>, kie la tempstampoj estas inkluzivaj. Ĉi tio estas kongrua kun ISO 8601 notacio. Gamoj estas kontinuaj, kaj povas interkovri, sed en kazo de interkovro devas enhavi identajn rekordojn kiel la antaŭe eldonita en tiu kolekto (ĉar AAC-oj estas nemoveblaj). Mankantaj rekordoj ne estas permesitaj. <code>{kolekto}</code>: la nomo de la kolekto, kiu povas enhavi ASCII-literojn, nombrojn, kaj substrekojn (sed ne duoblajn substrekojn). <code>{kolekto-specifa ID}</code>: kolekto-specifa identigilo, se aplikebla, ekz. la Z-Library ID. Povas esti preterlasita aŭ mallongigita. Devas esti preterlasita aŭ mallongigita se la AACID alie superus 150 signojn. <code>{ISO 8601 tempstampo}</code>: mallonga versio de la ISO 8601, ĉiam en UTC, ekz. <code>20220723T194746Z</code>. Ĉi tiu nombro devas monotone kreski por ĉiu eldono, kvankam ĝiaj precizaj semantikoj povas diferenci laŭ kolekto. Ni sugestas uzi la tempon de skrapado aŭ de generado de la ID. <code>{mallonguuid}</code>: UUID sed kunpremita al ASCII, ekz. uzante base57. Ni nuntempe uzas la <a %(github_skorokithakis_shortuuid)s>mallonguuid</a> Python-bibliotekon. <strong>Binara datumdosierujo.</strong> Dosierujo kun la binaraj datumoj de gamo de AAC-oj, por unu aparta kolekto. Ĉi tiuj havas la jenajn ecojn: La dosierujo devas enhavi datumdosierojn por ĉiuj AAC-oj ene de la specifita gamo. Ĉiu datumdosiero devas havi sian AACID kiel la dosiernomo (sen etendaĵoj). Dosierujnomo devas esti AACID gamo, prefiksita kun <code style="color: green">anna_arkivo_data__</code>, kaj sen sufikso. Ekzemple, unu el niaj efektivaj eldonoj havas dosierujon nomatan<br><code><span style="color: green">anna_arkivo_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Estas rekomendite fari ĉi tiujn dosierujojn iel ajn mastrumeblaj laŭ grandeco, ekzemple ne pli grandaj ol 100GB-1TB ĉiu, kvankam ĉi tiu rekomendo povas ŝanĝiĝi kun la tempo. <strong>Kolekto.</strong> Ĉiu AAC apartenas al kolekto, kiu laŭ difino estas listo de AAC-oj kiuj estas semantike koheraj. Tio signifas, ke se vi faras signifan ŝanĝon al la formato de la metadata, tiam vi devas krei novan kolekton. La normo <strong>Metadata dosiero.</strong> Metadata dosiero enhavas la metadata de gamo de AAC-oj, por unu aparta kolekto. Ĉi tiuj havas la jenajn ecojn: <code>data_folder</code> estas laŭvola, kaj estas la nomo de binara datumdosierujo kiu enhavas la respondajn binarajn datumojn. La dosiernomo de la respondaj binaraj datumoj ene de tiu dosierujo estas la AACID de la rekordo. Ĉiu JSON objekto devas enhavi la jenajn kampojn ĉe la plej alta nivelo: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (laŭvole). Neniuj aliaj kampoj estas permesitaj. Dosiernomo devas esti AACID gamo, prefiksita kun <code style="color: red">anna_arkivo_meta__</code> kaj sekvita de <code>.jsonl.zstd</code>. Ekzemple, unu el niaj eldonoj nomiĝas<br><code><span style="color: red">anna_arkivo_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kiel indikita de la dosieretendaĵo, la dosiertipo estas <a %(jsonlines)s>JSON Linioj</a> kunpremita kun <a %(zstd)s>Zstandard</a>. <code>metadata</code> estas arbitra metadata, laŭ la semantiko de la kolekto. Ĝi devas esti semantike kohera ene de la kolekto. La <code style="color: red">anna_arkivo_meta__</code> prefikso povas esti adaptita al la nomo de via institucio, ekz. <code style="color: red">mia_instituto_meta__</code>. <strong>“rekordoj” kaj “dosieroj” kolektoj.</strong> Laŭ kutimo, ofte estas oportune eldoni “rekordojn” kaj “dosierojn” kiel malsamajn kolektojn, por ke ili povu esti eldonitaj laŭ malsamaj horaroj, ekz. bazitaj sur skrapaj tarifoj. “Rekordo” estas nur-metadata kolekto, enhavanta informojn kiel librotitoloj, aŭtoroj, ISBN-oj, ktp., dum “dosieroj” estas la kolektoj kiuj enhavas la efektivajn dosierojn mem (pdf, epub). Fine, ni decidis pri relative simpla normo. Ĝi estas sufiĉe malstrikta, nenormiga, kaj ankoraŭ evoluanta. <strong>Torrents.</strong> La metadata dosieroj kaj binaraj datumdosierujoj povas esti kunigitaj en torentoj, kun unu torento por ĉiu metadata dosiero, aŭ unu torento por ĉiu binara datumdosierujo. La torentoj devas havi la originalan dosierujon/dosiernoman plus <code>.torrent</code> sufikson kiel sian dosiernoman. <a %(wikipedia_annas_archive)s>Arkivo de Anna</a> fariĝis longe la plej granda ombra biblioteko en la mondo, kaj la sola ombra biblioteko de sia skalo kiu estas plene malfermfonta kaj malfermdatumara. Sube estas tabelo de nia paĝo pri Datasets (iomete modifita): Ni atingis ĉi tion per tri manieroj: Spegulante ekzistantajn malfermdatumajn ombrajn bibliotekojn (kiel Sci-Hub kaj Library Genesis). Helpi ombrajn bibliotekojn kiuj volas esti pli malfermaj, sed ne havis la tempon aŭ rimedojn por fari tion (kiel la Libgen komikso-kolekto). Skrapante bibliotekojn kiuj ne volas dividi amase (kiel Z-Library). Por (2) kaj (3) ni nun mem administras konsiderindan kolekton de torentoj (centoj da TB-oj). Ĝis nun ni traktis ĉi tiujn kolektojn kiel unuopajn, signifante laŭmendan infrastrukturon kaj datuman organizon por ĉiu kolekto. Ĉi tio aldonas signifan superkoston al ĉiu eldono, kaj faras ĝin aparte malfacila fari pli laŭpaŝajn eldonojn. Tial ni decidis normigi niajn eldonojn. Ĉi tio estas teknika bloga afiŝo en kiu ni enkondukas nian normon: <strong>Konteneroj de Arkivo de Anna</strong>. Arkivo de Anna-Ujoj (AAU): normigado de eldonoj de la plej granda ombra biblioteko en la mondo Arkivo de Anna fariĝis la plej granda ombra biblioteko en la mondo, postulante ke ni normigu niajn eldonojn. 300GB+ da librokovriloj liberigitaj Fine, ni ĝojas anonci malgrandan eldonon. En kunlaboro kun la homoj, kiuj funkciigas la Libgen.rs forkon, ni dividas ĉiujn iliajn librokovrilojn per torentoj kaj IPFS. Ĉi tio distribuos la ŝarĝon de vidado de la kovriloj inter pli da maŝinoj, kaj konservos ilin pli bone. En multaj (sed ne ĉiuj) kazoj, la librokovriloj estas inkluzivitaj en la dosieroj mem, do ĉi tio estas speco de "derivita datumoj". Sed havi ĝin en IPFS estas ankoraŭ tre utila por ĉiutaga operacio de kaj la Arkivo de Anna kaj la diversaj Library Genesis forkoj. Kiel kutime, vi povas trovi ĉi tiun eldonon ĉe la Pirata Biblioteko Spegulo (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>). Ni ne ligos al ĝi ĉi tie, sed vi povas facile trovi ĝin. Espereble ni povas malstreĉi nian ritmon iomete, nun kiam ni havas decan alternativon al Z-Biblioteko. Ĉi tiu laborkvanto ne estas aparte daŭrigebla. Se vi interesiĝas helpi kun programado, servilaj operacioj, aŭ konservada laboro, certe kontaktu nin. Ankoraŭ estas multe da <a %(annas_archive)s>laboro por fari</a>. Dankon pro via intereso kaj subteno. Ŝanĝi al ElasticSearch Iuj demandoj daŭris tre longe, ĝis la punkto kie ili okupis ĉiujn malfermajn konektojn. Defaŭlte MySQL havas minimuman vortlongon, aŭ via indekso povas fariĝi vere granda. Homoj raportis ne povi serĉi "Ben Hur". Serĉo estis nur iom rapida kiam plene ŝarĝita en memoro, kio postulis, ke ni akiru pli multekostan maŝinon por funkciigi ĉi tion, plus iujn komandojn por antaŭŝarĝi la indekson ĉe starto. Ni ne povus facile etendi ĝin por konstrui novajn funkciojn, kiel pli bonan <a %(wikipedia_cjk_characters)s>tokenigon por lingvoj sen spacoj</a>, filtrado/faceting, ordigo, "ĉu vi celis" sugestoj, aŭtokompletigo, kaj tiel plu. Unu el niaj <a %(annas_archive)s>biletoj</a> estis kolekto de problemoj kun nia serĉsistemo. Ni uzis MySQL plen-tekstan serĉon, ĉar ni havis ĉiujn niajn datumojn en MySQL ĉiuokaze. Sed ĝi havis siajn limojn: Post parolado kun aro da fakuloj, ni decidis pri ElasticSearch. Ĝi ne estis perfekta (iliaj defaŭltaj "ĉu vi celis" sugestoj kaj aŭtokompletigo funkcioj estas malbonaj), sed ĝenerale ĝi estis multe pli bona ol MySQL por serĉo. Ni ankoraŭ ne estas <a %(youtube)s>tro entuziasmaj</a> pri uzado de ĝi por ajna misio-kritika datumoj (kvankam ili faris multan <a %(elastic_co)s>progreson</a>), sed ĝenerale ni estas sufiĉe kontentaj kun la ŝanĝo. Por nun, ni efektivigis multe pli rapidan serĉon, pli bonan lingvan subtenon, pli bonan relevantecan ordigon, malsamajn ordigajn opciojn, kaj filtradon laŭ lingvo/libro tipo/dosiero tipo. Se vi estas scivolema kiel ĝi funkcias, <a %(annas_archive_l140)s>rigardu</a> <a %(annas_archive_l1115)s>ĉi</a> <a %(annas_archive_l1635)s>tion</a>. Ĝi estas sufiĉe alirebla, kvankam ĝi povus uzi iom pli da komentoj… La Arkivo de Anna estas plene malferma fonta Ni kredas, ke informo devus esti libera, kaj nia propra kodo ne estas escepto. Ni eldonis ĉiujn niajn kodojn en nia private gastigita Gitlab-instanco: <a %(annas_archive)s>Programaro de Anna</a>. Ni ankaŭ uzas la problemo-spurilon por organizi nian laboron. Se vi volas engaĝiĝi kun nia disvolviĝo, ĉi tio estas bonega loko por komenci. Por doni al vi guston de la aferoj, kiujn ni laboras, rigardu nian lastatempan laboron pri plibonigoj de klientflanka agado. Ĉar ni ankoraŭ ne efektivigis paĝigon, ni ofte redonus tre longajn serĉpaĝojn, kun 100-200 rezultoj. Ni ne volis tro frue tranĉi la serĉrezultojn, sed ĉi tio signifis, ke ĝi malrapidigus iujn aparatojn. Por tio, ni efektivigis etan trukon: ni envolvis plej multajn serĉrezultojn en HTML-komentoj (<code><!-- --></code>), kaj poste skribis etan JavaScript, kiu detektus kiam rezulto devus iĝi videbla, en kiu momento ni malenvolvus la komenton: DOM "virtualigo" efektivigita en 23 linioj, neniu bezono por luksaj bibliotekoj! Tio estas la speco de rapida pragmata kodo, kiun vi finas kun limigita tempo kaj realaj problemoj, kiuj bezonas solvon. Oni raportis, ke nia serĉo nun bone funkcias sur malrapidaj aparatoj! Alia granda peno estis aŭtomatigi la konstruadon de la datumbazo. Kiam ni lanĉis, ni simple hazarde kunigis malsamajn fontojn. Nun ni volas teni ilin ĝisdatigitaj, do ni verkis aron da skriptoj por elŝuti novajn metadatajn de la du Library Genesis forkoj kaj integri ilin. La celo estas ne nur fari ĉi tion utila por nia arkivo, sed ankaŭ faciligi aferojn por iu ajn, kiu volas ludi kun ombra biblioteko metadata. La celo estus Jupyter-notlibro, kiu havas ĉiajn interesajn metadatajn disponeblajn, por ke ni povu fari pli da esplorado kiel eltrovi kian <a %(blog)s>procenton de ISBN-oj estas konservitaj por ĉiam</a>. Fine, ni renovigis nian donacadan sistemon. Vi nun povas uzi kreditkarton por rekte deponi monon en niajn kriptajn monujojn, sen vere bezoni scii ion pri kriptovalutoj. Ni daŭre monitoros kiel bone ĉi tio funkcias en praktiko, sed ĉi tio estas granda afero. Kun Z-Library malaperanta kaj ĝiaj (supozataj) fondintoj arestitaj, ni laboris senĉese por provizi bonan alternativon kun la Arkivo de Anna (ni ne ligos ĝin ĉi tie, sed vi povas serĉi ĝin en Google). Jen kelkaj el la aferoj, kiujn ni atingis lastatempe. Ĝisdatigo de Anna: plene malferma fonta arkivo, ElasticSearch, 300GB+ da librokovriloj Ni laboris senĉese por provizi bonan alternativon kun la Arkivo de Anna. Jen kelkaj el la aferoj, kiujn ni atingis lastatempe. Analizo Semantikaj duplikatoj (malsamaj skanoj de la sama libro) teorie povas esti filtritaj, sed ĝi estas malfacila. Kiam ni mane rigardis tra la bildstrioj, ni trovis tro multajn falsajn pozitivajn. Estas iuj duplikatoj nur per MD5, kio estas relative malŝparema, sed filtrante tiujn for donus al ni nur ĉirkaŭ 1% in ŝparadon. Je ĉi tiu skalo tio estas ankoraŭ ĉirkaŭ 1TB, sed ankaŭ, je ĉi tiu skalo 1TB vere ne gravas. Ni prefere ne riskus hazarde detrui datumojn en ĉi tiu procezo. Ni trovis amason da ne-libraj datumoj, kiel filmoj bazitaj sur bildstrioj. Tio ankaŭ ŝajnas malŝparema, ĉar ĉi tiuj jam estas vaste disponeblaj per aliaj rimedoj. Tamen, ni rimarkis, ke ni ne povis simple filtri filmajn dosierojn, ĉar estas ankaŭ <em>interagaj bildstrioj</em> kiuj estis publikigitaj sur la komputilo, kiujn iu registris kaj konservis kiel filmojn. Fine, ĉio, kion ni povus forigi el la kolekto, ŝparus nur kelkajn procentojn. Tiam ni rememoris, ke ni estas datumamantoj, kaj la homoj, kiuj spegulos ĉi tion, ankaŭ estas datumamantoj, do, "KION VI SIGNIFAS, FORIGI?!" :) Kiam vi ricevas 95TB enmetitajn en vian stokadan klustron, vi provas kompreni, kio eĉ estas tie… Ni faris iun analizon por vidi ĉu ni povus iom redukti la grandecon, ekzemple forigante duplikatojn. Jen kelkaj el niaj trovoj: Ni do prezentas al vi la plenan, nemodifitan kolekton. Estas multe da datumoj, sed ni esperas, ke sufiĉe da homoj zorgos semadi ĝin tamen. Kunlaboro Pro ĝia grandeco, ĉi tiu kolekto delonge estis en nia dezirlisto, do post nia sukceso kun sekurkopiado de Z-Library, ni celis ĉi tiun kolekton. Komence ni skrapis ĝin rekte, kio estis sufiĉe defia, ĉar ilia servilo ne estis en la plej bona stato. Ni akiris ĉirkaŭ 15TB ĉi tiamaniere, sed ĝi estis malrapida. Feliĉe, ni sukcesis kontakti la operacianton de la biblioteko, kiu konsentis sendi al ni ĉiujn datumojn rekte, kio estis multe pli rapida. Tamen, ĝi ankoraŭ daŭris pli ol duonon de jaro por transdoni kaj prilabori ĉiujn datumojn, kaj ni preskaŭ perdis ĉion pro diska korupto, kio signifus komenci denove. Ĉi tiu sperto igis nin kredi, ke estas grave eligi ĉi tiujn datumojn kiel eble plej rapide, por ke ili povu esti spegulitaj vaste. Ni estas nur unu aŭ du malbonŝancaj okazaĵoj for de perdi ĉi tiun kolekton por ĉiam! La kolekto Rapide moviĝi signifas, ke la kolekto estas iom neorganizita… Ni rigardu. Imagu, ke ni havas dosiersistemon (kiun ni reale disigas tra torentoj): La unua dosierujo, <code>/repository</code>, estas la pli strukturita parto de ĉi tio. Ĉi tiu dosierujo enhavas tiel nomatajn "mil dosierujojn": dosierujoj ĉiu kun mil dosieroj, kiuj estas plinombritaj en la datumbazo. Dosiersistemo <code>0</code> enhavas dosierojn kun comic_id 0–999, kaj tiel plu. Ĉi tio estas la sama skemo, kiun Library Genesis uzis por siaj fikciaj kaj nefikciaj kolektoj. La ideo estas, ke ĉiu "mil dosierujo" aŭtomate fariĝas torento tuj kiam ĝi estas plenigita. Tamen, la Libgen.li-operacianto neniam kreis torentojn por ĉi tiu kolekto, kaj tiel la mil dosierujoj verŝajne fariĝis maloportunaj, kaj cedis al "neordigitaj dosierujoj". Ĉi tiuj estas <code>/comics0</code> tra <code>/comics4</code>. Ili ĉiuj enhavas unikajn dosierujajn strukturojn, kiuj verŝajne havis sencon por kolekti la dosierojn, sed nun ne havas tro da senco por ni. Feliĉe, la metadata ankoraŭ rekte rilatas al ĉiuj ĉi tiuj dosieroj, do ilia stokada organizo sur disko efektive ne gravas! La metadata estas disponebla en la formo de MySQL-datumbazo. Ĉi tio povas esti elŝutita rekte de la Libgen.li-retejo, sed ni ankaŭ faros ĝin disponebla en torento, kune kun nia propra tabelo kun ĉiuj MD5-hashoj. <q>D-ro Barbara Gordon provas perdi sin en la ĉiutaga mondo de la biblioteko…</q> Libgen-forkoj Unue, iom da fono. Vi eble konas Library Genesis pro ilia epika librokolekto. Malpli da homoj scias, ke Library Genesis-volontuloj kreis aliajn projektojn, kiel ekzemple konsiderindan kolekton de magazinoj kaj normaj dokumentoj, plenan sekurkopion de Sci-Hub (en kunlaboro kun la fondinto de Sci-Hub, Alexandra Elbakyan), kaj efektive, grandegan kolekton de bildstrioj. Iumomente malsamaj operaciantoj de Library Genesis speguloj iris siajn apartajn vojojn, kio kaŭzis la nunan situacion havi kelkajn malsamajn "forkojn", ĉiuj ankoraŭ portante la nomon Library Genesis. La Libgen.li-forko unike havas ĉi tiun bildstrian kolekton, same kiel konsiderindan magazinan kolekton (kiun ni ankaŭ laboras pri). Monkolekto Ni publikigas ĉi tiujn datumojn en kelkaj grandaj partoj. La unua torento estas de <code>/comics0</code>, kiun ni metis en unu grandegan 12TB .tar-dosieron. Tio estas pli bona por via malmola disko kaj torenta programaro ol amaso da pli malgrandaj dosieroj. Kiel parto de ĉi tiu eldono, ni faras monkolekton. Ni celas kolekti $20,000 por kovri operaciajn kaj kontraktajn kostojn por ĉi tiu kolekto, same kiel ebligi daŭrajn kaj estontajn projektojn. Ni havas kelkajn <em>grandegajn</em> en la laboro. <em>Kiun mi subtenas per mia donaco?</em> Mallonge: ni subtenas ĉiujn sciojn kaj kulturojn de la homaro, kaj faras ilin facile alireblaj. Ĉiuj niaj kodo kaj datumoj estas malfermitaj fontoj, ni estas tute volontula projekto, kaj ni savis 125TB da libroj ĝis nun (aldone al la ekzistantaj torentoj de Libgen kaj Scihub). Finfine ni konstruas radon, kiu ebligas kaj instigas homojn trovi, skani, kaj sekurkopii ĉiujn librojn en la mondo. Ni skribos pri nia majstra plano en estonta afiŝo. :) Se vi donacas por 12-monata "Mirinda Arkivisto" membreco ($780), vi povas <strong>“adopti torenton”</strong>, signifante ke ni metos vian uzantnomon aŭ mesaĝon en la dosiernomo de unu el la torentoj! Vi povas donaci irante al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a> kaj alklakante la butonon "Donaci". Ni ankaŭ serĉas pli da volontuloj: programistoj, sekurecaj esploristoj, anonimaj komercaj fakuloj, kaj tradukistoj. Vi ankaŭ povas subteni nin provizante gastigajn servojn. Kaj kompreneble, bonvolu semadi niajn torentojn! Dankon al ĉiuj, kiuj jam tiel ĝeneroze subtenis nin! Vi vere faras diferencon. Jen la torentoj publikigitaj ĝis nun (ni ankoraŭ prilaboras la reston): Ĉiuj torentoj troveblas en <a %(wikipedia_annas_archive)s>Arkivo de Anna</a> sub "Datasets" (ni ne ligas tie rekte, do ligoj al ĉi tiu blogo ne estas forigitaj de Reddit, Twitter, ktp). De tie, sekvu la ligon al la Tor-retejo. <a %(news_ycombinator)s>Diskuti ĉe Hacker News</a> Kio sekvas? Amaso da torentoj estas bonegaj por longtempa konservado, sed ne tiom por ĉiutaga aliro. Ni laboros kun gastigaj partneroj por meti ĉiujn ĉi tiujn datumojn en la reton (ĉar Arkivo de Anna ne gastigas ion rekte). Kompreneble vi povos trovi ĉi tiujn elŝutligojn en Arkivo de Anna. Ni ankaŭ invitas ĉiujn fari aferojn kun ĉi tiuj datumoj! Helpu nin pli bone analizi ilin, dedupli ilin, meti ilin en IPFS, remiksi ilin, trejni viajn AI-modelojn kun ili, kaj tiel plu. Ĉio estas via, kaj ni ne povas atendi vidi, kion vi faros kun ĝi. Fine, kiel dirite antaŭe, ni ankoraŭ havas kelkajn grandegajn eldonojn venontajn (se <em>iu</em> povus <em>hazarde</em> sendi al ni elŝuton de <em>certa</em> ACS4-datumbazo, vi scias kie trovi nin...), same kiel konstruante la radon por sekurkopii ĉiujn librojn en la mondo. Do restu agordita, ni nur komencas. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) La plej granda ombra biblioteko de bildstrioj verŝajne estas tiu de aparta Library Genesis forko: Libgen.li. La unu administranto, kiu funkciigas tiun retejon, sukcesis kolekti frenezan kolekton de bildstrioj de pli ol 2 milionoj da dosieroj, sumante pli ol 95TB. Tamen, male al aliaj Library Genesis kolektoj, ĉi tiu ne estis disponebla amase per torentoj. Vi povis nur aliri ĉi tiujn bildstriojn individue per lia malrapida persona servilo — unuopa punkto de malsukceso. Ĝis hodiaŭ! En ĉi tiu afiŝo ni rakontos al vi pli pri ĉi tiu kolekto, kaj pri nia monkolekto por subteni pli de ĉi tiu laboro. Arkivo de Anna subtenis la plej grandan ombran bibliotekon de bildstrioj en la mondo (95TB) — vi povas helpi semadi ĝin La plej granda ombra biblioteko de bildstrioj en la mondo havis unuopan punkton de malsukceso.. ĝis hodiaŭ. Averto: ĉi tiu bloga afiŝo estas malaktuala. Ni decidis, ke IPFS ankoraŭ ne estas preta por la ĉefa tempo. Ni ankoraŭ ligos al dosieroj en IPFS el la Arkivo de Anna kiam eble, sed ni ne gastigos ĝin mem plu, nek ni rekomendas al aliaj speguli uzante IPFS. Bonvolu vidi nian Torrents-paĝon se vi volas helpi konservi nian kolekton. Metante 5,998,794 librojn en IPFS Multobligo de kopioj Reen al nia originala demando: kiel ni povas aserti konservi niajn kolektojn por ĉiam? La ĉefa problemo ĉi tie estas, ke nia kolekto <a %(torrents_stats)s>kreskas</a> rapide, per skrapado kaj malfermfontado de iuj grandegaj kolektoj (krom la mirinda laboro jam farita de aliaj malfermdatumaj ombrobibliotekoj kiel Sci-Hub kaj Library Genesis). Ĉi tiu kresko en datumoj malfaciligas speguli la kolektojn ĉirkaŭ la mondo. Datumstokado estas multekosta! Sed ni estas optimismaj, precipe kiam ni observas la jenajn tri tendencojn. La <a %(annas_archive_stats)s>totala grandeco</a> de niaj kolektoj, dum la lastaj monatoj, dividita laŭ nombro de torentaj semantoj. HDD-prezaj tendencoj el diversaj fontoj (klaku por vidi studon). <a %(critical_window_chinese)s>Ĉina versio 中文版</a>, diskutu en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Ni plukis la malaltpendantajn fruktojn Ĉi tiu sekvas rekte el niaj prioritatoj diskutataj supre. Ni preferas labori pri liberigado de grandaj kolektoj unue. Nun kiam ni certigis iujn el la plej grandaj kolektoj en la mondo, ni atendas, ke nia kresko estos multe pli malrapida. Estas ankoraŭ longa vosto de pli malgrandaj kolektoj, kaj novaj libroj estas skanitaj aŭ publikigitaj ĉiutage, sed la rapideco verŝajne estos multe pli malrapida. Ni eble ankoraŭ duobliĝos aŭ eĉ triobliĝos en grandeco, sed dum pli longa tempoperiodo. Plibonigoj de OCR. Prioritatoj Scienca kaj inĝeniera programaro Fikciaj aŭ amuzaj versioj de ĉio supre Geografiaj datumoj (ekz. mapoj, geologiaj enketoj) Internaj datumoj de korporacioj aŭ registaroj (likoj) Mezurdatenoj kiel sciencaj mezuroj, ekonomiaj datumoj, korporaciaj raportoj Metadatenaj rekordoj ĝenerale (de nefikcio kaj fikcio; de aliaj medioj, arto, homoj, ktp; inkluzive recenzojn) Nefikciaj libroj Nefikciaj magazinoj, ĵurnaloj, manlibroj Nefikciaj transskribaĵoj de paroladoj, dokumentarioj, podkastoj Organikaj datumoj kiel DNA-sekvencoj, plantosemoj, aŭ mikrobaĵoj Akademiaj artikoloj, ĵurnaloj, raportoj Sciencaj kaj inĝenieraj retejoj, interretaj diskutoj Transskribaĵoj de juraj aŭ kortumaj procedoj Unike riskas detruon (ekz. pro milito, financaj tranĉoj, procesoj, aŭ politika persekutado) Raraj Unike malfokusitaj Kial ni tiom zorgas pri artikoloj kaj libroj? Ni flankenmetu nian fundamentan kredon pri konservado ĝenerale — ni eble skribos alian afiŝon pri tio. Do kial artikoloj kaj libroj specife? La respondo estas simpla: <strong>informdenso</strong>. Por megabajto de stokado, skribita teksto stokas la plej multe da informo el ĉiuj amaskomunikiloj. Dum ni zorgas pri ambaŭ scio kaj kulturo, ni pli zorgas pri la unua. Entute, ni trovas hierarkion de informdenso kaj graveco de konservado kiu aspektas proksimume tiel: La rangigo en ĉi tiu listo estas iom arbitra — pluraj eroj estas egalaj aŭ havas malkonsentojn ene de nia teamo — kaj ni probable forgesas iujn gravajn kategoriojn. Sed ĉi tio estas proksimume kiel ni prioritatas. Iuj el ĉi tiuj eroj estas tro malsamaj de la aliaj por ke ni zorgu pri ili (aŭ jam estas prizorgataj de aliaj institucioj), kiel organikaj datumoj aŭ geografiaj datumoj. Sed plej multaj el la eroj en ĉi tiu listo estas efektive gravaj por ni. Alia granda faktoro en nia prioritato estas kiom multe riskas certa verko. Ni preferas fokusiĝi al verkoj kiuj estas: Fine, ni zorgas pri skalo. Ni havas limigitan tempon kaj monon, do ni preferus pasigi monaton savante 10,000 librojn ol 1,000 librojn — se ili estas proksimume egale valoraj kaj riskaj. <em><q>La perdita ne povas esti reakirita; sed ni savu tion, kio restas: ne per volboj kaj seruroj, kiuj baras ilin de la publika okulo kaj uzo, en transdonado de ili al la rubo de tempo, sed per tia multobligo de kopioj, kiu metos ilin preter la atingo de akcidento.</q></em><br>— Thomas Jefferson, 1791 Ombraj bibliotekoj Kodo povas esti malfermfonta en Github, sed Github kiel tuto ne povas esti facile spegulita kaj tiel konservita (kvankam en ĉi tiu aparta kazo estas sufiĉe distribuitaj kopioj de plej multaj kodaj deponejoj) Metadatenaj rekordoj povas esti libere rigardataj en la retejo Worldcat, sed ne elŝutitaj amase (ĝis ni <a %(worldcat_scrape)s>skrapis</a> ilin) Reddit estas senpaga por uzi, sed lastatempe starigis striktajn kontraŭ-skrapajn mezurojn, sekve de datum-avidaj LLM-trejnadoj (pli pri tio poste) Estas multaj organizoj kiuj havas similajn misiojn, kaj similajn prioritatojn. Efektive, estas bibliotekoj, arkivoj, laboratorioj, muzeoj, kaj aliaj institucioj taskitaj pri konservado de ĉi tiu speco. Multaj el tiuj estas bone financitaj, de registaroj, individuoj, aŭ korporacioj. Sed ili havas unu grandegan blindan punkton: la jura sistemo. Jen kuŝas la unika rolo de ombraj bibliotekoj, kaj la kialo ke la Arkivo de Anna ekzistas. Ni povas fari aferojn kiujn aliaj institucioj ne rajtas fari. Nun, ne estas (ofte) ke ni povas arkivi materialojn kiuj estas kontraŭleĝaj por konservi aliloke. Ne, estas laŭleĝe en multaj lokoj konstrui arkivon kun ajnaj libroj, artikoloj, magazinoj, kaj tiel plu. Sed kion ofte mankas al juraj arkivoj estas <strong>redundeco kaj longviveco</strong>. Ekzistas libroj, el kiuj nur unu ekzemplero ekzistas en iu fizika biblioteko ie. Ekzistas metadatenaj rekordoj gardataj de unuopa korporacio. Ekzistas gazetoj konservitaj nur sur mikrofilmo en unuopa arkivo. Bibliotekoj povas ricevi buĝetajn tranĉojn, korporacioj povas bankroti, arkivoj povas esti bombitaj kaj bruligitaj ĝis la grundo. Ĉi tio ne estas hipoteza — ĉi tio okazas ĉiam. La afero, kiun ni unike povas fari ĉe Arkivo de Anna, estas stoki multajn kopiojn de verkoj, grandskale. Ni povas kolekti artikolojn, librojn, revuojn, kaj pli, kaj distribui ilin amase. Ni nuntempe faras tion per torentoj, sed la precizaj teknologioj ne gravas kaj ŝanĝiĝos kun la tempo. La grava parto estas akiri multajn kopiojn distribuitajn tra la mondo. Ĉi tiu citaĵo de antaŭ pli ol 200 jaroj ankoraŭ sonas vera: Rapida noto pri publika domeno. Ĉar Arkivo de Anna unike fokusiĝas al agadoj, kiuj estas kontraŭleĝaj en multaj lokoj ĉirkaŭ la mondo, ni ne ĝenas pri vaste disponeblaj kolektoj, kiel publika domeno libroj. Juraj entoj ofte jam bone zorgas pri tio. Tamen, estas konsideroj, kiuj foje igas nin labori pri publike disponeblaj kolektoj: - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Stokaj kostoj daŭre falas eksponente 3. Plibonigoj en informdenso Ni nuntempe konservas librojn en la krudaj formatoj, kiujn oni donas al ni. Certe, ili estas kunpremitaj, sed ofte ili ankoraŭ estas grandaj skanaĵoj aŭ fotoj de paĝoj. Ĝis nun, la solaj opcioj por malpliigi la totalan grandecon de nia kolekto estis per pli agresema kunpremo aŭ deduplikado. Tamen, por akiri sufiĉe signifajn ŝparojn, ambaŭ estas tro perdfontaj por nia gusto. Peza kunpremo de fotoj povas fari tekston apenaŭ legebla. Kaj deduplikado postulas altan fidon pri libroj estantaj ekzakte samaj, kio ofte estas tro nepreciza, precipe se la enhavo estas sama sed la skanaĵoj estas faritaj en malsamaj okazoj. Ĉiam estis tria opcio, sed ĝia kvalito estis tiel abomena, ke ni neniam konsideris ĝin: <strong>OCR, aŭ Optika Karaktera Rekono</strong>. Ĉi tio estas la procezo de konverti fotojn en simpla teksto, uzante AI por detekti la karakterojn en la fotoj. Iloj por tio ekzistas de longe, kaj estis sufiĉe bonaj, sed "sufiĉe bonaj" ne sufiĉas por konservadaj celoj. Tamen, lastatempaj multmodalaj profundaj lernmodeloj faris ekstreme rapidan progreson, kvankam ankoraŭ je altaj kostoj. Ni atendas, ke kaj precizeco kaj kostoj draste pliboniĝos en la venontaj jaroj, ĝis la punkto, kie estos realisme apliki al nia tuta biblioteko. Kiam tio okazos, ni verŝajne ankoraŭ konservos la originalajn dosierojn, sed krome ni povus havi multe pli malgrandan version de nia biblioteko, kiun plej multaj homoj volos speguli. La surprizo estas, ke kruda teksto mem kunpremiĝas eĉ pli bone, kaj estas multe pli facile dedupliki, donante al ni eĉ pli da ŝparoj. Entute ne estas nerealisme atendi almenaŭ 5-10-oblan redukton en la totala dosiergrandeco, eble eĉ pli. Eĉ kun konservativa 5-obla redukto, ni rigardus <strong>$1,000–$3,000 en 10 jaroj eĉ se nia biblioteko triobliĝus en grandeco</strong>. En la momento de verkado, <a %(diskprices)s>diskoprezoj</a> po TB estas ĉirkaŭ $12 por novaj diskoj, $8 por uzitaj diskoj, kaj $4 por bendo. Se ni estas konservativaj kaj rigardas nur novajn diskojn, tio signifas, ke stoki petabajton kostas ĉirkaŭ $12,000. Se ni supozas, ke nia biblioteko triobliĝos de 900TB al 2.7PB, tio signifus $32,400 por speguli nian tutan bibliotekon. Aldonante elektran, koston de alia aparataro, kaj tiel plu, ni rondigu ĝin al $40,000. Aŭ kun bendo pli kiel $15,000–$20,000. Unuflanke <strong>$15,000–$40,000 por la sumo de ĉiu homa scio estas rabataĉo</strong>. Aliflanke, estas iom krute atendi tunojn da plenaj kopioj, precipe se ni ankaŭ ŝatus, ke tiuj homoj daŭre semu siajn torentojn por la profito de aliaj. Tio estas hodiaŭ. Sed progreso marŝas antaŭen: Malharddiskaj kostoj po TB estis proksimume trionigitaj dum la lastaj 10 jaroj, kaj verŝajne daŭre falos je simila rapideco. Bendo ŝajnas esti sur simila trajektorio. SSD-prezoj falas eĉ pli rapide, kaj eble superos HDD-prezojn antaŭ la fino de la jardeko. Se ĉi tio tenas, tiam post 10 jaroj ni eble rigardos nur $5,000–$13,000 por speguli nian tutan kolekton (1/3), aŭ eĉ malpli se ni kreskos malpli en grandeco. Dum ankoraŭ multe da mono, ĉi tio estos atingebla por multaj homoj. Kaj ĝi eble estos eĉ pli bona pro la sekva punkto… En la Arkivo de Anna, oni ofte demandas nin kiel ni povas aserti konservi niajn kolektojn por ĉiam, kiam la totala grandeco jam proksimiĝas al 1 Petabajto (1000 TB), kaj ankoraŭ kreskas. En ĉi tiu artikolo ni rigardos nian filozofion, kaj vidos kial la sekva jardeko estas kritika por nia misio konservi la scion kaj kulturon de la homaro. Kritika fenestro Se ĉi tiuj prognozoj estas ĝustaj, ni <strong>nur bezonas atendi kelkajn jarojn</strong> antaŭ ol nia tuta kolekto estos vaste spegulita. Tiel, laŭ la vortoj de Thomas Jefferson, "metita preter la atingo de akcidento." Bedaŭrinde, la apero de LLM-oj, kaj ilia datavora trejnado, metis multajn kopirajtulojn en defensivan pozicion. Eĉ pli ol ili jam estis. Multaj retejoj malfaciligas skrapadon kaj arkivadon, procesoj flugas ĉirkaŭe, kaj dumtempe fizikaj bibliotekoj kaj arkivoj daŭre estas neglektitaj. Ni povas nur atendi, ke ĉi tiuj tendencoj daŭre plimalboniĝos, kaj multaj verkoj perdiĝos longe antaŭ ol ili eniros la publikan domenon. <strong>Ni estas ĉe la sojlo de revolucio en konservado, sed <q>la perditaj ne povas esti reakiritaj.</q></strong> Ni havas kritikan fenestron de ĉirkaŭ 5-10 jaroj dum kiu ankoraŭ estas sufiĉe multekoste funkciigi ombran bibliotekon kaj krei multajn spegulojn ĉirkaŭ la mondo, kaj dum kiu aliro ankoraŭ ne estas tute fermita. Se ni povas transiri ĉi tiun fenestron, tiam ni efektive konservos la scion kaj kulturon de la homaro por ĉiam. Ni ne devus lasi ĉi tiun tempon malŝpari. Ni ne devus lasi ĉi tiun kritikan fenestron fermiĝi al ni. Ni iru. La kritika fenestro de ombro-bibliotekoj Kiel ni povas aserti konservi niajn kolektojn por ĉiam, kiam ili jam proksimiĝas al 1 PB? Kolekto Iom pli da informoj pri la kolekto. <a %(duxiu)s>Duxiu</a> estas granda datumbazo de skanitaj libroj, kreita de la <a %(chaoxing)s>SuperStar Digital Library Group</a>. Plejparte temas pri akademiaj libroj, skanitaj por igi ilin disponeblaj ciferece al universitatoj kaj bibliotekoj. Por nia anglalingva publiko, <a %(library_princeton)s>Princeton</a> kaj la <a %(guides_lib_uw)s>Universitato de Vaŝingtono</a> havas bonajn superrigardojn. Estas ankaŭ bonega artikolo donanta pli da fono: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (serĉu ĝin en la Arkivo de Anna). La libroj de Duxiu delonge estis piratitaj en la ĉina interreto. Ili kutime estas vendataj por malpli ol dolaro de revendistoj. Ili estas tipe distribuitaj uzante la ĉinan ekvivalenton de Google Drive, kiu ofte estis hakita por permesi pli da stokspaco. Iuj teknikaj detaloj troveblas <a %(github_duty_machine)s>ĉi tie</a> kaj <a %(github_821_github_io)s>ĉi tie</a>. Kvankam la libroj estis duonpublike distribuitaj, estas sufiĉe malfacile akiri ilin en amaso. Ni havis ĉi tion alte en nia FARU-listo, kaj asignis plurajn monatojn de plentempa laboro por ĝi. Tamen, lastatempe nekredebla, mirinda, kaj talenta volontulo kontaktis nin, dirante, ke ili jam faris ĉion ĉi — je granda kosto. Ili dividis la tutan kolekton kun ni, sen atendi ion en reveno, krom la garantio de longtempa konservado. Vere rimarkinda. Ili konsentis peti helpon ĉi tiamaniere por ricevi la kolekton OCR-itan. La kolekto enhavas 7,543,702 dosierojn. Ĉi tio estas pli ol Library Genesis nefikcio (ĉirkaŭ 5.3 milionoj). La totala dosiergrandeco estas ĉirkaŭ 359TB (326TiB) en ĝia nuna formo. Ni estas malfermitaj al aliaj proponoj kaj ideoj. Simple kontaktu nin. Rigardu la Arkivon de Anna por pli da informoj pri niaj kolektoj, konservaj klopodoj, kaj kiel vi povas helpi. Dankon! Ekzemplaj paĝoj Por pruvi al ni, ke vi havas bonan procezon, jen kelkaj ekzemplaj paĝoj por komenci, el libro pri superkonduktiloj. Via procezo devus ĝuste trakti matematikon, tabelojn, diagramojn, piednotojn, kaj tiel plu. Sendu viajn prilaboritajn paĝojn al nia retpoŝto. Se ili aspektas bone, ni sendos al vi pli private, kaj ni atendas, ke vi povos rapide funkciigi vian procezon sur tiuj ankaŭ. Kiam ni estos kontentaj, ni povas fari interkonsenton. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Ĉina versio 中文版</a>, <a %(news_ycombinator)s>Diskuti en Hacker News</a> Ĉi tio estas mallonga bloga afiŝo. Ni serĉas iun kompanion aŭ institucion por helpi nin kun OCR kaj teksto-ekstraktado por amasa kolekto, kiun ni akiris, kontraŭ ekskluziva frua aliro. Post la embargo-periodo, ni kompreneble liberigos la tutan kolekton. Altgradkvalita akademia teksto estas tre utila por trejnado de LLM-oj. Kvankam nia kolekto estas ĉina, ĉi tio devus esti eĉ utila por trejnado de anglaj LLM-oj: modeloj ŝajnas enkodigi konceptojn kaj scion sendepende de la fonta lingvo. Por tio, teksto devas esti eltirita el la skanaĵoj. Kion ricevas la Arkivo de Anna el tio? Plenteksta serĉo de la libroj por ĝiaj uzantoj. Ĉar niaj celoj kongruas kun tiuj de LLM-evoluigantoj, ni serĉas kunlaboranton. Ni pretas doni al vi <strong>ekskluzivan fruan aliron al ĉi tiu kolekto en amaso dum 1 jaro</strong>, se vi povas fari ĝustan OCR kaj teksto-ekstraktadon. Se vi pretas dividi la tutan kodon de via procezo kun ni, ni pretus embargigi la kolekton por pli longa tempo. Ekskluziva aliro por LLM-kompanioj al la plej granda kolekto de ĉinaj nefikciaj libroj en la mondo <em><strong>TL;DR:</strong> La Arkivo de Anna akiris unikan kolekton de 7.5 milionoj / 350TB da ĉinaj nefikciaj libroj — pli granda ol Library Genesis. Ni pretas doni al LLM-kompanio ekskluzivan aliron, kontraŭ altkvalita OCR kaj teksto-ekstraktado.</em> Sistemo-arkitekturo Do ni diru, ke vi trovis iujn kompaniojn, kiuj pretas gastigi vian retejon sen malaktivigi vin — ni nomu ĉi tiujn “liberec-amantaj provizantoj” 😄. Vi rapide trovos, ke gastigi ĉion kun ili estas sufiĉe multekosta, do vi eble volas trovi iujn “malmultekostajn provizantojn” kaj fari la realan gastigon tie, proksimigante tra la liberec-amantaj provizantoj. Se vi faras ĝin ĝuste, la malmultekostaj provizantoj neniam scios, kion vi gastigas, kaj neniam ricevos plendojn. Kun ĉiuj ĉi tiuj provizantoj estas risko, ke ili malaktivigos vin ĉiuokaze, do vi ankaŭ bezonas redundon. Ni bezonas ĉi tion sur ĉiuj niveloj de nia stako. Unu iom liberec-amanta kompanio, kiu metis sin en interesan pozicion, estas Cloudflare. Ili <a %(blog_cloudflare)s>argumentis</a>, ke ili ne estas gastiga provizanto, sed servaĵo, kiel ISP. Ili do ne estas submetitaj al DMCA aŭ aliaj malaktivigaj petoj, kaj plusendas ajnajn petojn al via reala gastiga provizanto. Ili iris ĝis iri al kortumo por protekti ĉi tiun strukturon. Ni do povas uzi ilin kiel alian tavolon de kaŝmemoro kaj protekto. Cloudflare ne akceptas anonimajn pagojn, do ni povas uzi nur ilian senpagan planon. Ĉi tio signifas, ke ni ne povas uzi iliajn ŝarĝ-ekvilibrigajn aŭ failover-funkciojn. Ni do <a %(annas_archive_l255)s>realigis ĉi tion mem</a> ĉe la domajna nivelo. Dum paĝoŝarĝo, la retumilo kontrolos ĉu la nuna domajno ankoraŭ disponeblas, kaj se ne, ĝi reskribas ĉiujn URL-ojn al alia domajno. Ĉar Cloudflare kaŝas multajn paĝojn, ĉi tio signifas, ke uzanto povas alveni sur nia ĉefa domajno, eĉ se la prokura servilo estas malsupre, kaj tiam ĉe la sekva klako esti movita al alia domajno. Ni ankoraŭ ankaŭ havas normalajn operaciajn zorgojn por trakti, kiel ekzemple monitorado de servila sano, registri malantaŭajn kaj antaŭajn erarojn, kaj tiel plu. Nia failover-arkitekturo permesas pli da fortikeco ankaŭ en ĉi tiu fronto, ekzemple per funkciado de tute malsama aro de serviloj sur unu el la domajnoj. Ni eĉ povas funkciigi pli malnovajn versiojn de la kodo kaj datasets sur ĉi tiu aparta domajno, en kazo kritika cimo en la ĉefa versio restas nerimarkita. Ni ankaŭ povas protekti kontraŭ Cloudflare turniĝanta kontraŭ ni, forigante ĝin de unu el la domajnoj, kiel ĉi tiu aparta domajno. Diversaj permutaĵoj de ĉi tiuj ideoj estas eblaj. Konkludo Ĝi estis interesa sperto lerni kiel starigi fortikan kaj rezisteman ombran bibliotekan serĉilon. Estas multe pli da detaloj por dividi en postaj afiŝoj, do sciigu min, kion vi ŝatus lerni pli pri! Kiel ĉiam, ni serĉas donacojn por subteni ĉi tiun laboron, do nepre kontrolu la Donacan paĝon en la Arkivo de Anna. Ni ankaŭ serĉas aliajn specojn de subteno, kiel stipendiojn, longdaŭrajn sponsorojn, alt-riskajn pagprovizantojn, eble eĉ (gustumajn!) reklamojn. Kaj se vi volas kontribui vian tempon kaj kapablojn, ni ĉiam serĉas programistojn, tradukistojn, kaj tiel plu. Dankon pro via intereso kaj subteno. Novigaj ĵetonoj Ni komencu kun nia teknologia stako. Ĝi estas intence enuiga. Ni uzas Flask, MariaDB, kaj ElasticSearch. Tio estas laŭvorte ĉio. Serĉado estas plejparte solvita problemo, kaj ni ne intencas reinventi ĝin. Cetere, ni devas elspezi niajn <a %(mcfunley)s>novigajn ĵetonojn</a> por io alia: ne esti malaktivigitaj de la aŭtoritatoj. Do kiom laŭleĝa aŭ kontraŭleĝa estas la Arkivo de Anna? Ĉi tio plejparte dependas de la jura jurisdikcio. Plej multaj landoj kredas je iu formo de kopirajto, kio signifas, ke homoj aŭ kompanioj ricevas ekskluzivan monopolon pri certaj specoj de verkoj por certa periodo de tempo. Flanke, ĉe la Arkivo de Anna ni kredas, ke kvankam estas iuj avantaĝoj, ĝenerale kopirajto estas neta negativaĵo por la socio — sed tio estas rakonto por alia fojo. Ĉi tiu ekskluziva monopolo pri certaj verkoj signifas, ke estas kontraŭleĝe por iu ajn ekster ĉi tiu monopolo rekte distribui tiujn verkojn — inkluzive nin. Sed la Arkivo de Anna estas serĉilo, kiu ne rekte distribuas tiujn verkojn (almenaŭ ne en nia klara retejo), do ni devus esti en ordo, ĉu ne? Ne ĝuste. En multaj jurisdikcioj estas ne nur kontraŭleĝe distribui kopirajtitajn verkojn, sed ankaŭ ligi al lokoj, kiuj faras tion. Klara ekzemplo de tio estas la usona DMCA-leĝo. Tio estas la plej strikta fino de la spektro. Aliflanke de la spektro povus teorie esti landoj sen kopirajtaj leĝoj entute, sed ĉi tiuj vere ne ekzistas. Preskaŭ ĉiu lando havas iun formon de kopirajta leĝo en la libroj. Devigo estas alia rakonto. Estas multaj landoj kun registaroj, kiuj ne zorgas pri devigo de kopirajta leĝo. Estas ankaŭ landoj inter la du ekstremoj, kiuj malpermesas distribui kopirajtitajn verkojn, sed ne malpermesas ligi al tiaj verkoj. Alia konsidero estas ĉe la kompanio-nivelo. Se kompanio funkcias en jurisdikcio, kiu ne zorgas pri kopirajto, sed la kompanio mem ne volas preni ajnan riskon, tiam ili povus malaktivigi vian retejon tuj kiam iu plendas pri ĝi. Fine, granda konsidero estas pagoj. Ĉar ni devas resti anonimaj, ni ne povas uzi tradiciajn pagmetodojn. Ĉi tio lasas nin kun kriptovalutoj, kaj nur malgranda subaro de kompanioj subtenas tiujn (estas virtualaj debetkartoj pagitaj per kripto, sed ili ofte ne estas akceptitaj). - Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Mi funkciigas <a %(wikipedia_annas_archive)s>la Arkivon de Anna</a>, la plej grandan malfermfontan neprofitcelan serĉilon por <a %(wikipedia_shadow_library)s>ombraj bibliotekoj</a>, kiel Sci-Hub, Library Genesis, kaj Z-Biblioteko. Nia celo estas fari scion kaj kulturon facile alireblaj, kaj finfine konstrui komunumon de homoj, kiuj kune arkivas kaj konservas <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>ĉiujn librojn en la mondo</a>. En ĉi tiu artikolo mi montros kiel ni funkciigas ĉi tiun retejon, kaj la unikajn defiojn, kiuj venas kun operaciado de retejo kun dubinda jura statuso, ĉar ne ekzistas “AWS por ombraj bonfaradoj”. <em>Ankaŭ kontrolu la fratan artikolon <a %(blog_how_to_become_a_pirate_archivist)s>Kiel fariĝi pirata arkivisto</a>.</em> Kiel funkciigi ombran bibliotekon: operacioj ĉe la Arkivo de Anna Ne ekzistas <q>AWS por ombraj bonfaradoj,</q> do kiel ni funkciigas la Arkivon de Anna? Iloj Aplika servilo: Flask, MariaDB, ElasticSearch, Docker. Evoluado: Gitlab, Weblate, Zulip. Servila administrado: Ansible, Checkmk, UFW. Cepbulajna statika gastigado: Tor, Nginx. Prokura servilo: Varnish. Ni rigardu, kiajn ilojn ni uzas por plenumi ĉion ĉi. Ĉi tio tre evoluas dum ni renkontas novajn problemojn kaj trovas novajn solvojn. Estas kelkaj decidoj, pri kiuj ni hezitis. Unu estas la komunikado inter serviloj: ni kutimis uzi Wireguard por tio, sed trovis, ke ĝi foje ĉesas transdoni ajnajn datumojn, aŭ nur transdonas datumojn en unu direkto. Tio okazis kun pluraj malsamaj Wireguard-agordoj, kiujn ni provis, kiel <a %(github_costela_wesher)s>wesher</a> kaj <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ni ankaŭ provis tuneli havenojn super SSH, uzante autossh kaj sshuttle, sed renkontis <a %(github_sshuttle)s>problemojn tie</a> (kvankam ankoraŭ ne estas klare al mi ĉu autossh suferas de TCP-super-TCP-problemoj aŭ ne — ĝi simple ŝajnas al mi kiel malglata solvo, sed eble ĝi estas fakte bona?). Anstataŭe, ni revenis al rektaj konektoj inter serviloj, kaŝante ke servilo funkcias ĉe malmultekostaj provizantoj uzante IP-filtradon kun UFW. Ĉi tio havas la malavantaĝon, ke Docker ne funkcias bone kun UFW, krom se vi uzas <code>network_mode: "host"</code>. Ĉio ĉi estas iom pli erar-ema, ĉar vi eksponos vian servilon al la interreto kun nur eta misagordo. Eble ni devus reveni al autossh — reagoj estus tre bonvenaj ĉi tie. Ni ankaŭ hezitis inter Varnish kaj Nginx. Nuntempe ni ŝatas Varnish, sed ĝi havas siajn kapricojn kaj malglatajn randojn. La sama aplikiĝas al Checkmk: ni ne amas ĝin, sed ĝi funkcias por nun. Weblate estis akceptebla sed ne mirinda — mi foje timas, ke ĝi perdos miajn datumojn kiam ajn mi provas sinkronigi ĝin kun nia git-repo. Flask estis ĝenerale bona, sed ĝi havas kelkajn strangajn kapricojn, kiuj kostis multe da tempo por senararigi, kiel agordi kutimajn domajnojn, aŭ problemojn kun ĝia SqlAlchemy-integriĝo. Ĝis nun la aliaj iloj estis bonegaj: ni ne havas seriozajn plendojn pri MariaDB, ElasticSearch, Gitlab, Zulip, Docker, kaj Tor. Ĉiuj ĉi tiuj havis kelkajn problemojn, sed nenion tro seriozan aŭ tempopostulan. Komunumo La unua defio povus esti surpriza. Ĝi ne estas teknika problemo aŭ jura problemo. Ĝi estas psikologia problemo: fari ĉi tiun laboron en la ombroj povas esti nekredeble soleca. Depende de tio, kion vi planas fari, kaj via minaca modelo, vi eble devos esti tre zorgema. Unuflanke de la spektro ni havas homojn kiel Alexandra Elbakyan*, la fondinto de Sci-Hub, kiu estas tre malferma pri siaj agadoj. Sed ŝi estas en alta risko esti arestita se ŝi vizitus okcidentan landon en ĉi tiu momento, kaj povus alfronti jardekojn da prizontempo. Ĉu tio estas risko, kiun vi pretus preni? Ni estas ĉe la alia fino de la spektro; estante tre zorgemaj ne lasi iun ajn spuron, kaj havante fortan operacian sekurecon. * Kiel menciite en HN de "ynno", Alexandra komence ne volis esti konata: "Ŝiaj serviloj estis agorditaj por elsendi detalajn erarmesaĝojn de PHP, inkluzive de plena vojo de kulpa fontdosiero, kiu estis sub dosierujo /home/<USER>" Do, uzu hazardajn uzantnomojn sur la komputiloj, kiujn vi uzas por ĉi tiuj aferoj, en kazo vi misagordas ion. Tiu sekreteco, tamen, venas kun psikologia kosto. Plej multaj homoj amas esti rekonitaj pro la laboro, kiun ili faras, kaj tamen vi ne povas preni iun ajn krediton por ĉi tio en la reala vivo. Eĉ simplaj aferoj povas esti defiaj, kiel amikoj demandantaj vin, kion vi faris (je iu punkto "ludante kun mia NAS / hejmlaboratorio" malnoviĝas). Tial estas tiel grave trovi iun komunumon. Vi povas rezigni iom da operacia sekureco konfidi al iuj tre proksimaj amikoj, kiujn vi scias, ke vi povas profunde fidi. Eĉ tiam estu zorgema ne meti ion ajn en skribon, en kazo ili devas transdoni siajn retpoŝtojn al la aŭtoritatoj, aŭ se iliaj aparatoj estas kompromititaj en iu alia maniero. Pli bone ankoraŭ estas trovi iujn samideanojn. Se viaj proksimaj amikoj interesiĝas aliĝi al vi, bonege! Alie, vi eble povos trovi aliajn interrete. Bedaŭrinde ĉi tio ankoraŭ estas niĉa komunumo. Ĝis nun ni trovis nur manplenon da aliaj, kiuj estas aktivaj en ĉi tiu spaco. Bonaj startpunktoj ŝajnas esti la forumoj de Library Genesis kaj r/DataHoarder. La Arkiva Teamo ankaŭ havas samideanojn, kvankam ili funkcias ene de la leĝo (eĉ se en iuj grizaj areoj de la leĝo). La tradiciaj "warez" kaj pirataj scenoj ankaŭ havas homojn, kiuj pensas en similaj manieroj. Ni estas malfermitaj al ideoj pri kiel kulturi komunumon kaj esplori ideojn. Bonvolu sendi mesaĝon al ni ĉe Twitter aŭ Reddit. Eble ni povus gastigi ian forumon aŭ babilejon. Unu defio estas, ke tio facile povas esti cenzurita kiam oni uzas komunajn platformojn, do ni devus gastigi ĝin mem. Estas ankaŭ kompromiso inter havi ĉi tiujn diskutojn tute publikaj (pli da ebla engaĝiĝo) kontraŭ fari ĝin privata (ne lasi eblajn "celojn" scii, ke ni intencas skrapi ilin). Ni devos pripensi tion. Sciigu nin se vi interesiĝas pri tio! Konkludo Espereble tio ĉi estas helpema por novaj pirataj arkivistoj. Ni ĝojas bonvenigi vin al ĉi tiu mondo, do ne hezitu kontakti nin. Ni konservu kiel eble plej multe de la monda scio kaj kulturo, kaj spegulu ĝin vaste kaj malproksime. Projektoj 4. Datuma elekto Ofte vi povas uzi la metadatenojn por eltrovi racian subaron de datumoj por elŝuti. Eĉ se vi fine volas elŝuti ĉiujn datumojn, povas esti utile prioritati la plej gravajn erojn unue, por kazo ke vi estu detektita kaj defendoj pliboniĝas, aŭ ĉar vi bezonus aĉeti pli da diskoj, aŭ simple ĉar io alia okazas en via vivo antaŭ ol vi povas elŝuti ĉion. Ekzemple, kolekto povus havi plurajn eldonojn de la sama baza rimedo (kiel libro aŭ filmo), kie unu estas markita kiel la plej bona kvalito. Konservi tiujn eldonojn unue havus multan sencon. Vi eble fine volos konservi ĉiujn eldonojn, ĉar en iuj kazoj la metadatenoj povus esti malĝuste etikedita, aŭ povus esti nekonataj kompromisoj inter eldonoj (ekzemple, la "plej bona eldono" povus esti plej bona en plej multaj manieroj sed pli malbona en aliaj manieroj, kiel filmo havanta pli altan rezolucion sed mankantajn subtekstojn). Vi ankaŭ povas serĉi vian metadatenan datumbazon por trovi interesajn aferojn. Kio estas la plej granda dosiero kiu estas gastigita, kaj kial ĝi estas tiel granda? Kio estas la plej malgranda dosiero? Ĉu estas interesaj aŭ neatenditaj ŝablonoj kiam temas pri certaj kategorioj, lingvoj, kaj tiel plu? Ĉu estas duplikataj aŭ tre similaj titoloj? Ĉu estas ŝablonoj al kiam datumoj estis aldonitaj, kiel unu tago en kiu multaj dosieroj estis aldonitaj samtempe? Vi ofte povas lerni multon rigardante la datumarojn en malsamaj manieroj. En nia kazo, ni deduplikis Z-Library librojn kontraŭ la md5-hashoj en Library Genesis, tiel ŝparante multan elŝutan tempon kaj diskospacon. Tamen, ĉi tio estas sufiĉe unika situacio. En plej multaj kazoj ne ekzistas ampleksaj datumbazoj de kiuj dosieroj jam estas ĝuste konservitaj de aliaj piratoj. Ĉi tio mem estas granda ŝanco por iu tie ekstere. Estus bonege havi regule ĝisdatigitan superrigardon de aferoj kiel muziko kaj filmoj kiuj jam estas vaste semitaj en torentaj retejoj, kaj tial estas malpli prioritataj por inkludi en pirataj speguloj. 6. Distribuado Vi havas la datumojn, tiel donante al vi posedon de la unua pirata spegulo de la mondo de via celo (plej verŝajne). En multaj manieroj la plej malfacila parto estas finita, sed la plej riska parto ankoraŭ estas antaŭ vi. Finfine, ĝis nun vi estis kaŝema; flugante sub la radaro. Ĉio, kion vi devis fari, estis uzi bonan VPN dum la tuta tempo, ne plenigi viajn personajn detalojn en iuj formoj (kompreneble), kaj eble uzi specialan retumilan sesion (aŭ eĉ malsaman komputilon). Nun vi devas distribui la datumojn. En nia kazo ni unue volis kontribui la librojn reen al Library Genesis, sed tiam rapide malkovris la malfacilaĵojn en tio (fikcio kontraŭ nefikcio ordigo). Do ni decidis pri distribuo uzante Library Genesis-stilajn torentojn. Se vi havas la ŝancon kontribui al ekzistanta projekto, tiam tio povus ŝpari al vi multan tempon. Tamen, ne estas multaj bone organizitaj pirataj speguloj tie ekstere nuntempe. Do ni diru, ke vi decidas distribui torentojn mem. Provu teni tiujn dosierojn malgrandaj, por ke ili estu facile spegulitaj en aliaj retejoj. Vi tiam devos semi la torentojn mem, dum vi ankoraŭ restas anonima. Vi povas uzi VPN (kun aŭ sen havenan plusendadon), aŭ pagi per miksitaj Bitmoneroj por Seedbox. Se vi ne scias, kion signifas iuj el tiuj terminoj, vi havos multon por legi, ĉar estas grave, ke vi komprenu la riskajn kompromisojn ĉi tie. Vi povas gastigi la torentajn dosierojn mem en ekzistantaj torentaj retejoj. En nia kazo, ni elektis efektive gastigi retejon, ĉar ni ankaŭ volis disvastigi nian filozofion klare. Vi povas fari tion mem en simila maniero (ni uzas Njalla por niaj domajnoj kaj gastigado, pagita per miksitaj Bitmoneroj), sed ankaŭ bonvolu kontakti nin por ke ni gastigu viajn torentojn. Ni celas konstrui ampleksan indekson de pirataj speguloj kun la tempo, se ĉi tiu ideo kaptas. Pri VPN-elekto, multe jam estis skribita pri tio, do ni nur ripetos la ĝeneralan konsilon elekti laŭ reputacio. Efektive testitaj en kortumo sen-logaj politikoj kun longaj historioj de protektado de privateco estas la plej malalta risko-opcio, laŭ nia opinio. Notu, ke eĉ kiam vi faras ĉion ĝuste, vi neniam povas atingi nul-risikon. Ekzemple, kiam vi semas viajn torentojn, tre motivita ŝtata aktoro verŝajne povas rigardi enirantajn kaj elirantajn datumfluojn por VPN-serviloj, kaj dedukti kiu vi estas. Aŭ vi povas simple erari iel. Ni verŝajne jam faris, kaj faros denove. Feliĉe, ŝtatoj ne zorgas <em>tiom</em> multe pri piratado. Unu decido por fari por ĉiu projekto estas ĉu publikigi ĝin uzante la saman identecon kiel antaŭe, aŭ ne. Se vi daŭre uzas la saman nomon, tiam eraroj en operacia sekureco de pli fruaj projektoj povus reveni por mordi vin. Sed publikigi sub malsamaj nomoj signifas, ke vi ne konstruas pli longdaŭran reputacion. Ni elektis havi fortan operacian sekurecon de la komenco por ke ni povu daŭre uzi la saman identecon, sed ni ne hezitos publikigi sub malsama nomo se ni eraras aŭ se la cirkonstancoj postulas tion. Disvastigi la vorton povas esti malfacile. Kiel ni diris, ĉi tio ankoraŭ estas niĉa komunumo. Ni origine afiŝis en Reddit, sed vere akiris atenton en Hacker News. Por nun nia rekomendo estas afiŝi ĝin en kelkaj lokoj kaj vidi kio okazas. Kaj denove, kontaktu nin. Ni ŝatus disvastigi la vorton pri pli da pirata arkivismo. 1. Elektado de domajno / filozofio Ne mankas scio kaj kultura heredaĵo por esti savita, kio povas esti superforta. Tial ofte utilas preni momenton kaj pensi pri kio via kontribuo povas esti. Ĉiu havas malsaman manieron pensi pri tio, sed jen kelkaj demandoj, kiujn vi povus demandi vin mem: En nia kazo, ni aparte zorgis pri la longtempa konservado de scienco. Ni sciis pri Library Genesis, kaj kiel ĝi estis plene spegulita multfoje per torentoj. Ni amis tiun ideon. Tiam unu tagon, unu el ni provis trovi iujn sciencajn lernolibrojn en Library Genesis, sed ne povis trovi ilin, kio metis en dubon kiom kompleta ĝi vere estis. Ni tiam serĉis tiujn lernolibrojn interrete, kaj trovis ilin en aliaj lokoj, kio plantis la semon por nia projekto. Eĉ antaŭ ol ni sciis pri la Z-Library, ni havis la ideon ne provi kolekti ĉiujn tiujn librojn permane, sed fokusiĝi al spegulaj ekzistantaj kolektoj, kaj kontribui ilin reen al Library Genesis. Kiajn kapablojn vi havas, kiujn vi povas uzi al via avantaĝo? Ekzemple, se vi estas fakulo pri reta sekureco, vi povas trovi manierojn venki IP-blokojn por sekuraj celoj. Se vi estas bonega en organizi komunumojn, tiam eble vi povas kunigi kelkajn homojn ĉirkaŭ celo. Tamen utilas scii iom da programado, se nur por konservi bonan operacian sekurecon tra ĉi tiu procezo. Kio estus alta levila areo por fokusiĝi? Se vi intencas pasigi X horojn pri pirata arkivado, tiam kiel vi povas akiri la plej grandan "eksplodon por via mono"? Kiaj estas unikaj manieroj, kiujn vi pensas pri tio? Vi eble havas iujn interesajn ideojn aŭ alirojn, kiujn aliaj eble preterlasis. Kiom da tempo vi havas por tio? Nia konsilo estus komenci malgrande kaj fari pli grandajn projektojn dum vi alkutimiĝas al ĝi, sed ĝi povas fariĝi ĉion konsumanta. Kial vi interesiĝas pri tio? Kio estas via pasio? Se ni povas akiri aron da homoj, kiuj ĉiuj arkivas la specojn de aferoj, kiujn ili specife zorgas, tio kovrus multon! Vi scios multe pli ol la averaĝa persono pri via pasio, kiel kio estas gravaj datumoj por savi, kiaj estas la plej bonaj kolektoj kaj interretaj komunumoj, kaj tiel plu. 3. Metadata elŝutado Dato aldonita/modifita: tiel vi povas reveni poste kaj elŝuti dosierojn, kiujn vi ne elŝutis antaŭe (kvankam vi ofte ankaŭ povas uzi la ID aŭ hakon por tio). Hako (md5, sha1): por konfirmi, ke vi elŝutis la dosieron ĝuste. ID: povas esti iu interna ID, sed ID-oj kiel ISBN aŭ DOI estas ankaŭ utilaj. Dosiernomo / loko Priskribo, kategorio, etikedoj, aŭtoroj, lingvo, ktp. Grando: por kalkuli kiom da diskospaco vi bezonas. Ni fariĝu iom pli teknikaj ĉi tie. Por efektive elŝuti la metadata de retejoj, ni konservis aferojn sufiĉe simplaj. Ni uzas Python-skriptojn, foje curl, kaj MySQL-datumbazon por stoki la rezultojn. Ni ne uzis iun ajn luksa elŝuta programaro kiu povas mapi kompleksajn retejojn, ĉar ĝis nun ni nur bezonis elŝuti unu aŭ du specojn de paĝoj simple numerante tra identigiloj kaj analizante la HTML. Se ne estas facile numeritaj paĝoj, tiam vi eble bezonos taŭgan rampilon kiu provas trovi ĉiujn paĝojn. Antaŭ ol vi komencas elŝuti tutan retejon, provu fari ĝin permane dum iom da tempo. Trarigardu kelkajn dekojn da paĝoj mem, por akiri senton pri kiel tio funkcias. Foje vi jam renkontos IP-blokojn aŭ alian interesan konduton ĉi tiel. La sama validas por datumelŝutado: antaŭ ol tro profundiĝi en ĉi tiun celon, certigu, ke vi efektive povas elŝuti ĝiajn datumojn efike. Por ĉirkaŭiri limigojn, estas kelkaj aferoj, kiujn vi povas provi. Ĉu estas aliaj IP-adresoj aŭ serviloj, kiuj gastigas la samajn datumojn sed ne havas la samajn limigojn? Ĉu estas iuj API-finaĵoj, kiuj ne havas limigojn, dum aliaj havas? Je kia elŝuta rapideco via IP estas blokita, kaj por kiom longe? Aŭ ĉu vi ne estas blokita sed malrapidigita? Kio se vi kreas uzantokonton, kiel tiam ŝanĝiĝas aferoj? Ĉu vi povas uzi HTTP/2 por teni konektojn malfermitaj, kaj ĉu tio pliigas la rapidecon, je kiu vi povas peti paĝojn? Ĉu estas paĝoj, kiuj listigas plurajn dosierojn samtempe, kaj ĉu la informo listigita tie estas sufiĉa? Aferoj, kiujn vi probable volas konservi, inkluzivas: Ni kutime faras ĉi tion en du etapoj. Unue ni elŝutas la krudajn HTML-dosierojn, kutime rekte en MySQL (por eviti multajn malgrandajn dosierojn, pri kiuj ni parolas pli sube). Poste, en aparta paŝo, ni trarigardas tiujn HTML-dosierojn kaj analizas ilin en realajn MySQL-tabelojn. Tiel vi ne devas re-elŝuti ĉion de nulo se vi malkovras eraron en via analiza kodo, ĉar vi povas simple reprocesi la HTML-dosierojn kun la nova kodo. Ankaŭ ofte estas pli facile paraleligi la prilaboran paŝon, tiel ŝparante iom da tempo (kaj vi povas skribi la prilaboran kodon dum la elŝutado funkcias, anstataŭ devi skribi ambaŭ paŝojn samtempe). Fine, notu ke por iuj celoj metadatenrubado estas ĉio, kio ekzistas. Estas kelkaj grandegaj metadatenkolektoj tie ekstere, kiuj ne estas ĝuste konservitaj. Titolo Elektado de domajno / filozofio: Kie vi proksimume volas fokusiĝi, kaj kial? Kiaj estas viaj unikaj pasioj, kapabloj, kaj cirkonstancoj, kiujn vi povas uzi al via avantaĝo? Elektado de celo: Kiun specifan kolekton vi spegulos? Skrapado de metadata: Katalogado de informoj pri la dosieroj, sen efektive elŝuti la (ofte multe pli grandajn) dosierojn mem. Elektado de datumoj: Bazite sur la metadata, mallarĝigi kiuj datumoj estas plej gravaj por arkivi nun. Povus esti ĉio, sed ofte estas racia maniero ŝpari spacon kaj bendolarĝon. Skrapado de datumoj: Efektive akiri la datumojn. Distribuado: Paki ĝin en torentojn, anonci ĝin ie, instigi homojn disvastigi ĝin. 5. Datumrubado Nun vi estas preta por efektive elŝuti la datumojn amase. Kiel menciite antaŭe, en ĉi tiu punkto vi jam devus mane elŝuti aron da dosieroj, por pli bone kompreni la konduton kaj limigojn de la celo. Tamen, ankoraŭ estos surprizoj por vi kiam vi efektive komencos elŝuti multajn dosierojn samtempe. Nia konsilo ĉi tie estas ĉefe teni ĝin simpla. Komencu per simple elŝuti aron da dosieroj. Vi povas uzi Python, kaj poste plivastigi al pluraj fadenoj. Sed foje eĉ pli simpla estas generi Bash-dosierojn rekte el la datumbazo, kaj poste ruli plurajn el ili en pluraj terminalaj fenestroj por skali supren. Rapida teknika truko menciinda ĉi tie estas uzi OUTFILE en MySQL, kiun vi povas skribi ie ajn se vi malŝaltas "secure_file_priv" en mysqld.cnf (kaj estu certa ankaŭ malŝalti/superregi AppArmor se vi estas en Linukso). Ni stokas la datumojn sur simplaj malmolaj diskoj. Komencu kun kio ajn vi havas, kaj plivastigu malrapide. Povas esti superforta pensi pri stokado de centoj da TB-oj da datumoj. Se tio estas la situacio, kiun vi alfrontas, simple elmetu bonan subaron unue, kaj en via anonco petu helpon en stokado de la resto. Se vi volas akiri pli da malmolaj diskoj mem, tiam r/DataHoarder havas kelkajn bonajn rimedojn pri akirado de bonaj ofertoj. Provu ne tro zorgi pri elegantaj dosiersistemoj. Estas facile fali en la kuniklotruon de starigado de aferoj kiel ZFS. Unu teknika detalo por esti konscia pri tamen, estas ke multaj dosiersistemoj ne bone traktas multajn dosierojn. Ni trovis, ke simpla solvo estas krei plurajn dosierujojn, ekzemple por malsamaj ID-gamoj aŭ hash-prefiksoj. Post elŝutado de la datumoj, estu certa kontroli la integrecon de la dosieroj uzante hash-ojn en la metadatenoj, se disponebla. 2. Elektado de celo Alirebla: ne uzas multajn tavolojn de protekto por malhelpi vin elŝuti iliajn metadata kaj datumojn. Speciala kompreno: vi havas ian specialan informon pri ĉi tiu celo, kiel vi iel havas specialan aliron al ĉi tiu kolekto, aŭ vi eltrovis kiel venki iliajn defendojn. Ĉi tio ne estas postulata (nia venonta projekto ne faras ion specialan), sed certe helpas! Granda Do, ni havas nian areon, kiun ni rigardas, nun kiun specifan kolekton ni spegulas? Estas kelkaj aferoj, kiuj faras bonan celon: Kiam ni trovis niajn sciencajn lernolibrojn en retejoj krom Library Genesis, ni provis eltrovi kiel ili atingis la interreton. Ni tiam trovis la Z-Library, kaj rimarkis, ke dum plej multaj libroj ne unue aperas tie, ili finfine finiĝas tie. Ni lernis pri ĝia rilato al Library Genesis, kaj la (financa) instiga strukturo kaj supera uzantinterfaco, kiuj ambaŭ faris ĝin multe pli kompleta kolekto. Ni tiam faris iujn antaŭajn metadata kaj datumajn elŝutojn, kaj rimarkis, ke ni povus ĉirkaŭiri iliajn IP-elŝutajn limojn, utiligante la specialan aliron de unu el niaj membroj al multaj prokurilaj serviloj. Dum vi esploras malsamajn celojn, jam gravas kaŝi viajn spurojn uzante VPN-ojn kaj forĵeteblajn retpoŝtadresojn, pri kiuj ni parolos pli poste. Unika: ne jam bone kovrita de aliaj projektoj. Kiam ni faras projekton, ĝi havas kelkajn fazojn: Ĉi tiuj ne estas tute sendependaj fazoj, kaj ofte komprenoj de pli posta fazo sendas vin reen al pli frua fazo. Ekzemple, dum skrapado de metadata vi povus rimarki, ke la celo, kiun vi elektis, havas defendajn mekanismojn preter via kapablo (kiel IP-blokoj), do vi reiras kaj trovas alian celon. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>) Tuta libroj povus esti skribitaj pri la <em>kialo</em> de cifereca konservado ĝenerale, kaj pirata arkivismo aparte, sed ni donu rapidan enkondukon por tiuj, kiuj ne estas tro konataj. La mondo produktas pli da scio kaj kulturo ol iam ajn antaŭe, sed ankaŭ pli da ĝi perdiĝas ol iam ajn antaŭe. La homaro plejparte fidas je korporacioj kiel akademiaj eldonistoj, streaming-servoj, kaj sociaj amaskomunikiloj por ĉi tiu heredaĵo, kaj ili ofte ne pruviĝis esti grandaj gardantoj. Rigardu la dokumentan filmon Digital Amnesia, aŭ vere ajnan paroladon de Jason Scott. Estas iuj institucioj, kiuj faras bonan laboron arkivante tiom kiom ili povas, sed ili estas ligitaj de la leĝo. Kiel piratoj, ni estas en unika pozicio por arkivi kolektojn, kiujn ili ne povas tuŝi, pro kopirajto-devigo aŭ aliaj restriktoj. Ni ankaŭ povas speguli kolektojn multfoje, tra la mondo, tiel pliigante la ŝancojn de ĝusta konservado. Por nun, ni ne eniros diskutojn pri la avantaĝoj kaj malavantaĝoj de intelekta proprieto, la moraleco de leĝrompo, meditadoj pri cenzuro, aŭ la afero de aliro al scio kaj kulturo. Kun ĉio tio for, ni plonĝu en la <em>kiel</em>. Ni dividos kiel nia teamo fariĝis pirataj arkivistoj, kaj la lecionojn, kiujn ni lernis laŭ la vojo. Estas multaj defioj kiam vi ekiras ĉi tiun vojaĝon, kaj espereble ni povas helpi vin tra iuj el ili. Kiel fariĝi pirata arkivisto La unua defio povus esti surpriza. Ĝi ne estas teknika problemo aŭ jura problemo. Ĝi estas psikologia problemo. Antaŭ ol ni plonĝas enen, du ĝisdatigoj pri la Pirata Biblioteka Spegulo (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>): Ni ricevis kelkajn ekstreme malavarajn donacojn. La unua estis $10k de anonima individuo, kiu ankaŭ subtenis "bookwarrior", la originalan fondinton de Library Genesis. Speciala danko al bookwarrior pro faciligado de ĉi tiu donaco. La dua estis alia $10k de anonima donacanto, kiu kontaktis nin post nia lasta eldono kaj estis inspirita helpi. Ni ankaŭ havis kelkajn pli malgrandajn donacojn. Multan dankon pro via malavara subteno. Ni havas kelkajn ekscitajn novajn projektojn en la tubaro, kiujn ĉi tio subtenos, do restu atentaj. Ni havis iujn teknikajn malfacilaĵojn kun la grandeco de nia dua eldono, sed niaj torentoj nun estas supren kaj semas. Ni ankaŭ ricevis malavaran oferton de anonima individuo por semi nian kolekton sur iliaj tre rapidaj serviloj, do ni faras specialan alŝuton al iliaj maŝinoj, post kio ĉiuj aliaj, kiuj elŝutas la kolekton, devus vidi grandan plibonigon en rapideco. Blogaĵoj Saluton, mi estas Anna. Mi kreis <a %(wikipedia_annas_archive)s>la Arkivon de Anna</a>, la plej grandan ombran bibliotekon en la mondo. Ĉi tio estas mia persona blogo, en kiu mi kaj miaj teamanoj skribas pri piratado, cifereca konservado, kaj pli. Konektiĝu kun mi en <a %(reddit)s>Reddit</a>. Notu, ke ĉi tiu retejo estas nur blogo. Ni nur gastigas niajn proprajn vortojn ĉi tie. Neniuj torentoj aŭ aliaj kopirajtigitaj dosieroj estas gastigitaj aŭ ligitaj ĉi tie. <strong>Biblioteko</strong> - Kiel plej multaj bibliotekoj, ni ĉefe fokusiĝas al skribitaj materialoj kiel libroj. Ni eble plivastigos al aliaj specoj de amaskomunikiloj en la estonteco. <strong>Spegulo</strong> - Ni estas strikte spegulo de ekzistantaj bibliotekoj. Ni fokusiĝas al konservado, ne al faciligado de serĉado kaj elŝutado de libroj (aliro) aŭ al kultivado de granda komunumo de homoj, kiuj kontribuas novajn librojn (fontado). <strong>Pirato</strong> - Ni intence malobservas la kopirajtleĝon en plej multaj landoj. Tio permesas al ni fari ion, kion laŭleĝaj entoj ne povas fari: certigi, ke libroj estas spegulitaj vaste kaj malproksime. <em>Ni ne ligas al la dosieroj de ĉi tiu blogo. Bonvolu trovi ĝin mem.</em> - Anna kaj la teamo (<a %(reddit)s>Reddit</a>) Ĉi tiu projekto (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>) celas kontribui al la konservado kaj liberigo de homa scio. Ni faras nian malgrandan kaj humilan kontribuon, sekvante la paŝojn de la granduloj antaŭ ni. La fokuso de ĉi tiu projekto estas ilustrita per ĝia nomo: La unua biblioteko, kiun ni spegulis, estas Z-Biblioteko. Ĉi tio estas populara (kaj kontraŭleĝa) biblioteko. Ili prenis la kolekton de Library Genesis kaj faris ĝin facile serĉebla. Krome, ili fariĝis tre efikaj en petado de novaj libro-kontribuoj, instigante kontribuantajn uzantojn per diversaj avantaĝoj. Ili nuntempe ne kontribuas ĉi tiujn novajn librojn reen al Library Genesis. Kaj male al Library Genesis, ili ne faciligas speguladon de sia kolekto, kio malhelpas vastan konservadon. Ĉi tio estas grava por ilia komerca modelo, ĉar ili ŝargas monon por aliro al sia kolekto en amaso (pli ol 10 libroj tage). Ni ne faras moralajn juĝojn pri postulado de mono por amasa aliro al kontraŭleĝa kolekto de libroj. Estas sendube, ke la Z-Biblioteko sukcesis plivastigi aliron al scio kaj akiri pli da libroj. Ni estas simple ĉi tie por fari nian parton: certigi la longdaŭran konservadon de ĉi tiu privata kolekto. Ni ŝatus inviti vin helpi konservi kaj liberigi homan scion elŝutante kaj semante niajn torentojn. Vidu la projekton paĝo por pli da informoj pri kiel la datumoj estas organizitaj. Ni ankaŭ tre ŝatus inviti vin kontribui viajn ideojn pri kiuj kolektoj speguli sekve, kaj kiel fari tion. Kune ni povas atingi multon. Ĉi tio estas nur malgranda kontribuo inter senfinaj aliaj. Dankon, pro ĉio, kion vi faras. Enkonduko al la Spegulo de la Pirata Biblioteko: Konservante 7TB da libroj (kiuj ne estas en Libgen) 10% of la skriba heredaĵo de la homaro konservita por ĉiam <strong>Google.</strong> Finfine, ili faris ĉi tiun esploron por Google Books. Tamen, ilia metadata ne estas alirebla en amaso kaj sufiĉe malfacile skrapebla. <strong>Diversaj individuaj bibliotekaj sistemoj kaj arkivoj.</strong> Estas bibliotekoj kaj arkivoj kiuj ne estis indeksitaj kaj agregitaj de iu el la supraj, ofte ĉar ili estas subfinancitaj, aŭ pro aliaj kialoj ne volas dividi siajn datumojn kun Open Library, OCLC, Google, kaj tiel plu. Multaj el ĉi tiuj havas ciferecajn rekordojn alireblajn tra la interreto, kaj ili ofte ne estas tre bone protektitaj, do se vi volas helpi kaj amuziĝi lernante pri strangaj bibliotekaj sistemoj, ĉi tiuj estas bonegaj startpunktoj. <strong>ISBNdb.</strong> Ĉi tio estas la temo de ĉi tiu bloga afiŝo. ISBNdb skrapas diversajn retejojn por libro-metadata, precipe prezo-datumojn, kiujn ili poste vendas al librovendistoj, por ke ili povu prezi siajn librojn laŭ la resto de la merkato. Ĉar ISBN-oj estas sufiĉe universalaj nuntempe, ili efike konstruis “retpaĝon por ĉiu libro”. <strong>Open Library.</strong> Kiel menciite antaŭe, ĉi tio estas ilia tuta misio. Ili akiris grandegajn kvantojn da bibliotekaj datumoj de kunlaborantaj bibliotekoj kaj naciaj arkivoj, kaj daŭre faras tion. Ili ankaŭ havas volontulajn bibliotekistojn kaj teknikan teamon kiu provas deduplikigi rekordojn, kaj etikedi ilin per ĉiuspecaj metadata. Plej bone, ilia datuma aro estas tute malferma. Vi povas simple <a %(openlibrary)s>elŝuti ĝin</a>. <strong>WorldCat.</strong> Ĉi tio estas retejo administrata de la neprofitcela OCLC, kiu vendas bibliotekajn mastrumajn sistemojn. Ili agregas libro-metadata el multaj bibliotekoj, kaj faras ĝin disponebla tra la retejo WorldCat. Tamen, ili ankaŭ enspezas vendante ĉi tiujn datumojn, do ĝi ne estas disponebla por amasa elŝuto. Ili havas iujn pli limigitajn amasajn datumarojn disponeblajn por elŝuto, en kunlaboro kun specifaj bibliotekoj. 1. Por iu racia difino de "por ĉiam". ;) 2. Kompreneble, la skriba heredaĵo de la homaro estas multe pli ol libroj, precipe nuntempe. Por la celo de ĉi tiu afiŝo kaj niaj lastatempaj eldonoj ni fokusiĝas al libroj, sed niaj interesoj etendiĝas pli malproksimen. 3. Estas multe pli kion oni povas diri pri Aaron Swartz, sed ni nur volis mencii lin mallonge, ĉar li ludas pivotan rolon en ĉi tiu rakonto. Dum la tempo pasas, pli da homoj eble renkontos lian nomon por la unua fojo, kaj poste povos mem plonĝi en la kuniklotruon. <strong>Fizikaj kopioj.</strong> Kompreneble ĉi tio ne estas tre helpema, ĉar ili estas nur duplikatoj de la sama materialo. Estus mojose se ni povus konservi ĉiujn notojn, kiujn homoj faras en libroj, kiel la famaj “griboj en la marĝenoj” de Fermat. Sed bedaŭrinde, tio restos revo de arkivisto. <strong>“Eldonaĵoj”.</strong> Ĉi tie vi kalkulas ĉiun unikan version de libro. Se io pri ĝi estas malsama, kiel malsama kovrilo aŭ malsama antaŭparolo, ĝi kalkulas kiel malsama eldonaĵo. <strong>Dosieroj.</strong> Kiam oni laboras kun ombro-bibliotekoj kiel Library Genesis, Sci-Hub, aŭ Z-Library, estas aldona konsidero. Povas esti pluraj skanaĵoj de la sama eldonaĵo. Kaj homoj povas fari pli bonajn versiojn de ekzistantaj dosieroj, skanante la tekston uzante OCR, aŭ korektante paĝojn kiuj estis skanitaj laŭ angulo. Ni volas kalkuli ĉi tiujn dosierojn kiel unu eldonaĵon, kio postulus bonan metadata, aŭ deduplikadon uzante dokumentajn similecajn mezurojn. <strong>“Verkoj”.</strong> Ekzemple “Harry Potter kaj la Ĉambro de Sekretoj” kiel logika koncepto, ampleksante ĉiujn versiojn de ĝi, kiel malsamajn tradukojn kaj represojn. Ĉi tio estas speco de utila difino, sed povas esti malfacile desegni la linion de kio kalkulas. Ekzemple, ni probable volas konservi malsamajn tradukojn, kvankam represoj kun nur malgrandaj diferencoj eble ne estas tiel gravaj. - Anna kaj la teamo (<a %(reddit)s>Reddit</a>) Kun la Spegulo de la Pirata Biblioteko (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>), nia celo estas preni ĉiujn librojn en la mondo, kaj konservi ilin por ĉiam.<sup>1</sup> Inter niaj Z-Biblioteko torentoj, kaj la originalaj Library Genesis torentoj, ni havas 11,783,153 dosierojn. Sed kiom estas tio, vere? Se ni ĝuste dedupliki tiujn dosierojn, kiun procenton de ĉiuj libroj en la mondo ni konservis? Ni vere ŝatus havi ion tian: Ni komencu kun iuj krudaj nombroj: En ambaŭ Z-Library/Libgen kaj Open Library estas multe pli da libroj ol unikaj ISBN-oj. Ĉu tio signifas ke multaj el tiuj libroj ne havas ISBN-ojn, aŭ ĉu la ISBN-metadata simple mankas? Ni probable povas respondi ĉi tiun demandon per kombinaĵo de aŭtomata kongruado bazita sur aliaj atributoj (titolo, aŭtoro, eldonisto, ktp), alportante pli da datumfontoj, kaj eltirante ISBN-ojn el la faktaj libro-skanadoj mem (en la kazo de Z-Library/Libgen). Kiom da tiuj ISBN-oj estas unikaj? Ĉi tio estas plej bone ilustrita per Venn-diagramo: Por esti pli preciza: Ni estis surprizitaj pri kiom malmulte da interkovro ekzistas! ISBNdb havas grandegan kvanton da ISBN-oj kiuj ne aperas en Z-Library aŭ Open Library, kaj la sama validas (en pli malgranda sed ankoraŭ konsiderinda grado) por la aliaj du. Tio levas multajn novajn demandojn. Kiom multe helpus aŭtomata kongruado en etikedado de la libroj kiuj ne estis etikedataj per ISBN-oj? Ĉu estus multaj kongruoj kaj sekve pliigita interkovro? Ankaŭ, kio okazus se ni aldonus 4-an aŭ 5-an datenserion? Kiom da interkovro ni tiam vidus? Ĉi tio donas al ni deirpunkton. Ni nun povas rigardi ĉiujn ISBN-ojn kiuj ne estis en la datenserio de Z-Library, kaj kiuj ankaŭ ne kongruas kun la kampoj de titolo/aŭtoro. Tio povas doni al ni manieron konservi ĉiujn librojn en la mondo: unue per skrapado de la interreto por skanoj, poste per eliro en la realan vivon por skani librojn. La lasta eĉ povus esti homamas-financita, aŭ movita de "rekompencoj" de homoj kiuj ŝatus vidi specifajn librojn ciferecigitaj. Ĉio tio estas rakonto por alia tempo. Se vi volas helpi kun iu ajn el ĉi tio — plia analizo; skrapado de pli da metadata; trovado de pli da libroj; OCR-ado de libroj; fari tion por aliaj domajnoj (ekz. artikoloj, aŭdlibroj, filmoj, televidaj programoj, revuoj) aŭ eĉ fari iujn el ĉi tiuj datumoj disponeblaj por aferoj kiel ML / granda lingvomodela trejnado — bonvolu kontakti min (<a %(reddit)s>Reddit</a>). Se vi estas specife interesita pri la datumanalizo, ni laboras por fari niajn datenseriojn kaj skriptojn disponeblaj en pli facile uzebla formato. Estus bonege se vi povus simple forkigi notlibron kaj komenci ludi kun tio. Fine, se vi volas subteni ĉi tiun laboron, bonvolu konsideri fari donacon. Ĉi tio estas tute volontula operacio, kaj via kontribuo faras grandegan diferencon. Ĉiu peco helpas. Por nun ni akceptas donacojn en kripto; vidu la paĝon Donaci en la Arkivo de Anna. Por procento, ni bezonas denominatoron: la totala nombro de libroj iam ajn publikigitaj.<sup>2</sup> Antaŭ la malapero de Google Books, inĝeniero en la projekto, Leonid Taycher, <a %(booksearch_blogspot)s>provis taksi</a> ĉi tiun nombron. Li elpensis — ŝerce — 129,864,880 (“almenaŭ ĝis dimanĉo”). Li taksis ĉi tiun nombron per konstruado de unuigita datumbazo de ĉiuj libroj en la mondo. Por tio, li kunigis malsamajn Datasets kaj poste kunfandis ilin diversmaniere. Kiel rapida flanka noto, estas alia persono kiu provis katalogi ĉiujn librojn en la mondo: Aaron Swartz, la forpasinta cifereca aktivulo kaj kunfondinto de Reddit.<sup>3</sup> Li <a %(youtube)s>komencis Open Library</a> kun la celo de “unu retpaĝo por ĉiu libro iam ajn publikigita”, kombinante datumojn el multaj diversaj fontoj. Li finfine pagis la plej altan prezon por sia laboro pri cifereca konservado kiam li estis procesigita pro amasa elŝutado de akademiaj artikoloj, kio kondukis al lia memmortigo. Nepre dirite, ĉi tio estas unu el la kialoj kial nia grupo estas pseŭdonima, kaj kial ni estas tre zorgemaj. Open Library ankoraŭ heroike estas administrata de homoj ĉe la Internet Archive, daŭrigante la heredaĵon de Aaron. Ni revenos al ĉi tio poste en ĉi tiu afiŝo. En la bloga afiŝo de Google, Taycher priskribas kelkajn el la defioj kun taksado de ĉi tiu nombro. Unue, kio konsistigas libron? Estas kelkaj eblaj difinoj: “Eldonaĵoj” ŝajnas la plej praktika difino de kio estas “libroj”. Oportune, ĉi tiu difino ankaŭ estas uzata por asigni unikajn ISBN-nombrojn. ISBN, aŭ Internacia Normo-Libro-Nombro, estas ofte uzata por internacia komerco, ĉar ĝi estas integrita kun la internacia strekokoda sistemo (”Internacia Artikola Nombro”). Se vi volas vendi libron en vendejoj, ĝi bezonas strekokodon, do vi ricevas ISBN. La bloga afiŝo de Taycher mencias ke dum ISBN-oj estas utilaj, ili ne estas universalaj, ĉar ili estis vere adoptitaj nur en la mez-sepdekaj, kaj ne ĉie en la mondo. Tamen, ISBN estas probable la plej vaste uzata identigilo de libroeldonaĵoj, do ĝi estas nia plej bona startpunkto. Se ni povas trovi ĉiujn ISBN-ojn en la mondo, ni ricevas utilan liston de kiuj libroj ankoraŭ bezonas esti konservitaj. Do, kie ni ricevas la datumojn? Estas kelkaj ekzistantaj klopodoj kiuj provas kompili liston de ĉiuj libroj en la mondo: En ĉi tiu afiŝo, ni ĝojas anonci malgrandan eldonon (kompare al niaj antaŭaj Z-Library eldonoj). Ni skrapis plejparton de ISBNdb, kaj faris la datumojn disponeblaj por torentado en la retejo de la Pirate Library Mirror (EDIT: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>; ni ne ligos ĝin ĉi tie rekte, nur serĉu ĝin). Ĉi tiuj estas ĉirkaŭ 30.9 milionoj da rekordoj (20GB kiel <a %(jsonlines)s>JSON Linioj</a>; 4.4GB kunpremita). Sur ilia retejo ili asertas ke ili fakte havas 32.6 milionojn da rekordoj, do ni eble iel mankis iujn, aŭ <em>ili</em> povus fari ion malĝuste. Ĉiuokaze, por nun ni ne dividos precize kiel ni faris ĝin — ni lasos tion kiel ekzercon por la leganto. ;-) Kion ni dividos estas iuj preliminaj analizoj, por provi alproksimiĝi al taksado de la nombro de libroj en la mondo. Ni rigardis tri datumarojn: ĉi tiu nova ISBNdb datuma aro, nia originala eldono de metadata kiun ni skrapis de la ombro-biblioteko Z-Library (kiu inkluzivas Library Genesis), kaj la datuma elŝuto de Open Library. ISBNdb dump, aŭ Kiom da Libroj Estas Konservitaj Por Ĉiam? Se ni ĝuste dedupliki la dosierojn de ombraj bibliotekoj, kiun procenton de ĉiuj libroj en la mondo ni konservis? Ĝisdatigoj pri <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>, la plej granda vere malferma biblioteko en la homa historio. <em>WorldCat-rediseño</em> Datumoj <strong>Formato?</strong> <a %(blog)s>Konteneroj de Arkivo de Anna (AAC)</a>, kiuj estas esence <a %(jsonlines)s>JSON Linioj</a> kunpremitaj per <a %(zstd)s>Zstandard</a>, plus kelkaj normigitaj semantikoj. Ĉi tiuj konteneroj envolvas diversajn tipojn de rekordoj, bazitaj sur la malsamaj skrapoj, kiujn ni deplojis. Antaŭ unu jaro, ni <a %(blog)s>ekiris</a> por respondi ĉi tiun demandon: <strong>Kia procento de libroj estis permanente konservita de ombraj bibliotekoj?</strong> Rigardu kelkajn bazajn informojn pri la datumoj: Kiam libro eniras en malfermdatan ombran bibliotekon kiel <a %(wikipedia_library_genesis)s>Library Genesis</a>, kaj nun <a %(wikipedia_annas_archive)s>la Arkivo de Anna</a>, ĝi estas spegulita tra la tuta mondo (per torentoj), tiel praktike konservante ĝin por ĉiam. Por respondi la demandon pri kiu procento de libroj estis konservita, ni bezonas scii la denominatoron: kiom da libroj ekzistas entute? Kaj ideale ni ne nur havas nombron, sed ankaŭ faktan metadata. Tiam ni povas ne nur kongrui ilin kontraŭ ombraj bibliotekoj, sed ankaŭ <strong>krei TODO-liston de restantaj libroj por konservi!</strong> Ni povus eĉ komenci revi pri homamasigita klopodo por trairi ĉi tiun TODO-liston. Ni skrapis <a %(wikipedia_isbndb_com)s>ISBNdb</a>, kaj elŝutis la <a %(openlibrary)s>Open Library dataseton</a>, sed la rezultoj estis malsatisfaj. La ĉefa problemo estis, ke ne estis granda interkovro de ISBN-oj. Vidu ĉi tiun Venn-diagramon el <a %(blog)s>nia bloga afiŝo</a>: Ni estis tre surprizitaj pri kiom malmulte da interkovro estis inter ISBNdb kaj Open Library, kiuj ambaŭ libere inkluzivas datumojn el diversaj fontoj, kiel retaj skrapoj kaj bibliotekaj rekordoj. Se ili ambaŭ bone faras sian laboron trovante plej multajn ISBN-ojn tie ekstere, iliaj cirkloj certe havus konsiderindan interkovron, aŭ unu estus subaro de la alia. Tio igis nin demandi, kiom da libroj falas <em>komplete ekster tiuj ĉi cirkloj</em>? Ni bezonas pli grandan datumbazon. Tiam ni celis la plej grandan libran datumbazon en la mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Ĉi tio estas proprieta datumbazo de la neprofitcela <a %(wikipedia_oclc)s>OCLC</a>, kiu kolektas metadata-rekordojn el bibliotekoj tra la tuta mondo, kontraŭ donado al tiuj bibliotekoj aliron al la plena dataseto, kaj montrado de ili en la serĉrezultoj de finuzantoj. Kvankam OCLC estas neprofitcela, ilia komerca modelo postulas protekti sian datumbazon. Nu, ni bedaŭras diri, amikoj ĉe OCLC, ni donas ĉion for. :-) Dum la pasinta jaro, ni zorge skrapis ĉiujn WorldCat-rekordojn. Komence, ni havis bonŝancan paŭzon. WorldCat ĵus lanĉis sian kompletan retejan rediseñon (en aŭg 2022). Ĉi tio inkluzivis konsiderindan revizion de iliaj malantaŭaj sistemoj, enkondukante multajn sekurecajn mankojn. Ni tuj kaptis la ŝancon, kaj povis skrapi centojn da milionoj (!) da rekordoj en nur kelkaj tagoj. Post tio, sekurecaj mankoj estis malrapide riparitaj unu post la alia, ĝis la lasta, kiun ni trovis, estis flikita antaŭ ĉirkaŭ monato. Tiam ni havis preskaŭ ĉiujn rekordojn, kaj nur celis iomete pli altkvalitajn rekordojn. Do ni sentis, ke estas tempo por eldoni! 1.3B WorldCat skrapado <em><strong>TL;DR:</strong> La Arkivo de Anna skrapis la tutan WorldCat (la plej granda biblioteka metadata kolekto en la mondo) por fari TODO-liston de libroj, kiuj bezonas esti konservitaj.</em> WorldCat Averto: ĉi tiu bloga afiŝo estas malaktuala. Ni decidis, ke IPFS ankoraŭ ne estas preta por la ĉefa tempo. Ni ankoraŭ ligos al dosieroj en IPFS el la Arkivo de Anna kiam eble, sed ni ne gastigos ĝin mem plu, nek ni rekomendas al aliaj speguli uzante IPFS. Bonvolu vidi nian Torrents-paĝon se vi volas helpi konservi nian kolekton. Helpu semadi Z-Bibliotekon en IPFS Elŝuto de Partnera Servilo SciDB Ekstera prunto Ekstera prunto (presado malebligita) Ekstera elŝuto Esploru metadatenojn Enhavita en torentoj Reen  (+%(num)s bonuso) nepagitaj pagita nuligita eksvalidiĝis atendante konfirmon de Anna nevalida La teksto sube daŭras en la angla. Iru Restarigi Antaŭen Lasta Se via retpoŝtadreso ne funkcias en la forumoj de Libgen, ni rekomendas uzi <a %(a_mail)s>Proton Mail</a> (senpaga). Vi ankaŭ povas <a %(a_manual)s>mane peti</a> ke via konto estu aktivigita. (povas postuli <a %(a_browser)s>retumilan konfirmon</a> — senlimaj elŝutoj!) Rapida Partnera Servilo #%(number)s (rekomendita) (ete pli rapida sed kun atendolisto) (sen retumila kontrolo postulata) (sen retumila kontrolo aŭ atendolistoj) (sen atendolisto, sed povas esti tre malrapida) Malrapida Partnera Servilo #%(number)s Aŭdlibro Komikso Libro (fikcio) Libro (nefikcio) Libro (nekonata) Revua artikolo Revuo Muzika partituro Alia Normdokumentoj Ne ĉiuj paĝoj povis esti konvertitaj al PDF Markita kiel rompita en Libgen.li Ne videbla en Libgen.li Ne videbla en Libgen.rs Fiction Ne videbla en Libgen.rs Nefikcio Ekzekuto de exiftool malsukcesis sur ĉi tiu dosiero Markita kiel “malbona dosiero” en Z-Library Mankas en Z-Library Markita kiel “spamo” en Z-Library Dosiero ne povas esti malfermita (ekz. koruptita dosiero, DRM) Aŭtorrajta aserto Elŝutaj problemoj (ekz. ne povas konekti, erarmesaĝo, tre malrapida) Malkorekta metadata (ekz. titolo, priskribo, kovrilbildo) Aliaj Malbona kvalito (ekz. formatproblemoj, malbona skana kvalito, mankantaj paĝoj) Spamo / dosiero devus esti forigita (ekz. reklamado, ofenda enhavo) %(amount)s (%(amount_usd)s) %(amount)s entute %(amount)s (%(amount_usd)s) entute Brila Bibliomano Bonŝanca Bibliotekisto Brila Bibliomaniulo Mirinda Arkivisto Bonusaj elŝutoj Cerlalc Ĉeĥa metadateno DuXiu 读秀 EBSCOhost eBook Indekso Google Books Goodreads HathiTrust IA IA Kontrolita Cifereca Pruntepreno ISBNdb ISBN GRP Libgen.li Ekskludante “scimag” Libgen.rs Nefikcio kaj Fikcio Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Rusa Ŝtata Biblioteko Sci-Hub Per Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Alŝutoj al AA Z-Library Z-Library Ĉina Titolo, aŭtoro, DOI, ISBN, MD5, … Serĉi Aŭtoro Priskribo kaj metadatenaj komentoj Eldono Originala dosiernomo Eldonejo (serĉu specifan kampon) Titolo Eldonjaro Teknikaj detaloj Ĉi tiu monero havas pli altan minimumon ol kutime. Bonvolu elekti alian daŭron aŭ alian moneron. Peto ne povis esti kompletigita. Bonvolu reprovi post kelkaj minutoj, kaj se ĝi daŭre okazas, kontaktu nin ĉe %(email)s kun ekrankopio. Okazis nekonata eraro. Bonvolu kontakti nin ĉe %(email)s kun ekrankopio. Eraro en pagoprocezo. Bonvolu atendi momenton kaj reprovi. Se la problemo daŭras pli ol 24 horojn, bonvolu kontakti nin ĉe %(email)s kun ekrankopio. Ni organizas monkolekton por <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">subteni</a> la plej grandan ombran bibliotekon de bildstrioj en la mondo. Dankon pro via subteno! <a href="/donate">Donaci.</a> Se vi ne povas donaci, konsideru subteni nin dirante al viaj amikoj, kaj sekvante nin en <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, aŭ <a href="https://t.me/annasarchiveorg">Telegram</a>. Ne retpoŝtu al ni por <a %(a_request)s>peti librojn</a><br>aŭ malgrandajn (<10k) <a %(a_upload)s>alŝutojn</a>. La arkivo de Anna DMCA / asertoj pri kopirajto Restu en kontakto Reddit Alternativoj SLUM (%(unaffiliated)s) neafiliita La Arkivo de Anna bezonas vian helpon! Se vi donacas nun, vi ricevas <strong>duoblan</strong> la nombron de rapidaj elŝutoj. Multaj provas faligi nin, sed ni kontraŭbatalas. Se vi donacas ĉi-monate, vi ricevas <strong>duoble</strong> la nombron de rapidaj elŝutoj. Valida ĝis la fino de ĉi tiu monato. Savi homan scion: bonega feria donaco! Membrecoj estos plilongigitaj laŭe. Partneraj serviloj estas neatingeblaj pro fermoj de gastigado. Ili devus esti denove funkciantaj baldaŭ. Por pligrandigi la rezistecon de la Arkivo de Anna, ni serĉas volontulojn por prizorgi spegulojn. Ni havas novan donacmetodon disponeblan: %(method_name)s. Bonvolu konsideri %(donate_link_open_tag)sdonaci</a> — ne estas malmultekoste funkciigi ĉi tiun retejon, kaj via donaco vere faras diferencon. Tre dankon. Referu amikon, kaj ambaŭ vi kaj via amiko ricevos %(percentage)s%% bonusajn rapidajn elŝutojn! Surprizu amaton, donu al ili konton kun membreco. La perfekta donaco por Valentintago! Lerni pli… Konto Aktiveco Altnivela La Blogo de Anna ↗ La Arkivo de Anna ↗ beta Koda Esploristo Datasets Donaci Elŝutitaj dosieroj Oftaj Demandoj Hejmo Plibonigi metadatenojn LLM-datenoj Ensaluti / Registri Miaj donacoj Publika profilo Serĉi Sekureco Torentoj Traduki ↗ Volontulado & Recompencoj Lastatempaj elŝutoj: 📚&nbsp;La plej granda malfermfonta malfermdatuma biblioteko en la mondo. ⭐️&nbsp;Speguloj de Sci-Hub, Library Genesis, Z-Library, kaj pli. 📈&nbsp;%(book_any)s libroj, %(journal_article)s artikoloj, %(book_comic)s bildstrioj, %(magazine)s magazinoj — konservitaj por ĉiam.  kaj  kaj pli DuXiu Internet Archive Pruntbiblioteko LibGen La pli granda malferma biblioteko de la homara historio. 📈&nbsp;%(book_count)s&nbsp;libroj, %(paper_count)s&nbsp;paperoj— konservita por ĉiam. ⭐️&nbsp;Ni havas kopion de %(libraries)s. Ni skrapas kaj malfermfonte %(scraped)s. Ĉiuj niaj kodo kaj datumoj estas tute malfermitaj. OpenLib Sci-Hub ,  📚 La plej granda malfermfonta malfermdatuma biblioteko en la mondo.<br>⭐️ Speguloj de Scihub, Libgen, Zlib, kaj pli. Z-Lib La Arkivo de Anna Nevalida Peto. Vizitu %(websites)s. La plej granda malfermfonta malfermdatuma biblioteko en la mondo. Speguloj de Sci-Hub, Library Genesis, Z-Library, kaj pli. Serĉu en la Arkivo de Anna La Arkivo de Anna Bonvolu refreŝigi por reprovi. <a %(a_contact)s>Kontaktu nin</a> se la problemo persistas dum pluraj horoj. 🔥 Problemo ŝarĝante ĉi tiun paĝon <li>1. Sekvu nin ĉe <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, aŭ <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Diskonigu pri Arkivo de Anna ĉe Twitter, Reddit, Tiktok, Instagram, en via loka kafejo aŭ biblioteko, aŭ kie ajn vi iras! Ni ne kredas je barado — se ni estos forigitaj, ni simple reaperos aliloke, ĉar nia tuta kodo kaj datumoj estas plene malfermitaj.</li><li>3. Se vi povas, konsideru <a href="/donate">doni</a>.</li><li>4. Helpu <a href="https://translate.annas-software.org/">traduki</a> nian retejon en diversajn lingvojn.</li><li>5. Se vi estas programisto, konsideru kontribui al nia <a href="https://annas-software.org/">malferma fonto</a>, aŭ semadi niajn <a href="/datasets">torentojn</a>.</li> 10. Krei aŭ helpi prizorgi la Vikipedian paĝon por la Arkivo de Anna en via lingvo. 11. Ni serĉas meti malgrandajn, gustumajn reklamojn. Se vi ŝatus reklami en Arkivo de Anna, bonvolu sciigi nin. 6. Se vi estas sekureca esploristo, ni povas uzi viajn kapablojn por ambaŭ ofendo kaj defendo. Rigardu nian <a %(a_security)s>Sekureco</a> paĝon. 7. Ni serĉas fakulojn pri pagoj por anonimaj komercistoj. Ĉu vi povas helpi nin aldoni pli oportunajn manierojn donaci? PayPal, WeChat, donackartoj. Se vi konas iun, bonvolu kontakti nin. 8. Ni ĉiam serĉas pli da servila kapacito. 9. Vi povas helpi raportante dosierajn problemojn, lasante komentojn, kaj kreante listojn rekte en ĉi tiu retejo. Vi ankaŭ povas helpi <a %(a_upload)s>alŝutante pli da libroj</a>, aŭ riparante dosierajn problemojn aŭ formatadon de ekzistantaj libroj. Por pli ampleksa informo pri kiel volontuli, vidu nian <a %(a_volunteering)s>Volontulado & Recompencoj</a> paĝon. Ni forte kredas je la libera fluo de informoj, kaj konservado de scio kaj kulturo. Kun ĉi tiu serĉilo, ni staras sur la ŝultroj de gigantoj. Ni profunde respektas la malfacilan laboron de la homoj, kiuj kreis la diversajn ombrobibliotekojn, kaj ni esperas, ke ĉi tiu serĉilo plivastigos ilian atingon. Por resti ĝisdatigita pri nia progreso, sekvu Annan ĉe <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> aŭ <a href="https://t.me/annasarchiveorg">Telegram</a>. Por demandoj kaj reagoj bonvolu kontakti Annan ĉe %(email)s. Konto ID: %(account_id)s Elsaluti ❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi. ✅ Vi nun estas elsalutinta. Reŝarĝu la paĝon por ensaluti denove. Rapidaj elŝutoj uzitaj (lastaj 24 horoj): <strong>%(used)s / %(total)s</strong> Membreco: <strong>%(tier_name)s</strong> ĝis %(until_date)s <a %(a_extend)s>(plilongigi)</a> Vi povas kombini plurajn membrecojn (rapidaj elŝutoj po 24 horoj estos aldonitaj kune). Membreco: <strong>Neniu</strong> <a %(a_become)s>(iĝi membro)</a> Kontaktu Annan ĉe %(email)s se vi interesiĝas pri ĝisdatigo de via membreco al pli alta nivelo. Publika profilo: %(profile_link)s Sekreta ŝlosilo (ne kundividu!): %(secret_key)s montri Partoprenu nin ĉi tie! Ĝisdatigu al <a %(a_tier)s>pli alta nivelo</a> por partopreni nian grupon. Ekskluziva Telegram-grupo: %(link)s Konto kiuj elŝutoj? Ensaluti Ne perdu vian ŝlosilon! Nevalida sekreta ŝlosilo. Kontrolu vian ŝlosilon kaj provu denove, aŭ alternative registru novan konton sube. Sekreta ŝlosilo Enigu vian sekretan ŝlosilon por ensaluti: Malnova retpoŝta konto? Enigu vian <a %(a_open)s>retpoŝton ĉi tie</a>. Registri novan konton Ĉu vi ankoraŭ ne havas konton? Registrado sukcese! Via sekreta ŝlosilo estas: <span %(span_key)s>%(key)s</span> Konservu ĉi tiun ŝlosilon zorge. Se vi perdas ĝin, vi perdos aliron al via konto. <li %(li_item)s><strong>Legosigno.</strong> Vi povas legosigni ĉi tiun paĝon por retrovi vian ŝlosilon.</li><li %(li_item)s><strong>Elŝuto.</strong> Alklaku <a %(a_download)s>ĉi tiun ligilon</a> por elŝuti vian ŝlosilon.</li><li %(li_item)s><strong>Pasvorta administrilo.</strong> Uzu pasvortan administrilon por konservi la ŝlosilon kiam vi enigas ĝin sube.</li> Ensaluti / Registriĝi Retumila kontrolo Averto: kodo enhavas malĝustajn Unikodajn signojn, kaj povus konduti malĝuste en diversaj situacioj. La kruda binaraĵo povas esti dekodita el la base64-reprezento en la URL. Priskribo Etikedo Prefikso URL por specifa kodo Retejo Kodoj komencantaj per “%(prefix_label)s” Bonvolu ne skrapi ĉi tiujn paĝojn. Anstataŭe ni rekomendas <a %(a_import)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn, kaj funkciigi nian <a %(a_software)s>malfermitkodan kodon</a>. La krudaj datumoj povas esti mane esploritaj per JSON-dosieroj kiel <a %(a_json_file)s>ĉi tiu</a>. Malpli ol %(count)s registroj Ĝenerala URL Koda Esploristo Indekso de Esploru la kodojn kun kiuj rekordoj estas etikedataj, laŭ prefikso. La kolumno “rekordoj” montras la nombron de rekordoj etikedataj kun kodoj kun la donita prefikso, kiel vidite en la serĉilo (inkluzive de nur-metadatenaj rekordoj). La kolumno “kodoj” montras kiom da realaj kodoj havas donitan prefikson. Konata koda prefikso “%(key)s” Pli… Prefikso %(count)s registro kongrua kun “%(prefix_label)s” %(count)s registroj kongruaj kun “%(prefix_label)s” kodoj registroj “%%s” estos anstataŭigita per la valoro de la kodo Serĉu la Arkivon de Anna Kodoj URL por specifa kodo: “%(url)s” Ĉi tiu paĝo povas daŭri iom da tempo por generi, tial ĝi postulas Cloudflare captcha. <a %(a_donate)s>Membroj</a> povas preterlasi la captcha. Misuzo raportita: Pli bona versio Ĉu vi volas raporti ĉi tiun uzanton pro misuzema aŭ maldeca konduto? Dosiera problemo: %(file_issue)s kaŝita komento Respondi Raporti misuzon Vi raportis ĉi tiun uzanton pro misuzo. Aŭtorrajtoj al ĉi tiu retpoŝto estos ignoritaj; uzu la formularon anstataŭe. Montri retpoŝton Ni tre bonvenigas viajn reagojn kaj demandojn! Tamen, pro la kvanto de spamo kaj sensencaj retpoŝtoj, kiujn ni ricevas, bonvolu marki la skatolojn por konfirmi, ke vi komprenas ĉi tiujn kondiĉojn por kontakti nin. Ajna alia maniero kontakti nin pri kopirajtaj asertoj estos aŭtomate forigita. Por DMCA / kopirajtaj asertoj, uzu <a %(a_copyright)s>ĉi tiun formularon</a>. Kontakta retpoŝto URL-oj en la Arkivo de Anna (deviga). Unu por linio. Bonvolu inkluzivi nur URL-ojn, kiuj priskribas la saman eldonon de libro. Se vi volas fari aserton por pluraj libroj aŭ pluraj eldonoj, bonvolu sendi ĉi tiun formularon plurfoje. Asertoj, kiuj kunigas plurajn librojn aŭ eldonojn, estos malakceptitaj. Adreso (deviga) Klara priskribo de la fonta materialo (deviga) Retpoŝto (deviga) URL-oj al la fonta materialo, unu por linio (deviga). Bonvolu inkluzivi kiel eble plej multajn, por helpi nin konfirmi vian aserton (ekz. Amazon, WorldCat, Google Books, DOI). ISBN-oj de la fonta materialo (se aplikebla). Unu por linio. Bonvolu inkluzivi nur tiujn, kiuj ĝuste kongruas kun la eldono por kiu vi raportas kopirajtan aserton. Via nomo (deviga) ❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi. ✅ Dankon pro sendi vian kopirajtan aserton. Ni revizios ĝin kiel eble plej baldaŭ. Bonvolu reŝargi la paĝon por sendi alian. <a %(a_openlib)s>Open Library</a> URL-oj de la fonta materialo, unu por linio. Bonvolu preni momenton por serĉi en Open Library vian fontan materialon. Ĉi tio helpos nin konfirmi vian aserton. Telefonnumero (deviga) Deklaro kaj subskribo (deviga) Sendi aserton Se vi havas DMCA aŭ alian aserton pri kopirajto, bonvolu plenigi ĉi tiun formularon kiel eble plej precize. Se vi renkontas iujn problemojn, bonvolu kontakti nin ĉe nia dediĉita DMCA-adreso: %(email)s. Notu, ke asertoj senditaj al ĉi tiu adreso ne estos prilaboritaj, ĝi estas nur por demandoj. Bonvolu uzi la suban formularon por sendi viajn asertojn. DMCA / Formulario de aserto pri kopirajto Ekzempla registro en Anna’s Archive Torentoj de Anna’s Archive Anna’s Archive Containers formato Skriptoj por importi metadatenojn Se vi interesiĝas pri spegulado de ĉi tiu datenserio por <a %(a_archival)s>arkivado</a> aŭ <a %(a_llm)s>LLM-trejnado</a>, bonvolu kontakti nin. Laste ĝisdatigita: %(date)s Ĉefa %(source)s retejo Metadatenaj dokumentado (plej multaj kampoj) Dosieroj spegulitaj de Anna’s Archive: %(count)s (%(percent)s%%) Rimedoj Totalaj dosieroj: %(count)s Totala dosiergrandeco: %(size)s Nia bloga afiŝo pri ĉi tiuj datumoj <a %(duxiu_link)s>Duxiu</a> estas granda datumbazo de skanitaj libroj, kreita de la <a %(superstar_link)s>SuperStar Digital Library Group</a>. Plejparte temas pri akademiaj libroj, skanitaj por fari ilin disponeblaj ciferece al universitatoj kaj bibliotekoj. Por nia anglalingva publiko, <a %(princeton_link)s>Princeton</a> kaj la <a %(uw_link)s>University of Washington</a> havas bonajn superrigardojn. Estas ankaŭ bonega artikolo donanta pli da fono: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. La libroj de Duxiu delonge estis piratitaj en la ĉina interreto. Ili kutime estas vendataj por malpli ol dolaro de revendistoj. Ili estas tipe distribuitaj uzante la ĉinan ekvivalenton de Google Drive, kiu ofte estis hakita por permesi pli da stokspaco. Iuj teknikaj detaloj troveblas <a %(link1)s>ĉi tie</a> kaj <a %(link2)s>ĉi tie</a>. Kvankam la libroj estis duonpublike distribuitaj, estas sufiĉe malfacile akiri ilin amase. Ni havis ĉi tion alte en nia TODO-listo, kaj asignis plurajn monatojn de plentempa laboro por ĝi. Tamen, malfrue en 2023 nekredebla, mirinda, kaj talenta volontulo kontaktis nin, dirante ke ili jam faris ĉion ĉi tiun laboron — je granda elspezo. Ili dividis la plenan kolekton kun ni, sen atendi ion ajn kompense, krom la garantio de longtempa konservado. Vere rimarkinda. Pli da informoj de niaj volontuloj (krudaj notoj): Adaptita el nia <a %(a_href)s>bloga afiŝo</a>. DuXiu 读秀 %(count)s dosiero %(count)s dosieroj Ĉi tiu datenserio estas proksime rilata al la <a %(a_datasets_openlib)s>Open Library datenserio</a>. Ĝi enhavas skrapadon de ĉiuj metadatenoj kaj granda parto de dosieroj el la Kontrolita Cifereca Pruntedona Biblioteko de IA. Ĝisdatigoj estas publikigitaj en la <a %(a_aac)s>Anna’s Archive Containers formato</a>. Ĉi tiuj rekordoj estas rekte referencitaj de la Open Library datenserio, sed ankaŭ enhavas rekordojn, kiuj ne estas en Open Library. Ni ankaŭ havas kelkajn datumdosierojn skrapitajn de komunumaj membroj tra la jaroj. La kolekto konsistas el du partoj. Vi bezonas ambaŭ partojn por akiri ĉiujn datumojn (krom anstataŭigitaj torentoj, kiuj estas forstrekitaj en la torentpaĝo). Cifereca Pruntejo nia unua eldono, antaŭ ol ni normigis la <a %(a_aac)s>Anna’s Archive Containers (AAC) formaton</a>. Enhavas metadatenojn (kiel json kaj xml), pdf-ojn (el acsm kaj lcpdf ciferecaj pruntsistemoj), kaj kovrilajn etbildojn. inkrementaj novaj eldonoj, uzante AAC. Enhavas nur metadatenojn kun tempomarkoj post 2023-01-01, ĉar la resto jam estas kovrita de “ia”. Ankaŭ ĉiuj pdf-dosieroj, ĉi-foje el la acsm kaj “bookreader” (IA’s retlegilo) pruntsistemoj. Malgraŭ la nomo ne esti tute ĝusta, ni ankoraŭ enmetas bookreader-dosierojn en la ia2_acsmpdf_files kolekton, ĉar ili estas reciproke ekskluzivaj. IA Kontrolita Cifereca Pruntado 98%%+ de dosieroj estas serĉeblaj. Nia misio estas arkivi ĉiujn librojn en la mondo (kiel ankaŭ artikolojn, magazinojn, ktp), kaj fari ilin vaste alireblaj. Ni kredas ke ĉiuj libroj devus esti spegulitaj vaste, por certigi redundon kaj rezistecon. Tial ni kunigas dosierojn el diversaj fontoj. Iuj fontoj estas tute malfermaj kaj povas esti spegulitaj amase (kiel Sci-Hub). Aliaj estas fermitaj kaj protektaj, do ni provas skrapi ilin por “liberigi” iliajn librojn. Aliaj falas ie intere. Ĉiuj niaj datumoj povas esti <a %(a_torrents)s>torentitaj</a>, kaj ĉiuj niaj metadatenoj povas esti <a %(a_anna_software)s>generitaj</a> aŭ <a %(a_elasticsearch)s>elŝutitaj</a> kiel ElasticSearch kaj MariaDB datumbazoj. La krudaj datumoj povas esti mane esploritaj tra JSON-dosieroj kiel <a %(a_dbrecord)s>ĉi tiu</a>. Metadatumoj Retejo de ISBN Laste ĝisdatigita: %(isbn_country_date)s (%(link)s) Rimedoj La Internacia ISBN-Agentejo regule publikigas la intervalojn, kiujn ĝi asignis al naciaj ISBN-agentejoj. El tio ni povas derivi al kiu lando, regiono aŭ lingva grupo apartenas ĉi tiu ISBN. Ni nuntempe uzas ĉi tiujn datumojn nerekte, per la <a %(a_isbnlib)s>isbnlib</a> Python-biblioteko. Informoj pri lando de ISBN Ĉi tio estas elŝuto de multaj vokoj al isbndb.com dum septembro 2022. Ni provis kovri ĉiujn ISBN-intervalojn. Ĉi tiuj estas ĉirkaŭ 30.9 milionoj da rekordoj. Sur ilia retejo ili asertas, ke ili fakte havas 32.6 milionojn da rekordoj, do ni eble iel maltrafis kelkajn, aŭ <em>ili</em> povus fari ion malĝuste. La JSON-respondoj estas preskaŭ krudaj de ilia servilo. Unu datuma kvalita problemo, kiun ni rimarkis, estas ke por ISBN-13-nombroj, kiuj komenciĝas per malsama prefikso ol “978-”, ili ankoraŭ inkluzivas “isbn”-kampon, kiu simple estas la ISBN-13-nombro kun la unuaj 3 nombroj fortranĉitaj (kaj la kontrolcifero rekalkulita). Ĉi tio estas evidente malĝusta, sed tiel ili ŝajnas fari ĝin, do ni ne ŝanĝis ĝin. Alia ebla problemo, kiun vi povus renkonti, estas la fakto, ke la kampo “isbn13” havas duplikatojn, do vi ne povas uzi ĝin kiel ĉefan ŝlosilon en datumbazo. “isbn13”+“isbn”-kampoj kombinitaj ŝajnas esti unikaj. Eldono 1 (2022-10-31) Fikciaj torentoj estas malantaŭe (kvankam ID-oj ~4-6M ne torentitaj ĉar ili koincidas kun niaj Zlib-torentoj). Nia bloga afiŝo pri la eldono de komiksoj Komiksoj torentoj en la Arkivo de Anna Por la fono de la malsamaj forkoj de Library Genesis, vidu la paĝon por la <a %(a_libgen_rs)s>Libgen.rs</a>. La Libgen.li enhavas plejparton de la sama enhavo kaj metadatumoj kiel la Libgen.rs, sed havas kelkajn kolektojn aldone al tio, nome komiksoj, magazinoj kaj normaj dokumentoj. Ĝi ankaŭ integris <a %(a_scihub)s>Sci-Hub</a> en siajn metadatumojn kaj serĉilon, kion ni uzas por nia datumbazo. La metadatumoj por ĉi tiu biblioteko estas libere disponeblaj <a %(a_libgen_li)s>ĉe libgen.li</a>. Tamen, ĉi tiu servilo estas malrapida kaj ne subtenas rekomencadon de rompitaj konektoj. La samaj dosieroj ankaŭ estas disponeblaj ĉe <a %(a_ftp)s>FTP-servilo</a>, kiu funkcias pli bone. Ne-fikcio ankaŭ ŝajnas esti diverĝinta, sed sen novaj torentoj. Ŝajnas, ke tio okazis ekde frua 2022, kvankam ni ne konfirmis tion. Laŭ la administranto de Libgen.li, la kolekto “fiction_rus” (rusa fikcio) devus esti kovrita de regule eldonitaj torentoj de <a %(a_booktracker)s>booktracker.org</a>, plej precipe la torentoj de <a %(a_flibusta)s>flibusta</a> kaj <a %(a_librusec)s>lib.rus.ec</a> (kiujn ni spegulas <a %(a_torrents)s>ĉi tie</a>, kvankam ni ankoraŭ ne establis kiuj torentoj respondas al kiuj dosieroj). La fikcia kolekto havas siajn proprajn torentojn (diverĝintajn de <a %(a_href)s>Libgen.rs</a>) komencante ĉe %(start)s. Certaj intervaloj sen torentoj (kiel ekzemple fikciaj intervaloj f_3463000 ĝis f_4260000) verŝajne estas dosieroj de Z-Library (aŭ aliaj duplikatoj), kvankam ni eble volas fari iun deduplikadon kaj krei torentojn por lgli-unikaj dosieroj en ĉi tiuj intervaloj. Statistikoj por ĉiuj kolektoj troveblas <a %(a_href)s>en la retejo de libgen</a>. Torentoj estas disponeblaj por plejparto de la aldona enhavo, plej precipe torentoj por bildstrioj, revuoj, kaj normaj dokumentoj estis eldonitaj en kunlaboro kun la Arkivo de Anna. Notu, ke la torentaj dosieroj referencantaj “libgen.is” estas eksplicite speguloj de <a %(a_libgen)s>Libgen.rs</a> (“.is” estas malsama domajno uzata de Libgen.rs). Utila rimedo por uzi la metadatenojn estas <a %(a_href)s>ĉi tiu paĝo</a>. %(icon)s Ilia kolekto “fiction_rus” (rusa fikcio) ne havas dediĉitajn torentojn, sed estas kovrita de torentoj de aliaj, kaj ni konservas <a %(fiction_rus)s>spejlon</a>. Rusaj fikciaj torentoj en la Arkivo de Anna Fikciaj torentoj en la Arkivo de Anna Diskutforumo Metadatenoj Metadatenoj per FTP Magazinoj torentoj en la Arkivo de Anna Informoj pri metadatenaj kampoj Spegulo de aliaj torentoj (kaj unikaj fikciaj kaj komiksoj torentoj) Normaj dokumentaj torentoj en la Arkivo de Anna Libgen.li Torentoj de la Arkivo de Anna (librokovriloj) Library Genesis estas konata pro jam malavare disponigi siajn datumojn amase per torentoj. Nia Libgen-kolekto konsistas el helpaj datumoj, kiujn ili ne publikigas rekte, en partnereco kun ili. Multan dankon al ĉiuj implikitaj kun Library Genesis pro kunlabori kun ni! Nia blogo pri la eldono de librokovriloj Ĉi tiu paĝo temas pri la “.rs” versio. Ĝi estas konata pro konstante publikigado de kaj siaj metadatenoj kaj la plenaj enhavoj de sia librokatalogo. Ĝia librokolekto estas dividita inter fikcia kaj nefikcia parto. Utila rimedo por uzi la metadatenojn estas <a %(a_metadata)s>ĉi tiu paĝo</a> (blokas IP-gamojn, VPN eble necesas). Ekde 2024-03, novaj torentoj estas afiŝataj en <a %(a_href)s>ĉi tiu forumfadeno</a> (blokas IP-gamojn, eble necesas VPN). Fikciaj torentoj en la Arkivo de Anna Libgen.rs Fikciaj torentoj Libgen.rs Diskutforumo Libgen.rs Metadatenoj Informoj pri metadatenaj kampoj de Libgen.rs Libgen.rs Nefikciaj torentoj Nefikciaj torentoj en la Arkivo de Anna %(example)s por fikcia libro. Ĉi tiu <a %(blog_post)s>unua eldono</a> estas sufiĉe malgranda: ĉirkaŭ 300GB da librokovriloj de la Libgen.rs-forko, kaj fikcio kaj nefikcio. Ili estas organizitaj same kiel ili aperas en libgen.rs, ekz.: %(example)s por nefikcia libro. Same kiel kun la Z-Library-kolekto, ni metis ilin ĉiujn en grandan .tar-dosieron, kiu povas esti muntita uzante <a %(a_ratarmount)s>ratarmount</a> se vi volas servi la dosierojn rekte. Eldono 1 (%(date)s) La rapida historio de la malsamaj forkigoj de Library Genesis (aŭ “Libgen”) estas, ke kun la tempo, la malsamaj homoj implikitaj en Library Genesis havis malkonsenton kaj iris siajn apartajn vojojn. Laŭ ĉi tiu <a %(a_mhut)s>forumafiŝo</a>, Libgen.li estis origine gastigita ĉe “http://free-books.dontexist.com”. La “.fun” versio estis kreita de la originala fondinto. Ĝi estas renovigata favore al nova, pli distribuita versio. La <a %(a_li)s>“.li” versio</a> havas grandegan kolekton de komiksoj, same kiel aliajn enhavojn, kiuj ankoraŭ ne estas disponeblaj por amasa elŝuto per torentoj. Ĝi havas apartan torentan kolekton de fikciaj libroj, kaj ĝi enhavas la metadatenojn de <a %(a_scihub)s>Sci-Hub</a> en sia datumbazo. La “.rs” versio havas tre similajn datumojn, kaj plej konstante publikigas sian kolekton en amasaj torentoj. Ĝi estas proksimume dividita en “fikcia” kaj “nefikcia” sekcio. Originale ĉe “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> iusence ankaŭ estas forkigo de Library Genesis, kvankam ili uzis malsaman nomon por sia projekto. Libgen.rs Ni ankaŭ riĉigas nian kolekton per nur-metadatumaj fontoj, kiujn ni povas kongrui kun dosieroj, ekz. uzante ISBN-numerojn aŭ aliajn kampojn. Sube estas superrigardo de tiuj. Denove, iuj el ĉi tiuj fontoj estas tute malfermaj, dum por aliaj ni devas skrapi ilin. Notu, ke en metadatumserĉo, ni montras la originalajn rekordojn. Ni ne faras ajnan kunfandiĝon de rekordoj. Nur-metadatumaj fontoj Open Library estas malfermfonta projekto de la Internet Archive por katalogi ĉiun libron en la mondo. Ĝi havas unu el la plej grandaj libro-skanaj operacioj en la mondo, kaj havas multajn librojn disponeblajn por cifereca pruntepreno. Ĝia libro-metadatokatalogo estas libere disponebla por elŝuto, kaj estas inkluzivita en la Arkivo de Anna (kvankam nuntempe ne en serĉo, krom se vi eksplicite serĉas Open Library ID). Open Library Ekskludante duplikatojn Laste ĝisdatigita Procentoj de nombro de dosieroj %% spegulita de AA / torentoj disponeblaj Grando Fonto Sube estas rapida superrigardo de la fontoj de la dosieroj en la Arkivo de Anna. Ĉar la ombro-bibliotekoj ofte sinkronigas datumojn unu de la alia, estas konsiderinda koincido inter la bibliotekoj. Tial la nombroj ne sumiĝas al la tuto. La “spegulita kaj semita de la Arkivo de Anna” procento montras kiom da dosieroj ni spegulas mem. Ni semas tiujn dosierojn amase per torentoj, kaj faras ilin disponeblaj por rekta elŝuto per partneraj retejoj. Superrigardo Sume Torentoj en la Arkivo de Anna Por fono pri Sci-Hub, bonvolu rilati al ĝia <a %(a_scihub)s>oficiala retejo</a>, <a %(a_wikipedia)s>Vikipedia paĝo</a>, kaj ĉi tiu <a %(a_radiolab)s>podkasta intervjuo</a>. Notu, ke Sci-Hub estis <a %(a_reddit)s>frostigita ekde 2021</a>. Ĝi estis frostigita antaŭe, sed en 2021 kelkaj milionoj da artikoloj estis aldonitaj. Tamen, iuj limigitaj nombroj da artikoloj estas aldonitaj al la Libgen “scimag” kolektoj, kvankam ne sufiĉe por pravigi novajn amasajn torentojn. Ni uzas la Sci-Hub metadatenojn kiel provizitajn de <a %(a_libgen_li)s>Libgen.li</a> en ĝia “scimag” kolekto. Ni ankaŭ uzas la <a %(a_dois)s>dois-2022-02-12.7z</a> datumaron. Notu, ke la “smarch” torentoj estas <a %(a_smarch)s>malaktualaj</a> kaj tial ne inkluzivitaj en nia torenta listo. Torentoj en Libgen.li Torentoj en Libgen.rs Metadatenoj kaj torentoj Ĝisdatigoj en Reddit Podkasta intervjuo Vikipedia paĝo Sci-Hub Sci-Hub: frostigita ekde 2021; plejparte disponebla per torentoj Libgen.li: malgrandaj aldonaĵoj ekde tiam</div> Iuj fontaj bibliotekoj antaŭenigas la amasan kundividon de siaj datumoj per torentoj, dum aliaj ne facile kundividas sian kolekton. En la lasta kazo, la Arkivo de Anna provas skrapi iliajn kolektojn kaj fari ilin disponeblaj (vidu nian <a %(a_torrents)s>paĝon pri Torentoj</a>). Estas ankaŭ interaj situacioj, ekzemple, kie fontaj bibliotekoj volas kundividi, sed ne havas la rimedojn por fari tion. En tiuj kazoj, ni ankaŭ provas helpi. Sube estas superrigardo pri kiel ni interagas kun la diversaj fontaj bibliotekoj. Fontaj bibliotekoj %(icon)s Diversaj dosieraj datumbazoj disigitaj tra la ĉina interreto; kvankam ofte pagitaj datumbazoj %(icon)s Plej multaj dosieroj estas alireblaj nur uzante premiumajn BaiduYun-kontojn; malrapidaj elŝutaj rapidoj. %(icon)s La Arkivo de Anna administras kolekton de <a %(duxiu)s>DuXiu-dosieroj</a> %(icon)s Diversaj metadatenbazoj disigitaj tra la ĉina interreto; kvankam ofte pagitaj datumbazoj %(icon)s Neniu facile alirebla metadatenaj elŝutoj disponeblaj por ilia tuta kolekto. %(icon)s La Arkivo de Anna administras kolekton de <a %(duxiu)s>DuXiu-metadatenoj</a> Dosieroj %(icon)s Dosieroj disponeblaj nur por pruntepreno laŭ limigita bazo, kun diversaj alirlimigoj %(icon)s Arkivo de Anna administras kolekton de <a %(ia)s>IA-dosieroj</a> %(icon)s Iuj metadatenoj disponeblaj tra <a %(openlib)s>Open Library-datumbazaj elŝutoj</a>, sed tiuj ne kovras la tutan IA-kolekton %(icon)s Neniuj facile alireblaj metadatenbazaj elŝutoj disponeblaj por ilia tuta kolekto %(icon)s La Arkivo de Anna administras kolekton de <a %(ia)s>IA metadatenoj</a> Laste ĝisdatigita %(icon)s La Arkivo de Anna kaj Libgen.li kunlabore administras kolektojn de <a %(comics)s>bildstrioj</a>, <a %(magazines)s>revuoj</a>, <a %(standarts)s>normaj dokumentoj</a>, kaj <a %(fiction)s>fikcio (diverĝinta de Libgen.rs)</a>. %(icon)s Ne-fikciaj torentoj estas dividitaj kun Libgen.rs (kaj spegulitaj <a %(libgenli)s>ĉi tie</a>). %(icon)s Trimonataj <a %(dbdumps)s>HTTP-datumbazaj elŝutoj</a> %(icon)s Aŭtomatigitaj torentoj por <a %(nonfiction)s>Ne-fikcio</a> kaj <a %(fiction)s>Fikcio</a> %(icon)s Arkivo de Anna administras kolekton de <a %(covers)s>librokovrilaj torentoj</a> %(icon)s Ĉiutagaj <a %(dbdumps)s>HTTP-datumbazaj elŝutoj</a> Metadatumoj %(icon)s Monataj <a %(dbdumps)s>datumbazaj elŝutoj</a> %(icon)s Datumtorentoj disponeblaj <a %(scihub1)s>ĉi tie</a>, <a %(scihub2)s>ĉi tie</a>, kaj <a %(libgenli)s>ĉi tie</a> %(icon)s Iuj novaj dosieroj estas <a %(libgenrs)s>aldonataj</a> al “scimag” de Libgen, sed ne sufiĉe por pravigi novajn torentojn %(icon)s Sci-Hub frostigis novajn dosierojn ekde 2021. %(icon)s Metadatenbazaj elŝutoj disponeblaj <a %(scihub1)s>ĉi tie</a> kaj <a %(scihub2)s>ĉi tie</a>, same kiel parto de la <a %(libgenli)s>Libgen.li-datumbazo</a> (kiun ni uzas) Fonto %(icon)s Diversaj pli malgrandaj aŭ unufojaj fontoj. Ni kuraĝigas homojn unue alŝuti al aliaj ombrobibliotekoj, sed foje homoj havas kolektojn tro grandajn por aliaj por trarigardi, kvankam ne sufiĉe grandajn por meriti sian propran kategorion. %(icon)s Ne disponebla rekte en amaso, protektita kontraŭ skrapado %(icon)s La Arkivo de Anna administras kolekton de <a %(worldcat)s>OCLC (WorldCat) metadatenoj</a> %(icon)s La Arkivo de Anna kaj Z-Library kunlabore administras kolekton de <a %(metadata)s>Z-Library metadatenoj</a> kaj <a %(files)s>Z-Library dosieroj</a> Datasets Ni kombinas ĉiujn supre menciitajn fontojn en unu unuigitan datumbazon, kiun ni uzas por servi ĉi tiun retejon. Ĉi tiu unuigita datumbazo ne estas rekte disponebla, sed ĉar la Arkivo de Anna estas tute malfermfonta, ĝi povas esti sufiĉe facile <a %(a_generated)s>generita</a> aŭ <a %(a_downloaded)s>elŝutita</a> kiel ElasticSearch kaj MariaDB datumbazoj. La skriptoj en tiu paĝo aŭtomate elŝutos ĉiujn necesajn metadatumojn de la supre menciitaj fontoj. Se vi ŝatus esplori niajn datumojn antaŭ ol ruli tiujn skriptojn loke, vi povas rigardi niajn JSON-dosierojn, kiuj ligas plu al aliaj JSON-dosieroj. <a %(a_json)s>Ĉi tiu dosiero</a> estas bona deirpunkto. Unuigita datumbazo Torentoj de Arkivo de Anna foliumi serĉi Diversaj pli malgrandaj aŭ unufojaj fontoj. Ni kuraĝigas homojn unue alŝuti al aliaj ombrobibliotekoj, sed foje homoj havas kolektojn tro grandajn por aliaj por trarigardi, kvankam ne sufiĉe grandajn por meriti sian propran kategorion. Superrigardo de <a %(a1)s>paĝo de datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Ŝajnas esti sufiĉe kompleta. De nia volontulo “cgiym”. De <a %(a_href)s><q>ACM Digital Library 2020</q></a> torento. Havas sufiĉe altan koincidon kun ekzistantaj kolektoj de artikoloj, sed tre malmultajn MD5-koincidojn, do ni decidis konservi ĝin tute. Skrapo de <q>iRead eBooks</q> (= fonetike <q>ai rit i-books</q>; airitibooks.com), fare de volontulo <q>j</q>. Korespondas al <q>airitibooks</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>. El kolekto <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte el la originala fonto, parte de the-eye.eu, parte de aliaj speguloj. De privata libro-torenta retejo, <a %(a_href)s>Bibliotik</a> (ofte referita kiel “Bib”), kies libroj estis kunigitaj en torentojn laŭ nomo (A.torento, B.torento) kaj distribuitaj tra the-eye.eu. De nia volontulo “bpb9v”. Por pli da informoj pri <a %(a_href)s>CADAL</a>, vidu la notojn en nia <a %(a_duxiu)s>DuXiu-datuma paĝo</a>. Pli de nia volontulo “bpb9v”, plejparte DuXiu-dosieroj, same kiel dosierujo “WenQu” kaj “SuperStar_Journals” (SuperStar estas la kompanio malantaŭ DuXiu). De nia volontulo “cgiym”, ĉinaj tekstoj el diversaj fontoj (reprezentitaj kiel subdosierujoj), inkluzive de <a %(a_href)s>China Machine Press</a> (grava ĉina eldonisto). Ne-ĉinaj kolektoj (reprezentitaj kiel subdosierujoj) de nia volontulo “cgiym”. Skrapo de libroj pri ĉina arkitekturo, fare de volontulo <q>cm</q>: <q>Mi akiris ĝin ekspluatante retnetan vundeblecon ĉe la eldonejo, sed tiu truo jam estis fermita</q>. Korespondas al <q>chinese_architecture</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>. Libroj de akademia eldonejo <a %(a_href)s>De Gruyter</a>, kolektitaj el kelkaj grandaj torentoj. Skrapo de <a %(a_href)s>docer.pl</a>, pola dosierdivida retejo fokusita je libroj kaj aliaj skribaj verkoj. Skrapita malfrue en 2023 de volontulo “p”. Ni ne havas bonajn metadatenojn de la originala retejo (eĉ ne dosieretendaĵojn), sed ni filtris por libro-similaj dosieroj kaj ofte povis eltiri metadatenojn el la dosieroj mem. DuXiu-epuboj, rekte de DuXiu, kolektitaj de volontulo “w”. Nur lastatempaj DuXiu-libroj estas rekte disponeblaj kiel e-libroj, do plej multaj el ĉi tiuj devas esti lastatempaj. Restantaj DuXiu-dosieroj de volontulo “m”, kiuj ne estis en la DuXiu-proprietara PDG-formato (la ĉefa <a %(a_href)s>DuXiu-datuma aro</a>). Kolektitaj el multaj originalaj fontoj, bedaŭrinde sen konservi tiujn fontojn en la dosierujo. <span></span> <span></span> <span></span> Skrapo de erotikaj libroj, fare de volontulo <q>do no harm</q>. Korespondas al <q>hentai</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>. <span></span> <span></span> Kolekto skrapita de japana Manga-eldonisto de volontulo “t”. <a %(a_href)s>Elektitaj juraj arkivoj de Longquan</a>, provizitaj de volontulo “c”. Skrapo de <a %(a_href)s>magzdb.org</a>, aliancano de Library Genesis (ĝi estas ligita en la libgen.rs hejmpaĝo) sed kiu ne volis provizi siajn dosierojn rekte. Akirita de volontulo “p” malfrue en 2023. <span></span> Diversaj malgrandaj alŝutoj, tro malgrandaj kiel siaj propraj subkolektoj, sed reprezentitaj kiel dosierujoj. E-libroj de AvaxHome, rusa dosierdivida retejo. Arkivo de gazetoj kaj revuoj. Korespondas al <q>newsarch_magz</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>. Skrapo de la <a %(a1)s>Philosophy Documentation Center</a>. Kolekto de volontulo “o” kiu kolektis polajn librojn rekte de originalaj eldonaj (“scene”) retejoj. Kombinitaj kolektoj de <a %(a_href)s>shuge.org</a> de volontuloj “cgiym” kaj “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (nomita laŭ la fikcia biblioteko), skrapita en 2022 de volontulo “t”. <span></span> <span></span> <span></span> Sub-sub-kolektoj (reprezentitaj kiel dosierujoj) de volontulo “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (de <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Tajvano), mebook (mebook.cc, 我的小书屋, mia malgranda libroĉambro — woz9ts: “Ĉi tiu retejo ĉefe fokusiĝas je divido de altkvalitaj e-libroj, kelkaj el kiuj estas tajpitaj de la posedanto mem. La posedanto estis <a %(a_arrested)s>arestita</a> en 2019 kaj iu faris kolekton de dosieroj kiujn li dividis.”). Restantaj DuXiu-dosieroj de volontulo “woz9ts”, kiuj ne estis en la proprieta PDG-formato de DuXiu (ankoraŭ konvertotaj al PDF). La "alŝuta" kolekto estas dividita en pli malgrandajn subkolektojn, kiuj estas indikitaj en la AACID-oj kaj torentaj nomoj. Ĉiuj subkolektoj unue estis deduplikitaj kontraŭ la ĉefa kolekto, kvankam la metadatenaj "upload_records" JSON-dosieroj ankoraŭ enhavas multajn referencojn al la originalaj dosieroj. Ne-libraj dosieroj ankaŭ estis forigitaj el plej multaj subkolektoj, kaj tipe <em>ne</em> estas notitaj en la "upload_records" JSON. La subkolektoj estas: Notoj Subkolekto Multaj subkolektoj mem konsistas el sub-sub-kolektoj (ekz. de malsamaj originalaj fontoj), kiuj estas reprezentitaj kiel dosierujoj en la "filepath" kampoj. Alŝutoj al la Arkivo de Anna Nia bloga afiŝo pri ĉi tiuj datumoj <a %(a_worldcat)s>WorldCat</a> estas proprieta datumbazo de la neprofitcela <a %(a_oclc)s>OCLC</a>, kiu agregas metadatenajn rekordojn de bibliotekoj tra la tuta mondo. Verŝajne ĝi estas la plej granda biblioteka metadatenkolekto en la mondo. En oktobro 2023 ni <a %(a_scrape)s>publikigis</a> ampleksan skrapadon de la OCLC (WorldCat) datumbazo, en la <a %(a_aac)s>Arkivo de Anna Ujoj formato</a>. Oktobro 2023, komenca eldono: OCLC (WorldCat) Torentoj de la Arkivo de Anna Ekzempla registro en Arkivo de Anna (originala kolekto) Ekzempla registro en Arkivo de Anna (“zlib3” kolekto) Torentoj de Arkivo de Anna (metadatumoj + enhavo) Bloga afiŝo pri Eldono 1 Bloga afiŝo pri Eldono 2 Fine de 2022, la supozataj fondintoj de Z-Library estis arestitaj, kaj domajnoj estis konfiskitaj de usonaj aŭtoritatoj. Ekde tiam la retejo malrapide revenas rete. Estas nekonate kiu nuntempe administras ĝin. Ĝisdatigo de februaro 2023. Z-Library havas siajn radikojn en la <a %(a_href)s>Library Genesis</a> komunumo, kaj origine startis kun iliaj datumoj. Ekde tiam, ĝi profesiigis konsiderinde, kaj havas multe pli modernan interfacon. Ili do kapablas ricevi multe pli da donacoj, kaj monaj por daŭre plibonigi sian retejon, kaj donacojn de novaj libroj. Ili amasigis grandan kolekton krom Library Genesis. La kolekto konsistas el tri partoj. La originalaj priskribaj paĝoj por la unuaj du partoj estas konservitaj sube. Vi bezonas ĉiujn tri partojn por akiri ĉiujn datumojn (krom anstataŭigitaj torentoj, kiuj estas forstrekitaj en la torentpaĝo). %(title)s: nia unua eldono. Ĉi tio estis la tre unua eldono de tio, kio tiam nomiĝis la “Pirata Biblioteka Spegulo” (“pilimi”). %(title)s: dua eldono, ĉi-foje kun ĉiuj dosieroj envolvitaj en .tar-dosierojn. %(title)s: inkrementaj novaj eldonoj, uzante la <a %(a_href)s>Arkivo de Anna Ujoj (AAC) formato</a>, nun eldonitaj en kunlaboro kun la teamo de Z-Library. La komenca spegulo estis akirita pene dum 2021 kaj 2022. Ĉi-momente ĝi estas iomete malaktuala: ĝi reflektas la staton de la kolekto en junio 2021. Ni ĝisdatigos ĉi tion en la estonteco. Nuntempe ni fokusiĝas al eldonado de ĉi tiu unua eldono. Ekde Library Genesis jam estas konservita per publikaj torentoj, kaj estas inkluzivita en la Z-Library, ni faris bazan deduplikadon kontraŭ Library Genesis en junio 2022. Por tio ni uzis MD5-hashojn. Verŝajne estas multe pli da duplikata enhavo en la biblioteko, kiel pluraj dosierformatoj kun la sama libro. Tio estas malfacile detektebla precize, do ni ne faras tion. Post la deduplikado ni restas kun pli ol 2 milionoj da dosieroj, sumante iom malpli ol 7TB. La kolekto konsistas el du partoj: MySQL “.sql.gz” dump de la metadatenoj, kaj la 72 torentdosieroj de ĉirkaŭ 50-100GB ĉiu. La metadatenoj enhavas la datumojn raportitajn de la retejo Z-Library (titolo, aŭtoro, priskribo, dosiertipo), same kiel la faktan dosiergrandecon kaj md5sum, kiujn ni observis, ĉar foje tiuj ne kongruas. Ŝajnas, ke ekzistas gamoj de dosieroj por kiuj la Z-Library mem havas malĝustajn metadatenojn. Ni ankaŭ eble malĝuste elŝutis dosierojn en iuj izolitaj kazoj, kiujn ni provos detekti kaj ripari en la estonteco. La grandaj torentdosieroj enhavas la faktan librodatenon, kun la Z-Library ID kiel la dosiernomo. La dosieretendaĵoj povas esti rekonstruitaj uzante la metadatenan dump. La kolekto estas miksaĵo de nefikcia kaj fikcia enhavo (ne apartigita kiel en Library Genesis). La kvalito ankaŭ estas vaste varia. Ĉi tiu unua eldono nun estas plene disponebla. Notu, ke la torentdosieroj estas disponeblaj nur per nia Tor-spegulo. Eldono 1 (%(date)s) Ĉi tio estas unuopa ekstra torentdosiero. Ĝi ne enhavas iujn novajn informojn, sed ĝi havas iujn datumojn en ĝi, kiuj povas daŭri iom da tempo por kalkuli. Tio faras ĝin oportuna havi, ĉar elŝuti ĉi tiun torenton ofte estas pli rapida ol kalkuli ĝin de nulo. Precipe, ĝi enhavas SQLite-indeksojn por la tar-dosieroj, por uzo kun <a %(a_href)s>ratarmount</a>. Eldono 2 aldonaĵo (%(date)s) Ni akiris ĉiujn librojn, kiuj estis aldonitaj al la Z-Library inter nia lasta spegulo kaj aŭgusto 2022. Ni ankaŭ revenis kaj skrapis iujn librojn, kiujn ni maltrafis la unuan fojon. Ĉiukaze, ĉi tiu nova kolekto estas ĉirkaŭ 24TB. Denove, ĉi tiu kolekto estas deduplikita kontraŭ Library Genesis, ĉar jam estas torentoj disponeblaj por tiu kolekto. La datumoj estas organizitaj simile al la unua eldono. Estas MySQL “.sql.gz” dump de la metadatenoj, kiu ankaŭ inkluzivas ĉiujn metadatenojn de la unua eldono, tiel superante ĝin. Ni ankaŭ aldonis kelkajn novajn kolumnojn: Ni menciis ĉi tion la lastan fojon, sed nur por klarigi: “filename” kaj “md5” estas la faktaj propraĵoj de la dosiero, dum “filename_reported” kaj “md5_reported” estas tio, kion ni skrapis de Z-Library. Foje ĉi tiuj du ne kongruas unu kun la alia, do ni inkluzivis ambaŭ. Por ĉi tiu eldono, ni ŝanĝis la kolacion al “utf8mb4_unicode_ci”, kiu devus esti kongrua kun pli malnovaj versioj de MySQL. La datumdosieroj estas similaj al la lasta fojo, kvankam ili estas multe pli grandaj. Ni simple ne povis ĝeni krei amason da pli malgrandaj torentdosieroj. “pilimi-zlib2-0-14679999-extra.torrent” enhavas ĉiujn dosierojn, kiujn ni maltrafis en la lasta eldono, dum la aliaj torentoj estas ĉiuj novaj ID-gamoj.  <strong>Ĝisdatigo %(date)s:</strong> Ni faris plej multajn el niaj torentoj tro grandaj, kaŭzante ke torentklientoj luktas. Ni forigis ilin kaj publikigis novajn torentojn. <strong>Ĝisdatigo %(date)s:</strong> Ankoraŭ estis tro multaj dosieroj, do ni envolvis ilin en tar-dosierojn kaj publikigis novajn torentojn denove. %(key)s: ĉu ĉi tiu dosiero jam estas en Library Genesis, en aŭ la nefikcia aŭ fikcia kolekto (kongrua per md5). %(key)s: en kiu torento ĉi tiu dosiero estas. %(key)s: agordita kiam ni ne povis elŝuti la libron. Eldono 2 (%(date)s) Zlib-eldonoj (originalaj priskribaj paĝoj) Tor-domajno Ĉefa retejo Z-Library skrapo La “Ĉina” kolekto en Z-Library ŝajnas esti la sama kiel nia DuXiu kolekto, sed kun malsamaj MD5-oj. Ni ekskludas ĉi tiujn dosierojn el torentoj por eviti duobligon, sed ankoraŭ montras ilin en nia serĉindekso. Metadatenoj Vi ricevas %(percentage)s%% bonusajn rapidajn elŝutojn, ĉar vi estis referita de uzanto %(profile_link)s. Ĉi tio validas por la tuta membreca periodo. Donaci Partopreno Elektita ĝis %(percentage)s%% rabatoj Alipay subtenas internaciajn kredit-/debetkartojn. Vidu <a %(a_alipay)s>ĉi tiun gvidilon</a> por pli da informoj. Sendu al ni donackartojn de Amazon.com uzante vian kredit-/debetkarton. Vi povas aĉeti kripton uzante kredit/debetkartojn. WeChat (Weixin Pay) subtenas internaciajn kredit-/debetkartojn. En la WeChat-aplikaĵo, iru al “Mi => Servoj => Monujo => Aldoni Karton”. Se vi ne vidas tion, ebligu ĝin uzante “Mi => Agordoj => Ĝenerala => Iloj => Weixin Pay => Ebligi”. (uzi kiam sendas Ethereum de Coinbase) kopiita! kopio (plej malalta minimuma kvanto) (averto: alta minimuma kvanto) -%(percentage)s%% 12 monatoj 1 monato 24 monatoj 3 monatoj 48 monatoj 6 monatoj 96 monatoj Elektu kiom longe vi volas aboni. <div %(div_monthly_cost)s></div><div %(div_after)s>post <span %(span_discount)s></span> rabatoj</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% por 12 monatoj por 1 monato dum 24 monatoj por 3 monatoj dum 48 monatoj por 6 monatoj dum 96 monatoj %(monthly_cost)s / monato kontaktu nin Rektaj <strong>SFTP</strong> serviloj Donaco aŭ interŝanĝo je entreprena nivelo por novaj kolektoj (ekz. novaj skanaĵoj, OCR-igitaj datasets). Sperta Alirado <strong>Senlima</strong> altrapida aliro <div %(div_question)s>Ĉu mi povas ĝisdatigi mian membrecon aŭ akiri plurajn membrecojn?</div> <div %(div_question)s>Ĉu mi povas fari donacon sen fariĝi membro?</div> Kompreneble. Ni akceptas donacojn de ajna sumo ĉe ĉi tiu Monero (XMR) adreso: %(address)s. <div %(div_question)s>Kion signifas la gamoj monate?</div> Vi povas atingi la pli malaltan flankon de gamo aplikante ĉiujn rabatojn, kiel elekti periodon pli longan ol unu monato. <div %(div_question)s>Ĉu membrecoj aŭtomate renovigas?</div> Membrecoj <strong>ne</strong> aŭtomate renovigas. Vi povas partopreni tiel longe aŭ mallonge kiel vi volas. <div %(div_question)s>Por kio vi elspezas donacojn?</div> 100%% iras al konservado kaj alirebligo de la monda scio kaj kulturo. Nuntempe ni plejparte elspezas ĝin por serviloj, stokado, kaj bendolarĝo. Neniu mono iras al iuj teamanoj persone. <div %(div_question)s>Ĉu mi povas fari grandan donacon?</div> Tio estus mirinda! Por donacoj super kelkaj mil dolaroj, bonvolu kontakti nin rekte ĉe %(email)s. <div %(div_question)s>Ĉu vi havas aliajn pagmetodojn?</div> Nuntempe ne. Multaj homoj ne volas, ke arkivoj kiel ĉi tiu ekzistu, do ni devas esti zorgemaj. Se vi povas helpi nin starigi aliajn (pli oportunajn) pagmetodojn sekure, bonvolu kontakti nin ĉe %(email)s. Donaca FAQ Vi havas <a %(a_donation)s>ekzistantan donacon</a> en progreso. Bonvolu fini aŭ nuligi tiun donacon antaŭ ol fari novan donacon. <a %(a_all_donations)s>Vidi ĉiujn miajn donacojn</a> Por donacoj super $5000 bonvolu kontakti nin rekte ĉe %(email)s. Ni bonvenigas grandajn donacojn de riĉaj individuoj aŭ institucioj.  Sciu, ke kvankam la membrecoj sur ĉi tiu paĝo estas "po monato", ili estas unufojaj donacoj (ne ripetiĝantaj). Vidu la <a %(faq)s>Donacan FAQ</a>. La Arkivo de Anna estas neprofitcela, malfermfonta, malfermdatuma projekto. Donacante kaj fariĝante membro, vi subtenas niajn operaciojn kaj disvolviĝon. Al ĉiuj niaj membroj: dankon pro teni nin funkciantaj! ❤️ Por pliaj informoj, kontrolu la <a %(a_donate)s>Donacan FAQ</a>. Por fariĝi membro, bonvolu <a %(a_login)s>Ensaluti aŭ Registriĝi</a>. Dankon pro via subteno! $%(cost)s / monato Se vi faris eraron dum pago, ni ne povas fari repagojn, sed ni provos ripari ĝin. Trovu la paĝon “Kripto” en via PayPal-aplikaĵo aŭ retejo. Tio kutime estas sub “Financoj”. Iru al la paĝo “Bitcoin” en via PayPal-aplikaĵo aŭ retejo. Premu la butonon “Transdoni” %(transfer_icon)s, kaj poste “Sendi”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Donackarto %(amazon)s donackarto Banka karto Banka karto (uzante aplikaĵon) Binance Kreditkarto/debetkarto/Apple/Google (BMC) Cash App Kredit/debetkarto Kredit-/debeto-karto 2 Kredit/debetkarto (rezerva) Kripto %(bitcoin_icon)s Karto / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regula) Pix(Brazilo) Revolut (provize ne disponebla) WeChat Elektu vian preferatan kriptomonon: Donacu uzante Amazon donackarton. <strong>GRAVE:</strong> Ĉi tiu opcio estas por %(amazon)s. Se vi volas uzi alian retejon de Amazon, elektu ĝin supre. <strong>GRAVE:</strong> Ni nur subtenas Amazon.com, ne aliajn Amazon-retejojn. Ekzemple, .de, .co.uk, .ca, NE estas subtenataj. Bonvolu NE skribi vian propran mesaĝon. Enigu la ĝustan sumon: %(amount)s Notu, ke ni devas rondigi al kvantoj akceptitaj de niaj revendistoj (minimumo %(minimum)s). Donacu uzante kredit-/debetkarton, per la Alipay-aplikaĵo (tre facile agordi). Instalu la Alipay-aplikaĵon el la <a %(a_app_store)s>Apple App Store</a> aŭ <a %(a_play_store)s>Google Play Store</a>. Registriĝu uzante vian telefonnumeron. Neniuj pliaj personaj detaloj estas postulataj. <span %(style)s>1</span>Instalu la Alipay-aplikaĵon Subtenataj: Visa, MasterCard, JCB, Diners Club kaj Discover. Vidu <a %(a_alipay)s>ĉi tiun gvidilon</a> por pli da informoj. <span %(style)s>2</span>Aldonu bankokarton Kun Binance, vi aĉetas Bitcoin per kredit-/debetkarto aŭ banka konto, kaj poste donacas tiun Bitcoin al ni. Tiel ni povas resti sekuraj kaj anonimaj kiam ni akceptas vian donacon. Binance estas disponebla en preskaŭ ĉiu lando, kaj subtenas plej multajn bankojn kaj kredit/debetkartojn. Ĉi tio estas nuntempe nia ĉefa rekomendo. Ni dankas vin pro preni la tempon por lerni kiel donaci uzante ĉi tiun metodon, ĉar ĝi multe helpas nin. Por kreditkartoj, debetkartoj, Apple Pay, kaj Google Pay, ni uzas “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). En ilia sistemo, unu “kafo” egalas al $5, do via donaco estos rondigita al la plej proksima multoblo de 5. Donaci uzante Cash App. Se vi havas Cash App, ĉi tio estas la plej facila maniero donaci! Notu, ke por transakcioj sub %(amount)s, Cash App povas ŝargi %(fee)s kotizon. Por %(amount)s aŭ pli, ĝi estas senpaga! Donacu per kreditkarto aŭ debetkarto. Ĉi tiu metodo uzas kriptomonan provizanton kiel mezan konvertiĝon. Ĉi tio povas esti iom konfuziga, do bonvolu uzi ĉi tiun metodon nur se aliaj pagmetodoj ne funkcias. Ĝi ankaŭ ne funkcias en ĉiuj landoj. Ni ne povas subteni kredit-/debetkartojn rekte, ĉar bankoj ne volas kunlabori kun ni. ☹ Tamen, ekzistas pluraj manieroj uzi kredit-/debetkartojn, uzante aliajn pagmetodojn: Kun kripto vi povas donaci uzante BTC, ETH, XMR, kaj SOL. Uzu ĉi tiun opcion se vi jam konas kriptovaluton. Kun kripto vi povas donaci uzante BTC, ETH, XMR, kaj pli. Kripto rapidaj servoj Se vi uzas kripton por la unua fojo, ni sugestas uzi %(options)s por aĉeti kaj donaci Bitcoin (la originala kaj plej uzata kriptovaluto). Notu, ke por malgrandaj donacoj la kreditkartaj kotizoj povas nuligi nian %(discount)s%% rabaton, do ni rekomendas pli longajn abonojn. Donacu uzante kredit/debetkarton, PayPal, aŭ Venmo. Vi povas elekti inter ĉi tiuj sur la sekva paĝo. Google Pay kaj Apple Pay ankaŭ povus funkcii. Notu, ke por malgrandaj donacoj la kotizoj estas altaj, do ni rekomendas pli longajn abonojn. Por donaci uzante PayPal US, ni uzos PayPal Crypto, kiu permesas al ni resti anonimaj. Ni dankas vin pro preni la tempon por lerni kiel donaci uzante ĉi tiun metodon, ĉar ĝi multe helpas nin. Donacu uzante PayPal. Donacu uzante vian regulan PayPal-konton. Donacu uzante Revolut. Se vi havas Revolut, ĉi tio estas la plej facila maniero donaci! Ĉi tiu pagmaniero permesas nur maksimumon de %(amount)s. Bonvolu elekti alian daŭron aŭ pagmanieron. Ĉi tiu pagmaniero postulas minimumon de %(amount)s. Bonvolu elekti alian daŭron aŭ pagmanieron. Binance Coinbase Kraken Bonvolu elekti pagmanieron. “Adoptu torenton”: via salutnomo aŭ mesaĝo en torenta dosiernomo <div %(div_months)s>unufoje ĉiun 12 monatojn de membreco</div> Via salutnomo aŭ anonima mencio en la kreditoj Frua aliro al novaj funkcioj Ekskluziva Telegram kun malantaŭ-kulisaĵoj ĝisdatigoj %(number)s rapidaj elŝutoj tage se vi donacos ĉi-monate! <a %(a_api)s>JSON API</a> aliro Legenda statuso en konservado de la scio kaj kulturo de la homaro Antaŭaj avantaĝoj, plus: Gajnu <strong>%(percentage)s%% bonusajn elŝutojn</strong> per <a %(a_refer)s>referencado de amikoj</a>. SciDB artikoloj <strong>senlima</strong> sen kontrolo Kiam vi demandas pri kontaj aŭ donacaj aferoj, aldonu vian kontan ID, ekrankopiojn, kvitancojn, kiel eble plej multe da informoj. Ni kontrolas nian retpoŝton nur ĉiun 1-2 semajnojn, do ne inkluzivi ĉi tiujn informojn malfruigos ajnan solvon. Por akiri eĉ pli da elŝutoj, <a %(a_refer)s>referu viajn amikojn</a>! Ni estas malgranda teamo de volontuloj. Eble daŭros 1-2 semajnojn por respondi. Notu, ke la konta nomo aŭ bildo eble aspektas strange. Ne necesas zorgi! Ĉi tiuj kontoj estas administrataj de niaj donacaj partneroj. Niaj kontoj ne estis hakitaj. Donacu <span %(span_cost)s></span> <span %(span_label)s></span> por 12 monatoj “%(tier_name)s” por 1 monato “%(tier_name)s” dum 24 monatoj “%(tier_name)s” dum 3 monatoj “%(tier_name)s” dum 48 monatoj “%(tier_name)s” por 6 monatoj “%(tier_name)s” dum 96 monatoj “%(tier_name)s” Vi ankoraŭ povas nuligi la donacon dum la pago. Alklaku la donacbutonon por konfirmi ĉi tiun donacon. <strong>Grava noto:</strong> Kriptaj prezoj povas fluktuadi sovaĝe, foje eĉ ĝis 20%% en kelkaj minutoj. Tio tamen estas malpli ol la kotizoj, kiujn ni suferas kun multaj pagprovizantoj, kiuj ofte ŝargas 50-60%% por labori kun “ombra bonfarado” kiel ni. <u>Se vi sendas al ni la kvitancon kun la originala prezo, kiun vi pagis, ni ankoraŭ kreditos vian konton por la elektita membreco</u> (kondiĉe ke la kvitanco ne estas pli malnova ol kelkaj horoj). Ni vere dankas, ke vi pretas elteni tiajn aferojn por subteni nin! ❤️ ❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi. <span %(span_circle)s>1</span>Aĉetu Bitcoin per Paypal <span %(span_circle)s>2</span>Transdonu la Bitcoin al nia adreso ✅ Redirektante al la donacpaĝo… Donaci Bonvolu atendi almenaŭ <span %(span_hours)s>24 horojn</span> (kaj refreŝigi ĉi tiun paĝon) antaŭ ol kontakti nin. Se vi ŝatus fari donacon (iu ajn sumo) sen membreco, bonvolu uzi ĉi tiun Monero (XMR) adreson: %(address)s. Post sendado de via donackarto, nia aŭtomata sistemo konfirmos ĝin ene de kelkaj minutoj. Se tio ne funkcias, provu resendi vian donackarton (<a %(a_instr)s>instrukcioj</a>). Se tio ankoraŭ ne funkcias, bonvolu retpoŝti al ni kaj Anna mane revizios ĝin (tio povas daŭri kelkajn tagojn), kaj certigu mencii se vi jam provis resendi. Ekzemplo: Bonvolu uzi la <a %(a_form)s>oficialan Amazon.com formularon</a> por sendi al ni donackarton de %(amount)s al la retpoŝtadreso sube. "Al" ricevanto retpoŝto en la formo: Amazon-donackarto Ni ne povas akcepti aliajn metodojn de donackartoj, <strong>nur senditajn rekte de la oficiala formularo ĉe Amazon.com</strong>. Ni ne povas redoni vian donackarton se vi ne uzas ĉi tiun formularon. Uzu nur unufoje. Unika por via konto, ne kundividu. Atendante donackarton… (refreŝigu la paĝon por kontroli) Malfermu la <a %(a_href)s>QR-koda donacpaĝo</a>. Skanu la QR-kodon per la Alipay-aplikaĵo, aŭ premu la butonon por malfermi la Alipay-aplikaĵon. Bonvolu esti pacienca; la paĝo eble bezonos iom da tempo por ŝarĝi ĉar ĝi estas en Ĉinio. <span %(style)s>3</span>Faru donacon (skanu QR-kodon aŭ premu butonon) Aĉeti PYUSD moneron en PayPal Aĉetu Bitcoin (BTC) en Cash App Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas. Iru al la paĝo “Bitcoin” (BTC) en Cash App. Transdonu la Bitcoin al nia adreso Por malgrandaj donacoj (sub $25), vi eble devos uzi Rush aŭ Priority. Alklaku la butonon “Send bitcoin” por fari “retiron”. Ŝanĝu de dolaroj al BTC premante la %(icon)s ikonon. Enigu la BTC-sumon sube kaj alklaku “Send”. Vidu <a %(help_video)s>ĉi tiun videon</a> se vi blokas. Rapidaj servoj estas oportunaj, sed ŝargas pli altajn kotizojn. Vi povas uzi ĉi tion anstataŭ kripta interŝanĝo se vi serĉas rapide fari pli grandan donacon kaj ne ĝenas kotizo de $5-10. Certiĝu sendi la ĝustan kriptan sumon montritan sur la donacpaĝo, ne la sumon en $USD. Alie la kotizo estos subtrahita kaj ni ne povas aŭtomate prilabori vian membrecon. Foje konfirmo povas daŭri ĝis 24 horoj, do certigu refreŝigi ĉi tiun paĝon (eĉ se ĝi eksvalidiĝis). Kreditkarto / debetkarto instrukcioj Donaci per nia kreditkarta / debetkarta paĝo Kelkaj el la paŝoj mencias kriptomonedajn monujojn, sed ne zorgu, vi ne devas lerni ion ajn pri kripto por ĉi tio. %(coin_name)s instrukcioj Skanu ĉi tiun QR -kodon per via Crypto -monujo por plenigi la pagajn detalojn Skanu QR -kodon por pagi Ni subtenas nur la norman version de kriptomonedoj, ne ekzotikajn retojn aŭ versiojn de moneroj. Ĝi povas daŭri ĝis unu horo por konfirmi la transakcion, depende de la monero. Donaci %(amount)s en <a %(a_page)s>ĉi tiu paĝo</a>. Ĉi tiu donaco eksvalidiĝis. Bonvolu nuligi kaj krei novan. Se vi jam pagis: Jes, mi sendis mian kvitancon per retpoŝto Se la kripta kurzo ŝanĝiĝis dum la transakcio, certigu inkluzivi la kvitancon montrantan la originalan kurzon. Ni vere dankas vin pro la peno uzi kripton, ĝi multe helpas nin! ❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi. <span %(span_circle)s>%(circle_number)s</span>Retpoŝtu al ni la kvitancon Se vi renkontas ajnajn problemojn, bonvolu kontakti nin ĉe %(email)s kaj inkluzivi kiel eble plej multe da informoj (kiel ekrankopiojn). ✅ Dankon pro via donaco! Anna mane aktivigos vian membrecon ene de kelkaj tagoj. Sendu kvitancon aŭ ekrankopion al via persona kontroladreso: Kiam vi sendis vian kvitancon per retpoŝto, alklaku ĉi tiun butonon, por ke Anna mane reviziu ĝin (tio povas daŭri kelkajn tagojn): Sendu kvitancon aŭ ekrankopion al via persona kontroladreso. NE uzu ĉi tiun retpoŝtadreson por via PayPal-donaco. Nuligi Jes, bonvolu nuligi Ĉu vi certas, ke vi volas nuligi? Ne nuligu se vi jam pagis. ❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovu. Faru novan donacon ✅ Via donaco estis nuligita. Dato: %(date)s Identigilo: %(id)s Reordigi Statuso: <span %(span_label)s>%(label)s</span> Sume: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / monato por %(duration)s monatoj, inkluzive de %(discounts)s%% rabato)</span> Sume: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / monato por %(duration)s monatoj)</span> 1. Enigu vian retpoŝton. 2. Elektu vian pagmanieron. 3. Elektu vian pagmanieron denove. 4. Elektu “Mem-gastigita” monujo. 5. Alklaku “Mi konfirmas posedon”. 6. Vi devus ricevi retpoŝtan kvitancon. Bonvolu sendi tion al ni, kaj ni konfirmos vian donacon kiel eble plej baldaŭ. (vi eble volas nuligi kaj krei novan donacon) La paginstrukcioj nun estas malaktualaj. Se vi volas fari alian donacon, uzu la butonon "Reordigi" supre. Vi jam pagis. Se vi volas revizii la pagajn instrukciojn tamen, alklaku ĉi tie: Montri malnovajn paginstrukciojn Se la donacpaĝo estas blokita, provu alian interretan konekton (ekz. VPN aŭ telefona interreto). Bedaŭrinde, la Alipay-paĝo ofte estas alirebla nur el <strong>ĉeftero Ĉinio</strong>. Vi eble devos provizore malŝalti vian VPN, aŭ uzi VPN al ĉeftero Ĉinio (aŭ foje ankaŭ funkcias Honkongo). <span %(span_circle)s>1</span>Donaci per Alipay Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun Alipay-konton</a> Instrukcioj por Alipay <span %(span_circle)s>1</span>Transdonu al unu el niaj kriptaj kontoj Donacu la tutan sumon de %(total)s al unu el ĉi tiuj adresoj: Kriptaj instrukcioj Sekvu la instrukciojn por aĉeti Bitcoin (BTC). Vi nur bezonas aĉeti la sumon, kiun vi volas donaci, %(total)s. Enigu nian Bitcoin (BTC) adreson kiel la ricevanton, kaj sekvu la instrukciojn por sendi vian donacon de %(total)s: <span %(span_circle)s>1</span>Donaci per Pix Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun Pix-konton Instrukcioj por Pix <span %(span_circle)s>1</span>Donaci per WeChat Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun WeChat-konton</a> Instrukcioj por WeChat Uzu iun ajn el la sekvaj “kreditkarto al Bitcoin” rapidaj servoj, kiuj nur daŭras kelkajn minutojn: BTC / Bitcoin adreso (ekstera monujo): BTC / Bitcoin kvanto: Plenigu la jenajn detalojn en la formularo: Se iu el ĉi tiu informo estas malaktuala, bonvolu retpoŝti al ni por sciigi nin. Bonvolu uzi ĉi tiun <span %(underline)s>ĝustan kvanton</span>. Via totala kosto eble estos pli alta pro kreditkartaj kotizoj. Por malgrandaj kvantoj ĉi tio bedaŭrinde povas esti pli ol nia rabato. (minimumo: %(minimum)s, neniu kontrolo por la unua transakcio) (minimumo: %(minimum)s) (minimumo: %(minimum)s) (minimumo: %(minimum)s, neniu kontrolo por la unua transakcio) (minimumo: %(minimum)s) (minimumo: %(minimum)s depende de lando, neniu kontrolo por la unua transakcio) Sekvu la instrukciojn por aĉeti PYUSD-monon (PayPal USD). Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas. Iru al la paĝo “PYUSD” en via PayPal-aplikaĵo aŭ retejo. Premu la butonon “Transfer” %(icon)s, kaj poste “Send”. Ĝisdatigi statuson Por restarigi la tempigilon, simple kreu novan donacon. Nepre uzu la kvanton de BTC sube, <em>NE</em> eŭroj aŭ dolaroj, alie ni ne ricevos la ĝustan sumon kaj ne povos aŭtomate konfirmi vian membrecon. Aĉetu Bitcoin (BTC) en Revolut Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas. Iru al la paĝo “Crypto” en Revolut por aĉeti Bitcoin (BTC). Transdonu la Bitcoin al nia adreso Por malgrandaj donacoj (sub $25) vi eble devos uzi Rush aŭ Priority. Alklaku la butonon “Send bitcoin” por fari “retiron”. Ŝanĝu de eŭroj al BTC premante la %(icon)s ikonon. Enigu la BTC-sumon sube kaj alklaku “Send”. Vidu <a %(help_video)s>ĉi tiun videon</a> se vi blokas. Statuso: 1 2 Paŝo-post-paŝa gvidilo Vidu la paŝon post paŝo gvidilon sube. Alie vi povus esti eksigita el ĉi tiu konto! Se vi ankoraŭ ne faris, notu vian sekretan ŝlosilon por ensaluti: Dankon pro via donaco! Restanta tempo: Donaco Transdoni %(amount)s al %(account)s Atendante konfirmon (refreŝigu la paĝon por kontroli)… Atendante translokigon (refreŝigu la paĝon por kontroli)… Pli frue Rapidaj elŝutoj en la lastaj 24 horoj kalkuliĝas al la ĉiutaga limo. Elŝutoj de Rapidaj Partneraj Serviloj estas markitaj per %(icon)s. Lasta 18 horoj Neniuj dosieroj elŝutitaj ankoraŭ. Elŝutitaj dosieroj ne estas publike montrataj. Ĉiuj tempoj estas en UTC. Elŝutitaj dosieroj Se vi elŝutis dosieron kun kaj rapidaj kaj malrapidaj elŝutoj, ĝi aperos dufoje. Ne tro zorgu, estas multaj homoj elŝutantaj de retejoj ligitaj de ni, kaj estas ekstreme malofte eniri problemojn. Tamen, por resti sekura, ni rekomendas uzi VPN (pagita), aŭ <a %(a_tor)s>Tor</a> (senpaga). Mi elŝutis 1984 de George Orwell, ĉu la polico venos al mia pordo? Vi estas Anna! Kiu estas Anna? Ni havas unu stabilan JSON API por membroj, por akiri rapidan elŝutan URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentado ene de la JSON mem). Por aliaj uzoj, kiel ekzemple iterado tra ĉiuj niaj dosieroj, konstruado de personigita serĉo, kaj tiel plu, ni rekomendas <a %(a_generate)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. La krudaj datumoj povas esti mane esploritaj <a %(a_explore)s>per JSON-dosieroj</a>. Nia kruda torenta listo ankaŭ povas esti elŝutita kiel <a %(a_torrents)s>JSON</a>. Ĉu vi havas API? Ni ne gastigas iujn ajn kopirajtitajn materialojn ĉi tie. Ni estas serĉilo, kaj kiel tia nur indeksas metadatenojn kiuj jam estas publike disponeblaj. Kiam vi elŝutas de ĉi tiuj eksteraj fontoj, ni sugestus kontroli la leĝojn en via jurisdikcio rilate al tio, kio estas permesata. Ni ne respondecas pri enhavo gastigita de aliaj. Se vi havas plendojn pri tio, kion vi vidas ĉi tie, via plej bona veto estas kontakti la originalan retejon. Ni regule alportas iliajn ŝanĝojn en nian datumbazon. Se vi vere pensas, ke vi havas validan DMCA-plendon, kiun ni devus respondi, bonvolu plenigi la <a %(a_copyright)s>DMCA / Kopirajta plendoformularo</a>. Ni prenas viajn plendojn serioze, kaj respondos al vi kiel eble plej baldaŭ. Kiel mi raportu kopirajtan malobservon? Jen kelkaj libroj kiuj havas specialan signifon por la mondo de ombraj bibliotekoj kaj cifereca konservado: Kiaj estas viaj plej ŝatataj libroj? Ni ankaŭ ŝatus rememori ĉiujn, ke nia tuta kodo kaj datumoj estas tute malfermfontaj. Ĉi tio estas unika por projektoj kiel la nia — ni ne konas iun ajn alian projekton kun simile masiva katalogo kiu estas ankaŭ tute malfermfonta. Ni tre bonvenigas iun ajn, kiu pensas ke ni malbone administras nian projekton, preni nian kodon kaj datumojn kaj starigi sian propran ombran bibliotekon! Ni ne diras ĉi tion el malbonvolo aŭ io simila — ni vere pensas ke ĉi tio estus mirinda ĉar ĝi levus la nivelon por ĉiuj, kaj pli bone konservus la heredaĵon de la homaro. Mi malamas kiel vi administras ĉi tiun projekton! Ni ŝatus, ke homoj starigu <a %(a_mirrors)s>spegulojn</a>, kaj ni finance subtenos tion. Kiel mi povas helpi? Ni ja faras. Nia inspiro por kolekti metadatenojn estas la celo de Aaron Swartz "unu retpaĝo por ĉiu libro iam ajn publikigita", por kiu li kreis <a %(a_openlib)s>Open Library</a>. Tiu projekto bone sukcesis, sed nia unika pozicio permesas al ni akiri metadatenojn, kiujn ili ne povas. Alia inspiro estis nia deziro scii <a %(a_blog)s>kiom da libroj ekzistas en la mondo</a>, por ke ni povu kalkuli kiom da libroj ni ankoraŭ devas savi. Ĉu vi kolektas metadatenojn? Notu, ke mhut.org blokas certajn IP-gamojn, do VPN eble estos necesa. <strong>Android:</strong> Alklaku la tri-punktan menuon en la supra dekstra angulo, kaj elektu “Aldoni al Hejma Ekrano”. <strong>iOS:</strong> Alklaku la butonon “Kunhavigi” ĉe la fundo, kaj elektu “Aldoni al Hejma Ekrano”. Ni ne havas oficialan poŝtelefonan aplikaĵon, sed vi povas instali ĉi tiun retejon kiel aplikaĵon. Ĉu vi havas poŝtelefonan aplikaĵon? Bonvolu sendi ilin al la <a %(a_archive)s>Internet Archive</a>. Ili ĝuste konservos ilin. Kiel mi povas donaci librojn aŭ aliajn fizikajn materialojn? Kiel mi petas librojn? <a %(a_blog)s>Blogo de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regulaj ĝisdatigoj <a %(a_software)s>Programaro de Anna</a> — nia malfermfonta kodo <a %(a_datasets)s>Datenserioj</a> — pri la datumoj <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativaj domajnoj Ĉu estas pli da rimedoj pri la Arkivo de Anna? <a %(a_translate)s>Traduki en la Programaro de Anna</a> — nia traduksistemo <a %(a_wikipedia)s>Vikipedio</a> — pli pri ni (bonvolu helpi teni ĉi tiun paĝon ĝisdatigita, aŭ krei unu por via propra lingvo!) Elektu la agordojn kiujn vi ŝatas, lasu la serĉkeston malplena, alklaku “Serĉi”, kaj tiam marku la paĝon uzante la markilon de via retumilo. Kiel mi konservas miajn serĉajn agordojn? Ni bonvenigas sekurecajn esploristojn serĉi vundeblecojn en niaj sistemoj. Ni estas grandaj subtenantoj de respondeca malkovro. Kontaktu nin <a %(a_contact)s>ĉi tie</a>. Ni nuntempe ne povas doni cimo-premiojn, krom por vundeblecoj kiuj havas la <a %(a_link)s>potencialon kompromiti nian anonimecon</a>, por kiuj ni ofertas premiojn en la gamo de $10k-50k. Ni ŝatus oferti pli vastan amplekson por cimo-premioj en la estonteco! Bonvolu noti, ke sociaj inĝenieraj atakoj estas ekster la amplekso. Se vi interesiĝas pri ofenda sekureco kaj volas helpi arkivi la mondan scion kaj kulturon, nepre kontaktu nin. Estas multaj manieroj kiel vi povas helpi. Ĉu vi havas programon por respondeca malkovro? Ni laŭvorte ne havas sufiĉe da rimedoj por doni al ĉiuj en la mondo altrapidajn elŝutojn, kiom ajn ni ŝatus. Se riĉa bonfaranto volus paŝi antaŭen kaj provizi tion por ni, tio estus nekredebla, sed ĝis tiam, ni faras nian plej bonan. Ni estas neprofitcela projekto, kiu apenaŭ povas subteni sin per donacoj. Tial ni efektivigis du sistemojn por senpagaj elŝutoj, kun niaj partneroj: komunaj serviloj kun malrapidaj elŝutoj, kaj iomete pli rapidaj serviloj kun atendolisto (por redukti la nombron de homoj elŝutantaj samtempe). Ni ankaŭ havas <a %(a_verification)s>retumilan konfirmon</a> por niaj malrapidaj elŝutoj, ĉar alie robotoj kaj skrapiloj misuzos ilin, farante aferojn eĉ pli malrapidaj por legitimaj uzantoj. Notu, ke uzante la Tor-Retumilon, vi eble devos agordi viajn sekurecajn agordojn. En la plej malalta el la opcioj, nomata “Normala”, la Cloudflare-turnstila defio sukcesas. En la pli altaj opcioj, nomataj “Pli Sekura” kaj “Plej Sekura”, la defio malsukcesas. Por grandaj dosieroj foje malrapidaj elŝutoj povas interrompiĝi meze. Ni rekomendas uzi elŝutan administrilon (kiel JDownloader) por aŭtomate rekomenci grandajn elŝutojn. Kial la malrapidaj elŝutoj estas tiel malrapidaj? Oftaj Demandoj (FAQ) Uzu la <a %(a_list)s>torentan listogeneratoron</a> por generi liston de torentoj kiuj plej bezonas semadon, ene de viaj limoj de stokado. Jes, vidu la paĝon <a %(a_llm)s>LLM datenoj</a>. Plej multaj torentoj enhavas la dosierojn rekte, kio signifas ke vi povas instrui torentajn klientojn elŝuti nur la bezonatajn dosierojn. Por determini kiujn dosierojn elŝuti, vi povas <a %(a_generate)s>generi</a> niajn metadatenojn, aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. Bedaŭrinde, kelkaj torentaj kolektoj enhavas .zip aŭ .tar dosierojn ĉe la radiko, en kiu kazo vi devas elŝuti la tutan torenton antaŭ ol povi elekti individuajn dosierojn. Neniuj facilaj iloj por filtri torentojn estas disponeblaj ankoraŭ, sed ni bonvenigas kontribuojn. (Ni tamen havas <a %(a_ideas)s>kelkajn ideojn</a> por la lasta kazo.) Longa respondo: Mallonga respondo: ne facile. Ni provas teni minimuman duplikadon aŭ interkovron inter la torentoj en ĉi tiu listo, sed tio ne ĉiam eblas, kaj dependas multe de la politikoj de la fontaj bibliotekoj. Por bibliotekoj kiuj eldonas siajn proprajn torentojn, tio estas ekster niaj manoj. Por torentoj eldonitaj de Arkivo de Anna, ni deduplikiĝas nur bazite sur MD5-hash, kio signifas ke malsamaj versioj de la sama libro ne estas deduplikitaj. Ĉu. Ĉi tiuj estas fakte PDF-oj kaj EPUB-oj, ili simple ne havas etendaĵon en multaj el niaj torentoj. Estas du lokoj kie vi povas trovi la metadatenojn por torentaj dosieroj, inkluzive de la dosiertipoj/etendaĵoj: 1. Ĉiu kolekto aŭ eldono havas siajn proprajn metadatenojn. Ekzemple, <a %(a_libgen_nonfic)s>Libgen.rs torentoj</a> havas respondan metadatenan datumbazon gastigitan en la retejo de Libgen.rs. Ni tipe ligas al rilataj metadatenaj rimedoj de ĉiu kolekto <a %(a_datasets)s>paĝo de datenserio</a>. 2. Ni rekomendas <a %(a_generate)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. Ĉi tiuj enhavas mapadon por ĉiu registro en la Arkivo de Anna al ĝiaj respondaj torentaj dosieroj (se disponeblaj), sub "torrent_paths" en la ElasticSearch JSON. Iuj torentklientoj ne subtenas grandajn pecetgrandecojn, kiujn multaj el niaj torentoj havas (por pli novaj ni ne plu faras tion — eĉ se ĝi estas valida laŭ specifoj!). Do provu alian klienton se vi renkontas ĉi tion, aŭ plendu al la kreintoj de via torentkliento. Mi ŝatus helpi semadon, sed mi ne havas multan diskospacon. La torentoj estas tro malrapidaj; ĉu mi povas elŝuti la datumojn rekte de vi? Ĉu mi povas elŝuti nur subaron de la dosieroj, kiel nur apartan lingvon aŭ temon? Kiel vi traktas duplikatojn en la torentoj? Ĉu mi povas akiri la torentan liston kiel JSON? Mi ne vidas PDF-ojn aŭ EPUB-ojn en la torentoj, nur binarajn dosierojn? Kion mi faru? Kial mia torentkliento ne povas malfermi iujn el viaj torentdosieroj / magnetligiloj? Torentaj Oftaj Demandoj Kiel mi alŝutas novajn librojn? Bonvolu vidi <a %(a_href)s>ĉi tiun bonegan projekton</a>. Ĉu vi havas monitoron de funkciado? Kio estas la Arkivo de Anna? Fariĝu membro por uzi rapidajn elŝutojn. Ni nun subtenas Amazon-donackartojn, kredit- kaj debetkartojn, kripton, Alipay, kaj WeChat. Vi elĉerpis rapidajn elŝutojn hodiaŭ. Alirado Horaj elŝutoj en la lastaj 30 tagoj. Horo-mezumo: %(hourly)s. Tago-mezumo: %(daily)s. Ni kunlaboras kun partneroj por fari niajn kolektojn facile kaj senpage alireblaj por ĉiuj. Ni kredas, ke ĉiuj havas rajton al la kolektiva saĝo de la homaro. Kaj <a %(a_search)s>ne je la kosto de aŭtoroj</a>. La datasets uzataj en Arkivo de Anna estas tute malfermaj, kaj povas esti spegulitaj amase uzante torentojn. <a %(a_datasets)s>Lernu pli…</a> Longtempa arkivo Plena datumbazo Serĉi Libroj, artikoloj, revuoj, bildstrioj, bibliotekaj rekordoj, metadatenoj, … Ĉiuj niaj <a %(a_code)s>kodoj</a> kaj <a %(a_datasets)s>datumoj</a> estas tute malfermitaj. <span %(span_anna)s>La Arkivo de Anna</span> estas neprofitcela projekto kun du celoj: <li><strong>Konservado:</strong> Rezervi ĉiun scion kaj kulturon de la homaro.</li><li><strong>Alirebleco:</strong> Fari ĉi tiun scion kaj kulturon disponebla al iu ajn en la mondo.</li> Ni havas la plej grandan kolekton de altkvalita teksta datumaro en la mondo. <a %(a_llm)s>Lernu pli…</a> LLM training data 🪩 Speguloj: alvoko por volontuloj Se vi administras alt-riskan anoniman pagprocesoron, bonvolu kontakti nin. Ni ankaŭ serĉas homojn por meti gustumajn malgrandajn reklamojn. Ĉiuj enspezoj iras al niaj konservaj klopodoj. Konservado Ni taksas, ke ni konservis ĉirkaŭ <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% el la mondaj libroj</a>. Ni konservas librojn, artikolojn, bildstriojn, revuojn, kaj pli, alportante ĉi tiujn materialojn el diversaj <a href="https://en.wikipedia.org/wiki/Shadow_library">ombraj bibliotekoj</a>, oficialaj bibliotekoj, kaj aliaj kolektoj kune en unu loko. Ĉiuj ĉi tiuj datumoj estas konservitaj por ĉiam per faciligado de ilia amasa duplikado — uzante torentojn — rezultigante multajn kopiojn ĉirkaŭ la mondo. Iuj ombraj bibliotekoj jam faras tion mem (ekz. Sci-Hub, Library Genesis), dum la Arkivo de Anna “liberigas” aliajn bibliotekojn, kiuj ne ofertas amasan distribuadon (ekz. Z-Library) aŭ tute ne estas ombraj bibliotekoj (ekz. Internet Archive, DuXiu). Ĉi tiu vasta distribuo, kombinita kun malfermfonta kodo, faras nian retejon rezistema al malaktivigoj, kaj certigas la longdaŭran konservadon de la scio kaj kulturo de la homaro. Lernu pli pri <a href="/datasets">niaj datasets</a>. Se vi estas <a %(a_member)s>membro</a>, retumila kontrolo ne estas postulata. 🧬&nbsp;SciDB estas daŭrigo de Sci-Hub. SciDB Malfermi DOI Sci-Hub <a %(a_paused)s>paŭzis</a> la alŝutadon de novaj artikoloj. Rekta aliro al %(count)s akademiaj artikoloj 🧬&nbsp;SciDB estas daŭrigo de Sci-Hub, kun sia konata interfaco kaj rekta vidado de PDF-oj. Enigu vian DOI por vidi. Ni havas la tutan kolekton de Sci-Hub, same kiel novajn artikolojn. Plej multaj povas esti viditaj rekte kun konata interfaco, simila al Sci-Hub. Iuj povas esti elŝutitaj per eksteraj fontoj, en kiu kazo ni montras ligojn al tiuj. Vi povas multe helpi per semado de torentoj. <a %(a_torrents)s>Lernu pli…</a> >%(count)s semantoj <%(count)s semantoj %(count_min)s–%(count_max)s semantoj 🤝 Serĉante volontulojn Kiel neprofitcela, malfermfonta projekto, ni ĉiam serĉas homojn por helpi. IPFS elŝutoj Listo de %(by)s, kreita <span %(span_time)s>%(time)s</span> Konservi ❌ Io fuŝiĝis. Bonvolu reprovi. ✅ Konservita. Bonvolu reŝargi la paĝon. Listo estas malplena. redakti Aldonu aŭ forigu el ĉi tiu listo trovante dosieron kaj malfermante la langeton “Listoj”. Listo Kiel ni povas helpi Forigo de supermeto (deduplikado) Teksto kaj metadatenoj ekstraktado OCR Ni kapablas provizi altrapidan aliron al niaj plenaj kolektoj, same kiel al nepublikigitaj kolektoj. Ĉi tio estas entreprena nivela aliro, kiun ni povas provizi kontraŭ donacoj en la gamo de dekoj da miloj da USD. Ni ankaŭ pretas interŝanĝi ĉi tion kontraŭ altkvalitaj kolektoj, kiujn ni ankoraŭ ne havas. Ni povas repagi vin se vi kapablas provizi al ni riĉigon de niaj datumoj, kiel: Subtenu longdaŭran arkivadon de homa scio, dum vi ricevas pli bonajn datumojn por via modelo! <a %(a_contact)s>Kontaktu nin</a> por diskuti kiel ni povas kunlabori. Estas bone komprenite, ke LLM-oj prosperas per altkvalitaj datumoj. Ni havas la plej grandan kolekton de libroj, artikoloj, revuoj, ktp en la mondo, kiuj estas iuj el la plej altkvalitaj tekstaj fontoj. LLM-datumoj Unika skalo kaj gamo Nia kolekto enhavas pli ol cent milionojn da dosieroj, inkluzive de akademiaj ĵurnaloj, lernolibroj, kaj revuoj. Ni atingas ĉi tiun skalon kombinante grandajn ekzistantajn deponejojn. Iuj el niaj fontkolektoj jam estas disponeblaj amase (Sci-Hub, kaj partoj de Libgen). Aliaj fontoj ni mem liberigis. <a %(a_datasets)s>Datasets</a> montras plenan superrigardon. Nia kolekto inkluzivas milionojn da libroj, artikoloj, kaj revuoj de antaŭ la epoko de e-libroj. Grandaj partoj de ĉi tiu kolekto jam estis OCR-igitaj, kaj jam havas malmulte da interna supermeto. Daŭrigu Se vi perdis vian ŝlosilon, bonvolu <a %(a_contact)s>kontakti nin</a> kaj provizi kiel eble plej multe da informoj. Vi eble devos provizore krei novan konton por kontakti nin. Bonvolu <a %(a_account)s>ensaluti</a> por vidi ĉi tiun paĝon.</a> Por malhelpi spam-robotojn krei multajn kontojn, ni devas unue kontroli vian retumilon. Se vi kaptas vin en senfina buklo, ni rekomendas instali <a %(a_privacypass)s>Privacy Pass</a>. Eble ankaŭ helpas malŝalti reklamblokilojn kaj aliajn retumilajn etendaĵojn. Ensaluti / Registriĝi La Arkivo de Anna estas provizore malŝaltita por prizorgado. Bonvolu reveni post unu horo. Alternativa aŭtoro Alternativa priskribo Alternativa eldono Alternativa etendo Alternativa dosiernomo Alternativa eldonejo Alternativa titolo dato malfermita fonto Legu pli… priskribo Serĉu en la Arkivo de Anna por CADAL SSNO-nombro Serĉu en la Arkivo de Anna por DuXiu SSID-nombro Serĉu en la Arkivo de Anna por DuXiu DXID-nombro Serĉu en la Arkivo de Anna per ISBN Serĉu en la Arkivo de Anna per OCLC (WorldCat) numero Serĉu en la Arkivo de Anna por Open Library ID Reta spektanto de Arkivo de Anna %(count)s trafitaj paĝoj Post elŝuto: Pli bona versio de ĉi tiu dosiero eble disponeblas ĉe %(link)s Amasaj torentaj elŝutoj kolekto Uzu interretajn ilojn por konverti inter formatoj. Rekomenditaj konvertaj iloj: %(links)s Por grandaj dosieroj, ni rekomendas uzi elŝutan administrilon por eviti interrompojn. Rekomenditaj elŝutaj administriloj: %(links)s EBSCOhost eBook Indekso (spertuloj nur) (ankaŭ alklaku “GET” ĉe la supro) (klaku “GET” ĉe la supro) Eksteraj elŝutoj <strong>🚀 Rapidaj elŝutoj</strong> Vi havas %(remaining)s hodiaŭ. Dankon pro esti membro! ❤️ <strong>🚀 Rapidaj elŝutoj</strong> Vi elĉerpis viajn rapidajn elŝutojn por hodiaŭ. <strong>🚀 Rapidaj elŝutoj</strong> Vi elŝutis ĉi tiun dosieron lastatempe. Ligiloj restas validaj dum iom da tempo. <strong>🚀 Rapidaj elŝutoj</strong> Fariĝu <a %(a_membership)s>membro</a> por subteni la longdaŭran konservadon de libroj, artikoloj, kaj pli. Por montri nian dankemon pro via subteno, vi ricevas rapidajn elŝutojn. ❤️ 🚀 Rapidaj elŝutoj 🐢 Malrapidaj elŝutoj Pruntepreni de la Internet Archive IPFS Pordego #%(num)d (vi eble devos provi plurfoje kun IPFS) Libgen.li Libgen.rs Fikcio Libgen.rs Nefikcio iliaj reklamoj estas konataj enhavi malican programaron, do uzu reklaman blokilon aŭ ne alklaku reklamojn Amazon-a “Send to Kindle” djazz-a “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC dosieroj povas esti nefidindaj por elŝuti) Neniuj elŝutoj trovitaj. Ĉiuj elŝuteblaj opcioj havas la saman dosieron, kaj devus esti sekuraj por uzi. Tamen, ĉiam estu singarda kiam elŝutante dosierojn de la interreto, precipe de retejoj ekster la Arkivo de Anna. Ekzemple, certigu ke viaj aparatoj estas ĝisdatigitaj. (sen redirekto) Malfermi en nia spektanto (malfermi en spektanto) Opcio #%(num)d: %(link)s %(extra)s Trovu originalan rekordon en CADAL Serĉu permane en DuXiu Trovu originalan rekordon en ISBNdb Trovu originalan rekordon en WorldCat Trovu originalan registron en Open Library Serĉu diversajn aliajn datumbazojn por ISBN (nur por patronoj kun malebligita presado) PubMed Vi bezonos legilon por e-libroj aŭ PDF por malfermi la dosieron, depende de la dosierformato. Rekomenditaj e-libraj legiloj: %(links)s La Arkivo de Anna 🧬 SciDB Sci-Hub: %(doi)s (asociita DOI eble ne disponeblas en Sci-Hub) Vi povas sendi ambaŭ PDF kaj EPUB dosierojn al via Kindle aŭ Kobo e-legilo. Rekomenditaj iloj: %(links)s Pli da informoj en la <a %(a_slow)s>Oftaj Demandoj</a>. Subtenu aŭtorojn kaj bibliotekojn Se vi ŝatas ĉi tion kaj povas permesi ĝin, konsideru aĉeti la originalon aŭ subteni la aŭtorojn rekte. Se ĉi tio estas disponebla en via loka biblioteko, konsideru prunti ĝin senpage tie. Elŝutoj de Partnera Servilo provizore ne disponeblas por ĉi tiu dosiero. torento De fidindaj partneroj. Z-Library Z-Biblioteko en Tor (postulas la Tor-Retumilon) montri eksterajn elŝutojn <span class="font-bold">❌ Ĉi tiu dosiero eble havas problemojn, kaj estis kaŝita de fonta biblioteko.</span> Foje tio estas laŭ peto de kopirajta posedanto, foje ĉar pli bona alternativo estas disponebla, sed foje pro problemo kun la dosiero mem. Ĝi eble ankoraŭ estas bona por elŝuti, sed ni rekomendas unue serĉi alternativan dosieron. Pli da detaloj: Se vi ankoraŭ volas elŝuti ĉi tiun dosieron, certigu uzi nur fidindan, ĝisdatigitan programaron por malfermi ĝin. metadatenaj komentoj AA: Serĉu en la Arkivo de Anna por “%(name)s” Koda Esplorilo: Vidi en Koda Esplorilo “%(name)s” URL: Retejo: Se vi havas ĉi tiun dosieron kaj ĝi ankoraŭ ne estas disponebla en la Arkivo de Anna, konsideru <a %(a_request)s>alŝuti ĝin</a>. Internet Archive Kontrolita Cifereca Pruntado dosiero “%(id)s” Ĉi tio estas registro de dosiero el la Internet Archive, ne rekte elŝutebla dosiero. Vi povas provi pruntepreni la libron (ligilo sube), aŭ uzi ĉi tiun URL-on kiam <a %(a_request)s>petante dosieron</a>. Plibonigi metadatenojn CADAL SSNO %(id)s metadatenregistro Ĉi tio estas metadateno-rekordo, ne elŝutebla dosiero. Vi povas uzi ĉi tiun URL-on kiam <a %(a_request)s>petante dosieron</a>. DuXiu SSID %(id)s metadatenregistro ISBNdb %(id)s metadatenregistro MagzDB ID %(id)s metadatenoto Nexus/STC ID %(id)s metadatenoto OCLC (WorldCat) numero %(id)s metadatenregistro Open Library %(id)s metadatenregistro Sci-Hub dosiero “%(id)s” Ne trovita “%(md5_input)s” ne estis trovita en nia datumbazo. Aldoni komenton (%(count)s) Vi povas akiri la md5 de la URL, ekz. MD5 de pli bona versio de ĉi tiu dosiero (se aplikebla). Plenigu ĉi tion se estas alia dosiero kiu proksime kongruas kun ĉi tiu dosiero (sama eldono, sama dosiera etendo se vi povas trovi unu), kiun homoj devus uzi anstataŭ ĉi tiu dosiero. Se vi scias pri pli bona versio de ĉi tiu dosiero ekster la Arkivo de Anna, tiam bonvolu <a %(a_upload)s>alŝuti ĝin</a>. Io misfunkciis. Bonvolu reŝargi la paĝon kaj reprovi. Vi lasis komenton. Eble daŭros minuton por ĝi aperi. Bonvolu uzi la <a %(a_copyright)s>DMCA / Kopirajta plendoformularo</a>. Priskribu la problemon (deviga) Se ĉi tiu dosiero havas bonegan kvaliton, vi povas diskuti pri ĝi ĉi tie! Se ne, bonvolu uzi la butonon “Raporti dosierproblemon”. Bona dosiera kvalito (%(count)s) Dosiera kvalito Lernu kiel <a %(a_metadata)s>plibonigi la metadatenojn</a> por ĉi tiu dosiero mem. Problema priskribo Bonvolu <a %(a_login)s>ensaluti</a>. Mi amis ĉi tiun libron! Helpu la komunumon raportante la kvaliton de ĉi tiu dosiero! 🙌 Io misfunkciis. Bonvolu reŝargi la paĝon kaj reprovi. Raporti dosieran problemon (%(count)s) Dankon pro sendi vian raporton. Ĝi estos montrata sur ĉi tiu paĝo, kaj ankaŭ reviziita mane de Anna (ĝis ni havos taŭgan moderan sistemon). Lasu komenton Sendi raporton Kio estas malĝusta kun ĉi tiu dosiero? Pruntepreni (%(count)s) Komentoj (%(count)s) Elŝutoj (%(count)s) Esploru metadatenojn (%(count)s) Listoj (%(count)s) Statistikoj (%(count)s) Por informoj pri ĉi tiu specifa dosiero, kontrolu ĝian <a %(a_href)s>JSON-dosieron</a>. Ĉi tiu estas dosiero administrata de la <a %(a_ia)s>IA’s Controlled Digital Lending</a> biblioteko, kaj indeksita de la Arkivo de Anna por serĉado. Por informoj pri la diversaj datasets kiujn ni kompilis, vidu la <a %(a_datasets)s>paĝon pri datasets</a>. Metadaten de ligita registro Plibonigi metadaten en Open Library “Dosiera MD5” estas haketo kiu estas kalkulita el la enhavo de la dosiero, kaj estas sufiĉe unika bazita sur tiu enhavo. Ĉiuj ombro-bibliotekoj kiujn ni indeksis ĉi tie ĉefe uzas MD5-ojn por identigi dosierojn. Dosiero povas aperi en pluraj ombro-bibliotekoj. Por informoj pri la diversaj datasets kiujn ni kompilis, vidu la <a %(a_datasets)s>paĝon pri datasets</a>. Raporti dosieran kvaliton Totalaj elŝutoj: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Ĉeĥa metadatenoj %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Averto: multoblaj ligitaj registroj: Kiam vi rigardas libron en la Arkivo de Anna, vi povas vidi diversajn kampojn: titolo, aŭtoro, eldonisto, eldono, jaro, priskribo, dosiernomo, kaj pli. Ĉiuj tiuj informeroj estas nomataj <em>metadatenoj</em>. Ĉar ni kombinas librojn el diversaj <em>fontaj bibliotekoj</em>, ni montras kiaj ajn metadatenoj estas disponeblaj en tiu fonta biblioteko. Ekzemple, por libro kiun ni ricevis de Library Genesis, ni montros la titolon el la datumbazo de Library Genesis. Foje libro estas ĉeestanta en <em>multoblaj</em> fontaj bibliotekoj, kiuj eble havas malsamajn metadatenajn kampojn. En tiu kazo, ni simple montras la plej longan version de ĉiu kampo, ĉar tiu verŝajne enhavas la plej utilajn informojn! Ni ankoraŭ montros la aliajn kampojn sub la priskribo, ekz. kiel "alternativa titolo" (sed nur se ili estas malsamaj). Ni ankaŭ elprenas <em>kodojn</em> kiel identigiloj kaj klasigiloj el la fonta biblioteko. <em>Identigiloj</em> unike reprezentas specifan eldonon de libro; ekzemploj estas ISBN, DOI, Open Library ID, Google Books ID, aŭ Amazon ID. <em>Klasigiloj</em> grupigas multoblajn similajn librojn; ekzemploj estas Dewey Decimal (DCC), UDC, LCC, RVK, aŭ GOST. Foje ĉi tiuj kodoj estas eksplicite ligitaj en fontaj bibliotekoj, kaj foje ni povas elpreni ilin el la dosiernomo aŭ priskribo (ĉefe ISBN kaj DOI). Ni povas uzi identigilojn por trovi rekordojn en <em>nur-metadatenaj kolektoj</em>, kiel OpenLibrary, ISBNdb, aŭ WorldCat/OCLC. Estas specifa <em>metadatenoj langeto</em> en nia serĉilo se vi volas foliumi tiujn kolektojn. Ni uzas kongruajn rekordojn por plenigi mankantajn metadatenajn kampojn (ekz. se titolo mankas), aŭ ekz. kiel "alternativa titolo" (se ekzistas ekzistanta titolo). Por vidi ĝuste de kie venis la metadatenoj de libro, vidu la <em>“Teknikaj detaloj” langeton</em> en libro-paĝo. Ĝi havas ligilon al la kruda JSON por tiu libro, kun indikiloj al la kruda JSON de la originalaj rekordoj. Por pliaj informoj, vidu la jenajn paĝojn: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Serĉo (metadatenoj langeto)</a>, <a %(a_codes)s>Koda Esplorilo</a>, kaj <a %(a_example)s>Ekzempla metadateno JSON</a>. Fine, ĉiuj niaj metadatenoj povas esti <a %(a_generated)s>generitaj</a> aŭ <a %(a_downloaded)s>elŝutitaj</a> kiel ElasticSearch kaj MariaDB datumbazoj. Fono Vi povas helpi konservadon de libroj plibonigante metadatenojn! Unue, legu la fonon pri metadatenoj en la Arkivo de Anna, kaj poste lernu kiel plibonigi metadatenojn per ligiloj kun Open Library, kaj gajnu senpagan membrecon en la Arkivo de Anna. Plibonigi metadatenojn Do se vi renkontas dosieron kun malbonaj metadatenoj, kiel vi devus ripari ĝin? Vi povas iri al la fonta biblioteko kaj sekvi ĝiajn procedurojn por ripari metadatenojn, sed kion fari se dosiero estas ĉeestanta en multoblaj fontaj bibliotekoj? Estas unu identigilo kiu estas traktata speciale en la Arkivo de Anna. <strong>La annas_archive md5 kampo en Open Library ĉiam superregas ĉiujn aliajn metadatenojn!</strong> Ni unue retrorigardu kaj lernu pri Open Library. Open Library estis fondita en 2006 de Aaron Swartz kun la celo de "unu retpaĝo por ĉiu libro iam ajn publikigita". Ĝi estas speco de Vikipedio por libro-metadatenoj: ĉiuj povas redakti ĝin, ĝi estas libere licencita, kaj povas esti elŝutita amase. Ĝi estas libro-datumbazo kiu plej kongruas kun nia misio — fakte, la Arkivo de Anna estis inspirita de la vizio kaj vivo de Aaron Swartz. Anstataŭ reinventi la radon, ni decidis redirekti niajn volontulojn al Open Library. Se vi vidas libron kun malĝustaj metadatenoj, vi povas helpi en la sekva maniero: Notu ke ĉi tio funkcias nur por libroj, ne por akademiaj artikoloj aŭ aliaj specoj de dosieroj. Por aliaj specoj de dosieroj ni ankoraŭ rekomendas trovi la fontan bibliotekon. Eble daŭros kelkajn semajnojn por ke ŝanĝoj estu inkluzivitaj en Anna’s Archive, ĉar ni devas elŝuti la plej novan Open Library-datumbazon, kaj regeneri nian serĉindekson.  Iru al la <a %(a_openlib)s>retejo de Open Library</a>. Trovu la ĝustan libro-rekordon. <strong>AVERTO:</strong> estu certa elekti la ĝustan <strong>eldonon</strong>. En Open Library, vi havas "verkojn" kaj "eldonojn". "Verko" povus esti "Harry Potter kaj la Ŝtono de la Saĝuloj". "Eldono" povus esti: La unua eldono de 1997 eldonita de Bloomsbery kun 256 paĝoj. La poŝlibra eldono de 2003 eldonita de Raincoast Books kun 223 paĝoj. La pola traduko de 2000 “Harry Potter I Kamie Filozoficzn” de Media Rodzina kun 328 paĝoj. Ĉiuj tiuj eldonoj havas malsamajn ISBN-ojn kaj malsamajn enhavojn, do certigu elekti la ĝustan! Redaktu la registron (aŭ kreu ĝin se neniu ekzistas), kaj aldonu kiel eble plej multe da utilaj informoj! Vi estas ĉi tie nun ĉiuokaze, do faru la registron vere mirinda. Sub “ID Numbers” elektu “Anna’s Archive” kaj aldonu la MD5 de la libro el Anna’s Archive. Ĉi tio estas la longa ĉeno de literoj kaj ciferoj post “/md5/” en la URL. Provu trovi aliajn dosierojn en Anna’s Archive kiuj ankaŭ kongruas kun ĉi tiu registro, kaj aldonu tiujn ankaŭ. Estonte ni povas grupigi tiujn kiel duplikatojn en la serĉpaĝo de Anna’s Archive. Kiam vi finos, notu la URL-on kiun vi ĵus ĝisdatigis. Post kiam vi ĝisdatigis almenaŭ 30 registrojn kun Anna’s Archive MD5-oj, sendu al ni <a %(a_contact)s>retpoŝton</a> kaj sendu al ni la liston. Ni donos al vi senpagan membrecon por Anna’s Archive, por ke vi povu pli facile fari ĉi tiun laboron (kaj kiel dankon pro via helpo). Ĉi tiuj devas esti altkvalitaj redaktoj kiuj aldonas konsiderindajn kvantojn da informoj, alie via peto estos malakceptita. Via peto ankaŭ estos malakceptita se iu el la redaktoj estos nuligita aŭ korektita de Open Library-moderatoroj. Ligado kun Open Library Se vi signife engaĝiĝos en la disvolviĝo kaj operacioj de nia laboro, ni povas diskuti pri dividado de pli da donacaj enspezoj kun vi, por ke vi povu uzi laŭnecese. Ni pagos por gastigado nur post kiam vi havas ĉion aranĝita kaj montris, ke vi kapablas teni la arkivon ĝisdatigita kun ĝisdatigoj. Tio signifas, ke vi devos pagi por la unuaj 1-2 monatoj el via poŝo. Via tempo ne estos kompensita (kaj ankaŭ nia ne), ĉar ĉi tio estas pura volontula laboro. Ni pretas kovri gastigajn kaj VPN-elspezojn, komence ĝis $200 monate. Ĉi tio estas sufiĉa por baza serĉservilo kaj DMCA-protektita prokurilo. Gastigaj elspezoj Bonvolu <strong>ne kontakti nin</strong> por peti permeson, aŭ por bazaj demandoj. Agoj parolas pli laŭte ol vortoj! Ĉiuj informoj estas tie, do simple daŭrigu kun starigado de via spegulo. Bonvolu sendi biletojn aŭ kunfandajn petojn al nia Gitlab kiam vi renkontas problemojn. Ni eble bezonos konstrui iujn spegul-specifajn funkciojn kun vi, kiel rebrandi de “Anna’s Archive” al via retejo nomo, (komence) malŝalti uzantajn kontojn, aŭ ligi reen al nia ĉefa retejo de libro-paĝoj. Post kiam vi havas vian spegulon funkcianta, bonvolu kontakti nin. Ni ŝatus revizii vian OpSec, kaj post kiam tio estas solida, ni ligos al via spegulo, kaj komencos labori pli proksime kun vi. Antaŭdankon al ĉiuj, kiuj volas kontribui tiel! Ĝi ne estas por la malfortaj, sed ĝi solidigus la longvivecon de la plej granda vere malferma biblioteko en la homa historio. Komenci Por pliigi la rezistecon de la Arkivo de Anna, ni serĉas volontulojn por funkciigi spegulojn. Via versio estas klare distingita kiel spegulo, ekz. “La Arkivo de Bob, spegulo de la Arkivo de Anna”. Vi estas preta preni la riskojn asociitajn kun ĉi tiu laboro, kiuj estas signifaj. Vi havas profundan komprenon de la operacia sekureco postulata. La enhavo de <a %(a_shadow)s>ĉi tiuj</a> <a %(a_pirate)s>afiŝoj</a> estas memkomprenebla por vi. Komence ni ne donos al vi aliron al niaj partneraj servilaj elŝutoj, sed se aferoj iros bone, ni povas dividi tion kun vi. Vi funkciigas la malfermitkodan bazon de Anna’s Archive, kaj vi regule ĝisdatigas kaj la kodon kaj la datumojn. Vi estas preta kontribui al nia <a %(a_codebase)s>kodbazo</a> — en kunlaboro kun nia teamo — por realigi ĉi tion. Ni serĉas ĉi tion: Speguloj: alvoko por volontuloj Fari alian donacon. Neniuj donacoj ankoraŭ. <a %(a_donate)s>Fari mian unuan donacon.</a> Donacaj detaloj ne estas publike montrataj. Miaj donacoj 📡 Por amasa speguligo de nia kolekto, kontrolu la paĝojn <a %(a_datasets)s>Datasets</a> kaj <a %(a_torrents)s>Torrents</a>. Elŝutoj de via IP-adreso en la lastaj 24 horoj: %(count)s. 🚀 Por akiri pli rapidajn elŝutojn kaj eviti la retumilajn kontrolojn, <a %(a_membership)s>iĝu membro</a>. Elŝuti de partnera retejo Bonvolu daŭrigi foliumi Arkivon de Anna en alia langeto dum vi atendas (se via retumilo subtenas refreŝigadon de fonaj langetoj). Bonvolu atendi ĝis pluraj elŝutpaĝoj ŝarĝiĝas samtempe (sed bonvolu elŝuti nur unu dosieron samtempe por ĉiu servilo). Post kiam vi ricevas elŝutligilon, ĝi validas dum pluraj horoj. Dankon pro via pacienco, tio tenas la retejon senpaga por ĉiuj! 😊 🔗 Ĉiuj elŝutligiloj por ĉi tiu dosiero: <a %(a_main)s>Ĉefa paĝo de dosiero</a>. ❌ Malrapidaj elŝutoj ne estas disponeblaj per Cloudflare VPN-oj aŭ alie de Cloudflare IP-adresoj. ❌ Malrapidaj elŝutoj estas disponeblaj nur tra la oficiala retejo. Vizitu %(websites)s. 📚 Uzu la sekvan URL por elŝuti: <a %(a_download)s>Elŝuti nun</a>. Por doni al ĉiuj ŝancon elŝuti dosierojn senpage, vi devas atendi antaŭ ol vi povas elŝuti ĉi tiun dosieron. Bonvolu atendi <span %(span_countdown)s>%(wait_seconds)s</span> sekundojn por elŝuti ĉi tiun dosieron. Averto: estis multaj elŝutoj de via IP-adreso en la lastaj 24 horoj. Elŝutoj povus esti pli malrapidaj ol kutime. Se vi uzas VPN, komunan interretan konekton, aŭ via ISP dividas IP-ojn, ĉi tiu averto eble estas pro tio. Konservi ❌ Io fuŝiĝis. Bonvolu reprovi. ✅ Konservita. Bonvolu reŝargi la paĝon. Ŝanĝu vian montronomon. Via identigilo (la parto post “#”) ne povas esti ŝanĝita. Profilo kreita <span %(span_time)s>%(time)s</span> redakti Listoj Kreu novan liston trovante dosieron kaj malfermante la langeton “Listoj”. Neniuj listoj ankoraŭ Profilo ne trovita. Profilo Nuntempe, ni ne povas akcepti libro-petojn. Ne retpoŝtu al ni viajn libro-petojn. Bonvolu fari viajn petojn en Z-Library aŭ Libgen forumoj. Rekordo en la Arkivo de Anna DOI: %(doi)s Elŝuti SciDB Nexus/STC Neniu antaŭrigardo disponebla ankoraŭ. Elŝutu dosieron de <a %(a_path)s>Arkivo de Anna</a>. Por subteni la alireblecon kaj longdaŭran konservadon de homa scio, fariĝu <a %(a_donate)s>membro</a>. Kiel bonuso, 🧬&nbsp;SciDB ŝarĝas pli rapide por membroj, sen ajnaj limoj. Ne funkcias? Provu <a %(a_refresh)s>refreŝigi</a>. Sci-Hub Aldoni specifan serĉkampon Serĉu priskribojn kaj metadatenajn komentojn Publikigita jaro Altnivela Aliri Enhavo Montri Listo Tabelo Dosiertipo Lingvo Ordigi laŭ Plej granda Plej rilata Plej nova (dosiergrandeco) (malfermfonta) (publikiga jaro) Plej malnova Hazarda Plej malgranda Fonto skrapita kaj malfermfonta de AA Cifereca Pruntedonado (%(count)s) Revuo Artikoloj (%(count)s) Ni trovis kongruojn en: %(in)s. Vi povas referenci la URL-on trovitan tie kiam <a %(a_request)s>petante dosieron</a>. Metadatenoj (%(count)s) Por esplori la serĉindekson per kodoj, uzu la <a %(a_href)s>Kodan Esplorilon</a>. La serĉindekso estas ĝisdatigita monate. Ĝi nuntempe inkluzivas enirojn ĝis %(last_data_refresh_date)s. Por pli teknikaj informoj, vidu la <a %(link_open_tag)s>paĝon pri datasets</a>. Ekskludu Inkluzivu nur Nekontrolita pli… Sekva … Antaŭa Ĉi tiu serĉindekso nuntempe inkluzivas metadatenojn de la Kontrolita Cifereca Pruntbiblioteko de Internet Archive. <a %(a_datasets)s>Pli pri niaj datasets</a>. Por pli da ciferecaj pruntbibliotekoj, vidu <a %(a_wikipedia)s>Vikipedion</a> kaj la <a %(a_mobileread)s>MobileRead Viki</a>. Por DMCA / kopirajtaj asertoj <a %(a_copyright)s>klaku ĉi tie</a>. Tempo de elŝuto Eraro dum serĉado. Provu <a %(a_reload)s>reŝargi la paĝon</a>. Se la problemo persistas, bonvolu retpoŝti al ni ĉe %(email)s. Rapida elŝuto Fakte, iu ajn povas helpi konservi ĉi tiujn dosierojn semante nian <a %(a_torrents)s>unuiĝintan liston de torentoj</a>. ➡️ Foje ĉi tio okazas malĝuste kiam la serĉa servilo estas malrapida. En tiaj kazoj, <a %(a_attrs)s>reŝargi</a> povas helpi. ❌ Ĉi tiu dosiero eble havas problemojn. Ĉu vi serĉas artikolojn? Ĉi tiu serĉindekso nuntempe inkluzivas metadatenojn de diversaj metadatenfontoj. <a %(a_datasets)s>Pli pri niaj datasets</a>. Estas multaj, multaj fontoj de metadatenoj por skribaj verkoj ĉirkaŭ la mondo. <a %(a_wikipedia)s>Ĉi tiu Vikipedia paĝo</a> estas bona komenco, sed se vi konas aliajn bonajn listojn, bonvolu sciigi nin. Por metadatenoj, ni montras la originalajn rekordojn. Ni ne faras ajnan kunfandadon de rekordoj. Ni nuntempe havas la plej ampleksan malferman katalogon de libroj, artikoloj kaj aliaj skribaj verkoj en la mondo. Ni spegulas Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>kaj pli</a>. <span class="font-bold">Neniuj dosieroj trovitaj.</span> Provu malpli aŭ malsamajn serĉvortojn kaj filtrilojn. Rezultoj %(from)s-%(to)s (%(total)s entute) Se vi trovas aliajn “ombrolibrojn” kiujn ni devus speguli, aŭ se vi havas demandojn, bonvolu kontakti nin ĉe %(email)s. %(num)d partaj kongruoj %(num)d+ partaj kongruoj Tajpu en la skatolo por serĉi dosierojn en ciferecaj pruntbibliotekoj. Tajpu en la skatolo por serĉi nian katalogon de %(count)s rekte elŝuteblaj dosieroj, kiujn ni <a %(a_preserve)s>konservas por ĉiam</a>. Tajpu en la skatolo por serĉi. Tajpu en la skatolo por serĉi nian katalogon de %(count)s akademiaj artikoloj kaj ĵurnalaj artikoloj, kiujn ni <a %(a_preserve)s>konservas por ĉiam</a>. Tajpu en la skatolo por serĉi metadatenojn de bibliotekoj. Ĉi tio povas esti utila kiam <a %(a_request)s>petante dosieron</a>. Konsilo: uzu klavkombinojn “/” (serĉa fokuso), “enter” (serĉi), “j” (supren), “k” (malsupren), “<” (antaŭa paĝo), “>” (sekva paĝo) por pli rapida navigado. Ĉi tiuj estas metadatenaj rekordoj, <span %(classname)s>ne</span> elŝuteblaj dosieroj. Serĉaj agordoj Serĉi Cifereca Pruntado Elŝuti Revuoartikoloj Metadatenoj Nova serĉo %(search_input)s - Serĉi La serĉo daŭris tro longe, kio signifas, ke vi eble vidos malprecizajn rezultojn. Kelkfoje <a %(a_reload)s>reŝargi</a> la paĝon helpas. La serĉo daŭris tro longe, kio estas ofta por larĝaj demandoj. La filtraj kalkuloj eble ne estas precizaj. Por grandaj alŝutoj (pli ol 10,000 dosieroj) kiuj ne estas akceptitaj de Libgen aŭ Z-Library, bonvolu kontakti nin ĉe %(a_email)s. Por Libgen.li, certigu unue ensaluti en <a %(a_forum)s>ilia forumo</a> kun uzantnomo %(username)s kaj pasvorto %(password)s, kaj poste revenu al ilia <a %(a_upload_page)s>alŝuta paĝo</a>. Por nun, ni sugestas alŝuti novajn librojn al la forkoj de Library Genesis. Jen <a %(a_guide)s>utila gvidilo</a>. Notu, ke ambaŭ forkoj, kiujn ni indeksas en ĉi tiu retejo, tiras el ĉi tiu sama alŝuta sistemo. Por malgrandaj alŝutoj (ĝis 10,000 dosieroj) bonvolu alŝuti ilin al ambaŭ %(first)s kaj %(second)s. Alternative, vi povas alŝuti ilin al Z-Library <a %(a_upload)s>ĉi tie</a>. Por alŝuti akademiajn artikolojn, bonvolu ankaŭ (aldone al Library Genesis) alŝuti al <a %(a_stc_nexus)s>STC Nexus</a>. Ili estas la plej bona ombra biblioteko por novaj artikoloj. Ni ankoraŭ ne integris ilin, sed ni faros iam. Vi povas uzi ilian <a %(a_telegram)s>alŝutan roboton en Telegram</a>, aŭ kontakti la adreson listigitan en ilia fiksita mesaĝo se vi havas tro multajn dosierojn por alŝuti ĉi tiun manieron. <span %(label)s>Peza volontula laboro (USD$50-USD$5,000 rekompencoj):</span> se vi povas dediĉi multan tempon kaj/aŭ rimedojn al nia misio, ni ŝatus labori pli proksime kun vi. Fine vi povas aliĝi al la interna teamo. Kvankam ni havas striktan buĝeton, ni povas doni <span %(bold)s>💰 monajn rekompencojn</span> por la plej intensa laboro. <span %(label)s>Luma volontula laboro:</span> se vi povas nur dediĉi kelkajn horojn ĉi tie kaj tie, ankoraŭ estas multaj manieroj, kiel vi povas helpi. Ni rekompencas konsekvencajn volontulojn per <span %(bold)s>🤝 membrecoj al la Arkivo de Anna</span>. La Arkivo de Anna dependas de volontuloj kiel vi. Ni bonvenigas ĉiujn nivelojn de engaĝiĝo, kaj havas du ĉefajn kategoriojn de helpo, kiujn ni serĉas: Se vi ne povas volontuli vian tempon, vi ankoraŭ povas multe helpi nin per <a %(a_donate)s>donacado de mono</a>, <a %(a_torrents)s>semajdo de niaj torentoj</a>, <a %(a_uploading)s>alŝutado de libroj</a>, aŭ <a %(a_help)s>rakontado al viaj amikoj pri la Arkivo de Anna</a>. <span %(bold)s>Kompanioj:</span> ni ofertas altrapidan rektan aliron al niaj kolektoj kontraŭ entreprena donaco aŭ interŝanĝo por novaj kolektoj (ekz. novaj skanaĵoj, OCR’itaj datasets, riĉigado de niaj datumoj). <a %(a_contact)s>Kontaktu nin</a> se tio estas vi. Vidu ankaŭ nian <a %(a_llm)s>LLM paĝon</a>. Rekompencoj Ni ĉiam serĉas homojn kun solidaj programaj aŭ ofensivaj sekurecaj kapabloj por engaĝiĝi. Vi povas fari gravan kontribuon al konservado de la heredaĵo de la homaro. Kiel dankon, ni donacas membrecon por solidaj kontribuoj. Kiel grandan dankon, ni donacas monajn rekompencojn por precipe gravaj kaj malfacilaj taskoj. Ĉi tio ne devus esti rigardata kiel anstataŭaĵo por laboro, sed ĝi estas ekstra instigo kaj povas helpi kun okazintaj kostoj. Plejparto de nia kodo estas malfermfonta, kaj ni petos tion ankaŭ de via kodo kiam ni atribuas la rekompencon. Estas kelkaj esceptoj, kiujn ni povas diskuti individue. Rekompencoj estas atribuitaj al la unua persono, kiu kompletigas taskon. Bonvolu komenti pri rekompenca bileto por informi aliajn, ke vi laboras pri io, tiel ke aliaj povas atendi aŭ kontakti vin por kunlabori. Sed estu konscia, ke aliaj ankoraŭ estas liberaj labori pri ĝi kaj provi superi vin. Tamen, ni ne atribuas rekompencojn por malzorga laboro. Se du altkvalitaj submetoj estas faritaj proksime unu al la alia (ene de tago aŭ du), ni eble elektos atribui rekompencojn al ambaŭ, laŭ nia diskreteco, ekzemple 100%% por la unua submeto kaj 50%% por la dua submeto (do 150%% entute). Por la pli grandaj rekompencoj (precipe skrapaj rekompencoj), bonvolu kontakti nin kiam vi kompletigis ~5%% de ĝi, kaj vi estas certa, ke via metodo skaliĝos al la plena mejloŝtono. Vi devos dividi vian metodon kun ni por ke ni povu doni reagojn. Ankaŭ, tiel ni povas decidi kion fari se estas pluraj homoj proksimiĝantaj al rekompenco, kiel ekzemple potenciale atribui ĝin al pluraj homoj, instigi homojn kunlabori, ktp. AVERTO: la alt-rekompencaj taskoj estas <span %(bold)s>malfacilaj</span> — eble estas saĝe komenci kun pli facilaj. Iru al nia <a %(a_gitlab)s>Gitlab-issues-listo</a> kaj ordigu laŭ “Etikeda prioritato”. Ĉi tio montras proksimume la ordon de taskoj, kiujn ni zorgas. Taskoj sen eksplicitaj rekompencoj ankoraŭ estas elekteblaj por membreco, precipe tiuj markitaj “Akceptita” kaj “La plej ŝatata de Anna”. Vi eble volas komenci kun “Komenca projekto”. Luma volontulado Ni nun ankaŭ havas sinkronigitan Matrix-kanalon ĉe %(matrix)s. Se vi havas kelkajn horojn por ŝpari, vi povas helpi en diversaj manieroj. Nepre aliĝu al la <a %(a_telegram)s>volontula babilejo en Telegram</a>. Kiel signo de aprezo, ni kutime donas 6 monatojn de “Bonŝanca Bibliotekisto” por bazaj mejloŝtonoj, kaj pli por daŭra volontula laboro. Ĉiuj mejloŝtonoj postulas altkvalitan laboron — malzorgema laboro pli damaĝas nin ol helpas kaj ni malakceptos ĝin. Bonvolu <a %(a_contact)s>retpoŝti nin</a> kiam vi atingas mejloŝtonon. %(links)s ligiloj aŭ ekrankopioj de petoj, kiujn vi plenumis. Plenumi libro- (aŭ artikolo, ktp) petojn en la forumoj de Z-Library aŭ Library Genesis. Ni ne havas nian propran libropetan sistemon, sed ni spegulas tiujn bibliotekojn, do plibonigi ilin plibonigas ankaŭ la Arĥivon de Anna. Mejloŝtono Tasko Dependas de la tasko. Malgrandaj taskoj afiŝitaj en nia <a %(a_telegram)s>volontula babilejo en Telegram</a>. Kutime por membreco, foje por malgrandaj rekompencoj. Malgrandaj taskoj afiŝitaj en nia volontula babilejo. Certiĝu lasi komenton pri problemoj, kiujn vi riparas, por ke aliaj ne duobligu vian laboron. %(links)s ligiloj de rekordoj, kiujn vi plibonigis. Vi povas uzi la <a %(a_list)s>liston de hazardaj metadata problemoj</a> kiel deirpunkton. Plibonigi metadatumojn per <a %(a_metadata)s>ligado</a> kun Open Library. Ĉi tiuj devus montri vin informante iun pri la Arkivo de Anna, kaj ilin dankante vin. %(links)s ligiloj aŭ ekrankopioj. Disvastigante la vorton pri la Arkivo de Anna. Ekzemple, rekomendante librojn en AA, ligante al niaj blogaĵoj, aŭ ĝenerale direktante homojn al nia retejo. Plene traduki lingvon (se ĝi ne estis preskaŭ kompletigita jam). <a %(a_translate)s>Tradukado</a> de la retejo. Ligo al redakta historio montranta, ke vi faris signifajn kontribuojn. Plibonigi la Vikipedian paĝon por la Arkivo de Anna en via lingvo. Inkluzivu informojn de la Vikipedia paĝo de AA en aliaj lingvoj, kaj de nia retejo kaj blogo. Aldonu referencojn al AA en aliaj rilataj paĝoj. Volontulado & Premioj 