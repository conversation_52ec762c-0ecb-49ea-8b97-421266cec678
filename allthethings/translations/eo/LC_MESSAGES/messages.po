msgid "layout.index.invalid_request"
msgstr "Nevalida Peto. Vizitu %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Pruntbiblioteko"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " kaj "

msgid "layout.index.header.tagline_and_more"
msgstr "kaj pli"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Ni havas kopion de %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Ni skrapas kaj malfermfonte %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Ĉiuj niaj kodo kaj datumoj estas tute malfermitaj."

msgid "layout.index.header.tagline_new1"
msgstr "La pli granda malferma biblioteko de la homara historio."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libroj, %(paper_count)s&nbsp;paperoj— konservita por ĉiam."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La plej granda malfermfonta malfermdatuma biblioteko en la mondo. ⭐️&nbsp;Speguloj de Sci-Hub, Library Genesis, Z-Library, kaj pli. 📈&nbsp;%(book_any)s libroj, %(journal_article)s artikoloj, %(book_comic)s bildstrioj, %(magazine)s magazinoj — konservitaj por ĉiam."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 La plej granda malfermfonta malfermdatuma biblioteko en la mondo.<br>⭐️ Speguloj de Scihub, Libgen, Zlib, kaj pli."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Malkorekta metadata (ekz. titolo, priskribo, kovrilbildo)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Elŝutaj problemoj (ekz. ne povas konekti, erarmesaĝo, tre malrapida)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Dosiero ne povas esti malfermita (ekz. koruptita dosiero, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Malbona kvalito (ekz. formatproblemoj, malbona skana kvalito, mankantaj paĝoj)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spamo / dosiero devus esti forigita (ekz. reklamado, ofenda enhavo)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Aŭtorrajta aserto"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Aliaj"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonusaj elŝutoj"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brila Bibliomano"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Bonŝanca Bibliotekisto"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Brila Bibliomaniulo"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Mirinda Arkivisto"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s entute"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) entute"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonuso)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "nepagitaj"

msgid "common.donation.order_processing_status_labels.1"
msgstr "pagita"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "nuligita"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "eksvalidiĝis"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "atendante konfirmon de Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "nevalida"

#, fuzzy
msgid "page.donate.title"
msgstr "Donaci"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Vi havas <a %(a_donation)s>ekzistantan donacon</a> en progreso. Bonvolu fini aŭ nuligi tiun donacon antaŭ ol fari novan donacon."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Vidi ĉiujn miajn donacojn</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "La Arkivo de Anna estas neprofitcela, malfermfonta, malfermdatuma projekto. Donacante kaj fariĝante membro, vi subtenas niajn operaciojn kaj disvolviĝon. Al ĉiuj niaj membroj: dankon pro teni nin funkciantaj! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Por pliaj informoj, kontrolu la <a %(a_donate)s>Donacan FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Por akiri eĉ pli da elŝutoj, <a %(a_refer)s>referu viajn amikojn</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Vi ricevas %(percentage)s%% bonusajn rapidajn elŝutojn, ĉar vi estis referita de uzanto %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Ĉi tio validas por la tuta membreca periodo."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s rapidaj elŝutoj tage"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se vi donacos ĉi-monate!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / monato"

msgid "page.donate.buttons.join"
msgstr "Partopreno"

msgid "page.donate.buttons.selected"
msgstr "Elektita"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "ĝis %(percentage)s%% rabatoj"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB artikoloj <strong>senlima</strong> sen kontrolo"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> aliro"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Gajnu <strong>%(percentage)s%% bonusajn elŝutojn</strong> per <a %(a_refer)s>referencado de amikoj</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Via salutnomo aŭ anonima mencio en la kreditoj"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Antaŭaj avantaĝoj, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Frua aliro al novaj funkcioj"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Ekskluziva Telegram kun malantaŭ-kulisaĵoj ĝisdatigoj"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adoptu torenton”: via salutnomo aŭ mesaĝo en torenta dosiernomo <div %(div_months)s>unufoje ĉiun 12 monatojn de membreco</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legenda statuso en konservado de la scio kaj kulturo de la homaro"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Sperta Alirado"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontaktu nin"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Ni estas malgranda teamo de volontuloj. Eble daŭros 1-2 semajnojn por respondi."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Senlima</strong> altrapida aliro"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Rektaj <strong>SFTP</strong> serviloj"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donaco aŭ interŝanĝo je entreprena nivelo por novaj kolektoj (ekz. novaj skanaĵoj, OCR-igitaj datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Ni bonvenigas grandajn donacojn de riĉaj individuoj aŭ institucioj. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Por donacoj super $5000 bonvolu kontakti nin rekte ĉe %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Sciu, ke kvankam la membrecoj sur ĉi tiu paĝo estas \"po monato\", ili estas unufojaj donacoj (ne ripetiĝantaj). Vidu la <a %(faq)s>Donacan FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Se vi ŝatus fari donacon (iu ajn sumo) sen membreco, bonvolu uzi ĉi tiun Monero (XMR) adreson: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Bonvolu elekti pagmanieron."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(provize ne disponebla)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s donackarto"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Banka karto (uzante aplikaĵon)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Kripto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit/debetkarto"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regula)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Karto / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kreditkarto/debetkarto/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix(Brazilo)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Banka karto"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit/debetkarto (rezerva)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit-/debeto-karto 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Kun kripto vi povas donaci uzante BTC, ETH, XMR, kaj SOL. Uzu ĉi tiun opcion se vi jam konas kriptovaluton."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Kun kripto vi povas donaci uzante BTC, ETH, XMR, kaj pli."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se vi uzas kripton por la unua fojo, ni sugestas uzi %(options)s por aĉeti kaj donaci Bitcoin (la originala kaj plej uzata kriptovaluto)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Por donaci uzante PayPal US, ni uzos PayPal Crypto, kiu permesas al ni resti anonimaj. Ni dankas vin pro preni la tempon por lerni kiel donaci uzante ĉi tiun metodon, ĉar ĝi multe helpas nin."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donacu uzante PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donaci uzante Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se vi havas Cash App, ĉi tio estas la plej facila maniero donaci!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Notu, ke por transakcioj sub %(amount)s, Cash App povas ŝargi %(fee)s kotizon. Por %(amount)s aŭ pli, ĝi estas senpaga!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donacu uzante Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se vi havas Revolut, ĉi tio estas la plej facila maniero donaci!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donacu per kreditkarto aŭ debetkarto."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay kaj Apple Pay ankaŭ povus funkcii."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Notu, ke por malgrandaj donacoj la kreditkartaj kotizoj povas nuligi nian %(discount)s%% rabaton, do ni rekomendas pli longajn abonojn."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Notu, ke por malgrandaj donacoj la kotizoj estas altaj, do ni rekomendas pli longajn abonojn."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Kun Binance, vi aĉetas Bitcoin per kredit-/debetkarto aŭ banka konto, kaj poste donacas tiun Bitcoin al ni. Tiel ni povas resti sekuraj kaj anonimaj kiam ni akceptas vian donacon."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance estas disponebla en preskaŭ ĉiu lando, kaj subtenas plej multajn bankojn kaj kredit/debetkartojn. Ĉi tio estas nuntempe nia ĉefa rekomendo. Ni dankas vin pro preni la tempon por lerni kiel donaci uzante ĉi tiun metodon, ĉar ĝi multe helpas nin."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donacu uzante vian regulan PayPal-konton."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donacu uzante kredit/debetkarton, PayPal, aŭ Venmo. Vi povas elekti inter ĉi tiuj sur la sekva paĝo."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donacu uzante Amazon donackarton."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Notu, ke ni devas rondigi al kvantoj akceptitaj de niaj revendistoj (minimumo %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>GRAVE:</strong> Ni nur subtenas Amazon.com, ne aliajn Amazon-retejojn. Ekzemple, .de, .co.uk, .ca, NE estas subtenataj."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>GRAVE:</strong> Ĉi tiu opcio estas por %(amazon)s. Se vi volas uzi alian retejon de Amazon, elektu ĝin supre."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Ĉi tiu metodo uzas kriptomonan provizanton kiel mezan konvertiĝon. Ĉi tio povas esti iom konfuziga, do bonvolu uzi ĉi tiun metodon nur se aliaj pagmetodoj ne funkcias. Ĝi ankaŭ ne funkcias en ĉiuj landoj."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donacu uzante kredit-/debetkarton, per la Alipay-aplikaĵo (tre facile agordi)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instalu la Alipay-aplikaĵon"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instalu la Alipay-aplikaĵon el la <a %(a_app_store)s>Apple App Store</a> aŭ <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registriĝu uzante vian telefonnumeron."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Neniuj pliaj personaj detaloj estas postulataj."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Aldonu bankokarton"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Subtenataj: Visa, MasterCard, JCB, Diners Club kaj Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Vidu <a %(a_alipay)s>ĉi tiun gvidilon</a> por pli da informoj."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Ni ne povas subteni kredit-/debetkartojn rekte, ĉar bankoj ne volas kunlabori kun ni. ☹ Tamen, ekzistas pluraj manieroj uzi kredit-/debetkartojn, uzante aliajn pagmetodojn:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Donackarto"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Sendu al ni donackartojn de Amazon.com uzante vian kredit-/debetkarton."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay subtenas internaciajn kredit-/debetkartojn. Vidu <a %(a_alipay)s>ĉi tiun gvidilon</a> por pli da informoj."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) subtenas internaciajn kredit-/debetkartojn. En la WeChat-aplikaĵo, iru al “Mi => Servoj => Monujo => Aldoni Karton”. Se vi ne vidas tion, ebligu ĝin uzante “Mi => Agordoj => Ĝenerala => Iloj => Weixin Pay => Ebligi”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Vi povas aĉeti kripton uzante kredit/debetkartojn."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Kripto rapidaj servoj"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Rapidaj servoj estas oportunaj, sed ŝargas pli altajn kotizojn."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Vi povas uzi ĉi tion anstataŭ kripta interŝanĝo se vi serĉas rapide fari pli grandan donacon kaj ne ĝenas kotizo de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Certiĝu sendi la ĝustan kriptan sumon montritan sur la donacpaĝo, ne la sumon en $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Alie la kotizo estos subtrahita kaj ni ne povas aŭtomate prilabori vian membrecon."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimumo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimumo: %(minimum)s depende de lando, neniu kontrolo por la unua transakcio)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimumo: %(minimum)s, neniu kontrolo por la unua transakcio)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimumo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimumo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimumo: %(minimum)s, neniu kontrolo por la unua transakcio)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se iu el ĉi tiu informo estas malaktuala, bonvolu retpoŝti al ni por sciigi nin."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Por kreditkartoj, debetkartoj, Apple Pay, kaj Google Pay, ni uzas “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). En ilia sistemo, unu “kafo” egalas al $5, do via donaco estos rondigita al la plej proksima multoblo de 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Elektu kiom longe vi volas aboni."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 monato"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 monatoj"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 monatoj"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 monatoj"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 monatoj"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 monatoj"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 monatoj"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>post <span %(span_discount)s></span> rabatoj</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Ĉi tiu pagmaniero postulas minimumon de %(amount)s. Bonvolu elekti alian daŭron aŭ pagmanieron."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donaci"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Ĉi tiu pagmaniero permesas nur maksimumon de %(amount)s. Bonvolu elekti alian daŭron aŭ pagmanieron."

#, fuzzy
msgid "page.donate.login2"
msgstr "Por fariĝi membro, bonvolu <a %(a_login)s>Ensaluti aŭ Registriĝi</a>. Dankon pro via subteno!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Elektu vian preferatan kriptomonon:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(plej malalta minimuma kvanto)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(uzi kiam sendas Ethereum de Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(averto: alta minimuma kvanto)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Alklaku la donacbutonon por konfirmi ĉi tiun donacon."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donacu <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Vi ankoraŭ povas nuligi la donacon dum la pago."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirektante al la donacpaĝo…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / monato"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "por 1 monato"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "por 3 monatoj"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "por 6 monatoj"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "por 12 monatoj"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "dum 24 monatoj"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "dum 48 monatoj"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "dum 96 monatoj"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "por 1 monato “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "dum 3 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "por 6 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "por 12 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "dum 24 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "dum 48 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "dum 96 monatoj “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donaco"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Dato: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Sume: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / monato por %(duration)s monatoj, inkluzive de %(discounts)s%% rabato)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Sume: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / monato por %(duration)s monatoj)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Statuso: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identigilo: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Nuligi"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Ĉu vi certas, ke vi volas nuligi? Ne nuligu se vi jam pagis."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Jes, bonvolu nuligi"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Via donaco estis nuligita."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Faru novan donacon"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovu."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reordigi"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Vi jam pagis. Se vi volas revizii la pagajn instrukciojn tamen, alklaku ĉi tie:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Montri malnovajn paginstrukciojn"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Dankon pro via donaco!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Se vi ankoraŭ ne faris, notu vian sekretan ŝlosilon por ensaluti:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Alie vi povus esti eksigita el ĉi tiu konto!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "La paginstrukcioj nun estas malaktualaj. Se vi volas fari alian donacon, uzu la butonon \"Reordigi\" supre."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Grava noto:</strong> Kriptaj prezoj povas fluktuadi sovaĝe, foje eĉ ĝis 20%% en kelkaj minutoj. Tio tamen estas malpli ol la kotizoj, kiujn ni suferas kun multaj pagprovizantoj, kiuj ofte ŝargas 50-60%% por labori kun “ombra bonfarado” kiel ni. <u>Se vi sendas al ni la kvitancon kun la originala prezo, kiun vi pagis, ni ankoraŭ kreditos vian konton por la elektita membreco</u> (kondiĉe ke la kvitanco ne estas pli malnova ol kelkaj horoj). Ni vere dankas, ke vi pretas elteni tiajn aferojn por subteni nin! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Ĉi tiu donaco eksvalidiĝis. Bonvolu nuligi kaj krei novan."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Kriptaj instrukcioj"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transdonu al unu el niaj kriptaj kontoj"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donacu la tutan sumon de %(total)s al unu el ĉi tiuj adresoj:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Aĉetu Bitcoin per Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Trovu la paĝon “Kripto” en via PayPal-aplikaĵo aŭ retejo. Tio kutime estas sub “Financoj”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Sekvu la instrukciojn por aĉeti Bitcoin (BTC). Vi nur bezonas aĉeti la sumon, kiun vi volas donaci, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transdonu la Bitcoin al nia adreso"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Iru al la paĝo “Bitcoin” en via PayPal-aplikaĵo aŭ retejo. Premu la butonon “Transdoni” %(transfer_icon)s, kaj poste “Sendi”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Enigu nian Bitcoin (BTC) adreson kiel la ricevanton, kaj sekvu la instrukciojn por sendi vian donacon de %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Kreditkarto / debetkarto instrukcioj"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donaci per nia kreditkarta / debetkarta paĝo"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donaci %(amount)s en <a %(a_page)s>ĉi tiu paĝo</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Vidu la paŝon post paŝo gvidilon sube."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Statuso:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Atendante konfirmon (refreŝigu la paĝon por kontroli)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Atendante translokigon (refreŝigu la paĝon por kontroli)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Restanta tempo:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(vi eble volas nuligi kaj krei novan donacon)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Por restarigi la tempigilon, simple kreu novan donacon."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Ĝisdatigi statuson"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Se vi renkontas ajnajn problemojn, bonvolu kontakti nin ĉe %(email)s kaj inkluzivi kiel eble plej multe da informoj (kiel ekrankopiojn)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Se vi jam pagis:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Foje konfirmo povas daŭri ĝis 24 horoj, do certigu refreŝigi ĉi tiun paĝon (eĉ se ĝi eksvalidiĝis)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Aĉeti PYUSD moneron en PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Sekvu la instrukciojn por aĉeti PYUSD-monon (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Iru al la paĝo “PYUSD” en via PayPal-aplikaĵo aŭ retejo. Premu la butonon “Transfer” %(icon)s, kaj poste “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transdoni %(amount)s al %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Aĉetu Bitcoin (BTC) en Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Iru al la paĝo “Bitcoin” (BTC) en Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transdonu la Bitcoin al nia adreso"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Alklaku la butonon “Send bitcoin” por fari “retiron”. Ŝanĝu de dolaroj al BTC premante la %(icon)s ikonon. Enigu la BTC-sumon sube kaj alklaku “Send”. Vidu <a %(help_video)s>ĉi tiun videon</a> se vi blokas."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Por malgrandaj donacoj (sub $25), vi eble devos uzi Rush aŭ Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Aĉetu Bitcoin (BTC) en Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Iru al la paĝo “Crypto” en Revolut por aĉeti Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Aĉetu iom pli (ni rekomendas %(more)s pli) ol la sumo, kiun vi donacas (%(amount)s), por kovri transakciajn kotizojn. Vi konservos ĉion, kio restas."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transdonu la Bitcoin al nia adreso"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Alklaku la butonon “Send bitcoin” por fari “retiron”. Ŝanĝu de eŭroj al BTC premante la %(icon)s ikonon. Enigu la BTC-sumon sube kaj alklaku “Send”. Vidu <a %(help_video)s>ĉi tiun videon</a> se vi blokas."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Nepre uzu la kvanton de BTC sube, <em>NE</em> eŭroj aŭ dolaroj, alie ni ne ricevos la ĝustan sumon kaj ne povos aŭtomate konfirmi vian membrecon."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Por malgrandaj donacoj (sub $25) vi eble devos uzi Rush aŭ Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Uzu iun ajn el la sekvaj “kreditkarto al Bitcoin” rapidaj servoj, kiuj nur daŭras kelkajn minutojn:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Plenigu la jenajn detalojn en la formularo:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin kvanto:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Bonvolu uzi ĉi tiun <span %(underline)s>ĝustan kvanton</span>. Via totala kosto eble estos pli alta pro kreditkartaj kotizoj. Por malgrandaj kvantoj ĉi tio bedaŭrinde povas esti pli ol nia rabato."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adreso (ekstera monujo):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instrukcioj"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Ni subtenas nur la norman version de kriptomonedoj, ne ekzotikajn retojn aŭ versiojn de moneroj. Ĝi povas daŭri ĝis unu horo por konfirmi la transakcion, depende de la monero."

msgid "page.donation.crypto_qr_code_title"
msgstr "Skanu QR -kodon por pagi"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Skanu ĉi tiun QR -kodon per via Crypto -monujo por plenigi la pagajn detalojn"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon-donackarto"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Bonvolu uzi la <a %(a_form)s>oficialan Amazon.com formularon</a> por sendi al ni donackarton de %(amount)s al la retpoŝtadreso sube."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Ni ne povas akcepti aliajn metodojn de donackartoj, <strong>nur senditajn rekte de la oficiala formularo ĉe Amazon.com</strong>. Ni ne povas redoni vian donackarton se vi ne uzas ĉi tiun formularon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Enigu la ĝustan sumon: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Bonvolu NE skribi vian propran mesaĝon."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "\"Al\" ricevanto retpoŝto en la formo:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unika por via konto, ne kundividu."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Uzu nur unufoje."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Atendante donackarton… (refreŝigu la paĝon por kontroli)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Post sendado de via donackarto, nia aŭtomata sistemo konfirmos ĝin ene de kelkaj minutoj. Se tio ne funkcias, provu resendi vian donackarton (<a %(a_instr)s>instrukcioj</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Se tio ankoraŭ ne funkcias, bonvolu retpoŝti al ni kaj Anna mane revizios ĝin (tio povas daŭri kelkajn tagojn), kaj certigu mencii se vi jam provis resendi."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Ekzemplo:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Notu, ke la konta nomo aŭ bildo eble aspektas strange. Ne necesas zorgi! Ĉi tiuj kontoj estas administrataj de niaj donacaj partneroj. Niaj kontoj ne estis hakitaj."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Instrukcioj por Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donaci per Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun Alipay-konton</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Se la donacpaĝo estas blokita, provu alian interretan konekton (ekz. VPN aŭ telefona interreto)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Bedaŭrinde, la Alipay-paĝo ofte estas alirebla nur el <strong>ĉeftero Ĉinio</strong>. Vi eble devos provizore malŝalti vian VPN, aŭ uzi VPN al ĉeftero Ĉinio (aŭ foje ankaŭ funkcias Honkongo)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faru donacon (skanu QR-kodon aŭ premu butonon)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Malfermu la <a %(a_href)s>QR-koda donacpaĝo</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Skanu la QR-kodon per la Alipay-aplikaĵo, aŭ premu la butonon por malfermi la Alipay-aplikaĵon."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Bonvolu esti pacienca; la paĝo eble bezonos iom da tempo por ŝarĝi ĉar ĝi estas en Ĉinio."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instrukcioj por WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donaci per WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun WeChat-konton</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Instrukcioj por Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donaci per Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donacu la tutan sumon de %(total)s uzante <a %(a_account)s>ĉi tiun Pix-konton"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Retpoŝtu al ni la kvitancon"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Sendu kvitancon aŭ ekrankopion al via persona kontroladreso. NE uzu ĉi tiun retpoŝtadreson por via PayPal-donaco."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Sendu kvitancon aŭ ekrankopion al via persona kontroladreso:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Se la kripta kurzo ŝanĝiĝis dum la transakcio, certigu inkluzivi la kvitancon montrantan la originalan kurzon. Ni vere dankas vin pro la peno uzi kripton, ĝi multe helpas nin!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Kiam vi sendis vian kvitancon per retpoŝto, alklaku ĉi tiun butonon, por ke Anna mane reviziu ĝin (tio povas daŭri kelkajn tagojn):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Jes, mi sendis mian kvitancon per retpoŝto"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Dankon pro via donaco! Anna mane aktivigos vian membrecon ene de kelkaj tagoj."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Paŝo-post-paŝa gvidilo"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Kelkaj el la paŝoj mencias kriptomonedajn monujojn, sed ne zorgu, vi ne devas lerni ion ajn pri kripto por ĉi tio."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Enigu vian retpoŝton."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Elektu vian pagmanieron."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Elektu vian pagmanieron denove."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Elektu “Mem-gastigita” monujo."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Alklaku “Mi konfirmas posedon”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Vi devus ricevi retpoŝtan kvitancon. Bonvolu sendi tion al ni, kaj ni konfirmos vian donacon kiel eble plej baldaŭ."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Bonvolu atendi almenaŭ <span %(span_hours)s>24 horojn</span> (kaj refreŝigi ĉi tiun paĝon) antaŭ ol kontakti nin."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Se vi faris eraron dum pago, ni ne povas fari repagojn, sed ni provos ripari ĝin."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Miaj donacoj"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donacaj detaloj ne estas publike montrataj."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Neniuj donacoj ankoraŭ. <a %(a_donate)s>Fari mian unuan donacon.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Fari alian donacon."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Elŝutitaj dosieroj"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Elŝutoj de Rapidaj Partneraj Serviloj estas markitaj per %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Se vi elŝutis dosieron kun kaj rapidaj kaj malrapidaj elŝutoj, ĝi aperos dufoje."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Rapidaj elŝutoj en la lastaj 24 horoj kalkuliĝas al la ĉiutaga limo."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Ĉiuj tempoj estas en UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Elŝutitaj dosieroj ne estas publike montrataj."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Neniuj dosieroj elŝutitaj ankoraŭ."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Lasta 18 horoj"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Pli frue"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Konto"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Ensaluti / Registriĝi"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Konto ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Publika profilo: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Sekreta ŝlosilo (ne kundividu!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "montri"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Membreco: <strong>%(tier_name)s</strong> ĝis %(until_date)s <a %(a_extend)s>(plilongigi)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Membreco: <strong>Neniu</strong> <a %(a_become)s>(iĝi membro)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Rapidaj elŝutoj uzitaj (lastaj 24 horoj): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "kiuj elŝutoj?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Ekskluziva Telegram-grupo: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Partoprenu nin ĉi tie!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Ĝisdatigu al <a %(a_tier)s>pli alta nivelo</a> por partopreni nian grupon."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontaktu Annan ĉe %(email)s se vi interesiĝas pri ĝisdatigo de via membreco al pli alta nivelo."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontakta retpoŝto"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Vi povas kombini plurajn membrecojn (rapidaj elŝutoj po 24 horoj estos aldonitaj kune)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Publika profilo"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Elŝutitaj dosieroj"

msgid "layout.index.header.nav.my_donations"
msgstr "Miaj donacoj"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Elsaluti"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Vi nun estas elsalutinta. Reŝarĝu la paĝon por ensaluti denove."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registrado sukcese! Via sekreta ŝlosilo estas: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Konservu ĉi tiun ŝlosilon zorge. Se vi perdas ĝin, vi perdos aliron al via konto."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Legosigno.</strong> Vi povas legosigni ĉi tiun paĝon por retrovi vian ŝlosilon.</li><li %(li_item)s><strong>Elŝuto.</strong> Alklaku <a %(a_download)s>ĉi tiun ligilon</a> por elŝuti vian ŝlosilon.</li><li %(li_item)s><strong>Pasvorta administrilo.</strong> Uzu pasvortan administrilon por konservi la ŝlosilon kiam vi enigas ĝin sube.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Enigu vian sekretan ŝlosilon por ensaluti:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Sekreta ŝlosilo"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Ensaluti"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Nevalida sekreta ŝlosilo. Kontrolu vian ŝlosilon kaj provu denove, aŭ alternative registru novan konton sube."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Ne perdu vian ŝlosilon!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Ĉu vi ankoraŭ ne havas konton?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registri novan konton"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Se vi perdis vian ŝlosilon, bonvolu <a %(a_contact)s>kontakti nin</a> kaj provizi kiel eble plej multe da informoj."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Vi eble devos provizore krei novan konton por kontakti nin."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Malnova retpoŝta konto? Enigu vian <a %(a_open)s>retpoŝton ĉi tie</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Listo"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "redakti"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Konservi"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Konservita. Bonvolu reŝargi la paĝon."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reprovi."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Listo de %(by)s, kreita <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Listo estas malplena."

#, fuzzy
msgid "page.list.new_item"
msgstr "Aldonu aŭ forigu el ĉi tiu listo trovante dosieron kaj malfermante la langeton “Listoj”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profilo"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilo ne trovita."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "redakti"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Ŝanĝu vian montronomon. Via identigilo (la parto post “#”) ne povas esti ŝanĝita."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Konservi"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Konservita. Bonvolu reŝargi la paĝon."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reprovi."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profilo kreita <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Listoj"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Neniuj listoj ankoraŭ"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Kreu novan liston trovante dosieron kaj malfermante la langeton “Listoj”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reformo de kopirajto estas necesa por nacia sekureco"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Ĉinaj LLM-oj (inkluzive de DeepSeek) estas trejnitaj sur mia kontraŭleĝa arkivo de libroj kaj artikoloj — la plej granda en la mondo. La Okcidento devas reformi kopirajtleĝon kiel afero de nacia sekureco."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "akompanaj artikoloj de TorrentFreak: <a %(torrentfreak)s>unua</a>, <a %(torrentfreak_2)s>dua</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Ne tro longe antaŭe, “ombro-bibliotekoj” estis malaperantaj. Sci-Hub, la granda kontraŭleĝa arkivo de akademiaj artikoloj, ĉesis akcepti novajn verkojn pro procesoj. “Z-Library”, la plej granda kontraŭleĝa biblioteko de libroj, vidis siajn supozitajn kreintojn arestitaj pro krimaj kopirajtaj akuzoj. Ili nekredeble sukcesis eskapi sian areston, sed ilia biblioteko ne estas malpli sub minaco."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Kiam Z-Library alfrontis fermon, mi jam subtenis ĝian tutan bibliotekon kaj serĉis platformon por gastigi ĝin. Tio estis mia motivo por komenci la Arĥivon de Anna: daŭrigo de la misio malantaŭ tiuj pli fruaj iniciatoj. Ni ekde tiam kreskis por esti la plej granda ombro-biblioteko en la mondo, gastigante pli ol 140 milionojn da kopirajtitaj tekstoj tra diversaj formatoj — libroj, akademiaj artikoloj, magazinoj, gazetoj, kaj pli."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mia teamo kaj mi estas ideologoj. Ni kredas, ke konservi kaj gastigi ĉi tiujn dosierojn estas morale ĝuste. Bibliotekoj tra la mondo vidas buĝetajn tranĉojn, kaj ni ne povas fidi la heredaĵon de la homaro al korporacioj ankaŭ."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Tiam venis AI. Preskaŭ ĉiuj grandaj kompanioj konstruantaj LLM-ojn kontaktis nin por trejni sur niaj datumoj. Plej multaj (sed ne ĉiuj!) usonaj kompanioj rekonsideris post kiam ili ekkomprenis la kontraŭleĝan naturon de nia laboro. Kontraste, ĉinaj firmaoj entuziasme akceptis nian kolekton, ŝajne ne ĝenataj de ĝia laŭleĝeco. Ĉi tio estas rimarkinda donita la rolon de Ĉinio kiel subskribinto de preskaŭ ĉiuj gravaj internaciaj kopirajtaj traktatoj."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Ni donis altrapidan aliron al ĉirkaŭ 30 kompanioj. Plej multaj el ili estas LLM-kompanioj, kaj kelkaj estas datumaj perantoj, kiuj revendos nian kolekton. Plej multaj estas ĉinaj, kvankam ni ankaŭ laboris kun kompanioj el Usono, Eŭropo, Rusio, Sud-Koreio, kaj Japanio. DeepSeek <a %(arxiv)s>konfesis</a> ke pli frua versio estis trejnita sur parto de nia kolekto, kvankam ili estas silentemaj pri sia plej nova modelo (verŝajne ankaŭ trejnita sur niaj datumoj tamen)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se la Okcidento volas resti antaŭe en la vetkuro de LLM-oj, kaj finfine, AGI, ĝi devas rekonsideri sian pozicion pri kopirajto, kaj baldaŭ. Ĉu vi konsentas kun ni aŭ ne pri nia morala kazo, ĉi tio nun fariĝas kazo de ekonomiko, kaj eĉ de nacia sekureco. Ĉiuj potencoblokoj konstruas artefaritajn super-sciencistojn, super-hakistojn, kaj super-militistojn. Libereco de informo fariĝas afero de supervivo por ĉi tiuj landoj — eĉ afero de nacia sekureco."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Nia teamo estas el la tuta mondo, kaj ni ne havas apartan alineon. Sed ni kuraĝigus landojn kun fortaj kopirajtaj leĝoj uzi ĉi tiun ekzistadan minacon por reformi ilin. Do kion fari?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Nia unua rekomendo estas simpla: mallongigi la kopirajtan periodon. En Usono, kopirajto estas donita por 70 jaroj post la morto de la aŭtoro. Ĉi tio estas absurda. Ni povas alporti ĉi tion en linion kun patentoj, kiuj estas donitaj por 20 jaroj post la dosierado. Ĉi tio devus esti pli ol sufiĉa tempo por aŭtoroj de libroj, artikoloj, muziko, arto, kaj aliaj kreaj verkoj, por esti plene kompensitaj por siaj klopodoj (inkluzive de pli longtempaj projektoj kiel filmadaptadoj)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Tiam, minimume, politikofarantoj devus inkluzivi esceptojn por la amasa konservado kaj disvastigo de tekstoj. Se perdita enspezo de individuaj klientoj estas la ĉefa zorgo, persona-nivela distribuo povus resti malpermesita. Siaflanke, tiuj kapablaj administri vastajn deponejojn — kompanioj trejnantaj LLM-ojn, kune kun bibliotekoj kaj aliaj arĥivoj — estus kovritaj de ĉi tiuj esceptoj."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Iuj landoj jam faras version de ĉi tio. TorrentFreak <a %(torrentfreak)s>raportis</a> ke Ĉinio kaj Japanio enkondukis AI-esceptojn al siaj kopirajtaj leĝoj. Estas neklare al ni kiel ĉi tio interagas kun internaciaj traktatoj, sed ĝi certe donas kovron al iliaj enlandaj kompanioj, kio klarigas tion, kion ni vidis."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Kiel por la Arĥivo de Anna — ni daŭrigos nian subteran laboron radikitan en morala konvinko. Tamen nia plej granda deziro estas eniri la lumon, kaj plifortigi nian efikon laŭleĝe. Bonvolu reformi kopirajton."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Legu la akompanajn artikolojn de TorrentFreak: <a %(torrentfreak)s>unua</a>, <a %(torrentfreak_2)s>dua</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Gajnintoj de la $10,000 ISBN-vidiĝa rekompenco"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Ni ricevis kelkajn nekredeblajn submetojn al la $10,000 ISBN-vidiĝa rekompenco."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Antaŭ kelkaj monatoj ni anoncis <a %(all_isbns)s>$10,000 rekompencon</a> por fari la plej bonan eblan vidiĝon de niaj datumoj montrante la ISBN-spacon. Ni emfazis montri kiujn dosierojn ni jam arĥivis/ne arĥivis, kaj ni poste aldonis datumaron priskribantan kiom da bibliotekoj tenas ISBN-ojn (mezuro de maloftaĵo)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Ni estis superŝutitaj de la respondo. Estis tiom da kreemo. Granda dankon al ĉiuj, kiuj partoprenis: via energio kaj entuziasmo estas infektaj!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Finfine, ni volis respondi la jenajn demandojn: <strong>kiuj libroj ekzistas en la mondo, kiom ni jam arkivis, kaj sur kiuj libroj ni devus fokusiĝi poste?</strong> Estas bone vidi, ke tiom da homoj zorgas pri ĉi tiuj demandoj."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Ni mem komencis per baza bildigo. En malpli ol 300kb, ĉi tiu bildo koncize reprezentas la plej grandan plene malferman \"liston de libroj\" iam ajn kunmetitan en la historio de la homaro:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Ĉiuj ISBN-oj"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Dosieroj en la Arkivo de Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO-oj"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC-datuma liko"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID-oj"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost-a eLibro-Indekso"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Interreta Arkivo"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Tutmonda Registro de Eldonistoj"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Rusa Ŝtata Biblioteko"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperiestra Biblioteko de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Reen"

#, fuzzy
msgid "common.forward"
msgstr "Antaŭen"

#, fuzzy
msgid "common.last"
msgstr "Lasta"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Bonvolu vidi la <a %(all_isbns)s>originalan blogaĵon</a> por pli da informoj."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Ni eldonis defion por plibonigi tion. Ni aljuĝus unuan lokon kun premio de $6,000, duan lokon de $3,000, kaj trian lokon de $1,000. Pro la superforta respondo kaj nekredeblaj submetoj, ni decidis iomete pliigi la premion, kaj aljuĝi kvaran lokon kun premio de $500 ĉiu. La gajnintoj estas sube, sed nepre rigardu ĉiujn submetojn <a %(annas_archive)s>ĉi tie</a>, aŭ elŝutu nian <a %(a_2025_01_isbn_visualization_files)s>kombinitan torenton</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Unua loko $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Ĉi tiu <a %(phiresky_github)s>submeto</a> (<a %(annas_archive_note_2951)s>Gitlab-komento</a>) estas simple ĉio, kion ni volis, kaj pli! Ni aparte ŝatis la nekredeble flekseblajn vizualigajn opciojn (eĉ subtenantajn kutimajn ombrojn), sed kun ampleksa listo de antaŭagordoj. Ni ankaŭ ŝatis kiel rapide kaj glate ĉio funkcias, la simpla efektivigo (kiu eĉ ne havas malantaŭan finon), la lerta minimapo, kaj ampleksa klarigo en ilia <a %(phiresky_github)s>blogaĵo</a>. Nekredebla laboro, kaj bone meritita gajninto!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Dua loko $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Alia nekredebla <a %(annas_archive_note_2913)s>submeto</a>. Ne tiel fleksebla kiel la unua loko, sed ni fakte preferis ĝian makronivelan vizualigon super la unua loko (spaco-pleniga kurbo, limoj, etikedado, elstarigo, panoramado, kaj zomado). Komento de <a %(annas_archive_note_2971)s>Joe Davis</a> resonis kun ni:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Dum perfektaj kvadratoj kaj rektanguloj estas matematike plaĉaj, ili ne provizas superan lokan proksimecon en mapiga kunteksto. Mi kredas, ke la asimetrio eneca en ĉi tiuj Hilbert aŭ klasikaj Morton ne estas difekto sed trajto. Same kiel la fame boto-forma konturo de Italio igas ĝin tuj rekonebla sur mapo, la unikaj \"kapricoj\" de ĉi tiuj kurboj povas servi kiel kognaj orientiloj. Ĉi tiu distingivo povas plibonigi spacan memoron kaj helpi uzantojn orientiĝi, eble faciligante trovi specifajn regionojn aŭ rimarki ŝablonojn.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Kaj ankoraŭ multaj opcioj por vizualigado kaj redonado, same kiel nekredeble glata kaj intuicia UI. Solida dua loko!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tria loko $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "En ĉi tiu <a %(annas_archive_note_2940)s>submeto</a> ni vere ŝatis la diversajn specojn de vidoj, precipe la komparajn kaj eldonistajn vidojn."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tria loko $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Kvankam ne la plej polurita UI, ĉi tiu <a %(annas_archive_note_2917)s>submeto</a> kontrolas multajn el la skatoloj. Ni aparte ŝatis ĝian komparan funkcion."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tria loko $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Kiel la unua loko, ĉi tiu <a %(annas_archive_note_2975)s>submeto</a> impresis nin per sia fleksebleco. Finfine tio estas kio faras bonegan vizualigan ilon: maksimuma fleksebleco por potencaj uzantoj, dum konservante aferojn simplaj por mezaj uzantoj."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tria loko $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "La fina <a %(annas_archive_note_2947)s>submeto</a> por ricevi premion estas sufiĉe baza, sed havas kelkajn unikajn trajtojn, kiujn ni vere ŝatis. Ni ŝatis kiel ili montras kiom da Datasets kovras specifan ISBN kiel mezuron de populareco/fiableco. Ni ankaŭ vere ŝatis la simplecon sed efikecon de uzado de opakeca glitilo por komparoj."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Notindaj ideoj"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Kelkaj pliaj ideoj kaj efektivigoj, kiujn ni aparte ŝatis:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Nubskrapuloj por maloftaĵo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Vivaj statistikoj"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Notoj, kaj ankaŭ vivaj statistikoj"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unika mapvidaĵo kaj filtriloj"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Malvarmeta defaŭlta kolorskemo kaj varmomapo."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facila ŝanĝado de datasets por rapidaj komparoj."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Belaj etikedoj."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skaldrinkejo kun nombro de libroj."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Multaj glitiloj por kompari datasets, kvazaŭ vi estus diskĵokeo."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Ni povus daŭrigi dum iom da tempo, sed ni haltu ĉi tie. Nepre rigardu ĉiujn alsendojn <a %(annas_archive)s>ĉi tie</a>, aŭ elŝutu nian <a %(a_2025_01_isbn_visualization_files)s>kombinitan torenton</a>. Tiel multaj alsendoj, kaj ĉiu alportas unikan perspektivon, ĉu en UI aŭ efektivigo."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Ni almenaŭ integrigos la unuan lokon alsendon en nian ĉefan retejon, kaj eble kelkajn aliajn. Ni ankaŭ komencis pensi pri kiel organizi la procezon de identigado, konfirmado, kaj tiam arkivado de la plej maloftaj libroj. Pli venos pri ĉi tiu fronto."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Dankon al ĉiuj kiuj partoprenis. Estas mirinde ke tiom da homoj zorgas."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Niaj koroj estas plenaj de dankemo."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Vizualigante Ĉiujn ISBN-ojn — $10,000 rekompenco ĝis 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Ĉi tiu bildo reprezentas la plej grandan plene malferman “liston de libroj” iam ajn kunmetitan en la historio de la homaro."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Ĉi tiu bildo estas 1000×800 pikseloj. Ĉiu pikselo reprezentas 2,500 ISBN-ojn. Se ni havas dosieron por ISBN, ni faras tiun pikselo pli verda. Se ni scias ke ISBN estis eldonita, sed ni ne havas kongruan dosieron, ni faras ĝin pli ruĝa."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En malpli ol 300kb, ĉi tiu bildo koncize reprezentas la plej grandan plene malferman “liston de libroj” iam ajn kunmetitan en la historio de la homaro (kelkaj centoj da GB kunpremitaj plene)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ĝi ankaŭ montras: estas multe da laboro restanta en subtenado de libroj (ni nur havas 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Fono"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Kiel povas Arkivo de Anna atingi sian mision subteni la tutan scion de la homaro, sen scii kiuj libroj ankoraŭ ekzistas? Ni bezonas TODO-liston. Unu maniero por mapi tion estas per ISBN-nombroj, kiuj ekde la 1970-aj jaroj estis asignitaj al ĉiu publikigita libro (en plej multaj landoj)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Ne ekzistas centra aŭtoritato kiu scias ĉiujn ISBN-asignojn. Anstataŭe, ĝi estas distribuita sistemo, kie landoj ricevas nombro-gamojn, kiuj poste asignas pli malgrandajn gamojn al ĉefaj eldonistoj, kiuj eble plu subdividos gamojn al malpli grandaj eldonistoj. Fine, individuaj nombroj estas asignitaj al libroj."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Ni komencis mapi ISBN-ojn <a %(blog)s>antaŭ du jaroj</a> per nia skrapado de ISBNdb. Ekde tiam, ni skrapis multajn pli da fontoj de metadata, kiel <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, kaj pli. Plena listo troveblas en la paĝoj “Datasets” kaj “Torrents” en Arkivo de Anna. Ni nun havas longe la plej grandan plene malferman, facile elŝuteblan kolekton de libro-metadata (kaj tiel ISBN-oj) en la mondo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Ni <a %(blog)s>amplekse verkis</a> pri kial ni zorgas pri konservado, kaj kial ni nuntempe estas en kritika fenestro. Ni devas nun identigi rarajn, malfokusitajn, kaj unike riskatajn librojn kaj konservi ilin. Havi bonan metadata pri ĉiuj libroj en la mondo helpas tion."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Vidigi"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Krom la superrigarda bildo, ni ankaŭ povas rigardi individuajn datasets kiujn ni akiris. Uzu la falmenuon kaj butonojn por ŝanĝi inter ili."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Estas multaj interesaj ŝablonoj por vidi en ĉi tiuj bildoj. Kial estas iu reguleco de linioj kaj blokoj, kiu ŝajnas okazi ĉe malsamaj skaloj? Kio estas la malplenaj areoj? Kial certaj datasets estas tiel grupigitaj? Ni lasos ĉi tiujn demandojn kiel ekzercon por la leganto."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 rekompenco"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Estas multe por esplori ĉi tie, do ni anoncas rekompencon por plibonigi la vidigon supre. Male al plej multaj el niaj rekompencoj, ĉi tiu estas tempolima. Vi devas <a %(annas_archive)s>sendi</a> vian malfermitkoda programo antaŭ 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La plej bona submeto ricevos $6,000, dua loko estas $3,000, kaj tria loko estas $1,000. Ĉiuj rekompencoj estos aljuĝitaj uzante Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Sube estas la minimumaj kriterioj. Se neniu submeto plenumas la kriteriojn, ni eble ankoraŭ aljuĝos iujn rekompencojn, sed tio estos laŭ nia bontrovo."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forku ĉi tiun repo, kaj redaktu ĉi tiun bloga afiŝo HTML (neniu alia malantaŭa sistemo krom nia Flask malantaŭa sistemo estas permesata)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faru la bildon supre glate zomebla, tiel ke vi povas zomi ĝis individuaj ISBN-oj. Alklakante ISBN-ojn devus konduki vin al metadata paĝo aŭ serĉo en Arkivo de Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Vi ankoraŭ devas povi ŝanĝi inter ĉiuj malsamaj datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landaj gamoj kaj eldonistaj gamoj devus esti elstarigitaj kiam oni pasas super ili. Vi povas uzi ekz. <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> por landa informo, kaj nia “isbngrp” skrapado por eldonistoj (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Ĝi devas bone funkcii sur labortablo kaj poŝtelefono."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Por bonusaj punktoj (ĉi tiuj estas nur ideoj — lasu vian kreemon flugi):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Forta konsidero estos donita al uzebleco kaj kiel bone ĝi aspektas."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Montru faktan metadata por individuaj ISBN-oj kiam zomi, kiel titolo kaj aŭtoro."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Pli bona spaco-pleniganta kurbo. Ekz. zigzago, irante de 0 ĝis 4 en la unua vico kaj poste reen (inverse) de 5 ĝis 9 en la dua vico — rekursive aplikita."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Diversaj aŭ personecigeblaj koloraj skemoj."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Specialaj vidpunktoj por kompari datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Manieroj por senararigi problemojn, kiel aliaj metadata kiuj ne bone kongruas (ekz. tre malsamaj titoloj)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotado de bildoj kun komentoj pri ISBN-oj aŭ intervaloj."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Ajna heuristiko por identigi maloftajn aŭ riskajn librojn."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Kiaj ajn kreemaj ideoj vi povas elpensi!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Vi POVAS tute foriri de la minimumaj kriterioj, kaj fari tute malsaman vizualigon. Se ĝi estas vere spektakla, tiam tio kvalifikiĝas por la rekompenco, sed laŭ nia diskreteco."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faru submetojn per afiŝado de komento al <a %(annas_archive)s>ĉi tiu afero</a> kun ligilo al via forkita repo, kuniga peto, aŭ diferenco."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kodo"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "La kodo por generi ĉi tiujn bildojn, same kiel aliajn ekzemplojn, troviĝas en <a %(annas_archive)s>ĉi tiu dosierujo</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Ni elpensis kompaktan datumformaton, kun kiu ĉiuj bezonataj ISBN-informoj estas ĉirkaŭ 75MB (kunpremitaj). La priskribo de la datumformato kaj kodo por generi ĝin troviĝas <a %(annas_archive_l1244_1319)s>ĉi tie</a>. Por la rekompenco vi ne estas devigita uzi ĉi tion, sed ĝi estas probable la plej oportuna formato por komenci. Vi povas transformi niajn metadata kiel ajn vi volas (kvankam via tuta kodo devas esti malfermfonta)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Ni ne povas atendi por vidi kion vi elpensas. Bonŝancon!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Arkivo de Anna-Ujoj (AAU): normigado de eldonoj de la plej granda ombra biblioteko en la mondo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Arkivo de Anna fariĝis la plej granda ombra biblioteko en la mondo, postulante ke ni normigu niajn eldonojn."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Arkivo de Anna</a> fariĝis longe la plej granda ombra biblioteko en la mondo, kaj la sola ombra biblioteko de sia skalo kiu estas plene malfermfonta kaj malfermdatumara. Sube estas tabelo de nia paĝo pri Datasets (iomete modifita):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Ni atingis ĉi tion per tri manieroj:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Spegulante ekzistantajn malfermdatumajn ombrajn bibliotekojn (kiel Sci-Hub kaj Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Helpi ombrajn bibliotekojn kiuj volas esti pli malfermaj, sed ne havis la tempon aŭ rimedojn por fari tion (kiel la Libgen komikso-kolekto)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Skrapante bibliotekojn kiuj ne volas dividi amase (kiel Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Por (2) kaj (3) ni nun mem administras konsiderindan kolekton de torentoj (centoj da TB-oj). Ĝis nun ni traktis ĉi tiujn kolektojn kiel unuopajn, signifante laŭmendan infrastrukturon kaj datuman organizon por ĉiu kolekto. Ĉi tio aldonas signifan superkoston al ĉiu eldono, kaj faras ĝin aparte malfacila fari pli laŭpaŝajn eldonojn."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Tial ni decidis normigi niajn eldonojn. Ĉi tio estas teknika bloga afiŝo en kiu ni enkondukas nian normon: <strong>Konteneroj de Arkivo de Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Dezajnaj celoj"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Nia ĉefa uzokazo estas la distribuo de dosieroj kaj asociita metadata el diversaj ekzistantaj kolektoj. Niaj plej gravaj konsideroj estas:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogenaj dosieroj kaj metadata, kiel eble plej proksime al la originala formato."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogenaj identigiloj en la fontaj bibliotekoj, aŭ eĉ manko de identigiloj."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Apartaj eldonoj de metadata kontraŭ dosieraj datumoj, aŭ nur-metadata eldonoj (ekz. nia ISBNdb eldono)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuo tra torentoj, kvankam kun la eblo de aliaj distribumetodoj (ekz. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Nemoveblaj rekordoj, ĉar ni devas supozi ke niaj torentoj vivos eterne."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Laŭpaŝaj eldonoj / aldonendaj eldonoj."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Maŝinlegeblaj kaj skribeblaj, oportune kaj rapide, precipe por nia stako (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Iom facile homa inspektado, kvankam ĉi tio estas sekundara al maŝinlegebleco."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile semigi niajn kolektojn per norma luita semkesto."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binara datumaro povas esti servata rekte de retserviloj kiel Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Iuj ne-celoj:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Ni ne zorgas pri dosieroj estantaj facile navigeblaj permane sur disko, aŭ serĉeblaj sen antaŭpretigo."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Ni ne zorgas pri esti rekte kongruaj kun ekzistanta bibliotekprogramaro."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Dum ĝi devus esti facila por iu ajn semigi nian kolekton uzante torentojn, ni ne atendas ke la dosieroj estu uzeblaj sen signifa teknika scio kaj sindediĉo."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Ĉar Arkivo de Anna estas malfermfonta, ni volas rekte uzi nian formaton. Kiam ni refreŝigas nian serĉindekson, ni nur aliras publike disponeblajn vojojn, tiel ke iu ajn kiu forkas nian bibliotekon povas rapide ekfunkcii."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "La normo"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Fine, ni decidis pri relative simpla normo. Ĝi estas sufiĉe malstrikta, nenormiga, kaj ankoraŭ evoluanta."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna Arkivo Ujo) estas unuopa ero konsistanta el <strong>metadata</strong>, kaj laŭvole <strong>binaraj datumoj</strong>, ambaŭ el kiuj estas nemoveblaj. Ĝi havas tutmonde unikan identigilon, nomatan <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Kolekto.</strong> Ĉiu AAC apartenas al kolekto, kiu laŭ difino estas listo de AAC-oj kiuj estas semantike koheraj. Tio signifas, ke se vi faras signifan ŝanĝon al la formato de la metadata, tiam vi devas krei novan kolekton."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“rekordoj” kaj “dosieroj” kolektoj.</strong> Laŭ kutimo, ofte estas oportune eldoni “rekordojn” kaj “dosierojn” kiel malsamajn kolektojn, por ke ili povu esti eldonitaj laŭ malsamaj horaroj, ekz. bazitaj sur skrapaj tarifoj. “Rekordo” estas nur-metadata kolekto, enhavanta informojn kiel librotitoloj, aŭtoroj, ISBN-oj, ktp., dum “dosieroj” estas la kolektoj kiuj enhavas la efektivajn dosierojn mem (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> La formato de AACID estas ĉi tio: <code style=\"color: #0093ff\">aacid__{kolekto}__{ISO 8601 tempstampo}__{kolekto-specifa ID}__{mallonguuid}</code>. Ekzemple, efektiva AACID kiun ni eldonis estas <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{kolekto}</code>: la nomo de la kolekto, kiu povas enhavi ASCII-literojn, nombrojn, kaj substrekojn (sed ne duoblajn substrekojn)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 tempstampo}</code>: mallonga versio de la ISO 8601, ĉiam en UTC, ekz. <code>20220723T194746Z</code>. Ĉi tiu nombro devas monotone kreski por ĉiu eldono, kvankam ĝiaj precizaj semantikoj povas diferenci laŭ kolekto. Ni sugestas uzi la tempon de skrapado aŭ de generado de la ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{kolekto-specifa ID}</code>: kolekto-specifa identigilo, se aplikebla, ekz. la Z-Library ID. Povas esti preterlasita aŭ mallongigita. Devas esti preterlasita aŭ mallongigita se la AACID alie superus 150 signojn."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{mallonguuid}</code>: UUID sed kunpremita al ASCII, ekz. uzante base57. Ni nuntempe uzas la <a %(github_skorokithakis_shortuuid)s>mallonguuid</a> Python-bibliotekon."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID gamo.</strong> Ĉar AACID-oj enhavas monotone kreskantajn tempstampojn, ni povas uzi tion por indiki gamojn ene de aparta kolekto. Ni uzas ĉi tiun formaton: <code style=\"color: blue\">aacid__{kolekto}__{de_tempstampo}--{ĝis_tempstampo}</code>, kie la tempstampoj estas inkluzivaj. Ĉi tio estas kongrua kun ISO 8601 notacio. Gamoj estas kontinuaj, kaj povas interkovri, sed en kazo de interkovro devas enhavi identajn rekordojn kiel la antaŭe eldonita en tiu kolekto (ĉar AAC-oj estas nemoveblaj). Mankantaj rekordoj ne estas permesitaj."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata dosiero.</strong> Metadata dosiero enhavas la metadata de gamo de AAC-oj, por unu aparta kolekto. Ĉi tiuj havas la jenajn ecojn:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Dosiernomo devas esti AACID gamo, prefiksita kun <code style=\"color: red\">anna_arkivo_meta__</code> kaj sekvita de <code>.jsonl.zstd</code>. Ekzemple, unu el niaj eldonoj nomiĝas<br><code><span style=\"color: red\">anna_arkivo_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Kiel indikita de la dosieretendaĵo, la dosiertipo estas <a %(jsonlines)s>JSON Linioj</a> kunpremita kun <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Ĉiu JSON objekto devas enhavi la jenajn kampojn ĉe la plej alta nivelo: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (laŭvole). Neniuj aliaj kampoj estas permesitaj."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> estas arbitra metadata, laŭ la semantiko de la kolekto. Ĝi devas esti semantike kohera ene de la kolekto."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> estas laŭvola, kaj estas la nomo de binara datumdosierujo kiu enhavas la respondajn binarajn datumojn. La dosiernomo de la respondaj binaraj datumoj ene de tiu dosierujo estas la AACID de la rekordo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "La <code style=\"color: red\">anna_arkivo_meta__</code> prefikso povas esti adaptita al la nomo de via institucio, ekz. <code style=\"color: red\">mia_instituto_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binara datumdosierujo.</strong> Dosierujo kun la binaraj datumoj de gamo de AAC-oj, por unu aparta kolekto. Ĉi tiuj havas la jenajn ecojn:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Dosierujnomo devas esti AACID gamo, prefiksita kun <code style=\"color: green\">anna_arkivo_data__</code>, kaj sen sufikso. Ekzemple, unu el niaj efektivaj eldonoj havas dosierujon nomatan<br><code><span style=\"color: green\">anna_arkivo_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "La dosierujo devas enhavi datumdosierojn por ĉiuj AAC-oj ene de la specifita gamo. Ĉiu datumdosiero devas havi sian AACID kiel la dosiernomo (sen etendaĵoj)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Estas rekomendite fari ĉi tiujn dosierujojn iel ajn mastrumeblaj laŭ grandeco, ekzemple ne pli grandaj ol 100GB-1TB ĉiu, kvankam ĉi tiu rekomendo povas ŝanĝiĝi kun la tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> La metadata dosieroj kaj binaraj datumdosierujoj povas esti kunigitaj en torentoj, kun unu torento por ĉiu metadata dosiero, aŭ unu torento por ĉiu binara datumdosierujo. La torentoj devas havi la originalan dosierujon/dosiernoman plus <code>.torrent</code> sufikson kiel sian dosiernoman."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Ekzemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Rigardu nian lastatempan eldonon de Z-Library kiel ekzemplon. Ĝi konsistas el du kolektoj: “<span style=\"background: #fffaa3\">zlib3_records</span>” kaj “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Ĉi tio permesas al ni aparte skrapi kaj eldoni metadata rekordojn de la realaj librodosieroj. Tiel, ni eldonis du torentojn kun metadata dosieroj:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Ni ankaŭ eldonis aron da torentoj kun binaraj datumdosierujoj, sed nur por la “<span style=\"background: #ffd6fe\">zlib3_files</span>” kolekto, entute 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Per rulado de <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> ni povas vidi kio estas interne:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "En ĉi tiu kazo, ĝi estas metadata de libro kiel raportite de Z-Library. Sur la plej alta nivelo ni nur havas “aacid” kaj “metadata”, sed neniu “data_folder”, ĉar ne estas respondaj binaraj datumoj. La AACID enhavas “22430000” kiel la ĉefa ID, kiun ni povas vidi estas prenita de “zlibrary_id”. Ni povas atendi, ke aliaj AAC-oj en ĉi tiu kolekto havos la saman strukturon."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nun ni rulu <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Ĉi tio estas multe pli malgranda AAC metadata, kvankam la plejparto de ĉi tiu AAC troviĝas aliloke en binara dosiero! Finfine, ni havas “data_folder” ĉi-foje, do ni povas atendi, ke la respondaj binaraj datumoj troviĝos ĉe <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. La “metadata” enhavas la “zlibrary_id”, do ni povas facile asocii ĝin kun la responda AAC en la “zlib_records” kolekto. Ni povus asocii laŭ diversaj manieroj, ekzemple per AACID — la normo ne preskribas tion."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Notu, ke ankaŭ ne necesas, ke la “metadata” kampo mem estu JSON. Ĝi povus esti ĉeno enhavanta XML aŭ ajnan alian datumformaton. Vi povus eĉ stoki metadata informojn en la asociita binara blobo, ekzemple se estas multe da datumoj."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Konkludo"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Kun ĉi tiu normo, ni povas fari eldonojn pli laŭgrade, kaj pli facile aldoni novajn datumfontojn. Ni jam havas kelkajn ekscitajn eldonojn en la tubaro!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Ni ankaŭ esperas, ke fariĝos pli facile por aliaj ombrobibliotekoj speguli niajn kolektojn. Finfine, nia celo estas konservi homan scion kaj kulturon por ĉiam, do ju pli da redundo des pli bone."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Ĝisdatigo de Anna: plene malferma fonta arkivo, ElasticSearch, 300GB+ da librokovriloj"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Ni laboris senĉese por provizi bonan alternativon kun la Arkivo de Anna. Jen kelkaj el la aferoj, kiujn ni atingis lastatempe."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Kun Z-Library malaperanta kaj ĝiaj (supozataj) fondintoj arestitaj, ni laboris senĉese por provizi bonan alternativon kun la Arkivo de Anna (ni ne ligos ĝin ĉi tie, sed vi povas serĉi ĝin en Google). Jen kelkaj el la aferoj, kiujn ni atingis lastatempe."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "La Arkivo de Anna estas plene malferma fonta"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Ni kredas, ke informo devus esti libera, kaj nia propra kodo ne estas escepto. Ni eldonis ĉiujn niajn kodojn en nia private gastigita Gitlab-instanco: <a %(annas_archive)s>Programaro de Anna</a>. Ni ankaŭ uzas la problemo-spurilon por organizi nian laboron. Se vi volas engaĝiĝi kun nia disvolviĝo, ĉi tio estas bonega loko por komenci."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Por doni al vi guston de la aferoj, kiujn ni laboras, rigardu nian lastatempan laboron pri plibonigoj de klientflanka agado. Ĉar ni ankoraŭ ne efektivigis paĝigon, ni ofte redonus tre longajn serĉpaĝojn, kun 100-200 rezultoj. Ni ne volis tro frue tranĉi la serĉrezultojn, sed ĉi tio signifis, ke ĝi malrapidigus iujn aparatojn. Por tio, ni efektivigis etan trukon: ni envolvis plej multajn serĉrezultojn en HTML-komentoj (<code><!-- --></code>), kaj poste skribis etan JavaScript, kiu detektus kiam rezulto devus iĝi videbla, en kiu momento ni malenvolvus la komenton:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualigo\" efektivigita en 23 linioj, neniu bezono por luksaj bibliotekoj! Tio estas la speco de rapida pragmata kodo, kiun vi finas kun limigita tempo kaj realaj problemoj, kiuj bezonas solvon. Oni raportis, ke nia serĉo nun bone funkcias sur malrapidaj aparatoj!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Alia granda peno estis aŭtomatigi la konstruadon de la datumbazo. Kiam ni lanĉis, ni simple hazarde kunigis malsamajn fontojn. Nun ni volas teni ilin ĝisdatigitaj, do ni verkis aron da skriptoj por elŝuti novajn metadatajn de la du Library Genesis forkoj kaj integri ilin. La celo estas ne nur fari ĉi tion utila por nia arkivo, sed ankaŭ faciligi aferojn por iu ajn, kiu volas ludi kun ombra biblioteko metadata. La celo estus Jupyter-notlibro, kiu havas ĉiajn interesajn metadatajn disponeblajn, por ke ni povu fari pli da esplorado kiel eltrovi kian <a %(blog)s>procenton de ISBN-oj estas konservitaj por ĉiam</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Fine, ni renovigis nian donacadan sistemon. Vi nun povas uzi kreditkarton por rekte deponi monon en niajn kriptajn monujojn, sen vere bezoni scii ion pri kriptovalutoj. Ni daŭre monitoros kiel bone ĉi tio funkcias en praktiko, sed ĉi tio estas granda afero."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Ŝanĝi al ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Unu el niaj <a %(annas_archive)s>biletoj</a> estis kolekto de problemoj kun nia serĉsistemo. Ni uzis MySQL plen-tekstan serĉon, ĉar ni havis ĉiujn niajn datumojn en MySQL ĉiuokaze. Sed ĝi havis siajn limojn:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Iuj demandoj daŭris tre longe, ĝis la punkto kie ili okupis ĉiujn malfermajn konektojn."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Defaŭlte MySQL havas minimuman vortlongon, aŭ via indekso povas fariĝi vere granda. Homoj raportis ne povi serĉi \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Serĉo estis nur iom rapida kiam plene ŝarĝita en memoro, kio postulis, ke ni akiru pli multekostan maŝinon por funkciigi ĉi tion, plus iujn komandojn por antaŭŝarĝi la indekson ĉe starto."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Ni ne povus facile etendi ĝin por konstrui novajn funkciojn, kiel pli bonan <a %(wikipedia_cjk_characters)s>tokenigon por lingvoj sen spacoj</a>, filtrado/faceting, ordigo, \"ĉu vi celis\" sugestoj, aŭtokompletigo, kaj tiel plu."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Post parolado kun aro da fakuloj, ni decidis pri ElasticSearch. Ĝi ne estis perfekta (iliaj defaŭltaj \"ĉu vi celis\" sugestoj kaj aŭtokompletigo funkcioj estas malbonaj), sed ĝenerale ĝi estis multe pli bona ol MySQL por serĉo. Ni ankoraŭ ne estas <a %(youtube)s>tro entuziasmaj</a> pri uzado de ĝi por ajna misio-kritika datumoj (kvankam ili faris multan <a %(elastic_co)s>progreson</a>), sed ĝenerale ni estas sufiĉe kontentaj kun la ŝanĝo."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por nun, ni efektivigis multe pli rapidan serĉon, pli bonan lingvan subtenon, pli bonan relevantecan ordigon, malsamajn ordigajn opciojn, kaj filtradon laŭ lingvo/libro tipo/dosiero tipo. Se vi estas scivolema kiel ĝi funkcias, <a %(annas_archive_l140)s>rigardu</a> <a %(annas_archive_l1115)s>ĉi</a> <a %(annas_archive_l1635)s>tion</a>. Ĝi estas sufiĉe alirebla, kvankam ĝi povus uzi iom pli da komentoj…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ da librokovriloj liberigitaj"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Fine, ni ĝojas anonci malgrandan eldonon. En kunlaboro kun la homoj, kiuj funkciigas la Libgen.rs forkon, ni dividas ĉiujn iliajn librokovrilojn per torentoj kaj IPFS. Ĉi tio distribuos la ŝarĝon de vidado de la kovriloj inter pli da maŝinoj, kaj konservos ilin pli bone. En multaj (sed ne ĉiuj) kazoj, la librokovriloj estas inkluzivitaj en la dosieroj mem, do ĉi tio estas speco de \"derivita datumoj\". Sed havi ĝin en IPFS estas ankoraŭ tre utila por ĉiutaga operacio de kaj la Arkivo de Anna kaj la diversaj Library Genesis forkoj."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Kiel kutime, vi povas trovi ĉi tiun eldonon ĉe la Pirata Biblioteko Spegulo (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>). Ni ne ligos al ĝi ĉi tie, sed vi povas facile trovi ĝin."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Espereble ni povas malstreĉi nian ritmon iomete, nun kiam ni havas decan alternativon al Z-Biblioteko. Ĉi tiu laborkvanto ne estas aparte daŭrigebla. Se vi interesiĝas helpi kun programado, servilaj operacioj, aŭ konservada laboro, certe kontaktu nin. Ankoraŭ estas multe da <a %(annas_archive)s>laboro por fari</a>. Dankon pro via intereso kaj subteno."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Arkivo de Anna subtenis la plej grandan ombran bibliotekon de bildstrioj en la mondo (95TB) — vi povas helpi semadi ĝin"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La plej granda ombra biblioteko de bildstrioj en la mondo havis unuopan punkton de malsukceso.. ĝis hodiaŭ."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskuti ĉe Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La plej granda ombra biblioteko de bildstrioj verŝajne estas tiu de aparta Library Genesis forko: Libgen.li. La unu administranto, kiu funkciigas tiun retejon, sukcesis kolekti frenezan kolekton de bildstrioj de pli ol 2 milionoj da dosieroj, sumante pli ol 95TB. Tamen, male al aliaj Library Genesis kolektoj, ĉi tiu ne estis disponebla amase per torentoj. Vi povis nur aliri ĉi tiujn bildstriojn individue per lia malrapida persona servilo — unuopa punkto de malsukceso. Ĝis hodiaŭ!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "En ĉi tiu afiŝo ni rakontos al vi pli pri ĉi tiu kolekto, kaj pri nia monkolekto por subteni pli de ĉi tiu laboro."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>D-ro Barbara Gordon provas perdi sin en la ĉiutaga mondo de la biblioteko…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-forkoj"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Unue, iom da fono. Vi eble konas Library Genesis pro ilia epika librokolekto. Malpli da homoj scias, ke Library Genesis-volontuloj kreis aliajn projektojn, kiel ekzemple konsiderindan kolekton de magazinoj kaj normaj dokumentoj, plenan sekurkopion de Sci-Hub (en kunlaboro kun la fondinto de Sci-Hub, Alexandra Elbakyan), kaj efektive, grandegan kolekton de bildstrioj."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Iumomente malsamaj operaciantoj de Library Genesis speguloj iris siajn apartajn vojojn, kio kaŭzis la nunan situacion havi kelkajn malsamajn \"forkojn\", ĉiuj ankoraŭ portante la nomon Library Genesis. La Libgen.li-forko unike havas ĉi tiun bildstrian kolekton, same kiel konsiderindan magazinan kolekton (kiun ni ankaŭ laboras pri)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Kunlaboro"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Pro ĝia grandeco, ĉi tiu kolekto delonge estis en nia dezirlisto, do post nia sukceso kun sekurkopiado de Z-Library, ni celis ĉi tiun kolekton. Komence ni skrapis ĝin rekte, kio estis sufiĉe defia, ĉar ilia servilo ne estis en la plej bona stato. Ni akiris ĉirkaŭ 15TB ĉi tiamaniere, sed ĝi estis malrapida."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Feliĉe, ni sukcesis kontakti la operacianton de la biblioteko, kiu konsentis sendi al ni ĉiujn datumojn rekte, kio estis multe pli rapida. Tamen, ĝi ankoraŭ daŭris pli ol duonon de jaro por transdoni kaj prilabori ĉiujn datumojn, kaj ni preskaŭ perdis ĉion pro diska korupto, kio signifus komenci denove."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Ĉi tiu sperto igis nin kredi, ke estas grave eligi ĉi tiujn datumojn kiel eble plej rapide, por ke ili povu esti spegulitaj vaste. Ni estas nur unu aŭ du malbonŝancaj okazaĵoj for de perdi ĉi tiun kolekton por ĉiam!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La kolekto"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Rapide moviĝi signifas, ke la kolekto estas iom neorganizita… Ni rigardu. Imagu, ke ni havas dosiersistemon (kiun ni reale disigas tra torentoj):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "La unua dosierujo, <code>/repository</code>, estas la pli strukturita parto de ĉi tio. Ĉi tiu dosierujo enhavas tiel nomatajn \"mil dosierujojn\": dosierujoj ĉiu kun mil dosieroj, kiuj estas plinombritaj en la datumbazo. Dosiersistemo <code>0</code> enhavas dosierojn kun comic_id 0–999, kaj tiel plu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Ĉi tio estas la sama skemo, kiun Library Genesis uzis por siaj fikciaj kaj nefikciaj kolektoj. La ideo estas, ke ĉiu \"mil dosierujo\" aŭtomate fariĝas torento tuj kiam ĝi estas plenigita."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tamen, la Libgen.li-operacianto neniam kreis torentojn por ĉi tiu kolekto, kaj tiel la mil dosierujoj verŝajne fariĝis maloportunaj, kaj cedis al \"neordigitaj dosierujoj\". Ĉi tiuj estas <code>/comics0</code> tra <code>/comics4</code>. Ili ĉiuj enhavas unikajn dosierujajn strukturojn, kiuj verŝajne havis sencon por kolekti la dosierojn, sed nun ne havas tro da senco por ni. Feliĉe, la metadata ankoraŭ rekte rilatas al ĉiuj ĉi tiuj dosieroj, do ilia stokada organizo sur disko efektive ne gravas!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "La metadata estas disponebla en la formo de MySQL-datumbazo. Ĉi tio povas esti elŝutita rekte de la Libgen.li-retejo, sed ni ankaŭ faros ĝin disponebla en torento, kune kun nia propra tabelo kun ĉiuj MD5-hashoj."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analizo"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Kiam vi ricevas 95TB enmetitajn en vian stokadan klustron, vi provas kompreni, kio eĉ estas tie… Ni faris iun analizon por vidi ĉu ni povus iom redukti la grandecon, ekzemple forigante duplikatojn. Jen kelkaj el niaj trovoj:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantikaj duplikatoj (malsamaj skanoj de la sama libro) teorie povas esti filtritaj, sed ĝi estas malfacila. Kiam ni mane rigardis tra la bildstrioj, ni trovis tro multajn falsajn pozitivajn."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Estas iuj duplikatoj nur per MD5, kio estas relative malŝparema, sed filtrante tiujn for donus al ni nur ĉirkaŭ 1% in ŝparadon. Je ĉi tiu skalo tio estas ankoraŭ ĉirkaŭ 1TB, sed ankaŭ, je ĉi tiu skalo 1TB vere ne gravas. Ni prefere ne riskus hazarde detrui datumojn en ĉi tiu procezo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Ni trovis amason da ne-libraj datumoj, kiel filmoj bazitaj sur bildstrioj. Tio ankaŭ ŝajnas malŝparema, ĉar ĉi tiuj jam estas vaste disponeblaj per aliaj rimedoj. Tamen, ni rimarkis, ke ni ne povis simple filtri filmajn dosierojn, ĉar estas ankaŭ <em>interagaj bildstrioj</em> kiuj estis publikigitaj sur la komputilo, kiujn iu registris kaj konservis kiel filmojn."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Fine, ĉio, kion ni povus forigi el la kolekto, ŝparus nur kelkajn procentojn. Tiam ni rememoris, ke ni estas datumamantoj, kaj la homoj, kiuj spegulos ĉi tion, ankaŭ estas datumamantoj, do, \"KION VI SIGNIFAS, FORIGI?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Ni do prezentas al vi la plenan, nemodifitan kolekton. Estas multe da datumoj, sed ni esperas, ke sufiĉe da homoj zorgos semadi ĝin tamen."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Monkolekto"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Ni publikigas ĉi tiujn datumojn en kelkaj grandaj partoj. La unua torento estas de <code>/comics0</code>, kiun ni metis en unu grandegan 12TB .tar-dosieron. Tio estas pli bona por via malmola disko kaj torenta programaro ol amaso da pli malgrandaj dosieroj."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Kiel parto de ĉi tiu eldono, ni faras monkolekton. Ni celas kolekti $20,000 por kovri operaciajn kaj kontraktajn kostojn por ĉi tiu kolekto, same kiel ebligi daŭrajn kaj estontajn projektojn. Ni havas kelkajn <em>grandegajn</em> en la laboro."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Kiun mi subtenas per mia donaco?</em> Mallonge: ni subtenas ĉiujn sciojn kaj kulturojn de la homaro, kaj faras ilin facile alireblaj. Ĉiuj niaj kodo kaj datumoj estas malfermitaj fontoj, ni estas tute volontula projekto, kaj ni savis 125TB da libroj ĝis nun (aldone al la ekzistantaj torentoj de Libgen kaj Scihub). Finfine ni konstruas radon, kiu ebligas kaj instigas homojn trovi, skani, kaj sekurkopii ĉiujn librojn en la mondo. Ni skribos pri nia majstra plano en estonta afiŝo. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se vi donacas por 12-monata \"Mirinda Arkivisto\" membreco ($780), vi povas <strong>“adopti torenton”</strong>, signifante ke ni metos vian uzantnomon aŭ mesaĝon en la dosiernomo de unu el la torentoj!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Vi povas donaci irante al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a> kaj alklakante la butonon \"Donaci\". Ni ankaŭ serĉas pli da volontuloj: programistoj, sekurecaj esploristoj, anonimaj komercaj fakuloj, kaj tradukistoj. Vi ankaŭ povas subteni nin provizante gastigajn servojn. Kaj kompreneble, bonvolu semadi niajn torentojn!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dankon al ĉiuj, kiuj jam tiel ĝeneroze subtenis nin! Vi vere faras diferencon."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Jen la torentoj publikigitaj ĝis nun (ni ankoraŭ prilaboras la reston):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Ĉiuj torentoj troveblas en <a %(wikipedia_annas_archive)s>Arkivo de Anna</a> sub \"Datasets\" (ni ne ligas tie rekte, do ligoj al ĉi tiu blogo ne estas forigitaj de Reddit, Twitter, ktp). De tie, sekvu la ligon al la Tor-retejo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Kio sekvas?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Amaso da torentoj estas bonegaj por longtempa konservado, sed ne tiom por ĉiutaga aliro. Ni laboros kun gastigaj partneroj por meti ĉiujn ĉi tiujn datumojn en la reton (ĉar Arkivo de Anna ne gastigas ion rekte). Kompreneble vi povos trovi ĉi tiujn elŝutligojn en Arkivo de Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Ni ankaŭ invitas ĉiujn fari aferojn kun ĉi tiuj datumoj! Helpu nin pli bone analizi ilin, dedupli ilin, meti ilin en IPFS, remiksi ilin, trejni viajn AI-modelojn kun ili, kaj tiel plu. Ĉio estas via, kaj ni ne povas atendi vidi, kion vi faros kun ĝi."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Fine, kiel dirite antaŭe, ni ankoraŭ havas kelkajn grandegajn eldonojn venontajn (se <em>iu</em> povus <em>hazarde</em> sendi al ni elŝuton de <em>certa</em> ACS4-datumbazo, vi scias kie trovi nin...), same kiel konstruante la radon por sekurkopii ĉiujn librojn en la mondo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Do restu agordita, ni nur komencas."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novaj libroj aldonitaj al la Pirata Biblioteka Spegulo (+24TB, 3.8 milionoj da libroj)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "En la originala eldono de la Pirata Biblioteka Spegulo (EDIT: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>), ni faris spegulon de Z-Library, granda kontraŭleĝa librokolekto. Kiel memorigilo, jen kion ni skribis en tiu originala bloga afiŝo:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library estas populara (kaj kontraŭleĝa) biblioteko. Ili prenis la kolekton de Library Genesis kaj faris ĝin facile serĉebla. Krome, ili fariĝis tre efikaj en petado de novaj libro-kontribuoj, instigante kontribuantajn uzantojn per diversaj avantaĝoj. Ili nuntempe ne kontribuas ĉi tiujn novajn librojn reen al Library Genesis. Kaj male al Library Genesis, ili ne faras sian kolekton facile spegulebla, kio malhelpas vastan konservadon. Ĉi tio estas grava por ilia komerca modelo, ĉar ili ŝargas monon por aliri sian kolekton amase (pli ol 10 libroj tage)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Ni ne faras moralajn juĝojn pri postulado de mono por amasa aliro al kontraŭleĝa kolekto de libroj. Estas sendube, ke la Z-Biblioteko sukcesis plivastigi aliron al scio kaj akiri pli da libroj. Ni estas simple ĉi tie por fari nian parton: certigi la longdaŭran konservadon de ĉi tiu privata kolekto."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Tiu kolekto datiĝas de meze de 2021. Dume, la Z-Biblioteko kreskis je miriga rapideco: ili aldonis ĉirkaŭ 3.8 milionojn da novaj libroj. Estas iuj duplikatoj tie, certe, sed la plimulto ŝajnas esti legitime novaj libroj aŭ pli altkvalitaj skanaĵoj de antaŭe senditaj libroj. Ĉi tio estas grandparte pro la pliigita nombro de volontulaj moderigantoj ĉe la Z-Biblioteko kaj ilia amasa alŝuta sistemo kun deduplikado. Ni ŝatus gratuli ilin pri ĉi tiuj atingoj."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Ni ĝojas anonci, ke ni akiris ĉiujn librojn, kiuj estis aldonitaj al la Z-Biblioteko inter nia lasta spegulo kaj aŭgusto 2022. Ni ankaŭ revenis kaj skrapis iujn librojn, kiujn ni maltrafis la unuan fojon. Ĉiukaze, ĉi tiu nova kolekto estas ĉirkaŭ 24TB, kio estas multe pli granda ol la lasta (7TB). Nia spegulo nun estas 31TB entute. Denove, ni deduplikis kontraŭ Library Genesis, ĉar jam estas torentoj disponeblaj por tiu kolekto."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Bonvolu iri al la Pirata Biblioteka Spegulo por kontroli la novan kolekton (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>). Estas pli da informoj tie pri kiel la dosieroj estas strukturitaj kaj kio ŝanĝiĝis ekde la lasta fojo. Ni ne ligos al ĝi de ĉi tie, ĉar ĉi tio estas nur bloga retejo, kiu ne gastigas iujn kontraŭleĝajn materialojn."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Kompreneble, semado ankaŭ estas bonega maniero helpi nin. Dankon al ĉiuj, kiuj semas nian antaŭan aron de torentoj. Ni estas dankemaj pro la pozitiva respondo kaj ĝojas, ke estas tiom da homoj, kiuj zorgas pri konservado de scio kaj kulturo en ĉi tiu nekutima maniero."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Kiel fariĝi pirata arkivisto"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "La unua defio povus esti surpriza. Ĝi ne estas teknika problemo aŭ jura problemo. Ĝi estas psikologia problemo."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antaŭ ol ni plonĝas enen, du ĝisdatigoj pri la Pirata Biblioteka Spegulo (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Ni ricevis kelkajn ekstreme malavarajn donacojn. La unua estis $10k de anonima individuo, kiu ankaŭ subtenis \"bookwarrior\", la originalan fondinton de Library Genesis. Speciala danko al bookwarrior pro faciligado de ĉi tiu donaco. La dua estis alia $10k de anonima donacanto, kiu kontaktis nin post nia lasta eldono kaj estis inspirita helpi. Ni ankaŭ havis kelkajn pli malgrandajn donacojn. Multan dankon pro via malavara subteno. Ni havas kelkajn ekscitajn novajn projektojn en la tubaro, kiujn ĉi tio subtenos, do restu atentaj."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Ni havis iujn teknikajn malfacilaĵojn kun la grandeco de nia dua eldono, sed niaj torentoj nun estas supren kaj semas. Ni ankaŭ ricevis malavaran oferton de anonima individuo por semi nian kolekton sur iliaj tre rapidaj serviloj, do ni faras specialan alŝuton al iliaj maŝinoj, post kio ĉiuj aliaj, kiuj elŝutas la kolekton, devus vidi grandan plibonigon en rapideco."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Tuta libroj povus esti skribitaj pri la <em>kialo</em> de cifereca konservado ĝenerale, kaj pirata arkivismo aparte, sed ni donu rapidan enkondukon por tiuj, kiuj ne estas tro konataj. La mondo produktas pli da scio kaj kulturo ol iam ajn antaŭe, sed ankaŭ pli da ĝi perdiĝas ol iam ajn antaŭe. La homaro plejparte fidas je korporacioj kiel akademiaj eldonistoj, streaming-servoj, kaj sociaj amaskomunikiloj por ĉi tiu heredaĵo, kaj ili ofte ne pruviĝis esti grandaj gardantoj. Rigardu la dokumentan filmon Digital Amnesia, aŭ vere ajnan paroladon de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Estas iuj institucioj, kiuj faras bonan laboron arkivante tiom kiom ili povas, sed ili estas ligitaj de la leĝo. Kiel piratoj, ni estas en unika pozicio por arkivi kolektojn, kiujn ili ne povas tuŝi, pro kopirajto-devigo aŭ aliaj restriktoj. Ni ankaŭ povas speguli kolektojn multfoje, tra la mondo, tiel pliigante la ŝancojn de ĝusta konservado."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por nun, ni ne eniros diskutojn pri la avantaĝoj kaj malavantaĝoj de intelekta proprieto, la moraleco de leĝrompo, meditadoj pri cenzuro, aŭ la afero de aliro al scio kaj kulturo. Kun ĉio tio for, ni plonĝu en la <em>kiel</em>. Ni dividos kiel nia teamo fariĝis pirataj arkivistoj, kaj la lecionojn, kiujn ni lernis laŭ la vojo. Estas multaj defioj kiam vi ekiras ĉi tiun vojaĝon, kaj espereble ni povas helpi vin tra iuj el ili."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Komunumo"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "La unua defio povus esti surpriza. Ĝi ne estas teknika problemo aŭ jura problemo. Ĝi estas psikologia problemo: fari ĉi tiun laboron en la ombroj povas esti nekredeble soleca. Depende de tio, kion vi planas fari, kaj via minaca modelo, vi eble devos esti tre zorgema. Unuflanke de la spektro ni havas homojn kiel Alexandra Elbakyan*, la fondinto de Sci-Hub, kiu estas tre malferma pri siaj agadoj. Sed ŝi estas en alta risko esti arestita se ŝi vizitus okcidentan landon en ĉi tiu momento, kaj povus alfronti jardekojn da prizontempo. Ĉu tio estas risko, kiun vi pretus preni? Ni estas ĉe la alia fino de la spektro; estante tre zorgemaj ne lasi iun ajn spuron, kaj havante fortan operacian sekurecon."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Kiel menciite en HN de \"ynno\", Alexandra komence ne volis esti konata: \"Ŝiaj serviloj estis agorditaj por elsendi detalajn erarmesaĝojn de PHP, inkluzive de plena vojo de kulpa fontdosiero, kiu estis sub dosierujo /home/<USER>" Do, uzu hazardajn uzantnomojn sur la komputiloj, kiujn vi uzas por ĉi tiuj aferoj, en kazo vi misagordas ion."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Tiu sekreteco, tamen, venas kun psikologia kosto. Plej multaj homoj amas esti rekonitaj pro la laboro, kiun ili faras, kaj tamen vi ne povas preni iun ajn krediton por ĉi tio en la reala vivo. Eĉ simplaj aferoj povas esti defiaj, kiel amikoj demandantaj vin, kion vi faris (je iu punkto \"ludante kun mia NAS / hejmlaboratorio\" malnoviĝas)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Tial estas tiel grave trovi iun komunumon. Vi povas rezigni iom da operacia sekureco konfidi al iuj tre proksimaj amikoj, kiujn vi scias, ke vi povas profunde fidi. Eĉ tiam estu zorgema ne meti ion ajn en skribon, en kazo ili devas transdoni siajn retpoŝtojn al la aŭtoritatoj, aŭ se iliaj aparatoj estas kompromititaj en iu alia maniero."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Pli bone ankoraŭ estas trovi iujn samideanojn. Se viaj proksimaj amikoj interesiĝas aliĝi al vi, bonege! Alie, vi eble povos trovi aliajn interrete. Bedaŭrinde ĉi tio ankoraŭ estas niĉa komunumo. Ĝis nun ni trovis nur manplenon da aliaj, kiuj estas aktivaj en ĉi tiu spaco. Bonaj startpunktoj ŝajnas esti la forumoj de Library Genesis kaj r/DataHoarder. La Arkiva Teamo ankaŭ havas samideanojn, kvankam ili funkcias ene de la leĝo (eĉ se en iuj grizaj areoj de la leĝo). La tradiciaj \"warez\" kaj pirataj scenoj ankaŭ havas homojn, kiuj pensas en similaj manieroj."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Ni estas malfermitaj al ideoj pri kiel kulturi komunumon kaj esplori ideojn. Bonvolu sendi mesaĝon al ni ĉe Twitter aŭ Reddit. Eble ni povus gastigi ian forumon aŭ babilejon. Unu defio estas, ke tio facile povas esti cenzurita kiam oni uzas komunajn platformojn, do ni devus gastigi ĝin mem. Estas ankaŭ kompromiso inter havi ĉi tiujn diskutojn tute publikaj (pli da ebla engaĝiĝo) kontraŭ fari ĝin privata (ne lasi eblajn \"celojn\" scii, ke ni intencas skrapi ilin). Ni devos pripensi tion. Sciigu nin se vi interesiĝas pri tio!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projektoj"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Kiam ni faras projekton, ĝi havas kelkajn fazojn:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Elektado de domajno / filozofio: Kie vi proksimume volas fokusiĝi, kaj kial? Kiaj estas viaj unikaj pasioj, kapabloj, kaj cirkonstancoj, kiujn vi povas uzi al via avantaĝo?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Elektado de celo: Kiun specifan kolekton vi spegulos?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Skrapado de metadata: Katalogado de informoj pri la dosieroj, sen efektive elŝuti la (ofte multe pli grandajn) dosierojn mem."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Elektado de datumoj: Bazite sur la metadata, mallarĝigi kiuj datumoj estas plej gravaj por arkivi nun. Povus esti ĉio, sed ofte estas racia maniero ŝpari spacon kaj bendolarĝon."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Skrapado de datumoj: Efektive akiri la datumojn."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuado: Paki ĝin en torentojn, anonci ĝin ie, instigi homojn disvastigi ĝin."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ĉi tiuj ne estas tute sendependaj fazoj, kaj ofte komprenoj de pli posta fazo sendas vin reen al pli frua fazo. Ekzemple, dum skrapado de metadata vi povus rimarki, ke la celo, kiun vi elektis, havas defendajn mekanismojn preter via kapablo (kiel IP-blokoj), do vi reiras kaj trovas alian celon."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Elektado de domajno / filozofio"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Ne mankas scio kaj kultura heredaĵo por esti savita, kio povas esti superforta. Tial ofte utilas preni momenton kaj pensi pri kio via kontribuo povas esti."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Ĉiu havas malsaman manieron pensi pri tio, sed jen kelkaj demandoj, kiujn vi povus demandi vin mem:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Kial vi interesiĝas pri tio? Kio estas via pasio? Se ni povas akiri aron da homoj, kiuj ĉiuj arkivas la specojn de aferoj, kiujn ili specife zorgas, tio kovrus multon! Vi scios multe pli ol la averaĝa persono pri via pasio, kiel kio estas gravaj datumoj por savi, kiaj estas la plej bonaj kolektoj kaj interretaj komunumoj, kaj tiel plu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Kiajn kapablojn vi havas, kiujn vi povas uzi al via avantaĝo? Ekzemple, se vi estas fakulo pri reta sekureco, vi povas trovi manierojn venki IP-blokojn por sekuraj celoj. Se vi estas bonega en organizi komunumojn, tiam eble vi povas kunigi kelkajn homojn ĉirkaŭ celo. Tamen utilas scii iom da programado, se nur por konservi bonan operacian sekurecon tra ĉi tiu procezo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Kiom da tempo vi havas por tio? Nia konsilo estus komenci malgrande kaj fari pli grandajn projektojn dum vi alkutimiĝas al ĝi, sed ĝi povas fariĝi ĉion konsumanta."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Kio estus alta levila areo por fokusiĝi? Se vi intencas pasigi X horojn pri pirata arkivado, tiam kiel vi povas akiri la plej grandan \"eksplodon por via mono\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Kiaj estas unikaj manieroj, kiujn vi pensas pri tio? Vi eble havas iujn interesajn ideojn aŭ alirojn, kiujn aliaj eble preterlasis."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "En nia kazo, ni aparte zorgis pri la longtempa konservado de scienco. Ni sciis pri Library Genesis, kaj kiel ĝi estis plene spegulita multfoje per torentoj. Ni amis tiun ideon. Tiam unu tagon, unu el ni provis trovi iujn sciencajn lernolibrojn en Library Genesis, sed ne povis trovi ilin, kio metis en dubon kiom kompleta ĝi vere estis. Ni tiam serĉis tiujn lernolibrojn interrete, kaj trovis ilin en aliaj lokoj, kio plantis la semon por nia projekto. Eĉ antaŭ ol ni sciis pri la Z-Library, ni havis la ideon ne provi kolekti ĉiujn tiujn librojn permane, sed fokusiĝi al spegulaj ekzistantaj kolektoj, kaj kontribui ilin reen al Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Elektado de celo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Do, ni havas nian areon, kiun ni rigardas, nun kiun specifan kolekton ni spegulas? Estas kelkaj aferoj, kiuj faras bonan celon:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Granda"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unika: ne jam bone kovrita de aliaj projektoj."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Alirebla: ne uzas multajn tavolojn de protekto por malhelpi vin elŝuti iliajn metadata kaj datumojn."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Speciala kompreno: vi havas ian specialan informon pri ĉi tiu celo, kiel vi iel havas specialan aliron al ĉi tiu kolekto, aŭ vi eltrovis kiel venki iliajn defendojn. Ĉi tio ne estas postulata (nia venonta projekto ne faras ion specialan), sed certe helpas!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Kiam ni trovis niajn sciencajn lernolibrojn en retejoj krom Library Genesis, ni provis eltrovi kiel ili atingis la interreton. Ni tiam trovis la Z-Library, kaj rimarkis, ke dum plej multaj libroj ne unue aperas tie, ili finfine finiĝas tie. Ni lernis pri ĝia rilato al Library Genesis, kaj la (financa) instiga strukturo kaj supera uzantinterfaco, kiuj ambaŭ faris ĝin multe pli kompleta kolekto. Ni tiam faris iujn antaŭajn metadata kaj datumajn elŝutojn, kaj rimarkis, ke ni povus ĉirkaŭiri iliajn IP-elŝutajn limojn, utiligante la specialan aliron de unu el niaj membroj al multaj prokurilaj serviloj."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Dum vi esploras malsamajn celojn, jam gravas kaŝi viajn spurojn uzante VPN-ojn kaj forĵeteblajn retpoŝtadresojn, pri kiuj ni parolos pli poste."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata elŝutado"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Ni fariĝu iom pli teknikaj ĉi tie. Por efektive elŝuti la metadata de retejoj, ni konservis aferojn sufiĉe simplaj. Ni uzas Python-skriptojn, foje curl, kaj MySQL-datumbazon por stoki la rezultojn. Ni ne uzis iun ajn luksa elŝuta programaro kiu povas mapi kompleksajn retejojn, ĉar ĝis nun ni nur bezonis elŝuti unu aŭ du specojn de paĝoj simple numerante tra identigiloj kaj analizante la HTML. Se ne estas facile numeritaj paĝoj, tiam vi eble bezonos taŭgan rampilon kiu provas trovi ĉiujn paĝojn."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antaŭ ol vi komencas elŝuti tutan retejon, provu fari ĝin permane dum iom da tempo. Trarigardu kelkajn dekojn da paĝoj mem, por akiri senton pri kiel tio funkcias. Foje vi jam renkontos IP-blokojn aŭ alian interesan konduton ĉi tiel. La sama validas por datumelŝutado: antaŭ ol tro profundiĝi en ĉi tiun celon, certigu, ke vi efektive povas elŝuti ĝiajn datumojn efike."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Por ĉirkaŭiri limigojn, estas kelkaj aferoj, kiujn vi povas provi. Ĉu estas aliaj IP-adresoj aŭ serviloj, kiuj gastigas la samajn datumojn sed ne havas la samajn limigojn? Ĉu estas iuj API-finaĵoj, kiuj ne havas limigojn, dum aliaj havas? Je kia elŝuta rapideco via IP estas blokita, kaj por kiom longe? Aŭ ĉu vi ne estas blokita sed malrapidigita? Kio se vi kreas uzantokonton, kiel tiam ŝanĝiĝas aferoj? Ĉu vi povas uzi HTTP/2 por teni konektojn malfermitaj, kaj ĉu tio pliigas la rapidecon, je kiu vi povas peti paĝojn? Ĉu estas paĝoj, kiuj listigas plurajn dosierojn samtempe, kaj ĉu la informo listigita tie estas sufiĉa?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Aferoj, kiujn vi probable volas konservi, inkluzivas:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titolo"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Dosiernomo / loko"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: povas esti iu interna ID, sed ID-oj kiel ISBN aŭ DOI estas ankaŭ utilaj."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Grando: por kalkuli kiom da diskospaco vi bezonas."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hako (md5, sha1): por konfirmi, ke vi elŝutis la dosieron ĝuste."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Dato aldonita/modifita: tiel vi povas reveni poste kaj elŝuti dosierojn, kiujn vi ne elŝutis antaŭe (kvankam vi ofte ankaŭ povas uzi la ID aŭ hakon por tio)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Priskribo, kategorio, etikedoj, aŭtoroj, lingvo, ktp."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Ni kutime faras ĉi tion en du etapoj. Unue ni elŝutas la krudajn HTML-dosierojn, kutime rekte en MySQL (por eviti multajn malgrandajn dosierojn, pri kiuj ni parolas pli sube). Poste, en aparta paŝo, ni trarigardas tiujn HTML-dosierojn kaj analizas ilin en realajn MySQL-tabelojn. Tiel vi ne devas re-elŝuti ĉion de nulo se vi malkovras eraron en via analiza kodo, ĉar vi povas simple reprocesi la HTML-dosierojn kun la nova kodo. Ankaŭ ofte estas pli facile paraleligi la prilaboran paŝon, tiel ŝparante iom da tempo (kaj vi povas skribi la prilaboran kodon dum la elŝutado funkcias, anstataŭ devi skribi ambaŭ paŝojn samtempe)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Fine, notu ke por iuj celoj metadatenrubado estas ĉio, kio ekzistas. Estas kelkaj grandegaj metadatenkolektoj tie ekstere, kiuj ne estas ĝuste konservitaj."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Datuma elekto"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Ofte vi povas uzi la metadatenojn por eltrovi racian subaron de datumoj por elŝuti. Eĉ se vi fine volas elŝuti ĉiujn datumojn, povas esti utile prioritati la plej gravajn erojn unue, por kazo ke vi estu detektita kaj defendoj pliboniĝas, aŭ ĉar vi bezonus aĉeti pli da diskoj, aŭ simple ĉar io alia okazas en via vivo antaŭ ol vi povas elŝuti ĉion."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Ekzemple, kolekto povus havi plurajn eldonojn de la sama baza rimedo (kiel libro aŭ filmo), kie unu estas markita kiel la plej bona kvalito. Konservi tiujn eldonojn unue havus multan sencon. Vi eble fine volos konservi ĉiujn eldonojn, ĉar en iuj kazoj la metadatenoj povus esti malĝuste etikedita, aŭ povus esti nekonataj kompromisoj inter eldonoj (ekzemple, la \"plej bona eldono\" povus esti plej bona en plej multaj manieroj sed pli malbona en aliaj manieroj, kiel filmo havanta pli altan rezolucion sed mankantajn subtekstojn)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Vi ankaŭ povas serĉi vian metadatenan datumbazon por trovi interesajn aferojn. Kio estas la plej granda dosiero kiu estas gastigita, kaj kial ĝi estas tiel granda? Kio estas la plej malgranda dosiero? Ĉu estas interesaj aŭ neatenditaj ŝablonoj kiam temas pri certaj kategorioj, lingvoj, kaj tiel plu? Ĉu estas duplikataj aŭ tre similaj titoloj? Ĉu estas ŝablonoj al kiam datumoj estis aldonitaj, kiel unu tago en kiu multaj dosieroj estis aldonitaj samtempe? Vi ofte povas lerni multon rigardante la datumarojn en malsamaj manieroj."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "En nia kazo, ni deduplikis Z-Library librojn kontraŭ la md5-hashoj en Library Genesis, tiel ŝparante multan elŝutan tempon kaj diskospacon. Tamen, ĉi tio estas sufiĉe unika situacio. En plej multaj kazoj ne ekzistas ampleksaj datumbazoj de kiuj dosieroj jam estas ĝuste konservitaj de aliaj piratoj. Ĉi tio mem estas granda ŝanco por iu tie ekstere. Estus bonege havi regule ĝisdatigitan superrigardon de aferoj kiel muziko kaj filmoj kiuj jam estas vaste semitaj en torentaj retejoj, kaj tial estas malpli prioritataj por inkludi en pirataj speguloj."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Datumrubado"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nun vi estas preta por efektive elŝuti la datumojn amase. Kiel menciite antaŭe, en ĉi tiu punkto vi jam devus mane elŝuti aron da dosieroj, por pli bone kompreni la konduton kaj limigojn de la celo. Tamen, ankoraŭ estos surprizoj por vi kiam vi efektive komencos elŝuti multajn dosierojn samtempe."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nia konsilo ĉi tie estas ĉefe teni ĝin simpla. Komencu per simple elŝuti aron da dosieroj. Vi povas uzi Python, kaj poste plivastigi al pluraj fadenoj. Sed foje eĉ pli simpla estas generi Bash-dosierojn rekte el la datumbazo, kaj poste ruli plurajn el ili en pluraj terminalaj fenestroj por skali supren. Rapida teknika truko menciinda ĉi tie estas uzi OUTFILE en MySQL, kiun vi povas skribi ie ajn se vi malŝaltas \"secure_file_priv\" en mysqld.cnf (kaj estu certa ankaŭ malŝalti/superregi AppArmor se vi estas en Linukso)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Ni stokas la datumojn sur simplaj malmolaj diskoj. Komencu kun kio ajn vi havas, kaj plivastigu malrapide. Povas esti superforta pensi pri stokado de centoj da TB-oj da datumoj. Se tio estas la situacio, kiun vi alfrontas, simple elmetu bonan subaron unue, kaj en via anonco petu helpon en stokado de la resto. Se vi volas akiri pli da malmolaj diskoj mem, tiam r/DataHoarder havas kelkajn bonajn rimedojn pri akirado de bonaj ofertoj."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Provu ne tro zorgi pri elegantaj dosiersistemoj. Estas facile fali en la kuniklotruon de starigado de aferoj kiel ZFS. Unu teknika detalo por esti konscia pri tamen, estas ke multaj dosiersistemoj ne bone traktas multajn dosierojn. Ni trovis, ke simpla solvo estas krei plurajn dosierujojn, ekzemple por malsamaj ID-gamoj aŭ hash-prefiksoj."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Post elŝutado de la datumoj, estu certa kontroli la integrecon de la dosieroj uzante hash-ojn en la metadatenoj, se disponebla."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuado"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Vi havas la datumojn, tiel donante al vi posedon de la unua pirata spegulo de la mondo de via celo (plej verŝajne). En multaj manieroj la plej malfacila parto estas finita, sed la plej riska parto ankoraŭ estas antaŭ vi. Finfine, ĝis nun vi estis kaŝema; flugante sub la radaro. Ĉio, kion vi devis fari, estis uzi bonan VPN dum la tuta tempo, ne plenigi viajn personajn detalojn en iuj formoj (kompreneble), kaj eble uzi specialan retumilan sesion (aŭ eĉ malsaman komputilon)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nun vi devas distribui la datumojn. En nia kazo ni unue volis kontribui la librojn reen al Library Genesis, sed tiam rapide malkovris la malfacilaĵojn en tio (fikcio kontraŭ nefikcio ordigo). Do ni decidis pri distribuo uzante Library Genesis-stilajn torentojn. Se vi havas la ŝancon kontribui al ekzistanta projekto, tiam tio povus ŝpari al vi multan tempon. Tamen, ne estas multaj bone organizitaj pirataj speguloj tie ekstere nuntempe."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Do ni diru, ke vi decidas distribui torentojn mem. Provu teni tiujn dosierojn malgrandaj, por ke ili estu facile spegulitaj en aliaj retejoj. Vi tiam devos semi la torentojn mem, dum vi ankoraŭ restas anonima. Vi povas uzi VPN (kun aŭ sen havenan plusendadon), aŭ pagi per miksitaj Bitmoneroj por Seedbox. Se vi ne scias, kion signifas iuj el tiuj terminoj, vi havos multon por legi, ĉar estas grave, ke vi komprenu la riskajn kompromisojn ĉi tie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Vi povas gastigi la torentajn dosierojn mem en ekzistantaj torentaj retejoj. En nia kazo, ni elektis efektive gastigi retejon, ĉar ni ankaŭ volis disvastigi nian filozofion klare. Vi povas fari tion mem en simila maniero (ni uzas Njalla por niaj domajnoj kaj gastigado, pagita per miksitaj Bitmoneroj), sed ankaŭ bonvolu kontakti nin por ke ni gastigu viajn torentojn. Ni celas konstrui ampleksan indekson de pirataj speguloj kun la tempo, se ĉi tiu ideo kaptas."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Pri VPN-elekto, multe jam estis skribita pri tio, do ni nur ripetos la ĝeneralan konsilon elekti laŭ reputacio. Efektive testitaj en kortumo sen-logaj politikoj kun longaj historioj de protektado de privateco estas la plej malalta risko-opcio, laŭ nia opinio. Notu, ke eĉ kiam vi faras ĉion ĝuste, vi neniam povas atingi nul-risikon. Ekzemple, kiam vi semas viajn torentojn, tre motivita ŝtata aktoro verŝajne povas rigardi enirantajn kaj elirantajn datumfluojn por VPN-serviloj, kaj dedukti kiu vi estas. Aŭ vi povas simple erari iel. Ni verŝajne jam faris, kaj faros denove. Feliĉe, ŝtatoj ne zorgas <em>tiom</em> multe pri piratado."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Unu decido por fari por ĉiu projekto estas ĉu publikigi ĝin uzante la saman identecon kiel antaŭe, aŭ ne. Se vi daŭre uzas la saman nomon, tiam eraroj en operacia sekureco de pli fruaj projektoj povus reveni por mordi vin. Sed publikigi sub malsamaj nomoj signifas, ke vi ne konstruas pli longdaŭran reputacion. Ni elektis havi fortan operacian sekurecon de la komenco por ke ni povu daŭre uzi la saman identecon, sed ni ne hezitos publikigi sub malsama nomo se ni eraras aŭ se la cirkonstancoj postulas tion."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Disvastigi la vorton povas esti malfacile. Kiel ni diris, ĉi tio ankoraŭ estas niĉa komunumo. Ni origine afiŝis en Reddit, sed vere akiris atenton en Hacker News. Por nun nia rekomendo estas afiŝi ĝin en kelkaj lokoj kaj vidi kio okazas. Kaj denove, kontaktu nin. Ni ŝatus disvastigi la vorton pri pli da pirata arkivismo."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Konkludo"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Espereble tio ĉi estas helpema por novaj pirataj arkivistoj. Ni ĝojas bonvenigi vin al ĉi tiu mondo, do ne hezitu kontakti nin. Ni konservu kiel eble plej multe de la monda scio kaj kulturo, kaj spegulu ĝin vaste kaj malproksime."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Enkonduko al la Spegulo de la Pirata Biblioteko: Konservante 7TB da libroj (kiuj ne estas en Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Ĉi tiu projekto (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>) celas kontribui al la konservado kaj liberigo de homa scio. Ni faras nian malgrandan kaj humilan kontribuon, sekvante la paŝojn de la granduloj antaŭ ni."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "La fokuso de ĉi tiu projekto estas ilustrita per ĝia nomo:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirato</strong> - Ni intence malobservas la kopirajtleĝon en plej multaj landoj. Tio permesas al ni fari ion, kion laŭleĝaj entoj ne povas fari: certigi, ke libroj estas spegulitaj vaste kaj malproksime."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteko</strong> - Kiel plej multaj bibliotekoj, ni ĉefe fokusiĝas al skribitaj materialoj kiel libroj. Ni eble plivastigos al aliaj specoj de amaskomunikiloj en la estonteco."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spegulo</strong> - Ni estas strikte spegulo de ekzistantaj bibliotekoj. Ni fokusiĝas al konservado, ne al faciligado de serĉado kaj elŝutado de libroj (aliro) aŭ al kultivado de granda komunumo de homoj, kiuj kontribuas novajn librojn (fontado)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La unua biblioteko, kiun ni spegulis, estas Z-Biblioteko. Ĉi tio estas populara (kaj kontraŭleĝa) biblioteko. Ili prenis la kolekton de Library Genesis kaj faris ĝin facile serĉebla. Krome, ili fariĝis tre efikaj en petado de novaj libro-kontribuoj, instigante kontribuantajn uzantojn per diversaj avantaĝoj. Ili nuntempe ne kontribuas ĉi tiujn novajn librojn reen al Library Genesis. Kaj male al Library Genesis, ili ne faciligas speguladon de sia kolekto, kio malhelpas vastan konservadon. Ĉi tio estas grava por ilia komerca modelo, ĉar ili ŝargas monon por aliro al sia kolekto en amaso (pli ol 10 libroj tage)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Ni ne faras moralajn juĝojn pri postulado de mono por amasa aliro al kontraŭleĝa kolekto de libroj. Estas sendube, ke la Z-Biblioteko sukcesis plivastigi aliron al scio kaj akiri pli da libroj. Ni estas simple ĉi tie por fari nian parton: certigi la longdaŭran konservadon de ĉi tiu privata kolekto."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Ni ŝatus inviti vin helpi konservi kaj liberigi homan scion elŝutante kaj semante niajn torentojn. Vidu la projekton paĝo por pli da informoj pri kiel la datumoj estas organizitaj."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Ni ankaŭ tre ŝatus inviti vin kontribui viajn ideojn pri kiuj kolektoj speguli sekve, kaj kiel fari tion. Kune ni povas atingi multon. Ĉi tio estas nur malgranda kontribuo inter senfinaj aliaj. Dankon, pro ĉio, kion vi faras."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Ni ne ligas al la dosieroj de ĉi tiu blogo. Bonvolu trovi ĝin mem.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, aŭ Kiom da Libroj Estas Konservitaj Por Ĉiam?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se ni ĝuste dedupliki la dosierojn de ombraj bibliotekoj, kiun procenton de ĉiuj libroj en la mondo ni konservis?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Kun la Spegulo de la Pirata Biblioteko (RED: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>), nia celo estas preni ĉiujn librojn en la mondo, kaj konservi ilin por ĉiam.<sup>1</sup> Inter niaj Z-Biblioteko torentoj, kaj la originalaj Library Genesis torentoj, ni havas 11,783,153 dosierojn. Sed kiom estas tio, vere? Se ni ĝuste dedupliki tiujn dosierojn, kiun procenton de ĉiuj libroj en la mondo ni konservis? Ni vere ŝatus havi ion tian:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of la skriba heredaĵo de la homaro konservita por ĉiam"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Por procento, ni bezonas denominatoron: la totala nombro de libroj iam ajn publikigitaj.<sup>2</sup> Antaŭ la malapero de Google Books, inĝeniero en la projekto, Leonid Taycher, <a %(booksearch_blogspot)s>provis taksi</a> ĉi tiun nombron. Li elpensis — ŝerce — 129,864,880 (“almenaŭ ĝis dimanĉo”). Li taksis ĉi tiun nombron per konstruado de unuigita datumbazo de ĉiuj libroj en la mondo. Por tio, li kunigis malsamajn Datasets kaj poste kunfandis ilin diversmaniere."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Kiel rapida flanka noto, estas alia persono kiu provis katalogi ĉiujn librojn en la mondo: Aaron Swartz, la forpasinta cifereca aktivulo kaj kunfondinto de Reddit.<sup>3</sup> Li <a %(youtube)s>komencis Open Library</a> kun la celo de “unu retpaĝo por ĉiu libro iam ajn publikigita”, kombinante datumojn el multaj diversaj fontoj. Li finfine pagis la plej altan prezon por sia laboro pri cifereca konservado kiam li estis procesigita pro amasa elŝutado de akademiaj artikoloj, kio kondukis al lia memmortigo. Nepre dirite, ĉi tio estas unu el la kialoj kial nia grupo estas pseŭdonima, kaj kial ni estas tre zorgemaj. Open Library ankoraŭ heroike estas administrata de homoj ĉe la Internet Archive, daŭrigante la heredaĵon de Aaron. Ni revenos al ĉi tio poste en ĉi tiu afiŝo."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "En la bloga afiŝo de Google, Taycher priskribas kelkajn el la defioj kun taksado de ĉi tiu nombro. Unue, kio konsistigas libron? Estas kelkaj eblaj difinoj:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fizikaj kopioj.</strong> Kompreneble ĉi tio ne estas tre helpema, ĉar ili estas nur duplikatoj de la sama materialo. Estus mojose se ni povus konservi ĉiujn notojn, kiujn homoj faras en libroj, kiel la famaj “griboj en la marĝenoj” de Fermat. Sed bedaŭrinde, tio restos revo de arkivisto."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Verkoj”.</strong> Ekzemple “Harry Potter kaj la Ĉambro de Sekretoj” kiel logika koncepto, ampleksante ĉiujn versiojn de ĝi, kiel malsamajn tradukojn kaj represojn. Ĉi tio estas speco de utila difino, sed povas esti malfacile desegni la linion de kio kalkulas. Ekzemple, ni probable volas konservi malsamajn tradukojn, kvankam represoj kun nur malgrandaj diferencoj eble ne estas tiel gravaj."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Eldonaĵoj”.</strong> Ĉi tie vi kalkulas ĉiun unikan version de libro. Se io pri ĝi estas malsama, kiel malsama kovrilo aŭ malsama antaŭparolo, ĝi kalkulas kiel malsama eldonaĵo."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Dosieroj.</strong> Kiam oni laboras kun ombro-bibliotekoj kiel Library Genesis, Sci-Hub, aŭ Z-Library, estas aldona konsidero. Povas esti pluraj skanaĵoj de la sama eldonaĵo. Kaj homoj povas fari pli bonajn versiojn de ekzistantaj dosieroj, skanante la tekston uzante OCR, aŭ korektante paĝojn kiuj estis skanitaj laŭ angulo. Ni volas kalkuli ĉi tiujn dosierojn kiel unu eldonaĵon, kio postulus bonan metadata, aŭ deduplikadon uzante dokumentajn similecajn mezurojn."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Eldonaĵoj” ŝajnas la plej praktika difino de kio estas “libroj”. Oportune, ĉi tiu difino ankaŭ estas uzata por asigni unikajn ISBN-nombrojn. ISBN, aŭ Internacia Normo-Libro-Nombro, estas ofte uzata por internacia komerco, ĉar ĝi estas integrita kun la internacia strekokoda sistemo (”Internacia Artikola Nombro”). Se vi volas vendi libron en vendejoj, ĝi bezonas strekokodon, do vi ricevas ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "La bloga afiŝo de Taycher mencias ke dum ISBN-oj estas utilaj, ili ne estas universalaj, ĉar ili estis vere adoptitaj nur en la mez-sepdekaj, kaj ne ĉie en la mondo. Tamen, ISBN estas probable la plej vaste uzata identigilo de libroeldonaĵoj, do ĝi estas nia plej bona startpunkto. Se ni povas trovi ĉiujn ISBN-ojn en la mondo, ni ricevas utilan liston de kiuj libroj ankoraŭ bezonas esti konservitaj."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Do, kie ni ricevas la datumojn? Estas kelkaj ekzistantaj klopodoj kiuj provas kompili liston de ĉiuj libroj en la mondo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Finfine, ili faris ĉi tiun esploron por Google Books. Tamen, ilia metadata ne estas alirebla en amaso kaj sufiĉe malfacile skrapebla."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Kiel menciite antaŭe, ĉi tio estas ilia tuta misio. Ili akiris grandegajn kvantojn da bibliotekaj datumoj de kunlaborantaj bibliotekoj kaj naciaj arkivoj, kaj daŭre faras tion. Ili ankaŭ havas volontulajn bibliotekistojn kaj teknikan teamon kiu provas deduplikigi rekordojn, kaj etikedi ilin per ĉiuspecaj metadata. Plej bone, ilia datuma aro estas tute malferma. Vi povas simple <a %(openlibrary)s>elŝuti ĝin</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Ĉi tio estas retejo administrata de la neprofitcela OCLC, kiu vendas bibliotekajn mastrumajn sistemojn. Ili agregas libro-metadata el multaj bibliotekoj, kaj faras ĝin disponebla tra la retejo WorldCat. Tamen, ili ankaŭ enspezas vendante ĉi tiujn datumojn, do ĝi ne estas disponebla por amasa elŝuto. Ili havas iujn pli limigitajn amasajn datumarojn disponeblajn por elŝuto, en kunlaboro kun specifaj bibliotekoj."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Ĉi tio estas la temo de ĉi tiu bloga afiŝo. ISBNdb skrapas diversajn retejojn por libro-metadata, precipe prezo-datumojn, kiujn ili poste vendas al librovendistoj, por ke ili povu prezi siajn librojn laŭ la resto de la merkato. Ĉar ISBN-oj estas sufiĉe universalaj nuntempe, ili efike konstruis “retpaĝon por ĉiu libro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Diversaj individuaj bibliotekaj sistemoj kaj arkivoj.</strong> Estas bibliotekoj kaj arkivoj kiuj ne estis indeksitaj kaj agregitaj de iu el la supraj, ofte ĉar ili estas subfinancitaj, aŭ pro aliaj kialoj ne volas dividi siajn datumojn kun Open Library, OCLC, Google, kaj tiel plu. Multaj el ĉi tiuj havas ciferecajn rekordojn alireblajn tra la interreto, kaj ili ofte ne estas tre bone protektitaj, do se vi volas helpi kaj amuziĝi lernante pri strangaj bibliotekaj sistemoj, ĉi tiuj estas bonegaj startpunktoj."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "En ĉi tiu afiŝo, ni ĝojas anonci malgrandan eldonon (kompare al niaj antaŭaj Z-Library eldonoj). Ni skrapis plejparton de ISBNdb, kaj faris la datumojn disponeblaj por torentado en la retejo de la Pirate Library Mirror (EDIT: movita al <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>; ni ne ligos ĝin ĉi tie rekte, nur serĉu ĝin). Ĉi tiuj estas ĉirkaŭ 30.9 milionoj da rekordoj (20GB kiel <a %(jsonlines)s>JSON Linioj</a>; 4.4GB kunpremita). Sur ilia retejo ili asertas ke ili fakte havas 32.6 milionojn da rekordoj, do ni eble iel mankis iujn, aŭ <em>ili</em> povus fari ion malĝuste. Ĉiuokaze, por nun ni ne dividos precize kiel ni faris ĝin — ni lasos tion kiel ekzercon por la leganto. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Kion ni dividos estas iuj preliminaj analizoj, por provi alproksimiĝi al taksado de la nombro de libroj en la mondo. Ni rigardis tri datumarojn: ĉi tiu nova ISBNdb datuma aro, nia originala eldono de metadata kiun ni skrapis de la ombro-biblioteko Z-Library (kiu inkluzivas Library Genesis), kaj la datuma elŝuto de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Ni komencu kun iuj krudaj nombroj:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "En ambaŭ Z-Library/Libgen kaj Open Library estas multe pli da libroj ol unikaj ISBN-oj. Ĉu tio signifas ke multaj el tiuj libroj ne havas ISBN-ojn, aŭ ĉu la ISBN-metadata simple mankas? Ni probable povas respondi ĉi tiun demandon per kombinaĵo de aŭtomata kongruado bazita sur aliaj atributoj (titolo, aŭtoro, eldonisto, ktp), alportante pli da datumfontoj, kaj eltirante ISBN-ojn el la faktaj libro-skanadoj mem (en la kazo de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Kiom da tiuj ISBN-oj estas unikaj? Ĉi tio estas plej bone ilustrita per Venn-diagramo:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Por esti pli preciza:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Ni estis surprizitaj pri kiom malmulte da interkovro ekzistas! ISBNdb havas grandegan kvanton da ISBN-oj kiuj ne aperas en Z-Library aŭ Open Library, kaj la sama validas (en pli malgranda sed ankoraŭ konsiderinda grado) por la aliaj du. Tio levas multajn novajn demandojn. Kiom multe helpus aŭtomata kongruado en etikedado de la libroj kiuj ne estis etikedataj per ISBN-oj? Ĉu estus multaj kongruoj kaj sekve pliigita interkovro? Ankaŭ, kio okazus se ni aldonus 4-an aŭ 5-an datenserion? Kiom da interkovro ni tiam vidus?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Ĉi tio donas al ni deirpunkton. Ni nun povas rigardi ĉiujn ISBN-ojn kiuj ne estis en la datenserio de Z-Library, kaj kiuj ankaŭ ne kongruas kun la kampoj de titolo/aŭtoro. Tio povas doni al ni manieron konservi ĉiujn librojn en la mondo: unue per skrapado de la interreto por skanoj, poste per eliro en la realan vivon por skani librojn. La lasta eĉ povus esti homamas-financita, aŭ movita de \"rekompencoj\" de homoj kiuj ŝatus vidi specifajn librojn ciferecigitaj. Ĉio tio estas rakonto por alia tempo."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se vi volas helpi kun iu ajn el ĉi tio — plia analizo; skrapado de pli da metadata; trovado de pli da libroj; OCR-ado de libroj; fari tion por aliaj domajnoj (ekz. artikoloj, aŭdlibroj, filmoj, televidaj programoj, revuoj) aŭ eĉ fari iujn el ĉi tiuj datumoj disponeblaj por aferoj kiel ML / granda lingvomodela trejnado — bonvolu kontakti min (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se vi estas specife interesita pri la datumanalizo, ni laboras por fari niajn datenseriojn kaj skriptojn disponeblaj en pli facile uzebla formato. Estus bonege se vi povus simple forkigi notlibron kaj komenci ludi kun tio."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Fine, se vi volas subteni ĉi tiun laboron, bonvolu konsideri fari donacon. Ĉi tio estas tute volontula operacio, kaj via kontribuo faras grandegan diferencon. Ĉiu peco helpas. Por nun ni akceptas donacojn en kripto; vidu la paĝon Donaci en la Arkivo de Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Por iu racia difino de \"por ĉiam\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Kompreneble, la skriba heredaĵo de la homaro estas multe pli ol libroj, precipe nuntempe. Por la celo de ĉi tiu afiŝo kaj niaj lastatempaj eldonoj ni fokusiĝas al libroj, sed niaj interesoj etendiĝas pli malproksimen."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Estas multe pli kion oni povas diri pri Aaron Swartz, sed ni nur volis mencii lin mallonge, ĉar li ludas pivotan rolon en ĉi tiu rakonto. Dum la tempo pasas, pli da homoj eble renkontos lian nomon por la unua fojo, kaj poste povos mem plonĝi en la kuniklotruon."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La kritika fenestro de ombro-bibliotekoj"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Kiel ni povas aserti konservi niajn kolektojn por ĉiam, kiam ili jam proksimiĝas al 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Ĉina versio 中文版</a>, diskutu en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "En la Arkivo de Anna, oni ofte demandas nin kiel ni povas aserti konservi niajn kolektojn por ĉiam, kiam la totala grandeco jam proksimiĝas al 1 Petabajto (1000 TB), kaj ankoraŭ kreskas. En ĉi tiu artikolo ni rigardos nian filozofion, kaj vidos kial la sekva jardeko estas kritika por nia misio konservi la scion kaj kulturon de la homaro."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "La <a %(annas_archive_stats)s>totala grandeco</a> de niaj kolektoj, dum la lastaj monatoj, dividita laŭ nombro de torentaj semantoj."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritatoj"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Kial ni tiom zorgas pri artikoloj kaj libroj? Ni flankenmetu nian fundamentan kredon pri konservado ĝenerale — ni eble skribos alian afiŝon pri tio. Do kial artikoloj kaj libroj specife? La respondo estas simpla: <strong>informdenso</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabajto de stokado, skribita teksto stokas la plej multe da informo el ĉiuj amaskomunikiloj. Dum ni zorgas pri ambaŭ scio kaj kulturo, ni pli zorgas pri la unua. Entute, ni trovas hierarkion de informdenso kaj graveco de konservado kiu aspektas proksimume tiel:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademiaj artikoloj, ĵurnaloj, raportoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organikaj datumoj kiel DNA-sekvencoj, plantosemoj, aŭ mikrobaĵoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Nefikciaj libroj"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Scienca kaj inĝeniera programaro"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Mezurdatenoj kiel sciencaj mezuroj, ekonomiaj datumoj, korporaciaj raportoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sciencaj kaj inĝenieraj retejoj, interretaj diskutoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Nefikciaj magazinoj, ĵurnaloj, manlibroj"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Nefikciaj transskribaĵoj de paroladoj, dokumentarioj, podkastoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Internaj datumoj de korporacioj aŭ registaroj (likoj)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadatenaj rekordoj ĝenerale (de nefikcio kaj fikcio; de aliaj medioj, arto, homoj, ktp; inkluzive recenzojn)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografiaj datumoj (ekz. mapoj, geologiaj enketoj)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transskribaĵoj de juraj aŭ kortumaj procedoj"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fikciaj aŭ amuzaj versioj de ĉio supre"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La rangigo en ĉi tiu listo estas iom arbitra — pluraj eroj estas egalaj aŭ havas malkonsentojn ene de nia teamo — kaj ni probable forgesas iujn gravajn kategoriojn. Sed ĉi tio estas proksimume kiel ni prioritatas."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Iuj el ĉi tiuj eroj estas tro malsamaj de la aliaj por ke ni zorgu pri ili (aŭ jam estas prizorgataj de aliaj institucioj), kiel organikaj datumoj aŭ geografiaj datumoj. Sed plej multaj el la eroj en ĉi tiu listo estas efektive gravaj por ni."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Alia granda faktoro en nia prioritato estas kiom multe riskas certa verko. Ni preferas fokusiĝi al verkoj kiuj estas:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Raraj"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unike malfokusitaj"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unike riskas detruon (ekz. pro milito, financaj tranĉoj, procesoj, aŭ politika persekutado)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Fine, ni zorgas pri skalo. Ni havas limigitan tempon kaj monon, do ni preferus pasigi monaton savante 10,000 librojn ol 1,000 librojn — se ili estas proksimume egale valoraj kaj riskaj."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Ombraj bibliotekoj"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Estas multaj organizoj kiuj havas similajn misiojn, kaj similajn prioritatojn. Efektive, estas bibliotekoj, arkivoj, laboratorioj, muzeoj, kaj aliaj institucioj taskitaj pri konservado de ĉi tiu speco. Multaj el tiuj estas bone financitaj, de registaroj, individuoj, aŭ korporacioj. Sed ili havas unu grandegan blindan punkton: la jura sistemo."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Jen kuŝas la unika rolo de ombraj bibliotekoj, kaj la kialo ke la Arkivo de Anna ekzistas. Ni povas fari aferojn kiujn aliaj institucioj ne rajtas fari. Nun, ne estas (ofte) ke ni povas arkivi materialojn kiuj estas kontraŭleĝaj por konservi aliloke. Ne, estas laŭleĝe en multaj lokoj konstrui arkivon kun ajnaj libroj, artikoloj, magazinoj, kaj tiel plu."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Sed kion ofte mankas al juraj arkivoj estas <strong>redundeco kaj longviveco</strong>. Ekzistas libroj, el kiuj nur unu ekzemplero ekzistas en iu fizika biblioteko ie. Ekzistas metadatenaj rekordoj gardataj de unuopa korporacio. Ekzistas gazetoj konservitaj nur sur mikrofilmo en unuopa arkivo. Bibliotekoj povas ricevi buĝetajn tranĉojn, korporacioj povas bankroti, arkivoj povas esti bombitaj kaj bruligitaj ĝis la grundo. Ĉi tio ne estas hipoteza — ĉi tio okazas ĉiam."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "La afero, kiun ni unike povas fari ĉe Arkivo de Anna, estas stoki multajn kopiojn de verkoj, grandskale. Ni povas kolekti artikolojn, librojn, revuojn, kaj pli, kaj distribui ilin amase. Ni nuntempe faras tion per torentoj, sed la precizaj teknologioj ne gravas kaj ŝanĝiĝos kun la tempo. La grava parto estas akiri multajn kopiojn distribuitajn tra la mondo. Ĉi tiu citaĵo de antaŭ pli ol 200 jaroj ankoraŭ sonas vera:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>La perdita ne povas esti reakirita; sed ni savu tion, kio restas: ne per volboj kaj seruroj, kiuj baras ilin de la publika okulo kaj uzo, en transdonado de ili al la rubo de tempo, sed per tia multobligo de kopioj, kiu metos ilin preter la atingo de akcidento.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Rapida noto pri publika domeno. Ĉar Arkivo de Anna unike fokusiĝas al agadoj, kiuj estas kontraŭleĝaj en multaj lokoj ĉirkaŭ la mondo, ni ne ĝenas pri vaste disponeblaj kolektoj, kiel publika domeno libroj. Juraj entoj ofte jam bone zorgas pri tio. Tamen, estas konsideroj, kiuj foje igas nin labori pri publike disponeblaj kolektoj:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadatenaj rekordoj povas esti libere rigardataj en la retejo Worldcat, sed ne elŝutitaj amase (ĝis ni <a %(worldcat_scrape)s>skrapis</a> ilin)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kodo povas esti malfermfonta en Github, sed Github kiel tuto ne povas esti facile spegulita kaj tiel konservita (kvankam en ĉi tiu aparta kazo estas sufiĉe distribuitaj kopioj de plej multaj kodaj deponejoj)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit estas senpaga por uzi, sed lastatempe starigis striktajn kontraŭ-skrapajn mezurojn, sekve de datum-avidaj LLM-trejnadoj (pli pri tio poste)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Multobligo de kopioj"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Reen al nia originala demando: kiel ni povas aserti konservi niajn kolektojn por ĉiam? La ĉefa problemo ĉi tie estas, ke nia kolekto <a %(torrents_stats)s>kreskas</a> rapide, per skrapado kaj malfermfontado de iuj grandegaj kolektoj (krom la mirinda laboro jam farita de aliaj malfermdatumaj ombrobibliotekoj kiel Sci-Hub kaj Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Ĉi tiu kresko en datumoj malfaciligas speguli la kolektojn ĉirkaŭ la mondo. Datumstokado estas multekosta! Sed ni estas optimismaj, precipe kiam ni observas la jenajn tri tendencojn."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Ni plukis la malaltpendantajn fruktojn"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Ĉi tiu sekvas rekte el niaj prioritatoj diskutataj supre. Ni preferas labori pri liberigado de grandaj kolektoj unue. Nun kiam ni certigis iujn el la plej grandaj kolektoj en la mondo, ni atendas, ke nia kresko estos multe pli malrapida."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Estas ankoraŭ longa vosto de pli malgrandaj kolektoj, kaj novaj libroj estas skanitaj aŭ publikigitaj ĉiutage, sed la rapideco verŝajne estos multe pli malrapida. Ni eble ankoraŭ duobliĝos aŭ eĉ triobliĝos en grandeco, sed dum pli longa tempoperiodo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Stokaj kostoj daŭre falas eksponente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "En la momento de verkado, <a %(diskprices)s>diskoprezoj</a> po TB estas ĉirkaŭ $12 por novaj diskoj, $8 por uzitaj diskoj, kaj $4 por bendo. Se ni estas konservativaj kaj rigardas nur novajn diskojn, tio signifas, ke stoki petabajton kostas ĉirkaŭ $12,000. Se ni supozas, ke nia biblioteko triobliĝos de 900TB al 2.7PB, tio signifus $32,400 por speguli nian tutan bibliotekon. Aldonante elektran, koston de alia aparataro, kaj tiel plu, ni rondigu ĝin al $40,000. Aŭ kun bendo pli kiel $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Unuflanke <strong>$15,000–$40,000 por la sumo de ĉiu homa scio estas rabataĉo</strong>. Aliflanke, estas iom krute atendi tunojn da plenaj kopioj, precipe se ni ankaŭ ŝatus, ke tiuj homoj daŭre semu siajn torentojn por la profito de aliaj."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Tio estas hodiaŭ. Sed progreso marŝas antaŭen:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Malharddiskaj kostoj po TB estis proksimume trionigitaj dum la lastaj 10 jaroj, kaj verŝajne daŭre falos je simila rapideco. Bendo ŝajnas esti sur simila trajektorio. SSD-prezoj falas eĉ pli rapide, kaj eble superos HDD-prezojn antaŭ la fino de la jardeko."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-prezaj tendencoj el diversaj fontoj (klaku por vidi studon)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se ĉi tio tenas, tiam post 10 jaroj ni eble rigardos nur $5,000–$13,000 por speguli nian tutan kolekton (1/3), aŭ eĉ malpli se ni kreskos malpli en grandeco. Dum ankoraŭ multe da mono, ĉi tio estos atingebla por multaj homoj. Kaj ĝi eble estos eĉ pli bona pro la sekva punkto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Plibonigoj en informdenso"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Ni nuntempe konservas librojn en la krudaj formatoj, kiujn oni donas al ni. Certe, ili estas kunpremitaj, sed ofte ili ankoraŭ estas grandaj skanaĵoj aŭ fotoj de paĝoj."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Ĝis nun, la solaj opcioj por malpliigi la totalan grandecon de nia kolekto estis per pli agresema kunpremo aŭ deduplikado. Tamen, por akiri sufiĉe signifajn ŝparojn, ambaŭ estas tro perdfontaj por nia gusto. Peza kunpremo de fotoj povas fari tekston apenaŭ legebla. Kaj deduplikado postulas altan fidon pri libroj estantaj ekzakte samaj, kio ofte estas tro nepreciza, precipe se la enhavo estas sama sed la skanaĵoj estas faritaj en malsamaj okazoj."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Ĉiam estis tria opcio, sed ĝia kvalito estis tiel abomena, ke ni neniam konsideris ĝin: <strong>OCR, aŭ Optika Karaktera Rekono</strong>. Ĉi tio estas la procezo de konverti fotojn en simpla teksto, uzante AI por detekti la karakterojn en la fotoj. Iloj por tio ekzistas de longe, kaj estis sufiĉe bonaj, sed \"sufiĉe bonaj\" ne sufiĉas por konservadaj celoj."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tamen, lastatempaj multmodalaj profundaj lernmodeloj faris ekstreme rapidan progreson, kvankam ankoraŭ je altaj kostoj. Ni atendas, ke kaj precizeco kaj kostoj draste pliboniĝos en la venontaj jaroj, ĝis la punkto, kie estos realisme apliki al nia tuta biblioteko."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Plibonigoj de OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Kiam tio okazos, ni verŝajne ankoraŭ konservos la originalajn dosierojn, sed krome ni povus havi multe pli malgrandan version de nia biblioteko, kiun plej multaj homoj volos speguli. La surprizo estas, ke kruda teksto mem kunpremiĝas eĉ pli bone, kaj estas multe pli facile dedupliki, donante al ni eĉ pli da ŝparoj."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Entute ne estas nerealisme atendi almenaŭ 5-10-oblan redukton en la totala dosiergrandeco, eble eĉ pli. Eĉ kun konservativa 5-obla redukto, ni rigardus <strong>$1,000–$3,000 en 10 jaroj eĉ se nia biblioteko triobliĝus en grandeco</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritika fenestro"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se ĉi tiuj prognozoj estas ĝustaj, ni <strong>nur bezonas atendi kelkajn jarojn</strong> antaŭ ol nia tuta kolekto estos vaste spegulita. Tiel, laŭ la vortoj de Thomas Jefferson, \"metita preter la atingo de akcidento.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Bedaŭrinde, la apero de LLM-oj, kaj ilia datavora trejnado, metis multajn kopirajtulojn en defensivan pozicion. Eĉ pli ol ili jam estis. Multaj retejoj malfaciligas skrapadon kaj arkivadon, procesoj flugas ĉirkaŭe, kaj dumtempe fizikaj bibliotekoj kaj arkivoj daŭre estas neglektitaj."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Ni povas nur atendi, ke ĉi tiuj tendencoj daŭre plimalboniĝos, kaj multaj verkoj perdiĝos longe antaŭ ol ili eniros la publikan domenon."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Ni estas ĉe la sojlo de revolucio en konservado, sed <q>la perditaj ne povas esti reakiritaj.</q></strong> Ni havas kritikan fenestron de ĉirkaŭ 5-10 jaroj dum kiu ankoraŭ estas sufiĉe multekoste funkciigi ombran bibliotekon kaj krei multajn spegulojn ĉirkaŭ la mondo, kaj dum kiu aliro ankoraŭ ne estas tute fermita."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se ni povas transiri ĉi tiun fenestron, tiam ni efektive konservos la scion kaj kulturon de la homaro por ĉiam. Ni ne devus lasi ĉi tiun tempon malŝpari. Ni ne devus lasi ĉi tiun kritikan fenestron fermiĝi al ni."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Ni iru."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ekskluziva aliro por LLM-kompanioj al la plej granda kolekto de ĉinaj nefikciaj libroj en la mondo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Ĉina versio 中文版</a>, <a %(news_ycombinator)s>Diskuti en Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> La Arkivo de Anna akiris unikan kolekton de 7.5 milionoj / 350TB da ĉinaj nefikciaj libroj — pli granda ol Library Genesis. Ni pretas doni al LLM-kompanio ekskluzivan aliron, kontraŭ altkvalita OCR kaj teksto-ekstraktado.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Ĉi tio estas mallonga bloga afiŝo. Ni serĉas iun kompanion aŭ institucion por helpi nin kun OCR kaj teksto-ekstraktado por amasa kolekto, kiun ni akiris, kontraŭ ekskluziva frua aliro. Post la embargo-periodo, ni kompreneble liberigos la tutan kolekton."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Altgradkvalita akademia teksto estas tre utila por trejnado de LLM-oj. Kvankam nia kolekto estas ĉina, ĉi tio devus esti eĉ utila por trejnado de anglaj LLM-oj: modeloj ŝajnas enkodigi konceptojn kaj scion sendepende de la fonta lingvo."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Por tio, teksto devas esti eltirita el la skanaĵoj. Kion ricevas la Arkivo de Anna el tio? Plenteksta serĉo de la libroj por ĝiaj uzantoj."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Ĉar niaj celoj kongruas kun tiuj de LLM-evoluigantoj, ni serĉas kunlaboranton. Ni pretas doni al vi <strong>ekskluzivan fruan aliron al ĉi tiu kolekto en amaso dum 1 jaro</strong>, se vi povas fari ĝustan OCR kaj teksto-ekstraktadon. Se vi pretas dividi la tutan kodon de via procezo kun ni, ni pretus embargigi la kolekton por pli longa tempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Ekzemplaj paĝoj"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Por pruvi al ni, ke vi havas bonan procezon, jen kelkaj ekzemplaj paĝoj por komenci, el libro pri superkonduktiloj. Via procezo devus ĝuste trakti matematikon, tabelojn, diagramojn, piednotojn, kaj tiel plu."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Sendu viajn prilaboritajn paĝojn al nia retpoŝto. Se ili aspektas bone, ni sendos al vi pli private, kaj ni atendas, ke vi povos rapide funkciigi vian procezon sur tiuj ankaŭ. Kiam ni estos kontentaj, ni povas fari interkonsenton."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Kolekto"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Iom pli da informoj pri la kolekto. <a %(duxiu)s>Duxiu</a> estas granda datumbazo de skanitaj libroj, kreita de la <a %(chaoxing)s>SuperStar Digital Library Group</a>. Plejparte temas pri akademiaj libroj, skanitaj por igi ilin disponeblaj ciferece al universitatoj kaj bibliotekoj. Por nia anglalingva publiko, <a %(library_princeton)s>Princeton</a> kaj la <a %(guides_lib_uw)s>Universitato de Vaŝingtono</a> havas bonajn superrigardojn. Estas ankaŭ bonega artikolo donanta pli da fono: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (serĉu ĝin en la Arkivo de Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "La libroj de Duxiu delonge estis piratitaj en la ĉina interreto. Ili kutime estas vendataj por malpli ol dolaro de revendistoj. Ili estas tipe distribuitaj uzante la ĉinan ekvivalenton de Google Drive, kiu ofte estis hakita por permesi pli da stokspaco. Iuj teknikaj detaloj troveblas <a %(github_duty_machine)s>ĉi tie</a> kaj <a %(github_821_github_io)s>ĉi tie</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Kvankam la libroj estis duonpublike distribuitaj, estas sufiĉe malfacile akiri ilin en amaso. Ni havis ĉi tion alte en nia FARU-listo, kaj asignis plurajn monatojn de plentempa laboro por ĝi. Tamen, lastatempe nekredebla, mirinda, kaj talenta volontulo kontaktis nin, dirante, ke ili jam faris ĉion ĉi — je granda kosto. Ili dividis la tutan kolekton kun ni, sen atendi ion en reveno, krom la garantio de longtempa konservado. Vere rimarkinda. Ili konsentis peti helpon ĉi tiamaniere por ricevi la kolekton OCR-itan."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La kolekto enhavas 7,543,702 dosierojn. Ĉi tio estas pli ol Library Genesis nefikcio (ĉirkaŭ 5.3 milionoj). La totala dosiergrandeco estas ĉirkaŭ 359TB (326TiB) en ĝia nuna formo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Ni estas malfermitaj al aliaj proponoj kaj ideoj. Simple kontaktu nin. Rigardu la Arkivon de Anna por pli da informoj pri niaj kolektoj, konservaj klopodoj, kaj kiel vi povas helpi. Dankon!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Averto: ĉi tiu bloga afiŝo estas malaktuala. Ni decidis, ke IPFS ankoraŭ ne estas preta por la ĉefa tempo. Ni ankoraŭ ligos al dosieroj en IPFS el la Arkivo de Anna kiam eble, sed ni ne gastigos ĝin mem plu, nek ni rekomendas al aliaj speguli uzante IPFS. Bonvolu vidi nian Torrents-paĝon se vi volas helpi konservi nian kolekton."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Helpu semadi Z-Bibliotekon en IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Kiel funkciigi ombran bibliotekon: operacioj ĉe la Arkivo de Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Ne ekzistas <q>AWS por ombraj bonfaradoj,</q> do kiel ni funkciigas la Arkivon de Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Mi funkciigas <a %(wikipedia_annas_archive)s>la Arkivon de Anna</a>, la plej grandan malfermfontan neprofitcelan serĉilon por <a %(wikipedia_shadow_library)s>ombraj bibliotekoj</a>, kiel Sci-Hub, Library Genesis, kaj Z-Biblioteko. Nia celo estas fari scion kaj kulturon facile alireblaj, kaj finfine konstrui komunumon de homoj, kiuj kune arkivas kaj konservas <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>ĉiujn librojn en la mondo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "En ĉi tiu artikolo mi montros kiel ni funkciigas ĉi tiun retejon, kaj la unikajn defiojn, kiuj venas kun operaciado de retejo kun dubinda jura statuso, ĉar ne ekzistas “AWS por ombraj bonfaradoj”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Ankaŭ kontrolu la fratan artikolon <a %(blog_how_to_become_a_pirate_archivist)s>Kiel fariĝi pirata arkivisto</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Novigaj ĵetonoj"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Ni komencu kun nia teknologia stako. Ĝi estas intence enuiga. Ni uzas Flask, MariaDB, kaj ElasticSearch. Tio estas laŭvorte ĉio. Serĉado estas plejparte solvita problemo, kaj ni ne intencas reinventi ĝin. Cetere, ni devas elspezi niajn <a %(mcfunley)s>novigajn ĵetonojn</a> por io alia: ne esti malaktivigitaj de la aŭtoritatoj."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Do kiom laŭleĝa aŭ kontraŭleĝa estas la Arkivo de Anna? Ĉi tio plejparte dependas de la jura jurisdikcio. Plej multaj landoj kredas je iu formo de kopirajto, kio signifas, ke homoj aŭ kompanioj ricevas ekskluzivan monopolon pri certaj specoj de verkoj por certa periodo de tempo. Flanke, ĉe la Arkivo de Anna ni kredas, ke kvankam estas iuj avantaĝoj, ĝenerale kopirajto estas neta negativaĵo por la socio — sed tio estas rakonto por alia fojo."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ĉi tiu ekskluziva monopolo pri certaj verkoj signifas, ke estas kontraŭleĝe por iu ajn ekster ĉi tiu monopolo rekte distribui tiujn verkojn — inkluzive nin. Sed la Arkivo de Anna estas serĉilo, kiu ne rekte distribuas tiujn verkojn (almenaŭ ne en nia klara retejo), do ni devus esti en ordo, ĉu ne? Ne ĝuste. En multaj jurisdikcioj estas ne nur kontraŭleĝe distribui kopirajtitajn verkojn, sed ankaŭ ligi al lokoj, kiuj faras tion. Klara ekzemplo de tio estas la usona DMCA-leĝo."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Tio estas la plej strikta fino de la spektro. Aliflanke de la spektro povus teorie esti landoj sen kopirajtaj leĝoj entute, sed ĉi tiuj vere ne ekzistas. Preskaŭ ĉiu lando havas iun formon de kopirajta leĝo en la libroj. Devigo estas alia rakonto. Estas multaj landoj kun registaroj, kiuj ne zorgas pri devigo de kopirajta leĝo. Estas ankaŭ landoj inter la du ekstremoj, kiuj malpermesas distribui kopirajtitajn verkojn, sed ne malpermesas ligi al tiaj verkoj."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Alia konsidero estas ĉe la kompanio-nivelo. Se kompanio funkcias en jurisdikcio, kiu ne zorgas pri kopirajto, sed la kompanio mem ne volas preni ajnan riskon, tiam ili povus malaktivigi vian retejon tuj kiam iu plendas pri ĝi."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Fine, granda konsidero estas pagoj. Ĉar ni devas resti anonimaj, ni ne povas uzi tradiciajn pagmetodojn. Ĉi tio lasas nin kun kriptovalutoj, kaj nur malgranda subaro de kompanioj subtenas tiujn (estas virtualaj debetkartoj pagitaj per kripto, sed ili ofte ne estas akceptitaj)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistemo-arkitekturo"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Do ni diru, ke vi trovis iujn kompaniojn, kiuj pretas gastigi vian retejon sen malaktivigi vin — ni nomu ĉi tiujn “liberec-amantaj provizantoj” 😄. Vi rapide trovos, ke gastigi ĉion kun ili estas sufiĉe multekosta, do vi eble volas trovi iujn “malmultekostajn provizantojn” kaj fari la realan gastigon tie, proksimigante tra la liberec-amantaj provizantoj. Se vi faras ĝin ĝuste, la malmultekostaj provizantoj neniam scios, kion vi gastigas, kaj neniam ricevos plendojn."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Kun ĉiuj ĉi tiuj provizantoj estas risko, ke ili malaktivigos vin ĉiuokaze, do vi ankaŭ bezonas redundon. Ni bezonas ĉi tion sur ĉiuj niveloj de nia stako."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Unu iom liberec-amanta kompanio, kiu metis sin en interesan pozicion, estas Cloudflare. Ili <a %(blog_cloudflare)s>argumentis</a>, ke ili ne estas gastiga provizanto, sed servaĵo, kiel ISP. Ili do ne estas submetitaj al DMCA aŭ aliaj malaktivigaj petoj, kaj plusendas ajnajn petojn al via reala gastiga provizanto. Ili iris ĝis iri al kortumo por protekti ĉi tiun strukturon. Ni do povas uzi ilin kiel alian tavolon de kaŝmemoro kaj protekto."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare ne akceptas anonimajn pagojn, do ni povas uzi nur ilian senpagan planon. Ĉi tio signifas, ke ni ne povas uzi iliajn ŝarĝ-ekvilibrigajn aŭ failover-funkciojn. Ni do <a %(annas_archive_l255)s>realigis ĉi tion mem</a> ĉe la domajna nivelo. Dum paĝoŝarĝo, la retumilo kontrolos ĉu la nuna domajno ankoraŭ disponeblas, kaj se ne, ĝi reskribas ĉiujn URL-ojn al alia domajno. Ĉar Cloudflare kaŝas multajn paĝojn, ĉi tio signifas, ke uzanto povas alveni sur nia ĉefa domajno, eĉ se la prokura servilo estas malsupre, kaj tiam ĉe la sekva klako esti movita al alia domajno."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ni ankoraŭ ankaŭ havas normalajn operaciajn zorgojn por trakti, kiel ekzemple monitorado de servila sano, registri malantaŭajn kaj antaŭajn erarojn, kaj tiel plu. Nia failover-arkitekturo permesas pli da fortikeco ankaŭ en ĉi tiu fronto, ekzemple per funkciado de tute malsama aro de serviloj sur unu el la domajnoj. Ni eĉ povas funkciigi pli malnovajn versiojn de la kodo kaj datasets sur ĉi tiu aparta domajno, en kazo kritika cimo en la ĉefa versio restas nerimarkita."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Ni ankaŭ povas protekti kontraŭ Cloudflare turniĝanta kontraŭ ni, forigante ĝin de unu el la domajnoj, kiel ĉi tiu aparta domajno. Diversaj permutaĵoj de ĉi tiuj ideoj estas eblaj."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Iloj"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Ni rigardu, kiajn ilojn ni uzas por plenumi ĉion ĉi. Ĉi tio tre evoluas dum ni renkontas novajn problemojn kaj trovas novajn solvojn."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Aplika servilo: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Prokura servilo: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Servila administrado: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Evoluado: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Cepbulajna statika gastigado: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Estas kelkaj decidoj, pri kiuj ni hezitis. Unu estas la komunikado inter serviloj: ni kutimis uzi Wireguard por tio, sed trovis, ke ĝi foje ĉesas transdoni ajnajn datumojn, aŭ nur transdonas datumojn en unu direkto. Tio okazis kun pluraj malsamaj Wireguard-agordoj, kiujn ni provis, kiel <a %(github_costela_wesher)s>wesher</a> kaj <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ni ankaŭ provis tuneli havenojn super SSH, uzante autossh kaj sshuttle, sed renkontis <a %(github_sshuttle)s>problemojn tie</a> (kvankam ankoraŭ ne estas klare al mi ĉu autossh suferas de TCP-super-TCP-problemoj aŭ ne — ĝi simple ŝajnas al mi kiel malglata solvo, sed eble ĝi estas fakte bona?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Anstataŭe, ni revenis al rektaj konektoj inter serviloj, kaŝante ke servilo funkcias ĉe malmultekostaj provizantoj uzante IP-filtradon kun UFW. Ĉi tio havas la malavantaĝon, ke Docker ne funkcias bone kun UFW, krom se vi uzas <code>network_mode: \"host\"</code>. Ĉio ĉi estas iom pli erar-ema, ĉar vi eksponos vian servilon al la interreto kun nur eta misagordo. Eble ni devus reveni al autossh — reagoj estus tre bonvenaj ĉi tie."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Ni ankaŭ hezitis inter Varnish kaj Nginx. Nuntempe ni ŝatas Varnish, sed ĝi havas siajn kapricojn kaj malglatajn randojn. La sama aplikiĝas al Checkmk: ni ne amas ĝin, sed ĝi funkcias por nun. Weblate estis akceptebla sed ne mirinda — mi foje timas, ke ĝi perdos miajn datumojn kiam ajn mi provas sinkronigi ĝin kun nia git-repo. Flask estis ĝenerale bona, sed ĝi havas kelkajn strangajn kapricojn, kiuj kostis multe da tempo por senararigi, kiel agordi kutimajn domajnojn, aŭ problemojn kun ĝia SqlAlchemy-integriĝo."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Ĝis nun la aliaj iloj estis bonegaj: ni ne havas seriozajn plendojn pri MariaDB, ElasticSearch, Gitlab, Zulip, Docker, kaj Tor. Ĉiuj ĉi tiuj havis kelkajn problemojn, sed nenion tro seriozan aŭ tempopostulan."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Konkludo"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Ĝi estis interesa sperto lerni kiel starigi fortikan kaj rezisteman ombran bibliotekan serĉilon. Estas multe pli da detaloj por dividi en postaj afiŝoj, do sciigu min, kion vi ŝatus lerni pli pri!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Kiel ĉiam, ni serĉas donacojn por subteni ĉi tiun laboron, do nepre kontrolu la Donacan paĝon en la Arkivo de Anna. Ni ankaŭ serĉas aliajn specojn de subteno, kiel stipendiojn, longdaŭrajn sponsorojn, alt-riskajn pagprovizantojn, eble eĉ (gustumajn!) reklamojn. Kaj se vi volas kontribui vian tempon kaj kapablojn, ni ĉiam serĉas programistojn, tradukistojn, kaj tiel plu. Dankon pro via intereso kaj subteno."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna kaj la teamo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Saluton, mi estas Anna. Mi kreis <a %(wikipedia_annas_archive)s>la Arkivon de Anna</a>, la plej grandan ombran bibliotekon en la mondo. Ĉi tio estas mia persona blogo, en kiu mi kaj miaj teamanoj skribas pri piratado, cifereca konservado, kaj pli."

#, fuzzy
msgid "blog.index.text2"
msgstr "Konektiĝu kun mi en <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Notu, ke ĉi tiu retejo estas nur blogo. Ni nur gastigas niajn proprajn vortojn ĉi tie. Neniuj torentoj aŭ aliaj kopirajtigitaj dosieroj estas gastigitaj aŭ ligitaj ĉi tie."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogaĵoj"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat skrapado"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Metante 5,998,794 librojn en IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Averto: ĉi tiu bloga afiŝo estas malaktuala. Ni decidis, ke IPFS ankoraŭ ne estas preta por la ĉefa tempo. Ni ankoraŭ ligos al dosieroj en IPFS el la Arkivo de Anna kiam eble, sed ni ne gastigos ĝin mem plu, nek ni rekomendas al aliaj speguli uzante IPFS. Bonvolu vidi nian Torrents-paĝon se vi volas helpi konservi nian kolekton."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> La Arkivo de Anna skrapis la tutan WorldCat (la plej granda biblioteka metadata kolekto en la mondo) por fari TODO-liston de libroj, kiuj bezonas esti konservitaj.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Antaŭ unu jaro, ni <a %(blog)s>ekiris</a> por respondi ĉi tiun demandon: <strong>Kia procento de libroj estis permanente konservita de ombraj bibliotekoj?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Kiam libro eniras en malfermdatan ombran bibliotekon kiel <a %(wikipedia_library_genesis)s>Library Genesis</a>, kaj nun <a %(wikipedia_annas_archive)s>la Arkivo de Anna</a>, ĝi estas spegulita tra la tuta mondo (per torentoj), tiel praktike konservante ĝin por ĉiam."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Por respondi la demandon pri kiu procento de libroj estis konservita, ni bezonas scii la denominatoron: kiom da libroj ekzistas entute? Kaj ideale ni ne nur havas nombron, sed ankaŭ faktan metadata. Tiam ni povas ne nur kongrui ilin kontraŭ ombraj bibliotekoj, sed ankaŭ <strong>krei TODO-liston de restantaj libroj por konservi!</strong> Ni povus eĉ komenci revi pri homamasigita klopodo por trairi ĉi tiun TODO-liston."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Ni skrapis <a %(wikipedia_isbndb_com)s>ISBNdb</a>, kaj elŝutis la <a %(openlibrary)s>Open Library dataseton</a>, sed la rezultoj estis malsatisfaj. La ĉefa problemo estis, ke ne estis granda interkovro de ISBN-oj. Vidu ĉi tiun Venn-diagramon el <a %(blog)s>nia bloga afiŝo</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ni estis tre surprizitaj pri kiom malmulte da interkovro estis inter ISBNdb kaj Open Library, kiuj ambaŭ libere inkluzivas datumojn el diversaj fontoj, kiel retaj skrapoj kaj bibliotekaj rekordoj. Se ili ambaŭ bone faras sian laboron trovante plej multajn ISBN-ojn tie ekstere, iliaj cirkloj certe havus konsiderindan interkovron, aŭ unu estus subaro de la alia. Tio igis nin demandi, kiom da libroj falas <em>komplete ekster tiuj ĉi cirkloj</em>? Ni bezonas pli grandan datumbazon."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Tiam ni celis la plej grandan libran datumbazon en la mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Ĉi tio estas proprieta datumbazo de la neprofitcela <a %(wikipedia_oclc)s>OCLC</a>, kiu kolektas metadata-rekordojn el bibliotekoj tra la tuta mondo, kontraŭ donado al tiuj bibliotekoj aliron al la plena dataseto, kaj montrado de ili en la serĉrezultoj de finuzantoj."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Kvankam OCLC estas neprofitcela, ilia komerca modelo postulas protekti sian datumbazon. Nu, ni bedaŭras diri, amikoj ĉe OCLC, ni donas ĉion for. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Dum la pasinta jaro, ni zorge skrapis ĉiujn WorldCat-rekordojn. Komence, ni havis bonŝancan paŭzon. WorldCat ĵus lanĉis sian kompletan retejan rediseñon (en aŭg 2022). Ĉi tio inkluzivis konsiderindan revizion de iliaj malantaŭaj sistemoj, enkondukante multajn sekurecajn mankojn. Ni tuj kaptis la ŝancon, kaj povis skrapi centojn da milionoj (!) da rekordoj en nur kelkaj tagoj."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat-rediseño</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Post tio, sekurecaj mankoj estis malrapide riparitaj unu post la alia, ĝis la lasta, kiun ni trovis, estis flikita antaŭ ĉirkaŭ monato. Tiam ni havis preskaŭ ĉiujn rekordojn, kaj nur celis iomete pli altkvalitajn rekordojn. Do ni sentis, ke estas tempo por eldoni!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Rigardu kelkajn bazajn informojn pri la datumoj:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Konteneroj de Arkivo de Anna (AAC)</a>, kiuj estas esence <a %(jsonlines)s>JSON Linioj</a> kunpremitaj per <a %(zstd)s>Zstandard</a>, plus kelkaj normigitaj semantikoj. Ĉi tiuj konteneroj envolvas diversajn tipojn de rekordoj, bazitaj sur la malsamaj skrapoj, kiujn ni deplojis."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Datumoj"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Okazis nekonata eraro. Bonvolu kontakti nin ĉe %(email)s kun ekrankopio."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Ĉi tiu monero havas pli altan minimumon ol kutime. Bonvolu elekti alian daŭron aŭ alian moneron."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Peto ne povis esti kompletigita. Bonvolu reprovi post kelkaj minutoj, kaj se ĝi daŭre okazas, kontaktu nin ĉe %(email)s kun ekrankopio."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Eraro en pagoprocezo. Bonvolu atendi momenton kaj reprovi. Se la problemo daŭras pli ol 24 horojn, bonvolu kontakti nin ĉe %(email)s kun ekrankopio."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "kaŝita komento"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Dosiera problemo: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Pli bona versio"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ĉu vi volas raporti ĉi tiun uzanton pro misuzema aŭ maldeca konduto?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Raporti misuzon"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Misuzo raportita:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Vi raportis ĉi tiun uzanton pro misuzo."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Respondi"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Bonvolu <a %(a_login)s>ensaluti</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Vi lasis komenton. Eble daŭros minuton por ĝi aperi."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Io misfunkciis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s trafitaj paĝoj"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Ne videbla en Libgen.rs Nefikcio"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Ne videbla en Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Ne videbla en Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Markita kiel rompita en Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Mankas en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Markita kiel “spamo” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Markita kiel “malbona dosiero” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Ne ĉiuj paĝoj povis esti konvertitaj al PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Ekzekuto de exiftool malsukcesis sur ĉi tiu dosiero"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (nekonata)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libro (nefikcio)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libro (fikcio)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Revua artikolo"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Normdokumentoj"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Revuo"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komikso"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Muzika partituro"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Aŭdlibro"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Alia"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Elŝuto de Partnera Servilo"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Ekstera elŝuto"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Ekstera prunto"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Ekstera prunto (presado malebligita)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Esploru metadatenojn"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Enhavita en torentoj"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Ĉina"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Alŝutoj al AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Indekso"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Ĉeĥa metadateno"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Rusa Ŝtata Biblioteko"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titolo"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Aŭtoro"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Eldonejo"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Eldono"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Eldonjaro"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Originala dosiernomo"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Priskribo kaj metadatenaj komentoj"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Elŝutoj de Partnera Servilo provizore ne disponeblas por ĉi tiu dosiero."

msgid "common.md5.servers.fast_partner"
msgstr "Rapida Partnera Servilo #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(rekomendita)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sen retumila kontrolo aŭ atendolistoj)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Malrapida Partnera Servilo #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(ete pli rapida sed kun atendolisto)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sen atendolisto, sed povas esti tre malrapida)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Nefikcio"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fikcio"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(ankaŭ alklaku “GET” ĉe la supro)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klaku “GET” ĉe la supro)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "iliaj reklamoj estas konataj enhavi malican programaron, do uzu reklaman blokilon aŭ ne alklaku reklamojn"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC dosieroj povas esti nefidindaj por elŝuti)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Biblioteko en Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(postulas la Tor-Retumilon)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Pruntepreni de la Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(nur por patronoj kun malebligita presado)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(asociita DOI eble ne disponeblas en Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "kolekto"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torento"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Amasaj torentaj elŝutoj"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(spertuloj nur)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Serĉu en la Arkivo de Anna per ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Serĉu diversajn aliajn datumbazojn por ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Trovu originalan rekordon en ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Serĉu en la Arkivo de Anna por Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Trovu originalan registron en Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Serĉu en la Arkivo de Anna per OCLC (WorldCat) numero"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Trovu originalan rekordon en WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Serĉu en la Arkivo de Anna por DuXiu SSID-nombro"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Serĉu permane en DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Serĉu en la Arkivo de Anna por CADAL SSNO-nombro"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Trovu originalan rekordon en CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Serĉu en la Arkivo de Anna por DuXiu DXID-nombro"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Indekso"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "La Arkivo de Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(sen retumila kontrolo postulata)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Ĉeĥa metadatenoj %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadatumoj"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "priskribo"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternativa dosiernomo"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternativa titolo"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternativa aŭtoro"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternativa eldonejo"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternativa eldono"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternativa etendo"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadatenaj komentoj"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternativa priskribo"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "dato malfermita fonto"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub dosiero “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Kontrolita Cifereca Pruntado dosiero “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Ĉi tio estas registro de dosiero el la Internet Archive, ne rekte elŝutebla dosiero. Vi povas provi pruntepreni la libron (ligilo sube), aŭ uzi ĉi tiun URL-on kiam <a %(a_request)s>petante dosieron</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Se vi havas ĉi tiun dosieron kaj ĝi ankoraŭ ne estas disponebla en la Arkivo de Anna, konsideru <a %(a_request)s>alŝuti ĝin</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadatenregistro"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadatenregistro"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) numero %(id)s metadatenregistro"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadatenregistro"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadatenregistro"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadatenoto"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadatenoto"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Ĉi tio estas metadateno-rekordo, ne elŝutebla dosiero. Vi povas uzi ĉi tiun URL-on kiam <a %(a_request)s>petante dosieron</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadaten de ligita registro"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Plibonigi metadaten en Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Averto: multoblaj ligitaj registroj:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Plibonigi metadatenojn"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Raporti dosieran kvaliton"

msgid "page.search.results.download_time"
msgstr "Tempo de elŝuto"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Retejo:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Serĉu en la Arkivo de Anna por “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Koda Esplorilo:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vidi en Koda Esplorilo “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Legu pli…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Elŝutoj (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Pruntepreni (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Esploru metadatenojn (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentoj (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listoj (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistikoj (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Teknikaj detaloj"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Ĉi tiu dosiero eble havas problemojn, kaj estis kaŝita de fonta biblioteko.</span> Foje tio estas laŭ peto de kopirajta posedanto, foje ĉar pli bona alternativo estas disponebla, sed foje pro problemo kun la dosiero mem. Ĝi eble ankoraŭ estas bona por elŝuti, sed ni rekomendas unue serĉi alternativan dosieron. Pli da detaloj:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Pli bona versio de ĉi tiu dosiero eble disponeblas ĉe %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Se vi ankoraŭ volas elŝuti ĉi tiun dosieron, certigu uzi nur fidindan, ĝisdatigitan programaron por malfermi ĝin."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Rapidaj elŝutoj"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Rapidaj elŝutoj</strong> Fariĝu <a %(a_membership)s>membro</a> por subteni la longdaŭran konservadon de libroj, artikoloj, kaj pli. Por montri nian dankemon pro via subteno, vi ricevas rapidajn elŝutojn. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se vi donacas ĉi-monate, vi ricevas <strong>duoble</strong> la nombron de rapidaj elŝutoj."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Rapidaj elŝutoj</strong> Vi havas %(remaining)s hodiaŭ. Dankon pro esti membro! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Rapidaj elŝutoj</strong> Vi elĉerpis viajn rapidajn elŝutojn por hodiaŭ."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Rapidaj elŝutoj</strong> Vi elŝutis ĉi tiun dosieron lastatempe. Ligiloj restas validaj dum iom da tempo."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opcio #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(sen redirekto)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(malfermi en spektanto)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Referu amikon, kaj ambaŭ vi kaj via amiko ricevos %(percentage)s%% bonusajn rapidajn elŝutojn!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Lerni pli…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Malrapidaj elŝutoj"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "De fidindaj partneroj."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Pli da informoj en la <a %(a_slow)s>Oftaj Demandoj</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(povas postuli <a %(a_browser)s>retumilan konfirmon</a> — senlimaj elŝutoj!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Post elŝuto:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Malfermi en nia spektanto"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "montri eksterajn elŝutojn"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Eksteraj elŝutoj"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Neniuj elŝutoj trovitaj."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Ĉiuj elŝuteblaj opcioj havas la saman dosieron, kaj devus esti sekuraj por uzi. Tamen, ĉiam estu singarda kiam elŝutante dosierojn de la interreto, precipe de retejoj ekster la Arkivo de Anna. Ekzemple, certigu ke viaj aparatoj estas ĝisdatigitaj."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Por grandaj dosieroj, ni rekomendas uzi elŝutan administrilon por eviti interrompojn."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Rekomenditaj elŝutaj administriloj: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Vi bezonos legilon por e-libroj aŭ PDF por malfermi la dosieron, depende de la dosierformato."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Rekomenditaj e-libraj legiloj: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Reta spektanto de Arkivo de Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Uzu interretajn ilojn por konverti inter formatoj."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Rekomenditaj konvertaj iloj: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Vi povas sendi ambaŭ PDF kaj EPUB dosierojn al via Kindle aŭ Kobo e-legilo."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Rekomenditaj iloj: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon-a “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz-a “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Subtenu aŭtorojn kaj bibliotekojn"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Se vi ŝatas ĉi tion kaj povas permesi ĝin, konsideru aĉeti la originalon aŭ subteni la aŭtorojn rekte."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Se ĉi tio estas disponebla en via loka biblioteko, konsideru prunti ĝin senpage tie."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Dosiera kvalito"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Helpu la komunumon raportante la kvaliton de ĉi tiu dosiero! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Raporti dosieran problemon (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Bona dosiera kvalito (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Aldoni komenton (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Kio estas malĝusta kun ĉi tiu dosiero?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Bonvolu uzi la <a %(a_copyright)s>DMCA / Kopirajta plendoformularo</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Priskribu la problemon (deviga)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Problema priskribo"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 de pli bona versio de ĉi tiu dosiero (se aplikebla)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Plenigu ĉi tion se estas alia dosiero kiu proksime kongruas kun ĉi tiu dosiero (sama eldono, sama dosiera etendo se vi povas trovi unu), kiun homoj devus uzi anstataŭ ĉi tiu dosiero. Se vi scias pri pli bona versio de ĉi tiu dosiero ekster la Arkivo de Anna, tiam bonvolu <a %(a_upload)s>alŝuti ĝin</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Vi povas akiri la md5 de la URL, ekz."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Sendi raporton"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lernu kiel <a %(a_metadata)s>plibonigi la metadatenojn</a> por ĉi tiu dosiero mem."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Dankon pro sendi vian raporton. Ĝi estos montrata sur ĉi tiu paĝo, kaj ankaŭ reviziita mane de Anna (ĝis ni havos taŭgan moderan sistemon)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Io misfunkciis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Se ĉi tiu dosiero havas bonegan kvaliton, vi povas diskuti pri ĝi ĉi tie! Se ne, bonvolu uzi la butonon “Raporti dosierproblemon”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Mi amis ĉi tiun libron!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lasu komenton"

#, fuzzy
msgid "common.english_only"
msgstr "La teksto sube daŭras en la angla."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totalaj elŝutoj: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“Dosiera MD5” estas haketo kiu estas kalkulita el la enhavo de la dosiero, kaj estas sufiĉe unika bazita sur tiu enhavo. Ĉiuj ombro-bibliotekoj kiujn ni indeksis ĉi tie ĉefe uzas MD5-ojn por identigi dosierojn."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Dosiero povas aperi en pluraj ombro-bibliotekoj. Por informoj pri la diversaj datasets kiujn ni kompilis, vidu la <a %(a_datasets)s>paĝon pri datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Ĉi tiu estas dosiero administrata de la <a %(a_ia)s>IA’s Controlled Digital Lending</a> biblioteko, kaj indeksita de la Arkivo de Anna por serĉado. Por informoj pri la diversaj datasets kiujn ni kompilis, vidu la <a %(a_datasets)s>paĝon pri datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Por informoj pri ĉi tiu specifa dosiero, kontrolu ĝian <a %(a_href)s>JSON-dosieron</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problemo ŝarĝante ĉi tiun paĝon"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Bonvolu refreŝigi por reprovi. <a %(a_contact)s>Kontaktu nin</a> se la problemo persistas dum pluraj horoj."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Ne trovita"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” ne estis trovita en nia datumbazo."

#, fuzzy
msgid "page.login.title"
msgstr "Ensaluti / Registriĝi"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Retumila kontrolo"

#, fuzzy
msgid "page.login.text1"
msgstr "Por malhelpi spam-robotojn krei multajn kontojn, ni devas unue kontroli vian retumilon."

#, fuzzy
msgid "page.login.text2"
msgstr "Se vi kaptas vin en senfina buklo, ni rekomendas instali <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Eble ankaŭ helpas malŝalti reklamblokilojn kaj aliajn retumilajn etendaĵojn."

#, fuzzy
msgid "page.codes.title"
msgstr "Kodoj"

#, fuzzy
msgid "page.codes.heading"
msgstr "Koda Esploristo"

#, fuzzy
msgid "page.codes.intro"
msgstr "Esploru la kodojn kun kiuj rekordoj estas etikedataj, laŭ prefikso. La kolumno “rekordoj” montras la nombron de rekordoj etikedataj kun kodoj kun la donita prefikso, kiel vidite en la serĉilo (inkluzive de nur-metadatenaj rekordoj). La kolumno “kodoj” montras kiom da realaj kodoj havas donitan prefikson."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Ĉi tiu paĝo povas daŭri iom da tempo por generi, tial ĝi postulas Cloudflare captcha. <a %(a_donate)s>Membroj</a> povas preterlasi la captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Bonvolu ne skrapi ĉi tiujn paĝojn. Anstataŭe ni rekomendas <a %(a_import)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn, kaj funkciigi nian <a %(a_software)s>malfermitkodan kodon</a>. La krudaj datumoj povas esti mane esploritaj per JSON-dosieroj kiel <a %(a_json_file)s>ĉi tiu</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefikso"

#, fuzzy
msgid "common.form.go"
msgstr "Iru"

#, fuzzy
msgid "common.form.reset"
msgstr "Restarigi"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Serĉu la Arkivon de Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Averto: kodo enhavas malĝustajn Unikodajn signojn, kaj povus konduti malĝuste en diversaj situacioj. La kruda binaraĵo povas esti dekodita el la base64-reprezento en la URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Konata koda prefikso “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefikso"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etikedo"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Priskribo"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL por specifa kodo"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” estos anstataŭigita per la valoro de la kodo"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Ĝenerala URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Retejo"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s registro kongrua kun “%(prefix_label)s”"
msgstr[1] "%(count)s registroj kongruaj kun “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL por specifa kodo: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Pli…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kodoj komencantaj per “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indekso de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "registroj"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kodoj"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Malpli ol %(count)s registroj"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Por DMCA / kopirajtaj asertoj, uzu <a %(a_copyright)s>ĉi tiun formularon</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Ajna alia maniero kontakti nin pri kopirajtaj asertoj estos aŭtomate forigita."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Ni tre bonvenigas viajn reagojn kaj demandojn!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tamen, pro la kvanto de spamo kaj sensencaj retpoŝtoj, kiujn ni ricevas, bonvolu marki la skatolojn por konfirmi, ke vi komprenas ĉi tiujn kondiĉojn por kontakti nin."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Aŭtorrajtoj al ĉi tiu retpoŝto estos ignoritaj; uzu la formularon anstataŭe."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partneraj serviloj estas neatingeblaj pro fermoj de gastigado. Ili devus esti denove funkciantaj baldaŭ."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Membrecoj estos plilongigitaj laŭe."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Ne retpoŝtu al ni por <a %(a_request)s>peti librojn</a><br>aŭ malgrandajn (<10k) <a %(a_upload)s>alŝutojn</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Kiam vi demandas pri kontaj aŭ donacaj aferoj, aldonu vian kontan ID, ekrankopiojn, kvitancojn, kiel eble plej multe da informoj. Ni kontrolas nian retpoŝton nur ĉiun 1-2 semajnojn, do ne inkluzivi ĉi tiujn informojn malfruigos ajnan solvon."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Montri retpoŝton"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Formulario de aserto pri kopirajto"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Se vi havas DMCA aŭ alian aserton pri kopirajto, bonvolu plenigi ĉi tiun formularon kiel eble plej precize. Se vi renkontas iujn problemojn, bonvolu kontakti nin ĉe nia dediĉita DMCA-adreso: %(email)s. Notu, ke asertoj senditaj al ĉi tiu adreso ne estos prilaboritaj, ĝi estas nur por demandoj. Bonvolu uzi la suban formularon por sendi viajn asertojn."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL-oj en la Arkivo de Anna (deviga). Unu por linio. Bonvolu inkluzivi nur URL-ojn, kiuj priskribas la saman eldonon de libro. Se vi volas fari aserton por pluraj libroj aŭ pluraj eldonoj, bonvolu sendi ĉi tiun formularon plurfoje."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Asertoj, kiuj kunigas plurajn librojn aŭ eldonojn, estos malakceptitaj."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Via nomo (deviga)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adreso (deviga)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefonnumero (deviga)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Retpoŝto (deviga)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Klara priskribo de la fonta materialo (deviga)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN-oj de la fonta materialo (se aplikebla). Unu por linio. Bonvolu inkluzivi nur tiujn, kiuj ĝuste kongruas kun la eldono por kiu vi raportas kopirajtan aserton."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL-oj de la fonta materialo, unu por linio. Bonvolu preni momenton por serĉi en Open Library vian fontan materialon. Ĉi tio helpos nin konfirmi vian aserton."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL-oj al la fonta materialo, unu por linio (deviga). Bonvolu inkluzivi kiel eble plej multajn, por helpi nin konfirmi vian aserton (ekz. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Deklaro kaj subskribo (deviga)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Sendi aserton"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Dankon pro sendi vian kopirajtan aserton. Ni revizios ĝin kiel eble plej baldaŭ. Bonvolu reŝargi la paĝon por sendi alian."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Io fuŝiĝis. Bonvolu reŝargi la paĝon kaj reprovi."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Se vi interesiĝas pri spegulado de ĉi tiu datenserio por <a %(a_archival)s>arkivado</a> aŭ <a %(a_llm)s>LLM-trejnado</a>, bonvolu kontakti nin."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Nia misio estas arkivi ĉiujn librojn en la mondo (kiel ankaŭ artikolojn, magazinojn, ktp), kaj fari ilin vaste alireblaj. Ni kredas ke ĉiuj libroj devus esti spegulitaj vaste, por certigi redundon kaj rezistecon. Tial ni kunigas dosierojn el diversaj fontoj. Iuj fontoj estas tute malfermaj kaj povas esti spegulitaj amase (kiel Sci-Hub). Aliaj estas fermitaj kaj protektaj, do ni provas skrapi ilin por “liberigi” iliajn librojn. Aliaj falas ie intere."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Ĉiuj niaj datumoj povas esti <a %(a_torrents)s>torentitaj</a>, kaj ĉiuj niaj metadatenoj povas esti <a %(a_anna_software)s>generitaj</a> aŭ <a %(a_elasticsearch)s>elŝutitaj</a> kiel ElasticSearch kaj MariaDB datumbazoj. La krudaj datumoj povas esti mane esploritaj tra JSON-dosieroj kiel <a %(a_dbrecord)s>ĉi tiu</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Superrigardo"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Sube estas rapida superrigardo de la fontoj de la dosieroj en la Arkivo de Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fonto"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Grando"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% spegulita de AA / torentoj disponeblaj"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Procentoj de nombro de dosieroj"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Laste ĝisdatigita"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Nefikcio kaj Fikcio"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s dosiero"
msgstr[1] "%(count)s dosieroj"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Per Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: frostigita ekde 2021; plejparte disponebla per torentoj"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: malgrandaj aldonaĵoj ekde tiam</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Ekskludante “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fikciaj torentoj estas malantaŭe (kvankam ID-oj ~4-6M ne torentitaj ĉar ili koincidas kun niaj Zlib-torentoj)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "La “Ĉina” kolekto en Z-Library ŝajnas esti la sama kiel nia DuXiu kolekto, sed kun malsamaj MD5-oj. Ni ekskludas ĉi tiujn dosierojn el torentoj por eviti duobligon, sed ankoraŭ montras ilin en nia serĉindekso."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrolita Cifereca Pruntepreno"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ de dosieroj estas serĉeblaj."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Sume"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Ekskludante duplikatojn"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Ĉar la ombro-bibliotekoj ofte sinkronigas datumojn unu de la alia, estas konsiderinda koincido inter la bibliotekoj. Tial la nombroj ne sumiĝas al la tuto."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "La “spegulita kaj semita de la Arkivo de Anna” procento montras kiom da dosieroj ni spegulas mem. Ni semas tiujn dosierojn amase per torentoj, kaj faras ilin disponeblaj por rekta elŝuto per partneraj retejoj."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Fontaj bibliotekoj"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Iuj fontaj bibliotekoj antaŭenigas la amasan kundividon de siaj datumoj per torentoj, dum aliaj ne facile kundividas sian kolekton. En la lasta kazo, la Arkivo de Anna provas skrapi iliajn kolektojn kaj fari ilin disponeblaj (vidu nian <a %(a_torrents)s>paĝon pri Torentoj</a>). Estas ankaŭ interaj situacioj, ekzemple, kie fontaj bibliotekoj volas kundividi, sed ne havas la rimedojn por fari tion. En tiuj kazoj, ni ankaŭ provas helpi."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Sube estas superrigardo pri kiel ni interagas kun la diversaj fontaj bibliotekoj."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fonto"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Dosieroj"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Ĉiutagaj <a %(dbdumps)s>HTTP-datumbazaj elŝutoj</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Aŭtomatigitaj torentoj por <a %(nonfiction)s>Ne-fikcio</a> kaj <a %(fiction)s>Fikcio</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Arkivo de Anna administras kolekton de <a %(covers)s>librokovrilaj torentoj</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub frostigis novajn dosierojn ekde 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadatenbazaj elŝutoj disponeblaj <a %(scihub1)s>ĉi tie</a> kaj <a %(scihub2)s>ĉi tie</a>, same kiel parto de la <a %(libgenli)s>Libgen.li-datumbazo</a> (kiun ni uzas)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Datumtorentoj disponeblaj <a %(scihub1)s>ĉi tie</a>, <a %(scihub2)s>ĉi tie</a>, kaj <a %(libgenli)s>ĉi tie</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Iuj novaj dosieroj estas <a %(libgenrs)s>aldonataj</a> al “scimag” de Libgen, sed ne sufiĉe por pravigi novajn torentojn"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Trimonataj <a %(dbdumps)s>HTTP-datumbazaj elŝutoj</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Ne-fikciaj torentoj estas dividitaj kun Libgen.rs (kaj spegulitaj <a %(libgenli)s>ĉi tie</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s La Arkivo de Anna kaj Libgen.li kunlabore administras kolektojn de <a %(comics)s>bildstrioj</a>, <a %(magazines)s>revuoj</a>, <a %(standarts)s>normaj dokumentoj</a>, kaj <a %(fiction)s>fikcio (diverĝinta de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Ilia kolekto “fiction_rus” (rusa fikcio) ne havas dediĉitajn torentojn, sed estas kovrita de torentoj de aliaj, kaj ni konservas <a %(fiction_rus)s>spejlon</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s La Arkivo de Anna kaj Z-Library kunlabore administras kolekton de <a %(metadata)s>Z-Library metadatenoj</a> kaj <a %(files)s>Z-Library dosieroj</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Iuj metadatenoj disponeblaj tra <a %(openlib)s>Open Library-datumbazaj elŝutoj</a>, sed tiuj ne kovras la tutan IA-kolekton"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Neniuj facile alireblaj metadatenbazaj elŝutoj disponeblaj por ilia tuta kolekto"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s La Arkivo de Anna administras kolekton de <a %(ia)s>IA metadatenoj</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Dosieroj disponeblaj nur por pruntepreno laŭ limigita bazo, kun diversaj alirlimigoj"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Arkivo de Anna administras kolekton de <a %(ia)s>IA-dosieroj</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Diversaj metadatenbazoj disigitaj tra la ĉina interreto; kvankam ofte pagitaj datumbazoj"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Neniu facile alirebla metadatenaj elŝutoj disponeblaj por ilia tuta kolekto."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s La Arkivo de Anna administras kolekton de <a %(duxiu)s>DuXiu-metadatenoj</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Diversaj dosieraj datumbazoj disigitaj tra la ĉina interreto; kvankam ofte pagitaj datumbazoj"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Plej multaj dosieroj estas alireblaj nur uzante premiumajn BaiduYun-kontojn; malrapidaj elŝutaj rapidoj."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s La Arkivo de Anna administras kolekton de <a %(duxiu)s>DuXiu-dosieroj</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Diversaj pli malgrandaj aŭ unufojaj fontoj. Ni kuraĝigas homojn unue alŝuti al aliaj ombrobibliotekoj, sed foje homoj havas kolektojn tro grandajn por aliaj por trarigardi, kvankam ne sufiĉe grandajn por meriti sian propran kategorion."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Nur-metadatumaj fontoj"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Ni ankaŭ riĉigas nian kolekton per nur-metadatumaj fontoj, kiujn ni povas kongrui kun dosieroj, ekz. uzante ISBN-numerojn aŭ aliajn kampojn. Sube estas superrigardo de tiuj. Denove, iuj el ĉi tiuj fontoj estas tute malfermaj, dum por aliaj ni devas skrapi ilin."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Nia inspiro por kolekti metadatenojn estas la celo de Aaron Swartz \"unu retpaĝo por ĉiu libro iam ajn publikigita\", por kiu li kreis <a %(a_openlib)s>Open Library</a>. Tiu projekto bone sukcesis, sed nia unika pozicio permesas al ni akiri metadatenojn, kiujn ili ne povas. Alia inspiro estis nia deziro scii <a %(a_blog)s>kiom da libroj ekzistas en la mondo</a>, por ke ni povu kalkuli kiom da libroj ni ankoraŭ devas savi."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Notu, ke en metadatumserĉo, ni montras la originalajn rekordojn. Ni ne faras ajnan kunfandiĝon de rekordoj."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Laste ĝisdatigita"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Monataj <a %(dbdumps)s>datumbazaj elŝutoj</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Ne disponebla rekte en amaso, protektita kontraŭ skrapado"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s La Arkivo de Anna administras kolekton de <a %(worldcat)s>OCLC (WorldCat) metadatenoj</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Unuigita datumbazo"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Ni kombinas ĉiujn supre menciitajn fontojn en unu unuigitan datumbazon, kiun ni uzas por servi ĉi tiun retejon. Ĉi tiu unuigita datumbazo ne estas rekte disponebla, sed ĉar la Arkivo de Anna estas tute malfermfonta, ĝi povas esti sufiĉe facile <a %(a_generated)s>generita</a> aŭ <a %(a_downloaded)s>elŝutita</a> kiel ElasticSearch kaj MariaDB datumbazoj. La skriptoj en tiu paĝo aŭtomate elŝutos ĉiujn necesajn metadatumojn de la supre menciitaj fontoj."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Se vi ŝatus esplori niajn datumojn antaŭ ol ruli tiujn skriptojn loke, vi povas rigardi niajn JSON-dosierojn, kiuj ligas plu al aliaj JSON-dosieroj. <a %(a_json)s>Ĉi tiu dosiero</a> estas bona deirpunkto."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptita el nia <a %(a_href)s>bloga afiŝo</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> estas granda datumbazo de skanitaj libroj, kreita de la <a %(superstar_link)s>SuperStar Digital Library Group</a>. Plejparte temas pri akademiaj libroj, skanitaj por fari ilin disponeblaj ciferece al universitatoj kaj bibliotekoj. Por nia anglalingva publiko, <a %(princeton_link)s>Princeton</a> kaj la <a %(uw_link)s>University of Washington</a> havas bonajn superrigardojn. Estas ankaŭ bonega artikolo donanta pli da fono: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "La libroj de Duxiu delonge estis piratitaj en la ĉina interreto. Ili kutime estas vendataj por malpli ol dolaro de revendistoj. Ili estas tipe distribuitaj uzante la ĉinan ekvivalenton de Google Drive, kiu ofte estis hakita por permesi pli da stokspaco. Iuj teknikaj detaloj troveblas <a %(link1)s>ĉi tie</a> kaj <a %(link2)s>ĉi tie</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Kvankam la libroj estis duonpublike distribuitaj, estas sufiĉe malfacile akiri ilin amase. Ni havis ĉi tion alte en nia TODO-listo, kaj asignis plurajn monatojn de plentempa laboro por ĝi. Tamen, malfrue en 2023 nekredebla, mirinda, kaj talenta volontulo kontaktis nin, dirante ke ili jam faris ĉion ĉi tiun laboron — je granda elspezo. Ili dividis la plenan kolekton kun ni, sen atendi ion ajn kompense, krom la garantio de longtempa konservado. Vere rimarkinda."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Rimedoj"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totalaj dosieroj: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totala dosiergrandeco: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Dosieroj spegulitaj de Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Laste ĝisdatigita: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torentoj de Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Ekzempla registro en Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Nia bloga afiŝo pri ĉi tiuj datumoj"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skriptoj por importi metadatenojn"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers formato"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Pli da informoj de niaj volontuloj (krudaj notoj):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Kontrolita Cifereca Pruntado"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Ĉi tiu datenserio estas proksime rilata al la <a %(a_datasets_openlib)s>Open Library datenserio</a>. Ĝi enhavas skrapadon de ĉiuj metadatenoj kaj granda parto de dosieroj el la Kontrolita Cifereca Pruntedona Biblioteko de IA. Ĝisdatigoj estas publikigitaj en la <a %(a_aac)s>Anna’s Archive Containers formato</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Ĉi tiuj rekordoj estas rekte referencitaj de la Open Library datenserio, sed ankaŭ enhavas rekordojn, kiuj ne estas en Open Library. Ni ankaŭ havas kelkajn datumdosierojn skrapitajn de komunumaj membroj tra la jaroj."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La kolekto konsistas el du partoj. Vi bezonas ambaŭ partojn por akiri ĉiujn datumojn (krom anstataŭigitaj torentoj, kiuj estas forstrekitaj en la torentpaĝo)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "nia unua eldono, antaŭ ol ni normigis la <a %(a_aac)s>Anna’s Archive Containers (AAC) formaton</a>. Enhavas metadatenojn (kiel json kaj xml), pdf-ojn (el acsm kaj lcpdf ciferecaj pruntsistemoj), kaj kovrilajn etbildojn."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementaj novaj eldonoj, uzante AAC. Enhavas nur metadatenojn kun tempomarkoj post 2023-01-01, ĉar la resto jam estas kovrita de “ia”. Ankaŭ ĉiuj pdf-dosieroj, ĉi-foje el la acsm kaj “bookreader” (IA’s retlegilo) pruntsistemoj. Malgraŭ la nomo ne esti tute ĝusta, ni ankoraŭ enmetas bookreader-dosierojn en la ia2_acsmpdf_files kolekton, ĉar ili estas reciproke ekskluzivaj."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Ĉefa %(source)s retejo"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Cifereca Pruntejo"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadatenaj dokumentado (plej multaj kampoj)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Informoj pri lando de ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "La Internacia ISBN-Agentejo regule publikigas la intervalojn, kiujn ĝi asignis al naciaj ISBN-agentejoj. El tio ni povas derivi al kiu lando, regiono aŭ lingva grupo apartenas ĉi tiu ISBN. Ni nuntempe uzas ĉi tiujn datumojn nerekte, per la <a %(a_isbnlib)s>isbnlib</a> Python-biblioteko."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Rimedoj"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Laste ĝisdatigita: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Retejo de ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadatumoj"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Por la fono de la malsamaj forkoj de Library Genesis, vidu la paĝon por la <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "La Libgen.li enhavas plejparton de la sama enhavo kaj metadatumoj kiel la Libgen.rs, sed havas kelkajn kolektojn aldone al tio, nome komiksoj, magazinoj kaj normaj dokumentoj. Ĝi ankaŭ integris <a %(a_scihub)s>Sci-Hub</a> en siajn metadatumojn kaj serĉilon, kion ni uzas por nia datumbazo."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "La metadatumoj por ĉi tiu biblioteko estas libere disponeblaj <a %(a_libgen_li)s>ĉe libgen.li</a>. Tamen, ĉi tiu servilo estas malrapida kaj ne subtenas rekomencadon de rompitaj konektoj. La samaj dosieroj ankaŭ estas disponeblaj ĉe <a %(a_ftp)s>FTP-servilo</a>, kiu funkcias pli bone."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torentoj estas disponeblaj por plejparto de la aldona enhavo, plej precipe torentoj por bildstrioj, revuoj, kaj normaj dokumentoj estis eldonitaj en kunlaboro kun la Arkivo de Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La fikcia kolekto havas siajn proprajn torentojn (diverĝintajn de <a %(a_href)s>Libgen.rs</a>) komencante ĉe %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Laŭ la administranto de Libgen.li, la kolekto “fiction_rus” (rusa fikcio) devus esti kovrita de regule eldonitaj torentoj de <a %(a_booktracker)s>booktracker.org</a>, plej precipe la torentoj de <a %(a_flibusta)s>flibusta</a> kaj <a %(a_librusec)s>lib.rus.ec</a> (kiujn ni spegulas <a %(a_torrents)s>ĉi tie</a>, kvankam ni ankoraŭ ne establis kiuj torentoj respondas al kiuj dosieroj)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistikoj por ĉiuj kolektoj troveblas <a %(a_href)s>en la retejo de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Ne-fikcio ankaŭ ŝajnas esti diverĝinta, sed sen novaj torentoj. Ŝajnas, ke tio okazis ekde frua 2022, kvankam ni ne konfirmis tion."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certaj intervaloj sen torentoj (kiel ekzemple fikciaj intervaloj f_3463000 ĝis f_4260000) verŝajne estas dosieroj de Z-Library (aŭ aliaj duplikatoj), kvankam ni eble volas fari iun deduplikadon kaj krei torentojn por lgli-unikaj dosieroj en ĉi tiuj intervaloj."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Notu, ke la torentaj dosieroj referencantaj “libgen.is” estas eksplicite speguloj de <a %(a_libgen)s>Libgen.rs</a> (“.is” estas malsama domajno uzata de Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Utila rimedo por uzi la metadatenojn estas <a %(a_href)s>ĉi tiu paĝo</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fikciaj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Komiksoj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Magazinoj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Normaj dokumentaj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Rusaj fikciaj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadatenoj"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadatenoj per FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informoj pri metadatenaj kampoj"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Spegulo de aliaj torentoj (kaj unikaj fikciaj kaj komiksoj torentoj)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskutforumo"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nia bloga afiŝo pri la eldono de komiksoj"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "La rapida historio de la malsamaj forkigoj de Library Genesis (aŭ “Libgen”) estas, ke kun la tempo, la malsamaj homoj implikitaj en Library Genesis havis malkonsenton kaj iris siajn apartajn vojojn."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La “.fun” versio estis kreita de la originala fondinto. Ĝi estas renovigata favore al nova, pli distribuita versio."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La “.rs” versio havas tre similajn datumojn, kaj plej konstante publikigas sian kolekton en amasaj torentoj. Ĝi estas proksimume dividita en “fikcia” kaj “nefikcia” sekcio."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originale ĉe “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>“.li” versio</a> havas grandegan kolekton de komiksoj, same kiel aliajn enhavojn, kiuj ankoraŭ ne estas disponeblaj por amasa elŝuto per torentoj. Ĝi havas apartan torentan kolekton de fikciaj libroj, kaj ĝi enhavas la metadatenojn de <a %(a_scihub)s>Sci-Hub</a> en sia datumbazo."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Laŭ ĉi tiu <a %(a_mhut)s>forumafiŝo</a>, Libgen.li estis origine gastigita ĉe “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> iusence ankaŭ estas forkigo de Library Genesis, kvankam ili uzis malsaman nomon por sia projekto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Ĉi tiu paĝo temas pri la “.rs” versio. Ĝi estas konata pro konstante publikigado de kaj siaj metadatenoj kaj la plenaj enhavoj de sia librokatalogo. Ĝia librokolekto estas dividita inter fikcia kaj nefikcia parto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Utila rimedo por uzi la metadatenojn estas <a %(a_metadata)s>ĉi tiu paĝo</a> (blokas IP-gamojn, VPN eble necesas)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Ekde 2024-03, novaj torentoj estas afiŝataj en <a %(a_href)s>ĉi tiu forumfadeno</a> (blokas IP-gamojn, eble necesas VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Nefikciaj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fikciaj torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadatenoj"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informoj pri metadatenaj kampoj de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Nefikciaj torentoj"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fikciaj torentoj"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskutforumo"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torentoj de la Arkivo de Anna (librokovriloj)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Nia blogo pri la eldono de librokovriloj"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis estas konata pro jam malavare disponigi siajn datumojn amase per torentoj. Nia Libgen-kolekto konsistas el helpaj datumoj, kiujn ili ne publikigas rekte, en partnereco kun ili. Multan dankon al ĉiuj implikitaj kun Library Genesis pro kunlabori kun ni!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Eldono 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Ĉi tiu <a %(blog_post)s>unua eldono</a> estas sufiĉe malgranda: ĉirkaŭ 300GB da librokovriloj de la Libgen.rs-forko, kaj fikcio kaj nefikcio. Ili estas organizitaj same kiel ili aperas en libgen.rs, ekz.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s por nefikcia libro."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s por fikcia libro."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Same kiel kun la Z-Library-kolekto, ni metis ilin ĉiujn en grandan .tar-dosieron, kiu povas esti muntita uzante <a %(a_ratarmount)s>ratarmount</a> se vi volas servi la dosierojn rekte."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> estas proprieta datumbazo de la neprofitcela <a %(a_oclc)s>OCLC</a>, kiu agregas metadatenajn rekordojn de bibliotekoj tra la tuta mondo. Verŝajne ĝi estas la plej granda biblioteka metadatenkolekto en la mondo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktobro 2023, komenca eldono:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "En oktobro 2023 ni <a %(a_scrape)s>publikigis</a> ampleksan skrapadon de la OCLC (WorldCat) datumbazo, en la <a %(a_aac)s>Arkivo de Anna Ujoj formato</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torentoj de la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nia bloga afiŝo pri ĉi tiuj datumoj"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library estas malfermfonta projekto de la Internet Archive por katalogi ĉiun libron en la mondo. Ĝi havas unu el la plej grandaj libro-skanaj operacioj en la mondo, kaj havas multajn librojn disponeblajn por cifereca pruntepreno. Ĝia libro-metadatokatalogo estas libere disponebla por elŝuto, kaj estas inkluzivita en la Arkivo de Anna (kvankam nuntempe ne en serĉo, krom se vi eksplicite serĉas Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadatenoj"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Eldono 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Ĉi tio estas elŝuto de multaj vokoj al isbndb.com dum septembro 2022. Ni provis kovri ĉiujn ISBN-intervalojn. Ĉi tiuj estas ĉirkaŭ 30.9 milionoj da rekordoj. Sur ilia retejo ili asertas, ke ili fakte havas 32.6 milionojn da rekordoj, do ni eble iel maltrafis kelkajn, aŭ <em>ili</em> povus fari ion malĝuste."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "La JSON-respondoj estas preskaŭ krudaj de ilia servilo. Unu datuma kvalita problemo, kiun ni rimarkis, estas ke por ISBN-13-nombroj, kiuj komenciĝas per malsama prefikso ol “978-”, ili ankoraŭ inkluzivas “isbn”-kampon, kiu simple estas la ISBN-13-nombro kun la unuaj 3 nombroj fortranĉitaj (kaj la kontrolcifero rekalkulita). Ĉi tio estas evidente malĝusta, sed tiel ili ŝajnas fari ĝin, do ni ne ŝanĝis ĝin."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Alia ebla problemo, kiun vi povus renkonti, estas la fakto, ke la kampo “isbn13” havas duplikatojn, do vi ne povas uzi ĝin kiel ĉefan ŝlosilon en datumbazo. “isbn13”+“isbn”-kampoj kombinitaj ŝajnas esti unikaj."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Por fono pri Sci-Hub, bonvolu rilati al ĝia <a %(a_scihub)s>oficiala retejo</a>, <a %(a_wikipedia)s>Vikipedia paĝo</a>, kaj ĉi tiu <a %(a_radiolab)s>podkasta intervjuo</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Notu, ke Sci-Hub estis <a %(a_reddit)s>frostigita ekde 2021</a>. Ĝi estis frostigita antaŭe, sed en 2021 kelkaj milionoj da artikoloj estis aldonitaj. Tamen, iuj limigitaj nombroj da artikoloj estas aldonitaj al la Libgen “scimag” kolektoj, kvankam ne sufiĉe por pravigi novajn amasajn torentojn."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Ni uzas la Sci-Hub metadatenojn kiel provizitajn de <a %(a_libgen_li)s>Libgen.li</a> en ĝia “scimag” kolekto. Ni ankaŭ uzas la <a %(a_dois)s>dois-2022-02-12.7z</a> datumaron."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Notu, ke la “smarch” torentoj estas <a %(a_smarch)s>malaktualaj</a> kaj tial ne inkluzivitaj en nia torenta listo."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torentoj en la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadatenoj kaj torentoj"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torentoj en Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torentoj en Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Ĝisdatigoj en Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Vikipedia paĝo"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podkasta intervjuo"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Alŝutoj al la Arkivo de Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Superrigardo de <a %(a1)s>paĝo de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Diversaj pli malgrandaj aŭ unufojaj fontoj. Ni kuraĝigas homojn unue alŝuti al aliaj ombrobibliotekoj, sed foje homoj havas kolektojn tro grandajn por aliaj por trarigardi, kvankam ne sufiĉe grandajn por meriti sian propran kategorion."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La \"alŝuta\" kolekto estas dividita en pli malgrandajn subkolektojn, kiuj estas indikitaj en la AACID-oj kaj torentaj nomoj. Ĉiuj subkolektoj unue estis deduplikitaj kontraŭ la ĉefa kolekto, kvankam la metadatenaj \"upload_records\" JSON-dosieroj ankoraŭ enhavas multajn referencojn al la originalaj dosieroj. Ne-libraj dosieroj ankaŭ estis forigitaj el plej multaj subkolektoj, kaj tipe <em>ne</em> estas notitaj en la \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Multaj subkolektoj mem konsistas el sub-sub-kolektoj (ekz. de malsamaj originalaj fontoj), kiuj estas reprezentitaj kiel dosierujoj en la \"filepath\" kampoj."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "La subkolektoj estas:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkolekto"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notoj"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "foliumi"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "serĉi"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Ŝajnas esti sufiĉe kompleta. De nia volontulo “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "De <a %(a_href)s><q>ACM Digital Library 2020</q></a> torento. Havas sufiĉe altan koincidon kun ekzistantaj kolektoj de artikoloj, sed tre malmultajn MD5-koincidojn, do ni decidis konservi ĝin tute."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Skrapo de <q>iRead eBooks</q> (= fonetike <q>ai rit i-books</q>; airitibooks.com), fare de volontulo <q>j</q>. Korespondas al <q>airitibooks</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "El kolekto <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte el la originala fonto, parte de the-eye.eu, parte de aliaj speguloj."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "De privata libro-torenta retejo, <a %(a_href)s>Bibliotik</a> (ofte referita kiel “Bib”), kies libroj estis kunigitaj en torentojn laŭ nomo (A.torento, B.torento) kaj distribuitaj tra the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "De nia volontulo “bpb9v”. Por pli da informoj pri <a %(a_href)s>CADAL</a>, vidu la notojn en nia <a %(a_duxiu)s>DuXiu-datuma paĝo</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Pli de nia volontulo “bpb9v”, plejparte DuXiu-dosieroj, same kiel dosierujo “WenQu” kaj “SuperStar_Journals” (SuperStar estas la kompanio malantaŭ DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "De nia volontulo “cgiym”, ĉinaj tekstoj el diversaj fontoj (reprezentitaj kiel subdosierujoj), inkluzive de <a %(a_href)s>China Machine Press</a> (grava ĉina eldonisto)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Ne-ĉinaj kolektoj (reprezentitaj kiel subdosierujoj) de nia volontulo “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Skrapo de libroj pri ĉina arkitekturo, fare de volontulo <q>cm</q>: <q>Mi akiris ĝin ekspluatante retnetan vundeblecon ĉe la eldonejo, sed tiu truo jam estis fermita</q>. Korespondas al <q>chinese_architecture</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libroj de akademia eldonejo <a %(a_href)s>De Gruyter</a>, kolektitaj el kelkaj grandaj torentoj."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Skrapo de <a %(a_href)s>docer.pl</a>, pola dosierdivida retejo fokusita je libroj kaj aliaj skribaj verkoj. Skrapita malfrue en 2023 de volontulo “p”. Ni ne havas bonajn metadatenojn de la originala retejo (eĉ ne dosieretendaĵojn), sed ni filtris por libro-similaj dosieroj kaj ofte povis eltiri metadatenojn el la dosieroj mem."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu-epuboj, rekte de DuXiu, kolektitaj de volontulo “w”. Nur lastatempaj DuXiu-libroj estas rekte disponeblaj kiel e-libroj, do plej multaj el ĉi tiuj devas esti lastatempaj."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Restantaj DuXiu-dosieroj de volontulo “m”, kiuj ne estis en la DuXiu-proprietara PDG-formato (la ĉefa <a %(a_href)s>DuXiu-datuma aro</a>). Kolektitaj el multaj originalaj fontoj, bedaŭrinde sen konservi tiujn fontojn en la dosierujo."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Skrapo de erotikaj libroj, fare de volontulo <q>do no harm</q>. Korespondas al <q>hentai</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Kolekto skrapita de japana Manga-eldonisto de volontulo “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Elektitaj juraj arkivoj de Longquan</a>, provizitaj de volontulo “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Skrapo de <a %(a_href)s>magzdb.org</a>, aliancano de Library Genesis (ĝi estas ligita en la libgen.rs hejmpaĝo) sed kiu ne volis provizi siajn dosierojn rekte. Akirita de volontulo “p” malfrue en 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Diversaj malgrandaj alŝutoj, tro malgrandaj kiel siaj propraj subkolektoj, sed reprezentitaj kiel dosierujoj."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-libroj de AvaxHome, rusa dosierdivida retejo."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arkivo de gazetoj kaj revuoj. Korespondas al <q>newsarch_magz</q> metadata en <a %(a1)s><q>Aliaj metadata skrapoj</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Skrapo de la <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Kolekto de volontulo “o” kiu kolektis polajn librojn rekte de originalaj eldonaj (“scene”) retejoj."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Kombinitaj kolektoj de <a %(a_href)s>shuge.org</a> de volontuloj “cgiym” kaj “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (nomita laŭ la fikcia biblioteko), skrapita en 2022 de volontulo “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-kolektoj (reprezentitaj kiel dosierujoj) de volontulo “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (de <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Tajvano), mebook (mebook.cc, 我的小书屋, mia malgranda libroĉambro — woz9ts: “Ĉi tiu retejo ĉefe fokusiĝas je divido de altkvalitaj e-libroj, kelkaj el kiuj estas tajpitaj de la posedanto mem. La posedanto estis <a %(a_arrested)s>arestita</a> en 2019 kaj iu faris kolekton de dosieroj kiujn li dividis.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Restantaj DuXiu-dosieroj de volontulo “woz9ts”, kiuj ne estis en la proprieta PDG-formato de DuXiu (ankoraŭ konvertotaj al PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torentoj de Arkivo de Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library skrapo"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library havas siajn radikojn en la <a %(a_href)s>Library Genesis</a> komunumo, kaj origine startis kun iliaj datumoj. Ekde tiam, ĝi profesiigis konsiderinde, kaj havas multe pli modernan interfacon. Ili do kapablas ricevi multe pli da donacoj, kaj monaj por daŭre plibonigi sian retejon, kaj donacojn de novaj libroj. Ili amasigis grandan kolekton krom Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Ĝisdatigo de februaro 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Fine de 2022, la supozataj fondintoj de Z-Library estis arestitaj, kaj domajnoj estis konfiskitaj de usonaj aŭtoritatoj. Ekde tiam la retejo malrapide revenas rete. Estas nekonate kiu nuntempe administras ĝin."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La kolekto konsistas el tri partoj. La originalaj priskribaj paĝoj por la unuaj du partoj estas konservitaj sube. Vi bezonas ĉiujn tri partojn por akiri ĉiujn datumojn (krom anstataŭigitaj torentoj, kiuj estas forstrekitaj en la torentpaĝo)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nia unua eldono. Ĉi tio estis la tre unua eldono de tio, kio tiam nomiĝis la “Pirata Biblioteka Spegulo” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: dua eldono, ĉi-foje kun ĉiuj dosieroj envolvitaj en .tar-dosierojn."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementaj novaj eldonoj, uzante la <a %(a_href)s>Arkivo de Anna Ujoj (AAC) formato</a>, nun eldonitaj en kunlaboro kun la teamo de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torentoj de Arkivo de Anna (metadatumoj + enhavo)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Ekzempla registro en Arkivo de Anna (originala kolekto)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Ekzempla registro en Arkivo de Anna (“zlib3” kolekto)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Ĉefa retejo"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domajno"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Bloga afiŝo pri Eldono 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Bloga afiŝo pri Eldono 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-eldonoj (originalaj priskribaj paĝoj)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Eldono 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "La komenca spegulo estis akirita pene dum 2021 kaj 2022. Ĉi-momente ĝi estas iomete malaktuala: ĝi reflektas la staton de la kolekto en junio 2021. Ni ĝisdatigos ĉi tion en la estonteco. Nuntempe ni fokusiĝas al eldonado de ĉi tiu unua eldono."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Ekde Library Genesis jam estas konservita per publikaj torentoj, kaj estas inkluzivita en la Z-Library, ni faris bazan deduplikadon kontraŭ Library Genesis en junio 2022. Por tio ni uzis MD5-hashojn. Verŝajne estas multe pli da duplikata enhavo en la biblioteko, kiel pluraj dosierformatoj kun la sama libro. Tio estas malfacile detektebla precize, do ni ne faras tion. Post la deduplikado ni restas kun pli ol 2 milionoj da dosieroj, sumante iom malpli ol 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La kolekto konsistas el du partoj: MySQL “.sql.gz” dump de la metadatenoj, kaj la 72 torentdosieroj de ĉirkaŭ 50-100GB ĉiu. La metadatenoj enhavas la datumojn raportitajn de la retejo Z-Library (titolo, aŭtoro, priskribo, dosiertipo), same kiel la faktan dosiergrandecon kaj md5sum, kiujn ni observis, ĉar foje tiuj ne kongruas. Ŝajnas, ke ekzistas gamoj de dosieroj por kiuj la Z-Library mem havas malĝustajn metadatenojn. Ni ankaŭ eble malĝuste elŝutis dosierojn en iuj izolitaj kazoj, kiujn ni provos detekti kaj ripari en la estonteco."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "La grandaj torentdosieroj enhavas la faktan librodatenon, kun la Z-Library ID kiel la dosiernomo. La dosieretendaĵoj povas esti rekonstruitaj uzante la metadatenan dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La kolekto estas miksaĵo de nefikcia kaj fikcia enhavo (ne apartigita kiel en Library Genesis). La kvalito ankaŭ estas vaste varia."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Ĉi tiu unua eldono nun estas plene disponebla. Notu, ke la torentdosieroj estas disponeblaj nur per nia Tor-spegulo."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Eldono 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Ni akiris ĉiujn librojn, kiuj estis aldonitaj al la Z-Library inter nia lasta spegulo kaj aŭgusto 2022. Ni ankaŭ revenis kaj skrapis iujn librojn, kiujn ni maltrafis la unuan fojon. Ĉiukaze, ĉi tiu nova kolekto estas ĉirkaŭ 24TB. Denove, ĉi tiu kolekto estas deduplikita kontraŭ Library Genesis, ĉar jam estas torentoj disponeblaj por tiu kolekto."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "La datumoj estas organizitaj simile al la unua eldono. Estas MySQL “.sql.gz” dump de la metadatenoj, kiu ankaŭ inkluzivas ĉiujn metadatenojn de la unua eldono, tiel superante ĝin. Ni ankaŭ aldonis kelkajn novajn kolumnojn:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ĉu ĉi tiu dosiero jam estas en Library Genesis, en aŭ la nefikcia aŭ fikcia kolekto (kongrua per md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: en kiu torento ĉi tiu dosiero estas."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: agordita kiam ni ne povis elŝuti la libron."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Ni menciis ĉi tion la lastan fojon, sed nur por klarigi: “filename” kaj “md5” estas la faktaj propraĵoj de la dosiero, dum “filename_reported” kaj “md5_reported” estas tio, kion ni skrapis de Z-Library. Foje ĉi tiuj du ne kongruas unu kun la alia, do ni inkluzivis ambaŭ."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Por ĉi tiu eldono, ni ŝanĝis la kolacion al “utf8mb4_unicode_ci”, kiu devus esti kongrua kun pli malnovaj versioj de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "La datumdosieroj estas similaj al la lasta fojo, kvankam ili estas multe pli grandaj. Ni simple ne povis ĝeni krei amason da pli malgrandaj torentdosieroj. “pilimi-zlib2-0-14679999-extra.torrent” enhavas ĉiujn dosierojn, kiujn ni maltrafis en la lasta eldono, dum la aliaj torentoj estas ĉiuj novaj ID-gamoj. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Ĝisdatigo %(date)s:</strong> Ni faris plej multajn el niaj torentoj tro grandaj, kaŭzante ke torentklientoj luktas. Ni forigis ilin kaj publikigis novajn torentojn."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Ĝisdatigo %(date)s:</strong> Ankoraŭ estis tro multaj dosieroj, do ni envolvis ilin en tar-dosierojn kaj publikigis novajn torentojn denove."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Eldono 2 aldonaĵo (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Ĉi tio estas unuopa ekstra torentdosiero. Ĝi ne enhavas iujn novajn informojn, sed ĝi havas iujn datumojn en ĝi, kiuj povas daŭri iom da tempo por kalkuli. Tio faras ĝin oportuna havi, ĉar elŝuti ĉi tiun torenton ofte estas pli rapida ol kalkuli ĝin de nulo. Precipe, ĝi enhavas SQLite-indeksojn por la tar-dosieroj, por uzo kun <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Oftaj Demandoj (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Kio estas la Arkivo de Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>La Arkivo de Anna</span> estas neprofitcela projekto kun du celoj:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Konservado:</strong> Rezervi ĉiun scion kaj kulturon de la homaro.</li><li><strong>Alirebleco:</strong> Fari ĉi tiun scion kaj kulturon disponebla al iu ajn en la mondo.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Ĉiuj niaj <a %(a_code)s>kodoj</a> kaj <a %(a_datasets)s>datumoj</a> estas tute malfermitaj."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Konservado"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Ni konservas librojn, artikolojn, bildstriojn, revuojn, kaj pli, alportante ĉi tiujn materialojn el diversaj <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">ombraj bibliotekoj</a>, oficialaj bibliotekoj, kaj aliaj kolektoj kune en unu loko. Ĉiuj ĉi tiuj datumoj estas konservitaj por ĉiam per faciligado de ilia amasa duplikado — uzante torentojn — rezultigante multajn kopiojn ĉirkaŭ la mondo. Iuj ombraj bibliotekoj jam faras tion mem (ekz. Sci-Hub, Library Genesis), dum la Arkivo de Anna “liberigas” aliajn bibliotekojn, kiuj ne ofertas amasan distribuadon (ekz. Z-Library) aŭ tute ne estas ombraj bibliotekoj (ekz. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Ĉi tiu vasta distribuo, kombinita kun malfermfonta kodo, faras nian retejon rezistema al malaktivigoj, kaj certigas la longdaŭran konservadon de la scio kaj kulturo de la homaro. Lernu pli pri <a href=\"/datasets\">niaj datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Ni taksas, ke ni konservis ĉirkaŭ <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% el la mondaj libroj</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Alirado"

#, fuzzy
msgid "page.home.access.text"
msgstr "Ni kunlaboras kun partneroj por fari niajn kolektojn facile kaj senpage alireblaj por ĉiuj. Ni kredas, ke ĉiuj havas rajton al la kolektiva saĝo de la homaro. Kaj <a %(a_search)s>ne je la kosto de aŭtoroj</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Horaj elŝutoj en la lastaj 30 tagoj. Horo-mezumo: %(hourly)s. Tago-mezumo: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Ni forte kredas je la libera fluo de informoj, kaj konservado de scio kaj kulturo. Kun ĉi tiu serĉilo, ni staras sur la ŝultroj de gigantoj. Ni profunde respektas la malfacilan laboron de la homoj, kiuj kreis la diversajn ombrobibliotekojn, kaj ni esperas, ke ĉi tiu serĉilo plivastigos ilian atingon."

#, fuzzy
msgid "page.about.text3"
msgstr "Por resti ĝisdatigita pri nia progreso, sekvu Annan ĉe <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> aŭ <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Por demandoj kaj reagoj bonvolu kontakti Annan ĉe %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Kiel mi povas helpi?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Sekvu nin ĉe <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, aŭ <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Diskonigu pri Arkivo de Anna ĉe Twitter, Reddit, Tiktok, Instagram, en via loka kafejo aŭ biblioteko, aŭ kie ajn vi iras! Ni ne kredas je barado — se ni estos forigitaj, ni simple reaperos aliloke, ĉar nia tuta kodo kaj datumoj estas plene malfermitaj.</li><li>3. Se vi povas, konsideru <a href=\"/donate\">doni</a>.</li><li>4. Helpu <a href=\"https://translate.annas-software.org/\">traduki</a> nian retejon en diversajn lingvojn.</li><li>5. Se vi estas programisto, konsideru kontribui al nia <a href=\"https://annas-software.org/\">malferma fonto</a>, aŭ semadi niajn <a href=\"/datasets\">torentojn</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Ni nun ankaŭ havas sinkronigitan Matrix-kanalon ĉe %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Se vi estas sekureca esploristo, ni povas uzi viajn kapablojn por ambaŭ ofendo kaj defendo. Rigardu nian <a %(a_security)s>Sekureco</a> paĝon."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Ni serĉas fakulojn pri pagoj por anonimaj komercistoj. Ĉu vi povas helpi nin aldoni pli oportunajn manierojn donaci? PayPal, WeChat, donackartoj. Se vi konas iun, bonvolu kontakti nin."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Ni ĉiam serĉas pli da servila kapacito."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Vi povas helpi raportante dosierajn problemojn, lasante komentojn, kaj kreante listojn rekte en ĉi tiu retejo. Vi ankaŭ povas helpi <a %(a_upload)s>alŝutante pli da libroj</a>, aŭ riparante dosierajn problemojn aŭ formatadon de ekzistantaj libroj."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Krei aŭ helpi prizorgi la Vikipedian paĝon por la Arkivo de Anna en via lingvo."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Ni serĉas meti malgrandajn, gustumajn reklamojn. Se vi ŝatus reklami en Arkivo de Anna, bonvolu sciigi nin."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Ni ŝatus, ke homoj starigu <a %(a_mirrors)s>spegulojn</a>, kaj ni finance subtenos tion."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Por pli ampleksa informo pri kiel volontuli, vidu nian <a %(a_volunteering)s>Volontulado & Recompencoj</a> paĝon."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Kial la malrapidaj elŝutoj estas tiel malrapidaj?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Ni laŭvorte ne havas sufiĉe da rimedoj por doni al ĉiuj en la mondo altrapidajn elŝutojn, kiom ajn ni ŝatus. Se riĉa bonfaranto volus paŝi antaŭen kaj provizi tion por ni, tio estus nekredebla, sed ĝis tiam, ni faras nian plej bonan. Ni estas neprofitcela projekto, kiu apenaŭ povas subteni sin per donacoj."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Tial ni efektivigis du sistemojn por senpagaj elŝutoj, kun niaj partneroj: komunaj serviloj kun malrapidaj elŝutoj, kaj iomete pli rapidaj serviloj kun atendolisto (por redukti la nombron de homoj elŝutantaj samtempe)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Ni ankaŭ havas <a %(a_verification)s>retumilan konfirmon</a> por niaj malrapidaj elŝutoj, ĉar alie robotoj kaj skrapiloj misuzos ilin, farante aferojn eĉ pli malrapidaj por legitimaj uzantoj."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Notu, ke uzante la Tor-Retumilon, vi eble devos agordi viajn sekurecajn agordojn. En la plej malalta el la opcioj, nomata “Normala”, la Cloudflare-turnstila defio sukcesas. En la pli altaj opcioj, nomataj “Pli Sekura” kaj “Plej Sekura”, la defio malsukcesas."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Por grandaj dosieroj foje malrapidaj elŝutoj povas interrompiĝi meze. Ni rekomendas uzi elŝutan administrilon (kiel JDownloader) por aŭtomate rekomenci grandajn elŝutojn."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donaca FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Ĉu membrecoj aŭtomate renovigas?</div> Membrecoj <strong>ne</strong> aŭtomate renovigas. Vi povas partopreni tiel longe aŭ mallonge kiel vi volas."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Ĉu mi povas ĝisdatigi mian membrecon aŭ akiri plurajn membrecojn?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Ĉu vi havas aliajn pagmetodojn?</div> Nuntempe ne. Multaj homoj ne volas, ke arkivoj kiel ĉi tiu ekzistu, do ni devas esti zorgemaj. Se vi povas helpi nin starigi aliajn (pli oportunajn) pagmetodojn sekure, bonvolu kontakti nin ĉe %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Kion signifas la gamoj monate?</div> Vi povas atingi la pli malaltan flankon de gamo aplikante ĉiujn rabatojn, kiel elekti periodon pli longan ol unu monato."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Por kio vi elspezas donacojn?</div> 100%% iras al konservado kaj alirebligo de la monda scio kaj kulturo. Nuntempe ni plejparte elspezas ĝin por serviloj, stokado, kaj bendolarĝo. Neniu mono iras al iuj teamanoj persone."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Ĉu mi povas fari grandan donacon?</div> Tio estus mirinda! Por donacoj super kelkaj mil dolaroj, bonvolu kontakti nin rekte ĉe %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Ĉu mi povas fari donacon sen fariĝi membro?</div> Kompreneble. Ni akceptas donacojn de ajna sumo ĉe ĉi tiu Monero (XMR) adreso: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Kiel mi alŝutas novajn librojn?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternative, vi povas alŝuti ilin al Z-Library <a %(a_upload)s>ĉi tie</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Por malgrandaj alŝutoj (ĝis 10,000 dosieroj) bonvolu alŝuti ilin al ambaŭ %(first)s kaj %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Por nun, ni sugestas alŝuti novajn librojn al la forkoj de Library Genesis. Jen <a %(a_guide)s>utila gvidilo</a>. Notu, ke ambaŭ forkoj, kiujn ni indeksas en ĉi tiu retejo, tiras el ĉi tiu sama alŝuta sistemo."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Por Libgen.li, certigu unue ensaluti en <a %(a_forum)s>ilia forumo</a> kun uzantnomo %(username)s kaj pasvorto %(password)s, kaj poste revenu al ilia <a %(a_upload_page)s>alŝuta paĝo</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Se via retpoŝtadreso ne funkcias en la forumoj de Libgen, ni rekomendas uzi <a %(a_mail)s>Proton Mail</a> (senpaga). Vi ankaŭ povas <a %(a_manual)s>mane peti</a> ke via konto estu aktivigita."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Notu, ke mhut.org blokas certajn IP-gamojn, do VPN eble estos necesa."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Por grandaj alŝutoj (pli ol 10,000 dosieroj) kiuj ne estas akceptitaj de Libgen aŭ Z-Library, bonvolu kontakti nin ĉe %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Por alŝuti akademiajn artikolojn, bonvolu ankaŭ (aldone al Library Genesis) alŝuti al <a %(a_stc_nexus)s>STC Nexus</a>. Ili estas la plej bona ombra biblioteko por novaj artikoloj. Ni ankoraŭ ne integris ilin, sed ni faros iam. Vi povas uzi ilian <a %(a_telegram)s>alŝutan roboton en Telegram</a>, aŭ kontakti la adreson listigitan en ilia fiksita mesaĝo se vi havas tro multajn dosierojn por alŝuti ĉi tiun manieron."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Kiel mi petas librojn?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Nuntempe, ni ne povas akcepti libro-petojn."

#, fuzzy
msgid "page.request.forums"
msgstr "Bonvolu fari viajn petojn en Z-Library aŭ Libgen forumoj."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Ne retpoŝtu al ni viajn libro-petojn."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Ĉu vi kolektas metadatenojn?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Ni ja faras."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Mi elŝutis 1984 de George Orwell, ĉu la polico venos al mia pordo?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Ne tro zorgu, estas multaj homoj elŝutantaj de retejoj ligitaj de ni, kaj estas ekstreme malofte eniri problemojn. Tamen, por resti sekura, ni rekomendas uzi VPN (pagita), aŭ <a %(a_tor)s>Tor</a> (senpaga)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Kiel mi konservas miajn serĉajn agordojn?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Elektu la agordojn kiujn vi ŝatas, lasu la serĉkeston malplena, alklaku “Serĉi”, kaj tiam marku la paĝon uzante la markilon de via retumilo."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Ĉu vi havas poŝtelefonan aplikaĵon?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Ni ne havas oficialan poŝtelefonan aplikaĵon, sed vi povas instali ĉi tiun retejon kiel aplikaĵon."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Alklaku la tri-punktan menuon en la supra dekstra angulo, kaj elektu “Aldoni al Hejma Ekrano”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Alklaku la butonon “Kunhavigi” ĉe la fundo, kaj elektu “Aldoni al Hejma Ekrano”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Ĉu vi havas API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Ni havas unu stabilan JSON API por membroj, por akiri rapidan elŝutan URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentado ene de la JSON mem)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Por aliaj uzoj, kiel ekzemple iterado tra ĉiuj niaj dosieroj, konstruado de personigita serĉo, kaj tiel plu, ni rekomendas <a %(a_generate)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. La krudaj datumoj povas esti mane esploritaj <a %(a_explore)s>per JSON-dosieroj</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Nia kruda torenta listo ankaŭ povas esti elŝutita kiel <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torentaj Oftaj Demandoj"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Mi ŝatus helpi semadon, sed mi ne havas multan diskospacon."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Uzu la <a %(a_list)s>torentan listogeneratoron</a> por generi liston de torentoj kiuj plej bezonas semadon, ene de viaj limoj de stokado."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "La torentoj estas tro malrapidaj; ĉu mi povas elŝuti la datumojn rekte de vi?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Jes, vidu la paĝon <a %(a_llm)s>LLM datenoj</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Ĉu mi povas elŝuti nur subaron de la dosieroj, kiel nur apartan lingvon aŭ temon?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Mallonga respondo: ne facile."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Longa respondo:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Plej multaj torentoj enhavas la dosierojn rekte, kio signifas ke vi povas instrui torentajn klientojn elŝuti nur la bezonatajn dosierojn. Por determini kiujn dosierojn elŝuti, vi povas <a %(a_generate)s>generi</a> niajn metadatenojn, aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. Bedaŭrinde, kelkaj torentaj kolektoj enhavas .zip aŭ .tar dosierojn ĉe la radiko, en kiu kazo vi devas elŝuti la tutan torenton antaŭ ol povi elekti individuajn dosierojn."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Ni tamen havas <a %(a_ideas)s>kelkajn ideojn</a> por la lasta kazo.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Neniuj facilaj iloj por filtri torentojn estas disponeblaj ankoraŭ, sed ni bonvenigas kontribuojn."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Kiel vi traktas duplikatojn en la torentoj?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Ni provas teni minimuman duplikadon aŭ interkovron inter la torentoj en ĉi tiu listo, sed tio ne ĉiam eblas, kaj dependas multe de la politikoj de la fontaj bibliotekoj. Por bibliotekoj kiuj eldonas siajn proprajn torentojn, tio estas ekster niaj manoj. Por torentoj eldonitaj de Arkivo de Anna, ni deduplikiĝas nur bazite sur MD5-hash, kio signifas ke malsamaj versioj de la sama libro ne estas deduplikitaj."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Ĉu mi povas akiri la torentan liston kiel JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ĉu."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Mi ne vidas PDF-ojn aŭ EPUB-ojn en la torentoj, nur binarajn dosierojn? Kion mi faru?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Ĉi tiuj estas fakte PDF-oj kaj EPUB-oj, ili simple ne havas etendaĵon en multaj el niaj torentoj. Estas du lokoj kie vi povas trovi la metadatenojn por torentaj dosieroj, inkluzive de la dosiertipoj/etendaĵoj:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Ĉiu kolekto aŭ eldono havas siajn proprajn metadatenojn. Ekzemple, <a %(a_libgen_nonfic)s>Libgen.rs torentoj</a> havas respondan metadatenan datumbazon gastigitan en la retejo de Libgen.rs. Ni tipe ligas al rilataj metadatenaj rimedoj de ĉiu kolekto <a %(a_datasets)s>paĝo de datenserio</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Ni rekomendas <a %(a_generate)s>generi</a> aŭ <a %(a_download)s>elŝuti</a> niajn ElasticSearch kaj MariaDB datumbazojn. Ĉi tiuj enhavas mapadon por ĉiu registro en la Arkivo de Anna al ĝiaj respondaj torentaj dosieroj (se disponeblaj), sub \"torrent_paths\" en la ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Kial mia torentkliento ne povas malfermi iujn el viaj torentdosieroj / magnetligiloj?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Iuj torentklientoj ne subtenas grandajn pecetgrandecojn, kiujn multaj el niaj torentoj havas (por pli novaj ni ne plu faras tion — eĉ se ĝi estas valida laŭ specifoj!). Do provu alian klienton se vi renkontas ĉi tion, aŭ plendu al la kreintoj de via torentkliento."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Ĉu vi havas programon por respondeca malkovro?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Ni bonvenigas sekurecajn esploristojn serĉi vundeblecojn en niaj sistemoj. Ni estas grandaj subtenantoj de respondeca malkovro. Kontaktu nin <a %(a_contact)s>ĉi tie</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Ni nuntempe ne povas doni cimo-premiojn, krom por vundeblecoj kiuj havas la <a %(a_link)s>potencialon kompromiti nian anonimecon</a>, por kiuj ni ofertas premiojn en la gamo de $10k-50k. Ni ŝatus oferti pli vastan amplekson por cimo-premioj en la estonteco! Bonvolu noti, ke sociaj inĝenieraj atakoj estas ekster la amplekso."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Se vi interesiĝas pri ofenda sekureco kaj volas helpi arkivi la mondan scion kaj kulturon, nepre kontaktu nin. Estas multaj manieroj kiel vi povas helpi."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ĉu estas pli da rimedoj pri la Arkivo de Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blogo de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regulaj ĝisdatigoj"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Programaro de Anna</a> — nia malfermfonta kodo"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduki en la Programaro de Anna</a> — nia traduksistemo"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datenserioj</a> — pri la datumoj"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativaj domajnoj"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Vikipedio</a> — pli pri ni (bonvolu helpi teni ĉi tiun paĝon ĝisdatigita, aŭ krei unu por via propra lingvo!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Kiel mi raportu kopirajtan malobservon?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Ni ne gastigas iujn ajn kopirajtitajn materialojn ĉi tie. Ni estas serĉilo, kaj kiel tia nur indeksas metadatenojn kiuj jam estas publike disponeblaj. Kiam vi elŝutas de ĉi tiuj eksteraj fontoj, ni sugestus kontroli la leĝojn en via jurisdikcio rilate al tio, kio estas permesata. Ni ne respondecas pri enhavo gastigita de aliaj."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Se vi havas plendojn pri tio, kion vi vidas ĉi tie, via plej bona veto estas kontakti la originalan retejon. Ni regule alportas iliajn ŝanĝojn en nian datumbazon. Se vi vere pensas, ke vi havas validan DMCA-plendon, kiun ni devus respondi, bonvolu plenigi la <a %(a_copyright)s>DMCA / Kopirajta plendoformularo</a>. Ni prenas viajn plendojn serioze, kaj respondos al vi kiel eble plej baldaŭ."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Mi malamas kiel vi administras ĉi tiun projekton!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Ni ankaŭ ŝatus rememori ĉiujn, ke nia tuta kodo kaj datumoj estas tute malfermfontaj. Ĉi tio estas unika por projektoj kiel la nia — ni ne konas iun ajn alian projekton kun simile masiva katalogo kiu estas ankaŭ tute malfermfonta. Ni tre bonvenigas iun ajn, kiu pensas ke ni malbone administras nian projekton, preni nian kodon kaj datumojn kaj starigi sian propran ombran bibliotekon! Ni ne diras ĉi tion el malbonvolo aŭ io simila — ni vere pensas ke ĉi tio estus mirinda ĉar ĝi levus la nivelon por ĉiuj, kaj pli bone konservus la heredaĵon de la homaro."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Ĉu vi havas monitoron de funkciado?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Bonvolu vidi <a %(a_href)s>ĉi tiun bonegan projekton</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Kiel mi povas donaci librojn aŭ aliajn fizikajn materialojn?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Bonvolu sendi ilin al la <a %(a_archive)s>Internet Archive</a>. Ili ĝuste konservos ilin."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Kiu estas Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Vi estas Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Kiaj estas viaj plej ŝatataj libroj?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Jen kelkaj libroj kiuj havas specialan signifon por la mondo de ombraj bibliotekoj kaj cifereca konservado:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Vi elĉerpis rapidajn elŝutojn hodiaŭ."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Fariĝu membro por uzi rapidajn elŝutojn."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Ni nun subtenas Amazon-donackartojn, kredit- kaj debetkartojn, kripton, Alipay, kaj WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Plena datumbazo"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libroj, artikoloj, revuoj, bildstrioj, bibliotekaj rekordoj, metadatenoj, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Serĉi"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>paŭzis</a> la alŝutadon de novaj artikoloj."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB estas daŭrigo de Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Rekta aliro al %(count)s akademiaj artikoloj"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Malfermi"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Se vi estas <a %(a_member)s>membro</a>, retumila kontrolo ne estas postulata."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Longtempa arkivo"

#, fuzzy
msgid "page.home.archive.body"
msgstr "La datasets uzataj en Arkivo de Anna estas tute malfermaj, kaj povas esti spegulitaj amase uzante torentojn. <a %(a_datasets)s>Lernu pli…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Vi povas multe helpi per semado de torentoj. <a %(a_torrents)s>Lernu pli…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s semantoj"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s semantoj"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s semantoj"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Ni havas la plej grandan kolekton de altkvalita teksta datumaro en la mondo. <a %(a_llm)s>Lernu pli…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Speguloj: alvoko por volontuloj"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Serĉante volontulojn"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Kiel neprofitcela, malfermfonta projekto, ni ĉiam serĉas homojn por helpi."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Se vi administras alt-riskan anoniman pagprocesoron, bonvolu kontakti nin. Ni ankaŭ serĉas homojn por meti gustumajn malgrandajn reklamojn. Ĉiuj enspezoj iras al niaj konservaj klopodoj."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "La Blogo de Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS elŝutoj"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Ĉiuj elŝutligiloj por ĉi tiu dosiero: <a %(a_main)s>Ĉefa paĝo de dosiero</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Pordego #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(vi eble devos provi plurfoje kun IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Por akiri pli rapidajn elŝutojn kaj eviti la retumilajn kontrolojn, <a %(a_membership)s>iĝu membro</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Por amasa speguligo de nia kolekto, kontrolu la paĝojn <a %(a_datasets)s>Datasets</a> kaj <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-datumoj"

#, fuzzy
msgid "page.llm.intro"
msgstr "Estas bone komprenite, ke LLM-oj prosperas per altkvalitaj datumoj. Ni havas la plej grandan kolekton de libroj, artikoloj, revuoj, ktp en la mondo, kiuj estas iuj el la plej altkvalitaj tekstaj fontoj."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unika skalo kaj gamo"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Nia kolekto enhavas pli ol cent milionojn da dosieroj, inkluzive de akademiaj ĵurnaloj, lernolibroj, kaj revuoj. Ni atingas ĉi tiun skalon kombinante grandajn ekzistantajn deponejojn."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Iuj el niaj fontkolektoj jam estas disponeblaj amase (Sci-Hub, kaj partoj de Libgen). Aliaj fontoj ni mem liberigis. <a %(a_datasets)s>Datasets</a> montras plenan superrigardon."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Nia kolekto inkluzivas milionojn da libroj, artikoloj, kaj revuoj de antaŭ la epoko de e-libroj. Grandaj partoj de ĉi tiu kolekto jam estis OCR-igitaj, kaj jam havas malmulte da interna supermeto."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Kiel ni povas helpi"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Ni kapablas provizi altrapidan aliron al niaj plenaj kolektoj, same kiel al nepublikigitaj kolektoj."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Ĉi tio estas entreprena nivela aliro, kiun ni povas provizi kontraŭ donacoj en la gamo de dekoj da miloj da USD. Ni ankaŭ pretas interŝanĝi ĉi tion kontraŭ altkvalitaj kolektoj, kiujn ni ankoraŭ ne havas."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Ni povas repagi vin se vi kapablas provizi al ni riĉigon de niaj datumoj, kiel:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Forigo de supermeto (deduplikado)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Teksto kaj metadatenoj ekstraktado"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Subtenu longdaŭran arkivadon de homa scio, dum vi ricevas pli bonajn datumojn por via modelo!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontaktu nin</a> por diskuti kiel ni povas kunlabori."

#, fuzzy
msgid "page.login.continue"
msgstr "Daŭrigu"

#, fuzzy
msgid "page.login.please"
msgstr "Bonvolu <a %(a_account)s>ensaluti</a> por vidi ĉi tiun paĝon.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "La Arkivo de Anna estas provizore malŝaltita por prizorgado. Bonvolu reveni post unu horo."

#, fuzzy
msgid "page.metadata.header"
msgstr "Plibonigi metadatenojn"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Vi povas helpi konservadon de libroj plibonigante metadatenojn! Unue, legu la fonon pri metadatenoj en la Arkivo de Anna, kaj poste lernu kiel plibonigi metadatenojn per ligiloj kun Open Library, kaj gajnu senpagan membrecon en la Arkivo de Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Fono"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Kiam vi rigardas libron en la Arkivo de Anna, vi povas vidi diversajn kampojn: titolo, aŭtoro, eldonisto, eldono, jaro, priskribo, dosiernomo, kaj pli. Ĉiuj tiuj informeroj estas nomataj <em>metadatenoj</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Ĉar ni kombinas librojn el diversaj <em>fontaj bibliotekoj</em>, ni montras kiaj ajn metadatenoj estas disponeblaj en tiu fonta biblioteko. Ekzemple, por libro kiun ni ricevis de Library Genesis, ni montros la titolon el la datumbazo de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Foje libro estas ĉeestanta en <em>multoblaj</em> fontaj bibliotekoj, kiuj eble havas malsamajn metadatenajn kampojn. En tiu kazo, ni simple montras la plej longan version de ĉiu kampo, ĉar tiu verŝajne enhavas la plej utilajn informojn! Ni ankoraŭ montros la aliajn kampojn sub la priskribo, ekz. kiel \"alternativa titolo\" (sed nur se ili estas malsamaj)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Ni ankaŭ elprenas <em>kodojn</em> kiel identigiloj kaj klasigiloj el la fonta biblioteko. <em>Identigiloj</em> unike reprezentas specifan eldonon de libro; ekzemploj estas ISBN, DOI, Open Library ID, Google Books ID, aŭ Amazon ID. <em>Klasigiloj</em> grupigas multoblajn similajn librojn; ekzemploj estas Dewey Decimal (DCC), UDC, LCC, RVK, aŭ GOST. Foje ĉi tiuj kodoj estas eksplicite ligitaj en fontaj bibliotekoj, kaj foje ni povas elpreni ilin el la dosiernomo aŭ priskribo (ĉefe ISBN kaj DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Ni povas uzi identigilojn por trovi rekordojn en <em>nur-metadatenaj kolektoj</em>, kiel OpenLibrary, ISBNdb, aŭ WorldCat/OCLC. Estas specifa <em>metadatenoj langeto</em> en nia serĉilo se vi volas foliumi tiujn kolektojn. Ni uzas kongruajn rekordojn por plenigi mankantajn metadatenajn kampojn (ekz. se titolo mankas), aŭ ekz. kiel \"alternativa titolo\" (se ekzistas ekzistanta titolo)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Por vidi ĝuste de kie venis la metadatenoj de libro, vidu la <em>“Teknikaj detaloj” langeton</em> en libro-paĝo. Ĝi havas ligilon al la kruda JSON por tiu libro, kun indikiloj al la kruda JSON de la originalaj rekordoj."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Por pliaj informoj, vidu la jenajn paĝojn: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Serĉo (metadatenoj langeto)</a>, <a %(a_codes)s>Koda Esplorilo</a>, kaj <a %(a_example)s>Ekzempla metadateno JSON</a>. Fine, ĉiuj niaj metadatenoj povas esti <a %(a_generated)s>generitaj</a> aŭ <a %(a_downloaded)s>elŝutitaj</a> kiel ElasticSearch kaj MariaDB datumbazoj."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Ligado kun Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Do se vi renkontas dosieron kun malbonaj metadatenoj, kiel vi devus ripari ĝin? Vi povas iri al la fonta biblioteko kaj sekvi ĝiajn procedurojn por ripari metadatenojn, sed kion fari se dosiero estas ĉeestanta en multoblaj fontaj bibliotekoj?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Estas unu identigilo kiu estas traktata speciale en la Arkivo de Anna. <strong>La annas_archive md5 kampo en Open Library ĉiam superregas ĉiujn aliajn metadatenojn!</strong> Ni unue retrorigardu kaj lernu pri Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library estis fondita en 2006 de Aaron Swartz kun la celo de \"unu retpaĝo por ĉiu libro iam ajn publikigita\". Ĝi estas speco de Vikipedio por libro-metadatenoj: ĉiuj povas redakti ĝin, ĝi estas libere licencita, kaj povas esti elŝutita amase. Ĝi estas libro-datumbazo kiu plej kongruas kun nia misio — fakte, la Arkivo de Anna estis inspirita de la vizio kaj vivo de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Anstataŭ reinventi la radon, ni decidis redirekti niajn volontulojn al Open Library. Se vi vidas libron kun malĝustaj metadatenoj, vi povas helpi en la sekva maniero:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Iru al la <a %(a_openlib)s>retejo de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Trovu la ĝustan libro-rekordon. <strong>AVERTO:</strong> estu certa elekti la ĝustan <strong>eldonon</strong>. En Open Library, vi havas \"verkojn\" kaj \"eldonojn\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "\"Verko\" povus esti \"Harry Potter kaj la Ŝtono de la Saĝuloj\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "\"Eldono\" povus esti:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La unua eldono de 1997 eldonita de Bloomsbery kun 256 paĝoj."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "La poŝlibra eldono de 2003 eldonita de Raincoast Books kun 223 paĝoj."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La pola traduko de 2000 “Harry Potter I Kamie Filozoficzn” de Media Rodzina kun 328 paĝoj."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Ĉiuj tiuj eldonoj havas malsamajn ISBN-ojn kaj malsamajn enhavojn, do certigu elekti la ĝustan!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Redaktu la registron (aŭ kreu ĝin se neniu ekzistas), kaj aldonu kiel eble plej multe da utilaj informoj! Vi estas ĉi tie nun ĉiuokaze, do faru la registron vere mirinda."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sub “ID Numbers” elektu “Anna’s Archive” kaj aldonu la MD5 de la libro el Anna’s Archive. Ĉi tio estas la longa ĉeno de literoj kaj ciferoj post “/md5/” en la URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Provu trovi aliajn dosierojn en Anna’s Archive kiuj ankaŭ kongruas kun ĉi tiu registro, kaj aldonu tiujn ankaŭ. Estonte ni povas grupigi tiujn kiel duplikatojn en la serĉpaĝo de Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Kiam vi finos, notu la URL-on kiun vi ĵus ĝisdatigis. Post kiam vi ĝisdatigis almenaŭ 30 registrojn kun Anna’s Archive MD5-oj, sendu al ni <a %(a_contact)s>retpoŝton</a> kaj sendu al ni la liston. Ni donos al vi senpagan membrecon por Anna’s Archive, por ke vi povu pli facile fari ĉi tiun laboron (kaj kiel dankon pro via helpo). Ĉi tiuj devas esti altkvalitaj redaktoj kiuj aldonas konsiderindajn kvantojn da informoj, alie via peto estos malakceptita. Via peto ankaŭ estos malakceptita se iu el la redaktoj estos nuligita aŭ korektita de Open Library-moderatoroj."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Notu ke ĉi tio funkcias nur por libroj, ne por akademiaj artikoloj aŭ aliaj specoj de dosieroj. Por aliaj specoj de dosieroj ni ankoraŭ rekomendas trovi la fontan bibliotekon. Eble daŭros kelkajn semajnojn por ke ŝanĝoj estu inkluzivitaj en Anna’s Archive, ĉar ni devas elŝuti la plej novan Open Library-datumbazon, kaj regeneri nian serĉindekson."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Speguloj: alvoko por volontuloj"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Por pliigi la rezistecon de la Arkivo de Anna, ni serĉas volontulojn por funkciigi spegulojn."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Ni serĉas ĉi tion:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Vi funkciigas la malfermitkodan bazon de Anna’s Archive, kaj vi regule ĝisdatigas kaj la kodon kaj la datumojn."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Via versio estas klare distingita kiel spegulo, ekz. “La Arkivo de Bob, spegulo de la Arkivo de Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Vi estas preta preni la riskojn asociitajn kun ĉi tiu laboro, kiuj estas signifaj. Vi havas profundan komprenon de la operacia sekureco postulata. La enhavo de <a %(a_shadow)s>ĉi tiuj</a> <a %(a_pirate)s>afiŝoj</a> estas memkomprenebla por vi."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Vi estas preta kontribui al nia <a %(a_codebase)s>kodbazo</a> — en kunlaboro kun nia teamo — por realigi ĉi tion."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Komence ni ne donos al vi aliron al niaj partneraj servilaj elŝutoj, sed se aferoj iros bone, ni povas dividi tion kun vi."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Gastigaj elspezoj"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Ni pretas kovri gastigajn kaj VPN-elspezojn, komence ĝis $200 monate. Ĉi tio estas sufiĉa por baza serĉservilo kaj DMCA-protektita prokurilo."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Ni pagos por gastigado nur post kiam vi havas ĉion aranĝita kaj montris, ke vi kapablas teni la arkivon ĝisdatigita kun ĝisdatigoj. Tio signifas, ke vi devos pagi por la unuaj 1-2 monatoj el via poŝo."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Via tempo ne estos kompensita (kaj ankaŭ nia ne), ĉar ĉi tio estas pura volontula laboro."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se vi signife engaĝiĝos en la disvolviĝo kaj operacioj de nia laboro, ni povas diskuti pri dividado de pli da donacaj enspezoj kun vi, por ke vi povu uzi laŭnecese."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Komenci"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Bonvolu <strong>ne kontakti nin</strong> por peti permeson, aŭ por bazaj demandoj. Agoj parolas pli laŭte ol vortoj! Ĉiuj informoj estas tie, do simple daŭrigu kun starigado de via spegulo."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Bonvolu sendi biletojn aŭ kunfandajn petojn al nia Gitlab kiam vi renkontas problemojn. Ni eble bezonos konstrui iujn spegul-specifajn funkciojn kun vi, kiel rebrandi de “Anna’s Archive” al via retejo nomo, (komence) malŝalti uzantajn kontojn, aŭ ligi reen al nia ĉefa retejo de libro-paĝoj."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Post kiam vi havas vian spegulon funkcianta, bonvolu kontakti nin. Ni ŝatus revizii vian OpSec, kaj post kiam tio estas solida, ni ligos al via spegulo, kaj komencos labori pli proksime kun vi."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Antaŭdankon al ĉiuj, kiuj volas kontribui tiel! Ĝi ne estas por la malfortaj, sed ĝi solidigus la longvivecon de la plej granda vere malferma biblioteko en la homa historio."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Elŝuti de partnera retejo"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Malrapidaj elŝutoj estas disponeblaj nur tra la oficiala retejo. Vizitu %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Malrapidaj elŝutoj ne estas disponeblaj per Cloudflare VPN-oj aŭ alie de Cloudflare IP-adresoj."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Bonvolu atendi <span %(span_countdown)s>%(wait_seconds)s</span> sekundojn por elŝuti ĉi tiun dosieron."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Uzu la sekvan URL por elŝuti: <a %(a_download)s>Elŝuti nun</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Dankon pro via pacienco, tio tenas la retejon senpaga por ĉiuj! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Averto: estis multaj elŝutoj de via IP-adreso en la lastaj 24 horoj. Elŝutoj povus esti pli malrapidaj ol kutime."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Elŝutoj de via IP-adreso en la lastaj 24 horoj: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Se vi uzas VPN, komunan interretan konekton, aŭ via ISP dividas IP-ojn, ĉi tiu averto eble estas pro tio."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Por doni al ĉiuj ŝancon elŝuti dosierojn senpage, vi devas atendi antaŭ ol vi povas elŝuti ĉi tiun dosieron."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Bonvolu daŭrigi foliumi Arkivon de Anna en alia langeto dum vi atendas (se via retumilo subtenas refreŝigadon de fonaj langetoj)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Bonvolu atendi ĝis pluraj elŝutpaĝoj ŝarĝiĝas samtempe (sed bonvolu elŝuti nur unu dosieron samtempe por ĉiu servilo)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Post kiam vi ricevas elŝutligilon, ĝi validas dum pluraj horoj."

#, fuzzy
msgid "layout.index.header.title"
msgstr "La Arkivo de Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rekordo en la Arkivo de Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Elŝuti"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Por subteni la alireblecon kaj longdaŭran konservadon de homa scio, fariĝu <a %(a_donate)s>membro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Kiel bonuso, 🧬&nbsp;SciDB ŝarĝas pli rapide por membroj, sen ajnaj limoj."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Ne funkcias? Provu <a %(a_refresh)s>refreŝigi</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Neniu antaŭrigardo disponebla ankoraŭ. Elŝutu dosieron de <a %(a_path)s>Arkivo de Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB estas daŭrigo de Sci-Hub, kun sia konata interfaco kaj rekta vidado de PDF-oj. Enigu vian DOI por vidi."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Ni havas la tutan kolekton de Sci-Hub, same kiel novajn artikolojn. Plej multaj povas esti viditaj rekte kun konata interfaco, simila al Sci-Hub. Iuj povas esti elŝutitaj per eksteraj fontoj, en kiu kazo ni montras ligojn al tiuj."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Serĉi"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nova serĉo"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Inkluzivu nur"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Ekskludu"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nekontrolita"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Elŝuti"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Revuoartikoloj"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Cifereca Pruntado"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadatenoj"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titolo, aŭtoro, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Serĉi"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Serĉaj agordoj"

#, fuzzy
msgid "page.search.submit"
msgstr "Serĉi"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "La serĉo daŭris tro longe, kio estas ofta por larĝaj demandoj. La filtraj kalkuloj eble ne estas precizaj."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "La serĉo daŭris tro longe, kio signifas, ke vi eble vidos malprecizajn rezultojn. Kelkfoje <a %(a_reload)s>reŝargi</a> la paĝon helpas."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Montri"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Listo"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabelo"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Altnivela"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Serĉu priskribojn kaj metadatenajn komentojn"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Aldoni specifan serĉkampon"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(serĉu specifan kampon)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Publikigita jaro"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Enhavo"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Dosiertipo"

#, fuzzy
msgid "page.search.more"
msgstr "pli…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Aliri"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Fonto"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "skrapita kaj malfermfonta de AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Lingvo"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordigi laŭ"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Plej rilata"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Plej nova"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publikiga jaro)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Plej malnova"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Plej granda"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(dosiergrandeco)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Plej malgranda"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(malfermfonta)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Hazarda"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "La serĉindekso estas ĝisdatigita monate. Ĝi nuntempe inkluzivas enirojn ĝis %(last_data_refresh_date)s. Por pli teknikaj informoj, vidu la <a %(link_open_tag)s>paĝon pri datasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Por esplori la serĉindekson per kodoj, uzu la <a %(a_href)s>Kodan Esplorilon</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Tajpu en la skatolo por serĉi nian katalogon de %(count)s rekte elŝuteblaj dosieroj, kiujn ni <a %(a_preserve)s>konservas por ĉiam</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Fakte, iu ajn povas helpi konservi ĉi tiujn dosierojn semante nian <a %(a_torrents)s>unuiĝintan liston de torentoj</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Ni nuntempe havas la plej ampleksan malferman katalogon de libroj, artikoloj kaj aliaj skribaj verkoj en la mondo. Ni spegulas Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>kaj pli</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Se vi trovas aliajn “ombrolibrojn” kiujn ni devus speguli, aŭ se vi havas demandojn, bonvolu kontakti nin ĉe %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Por DMCA / kopirajtaj asertoj <a %(a_copyright)s>klaku ĉi tie</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Konsilo: uzu klavkombinojn “/” (serĉa fokuso), “enter” (serĉi), “j” (supren), “k” (malsupren), “<” (antaŭa paĝo), “>” (sekva paĝo) por pli rapida navigado."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Ĉu vi serĉas artikolojn?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Tajpu en la skatolo por serĉi nian katalogon de %(count)s akademiaj artikoloj kaj ĵurnalaj artikoloj, kiujn ni <a %(a_preserve)s>konservas por ĉiam</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Tajpu en la skatolo por serĉi dosierojn en ciferecaj pruntbibliotekoj."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Ĉi tiu serĉindekso nuntempe inkluzivas metadatenojn de la Kontrolita Cifereca Pruntbiblioteko de Internet Archive. <a %(a_datasets)s>Pli pri niaj datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Por pli da ciferecaj pruntbibliotekoj, vidu <a %(a_wikipedia)s>Vikipedion</a> kaj la <a %(a_mobileread)s>MobileRead Viki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Tajpu en la skatolo por serĉi metadatenojn de bibliotekoj. Ĉi tio povas esti utila kiam <a %(a_request)s>petante dosieron</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Ĉi tiu serĉindekso nuntempe inkluzivas metadatenojn de diversaj metadatenfontoj. <a %(a_datasets)s>Pli pri niaj datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Por metadatenoj, ni montras la originalajn rekordojn. Ni ne faras ajnan kunfandadon de rekordoj."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Estas multaj, multaj fontoj de metadatenoj por skribaj verkoj ĉirkaŭ la mondo. <a %(a_wikipedia)s>Ĉi tiu Vikipedia paĝo</a> estas bona komenco, sed se vi konas aliajn bonajn listojn, bonvolu sciigi nin."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Tajpu en la skatolo por serĉi."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Ĉi tiuj estas metadatenaj rekordoj, <span %(classname)s>ne</span> elŝuteblaj dosieroj."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Eraro dum serĉado."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Provu <a %(a_reload)s>reŝargi la paĝon</a>. Se la problemo persistas, bonvolu retpoŝti al ni ĉe %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Neniuj dosieroj trovitaj.</span> Provu malpli aŭ malsamajn serĉvortojn kaj filtrilojn."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Foje ĉi tio okazas malĝuste kiam la serĉa servilo estas malrapida. En tiaj kazoj, <a %(a_attrs)s>reŝargi</a> povas helpi."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Ni trovis kongruojn en: %(in)s. Vi povas referenci la URL-on trovitan tie kiam <a %(a_request)s>petante dosieron</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Revuo Artikoloj (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Cifereca Pruntedonado (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadatenoj (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Rezultoj %(from)s-%(to)s (%(total)s entute)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ partaj kongruoj"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d partaj kongruoj"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Volontulado & Premioj"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "La Arkivo de Anna dependas de volontuloj kiel vi. Ni bonvenigas ĉiujn nivelojn de engaĝiĝo, kaj havas du ĉefajn kategoriojn de helpo, kiujn ni serĉas:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Luma volontula laboro:</span> se vi povas nur dediĉi kelkajn horojn ĉi tie kaj tie, ankoraŭ estas multaj manieroj, kiel vi povas helpi. Ni rekompencas konsekvencajn volontulojn per <span %(bold)s>🤝 membrecoj al la Arkivo de Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Peza volontula laboro (USD$50-USD$5,000 rekompencoj):</span> se vi povas dediĉi multan tempon kaj/aŭ rimedojn al nia misio, ni ŝatus labori pli proksime kun vi. Fine vi povas aliĝi al la interna teamo. Kvankam ni havas striktan buĝeton, ni povas doni <span %(bold)s>💰 monajn rekompencojn</span> por la plej intensa laboro."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Se vi ne povas volontuli vian tempon, vi ankoraŭ povas multe helpi nin per <a %(a_donate)s>donacado de mono</a>, <a %(a_torrents)s>semajdo de niaj torentoj</a>, <a %(a_uploading)s>alŝutado de libroj</a>, aŭ <a %(a_help)s>rakontado al viaj amikoj pri la Arkivo de Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Kompanioj:</span> ni ofertas altrapidan rektan aliron al niaj kolektoj kontraŭ entreprena donaco aŭ interŝanĝo por novaj kolektoj (ekz. novaj skanaĵoj, OCR’itaj datasets, riĉigado de niaj datumoj). <a %(a_contact)s>Kontaktu nin</a> se tio estas vi. Vidu ankaŭ nian <a %(a_llm)s>LLM paĝon</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Luma volontulado"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Se vi havas kelkajn horojn por ŝpari, vi povas helpi en diversaj manieroj. Nepre aliĝu al la <a %(a_telegram)s>volontula babilejo en Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Kiel signo de aprezo, ni kutime donas 6 monatojn de “Bonŝanca Bibliotekisto” por bazaj mejloŝtonoj, kaj pli por daŭra volontula laboro. Ĉiuj mejloŝtonoj postulas altkvalitan laboron — malzorgema laboro pli damaĝas nin ol helpas kaj ni malakceptos ĝin. Bonvolu <a %(a_contact)s>retpoŝti nin</a> kiam vi atingas mejloŝtonon."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tasko"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Mejloŝtono"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Disvastigante la vorton pri la Arkivo de Anna. Ekzemple, rekomendante librojn en AA, ligante al niaj blogaĵoj, aŭ ĝenerale direktante homojn al nia retejo."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s ligiloj aŭ ekrankopioj."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Ĉi tiuj devus montri vin informante iun pri la Arkivo de Anna, kaj ilin dankante vin."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Plibonigi metadatumojn per <a %(a_metadata)s>ligado</a> kun Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Vi povas uzi la <a %(a_list)s>liston de hazardaj metadata problemoj</a> kiel deirpunkton."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Certiĝu lasi komenton pri problemoj, kiujn vi riparas, por ke aliaj ne duobligu vian laboron."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s ligiloj de rekordoj, kiujn vi plibonigis."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Tradukado</a> de la retejo."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Plene traduki lingvon (se ĝi ne estis preskaŭ kompletigita jam)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Plibonigi la Vikipedian paĝon por la Arkivo de Anna en via lingvo. Inkluzivu informojn de la Vikipedia paĝo de AA en aliaj lingvoj, kaj de nia retejo kaj blogo. Aldonu referencojn al AA en aliaj rilataj paĝoj."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Ligo al redakta historio montranta, ke vi faris signifajn kontribuojn."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Plenumi libro- (aŭ artikolo, ktp) petojn en la forumoj de Z-Library aŭ Library Genesis. Ni ne havas nian propran libropetan sistemon, sed ni spegulas tiujn bibliotekojn, do plibonigi ilin plibonigas ankaŭ la Arĥivon de Anna."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s ligiloj aŭ ekrankopioj de petoj, kiujn vi plenumis."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Malgrandaj taskoj afiŝitaj en nia <a %(a_telegram)s>volontula babilejo en Telegram</a>. Kutime por membreco, foje por malgrandaj rekompencoj."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Malgrandaj taskoj afiŝitaj en nia volontula babilejo."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Dependas de la tasko."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Rekompencoj"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Ni ĉiam serĉas homojn kun solidaj programaj aŭ ofensivaj sekurecaj kapabloj por engaĝiĝi. Vi povas fari gravan kontribuon al konservado de la heredaĵo de la homaro."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Kiel dankon, ni donacas membrecon por solidaj kontribuoj. Kiel grandan dankon, ni donacas monajn rekompencojn por precipe gravaj kaj malfacilaj taskoj. Ĉi tio ne devus esti rigardata kiel anstataŭaĵo por laboro, sed ĝi estas ekstra instigo kaj povas helpi kun okazintaj kostoj."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Plejparto de nia kodo estas malfermfonta, kaj ni petos tion ankaŭ de via kodo kiam ni atribuas la rekompencon. Estas kelkaj esceptoj, kiujn ni povas diskuti individue."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Rekompencoj estas atribuitaj al la unua persono, kiu kompletigas taskon. Bonvolu komenti pri rekompenca bileto por informi aliajn, ke vi laboras pri io, tiel ke aliaj povas atendi aŭ kontakti vin por kunlabori. Sed estu konscia, ke aliaj ankoraŭ estas liberaj labori pri ĝi kaj provi superi vin. Tamen, ni ne atribuas rekompencojn por malzorga laboro. Se du altkvalitaj submetoj estas faritaj proksime unu al la alia (ene de tago aŭ du), ni eble elektos atribui rekompencojn al ambaŭ, laŭ nia diskreteco, ekzemple 100%% por la unua submeto kaj 50%% por la dua submeto (do 150%% entute)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Por la pli grandaj rekompencoj (precipe skrapaj rekompencoj), bonvolu kontakti nin kiam vi kompletigis ~5%% de ĝi, kaj vi estas certa, ke via metodo skaliĝos al la plena mejloŝtono. Vi devos dividi vian metodon kun ni por ke ni povu doni reagojn. Ankaŭ, tiel ni povas decidi kion fari se estas pluraj homoj proksimiĝantaj al rekompenco, kiel ekzemple potenciale atribui ĝin al pluraj homoj, instigi homojn kunlabori, ktp."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVERTO: la alt-rekompencaj taskoj estas <span %(bold)s>malfacilaj</span> — eble estas saĝe komenci kun pli facilaj."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Iru al nia <a %(a_gitlab)s>Gitlab-issues-listo</a> kaj ordigu laŭ “Etikeda prioritato”. Ĉi tio montras proksimume la ordon de taskoj, kiujn ni zorgas. Taskoj sen eksplicitaj rekompencoj ankoraŭ estas elekteblaj por membreco, precipe tiuj markitaj “Akceptita” kaj “La plej ŝatata de Anna”. Vi eble volas komenci kun “Komenca projekto”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Ĝisdatigoj pri <a %(wikipedia_annas_archive)s>Arkivo de Anna</a>, la plej granda vere malferma biblioteko en la homa historio."

#, fuzzy
msgid "layout.index.title"
msgstr "La Arkivo de Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "La plej granda malfermfonta malfermdatuma biblioteko en la mondo. Speguloj de Sci-Hub, Library Genesis, Z-Library, kaj pli."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Serĉu en la Arkivo de Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "La Arkivo de Anna bezonas vian helpon!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Multaj provas faligi nin, sed ni kontraŭbatalas."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se vi donacas nun, vi ricevas <strong>duoblan</strong> la nombron de rapidaj elŝutoj."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valida ĝis la fino de ĉi tiu monato."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donaci"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Savi homan scion: bonega feria donaco!"

msgid "layout.index.header.banner.surprise"
msgstr "Surprizu amaton, donu al ili konton kun membreco."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Por pligrandigi la rezistecon de la Arkivo de Anna, ni serĉas volontulojn por prizorgi spegulojn."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "La perfekta donaco por Valentintago!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Ni havas novan donacmetodon disponeblan: %(method_name)s. Bonvolu konsideri %(donate_link_open_tag)sdonaci</a> — ne estas malmultekoste funkciigi ĉi tiun retejon, kaj via donaco vere faras diferencon. Tre dankon."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Ni organizas monkolekton por <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">subteni</a> la plej grandan ombran bibliotekon de bildstrioj en la mondo. Dankon pro via subteno! <a href=\"/donate\">Donaci.</a> Se vi ne povas donaci, konsideru subteni nin dirante al viaj amikoj, kaj sekvante nin en <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, aŭ <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Lastatempaj elŝutoj:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Serĉi"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Oftaj Demandoj"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Plibonigi metadatenojn"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Volontulado & Recompencoj"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torentoj"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktiveco"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Koda Esploristo"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM-datenoj"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Hejmo"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "La Arkivo de Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduki ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Ensaluti / Registri"

msgid "layout.index.header.nav.account"
msgstr "Konto"

msgid "layout.index.footer.list1.header"
msgstr "La arkivo de Anna"

msgid "layout.index.footer.list2.header"
msgstr "Restu en kontakto"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / asertoj pri kopirajto"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Altnivela"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sekureco"

msgid "layout.index.footer.list3.header"
msgstr "Alternativoj"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "neafiliita"

msgid "page.search.results.issues"
msgstr "❌ Ĉi tiu dosiero eble havas problemojn."

msgid "page.search.results.fast_download"
msgstr "Rapida elŝuto"

msgid "page.donate.copy"
msgstr "kopio"

msgid "page.donate.copied"
msgstr "kopiita!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Antaŭa"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Sekva"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr ""

#~ msgid "layout.index.header.nav.home"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_tor"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_tor_extra"
#~ msgstr ""

#~ msgid "page.isbn.title"
#~ msgstr ""

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr ""

#~ msgid "page.isbn.invalid.header"
#~ msgstr ""

#~ msgid "page.isbn.invalid.text"
#~ msgstr ""

#~ msgid "page.isbn.results.text"
#~ msgstr ""

#~ msgid "page.isbn.results.none"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr ""

#~ msgid "page.donate.header.text3"
#~ msgstr ""

#~ msgid "page.donate.buttons.one_time"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr ""

#~ msgid "page.donate.text_thank_you"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr ""

#~ msgid "page.donate.login"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.home"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.about"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Poŝtelefona aplikaĵo"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.software"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traduki"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr ""

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr ""

#~ msgid "page.doi.breadcrumbs"
#~ msgstr ""

#~ msgid "page.doi.invalid.header"
#~ msgstr ""

#~ msgid "page.doi.invalid.text"
#~ msgstr ""

#~ msgid "page.doi.box.header"
#~ msgstr ""

#~ msgid "page.doi.box.canonical_url"
#~ msgstr ""

#~ msgid "page.doi.box.scihub"
#~ msgstr ""

#~ msgid "page.doi.results.text"
#~ msgstr ""

#~ msgid "page.doi.results.none"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr ""

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr ""

#~ msgid "page.about.header"
#~ msgstr ""

#~ msgid "page.home.search.header"
#~ msgstr ""

#~ msgid "page.home.search.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr ""

#~ msgid "page.home.explore.header"
#~ msgstr ""

#~ msgid "page.home.explore.intro"
#~ msgstr ""

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Poŝtelefona aplikaĵo"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Petu librojn"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr ""

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr ""

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "nur ĉi-monate!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>paŭzis</a> alŝutadon de novaj artikoloj."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Elektu pagmanieron. Ni donas rabatojn por kriptaj pagoj %(bitcoin_icon)s, ĉar ni havas (tre) malpli da kotizoj."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Elektu pagmanieron. Ni nuntempe nur havas kriptajn pagojn %(bitcoin_icon)s, ĉar tradiciaj pagprocesoroj rifuzas kunlabori kun ni."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Ni ne povas subteni kredit-/debetkartojn rekte, ĉar bankoj ne volas kunlabori kun ni. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tamen, ekzistas pluraj manieroj uzi kredit-/debetkartojn, uzante niajn aliajn pagmetodojn:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Malrapidaj kaj eksteraj elŝutoj"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Elŝutoj"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se vi uzas kripton por la unua fojo, ni sugestas uzi %(option1)s, %(option2)s, aŭ %(option3)s por aĉeti kaj donaci Bitcoin (la originala kaj plej uzata kriptovaluto)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 ligiloj de rekordoj, kiujn vi plibonigis."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 ligiloj aŭ ekrankopioj."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 ligiloj aŭ ekrankopioj de petoj, kiujn vi plenumis."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se vi interesiĝas pri spegulado de ĉi tiuj datasets por <a %(a_faq)s>arkivado</a> aŭ <a %(a_llm)s>LLM-trejnado</a> celoj, bonvolu kontakti nin."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se vi interesiĝas pri speguli ĉi tiun datenserion por <a %(a_archival)s>arkivado</a> aŭ <a %(a_llm)s>LLM-trejnado</a> celoj, bonvolu kontakti nin."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Ĉefa retejo"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN-landoinformo"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se vi interesiĝas speguli ĉi tiun datenserion por <a %(a_archival)s>arkivaj</a> aŭ <a %(a_llm)s>LLM-trejnaj</a> celoj, bonvolu kontakti nin."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "La Internacia ISBN-Agentejo regule publikigas la intervalojn, kiujn ĝi asignis al naciaj ISBN-agentejoj. El tio ni povas derivi al kiu lando, regiono aŭ lingvogrupo apartenas ĉi tiu ISBN. Ni nuntempe uzas ĉi tiujn datumojn nerekte, per la <a %(a_isbnlib)s>isbnlib</a> Python-biblioteko."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Rimedoj"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Laste ĝisdatigita: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN-retejo"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadatumoj"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Ekskludante “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Nia inspiro por kolekti metadatenojn estas la celo de Aaron Swartz \"unu retpaĝo por ĉiu libro iam ajn publikigita\", por kiu li kreis <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Tiu projekto sukcesis, sed nia unika pozicio permesas al ni akiri metadatenojn, kiujn ili ne povas."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Alia inspiro estis nia deziro scii <a %(a_blog)s>kiom da libroj ekzistas en la mondo</a>, por ke ni povu kalkuli kiom da libroj ni ankoraŭ devas savi."

#~ msgid "page.partner_download.text1"
#~ msgstr "Por doni al ĉiuj ŝancon elŝuti dosierojn senpage, vi devas atendi <strong>%(wait_seconds)s sekundojn</strong> antaŭ ol vi povas elŝuti ĉi tiun dosieron."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Aŭtomate refreŝigi paĝon. Se vi maltrafas la elŝutan fenestron, la tempigilo rekomencos, do aŭtomata refreŝigo estas rekomendata."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Elŝuti nun"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Uzu interretajn ilojn por konverti inter formatoj. Ekzemple, por konverti inter epub kaj pdf, uzu <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: elŝutu la dosieron (pdf aŭ epub estas subtenataj), poste <a %(a_kindle)s>sendu ĝin al Kindle</a> uzante reton, aplikaĵon aŭ retpoŝton. Utilaj iloj: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Subtenu aŭtorojn: Se vi ŝatas ĉi tion kaj povas permesi ĝin, konsideru aĉeti la originalon, aŭ subteni la aŭtorojn rekte."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Subtenu bibliotekojn: Se ĉi tio estas disponebla en via loka biblioteko, konsideru pruntepreni ĝin senpage tie."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Ne disponebla rekte en amaso, nur en duonamaso malantaŭ pagmuro"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s La Arkivo de Anna administras kolekton de <a %(isbndb)s>ISBNdb-metadatenoj</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb estas kompanio, kiu skrapas diversajn retajn librovendejojn por trovi ISBN-metadatumojn. La Arkivo de Anna faris sekurkopiojn de la ISBNdb-librometadatumoj. Ĉi tiuj metadatumoj estas disponeblaj tra la Arkivo de Anna (kvankam nuntempe ne en serĉo, krom se vi eksplicite serĉas ISBN-numeron)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Por teknikaj detaloj, vidu sube. Iam ni povas uzi ĝin por determini kiuj libroj ankoraŭ mankas en ombraj bibliotekoj, por prioritati kiujn librojn trovi kaj/aŭ skani."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nia bloga afiŝo pri ĉi tiuj datumoj"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb-skrapo"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Nuntempe ni havas unu torenton, kiu enhavas 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> dosieron (20GB malzipita): “isbndb_2022_09.jsonl.gz”. Por importi “.jsonl”-dosieron en PostgreSQL, vi povas uzi ion kiel <a %(a_script)s>ĉi tiu skripto</a>. Vi eĉ povas pipo ĝin rekte uzante ion kiel %(example_code)s por ke ĝi malzipiĝu dumflue."

#~ msgid "page.donate.wait"
#~ msgstr "Bonvolu atendi almenaŭ <span %(span_hours)s>du horojn</span> (kaj refreŝigi ĉi tiun paĝon) antaŭ ol kontakti nin."

#~ msgid "page.codes.search_archive"
#~ msgstr "Serĉu en la Arkivo de Anna por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donacu uzante Alipay aŭ WeChat. Vi povas elekti inter ĉi tiuj sur la sekva paĝo."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Disvastigi la vorton pri la Arkivo de Anna en sociaj retoj kaj interretaj forumoj, rekomendante librojn aŭ listojn en AA, aŭ respondante demandojn."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fikcia kolekto diverĝis sed ankoraŭ havas <a %(libgenli)s>torentojn</a>, kvankam ne ĝisdatigitaj ekde 2022 (ni ja havas rektajn elŝutojn)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Arkivo de Anna kaj Libgen.li kunlabore administras kolektojn de <a %(comics)s>bildstrioj</a> kaj <a %(magazines)s>revuoj</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Neniuj torentoj por rusaj fikciaj kaj normaj dokumentaj kolektoj."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Ne estas disponeblaj torentoj por la aldona enhavo. La torentoj, kiuj estas en la retejo Libgen.li, estas speguloj de aliaj torentoj listigitaj ĉi tie. La sola escepto estas fikciaj torentoj komencantaj je %(fiction_starting_point)s. La komiksoj kaj magazinoj torentoj estas publikigitaj kiel kunlaboro inter la Arkivo de Anna kaj Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "De kolekto <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> preciza origino neklara. Parte de the-eye.eu, parte de aliaj fontoj."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

