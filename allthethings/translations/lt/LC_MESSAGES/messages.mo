��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b N  �d M  g +   Oh 
  {h �  �i �  dk �  Cm   �n X   �o t   @p F   �p e   �p �  bq G  s �   [t �  Gu �   :w   .x �  2z �  | �   �} �  �~ �  �� ;   ]� �   �� U   t� �  ʃ !   ȅ P  � F   ;� !   ��    ��    �� @   Ј $   �    6� 9   J� '   �� -   ��    ډ C   � ;  5� 
  q� H   � >   Ȍ 	   � 
   �    � 
   :�    E� 
   ^� 	   l�    v�    �� #   ��    ��    �� 	   э 
   ۍ    �    �� $   �    9� P  X� c  �� �   
� +   �� "  ͒ �   �   �� �   �� !   S� ~   u� !   �� �   � &   ×   � !   � k  � 0   � M   ��    �� 4  � 4  9� �  n� 4  4�    i� A   z� Y   �� �   � ,   ȡ (   �� j   � :   �� X   Ģ 8   �    V� �   \� �  ߣ -   �� �   ƥ �   T� A   /� #  q� J   ��   � �   � �   �� [   .� �   �� �   `� F   �� �   E� �   2� M   � =   A� y   �    �� �   � &  �� F   ܱ    #� �   +� �   ϲ    �� {  �� �   /� �   ڵ �  ڶ �   ^� �  D�   =� G   L� d   �� ^   �� o   X� [   Ⱦ O   $� -   t� �   �� h   -� N   ��    �� l   �� W   b� �   �� �   o�    � �   �   � K  (� 7  t� �   �� �   7� '  %� �   M� �   �� �   �� x  0� �   �� �   F� 
   1� �   <� �   �� �   �� y  x� }   �� �   p� �   �� �  �� o   k� T  �� '  0�    X� j   w� �   �� X   �� i  �� �   C� k   �� u   d� "   �� .  �� �   ,� r  ��    j� _   �� �   �� �   ��   d� �   �� �  _� �  5� (   �� p  �� n  m�   �� �  �� )  �� �   �� f   �� |   !�    �� �   ��   v� j  ��    � �   � �   ��    t� w  �� X  �� �   W� 	   H� �   R� D    �   J �  - �    N    
   V �  d c  �    J �   [   Y	   p
 �   � a  v
 U   � >   .   m 2   q    � ,  �   � K  � +   / F   [   � ~   � �   & f   � q       �    � �  � �   ? �   � a    �   � -   l �   � �   �    ~    � 2   � F   � ?     =   W  ]   �  �   �  !   {! <   �! Z   �! P   5" .   �" ;   �" +   �" v   #    �# )   �#   �#   �$ �   �%   �& �   �' �   �( "  D)    g* �   �* �   J+ �   �+ b  �, y  �-   i/ �  q1 �  &3 F   �4 6   �4 $   05 �   U5   6 �  8   �9 N  �:   �;   
= �   &? 4   &@   [@ *  tA R  �B    �C �   D   �D �   �E .  uF �   �G    �H )   �H ]   �H 	   I �  I �  �K $  <M �   aO �   P    �P �   �P �   �Q F   �R n   %S   �S �   �T t   �U �  V q   �W #  X    (Y �  ?Y �   #[ �  �[ d  �] �  ` �   �a    �b �   �b �  �c    Oe m  ce �  �f $  �h   �j �   �l %  �m F   �n �  3o �   q �   �q ?   hr Y   �r 	   s :   s !   Gs )   is &   �s    �s �   �s �  jt �  Uw $  $y �   I{    | �  *| E  �~ K  >� \  �� :  � ?  "�    b� �   j� 	   f�    p� �  �� 1  3� 
  e� ^  s�    ґ �  � �  �� �  m� �  ,� �  � !  � c  � #   y� �   �� j   ]� �  Ƞ �  W� �   � �   �� �   +� Z  Ѧ    ,� �   D� H   �� I   A�    �� 6   �� ;   ܩ P  � �  i� �  �� 9   �� x  �� �   6�    � �   �� N   �� s   �� �   s� 0   A� ^   r�    Ѷ S  � *  :� �  e� ~  � |   ��    � r   3�   ��    �� �   �� }  E� �   �� 2   ]� -   �� S  �� +   �   >� p  �� �  /�     � y   � �   ��   )� {  :�    ��   �� /   �� �   � �   �� 
  �� �   �� S   p� +   �� �   �� 4   �� x  � M  �� �   �� �   �� U   �� 7   �� �   � C  �� p  �� �  b� �  =� ?   � �   Q� 	  $�   .� �   N� �  &� �  � +   �� �  �� %   �� �  �� J   ��    �� �  �� �  �� �  �� �   �   m�   z� (  }� �   �� �  W� �  !� o   �    E d  G <   � ~   �    h    �     A   �   R A   �   7	 �  U
 @   �  O
 �  : �    �  �   P    e �       I q  R    � &   �    
     0   %    V    q    �    �    �    � 
   � 
   �    �    � 	    &       2 	   7    A 
   H �   S Z   6 (   �    � #   � )   � )    (   C '   l    �    �    �    �    �    � 	           2    7 ,   M $   z    � ,   � .   � '    -   6    d *   t :   �    � V   � H   M    � \   � W   �    P    l     |    �    �    �    �        $    ,    C    P 
   f 	   t 
   ~    � *   �    �    � 	   �    � 	   � $   �           	   $     .     >     J     i     q     �     �     �  	   �     �  *   �     
!    ! %   !    B!    K! 	   h!    r!    �!    �!    �! m   �! �   -" e   �" �   A# �   $ y   �% 
   u&    �& 
   �&    �&    �&    �&    �& '   �& T   ' 6   `' ^   �' #   �' 6   ( %   Q( c   w( h   �( �   D) [   * B   v*    �*    �*    �*    �* 
   �*    +    +    -+    2+    I+    \+    c+    u+    y+    �+    �+    �+ 
   �+    �+    �+    �+    �+ 
   �+    ,    ,   8,    E- 	   J-    T- %   Z-    �- H   �- _   �- )   0. .   Z. 9   �.    �.    �.    �. m   �.    D/    J/ 3   Y/ t   �/    0    0 �   )0 %   �0 (  �0 `   �3 �   ^4 �   �4 �   ~5 0   ^6   �6 t   �7 >  8 �   G9    .: 
   J: K   U: M   �: Z   �: [   J; U   �; C   �; \   @< "   �< /   �<    �<    �< ]   = &   t=    �=    �=    �=    �= x   �= 
   U> +   c> ]   �>    �>    ? S   ? R   p? �  �?    qA    �A �   �A 
   dB    oB 	   xB    �B    �B *   �B g  �B    =D    \D    hD    yD G  �D (   �E 
   �E 	   �E �   F    �F    �F "   �F    �F    �F $   �F �   !G    �G    �G T   �G    2H    QH    gH     pH <   �H [   �H    *I 0   =I �   nI h   J W   �J    �J 	  �J B   �K    BL 3   VL    �L �   �L �   gM    N I   7N �   �N �   4O    P $   4P    YP �  nP (   R !   *R    LR #   kR "   �R �   �R &   NS     uS +   �S <   �S 
   �S    
T    %T 3   DT i  xT t  �V �  WX B   <Z ?   Z    �Z 2   �Z -  �Z �   -\ �   ] !   �] �   �] �  �^ *   �`    �`   �` b  �b    Sd    `d >   od 
   �d 0  �d    �e G  f �  Kg �   �h    �i y   �i 8   aj     �j `   �j 5  k   Rl �   Vm �  �m z   �o   p T   *q �   q �   &r O   �r �   )s 5   �s /   t    Jt    ]t    jt !   t    �t W   �t 0   u 	   Ju +   Tu   �u 7   �v �   �v �   �w {   :x /   �x +   �x    y    /y )   Fy .   py 2   �y +   �y �   �y .   �z �   {    �{ �   �{ �   �|    }   �} �   �~ 1   � �   � 	   J�   T� m   t�    � �  ��    ��    ��    Ѓ    � '   	�    1� 	   7� D   A� �   �� �   0� 	   �    %�    .� �   N� l  � �   �� �   @�    ʉ    �    �    �    .�    @�    U� A   ]� -   �� �  ͊ ^   ��    � s   � g   v� F   ލ y   %� N   �� L   �    ;� Z   B� @   �� �   ޏ M   k� F   ��     � �   � n   
� G   |�    Ē V   D� <   ��    ؓ D   � r   *� �   �� :   -� �   h� 	    �    *� P   K� Y   �� �   ��    ��   �� �   ��    {�    ��    ��    ��   �� 8   ޜ [   � �   s� �   =� �   � �   �� �   z� �   � �   �� W   x�   Т e   � f  G� �   �� �   ]� 
   H� 
   V� 
   d� �   r� 
   � 
   � C   !� S   e� �   �� 
   �� a   �� H   � {   O� <   ˪ x   � \   �� 
   ޫ �   � 
   s� 
   �� 
   �� .  �� }   ̮ �  J�    ��    �    � �   �    α 3   �   � �   5� '   ֳ    ��    � :   &� <   a� 9   �� '   ش '    � �   (� "   � �  /� �   �� �   �� X   >� �   ��   <� �  C� E  � �   \� �   �� �   ��    � _  0�    �� p  �� �   �   �    � 9  �� �   �� �   �� �   0� *   �� 4   ��    � /   )�    Y�    e�    {� �   ��    t� i   �� (   ��    �    �    )�    5� s   T� X   �� E   !� �   g� *   d�    �� 	   ��    �� $   ��    ��    �� 
   �    �    �    )�    6�    B� 4   O� �   ��     �    1�    K� 
   W�    e� 
   r�    �� 
   ��    ��    �� ,   �� z   ��    s� ,   �� Y   �� �   � �   �� �   ]� >  � �   N�   	�    � �   )� 9   �� 4   �� B   %� �   h� �   � =   �� o   �    �� m   �� r   � �   ��    $�     +�    L�    d�    �� '   ��    �� *   ��    ��    ��    � $   %�    J�    j�    ��    ��    ��    ��    ��    ��    �� #   �� -   � �   @� t   �� "   6� $   Y� q   ~� V   ��    G� -   �� /   �� 4   %� :   Z� >   �� /   �� �   � �   �� *  ��    �� =   � �   @� 0   �� �   
� �   �� �   �� ?   ?� #   � �   �� �   F� [   �� -   S� �   �� �   �    �� 0   ��    
� 7   $� q   \� q   ��    @�    H�    Q�    X� �   v� 3   � &   9� :   `� .   ��    �� !   �� ?   
�     J� r   k� ;   �� �   � [   � N   c� �   �� ?   o�     ��    �� !   ��     � !   3�     U� !   v� -   �� 9   �� 8   � R   9� 3   �� B   �� )   �    -� z   4� q   �� �   !� �   �� 	   �� �   �� &   ^�    �� �   ��    p� (   �� J   �� 8   �� n   8� W   �� Z   ��    Z� !   z� �   �� ?   J� $   �� K   ��   �� @     {   S  `   �  \   0 {   � '   	 =   1 i   o    � v   � )   k �   � 5   L :   �    � $   � �   � U   � Y     �   z W    9   o �   � �   ; 	   �    � T   � R   8	    �	    �	    �	    �	    �	 .   �	 �   +
 l   �
 %   + !   Q ,   s 9   � -   � z    *   � �   � a   7
 #   �
 o   �
 �   - +   � U       t M   � 6   �    
 p   ) z   � 2    H   H    � 3   � T   �    + {   ? ,   �    �    � V    �   n    ;    T    m A   �    � Z   � >   < �   { �       � >   � �   � !   � �   � Z   | $   � K   �   H    \    e    g    i :   o ?   � I   �    4    M    \ "   a E   � B   � 	   
 R    C   j    � #   � &   � (       : T   K �   � B   �    �    � �   � M  � c   �     I! P  X! �  �" 1   =$ w   o$ %   �$ X  
% ,   f' n   �'    (    ( �  ,(    �) `   * �   h* |   �* i   x+ #   �+ \   , 7   c,    �, z   �, Q   5- :   �- Y   �- 0   . T   M. v   �. �   / %   �/ �   �/ �  �0 �   %2 *   �2 :  �2   *4 �   05 *  �5 �   '7 '   �7 '   8 �   48 :   �8 �  ,9 ^   ; B   y;    �;    �; �  �;    �= �   �= O  �> &  �? +  A 8   /B L   hB S   �B %   	C -   /C E   ]C j   �C 
   D    D ?   8D ,   xD    �D A   �D l   �D :   lE    �E k   �E �   F �   �F    �G    �G    �G Q   �G a   $H `   �H �   �H {   �I    1J (   CJ �   lJ 
   NK �   \K �  �K   �N L   �O %   �O    P 	   $P    .P D   2P :   wP �   �P �   ?Q Z   3R    �R    �R '   �R    �R k   �R    gS E   zS 
   �S :   �S %   T    ,T 	   AT a   KT 	   �T    �T '   �T #   �T    U n   U �   �U Y   VV k   �V V   W �   sW    EX    RX �   pX �   %Y �   �Y    �Z q   �Z R   [ T   r[ `   �[ c   (\ X   �\    �\ \    ]    ]]    s]    �]    �]    �]    �]    �] !   ^    )^ 
   =^ (   H^ /   q^ /   �^ !   �^ 4   �^ ,   (_ 0   U_    �_    �_ @   �_     �_ 	   ` 8    ` 0   Y` `   �` 3   �`    a    5a 1   Fa (   xa    �a \   �a 7   b L   Ob �   �b    Rc    lc    �c    �c 2   �c 	   �c    �c "   d �   9d    �d "   �d    e 
   
e 	   e 4   e    Te �   re    Of '   _f "   �f (   �f    �f    �f     g "   2g &   Ug 8   |g (   �g    �g b   �g 0   Hh    yh    �h /   �h S   �h #   (i +   Li !   xi x   �i [   j D   oj    �j    �j 	   �j    �j    �j    k �  3k �   �l    [m    sm %   wm    �m 3   �m    �m    �m ~   n J   �n �   �n    �o '   �o {   �o '   Rp #   zp &   �p )   �p 4   �p )   $q    Nq    jq 5   rq    �q    �q 0   �q *  r ?   Cs D   �s U   �s #   t �   Bt    �t 
   �t T   �t    Mu (   bu    �u @   �u ?   �u *   (v �   Sv    �v    w    w    3w    Iw    `w !   zw    �w    �w O   �w    x     y 1   ;y �   my �   Iz     { !   {    ?{    R{    b{    �{    �{    �{    �{    �{    �{    �{ 
   |    |    $|    6|    C|    O| #   _| �   �|   ^} `  n~   � �  � �   �� �  ��    S� 
  _�    j� �   �� �   |� �  k� �   � u  ǋ 7   =� �   u� D   !�    f� D   �� U   ʎ g    � d   �� �   � �   �� �   ?� ?  �    E� �   ]� �   � a   ҕ �   4�    � �   ��   �� �   <� �   �    ՚ j   ޚ g   I� �   �� �   �� g   ;� �   ��    0� #   D�    h� D   �� %   Ǟ 
   � �   �� K   �� r   ؟ $   K� �   p� �   � =   �� X   ʡ Y   #� X   }� X   ֢ X   /� k   �� y   �� �   n� v   �� 
   n� 3   y� %   �� ]   ӥ 5   1� 	   g� 	   q� Q   {�    ͦ    �    �� .   � 5   1� B   g�    ��    §    ϧ    ۧ 	   � W   � r   C� Q   �� 6   �    ?� #   G� .   k�    �� 
   ��    ��    ��    ɩ 	   Щ    ک    �    �    ��    �    � 	   &� 
   0� 
   >�    L� 	   \�    f� 
   s� 	   ~�    �� #   �� !   Ǫ h   �    R� b   k� �   Ϋ    �� 
   ��    �� 
   ¬    ͬ    Ӭ 
   ׬ �   � �   �� O   #�    s�    �� x   ��    � �   4� �   �� '   I�    q� �   �� �   "� I   � �   5� i   �� /   h� �   ��    *�    G� V   e� �   �� )   T� �   ~� �   (� �   �� S   ��    �    ��    �    (�    4�    J�    W�    f� �   �� m   � �   r� �   �� �   ̺ ^   �� ?    � �  @� t  �� �   p� �   e�   � p  #�    �� �   ��   c� �   � t  =� �  �� �   �� h  �    p� G   �� �   �� r  k� D   �� �   #� 
   � 	   �    &� �   @� A   �� ]   � 5   n� c   �� I   � ^   R� *   �� �   �� 9   �� (   �� O   �� �   L�    7�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: lt
Language-Team: lt <<EMAIL>>
Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && (n%100<10 || n%100>=20) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library yra populiari (ir nelegali) biblioteka. Jie paėmė Library Genesis kolekciją ir padarė ją lengvai ieškomą. Be to, jie tapo labai veiksmingi pritraukiant naujas knygų įnašas, skatindami prisidedančius vartotojus įvairiomis privilegijomis. Šiuo metu jie negrąžina šių naujų knygų atgal į Library Genesis. Skirtingai nei Library Genesis, jie neleidžia lengvai atkartoti savo kolekcijos, kas trukdo plačiam išsaugojimui. Tai svarbu jų verslo modeliui, nes jie ima mokestį už prieigą prie savo kolekcijos dideliais kiekiais (daugiau nei 10 knygų per dieną). Mes nedarome moralinių sprendimų dėl mokesčio už didelės apimties prieigą prie neteisėtos knygų kolekcijos. Neabejotina, kad Z-Library sėkmingai išplėtė prieigą prie žinių ir pritraukė daugiau knygų. Mes tiesiog esame čia, kad atliktume savo dalį: užtikrinti ilgalaikį šios privačios kolekcijos išsaugojimą. - Ana ir komanda (<a %(reddit)s>Reddit</a>) Pirate Library Mirror originaliame leidime (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>), mes sukūrėme Z-Library, didelės nelegalios knygų kolekcijos, atkartojimą. Primename, kad tai, ką parašėme originaliame tinklaraščio įraše: Ta kolekcija datuojama 2021 metų viduriu. Tuo tarpu Z-Library auga stulbinančiu tempu: jie pridėjo apie 3,8 milijono naujų knygų. Žinoma, yra keletas dublikatų, bet dauguma jų atrodo kaip teisėtai naujos knygos arba aukštesnės kokybės anksčiau pateiktų knygų skenavimai. Tai daugiausia dėl padidėjusio savanorių moderatorių skaičiaus Z-Library ir jų masinio įkėlimo sistemos su dublikatų pašalinimu. Norėtume juos pasveikinti su šiais pasiekimais. Džiaugiamės galėdami pranešti, kad gavome visas knygas, kurios buvo pridėtos į Z-Library tarp mūsų paskutinio atkartojimo ir 2022 metų rugpjūčio. Taip pat grįžome ir surinkome kai kurias knygas, kurias praleidome pirmą kartą. Iš viso ši nauja kolekcija yra apie 24TB, kas yra daug didesnė nei ankstesnė (7TB). Mūsų atkartojimas dabar yra 31TB iš viso. Vėlgi, mes pašalinome dublikatus su Library Genesis, nes jau yra prieinami torrentai šiai kolekcijai. Prašome eiti į Piratų bibliotekos atkartojimą, kad patikrintumėte naują kolekciją (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>). Ten yra daugiau informacijos apie tai, kaip failai yra struktūrizuoti ir kas pasikeitė nuo paskutinio karto. Mes nesusiesime su ja iš čia, nes tai tik tinklaraščio svetainė, kuri netalpina jokių nelegalių medžiagų. Žinoma, sėjimas taip pat yra puikus būdas mums padėti. Dėkojame visiems, kurie sėja mūsų ankstesnį torrentų rinkinį. Esame dėkingi už teigiamą atsaką ir džiaugiamės, kad yra tiek daug žmonių, kurie rūpinasi žinių ir kultūros išsaugojimu tokiu neįprastu būdu. 3x naujos knygos pridėtos į Piratų Bibliotekos Veidrodį (+24TB, 3,8 milijono knygų) Skaitykite papildomus straipsnius iš TorrentFreak: <a %(torrentfreak)s>pirmas</a>, <a %(torrentfreak_2)s>antras</a> - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) lydinčios TorrentFreak straipsniai: <a %(torrentfreak)s>pirmas</a>, <a %(torrentfreak_2)s>antras</a> Ne taip seniai „šešėlinės bibliotekos“ buvo nykstančios. Sci-Hub, didžiulė nelegali akademinių straipsnių archyvas, nustojo priimti naujus darbus dėl teisminių procesų. „Z-Library“, didžiausia nelegali knygų biblioteka, matė, kaip jos tariami kūrėjai buvo suimti dėl baudžiamųjų autorių teisių pažeidimų. Jie neįtikėtinai sugebėjo išvengti arešto, tačiau jų biblioteka vis dar yra grėsmėje. Kai kurios šalys jau daro šios versijos. TorrentFreak <a %(torrentfreak)s>pranešė</a>, kad Kinija ir Japonija įtraukė AI išimtis į savo autorių teisių įstatymus. Mums neaišku, kaip tai sąveikauja su tarptautinėmis sutartimis, bet tai tikrai suteikia apsaugą jų vidaus įmonėms, kas paaiškina, ką mes matėme. Kalbant apie Anos Archyvą — mes tęsime savo pogrindinį darbą, pagrįstą moraliniu įsitikinimu. Tačiau mūsų didžiausias noras yra išeiti į šviesą ir teisėtai padidinti savo poveikį. Prašome reformuoti autorių teises. Kai Z-Library susidūrė su uždarymu, aš jau buvau atsarginę kopiją padaręs visai jos bibliotekai ir ieškojau platformos, kurioje ją patalpinti. Tai buvo mano motyvacija pradėti Anos Archyvą: tęsti misiją, kurią pradėjo ankstesnės iniciatyvos. Nuo tada mes išaugome iki didžiausios šešėlinės bibliotekos pasaulyje, talpinančios daugiau nei 140 milijonų autorių teisių saugomų tekstų įvairiais formatais — knygas, akademinius straipsnius, žurnalus, laikraščius ir kt. Mano komanda ir aš esame ideologai. Mes tikime, kad šių failų išsaugojimas ir talpinimas yra moraliai teisingas. Bibliotekos visame pasaulyje susiduria su finansavimo mažinimu, ir mes negalime pasitikėti žmonijos paveldu korporacijoms. Tada atėjo dirbtinis intelektas. Beveik visos pagrindinės įmonės, kuriančios LLM, susisiekė su mumis, kad galėtų mokytis iš mūsų duomenų. Dauguma (bet ne visos!) JAV įmonės persvarstė savo poziciją, kai suprato mūsų darbo neteisėtumą. Priešingai, Kinijos įmonės entuziastingai priėmė mūsų kolekciją, akivaizdžiai nesijaudindamos dėl jos teisėtumo. Tai yra pastebima, atsižvelgiant į tai, kad Kinija yra beveik visų pagrindinių tarptautinių autorių teisių sutarčių signatarė. Mes suteikėme didelės spartos prieigą apie 30 įmonių. Dauguma jų yra LLM įmonės, o kai kurios yra duomenų brokeriai, kurie perparduos mūsų kolekciją. Dauguma yra iš Kinijos, tačiau mes taip pat dirbome su įmonėmis iš JAV, Europos, Rusijos, Pietų Korėjos ir Japonijos. DeepSeek <a %(arxiv)s>pripažino</a>, kad ankstesnė versija buvo mokoma iš dalies mūsų kolekcijos, nors jie tyliai kalba apie savo naujausią modelį (tikriausiai taip pat mokytą iš mūsų duomenų). Jei Vakarai nori išlikti priekyje LLM lenktynėse ir galiausiai AGI, jie turi persvarstyti savo poziciją dėl autorių teisių, ir greitai. Nesvarbu, ar sutinkate su mumis dėl mūsų moralinio atvejo, tai dabar tampa ekonomikos ir net nacionalinio saugumo klausimu. Visos galios blokai kuria dirbtinius supermokslininkus, superhakerius ir superkarinius. Informacijos laisvė tampa išlikimo klausimu šioms šalims — net nacionalinio saugumo klausimu. Mūsų komanda yra iš viso pasaulio, ir mes neturime konkrečios krypties. Tačiau mes skatintume šalis, turinčias stiprias autorių teisių įstatymus, naudoti šią egzistencinę grėsmę jiems reformuoti. Taigi, ką daryti? Mūsų pirmasis rekomendacija yra paprasta: sutrumpinti autorių teisių terminą. JAV autorių teisės suteikiamos 70 metų po autoriaus mirties. Tai absurdiška. Mes galime tai suderinti su patentais, kurie suteikiami 20 metų po pateikimo. Tai turėtų būti daugiau nei pakankamai laiko knygų, straipsnių, muzikos, meno ir kitų kūrybinių darbų autoriams, kad jie būtų visiškai kompensuoti už savo pastangas (įskaitant ilgalaikius projektus, tokius kaip filmų adaptacijos). Tada, bent jau, politikos formuotojai turėtų įtraukti išimtis masinio tekstų išsaugojimo ir platinimo atvejams. Jei pagrindinis rūpestis yra prarastos pajamos iš individualių klientų, asmeninio lygio platinimas galėtų likti draudžiamas. Savo ruožtu, tie, kurie gali valdyti didžiules saugyklas — įmonės, mokančios LLM, kartu su bibliotekomis ir kitais archyvais — būtų padengti šiomis išimtimis. Autorių teisių reforma yra būtina nacionaliniam saugumui TL;DR: Kinijos LLM (įskaitant DeepSeek) yra mokomi iš mano nelegalios knygų ir straipsnių archyvo — didžiausio pasaulyje. Vakarai turi pertvarkyti autorių teisių įstatymus kaip nacionalinio saugumo klausimą. Daugiau informacijos rasite <a %(all_isbns)s>originaliame tinklaraščio įraše</a>. Mes iškėlėme iššūkį tai pagerinti. Pirmąją vietą apdovanotume 6 000 USD, antrąją vietą – 3 000 USD, o trečiąją vietą – 1 000 USD. Dėl didžiulio atsako ir neįtikėtinų pateikimų nusprendėme šiek tiek padidinti prizų fondą ir apdovanoti keturias trečiąsias vietas po 500 USD kiekvienai. Nugalėtojai yra žemiau, bet būtinai peržiūrėkite visus pateikimus <a %(annas_archive)s>čia</a> arba atsisiųskite mūsų <a %(a_2025_01_isbn_visualization_files)s>bendrą torrentą</a>. Pirmoji vieta 6 000 USD: phiresky Šis <a %(phiresky_github)s>pateikimas</a> (<a %(annas_archive_note_2951)s>Gitlab komentaras</a>) yra tiesiog viskas, ko norėjome, ir dar daugiau! Ypač patiko neįtikėtinai lankstūs vizualizacijos pasirinkimai (netgi palaikantys pasirinktinius šeiderius), tačiau su išsamiu išankstinių nustatymų sąrašu. Taip pat patiko, kaip viskas greitai ir sklandžiai veikia, paprasta įgyvendinimo struktūra (neturinti net serverio), sumanus minimapas ir išsamus paaiškinimas jų <a %(phiresky_github)s>tinklaraščio įraše</a>. Neįtikėtinas darbas ir pelnytai laimėta pirmoji vieta! - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Mūsų širdys pilnos dėkingumo. Pažymėtinos idėjos Dangoraižiai retumui Daug slankiklių, skirtų lyginti datasets, tarsi būtumėte DJ. Skalės juosta su knygų skaičiumi. Gražios etiketės. Šauni numatytoji spalvų schema ir šilumos žemėlapis. Unikalus žemėlapio vaizdas ir filtrai Anotacijos ir taip pat tiesioginė statistika Tiesioginė statistika Keletas daugiau idėjų ir įgyvendinimų, kurie mums ypač patiko: Galėtume tęsti dar ilgai, bet sustokime čia. Būtinai peržiūrėkite visas pateiktis <a %(annas_archive)s>čia</a> arba atsisiųskite mūsų <a %(a_2025_01_isbn_visualization_files)s>bendrą torrentą</a>. Tiek daug pateikčių, ir kiekviena suteikia unikalią perspektyvą, tiek UI, tiek įgyvendinimo srityje. Mes bent jau įtrauksime pirmos vietos pateiktį į mūsų pagrindinę svetainę, o galbūt ir kitas. Taip pat pradėjome galvoti, kaip organizuoti procesą, identifikuojant, patvirtinant ir archyvuojant rečiausias knygas. Daugiau informacijos šiuo klausimu ateityje. Dėkojame visiems dalyviams. Nuostabu, kad tiek daug žmonių rūpinasi. Lengvas duomenų rinkinių perjungimas greitiems palyginimams. Visi ISBN CADAL SSNO CERLALC duomenų nutekėjimas DuXiu SSID EBSCOhost eBook indeksas Google Knygos Goodreads Internetinis Archyvas ISBNdb ISBN Pasaulinis leidėjų registras Libby Failai Anos Archyve Nexus/STC OCLC/Worldcat OpenLibrary Rusijos valstybinė biblioteka Trantoro imperatoriškoji biblioteka Antroji vieta 3 000 USD: hypha „Nors tobuli kvadratai ir stačiakampiai yra matematiškai patrauklūs, jie nesuteikia pranašumo lokalumo kontekste. Manau, kad asimetrija, būdinga šioms Hilberto ar klasikinėms Mortono kreivėms, nėra trūkumas, o savybė. Kaip Italijos garsusis bato formos kontūras daro ją iš karto atpažįstamą žemėlapyje, taip ir šių kreivių unikalūs „keistenybės“ gali tarnauti kaip kognityviniai orientyrai. Šis išskirtinumas gali pagerinti erdvinę atmintį ir padėti vartotojams orientuotis, galbūt palengvinant konkrečių regionų suradimą ar šablonų pastebėjimą.“ Kitas neįtikėtinas <a %(annas_archive_note_2913)s>pateikimas</a>. Ne toks lankstus kaip pirmoji vieta, bet iš tikrųjų mums labiau patiko jo makro lygio vizualizacija nei pirmosios vietos (erdvės užpildymo kreivė, ribos, žymėjimas, paryškinimas, slinkimas ir priartinimas). Joe Davis <a %(annas_archive_note_2971)s>komentaras</a> mus sužavėjo: Ir vis dar daug galimybių vizualizavimui ir atvaizdavimui, taip pat neįtikėtinai sklandus ir intuityvus vartotojo sąsaja. Tvirta antroji vieta! - Ana ir komanda (<a %(reddit)s>Reddit</a>) Prieš kelis mėnesius paskelbėme <a %(all_isbns)s>$10,000 premiją</a> už geriausią mūsų duomenų vizualizaciją, parodančią ISBN erdvę. Pabrėžėme, kurios bylos jau esame/nesame archyvavę, ir vėliau duomenų rinkinį, apibūdinantį, kiek bibliotekų turi ISBN (retumo matas). Mus esame užplūdę atsakymai. Buvo tiek daug kūrybiškumo. Didelis ačiū visiems, kurie dalyvavo: jūsų energija ir entuziazmas yra užkrečiantys! Galiausiai norėjome atsakyti į šiuos klausimus: <strong>kokios knygos egzistuoja pasaulyje, kiek jų jau esame archyvavę ir į kurias knygas turėtume sutelkti dėmesį toliau?</strong> Džiugu matyti, kad tiek daug žmonių rūpinasi šiais klausimais. Mes patys pradėjome nuo paprastos vizualizacijos. Mažiau nei 300 kb, ši nuotrauka glaustai atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje: Trečioji vieta 500 $ #1: maxlion Šiame <a %(annas_archive_note_2940)s>pateikime</a> mums labai patiko įvairūs vaizdai, ypač palyginimo ir leidėjo vaizdai. Trečioji vieta 500 $ #2: abetusk Nors tai nėra pati išbaigčiausia vartotojo sąsaja, šis <a %(annas_archive_note_2917)s>pateikimas</a> atitinka daugelį kriterijų. Ypač patiko jo palyginimo funkcija. Trečioji vieta 500 $ #3: conundrumer0 Kaip ir pirmoji vieta, šis <a %(annas_archive_note_2975)s>pateikimas</a> mus sužavėjo savo lankstumu. Galiausiai tai yra tai, kas daro puikų vizualizavimo įrankį: maksimalus lankstumas galingiems vartotojams, išlaikant paprastumą vidutiniams vartotojams. Trečioji vieta 500 $ #4: charelf Paskutinis <a %(annas_archive_note_2947)s>pateikimas</a>, gavęs premiją, yra gana paprastas, bet turi keletą unikalių savybių, kurios mums labai patiko. Mums patiko, kaip jie parodo, kiek duomenų rinkinių apima konkretų ISBN kaip populiarumo/patikimumo matą. Taip pat labai patiko paprastumas, bet efektyvumas naudojant skaidrumo slankiklį palyginimams. $10,000 ISBN vizualizacijos premijos laimėtojai TL;DR: Gavome neįtikėtinų pateikimų $10,000 ISBN vizualizacijos premijai. Fonas Kaip „Anos Archyvas“ gali pasiekti savo misiją atsargiai saugoti visą žmonijos žinias, nežinodamas, kurios knygos dar egzistuoja? Mums reikia TODO sąrašo. Vienas iš būdų tai išdėstyti yra per ISBN numerius, kurie nuo 1970-ųjų buvo priskirti kiekvienai išleistai knygai (daugumoje šalių). Nėra centrinės institucijos, kuri žinotų visus ISBN priskyrimus. Vietoj to, tai yra paskirstyta sistema, kurioje šalys gauna numerių diapazonus, kurie tada priskiriami didiesiems leidėjams, kurie gali toliau padalinti diapazonus mažesniems leidėjams. Galiausiai atskiri numeriai priskiriami knygoms. Pradėjome žemėlapiuoti ISBN <a %(blog)s>prieš dvejus metus</a> su mūsų ISBNdb duomenų rinkimu. Nuo tada surinkome daug daugiau metadata šaltinių, tokių kaip <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ir daugiau. Visą sąrašą galima rasti „Datasets“ ir „Torrents“ puslapiuose „Anos Archyve“. Dabar turime didžiausią visiškai atvirą, lengvai atsisiunčiamą knygų metadata (ir taip ISBN) kolekciją pasaulyje. Mes <a %(blog)s>plačiai rašėme</a> apie tai, kodėl mums rūpi išsaugojimas, ir kodėl šiuo metu esame kritiniame lange. Dabar turime identifikuoti retas, nepakankamai dėmesio sulaukiančias ir unikaliai rizikingas knygas ir jas išsaugoti. Turint gerą metadata apie visas pasaulio knygas, tai padeda. 10 000 $ atlygis Didelis dėmesys bus skiriamas naudojimo patogumui ir išvaizdai. Rodyti faktinę metadata atskiriems ISBN, kai priartinama, pvz., pavadinimą ir autorių. Geresnė erdvės užpildymo kreivė. Pvz., zigzagas, einantis nuo 0 iki 4 pirmoje eilutėje ir tada atgal (atvirkščiai) nuo 5 iki 9 antroje eilutėje — taikomas rekursyviai. Skirtingos arba pritaikomos spalvų schemos. Specialūs vaizdai Datasets palyginimui. Būdai spręsti problemas, pvz., kiti metadata, kurie nesutampa gerai (pvz., labai skirtingi pavadinimai). Vaizdų anotavimas su komentarais apie ISBN ar diapazonus. Bet kokie euristikos metodai retų ar rizikos grupėje esančių knygų identifikavimui. Bet kokios kūrybinės idėjos, kurias galite sugalvoti! Kodas Kodas, skirtas šioms nuotraukoms generuoti, taip pat kiti pavyzdžiai, gali būti rasti <a %(annas_archive)s>šiame kataloge</a>. Sukūrėme kompaktišką duomenų formatą, su kuriuo visa reikalinga ISBN informacija užima apie 75 MB (suspausta). Duomenų formato aprašymas ir kodas, skirtas jį generuoti, gali būti rasti <a %(annas_archive_l1244_1319)s>čia</a>. Norint gauti atlygį, jums nereikia to naudoti, bet tai tikriausiai yra patogiausias formatas pradėti. Galite transformuoti mūsų metadata kaip norite (nors visas jūsų kodas turi būti atviro kodo). Negalime sulaukti, ką sugalvosite. Sėkmės! Fork'uokite šį repo ir redaguokite šį tinklaraščio įrašo HTML (kiti backend'ai, išskyrus mūsų Flask backend'ą, nėra leidžiami). Padarykite, kad aukščiau esanti nuotrauka būtų sklandžiai priartinama, kad galėtumėte priartinti iki atskirų ISBN. Spustelėjus ISBN, turėtų būti nukreipiama į metadata puslapį arba paiešką Anos Archyve. Jūs vis tiek turite galėti perjungti visus skirtingus Datasets. Šalies ir leidėjo diapazonai turėtų būti paryškinti užvedus pelę. Galite naudoti, pvz., <a %(github_xlcnd_isbnlib)s>data4info.py isbnlib'e</a> šalies informacijai, ir mūsų „isbngrp“ nuskaitymą leidėjams (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Tai turi gerai veikti tiek staliniuose, tiek mobiliuosiuose įrenginiuose. Čia yra daug ką tyrinėti, todėl skelbiame atlygį už aukščiau pateiktos vizualizacijos tobulinimą. Skirtingai nuo daugumos mūsų atlygių, šis yra laiko ribotas. Jūs turite <a %(annas_archive)s>pateikti</a> savo atviro kodo kodą iki 2025-01-31 (23:59 UTC). Geriausias pateikimas gaus 6 000 USD, antroji vieta – 3 000 USD, o trečioji vieta – 1 000 USD. Visi prizai bus išmokėti naudojant Monero (XMR). Žemiau pateikti minimalūs kriterijai. Jei nė vienas pateikimas neatitiks kriterijų, mes vis tiek galime skirti kai kuriuos prizus, tačiau tai bus mūsų nuožiūra. Papildomiems taškams (tai tik idėjos — leiskite savo kūrybiškumui laisvai reikštis): Galite visiškai nukrypti nuo minimalių kriterijų ir sukurti visiškai kitokią vizualizaciją. Jei ji bus tikrai įspūdinga, tai gali atitikti premijos reikalavimus, tačiau tai priklauso nuo mūsų sprendimo. Pateikite pasiūlymus, palikdami komentarą <a %(annas_archive)s>šiame klausime</a> su nuoroda į jūsų šakotą repo, sujungimo užklausą arba skirtumą. - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Šis paveikslėlis yra 1000×800 pikselių. Kiekvienas pikselis atitinka 2 500 ISBN. Jei turime failą ISBN, tą pikselį padarome žalesnį. Jei žinome, kad ISBN buvo išduotas, bet neturime atitinkamo failo, padarome jį raudonesnį. Mažiau nei 300 kb, šis paveikslėlis glaustai atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje (keletas šimtų GB suspaustų pilnai). Tai taip pat rodo: dar daug darbo liko atsarginėms knygoms (turime tik 16%). Vizualizuojant visus ISBN — 10 000 $ premija iki 2025-01-31 Šis paveikslas atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje. Vizualizavimas Be apžvalgos vaizdo, mes taip pat galime pažvelgti į atskirus datasets, kuriuos įsigijome. Naudokite išskleidžiamąjį meniu ir mygtukus, kad perjungtumėte tarp jų. Šiuose paveikslėliuose galima pamatyti daug įdomių modelių. Kodėl yra tam tikras linijų ir blokų reguliarumas, kuris atrodo vykstantis skirtingais masteliais? Kas yra tuščios sritys? Kodėl tam tikri datasets yra taip susitelkę? Paliksime šiuos klausimus skaitytojui kaip užduotį. - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Išvada Su šiuo standartu galime išleisti daugiau palaipsniui ir lengviau pridėti naujus duomenų šaltinius. Jau turime keletą įdomių leidimų, kurie yra ruošiami! Tikimės, kad kitiems šešėliniams bibliotekoms bus lengviau atkartoti mūsų kolekcijas. Juk mūsų tikslas yra išsaugoti žmonijos žinias ir kultūrą amžinai, todėl kuo daugiau atsarginių kopijų, tuo geriau. Pavyzdys Pažvelkime į mūsų naujausią Z-Library leidimą kaip pavyzdį. Jį sudaro dvi kolekcijos: “<span style="background: #fffaa3">zlib3_records</span>” ir “<span style="background: #ffd6fe">zlib3_files</span>”. Tai leidžia mums atskirai nuskaityti ir išleisti metaduomenų įrašus nuo tikrųjų knygų failų. Todėl mes išleidome du torrentus su metaduomenų failais: Mes taip pat išleidome daugybę torrentų su dvejetainių duomenų aplankais, bet tik “<span style="background: #ffd6fe">zlib3_files</span>” kolekcijai, iš viso 62: Paleidę <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> galime pamatyti, kas yra viduje: Šiuo atveju tai yra knygos metaduomenys, kaip pranešė Z-Library. Aukščiausiame lygyje turime tik „aacid“ ir „metadata“, bet nėra „data_folder“, nes nėra atitinkamų dvejetainių duomenų. AACID turi „22430000“ kaip pagrindinį ID, kurį matome, kad yra paimtas iš „zlibrary_id“. Galime tikėtis, kad kiti AAC šioje kolekcijoje turės tą pačią struktūrą. Dabar paleiskime <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Tai yra daug mažesnė AAC metadata, nors didžioji dalis šios AAC yra kitur dvejetainėje byloje! Juk šį kartą turime „data_folder“, todėl galime tikėtis, kad atitinkami dvejetainiai duomenys bus <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata“ turi „zlibrary_id“, todėl galime lengvai susieti jį su atitinkamu AAC „zlib_records“ kolekcijoje. Galėjome susieti įvairiais būdais, pvz., per AACID — standartas to nereikalauja. Atkreipkite dėmesį, kad „metadata“ laukas nebūtinai turi būti JSON. Tai gali būti eilutė, kurioje yra XML arba bet kuris kitas duomenų formatas. Galite netgi saugoti metaduomenų informaciją susijusiame dvejetainėje masėje, pvz., jei tai yra daug duomenų. Heterogeniški failai ir metaduomenys, kuo arčiau originalaus formato. Dvejetainiai duomenys gali būti tiesiogiai aptarnaujami žiniatinklio serverių, tokių kaip Nginx. Heterogeniški identifikatoriai šaltiniuose bibliotekose arba net identifikatorių trūkumas. Atskiri metaduomenų ir failų duomenų leidimai arba tik metaduomenų leidimai (pvz., mūsų ISBNdb leidimas). Platinimas per torrentus, tačiau su galimybe naudoti kitus platinimo metodus (pvz., IPFS). Nekintami įrašai, nes turėtume manyti, kad mūsų torrentai gyvens amžinai. Inkrementiniai leidimai / papildomi leidimai. Mašininiu būdu skaitomi ir rašomi, patogiai ir greitai, ypač mūsų stekui (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Šiek tiek lengva žmogaus patikra, nors tai yra antraeilis dalykas, palyginti su mašininiu skaitomumu. Lengva užpildyti mūsų kolekcijas naudojant standartinę nuomojamą seedbox. Dizaino tikslai Mums nerūpi, kad failus būtų lengva naršyti rankiniu būdu diske ar ieškoti be išankstinio apdorojimo. Mums nerūpi, kad būtų tiesiogiai suderinama su esama bibliotekos programine įranga. Nors turėtų būti lengva bet kam užpildyti mūsų kolekciją naudojant torrentus, mes nesitikime, kad failai bus naudojami be reikšmingų techninių žinių ir įsipareigojimo. Mūsų pagrindinis naudojimo atvejis yra failų ir susijusių metaduomenų platinimas iš skirtingų esamų kolekcijų. Mūsų svarbiausi aspektai yra: Kai kurie ne tikslai: Kadangi Annos Archyvas yra atviro kodo, norime tiesiogiai naudoti savo formatą. Kai atnaujiname savo paieškos indeksą, prieiname tik viešai prieinamus kelius, kad bet kas, kas kopijuoja mūsų biblioteką, galėtų greitai pradėti naudotis. <strong>AAC.</strong> AAC (Annės Archyvo Konteineris) yra vienas elementas, sudarytas iš <strong>metaduomenų</strong> ir, jei reikia, <strong>dvejetainės duomenų</strong>, abu yra nekintami. Jis turi pasaulinį unikalų identifikatorių, vadinamą <strong>AACID</strong>. <strong>AACID.</strong> AACID formatas yra toks: <code style="color: #0093ff">aacid__{kolekcija}__{ISO 8601 laiko žyma}__{kolekcijos specifinis ID}__{shortuuid}</code>. Pavyzdžiui, vienas iš mūsų išleistų AACID yra <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID diapazonas.</strong> Kadangi AACID turi monotoniškai didėjančias laiko žymas, galime tai naudoti, norėdami nurodyti diapazonus tam tikroje kolekcijoje. Naudojame šį formatą: <code style="color: blue">aacid__{kolekcija}__{nuo_laiko_žymos}--{iki_laiko_žymos}</code>, kur laiko žymos yra įtrauktos. Tai atitinka ISO 8601 notaciją. Diapazonai yra tęstiniai ir gali persidengti, bet persidengimo atveju turi turėti identiškus įrašus kaip ir anksčiau išleisti toje kolekcijoje (kadangi AAC yra nekintami). Trūkstami įrašai neleidžiami. <code>{kolekcija}</code>: kolekcijos pavadinimas, kuris gali turėti ASCII raides, skaičius ir pabraukimus (bet ne dvigubus pabraukimus). <code>{kolekcijos specifinis ID}</code>: kolekcijos specifinis identifikatorius, jei taikoma, pvz., Z-Library ID. Gali būti praleistas arba sutrumpintas. Turi būti praleistas arba sutrumpintas, jei AACID kitaip viršytų 150 simbolių. <code>{ISO 8601 laiko žyma}</code>: trumpa ISO 8601 versija, visada UTC, pvz., <code>20220723T194746Z</code>. Šis skaičius turi monotoniškai didėti kiekvienam leidimui, nors jo tiksli semantika gali skirtis priklausomai nuo kolekcijos. Siūlome naudoti nuskaitymo arba ID generavimo laiką. <code>{shortuuid}</code>: UUID, bet suspaustas iki ASCII, pvz., naudojant base57. Šiuo metu naudojame <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python biblioteką. <strong>Dvejetainių duomenų aplankas.</strong> Aplankas su tam tikros kolekcijos AAC diapazono dvejetainiais duomenimis. Jie turi šias savybes: Kataloge turi būti duomenų failai visiems AAC nurodytame diapazone. Kiekvienas duomenų failas turi turėti savo AACID kaip failo pavadinimą (be plėtinių). Katalogo pavadinimas turi būti AACID diapazonas, su priešdėliu <code style="color: green">annas_archive_data__</code>, be jokio priesagos. Pavyzdžiui, vienas iš mūsų faktinių leidimų turi katalogą, vadinamą<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Rekomenduojama, kad šie aplankai būtų valdomi pagal dydį, pvz., ne didesni nei 100GB-1TB kiekvienas, nors ši rekomendacija gali keistis laikui bėgant. <strong>Kolekcija.</strong> Kiekvienas AAC priklauso kolekcijai, kuri pagal apibrėžimą yra semantiškai nuoseklių AAC sąrašas. Tai reiškia, kad jei reikšmingai pakeisite metaduomenų formatą, turite sukurti naują kolekciją. Standartas <strong>Metadata failas.</strong> Metadata failas turi AAC diapazono metaduomenis, skirtus vienai konkrečiai kolekcijai. Jie turi šias savybes: <code>data_folder</code> yra neprivaloma ir tai yra dvejetainių duomenų aplanko pavadinimas, kuriame yra atitinkami dvejetainiai duomenys. Atitinkamų dvejetainių duomenų failo pavadinimas tame aplanke yra įrašo AACID. Kiekvienas JSON objektas turi turėti šiuos laukus aukščiausiame lygyje: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (neprivaloma). Kiti laukai nėra leidžiami. Failo pavadinimas turi būti AACID diapazonas, prieš kurį yra <code style="color: red">annas_archive_meta__</code> ir po kurio eina <code>.jsonl.zstd</code>. Pavyzdžiui, vienas iš mūsų leidimų vadinasi<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Kaip nurodo failo plėtinys, failo tipas yra <a %(jsonlines)s>JSON Lines</a>, suspaustas naudojant <a %(zstd)s>Zstandard</a>. <code>metadata</code> yra savavališka metadata, atitinkanti kolekcijos semantiką. Ji turi būti semantiškai nuosekli kolekcijoje. <code style="color: red">annas_archive_meta__</code> priešdėlis gali būti pritaikytas jūsų institucijos pavadinimui, pvz., <code style="color: red">my_institute_meta__</code>. <strong>„įrašų“ ir „failų“ kolekcijos.</strong> Pagal susitarimą, dažnai patogu išleisti „įrašus“ ir „failus“ kaip skirtingas kolekcijas, kad jie galėtų būti išleisti skirtingais grafikais, pvz., pagal nuskaitymo greitį. „Įrašas“ yra tik metaduomenų kolekcija, kurioje yra informacija, tokia kaip knygų pavadinimai, autoriai, ISBN ir kt., o „failai“ yra kolekcijos, kuriose yra tikrieji failai (pdf, epub). Galiausiai, mes apsistojome ties gana paprastu standartu. Jis yra gana laisvas, nenormatyvinis ir dar vystomas. <strong>Torrentai.</strong> Metaduomenų failai ir dvejetainių duomenų aplankai gali būti sujungti į torrentus, su vienu torrentu per metaduomenų failą arba vienu torrentu per dvejetainių duomenų aplanką. Torrentai turi turėti originalų failo/katalogo pavadinimą plius <code>.torrent</code> priesagą kaip jų failo pavadinimą. <a %(wikipedia_annas_archive)s>Annės Archyvas</a> tapo toli gražu didžiausia šešėline biblioteka pasaulyje ir vienintele tokio masto šešėline biblioteka, kuri yra visiškai atviro kodo ir atvirų duomenų. Žemiau pateikta lentelė iš mūsų Datasets puslapio (šiek tiek modifikuota): Tai pasiekėme trimis būdais: Veidrodžiuodami esamas atviro duomenų šešėlines bibliotekas (tokias kaip Sci-Hub ir Library Genesis). Padėdami šešėlinėms bibliotekoms, kurios nori būti atviresnės, bet neturėjo laiko ar išteklių tai padaryti (tokios kaip Libgen komiksų kolekcija). Rinkdami bibliotekas, kurios nenori dalintis dideliais kiekiais (tokios kaip Z-Library). Dėl (2) ir (3) dabar patys valdome nemažą torrentų kolekciją (šimtus TB). Iki šiol šias kolekcijas vertinome kaip vienkartines, tai reiškia, kad kiekvienai kolekcijai reikalinga speciali infrastruktūra ir duomenų organizavimas. Tai prideda reikšmingą papildomą darbą kiekvienam leidimui ir ypač apsunkina daugiau laipsniškų leidimų atlikimą. Štai kodėl nusprendėme standartizuoti savo leidimus. Tai yra techninis tinklaraščio įrašas, kuriame pristatome savo standartą: <strong>Annės Archyvo Konteineriai</strong>. Anos Archyvo Konteineriai (AAC): didžiausios pasaulyje šešėlinės bibliotekos leidimų standartizavimas Annės Archyvas tapo didžiausia šešėline biblioteka pasaulyje, todėl mums reikėjo standartizuoti savo leidimus. 300GB+ knygų viršelių išleista Galiausiai, džiaugiamės galėdami pranešti apie mažą išleidimą. Bendradarbiaudami su žmonėmis, kurie valdo Libgen.rs šaką, dalijamės visais jų knygų viršeliais per torrentus ir IPFS. Tai paskirstys viršelių peržiūros apkrovą tarp daugiau mašinų ir geriau juos išsaugos. Daugeliu (bet ne visais) atvejais knygų viršeliai yra įtraukti į pačius failus, todėl tai yra tam tikri „išvestiniai duomenys“. Tačiau turėti juos IPFS vis dar labai naudinga kasdieniam tiek Anos Archyvo, tiek įvairių Library Genesis šakų veikimui. Kaip įprasta, šį išleidimą galite rasti Piratų bibliotekos veidrodyje (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvas</a>). Mes čia nesusiesime, bet galite lengvai jį rasti. Tikimės, kad galime šiek tiek sulėtinti tempą, dabar, kai turime tinkamą alternatyvą Z-Library. Šis darbo krūvis nėra ypač tvarus. Jei jus domina pagalba su programavimu, serverių veikimu ar išsaugojimo darbu, būtinai susisiekite su mumis. Dar yra daug <a %(annas_archive)s>darbo, kurį reikia atlikti</a>. Dėkojame už jūsų susidomėjimą ir palaikymą. Perėjimas prie ElasticSearch Kai kurios užklausos užtruko labai ilgai, iki taško, kai jos užėmė visas atviras jungtis. Pagal numatytuosius nustatymus MySQL turi minimalų žodžio ilgį, arba jūsų indeksas gali tapti labai didelis. Žmonės pranešė, kad negalėjo ieškoti „Ben Hur“. Paieška buvo tik šiek tiek greita, kai visiškai įkelta į atmintį, o tai reikalavo gauti brangesnį įrenginį, kad tai veiktų, plius kai kuriuos komandas, kad indeksas būtų įkeltas paleidimo metu. Nebūtume galėję lengvai išplėsti, kad sukurtume naujas funkcijas, tokias kaip geresnė <a %(wikipedia_cjk_characters)s>tokenizacija ne tarpuose esančioms kalboms</a>, filtravimas/fasetavimas, rūšiavimas, „ar turėjote omenyje“ pasiūlymai, automatinis užbaigimas ir pan. Vienas iš mūsų <a %(annas_archive)s>bilietų</a> buvo įvairių problemų su mūsų paieškos sistema rinkinys. Naudojome MySQL pilno teksto paiešką, nes visus duomenis turėjome MySQL. Tačiau tai turėjo savo ribas: Pasikalbėję su daugybe ekspertų, nusprendėme naudoti ElasticSearch. Tai nebuvo tobula (jų numatytieji „ar turėjote omenyje“ pasiūlymai ir automatinio užbaigimo funkcijos yra prastos), bet apskritai tai buvo daug geriau nei MySQL paieškai. Mes vis dar nesame <a %(youtube)s>per daug entuziastingi</a> naudoti jį bet kokiems misijai kritiniams duomenims (nors jie padarė daug <a %(elastic_co)s>pažangos</a>), bet apskritai esame gana patenkinti perėjimu. Kol kas įgyvendinome daug greitesnę paiešką, geresnę kalbų palaikymą, geresnį atitikimo rūšiavimą, skirtingas rūšiavimo parinktis ir filtravimą pagal kalbą/knygos tipą/bylos tipą. Jei jums įdomu, kaip tai veikia, <a %(annas_archive_l140)s>pažiūrėkite</a> <a %(annas_archive_l1115)s>čia</a> <a %(annas_archive_l1635)s>daugiau</a>. Tai gana prieinama, nors galėtų būti daugiau komentarų… Annos Archyvas yra visiškai atviro kodo Tikime, kad informacija turėtų būti laisva, ir mūsų pačių kodas nėra išimtis. Mes išleidome visą savo kodą mūsų privačiai talpinamoje Gitlab instancijoje: <a %(annas_archive)s>Annos Programinė Įranga</a>. Taip pat naudojame problemų sekimo sistemą, kad organizuotume savo darbą. Jei norite prisijungti prie mūsų vystymo, tai puiki vieta pradėti. Kad suteiktume jums skonį, ką mes dirbame, pažiūrėkite į mūsų neseniai atliktus klientų pusės našumo patobulinimus. Kadangi dar neįgyvendinome puslapiavimo, dažnai grąžindavome labai ilgas paieškos puslapius, su 100-200 rezultatų. Nenorėjome per anksti nutraukti paieškos rezultatų, tačiau tai reiškė, kad kai kuriuos įrenginius tai sulėtindavo. Tam įgyvendinome mažą triuką: daugumą paieškos rezultatų apvyniojome HTML komentarais (<code><!-- --></code>), o tada parašėme mažą Javascript, kuris aptiktų, kada rezultatas turėtų tapti matomas, tuo momentu mes išvyniotume komentarą: DOM "virtualizacija" įgyvendinta 23 eilutėse, nereikia įmantrių bibliotekų! Tai yra greito pragmatiško kodo pavyzdys, kurį gaunate, kai turite ribotą laiką ir realias problemas, kurias reikia išspręsti. Pranešta, kad mūsų paieška dabar veikia gerai lėtuose įrenginiuose! Kita didelė pastanga buvo automatizuoti duomenų bazės kūrimą. Kai pradėjome, tiesiog atsitiktinai sujungėme skirtingus šaltinius. Dabar norime juos atnaujinti, todėl parašėme daugybę scenarijų, kad atsisiųstume naujus metaduomenis iš dviejų Library Genesis šakų ir juos integruotume. Tikslas yra ne tik padaryti tai naudinga mūsų archyvui, bet ir palengvinti visiems, kurie nori žaisti su šešėlinės bibliotekos metaduomenimis. Tikslas būtų Jupyter užrašų knygelė, kurioje būtų įvairių įdomių metaduomenų, kad galėtume atlikti daugiau tyrimų, pavyzdžiui, išsiaiškinti, kokia <a %(blog)s>ISBN dalis yra išsaugota amžinai</a>. Galiausiai, atnaujinome savo aukojimo sistemą. Dabar galite naudoti kreditinę kortelę, kad tiesiogiai pervestumėte pinigus į mūsų kriptovaliutų pinigines, iš tikrųjų nereikėdami nieko žinoti apie kriptovaliutas. Mes stebėsime, kaip tai veikia praktikoje, bet tai yra didelis dalykas. Kai Z-Library buvo uždaryta ir jos (neva) įkūrėjai buvo suimti, dirbome visą parą, kad pateiktume gerą alternatyvą su Annos Archyvu (čia nesusiesime, bet galite tai rasti „Google“). Štai keletas dalykų, kuriuos neseniai pasiekėme. Annos Atnaujinimas: visiškai atviro kodo archyvas, ElasticSearch, daugiau nei 300GB knygų viršelių Dirbome visą parą, kad pateiktume gerą alternatyvą su Annos Archyvu. Štai keletas dalykų, kuriuos neseniai pasiekėme. Analizė Semantiniai dublikatai (skirtingi to paties knygos skenavimai) teoriškai gali būti filtruojami, bet tai sudėtinga. Rankiniu būdu peržiūrėdami komiksus radome per daug klaidingų teigiamų rezultatų. Yra keletas dublikatų tik pagal MD5, kas yra gana švaistoma, bet jų filtravimas suteiktų tik apie 1% in sutaupymą. Tokiu mastu tai vis dar yra apie 1TB, bet taip pat, tokiu mastu 1TB iš tikrųjų nesvarbu. Mes verčiau nerizikuotume netyčia sunaikinti duomenis šio proceso metu. Radome krūvą ne knygų duomenų, tokių kaip filmai, paremti komiksais. Tai taip pat atrodo švaistoma, nes jie jau plačiai prieinami kitais būdais. Tačiau supratome, kad negalime tiesiog filtruoti filmų failų, nes taip pat yra <em>interaktyvių komiksų knygų</em>, kurios buvo išleistos kompiuteryje, kurias kažkas įrašė ir išsaugojo kaip filmus. Galiausiai, bet kas, ką galėtume ištrinti iš kolekcijos, sutaupytų tik kelis procentus. Tada prisiminėme, kad esame duomenų kaupėjai, ir žmonės, kurie tai atkartos, taip pat yra duomenų kaupėjai, todėl, „KĄ TU TURI OMENYJE, IŠTRINTI?!“ :) Kai gaunate 95TB į savo saugojimo klasterį, bandote suprasti, kas ten iš viso yra... Mes atlikome analizę, kad pamatytume, ar galėtume šiek tiek sumažinti dydį, pavyzdžiui, pašalindami dublikatus. Štai keletas mūsų išvadų: Todėl pristatome jums visą, nemodifikuotą kolekciją. Tai daug duomenų, bet tikimės, kad pakankamai žmonių norės ją sėti. Bendradarbiavimas Atsižvelgiant į jos dydį, ši kolekcija ilgą laiką buvo mūsų norų sąraše, todėl po mūsų sėkmės su Z-Library atsargine kopija, mes nukreipėme savo dėmesį į šią kolekciją. Iš pradžių mes ją tiesiogiai nuskaitydavome, kas buvo nemažas iššūkis, nes jų serveris nebuvo geriausios būklės. Tokiu būdu gavome apie 15TB, bet tai buvo lėtas procesas. Laimei, mums pavyko susisiekti su bibliotekos operatoriumi, kuris sutiko atsiųsti mums visus duomenis tiesiogiai, ir tai buvo daug greičiau. Vis dėlto prireikė daugiau nei pusės metų, kad perkeltume ir apdorotume visus duomenis, ir beveik praradome juos dėl disko sugadinimo, kas būtų reiškę, kad reikėtų pradėti viską iš naujo. Ši patirtis privertė mus manyti, kad svarbu kuo greičiau išplatinti šiuos duomenis, kad jie galėtų būti atkartoti plačiai ir toli. Esame tik vieno ar dviejų nelaimingų atsitikimų nuo to, kad prarastume šią kolekciją amžiams! Kolekcija Greitas judėjimas reiškia, kad kolekcija yra šiek tiek neorganizuota... Pažvelkime. Įsivaizduokime, kad turime failų sistemą (kurią iš tikrųjų skaidome per torrentus): Pirmasis katalogas, <code>/repository</code>, yra labiau struktūruota šios dalies dalis. Šis katalogas turi vadinamuosius „tūkstančio katalogus“: katalogus, kuriuose yra tūkstančiai failų, kurie yra nuosekliai numeruojami duomenų bazėje. Katalogas <code>0</code> turi failus su comic_id 0–999 ir taip toliau. Tai yra ta pati schema, kurią Library Genesis naudoja savo grožinės ir negrožinės literatūros kolekcijoms. Idėja yra ta, kad kiekvienas „tūkstančio katalogas“ automatiškai tampa torrentu, kai tik jis užpildomas. Tačiau Libgen.li operatorius niekada nesukūrė torrentų šiai kolekcijai, todėl tūkstančiai katalogų greičiausiai tapo nepatogūs ir virto „nesutvarkytais katalogais“. Tai yra <code>/comics0</code> iki <code>/comics4</code>. Jie visi turi unikalią katalogų struktūrą, kuri tikriausiai buvo prasminga renkant failus, bet dabar mums nelabai suprantama. Laimei, metadata vis dar tiesiogiai nurodo visus šiuos failus, todėl jų saugojimo organizacija diske iš tikrųjų nesvarbi! Metadata yra prieinama MySQL duomenų bazės formatu. Ją galima atsisiųsti tiesiogiai iš Libgen.li svetainės, bet mes taip pat padarysime ją prieinamą per torrentą, kartu su mūsų pačių lentele, kurioje yra visi MD5 maišos. <q>Dr. Barbara Gordon bando pasinerti į kasdienį bibliotekos pasaulį…</q> Libgen šakos Pirmiausia, šiek tiek fono. Galbūt žinote Library Genesis dėl jų epinės knygų kolekcijos. Mažiau žmonių žino, kad Library Genesis savanoriai sukūrė kitus projektus, tokius kaip didelė žurnalų ir standartinių dokumentų kolekcija, pilna Sci-Hub atsarginė kopija (bendradarbiaujant su Sci-Hub įkūrėja Alexandra Elbakyan) ir iš tiesų, didžiulė komiksų kolekcija. Tam tikru momentu skirtingi Library Genesis veidrodžių operatoriai pasuko skirtingais keliais, kas sukėlė dabartinę situaciją, kai yra keletas skirtingų „šakų“, vis dar turinčių Library Genesis pavadinimą. Libgen.li šaka unikali tuo, kad turi šią komiksų kolekciją, taip pat didelę žurnalų kolekciją (kurią mes taip pat dirbame). Lėšų rinkimas Mes išleidžiame šiuos duomenis dideliais gabalais. Pirmasis torrentas yra <code>/comics0</code>, kurį sudėjome į vieną didžiulį 12TB .tar failą. Tai geriau jūsų kietajam diskui ir torrent programinei įrangai nei milijonas mažesnių failų. Kaip šios išleidimo dalis, mes organizuojame lėšų rinkimą. Siekiame surinkti 20 000 USD, kad padengtume operacines ir sutartines šios kolekcijos išlaidas, taip pat leistume tęsti ir būsimus projektus. Turime keletą <em>milžiniškų</em> projektų, kurie yra kuriami. <em>Kam aš remiu savo auka?</em> Trumpai tariant: mes atsarginę kopiją visų žmonijos žinių ir kultūros, ir darome jas lengvai prieinamas. Visi mūsų kodai ir duomenys yra atviro kodo, mes esame visiškai savanorių valdomas projektas, ir iki šiol išsaugojome 125TB knygų (be Libgen ir Scihub esamų torrentų). Galiausiai mes kuriame smagračio mechanizmą, kuris leidžia ir skatina žmones rasti, skenuoti ir atsarginę kopiją visų pasaulio knygų. Apie mūsų pagrindinį planą rašysime būsimame įraše. :) Jei paaukosite už 12 mėnesių „Nuostabus Archyvaras“ narystę (780 USD), galėsite <strong>„įsivaikinti torrentą“</strong>, tai reiškia, kad mes įdėsime jūsų vartotojo vardą ar žinutę į vieno iš torrentų failo pavadinimą! Galite paaukoti apsilankę <a %(wikipedia_annas_archive)s>Anos Archyve</a> ir paspaudę mygtuką „Paaukoti“. Taip pat ieškome daugiau savanorių: programinės įrangos inžinierių, saugumo tyrėjų, anonimiškų prekybos ekspertų ir vertėjų. Galite mus paremti ir teikdami prieglobos paslaugas. Ir, žinoma, prašome dalintis mūsų torrentais! Dėkojame visiems, kurie jau taip dosniai mus parėmė! Jūs tikrai darote skirtumą. Štai iki šiol išleisti torrentai (dar apdorojame likusius): Visus torrentus galite rasti <a %(wikipedia_annas_archive)s>Anos Archyve</a> skiltyje „Datasets“ (mes tiesiogiai ten nenurodome, kad nuorodos į šį tinklaraštį nebūtų pašalintos iš Reddit, Twitter ir kt.). Iš ten sekite nuorodą į Tor svetainę. <a %(news_ycombinator)s>Diskutuoti Hacker News</a> Kas toliau? Daugybė torrentų puikiai tinka ilgalaikiam išsaugojimui, bet ne kasdieniam naudojimui. Mes dirbsime su prieglobos partneriais, kad visa ši informacija būtų prieinama internete (kadangi Anos Archyvas nieko tiesiogiai netalpina). Žinoma, šias atsisiuntimo nuorodas galėsite rasti Anos Archyve. Taip pat kviečiame visus ką nors daryti su šiais duomenimis! Padėkite mums juos geriau analizuoti, pašalinti dublikatus, įkelti į IPFS, perdirbti, treniruoti savo AI modelius ir t. t. Visa tai jūsų, ir mes nekantraujame pamatyti, ką su tuo padarysite. Galiausiai, kaip jau minėta, mes vis dar turime keletą didžiulių leidimų, kurie netrukus pasirodys (jei <em>kažkas</em> galėtų <em>netyčia</em> atsiųsti mums <em>tam tikros</em> ACS4 duomenų bazės išrašą, žinote, kur mus rasti...), taip pat kuriame sūkurį, kad galėtume atsargiai kopijuoti visas pasaulio knygas. Taigi, sekite naujienas, mes tik pradedame. - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Didžiausia komiksų šešėlinė biblioteka tikriausiai yra tam tikros Library Genesis šakos: Libgen.li. Vienas administratorius, valdantis tą svetainę, sugebėjo surinkti neįtikėtiną komiksų kolekciją, kurią sudaro daugiau nei 2 milijonai failų, iš viso daugiau nei 95TB. Tačiau, skirtingai nei kitos Library Genesis kolekcijos, ši nebuvo prieinama masiškai per torrentus. Galėjote pasiekti šiuos komiksus tik individualiai per jo lėtą asmeninį serverį — vieną gedimo tašką. Iki šiandien! Šiame įraše papasakosime daugiau apie šią kolekciją ir apie mūsų lėšų rinkimą, kad palaikytume daugiau šio darbo. Anos Archyvas atsarginę kopiją padarė didžiausiai pasaulyje komiksų šešėlinei bibliotekai (95TB) — galite padėti ją sėti Didžiausia pasaulyje komiksų šešėlinė biblioteka turėjo vieną gedimo tašką... iki šiandien. Įspėjimas: šis tinklaraščio įrašas yra pasenęs. Nusprendėme, kad IPFS dar nėra pasirengęs plačiam naudojimui. Vis dar nuorodų į failus IPFS iš Annos Archyvo, kai įmanoma, bet nebepriglobosime jų patys ir nerekomenduojame kitiems atkartoti naudojant IPFS. Prašome peržiūrėti mūsų Torrents puslapį, jei norite padėti išsaugoti mūsų kolekciją. 5,998,794 knygų talpinimas IPFS Kopijų dauginimas Grįžtant prie mūsų pradinio klausimo: kaip mes galime teigti, kad išsaugosime savo kolekcijas amžinai? Pagrindinė problema čia yra ta, kad mūsų kolekcija <a %(torrents_stats)s>auga</a> sparčiai, perimant ir atveriant kai kurias didžiules kolekcijas (be jau nuostabaus darbo, kurį atliko kitos atviro duomenų šešėlinės bibliotekos, tokios kaip Sci-Hub ir Library Genesis). Šis duomenų augimas apsunkina kolekcijų atkūrimą visame pasaulyje. Duomenų saugojimas yra brangus! Bet mes esame optimistiški, ypač stebėdami šias tris tendencijas. Mūsų kolekcijų <a %(annas_archive_stats)s>bendras dydis</a> per pastaruosius kelis mėnesius, suskirstytas pagal torrent sėjėjų skaičių. HDD kainų tendencijos iš skirtingų šaltinių (spustelėkite, kad peržiūrėtumėte tyrimą). <a %(critical_window_chinese)s>Kinų versija 中文版</a>, diskutuokite <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Mes nuskynėme lengvai pasiekiamus vaisius Tai tiesiogiai seka iš mūsų aukščiau aptartų prioritetų. Mes teikiame pirmenybę didelių kolekcijų išlaisvinimui pirmiausia. Dabar, kai užsitikrinome kai kurias didžiausias pasaulio kolekcijas, tikimės, kad mūsų augimas bus daug lėtesnis. Vis dar yra ilga mažesnių kolekcijų uodega, ir naujos knygos skenuojamos ar leidžiamos kasdien, bet greitis greičiausiai bus daug lėtesnis. Mes galime vis dar padvigubėti ar net patrigubėti, bet per ilgesnį laikotarpį. OCR patobulinimai. Prioritetai Mokslo ir inžinerijos programinės įrangos kodas Visų aukščiau paminėtų dalykų grožinės ar pramoginės versijos Geografiniai duomenys (pvz., žemėlapiai, geologiniai tyrimai) Vidiniai duomenys iš įmonių ar vyriausybių (nutekėjimai) Matavimo duomenys, tokie kaip moksliniai matavimai, ekonominiai duomenys, įmonių ataskaitos Metaduomenų įrašai apskritai (ne grožinės ir grožinės literatūros; kitų medijų, meno, žmonių ir kt.; įskaitant apžvalgas) Ne grožinės literatūros knygos Ne grožinės literatūros žurnalai, laikraščiai, vadovai Ne grožinės literatūros pokalbių, dokumentinių filmų, tinklalaidžių transkripcijos Organiniai duomenys, tokie kaip DNR sekos, augalų sėklos ar mikrobų mėginiai Akademiniai straipsniai, žurnalai, ataskaitos Mokslo ir inžinerijos svetainės, internetinės diskusijos Teisinių ar teismo procesų transkripcijos Unikaliai sunaikinimo rizikoje (pvz., dėl karo, finansavimo mažinimo, teisminių procesų ar politinio persekiojimo) Retas Unikaliai nepakankamai dėmesio sulaukęs Kodėl mums taip rūpi straipsniai ir knygos? Atsisakykime mūsų pagrindinio tikėjimo išsaugojimu apskritai — galbūt apie tai parašysime kitą įrašą. Taigi kodėl būtent straipsniai ir knygos? Atsakymas paprastas: <strong>informacijos tankis</strong>. Vienam megabaitui saugyklos, rašytinis tekstas saugo daugiausiai informacijos iš visų medijų. Nors mums rūpi tiek žinios, tiek kultūra, labiau rūpinamės pirmąja. Apskritai, randame informacijos tankio ir išsaugojimo svarbos hierarchiją, kuri atrodo maždaug taip: Šio sąrašo reitingas yra šiek tiek savavališkas — kai kurie elementai yra lygūs arba mūsų komandoje yra nesutarimų — ir tikriausiai pamirštame kai kurias svarbias kategorijas. Tačiau tai yra maždaug taip, kaip mes teikiame prioritetus. Kai kurie iš šių elementų yra per daug skirtingi nuo kitų, kad mums reikėtų dėl jų nerimauti (arba jais jau rūpinasi kitos institucijos), pavyzdžiui, organiniai duomenys ar geografiniai duomenys. Tačiau dauguma šio sąrašo elementų mums iš tikrųjų yra svarbūs. Kitas didelis veiksnys mūsų prioritetizacijoje yra tai, kiek tam tikras darbas yra rizikingas. Mes teikiame pirmenybę darbams, kurie yra: Galiausiai, mums rūpi mastas. Turime ribotą laiką ir pinigų, todėl verčiau praleistume mėnesį išsaugodami 10 000 knygų nei 1 000 knygų — jei jos yra vienodai vertingos ir rizikingos. <em><q>Prarasto negalima atgauti; bet išsaugokime tai, kas liko: ne seifais ir spynomis, kurios atskiria juos nuo visuomenės akių ir naudojimo, pasmerkdamos juos laiko švaistymui, bet tokiu kopijų dauginimu, kuris padės juos už nelaimės ribų.</q></em><br>— Thomas Jefferson, 1791 Šešėlinės bibliotekos Kodas gali būti atviro kodo Github, bet Github kaip visuma negali būti lengvai atkuriama ir taip išsaugoma (nors šiuo konkrečiu atveju yra pakankamai paskirstytų daugumos kodo saugyklų kopijų) Metaduomenų įrašus galima laisvai peržiūrėti Worldcat svetainėje, bet jų negalima atsisiųsti dideliais kiekiais (kol mes jų <a %(worldcat_scrape)s>neperėmėme</a>) Reddit yra nemokamas naudoti, bet neseniai įvedė griežtas anti-scraping priemones, dėl duomenų alkanų LLM mokymų (apie tai daugiau vėliau) Yra daug organizacijų, turinčių panašias misijas ir prioritetus. Iš tiesų, yra bibliotekų, archyvų, laboratorijų, muziejų ir kitų institucijų, kurioms pavesta tokio pobūdžio išsaugojimo užduotis. Daugelis jų yra gerai finansuojamos vyriausybių, asmenų ar korporacijų. Tačiau jos turi vieną didelį akląją zoną: teisinę sistemą. Čia slypi unikalus šešėlinių bibliotekų vaidmuo ir priežastis, kodėl egzistuoja Anos Archyvas. Mes galime daryti tai, ko kitos institucijos negali. Dabar, tai nėra (dažnai) taip, kad galime archyvuoti medžiagas, kurios kitur yra neteisėtos išsaugoti. Ne, daugelyje vietų yra teisėta kurti archyvą su bet kokiomis knygomis, straipsniais, žurnalais ir panašiai. Tačiau tai, ko dažnai trūksta teisėtiems archyvams, yra <strong>redundancija ir ilgaamžiškumas</strong>. Yra knygų, kurių egzistuoja tik vienas egzempliorius kažkurioje fizinėje bibliotekoje. Yra metaduomenų įrašų, saugomų vienos korporacijos. Yra laikraščių, išsaugotų tik mikrofilmuose viename archyve. Bibliotekos gali patirti finansavimo sumažinimus, korporacijos gali bankrutuoti, archyvai gali būti bombarduojami ir sudeginti iki pamatų. Tai nėra hipotetinis dalykas — tai vyksta nuolat. Tai, ką mes galime unikalai daryti Anos Archyve, yra saugoti daugybę kūrinių kopijų, dideliu mastu. Mes galime rinkti straipsnius, knygas, žurnalus ir daugiau, ir platinti juos dideliais kiekiais. Šiuo metu tai darome per torrentus, bet tikslios technologijos nesvarbios ir laikui bėgant keisis. Svarbiausia yra gauti daug kopijų, paskirstytų visame pasaulyje. Ši citata iš daugiau nei 200 metų senumo vis dar skamba tiesa: Trumpa pastaba apie viešąją sritį. Kadangi Anos Archyvas unikalai orientuojasi į veiklas, kurios daugelyje pasaulio vietų yra neteisėtos, mes nesivarginame su plačiai prieinamomis kolekcijomis, tokiomis kaip viešosios srities knygos. Teisinės institucijos dažnai jau gerai rūpinasi tuo. Tačiau yra aplinkybių, kurios kartais verčia mus dirbti su viešai prieinamomis kolekcijomis: - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Saugojimo išlaidos toliau eksponentiškai mažėja 3. Informacijos tankio patobulinimai Šiuo metu saugome knygas tokiuose formatuose, kokius jie mums pateikiami. Žinoma, jie yra suspausti, bet dažnai tai vis dar yra dideli puslapių skenavimai ar nuotraukos. Iki šiol vienintelės galimybės sumažinti mūsų kolekcijos bendrą dydį buvo per agresyvesnį suspaudimą arba deduplikaciją. Tačiau norint gauti pakankamai reikšmingų sutaupymų, abu būdai yra per daug prarandantys kokybę mūsų skoniui. Stiprus nuotraukų suspaudimas gali padaryti tekstą vos įskaitomą. O deduplikacijai reikia didelio pasitikėjimo, kad knygos yra visiškai vienodos, kas dažnai yra per daug netikslu, ypač jei turinys yra tas pats, bet skenavimai atlikti skirtingomis progomis. Visada buvo trečias variantas, bet jo kokybė buvo tokia prasta, kad niekada jo nesvarstėme: <strong>OCR, arba optinis simbolių atpažinimas</strong>. Tai yra procesas, kai nuotraukos paverčiamos paprastu tekstu, naudojant AI, kad atpažintų simbolius nuotraukose. Įrankiai tam jau seniai egzistuoja ir yra gana geri, bet „gana geri“ nėra pakankamai gerai išsaugojimo tikslais. Tačiau naujausi daugiarūšiai giluminio mokymosi modeliai padarė itin greitą pažangą, nors vis dar už didelę kainą. Tikimės, kad tiek tikslumas, tiek kainos dramatiškai pagerės artimiausiais metais, iki taško, kai tai taps realistiška taikyti visai mūsų bibliotekai. Kai tai įvyks, mes tikriausiai vis dar išsaugosime originalius failus, bet papildomai galėtume turėti daug mažesnę mūsų bibliotekos versiją, kurią dauguma žmonių norės atkartoti. Esmė ta, kad pats neapdorotas tekstas suspaudžiamas dar geriau ir yra daug lengviau deduplikuojamas, suteikiant mums dar daugiau sutaupymų. Apskritai, nėra nerealistiška tikėtis bent 5-10 kartų sumažinti bendrą failų dydį, galbūt net daugiau. Net ir su konservatyviu 5 kartų sumažinimu, po 10 metų mes žiūrėtume į <strong>1 000–3 000 USD, net jei mūsų biblioteka padidėtų tris kartus</strong>. Rašymo metu, <a %(diskprices)s>diskų kainos</a> už TB yra apie 12 USD už naujus diskus, 8 USD už naudotus diskus ir 4 USD už juostą. Jei esame konservatyvūs ir žiūrime tik į naujus diskus, tai reiškia, kad petabaito saugojimas kainuoja apie 12 000 USD. Jei manome, kad mūsų biblioteka patrigubės nuo 900 TB iki 2,7 PB, tai reikštų 32 400 USD, kad atkurtume visą mūsų biblioteką. Pridėjus elektros, kitų aparatūros išlaidų ir panašiai, suapvalinkime iki 40 000 USD. Arba su juosta labiau kaip 15 000–20 000 USD. Viena vertus, <strong>15 000–40 000 USD už visos žmonijos žinias yra puikus sandoris</strong>. Kita vertus, šiek tiek brangu tikėtis daugybės pilnų kopijų, ypač jei norėtume, kad tie žmonės taip pat toliau sėtų savo torrentus kitų naudai. Tai yra šiandien. Bet progresas žengia į priekį: Kietųjų diskų kainos už TB per pastaruosius 10 metų buvo maždaug sumažintos trečdaliu ir tikėtina, kad toliau mažės panašiu tempu. Juostos atrodo einančios panašia trajektorija. SSD kainos krenta dar greičiau ir gali pralenkti HDD kainas iki dešimtmečio pabaigos. Jei tai pasitvirtins, po 10 metų galime tikėtis, kad mūsų visos kolekcijos atkūrimas (1/3) kainuos tik 5 000–13 000 USD arba net mažiau, jei mūsų kolekcija mažiau augs. Nors tai vis dar daug pinigų, tai bus pasiekiama daugeliui žmonių. Ir tai gali būti dar geriau dėl kito punkto… Anna’s Archive dažnai klausiama, kaip galime teigti, kad išsaugosime savo kolekcijas amžinai, kai bendras dydis jau artėja prie 1 petabaito (1000 TB) ir vis dar auga. Šiame straipsnyje pažvelgsime į mūsų filosofiją ir pamatysime, kodėl kitas dešimtmetis yra kritinis mūsų misijai išsaugoti žmonijos žinias ir kultūrą. Kritinis langas Jei šios prognozės yra tikslios, mums <strong>reikia tik palaukti keletą metų</strong>, kol visa mūsų kolekcija bus plačiai atkartota. Taigi, kaip sakė Thomas Jefferson, „padėta už nelaimės ribų“. Deja, LLM atsiradimas ir jų duomenų alkani mokymai privertė daugelį autorių teisių turėtojų gintis. Dar labiau nei jie jau buvo. Daugelis svetainių daro sunkiau nuskaityti ir archyvuoti, vyksta bylos, o tuo tarpu fizinės bibliotekos ir archyvai toliau yra apleidžiami. Galime tikėtis, kad šios tendencijos toliau blogės, ir daugelis darbų bus prarasti dar prieš jiems patekus į viešąją sritį. <strong>Esame išsaugojimo revoliucijos išvakarėse, bet <q>prarasto negalima atkurti.</q></strong> Turime kritinį langą apie 5-10 metų, per kurį vis dar gana brangu valdyti šešėlinę biblioteką ir sukurti daug atkartojimų visame pasaulyje, ir per kurį prieiga dar nėra visiškai uždaryta. Jei galime peržengti šį langą, tada iš tiesų išsaugosime žmonijos žinias ir kultūrą amžinai. Neturėtume leisti, kad šis laikas būtų švaistomas. Neturėtume leisti, kad šis kritinis langas užsidarytų mums. Pirmyn. Kritinis šešėlinių bibliotekų langas Kaip galime teigti, kad išsaugosime savo kolekcijas amžinai, kai jos jau artėja prie 1 PB? Kolekcija Šiek tiek daugiau informacijos apie kolekciją. <a %(duxiu)s>Duxiu</a> yra didžiulė skenuotų knygų duomenų bazė, sukurta <a %(chaoxing)s>SuperStar Digital Library Group</a>. Dauguma jų yra akademinės knygos, skenuotos tam, kad būtų prieinamos skaitmeniniu būdu universitetams ir bibliotekoms. Mūsų anglakalbei auditorijai <a %(library_princeton)s>Princeton</a> ir <a %(guides_lib_uw)s>Vašingtono universitetas</a> turi geras apžvalgas. Taip pat yra puikus straipsnis, suteikiantis daugiau konteksto: <a %(doi)s>„Kinų knygų skaitmeninimas: SuperStar DuXiu Scholar paieškos variklio atvejo analizė“</a> (ieškokite Annos Archyve). Duxiu knygos jau seniai buvo piratuojamos Kinijos internete. Paprastai jos parduodamos už mažiau nei dolerį perpardavėjų. Jos dažniausiai platinamos naudojant Kinijos „Google Drive“ atitikmenį, kuris dažnai buvo nulaužtas, kad būtų galima saugoti daugiau duomenų. Kai kurios techninės detalės pateikiamos <a %(github_duty_machine)s>čia</a> ir <a %(github_821_github_io)s>čia</a>. Nors knygos buvo pusiau viešai platinamos, jas gana sunku gauti dideliais kiekiais. Tai buvo aukštai mūsų darbų sąraše, ir tam skyrėme kelis mėnesius pilno darbo laiko. Tačiau neseniai neįtikėtinas, nuostabus ir talentingas savanoris susisiekė su mumis, pranešdamas, kad jau atliko visą šį darbą — didelėmis išlaidomis. Jie pasidalino visa kolekcija su mumis, nesitikėdami nieko mainais, išskyrus ilgalaikio išsaugojimo garantiją. Tikrai nepaprasta. Jie sutiko prašyti pagalbos šiuo būdu, kad kolekcija būtų OCR'inta. Kolekcija sudaro 7 543 702 failus. Tai daugiau nei Library Genesis negrožinės literatūros (apie 5,3 milijono). Bendras failų dydis yra apie 359TB (326TiB) dabartine forma. Esame atviri kitiems pasiūlymams ir idėjoms. Tiesiog susisiekite su mumis. Apsilankykite Annos Archyve, kad sužinotumėte daugiau apie mūsų kolekcijas, išsaugojimo pastangas ir kaip galite padėti. Ačiū! Pavyzdiniai puslapiai Norėdami įrodyti, kad turite gerą procesą, pateikiame keletą pavyzdinių puslapių, nuo kurių pradėti, iš knygos apie superlaidininkus. Jūsų procesas turėtų tinkamai apdoroti matematiką, lenteles, diagramas, išnašas ir pan. Siųskite savo apdorotus puslapius į mūsų el. paštą. Jei jie atrodys gerai, mes atsiųsime jums daugiau privačiai, ir tikimės, kad galėsite greitai paleisti savo procesą ir ant jų. Kai būsime patenkinti, galėsime sudaryti sandorį. - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kinų versija 中文版</a>, <a %(news_ycombinator)s>Diskutuoti Hacker News</a> Tai trumpas tinklaraščio įrašas. Ieškome įmonės ar institucijos, kuri padėtų mums su OCR ir teksto ištraukimais didžiulei kolekcijai, kurią įsigijome, mainais už išskirtinę ankstyvą prieigą. Po embargo laikotarpio, žinoma, išleisime visą kolekciją. Aukštos kokybės akademinis tekstas yra labai naudingas LLM mokymui. Nors mūsų kolekcija yra kinų kalba, tai turėtų būti naudinga ir anglų LLM mokymui: modeliai, atrodo, koduoja koncepcijas ir žinias nepriklausomai nuo šaltinio kalbos. Tam reikia ištraukti tekstą iš skenų. Ką gauna Annos Archyvas? Pilno teksto paiešką knygose savo vartotojams. Kadangi mūsų tikslai sutampa su LLM kūrėjų tikslais, ieškome bendradarbio. Esame pasirengę suteikti jums <strong>išskirtinę ankstyvą prieigą prie šios kolekcijos dideliais kiekiais 1 metams</strong>, jei galite tinkamai atlikti OCR ir teksto ištraukimą. Jei esate pasirengę pasidalinti visu savo proceso kodu su mumis, mes būtume pasirengę pratęsti kolekcijos embargą. Išskirtinė prieiga LLM įmonėms prie didžiausios pasaulyje kinų negrožinės literatūros knygų kolekcijos. <em><strong>Trumpai:</strong> Annos Archyvas įsigijo unikalią 7,5 milijono / 350TB kinų negrožinės literatūros knygų kolekciją — didesnę nei Library Genesis. Esame pasirengę suteikti LLM įmonei išskirtinę prieigą mainais už aukštos kokybės OCR ir teksto ištraukimą.</em> Sistemos architektūra Tarkime, kad radote keletą įmonių, kurios nori talpinti jūsų svetainę, nesustabdydamos jūsų — pavadinkime jas „laisvę mylinčiais tiekėjais“ 😄. Greitai pastebėsite, kad viską talpinti su jais yra gana brangu, todėl galbūt norėsite rasti keletą „pigių tiekėjų“ ir ten atlikti tikrąjį talpinimą, per laisvę mylinčius tiekėjus. Jei tai padarysite teisingai, pigūs tiekėjai niekada nesužinos, ką jūs talpinate, ir niekada negaus jokių skundų. Su visais šiais tiekėjais yra rizika, kad jie vis tiek jus uždarys, todėl jums taip pat reikia atsargumo. Mums to reikia visuose mūsų krūvos lygiuose. Viena šiek tiek laisvę mylinti įmonė, kuri užėmė įdomią poziciją, yra Cloudflare. Jie <a %(blog_cloudflare)s>teigė</a>, kad jie nėra talpinimo tiekėjas, o paslauga, kaip interneto paslaugų teikėjas. Todėl jie nėra taikomi DMCA ar kitiems pašalinimo prašymams ir perduoda bet kokius prašymus jūsų faktiniam talpinimo tiekėjui. Jie netgi ėjo į teismą, kad apsaugotų šią struktūrą. Todėl mes galime juos naudoti kaip dar vieną talpinimo ir apsaugos sluoksnį. Cloudflare nepriima anonimiškų mokėjimų, todėl galime naudoti tik jų nemokamą planą. Tai reiškia, kad negalime naudoti jų apkrovos balansavimo ar atsarginio plano funkcijų. Todėl mes <a %(annas_archive_l255)s>įgyvendinome tai patys</a> domeno lygiu. Puslapio įkrovos metu naršyklė patikrina, ar dabartinis domenas vis dar prieinamas, ir jei ne, perrašo visas URL į kitą domeną. Kadangi Cloudflare talpina daugelį puslapių, tai reiškia, kad vartotojas gali patekti į mūsų pagrindinį domeną, net jei tarpinis serveris neveikia, o tada kitame paspaudime būti perkeltas į kitą domeną. Mes vis dar turime spręsti įprastus veiklos rūpesčius, tokius kaip serverio sveikatos stebėjimas, klaidų registravimas galinėje ir priekinėje dalyje ir pan. Mūsų atsarginė architektūra leidžia didesnį patikimumą ir šioje srityje, pavyzdžiui, paleidžiant visiškai kitą serverių rinkinį viename iš domenų. Mes netgi galime paleisti senesnes kodo ir duomenų rinkinių versijas šiame atskirame domene, jei pagrindinėje versijoje nepastebima kritinė klaida. Mes taip pat galime apsidrausti nuo Cloudflare pasisukimo prieš mus, pašalindami jį iš vieno iš domenų, pavyzdžiui, šio atskiro domeno. Galimos įvairios šių idėjų permutacijos. Išvada Buvo įdomi patirtis mokytis, kaip sukurti tvirtą ir atsparią šešėlinės bibliotekos paieškos sistemą. Yra daugybė detalių, kurias galima pasidalinti vėlesniuose įrašuose, tad praneškite, ką norėtumėte sužinoti daugiau! Kaip visada, ieškome aukų, kad palaikytume šį darbą, tad būtinai apsilankykite Anna’s Archive aukojimo puslapyje. Taip pat ieškome kitų paramos formų, tokių kaip dotacijos, ilgalaikiai rėmėjai, didelės rizikos mokėjimo tiekėjai, galbūt net (skoningos!) reklamos. Jei norite prisidėti savo laiku ir įgūdžiais, visada ieškome kūrėjų, vertėjų ir pan. Dėkojame už jūsų susidomėjimą ir palaikymą. Inovacijų žetonai Pradėkime nuo mūsų technologijų krūvos. Ji yra tyčia nuobodi. Mes naudojame Flask, MariaDB ir ElasticSearch. Tai tiesiogine prasme viskas. Paieška iš esmės yra išspręsta problema, ir mes neketiname jos iš naujo išrasti. Be to, turime išleisti savo <a %(mcfunley)s>inovacijų žetonus</a> kažkam kitam: kad mūsų nepašalintų valdžios institucijos. Taigi, kiek teisėtas ar neteisėtas yra Anos Archyvas? Tai daugiausia priklauso nuo teisinės jurisdikcijos. Dauguma šalių tiki tam tikra autorių teisių forma, o tai reiškia, kad žmonėms ar įmonėms suteikiamas išskirtinis monopolija tam tikrų rūšių kūriniams tam tikrą laikotarpį. Beje, Anos Archyve mes tikime, kad nors yra tam tikra nauda, bendrai autorių teisės yra neigiamas dalykas visuomenei — bet tai jau kita istorija. Šis išskirtinis monopolija tam tikriems kūriniams reiškia, kad nelegalu bet kam už šio monopolio ribų tiesiogiai platinti tuos kūrinius — įskaitant mus. Tačiau Anos Archyvas yra paieškos variklis, kuris tiesiogiai neplatina tų kūrinių (bent jau ne mūsų aiškioje svetainėje), todėl turėtume būti gerai, tiesa? Ne visai. Daugelyje jurisdikcijų ne tik nelegalu platinti autorių teisių saugomus kūrinius, bet ir nuorodų į vietas, kuriose tai daroma, pateikimas. Klasikinis pavyzdys yra Jungtinių Valstijų DMCA įstatymas. Tai yra griežčiausias spektro galas. Kitoje spektro pusėje teoriškai galėtų būti šalys, kuriose nėra jokių autorių teisių įstatymų, bet tokios iš tikrųjų neegzistuoja. Beveik kiekviena šalis turi tam tikrą autorių teisių įstatymą. Vykdymas yra kita istorija. Yra daug šalių, kurių vyriausybės nesirūpina autorių teisių įstatymų vykdymu. Taip pat yra šalių, esančių tarp dviejų kraštutinumų, kurios draudžia platinti autorių teisių saugomus kūrinius, bet nedraudžia nuorodų į tokius kūrinius. Kitas svarstymas yra įmonės lygmenyje. Jei įmonė veikia jurisdikcijoje, kuri nesirūpina autorių teisėmis, bet pati įmonė nenori prisiimti jokios rizikos, tada jie gali uždaryti jūsų svetainę, kai tik kas nors dėl jos skundžiasi. Galiausiai, didelis svarstymas yra mokėjimai. Kadangi turime likti anonimiški, negalime naudoti tradicinių mokėjimo būdų. Tai palieka mums kriptovaliutas, ir tik nedidelė dalis įmonių jas palaiko (yra virtualių debeto kortelių, apmokamų kriptovaliuta, bet jos dažnai nepriimamos). - Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Aš valdau <a %(wikipedia_annas_archive)s>Anos Archyvą</a>, didžiausią pasaulyje atvirojo kodo ne pelno siekiantį paieškos variklį <a %(wikipedia_shadow_library)s>šešėliniams bibliotekoms</a>, tokioms kaip Sci-Hub, Library Genesis ir Z-Library. Mūsų tikslas yra padaryti žinias ir kultūrą lengvai prieinamas, o galiausiai sukurti bendruomenę žmonių, kurie kartu archyvuoja ir saugo <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>visas pasaulio knygas</a>. Šiame straipsnyje parodysiu, kaip mes valdome šią svetainę, ir unikalius iššūkius, kurie kyla valdant svetainę su abejotinu teisiniu statusu, nes nėra „AWS šešėliniams labdaros fondams“. <em>Taip pat peržiūrėkite sesers straipsnį <a %(blog_how_to_become_a_pirate_archivist)s>Kaip tapti piratų archyvaru</a>.</em> Kaip valdyti šešėlinę biblioteką: operacijos Annos Archyve Nėra <q>AWS šešėliniams labdaros projektams,</q> tad kaip mes valdome Annos Archyvą? Įrankiai Programų serveris: Flask, MariaDB, ElasticSearch, Docker. Kūrimas: Gitlab, Weblate, Zulip. Serverio valdymas: Ansible, Checkmk, UFW. Onion statinis talpinimas: Tor, Nginx. Proxy serveris: Varnish. Pažvelkime, kokius įrankius naudojame visam tam pasiekti. Tai labai keičiasi, kai susiduriame su naujomis problemomis ir randame naujus sprendimus. Yra keletas sprendimų, dėl kurių mes svarstėme pirmyn ir atgal. Vienas iš jų yra komunikacija tarp serverių: anksčiau tam naudojome Wireguard, bet pastebėjome, kad kartais jis nustoja perduoti duomenis arba perduoda juos tik viena kryptimi. Tai nutiko su keliais skirtingais Wireguard nustatymais, kuriuos bandėme, tokiais kaip <a %(github_costela_wesher)s>wesher</a> ir <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Taip pat bandėme tuneliuoti prievadus per SSH, naudodami autossh ir sshuttle, bet susidūrėme su <a %(github_sshuttle)s>problemomis ten</a> (nors man vis dar neaišku, ar autossh kenčia nuo TCP-per-TCP problemų ar ne — man tai atrodo kaip netvarkingas sprendimas, bet galbūt jis iš tikrųjų yra tinkamas?). Vietoj to, grįžome prie tiesioginių ryšių tarp serverių, slėpdami, kad serveris veikia pigiuose tiekėjuose, naudodami IP filtravimą su UFW. Tai turi trūkumą, kad Docker neveikia gerai su UFW, nebent naudojate <code>network_mode: "host"</code>. Visa tai yra šiek tiek labiau linkę į klaidas, nes su maža konfigūracijos klaida galite atskleisti savo serverį internetui. Galbūt turėtume grįžti prie autossh — čia labai lauktume atsiliepimų. Mes taip pat svarstėme Varnish prieš Nginx. Šiuo metu mums patinka Varnish, bet jis turi savo keistenybių ir šiurkščių briaunų. Tas pats pasakytina apie Checkmk: mes jo nemylime, bet jis veikia šiuo metu. Weblate buvo gerai, bet ne neįtikėtina — kartais bijau, kad jis praras mano duomenis, kai bandau sinchronizuoti su mūsų git repo. Flask buvo geras apskritai, bet turi keletą keistų keistenybių, kurios kainavo daug laiko derinant, pavyzdžiui, konfigūruojant pasirinktinius domenus ar problemas su jo SqlAlchemy integracija. Iki šiol kiti įrankiai buvo puikūs: neturime rimtų skundų dėl MariaDB, ElasticSearch, Gitlab, Zulip, Docker ir Tor. Visi jie turėjo tam tikrų problemų, bet nieko pernelyg rimto ar laiko reikalaujančio. Bendruomenė Pirmasis iššūkis gali būti netikėtas. Tai nėra techninė problema ar teisinė problema. Tai psichologinė problema: darbas šešėlyje gali būti nepaprastai vienišas. Priklausomai nuo to, ką planuojate daryti, ir jūsų grėsmės modelio, gali tekti būti labai atsargiems. Viename spektro gale turime žmones kaip Alexandra Elbakyan*, Sci-Hub įkūrėją, kuri yra labai atvira apie savo veiklą. Tačiau ji yra didelėje rizikoje būti suimta, jei šiuo metu apsilankytų Vakarų šalyje, ir galėtų susidurti su dešimtmečiais kalėjimo. Ar tai rizika, kurią norėtumėte prisiimti? Mes esame kitame spektro gale; labai atsargūs, kad nepaliktume jokių pėdsakų, ir turime stiprią operacinę saugą. * Kaip minėta HN "ynno", Alexandra iš pradžių nenorėjo būti žinoma: "Jos serveriai buvo sukonfigūruoti taip, kad išduotų detalias PHP klaidų žinutes, įskaitant pilną klaidos šaltinio failo kelią, kuris buvo kataloge /home/<USER>" Taigi, naudokite atsitiktinius vartotojų vardus kompiuteriuose, kuriuos naudojate šiems dalykams, jei netyčia ką nors neteisingai sukonfigūruosite. Tačiau tas slaptumas turi psichologinę kainą. Dauguma žmonių mėgsta būti pripažinti už savo darbą, tačiau jūs negalite gauti jokio pripažinimo už tai realiame gyvenime. Net paprasti dalykai gali būti iššūkis, kaip draugai klausia, ką veikėte (tam tikru momentu "žaidimas su mano NAS / homelab" tampa nuobodus). Štai kodėl taip svarbu rasti bendruomenę. Galite paaukoti dalį operacinio saugumo, pasitikėdami labai artimais draugais, kuriais žinote, kad galite giliai pasitikėti. Net ir tada būkite atsargūs, kad nieko nerašytumėte, jei jie turėtų perduoti savo el. laiškus valdžios institucijoms arba jei jų įrenginiai būtų pažeisti kitaip. Dar geriau yra rasti bendraminčių piratų. Jei jūsų artimi draugai nori prisijungti prie jūsų, puiku! Priešingu atveju galite rasti kitų internete. Deja, tai vis dar nišinė bendruomenė. Iki šiol radome tik keletą kitų, kurie yra aktyvūs šioje srityje. Geros pradinės vietos atrodo Library Genesis forumai ir r/DataHoarder. Archive Team taip pat turi panašiai mąstančių asmenų, nors jie veikia pagal įstatymus (net jei kai kuriose pilkose įstatymų srityse). Tradicinės "warez" ir piratavimo scenos taip pat turi žmonių, kurie mąsto panašiai. Esame atviri idėjoms, kaip skatinti bendruomenę ir tyrinėti idėjas. Drąsiai rašykite mums Twitter ar Reddit. Galbūt galėtume surengti kokį nors forumą ar pokalbių grupę. Vienas iššūkis yra tas, kad tai gali būti lengvai cenzūruojama naudojant įprastas platformas, todėl turėtume tai talpinti patys. Taip pat yra kompromisas tarp šių diskusijų visiško viešumo (didesnis potencialus įsitraukimas) ir jų privatumo (neleisti potencialiems "taikiniams" žinoti, kad ketiname juos surinkti). Turėsime apie tai pagalvoti. Praneškite, jei jus tai domina! Išvada Tikimės, kad tai bus naudinga naujai pradedantiems piratų archyvarams. Mes džiaugiamės galėdami jus pasveikinti šiame pasaulyje, todėl nedvejodami susisiekite. Išsaugokime kuo daugiau pasaulio žinių ir kultūros, ir atkartokime jas plačiai. Projektai 4. Duomenų pasirinkimas Dažnai galite naudoti metaduomenis, kad nustatytumėte pagrįstą duomenų dalį, kurią reikia atsisiųsti. Net jei galiausiai norite atsisiųsti visus duomenis, gali būti naudinga pirmiausia teikti pirmenybę svarbiausiems elementams, jei jus aptiks ir bus pagerintos gynybos, arba jei reikės įsigyti daugiau diskų, arba tiesiog dėl to, kad kažkas kitas atsiranda jūsų gyvenime, kol dar negalite atsisiųsti visko. Pavyzdžiui, kolekcijoje gali būti keli to paties pagrindinio šaltinio (pvz., knygos ar filmo) leidimai, kur vienas pažymėtas kaip geriausios kokybės. Pirmiausia išsaugoti tuos leidimus būtų labai prasminga. Galiausiai galite norėti išsaugoti visus leidimus, nes kai kuriais atvejais metaduomenys gali būti neteisingai pažymėti arba gali būti nežinomų kompromisų tarp leidimų (pavyzdžiui, „geriausias leidimas“ gali būti geriausias daugeliu atžvilgių, bet blogesnis kitais, pvz., filmas turi didesnę raišką, bet trūksta subtitrų). Taip pat galite ieškoti savo metaduomenų bazėje, kad rastumėte įdomių dalykų. Koks yra didžiausias failas, kuris yra talpinamas, ir kodėl jis toks didelis? Koks yra mažiausias failas? Ar yra įdomių ar netikėtų modelių tam tikrose kategorijose, kalbose ir pan.? Ar yra dublikatų ar labai panašių pavadinimų? Ar yra modelių, kada duomenys buvo pridėti, pavyzdžiui, viena diena, kai buvo pridėta daug failų vienu metu? Dažnai galite daug sužinoti, žiūrėdami į duomenų rinkinį skirtingais būdais. Mūsų atveju, mes pašalinome Z-Library knygų dublikatus, palygindami su md5 maišos kodais Library Genesis, taip sutaupydami daug atsisiuntimo laiko ir disko vietos. Tačiau tai yra gana unikali situacija. Daugeliu atvejų nėra išsamių duomenų bazių, kuriose būtų nurodyta, kurie failai jau yra tinkamai išsaugoti kitų piratų. Tai savaime yra didžiulė galimybė kažkam ten. Būtų puiku turėti reguliariai atnaujinamą apžvalgą apie tokius dalykus kaip muzika ir filmai, kurie jau plačiai sėjami torrent svetainėse, todėl jie yra mažesnės svarbos įtraukti į piratų veidrodžius. 6. Paskirstymas Jūs turite duomenis, taip suteikdami jums pasaulio pirmąjį piratų veidrodį jūsų tikslui (greičiausiai). Daugeliu atžvilgių sunkiausia dalis jau baigta, bet rizikingiausia dalis dar priešakyje. Juk iki šiol buvote nepastebimi; skraidėte po radarais. Viskas, ką turėjote padaryti, buvo naudoti gerą VPN visą laiką, neįvedant savo asmeninių duomenų jokiose formose (akivaizdu), ir galbūt naudojant specialią naršyklės sesiją (ar net kitą kompiuterį). Dabar turite paskirstyti duomenis. Mūsų atveju pirmiausia norėjome grąžinti knygas į Library Genesis, bet greitai atradome sunkumus tame (grožinės ir negrožinės literatūros rūšiavimas). Taigi nusprendėme paskirstyti naudojant Library Genesis stiliaus torrentus. Jei turite galimybę prisidėti prie esamo projekto, tai gali sutaupyti daug laiko. Tačiau šiuo metu nėra daug gerai organizuotų piratų veidrodžių. Tarkime, nusprendžiate patys paskirstyti torrentus. Stenkitės, kad tie failai būtų maži, kad juos būtų lengva atkartoti kitose svetainėse. Tada turėsite patys sėti torrentus, vis dar išlikdami anonimiški. Galite naudoti VPN (su arba be prievado persiuntimo) arba mokėti su maišytais Bitcoin už Seedbox. Jei nežinote, ką reiškia kai kurie iš šių terminų, turėsite daug skaityti, nes svarbu suprasti rizikos kompromisus čia. Galite talpinti pačius torrent failus esamose torrent svetainėse. Mūsų atveju, mes pasirinkome iš tikrųjų talpinti svetainę, nes taip pat norėjome aiškiai skleisti savo filosofiją. Galite tai padaryti patys panašiu būdu (mes naudojame Njalla savo domenams ir talpinimui, mokėdami su maišytais Bitcoin), bet taip pat drąsiai susisiekite su mumis, kad mes talpintume jūsų torrentus. Mes siekiame sukurti išsamų piratų veidrodžių indeksą laikui bėgant, jei ši idėja prigis. Kalbant apie VPN pasirinkimą, apie tai jau daug rašyta, todėl tiesiog pakartosime bendrą patarimą rinktis pagal reputaciją. Tikros teismo patikrintos bežurnalinės politikos su ilga privatumo apsaugos istorija, mūsų nuomone, yra mažiausios rizikos pasirinkimas. Atkreipkite dėmesį, kad net jei viską darote teisingai, niekada negalite pasiekti nulio rizikos. Pavyzdžiui, kai dalijatės savo torrentais, labai motyvuotas valstybės veikėjas gali tikriausiai stebėti įeinančius ir išeinančius duomenų srautus VPN serveriuose ir išsiaiškinti, kas jūs esate. Arba galite tiesiog padaryti klaidą. Mes tikriausiai jau padarėme ir vėl padarysime. Laimei, valstybės nelabai rūpinasi <em>piratavimu</em>. Kiekvienam projektui reikia priimti sprendimą, ar jį publikuoti naudojant tą pačią tapatybę kaip anksčiau, ar ne. Jei toliau naudosite tą patį vardą, ankstesnių projektų operacinio saugumo klaidos gali sugrįžti ir pakenkti jums. Tačiau publikuojant skirtingais vardais, jūs nesukuriate ilgalaikės reputacijos. Mes pasirinkome stiprų operacinį saugumą nuo pat pradžių, kad galėtume toliau naudoti tą pačią tapatybę, tačiau nedvejodami publikuosime kitu vardu, jei padarysime klaidą arba jei to reikalauja aplinkybės. Žodžio skleidimas gali būti sudėtingas. Kaip sakėme, tai vis dar nišinė bendruomenė. Iš pradžių paskelbėme Reddit, bet tikrai sulaukėme dėmesio Hacker News. Šiuo metu rekomenduojame paskelbti keliose vietose ir pažiūrėti, kas nutiks. Ir vėlgi, susisiekite su mumis. Norėtume skleisti žinią apie daugiau piratų archyvavimo pastangų. 1. Domeno pasirinkimas / filosofija Nėra trūkumo žinių ir kultūrinio paveldo, kurį reikia išsaugoti, kas gali būti pribloškiantis. Todėl dažnai naudinga skirti akimirką ir pagalvoti, koks gali būti jūsų indėlis. Kiekvienas turi skirtingą požiūrį į tai, bet čia yra keletas klausimų, kuriuos galite sau užduoti: Mūsų atveju ypatingai rūpėjome ilgalaikiu mokslo išsaugojimu. Žinojome apie Library Genesis ir kaip ji buvo visiškai atkartota daugybę kartų naudojant torrentus. Mums patiko ši idėja. Vieną dieną vienas iš mūsų bandė rasti mokslinius vadovėlius Library Genesis, bet jų nerado, kas sukėlė abejonių dėl jos išsamumo. Tada ieškojome tų vadovėlių internete ir radome juos kitose vietose, kas pasėjo mūsų projekto sėklą. Net prieš sužinodami apie Z-Library, turėjome idėją ne bandyti surinkti visas tas knygas rankiniu būdu, bet sutelkti dėmesį į esamų kolekcijų atkartojimą ir jų grąžinimą į Library Genesis. Kokius įgūdžius turite, kuriuos galite panaudoti savo naudai? Pavyzdžiui, jei esate interneto saugumo ekspertas, galite rasti būdų, kaip įveikti IP blokavimus saugiems tikslams. Jei puikiai organizuojate bendruomenes, galbūt galite suburti žmones aplink tikslą. Tačiau naudinga žinoti šiek tiek programavimo, net jei tik tam, kad viso proceso metu išlaikytumėte gerą operacinį saugumą. Kokia būtų didelės naudos sritis, į kurią verta sutelkti dėmesį? Jei ketinate praleisti X valandas piratų archyvavimui, kaip galite gauti didžiausią „naudos už pinigus“? Kokie yra unikalūs būdai, kaip apie tai galvojate? Galbūt turite įdomių idėjų ar požiūrių, kurių kiti galėjo nepastebėti. Kiek laiko turite tam? Mūsų patarimas būtų pradėti nuo mažų projektų ir imtis didesnių, kai įgysite patirties, tačiau tai gali tapti viską užvaldančiu. Kodėl jus tai domina? Kas jus jaudina? Jei galėtume surinkti grupę žmonių, kurie visi archyvuotų tai, kas jiems ypatingai rūpi, tai apimtų daug! Jūs žinosite daug daugiau nei vidutinis žmogus apie savo aistrą, pavyzdžiui, kokie duomenys yra svarbūs išsaugoti, kokios yra geriausios kolekcijos ir internetinės bendruomenės ir pan. 3. Metadata nuskaitymas Pridėjimo/modifikavimo data: kad galėtumėte grįžti vėliau ir atsisiųsti failus, kurių anksčiau neatsisiuntėte (nors dažnai tam galite naudoti ir ID arba maišos kodą). Hash (md5, sha1): norint patvirtinti, kad tinkamai atsisiuntėte failą. ID: gali būti vidinis ID, bet ID kaip ISBN ar DOI taip pat yra naudingi. Failo pavadinimas / vieta Aprašymas, kategorija, žymos, autoriai, kalba ir kt. Dydis: norint apskaičiuoti, kiek disko vietos jums reikia. Pakalbėkime šiek tiek techniškiau. Norėdami iš tikrųjų nuskaityti metadata iš svetainių, mes laikėmės gana paprasto požiūrio. Naudojame Python skriptus, kartais curl, ir MySQL duomenų bazę rezultatams saugoti. Nenaudojome jokios sudėtingos nuskaitymo programinės įrangos, kuri galėtų žemėlapiuoti sudėtingas svetaines, nes iki šiol mums reikėjo nuskaityti tik vieno ar dviejų tipų puslapius, tiesiog numeruojant per ID ir analizuojant HTML. Jei nėra lengvai numeruojamų puslapių, tada jums gali prireikti tinkamo naršyklės, kuri bandytų rasti visus puslapius. Prieš pradėdami nuskaityti visą svetainę, pabandykite tai padaryti rankiniu būdu. Pereikite per kelias dešimtis puslapių patys, kad suprastumėte, kaip tai veikia. Kartais jau tokiu būdu susidursite su IP blokavimu ar kitokiu įdomiu elgesiu. Tas pats galioja ir duomenų nuskaitymui: prieš gilindamiesi į šį tikslą, įsitikinkite, kad iš tikrųjų galite efektyviai atsisiųsti jo duomenis. Norėdami apeiti apribojimus, galite išbandyti keletą dalykų. Ar yra kitų IP adresų ar serverių, kurie talpina tuos pačius duomenis, bet neturi tų pačių apribojimų? Ar yra API galinių taškų, kurie neturi apribojimų, nors kiti turi? Koks atsisiuntimo greitis blokuoja jūsų IP ir kiek laiko? Arba ar jūs neblokuojate, bet sulėtinate? Ką daryti, jei sukuriate vartotojo paskyrą, kaip tada keičiasi dalykai? Ar galite naudoti HTTP/2, kad išlaikytumėte atviras jungtis, ir ar tai padidina puslapių užklausų greitį? Ar yra puslapių, kurie išvardija kelis failus vienu metu, ir ar ten pateikta informacija yra pakankama? Dalykai, kuriuos tikriausiai norėsite išsaugoti, apima: Paprastai tai atliekame dviem etapais. Pirmiausia atsisiunčiame neapdorotus HTML failus, dažniausiai tiesiai į MySQL (kad išvengtume daugybės mažų failų, apie kuriuos kalbėsime toliau). Tada, atskirame žingsnyje, peržiūrime tuos HTML failus ir išskirstome juos į tikras MySQL lenteles. Tokiu būdu nereikia visko iš naujo atsisiųsti, jei pastebite klaidą savo analizės kode, nes galite tiesiog perdirbti HTML failus su nauju kodu. Taip pat dažnai lengviau paralelizuoti apdorojimo etapą, taip sutaupant laiko (ir galite rašyti apdorojimo kodą, kol vyksta duomenų rinkimas, o ne rašyti abu etapus vienu metu). Galiausiai, atkreipkite dėmesį, kad kai kuriems tikslams metaduomenų rinkimas yra viskas, kas yra. Yra didžiulės metaduomenų kolekcijos, kurios nėra tinkamai išsaugotos. Pavadinimas Domeno pasirinkimas / filosofija: Kur norite apytiksliai sutelkti dėmesį ir kodėl? Kokios yra jūsų unikalios aistros, įgūdžiai ir aplinkybės, kurias galite panaudoti savo naudai? Tikslinės kolekcijos pasirinkimas: Kurią konkrečią kolekciją atkartosite? Metaduomenų rinkimas: Failų informacijos katalogavimas, neatsisiunčiant pačių (dažnai daug didesnių) failų. Duomenų pasirinkimas: Remiantis metaduomenimis, susiaurinama, kurie duomenys šiuo metu yra svarbiausi archyvavimui. Tai gali būti viskas, bet dažnai yra pagrįstas būdas taupyti vietą ir pralaidumą. Duomenų rinkimas: Tiesioginis duomenų gavimas. Platinimas: Supakavimas į torrentus, paskelbimas kažkur, žmonių įtraukimas į platinimą. 5. Duomenų rinkimas Dabar esate pasiruošę iš tikrųjų atsisiųsti duomenis dideliais kiekiais. Kaip minėta anksčiau, šiuo metu jau turėtumėte rankiniu būdu atsisiųsti daugybę failų, kad geriau suprastumėte tikslinio objekto elgesį ir apribojimus. Tačiau vis tiek bus staigmenų, kai iš tikrųjų pradėsite atsisiųsti daug failų vienu metu. Mūsų patarimas čia yra laikytis paprastumo. Pradėkite tiesiog atsisiųsdami daugybę failų. Galite naudoti Python, o tada išplėsti iki kelių gijų. Tačiau kartais dar paprasčiau yra generuoti Bash failus tiesiai iš duomenų bazės ir tada paleisti kelis iš jų keliuose terminalo languose, kad padidintumėte mastą. Greitas techninis triukas, kurį verta paminėti čia, yra OUTFILE naudojimas MySQL, kurį galite rašyti bet kur, jei išjungiate "secure_file_priv" mysqld.cnf (ir būtinai išjunkite/apeikite AppArmor, jei naudojate Linux). Mes saugome duomenis paprastuose kietuosiuose diskuose. Pradėkite nuo to, ką turite, ir lėtai plėskite. Gali būti sunku galvoti apie šimtų TB duomenų saugojimą. Jei tai yra situacija, su kuria susiduriate, tiesiog išleiskite gerą dalį pirmiausia ir savo pranešime paprašykite pagalbos saugant likusią dalį. Jei norite patys įsigyti daugiau kietųjų diskų, r/DataHoarder turi gerų išteklių, kaip gauti gerus pasiūlymus. Stenkitės per daug nesijaudinti dėl sudėtingų failų sistemų. Lengva įkristi į triušio urvą, nustatant tokius dalykus kaip ZFS. Viena techninė detalė, į kurią reikia atkreipti dėmesį, yra ta, kad daugelis failų sistemų blogai tvarko daugybę failų. Mes radome paprastą sprendimą - sukurti kelis katalogus, pvz., skirtingiems ID diapazonams ar maišos prefiksams. Atsisiuntę duomenis, būtinai patikrinkite failų vientisumą naudodami metaduomenyse esančius maišos kodus, jei jie yra. 2. Tikslų pasirinkimas Prieinama: nenaudoja daugybės apsaugos sluoksnių, kad užkirstų kelią jūsų metadata ir duomenų nuskaitymui. Speciali įžvalga: turite specialios informacijos apie šį tikslą, pavyzdžiui, kažkaip turite specialią prieigą prie šios kolekcijos arba sugebėjote įveikti jų gynybą. Tai nėra būtina (mūsų būsimas projektas nieko ypatingo nedaro), bet tikrai padeda! Didelė Taigi, turime sritį, į kurią žiūrime, dabar kurią konkrečią kolekciją atkartoti? Yra keletas dalykų, kurie sudaro gerą tikslą: Kai radome savo mokslinius vadovėlius kitose svetainėse nei Library Genesis, bandėme išsiaiškinti, kaip jie pateko į internetą. Tada radome Z-Library ir supratome, kad nors dauguma knygų pirmiausia ten nepasirodo, jos galiausiai ten atsiduria. Sužinojome apie jos santykį su Library Genesis ir (finansinę) paskatų struktūrą bei pranašesnę vartotojo sąsają, kurios abi padarė ją daug išsamesne kolekcija. Tada atlikome preliminarų metadata ir duomenų nuskaitymą ir supratome, kad galime apeiti jų IP atsisiuntimo apribojimus, pasinaudodami vieno iš mūsų narių specialia prieiga prie daugybės proxy serverių. Kai tyrinėjate skirtingus tikslus, jau svarbu slėpti savo pėdsakus naudojant VPN ir vienkartinius el. pašto adresus, apie kuriuos kalbėsime vėliau. Unikali: dar nėra gerai padengta kitų projektų. Kai vykdome projektą, jis turi kelis etapus: Tai nėra visiškai nepriklausomi etapai, ir dažnai įžvalgos iš vėlesnio etapo grąžina jus į ankstesnį etapą. Pavyzdžiui, metu, kai renkate metadata, galite suprasti, kad pasirinktas taikinys turi gynybinius mechanizmus, viršijančius jūsų įgūdžių lygį (pvz., IP blokavimas), todėl grįžtate ir randate kitą taikinį. - Ana ir komanda (<a %(reddit)s>Reddit</a>) Visos knygos gali būti parašytos apie <em>kodėl</em> skaitmeninio išsaugojimo apskritai ir piratų archyvavimo ypač, bet leiskite mums pateikti trumpą įvadą tiems, kurie nėra labai susipažinę. Pasaulis gamina daugiau žinių ir kultūros nei bet kada anksčiau, bet taip pat daugiau jų prarandama nei bet kada anksčiau. Žmonija daugiausia pasitiki korporacijomis, tokiomis kaip akademiniai leidėjai, srautinio perdavimo paslaugos ir socialinės žiniasklaidos įmonės, šiuo paveldu, ir jos dažnai neįrodė esą puikūs globėjai. Pažiūrėkite dokumentinį filmą "Digital Amnesia" arba bet kokią Jasono Scotto kalbą. Yra keletas institucijų, kurios gerai archyvuoja tiek, kiek gali, bet jos yra saistomos įstatymų. Kaip piratai, mes esame unikalioje padėtyje archyvuoti kolekcijas, kurių jie negali paliesti, dėl autorių teisių vykdymo ar kitų apribojimų. Mes taip pat galime atkartoti kolekcijas daug kartų visame pasaulyje, taip padidindami tinkamo išsaugojimo galimybes. Kol kas nesigilinsime į diskusijas apie intelektinės nuosavybės privalumus ir trūkumus, įstatymų pažeidimo moralę, cenzūros apmąstymus ar prieigos prie žinių ir kultūros klausimą. Su visa tai išsprendus, pasinerkime į <em>kaip</em>. Pasidalinsime, kaip mūsų komanda tapo piratų archyvarais, ir pamokomis, kurias išmokome pakeliui. Yra daug iššūkių, kai pradedate šią kelionę, ir tikimės, kad galime padėti jums per kai kuriuos iš jų. Kaip tapti piratų archyvaru Pirmasis iššūkis gali būti netikėtas. Tai nėra techninė problema ar teisinė problema. Tai psichologinė problema. Prieš pradedant, dvi naujienos apie Piratų bibliotekos atkartojimą (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>): Gavome labai dosnių aukų. Pirmoji buvo 10 tūkst. dolerių iš anoniminio asmens, kuris taip pat rėmė "bookwarrior", originalų Library Genesis įkūrėją. Ypatinga padėka bookwarrior už šios aukos palengvinimą. Antroji buvo dar 10 tūkst. dolerių iš anoniminio donoro, kuris susisiekė po mūsų paskutinio leidimo ir buvo įkvėptas padėti. Taip pat turėjome keletą mažesnių aukų. Labai ačiū už jūsų dosnų palaikymą. Turime keletą įdomių naujų projektų, kuriuos tai palaikys, tad sekite naujienas. Turėjome techninių sunkumų su mūsų antrojo leidimo dydžiu, bet mūsų torrentai dabar veikia ir sėja. Taip pat gavome dosnų pasiūlymą iš anoniminio asmens sėti mūsų kolekciją jų labai greituose serveriuose, todėl atliekame specialų įkėlimą į jų mašinas, po kurio visi kiti, kurie atsisiunčia kolekciją, turėtų pastebėti didelį greičio pagerėjimą. Tinklaraščio įrašai Sveiki, aš esu Anna. Sukūriau <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, didžiausią pasaulyje šešėlinę biblioteką. Tai yra mano asmeninis tinklaraštis, kuriame aš ir mano komandos nariai rašome apie piratavimą, skaitmeninį išsaugojimą ir dar daugiau. Susisiekite su manimi <a %(reddit)s>Reddit</a>. Atkreipkite dėmesį, kad ši svetainė yra tik tinklaraštis. Čia talpiname tik savo žodžius. Čia nėra talpinami ar susieti jokie torrentai ar kiti autorių teisių saugomi failai. <strong>Biblioteka</strong> - Kaip ir dauguma bibliotekų, mes daugiausia dėmesio skiriame rašytinei medžiagai, pavyzdžiui, knygoms. Ateityje galime plėstis į kitų tipų medijas. <strong>Veidrodis</strong> - Mes esame griežtai esamų bibliotekų veidrodis. Mes orientuojamės į išsaugojimą, o ne į knygų paieškos ir atsisiuntimo (prieigos) palengvinimą ar didelės bendruomenės, kuri prisideda naujomis knygomis (šaltiniais), kūrimą. <strong>Piratų</strong> - Mes sąmoningai pažeidžiame autorių teisių įstatymus daugelyje šalių. Tai leidžia mums daryti tai, ko teisėtos organizacijos negali: užtikrinti, kad knygos būtų atkartotos plačiai. <em>Mes nesusiejame failų iš šio tinklaraščio. Prašome rasti juos patys.</em> - Ana ir komanda (<a %(reddit)s>Reddit</a>) Šis projektas (REDAGUOTA: perkeltas į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>) siekia prisidėti prie žmonijos žinių išsaugojimo ir išlaisvinimo. Mes darome savo mažą ir kuklų indėlį, sekdami didžiųjų pėdomis. Šio projekto dėmesys iliustruojamas jo pavadinimu: Pirmoji biblioteka, kurią atkartojome, yra Z-Library. Tai populiari (ir neteisėta) biblioteka. Jie paėmė Library Genesis kolekciją ir padarė ją lengvai ieškomą. Be to, jie tapo labai veiksmingi pritraukiant naujas knygų įnašas, skatindami prisidedančius vartotojus įvairiomis privilegijomis. Šiuo metu jie negrąžina šių naujų knygų Library Genesis. Ir skirtingai nei Library Genesis, jie nepadaro savo kolekcijos lengvai atkartojamos, kas trukdo plačiam išsaugojimui. Tai svarbu jų verslo modeliui, nes jie ima mokestį už prieigą prie savo kolekcijos dideliais kiekiais (daugiau nei 10 knygų per dieną). Mes nedarome moralinių sprendimų dėl mokesčio už didelės apimties prieigą prie neteisėtos knygų kolekcijos. Neabejotina, kad Z-Library sėkmingai išplėtė prieigą prie žinių ir pritraukė daugiau knygų. Mes tiesiog esame čia, kad atliktume savo dalį: užtikrinti ilgalaikį šios privačios kolekcijos išsaugojimą. Norėtume pakviesti jus padėti išsaugoti ir išlaisvinti žmonijos žinias atsisiunčiant ir dalijantis mūsų torrentais. Daugiau informacijos apie tai, kaip duomenys yra organizuoti, rasite projekto puslapyje. Taip pat labai norėtume pakviesti jus prisidėti savo idėjomis, kokias kolekcijas atkartoti toliau ir kaip tai padaryti. Kartu galime pasiekti daug. Tai tik mažas indėlis tarp daugybės kitų. Ačiū už viską, ką darote. Pristatome Piratų Bibliotekos Veidrodį: Išsaugome 7TB knygų (kurios nėra Libgen) 10% of žmonijos rašytinio paveldo išsaugota amžinai <strong>Google.</strong> Juk jie atliko šį tyrimą Google Books. Tačiau jų metadata nėra prieinama masiškai ir gana sunkiai nuskaityti. <strong>Įvairios individualios bibliotekų sistemos ir archyvai.</strong> Yra bibliotekų ir archyvų, kurie nebuvo indeksuoti ir sujungti nė vieno iš aukščiau paminėtų, dažnai todėl, kad jie yra nepakankamai finansuojami arba dėl kitų priežasčių nenori dalintis savo duomenimis su Open Library, OCLC, Google ir t. t. Dauguma jų turi skaitmeninius įrašus, prieinamus per internetą, ir jie dažnai nėra labai gerai apsaugoti, todėl jei norite padėti ir smagiai praleisti laiką mokydamiesi apie keistas bibliotekų sistemas, tai yra puikios pradžios taškai. <strong>ISBNdb.</strong> Tai yra šio tinklaraščio įrašo tema. ISBNdb renka įvairių svetainių knygų metadata, ypač kainų duomenis, kuriuos jie tada parduoda knygų pardavėjams, kad jie galėtų nustatyti savo knygų kainas pagal likusią rinką. Kadangi ISBN šiais laikais yra gana universalūs, jie efektyviai sukūrė „tinklalapį kiekvienai knygai“. <strong>Open Library.</strong> Kaip minėta anksčiau, tai yra jų visa misija. Jie surinko didžiulius bibliotekų duomenų kiekius iš bendradarbiaujančių bibliotekų ir nacionalinių archyvų ir toliau tai daro. Jie taip pat turi savanorius bibliotekininkus ir techninę komandą, kurie bando pašalinti dublikatus ir pažymėti juos visokiomis metadata. Geriausia, kad jų duomenų rinkinys yra visiškai atviras. Galite tiesiog <a %(openlibrary)s>atsisiųsti jį</a>. <strong>WorldCat.</strong> Tai yra svetainė, kurią valdo ne pelno siekianti OCLC, kuri parduoda bibliotekų valdymo sistemas. Jie renka knygų metadata iš daugybės bibliotekų ir pateikia ją per WorldCat svetainę. Tačiau jie taip pat uždirba pinigus parduodami šiuos duomenis, todėl jie nėra prieinami masiškai atsisiųsti. Jie turi keletą ribotų masinių duomenų rinkinių, kuriuos galima atsisiųsti, bendradarbiaujant su konkrečiomis bibliotekomis. 1. Pagal tam tikrą pagrįstą „amžinai“ apibrėžimą. ;) 2. Žinoma, žmonijos rašytinis paveldas yra daug daugiau nei knygos, ypač šiais laikais. Šio įrašo ir mūsų nesenų leidimų dėlei mes sutelkiame dėmesį į knygas, bet mūsų interesai siekia toliau. 3. Apie Aaroną Swartzą galima pasakyti daug daugiau, bet mes norėjome jį trumpai paminėti, nes jis vaidina svarbų vaidmenį šioje istorijoje. Laikui bėgant, daugiau žmonių gali pirmą kartą susidurti su jo vardu ir vėliau patys pasinerti į šią temą. <strong>Fizinės kopijos.</strong> Akivaizdu, kad tai nėra labai naudinga, nes tai tik tos pačios medžiagos dublikatai. Būtų šaunu, jei galėtume išsaugoti visas žmonių pastabas knygose, kaip Fermato garsiosios „rašliavos paraštėse“. Bet deja, tai liks archyvaro svajone. <strong>„Leidiniai“.</strong> Čia skaičiuojate kiekvieną unikalią knygos versiją. Jei kas nors apie ją skiriasi, kaip skirtingas viršelis ar skirtinga įžanga, tai skaičiuojama kaip skirtingas leidinys. <strong>Failai.</strong> Dirbant su šešėlinėmis bibliotekomis, tokiomis kaip Library Genesis, Sci-Hub ar Z-Library, yra papildomas aspektas. Gali būti keli to paties leidinio skenavimai. Ir žmonės gali sukurti geresnes esamų failų versijas, nuskaitydami tekstą naudojant OCR arba taisydami puslapius, kurie buvo nuskaityti kampu. Mes norime skaičiuoti šiuos failus kaip vieną leidinį, kas reikalautų geros metadata arba dublikavimo pašalinimo naudojant dokumentų panašumo matavimus. <strong>„Kūriniai“.</strong> Pavyzdžiui, „Haris Poteris ir paslapčių kambarys“ kaip loginė sąvoka, apimanti visas jo versijas, kaip skirtingi vertimai ir perleidimai. Tai yra tam tikras naudingas apibrėžimas, bet gali būti sunku nubrėžti ribą, kas skaičiuojama. Pavyzdžiui, mes tikriausiai norime išsaugoti skirtingus vertimus, nors perleidimai su tik nedideliais skirtumais gali būti ne tokie svarbūs. - Ana ir komanda (<a %(reddit)s>Reddit</a>) Su Piratų bibliotekos veidrodžiu (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>), mūsų tikslas yra surinkti visas pasaulio knygas ir išsaugoti jas amžinai.<sup>1</sup> Tarp mūsų Z-Library torrentų ir originalių Library Genesis torrentų turime 11 783 153 failus. Bet kiek tai iš tikrųjų yra? Jei tinkamai pašalintume dublikatus, kokį procentą visų pasaulio knygų mes išsaugojome? Mes tikrai norėtume turėti kažką panašaus: Pradėkime nuo apytikslių skaičių: Tiek Z-Library/Libgen, tiek Open Library yra daug daugiau knygų nei unikalių ISBN. Ar tai reiškia, kad dauguma tų knygų neturi ISBN, ar tiesiog trūksta ISBN metadata? Tikriausiai galime atsakyti į šį klausimą, derindami automatinį atitikimą pagal kitus atributus (pavadinimą, autorių, leidėją ir kt.), įtraukdami daugiau duomenų šaltinių ir ištraukdami ISBN iš pačių knygų skenų (Z-Library/Libgen atveju). Kiek iš tų ISBN yra unikalūs? Tai geriausiai iliustruoja Veno diagrama: Tiksliau tariant: Mus nustebino, kaip mažai yra persidengimo! ISBNdb turi didžiulį kiekį ISBN, kurie neatsiranda nei Z-Library, nei Open Library, ir tas pats galioja (nors ir mažesniu, bet vis tiek reikšmingu mastu) kitoms dviem. Tai kelia daug naujų klausimų. Kiek automatinis atitikimas padėtų pažymėti knygas, kurios nebuvo pažymėtos ISBN? Ar būtų daug atitikimų ir todėl padidėtų persidengimas? Taip pat, kas nutiktų, jei įtrauktume 4-ą ar 5-ą duomenų rinkinį? Kiek persidengimo matytume tada? Tai suteikia mums pradžios tašką. Dabar galime pažvelgti į visus ISBN, kurie nebuvo Z-Library duomenų rinkinyje ir kurie neatitinka pavadinimo/autoriaus laukų. Tai gali padėti išsaugoti visas pasaulio knygas: pirmiausia skenuojant internetą dėl skenų, tada realiame gyvenime skenuojant knygas. Pastarasis netgi galėtų būti finansuojamas minios, arba skatinamas „premijomis“ iš žmonių, kurie norėtų matyti tam tikras knygas skaitmenizuotas. Visa tai yra istorija kitam kartui. Jei norite padėti su bet kuriuo iš šių dalykų — tolesne analize; daugiau metadata rinkimu; daugiau knygų paieška; knygų OCR’inimu; tai daryti kitose srityse (pvz., straipsniai, audioknygos, filmai, TV laidos, žurnalai) ar net dalį šių duomenų padaryti prieinamus tokiems dalykams kaip ML / didelių kalbos modelių mokymas — prašome susisiekti su manimi (<a %(reddit)s>Reddit</a>). Jei jus ypač domina duomenų analizė, mes dirbame, kad mūsų duomenų rinkiniai ir skriptai būtų prieinami lengviau naudojamu formatu. Būtų puiku, jei galėtumėte tiesiog nukopijuoti užrašų knygelę ir pradėti su tuo žaisti. Galiausiai, jei norite paremti šį darbą, apsvarstykite galimybę paaukoti. Tai yra visiškai savanoriška operacija, ir jūsų indėlis daro didelį skirtumą. Kiekviena dalis padeda. Šiuo metu priimame aukas kriptovaliuta; žr. Anna’s Archive aukojimo puslapį. Norint apskaičiuoti procentą, mums reikia vardiklio: bendro kada nors išleistų knygų skaičiaus.<sup>2</sup> Prieš Google Books žlugimą, vienas iš projekto inžinierių, Leonidas Taycheris, <a %(booksearch_blogspot)s>bandė įvertinti</a> šį skaičių. Jis pateikė — su šypsena — 129 864 880 („bent jau iki sekmadienio“). Jis įvertino šį skaičių, sukūręs vieningą visų pasaulio knygų duomenų bazę. Tam jis sujungė skirtingus duomenų rinkinius ir sujungė juos įvairiais būdais. Trumpai tariant, yra dar vienas asmuo, bandęs kataloguoti visas pasaulio knygas: Aaronas Swartz, velionis skaitmeninis aktyvistas ir Reddit bendraįkūrėjas.<sup>3</sup> Jis <a %(youtube)s>pradėjo Open Library</a> su tikslu „vienas tinklalapis kiekvienai kada nors išleistai knygai“, sujungdamas duomenis iš daugybės skirtingų šaltinių. Jis sumokėjo aukščiausią kainą už savo skaitmeninio išsaugojimo darbą, kai buvo patrauktas baudžiamojon atsakomybėn už masinį akademinių straipsnių atsisiuntimą, kas lėmė jo savižudybę. Be abejo, tai yra viena iš priežasčių, kodėl mūsų grupė yra pseudoniminė ir kodėl mes esame labai atsargūs. Open Library vis dar herojiškai valdo Internet Archive žmonės, tęsdami Aarono palikimą. Prie to grįšime vėliau šiame įraše. Google tinklaraščio įraše Taycheris aprašo kai kuriuos iššūkius, susijusius su šio skaičiaus įvertinimu. Pirma, kas sudaro knygą? Yra keletas galimų apibrėžimų: „Leidiniai“ atrodo praktiškiausias apibrėžimas, kas yra „knygos“. Patogiai, šis apibrėžimas taip pat naudojamas unikaliems ISBN numeriams priskirti. ISBN, arba Tarptautinis standartinis knygos numeris, dažnai naudojamas tarptautinėje prekyboje, nes jis integruotas su tarptautine brūkšninių kodų sistema („Tarptautinis straipsnio numeris“). Jei norite parduoti knygą parduotuvėse, jai reikia brūkšninio kodo, todėl gaunate ISBN. Taycherio tinklaraščio įraše minėta, kad nors ISBN yra naudingi, jie nėra universalūs, nes jie buvo tikrai priimti tik septintojo dešimtmečio viduryje ir ne visur pasaulyje. Vis dėlto, ISBN tikriausiai yra plačiausiai naudojamas knygų leidinių identifikatorius, todėl tai yra geriausias mūsų pradžios taškas. Jei galime rasti visus pasaulio ISBN, gauname naudingą sąrašą, kurios knygos dar turi būti išsaugotos. Taigi, kur gauti duomenis? Yra keletas esamų pastangų, kurios bando sudaryti visų pasaulio knygų sąrašą: Šiame įraše džiaugiamės galėdami pranešti apie nedidelį leidimą (palyginti su ankstesniais Z-Library leidimais). Mes nuskaityme didžiąją dalį ISBNdb ir padarėme duomenis prieinamus torrentams Piratų bibliotekos veidrodžio svetainėje (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>; mes čia tiesiogiai nesusiesime, tiesiog ieškokite). Tai yra apie 30,9 milijono įrašų (20 GB kaip <a %(jsonlines)s>JSON Lines</a>; 4,4 GB suspausta). Jų svetainėje jie teigia, kad iš tikrųjų turi 32,6 milijono įrašų, todėl mes galbūt kažkaip praleidome kai kuriuos, arba <em>jie</em> gali daryti kažką neteisingai. Bet kokiu atveju, kol kas mes neatskleisime, kaip tai padarėme — paliksime tai kaip užduotį skaitytojui. ;-) Ką mes pasidalinsime, tai preliminari analizė, siekiant priartėti prie pasaulio knygų skaičiaus įvertinimo. Mes peržiūrėjome tris duomenų rinkinius: šį naują ISBNdb duomenų rinkinį, mūsų originalų metadata leidimą, kurį nuskaityme iš Z-Library šešėlinės bibliotekos (kuri apima Library Genesis), ir Open Library duomenų iškrovą. ISBNdb išmetimas, arba Kiek Knygų Yra Išsaugota Amžinai? Jei tinkamai pašalintume šešėlinių bibliotekų failų dublikatus, kokį procentą visų pasaulio knygų mes išsaugojome? Naujienos apie <a %(wikipedia_annas_archive)s>Annos Archyvą</a>, didžiausią tikrai atvirą biblioteką žmonijos istorijoje. <em>WorldCat pertvarkymas</em> Duomenys <strong>Formatas?</strong> <a %(blog)s>Annos Archyvo Konteineriai (AAC)</a>, kurie iš esmės yra <a %(jsonlines)s>JSON Lines</a>, suspausti su <a %(zstd)s>Zstandard</a>, plius kai kurios standartizuotos semantikos. Šie konteineriai apima įvairių tipų įrašus, remiantis skirtingais mūsų įdiegtais išgremžimais. Prieš metus mes <a %(blog)s>pradėjome</a> atsakyti į šį klausimą: <strong>Kokį procentą knygų šešėlinės bibliotekos išsaugojo visam laikui?</strong> Pažvelkime į kai kurią pagrindinę informaciją apie duomenis: Kai knyga patenka į atviro duomenų šešėlinę biblioteką, tokią kaip <a %(wikipedia_library_genesis)s>Library Genesis</a>, o dabar ir <a %(wikipedia_annas_archive)s>Annos Archyvas</a>, ji yra veidrodinė visame pasaulyje (per torrentus), taip praktiškai išsaugant ją amžinai. Norint atsakyti į klausimą, koks procentas knygų buvo išsaugotas, reikia žinoti vardiklį: kiek iš viso egzistuoja knygų? Ir idealiu atveju mes turime ne tik skaičių, bet ir tikrąją metadata. Tada galime ne tik palyginti jas su šešėlinėmis bibliotekomis, bet ir <strong>sukurti likusių knygų, kurias reikia išsaugoti, sąrašą!</strong> Galėtume net pradėti svajoti apie bendruomenės pastangas eiti per šį sąrašą. Mes išgremžėme <a %(wikipedia_isbndb_com)s>ISBNdb</a> ir atsisiuntėme <a %(openlibrary)s>Open Library duomenų rinkinį</a>, tačiau rezultatai buvo nepatenkinami. Pagrindinė problema buvo ta, kad ISBN sutapimų buvo labai mažai. Žiūrėkite šią Venn diagramą iš <a %(blog)s>mūsų tinklaraščio įrašo</a>: Mus labai nustebino, kaip mažai sutapimų buvo tarp ISBNdb ir Open Library, kurios abi laisvai įtraukia duomenis iš įvairių šaltinių, tokių kaip interneto išgremžimai ir bibliotekų įrašai. Jei jos abi gerai atlieka darbą ieškodamos daugumos ISBN, jų apskritimai tikrai turėtų turėti reikšmingą sutapimą arba viena būtų kitos dalis. Tai privertė mus susimąstyti, kiek knygų <em>visiškai nepatenka į šiuos apskritimus</em>? Mums reikia didesnės duomenų bazės. Tada mes nukreipėme savo žvilgsnį į didžiausią knygų duomenų bazę pasaulyje: <a %(wikipedia_worldcat)s>WorldCat</a>. Tai yra nuosavybinė duomenų bazė, kurią valdo ne pelno siekianti organizacija <a %(wikipedia_oclc)s>OCLC</a>, kuri renka metadata įrašus iš bibliotekų visame pasaulyje, mainais už tai, kad suteikia toms bibliotekoms prieigą prie viso duomenų rinkinio ir leidžia joms pasirodyti galutinių vartotojų paieškos rezultatuose. Nors OCLC yra ne pelno siekianti organizacija, jų verslo modelis reikalauja saugoti jų duomenų bazę. Na, atsiprašome, draugai iš OCLC, mes viską atiduodame. :-) Per pastaruosius metus mes kruopščiai išgremžėme visus WorldCat įrašus. Iš pradžių mums pasisekė. WorldCat ką tik pradėjo savo svetainės visišką pertvarkymą (2022 m. rugpjūtį). Tai apėmė reikšmingą jų užpakalinių sistemų pertvarkymą, įvedant daugybę saugumo spragų. Mes iš karto pasinaudojome šia galimybe ir per kelias dienas sugebėjome išgremžti šimtus milijonų (!) įrašų. Po to saugumo spragos buvo lėtai taisomos viena po kitos, kol paskutinė, kurią radome, buvo užtaisyta maždaug prieš mėnesį. Tuo metu mes jau turėjome beveik visus įrašus ir siekėme tik šiek tiek aukštesnės kokybės įrašų. Taigi, manome, kad laikas išleisti! 1,3B WorldCat nuskaitymas <em><strong>TL;DR:</strong> Anna’s Archive nuskaityta visa WorldCat (didžiausia pasaulyje bibliotekos metadata kolekcija), kad būtų sudarytas knygų, kurias reikia išsaugoti, TODO sąrašas.</em> WorldCat Įspėjimas: šis tinklaraščio įrašas yra pasenęs. Nusprendėme, kad IPFS dar nėra pasirengęs plačiam naudojimui. Vis dar nuorodų į failus IPFS iš Annos Archyvo, kai įmanoma, bet nebepriglobosime jų patys ir nerekomenduojame kitiems atkartoti naudojant IPFS. Prašome peržiūrėti mūsų Torrents puslapį, jei norite padėti išsaugoti mūsų kolekciją. Padėkite sėti Z-Library IPFS Atsisiuntimas iš partnerių serverių SciDB Paskola iš išorės Išorinis skolinimasis (spausdinimas išjungtas) Atsisiuntimas iš išorės Tyrinėti metaduomenis Yra torrentuose Atgal  (+%(num)s premija) neapmokėtas apmokėtas atšauktas pasibaigęs laukiama Anna dėl patvirtinimo negalioja Tekstas žemiau tęsiasi anglų kalba. Eiti Atstatyti Pirmyn Paskutinis Jei jūsų el. pašto adresas neveikia Libgen forumuose, rekomenduojame naudoti <a %(a_mail)s>Proton Mail</a> (nemokamas). Taip pat galite <a %(a_manual)s>rankiniu būdu pateikti užklausą</a>, kad paskyra būtų suaktyvinta. (gali prireikti <a %(a_browser)s>naršyklės patvirtinimo</a> — neriboti atsisiuntimai!) Greitasis partnerio serveris #%(number)s (rekomenduojama) (šiek tiek greičiau, bet su eile) (nereikalingas naršyklės patvirtinimas) (nėra naršyklės patikrinimo ar eilės) (be eilės, bet gali būti labai lėtas) Lėtasis partnerio serveris #%(number)s Garso knyga Komiksų knyga Knyga (grožinė literatūra) Knyga (negrožinė literatūra) Knyga (nežinoma) Žurnalo straipsnis Žurnalas Muzikinė partitūra Kita Standartų dokumentas Ne visus puslapius pavyko konvertuoti į PDF Pažymėta kaip neteisinga Libgen.li Nematoma Libgen.li Nematoma Libgen.rs grožinėje literatūroje Nematoma Libgen.rs negrožinėje literatūroje Nepavyko paleisti exiftool šiam failui Z-Library pažymėta kaip „blogas failas“ Nėra Z-Library Z-Library pažymėta kaip „šlamštas“ Dokumentas neatidaromas (pvz., sugadintas dokumentas, DRM) Autorių teisių pretenzija Atsisiuntimo problemos (pvz., negalima prisijungti, klaidos pranešimas, labai lėtas) Neteisingi metaduomenys (pvz., pavadinimas, aprašas, viršelio vaizdas) Kita Prasta kokybė (pvz., formatavimo problemos, prasta skenavimo kokybė, trūkstami puslapiai) Šlamštas / failas turėtų būti ištrintas (pvz., reklama, piktnaudžiavimo turinys) %(amount)s (%(amount_usd)s) %(amount)s viso %(amount)s (%(amount_usd)s) viso Nepriekaištingas knygėdis Stebuklingas bibliotekininkas Akinantis duomenų rinkėjas Garbingas archyvininkas Papildomi atsisiuntimai Cerlalc Čekiški metaduomenys DuXiu 读秀 EBSCOhost eBook Index Google knygos Goodreads HathiTrust IA IA kontroliuojamas skaitmeninis skolinimas ISBNdb ISBN GRP Libgen.li Išskyrus „scimag“ Libgen.rs Grožinė ir negrožinė literatūra Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Rusijos valstybinė biblioteka Sci-Hub Per Libgen.li „scimag” Sci-Hub / Libgen „scimag“ Trantor Įkėlimai į AA Z-Library Z-Library Chinese Pavadinimas, autorius, DOI, ISBN, MD5, … Ieškoti Autorius Aprašymas ir metaduomenų komentarai Leidimas Originalus failo pavadinimas Leidėjas (ieškoti konkretaus lauko) Pavadinimas Išleidimo metai Techninės detalės Ši moneta turi didesnę nei įprasta minimalią sumą. Prašome pasirinkti kitą trukmę arba kitą monetą. Užklausa negalėjo būti įvykdyta. Bandykite dar kartą po kelių minučių, o jei problema išlieka, susisiekite su mumis adresu %(email)s ir prisekite ekrano nuotrauką. Įvyko nežinoma klaida. Prašome susisiekti su mumis adresu %(email)s ir prisegti ekrano nuotrauką. Mokėjimo apdorojimo klaida. Palaukite akimirką ir bandykite dar kartą. Jei problema išlieka ilgiau nei 24 valandas, susisiekite su mumis adresu %(email)s ir pridėkite ekrano nuotrauką. Renkame lėšų didžiausios komiksų šešėlinės bibliotekos <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">atsarginėms kopijoms sukurti</a>. Ačiū už Jūsų pagalbą! <a href="/donate">Aukokite</a>. Jeigu aukoti negalite, apsvarstykite galimybę mus paremti kitais būdais: pasidalinant apie šią svetainę su draugais ir sekant mus <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> ar <a href="https://t.me/annasarchiveorg">Telegram</a> platformose. Nerašykite mums dėl <a %(a_request)s>knygų prašymų</a><br> ar smulkių (<10 tūkst.) <a %(a_upload)s>įkėlimų</a>. Anos archyvas DMCA / autorinės teisės Susisiekti Reddit Alternatyvos SLUM (%(unaffiliated)s) nepriklausomas Annės Archyvui reikia jūsų pagalbos! Jei aukosite dabar, gausite <strong>dvigubai</strong> daugiau greitų atsisiuntimų. Daugelis bando mus sunaikinti, bet mes kovojame atgal. Jei paaukosite šį mėnesį, gausite <strong>dvigubai</strong> daugiau greitų atsisiuntimų. Galioja iki šio mėnesio pabaigos. Žmonių žinių išsaugojimas: puiki dovana šventei! Narystės bus atitinkamai pratęstos. Partnerių serveriai yra nepasiekiami dėl prieglobos uždarymo. Jie netrukus vėl turėtų veikti. Norėdami padidinti Annės Archyvo atsparumą, ieškome savanorių, kurie galėtų valdyti veidrodžius. Mes turime naują aukojimo metodą pasiekiamą: %(method_name)s. Pagalvokite apie %(donate_link_open_tag)saukojimą</a> - nėra pigu išlaikyti šią svetainę ir jūsų auka tikrai padaro skirtumą. Labai ačiū. Pakvieskite draugą, ir jūs abu gausite %(percentage)s%% papildomų greitų atsisiuntimų! Nustebinkite mylimą žmogų, suteikite jam sąskaitą su naryste. Tobula Valentino dienos dovana! Sužinoti daugiau… Paskyra Veikla Išplėstinė Anos blogas ↗ Anos programa ↗ beta Koduotės tyrinėtojas Duomenų rinkiniai Aukoti Parsiųsti failai DUK Pradžia Tobulinti metaduomenis LLM duomenys Prisijungti / Registruotis Mano aukos Viešas profilis Ieškoti Saugumas Torentai Versti ↗ Savanorystė ir atlyginimai Naujausi parsisiuntimai: 📚&nbsp;Didžiausia pasaulyje atvirųjų duomenų biblioteka. ⭐️&nbsp;Atspindi Sci-Hub, Library Genesis, Z-Library ir ne tik. 📈&nbsp;%(book_any)s knygos, %(journal_article)s straipsniai, %(book_comic)s komiksai, %(magazine)s žurnalai — amžinai išsaugoti.  ir  ir ne tik DuXiu Internet Archive Skolinimo Biblioteka LibGen 📚&nbsp;Didžiausia iš tiesų atvira biblioteka žmonijos istorijoje. 📈&nbsp;%(book_count)s&nbsp;knygų, %(paper_count)s&nbsp;straipsnių — amžinai išsaugoti. ⭐️&nbsp;Mes atspindime %(libraries)s. Mes renkame ir viešai pateikiame %(scraped)s. Visas mūsų kodas ir duomenys yra visiškai atviro kodo. OpenLib Sci-Hub ,  📚 Didžiausia pasaulyje atvirųjų duomenų biblioteka.<br>⭐️ Atspindi Scihub, Libgen, Zlib ir ne tik. Z-Lib Anna's Archive Negaliojanti užklausa. Apsilankykite %(websites)s. Didžiausia pasaulyje atvirojo kodo atvirųjų duomenų biblioteka. Apima Sci-Hub, Library Genesis, Z-Library ir kt. Ieškoti Anna's Archive Anna's Archive Prašome perkrauti puslapį ir bandyti dar kartą. <a %(a_contact)s>Susisiekite su mumis</a>, jei problema išlieka kelias valandas. 🔥 Problema įkeliant šį puslapį <li>1. Sekite mus <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, ar <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Platinkite žinią apie Anna's Archive Twitter, Reddit, Tiktok, Instagram, vietinėje kavinėje, bibliotekoje ar kažin kur jūs einate! Mes netikime informacijos uždarimu - jeigu būsime sustabdyti, atsirasime kitur, kadangi visas mūsų kodas ir duomenys yra visiškai atviri.</li><li>3. Jeigu galite <a href="/donate">paaukokite</a>.</li><li>4. Padėkite <a href="https://translate.annas-software.org/">išversti</a> mūsų svetainę į skirtingas kalbas. </li><li>5. Jeigu esate programuotojas, galite prisidėti prie mūsų <a href="https://annas-software.org/">atviro kodo</a>, arba bendrinant (angl. seeding) mūsų <a href="/datasets">torrent</a>.</li> 10. Sukurkite arba padėkite prižiūrėti Vikipedijos puslapį „Annos Archyvas“ savo kalba. 11. Mes ieškome galimybių talpinti mažas, skoningas reklamas. Jei norėtumėte reklamuotis Anna’s Archive, praneškite mums. 6. Jei esate saugumo tyrėjas, galime panaudoti jūsų įgūdžius tiek puolimui, tiek gynybai. Apsilankykite mūsų <a %(a_security)s>Saugumo</a> puslapyje. 7. Mes ieškome ekspertų mokėjimų anoniminiams prekybininkams srityje. Ar galite padėti mums pridėti patogesnių būdų aukoti? PayPal, WeChat, dovanų kortelės. Jei pažįstate ką nors, prašome susisiekti su mumis. Mes visada ieškome daugiau serverio pajėgumų. 9. Jūs galite padėti pranešdami apie failų problemas, palikdami komentarus ir kurdami sąrašus tiesiog šioje svetainėje. Taip pat galite padėti <a %(a_upload)s>įkeldami daugiau knygų</a> arba taisydami esamų knygų failų problemas ar formatavimą. Daugiau informacijos apie savanoriavimą rasite mūsų <a %(a_volunteering)s>Savanoriavimo ir atlygio</a> puslapyje. Mes stipriai tikime laisvu informacijos platinimu ir žinių bei kultūros išsaugojimu. Su šia paieškos sistema, mes statome ant milžinų pečių. We giliai gerbiame sunkų darbą atliktą žmonių kurie sukūrė įvairias šešėlines bibliotekas ir tikimės, kad ši paieškos sistema padidins jų pasiekiamumą. Norint sužinoti žinias apie mūsų progresą, sekite Anna <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> arba <a href="https://t.me/annasarchiveorg">Telegram</a>. Dėl klausimų galite susisiekti su Anna %(email)s. Paskyros ID: %(account_id)s Atsijungti ❌ Kažkas negerai atsitiko. Perkraukite puslapį ir bandykite dar kartą. ✅ Dabar esate atsijungęs. Perkraukite puslapį, kad vėl prisijungtumėte. Greiti atsisiuntimai (per pastarąsias 24 valandas): <strong>%(used)s / %(total)s</strong> Narystė: <strong>%(tier_name)s</strong> iki %(until_date)s <a %(a_extend)s>(pratęsti)</a> Galite sujungti kelias narystes (greiti atsisiuntimai per 24 valandas bus sumuojami). Narystė: <strong>Jokios</strong> <a %(a_become)s>(tapti nariu)</a> Susisiekite su Anna adresu %(email)s, jei norite pakelti savo narystę į aukštesnį lygį. Viešas profilis: %(profile_link)s Slaptas raktas (nesidalinkite!): %(secret_key)s rodyti Prisijunkite prie mūsų čia! Pasikelkite į <a %(a_tier)s>aukštesnį lygį</a>, kad prisijungtumėte prie mūsų grupės. Išskirtinė Telegram grupė: %(link)s Paskyra kurie atsisiuntimai? Prisijungti Nepraraskite savo kodo! Neteisingas slaptas kodas. Patikrinkite savo kodą ir bandykite dar kartą arba registruokitės naują paskyrą žemiau. Slaptas kodas Įveskite slaptą kodą norint prisijungti: Sena paskyra su elektroniniu paštu? Įveskite jo adresą <a %(a_open)s>šiame puslapyje</a>. Sukurti naują paskyrą Dar neturite paskyros? Registracija sėkminga! Jūsų slaptas kodas yra: <span %(span_key)s>%(key)s</span> Išsaugokite šitą kodą. Jeigu jį prarasite - neteksite prieigos prie paskyros. <li %(li_item)s><strong>Pažymėti.</strong> Galite pažymėti šį puslapį, kad gautumėte raktą.</li><li %(li_item)s><strong>Atsisiųsti.</strong> Spustelėkite < %(a_download)s>šią nuorodą</a>, kad atsisiųstumėte kodą.</li><li %(li_item)s><strong>Slaptažodžių tvarkyklė.</strong> Jūsų patogumui kodas yra iš anksto užpildytas aukščiau, todėl prisijungę galite išsaugoti slaptažodžių tvarkytuvėje.</li> Prisijungti / Registruotis Naršyklės patikrinimas Įspėjimas: kode yra neteisingų Unicode simbolių, ir jis gali netinkamai veikti įvairiose situacijose. Neapdorotas dvejetainis kodas gali būti dekoduotas iš base64 reprezentacijos URL. Aprašymas Etiketė Prefiksas Konkretaus kodo URL Interneto svetainė Kodai prasidedantys „%(prefix_label)s” Prašome nenuskaitynėti šių puslapių. Vietoj to rekomenduojame <a %(a_import)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes ir naudoti mūsų <a %(a_software)s>atvirojo kodo kodą</a>. Neapdoroti duomenys gali būti rankiniu būdu tyrinėjami per JSON failus, tokius kaip <a %(a_json_file)s>šis</a>. Mažiau nei %(count)s įrašų Bendras URL Kodų naršyklė Indeksas Tyrinėkite kodus, su kuriais įrašai yra pažymėti pagal priešdėlį. Stulpelyje „įrašai“ rodomas įrašų skaičius, pažymėtų kodais su nurodytu priešdėliu, kaip matoma paieškos sistemoje (įskaitant tik metaduomenų įrašus). Stulpelyje „kodai“ rodomas faktinių kodų su nurodytu priešdėliu skaičius. Žinomas kodo priešdėlis „%(key)s“ Daugiau… Prefiksas %(count)s įrašas atitinkantys „%(prefix_label)s” %(count)s įrašai atitinkantys „%(prefix_label)s” %(count)s įrašų atitinkantys „%(prefix_label)s” kodai įrašai „%%s“ bus pakeistas kodo verte Ieškoti Annos Archyve Kodai URL konkrečiam kodui: „%(url)s” Gali užtrukti, kol šis puslapis bus sugeneruotas, todėl reikalinga Cloudflare captcha. <a %(a_donate)s>Nariai</a> gali praleisti captcha. Piktnaudžiavimas praneštas: Geresnė versija Ar norite pranešti apie šį vartotoją dėl įžeidžiančio ar netinkamo elgesio? Failo problema: %(file_issue)s paslėptas komentaras Atsakyti Pranešti apie piktnaudžiavimą Jūs pranešėte apie šį vartotoją dėl piktnaudžiavimo. Autorių teisių pretenzijos šiuo el. paštu bus ignoruojamos; vietoj to naudokite formą. Rodyti el. paštą Labai laukiame jūsų atsiliepimų ir klausimų! Tačiau dėl didelio kiekio šlamšto ir nesąmoningų el. laiškų, prašome pažymėti langelius, kad patvirtintumėte, jog suprantate šias susisiekimo su mumis sąlygas. Bet kokie kiti būdai susisiekti su mumis dėl autorių teisių pretenzijų bus automatiškai ištrinti. Dėl DMCA / autorių teisių pretenzijų naudokite <a %(a_copyright)s>šią formą</a>. Kontaktinis el. paštas URL adresai Anna’s Archive (privaloma). Po vieną eilutėje. Prašome įtraukti tik URL adresus, kurie aprašo tą pačią knygos leidimo versiją. Jei norite pateikti pretenziją dėl kelių knygų ar kelių leidimų, prašome pateikti šią formą kelis kartus. Pretenzijos, kurios apima kelias knygas ar leidimus, bus atmestos. Adresas (privaloma) Aiškus šaltinio medžiagos aprašymas (privaloma) El. paštas (privaloma) Šaltinio medžiagos URL adresai, po vieną eilutėje (privaloma). Prašome įtraukti kuo daugiau, kad padėtumėte mums patvirtinti jūsų pretenziją (pvz., Amazon, WorldCat, Google Books, DOI). Šaltinio medžiagos ISBN (jei taikoma). Po vieną eilutėje. Prašome įtraukti tik tuos, kurie tiksliai atitinka leidimą, dėl kurio pranešate apie autorių teisių pažeidimą. Jūsų vardas (privaloma) ❌ Kažkas nepavyko. Prašome perkrautį puslapį ir bandyti dar kartą. ✅ Ačiū, kad pateikėte savo autorių teisių pažeidimo pretenziją. Mes ją peržiūrėsime kuo greičiau. Prašome atnaujinti puslapį, kad pateiktumėte kitą pretenziją. <a %(a_openlib)s>Open Library</a> šaltinio medžiagos URL adresai, po vieną eilutėje. Prašome skirti laiko paieškai Open Library, kad surastumėte savo šaltinio medžiagą. Tai padės mums patvirtinti jūsų pretenziją. Telefono numeris (privaloma) Pareikšimas ir parašas (privaloma) Pateikti pretenziją Jei turite DCMA ar kitą autorių teisių pažeidimo pretenziją, prašome užpildyti šią formą kuo tiksliau. Jei susidursite su kokiomis nors problemomis, susisiekite su mumis mūsų specialiu DMCA adresu: %(email)s. Atkreipkite dėmesį, kad pretenzijos, siunčiamos šiuo adresu, nebus apdorojamos, jis skirtas tik klausimams. Prašome naudoti žemiau esančią formą savo pretenzijoms pateikti. DMCA / Autorių teisių pažeidimo forma Pavyzdinis įrašas Annos Archyve Torrentai pagal Annos Archyvą Annos Archyvo Konteinerių formatas Skriptai metaduomenų importavimui Jei jus domina šio duomenų rinkinio atkartojimas <a %(a_archival)s>archyvavimo</a> ar <a %(a_llm)s>LLM mokymo</a> tikslais, prašome susisiekti su mumis. Paskutinį kartą atnaujinta: %(date)s Pagrindinė %(source)s svetainė Metaduomenų dokumentacija (dauguma laukų) Failai, atkartojami Annos Archyve: %(count)s (%(percent)s%%) Ištekliai Iš viso failų: %(count)s Bendras failų dydis: %(size)s Mūsų tinklaraščio įrašas apie šiuos duomenis <a %(duxiu_link)s>Duxiu</a> yra didžiulė nuskaitytų knygų duomenų bazė, sukurta <a %(superstar_link)s>SuperStar Digital Library Group</a>. Dauguma jų yra akademinės knygos, nuskaitytos siekiant jas padaryti prieinamas universitetams ir bibliotekoms skaitmeniniu formatu. Mūsų anglakalbei auditorijai <a %(princeton_link)s>Princeton</a> ir <a %(uw_link)s>Vašingtono universitetas</a> turi gerus apžvalginius straipsnius. Taip pat yra puikus straipsnis, suteikiantis daugiau informacijos: <a %(article_link)s>„Kinų knygų skaitmeninimas: SuperStar DuXiu Scholar paieškos variklio atvejo analizė“</a>. Duxiu knygos ilgą laiką buvo piratuojamos Kinijos internete. Paprastai jas perpardavėjai parduoda už mažiau nei dolerį. Jos dažniausiai platinamos naudojant Kinijos „Google Drive“ ekvivalentą, kuris dažnai buvo nulaužtas, kad būtų galima padidinti saugojimo vietą. Kai kurios techninės detalės pateikiamos <a %(link1)s>čia</a> ir <a %(link2)s>čia</a>. Nors knygos buvo pusiau viešai platinamos, jas gana sunku gauti dideliais kiekiais. Tai buvo aukštai mūsų darbų sąraše, ir tam skyrėme kelis mėnesius pilno darbo laiko. Tačiau 2023 m. pabaigoje neįtikėtinas, nuostabus ir talentingas savanoris susisiekė su mumis, pranešdamas, kad jau atliko visą šį darbą — didelėmis sąnaudomis. Jie pasidalino visa kolekcija su mumis, nesitikėdami nieko mainais, išskyrus ilgalaikio išsaugojimo garantiją. Tikrai nepaprasta. Daugiau informacijos iš mūsų savanorių (neapdoroti užrašai): Pritaikyta iš mūsų <a %(a_href)s>tinklaraščio įrašo</a>. DuXiu 读秀 %(count)s failas %(count)s failai %(count)s failų Šis duomenų rinkinys glaudžiai susijęs su <a %(a_datasets_openlib)s>Open Library duomenų rinkiniu</a>. Jame yra visų metaduomenų ir didelės dalies failų iš IA’s Controlled Digital Lending Library nuskaitymas. Atnaujinimai išleidžiami <a %(a_aac)s>Anna’s Archive Containers formatu</a>. Šie įrašai yra tiesiogiai susiję su Open Library duomenų rinkiniu, tačiau taip pat yra įrašų, kurių nėra Open Library. Taip pat turime keletą duomenų failų, kuriuos per daugelį metų nuskaitydavo bendruomenės nariai. Kolekcija susideda iš dviejų dalių. Jums reikia abiejų dalių, kad gautumėte visus duomenis (išskyrus pakeistus torrentus, kurie yra perbraukti torrentų puslapyje). Skaitmeninė Skolinimo Biblioteka mūsų pirmasis leidimas, prieš standartizuojant <a %(a_aac)s>Annės Archyvo Konteinerių (AAC) formatą</a>. Sudėtyje yra metaduomenys (json ir xml formatu), pdf failai (iš acsm ir lcpdf skaitmeninių skolinimo sistemų) ir viršelių miniatiūros. inkrementiniai nauji leidimai, naudojant AAC. Sudėtyje yra tik metaduomenys su laiko žymėmis po 2023-01-01, nes likusią dalį jau apima „ia“. Taip pat visi pdf failai, šį kartą iš acsm ir „bookreader“ (IA internetinio skaitytuvo) skolinimo sistemų. Nepaisant to, kad pavadinimas nėra visiškai tikslus, mes vis tiek įtraukiame bookreader failus į ia2_acsmpdf_files kolekciją, nes jie yra tarpusavyje nesuderinami. IA kontroliuojamas skaitmeninis skolinimas 98%%+ failų galima ieškoti. Mūsų misija yra archyvuoti visas pasaulio knygas (taip pat straipsnius, žurnalus ir kt.) ir padaryti jas plačiai prieinamas. Mes tikime, kad visos knygos turėtų būti plačiai atkartojamos, siekiant užtikrinti jų išlikimą. Štai kodėl mes jungiame failus iš įvairių šaltinių. Kai kurie šaltiniai yra visiškai atviri ir gali būti atkartojami dideliais kiekiais (pvz., Sci-Hub). Kiti yra uždari ir apsaugoti, todėl stengiamės juos išgauti, kad „išlaisvintume“ jų knygas. Dar kiti yra kažkur tarp šių. Visi mūsų duomenys gali būti <a %(a_torrents)s>torrentuojami</a>, o visi mūsų metaduomenys gali būti <a %(a_anna_software)s>generuojami</a> arba <a %(a_elasticsearch)s>atsisiunčiami</a> kaip ElasticSearch ir MariaDB duomenų bazės. Neapdoroti duomenys gali būti rankiniu būdu tyrinėjami per JSON failus, tokius kaip <a %(a_dbrecord)s>šis</a>. Metaduomenys ISBN svetainė Paskutinį kartą atnaujinta: %(isbn_country_date)s (%(link)s) Ištekliai Tarptautinė ISBN agentūra reguliariai skelbia diapazonus, kuriuos ji paskyrė nacionalinėms ISBN agentūroms. Iš to galime nustatyti, kuriai šaliai, regionui ar kalbos grupei priklauso šis ISBN. Šiuo metu šiuos duomenis naudojame netiesiogiai, per <a %(a_isbnlib)s>isbnlib</a> Python biblioteką. ISBN šalių informacija Tai yra daugybės skambučių į isbndb.com iškrova 2022 m. rugsėjo mėn. Bandėme apimti visus ISBN diapazonus. Tai yra apie 30,9 milijono įrašų. Jų svetainėje teigiama, kad jie iš tikrųjų turi 32,6 milijono įrašų, todėl galbūt kažkaip praleidome kai kuriuos, arba <em>jie</em> gali daryti kažką neteisingai. JSON atsakymai yra beveik neapdoroti iš jų serverio. Viena duomenų kokybės problema, kurią pastebėjome, yra ta, kad ISBN-13 numeriams, kurie prasideda kitu prefiksu nei „978-“, jie vis tiek įtraukia „isbn“ lauką, kuris tiesiog yra ISBN-13 numeris su pirmomis 3 skaitmenimis nukirptas (ir patikros skaitmuo perskaičiuotas). Tai akivaizdžiai neteisinga, bet taip jie tai daro, todėl mes to nekeitėme. Kita potenciali problema, su kuria galite susidurti, yra ta, kad „isbn13“ laukas turi dublikatų, todėl negalite jo naudoti kaip pirminio rakto duomenų bazėje. „isbn13“+„isbn“ laukai kartu atrodo unikalūs. 1 Išleidimas (2022-10-31) Grožinės literatūros torrentai atsilieka (nors ID ~4-6M nėra torrentuose, nes jie sutampa su mūsų Zlib torrentais). Mūsų tinklaraščio įrašas apie komiksų išleidimą Komiksų torrentai Annos Archyve Dėl skirtingų Library Genesis šakų istorijos žr. puslapį <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li turi daugumą tų pačių turinio ir metaduomenų kaip ir Libgen.rs, bet turi keletą papildomų kolekcijų, būtent komiksus, žurnalus ir standartinius dokumentus. Jis taip pat integravo <a %(a_scihub)s>Sci-Hub</a> į savo metaduomenis ir paieškos variklį, kurį naudojame savo duomenų bazėje. Šios bibliotekos metaduomenys yra laisvai prieinami <a %(a_libgen_li)s>libgen.li</a>. Tačiau šis serveris yra lėtas ir nepalaiko nutrūkusių ryšių atnaujinimo. Tie patys failai taip pat yra prieinami <a %(a_ftp)s>FTP serveryje</a>, kuris veikia geriau. Atrodo, kad negrožinė literatūra taip pat pasikeitė, tačiau be naujų torrentų. Panašu, kad tai įvyko nuo 2022 metų pradžios, nors mes to nepatvirtinome. Pasak Libgen.li administratoriaus, „fiction_rus“ (rusų grožinės literatūros) kolekcija turėtų būti padengta reguliariai išleidžiamais torrentais iš <a %(a_booktracker)s>booktracker.org</a>, ypač <a %(a_flibusta)s>flibusta</a> ir <a %(a_librusec)s>lib.rus.ec</a> torrentais (kuriuos mes veidrodžiuojame <a %(a_torrents)s>čia</a>, nors dar nenustatėme, kurie torrentai atitinka kuriuos failus). Grožinės literatūros kolekcija turi savo torrentus (atskirtus nuo <a %(a_href)s>Libgen.rs</a>) pradedant nuo %(start)s. Tam tikri diapazonai be torrentų (pvz., grožinės literatūros diapazonai f_3463000 iki f_4260000) greičiausiai yra Z-Library (ar kiti dublikatai) failai, nors galbūt norėtume atlikti tam tikrą deduplikaciją ir sukurti torrentus lgli-unikaliems failams šiuose diapazonuose. Statistiką apie visas kolekcijas galima rasti <a %(a_href)s>libgen svetainėje</a>. Torrentai yra prieinami daugumai papildomo turinio, ypač komiksų, žurnalų ir standartinių dokumentų torrentai buvo išleisti bendradarbiaujant su Annos Archyvu. Atkreipkite dėmesį, kad torrent failai, nurodantys „libgen.is“, yra aiškiai <a %(a_libgen)s>Libgen.rs</a> veidrodžiai („.is“ yra kitas domenas, naudojamas Libgen.rs). Naudingas šaltinis naudojant metaduomenis yra <a %(a_href)s>šis puslapis</a>. %(icon)s Jų „fiction_rus“ kolekcija (rusų grožinė literatūra) neturi specialių torrentų, tačiau yra padengta kitų torrentų, ir mes laikome <a %(fiction_rus)s>veidrodį</a>. Rusų grožinės literatūros torrentai Annos Archyve Grožinės literatūros torrentai Annos Archyve Diskusijų forumas Metaduomenys Metaduomenys per FTP Žurnalų torrentai Annos Archyve Metaduomenų laukų informacija Kitų torrentų veidrodis (ir unikalūs grožinės literatūros bei komiksų torrentai) Standartinių dokumentų torrentai Annos Archyve Libgen.li Annos Archyvo torrentai (knygų viršeliai) Library Genesis yra žinomas dėl savo dosnumo, nes jau leidžia savo duomenis masiškai per torrentus. Mūsų Libgen kolekcija susideda iš papildomų duomenų, kurių jie tiesiogiai neišleidžia, bendradarbiaujant su jais. Didelis ačiū visiems, kurie dirbo su Library Genesis! Mūsų tinklaraštis apie knygų viršelių išleidimą Šis puslapis yra apie „.rs“ versiją. Ji yra žinoma dėl nuolatinio tiek metaduomenų, tiek viso savo knygų katalogo turinio publikavimo. Jos knygų kolekcija yra padalinta į grožinės ir ne grožinės literatūros dalis. Naudingas šaltinis naudojant metaduomenis yra <a %(a_metadata)s>šis puslapis</a> (blokuoja IP diapazonus, gali prireikti VPN). Nuo 2024-03, nauji torrentai skelbiami <a %(a_href)s>šiame forumo temoje</a> (blokuoja IP diapazonus, gali prireikti VPN). Grožinės literatūros torrentai Annos Archyve Libgen.rs Grožinės literatūros torrentai Libgen.rs Diskusijų forumas Libgen.rs Metaduomenys Libgen.rs metaduomenų laukų informacija Libgen.rs Ne grožinės literatūros torrentai Ne grožinės literatūros torrentai Annos Archyve %(example)s grožinės literatūros knygai. Šis <a %(blog_post)s>pirmasis išleidimas</a> yra gana mažas: apie 300GB knygų viršelių iš Libgen.rs šakos, tiek grožinės, tiek ne grožinės literatūros. Jie yra organizuoti taip pat, kaip jie pasirodo libgen.rs, pvz.: %(example)s ne grožinės literatūros knygai. Kaip ir su Z-Library kolekcija, mes visus juos sudėjome į didelį .tar failą, kurį galima montuoti naudojant <a %(a_ratarmount)s>ratarmount</a>, jei norite tiesiogiai aptarnauti failus. Išleidimas 1 (%(date)s) Trumpa skirtingų Library Genesis (arba „Libgen“) šakų istorija yra tokia, kad laikui bėgant, skirtingi Library Genesis dalyviai susipyko ir pasuko skirtingais keliais. Pagal šį <a %(a_mhut)s>forumo įrašą</a>, Libgen.li iš pradžių buvo talpinamas adresu „http://free-books.dontexist.com“. „.fun“ versiją sukūrė originalus įkūrėjas. Ji yra atnaujinama, siekiant sukurti naują, labiau paskirstytą versiją. <a %(a_li)s>„.li“ versija</a> turi didžiulę komiksų kolekciją, taip pat kitą turinį, kuris (dar) nėra prieinamas dideliais torrentais. Ji turi atskirą grožinės literatūros knygų torrentų kolekciją ir savo duomenų bazėje turi <a %(a_scihub)s>Sci-Hub</a> metaduomenis. „.rs“ versija turi labai panašius duomenis ir nuolat išleidžia savo kolekciją dideliais torrentais. Ji yra apytiksliai padalinta į „grožinės literatūros“ ir „ne grožinės literatūros“ skyrius. Iš pradžių adresu „http://gen.lib.rus.ec“. <a %(a_zlib)s>Z-Library</a> tam tikra prasme taip pat yra Library Genesis šaka, nors jie savo projektui naudojo kitą pavadinimą. Libgen.rs Mes taip pat praturtiname savo kolekciją tik metaduomenų šaltiniais, kuriuos galime susieti su failais, pvz., naudojant ISBN numerius ar kitus laukus. Žemiau pateikiama tokių šaltinių apžvalga. Vėlgi, kai kurie iš šių šaltinių yra visiškai atviri, o kitus turime surinkti. Atkreipkite dėmesį, kad metaduomenų paieškoje mes rodome originalius įrašus. Mes nesujungiame įrašų. Tik metaduomenų šaltiniai Open Library yra atviro kodo projektas, kurį vykdo Internet Archive, siekiant kataloguoti visas pasaulio knygas. Jis turi vieną didžiausių pasaulyje knygų skenavimo operacijų ir turi daug knygų, prieinamų skaitmeniniam skolinimui. Jo knygų metaduomenų katalogas yra laisvai prieinamas atsisiuntimui ir yra įtrauktas į Anna’s Archive (nors šiuo metu ne paieškoje, išskyrus jei aiškiai ieškote Open Library ID). Open Library Neįskaitant dublikatų Paskutinį kartą atnaujinta Failų skaičiaus procentai %% veidrodinis AA / torrentai prieinami Dydis Šaltinis Žemiau pateikiama greita failų šaltinių apžvalga Annos Archyve. Kadangi šešėlinės bibliotekos dažnai sinchronizuoja duomenis viena iš kitos, tarp bibliotekų yra nemažai persidengimo. Todėl skaičiai nesutampa su bendra suma. „Veidrodinis ir sėtas Anna’s Archive“ procentas rodo, kiek failų mes patys veidrodiname. Mes sėjame tuos failus dideliais kiekiais per torrentus ir padarome juos prieinamus tiesioginiam atsisiuntimui per partnerių svetaines. Apžvalga Iš viso Torrentai esantys Annos Archyve Dėl informacijos apie Sci-Hub, prašome apsilankyti jo <a %(a_scihub)s>oficialioje svetainėje</a>, <a %(a_wikipedia)s>Vikipedijos puslapyje</a> ir šiame <a %(a_radiolab)s>podkasto interviu</a>. Atkreipkite dėmesį, kad Sci-Hub buvo <a %(a_reddit)s>užšaldytas nuo 2021 metų</a>. Jis buvo užšaldytas ir anksčiau, tačiau 2021 metais buvo pridėta keletas milijonų straipsnių. Vis dėlto, kai kurie riboti straipsniai vis dar pridedami į Libgen „scimag“ kolekcijas, nors jų nepakanka, kad būtų verta kurti naujus masinio atsisiuntimo torrentus. Mes naudojame Sci-Hub metaduomenis, kuriuos pateikia <a %(a_libgen_li)s>Libgen.li</a> savo „scimag“ kolekcijoje. Taip pat naudojame <a %(a_dois)s>dois-2022-02-12.7z</a> duomenų rinkinį. Atkreipkite dėmesį, kad „smarch“ torrentai yra <a %(a_smarch)s>pasenę</a> ir todėl nėra įtraukti į mūsų torrentų sąrašą. Torrentai esantys Libgen.li Torrentai esantys Libgen.rs Metaduomenys ir torrentai Naujienos Reddite Podkasto interviu Vikipedijos puslapis Sci-Hub Sci-Hub: užšaldyta nuo 2021 m.; dauguma prieinama per torrentus Libgen.li: nuo tada nedideli papildymai</div> Kai kurios šaltinių bibliotekos skatina masinį duomenų dalijimąsi per torrentus, o kitos neskuba dalintis savo kolekcijomis. Pastaruoju atveju, Anna’s Archive bando surinkti jų kolekcijas ir padaryti jas prieinamas (žr. mūsų <a %(a_torrents)s>Torrentų</a> puslapį). Taip pat yra tarpinės situacijos, pavyzdžiui, kai šaltinių bibliotekos nori dalintis, bet neturi tam išteklių. Tokiais atvejais mes taip pat stengiamės padėti. Žemiau pateikiama apžvalga, kaip mes sąveikaujame su skirtingomis šaltinių bibliotekomis. Šaltinių bibliotekos %(icon)s Įvairios failų duomenų bazės, išsibarsčiusios po Kinijos internetą; dažnai mokamos duomenų bazės %(icon)s Dauguma failų prieinami tik naudojant premium BaiduYun paskyras; lėtas atsisiuntimo greitis. %(icon)s Annos Archyvas valdo <a %(duxiu)s>DuXiu failų</a> kolekciją %(icon)s Įvairios metaduomenų duomenų bazės, išsibarsčiusios po Kinijos internetą; dažnai mokamos duomenų bazės %(icon)s Nėra lengvai prieinamų metaduomenų iškrovų visai jų kolekcijai. %(icon)s Annos Archyvas valdo <a %(duxiu)s>DuXiu metaduomenų</a> kolekciją Failai %(icon)s Failai, kuriuos galima skolintis tik ribotai, su įvairiais prieigos apribojimais %(icon)s Annos Archyvas valdo <a %(ia)s>IA failų</a> kolekciją %(icon)s Kai kurie metaduomenys prieinami per <a %(openlib)s>Open Library duomenų bazės iškrovas</a>, bet jie neapima visos IA kolekcijos %(icon)s Nėra lengvai prieinamų metaduomenų iškrovų visai jų kolekcijai %(icon)s Annos Archyvas valdo <a %(ia)s>IA metaduomenų</a> kolekciją Paskutinį kartą atnaujinta %(icon)s Annos Archyvas ir Libgen.li kartu tvarko <a %(comics)s>komiksų</a>, <a %(magazines)s>žurnalų</a>, <a %(standarts)s>standartinių dokumentų</a> ir <a %(fiction)s>grožinės literatūros (atskirtos nuo Libgen.rs)</a> kolekcijas. %(icon)s Ne grožinės literatūros torrentai dalijami su Libgen.rs (ir atkartojami <a %(libgenli)s>čia</a>). %(icon)s Ketvirtiniai <a %(dbdumps)s>HTTP duomenų bazės iškrovos</a> %(icon)s Automatiniai torrent failai <a %(nonfiction)s>Negrožinei literatūrai</a> ir <a %(fiction)s>Grožinei literatūra</a> %(icon)s Annos Archyvas valdo <a %(covers)s>knygų viršelių torrentų</a> kolekciją %(icon)s Kasdienės <a %(dbdumps)s>HTTP duomenų kopijos</a> Metaduomenys %(icon)s Mėnesiniai <a %(dbdumps)s>duomenų bazės parsiuntimai</a> %(icon)s Duomenų torrentai prieinami <a %(scihub1)s>čia</a>, <a %(scihub2)s>čia</a> ir <a %(libgenli)s>čia</a> %(icon)s Kai kurie nauji failai <a %(libgenrs)s>pridedami</a> į Libgen „scimag“, bet nepakankamai, kad būtų verta kurti naujus torrentus %(icon)s Sci-Hub nuo 2021 metų nebeprideda naujų failų. %(icon)s Metaduomenų iškrovos prieinamos <a %(scihub1)s>čia</a> ir <a %(scihub2)s>čia</a>, taip pat kaip dalis <a %(libgenli)s>Libgen.li duomenų bazės</a> (kurią mes naudojame) Šaltinis %(icon)s Įvairūs mažesni ar vienkartiniai šaltiniai. Skatiname žmones pirmiausia įkelti į kitas šešėlines bibliotekas, tačiau kartais žmonės turi kolekcijų, kurios yra per didelės kitiems rūšiuoti, bet nepakankamai didelės, kad būtų verta sukurti atskirą kategoriją. %(icon)s Nėra tiesiogiai prieinami dideliais kiekiais, apsaugoti nuo nuskaitymo %(icon)s Annos Archyvas valdo <a %(worldcat)s>OCLC (WorldCat) metaduomenų</a> kolekciją %(icon)s Annos Archyvas ir Z-Library kartu valdo <a %(metadata)s>Z-Library metaduomenų</a> ir <a %(files)s>Z-Library failų</a> kolekciją Duomenų rinkiniai Mes sujungiame visus aukščiau paminėtus šaltinius į vieną vieningą duomenų bazę, kurią naudojame šiai svetainei aptarnauti. Ši vieninga duomenų bazė nėra tiesiogiai prieinama, tačiau kadangi Anna’s Archive yra visiškai atviro kodo, ją galima gana lengvai <a %(a_generated)s>sugeneruoti</a> arba <a %(a_downloaded)s>atsisiųsti</a> kaip ElasticSearch ir MariaDB duomenų bazes. Skriptai tame puslapyje automatiškai atsisiųs visus reikalingus metaduomenis iš aukščiau paminėtų šaltinių. Jei norite išnagrinėti mūsų duomenis prieš paleisdami tuos skriptus lokaliai, galite peržiūrėti mūsų JSON failus, kurie toliau susieja su kitais JSON failais. <a %(a_json)s>Šis failas</a> yra geras pradžios taškas. Vieninga duomenų bazė Torrentai iš Annos Archyvo naršyti ieškoti Įvairūs mažesni arba vienkartiniai šaltiniai. Skatiname žmones pirmiausia įkelti į kitas šešėlines bibliotekas, tačiau kartais žmonės turi kolekcijų, kurios yra per didelės kitiems peržiūrėti, bet nepakankamai didelės, kad būtų verta sukurti atskirą kategoriją. Apžvalga iš <a %(a1)s>duomenų rinkinių puslapio</a>. Iš <a %(a_href)s>aaaaarg.fail</a>. Atrodo gana išsamus. Iš mūsų savanorio „cgiym“. Iš <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrento. Turi gana didelį sutapimą su esamomis straipsnių kolekcijomis, bet labai mažai MD5 atitikmenų, todėl nusprendėme išlaikyti visą. „iRead eBooks“ (fonetiškai „ai rit i-books“; airitibooks.com) nuskaitymas, atliktas savanorio „j“. Atitinka „airitibooks“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>. Iš kolekcijos <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Iš dalies iš originalaus šaltinio, iš dalies iš the-eye.eu, iš dalies iš kitų veidrodžių. Iš privačios knygų torrento svetainės, <a %(a_href)s>Bibliotik</a> (dažnai vadinamos „Bib“), kurios knygos buvo sujungtos į torrentus pagal pavadinimą (A.torrent, B.torrent) ir platinamos per the-eye.eu. Iš mūsų savanorio „bpb9v“. Daugiau informacijos apie <a %(a_href)s>CADAL</a> rasite mūsų <a %(a_duxiu)s>DuXiu duomenų rinkinio puslapyje</a>. Daugiau iš mūsų savanorio „bpb9v“, daugiausia DuXiu failai, taip pat aplankai „WenQu“ ir „SuperStar_Journals“ (SuperStar yra kompanija, kuri valdo DuXiu). Iš mūsų savanorio „cgiym“, kinų tekstai iš įvairių šaltinių (atstovaujami kaip subkatalogai), įskaitant iš <a %(a_href)s>China Machine Press</a> (didžiojo kinų leidėjo). Ne kinų kolekcijos (atstovaujamos kaip subkatalogai) iš mūsų savanorio „cgiym“. Knygų apie Kinijos architektūrą nuskaitymas, atliktas savanorio „cm“: „Aš tai gavau išnaudodamas leidyklos tinklo pažeidžiamumą, tačiau ta spraga jau uždaryta“. Atitinka „chinese_architecture“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>. Knygos iš akademinio leidėjo <a %(a_href)s>De Gruyter</a>, surinktos iš kelių didelių torrentų. Scrape iš <a %(a_href)s>docer.pl</a>, lenkų failų dalijimosi svetainės, orientuotos į knygas ir kitus rašytinius darbus. Scrape atliktas 2023 m. pabaigoje savanorio „p“. Neturime geros metaduomenų iš originalios svetainės (netgi failų plėtinių), bet filtravome knygų tipo failus ir dažnai galėjome išgauti metaduomenis iš pačių failų. DuXiu epubs, tiesiogiai iš DuXiu, surinkti savanorio „w“. Tik naujausios DuXiu knygos yra tiesiogiai prieinamos per el. knygas, todėl dauguma jų turi būti naujausios. Likę DuXiu failai iš savanorio „m“, kurie nebuvo DuXiu patentuoto PDG formato (pagrindinis <a %(a_href)s>DuXiu duomenų rinkinys</a>). Surinkti iš daugelio originalių šaltinių, deja, nesaugant tų šaltinių failų keliuose. <span></span> <span></span> <span></span> Erotinių knygų nuskaitymas, atliktas savanorio „do no harm“. Atitinka „hentai“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>. <span></span> <span></span> Kolekcija, surinkta iš Japonijos Manga leidėjo savanorio „t“. <a %(a_href)s>Pasirinkti Longquan teismų archyvai</a>, pateikti savanorio „c“. Scrape iš <a %(a_href)s>magzdb.org</a>, Library Genesis sąjungininko (jis yra susietas libgen.rs pagrindiniame puslapyje), bet kuris nenorėjo tiesiogiai pateikti savo failų. Gauta savanorio „p“ 2023 m. pabaigoje. <span></span> Įvairūs maži įkėlimai, per maži kaip atskira subkolekcija, bet atstovaujami kaip katalogai. Elektroninės knygos iš AvaxHome, Rusijos failų dalijimosi svetainės. Laikraščių ir žurnalų archyvas. Atitinka „newsarch_magz“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>. <a %(a1)s>Filosofijos dokumentacijos centro</a> nuskaitymas. Kolekcija savanorio „o“, kuris surinko lenkų knygas tiesiogiai iš originalių išleidimo („scene“) svetainių. Sujungtos kolekcijos iš <a %(a_href)s>shuge.org</a> savanorių „cgiym“ ir „woz9ts“. <span></span> <a %(a_href)s>„Trantoro Imperijos Biblioteka“</a> (pavadinta pagal išgalvotą biblioteką), nuskaityta 2022 m. savanorio „t“. <span></span> <span></span> <span></span> Sub-sub-kolekcijos (atstovaujamos kaip katalogai) iš savanorio „woz9ts“: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (autorius <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taivane), mebook (mebook.cc, 我的小书屋, mano mažoji knygų kambarys — woz9ts: „Ši svetainė daugiausia dėmesio skiria aukštos kokybės el. knygų failų dalijimuisi, kai kuriuos iš jų pats savininkas suformatavo. Savininkas buvo <a %(a_arrested)s>suimtas</a> 2019 m. ir kažkas sukūrė jo dalintų failų kolekciją.“). Likę DuXiu failai iš savanorio „woz9ts“, kurie nebuvo DuXiu patentuotu PDG formatu (dar turi būti konvertuoti į PDF). „Įkėlimų“ kolekcija yra suskirstyta į mažesnes subkolekcijas, kurios nurodytos AACID ir torrentų pavadinimuose. Visos subkolekcijos pirmiausia buvo deduplikuotos su pagrindine kolekcija, nors „upload_records“ metaduomenų JSON failuose vis dar yra daug nuorodų į originalius failus. Dauguma subkolekcijų taip pat buvo pašalinti ne knygų failai, ir paprastai <em>ne</em> pažymėti „upload_records“ JSON. Subkolekcijos yra: Pastabos Subkolekcija Daugelis subkolekcijų pačios susideda iš sub-sub-kolekcijų (pvz., iš skirtingų originalių šaltinių), kurios yra atvaizduojamos kaip katalogai „filepath“ laukuose. Įkėlimai į Annos Archyvą Mūsų tinklaraščio įrašas apie šiuos duomenis <a %(a_worldcat)s>WorldCat</a> yra nuosavybinė duomenų bazė, kurią valdo ne pelno siekianti organizacija <a %(a_oclc)s>OCLC</a>, kuri renka metaduomenų įrašus iš bibliotekų visame pasaulyje. Tai tikriausiai yra didžiausia bibliotekų metaduomenų kolekcija pasaulyje. 2023 metų spalį mes <a %(a_scrape)s>išleidome</a> išsamų OCLC (WorldCat) duomenų bazės nuskaitymą, <a %(a_aac)s>Anna’s Archive Containers formatu</a>. 2023 m. spalio mėn., pradinė versija: OCLC (WorldCat) Annos Archyvo torrentai Pavyzdinis įrašas Anna’s Archive (originali kolekcija) Pavyzdinis įrašas Anna’s Archive („zlib3“ kolekcija) Torrentai pagal Anna’s Archive (metaduomenys + turinys) Įrašas tinklaraštyje apie 1 leidimą Įrašas tinklaraštyje apie 2 leidimą 2022 m. pabaigoje tariami Z-Library įkūrėjai buvo suimti, o domenai buvo konfiskuoti Jungtinių Valstijų valdžios institucijų. Nuo tada svetainė pamažu vėl pradeda veikti internete. Nežinoma, kas šiuo metu ją valdo. Atnaujinimas 2023 m. vasario mėn. Z-Library turi savo šaknis <a %(a_href)s>Library Genesis</a> bendruomenėje ir iš pradžių buvo sukurta naudojant jų duomenis. Nuo tada ji labai profesionalizavosi ir turi daug modernesnę sąsają. Todėl jie gali gauti daug daugiau aukų, tiek piniginių, kad galėtų toliau tobulinti savo svetainę, tiek naujų knygų aukų. Jie sukaupė didelę kolekciją, papildančią Library Genesis. Kolekcija susideda iš trijų dalių. Žemiau pateikiami originalūs aprašymo puslapiai pirmosioms dviem dalims. Norint gauti visus duomenis, reikia visų trijų dalių (išskyrus pakeistus torrentus, kurie yra perbraukti torrentų puslapyje). %(title)s: mūsų pirmasis leidimas. Tai buvo pats pirmasis leidimas, kuris tada buvo vadinamas „Pirate Library Mirror“ („pilimi“). %(title)s: antrasis leidimas, šį kartą su visais failais, supakuotais į .tar failus. %(title)s: papildomi nauji leidimai, naudojant <a %(a_href)s>Anna’s Archive Containers (AAC) formatą</a>, dabar išleisti bendradarbiaujant su Z-Library komanda. Pradinis veidrodis buvo kruopščiai gautas per 2021 ir 2022 metus. Šiuo metu jis yra šiek tiek pasenęs: jis atspindi kolekcijos būklę 2021 m. birželio mėn. Ateityje jį atnaujinsime. Šiuo metu mes sutelkiame dėmesį į šio pirmojo leidimo išleidimą. Kadangi Library Genesis jau yra išsaugota su viešais torrentais ir yra įtraukta į Z-Library, 2022 m. birželio mėn. atlikome pagrindinį deduplikavimą prieš Library Genesis. Tam naudojome MD5 maišas. Tikėtina, kad bibliotekoje yra daug daugiau pasikartojančio turinio, pvz., keli failų formatai su ta pačia knyga. Tai sunku tiksliai aptikti, todėl to nedarome. Po deduplikavimo liko daugiau nei 2 milijonai failų, kurių bendra apimtis yra beveik 7 TB. Kolekcija susideda iš dviejų dalių: MySQL „.sql.gz“ metaduomenų iškrovos ir 72 torrentų failų, kurių kiekvienas yra apie 50-100 GB. Metaduomenyse yra duomenys, kuriuos pranešė Z-Library svetainė (pavadinimas, autorius, aprašymas, failo tipas), taip pat faktinis failo dydis ir md5sum, kuriuos mes pastebėjome, nes kartais jie nesutampa. Atrodo, kad yra failų diapazonų, kuriuose pati Z-Library turi neteisingus metaduomenis. Taip pat galime turėti neteisingai atsisiųstų failų kai kuriais atskirais atvejais, kuriuos ateityje bandysime aptikti ir ištaisyti. Dideli torrent failai turi tikruosius knygų duomenis, su Z-Library ID kaip failo pavadinimu. Failų plėtinius galima atkurti naudojant metaduomenų iškrovą. Kolekcija yra mišri, apimanti tiek negrožinę, tiek grožinę literatūrą (neatskirta kaip Library Genesis). Kokybė taip pat labai skiriasi. Šis pirmasis leidimas dabar yra visiškai prieinamas. Atkreipkite dėmesį, kad torrent failai yra prieinami tik per mūsų Tor veidrodį. 1 leidimas (%(date)s) Tai yra vienas papildomas torrent failas. Jame nėra jokios naujos informacijos, tačiau jame yra duomenų, kuriuos gali užtrukti apskaičiuoti. Tai patogu turėti, nes atsisiųsti šį torrent failą dažnai yra greičiau nei apskaičiuoti iš naujo. Konkrečiai, jame yra SQLite indeksai tar failams, skirti naudoti su <a %(a_href)s>ratarmount</a>. Leidimas 2 priedas (%(date)s) Mes surinkome visas knygas, kurios buvo pridėtos į Z-Library tarp mūsų paskutinio veidrodžio ir 2022 m. rugpjūčio. Taip pat grįžome ir surinkome kai kurias knygas, kurias praleidome pirmą kartą. Iš viso ši nauja kolekcija yra apie 24TB. Vėlgi, ši kolekcija yra deduplikuota prieš Library Genesis, nes jau yra torrent failai, prieinami šiai kolekcijai. Duomenys yra organizuoti panašiai kaip pirmasis leidimas. Yra MySQL „.sql.gz“ metaduomenų iškrova, kuri taip pat apima visus pirmojo leidimo metaduomenis, taip jį pakeisdama. Taip pat pridėjome keletą naujų stulpelių: Mes tai minėjome paskutinį kartą, bet tik norėdami paaiškinti: „filename“ ir „md5“ yra tikrosios failo savybės, o „filename_reported“ ir „md5_reported“ yra tai, ką mes surinkome iš Z-Library. Kartais šie duomenys nesutampa, todėl įtraukėme abu. Šiam leidimui pakeitėme koduotę į „utf8mb4_unicode_ci“, kuri turėtų būti suderinama su senesnėmis MySQL versijomis. Duomenų failai yra panašūs į praėjusį kartą, nors jie yra daug didesni. Mes tiesiog negalėjome vargintis kurdami daugybę mažesnių torrent failų. „pilimi-zlib2-0-14679999-extra.torrent“ turi visus failus, kuriuos praleidome paskutiniame leidime, o kiti torrent failai yra visi nauji ID diapazonai.  <strong>Atnaujinimas %(date)s:</strong> Mes padarėme daugumą mūsų torrent failų per didelius, todėl torrent klientai turėjo sunkumų. Mes juos pašalinome ir išleidome naujus torrent failus. <strong>Atnaujinimas %(date)s:</strong> Vis dar buvo per daug failų, todėl mes juos supakavome į tar failus ir vėl išleidome naujus torrent failus. %(key)s: ar šis failas jau yra Library Genesis, tiek negrožinės, tiek grožinės literatūros kolekcijoje (atitinka pagal md5). %(key)s: kuriame torrente yra šis failas. %(key)s: nustatyta, kai nepavyko atsisiųsti knygos. Leidimas 2 (%(date)s) Zlib leidimai (originalūs aprašymo puslapiai) Tor domenas Pagrindinė svetainė Z-Library nuskaitymas „Kinų” kolekcija Z-Library atrodo tokia pati kaip mūsų DuXiu kolekcija, bet su skirtingais MD5. Mes neįtraukiame šių failų į torrentus, kad išvengtume dublikatų, bet vis tiek rodome juos mūsų paieškos indekse. Metaduomenys Jūs gaunate %(percentage)s%% papildomų atsisiuntimų, nes buvote pakviestas vartotojo %(profile_link)s. Tai taikoma visą narystės laikotarpį. Paaukoti Prisijungti Pasirinktas iki %(percentage)s%% nuolaidos Alipay palaiko tarptautines kredito/debeto korteles. Daugiau informacijos rasite <a %(a_alipay)s>šiame vadove</a>. Siųskite mums Amazon.com dovanų korteles naudodami savo kreditinę/debetinę kortelę. Galite pirkti kriptovaliutą naudodami kreditines/debetines korteles. WeChat (Weixin Pay) palaiko tarptautines kreditines/debetines korteles. WeChat programėlėje eikite į „Me => Services => Wallet => Add a Card“. Jei to nematote, įjunkite naudodami „Me => Settings => General => Tools => Weixin Pay => Enable“. (naudoti siunčiant Ethereum iš Coinbase) nukopijuota! kopijuoti (mažiausia minimali suma) (įspėjimas: didelė minimali suma) -%(percentage)s%% 12 mėnesių 1 mėnesis 24 mėnesiai 3 mėnesiai 48 mėnesiai 6 mėnesiai 96 mėnesiai Pasirinkite kokiam laikotarpiui norite prenumeruoti. <div %(div_monthly_cost)s></div><div %(div_after)s>po <span %(span_discount)s></span> nuolaidos:</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 mėnesių laikotarpiui 1 mėnesiui 24 mėnesiams 3 mėnesiams 48 mėnesiams 6 mėnesiams 96 mėnesiams %(monthly_cost)s / mėnesį susisiekti su mumis Tiesioginiai <strong>SFTP</strong> serveriai Įmonės lygio auka arba mainai į naujas kolekcijas (pvz., nauji nuskenavimai, duomenų rinkiniai su teksto atpažinimu). Ekspertų prieiga <strong>Neribota</strong> sparčioji prieiga <div %(div_question)s>Ar galiu atnaujinti savo narystę arba gauti kelias narystes?</div> <div %(div_question)s>Ar galiu paaukoti netapdamas nariu?</div> Žinoma. Mes priimame bet kokio dydžio aukas šiuo Monero (XMR) adresu: %(address)s. <div %(div_question)s>Ką reiškia mėnesio diapazonai?</div> Galite pasiekti diapazono apatinę ribą pritaikydami visas nuolaidas, pvz., pasirinkdami ilgesnį nei mėnesio laikotarpį. <div %(div_question)s>Ar narystės automatiškai atsinaujina?</div> Narystės automatiškai <strong>neatsinaujina</strong>. Jūs galite prisijungti kiek norite ilgai ar trumpai. <div %(div_question)s>Kam išleidžiate aukas?</div> 100 %% skiriama pasaulio žinioms ir kultūrai išsaugoti ir padaryti jas prieinamas. Šiuo metu daugiausia išleidžiame serveriams, saugyklai ir pralaidumui. Pinigų neduodame nė vienam komandos nariui asmeniškai. Bet kokiu atveju tai būtų per daug pavojinga. <div %(div_question)s>Ar galiu atlikti didelę auką?</div> Tai būtų puiku! Norint paaukoti daugiau nei kelis tūkstančius dolerių, prašome susisiekti su mumis tiesiogiai %(email)s. <div %(div_question)s>Ar turite kitokių mokėjimo metodų?</div>Kol kas ne. Daugelis žmonių nenori, kad tokie archyvai egzistuotų, todėl mes turime būti atsargūs. Jeigu galite mums padėti mums pridėti kitokius (patogesnius) mokėjimo metodus, susisiekite %(email)s. Aukojimo DUK Jūs turite <a %(a_donation)s>esamą auką</a>, kuri yra vykdoma. Prašome užbaigti arba atšaukti tą auką prieš sukuriant naują auką. <a %(a_all_donations)s>Peržiūrėti visas mano aukas</a> Aukoms virš 5000$ susisiekite tiesiogiai %(email)s. Mes priimame dideles aukas iš turtingų asmenų ar institucijų.  Atkreipkite dėmesį, kad nors narystės šiame puslapyje yra „mėnesinės“, jos yra vienkartinės aukos (nepasikartojančios). Žr. <a %(faq)s>Aukojimo DUK</a>. Anna's Archive yra pelno nesiekiantis, atviro kodo, atvirų duomenų projektas. Aukojant ir tampant nauju nariu, palaikote mūsų veiksmus ir kūrimą. Visiems mūsų nariams: ačiū, kad palaikote mus! ❤️ Daugiau informacijos rasite <a %(a_donate)s>Aukojimo DUK</a>. Norėdami tapti nariu, prašome <a %(a_login)s>Prisijungti arba Registruotis</a>. Ačiū už jūsų palaikymą! $%(cost)s / mėn. Jei padarėte klaidą mokėjimo metu, mes negalime grąžinti pinigų, bet stengsimės išspręsti problemą. „PayPal“ programoje arba svetainėje raskite puslapį „Crypto“. Paprastai tai yra skiltyje „Finansai“. Eikite į “Bitcoin” puslapį PayPal programėlėje arba svetainėje. Nuspauskite “Perduoti” mygtuką %(transfer_icon)s, ir tada paspauskite “Siųsti”. Alipay Alipay 支付宝 / WeChat 微信 Amazon dovanų kortelė %(amazon)s dovanų kortelė Banko kortelė Banko kortelė (naudojant programėlę) Binance Kredito/debeto kortelė/Apple/Google (BMC) Cash App Kredito/debeto kortelė Kredito/debeto kortelė 2 Kredito/debeto kortelė (atsarginė) Kriptovaliutos %(bitcoin_icon)s Kortelė / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (reguliarus) Pix (Brazilija) Revolut (laikinai nepasiekiama) WeChat Pasirinkite norimą kriptovaliutą: Aukokite naudodamiesi Amazon dovanų kortele. <strong>SVARBU:</strong> Ši parinktis skirta %(amazon)s. Jei norite naudoti kitą Amazon svetainę, pasirinkite ją aukščiau. <strong>SVARBU:</strong> Mes palaikome tik Amazon.com, kitų Amazon svetainių, pvz., .de, .co.uk, .ca, NEPALAIKOME. Prašome NERAŠYTI savo žinutės. Įveskite tikslią sumą: %(amount)s Atkreipkite dėmesį, kad turime suapvalinti iki mūsų perpardavėjų priimamų sumų (mažiausiai %(minimum)s). Aukoti naudojant kreditinę/debetinę kortelę per Alipay programėlę (labai lengva). Įdiekite Alipay programėlę iš <a %(a_app_store)s>Apple App Store</a> arba <a %(a_play_store)s>Google Play parduotuvės</a>. Registruotis naudojant savo telefono numerį. Jokių papildomų asmeninių duomenų nereikia. <span %(style)s>1</span>Įdiegti Alipay programėlę Palaikoma: Visa, MasterCard, JCB, Diners Club ir Discover. Daugiau informacijos rasite <a %(a_alipay)s>šiame vadove</a>. <span %(style)s>2</span>Pridėti banko kortelę Naudojant Binance, galite nusipirkti Bitcoin su kredito/debeto kortele arba banko sąskaita, o tada paaukoti tą Bitcoin mums. Tokiu būdu galime išlikti saugūs ir anonimiški priimdami jūsų auką. Binance yra prieinamas beveik visose šalyse ir palaiko daugumą bankų bei kredito/debeto kortelių. Tai šiuo metu yra mūsų pagrindinė rekomendacija. Vertiname, kad skiriate laiko išmokti aukoti šiuo metodu, nes tai mums labai padeda. Kreditinėms kortelėms, debetinėms kortelėms, Apple Pay ir Google Pay mes naudojame „Buy Me a Coffee“ (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Jų sistemoje viena „kava“ yra lygi 5 USD, todėl jūsų auka bus suapvalinta iki artimiausio 5 kartotinio. Aukokite per Cash App. Jei naudojatės Cash App, tai yra lengviausias būdas aukoti! Atkreipkite dėmesį, kad už sandorius, smulkesnius nei %(amount)s, Cash App gali imti %(fee)s mokestį. Už %(amount)s ir duagiau, sandoris nemokamas! Aukokite naudadamiesi kredito ar debito kortele. Šis metodas naudoja kriptovaliutų tiekėją kaip tarpinį konversijos būdą. Tai gali būti šiek tiek painu, todėl naudokite šį metodą tik tada, jei kiti mokėjimo būdai neveikia. Taip pat, jis neveikia visose šalyse. Negalime tiesiogiai priimti kredito/debeto kortelių, nes bankai nenori su mumis dirbti. ☹ Tačiau yra keletas būdų, kaip naudoti kredito/debeto korteles, naudojant kitus mokėjimo metodus: Galite aukoti su šiomis kriptovaliutomis: BTC, ETH, XMR ir SOL. Naudokite šį pasirinkimą jeigu jau esate susipažinę su kriptovaliutomis. Su kriptovaliuta galite paaukoti naudodami BTC, ETH, XMR ir kt. Kriptovaliutos greitosios paslaugos Jei pirmą kartą naudojate kriptovaliutą, siūlome naudoti %(options)s nusipirkti ir paaukoti Bitkoinus (originalią ir plačiausiai naudojamą kriptovaliutą). Atkreipkite dėmesį, kad aukojant smulkias aukas kredito kortelės mokestis gali panaikinti Jūsų %(discount)s nuolaidą, tad rekomenduojame ilgiau trunkančias prenumeratas. Aukoti naudojant kredito/debeto kortelę, PayPal arba Venmo. Pasirinksite kitame puslapyje. Google Pay ir Apple Pay taip pat gali veikti. Atkreipkite dėmesį, kad aukojant smulkias aukas taikomi dideli mokesčiai, tad rekomenduojame ilgiau trunkančias prenumeratas. Norint aukoti naudojant PayPal, mes naudosime PayPal Crypto. Tai leidžia mums išlikti anoniminiais. Dėkojame, kad skyrėte laiko ir išmokote aukoti šiuo metodu, nes jis mums labai padeda. Aukokite per PayPal. Aukoti naudojant savo įprastą PayPal paskyrą. Aukoti naudojant Revolut. Jei turite Revolut, tai yra lengviausias būdas aukoti! Šis mokėjimo metodas leidžia daugiausiai %(amount)s. Prašome pasirinkti kitą trukmę arba mokėjimo metodą. Šis mokėjimo būdas reikalauja mažiausiai %(amount)s. Prašome pasirinkti kitą trukmę arba mokėjimo būdą. Binance Coinbase Kraken Pasirinkite mokėjimo būdą. "Priglobti torrentą": Jūsų vartotojo vardas arba pranešimas torrento failo pavadinime <div %(div_months)s>kas 12 mėnesių narystės</div> Jūsų vartotojo vardo arba anoniminis paminėjimas Ankstyva prieiga prie naujų funkcijų Prieiga prie uždaro Telegram su užkulisių atnaujinimais %(number)s greitųjų atsisiuntimų per dieną jei paaukosite šį mėnesį! <a %(a_api)s>JSON API</a> prieiga Legendinis žmonijos žinių ir kultūros išsaugojimo statusas Praėjusios privilegijos, plius: Uždirbkite <strong>%(percentage)s%% papildomų atsisiuntimų</strong> <a %(a_refer)s>rekomenduodami draugams</a>. SciDB straipsniai <strong>neriboti</strong> be patvirtinimo Klausiant apie paskyrą ar aukas, pridėkite savo paskyros ID, ekrano nuotraukas, kvitus ir kuo daugiau informacijos. Mes tikriname savo el. paštą kas 1-2 savaites, todėl šios informacijos neįtraukimas uždels bet kokį sprendimą. Norėdami gauti dar daugiau atsisiuntimų, <a %(a_refer)s>rekomenduokite savo draugams</a>! Mes esame maža savanorių komanda. Jums atsakyti gali užtrukti 1-2 savaites. Svarbu žinoti, jog paskyros vardas arba nuotrauka gali atrodyti keistai. Nereikia jaudintis! Šios paskyros yra valdomos mūsų aukojimo partnerių. Į mūsų paskyras nebuvo įsilaužta. Aukoti <span %(span_cost)s></span> <span %(span_label)s></span> 12 mėnesių “%(tier_name)s” 1 mėnesiui “%(tier_name)s” 24 mėnesiams “%(tier_name)s” 3 mėnesiams “%(tier_name)s” 48 mėnesiams “%(tier_name)s” 6 mėnesiams “%(tier_name)s” 96 mėnesiams “%(tier_name)s” Jūs vis dar galite atšaukti mokėjimo metu. Paspauskite mygtuką aukoti, kad patvirtinti šią auką. <strong>Svarbu:</strong> Kriptovaliutų kainos gali labai greitai keistis, kartais net po 20%% per kelias minutes. Tai vis tiek yra mažiau negu mokesčiai kuriuos patiriame su daugybe mokėjimo tiekėjų, kurie dažnai prašo 50-60%% už darbą su “šešėlinę labdara” kaip mumis. <u>Jeigu atsiųsite čekį su originalia kainą kurią mokėjote, mes vis dar suteiksime Jūsų paskyrai pasirinktą narystę</u> (jeigu čekis nėra senesnis negu kelios valandos). Labai vertiname, kad esate pasirengę taikstytis su tokiais dalykais, kad mus paremtumėte. ❤️ ❌ Kažkas atsitiko negerai. Prašome perkrauti puslapį ir bandykite dar kartą. <span %(span_circle)s>1</span>Pirkti Bitcoin Paypal <span %(span_circle)s>2</span>Perduokite Bitcoin į mūsų adresą ✅ Persiunčiame į aukojimo puslapį… Aukoti Prašome palaukti bent <span %(span_hours)s>24 valandas</span> (ir atnaujinti šį puslapį) prieš susisiekiant su mumis. Jei norėtumėte paaukoti (bet kokią sumą) be narystės, galite naudoti šį Monero (XMR) adresą: %(address)s. Išsiuntus dovanų kortelę, mūsų automatinė sistema ją patvirtins per kelias minutes. Jei tai neveikia, pabandykite išsiųsti dovanų kortelę dar kartą (<a %(a_instr)s>instrukcijos</a>). Jei tai vis tiek neveikia, prašome parašyti mums el. laišką, ir Anna tai peržiūrės rankiniu būdu (tai gali užtrukti kelias dienas). Būtinai paminėkite, jei jau bandėte išsiųsti iš naujo. Pavyzdys: Prašome naudoti <a %(a_form)s>oficialią Amazon.com formą</a>, kad atsiųstumėte mums %(amount)s vertės dovanų kortelę į žemiau nurodytą el. pašto adresą. „Kam“ gavėjo el. paštas formoje: Amazon dovanų kortelė Negalime priimti kitų dovanų kortelių metodų, <strong>tiktais siunčiamus tiesiogiai iš oficialios formos Amazon.com</strong>. Negalėsime grąžinti jūsų dovanų kortelės, jei nenaudosite šios formos. Naudoti tik vieną kartą. Unikalus jūsų paskyrai, nesidalinkite. Laukiama dovanų kortelės… (atnaujinkite puslapį, kad patikrintumėte) Atidarykite <a %(a_href)s>QR kodo aukojimo puslapį</a>. Nuskaitykite QR kodą su Alipay programėle arba paspauskite mygtuką, kad atidarytumėte Alipay programėlę. Prašome būti kantriems; puslapis gali užtrukti, kol įsikraus, nes jis yra Kinijoje. <span %(style)s>3</span>Padarykite auką (nuskaitykite QR kodą arba paspauskite mygtuką) Pirkti PYUSD monetą per PayPal Pirkti Bitcoin (BTC) per Cash App Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei suma, kurią aukojate (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite sau. Eikite į „Bitcoin“ (BTC) puslapį Cash App programėlėje. Perveskite Bitcoin į mūsų adresą Mažoms aukoms (mažiau nei $25) gali prireikti naudoti Rush arba Priority. Paspauskite „Send bitcoin“ mygtuką, kad atliktumėte „išėmimą“. Pereikite nuo dolerių prie BTC paspausdami %(icon)s ikoną. Įveskite BTC sumą žemiau ir paspauskite „Send“. Jei susiduriate su sunkumais, žiūrėkite <a %(help_video)s>šį vaizdo įrašą</a>. Greitosios paslaugos yra patogios, bet ima didesnius mokesčius. Galite naudoti tai vietoj kripto biržos, jei norite greitai padaryti didesnę auką ir nesijaudinate dėl $5-10 mokesčio. Būtinai siųskite tikslų kriptovaliutos kiekį, nurodytą aukojimo puslapyje, o ne sumą $USD. Kitu atveju mokestis bus atimtas ir mes negalėsime automatiškai apdoroti jūsų narystės. Kartais patvirtinimas gali užtrukti iki 24 valandų, todėl būtinai atnaujinkite šį puslapį (net jei jis pasibaigęs). Kredito / debeto kortelės instrukcijos Aukoti per mūsų kreditinės / debetinės kortelės puslapį Kai kurie žingsniai mini kripto pinigines, bet nesijaudinkite, jums nereikės nieko mokytis apie kripto. %(coin_name)s instrukcijos Nuskaitykite šį QR kodą naudodami „Crypto Wallet“ programą, kad greitai užpildytumėte mokėjimo informaciją Nuskaitykite QR kodą, kad sumokėtumėte Mes palaikome tik standartinę kriptovaliutų versiją, jokių egzotinių tinklų ar monetų versijų. Priklausomai nuo monetos, operacijos patvirtinimas gali užtrukti iki valandos. Aukoti %(amount)s <a %(a_page)s>šiame puslapyje</a>. Ši auka pasibaigė. Prašome atšaukti ir sukurti naują. Jei jau sumokėjote: Taip, išsiunčiau kvitą el. paštu Jei operacijos metu kriptovaliutų kursas svyravo, būtinai pridėkite kvitą, kuriame nurodytas pradinis valiutos kursas. Labai dėkojame už tai, kad stengiatės naudoti kriptovaliutą, tai mums labai padeda! ❌ Kažkas atsitiko negerai. Perkraukite puslapį iš naujo ir bandykite dar kartą. <span %(span_circle)s>%(circle_number)s</span>Parašykite mums čekį elektroniniu paštu Jei susidūrėte su kokiomis nors problemomis, prašome susisiekti su mumis adresu %(email)s ir pateikti kuo daugiau informacijos (pvz., ekrano nuotraukas). ✅ Ačiū už auką! Anna rankiniu būdu suaktyvins jūsų narystę per kelias dienas. Atsiųskite čeki arba ekrano nuotrauką į šį adresą: Išsiuntę kvitą el. paštu, spustelėkite šį mygtuką, kad Anna galėtų jį peržiūrėti rankiniu būdu (tai gali užtrukti kelias dienas): Siųskite kvitą arba ekrano nuotrauką į savo asmeninį patvirtinimo adresą. NENAUDOKITE šio el. pašto adreso savo PayPal aukai. Atšaukti Taip, prašau atšaukti Ar jūs esate tikras, kad norite atšaukti? Neatšaukite jeigu jau esate sumokėję. ❌ Kažkas negerai atsitiko. Prašome perkrauti puslapį ir bandykite dar kartą. Atlikti naują auką ✅ Jūsų auka buvo atšaukta. Data: %(date)s Identifikatorius: %(id)s Užsakyti iš naujo Būsena: <span %(span_label)s>%(label)s</span> Iš viso: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mėnesį %(duration)s mėnesius, įskaitant %(discounts)s%% nuolaidą)</span> Iš viso: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mėnesį %(duration)s mėnesių)</span> 1. Įveskite savo el. pašto adresą. 2. Pasirinkite mokėjimo metodą. 3. Pasirinkite mokėjimo metodą dar kartą. 4. Pasirinkite „Savarankiškai talpinamą“ piniginę. 5. Spustelėkite „Patvirtinu nuosavybę“. 6. Turėtumėte gauti el. laišką su kvitu. Prašome jį atsiųsti mums, ir mes kuo greičiau patvirtinsime jūsų auką. (galite atšaukti ir sukurti naują auką) Mokėjimo instrukcijos yra pasenusios. Jeigu norėtumėte atlikti naują auką, naudokite “Užsakyti iš naujo” mygtuką aukščiau. Jūs jau apmokėjote. Jeigu vis vien norite peržiūrėti mokėjimo instrukcijas, spauskite čia: Rodyti senas mokėjimo instrukcijas Jei aukojimo puslapis yra blokuojamas, pabandykite kitą interneto ryšį (pvz., VPN arba telefono internetą). Deja, Alipay puslapis dažnai pasiekiamas tik iš <strong>žemyninės Kinijos</strong>. Gali tekti laikinai išjungti savo VPN arba naudoti VPN į žemyninę Kiniją (kartais veikia ir Honkongas). <span %(span_circle)s>1</span>Aukoti Alipay Paaukokite visą sumą %(total)s naudodami <a %(a_account)s>šią Alipay paskyrą</a> Alipay instrukcijos <span %(span_circle)s>1</span>Perduokite į vieną iš mūsų kripto paskyrų Paukokite %(total)s sumą į vieną iš šių adresų: Kriptovaliutos instrukcijos Sekite instrukcijas nusipirkti Bitcoin (BTC). Jums tereikia nusipirkti sumą, kurią norite paaukoti, %(total)s. Įveskite mūsų Bitcoin (BTC) adresą kaip gavėją ir vadovaukitės instrukcijomis, kad išsiųstumėte %(total)s auką: <span %(span_circle)s>1</span>Aukoti naudojant Pix Paaukokite %(total)s sumą naudojant <a %(a_account)s>šią Pix paskyrą Pix instrukcijos <span %(span_circle)s>1</span>Paaukokite per WeChat Paaukokite visą sumą %(total)s naudodami <a %(a_account)s>šį WeChat paskyrą</a> WeChat instrukcijos Naudokite bet kurią iš šių „kredito kortelė į Bitcoin“ greitųjų paslaugų, kurios užtrunka tik kelias minutes: BTC / Bitcoin adresas (išorinė piniginė): BTC / Bitcoin suma: Užpildykite šią formą: Jei kuri nors iš šios informacijos yra pasenusi, prašome pranešti mums el. paštu. Prašome naudoti šį <span %(underline)s>tikslų kiekį</span>. Jūsų bendra kaina gali būti didesnė dėl kredito kortelės mokesčių. Deja, mažoms sumoms tai gali būti daugiau nei mūsų nuolaida. (minimalus: %(minimum)s) (minimalus: %(minimum)s) (minimalus: %(minimum)s) (minimalus: %(minimum)s, pirmam mokėjimui nereikia patvirtinimo) (minimalus: %(minimum)s) (minimalus: %(minimum)s priklausomai nuo šalies, pirmam mokėjimui nereikia patvirtinimo) Sekite instrukcijas, kaip įsigyti PYUSD monetą (PayPal USD). Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei aukojamą sumą (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite. Eikite į „PYUSD“ puslapį savo PayPal programėlėje arba svetainėje. Paspauskite mygtuką „Transfer“ %(icon)s, tada „Send“. Atnaujinti būseną Norėdami atstatyti laikmatį, tiesiog sukurkite naują auką. Būtinai naudokite žemiau nurodytą BTC sumą, <em>NE</em> eurus ar dolerius, kitaip mes negausime teisingos sumos ir negalėsime automatiškai patvirtinti jūsų narystės. Pirkite Bitcoin (BTC) per Revolut Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei suma, kurią aukojate (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite sau. Eikite į „Crypto“ puslapį Revolut programėlėje, kad nusipirktumėte Bitcoin (BTC). Perveskite Bitcoin į mūsų adresą Mažoms aukoms (mažiau nei $25) gali prireikti naudoti Rush arba Priority. Paspauskite „Send bitcoin“ mygtuką, kad atliktumėte „išėmimą“. Pereikite nuo eurų prie BTC paspausdami %(icon)s ikoną. Įveskite BTC sumą žemiau ir paspauskite „Send“. Jei susiduriate su sunkumais, žiūrėkite <a %(help_video)s>šį vaizdo įrašą</a>. Būsena: 1 2 Gidas Žiūrėkite „žingsnis po žingsnio“ vadovą žemiau. Kitu atveju galite nebegalėti prisijungti prie šios paskyros! Jei to dar nepadarėte, užsirašykite savo slaptą raktą prisijungimui: Ačiū už jūsų auką! Likęs laikas: Auka Perkelti %(amount)s į %(account)s Laukiama patvirtinimo (atnaujinkite puslapį, kad patikrintumėte)… Laukiama mokėjimo (atnaujinkite puslapį, kad patikrintumėte)… Ankstesni Greiti atsisiuntimai per pastarąsias 24 valandas skaičiuojami į dienos limitą. Atsisiuntimai iš Greitų Partnerių Serverių pažymėti %(icon)s. Paskutinės 18 valandų Jokie failai dar nebuvo parsiųsti. Parsiusti failai nėra viešai rodomi. Visi laikai nurodyti UTC laiko juostoje. Parsiusti failai Jei atsisiuntėte failą su greitu ir lėtu atsisiuntimu, jis bus rodomas du kartus. Nesijaudinkite per daug, yra daug žmonių, kurie atsisiunčia iš mūsų nurodytų svetainių, ir labai retai kyla problemų. Tačiau, norėdami būti saugūs, rekomenduojame naudoti VPN (mokamą) arba <a %(a_tor)s>Tor</a> (nemokamą). Atsisiunčiau 1984 George Orwell, ar policija ateis į mano duris? Jūs esate Ana! Kas yra Ana? Mes turime vieną stabilų JSON API nariams, kad gautumėte greitą atsisiuntimo URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacija JSON viduje). Kitiems naudojimo atvejams, pvz., visų mūsų failų peržiūrai, individualios paieškos kūrimui ir pan., rekomenduojame <a %(a_generate)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Neapdorotus duomenis galima rankiniu būdu tyrinėti <a %(a_explore)s>per JSON failus</a>. Mūsų neapdorotų torrentų sąrašą taip pat galima atsisiųsti kaip <a %(a_torrents)s>JSON</a>. Ar turite API? Mes čia netalpiname jokių autorių teisių saugomų medžiagų. Esame paieškos sistema, todėl indeksuojame tik jau viešai prieinamus metaduomenis. Atsisiųsdami iš šių išorinių šaltinių, rekomenduojame pasitikrinti savo jurisdikcijos įstatymus dėl to, kas yra leidžiama. Mes nesame atsakingi už kitų talpinamą turinį. Jei turite skundų dėl to, ką matote čia, geriausia būtų susisiekti su originalia svetaine. Mes reguliariai atnaujiname jų pakeitimus mūsų duomenų bazėje. Jei tikrai manote, kad turite galiojantį DMCA skundą, į kurį turėtume reaguoti, prašome užpildyti <a %(a_copyright)s>DMCA / Autorių teisių pažeidimo formą</a>. Mes rimtai žiūrime į jūsų skundus ir atsakysime kuo greičiau. Kaip pranešti apie autorių teisių pažeidimą? Štai keletas knygų, kurios turi ypatingą reikšmę šešėlinių bibliotekų ir skaitmeninio išsaugojimo pasauliui: Kokios jūsų mėgstamiausios knygos? Taip pat norėtume priminti visiems, kad visas mūsų kodas ir duomenys yra visiškai atvirojo kodo. Tai unikali savybė tokiems projektams kaip mūsų — mes nežinome jokio kito projekto su panašiai didžiuliu katalogu, kuris taip pat būtų visiškai atvirojo kodo. Labai laukiame visų, kurie mano, kad blogai valdome savo projektą, kad jie paimtų mūsų kodą ir duomenis ir sukurtų savo šešėlinę biblioteką! Mes tai sakome ne iš pykčio ar kažko panašaus — mes nuoširdžiai manome, kad tai būtų nuostabu, nes tai pakeltų visų lygį ir geriau išsaugotų žmonijos palikimą. Nekenčiu, kaip jūs vykdote šį projektą! Mes norėtume, kad žmonės nustatytų <a %(a_mirrors)s>veidrodžius</a>, ir mes finansiškai palaikysime tai. Kaip galiu padėti? Taip, mes tai darome. Mūsų įkvėpimas rinkti metaduomenis yra Aarono Swartzo tikslas „vienas tinklalapis kiekvienai kada nors išleistai knygai“, kuriam jis sukūrė <a %(a_openlib)s>Open Library</a>. Šis projektas sekasi gerai, tačiau mūsų unikali padėtis leidžia gauti metaduomenis, kurių jie negali. Kitas įkvėpimas buvo mūsų noras sužinoti <a %(a_blog)s>kiek knygų yra pasaulyje</a>, kad galėtume apskaičiuoti, kiek knygų dar turime išsaugoti. Ar renkate metaduomenis? Atkreipkite dėmesį, kad mhut.org blokuoja tam tikrus IP diapazonus, todėl gali prireikti VPN. <strong>Android:</strong> Spustelėkite trijų taškų meniu viršutiniame dešiniajame kampe ir pasirinkite „Pridėti prie pradžios ekrano“. <strong>iOS:</strong> Spustelėkite mygtuką „Dalintis“ apačioje ir pasirinkite „Pridėti prie pagrindinio ekrano“. Mes neturime oficialios mobiliosios programėlės, bet galite įdiegti šią svetainę kaip programėlę. Ar turite mobiliąją programėlę? Prašome siųsti juos į <a %(a_archive)s>Internet Archive</a>. Jie tinkamai juos išsaugos. Kaip galiu paaukoti knygas ar kitą fizinę medžiagą? Kaip galiu užsisakyti knygas? <a %(a_blog)s>Anna’s Blogas</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — reguliarios naujienos <a %(a_software)s>Anna’s Programinė įranga</a> — mūsų atvirojo kodo kodas <a %(a_datasets)s>Duomenų rinkiniai</a> — apie duomenis <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatyvūs domenai Ar yra daugiau išteklių apie Anna’s Archive? <a %(a_translate)s>Versti su Annos programine įranga</a> — mūsų vertimo sistema <a %(a_wikipedia)s>Vikipedija</a> — daugiau apie mus (padėkite atnaujinti šį puslapį arba sukurkite savo kalba!) Pasirinkite norimus nustatymus, palikite paieškos laukelį tuščią, spustelėkite „Ieškoti“, o tada pažymėkite puslapį naudodami savo naršyklės žymėjimo funkciją. Kaip išsaugoti paieškos nustatymus? Kviečiame saugumo tyrėjus ieškoti mūsų sistemų pažeidžiamumų. Mes esame atsakingo atskleidimo šalininkai. Susisiekite su mumis <a %(a_contact)s>čia</a>. Šiuo metu negalime skirti atlygių už klaidų radimą, išskyrus pažeidžiamumus, kurie turi <a %(a_link)s>potencialą pakenkti mūsų anonimiškumui</a>, už kuriuos siūlome atlygius nuo 10 tūkst. iki 50 tūkst. dolerių. Norėtume ateityje pasiūlyti platesnį klaidų radimo atlygių spektrą! Atkreipkite dėmesį, kad socialinės inžinerijos atakos nėra įtrauktos į programos apimtį. Jei domitės puolamąja sauga ir norite padėti archyvuoti pasaulio žinias ir kultūrą, būtinai susisiekite su mumis. Yra daug būdų, kaip galite padėti. Ar turite atsakingo atskleidimo programą? Mes tiesiog neturime pakankamai išteklių, kad visiems pasaulyje suteiktume didelės spartos atsisiuntimus, nors labai norėtume. Jei turtingas rėmėjas norėtų mums tai suteikti, tai būtų nuostabu, bet kol kas stengiamės iš visų jėgų. Esame ne pelno siekiantis projektas, kuris vos išsilaiko iš aukų. Štai kodėl mes įdiegėme dvi nemokamų atsisiuntimų sistemas su mūsų partneriais: bendri serveriai su lėtais atsisiuntimais ir šiek tiek greitesni serveriai su laukimo sąrašu (siekiant sumažinti tuo pačiu metu atsisiunčiančių žmonių skaičių). Mes taip pat turime <a %(a_verification)s>naršyklės patvirtinimą</a> lėtiems atsisiuntimams, nes kitaip botai ir skreperiai juos piktnaudžiautų, dar labiau lėtindami teisėtų vartotojų patirtį. Atkreipkite dėmesį, kad naudojant Tor naršyklę gali tekti koreguoti savo saugumo nustatymus. Žemiausiuose nustatymuose, vadinamuose „Standartinis“, Cloudflare turniketo iššūkis pavyksta. Aukštesniuose nustatymuose, vadinamuose „Saugiau“ ir „Saugiausias“, iššūkis nepavyksta. Kartais didelių failų atsisiuntimai gali nutrūkti viduryje. Rekomenduojame naudoti atsisiuntimo tvarkyklę (pvz., JDownloader), kad automatiškai atnaujintumėte didelius atsisiuntimus. Kodėl lėti atsisiuntimai tokie lėti? Dažniausiai užduodami klausimai (DUK) Naudokite <a %(a_list)s>torentų sąrašo generatorių</a>, kad sugeneruotumėte torentų sąrašą, kuriems labiausiai reikia torentavimo, atsižvelgiant į jūsų saugojimo vietos ribas. Taip, žiūrėkite <a %(a_llm)s>LLM duomenų</a> puslapį. Dauguma torrentų tiesiogiai turi failus, tai reiškia, kad galite nurodyti torrentų klientams atsisiųsti tik reikiamus failus. Norėdami nustatyti, kuriuos failus atsisiųsti, galite <a %(a_generate)s>generuoti</a> mūsų metaduomenis arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Deja, kai kurios torrentų kolekcijos turi .zip arba .tar failus šaknyje, tokiu atveju turite atsisiųsti visą torrentą, kad galėtumėte pasirinkti atskirus failus. Dar nėra lengvai naudojamų įrankių torrentams filtruoti, tačiau laukiame jūsų indėlio. (Tačiau turime <a %(a_ideas)s>keletą idėjų</a> šiam atvejui.) Ilgas atsakymas: Trumpas atsakymas: ne lengvai. Stengiamės išlaikyti minimalų dublikatų ar persidengimų kiekį šiame sąraše esančiuose torrentuose, tačiau tai ne visada įmanoma ir labai priklauso nuo šaltinių bibliotekų politikos. Bibliotekoms, kurios išleidžia savo torrentus, tai nėra mūsų rankose. Torrentams, kuriuos išleidžia „Annos Archyvas“, dublikatų šalinimas atliekamas tik pagal MD5 maišos kodą, tai reiškia, kad skirtingos tos pačios knygos versijos nėra pašalinamos. Taip. Tai iš tikrųjų yra PDF ir EPUB failai, jie tiesiog neturi plėtinio daugelyje mūsų torrentų. Yra dvi vietos, kuriose galite rasti torrent failų metaduomenis, įskaitant failų tipus/plėtinius: 1. Kiekviena kolekcija ar leidimas turi savo metaduomenis. Pavyzdžiui, <a %(a_libgen_nonfic)s>Libgen.rs torrentai</a> turi atitinkamą metaduomenų bazę, talpinamą Libgen.rs svetainėje. Mes paprastai nuorodą į atitinkamus metaduomenų šaltinius pateikiame kiekvienos kolekcijos <a %(a_datasets)s>duomenų rinkinio puslapyje</a>. 2. Rekomenduojame <a %(a_generate)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Jose yra kiekvieno įrašo Anna’s Archive atitikmenų torrent failams (jei yra), žemėlapis, esantis „torrent_paths“ lauke ElasticSearch JSON formatu. Kai kurie torrentų klientai nepalaiko didelių dalių dydžių, kuriuos turi daug mūsų torrentų (naujesniems mes to nebedarome — nors tai yra galiojančios specifikacijos!). Taigi, jei susiduriate su šia problema, pabandykite kitą klientą arba skųskitės savo torrentų kliento kūrėjams. Norėčiau padėti sėti, bet neturiu daug vietos diske. Torentai yra per lėti; ar galiu atsisiųsti duomenis tiesiogiai iš jūsų? Ar galiu atsisiųsti tik dalį failų, pavyzdžiui, tik tam tikrą kalbą ar temą? Kaip tvarkote dublikatus torrentuose? Ar galiu gauti torrentų sąrašą kaip JSON? Nematau PDF ar EPUB torrentuose, tik dvejetainius failus? Ką daryti? Kodėl mano torrentų klientas negali atidaryti kai kurių jūsų torrentų failų / magnetinių nuorodų? Torrentų DUK Kaip įkelti naujas knygas? Prašome peržiūrėti <a %(a_href)s>šį puikų projektą</a>. Ar turite veikimo laiko stebėjimo įrankį? Kas yra Annos Archyvas? Tapkite nariu, kad galėtumėte naudotis greitais atsisiuntimais. Dabar palaikome Amazon dovanų korteles, kreditines ir debetines korteles, kriptovaliutas, Alipay ir WeChat. Jūs išnaudojote greitų atsisiuntimų limitą šiandien. Prieiga Atsisiuntimai kas valandą paskutines 30 dienų. Valandos vidurkis: %(hourly)s. Dienos vidurkis: %(daily)s. Dirbame su partneriais, kad mūsų kolekcijos būtų lengvai ir laisvai prieinamos visiems. Mes tikime, kad kiekvienas turi teisę į kolektyvinę žmonijos išmintį. Ir <a %(a_search)s>ne autorių sąskaita</a>. Annos Archyve naudojami duomenų rinkiniai yra visiškai atviri ir gali būti veidrodiniai dideliais kiekiais naudojant torrentus. <a %(a_datasets)s>Sužinokite daugiau…</a> Ilgalaikis archyvas Visa duomenų bazė Paieška Knygos, straipsniai, žurnalai, komiksai, bibliotekos įrašai, metaduomenys, … Visas mūsų <a %(a_code)s>kodas</a> ir <a %(a_datasets)s>duomenys</a> yra visiškai atviro kodo. <span %(span_anna)s>Anos archyvas</span> yra pelno nesiekiantis projektas, kurio tikslai yra du: <li><strong>Išsaugojimas:</strong> visų žmonijos žinių ir kultūros išsaugojimas.</li><li><strong>Prieiga:</strong> padaryti šias žinias ir kultūrą prieinamas bet kuriam pasaulio gyventojui.</li> Mes turime didžiausią pasaulyje aukštos kokybės tekstinių duomenų kolekciją. <a %(a_llm)s>Sužinokite daugiau…</a> LLM training data 🪩 Veidrodžiai: kvietimas savanoriams Jei valdote aukštos rizikos anoniminį mokėjimo procesorių, prašome susisiekti su mumis. Taip pat ieškome žmonių, norinčių patalpinti skoningas mažas reklamas. Visos pajamos skiriamos mūsų išsaugojimo pastangoms. Išsaugojimas Apskaičiavome, kad išsaugojome apie <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5 %% pasaulio knygų </a>. Mes saugome knygas, straipsnius, komiksus, žurnalus ir dar daugiau, sujungdami šias medžiagas iš įvairių <a href="https://en.wikipedia.org/wiki/Shadow_library">šešėlinių bibliotekų</a>, oficialių bibliotekų ir kitų kolekcijų į vieną vietą. Visi šie duomenys yra saugomi amžinai, nes juos lengva kopijuoti dideliais kiekiais — naudojant torrentus — todėl visame pasaulyje yra daug kopijų. Kai kurios šešėlinės bibliotekos tai jau daro pačios (pvz., Sci-Hub, Library Genesis), o Annos Archyvas „išlaisvina“ kitas bibliotekas, kurios nesiūlo masinio platinimo (pvz., Z-Library) arba visai nėra šešėlinės bibliotekos (pvz., Internet Archive, DuXiu). Šis platus platinimas kartu su atvirojo kodo programine įranga daro mūsų svetainę atsparią pašalinimams ir užtikrina ilgalaikį žmonijos žinių ir kultūros išsaugojimą. Sužinokite daugiau apie <a href="/datasets">mūsų duomenų rinkinius</a>. Jei esate <a %(a_member)s>narys</a>, naršyklės patikrinimas nereikalingas. 🧬&nbsp;SciDB yra Sci-Hub tęsinys. SciDB Atidaryti DOI Sci-Hub <a %(a_paused)s>sustabdė</a> naujų straipsnių įkėlimą. Tiesioginė prieiga prie %(count)s akademinių straipsnių 🧬&nbsp;SciDB yra Sci-Hub tęsinys, su pažįstama sąsaja ir tiesioginiu PDF peržiūrėjimu. Įveskite savo DOI, kad peržiūrėtumėte. Turime visą Sci-Hub kolekciją, taip pat naujus straipsnius. Daugumą galima peržiūrėti tiesiogiai su pažįstama sąsaja, panašia į Sci-Hub. Kai kuriuos galima atsisiųsti per išorinius šaltinius, tokiu atveju rodome nuorodas į juos. Jūs galite labai padėti, sėdami torrentus. <a %(a_torrents)s>Sužinokite daugiau…</a> >%(count)s siuntėjai <%(count)s siuntėjai %(count_min)s–%(count_max)s sėjėjai 🤝 Ieškome savanorių Būdami ne pelno siekiantis, atvirojo kodo projektas, mes visada ieškome žmonių, kurie galėtų padėti. IPFS atsisiuntimai Išrikiuoti pagal %(by)s, sukurta <span %(span_time)s>%(time)s</span> Išsaugoti ❌ Kažkas negerai atsitiko. Prašome bandyti dar kartą. ✅ Išsaugota. Perkraukite puslapį. Sąrašas tuščias. Redaguoti Pridėti arba pašalinti iš šio sąrašo randant failą ir atidarant “Sąrašai” skirtuką. Sąrašas Kaip mes galime padėti Dubliavimo pašalinimas (deduplikacija) Teksto ir metaduomenų ištraukimas OCR Galime suteikti didelės spartos prieigą prie visų mūsų kolekcijų, taip pat prie neišleistų kolekcijų. Tai yra įmonės lygio prieiga, kurią galime suteikti už aukas, siekiančias dešimtis tūkstančių USD. Taip pat esame pasirengę mainyti tai į aukštos kokybės kolekcijas, kurių dar neturime. Galime grąžinti jums pinigus, jei galėsite suteikti mums duomenų praturtinimą, pvz.: Palaikykite ilgalaikį žmonijos žinių archyvavimą, tuo pačiu gaudami geresnius duomenis savo modeliui! <a %(a_contact)s>Susisiekite su mumis</a>, kad aptartume, kaip galime bendradarbiauti. Gerai žinoma, kad LLM klesti aukštos kokybės duomenimis. Mes turime didžiausią knygų, straipsnių, žurnalų ir kt. kolekciją pasaulyje, kuri yra viena iš aukščiausios kokybės tekstinių šaltinių. LLM duomenys Unikalus mastas ir diapazonas Mūsų kolekcijoje yra daugiau nei šimtas milijonų failų, įskaitant akademinius žurnalus, vadovėlius ir žurnalus. Šį mastą pasiekiame sujungdami dideles esamas saugyklas. Kai kurios mūsų šaltinių kolekcijos jau yra prieinamos dideliais kiekiais (Sci-Hub ir dalys Libgen). Kitus šaltinius išlaisvinome patys. <a %(a_datasets)s>Datasets</a> rodo visą apžvalgą. Mūsų kolekcijoje yra milijonai knygų, straipsnių ir žurnalų iš laikotarpio prieš e-knygų erą. Didelė dalis šios kolekcijos jau buvo OCR'inta ir turi mažai vidinio dubliavimo. Tęsti Jei praradote savo kodą, prašome <a %(a_contact)s>susisiekti su mumis</a> ir pateikti kuo daugiau informacijos. Gali tekti laikinai sukurti naują paskyrą, kad galėtumėte su mumis susisiekti. Prašome <a %(a_account)s>prisijungti</a> norėdami peržiūrėti šį puslapį.</a> Kad šlamšto robotai nesukurtų daug paskyrų, pirmiausia turime patvirtinti jūsų naršyklę. Jei pakliūsite į begalinę kilpą, rekomenduojame įdiegti <a %(a_privacypass)s>Privacy Pass</a>. Taip pat gali padėti reklamos blokatoriaus ir kitų naršyklės plėtinių išjungimas. Prisijungti / Registruotis Annės Archyvas laikinai neveikia dėl priežiūros darbų. Prašome sugrįžti po valandos. Alternatyvus autorius Alternatyvus aprašymas Alternatyvus leidimas Alternatyvus plėtinys Alternatyvus failo pavadinimas Alternatyvus leidėjas Alternatyvus pavadinimas data, kai buvo atvertas šaltinis Skaityti daugiau… aprašymas Ieškoti Anos Archyve CADAL SSNO numerio Ieškoti Annos Archyve pagal DuXiu SSID numerį Ieškoti Annos Archyve pagal DuXiu DXID numerį Ieškoti Annos Archyve pagal ISBN Ieškoti Annos Archyve pagal OCLC (WorldCat) numerį Ieškoti Annos Archyve pagal Open Library ID Annės Archyvo internetinė peržiūros programa %(count)s paveikti puslapiai Po atsisiuntimo: Geresnė šio failo versija gali būti prieinama adresu %(link)s Masiniai torrentų atsisiuntimai kolekcija Naudokite internetinius įrankius formatams konvertuoti. Rekomenduojami konvertavimo įrankiai: %(links)s Dideliems failams rekomenduojame naudoti atsisiuntimo tvarkyklę, kad išvengtumėte trigdžių. Rekomenduojamos atsisiuntimo tvarkyklės: %(links)s EBSCOhost eBook Index (tik ekspertams) (taip pat paspauskite „get“ (gauti) viršuje) (paspauskite „get“ (gauti) viršuje) Išoriniai atsisiuntimai Šiandieną dar turite %(remaining)s greitus parsisiuntimus. Ačiū, kad esate narys! ❤️ Jūs išnaudojote greitų atsisiuntimų dienos limitą. Jūs neseniai atsisiuntėte šį failą. Nuorodos galioja tam tikrą laiką. Tapk <a %(a_membership)s>nariu</a>, kad prisidėtum prie ilgalaikio knygų ir popieriaus tausojimo. Rodydami padėką už jūsų palaikymą, dovanojame greitus atsisiuntimus. ❤️ 🚀 Greiti atsisiuntimai 🐢 Lėti atsisiuntimai Skolintis iš Internet Archive IPFS Vartai #%(num)d (gali tekti pabandyti kelis kartus naudojant IPFS) Libgen.li Libgen.rs grožinė literatūra Libgen.rs ne grožinė literatūra jų reklamos yra žinomos dėl kenkėjiškos programinės įrangos, todėl naudokite reklamos blokatorių arba nespauskite ant reklamų Amazon „Siųsti į Kindle“ djazz „Siųsti į Kobo/Kindle“ MagzDB ManualsLib Nexus/STC (Nexus/STC failų gali būti neįmanoma atsisiųsti) Jokių atsisiuntimų nerasta. Visi parsiuntimo metodai turi tą patį failą ir turi būti saugus parsisiųsti, tačiau visada reikia būti atsargiam parsisiunčiant failus iš interneto. Pavyzdžiui, reguliariai atnaujinti savo programinę įrangą. (be nukreipimo) Atidaryti mūsų peržiūros programoje (atidaryti peržiūros programoje) Pasirinkimas %(num)d: %(link)s %(extra)s Rasti originalų įrašą CADAL Ieškoti rankiniu būdu DuXiu Rasti originalų įrašą ISBNdb Rasti originalų įrašą WorldCat Rasti originalų įrašą Open Library Ieškokite įvairiose kitose duomenų bazėse pagal ISBN (tik neįgaliesiems su skaitymo negalia) PubMed Jums reikės el. knygų arba PDF skaitytuvo, kad atidarytumėte failą (priklausomai nuo formato). Rekomenduojami el. knygų skaitytuvai: %(links)s Annos Archyvas 🧬 SciDB Sci-Hub: %(doi)s (susijęs DOI gali būti nepasiekiamas Sci-Hub) Galite siųsti tiek PDF, tiek EPUB failus į savo Kindle arba Kobo el. skaitytuvą. Rekomenduojami įrankiai: %(links)s Daugiau informacijos <a %(a_slow)s>DUK</a>. Palaikyti autorius ir bibliotekas Jei jums tai patinka ir galite sau leisti, apsvarstykite galimybę įsigyti originalą arba tiesiogiai paremti autorius. Jei tai yra jūsų vietinėje bibliotekoje, apsvarstykite galimybę pasiskolinti nemokamai. Partnerio serverio atsisiuntimai laikinai nepasiekiami šiam failui. torentas Iš patikimų partnerių. Z-Library Z-Library Tor tinkle (reikalauja Tor naršyklės) rodyti išorinius atsisiuntimus <span class="font-bold">❌ Šis failas gali turėti problemų ir todėl buvo paslėptas iš šaltinio bibliotekos.</span> Kartais tai yra atliekama dėl autorines teises turinčio asmens užklausos, kartais dėl to, kad yra pasiekiama geresnė alternatyva, o kitais kartais, kadangi yra problema su pačiu failu. Jis gali būti tinkamas parsisiųsti, tačiau rekomenduojame pirmiausiai ieškoti alternatyvos. Plačiau: Jeigu jūs vis dar norite parsisiųsti šį failą, naudokite tik patikimą, atnaujintą programinę įrangą norint jį atidaryti. metaduomenų komentarai AA: Ieškoti Annos Archyve „%(name)s“ Koduotės naršyklė: Peržiūrėti Koduotės naršyklėje „%(name)s“ URL: Interneto svetainė: Jei turite šį failą ir jis dar nėra prieinamas Anna’s Archive, apsvarstykite galimybę <a %(a_request)s>jį įkelti</a>. Internet Archive kontroliuojamo skaitmeninio skolinimo failas „%(id)s“ Tai yra failo iš Internet Archive įrašas, o ne tiesiogiai atsisiunčiamas failas. Galite pabandyti pasiskolinti knygą (nuoroda žemiau) arba naudoti šį URL, kai <a %(a_request)s>prašote failo</a>. Tobulinti metaduomenis CADAL SSNO %(id)s metaduomenų įrašas Tai yra metaduomenų įrašas, o ne atsisiunčiamas failas. Galite naudoti šį URL norint <a %(a_request)s>paprašyti</a>. DuXiu SSID %(id)s metaduomenų įrašas ISBNdb %(id)s metaduomenų įrašas MagzDB ID %(id)s metaduomenų įrašas Nexus/STC ID %(id)s metaduomenų įrašas OCLC (WorldCat) numerio %(id)s metaduomenų įrašas Open Library %(id)s metaduomenų įrašas Sci-Hub failas “%(id)s” Nerasta "%(md5_input)s" nebuvo rasta mūsų duomenų bazėje. Pridėti komentarą (%(count)s) MD5 galite gauti iš URL, pvz. Geresnės šio failo versijos MD5 (jei taikoma). Užpildykite tai, jei yra kitas failas, kuris labai panašus į šį failą (ta pati laida, tas pats failo plėtinys, jei galite rasti), kurį žmonės turėtų naudoti vietoj šio failo. Jei žinote geresnę šio failo versiją už Anna’s Archive ribų, prašome <a %(a_upload)s>ją įkelti</a>. Kažkas nepavyko. Perkraukite puslapį ir bandykite dar kartą. Jūs palikote komentarą. Gali praeiti minutė, kol jis bus rodomas. Prašome naudoti <a %(a_copyright)s>DMCA / Autorinių teisių pretenzijos formą</a>. Apibūdinkite problemą (privaloma) Jei šis failas yra puikios kokybės, galite diskutuoti apie jį čia! Jei ne, naudokite mygtuką „Pranešti apie failo problemą“. Puiki failo kokybė (%(count)s) Failo kokybė Sužinokite, kaip patys galite <a %(a_metadata)s>patobulinti failo metaduomenis</a>. Problemos aprašymas Prašome <a %(a_login)s>prisijungti</a>. Man labai patiko ši knyga! Padėkite bendruomenei pranešdami apie šio failo kokybę! 🙌 Kažkas nepavyko. Perkraukite puslapį ir bandykite dar kartą. Pranešti apie failo problemą (%(count)s) Ačiū, kad pateikėte savo pranešimą. Jis bus rodomas šiame puslapyje ir peržiūrima rankiniu būdu Annos (kol neturime tinkamos moderavimo sistemos). Palikti komentarą Pateikti pranešimą Kas negerai su šiuo failu? Skolintis (%(count)s) Komentarai (%(count)s) Atsisiuntimai (%(count)s) Naršyti metaduomenis (%(count)s) Sąrašai (%(count)s) Statistika (%(count)s) Informaciją apie šį konkretų failą rasite jo <a %(a_href)s>JSON faile</a>. Tai yra failas, valdomas <a %(a_ia)s>IA kontroliuojamo skaitmeninio skolinimo</a> bibliotekos ir indeksuotas Annos Archyve paieškai. Informaciją apie įvairius mūsų sudarytus duomenų rinkinius rasite <a %(a_datasets)s>Duomenų rinkinių puslapyje</a>. Metaduomenys iš susieto įrašo Patobulinkite metaduomenis esančius Open Library „Failo MD5“ yra maiša, kuri apskaičiuojama iš failo turinio ir yra pakankamai unikali pagal tą turinį. Visos šešėlinės bibliotekos, kurias mes indeksavome čia, pirmiausia naudoja MD5 failams identifikuoti. Failas gali pasirodyti keliose šešėlinėse bibliotekose. Informaciją apie įvairius mūsų sudarytus duomenų rinkinius rasite <a %(a_datasets)s>Duomenų rinkinių puslapyje</a>. Pranešti apie failo kokybę Iš viso atsisiuntimų: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Čekijos metaduomenys %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Knygos %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Įspėjimas: keli susieti įrašai: Kai žiūrite knygą Anna’s Archive, galite matyti įvairius laukus: pavadinimą, autorių, leidėją, leidimą, metus, aprašymą, failo pavadinimą ir daugiau. Visa ši informacija vadinama <em>metaduomenimis</em>. Kadangi mes sujungiame knygas iš įvairių <em>šaltinių bibliotekų</em>, rodome bet kokius metaduomenis, kurie yra prieinami toje šaltinio bibliotekoje. Pavyzdžiui, knygai, kurią gavome iš Library Genesis, rodysime pavadinimą iš Library Genesis duomenų bazės. Kartais knyga yra <em>keliose</em> šaltinių bibliotekose, kurios gali turėti skirtingus metaduomenų laukus. Tokiu atveju mes tiesiog rodome ilgiausią kiekvieno lauko versiją, nes tikimės, kad ji turės naudingiausią informaciją! Vis tiek rodysime kitus laukus po aprašymu, pvz., kaip „alternatyvų pavadinimą“ (bet tik jei jie skiriasi). Mes taip pat ištraukiame <em>kodų</em>, tokių kaip identifikatoriai ir klasifikatoriai, iš šaltinio bibliotekos. <em>Identifikatoriai</em> unikalūs tam tikram knygos leidimui; pavyzdžiai yra ISBN, DOI, Open Library ID, Google Books ID arba Amazon ID. <em>Klasifikatoriai</em> grupuoja panašias knygas; pavyzdžiai yra Dewey Decimal (DCC), UDC, LCC, RVK arba GOST. Kartais šie kodai yra aiškiai susieti šaltinių bibliotekose, o kartais mes galime juos ištraukti iš failo pavadinimo ar aprašymo (daugiausia ISBN ir DOI). Mes galime naudoti identifikatorius, kad rastume įrašus <em>tik metaduomenų kolekcijose</em>, tokiose kaip OpenLibrary, ISBNdb arba WorldCat/OCLC. Mūsų paieškos sistemoje yra specifinis <em>metaduomenų skirtukas</em>, jei norite naršyti tas kolekcijas. Mes naudojame atitinkančius įrašus, kad užpildytume trūkstamus metaduomenų laukus (pvz., jei trūksta pavadinimo), arba pvz., kaip „alternatyvų pavadinimą“ (jei yra esamas pavadinimas). Norėdami pamatyti, iš kur tiksliai atėjo knygos metaduomenys, žiūrėkite <em>„Techninės detalės“ skirtuką</em> knygos puslapyje. Jame yra nuoroda į neapdorotą JSON failą, kuriame yra nuorodos į neapdorotus originalių įrašų JSON failus. Daugiau informacijos rasite šiuose puslapiuose: <a %(a_datasets)s>Duomenų rinkiniai</a>, <a %(a_search_metadata)s>Paieška (metaduomenų skirtukas)</a>, <a %(a_codes)s>Kodų naršyklė</a> ir <a %(a_example)s>Pavyzdinis metaduomenų JSON</a>. Galiausiai, visi mūsų metaduomenys gali būti <a %(a_generated)s>generuojami</a> arba <a %(a_downloaded)s>atsisiunčiami</a> kaip ElasticSearch ir MariaDB duomenų bazės. Informacija Jūs galite prisidėti prie knygų išsaugojimo tobulindami metaduomenis! Pirmiausia perskaitykite informaciją apie metaduomenis Anna’s Archive, o tada sužinokite, kaip tobulinti metaduomenis per Open Library ir užsidirbkite nemokamą narystę Anna’s Archive. Tobulinti metaduomenis Taigi, jei susiduriate su failu, kuriame yra blogi metaduomenys, kaip turėtumėte juos pataisyti? Galite eiti į šaltinio biblioteką ir laikytis jos procedūrų metaduomenims taisyti, bet ką daryti, jei failas yra keliose šaltinių bibliotekose? Yra vienas identifikatorius, kuris Anna’s Archive yra traktuojamas ypatingai. <strong>Annas_archive md5 laukas Open Library visada viršija visus kitus metaduomenis!</strong> Pirmiausia grįžkime atgal ir sužinokime apie Open Library. Open Library buvo įkurta 2006 metais Aaron Swartz su tikslu „vienas tinklalapis kiekvienai kada nors išleistai knygai“. Tai tarsi Vikipedija knygų metaduomenims: visi gali ją redaguoti, ji yra laisvai licencijuota ir gali būti atsisiunčiama dideliais kiekiais. Tai knygų duomenų bazė, kuri labiausiai atitinka mūsų misiją — iš tiesų, Anna’s Archive buvo įkvėpta Aaron Swartz vizijos ir gyvenimo. Vietoj to, kad išradinėtume dviratį iš naujo, nusprendėme nukreipti savo savanorius į Open Library. Jei matote knygą su neteisingais metaduomenimis, galite padėti šiuo būdu: Atkreipkite dėmesį, kad tai veikia tik knygoms, ne akademiniams straipsniams ar kitiems failų tipams. Kitiems failų tipams vis tiek rekomenduojame rasti šaltinio biblioteką. Gali prireikti kelių savaičių, kol pakeitimai bus įtraukti į Annos Archyvą, nes mums reikia atsisiųsti naujausią Open Library duomenų iškrovą ir atnaujinti mūsų paieškos indeksą.  Eikite į <a %(a_openlib)s>Open Library svetainę</a>. Raskite teisingą knygos įrašą. <strong>ĮSPĖJIMAS:</strong> būtinai pasirinkite teisingą <strong>leidimą</strong>. Open Library yra „darbai“ ir „leidimai“. „Darbas“ galėtų būti „Haris Poteris ir Išminties akmuo“. „Leidimas“ galėtų būti: 1997 m. pirmasis leidimas, išleistas Bloomsbery, su 256 puslapiais. 2003 m. minkštų viršelių leidimas, išleistas Raincoast Books, su 223 puslapiais. 2000 m. lenkiškas vertimas „Harry Potter I Kamie Filozoficzn“ iš Media Rodzina su 328 puslapiais. Visi šie leidimai turi skirtingus ISBN ir skirtingą turinį, todėl būtinai pasirinkite tinkamą! Redaguokite įrašą (arba sukurkite jį, jei jo nėra) ir pridėkite kuo daugiau naudingos informacijos! Jūs jau čia, tad padarykite įrašą tikrai nuostabų. Skiltyje „ID numeriai“ pasirinkite „Annos Archyvas“ ir pridėkite knygos MD5 iš Annos Archyvo. Tai yra ilga raidžių ir skaičių eilutė po „/md5/“ URL adrese. Pabandykite rasti kitus failus Annos Archyve, kurie taip pat atitinka šį įrašą, ir pridėkite juos taip pat. Ateityje galėsime grupuoti juos kaip dublikatus Annos Archyvo paieškos puslapyje. Kai baigsite, užsirašykite URL, kurį ką tik atnaujinote. Kai atnaujinsite bent 30 įrašų su Annos Archyvo MD5, atsiųskite mums <a %(a_contact)s>el. laišką</a> ir atsiųskite sąrašą. Mes suteiksime jums nemokamą narystę Annos Archyve, kad galėtumėte lengviau atlikti šį darbą (ir kaip padėką už jūsų pagalbą). Tai turi būti aukštos kokybės redagavimai, kurie prideda daug informacijos, kitaip jūsų prašymas bus atmestas. Jūsų prašymas taip pat bus atmestas, jei bet kuris redagavimas bus atšauktas arba pataisytas Open Library moderatoriais. Open Library susiejimas Jei reikšmingai įsitrauksite į mūsų darbo plėtrą ir operacijas, galime aptarti didesnės dalies aukų pajamų pasidalijimą su jumis, kad galėtumėte jas panaudoti pagal poreikį. Mes mokėsime už prieglobą tik tada, kai viską įrengsite ir parodysite, kad galite atnaujinti archyvą. Tai reiškia, kad pirmus 1-2 mėnesius turėsite mokėti iš savo kišenės. Jūsų laikas nebus kompensuojamas (ir mūsų taip pat), nes tai yra grynai savanoriškas darbas. Mes esame pasirengę padengti prieglobos ir VPN išlaidas, iš pradžių iki 200 USD per mėnesį. Tai pakanka pagrindiniam paieškos serveriui ir DMCA apsaugotam tarpininkui. Prieglobos išlaidos Prašome <strong>nesikreipti į mus</strong> leidimo ar pagrindinių klausimų. Veiksmai kalba garsiau nei žodžiai! Visa informacija yra prieinama, tad tiesiog pradėkite kurti savo veidrodį. Nedvejodami pateikite bilietus ar sujungimo užklausas mūsų Gitlab, kai susiduriate su problemomis. Gali prireikti sukurti kai kurias veidrodžio specifines funkcijas kartu su jumis, pavyzdžiui, pervadinti iš „Annos Archyvas“ į jūsų svetainės pavadinimą, (pradžioje) išjungti vartotojų paskyras arba susieti atgal į mūsų pagrindinę svetainę iš knygų puslapių. Kai jūsų veidrodis veiks, prašome susisiekti su mumis. Norėtume peržiūrėti jūsų OpSec, ir kai tai bus patikima, mes susiesime su jūsų veidrodžiu ir pradėsime glaudžiau bendradarbiauti su jumis. Iš anksto dėkojame visiems, kurie nori prisidėti šiuo būdu! Tai nėra silpnų nervų žmonėms, bet tai sustiprintų didžiausios tikrai atviros bibliotekos žmonijos istorijoje ilgaamžiškumą. Pradžia Norėdami padidinti Anna’s Archive atsparumą, ieškome savanorių, kurie galėtų valdyti veidrodžius. Jūsų versija aiškiai išskiriama kaip veidrodis, pvz., „Bobo Archyvas, Annos Archyvo veidrodis“. Jūs esate pasirengę prisiimti su šiuo darbu susijusią riziką, kuri yra reikšminga. Jūs giliai suprantate reikiamą operacinį saugumą. <a %(a_shadow)s>Šių</a> <a %(a_pirate)s>įrašų</a> turinys jums yra savaime suprantamas. Iš pradžių mes nesuteiksime jums prieigos prie mūsų partnerių serverių atsisiuntimų, bet jei viskas klostysis gerai, galime tai pasidalinti su jumis. Jūs valdote Anna’s Archive atvirojo kodo bazę ir reguliariai atnaujinate tiek kodą, tiek duomenis. Jūs esate pasirengę prisidėti prie mūsų <a %(a_codebase)s>kodo bazės</a> — bendradarbiaudami su mūsų komanda — kad tai įvyktų. Mes ieškome šito: Veidrodžiai: kvietimas savanoriams Atlikti dar vieną auką. Kol kas jokių aukų. <a %(a_donate)s>Atlikti mano pirmą auką.</a> Aukų detalės nėra viešai rodomos. Mano aukos 📡 Norėdami masiškai veidrodinti mūsų kolekciją, apsilankykite <a %(a_datasets)s>Datasets</a> ir <a %(a_torrents)s>Torrents</a> puslapiuose. Atsisiuntimai iš jūsų IP adreso per pastarąsias 24 valandas: %(count)s. 🚀 Norėdami greičiau atsisiųsti ir praleisti naršyklės patikrinimus, <a %(a_membership)s>tapkite nariu</a>. Atsisiųsti iš partnerio svetainės Kol laukiate, galite toliau naršyti Annės Archyvą kitame skirtuke (jei jūsų naršyklė palaiko skirtukų atnaujinimą fone). Galite laisvai laukti, kol įkels kelis atsisiuntimo puslapius vienu metu (bet prašome atsisiųsti tik vieną failą vienu metu iš kiekvieno serverio). Kai gausite atsisiuntimo nuorodą, ji galios kelias valandas. Ačiū, kad laukiate, tai padeda išlaikyti svetainę prieinamą visiems nemokamai! 😊 🔗 Visi šio failo atsisiuntimo nuorodos: <a %(a_main)s>Failo pagrindinis puslapis</a>. ❌ Lėti atsisiuntimai nėra prieinami per Cloudflare VPN ar iš Cloudflare IP adresų. ❌ Lėti atsisiuntimai galimi tik per oficialią svetainę. Apsilankykite %(websites)s. 📚 Naudokite šią URL norėdami atsisiųsti: <a %(a_download)s>Atsisiųsti dabar</a>. Kad visi turėtų galimybę nemokamai atsisiųsti failus, turite palaukti prieš atsisiųsdami šį failą. Prašome palaukti <span %(span_countdown)s>%(wait_seconds)s</span> sekundžių, kad galėtumėte atsisiųsti šį failą. Įspėjimas: per pastarąsias 24 valandas iš jūsų IP adreso buvo daug atsisiuntimų. Atsisiuntimai gali būti lėtesni nei įprastai. Jei naudojate VPN, bendrą interneto ryšį arba jūsų IPT dalijasi IP adresais, šis įspėjimas gali būti dėl to. Išsaugoti ❌ Kažkas atsitiko negerai. Bandykite dar kartą. ✅ Išsaugota. Perkraukite puslapį. Pasikeisti rodomą vardą. Jūsų identifikatorius (dalis po “#”) negali būti pakeistas. Profilis sukurtas <span %(span_time)s>%(time)s</span> redaguoti Sąrašai Sukurkite naują sąrašą randant failą ir atidarant skirtuką “Sąrašai”. Kol kas jokių sąrašų Profilis nerastas. Profilis Šiuo metu negalime priimti knygų užklausų. Nesiųskite mums el. laiškų su knygų užklausomis. Prašome pateikti savo užklausas Z-Library arba Libgen forumuose. Įrašas Annės Archyve DOI: %(doi)s Atsisiųsti SciDB Nexus/STC Peržiūra dar nepasiekiama. Atsisiųskite failą iš <a %(a_path)s>Annos Archyvas</a>. Norėdami palaikyti prieinamumą ir ilgalaikį žmonijos žinių išsaugojimą, tapkite <a %(a_donate)s>nariu</a>. Kaip priedas, 🧬&nbsp;SciDB nariams įkeliama greičiau, be jokių apribojimų. Neveikia? Pabandykite <a %(a_refresh)s>atnaujinti</a>. Sci-Hub Pridėti konkretų paieškos lauką Ieškoti aprašymų ir metaduomenų komentarų Išleidimo metai Išplėstinė Prieiga Turinys Rodyti Sąrašas Lentelė Failo tipas Kalba Rūšiuoti pagal Didžiausia Labiausiai atitinka Naujausia (bylos dydis) (atviro kodo) (leidimo metai) Seniausia Atsitiktinis Mažiausia Šaltinis išgauta ir atviro kodo AA Skaitmeninis skolinimas (%(count)s) Žurnalų straipsniai (%(count)s) Mes radome atitikmenis: %(in)s. Galite nurodyti ten rastą URL, kai <a %(a_request)s>prašote failo</a>. Metaduomenys (%(count)s) Norėdami tyrinėti paieškos indeksą pagal kodus, naudokite <a %(a_href)s>Kodo tyrinėtoją</a>. Paieškos indeksas yra atnaujinamas kas mėnesį. Šiuo momentu jis yra iš %(last_data_refresh_date)s. Dėl daugiau techninės informacijos, atsidarykite %(link_open_tag)sduomenų rinkinių puslapį</a>. Neįtraukti Įtraukti tik Nepatikrinta daugiau… Kitas … Ankstesnis Šis paieškos indeksas šiuo metu apima metaduomenis iš Internet Archive kontroliuojamos skaitmeninės skolinimo bibliotekos. <a %(a_datasets)s>Daugiau apie mūsų duomenų rinkinius</a>. Daugiau skaitmeninių skolinimo bibliotekų rasite <a %(a_wikipedia)s>Vikipedijoje</a> ir <a %(a_mobileread)s>MobileRead Wiki</a>. Dėl DMCA / autorių teisių pretenzijų <a %(a_copyright)s>spauskite čia</a>. Atsisiuntimo laikas Klaida atliekant paiešką. Pabandykite <a %(a_reload)s>perkrauti puslapį</a>. Jei problema išlieka, prašome parašyti mums el. paštu %(email)s. Greitasis atsisiuntimas Iš tiesų, kiekvienas gali padėti išsaugoti šiuos failus, sėjant mūsų <a %(a_torrents)s>vieningą torrentų sąrašą</a>. ➡️ Kartais tai įvyksta neteisingai, kai paieškos serveris yra lėtas. Tokiais atvejais gali padėti <a %(a_attrs)s>puslapio perkrovimas</a>. ❌ Šis failas gali turėti problemų. Ieškote straipsnių? Šis paieškos indeksas šiuo metu apima metaduomenis iš įvairių metaduomenų šaltinių. <a %(a_datasets)s>Daugiau apie mūsų duomenų rinkinius</a>. Yra daugybė metaduomenų šaltinių rašytiniams darbams visame pasaulyje. <a %(a_wikipedia)s>Šis Vikipedijos puslapis</a> yra gera pradžia, bet jei žinote kitų gerų sąrašų, praneškite mums. Metaduomenims mes rodome originalius įrašus. Mes nesujungiame įrašų. Šiuo metu turime išsamiausią pasaulyje atvirą knygų, straipsnių ir kitų rašytinių darbų katalogą. Mes veidrodžiuojame Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ir daugiau</a>. <span class="font-bold">Jokių failų nerasta</span> Bandykite mačiau arba kitokius terminus ir filtrus. Rezultatai %(from)s-%(to)s (%(total)s iš viso) Jei rasite kitų „šešėlinių bibliotekų“, kurias turėtume atspindėti, arba jei turite klausimų, susisiekite su mumis adresu %(email)s. %(num)d daliniai atitikmenys %(num)d+ daliniai atitikmenys Įveskite į laukelį, kad ieškotumėte failų skaitmeninėse skolinimo bibliotekose. Įveskite laukelyje, kad ieškotumėte mūsų kataloge %(count)s tiesiogiai atsisiunčiamų failų, kuriuos mes <a %(a_preserve)s>saugome amžinai</a>. Įveskite į laukelį, kad ieškotumėte. Įveskite laukelyje, kad ieškotumėte mūsų kataloge %(count)s akademinių straipsnių ir žurnalų straipsnių, kuriuos mes <a %(a_preserve)s>išsaugome amžinai</a>. Įveskite į laukelį, kad ieškotumėte metaduomenų iš bibliotekų. Tai gali būti naudinga, kai <a %(a_request)s>prašote failo</a>. Patarimas: naudokite klaviatūros sparčiuosius klavišus „/“ (paieškos fokusas), „enter“ (paieška), „j“ (aukštyn), „k“ (žemyn), „<“ (ankstesnis puslapis), „>“ (kitas puslapis) greitesnei navigacijai. Tai yra metaduomenų įrašai, <span %(classname)s>ne</span> atsisiunčiami failai. Paieškos nustatymai Paieška Skaitmeninė skolinimo paslauga Atsisiųsti Žurnalų straipsniai Metaduomenys Nauja paieška %(search_input)s - Ieškoti Paieška užtruko per ilgai, todėl galite matyti netikslius rezultatus. Kartais padeda <a %(a_reload)s>puslapio perkrovimas</a>. Paieška užtruko per ilgai, kas yra įprasta plačioms užklausoms. Filtrų skaičiai gali būti netikslūs. Dėl didelių įkėlimų (daugiau nei 10 000 failų), kurie nėra priimami Libgen ar Z-Library, susisiekite su mumis adresu %(a_email)s. Libgen.li atveju, pirmiausia prisijunkite prie <a %(a_forum)s>jų forumo</a> su vartotojo vardu %(username)s ir slaptažodžiu %(password)s, tada grįžkite į jų <a %(a_upload_page)s>įkėlimo puslapį</a>. Kol kas siūlome įkelti naujas knygas į Library Genesis. Čia yra <a %(a_guide)s>patogus gidas</a>. Atminkite, kad abi svetainės, kurias indeksuojame šioje svetainėje, yra iš tos pačios įkėlimo sistemos. Mažiems įkėlimams (iki 10 000 failų) prašome įkelti juos į abu %(first)s ir %(second)s. Arba galite įkelti juos į Z-Library <a %(a_upload)s>čia</a>. Norėdami įkelti akademinius straipsnius, prašome taip pat (be Library Genesis) įkelti į <a %(a_stc_nexus)s>STC Nexus</a>. Jie yra geriausia šešėlinė biblioteka naujiems straipsniams. Mes dar jų neintegravome, bet tai padarysime ateityje. Galite naudoti jų <a %(a_telegram)s>įkėlimo botą Telegram</a> arba susisiekti su adresu, nurodytu jų prisegtoje žinutėje, jei turite per daug failų, kad galėtumėte įkelti šiuo būdu. <span %(label)s>Intensyvus savanoriškas darbas (USD$50-USD$5,000 premijos):</span> jei galite skirti daug laiko ir/arba išteklių mūsų misijai, norėtume glaudžiau bendradarbiauti su jumis. Galiausiai galite prisijungti prie vidinės komandos. Nors mūsų biudžetas yra ribotas, galime skirti <span %(bold)s>💰 pinigines premijas</span> už intensyviausią darbą. <span %(label)s>Lengvas savanoriškas darbas:</span> jei galite skirti tik kelias valandas čia ir ten, vis tiek yra daug būdų, kaip galite padėti. Mes apdovanojame nuolatinius savanorius <span %(bold)s>🤝 narystėmis Annos Archyve</span>. Annos Archyvas remiasi tokiais savanoriais kaip jūs. Mes laukiame visų įsipareigojimo lygių ir turime dvi pagrindines pagalbos kategorijas, kurių ieškome: Jei negalite skirti savo laiko savanoriškai veiklai, vis tiek galite mums labai padėti <a %(a_donate)s>aukodami pinigus</a>, <a %(a_torrents)s>dalindamiesi mūsų torrentais</a>, <a %(a_uploading)s>įkeldami knygas</a> arba <a %(a_help)s>pasakodami draugams apie Annos Archyvą</a>. <span %(bold)s>Įmonės:</span> siūlome didelės spartos tiesioginę prieigą prie mūsų kolekcijų mainais už įmonės lygio auką arba mainais už naujas kolekcijas (pvz., naujus skenavimus, OCR duomenų rinkinius, mūsų duomenų praturtinimą). <a %(a_contact)s>Susisiekite su mumis</a>, jei tai jūs. Taip pat žiūrėkite mūsų <a %(a_llm)s>LLM puslapį</a>. Premijos Mes visada ieškome žmonių, turinčių tvirtų programavimo ar puolamosios saugos įgūdžių, kurie norėtų prisidėti. Jūs galite reikšmingai prisidėti prie žmonijos palikimo išsaugojimo. Kaip padėką, už solidžius indėlius suteikiame narystę. Kaip didelę padėką, už ypač svarbias ir sudėtingas užduotis suteikiame pinigines premijas. Tai neturėtų būti laikoma darbo pakaitalu, tačiau tai yra papildoma paskata ir gali padėti padengti patirtas išlaidas. Dauguma mūsų kodo yra atvirojo kodo, ir mes prašysime, kad jūsų kodas taip pat būtų atvirojo kodo, kai skiriame premiją. Yra keletas išimčių, kurias galime aptarti individualiai. Premijos skiriamos pirmajam asmeniui, kuris užbaigia užduotį. Drąsiai komentuokite premijos bilietą, kad kiti žinotų, jog dirbate prie kažko, kad kiti galėtų palaukti arba susisiekti su jumis dėl bendradarbiavimo. Tačiau būkite pasiruošę, kad kiti vis tiek gali dirbti prie tos pačios užduoties ir bandyti jus aplenkti. Tačiau mes neskiriame premijų už prastą darbą. Jei dvi aukštos kokybės pateiktys bus pateiktos artimu metu (per dieną ar dvi), galime nuspręsti skirti premijas abiem, savo nuožiūra, pavyzdžiui, 100%% už pirmąją pateiktį ir 50%% už antrąją pateiktį (taigi iš viso 150%%). Dėl didesnių premijų (ypač duomenų rinkimo premijų) prašome susisiekti su mumis, kai užbaigsite ~5%% užduoties, ir būsite įsitikinę, kad jūsų metodas bus tinkamas visam tikslui pasiekti. Turėsite pasidalinti savo metodu su mumis, kad galėtume pateikti atsiliepimus. Taip pat, tokiu būdu galime nuspręsti, ką daryti, jei keli žmonės artėja prie premijos, pavyzdžiui, galbūt skirti ją keliems žmonėms, skatinti žmones bendradarbiauti ir pan. ĮSPĖJIMAS: didelės premijos užduotys yra <span %(bold)s>sudėtingos</span> — gali būti protinga pradėti nuo lengvesnių. Eikite į mūsų <a %(a_gitlab)s>Gitlab užduočių sąrašą</a> ir rūšiuokite pagal „Label priority“. Tai rodo apytikslę užduočių, kurios mums rūpi, tvarką. Užduotys be aiškių premijų vis tiek yra tinkamos narystei, ypač tos, kurios pažymėtos „Accepted“ ir „Anna’s favorite“. Galbūt norėsite pradėti nuo „Starter project“. Lengvas savanoriškas darbas Dabar taip pat turime sinchronizuotą Matrix kanalą adresu %(matrix)s. Jei turite kelias laisvas valandas, galite padėti įvairiais būdais. Būtinai prisijunkite prie <a %(a_telegram)s>savanorių pokalbio Telegram</a>. Kaip padėkos ženklą, paprastai suteikiame 6 mėnesius „Laimingo Bibliotekininko“ už pagrindinius pasiekimus, o daugiau už tęstinį savanorišką darbą. Visi pasiekimai reikalauja aukštos kokybės darbo — prastas darbas mums kenkia labiau nei padeda, todėl jį atmesime. Prašome <a %(a_contact)s>atsiųsti mums el. laišką</a>, kai pasieksite pasiekimą. %(links)s nuorodos arba ekrano nuotraukos apie įvykdytus prašymus. Patenkinti knygų (arba straipsnių ir kt.) užklausas Z-Library arba Library Genesis forumuose. Mes neturime savo knygų užklausų sistemos, bet mes veidrodžiuojame tas bibliotekas, todėl jų gerinimas taip pat gerina Annos Archyvą. Pasiekimas Užduotis Priklauso nuo užduoties. Mažos užduotys, paskelbtos mūsų <a %(a_telegram)s>savanorių pokalbyje Telegram</a>. Paprastai už narystę, kartais už mažas premijas. Mažos užduotys skelbiamos mūsų savanorių pokalbių grupėje. Būtinai palikite komentarą apie išspręstas problemas, kad kiti nedubliuotų jūsų darbo. %(links)s nuorodos į įrašus, kuriuos patobulinote. Galite naudoti <a %(a_list)s >atsitiktinių metadata problemų sąrašą</a> kaip pradinį tašką. Pagerinkite metaduomenis <a %(a_metadata)s>susiedami</a> su Open Library. Tai turėtų parodyti, kaip pranešate kam nors apie Annos Archyvą, ir kaip jie jums dėkoja. %(links)s nuorodos arba ekrano nuotraukos. Skleidžiame žinią apie Annos Archyvą. Pavyzdžiui, rekomenduodami knygas AA, pateikdami nuorodas į mūsų tinklaraščio įrašus arba tiesiog nukreipdami žmones į mūsų svetainę. Visiškai išversti kalbą (jei ji nebuvo beveik baigta). <a %(a_translate)s>Versti</a> svetainę. Nuoroda į redagavimo istoriją, rodanti, kad padarėte reikšmingus indėlius. Pagerinkite Annos Archyvo Wikipedia puslapį savo kalba. Įtraukite informaciją iš AA Wikipedia puslapio kitomis kalbomis, taip pat iš mūsų svetainės ir tinklaraščio. Pridėkite nuorodas į AA kituose susijusiuose puslapiuose. Savanorystė ir atlygis 