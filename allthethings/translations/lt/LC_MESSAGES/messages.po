msgid "layout.index.invalid_request"
msgstr "Negaliojanti užklausa. Apsilankykite %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Skolinimo Biblioteka"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " ir "

msgid "layout.index.header.tagline_and_more"
msgstr "ir ne tik"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Mes atspindime %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Mes renkame ir viešai patei<PERSON>ame %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Visas mūsų kodas ir duomenys yra visiškai atviro kodo."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Didžiausia iš tiesų atvira biblioteka žmonijos istorijoje."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;knygų, %(paper_count)s&nbsp;straipsnių — amžinai išsaugoti."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Didžiausia pasaulyje atvirųjų duomenų biblioteka. ⭐️&nbsp;Atspindi Sci-Hub, Library Genesis, Z-Library ir ne tik. 📈&nbsp;%(book_any)s knygos, %(journal_article)s straipsniai, %(book_comic)s komiksai, %(magazine)s žurnalai — amžinai išsaugoti."

msgid "layout.index.header.tagline_short"
msgstr "📚 Didžiausia pasaulyje atvirųjų duomenų biblioteka.<br>⭐️ Atspindi Scihub, Libgen, Zlib ir ne tik."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Neteisingi metaduomenys (pvz., pavadinimas, aprašas, viršelio vaizdas)"

msgid "common.md5_report_type_mapping.download"
msgstr "Atsisiuntimo problemos (pvz., negalima prisijungti, klaidos pranešimas, labai lėtas)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Dokumentas neatidaromas (pvz., sugadintas dokumentas, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Prasta kokybė (pvz., formatavimo problemos, prasta skenavimo kokybė, trūkstami puslapiai)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Šlamštas / failas turėtų būti ištrintas (pvz., reklama, piktnaudžiavimo turinys)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Autorių teisių pretenzija"

msgid "common.md5_report_type_mapping.other"
msgstr "Kita"

msgid "common.membership.tier_name.bonus"
msgstr "Papildomi atsisiuntimai"

msgid "common.membership.tier_name.2"
msgstr "Nepriekaištingas knygėdis"

msgid "common.membership.tier_name.3"
msgstr "Stebuklingas bibliotekininkas"

msgid "common.membership.tier_name.4"
msgstr "Akinantis duomenų rinkėjas"

msgid "common.membership.tier_name.5"
msgstr "Garbingas archyvininkas"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s viso"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) viso"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s premija)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "neapmokėtas"

msgid "common.donation.order_processing_status_labels.1"
msgstr "apmokėtas"

msgid "common.donation.order_processing_status_labels.2"
msgstr "atšauktas"

msgid "common.donation.order_processing_status_labels.3"
msgstr "pasibaigęs"

msgid "common.donation.order_processing_status_labels.4"
msgstr "laukiama Anna dėl patvirtinimo"

msgid "common.donation.order_processing_status_labels.5"
msgstr "negalioja"

msgid "page.donate.title"
msgstr "Aukoti"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Jūs turite <a %(a_donation)s>esamą auką</a>, kuri yra vykdoma. Prašome užbaigti arba atšaukti tą auką prieš sukuriant naują auką."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Peržiūrėti visas mano aukas</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archive yra pelno nesiekiantis, atviro kodo, atvirų duomenų projektas. Aukojant ir tampant nauju nariu, palaikote mūsų veiksmus ir kūrimą. Visiems mūsų nariams: ačiū, kad palaikote mus! ❤️"

msgid "page.donate.header.text2"
msgstr "Daugiau informacijos rasite <a %(a_donate)s>Aukojimo DUK</a>."

msgid "page.donate.refer.text1"
msgstr "Norėdami gauti dar daugiau atsisiuntimų, <a %(a_refer)s>rekomenduokite savo draugams</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Jūs gaunate %(percentage)s%% papildomų atsisiuntimų, nes buvote pakviestas vartotojo %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Tai taikoma visą narystės laikotarpį."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s greitųjų atsisiuntimų per dieną"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "jei paaukosite šį mėnesį!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mėn."

msgid "page.donate.buttons.join"
msgstr "Prisijungti"

msgid "page.donate.buttons.selected"
msgstr "Pasirinktas"

msgid "page.donate.buttons.up_to_discounts"
msgstr "iki %(percentage)s%% nuolaidos"

msgid "page.donate.perks.scidb"
msgstr "SciDB straipsniai <strong>neriboti</strong> be patvirtinimo"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> prieiga"

msgid "page.donate.perks.refer"
msgstr "Uždirbkite <strong>%(percentage)s%% papildomų atsisiuntimų</strong> <a %(a_refer)s>rekomenduodami draugams</a>."

msgid "page.donate.perks.credits"
msgstr "Jūsų vartotojo vardo arba anoniminis paminėjimas"

msgid "page.donate.perks.previous_plus"
msgstr "Praėjusios privilegijos, plius:"

msgid "page.donate.perks.early_access"
msgstr "Ankstyva prieiga prie naujų funkcijų"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Prieiga prie uždaro Telegram su užkulisių atnaujinimais"

msgid "page.donate.perks.adopt"
msgstr "\"Priglobti torrentą\": Jūsų vartotojo vardas arba pranešimas torrento failo pavadinime <div %(div_months)s>kas 12 mėnesių narystės</div>"

msgid "page.donate.perks.legendary"
msgstr "Legendinis žmonijos žinių ir kultūros išsaugojimo statusas"

msgid "page.donate.expert.title"
msgstr "Ekspertų prieiga"

msgid "page.donate.expert.contact_us"
msgstr "susisiekti su mumis"

msgid "page.donate.small_team"
msgstr "Mes esame maža savanorių komanda. Jums atsakyti gali užtrukti 1-2 savaites."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Neribota</strong> sparčioji prieiga"

msgid "page.donate.expert.direct_sftp"
msgstr "Tiesioginiai <strong>SFTP</strong> serveriai"

msgid "page.donate.expert.enterprise_donation"
msgstr "Įmonės lygio auka arba mainai į naujas kolekcijas (pvz., nauji nuskenavimai, duomenų rinkiniai su teksto atpažinimu)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Mes priimame dideles aukas iš turtingų asmenų ar institucijų. "

msgid "page.donate.header.large_donations"
msgstr "Aukoms virš 5000$ susisiekite tiesiogiai %(email)s."

msgid "page.donate.header.recurring"
msgstr "Atkreipkite dėmesį, kad nors narystės šiame puslapyje yra „mėnesinės“, jos yra vienkartinės aukos (nepasikartojančios). Žr. <a %(faq)s>Aukojimo DUK</a>."

msgid "page.donate.without_membership"
msgstr "Jei norėtumėte paaukoti (bet kokią sumą) be narystės, galite naudoti šį Monero (XMR) adresą: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Pasirinkite mokėjimo būdą."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(laikinai nepasiekiama)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s dovanų kortelė"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Banko kortelė (naudojant programėlę)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kriptovaliutos %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredito/debeto kortelė"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (reguliarus)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Kortelė / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Kredito/debeto kortelė/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazilija)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Banko kortelė"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredito/debeto kortelė (atsarginė)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredito/debeto kortelė 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Galite aukoti su šiomis kriptovaliutomis: BTC, ETH, XMR ir SOL. Naudokite šį pasirinkimą jeigu jau esate susipažinę su kriptovaliutomis."

msgid "page.donate.payment.desc.crypto2"
msgstr "Su kriptovaliuta galite paaukoti naudodami BTC, ETH, XMR ir kt."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Jei pirmą kartą naudojate kriptovaliutą, siūlome naudoti %(options)s nusipirkti ir paaukoti Bitkoinus (originalią ir plačiausiai naudojamą kriptovaliutą)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Norint aukoti naudojant PayPal, mes naudosime PayPal Crypto. Tai leidžia mums išlikti anoniminiais. Dėkojame, kad skyrėte laiko ir išmokote aukoti šiuo metodu, nes jis mums labai padeda."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Aukokite per PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Aukokite per Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Jei naudojatės Cash App, tai yra lengviausias būdas aukoti!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Atkreipkite dėmesį, kad už sandorius, smulkesnius nei %(amount)s, Cash App gali imti %(fee)s mokestį. Už %(amount)s ir duagiau, sandoris nemokamas!"

msgid "page.donate.payment.desc.revolut"
msgstr "Aukoti naudojant Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Jei turite Revolut, tai yra lengviausias būdas aukoti!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Aukokite naudadamiesi kredito ar debito kortele."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay ir Apple Pay taip pat gali veikti."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Atkreipkite dėmesį, kad aukojant smulkias aukas kredito kortelės mokestis gali panaikinti Jūsų %(discount)s nuolaidą, tad rekomenduojame ilgiau trunkančias prenumeratas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Atkreipkite dėmesį, kad aukojant smulkias aukas taikomi dideli mokesčiai, tad rekomenduojame ilgiau trunkančias prenumeratas."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Naudojant Binance, galite nusipirkti Bitcoin su kredito/debeto kortele arba banko sąskaita, o tada paaukoti tą Bitcoin mums. Tokiu būdu galime išlikti saugūs ir anonimiški priimdami jūsų auką."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance yra prieinamas beveik visose šalyse ir palaiko daugumą bankų bei kredito/debeto kortelių. Tai šiuo metu yra mūsų pagrindinė rekomendacija. Vertiname, kad skiriate laiko išmokti aukoti šiuo metodu, nes tai mums labai padeda."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Aukoti naudojant savo įprastą PayPal paskyrą."

msgid "page.donate.payment.desc.givebutter"
msgstr "Aukoti naudojant kredito/debeto kortelę, PayPal arba Venmo. Pasirinksite kitame puslapyje."

msgid "page.donate.payment.desc.amazon"
msgstr "Aukokite naudodamiesi Amazon dovanų kortele."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Atkreipkite dėmesį, kad turime suapvalinti iki mūsų perpardavėjų priimamų sumų (mažiausiai %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>SVARBU:</strong> Mes palaikome tik Amazon.com, kitų Amazon svetainių, pvz., .de, .co.uk, .ca, NEPALAIKOME."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>SVARBU:</strong> Ši parinktis skirta %(amazon)s. Jei norite naudoti kitą Amazon svetainę, pasirinkite ją aukščiau."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Šis metodas naudoja kriptovaliutų tiekėją kaip tarpinį konversijos būdą. Tai gali būti šiek tiek painu, todėl naudokite šį metodą tik tada, jei kiti mokėjimo būdai neveikia. Taip pat, jis neveikia visose šalyse."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Aukoti naudojant kreditinę/debetinę kortelę per Alipay programėlę (labai lengva)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Įdiegti Alipay programėlę"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Įdiekite Alipay programėlę iš <a %(a_app_store)s>Apple App Store</a> arba <a %(a_play_store)s>Google Play parduotuvės</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registruotis naudojant savo telefono numerį."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Jokių papildomų asmeninių duomenų nereikia."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Pridėti banko kortelę"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Palaikoma: Visa, MasterCard, JCB, Diners Club ir Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Daugiau informacijos rasite <a %(a_alipay)s>šiame vadove</a>."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Negalime tiesiogiai priimti kredito/debeto kortelių, nes bankai nenori su mumis dirbti. ☹ Tačiau yra keletas būdų, kaip naudoti kredito/debeto korteles, naudojant kitus mokėjimo metodus:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon dovanų kortelė"

msgid "page.donate.ccexp.amazon_com"
msgstr "Siųskite mums Amazon.com dovanų korteles naudodami savo kreditinę/debetinę kortelę."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay palaiko tarptautines kredito/debeto korteles. Daugiau informacijos rasite <a %(a_alipay)s>šiame vadove</a>."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) palaiko tarptautines kreditines/debetines korteles. WeChat programėlėje eikite į „Me => Services => Wallet => Add a Card“. Jei to nematote, įjunkite naudodami „Me => Settings => General => Tools => Weixin Pay => Enable“."

msgid "page.donate.ccexp.crypto"
msgstr "Galite pirkti kriptovaliutą naudodami kreditines/debetines korteles."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Kriptovaliutos greitosios paslaugos"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Greitosios paslaugos yra patogios, bet ima didesnius mokesčius."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Galite naudoti tai vietoj kripto biržos, jei norite greitai padaryti didesnę auką ir nesijaudinate dėl $5-10 mokesčio."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Būtinai siųskite tikslų kriptovaliutos kiekį, nurodytą aukojimo puslapyje, o ne sumą $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Kitu atveju mokestis bus atimtas ir mes negalėsime automatiškai apdoroti jūsų narystės."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimalus: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimalus: %(minimum)s priklausomai nuo šalies, pirmam mokėjimui nereikia patvirtinimo)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimalus: %(minimum)s, pirmam mokėjimui nereikia patvirtinimo)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimalus: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimalus: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimalus: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Jei kuri nors iš šios informacijos yra pasenusi, prašome pranešti mums el. paštu."

msgid "page.donate.payment.desc.bmc"
msgstr "Kreditinėms kortelėms, debetinėms kortelėms, Apple Pay ir Google Pay mes naudojame „Buy Me a Coffee“ (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Jų sistemoje viena „kava“ yra lygi 5 USD, todėl jūsų auka bus suapvalinta iki artimiausio 5 kartotinio."

msgid "page.donate.duration.intro"
msgstr "Pasirinkite kokiam laikotarpiui norite prenumeruoti."

msgid "page.donate.duration.1_mo"
msgstr "1 mėnesis"

msgid "page.donate.duration.3_mo"
msgstr "3 mėnesiai"

msgid "page.donate.duration.6_mo"
msgstr "6 mėnesiai"

msgid "page.donate.duration.12_mo"
msgstr "12 mėnesių"

msgid "page.donate.duration.24_mo"
msgstr "24 mėnesiai"

msgid "page.donate.duration.48_mo"
msgstr "48 mėnesiai"

msgid "page.donate.duration.96_mo"
msgstr "96 mėnesiai"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>po <span %(span_discount)s></span> nuolaidos:</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Šis mokėjimo būdas reikalauja mažiausiai %(amount)s. Prašome pasirinkti kitą trukmę arba mokėjimo būdą."

msgid "page.donate.buttons.donate"
msgstr "Paaukoti"

msgid "page.donate.payment.maximum_method"
msgstr "Šis mokėjimo metodas leidžia daugiausiai %(amount)s. Prašome pasirinkti kitą trukmę arba mokėjimo metodą."

msgid "page.donate.login2"
msgstr "Norėdami tapti nariu, prašome <a %(a_login)s>Prisijungti arba Registruotis</a>. Ačiū už jūsų palaikymą!"

msgid "page.donate.payment.crypto_select"
msgstr "Pasirinkite norimą kriptovaliutą:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(mažiausia minimali suma)"

msgid "page.donate.coinbase_eth"
msgstr "(naudoti siunčiant Ethereum iš Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(įspėjimas: didelė minimali suma)"

msgid "page.donate.submit.confirm"
msgstr "Paspauskite mygtuką aukoti, kad patvirtinti šią auką."

msgid "page.donate.submit.button"
msgstr "Aukoti <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Jūs vis dar galite atšaukti mokėjimo metu."

msgid "page.donate.submit.success"
msgstr "✅ Persiunčiame į aukojimo puslapį…"

msgid "page.donate.submit.failure"
msgstr "❌ Kažkas atsitiko negerai. Prašome perkrauti puslapį ir bandykite dar kartą."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mėnesį"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "1 mėnesiui"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "3 mėnesiams"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "6 mėnesiams"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "12 mėnesių laikotarpiui"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "24 mėnesiams"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "48 mėnesiams"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "96 mėnesiams"

msgid "page.donate.submit.button.label.1_mo"
msgstr "1 mėnesiui “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "3 mėnesiams “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "6 mėnesiams “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "12 mėnesių “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "24 mėnesiams “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "48 mėnesiams “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "96 mėnesiams “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Auka"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Iš viso: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mėnesį %(duration)s mėnesius, įskaitant %(discounts)s%% nuolaidą)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Iš viso: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mėnesį %(duration)s mėnesių)</span>"

msgid "page.donation.header.status"
msgstr "Būsena: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identifikatorius: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Atšaukti"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Ar jūs esate tikras, kad norite atšaukti? Neatšaukite jeigu jau esate sumokėję."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Taip, prašau atšaukti"

msgid "page.donation.header.cancel.success"
msgstr "✅ Jūsų auka buvo atšaukta."

msgid "page.donation.header.cancel.new_donation"
msgstr "Atlikti naują auką"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Kažkas negerai atsitiko. Prašome perkrauti puslapį ir bandykite dar kartą."

msgid "page.donation.header.reorder"
msgstr "Užsakyti iš naujo"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Jūs jau apmokėjote. Jeigu vis vien norite peržiūrėti mokėjimo instrukcijas, spauskite čia:"

msgid "page.donation.old_instructions.show_button"
msgstr "Rodyti senas mokėjimo instrukcijas"

msgid "page.donation.thank_you_donation"
msgstr "Ačiū už jūsų auką!"

msgid "page.donation.thank_you.secret_key"
msgstr "Jei to dar nepadarėte, užsirašykite savo slaptą raktą prisijungimui:"

msgid "page.donation.thank_you.locked_out"
msgstr "Kitu atveju galite nebegalėti prisijungti prie šios paskyros!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Mokėjimo instrukcijos yra pasenusios. Jeigu norėtumėte atlikti naują auką, naudokite “Užsakyti iš naujo” mygtuką aukščiau."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Svarbu:</strong> Kriptovaliutų kainos gali labai greitai keistis, kartais net po 20%% per kelias minutes. Tai vis tiek yra mažiau negu mokesčiai kuriuos patiriame su daugybe mokėjimo tiekėjų, kurie dažnai prašo 50-60%% už darbą su “šešėlinę labdara” kaip mumis. <u>Jeigu atsiųsite čekį su originalia kainą kurią mokėjote, mes vis dar suteiksime Jūsų paskyrai pasirinktą narystę</u> (jeigu čekis nėra senesnis negu kelios valandos). Labai vertiname, kad esate pasirengę taikstytis su tokiais dalykais, kad mus paremtumėte. ❤️"

msgid "page.donation.expired"
msgstr "Ši auka pasibaigė. Prašome atšaukti ir sukurti naują."

msgid "page.donation.payment.crypto.top_header"
msgstr "Kriptovaliutos instrukcijos"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Perduokite į vieną iš mūsų kripto paskyrų"

msgid "page.donation.payment.crypto.text1"
msgstr "Paukokite %(total)s sumą į vieną iš šių adresų:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Pirkti Bitcoin Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "„PayPal“ programoje arba svetainėje raskite puslapį „Crypto“. Paprastai tai yra skiltyje „Finansai“."

msgid "page.donation.payment.paypal.text3"
msgstr "Sekite instrukcijas nusipirkti Bitcoin (BTC). Jums tereikia nusipirkti sumą, kurią norite paaukoti, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Perduokite Bitcoin į mūsų adresą"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Eikite į “Bitcoin” puslapį PayPal programėlėje arba svetainėje. Nuspauskite “Perduoti” mygtuką %(transfer_icon)s, ir tada paspauskite “Siųsti”."

msgid "page.donation.payment.paypal.text5"
msgstr "Įveskite mūsų Bitcoin (BTC) adresą kaip gavėją ir vadovaukitės instrukcijomis, kad išsiųstumėte %(total)s auką:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredito / debeto kortelės instrukcijos"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Aukoti per mūsų kreditinės / debetinės kortelės puslapį"

msgid "page.donation.donate_on_this_page"
msgstr "Aukoti %(amount)s <a %(a_page)s>šiame puslapyje</a>."

msgid "page.donation.stepbystep_below"
msgstr "Žiūrėkite „žingsnis po žingsnio“ vadovą žemiau."

msgid "page.donation.status_header"
msgstr "Būsena:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Laukiama patvirtinimo (atnaujinkite puslapį, kad patikrintumėte)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Laukiama mokėjimo (atnaujinkite puslapį, kad patikrintumėte)…"

msgid "page.donation.time_left_header"
msgstr "Likęs laikas:"

msgid "page.donation.might_want_to_cancel"
msgstr "(galite atšaukti ir sukurti naują auką)"

msgid "page.donation.reset_timer"
msgstr "Norėdami atstatyti laikmatį, tiesiog sukurkite naują auką."

msgid "page.donation.refresh_status"
msgstr "Atnaujinti būseną"

msgid "page.donation.footer.issues_contact"
msgstr "Jei susidūrėte su kokiomis nors problemomis, prašome susisiekti su mumis adresu %(email)s ir pateikti kuo daugiau informacijos (pvz., ekrano nuotraukas)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Jei jau sumokėjote:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Kartais patvirtinimas gali užtrukti iki 24 valandų, todėl būtinai atnaujinkite šį puslapį (net jei jis pasibaigęs)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Pirkti PYUSD monetą per PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Sekite instrukcijas, kaip įsigyti PYUSD monetą (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei aukojamą sumą (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Eikite į „PYUSD“ puslapį savo PayPal programėlėje arba svetainėje. Paspauskite mygtuką „Transfer“ %(icon)s, tada „Send“."

msgid "page.donation.transfer_amount_to"
msgstr "Perkelti %(amount)s į %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Pirkti Bitcoin (BTC) per Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Eikite į „Bitcoin“ (BTC) puslapį Cash App programėlėje."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei suma, kurią aukojate (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite sau."

msgid "page.donation.cash_app_btc.step2"
msgstr "Perveskite Bitcoin į mūsų adresą"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Paspauskite „Send bitcoin“ mygtuką, kad atliktumėte „išėmimą“. Pereikite nuo dolerių prie BTC paspausdami %(icon)s ikoną. Įveskite BTC sumą žemiau ir paspauskite „Send“. Jei susiduriate su sunkumais, žiūrėkite <a %(help_video)s>šį vaizdo įrašą</a>."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Mažoms aukoms (mažiau nei $25) gali prireikti naudoti Rush arba Priority."

msgid "page.donation.revolut.step1"
msgstr "Pirkite Bitcoin (BTC) per Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Eikite į „Crypto“ puslapį Revolut programėlėje, kad nusipirktumėte Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Pirkite šiek tiek daugiau (rekomenduojame %(more)s daugiau) nei suma, kurią aukojate (%(amount)s), kad padengtumėte operacijos mokesčius. Likusią sumą pasiliksite sau."

msgid "page.donation.revolut.step2"
msgstr "Perveskite Bitcoin į mūsų adresą"

msgid "page.donation.revolut.step2.transfer"
msgstr "Paspauskite „Send bitcoin“ mygtuką, kad atliktumėte „išėmimą“. Pereikite nuo eurų prie BTC paspausdami %(icon)s ikoną. Įveskite BTC sumą žemiau ir paspauskite „Send“. Jei susiduriate su sunkumais, žiūrėkite <a %(help_video)s>šį vaizdo įrašą</a>."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Būtinai naudokite žemiau nurodytą BTC sumą, <em>NE</em> eurus ar dolerius, kitaip mes negausime teisingos sumos ir negalėsime automatiškai patvirtinti jūsų narystės."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Mažoms aukoms (mažiau nei $25) gali prireikti naudoti Rush arba Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Naudokite bet kurią iš šių „kredito kortelė į Bitcoin“ greitųjų paslaugų, kurios užtrunka tik kelias minutes:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Užpildykite šią formą:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin suma:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Prašome naudoti šį <span %(underline)s>tikslų kiekį</span>. Jūsų bendra kaina gali būti didesnė dėl kredito kortelės mokesčių. Deja, mažoms sumoms tai gali būti daugiau nei mūsų nuolaida."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adresas (išorinė piniginė):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instrukcijos"

msgid "page.donation.crypto_standard"
msgstr "Mes palaikome tik standartinę kriptovaliutų versiją, jokių egzotinių tinklų ar monetų versijų. Priklausomai nuo monetos, operacijos patvirtinimas gali užtrukti iki valandos."

msgid "page.donation.crypto_qr_code_title"
msgstr "Nuskaitykite QR kodą, kad sumokėtumėte"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Nuskaitykite šį QR kodą naudodami „Crypto Wallet“ programą, kad greitai užpildytumėte mokėjimo informaciją"

msgid "page.donation.amazon.header"
msgstr "Amazon dovanų kortelė"

msgid "page.donation.amazon.form_instructions"
msgstr "Prašome naudoti <a %(a_form)s>oficialią Amazon.com formą</a>, kad atsiųstumėte mums %(amount)s vertės dovanų kortelę į žemiau nurodytą el. pašto adresą."

msgid "page.donation.amazon.only_official"
msgstr "Negalime priimti kitų dovanų kortelių metodų, <strong>tiktais siunčiamus tiesiogiai iš oficialios formos Amazon.com</strong>. Negalėsime grąžinti jūsų dovanų kortelės, jei nenaudosite šios formos."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Įveskite tikslią sumą: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Prašome NERAŠYTI savo žinutės."

msgid "page.donation.amazon.form_to"
msgstr "„Kam“ gavėjo el. paštas formoje:"

msgid "page.donation.amazon.unique"
msgstr "Unikalus jūsų paskyrai, nesidalinkite."

msgid "page.donation.amazon.only_use_once"
msgstr "Naudoti tik vieną kartą."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Laukiama dovanų kortelės… (atnaujinkite puslapį, kad patikrintumėte)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Išsiuntus dovanų kortelę, mūsų automatinė sistema ją patvirtins per kelias minutes. Jei tai neveikia, pabandykite išsiųsti dovanų kortelę dar kartą (<a %(a_instr)s>instrukcijos</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Jei tai vis tiek neveikia, prašome parašyti mums el. laišką, ir Anna tai peržiūrės rankiniu būdu (tai gali užtrukti kelias dienas). Būtinai paminėkite, jei jau bandėte išsiųsti iš naujo."

msgid "page.donation.amazon.example"
msgstr "Pavyzdys:"

msgid "page.donate.strange_account"
msgstr "Svarbu žinoti, jog paskyros vardas arba nuotrauka gali atrodyti keistai. Nereikia jaudintis! Šios paskyros yra valdomos mūsų aukojimo partnerių. Į mūsų paskyras nebuvo įsilaužta."

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay instrukcijos"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Aukoti Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Paaukokite visą sumą %(total)s naudodami <a %(a_account)s>šią Alipay paskyrą</a>"

msgid "page.donation.page_blocked"
msgstr "Jei aukojimo puslapis yra blokuojamas, pabandykite kitą interneto ryšį (pvz., VPN arba telefono internetą)."

msgid "page.donation.payment.alipay.error"
msgstr "Deja, Alipay puslapis dažnai pasiekiamas tik iš <strong>žemyninės Kinijos</strong>. Gali tekti laikinai išjungti savo VPN arba naudoti VPN į žemyninę Kiniją (kartais veikia ir Honkongas)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Padarykite auką (nuskaitykite QR kodą arba paspauskite mygtuką)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Atidarykite <a %(a_href)s>QR kodo aukojimo puslapį</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Nuskaitykite QR kodą su Alipay programėle arba paspauskite mygtuką, kad atidarytumėte Alipay programėlę."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Prašome būti kantriems; puslapis gali užtrukti, kol įsikraus, nes jis yra Kinijoje."

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat instrukcijos"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Paaukokite per WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Paaukokite visą sumą %(total)s naudodami <a %(a_account)s>šį WeChat paskyrą</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix instrukcijos"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Aukoti naudojant Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Paaukokite %(total)s sumą naudojant <a %(a_account)s>šią Pix paskyrą"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Parašykite mums čekį elektroniniu paštu"

msgid "page.donation.footer.verification"
msgstr "Siųskite kvitą arba ekrano nuotrauką į savo asmeninį patvirtinimo adresą. NENAUDOKITE šio el. pašto adreso savo PayPal aukai."

msgid "page.donation.footer.text1"
msgstr "Atsiųskite čeki arba ekrano nuotrauką į šį adresą:"

msgid "page.donation.footer.crypto_note"
msgstr "Jei operacijos metu kriptovaliutų kursas svyravo, būtinai pridėkite kvitą, kuriame nurodytas pradinis valiutos kursas. Labai dėkojame už tai, kad stengiatės naudoti kriptovaliutą, tai mums labai padeda!"

msgid "page.donation.footer.text2"
msgstr "Išsiuntę kvitą el. paštu, spustelėkite šį mygtuką, kad Anna galėtų jį peržiūrėti rankiniu būdu (tai gali užtrukti kelias dienas):"

msgid "page.donation.footer.button"
msgstr "Taip, išsiunčiau kvitą el. paštu"

msgid "page.donation.footer.success"
msgstr "✅ Ačiū už auką! Anna rankiniu būdu suaktyvins jūsų narystę per kelias dienas."

msgid "page.donation.footer.failure"
msgstr "❌ Kažkas atsitiko negerai. Perkraukite puslapį iš naujo ir bandykite dar kartą."

msgid "page.donation.stepbystep"
msgstr "Gidas"

msgid "page.donation.crypto_dont_worry"
msgstr "Kai kurie žingsniai mini kripto pinigines, bet nesijaudinkite, jums nereikės nieko mokytis apie kripto."

msgid "page.donation.hoodpay.step1"
msgstr "1. Įveskite savo el. pašto adresą."

msgid "page.donation.hoodpay.step2"
msgstr "2. Pasirinkite mokėjimo metodą."

msgid "page.donation.hoodpay.step3"
msgstr "3. Pasirinkite mokėjimo metodą dar kartą."

msgid "page.donation.hoodpay.step4"
msgstr "4. Pasirinkite „Savarankiškai talpinamą“ piniginę."

msgid "page.donation.hoodpay.step5"
msgstr "5. Spustelėkite „Patvirtinu nuosavybę“."

msgid "page.donation.hoodpay.step6"
msgstr "6. Turėtumėte gauti el. laišką su kvitu. Prašome jį atsiųsti mums, ir mes kuo greičiau patvirtinsime jūsų auką."

msgid "page.donate.wait_new"
msgstr "Prašome palaukti bent <span %(span_hours)s>24 valandas</span> (ir atnaujinti šį puslapį) prieš susisiekiant su mumis."

msgid "page.donate.mistake"
msgstr "Jei padarėte klaidą mokėjimo metu, mes negalime grąžinti pinigų, bet stengsimės išspręsti problemą."

msgid "page.my_donations.title"
msgstr "Mano aukos"

msgid "page.my_donations.not_shown"
msgstr "Aukų detalės nėra viešai rodomos."

msgid "page.my_donations.no_donations"
msgstr "Kol kas jokių aukų. <a %(a_donate)s>Atlikti mano pirmą auką.</a>"

msgid "page.my_donations.make_another"
msgstr "Atlikti dar vieną auką."

msgid "page.downloaded.title"
msgstr "Parsiusti failai"

msgid "page.downloaded.fast_partner_star"
msgstr "Atsisiuntimai iš Greitų Partnerių Serverių pažymėti %(icon)s."

msgid "page.downloaded.twice"
msgstr "Jei atsisiuntėte failą su greitu ir lėtu atsisiuntimu, jis bus rodomas du kartus."

msgid "page.downloaded.fast_download_time"
msgstr "Greiti atsisiuntimai per pastarąsias 24 valandas skaičiuojami į dienos limitą."

msgid "page.downloaded.times_utc"
msgstr "Visi laikai nurodyti UTC laiko juostoje."

msgid "page.downloaded.not_public"
msgstr "Parsiusti failai nėra viešai rodomi."

msgid "page.downloaded.no_files"
msgstr "Jokie failai dar nebuvo parsiųsti."

msgid "page.downloaded.last_18_hours"
msgstr "Paskutinės 18 valandų"

msgid "page.downloaded.earlier"
msgstr "Ankstesni"

msgid "page.account.logged_in.title"
msgstr "Paskyra"

msgid "page.account.logged_out.title"
msgstr "Prisijungti / Registruotis"

msgid "page.account.logged_in.account_id"
msgstr "Paskyros ID: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Viešas profilis: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Slaptas raktas (nesidalinkite!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "rodyti"

msgid "page.account.logged_in.membership_has_some"
msgstr "Narystė: <strong>%(tier_name)s</strong> iki %(until_date)s <a %(a_extend)s>(pratęsti)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Narystė: <strong>Jokios</strong> <a %(a_become)s>(tapti nariu)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Greiti atsisiuntimai (per pastarąsias 24 valandas): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "kurie atsisiuntimai?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Išskirtinė Telegram grupė: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Prisijunkite prie mūsų čia!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Pasikelkite į <a %(a_tier)s>aukštesnį lygį</a>, kad prisijungtumėte prie mūsų grupės."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Susisiekite su Anna adresu %(email)s, jei norite pakelti savo narystę į aukštesnį lygį."

msgid "page.contact.title"
msgstr "Kontaktinis el. paštas"

msgid "page.account.logged_in.membership_multiple"
msgstr "Galite sujungti kelias narystes (greiti atsisiuntimai per 24 valandas bus sumuojami)."

msgid "layout.index.header.nav.public_profile"
msgstr "Viešas profilis"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Parsiųsti failai"

msgid "layout.index.header.nav.my_donations"
msgstr "Mano aukos"

msgid "page.account.logged_in.logout.button"
msgstr "Atsijungti"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Dabar esate atsijungęs. Perkraukite puslapį, kad vėl prisijungtumėte."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Kažkas negerai atsitiko. Perkraukite puslapį ir bandykite dar kartą."

msgid "page.account.logged_out.registered.text1"
msgstr "Registracija sėkminga! Jūsų slaptas kodas yra: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Išsaugokite šitą kodą. Jeigu jį prarasite - neteksite prieigos prie paskyros."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Pažymėti.</strong> Galite pažymėti šį puslapį, kad gautumėte raktą.</li><li %(li_item)s><strong>Atsisiųsti.</strong> Spustelėkite < %(a_download)s>šią nuorodą</a>, kad atsisiųstumėte kodą.</li><li %(li_item)s><strong>Slaptažodžių tvarkyklė.</strong> Jūsų patogumui kodas yra iš anksto užpildytas aukščiau, todėl prisijungę galite išsaugoti slaptažodžių tvarkytuvėje.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Įveskite slaptą kodą norint prisijungti:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Slaptas kodas"

msgid "page.account.logged_out.key_form.button"
msgstr "Prisijungti"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Neteisingas slaptas kodas. Patikrinkite savo kodą ir bandykite dar kartą arba registruokitės naują paskyrą žemiau."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Nepraraskite savo kodo!"

msgid "page.account.logged_out.register.header"
msgstr "Dar neturite paskyros?"

msgid "page.account.logged_out.register.button"
msgstr "Sukurti naują paskyrą"

msgid "page.login.lost_key"
msgstr "Jei praradote savo kodą, prašome <a %(a_contact)s>susisiekti su mumis</a> ir pateikti kuo daugiau informacijos."

msgid "page.login.lost_key_contact"
msgstr "Gali tekti laikinai sukurti naują paskyrą, kad galėtumėte su mumis susisiekti."

msgid "page.account.logged_out.old_email.button"
msgstr "Sena paskyra su elektroniniu paštu? Įveskite jo adresą <a %(a_open)s>šiame puslapyje</a>."

msgid "page.list.title"
msgstr "Sąrašas"

msgid "page.list.header.edit.link"
msgstr "Redaguoti"

msgid "page.list.edit.button"
msgstr "Išsaugoti"

msgid "page.list.edit.success"
msgstr "✅ Išsaugota. Perkraukite puslapį."

msgid "page.list.edit.failure"
msgstr "❌ Kažkas negerai atsitiko. Prašome bandyti dar kartą."

msgid "page.list.by_and_date"
msgstr "Išrikiuoti pagal %(by)s, sukurta <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Sąrašas tuščias."

msgid "page.list.new_item"
msgstr "Pridėti arba pašalinti iš šio sąrašo randant failą ir atidarant “Sąrašai” skirtuką."

msgid "page.profile.title"
msgstr "Profilis"

msgid "page.profile.not_found"
msgstr "Profilis nerastas."

msgid "page.profile.header.edit"
msgstr "redaguoti"

msgid "page.profile.change_display_name.text"
msgstr "Pasikeisti rodomą vardą. Jūsų identifikatorius (dalis po “#”) negali būti pakeistas."

msgid "page.profile.change_display_name.button"
msgstr "Išsaugoti"

msgid "page.profile.change_display_name.success"
msgstr "✅ Išsaugota. Perkraukite puslapį."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Kažkas atsitiko negerai. Bandykite dar kartą."

msgid "page.profile.created_time"
msgstr "Profilis sukurtas <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Sąrašai"

msgid "page.profile.lists.no_lists"
msgstr "Kol kas jokių sąrašų"

msgid "page.profile.lists.new_list"
msgstr "Sukurkite naują sąrašą randant failą ir atidarant skirtuką “Sąrašai”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Autorių teisių reforma yra būtina nacionaliniam saugumui"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Kinijos LLM (įskaitant DeepSeek) yra mokomi iš mano nelegalios knygų ir straipsnių archyvo — didžiausio pasaulyje. Vakarai turi pertvarkyti autorių teisių įstatymus kaip nacionalinio saugumo klausimą."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "lydinčios TorrentFreak straipsniai: <a %(torrentfreak)s>pirmas</a>, <a %(torrentfreak_2)s>antras</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Ne taip seniai „šešėlinės bibliotekos“ buvo nykstančios. Sci-Hub, didžiulė nelegali akademinių straipsnių archyvas, nustojo priimti naujus darbus dėl teisminių procesų. „Z-Library“, didžiausia nelegali knygų biblioteka, matė, kaip jos tariami kūrėjai buvo suimti dėl baudžiamųjų autorių teisių pažeidimų. Jie neįtikėtinai sugebėjo išvengti arešto, tačiau jų biblioteka vis dar yra grėsmėje."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Kai Z-Library susidūrė su uždarymu, aš jau buvau atsarginę kopiją padaręs visai jos bibliotekai ir ieškojau platformos, kurioje ją patalpinti. Tai buvo mano motyvacija pradėti Anos Archyvą: tęsti misiją, kurią pradėjo ankstesnės iniciatyvos. Nuo tada mes išaugome iki didžiausios šešėlinės bibliotekos pasaulyje, talpinančios daugiau nei 140 milijonų autorių teisių saugomų tekstų įvairiais formatais — knygas, akademinius straipsnius, žurnalus, laikraščius ir kt."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mano komanda ir aš esame ideologai. Mes tikime, kad šių failų išsaugojimas ir talpinimas yra moraliai teisingas. Bibliotekos visame pasaulyje susiduria su finansavimo mažinimu, ir mes negalime pasitikėti žmonijos paveldu korporacijoms."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Tada atėjo dirbtinis intelektas. Beveik visos pagrindinės įmonės, kuriančios LLM, susisiekė su mumis, kad galėtų mokytis iš mūsų duomenų. Dauguma (bet ne visos!) JAV įmonės persvarstė savo poziciją, kai suprato mūsų darbo neteisėtumą. Priešingai, Kinijos įmonės entuziastingai priėmė mūsų kolekciją, akivaizdžiai nesijaudindamos dėl jos teisėtumo. Tai yra pastebima, atsižvelgiant į tai, kad Kinija yra beveik visų pagrindinių tarptautinių autorių teisių sutarčių signatarė."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Mes suteikėme didelės spartos prieigą apie 30 įmonių. Dauguma jų yra LLM įmonės, o kai kurios yra duomenų brokeriai, kurie perparduos mūsų kolekciją. Dauguma yra iš Kinijos, tačiau mes taip pat dirbome su įmonėmis iš JAV, Europos, Rusijos, Pietų Korėjos ir Japonijos. DeepSeek <a %(arxiv)s>pripažino</a>, kad ankstesnė versija buvo mokoma iš dalies mūsų kolekcijos, nors jie tyliai kalba apie savo naujausią modelį (tikriausiai taip pat mokytą iš mūsų duomenų)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Jei Vakarai nori išlikti priekyje LLM lenktynėse ir galiausiai AGI, jie turi persvarstyti savo poziciją dėl autorių teisių, ir greitai. Nesvarbu, ar sutinkate su mumis dėl mūsų moralinio atvejo, tai dabar tampa ekonomikos ir net nacionalinio saugumo klausimu. Visos galios blokai kuria dirbtinius supermokslininkus, superhakerius ir superkarinius. Informacijos laisvė tampa išlikimo klausimu šioms šalims — net nacionalinio saugumo klausimu."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Mūsų komanda yra iš viso pasaulio, ir mes neturime konkrečios krypties. Tačiau mes skatintume šalis, turinčias stiprias autorių teisių įstatymus, naudoti šią egzistencinę grėsmę jiems reformuoti. Taigi, ką daryti?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Mūsų pirmasis rekomendacija yra paprasta: sutrumpinti autorių teisių terminą. JAV autorių teisės suteikiamos 70 metų po autoriaus mirties. Tai absurdiška. Mes galime tai suderinti su patentais, kurie suteikiami 20 metų po pateikimo. Tai turėtų būti daugiau nei pakankamai laiko knygų, straipsnių, muzikos, meno ir kitų kūrybinių darbų autoriams, kad jie būtų visiškai kompensuoti už savo pastangas (įskaitant ilgalaikius projektus, tokius kaip filmų adaptacijos)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Tada, bent jau, politikos formuotojai turėtų įtraukti išimtis masinio tekstų išsaugojimo ir platinimo atvejams. Jei pagrindinis rūpestis yra prarastos pajamos iš individualių klientų, asmeninio lygio platinimas galėtų likti draudžiamas. Savo ruožtu, tie, kurie gali valdyti didžiules saugyklas — įmonės, mokančios LLM, kartu su bibliotekomis ir kitais archyvais — būtų padengti šiomis išimtimis."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Kai kurios šalys jau daro šios versijos. TorrentFreak <a %(torrentfreak)s>pranešė</a>, kad Kinija ir Japonija įtraukė AI išimtis į savo autorių teisių įstatymus. Mums neaišku, kaip tai sąveikauja su tarptautinėmis sutartimis, bet tai tikrai suteikia apsaugą jų vidaus įmonėms, kas paaiškina, ką mes matėme."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Kalbant apie Anos Archyvą — mes tęsime savo pogrindinį darbą, pagrįstą moraliniu įsitikinimu. Tačiau mūsų didžiausias noras yra išeiti į šviesą ir teisėtai padidinti savo poveikį. Prašome reformuoti autorių teises."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Skaitykite papildomus straipsnius iš TorrentFreak: <a %(torrentfreak)s>pirmas</a>, <a %(torrentfreak_2)s>antras</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "$10,000 ISBN vizualizacijos premijos laimėtojai"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Gavome neįtikėtinų pateikimų $10,000 ISBN vizualizacijos premijai."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Prieš kelis mėnesius paskelbėme <a %(all_isbns)s>$10,000 premiją</a> už geriausią mūsų duomenų vizualizaciją, parodančią ISBN erdvę. Pabrėžėme, kurios bylos jau esame/nesame archyvavę, ir vėliau duomenų rinkinį, apibūdinantį, kiek bibliotekų turi ISBN (retumo matas)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Mus esame užplūdę atsakymai. Buvo tiek daug kūrybiškumo. Didelis ačiū visiems, kurie dalyvavo: jūsų energija ir entuziazmas yra užkrečiantys!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Galiausiai norėjome atsakyti į šiuos klausimus: <strong>kokios knygos egzistuoja pasaulyje, kiek jų jau esame archyvavę ir į kurias knygas turėtume sutelkti dėmesį toliau?</strong> Džiugu matyti, kad tiek daug žmonių rūpinasi šiais klausimais."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Mes patys pradėjome nuo paprastos vizualizacijos. Mažiau nei 300 kb, ši nuotrauka glaustai atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Visi ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Failai Anos Archyve"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNO"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC duomenų nutekėjimas"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost eBook indeksas"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Knygos"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internetinis Archyvas"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Pasaulinis leidėjų registras"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Rusijos valstybinė biblioteka"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Trantoro imperatoriškoji biblioteka"

#, fuzzy
msgid "common.back"
msgstr "Atgal"

#, fuzzy
msgid "common.forward"
msgstr "Pirmyn"

#, fuzzy
msgid "common.last"
msgstr "Paskutinis"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Daugiau informacijos rasite <a %(all_isbns)s>originaliame tinklaraščio įraše</a>."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Mes iškėlėme iššūkį tai pagerinti. Pirmąją vietą apdovanotume 6 000 USD, antrąją vietą – 3 000 USD, o trečiąją vietą – 1 000 USD. Dėl didžiulio atsako ir neįtikėtinų pateikimų nusprendėme šiek tiek padidinti prizų fondą ir apdovanoti keturias trečiąsias vietas po 500 USD kiekvienai. Nugalėtojai yra žemiau, bet būtinai peržiūrėkite visus pateikimus <a %(annas_archive)s>čia</a> arba atsisiųskite mūsų <a %(a_2025_01_isbn_visualization_files)s>bendrą torrentą</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Pirmoji vieta 6 000 USD: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Šis <a %(phiresky_github)s>pateikimas</a> (<a %(annas_archive_note_2951)s>Gitlab komentaras</a>) yra tiesiog viskas, ko norėjome, ir dar daugiau! Ypač patiko neįtikėtinai lankstūs vizualizacijos pasirinkimai (netgi palaikantys pasirinktinius šeiderius), tačiau su išsamiu išankstinių nustatymų sąrašu. Taip pat patiko, kaip viskas greitai ir sklandžiai veikia, paprasta įgyvendinimo struktūra (neturinti net serverio), sumanus minimapas ir išsamus paaiškinimas jų <a %(phiresky_github)s>tinklaraščio įraše</a>. Neįtikėtinas darbas ir pelnytai laimėta pirmoji vieta!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Antroji vieta 3 000 USD: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Kitas neįtikėtinas <a %(annas_archive_note_2913)s>pateikimas</a>. Ne toks lankstus kaip pirmoji vieta, bet iš tikrųjų mums labiau patiko jo makro lygio vizualizacija nei pirmosios vietos (erdvės užpildymo kreivė, ribos, žymėjimas, paryškinimas, slinkimas ir priartinimas). Joe Davis <a %(annas_archive_note_2971)s>komentaras</a> mus sužavėjo:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Nors tobuli kvadratai ir stačiakampiai yra matematiškai patrauklūs, jie nesuteikia pranašumo lokalumo kontekste. Manau, kad asimetrija, būdinga šioms Hilberto ar klasikinėms Mortono kreivėms, nėra trūkumas, o savybė. Kaip Italijos garsusis bato formos kontūras daro ją iš karto atpažįstamą žemėlapyje, taip ir šių kreivių unikalūs „keistenybės“ gali tarnauti kaip kognityviniai orientyrai. Šis išskirtinumas gali pagerinti erdvinę atmintį ir padėti vartotojams orientuotis, galbūt palengvinant konkrečių regionų suradimą ar šablonų pastebėjimą.“"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Ir vis dar daug galimybių vizualizavimui ir atvaizdavimui, taip pat neįtikėtinai sklandus ir intuityvus vartotojo sąsaja. Tvirta antroji vieta!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Trečioji vieta 500 $ #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Šiame <a %(annas_archive_note_2940)s>pateikime</a> mums labai patiko įvairūs vaizdai, ypač palyginimo ir leidėjo vaizdai."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Trečioji vieta 500 $ #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Nors tai nėra pati išbaigčiausia vartotojo sąsaja, šis <a %(annas_archive_note_2917)s>pateikimas</a> atitinka daugelį kriterijų. Ypač patiko jo palyginimo funkcija."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Trečioji vieta 500 $ #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Kaip ir pirmoji vieta, šis <a %(annas_archive_note_2975)s>pateikimas</a> mus sužavėjo savo lankstumu. Galiausiai tai yra tai, kas daro puikų vizualizavimo įrankį: maksimalus lankstumas galingiems vartotojams, išlaikant paprastumą vidutiniams vartotojams."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Trečioji vieta 500 $ #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Paskutinis <a %(annas_archive_note_2947)s>pateikimas</a>, gavęs premiją, yra gana paprastas, bet turi keletą unikalių savybių, kurios mums labai patiko. Mums patiko, kaip jie parodo, kiek duomenų rinkinių apima konkretų ISBN kaip populiarumo/patikimumo matą. Taip pat labai patiko paprastumas, bet efektyvumas naudojant skaidrumo slankiklį palyginimams."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Pažymėtinos idėjos"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Keletas daugiau idėjų ir įgyvendinimų, kurie mums ypač patiko:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Dangoraižiai retumui"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Tiesioginė statistika"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotacijos ir taip pat tiesioginė statistika"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unikalus žemėlapio vaizdas ir filtrai"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Šauni numatytoji spalvų schema ir šilumos žemėlapis."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Lengvas duomenų rinkinių perjungimas greitiems palyginimams."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Gražios etiketės."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skalės juosta su knygų skaičiumi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Daug slankiklių, skirtų lyginti datasets, tarsi būtumėte DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Galėtume tęsti dar ilgai, bet sustokime čia. Būtinai peržiūrėkite visas pateiktis <a %(annas_archive)s>čia</a> arba atsisiųskite mūsų <a %(a_2025_01_isbn_visualization_files)s>bendrą torrentą</a>. Tiek daug pateikčių, ir kiekviena suteikia unikalią perspektyvą, tiek UI, tiek įgyvendinimo srityje."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Mes bent jau įtrauksime pirmos vietos pateiktį į mūsų pagrindinę svetainę, o galbūt ir kitas. Taip pat pradėjome galvoti, kaip organizuoti procesą, identifikuojant, patvirtinant ir archyvuojant rečiausias knygas. Daugiau informacijos šiuo klausimu ateityje."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Dėkojame visiems dalyviams. Nuostabu, kad tiek daug žmonių rūpinasi."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Mūsų širdys pilnos dėkingumo."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Vizualizuojant visus ISBN — 10 000 $ premija iki 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Šis paveikslas atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Šis paveikslėlis yra 1000×800 pikselių. Kiekvienas pikselis atitinka 2 500 ISBN. Jei turime failą ISBN, tą pikselį padarome žalesnį. Jei žinome, kad ISBN buvo išduotas, bet neturime atitinkamo failo, padarome jį raudonesnį."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Mažiau nei 300 kb, šis paveikslėlis glaustai atspindi didžiausią visiškai atvirą „knygų sąrašą“, kada nors surinktą žmonijos istorijoje (keletas šimtų GB suspaustų pilnai)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Tai taip pat rodo: dar daug darbo liko atsarginėms knygoms (turime tik 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Fonas"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Kaip „Anos Archyvas“ gali pasiekti savo misiją atsargiai saugoti visą žmonijos žinias, nežinodamas, kurios knygos dar egzistuoja? Mums reikia TODO sąrašo. Vienas iš būdų tai išdėstyti yra per ISBN numerius, kurie nuo 1970-ųjų buvo priskirti kiekvienai išleistai knygai (daugumoje šalių)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nėra centrinės institucijos, kuri žinotų visus ISBN priskyrimus. Vietoj to, tai yra paskirstyta sistema, kurioje šalys gauna numerių diapazonus, kurie tada priskiriami didiesiems leidėjams, kurie gali toliau padalinti diapazonus mažesniems leidėjams. Galiausiai atskiri numeriai priskiriami knygoms."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Pradėjome žemėlapiuoti ISBN <a %(blog)s>prieš dvejus metus</a> su mūsų ISBNdb duomenų rinkimu. Nuo tada surinkome daug daugiau metadata šaltinių, tokių kaip <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ir daugiau. Visą sąrašą galima rasti „Datasets“ ir „Torrents“ puslapiuose „Anos Archyve“. Dabar turime didžiausią visiškai atvirą, lengvai atsisiunčiamą knygų metadata (ir taip ISBN) kolekciją pasaulyje."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Mes <a %(blog)s>plačiai rašėme</a> apie tai, kodėl mums rūpi išsaugojimas, ir kodėl šiuo metu esame kritiniame lange. Dabar turime identifikuoti retas, nepakankamai dėmesio sulaukiančias ir unikaliai rizikingas knygas ir jas išsaugoti. Turint gerą metadata apie visas pasaulio knygas, tai padeda."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Vizualizavimas"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Be apžvalgos vaizdo, mes taip pat galime pažvelgti į atskirus datasets, kuriuos įsigijome. Naudokite išskleidžiamąjį meniu ir mygtukus, kad perjungtumėte tarp jų."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Šiuose paveikslėliuose galima pamatyti daug įdomių modelių. Kodėl yra tam tikras linijų ir blokų reguliarumas, kuris atrodo vykstantis skirtingais masteliais? Kas yra tuščios sritys? Kodėl tam tikri datasets yra taip susitelkę? Paliksime šiuos klausimus skaitytojui kaip užduotį."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "10 000 $ atlygis"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Čia yra daug ką tyrinėti, todėl skelbiame atlygį už aukščiau pateiktos vizualizacijos tobulinimą. Skirtingai nuo daugumos mūsų atlygių, šis yra laiko ribotas. Jūs turite <a %(annas_archive)s>pateikti</a> savo atviro kodo kodą iki 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Geriausias pateikimas gaus 6 000 USD, antroji vieta – 3 000 USD, o trečioji vieta – 1 000 USD. Visi prizai bus išmokėti naudojant Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Žemiau pateikti minimalūs kriterijai. Jei nė vienas pateikimas neatitiks kriterijų, mes vis tiek galime skirti kai kuriuos prizus, tačiau tai bus mūsų nuožiūra."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork'uokite šį repo ir redaguokite šį tinklaraščio įrašo HTML (kiti backend'ai, išskyrus mūsų Flask backend'ą, nėra leidžiami)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Padarykite, kad aukščiau esanti nuotrauka būtų sklandžiai priartinama, kad galėtumėte priartinti iki atskirų ISBN. Spustelėjus ISBN, turėtų būti nukreipiama į metadata puslapį arba paiešką Anos Archyve."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Jūs vis tiek turite galėti perjungti visus skirtingus Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Šalies ir leidėjo diapazonai turėtų būti paryškinti užvedus pelę. Galite naudoti, pvz., <a %(github_xlcnd_isbnlib)s>data4info.py isbnlib'e</a> šalies informacijai, ir mūsų „isbngrp“ nuskaitymą leidėjams (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Tai turi gerai veikti tiek staliniuose, tiek mobiliuosiuose įrenginiuose."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Papildomiems taškams (tai tik idėjos — leiskite savo kūrybiškumui laisvai reikštis):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Didelis dėmesys bus skiriamas naudojimo patogumui ir išvaizdai."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Rodyti faktinę metadata atskiriems ISBN, kai priartinama, pvz., pavadinimą ir autorių."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Geresnė erdvės užpildymo kreivė. Pvz., zigzagas, einantis nuo 0 iki 4 pirmoje eilutėje ir tada atgal (atvirkščiai) nuo 5 iki 9 antroje eilutėje — taikomas rekursyviai."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Skirtingos arba pritaikomos spalvų schemos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Specialūs vaizdai Datasets palyginimui."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Būdai spręsti problemas, pvz., kiti metadata, kurie nesutampa gerai (pvz., labai skirtingi pavadinimai)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Vaizdų anotavimas su komentarais apie ISBN ar diapazonus."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Bet kokie euristikos metodai retų ar rizikos grupėje esančių knygų identifikavimui."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Bet kokios kūrybinės idėjos, kurias galite sugalvoti!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Galite visiškai nukrypti nuo minimalių kriterijų ir sukurti visiškai kitokią vizualizaciją. Jei ji bus tikrai įspūdinga, tai gali atitikti premijos reikalavimus, tačiau tai priklauso nuo mūsų sprendimo."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Pateikite pasiūlymus, palikdami komentarą <a %(annas_archive)s>šiame klausime</a> su nuoroda į jūsų šakotą repo, sujungimo užklausą arba skirtumą."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kodas"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Kodas, skirtas šioms nuotraukoms generuoti, taip pat kiti pavyzdžiai, gali būti rasti <a %(annas_archive)s>šiame kataloge</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Sukūrėme kompaktišką duomenų formatą, su kuriuo visa reikalinga ISBN informacija užima apie 75 MB (suspausta). Duomenų formato aprašymas ir kodas, skirtas jį generuoti, gali būti rasti <a %(annas_archive_l1244_1319)s>čia</a>. Norint gauti atlygį, jums nereikia to naudoti, bet tai tikriausiai yra patogiausias formatas pradėti. Galite transformuoti mūsų metadata kaip norite (nors visas jūsų kodas turi būti atviro kodo)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Negalime sulaukti, ką sugalvosite. Sėkmės!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anos Archyvo Konteineriai (AAC): didžiausios pasaulyje šešėlinės bibliotekos leidimų standartizavimas"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annės Archyvas tapo didžiausia šešėline biblioteka pasaulyje, todėl mums reikėjo standartizuoti savo leidimus."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annės Archyvas</a> tapo toli gražu didžiausia šešėline biblioteka pasaulyje ir vienintele tokio masto šešėline biblioteka, kuri yra visiškai atviro kodo ir atvirų duomenų. Žemiau pateikta lentelė iš mūsų Datasets puslapio (šiek tiek modifikuota):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Tai pasiekėme trimis būdais:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Veidrodžiuodami esamas atviro duomenų šešėlines bibliotekas (tokias kaip Sci-Hub ir Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Padėdami šešėlinėms bibliotekoms, kurios nori būti atviresnės, bet neturėjo laiko ar išteklių tai padaryti (tokios kaip Libgen komiksų kolekcija)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Rinkdami bibliotekas, kurios nenori dalintis dideliais kiekiais (tokios kaip Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Dėl (2) ir (3) dabar patys valdome nemažą torrentų kolekciją (šimtus TB). Iki šiol šias kolekcijas vertinome kaip vienkartines, tai reiškia, kad kiekvienai kolekcijai reikalinga speciali infrastruktūra ir duomenų organizavimas. Tai prideda reikšmingą papildomą darbą kiekvienam leidimui ir ypač apsunkina daugiau laipsniškų leidimų atlikimą."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Štai kodėl nusprendėme standartizuoti savo leidimus. Tai yra techninis tinklaraščio įrašas, kuriame pristatome savo standartą: <strong>Annės Archyvo Konteineriai</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Dizaino tikslai"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Mūsų pagrindinis naudojimo atvejis yra failų ir susijusių metaduomenų platinimas iš skirtingų esamų kolekcijų. Mūsų svarbiausi aspektai yra:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogeniški failai ir metaduomenys, kuo arčiau originalaus formato."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogeniški identifikatoriai šaltiniuose bibliotekose arba net identifikatorių trūkumas."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Atskiri metaduomenų ir failų duomenų leidimai arba tik metaduomenų leidimai (pvz., mūsų ISBNdb leidimas)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Platinimas per torrentus, tačiau su galimybe naudoti kitus platinimo metodus (pvz., IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Nekintami įrašai, nes turėtume manyti, kad mūsų torrentai gyvens amžinai."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementiniai leidimai / papildomi leidimai."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Mašininiu būdu skaitomi ir rašomi, patogiai ir greitai, ypač mūsų stekui (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Šiek tiek lengva žmogaus patikra, nors tai yra antraeilis dalykas, palyginti su mašininiu skaitomumu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Lengva užpildyti mūsų kolekcijas naudojant standartinę nuomojamą seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dvejetainiai duomenys gali būti tiesiogiai aptarnaujami žiniatinklio serverių, tokių kaip Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Kai kurie ne tikslai:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Mums nerūpi, kad failus būtų lengva naršyti rankiniu būdu diske ar ieškoti be išankstinio apdorojimo."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Mums nerūpi, kad būtų tiesiogiai suderinama su esama bibliotekos programine įranga."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Nors turėtų būti lengva bet kam užpildyti mūsų kolekciją naudojant torrentus, mes nesitikime, kad failai bus naudojami be reikšmingų techninių žinių ir įsipareigojimo."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Kadangi Annos Archyvas yra atviro kodo, norime tiesiogiai naudoti savo formatą. Kai atnaujiname savo paieškos indeksą, prieiname tik viešai prieinamus kelius, kad bet kas, kas kopijuoja mūsų biblioteką, galėtų greitai pradėti naudotis."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standartas"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Galiausiai, mes apsistojome ties gana paprastu standartu. Jis yra gana laisvas, nenormatyvinis ir dar vystomas."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Annės Archyvo Konteineris) yra vienas elementas, sudarytas iš <strong>metaduomenų</strong> ir, jei reikia, <strong>dvejetainės duomenų</strong>, abu yra nekintami. Jis turi pasaulinį unikalų identifikatorių, vadinamą <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Kolekcija.</strong> Kiekvienas AAC priklauso kolekcijai, kuri pagal apibrėžimą yra semantiškai nuoseklių AAC sąrašas. Tai reiškia, kad jei reikšmingai pakeisite metaduomenų formatą, turite sukurti naują kolekciją."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>„įrašų“ ir „failų“ kolekcijos.</strong> Pagal susitarimą, dažnai patogu išleisti „įrašus“ ir „failus“ kaip skirtingas kolekcijas, kad jie galėtų būti išleisti skirtingais grafikais, pvz., pagal nuskaitymo greitį. „Įrašas“ yra tik metaduomenų kolekcija, kurioje yra informacija, tokia kaip knygų pavadinimai, autoriai, ISBN ir kt., o „failai“ yra kolekcijos, kuriose yra tikrieji failai (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> AACID formatas yra toks: <code style=\"color: #0093ff\">aacid__{kolekcija}__{ISO 8601 laiko žyma}__{kolekcijos specifinis ID}__{shortuuid}</code>. Pavyzdžiui, vienas iš mūsų išleistų AACID yra <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{kolekcija}</code>: kolekcijos pavadinimas, kuris gali turėti ASCII raides, skaičius ir pabraukimus (bet ne dvigubus pabraukimus)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 laiko žyma}</code>: trumpa ISO 8601 versija, visada UTC, pvz., <code>20220723T194746Z</code>. Šis skaičius turi monotoniškai didėti kiekvienam leidimui, nors jo tiksli semantika gali skirtis priklausomai nuo kolekcijos. Siūlome naudoti nuskaitymo arba ID generavimo laiką."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{kolekcijos specifinis ID}</code>: kolekcijos specifinis identifikatorius, jei taikoma, pvz., Z-Library ID. Gali būti praleistas arba sutrumpintas. Turi būti praleistas arba sutrumpintas, jei AACID kitaip viršytų 150 simbolių."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, bet suspaustas iki ASCII, pvz., naudojant base57. Šiuo metu naudojame <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python biblioteką."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID diapazonas.</strong> Kadangi AACID turi monotoniškai didėjančias laiko žymas, galime tai naudoti, norėdami nurodyti diapazonus tam tikroje kolekcijoje. Naudojame šį formatą: <code style=\"color: blue\">aacid__{kolekcija}__{nuo_laiko_žymos}--{iki_laiko_žymos}</code>, kur laiko žymos yra įtrauktos. Tai atitinka ISO 8601 notaciją. Diapazonai yra tęstiniai ir gali persidengti, bet persidengimo atveju turi turėti identiškus įrašus kaip ir anksčiau išleisti toje kolekcijoje (kadangi AAC yra nekintami). Trūkstami įrašai neleidžiami."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata failas.</strong> Metadata failas turi AAC diapazono metaduomenis, skirtus vienai konkrečiai kolekcijai. Jie turi šias savybes:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Failo pavadinimas turi būti AACID diapazonas, prieš kurį yra <code style=\"color: red\">annas_archive_meta__</code> ir po kurio eina <code>.jsonl.zstd</code>. Pavyzdžiui, vienas iš mūsų leidimų vadinasi<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Kaip nurodo failo plėtinys, failo tipas yra <a %(jsonlines)s>JSON Lines</a>, suspaustas naudojant <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Kiekvienas JSON objektas turi turėti šiuos laukus aukščiausiame lygyje: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (neprivaloma). Kiti laukai nėra leidžiami."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> yra savavališka metadata, atitinkanti kolekcijos semantiką. Ji turi būti semantiškai nuosekli kolekcijoje."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> yra neprivaloma ir tai yra dvejetainių duomenų aplanko pavadinimas, kuriame yra atitinkami dvejetainiai duomenys. Atitinkamų dvejetainių duomenų failo pavadinimas tame aplanke yra įrašo AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code> priešdėlis gali būti pritaikytas jūsų institucijos pavadinimui, pvz., <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Dvejetainių duomenų aplankas.</strong> Aplankas su tam tikros kolekcijos AAC diapazono dvejetainiais duomenimis. Jie turi šias savybes:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Katalogo pavadinimas turi būti AACID diapazonas, su priešdėliu <code style=\"color: green\">annas_archive_data__</code>, be jokio priesagos. Pavyzdžiui, vienas iš mūsų faktinių leidimų turi katalogą, vadinamą<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Kataloge turi būti duomenų failai visiems AAC nurodytame diapazone. Kiekvienas duomenų failas turi turėti savo AACID kaip failo pavadinimą (be plėtinių)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Rekomenduojama, kad šie aplankai būtų valdomi pagal dydį, pvz., ne didesni nei 100GB-1TB kiekvienas, nors ši rekomendacija gali keistis laikui bėgant."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrentai.</strong> Metaduomenų failai ir dvejetainių duomenų aplankai gali būti sujungti į torrentus, su vienu torrentu per metaduomenų failą arba vienu torrentu per dvejetainių duomenų aplanką. Torrentai turi turėti originalų failo/katalogo pavadinimą plius <code>.torrent</code> priesagą kaip jų failo pavadinimą."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Pavyzdys"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Pažvelkime į mūsų naujausią Z-Library leidimą kaip pavyzdį. Jį sudaro dvi kolekcijos: “<span style=\"background: #fffaa3\">zlib3_records</span>” ir “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Tai leidžia mums atskirai nuskaityti ir išleisti metaduomenų įrašus nuo tikrųjų knygų failų. Todėl mes išleidome du torrentus su metaduomenų failais:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Mes taip pat išleidome daugybę torrentų su dvejetainių duomenų aplankais, bet tik “<span style=\"background: #ffd6fe\">zlib3_files</span>” kolekcijai, iš viso 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Paleidę <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> galime pamatyti, kas yra viduje:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Šiuo atveju tai yra knygos metaduomenys, kaip pranešė Z-Library. Aukščiausiame lygyje turime tik „aacid“ ir „metadata“, bet nėra „data_folder“, nes nėra atitinkamų dvejetainių duomenų. AACID turi „22430000“ kaip pagrindinį ID, kurį matome, kad yra paimtas iš „zlibrary_id“. Galime tikėtis, kad kiti AAC šioje kolekcijoje turės tą pačią struktūrą."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Dabar paleiskime <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Tai yra daug mažesnė AAC metadata, nors didžioji dalis šios AAC yra kitur dvejetainėje byloje! Juk šį kartą turime „data_folder“, todėl galime tikėtis, kad atitinkami dvejetainiai duomenys bus <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata“ turi „zlibrary_id“, todėl galime lengvai susieti jį su atitinkamu AAC „zlib_records“ kolekcijoje. Galėjome susieti įvairiais būdais, pvz., per AACID — standartas to nereikalauja."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Atkreipkite dėmesį, kad „metadata“ laukas nebūtinai turi būti JSON. Tai gali būti eilutė, kurioje yra XML arba bet kuris kitas duomenų formatas. Galite netgi saugoti metaduomenų informaciją susijusiame dvejetainėje masėje, pvz., jei tai yra daug duomenų."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Išvada"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Su šiuo standartu galime išleisti daugiau palaipsniui ir lengviau pridėti naujus duomenų šaltinius. Jau turime keletą įdomių leidimų, kurie yra ruošiami!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Tikimės, kad kitiems šešėliniams bibliotekoms bus lengviau atkartoti mūsų kolekcijas. Juk mūsų tikslas yra išsaugoti žmonijos žinias ir kultūrą amžinai, todėl kuo daugiau atsarginių kopijų, tuo geriau."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annos Atnaujinimas: visiškai atviro kodo archyvas, ElasticSearch, daugiau nei 300GB knygų viršelių"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Dirbome visą parą, kad pateiktume gerą alternatyvą su Annos Archyvu. Štai keletas dalykų, kuriuos neseniai pasiekėme."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Kai Z-Library buvo uždaryta ir jos (neva) įkūrėjai buvo suimti, dirbome visą parą, kad pateiktume gerą alternatyvą su Annos Archyvu (čia nesusiesime, bet galite tai rasti „Google“). Štai keletas dalykų, kuriuos neseniai pasiekėme."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annos Archyvas yra visiškai atviro kodo"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Tikime, kad informacija turėtų būti laisva, ir mūsų pačių kodas nėra išimtis. Mes išleidome visą savo kodą mūsų privačiai talpinamoje Gitlab instancijoje: <a %(annas_archive)s>Annos Programinė Įranga</a>. Taip pat naudojame problemų sekimo sistemą, kad organizuotume savo darbą. Jei norite prisijungti prie mūsų vystymo, tai puiki vieta pradėti."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Kad suteiktume jums skonį, ką mes dirbame, pažiūrėkite į mūsų neseniai atliktus klientų pusės našumo patobulinimus. Kadangi dar neįgyvendinome puslapiavimo, dažnai grąžindavome labai ilgas paieškos puslapius, su 100-200 rezultatų. Nenorėjome per anksti nutraukti paieškos rezultatų, tačiau tai reiškė, kad kai kuriuos įrenginius tai sulėtindavo. Tam įgyvendinome mažą triuką: daugumą paieškos rezultatų apvyniojome HTML komentarais (<code><!-- --></code>), o tada parašėme mažą Javascript, kuris aptiktų, kada rezultatas turėtų tapti matomas, tuo momentu mes išvyniotume komentarą:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualizacija\" įgyvendinta 23 eilutėse, nereikia įmantrių bibliotekų! Tai yra greito pragmatiško kodo pavyzdys, kurį gaunate, kai turite ribotą laiką ir realias problemas, kurias reikia išspręsti. Pranešta, kad mūsų paieška dabar veikia gerai lėtuose įrenginiuose!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Kita didelė pastanga buvo automatizuoti duomenų bazės kūrimą. Kai pradėjome, tiesiog atsitiktinai sujungėme skirtingus šaltinius. Dabar norime juos atnaujinti, todėl parašėme daugybę scenarijų, kad atsisiųstume naujus metaduomenis iš dviejų Library Genesis šakų ir juos integruotume. Tikslas yra ne tik padaryti tai naudinga mūsų archyvui, bet ir palengvinti visiems, kurie nori žaisti su šešėlinės bibliotekos metaduomenimis. Tikslas būtų Jupyter užrašų knygelė, kurioje būtų įvairių įdomių metaduomenų, kad galėtume atlikti daugiau tyrimų, pavyzdžiui, išsiaiškinti, kokia <a %(blog)s>ISBN dalis yra išsaugota amžinai</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Galiausiai, atnaujinome savo aukojimo sistemą. Dabar galite naudoti kreditinę kortelę, kad tiesiogiai pervestumėte pinigus į mūsų kriptovaliutų pinigines, iš tikrųjų nereikėdami nieko žinoti apie kriptovaliutas. Mes stebėsime, kaip tai veikia praktikoje, bet tai yra didelis dalykas."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Perėjimas prie ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Vienas iš mūsų <a %(annas_archive)s>bilietų</a> buvo įvairių problemų su mūsų paieškos sistema rinkinys. Naudojome MySQL pilno teksto paiešką, nes visus duomenis turėjome MySQL. Tačiau tai turėjo savo ribas:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Kai kurios užklausos užtruko labai ilgai, iki taško, kai jos užėmė visas atviras jungtis."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Pagal numatytuosius nustatymus MySQL turi minimalų žodžio ilgį, arba jūsų indeksas gali tapti labai didelis. Žmonės pranešė, kad negalėjo ieškoti „Ben Hur“."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Paieška buvo tik šiek tiek greita, kai visiškai įkelta į atmintį, o tai reikalavo gauti brangesnį įrenginį, kad tai veiktų, plius kai kuriuos komandas, kad indeksas būtų įkeltas paleidimo metu."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nebūtume galėję lengvai išplėsti, kad sukurtume naujas funkcijas, tokias kaip geresnė <a %(wikipedia_cjk_characters)s>tokenizacija ne tarpuose esančioms kalboms</a>, filtravimas/fasetavimas, rūšiavimas, „ar turėjote omenyje“ pasiūlymai, automatinis užbaigimas ir pan."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Pasikalbėję su daugybe ekspertų, nusprendėme naudoti ElasticSearch. Tai nebuvo tobula (jų numatytieji „ar turėjote omenyje“ pasiūlymai ir automatinio užbaigimo funkcijos yra prastos), bet apskritai tai buvo daug geriau nei MySQL paieškai. Mes vis dar nesame <a %(youtube)s>per daug entuziastingi</a> naudoti jį bet kokiems misijai kritiniams duomenims (nors jie padarė daug <a %(elastic_co)s>pažangos</a>), bet apskritai esame gana patenkinti perėjimu."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Kol kas įgyvendinome daug greitesnę paiešką, geresnę kalbų palaikymą, geresnį atitikimo rūšiavimą, skirtingas rūšiavimo parinktis ir filtravimą pagal kalbą/knygos tipą/bylos tipą. Jei jums įdomu, kaip tai veikia, <a %(annas_archive_l140)s>pažiūrėkite</a> <a %(annas_archive_l1115)s>čia</a> <a %(annas_archive_l1635)s>daugiau</a>. Tai gana prieinama, nors galėtų būti daugiau komentarų…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ knygų viršelių išleista"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Galiausiai, džiaugiamės galėdami pranešti apie mažą išleidimą. Bendradarbiaudami su žmonėmis, kurie valdo Libgen.rs šaką, dalijamės visais jų knygų viršeliais per torrentus ir IPFS. Tai paskirstys viršelių peržiūros apkrovą tarp daugiau mašinų ir geriau juos išsaugos. Daugeliu (bet ne visais) atvejais knygų viršeliai yra įtraukti į pačius failus, todėl tai yra tam tikri „išvestiniai duomenys“. Tačiau turėti juos IPFS vis dar labai naudinga kasdieniam tiek Anos Archyvo, tiek įvairių Library Genesis šakų veikimui."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Kaip įprasta, šį išleidimą galite rasti Piratų bibliotekos veidrodyje (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvas</a>). Mes čia nesusiesime, bet galite lengvai jį rasti."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Tikimės, kad galime šiek tiek sulėtinti tempą, dabar, kai turime tinkamą alternatyvą Z-Library. Šis darbo krūvis nėra ypač tvarus. Jei jus domina pagalba su programavimu, serverių veikimu ar išsaugojimo darbu, būtinai susisiekite su mumis. Dar yra daug <a %(annas_archive)s>darbo, kurį reikia atlikti</a>. Dėkojame už jūsų susidomėjimą ir palaikymą."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Ana ir komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anos Archyvas atsarginę kopiją padarė didžiausiai pasaulyje komiksų šešėlinei bibliotekai (95TB) — galite padėti ją sėti"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Didžiausia pasaulyje komiksų šešėlinė biblioteka turėjo vieną gedimo tašką... iki šiandien."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskutuoti Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Didžiausia komiksų šešėlinė biblioteka tikriausiai yra tam tikros Library Genesis šakos: Libgen.li. Vienas administratorius, valdantis tą svetainę, sugebėjo surinkti neįtikėtiną komiksų kolekciją, kurią sudaro daugiau nei 2 milijonai failų, iš viso daugiau nei 95TB. Tačiau, skirtingai nei kitos Library Genesis kolekcijos, ši nebuvo prieinama masiškai per torrentus. Galėjote pasiekti šiuos komiksus tik individualiai per jo lėtą asmeninį serverį — vieną gedimo tašką. Iki šiandien!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Šiame įraše papasakosime daugiau apie šią kolekciją ir apie mūsų lėšų rinkimą, kad palaikytume daugiau šio darbo."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon bando pasinerti į kasdienį bibliotekos pasaulį…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen šakos"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Pirmiausia, šiek tiek fono. Galbūt žinote Library Genesis dėl jų epinės knygų kolekcijos. Mažiau žmonių žino, kad Library Genesis savanoriai sukūrė kitus projektus, tokius kaip didelė žurnalų ir standartinių dokumentų kolekcija, pilna Sci-Hub atsarginė kopija (bendradarbiaujant su Sci-Hub įkūrėja Alexandra Elbakyan) ir iš tiesų, didžiulė komiksų kolekcija."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Tam tikru momentu skirtingi Library Genesis veidrodžių operatoriai pasuko skirtingais keliais, kas sukėlė dabartinę situaciją, kai yra keletas skirtingų „šakų“, vis dar turinčių Library Genesis pavadinimą. Libgen.li šaka unikali tuo, kad turi šią komiksų kolekciją, taip pat didelę žurnalų kolekciją (kurią mes taip pat dirbame)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Bendradarbiavimas"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Atsižvelgiant į jos dydį, ši kolekcija ilgą laiką buvo mūsų norų sąraše, todėl po mūsų sėkmės su Z-Library atsargine kopija, mes nukreipėme savo dėmesį į šią kolekciją. Iš pradžių mes ją tiesiogiai nuskaitydavome, kas buvo nemažas iššūkis, nes jų serveris nebuvo geriausios būklės. Tokiu būdu gavome apie 15TB, bet tai buvo lėtas procesas."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Laimei, mums pavyko susisiekti su bibliotekos operatoriumi, kuris sutiko atsiųsti mums visus duomenis tiesiogiai, ir tai buvo daug greičiau. Vis dėlto prireikė daugiau nei pusės metų, kad perkeltume ir apdorotume visus duomenis, ir beveik praradome juos dėl disko sugadinimo, kas būtų reiškę, kad reikėtų pradėti viską iš naujo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Ši patirtis privertė mus manyti, kad svarbu kuo greičiau išplatinti šiuos duomenis, kad jie galėtų būti atkartoti plačiai ir toli. Esame tik vieno ar dviejų nelaimingų atsitikimų nuo to, kad prarastume šią kolekciją amžiams!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Kolekcija"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Greitas judėjimas reiškia, kad kolekcija yra šiek tiek neorganizuota... Pažvelkime. Įsivaizduokime, kad turime failų sistemą (kurią iš tikrųjų skaidome per torrentus):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Pirmasis katalogas, <code>/repository</code>, yra labiau struktūruota šios dalies dalis. Šis katalogas turi vadinamuosius „tūkstančio katalogus“: katalogus, kuriuose yra tūkstančiai failų, kurie yra nuosekliai numeruojami duomenų bazėje. Katalogas <code>0</code> turi failus su comic_id 0–999 ir taip toliau."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Tai yra ta pati schema, kurią Library Genesis naudoja savo grožinės ir negrožinės literatūros kolekcijoms. Idėja yra ta, kad kiekvienas „tūkstančio katalogas“ automatiškai tampa torrentu, kai tik jis užpildomas."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tačiau Libgen.li operatorius niekada nesukūrė torrentų šiai kolekcijai, todėl tūkstančiai katalogų greičiausiai tapo nepatogūs ir virto „nesutvarkytais katalogais“. Tai yra <code>/comics0</code> iki <code>/comics4</code>. Jie visi turi unikalią katalogų struktūrą, kuri tikriausiai buvo prasminga renkant failus, bet dabar mums nelabai suprantama. Laimei, metadata vis dar tiesiogiai nurodo visus šiuos failus, todėl jų saugojimo organizacija diske iš tikrųjų nesvarbi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata yra prieinama MySQL duomenų bazės formatu. Ją galima atsisiųsti tiesiogiai iš Libgen.li svetainės, bet mes taip pat padarysime ją prieinamą per torrentą, kartu su mūsų pačių lentele, kurioje yra visi MD5 maišos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analizė"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Kai gaunate 95TB į savo saugojimo klasterį, bandote suprasti, kas ten iš viso yra... Mes atlikome analizę, kad pamatytume, ar galėtume šiek tiek sumažinti dydį, pavyzdžiui, pašalindami dublikatus. Štai keletas mūsų išvadų:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantiniai dublikatai (skirtingi to paties knygos skenavimai) teoriškai gali būti filtruojami, bet tai sudėtinga. Rankiniu būdu peržiūrėdami komiksus radome per daug klaidingų teigiamų rezultatų."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Yra keletas dublikatų tik pagal MD5, kas yra gana švaistoma, bet jų filtravimas suteiktų tik apie 1% in sutaupymą. Tokiu mastu tai vis dar yra apie 1TB, bet taip pat, tokiu mastu 1TB iš tikrųjų nesvarbu. Mes verčiau nerizikuotume netyčia sunaikinti duomenis šio proceso metu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Radome krūvą ne knygų duomenų, tokių kaip filmai, paremti komiksais. Tai taip pat atrodo švaistoma, nes jie jau plačiai prieinami kitais būdais. Tačiau supratome, kad negalime tiesiog filtruoti filmų failų, nes taip pat yra <em>interaktyvių komiksų knygų</em>, kurios buvo išleistos kompiuteryje, kurias kažkas įrašė ir išsaugojo kaip filmus."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Galiausiai, bet kas, ką galėtume ištrinti iš kolekcijos, sutaupytų tik kelis procentus. Tada prisiminėme, kad esame duomenų kaupėjai, ir žmonės, kurie tai atkartos, taip pat yra duomenų kaupėjai, todėl, „KĄ TU TURI OMENYJE, IŠTRINTI?!“ :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Todėl pristatome jums visą, nemodifikuotą kolekciją. Tai daug duomenų, bet tikimės, kad pakankamai žmonių norės ją sėti."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Lėšų rinkimas"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Mes išleidžiame šiuos duomenis dideliais gabalais. Pirmasis torrentas yra <code>/comics0</code>, kurį sudėjome į vieną didžiulį 12TB .tar failą. Tai geriau jūsų kietajam diskui ir torrent programinei įrangai nei milijonas mažesnių failų."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Kaip šios išleidimo dalis, mes organizuojame lėšų rinkimą. Siekiame surinkti 20 000 USD, kad padengtume operacines ir sutartines šios kolekcijos išlaidas, taip pat leistume tęsti ir būsimus projektus. Turime keletą <em>milžiniškų</em> projektų, kurie yra kuriami."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Kam aš remiu savo auka?</em> Trumpai tariant: mes atsarginę kopiją visų žmonijos žinių ir kultūros, ir darome jas lengvai prieinamas. Visi mūsų kodai ir duomenys yra atviro kodo, mes esame visiškai savanorių valdomas projektas, ir iki šiol išsaugojome 125TB knygų (be Libgen ir Scihub esamų torrentų). Galiausiai mes kuriame smagračio mechanizmą, kuris leidžia ir skatina žmones rasti, skenuoti ir atsarginę kopiją visų pasaulio knygų. Apie mūsų pagrindinį planą rašysime būsimame įraše. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Jei paaukosite už 12 mėnesių „Nuostabus Archyvaras“ narystę (780 USD), galėsite <strong>„įsivaikinti torrentą“</strong>, tai reiškia, kad mes įdėsime jūsų vartotojo vardą ar žinutę į vieno iš torrentų failo pavadinimą!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Galite paaukoti apsilankę <a %(wikipedia_annas_archive)s>Anos Archyve</a> ir paspaudę mygtuką „Paaukoti“. Taip pat ieškome daugiau savanorių: programinės įrangos inžinierių, saugumo tyrėjų, anonimiškų prekybos ekspertų ir vertėjų. Galite mus paremti ir teikdami prieglobos paslaugas. Ir, žinoma, prašome dalintis mūsų torrentais!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dėkojame visiems, kurie jau taip dosniai mus parėmė! Jūs tikrai darote skirtumą."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Štai iki šiol išleisti torrentai (dar apdorojame likusius):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Visus torrentus galite rasti <a %(wikipedia_annas_archive)s>Anos Archyve</a> skiltyje „Datasets“ (mes tiesiogiai ten nenurodome, kad nuorodos į šį tinklaraštį nebūtų pašalintos iš Reddit, Twitter ir kt.). Iš ten sekite nuorodą į Tor svetainę."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Kas toliau?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Daugybė torrentų puikiai tinka ilgalaikiam išsaugojimui, bet ne kasdieniam naudojimui. Mes dirbsime su prieglobos partneriais, kad visa ši informacija būtų prieinama internete (kadangi Anos Archyvas nieko tiesiogiai netalpina). Žinoma, šias atsisiuntimo nuorodas galėsite rasti Anos Archyve."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Taip pat kviečiame visus ką nors daryti su šiais duomenimis! Padėkite mums juos geriau analizuoti, pašalinti dublikatus, įkelti į IPFS, perdirbti, treniruoti savo AI modelius ir t. t. Visa tai jūsų, ir mes nekantraujame pamatyti, ką su tuo padarysite."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Galiausiai, kaip jau minėta, mes vis dar turime keletą didžiulių leidimų, kurie netrukus pasirodys (jei <em>kažkas</em> galėtų <em>netyčia</em> atsiųsti mums <em>tam tikros</em> ACS4 duomenų bazės išrašą, žinote, kur mus rasti...), taip pat kuriame sūkurį, kad galėtume atsargiai kopijuoti visas pasaulio knygas."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Taigi, sekite naujienas, mes tik pradedame."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x naujos knygos pridėtos į Piratų Bibliotekos Veidrodį (+24TB, 3,8 milijono knygų)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Pirate Library Mirror originaliame leidime (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>), mes sukūrėme Z-Library, didelės nelegalios knygų kolekcijos, atkartojimą. Primename, kad tai, ką parašėme originaliame tinklaraščio įraše:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library yra populiari (ir nelegali) biblioteka. Jie paėmė Library Genesis kolekciją ir padarė ją lengvai ieškomą. Be to, jie tapo labai veiksmingi pritraukiant naujas knygų įnašas, skatindami prisidedančius vartotojus įvairiomis privilegijomis. Šiuo metu jie negrąžina šių naujų knygų atgal į Library Genesis. Skirtingai nei Library Genesis, jie neleidžia lengvai atkartoti savo kolekcijos, kas trukdo plačiam išsaugojimui. Tai svarbu jų verslo modeliui, nes jie ima mokestį už prieigą prie savo kolekcijos dideliais kiekiais (daugiau nei 10 knygų per dieną)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Mes nedarome moralinių sprendimų dėl mokesčio už didelės apimties prieigą prie neteisėtos knygų kolekcijos. Neabejotina, kad Z-Library sėkmingai išplėtė prieigą prie žinių ir pritraukė daugiau knygų. Mes tiesiog esame čia, kad atliktume savo dalį: užtikrinti ilgalaikį šios privačios kolekcijos išsaugojimą."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Ta kolekcija datuojama 2021 metų viduriu. Tuo tarpu Z-Library auga stulbinančiu tempu: jie pridėjo apie 3,8 milijono naujų knygų. Žinoma, yra keletas dublikatų, bet dauguma jų atrodo kaip teisėtai naujos knygos arba aukštesnės kokybės anksčiau pateiktų knygų skenavimai. Tai daugiausia dėl padidėjusio savanorių moderatorių skaičiaus Z-Library ir jų masinio įkėlimo sistemos su dublikatų pašalinimu. Norėtume juos pasveikinti su šiais pasiekimais."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Džiaugiamės galėdami pranešti, kad gavome visas knygas, kurios buvo pridėtos į Z-Library tarp mūsų paskutinio atkartojimo ir 2022 metų rugpjūčio. Taip pat grįžome ir surinkome kai kurias knygas, kurias praleidome pirmą kartą. Iš viso ši nauja kolekcija yra apie 24TB, kas yra daug didesnė nei ankstesnė (7TB). Mūsų atkartojimas dabar yra 31TB iš viso. Vėlgi, mes pašalinome dublikatus su Library Genesis, nes jau yra prieinami torrentai šiai kolekcijai."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Prašome eiti į Piratų bibliotekos atkartojimą, kad patikrintumėte naują kolekciją (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>). Ten yra daugiau informacijos apie tai, kaip failai yra struktūrizuoti ir kas pasikeitė nuo paskutinio karto. Mes nesusiesime su ja iš čia, nes tai tik tinklaraščio svetainė, kuri netalpina jokių nelegalių medžiagų."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Žinoma, sėjimas taip pat yra puikus būdas mums padėti. Dėkojame visiems, kurie sėja mūsų ankstesnį torrentų rinkinį. Esame dėkingi už teigiamą atsaką ir džiaugiamės, kad yra tiek daug žmonių, kurie rūpinasi žinių ir kultūros išsaugojimu tokiu neįprastu būdu."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Ana ir komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Kaip tapti piratų archyvaru"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Pirmasis iššūkis gali būti netikėtas. Tai nėra techninė problema ar teisinė problema. Tai psichologinė problema."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Prieš pradedant, dvi naujienos apie Piratų bibliotekos atkartojimą (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Annos Archyvą</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Gavome labai dosnių aukų. Pirmoji buvo 10 tūkst. dolerių iš anoniminio asmens, kuris taip pat rėmė \"bookwarrior\", originalų Library Genesis įkūrėją. Ypatinga padėka bookwarrior už šios aukos palengvinimą. Antroji buvo dar 10 tūkst. dolerių iš anoniminio donoro, kuris susisiekė po mūsų paskutinio leidimo ir buvo įkvėptas padėti. Taip pat turėjome keletą mažesnių aukų. Labai ačiū už jūsų dosnų palaikymą. Turime keletą įdomių naujų projektų, kuriuos tai palaikys, tad sekite naujienas."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Turėjome techninių sunkumų su mūsų antrojo leidimo dydžiu, bet mūsų torrentai dabar veikia ir sėja. Taip pat gavome dosnų pasiūlymą iš anoniminio asmens sėti mūsų kolekciją jų labai greituose serveriuose, todėl atliekame specialų įkėlimą į jų mašinas, po kurio visi kiti, kurie atsisiunčia kolekciją, turėtų pastebėti didelį greičio pagerėjimą."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Visos knygos gali būti parašytos apie <em>kodėl</em> skaitmeninio išsaugojimo apskritai ir piratų archyvavimo ypač, bet leiskite mums pateikti trumpą įvadą tiems, kurie nėra labai susipažinę. Pasaulis gamina daugiau žinių ir kultūros nei bet kada anksčiau, bet taip pat daugiau jų prarandama nei bet kada anksčiau. Žmonija daugiausia pasitiki korporacijomis, tokiomis kaip akademiniai leidėjai, srautinio perdavimo paslaugos ir socialinės žiniasklaidos įmonės, šiuo paveldu, ir jos dažnai neįrodė esą puikūs globėjai. Pažiūrėkite dokumentinį filmą \"Digital Amnesia\" arba bet kokią Jasono Scotto kalbą."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Yra keletas institucijų, kurios gerai archyvuoja tiek, kiek gali, bet jos yra saistomos įstatymų. Kaip piratai, mes esame unikalioje padėtyje archyvuoti kolekcijas, kurių jie negali paliesti, dėl autorių teisių vykdymo ar kitų apribojimų. Mes taip pat galime atkartoti kolekcijas daug kartų visame pasaulyje, taip padidindami tinkamo išsaugojimo galimybes."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Kol kas nesigilinsime į diskusijas apie intelektinės nuosavybės privalumus ir trūkumus, įstatymų pažeidimo moralę, cenzūros apmąstymus ar prieigos prie žinių ir kultūros klausimą. Su visa tai išsprendus, pasinerkime į <em>kaip</em>. Pasidalinsime, kaip mūsų komanda tapo piratų archyvarais, ir pamokomis, kurias išmokome pakeliui. Yra daug iššūkių, kai pradedate šią kelionę, ir tikimės, kad galime padėti jums per kai kuriuos iš jų."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Bendruomenė"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Pirmasis iššūkis gali būti netikėtas. Tai nėra techninė problema ar teisinė problema. Tai psichologinė problema: darbas šešėlyje gali būti nepaprastai vienišas. Priklausomai nuo to, ką planuojate daryti, ir jūsų grėsmės modelio, gali tekti būti labai atsargiems. Viename spektro gale turime žmones kaip Alexandra Elbakyan*, Sci-Hub įkūrėją, kuri yra labai atvira apie savo veiklą. Tačiau ji yra didelėje rizikoje būti suimta, jei šiuo metu apsilankytų Vakarų šalyje, ir galėtų susidurti su dešimtmečiais kalėjimo. Ar tai rizika, kurią norėtumėte prisiimti? Mes esame kitame spektro gale; labai atsargūs, kad nepaliktume jokių pėdsakų, ir turime stiprią operacinę saugą."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Kaip minėta HN \"ynno\", Alexandra iš pradžių nenorėjo būti žinoma: \"Jos serveriai buvo sukonfigūruoti taip, kad išduotų detalias PHP klaidų žinutes, įskaitant pilną klaidos šaltinio failo kelią, kuris buvo kataloge /home/<USER>" Taigi, naudokite atsitiktinius vartotojų vardus kompiuteriuose, kuriuos naudojate šiems dalykams, jei netyčia ką nors neteisingai sukonfigūruosite."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Tačiau tas slaptumas turi psichologinę kainą. Dauguma žmonių mėgsta būti pripažinti už savo darbą, tačiau jūs negalite gauti jokio pripažinimo už tai realiame gyvenime. Net paprasti dalykai gali būti iššūkis, kaip draugai klausia, ką veikėte (tam tikru momentu \"žaidimas su mano NAS / homelab\" tampa nuobodus)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Štai kodėl taip svarbu rasti bendruomenę. Galite paaukoti dalį operacinio saugumo, pasitikėdami labai artimais draugais, kuriais žinote, kad galite giliai pasitikėti. Net ir tada būkite atsargūs, kad nieko nerašytumėte, jei jie turėtų perduoti savo el. laiškus valdžios institucijoms arba jei jų įrenginiai būtų pažeisti kitaip."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Dar geriau yra rasti bendraminčių piratų. Jei jūsų artimi draugai nori prisijungti prie jūsų, puiku! Priešingu atveju galite rasti kitų internete. Deja, tai vis dar nišinė bendruomenė. Iki šiol radome tik keletą kitų, kurie yra aktyvūs šioje srityje. Geros pradinės vietos atrodo Library Genesis forumai ir r/DataHoarder. Archive Team taip pat turi panašiai mąstančių asmenų, nors jie veikia pagal įstatymus (net jei kai kuriose pilkose įstatymų srityse). Tradicinės \"warez\" ir piratavimo scenos taip pat turi žmonių, kurie mąsto panašiai."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Esame atviri idėjoms, kaip skatinti bendruomenę ir tyrinėti idėjas. Drąsiai rašykite mums Twitter ar Reddit. Galbūt galėtume surengti kokį nors forumą ar pokalbių grupę. Vienas iššūkis yra tas, kad tai gali būti lengvai cenzūruojama naudojant įprastas platformas, todėl turėtume tai talpinti patys. Taip pat yra kompromisas tarp šių diskusijų visiško viešumo (didesnis potencialus įsitraukimas) ir jų privatumo (neleisti potencialiems \"taikiniams\" žinoti, kad ketiname juos surinkti). Turėsime apie tai pagalvoti. Praneškite, jei jus tai domina!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projektai"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Kai vykdome projektą, jis turi kelis etapus:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domeno pasirinkimas / filosofija: Kur norite apytiksliai sutelkti dėmesį ir kodėl? Kokios yra jūsų unikalios aistros, įgūdžiai ir aplinkybės, kurias galite panaudoti savo naudai?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Tikslinės kolekcijos pasirinkimas: Kurią konkrečią kolekciją atkartosite?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metaduomenų rinkimas: Failų informacijos katalogavimas, neatsisiunčiant pačių (dažnai daug didesnių) failų."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Duomenų pasirinkimas: Remiantis metaduomenimis, susiaurinama, kurie duomenys šiuo metu yra svarbiausi archyvavimui. Tai gali būti viskas, bet dažnai yra pagrįstas būdas taupyti vietą ir pralaidumą."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Duomenų rinkimas: Tiesioginis duomenų gavimas."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Platinimas: Supakavimas į torrentus, paskelbimas kažkur, žmonių įtraukimas į platinimą."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Tai nėra visiškai nepriklausomi etapai, ir dažnai įžvalgos iš vėlesnio etapo grąžina jus į ankstesnį etapą. Pavyzdžiui, metu, kai renkate metadata, galite suprasti, kad pasirinktas taikinys turi gynybinius mechanizmus, viršijančius jūsų įgūdžių lygį (pvz., IP blokavimas), todėl grįžtate ir randate kitą taikinį."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domeno pasirinkimas / filosofija"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nėra trūkumo žinių ir kultūrinio paveldo, kurį reikia išsaugoti, kas gali būti pribloškiantis. Todėl dažnai naudinga skirti akimirką ir pagalvoti, koks gali būti jūsų indėlis."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Kiekvienas turi skirtingą požiūrį į tai, bet čia yra keletas klausimų, kuriuos galite sau užduoti:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Kodėl jus tai domina? Kas jus jaudina? Jei galėtume surinkti grupę žmonių, kurie visi archyvuotų tai, kas jiems ypatingai rūpi, tai apimtų daug! Jūs žinosite daug daugiau nei vidutinis žmogus apie savo aistrą, pavyzdžiui, kokie duomenys yra svarbūs išsaugoti, kokios yra geriausios kolekcijos ir internetinės bendruomenės ir pan."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Kokius įgūdžius turite, kuriuos galite panaudoti savo naudai? Pavyzdžiui, jei esate interneto saugumo ekspertas, galite rasti būdų, kaip įveikti IP blokavimus saugiems tikslams. Jei puikiai organizuojate bendruomenes, galbūt galite suburti žmones aplink tikslą. Tačiau naudinga žinoti šiek tiek programavimo, net jei tik tam, kad viso proceso metu išlaikytumėte gerą operacinį saugumą."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Kiek laiko turite tam? Mūsų patarimas būtų pradėti nuo mažų projektų ir imtis didesnių, kai įgysite patirties, tačiau tai gali tapti viską užvaldančiu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Kokia būtų didelės naudos sritis, į kurią verta sutelkti dėmesį? Jei ketinate praleisti X valandas piratų archyvavimui, kaip galite gauti didžiausią „naudos už pinigus“?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Kokie yra unikalūs būdai, kaip apie tai galvojate? Galbūt turite įdomių idėjų ar požiūrių, kurių kiti galėjo nepastebėti."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Mūsų atveju ypatingai rūpėjome ilgalaikiu mokslo išsaugojimu. Žinojome apie Library Genesis ir kaip ji buvo visiškai atkartota daugybę kartų naudojant torrentus. Mums patiko ši idėja. Vieną dieną vienas iš mūsų bandė rasti mokslinius vadovėlius Library Genesis, bet jų nerado, kas sukėlė abejonių dėl jos išsamumo. Tada ieškojome tų vadovėlių internete ir radome juos kitose vietose, kas pasėjo mūsų projekto sėklą. Net prieš sužinodami apie Z-Library, turėjome idėją ne bandyti surinkti visas tas knygas rankiniu būdu, bet sutelkti dėmesį į esamų kolekcijų atkartojimą ir jų grąžinimą į Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Tikslų pasirinkimas"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Taigi, turime sritį, į kurią žiūrime, dabar kurią konkrečią kolekciją atkartoti? Yra keletas dalykų, kurie sudaro gerą tikslą:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Didelė"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unikali: dar nėra gerai padengta kitų projektų."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Prieinama: nenaudoja daugybės apsaugos sluoksnių, kad užkirstų kelią jūsų metadata ir duomenų nuskaitymui."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Speciali įžvalga: turite specialios informacijos apie šį tikslą, pavyzdžiui, kažkaip turite specialią prieigą prie šios kolekcijos arba sugebėjote įveikti jų gynybą. Tai nėra būtina (mūsų būsimas projektas nieko ypatingo nedaro), bet tikrai padeda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Kai radome savo mokslinius vadovėlius kitose svetainėse nei Library Genesis, bandėme išsiaiškinti, kaip jie pateko į internetą. Tada radome Z-Library ir supratome, kad nors dauguma knygų pirmiausia ten nepasirodo, jos galiausiai ten atsiduria. Sužinojome apie jos santykį su Library Genesis ir (finansinę) paskatų struktūrą bei pranašesnę vartotojo sąsają, kurios abi padarė ją daug išsamesne kolekcija. Tada atlikome preliminarų metadata ir duomenų nuskaitymą ir supratome, kad galime apeiti jų IP atsisiuntimo apribojimus, pasinaudodami vieno iš mūsų narių specialia prieiga prie daugybės proxy serverių."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Kai tyrinėjate skirtingus tikslus, jau svarbu slėpti savo pėdsakus naudojant VPN ir vienkartinius el. pašto adresus, apie kuriuos kalbėsime vėliau."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata nuskaitymas"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Pakalbėkime šiek tiek techniškiau. Norėdami iš tikrųjų nuskaityti metadata iš svetainių, mes laikėmės gana paprasto požiūrio. Naudojame Python skriptus, kartais curl, ir MySQL duomenų bazę rezultatams saugoti. Nenaudojome jokios sudėtingos nuskaitymo programinės įrangos, kuri galėtų žemėlapiuoti sudėtingas svetaines, nes iki šiol mums reikėjo nuskaityti tik vieno ar dviejų tipų puslapius, tiesiog numeruojant per ID ir analizuojant HTML. Jei nėra lengvai numeruojamų puslapių, tada jums gali prireikti tinkamo naršyklės, kuri bandytų rasti visus puslapius."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Prieš pradėdami nuskaityti visą svetainę, pabandykite tai padaryti rankiniu būdu. Pereikite per kelias dešimtis puslapių patys, kad suprastumėte, kaip tai veikia. Kartais jau tokiu būdu susidursite su IP blokavimu ar kitokiu įdomiu elgesiu. Tas pats galioja ir duomenų nuskaitymui: prieš gilindamiesi į šį tikslą, įsitikinkite, kad iš tikrųjų galite efektyviai atsisiųsti jo duomenis."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Norėdami apeiti apribojimus, galite išbandyti keletą dalykų. Ar yra kitų IP adresų ar serverių, kurie talpina tuos pačius duomenis, bet neturi tų pačių apribojimų? Ar yra API galinių taškų, kurie neturi apribojimų, nors kiti turi? Koks atsisiuntimo greitis blokuoja jūsų IP ir kiek laiko? Arba ar jūs neblokuojate, bet sulėtinate? Ką daryti, jei sukuriate vartotojo paskyrą, kaip tada keičiasi dalykai? Ar galite naudoti HTTP/2, kad išlaikytumėte atviras jungtis, ir ar tai padidina puslapių užklausų greitį? Ar yra puslapių, kurie išvardija kelis failus vienu metu, ir ar ten pateikta informacija yra pakankama?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dalykai, kuriuos tikriausiai norėsite išsaugoti, apima:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Pavadinimas"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Failo pavadinimas / vieta"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: gali būti vidinis ID, bet ID kaip ISBN ar DOI taip pat yra naudingi."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dydis: norint apskaičiuoti, kiek disko vietos jums reikia."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): norint patvirtinti, kad tinkamai atsisiuntėte failą."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Pridėjimo/modifikavimo data: kad galėtumėte grįžti vėliau ir atsisiųsti failus, kurių anksčiau neatsisiuntėte (nors dažnai tam galite naudoti ir ID arba maišos kodą)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Aprašymas, kategorija, žymos, autoriai, kalba ir kt."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Paprastai tai atliekame dviem etapais. Pirmiausia atsisiunčiame neapdorotus HTML failus, dažniausiai tiesiai į MySQL (kad išvengtume daugybės mažų failų, apie kuriuos kalbėsime toliau). Tada, atskirame žingsnyje, peržiūrime tuos HTML failus ir išskirstome juos į tikras MySQL lenteles. Tokiu būdu nereikia visko iš naujo atsisiųsti, jei pastebite klaidą savo analizės kode, nes galite tiesiog perdirbti HTML failus su nauju kodu. Taip pat dažnai lengviau paralelizuoti apdorojimo etapą, taip sutaupant laiko (ir galite rašyti apdorojimo kodą, kol vyksta duomenų rinkimas, o ne rašyti abu etapus vienu metu)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Galiausiai, atkreipkite dėmesį, kad kai kuriems tikslams metaduomenų rinkimas yra viskas, kas yra. Yra didžiulės metaduomenų kolekcijos, kurios nėra tinkamai išsaugotos."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Duomenų pasirinkimas"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Dažnai galite naudoti metaduomenis, kad nustatytumėte pagrįstą duomenų dalį, kurią reikia atsisiųsti. Net jei galiausiai norite atsisiųsti visus duomenis, gali būti naudinga pirmiausia teikti pirmenybę svarbiausiems elementams, jei jus aptiks ir bus pagerintos gynybos, arba jei reikės įsigyti daugiau diskų, arba tiesiog dėl to, kad kažkas kitas atsiranda jūsų gyvenime, kol dar negalite atsisiųsti visko."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Pavyzdžiui, kolekcijoje gali būti keli to paties pagrindinio šaltinio (pvz., knygos ar filmo) leidimai, kur vienas pažymėtas kaip geriausios kokybės. Pirmiausia išsaugoti tuos leidimus būtų labai prasminga. Galiausiai galite norėti išsaugoti visus leidimus, nes kai kuriais atvejais metaduomenys gali būti neteisingai pažymėti arba gali būti nežinomų kompromisų tarp leidimų (pavyzdžiui, „geriausias leidimas“ gali būti geriausias daugeliu atžvilgių, bet blogesnis kitais, pvz., filmas turi didesnę raišką, bet trūksta subtitrų)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Taip pat galite ieškoti savo metaduomenų bazėje, kad rastumėte įdomių dalykų. Koks yra didžiausias failas, kuris yra talpinamas, ir kodėl jis toks didelis? Koks yra mažiausias failas? Ar yra įdomių ar netikėtų modelių tam tikrose kategorijose, kalbose ir pan.? Ar yra dublikatų ar labai panašių pavadinimų? Ar yra modelių, kada duomenys buvo pridėti, pavyzdžiui, viena diena, kai buvo pridėta daug failų vienu metu? Dažnai galite daug sužinoti, žiūrėdami į duomenų rinkinį skirtingais būdais."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Mūsų atveju, mes pašalinome Z-Library knygų dublikatus, palygindami su md5 maišos kodais Library Genesis, taip sutaupydami daug atsisiuntimo laiko ir disko vietos. Tačiau tai yra gana unikali situacija. Daugeliu atvejų nėra išsamių duomenų bazių, kuriose būtų nurodyta, kurie failai jau yra tinkamai išsaugoti kitų piratų. Tai savaime yra didžiulė galimybė kažkam ten. Būtų puiku turėti reguliariai atnaujinamą apžvalgą apie tokius dalykus kaip muzika ir filmai, kurie jau plačiai sėjami torrent svetainėse, todėl jie yra mažesnės svarbos įtraukti į piratų veidrodžius."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Duomenų rinkimas"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Dabar esate pasiruošę iš tikrųjų atsisiųsti duomenis dideliais kiekiais. Kaip minėta anksčiau, šiuo metu jau turėtumėte rankiniu būdu atsisiųsti daugybę failų, kad geriau suprastumėte tikslinio objekto elgesį ir apribojimus. Tačiau vis tiek bus staigmenų, kai iš tikrųjų pradėsite atsisiųsti daug failų vienu metu."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Mūsų patarimas čia yra laikytis paprastumo. Pradėkite tiesiog atsisiųsdami daugybę failų. Galite naudoti Python, o tada išplėsti iki kelių gijų. Tačiau kartais dar paprasčiau yra generuoti Bash failus tiesiai iš duomenų bazės ir tada paleisti kelis iš jų keliuose terminalo languose, kad padidintumėte mastą. Greitas techninis triukas, kurį verta paminėti čia, yra OUTFILE naudojimas MySQL, kurį galite rašyti bet kur, jei išjungiate \"secure_file_priv\" mysqld.cnf (ir būtinai išjunkite/apeikite AppArmor, jei naudojate Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Mes saugome duomenis paprastuose kietuosiuose diskuose. Pradėkite nuo to, ką turite, ir lėtai plėskite. Gali būti sunku galvoti apie šimtų TB duomenų saugojimą. Jei tai yra situacija, su kuria susiduriate, tiesiog išleiskite gerą dalį pirmiausia ir savo pranešime paprašykite pagalbos saugant likusią dalį. Jei norite patys įsigyti daugiau kietųjų diskų, r/DataHoarder turi gerų išteklių, kaip gauti gerus pasiūlymus."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Stenkitės per daug nesijaudinti dėl sudėtingų failų sistemų. Lengva įkristi į triušio urvą, nustatant tokius dalykus kaip ZFS. Viena techninė detalė, į kurią reikia atkreipti dėmesį, yra ta, kad daugelis failų sistemų blogai tvarko daugybę failų. Mes radome paprastą sprendimą - sukurti kelis katalogus, pvz., skirtingiems ID diapazonams ar maišos prefiksams."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Atsisiuntę duomenis, būtinai patikrinkite failų vientisumą naudodami metaduomenyse esančius maišos kodus, jei jie yra."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Paskirstymas"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Jūs turite duomenis, taip suteikdami jums pasaulio pirmąjį piratų veidrodį jūsų tikslui (greičiausiai). Daugeliu atžvilgių sunkiausia dalis jau baigta, bet rizikingiausia dalis dar priešakyje. Juk iki šiol buvote nepastebimi; skraidėte po radarais. Viskas, ką turėjote padaryti, buvo naudoti gerą VPN visą laiką, neįvedant savo asmeninių duomenų jokiose formose (akivaizdu), ir galbūt naudojant specialią naršyklės sesiją (ar net kitą kompiuterį)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Dabar turite paskirstyti duomenis. Mūsų atveju pirmiausia norėjome grąžinti knygas į Library Genesis, bet greitai atradome sunkumus tame (grožinės ir negrožinės literatūros rūšiavimas). Taigi nusprendėme paskirstyti naudojant Library Genesis stiliaus torrentus. Jei turite galimybę prisidėti prie esamo projekto, tai gali sutaupyti daug laiko. Tačiau šiuo metu nėra daug gerai organizuotų piratų veidrodžių."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Tarkime, nusprendžiate patys paskirstyti torrentus. Stenkitės, kad tie failai būtų maži, kad juos būtų lengva atkartoti kitose svetainėse. Tada turėsite patys sėti torrentus, vis dar išlikdami anonimiški. Galite naudoti VPN (su arba be prievado persiuntimo) arba mokėti su maišytais Bitcoin už Seedbox. Jei nežinote, ką reiškia kai kurie iš šių terminų, turėsite daug skaityti, nes svarbu suprasti rizikos kompromisus čia."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Galite talpinti pačius torrent failus esamose torrent svetainėse. Mūsų atveju, mes pasirinkome iš tikrųjų talpinti svetainę, nes taip pat norėjome aiškiai skleisti savo filosofiją. Galite tai padaryti patys panašiu būdu (mes naudojame Njalla savo domenams ir talpinimui, mokėdami su maišytais Bitcoin), bet taip pat drąsiai susisiekite su mumis, kad mes talpintume jūsų torrentus. Mes siekiame sukurti išsamų piratų veidrodžių indeksą laikui bėgant, jei ši idėja prigis."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Kalbant apie VPN pasirinkimą, apie tai jau daug rašyta, todėl tiesiog pakartosime bendrą patarimą rinktis pagal reputaciją. Tikros teismo patikrintos bežurnalinės politikos su ilga privatumo apsaugos istorija, mūsų nuomone, yra mažiausios rizikos pasirinkimas. Atkreipkite dėmesį, kad net jei viską darote teisingai, niekada negalite pasiekti nulio rizikos. Pavyzdžiui, kai dalijatės savo torrentais, labai motyvuotas valstybės veikėjas gali tikriausiai stebėti įeinančius ir išeinančius duomenų srautus VPN serveriuose ir išsiaiškinti, kas jūs esate. Arba galite tiesiog padaryti klaidą. Mes tikriausiai jau padarėme ir vėl padarysime. Laimei, valstybės nelabai rūpinasi <em>piratavimu</em>."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Kiekvienam projektui reikia priimti sprendimą, ar jį publikuoti naudojant tą pačią tapatybę kaip anksčiau, ar ne. Jei toliau naudosite tą patį vardą, ankstesnių projektų operacinio saugumo klaidos gali sugrįžti ir pakenkti jums. Tačiau publikuojant skirtingais vardais, jūs nesukuriate ilgalaikės reputacijos. Mes pasirinkome stiprų operacinį saugumą nuo pat pradžių, kad galėtume toliau naudoti tą pačią tapatybę, tačiau nedvejodami publikuosime kitu vardu, jei padarysime klaidą arba jei to reikalauja aplinkybės."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Žodžio skleidimas gali būti sudėtingas. Kaip sakėme, tai vis dar nišinė bendruomenė. Iš pradžių paskelbėme Reddit, bet tikrai sulaukėme dėmesio Hacker News. Šiuo metu rekomenduojame paskelbti keliose vietose ir pažiūrėti, kas nutiks. Ir vėlgi, susisiekite su mumis. Norėtume skleisti žinią apie daugiau piratų archyvavimo pastangų."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Išvada"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Tikimės, kad tai bus naudinga naujai pradedantiems piratų archyvarams. Mes džiaugiamės galėdami jus pasveikinti šiame pasaulyje, todėl nedvejodami susisiekite. Išsaugokime kuo daugiau pasaulio žinių ir kultūros, ir atkartokime jas plačiai."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Ana ir komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Pristatome Piratų Bibliotekos Veidrodį: Išsaugome 7TB knygų (kurios nėra Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Šis projektas (REDAGUOTA: perkeltas į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>) siekia prisidėti prie žmonijos žinių išsaugojimo ir išlaisvinimo. Mes darome savo mažą ir kuklų indėlį, sekdami didžiųjų pėdomis."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Šio projekto dėmesys iliustruojamas jo pavadinimu:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piratų</strong> - Mes sąmoningai pažeidžiame autorių teisių įstatymus daugelyje šalių. Tai leidžia mums daryti tai, ko teisėtos organizacijos negali: užtikrinti, kad knygos būtų atkartotos plačiai."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteka</strong> - Kaip ir dauguma bibliotekų, mes daugiausia dėmesio skiriame rašytinei medžiagai, pavyzdžiui, knygoms. Ateityje galime plėstis į kitų tipų medijas."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Veidrodis</strong> - Mes esame griežtai esamų bibliotekų veidrodis. Mes orientuojamės į išsaugojimą, o ne į knygų paieškos ir atsisiuntimo (prieigos) palengvinimą ar didelės bendruomenės, kuri prisideda naujomis knygomis (šaltiniais), kūrimą."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Pirmoji biblioteka, kurią atkartojome, yra Z-Library. Tai populiari (ir neteisėta) biblioteka. Jie paėmė Library Genesis kolekciją ir padarė ją lengvai ieškomą. Be to, jie tapo labai veiksmingi pritraukiant naujas knygų įnašas, skatindami prisidedančius vartotojus įvairiomis privilegijomis. Šiuo metu jie negrąžina šių naujų knygų Library Genesis. Ir skirtingai nei Library Genesis, jie nepadaro savo kolekcijos lengvai atkartojamos, kas trukdo plačiam išsaugojimui. Tai svarbu jų verslo modeliui, nes jie ima mokestį už prieigą prie savo kolekcijos dideliais kiekiais (daugiau nei 10 knygų per dieną)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Mes nedarome moralinių sprendimų dėl mokesčio už didelės apimties prieigą prie neteisėtos knygų kolekcijos. Neabejotina, kad Z-Library sėkmingai išplėtė prieigą prie žinių ir pritraukė daugiau knygų. Mes tiesiog esame čia, kad atliktume savo dalį: užtikrinti ilgalaikį šios privačios kolekcijos išsaugojimą."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Norėtume pakviesti jus padėti išsaugoti ir išlaisvinti žmonijos žinias atsisiunčiant ir dalijantis mūsų torrentais. Daugiau informacijos apie tai, kaip duomenys yra organizuoti, rasite projekto puslapyje."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Taip pat labai norėtume pakviesti jus prisidėti savo idėjomis, kokias kolekcijas atkartoti toliau ir kaip tai padaryti. Kartu galime pasiekti daug. Tai tik mažas indėlis tarp daugybės kitų. Ačiū už viską, ką darote."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Ana ir komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Mes nesusiejame failų iš šio tinklaraščio. Prašome rasti juos patys.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb išmetimas, arba Kiek Knygų Yra Išsaugota Amžinai?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Jei tinkamai pašalintume šešėlinių bibliotekų failų dublikatus, kokį procentą visų pasaulio knygų mes išsaugojome?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Su Piratų bibliotekos veidrodžiu (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>), mūsų tikslas yra surinkti visas pasaulio knygas ir išsaugoti jas amžinai.<sup>1</sup> Tarp mūsų Z-Library torrentų ir originalių Library Genesis torrentų turime 11 783 153 failus. Bet kiek tai iš tikrųjų yra? Jei tinkamai pašalintume dublikatus, kokį procentą visų pasaulio knygų mes išsaugojome? Mes tikrai norėtume turėti kažką panašaus:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of žmonijos rašytinio paveldo išsaugota amžinai"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Norint apskaičiuoti procentą, mums reikia vardiklio: bendro kada nors išleistų knygų skaičiaus.<sup>2</sup> Prieš Google Books žlugimą, vienas iš projekto inžinierių, Leonidas Taycheris, <a %(booksearch_blogspot)s>bandė įvertinti</a> šį skaičių. Jis pateikė — su šypsena — 129 864 880 („bent jau iki sekmadienio“). Jis įvertino šį skaičių, sukūręs vieningą visų pasaulio knygų duomenų bazę. Tam jis sujungė skirtingus duomenų rinkinius ir sujungė juos įvairiais būdais."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Trumpai tariant, yra dar vienas asmuo, bandęs kataloguoti visas pasaulio knygas: Aaronas Swartz, velionis skaitmeninis aktyvistas ir Reddit bendraįkūrėjas.<sup>3</sup> Jis <a %(youtube)s>pradėjo Open Library</a> su tikslu „vienas tinklalapis kiekvienai kada nors išleistai knygai“, sujungdamas duomenis iš daugybės skirtingų šaltinių. Jis sumokėjo aukščiausią kainą už savo skaitmeninio išsaugojimo darbą, kai buvo patrauktas baudžiamojon atsakomybėn už masinį akademinių straipsnių atsisiuntimą, kas lėmė jo savižudybę. Be abejo, tai yra viena iš priežasčių, kodėl mūsų grupė yra pseudoniminė ir kodėl mes esame labai atsargūs. Open Library vis dar herojiškai valdo Internet Archive žmonės, tęsdami Aarono palikimą. Prie to grįšime vėliau šiame įraše."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Google tinklaraščio įraše Taycheris aprašo kai kuriuos iššūkius, susijusius su šio skaičiaus įvertinimu. Pirma, kas sudaro knygą? Yra keletas galimų apibrėžimų:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fizinės kopijos.</strong> Akivaizdu, kad tai nėra labai naudinga, nes tai tik tos pačios medžiagos dublikatai. Būtų šaunu, jei galėtume išsaugoti visas žmonių pastabas knygose, kaip Fermato garsiosios „rašliavos paraštėse“. Bet deja, tai liks archyvaro svajone."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Kūriniai“.</strong> Pavyzdžiui, „Haris Poteris ir paslapčių kambarys“ kaip loginė sąvoka, apimanti visas jo versijas, kaip skirtingi vertimai ir perleidimai. Tai yra tam tikras naudingas apibrėžimas, bet gali būti sunku nubrėžti ribą, kas skaičiuojama. Pavyzdžiui, mes tikriausiai norime išsaugoti skirtingus vertimus, nors perleidimai su tik nedideliais skirtumais gali būti ne tokie svarbūs."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Leidiniai“.</strong> Čia skaičiuojate kiekvieną unikalią knygos versiją. Jei kas nors apie ją skiriasi, kaip skirtingas viršelis ar skirtinga įžanga, tai skaičiuojama kaip skirtingas leidinys."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Failai.</strong> Dirbant su šešėlinėmis bibliotekomis, tokiomis kaip Library Genesis, Sci-Hub ar Z-Library, yra papildomas aspektas. Gali būti keli to paties leidinio skenavimai. Ir žmonės gali sukurti geresnes esamų failų versijas, nuskaitydami tekstą naudojant OCR arba taisydami puslapius, kurie buvo nuskaityti kampu. Mes norime skaičiuoti šiuos failus kaip vieną leidinį, kas reikalautų geros metadata arba dublikavimo pašalinimo naudojant dokumentų panašumo matavimus."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Leidiniai“ atrodo praktiškiausias apibrėžimas, kas yra „knygos“. Patogiai, šis apibrėžimas taip pat naudojamas unikaliems ISBN numeriams priskirti. ISBN, arba Tarptautinis standartinis knygos numeris, dažnai naudojamas tarptautinėje prekyboje, nes jis integruotas su tarptautine brūkšninių kodų sistema („Tarptautinis straipsnio numeris“). Jei norite parduoti knygą parduotuvėse, jai reikia brūkšninio kodo, todėl gaunate ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycherio tinklaraščio įraše minėta, kad nors ISBN yra naudingi, jie nėra universalūs, nes jie buvo tikrai priimti tik septintojo dešimtmečio viduryje ir ne visur pasaulyje. Vis dėlto, ISBN tikriausiai yra plačiausiai naudojamas knygų leidinių identifikatorius, todėl tai yra geriausias mūsų pradžios taškas. Jei galime rasti visus pasaulio ISBN, gauname naudingą sąrašą, kurios knygos dar turi būti išsaugotos."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Taigi, kur gauti duomenis? Yra keletas esamų pastangų, kurios bando sudaryti visų pasaulio knygų sąrašą:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Juk jie atliko šį tyrimą Google Books. Tačiau jų metadata nėra prieinama masiškai ir gana sunkiai nuskaityti."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Kaip minėta anksčiau, tai yra jų visa misija. Jie surinko didžiulius bibliotekų duomenų kiekius iš bendradarbiaujančių bibliotekų ir nacionalinių archyvų ir toliau tai daro. Jie taip pat turi savanorius bibliotekininkus ir techninę komandą, kurie bando pašalinti dublikatus ir pažymėti juos visokiomis metadata. Geriausia, kad jų duomenų rinkinys yra visiškai atviras. Galite tiesiog <a %(openlibrary)s>atsisiųsti jį</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Tai yra svetainė, kurią valdo ne pelno siekianti OCLC, kuri parduoda bibliotekų valdymo sistemas. Jie renka knygų metadata iš daugybės bibliotekų ir pateikia ją per WorldCat svetainę. Tačiau jie taip pat uždirba pinigus parduodami šiuos duomenis, todėl jie nėra prieinami masiškai atsisiųsti. Jie turi keletą ribotų masinių duomenų rinkinių, kuriuos galima atsisiųsti, bendradarbiaujant su konkrečiomis bibliotekomis."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Tai yra šio tinklaraščio įrašo tema. ISBNdb renka įvairių svetainių knygų metadata, ypač kainų duomenis, kuriuos jie tada parduoda knygų pardavėjams, kad jie galėtų nustatyti savo knygų kainas pagal likusią rinką. Kadangi ISBN šiais laikais yra gana universalūs, jie efektyviai sukūrė „tinklalapį kiekvienai knygai“."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Įvairios individualios bibliotekų sistemos ir archyvai.</strong> Yra bibliotekų ir archyvų, kurie nebuvo indeksuoti ir sujungti nė vieno iš aukščiau paminėtų, dažnai todėl, kad jie yra nepakankamai finansuojami arba dėl kitų priežasčių nenori dalintis savo duomenimis su Open Library, OCLC, Google ir t. t. Dauguma jų turi skaitmeninius įrašus, prieinamus per internetą, ir jie dažnai nėra labai gerai apsaugoti, todėl jei norite padėti ir smagiai praleisti laiką mokydamiesi apie keistas bibliotekų sistemas, tai yra puikios pradžios taškai."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Šiame įraše džiaugiamės galėdami pranešti apie nedidelį leidimą (palyginti su ankstesniais Z-Library leidimais). Mes nuskaityme didžiąją dalį ISBNdb ir padarėme duomenis prieinamus torrentams Piratų bibliotekos veidrodžio svetainėje (REDAGUOTA: perkelta į <a %(wikipedia_annas_archive)s>Anos Archyvą</a>; mes čia tiesiogiai nesusiesime, tiesiog ieškokite). Tai yra apie 30,9 milijono įrašų (20 GB kaip <a %(jsonlines)s>JSON Lines</a>; 4,4 GB suspausta). Jų svetainėje jie teigia, kad iš tikrųjų turi 32,6 milijono įrašų, todėl mes galbūt kažkaip praleidome kai kuriuos, arba <em>jie</em> gali daryti kažką neteisingai. Bet kokiu atveju, kol kas mes neatskleisime, kaip tai padarėme — paliksime tai kaip užduotį skaitytojui. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Ką mes pasidalinsime, tai preliminari analizė, siekiant priartėti prie pasaulio knygų skaičiaus įvertinimo. Mes peržiūrėjome tris duomenų rinkinius: šį naują ISBNdb duomenų rinkinį, mūsų originalų metadata leidimą, kurį nuskaityme iš Z-Library šešėlinės bibliotekos (kuri apima Library Genesis), ir Open Library duomenų iškrovą."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Pradėkime nuo apytikslių skaičių:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Tiek Z-Library/Libgen, tiek Open Library yra daug daugiau knygų nei unikalių ISBN. Ar tai reiškia, kad dauguma tų knygų neturi ISBN, ar tiesiog trūksta ISBN metadata? Tikriausiai galime atsakyti į šį klausimą, derindami automatinį atitikimą pagal kitus atributus (pavadinimą, autorių, leidėją ir kt.), įtraukdami daugiau duomenų šaltinių ir ištraukdami ISBN iš pačių knygų skenų (Z-Library/Libgen atveju)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Kiek iš tų ISBN yra unikalūs? Tai geriausiai iliustruoja Veno diagrama:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Tiksliau tariant:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Mus nustebino, kaip mažai yra persidengimo! ISBNdb turi didžiulį kiekį ISBN, kurie neatsiranda nei Z-Library, nei Open Library, ir tas pats galioja (nors ir mažesniu, bet vis tiek reikšmingu mastu) kitoms dviem. Tai kelia daug naujų klausimų. Kiek automatinis atitikimas padėtų pažymėti knygas, kurios nebuvo pažymėtos ISBN? Ar būtų daug atitikimų ir todėl padidėtų persidengimas? Taip pat, kas nutiktų, jei įtrauktume 4-ą ar 5-ą duomenų rinkinį? Kiek persidengimo matytume tada?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Tai suteikia mums pradžios tašką. Dabar galime pažvelgti į visus ISBN, kurie nebuvo Z-Library duomenų rinkinyje ir kurie neatitinka pavadinimo/autoriaus laukų. Tai gali padėti išsaugoti visas pasaulio knygas: pirmiausia skenuojant internetą dėl skenų, tada realiame gyvenime skenuojant knygas. Pastarasis netgi galėtų būti finansuojamas minios, arba skatinamas „premijomis“ iš žmonių, kurie norėtų matyti tam tikras knygas skaitmenizuotas. Visa tai yra istorija kitam kartui."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Jei norite padėti su bet kuriuo iš šių dalykų — tolesne analize; daugiau metadata rinkimu; daugiau knygų paieška; knygų OCR’inimu; tai daryti kitose srityse (pvz., straipsniai, audioknygos, filmai, TV laidos, žurnalai) ar net dalį šių duomenų padaryti prieinamus tokiems dalykams kaip ML / didelių kalbos modelių mokymas — prašome susisiekti su manimi (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Jei jus ypač domina duomenų analizė, mes dirbame, kad mūsų duomenų rinkiniai ir skriptai būtų prieinami lengviau naudojamu formatu. Būtų puiku, jei galėtumėte tiesiog nukopijuoti užrašų knygelę ir pradėti su tuo žaisti."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Galiausiai, jei norite paremti šį darbą, apsvarstykite galimybę paaukoti. Tai yra visiškai savanoriška operacija, ir jūsų indėlis daro didelį skirtumą. Kiekviena dalis padeda. Šiuo metu priimame aukas kriptovaliuta; žr. Anna’s Archive aukojimo puslapį."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Ana ir komanda (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pagal tam tikrą pagrįstą „amžinai“ apibrėžimą. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Žinoma, žmonijos rašytinis paveldas yra daug daugiau nei knygos, ypač šiais laikais. Šio įrašo ir mūsų nesenų leidimų dėlei mes sutelkiame dėmesį į knygas, bet mūsų interesai siekia toliau."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Apie Aaroną Swartzą galima pasakyti daug daugiau, bet mes norėjome jį trumpai paminėti, nes jis vaidina svarbų vaidmenį šioje istorijoje. Laikui bėgant, daugiau žmonių gali pirmą kartą susidurti su jo vardu ir vėliau patys pasinerti į šią temą."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Kritinis šešėlinių bibliotekų langas"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Kaip galime teigti, kad išsaugosime savo kolekcijas amžinai, kai jos jau artėja prie 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Kinų versija 中文版</a>, diskutuokite <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Anna’s Archive dažnai klausiama, kaip galime teigti, kad išsaugosime savo kolekcijas amžinai, kai bendras dydis jau artėja prie 1 petabaito (1000 TB) ir vis dar auga. Šiame straipsnyje pažvelgsime į mūsų filosofiją ir pamatysime, kodėl kitas dešimtmetis yra kritinis mūsų misijai išsaugoti žmonijos žinias ir kultūrą."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Mūsų kolekcijų <a %(annas_archive_stats)s>bendras dydis</a> per pastaruosius kelis mėnesius, suskirstytas pagal torrent sėjėjų skaičių."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritetai"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Kodėl mums taip rūpi straipsniai ir knygos? Atsisakykime mūsų pagrindinio tikėjimo išsaugojimu apskritai — galbūt apie tai parašysime kitą įrašą. Taigi kodėl būtent straipsniai ir knygos? Atsakymas paprastas: <strong>informacijos tankis</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Vienam megabaitui saugyklos, rašytinis tekstas saugo daugiausiai informacijos iš visų medijų. Nors mums rūpi tiek žinios, tiek kultūra, labiau rūpinamės pirmąja. Apskritai, randame informacijos tankio ir išsaugojimo svarbos hierarchiją, kuri atrodo maždaug taip:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademiniai straipsniai, žurnalai, ataskaitos"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organiniai duomenys, tokie kaip DNR sekos, augalų sėklos ar mikrobų mėginiai"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Ne grožinės literatūros knygos"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Mokslo ir inžinerijos programinės įrangos kodas"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Matavimo duomenys, tokie kaip moksliniai matavimai, ekonominiai duomenys, įmonių ataskaitos"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Mokslo ir inžinerijos svetainės, internetinės diskusijos"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Ne grožinės literatūros žurnalai, laikraščiai, vadovai"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Ne grožinės literatūros pokalbių, dokumentinių filmų, tinklalaidžių transkripcijos"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Vidiniai duomenys iš įmonių ar vyriausybių (nutekėjimai)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metaduomenų įrašai apskritai (ne grožinės ir grožinės literatūros; kitų medijų, meno, žmonių ir kt.; įskaitant apžvalgas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografiniai duomenys (pvz., žemėlapiai, geologiniai tyrimai)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Teisinių ar teismo procesų transkripcijos"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Visų aukščiau paminėtų dalykų grožinės ar pramoginės versijos"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Šio sąrašo reitingas yra šiek tiek savavališkas — kai kurie elementai yra lygūs arba mūsų komandoje yra nesutarimų — ir tikriausiai pamirštame kai kurias svarbias kategorijas. Tačiau tai yra maždaug taip, kaip mes teikiame prioritetus."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Kai kurie iš šių elementų yra per daug skirtingi nuo kitų, kad mums reikėtų dėl jų nerimauti (arba jais jau rūpinasi kitos institucijos), pavyzdžiui, organiniai duomenys ar geografiniai duomenys. Tačiau dauguma šio sąrašo elementų mums iš tikrųjų yra svarbūs."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Kitas didelis veiksnys mūsų prioritetizacijoje yra tai, kiek tam tikras darbas yra rizikingas. Mes teikiame pirmenybę darbams, kurie yra:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Retas"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unikaliai nepakankamai dėmesio sulaukęs"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unikaliai sunaikinimo rizikoje (pvz., dėl karo, finansavimo mažinimo, teisminių procesų ar politinio persekiojimo)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Galiausiai, mums rūpi mastas. Turime ribotą laiką ir pinigų, todėl verčiau praleistume mėnesį išsaugodami 10 000 knygų nei 1 000 knygų — jei jos yra vienodai vertingos ir rizikingos."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Šešėlinės bibliotekos"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Yra daug organizacijų, turinčių panašias misijas ir prioritetus. Iš tiesų, yra bibliotekų, archyvų, laboratorijų, muziejų ir kitų institucijų, kurioms pavesta tokio pobūdžio išsaugojimo užduotis. Daugelis jų yra gerai finansuojamos vyriausybių, asmenų ar korporacijų. Tačiau jos turi vieną didelį akląją zoną: teisinę sistemą."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Čia slypi unikalus šešėlinių bibliotekų vaidmuo ir priežastis, kodėl egzistuoja Anos Archyvas. Mes galime daryti tai, ko kitos institucijos negali. Dabar, tai nėra (dažnai) taip, kad galime archyvuoti medžiagas, kurios kitur yra neteisėtos išsaugoti. Ne, daugelyje vietų yra teisėta kurti archyvą su bet kokiomis knygomis, straipsniais, žurnalais ir panašiai."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Tačiau tai, ko dažnai trūksta teisėtiems archyvams, yra <strong>redundancija ir ilgaamžiškumas</strong>. Yra knygų, kurių egzistuoja tik vienas egzempliorius kažkurioje fizinėje bibliotekoje. Yra metaduomenų įrašų, saugomų vienos korporacijos. Yra laikraščių, išsaugotų tik mikrofilmuose viename archyve. Bibliotekos gali patirti finansavimo sumažinimus, korporacijos gali bankrutuoti, archyvai gali būti bombarduojami ir sudeginti iki pamatų. Tai nėra hipotetinis dalykas — tai vyksta nuolat."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Tai, ką mes galime unikalai daryti Anos Archyve, yra saugoti daugybę kūrinių kopijų, dideliu mastu. Mes galime rinkti straipsnius, knygas, žurnalus ir daugiau, ir platinti juos dideliais kiekiais. Šiuo metu tai darome per torrentus, bet tikslios technologijos nesvarbios ir laikui bėgant keisis. Svarbiausia yra gauti daug kopijų, paskirstytų visame pasaulyje. Ši citata iš daugiau nei 200 metų senumo vis dar skamba tiesa:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Prarasto negalima atgauti; bet išsaugokime tai, kas liko: ne seifais ir spynomis, kurios atskiria juos nuo visuomenės akių ir naudojimo, pasmerkdamos juos laiko švaistymui, bet tokiu kopijų dauginimu, kuris padės juos už nelaimės ribų.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Trumpa pastaba apie viešąją sritį. Kadangi Anos Archyvas unikalai orientuojasi į veiklas, kurios daugelyje pasaulio vietų yra neteisėtos, mes nesivarginame su plačiai prieinamomis kolekcijomis, tokiomis kaip viešosios srities knygos. Teisinės institucijos dažnai jau gerai rūpinasi tuo. Tačiau yra aplinkybių, kurios kartais verčia mus dirbti su viešai prieinamomis kolekcijomis:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metaduomenų įrašus galima laisvai peržiūrėti Worldcat svetainėje, bet jų negalima atsisiųsti dideliais kiekiais (kol mes jų <a %(worldcat_scrape)s>neperėmėme</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kodas gali būti atviro kodo Github, bet Github kaip visuma negali būti lengvai atkuriama ir taip išsaugoma (nors šiuo konkrečiu atveju yra pakankamai paskirstytų daugumos kodo saugyklų kopijų)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit yra nemokamas naudoti, bet neseniai įvedė griežtas anti-scraping priemones, dėl duomenų alkanų LLM mokymų (apie tai daugiau vėliau)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Kopijų dauginimas"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Grįžtant prie mūsų pradinio klausimo: kaip mes galime teigti, kad išsaugosime savo kolekcijas amžinai? Pagrindinė problema čia yra ta, kad mūsų kolekcija <a %(torrents_stats)s>auga</a> sparčiai, perimant ir atveriant kai kurias didžiules kolekcijas (be jau nuostabaus darbo, kurį atliko kitos atviro duomenų šešėlinės bibliotekos, tokios kaip Sci-Hub ir Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Šis duomenų augimas apsunkina kolekcijų atkūrimą visame pasaulyje. Duomenų saugojimas yra brangus! Bet mes esame optimistiški, ypač stebėdami šias tris tendencijas."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Mes nuskynėme lengvai pasiekiamus vaisius"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Tai tiesiogiai seka iš mūsų aukščiau aptartų prioritetų. Mes teikiame pirmenybę didelių kolekcijų išlaisvinimui pirmiausia. Dabar, kai užsitikrinome kai kurias didžiausias pasaulio kolekcijas, tikimės, kad mūsų augimas bus daug lėtesnis."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Vis dar yra ilga mažesnių kolekcijų uodega, ir naujos knygos skenuojamos ar leidžiamos kasdien, bet greitis greičiausiai bus daug lėtesnis. Mes galime vis dar padvigubėti ar net patrigubėti, bet per ilgesnį laikotarpį."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Saugojimo išlaidos toliau eksponentiškai mažėja"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Rašymo metu, <a %(diskprices)s>diskų kainos</a> už TB yra apie 12 USD už naujus diskus, 8 USD už naudotus diskus ir 4 USD už juostą. Jei esame konservatyvūs ir žiūrime tik į naujus diskus, tai reiškia, kad petabaito saugojimas kainuoja apie 12 000 USD. Jei manome, kad mūsų biblioteka patrigubės nuo 900 TB iki 2,7 PB, tai reikštų 32 400 USD, kad atkurtume visą mūsų biblioteką. Pridėjus elektros, kitų aparatūros išlaidų ir panašiai, suapvalinkime iki 40 000 USD. Arba su juosta labiau kaip 15 000–20 000 USD."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Viena vertus, <strong>15 000–40 000 USD už visos žmonijos žinias yra puikus sandoris</strong>. Kita vertus, šiek tiek brangu tikėtis daugybės pilnų kopijų, ypač jei norėtume, kad tie žmonės taip pat toliau sėtų savo torrentus kitų naudai."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Tai yra šiandien. Bet progresas žengia į priekį:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Kietųjų diskų kainos už TB per pastaruosius 10 metų buvo maždaug sumažintos trečdaliu ir tikėtina, kad toliau mažės panašiu tempu. Juostos atrodo einančios panašia trajektorija. SSD kainos krenta dar greičiau ir gali pralenkti HDD kainas iki dešimtmečio pabaigos."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD kainų tendencijos iš skirtingų šaltinių (spustelėkite, kad peržiūrėtumėte tyrimą)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Jei tai pasitvirtins, po 10 metų galime tikėtis, kad mūsų visos kolekcijos atkūrimas (1/3) kainuos tik 5 000–13 000 USD arba net mažiau, jei mūsų kolekcija mažiau augs. Nors tai vis dar daug pinigų, tai bus pasiekiama daugeliui žmonių. Ir tai gali būti dar geriau dėl kito punkto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Informacijos tankio patobulinimai"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Šiuo metu saugome knygas tokiuose formatuose, kokius jie mums pateikiami. Žinoma, jie yra suspausti, bet dažnai tai vis dar yra dideli puslapių skenavimai ar nuotraukos."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Iki šiol vienintelės galimybės sumažinti mūsų kolekcijos bendrą dydį buvo per agresyvesnį suspaudimą arba deduplikaciją. Tačiau norint gauti pakankamai reikšmingų sutaupymų, abu būdai yra per daug prarandantys kokybę mūsų skoniui. Stiprus nuotraukų suspaudimas gali padaryti tekstą vos įskaitomą. O deduplikacijai reikia didelio pasitikėjimo, kad knygos yra visiškai vienodos, kas dažnai yra per daug netikslu, ypač jei turinys yra tas pats, bet skenavimai atlikti skirtingomis progomis."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Visada buvo trečias variantas, bet jo kokybė buvo tokia prasta, kad niekada jo nesvarstėme: <strong>OCR, arba optinis simbolių atpažinimas</strong>. Tai yra procesas, kai nuotraukos paverčiamos paprastu tekstu, naudojant AI, kad atpažintų simbolius nuotraukose. Įrankiai tam jau seniai egzistuoja ir yra gana geri, bet „gana geri“ nėra pakankamai gerai išsaugojimo tikslais."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tačiau naujausi daugiarūšiai giluminio mokymosi modeliai padarė itin greitą pažangą, nors vis dar už didelę kainą. Tikimės, kad tiek tikslumas, tiek kainos dramatiškai pagerės artimiausiais metais, iki taško, kai tai taps realistiška taikyti visai mūsų bibliotekai."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR patobulinimai."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Kai tai įvyks, mes tikriausiai vis dar išsaugosime originalius failus, bet papildomai galėtume turėti daug mažesnę mūsų bibliotekos versiją, kurią dauguma žmonių norės atkartoti. Esmė ta, kad pats neapdorotas tekstas suspaudžiamas dar geriau ir yra daug lengviau deduplikuojamas, suteikiant mums dar daugiau sutaupymų."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Apskritai, nėra nerealistiška tikėtis bent 5-10 kartų sumažinti bendrą failų dydį, galbūt net daugiau. Net ir su konservatyviu 5 kartų sumažinimu, po 10 metų mes žiūrėtume į <strong>1 000–3 000 USD, net jei mūsų biblioteka padidėtų tris kartus</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritinis langas"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Jei šios prognozės yra tikslios, mums <strong>reikia tik palaukti keletą metų</strong>, kol visa mūsų kolekcija bus plačiai atkartota. Taigi, kaip sakė Thomas Jefferson, „padėta už nelaimės ribų“."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Deja, LLM atsiradimas ir jų duomenų alkani mokymai privertė daugelį autorių teisių turėtojų gintis. Dar labiau nei jie jau buvo. Daugelis svetainių daro sunkiau nuskaityti ir archyvuoti, vyksta bylos, o tuo tarpu fizinės bibliotekos ir archyvai toliau yra apleidžiami."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Galime tikėtis, kad šios tendencijos toliau blogės, ir daugelis darbų bus prarasti dar prieš jiems patekus į viešąją sritį."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Esame išsaugojimo revoliucijos išvakarėse, bet <q>prarasto negalima atkurti.</q></strong> Turime kritinį langą apie 5-10 metų, per kurį vis dar gana brangu valdyti šešėlinę biblioteką ir sukurti daug atkartojimų visame pasaulyje, ir per kurį prieiga dar nėra visiškai uždaryta."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Jei galime peržengti šį langą, tada iš tiesų išsaugosime žmonijos žinias ir kultūrą amžinai. Neturėtume leisti, kad šis laikas būtų švaistomas. Neturėtume leisti, kad šis kritinis langas užsidarytų mums."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Pirmyn."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Išskirtinė prieiga LLM įmonėms prie didžiausios pasaulyje kinų negrožinės literatūros knygų kolekcijos."

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Kinų versija 中文版</a>, <a %(news_ycombinator)s>Diskutuoti Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Trumpai:</strong> Annos Archyvas įsigijo unikalią 7,5 milijono / 350TB kinų negrožinės literatūros knygų kolekciją — didesnę nei Library Genesis. Esame pasirengę suteikti LLM įmonei išskirtinę prieigą mainais už aukštos kokybės OCR ir teksto ištraukimą.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Tai trumpas tinklaraščio įrašas. Ieškome įmonės ar institucijos, kuri padėtų mums su OCR ir teksto ištraukimais didžiulei kolekcijai, kurią įsigijome, mainais už išskirtinę ankstyvą prieigą. Po embargo laikotarpio, žinoma, išleisime visą kolekciją."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Aukštos kokybės akademinis tekstas yra labai naudingas LLM mokymui. Nors mūsų kolekcija yra kinų kalba, tai turėtų būti naudinga ir anglų LLM mokymui: modeliai, atrodo, koduoja koncepcijas ir žinias nepriklausomai nuo šaltinio kalbos."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Tam reikia ištraukti tekstą iš skenų. Ką gauna Annos Archyvas? Pilno teksto paiešką knygose savo vartotojams."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Kadangi mūsų tikslai sutampa su LLM kūrėjų tikslais, ieškome bendradarbio. Esame pasirengę suteikti jums <strong>išskirtinę ankstyvą prieigą prie šios kolekcijos dideliais kiekiais 1 metams</strong>, jei galite tinkamai atlikti OCR ir teksto ištraukimą. Jei esate pasirengę pasidalinti visu savo proceso kodu su mumis, mes būtume pasirengę pratęsti kolekcijos embargą."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pavyzdiniai puslapiai"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Norėdami įrodyti, kad turite gerą procesą, pateikiame keletą pavyzdinių puslapių, nuo kurių pradėti, iš knygos apie superlaidininkus. Jūsų procesas turėtų tinkamai apdoroti matematiką, lenteles, diagramas, išnašas ir pan."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Siųskite savo apdorotus puslapius į mūsų el. paštą. Jei jie atrodys gerai, mes atsiųsime jums daugiau privačiai, ir tikimės, kad galėsite greitai paleisti savo procesą ir ant jų. Kai būsime patenkinti, galėsime sudaryti sandorį."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Kolekcija"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Šiek tiek daugiau informacijos apie kolekciją. <a %(duxiu)s>Duxiu</a> yra didžiulė skenuotų knygų duomenų bazė, sukurta <a %(chaoxing)s>SuperStar Digital Library Group</a>. Dauguma jų yra akademinės knygos, skenuotos tam, kad būtų prieinamos skaitmeniniu būdu universitetams ir bibliotekoms. Mūsų anglakalbei auditorijai <a %(library_princeton)s>Princeton</a> ir <a %(guides_lib_uw)s>Vašingtono universitetas</a> turi geras apžvalgas. Taip pat yra puikus straipsnis, suteikiantis daugiau konteksto: <a %(doi)s>„Kinų knygų skaitmeninimas: SuperStar DuXiu Scholar paieškos variklio atvejo analizė“</a> (ieškokite Annos Archyve)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Duxiu knygos jau seniai buvo piratuojamos Kinijos internete. Paprastai jos parduodamos už mažiau nei dolerį perpardavėjų. Jos dažniausiai platinamos naudojant Kinijos „Google Drive“ atitikmenį, kuris dažnai buvo nulaužtas, kad būtų galima saugoti daugiau duomenų. Kai kurios techninės detalės pateikiamos <a %(github_duty_machine)s>čia</a> ir <a %(github_821_github_io)s>čia</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Nors knygos buvo pusiau viešai platinamos, jas gana sunku gauti dideliais kiekiais. Tai buvo aukštai mūsų darbų sąraše, ir tam skyrėme kelis mėnesius pilno darbo laiko. Tačiau neseniai neįtikėtinas, nuostabus ir talentingas savanoris susisiekė su mumis, pranešdamas, kad jau atliko visą šį darbą — didelėmis išlaidomis. Jie pasidalino visa kolekcija su mumis, nesitikėdami nieko mainais, išskyrus ilgalaikio išsaugojimo garantiją. Tikrai nepaprasta. Jie sutiko prašyti pagalbos šiuo būdu, kad kolekcija būtų OCR'inta."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Kolekcija sudaro 7 543 702 failus. Tai daugiau nei Library Genesis negrožinės literatūros (apie 5,3 milijono). Bendras failų dydis yra apie 359TB (326TiB) dabartine forma."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Esame atviri kitiems pasiūlymams ir idėjoms. Tiesiog susisiekite su mumis. Apsilankykite Annos Archyve, kad sužinotumėte daugiau apie mūsų kolekcijas, išsaugojimo pastangas ir kaip galite padėti. Ačiū!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Įspėjimas: šis tinklaraščio įrašas yra pasenęs. Nusprendėme, kad IPFS dar nėra pasirengęs plačiam naudojimui. Vis dar nuorodų į failus IPFS iš Annos Archyvo, kai įmanoma, bet nebepriglobosime jų patys ir nerekomenduojame kitiems atkartoti naudojant IPFS. Prašome peržiūrėti mūsų Torrents puslapį, jei norite padėti išsaugoti mūsų kolekciją."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Padėkite sėti Z-Library IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Kaip valdyti šešėlinę biblioteką: operacijos Annos Archyve"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nėra <q>AWS šešėliniams labdaros projektams,</q> tad kaip mes valdome Annos Archyvą?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Aš valdau <a %(wikipedia_annas_archive)s>Anos Archyvą</a>, didžiausią pasaulyje atvirojo kodo ne pelno siekiantį paieškos variklį <a %(wikipedia_shadow_library)s>šešėliniams bibliotekoms</a>, tokioms kaip Sci-Hub, Library Genesis ir Z-Library. Mūsų tikslas yra padaryti žinias ir kultūrą lengvai prieinamas, o galiausiai sukurti bendruomenę žmonių, kurie kartu archyvuoja ir saugo <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>visas pasaulio knygas</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Šiame straipsnyje parodysiu, kaip mes valdome šią svetainę, ir unikalius iššūkius, kurie kyla valdant svetainę su abejotinu teisiniu statusu, nes nėra „AWS šešėliniams labdaros fondams“."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Taip pat peržiūrėkite sesers straipsnį <a %(blog_how_to_become_a_pirate_archivist)s>Kaip tapti piratų archyvaru</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Inovacijų žetonai"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Pradėkime nuo mūsų technologijų krūvos. Ji yra tyčia nuobodi. Mes naudojame Flask, MariaDB ir ElasticSearch. Tai tiesiogine prasme viskas. Paieška iš esmės yra išspręsta problema, ir mes neketiname jos iš naujo išrasti. Be to, turime išleisti savo <a %(mcfunley)s>inovacijų žetonus</a> kažkam kitam: kad mūsų nepašalintų valdžios institucijos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Taigi, kiek teisėtas ar neteisėtas yra Anos Archyvas? Tai daugiausia priklauso nuo teisinės jurisdikcijos. Dauguma šalių tiki tam tikra autorių teisių forma, o tai reiškia, kad žmonėms ar įmonėms suteikiamas išskirtinis monopolija tam tikrų rūšių kūriniams tam tikrą laikotarpį. Beje, Anos Archyve mes tikime, kad nors yra tam tikra nauda, bendrai autorių teisės yra neigiamas dalykas visuomenei — bet tai jau kita istorija."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Šis išskirtinis monopolija tam tikriems kūriniams reiškia, kad nelegalu bet kam už šio monopolio ribų tiesiogiai platinti tuos kūrinius — įskaitant mus. Tačiau Anos Archyvas yra paieškos variklis, kuris tiesiogiai neplatina tų kūrinių (bent jau ne mūsų aiškioje svetainėje), todėl turėtume būti gerai, tiesa? Ne visai. Daugelyje jurisdikcijų ne tik nelegalu platinti autorių teisių saugomus kūrinius, bet ir nuorodų į vietas, kuriose tai daroma, pateikimas. Klasikinis pavyzdys yra Jungtinių Valstijų DMCA įstatymas."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Tai yra griežčiausias spektro galas. Kitoje spektro pusėje teoriškai galėtų būti šalys, kuriose nėra jokių autorių teisių įstatymų, bet tokios iš tikrųjų neegzistuoja. Beveik kiekviena šalis turi tam tikrą autorių teisių įstatymą. Vykdymas yra kita istorija. Yra daug šalių, kurių vyriausybės nesirūpina autorių teisių įstatymų vykdymu. Taip pat yra šalių, esančių tarp dviejų kraštutinumų, kurios draudžia platinti autorių teisių saugomus kūrinius, bet nedraudžia nuorodų į tokius kūrinius."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Kitas svarstymas yra įmonės lygmenyje. Jei įmonė veikia jurisdikcijoje, kuri nesirūpina autorių teisėmis, bet pati įmonė nenori prisiimti jokios rizikos, tada jie gali uždaryti jūsų svetainę, kai tik kas nors dėl jos skundžiasi."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Galiausiai, didelis svarstymas yra mokėjimai. Kadangi turime likti anonimiški, negalime naudoti tradicinių mokėjimo būdų. Tai palieka mums kriptovaliutas, ir tik nedidelė dalis įmonių jas palaiko (yra virtualių debeto kortelių, apmokamų kriptovaliuta, bet jos dažnai nepriimamos)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistemos architektūra"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Tarkime, kad radote keletą įmonių, kurios nori talpinti jūsų svetainę, nesustabdydamos jūsų — pavadinkime jas „laisvę mylinčiais tiekėjais“ 😄. Greitai pastebėsite, kad viską talpinti su jais yra gana brangu, todėl galbūt norėsite rasti keletą „pigių tiekėjų“ ir ten atlikti tikrąjį talpinimą, per laisvę mylinčius tiekėjus. Jei tai padarysite teisingai, pigūs tiekėjai niekada nesužinos, ką jūs talpinate, ir niekada negaus jokių skundų."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Su visais šiais tiekėjais yra rizika, kad jie vis tiek jus uždarys, todėl jums taip pat reikia atsargumo. Mums to reikia visuose mūsų krūvos lygiuose."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Viena šiek tiek laisvę mylinti įmonė, kuri užėmė įdomią poziciją, yra Cloudflare. Jie <a %(blog_cloudflare)s>teigė</a>, kad jie nėra talpinimo tiekėjas, o paslauga, kaip interneto paslaugų teikėjas. Todėl jie nėra taikomi DMCA ar kitiems pašalinimo prašymams ir perduoda bet kokius prašymus jūsų faktiniam talpinimo tiekėjui. Jie netgi ėjo į teismą, kad apsaugotų šią struktūrą. Todėl mes galime juos naudoti kaip dar vieną talpinimo ir apsaugos sluoksnį."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nepriima anonimiškų mokėjimų, todėl galime naudoti tik jų nemokamą planą. Tai reiškia, kad negalime naudoti jų apkrovos balansavimo ar atsarginio plano funkcijų. Todėl mes <a %(annas_archive_l255)s>įgyvendinome tai patys</a> domeno lygiu. Puslapio įkrovos metu naršyklė patikrina, ar dabartinis domenas vis dar prieinamas, ir jei ne, perrašo visas URL į kitą domeną. Kadangi Cloudflare talpina daugelį puslapių, tai reiškia, kad vartotojas gali patekti į mūsų pagrindinį domeną, net jei tarpinis serveris neveikia, o tada kitame paspaudime būti perkeltas į kitą domeną."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Mes vis dar turime spręsti įprastus veiklos rūpesčius, tokius kaip serverio sveikatos stebėjimas, klaidų registravimas galinėje ir priekinėje dalyje ir pan. Mūsų atsarginė architektūra leidžia didesnį patikimumą ir šioje srityje, pavyzdžiui, paleidžiant visiškai kitą serverių rinkinį viename iš domenų. Mes netgi galime paleisti senesnes kodo ir duomenų rinkinių versijas šiame atskirame domene, jei pagrindinėje versijoje nepastebima kritinė klaida."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Mes taip pat galime apsidrausti nuo Cloudflare pasisukimo prieš mus, pašalindami jį iš vieno iš domenų, pavyzdžiui, šio atskiro domeno. Galimos įvairios šių idėjų permutacijos."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Įrankiai"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Pažvelkime, kokius įrankius naudojame visam tam pasiekti. Tai labai keičiasi, kai susiduriame su naujomis problemomis ir randame naujus sprendimus."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Programų serveris: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy serveris: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serverio valdymas: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Kūrimas: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion statinis talpinimas: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Yra keletas sprendimų, dėl kurių mes svarstėme pirmyn ir atgal. Vienas iš jų yra komunikacija tarp serverių: anksčiau tam naudojome Wireguard, bet pastebėjome, kad kartais jis nustoja perduoti duomenis arba perduoda juos tik viena kryptimi. Tai nutiko su keliais skirtingais Wireguard nustatymais, kuriuos bandėme, tokiais kaip <a %(github_costela_wesher)s>wesher</a> ir <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Taip pat bandėme tuneliuoti prievadus per SSH, naudodami autossh ir sshuttle, bet susidūrėme su <a %(github_sshuttle)s>problemomis ten</a> (nors man vis dar neaišku, ar autossh kenčia nuo TCP-per-TCP problemų ar ne — man tai atrodo kaip netvarkingas sprendimas, bet galbūt jis iš tikrųjų yra tinkamas?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Vietoj to, grįžome prie tiesioginių ryšių tarp serverių, slėpdami, kad serveris veikia pigiuose tiekėjuose, naudodami IP filtravimą su UFW. Tai turi trūkumą, kad Docker neveikia gerai su UFW, nebent naudojate <code>network_mode: \"host\"</code>. Visa tai yra šiek tiek labiau linkę į klaidas, nes su maža konfigūracijos klaida galite atskleisti savo serverį internetui. Galbūt turėtume grįžti prie autossh — čia labai lauktume atsiliepimų."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Mes taip pat svarstėme Varnish prieš Nginx. Šiuo metu mums patinka Varnish, bet jis turi savo keistenybių ir šiurkščių briaunų. Tas pats pasakytina apie Checkmk: mes jo nemylime, bet jis veikia šiuo metu. Weblate buvo gerai, bet ne neįtikėtina — kartais bijau, kad jis praras mano duomenis, kai bandau sinchronizuoti su mūsų git repo. Flask buvo geras apskritai, bet turi keletą keistų keistenybių, kurios kainavo daug laiko derinant, pavyzdžiui, konfigūruojant pasirinktinius domenus ar problemas su jo SqlAlchemy integracija."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Iki šiol kiti įrankiai buvo puikūs: neturime rimtų skundų dėl MariaDB, ElasticSearch, Gitlab, Zulip, Docker ir Tor. Visi jie turėjo tam tikrų problemų, bet nieko pernelyg rimto ar laiko reikalaujančio."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Išvada"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Buvo įdomi patirtis mokytis, kaip sukurti tvirtą ir atsparią šešėlinės bibliotekos paieškos sistemą. Yra daugybė detalių, kurias galima pasidalinti vėlesniuose įrašuose, tad praneškite, ką norėtumėte sužinoti daugiau!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Kaip visada, ieškome aukų, kad palaikytume šį darbą, tad būtinai apsilankykite Anna’s Archive aukojimo puslapyje. Taip pat ieškome kitų paramos formų, tokių kaip dotacijos, ilgalaikiai rėmėjai, didelės rizikos mokėjimo tiekėjai, galbūt net (skoningos!) reklamos. Jei norite prisidėti savo laiku ir įgūdžiais, visada ieškome kūrėjų, vertėjų ir pan. Dėkojame už jūsų susidomėjimą ir palaikymą."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna ir komanda (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Sveiki, aš esu Anna. Sukūriau <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, didžiausią pasaulyje šešėlinę biblioteką. Tai yra mano asmeninis tinklaraštis, kuriame aš ir mano komandos nariai rašome apie piratavimą, skaitmeninį išsaugojimą ir dar daugiau."

#, fuzzy
msgid "blog.index.text2"
msgstr "Susisiekite su manimi <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Atkreipkite dėmesį, kad ši svetainė yra tik tinklaraštis. Čia talpiname tik savo žodžius. Čia nėra talpinami ar susieti jokie torrentai ar kiti autorių teisių saugomi failai."

#, fuzzy
msgid "blog.index.heading"
msgstr "Tinklaraščio įrašai"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat nuskaitymas"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5,998,794 knygų talpinimas IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Įspėjimas: šis tinklaraščio įrašas yra pasenęs. Nusprendėme, kad IPFS dar nėra pasirengęs plačiam naudojimui. Vis dar nuorodų į failus IPFS iš Annos Archyvo, kai įmanoma, bet nebepriglobosime jų patys ir nerekomenduojame kitiems atkartoti naudojant IPFS. Prašome peržiūrėti mūsų Torrents puslapį, jei norite padėti išsaugoti mūsų kolekciją."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archive nuskaityta visa WorldCat (didžiausia pasaulyje bibliotekos metadata kolekcija), kad būtų sudarytas knygų, kurias reikia išsaugoti, TODO sąrašas.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Prieš metus mes <a %(blog)s>pradėjome</a> atsakyti į šį klausimą: <strong>Kokį procentą knygų šešėlinės bibliotekos išsaugojo visam laikui?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Kai knyga patenka į atviro duomenų šešėlinę biblioteką, tokią kaip <a %(wikipedia_library_genesis)s>Library Genesis</a>, o dabar ir <a %(wikipedia_annas_archive)s>Annos Archyvas</a>, ji yra veidrodinė visame pasaulyje (per torrentus), taip praktiškai išsaugant ją amžinai."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Norint atsakyti į klausimą, koks procentas knygų buvo išsaugotas, reikia žinoti vardiklį: kiek iš viso egzistuoja knygų? Ir idealiu atveju mes turime ne tik skaičių, bet ir tikrąją metadata. Tada galime ne tik palyginti jas su šešėlinėmis bibliotekomis, bet ir <strong>sukurti likusių knygų, kurias reikia išsaugoti, sąrašą!</strong> Galėtume net pradėti svajoti apie bendruomenės pastangas eiti per šį sąrašą."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Mes išgremžėme <a %(wikipedia_isbndb_com)s>ISBNdb</a> ir atsisiuntėme <a %(openlibrary)s>Open Library duomenų rinkinį</a>, tačiau rezultatai buvo nepatenkinami. Pagrindinė problema buvo ta, kad ISBN sutapimų buvo labai mažai. Žiūrėkite šią Venn diagramą iš <a %(blog)s>mūsų tinklaraščio įrašo</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Mus labai nustebino, kaip mažai sutapimų buvo tarp ISBNdb ir Open Library, kurios abi laisvai įtraukia duomenis iš įvairių šaltinių, tokių kaip interneto išgremžimai ir bibliotekų įrašai. Jei jos abi gerai atlieka darbą ieškodamos daugumos ISBN, jų apskritimai tikrai turėtų turėti reikšmingą sutapimą arba viena būtų kitos dalis. Tai privertė mus susimąstyti, kiek knygų <em>visiškai nepatenka į šiuos apskritimus</em>? Mums reikia didesnės duomenų bazės."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Tada mes nukreipėme savo žvilgsnį į didžiausią knygų duomenų bazę pasaulyje: <a %(wikipedia_worldcat)s>WorldCat</a>. Tai yra nuosavybinė duomenų bazė, kurią valdo ne pelno siekianti organizacija <a %(wikipedia_oclc)s>OCLC</a>, kuri renka metadata įrašus iš bibliotekų visame pasaulyje, mainais už tai, kad suteikia toms bibliotekoms prieigą prie viso duomenų rinkinio ir leidžia joms pasirodyti galutinių vartotojų paieškos rezultatuose."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Nors OCLC yra ne pelno siekianti organizacija, jų verslo modelis reikalauja saugoti jų duomenų bazę. Na, atsiprašome, draugai iš OCLC, mes viską atiduodame. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Per pastaruosius metus mes kruopščiai išgremžėme visus WorldCat įrašus. Iš pradžių mums pasisekė. WorldCat ką tik pradėjo savo svetainės visišką pertvarkymą (2022 m. rugpjūtį). Tai apėmė reikšmingą jų užpakalinių sistemų pertvarkymą, įvedant daugybę saugumo spragų. Mes iš karto pasinaudojome šia galimybe ir per kelias dienas sugebėjome išgremžti šimtus milijonų (!) įrašų."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat pertvarkymas</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Po to saugumo spragos buvo lėtai taisomos viena po kitos, kol paskutinė, kurią radome, buvo užtaisyta maždaug prieš mėnesį. Tuo metu mes jau turėjome beveik visus įrašus ir siekėme tik šiek tiek aukštesnės kokybės įrašų. Taigi, manome, kad laikas išleisti!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Pažvelkime į kai kurią pagrindinę informaciją apie duomenis:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formatas?</strong> <a %(blog)s>Annos Archyvo Konteineriai (AAC)</a>, kurie iš esmės yra <a %(jsonlines)s>JSON Lines</a>, suspausti su <a %(zstd)s>Zstandard</a>, plius kai kurios standartizuotos semantikos. Šie konteineriai apima įvairių tipų įrašus, remiantis skirtingais mūsų įdiegtais išgremžimais."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Duomenys"

msgid "dyn.buy_membership.error.unknown"
msgstr "Įvyko nežinoma klaida. Prašome susisiekti su mumis adresu %(email)s ir prisegti ekrano nuotrauką."

msgid "dyn.buy_membership.error.minimum"
msgstr "Ši moneta turi didesnę nei įprasta minimalią sumą. Prašome pasirinkti kitą trukmę arba kitą monetą."

msgid "dyn.buy_membership.error.try_again"
msgstr "Užklausa negalėjo būti įvykdyta. Bandykite dar kartą po kelių minučių, o jei problema išlieka, susisiekite su mumis adresu %(email)s ir prisekite ekrano nuotrauką."

msgid "dyn.buy_membership.error.wait"
msgstr "Mokėjimo apdorojimo klaida. Palaukite akimirką ir bandykite dar kartą. Jei problema išlieka ilgiau nei 24 valandas, susisiekite su mumis adresu %(email)s ir pridėkite ekrano nuotrauką."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "paslėptas komentaras"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Failo problema: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Geresnė versija"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ar norite pranešti apie šį vartotoją dėl įžeidžiančio ar netinkamo elgesio?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Pranešti apie piktnaudžiavimą"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Piktnaudžiavimas praneštas:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Jūs pranešėte apie šį vartotoją dėl piktnaudžiavimo."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Atsakyti"

msgid "page.md5.quality.logged_out_login"
msgstr "Prašome <a %(a_login)s>prisijungti</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Jūs palikote komentarą. Gali praeiti minutė, kol jis bus rodomas."

msgid "page.md5.quality.comment_error"
msgstr "Kažkas nepavyko. Perkraukite puslapį ir bandykite dar kartą."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s paveikti puslapiai"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nematoma Libgen.rs negrožinėje literatūroje"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nematoma Libgen.rs grožinėje literatūroje"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nematoma Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Pažymėta kaip neteisinga Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Nėra Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Z-Library pažymėta kaip „šlamštas“"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Z-Library pažymėta kaip „blogas failas“"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Ne visus puslapius pavyko konvertuoti į PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Nepavyko paleisti exiftool šiam failui"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Knyga (nežinoma)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Knyga (negrožinė literatūra)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Knyga (grožinė literatūra)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Žurnalo straipsnis"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standartų dokumentas"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Žurnalas"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komiksų knyga"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Muzikinė partitūra"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Garso knyga"

msgid "common.md5_content_type_mapping.other"
msgstr "Kita"

msgid "common.access_types_mapping.aa_download"
msgstr "Atsisiuntimas iš partnerių serverių"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Atsisiuntimas iš išorės"

msgid "common.access_types_mapping.external_borrow"
msgstr "Paskola iš išorės"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Išorinis skolinimasis (spausdinimas išjungtas)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Tyrinėti metaduomenis"

msgid "common.access_types_mapping.torrents_available"
msgstr "Yra torrentuose"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Įkėlimai į AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Čekiški metaduomenys"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google knygos"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Rusijos valstybinė biblioteka"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Pavadinimas"

msgid "common.specific_search_fields.author"
msgstr "Autorius"

msgid "common.specific_search_fields.publisher"
msgstr "Leidėjas"

msgid "common.specific_search_fields.edition_varia"
msgstr "Leidimas"

msgid "common.specific_search_fields.year"
msgstr "Išleidimo metai"

msgid "common.specific_search_fields.original_filename"
msgstr "Originalus failo pavadinimas"

msgid "common.specific_search_fields.description_comments"
msgstr "Aprašymas ir metaduomenų komentarai"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partnerio serverio atsisiuntimai laikinai nepasiekiami šiam failui."

msgid "common.md5.servers.fast_partner"
msgstr "Greitasis partnerio serveris #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(rekomenduojama)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(nėra naršyklės patikrinimo ar eilės)"

msgid "common.md5.servers.slow_partner"
msgstr "Lėtasis partnerio serveris #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(šiek tiek greičiau, bet su eile)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(be eilės, bet gali būti labai lėtas)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs ne grožinė literatūra"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs grožinė literatūra"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(taip pat paspauskite „get“ (gauti) viršuje)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(paspauskite „get“ (gauti) viršuje)"

msgid "page.md5.box.download.libgen_ads"
msgstr "jų reklamos yra žinomos dėl kenkėjiškos programinės įrangos, todėl naudokite reklamos blokatorių arba nespauskite ant reklamų"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC failų gali būti neįmanoma atsisiųsti)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library Tor tinkle"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(reikalauja Tor naršyklės)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Skolintis iš Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(tik neįgaliesiems su skaitymo negalia)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(susijęs DOI gali būti nepasiekiamas Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "kolekcija"

msgid "page.md5.box.download.torrent"
msgstr "torentas"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Masiniai torrentų atsisiuntimai"

msgid "page.md5.box.download.experts_only"
msgstr "(tik ekspertams)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Ieškoti Annos Archyve pagal ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Ieškokite įvairiose kitose duomenų bazėse pagal ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Rasti originalų įrašą ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Ieškoti Annos Archyve pagal Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Rasti originalų įrašą Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Ieškoti Annos Archyve pagal OCLC (WorldCat) numerį"

msgid "page.md5.box.download.original_oclc"
msgstr "Rasti originalų įrašą WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Ieškoti Annos Archyve pagal DuXiu SSID numerį"

msgid "page.md5.box.download.original_duxiu"
msgstr "Ieškoti rankiniu būdu DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Ieškoti Anos Archyve CADAL SSNO numerio"

msgid "page.md5.box.download.original_cadal"
msgstr "Rasti originalų įrašą CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Ieškoti Annos Archyve pagal DuXiu DXID numerį"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Annos Archyvas 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(nereikalingas naršyklės patvirtinimas)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Čekijos metaduomenys %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Knygos %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metaduomenys"

msgid "page.md5.box.descr_title"
msgstr "aprašymas"

msgid "page.md5.box.alternative_filename"
msgstr "Alternatyvus failo pavadinimas"

msgid "page.md5.box.alternative_title"
msgstr "Alternatyvus pavadinimas"

msgid "page.md5.box.alternative_author"
msgstr "Alternatyvus autorius"

msgid "page.md5.box.alternative_publisher"
msgstr "Alternatyvus leidėjas"

msgid "page.md5.box.alternative_edition"
msgstr "Alternatyvus leidimas"

msgid "page.md5.box.alternative_extension"
msgstr "Alternatyvus plėtinys"

msgid "page.md5.box.metadata_comments_title"
msgstr "metaduomenų komentarai"

msgid "page.md5.box.alternative_description"
msgstr "Alternatyvus aprašymas"

msgid "page.md5.box.date_open_sourced_title"
msgstr "data, kai buvo atvertas šaltinis"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub failas “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive kontroliuojamo skaitmeninio skolinimo failas „%(id)s“"

msgid "page.md5.header.ia_desc"
msgstr "Tai yra failo iš Internet Archive įrašas, o ne tiesiogiai atsisiunčiamas failas. Galite pabandyti pasiskolinti knygą (nuoroda žemiau) arba naudoti šį URL, kai <a %(a_request)s>prašote failo</a>."

msgid "page.md5.header.consider_upload"
msgstr "Jei turite šį failą ir jis dar nėra prieinamas Anna’s Archive, apsvarstykite galimybę <a %(a_request)s>jį įkelti</a>."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) numerio %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metaduomenų įrašas"

msgid "page.md5.header.meta_desc"
msgstr "Tai yra metaduomenų įrašas, o ne atsisiunčiamas failas. Galite naudoti šį URL norint <a %(a_request)s>paprašyti</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Metaduomenys iš susieto įrašo"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Patobulinkite metaduomenis esančius Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Įspėjimas: keli susieti įrašai:"

msgid "page.md5.header.improve_metadata"
msgstr "Tobulinti metaduomenis"

msgid "page.md5.text.report_quality"
msgstr "Pranešti apie failo kokybę"

msgid "page.search.results.download_time"
msgstr "Atsisiuntimo laikas"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Interneto svetainė:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Ieškoti Annos Archyve „%(name)s“"

msgid "page.md5.codes.code_explorer"
msgstr "Koduotės naršyklė:"

msgid "page.md5.codes.code_search"
msgstr "Peržiūrėti Koduotės naršyklėje „%(name)s“"

msgid "page.md5.box.descr_read_more"
msgstr "Skaityti daugiau…"

msgid "page.md5.tabs.downloads"
msgstr "Atsisiuntimai (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Skolintis (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Naršyti metaduomenis (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komentarai (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Sąrašai (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistika (%(count)s)"

msgid "common.tech_details"
msgstr "Techninės detalės"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Šis failas gali turėti problemų ir todėl buvo paslėptas iš šaltinio bibliotekos.</span> Kartais tai yra atliekama dėl autorines teises turinčio asmens užklausos, kartais dėl to, kad yra pasiekiama geresnė alternatyva, o kitais kartais, kadangi yra problema su pačiu failu. Jis gali būti tinkamas parsisiųsti, tačiau rekomenduojame pirmiausiai ieškoti alternatyvos. Plačiau:"

msgid "page.md5.box.download.better_file"
msgstr "Geresnė šio failo versija gali būti prieinama adresu %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Jeigu jūs vis dar norite parsisiųsti šį failą, naudokite tik patikimą, atnaujintą programinę įrangą norint jį atidaryti."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Greiti atsisiuntimai"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Tapk <a %(a_membership)s>nariu</a>, kad prisidėtum prie ilgalaikio knygų ir popieriaus tausojimo. Rodydami padėką už jūsų palaikymą, dovanojame greitus atsisiuntimus. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Jei paaukosite šį mėnesį, gausite <strong>dvigubai</strong> daugiau greitų atsisiuntimų."

msgid "page.md5.box.download.header_fast_member"
msgstr "Šiandieną dar turite %(remaining)s greitus parsisiuntimus. Ačiū, kad esate narys! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Jūs išnaudojote greitų atsisiuntimų dienos limitą."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Jūs neseniai atsisiuntėte šį failą. Nuorodos galioja tam tikrą laiką."

msgid "page.md5.box.download.option"
msgstr "Pasirinkimas %(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(be nukreipimo)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(atidaryti peržiūros programoje)"

msgid "layout.index.header.banner.refer"
msgstr "Pakvieskite draugą, ir jūs abu gausite %(percentage)s%% papildomų greitų atsisiuntimų!"

msgid "layout.index.header.learn_more"
msgstr "Sužinoti daugiau…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Lėti atsisiuntimai"

msgid "page.md5.box.download.trusted_partners"
msgstr "Iš patikimų partnerių."

msgid "page.md5.box.download.slow_faq"
msgstr "Daugiau informacijos <a %(a_slow)s>DUK</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(gali prireikti <a %(a_browser)s>naršyklės patvirtinimo</a> — neriboti atsisiuntimai!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Po atsisiuntimo:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Atidaryti mūsų peržiūros programoje"

msgid "page.md5.box.external_downloads"
msgstr "rodyti išorinius atsisiuntimus"

msgid "page.md5.box.download.header_external"
msgstr "Išoriniai atsisiuntimai"

msgid "page.md5.box.download.no_found"
msgstr "Jokių atsisiuntimų nerasta."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Visi parsiuntimo metodai turi tą patį failą ir turi būti saugus parsisiųsti, tačiau visada reikia būti atsargiam parsisiunčiant failus iš interneto. Pavyzdžiui, reguliariai atnaujinti savo programinę įrangą."

msgid "page.md5.box.download.dl_managers"
msgstr "Dideliems failams rekomenduojame naudoti atsisiuntimo tvarkyklę, kad išvengtumėte trigdžių."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Rekomenduojamos atsisiuntimo tvarkyklės: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Jums reikės el. knygų arba PDF skaitytuvo, kad atidarytumėte failą (priklausomai nuo formato)."

msgid "page.md5.box.download.readers.links"
msgstr "Rekomenduojami el. knygų skaitytuvai: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Annės Archyvo internetinė peržiūros programa"

msgid "page.md5.box.download.conversion"
msgstr "Naudokite internetinius įrankius formatams konvertuoti."

msgid "page.md5.box.download.conversion.links"
msgstr "Rekomenduojami konvertavimo įrankiai: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Galite siųsti tiek PDF, tiek EPUB failus į savo Kindle arba Kobo el. skaitytuvą."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Rekomenduojami įrankiai: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon „Siųsti į Kindle“"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz „Siųsti į Kobo/Kindle“"

msgid "page.md5.box.download.support"
msgstr "Palaikyti autorius ir bibliotekas"

msgid "page.md5.box.download.support.authors"
msgstr "Jei jums tai patinka ir galite sau leisti, apsvarstykite galimybę įsigyti originalą arba tiesiogiai paremti autorius."

msgid "page.md5.box.download.support.libraries"
msgstr "Jei tai yra jūsų vietinėje bibliotekoje, apsvarstykite galimybę pasiskolinti nemokamai."

msgid "page.md5.quality.header"
msgstr "Failo kokybė"

msgid "page.md5.quality.report"
msgstr "Padėkite bendruomenei pranešdami apie šio failo kokybę! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Pranešti apie failo problemą (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Puiki failo kokybė (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Pridėti komentarą (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Kas negerai su šiuo failu?"

msgid "page.md5.quality.copyright"
msgstr "Prašome naudoti <a %(a_copyright)s>DMCA / Autorinių teisių pretenzijos formą</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Apibūdinkite problemą (privaloma)"

msgid "page.md5.quality.issue_description"
msgstr "Problemos aprašymas"

msgid "page.md5.quality.better_md5.text1"
msgstr "Geresnės šio failo versijos MD5 (jei taikoma)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Užpildykite tai, jei yra kitas failas, kuris labai panašus į šį failą (ta pati laida, tas pats failo plėtinys, jei galite rasti), kurį žmonės turėtų naudoti vietoj šio failo. Jei žinote geresnę šio failo versiją už Anna’s Archive ribų, prašome <a %(a_upload)s>ją įkelti</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "MD5 galite gauti iš URL, pvz."

msgid "page.md5.quality.submit_report"
msgstr "Pateikti pranešimą"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Sužinokite, kaip patys galite <a %(a_metadata)s>patobulinti failo metaduomenis</a>."

msgid "page.md5.quality.report_thanks"
msgstr "Ačiū, kad pateikėte savo pranešimą. Jis bus rodomas šiame puslapyje ir peržiūrima rankiniu būdu Annos (kol neturime tinkamos moderavimo sistemos)."

msgid "page.md5.quality.report_error"
msgstr "Kažkas nepavyko. Perkraukite puslapį ir bandykite dar kartą."

msgid "page.md5.quality.great.summary"
msgstr "Jei šis failas yra puikios kokybės, galite diskutuoti apie jį čia! Jei ne, naudokite mygtuką „Pranešti apie failo problemą“."

msgid "page.md5.quality.loved_the_book"
msgstr "Man labai patiko ši knyga!"

msgid "page.md5.quality.submit_comment"
msgstr "Palikti komentarą"

msgid "common.english_only"
msgstr "Tekstas žemiau tęsiasi anglų kalba."

msgid "page.md5.text.stats.total_downloads"
msgstr "Iš viso atsisiuntimų: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "„Failo MD5“ yra maiša, kuri apskaičiuojama iš failo turinio ir yra pakankamai unikali pagal tą turinį. Visos šešėlinės bibliotekos, kurias mes indeksavome čia, pirmiausia naudoja MD5 failams identifikuoti."

msgid "page.md5.text.md5_info.text2"
msgstr "Failas gali pasirodyti keliose šešėlinėse bibliotekose. Informaciją apie įvairius mūsų sudarytus duomenų rinkinius rasite <a %(a_datasets)s>Duomenų rinkinių puslapyje</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Tai yra failas, valdomas <a %(a_ia)s>IA kontroliuojamo skaitmeninio skolinimo</a> bibliotekos ir indeksuotas Annos Archyve paieškai. Informaciją apie įvairius mūsų sudarytus duomenų rinkinius rasite <a %(a_datasets)s>Duomenų rinkinių puslapyje</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Informaciją apie šį konkretų failą rasite jo <a %(a_href)s>JSON faile</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Problema įkeliant šį puslapį"

msgid "page.aarecord_issue.text"
msgstr "Prašome perkrauti puslapį ir bandyti dar kartą. <a %(a_contact)s>Susisiekite su mumis</a>, jei problema išlieka kelias valandas."

msgid "page.md5.invalid.header"
msgstr "Nerasta"

msgid "page.md5.invalid.text"
msgstr "\"%(md5_input)s\" nebuvo rasta mūsų duomenų bazėje."

msgid "page.login.title"
msgstr "Prisijungti / Registruotis"

msgid "page.browserverification.header"
msgstr "Naršyklės patikrinimas"

msgid "page.login.text1"
msgstr "Kad šlamšto robotai nesukurtų daug paskyrų, pirmiausia turime patvirtinti jūsų naršyklę."

msgid "page.login.text2"
msgstr "Jei pakliūsite į begalinę kilpą, rekomenduojame įdiegti <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Taip pat gali padėti reklamos blokatoriaus ir kitų naršyklės plėtinių išjungimas."

msgid "page.codes.title"
msgstr "Kodai"

msgid "page.codes.heading"
msgstr "Kodų naršyklė"

#, fuzzy
msgid "page.codes.intro"
msgstr "Tyrinėkite kodus, su kuriais įrašai yra pažymėti pagal priešdėlį. Stulpelyje „įrašai“ rodomas įrašų skaičius, pažymėtų kodais su nurodytu priešdėliu, kaip matoma paieškos sistemoje (įskaitant tik metaduomenų įrašus). Stulpelyje „kodai“ rodomas faktinių kodų su nurodytu priešdėliu skaičius."

msgid "page.codes.why_cloudflare"
msgstr "Gali užtrukti, kol šis puslapis bus sugeneruotas, todėl reikalinga Cloudflare captcha. <a %(a_donate)s>Nariai</a> gali praleisti captcha."

msgid "page.codes.dont_scrape"
msgstr "Prašome nenuskaitynėti šių puslapių. Vietoj to rekomenduojame <a %(a_import)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes ir naudoti mūsų <a %(a_software)s>atvirojo kodo kodą</a>. Neapdoroti duomenys gali būti rankiniu būdu tyrinėjami per JSON failus, tokius kaip <a %(a_json_file)s>šis</a>."

msgid "page.codes.prefix"
msgstr "Prefiksas"

msgid "common.form.go"
msgstr "Eiti"

msgid "common.form.reset"
msgstr "Atstatyti"

msgid "page.codes.search_archive_start"
msgstr "Ieškoti Annos Archyve"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Įspėjimas: kode yra neteisingų Unicode simbolių, ir jis gali netinkamai veikti įvairiose situacijose. Neapdorotas dvejetainis kodas gali būti dekoduotas iš base64 reprezentacijos URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Žinomas kodo priešdėlis „%(key)s“"

msgid "page.codes.code_prefix"
msgstr "Prefiksas"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiketė"

msgid "page.codes.code_description"
msgstr "Aprašymas"

msgid "page.codes.code_url"
msgstr "Konkretaus kodo URL"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "„%%s“ bus pakeistas kodo verte"

msgid "page.codes.generic_url"
msgstr "Bendras URL"

msgid "page.codes.code_website"
msgstr "Interneto svetainė"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s įrašas atitinkantys „%(prefix_label)s”"
msgstr[1] "%(count)s įrašai atitinkantys „%(prefix_label)s”"
msgstr[2] "%(count)s įrašų atitinkantys „%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL konkrečiam kodui: „%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Daugiau…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kodai prasidedantys „%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeksas"

msgid "page.codes.records_prefix"
msgstr "įrašai"

msgid "page.codes.records_codes"
msgstr "kodai"

msgid "page.codes.fewer_than"
msgstr "Mažiau nei %(count)s įrašų"

msgid "page.contact.dmca.form"
msgstr "Dėl DMCA / autorių teisių pretenzijų naudokite <a %(a_copyright)s>šią formą</a>."

msgid "page.contact.dmca.delete"
msgstr "Bet kokie kiti būdai susisiekti su mumis dėl autorių teisių pretenzijų bus automatiškai ištrinti."

msgid "page.contact.checkboxes.text1"
msgstr "Labai laukiame jūsų atsiliepimų ir klausimų!"

msgid "page.contact.checkboxes.text2"
msgstr "Tačiau dėl didelio kiekio šlamšto ir nesąmoningų el. laiškų, prašome pažymėti langelius, kad patvirtintumėte, jog suprantate šias susisiekimo su mumis sąlygas."

msgid "page.contact.checkboxes.copyright"
msgstr "Autorių teisių pretenzijos šiuo el. paštu bus ignoruojamos; vietoj to naudokite formą."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partnerių serveriai yra nepasiekiami dėl prieglobos uždarymo. Jie netrukus vėl turėtų veikti."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Narystės bus atitinkamai pratęstos."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Nerašykite mums dėl <a %(a_request)s>knygų prašymų</a><br> ar smulkių (<10 tūkst.) <a %(a_upload)s>įkėlimų</a>."

msgid "page.donate.please_include"
msgstr "Klausiant apie paskyrą ar aukas, pridėkite savo paskyros ID, ekrano nuotraukas, kvitus ir kuo daugiau informacijos. Mes tikriname savo el. paštą kas 1-2 savaites, todėl šios informacijos neįtraukimas uždels bet kokį sprendimą."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Rodyti el. paštą"

msgid "page.copyright.title"
msgstr "DMCA / Autorių teisių pažeidimo forma"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Jei turite DCMA ar kitą autorių teisių pažeidimo pretenziją, prašome užpildyti šią formą kuo tiksliau. Jei susidursite su kokiomis nors problemomis, susisiekite su mumis mūsų specialiu DMCA adresu: %(email)s. Atkreipkite dėmesį, kad pretenzijos, siunčiamos šiuo adresu, nebus apdorojamos, jis skirtas tik klausimams. Prašome naudoti žemiau esančią formą savo pretenzijoms pateikti."

msgid "page.copyright.form.aa_urls"
msgstr "URL adresai Anna’s Archive (privaloma). Po vieną eilutėje. Prašome įtraukti tik URL adresus, kurie aprašo tą pačią knygos leidimo versiją. Jei norite pateikti pretenziją dėl kelių knygų ar kelių leidimų, prašome pateikti šią formą kelis kartus."

msgid "page.copyright.form.aa_urls.note"
msgstr "Pretenzijos, kurios apima kelias knygas ar leidimus, bus atmestos."

msgid "page.copyright.form.name"
msgstr "Jūsų vardas (privaloma)"

msgid "page.copyright.form.address"
msgstr "Adresas (privaloma)"

msgid "page.copyright.form.phone"
msgstr "Telefono numeris (privaloma)"

msgid "page.copyright.form.email"
msgstr "El. paštas (privaloma)"

msgid "page.copyright.form.description"
msgstr "Aiškus šaltinio medžiagos aprašymas (privaloma)"

msgid "page.copyright.form.isbns"
msgstr "Šaltinio medžiagos ISBN (jei taikoma). Po vieną eilutėje. Prašome įtraukti tik tuos, kurie tiksliai atitinka leidimą, dėl kurio pranešate apie autorių teisių pažeidimą."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> šaltinio medžiagos URL adresai, po vieną eilutėje. Prašome skirti laiko paieškai Open Library, kad surastumėte savo šaltinio medžiagą. Tai padės mums patvirtinti jūsų pretenziją."

msgid "page.copyright.form.external_urls"
msgstr "Šaltinio medžiagos URL adresai, po vieną eilutėje (privaloma). Prašome įtraukti kuo daugiau, kad padėtumėte mums patvirtinti jūsų pretenziją (pvz., Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Pareikšimas ir parašas (privaloma)"

msgid "page.copyright.form.submit_claim"
msgstr "Pateikti pretenziją"

msgid "page.copyright.form.on_success"
msgstr "✅ Ačiū, kad pateikėte savo autorių teisių pažeidimo pretenziją. Mes ją peržiūrėsime kuo greičiau. Prašome atnaujinti puslapį, kad pateiktumėte kitą pretenziją."

msgid "page.copyright.form.on_failure"
msgstr "❌ Kažkas nepavyko. Prašome perkrautį puslapį ir bandyti dar kartą."

msgid "page.datasets.title"
msgstr "Duomenų rinkiniai"

msgid "page.datasets.common.intro"
msgstr "Jei jus domina šio duomenų rinkinio atkartojimas <a %(a_archival)s>archyvavimo</a> ar <a %(a_llm)s>LLM mokymo</a> tikslais, prašome susisiekti su mumis."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Mūsų misija yra archyvuoti visas pasaulio knygas (taip pat straipsnius, žurnalus ir kt.) ir padaryti jas plačiai prieinamas. Mes tikime, kad visos knygos turėtų būti plačiai atkartojamos, siekiant užtikrinti jų išlikimą. Štai kodėl mes jungiame failus iš įvairių šaltinių. Kai kurie šaltiniai yra visiškai atviri ir gali būti atkartojami dideliais kiekiais (pvz., Sci-Hub). Kiti yra uždari ir apsaugoti, todėl stengiamės juos išgauti, kad „išlaisvintume“ jų knygas. Dar kiti yra kažkur tarp šių."

msgid "page.datasets.intro.text3"
msgstr "Visi mūsų duomenys gali būti <a %(a_torrents)s>torrentuojami</a>, o visi mūsų metaduomenys gali būti <a %(a_anna_software)s>generuojami</a> arba <a %(a_elasticsearch)s>atsisiunčiami</a> kaip ElasticSearch ir MariaDB duomenų bazės. Neapdoroti duomenys gali būti rankiniu būdu tyrinėjami per JSON failus, tokius kaip <a %(a_dbrecord)s>šis</a>."

msgid "page.datasets.overview.title"
msgstr "Apžvalga"

msgid "page.datasets.overview.text1"
msgstr "Žemiau pateikiama greita failų šaltinių apžvalga Annos Archyve."

msgid "page.datasets.overview.source.header"
msgstr "Šaltinis"

msgid "page.datasets.overview.size.header"
msgstr "Dydis"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% veidrodinis AA / torrentai prieinami"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Failų skaičiaus procentai"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Paskutinį kartą atnaujinta"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Grožinė ir negrožinė literatūra"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s failas"
msgstr[1] "%(count)s failai"
msgstr[2] "%(count)s failų"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Per Libgen.li „scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: užšaldyta nuo 2021 m.; dauguma prieinama per torrentus"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: nuo tada nedideli papildymai</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Išskyrus „scimag“"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Grožinės literatūros torrentai atsilieka (nors ID ~4-6M nėra torrentuose, nes jie sutampa su mūsų Zlib torrentais)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "„Kinų” kolekcija Z-Library atrodo tokia pati kaip mūsų DuXiu kolekcija, bet su skirtingais MD5. Mes neįtraukiame šių failų į torrentus, kad išvengtume dublikatų, bet vis tiek rodome juos mūsų paieškos indekse."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA kontroliuojamas skaitmeninis skolinimas"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ failų galima ieškoti."

msgid "page.datasets.overview.total"
msgstr "Iš viso"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Neįskaitant dublikatų"

msgid "page.datasets.overview.text4"
msgstr "Kadangi šešėlinės bibliotekos dažnai sinchronizuoja duomenis viena iš kitos, tarp bibliotekų yra nemažai persidengimo. Todėl skaičiai nesutampa su bendra suma."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "„Veidrodinis ir sėtas Anna’s Archive“ procentas rodo, kiek failų mes patys veidrodiname. Mes sėjame tuos failus dideliais kiekiais per torrentus ir padarome juos prieinamus tiesioginiam atsisiuntimui per partnerių svetaines."

msgid "page.datasets.source_libraries.title"
msgstr "Šaltinių bibliotekos"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Kai kurios šaltinių bibliotekos skatina masinį duomenų dalijimąsi per torrentus, o kitos neskuba dalintis savo kolekcijomis. Pastaruoju atveju, Anna’s Archive bando surinkti jų kolekcijas ir padaryti jas prieinamas (žr. mūsų <a %(a_torrents)s>Torrentų</a> puslapį). Taip pat yra tarpinės situacijos, pavyzdžiui, kai šaltinių bibliotekos nori dalintis, bet neturi tam išteklių. Tokiais atvejais mes taip pat stengiamės padėti."

msgid "page.datasets.source_libraries.text2"
msgstr "Žemiau pateikiama apžvalga, kaip mes sąveikaujame su skirtingomis šaltinių bibliotekomis."

msgid "page.datasets.sources.source.header"
msgstr "Šaltinis"

msgid "page.datasets.sources.files.header"
msgstr "Failai"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Kasdienės <a %(dbdumps)s>HTTP duomenų kopijos</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatiniai torrent failai <a %(nonfiction)s>Negrožinei literatūrai</a> ir <a %(fiction)s>Grožinei literatūra</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annos Archyvas valdo <a %(covers)s>knygų viršelių torrentų</a> kolekciją"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen „scimag“"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub nuo 2021 metų nebeprideda naujų failų."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metaduomenų iškrovos prieinamos <a %(scihub1)s>čia</a> ir <a %(scihub2)s>čia</a>, taip pat kaip dalis <a %(libgenli)s>Libgen.li duomenų bazės</a> (kurią mes naudojame)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Duomenų torrentai prieinami <a %(scihub1)s>čia</a>, <a %(scihub2)s>čia</a> ir <a %(libgenli)s>čia</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Kai kurie nauji failai <a %(libgenrs)s>pridedami</a> į Libgen „scimag“, bet nepakankamai, kad būtų verta kurti naujus torrentus"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Ketvirtiniai <a %(dbdumps)s>HTTP duomenų bazės iškrovos</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Ne grožinės literatūros torrentai dalijami su Libgen.rs (ir atkartojami <a %(libgenli)s>čia</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annos Archyvas ir Libgen.li kartu tvarko <a %(comics)s>komiksų</a>, <a %(magazines)s>žurnalų</a>, <a %(standarts)s>standartinių dokumentų</a> ir <a %(fiction)s>grožinės literatūros (atskirtos nuo Libgen.rs)</a> kolekcijas."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Jų „fiction_rus“ kolekcija (rusų grožinė literatūra) neturi specialių torrentų, tačiau yra padengta kitų torrentų, ir mes laikome <a %(fiction_rus)s>veidrodį</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annos Archyvas ir Z-Library kartu valdo <a %(metadata)s>Z-Library metaduomenų</a> ir <a %(files)s>Z-Library failų</a> kolekciją"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Kai kurie metaduomenys prieinami per <a %(openlib)s>Open Library duomenų bazės iškrovas</a>, bet jie neapima visos IA kolekcijos"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nėra lengvai prieinamų metaduomenų iškrovų visai jų kolekcijai"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annos Archyvas valdo <a %(ia)s>IA metaduomenų</a> kolekciją"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Failai, kuriuos galima skolintis tik ribotai, su įvairiais prieigos apribojimais"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annos Archyvas valdo <a %(ia)s>IA failų</a> kolekciją"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Įvairios metaduomenų duomenų bazės, išsibarsčiusios po Kinijos internetą; dažnai mokamos duomenų bazės"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nėra lengvai prieinamų metaduomenų iškrovų visai jų kolekcijai."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annos Archyvas valdo <a %(duxiu)s>DuXiu metaduomenų</a> kolekciją"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Įvairios failų duomenų bazės, išsibarsčiusios po Kinijos internetą; dažnai mokamos duomenų bazės"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Dauguma failų prieinami tik naudojant premium BaiduYun paskyras; lėtas atsisiuntimo greitis."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annos Archyvas valdo <a %(duxiu)s>DuXiu failų</a> kolekciją"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Įvairūs mažesni ar vienkartiniai šaltiniai. Skatiname žmones pirmiausia įkelti į kitas šešėlines bibliotekas, tačiau kartais žmonės turi kolekcijų, kurios yra per didelės kitiems rūšiuoti, bet nepakankamai didelės, kad būtų verta sukurti atskirą kategoriją."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Tik metaduomenų šaltiniai"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Mes taip pat praturtiname savo kolekciją tik metaduomenų šaltiniais, kuriuos galime susieti su failais, pvz., naudojant ISBN numerius ar kitus laukus. Žemiau pateikiama tokių šaltinių apžvalga. Vėlgi, kai kurie iš šių šaltinių yra visiškai atviri, o kitus turime surinkti."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Mūsų įkvėpimas rinkti metaduomenis yra Aarono Swartzo tikslas „vienas tinklalapis kiekvienai kada nors išleistai knygai“, kuriam jis sukūrė <a %(a_openlib)s>Open Library</a>. Šis projektas sekasi gerai, tačiau mūsų unikali padėtis leidžia gauti metaduomenis, kurių jie negali. Kitas įkvėpimas buvo mūsų noras sužinoti <a %(a_blog)s>kiek knygų yra pasaulyje</a>, kad galėtume apskaičiuoti, kiek knygų dar turime išsaugoti."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Atkreipkite dėmesį, kad metaduomenų paieškoje mes rodome originalius įrašus. Mes nesujungiame įrašų."

msgid "page.datasets.sources.last_updated.header"
msgstr "Paskutinį kartą atnaujinta"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Mėnesiniai <a %(dbdumps)s>duomenų bazės parsiuntimai</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nėra tiesiogiai prieinami dideliais kiekiais, apsaugoti nuo nuskaitymo"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annos Archyvas valdo <a %(worldcat)s>OCLC (WorldCat) metaduomenų</a> kolekciją"

msgid "page.datasets.unified_database.title"
msgstr "Vieninga duomenų bazė"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Mes sujungiame visus aukščiau paminėtus šaltinius į vieną vieningą duomenų bazę, kurią naudojame šiai svetainei aptarnauti. Ši vieninga duomenų bazė nėra tiesiogiai prieinama, tačiau kadangi Anna’s Archive yra visiškai atviro kodo, ją galima gana lengvai <a %(a_generated)s>sugeneruoti</a> arba <a %(a_downloaded)s>atsisiųsti</a> kaip ElasticSearch ir MariaDB duomenų bazes. Skriptai tame puslapyje automatiškai atsisiųs visus reikalingus metaduomenis iš aukščiau paminėtų šaltinių."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Jei norite išnagrinėti mūsų duomenis prieš paleisdami tuos skriptus lokaliai, galite peržiūrėti mūsų JSON failus, kurie toliau susieja su kitais JSON failais. <a %(a_json)s>Šis failas</a> yra geras pradžios taškas."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Pritaikyta iš mūsų <a %(a_href)s>tinklaraščio įrašo</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> yra didžiulė nuskaitytų knygų duomenų bazė, sukurta <a %(superstar_link)s>SuperStar Digital Library Group</a>. Dauguma jų yra akademinės knygos, nuskaitytos siekiant jas padaryti prieinamas universitetams ir bibliotekoms skaitmeniniu formatu. Mūsų anglakalbei auditorijai <a %(princeton_link)s>Princeton</a> ir <a %(uw_link)s>Vašingtono universitetas</a> turi gerus apžvalginius straipsnius. Taip pat yra puikus straipsnis, suteikiantis daugiau informacijos: <a %(article_link)s>„Kinų knygų skaitmeninimas: SuperStar DuXiu Scholar paieškos variklio atvejo analizė“</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Duxiu knygos ilgą laiką buvo piratuojamos Kinijos internete. Paprastai jas perpardavėjai parduoda už mažiau nei dolerį. Jos dažniausiai platinamos naudojant Kinijos „Google Drive“ ekvivalentą, kuris dažnai buvo nulaužtas, kad būtų galima padidinti saugojimo vietą. Kai kurios techninės detalės pateikiamos <a %(link1)s>čia</a> ir <a %(link2)s>čia</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Nors knygos buvo pusiau viešai platinamos, jas gana sunku gauti dideliais kiekiais. Tai buvo aukštai mūsų darbų sąraše, ir tam skyrėme kelis mėnesius pilno darbo laiko. Tačiau 2023 m. pabaigoje neįtikėtinas, nuostabus ir talentingas savanoris susisiekė su mumis, pranešdamas, kad jau atliko visą šį darbą — didelėmis sąnaudomis. Jie pasidalino visa kolekcija su mumis, nesitikėdami nieko mainais, išskyrus ilgalaikio išsaugojimo garantiją. Tikrai nepaprasta."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Ištekliai"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Iš viso failų: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Bendras failų dydis: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Failai, atkartojami Annos Archyve: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Paskutinį kartą atnaujinta: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrentai pagal Annos Archyvą"

msgid "page.datasets.common.aa_example_record"
msgstr "Pavyzdinis įrašas Annos Archyve"

msgid "page.datasets.duxiu.blog_post"
msgstr "Mūsų tinklaraščio įrašas apie šiuos duomenis"

msgid "page.datasets.common.import_scripts"
msgstr "Skriptai metaduomenų importavimui"

msgid "page.datasets.common.aac"
msgstr "Annos Archyvo Konteinerių formatas"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Daugiau informacijos iš mūsų savanorių (neapdoroti užrašai):"

msgid "page.datasets.ia.title"
msgstr "IA kontroliuojamas skaitmeninis skolinimas"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Šis duomenų rinkinys glaudžiai susijęs su <a %(a_datasets_openlib)s>Open Library duomenų rinkiniu</a>. Jame yra visų metaduomenų ir didelės dalies failų iš IA’s Controlled Digital Lending Library nuskaitymas. Atnaujinimai išleidžiami <a %(a_aac)s>Anna’s Archive Containers formatu</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Šie įrašai yra tiesiogiai susiję su Open Library duomenų rinkiniu, tačiau taip pat yra įrašų, kurių nėra Open Library. Taip pat turime keletą duomenų failų, kuriuos per daugelį metų nuskaitydavo bendruomenės nariai."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Kolekcija susideda iš dviejų dalių. Jums reikia abiejų dalių, kad gautumėte visus duomenis (išskyrus pakeistus torrentus, kurie yra perbraukti torrentų puslapyje)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "mūsų pirmasis leidimas, prieš standartizuojant <a %(a_aac)s>Annės Archyvo Konteinerių (AAC) formatą</a>. Sudėtyje yra metaduomenys (json ir xml formatu), pdf failai (iš acsm ir lcpdf skaitmeninių skolinimo sistemų) ir viršelių miniatiūros."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementiniai nauji leidimai, naudojant AAC. Sudėtyje yra tik metaduomenys su laiko žymėmis po 2023-01-01, nes likusią dalį jau apima „ia“. Taip pat visi pdf failai, šį kartą iš acsm ir „bookreader“ (IA internetinio skaitytuvo) skolinimo sistemų. Nepaisant to, kad pavadinimas nėra visiškai tikslus, mes vis tiek įtraukiame bookreader failus į ia2_acsmpdf_files kolekciją, nes jie yra tarpusavyje nesuderinami."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Pagrindinė %(source)s svetainė"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Skaitmeninė Skolinimo Biblioteka"

msgid "page.datasets.common.metadata_docs"
msgstr "Metaduomenų dokumentacija (dauguma laukų)"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN šalių informacija"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Tarptautinė ISBN agentūra reguliariai skelbia diapazonus, kuriuos ji paskyrė nacionalinėms ISBN agentūroms. Iš to galime nustatyti, kuriai šaliai, regionui ar kalbos grupei priklauso šis ISBN. Šiuo metu šiuos duomenis naudojame netiesiogiai, per <a %(a_isbnlib)s>isbnlib</a> Python biblioteką."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Ištekliai"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Paskutinį kartą atnaujinta: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN svetainė"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metaduomenys"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Dėl skirtingų Library Genesis šakų istorijos žr. puslapį <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li turi daugumą tų pačių turinio ir metaduomenų kaip ir Libgen.rs, bet turi keletą papildomų kolekcijų, būtent komiksus, žurnalus ir standartinius dokumentus. Jis taip pat integravo <a %(a_scihub)s>Sci-Hub</a> į savo metaduomenis ir paieškos variklį, kurį naudojame savo duomenų bazėje."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Šios bibliotekos metaduomenys yra laisvai prieinami <a %(a_libgen_li)s>libgen.li</a>. Tačiau šis serveris yra lėtas ir nepalaiko nutrūkusių ryšių atnaujinimo. Tie patys failai taip pat yra prieinami <a %(a_ftp)s>FTP serveryje</a>, kuris veikia geriau."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrentai yra prieinami daugumai papildomo turinio, ypač komiksų, žurnalų ir standartinių dokumentų torrentai buvo išleisti bendradarbiaujant su Annos Archyvu."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Grožinės literatūros kolekcija turi savo torrentus (atskirtus nuo <a %(a_href)s>Libgen.rs</a>) pradedant nuo %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Pasak Libgen.li administratoriaus, „fiction_rus“ (rusų grožinės literatūros) kolekcija turėtų būti padengta reguliariai išleidžiamais torrentais iš <a %(a_booktracker)s>booktracker.org</a>, ypač <a %(a_flibusta)s>flibusta</a> ir <a %(a_librusec)s>lib.rus.ec</a> torrentais (kuriuos mes veidrodžiuojame <a %(a_torrents)s>čia</a>, nors dar nenustatėme, kurie torrentai atitinka kuriuos failus)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistiką apie visas kolekcijas galima rasti <a %(a_href)s>libgen svetainėje</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Atrodo, kad negrožinė literatūra taip pat pasikeitė, tačiau be naujų torrentų. Panašu, kad tai įvyko nuo 2022 metų pradžios, nors mes to nepatvirtinome."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Tam tikri diapazonai be torrentų (pvz., grožinės literatūros diapazonai f_3463000 iki f_4260000) greičiausiai yra Z-Library (ar kiti dublikatai) failai, nors galbūt norėtume atlikti tam tikrą deduplikaciją ir sukurti torrentus lgli-unikaliems failams šiuose diapazonuose."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Atkreipkite dėmesį, kad torrent failai, nurodantys „libgen.is“, yra aiškiai <a %(a_libgen)s>Libgen.rs</a> veidrodžiai („.is“ yra kitas domenas, naudojamas Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Naudingas šaltinis naudojant metaduomenis yra <a %(a_href)s>šis puslapis</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Grožinės literatūros torrentai Annos Archyve"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Komiksų torrentai Annos Archyve"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Žurnalų torrentai Annos Archyve"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standartinių dokumentų torrentai Annos Archyve"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Rusų grožinės literatūros torrentai Annos Archyve"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metaduomenys"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metaduomenys per FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metaduomenų laukų informacija"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Kitų torrentų veidrodis (ir unikalūs grožinės literatūros bei komiksų torrentai)"

msgid "page.datasets.libgen_li.forum"
msgstr "Diskusijų forumas"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Mūsų tinklaraščio įrašas apie komiksų išleidimą"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Trumpa skirtingų Library Genesis (arba „Libgen“) šakų istorija yra tokia, kad laikui bėgant, skirtingi Library Genesis dalyviai susipyko ir pasuko skirtingais keliais."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "„.fun“ versiją sukūrė originalus įkūrėjas. Ji yra atnaujinama, siekiant sukurti naują, labiau paskirstytą versiją."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "„.rs“ versija turi labai panašius duomenis ir nuolat išleidžia savo kolekciją dideliais torrentais. Ji yra apytiksliai padalinta į „grožinės literatūros“ ir „ne grožinės literatūros“ skyrius."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Iš pradžių adresu „http://gen.lib.rus.ec“."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>„.li“ versija</a> turi didžiulę komiksų kolekciją, taip pat kitą turinį, kuris (dar) nėra prieinamas dideliais torrentais. Ji turi atskirą grožinės literatūros knygų torrentų kolekciją ir savo duomenų bazėje turi <a %(a_scihub)s>Sci-Hub</a> metaduomenis."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Pagal šį <a %(a_mhut)s>forumo įrašą</a>, Libgen.li iš pradžių buvo talpinamas adresu „http://free-books.dontexist.com“."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> tam tikra prasme taip pat yra Library Genesis šaka, nors jie savo projektui naudojo kitą pavadinimą."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Šis puslapis yra apie „.rs“ versiją. Ji yra žinoma dėl nuolatinio tiek metaduomenų, tiek viso savo knygų katalogo turinio publikavimo. Jos knygų kolekcija yra padalinta į grožinės ir ne grožinės literatūros dalis."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Naudingas šaltinis naudojant metaduomenis yra <a %(a_metadata)s>šis puslapis</a> (blokuoja IP diapazonus, gali prireikti VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Nuo 2024-03, nauji torrentai skelbiami <a %(a_href)s>šiame forumo temoje</a> (blokuoja IP diapazonus, gali prireikti VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Ne grožinės literatūros torrentai Annos Archyve"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Grožinės literatūros torrentai Annos Archyve"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metaduomenys"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metaduomenų laukų informacija"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Ne grožinės literatūros torrentai"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Grožinės literatūros torrentai"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskusijų forumas"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Annos Archyvo torrentai (knygų viršeliai)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Mūsų tinklaraštis apie knygų viršelių išleidimą"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis yra žinomas dėl savo dosnumo, nes jau leidžia savo duomenis masiškai per torrentus. Mūsų Libgen kolekcija susideda iš papildomų duomenų, kurių jie tiesiogiai neišleidžia, bendradarbiaujant su jais. Didelis ačiū visiems, kurie dirbo su Library Genesis!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Išleidimas 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Šis <a %(blog_post)s>pirmasis išleidimas</a> yra gana mažas: apie 300GB knygų viršelių iš Libgen.rs šakos, tiek grožinės, tiek ne grožinės literatūros. Jie yra organizuoti taip pat, kaip jie pasirodo libgen.rs, pvz.:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s ne grožinės literatūros knygai."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s grožinės literatūros knygai."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Kaip ir su Z-Library kolekcija, mes visus juos sudėjome į didelį .tar failą, kurį galima montuoti naudojant <a %(a_ratarmount)s>ratarmount</a>, jei norite tiesiogiai aptarnauti failus."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> yra nuosavybinė duomenų bazė, kurią valdo ne pelno siekianti organizacija <a %(a_oclc)s>OCLC</a>, kuri renka metaduomenų įrašus iš bibliotekų visame pasaulyje. Tai tikriausiai yra didžiausia bibliotekų metaduomenų kolekcija pasaulyje."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "2023 m. spalio mėn., pradinė versija:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "2023 metų spalį mes <a %(a_scrape)s>išleidome</a> išsamų OCLC (WorldCat) duomenų bazės nuskaitymą, <a %(a_aac)s>Anna’s Archive Containers formatu</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Annos Archyvo torrentai"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Mūsų tinklaraščio įrašas apie šiuos duomenis"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library yra atviro kodo projektas, kurį vykdo Internet Archive, siekiant kataloguoti visas pasaulio knygas. Jis turi vieną didžiausių pasaulyje knygų skenavimo operacijų ir turi daug knygų, prieinamų skaitmeniniam skolinimui. Jo knygų metaduomenų katalogas yra laisvai prieinamas atsisiuntimui ir yra įtrauktas į Anna’s Archive (nors šiuo metu ne paieškoje, išskyrus jei aiškiai ieškote Open Library ID)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metaduomenys"

msgid "page.datasets.isbndb.release1.title"
msgstr "1 Išleidimas (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Tai yra daugybės skambučių į isbndb.com iškrova 2022 m. rugsėjo mėn. Bandėme apimti visus ISBN diapazonus. Tai yra apie 30,9 milijono įrašų. Jų svetainėje teigiama, kad jie iš tikrųjų turi 32,6 milijono įrašų, todėl galbūt kažkaip praleidome kai kuriuos, arba <em>jie</em> gali daryti kažką neteisingai."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON atsakymai yra beveik neapdoroti iš jų serverio. Viena duomenų kokybės problema, kurią pastebėjome, yra ta, kad ISBN-13 numeriams, kurie prasideda kitu prefiksu nei „978-“, jie vis tiek įtraukia „isbn“ lauką, kuris tiesiog yra ISBN-13 numeris su pirmomis 3 skaitmenimis nukirptas (ir patikros skaitmuo perskaičiuotas). Tai akivaizdžiai neteisinga, bet taip jie tai daro, todėl mes to nekeitėme."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Kita potenciali problema, su kuria galite susidurti, yra ta, kad „isbn13“ laukas turi dublikatų, todėl negalite jo naudoti kaip pirminio rakto duomenų bazėje. „isbn13“+„isbn“ laukai kartu atrodo unikalūs."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Dėl informacijos apie Sci-Hub, prašome apsilankyti jo <a %(a_scihub)s>oficialioje svetainėje</a>, <a %(a_wikipedia)s>Vikipedijos puslapyje</a> ir šiame <a %(a_radiolab)s>podkasto interviu</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Atkreipkite dėmesį, kad Sci-Hub buvo <a %(a_reddit)s>užšaldytas nuo 2021 metų</a>. Jis buvo užšaldytas ir anksčiau, tačiau 2021 metais buvo pridėta keletas milijonų straipsnių. Vis dėlto, kai kurie riboti straipsniai vis dar pridedami į Libgen „scimag“ kolekcijas, nors jų nepakanka, kad būtų verta kurti naujus masinio atsisiuntimo torrentus."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Mes naudojame Sci-Hub metaduomenis, kuriuos pateikia <a %(a_libgen_li)s>Libgen.li</a> savo „scimag“ kolekcijoje. Taip pat naudojame <a %(a_dois)s>dois-2022-02-12.7z</a> duomenų rinkinį."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Atkreipkite dėmesį, kad „smarch“ torrentai yra <a %(a_smarch)s>pasenę</a> ir todėl nėra įtraukti į mūsų torrentų sąrašą."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrentai esantys Annos Archyve"

msgid "page.datasets.scihub.link_metadata"
msgstr "Metaduomenys ir torrentai"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrentai esantys Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrentai esantys Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Naujienos Reddite"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Vikipedijos puslapis"

msgid "page.datasets.scihub.link_podcast"
msgstr "Podkasto interviu"

msgid "page.datasets.upload.title"
msgstr "Įkėlimai į Annos Archyvą"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Apžvalga iš <a %(a1)s>duomenų rinkinių puslapio</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Įvairūs mažesni arba vienkartiniai šaltiniai. Skatiname žmones pirmiausia įkelti į kitas šešėlines bibliotekas, tačiau kartais žmonės turi kolekcijų, kurios yra per didelės kitiems peržiūrėti, bet nepakankamai didelės, kad būtų verta sukurti atskirą kategoriją."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "„Įkėlimų“ kolekcija yra suskirstyta į mažesnes subkolekcijas, kurios nurodytos AACID ir torrentų pavadinimuose. Visos subkolekcijos pirmiausia buvo deduplikuotos su pagrindine kolekcija, nors „upload_records“ metaduomenų JSON failuose vis dar yra daug nuorodų į originalius failus. Dauguma subkolekcijų taip pat buvo pašalinti ne knygų failai, ir paprastai <em>ne</em> pažymėti „upload_records“ JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Daugelis subkolekcijų pačios susideda iš sub-sub-kolekcijų (pvz., iš skirtingų originalių šaltinių), kurios yra atvaizduojamos kaip katalogai „filepath“ laukuose."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Subkolekcijos yra:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkolekcija"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Pastabos"

msgid "page.datasets.upload.action.browse"
msgstr "naršyti"

msgid "page.datasets.upload.action.search"
msgstr "ieškoti"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Iš <a %(a_href)s>aaaaarg.fail</a>. Atrodo gana išsamus. Iš mūsų savanorio „cgiym“."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Iš <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrento. Turi gana didelį sutapimą su esamomis straipsnių kolekcijomis, bet labai mažai MD5 atitikmenų, todėl nusprendėme išlaikyti visą."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "„iRead eBooks“ (fonetiškai „ai rit i-books“; airitibooks.com) nuskaitymas, atliktas savanorio „j“. Atitinka „airitibooks“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Iš kolekcijos <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Iš dalies iš originalaus šaltinio, iš dalies iš the-eye.eu, iš dalies iš kitų veidrodžių."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Iš privačios knygų torrento svetainės, <a %(a_href)s>Bibliotik</a> (dažnai vadinamos „Bib“), kurios knygos buvo sujungtos į torrentus pagal pavadinimą (A.torrent, B.torrent) ir platinamos per the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Iš mūsų savanorio „bpb9v“. Daugiau informacijos apie <a %(a_href)s>CADAL</a> rasite mūsų <a %(a_duxiu)s>DuXiu duomenų rinkinio puslapyje</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Daugiau iš mūsų savanorio „bpb9v“, daugiausia DuXiu failai, taip pat aplankai „WenQu“ ir „SuperStar_Journals“ (SuperStar yra kompanija, kuri valdo DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Iš mūsų savanorio „cgiym“, kinų tekstai iš įvairių šaltinių (atstovaujami kaip subkatalogai), įskaitant iš <a %(a_href)s>China Machine Press</a> (didžiojo kinų leidėjo)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Ne kinų kolekcijos (atstovaujamos kaip subkatalogai) iš mūsų savanorio „cgiym“."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Knygų apie Kinijos architektūrą nuskaitymas, atliktas savanorio „cm“: „Aš tai gavau išnaudodamas leidyklos tinklo pažeidžiamumą, tačiau ta spraga jau uždaryta“. Atitinka „chinese_architecture“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Knygos iš akademinio leidėjo <a %(a_href)s>De Gruyter</a>, surinktos iš kelių didelių torrentų."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape iš <a %(a_href)s>docer.pl</a>, lenkų failų dalijimosi svetainės, orientuotos į knygas ir kitus rašytinius darbus. Scrape atliktas 2023 m. pabaigoje savanorio „p“. Neturime geros metaduomenų iš originalios svetainės (netgi failų plėtinių), bet filtravome knygų tipo failus ir dažnai galėjome išgauti metaduomenis iš pačių failų."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, tiesiogiai iš DuXiu, surinkti savanorio „w“. Tik naujausios DuXiu knygos yra tiesiogiai prieinamos per el. knygas, todėl dauguma jų turi būti naujausios."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Likę DuXiu failai iš savanorio „m“, kurie nebuvo DuXiu patentuoto PDG formato (pagrindinis <a %(a_href)s>DuXiu duomenų rinkinys</a>). Surinkti iš daugelio originalių šaltinių, deja, nesaugant tų šaltinių failų keliuose."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Erotinių knygų nuskaitymas, atliktas savanorio „do no harm“. Atitinka „hentai“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Kolekcija, surinkta iš Japonijos Manga leidėjo savanorio „t“."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Pasirinkti Longquan teismų archyvai</a>, pateikti savanorio „c“."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape iš <a %(a_href)s>magzdb.org</a>, Library Genesis sąjungininko (jis yra susietas libgen.rs pagrindiniame puslapyje), bet kuris nenorėjo tiesiogiai pateikti savo failų. Gauta savanorio „p“ 2023 m. pabaigoje."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Įvairūs maži įkėlimai, per maži kaip atskira subkolekcija, bet atstovaujami kaip katalogai."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Elektroninės knygos iš AvaxHome, Rusijos failų dalijimosi svetainės."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Laikraščių ir žurnalų archyvas. Atitinka „newsarch_magz“ metadata <a %(a1)s><q>Kitų metadata nuskaitymų</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Filosofijos dokumentacijos centro</a> nuskaitymas."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Kolekcija savanorio „o“, kuris surinko lenkų knygas tiesiogiai iš originalių išleidimo („scene“) svetainių."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Sujungtos kolekcijos iš <a %(a_href)s>shuge.org</a> savanorių „cgiym“ ir „woz9ts“."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>„Trantoro Imperijos Biblioteka“</a> (pavadinta pagal išgalvotą biblioteką), nuskaityta 2022 m. savanorio „t“."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-kolekcijos (atstovaujamos kaip katalogai) iš savanorio „woz9ts“: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (autorius <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taivane), mebook (mebook.cc, 我的小书屋, mano mažoji knygų kambarys — woz9ts: „Ši svetainė daugiausia dėmesio skiria aukštos kokybės el. knygų failų dalijimuisi, kai kuriuos iš jų pats savininkas suformatavo. Savininkas buvo <a %(a_arrested)s>suimtas</a> 2019 m. ir kažkas sukūrė jo dalintų failų kolekciją.“)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Likę DuXiu failai iš savanorio „woz9ts“, kurie nebuvo DuXiu patentuotu PDG formatu (dar turi būti konvertuoti į PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrentai iš Annos Archyvo"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library nuskaitymas"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library turi savo šaknis <a %(a_href)s>Library Genesis</a> bendruomenėje ir iš pradžių buvo sukurta naudojant jų duomenis. Nuo tada ji labai profesionalizavosi ir turi daug modernesnę sąsają. Todėl jie gali gauti daug daugiau aukų, tiek piniginių, kad galėtų toliau tobulinti savo svetainę, tiek naujų knygų aukų. Jie sukaupė didelę kolekciją, papildančią Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Atnaujinimas 2023 m. vasario mėn."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "2022 m. pabaigoje tariami Z-Library įkūrėjai buvo suimti, o domenai buvo konfiskuoti Jungtinių Valstijų valdžios institucijų. Nuo tada svetainė pamažu vėl pradeda veikti internete. Nežinoma, kas šiuo metu ją valdo."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Kolekcija susideda iš trijų dalių. Žemiau pateikiami originalūs aprašymo puslapiai pirmosioms dviem dalims. Norint gauti visus duomenis, reikia visų trijų dalių (išskyrus pakeistus torrentus, kurie yra perbraukti torrentų puslapyje)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: mūsų pirmasis leidimas. Tai buvo pats pirmasis leidimas, kuris tada buvo vadinamas „Pirate Library Mirror“ („pilimi“)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: antrasis leidimas, šį kartą su visais failais, supakuotais į .tar failus."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: papildomi nauji leidimai, naudojant <a %(a_href)s>Anna’s Archive Containers (AAC) formatą</a>, dabar išleisti bendradarbiaujant su Z-Library komanda."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrentai pagal Anna’s Archive (metaduomenys + turinys)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Pavyzdinis įrašas Anna’s Archive (originali kolekcija)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Pavyzdinis įrašas Anna’s Archive („zlib3“ kolekcija)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Pagrindinė svetainė"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domenas"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Įrašas tinklaraštyje apie 1 leidimą"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Įrašas tinklaraštyje apie 2 leidimą"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib leidimai (originalūs aprašymo puslapiai)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "1 leidimas (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Pradinis veidrodis buvo kruopščiai gautas per 2021 ir 2022 metus. Šiuo metu jis yra šiek tiek pasenęs: jis atspindi kolekcijos būklę 2021 m. birželio mėn. Ateityje jį atnaujinsime. Šiuo metu mes sutelkiame dėmesį į šio pirmojo leidimo išleidimą."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Kadangi Library Genesis jau yra išsaugota su viešais torrentais ir yra įtraukta į Z-Library, 2022 m. birželio mėn. atlikome pagrindinį deduplikavimą prieš Library Genesis. Tam naudojome MD5 maišas. Tikėtina, kad bibliotekoje yra daug daugiau pasikartojančio turinio, pvz., keli failų formatai su ta pačia knyga. Tai sunku tiksliai aptikti, todėl to nedarome. Po deduplikavimo liko daugiau nei 2 milijonai failų, kurių bendra apimtis yra beveik 7 TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Kolekcija susideda iš dviejų dalių: MySQL „.sql.gz“ metaduomenų iškrovos ir 72 torrentų failų, kurių kiekvienas yra apie 50-100 GB. Metaduomenyse yra duomenys, kuriuos pranešė Z-Library svetainė (pavadinimas, autorius, aprašymas, failo tipas), taip pat faktinis failo dydis ir md5sum, kuriuos mes pastebėjome, nes kartais jie nesutampa. Atrodo, kad yra failų diapazonų, kuriuose pati Z-Library turi neteisingus metaduomenis. Taip pat galime turėti neteisingai atsisiųstų failų kai kuriais atskirais atvejais, kuriuos ateityje bandysime aptikti ir ištaisyti."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Dideli torrent failai turi tikruosius knygų duomenis, su Z-Library ID kaip failo pavadinimu. Failų plėtinius galima atkurti naudojant metaduomenų iškrovą."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Kolekcija yra mišri, apimanti tiek negrožinę, tiek grožinę literatūrą (neatskirta kaip Library Genesis). Kokybė taip pat labai skiriasi."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Šis pirmasis leidimas dabar yra visiškai prieinamas. Atkreipkite dėmesį, kad torrent failai yra prieinami tik per mūsų Tor veidrodį."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Leidimas 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Mes surinkome visas knygas, kurios buvo pridėtos į Z-Library tarp mūsų paskutinio veidrodžio ir 2022 m. rugpjūčio. Taip pat grįžome ir surinkome kai kurias knygas, kurias praleidome pirmą kartą. Iš viso ši nauja kolekcija yra apie 24TB. Vėlgi, ši kolekcija yra deduplikuota prieš Library Genesis, nes jau yra torrent failai, prieinami šiai kolekcijai."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Duomenys yra organizuoti panašiai kaip pirmasis leidimas. Yra MySQL „.sql.gz“ metaduomenų iškrova, kuri taip pat apima visus pirmojo leidimo metaduomenis, taip jį pakeisdama. Taip pat pridėjome keletą naujų stulpelių:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: ar šis failas jau yra Library Genesis, tiek negrožinės, tiek grožinės literatūros kolekcijoje (atitinka pagal md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: kuriame torrente yra šis failas."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: nustatyta, kai nepavyko atsisiųsti knygos."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mes tai minėjome paskutinį kartą, bet tik norėdami paaiškinti: „filename“ ir „md5“ yra tikrosios failo savybės, o „filename_reported“ ir „md5_reported“ yra tai, ką mes surinkome iš Z-Library. Kartais šie duomenys nesutampa, todėl įtraukėme abu."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Šiam leidimui pakeitėme koduotę į „utf8mb4_unicode_ci“, kuri turėtų būti suderinama su senesnėmis MySQL versijomis."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Duomenų failai yra panašūs į praėjusį kartą, nors jie yra daug didesni. Mes tiesiog negalėjome vargintis kurdami daugybę mažesnių torrent failų. „pilimi-zlib2-0-14679999-extra.torrent“ turi visus failus, kuriuos praleidome paskutiniame leidime, o kiti torrent failai yra visi nauji ID diapazonai. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Atnaujinimas %(date)s:</strong> Mes padarėme daugumą mūsų torrent failų per didelius, todėl torrent klientai turėjo sunkumų. Mes juos pašalinome ir išleidome naujus torrent failus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Atnaujinimas %(date)s:</strong> Vis dar buvo per daug failų, todėl mes juos supakavome į tar failus ir vėl išleidome naujus torrent failus."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Leidimas 2 priedas (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Tai yra vienas papildomas torrent failas. Jame nėra jokios naujos informacijos, tačiau jame yra duomenų, kuriuos gali užtrukti apskaičiuoti. Tai patogu turėti, nes atsisiųsti šį torrent failą dažnai yra greičiau nei apskaičiuoti iš naujo. Konkrečiai, jame yra SQLite indeksai tar failams, skirti naudoti su <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Dažniausiai užduodami klausimai (DUK)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Kas yra Annos Archyvas?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anos archyvas</span> yra pelno nesiekiantis projektas, kurio tikslai yra du:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Išsaugojimas:</strong> visų žmonijos žinių ir kultūros išsaugojimas.</li><li><strong>Prieiga:</strong> padaryti šias žinias ir kultūrą prieinamas bet kuriam pasaulio gyventojui.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Visas mūsų <a %(a_code)s>kodas</a> ir <a %(a_datasets)s>duomenys</a> yra visiškai atviro kodo."

msgid "page.home.preservation.header"
msgstr "Išsaugojimas"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Mes saugome knygas, straipsnius, komiksus, žurnalus ir dar daugiau, sujungdami šias medžiagas iš įvairių <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">šešėlinių bibliotekų</a>, oficialių bibliotekų ir kitų kolekcijų į vieną vietą. Visi šie duomenys yra saugomi amžinai, nes juos lengva kopijuoti dideliais kiekiais — naudojant torrentus — todėl visame pasaulyje yra daug kopijų. Kai kurios šešėlinės bibliotekos tai jau daro pačios (pvz., Sci-Hub, Library Genesis), o Annos Archyvas „išlaisvina“ kitas bibliotekas, kurios nesiūlo masinio platinimo (pvz., Z-Library) arba visai nėra šešėlinės bibliotekos (pvz., Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Šis platus platinimas kartu su atvirojo kodo programine įranga daro mūsų svetainę atsparią pašalinimams ir užtikrina ilgalaikį žmonijos žinių ir kultūros išsaugojimą. Sužinokite daugiau apie <a href=\"/datasets\">mūsų duomenų rinkinius</a>."

msgid "page.home.preservation.label"
msgstr "Apskaičiavome, kad išsaugojome apie <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5 %% pasaulio knygų </a>."

msgid "page.home.access.header"
msgstr "Prieiga"

msgid "page.home.access.text"
msgstr "Dirbame su partneriais, kad mūsų kolekcijos būtų lengvai ir laisvai prieinamos visiems. Mes tikime, kad kiekvienas turi teisę į kolektyvinę žmonijos išmintį. Ir <a %(a_search)s>ne autorių sąskaita</a>."

msgid "page.home.access.label"
msgstr "Atsisiuntimai kas valandą paskutines 30 dienų. Valandos vidurkis: %(hourly)s. Dienos vidurkis: %(daily)s."

msgid "page.about.text2"
msgstr "Mes stipriai tikime laisvu informacijos platinimu ir žinių bei kultūros išsaugojimu. Su šia paieškos sistema, mes statome ant milžinų pečių. We giliai gerbiame sunkų darbą atliktą žmonių kurie sukūrė įvairias šešėlines bibliotekas ir tikimės, kad ši paieškos sistema padidins jų pasiekiamumą."

msgid "page.about.text3"
msgstr "Norint sužinoti žinias apie mūsų progresą, sekite Anna <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> arba <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Dėl klausimų galite susisiekti su Anna %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Kaip galiu padėti?"

msgid "page.about.help.text"
msgstr "<li>1. Sekite mus <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ar <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Platinkite žinią apie Anna's Archive Twitter, Reddit, Tiktok, Instagram, vietinėje kavinėje, bibliotekoje ar kažin kur jūs einate! Mes netikime informacijos uždarimu - jeigu būsime sustabdyti, atsirasime kitur, kadangi visas mūsų kodas ir duomenys yra visiškai atviri.</li><li>3. Jeigu galite <a href=\"/donate\">paaukokite</a>.</li><li>4. Padėkite <a href=\"https://translate.annas-software.org/\">išversti</a> mūsų svetainę į skirtingas kalbas. </li><li>5. Jeigu esate programuotojas, galite prisidėti prie mūsų <a href=\"https://annas-software.org/\">atviro kodo</a>, arba bendrinant (angl. seeding) mūsų <a href=\"/datasets\">torrent</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Dabar taip pat turime sinchronizuotą Matrix kanalą adresu %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Jei esate saugumo tyrėjas, galime panaudoti jūsų įgūdžius tiek puolimui, tiek gynybai. Apsilankykite mūsų <a %(a_security)s>Saugumo</a> puslapyje."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Mes ieškome ekspertų mokėjimų anoniminiams prekybininkams srityje. Ar galite padėti mums pridėti patogesnių būdų aukoti? PayPal, WeChat, dovanų kortelės. Jei pažįstate ką nors, prašome susisiekti su mumis."

#, fuzzy
msgid "page.about.help.text8"
msgstr "Mes visada ieškome daugiau serverio pajėgumų."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Jūs galite padėti pranešdami apie failų problemas, palikdami komentarus ir kurdami sąrašus tiesiog šioje svetainėje. Taip pat galite padėti <a %(a_upload)s>įkeldami daugiau knygų</a> arba taisydami esamų knygų failų problemas ar formatavimą."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Sukurkite arba padėkite prižiūrėti Vikipedijos puslapį „Annos Archyvas“ savo kalba."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Mes ieškome galimybių talpinti mažas, skoningas reklamas. Jei norėtumėte reklamuotis Anna’s Archive, praneškite mums."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Mes norėtume, kad žmonės nustatytų <a %(a_mirrors)s>veidrodžius</a>, ir mes finansiškai palaikysime tai."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Daugiau informacijos apie savanoriavimą rasite mūsų <a %(a_volunteering)s>Savanoriavimo ir atlygio</a> puslapyje."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Kodėl lėti atsisiuntimai tokie lėti?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Mes tiesiog neturime pakankamai išteklių, kad visiems pasaulyje suteiktume didelės spartos atsisiuntimus, nors labai norėtume. Jei turtingas rėmėjas norėtų mums tai suteikti, tai būtų nuostabu, bet kol kas stengiamės iš visų jėgų. Esame ne pelno siekiantis projektas, kuris vos išsilaiko iš aukų."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Štai kodėl mes įdiegėme dvi nemokamų atsisiuntimų sistemas su mūsų partneriais: bendri serveriai su lėtais atsisiuntimais ir šiek tiek greitesni serveriai su laukimo sąrašu (siekiant sumažinti tuo pačiu metu atsisiunčiančių žmonių skaičių)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Mes taip pat turime <a %(a_verification)s>naršyklės patvirtinimą</a> lėtiems atsisiuntimams, nes kitaip botai ir skreperiai juos piktnaudžiautų, dar labiau lėtindami teisėtų vartotojų patirtį."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Atkreipkite dėmesį, kad naudojant Tor naršyklę gali tekti koreguoti savo saugumo nustatymus. Žemiausiuose nustatymuose, vadinamuose „Standartinis“, Cloudflare turniketo iššūkis pavyksta. Aukštesniuose nustatymuose, vadinamuose „Saugiau“ ir „Saugiausias“, iššūkis nepavyksta."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Kartais didelių failų atsisiuntimai gali nutrūkti viduryje. Rekomenduojame naudoti atsisiuntimo tvarkyklę (pvz., JDownloader), kad automatiškai atnaujintumėte didelius atsisiuntimus."

msgid "page.donate.faq.title"
msgstr "Aukojimo DUK"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Ar narystės automatiškai atsinaujina?</div> Narystės automatiškai <strong>neatsinaujina</strong>. Jūs galite prisijungti kiek norite ilgai ar trumpai."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Ar galiu atnaujinti savo narystę arba gauti kelias narystes?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Ar turite kitokių mokėjimo metodų?</div>Kol kas ne. Daugelis žmonių nenori, kad tokie archyvai egzistuotų, todėl mes turime būti atsargūs. Jeigu galite mums padėti mums pridėti kitokius (patogesnius) mokėjimo metodus, susisiekite %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Ką reiškia mėnesio diapazonai?</div> Galite pasiekti diapazono apatinę ribą pritaikydami visas nuolaidas, pvz., pasirinkdami ilgesnį nei mėnesio laikotarpį."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Kam išleidžiate aukas?</div> 100 %% skiriama pasaulio žinioms ir kultūrai išsaugoti ir padaryti jas prieinamas. Šiuo metu daugiausia išleidžiame serveriams, saugyklai ir pralaidumui. Pinigų neduodame nė vienam komandos nariui asmeniškai. Bet kokiu atveju tai būtų per daug pavojinga."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Ar galiu atlikti didelę auką?</div> Tai būtų puiku! Norint paaukoti daugiau nei kelis tūkstančius dolerių, prašome susisiekti su mumis tiesiogiai %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Ar galiu paaukoti netapdamas nariu?</div> Žinoma. Mes priimame bet kokio dydžio aukas šiuo Monero (XMR) adresu: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Kaip įkelti naujas knygas?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Arba galite įkelti juos į Z-Library <a %(a_upload)s>čia</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Mažiems įkėlimams (iki 10 000 failų) prašome įkelti juos į abu %(first)s ir %(second)s."

msgid "page.upload.text1"
msgstr "Kol kas siūlome įkelti naujas knygas į Library Genesis. Čia yra <a %(a_guide)s>patogus gidas</a>. Atminkite, kad abi svetainės, kurias indeksuojame šioje svetainėje, yra iš tos pačios įkėlimo sistemos."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.li atveju, pirmiausia prisijunkite prie <a %(a_forum)s>jų forumo</a> su vartotojo vardu %(username)s ir slaptažodžiu %(password)s, tada grįžkite į jų <a %(a_upload_page)s>įkėlimo puslapį</a>."

msgid "common.libgen.email"
msgstr "Jei jūsų el. pašto adresas neveikia Libgen forumuose, rekomenduojame naudoti <a %(a_mail)s>Proton Mail</a> (nemokamas). Taip pat galite <a %(a_manual)s>rankiniu būdu pateikti užklausą</a>, kad paskyra būtų suaktyvinta."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Atkreipkite dėmesį, kad mhut.org blokuoja tam tikrus IP diapazonus, todėl gali prireikti VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Dėl didelių įkėlimų (daugiau nei 10 000 failų), kurie nėra priimami Libgen ar Z-Library, susisiekite su mumis adresu %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Norėdami įkelti akademinius straipsnius, prašome taip pat (be Library Genesis) įkelti į <a %(a_stc_nexus)s>STC Nexus</a>. Jie yra geriausia šešėlinė biblioteka naujiems straipsniams. Mes dar jų neintegravome, bet tai padarysime ateityje. Galite naudoti jų <a %(a_telegram)s>įkėlimo botą Telegram</a> arba susisiekti su adresu, nurodytu jų prisegtoje žinutėje, jei turite per daug failų, kad galėtumėte įkelti šiuo būdu."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Kaip galiu užsisakyti knygas?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Šiuo metu negalime priimti knygų užklausų."

#, fuzzy
msgid "page.request.forums"
msgstr "Prašome pateikti savo užklausas Z-Library arba Libgen forumuose."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Nesiųskite mums el. laiškų su knygų užklausomis."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Ar renkate metaduomenis?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Taip, mes tai darome."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Atsisiunčiau 1984 George Orwell, ar policija ateis į mano duris?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Nesijaudinkite per daug, yra daug žmonių, kurie atsisiunčia iš mūsų nurodytų svetainių, ir labai retai kyla problemų. Tačiau, norėdami būti saugūs, rekomenduojame naudoti VPN (mokamą) arba <a %(a_tor)s>Tor</a> (nemokamą)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Kaip išsaugoti paieškos nustatymus?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Pasirinkite norimus nustatymus, palikite paieškos laukelį tuščią, spustelėkite „Ieškoti“, o tada pažymėkite puslapį naudodami savo naršyklės žymėjimo funkciją."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Ar turite mobiliąją programėlę?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Mes neturime oficialios mobiliosios programėlės, bet galite įdiegti šią svetainę kaip programėlę."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Spustelėkite trijų taškų meniu viršutiniame dešiniajame kampe ir pasirinkite „Pridėti prie pradžios ekrano“."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Spustelėkite mygtuką „Dalintis“ apačioje ir pasirinkite „Pridėti prie pagrindinio ekrano“."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Ar turite API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Mes turime vieną stabilų JSON API nariams, kad gautumėte greitą atsisiuntimo URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentacija JSON viduje)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Kitiems naudojimo atvejams, pvz., visų mūsų failų peržiūrai, individualios paieškos kūrimui ir pan., rekomenduojame <a %(a_generate)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Neapdorotus duomenis galima rankiniu būdu tyrinėti <a %(a_explore)s>per JSON failus</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Mūsų neapdorotų torrentų sąrašą taip pat galima atsisiųsti kaip <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrentų DUK"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Norėčiau padėti sėti, bet neturiu daug vietos diske."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Naudokite <a %(a_list)s>torentų sąrašo generatorių</a>, kad sugeneruotumėte torentų sąrašą, kuriems labiausiai reikia torentavimo, atsižvelgiant į jūsų saugojimo vietos ribas."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torentai yra per lėti; ar galiu atsisiųsti duomenis tiesiogiai iš jūsų?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Taip, žiūrėkite <a %(a_llm)s>LLM duomenų</a> puslapį."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Ar galiu atsisiųsti tik dalį failų, pavyzdžiui, tik tam tikrą kalbą ar temą?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Trumpas atsakymas: ne lengvai."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Ilgas atsakymas:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Dauguma torrentų tiesiogiai turi failus, tai reiškia, kad galite nurodyti torrentų klientams atsisiųsti tik reikiamus failus. Norėdami nustatyti, kuriuos failus atsisiųsti, galite <a %(a_generate)s>generuoti</a> mūsų metaduomenis arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Deja, kai kurios torrentų kolekcijos turi .zip arba .tar failus šaknyje, tokiu atveju turite atsisiųsti visą torrentą, kad galėtumėte pasirinkti atskirus failus."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Tačiau turime <a %(a_ideas)s>keletą idėjų</a> šiam atvejui.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Dar nėra lengvai naudojamų įrankių torrentams filtruoti, tačiau laukiame jūsų indėlio."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Kaip tvarkote dublikatus torrentuose?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Stengiamės išlaikyti minimalų dublikatų ar persidengimų kiekį šiame sąraše esančiuose torrentuose, tačiau tai ne visada įmanoma ir labai priklauso nuo šaltinių bibliotekų politikos. Bibliotekoms, kurios išleidžia savo torrentus, tai nėra mūsų rankose. Torrentams, kuriuos išleidžia „Annos Archyvas“, dublikatų šalinimas atliekamas tik pagal MD5 maišos kodą, tai reiškia, kad skirtingos tos pačios knygos versijos nėra pašalinamos."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Ar galiu gauti torrentų sąrašą kaip JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Taip."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Nematau PDF ar EPUB torrentuose, tik dvejetainius failus? Ką daryti?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Tai iš tikrųjų yra PDF ir EPUB failai, jie tiesiog neturi plėtinio daugelyje mūsų torrentų. Yra dvi vietos, kuriose galite rasti torrent failų metaduomenis, įskaitant failų tipus/plėtinius:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Kiekviena kolekcija ar leidimas turi savo metaduomenis. Pavyzdžiui, <a %(a_libgen_nonfic)s>Libgen.rs torrentai</a> turi atitinkamą metaduomenų bazę, talpinamą Libgen.rs svetainėje. Mes paprastai nuorodą į atitinkamus metaduomenų šaltinius pateikiame kiekvienos kolekcijos <a %(a_datasets)s>duomenų rinkinio puslapyje</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Rekomenduojame <a %(a_generate)s>generuoti</a> arba <a %(a_download)s>atsisiųsti</a> mūsų ElasticSearch ir MariaDB duomenų bazes. Jose yra kiekvieno įrašo Anna’s Archive atitikmenų torrent failams (jei yra), žemėlapis, esantis „torrent_paths“ lauke ElasticSearch JSON formatu."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Kodėl mano torrentų klientas negali atidaryti kai kurių jūsų torrentų failų / magnetinių nuorodų?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Kai kurie torrentų klientai nepalaiko didelių dalių dydžių, kuriuos turi daug mūsų torrentų (naujesniems mes to nebedarome — nors tai yra galiojančios specifikacijos!). Taigi, jei susiduriate su šia problema, pabandykite kitą klientą arba skųskitės savo torrentų kliento kūrėjams."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Ar turite atsakingo atskleidimo programą?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Kviečiame saugumo tyrėjus ieškoti mūsų sistemų pažeidžiamumų. Mes esame atsakingo atskleidimo šalininkai. Susisiekite su mumis <a %(a_contact)s>čia</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Šiuo metu negalime skirti atlygių už klaidų radimą, išskyrus pažeidžiamumus, kurie turi <a %(a_link)s>potencialą pakenkti mūsų anonimiškumui</a>, už kuriuos siūlome atlygius nuo 10 tūkst. iki 50 tūkst. dolerių. Norėtume ateityje pasiūlyti platesnį klaidų radimo atlygių spektrą! Atkreipkite dėmesį, kad socialinės inžinerijos atakos nėra įtrauktos į programos apimtį."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Jei domitės puolamąja sauga ir norite padėti archyvuoti pasaulio žinias ir kultūrą, būtinai susisiekite su mumis. Yra daug būdų, kaip galite padėti."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ar yra daugiau išteklių apie Anna’s Archive?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blogas</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — reguliarios naujienos"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Programinė įranga</a> — mūsų atvirojo kodo kodas"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Versti su Annos programine įranga</a> — mūsų vertimo sistema"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Duomenų rinkiniai</a> — apie duomenis"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatyvūs domenai"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Vikipedija</a> — daugiau apie mus (padėkite atnaujinti šį puslapį arba sukurkite savo kalba!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Kaip pranešti apie autorių teisių pažeidimą?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Mes čia netalpiname jokių autorių teisių saugomų medžiagų. Esame paieškos sistema, todėl indeksuojame tik jau viešai prieinamus metaduomenis. Atsisiųsdami iš šių išorinių šaltinių, rekomenduojame pasitikrinti savo jurisdikcijos įstatymus dėl to, kas yra leidžiama. Mes nesame atsakingi už kitų talpinamą turinį."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Jei turite skundų dėl to, ką matote čia, geriausia būtų susisiekti su originalia svetaine. Mes reguliariai atnaujiname jų pakeitimus mūsų duomenų bazėje. Jei tikrai manote, kad turite galiojantį DMCA skundą, į kurį turėtume reaguoti, prašome užpildyti <a %(a_copyright)s>DMCA / Autorių teisių pažeidimo formą</a>. Mes rimtai žiūrime į jūsų skundus ir atsakysime kuo greičiau."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Nekenčiu, kaip jūs vykdote šį projektą!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Taip pat norėtume priminti visiems, kad visas mūsų kodas ir duomenys yra visiškai atvirojo kodo. Tai unikali savybė tokiems projektams kaip mūsų — mes nežinome jokio kito projekto su panašiai didžiuliu katalogu, kuris taip pat būtų visiškai atvirojo kodo. Labai laukiame visų, kurie mano, kad blogai valdome savo projektą, kad jie paimtų mūsų kodą ir duomenis ir sukurtų savo šešėlinę biblioteką! Mes tai sakome ne iš pykčio ar kažko panašaus — mes nuoširdžiai manome, kad tai būtų nuostabu, nes tai pakeltų visų lygį ir geriau išsaugotų žmonijos palikimą."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Ar turite veikimo laiko stebėjimo įrankį?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Prašome peržiūrėti <a %(a_href)s>šį puikų projektą</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Kaip galiu paaukoti knygas ar kitą fizinę medžiagą?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Prašome siųsti juos į <a %(a_archive)s>Internet Archive</a>. Jie tinkamai juos išsaugos."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Kas yra Ana?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Jūs esate Ana!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Kokios jūsų mėgstamiausios knygos?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Štai keletas knygų, kurios turi ypatingą reikšmę šešėlinių bibliotekų ir skaitmeninio išsaugojimo pasauliui:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Jūs išnaudojote greitų atsisiuntimų limitą šiandien."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Tapkite nariu, kad galėtumėte naudotis greitais atsisiuntimais."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Dabar palaikome Amazon dovanų korteles, kreditines ir debetines korteles, kriptovaliutas, Alipay ir WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Visa duomenų bazė"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Knygos, straipsniai, žurnalai, komiksai, bibliotekos įrašai, metaduomenys, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Paieška"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>sustabdė</a> naujų straipsnių įkėlimą."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB yra Sci-Hub tęsinys."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Tiesioginė prieiga prie %(count)s akademinių straipsnių"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Atidaryti"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Jei esate <a %(a_member)s>narys</a>, naršyklės patikrinimas nereikalingas."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Ilgalaikis archyvas"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Annos Archyve naudojami duomenų rinkiniai yra visiškai atviri ir gali būti veidrodiniai dideliais kiekiais naudojant torrentus. <a %(a_datasets)s>Sužinokite daugiau…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Jūs galite labai padėti, sėdami torrentus. <a %(a_torrents)s>Sužinokite daugiau…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s siuntėjai"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s sėjėjai"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s siuntėjai"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Mes turime didžiausią pasaulyje aukštos kokybės tekstinių duomenų kolekciją. <a %(a_llm)s>Sužinokite daugiau…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Veidrodžiai: kvietimas savanoriams"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Ieškome savanorių"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Būdami ne pelno siekiantis, atvirojo kodo projektas, mes visada ieškome žmonių, kurie galėtų padėti."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Jei valdote aukštos rizikos anoniminį mokėjimo procesorių, prašome susisiekti su mumis. Taip pat ieškome žmonių, norinčių patalpinti skoningas mažas reklamas. Visos pajamos skiriamos mūsų išsaugojimo pastangoms."

msgid "layout.index.header.nav.annasblog"
msgstr "Anos blogas ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS atsisiuntimai"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Visi šio failo atsisiuntimo nuorodos: <a %(a_main)s>Failo pagrindinis puslapis</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Vartai #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(gali tekti pabandyti kelis kartus naudojant IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Norėdami greičiau atsisiųsti ir praleisti naršyklės patikrinimus, <a %(a_membership)s>tapkite nariu</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Norėdami masiškai veidrodinti mūsų kolekciją, apsilankykite <a %(a_datasets)s>Datasets</a> ir <a %(a_torrents)s>Torrents</a> puslapiuose."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM duomenys"

#, fuzzy
msgid "page.llm.intro"
msgstr "Gerai žinoma, kad LLM klesti aukštos kokybės duomenimis. Mes turime didžiausią knygų, straipsnių, žurnalų ir kt. kolekciją pasaulyje, kuri yra viena iš aukščiausios kokybės tekstinių šaltinių."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unikalus mastas ir diapazonas"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Mūsų kolekcijoje yra daugiau nei šimtas milijonų failų, įskaitant akademinius žurnalus, vadovėlius ir žurnalus. Šį mastą pasiekiame sujungdami dideles esamas saugyklas."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Kai kurios mūsų šaltinių kolekcijos jau yra prieinamos dideliais kiekiais (Sci-Hub ir dalys Libgen). Kitus šaltinius išlaisvinome patys. <a %(a_datasets)s>Datasets</a> rodo visą apžvalgą."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Mūsų kolekcijoje yra milijonai knygų, straipsnių ir žurnalų iš laikotarpio prieš e-knygų erą. Didelė dalis šios kolekcijos jau buvo OCR'inta ir turi mažai vidinio dubliavimo."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Kaip mes galime padėti"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Galime suteikti didelės spartos prieigą prie visų mūsų kolekcijų, taip pat prie neišleistų kolekcijų."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Tai yra įmonės lygio prieiga, kurią galime suteikti už aukas, siekiančias dešimtis tūkstančių USD. Taip pat esame pasirengę mainyti tai į aukštos kokybės kolekcijas, kurių dar neturime."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Galime grąžinti jums pinigus, jei galėsite suteikti mums duomenų praturtinimą, pvz.:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Dubliavimo pašalinimas (deduplikacija)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Teksto ir metaduomenų ištraukimas"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Palaikykite ilgalaikį žmonijos žinių archyvavimą, tuo pačiu gaudami geresnius duomenis savo modeliui!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Susisiekite su mumis</a>, kad aptartume, kaip galime bendradarbiauti."

msgid "page.login.continue"
msgstr "Tęsti"

#, fuzzy
msgid "page.login.please"
msgstr "Prašome <a %(a_account)s>prisijungti</a> norėdami peržiūrėti šį puslapį.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Annės Archyvas laikinai neveikia dėl priežiūros darbų. Prašome sugrįžti po valandos."

#, fuzzy
msgid "page.metadata.header"
msgstr "Tobulinti metaduomenis"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Jūs galite prisidėti prie knygų išsaugojimo tobulindami metaduomenis! Pirmiausia perskaitykite informaciją apie metaduomenis Anna’s Archive, o tada sužinokite, kaip tobulinti metaduomenis per Open Library ir užsidirbkite nemokamą narystę Anna’s Archive."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Informacija"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Kai žiūrite knygą Anna’s Archive, galite matyti įvairius laukus: pavadinimą, autorių, leidėją, leidimą, metus, aprašymą, failo pavadinimą ir daugiau. Visa ši informacija vadinama <em>metaduomenimis</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Kadangi mes sujungiame knygas iš įvairių <em>šaltinių bibliotekų</em>, rodome bet kokius metaduomenis, kurie yra prieinami toje šaltinio bibliotekoje. Pavyzdžiui, knygai, kurią gavome iš Library Genesis, rodysime pavadinimą iš Library Genesis duomenų bazės."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Kartais knyga yra <em>keliose</em> šaltinių bibliotekose, kurios gali turėti skirtingus metaduomenų laukus. Tokiu atveju mes tiesiog rodome ilgiausią kiekvieno lauko versiją, nes tikimės, kad ji turės naudingiausią informaciją! Vis tiek rodysime kitus laukus po aprašymu, pvz., kaip „alternatyvų pavadinimą“ (bet tik jei jie skiriasi)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Mes taip pat ištraukiame <em>kodų</em>, tokių kaip identifikatoriai ir klasifikatoriai, iš šaltinio bibliotekos. <em>Identifikatoriai</em> unikalūs tam tikram knygos leidimui; pavyzdžiai yra ISBN, DOI, Open Library ID, Google Books ID arba Amazon ID. <em>Klasifikatoriai</em> grupuoja panašias knygas; pavyzdžiai yra Dewey Decimal (DCC), UDC, LCC, RVK arba GOST. Kartais šie kodai yra aiškiai susieti šaltinių bibliotekose, o kartais mes galime juos ištraukti iš failo pavadinimo ar aprašymo (daugiausia ISBN ir DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Mes galime naudoti identifikatorius, kad rastume įrašus <em>tik metaduomenų kolekcijose</em>, tokiose kaip OpenLibrary, ISBNdb arba WorldCat/OCLC. Mūsų paieškos sistemoje yra specifinis <em>metaduomenų skirtukas</em>, jei norite naršyti tas kolekcijas. Mes naudojame atitinkančius įrašus, kad užpildytume trūkstamus metaduomenų laukus (pvz., jei trūksta pavadinimo), arba pvz., kaip „alternatyvų pavadinimą“ (jei yra esamas pavadinimas)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Norėdami pamatyti, iš kur tiksliai atėjo knygos metaduomenys, žiūrėkite <em>„Techninės detalės“ skirtuką</em> knygos puslapyje. Jame yra nuoroda į neapdorotą JSON failą, kuriame yra nuorodos į neapdorotus originalių įrašų JSON failus."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Daugiau informacijos rasite šiuose puslapiuose: <a %(a_datasets)s>Duomenų rinkiniai</a>, <a %(a_search_metadata)s>Paieška (metaduomenų skirtukas)</a>, <a %(a_codes)s>Kodų naršyklė</a> ir <a %(a_example)s>Pavyzdinis metaduomenų JSON</a>. Galiausiai, visi mūsų metaduomenys gali būti <a %(a_generated)s>generuojami</a> arba <a %(a_downloaded)s>atsisiunčiami</a> kaip ElasticSearch ir MariaDB duomenų bazės."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library susiejimas"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Taigi, jei susiduriate su failu, kuriame yra blogi metaduomenys, kaip turėtumėte juos pataisyti? Galite eiti į šaltinio biblioteką ir laikytis jos procedūrų metaduomenims taisyti, bet ką daryti, jei failas yra keliose šaltinių bibliotekose?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Yra vienas identifikatorius, kuris Anna’s Archive yra traktuojamas ypatingai. <strong>Annas_archive md5 laukas Open Library visada viršija visus kitus metaduomenis!</strong> Pirmiausia grįžkime atgal ir sužinokime apie Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library buvo įkurta 2006 metais Aaron Swartz su tikslu „vienas tinklalapis kiekvienai kada nors išleistai knygai“. Tai tarsi Vikipedija knygų metaduomenims: visi gali ją redaguoti, ji yra laisvai licencijuota ir gali būti atsisiunčiama dideliais kiekiais. Tai knygų duomenų bazė, kuri labiausiai atitinka mūsų misiją — iš tiesų, Anna’s Archive buvo įkvėpta Aaron Swartz vizijos ir gyvenimo."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Vietoj to, kad išradinėtume dviratį iš naujo, nusprendėme nukreipti savo savanorius į Open Library. Jei matote knygą su neteisingais metaduomenimis, galite padėti šiuo būdu:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Eikite į <a %(a_openlib)s>Open Library svetainę</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Raskite teisingą knygos įrašą. <strong>ĮSPĖJIMAS:</strong> būtinai pasirinkite teisingą <strong>leidimą</strong>. Open Library yra „darbai“ ir „leidimai“."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "„Darbas“ galėtų būti „Haris Poteris ir Išminties akmuo“."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "„Leidimas“ galėtų būti:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997 m. pirmasis leidimas, išleistas Bloomsbery, su 256 puslapiais."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003 m. minkštų viršelių leidimas, išleistas Raincoast Books, su 223 puslapiais."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000 m. lenkiškas vertimas „Harry Potter I Kamie Filozoficzn“ iš Media Rodzina su 328 puslapiais."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Visi šie leidimai turi skirtingus ISBN ir skirtingą turinį, todėl būtinai pasirinkite tinkamą!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Redaguokite įrašą (arba sukurkite jį, jei jo nėra) ir pridėkite kuo daugiau naudingos informacijos! Jūs jau čia, tad padarykite įrašą tikrai nuostabų."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Skiltyje „ID numeriai“ pasirinkite „Annos Archyvas“ ir pridėkite knygos MD5 iš Annos Archyvo. Tai yra ilga raidžių ir skaičių eilutė po „/md5/“ URL adrese."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Pabandykite rasti kitus failus Annos Archyve, kurie taip pat atitinka šį įrašą, ir pridėkite juos taip pat. Ateityje galėsime grupuoti juos kaip dublikatus Annos Archyvo paieškos puslapyje."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Kai baigsite, užsirašykite URL, kurį ką tik atnaujinote. Kai atnaujinsite bent 30 įrašų su Annos Archyvo MD5, atsiųskite mums <a %(a_contact)s>el. laišką</a> ir atsiųskite sąrašą. Mes suteiksime jums nemokamą narystę Annos Archyve, kad galėtumėte lengviau atlikti šį darbą (ir kaip padėką už jūsų pagalbą). Tai turi būti aukštos kokybės redagavimai, kurie prideda daug informacijos, kitaip jūsų prašymas bus atmestas. Jūsų prašymas taip pat bus atmestas, jei bet kuris redagavimas bus atšauktas arba pataisytas Open Library moderatoriais."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Atkreipkite dėmesį, kad tai veikia tik knygoms, ne akademiniams straipsniams ar kitiems failų tipams. Kitiems failų tipams vis tiek rekomenduojame rasti šaltinio biblioteką. Gali prireikti kelių savaičių, kol pakeitimai bus įtraukti į Annos Archyvą, nes mums reikia atsisiųsti naujausią Open Library duomenų iškrovą ir atnaujinti mūsų paieškos indeksą."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Veidrodžiai: kvietimas savanoriams"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Norėdami padidinti Anna’s Archive atsparumą, ieškome savanorių, kurie galėtų valdyti veidrodžius."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Mes ieškome šito:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Jūs valdote Anna’s Archive atvirojo kodo bazę ir reguliariai atnaujinate tiek kodą, tiek duomenis."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Jūsų versija aiškiai išskiriama kaip veidrodis, pvz., „Bobo Archyvas, Annos Archyvo veidrodis“."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Jūs esate pasirengę prisiimti su šiuo darbu susijusią riziką, kuri yra reikšminga. Jūs giliai suprantate reikiamą operacinį saugumą. <a %(a_shadow)s>Šių</a> <a %(a_pirate)s>įrašų</a> turinys jums yra savaime suprantamas."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Jūs esate pasirengę prisidėti prie mūsų <a %(a_codebase)s>kodo bazės</a> — bendradarbiaudami su mūsų komanda — kad tai įvyktų."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Iš pradžių mes nesuteiksime jums prieigos prie mūsų partnerių serverių atsisiuntimų, bet jei viskas klostysis gerai, galime tai pasidalinti su jumis."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Prieglobos išlaidos"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Mes esame pasirengę padengti prieglobos ir VPN išlaidas, iš pradžių iki 200 USD per mėnesį. Tai pakanka pagrindiniam paieškos serveriui ir DMCA apsaugotam tarpininkui."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Mes mokėsime už prieglobą tik tada, kai viską įrengsite ir parodysite, kad galite atnaujinti archyvą. Tai reiškia, kad pirmus 1-2 mėnesius turėsite mokėti iš savo kišenės."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Jūsų laikas nebus kompensuojamas (ir mūsų taip pat), nes tai yra grynai savanoriškas darbas."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Jei reikšmingai įsitrauksite į mūsų darbo plėtrą ir operacijas, galime aptarti didesnės dalies aukų pajamų pasidalijimą su jumis, kad galėtumėte jas panaudoti pagal poreikį."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Pradžia"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Prašome <strong>nesikreipti į mus</strong> leidimo ar pagrindinių klausimų. Veiksmai kalba garsiau nei žodžiai! Visa informacija yra prieinama, tad tiesiog pradėkite kurti savo veidrodį."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Nedvejodami pateikite bilietus ar sujungimo užklausas mūsų Gitlab, kai susiduriate su problemomis. Gali prireikti sukurti kai kurias veidrodžio specifines funkcijas kartu su jumis, pavyzdžiui, pervadinti iš „Annos Archyvas“ į jūsų svetainės pavadinimą, (pradžioje) išjungti vartotojų paskyras arba susieti atgal į mūsų pagrindinę svetainę iš knygų puslapių."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Kai jūsų veidrodis veiks, prašome susisiekti su mumis. Norėtume peržiūrėti jūsų OpSec, ir kai tai bus patikima, mes susiesime su jūsų veidrodžiu ir pradėsime glaudžiau bendradarbiauti su jumis."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Iš anksto dėkojame visiems, kurie nori prisidėti šiuo būdu! Tai nėra silpnų nervų žmonėms, bet tai sustiprintų didžiausios tikrai atviros bibliotekos žmonijos istorijoje ilgaamžiškumą."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Atsisiųsti iš partnerio svetainės"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Lėti atsisiuntimai galimi tik per oficialią svetainę. Apsilankykite %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Lėti atsisiuntimai nėra prieinami per Cloudflare VPN ar iš Cloudflare IP adresų."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Prašome palaukti <span %(span_countdown)s>%(wait_seconds)s</span> sekundžių, kad galėtumėte atsisiųsti šį failą."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Naudokite šią URL norėdami atsisiųsti: <a %(a_download)s>Atsisiųsti dabar</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Ačiū, kad laukiate, tai padeda išlaikyti svetainę prieinamą visiems nemokamai! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Įspėjimas: per pastarąsias 24 valandas iš jūsų IP adreso buvo daug atsisiuntimų. Atsisiuntimai gali būti lėtesni nei įprastai."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Atsisiuntimai iš jūsų IP adreso per pastarąsias 24 valandas: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Jei naudojate VPN, bendrą interneto ryšį arba jūsų IPT dalijasi IP adresais, šis įspėjimas gali būti dėl to."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Kad visi turėtų galimybę nemokamai atsisiųsti failus, turite palaukti prieš atsisiųsdami šį failą."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Kol laukiate, galite toliau naršyti Annės Archyvą kitame skirtuke (jei jūsų naršyklė palaiko skirtukų atnaujinimą fone)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Galite laisvai laukti, kol įkels kelis atsisiuntimo puslapius vienu metu (bet prašome atsisiųsti tik vieną failą vienu metu iš kiekvieno serverio)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Kai gausite atsisiuntimo nuorodą, ji galios kelias valandas."

msgid "layout.index.header.title"
msgstr "Anna's Archive"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Įrašas Annės Archyve"

#, fuzzy
msgid "page.scidb.download"
msgstr "Atsisiųsti"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Norėdami palaikyti prieinamumą ir ilgalaikį žmonijos žinių išsaugojimą, tapkite <a %(a_donate)s>nariu</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Kaip priedas, 🧬&nbsp;SciDB nariams įkeliama greičiau, be jokių apribojimų."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Neveikia? Pabandykite <a %(a_refresh)s>atnaujinti</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Peržiūra dar nepasiekiama. Atsisiųskite failą iš <a %(a_path)s>Annos Archyvas</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB yra Sci-Hub tęsinys, su pažįstama sąsaja ir tiesioginiu PDF peržiūrėjimu. Įveskite savo DOI, kad peržiūrėtumėte."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Turime visą Sci-Hub kolekciją, taip pat naujus straipsnius. Daugumą galima peržiūrėti tiesiogiai su pažįstama sąsaja, panašia į Sci-Hub. Kai kuriuos galima atsisiųsti per išorinius šaltinius, tokiu atveju rodome nuorodas į juos."

msgid "page.search.title.results"
msgstr "%(search_input)s - Ieškoti"

msgid "page.search.title.new"
msgstr "Nauja paieška"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Įtraukti tik"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Neįtraukti"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Nepatikrinta"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Atsisiųsti"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Žurnalų straipsniai"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Skaitmeninė skolinimo paslauga"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metaduomenys"

msgid "common.search.placeholder"
msgstr "Pavadinimas, autorius, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Ieškoti"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Paieškos nustatymai"

#, fuzzy
msgid "page.search.submit"
msgstr "Paieška"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Paieška užtruko per ilgai, kas yra įprasta plačioms užklausoms. Filtrų skaičiai gali būti netikslūs."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Paieška užtruko per ilgai, todėl galite matyti netikslius rezultatus. Kartais padeda <a %(a_reload)s>puslapio perkrovimas</a>."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Rodyti"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Sąrašas"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Lentelė"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Išplėstinė"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Ieškoti aprašymų ir metaduomenų komentarų"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Pridėti konkretų paieškos lauką"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(ieškoti konkretaus lauko)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Išleidimo metai"

msgid "page.search.filters.content.header"
msgstr "Turinys"

msgid "page.search.filters.filetype.header"
msgstr "Failo tipas"

#, fuzzy
msgid "page.search.more"
msgstr "daugiau…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Prieiga"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Šaltinis"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "išgauta ir atviro kodo AA"

msgid "page.search.filters.language.header"
msgstr "Kalba"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Rūšiuoti pagal"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Labiausiai atitinka"

msgid "page.search.filters.sorting.newest"
msgstr "Naujausia"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(leidimo metai)"

msgid "page.search.filters.sorting.oldest"
msgstr "Seniausia"

msgid "page.search.filters.sorting.largest"
msgstr "Didžiausia"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(bylos dydis)"

msgid "page.search.filters.sorting.smallest"
msgstr "Mažiausia"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(atviro kodo)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Atsitiktinis"

msgid "page.search.header.update_info"
msgstr "Paieškos indeksas yra atnaujinamas kas mėnesį. Šiuo momentu jis yra iš %(last_data_refresh_date)s. Dėl daugiau techninės informacijos, atsidarykite %(link_open_tag)sduomenų rinkinių puslapį</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Norėdami tyrinėti paieškos indeksą pagal kodus, naudokite <a %(a_href)s>Kodo tyrinėtoją</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Įveskite laukelyje, kad ieškotumėte mūsų kataloge %(count)s tiesiogiai atsisiunčiamų failų, kuriuos mes <a %(a_preserve)s>saugome amžinai</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Iš tiesų, kiekvienas gali padėti išsaugoti šiuos failus, sėjant mūsų <a %(a_torrents)s>vieningą torrentų sąrašą</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Šiuo metu turime išsamiausią pasaulyje atvirą knygų, straipsnių ir kitų rašytinių darbų katalogą. Mes veidrodžiuojame Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ir daugiau</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Jei rasite kitų „šešėlinių bibliotekų“, kurias turėtume atspindėti, arba jei turite klausimų, susisiekite su mumis adresu %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Dėl DMCA / autorių teisių pretenzijų <a %(a_copyright)s>spauskite čia</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Patarimas: naudokite klaviatūros sparčiuosius klavišus „/“ (paieškos fokusas), „enter“ (paieška), „j“ (aukštyn), „k“ (žemyn), „<“ (ankstesnis puslapis), „>“ (kitas puslapis) greitesnei navigacijai."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Ieškote straipsnių?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Įveskite laukelyje, kad ieškotumėte mūsų kataloge %(count)s akademinių straipsnių ir žurnalų straipsnių, kuriuos mes <a %(a_preserve)s>išsaugome amžinai</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Įveskite į laukelį, kad ieškotumėte failų skaitmeninėse skolinimo bibliotekose."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Šis paieškos indeksas šiuo metu apima metaduomenis iš Internet Archive kontroliuojamos skaitmeninės skolinimo bibliotekos. <a %(a_datasets)s>Daugiau apie mūsų duomenų rinkinius</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Daugiau skaitmeninių skolinimo bibliotekų rasite <a %(a_wikipedia)s>Vikipedijoje</a> ir <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Įveskite į laukelį, kad ieškotumėte metaduomenų iš bibliotekų. Tai gali būti naudinga, kai <a %(a_request)s>prašote failo</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Šis paieškos indeksas šiuo metu apima metaduomenis iš įvairių metaduomenų šaltinių. <a %(a_datasets)s>Daugiau apie mūsų duomenų rinkinius</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Metaduomenims mes rodome originalius įrašus. Mes nesujungiame įrašų."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Yra daugybė metaduomenų šaltinių rašytiniams darbams visame pasaulyje. <a %(a_wikipedia)s>Šis Vikipedijos puslapis</a> yra gera pradžia, bet jei žinote kitų gerų sąrašų, praneškite mums."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Įveskite į laukelį, kad ieškotumėte."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Tai yra metaduomenų įrašai, <span %(classname)s>ne</span> atsisiunčiami failai."

msgid "page.search.results.error.header"
msgstr "Klaida atliekant paiešką."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Pabandykite <a %(a_reload)s>perkrauti puslapį</a>. Jei problema išlieka, prašome parašyti mums el. paštu %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Jokių failų nerasta</span> Bandykite mačiau arba kitokius terminus ir filtrus."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Kartais tai įvyksta neteisingai, kai paieškos serveris yra lėtas. Tokiais atvejais gali padėti <a %(a_attrs)s>puslapio perkrovimas</a>."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Mes radome atitikmenis: %(in)s. Galite nurodyti ten rastą URL, kai <a %(a_request)s>prašote failo</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Žurnalų straipsniai (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Skaitmeninis skolinimas (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metaduomenys (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Rezultatai %(from)s-%(to)s (%(total)s iš viso)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ daliniai atitikmenys"

msgid "page.search.results.partial"
msgstr "%(num)d daliniai atitikmenys"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Savanorystė ir atlygis"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Annos Archyvas remiasi tokiais savanoriais kaip jūs. Mes laukiame visų įsipareigojimo lygių ir turime dvi pagrindines pagalbos kategorijas, kurių ieškome:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lengvas savanoriškas darbas:</span> jei galite skirti tik kelias valandas čia ir ten, vis tiek yra daug būdų, kaip galite padėti. Mes apdovanojame nuolatinius savanorius <span %(bold)s>🤝 narystėmis Annos Archyve</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Intensyvus savanoriškas darbas (USD$50-USD$5,000 premijos):</span> jei galite skirti daug laiko ir/arba išteklių mūsų misijai, norėtume glaudžiau bendradarbiauti su jumis. Galiausiai galite prisijungti prie vidinės komandos. Nors mūsų biudžetas yra ribotas, galime skirti <span %(bold)s>💰 pinigines premijas</span> už intensyviausią darbą."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Jei negalite skirti savo laiko savanoriškai veiklai, vis tiek galite mums labai padėti <a %(a_donate)s>aukodami pinigus</a>, <a %(a_torrents)s>dalindamiesi mūsų torrentais</a>, <a %(a_uploading)s>įkeldami knygas</a> arba <a %(a_help)s>pasakodami draugams apie Annos Archyvą</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Įmonės:</span> siūlome didelės spartos tiesioginę prieigą prie mūsų kolekcijų mainais už įmonės lygio auką arba mainais už naujas kolekcijas (pvz., naujus skenavimus, OCR duomenų rinkinius, mūsų duomenų praturtinimą). <a %(a_contact)s>Susisiekite su mumis</a>, jei tai jūs. Taip pat žiūrėkite mūsų <a %(a_llm)s>LLM puslapį</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Lengvas savanoriškas darbas"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Jei turite kelias laisvas valandas, galite padėti įvairiais būdais. Būtinai prisijunkite prie <a %(a_telegram)s>savanorių pokalbio Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Kaip padėkos ženklą, paprastai suteikiame 6 mėnesius „Laimingo Bibliotekininko“ už pagrindinius pasiekimus, o daugiau už tęstinį savanorišką darbą. Visi pasiekimai reikalauja aukštos kokybės darbo — prastas darbas mums kenkia labiau nei padeda, todėl jį atmesime. Prašome <a %(a_contact)s>atsiųsti mums el. laišką</a>, kai pasieksite pasiekimą."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Užduotis"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Pasiekimas"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Skleidžiame žinią apie Annos Archyvą. Pavyzdžiui, rekomenduodami knygas AA, pateikdami nuorodas į mūsų tinklaraščio įrašus arba tiesiog nukreipdami žmones į mūsų svetainę."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s nuorodos arba ekrano nuotraukos."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Tai turėtų parodyti, kaip pranešate kam nors apie Annos Archyvą, ir kaip jie jums dėkoja."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Pagerinkite metaduomenis <a %(a_metadata)s>susiedami</a> su Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Galite naudoti <a %(a_list)s >atsitiktinių metadata problemų sąrašą</a> kaip pradinį tašką."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Būtinai palikite komentarą apie išspręstas problemas, kad kiti nedubliuotų jūsų darbo."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s nuorodos į įrašus, kuriuos patobulinote."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Versti</a> svetainę."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Visiškai išversti kalbą (jei ji nebuvo beveik baigta)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Pagerinkite Annos Archyvo Wikipedia puslapį savo kalba. Įtraukite informaciją iš AA Wikipedia puslapio kitomis kalbomis, taip pat iš mūsų svetainės ir tinklaraščio. Pridėkite nuorodas į AA kituose susijusiuose puslapiuose."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Nuoroda į redagavimo istoriją, rodanti, kad padarėte reikšmingus indėlius."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Patenkinti knygų (arba straipsnių ir kt.) užklausas Z-Library arba Library Genesis forumuose. Mes neturime savo knygų užklausų sistemos, bet mes veidrodžiuojame tas bibliotekas, todėl jų gerinimas taip pat gerina Annos Archyvą."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s nuorodos arba ekrano nuotraukos apie įvykdytus prašymus."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Mažos užduotys, paskelbtos mūsų <a %(a_telegram)s>savanorių pokalbyje Telegram</a>. Paprastai už narystę, kartais už mažas premijas."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Mažos užduotys skelbiamos mūsų savanorių pokalbių grupėje."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Priklauso nuo užduoties."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Premijos"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Mes visada ieškome žmonių, turinčių tvirtų programavimo ar puolamosios saugos įgūdžių, kurie norėtų prisidėti. Jūs galite reikšmingai prisidėti prie žmonijos palikimo išsaugojimo."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Kaip padėką, už solidžius indėlius suteikiame narystę. Kaip didelę padėką, už ypač svarbias ir sudėtingas užduotis suteikiame pinigines premijas. Tai neturėtų būti laikoma darbo pakaitalu, tačiau tai yra papildoma paskata ir gali padėti padengti patirtas išlaidas."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Dauguma mūsų kodo yra atvirojo kodo, ir mes prašysime, kad jūsų kodas taip pat būtų atvirojo kodo, kai skiriame premiją. Yra keletas išimčių, kurias galime aptarti individualiai."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Premijos skiriamos pirmajam asmeniui, kuris užbaigia užduotį. Drąsiai komentuokite premijos bilietą, kad kiti žinotų, jog dirbate prie kažko, kad kiti galėtų palaukti arba susisiekti su jumis dėl bendradarbiavimo. Tačiau būkite pasiruošę, kad kiti vis tiek gali dirbti prie tos pačios užduoties ir bandyti jus aplenkti. Tačiau mes neskiriame premijų už prastą darbą. Jei dvi aukštos kokybės pateiktys bus pateiktos artimu metu (per dieną ar dvi), galime nuspręsti skirti premijas abiem, savo nuožiūra, pavyzdžiui, 100%% už pirmąją pateiktį ir 50%% už antrąją pateiktį (taigi iš viso 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Dėl didesnių premijų (ypač duomenų rinkimo premijų) prašome susisiekti su mumis, kai užbaigsite ~5%% užduoties, ir būsite įsitikinę, kad jūsų metodas bus tinkamas visam tikslui pasiekti. Turėsite pasidalinti savo metodu su mumis, kad galėtume pateikti atsiliepimus. Taip pat, tokiu būdu galime nuspręsti, ką daryti, jei keli žmonės artėja prie premijos, pavyzdžiui, galbūt skirti ją keliems žmonėms, skatinti žmones bendradarbiauti ir pan."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ĮSPĖJIMAS: didelės premijos užduotys yra <span %(bold)s>sudėtingos</span> — gali būti protinga pradėti nuo lengvesnių."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Eikite į mūsų <a %(a_gitlab)s>Gitlab užduočių sąrašą</a> ir rūšiuokite pagal „Label priority“. Tai rodo apytikslę užduočių, kurios mums rūpi, tvarką. Užduotys be aiškių premijų vis tiek yra tinkamos narystei, ypač tos, kurios pažymėtos „Accepted“ ir „Anna’s favorite“. Galbūt norėsite pradėti nuo „Starter project“."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Naujienos apie <a %(wikipedia_annas_archive)s>Annos Archyvą</a>, didžiausią tikrai atvirą biblioteką žmonijos istorijoje."

msgid "layout.index.title"
msgstr "Anna's Archive"

msgid "layout.index.meta.description"
msgstr "Didžiausia pasaulyje atvirojo kodo atvirųjų duomenų biblioteka. Apima Sci-Hub, Library Genesis, Z-Library ir kt."

msgid "layout.index.meta.opensearch"
msgstr "Ieškoti Anna's Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annės Archyvui reikia jūsų pagalbos!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Daugelis bando mus sunaikinti, bet mes kovojame atgal."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Jei aukosite dabar, gausite <strong>dvigubai</strong> daugiau greitų atsisiuntimų."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Galioja iki šio mėnesio pabaigos."

msgid "layout.index.header.nav.donate"
msgstr "Aukoti"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Žmonių žinių išsaugojimas: puiki dovana šventei!"

msgid "layout.index.header.banner.surprise"
msgstr "Nustebinkite mylimą žmogų, suteikite jam sąskaitą su naryste."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Norėdami padidinti Annės Archyvo atsparumą, ieškome savanorių, kurie galėtų valdyti veidrodžius."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Tobula Valentino dienos dovana!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Mes turime naują aukojimo metodą pasiekiamą: %(method_name)s. Pagalvokite apie %(donate_link_open_tag)saukojimą</a> - nėra pigu išlaikyti šią svetainę ir jūsų auka tikrai padaro skirtumą. Labai ačiū."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Renkame lėšų didžiausios komiksų šešėlinės bibliotekos <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">atsarginėms kopijoms sukurti</a>. Ačiū už Jūsų pagalbą! <a href=\"/donate\">Aukokite</a>. Jeigu aukoti negalite, apsvarstykite galimybę mus paremti kitais būdais: pasidalinant apie šią svetainę su draugais ir sekant mus <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> ar <a href=\"https://t.me/annasarchiveorg\">Telegram</a> platformose."

msgid "layout.index.header.recent_downloads"
msgstr "Naujausi parsisiuntimai:"

msgid "layout.index.header.nav.search"
msgstr "Ieškoti"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "DUK"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Tobulinti metaduomenis"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Savanorystė ir atlyginimai"

msgid "layout.index.header.nav.datasets"
msgstr "Duomenų rinkiniai"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torentai"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Veikla"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Koduotės tyrinėtojas"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM duomenys"

msgid "layout.index.header.nav.home"
msgstr "Pradžia"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anos programa ↗"

msgid "layout.index.header.nav.translate"
msgstr "Versti ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Prisijungti / Registruotis"

msgid "layout.index.header.nav.account"
msgstr "Paskyra"

msgid "layout.index.footer.list1.header"
msgstr "Anos archyvas"

msgid "layout.index.footer.list2.header"
msgstr "Susisiekti"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / autorinės teisės"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Išplėstinė"

msgid "layout.index.header.nav.security"
msgstr "Saugumas"

msgid "layout.index.footer.list3.header"
msgstr "Alternatyvos"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nepriklausomas"

msgid "page.search.results.issues"
msgstr "❌ Šis failas gali turėti problemų."

msgid "page.search.results.fast_download"
msgstr "Greitasis atsisiuntimas"

msgid "page.donate.copy"
msgstr "kopijuoti"

msgid "page.donate.copied"
msgstr "nukopijuota!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Ankstesnis"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Kitas"

#~ msgid "page.donate.title"
#~ msgstr "Aukoti"

#~ msgid "page.donate.header"
#~ msgstr "Aukoti"

#~ msgid "page.donate.text2"
#~ msgstr "Su jūsų bendradarbiavimu mes galime išlaikyti šią svetainę, patobulinti jos funkcijas ir išlaikyti daugiau rinkinių."

#~ msgid "page.donate.text3"
#~ msgstr "Naujausios aukos: %(donations)s. Dėkojame visiems už jūsų dosnumą. Mes labai džiaugiamės jūsų pasitikėjimu mumis, su kad ir kokia mums skirta suma."

#~ msgid "page.donate.text4"
#~ msgstr "Norint aukoti, pasirinkite mokėjimo metodą žemiau. Jei patiriate problemų susisiekite %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Kreditinė/debetinė kortelė"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Kriptovaliutos"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Klausimai"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.cc.header"
#~ msgstr "Kreditinė/debetinė kortelė"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Mes naudojame Sendwyre, norint perduoti pinigus iškart į mūsų Bitcoin (BTC) piniginę. Tai atlikti trunka apie 5 minutes."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Šis metodas turi minimalią sumą 30$ ir 5$ mokestį."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Žingniai:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Kopijuokite mūsų Bitcoin (BTC) piniginės adresą %(address)s"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Įklijuokite mūsų piniginės adresą ir sekite instrukcijas"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Kriptovaliuta"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(taip pat veikia BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.url"
#~ msgstr "Adresas"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Prašome naudoti %(link_open_tag)sšį Pix puslapį</a> norint išsiųsti auką. Jeigu šitai neveikia, bandykite perkrauti puslapį, kadangi taip galite gauti naują paskyrą."

#~ msgid "page.donate.faq.header"
#~ msgstr "Dažnai užduodami klausimai"

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library anoniminis atkartojimas #%(num)d"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive yra ne pelno atviro kodo projektas, išlaikomas savanorių. Mes priimame aukas, norint išlaikyti serverius, domenus, kūrimą ir kitas išlaidas."

#~ msgid "page.donate.paypal.text"
#~ msgstr "Eikite į %(link_open_tag)sšitą puslapį</a> ir sekite instrukcijas, arba skenuojant QR kodą arba spaudžiant ant \"paypal.me\" nuorodos. Jeigu šitai neveikia, bandykite perkrauti puslapį, kadangi tai gali suteikti prieigą prie skirtingos paskyros."

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Eikite į %(link_open_tag)sšį puslapį</a> ir spauskite ant \"buy crypto instantly\" (pirkti kriptovaliutą iškarto)"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Prašome naudoti %(link_open_tag)sšią Alipay paskyrą</a> norint išsiųsti auką. Jeigu šitai neveikia, bandykite perkrauti puslapį, kadangi tai gali jums suteikti naują paskyrą."

#~ msgid "page.donate.out_of_order"
#~ msgstr "Šis aukojimo metodas šiuo momentu neveikia. Prašome bandyti dar kartą. Ačiū už norą paaukoti!"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna's Archive</span> yra projektas kuris bando kataloguoti visas egzistuojančias knygas, surenkant duomenis iš skirtingų šaltinių. Mes taip pat sekame žmonyjos progresą link šių knygų lengvo pasiekiamumo skaitmeniniais formatais, per „<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">šešėlines bibliotekas</a>“ Sužinokite daugiau <a href=\"/about\">apie mus.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr "Narystė: <strong>%s(tier_name)</strong> iki %(until_date) <a %(a_extend)s>(pratęsti)</a>"

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr "🚀 Greiti atsisiuntimai iš mūsų partnerių (reikia <a %(a_login)s>prisijungti</a>)"

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr "🚀 Greitas atsisiuntimas (jūs esate prisijungęs!)"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Knyga (bet kokia)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Pagrindinis"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Duomenų rinkiniai ▶ ISBN(-ai) ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Nerasta"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "\"%(isbn_input)s\" nėra tinkamas ISBN numeris. ISBN yra 10 arba 13 simbolių ilgio, neskaičiuojant neprivalomų brūkšnelių. Visi simboliai turi būti numeriai, išskyrus paskutinį kuris taip pat gali būti \"X\". Paskutinė raidė yra \"patikrinimo skaitmuo\", kuris turi turėti vertę apskaičiuotą iš kitų numerių. Jis taip pat turi būti tinkamas pagal Tarptautinę ISBN Agentūrą."

#~ msgid "page.isbn.results.text"
#~ msgstr "Atitinkantys failai mūsų duomenų bazėje:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Jokių atitinkamų failų mūsų duomenų bazėje."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Ieškoti ▶ %(num)d+ rezultatai ieškant <span class=\"italic\">%(search_input)s</span> (šešėlinių bibliotekų metaduomenyse)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Paieška ▶ %(num)d rezultatai ieškant <span class=\"italic\">%(search_input)s</span> (šešėlinių bibliotekų metaduomenyse)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Paieška ▶ Paieškos klaida ieškant <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Paieška ▶ Nauja paieška"

#~ msgid "page.donate.header.text3"
#~ msgstr "Taip pat galite paaukoti nekuriant paskyros (tie patys mokėjimo metodai yra palaikomi vienkartinėms aukoms ir narystėms):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Atlikti vienkartinę anoniminę auką (jokių privilegijų)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Pasirinkite mokėjimo metodą. Apsvarstykite galimybę naudoti kriptovaliutų mokėjimą %(bitcoin_icon)s, nes taikome (kiek) mažesnius mokesčius."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Jeigu jau turite kriptovaliutų, čia yra mūsų adresai."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Ačiū už pagalbą! Šis projektas be jūsų nebūtų įmanomas."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Norėdami paaukoti naudodami „PayPal“, naudosime „PayPal Crypto“, kuris leidžia išlikti anonimiškiems. Dėkojame, kad skyrėte laiko ir išmokote aukoti šiuo metodu, nes jis mums labai padeda."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Vykdykite instrukcijas norėdami nusipirkti Bitcoin (BTC). Jums tereikia nusipirkti sumą, kurią norite paaukoti."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Įveskite mūsų Bitcoin (BTC) adresą kaip gavėja ir sekite instrukcijas išsiųsti aukai:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Prašome naudoti <a %(a_account)s>šią Alipay paskyrą</a> išsiųsti aukai."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Prašome naudoti <a %(a_account)s>šią Pix paskyrą</a> išsiųsti aukai."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr "Pabandykite <a href=\"javascript:location.reload()\">perkrauti puslapį</a>. Jei problema lieka, informuokite mus <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> arba <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Norint tapti nariu, prašome <a href=\"/login\">Prisijungti arba Registruotis</a>. Jeigu nenorite sukurti paskyros, pasirinkite \"Atlikti vienkartinę anoniminę auką\" aukščiau. Ačiū už Jūsų palaikymą!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Pagrindinis"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Apie"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Aukoti"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Duomenų rinkiniai"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Mobili programėlė"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Versti"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Duomenų rinkiniai ▶ DOI(-ai) ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Nerasta"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" neatrodo kaip DOI. Jis turi prasideti su \"10.\" ir turėti pasvira brūkšnį palinkusį į priekį."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Pagrindinis adresas: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Šitas failas gali būti pasiekiamas %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Atitikę failai mūsų duomenų bazėje:"

#~ msgid "page.doi.results.none"
#~ msgstr "Jokių atitinkančių failų mūsų duomenų bazėje nerasta."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Ar galiu prisidėti kitokiais būdais?</div> Taip! Pažiūrėkite <a href=\"/about\">apie puslapį</a> po „Kaip prisidėti“."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Man nepatinka, kad jūs „gaunate pajamų“ iš Anos archyvo!</div> Jei jums nepatinka, kaip vykdome savo projektą, vadovaukitės savo šešėline biblioteka! Visas mūsų kodas ir duomenys yra atvirojo kodo, todėl niekas jums netrukdo. ;)"

#~ msgid "page.request.title"
#~ msgstr "Pateikti užklausą knygai"

#~ msgid "page.request.text1"
#~ msgstr "Kol kas atlikite knygų užklausas <a %(a_forum)s>Libgen.rs forume</a>. Jūs galite sukurti paskyra ir paskelbti vienoje iš šių gijų:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>eKnygoms naudokite <a %(a_ebook)s>šią giją</a>.</li><li %(li_item)s>Knygoms kurios nėra pasiekiamos kaip eKnygos naudokite <a %(a_regular)s>šią giją</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Abiem atvejais sekite taisykles aukščiau minėtuose gijose."

#~ msgid "page.upload.title"
#~ msgstr "Įkelti"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Apie"

#~ msgid "page.about.header"
#~ msgstr "Apie"

#~ msgid "page.home.search.header"
#~ msgstr "Ieškoti"

#~ msgid "page.home.search.intro"
#~ msgstr "Ieškoti mūsų šešėlinių bibliotekų kataloge."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr "Anna's Archive yra ne pelno, atviro kodo paieškos sistema skirta “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">šešėlinėms bibliotekoms</a>”. Ji buvo sukurta <a href=\"http://annas-blog.org\">Anna</a>, kuri manė, kad turi būti viena vieta ieškoti knygų, dokumentų, komiksų, žurnalų ir kitų leidinių."

#~ msgid "page.about.text4"
#~ msgstr "Jeigu turite DMCA skundą, žvilgtelėkite į puslapio apačią, arba susisiekite %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Ieškoti knygas"

#~ msgid "page.home.explore.intro"
#~ msgstr "Tai yra rinkinys sudarytas iš populiarių knygų ir knygų, kurios yra svarbios šešėlinėms bibliotekoms bei skaitmeniniam išlaikymui."

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Apie"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Mobili programėlė"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Prašyti knygų"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Įkelti"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Pakviesti draugus"

#~ msgid "page.about.help.header"
#~ msgstr "Kaip prisidėti"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Paukokite %(total)s sumą naudojant <a %(a_account)s>šią Alipay paskyrą"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "tik šį mėnesį!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>sustabdė</a> naujų straipsnių įkėlimą."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Pasirinkite mokėjimo metodą. Mokėjimams atliekamiems su kriptovaliutomis %(bitcoin_icon)s yra suteikiamos nuolaidos, kadangi yra daug mažiau mokesčių."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Pasirinkite mokėjimo būdą. Šiuo metu galimi tik kriptovaliutų mokėjimai %(bitcoin_icon)s, nes tradicinių apmokėjimų būdų teikėjai atsisako su mumis dirbti."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Negalime tiesiogiai palaikyti kredito/debeto kortelių, nes bankai nenori su mumis dirbti. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tačiau yra keletas būdų, kaip naudoti kreditines/debetines korteles, naudojant kitus mūsų mokėjimo metodus:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Lėti ir išoriniai atsisiuntimai"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Atsisiuntimai"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Jei pirmą kartą naudojatės kriptovaliutomis, rekomenduojame naudotis %(option1)s, %(option2)s, arba %(option3)s perkant ir aukojant Bitcoin (originali ir dažniausiai naudojama kriptovaliuta)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 nuorodų į įrašus, kuriuos patobulinote."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 nuorodų arba ekrano nuotraukų."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 nuorodų arba ekrano nuotraukų apie įvykdytas užklausas."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Jei jus domina šių duomenų rinkinių veidrodinis atspindys <a %(a_faq)s>archyvavimo</a> ar <a %(a_llm)s>LLM mokymo</a> tikslais, susisiekite su mumis."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Jei jus domina šio duomenų rinkinio veidrodinis kopijavimas <a %(a_archival)s>archyvavimo</a> ar <a %(a_llm)s>LLM mokymo</a> tikslais, prašome susisiekti su mumis."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Pagrindinė svetainė"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN šalies informacija"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Jei jus domina šio duomenų rinkinio veidrodinis kopijavimas <a %(a_archival)s>archyvavimo</a> ar <a %(a_llm)s>LLM mokymo</a> tikslais, prašome susisiekti su mumis."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Tarptautinė ISBN agentūra reguliariai išleidžia diapazonus, kuriuos ji paskyrė nacionalinėms ISBN agentūroms. Iš to galime nustatyti, kuriai šaliai, regionui ar kalbos grupei priklauso šis ISBN. Šiuo metu mes naudojame šiuos duomenis netiesiogiai, per <a %(a_isbnlib)s>isbnlib</a> Python biblioteką."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ištekliai"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Paskutinį kartą atnaujinta: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN svetainė"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metaduomenys"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Neįtraukiant „scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Mūsų įkvėpimas rinkti metaduomenis yra Aarono Swartzo tikslas „vienas tinklalapis kiekvienai kada nors išleistai knygai“, kuriam jis sukūrė <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Tas projektas sekasi gerai, tačiau mūsų unikali padėtis leidžia mums gauti metaduomenis, kurių jie negali."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Kita įkvėpimo priežastis buvo mūsų noras sužinoti <a %(a_blog)s>kiek knygų yra pasaulyje</a>, kad galėtume apskaičiuoti, kiek knygų dar turime išsaugoti."

#~ msgid "page.partner_download.text1"
#~ msgstr "Norėdami suteikti visiems galimybę nemokamai atsisiųsti failus, turite palaukti <strong>%(wait_seconds)s sekundžių</strong> prieš atsisiųsdami šį failą."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automatiškai atnaujinti puslapį. Jei praleisite atsisiuntimo langą, laikmatis bus paleistas iš naujo, todėl rekomenduojama automatinis atnaujinimas."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Atsisiųsti dabar"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvertuoti: naudokite internetinius įrankius formatų konvertavimui. Pavyzdžiui, norėdami konvertuoti tarp epub ir pdf, naudokite <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: atsisiųskite failą (palaikomi pdf arba epub), tada <a %(a_kindle)s>siųskite jį į Kindle</a> naudodami internetą, programėlę arba el. paštą. Naudingi įrankiai: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Paremkite autorius: jei jums tai patinka ir galite sau leisti, apsvarstykite galimybę įsigyti originalą arba tiesiogiai paremti autorius."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Paremkite bibliotekas: jei tai yra jūsų vietinėje bibliotekoje, apsvarstykite galimybę pasiskolinti nemokamai."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nėra tiesiogiai prieinami dideliais kiekiais, tik pusiau dideliais kiekiais už mokamą sieną"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annos Archyvas valdo <a %(isbndb)s>ISBNdb metaduomenų</a> kolekciją"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb yra įmonė, kuri renka įvairių internetinių knygynų ISBN metaduomenis. Annos Archyvas daro ISBNdb knygų metaduomenų atsargines kopijas. Šie metaduomenys yra prieinami per Annos Archyvą (nors šiuo metu jų negalima ieškoti, išskyrus jei aiškiai ieškote ISBN numerio)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Dėl techninių detalių žr. žemiau. Tam tikru momentu galime naudoti šiuos duomenis nustatyti, kurios knygos vis dar trūksta šešėliniuose bibliotekose, kad galėtume prioritetizuoti, kurias knygas rasti ir/arba skenuoti."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Mūsų tinklaraščio įrašas apie šiuos duomenis"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb rinkimas"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Šiuo metu turime vieną torrentą, kuriame yra 4,4 GB suspaustas <a %(a_jsonl)s>JSON Lines</a> failas (20 GB nesuspaustas): „isbndb_2022_09.jsonl.gz“. Norėdami importuoti „.jsonl“ failą į PostgreSQL, galite naudoti kažką panašaus į <a %(a_script)s>šį skriptą</a>. Galite netgi tiesiogiai jį perduoti naudodami kažką panašaus į %(example_code)s, kad jis dekompresuotų tiesiogiai."

#~ msgid "page.donate.wait"
#~ msgstr "Prašome palaukti bent <span %(span_hours)s>dvi valandas</span> (ir atnaujinti šį puslapį) prieš susisiekdami su mumis."

#~ msgid "page.codes.search_archive"
#~ msgstr "Ieškoti Anna’s Archive „%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Aukokite per Alipay ar WeChat. Galite pasirinkti vieną iš jų sekančiame puslapyje."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Skleisti žinią apie Annos Archyvą socialiniuose tinkluose ir interneto forumuose, rekomenduojant knygas ar sąrašus AA, arba atsakant į klausimus."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Grožinės literatūros kolekcija skiriasi, bet vis dar turi <a %(libgenli)s>torrentus</a>, nors nuo 2022 metų neatnaujinta (turime tiesioginius atsisiuntimus)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annos Archyvas ir Libgen.li kartu valdo <a %(comics)s>komiksų</a> ir <a %(magazines)s>žurnalų</a> kolekcijas."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nėra torrentų rusų grožinės literatūros ir standartinių dokumentų kolekcijoms."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Papildomam turiniui nėra prieinamų torrentų. Torrentai, esantys Libgen.li svetainėje, yra kitų čia išvardytų torrentų veidrodžiai. Vienintelė išimtis yra grožinės literatūros torrentai, pradedant nuo %(fiction_starting_point)s. Komiksų ir žurnalų torrentai yra išleisti bendradarbiaujant tarp Annos Archyvo ir Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Iš kolekcijos <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> tiksli kilmė neaiški. Iš dalies iš the-eye.eu, iš dalies iš kitų šaltinių."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

