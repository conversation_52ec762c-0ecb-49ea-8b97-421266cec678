#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Invalid request. Visit %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " na "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "na moa"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Mi<PERSON><PERSON> save mekim kopi bilong %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Mipela save kisim na mekim open-source %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Olgeta kod na data bilong mipela i olgeta open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Bikpela tru open laibreri long histori bilong ol manmeri."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;buk, %(paper_count)s&nbsp;pepa — i stap oltaim."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Bikpela tru open-source open-data laibreri long graun. ⭐️&nbsp;Mekim kopi bilong Sci-Hub, Library Genesis, Z-Library, na moa. 📈&nbsp;%(book_any)s buk, %(journal_article)s pepa, %(book_comic)s komik, %(magazine)s magasin — i stap oltaim."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Bikpela tru open-source open-data laibreri long graun.<br>⭐️ Mekim kopi bilong Scihub, Libgen, Zlib, na moa."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Rongpela metadata (olsem taitel, diskripsen, kavainis)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Hevi long daunlod (olsem i no inap konekt, eror mesis, tumas slou)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Fail i no inap op (olsem bagarap fail, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Rabis kwoliti (olsem hevi long format, rabis skan kwoliti, lusim ol peij)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fail i mas rausim (olsem advaetis, nogutpela konten)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Copyright claim"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Arap"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonus daunlod"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brata Buk"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Laki Laibrerian"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Daizling Datahoarder"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Ameizing Arkivist"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s olgeta"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) olgeta"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "no gat pe"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "gat pe"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "i go bek"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "i go pinis"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "wetim Anna long tok orait"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "i no stret"

#, fuzzy
msgid "page.donate.title"
msgstr "Givim mani"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Yu gat wanpela <a %(a_donation)s>donet we i stap</a>. Plis pinisim o kanselim dispela donet bipo yu mekim nupela donet."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Lukim olgeta donet bilong mi</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Anna’s Archive em wanpela non-profit, open-source, open-data projek. Taim yu givim mani na kamap memba, yu sapotim wok bilong mipela na divelopmen. Long olgeta memba bilong mipela: tenkyu long holim mipela i go! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Long kisim moa infomesen, lukim <a %(a_donate)s>Donation FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Long kisim moa downloads, <a %(a_refer)s>tokim ol pren bilong yu</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Yu kisim %(percentage)s%% bonus kwik downloads, bikos wanpela user %(profile_link)s i tokim yu."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Dispela i wok long olgeta taim bilong memba."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s kwik downloads long wanpela de"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "sapos yu givim mani long dispela mun!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mun"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Joinim"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Makim"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "i go inap long %(percentage)s%% diskaun"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB pepa <strong>nogat limit</strong> wantaimaut verifikesen"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> akses"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Kisim <strong>%(percentage)s%% bonus downloads</strong> long <a %(a_refer)s>tokim ol pren</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Nem bilong yu o nem bilong yu we i no save kamap long ol kredits"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Ol bipoa samting, wantaim:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Akses long ol niu featur bipo long ol arapela"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Eksklusiv Telegram wantaim ol apdeit bihain long ol skrin"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adopt a torrent”: nem bilong yu o mesej long torrent filename <div %(div_months)s>wanpela taim long olgeta 12 mun bilong membasip</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legendari stetus long presaveisen bilong save na kalja bilong ol pipol"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Ekspert Akses"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontakim mipela"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Mipela liklik tim bilong ol volentia. Em inap kisim 1-2 wik long bekim yu."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Unlimited</strong> akses long hai-spid"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direk <strong>SFTP</strong> seva"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Enap long donation o eksens bilong niu koleksen (e.g. niu skans, OCR’ed datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Mipela welkamim ol bikpela donation bilong ol welti indibidual o institusen. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Long ol donation i go antap long $5000 plis kontakim mipela direk long %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Lukim gut olsem membership long dispela pes em “long wanpela mun”, tasol ol i wanpela taim donesen (i no save kamap gen). Lukim <a %(faq)s>Donation FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Sapos yu laik mekim donation (wanpela amount) wantaimaut membasip, yu ken yusim dispela Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Plis selektim wanpela peimen metod."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(taimporeri no inap wok)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s presen kart"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bank kad (yusim app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit/debit kad"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (stret)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kiat / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kiat kredit/debit/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bank kad"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit/debit kad (bekap)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit/debit kad 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Wantaim crypto yu inap givim donesen wantaim BTC, ETH, XMR, na SOL. Yusim dispela opsen sapos yu save pinis long cryptocurrency."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Wantaim crypto yu inap givim donesen wantaim BTC, ETH, XMR, na moa."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Sapos yu yusim crypto long namba wan taim, mipela i ting yu ken yusim %(options)s long baim na givim Bitcoin (olsem namba wan na bikpela cryptocurrency)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Long givim donesen wantaim PayPal US, mipela bai yusim PayPal Crypto, we i save mekim mipela stap ananim. Mipela i tenkyu long yu long kisim taim long lainim olsem wanem long givim donesen wantaim dispela rot, bikos em i helpim mipela planti."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Givim donesen wantaim PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Givim donesen wantaim Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Sapos yu gat Cash App, dispela em i isi rot long givim donesen!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Tingim olsem long ol transaksen aninit long %(amount)s, Cash App inap askim wanpela %(fee)s fi. Long %(amount)s o antap, em fri!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donet long Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Sapos yu gat Revolut, em i isi tru long donet!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donet wantaim kredit o debit kad."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay na Apple Pay inap wok tu."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Tingim olsem long ol liklik donesen, ol kredit kad fi inap rausim %(discount)s%% diskaun bilong mipela, olsem na mipela i rekomendim longpela sapkripsen."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Tingim olsem long ol liklik donesen, ol fi i antap, olsem na mipela i rekomendim longpela sapkripsen."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Wantaim Binance, yu ken baim Bitcoin wantaim kredit/debit kad o bank akaun, na bihain donetim dispela Bitcoin long mipela. Dispela rot i helpim mipela long stap sekiur na ananimas taim mipela kisim donesen bilong yu."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance i stap long klostu olgeta kantri, na sapotim planti bank na kredit/debit kad. Dispela em nau as tingting bilong mipela. Mipela i tenkyu long yu long kisim taim long lainim olsem wanem long donetim wantaim dispela rot, bikos em i helpim mipela planti."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donet long yusim PayPal akaun bilong yu."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donet wantaim kredit/debit kad, PayPal, o Venmo. Yu ken makim namel long dispela long nekis peij."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donet wantaim Amazon gift kad."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Tingim olsem mipela i nid long raunim igo long ol amaunt we ol resela i akseptim (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTENT:</strong> Mipela i sapotim Amazon.com tasol, nogat ol arapela Amazon websait. Olsem, .de, .co.uk, .ca, i NO sapotim."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>Igat samting i bikpela samting:</strong> Dispela opsen em bilong %(amazon)s. Sapos yu laik yusim narapela Amazon website, yu mas makim antap."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Dispela rot i yusim wanpela kriptokarensi provaidar olsem wanpela midelman konvesen. Dispela inap luk olsem liklik konfius, olsem na plis yusim dispela rot sapos ol arapela peimen rot i no wok. Em tu i no wok long olgeta kantri."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donet yusim kredit/debit kad, long Alipay app (i isi tru long setim)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instolim Alipay app"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instolim Alipay app long <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Rijista yusim fon namba bilong yu."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nogat narapela personal detel i nidim."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Addim bank kad"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Sapot: Visa, MasterCard, JCB, Diners Club na Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Lukim <a %(a_alipay)s>dispela gaim</a> bilong kisim moa infomesen."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Mipela i no inap sapotim kiat kredit/debit stret, bikos ol benk i no laik wok wantaim mipela. ☹ Tasol, i gat sampela rot long yusim kiat kredit/debit, yusim ol narapela peimen rot:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Gift Card"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Salim mipela Amazon.com gift kad yusim kredit/debit kad bilong yu."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay i sapotim ol intanasonal kiat kredit/debit. Lukim <a %(a_alipay)s>dispela gaim</a> long kisim moa infomesen."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) i sapotim intanasonal kredit/debit kad. Long WeChat app, go long “Me => Services => Wallet => Add a Card”. Sapos yu no lukim dispela, enablim yusim “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Yu ken baim kripto yusim kredit/debit kad."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Krypto ekspres sevis"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Ekspres sevis i konvinien, tasol i save askim bikpela fi."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Yu ken yusim dispela insait long krypto eksens sapos yu laik kwiktaim mekim bikpela donesen na yu no wari long fi bilong $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Lukaut long salim stretpela krypto amaunt i soim long donesen pes, nogat amaunt long $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Sapos nogat, fi bai subtrak na mipela no inap wokim membership bilong yu long otomat."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s long kantri, nogat verifikesen long namba wan transaksen)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, no nid long olsem long fes taem yu baem)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Sapos wanpela bilong dispela infomesen i no stret, plis emailim mipela long tokim mipela."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Long kredit kad, debit kad, Apple Pay, na Google Pay, mipela i yusim “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Long sistem bilong ol, wanpela “coffee” i wankain olsem $5, olsem na donesen bilong yu bai raun igo long klostu multiple bilong 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Makim olsem hamas taim yu laik sapkripsen."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mun"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 mun"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 mun"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 mun"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 mun"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 mun"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 mun"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>bihain <span %(span_discount)s></span> diskaun</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Dispela rot bilong baim i nidim liklik %(amount)s. Plis makim narapela taim o rot bilong baim."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donet"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Dispela rot bilong baim i gat bikpela mak bilong %(amount)s. Plis makim narapela taim o rot bilong baim."

#, fuzzy
msgid "page.donate.login2"
msgstr "Long kamap memba, plis <a %(a_login)s>Login o Reta</a>. Tenkyu long sapot bilong yu!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Makim kripto koin yu laikim:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(smolpela mak bilong stat)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(yusim taim yu salim Ethereum long Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(warning: bikpela mak bilong stat)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Klikim donet baton long konfirmim dispela donet."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donet <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Yu inap yet kanselim donet long taim bilong checkout."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirecting long donet peij…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "long 1 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "long 3 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "long 6 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "long 12 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "long 24 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "long 48 mun"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "long 96 mun"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "long 1 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "long 3 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "long 6 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "long 12 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "long 24 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "long 48 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "long 96 mun “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donesen"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Deit: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mun long %(duration)s mun, i go insait %(discounts)s%% diskaun)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mun for %(duration)s mun)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Namba: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Kanselim"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Yu sure yu laik kanselim? No ken kanselim sapos yu bin peim pinis."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Yes, plis kanselim"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Donasin bilong yu i bin kanselim."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Mekim nupela donasin"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reorder"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Yu bin peim pinis. Sapos yu laik lukim gen ol peimen instraksen, klik hia:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Soim ol olpela peimen instraksen"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Tenkyu long donasin bilong yu!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Sapos yu no bin mekim yet, raitim daun sekrit ki bilong login:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Narapela ways yu ken lusim akses long dispela akaun!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Ol peimen instraksen i no wok moa. Sapos yu laik mekim narapela donasin, yusim “Reorder” baton antap."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Impoten notis:</strong> Prais bilong crypto i ken senis kwiktaim, sampela taim inap 20%% long sampela minit tasol. Dispela i no bikpela olsem ol fi mipela kisim long planti peimen provida, husat i save chajim 50-60%% long wok wantaim “shadow charity” olsem mipela. <u>Sapos yu salim risit wantaim orijinal prais yu bin peim, mipela bai yet kreditim akaun bilong yu long membasip yu bin makim</u> (sapos risit i no olpela moa long sampela aua). Mipela i rili aprisiatim olsem yu redi long putim ap wantaim ol kain samting olsem long sapotim mipela! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Dispela donasin i bin expire. Plis kanselim na mekim nupela wan."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Crypto instraksen"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfa long wanpela bilong ol crypto akaun bilong mipela"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donetim total amaunt bilong %(total)s long wanpela bilong ol dispela adres:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Baem Bitcoin long Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Painim “Crypto” peij long PayPal app o website bilong yu. Dispela i stap aninit long “Finances”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Bihainim ol tok bilong baem Bitcoin (BTC). Yu mas baem tasol dispela mani yu laik givim, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfarem Bitcoin igo long adres bilong mipela"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Go long “Bitcoin” peij long PayPal app o website bilong yu. Presim “Transfer” button %(transfer_icon)s, na bihain “Send”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Putim adres bilong mipela bilong Bitcoin (BTC) olsem recipient, na bihainim ol tok bilong salim donesen bilong yu %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredit / debit kad insaksons"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donet long peij bilong mipela bilong kredit / debit kad"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donet %(amount)s long <a %(a_page)s>dispela peij</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Lukim step-by-step gide aninit."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Wetim konfirmesen (refreshim peij bilong lukim)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Wetim transfer (refreshim peij bilong lukim)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Taim i stap:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(yu inap laik kanselim na mekim nupela donesen)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Long resetim taim, yu mas mekim nupela donesen."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Apdeitim status"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Sapos yu bungim sampela hevi, plis kontakim mipela long %(email)s na putim planti infomesen (olsem screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Sapos yu bin baim pinis:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Sampela taim konfirmesen inap kisim 24 aua, olsem na yu mas refresim dispela pes (maski em i pinis)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Baem PYUSD koin long PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Bihainim ol tok bilong baem PYUSD koin (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Baing liklik moa (mipela tingim %(more)s moa) winim dispela mani yu laik givim (%(amount)s), long karamapim ol fees bilong transaksen. Yu bai holim olgeta samting i stap bihain."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Go long “PYUSD” pes long PayPal app o website bilong yu. Presim “Transfer” button %(icon)s, na bihain “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transferim %(amount)s long %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Baem Bitcoin (BTC) long Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Go long “Bitcoin” (BTC) peij long Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Baem liklik moa (mipela i rikumendim %(more)s moa) winim mani yu laik givim (%(amount)s), bilong karamapim transaction fees. Yu bai holim olgeta samting i stap yet."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transferim Bitcoin long adres bilong mipela"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klikim “Send bitcoin” button bilong mekim “withdrawal”. Senisim long dollars i go long BTC bai yu presim %(icon)s ikon. Putim BTC amount ananit na klikim “Send”. Lukim <a %(help_video)s>dispela video</a> sapos yu gat hevi."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Long liklik donesen (aninit $25), yu inap yusim Rush o Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Baim Bitcoin (BTC) long Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Go long “Crypto” peij long Revolut long baim Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Baim liklik moa (mipela i ting %(more)s moa) winim mani yu laik donetim (%(amount)s), long karamapim transaction fees. Yu bai holim olgeta samting i stap."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfaim Bitcoin long adres bilong mipela"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klikim “Send bitcoin” button blong mekim “withdrawal”. Senisim long euro i go long BTC long presim %(icon)s aikon. Putim BTC amaunt long daunbilo na klikim “Send”. Lukim <a %(help_video)s>dispela video</a> sapos yu bungim hevi."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Yu mas yusim dispela BTC amaunt antap, <em>NOGAT</em> euros o dola, nogut mipela no inap kisim stret amaunt na no inap long autim konfirmesen bilong membesip bilong yu."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Long liklik donesen (aninit $25) yu inap nidim yusim Rush o Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Yusim wanpela bilong ol “kredit kad long Bitcoin” express sevis, we i kisim sampela minit tasol:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Plis pulimapim ol dispela detel long form:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin mani:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Plis yusim dispela <span %(underline)s>stretpela mani</span>. Total kost bilong yu inap i go antap long wanem bilong credit card fees. Long liklik mani, dispela inap i winim diskaun bilong mipela, sori tru."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adres (external wallet):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s inskraksen"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Mipela sapotim tasol standard veson bilong crypto coins, nogat ol narapela netwok o veson bilong coins. Em inap kisim inap long wanpela aua long konfemim transaksen, dipen long coin."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR Code to Pay"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan this QR code with your crypto wallet app to quickly fill in the payment details"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon gift card"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Plis yusim <a %(a_form)s>ofisel Amazon.com form</a> long salim mipela wanpela gift card bilong %(amount)s long email adres i stap aninit."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Mipela no inap kisim ol narapela rot bilong gift cards, <strong>tasol ol i kam stret long ofisel form long Amazon.com</strong>. Mipela no inap givim bek gift card bilong yu sapos yu no yusim dispela form."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Raitim stretpela mani: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Plis no raitim yu yet wanpela mesej."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“To” risipien email long form:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Uniq long akaun bilong yu, no ken serim."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Yusim wanpela taim tasol."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Wetim gift card… (refreshim pes long lukim)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Bihain long salim gift card bilong yu, sistem bilong mipela bai konfemim em insait long sampela minit. Sapos dispela i no wok, traim resalim gift card bilong yu (<a %(a_instr)s>inskraksen</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Sapos dispela i no wok yet, plis emailim mipela na Anna bai lukim em long han (em inap kisim sampela de), na tokim sapos yu bin traim resalim pinis."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Eksampel:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Nating long nem bilong akaun o piksa i luk olsem i narakain. No ken wari! Ol dispela akaun i stap aninit long ol donation patna bilong mipela. Akaun bilong mipela i no bin hakim."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay inskraksen"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donet long Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donetim olgeta mani bilong %(total)s yusim <a %(a_account)s>dispela Alipay akaun</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Sapos peij bilong donesen i blok, traim narapela internet koneksen (olsem VPN o fon internet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Sori, Alipay pes i save stap tasol long <strong>mainland China</strong>. Yu inap nid long disablim VPN bilong yu taimporeri, o yusim VPN long mainland China (o Hong Kong tu save wok sampela taim)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Mekim donesen (skanim QR kod o presim batin)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Opim <a %(a_href)s>QR-kod donesen pes</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Skanim QR kod wantaim Alipay app, o presim batin bilong opim Alipay app."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Plis wet liklik; pes i stap long China na em inap kisim taim long load."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Olsem wanem long WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donet long WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donet olgeta mani bilong %(total)s yusim <a %(a_account)s>dispela WeChat akaun</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Olsem wanem long Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donet long Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donet olgeta mani bilong %(total)s yusim <a %(a_account)s>dispela Pix akaun"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Salim email long mipela wantaim risit"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Salim wanpela risit o screenshot long personal verification adres bilong yu. NO yusim dispela email adres bilong PayPal donesen bilong yu."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Salim risit o screenshot long pesonal verifikesen adres bilong yu:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Sapos kripto eksens reit i senis long taim bilong transaksen, yu mas putim risit i soim orijinal eksens reit. Mipela i tenkyu tru long yu long yusim kripto, em i helpim mipela planti!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Taim yu bin salim email wantaim risit, klikim dispela button, bai Anna i ken lukim em (em inap kisim sampela de):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Yes, mi bin salim risit bilong mi"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Tenkyu long donesen bilong yu! Anna bai manimol aktivet membesip bilong yu insait long sampela de."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Step-by-step gaim"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Sampela step i tok long kripto wales, tasol no wari, yu no nid long lainim samting long kripto bilong dispela."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Putim email bilong yu."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Makim rot bilong peim mani."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Makim rot bilong peim mani gen."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Makim “Self-hosted” wales."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Klikim “Mi konfirmim owenesip”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "Yu bai kisim wan email risit. Plis salim i kam long mipela, na mipela bai konfirmim donesen bilong yu kwiktaim."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Plis wet inap <span %(span_hours)s>24 aua</span> (na refresim dispela peij) bipo yu kontakim mipela."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Sapos yu mekim wan rong long taim bilong peimen, mipela no inap givim bek mani, tasol mipela bai traim stretim."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Ol donesen bilong mi"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Ol donesen detel i no soim long ol manmeri."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nogat donesen yet. <a %(a_donate)s>Mekim namba wan donesen bilong mi.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Mekim narapela donesen."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Ol fael yu daunlodim"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Ol daunlod long Fast Partner Servers i makim wantaim %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Sapos yu daunlodim wan fael wantaim kwik na slou daunlod, em bai soim tupela taim."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Ol kwik daunlod long las 24 aua i kaun long deili limit."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Ol taim i stap long UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Ol fael yu daunlodim i no soim long ol manmeri."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nogat fael daunlod yet."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Las 18 aua"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Bipo"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Akaun"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Log in / Rigista"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Akaun ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Pablik profail: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Sikret ki (no ken serim!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "soim"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Membership: <strong>%(tier_name)s</strong> inap %(until_date)s <a %(a_extend)s>(mekim moa)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Membership: <strong>Ino</strong> <a %(a_become)s>(kamap memba)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Kwik download i bin yus (long 24 aua i go pinis): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "olsem wanem download?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Eksklusiv Telegram grup: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Joinim mipela hia!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Apgreid long <a %(a_tier)s>nambawan level</a> bilong joinim grup bilong mipela."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontakim Anna long %(email)s sapos yu laik apgreidim membership bilong yu long nambawan level."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontak email"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Yu inap bungim planti membership (kwik download long 24 aua bai bung wantaim)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Public profile"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Ol file yu bin daunlod"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Ol donesen bilong mi"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Logaut"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Yu nau logaut pinis. Rilodim peij bilong login gen."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Rijistresen i orait! Sikret ki bilong yu em: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Lukautim gut dispela ki. Sapos yu lusim, yu bai lusim akses long akaun bilong yu."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Makim.</strong> Yu inap makim dispela peij long kisim bek ki bilong yu.</li><li %(li_item)s><strong>Daunlod.</strong> Klik <a %(a_download)s>dispela link</a> long daunlodim ki bilong yu.</li><li %(li_item)s><strong>Paswod manija.</strong> Yusim paswod manija long seivim ki taim yu putim em ananit.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Putim sikret ki bilong yu long login:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Sikret ki"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Login"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Kisim yu givim i no stret. Traim gen, o yu ken makim nupela akaun long daunbilo."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "No ken lusim kisim yu!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Yu no gat akaun yet?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Makim nupela akaun"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Sapos yu lusim kisim yu, plis <a %(a_contact)s>kontakim mipela</a> na givim planti infomesen."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Yu inap mas wokim nupela akaun taim yu laik kontakim mipela."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Olpela email-based akaun? Putim <a %(a_open)s>email bilong yu hia</a>."

#, fuzzy
msgid "page.list.title"
msgstr "List"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "editim"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Seivim"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Seivim pinis. Plis reloadim peij."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Samting i rong. Traim gen."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "List bilong %(by)s, wokim <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "List i nating."

#, fuzzy
msgid "page.list.new_item"
msgstr "Putim o rausim long dispela list bai painim wanpela file na opim “Lists” tab."

#, fuzzy
msgid "page.profile.title"
msgstr "Profail"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profail i no stap."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "editim"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Senisim nem yu soim. Identifier (hap bihain “#”) i no inap senis."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Salim"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Salim. Plis reloadim peij."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Samting i rong. Plis traem gen."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profail i kamap <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "List"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Nogat list yet"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Mekim niu list long painim wanpela file na opim “List” tab."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reform bilong copyright em i nidim long lukautim seifti bilong kantri"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Ol LLM bilong China (olsem DeepSeek) ol i bin kisim skul long ol buk na pepa bilong mi we i stap long arkaiv bilong mi we i no gat lo — em i bikpela tru long olgeta hap bilong graun. Ol Wes i nidim long senisim lo bilong copyright olsem wanpela samting bilong lukautim seifti bilong kantri."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "ol arikil bilong TorrentFreak: <a %(torrentfreak)s>namba wan</a>, <a %(torrentfreak_2)s>namba tu</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "I no longtaim i go pinis, ol “sado-laibreri” i bin dai. Sci-Hub, bikpela arkaiv bilong akademik pepa we i no gat lo, i bin stopim kisim ol nupela wok, long wanem ol kot i bin sutim ol. “Z-Library”, bikpela laibreri bilong buk we i no gat lo, i bin lukim ol man i wokim dispela i bin arestim long kot bilong copyright. Ol i bin abrusim arest bilong ol, tasol laibreri bilong ol i stap long hevi yet."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Taim Z-Library i bin bungim hevi bilong pasim, mi bin redi pinis long bekapim olgeta buk bilong ol na mi bin painim wanpela ples bilong putim ol. Dispela em i bin as bilong mi long statim Arkaiv bilong Anna: wanpela wok bilong gohet long misin bilong ol wok bipo. Nau mipela i kamap bikpela sado laibreri long olgeta hap bilong graun, i holim winim 140 milion buk we i gat copyright long ol kain kain format — buk, akademik pepa, magasin, niuspepa, na moa yet."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mi na tim bilong mi i stap olsem ol man bilong tingting. Mipela i bilip olsem lukautim na holim ol dispela fail em i stret long sait bilong moral. Ol laibreri long olgeta hap bilong graun i lukim ol mani bilong ol i katim, na mipela i no inap bilipim ol korporesen long lukautim ol samting bilong ol manmeri."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Bihain AI i kamap. Ol bikpela kampani i wokim LLM i bin kontakim mipela long kisim skul long data bilong mipela. Planti (tasol i no olgeta!) kampani bilong US i bin tingim gen taim ol i luksave long wok bilong mipela i no gat lo. Long narapela sait, ol kampani bilong China i bin kisim amamas long bungim ol samting bilong mipela, i no wari long lo bilong en. Dispela i makim samting bikpela taim yu lukim China i bin sainim olgeta bikpela agrimen bilong copyright long intanasonal."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Mipela i bin givim akses long 30 kampani. Planti bilong ol em i LLM kampani, na sampela em i data broka, husat bai salim gen ol samting bilong mipela. Planti bilong ol em i bilong China, tasol mipela i bin wok wantaim ol kampani bilong US, Yurop, Rusia, Saut Korea, na Japan. DeepSeek <a %(arxiv)s>i tokaut</a> olsem wanpela bipo model bilong ol i bin kisim skul long hap bilong ol samting bilong mipela, tasol ol i no laik tokaut long nupela model bilong ol (i luk olsem i kisim skul long data bilong mipela tu)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Sapos ol Wes i laik stap antap long res bilong LLM, na long las, AGI, ol i nidim long tingim gen tingting bilong ol long copyright, na kwiktaim. Maski yu tingim mipela long sait bilong moral o nogat, dispela nau i kamap wanpela samting bilong ekonomi, na tu bilong seifti bilong kantri. Olgeta pawa blok i wokim ol super-saintis, super-haka, na super-militeri. Fridom bilong infomesen i kamap wanpela samting bilong stap laip bilong ol dispela kantri — na tu bilong seifti bilong kantri."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Tim bilong mipela i kam long olgeta hap bilong graun, na mipela i no gat wanpela lain. Tasol mipela i laik strongim ol kantri we i gat strongpela lo bilong copyright long yusim dispela hevi bilong senisim ol. Olsem wanem bai yumi mekim?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Namba wan tingting bilong mipela i klia: katim sot taim bilong copyright. Long US, copyright i stap 70 yia bihain long dai bilong raita. Dispela i no stret. Mipela inap bringim dispela i go wantaim patent, we i stap 20 yia bihain long filing. Dispela i mas inap long ol raita bilong buk, pepa, musik, art, na ol narapela wok bilong kreatif, long kisim pe bilong ol (olsem ol projek longpela taim olsem adaptesen bilong muvi)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Bihain, long liklik samting, ol man i wokim lo i mas putim ol eksen bilong lukautim na salim ol teks. Sapos lusim mani long ol kastoma i stap bikpela wari, distribusen long pesonal level inap stap tambu yet. Long narapela sait, ol man i save long lukautim bikpela arkaiv — ol kampani i wokim LLM, wantaim ol laibreri na ol narapela arkaiv — bai stap aninit long ol eksen bilong dispela."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Sampela kantri i wokim pinis wanpela kain olsem. TorrentFreak <a %(torrentfreak)s>i ripot</a> olsem China na Japan i bin putim AI eksen long lo bilong copyright bilong ol. I no klia long mipela olsem dispela i wok wantaim ol agrimen bilong intanasonal, tasol i givim proteksen long ol kampani bilong ol, we i eksplenim wanem mipela i bin lukim."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Long sait bilong Arkaiv bilong Anna — mipela bai gohet long wok aninit long graun we i stap long moral. Tasol bikpela laik bilong mipela em long kamap long lait, na mekim bikpela wok long sait bilong lo. Plis senisim lo bilong copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Ritim ol arikil bilong TorrentFreak: <a %(torrentfreak)s>namba wan</a>, <a %(torrentfreak_2)s>namba tu</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Ol winim $10,000 ISBN visualisation bounty"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Mipela i kisim ol bikpela wok long $10,000 ISBN visualisation bounty."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Sampela mun i go pinis mipela i bin tokaut long wanpela <a %(all_isbns)s>$10,000 bounty</a> long mekim nambawan visualisation bilong data bilong mipela i soim ISBN hap. Mipela i bin strongim long soim wanem fail mipela i gat/nogat pinis, na bihain wanpela dataset i soim hamas laibreri i holim ISBN (wanpela mak bilong raris)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Mipela i bin pulap long ol bekim. I gat planti kreativiti. Bikpela tenkyu long olgeta husat i bin joinim: strong na amamas bilong yupela i save go long ol narapela!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Las, mipela laik long bekim ol dispela askim: <strong>ol buk i stap long dispela graun, hamas buk mipela bin putim pinis long arkaiv, na ol buk wanem mipela mas lukluk long en bihain?</strong> Em i gutpela long lukim planti manmeri i tingim ol dispela askim."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Mipela stat wantaim wanpela isi piksa bilong mipela yet. Insait long 300kb tasol, dispela piksa i soim klia bikpela “list bilong ol buk” i op long olgeta manmeri long histori bilong man:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Ol ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Ol fail insait long Arkaiv bilong Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "Ol CADAL SSNO"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC data leak"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "Ol DuXiu SSID"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russian State Library"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperial Library of Trantor"

#, fuzzy
msgid "common.back"
msgstr "Bek"

#, fuzzy
msgid "common.forward"
msgstr "Go pas"

#, fuzzy
msgid "common.last"
msgstr "Las"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Lukim ples <a %(all_isbns)s>blog post bilong mipela</a> bilong kisim moa save."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Mipela i putim wanpela challenge bilong mekim gutpela moa dispela. Mipela bai givim namba wan prais $6,000, namba tu $3,000, na namba tri $1,000. Long bikpela tingting na gutpela wok bilong ol, mipela i makim long mekim bikpela liklik prais, na givim namba tri prais long foapela manmeri, $500 long wanwan. Ol manmeri i win i stap daunbilo, tasol lukim olgeta wok <a %(annas_archive)s>long hia</a>, o daunlodim <a %(a_2025_01_isbn_visualization_files)s>torrent bilong mipela</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Namba wan prais $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Dispela <a %(phiresky_github)s>wok</a> (<a %(annas_archive_note_2951)s>Gitlab toktok</a>) em i olgeta samting mipela i laikim, na moa yet! Mipela i save laikim tru ol flexible visualization options (na sapotim custom shaders), tasol wantaim wanpela gutpela list bilong presets. Mipela i save laikim tu olsem olgeta samting i wok kwik na isi, na em i no gat backend, na gutpela minimap, na planti save long <a %(phiresky_github)s>blog post bilong ol</a>. Gutpela wok tru, na em i win tru!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Namba tu prais $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Narapela gutpela <a %(annas_archive_note_2913)s>wok</a>. Em i no flexible olsem namba wan, tasol mipela i save laikim tru macro-level visualization bilong en (space-filling curve, borders, labeling, highlighting, panning, na zooming). Wanpela <a %(annas_archive_note_2971)s>toktok</a> bilong Joe Davis i save kamap long tingting bilong mipela:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Taim ol square na rectangle i luk gut long matimatiks, ol i no save givim gutpela locality long mapping context. Mi bilip olsem asymmetry bilong ol Hilbert o classic Morton i no wanpela rong tasol em i wanpela samting bilong luksave. Olsem Italy i gat wanpela boot-shaped outline em i save luksave long map, ol unique \"quirks\" bilong ol curves i ken kamap olsem cognitive landmarks. Dispela distinctiveness i ken mekim gutpela memory bilong ples na helpim ol yusa long save long ples, na mekim isi long painim ol spesifik hap o luksave long ol pattern.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Na yet planti opsen bilong visualizing na rendering, wantaim wanpela isi na gutpela UI. Wanpela strong namba tu!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Namba tri prais $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Long dispela <a %(annas_archive_note_2940)s>wok</a> mipela i save laikim tru ol kain kain lukluk, long wanem mipela i save laikim tru comparison na publisher views."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Namba tri prais $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Tasol em i no gat gutpela UI, dispela <a %(annas_archive_note_2917)s>wok</a> i save mekim planti samting. Mipela i save laikim tru comparison feature bilong en."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Namba tri prais $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Olsem namba wan, dispela <a %(annas_archive_note_2975)s>wok</a> i save mekim mipela i tingting long flexibility bilong en. Long pinis dispela em i mekim gutpela visualization tool: bikpela flexibility bilong ol power yusa, tasol mekim isi long ol average yusa."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Namba tri prais $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Las <a %(annas_archive_note_2947)s>wok</a> bilong kisim prais em i priti basik, tasol i gat sampela unique features mipela i save laikim tru. Mipela i save laikim olsem ol i soim hamas datasets i save karamapim wanpela ISBN olsem wanpela mak bilong popularity/reliability. Mipela i save laikim tru simplicity tasol effectiveness bilong yusim opacity slider bilong comparisons."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Notable ideas"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Sampela moa ideas na implementations mipela i save laikim tru:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Ol i haus antap bilong ol samting we i stap rabis"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Ol namba i stap laip"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Ol makim tok, na tu ol namba i stap laip"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Narapela kain lukluk bilong map na ol filt"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Kainkain kala na hetmap we i kol"

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Isi long tanim ol Datasets bilong kwikpela komper"

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Ol makim tok we i luk nais"

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skel ba i gat namba bilong ol buk"

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Planti slida bilong komperim ol Datasets, olsem yu DJ"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Mipela inap go yet longpela taim, tasol mipela bai stop nau. Lukim olgeta sabmisen <a %(annas_archive)s>hia</a>, o daunlodim <a %(a_2025_01_isbn_visualization_files)s>kombo torrent</a> bilong mipela. Planti sabmisen, na wan wan i bringim narapela lukluk, maski long UI o implementesen."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Mipela bai putim namba wan sabmisen long main website bilong mipela, na ating sampela narapela tu. Mipela tu i stat tingting long hau long bungim, konfirmim, na den putim ol buk we i rabis. Planti moa bai kamap long dispela hap."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Tenkyu olgeta husat i bin join. Em i nambawan tru olsem planti manmeri i tingim."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Ol bel bilong mipela i pulap long tenkyu."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Lukim Ol ISBN — $10,000 banti long 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dispela piksa i makim bikpela “list bilong ol buk” we i op long olgeta manmeri long histori bilong man."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dispela piksa i 1000×800 piksel. Wan wan piksel i makim 2,500 ISBN. Sapos mipela i gat wanpela fail bilong wanpela ISBN, mipela i mekim dispela piksel i mo gre. Sapos mipela i save wanpela ISBN i bin kamap, tasol mipela i no gat fail bilong en, mipela i mekim i mo ret."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Long hap 300kb tasol, dispela piksa i soim bikpela “list bilong ol buk” we i op long olgeta manmeri long histori bilong man (sampela handet GB i kompres long ful)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Em tu i soim: i gat planti wok i stap yet long bekapim ol buk (mipela i gat 16% tasol)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Baksait"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Olsem wanem Anna’s Archive inap long kisim mak bilong em long bekim olgeta save bilong ol manmeri, sapos em ino save long ol buk i stap yet? Mipela nidim wanpela TODO list. Wanpela rot bilong mekim dispela em long yusim ISBN namba, we stat long 1970s ol i bin givim long olgeta buk i kamap (long planti kantri)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Igat nogat wanpela sentral otoriti i save long olgeta ISBN namba. Nogat, em wanpela sistem i stap long planti hap, we ol kantri i kisim ol namba, na ol i givim liklik namba i go long ol bikpela pablisha, na ol i ken givim moa liklik namba i go long ol liklik pablisha. Las, ol namba bilong wanwan buk i stap."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Mipela i stat long mapim ol ISBN <a %(blog)s>tupela yia i go pinis</a> wantaim scrape bilong mipela long ISBNdb. Stat long dispela taim, mipela i bin scrape planti moa metadata sos, olsem <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, na moa yet. Wanpela ful list i stap long ol “Datasets” na “Torrents” peij long Anna’s Archive. Nau mipela i gat bikpela na op long olgeta, isi long daunlodim koleksen bilong buk metadata (na olsem tu ISBNs) long olgeta hap bilong graun."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Mipela i <a %(blog)s>raitim planti samting</a> long as mipela i tingim presave, na as mipela i stap long wanpela bikpela taim nau. Mipela mas nau painim ol buk i rabis, ol buk i no gat planti luksave, na ol buk i stap long bikpela risk na presaveim ol. Gat gutpela metadata long olgeta buk long graun i helpim dispela."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Lukluk"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Narapela long dispela lukluk piksa, mipela inap lukluk tu long wanwan datasets mipela i kisim. Yusim dropdown na ol button bilong senisim ol."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Igat planti intresing paten long lukim long ol dispela piksa. Bilong wanem igat sampela regulariti bilong lain na blok, olsem i kamap long narapela skel? Olsem wanem ol hap i stap nating? Bilong wanem sampela datasets i bung wantaim? Mipela bai lusim ol dispela askim olsem eksasais bilong ridim."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 banti"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Igat planti samting long painimaut hia, olsem na mipela i autim wanpela banti bilong improvim dispela lukluk antap. Nogat olsem planti bilong ol banti bilong mipela, dispela wan em i gat taim. Yu mas <a %(annas_archive)s>submit</a> open source code bilong yu long 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Dispela best submission bai kisim $6,000, sekon ples em $3,000, na namba tri ples em $1,000. Olgeta banti bai givim yusim Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Aninit em ol liklik kriterian. Sapos nogat submission i mitim ol kriterian, mipela inap yet givim sampela banti, tasol dispela bai stap long disgresen bilong mipela."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forkim dispela repo, na editim dispela blog post HTML (nogat narapela backend long sait bilong Flask backend bilong mipela)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Mekim piksa antap i ken zoom isi, olsem yu ken zoom i go long wanwan ISBN. Klikim ISBNs bai kisim yu i go long metadata peij o searchem long Anna’s Archive."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Yu mas yet inap long senisim olgeta narapela datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Kantri renj na pablisha renj i mas laitap taim yu hover. Yu ken yusim e.g. <a %(github_xlcnd_isbnlib)s>data4info.py long isbnlib</a> bilong kantri info, na scrape bilong mipela “isbngrp” bilong pablisha (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Em i mas wok gut long desktop na mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Long kisim bonus point (ol dispela em tasol ol aidea — larim kreativiti bilong yu i go fri):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Strong tingting bai givim long yusabiliti na olsem wanem em i luk gut."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Soim tru metadata bilong wanwan ISBN taim yu zoom i go insait, olsem taitel na raita."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Gutpela spais-filling curve. E.g. wanpela zig-zag, i go long 0 i go long 4 long namba wan ro na bihain i go bek (long bek) long 5 i go long 9 long namba tu ro — i go bek gen."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Ol narakain o kolim kolim bilong kala."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Ol spesel lukluk bilong skelim ol Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Ol rot bilong stretim ol hevi, olsem narapela metadata we i no wok gut wantaim (olsem ol taitel i narapela tru)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Raitim ol toktok long ol piksa wantaim ol koment long ISBN o ol renj."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Ol rot bilong painim ol buk we i rabis o i stap long risk."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Olgeta kainkain kranki tingting yu inap kamap wantaim!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Yu INAP lusim olgeta liklik mak, na mekim narapela kain lukluk. Sapos em i gutpela tru, orait em i inap kisim bounti, tasol long laik bilong mipela."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Mekim ol sapmisen long raitim wanpela koment long <a %(annas_archive)s>dispela isyu</a> wantaim wanpela link long yu yet i wokim repo, makim rikwes, o diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Ol kod bilong wokim ol dispela piksa, wantaim ol narapela eksampel, inap painim long <a %(annas_archive)s>dispela direktori</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Mipela i kamapim wanpela liklik data format, we olgeta ISBN infomesen i stap long 75MB (kompres). Dispela data format na kod bilong wokim inap painim <a %(annas_archive_l1244_1319)s>hia</a>. Long bounti yu no mas yusim dispela, tasol em i save gutpela long stat wantaim. Yu inap senisim metadata bilong mipela olsem yu laik (tasol olgeta kod bilong yu mas open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Mipela i no inap wet long lukim wanem yu kamap wantaim. Lukim yu!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna’s Archive Kontena (AAC): standardaisim ol rilisim bilong bikpela sado laibreri bilong graun"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’s Archive i kamap bikpela sado laibreri bilong graun, na mipela i mas standardaisim ol rilisim bilong mipela."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’s Archive</a> i kamap bikpela tru sado laibreri bilong graun, na em tasol sado laibreri bilong dispela skel we i open-source na open-data olgeta. Aninit i stap wanpela tebol long Datasets peij bilong mipela (liklik senis i stap):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Mipela i wokim dispela long tripela rot:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Mekim sem long ol open-data sado laibreri we i stap (olsem Sci-Hub na Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Halivim ol sado laibreri we i laik kamap moa open, tasol i no gat taim o risos bilong mekim olsem (olsem Libgen comics koleksen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Skrepim ol laibreri we i no laik sharim long bikpela (olsem Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Long dispela, mipela i stap bosim planti koleksen bilong torrents yet (100s bilong TBs). I stap nau, mipela i bin lukim ol dispela koleksen olsem wan wan samting, mining mipela i wokim narapela kain infrastaksa na data orgeanisasin bilong wan wan koleksen. Dispela i mekim bikpela wok long wan wan rilisim, na i mekim i hat tru long mekim moa liklik rilisim."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Olsem na mipela i makim long mekim wankain long ol rilisim bilong mipela. Dispela em wanpela teknikal blog post we mipela i intodusi wankain: <strong>Anna’s Archive Containers</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Desain gol"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "As bilong yus bilong mipela em long distribiusen bilong ol fail na metadata bilong ol narapela koleksen. Ol bikpela tingting bilong mipela em:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Ol fail na metadata i no wankain, long klostu long orijinal format."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Ol idenitifaias i no wankain long ol laibrari bilong as, o nogat idenitifaias."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Saparit rilisim bilong metadata vs fail data, o metadata tasol rilisim (e.g. ISBNdb rilisim bilong mipela)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribiusen long torrents, tasol wantaim posibol bilong narapela rot bilong distribiusen (e.g. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Ol rikod i no ken senis, bikos mipela i mas tingim olsem ol torrents bilong mipela bai stap oltaim."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrimental rilisim / apendabol rilisim."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Masin i ken rit na rait, kwik na isi, speseli bilong stack bilong mipela (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Sampela isi long lukluk bilong man, tasol dispela em sekondari long masin i ken rit."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Isi long putim ol koleksen bilong mipela wantaim wankain rentim seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binari data i ken go stret long webservers olsem Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Sampela samting mipela i no tingim:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Mipela i no tingim olsem ol fail i ken isi long lukluk long disk, o painimaut wantaimaut preprocessing."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Mipela i no tingim olsem i ken stret wankain wantaim ol laibrari software i stap."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Taim i mas isi long wanpela long putim ol koleksen bilong mipela wantaim torrents, mipela i no tingim olsem ol fail i ken yusim wantaimaut bikpela teknikal save na komitmen."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Bikos Anna’s Archive em open source, mipela i laik yusim format bilong mipela yet. Taim mipela i refresim searc index bilong mipela, mipela i go long ol pablik ples tasol, olsem na wanpela i ken kisim na wokim kwik."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Wankain"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Las, mipela i makim wanpela isi na klia stail. Em i no stretpela, na em i wok yet long kamap gut."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archive Container) em i wanpela samting tasol i gat <strong>metadata</strong>, na sapos yu laik, <strong>binary data</strong>, tupela i no inap senis. Em i gat wanpela namba bilong em yet long olgeta hap, ol i kolim <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Koleksen.</strong> Olgeta AAC i stap long wanpela koleksen, we long tok i klia, em i wanpela lis bilong ol AACs we i semantik i wankain. Dispela i makim olsem sapos yu mekim bikpela senis long format bilong metadata, orait yu mas wokim wanpela nupela koleksen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“rekod” na “fael” koleksen.</strong> Long pasin bilong ol, em i save gutpela long putim “rekod” na “fael” olsem narapela koleksen, bai ol inap putim long narapela taim, olsem long skraping rates. “Rekod” em i koleksen bilong metadata tasol, i gat ol infomesen olsem buk taitel, raita, ISBN, na ol arapela, tasol “fael” em ol koleksen i gat ol rilis fael yet (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Format bilong AACID em olsem: <code style=\"color: #0093ff\">aacid__{koleksen}__{ISO 8601 timestamp}__{koleksen-specific ID}__{shortuuid}</code>. Olsem, wanpela tru AACID mipela i rilis em <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{koleksen}</code>: nem bilong koleksen, we inap gat ASCII leta, namba, na andaskor (tasol nogat tupela andaskor)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: wanpela sotpela vesin bilong ISO 8601, oltaim long UTC, olsem <code>20220723T194746Z</code>. Dispela namba i mas go antap long olgeta rilis, tasol mining bilong en inap senis long olgeta koleksen. Mipela i tingim long yusim taim bilong skraping o wokim ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{koleksen-specific ID}</code>: wanpela koleksen-specific mak, sapos i wok, olsem Z-Library ID. Inap lusim o katim. Mas lusim o katim sapos AACID bai i go bikpela moa long 150 karakter."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: wanpela UUID tasol i kompres long ASCII, olsem yusim base57. Nau mipela i yusim <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python laibrari."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID renj.</strong> Sapos AACIDs i gat ol timestamp i go antap, mipela inap yusim dispela long makim renj insait long wanpela koleksen. Mipela yusim dispela format: <code style=\"color: blue\">aacid__{koleksen}__{from_timestamp}--{to_timestamp}</code>, we ol timestamp i insait. Dispela i wankain wantaim ISO 8601 notesen. Ol renj i go yet, na inap laplap, tasol sapos i laplap mas gat wankain rekod olsem bipo rilis long dispela koleksen (bikos AACs i no inap senis). Nogat rekod i no inap."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata fael.</strong> Wanpela metadata fael i gat metadata bilong wanpela renj bilong AACs, long wanpela koleksen. Ol i gat ol dispela pasin:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Fael nem i mas wanpela AACID renj, i stat wantaim <code style=\"color: red\">annas_archive_meta__</code> na bihainim wantaim <code>.jsonl.zstd</code>. Olsem, wanpela bilong mipela rilis i kolim<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Olsem fael ekstensen i soim, fael kaind em <a %(jsonlines)s>JSON Lines</a> i kompres wantaim <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Olgeta JSON objek i mas gat ol dispela filed long top level: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (sapos yu laik). Nogat narapela filed i orait."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> em i fri metadata, long semantik bilong koleksen. Em i mas semantik i wankain insait long koleksen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> em i sapos yu laik, na em i nem bilong binary data folder we i gat ol binary data bilong en. Fael nem bilong ol binary data insait long dispela folder em rekod bilong AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Dispela <code style=\"color: red\">annas_archive_meta__</code> prefiks inap senis long nem bilong institusen bilong yu, olsem <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binary data folder.</strong> Wanpela folder wantaim ol binary data bilong wanpela renj bilong AACs, long wanpela koleksen. Ol i gat ol dispela pasin:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Direktori nem i mas wanpela AACID renj, i stat wantaim <code style=\"color: green\">annas_archive_data__</code>, na nogat sufiks. Olsem, wanpela bilong mipela rilis i gat wanpela direktori i kolim<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Direktori i mas gat ol data fael bilong olgeta AACs insait long makim renj. Olgeta data fael i mas gat AACID bilong en olsem fael nem (nogat ekstensen)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Em i gutpela long mekim ol dispela folda i stap long saiz we i no bikpela tumas, olsem nogat bikpela moa long 100GB-1TB long wan wan, tasol dispela tingting inap senis long taim i go."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Ol metadata fail na ol binari data folda inap bung wantaim long torrents, wantaim wan torrent long wan metadata fail, o wan torrent long wan binari data folda. Ol torrents mas gat orijinal fail/direktori nem wantaim <code>.torrent</code> long pinis bilong nem bilong ol."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Eksampel"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Yumi ken lukim long laspela Z-Library rilisim olsem wanpela eksampel. Em i gat tupela koleksen: “<span style=\"background: #fffaa3\">zlib3_records</span>” na “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dispela i larim yumi long separetim na rilisim metadata rikods long ol buk fail yet. Olsem na, mipela i rilisim tupela torrents wantaim metadata fail:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Mipela tu i rilisim planti torrents wantaim binari data folda, tasol long “<span style=\"background: #ffd6fe\">zlib3_files</span>” koleksen tasol, 62 olgeta:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Taim yumi ranim <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> yumi ken lukim wanem i stap insait:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Long dispela kes, em i metadata bilong wanpela buk olsem Z-Library i ripotim. Long top-level yumi gat “aacid” na “metadata” tasol, tasol nogat “data_folder”, bikos nogat binari data i stap wantaim. AACID i gat “22430000” olsem praimeri ID, we yumi ken lukim em i kisim long “zlibrary_id”. Yumi ken ekspektim ol narapela AAC long dispela koleksen i gat wankain strakta."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nau yumi ranim <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dispela em i liklikpela AAC metadata, tasol bikpela hap bilong dispela AAC i stap long narapela hap long binari fail! Bihain, yumi gat “data_folder” long dispela taim, olsem na yumi ken ekspektim binari data i stap long <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” i gat “zlibrary_id”, olsem na yumi ken isi long asosiatim wantaim AAC long “zlib_records” koleksen. Yumi inap asosiatim long planti narapela rot, olsem long AACID — standard i no tokim olsem."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nating olsem, em i no nidim long “metadata” fil i yet i stap olsem JSON. Em inap stap olsem string i gat XML o narapela data format. Yu inap putim metadata infomesen long binari blob i asosiatim, olsem sapos em i planti data."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Konklusen"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Wantaim dispela standard, yumi ken mekim rilisim i go long liklik liklik hap, na isi long putim ol niupela data sos. Mipela i gat sampela eksaiting rilisim i stap long lain!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Mipela tu i hop olsem bai isi moa long ol narapela sado laibrari long mirorim ol koleksen bilong mipela. Bihain, as bilong mipela em long preserim save na kalja bilong ol manmeri oltaim, olsem na moa ridaunensi em i gutpela."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Apdeit bilong Anna: ful opun sos arkaiv, ElasticSearch, 300GB+ bilong buk kavaz"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Mipela i wok strong olgeta long provaidim gutpela alternetiv wantaim Arkaiv bilong Anna. Hia sampela samting mipela i bin winim long las taim."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Wantaim Z-Library i go daun na ol (alleged) faunda bilong en i arestim, mipela i wok strong olgeta long provaidim gutpela alternetiv wantaim Arkaiv bilong Anna (mipela bai no linkim hia, tasol yu ken Googleim). Hia sampela samting mipela i bin winim long las taim."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Arkaiv bilong Anna em i ful opun sos"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Mipela i bilip olsem infomesen i mas fri, na kod bilong mipela yet i no eksen. Mipela i rilisim olgeta kod bilong mipela long praiwetli hostim Gitlab instans: <a %(annas_archive)s>Sopwea bilong Anna</a>. Mipela tu i yusim isiu trakim long oganaisim wok bilong mipela. Sapos yu laik involvim wantaim divelopmen bilong mipela, dispela em i gutpela ples long statim."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Long givim yu wanpela teist bilong ol samting mipela i wok long en, kisim wok bilong mipela long klien-said performens impruvmen. Sapos mipela i no implementim pagination yet, mipela bai ofen ritonim longpela tumas searc peij, wantaim 100-200 risalts. Mipela i no laik katim of searc risalts kwiktaim, tasol dispela i min olsem bai em i slou daun sampela divais. Long dispela, mipela i implementim liklik trik: mipela i wrapim bikpela hap bilong searc risalts long HTML koments (<code><!-- --></code>), na bihain raitim liklik Javascript we bai detektim taim wanpela risalt i mas kamap visibol, long dispela taim mipela bai unwrapim koment:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualization\" i stap long 23 lain, nogat nid long ol fancy libraries! Dispela kain kwik pragmatik kod em yu kisim taim yu gat liklik taim, na ol rilis problem i nid long solvim. I bin ripot olsem nau sevis bilong mipela i wok gut long ol slak divais!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Narapela bikpela wok em long automateim wok bilong bildim database. Taim mipela i stat, mipela i bungim ol narapela sos wantaim. Nau mipela i laik mekim ol dispela sos i stap apdet, olsem na mipela i raitim planti skrip long daunlodim niu metadata long tupela Library Genesis forks, na bungim ol. Aim bilong mipela em long mekim dispela i yusful long arkaiv bilong mipela, na mekim i isi long ol manmeri husat i laik pilai wantaim metadata bilong shadow library. Aim em long gat wanpela Jupyter notebook we i gat olgeta kain metadata i stap, olsem mipela inap mekim moa risas olsem painimaut wanem <a %(blog)s>pesent bilong ISBNs i stap oltaim</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Las, mipela i senisim sistem bilong donesen. Nau yu inap yusim kredit kad long putim mani stret long ol crypto wallets bilong mipela, wantaimaut nid long save long ol cryptocurrencies. Mipela bai lukluk yet long hau gut dispela i wok long praktis, tasol dispela em wanpela bikpela samting."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Senisim long ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Wanpela bilong ol <a %(annas_archive)s>tikets</a> bilong mipela em i wanpela bag bilong ol isyu wantaim sevis sistem bilong mipela. Mipela i yusim MySQL full-text sevis, bikos mipela i gat olgeta data bilong mipela long MySQL. Tasol i gat ol limit:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Sampela kwari i kisim tumas taim, inap long holim olgeta opn koneksen."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Long defolt MySQL i gat minimum wod len, o indeks bilong yu inap kamap bikpela tru. Ol manmeri i ripot olsem ol i no inap sevis long “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Sevis i liklik kwik tasol taim i fulap long memory, we i nidim mipela long kisim wanpela expensiv masin long ranim dispela, na sampela koman long preloadim indeks long statap."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Mipela i no inap long extendim isi long bildim niu features, olsem gutpela <a %(wikipedia_cjk_characters)s>tokenization bilong ol tokples we i no gat spais</a>, filtering/faceting, sorting, \"did you mean\" sujestions, autocomplete, na olsem."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Bihain long toktok wantaim planti eksperts, mipela i setel long ElasticSearch. Em i no bin perpekt (ol defolt “did you mean” sujestions na autocomplete features i no gutpela), tasol olgeta samting em i bin gutpela moa long MySQL long sevis. Mipela i no <a %(youtube)s>tumuch keen</a> long yusim em long ol mission-critical data (maski ol i bin mekim planti <a %(elastic_co)s>progress</a>), tasol olgeta samting mipela i amamas tru long senis."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Nau, mipela i implementim kwikpela sevis, gutpela sapot bilong tokples, gutpela relevancy sorting, narapela sorting options, na filtering long tokples/buk kain/fail kain. Sapos yu kirap nogut long hau em i wok, <a %(annas_archive_l140)s>lukim</a> <a %(annas_archive_l1115)s>wanpela</a> <a %(annas_archive_l1635)s>lukluk</a>. Em i isi long luksave, maski em i nidim sampela moa koments…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ bilong buk kavars i rilis"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Las, mipela i amamas long tokaut long liklik rilis. Long wokbung wantaim ol manmeri husat i opretim Libgen.rs fork, mipela i searim olgeta buk kavars bilong ol long torrents na IPFS. Dispela bai distribuitim lo bilong lukim ol kavars namel long planti masin, na bai lukautim ol gutpela moa. Long planti (tasol nogat olgeta) kes, ol buk kavars i stap insait long ol fail yet, olsem dispela em kain “derived data”. Tasol gatim em long IPFS i yet i yusful tru long deili opresen bilong Anna’s Archive na ol narapela Library Genesis forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Olsem save, yu inap painim dispela rilis long Pirate Library Mirror (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). Mipela i no bai linkim i go long em hia, tasol yu inap isi long painim."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hopfuli mipela inap relax liklik, nau se mipela i gat gutpela alternetiv long Z-Library. Dispela wokload i no tru long stap. Sapos yu intres long helpim wantaim programing, server opresens, o wok bilong lukautim, tru tru reachim mipela. I gat planti <a %(annas_archive)s>wok i stap yet</a>. Tenkyu long intres na sapot bilong yu."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna’s Archive i bin bekapim bikpela comics shadow library long wol (95TB) — yu inap helpim long seedim"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Bikpela comics shadow library long wol i gat wanpela point bilong faila.. inap long tude."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Toktok long Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Bikpela shadow library bilong comics i save olsem bilong wanpela Library Genesis fork: Libgen.li. Wanpela admin husat i ranim dispela sait i bin manisim long bungim wanpela bikpela comics koleksen bilong ova 2 milien fail, i go inap long 95TB. Tasol, narapela long ol narapela Library Genesis koleksen, dispela wan i no stap long bulk long torrents. Yu inap onli aksesim ol dispela comics wan wan long personal server bilong em — wanpela point bilong faila. Inap long tude!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Long dispela post bai mipela tokim yu moa long dispela bung, na long wok bilong mipela long kisim mani bilong sapotim moa dispela wok."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon traim long lusim em yet long ol samting bilong laibreri…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Ol Libgen fork"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Pastaim, sampela bekraun. Yu inap save long Library Genesis long bikpela bung bilong ol buk bilong ol. Tasol liklik lain save olsem ol volentia bilong Library Genesis i wokim ol narapela projek, olsem bikpela bung bilong ol magasin na ol standard dokumen, wanpela ful bekap bilong Sci-Hub (wantaim wok bung bilong faunda bilong Sci-Hub, Alexandra Elbakyan), na tru, wanpela bikpela bung bilong ol komik."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Long wanpela taim ol narapela opareta bilong ol miror bilong Library Genesis i go long narapela rot, na dispela i kamapim dispela situesen nau we i gat planti “fork”, tasol olgeta i karim yet nem Library Genesis. Ol Libgen.li fork i gat dispela bung bilong ol komik, wantaim bikpela bung bilong ol magasin (we mipela tu i wok long en)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Wok Bung Wantaim"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Long luksave long bikpela bilong en, dispela bung i stap long wishlis bilong mipela longpela taim, olsem na bihain long sukses bilong mipela long bekapim Z-Library, mipela i lukluk long dispela bung. Pastaim mipela i scrapim stret, na dispela i bin hatwok, bikos server bilong ol i no stap long gutpela kondisen. Mipela i kisim klostu 15TB long dispela rot, tasol i go isi isi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Lukim, mipela i bin toktok wantaim opareta bilong laibreri, na em i orait long salim olgeta data stret long mipela, na dispela i go kwik. Tasol i kisim moa long hap yia long transfer na prosesim olgeta data, na mipela i klostu lusim olgeta long disk korapsen, we bai i min long statim gen olgeta."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Dispela eksperiens i mekim mipela bilip olsem i bikpela samting long kisim dispela data i go ausait kwik, bai ol narapela i ken mirorim long planti hap. Mipela i stap wan o tu samting nogut long lusim dispela bung oltaim!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Dispela bung"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Muv kwik i min olsem dispela bung i liklik no orait… Lukim. Tingim yumi gat wanpela filesistem (we long tru mipela i brukim long ol torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Fes direktori, <code>/repository</code>, em i moa stretpela hap bilong dispela. Dispela direktori i gat ol “tausen dir”: ol direktori we i gat tausen fael, we i namba long database. Direktori <code>0</code> i gat ol fael wantaim comic_id 0–999, na olsem."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dispela em i sem skem olsem Library Genesis i bin yusim long ol fiksen na non-fiksen bung bilong ol. Tingting em olsem olgeta “tausen dir” i ken tanim kamap torrent taim em i pulap."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tasol, opareta bilong Libgen.li i no bin wokim ol torrent bilong dispela bung, na olsem na ol tausen dir i kamap hatwok, na i kamapim “unsorted dir”. Ol dispela em <code>/comics0</code> i go long <code>/comics4</code>. Olgeta i gat ol unik direktori strukta, we i save mekim sens long bungim ol fael, tasol nau i no mekim tumas sens long mipela. Lukim, metadata i stap yet long ol dispela fael, olsem na stoa oranisisen bilong ol long disk i no tru samting!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata i stap long fomu bilong wanpela MySQL database. Dispela inap daunlod stret long Libgen.li website, tasol mipela bai mekim tu i stap long wanpela torrent, wantaim tebol bilong mipela yet wantaim olgeta MD5 hash."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Taim yu kisim 95TB i pundaun long stoa klasta bilong yu, yu traim long mekim sens long wanem samting i stap long en… Mipela i bin mekim sampela analis long lukim sapos mipela inap rediusim sais liklik, olsem long rausim ol dubliket. Hia sampela bilong ol faindings bilong mipela:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantik dubliket (ol narapela skan bilong sem buk) inap long teori i stap, tasol i hatwok. Taim mipela i lukluk long ol komik, mipela i painim planti false positives."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "I gat sampela dubliket stret long MD5, we i liklik west, tasol rausim ol bai givim mipela klostu 1% in seving. Long dispela skel em i stap klostu 1TB, tasol, long dispela skel 1TB i no tru samting. Mipela i no laik riskim bagarapim data long dispela proses."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Mipela i painim planti non-buk data, olsem ol muvi we i stap long komik buk. Dispela tu i luk olsem west, bikos ol dispela i stap pinis long narapela rot. Tasol, mipela i luksave olsem mipela i no inap rausim ol muvi fael, bikos i gat tu <em>interactive comic books</em> we i bin kamap long kompiuta, we sampela i rikod na sevim olsem muvi."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Las, sapos olgeta samting yumi ken rausim long koleksen bai saveim tasol liklik pesen. Orait, mipela tingim gen olsem mipela ol data hoarders, na ol manmeri husat bai mekim mirroring long dispela tu ol data hoarders, olsem na, “YU MEAN, RAUSIM?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Olsem na mipela i bringim long yu, olgeta, koleksen i no senis. Em i planti data, tasol mipela i ting planti manmeri bai laikim long seedit."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Raisim Mani"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Mipela i putim dispela data long sampela bikpela hap. Namba wan torrent em bilong <code>/comics0</code>, we mipela i putim long wanpela bikpela 12TB .tar file. Dispela i gutpela moa long hard drive na torrent software bilong yu long planti liklik files."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Olsem wanpela hap bilong dispela release, mipela i mekim wanpela raisim mani. Mipela i lukluk long kisim $20,000 bilong karamapim operational na contracting cost bilong dispela koleksen, na tu long sapotim ol wok na projek i kamap bihain. Mipela i gat sampela <em>bikpela</em> wan i stap long wok."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Husat mi sapotim wantaim donesen bilong mi?</em> Long tok hait: mipela i bekapim olgeta save na kalja bilong ol manmeri, na mekim i isi long kisim. Olgeta kod na data bilong mipela i opan sos, mipela i wanpela projek i ran wantaim ol volentia, na mipela i bin seivim 125TB bilong ol buk inap nau (long hap bilong Libgen na Scihub’s i stap pinis long torrents). Las, mipela i wokim wanpela flywheel we i mekim na kirapim ol manmeri long painim, skanim, na bekapim olgeta buk long wol. Mipela bai raitim long master plan bilong mipela long wanpela post bihain. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Sapos yu donetim long 12 mun “Amazing Archivist” membasip ($780), yu ken <strong>“adoptim wanpela torrent”</strong>, mining olsem mipela bai putim username o mesej bilong yu long filename bilong wanpela long ol torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Yu ken donetim long go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a> na klikim “Donet” button. Mipela tu i lukluk long painim moa volentia: software engineers, security researchers, anonymous merchant experts, na transleiters. Yu ken sapotim mipela tu long givim hosting services. Na tru, plis seedim ol torrents bilong mipela!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Tenkyu long olgeta husat i bin sapotim mipela wantaim bikpela bel! Yu tru tru i mekim wanpela difrens."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hia ol torrents mipela i bin release inap nau (mipela i wok long prosesim ol arapela yet):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Olgeta torrents inap long painim long <a %(wikipedia_annas_archive)s>Anna’s Archive</a> aninit long “Datasets” (mipela i no linkim stret long hia, olsem na links long dispela blog i no rausim long Reddit, Twitter, na arapela). Long hia, bihainim link i go long Tor website."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wanem bai kamap bihain?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Planti torrents i gutpela long longpela taim seivim, tasol i no gutpela long olgeta de akses. Mipela bai wok wantaim ol hosting partners long putim olgeta dispela data long web (bikos Anna’s Archive i no hostim samting stret). Tru, yu bai inap long painim ol dispela download links long Anna’s Archive."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Mipela tu i singautim olgeta long mekim samting wantaim dispela data! Helpim mipela long betta analizim, dedupliketim, putim long IPFS, remixim, trenim AI models bilong yu wantaim, na olsem. Em olgeta bilong yu, na mipela i no inap wet long lukim wanem yu mekim wantaim."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Las, olsem mipela i bin tok bipo, mipela i gat sampela bikpela release i kamap yet (sapos <em>wanpela</em> i ken <em>accidentally</em> salim wanpela dump bilong wanpela <em>certain</em> ACS4 database, yu save long we long painim mipela…), na tu long wokim flywheel bilong bekapim olgeta buk long wol."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Olsem na stap isi, mipela i stat yet."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nupela buk i addim long Pirate Library Mirror (+24TB, 3.8 million buk)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Long orijinal release bilong Pirate Library Mirror (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), mipela i wokim wanpela mirror bilong Z-Library, wanpela bikpela illegal buk koleksen. Long tingim, dispela em samting mipela i raitim long dispela orijinal blog post:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library em wanpela popular (na illegal) laibrari. Ol i kisim Library Genesis koleksen na mekim i isi long painim. Antap long dispela, ol i kamap gutpela long askim ol nupela buk kontribusens, long kirapim ol kontributing users wantaim ol kainkain perks. Nau ol i no kontributim ol dispela nupela buk i go bek long Library Genesis. Na narapela long Library Genesis, ol i no mekim koleksen bilong ol i isi long mirroring, we i stopim longpela taim seivim. Dispela i bikpela long bisnis model bilong ol, bikos ol i askim mani long aksesim koleksen bilong ol long bikpela hap (winim 10 buk long wanpela de)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Mipela i no mekim ol moral judgements long askim mani long kisim bikpela akses long wanpela buk koleksen we i no gat lo. I no gat tupela tingting olsem Z-Library i bin mekim gutpela wok long bringim save i go long planti manmeri, na kisim moa ol buk. Mipela i stap hia long mekim wok bilong mipela: lukautim longpela taim bilong dispela praiwet koleksen."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Dispela koleksen i stat long hap 2021. Long dispela taim, Z-Library i bin kamap bikpela tru: ol i bin putim klostu 3.8 milion nupela buk. I gat sampela buk i stap tupela taim, tasol bikpela hap bilong ol i luk olsem ol i nupela buk tru, o gutpela sken bilong ol buk we ol i bin putim bipo. Dispela i bikpela hap i kamap bikos ol i gat planti moa volentia modereita long Z-Library, na sistem bilong ol long putim planti buk wantaim deduplikesen. Mipela i laik tok tenkyu long ol long dispela gutpela wok."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Mipela i amamas long tokaut olsem mipela i bin kisim olgeta buk we ol i bin putim long Z-Library namel long las miror bilong mipela na Ogas 2022. Mipela i bin go bek na kisim sampela buk we mipela i lusim long taim bipo. Olgeta wantaim, dispela nupela koleksen i klostu 24TB, we i bikpela moa long las wan (7TB). Miror bilong mipela nau i 31TB olgeta. Gen, mipela i bin dedupliket long Library Genesis, bikos i gat ol torrents i stap pinis bilong dispela koleksen."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Plis go long Pirate Library Mirror bilong lukim nupela koleksen (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). I gat moa infomesen long hap long olsem wanem ol fail i stap, na wanem samting i senis long taim bipo. Mipela bai no linkim i go long hap, bikos dispela em wanpela blog website tasol na i no hostim ol samting i no gat lo."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Tru, seeding em tu wanpela gutpela rot bilong helpim mipela. Tenkyu long olgeta manmeri husat i seeding ol torrents bilong mipela bipo. Mipela i tenkyu tru long gutpela response, na amamas olsem i gat planti manmeri husat i tingim lukautim save na kalja long dispela narakain rot."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Olsem wanem long kamap wanpela pirate archivist"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Fes challenge inap mekim yu kirap nogut. Em i no wanpela teknikal problem, o wanpela legal problem. Em i wanpela saikolodikal problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Bipo mipela i go insait, tupela apdeit long Pirate Library Mirror (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Mipela i kisim sampela donesen we i bikpela tru. Fes wan em $10k long wanpela anonimus manmeri husat i bin sapotim \"bookwarrior\", orijinal faunda bilong Library Genesis. Tenkyu spesel long bookwarrior bilong helpim dispela donesen. Sekon wan em narapela $10k long wanpela anonimus donor, husat i bin kontakim mipela bihain long las rilisim, na i bin kisim insipiresen long helpim. Mipela i bin kisim sampela liklik donesen tu. Tenkyu tru long olgeta sapot bilong yupela. Mipela i gat sampela nupela projek we i eksaitim mipela long wokim, olsem na stap isi."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Mipela i bin bungim sampela teknikal hevi wantaim saiz bilong sekon rilisim, tasol ol torrents bilong mipela i stap na seeding nau. Mipela i bin kisim wanpela gutpela ofa long wanpela anonimus manmeri bilong seeding koleksen bilong mipela long ol server bilong ol we i gat bikpela spid, olsem na mipela i mekim spesel upload long ol masin bilong ol, bihain long dispela olgeta manmeri husat i daunlodim koleksen bai lukim bikpela senis long spid."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Olgeta buk inap raitim long <em>why</em> bilong digital preservation long olgeta, na pirate archivism long spesel, tasol mipela bai givim kwik primer long ol husat i no tumas familiya. Wol i wokim moa save na kalja long bipo, tasol moa bilong en i lus long bipo. Manmeri i save bilipim ol korporesen olsem akademik pablishers, streaming services, na social media kampani wantaim dispela heritij, na ol i no save lukautim gut. Lukim dokyumentari Digital Amnesia, o rili any tok bilong Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "I gat sampela institusen we i mekim gutpela wok long arkaivim olsem planti samting ol inap, tasol ol i stap aninit long lo. Olsem pirates, mipela i stap long wanpela spesel hap bilong arkaivim ol koleksen we ol i no inap touchim, bikos bilong copyright enforcement o narapela restriksen. Mipela inap tu long mirorim ol koleksen planti taim, long olgeta hap bilong wol, olsem na i go bikpela sans bilong gutpela lukautim."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Nau, mipela bai no go insait long toktok long ol gutpela na nogutpela bilong intelektual property, moraliti bilong brukim lo, tingting long sensa, o askim bilong akses long save na kalja. Wantaim olgeta dispela i stap longwe, mipela i go insait long <em>how</em>. Mipela bai soim olsem wanem tim bilong mipela i kamap pirate archivists, na ol leson we mipela i lainim long rot. I gat planti challenge taim yu stat long dispela wokabaut, na hopfully mipela inap helpim yu long sampela bilong ol."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Komuniti"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Fes challenge inap mekim yu kirap nogut. Em i no wanpela teknikal problem, o wanpela legal problem. Em i wanpela saikolodikal problem: mekim dispela wok long seket inap mekim yu pilim tru olsem yu stap wanpis. Dipen long wanem samting yu planim long mekim, na model bilong threat bilong yu, yu inap mas lukaut gut tru. Long wanpela end bilong spektrum mipela i gat ol manmeri olsem Alexandra Elbakyan*, faunda bilong Sci-Hub, husat i op long ol wok bilong em. Tasol em i stap long bikpela risk bilong arestim sapos em i go long wanpela western kantri long dispela taim, na inap kisim planti yia long kalabus. Em i wanpela risk yu laik kisim? Mipela i stap long narapela end bilong spektrum; lukaut gut long no lusim wanpela mak, na gat strong operational sekuriti."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Olsem i bin tok long HN bilong \"ynno\", Alexandra i no bin laik long ol i save long em: \"Ol server bilong em i bin setim long putim aut ol detel eror mesij long PHP, wantaim ful pat bilong fulting source file, we i stap aninit long direktori /home/<USER>" Olsem na, yusim random usernames long ol kompiuta yu yusim long dispela samting, long kes yu misconfigureim samting."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Dispela sikret, tasol, i kam wantaim wanpela saikolodikal kost. Planti manmeri i laik save long wok bilong ol, na yet yu no inap kisim wanpela kredit long dispela long real laip. Maski ol liklik samting inap kamap challenge, olsem ol pren i askim yu wanem samting yu bin mekim (long wanpela taim \"messing with my NAS / homelab\" i kamap olpela)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dispela em as na em i bikpela samting long painim wanpela komuniti. Yu inap lusim sampela operational sekuriti long tokim sampela tru pren bilong yu, husat yu save yu inap bilipim tru. Maski long dispela taim lukaut gut long no putim wanpela samting long rait, long kes ol i mas tanim ol email bilong ol i go long ol authorities, o sapos ol divais bilong ol i bin kompromais long narapela rot."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Gutpela moa yet em long painim sampela narapela pirates. Sapos ol tru pren bilong yu i intres long joinim yu, gutpela! Sapos nogat, yu inap painim ol narapela online. Sori tasol dispela i stap yet olsem wanpela niche komuniti. I go inap nau mipela i bin painim tasol liklik narapela husat i aktiv long dispela hap. Gutpela stat ples i luk olsem Library Genesis forums, na r/DataHoarder. Archive Team tu i gat ol manmeri husat i tingting olsem, tasol ol i wok aninit long lo (maski long sampela grey areas bilong lo). Ol tradisional \"warez\" na pirating scenes tu i gat ol manmeri husat i tingting long wankain rot."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Mipela redi long kisim tingting long hau long wok bung wantaim na painimaut ol tingting. Yu ken raitim tok long mipela long Twitter o Reddit. Ating mipela inap long holim wanpela kain forum o grup bilong toktok. Wanpela hevi em olsem dispela inap isi long kisim sensa taim mipela yusim ol komon platform, olsem na mipela mas holim em yet. I gat tu wanpela hevi long mekim ol dispela toktok i stap klia long ai bilong olgeta man (bai i gat planti man bai i ken bung wantaim) o mekim em i stap hait (nogat wanpela \"target\" bai i save olsem mipela bai kisim ol samting bilong ol). Mipela bai tingim dispela. Tokim mipela sapos yu gat laik long dispela!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projek"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Taim mipela mekim wanpela projek, em i gat sampela hap:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Mak bilong wok / tingting: We yu laik putim tingting bilong yu, na bilong wanem? Wanem ol pasin, save, na sindaun bilong yu we yu inap yusim long helpim yu yet?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Mak bilong kisim: Wanem kain koleksen yu laik mekim kopi bilong en?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata scraping: Raitim ol save bilong ol fail, tasol nogat daunlodim ol fail yet (olsem ol fail i bikpela tru)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Mak bilong data: Long lukluk long metadata, skelim wanem data i bikpela samting long putim long arkaiv nau. Inap long olgeta samting, tasol planti taim i gat gutpela rot long sevim hap na bandwidth."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Data scraping: Kisim data yet."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribusen: Putim long torrents, tokaut long hap, mekim ol man long helpim long skelim."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ol dispela i no olgeta taim i stap long wanpela hap, na planti taim ol save bilong bihain bai bringim yu i go bek long pastaim. Olsem, long metadata scraping yu inap luksave olsem mak yu bin makim i gat ol defen mekanism we i antap long save bilong yu (olsem IP blok), olsem na yu go bek na painim narapela mak."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Mak bilong wok / tingting"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "I no gat sot long save na kastom bilong ol manmeri long sevim, na dispela inap mekim yu pilim hevi. Olsem na i gutpela long kisim taim liklik na tingim wanem kontribusen yu inap mekim."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Olgeta manmeri i gat narapela rot bilong tingim dispela, tasol hia i gat sampela askim yu inap askim yu yet:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Bilong wanem yu gat laik long dispela? Wanem samting yu laikim tru? Sapos mipela inap kisim planti manmeri husat i arkaivim ol kain samting ol i laikim tru, dispela bai karamapim planti samting! Yu bai save long planti samting moa long ol narapela manmeri long pasin bilong yu, olsem wanem data i bikpela samting long sevim, wanem ol koleksen na komuniti long online i gutpela, na olsem."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Wanem ol save yu gat we yu inap yusim long helpim yu yet? Olsem, sapos yu wanpela save long sekuriti long online, yu inap painim rot bilong daunim ol IP blok bilong ol sekur mak. Sapos yu gutpela long wokim ol komuniti, orait ating yu inap bungim sampela manmeri wantaim long wanpela mak. I gutpela long save liklik long programing tu, maski long sevim gut sekuriti bilong wok long dispela proses."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hamaspela taim yu gat long dispela? Tingting bilong mipela em long stat liklik na mekim ol bikpela projek taim yu kisim save long en, tasol dispela inap kisim olgeta taim bilong yu."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wanem bai i stap olsem wanpela bikpela hap long putim tingting long en? Sapos yu laik putim X aua long arkaivim ol samting bilong ol poret, orait olsem wanem yu inap kisim bikpela \"bang for your buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wanem ol narapela rot yu tingim long dispela? Yu inap gat sampela gutpela tingting o rot we ol narapela i no lukim."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Long hap bilong mipela, mipela i lukautim longpela taim sevim bilong sain. Mipela i save long Library Genesis, na olsem em i bin mekim kopi planti taim tru long yusim torrents. Mipela i laikim dispela tingting. Na wanpela de, wanpela bilong mipela i traim long painim sampela buk bilong sain long Library Genesis, tasol i no inap painim ol, na dispela i bringim long tingting olsem em i no tru olgeta. Mipela i painim ol dispela buk long online, na painim ol long narapela hap, na dispela i planim sid bilong projek bilong mipela. Maski bipo mipela i no save long Z-Library, mipela i gat tingting bilong no traim long kisim olgeta buk long han, tasol long putim tingting long mekim kopi bilong ol koleksen i stap, na putim bek long Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Mak bilong kisim"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Orait, yumi gat ples bilong yumi lukluk long en, nau wanem kain koleksen yumi laik kisim kopi bilong en? I gat sampela samting i mekim gutpela mak bilong en:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Bikpela"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Nupela: i no stap pinis long ol narapela projek."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Isi long kisim: i no gat planti banis bilong banisim yu long kisim metadata na data bilong ol."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Spezel save: yu gat sampela spesel save long dispela mak, olsem yu gat spesel akses long dispela koleksen, o yu save long rot bilong daunim ol banis bilong ol. Dispela i no nidim (projek bilong yumi i no mekim wanpela spesel samting), tasol em i helpim tru!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Taim yumi painim ol buk bilong sainis long ol narapela websait i no Library Genesis, yumi traim long save olsem ol i kamap long intanet olsem wanem. Bihain yumi painim Z-Library, na yumi luksave olsem maski planti buk i no kamap pastaim long dispela hap, ol i save kamap long dispela hap bihain. Yumi lainim long as bilong en wantaim Library Genesis, na (moni) stail bilong insentiv na gutpela yusim pes bilong en, tupela i mekim em i kamap wanpela gutpela koleksen. Bihain yumi mekim sampela wok painimaut long metadata na data, na yumi luksave olsem yumi inap long ranawe long ol IP daunlod limit bilong ol, yusim spesel akses bilong wanpela memba bilong yumi long planti proxy server."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Taim yu wok long painim ol narapela mak, em i bikpela samting pinis long haitim ol mak bilong yu long yusim VPN na ol email adres bilong rausim, bai yumi toktok moa long dispela bihain."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata kisim"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Yumi go liklik moa long teknikal hap nau. Long kisim tru metadata long ol websait, yumi mekim samting i stap isi. Yumi yusim Python script, sampela taim curl, na wanpela MySQL database long putim ol risalt long en. Yumi no yusim wanpela naispela scraping software we inap mapim ol hatpela websait, bikos inap nau yumi nidim tasol long kisim wan o tupela kain pes long yusim id na pasim HTML. Sapos i no gat isi long painim ol pes, orait yu mas yusim wanpela proper crawler we i traim long painim olgeta pes."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Bipo yu stat long kisim olgeta websait, traim long mekim em long han pastaim. Go long sampela ten pes yu yet, long kisim save long olsem wanem dispela i wok. Sampela taim yu bai painim pinis ol IP blok o narapela kain pasin long dispela rot. Dispela i wankain long data kisim: bipo yu go tumas long dispela mak, mekim sua yu inap tru long daunlod data bilong en gut."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Long ranawe long ol banis, i gat sampela samting yu inap traim. I gat narapela IP adres o server we i holim wankain data tasol i no gat wankain banis? I gat ol API endpoint we i no gat banis, taim ol narapela i gat? Long wanem ret bilong daunlod yu IP i blok, na long wanem taim? O yu no blok tasol i daunim ret bilong yu? Sapos yu wokim wanpela yusa akaun, olsem wanem samting i senis? Yu inap yusim HTTP/2 long holim ol koneksen op, na dispela i mekim ret bilong yu long rikwesim ol pes i go antap? I gat ol pes we i listim planti fail wantaim, na ol infomesen i stap long hap i inap?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Ol samting yu laik seivim i save kamap:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Namba"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Failnem / ples"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: inap long wanpela internal ID, tasol ID olsem ISBN o DOI i gutpela tu."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Sais: long kaunim hamas disk spais yu nidim."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): long mekim sua yu daunlodim fail gut."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Deit putim/modifaim: bai yu inap kam bek bihain na daunlodim ol fail yu no daunlod bipo (maski yu save yusim ID o hash long dispela)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Diskripsen, kategoria, tag, autas, tokples, na ol arapela."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Yumi save mekim dispela long tupela step. Pastaim yumi daunlodim ol raw HTML fail, i save go stret long MySQL (long ranawe long planti liklik fail, bai yumi toktok moa long dispela daunbilo). Bihain, long narapela step, yumi go long ol dispela HTML fail na pasim ol long tru MySQL tebol. Dispela rot yu no nidim long daunlodim olgeta samting long stat sapos yu painim wanpela rong long pasim kod bilong yu, bikos yu inap yusim gen ol HTML fail wantaim niupela kod. Em i save isi long mekim wok wantaim long dispela step, olsem yu save sevim sampela taim (na yu inap raitim pasim kod taim scraping i wok, i no nidim long raitim tupela step wantaim)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Las, tingim olsem long sampela mak, metadata scraping em tasol samting i stap. I gat sampela bikpela metadata koleksen i stap ausait we ol i no bin lukautim gut."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Data makim"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Planti taim yu inap yusim metadata long painim wanpela gutpela hap bilong data long daunlod. Maski sapos yu laik daunlod olgeta data, em inap helpim yu long makim ol bikpela samting pastaim, long wanem, yu inap kisim save na ol defences i kamap strong, o long wanem yu bai nidim long baim moa disk, o long wanem narapela samting i kamap long laip bilong yu bipo yu inap daunlod olgeta samting."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Olsem, wanpela koleksen inap gat planti edisen bilong wankain risos (olsem buk o film), we wanpela i makim olsem em i gutpela kwoliti. Seivim ol dispela edisen pastaim bai mekim planti sens. Yu inap bihain laik seivim olgeta edisen, long wanem, long sampela taim metadata inap bin makim rong, o inap gat ol narapela samting we yu no save long en namel long ol edisen (olsem, \"gutpela edisen\" inap gut long planti rot tasol nogut long narapela rot, olsem film i gat bikpela risoluson tasol i no gat subtaitel)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Yu inap tu painimaut long metadata databes bilong yu long painim ol intresing samting. Wanem em bikpela fail we i stap, na bilong wanem em i bikpela tru? Wanem em liklik fail? I gat ol intresing o samting yu no tingim bipo long ol kategori, tokples, na olsem? I gat ol dubliket o truely wankain taitel? I gat ol paten long taim data i bin addim, olsem wanpela de we planti fail i bin addim wantaim? Yu inap planti save long lukluk long dataset long ol narapela rot."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Long kes bilong mipela, mipela i bin dedupliketim ol Z-Library buk agensim md5 hashes long Library Genesis, olsem mipela i seivim planti taim bilong daunlod na disk spais. Dispela em wanpela truely unik situesen tasol. Long planti kes i no gat ol komprehensiv databes bilong wanem fail i bin lukautim gut pinis long ol narapela pirat. Dispela yet em wanpela bikpela opotuniti bilong wanpela man i stap ausait. Em bai gutpela tru long gat wanpela regyularli apdetet ovaview bilong ol samting olsem musik na film we i bin planti seed long torrent websites, na olsem em i loer praioriti long inkludim long pirat miras."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Data scraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nau yu redi long truely daunlodim data long bulk. Olsem mipela i bin tok bipo, long dispela taim yu mas bin daunlodim ol fail long han, long gutpela save long bihavia na restriksen bilong mak. Tasol, bai i gat ol sapraisis i stap long yu taim yu truely daunlodim planti fail wantaim."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Advais bilong mipela long hia em long mekim i simpol. Stat long daunlodim planti fail. Yu inap yusim Python, na bihain expand long planti threads. Tasol sampela taim moa simpol em long jenereit Bash fail stret long databes, na bihain ranim planti bilong ol long planti terminal windo long skel ap. Wanpela kwik teknikal trik i gutpela long tokaut long hia em yusim OUTFILE long MySQL, we yu inap raitim long we yu laik sapos yu diseblim \"secure_file_priv\" long mysqld.cnf (na lukaut tu long diseblim/ovaraidim AppArmor sapos yu stap long Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Mipela i stoa data long simpol had disk. Stat long wanem yu gat, na expand sloli. Em inap ovawelming long tingim long stoa handret bilong TBs bilong data. Sapos dispela em situesen yu stap long en, putim wanpela gutpela hap pastaim, na long anansmen bilong yu askim help long stoaim ol narapela. Sapos yu truely laik kisim moa had draiv bilong yu yet, r/DataHoarder i gat sampela gutpela risos long kisim gutpela dil."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Traim long no wari tumas long fancy filesystems. Em isi long pundaun long rabit hol bilong setim ap ol samting olsem ZFS. Wanpela teknikal ditel long save long en tasol, em planti filesystems i no deal gut wantaim planti fail. Mipela i painimaut olsem wanpela simpol wokaraun em long kirietim planti direktori, e.g. long ol narapela ID renj o hash prefix."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Afta daunlodim data, lukaut long sekim integriti bilong ol fail yusim hashes long metadata, sapos i stap."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribusen"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Yu gat data, olsem yu gat posesen bilong world's first pirat mira bilong mak bilong yu (most likely). Long planti rot, hardest hap i ova, tasol riskiest hap i stap yet long yu. Afta olgeta, inap nau yu bin stap stil; flai ananit long radar. Olgeta samting yu mas mekim em yusim gutpela VPN long olgeta taim, no filim long ol personal ditel long ol fom (duh), na mebi yusim spesel brausa sesen (o even narapela kompiuta)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nau yu mas distribuitim data. Long kes bilong mipela mipela pastaim laik kontribuitim ol buk bek long Library Genesis, tasol kwiktaim mipela i painimaut ol hatwok long dispela (fiksen vs non-fiksen sotim). Olsem mipela i disaid long distribusen yusim Library Genesis-stail torrents. Sapos yu gat opotuniti long kontribuit long wanpela eksisting projek, dispela inap seivim yu planti taim. Tasol, i no gat planti wel-organized pirat miras i stap ausait nau."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Olsem, sapos yu disaid long distribuitim torrents bilong yu yet. Traim long mekim ol dispela fail liklik, olsem ol i isi long mira long ol narapela websites. Yu bai nid long seedim ol torrents bilong yu yet, taim yu stap stil ananim. Yu inap yusim VPN (wantaim o nogat port forwarding), o baim wantaim tumbled Bitcoins bilong wanpela Seedbox. Sapos yu no save long wanem ol dispela tok i min, yu bai gat planti riting long mekim, long wanem em i impotent long save long risk tradeoffs long hia."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Yu inap hostim ol torrent fail yet long eksisting torrent websites. Long kes bilong mipela, mipela i disaid long truely hostim wanpela website, long wanem mipela tu laik spreadim filosofi bilong mipela long klia rot. Yu inap mekim dispela long yu yet long wankain rot (mipela yusim Njalla bilong ol domains na hosting, baim wantaim tumbled Bitcoins), tasol tu pilim fri long kontakim mipela long mipela hostim ol torrents bilong yu. Mipela i lukluk long bildim wanpela komprehensiv indeks bilong pirat miras long taim, sapos dispela idea i kisim."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Long VPN seleksen, planti samting i bin raitim pinis long dispela, olsem mipela bai ripitim general advais bilong makim long reputesen. Akchual kot-testet no-log policies wantaim long trak rekods bilong protektim praiwasi em loest risk opsen, long tingting bilong mipela. Tingim olsem maski yu mekim olgeta samting stret, yu no inap kisim long zero risk. Olsem, taim yu seedim ol torrents bilong yu, wanpela truely motiveted nason-stet akta inap lukluk long inkoming na outgoing data flos bilong VPN servers, na dediusim husat yu. O yu inap truely mekim wanpela rong. Mipela i save pinis, na bai mekim gen. Lukily, nason stet i no kea <em>tumuch</em> long pirasi."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Wanpela disisen long mekim bilong olgeta projek, em long we yu laik publisim yusim wankain identity olsem bipo, o nogat. Sapos yu stap yusim wankain nem, orait ol misteks long operational sekuriti long ol bipo projek inap kam bek long bitim yu. Tasol publisim ananit long narapela nem i min olsem yu no bildim longpela lasing reputesen. Mipela i makim long gat strong operational sekuriti long stat olsem mipela inap stap yusim wankain identity, tasol mipela bai no hestet long publisim ananit long narapela nem sapos mipela mekim rong o sapos ol situesen i kolim long en."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Kisim tokaut inap hatwok. Olsem mipela i bin tok, dispela em yet wanpela niche komuniti. Mipela i bin postim long Reddit, tasol truely kisim traction long Hacker News. Long nau rekomendesen bilong mipela em long postim long sampela ples na lukim wanem i kamap. Na gen, kontakim mipela. Mipela bai laik truely long spreadim tok bilong moa pirat archivism efforts."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Konklusen"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Mipela laik bai dispela i helpim ol nupela ol manmeri husat i laik kamap ol pirate archivists. Mipela i amamas long welkamim yu long dispela ples, olsem na no ken pret long toktok wantaim mipela. Yumi mas traim long lukautim na sevim save na kastom bilong olgeta hap bilong graun, na mekim kopi bilong ol long olgeta hap."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Yumi bringim Pirate Library Mirror: Lukautim 7TB bilong ol buk (ol dispela i no stap long Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dispela projek (EDIT: i muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>) i laik helpim long lukautim na mekim fri save bilong ol manmeri. Mipela i mekim liklik na daunbilo kontribusen bilong mipela, bihainim ol bikmanmeri i bin kamap pastaim."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "As bilong dispela projek i stap long nem bilong en:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirate</strong> - Mipela i save brukim lo bilong copyright long planti kantri. Dispela i larim mipela long mekim samting ol legal entiti i no inap mekim: mekim sua ol buk i stap long olgeta hap."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Library</strong> - Olsem planti library, mipela i save lukluk moa long ol raiten samting olsem ol buk. Mipela inap expand long ol narapela kain media long bihain."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Mipela i stretim olsem wanpela mirror bilong ol library i stap pinis. Mipela i lukluk long lukautim, i no long mekim ol buk i isi long painim na daunlod (akses) o long wokim bikpela komuniti bilong ol manmeri husat i kontribiutim ol nupela buk (sourcing)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Fas library mipela i bin mekim kopi bilong em Z-Library. Dispela em i wanpela library i save kamap bikpela (na i no legal). Ol i bin kisim Library Genesis koleksen na mekim isi long painim. Antap long dispela, ol i bin kamap gut long askim ol nupela kontribusen bilong buk, long givim ol gutpela samting long ol manmeri husat i kontribiut. Ol i no save givim bek ol dispela nupela buk long Library Genesis. Na olsem Library Genesis, ol i no mekim isi long mekim kopi bilong koleksen bilong ol, we i stopim bikpela lukautim. Dispela i bikpela samting long bisnis model bilong ol, bikos ol i save askim mani long aksesim koleksen bilong ol long bikpela (winim 10 buk long wanpela de)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Mipela i no mekim ol moral judgements long askim mani long kisim bikpela akses long wanpela buk koleksen we i no gat lo. I no gat tupela tingting olsem Z-Library i bin mekim gutpela wok long bringim save i go long planti manmeri, na kisim moa ol buk. Mipela i stap hia long mekim wok bilong mipela: lukautim longpela taim bilong dispela praiwet koleksen."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Mipela i laik invaitim yu long helpim long lukautim na mekim fri save bilong ol manmeri long daunlod na seedim ol torrents bilong mipela. Lukim projek peij bilong kisim moa infomesen long olsem wanem ol data i stap."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Mipela i laik tru invaitim yu long kontribiutim ol tingting bilong yu long wanem koleksen long mekim kopi long bihain, na olsem wanem long mekim. Wantaim mipela inap mekim planti samting. Dispela em i liklik kontribusen namel long planti narapela. Tenkyu, long olgeta samting yu mekim."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Mipela i no linkim ol fail long dispela blog. Plis painim yu yet.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, o Olsem Wanem Planti Buk I Stap Oltaim?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Sapos mipela i stretim gut ol fail bilong shadow libraries, wanem pesen bilong olgeta buk long graun mipela i bin lukautim?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Wantaim Pirate Library Mirror (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), aim bilong mipela em long kisim olgeta buk long graun, na lukautim ol oltaim.<sup>1</sup> Namel long ol Z-Library torrents bilong mipela, na ol orijinal Library Genesis torrents, mipela i gat 11,783,153 fail. Tasol em i hamas tru? Sapos mipela i stretim gut ol dispela fail, wanem pesen bilong olgeta buk long graun mipela i bin lukautim? Mipela i laik tru long gat samting olsem dispela:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% o bilong raiten kastom bilong ol manmeri i stap oltaim"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Long kisim pesen, mipela i nidim wanpela denominator: namba bilong olgeta buk i bin kamap long bipo.<sup>2</sup> Pastaim long Google Books i pinis, wanpela injinia long projek, Leonid Taycher, <a %(booksearch_blogspot)s>trai long makim</a> dispela namba. Em i kamap — wantaim jok — wantaim 129,864,880 (“long Sunday tasol”). Em i makim dispela namba long wokim wanpela unifaid database bilong olgeta buk long graun. Long dispela, em i bungim ol narapela datasets na bihain em i joinim ol long narapela rot."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Olsem wan liklik toktok, i gat narapela man husat i traim long raitim olgeta buk long graun: Aaron Swartz, dispela man i bin wok long digital na co-founder bilong Reddit. Em i <a %(youtube)s>statim Open Library</a> wantaim as bilong “wanpela web peij bilong olgeta buk i bin kamap”, na em i bungim ol data long planti narapela hap. Em i bin kisim bikpela penaltim long wok bilong em long digital presave, taim ol i kotim em long daunlodim planti academic pepa, na dispela i lidim long dai bilong em. I no nid long tok, dispela em wanpela as bilong wanem grup bilong mipela i yusim narapela nem, na wanem mipela i wok long lukaut gut. Open Library i stap yet na ol manmeri long Internet Archive i wok long ranim, na ol i wok long karim wok bilong Aaron. Mipela bai toktok moa long dispela bihain long dispela post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Long Google blog post, Taycher i tokaut long sampela hevi bilong traim long makim dispela namba. Pastaim, wanem samting i makim buk? I gat sampela definsin:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Ol physical kopi.</strong> Klia olsem dispela i no helpim tumas, bikos ol i stap olsem ol kopi bilong wankain samting. Bai i gutpela sapos mipela inap presave olgeta rait ol manmeri i mekim insait long ol buk, olsem Fermat i bin raitim “ol liklik rait long sait bilong peij”. Tasol sori, dispela bai stap olsem driman bilong ol man i save long presave."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Ol wok”.</strong> Olsem “Harry Potter and the Chamber of Secrets” olsem wanpela tingting, i bungim olgeta vesin bilong en, olsem ol narapela translesen na reprint. Dispela kain definsin i gat liklik helpim, tasol i hat long makim wanem samting i kaun. Olsem, mipela laik presave ol narapela translesen, tasol ol reprint wantaim liklik difrens i no bikpela samting."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Ol edisen”.</strong> Long hia yu kaunim olgeta narapela vesin bilong wanpela buk. Sapos wanpela samting long en i narapela, olsem narapela kavara o narapela preface, em i kaun olsem narapela edisen."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Ol fail.</strong> Taim yu wok wantaim ol shadow library olsem Library Genesis, Sci-Hub, o Z-Library, i gat narapela samting yu mas tingim. I gat planti scan bilong wankain edisen. Na ol manmeri inap mekim gutpela vesin bilong ol fail i stap, long scanim teks yusim OCR, o stretim ol peij i bin scan long wanpela sait. Mipela laik kaunim ol dispela fail olsem wanpela edisen tasol, na dispela i nidim gutpela metadata, o yusim ol mekim gutpela mekim long dokumen similarity."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Ol edisen” i luk olsem gutpela definsin bilong wanem “buk” i stap. Gutpela samting, dispela definsin i yusim tu long givim narapela ISBN namba. ISBN, o International Standard Book Number, i save yusim long intanesenel bisnis, bikos em i bung wantaim intanesenel barcode sistem (”International Article Number”). Sapos yu laik salim wanpela buk long stoa, em i nidim barcode, olsem na yu kisim ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher i tok long blog post olsem ISBN i gat helpim, tasol i no universal, bikos ol i bin yusim long mid-seventies tasol, na i no long olgeta hap bilong graun. Tasol, ISBN i save yusim long planti hap bilong graun, olsem na em i gutpela statim point bilong mipela. Sapos mipela inap painim olgeta ISBN long graun, mipela i gat gutpela list bilong ol buk i mas presave yet."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Olsem na, long wanem hap mipela bai kisim ol data? I gat planti wok i stap long traim long bungim list bilong olgeta buk long graun:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Bihainim, ol i bin mekim dispela risas long Google Books. Tasol, metadata bilong ol i no stap long bulk na i hat long scrapim."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Olsem mipela i bin tok bipo, dispela em as bilong ol. Ol i bin kisim planti data bilong library long ol library i wok wantaim na national archive, na ol i wok long mekim olsem yet. Ol i gat volunteer librarian na wanpela teknikal tim i wok long mekim gut ol rekod, na tagim ol wantaim ol kain metadata. Gutpela samting, dataset bilong ol i open olgeta. Yu ken <a %(openlibrary)s>daunlodim</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dispela em wanpela website i ran bilong non-profit OCLC, husat i salim ol library management sistem. Ol i bungim metadata bilong buk long planti library, na mekim em i stap long WorldCat website. Tasol, ol i mekim mani long salim dispela data, olsem na em i no stap long bulk daunlod. Ol i gat sampela liklik bulk dataset i stap long daunlod, long wok wantaim ol spesifik library."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dispela em topik bilong dispela blog post. ISBNdb i scrapim ol narapela website bilong metadata bilong buk, long spesifik prais data, na ol i salim long ol bookseller, olsem na ol inap praisim ol buk bilong ol wantaim maket. Olsem na, ISBN i save yusim long planti hap nau, ol i wokim “web peij bilong olgeta buk”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Ol narapela individual library sistem na archive.</strong> I gat ol library na archive i no bin indeks na bungim long ol i stap antap, ofen bikos ol i no gat planti mani, o long narapela as ol i no laik sharim data bilong ol wantaim Open Library, OCLC, Google, na olsem. Planti bilong ol i gat digital rekod i stap long internet, na ol i ofen no gutpela long protektim, olsem na sapos yu laik helpim na gat liklik amamas long lainim long ol narapela library sistem, ol dispela em gutpela statim point."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Long dispela post, mipela i amamas long tokaut long wanpela liklik rilisim (kompea long ol bipo Z-Library rilisim bilong mipela). Mipela i scrapim bikpela hap bilong ISBNdb, na mekim data i stap long torrenting long website bilong Pirate Library Mirror (EDIT: muv i go long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>; mipela bai no linkim em hia stret, tasol yu ken painim). Ol dispela i gat klostu 30.9 milion rekod (20GB olsem <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). Long website bilong ol, ol i tokaut olsem ol i gat 32.6 milion rekod, olsem na mipela inap lusim sampela, o <em>ol</em> inap mekim wanpela rong. Long wanem kain, nau mipela bai no sharim stret olsem mipela i mekim — mipela bai lusim olsem wanpela eksasais bilong ridim. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wanpela samting mipela bai sharim em sampela prilimineri analisis, long traim long kamap klostu long makim namba bilong buk long graun. Mipela i lukluk long tripela dataset: dispela niu ISBNdb dataset, orijinal rilisim bilong metadata mipela i scrapim long Z-Library shadow library (em i bungim Library Genesis), na Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Mipela stat wantaim sampela ruf namba:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Long Z-Library/Libgen na Open Library i gat planti moa buk long ol unik ISBN. Dispela i makim olsem planti bilong ol buk i no gat ISBN, o em ISBN metadata i no stap? Mipela inap ansaim dispela askim wantaim kombinason bilong automated matching long ol narapela atribut (title, author, publisher, etc), pulim moa data sos, na kisim ISBN long ol buk scan yet (long Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Olsem wanem planti bilong ol ISBN i unik? Dispela i gutpela long soim wantaim Venn diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Long tok klia moa:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Mipela kirap nogut tru long lukim olsem i no gat planti samting i bung wantaim! ISBNdb i gat planti ISBN we i no stap long Z-Library o Open Library, na dispela i tru tu (maski i liklik tasol) long tupela arapela. Dispela i kirapim planti askim. Olsem wanem bai ol wok bilong masin i helpim long makim ol buk we i no gat ISBN? Bai i gat planti samting i bung wantaim na olsem na i kamap planti samting i bung wantaim? Na tu, bai i olsem wanem sapos mipela bringim 4-pela o 5-pela dataset? Bai mipela lukim planti samting i bung wantaim long dispela taim?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dispela i givim mipela wanpela stat bilong wok. Nau mipela inap lukim olgeta ISBN we i no stap long dataset bilong Z-Library, na we i no bung wantaim title/author fields tu. Dispela inap helpim mipela long lukautim olgeta buk long graun: pastaim long painim ol scan long internet, na bihain long go aut long real life long scanim ol buk. Dispela laspela samting inap kamap long crowd-funded, o long ol “bounties” bilong ol manmeri husat i laik lukim sampela buk i kamap long digital. Olgeta dispela i wanpela stori bilong narapela taim."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Sapos yu laik helpim long wanpela bilong dispela — moa anisis; painim moa metadata; painim moa buk; OCR’ing bilong ol buk; mekim dispela long ol narapela hap (olsem pepa, audiobooks, movies, tv shows, magazines) o mekim sampela bilong dispela data i stap long ol samting olsem ML / large language model training — plis kontakim mi (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Sapos yu gat spesel intres long data anisis, mipela i wok long mekim ol datasets na scripts bilong mipela i stap long wanpela isi long yusim format. Bai i gutpela sapos yu inap tasim wanpela notebook na stat long pilai wantaim dispela."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Las, sapos yu laik sapotim dispela wok, plis tingim long mekim wanpela donesen. Dispela i wanpela wok bilong ol volunteer tasol, na kontribusen bilong yu i mekim bikpela senis. Olgeta liklik samting i helpim. Nau mipela kisim donesen long crypto; lukim Donate page long Anna’s Archive."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Long sampela gutpela defisen bilong \"oltaim\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Tru, raiten heritij bilong manmeri i planti moa long ol buk, speseli nau. Long dispela post na ol nupela rilisim bilong mipela mipela i fokus long ol buk, tasol intres bilong mipela i go moa."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. I gat planti moa samting inap tok long Aaron Swartz, tasol mipela laik mentionim em liklik tasol, bikos em i gat bikpela hap long dispela stori. Taim i go, planti moa manmeri inap bungim nem bilong em long namba wan taim, na bihain inap go daun long rabbit hole bilong ol yet."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Kritikal windo bilong ol shadow libraries"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Olsem wanem mipela inap tok long lukautim ol koleksen bilong mipela oltaim, taim ol i klostu long 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Sina tokples 中文版</a>, toktok long <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Long Anna’s Archive, ol i save askim mipela olsem wanem mipela inap tok long lukautim ol koleksen bilong mipela oltaim, taim olgeta saiz i klostu long 1 Petabyte (1000 TB), na i wok long kamap bikpela moa. Long dispela atikol mipela bai lukim filosofi bilong mipela, na lukim wanem as na nekis deked i kritikal long misin bilong mipela bilong lukautim save na kalja bilong manmeri."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Olgeta saiz</a> bilong ol koleksen bilong mipela, long ol mun i go pinis, brukim daun long namba bilong torrent seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Ol praioriti"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Olsem wanem mipela i tingting planti long ol pepa na buk? Larim mipela putim long sait bilip bilong mipela long lukautim long olgeta samting — mipela inap raitim narapela post long dispela. Olsem wanem pepa na buk spesifik? Ansa i isi: <strong>densiti bilong save</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Long wanpela megabyte bilong stoa, raiten teks i stoa planti save moa long olgeta media. Taim mipela i tingim save na kalja, mipela i tingim moa long pastaim. Ovarol, mipela i lukim wanpela hierarki bilong densiti bilong save na bikpela bilong lukautim olsem:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademik pepa, journals, ripot"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organic data olsem DNA sequences, plant seeds, o microbial samples"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Non-fiksen buk"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Sains na injiniring software kod"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Ol save long makim olsem save long sait bilong sains, save long ekonomi, ripot bilong kampani"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sains na injiniring websait, toktok long online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Ol magasin bilong non-fiksen, niuspepa, maniwal"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Ol toktok bilong non-fiksen, dokyumentari, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Ol data insait long kampani o gavman (ol leaks)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Ol metadata rekod long olgeta (bilong non-fiksen na fiksen; bilong narapela media, art, pipol, na ol narapela; wantaim ol riviu)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Ol data bilong geografi (olsem map, ol geological survey)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Ol toktok bilong lo o kot"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Ol fiksen o entetainment vesin bilong olgeta samting antap"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Ol ranking long dispela list i liklik nating — sampela samting i stap long sem ples o i gat ol tok pait insait long tim bilong mipela — na mipela i save lusim tingting long sampela impoten kategori. Tasol dispela em olsem mipela i save makim."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Sampela bilong ol dispela samting i narapela kain tru long ol arapela na mipela i no wari long ol (o ol arapela institiusen i lukautim pinis), olsem ol organic data o ol geographic data. Tasol bikpela hap bilong ol samting long dispela list i tru tru impoten long mipela."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Narapela bikpela samting long makim em olsem wanem wanpela wok i stap long risk. Mipela i laik luksave long ol wok we:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rar"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniquely i no luksave long"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniquely i stap long risk bilong bagarap (olsem long pait, katim mani, kot, o politikal bagarap)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Las, mipela i tingim skel. Mipela i gat liklik taim na mani, olsem na mipela i laik spendim wan mun long seivim 10,000 buk long 1,000 buk — sapos ol i gat wankain value na risk."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Sado laibrari"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "I gat planti organisasen we i gat wankain misin, na wankain prioriti. Tru, i gat ol laibrari, arkaiv, lab, myusium, na ol arapela institiusen we i lukautim kain samting olsem. Planti bilong ol i gat planti mani, long gavman, indibidual, o kampani. Tasol ol i gat wanpela bikpela blind spot: lo sistem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Long dispela ples i stap unik rol bilong sado laibrari, na as bilong Anna’s Archive i stap. Mipela inap mekim ol samting we ol arapela institiusen i no inap mekim. Nau, em i no (olsem) mipela inap arkaivim ol matirial we i no legal long lukautim long narapela hap. Nogat, em i legal long planti ples long wokim arkaiv wantaim ol buk, pepa, magasin, na olsem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Tasol wanem ol lo bilong ol buk em i no gat planti kopi na i no stap longpela taim. I gat ol buk we wanpela kopi tasol i stap long wanpela laibreri longwe. I gat ol metadata rekod we wanpela kampani tasol i lukautim. I gat ol niuspepa we i stap long microfilm tasol long wanpela arkaiv. Ol laibreri inap kisim kat long mani, ol kampani inap go long bengkrap, ol arkaiv inap kisim bom na paia i kukim olgeta. Dispela i no tingting nating — dispela i kamap oltaim."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Wanpela samting yumi inap mekim long Arkaiv bilong Anna em long putim planti kopi bilong ol wok, long bikpela skel. Yumi inap bungim ol pepa, buk, magasin, na moa yet, na salim ol long bikpela namba. Nau yumi mekim dispela long torrents, tasol ol teknologi i no bikpela samting na bai senis long taim i kamap. Samting i bikpela em long kisim planti kopi i go long olgeta hap bilong graun. Dispela tok bilong 200 yia i go pinis i stap tru yet:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ol samting i lus i no inap kam bek; tasol yumi mas lukautim ol samting i stap yet: i no long putim ol long ples hait na lokim ol we ol manmeri i no inap lukim na yusim, long putim ol long hap we taim i bagarapim, tasol long mekim planti kopi, olsem bai ol i stap longwe long bagarap.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Wanpela liklik tok long public domain. Olsem Arkaiv bilong Anna i luksave long ol wok we i no legal long planti hap bilong graun, yumi no wari long ol koleksen we i stap long public domain. Ol legal entiti i save lukautim gut dispela. Tasol, i gat sampela tingting we mekim yumi wok long ol koleksen we i stap long public domain:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Ol metadata rekod inap lukim fri long Worldcat website, tasol i no inap daunlodim long bikpela namba (bipo yumi <a %(worldcat_scrape)s>scraped</a> ol)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kode inap stap open source long Github, tasol Github yet i no inap isi long mirorim na lukautim (tasol long dispela hap i gat planti kopi bilong planti kode repositori)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit i fri long yusim, tasol i bin putim ol strongpela lo long anti-scraping, bihain long data-hungry LLM training (moa long dispela bihain)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Planti kopi"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Bek long askim bilong yumi: olsem wanem yumi inap tok yumi lukautim ol koleksen bilong yumi longpela taim? Main problem hia em olsem koleksen bilong yumi i bin <a %(torrents_stats)s>gro</a> kwiktaim, long scraping na open-sourcing sampela bikpela koleksen (antap long wok bilong ol arapela open-data shadow laibreri olsem Sci-Hub na Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Dispela gro long data i mekim i hat long ol koleksen i go long olgeta hap bilong graun. Data storage i kostim planti mani! Tasol yumi luksave long dispela, moa yet taim yumi lukim tripela trend."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Yumi bin kisim ol isi samting"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dispela i kamap stret long ol praioriti yumi bin toktok long en antap. Yumi laik wok long mekim fri ol bikpela koleksen pastaim. Nau yumi bin lukautim sampela bilong ol bikpela koleksen long graun, yumi tingting gro bilong yumi bai isi isi."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "I gat yet longpela teol bilong ol liklik koleksen, na ol nupela buk i save skanim o putim long olgeta de, tasol reit bai isi isi. Yumi inap yet mekim tupela o tripela taim bikpela, tasol longpela taim."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Kost bilong storage i go daun kwiktaim"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Long taim bilong raitim, <a %(diskprices)s>disk prices</a> long TB i stap long $12 bilong nupela disk, $8 bilong yusim disk, na $4 bilong teip. Sapos yumi lukim nupela disk tasol, dispela i min olsem long storim wanpela petabyte i kostim $12,000. Sapos yumi tingting laibreri bilong yumi bai tripela taim long 900TB i go long 2.7PB, dispela bai min $32,400 long mirorim olgeta laibreri bilong yumi. Putim long en elektrisiti, kost bilong arapela hardware, na moa yet, yumi raunim i go long $40,000. O long teip olsem $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Long wanpela han <strong>$15,000–$40,000 bilong olgeta save bilong man i olsem stil</strong>. Long narapela han, em i liklik hat long tingting planti kopi, moa yet sapos yumi laik ol dispela manmeri i mas stap long seeding bilong ol torrents bilong ol arapela."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dispela em nau. Tasol progres i wokabaut i go:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Kost bilong hard drive long TB i bin go daun long tripela hap long 10 yia i go pinis, na bai go daun long wankain pasin. Teip i luk olsem i go long wankain rot. SSD prices i go daun kwiktaim moa yet, na inap kisim ples bilong HDD prices long pinis bilong deked."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD price trends long ol narapela hap (klik long lukim stadi)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Sapos dispela i stap, orait long 10 yia yumi inap lukim tasol $5,000–$13,000 long mirorim olgeta koleksen bilong yumi (1/3rd), o liklik moa sapos yumi no gro long bikpela. Na em inap gutpela moa long dispela nekis point…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Olsem wanem long ol save bilong ol buk"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Nau, mipela i stap putim ol buk long ol raw format olsem ol i givim long mipela. Tru, ol i kompres, tasol planti taim ol i stap yet olsem bikpela skan o foto bilong ol pes."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "I no longtaim, ol opsen bilong mekim liklikpela saiz bilong ol buk bilong mipela em i bin kamap long strongpela kompres, o deduplikesen. Tasol, bilong kisim bikpela seving, tupela i save lusim tumas samting bilong mipela. Strongpela kompres bilong ol foto inap mekim teks i no klia. Na deduplikesen i nidim bikpela bilip olsem ol buk i wankain stret, we planti taim i no tru, sapos ol konten i wankain tasol ol skan i bin mekim long narapela taim."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "I gat wanpela namba tri opsen, tasol kwoliti bilong en i bin nogut tru olsem mipela i no bin tingim: <strong>OCR, o Optical Character Recognition</strong>. Dispela em i proses bilong tanim ol foto i go long plain teks, long yusim AI bilong luksave long ol leta long ol foto. Ol tul bilong dispela i bin stap longpela taim, na i bin orait liklik, tasol “orait liklik” i no inap bilong presave."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tasol, ol nupela multi-modal dip-lening model i bin mekim bikpela wokabaut kwik, maski i stap yet long bikpela kost. Mipela i ting bai kwiknes na kost i go daun bikpela long ol yia i kamap, inap long mekim i realist long yusim long olgeta laibreri bilong mipela."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Olsem wanem OCR i go bikpela."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Taim dispela i kamap, mipela bai save presave yet ol orijinal fail, tasol long addisen mipela inap gat wanpela liklikpela vesin bilong laibreri bilong mipela we planti manmeri bai laik miror. Dispela em i bikpela samting olsem raw teks yet i kompres gutpela moa, na i isi moa long dedupliket, givim mipela moa seving."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Olgeta samting i no noken tingting olsem i no inap long tingim olsem 5-10x redaksen long olgeta fail saiz, inap moa yet. Maski long wanpela konserve 5x redaksen, mipela bai lukluk long <strong>$1,000–$3,000 long 10 yia maski laibreri bilong mipela i tripel long saiz</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritikal windo"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Sapos ol dispela forkes i tru, mipela <strong>just nidim long wetim sampela yia</strong> bipo olgeta koleksen bilong mipela bai i stap long planti miror. Olsem, long tok bilong Thomas Jefferson, “putim i stap longwe long han bilong aksiden.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Sori tru, kamap bilong LLM, na ol data-hungry trening bilong ol, i bin mekim planti kopiraith holim i go long difensiv. Moa yet long ol i bin stap pinis. Planti websait i mekim i hat long skrep na arkaiv, ol kot i flai raun, na olgeta taim ol fisikal laibreri na arkaiv i go long nogut."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Mipela inap tingim ol dispela tren bai go long nogut moa, na planti wok bai lus bipo ol i go long pablik domen."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Mipela i stap long bipo bilong revolusen long presave, tasol <q>ol lus i no inap kisim bek.</q></strong> Mipela i gat wanpela kritikal windo bilong 5-10 yia we i yet i kostim planti long opretim wanpela sado laibreri na mekim planti miror long olgeta hap bilong graun, na we akses i no bin klos yet."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Sapos mipela inap brukim dispela windo, orait mipela bai presave tru save na kalja bilong manmeri long oltaim. Mipela i no ken larim dispela taim i go nating. Mipela i no ken larim dispela kritikal windo i klos long mipela."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Lukim mipela i go."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Eksklusiv akses bilong LLM kampani long bikpela koleksen bilong ol buk bilong China long graun"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Vesin bilong China 中文版</a>, <a %(news_ycombinator)s>Toktok long Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Arkaiv bilong Anna i kisim wanpela unik koleksen bilong 7.5 milion / 350TB buk bilong China — bikpela moa long Library Genesis. Mipela i redi long givim wanpela LLM kampani eksklusiv akses, long senis bilong kwoliti OCR na teks ekstraksen.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dispela em i wanpela sotpela blog post. Mipela i lukluk long sampela kampani o institusen long helpim mipela wantaim OCR na teks ekstraksen bilong bikpela koleksen mipela i kisim, long senis bilong eksklusiv eli akses. Bihain long embargo taim, mipela bai tru tru rilisim olgeta koleksen."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Olsem wanem, ol buk bilong skul i gat gutpela save i helpim tru long trenim ol LLM. Maski sapos ol buk bilong mipela i stap long Tok Saina, dispela bai helpim yet long trenim ol LLM bilong Tok Inglis: ol model i save kisim save na save long ol tingting maski long wanem tokples."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Long dispela, teks i mas kisim long ol sken. Wanem samting Anna’s Archive i kisim long dispela? Ful teks sevis bilong ol buk bilong ol yusa bilong en."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Long wanem mipela i gat wankain tingting wantaim ol LLM divelopa, mipela i lukautim wanpela wantok. Mipela i redi long givim yu <strong>eksklusiv akses long dispela koleksen long bikpela hap long 1 yia</strong>, sapos yu inap mekim gutpela OCR na teks kisim. Sapos yu redi long searem olgeta kod bilong yu wantaim mipela, mipela bai redi long tambuim koleksen longpela taim moa."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Eksampel peij"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Long soim mipela olsem yu gat gutpela lain, hia sampela eksampel peij bilong statim, long wanpela buk long superconductors. Lain bilong yu i mas lukautim gut ol matimatiks, tebol, chat, futnot, na olsem."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Salim ol peij yu bin wokim long email bilong mipela. Sapos ol i luk gut, mipela bai salim moa long yu long praivet, na mipela i ting yu inap long ranim kwik lain bilong yu long ol dispela tu. Taim mipela i amamas, mipela inap mekim wanpela dil."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Koleksen"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Sampela moa infomesen long koleksen. <a %(duxiu)s>Duxiu</a> em i wanpela bikpela databes bilong ol sken buk, we <a %(chaoxing)s>SuperStar Digital Library Group</a> i wokim. Planti bilong ol em i buk bilong skul, sken bilong mekim ol i stap long digital long ol yunivesiti na laibreri. Long ol manmeri i save Tok Inglis, <a %(library_princeton)s>Princeton</a> na <a %(guides_lib_uw)s>University of Washington</a> i gat gutpela luksave. I gat wanpela gutpela artikol tu i givim moa baksait: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (lukim long Anna’s Archive)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Ol buk bilong Duxiu i bin longtaim pinis i stap long ol sait bilong Tok Saina. Olsem tasol, ol i save salim long liklik mani tasol long ol resela. Ol i save yusim Tok Saina olsem Google Drive, we ol i save hakim long mekim i gat planti hap. Sampela teknikal detel i stap <a %(github_duty_machine)s>hia</a> na <a %(github_821_github_io)s>hia</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Maski ol buk i bin stap long ol sait, em i hatwok long kisim ol long bikpela hap. Mipela i putim dispela long bikpela hap bilong wok bilong mipela, na mipela i wokim planti mun long dispela. Tasol, i no longtaim, wanpela gutpela, na talenta volentia i kam long mipela, tokim mipela olsem ol i bin wokim olgeta dispela wok pinis — long bikpela kost. Ol i searem olgeta koleksen wantaim mipela, nating tingim wanpela samting long bek, tasol long mekim sua longpela taim presavesen. Em i tru tru gutpela. Ol i orait long askim help long dispela rot long kisim koleksen OCR'ed."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Koleksen i gat 7,543,702 fail. Dispela i moa long Library Genesis non-fiksen (olsem 5.3 milion). Total fail saiz i olsem 359TB (326TiB) long nau."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Mipela i redi long ol narapela proposol na tingting. Tasol kontakim mipela. Lukim Anna’s Archive long moa infomesen long ol koleksen bilong mipela, presavesen wok, na olsem wanem yu inap helpim. Tenkyu!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Waning: dispela blog post i bin lusim. Mipela i bin makim olsem IPFS i no redi yet long bikpela taim. Mipela bai linkim yet ol fail long IPFS long Anna’s Archive sapos inap, tasol mipela bai no hostim yet, na mipela i no rekomendim ol narapela long miror yusim IPFS. Lukim peij bilong mipela long Torrents sapos yu laik helpim presavim koleksen bilong mipela."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Helpim long sidim Z-Library long IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Olsem wanem long ranim wanpela laibreri long sait: operesen long Anna’s Archive"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "I no gat <q>AWS bilong sait charities,</q> olsem wanem mipela i ranim Anna’s Archive?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Mi ranim <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, bikpela open-sos non-profit sevis bilong painim ol buk long <a %(wikipedia_shadow_library)s>sait laibreri</a>, olsem Sci-Hub, Library Genesis, na Z-Library. Aim bilong mipela em long mekim save na kalja i stap isi, na long las, long wokim wanpela komuniti bilong ol manmeri husat i bung wantaim long presavim <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>olgeta buk long wol</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Long dispela artikol bai mi soim olsem wanem mipela i ranim dispela website, na ol narapela halivim i kam wantaim long operetim wanpela website wantaim kwesten legal status, bikos i no gat “AWS bilong sait charities”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Lukim tu artikol bilong wantok <a %(blog_how_to_become_a_pirate_archivist)s>Olsem wanem long kamap wanpela pirate archivist</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Taim bilong senis"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Yumi kirap wantaim ol samting bilong teknologi bilong yumi. Em i no samting bilong kirap nogut. Yumi yusim Flask, MariaDB, na ElasticSearch. Em tasol. Painim samting i stap pinis, na yumi no laik mekim gen. Na tu, yumi mas yusim ol <a %(mcfunley)s>taim bilong senis</a> bilong yumi long narapela samting: no ken kisim daun long ol gavman."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Olsem wanem, Anna’s Archive i legal o i no legal stret? Dispela i dipen long lo bilong kantri. Planti kantri i bilip long sampela kain copyright, we ol manmeri o kampani i kisim wanpela eksklusiv monopoli long sampela kain wok bilong wanpela taim. Long sait bilong Anna’s Archive, yumi bilip olsem i gat sampela gutpela samting, tasol long olgeta, copyright i no gutpela long sosaiti — tasol dispela i narapela stori long narapela taim."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dispela eksklusiv monopoli long sampela wok i makim olsem i no legal long ol manmeri ausait long dispela monopoli long distribiutim ol dispela wok — inkluding yumi. Tasol Anna’s Archive em i wanpela searj enjin we i no distribiutim ol dispela wok stret (long lebel bilong yumi klia net website), olsem na yumi orait, a? Nogat stret. Long planti ples, i no legal tasol long distribiutim ol copyright wok, tasol tu long linkim long ol ples we i mekim olsem. Wanpela eksampel bilong dispela em lo bilong DMCA bilong Amerika."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dispela em i strongpela sait bilong lo. Long narapela sait, i gat kantri we i no gat copyright lo, tasol ol dispela i no stap tru. Klostu olgeta kantri i gat sampela kain copyright lo. Enfosemen em narapela stori. I gat planti kantri we gavman i no wari long enfose copyright lo. I gat tu ol kantri namel long tupela strongpela sait, we i tambu long distribiutim ol copyright wok, tasol i no tambu long linkim long ol dispela wok."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Narapela samting bilong tingim em long level bilong kampani. Sapos wanpela kampani i stap long ples we i no wari long copyright, tasol kampani yet i no laik kisim ris, orait ol inap pasim website bilong yu taim wanpela i tokaut long en."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Las, wanpela bikpela samting bilong tingim em ol peimen. Sapos yumi mas stap aninimus, yumi no inap yusim ol tradisional peimen metod. Dispela i larim yumi wantaim kripto, na liklik namba bilong kampani i sapotim ol dispela (i gat ol virtual debit kad we kripto i peim, tasol ol i no save kisim)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistem aritektua"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Olsem na sapos yu painim sampela kampani we i redi long hostim website bilong yu wantaimaut long pasim yu — yumi kolim ol dispela “fri-liking provaiders” 😄. Yu bai kwiktaim painimaut olsem hostim olgeta samting wantaim ol dispela i kostim bikpela, olsem na yu laik painim sampela “sip provaiders” na mekim hostim tru long hap, proxying long ol fri-liking provaiders. Sapos yu mekim stret, ol sip provaiders bai no save long wanem yu hostim, na bai no kisim ol tokaut."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Wantaim olgeta dispela provaiders i gat risk bilong ol pasim yu, olsem na yu nidim redandansi. Yumi nidim dispela long olgeta level bilong stack bilong yumi."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Wanpela kampani we i likim fri liklik na i putim em yet long wanpela intresing posisen em Cloudflare. Ol i <a %(blog_cloudflare)s>tokaut</a> olsem ol i no wanpela hosting provaiders, tasol wanpela utility, olsem ISP. Ol i no stap aninit long DMCA o narapela takedown rikwes, na ol i forwadim ol rikwes long tru hosting provaiders bilong yu. Ol i go inap long kot long protektim dispela strakta. Yumi inap yusim ol olsem narapela leya bilong caching na proteksen."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare i no kisim aninimus peimen, olsem na yumi inap yusim tasol fri plan bilong ol. Dispela i makim olsem yumi no inap yusim ol load balancing o failover features bilong ol. Yumi <a %(annas_archive_l255)s>implimentim dispela yumi yet</a> long level bilong doman. Long taim bilong page load, browser bai sekim sapos doman i stap yet, na sapos nogat, em i raitim gen olgeta URLs long narapela doman. Sapos Cloudflare i cacheim planti pages, dispela i makim olsem wanpela yusa inap land long main doman bilong yumi, maski sapos proxy server i daun, na long nekis klik bai muv i go long narapela doman."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Yumi yet i gat ol normal operesenal konsens long deal wantaim, olsem monitaring server helt, logging backend na frontend erors, na olsem. Failover aritektua bilong yumi i larim moa strong long dispela sait tu, olsem long ranim wanpela narapela set bilong servers long wanpela bilong ol domans. Yumi inap ranim olpela vesens bilong kod na datasets long dispela sapret doman, long taim wanpela kritikal bug long main vesens i no luksave."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Yumi inap tu hedj agens Cloudflare i tanim bek long yumi, long rimuving em long wanpela bilong ol domans, olsem dispela sapret doman. Ol narapela permutations bilong ol dispela ideas i posibol."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ol tul"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Lukim ol tul yumi yusim long mekim olgeta dispela. Dispela i save senis taim yumi bungim ol niupela problem na painim ol niupela solusens."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Aplication server: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy server: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Server manesmen: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Divelopmen: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion static hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "I gat sampela disisen we mipela i bin go bek na kam bek long en. Wanpela em long toktok namel long ol server: mipela i bin yusim Wireguard bipo, tasol mipela i painim aut olsem em i save stopim data long go, o em i save salim data long wanpela hap tasol. Dispela i bin kamap wantaim planti kain Wireguard setup we mipela i traim, olsem <a %(github_costela_wesher)s>wesher</a> na <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Mipela i traim tu long yusim SSH long tunneling ol port, yusim autossh na sshuttle, tasol mipela i bungim <a %(github_sshuttle)s>ol hevi long hia</a> (maski mi no klia yet sapos autossh i gat hevi wantaim TCP-over-TCP o nogat — em i luk olsem wanpela janky solution long mi tasol ating em i orait?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Nau, mipela i go bek long stret koneksen namel long ol server, haitim olsem wanpela server i wok long ol isi provider yusim IP-filtering wantaim UFW. Dispela i gat wanpela hevi olsem Docker i no wok gut wantaim UFW, sapos yu no yusim <code>network_mode: \"host\"</code>. Olgeta dispela i liklik moa hevi, bikos yu bai putim server bilong yu long internet wantaim liklik rong long konfigurim. Ating mipela i mas go bek long autossh — mipela i nidim ol tingting bilong yu long hia."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Mipela i bin go bek na kam bek long Varnish vs. Nginx. Nau mipela i laikim Varnish, tasol em i gat ol liklik hevi na rough edges. Olsem tasol wantaim Checkmk: mipela i no laikim tumas, tasol em i wok nau. Weblate i orait tasol i no nambawan — mi save pret olsem em bai lusim data bilong mi taim mi traim long syncim wantaim git repo bilong mipela. Flask i bin gut olgeta, tasol em i gat sampela longlong hevi we i bin kisim planti taim long painim aut, olsem konfigurim custom domains, o ol hevi wantaim SqlAlchemy integration bilong en."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "I stap nau ol narapela tul i bin gut: mipela i no gat ol bikpela tok baksait long MariaDB, ElasticSearch, Gitlab, Zulip, Docker, na Tor. Olgeta dispela i bin gat sampela hevi, tasol i no bikpela tumas o kisim planti taim."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Konklusen"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Em i bin wanpela intresing experience long lainim olsem wanem long setim wanpela strong na stap gut shadow library search engine. I gat planti moa samting long tokaut long ol post bihain, olsem na tokim mi wanem samting yu laik lainim moa long en!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Oltaim, mipela i lukautim ol donesen long sapotim dispela wok, olsem na lukluk long Donate page long Anna’s Archive. Mipela i lukautim tu ol narapela kain sapot, olsem grants, long-term sponsors, high-risk payment providers, ating tu (olsem tasol!) ads. Na sapos yu laik givim taim na skill bilong yu, mipela i lukautim ol developer, translator, na olsem. Tenkyu long intres na sapot bilong yu."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna na tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hi, mi Anna. Mi bin wokim <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, bikpela tru shadow library long graun. Dispela em personal blog bilong mi, we mi na ol tim bilong mi i raitim long piracy, digital preservation, na moa yet."

#, fuzzy
msgid "blog.index.text2"
msgstr "Joinim mi long <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Noken lus tingting olsem dispela website em wanpela blog tasol. Mipela i hostim tasol ol tok bilong mipela long hia. Nogat ol torrents o narapela copyrighted files i stap o link long hia."

#, fuzzy
msgid "blog.index.heading"
msgstr "Ol blog post"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Putim 5,998,794 buk long IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Waning: dispela blog post i bin lusim. Mipela i bin makim olsem IPFS i no redi yet long bikpela taim. Mipela bai linkim yet ol fail long IPFS long Anna’s Archive sapos inap, tasol mipela bai no hostim yet, na mipela i no rekomendim ol narapela long miror yusim IPFS. Lukim peij bilong mipela long Torrents sapos yu laik helpim presavim koleksen bilong mipela."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archive i bin scrape olgeta WorldCat (bikpela tru library metadata collection long graun) long wokim wanpela TODO list bilong ol buk we i nid long stap gut.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Wanpela yia i go pinis, mipela <a %(blog)s>statim</a> long askim dispela askim: <strong>Wanem pesen bilong ol buk i bin stap gut long ol shadow libraries?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Taim wanpela buk i kam insait long open-data shadow library olsem <a %(wikipedia_library_genesis)s>Library Genesis</a>, na nau <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, em i save stap long olgeta hap bilong graun (long ol torrents), olsem na em i stap gut inap oltaim."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Long bekim askim bilong wanem pesen bilong ol buk i stap gut, mipela i nid long save long denominator: hamas buk i stap olgeta? Na ideal mipela i no gat namba tasol, tasol tru metadata. Orait mipela i ken no tasol matchim ol wantaim shadow libraries, tasol <strong>wokim wanpela TODO list bilong ol buk i stap yet long stap gut!</strong> Mipela i ken stat long drim long wanpela crowdsourced effort long go daun long dispela TODO list."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Mipela kisim ol infomesen long <a %(wikipedia_isbndb_com)s>ISBNdb</a>, na daunlodim <a %(openlibrary)s>Open Library dataset</a>, tasol ol risalts i no gutpela. As tingting bilong dispela hevi em i no planti ISBN i bung wantaim. Lukim dispela Venn diagram long <a %(blog)s>blog post bilong mipela</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Mipela i kirap nogut tru long lukim liklik bung bilong ISBNdb na Open Library, tupela i save kisim data long planti hap, olsem web scrapes na library records. Sapos tupela i wok gut long painim planti ISBN, ol kru bilong tupela bai gat bikpela bung, o wanpela bai stap insait long narapela. Em i mekim mipela tingting, hamas buk i stap <em>austait tru long ol dispela kru</em>? Mipela nidim bikpela database."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Em taim mipela i lukluk long bikpela buk database long wol: <a %(wikipedia_worldcat)s>WorldCat</a>. Dispela em i wanpela database bilong non-profit <a %(wikipedia_oclc)s>OCLC</a>, we i bungim metadata records long ol library long olgeta hap bilong wol, long senis bilong givim ol dispela library akses long olgeta dataset, na mekim ol i kamap long ol end-users’ search risalts."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Maski OCLC em i non-profit, bisnis model bilong ol i nidim lukautim database bilong ol. Orait, mipela sori long tok, ol pren bilong OCLC, mipela bai givim olgeta samting. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Long yia i go pinis, mipela i kisim olgeta WorldCat records. Pastaim, mipela i lukim wanpela gutpela sans. WorldCat i wok long senisim olgeta website bilong ol (long Aug 2022). Dispela i gat bikpela senis long backend systems bilong ol, na i bringim planti security flaws. Mipela i kisim dispela sans kwiktaim, na mipela i save kisim handret milien (!) records long liklik de tasol."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Bihain long dispela, ol security flaws i wok long stretim wan wan, inap laspela wan mipela i painim i bin stretim long wanpela mun i go pinis. Long dispela taim mipela i gat prai bilong olgeta records, na mipela i wok long kisim liklik moa gutpela records. Olsem na mipela i pilim em taim bilong rilisim!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Lukim sampela basik infomesen long data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Anna’s Archive Containers (AAC)</a>, we em i olsem <a %(jsonlines)s>JSON Lines</a> i kompresim wantaim <a %(zstd)s>Zstandard</a>, wantaim sampela standardised semantics. Ol dispela containers i bungim ol kain kain records, long ol narapela scrapes mipela i bin yusim."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Wanpela unknown error i kamap. Plis kontakim mipela long %(email)s wantaim wanpela screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Dispela coin i gat bikpela minimum. Plis makim narapela taim o narapela coin."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Request i no inap long pinis. Plis traem gen long sampela minit, na sapos i go yet, kontakim mipela long %(email)s wantaim wanpela screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Error long payment processing. Plis wet liklik na traem gen. Sapos dispela hevi i go moa long 24 aua, plis kontakim mipela long %(email)s wantaim wanpela screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "hait koment"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Fail isiu: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Gutpela vesin"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Yu laik ripotim dispela yusa long bikosim o inapropiet pasin?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Ripotim bikosim"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Bikosim i ripotim:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Yu ripotim dispela yusa long bikosim."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Bekim"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Plis <a %(a_login)s>log in</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Yu bin larim wanpela koments. Em bai kisim liklik taim bilong soim."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Sampela samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s afekted pej"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "I no lukim long Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "I no lukim long Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "I no lukim long Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Makim bruk long Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "I no stap long Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Makim olsem “spam” long Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Makim olsem “nogut file” long Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nogat olgeta pej i save konvet long PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Raning exiftool i pundaun long dispela fail"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Buk (i no save)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Buk (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Buk (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Jurnal atikol"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standa dokumen"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Magasin"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Komik buk"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Musikal skoa"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Buk bilong harim"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Arakain"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Patna Sava daunlod"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Eksenal daunlod"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Eksenal boro"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Eksenal boro (print disebul)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Lukluk long metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "I stap long torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Saina"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads long AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Indeks"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Czech metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russian State Library"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Title"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Author"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Publisher"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edition"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Year published"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Original filename"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Description na metadata comments"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partner Server i no inap long daunlod dispela fail nau."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Fast Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(irit)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(no browser verification or waitlists)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Slow Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(slightly faster but with waitlist)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(no waitlist, but can be very slow)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(tu klikim “GET” antap)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klikim “GET” antap)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "ol ads bilong ol i save gat nogut software, olsem na yusim ad blocker o no ken klikim ads"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Fail bilong Nexus/STC inap no gut long daunlod)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library long Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(igat nid long Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Borom long Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(ol manmeri i no inap prinim tasol)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI i no inap stap long Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "koleksen"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Bulk torrent daunlods"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(ol eksperts tasol)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Sosim ISBN long Anna’s Archive"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Painim long ol narapela database bilong ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Painim oraitinal rekod long ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Painim long Anna’s Archive bilong Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Painim oraitinal rekod long Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Painim long Anna’s Archive bilong OCLC (WorldCat) namba"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Painim oraitinal rekod long WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Painim long Anna’s Archive bilong DuXiu SSID namba"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Painim long han long DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Painim long Anna’s Archive bilong CADAL SSNO namba"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Painim oraitinal rekod long CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Painim long Anna’s Archive bilong DuXiu DXID namba"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Indeks"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(nogat browser verifikesen i nidim)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Czech metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "description"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Narapla filename"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternative title"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternative author"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternative publisher"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternative edition"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Narapla extension"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata comments"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Narap bilong narapla"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "deit em i op long ol manmeri"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dispela em i rekod bilong wanpela file long Internet Archive, nogat direct download file. Yu inap traem long borou buk (link i stap daunbilo), o yusim dispela URL taim yu <a %(a_request)s>requestim wanpela file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Sapos yu gat dispela file na em i no yet stap long Anna’s Archive, tingim long <a %(a_request)s>uploadim</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata rekod"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata rekod"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) namba %(id)s metadata rekod"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata rekod"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dispela em i wan metadata record, i no wan file yu inap daunlodim. Yu inap yusim dispela URL taim yu <a %(a_request)s>askim long wanpela file</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Olsem wanem long ol save bilong buk i stap long narapela rekod"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Betenim ol save bilong buk long Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Woning: planti rekod i stap wantaim:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Impruvim metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Ripotim kwoliti bilong fail"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Taim bilong daunlod"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Website:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Painim Anna’s Archive long “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Codes Explorer:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Lukim long Codes Explorer “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Ridim moa…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Daunlods (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Borom (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Lukluk long metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Koments (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "List (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statis (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Teknikal detels"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Dispela file i gat sampela hevi, na i bin haitim long wanpela source library.</span> Sampela taim dispela i kamap long askim bilong copyright holim, sampela taim em i bikos i gat gutpela narapela file, tasol sampela taim em i bikos i gat hevi wantaim file yet. Em inap yet long daunlod, tasol mipela i rikumendim yu painim gutpela narapela file pastaim. Moa detels:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Gutpela veson bilong dispela file inap stap long %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Sapos yu yet laik daunlodim dispela file, yu mas yusim trusted, updated software tasol long opim."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Kwik daunlod"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Kwik daunlods</strong> Kamap wanpela <a %(a_membership)s>memba</a> long sapotim longpela taim presave bilong buk, pepa, na moa yet. Long soim tenkyu bilong mipela long sapot bilong yu, yu kisim kwik daunlods. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Sapos yu givim donesen long dispela mun, yu bai kisim <strong>tupla taim</strong> namba bilong kwik daunlod."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Kwikta daunlod</strong> Yu gat %(remaining)s i stap tude. Tenkyu long yu olsem memba! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Kwikta daunlod</strong> Yu no gat moa kwikta daunlod long tude."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Kwikta daunlod</strong> Yu daunlodim dispela file bipo. Ol link bai stap gut liklik taim yet."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opsen #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nogat redirect)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(opin long viua)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Tokim wanpela pren, na tupela bai kisim %(percentage)s%% bonus kwikta daunlod!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Lainim moa…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Slop daunlod"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Long ol traste partner."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Moainfo long <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(ating nidim <a %(a_browser)s>browser verifikesen</a> — anlimitet daunlod!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Bihain long daunlod:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Opim long viua bilong mipela"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "soim ol ekstanal daunlod"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Ekstanal daunlod"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nogat daunlod i painim."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Ol opsen bilong daunlod i gat wankain file, na i orait long yusim. Tasol, yu mas lukaut taim yu daunlodim ol file long internet, speseli long ol sait ausait long Anna’s Archive. Eksampel, yu mas meksua ol divais bilong yu i apdet."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Sapos longpela fail, mipela i tingim yu long yusim wanpela daunlod manija bilong banisim bagarap."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Ol daunlod manija mipela i tingim: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Yu bai nidim wanpela ebook o PDF rida bilong opim fail, long lukim long wanem kain format bilong fail."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Ol ebook rida mipela i tingim: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Viua bilong Anna’s Archive long intanet"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Yusim ol online tul bilong tanim namel long ol format."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Ol tul bilong tanim mipela i tingim: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Yu inap salim tupela PDF na EPUB fail igo long Kindle o Kobo eReader bilong yu."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ol tul mipela i tingim: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon i gat “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz i gat “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Sapota ol raita na ol laibreri"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Sapos yu laikim dispela na yu inap long baim, tingim long baim orijinal, o sapotim ol raita stret."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Sapos dispela i stap long laibreri bilong yu, traim long borouim bilong fri long hap."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Kwoliti bilong fail"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Halivim komyuniti long ripotim kwoliti bilong dispela fail! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Ripotim hevi bilong fail (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Gutpela kwoliti bilong fail (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Putim koment (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Wanem i rong wantaim dispela fail?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Plis yusim <a %(a_copyright)s>DMCA / Copyright claim form</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Skruim hevi (i mas)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Skru bilong hevi"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 bilong gutpela vesin bilong dispela fail (sapos i stap)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Putim dispela sapos i gat narapela fail i klostu long dispela fail (wankain edisen, wankain fail ekstensen sapos yu inap painim), we ol manmeri i ken yusim instet bilong dispela fail. Sapos yu save long gutpela vesin bilong dispela fail ausait long Anna’s Archive, orait plis <a %(a_upload)s>aplodim</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Yu inap kisim md5 long URL, eg."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Sabitim ripot"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lanem how long yu <a %(a_metadata)s>improveim metadata</a> bilong dispela file yu yet."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Tenkyu long putim ripot bilong yu. Bai mipela soim long dispela peij, na tu, Anna bai lukim gut (tete mipela i no gat gutpela sistem bilong lukautim)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Sampela samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Sapos dispela file i gat gutpela quality, yu inap toktok long en hia! Sapos nogat, plis yusim “Report file issue” button."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Mi laikim tru dispela buk!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Larim koments"

#, fuzzy
msgid "common.english_only"
msgstr "Text i stap daun i go long Inglis."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Wanpela “file MD5” em wanpela hash we i kamap long file contents, na em i save stap narakain long dispela content. Olgeta shadow libraries we mipela i putim long hia i save yusim MD5s bilong makim ol files."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Wanpela file inap stap long planti shadow libraries. Long save long ol narakain datasets we mipela i bungim, lukim <a %(a_datasets)s>Datasets peij</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dispela em wanpela file we <a %(a_ia)s>IA’s Controlled Digital Lending</a> library i lukautim, na Anna’s Archive i putim long searchem. Long save long ol narakain datasets we mipela i bungim, lukim <a %(a_datasets)s>Datasets peij</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Long save long dispela file stret, lukim <a %(a_href)s>JSON file</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 I gat wanpela hevi long loadim dispela peij"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Plis refresh na traim gen. <a %(a_contact)s>Kontakim mipela</a> sapos dispela hevi i stap long planti aua."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Nogat"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” i no stap long data beis bilong mipela."

#, fuzzy
msgid "page.login.title"
msgstr "Login / Reta"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Brousa verifikesen"

#, fuzzy
msgid "page.login.text1"
msgstr "Long stopim spam-bots long wokim planti akaun, mipela mas verifaim brousa bilong yu pastaim."

#, fuzzy
msgid "page.login.text2"
msgstr "Sapos yu kisim wanpela loop i no pinis, mipela i rekomendim yu long instalim <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Em inap helpim tu long ofim ad blokas na arapela brousa ekstensen."

#, fuzzy
msgid "page.codes.title"
msgstr "Kod"

#, fuzzy
msgid "page.codes.heading"
msgstr "Eksplora bilong Kods"

#, fuzzy
msgid "page.codes.intro"
msgstr "Lukim ol kod we ol rekod i gat, long prefix. Kolom “rekod” i soim namba bilong ol rekod we i gat kod wantaim dispela prefix, olsem yu lukim long search engine (inkludim ol metadata tasol rekod). Kolom “kod” i soim hamas tru kod i gat dispela prefix."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Dispela peij inap kisim liklik taim long kamap, olsem na em i nidim Cloudflare captcha. <a %(a_donate)s>Ol memba</a> inap lusim captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Plis no scrapim ol dispela peij. Bihainim mipela i rekomendim <a %(a_import)s>jenereitim</a> o <a %(a_download)s>daunlodim</a> ol ElasticSearch na MariaDB databesis bilong mipela, na ranim <a %(a_software)s>open source kod</a> bilong mipela. Ol row data inap long yusim han long eksplore long ol JSON fael olsem <a %(a_json_file)s>dispela wan</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefix"

#, fuzzy
msgid "common.form.go"
msgstr "Go"

#, fuzzy
msgid "common.form.reset"
msgstr "Reset"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Painim Anna’s Archive"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Warning: code i gat rong Unicode mak, na bai i no wok gut long sampela hap. Raw binary inap long painim long base64 representation insait long URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Known code prefix “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefix"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Description"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL bilong wanpela code"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” bai senis wantaim code’s value"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generic URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s records i matchim “%(prefix_label)s”"
msgstr[1] ""

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL bilong wanpela code: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mo…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes i stat wantaim “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks bilong"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "records"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codes"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Fewer than %(count)s records"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Long DMCA / kopiraet kleim, yusim <a %(a_copyright)s>dispela fom</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Ol arapela rot bilong kontakim mipela long kopiraet kleim bai i dilitim wantaim."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Mipela i welkamim tru tingting na askim bilong yu!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tasol, long wanem mipela i kisim planti spam na rabis email, plis lukim bokis na konfirmim yu save long ol dispela kondisen bilong kontakim mipela."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Kopiraet kleim long dispela email bai i no luksave; yusim fom."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Ol i partner server i no stap long wanem ol hosting i pas. Ol bai i kamap gen kwiktaim."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Ol membership bai i go longpela taim olsem."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "No emailim mipela long <a %(a_request)s>rikwestim buk</a><br>o liklik (<10k) <a %(a_upload)s>aplods</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Taim yu askim long akaun o donesen kwesten, putim akaun ID, skrinshot, risit, olgeta infomesen yu inap. Mipela i lukim email bilong mipela olgeta 1-2 wik, olsem nogat dispela infomesen bai i delayim solusen."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Soim email"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Copyright claim form"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Sapos yu gat wanpela DCMA o narapela copyright klos, plis pulimapim dispela fomu stret tru. Sapos yu bungim sampela hevi, plis kontakim mipela long dispela DMCA adres: %(email)s. Noken salim klos long dispela adres, em i stap bilong askim askim tasol. Plis yusim fomu aninit bilong salim klos bilong yu."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs long Anna’s Archive (i mas). Wanpela long wanlain. Plis putim URLs bilong buk we i wankain edisen tasol. Sapos yu laik mekim klos bilong planti buk o planti edisen, plis salim dispela fomu planti taim."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Klos we i bungim planti buk o edisen wantaim bai mipela i rausim."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Nem bilong yu (i mas)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adres (i mas)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Namba bilong fon (i mas)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (i mas)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Klinpela tok bilong as bilong buk (i mas)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs bilong as bilong buk (sapos i gat). Wanpela long wanlain. Plis putim ol ISBNs we i stret wantaim edisen yu ripotim copyright klos."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs bilong as bilong buk, wanpela long wanlain. Plis kisim taim bilong painim as bilong buk long Open Library. Dispela bai helpim mipela long luksave long klos bilong yu."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs bilong as bilong buk, wanpela long wanlain (i mas). Plis putim planti sapos inap, bilong helpim mipela long luksave long klos bilong yu (e.g. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Tok na sain (i mas)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Salim klos"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Tenkyu bilong salim copyright klos bilong yu. Mipela bai lukluk long en kwiktaim. Plis reloadim peij bilong salim narapela."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Samting i go rong. Plis reloadim peij na traim gen."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Sapos yu laik mirroring long dispela dataset bilong <a %(a_archival)s>archival</a> o <a %(a_llm)s>LLM training</a>, plis kontakim mipela."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Mipela i gat wanpela mission bilong archiveim olgeta buk long dispela world (na tu ol papers, magazines, etc), na mekim ol i stap long olgeta hap. Mipela bilip olsem olgeta buk i mas stap long planti hap, bilong mekim seif na strong. Dispela em as na mipela i bungim ol files long narakain hap. Sampela hap i open olgeta na yu inap mirroring long bulk (olsem Sci-Hub). Narapela i pas na lukautim, olsem na mipela i traim scrapeim bilong “liberate” ol buk bilong ol. Narapela i stap namel."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Olgeta data bilong mipela yu inap <a %(a_torrents)s>torrentim</a>, na olgeta metadata yu inap <a %(a_anna_software)s>generatem</a> o <a %(a_elasticsearch)s>downloadim</a> olsem ElasticSearch na MariaDB databases. Raw data yu inap lukluk long en long han bilong JSON files olsem <a %(a_dbrecord)s>dispela</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Overview"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Aninit em wanpela kwik overview bilong ol sources bilong ol files long Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sos"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Saiz"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% i stap long AA / torrents i stap"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentages bilong namba bilong files"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Las deit"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction na Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s files"
msgstr[1] ""

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: i stap kol long 2021; planti i stap long torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: liklik nupela samting i kamap bihain</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Nogat “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Ol tokples stori i stap bihain (tasol ol ID ~4-6M i no stap long torrent bikos ol i kamap wantaim ol Zlib torrents bilong mipela)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Ol “Chinese” buk long Z-Library i luk olsem ol i wankain wantaim DuXiu buk bilong mipela, tasol ol i gat narapela kain MD5. Mipela i no putim ol dispela fael long torrents bilong mipela bilong abrusim tupela i wankain, tasol mipela i soim ol yet long sevis bilong mipela."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrolim Digital Lending"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ bilong ol fael i save long painim."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Nogat tupela i wankain"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Bikos ol shadow libraries i save kisim data long narapela, i gat planti wankain long ol libraries. Dispela as na namba i no inap kamap long total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Dispela “mirrored and seeded by Anna’s Archive” pesentis i soim hamas fael mipela i save putim yet. Mipela i putim ol dispela fael long torrents, na mekim ol i redi long daunlod long ol partner websites."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Ol buk libraries"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Sampela buk naispela laibrari i save putim ol data bilong ol long torrent, tasol sampela narapela i no save redi long putim ol buk bilong ol. Long dispela hap, Anna’s Archive i traim long kisim ol buk bilong ol na mekim ol i stap (lukim <a %(a_torrents)s>Torrents</a> peij bilong mipela). I gat tu ol narapela kain situesen, olsem, sampela laibrari i laik putim ol buk, tasol ol i no gat ol risos long mekim olsem. Long dispela hap, mipela tu i traim long helpim ol."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Aninit i stap wanpela lukluk long hau mipela i wok wantaim ol narapela laibrari."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sos"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Ol Fael"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Olgeta de <a %(dbdumps)s>HTTP database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Olgeta automated torrents bilong <a %(nonfiction)s>Non-Fiction</a> na <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(covers)s>buk kava torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub i no inap putim nupela fail stat long 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata dumps i stap <a %(scihub1)s>long hia</a> na <a %(scihub2)s>long hia</a>, na tu olsem hap bilong <a %(libgenli)s>Libgen.li database</a> (yumi yusim)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Ol data torrent i stap <a %(scihub1)s>hia</a>, <a %(scihub2)s>hia</a>, na <a %(libgenli)s>hia</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Sampela nupela file i <a %(libgenrs)s>kamap</a> <a %(libgenli)s>insait</a> long “scimag” bilong Libgen, tasol i no inap long mekim nupela torrent."

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kwaterli <a %(dbdumps)s>HTTP database dump</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiction torrents i stap wantaim Libgen.rs (na i stap long <a %(libgenli)s>long hia</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna’s Archive na Libgen.li i wok bung wantaim long lukautim ol koleksen bilong <a %(comics)s>komik buk</a>, <a %(magazines)s>makasin</a>, <a %(standarts)s>stana dokumen</a>, na <a %(fiction)s>fiksen (i stap longwe long Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Ol “fiction_rus” koleksen bilong ol (Rasen fiksen) i no gat ol torrent bilong em yet, tasol ol narapela i gat ol torrent bilong em, na mipela i holim wanpela <a %(fiction_rus)s>miror</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna’s Archive na Z-Library i wok bung wantaim long lukautim wanpela bung bilong <a %(metadata)s>Z-Library metadata</a> na <a %(files)s>Z-Library files</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Sampela metadata i stap long <a %(openlib)s>Open Library database dumps</a>, tasol ol dispela i no karamapim olgeta IA bung"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nogat isi rot bilong kisim metadata dumps bilong olgeta bung"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Ol files i stap long borom long liklik hap, wantaim ol kain kain pasin bilong banisim akses"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(ia)s>IA files</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Ol narapela metadata databases i stap long China internet; tasol planti taim ol i baim"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s I no gat isi metadata dump i stap bilong olgeta koleksen bilong ol."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’s Archive i manesim wanpela koleksen bilong <a %(duxiu)s>DuXiu metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Ol kain kain file databases i stap long ol hap bilong Chinese internet; tasol planti taim ol i peid databases"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Planti files i save stap tasol long premium BaiduYun accounts; daunlodim i go isi isi."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(duxiu)s>DuXiu files</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Ol narapela liklik o wanpela taim ol samting. Mipela kirapim ol manmeri long putim ol samting long ol narapela laibrari bilong draipela pastaim, tasol sampela taim ol manmeri i gat ol bung i bikpela tumas long ol narapela long lukluk long, tasol i no bikpela inap long kisim wanpela kategori bilong ol yet."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Ol sos bilong metadata tasol"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Mipela tu i save putim ol metadata tasol long koleksen bilong mipela, we mipela inap painim ol fael, olsem long yusim ISBN namba o narapela filed. Aninit i stap wanpela lukluk bilong ol dispela. Gen, sampela bilong ol dispela sos i op, tasol sampela narapela mipela i mas kisim ol."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Inspiration bilong yumi long bungim metadata em Aaron Swartz’ tingting bilong “wanpela web peij bilong olgeta buk i bin kamap”, em i bin wokim <a %(a_openlib)s>Open Library</a>. Dispela projek i wok gut, tasol yumi gat wanpela spesel hap long kisim metadata ol i no inap. Narapela inspiration em laik bilong yumi long save <a %(a_blog)s>hamas buk i stap long wol</a>, bai yumi inap kaunim hamas buk i stap yet long seivim."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Noken lus tingim long metadata sejen, mipela i soim ol orijinal rekod. Mipela i no mekim wanpela join bilong ol rekod."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Las taim mipela bin apdeitim"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Olgeta mun <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nogat long bulk, banisim long scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Wanpela database"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Mipela i joinim olgeta sos antap long wanpela database we mipela i yusim long sapotim dispela website. Dispela wanpela database i no stap redi, tasol bikos Anna’s Archive i op so, em inap isi <a %(a_generated)s>mekim</a> o <a %(a_downloaded)s>daunlod</a> olsem ElasticSearch na MariaDB database. Ol skrip long dispela peij bai daunlod olgeta metadata long ol sos mipela i tokaut antap."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Sapos yu laik lukluk long data bilong mipela bipo yu ranim ol skrip long ples bilong yu, yu ken lukim ol JSON fael bilong mipela, we i link i go long narapela JSON fael. <a %(a_json)s>Dispela fael</a> em i gutpela stat poin."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adapted from our <a %(a_href)s>blog post</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> em i bikpela database bilong skanim ol buk, wokim bilong <a %(superstar_link)s>SuperStar Digital Library Group</a>. Planti bilong ol em academic buk, skanim bilong mekim ol i stap long digital long ol university na library. Long ol manmeri husat i toktok Inglis, <a %(princeton_link)s>Princeton</a> na <a %(uw_link)s>University of Washington</a> i gat gutpela overview. I gat tu wanpela gutpela artikol i givim moa background: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Ol buk bilong Duxiu i bin longtaim pinis i stap long pirated long Chinese internet. Olsem tasol, ol i save salim ol long liklik mani tasol, wanpela dollar o aninit long dispela. Ol i save yusim Chinese equivalent bilong Google Drive, we i save bin hakim bilong mekim moa storage space. Sampela technical details yu ken painim <a %(link1)s>hia</a> na <a %(link2)s>hia</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Maski ol buk i bin semi-publicly distributed, em i hatwok long kisim ol long bulk. Mipela i bin putim dispela long high long TODO-list, na mipela i bin wokim planti mun bilong full-time wok long en. Tasol, long late 2023 wanpela volunteer i kamap, em i bin wokim olgeta dispela wok pinis — long bikpela cost. Em i bin shareim full collection wantaim mipela, nating expectim wanpela samting long return, tasol long guarantee bilong long-term preservation. Truely remarkable."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resources"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Olgeta files: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Olgeta filesize: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Ol files i stap long Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Las taim i update: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents bilong Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Eksampel rekod long Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Blog post bilong mipela long dispela data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripts bilong importim metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers format"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Moainfo long ol volunteer bilong mipela (raw notes):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dispela dataset i klostu wantaim <a %(a_datasets_openlib)s>Open Library dataset</a>. Em i gat scrape bilong olgeta metadata na bikpela hap bilong ol fail long IA’s Controlled Digital Lending Library. Ol updates i kamap long <a %(a_aac)s>Anna’s Archive Containers format</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Ol dispela rekod i kam stret long Open Library dataset, tasol i gat ol rekod we i no stap long Open Library tu. Mipela i gat planti data files we ol komyuniti memba i scrape long planti yia."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Dispela koleksen i gat tupela hap. Yu nidim tupela hap bilong kisim olgeta data (sapos i no ol superseded torrents, we i krosim long torrents peij)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "namba wan rilisim, bipo mipela i standardizim long <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. I gat metadata (olsem json na xml), pdfs (long acsm na lcpdf digital lending systems), na cover thumbnails."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "nupela ol i kamap, yusim AAC. I gat tasol metadata wantaim timestamps bihain long 2023-01-01, bikos ol arapela i stap pinis long “ia”. Na tu olgeta pdf files, dispela taim i kam long acsm na “bookreader” (IA’s web reader) lending systems. Maski nem i no stret tru, mipela yet i putim bookreader files insait long ia2_acsmpdf_files collection, bikos ol i no wankain."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Main %(source)s website"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digital Lending Library"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata dokyumentesen (planti fields)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN kantri infomesen"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Intanasonal ISBN Agency i save wok long putim ol namba long ol kantri, rijen, o grup bilong tokples. Long dispela, yumi save lukim wanem kantri, rijen, o grup bilong tokples dispela ISBN i kam long en. Nau mipela i yusim dispela infomesen long narapela rot, long yusim <a %(a_isbnlib)s>isbnlib</a> Python library."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Las deit: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN websait"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Long stori bilong ol defren Library Genesis forks, lukim peij bilong <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li i gat bikpela hap bilong sem content na metadata olsem Libgen.rs, tasol i gat sampela koleksen antap long dispela, olsem komiks, magazin, na standard dokumen. Em i bin integratim <a %(a_scihub)s>Sci-Hub</a> long metadata na sear engine bilong en, em mipela i yusim long database bilong mipela."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadata bilong dispela laibrari i fri long <a %(a_libgen_li)s>libgen.li</a>. Tasol, dispela server i slek na i no sapotim resuming bilong bruk koneksen. Olsem files i stap tu long <a %(a_ftp)s>wanpela FTP server</a>, em i wok gutpela."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents i stap bilong planti moa samting, moa yet torrents bilong comics, magazines, na standard dokumen i bin kamap wantaim wok bung wantaim Anna’s Archive."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Fiksen koleksen i gat em yet torrents (i narapela long <a %(a_href)s>Libgen.rs</a>) stat long %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Olsem tok bilong Libgen.li admin, “fiction_rus” (Russian fiksen) koleksen i mas kamap long ol torrents i save kamap long <a %(a_booktracker)s>booktracker.org</a>, moa yet ol <a %(a_flibusta)s>flibusta</a> na <a %(a_librusec)s>lib.rus.ec</a> torrents (yumi save lukim <a %(a_torrents)s>hia</a>, tasol yumi no yet save wanem torrents i stret long wanem files)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistik bilong olgeta koleksen i stap <a %(a_href)s>long libgen's website</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Olsem i luk olsem ol buk we i no stori tu i bin senis, tasol i no gat ol niu torrent. I luk olsem dispela i bin kamap stat long yia 2022, tasol mipela i no bin luksave long dispela yet."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Sampela renj i no gat torrents (olsem fiksen renj f_3463000 i go long f_4260000) i save Z-Library (o narapela dubliket) files, tasol yumi laik mekim sampela deduplikesen na mekim torrents bilong lgli-unik files long ol dispela renj."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Luksave olsem ol torrent files i tok long “libgen.is” em ol miras bilong <a %(a_libgen)s>Libgen.rs</a> (“.is” em narapela domain bilong Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Wanpela gutpela resos bilong yusim metadata em <a %(a_href)s>dispela pes</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiction torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Comics torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Magazine torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standard dokumen torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russian fiksen torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata long FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Infomesen bilong metadata field"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mira bilong ol narapela torrents (na ol narapela fiction na comics torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Toktok forum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Blog post bilong mipela long comics buk release"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Stori kwik bilong ol narapela Library Genesis (o “Libgen”) forks, em olsem long taim i go, ol narapela manmeri i wok wantaim long Library Genesis i no gutpela moa, na ol i go long narapela rot."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Dispela “.fun” veson em i kamap long han bilong orijinal faunda. Em i wok long kamapim gen long wanpela niupela, moa distribiut veson."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Dispela “.rs” veson i gat wankain data, na oltaim i putim autim ol koleksen bilong ol long bulk torrents. Em i gat tupela hap, “fiction” na “non-fiction”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oraitinal long “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Dispela <a %(a_li)s>“.li” veson</a> i gat bikpela koleksen bilong comics, na ol narapela samting, we i no (yet) redi long bulk download long torrents. Em i gat narapela torrent koleksen bilong fiction books, na i gat metadata bilong <a %(a_scihub)s>Sci-Hub</a> long database bilong en."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Long dispela <a %(a_mhut)s>forum post</a>, Libgen.li i bin stap long “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> long sampela rot tu em i wanpela fork bilong Library Genesis, tasol ol i yusim narapela nem bilong projek bilong ol."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Dispela pes em long “.rs” veson. Em i save long putim autim ol metadata na olgeta buk katalog bilong en. Koleksen bilong buk bilong en i gat tupela hap, fiction na non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Wanpela gutpela resos bilong yusim metadata em <a %(a_metadata)s>dispela pes</a> (i blokim IP ranges, VPN inap nidim)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Long 2024-03, ol i kamapim ol nupela torrents long <a %(a_href)s>dispela forum thread</a> (i blokim ol IP ranges, VPN inap nidim)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-Fiction torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiction torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata field information"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-fiction torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiction torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Discussion forum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents bilong Anna’s Archive (buk covers)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Blog bilong mipela long buk covers release"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis i save long givim ol data bilong ol long bulk tru long torrents. Ol data bilong mipela long Libgen i gat ol auxiliary data we ol i no givim stret, long partnership wantaim ol. Tenkyu tru long olgeta manmeri i wok wantaim Library Genesis long wok wantaim mipela!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Dispela <a %(blog_post)s>first release</a> i liklik tasol: klostu 300GB bilong buk covers long Libgen.rs fork, tupela fiction na non-fiction. Ol i stap long wankain we ol i kamap long libgen.rs, olsem:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s bilong wanpela non-fiction buk."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s bilong wanpela fiction buk."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Wankain olsem wantaim Z-Library collection, mipela i putim olgeta long wanpela bikpela .tar file, we inap mountim wantaim <a %(a_ratarmount)s>ratarmount</a> sapos yu laik serveim ol files stret."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> em i wanpela praiwet database bilong non-profit <a %(a_oclc)s>OCLC</a>, we i bungim metadata rekod bilong ol laibreri long olgeta hap bilong graun. Em i save olsem bikpela laibreri metadata koleksen long graun."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktoba 2023, namba wan putim aut:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Long Oktoba 2023 mipela <a %(a_scrape)s>rilisim</a> wanpela bikpela scrape bilong OCLC (WorldCat) database, long <a %(a_aac)s>Anna’s Archive Containers format</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents bilong Anna’s Archive"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Blog post bilong mipela long dispela data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library em wanpela open source project bilong Internet Archive long catalogim olgeta buk long world. Em i gat wanpela bilong bikpela buk scanning operations long world, na i gat planti buk bilong digital lending. Metadata catalog bilong ol buk em i fri long download, na i stap long Anna’s Archive (tasol i no stap long search nau, sapos yu no search stret long Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rilis 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dispela em i wanpela dump bilong planti kol long isbndb.com long Septemba 2022. Mipela traim long karamapim olgeta ISBN renj. Olgeta i olsem 30.9 milion rikod. Long websait bilong ol, ol i tok ol i gat 32.6 milion rikod, olsem mipela inap lusim sampela, o <em>ol</em> inap mekim wanpela rong."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Ol JSON rispons i praioriti raw long server bilong ol. Wanpela data kwoliti isiu mipela lukim, em long ISBN-13 namba i stat wantaim narapela prefix long “978-”, ol i putim yet wanpela “isbn” fil i olsem ISBN-13 namba wantaim namba 3 i katim (na chek digit i kalkuleit gen). Dispela em i klia rong, tasol olsem ol i mekim, olsem mipela no senisim."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Narapela isiu yu inap bungim, em long “isbn13” fil i gat tupela, olsem yu no inap yusim olsem praimari ki long database. “isbn13”+“isbn” fil i kamap olsem i unik."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Longlong bilong Sci-Hub, lukim dispela <a %(a_scihub)s>ofisel websait</a>, <a %(a_wikipedia)s>Wikipedia peij</a>, na dispela <a %(a_radiolab)s>podcast intaviu</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Noken lus tingting olsem Sci-Hub i bin <a %(a_reddit)s>kalabus stat long 2021</a>. Em i bin kalabus bipo, tasol long 2021 sampela milian pepa i bin kamap. Maski, sampela liklik namba bilong pepa i stap yet long kamap long Libgen “scimag” koleksen, tasol i no inap long mekim nupela bikpela torrents."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Mipela i yusim Sci-Hub metadata olsem i kam long <a %(a_libgen_li)s>Libgen.li</a> long “scimag” koleksen. Mipela tu i yusim <a %(a_dois)s>dois-2022-02-12.7z</a> dataset."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Noken lus tingting olsem “smarch” torrents i <a %(a_smarch)s>no wok moa</a> na ol i no stap long list bilong mipela."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents long Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata na torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents long Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents long Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Nupela toktok long Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia peij"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast intaviu"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Ol uploads i go long Anna’s Archive"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Ovaia long <a %(a1)s>datasets peij</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Ol narapela liklik o wanpela taim ol i kamap. Yumi kirapim ol manmeri long putim long ol narapela sado laibrari pastaim, tasol sampela taim ol i gat bung i bikpela tumas long ol narapela i luksave, tasol i no bikpela inap long gat wanpela kategori bilong ol yet."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Dispela “upload” bung i bruk long liklik subcollections, em ol i makim long AACIDs na torrent nem. Olgeta subcollections i bin dedupliket agensim main bung, tasol metadata “upload_records” JSON fail i gat planti referens long ol orijinal fail yet. Non-buk fail tu i bin rausim long planti subcollections, na i no save <em>not</em> makim long “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Planti subcollections yet i gat ol sub-sub-collections (e.g. long ol narapela orijinal sos), we ol i stap olsem directories long “filepath” fields."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Ol subcollections i stap:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Liklik bung"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Nots"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "lukluk"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "painim"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Long <a %(a_href)s>aaaaarg.fail</a>. I luk olsem i klia olgeta. Long volentia bilong mipela “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Long dispela i kam long <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. I gat planti samting i wankain wantaim ol arapela pepa koleksen, tasol i no gat planti MD5 i wankain, olsem na mipela i makim long holim olgeta."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Skrep bilong <q>iRead eBooks</q> (= fonetik <q>ai rit i-books</q>; airitibooks.com), bilong volentia <q>j</q>. Em i stret wantaim <q>airitibooks</q> metadata long <a %(a1)s><q>Ol narapela metadata skreps</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Bikpela bung <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sampela i kam long orijinal ples, sampela i kam long the-eye.eu, na sampela i kam long ol narapela miras."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Long wanpela praiwet buk torrent website, <a %(a_href)s>Bibliotik</a> (ol i save kolim “Bib”), we ol buk i bin bungim long ol torrent long nem (A.torrent, B.torrent) na ol i bin salim i go long the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Long volentia bilong mipela “bpb9v”. Long kisim moa save long <a %(a_href)s>CADAL</a>, lukim ol not long <a %(a_duxiu)s>DuXiu dataset page</a> bilong mipela."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mo bilong volentia bilong mipela “bpb9v”, bikpela hap bilong ol DuXiu fail, wantaim wanpela foilda “WenQu” na “SuperStar_Journals” (SuperStar em kampani bihainim DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Long volentia bilong mipela “cgiym”, ol Saina teks long ol kain kain ples (ol i makim olsem subdirectories), wantaim long <a %(a_href)s>China Machine Press</a> (wanpela bikpela Saina pablikasa)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Ol koleksen i no bilong Saina (ol i makim olsem subdirectories) long volentia bilong mipela “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Skrep bilong ol buk long saet bilong Saina arkitekta, bilong volentia <q>cm</q>: <q>Mi kisim long yusim wanpela netwok lohoul long haus bilong ol buk, tasol dispela lohoul i pas pinis</q>. Em i stret wantaim <q>chinese_architecture</q> metadata long <a %(a1)s><q>Ol narapela metadata skreps</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Ol buk long akademik pablikasa haus <a %(a_href)s>De Gruyter</a>, ol i bin bungim long sampela bikpela torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape bilong <a %(a_href)s>docer.pl</a>, wanpela Polan websait bilong senis bilong ol buk na narapela raitim wok. Ol i scrape long pinis bilong 2023 bilong volentia “p”. Mipela i no gat gutpela metadata long orijinal websait (noken tok long file extension), tasol mipela i bin filtaim ol file i luk olsem buk na planti taim mipela inap kisim metadata long ol file yet."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, stret long DuXiu, ol i bin bungim long volentia “w”. Tasol ol nupela DuXiu buk tasol i stap stret long ol ebooks, olsem na planti bilong ol dispela i mas nupela."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Ol DuXiu fail i stap yet long volentia “m”, we ol i no stap long DuXiu praiwet PDG format (main <a %(a_href)s>DuXiu dataset</a>). Ol i bin bungim long planti orijinal ples, sori tasol ol i no bin holim ol dispela ples long filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Skrep bilong ol buk bilong erotik, bilong volentia <q>do no harm</q>. Em i stret wantaim <q>hentai</q> metadata long <a %(a1)s><q>Ol narapela metadata skreps</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Koleksen ol i bin scrapim long wanpela Japan Manga pablikasa long volentia “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Ol selektim judisial arkaiv bilong Longquan</a>, ol i bin givim long volentia “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape bilong <a %(a_href)s>magzdb.org</a>, wanpela poro bilong Library Genesis (em i stap long libgen.rs hompej) tasol ol i no laik givim ol file bilong ol stret. Volentia “p” i kisim long pinis bilong 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Ol liklik upload i stap, liklik tumas long kamap olsem subkoleksen bilong ol yet, tasol ol i stap olsem direktori."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks bilong AvaxHome, wanpela Rasia fael serim website."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arkaiv bilong niuspepa na makasin. Em i stret wantaim <q>newsarch_magz</q> metadata long <a %(a1)s><q>Ol narapela metadata skreps</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Skrep bilong <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Koleksen bilong volentia “o” husat i bin kisim ol Polan buk stret long orijinal rilis (“scene”) websait."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Ol bungim koleksen bilong <a %(a_href)s>shuge.org</a> long volentia “cgiym” na “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (ol i kolim long nem bilong wanpela buk), ol i bin scrapim long 2022 long volentia “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-koleksen (ol i makim olsem directories) long volentia “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (long <a %(a_sikuquanshu)s>Dizhi(迪志)</a> long Taiwan), mebook (mebook.cc, 我的小书屋, liklik bukrum bilong mi — woz9ts: “Dispela sait i save wok long searim ol gutpela ebook fail, sampela bilong ol i bin setim wantaim long onaman bilong em yet. Onaman i bin <a %(a_arrested)s>arestim</a> long 2019 na wanpela i bin mekim wanpela koleksen bilong ol fail em i bin searim.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Ol DuXiu fail i stap yet long volentia “woz9ts”, we ol i no stap long DuXiu praiwet PDG format (i mas konvetim i go long PDF yet)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Ol torrents bilong Arkaiv bilong Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library i gat rot bilong em long <a %(a_href)s>Library Genesis</a> komuniti, na orijinal i bin stat wantaim ol data bilong ol. Bihain, em i bin kamap profesenel tru, na i gat wanpela nupela interface. Olsem na ol inap kisim planti moa donesen, long mani bilong wokim gutpela sait bilong ol, na donesen bilong ol nupela buk. Ol i bin bungim wanpela bikpela koleksen wantaim Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Apdeit bilong Februeri 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Long pinis bilong 2022, ol man husat ol i tok ol i bin statim Z-Library i bin arestim, na ol domens i bin kisim long ol gavman bilong Yunaitet Stet. Stat long dispela taim websait i go bek online isi isi. I no klia husat i ranim nau."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Koleksen i gat tripela hap. Ol orijinal diskripsen pej bilong namba wan na namba tu hap i stap daunbilo. Yu nidim tripela hap bilong kisim olgeta data (noken tok long ol torrent i bin senis, ol i bin skelim long pej bilong torrent)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: namba wan rilis bilong mipela. Dispela em i bin namba wan rilis bilong samting ol i kolim “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: namba tu rilis, dispela taim wantaim olgeta file i bin wrap insait long .tar file."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: ol nupela rilisim, yusim <a %(a_href)s>Anna’s Archive Containers (AAC) format</a>, nau ol i bin rilisim wantaim Z-Library tim."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrent bilong Anna’s Archive (metadata + konten)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Eksampel rikod long Anna’s Archive (orijinal koleksen)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Eksampel rikod long Anna’s Archive (“zlib3” koleksen)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Main websait"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domen"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blog post bilong Rilis 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blog post bilong Rilis 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib rilis (orijinal deskripson peij)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Rilis 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Fos miror i bin kisim wantaim bikpela hatwok long 2021 na 2022. Long dispela taim em i liklik aotdet: em i soim koleksen long Jun 2021. Mipela bai apdeitim dispela long bihain. Nau mipela i wok long kisim dispela fos rilis i go aut."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Bikos Library Genesis i stap pinis wantaim pablik torrents, na i stap insait long Z-Library, mipela bin mekim liklik wok bilong rausim ol samting i kamap tupela taim long Library Genesis long Jun 2022. Long dispela wok, mipela yusim MD5 hashes. I gat planti moa samting i save kamap tupela taim insait long laibrari, olsem ol buk i stap long planti kain file format. Dispela i hatwok long painim tru, olsem na mipela no mekim. Bihain long rausim ol samting i kamap tupela taim, mipela i gat moa long 2 milion files, i stap klostu long 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Dispela bung bilong ol buk i gat tupela hap: wanpela MySQL “.sql.gz” dump bilong metadata, na 72 torrent files i stap long 50-100GB long wanwan. Metadata i gat ol data olsem Z-Library website i ripotim (title, author, description, filetype), wantaim tru filesize na md5sum mipela i lukim, bikos sampela taim ol dispela i no wankain. I luk olsem i gat ol files Z-Library yet i gat rong metadata. Mipela inap tu i gat rong long daunlodim ol files long sampela hap, na mipela bai traim long painim na stretim long bihain taim."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Ol bikpela torrent files i gat tru data bilong buk, wantaim Z-Library ID olsem filename. Ol file extensions inap long wokim gen yusim metadata dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Dispela koleksen i gat ol buk bilong non-fikson na fikson (i no separatim olsem long Library Genesis). Kwaliti bilong ol buk i no wankain."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Dispela namba wan rilis i nau redi olgeta. Tingim olsem ol torrent files i stap tasol long Tor miror bilong mipela."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Rilis 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Mipela i kisim olgeta buk we ol i putim long Z-Library namel long las miror bilong mipela na August 2022. Mipela i go bek na kisim sampela buk we mipela i lusim long taim bipo. Olgeta, dispela niu koleksen i olsem 24TB. Gen, dispela koleksen i no gat ol buk we i stap pinis long Library Genesis, bikos ol torrents i stap pinis long dispela koleksen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Ol data i stap long wankain pasin olsem namba wan rilisim. I gat wanpela MySQL “.sql.gz” dump bilong metadata, we i gat olgeta metadata long namba wan rilisim, olsem na i winim dispela. Mipela tu addim sampela nupela kolom:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: sapos dispela file i stap pinis long Library Genesis, long non-fikson o fikson koleksen (i mas matchim long md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: we dispela file i stap long wanem torrent."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: setim taim mipela i no inap daunlodim buk."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mipela bin tokaut long dispela taim bipo, tasol long kliaim: “filename” na “md5” em ol tru samting bilong file, tasol “filename_reported” na “md5_reported” em ol samting mipela kisim long Z-Library. Sampela taim ol dispela tupela i no wankain, olsem na mipela putim tupela."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Long dispela rilisim, mipela senisim collation i go long “utf8mb4_unicode_ci”, we i mas wok wantaim ol olpela vesin bilong MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Ol data files i wankain olsem taim bipo, tasol ol i bikpela moa. Mipela no inap long wokim planti liklik torrent files. “pilimi-zlib2-0-14679999-extra.torrent” i gat olgeta files mipela lusim long namba wan rilisim, taim ol narapela torrents i gat ol nupela ID ranges. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Apdeit %(date)s:</strong> Mipela i mekim planti bilong ol torrents bilong mipela i bikpela tumas, na ol torrent klients i hatwok. Mipela i rausim ol na rilisim niu torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Apdeit %(date)s:</strong> I gat planti files yet, olsem mipela i putim ol long tar files na rilisim niu torrents gen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Rilis 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dispela em wanpela tasol ekstra torrent file. Em i no gat niu infomesen, tasol em i gat sampela data insait long em we inap kisim taim long wokim. Dispela i mekim em isi long gat, bikos daunlodim dispela torrent i save kwik winim wokim em long nupela. Long spesel, em i gat SQLite indexes bilong tar files, bilong yusim wantaim <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Olsem Wanem Olsem Wanem (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Wanem samting em “Anna’s Archive”?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> em wanpela projek bilong helpim ol manmeri wantaim tupela as tingting:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservation:</strong> Lukautim olgeta save na kastom bilong ol manmeri.</li><li><strong>Access:</strong> Mekim dispela save na kastom i stap long olgeta manmeri long graun.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Olgeta <a %(a_code)s>kod</a> na <a %(a_datasets)s>data</a> bilong mipela i stap open source olgeta."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Preservation"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Mipela i lukautim ol buk, pepa, komik, magasin, na planti moa, long kisim ol dispela samting long ol narapela <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>, ofisel libraries, na ol narapela koleksen na bungim olgeta long wanpela hap. Olgeta dispela data bai stap oltaim long mekim isi long kopim long bikpela namba — yusim torrents — na mekim planti kopi i stap long olgeta hap bilong graun. Sampela shadow libraries i mekim pinis dispela yet (e.g. Sci-Hub, Library Genesis), tasol Anna’s Archive i “liberates” ol narapela libraries husat i no givim bikpela distribusen (e.g. Z-Library) o i no shadow libraries olgeta (e.g. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Dispela bikpela distribusen, wantaim open-source kod, i mekim website bilong mipela i strong long ol takedowns, na lukautim longpela taim bilong save na kastom bilong ol manmeri. Lanem moa long <a href=\"/datasets\">datasets bilong mipela</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Mipela i ting mipela i lukautim klostu <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% bilong ol buk long graun</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Access"

#, fuzzy
msgid "page.home.access.text"
msgstr "Mipela i wok wantaim ol patna long mekim ol koleksen bilong mipela i isi na fri long olgeta manmeri. Mipela i bilip olsem olgeta manmeri i gat rait long save bilong olgeta manmeri. Na <a %(a_search)s>i no long kot bilong ol raita</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Olgeta daunlod long las 30 de. Avaris long aua: %(hourly)s. Avaris long de: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Mipela i strong bilip long fri flou bilong infomesen, na lukautim save na kastom. Wantaim dispela search engine, mipela i wok long sol bilong ol giant. Mipela i gat bikpela rispek long hatwok bilong ol manmeri husat i bin wokim ol narapela shadow libraries, na mipela i hop olsem dispela search engine bai mekim reach bilong ol i bikpela moa."

#, fuzzy
msgid "page.about.text3"
msgstr "Long stap apdet long progres bilong mipela, bihainim Anna long <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Long ol askim na feedback plis kontakim Anna long %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Olsem wanem mi inap helpim?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Bihainim mipela long <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Tokaut long Anna’s Archive long Twitter, Reddit, Tiktok, Instagram, long lokal kafe o laibrari, o we yu go! Mipela i no bilip long gatekeeping — sapos ol i rausim mipela, mipela bai kamap gen long narapela hap, bikos olgeta kod na data bilong mipela i open source olgeta.</li><li>3. Sapos yu inap, tingim <a href=\"/donate\">donating</a>.</li><li>4. Helpim <a href=\"https://translate.annas-software.org/\">translet</a> website bilong mipela long ol narapela tokples.</li><li>5. Sapos yu wanpela software engineer, tingim long kontribiut long <a href=\"https://annas-software.org/\">open source</a>, o seeding ol <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Nau yumi gat wanpela Matrix channel i wok bung wantaim long %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Sapos yu wanpela security researcher, mipela inap yusim skill bilong yu long ofens na difens. Lukim <a %(a_security)s>Security</a> pej bilong mipela."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Mipela i lukautim ol eksper long payments bilong anonymous merchants. Yu inap helpim mipela long addim moa isi rot bilong donet? PayPal, WeChat, gift cards. Sapos yu save long wanpela, plis kontakim mipela."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Mipela i save lukautim moa server kapasiti."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Yu inap helpim long ripotim ol file issues, lusim koments, na wokim ol lis long dispela website. Yu inap tu helpim long <a %(a_upload)s>uploadim moa buk</a>, o stretim ol file issues o formatting bilong ol buk i stap pinis."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Yu ken wokim o helpim long Wikipedia pej bilong Anna’s Archive long tokples bilong yu."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Mipela laik putim liklik, nais advaetismen. Sapos yu laik advaetais long Anna’s Archive, plis tokim mipela."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Mipela bai amamas sapos ol pipol inap setim <a %(a_mirrors)s>mirrors</a>, na mipela bai sapotim long mani."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Long kisim moa save long hau bilong volunteer, lukim <a %(a_volunteering)s>Volunteering & Bounties</a> pes bilong mipela."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Bilong wanem ol download i go slow tumas?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Mipela tru i no gat inap risos long givim olgeta manmeri long wol kwik daunlod, maski mipela laikim tumas. Sapos wanpela raitman i laik kamap na givim dispela long mipela, em bai nambawan tru, tasol inap nau, mipela traim bes bilong mipela. Mipela wanpela non-profit projek we i save stap tasol long donesen."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dispela em as na mipela putim tupela sistem bilong fri daunlod, wantaim ol patna bilong mipela: ol serba we i daunlod isi, na ol liklik kwik serba wantaim wetlist (long rediusim namba bilong ol pipol i daunlod wantaim)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Mipela tu gat <a %(a_verification)s>browser verification</a> bilong ol isi daunlod, bikos sapos nogat, ol bots na scrapers bai yusim nogut, na mekim samting i moa isi long ol rait yusa."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Noken lus tingim, taim yu yusim Tor Brausa, yu mas senisim ol sekuriti seting bilong yu. Long loas opisen, em i kolim “Standard”, Cloudflare turnstile challenge i wok. Long hai opisen, em i kolim “Safer” na “Safest”, challenge i no wok."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Sapos yu daunlodim bikpela fail, sampela taim daunlod i ken bruk long namel. Mipela i rikumendim yu long yusim daunlod manija (olsem JDownloader) bilong wokim gen bikpela daunlod."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donation FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Ol membership i save renew wantaim?</div> Membership <strong>i no</strong> save renew wantaim. Yu ken join longpela taim o sotpela taim olsem yu laik."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Mi inap apgreitim membasip bilong mi o kisim planti membasip?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Yu gat narapela rot bilong peim?</div> Nau nogat. Planti pipol i no laik ol kain archive olsem i stap, olsem na mipela mas lukaut. Sapos yu ken helpim mipela setim narapela (moa isi) rot bilong peim long seif wei, plis kontakim mipela long %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Olsem wanem ol renj long mun i min?</div> Yu inap kisim long daunbilo hap bilong renj sapos yu yusim olgeta diskaun, olsem yu makim taim i longpela moa long wanpela mun."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Wanem samting yu save yusim donesen long en?</div> 100%% i go long lukautim na mekim save na kalja bilong wol i stap. Nau mipela save yusim bikpela hap long serba, stoa, na bandwidth. No gat mani i go long wanpela memba bilong tim."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Mi ken mekim bikpela donesen?</div> Dispela bai nambawan tru! Long donesen i winim sampela tausen dola, plis kontakim mipela stret long %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Mi ken mekim donesen nating kamap memba?</div> Tru tumas. Mipela kisim donesen bilong olgeta kain mani long dispela Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Olsem wanem mi ken uploadim ol nupela buk?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Narapela rot, yu ken uploadim ol long Z-Library <a %(a_upload)s>long hia</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Long liklik apolot (i go inap long 10,000 fail) plis apolotim long tupela %(first)s na %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Nau, mipela tingim yu uploadim ol nupela buk long Library Genesis forks. Hia em wanpela <a %(a_guide)s>gutpela gaim</a>. Tingim olsem tupela fork we mipela indeks long dispela website i pulim long dispela wankain upload sistem."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Long Libgen.li, yu mas login pastaim long <a %(a_forum)s >forum bilong ol</a> wantaim yusa nem %(username)s na paswod %(password)s, na bihain go bek long <a %(a_upload_page)s >apolot pes bilong ol</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Sapos email adres bilong yu i no wok long Libgen forums, mipela rekomendim yusim <a %(a_mail)s>Proton Mail</a> (fri). Yu ken tu <a %(a_manual)s>askim man</a> long aktivetim akaun bilong yu."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Tingim olsem mhut.org i blokim sampela IP ranges, olsem na VPN inap nidim."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Long bikpela uploads (winim 10,000 fail) we i no kisim long Libgen o Z-Library, plis kontakim mipela long %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Long uploadim akademik pepa, plis tu (long hap bilong Library Genesis) uploadim long <a %(a_stc_nexus)s>STC Nexus</a>. Ol i nambawan shadow library bilong nupela pepa. Mipela i no yet integratim ol, tasol mipela bai long wanpela taim. Yu ken yusim ol <a %(a_telegram)s>upload bot long Telegram</a>, o kontakim adres i stap long pin mesis sapos yu gat planti fail long uploadim long dispela rot."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Olsem wanem mi ken askim buk?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Long dispela taim, mipela i no inap kisim ol buk we yu askim."

#, fuzzy
msgid "page.request.forums"
msgstr "Plis mekim ol askim bilong yu long Z-Library o Libgen forums."

#, fuzzy
msgid "page.request.dont_email"
msgstr "No ken emailim mipela ol askim bilong buk."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Yu save bungim ol metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Yes, mipela save bungim."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Mi daunlodim 1984 bilong George Orwell, ol polis bai kam long haus bilong mi?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "No wari tumas, planti manmeri save daunlodim long ol websait we mipela linkim, na i rabis tru long kisim trabel. Tasol, long stap sef, mipela rekomendim yu long yusim VPN (peid), o <a %(a_tor)s>Tor</a> (fri)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Bai mi save olsem wanem long sevim ol seting bilong searc?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selesim ol seting yu laikim, lusim searc bokis i stap nating, klik “Search”, na bookmarkim peij yusim bookmark featur bilong browser bilong yu."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Yu gat wanpela mobile app?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Mipela i no gat ofisel mobile app, tasol yu inap instolim dispela websait olsem app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klikim tri-dot menu long top raet, na selekt “Add to Home Screen”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klikim “Share” button long daun, na selekt “Add to Home Screen”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Yu gat wanpela API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Mipela gat wanpela stapel JSON API bilong ol memba, bilong kisim kwik daunlod URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentesen insait long JSON yet)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Long ol narapela yus, olsem iterating long olgeta fael bilong mipela, wokim custom searc, na olsem, mipela rekomendim <a %(a_generate)s>generating</a> o <a %(a_download)s>daunlodim</a> ol ElasticSearch na MariaDB databases bilong mipela. Ol raw data inap maniman eksplor <a %(a_explore)s>long ol JSON fael</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Ol raw torrents list bilong mipela inap daunlod olsem <a %(a_torrents)s>JSON</a> tu."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Mi laik helpim long seed, tasol mi no gat planti disk spais."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Yusim <a %(a_list)s>torrent list generator</a> long wokim wanpela list bilong ol torrents we i nidim torrenting moa, insait long ol limit bilong storage spais bilong yu."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Olsem wanem, ol torrents i tu slou; mi inap daunlod ol data stret long yu?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Yes, lukim <a %(a_llm)s>LLM data</a> pej."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Mi inap daunlod wanpela hap tasol bilong ol fael, olsem wanpela tokples o topik tasol?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Liklik ansa: i no isi."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Longpela ansa:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Planti torrents i gat ol fael stret, we yu inap tokim ol torrent klien long daunlodim ol fael yu nidim tasol. Long save long wanem fael long daunlod, yu inap <a %(a_generate)s>mekim</a> metadata bilong mipela, o <a %(a_download)s>daunlod</a> ElasticSearch na MariaDB databases bilong mipela. Sori tumas, sampela torrent koleksens i gat .zip o .tar fael long rot, we yu mas daunlod olgeta torrent pastaim bipo yu inap makim ol fael wan wan."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Mipela i gat <a %(a_ideas)s>sampela tingting</a> long dispela laspela kes.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "I nogat ol i isi yusim ol tul bilong painim ol torrent, tasol mipela welkamim ol kontribusen."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Olsem wanem yu mekim long ol dubliket long ol torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Mipela traim long holim liklik dubliket o ovaplap namel long ol torrents long dispela lis, tasol dispela i no inap oltaim, na i dipen tumas long ol polisi bilong ol laibrari we mipela kisim ol fael. Long ol laibrari we ol i putim ol torrents bilong ol yet, dispela i no long han bilong mipela. Long ol torrents we Anna’s Archive i putim, mipela i mekim dubliket tasol long MD5 hash, we i min olsem ol defren vesens bilong sem buk i no kamap dubliket."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Mi inap kisim lis bilong torrents olsem JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Yes."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Mi no lukim ol PDFs o EPUBs long ol torrents, ol i binari fael tasol? Bai mi mekim wanem?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Ol dispela i tru PDFs na EPUBs, ol i no gat ekstensen long planti bilong ol torrents bilong mipela. I gat tupela ples we yu inap painim metadata bilong ol torrent fael, inkluding ol fael taips/ekstensens:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Wan wan koleksen o rilisim i gat metadata bilong em yet. Olsem, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> i gat wanpela metadata database long Libgen.rs website. Mipela i save linkim long ol relevan metadata risos long wan wan koleksen’s <a %(a_datasets)s>dataset pej</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Mipela i rekomendim <a %(a_generate)s>mekim</a> o <a %(a_download)s>daunlod</a> ElasticSearch na MariaDB databases bilong mipela. Ol dispela i gat wanpela maping bilong wan wan rikod long Anna’s Archive i go long ol torrent fael bilong em (sapos i stap), aninit long “torrent_paths” long ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Bilong wanem torrent client bilong mi ino inap opim sampela bilong ol torrent file / magnet link bilong yupela?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Sampela torrent client ino sapotim bikpela hap saiz, we planti bilong ol torrent bilong mipela i gat (long ol nupela mipela ino mekim olsem moa — maski em i orait long spesifik!). Olsem na traim narapela client sapos yu bungim dispela, o tokim ol manmeri husat i wokim torrent client bilong yu."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Yu gat wanpela risponsibol disklosa program?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Mipela welkamim ol sekuriti risetjas long painim ol wiknes long sistem bilong mipela. Mipela i bikpela sapota bilong risponsibol disklosa. Kontakim mipela <a %(a_contact)s>hia</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Nau mipela i no inap givim bug bounties, ekscept long ol wiknes we i gat <a %(a_link)s>potensel long bagarapim anonimiti bilong mipela</a>, we mipela i ofaim bounties long $10k-50k renj. Mipela laik ofaim moa skop long bug bounties long bihain! Plis notis olsem social engineering ataks i aut long skop."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Sapos yu intres long ofensiv sekuriti, na yu laik helpim long arkaivim save na kalja bilong wol, mas kontakim mipela. I gat planti rot we yu inap helpim."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "I gat moa risos long Anna’s Archive?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — ol regila apdeits"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — open sos kod bilong mipela"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Translate on Anna’s Software</a> — translesen sistem bilong mipela"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — long ol data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — ol narapela domains"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — moa long mipela (plis helpim long mekim dispela pes i stap orait, o wokim wanpela long tokples bilong yu yet!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Bai mi ripotim olsem mi lukim wanpela samting i brukim copyright olsem wanem?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Mipela i no holim wanpela copyright materials long hia. Mipela i wanpela search engine, na olsem mipela i indeksim tasol metadata we i stap pinis long public. Taim yu daunlodim long ol dispela external sources, mipela i ting yu mas lukim ol lo bilong ples yu long wanem samting i orait. Mipela i no gat wok long ol samting ol arapela i hostim."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Sapos yu gat ol toktok long wanem yu lukim long hia, nambawan samting yu ken mekim em long kontakim orijinal website. Mipela i save pulim ol senis bilong ol long database bilong mipela. Sapos yu tru tru ting yu gat wanpela valid DMCA toktok mipela mas bekim, plis pulimapim <a %(a_copyright)s>DMCA / Copyright claim form</a>. Mipela i tingim ol toktok bilong yu, na bai mipela bekim kwiktaim."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Mi no laikim olsem yu wokim dispela projek!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Mipela tu laik tingim olgeta manmeri olsem olgeta kod na data bilong mipela i open source olgeta. Dispela i narapela kain long ol projek olsem bilong mipela — mipela i no save long wanpela narapela projek i gat bikpela katalog olsem na i open source olgeta. Mipela i welkamim tru olgeta husat i ting mipela i no wokim gut projek bilong mipela long kisim kod na data bilong mipela na setimap ol yet shadow library bilong ol! Mipela i no tok olsem long kros o samting — mipela tru tru ting dispela bai i orait tru bikos bai i mekim olgeta samting i kamap gutpela moa, na gutpela long preserim legasi bilong ol manmeri."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Yu gat wanpela uptime monitor?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Lukim dispela <a %(a_href)s>gutpela projek</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Olsem wanem mi ken givim ol buk o narapela pisikal samting?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Plis salim ol i go long <a %(a_archive)s>Internet Archive</a>. Ol bai lukautim gut ol."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Husat em Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Yu em Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Olsem wanem ol buk yu laikim tru?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Hia sampela buk i gat bikpela as long world bilong shadow libraries na digital preservation:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Yu pinis long kwik daunlods bilong tude."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Kamap memba long yusim kwik daunlods."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Nau mipela sapotim Amazon gift cards, kredit na debit cards, crypto, Alipay, na WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Full database"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Buk, pepa, magasin, komik, rekod bilong laibrari, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Sos"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub i <a %(a_paused)s>stopim</a> putim ol nupela pepa."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB i wok long go het bilong Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direk akses long %(count)s akademik pepa"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Op"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Sapos yu wanpela <a %(a_member)s>memba</a>, yu no nidim browser verification."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Longpela taim archive"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Ol datasets we i stap long Anna’s Archive i op olgeta, na yu inap kisim olgeta wantaim torrents. <a %(a_datasets)s>Lainim moa…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Yu inap helpim bikpela tru sapos yu save seedim torrents. <a %(a_torrents)s>Lainim moa…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Mipela i gat bikpela koleksen bilong gutpela teks data long olgeta hap bilong graun. <a %(a_llm)s>Lainim moa…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: askim bilong ol volentia"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Lukautim volentia"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Olsem wanpela non-profit, open-source projek, mipela i save lukautim ol manmeri bilong helpim mipela."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Sapos yu ranim wanpela high-risk anonymous payment processor, plis kontakim mipela. Mipela tu i lukautim ol manmeri bilong putim liklik ads we i luk nais. Olgeta moni i go long ol wok bilong mipela long preserim ol buk."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS daunlods"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Olgeta daunlod links bilong dispela fail: <a %(a_main)s>Fail main page</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(yu inap traim planti taim wantaim IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Sapos yu laik daunlod kwikta na no ken go pas long brausa sek, <a %(a_membership)s>kamap memba</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Sapos yu laik kisim planti buk long wanpela taim, lukim <a %(a_datasets)s>Datasets</a> na <a %(a_torrents)s>Torrents</a> peij."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM data"

#, fuzzy
msgid "page.llm.intro"
msgstr "Em i klia olsem LLM i save wok gut long data i gat gutpela kwoliti. Mipela i gat bikpela koleksen bilong buk, pepa, magasin, na ol arapela samting long olgeta hap bilong graun, we ol dispela i wanpela bilong ol gutpela kwoliti tekst sos."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Spezel skel na renj"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Koleksen bilong mipela i gat moa long handet milien fail, insait long ol akademik jonal, teksbuk, na magasin. Mipela i kamapim dispela skel long bungim ol bikpela eksistim ripositori."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Sampela bilong ol sos koleksen bilong mipela i stap pinis long bulk (Sci-Hub, na sampela hap bilong Libgen). Ol arapela sos mipela yet i bin libereitim. <a %(a_datasets)s>Datasets</a> i soim olgeta ovasait."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Koleksen bilong mipela i gat planti milien buk, pepa, na magasin bipo long e-buk taim. Bikpela hap bilong dispela koleksen i bin OCR’ed pinis, na i gat liklik internal overlap."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Olsem wanem mipela inap helpim yu"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Mipela inap givim yu kwikpela akses long olgeta koleksen bilong mipela, na tu long ol koleksen i no bin rilisim yet."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Dispela em i enterprais-level akses we mipela inap givim long donesen long renj bilong ten tausen USD. Mipela tu redi long swopim dispela long ol gutpela kwoliti koleksen we mipela i no gat yet."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Mipela inap refundim yu sapos yu inap givim mipela enrikmen bilong data bilong mipela, olsem:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Rimuving overlap (deduplication)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Tekst na metadata ekstraksen"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Sapotim longpela taim arkaiv bilong human save, taim yu kisim gutpela data bilong model bilong yu!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontakim mipela</a> bilong toktok long olsem wanem mipela inap wok bung."

#, fuzzy
msgid "page.login.continue"
msgstr "Go yet"

#, fuzzy
msgid "page.login.please"
msgstr "Plis <a %(a_account)s>login</a> long lukim dispela peij.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Archive i stap daun long wokim sampela wok. Plis kam bek long wanpela aua."

#, fuzzy
msgid "page.metadata.header"
msgstr "Wokim gut metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Yu inap helpim long lukautim ol buk sapos yu wokim gut metadata! Pastaim, ritim ol stori long metadata long Anna’s Archive, na bihain lainim olsem yu inap wokim gut metadata long linkim wantaim Open Library, na kisim fri memba long Anna’s Archive."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Stori"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Taim yu lukim wanpela buk long Anna’s Archive, yu inap lukim ol kain kain field: title, raita, publisher, edition, yia, description, filename, na moa yet. Ol dispela infomesen ol i kolim <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Long wanem yumi bungim ol buk long ol kain kain <em>source libraries</em>, yumi soim wanem metadata i stap long dispela source library. Long eksampel, sapos yumi kisim wanpela buk long Library Genesis, bai yumi soim title long Library Genesis’ database."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Sampela taim wanpela buk i stap long <em>planti</em> source libraries, na ol i gat ol narapela metadata field. Long dispela taim, yumi soim longpela vesin bilong wan wan field, long wanem, dispela wan i gat planti gutpela infomesen! Yumi bai soim ol narapela field aninit long description, olsem ”alternative title” (tasol sapos ol i narapela kain)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Yumi tu kisim <em>kode</em> olsem identifiers na classifiers long source library. <em>Identifiers</em> i makim wanpela edition bilong buk; eksampel i stap ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classifiers</em> i bungim planti buk i wankain; eksampel i stap Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. Sampela taim ol dispela kode i stap klia long source libraries, na sampela taim yumi inap kisim long filename o description (mostly ISBN na DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Yumi inap yusim identifiers long painim ol rekod long <em>metadata-only collections</em>, olsem OpenLibrary, ISBNdb, o WorldCat/OCLC. I gat wanpela <em>metadata tab</em> long yumi search engine sapos yu laik lukluk long ol dispela collections. Yumi yusim ol rekod i match long pulimapim ol lus metadata field (eksampel sapos title i lus), o olsem “alternative title” (sapos i gat wanpela title i stap)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Long lukim stret we metadata bilong wanpela buk i kam long, lukim <em>“Technical details” tab</em> long buk page. Em i gat link long raw JSON bilong dispela buk, wantaim pointers long raw JSON bilong ol original rekod."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Long kisim moa infomesen, lukim ol dispela pages: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, na <a %(a_example)s>Example metadata JSON</a>. Las, olgeta metadata bilong yumi inap <a %(a_generated)s>generated</a> o <a %(a_downloaded)s>downloaded</a> olsem ElasticSearch na MariaDB databases."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library linkim"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Sapos yu bungim wanpela file i gat nogut metadata, olsem wanem bai yu stretim? Yu inap go long source library na bihainim ol prosedur bilong stretim metadata, tasol wanem bai yu mekim sapos wanpela file i stap long planti source libraries?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "I gat wanpela identifier i gat spesel ples long Anna’s Archive. <strong>The annas_archive md5 field long Open Library i save winim olgeta narapela metadata!</strong> Bihain liklik na yumi lain long Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library i stat long 2006 bilong Aaron Swartz wantaim gol bilong “wanpela web page bilong olgeta buk i bin kamap”. Em i kain olsem Wikipedia bilong buk metadata: olgeta man inap editim, em i fri lisens, na inap downloadim long bulk. Em i wanpela buk database i stret wantaim misin bilong yumi — tru tumas, Anna’s Archive i bin kisim insperesen long visen na laip bilong Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Nogat wokim gen, yumi makim ol volentia bilong yumi i go long Open Library. Sapos yu lukim wanpela buk i gat rong metadata, yu inap helpim long dispela rot:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Go long <a %(a_openlib)s>Open Library website</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Painim stret buk rekod. <strong>WARNING:</strong> lukaut long makim stret <strong>edition</strong>. Long Open Library, yu gat “works” na “editions”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Wanpela “work” inap olsem “Harry Potter and the Philosopher's Stone”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Wanpela “edition” inap olsem:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Namba 1997 namba wan edisen we Bloomsbery i wokim wantaim 256 pes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Namba 2003 pepa bek edisen we Raincoast Books i wokim wantaim 223 pes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Namba 2000 Polan translesen “Harry Potter I Kamie Filozoficzn” we Media Rodzina i wokim wantaim 328 pes."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Olgeta dispela edisen i gat narapela ISBN na narapela kaen samting insait, olsem yu mas lukaut gut na makim stret wan!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Senisim rekod (o wokim sapos i no gat), na putim planti gutpela infomesen olsem yu inap! Yu stap hia nau, orait, mekim rekod i rili nais."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Aninit long “ID Nambas” makim “Anna’s Archive” na putim MD5 bilong buk long Anna’s Archive. Dispela em longpela string bilong leta na namba bihain long “/md5/” long URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Traim painim narapela fail long Anna’s Archive we i stret wantaim dispela rekod, na putim ol tu. Bihain yumi inap grupim ol olsem dubliket long Anna’s Archive searc peij."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Taim yu pinis, raitim daun URL we yu bin apdeit. Taim yu bin apdeit wantaim 30 rekod wantaim Anna’s Archive MD5s, salim mipela wanpela <a %(a_contact)s>imel</a> na salim mipela list. Mipela bai givim yu wanpela fri membasip bilong Anna’s Archive, olsem yu inap mekim dispela wok isi moa (na olsem tenkyu bilong helpim mipela). Dispela i mas i stap gutpela apdeit we i putim planti infomesen, sapos nogat, yu request bai i no inap. Yu request bai i no inap tu sapos wanpela apdeit i kam bek o i stretim gen bai Open Library modereitas."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Noken lus tingting olsem dispela i wok long ol buk tasol, nogat long akademik pepa o narapela kaen fail. Long narapela kaen fail mipela i stil rekomendim painim ples bilong ol. Em inap kisim sampela wik bilong senis i go insait long Anna’s Archive, bikos mipela i nid daunlodim nupela Open Library data dump, na wokim gen searc indeks bilong mipela."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Mirrors: singaut long ol volentia"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Long mekim strongpela Anna’s Archive, mipela i lukautim ol volentia bilong ranim mirrors."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Mipela lukluk long dispela:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Yu ranim Anna’s Archive open source codebase, na yu save apdeitim kode na data oltaim."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Versen bilong yu i klia olsem wanpela miror, olsem “Bob’s Archive, wanpela miror bilong Anna’s Archive”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Yu redi long kisim ol risik bilong dispela wok, we i bikpela. Yu gat gutpela save long operational security we i nidim. Ol samting long <a %(a_shadow)s>dispela</a> <a %(a_pirate)s>post</a> i klia long yu."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Yu redi long kontribiut long <a %(a_codebase)s>codebase</a> bilong mipela — wantaim tim bilong mipela — long mekim dispela i kamap."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Long stat, mipela bai no givim yu akses long server bilong ol patna bilong mipela, tasol sapos olgeta samting i go gut, mipela inap share wantaim yu."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hosting expenses"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Mipela redi long karamapim hosting na VPN expenses, long stat inap $200 long wanpela mun. Dispela i inap long wanpela basik search server na wanpela DMCA-protected proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Mipela bai baim hosting tasol taim yu setim olgeta samting, na yu soim olsem yu inap apdeitim archive wantaim ol apdeit. Dispela i min olsem yu bai baim pastaim long 1-2 mun long poket bilong yu yet."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Taim bilong yu bai no kisim pe (na taim bilong mipela tu), bikos dispela em i volunteer wok tasol."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Sapos yu kisim bikpela hap long development na operations bilong wok bilong mipela, mipela inap toktok long share moa long donation revenue wantaim yu, bilong yu yusim olsem yu nidim."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Statim wok"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Plis <strong>no kontakim mipela</strong> long askim long permission, o long ol basik askim. Aksen i tok strong moa long ol toktok! Olgeta infomesen i stap, olsem na yu go het na setim miror bilong yu."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Yu inap postim tickets o merge requests long Gitlab bilong mipela taim yu bungim ol hevi. Mipela inap nid long wokim sampela miror-specific features wantaim yu, olsem rebranding long “Anna’s Archive” i go long website nem bilong yu, (long stat) disablim user accounts, o linkim bek long main sait bilong mipela long ol buk pages."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Taim yu ranim miror bilong yu, plis kontakim mipela. Mipela laik lukim OpSec bilong yu, na taim dispela i orait, mipela bai linkim long miror bilong yu, na stat wok klostu wantaim yu."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Tenkyu long olgeta manmeri we i redi long kontribiut long dispela rot! Em i no bilong ol manmeri we i pret, tasol bai i strongim longpela taim bilong bikpela tru open library long histori bilong man."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Daunlod long patna website"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Slo daunlod i stap tasol long ofisal website. Go long %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Slo daunlod i no stap long Cloudflare VPNs o long Cloudflare IP adres."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Plis wet <span %(span_countdown)s>%(wait_seconds)s</span> sekens bilong daunlodim dispela file."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Yusim dispela URL long daunlod: <a %(a_download)s>Daunlod nau</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Tenk yu long wet, dispela i mekim website i fri long olgeta manmeri! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Waning: i gat planti daunlod long IP adres bilong yu long las 24 aua. Daunlod i ken isi isi."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Daunlod long IP adres bilong yu long las 24 aua: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Sapos yu yusim VPN, serim internet koneksen, o ISP bilong yu i serim IPs, dispela waning i ken kamap long dispela as."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Biklong givim olgeta wanpela sans bilong daunlodim ol files fri, yu mas wet pastaim bipo yu inap daunlodim dispela file."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Yu ken go yet long lukluk long Anna’s Archive long narapela tab taim yu wet (sapos brausa bilong yu i sapotim refreshing background tabs)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Yu ken wet long planti daunlod peij long op long wankain taim (tasol plis daunlodim wanpela fail long wankain taim long wanpela seva)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Taim yu kisim daunlod link, em i orait long sampela aua."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rikot long Anna’s Archive"

#, fuzzy
msgid "page.scidb.download"
msgstr "Daunlod"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Long sapotim akses na longpela taim bilong save bilong ol manmeri, kamap wanpela <a %(a_donate)s>memba</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Olsem bonus, 🧬&nbsp;SciDB i save load kwiktaim long ol memba, wantaim no gat limit."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "I no wok? Traim <a %(a_refresh)s>refreshing</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Nogat preview yet. Daunlodim fail long <a %(a_path)s>Anna’s Archive</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB em i wok long bihainim Sci-Hub, wantaim ol i save interface na stret lukim bilong ol PDF. Putim DOI bilong yu long lukim."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Mipela i gat olgeta koleksen bilong Sci-Hub, na tu ol nupela pepa. Planti inap lukim stret wantaim save interface, olsem Sci-Hub. Sampela inap daunlod long ol external sources, na mipela i soim ol link bilong ol."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Painim"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nupela painim"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Putim tasol"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "No putim"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "No makim"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Daunlod"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Jurnal atikol"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digital Lending"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Title, aut, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Painim"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Sait bilong painim"

#, fuzzy
msgid "page.search.submit"
msgstr "Painim"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Painim i kisim longpela taim, em i save kamap long bikpela askim. Ol kaun bilong filta i no inap tru."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Painim i kisim longpela taim, em i makim yu inap lukim rongpela risalts. Sampela taim <a %(a_reload)s>reloadim</a> peij i helpim."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Soim"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "List"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tebol"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Advanst"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Painim ol diskripsen na metadata koments"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Putim spesifik sait bilong painim"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(painim spesifik sait)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Yia i kamap"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Olgeta samting"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Kaen bilong fael"

#, fuzzy
msgid "page.search.more"
msgstr "moa…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Akses"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "As"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scraped na open-sourced bilong AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Tokples"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Oda long"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Ol i bikpela samting tru"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Nupela"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(yia bilong pablikesen)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Olpela"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Bikpela"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(saiz bilong fael)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Liklik"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(opensos)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Rendom"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Sos indiks i apdeit olgeta mun. Nau em i gat ol entri inap long %(last_data_refresh_date)s. Long save moa long teknikal samting, lukim %(link_open_tag)sdatasets page</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Long painim searc indeks long kode, yusim <a %(a_href)s>Kode Eksplora</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Raitim long bokis bilong painim ol %(count)s fael yu ken daunlod stret, we mipela <a %(a_preserve)s>preserve ol inap oltaim</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Tru tumas, olgeta manmeri inap helpim long preserve ol dispela fael long seeding long <a %(a_torrents)s>unified list of torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Nau mipela i gat bikpela open katalog bilong ol buk, pepa, na arapela raiten wok long olgeta hap bilong graun. Mipela i mirorim Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>na moa</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Sapos yu painim ol arapela “shadow libraries” we mipela mas mirorim, o sapos yu gat sampela askim, plis kontakim mipela long %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Long DMCA / copyright claims <a %(a_copyright)s>klik hia</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: yusim keyboard shortcuts “/” (search focus), “enter” (search), “j” (ap), “k” (daun), “<” (prev page), “>” (nex page) bilong kwikpela navigesen."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Lukautim ol pepa?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Raitim long bokis bilong painim ol %(count)s akademik pepa na journal artikol, we mipela <a %(a_preserve)s>preserve ol inap oltaim</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Raitim long bokis bilong painim ol fael long digital lending libraries."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Dispela sos indiks nau i gat metadata bilong Internet Archive’s Controlled Digital Lending library. <a %(a_datasets)s>Moainfo long datasets bilong mipela</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Long painim moa digital lending libraries, lukim <a %(a_wikipedia)s>Wikipedia</a> na <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Raitim long bokis bilong painim metadata bilong libraries. Dispela inap helpim taim yu <a %(a_request)s>requestim wanpela fael</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Dispela sos indiks nau i gat metadata bilong planti metadata sos. <a %(a_datasets)s>Moainfo long datasets bilong mipela</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Long metadata, mipela soim ol orijinal rekod. Mipela no mekim wanpela merging bilong rekod."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "I gat planti, planti sos bilong metadata bilong raiten wok long olgeta hap bilong graun. <a %(a_wikipedia)s>Dispela Wikipedia page</a> em i gutpela stat, tasol sapos yu save long arapela gutpela list, plis tokim mipela."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Taim long bokis bilong painim."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Ol dispela em metadata records, <span %(classname)s>no</span> downloadable files."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "I gat rong long painim."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Traim <a %(a_reload)s>long kirapim gen peij</a>. Sapos dispela hevi i stap yet, plis emailim mipela long %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nogat fael i painim.</span> Traim sampela narapela o sotpela tok bilong painim na filta."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Sampela taim dispela i kamap nogut taim sevis bilong painim i slek. Long dispela taim, <a %(a_attrs)s>reload</a> inap helpim."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Mipela i painim ol samting long: %(in)s. Yu inap lukim URL i stap long hap taim yu <a %(a_request)s>askim long wanpela fael</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Ol Artikol Bilong Jurnal (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digital Lending (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Risalts %(from)s-%(to)s (%(total)s olgeta)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ sampela samting i kamap"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d sampela samting i kamap"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Volunteering & Bounties"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive i bilip long ol volunteers olsem yu. Mipela welkamim olgeta level bilong commitment, na gat tupela main kategori bilong help mipela i lukluk long:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lait volunteer wok:</span> sapos yu inap givim sampela aua hia na hia, i gat planti rot yu inap helpim. Mipela i givim pe long ol volunteers we i wok oltaim wantaim <span %(bold)s>🤝 memberships long Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Hevi wok blong volunteer (USD$50-USD$5,000 bounties):</span> sapos yu save givim planti taem mo/oa risos long wok blong mipela, mipela bai laik wok wantaim yu moa klostu. Bihain yu inap joinim insait tim. Maski mipela i gat smol mani, mipela inap givim <span %(bold)s>💰 mani bounties</span> long ol hevi wok."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Sapos yu no inap volunteerim taem blong yu, yu save helpim mipela yet long <a %(a_donate)s>donetim mani</a>, <a %(a_torrents)s>seeding ol torrents blong mipela</a>, <a %(a_uploading)s>uploadim ol buk</a>, o <a %(a_help)s>tokim ol pren blong yu long Anna’s Archive</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Kampani:</span> mipela i ofaim high-speed direct access long ol koleksen blong mipela long senis blong enterprise-level donesen o senis long ol niupela koleksen (e.g. niupela scans, OCR’ed datasets, enriching data blong mipela). <a %(a_contact)s>Kontakim mipela</a> sapos yu olsem. Lukim tu LLM page blong mipela <a %(a_llm)s>LLM page</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Isi volunteer wok"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Sapos yu gat sampela aua nating, yu save helpim long planti rot. Joinim <a %(a_telegram)s>volunteers chat long Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Long sait blong tenkyu, mipela save givim 6 mun “Lucky Librarian” long ol bikpela milestone, na moa long ol wok volunteer i go yet. Ol milestone i nidim wok i gat gutpela quality — wok nogut i bagarapim mipela moa na mipela bai rausim. Plis <a %(a_contact)s>emailim mipela</a> taim yu kamap long wanpela milestone."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Wok"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Milestone"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Tokaut long Anna’s Archive. Olsem, yu inap tokaut long ol buk long AA, linkim ol post blong blog blong mipela, o soim ol man long website blong mipela."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links o screenshots."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Ol dispela bai soim yu tokim wanpela man long Anna’s Archive, na ol bai tok tenkyu long yu."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Impruvim metadata long <a %(a_metadata)s>linkim</a> wantaim Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Yu inap yusim <a %(a_list)s >list bilong rendom metadata isyu</a> olsem statim poin."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Lukautim yu lusim wanpela koment long ol isyu yu stretim, bai ol arapela ino wokim wankain wok."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links bilong ol rekod yu bin stretim."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Transletim</a> website."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Transletim olgeta tokples (sapos i no klostu long pinis pinis yet.)"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Impruvim Wikipedia page blong Anna’s Archive long tokples blong yu. Putim infomesen long AA’s Wikipedia page long narapela tokples, na long website na blog blong mipela. Putim references long AA long ol narapela relevan pages."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link long edit history i soim yu mekim bikpela kontribusen."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Fulfilim ol buk (o pepa, etc) requests long Z-Library o Library Genesis forums. Mipela i no gat yet buk request system, tasol mipela i mirorim ol library ia, olsem mekim ol i gutpela moa i mekim Anna’s Archive i gutpela tu."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links o screenshots bilong ol askim yu bin mekim."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Sampela smol wok i post long <a %(a_telegram)s>volunteers chat long Telegram</a>. I stap long membership, sampela taim long smol bounties."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Ol liklik wok mipela postim long volunteer chat grup bilong mipela."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "I stap long wok."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Bounti"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Mipela save lukautim ol manmeri husat i gat strongpela save long programing o ofensiv sekuriti long kam insait. Yu inap mekim bikpela wok long lukautim ol samting bilong ol manmeri."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Taim mipela tok tenkyu, mipela givim membasip long ol strongpela kontribiusen. Taim mipela tok bikpela tenkyu, mipela givim moni bounti long ol bikpela na hatpela wok. Dispela i no mas luk olsem wanpela wok, tasol em i wanpela moa insentiv na inap helpim long ol kost."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Bihainim, planti bilong kod bilong mipela i opan sos, na mipela bai askim yu long mekim olsem tu long kod bilong yu taim mipela givim bounti. I gat sampela eksesens yumi inap toktok long wanwan taim."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Bounti i go long namba wan manmeri husat i pinisim wanpela wok. Yu inap raitim koments long bounti tiket long tokim ol arapela olsem yu wok long wanpela samting, olsem ol arapela inap wet o kontakim yu long wok bung. Tasol yu mas save olsem ol arapela i stil fri long wok long en na traim long winim yu. Tasol, mipela no givim bounti long ol wok nogut. Sapos tupela gutpela wok i kamap klostu wantaim (insait long wanpela o tupela de), mipela inap makim bounti long tupela, long laik bilong mipela, olsem 100%% long namba wan wok na 50%% long namba tu wok (olsem 150%% olgeta)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Long ol bikpela bounti (sampela taim skreping bounti), plis kontakim mipela taim yu pinisim ~5%% bilong en, na yu bilip olsem rot bilong yu bai inap long olgeta milestone. Yu bai mas soim rot bilong yu long mipela olsem mipela inap givim feedback. Na tu, long dispela rot mipela inap makim wanem long mekim sapos i gat planti manmeri i klostu long bounti, olsem inap givim long planti manmeri, kirapim ol long wok bung, na olsem."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "WARNIN: ol bikpela bounti wok i <span %(bold)s>hatpela</span> — i gutpela long stat long ol isi wan."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Go long <a %(a_gitlab)s>Gitlab isius lis</a> bilong mipela na soatim long “Label praioriti”. Dispela i soim ol wok mipela i laikim. Ol wok i no gat klia bounti i stil inap long membasip, speseli ol makim “Asepted” na “Anna’s favorite”. Yu inap laik stat long “Starter projek”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Ol updates long <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, bikpela tru open library long histori bilong man."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Bikpela na fri open-data laibreri bilong graun. I gat ol kopi bilong Sci-Hub, Library Genesis, Z-Library, na moa yet."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Painim long Anna’s Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’s Archive i nidim help bilong yu!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Planti i traim long rausim mipela, tasol mipela i pait bek."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Sapos yu donet nau, yu kisim <strong>tupla taim</strong> namba bilong kwik daunlod."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "I orait inap pinis long pinis bilong dispela mun."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donet"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Seivim save bilong ol man: wanpela gutpela presen bilong holide!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Surprisim wanpela yu laikim, givim em wanpela akaun wantaim membasip."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Blong mekim strong moa Anna’s Archive, mipela lukautim ol lain long helpim long ranim ol miras."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Gutpela presen bilong Valentine!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Mipela i gat nupela rot bilong givim donesen: %(method_name)s. Plis tingim %(donate_link_open_tag)sgivim donesen</a> — i no isi long ranim dispela website, na donesen bilong yu i tru tru helpim. Tenkyu tru."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Mipela i ranim wanpela fundraiser bilong <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">bekapim</a> bikpela comics shadow library long wol. Tenkyu long sapot bilong yu! <a href=\"/donate\">Donet nau.</a> Sapos yu no inap donet, plis sapotim mipela long tokim ol pren bilong yu, na bihainim mipela long <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Ol nupela daunlod:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Sos"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Impruvim metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Volunteering & Bounties"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktiviti"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Codes Explorer"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Haus"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Translet ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Login / Register"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Akaun"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Stap long kontak"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / copyright claims"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Advan"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sefti"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Olsem"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nogat asosiet"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Dispela fail i gat sampela hevi."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Kwik daunlod"

#, fuzzy
msgid "page.donate.copy"
msgstr "kopi"

#, fuzzy
msgid "page.donate.copied"
msgstr "kopied!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Bipo"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Neks"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "tasol long dispela mun!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub i <a %(a_closed)s>stopim</a> upload bilong ol nupela pepa."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selesim wanpela peimen opsen. Mipela givim diskaun long ol crypto-based peimen %(bitcoin_icon)s, bikos mipela kisim (planti) liklik fi."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selesim wanpela peimen opsen. Mipela nau gat crypto-based peimen tasol %(bitcoin_icon)s, bikos ol tradisenel peimen prosesa i no laik wok wantaim mipela."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Mipela i no inap sapotim kredit/debit kad stret, bikos ol bank i no laik wok wantaim mipela. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tasol, i gat sampela rot long yusim kredit/debit kad, yusim ol arapela peimen rot bilong mipela:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Slop & ekstanal daunlod"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Daunlod"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Sapos yu yusim crypto long namba wan taim, mipela i ting yu yusim %(option1)s, %(option2)s, o %(option3)s long baim na givim donesen wantaim Bitcoin (olsem orijinal na bikpela cryptocurrency)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links blong ol records yu impruvim."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links o screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links o screenshots blong ol requests yu fulfilim."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Sapos yu laik mirroring ol dispela datasets long <a %(a_faq)s>archival</a> o <a %(a_llm)s>LLM training</a> purposes, plis kontakim mipela."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Sapos yu intres long mirroring dispela dataset bilong <a %(a_archival)s>archival</a> o <a %(a_llm)s>LLM training</a> purposes, plis kontakim mipela."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Main website"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN kantri infomesen"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Sapos yu gat intres long mirorim dispela dataset long <a %(a_archival)s>archival</a> o <a %(a_llm)s>LLM trening</a> purposes, plis kontakim mipela."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Intanesenel ISBN Ejensi i save putim ol renj we em i givim long ol nesenel ISBN ejensi. Long dispela mipela inap save wanem kantri, rijen, o tokples grup dispela ISBN i bilong. Nau mipela i yusim dispela data long narapela rot, long yusim <a %(a_isbnlib)s>isbnlib</a> Python laibrari."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Risos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Las apdeit: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN website"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Nogat “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Inspiresen bilong mipela long bungim metadata em Aaron Swartz’ gol bilong “wanpela web peij bilong olgeta buk i bin kamap”, em i wokim <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dispela projek i wok gut, tasol posisen bilong mipela i save kisim metadata ol i no inap."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Narapela inspiresen em laik bilong mipela long save <a %(a_blog)s>hamas buk i stap long wol</a>, olsem mipela inap kauntim hamas buk i stap yet long seivim."

#~ msgid "page.partner_download.text1"
#~ msgstr "Long givim olgeta manmeri sans long daunlodim ol fail fri, yu mas wet <strong>%(wait_seconds)s sekens </strong> bipo yu ken daunlodim dispela fail."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automatik refresh pes. Sapos yu lusim taim bilong daunlod, taim bai stat gen, olsem na mipela i tingim yu long yusim automatik refresh."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Daunlod nau"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvet: yusim online tul long konvetim ol format. Eksampel, long konvetim epub na pdf, yusim <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: daunlodim file (pdf o epub i sapot), na <a %(a_kindle)s>salim i go long Kindle</a> yusim web, app, o email. Helpim tul: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Saportim ol raita: Sapos yu laikim dispela na yu inap, tingim long baim orijinal, o sapotim ol raita stret."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Sapotim ol laibreri: Sapos dispela i stap long laibreri bilong yu, tingim long borouim fri long hap."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nogat long bulk, tasol long semi-bulk bihain long wanpela peiwol"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive i lukautim wanpela bung bilong <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb em i wanpela kampani i save kisim ol infomesen long ol online bukstoa long painim ISBN metadata. Anna’s Archive i save mekim bekap bilong ISBNdb buk metadata. Dispela metadata i stap long Anna’s Archive (tasol nau yu no inap painim long sear, sapos yu no painim ISBN namba stret)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Long teknikal detel, lukim daunbilo. Long wanpela taim yumi inap yusim long lukim wanem buk i no stap yet long ol sado laibrari, long mekim praioriti long painim na/ o skanim ol buk."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Blog post bilong mipela long dispela data"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb scrape"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Nau mipela i gat wanpela torrent, i gat 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> fail (20GB unzipped): “isbndb_2022_09.jsonl.gz”. Long importim wanpela “.jsonl” fail long PostgreSQL, yu inap yusim samting olsem <a %(a_script)s>dispela skript</a>. Yu inap paipim stret yusim samting olsem %(example_code)s olsem i decompress long fly."

#~ msgid "page.donate.wait"
#~ msgstr "Plis wet inap <span %(span_hours)s>tu aua</span> (na refresim dispela peij) bipo yu kontakim mipela."

#~ msgid "page.codes.search_archive"
#~ msgstr "Painim Anna’s Archive bilong “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donet wantaim Alipay o WeChat. Yu ken makim namel long dispela long nekis peij."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Tokaut long Anna’s Archive long social media na online forums, long rekomendim buk o lis long AA, o ansaim ol askim."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Koleksen bilong fiksen i bin senis tasol i gat <a %(libgenli)s>torrent</a>, tasol i no bin apdeit stat long 2022 (mipela i gat stret daunlod)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna’s Archive na Libgen.li i wok bung wantaim long manesim ol koleksen bilong <a %(comics)s>comic books</a> na <a %(magazines)s>magazines</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nogat torrents bilong Russian fikson na standard dokuments koleksen."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "I no gat torrents bilong ol narapela samting. Ol torrents long Libgen.li em ol miras bilong ol narapela torrents i stap hia. Wanpela samting i narapela em ol fiction torrents i stat long %(fiction_starting_point)s. Ol comics na magazines torrents i kamap wantaim wok bung bilong Anna’s Archive na Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Long wanpela bung <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> orijin i no klia. Sampela i kam long the-eye.eu, sampela i kam long narapela hap."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

