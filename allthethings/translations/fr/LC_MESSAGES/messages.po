msgid "layout.index.invalid_request"
msgstr "Requ<PERSON>te invalide. Visitez %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Bibliothèque de prêt de l'Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " et "

msgid "layout.index.header.tagline_and_more"
msgstr "et plus encore"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Archive-miroir pour %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Nous arpentons et rendons open-source %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Tout notre code et nos données sont entièrement open-source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;La plus grande bibliothèque véritablement ouverte de l’histoire de l’humanité."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;livres, %(paper_count)s&nbsp;articles— préservés à tout jamais."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La plus grande bibliothèque open-source et open-data au monde. ⭐️&nbsp;Archive-miroir de Sci-Hub, Library Genesis, Z-Library, et autres. 📈&nbsp;%(book_any)s livres, %(journal_article)s articles, %(book_comic)s bandes-dessinées, %(magazine)s magazines — préservés pour toujours."

msgid "layout.index.header.tagline_short"
msgstr "📚 La plus grande bibliothèque open source de données libres.<br>⭐️ Archive-miroir de Scihub, Libgen, Zlib, et bien d'autres."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Métadonnées incorrectes (ex : titre, description, image de couverture)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problèmes de téléchargement (ex : impossible de se connecter, message d'erreur, extrême lenteur)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Impossible d'ouvrir le fichier (ex : fichier corrompu, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Mauvaise qualité (ex : problèmes de formatage, scan de mauvaise qualité, pages manquantes)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Ce fichier/spam devrait être retiré (ex : pub, contenu abusif)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Revendication de droits d'auteur"

msgid "common.md5_report_type_mapping.other"
msgstr "Autre"

msgid "common.membership.tier_name.bonus"
msgstr "Téléchargements bonus"

msgid "common.membership.tier_name.2"
msgstr "Brillant Bibliophile"

msgid "common.membership.tier_name.3"
msgstr "Libraire Lucide"

msgid "common.membership.tier_name.4"
msgstr "Dico Doux-Dingue"

msgid "common.membership.tier_name.5"
msgstr "Archiviste Astral"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "non payé"

msgid "common.donation.order_processing_status_labels.1"
msgstr "payé"

msgid "common.donation.order_processing_status_labels.2"
msgstr "annulé"

msgid "common.donation.order_processing_status_labels.3"
msgstr "expiré"

msgid "common.donation.order_processing_status_labels.4"
msgstr "En attente de la confirmation d'Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "invalide"

msgid "page.donate.title"
msgstr "Faire un don"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Vous avez déjà un <a %(a_donation)s>don en cours</a>. Veuillez le finaliser ou l'annuler avant d'en faire un nouveau."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Voir tous mes dons</a>"

msgid "page.donate.header.text1"
msgstr "Les Archives d'Anna est un projet à but non lucratif, open-source et aux données libres. En faisant un don et en devenant membre, vous soutenez nos opérations et notre développement. À tous nos membres : merci de nous permettre de continuer ! ❤️"

msgid "page.donate.header.text2"
msgstr "Pour plus d'informations, consultez la <a %(a_donate)s>FAQ sur les dons</a>."

msgid "page.donate.refer.text1"
msgstr "Pour obtenir encore plus de téléchargements, <a %(a_refer)s>parrainez vos amis</a> !"

msgid "page.donate.bonus_downloads.main"
msgstr "Vous bénéficiez de %(percentage)s%% de téléchargements rapides en bonus, car l'utilisateur %(profile_link)s vous a parrainé."

msgid "page.donate.bonus_downloads.period"
msgstr "Cela s'applique à toute la période d'adhésion."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s téléchargements rapides par jour"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "si vous faites un don ce mois-ci !"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mois"

msgid "page.donate.buttons.join"
msgstr "Rejoindre"

msgid "page.donate.buttons.selected"
msgstr "Sélectionné"

msgid "page.donate.buttons.up_to_discounts"
msgstr "jusqu'à %(percentage)s%% de réduction"

msgid "page.donate.perks.scidb"
msgstr "Documents SciDB <strong>illimités</strong> sans vérification"

msgid "page.donate.perks.jsonapi"
msgstr "Accès à l' <a %(a_api)s>API JSON</a>"

msgid "page.donate.perks.refer"
msgstr "Gagnez <strong>%(percentage)s%% de téléchargements bonus</strong> en <a %(a_refer)s>parrainant des amis</a>."

msgid "page.donate.perks.credits"
msgstr "Votre nom d'utilisateur ou mention anonyme dans les crédits"

msgid "page.donate.perks.previous_plus"
msgstr "Avantages précédents, avec en plus :"

msgid "page.donate.perks.early_access"
msgstr "Accès anticipé aux nouvelles fonctionnalités"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Compte Telegram exclusif avec les coulisses du projet"

msgid "page.donate.perks.adopt"
msgstr "“Adopte un torrent” : votre nom d'utilisateur ou message dans le nom d'un fichier torrent <div %(div_months)s> une fois tous les 12 mois d'adhésion</div>"

msgid "page.donate.perks.legendary"
msgstr "Statut Légendaire dans la préservation de la culture et du savoir humain"

msgid "page.donate.expert.title"
msgstr "Accès expert"

msgid "page.donate.expert.contact_us"
msgstr "contactez-nous"

msgid "page.donate.small_team"
msgstr "Nous sommes une petite équipe de bénévoles. Nous pouvons prendre 1 à 2 semaines à vous répondre."

msgid "page.donate.expert.unlimited_access"
msgstr "Accès haut débit <strong>illimité</strong>"

msgid "page.donate.expert.direct_sftp"
msgstr "Serveurs <strong>SFTP</strong> directs"

msgid "page.donate.expert.enterprise_donation"
msgstr "Dons à l'échelle d'une entreprise ou échange contre de nouvelles collections (ex : nouvelles numérisations, jeu de données OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Nous acceptons les dons conséquents provenant d'individus fortunés ou d'institutions. "

msgid "page.donate.header.large_donations"
msgstr "Pour les dons supérieurs à 5 000 $, veuillez nous contacter directement à %(email)s."

msgid "page.donate.header.recurring"
msgstr "Veuillez noter que bien que les adhésions sur cette page soient « par mois », ce sont des dons uniques (non récurrents). Voir la <a %(faq)s>FAQ sur les dons</a>."

msgid "page.donate.without_membership"
msgstr "Si vous désirez faire un don (quel que soit le montant) sans demander d'adhésion, utilisez librement cette adresse Monero (XMR) : %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Sélectionnez un moyen de paiement."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporairement indisponible)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Carte cadeau %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carte bancaire (via l'application)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto-monnaie %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carte de crédit/débit"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (régulier)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Carte / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Crédit/débit/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brésil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Carte bancaire"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carte de crédit/débit (secours)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carte de crédit/débit 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Les crypto-monnaies acceptées en don sont les BTC, ETH, XMR et SOL. Utilisez cette option si vous êtes déjà familier des crypto-monnaies."

msgid "page.donate.payment.desc.crypto2"
msgstr "Les crypto-monnaies acceptées en don sont les BTC, ETH, XMR et autres."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si vous utilisez de la crypto pour la première fois, nous vous suggérons d'utiliser %(options)s pour acheter et faire un don en Bitcoin (la cryptomonnaie originale et la plus utilisée)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Pour faire un don par PayPal, nous utiliserons PayPal Crypto, ce qui nous permet de rester anonyme. Nous vous remercions de prendre le temps d'apprendre à utiliser cette méthode, car cela nous aide beaucoup."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Faites un don en utilisant PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Faites un don via Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si vous avez Cash App, c'est le moyen le plus facile de nous faire un don !"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Notez que pour les transactions inférieures à %(amount)s, Cash App impose %(fee)s de frais. Pour %(amount)s et plus, c'est gratuit !"

msgid "page.donate.payment.desc.revolut"
msgstr "Faites un don en utilisant Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Si vous avez Revolut, c’est le moyen le plus simple de faire un don !"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Faites un don avec une carte de crédit ou de débit."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay et Apple Pay peuvent aussi marcher."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Notez que pour les petits dons, les frais de carte de crédit peuvent anéantir notre réduction de %(discount)s, aussi, nous recommandons des abonnements plus longs."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Notez que pour les petits dons, les frais sont élevés, aussi, nous recommandons un abonnement plus long."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Avec Binance, vous achetez du Bitcoin avec une carte de crédit/débit ou un compte bancaire, pour ensuite nous faire don de cette quantité de Bitcoin. Ainsi, nous pouvons recevoir votre don tout en préservant votre anonymat et votre sécurité."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance est disponible dans la plupart des pays du monde et fonctionne avec la plupart des banques et cartes de crédit/débit. Pour nous faire don, c'est actuellement la méthode que nous vous recommandons. Merci d'avance pour le temps que vous aurez pris pour apprendre à nous faire don par ce biais, cette méthode nous aide énormément."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Faites un don en utilisant votre compte PayPal."

msgid "page.donate.payment.desc.givebutter"
msgstr "Faites un don en utilisant une carte de crédit/débit, PayPal ou encore Venmo. Vous pouvez choisir une de ces méthodes sur la page suivante."

msgid "page.donate.payment.desc.amazon"
msgstr "Faites un don en utilisant une carte cadeau Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Notez que nous devons arrondir aux valeurs acceptées par nos revendeurs (%(minimum)s minimum)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> Nous utilisons exclusivement le site Amazon.com, et pas les variantes locales. Par exemple, .fr, .de, .co.uk, .ca, ne sont PAS supportés."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANT :</strong> Cette option est pour %(amazon)s. Si vous souhaitez utiliser un autre site Amazon, sélectionnez-le ci-dessus."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Cette méthode utilise un fournisseur de cryptomonnaie comme convertisseur intermédiaire. Cela peut être un peu déroutant, alors veuillez n'utiliser cette méthode que si les autres ne fonctionnent pas. De plus, elle ne fonctionne pas dans tous les pays."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Faites un don en utilisant une carte de crédit/débit, via l'application Alipay (très facile à configurer)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installez l'application Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installez l'application Alipay depuis l'<a %(a_app_store)s>Apple App Store</a> ou le <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "S'inscrire en utilisant un numéro de téléphone."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Aucune autre donnée personnelle n'est requise."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Ajoutez une carte bancaire"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supporté : Visa, MasterCard, JCB, Diners Club et Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Voir <a %(a_alipay)s>ce guide</a> pour plus d'informations."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nous ne pouvons pas accepter directement les cartes de crédit/débit, car les banques ne veulent pas travailler avec nous. ☹ Cependant, il existe plusieurs façons d’utiliser indirectement les cartes de crédit/débit avec méthodes de paiement :"

msgid "page.donate.payment.buttons.amazon"
msgstr "Carte Cadeau Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Envoyez-nous des cartes cadeau Amazon.com en utilisant votre carte de crédit/débit."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay prend en charge les cartes de crédit/débit internationales. Voir <a %(a_alipay)s>ce guide</a> pour plus d’informations."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) accepte les cartes de crédit/débit internationales. Dans l'appli WeChat, allez dans “Me → Services → Wallet → Add a Card”. Si vous ne voyez pas ce menu, activez-le en naviguant dans “Me → Settings → General → Tools → Weixin Pay → Enable”."

msgid "page.donate.ccexp.crypto"
msgstr "Vous pouvez acheter de la crypto-monnaie en utilisant votre carte de crédit/débit."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Service express Crypto"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Les services express sont pratiques, mais facturent des frais plus élevés."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Vous pouvez les utiliser à la place d'un échange crypto si vous souhaitez faire rapidement un don plus important et que des frais de 5 à 10 $ de nous dérangent pas."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assurez-vous d'envoyer le montant exact en crypto indiqué sur la page de don, et non le montant en $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Sinon, les frais seront déduits et nous ne pourrons pas traiter automatiquement votre adhésion."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum : %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum : %(minimum)s selon le pays, pas de vérification pour la première transaction)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum : %(minimum)s, pas de vérification pour la première transaction)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum : %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum : %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum : %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si l'une de ces informations est obsolète, veuillez nous envoyer un email pour nous en informer."

msgid "page.donate.payment.desc.bmc"
msgstr "Pour les cartes de crédit/débit, Apple Pay et Google Pay, nous utilisons “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Dans leur système, un “café” vaut 5 $, donc votre don sera arrondi au multiple de 5 le plus proche."

msgid "page.donate.duration.intro"
msgstr "Sélectionnez la durée de votre abonnement."

msgid "page.donate.duration.1_mo"
msgstr "1 mois"

msgid "page.donate.duration.3_mo"
msgstr "3 mois"

msgid "page.donate.duration.6_mo"
msgstr "6 mois"

msgid "page.donate.duration.12_mo"
msgstr "12 mois"

msgid "page.donate.duration.24_mo"
msgstr "24 mois"

msgid "page.donate.duration.48_mo"
msgstr "48 mois"

msgid "page.donate.duration.96_mo"
msgstr "96 mois"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>après <span %(span_discount)s></span> réductions</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Cette méthode de paiement requiert un minimum de %(amount)s. Veuillez choisir un abonnement ou un moyen de paiement différent."

msgid "page.donate.buttons.donate"
msgstr "Faites un don"

msgid "page.donate.payment.maximum_method"
msgstr "Cette méthode de paiement n'accepte pas les montants au-dessus de %(amount)s. Veuillez choisir un abonnement ou un moyen de paiement différent."

msgid "page.donate.login2"
msgstr "Pour devenir membre, veuillez <a %(a_login)s>vous connecter ou vous inscrire </a>. Merci de votre soutien !"

msgid "page.donate.payment.crypto_select"
msgstr "Sélectionner la cryptomonnaie de votre choix :"

msgid "page.donate.currency_lowest_minimum"
msgstr "(montant minimal le plus bas)"

msgid "page.donate.coinbase_eth"
msgstr "(utiliser lors de l'envoi d'Ethereum depuis Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(attention : montant minimum élevé)"

msgid "page.donate.submit.confirm"
msgstr "Cliquez sur le bouton \"Faire un don\" pour confirmer le don."

msgid "page.donate.submit.button"
msgstr "Faire un don <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Vous pouvez toujours annuler le don durant la phase de paiement."

msgid "page.donate.submit.success"
msgstr "✅ Redirection vers la page de don…"

msgid "page.donate.submit.failure"
msgstr "❌ Une erreur est survenue. Veuillez recharger la page et réessayer."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mois"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "pour 1 mois"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "pour 3 mois"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "pour 6 mois"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "pour 12 mois"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "pour 24 mois"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "pour 48 mois"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "pour 96 mois"

msgid "page.donate.submit.button.label.1_mo"
msgstr "pour 1 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "pour 3 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "pour 6 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "pour 12 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "pour 24 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "pour 48 mois “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "pour 96 mois “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Don"

msgid "page.donation.header.date"
msgstr "Date : %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total : %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mois pour %(duration)s mois, incluant %(discounts)s%% de remise)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total : %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mois pour %(duration)s mois)</span>"

msgid "page.donation.header.status"
msgstr "Statut : <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identifiant : %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Annuler"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Êtes-vous sûr de vouloir annuler ? N'annulez pas si vous avez déjà payé."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Oui, veuillez annuler"

msgid "page.donation.header.cancel.success"
msgstr "✅ Votre don a été annulé."

msgid "page.donation.header.cancel.new_donation"
msgstr "Faire un nouveau don"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Une erreur est survenue. Veuillez recharger la page et réessayer."

msgid "page.donation.header.reorder"
msgstr "Commander à nouveau"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Vous avez déjà payé. Si vous souhaitez tout de même consulter les instructions de paiement, cliquez ici :"

msgid "page.donation.old_instructions.show_button"
msgstr "Afficher les anciennes instructions de paiement"

msgid "page.donation.thank_you_donation"
msgstr "Merci pour votre don !"

msgid "page.donation.thank_you.secret_key"
msgstr "Si vous ne l'avez pas déjà fait, récupérez votre clé secrète qui vous permet de vous connecter :"

msgid "page.donation.thank_you.locked_out"
msgstr "Ou vous prenez le risque de bloquer l'accès à votre compte !"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Les instructions de paiement ont expiré. Si vous souhaitez faire un autre don, utilisez le bouton \"Commander à nouveau\" ci-dessus."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Remarque importante :</strong>Le prix des cryptomonnaies peut énormément fluctuer, d'une différence atteignant parfois 20 %% en quelques minutes. C'est toujours moins que les frais que nous payons auprès de nombreux services de paiement, qui facturent souvent 50 à 60 %% pour travailler avec une \"organisation clandestine\" comme la nôtre. <u>Si vous nous envoyez le justificatif de paiement contenant le prix initial que vous avez payé avant ces possibles fluctuations, nous accréditerons votre compte avec l'abonnement choisi</u> (à condition que le justificatif date de quelques heures tout au plus). Nous apprécions sincèrement votre volonté à surmonter ce genre de contraintes afin nous soutenir ! ❤️"

msgid "page.donation.expired"
msgstr "Ce don a expiré. Veuillez l'annuler et en créer un nouveau."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instructions pour les cryptomonnaies"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Faites un transfert vers l'un de nos comptes crypto"

msgid "page.donation.payment.crypto.text1"
msgstr "Faites un don d'un montant total de %(total)s à l'une de ces adresses :"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Achetez des bitcoins avec Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Cherchez la page \"Crypto\" dans votre application ou site web PayPal. Elle se trouve généralement dans la catégorie\"Finances\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Suivez les instructions pour acheter des Bitcoins (BTC). Vous n'avez besoin d'acheter que le montant que vous souhaitez donner, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transférez les bitcoins vers notre adresse"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Accédez à la page \"Bitcoin\" de votre application ou site web PayPal. Appuyez sur le bouton \"Transférer\" %(transfer_icon)s, puis \"Envoyer\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Entrez notre adresse Bitcoin (BTC) en tant que destinataire, et suivez les instructions pour envoyer votre don de %(total)s :"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instructions pour les cartes de crédit / débit"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Faites un don via notre page pour cartes de crédit / débit"

msgid "page.donation.donate_on_this_page"
msgstr "Faites un don de %(amount)s sur <a %(a_page)s>cette page</a>."

msgid "page.donation.stepbystep_below"
msgstr "Reportez-vous au pas-à-pas ci-dessous."

msgid "page.donation.status_header"
msgstr "Statut :"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "En attente de confirmation (rafraîchissez la page pour vérifier)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "En attente du transfert (rafraîchissez la page pour vérifier)…"

msgid "page.donation.time_left_header"
msgstr "Temps restant :"

msgid "page.donation.might_want_to_cancel"
msgstr "(si vous le souhaitez, vous pouvez annuler et créer un nouveau don)"

msgid "page.donation.reset_timer"
msgstr "Pour réinitialiser le minuteur, créez simplement un nouveau don."

msgid "page.donation.refresh_status"
msgstr "Mise à jour du statut"

msgid "page.donation.footer.issues_contact"
msgstr "Si vous rencontrez un quelconque problème, veuillez nous contacter à l'adresse %(email)s et inclure autant d'informations que possible (comme des captures d'écran par exemple)."

msgid "page.donation.expired_already_paid"
msgstr "Si vous avez déjà payé :"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Parfois, la confirmation peut prendre jusqu'à 24 heures, alors assurez-vous de rafraîchir cette page (même si elle a expiré)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Achetez des PYUSD sur PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Suivez les instructions afin d'acheter des PYUSD (PayPal US dollars)."

msgid "page.donation.pyusd.more"
msgstr "Achetez un peu plus (nous vous recommandons d'ajouter %(more)s) que le montant dont vous souhaitez faire don (%(amount)s), pour couvrir les frais de transaction. Vous garderez ce qu'il restera."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Accédez à la page \"PYUSD\" de l'application ou site web PayPal. Appuyez sur le bouton \"Transférer\" %(icon)s, puis sur \"Envoyer\"."

msgid "page.donation.transfer_amount_to"
msgstr "Transférez %(amount)s vers %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Acheter du Bitcoin (BTC) sur Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Allez à la page « Bitcoin » (BTC) dans Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Achetez-en un peu plus (nous recommandons %(more)s de plus) que le montant dont vous souhaitez faire don (%(amount)s), pour couvrir les frais de transaction. Vous garderez le reste qui n'aura pas servi à la transaction."

msgid "page.donation.cash_app_btc.step2"
msgstr "Transférez le Bitcoin à notre adresse"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Cliquez sur le bouton \"Envoyer du bitcoin\" pour effectuer un \"retrait\". Passez des dollars au BTC en appuyant sur l’icône %(icon)s. Entrez le montant en BTC ci-dessous et cliquez sur \"Envoyer\". Regardez <a %(help_video)s>cette vidéo</a> si vous êtes bloqué."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Pour les petits dons (moins de 25 $), vous devrez peut-être utiliser Rush ou Priority."

msgid "page.donation.revolut.step1"
msgstr "Acheter du Bitcoin (BTC) sur Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Allez à la page \"Crypto\" dans Revolut pour acheter du Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Achetez un peu plus (nous recommandons %(more)s de plus) que le montant que vous donnez (%(amount)s), pour couvrir les frais de transaction. Vous garderez tout ce qui reste."

msgid "page.donation.revolut.step2"
msgstr "Transférez le Bitcoin à notre adresse"

msgid "page.donation.revolut.step2.transfer"
msgstr "Cliquez sur le bouton « Envoyer du bitcoin » pour effectuer un « retrait ». Passez des euros au BTC en appuyant sur l’icône %(icon)s. Entrez le montant en BTC ci-dessous et cliquez sur « Envoyer ». Regardez <a %(help_video)s>cette vidéo</a> si vous êtes bloqué."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Assurez-vous d'utiliser le montant en BTC ci-dessous, <em>PAS</em> en euros ou en dollars, sinon nous ne recevrons pas le montant correct et ne pourrons pas confirmer automatiquement votre adhésion."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Pour les petits dons (moins de 25 $), vous devrez peut-être utiliser Rush ou Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Utilisez l’un des services express « carte de crédit vers Bitcoin » suivants, qui ne prennent que quelques minutes :"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Remplissez les champs suivants dans le formulaire :"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Montant BTC / Bitcoin :"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Veuillez utiliser ce <span %(underline)s>montant exact</span>. Votre coût total pourrait être plus élevé en raison des frais de carte de crédit. Pour les petits montants, cela peut malheureusement dépasser notre remise."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Adresse BTC / Bitcoin (portefeuille externe) :"

msgid "page.donation.crypto_instructions"
msgstr "Instructions pour %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Nous ne proposons pour l'instant que la version standard des cryptomonnaies et aucun réseau marginal de crypto. Une transaction peut nécessiter une heure avant d'être confirmée, selon la cryptomonnaie utilisée."

msgid "page.donation.crypto_qr_code_title"
msgstr "Analyser le code QR à payer"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scannez ce code QR avec votre application Crypto Wallet pour remplir rapidement les détails de paiement"

msgid "page.donation.amazon.header"
msgstr "Carte-cadeau Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Veuillez utiliser le <a %(a_form)s>formulaire officiel Amazon.com</a> pour nous envoyer une carte-cadeau de %(amount)s à l'adresse ci-dessous."

msgid "page.donation.amazon.only_official"
msgstr "Nous ne pouvons accepter d'autres moyens d'envoi de carte-cadeau, <strong>envoyez-les exclusivement via le formulaire officiel sur Amazon.com</strong>. Nous ne pourrons pas vous renvoyer votre carte-cadeau si vous ne l'utilisez pas."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Entrez le montant exact : %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Veuillez NE PAS écrire votre propre message."

msgid "page.donation.amazon.form_to"
msgstr "\"À\" récipiendaire de l'email dans le formulaire :"

msgid "page.donation.amazon.unique"
msgstr "Unique à votre compte, ne le divulguez pas."

msgid "page.donation.amazon.only_use_once"
msgstr "À utiliser une seule fois."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "En attente de la carte-cadeau… (rafraîchissez la page pour vérifier)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Après avoir envoyé votre carte-cadeau, notre système automatisé confirmera sa réception en quelques minutes. Si ça ne marche pas, essayez de la renvoyer (<a %(a_instr)s>instructions</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Si ça ne marche toujours pas, veuillez nous envoyer un email et Anna fera une vérification manuelle (ce qui peut prendre plusieurs jours), et prenez bien soin de préciser si vous avez déjà tenté de la renvoyer."

msgid "page.donation.amazon.example"
msgstr "Exemple :"

msgid "page.donate.strange_account"
msgstr "Notez que le nom ou la photo du compte peut vous sembler étrange. Ne vous inquiétez pas ! Ces comptes sont gérés par nos partenaires de dons. Nos comptes n'ont pas été piratés."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instructions pour Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Faites un don avec Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Faites un don du montant total de %(total)s en utilisant <a %(a_account)s>ce compte Alipay</a>"

msgid "page.donation.page_blocked"
msgstr "Si la page de don ne fonctionne pas, essayez une autre connexion internet (par exemple, VPN ou internet mobile)."

msgid "page.donation.payment.alipay.error"
msgstr "Malheureusement, la page Alipay est souvent accessible uniquement depuis <strong>la Chine continentale</strong>. Vous devrez peut-être désactiver temporairement votre VPN, ou utiliser un VPN vers la Chine continentale (ou Hong Kong, qui fonctionne parfois)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faire un don (scanner le code QR ou appuyer sur le bouton)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Ouvrir la <a %(a_href)s>page de don par QR code</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scannez le code QR avec l'application Alipay, ou appuyez sur le bouton pour ouvrir l'application Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Merci de patienter ; la page peut prendre un certain temps à se charger car elle est en Chine."

msgid "page.donation.payment.wechat.top_header"
msgstr "Instructions WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Faites un don sur WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Faites un don du montant total de %(total)s en utilisant <a %(a_account)s>ce compte WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instructions pour Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Faites un don avec Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Faites un don total de %(total)s avec <a %(a_account)s>ce compte Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Envoyez-nous le reçu par email"

msgid "page.donation.footer.verification"
msgstr "Envoyez un reçu ou une capture d'écran à votre adresse de vérification personnelle. N'UTILISEZ PAS cette adresse email pour votre don via PayPal."

msgid "page.donation.footer.text1"
msgstr "Envoyez un reçu ou une capture d'écran à votre adresse de vérification personnelle :"

msgid "page.donation.footer.crypto_note"
msgstr "Si le taux d'échange de la crypto-monnaie a fluctué durant la transaction, veillez à inclure le reçu contenant le taux initial d'échange. Nous vous remercions de faire l'effort d'utiliser la crypto-monnaie, cela nous aide beaucoup !"

msgid "page.donation.footer.text2"
msgstr "Quand vous nous aurez envoyé votre reçu par email, cliquez sur ce bouton, afin qu'Anna puisse le vérifier manuellement (cela peut prendre quelques jours) :"

msgid "page.donation.footer.button"
msgstr "Oui, j'ai envoyé mon reçu par email"

msgid "page.donation.footer.success"
msgstr "✅ Merci pour votre don ! Anna activera manuellement votre adhésion dans quelques jours."

msgid "page.donation.footer.failure"
msgstr "❌ Une erreur est survenue. Veuillez rafraîchir la page et essayer à nouveau."

msgid "page.donation.stepbystep"
msgstr "Guide pas-à-pas"

msgid "page.donation.crypto_dont_worry"
msgstr "Certaines des étapes font mention de \"crypto wallet\" ou portefeuille de cryptomonnaies, mais pas d'inquiétude, vous n'avez besoin d'aucune connaissance en cryptomonnaies pour continuer."

msgid "page.donation.hoodpay.step1"
msgstr "1. Renseignez votre adresse mail."

msgid "page.donation.hoodpay.step2"
msgstr "2. Sélectionnez votre moyen de paiement."

msgid "page.donation.hoodpay.step3"
msgstr "3. Sélectionnez à nouveau votre moyen de paiement."

msgid "page.donation.hoodpay.step4"
msgstr "4. Choisissez un portefeuille \"Self-hosted\" (\"auto-hébergé\")."

msgid "page.donation.hoodpay.step5"
msgstr "5. Cliquez sur \"I confirm ownership\" (\"Je confirme son appartenance\")."

msgid "page.donation.hoodpay.step6"
msgstr "6. Vous devriez recevoir un reçu par mail. Veuillez nous le transmettre, et nous confirmerons votre don aussi vite que possible."

msgid "page.donate.wait_new"
msgstr "Merci d'attendre au moins <span %(span_hours)s>24 heures</span> (puis d'actualiser cette page) avant de nous contacter."

msgid "page.donate.mistake"
msgstr "Si vous avez fait une erreur au moment du paiement, nous ne pourrons pas vous rembourser, mais nous essaierons de trouver un arrangement."

msgid "page.my_donations.title"
msgstr "Mes dons"

msgid "page.my_donations.not_shown"
msgstr "Les détails des dons ne sont pas affichés publiquement."

msgid "page.my_donations.no_donations"
msgstr "Aucun don pour le moment. <a %(a_donate)s>Faire mon premier don.</a>"

msgid "page.my_donations.make_another"
msgstr "Faire un autre don."

msgid "page.downloaded.title"
msgstr "Fichiers téléchargés"

msgid "page.downloaded.fast_partner_star"
msgstr "Les téléchargements depuis les Serveurs Partenaire Rapides sont indiqués par un %(icon)s."

msgid "page.downloaded.twice"
msgstr "Si vous téléchargez un même fichier avec un téléchargement rapide et un téléchargement lent, il apparaîtra deux fois."

msgid "page.downloaded.fast_download_time"
msgstr "Les téléchargements rapides des dernières 24 heures comptent dans votre limite journalière."

msgid "page.downloaded.times_utc"
msgstr "Toutes les heures sont indiquées au format UTC."

msgid "page.downloaded.not_public"
msgstr "Les fichiers téléchargés ne sont pas affichés publiquement."

msgid "page.downloaded.no_files"
msgstr "Aucun fichier téléchargé pour le moment."

msgid "page.downloaded.last_18_hours"
msgstr "Dernières 18 heures"

msgid "page.downloaded.earlier"
msgstr "Plus tôt"

msgid "page.account.logged_in.title"
msgstr "Compte"

msgid "page.account.logged_out.title"
msgstr "Se connecter / S'inscrire"

msgid "page.account.logged_in.account_id"
msgstr "Identifiant de compte (Account ID) : %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Profil public : %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clé secrète (ne pas divulguer !) : %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "révéler"

msgid "page.account.logged_in.membership_has_some"
msgstr "Adhésion : <strong>%(tier_name)s</strong> jusqu'au%(until_date)s <a %(a_extend)s>(prolonger)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Adhésion : <strong>Aucune</strong> <a %(a_become)s>(devenir membre)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Téléchargements rapides utilisés (dernières 24 heures) : <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "quels téléchargements ?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Groupe Telegram exclusif : %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Rejoignez-nous ici !"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Adhérez à un <a %(a_tier)s>rang supérieur</a> pour rejoindre notre groupe."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Contactez Anna à l'adresse %(email)s si vous souhaitez souscrire à une adhésion de rang supérieur."

msgid "page.contact.title"
msgstr "Adresse mail de contact"

msgid "page.account.logged_in.membership_multiple"
msgstr "Vous pouvez combiner plusieurs adhésions (les téléchargements rapides par 24 heures seront additionnés)."

msgid "layout.index.header.nav.public_profile"
msgstr "Profil public"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Fichiers téléchargés"

msgid "layout.index.header.nav.my_donations"
msgstr "Mes dons"

msgid "page.account.logged_in.logout.button"
msgstr "Se déconnecter"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Vous êtes désormais déconnecté. Rafraîchissez la page pour vous connecter à nouveau."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Une erreur est survenue. Veuillez rafraîchir la page et essayer à nouveau."

msgid "page.account.logged_out.registered.text1"
msgstr "Vous êtes bien inscrit ! Votre clé secrète est : <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Conservez précieusement cette clé. En la perdant, vous perdez également l'accès à votre compte."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Favori/Marque-page.</strong> Vous pouvez ajouter cette page à vos favoris/marques-page afin de conserver votre clé.</li><li %(li_item)s><strong>Téléchargement.</strong> Cliquez sur <a %(a_download)s>ce lien</a> pour télécharger votre clé.</li><li %(li_item)s><strong>Gestionnaire de mots de passe.</strong> Utilisez un gestionnaire de mots de passe pour enregistrer la clé lorsque vous la saisissez ci-dessous.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Entrez votre clé secrète pour vous connecter :"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clé secrète"

msgid "page.account.logged_out.key_form.button"
msgstr "Se connecter"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clé secrète invalide. Vérifiez votre clé et essayez à nouveau, ou créez un nouveau compte ci-dessous."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Ne perdez pas votre clé !"

msgid "page.account.logged_out.register.header"
msgstr "Vous n'avez pas encore de compte ?"

msgid "page.account.logged_out.register.button"
msgstr "Créer un nouveau compte"

msgid "page.login.lost_key"
msgstr "Si vous avez perdu votre clé, merci de <a %(a_contact)s>nous contacter</a> en nous fournissant le plus d'informations possibles."

msgid "page.login.lost_key_contact"
msgstr "Vous aurez peut-être à créer un compte temporaire afin de pouvoir nous contacter."

msgid "page.account.logged_out.old_email.button"
msgstr "Compte enregistré avec un ancien email ? Entrez votre <a %(a_open)s>email ici</a>."

msgid "page.list.title"
msgstr "Liste"

msgid "page.list.header.edit.link"
msgstr "modifier"

msgid "page.list.edit.button"
msgstr "Sauvegarder"

msgid "page.list.edit.success"
msgstr "✅ Sauvegardé. Veuillez recharger la page."

msgid "page.list.edit.failure"
msgstr "❌ Une erreur est survenue. Veuillez réessayer."

msgid "page.list.by_and_date"
msgstr "Liste par %(by)s, créée <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "La liste est vide."

msgid "page.list.new_item"
msgstr "Ajoutez ou retirez des éléments de cette liste en accédant à un fichier et en ouvrant l'onglet \"Listes\"."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Le profil n'a pas été trouvé."

msgid "page.profile.header.edit"
msgstr "modifier"

msgid "page.profile.change_display_name.text"
msgstr "Modifier votre nom public. Votre identifiant (la partie après le “#”) ne peut pas être modifiée."

msgid "page.profile.change_display_name.button"
msgstr "Sauvegarder"

msgid "page.profile.change_display_name.success"
msgstr "✅ Sauvegardé. Veuillez recharger la page."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Une erreur est survenue. Veuillez réessayer."

msgid "page.profile.created_time"
msgstr "Profil créé <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listes"

msgid "page.profile.lists.no_lists"
msgstr "Aucune liste pour le moment"

msgid "page.profile.lists.new_list"
msgstr "Créez une nouvelle liste en accédant à un fichier et en ouvrant l'onglet \"Listes\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "La réforme du droit d'auteur est nécessaire pour la sécurité nationale"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "En résumé : les LLM chinois (y compris DeepSeek) sont entraînés sur mon archive illégale de livres et d'articles — la plus grande au monde. L'Occident doit réviser le droit d'auteur pour des raisons de sécurité nationale."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articles complémentaires par TorrentFreak : <a %(torrentfreak)s>premier</a>, <a %(torrentfreak_2)s>deuxième</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Il n'y a pas si longtemps, les « bibliothèques fantômes » étaient en déclin. Sci-Hub, la vaste archive illégale d'articles académiques, avait cessé d'accepter de nouvelles œuvres en raison de poursuites judiciaires. « Z-Library », la plus grande bibliothèque illégale de livres, a vu ses créateurs présumés arrêtés pour des accusations criminelles de violation du droit d'auteur. Ils ont incroyablement réussi à échapper à leur arrestation, mais leur bibliothèque n'en est pas moins menacée."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Lorsque Z-Library a été menacée de fermeture, j'avais déjà sauvegardé toute sa bibliothèque et cherchais une plateforme pour l'héberger. C'était ma motivation pour lancer l'Archive d'Anna : une continuation de la mission derrière ces initiatives précédentes. Depuis, nous sommes devenus la plus grande bibliothèque fantôme au monde, hébergeant plus de 140 millions de textes protégés par le droit d'auteur dans de nombreux formats — livres, articles académiques, magazines, journaux, et au-delà."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mon équipe et moi sommes des idéologues. Nous croyons que préserver et héberger ces fichiers est moralement juste. Les bibliothèques du monde entier voient leurs financements réduits, et nous ne pouvons pas non plus faire confiance au patrimoine de l'humanité aux entreprises."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Puis est venue l'IA. Pratiquement toutes les grandes entreprises construisant des LLM nous ont contactés pour s'entraîner sur nos données. La plupart (mais pas toutes !) des entreprises basées aux États-Unis ont reconsidéré une fois qu'elles ont réalisé la nature illégale de notre travail. En revanche, les entreprises chinoises ont accueilli notre collection avec enthousiasme, apparemment peu préoccupées par sa légalité. Cela est notable étant donné le rôle de la Chine en tant que signataire de presque tous les grands traités internationaux sur le droit d'auteur."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Nous avons donné un accès à haute vitesse à environ 30 entreprises. La plupart d'entre elles sont des entreprises de LLM, et certaines sont des courtiers en données, qui revendront notre collection. La plupart sont chinoises, bien que nous ayons également travaillé avec des entreprises des États-Unis, d'Europe, de Russie, de Corée du Sud et du Japon. DeepSeek <a %(arxiv)s>a admis</a> qu'une version antérieure a été entraînée sur une partie de notre collection, bien qu'ils restent discrets sur leur dernier modèle (probablement aussi entraîné sur nos données cependant)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si l'Occident veut rester en tête dans la course aux LLM, et finalement à l'AGI, il doit reconsidérer sa position sur le droit d'auteur, et rapidement. Que vous soyez d'accord ou non avec nous sur notre position morale, cela devient maintenant une question d'économie, et même de sécurité nationale. Tous les blocs de pouvoir construisent des super-scientifiques, des super-pirates et des super-militaires artificiels. La liberté de l'information devient une question de survie pour ces pays — même une question de sécurité nationale."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Notre équipe vient du monde entier, et nous n'avons pas d'alignement particulier. Mais nous encouragerions les pays avec des lois sur le droit d'auteur strictes à utiliser cette menace existentielle pour les réformer. Alors, que faire ?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Notre première recommandation est simple : raccourcir la durée du droit d'auteur. Aux États-Unis, le droit d'auteur est accordé pour 70 ans après la mort de l'auteur. C'est absurde. Nous pouvons aligner cela sur les brevets, qui sont accordés pour 20 ans après le dépôt. Cela devrait être plus que suffisant pour que les auteurs de livres, d'articles, de musique, d'art et d'autres œuvres créatives soient pleinement compensés pour leurs efforts (y compris les projets à plus long terme tels que les adaptations cinématographiques)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Ensuite, au minimum, les décideurs politiques devraient inclure des exceptions pour la préservation et la diffusion massive de textes. Si la perte de revenus des clients individuels est la principale préoccupation, la distribution au niveau personnel pourrait rester interdite. En retour, ceux capables de gérer de vastes dépôts — les entreprises formant des LLM, ainsi que les bibliothèques et autres archives — seraient couverts par ces exceptions."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Certains pays font déjà une version de cela. TorrentFreak <a %(torrentfreak)s>a rapporté</a> que la Chine et le Japon ont introduit des exceptions pour l'IA dans leurs lois sur le droit d'auteur. Il n'est pas clair pour nous comment cela interagit avec les traités internationaux, mais cela donne certainement une couverture à leurs entreprises nationales, ce qui explique ce que nous avons observé."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quant à l'Archive d'Anna — nous continuerons notre travail clandestin ancré dans la conviction morale. Pourtant, notre plus grand souhait est d'entrer dans la lumière et d'amplifier notre impact légalement. Veuillez réformer le droit d'auteur."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lisez les articles complémentaires par TorrentFreak : <a %(torrentfreak)s>premier</a>, <a %(torrentfreak_2)s>deuxième</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Gagnants de la récompense de visualisation ISBN de 10 000 $"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "En résumé : Nous avons reçu des soumissions incroyables pour la récompense de visualisation ISBN de 10 000 $."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Il y a quelques mois, nous avons annoncé une <a %(all_isbns)s>récompense de 10 000 $</a> pour réaliser la meilleure visualisation possible de nos données montrant l'espace ISBN. Nous avons insisté sur la démonstration des fichiers que nous avons/n'avons pas encore archivés, et nous avons ensuite ajouté un ensemble de données décrivant combien de bibliothèques détiennent des ISBN (une mesure de rareté)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Nous avons été submergés par la réponse. Il y a eu tant de créativité. Un grand merci à tous ceux qui ont participé : votre énergie et votre enthousiasme sont contagieux !"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "En fin de compte, nous voulions répondre aux questions suivantes : <strong>quels livres existent dans le monde, combien en avons-nous déjà archivés, et sur quels livres devrions-nous nous concentrer ensuite ?</strong> C'est formidable de voir que tant de personnes se soucient de ces questions."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Nous avons commencé par une visualisation de base nous-mêmes. En moins de 300 ko, cette image représente succinctement la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité :"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tous les ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Fichiers dans l'Archive d'Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNOs de CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuite de données de CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSIDs de DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Index des eBooks d'EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Livres"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registre mondial des éditeurs ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Bibliothèque d'État de Russie"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Bibliothèque Impériale de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Retour"

#, fuzzy
msgid "common.forward"
msgstr "Avancer"

#, fuzzy
msgid "common.last"
msgstr "Dernier"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Veuillez consulter le <a %(all_isbns)s>billet de blog original</a> pour plus d'informations."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Nous avons lancé un défi pour améliorer cela. Nous offririons une récompense de 6 000 $ pour la première place, 3 000 $ pour la deuxième place et 1 000 $ pour la troisième place. En raison de la réponse écrasante et des soumissions incroyables, nous avons décidé d'augmenter légèrement le pool de prix et d'attribuer une troisième place à quatre participants, chacun recevant 500 $. Les gagnants sont ci-dessous, mais assurez-vous de consulter toutes les soumissions <a %(annas_archive)s>ici</a>, ou téléchargez notre <a %(a_2025_01_isbn_visualization_files)s>torrent combiné</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Première place 6 000 $ : phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Cette <a %(phiresky_github)s>soumission</a> (<a %(annas_archive_note_2951)s>commentaire Gitlab</a>) est tout simplement tout ce que nous voulions, et plus encore ! Nous avons particulièrement apprécié les options de visualisation incroyablement flexibles (même avec prise en charge des shaders personnalisés), mais avec une liste complète de préréglages. Nous avons également aimé la rapidité et la fluidité de l'ensemble, la mise en œuvre simple (qui n'a même pas de backend), la carte miniature astucieuse et l'explication détaillée dans leur <a %(phiresky_github)s>billet de blog</a>. Un travail incroyable, et un gagnant bien mérité !"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Deuxième place 3 000 $ : hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Une autre <a %(annas_archive_note_2913)s>soumission</a> incroyable. Pas aussi flexible que la première place, mais nous avons en fait préféré sa visualisation au niveau macro par rapport à la première place (courbe de remplissage d'espace, bordures, étiquetage, surlignage, panoramique et zoom). Un <a %(annas_archive_note_2971)s>commentaire</a> de Joe Davis a résonné avec nous :"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "« Bien que les carrés et rectangles parfaits soient mathématiquement plaisants, ils ne fournissent pas une meilleure localité dans un contexte de cartographie. Je crois que l'asymétrie inhérente à ces courbes de Hilbert ou Morton classiques n'est pas un défaut mais une caractéristique. Tout comme le contour en forme de botte de l'Italie la rend instantanément reconnaissable sur une carte, les \"particularités\" uniques de ces courbes peuvent servir de repères cognitifs. Cette distinction peut améliorer la mémoire spatiale et aider les utilisateurs à s'orienter, rendant potentiellement plus facile la localisation de régions spécifiques ou la détection de motifs. »"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Et encore beaucoup d'options pour la visualisation et le rendu, ainsi qu'une interface utilisateur incroyablement fluide et intuitive. Une solide deuxième place !"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Troisième place 500 $ #1 : maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Dans cette <a %(annas_archive_note_2940)s>soumission</a>, nous avons vraiment aimé les différents types de vues, en particulier les vues de comparaison et d'éditeur."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Troisième place 500 $ #2 : abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Bien que l'interface utilisateur ne soit pas la plus soignée, cette <a %(annas_archive_note_2917)s>soumission</a> coche beaucoup de cases. Nous avons particulièrement aimé sa fonctionnalité de comparaison."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Troisième place 500 $ #3 : conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Comme la première place, cette <a %(annas_archive_note_2975)s>soumission</a> nous a impressionnés par sa flexibilité. En fin de compte, c'est ce qui fait un excellent outil de visualisation : une flexibilité maximale pour les utilisateurs avancés, tout en gardant les choses simples pour les utilisateurs moyens."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Troisième place 500 $ #4 : charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "La dernière <a %(annas_archive_note_2947)s>soumission</a> à recevoir une récompense est assez basique, mais possède des fonctionnalités uniques que nous avons vraiment appréciées. Nous avons aimé la façon dont ils montrent combien de datasets couvrent un ISBN particulier comme mesure de popularité/fiabilité. Nous avons également beaucoup aimé la simplicité mais l'efficacité de l'utilisation d'un curseur d'opacité pour les comparaisons."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idées notables"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Quelques autres idées et mises en œuvre que nous avons particulièrement aimées :"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Gratte-ciels pour la rareté"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistiques en direct"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotations, et aussi statistiques en direct"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vue de carte unique et filtres"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schéma de couleurs par défaut cool et carte thermique."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Basculement facile des datasets pour des comparaisons rapides."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Belles étiquettes."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barre d'échelle avec le nombre de livres."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Beaucoup de curseurs pour comparer les datasets, comme si vous étiez un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Nous pourrions continuer encore un moment, mais arrêtons-nous ici. Assurez-vous de regarder toutes les soumissions <a %(annas_archive)s>ici</a>, ou téléchargez notre <a %(a_2025_01_isbn_visualization_files)s>torrent combiné</a>. Tant de soumissions, et chacune apporte une perspective unique, que ce soit dans l'interface utilisateur ou l'implémentation."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Nous intégrerons au moins la soumission de première place dans notre site principal, et peut-être d'autres. Nous avons également commencé à réfléchir à la manière d'organiser le processus d'identification, de confirmation, puis d'archivage des livres les plus rares. Plus d'informations à venir sur ce front."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Merci à tous ceux qui ont participé. C'est incroyable que tant de gens se soucient."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nos cœurs sont remplis de gratitude."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisation de tous les ISBN — Prime de 10 000 $ d'ici le 31-01-2025"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Cette image représente la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Cette image fait 1000×800 pixels. Chaque pixel représente 2 500 ISBN. Si nous avons un fichier pour un ISBN, nous rendons ce pixel plus vert. Si nous savons qu'un ISBN a été émis, mais que nous n'avons pas de fichier correspondant, nous le rendons plus rouge."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En moins de 300 ko, cette image représente succinctement la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité (quelques centaines de Go compressés en entier)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Elle montre également : il reste beaucoup de travail pour sauvegarder les livres (nous n'avons que 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Contexte"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Comment les Archives d’Anna peuvent-elles accomplir leur mission de sauvegarder toutes les connaissances de l’humanité, sans savoir quels livres sont encore disponibles ? Nous avons besoin d’une liste de tâches. Une façon de cartographier cela est à travers les numéros ISBN, qui depuis les années 1970 ont été attribués à chaque livre publié (dans la plupart des pays)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Il n’existe pas d’autorité centrale qui connaît toutes les attributions d’ISBN. Au lieu de cela, c’est un système distribué, où les pays obtiennent des plages de numéros, qui sont ensuite attribuées à de grands éditeurs, qui peuvent à leur tour sous-diviser les plages pour les petits éditeurs. Enfin, des numéros individuels sont attribués aux livres."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Nous avons commencé à cartographier les ISBN <a %(blog)s>il y a deux ans</a> avec notre extraction de ISBNdb. Depuis, nous avons extrait de nombreuses autres sources de metadata, telles que <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, et plus encore. Une liste complète peut être trouvée sur les pages “Datasets” et “Torrents” des Archives d’Anna. Nous avons maintenant de loin la plus grande collection de metadata de livres entièrement ouverte et facilement téléchargeable (et donc d’ISBN) au monde."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Nous avons <a %(blog)s>écrit abondamment</a> sur pourquoi nous nous soucions de la préservation, et pourquoi nous sommes actuellement dans une fenêtre critique. Nous devons maintenant identifier les livres rares, négligés et particulièrement à risque et les préserver. Avoir de bons metadata sur tous les livres du monde aide à cela."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisation"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "En plus de l’image d’ensemble, nous pouvons également examiner les datasets individuels que nous avons acquis. Utilisez le menu déroulant et les boutons pour passer de l’un à l’autre."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Il y a beaucoup de motifs intéressants à voir dans ces images. Pourquoi y a-t-il une certaine régularité des lignes et des blocs, qui semble se produire à différentes échelles ? Quelles sont les zones vides ? Pourquoi certains datasets sont-ils si regroupés ? Nous laisserons ces questions comme un exercice pour le lecteur."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Prime de 10 000 $"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Il y a beaucoup à explorer ici, nous annonçons donc une prime pour améliorer la visualisation ci-dessus. Contrairement à la plupart de nos primes, celle-ci est limitée dans le temps. Vous devez <a %(annas_archive)s>soumettre</a> votre code open source avant le 31-01-2025 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La meilleure soumission recevra 6 000 $, la deuxième place 3 000 $, et la troisième place 1 000 $. Toutes les primes seront attribuées en utilisant Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Ci-dessous se trouvent les critères minimaux. Si aucune soumission ne répond aux critères, nous pourrions tout de même attribuer certaines primes, mais cela sera à notre discrétion."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forkez ce dépôt, et éditez ce post de blog HTML (aucun autre backend en dehors de notre backend Flask n’est autorisé)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Rendez l’image ci-dessus zoomable en douceur, afin que vous puissiez zoomer jusqu’aux ISBN individuels. Cliquer sur les ISBN devrait vous amener à une page de metadata ou à une recherche sur les Archives d’Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Vous devez toujours pouvoir passer entre tous les différents datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Les plages de pays et les plages d’éditeurs doivent être mises en évidence au survol. Vous pouvez utiliser par exemple <a %(github_xlcnd_isbnlib)s>data4info.py dans isbnlib</a> pour les informations sur les pays, et notre extraction “isbngrp” pour les éditeurs (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Cela doit bien fonctionner sur ordinateur de bureau et mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pour des points bonus (ce ne sont que des idées — laissez libre cours à votre créativité) :"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Une forte considération sera accordée à l’utilisabilité et à l’esthétique."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Afficher les metadata réels pour les ISBN individuels lors du zoom, tels que le titre et l’auteur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Meilleure courbe de remplissage d’espace. Par exemple, un zigzag, allant de 0 à 4 sur la première ligne puis revenant (en sens inverse) de 5 à 9 sur la deuxième ligne — appliqué de manière récursive."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Différents schémas de couleurs personnalisables."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vues spéciales pour comparer les datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Moyens de déboguer les problèmes, tels que d'autres metadata qui ne concordent pas bien (par exemple, des titres très différents)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annoter des images avec des commentaires sur les ISBN ou les plages."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Tout heuristique pour identifier les livres rares ou en danger."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Toutes les idées créatives que vous pouvez proposer !"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Vous POUVEZ vous écarter complètement des critères minimaux et réaliser une visualisation complètement différente. Si elle est vraiment spectaculaire, elle peut être éligible pour la récompense, mais à notre discrétion."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faites vos soumissions en postant un commentaire sur <a %(annas_archive)s>ce problème</a> avec un lien vers votre dépôt forké, demande de fusion ou diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Le code pour générer ces images, ainsi que d'autres exemples, se trouve dans <a %(annas_archive)s>ce répertoire</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Nous avons conçu un format de données compact, avec lequel toutes les informations ISBN requises représentent environ 75 Mo (compressé). La description du format de données et le code pour le générer se trouvent <a %(annas_archive_l1244_1319)s>ici</a>. Pour la récompense, vous n'êtes pas obligé d'utiliser cela, mais c'est probablement le format le plus pratique pour commencer. Vous pouvez transformer notre metadata comme vous le souhaitez (bien que tout votre code doive être open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nous avons hâte de voir ce que vous allez proposer. Bonne chance !"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Conteneurs des Archives d’Anna (CAA) : standardiser les publications de la plus grande bibliothèque fantôme du monde"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Les Archives d’Anna sont devenues la plus grande bibliothèque fantôme du monde, nous obligeant à standardiser nos publications."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Les Archives d’Anna</a> sont devenues de loin la plus grande bibliothèque fantôme du monde, et la seule bibliothèque fantôme de cette envergure qui soit entièrement open-source et open-data. Ci-dessous se trouve un tableau de notre page Datasets (légèrement modifié) :"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Nous avons accompli cela de trois manières :"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Refléter les bibliothèques fantômes open-data existantes (comme Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Aider les bibliothèques fantômes qui souhaitent être plus ouvertes, mais qui n'avaient pas le temps ou les ressources pour le faire (comme la collection de bandes dessinées de Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Extraire les bibliothèques qui ne souhaitent pas partager en masse (comme Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Pour (2) et (3), nous gérons désormais nous-mêmes une collection considérable de torrents (des centaines de To). Jusqu'à présent, nous avons abordé ces collections comme des cas uniques, ce qui signifie une infrastructure et une organisation des données sur mesure pour chaque collection. Cela ajoute une surcharge significative à chaque publication et rend particulièrement difficile la réalisation de publications plus incrémentales."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "C'est pourquoi nous avons décidé de standardiser nos publications. Ceci est un article technique dans lequel nous introduisons notre standard : <strong>Les Conteneurs d'Anna’s Archive</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Objectifs de conception"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Notre cas d'utilisation principal est la distribution de fichiers et de metadata associés provenant de différentes collections existantes. Nos considérations les plus importantes sont :"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Fichiers et metadata hétérogènes, dans un format aussi proche que possible de l'original."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identifiants hétérogènes dans les bibliothèques sources, voire absence d'identifiants."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Publications séparées de metadata par rapport aux données de fichiers, ou publications uniquement de metadata (par exemple, notre publication ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribution via torrents, bien que d'autres méthodes de distribution soient possibles (par exemple, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Enregistrements immuables, car nous devons supposer que nos torrents vivront éternellement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Publications incrémentales / publications ajoutables."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Lisible et inscriptible par machine, de manière pratique et rapide, surtout pour notre pile (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspection humaine relativement facile, bien que cela soit secondaire par rapport à la lisibilité par machine."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile à semer nos collections avec un seedbox standard loué."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Les données binaires peuvent être servies directement par des serveurs web comme Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Quelques non-objectifs :"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nous ne nous soucions pas que les fichiers soient faciles à naviguer manuellement sur le disque, ou recherchables sans prétraitement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nous ne nous soucions pas d'être directement compatibles avec les logiciels de bibliothèque existants."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Bien qu'il devrait être facile pour quiconque de semer notre collection en utilisant des torrents, nous ne nous attendons pas à ce que les fichiers soient utilisables sans connaissances techniques significatives et engagement."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Puisque Anna’s Archive est open source, nous voulons utiliser directement notre format. Lorsque nous actualisons notre index de recherche, nous n'accédons qu'aux chemins disponibles publiquement, afin que quiconque qui bifurque notre bibliothèque puisse démarrer rapidement."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Le standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Finalement, nous avons opté pour une norme relativement simple. Elle est assez souple, non normative, et en cours d'élaboration."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Conteneur des Archives d'Anna) est un élément unique composé de <strong>metadata</strong>, et éventuellement de <strong>données binaires</strong>, qui sont toutes deux immuables. Il possède un identifiant unique global, appelé <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collection.</strong> Chaque AAC appartient à une collection, qui par définition est une liste d'AACs sémantiquement cohérente. Cela signifie que si vous apportez un changement significatif au format des metadata, vous devez créer une nouvelle collection."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Collections de « archives » et de « fichiers ».</strong> Par convention, il est souvent pratique de publier les « archives » et les « fichiers » comme des collections différentes, afin qu'ils puissent être publiés à des moments différents, par exemple en fonction des taux de collecte. Une « archive » est une collection contenant uniquement des metadata, avec des informations comme les titres de livres, les auteurs, les ISBN, etc., tandis que les « fichiers » sont les collections qui contiennent les fichiers eux-mêmes (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Le format de l'AACID est le suivant : <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Par exemple, un AACID réel que nous avons publié est <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code> : le nom de la collection, qui peut contenir des lettres ASCII, des chiffres et des underscores (mais pas de double underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code> : une version courte de l'ISO 8601, toujours en UTC, par exemple <code>20220723T194746Z</code>. Ce nombre doit augmenter de manière monotone à chaque publication, bien que sa signification exacte puisse différer selon la collection. Nous suggérons d'utiliser le moment de la collecte ou de la génération de l'ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code> : un identifiant spécifique à la collection, si applicable, par exemple l'ID de Z-Library. Peut être omis ou tronqué. Doit être omis ou tronqué si l'AACID dépasse autrement 150 caractères."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code> : un UUID mais compressé en ASCII, par exemple en utilisant base57. Nous utilisons actuellement la bibliothèque Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Plage d'AACID.</strong> Étant donné que les AACIDs contiennent des timestamps qui augmentent de manière monotone, nous pouvons les utiliser pour indiquer des plages au sein d'une collection particulière. Nous utilisons ce format : <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, où les timestamps sont inclusifs. Cela est cohérent avec la notation ISO 8601. Les plages sont continues et peuvent se chevaucher, mais en cas de chevauchement, elles doivent contenir des archives identiques à celles précédemment publiées dans cette collection (puisque les AACs sont immuables). Les archives manquantes ne sont pas autorisées."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Fichier de metadata.</strong> Un fichier de metadata contient les metadata d'une plage d'AACs, pour une collection particulière. Ceux-ci ont les propriétés suivantes :"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Le nom de fichier doit être une plage d'AACID, précédée de <code style=\"color: red\">annas_archive_meta__</code> et suivie de <code>.jsonl.zstd</code>. Par exemple, l'une de nos publications s'appelle<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Comme indiqué par l'extension de fichier, le type de fichier est <a %(jsonlines)s>JSON Lines</a> compressé avec <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Chaque objet JSON doit contenir les champs suivants au niveau supérieur : <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optionnel). Aucun autre champ n'est autorisé."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> est un metadata arbitraire, selon la sémantique de la collection. Il doit être sémantiquement cohérent au sein de la collection."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> est optionnel, et est le nom du dossier de données binaires qui contient les données binaires correspondantes. Le nom de fichier des données binaires correspondantes dans ce dossier est l'AACID de l'archive."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Le préfixe <code style=\"color: red\">annas_archive_meta__</code> peut être adapté au nom de votre institution, par exemple <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Dossier de données binaires.</strong> Un dossier contenant les données binaires d'une plage d'AACs, pour une collection particulière. Ceux-ci ont les propriétés suivantes :"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Le nom du répertoire doit être une plage d'AACID, précédée de <code style=\"color: green\">annas_archive_data__</code>, et sans suffixe. Par exemple, l'une de nos publications réelles a un répertoire appelé<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Le répertoire doit contenir des fichiers de données pour tous les AACs dans la plage spécifiée. Chaque fichier de données doit avoir son AACID comme nom de fichier (sans extensions)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Il est recommandé de rendre ces dossiers relativement gérables en taille, par exemple, ne dépassant pas 100 Go à 1 To chacun, bien que cette recommandation puisse évoluer avec le temps."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Les fichiers de metadata et les dossiers de données binaires peuvent être regroupés dans des torrents, avec un torrent par fichier de metadata, ou un torrent par dossier de données binaires. Les torrents doivent avoir le nom de fichier/répertoire original plus un suffixe <code>.torrent</code> comme nom de fichier."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemple"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Prenons notre récente publication de Z-Library comme exemple. Elle se compose de deux collections : “<span style=\"background: #fffaa3\">zlib3_records</span>” et “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Cela nous permet de récupérer et de publier séparément les enregistrements de metadata des fichiers de livres réels. Ainsi, nous avons publié deux torrents avec des fichiers de metadata :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Nous avons également publié une série de torrents avec des dossiers de données binaires, mais uniquement pour la collection “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 au total :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "En exécutant <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, nous pouvons voir ce qu’il y a à l’intérieur :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Dans ce cas, il s’agit de la metadata d’un livre tel que rapporté par Z-Library. Au niveau supérieur, nous avons seulement “aacid” et “metadata”, mais pas de “data_folder”, car il n’y a pas de données binaires correspondantes. L’AACID contient “22430000” comme identifiant principal, que nous pouvons voir est pris de “zlibrary_id”. Nous pouvons nous attendre à ce que d’autres AAC dans cette collection aient la même structure."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Maintenant, exécutons <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> :"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Il s’agit d’une metadata AAC beaucoup plus petite, bien que l’essentiel de cet AAC soit situé ailleurs dans un fichier binaire ! Après tout, nous avons cette fois un “data_folder”, donc nous pouvons nous attendre à ce que les données binaires correspondantes se trouvent à <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Le “metadata” contient le “zlibrary_id”, donc nous pouvons facilement l’associer à l’AAC correspondant dans la collection “zlib_records”. Nous aurions pu l’associer de plusieurs manières différentes, par exemple via AACID — la norme ne le prescrit pas."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Notez qu’il n’est pas non plus nécessaire que le champ “metadata” soit lui-même en JSON. Il pourrait s’agir d’une chaîne contenant du XML ou tout autre format de données. Vous pourriez même stocker des informations de metadata dans le blob binaire associé, par exemple si c’est beaucoup de données."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusion"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Avec cette norme, nous pouvons effectuer des publications de manière plus incrémentale et ajouter plus facilement de nouvelles sources de données. Nous avons déjà quelques publications passionnantes en préparation !"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Nous espérons également qu’il deviendra plus facile pour d’autres bibliothèques fantômes de mirrorer nos collections. Après tout, notre objectif est de préserver le savoir et la culture humains pour toujours, donc plus il y a de redondance, mieux c’est."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Mise à jour d’Anna : archive entièrement open source, ElasticSearch, plus de 300 Go de couvertures de livres"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Nous avons travaillé d’arrache-pied pour fournir une bonne alternative avec l’Archive d’Anna. Voici quelques-unes des choses que nous avons récemment accomplies."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Avec la fermeture de Z-Library et l’arrestation (présumée) de ses fondateurs, nous avons travaillé d’arrache-pied pour fournir une bonne alternative avec l’Archive d’Anna (nous ne la lierons pas ici, mais vous pouvez la rechercher sur Google). Voici quelques-unes des choses que nous avons récemment accomplies."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L’Archive d’Anna est entièrement open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Nous croyons que l’information doit être libre, et notre propre code ne fait pas exception. Nous avons publié tout notre code sur notre instance Gitlab hébergée en privé : <a %(annas_archive)s>Logiciel d’Anna</a>. Nous utilisons également le gestionnaire de problèmes pour organiser notre travail. Si vous souhaitez vous engager dans notre développement, c’est un excellent point de départ."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Pour vous donner un aperçu des choses sur lesquelles nous travaillons, prenez notre récent travail sur les améliorations de performance côté client. Comme nous n’avons pas encore implémenté la pagination, nous retournions souvent des pages de recherche très longues, avec 100 à 200 résultats. Nous ne voulions pas couper les résultats de recherche trop tôt, mais cela signifiait que cela ralentissait certains appareils. Pour cela, nous avons mis en place une petite astuce : nous avons enveloppé la plupart des résultats de recherche dans des commentaires HTML (<code><!-- --></code>), puis écrit un petit Javascript qui détecterait quand un résultat devrait devenir visible, moment auquel nous déballerions le commentaire :"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "La \"virtualisation\" du DOM implémentée en 23 lignes, pas besoin de bibliothèques sophistiquées ! C'est le genre de code pragmatique rapide que l'on obtient lorsque l'on dispose de peu de temps et de problèmes réels à résoudre. Il a été rapporté que notre recherche fonctionne désormais bien sur les appareils lents !"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un autre grand effort a été d'automatiser la construction de la base de données. Lors de notre lancement, nous avons simplement rassemblé différentes sources de manière désordonnée. Maintenant, nous voulons les garder à jour, alors nous avons écrit un ensemble de scripts pour télécharger de nouveaux metadata à partir des deux forks de Library Genesis, et les intégrer. L'objectif est non seulement de rendre cela utile pour notre archive, mais aussi de faciliter les choses pour quiconque souhaite explorer les metadata de la bibliothèque fantôme. L'objectif serait un notebook Jupyter contenant toutes sortes de metadata intéressants, afin que nous puissions faire plus de recherches comme déterminer quel <a %(blog)s>pourcentage d'ISBNs est préservé à jamais</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Enfin, nous avons remanié notre système de dons. Vous pouvez désormais utiliser une carte de crédit pour déposer directement de l'argent dans nos portefeuilles crypto, sans vraiment avoir besoin de connaître quoi que ce soit sur les cryptomonnaies. Nous continuerons à surveiller l'efficacité de ce système en pratique, mais c'est un grand pas en avant."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Passer à ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Un de nos <a %(annas_archive)s>tickets</a> était un fourre-tout de problèmes avec notre système de recherche. Nous utilisions la recherche en texte intégral de MySQL, puisque nous avions toutes nos données dans MySQL de toute façon. Mais cela avait ses limites :"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Certaines requêtes prenaient énormément de temps, au point de monopoliser toutes les connexions ouvertes."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Par défaut, MySQL a une longueur minimale de mot, ou votre index peut devenir vraiment grand. Les gens ont signalé ne pas pouvoir rechercher \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La recherche n'était rapide que lorsqu'elle était entièrement chargée en mémoire, ce qui nous obligeait à obtenir une machine plus coûteuse pour l'exécuter, plus quelques commandes pour précharger l'index au démarrage."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nous n'aurions pas pu l'étendre facilement pour créer de nouvelles fonctionnalités, comme une meilleure <a %(wikipedia_cjk_characters)s>tokenisation pour les langues sans espaces</a>, le filtrage/facettage, le tri, les suggestions \"vouliez-vous dire\", l'autocomplétion, etc."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Après avoir discuté avec plusieurs experts, nous avons opté pour ElasticSearch. Ce n'est pas parfait (leurs suggestions \"vouliez-vous dire\" par défaut et les fonctionnalités d'autocomplétion sont médiocres), mais dans l'ensemble, c'est bien mieux que MySQL pour la recherche. Nous ne sommes toujours pas <a %(youtube)s>très enthousiastes</a> à l'idée de l'utiliser pour des données critiques (bien qu'ils aient fait beaucoup de <a %(elastic_co)s>progrès</a>), mais dans l'ensemble, nous sommes assez satisfaits du changement."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Pour l'instant, nous avons mis en place une recherche beaucoup plus rapide, un meilleur support linguistique, un meilleur tri par pertinence, différentes options de tri, et le filtrage par langue/type de livre/type de fichier. Si vous êtes curieux de savoir comment cela fonctionne, <a %(annas_archive_l140)s>jetez</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>coup d'œil</a>. C'est assez accessible, bien qu'il pourrait y avoir plus de commentaires…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Plus de 300 Go de couvertures de livres publiées"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Enfin, nous sommes heureux d'annoncer une petite publication. En collaboration avec les personnes qui gèrent le fork Libgen.rs, nous partageons toutes leurs couvertures de livres via des torrents et IPFS. Cela répartira la charge de visualisation des couvertures entre plus de machines et les préservera mieux. Dans de nombreux cas (mais pas tous), les couvertures de livres sont incluses dans les fichiers eux-mêmes, donc c'est une sorte de \"données dérivées\". Mais les avoir dans IPFS est toujours très utile pour le fonctionnement quotidien à la fois de l'Archive d'Anna et des différents forks de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Comme d'habitude, vous pouvez trouver cette publication sur le Pirate Library Mirror (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>). Nous ne mettrons pas de lien ici, mais vous pouvez facilement le trouver."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Nous espérons pouvoir ralentir un peu notre rythme, maintenant que nous avons une alternative décente à Z-Library. Cette charge de travail n'est pas particulièrement durable. Si vous êtes intéressé à aider avec la programmation, les opérations de serveur ou le travail de préservation, n'hésitez pas à nous contacter. Il y a encore beaucoup de <a %(annas_archive)s>travail à faire</a>. Merci pour votre intérêt et votre soutien."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "L'Archive d'Anna a sauvegardé la plus grande bibliothèque fantôme de bandes dessinées au monde (95 To) — vous pouvez aider à la partager"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La plus grande bibliothèque fantôme de bandes dessinées au monde avait un point de défaillance unique... jusqu'à aujourd'hui."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discuter sur Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La plus grande bibliothèque fantôme de bandes dessinées est probablement celle d'un fork particulier de Library Genesis : Libgen.li. L'administrateur unique de ce site a réussi à rassembler une collection de bandes dessinées incroyable de plus de 2 millions de fichiers, totalisant plus de 95 To. Cependant, contrairement à d'autres collections de Library Genesis, celle-ci n'était pas disponible en masse via des torrents. Vous ne pouviez accéder à ces bandes dessinées qu'individuellement via son serveur personnel lent — un point de défaillance unique. Jusqu'à aujourd'hui !"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Dans cet article, nous vous en dirons plus sur cette collection, ainsi que sur notre collecte de fonds pour soutenir davantage ce travail."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon essaie de se perdre dans le monde banal de la bibliothèque…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Forks de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Tout d'abord, un peu de contexte. Vous connaissez peut-être Library Genesis pour leur collection épique de livres. Moins de gens savent que les bénévoles de Library Genesis ont créé d'autres projets, tels qu'une vaste collection de magazines et de documents standards, une sauvegarde complète de Sci-Hub (en collaboration avec la fondatrice de Sci-Hub, Alexandra Elbakyan), et en effet, une immense collection de bandes dessinées."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "À un moment donné, différents opérateurs de miroirs de Library Genesis ont pris des chemins séparés, ce qui a donné lieu à la situation actuelle avec plusieurs \"forks\" différents, portant tous encore le nom de Library Genesis. Le fork Libgen.li possède de manière unique cette collection de bandes dessinées, ainsi qu'une collection de magazines considérable (sur laquelle nous travaillons également)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaboration"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Étant donné sa taille, cette collection figurait depuis longtemps sur notre liste de souhaits, donc après notre succès avec la sauvegarde de Z-Library, nous avons mis le cap sur cette collection. Au début, nous l'avons extraite directement, ce qui a été un véritable défi, car leur serveur n'était pas en très bon état. Nous avons obtenu environ 15 To de cette manière, mais c'était lent."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Heureusement, nous avons réussi à entrer en contact avec l'opérateur de la bibliothèque, qui a accepté de nous envoyer toutes les données directement, ce qui a été beaucoup plus rapide. Il a tout de même fallu plus de six mois pour transférer et traiter toutes les données, et nous avons failli tout perdre à cause d'une corruption de disque, ce qui aurait signifié tout recommencer."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Cette expérience nous a convaincus qu'il est important de diffuser ces données le plus rapidement possible, afin qu'elles puissent être dupliquées largement. Nous ne sommes qu'à un ou deux incidents malheureux de perdre cette collection à jamais !"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La collection"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Aller vite signifie que la collection est un peu désorganisée… Jetons un coup d'œil. Imaginez que nous ayons un système de fichiers (que nous divisons en réalité en torrents) :"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Le premier répertoire, <code>/repository</code>, est la partie la plus structurée de cela. Ce répertoire contient ce qu'on appelle des \"mille répertoires\" : des répertoires contenant chacun un millier de fichiers, numérotés de manière incrémentale dans la base de données. Le répertoire <code>0</code> contient des fichiers avec comic_id 0–999, et ainsi de suite."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "C'est le même schéma que Library Genesis utilise pour ses collections de fiction et de non-fiction. L'idée est que chaque \"mille répertoire\" soit automatiquement transformé en torrent dès qu'il est rempli."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Cependant, l'opérateur de Libgen.li n'a jamais créé de torrents pour cette collection, et donc les mille répertoires sont probablement devenus gênants, et ont laissé place à des \"répertoires non triés\". Ce sont <code>/comics0</code> à <code>/comics4</code>. Ils contiennent tous des structures de répertoires uniques, qui avaient probablement du sens pour collecter les fichiers, mais qui n'ont plus beaucoup de sens pour nous maintenant. Heureusement, le metadata fait toujours référence directement à tous ces fichiers, donc leur organisation de stockage sur disque n'a en fait pas d'importance !"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Le metadata est disponible sous la forme d'une base de données MySQL. Cela peut être téléchargé directement depuis le site Web de Libgen.li, mais nous le rendrons également disponible dans un torrent, aux côtés de notre propre table avec tous les hachages MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Lorsque vous recevez 95 To déversés dans votre cluster de stockage, vous essayez de comprendre ce qu'il y a dedans… Nous avons fait quelques analyses pour voir si nous pouvions réduire un peu la taille, par exemple en supprimant les doublons. Voici quelques-unes de nos découvertes :"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Les doublons sémantiques (différents scans du même livre) peuvent théoriquement être filtrés, mais c'est délicat. En regardant manuellement les bandes dessinées, nous avons trouvé trop de faux positifs."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Il y a quelques doublons purement par MD5, ce qui est relativement gaspilleur, mais les filtrer ne nous donnerait qu'environ 1% in d'économies. À cette échelle, cela représente toujours environ 1 To, mais aussi, à cette échelle, 1 To n'a pas vraiment d'importance. Nous préférons ne pas risquer de détruire accidentellement des données dans ce processus."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Nous avons trouvé un tas de données non liées aux livres, comme des films basés sur des bandes dessinées. Cela semble également gaspilleur, car ils sont déjà largement disponibles par d'autres moyens. Cependant, nous avons réalisé que nous ne pouvions pas simplement filtrer les fichiers de films, car il y a aussi des <em>bandes dessinées interactives</em> qui ont été publiées sur ordinateur, que quelqu'un a enregistrées et sauvegardées sous forme de films."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "En fin de compte, tout ce que nous pourrions supprimer de la collection ne représenterait qu'un faible pourcentage. Puis nous nous sommes rappelés que nous sommes des accumulateurs de données, et que les personnes qui vont faire un site miroir sont aussi des accumulateurs de données, alors, « QU'EST-CE QUE VOUS VOULEZ DIRE PAR SUPPRIMER ?! » :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Nous vous présentons donc la collection complète et non modifiée. C'est beaucoup de données, mais nous espérons que suffisamment de personnes se soucieront de la partager quand même."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Collecte de fonds"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Nous publions ces données en gros morceaux. Le premier torrent est de <code>/comics0</code>, que nous avons mis dans un énorme fichier .tar de 12 To. C'est mieux pour votre disque dur et votre logiciel de torrent qu'une multitude de petits fichiers."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Dans le cadre de cette publication, nous organisons une collecte de fonds. Nous cherchons à lever 20 000 $ pour couvrir les coûts opérationnels et de sous-traitance pour cette collection, ainsi que pour permettre des projets en cours et futurs. Nous avons quelques <em>projets énormes</em> en préparation."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Qui soutiens-je avec mon don ?</em> En bref : nous sauvegardons tout le savoir et la culture de l'humanité, et les rendons facilement accessibles. Tout notre code et nos données sont open source, nous sommes un projet entièrement géré par des bénévoles, et nous avons déjà sauvegardé 125 To de livres (en plus des torrents existants de Libgen et Scihub). En fin de compte, nous construisons un volant d'inertie qui permet et incite les gens à trouver, numériser et sauvegarder tous les livres du monde. Nous écrirons sur notre plan directeur dans un futur article. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si vous faites un don pour une adhésion de 12 mois « Amazing Archivist » (780 $), vous pouvez <strong>« adopter un torrent »</strong>, ce qui signifie que nous mettrons votre nom d'utilisateur ou votre message dans le nom de fichier de l'un des torrents !"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Vous pouvez faire un don en vous rendant sur <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> et en cliquant sur le bouton « Faites un don ». Nous recherchons également plus de bénévoles : ingénieurs logiciels, chercheurs en sécurité, experts marchands anonymes et traducteurs. Vous pouvez également nous soutenir en fournissant des services d'hébergement. Et bien sûr, veuillez partager nos torrents !"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Merci à tous ceux qui nous ont déjà soutenus si généreusement ! Vous faites vraiment une différence."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Voici les torrents publiés jusqu'à présent (nous traitons encore le reste) :"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tous les torrents peuvent être trouvés sur <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> sous « Datasets » (nous ne faisons pas de lien direct là-bas, pour que les liens vers ce blog ne soient pas supprimés de Reddit, Twitter, etc.). De là, suivez le lien vers le site Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Quelles sont les prochaines étapes ?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un tas de torrents sont excellents pour la préservation à long terme, mais pas tellement pour l'accès quotidien. Nous travaillerons avec des partenaires d'hébergement pour mettre toutes ces données en ligne (puisque l'Archive d'Anna n'héberge rien directement). Bien sûr, vous pourrez trouver ces liens de téléchargement sur l'Archive d'Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Nous invitons également tout le monde à faire des choses avec ces données ! Aidez-nous à mieux les analyser, les dédupliquer, les mettre sur IPFS, les remixer, entraîner vos modèles d'IA avec elles, et ainsi de suite. Elles sont toutes à vous, et nous avons hâte de voir ce que vous en ferez."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Enfin, comme dit précédemment, nous avons encore quelques sorties massives à venir (si <em>quelqu'un</em> pouvait <em>accidentellement</em> nous envoyer un dump d'une <em>certaine</em> base de données ACS4, vous savez où nous trouver...), ainsi que la construction du volant d'inertie pour sauvegarder tous les livres du monde."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Alors restez à l'écoute, nous ne faisons que commencer."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nouveaux livres ajoutés au Pirate Library Mirror (+24 To, 3,8 millions de livres)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Dans la version originale du Pirate Library Mirror (EDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>), nous avons créé un site miroir de Z-Library, une grande collection de livres illégale. Pour rappel, voici ce que nous avons écrit dans cet article de blog original :"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library est une bibliothèque populaire (et illégale). Ils ont pris la collection de Library Genesis et l'ont rendue facilement consultable. En plus de cela, ils sont devenus très efficaces pour solliciter de nouvelles contributions de livres, en incitant les utilisateurs contributeurs avec divers avantages. Actuellement, ils ne contribuent pas ces nouveaux livres à Library Genesis. Et contrairement à Library Genesis, ils ne rendent pas leur collection facilement duplicable, ce qui empêche une large préservation. Cela est important pour leur modèle économique, car ils facturent l'accès à leur collection en masse (plus de 10 livres par jour)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nous ne portons pas de jugement moral sur le fait de facturer l'accès en masse à une collection de livres illégale. Il est indéniable que la Z-Library a réussi à élargir l'accès au savoir et à obtenir plus de livres. Nous sommes simplement ici pour faire notre part : assurer la préservation à long terme de cette collection privée."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Cette collection remonte à la mi-2021. Entre-temps, la Z-Library a connu une croissance fulgurante : elle a ajouté environ 3,8 millions de nouveaux livres. Il y a bien sûr quelques doublons, mais la majorité semble être de nouveaux livres légitimes ou des scans de meilleure qualité de livres précédemment soumis. Cela est en grande partie dû à l'augmentation du nombre de modérateurs bénévoles à la Z-Library et à leur système de téléversement en masse avec déduplication. Nous tenons à les féliciter pour ces réalisations."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Nous sommes heureux d'annoncer que nous avons récupéré tous les livres ajoutés à la Z-Library entre notre dernier site miroir et août 2022. Nous avons également récupéré certains livres que nous avions manqués la première fois. En tout, cette nouvelle collection fait environ 24 To, ce qui est bien plus grand que la précédente (7 To). Notre site miroir fait maintenant 31 To au total. Encore une fois, nous avons dédupliqué par rapport à Library Genesis, car il existe déjà des torrents disponibles pour cette collection."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Veuillez vous rendre sur le Pirate Library Mirror pour découvrir la nouvelle collection (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d’Anna</a>). Vous y trouverez plus d'informations sur la structure des fichiers et sur ce qui a changé depuis la dernière fois. Nous ne mettrons pas de lien ici, car il s'agit simplement d'un site de blog qui n'héberge aucun matériel illégal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Bien sûr, le partage est également un excellent moyen de nous aider. Merci à tous ceux qui partagent notre précédent ensemble de torrents. Nous sommes reconnaissants pour la réponse positive et heureux qu'il y ait tant de personnes qui se soucient de la préservation du savoir et de la culture de cette manière inhabituelle."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Comment devenir un archiviste pirate"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Le premier défi pourrait être surprenant. Ce n'est pas un problème technique, ni un problème juridique. C'est un problème psychologique."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Avant de plonger, deux mises à jour sur le Pirate Library Mirror (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d’Anna</a>) :"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Nous avons reçu des dons extrêmement généreux. Le premier était de 10 000 $ de la part d'un individu anonyme qui a également soutenu \"bookwarrior\", le fondateur original de Library Genesis. Un merci spécial à bookwarrior pour avoir facilité ce don. Le second était un autre don de 10 000 $ d'un donateur anonyme, qui a pris contact après notre dernière publication et a été inspiré pour aider. Nous avons également reçu un certain nombre de petits dons. Merci beaucoup pour tout votre soutien généreux. Nous avons des projets passionnants en préparation que cela soutiendra, alors restez à l'écoute."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Nous avons rencontré quelques difficultés techniques avec la taille de notre deuxième publication, mais nos torrents sont maintenant en ligne et partagés. Nous avons également reçu une offre généreuse d'un individu anonyme pour partager notre collection sur leurs serveurs à très haute vitesse, donc nous faisons un téléversement spécial vers leurs machines, après quoi tous ceux qui téléchargent la collection devraient voir une grande amélioration de la vitesse."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Des livres entiers peuvent être écrits sur le <em>pourquoi</em> de la préservation numérique en général, et de l'archivisme pirate en particulier, mais donnons un bref aperçu pour ceux qui ne sont pas trop familiers. Le monde produit plus de savoir et de culture que jamais auparavant, mais plus que jamais, une grande partie est perdue. L'humanité confie en grande partie ce patrimoine à des entreprises comme les éditeurs académiques, les services de streaming et les entreprises de médias sociaux, et elles ne se sont souvent pas révélées être de grands gardiens. Consultez le documentaire Digital Amnesia, ou vraiment n'importe quelle conférence de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Il y a certaines institutions qui font un bon travail d'archivage autant qu'elles le peuvent, mais elles sont liées par la loi. En tant que pirates, nous sommes dans une position unique pour archiver des collections qu'elles ne peuvent pas toucher, en raison de l'application des droits d'auteur ou d'autres restrictions. Nous pouvons également reproduire des collections de nombreuses fois à travers le monde, augmentant ainsi les chances d'une préservation adéquate."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Pour l'instant, nous n'entrerons pas dans les discussions sur les avantages et les inconvénients de la propriété intellectuelle, la moralité de la violation de la loi, les réflexions sur la censure ou la question de l'accès au savoir et à la culture. Avec tout cela de côté, plongeons dans le <em>comment</em>. Nous partagerons comment notre équipe est devenue des archivistes pirates, et les leçons que nous avons apprises en cours de route. Il y a de nombreux défis lorsque vous vous lancez dans ce voyage, et nous espérons pouvoir vous aider à traverser certains d'entre eux."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Communauté"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Le premier défi pourrait être surprenant. Ce n'est pas un problème technique, ni un problème juridique. C'est un problème psychologique : faire ce travail dans l'ombre peut être incroyablement solitaire. Selon ce que vous prévoyez de faire et votre modèle de menace, vous devrez peut-être être très prudent. À une extrémité du spectre, nous avons des personnes comme Alexandra Elbakyan*, la fondatrice de Sci-Hub, qui est très ouverte sur ses activités. Mais elle risque fortement d'être arrêtée si elle visite un pays occidental à ce stade, et pourrait faire face à des décennies de prison. Est-ce un risque que vous seriez prêt à prendre ? Nous sommes à l'autre extrémité du spectre ; étant très prudents pour ne laisser aucune trace et ayant une sécurité opérationnelle forte."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Comme mentionné sur HN par \"ynno\", Alexandra ne voulait initialement pas être connue : \"Ses serveurs étaient configurés pour émettre des messages d'erreur détaillés de PHP, y compris le chemin complet du fichier source en faute, qui était sous le répertoire /home/<USER>'utilisateur qu'elle avait en ligne sur un site non lié, attaché à son vrai nom. Avant cette révélation, elle était anonyme.\" Donc, utilisez des noms d'utilisateur aléatoires sur les ordinateurs que vous utilisez pour ces choses, au cas où vous malconfigureriez quelque chose."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Cette discrétion, cependant, a un coût psychologique. La plupart des gens aiment être reconnus pour le travail qu'ils font, et pourtant vous ne pouvez pas prendre de crédit pour cela dans la vie réelle. Même des choses simples peuvent être difficiles, comme des amis vous demandant ce que vous avez fait (à un moment donné \"jouer avec mon NAS / homelab\" devient lassant)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "C'est pourquoi il est si important de trouver une communauté. Vous pouvez renoncer à une partie de la sécurité opérationnelle en vous confiant à des amis très proches, en qui vous savez que vous pouvez avoir une confiance profonde. Même alors, soyez prudent de ne rien mettre par écrit, au cas où ils devraient remettre leurs e-mails aux autorités, ou si leurs appareils sont compromis d'une autre manière."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Mieux encore est de trouver d'autres pirates. Si vos amis proches sont intéressés à vous rejoindre, tant mieux ! Sinon, vous pourriez être en mesure de trouver d'autres personnes en ligne. Malheureusement, c'est encore une communauté de niche. Jusqu'à présent, nous n'avons trouvé qu'une poignée d'autres personnes actives dans cet espace. Les forums de Library Genesis et r/DataHoarder semblent être de bons points de départ. L'Archive Team compte également des individus partageant les mêmes idées, bien qu'ils opèrent dans le cadre de la loi (même si dans certaines zones grises de la loi). Les scènes traditionnelles de \"warez\" et de piratage comptent également des personnes qui pensent de manière similaire."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Nous sommes ouverts aux idées sur la manière de favoriser la communauté et d'explorer des idées. N'hésitez pas à nous envoyer un message sur Twitter ou Reddit. Peut-être pourrions-nous organiser une sorte de forum ou de groupe de discussion. Un défi est que cela peut facilement être censuré lorsque l'on utilise des plateformes courantes, donc nous devrions l'héberger nous-mêmes. Il y a aussi un compromis entre rendre ces discussions entièrement publiques (plus d'engagement potentiel) et les rendre privées (ne pas laisser les \"cibles\" potentielles savoir que nous sommes sur le point de les scraper). Nous devrons réfléchir à cela. Faites-nous savoir si cela vous intéresse !"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projets"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Lorsque nous réalisons un projet, il comporte plusieurs phases :"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Sélection du domaine / philosophie : Sur quoi souhaitez-vous vous concentrer, et pourquoi ? Quelles sont vos passions, compétences et circonstances uniques que vous pouvez utiliser à votre avantage ?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Sélection de la cible : Quelle collection spécifique allez-vous mirrorer ?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Scraping des metadata : Cataloguer les informations sur les fichiers, sans réellement télécharger les fichiers eux-mêmes (souvent beaucoup plus volumineux)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Sélection des données : Sur la base des metadata, déterminer quelles données sont les plus pertinentes à archiver maintenant. Cela pourrait être tout, mais souvent il y a un moyen raisonnable d'économiser de l'espace et de la bande passante."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Scraping des données : Obtenir réellement les données."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution : Les emballer dans des torrents, les annoncer quelque part, inciter les gens à les diffuser."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ce ne sont pas des phases complètement indépendantes, et souvent des idées d'une phase ultérieure vous renvoient à une phase antérieure. Par exemple, lors du scraping des metadata, vous pourriez réaliser que la cible que vous avez sélectionnée a des mécanismes de défense au-delà de votre niveau de compétence (comme des blocages IP), donc vous revenez en arrière et trouvez une autre cible."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Sélection du domaine / philosophie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Il n'y a pas de pénurie de connaissances et de patrimoine culturel à sauvegarder, ce qui peut être accablant. C'est pourquoi il est souvent utile de prendre un moment pour réfléchir à ce que peut être votre contribution."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Chacun a une façon différente de penser à cela, mais voici quelques questions que vous pourriez vous poser :"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Pourquoi cela vous intéresse-t-il ? Qu'est-ce qui vous passionne ? Si nous pouvons réunir un groupe de personnes qui archivent toutes les choses qui leur tiennent spécifiquement à cœur, cela couvrirait beaucoup ! Vous en saurez beaucoup plus que la personne moyenne sur votre passion, comme quelles sont les données importantes à sauvegarder, quelles sont les meilleures collections et communautés en ligne, etc."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quelles compétences avez-vous que vous pouvez utiliser à votre avantage ? Par exemple, si vous êtes un expert en sécurité en ligne, vous pouvez trouver des moyens de contourner les blocages IP pour des cibles sécurisées. Si vous êtes excellent pour organiser des communautés, alors peut-être pouvez-vous rassembler des gens autour d'un objectif. Il est utile de connaître un peu la programmation, ne serait-ce que pour maintenir une bonne sécurité opérationnelle tout au long de ce processus."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Combien de temps avez-vous pour cela ? Notre conseil serait de commencer petit et de réaliser des projets plus importants au fur et à mesure que vous vous y habituez, mais cela peut devenir très prenant."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Quel serait un domaine à fort effet de levier sur lequel se concentrer ? Si vous allez passer X heures à archiver des contenus pirates, comment pouvez-vous obtenir le meilleur \"retour sur investissement\" ?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quelles sont les façons uniques dont vous pensez à cela ? Vous pourriez avoir des idées ou des approches intéressantes que d'autres auraient pu manquer."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Dans notre cas, nous nous soucions particulièrement de la préservation à long terme de la science. Nous connaissions Library Genesis, et comment il était entièrement mirrorer plusieurs fois via des torrents. Nous aimions cette idée. Puis un jour, l'un de nous a essayé de trouver des manuels scientifiques sur Library Genesis, mais n'a pas pu les trouver, remettant en question sa complétude. Nous avons ensuite cherché ces manuels en ligne, et les avons trouvés ailleurs, ce qui a planté la graine de notre projet. Même avant de connaître la Z-Library, nous avions l'idée de ne pas essayer de collecter tous ces livres manuellement, mais de nous concentrer sur le mirroring des collections existantes, et de les contribuer à Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Sélection de la cible"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Alors, nous avons notre domaine d'intérêt, maintenant quelle collection spécifique devons-nous mirrorer ? Il y a quelques éléments qui font un bon objectif :"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unique : pas déjà bien couverte par d'autres projets."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessible : n'utilise pas des tonnes de couches de protection pour vous empêcher de récupérer leurs metadata et données."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Connaissance spéciale : vous avez des informations particulières sur cet objectif, comme un accès spécial à cette collection, ou vous avez trouvé comment contourner leurs défenses. Ce n'est pas obligatoire (notre projet à venir ne fait rien de spécial), mais cela aide certainement !"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Lorsque nous avons trouvé nos manuels de sciences sur des sites autres que Library Genesis, nous avons essayé de comprendre comment ils avaient fait leur chemin sur Internet. Nous avons ensuite découvert Z-Library, et réalisé que bien que la plupart des livres n'y apparaissent pas en premier, ils finissent par s'y retrouver. Nous avons appris sa relation avec Library Genesis, et la structure d'incitation (financière) et l'interface utilisateur supérieure, qui en faisaient une collection beaucoup plus complète. Nous avons ensuite effectué quelques préliminaires de scraping de metadata et de données, et réalisé que nous pouvions contourner leurs limites de téléchargement IP, en tirant parti de l'accès spécial de l'un de nos membres à de nombreux serveurs proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "En explorant différents objectifs, il est déjà important de cacher vos traces en utilisant des VPN et des adresses e-mail jetables, dont nous parlerons plus tard."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Scraping de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Entrons un peu plus dans les détails techniques. Pour récupérer les metadata des sites web, nous avons gardé les choses assez simples. Nous utilisons des scripts Python, parfois curl, et une base de données MySQL pour stocker les résultats. Nous n'avons pas utilisé de logiciel de scraping sophistiqué capable de cartographier des sites complexes, car jusqu'à présent, nous n'avons eu besoin de récupérer qu'un ou deux types de pages en énumérant simplement les identifiants et en analysant le HTML. S'il n'y a pas de pages facilement énumérables, vous pourriez avoir besoin d'un véritable crawler qui essaie de trouver toutes les pages."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Avant de commencer à récupérer un site entier, essayez de le faire manuellement pendant un moment. Parcourez vous-même quelques dizaines de pages pour comprendre comment cela fonctionne. Parfois, vous rencontrerez déjà des blocages IP ou d'autres comportements intéressants de cette manière. Il en va de même pour le scraping de données : avant de vous plonger trop profondément dans cet objectif, assurez-vous de pouvoir effectivement télécharger ses données."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Pour contourner les restrictions, il y a quelques choses que vous pouvez essayer. Y a-t-il d'autres adresses IP ou serveurs qui hébergent les mêmes données mais n'ont pas les mêmes restrictions ? Y a-t-il des points de terminaison API qui n'ont pas de restrictions, alors que d'autres en ont ? À quel rythme de téléchargement votre IP est-elle bloquée, et pour combien de temps ? Ou n'êtes-vous pas bloqué mais ralenti ? Que se passe-t-il si vous créez un compte utilisateur, comment les choses changent-elles alors ? Pouvez-vous utiliser HTTP/2 pour garder les connexions ouvertes, et cela augmente-t-il le taux auquel vous pouvez demander des pages ? Y a-t-il des pages qui listent plusieurs fichiers à la fois, et les informations listées y sont-elles suffisantes ?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Les éléments que vous voudrez probablement sauvegarder incluent :"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titre"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nom de fichier / emplacement"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID : peut être un ID interne, mais des IDs comme ISBN ou DOI sont également utiles."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Taille : pour calculer l'espace disque dont vous avez besoin."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1) : pour confirmer que vous avez téléchargé le fichier correctement."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Date d'ajout/modification : pour que vous puissiez revenir plus tard et télécharger des fichiers que vous n'avez pas téléchargés auparavant (bien que vous puissiez souvent aussi utiliser l'ID ou le hash pour cela)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Description, catégorie, tags, auteurs, langue, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Nous faisons généralement cela en deux étapes. D'abord, nous téléchargeons les fichiers HTML bruts, généralement directement dans MySQL (pour éviter beaucoup de petits fichiers, dont nous parlerons plus en détail ci-dessous). Ensuite, dans une étape distincte, nous parcourons ces fichiers HTML et les analysons dans de véritables tables MySQL. De cette façon, vous n'avez pas à tout re-télécharger depuis le début si vous découvrez une erreur dans votre code d'analyse, car vous pouvez simplement retraiter les fichiers HTML avec le nouveau code. Il est également souvent plus facile de paralléliser l'étape de traitement, ce qui permet de gagner du temps (et vous pouvez écrire le code de traitement pendant que le scraping est en cours, au lieu d'avoir à écrire les deux étapes en même temps)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Enfin, notez que pour certaines cibles, le scraping des metadata est tout ce qu'il y a. Il existe d'énormes collections de metadata qui ne sont pas correctement préservées."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Sélection des données"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Souvent, vous pouvez utiliser les metadata pour déterminer un sous-ensemble raisonnable de données à télécharger. Même si vous souhaitez finalement télécharger toutes les données, il peut être utile de prioriser les éléments les plus importants en premier, au cas où vous seriez détecté et que les défenses seraient améliorées, ou parce que vous auriez besoin d'acheter plus de disques, ou simplement parce que quelque chose d'autre surviendrait dans votre vie avant que vous ne puissiez tout télécharger."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Par exemple, une collection peut avoir plusieurs éditions de la même ressource sous-jacente (comme un livre ou un film), où l'une est marquée comme étant de la meilleure qualité. Sauvegarder ces éditions en premier serait très judicieux. Vous pourriez éventuellement vouloir sauvegarder toutes les éditions, car dans certains cas, les metadata pourraient être mal étiquetées, ou il pourrait y avoir des compromis inconnus entre les éditions (par exemple, la \"meilleure édition\" pourrait être la meilleure à bien des égards mais pire à d'autres, comme un film ayant une résolution plus élevée mais sans sous-titres)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Vous pouvez également rechercher dans votre base de données de metadata pour trouver des éléments intéressants. Quel est le plus gros fichier hébergé, et pourquoi est-il si gros ? Quel est le plus petit fichier ? Y a-t-il des motifs intéressants ou inattendus en ce qui concerne certaines catégories, langues, etc. ? Y a-t-il des titres en double ou très similaires ? Y a-t-il des motifs quant à la date d'ajout des données, comme un jour où de nombreux fichiers ont été ajoutés en même temps ? Vous pouvez souvent apprendre beaucoup en examinant le jeu de données de différentes manières."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Dans notre cas, nous avons dédupliqué les livres de Z-Library par rapport aux hachages md5 dans Library Genesis, économisant ainsi beaucoup de temps de téléchargement et d'espace disque. C'est une situation assez unique cependant. Dans la plupart des cas, il n'existe pas de bases de données complètes des fichiers déjà correctement préservés par d'autres pirates. Cela représente en soi une énorme opportunité pour quelqu'un. Ce serait formidable d'avoir un aperçu régulièrement mis à jour des éléments comme la musique et les films qui sont déjà largement partagés sur les sites de torrents, et qui sont donc de moindre priorité à inclure dans les sites miroirs pirates."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extraction des données"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Vous êtes maintenant prêt à télécharger réellement les données en masse. Comme mentionné précédemment, à ce stade, vous devriez déjà avoir téléchargé manuellement un tas de fichiers, pour mieux comprendre le comportement et les restrictions de la cible. Cependant, il y aura encore des surprises pour vous une fois que vous commencerez à télécharger beaucoup de fichiers à la fois."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Notre conseil ici est principalement de garder les choses simples. Commencez par télécharger simplement un tas de fichiers. Vous pouvez utiliser Python, puis passer à plusieurs threads. Mais parfois, il est encore plus simple de générer directement des fichiers Bash à partir de la base de données, puis d'en exécuter plusieurs dans plusieurs fenêtres de terminal pour augmenter l'échelle. Un petit truc technique à mentionner ici est l'utilisation de OUTFILE dans MySQL, que vous pouvez écrire n'importe où si vous désactivez \"secure_file_priv\" dans mysqld.cnf (et assurez-vous également de désactiver/contourner AppArmor si vous êtes sur Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Nous stockons les données sur de simples disques durs. Commencez avec ce que vous avez, et développez lentement. Il peut être accablant de penser à stocker des centaines de To de données. Si c'est la situation à laquelle vous faites face, mettez d'abord un bon sous-ensemble, et dans votre annonce, demandez de l'aide pour stocker le reste. Si vous souhaitez obtenir plus de disques durs vous-même, alors r/DataHoarder propose de bonnes ressources pour obtenir de bonnes affaires."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Essayez de ne pas trop vous soucier des systèmes de fichiers sophistiqués. Il est facile de tomber dans le piège de la configuration de choses comme ZFS. Un détail technique à connaître cependant, est que de nombreux systèmes de fichiers ne gèrent pas bien un grand nombre de fichiers. Nous avons trouvé qu'une solution simple consiste à créer plusieurs répertoires, par exemple pour différentes plages d'ID ou préfixes de hachage."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Après avoir téléchargé les données, assurez-vous de vérifier l'intégrité des fichiers en utilisant les hachages dans les metadata, si disponibles."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Vous avez les données, vous donnant ainsi possession du premier site miroir pirate de votre cible (très probablement). À bien des égards, la partie la plus difficile est terminée, mais la partie la plus risquée est encore devant vous. Après tout, jusqu'à présent, vous avez été furtif ; volant sous le radar. Tout ce que vous aviez à faire était d'utiliser un bon VPN tout au long, de ne pas remplir vos informations personnelles dans aucun formulaire (évidemment), et peut-être d'utiliser une session de navigateur spéciale (ou même un ordinateur différent)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Maintenant, vous devez distribuer les données. Dans notre cas, nous voulions d'abord contribuer les livres à Library Genesis, mais nous avons rapidement découvert les difficultés de cela (tri fiction vs non-fiction). Nous avons donc décidé de distribuer en utilisant des torrents de style Library Genesis. Si vous avez l'opportunité de contribuer à un projet existant, cela pourrait vous faire gagner beaucoup de temps. Cependant, il n'existe actuellement pas beaucoup de sites miroirs pirates bien organisés."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Disons donc que vous décidez de distribuer des torrents vous-même. Essayez de garder ces fichiers petits, afin qu'ils soient faciles à reproduire sur d'autres sites web. Vous devrez alors semer les torrents vous-même, tout en restant anonyme. Vous pouvez utiliser un VPN (avec ou sans redirection de port), ou payer avec des Bitcoins mélangés pour un Seedbox. Si vous ne savez pas ce que certains de ces termes signifient, vous aurez beaucoup de lecture à faire, car il est important que vous compreniez les compromis de risque ici."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Vous pouvez héberger les fichiers torrent eux-mêmes sur des sites de torrents existants. Dans notre cas, nous avons choisi d'héberger réellement un site web, car nous voulions également diffuser notre philosophie de manière claire. Vous pouvez le faire vous-même de manière similaire (nous utilisons Njalla pour nos domaines et notre hébergement, payés avec des Bitcoins mélangés), mais n'hésitez pas à nous contacter pour que nous hébergions vos torrents. Nous cherchons à construire un index complet de sites miroirs pirates au fil du temps, si cette idée prend."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Quant à la sélection de VPN, beaucoup a déjà été écrit à ce sujet, nous allons donc simplement répéter le conseil général de choisir par réputation. Des politiques de non-journalisation testées en justice avec de longs antécédents de protection de la vie privée sont l'option la moins risquée, à notre avis. Notez que même lorsque vous faites tout correctement, vous ne pouvez jamais atteindre un risque zéro. Par exemple, lors de la mise en semence de vos torrents, un acteur étatique très motivé peut probablement examiner les flux de données entrants et sortants pour les serveurs VPN, et déduire qui vous êtes. Ou vous pouvez simplement faire une erreur d'une manière ou d'une autre. Nous l'avons probablement déjà fait, et le referons. Heureusement, les États-nations ne se soucient pas <em>tant</em> que ça du piratage."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Une décision à prendre pour chaque projet est de savoir s'il faut le publier en utilisant la même identité qu'auparavant, ou non. Si vous continuez à utiliser le même nom, alors les erreurs de sécurité opérationnelle des projets précédents pourraient revenir vous hanter. Mais publier sous différents noms signifie que vous ne construisez pas une réputation durable. Nous avons choisi d'avoir une sécurité opérationnelle forte dès le départ pour pouvoir continuer à utiliser la même identité, mais nous n'hésiterons pas à publier sous un nom différent si nous faisons une erreur ou si les circonstances l'exigent."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Faire passer le mot peut être délicat. Comme nous l'avons dit, c'est encore une communauté de niche. Nous avons initialement posté sur Reddit, mais avons vraiment gagné en traction sur Hacker News. Pour l'instant, notre recommandation est de le poster à quelques endroits et de voir ce qui se passe. Et encore une fois, contactez-nous. Nous aimerions diffuser la parole de plus d'efforts d'archivage pirate."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Nous espérons que cela sera utile pour les archivistes pirates débutants. Nous sommes ravis de vous accueillir dans ce monde, alors n'hésitez pas à nous contacter. Préservons autant que possible les connaissances et la culture du monde, et diffusons-les largement."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Présentation du Miroir de la Bibliothèque Pirate : Préserver 7 To de livres (qui ne sont pas dans Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Ce projet (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>) vise à contribuer à la préservation et à la libération des connaissances humaines. Nous apportons notre modeste contribution, dans les pas des grands qui nous ont précédés."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "L'objectif de ce projet est illustré par son nom :"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirate</strong> - Nous violons délibérément la loi sur le droit d'auteur dans la plupart des pays. Cela nous permet de faire quelque chose que les entités légales ne peuvent pas faire : s'assurer que les livres sont diffusés largement."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliothèque</strong> - Comme la plupart des bibliothèques, nous nous concentrons principalement sur les documents écrits comme les livres. Nous pourrions nous étendre à d'autres types de médias à l'avenir."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Miroir</strong> - Nous sommes strictement un miroir de bibliothèques existantes. Nous nous concentrons sur la préservation, pas sur la facilité de recherche et de téléchargement des livres (accès) ou sur la création d'une grande communauté de personnes qui contribuent de nouveaux livres (approvisionnement)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La première bibliothèque que nous avons reproduite est Z-Library. C'est une bibliothèque populaire (et illégale). Ils ont pris la collection de Library Genesis et l'ont rendue facilement consultable. De plus, ils sont devenus très efficaces pour solliciter de nouvelles contributions de livres, en incitant les utilisateurs contributeurs avec divers avantages. Actuellement, ils ne contribuent pas ces nouveaux livres à Library Genesis. Et contrairement à Library Genesis, ils ne rendent pas leur collection facilement reproductible, ce qui empêche une large préservation. Cela est important pour leur modèle économique, car ils facturent l'accès à leur collection en masse (plus de 10 livres par jour)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nous ne portons pas de jugement moral sur le fait de facturer l'accès en masse à une collection de livres illégale. Il est indéniable que la Z-Library a réussi à élargir l'accès au savoir et à obtenir plus de livres. Nous sommes simplement ici pour faire notre part : assurer la préservation à long terme de cette collection privée."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Nous vous invitons à aider à préserver et libérer les connaissances humaines en téléchargeant et en partageant nos torrents. Consultez la page du projet pour plus d'informations sur l'organisation des données."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Nous vous invitons également à contribuer avec vos idées sur les collections à reproduire ensuite, et comment procéder. Ensemble, nous pouvons accomplir beaucoup. Ceci n'est qu'une petite contribution parmi tant d'autres. Merci, pour tout ce que vous faites."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nous ne faisons pas de lien vers les fichiers depuis ce blog. Veuillez les trouver vous-même.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump ISBNdb, ou Combien de Livres Sont Préservés Pour Toujours ?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si nous devions correctement dédupliquer les fichiers des bibliothèques fantômes, quel pourcentage de tous les livres du monde avons-nous préservé ?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Avec le Miroir de la Bibliothèque Pirate (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>), notre objectif est de prendre tous les livres du monde et de les préserver pour toujours.<sup>1</sup> Entre nos torrents Z-Library et les torrents originaux de Library Genesis, nous avons 11 783 153 fichiers. Mais combien cela représente-t-il vraiment ? Si nous dédupliquions correctement ces fichiers, quel pourcentage de tous les livres du monde avons-nous préservé ? Nous aimerions vraiment avoir quelque chose comme ça :"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of l'héritage écrit de l'humanité préservé pour toujours"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Pour un pourcentage, nous avons besoin d'un dénominateur : le nombre total de livres jamais publiés.<sup>2</sup> Avant la disparition de Google Books, un ingénieur du projet, Leonid Taycher, <a %(booksearch_blogspot)s>a essayé d'estimer</a> ce nombre. Il est arrivé — avec humour — à 129 864 880 (« au moins jusqu'à dimanche »). Il a estimé ce nombre en construisant une base de données unifiée de tous les livres du monde. Pour cela, il a rassemblé différents ensembles de données et les a ensuite fusionnés de diverses manières."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "En passant, il y a une autre personne qui a tenté de cataloguer tous les livres du monde : Aaron Swartz, le regretté activiste numérique et co-fondateur de Reddit.<sup>3</sup> Il a <a %(youtube)s>lancé Open Library</a> avec pour objectif « une page web pour chaque livre jamais publié », en combinant des données provenant de nombreuses sources différentes. Il a fini par payer le prix ultime pour son travail de préservation numérique lorsqu'il a été poursuivi pour avoir téléchargé en masse des articles académiques, ce qui a conduit à son suicide. Inutile de dire que c'est l'une des raisons pour lesquelles notre groupe est pseudonyme, et pourquoi nous faisons très attention. Open Library est toujours héroïquement géré par des personnes de l'Internet Archive, poursuivant l'héritage d'Aaron. Nous y reviendrons plus tard dans cet article."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Dans le billet de blog de Google, Taycher décrit certains des défis liés à l'estimation de ce nombre. Tout d'abord, qu'est-ce qui constitue un livre ? Il existe quelques définitions possibles :"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copies physiques.</strong> Évidemment, cela n'est pas très utile, car ce ne sont que des duplicatas du même document. Ce serait génial si nous pouvions préserver toutes les annotations que les gens font dans les livres, comme les célèbres « gribouillis dans les marges » de Fermat. Mais hélas, cela restera un rêve d'archiviste."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>« Œuvres ».</strong> Par exemple, « Harry Potter et la Chambre des Secrets » en tant que concept logique, englobant toutes ses versions, comme les différentes traductions et réimpressions. C'est une définition assez utile, mais il peut être difficile de tracer la ligne de ce qui compte. Par exemple, nous voulons probablement préserver les différentes traductions, bien que les réimpressions avec seulement des différences mineures pourraient ne pas être aussi importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>« Éditions ».</strong> Ici, vous comptez chaque version unique d'un livre. Si quelque chose est différent, comme une couverture différente ou une préface différente, cela compte comme une édition différente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Fichiers.</strong> Lorsqu'on travaille avec des bibliothèques fantômes comme Library Genesis, Sci-Hub ou Z-Library, il y a une considération supplémentaire. Il peut y avoir plusieurs scans de la même édition. Et les gens peuvent créer de meilleures versions de fichiers existants, en scannant le texte à l'aide de l'OCR, ou en rectifiant des pages qui ont été scannées de travers. Nous voulons compter ces fichiers comme une seule édition, ce qui nécessiterait de bons metadata, ou une déduplication utilisant des mesures de similarité de documents."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Les « Éditions » semblent être la définition la plus pratique de ce que sont les « livres ». Heureusement, cette définition est également utilisée pour attribuer des numéros ISBN uniques. Un ISBN, ou International Standard Book Number, est couramment utilisé pour le commerce international, car il est intégré au système international de codes-barres (« International Article Number »). Si vous voulez vendre un livre en magasin, il a besoin d'un code-barres, donc vous obtenez un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Le billet de blog de Taycher mentionne que bien que les ISBN soient utiles, ils ne sont pas universels, car ils n'ont été réellement adoptés qu'au milieu des années soixante-dix, et pas partout dans le monde. Néanmoins, l'ISBN est probablement l'identifiant le plus largement utilisé pour les éditions de livres, donc c'est notre meilleur point de départ. Si nous pouvons trouver tous les ISBN du monde, nous obtenons une liste utile des livres qui doivent encore être préservés."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Alors, où obtenons-nous les données ? Il existe un certain nombre d'efforts existants qui tentent de compiler une liste de tous les livres du monde :"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Après tout, ils ont fait cette recherche pour Google Books. Cependant, leurs metadata ne sont pas accessibles en masse et sont plutôt difficiles à extraire."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Comme mentionné précédemment, c'est leur mission entière. Ils ont collecté d'énormes quantités de données de bibliothèques provenant de bibliothèques coopérantes et d'archives nationales, et continuent de le faire. Ils ont également des bibliothécaires bénévoles et une équipe technique qui tentent de dédupliquer les enregistrements et de les étiqueter avec toutes sortes de metadata. Le meilleur de tout, leur ensemble de données est complètement ouvert. Vous pouvez simplement <a %(openlibrary)s>le télécharger</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> C'est un site web géré par l'organisation à but non lucratif OCLC, qui vend des systèmes de gestion de bibliothèques. Ils agrègent les metadata de livres de nombreuses bibliothèques et les rendent disponibles via le site web WorldCat. Cependant, ils gagnent également de l'argent en vendant ces données, donc elles ne sont pas disponibles pour un téléchargement en masse. Ils ont quelques ensembles de données en masse plus limités disponibles en téléchargement, en coopération avec des bibliothèques spécifiques."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> C'est le sujet de cet article de blog. ISBNdb extrait des données de divers sites web pour les metadata de livres, en particulier les données de tarification, qu'ils vendent ensuite aux libraires, afin qu'ils puissent fixer le prix de leurs livres en fonction du reste du marché. Puisque les ISBN sont assez universels de nos jours, ils ont effectivement construit une « page web pour chaque livre »."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Divers systèmes de bibliothèques et archives individuels.</strong> Il existe des bibliothèques et des archives qui n'ont pas été indexées et agrégées par aucun des précédents, souvent parce qu'elles sont sous-financées, ou pour d'autres raisons ne souhaitent pas partager leurs données avec Open Library, OCLC, Google, etc. Beaucoup d'entre elles ont des enregistrements numériques accessibles via Internet, et elles ne sont souvent pas très bien protégées, donc si vous voulez aider et vous amuser à apprendre sur des systèmes de bibliothèques étranges, ce sont d'excellents points de départ."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Dans cet article, nous sommes heureux d'annoncer une petite sortie (comparée à nos précédentes sorties de Z-Library). Nous avons extrait la plupart d'ISBNdb, et rendu les données disponibles pour le téléchargement en torrent sur le site de Pirate Library Mirror (EDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> ; nous ne le lierons pas directement ici, il suffit de le rechercher). Il s'agit d'environ 30,9 millions d'enregistrements (20 Go en <a %(jsonlines)s>JSON Lines</a> ; 4,4 Go compressés). Sur leur site web, ils affirment qu'ils ont en fait 32,6 millions d'enregistrements, donc nous avons peut-être manqué certains, ou <em>ils</em> pourraient faire quelque chose de mal. Dans tous les cas, pour l'instant, nous ne partagerons pas exactement comment nous l'avons fait — nous laisserons cela comme un exercice pour le lecteur. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Ce que nous partagerons, c'est une analyse préliminaire, pour essayer de se rapprocher de l'estimation du nombre de livres dans le monde. Nous avons examiné trois ensembles de données : ce nouvel ensemble de données ISBNdb, notre première sortie de metadata que nous avons extraite de la bibliothèque fantôme Z-Library (qui inclut Library Genesis), et le dump de données d'Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Commençons par quelques chiffres approximatifs :"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Dans Z-Library/Libgen et Open Library, il y a beaucoup plus de livres que d'ISBN uniques. Cela signifie-t-il que beaucoup de ces livres n'ont pas d'ISBN, ou que les metadata des ISBN sont simplement manquantes ? Nous pouvons probablement répondre à cette question avec une combinaison de correspondance automatisée basée sur d'autres attributs (titre, auteur, éditeur, etc.), en intégrant plus de sources de données, et en extrayant les ISBN des scans réels des livres eux-mêmes (dans le cas de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Combien de ces ISBN sont uniques ? Cela est mieux illustré par un diagramme de Venn :"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Pour être plus précis :"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Nous avons été surpris par le peu de chevauchement qu'il y a ! ISBNdb possède une énorme quantité d'ISBN qui n'apparaissent ni dans Z-Library ni dans Open Library, et il en va de même (à un degré moindre mais toujours substantiel) pour les deux autres. Cela soulève de nombreuses nouvelles questions. Dans quelle mesure le rapprochement automatisé aiderait-il à étiqueter les livres qui n'ont pas été étiquetés avec des ISBN ? Y aurait-il beaucoup de correspondances et donc un chevauchement accru ? De plus, que se passerait-il si nous ajoutions un 4ème ou 5ème ensemble de données ? Quel chevauchement verrions-nous alors ?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Cela nous donne un point de départ. Nous pouvons maintenant examiner tous les ISBN qui n'étaient pas dans l'ensemble de données de Z-Library, et qui ne correspondent pas non plus aux champs titre/auteur. Cela peut nous aider à préserver tous les livres du monde : d'abord en scrappant Internet pour des scans, puis en sortant dans la vie réelle pour numériser des livres. Ce dernier pourrait même être financé par la foule, ou motivé par des « primes » de personnes souhaitant voir certains livres numérisés. Tout cela est une histoire pour une autre fois."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si vous souhaitez aider dans l'un de ces domaines — analyse approfondie ; extraction de plus de metadata ; recherche de plus de livres ; OCR de livres ; faire cela pour d'autres domaines (par exemple, articles, livres audio, films, émissions de télévision, magazines) ou même rendre certaines de ces données disponibles pour des choses comme l'entraînement de modèles de langage ML / de grande taille — veuillez me contacter (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si vous êtes spécifiquement intéressé par l'analyse de données, nous travaillons à rendre nos ensembles de données et scripts disponibles dans un format plus facile à utiliser. Ce serait formidable si vous pouviez simplement forker un notebook et commencer à jouer avec cela."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Enfin, si vous souhaitez soutenir ce travail, veuillez envisager de faire un don. Il s'agit d'une opération entièrement gérée par des bénévoles, et votre contribution fait une énorme différence. Chaque contribution compte. Pour l'instant, nous acceptons les dons en crypto ; consultez la page Faites un don sur l'Archive d'Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pour une définition raisonnable de « pour toujours ». ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Bien sûr, le patrimoine écrit de l'humanité est bien plus que des livres, surtout de nos jours. Pour les besoins de cet article et de nos récentes publications, nous nous concentrons sur les livres, mais nos intérêts vont plus loin."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Il y a beaucoup plus à dire sur Aaron Swartz, mais nous voulions simplement le mentionner brièvement, car il joue un rôle central dans cette histoire. Avec le temps, plus de gens pourraient rencontrer son nom pour la première fois, et pourront ensuite plonger dans le terrier du lapin eux-mêmes."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La fenêtre critique des bibliothèques fantômes"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Comment pouvons-nous prétendre préserver nos collections à perpétuité, alors qu'elles approchent déjà de 1 Po ?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Version chinoise 中文版</a>, discutez sur <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "À l'Archive d'Anna, on nous demande souvent comment nous pouvons prétendre préserver nos collections à perpétuité, alors que la taille totale approche déjà 1 Pétaoctet (1000 To), et continue de croître. Dans cet article, nous examinerons notre philosophie, et verrons pourquoi la prochaine décennie est cruciale pour notre mission de préservation des connaissances et de la culture de l'humanité."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "La <a %(annas_archive_stats)s>taille totale</a> de nos collections, au cours des derniers mois, ventilée par nombre de seeders de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorités"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Pourquoi nous soucions-nous autant des articles et des livres ? Mettons de côté notre croyance fondamentale en la préservation en général — nous pourrions écrire un autre article à ce sujet. Alors pourquoi les articles et les livres spécifiquement ? La réponse est simple : <strong>densité d'information</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Par mégaoctet de stockage, le texte écrit stocke le plus d'informations parmi tous les médias. Bien que nous nous préoccupions à la fois de la connaissance et de la culture, nous nous soucions davantage de la première. Dans l'ensemble, nous trouvons une hiérarchie de densité d'information et d'importance de la préservation qui ressemble à peu près à ceci :"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Articles académiques, revues, rapports"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Données organiques comme les séquences d'ADN, les graines de plantes ou les échantillons microbiens"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Livres non-fictionnels"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Code logiciel de science et d'ingénierie"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Données de mesure comme les mesures scientifiques, les données économiques, les rapports d'entreprise"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sites web de science et d'ingénierie, discussions en ligne"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Magazines non-fictionnels, journaux, manuels"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcriptions non-fictionnelles de conférences, documentaires, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Données internes d'entreprises ou de gouvernements (fuites)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Enregistrements de metadata en général (de non-fiction et fiction ; d'autres médias, art, personnes, etc. ; y compris les critiques)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Données géographiques (par exemple, cartes, études géologiques)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcriptions de procédures légales ou judiciaires"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versions fictives ou de divertissement de tout ce qui précède"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Le classement dans cette liste est quelque peu arbitraire — plusieurs éléments sont à égalité ou font l'objet de désaccords au sein de notre équipe — et nous oublions probablement certaines catégories importantes. Mais c'est à peu près ainsi que nous priorisons."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Certains de ces éléments sont trop différents des autres pour que nous nous en préoccupions (ou sont déjà pris en charge par d'autres institutions), comme les données organiques ou géographiques. Mais la plupart des éléments de cette liste sont en réalité importants pour nous."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un autre grand facteur dans notre priorisation est le risque auquel une œuvre est exposée. Nous préférons nous concentrer sur les œuvres qui sont :"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rares"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniquement sous-exposées"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniquement à risque de destruction (par exemple, par la guerre, les réductions de financement, les poursuites judiciaires ou la persécution politique)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Enfin, nous nous soucions de l'échelle. Nous avons un temps et des ressources limités, donc nous préférons passer un mois à sauver 10 000 livres plutôt que 1 000 livres — s'ils sont à peu près également précieux et à risque."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliothèque fantôme"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Il existe de nombreuses organisations qui ont des missions similaires et des priorités similaires. En effet, il y a des bibliothèques, archives, laboratoires, musées et autres institutions chargées de la préservation de ce type. Beaucoup d'entre elles sont bien financées, par des gouvernements, des particuliers ou des entreprises. Mais elles ont un énorme angle mort : le système juridique."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "C'est là que réside le rôle unique des bibliothèques fantômes, et la raison pour laquelle l'Archive d'Anna existe. Nous pouvons faire des choses que d'autres institutions ne sont pas autorisées à faire. Maintenant, ce n'est pas (souvent) que nous pouvons archiver des matériaux qu'il est illégal de conserver ailleurs. Non, il est légal dans de nombreux endroits de construire une archive avec n'importe quels livres, articles, magazines, et ainsi de suite."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Mais ce qui manque souvent aux archives légales, c’est <strong>la redondance et la longévité</strong>. Il existe des livres dont une seule copie est conservée dans une bibliothèque physique quelque part. Il existe des enregistrements de metadata gardés par une seule entreprise. Il existe des journaux uniquement préservés sur microfilm dans une seule archive. Les bibliothèques peuvent subir des réductions de financement, les entreprises peuvent faire faillite, les archives peuvent être bombardées et brûlées jusqu’au sol. Ce n’est pas hypothétique — cela arrive tout le temps."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Ce que nous pouvons faire de manière unique sur l’Archive d’Anna, c’est stocker de nombreuses copies d’œuvres, à grande échelle. Nous pouvons collecter des articles, des livres, des magazines, et plus encore, et les distribuer en masse. Nous le faisons actuellement via des torrents, mais les technologies exactes importent peu et évolueront avec le temps. L’important est de distribuer de nombreuses copies à travers le monde. Cette citation d’il y a plus de 200 ans reste toujours vraie :"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ce qui est perdu ne peut être récupéré ; mais sauvons ce qui reste : non pas par des coffres et des serrures qui les protègent du regard et de l’usage du public, en les confiant à l’oubli du temps, mais par une telle multiplication de copies, qui les mettra hors de portée des accidents.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Une petite note sur le domaine public. Puisque l’Archive d’Anna se concentre de manière unique sur des activités illégales dans de nombreux endroits à travers le monde, nous ne nous soucions pas des collections largement disponibles, telles que les livres du domaine public. Les entités légales s’en occupent souvent déjà bien. Cependant, il y a des considérations qui nous amènent parfois à travailler sur des collections publiquement disponibles :"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Les enregistrements de metadata peuvent être librement consultés sur le site Worldcat, mais pas téléchargés en masse (jusqu’à ce que nous les <a %(worldcat_scrape)s>ayons extraits</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Le code peut être open source sur Github, mais Github dans son ensemble ne peut pas être facilement dupliqué et donc préservé (bien que dans ce cas particulier, il existe suffisamment de copies distribuées de la plupart des dépôts de code)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit est gratuit à utiliser, mais a récemment mis en place des mesures anti-extraction strictes, à la suite de la formation de LLM avides de données (plus à ce sujet plus tard)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Une multiplication de copies"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Revenons à notre question initiale : comment pouvons-nous prétendre préserver nos collections à perpétuité ? Le principal problème ici est que notre collection a <a %(torrents_stats)s>augmenté</a> à un rythme rapide, en extrayant et en open-sourçant certaines collections massives (en plus du travail incroyable déjà réalisé par d’autres bibliothèques fantômes de données ouvertes comme Sci-Hub et Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Cette croissance des données rend plus difficile la duplication des collections à travers le monde. Le stockage de données est coûteux ! Mais nous sommes optimistes, surtout en observant les trois tendances suivantes."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Nous avons cueilli les fruits à portée de main"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Celle-ci découle directement de nos priorités discutées ci-dessus. Nous préférons travailler d’abord sur la libération de grandes collections. Maintenant que nous avons sécurisé certaines des plus grandes collections au monde, nous nous attendons à ce que notre croissance soit beaucoup plus lente."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Il reste encore une longue traîne de petites collections, et de nouveaux livres sont numérisés ou publiés chaque jour, mais le rythme sera probablement beaucoup plus lent. Nous pourrions encore doubler ou même tripler de taille, mais sur une période plus longue."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Les coûts de stockage continuent de baisser de manière exponentielle"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Au moment de la rédaction, les <a %(diskprices)s>prix des disques</a> par To sont d’environ 12 $ pour les disques neufs, 8 $ pour les disques d’occasion et 4 $ pour les bandes. Si nous sommes prudents et ne regardons que les disques neufs, cela signifie que stocker un pétaoctet coûte environ 12 000 $. Si nous supposons que notre bibliothèque triplera de 900 To à 2,7 Po, cela signifierait 32 400 $ pour dupliquer notre bibliothèque entière. En ajoutant l’électricité, le coût d’autres matériels, etc., arrondissons à 40 000 $. Ou avec des bandes, plutôt entre 15 000 $ et 20 000 $."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "D’un côté <strong>15 000 $–40 000 $ pour la somme de toutes les connaissances humaines est une aubaine</strong>. De l’autre, c’est un peu raide d’attendre des tonnes de copies complètes, surtout si nous voulons aussi que ces personnes continuent de partager leurs torrents pour le bénéfice des autres."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "C’est aujourd’hui. Mais le progrès avance :"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Les coûts des disques durs par To ont été réduits d’environ un tiers au cours des 10 dernières années, et continueront probablement de baisser à un rythme similaire. Les bandes semblent suivre une trajectoire similaire. Les prix des SSD baissent encore plus rapidement, et pourraient surpasser les prix des HDD d’ici la fin de la décennie."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendances des prix des HDD de différentes sources (cliquez pour voir l’étude)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si cela se confirme, alors dans 10 ans, nous pourrions envisager seulement 5 000 $–13 000 $ pour dupliquer notre collection entière (1/3), ou même moins si nous croissons moins en taille. Bien que cela représente encore beaucoup d’argent, cela sera accessible pour de nombreuses personnes. Et cela pourrait être encore mieux grâce au point suivant…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Améliorations de la densité d'information"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Nous stockons actuellement les livres dans les formats bruts qui nous sont fournis. Certes, ils sont compressés, mais souvent, ce sont encore de grandes numérisations ou photographies de pages."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Jusqu'à présent, les seules options pour réduire la taille totale de notre collection étaient une compression plus agressive ou la déduplication. Cependant, pour obtenir des économies significatives, les deux sont trop destructeurs à notre goût. Une forte compression des photos peut rendre le texte à peine lisible. Et la déduplication nécessite une grande confiance dans le fait que les livres soient exactement les mêmes, ce qui est souvent trop imprécis, surtout si le contenu est le même mais que les numérisations sont faites à des moments différents."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Il y a toujours eu une troisième option, mais sa qualité était si abominable que nous ne l'avons jamais envisagée : <strong>OCR, ou Reconnaissance Optique de Caractères</strong>. C'est le processus de conversion des photos en texte brut, en utilisant l'IA pour détecter les caractères dans les photos. Les outils pour cela existent depuis longtemps et sont assez corrects, mais « assez correct » n'est pas suffisant pour des fins de préservation."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Cependant, les récents modèles d'apprentissage profond multimodal ont fait des progrès extrêmement rapides, bien que toujours à des coûts élevés. Nous nous attendons à ce que la précision et les coûts s'améliorent considérablement dans les années à venir, au point qu'il deviendra réaliste de l'appliquer à l'ensemble de notre bibliothèque."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Améliorations de l'OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Lorsque cela se produira, nous conserverons probablement toujours les fichiers originaux, mais en plus, nous pourrions avoir une version beaucoup plus petite de notre bibliothèque que la plupart des gens voudront reproduire. Le point fort est que le texte brut lui-même se compresse encore mieux et est beaucoup plus facile à dédupliquer, nous offrant encore plus d'économies."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Dans l'ensemble, il n'est pas irréaliste de s'attendre à une réduction d'au moins 5 à 10 fois de la taille totale des fichiers, voire plus. Même avec une réduction conservatrice de 5 fois, nous envisagerions <strong>1 000 à 3 000 $ dans 10 ans même si notre bibliothèque triple de taille</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Fenêtre critique"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si ces prévisions sont exactes, nous <strong>n'avons qu'à attendre quelques années</strong> avant que notre collection entière ne soit largement reproduite. Ainsi, pour reprendre les mots de Thomas Jefferson, « placée hors de portée des accidents »."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Malheureusement, l'avènement des LLM, et leur formation gourmande en données, a mis de nombreux détenteurs de droits d'auteur sur la défensive. Encore plus qu'ils ne l'étaient déjà. De nombreux sites Web rendent plus difficile le scraping et l'archivage, les poursuites judiciaires fusent, et pendant ce temps, les bibliothèques et archives physiques continuent d'être négligées."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Nous ne pouvons qu'attendre que ces tendances continuent de s'aggraver, et que de nombreuses œuvres soient perdues bien avant d'entrer dans le domaine public."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Nous sommes à la veille d'une révolution dans la préservation, mais <q>ce qui est perdu ne peut être récupéré.</q></strong> Nous avons une fenêtre critique d'environ 5 à 10 ans pendant laquelle il est encore assez coûteux d'exploiter une bibliothèque fantôme et de créer de nombreux sites miroirs à travers le monde, et pendant laquelle l'accès n'a pas encore été complètement fermé."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si nous pouvons franchir cette fenêtre, alors nous aurons effectivement préservé le savoir et la culture de l'humanité à perpétuité. Nous ne devrions pas laisser ce temps se perdre. Nous ne devrions pas laisser cette fenêtre critique se refermer sur nous."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Allons-y."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accès exclusif pour les entreprises LLM à la plus grande collection de livres non-fictionnels chinois au monde"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Version chinoise 中文版</a>, <a %(news_ycombinator)s>Discuter sur Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR :</strong> Les Archives d'Anna ont acquis une collection unique de 7,5 millions / 350 To de livres non-fictionnels chinois — plus grande que Library Genesis. Nous sommes prêts à donner à une entreprise LLM un accès exclusif, en échange d'un OCR et d'une extraction de texte de haute qualité.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Ceci est un court article de blog. Nous recherchons une entreprise ou une institution pour nous aider avec l'OCR et l'extraction de texte pour une collection massive que nous avons acquise, en échange d'un accès exclusif anticipé. Après la période d'embargo, nous publierons bien sûr l'intégralité de la collection."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Un texte académique de haute qualité est extrêmement utile pour l'entraînement des LLM. Bien que notre collection soit en chinois, cela devrait être utile même pour l'entraînement des LLM en anglais : les modèles semblent encoder des concepts et des connaissances indépendamment de la langue source."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Pour cela, le texte doit être extrait des scans. Qu'est-ce que l'Archive d'Anna en retire ? Une recherche en texte intégral des livres pour ses utilisateurs."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Parce que nos objectifs s'alignent avec ceux des développeurs de LLM, nous recherchons un collaborateur. Nous sommes prêts à vous donner <strong>un accès anticipé exclusif à cette collection en masse pendant 1 an</strong>, si vous pouvez effectuer une OCR et une extraction de texte appropriées. Si vous êtes prêt à partager l'intégralité du code de votre pipeline avec nous, nous serions prêts à prolonger l'embargo sur la collection."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pages d'exemple"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Pour nous prouver que vous avez un bon pipeline, voici quelques pages d'exemple pour commencer, tirées d'un livre sur les supraconducteurs. Votre pipeline doit gérer correctement les mathématiques, les tableaux, les graphiques, les notes de bas de page, etc."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envoyez vos pages traitées à notre adresse e-mail. Si elles sont satisfaisantes, nous vous enverrons plus en privé, et nous nous attendons à ce que vous puissiez rapidement exécuter votre pipeline sur celles-ci également. Une fois que nous serons satisfaits, nous pourrons conclure un accord."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collection"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Quelques informations supplémentaires sur la collection. <a %(duxiu)s>Duxiu</a> est une base de données massive de livres numérisés, créée par le <a %(chaoxing)s>SuperStar Digital Library Group</a>. La plupart sont des livres académiques, numérisés pour les rendre disponibles numériquement aux universités et bibliothèques. Pour notre public anglophone, <a %(library_princeton)s>Princeton</a> et l'<a %(guides_lib_uw)s>Université de Washington</a> offrent de bons aperçus. Il y a aussi un excellent article donnant plus de contexte : <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (à consulter dans l'Archive d'Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Les livres de Duxiu ont longtemps été piratés sur l'internet chinois. Ils sont généralement vendus pour moins d'un dollar par des revendeurs. Ils sont typiquement distribués en utilisant l'équivalent chinois de Google Drive, qui a souvent été piraté pour permettre plus d'espace de stockage. Quelques détails techniques peuvent être trouvés <a %(github_duty_machine)s>ici</a> et <a %(github_821_github_io)s>ici</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Bien que les livres aient été distribués de manière semi-publique, il est assez difficile de les obtenir en masse. Nous avions cela en haut de notre liste de tâches, et avons alloué plusieurs mois de travail à temps plein pour cela. Cependant, récemment, un bénévole incroyable, étonnant et talentueux nous a contactés, nous disant qu'il avait déjà fait tout ce travail — à grands frais. Ils ont partagé l'intégralité de la collection avec nous, sans rien attendre en retour, sauf la garantie d'une préservation à long terme. Vraiment remarquable. Ils ont accepté de demander de l'aide de cette manière pour faire l'OCR de la collection."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La collection compte 7 543 702 fichiers. C'est plus que la non-fiction de Library Genesis (environ 5,3 millions). La taille totale des fichiers est d'environ 359 To (326 TiB) dans sa forme actuelle."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Nous sommes ouverts à d'autres propositions et idées. Contactez-nous simplement. Consultez l'Archive d'Anna pour plus d'informations sur nos collections, nos efforts de préservation, et comment vous pouvez aider. Merci !"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avertissement : cet article de blog a été déprécié. Nous avons décidé que l'IPFS n'est pas encore prêt pour le grand public. Nous continuerons à lier des fichiers sur IPFS depuis l'Archive d'Anna lorsque cela est possible, mais nous ne l'hébergerons plus nous-mêmes, ni ne recommandons aux autres de créer un site miroir en utilisant IPFS. Veuillez consulter notre page Torrents si vous souhaitez aider à préserver notre collection."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Aidez à semer Z-Library sur IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Comment gérer une bibliothèque fantôme : opérations à l'Archive d'Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Il n'y a pas de <q>AWS pour les associations caritatives fantômes,</q> alors comment gérons-nous l'Archive d'Anna ?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Je gère <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, le plus grand moteur de recherche open-source à but non lucratif pour les <a %(wikipedia_shadow_library)s>bibliothèques fantômes</a>, comme Sci-Hub, Library Genesis et Z-Library. Notre objectif est de rendre la connaissance et la culture facilement accessibles, et finalement de construire une communauté de personnes qui, ensemble, archivent et préservent <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tous les livres du monde</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Dans cet article, je vais montrer comment nous gérons ce site web, et les défis uniques qui accompagnent l'exploitation d'un site web avec un statut juridique discutable, puisqu'il n'y a pas d'“AWS pour les associations caritatives fantômes”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Consultez également l'article associé <a %(blog_how_to_become_a_pirate_archivist)s>Comment devenir un archiviste pirate</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Jetons d'innovation"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Commençons par notre pile technologique. Elle est délibérément ennuyeuse. Nous utilisons Flask, MariaDB et ElasticSearch. C'est littéralement tout. La recherche est en grande partie un problème résolu, et nous n'avons pas l'intention de la réinventer. De plus, nous devons dépenser nos <a %(mcfunley)s>jetons d'innovation</a> sur autre chose : ne pas être fermés par les autorités."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Alors, à quel point Anna’s Archive est-elle légale ou illégale exactement ? Cela dépend principalement de la juridiction légale. La plupart des pays croient en une forme de droit d'auteur, ce qui signifie que des personnes ou des entreprises se voient attribuer un monopole exclusif sur certains types d'œuvres pour une certaine période. En passant, chez Anna’s Archive, nous pensons que bien qu'il y ait certains avantages, dans l'ensemble, le droit d'auteur est un net-négatif pour la société — mais c'est une histoire pour une autre fois."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Ce monopole exclusif sur certaines œuvres signifie qu'il est illégal pour quiconque en dehors de ce monopole de distribuer directement ces œuvres — y compris nous. Mais Anna’s Archive est un moteur de recherche qui ne distribue pas directement ces œuvres (du moins pas sur notre site web en clair), donc nous devrions être en règle, n'est-ce pas ? Pas exactement. Dans de nombreuses juridictions, il est non seulement illégal de distribuer des œuvres protégées par le droit d'auteur, mais aussi de créer des liens vers des endroits qui le font. Un exemple classique de cela est la loi DMCA des États-Unis."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "C'est l'extrémité la plus stricte du spectre. À l'autre extrémité du spectre, il pourrait théoriquement y avoir des pays sans aucune loi sur le droit d'auteur, mais ceux-ci n'existent pas vraiment. Pratiquement tous les pays ont une forme de loi sur le droit d'auteur dans leurs livres. L'application est une autre histoire. Il y a beaucoup de pays dont les gouvernements ne se soucient pas d'appliquer la loi sur le droit d'auteur. Il y a aussi des pays entre les deux extrêmes, qui interdisent la distribution d'œuvres protégées par le droit d'auteur, mais n'interdisent pas de créer des liens vers ces œuvres."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Une autre considération est au niveau de l'entreprise. Si une entreprise opère dans une juridiction qui ne se soucie pas du droit d'auteur, mais que l'entreprise elle-même n'est pas prête à prendre de risque, alors elle pourrait fermer votre site web dès que quelqu'un se plaint à ce sujet."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Enfin, une grande considération est les paiements. Puisque nous devons rester anonymes, nous ne pouvons pas utiliser les méthodes de paiement traditionnelles. Cela nous laisse avec les cryptomonnaies, et seulement un petit sous-ensemble d'entreprises les soutiennent (il existe des cartes de débit virtuelles payées par crypto, mais elles sont souvent non acceptées)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architecture système"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Disons donc que vous avez trouvé des entreprises prêtes à héberger votre site web sans vous fermer — appelons-les “fournisseurs amoureux de la liberté” 😄. Vous découvrirez rapidement que tout héberger chez eux est plutôt coûteux, donc vous pourriez vouloir trouver des “fournisseurs bon marché” et faire l'hébergement réel là-bas, en passant par les fournisseurs amoureux de la liberté. Si vous le faites correctement, les fournisseurs bon marché ne sauront jamais ce que vous hébergez, et ne recevront jamais de plaintes."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Avec tous ces fournisseurs, il y a un risque qu'ils vous ferment quand même, donc vous avez également besoin de redondance. Nous avons besoin de cela à tous les niveaux de notre pile."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Une entreprise quelque peu amoureuse de la liberté qui s'est mise dans une position intéressante est Cloudflare. Ils ont <a %(blog_cloudflare)s>argumenté</a> qu'ils ne sont pas un fournisseur d'hébergement, mais une utilité, comme un FAI. Ils ne sont donc pas soumis aux demandes de retrait DMCA ou autres, et transmettent toute demande à votre véritable fournisseur d'hébergement. Ils sont allés jusqu'à aller en justice pour protéger cette structure. Nous pouvons donc les utiliser comme une autre couche de mise en cache et de protection."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare n'accepte pas les paiements anonymes, donc nous ne pouvons utiliser que leur plan gratuit. Cela signifie que nous ne pouvons pas utiliser leurs fonctionnalités d'équilibrage de charge ou de basculement. Nous avons donc <a %(annas_archive_l255)s>implémenté cela nous-mêmes</a> au niveau du domaine. Lors du chargement de la page, le navigateur vérifiera si le domaine actuel est toujours disponible, et sinon, il réécrit toutes les URL vers un autre domaine. Étant donné que Cloudflare met en cache de nombreuses pages, cela signifie qu'un utilisateur peut atterrir sur notre domaine principal, même si le serveur proxy est en panne, et ensuite au clic suivant être déplacé vers un autre domaine."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Nous avons également des préoccupations opérationnelles normales à gérer, telles que la surveillance de la santé des serveurs, la journalisation des erreurs backend et frontend, etc. Notre architecture de basculement permet également plus de robustesse sur ce front, par exemple en exécutant un ensemble complètement différent de serveurs sur l'un des domaines. Nous pouvons même exécuter des versions plus anciennes du code et des datasets sur ce domaine séparé, au cas où un bug critique dans la version principale passerait inaperçu."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Nous pouvons également nous prémunir contre un retournement de Cloudflare contre nous, en le supprimant d'un des domaines, comme ce domaine séparé. Différentes permutations de ces idées sont possibles."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Outils"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Voyons quels outils nous utilisons pour accomplir tout cela. Cela évolue beaucoup à mesure que nous rencontrons de nouveaux problèmes et trouvons de nouvelles solutions."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Serveur d'application : Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Serveur proxy : Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestion des serveurs : Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Développement : Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hébergement statique Onion : Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Il y a certaines décisions sur lesquelles nous avons hésité. L'une d'elles concerne la communication entre serveurs : nous utilisions Wireguard pour cela, mais nous avons constaté qu'il cessait parfois de transmettre des données, ou ne les transmettait que dans une seule direction. Cela s'est produit avec plusieurs configurations différentes de Wireguard que nous avons essayées, telles que <a %(github_costela_wesher)s>wesher</a> et <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Nous avons également essayé de faire passer les ports via SSH, en utilisant autossh et sshuttle, mais nous avons rencontré <a %(github_sshuttle)s>des problèmes là-bas</a> (bien qu'il ne soit toujours pas clair pour moi si autossh souffre de problèmes TCP-sur-TCP ou non — cela me semble juste être une solution bancale, mais peut-être que c'est en fait correct ?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Au lieu de cela, nous sommes revenus à des connexions directes entre serveurs, cachant qu'un serveur fonctionne sur des fournisseurs bon marché en utilisant le filtrage IP avec UFW. Cela a l'inconvénient que Docker ne fonctionne pas bien avec UFW, à moins d'utiliser <code>network_mode: \"host\"</code>. Tout cela est un peu plus sujet aux erreurs, car vous exposez votre serveur à Internet avec juste une petite mauvaise configuration. Peut-être devrions-nous revenir à autossh — vos retours seraient très appréciés ici."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Nous avons également hésité entre Varnish et Nginx. Nous aimons actuellement Varnish, mais il a ses bizarreries et ses aspérités. Il en va de même pour Checkmk : nous ne l'adorons pas, mais il fonctionne pour l'instant. Weblate a été correct mais pas incroyable — je crains parfois qu'il ne perde mes données chaque fois que j'essaie de les synchroniser avec notre dépôt git. Flask a été bon dans l'ensemble, mais il a quelques bizarreries étranges qui ont coûté beaucoup de temps à déboguer, comme la configuration de domaines personnalisés, ou des problèmes avec son intégration SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Jusqu'à présent, les autres outils ont été excellents : nous n'avons pas de plaintes sérieuses concernant MariaDB, ElasticSearch, Gitlab, Zulip, Docker et Tor. Tous ont eu quelques problèmes, mais rien de trop sérieux ou chronophage."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Cela a été une expérience intéressante d'apprendre à configurer un moteur de recherche de bibliothèque fantôme robuste et résilient. Il y a beaucoup plus de détails à partager dans de futurs articles, alors faites-moi savoir ce que vous aimeriez apprendre davantage !"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Comme toujours, nous recherchons des dons pour soutenir ce travail, alors n'oubliez pas de consulter la page Faites un don sur l'Archive d'Anna. Nous recherchons également d'autres types de soutien, tels que des subventions, des sponsors à long terme, des fournisseurs de paiement à haut risque, peut-être même des publicités (de bon goût !). Et si vous souhaitez contribuer de votre temps et de vos compétences, nous recherchons toujours des développeurs, des traducteurs, etc. Merci pour votre intérêt et votre soutien."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Bonjour, je suis Anna. J'ai créé <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, la plus grande bibliothèque fantôme du monde. Ceci est mon blog personnel, dans lequel mes coéquipiers et moi écrivons sur le piratage, la préservation numérique, et plus encore."

#, fuzzy
msgid "blog.index.text2"
msgstr "Connectez-vous avec moi sur <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Notez que ce site web est juste un blog. Nous n'hébergeons ici que nos propres mots. Aucun torrent ou autre fichier protégé par des droits d'auteur n'est hébergé ou lié ici."

#, fuzzy
msgid "blog.index.heading"
msgstr "Articles de blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 milliard de grattage WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Mettre 5 998 794 livres sur IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avertissement : cet article de blog a été déprécié. Nous avons décidé que l'IPFS n'est pas encore prêt pour le grand public. Nous continuerons à lier des fichiers sur IPFS depuis l'Archive d'Anna lorsque cela est possible, mais nous ne l'hébergerons plus nous-mêmes, ni ne recommandons aux autres de créer un site miroir en utilisant IPFS. Veuillez consulter notre page Torrents si vous souhaitez aider à préserver notre collection."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR :</strong> L'Archive d'Anna a gratté l'intégralité de WorldCat (la plus grande collection de metadata de bibliothèque au monde) pour créer une liste de livres à préserver.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Il y a un an, nous avons <a %(blog)s>entrepris</a> de répondre à cette question : <strong>Quel pourcentage de livres a été préservé de manière permanente par les bibliothèques fantômes ?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Une fois qu'un livre entre dans une bibliothèque fantôme de données ouvertes comme <a %(wikipedia_library_genesis)s>Library Genesis</a>, et maintenant <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, il est dupliqué dans le monde entier (via des torrents), le préservant ainsi pratiquement pour toujours."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Pour répondre à la question de savoir quel pourcentage de livres a été préservé, nous devons connaître le dénominateur : combien de livres existent au total ? Et idéalement, nous n'avons pas seulement un chiffre, mais des metadata réelles. Ensuite, nous pouvons non seulement les comparer aux bibliothèques fantômes, mais aussi <strong>créer une liste de livres restants à préserver !</strong> Nous pourrions même commencer à rêver d'un effort participatif pour parcourir cette liste."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Nous avons extrait des données de <a %(wikipedia_isbndb_com)s>ISBNdb</a> et téléchargé le <a %(openlibrary)s>jeu de données d'Open Library</a>, mais les résultats étaient insatisfaisants. Le principal problème était qu'il n'y avait pas beaucoup de chevauchement des ISBN. Consultez ce diagramme de Venn de <a %(blog)s>notre article de blog</a> :"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Nous avons été très surpris par le peu de chevauchement entre ISBNdb et Open Library, qui incluent tous deux des données de diverses sources, telles que des extractions web et des enregistrements de bibliothèques. Si les deux faisaient un bon travail pour trouver la plupart des ISBN existants, leurs cercles auraient sûrement un chevauchement substantiel, ou l'un serait un sous-ensemble de l'autre. Cela nous a fait nous demander combien de livres se trouvent <em>complètement en dehors de ces cercles</em> ? Nous avons besoin d'une base de données plus grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "C'est alors que nous avons porté notre attention sur la plus grande base de données de livres au monde : <a %(wikipedia_worldcat)s>WorldCat</a>. Il s'agit d'une base de données propriétaire de l'organisation à but non lucratif <a %(wikipedia_oclc)s>OCLC</a>, qui agrège des enregistrements de metadata de bibliothèques du monde entier, en échange de donner à ces bibliothèques l'accès à l'ensemble du jeu de données, et de les faire apparaître dans les résultats de recherche des utilisateurs finaux."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Bien que l'OCLC soit une organisation à but non lucratif, leur modèle économique nécessite de protéger leur base de données. Eh bien, nous sommes désolés de le dire, amis de l'OCLC, nous allons tout révéler. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Au cours de l'année écoulée, nous avons méticuleusement extrait tous les enregistrements de WorldCat. Au début, nous avons eu un coup de chance. WorldCat venait de lancer la refonte complète de son site web (en août 2022). Cela comprenait une refonte substantielle de leurs systèmes backend, introduisant de nombreuses failles de sécurité. Nous avons immédiatement saisi l'opportunité et avons pu extraire des centaines de millions (!) d'enregistrements en quelques jours."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Refonte de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Après cela, les failles de sécurité ont été lentement corrigées une par une, jusqu'à ce que la dernière que nous ayons trouvée soit corrigée il y a environ un mois. À ce moment-là, nous avions pratiquement tous les enregistrements et nous ne cherchions qu'à obtenir des enregistrements de qualité légèrement supérieure. Nous avons donc estimé qu'il était temps de publier !"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Examinons quelques informations de base sur les données :"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format ?</strong> <a %(blog)s>Conteneurs d'Anna’s Archive (AAC)</a>, qui est essentiellement des <a %(jsonlines)s>JSON Lines</a> compressées avec <a %(zstd)s>Zstandard</a>, plus quelques sémantiques standardisées. Ces conteneurs enveloppent divers types d'enregistrements, basés sur les différentes extractions que nous avons déployées."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Données"

msgid "dyn.buy_membership.error.unknown"
msgstr "Une erreur inconnue s'est produite. Veuillez nous contacter à %(email)s avec une capture d'écran."

msgid "dyn.buy_membership.error.minimum"
msgstr "Cette cryptomonnaie exige un montant minimum plus haut que la normale. Veuillez choisir un abonnement ou une cryptomonnaie différente."

msgid "dyn.buy_membership.error.try_again"
msgstr "La demande n'a pas pu être complétée. Veuillez réessayer dans quelques minutes, et si le problème persiste, contactez-nous à l'adresse %(email)s avec une capture d'écran."

msgid "dyn.buy_membership.error.wait"
msgstr "Erreur durant le processus de paiement. Merci d'attendre un moment puis d'essayer à nouveau. Si le problème persiste pendant plus de 24 heures, merci de nous contacter à %(email)s avec une capture d'écran."

msgid "page.comments.hidden_comment"
msgstr "commentaire caché"

msgid "page.comments.file_issue"
msgstr "Problème du fichier : %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Meilleure version"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Souhaitez-vous signaler cet utilisateur pour comportement abusif ou inapproprié ?"

msgid "page.comments.report_abuse"
msgstr "Signaler un abus"

msgid "page.comments.abuse_reported"
msgstr "Abus signalé :"

msgid "page.comments.reported_abuse_this_user"
msgstr "Vous avez signalé cet utilisateur pour abus."

msgid "page.comments.reply_button"
msgstr "Répondre"

msgid "page.md5.quality.logged_out_login"
msgstr "Veuillez <a %(a_login)s>vous connecter</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Vous avez bien laissé votre commentaire. Cela peut prendre une minute pour qu'il apparaisse."

msgid "page.md5.quality.comment_error"
msgstr "Quelque chose s'est mal passé. Veuillez recharger la page et réessayer."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s pages affectées"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visible dans Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visible dans Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visible dans Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marqué comme endommagé dans Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Manquant sur Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marqué comme \"spam\" dans Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marqué comme \"mauvais fichier\" dans Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Certaines pages n'ont pas pu être converties en PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "L'exécution de exiftool a échoué sur ce fichier"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Livre (inconnu)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Livre (non-fiction)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Livre (fiction)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Article de journal"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documents standards"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Magazine"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Bande-dessinée"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partition de musique"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Livre audio"

msgid "common.md5_content_type_mapping.other"
msgstr "Autre"

msgid "common.access_types_mapping.aa_download"
msgstr "Téléchargement depuis un Serveur Partenaire"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Téléchargement externe"

msgid "common.access_types_mapping.external_borrow"
msgstr "Emprunt externe"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Emprunt externe (version \"print-disabled\")"

msgid "common.access_types_mapping.meta_explore"
msgstr "Explorer les métadonnées"

msgid "common.access_types_mapping.torrents_available"
msgstr "Disponible dans les torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library chinois"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Téléversements vers AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "Index des eBooks EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Métadonnées tchèques"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Livres (Google Books)"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Bibliothèque d'État de Russie"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Titre"

msgid "common.specific_search_fields.author"
msgstr "Auteur"

msgid "common.specific_search_fields.publisher"
msgstr "Maison d'édition"

msgid "common.specific_search_fields.edition_varia"
msgstr "Édition"

msgid "common.specific_search_fields.year"
msgstr "Année de publication"

msgid "common.specific_search_fields.original_filename"
msgstr "Nom du fichier d'origine"

msgid "common.specific_search_fields.description_comments"
msgstr "Description et commentaires dans les métadonnées"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Téléchargements depuis un Serveur Partenaire temporairement indisponibles pour ce fichier."

msgid "common.md5.servers.fast_partner"
msgstr "Serveur Partenaire Rapide #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recommandé)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(pas de vérification du navigateur ou de listes d'attente)"

msgid "common.md5.servers.slow_partner"
msgstr "Serveur Partenaire lent #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(légèrement plus rapide, mais avec une liste d'attente)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(pas de liste d'attente, mais peut être très lent)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub : %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(cliquez ensuite sur \"GET\" en haut de la page)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(cliquez sur \"GET\" en haut de la page)"

msgid "page.md5.box.download.libgen_ads"
msgstr "leurs publicités sont connues pour contenir des logiciels malveillants, utilisez donc un bloqueur de publicités ou ne cliquez pas dessus"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Les fichiers Nexus/STC peuvent avoir un téléchargement instable)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library avec TOR"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(nécessite le Navigateur TOR)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Emprunter à Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(réservé aux comptes vérifiés \"print-disabled\")"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(le DOI associé peut ne pas être disponible dans Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "collection"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Téléchargements torrent en masse"

msgid "page.md5.box.download.experts_only"
msgstr "(spécialistes uniquement)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Rechercher par ISBN sur les Archives d'Anna"

msgid "page.md5.box.download.other_isbn"
msgstr "Rechercher par ISBN sur d'autres bases de données"

msgid "page.md5.box.download.original_isbndb"
msgstr "Trouver l'archive d'origine sur ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Rechercher par Open Library ID sur les Archives d'Anna"

msgid "page.md5.box.download.original_openlib"
msgstr "Trouver l'archive d'origine sur Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Rechercher par numéro OCLC (WorldCat) sur les Archives d'Anna"

msgid "page.md5.box.download.original_oclc"
msgstr "Rechercher l'archive originale dans WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Rechercher par numéro SSID DuXiu sur les Archives d'Anna"

msgid "page.md5.box.download.original_duxiu"
msgstr "Rechercher manuellement sur DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Rechercher par numéro CADAL SSNO sur les Archives d'Anna"

msgid "page.md5.box.download.original_cadal"
msgstr "Retrouver l'archive originelle sur CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Rechercher par nombre DXID DuXiu sur les Archives d'Anna"

msgid "page.md5.box.download.edsebk"
msgstr "Index des eBooks EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(aucune vérification de navigateur requise)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Méta-données tchèques %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Livres %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Métadonnées"

msgid "page.md5.box.descr_title"
msgstr "description"

msgid "page.md5.box.alternative_filename"
msgstr "Nom de fichier alternatif"

msgid "page.md5.box.alternative_title"
msgstr "Titre alternatif"

msgid "page.md5.box.alternative_author"
msgstr "Auteur alternatif"

msgid "page.md5.box.alternative_publisher"
msgstr "Éditeur alternatif"

msgid "page.md5.box.alternative_edition"
msgstr "Édition alternative"

msgid "page.md5.box.alternative_extension"
msgstr "Extension alternative"

msgid "page.md5.box.metadata_comments_title"
msgstr "commentaires dans les métadonnées"

msgid "page.md5.box.alternative_description"
msgstr "Description alternative"

msgid "page.md5.box.date_open_sourced_title"
msgstr "date de libération publique"

msgid "page.md5.header.scihub"
msgstr "Fichier Sci-Hub \"%(id)s\""

msgid "page.md5.header.ia"
msgstr "Fichier de prêt numérique (Controlled Digital Lending) de l'Internet Archive “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Ceci est l'archive d'un fichier provenant de l'Internet Archive, et non un fichier en téléchargement direct. Vous pouvez essayer d'emprunter ce livre (lien ci-dessous), ou d'utiliser cette URL <a %(a_request)s> lorsque vous faites une demande de fichier</a>."

msgid "page.md5.header.consider_upload"
msgstr "Si vous possédez ce document et qu'il n'est pas encore disponible sur les Archives d'Anna, envisagez de <a %(a_request)s>le téléverser</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Archive de métadonnées de l'ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Archive de métadonnées de l'Open Library ID %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Archive de métadonnées du numéro d'OCLC (WorldCat) %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Archive de métadonnées du SSID DuXiu %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Archive de métadonnées du CADAL SSNO %(id)s"

msgid "page.md5.header.meta_magzdb_id"
msgstr "Archive de métadonnées MagzDB ID %(id)s"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Archive de métadonnées Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Ceci est une archive de métadonnées, pas un fichier téléchargeable. Vous pouvez utiliser cette URL <a %(a_request)s>lorsque vous faites une demande de fichier</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Métadonnées de l'archive liée"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Améliorer les métadonnées sur Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Attention : plusieurs archives liées :"

msgid "page.md5.header.improve_metadata"
msgstr "Améliorer les métadonnées"

msgid "page.md5.text.report_quality"
msgstr "Signaler la qualité du fichier"

msgid "page.search.results.download_time"
msgstr "Temps de téléchargement"

msgid "page.md5.codes.url"
msgstr "URL :"

msgid "page.md5.codes.website"
msgstr "Site internet :"

msgid "page.md5.codes.aa_abbr"
msgstr "AA :"

msgid "page.md5.codes.aa_search"
msgstr "Rechercher \"%(name)s\" sur les Archives d'Anna"

msgid "page.md5.codes.code_explorer"
msgstr "Explorateur de Codes :"

msgid "page.md5.codes.code_search"
msgstr "Voir dans l'Explorateur de Codes “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Lire plus…"

msgid "page.md5.tabs.downloads"
msgstr "Téléchargement (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Emprunter (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Explorer les métadonnées (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Commentaires (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listes (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistiques (%(count)s)"

msgid "common.tech_details"
msgstr "Afficher les détails techniques"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Ce fichier semble être problématique et a été retiré de la bibliothèque-source. </span> Parfois, c'est à la demande d'un détenteur de droits d'auteur ou encore parce qu'une meilleure alternative est disponible, ou bien car le fichier est corrompu. Le télécharger ne devrait sans doute pas vous poser de problèmes, mais nous préférons vous recommander de chercher une alternative à ce fichier. Plus de détails :"

msgid "page.md5.box.download.better_file"
msgstr "Une meilleure version de ce fichier est peut-être disponible sur %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Si vous voulez toujours télécharger le fichier, soyez sûr d'utiliser un logiciel de confiance et mis à jour pour l'ouvrir."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Téléchargements rapides"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Téléchargements rapides</strong> Devenez <a %(a_membership)s>membre</a> pour soutenir la préservation à long terme des livres, des documents, etc. Pour vous remercier de votre soutien, vous bénéficiez de téléchargements rapides. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si vous faites un don ce mois-ci, vous obtenez <strong>le double</strong> du nombre de téléchargements rapides."

msgid "page.md5.box.download.header_fast_member"
msgstr "Il vous en reste %(remaining)s aujourd'hui. Merci d'être un membre ! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Vous avez épuisé votre quantité de téléchargements rapides pour aujourd'hui."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Vous avez téléchargé ce fichier récemment. Les liens restent valides pendant un moment."

msgid "page.md5.box.download.option"
msgstr "Option #%(num)d : %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(pas de redirection)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(ouvrir dans le visualiseur)"

msgid "layout.index.header.banner.refer"
msgstr "Parrainez un ami, et bénéficiez tous les deux de %(percentage)s%% de téléchargements rapides en plus !"

msgid "layout.index.header.learn_more"
msgstr "En savoir plus…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Téléchargements lents"

msgid "page.md5.box.download.trusted_partners"
msgstr "Depuis nos partenaires de confiance."

msgid "page.md5.box.download.slow_faq"
msgstr "Plus d'informations dans la <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(peut nécessiter une <a %(a_browser)s>vérification du navigateur</a> — téléchargements illimités !)"

msgid "page.md5.box.download.after_downloading"
msgstr "Après le téléchargement :"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Ouvrir dans notre visualiseur"

msgid "page.md5.box.external_downloads"
msgstr "afficher les téléchargements externes"

msgid "page.md5.box.download.header_external"
msgstr "Téléchargements externes"

msgid "page.md5.box.download.no_found"
msgstr "Aucun téléchargement trouvé."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Toutes les options de téléchargement devraient pouvoir être utilisées en toute sécurité. Cela dit, soyez toujours prudent lorsque vous téléchargez des fichiers depuis internet. Par exemple, veillez à maintenir vos appareils à jour."

msgid "page.md5.box.download.dl_managers"
msgstr "Pour les fichiers volumineux, nous recommandons d'utiliser un gestionnaire de téléchargements pour éviter les interruptions."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Gestionnaires de téléchargements recommandés : %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Vous aurez besoin d'un lecteur d'ebook ou de PDF pour ouvrir le fichier, selon le format du fichier."

msgid "page.md5.box.download.readers.links"
msgstr "Lecteurs d'ebooks recommandés : %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Visualiseur en ligne d'Anna's Archive"

msgid "page.md5.box.download.conversion"
msgstr "Utilisez des outils en ligne pour convertir les formats."

msgid "page.md5.box.download.conversion.links"
msgstr "Outils de conversion recommandés : %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Vous pouvez envoyer des fichiers PDF et EPUB à votre Kindle ou à votre eReader Kobo."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Outils recommandés : %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "La fonction « Envoyer vers Kindle » d'Amazon"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "La fonction « Envoyer vers Kobo/Kindle » de djazz"

msgid "page.md5.box.download.support"
msgstr "Soutenez les auteurs et les bibliothèques"

msgid "page.md5.box.download.support.authors"
msgstr "Si vous aimez cela et que vous en avez les moyens, envisagez d'acheter l'original ou de soutenir directement les auteurs."

msgid "page.md5.box.download.support.libraries"
msgstr "Si cela est disponible dans votre bibliothèque locale, envisagez de l'emprunter gratuitement là-bas."

msgid "page.md5.quality.header"
msgstr "Qualité du fichier"

msgid "page.md5.quality.report"
msgstr "Aidez la communauté en signalant la qualité de ce fichier ! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Signaler un problème de fichier (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Excellente qualité du fichier (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Ajouter un commentaire (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Quel est le problème de ce fichier ?"

msgid "page.md5.quality.copyright"
msgstr "Veuillez utiliser le <a %(a_copyright)s>formulaire de réclamation DMCA / Droits d'auteur</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Décrire le problème (obligatoire)"

msgid "page.md5.quality.issue_description"
msgstr "Description du problème"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 d'une meilleure version de ce fichier (si applicable)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Remplissez ceci s'il existe un autre fichier qui correspond étroitement à ce fichier (même édition, même extension de fichier si vous pouvez en trouver un), que les gens devraient utiliser à la place de ce fichier. Si vous connaissez une meilleure version de ce fichier en dehors de l’Archive d’Anna, veuillez <a %(a_upload)s>la téléverser</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Vous pouvez obtenir le md5 à partir de l'URL, par exemple."

msgid "page.md5.quality.submit_report"
msgstr "Envoyer le signalement"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Apprenez comment <a %(a_metadata)s>améliorer les métadonnées</a> de ce fichier vous-même."

msgid "page.md5.quality.report_thanks"
msgstr "Merci d'avoir envoyé votre signalement. Il sera affiché sur cette page et examiné manuellement par Anna (jusqu'à ce que nous ayons un système de modération approprié)."

msgid "page.md5.quality.report_error"
msgstr "Quelque chose s'est mal passé. Veuillez recharger la page et réessayer."

msgid "page.md5.quality.great.summary"
msgstr "Si ce fichier est de grande qualité, vous pouvez en discuter ici ! Sinon, veuillez utiliser le bouton « Signaler un problème de fichier »."

msgid "page.md5.quality.loved_the_book"
msgstr "J'ai adoré ce livre !"

msgid "page.md5.quality.submit_comment"
msgstr "Laisser un commentaire"

msgid "common.english_only"
msgstr "Le texte continue ci-dessous en anglais."

msgid "page.md5.text.stats.total_downloads"
msgstr "Nombre total de téléchargements : %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Un « fichier MD5 » est un hash calculé à partir du contenu du fichier, et est unique en fonction de ce contenu. Toutes les bibliothèques fantômes que nous avons indexées ici utilisent principalement les MD5 pour identifier les fichiers."

msgid "page.md5.text.md5_info.text2"
msgstr "Un fichier peut apparaître dans plusieurs bibliothèques fantômes. Pour des informations sur les différents datasets que nous avons compilés, consultez la <a %(a_datasets)s>page des Datasets</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Ceci est un fichier géré par la bibliothèque <a %(a_ia)s>Prêt Numérique Contrôlé de l'IA</a>, et indexé par Anna’s Archive pour la recherche. Pour des informations sur les différents jeux de donnés que nous avons compilés, consultez la <a %(a_datasets)s>page des Jeux de données</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Pour plus d'informations sur ce fichier en particulier, consultez son <a %(a_href)s>fichier JSON</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Problème lors du chargement de cette page"

msgid "page.aarecord_issue.text"
msgstr "Veuillez actualiser pour réessayer. <a %(a_contact)s>Contactez-nous</a> si le problème persiste pendant plusieurs heures."

msgid "page.md5.invalid.header"
msgstr "Aucun résultat"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” n'a pas été trouvé dans notre base de données."

msgid "page.login.title"
msgstr "Se connecter / S'inscrire"

msgid "page.browserverification.header"
msgstr "Vérification du navigateur"

msgid "page.login.text1"
msgstr "Afin d'empêcher des robots de créer trop de comptes, nous devons d'abord vérifier votre navigateur."

msgid "page.login.text2"
msgstr "Si vous êtes pris dans une boucle infinie, nous vous recommandons d'installer <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Il peut également être utile de désactiver les bloqueurs de publicités et autres extensions de navigateur."

msgid "page.codes.title"
msgstr "Codes"

msgid "page.codes.heading"
msgstr "Explorateur de codes"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explorez les codes avec lesquels les enregistrements sont étiquetés, par préfixe. La colonne « enregistrements » montre le nombre d'enregistrements étiquetés avec des codes ayant le préfixe donné, comme vu dans le moteur de recherche (y compris les enregistrements uniquement de métadonnées). La colonne « codes » montre combien de codes réels ont un préfixe donné."

msgid "page.codes.why_cloudflare"
msgstr "Cette page peut prendre un certain temps à se générer, c'est pourquoi elle nécessite un captcha Cloudflare. <a %(a_donate)s>Les membres</a> peuvent passer ce captcha."

msgid "page.codes.dont_scrape"
msgstr "Veuillez ne pas scraper ces pages. Nous vous recommandons plutôt de <a %(a_import)s>générer</a> ou de <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB, et d'exécuter notre <a %(a_software)s>code source ouvert</a>. Les données brutes peuvent être explorées manuellement via des fichiers JSON tels que <a %(a_json_file)s>celui-ci</a>."

msgid "page.codes.prefix"
msgstr "Préfixe"

msgid "common.form.go"
msgstr "Aller"

msgid "common.form.reset"
msgstr "Réinitialiser"

msgid "page.codes.search_archive_start"
msgstr "Rechercher dans Anna's Archive"

msgid "page.codes.bad_unicode"
msgstr "Avertissement : le code contient des caractères Unicode incorrects et peut se comporter de manière incorrecte dans diverses situations. Le binaire brut peut être décodé à partir de la représentation base64 dans l'URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Préfixe de code connu « %(key)s »"

msgid "page.codes.code_prefix"
msgstr "Préfixe"

msgid "page.codes.code_label"
msgstr "Étiquette"

msgid "page.codes.code_description"
msgstr "Description"

msgid "page.codes.code_url"
msgstr "URL pour un code spécifique"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "Les « %%s » seront remplacés par la valeur du code"

msgid "page.codes.generic_url"
msgstr "URL générique"

msgid "page.codes.code_website"
msgstr "Site web"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s archive correspondant à \"%(prefix_label)s\""
msgstr[1] "%(count)s archives correspondant à \"%(prefix_label)s\""

msgid "page.codes.url_link"
msgstr "URL pour un code spécifique : « %(url)s »"

#, fuzzy
msgid "page.codes.more"
msgstr "Plus…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes commençant par « %(prefix_label)s »"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index de"

msgid "page.codes.records_prefix"
msgstr "archives"

msgid "page.codes.records_codes"
msgstr "codes"

msgid "page.codes.fewer_than"
msgstr "Moins de %(count)s archives"

msgid "page.contact.dmca.form"
msgstr "Pour les réclamation des droits d'auteur et autres DMCA, remplissez <a %(a_copyright)s>ce formulaire</a>."

msgid "page.contact.dmca.delete"
msgstr "Toute autre manière de nous contacter pour une réclamation de droits d'auteur conduira à la suppression automatique du message concerné."

msgid "page.contact.checkboxes.text1"
msgstr "Nous accueillons avec plaisir tous vos retours et suggestions !"

msgid "page.contact.checkboxes.text2"
msgstr "Cependant, compte tenu de la quantité de spam et de courriels insensés que nous recevons, veuillez cocher les cases ci-contre pour nous confirmer avoir compris les conditions posées pour nous contacter."

msgid "page.contact.checkboxes.copyright"
msgstr "Toute réclamation de droits d'auteur à cette adresse mail sera ignorée ; veuillez utiliser le formulaire."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Les serveurs partenaires sont indisponibles en raison de fermetures de l'hébergement. Ils devraient bientôt être de nouveau opérationnels."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Les adhésions seront prolongées en conséquence."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Ne nous envoyez pas d'e-mail pour une <a %(a_request)s>demande de livre</a><br>ou de <a %(a_upload)s>téléversement</a> d'un petit fichier (<10k)."

msgid "page.donate.please_include"
msgstr "Lorsque vous posez des questions sur le compte ou les dons, ajoutez votre identifiant de compte, des captures d'écran, reçus, autant d'informations que possible. Nous ne vérifions nos e-mails que toutes les 1-2 semaines, donc ne pas inclure ces informations retardera toute résolution."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Montrer l'adresse mail"

msgid "page.copyright.title"
msgstr "Formulaire de réclamation des droits d'auteur (DMCA)"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si vous avez une réclamation de droits d'auteurs (DMCA) ou autre réclamation similaire, veuillez remplir ce formulaire aussi précisément que possible. Si vous rencontrez des problèmes, veuillez nous contacter à notre adresse DMCA dédiée : %(email)s. Notez qu'aucune réclamation envoyée par e-mail à cette adresse ne sera traitée, elle est uniquement destinée aux questions. Veuillez utiliser le formulaire ci-dessous pour soumettre vos réclamations."

msgid "page.copyright.form.aa_urls"
msgstr "URLs sur Anna's Archive (obligatoire). Une par ligne. Veuillez inclure uniquement les URLs qui décrivent exactement la même édition d'un livre. Si vous souhaitez faire une réclamation pour plusieurs livres ou plusieurs éditions, veuillez soumettre ce formulaire plusieurs fois."

msgid "page.copyright.form.aa_urls.note"
msgstr "Les réclamations regroupant plusieurs livres ou éditions seront rejetées."

msgid "page.copyright.form.name"
msgstr "Votre nom (obligatoire)"

msgid "page.copyright.form.address"
msgstr "Adresse (obligatoire)"

msgid "page.copyright.form.phone"
msgstr "Numéro de téléphone (obligatoire)"

msgid "page.copyright.form.email"
msgstr "E-mail (obligatoire)"

msgid "page.copyright.form.description"
msgstr "Description claire du document source (obligatoire)"

msgid "page.copyright.form.isbns"
msgstr "ISBNs des documents sources (si applicable). Un par ligne. Veuillez inclure uniquement ceux qui correspondent exactement à l'édition pour laquelle vous signalez une réclamation de droits d'auteur."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs du document source, une par ligne. Veuillez prendre un moment pour rechercher votre document source sur Open Library. Cela nous aidera à vérifier votre réclamation."

msgid "page.copyright.form.external_urls"
msgstr "URLs vers le document source, une par ligne (obligatoire). Veuillez en inclure autant que possible, pour nous aider à vérifier votre réclamation (par exemple, Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Déclaration et signature (obligatoire)"

msgid "page.copyright.form.submit_claim"
msgstr "Soumettre la réclamation"

msgid "page.copyright.form.on_success"
msgstr "✅ Merci d'avoir soumis votre réclamation de droits d'auteur. Nous l'examinerons dès que possible. Veuillez recharger la page pour en soumettre une autre."

msgid "page.copyright.form.on_failure"
msgstr "❌ Une erreur s'est produite. Veuillez recharger la page et réessayer."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "Si vous êtes intéressé par la réplication de cet ensemble de données à des fins d’<a %(a_archival)s>archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter."

msgid "page.datasets.intro.text2"
msgstr "Notre mission est d'archiver tous les livres du monde (ainsi que les articles, magazines, etc.), et de les rendre largement accessibles. Nous croyons que tous les livres devraient être dupliqués autant que possible pour assurer leur redondance et leur résilience. C'est pourquoi nous regroupons des fichiers provenant de diverses sources. Certaines sources sont complètement ouvertes et peuvent être dupliquées en masse (comme Sci-Hub). D'autres sont fermées et protectrices, nous essayons donc de les scraper pour « libérer » leurs livres. D'autres encore se situent quelque part entre les deux."

msgid "page.datasets.intro.text3"
msgstr "Toutes nos données peuvent être <a %(a_torrents)s>téléchargées via torrent</a>, et toutes nos métadonnées peuvent être <a %(a_anna_software)s>générées</a> ou <a %(a_elasticsearch)s>téléchargées</a> sous forme de bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement via des fichiers JSON tels que <a %(a_dbrecord)s>celui-ci</a>."

msgid "page.datasets.overview.title"
msgstr "Aperçu"

msgid "page.datasets.overview.text1"
msgstr "Vous trouverez ci-dessous un aperçu rapide des sources des fichiers sur Anna’s Archive."

msgid "page.datasets.overview.source.header"
msgstr "Source"

msgid "page.datasets.overview.size.header"
msgstr "Taille"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% dupliqué par AA / torrents disponibles"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Pourcentages du nombre de fichiers"

msgid "page.datasets.overview.last_updated.header"
msgstr "Dernière mise à jour"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction et Fiction"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s fichier"
msgstr[1] "%(count)s fichiers"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li « scimag »"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub : gelé depuis 2021 ; la plupart disponibles via torrents"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li : ajouts mineurs depuis</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Exclure \"scimag\""

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Les torrents de fiction sont en retard (bien que les IDs ~4-6M ne soient pas torrentés car ils se chevauchent avec nos torrents Zlib)."

msgid "page.datasets.zlibzh.searchable"
msgstr "La collection « chinoise » dans Z-Library semble être la même que notre collection DuXiu, mais avec des MD5s différents. Nous excluons ces fichiers des torrents pour éviter les doublons, mais nous les affichons toujours dans notre index de recherche."

msgid "common.record_sources_mapping.iacdl"
msgstr "Prêt numérique contrôlé par Internet Archive (Controlled Digital Lending)"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ des fichiers sont disponibles à la recherche."

msgid "page.datasets.overview.total"
msgstr "Total"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Doublons exclus"

msgid "page.datasets.overview.text4"
msgstr "Comme les bibliothèques fantômes synchronisent souvent leurs données entre elles, il y a un chevauchement considérable entre elles. C'est pourquoi les chiffres ne correspondent pas au total."

msgid "page.datasets.overview.text5"
msgstr "Le pourcentage de « sites miroirs et de seeding effectués par Anna's Archive » montre combien de fichiers nous reproduisons nous-mêmes. Nous semons ces fichiers en masse au travers des torrents et les rendons disponibles en téléchargement direct via des sites partenaires."

msgid "page.datasets.source_libraries.title"
msgstr "Bibliothèques sources"

msgid "page.datasets.source_libraries.text1"
msgstr "Certaines bibliothèques sources encouragent le partage massif de leurs données via des torrents, tandis que d'autres ne partagent pas facilement leur collection. Dans ce dernier cas, Anna’s Archive essaie de scraper leurs collections et de les rendre disponibles (voir notre page <a %(a_torrents)s>Torrents</a>). Il existe également des situations intermédiaires, par exemple, où les bibliothèques sources sont disposées à partager, mais n'ont pas les ressources pour le faire. Dans ces cas, nous essayons également d'aider."

msgid "page.datasets.source_libraries.text2"
msgstr "Vous trouverez ci-dessous un aperçu de la manière dont nous interagissons avec les différentes bibliothèques sources."

msgid "page.datasets.sources.source.header"
msgstr "Source"

msgid "page.datasets.sources.files.header"
msgstr "Fichiers"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dépôt de base de données HTTP</a> journalier"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatisés pour la <a %(nonfiction)s>Non-Fiction</a> et la <a %(fiction)s>Fiction</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Les Archives d'Anna gère une liste de <a %(covers)s>torrents de couvertures de livres</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen « scimag »"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub a gelé le téléversement de nouveaux fichiers depuis 2021."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dépôts de métadonnées disponibles <a %(scihub1)s>ici</a> et <a %(scihub2)s>ici</a>, font aussi partie de la <a %(libgenli)s>base de données Libgen.li</a> (que nous utilisons)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents de données disponibles <a %(scihub1)s>ici</a>, <a %(scihub2)s>ici</a>, et <a %(libgenli)s>ici</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Certains nouveaux fichiers sont <a %(libgenrs)s>en cours</a> <a %(libgenli)s>d'ajout</a> à la section \"scimag\" de Libgen’s, mais pas assez pour justifier un nouveau torrent"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dépôts de base de donnée HTTP</a> trimestriels"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Les torrents de Non-Fiction sont partagés avec Libgen.rs (et miroirés <a %(libgenli)s>ici</a>)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna's Archive et Libgen.li gèrent ensemble des collections de <a %(comics)s>bandes dessinées et comics</a>, <a %(magazines)s>magazines</a>, <a %(standarts)s>documents standards</a>, et <a %(fiction)s>fiction (dérivée de Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Leur collection « fiction_rus » (fiction russe) n'a pas de torrents dédiés, mais est couverte par des torrents d'autres sources, et nous maintenons un <a %(fiction_rus)s>site miroir</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Les Archives d'Anna et Z-Library gèrent une collection de <a %(metadata)s>métadonnées Z-Library</a> et de <a %(files)s>fichiers Z-Library</a> en collaboration"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Certaines métadonnées disponibles via <a %(openlib)s>les dépôts de base de données Open Library</a>, mais ceux-ci ne couvrent pas l'entierté de la collection de l'Internet Archive"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Pas de dépôts de métadonnées facilement accessibles pour la totalité de leur collection"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Les Archives d'Anna gèrent une collection de <a %(ia)s>métadonnées Internet Archive</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Fichiers seulement disponibles au prêt de manière limitée, avec restrictions d'accès variées"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Les Archives d'Anna gèrent une collection de <a %(ia)s>fichiers Internet Archive</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s De multiples bases de données de métadonnées dispersée à travers l'internet chinois ; mais souvent payantes"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Aucun dump de métadonnées n'est facilement accessible pour leur collection entière."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’s Archive gère une collection de <a %(duxiu)s>métadonnées DuXiu</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Diverses bases de données de fichiers dispersées sur l'internet chinois ; souvent des bases de données payantes"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La plupart des fichiers ne sont accessibles qu'avec des comptes premium BaiduYun ; vitesses de téléchargement lentes."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Archive gère une collection de <a %(duxiu)s>fichiers DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Diverses sources plus petites ou ponctuelles. Nous encourageons les gens à téléverser d'abord dans d'autres bibliothèques fantômes, mais parfois les gens ont des collections trop grandes pour que d'autres puissent les trier, bien que pas assez grandes pour justifier leur propre catégorie."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Sources de métadonnées uniquement"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Nous enrichissons également notre collection avec des sources uniquement de métadonnées, que nous pouvons associer à des fichiers, par exemple en utilisant des numéros ISBN ou d'autres champs. Vous trouverez ci-dessous un aperçu de celles-ci. Encore une fois, certaines de ces sources sont complètement ouvertes, tandis que pour d'autres, nous devons les scraper."

msgid "page.faq.metadata.inspiration"
msgstr "Notre inspiration pour collecter des métadonnées est Aaron Swartz et son objectif d'« une seule page web pour chaque livre jamais publié », pour lequel il a créé <a %(a_openlib)s>Open Library</a>. Ce projet a accompli beaucoup, mais notre position unique nous permet d'obtenir des métadonnées qu'ils ne peuvent pas. Une autre inspiration était notre désir de savoir <a %(a_blog)s>combien de livres il y a dans le monde</a>, afin que nous puissions calculer combien de livres il nous reste à sauver."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Notez que dans la recherche de métadonnées, nous affichons les documents originaux. Nous n'en fusionnons aucun."

msgid "page.datasets.sources.last_updated.header"
msgstr "Dernière mise à jour"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Dumps de base de données <a %(dbdumps)s>mensuels</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Pas disponible directement en masse, protégé contre le scraping"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’s Archive gère une collection de <a %(worldcat)s>métadonnées OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Base de données unifiée"

msgid "page.datasets.unified_database.text1"
msgstr "Nous combinons toutes les sources mentionnées ci-dessus en une seule base de données unifiée que nous utilisons pour alimenter ce site web. Cette base de données unifiée n'est pas disponible directement, mais comme Anna’s Archive est entièrement open source, elle peut être assez facilement <a %(a_generated)s>générée</a> ou <a %(a_downloaded)s>téléchargée</a> sous forme de bases de données ElasticSearch ou MariaDB. Les scripts sur cette page téléchargeront automatiquement toutes les métadonnées requises des sources mentionnées ci-dessus."

msgid "page.datasets.unified_database.text2"
msgstr "Si vous souhaitez explorer nos données avant d'exécuter ces scripts localement, vous pouvez consulter nos fichiers JSON, qui renvoient à d'autres fichiers JSON. <a %(a_json)s>Ce fichier</a> est un bon point de départ."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adapté de notre <a %(a_href)s>article de blog</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> est une immense base de données de livres numérisés, créée par le <a %(superstar_link)s>SuperStar Digital Library Group</a>. La plupart sont des livres académiques, numérisés afin de les rendre disponibles numériquement aux universités et bibliothèques. Pour notre public anglophone, <a %(princeton_link)s>Princeton</a> et l'<a %(uw_link)s>Université de Washington</a> offrent de bons aperçus. Il y a aussi un excellent article donnant plus de contexte : <a %(article_link)s>« Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine »</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Les livres de Duxiu ont longtemps été piratés sur l'internet chinois. Ils sont généralement vendus pour moins d'un dollar par des revendeurs. Ils sont typiquement distribués en utilisant l'équivalent chinois de Google Drive, qui a souvent été piraté pour permettre plus d'espace de stockage. Quelques détails techniques peuvent être trouvés <a %(link1)s>ici</a> et <a %(link2)s>ici</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Bien que les livres aient été distribués de manière semi-publique, il est assez difficile de les obtenir en masse. Nous avions cela en haut de notre liste de tâches, et avons alloué plusieurs mois de travail à temps plein pour cela. Cependant, fin 2023, un bénévole incroyable, étonnant et talentueux nous a contactés, nous disant qu'il avait déjà fait tout ce travail — à grands frais. Ils ont partagé la collection complète avec nous, sans rien attendre en retour, sauf la garantie d'une préservation à long terme. Vraiment remarquable."

msgid "page.datasets.common.resources"
msgstr "Ressources"

msgid "page.datasets.common.total_files"
msgstr "Total des fichiers : %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Taille totale du fichier : %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Fichiers reproduits par Anna’s Archive : %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Dernière mise à jour : %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrents par Anna’s Archive"

msgid "page.datasets.common.aa_example_record"
msgstr "Exemple d'archive sur Anna’s Archive"

msgid "page.datasets.duxiu.blog_post"
msgstr "Notre article de blog sur ces données"

msgid "page.datasets.common.import_scripts"
msgstr "Scripts pour importer des métadonnées"

msgid "page.datasets.common.aac"
msgstr "Format Conteneurs d'Anna’s Archive"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Plus d'informations de nos bénévoles (notes brutes) :"

msgid "page.datasets.ia.title"
msgstr "Prêt numérique contrôlé par Internet Archive (Controlled Digital Lending)"

msgid "page.datasets.ia.description"
msgstr "Cet ensemble de données est étroitement lié à l'<a %(a_datasets_openlib)s>ensemble de données Open Library</a>. Il contient un scrape de toutes les métadonnées et une grande partie des fichiers de la bibliothèque de prêt numérique contrôlé par Internet Archive (Controlled Digital Lending). Les mises à jour sont publiées dans le <a %(a_aac)s>format Conteneur d'Anna's Archive</a>."

msgid "page.datasets.ia.description2"
msgstr "Ces archives sont directement référencées à partir de l'ensemble de données Open Library, mais contiennent également des archives qui ne sont pas dans Open Library. Nous avons également un certain nombre de fichiers de données extraits par des membres de la communauté au fil des ans."

msgid "page.datasets.ia.description3"
msgstr "La collection se compose de deux parties. Vous avez besoin des deux parties pour obtenir toutes les données (sauf les torrents remplacés, qui sont barrés sur la page des torrents)."

msgid "page.datasets.ia.part1"
msgstr "notre première version, avant que nous ne standardisions sur le <a %(a_aac)s>format des Conteneurs d'Anna’s Archive (AAC)</a>. Contient des métadonnées (en json et xml), des pdfs (des systèmes de prêt numérique acsm et lcpdf), et des vignettes de couverture."

msgid "page.datasets.ia.part2"
msgstr "nouvelles versions incrémentielles, utilisant le format AAC. Contient uniquement des métadonnées avec des horodatages après le 01-01-2023, puisque le reste est déjà couvert par « IA (Internet Archive) ». Également tous les fichiers pdf, cette fois des systèmes de prêt acsm et « bookreader » (lecteur web de l'Internet Archive). Bien que le nom ne soit pas exactement correct, nous continuons à peupler les fichiers bookreader dans la collection ia2_acsmpdf_files, car ils sont mutuellement exclusifs."

msgid "page.datasets.common.main_website"
msgstr "Site principal %(source)s"

msgid "page.datasets.ia.ia_lending"
msgstr "Bibliothèque de prêt numérique"

msgid "page.datasets.common.metadata_docs"
msgstr "Documentation des métadonnées (la plupart des champs)"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN Informations sur les pays"

msgid "page.datasets.isbn_ranges.text1"
msgstr "L'Agence internationale de l'ISBN publie régulièrement les plages qu'elle a attribuées aux agences nationales de l'ISBN. À partir de cela, nous pouvons déterminer à quel pays, région ou groupe linguistique appartient cet ISBN. Nous utilisons actuellement ces données indirectement, via la bibliothèque Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Ressources"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Dernière mise à jour : %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN Site web"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Métadonnées"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Pour l'historique des différentes bifurcations de Library Genesis, voir la page de <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li contient la plupart du même contenu et des mêmes métadonnées que Libgen.rs, mais possède quelques collections supplémentaires, à savoir des bandes dessinées et des comics, des magazines et des documents références. Il a également intégré <a %(a_scihub)s>Sci-Hub</a> dans ses métadonnées et son moteur de recherche, ce que nous utilisons pour notre base de données."

msgid "page.datasets.libgen_li.description3"
msgstr "Les métadonnées de cette bibliothèque sont disponibles librement <a %(a_libgen_li)s>sur libgen.li</a>. Cependant, ce serveur est lent et ne prend pas en charge la reprise des connexions interrompues. Les mêmes fichiers sont également disponibles sur <a %(a_ftp)s>un serveur FTP</a>, qui fonctionne mieux."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Des torrents sont disponibles pour la plupart des contenus supplémentaires, notamment des torrents pour les bandes dessinées et comics, magazines et des documents standards ont été publiés en collaboration avec Anna's Archive."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La collection de fiction a ses propres torrents (divergents de <a %(a_href)s>Libgen.rs</a>) à partir de %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Selon l'administrateur de Libgen.li, la collection « fiction_rus » (fiction russe) devrait être couverte par des torrents régulièrement publiés par <a %(a_booktracker)s>booktracker.org</a>, notamment les torrents <a %(a_flibusta)s>flibusta</a> et <a %(a_librusec)s>lib.rus.ec</a> (que nous reproduisons <a %(a_torrents)s>ici</a>, bien que nous n'ayons pas encore établi quels torrents correspondent à quels fichiers)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Les statistiques pour toutes les collections peuvent être trouvées <a %(a_href)s>sur le site de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "La non-fiction semble également avoir divergé, mais sans nouveaux torrents. Il semble que cela se soit produit depuis début 2022, bien que nous ne l'ayons pas vérifié."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certaines plages sans torrents (comme les plages de fiction f_3463000 à f_4260000) sont probablement des fichiers de Z-Library (ou d'autres doublons). Malgré tout nous souhaitons faire un peu de déduplication et créer des torrents pour les fichiers uniques de lgli dans ces plages-là."

msgid "page.datasets.libgen_li.description5"
msgstr "Notez que les fichiers torrent faisant référence à « libgen.is » sont explicitement des sites miroirs de <a %(a_libgen)s>Libgen.rs</a> (« .is » est un domaine différent utilisé par Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Une ressource utile pour utiliser les métadonnées est <a %(a_href)s>cette page</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de fiction sur Anna's Archive"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de bandes dessinées et de comics sur Anna's Archive"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de magazines sur Anna's Archive"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documents standards sur Anna's Archive"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de fiction russe sur Anna's Archive"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Métadonnées"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Métadonnées via FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informations sur les champs de métadonnées"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Reproduction d'autres torrents (et torrents uniques de fiction et de bandes dessinées et comics)"

msgid "page.datasets.libgen_li.forum"
msgstr "Forum de discussion"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Notre article de blog sur la sortie des bandes dessinées et comics"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "L'histoire rapide des différentes bifurcations de Library Genesis (ou « Libgen ») est qu'au fil du temps, les différentes personnes impliquées dans le projet Library Genesis se sont disputées et ont pris des chemins séparés."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La version « .fun » a été créée par le fondateur original. Elle est en cours de refonte vers une nouvelle version plus distribuée."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La version « .rs » a des données très similaires et publie le plus souvent sa collection en torrents groupés. Elle est grossièrement divisée en une section « fiction » et une section « non-fiction »."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Initialement sur « http://gen.lib.rus.ec »."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La version <a %(a_li)s> « .li »</a> possède une vaste collection de bandes dessinées et comics, ainsi que d'autres contenus, qui ne sont pas (encore) disponibles en téléchargement massif via torrents. Elle dispose d'une collection de torrents séparée de livres de fiction, et contient les métadonnées de <a %(a_scihub)s>Sci-Hub</a> dans sa base de données."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Selon ce <a %(a_mhut)s>post sur le forum</a>, Libgen.li était initialement hébergé sur « http://free-books.dontexist.com »."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> est en quelque sorte aussi une branche de Library Genesis, bien qu'ils aient utilisé un nom différent pour leur projet."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Cette page concerne la version « .rs ». Elle est connue pour publier régulièrement à la fois ses métadonnées et le contenu complet de son catalogue de livres. Sa collection de livres est divisée entre une partie fiction et une partie non-fiction."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Une ressource utile pour utiliser les métadonnées est <a %(a_metadata)s>cette page</a> (elle bloque les plages d'IP, un VPN peut être nécessaire)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Depuis mars 2024, de nouveaux torrents sont publiés dans <a %(a_href)s>ce fil de discussion du forum</a> (Les plages d'IP sont bloquées, un VPN pourrait être nécessaire)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de non-fiction sur Anna’s Archive"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de fiction sur Anna's Archive"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Métadonnées de Libgen.rs"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informations sur les champs de métadonnées de Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de non-fiction de Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de fiction de Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum de discussion de Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents d'Anna's Archive (couvertures de livres)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Notre blog à propos de la parution des couvertures de livres"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis est connu pour d'ores et déjà rendre généreusement ses données disponibles en masse via des torrents. Notre collection Libgen consiste en des données auxiliaires qu'ils ne publient pas directement, en partenariat avec eux. Un grand merci à tous ceux impliqués avec Library Genesis pour leur collaboration avec nous !"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Parution 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Cette <a %(blog_post)s>première parution</a> est assez petite : environ 300 Go de couvertures de livres de la branche Libgen.rs, à la fois de fiction et de non-fiction. Elles sont organisées de la même manière qu'elles apparaissent sur libgen.rs, par exemple :"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pour un livre de non-fiction."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pour un livre de fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Tout comme avec la collection Z-Library, nous les avons tous mis dans un gros fichier .tar, qui peut être monté en utilisant <a %(a_ratarmount)s>ratarmount</a> si vous souhaitez servir les fichiers directement."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> est une base de données propriétaire de l'organisation à but non lucratif <a %(a_oclc)s>OCLC</a>, qui regoupe des enregistrements de métadonnées provenant de bibliothèques du monde entier. Il s'agit probablement de la plus grande collection de métadonnées de bibliothèques au monde."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octobre 2023, première version :"

msgid "page.datasets.worldcat.description2"
msgstr "En octobre 2023, nous avons <a %(a_scrape)s>publié</a> une extraction complète de la base de données OCLC (WorldCat), au format <a %(a_aac)s>Conteneurs d'Anna's Archive</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrents par Anna's Archive"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Notre article de blog à propos de cette donnée"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library est un projet open source de l'Internet Archive visant à cataloguer tous les livres du monde. Il possède l'une des plus grandes opérations de numérisation de livres au monde et propose de nombreux livres en prêt numérique. Son catalogue de méta-données de livres est librement téléchargeable et est inclus sur les Archives d’Anna (bien qu'il ne soit actuellement pas dans la recherche, sauf si vous recherchez explicitement un ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Méta-données"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Version 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Il s'agit d'un dump de nombreux appels à isbndb.com en septembre 2022. Nous avons essayé de couvrir toutes les plages ISBN. Il s'agit d'environ 30,9 millions d'enregistrements. Sur leur site web, ils prétendent avoir en réalité 32,6 millions d'enregistrements, donc nous avons peut-être manqué certains, ou <em>ils</em> pourraient faire quelque chose de mal."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Les réponses JSON sont pratiquement brutes de leur serveur. Un problème de qualité des données que nous avons remarqué est que pour les numéros ISBN-13 qui commencent par un préfixe différent de « 978- », ils incluent toujours un champ « isbn » qui est simplement le numéro ISBN-13 avec les trois premiers chiffres supprimés (et le chiffre de contrôle recalculé). C'est évidemment incorrect, mais c'est ainsi qu'ils semblent le faire, donc nous ne l'avons pas modifié."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Un autre problème potentiel que vous pourriez rencontrer est que le champ « isbn13 » contient des doublons, ce qui empêche son utilisation comme clé primaire dans une base de données. En revanche, la combinaison des champs « isbn13 » et « isbn » semble être unique."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Pour en savoir plus sur Sci-Hub, veuillez consulter son <a %(a_scihub)s>site officiel</a>, sa <a %(a_wikipedia)s>page Wikipedia</a> et cette <a %(a_radiolab)s>interview en podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Notez que Sci-Hub a été <a %(a_reddit)s>gelé depuis 2021</a>. Il avait été gelé auparavant, mais en 2021, quelques millions d'articles ont été ajoutés. Cependant, un nombre limité d'articles continue d'être ajouté aux collections “scimag” de Libgen, bien que cela ne justifie pas de nouveaux torrents en masse."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Nous utilisons les métadonnées de Sci-Hub fournies par <a %(a_libgen_li)s>Libgen.li</a> dans sa collection “scimag”. Nous utilisons également le dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Notez que les torrents “smarch” sont <a %(a_smarch)s>obsolètes</a> et ne sont donc pas inclus dans notre liste de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents sur l’Archive d’Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Métadonnées et torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents sur Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents sur Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Mises à jour sur Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Page Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Interview en podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Téléversements vers Anna’s Archive"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Aperçu de la <a %(a1)s>page des datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Diverses sources plus petites ou ponctuelles. Nous encourageons les gens à téléverser d'abord dans d'autres bibliothèques de l'ombre, mais parfois les gens ont des collections trop grandes pour que d'autres puissent les trier, bien que pas assez grandes pour justifier leur propre catégorie."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La collection de “téléversements” est divisée en sous-collections plus petites, qui sont indiquées dans les AACIDs et les noms de torrents. Toutes les sous-collections ont d'abord été dédupliquées par rapport à la collection principale, bien que les fichiers JSON de métadonnées “upload_records” contiennent encore beaucoup de références aux fichiers originaux. Les fichiers non livres ont également été retirés de la plupart des sous-collections, et ne sont généralement <em>pas</em> notés dans les “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "De nombreuses sous-collections elles-mêmes sont composées de sous-sous-collections (par exemple, de différentes sources originales), qui sont représentées comme des répertoires dans les champs “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Les sous-collections sont :"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Sous-collection"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notes"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "parcourir"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "rechercher"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Semble être assez complet. De notre bénévole « cgiym »."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "D'un <a %(a_href)s><q>torrent ACM Digital Library 2020</q></a>. A un chevauchement assez important avec les collections de papiers existantes, mais très peu de correspondances MD5, nous avons donc décidé de le conserver entièrement."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Extraction de <q>iRead eBooks</q> (= phonétiquement <q>ai rit i-books</q>; airitibooks.com), par le bénévole <q>j</q>. Correspond à la metadata <q>airitibooks</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "D'une collection <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. En partie de la source originale, en partie de the-eye.eu, en partie d'autres miroirs."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "D'un site web privé de torrents de livres, <a %(a_href)s>Bibliotik</a> (souvent appelé « Bib »), dont les livres ont été regroupés en torrents par nom (A.torrent, B.torrent) et distribués via the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "De notre bénévole « bpb9v ». Pour plus d'informations sur <a %(a_href)s>CADAL</a>, voir les notes sur notre <a %(a_duxiu)s>page du dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Plus de notre bénévole « bpb9v », principalement des fichiers DuXiu, ainsi qu'un dossier « WenQu » et « SuperStar_Journals » (SuperStar est la société derrière DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "De notre bénévole « cgiym », textes chinois de diverses sources (représentés sous forme de sous-répertoires), y compris de <a %(a_href)s>China Machine Press</a> (un grand éditeur chinois)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Collections non-chinoises (représentées sous forme de sous-répertoires) de notre bénévole « cgiym »."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Extraction de livres sur l'architecture chinoise, par le bénévole <q>cm</q> : <q>Je l'ai obtenue en exploitant une vulnérabilité du réseau à la maison d'édition, mais cette faille a depuis été corrigée</q>. Correspond à la metadata <q>chinese_architecture</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Livres de la maison d'édition académique <a %(a_href)s>De Gruyter</a>, collectés à partir de quelques grands torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Grattage de <a %(a_href)s>docer.pl</a>, un site polonais de partage de fichiers axé sur les livres et autres œuvres écrites. Gratté fin 2023 par le bénévole « p ». Nous n'avons pas de bonnes métadonnées du site original (même pas les extensions de fichiers), mais nous avons filtré les fichiers ressemblant à des livres et avons souvent pu extraire des métadonnées des fichiers eux-mêmes."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, directement de DuXiu, collectés par le bénévole « w ». Seuls les livres récents de DuXiu sont disponibles directement via des ebooks, donc la plupart de ceux-ci doivent être récents."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Fichiers DuXiu restants du bénévole « m », qui n'étaient pas au format propriétaire PDG de DuXiu (le principal <a %(a_href)s>dataset DuXiu</a>). Collectés à partir de nombreuses sources originales, malheureusement sans conserver ces sources dans le chemin du fichier."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Extraction de livres érotiques, par le bénévole <q>do no harm</q>. Correspond à la metadata <q>hentai</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collection grattée d'un éditeur de manga japonais par le bénévole « t »."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archives judiciaires sélectionnées de Longquan</a>, fournies par le bénévole « c »."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Grattage de <a %(a_href)s>magzdb.org</a>, un allié de Library Genesis (il est lié sur la page d'accueil de libgen.rs) mais qui ne voulait pas fournir ses fichiers directement. Obtenu par le bénévole « p » fin 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Divers petits téléchargements, trop petits pour être leur propre sous-collection, mais représentés sous forme de répertoires."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks d'AvaxHome, un site russe de partage de fichiers."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archive de journaux et magazines. Correspond à la metadata <q>newsarch_magz</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Extraction du <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collection du bénévole « o » qui a collecté des livres polonais directement à partir des sites de sortie originaux (« scène »)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Collections combinées de <a %(a_href)s>shuge.org</a> par les bénévoles « cgiym » et « woz9ts »."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>« Bibliothèque Impériale de Trantor »</a> (nommée d'après la bibliothèque fictive), grattée en 2022 par le bénévole « t »."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sous-sous-collections (représentées sous forme de répertoires) du bénévole « woz9ts » : <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (par <a %(a_sikuquanshu)s>Dizhi(迪志)</a> à Taïwan), mebook (mebook.cc, 我的小书屋, ma petite bibliothèque — woz9ts : « Ce site se concentre principalement sur le partage de fichiers ebook de haute qualité, dont certains sont mis en page par le propriétaire lui-même. Le propriétaire a été <a %(a_arrested)s>arrêté</a> en 2019 et quelqu'un a fait une collection des fichiers qu'il a partagés. »)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Fichiers restants de DuXiu du volontaire « woz9ts », qui n’étaient pas au format propriétaire PDG de DuXiu (à convertir encore en PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents par l’Archive d’Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Scrape de Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library a ses racines dans la communauté <a %(a_href)s>Library Genesis</a>, et a initialement démarré avec leurs données. Depuis, elle s’est considérablement professionnalisée et dispose d’une interface beaucoup plus moderne. Ils sont donc capables de recevoir beaucoup plus de dons, tant monétaires pour continuer à améliorer leur site web, que de dons de nouveaux livres. Ils ont amassé une grande collection en plus de Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Mise à jour en février 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Fin 2022, les présumés fondateurs de Z-Library ont été arrêtés, et les domaines ont été saisis par les autorités américaines. Depuis, le site web revient lentement en ligne. On ne sait pas qui le gère actuellement."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La collection se compose de trois parties. Les pages de description originales pour les deux premières parties sont conservées ci-dessous. Vous avez besoin des trois parties pour obtenir toutes les données (sauf les torrents remplacés, qui sont barrés sur la page des torrents)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s : notre première version. C’était la toute première version de ce qui était alors appelé la « Pirate Library Mirror » (« pilimi »)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s : seconde version, cette fois incluant tous les fichiers emballés dans des fichiers .tar."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s : nouvelles versions incrémentielles, utilisant le <a %(a_href)s>format Anna’s Archive Containers (AAC)</a>, maintenant publié en collaboration avec l’équipe de Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents par Anna's Archive (métadonnées + contenu)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemple d’archive sur Anna's Archive (collection originale)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemple d’archive sur Anna's Archive (collection « zlib3 »)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Site principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domaine Tor"

msgid "page.datasets.zlib.blog.release1"
msgstr "Article de blog sur la Version 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Article de blog sur la Version 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Publications de Zlib (pages de description originales)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Publication 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Le site miroir initial a été obtenu laborieusement au cours des années 2021 et 2022. À ce stade, il est légèrement obsolète : il reflète l’état de la collection en juin 2021. Nous mettrons cela à jour à l’avenir. Pour l’instant, nous nous concentrons sur la parution de cette première publication."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Comme Library Genesis est déjà préservé avec des torrents publics et est inclus dans Z-Library, nous avons effectué une déduplication de base contre Library Genesis en juin 2022. Pour cela, nous avons utilisé des hachages MD5. Il est probable qu'il y ait beaucoup plus de contenu dupliqué dans la bibliothèque, comme plusieurs formats de fichiers pour le même livre. Cela est difficile à détecter avec précision, donc nous ne le faisons pas. Après la déduplication, il nous reste plus de 2 millions de fichiers, totalisant un peu moins de 7 To."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La collection se compose de deux parties : un dump MySQL “.sql.gz” des métadonnées, et les 72 fichiers torrent d'environ 50-100 Go chacun. Les métadonnées contiennent les données telles que rapportées par le site Z-Library (titre, auteur, description, type de fichier), ainsi que la taille réelle du fichier et le md5sum que nous avons observé, car parfois ces informations ne concordent pas. Il semble y avoir des plages de fichiers pour lesquelles Z-Library elle-même a des métadonnées incorrectes. Nous avons peut-être aussi téléchargé incorrectement des fichiers dans certains cas isolés, que nous essaierons de détecter et de corriger à l'avenir."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Les gros fichiers torrent contiennent les données réelles des livres, avec l'ID Z-Library comme nom de fichier. Les extensions de fichiers peuvent être reconstruites à l'aide du dump des métadonnées."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La collection est un mélange de contenu de fiction et de non-fiction (non séparé comme dans Library Genesis). La qualité est également très variable."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Cette première version est maintenant entièrement disponible. Notez que les fichiers torrent ne sont disponibles que via notre site miroir Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Version 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Nous avons récupéré tous les livres ajoutés à Z-Library entre notre dernier site miroir et août 2022. Nous avons également repris et récupéré certains livres que nous avions manqués la première fois. En tout, cette nouvelle collection fait environ 24 To. Encore une fois, cette collection est dédupliquée par rapport à Library Genesis, car il existe déjà des torrents disponibles pour cette collection."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Les données sont organisées de manière similaire à la première version. Il y a un dump MySQL “.sql.gz” des métadonnées, qui inclut également toutes les métadonnées de la première version, la remplaçant ainsi. Nous avons également ajouté de nouvelles colonnes :"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s : si ce fichier est déjà dans Library Genesis, dans la collection de non-fiction ou de fiction (correspondance par md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s : dans quel torrent se trouve ce fichier."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s : défini lorsque nous n'avons pas pu télécharger le livre."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Nous l'avons mentionné la dernière fois, mais juste pour clarifier : “filename” et “md5” sont les propriétés réelles du fichier, tandis que “filename_reported” et “md5_reported” sont ce que nous avons récupéré de Z-Library. Parfois, ces deux informations ne concordent pas, donc nous avons inclus les deux."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pour cette version, nous avons changé le classement en “utf8mb4_unicode_ci”, qui devrait être compatible avec les anciennes versions de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Les fichiers de données sont similaires à la dernière fois, bien qu'ils soient beaucoup plus gros. Nous n'avons tout simplement pas pris la peine de créer des tonnes de petits fichiers torrent. “pilimi-zlib2-0-14679999-extra.torrent” contient tous les fichiers que nous avons manqués dans la dernière version, tandis que les autres torrents sont tous de nouvelles plages d'ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Mise à jour %(date)s:</strong> Nous avons fait la plupart de nos torrents trop gros, ce qui a causé des difficultés aux clients torrent. Nous les avons supprimés et avons publié de nouveaux torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Mise à jour %(date)s:</strong> Il y avait encore trop de fichiers, donc nous les avons enveloppés dans des fichiers tar et avons publié de nouveaux torrents à nouveau."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Version 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Il s'agit d'un seul fichier torrent supplémentaire. Il ne contient aucune nouvelle information, mais il contient des données qui peuvent prendre du temps à calculer. Cela le rend pratique à avoir, car télécharger ce torrent est souvent plus rapide que de le calculer à partir de zéro. En particulier, il contient des index SQLite pour les fichiers tar, à utiliser avec <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Foire aux questions (FAQ)"

msgid "page.faq.what_is.title"
msgstr "C'est quoi les Archives d'Anna ?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Les Archives d'Anna</span> est un projet à but non lucratif ayant deux objectifs :"

msgid "page.home.intro.text2"
msgstr "<li><strong>Préservation :</strong> Sauvegarder toutes les connaissances et la culture de l'humanité.</li><li><strong>Accès :</strong> Rendre ces connaissances et cette culture accessibles à n'importe qui dans le monde.</li>"

msgid "page.home.intro.open_source"
msgstr "Tout notre <a %(a_code)s>code</a> et nos <a %(a_datasets)s>données</a> sont complètement open-source."

msgid "page.home.preservation.header"
msgstr "Préservation"

msgid "page.home.preservation.text1"
msgstr "Nous préservons les livres, bandes dessinées, magazines et autres, en rassemblant ces documents depuis une variété de <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothèques fantôme</a>, bibliothèques officielles et autres collections, tout cela au même endroit. Toutes ces données sont pour toujours préservées, car nous facilitons leur duplication massive — à l'aide de torrents — créant ainsi une multitude de copies à travers le monde. Certaines bibliothèques fantôme font déjà ce travail (Sci-Hub, Library Genesis par exemple), tandis que les Archives d'Anna s'occupent de \"libérer\" celles qui ne proposent pas de redistribution massive (Z-Library par exemple) ou qui ne sont tout simplement pas des bibliothèques fantômes (Internet Achive, DuXiu par exemple)."

msgid "page.home.preservation.text2"
msgstr "Cette large redistribution, combinée à un code en open-source, rend notre site internet résilient aux tentatives d'extinction, et assure une préservation à long terme des connaissances et de la culture humaine. Apprenez-en plus sur <a href=\"/datasets\">nos jeux de données</a>."

msgid "page.home.preservation.label"
msgstr "Nous estimons avoir préservé environ <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% des livres dans le monde</a>."

msgid "page.home.access.header"
msgstr "Accès"

msgid "page.home.access.text"
msgstr "Nous travaillons avec des partenaires afin de rendre nos collections accessibles de manière facile et libre à n'importe qui. Nous pensons que tout le monde a le droit à la sagesse collective de l'humanité. Et <a %(a_search)s>pas aux dépens des auteurs</a>."

msgid "page.home.access.label"
msgstr "Téléchargements par heure ces 30 derniers jours. Moyenne par heure : %(hourly)s. Moyenne par jour : %(daily)s."

msgid "page.about.text2"
msgstr "Nous croyons fermement à la libre circulation de l'information et à la préservation des connaissances et de la culture. Avec ce moteur de recherche, nous nous appuyons sur les épaules des géants. Nous respectons profondément le travail acharné des personnes qui ont créé les différentes bibliothèques clandestines, et nous espérons que ce moteur de recherche élargira leur portée."

msgid "page.about.text3"
msgstr "Pour rester informé de nos progrès, suivez Anna sur <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pour toutes questions ou suggestions, contactez Anna à l'adresse %(email)s."

msgid "page.faq.help.title"
msgstr "Comment je peux vous aider ?"

msgid "page.about.help.text"
msgstr "<li>1. Suivez-nous sur <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Parlez des Archives d'Anna sur Twitter, Reddit, Tiktok, Instagram, au café du coin, à la bibliothèque, partout où vous allez ! Nous ne croyons pas en la rétention de l'information — Si notre site venait à être fermé, nous réapparaîtrions ailleurs, puisque notre code et nos données sont \"open source\".</li><li>3. Si vous le pouvez, pensez à <a href=\"/donate\">faire un don</a>.</li><li>4. Aidez-nous <a href=\"https://translate.annas-software.org/\">à traduire</a> notre site dans d'autres langues.</li><li>5. Si vous faites de la programmation, envisagez de contribuer à notre <a href=\"https://annas-software.org/\">projet \"open source\"</a>, ou à seeder nos <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Nous avons maintenant également un canal Matrix synchronisé à %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Si vous êtes un chercheur en sécurité, nous pouvons utiliser vos compétences tant en attaque qu'en défense. Consultez notre page <a %(a_security)s>Sécurité</a>."

msgid "page.about.help.text7"
msgstr "7. Nous recherchons des experts en paiements pour commerçants anonymes. Pouvez-vous nous aider à ajouter des moyens plus pratiques de faire un don ? PayPal, WeChat, cartes-cadeaux. Si vous connaissez quelqu'un, veuillez nous contacter."

msgid "page.about.help.text8"
msgstr "8. Nous recherchons toujours plus de capacité de serveur."

msgid "page.about.help.text9"
msgstr "9. Vous pouvez nous aider en signalant des problèmes sur les fichiers, en laissant des commentaires et en créant des listes directement sur ce site Web. Vous pouvez également aider en <a %(a_upload)s>téléversant davantage de livres</a>, en résolvant des problèmes de fichiers ou en formatant des livres existants."

msgid "page.about.help.text10"
msgstr "10. Créez ou aidez à maintenir la page Wikipédia des Archives d'Anna dans votre langue."

msgid "page.about.help.text11"
msgstr "11. Nous cherchons à placer de petites publicités de bon goût. Si vous souhaitez faire de la publicité sur les Archives d'Anna, veuillez nous le faire savoir."

msgid "page.faq.help.mirrors"
msgstr "On aimerait beaucoup voir des <a %(a_mirrors)s>des bibliothèques-miroir</a> se mettre en place, action pour laquelle nous pourrions apporter notre soutien financier."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pour plus d'informations sur la façon de devenir bénévole, consultez notre page <a %(a_volunteering)s>Bénévolat & Récompenses</a>."

msgid "page.faq.slow.title"
msgstr "Pourquoi les téléchargements lents sont-ils si lents ?"

msgid "page.faq.slow.text1"
msgstr "Nous n'avons malheureusement pas assez de ressources pour offrir à tout le monde des téléchargements à haute vitesse, bien que nous aimerions le faire. Si un riche bienfaiteur souhaite se manifester et nous fournir cela, ce serait incroyable, mais d'ici-là, nous faisons de notre mieux. Nous sommes un projet à but non lucratif qui peut à peine se maintenir grâce aux dons."

msgid "page.faq.slow.text2"
msgstr "C'est pourquoi nous avons mis en place deux systèmes pour les téléchargements gratuits avec nos partenaires : des serveurs partagés avec des téléchargements lents et des serveurs légèrement plus rapides avec une liste d'attente (pour réduire le nombre de personnes téléchargeant en même temps)."

msgid "page.faq.slow.text3"
msgstr "Nous effectuons également une <a %(a_verification)s>vérification du navigateur</a> pour nos téléchargements lents, car les bots et scrapers en abuseraient, rendant les choses encore plus lentes pour les utilisateurs légitimes."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Notez que, lorsque vous utilisez le navigateur Tor, vous devrez peut-être ajuster vos paramètres de sécurité. Avec l'option la plus basse, appelée « Standard », le défi Cloudflare turnstile réussit. Avec les options plus élevées, appelées « Plus sûr » et « Le plus sûr », le défi échoue."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Pour les fichiers volumineux, les téléchargements lents peuvent parfois se couper en cours de route. Nous vous recommandons d'utiliser un gestionnaire de téléchargement (tel que JDownloader) pour reprendre automatiquement les téléchargements volumineux."

msgid "page.donate.faq.title"
msgstr "FAQ Dons"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Les adhésions sont-elles renouvelées automatiquement ?</div> Les adhésions<strong>ne sont pas</strong> renouvelées automatiquement. Vous pouvez choisir librement la durée de votre adhésion."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Puis-je améliorer mon adhésion ou obtenir plusieurs adhésions ?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Avez-vous d'autres moyens de paiement ?</div> Actuellement non. Beaucoup de gens ne veulent pas que des archives comme celle-ci existent, nous devons donc être prudents. Si vous pouvez nous aider à mettre en place d'autres méthodes de paiement (plus pratiques) en toute sécurité, contactez nous à l'adresse suivante %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Que signifient les plages par mois ?</div> Vous pouvez atteindre le bas d'une plage en appliquant toutes les réductions, comme choisir une période plus longue qu'un mois."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Comment utilisez-vous les dons ?</div> 100%% sont utilisés pour préserver et rendre accessible les connaissances et la culture du monde. Nos dépenses sont orientées actuellement principalement vers les serveurs, le stockage et la bande passante. Aucune somme n'est reversée personnellement à un membre de l'équipe."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Puis-je faire un don important ?</div> Ce serait formidable ! Pour les dons supérieurs à quelques milliers de dollars, contactez-nous directement à l'adresse suivante %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Puis-je faire un don sans devenir membre ?</div> Bien sûr. Nous acceptons les dons de tout montant à cette adresse Monero (XMR) : %(address)s."

msgid "page.faq.upload.title"
msgstr "Comment puis-je téléverser de nouveaux livres ?"

msgid "page.upload.zlib.text1"
msgstr "Alternativement, vous pouvez les téléverser sur Z-Library <a %(a_upload)s>ici</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pour les petits téléversements (jusqu'à 10 000 fichiers), veuillez les téléverser à la fois sur %(first)s et %(second)s."

msgid "page.upload.text1"
msgstr "Pour le moment, nous vous suggérons de téléverser de nouveaux livres sur les forks de Library Genesis. Voici un <a %(a_guide)s>guide pratique</a> à ce sujet. Remarquez que les deux forks auxquels nous faisons référence sur ce site se basent sur le même système de téléversement."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pour Libgen.li, assurez-vous de d'abord vous connecter sur <a %(a_forum)s >leur forum</a> avec le nom d'utilisateur %(username)s et le mot de passe %(password)s, puis retournez sur leur <a %(a_upload_page)s >page de téléversement</a>."

msgid "common.libgen.email"
msgstr "Si votre adresse email ne fonctionne pas sur les forums de Libgen, nous vous conseillons d'utiliser <a %(a_mail)s>Proton Mail</a> (gratuit). Vous pouvez aussi <a %(a_manual)s>demander manuellement</a> que votre compte soit activé."

msgid "page.faq.mhut_upload"
msgstr "Notez que mhut.org bloque certaines plages d'IP, donc un VPN pourrait être nécessaire."

msgid "page.upload.large.text"
msgstr "Pour les gros téléversements (plus de 10000 fichiers) qui ne sont pas acceptés par Libgen ou Z-Library, merci de nous contacter à %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Pour téléverser des écrits académiques, veuillez également (en plus de Library Genesis) les téléverser sur <a %(a_stc_nexus)s>STC Nexus</a>. Ils sont la meilleure bibliothèque fantôme pour les nouveaux articles. Nous ne les avons pas encore intégrés, mais nous le ferons à un moment donné. Vous pouvez utiliser leur <a %(a_telegram)s>bot de téléversement sur Telegram</a>, ou contacter l'adresse indiquée dans leur message épinglé si vous avez beaucoup de fichiers à téléverser."

msgid "page.faq.request.title"
msgstr "Comment faire une demande pour un livre manquant ?"

msgid "page.request.cannot_accomodate"
msgstr "Pour le moment, nous ne pouvons pas nous occuper des demandes de livres."

msgid "page.request.forums"
msgstr "Merci de faire vos demandes sur les forums de Z-Library ou Libgen."

msgid "page.request.dont_email"
msgstr "Ne nous envoyez pas vos demandes de livres."

msgid "page.faq.metadata.title"
msgstr "Collectez-vous des métadonnées ?"

msgid "page.faq.metadata.indeed"
msgstr "Effectivement, oui."

msgid "page.faq.1984.title"
msgstr "J'ai téléchargé 1984 de George Orwell, la police va-t-elle venir chez moi ?"

msgid "page.faq.1984.text"
msgstr "Ne vous inquiétez pas trop, beaucoup de gens téléchargent depuis les sites web auxquels nous renvoyons, et il est extrêmement rare d'avoir des problèmes. Cependant, pour rester en sécurité, nous recommandons d'utiliser un VPN (payant), ou <a %(a_tor)s>Tor</a> (gratuit)."

msgid "page.faq.save_search.title"
msgstr "Comment puis-je sauvegarder mes paramètres de recherche ?"

msgid "page.faq.save_search.text1"
msgstr "Sélectionnez les paramètres souhaités, laissez la barre de recherche vide, cliquez sur « Rechercher », puis ajoutez la page aux favoris en utilisant la fonction de favoris de votre navigateur."

msgid "page.faq.mobile.title"
msgstr "Avez-vous une application mobile ?"

msgid "page.faq.mobile.text1"
msgstr "Nous n'avons pas d'application mobile officielle, mais vous pouvez installer ce site web comme une application."

msgid "page.faq.mobile.android"
msgstr "<strong>Android :</strong> Cliquez sur le menu à trois points en haut à droite, et sélectionnez « Ajouter à l'écran d'accueil »."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS :</strong> Cliquez sur le bouton « Partager » en bas et sélectionnez « Ajouter à l'écran d'accueil »."

msgid "page.faq.api.title"
msgstr "Avez-vous une API ?"

msgid "page.faq.api.text1"
msgstr "Nous avons une API JSON pour les membres afin d'obtenir un lien de téléchargement rapide : <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation dans le JSON lui-même)."

msgid "page.faq.api.text2"
msgstr "Pour d'autres cas d'utilisation, comme parcourir tous les fichiers, créer des recherches personnalisées, etc., nous recommandons de <a %(a_generate)s>générer</a> ou de<a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement <a %(a_explore)s>via des fichiers JSON</a>."

msgid "page.faq.api.text3"
msgstr "Notre liste de torrents peut également être téléchargée en tant que <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "FAQ sur les torrents"

msgid "page.faq.torrents.q1"
msgstr "Je voudrais aider à partager, mais je n'ai pas beaucoup d'espace disque."

msgid "page.faq.torrents.a1"
msgstr "Utilisez le <a %(a_list)s>générateur de liste de torrents</a> pour générer une liste de torrents qui ont le plus besoin d'être partagés, dans les limites de votre espace de stockage."

msgid "page.faq.torrents.q2"
msgstr "Les torrents sont trop lents ; puis-je télécharger les données directement depuis le site ?"

msgid "page.faq.torrents.a2"
msgstr "Oui, consultez la page <a %(a_llm)s>données LLM</a>."

msgid "page.faq.torrents.q3"
msgstr "Puis-je télécharger seulement un sous-ensemble des fichiers, comme ceux écrits dans une certaine langue ou concernant un sujet particulier ?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Réponse courte : pas facilement."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Réponse longue :"

msgid "page.faq.torrents.a3"
msgstr "La plupart des torrents contiennent les fichiers directement, ce qui signifie que vous pouvez demander à votre client torrent de ne télécharger que les fichiers souhaités. Pour déterminer quels fichiers télécharger, vous pouvez <a %(a_generate)s>générer</a> nos métadonnées, ou <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Malheureusement, un certain nombre de collections de torrents contiennent des fichiers .zip ou .tar à la racine, auquel cas, vous devez télécharger l'intégralité du torrent avant de pouvoir sélectionner des fichiers individuels."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Nous avons <a %(a_ideas)s>quelques idées</a> pour ce dernier cas cependant.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Aucun outil facile à utiliser pour filtrer les torrents n'est encore disponible, mais nous accueillons les contributions."

msgid "page.faq.torrents.q4"
msgstr "Comment gérez-vous les doublons dans les torrents ?"

msgid "page.faq.torrents.a4"
msgstr "Nous essayons de minimiser les doublons et chevauchements entre les torrents de cette liste, mais cela ne peut pas toujours être réalisé et dépend fortement des bibliothèques sources. Pour celles qui publient leurs propres torrents, cela échappe à notre contrôle. Pour les torrents publiés par Anna’s Archive, nous dédupliquons uniquement en fonction du hachage MD5, ce qui signifie que différentes versions du même livre peuvent être dupliquées."

msgid "page.faq.torrents.q5"
msgstr "Puis-je obtenir la liste des torrents en JSON ?"

msgid "page.faq.torrents.a5"
msgstr "Oui."

msgid "page.faq.torrents.q6"
msgstr "Je ne vois pas de fichiers PDF ou EPUB dans les torrents, seulement des fichiers binaires ? Que dois-je faire ?"

msgid "page.faq.torrents.a6"
msgstr "Ce sont en fait des PDF et des EPUB, ils n'ont tout simplement pas d'extension dans beaucoup de nos torrents. Il y a deux endroits où vous pouvez trouver les métadonnées des fichiers torrents, y compris leurs types/extensions :"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Chaque collection a ses propres métadonnées. Par exemple, les <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> ont une base de métadonnées hébergée sur le site de Libgen.rs. Nous lions généralement les métadonnées pertinentes à partir de la <a %(a_datasets)s>page du jeu de données</a> de chaque collection."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Nous recommandons de <a %(a_generate)s>générer</a> ou de <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Celles-ci contiennent une entrée pour chaque archive des Archives d'Anna avec les fichiers torrent correspondants (si disponibles), sous \"torrent_paths\" dans le JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Pourquoi mon client torrent ne peut-il pas ouvrir certains de vos fichiers torrent / liens magnet ?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Certains clients torrent ne prennent pas en charge les grandes tailles de pièces, ce que beaucoup de nos torrents ont (pour les plus récents, nous ne le faisons plus — même si c'est valide selon les spécifications !). Essayez donc un autre client si vous rencontrez ce problème, ou adressez-vous aux créateurs de votre client torrent."

msgid "page.faq.security.title"
msgstr "Avez-vous un programme de divulgation responsable des failles de sécurité ?"

msgid "page.faq.security.text1"
msgstr "Nous invitons les chercheurs en cybersécurité à rechercher des vulnérabilités dans nos systèmes. Nous sommes de grands partisans de la divulgation responsable. Contactez-nous <a %(a_contact)s>ici</a>."

msgid "page.faq.security.text2"
msgstr "Nous ne sommes actuellement pas en mesure d'offrir des primes de bogues, sauf pour les vulnérabilités qui peuvent potentiellement <a %(a_link)s>compromettre notre anonymat</a>. Pour ces dernières, nous offrons des primes allant de 10 000 à 50 000 $. À l'avenir, nous aimerions offrir ces primes pour une sélection de vulnérabilités plus large ! Veuillez noter que les attaques d'ingénierie sociale ne sont pas comprises."

msgid "page.faq.security.text3"
msgstr "Si vous êtes intéressé par la cybersécurité offensive et souhaitez aider à archiver les connaissances et la culture du monde, contactez-nous. Il y a de nombreuses façons de nous aider."

msgid "page.faq.resources.title"
msgstr "Y a-t-il plus de ressources concernant les Archives d'Anna ?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog d'Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — mises à jour régulières"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Logiciel d'Anna</a> — notre code open source"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduire les logiciels d'Anna</a> — notre système de traduction"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — à propos des données"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domaines alternatifs"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipédia</a> — plus d'informations nous concernant (veuillez aider à maintenir cette page à jour, ou à en créer une pour votre propre langue !)"

msgid "page.faq.copyright.title"
msgstr "Comment signaler une violation de droits d'auteur ?"

msgid "page.faq.copyright.text1"
msgstr "Nous n'hébergeons aucun fichier protégé par des droits d'auteur ici. Nous sommes un moteur de recherche et, en tant que tel, nous indexons uniquement les métadonnées déjà disponibles publiquement. Lors du téléchargement à partir des sources externes, nous vous suggérons de vérifier les lois applicables dans votre pays pour savoir ce qui est autorisé. Nous ne sommes pas responsables du contenu hébergé par d'autres."

msgid "page.faq.copyright.text2"
msgstr "Si vous avez des plaintes concernant ce que vous voyez ici, le mieux est de contacter le site web d'origine. Nous intégrons leurs modifications dans notre base de données. Si vous pensez vraiment avoir une plainte DMCA valide à laquelle nous devrions répondre, veuillez remplir le <a %(a_copyright)s>formulaire de réclamation DMCA / Droits d'auteur</a>. Nous prenons vos plaintes au sérieux et nous vous répondrons dès que possible."

msgid "page.faq.hate.title"
msgstr "Je déteste la façon dont vous gérez ce projet !"

msgid "page.faq.hate.text1"
msgstr "Nous tenons également à rappeler à tout le monde que tout notre code et nos données sont entièrement open source. C'est très rare pour des projets comme le nôtre — nous ne connaissons aucun autre projet avec un catalogue aussi vaste qui soit aussi complètement open source. Nous encourageons toute personne qui pense que nous gérons mal notre projet à prendre notre code et nos données et à créer sa propre bibliothèque fantôme ! Nous ne disons pas cela par dépit ou autre — nous pensons sincèrement que ce serait génial, car cela élèverait le niveau pour tout le monde et préserverait mieux l'héritage de l'humanité."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Avez-vous un moniteur de disponibilité ?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Veuillez consulter <a %(a_href)s>cet excellent projet</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Comment puis-je faire un don de livres ou d'autres matériaux physiques ?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Veuillez les envoyer à l'<a %(a_archive)s>Internet Archive</a>. Ils les préserveront correctement."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Qui est Anna ?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Vous êtes Anna !"

msgid "page.faq.favorite.title"
msgstr "Quels sont vos livres préférés ?"

msgid "page.faq.favorite.text1"
msgstr "Voici quelques livres qui revêtent une importance singulière pour le monde des bibliothèques fantômes et de la préservation numérique :"

msgid "page.fast_downloads.no_more_new"
msgstr "Vous êtes arrivé à court de téléchargements rapides pour aujourd'hui."

msgid "page.fast_downloads.no_member"
msgstr "Devenez membre pour utiliser les téléchargements rapides."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Nous acceptons désormais les cartes cadeaux Amazon, les cartes de crédit et de débit, les cryptomonnaies, Alipay et WeChat."

msgid "page.home.full_database.header"
msgstr "Base de données complète"

msgid "page.home.full_database.subtitle"
msgstr "Livres, articles, magazines, bande dessinés, archives de bibliothèques, métadonnées…"

msgid "page.home.full_database.search"
msgstr "Rechercher"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub a <a %(a_paused)s>mis en pause</a> le téléversement de nouveaux articles."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB est une continuation de Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Accès direct à %(count)s articles académiques"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Ouvrir"

msgid "page.home.scidb.browser_verification"
msgstr "Si vous êtes <a %(a_member)s>membre</a>, la vérification navigateur n'est pas nécessaire."

msgid "page.home.archive.header"
msgstr "Archive à long-terme"

msgid "page.home.archive.body"
msgstr "Les jeux de données utilisés par les Archives d'Anna sont totalement ouverts, et peuvent être massivement reproduits à l'aide de torrents. <a %(a_datasets)s>En savoir plus… ⁣</a>"

msgid "page.home.torrents.body"
msgstr "Vous pouvez apporter une aide énorme en diffusant nos torrents. <a %(a_torrents)s>En savoir plus…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "Données d'entraînement pour LLM"

msgid "page.home.llm.body"
msgstr "Nous fournissons la plus grande collection au monde de données textuelles de haute qualité. <a %(a_llm)s>Apprenez-en plus…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Archives-Miroir : appel à bénévoles"

msgid "page.home.volunteering.header"
msgstr "🤝 Nous recherchons des bénévoles"

msgid "page.home.volunteering.help_out"
msgstr "En tant que projet à but non lucratif et open source, nous recherchons toujours des personnes pour nous aider."

msgid "page.home.payment_processor.body"
msgstr "Si vous êtes responsable d'un organisme de paiement anonyme et à haut risque, contactez-nous. Nous cherchons également des annonceurs à la recherche d'emplacements pour de petites publicités de bon goût. Toutes les recettes servent directement à nos efforts de préservation."

msgid "layout.index.header.nav.annasblog"
msgstr "Le Blog d'Anna ↗"

msgid "page.ipfs_downloads.title"
msgstr "Téléchargements IPFS"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Tous les liens de téléchargement pour ce fichier</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Portail IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(vous devrez peut-être essayer plusieurs fois avec IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Pour obtenir des téléchargements plus rapides et passer la vérification navigateur, <a %(a_membership)s>devenez membre</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Pour la reproduction massive de notre collection en archives-miroir, consultez les pages <a %(a_datasets)s>Jeux de données</a> et <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Données LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Il est bien compris que les LLM prospèrent grâce à des données de haute qualité. Nous avons la plus grande collection de livres, articles, magazines, etc. au monde, qui sont parmi les sources de texte de la plus haute qualité."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Échelle et portée uniques"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Notre collection contient plus de cent millions de fichiers, y compris des revues académiques, des manuels scolaires et des magazines. Nous atteignons cette échelle en combinant de grands dépôts existants."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Certaines de nos collections sources sont déjà disponibles en vrac (Sci-Hub, et certaines parties de Libgen). D'autres sources, nous les avons libérées nous-mêmes. <a %(a_datasets)s>Datasets</a> montre un aperçu complet."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Notre collection comprend des millions de livres, articles et magazines d'avant l'ère des e-books. De grandes parties de cette collection ont déjà été OCRisées, et ont déjà peu de chevauchement interne."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Comment nous pouvons aider"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Nous sommes en mesure de fournir un accès à haute vitesse à nos collections complètes, ainsi qu'à des collections non publiées."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Il s'agit d'un accès de niveau entreprise que nous pouvons fournir contre des dons de l'ordre de dizaines de milliers de dollars USD. Nous sommes également prêts à échanger cela contre des collections de haute qualité que nous n'avons pas encore."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Nous pouvons vous rembourser si vous êtes en mesure de nous fournir un enrichissement de nos données, tel que :"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Suppression des doublons (déduplication)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extraction de texte et de métadonnées"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Soutenez l'archivage à long terme des connaissances humaines, tout en obtenant de meilleures données pour votre modèle !"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contactez-nous</a> pour discuter de la manière dont nous pouvons collaborer."

msgid "page.login.continue"
msgstr "Continuer"

msgid "page.login.please"
msgstr "Merci de vous <a %(a_account)s>connecter</a> pour consulter cette page.</a>"

msgid "page.maintenance.header"
msgstr "Les Archives d'Anna sont temporairement en maintenance. Veuillez revenir dans une heure."

msgid "page.metadata.header"
msgstr "Améliorer les métadonnées"

msgid "page.metadata.body1"
msgstr "Vous pouvez aider à la préservation des livres en améliorant les métadonnées ! Tout d'abord, lisez les informations de base concernant les métadonnées sur les Archives d’Anna, puis apprenez comment améliorer les métadonnées en les liant avec Open Library, et gagnez une adhésion gratuite sur les Archives d’Anna."

msgid "page.metadata.background.title"
msgstr "Contexte"

msgid "page.metadata.background.body1"
msgstr "Lorsque vous regardez un livre sur les Archives d’Anna, vous pouvez voir divers champs : titre, auteur, éditeur, édition, année, description, nom de fichier, et plus encore. Toutes ces informations sont appelées <em>métadonnées</em>."

msgid "page.metadata.background.body2"
msgstr "Comme nous combinons des livres de diverses <em>bibliothèques sources</em>, nous affichons les métadonnées disponibles dans ces bibliothèques source. Par exemple, pour un livre que nous avons obtenu de Library Genesis, nous afficherons le titre de la base de données de Library Genesis."

msgid "page.metadata.background.body3"
msgstr "Parfois, un livre est présent dans <em>plusieurs</em> bibliothèques sources, qui peuvent avoir des métadonnées différentes. Dans ce cas, nous affichons simplement la version la plus longue de chaque champ, car celle-ci contient probablement les informations les plus utiles ! Nous afficherons toujours les autres champs sous la description, par exemple en tant que « titre alternatif » (mais seulement s'ils sont différents)."

msgid "page.metadata.background.body4"
msgstr "Nous extrayons également des <em>codes</em> tels que des identifiants et des classificateurs de la bibliothèque source. Les <em>identifiants</em> (ISBN, DOI, Open Library ID, Google Books ID, Amazon ID) représentent de manière unique une édition particulière d'un livre. Les <em>classificateurs</em> (Dewey Decimal (DCC), UDC, LCC, RVK, GOST) regroupent plusieurs livres similaires. Parfois, ces codes sont explicitement liés dans les bibliothèques sources. Par ailleurs, il est possible de les extraire du nom de fichier ou de la description (principalement ISBN et DOI)."

msgid "page.metadata.background.body5"
msgstr "Nous pouvons utiliser les identifiants pour trouver des entrées dans des <em>collections de métadonnées uniquement</em>, telles que OpenLibrary, ISBNdb, ou WorldCat/OCLC. Il y a un <em>onglet de métadonnées</em> spécifique dans notre moteur de recherche si vous souhaitez parcourir ces collections. Nous utilisons les entrées correspondantes pour remplir les champs de métadonnées manquants (par exemple, si un titre manque), ou par exemple comme « titre alternatif » (s'il existe un titre existant)."

msgid "page.metadata.background.body6"
msgstr "Pour voir exactement d'où proviennent les métadonnées d'un livre, consultez l'<em>onglet « Détails techniques »</em> sur la page d'un livre. Il contient un lien vers le JSON brut de ce livre, avec des pointeurs vers le JSON brut des entrées originales."

msgid "page.metadata.background.body7"
msgstr "Pour plus d'informations, consultez les pages suivantes : <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Recherche (onglet métadonnées)</a>, <a %(a_codes)s>Explorateur de codes</a>, et <a %(a_example)s>Exemple de fichier JSON de métadonnées</a>. Enfin, toutes nos métadonnées peuvent être <a %(a_generated)s>générées</a> ou <a %(a_downloaded)s>téléchargées</a> sous forme de bases de données ElasticSearch et MariaDB."

msgid "page.metadata.openlib.title"
msgstr "Correspondance avec Open Library"

msgid "page.metadata.openlib.body1"
msgstr "Donc, si vous trouver un fichier avec de mauvaises métadonnées, comment devriez-vous le corriger ? Vous pouvez aller à la bibliothèque source et suivre ses procédures pour corriger les métadonnées, mais que faire si un fichier est présent dans plusieurs bibliothèques sources ?"

msgid "page.metadata.openlib.body2"
msgstr "Il y a un identifiant qui est traité de manière spéciale sur les Archives d’Anna. <strong>Le champ md5 \"annas_archive\" d'Open Library remplace toujours toutes les autres métadonnées !</strong> Revenons un peu en arrière et apprenons en un peu plus sur Open Library."

msgid "page.metadata.openlib.body3"
msgstr "Open Library a été fondée en 2006 par Aaron Swartz avec pour objectif « une page web pour chaque livre publié ». C'est une sorte de Wikipédia pour les métadonnées de livres : tout le monde peut l'éditer, elle est sous licence libre, et peut être entièrement téléchargée. C'est une base de données de livres qui correspond le plus avec notre mission — en fait, les Archives d’Anna a été inspirée par la vision et la vie d'Aaron Swartz."

msgid "page.metadata.openlib.body4"
msgstr "Au lieu de réinventer la roue, nous avons décidé de rediriger nos bénévoles vers Open Library. Si vous voyez un livre avec des métadonnées incorrectes, vous pouvez aider de la manière suivante :"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Allez sur le <a %(a_openlib)s>site web d'Open Library</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Trouvez l'archive correcte du livre. <strong>ATTENTION :</strong> assurez-vous de sélectionner la <strong>bonne édition</strong>. Dans Open Library, vous avez des « œuvres » et des « éditions »."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Une « œuvre » pourrait être « Harry Potter à l'école des sorciers »."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Une « édition » pourrait être :"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La première édition de 1997 publiée par Bloomsbery avec 256 pages."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "L'édition de poche de 2003 publiée par Raincoast Books avec 223 pages."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La traduction polonaise de 2000 « Harry Potter I Kamie Filozoficzn » par Media Rodzina avec 328 pages."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Toutes ces éditions ont des ISBN et des contenus différents, alors assurez-vous de sélectionner la bonne !"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Modifiez l'entrée (ou créez-la si elle n'existe pas), et ajoutez autant d'informations utiles que possible ! Vous êtes ici de toute façon, alors autant rendre l'archive vraiment incroyable."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Sous « ID Numbers », sélectionnez « Anna’s Archive » et ajoutez le MD5 du livre provenant d'Anna’s Archive. C'est la longue chaîne de lettres et de chiffres après « /md5/ » dans l'URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Essayez de trouver d'autres fichiers dans les Archives d'Anna qui correspondent également à cette archive, et ajoutez-les aussi. À l'avenir, nous pourrons regrouper ceux-ci comme des doublons sur notre page de recherche."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Lorsque vous avez terminé, notez l'URL que vous venez de mettre à jour. Une fois que vous avez mis à jour au moins 30 archives avec les MD5 des Archives d'Anna, envoyez-nous un <a %(a_contact)s>email</a> avec la liste. Nous vous offrirons une adhésion gratuite aux Archives d'Anna, afin que vous puissiez plus facilement faire ce travail (et en guise de remerciement pour votre aide). Ces modifications doivent être de bonne qualité et ajouter des informations substantielles, sinon votre demande sera rejetée. Elle le sera également si l'une des modifications est annulée ou corrigée par les modérateurs d'Open Library."

msgid "page.metadata.openlib.body5"
msgstr "Notez que cela ne fonctionne que pour les livres, pas pour les articles académiques ou d'autres types de fichiers. Pour d'autres types de fichiers, nous recommandons toujours de trouver la bibliothèque source. Il peut falloir quelques semaines pour que les modifications soient incluses dans les Archives d'Anna, car nous devons télécharger la dernière version de la base de données d'Open Library et régénérer notre index de recherche."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Miroirs : appel aux volontaires"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Pour augmenter la résilience de l’Archive d’Anna, nous recherchons des volontaires pour gérer des miroirs."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Nous recherchons ceci :"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Vous exécutez la base de code open source d’Anna’s Archive, et vous mettez régulièrement à jour à la fois le code et les données."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Votre version est clairement distinguée comme un site miroir, par exemple « L’Archive de Bob, un site miroir d’Anna’s Archive »."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Vous êtes prêt à prendre les risques associés à ce travail, qui sont significatifs. Vous avez une compréhension approfondie de la sécurité opérationnelle requise. Le contenu de <a %(a_shadow)s>ces</a> <a %(a_pirate)s>articles</a> vous est évident."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Vous êtes prêt à contribuer à notre <a %(a_codebase)s>code source</a> — en collaboration avec notre équipe — pour que cela se réalise."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Initialement, nous ne vous donnerons pas accès aux téléchargements de notre serveur partenaire, mais si tout se passe bien, nous pourrons les partager avec vous."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Frais d'hébergement"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Nous sommes prêts à couvrir les frais d'hébergement et de VPN, initialement jusqu'à 200 $ par mois. Cela suffit pour un serveur de recherche de base et un proxy protégé par le DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Nous ne paierons l'hébergement que lorsque vous aurez tout mis en place et démontré que vous êtes capable de maintenir l'archive à jour avec les mises à jour. Cela signifie que vous devrez payer les 1-2 premiers mois de votre poche."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Votre temps ne sera pas compensé (et le nôtre non plus), car il s'agit d'un travail purement bénévole."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si vous vous impliquez de manière significative dans le développement et les opérations de notre travail, nous pouvons discuter du partage d'une plus grande partie des revenus des dons avec vous, pour que vous les déployiez selon les besoins."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Commencer"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Veuillez <strong>ne pas nous contacter</strong> pour demander la permission ou pour des questions de base. Les actions parlent plus fort que les mots ! Toutes les informations sont disponibles, alors allez-y et configurez votre site miroir."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "N'hésitez pas à poster des tickets ou des demandes de fusion sur notre Gitlab lorsque vous rencontrez des problèmes. Nous pourrions avoir besoin de développer certaines fonctionnalités spécifiques au site miroir avec vous, telles que le rebranding de « l'Archive d'Anna » au nom de votre site, la désactivation (initiale) des comptes utilisateurs, ou le lien vers notre site principal depuis les pages de livres."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Une fois que vous avez votre site miroir en fonctionnement, veuillez nous contacter. Nous aimerions examiner votre OpSec, et une fois que ce sera solide, nous créerons un lien vers votre site miroir et commencerons à travailler plus étroitement avec vous."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Merci d’avance à tous ceux qui sont prêts à contribuer de cette manière ! Ce n’est pas pour les âmes sensibles, mais cela renforcerait la longévité de la plus grande bibliothèque véritablement ouverte de l’histoire humaine."

msgid "page.partner_download.header"
msgstr "Télécharger depuis le site partenaire"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Les téléchargements lents ne sont disponibles que via le site officiel. Visitez %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Les téléchargements lents ne sont pas disponibles via les VPN de Cloudflare ou par les adresses IP qu'ils utilisent."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Veuillez patienter <span %(span_countdown)s>%(wait_seconds)s</span> secondes pour télécharger ce fichier."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Télécharger maintenant</a>"

msgid "page.partner_download.li4"
msgstr "Merci d'avoir attendu, cela permet de garder le site accessible gratuitement pour tout le monde ! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Attention : de nombreux téléchargements ont été effectués depuis votre adresse IP au cours des dernières 24 heures. Les téléchargements peuvent être plus lents que d'habitude."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Téléchargements depuis votre adresse IP depuis les dernières 24 heures :%(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Si vous utilisez un VPN, une connexion internet partagée ou si votre FAI partage des IP, cet avertissement peut en être la cause."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Afin de donner à chacun l'opportunité de télécharger des fichiers gratuitement, vous devez attendre avant de pouvoir télécharger ce fichier."

msgid "page.partner_download.li1"
msgstr "N'hésitez pas à continuer de naviguer sur les Archives d'Anna dans un autre onglet en attendant (si votre navigateur prend en charge l'actualisation des onglets en arrière-plan)."

msgid "page.partner_download.li2"
msgstr "N'hésitez pas à laisser plusieurs pages de téléchargement se charger en simultané (mais veuillez ne télécharger qu'un seul fichier à la fois par serveur)."

msgid "page.partner_download.li3"
msgstr "Une fois que vous obtenez un lien de téléchargement, ce dernier est valable pendant plusieurs heures."

msgid "layout.index.header.title"
msgstr "Archives d'Anna"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI : %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Archive sur les Archives d'Anna"

msgid "page.scidb.download"
msgstr "Télécharger"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Pour soutenir l'accessibilité et la préservation à long terme des connaissances humaines, devenez <a %(a_donate)s>membre</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "En bonus, 🧬&nbsp;SciDB se charge plus rapidement pour les membres, sans aucune limite."

msgid "page.scidb.refresh"
msgstr "Ça ne marche pas ? Essayez de <a %(a_refresh)s>rafraîchir</a> la page."

msgid "page.scidb.no_preview_new"
msgstr "Aucun aperçu disponible pour le moment. Téléchargez le fichier depuis <a %(a_path)s>les Archives d'Anna</a>."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB est une continuation de Sci-Hub, avec son interface familière et la visualisation directe des PDF. Entrez votre DOI pour commencer."

msgid "page.home.scidb.text3"
msgstr "Nous avons l'intégralité de la collection Sci-Hub, ainsi que de nouveaux articles. La plupart peuvent être consultées directement avec une interface familière similaire à Sci-Hub. Certains peuvent être téléchargés via des sources externes dont nous affichons des liens."

msgid "page.search.title.results"
msgstr "%(search_input)s - Rechercher"

msgid "page.search.title.new"
msgstr "Nouvelle recherche"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Inclure uniquement"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Exclure"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Non vérifié"

msgid "page.search.tabs.download"
msgstr "Télécharger"

msgid "page.search.tabs.journals"
msgstr "Articles de journaux"

msgid "page.search.tabs.digital_lending"
msgstr "Prêt numérique"

msgid "page.search.tabs.metadata"
msgstr "Métadonnées"

msgid "common.search.placeholder"
msgstr "Recherche par titre, auteur, langue, type de fichier, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Rechercher"

msgid "page.search.search_settings"
msgstr "Paramètres de recherche"

msgid "page.search.submit"
msgstr "Recherche"

msgid "page.search.too_long_broad_query"
msgstr "La recherche a pris trop de temps, ce qui est courant pour les requêtes larges. Le nombre de filtres peut ne pas être précis."

msgid "page.search.too_inaccurate"
msgstr "La recherche a pris trop de temps, ce qui risque de vous produire des résultats inexacts. <a %(a_reload)s>Recharger</a> la page peut être parfois utile."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Afficher"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Liste"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tableau"

msgid "page.search.advanced.header"
msgstr "Avancé"

msgid "page.search.advanced.description_comments"
msgstr "Rechercher dans les descriptions et commentaires dans les métadonnées"

msgid "page.search.advanced.add_specific"
msgstr "Ajouter un champs de recherche spécifique"

msgid "common.specific_search_fields.select"
msgstr "(rechercher un champs spécifique)"

msgid "page.search.advanced.field.year_published"
msgstr "Année de publication"

msgid "page.search.filters.content.header"
msgstr "Contenu"

msgid "page.search.filters.filetype.header"
msgstr "Type de fichier"

msgid "page.search.more"
msgstr "plus…"

msgid "page.search.filters.access.header"
msgstr "Accès"

msgid "page.search.filters.source.header"
msgstr "Source"

msgid "page.search.filters.source.scraped"
msgstr "arpentés et libérés par AA"

msgid "page.search.filters.language.header"
msgstr "Langue"

msgid "page.search.filters.order_by.header"
msgstr "Trier par"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Le plus pertinent"

msgid "page.search.filters.sorting.newest"
msgstr "Le plus récent"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(année de parution)"

msgid "page.search.filters.sorting.oldest"
msgstr "Le plus ancien"

msgid "page.search.filters.sorting.largest"
msgstr "Le plus volumineux"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(taille du fichier)"

msgid "page.search.filters.sorting.smallest"
msgstr "Le moins volumineux"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(libéré par AA)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aléatoire"

msgid "page.search.header.update_info"
msgstr "L'index de recherche est mis à jour mensuellement. Il comprend actuellement les entrées jusqu'au %(last_data_refresh_date)s. Pour plus d'informations techniques, voir %(link_open_tag)s la page concernant les jeux de données utilisés</a>."

msgid "page.search.header.codes_explorer"
msgstr "Pour explorer l'index de recherche par codes, utilisez <a %(a_href)s>l'Explorateur de Codes</a>."

msgid "page.search.results.search_downloads"
msgstr "Taper ici votre texte pour effectuer une recherche dans notre catalogue de %(count)s fichiers en téléchargement direct, que nous <a %(a_preserve)s>préservons pour toujours</a>."

msgid "page.search.results.help_preserve"
msgstr "En fait, tout le monde peut aider à préserver ces fichiers en essaimant notre <a %(a_torrents)s> liste unifiée de torrents</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Ce catalogue ouvert de livres, articles, et autres œuvres écrites que nous avons est actuellement le plus exhaustif au monde. Nous reproduisons les catalogues de Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>et d'autres encore</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Si vous découvrez d'autres bibliothèques fantôme (\"shadow librairies\") dont nous devrions faire la reproduction, ou si vous avez une quelconque question, merci de nous contacter à l'adresse %(email)s."

msgid "page.search.results.dmca"
msgstr "Pour les DMCA / réclamations de droits d'auteur <a %(a_copyright)s>cliquez ici</a>."

msgid "page.search.results.shortcuts"
msgstr "Conseil : utilisez les raccourcis clavier \"/\" (recherche), \"enter\" (recherche), \"j\" (haut), \"k\" (bas) pour une navigation plus rapide."

msgid "page.search.results.looking_for_papers"
msgstr "Vous cherchez des articles scientifiques ?"

msgid "page.search.results.search_journals"
msgstr "Tapez ici votre texte pour effectuer une recherche dans notre catalogue de %(count)s articles de journaux et académiques, que nous <a %(a_preserve)s>préservons pour toujours</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Tapez ici votre texte pour effectuer une recherche dans les bibliothèques de prêt numérique."

msgid "page.search.results.digital_lending_info"
msgstr "Cet index de recherhe contient actuellement des métadonnées provenant de la librairie de prêt numérique (Controlled Digital Lending) de l'Internet Archive. <a %(a_datasets)s>Plus d'informations sur notre panel de données</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Pour plus de librairies de prêt numérique, consultez <a %(a_wikipedia)s>Wikipédia</a> et le <a %(a_mobileread)s>wiki MobileRead</a>."

msgid "page.search.results.search_metadata"
msgstr "Tapez ici les métadonnées de bibliothèques à rechercher. Cela peut être utile lorsque vous <a %(a_request)s>faites une demande de fichier</a>."

msgid "page.search.results.metadata_info"
msgstr "Cet index de recherche contient actuellement des métadonnées provenant de diverses sources. <a %(a_datasets)s>Plus d'informations sur nos jeux de données</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Concernant les métadonnées, nous affichons celles d'origine. Nous ne fusionnons aucunes métadonnées entre elles."

msgid "page.search.results.metadata_info_more"
msgstr "Il existe dans le monde beaucoup, beaucoup de sources de métadonnées différentes pour les œuvres écrites. <a %(a_wikipedia)s>Cette page Wikipedia</a> est un bon début, mais is vous connaissez l'existence d'autres bonnes listes, merci de nous en faire part."

msgid "page.search.results.search_generic"
msgstr "Tapez ici votre texte pour effectuer une recherche."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Ce sont des enregistrements de métadonnées, <span %(classname)s>pas</span> des fichiers téléchargeables."

msgid "page.search.results.error.header"
msgstr "Erreur lors de la recherche."

msgid "page.search.results.error.unknown"
msgstr "Essayez de <a %(a_reload)s>recharger la page</a>. Si le problème persiste, merci de nous envoyer un mail à %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Aucun fichier trouvé.</span> Essayez de réduire le nombre de mots-clés, de filtres ou d'en changer."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Parfois, cela se produit incorrectement lorsque le serveur de recherche est lent. Dans ce cas, <a %(a_attrs)s>recharger</a> peut aider."

msgid "page.search.found_matches.main"
msgstr "Nous avons trouvé des correspondances dans : %(in)s. Vous pouvez vous référer à l'URL qui s'y trouve lorsque vous <a %(a_request)s>demandez un fichier</a>."

msgid "page.search.found_matches.journals"
msgstr "Articles de journaux (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Digital Lending (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Métadonnées (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Résultats %(from)s à %(to)s (%(total)s au total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ correspondances partielles"

msgid "page.search.results.partial"
msgstr "%(num)d correspondances partielles"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Bénévolat & Récompenses"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive repose sur des bénévoles comme vous. Nous accueillons tous les niveaux d'engagement et recherchons principalement deux types d'aide :"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Travail bénévole léger :</span> si vous ne pouvez consacrer que quelques heures de temps en temps, il existe encore de nombreuses façons de nous aider. Nous récompensons les bénévoles réguliers avec des <span %(bold)s>🤝 adhésions à Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Travail de bénévolat intensif (primes de 50 USD à 5 000 USD) :</span> si vous pouvez consacrer beaucoup de temps et/ou de ressources à notre mission, nous serions ravis de travailler plus étroitement avec vous. Vous pourrez éventuellement rejoindre l'équipe interne. Bien que notre budget soit serré, nous sommes en mesure d'attribuer des <span %(bold)s>💰 primes monétaires</span> pour le travail le plus intense."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si vous ne pouvez pas offrir de votre temps, vous pouvez toujours nous aider beaucoup en <a %(a_donate)s>faisant un don</a>, en <a %(a_torrents)s>partageant nos torrents</a>, en <a %(a_uploading)s>téléchargeant des livres</a>, ou en <a %(a_help)s>parlant de l'Archive d'Anna à vos amis</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Entreprises :</span> nous offrons un accès direct à haute vitesse à nos collections en échange d'un don de niveau entreprise ou en échange de nouvelles collections (par exemple, nouveaux scans, datasets OCRisés, enrichissement de nos données). <a %(a_contact)s>Contactez-nous</a> si cela vous concerne. Voir aussi notre <a %(a_llm)s>page LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Bénévolat léger"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si vous avez quelques heures à consacrer, vous pouvez aider de plusieurs façons. Assurez-vous de rejoindre le <a %(a_telegram)s>chat des bénévoles sur Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "En signe de reconnaissance, nous offrons généralement 6 mois de “Bibliothécaire Chanceux” pour les jalons de base, et plus pour un travail de bénévolat continu. Tous les jalons nécessitent un travail de haute qualité — un travail bâclé nous nuit plus qu'il ne nous aide et nous le rejetterons. Veuillez <a %(a_contact)s>nous envoyer un email</a> lorsque vous atteignez un jalon."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tâche"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Jalon"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Diffuser la nouvelle de l'Archive d'Anna. Par exemple, en recommandant des livres sur AA, en partageant nos articles de blog, ou en dirigeant les gens vers notre site web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s liens ou captures d'écran."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Cela devrait montrer que vous informez quelqu'un de l'Archive d'Anna, et qu'il vous remercie."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Améliorer les métadonnées en <a %(a_metadata)s>liant</a> avec Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Vous pouvez utiliser la <a %(a_list)s >liste des problèmes de metadata aléatoires</a> comme point de départ."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assurez-vous de laisser un commentaire sur les problèmes que vous résolvez, afin que d'autres ne dupliquent pas votre travail."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s liens des enregistrements que vous avez améliorés."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traduire</a> le site web."

msgid "page.volunteering.table.translate.milestone"
msgstr "Traduire entièrement une langue (si elle n'était pas déjà presque terminée.)"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Améliorer la page Wikipedia de l'Archive d'Anna dans votre langue. Inclure des informations de la page Wikipedia d'AA dans d'autres langues, ainsi que de notre site web et blog. Ajouter des références à AA sur d'autres pages pertinentes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Lien vers l'historique des modifications montrant que vous avez apporté des contributions significatives."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Répondre aux demandes de livres (ou d'articles, etc.) sur les forums de Z-Library ou de Library Genesis. Nous n'avons pas notre propre système de demande de livres, mais nous mirroirons ces bibliothèques, donc les améliorer rend également l'Archive d'Anna meilleure."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s liens ou captures d'écran des demandes que vous avez satisfaites."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Petites tâches postées sur notre <a %(a_telegram)s>chat des bénévoles sur Telegram</a>. Généralement pour l'adhésion, parfois pour de petites primes."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Petites tâches publiées dans notre groupe de discussion de bénévoles."

msgid "page.volunteering.table.misc.milestone"
msgstr "Dépend de la tâche."

msgid "page.volunteering.section.bounties.heading"
msgstr "Primes"

msgid "page.volunteering.section.bounties.text1"
msgstr "Nous recherchons toujours des personnes ayant de solides compétences en programmation ou en sécurité offensive pour s'impliquer. Vous pouvez jouer un rôle important dans la préservation de l'héritage de l'humanité."

msgid "page.volunteering.section.bounties.text2"
msgstr "En guise de remerciement, nous offrons des abonnements pour les contributions solides. En guise de plus grand remerciement, nous offrons des primes monétaires pour les tâches particulièrement importantes et difficiles. Cela ne doit pas être considéré comme un remplacement d'emploi, mais c'est une incitation supplémentaire et peut aider à couvrir les coûts engagés."

msgid "page.volunteering.section.bounties.text3"
msgstr "La plupart de notre code est en open source, et nous vous demanderons d'accepter que le vôtre le soit aussi lors de l'attribution de la prime. Il y a quelques exceptions que nous pouvons discuter au cas par cas."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Les primes sont attribuées à la première personne qui termine une tâche. N'hésitez pas à commenter un ticket de prime pour informer les autres que vous travaillez sur quelque chose, afin que les autres puissent attendre ou vous contacter pour faire équipe. Mais sachez que les autres sont toujours libres de travailler dessus et d'essayer de vous devancer. Cependant, nous n'attribuons pas de primes pour un travail bâclé. Si deux soumissions de haute qualité sont faites à peu près en même temps (dans un délai d'un jour ou deux), nous pourrions choisir d'attribuer des primes aux deux, à notre discrétion, par exemple 100%% pour la première soumission et 50%% pour la deuxième soumission (soit 150%% au total)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Pour les primes les plus importantes (en particulier les primes de scraping), veuillez nous contacter lorsque vous avez complété environ 5%% de la tâche et que vous êtes convaincu que votre méthode pourra s'adapter à l'ensemble de l'objectif. Vous devrez partager votre méthode avec nous afin que nous puissions donner notre avis. De plus, de cette manière, nous pouvons décider quoi faire s'il y a plusieurs personnes proches d'obtenir une prime, comme potentiellement l'attribuer à plusieurs personnes, encourager les gens à faire équipe, etc."

msgid "page.volunteering.section.bounties.text6"
msgstr "AVERTISSEMENT : les tâches à haute prime sont <span %(bold)s>difficiles</span> — il peut être avisé de commencer par des tâches plus faciles."

msgid "page.volunteering.section.bounties.text7"
msgstr "Allez sur notre <a %(a_gitlab)s>liste de problèmes Gitlab</a> et triez par « Priorité du label ». Cela montre à peu près l'ordre des tâches qui nous importent. Les tâches sans primes explicites sont toujours éligibles à une adhésion, en particulier celles marquées «Accepted » et « Anna's favorite ». Il sera peut-être nécessaire de commencer par un « Starter project »."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Mises à jour sur <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, la plus grande bibliothèque véritablement ouverte de l'histoire humaine."

msgid "layout.index.title"
msgstr "Archives d'Anna"

msgid "layout.index.meta.description"
msgstr "La plus grande bibliothèque open-source et open-data au monde. Inclus Sci-Hub, Library Genesis, Z-Library, et plus."

msgid "layout.index.meta.opensearch"
msgstr "Rechercher dans les Archives d'Anna"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Les Archives d'Anna ont besoin de votre aide !"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Beaucoup essaient de nous faire tomber, mais nous continuons la lutte."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si vous faites un don maintenant, vous obtenez <strong>le double</strong> du nombre de téléchargements rapides."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valable jusqu'à la fin du mois en cours."

msgid "layout.index.header.nav.donate"
msgstr "Contribuer"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Sauver le savoir humain : un super cadeau de vacances !"

msgid "layout.index.header.banner.surprise"
msgstr "Surprenez un proche, offrez-lui un compte avec adhésion."

msgid "layout.index.header.banner.mirrors"
msgstr "Pour augmenter la résilience des Archives d'Anna, nous recherchons des volontaires capables de maintenir des archives-miroir."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Le parfait cadeau pour la Saint-Valentin !"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Nous disposons d'une nouvelle méthode de don : %(method_name)s. S'il vous plait, considérez %(donate_link_open_tag)sdonner </a> - Faire fonctionner ce site coûte cher, et vos dons font vraiment la différence. Merci beaucoup."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Nous menons une campagne de financement pour <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">sauvegarder</a> la plus grande bibliothèque de comics au monde. Merci pour votre soutien ! <a href=\"/donate\">Faire un don.</a> Si vous ne pouvez pas faire de don, soutenez nous en parlant à vos amis, et en nous suivant sur <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Téléchargements récents :"

msgid "layout.index.header.nav.search"
msgstr "Rechercher"

msgid "layout.index.header.nav.faq"
msgstr "FAQ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Améliorer les métadonnées"

msgid "layout.index.header.nav.volunteering"
msgstr "Bénévolat & Récompenses"

msgid "layout.index.header.nav.datasets"
msgstr "Jeux de données"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activité"

msgid "layout.index.header.nav.codes"
msgstr "Explorateur de Codes"

msgid "layout.index.header.nav.llm_data"
msgstr "Données de LLM"

msgid "layout.index.header.nav.home"
msgstr "Accueil"

msgid "layout.index.header.nav.annassoftware"
msgstr "Les Logiciels d'Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduire ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Se connecter / S'inscrire"

msgid "layout.index.header.nav.account"
msgstr "Compte"

msgid "layout.index.footer.list1.header"
msgstr "Les Archives d'Anna"

msgid "layout.index.footer.list2.header"
msgstr "Restez en contact"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / réclamations de droits d'auteurs"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Avancé"

msgid "layout.index.header.nav.security"
msgstr "Sécurité"

msgid "layout.index.footer.list3.header"
msgstr "Alternatives"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non affilié"

msgid "page.search.results.issues"
msgstr "❌ Ce fichier pourrait avoir un problème."

msgid "page.search.results.fast_download"
msgstr "Téléchargement rapide"

msgid "page.donate.copy"
msgstr "copier"

msgid "page.donate.copied"
msgstr "copié !"

msgid "page.search.pagination.prev"
msgstr "Précédent"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Suivant"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Miroir #%(num)d : %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% du patrimoine écrit de l'humanité, préservé à jamais %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Base de données ▶ Fichiers ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Téléchargez depuis :"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Nous avons plusieurs options de téléchargement au cas où l'une d'entre elles ne fonctionnerait pas. Elles contiennent toutes exactement le même fichier."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Notez que Anna's Archive n'héberge aucun contenu ici. Nous ne redirigeons que très peu vers d'autres sites. Si vous pensez avoir une plainte DMCA valide, veuillez consulter la %(about_link)spage à propos</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Miroir Anonyme de Z-library #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Faire un don"

#~ msgid "page.donate.header"
#~ msgstr "Faire un don"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive est un projet gratuit à but non lucratif, \"open source\", maintenu entièrement par des volontaires. Nous acceptons les dons pour couvrir nos frais qui comprennent : l'hébergement, les noms de domaine, le développement et d'autres dépenses."

#~ msgid "page.donate.text2"
#~ msgstr "Grâce à vos contributions, nous pouvons faire fonctionner ce site, améliorer ses fonctionnalités et sauvegarder plus de collections."

#~ msgid "page.donate.text3"
#~ msgstr "Dons récents : %(donations)s. Merci à tous pour votre générosité. Nous apprécions vraiment la confiance que vous nous accordez, quel que soit le montant que vous pouvez nous donner."

#~ msgid "page.donate.text4"
#~ msgstr "Pour vos dons, sélectionnez ci-dessous votre moyen de paiement préféré. En cas de problème, contactez nous à %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Carte de crédit/débit"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Crypto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Questions"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Allez sur %(link_open_tag)scette page</a> et suivez les instructions, soit en scannant le code QR ou en cliquant sur le lien “paypal.me”. Si cela ne fonctionne pas, essayez de rafraîchir la page, ceci pourrait vous donner un compte différent."

#~ msgid "page.donate.cc.header"
#~ msgstr "Carte de crédit/débit"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Nous utilisons Sendwyre pour déposer l'argent directement dans notre porte-monnaie Bitcoin (BTC). Cela prend environ 5 minutes à compléter."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Le montant minimum d'une transaction est de 30$ et les frais sont d'environ 5$."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Étapes :"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copiez l'adresse de notre portefeuille Bitcoin (BTC) : %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Allez sur %(link_open_tag)scette page</a> et cliquez sur \"acheter crypto instantané\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Copiez l'adresse de notre portefeuille et suivez les instructions"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Crypto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(Fonctionne aussi pour BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Veuillez utiliser ce compte %(link_open_tag)sAlipay</a> pour envoyer votre don. Si cela ne fonctionne pas, essayez de rafraîchir la page, cela pourrait vous donner un compte différent."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Cette option de don est actuellement en rupture de stock. Veuillez vérifier plus tard. Merci de vouloir nous faire un don, nous apprécions vraiment !"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Merci d'utiliser %(link_open_tag)scette page Pix</a> pour envoyer votre don. Si cela ne fonctionne pas, essayez d'actualiser la page, cela pourrait vous donner un compte différent."

#~ msgid "page.donate.faq.header"
#~ msgstr "Questions fréquentes"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> est un projet qui vise à cataloguer tous les livres existants, en agrégeant des données de sources diverses. Nous suivons également les progrès réalisés par l'humanité pour rendre tous ces livres facilement disponibles sous forme numérique, à travers des “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothèques clandestines</a>”. Apprenez-en plus <a href=\"/about\">à propos de nous.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr "Adhésion : <strong>%s(tier_name)</strong> jusqu'à %(until_date) <a %(a_extend)s>(extend)</a>"

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr "🚀 Téléchargements rapides de nos partenaires (nécéssite de <a %(a_login)s>se connecter</a>)"

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr "🚀 Téléchargements rapides (vous êtes connecté !)"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Livre (tous)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Accueil"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Base de données▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Aucun résultat"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” n'est pas un numéro ISBN valide. Les ISBN comportent 10 ou 13 caractères, sans compter les tirets facultatifs. Tous les caractères doivent être des chiffres, à l'exception du dernier caractère, qui peut également être \"X\". Le dernier caractère est le \"chiffre de contrôle\", il doit correspondre à une valeur de somme de contrôle calculée à partir des autres chiffres. Il doit aussi se trouver dans une plage valide, attribuée par l'Agence internationale de l'ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Fichiers correspondants dans notre base de données :"

#~ msgid "page.isbn.results.none"
#~ msgstr "Aucuns fichiers correspondants dans notre base de données."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Recherche ▶ %(num)d+ résultats pour <span class=\"italic\">%(search_input)s</span> (dans les métadonnées de la bibliothèque clandestine)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Recherche ▶ %(num)d résultats pour <span class=\"italic\">%(search_input)s</span> (dans les métadonnées de la bibliothèque clandestine)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Recherche ▶ Erreur de recherche pour <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Rechercher ▶ Nouvelle recherche"

#~ msgid "page.donate.header.text3"
#~ msgstr "Vous pouvez également faire un don sans créer de compte (les mêmes modes de paiement sont pris en charge pour les dons uniques et les adhésions) :"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Faire un don anonyme unique (sans avantages)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Choisissez un mode de paiement. Veuillez envisager le paiement en crypto-monnaie %(bitcoin_icon)s, car nous payons ainsi (beaucoup) moins de frais."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Si vous avez un portefeuille crypto, voici nos adresses."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Merci beaucoup pour votre aide ! Ce projet ne serait pas possible sans vous."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Pour faire un don avec PayPal, nous allons utiliser PayPal Crypto, ce qui nous permet de rester anonymes. Nous vous remercions de prendre le temps d'apprendre à faire un don en utilisant cette méthode, car cela nous aide beaucoup."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Suivez les instructions pour acheter des bitcoins (BTC). Vous n'avez besoin d'acheter que le montant que vous souhaitez donner."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Si vous perdez des bitcoins en raison de fluctuations ou de frais, <em>ne vous inquiétez pas</em>. C'est normal avec les crypto-monnaies, mais cela nous permet d'opérer de façon anonyme."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Entrez notre adresse Bitcoin (BTC) en tant que destinataire et suivez les instructions pour envoyer votre don :"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Veuillez utiliser <a %(a_account)s>ce compte Alipay</a> pour envoyer votre don."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Veuillez utiliser <a %(a_account)s>ce compte Pix</a> pour envoyer votre don."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Si votre moyen de paiement n'est pas dans la liste, le plus simple serait de télécharger <a href=\"https://paypal.com/\">PayPal</a> ou <a href=\"https://coinbase.com/\">Coinbase</a> sur votre téléphone portable et d'y acheter un peu de Bitcoin (BTC). Vous pouvez ensuite l'envoyer à notre adresse : %(address)s. Dans la plupart des pays, cette procédure ne devrait prendre que quelques minutes."

#~ msgid "page.search.results.error.text"
#~ msgstr "Essayez de <a href=\"javascript:location.reload()\">rafraîchir la page</a>. Si le problème persiste, merci de nous le faire savoir sur <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Pour devenir membre, veuillez <a href=\"/login\"> vous inscrire ou vous connecter</a>. Si vous préférez ne pas créer de compte, sélectionnez “faire un don ponctuel anonyme” ci-dessus. Merci pour votre soutien !"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Accueil"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "À propos"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donner"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Base de données"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Application mobile"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog d'Anna"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Logiciels d'Anna"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traduire"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s torrents"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;copie du contenu de %(libraries)s, et plus."

#~ msgid "page.home.preservation.text"
#~ msgstr "Nous préservons les livres, articles de recherche, bandes-dessinées, magazines, et plus, en rassemblant diverses <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothèques clandestines</a> en un seul lieu. Toutes ces données sont préservées éternellement grâce à la possibilité de les dupliquer facilement en masse, ce qui permet l'existence de nombreuses copies dans le monde. Cette large distribution, combinée à un code open-source, permet également à notre site de résister aux tentatives de fermeture. En savoir plus sur<a href=\"/datasets\"> l'ensemble de nos données</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Base de données ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Aucun résultat"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" ne ressemble pas à un DOI. Cela devrait commencer par \"10.\" et contenir une barre oblique."

#~ msgid "page.doi.box.header"
#~ msgstr "doi : %(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL canonique : %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Ce fichier pourrait être sur %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Fichiers correspondants dans notre base de données :"

#~ msgid "page.doi.results.none"
#~ msgstr "Aucuns fichiers correspondants dans notre base de données."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Téléchargements rapides</strong> Vous avez épuisé vos téléchargements rapides pour aujourd'hui. Veuillez contacter Anna à l'adresse %(email)s si vous souhaitez augmenter votre adhésion."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Vous avez utilisé tous vos téléchargements rapides pour aujourd'hui. Contactez Anna à l'adresse %(email)s si vous souhaitez augmenter votre adhésion."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Puis-je contribuer autrement ?</div> Oui ! Regardez <a href=\"/about\">la page</a> sous \"Comment aider\"."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Je n'aime pas le fait que vous “monétisiez” Anna’s Archive !</div> Si vous n'aimez pas la manière dont nous gérons notre projet, n'hésitez pas à créer votre propre bibliothèque clandestine ! L'entièreté de notre code et de nos données est open source, donc rien ne vous en empêche. ;)"

#~ msgid "page.request.title"
#~ msgstr "Demander un livre"

#~ msgid "page.request.text1"
#~ msgstr "Pour le moment, pouvez-vous faire vos demandes d'eBooks sur le forum de <a %(a_forum)s>Libgen.rs ? Vous pouvez y créer un compte et poster dans l'un de ces fils de discussion :"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Pour les eBooks, veuillez utiliser<a %(a_ebook)s>ce fil de discussion</a>.</li><li %(li_item)s>Pour les livres n'existant pas en eBook, veuillez utiliser <a %(a_regular)s>ce fil de discussion</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Dans les deux cas, veillez à suivre les règles mentionnées dans les fils de discussion."

#~ msgid "page.upload.title"
#~ msgstr "Téléverser"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Gros téléversements"

#~ msgid "page.about.title"
#~ msgstr "À propos"

#~ msgid "page.about.header"
#~ msgstr "À propos"

#~ msgid "page.home.search.header"
#~ msgstr "Rechercher"

#~ msgid "page.home.search.intro"
#~ msgstr "Faites une recherche dans notre catalogue de bibliothèques clandestines."

#~ msgid "page.home.random_book.header"
#~ msgstr "Livre aléatoire"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Accédez à un livre au hasard dans le catalogue."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Livre aléatoire"

#~ msgid "page.about.text1"
#~ msgstr "Les Archives d'Anna sont un métamoteur de recherche open-source et gratuit à but non lucratif, focalisé sur les “<a href=\"https://fr.wikipedia.org/wiki/Bibliothèque_clandestine\">bibliothèques clandestines</a>”. Elles ont été crées par <a href=\"http://annas-blog.org\">Anna</a>, qui a estimé nécessaire d'avoir un lieu central où l'on puisse rechercher des livres, des articles, des bandes dessinées, des magazines, et d'autres documents."

#~ msgid "page.about.text4"
#~ msgstr "Si vous avez une plainte DMCA valide, voyez en bas de cette page ou contactez-nous à %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Explorer les livres"

#~ msgid "page.home.explore.intro"
#~ msgstr "Il s'agit d'une combinaison de livres populaires et de livres qui ont une importance particulière dans le monde des bibliothèques clandestines et de la préservation numérique."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat non-officiel"

#~ msgid "page.wechat.body"
#~ msgstr "Nous avons une page WeChat non-officielle, maintenue par des membres de la communauté. Utilisez le code ci-dessous pour y accéder."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "À propos"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Application mobile"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "WeChat non-officiel"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Demander un livre"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Téléverser"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Parrainer un ami"

#~ msgid "page.about.help.header"
#~ msgstr "Comment aider"

#~ msgid "page.refer.title"
#~ msgstr "Parrainez vos amis pour bénéficier de téléchargements rapides en plus"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Les membres peuvent parrainer des amis pour gagner des téléchargements bonus."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Pour chaque ami qui devient membre :"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Vos amis parrainés</strong> bénéficieront de %(percentage)s%% de téléchargements bonus en plus de leurs téléchargements journaliers habituels, pour toute la durée de leur adhésion."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Vous-même</strong> bénéficierez du même nombre de téléchargements bonus en plus de vos téléchargements journaliers habituels, pour toute la durée d'adhésion de votre ami (pour un maximum de %(max)s téléchargements bonus quelque soit la situation). Vous devez-vous même maintenir votre statut d'adhérent pour bénéficier de ces téléchargements bonus."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Exemple :"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Votre ami utilise le lien de parrainage et adhère pour une période de 3 mois en tant que \"Libraire Lucide\", ce qui lui octroie %(num)s téléchargements rapides."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Ils bénéficieront de %(num)s téléchargements bonus chaque jour, pour toute cette période d'adhésion de 3 mois."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Vous bénéficierez également de %(num)s téléchargements bonus chaque jour, pour cette même période de 3 mois."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Lien de parrainage :</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Connectez-vous</a> et devenez membre pour parrainer vos amis."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Devenez membre</a> pour parrainer vos amis."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Ou ajoutez %(referral_suffix)s à la fin de n'importe quel autre lien, et l'affiliation sera prise en compte lorsqu'ils deviendront membre."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Faites un don total de %(total)s avec <a %(a_account)s>ce compte Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Un autre solution consiste à les téléverser sur Z-Library <a %(a_upload)s>ici</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Pour augmenter la résilience des Archives d'Anna, nous recherchons des volontaires capables de maintenir des archives-miroir. <a href=\"/mirrors\">En savoir plus…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Archives-miroir : appel à volontaires"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "seulement ce mois-ci !"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub a <a %(a_closed)s>suspendu</a> le téléchargement de nouveaux articles."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Sélectionnez un mode de paiement. Nous offrons des réductions pour les paiements par crypto-monnaie %(bitcoin_icon)s, car nos frais de transaction sont (beaucoup) plus faibles dans ce cas."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Sélectionnez un mode de paiement. Nous n'avons actuellement que des moyens de paiements basés sur les cryptomonnaies %(bitcoin_icon)s, car les intermédiaires de paiement traditionnels refusent de travailler avec nous."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nous ne pouvons pas accepter les cartes de crédit/débit directement, car les banques ne veulent pas travailler avec nous. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Cependant, il existe plusieurs façons d'utiliser des cartes de crédit/débit, en utilisant nos autres méthodes de paiement :"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Téléchargements lents et externes"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Téléchargements"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si vous utilisez une cryptomonnaie pour la première fois, nous vous suggérons d'utiliser %(option1)s, %(option2)s ou %(option3)s pour acheter et faire un don en Bitcoin (la plus utilisée et la plus ancienne des cryptomonnaies)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 liens de dossiers que vous avez améliorés."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 liens ou captures d'écran."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 liens ou captures d'écran des demandes que vous avez satisfaites."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si vous êtes intéressé par le mirroring de ces datasets à des fins d'<a %(a_faq)s>archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si vous êtes intéressé par le miroir de cet ensemble de données à des fins d'<a %(a_archival)s>archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Site principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informations sur les pays ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si vous êtes intéressé par le miroir de cet ensemble de données à des fins d'<a %(a_archival)s>archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L'Agence internationale de l'ISBN publie régulièrement les plages qu'elle a attribuées aux agences nationales de l'ISBN. À partir de cela, nous pouvons déterminer à quel pays, région ou groupe linguistique appartient cet ISBN. Nous utilisons actuellement ces données indirectement, via la bibliothèque Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ressources"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Dernière mise à jour : %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Site web de l'ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Métadonnées"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluant “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Notre aspiration concernant la collecte de métadonnées est l'objectif d'Aaron Swartz \"une page pour chaque livre qui ait jamais existé\", pour lequel il a créé <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ce projet a fait du bon travail, mais notre position particulière nous permet de récupérer des métadonnées qui leur sont inaccessibles."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Une autre aspiration était notre envie de savoir <a %(a_blog)s>combien de livres il existe dans le monde</a>, afin de calculer combien de livres il nous reste encore à sauver."

#~ msgid "page.partner_download.text1"
#~ msgstr "Afin de donner à tout le monde l'opportunité de télécharger des fichiers gratuitement, veuillez attendre <strong>%(wait_seconds)s secondes</strong> avant de pouvoir télécharger ce fichier."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Rafraîchissement automatique de la page. Si vous manquez la fenêtre de téléchargement, le minuteur redémarrera, donc le rafraîchissement automatique est recommandé."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Télécharger maintenant"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Conversion : utilisez des outils en ligne pour convertir les fichiers. Par exemple, pour convertir de epub vers pdf, utilisez <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle : téléchargez le fichier (pdf et epub sont pris en charge), puis <a %(a_kindle)s>envoyez-le à votre Kindle</a> via le site web, l'application ou par email. Outils utiles : <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Soutenez les auteurices : Si vous aimez ce que vous lisez et que vous pouvez vous le permettre, envisagez d'acheter l'original ou de soutenir directement les auteurices."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Soutenez les bibliothèques : si ce document est disponible dans votre bibliothèque locale, envisagez de l'emprunter gratuitement sur place."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Pas disponible directement en vrac, seulement en semi-vrac derrière un paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive gère une collection de <a %(isbndb)s>métadonnées ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb est une entreprise qui extrait des métadonnées ISBN de diverses librairies en ligne. Les Archives d'Anna ont sauvegardé les métadonnées des livres d'ISBNdb. Ces métadonnées sont disponibles via les Archives d'Anna (mais pas encore dans la recherche, sauf si vous recherchez explicitement un numéro ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Pour les détails techniques, voir ci-dessous. À un moment donné, nous pourrons l'utiliser pour déterminer quels livres manquent encore dans les bibliothèques de l'ombre, afin de prioriser les livres à trouver et/ou à numériser."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Notre article de blog sur ces données"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Extraction ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actuellement, nous avons un seul torrent, qui contient un fichier <a %(a_jsonl)s>JSON Lines</a> de 4,4 Go compressé en gzip (20 Go décompressé) : « isbndb_2022_09.jsonl.gz ». Pour importer un fichier « .jsonl » dans PostgreSQL, vous pouvez utiliser quelque chose comme <a %(a_script)s>ce script</a>. Vous pouvez même le canaliser directement en utilisant quelque chose comme %(example_code)s pour qu'il se décompresse à la volée."

#~ msgid "page.donate.wait"
#~ msgstr "Merci d'attendre au moins <span %(span_hours)s>deux heures</span> (puis de rafraîchir cette page) avant de nous contacter."

#~ msgid "page.codes.search_archive"
#~ msgstr "Rechercher dans les Archives d’Anna pour “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Faites un don en utilisant Alipay ou WeChat. Vous pourrez choisir entre les deux sur la page suivante."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Faire connaître l'Archive d'Anna sur les réseaux sociaux et les forums en ligne, en recommandant des livres ou des listes sur AA, ou en répondant à des questions."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La collection de Fiction a évoluée mais est toujours desservie par des <a %(libgenli)s>torrents</a>, cependant pas mis à jour depuis 2022 (nous proposons des téléchargements direct)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Les Archives d'Anna et Libgen.li gèrent des collections de <a %(comics)s>bandes-dessinées</a> et de <a %(magazines)s>magazines</a> en collaboration."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Pas de torrents pour la fiction russe et les collections standard de documents."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Il n'y a pas de torrents disponibles pour le contenu supplémentaire. Les torrents présents sur le site Libgen.li sont des sites miroirs d'autres torrents listés ici. La seule exception concerne les torrents de fiction à partir de %(fiction_starting_point)s. Les torrents de bandes dessinées et de magazines sont publiés en collaboration entre Les Archives d’Anna et Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "D'une collection <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origine exacte incertaine. En partie de the-eye.eu, en partie d'autres sources."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

