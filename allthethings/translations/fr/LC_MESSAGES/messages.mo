��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  rd X  g .   ah )  �h #  �i   �k �  �m L  �o U   �p {   6q H   �q q   �q   mr �  rt �   v   w   y K  $z P  p| #  �~ �   � #  Ձ �  �� J   ǅ �   � \   �� V  W� "   �� �  щ H   b� %   ��    ь    � L   �� *   K�    v� 8   ��    Í ,   �    � T   &� g  {� ?  � U   #� >   y� 
   ��    Ƒ    Ց    �    � 
   � 	   ,�    6�    G� #   N�    r�    x� 	   �� 
   ��    ��    �� #   ے    �� �  � �  Е �   V� .   �� �  )� �   ̙ *  �� �   �� #   �� �   �� #   `� �   �� (   V� =  � #   �� �  � <   �� q   �    X� �  a� u  �   [� V  t�    ˨ T   ݨ e   2� �   �� 2   k� +   �� �   ʪ D   Q� ?   �� 7   ֫    � w   � �  �� C   �� |   Ȯ �   E� G   !� V  i� >   �� !  �� �   !� �   Ƴ a   �� �   � �   ˵ H   i�   �� �   �� i   �� H   �� �   E� 
   ɹ �   ׹ L  �� H   � 
   0� �   ;� 	  �    #� �  +� �   ο   �� �  �� �   �� �  o� ?  �� \   <� Y   �� Z   �� �   N� l   �� \   U� 6   �� �   �� p   �� ?   ��    3� �   K� h   �� �   <� �   !�    ��   ��   � X  ,� �  �� �   /� �   �� `  �� �   � �   �� �   �� s  T� �   ��   ��    �� �   �� �   R� �   >� t  	� �   ~� �   � �   �� 1  X� �   �� [  
� 5  i� -   �� _   �� �   -� V   �� �  A� �   � x   �� �   @� 1   �� s  �� �   k� �  X�    � l   ,� �   �� �   3�   �   /�   <� �  V� 1   1� �  c� �  �� H  �    , j  A C  � p   � �   a    	 �   	 l  �	 �  T `  1
 !  � �   � 
   q �   �   �   � 
   � �   � x  d �   � d  �    X   #    | �  � �  C    � �   � 6  � H  (!   q# �  v$ j   & O   �&   �& 4   �' %   &( _  L( -  �) L  �* 9   ', H   a, P  �, �   �. �   �/ �   0 �  �0     X2    y2 �  �2 �   I4 �   '5 R   �5 �   6 3   �6 5  �6   �7    9 
   $9 )   /9 ?   Y9 C   �9 <   �9 h   : �   �:    ; ,   "; I   O; f   �; '    < ;   (< 5   d< �   �<    4=    := E  T= r  �>   
@ !  "A �   DB �   �B X  �C    $E �   ;E �   3F �   �F �  �G �  =I [  K �  mM �  jO H   =Q I   �Q .   �Q �   �Q >  �R �  U f  �V }  2X 2  �Y \  �Z <  @] 0   }^ _  �^ g  ` �  va    c   "c �  $d �   �e �  Kf   �g 	   �h 1   �h v   *i 
   �i �  �i �  `l �  
n �   �p �   jq    Jr   Zr *  `s H   �t s   �t C  Hu 4  �v �   �w �  ax p   #z B  �z    �{ '  �{ �   ~ (  �~ �  �� (  ʃ �   � 
   Æ   Ά   �    �� �  
� -  �� m  ō p  3� )  �� t  Γ H   C�   �� �   �� �   �� K   � u   ]�    ә >   ڙ (   � -   B� )   p�    �� �   �� c  `�   Ğ g  ؠ �   @�    1� +  =� _  i� |  ɩ �  F� �  � �  Ư 
   �� 
  ��    ��    �� 
  �� }  ˵ a  I� �  ��    e� A  u�   ��   �� E  �� Y   � }  z� �  �� &   �� �   �� o   �� �  � �  � �   � �   �� �   o� �  >�    �� �   �� V   �� U   .�    �� 4   �� =   �� �  � �  �� 
  ~� C   �� 5  �� �   �    �� �   �� L   �� �   �� �   u� 9   n� k   ��    � �  /� �  �� �  Z� �  B� �    �    �� }   �� $  3�    X� �   _�   � �   � 7   �� A   �� �  6� .   �� �  �� �  �� P  �� $   �  �   �  �   � m   �  �    j   { 5   � �   � �   v D  T	 �   �
 g   � .   �   * 3   9
 �  m
 X  : �   �   l l   t A   � �   # n  � �  K ;  � .  6 >   e �   � /  � [  � �   "! <  " �  @$ .   1& )  `& 1   �( 
  �( V   �*    + �  8+ ;  �- �  �/   �1 P  �2 '  74 d  _6 �   �9 �  �: �  �< �   o> l  ? �  tB B   �C �   BD �   �D    nE    �E a  �E �   �F :   �G 9  �G �  8I b  /K ;  �L   �N �   �P �  �Q �  �S !   U �   @U    
V �  V !   �W -   �W    "X    (X *   8X    cX    |X    �X    �X    �X 	   �X    �X    �X    �X $   �X    Y (   Y    DY    JY    YY    aY �   iY j   QZ %   �Z 
   �Z 9   �Z ,   *[ ;   W[ 4   �[ #   �[    �[    �[    \    \    ,\    <\    O\    X\    m\    s\ 4   �\ '   �\    �\ "   �\ &   "] 2   I] .   |]    �] #   �] ;   �]     "^ d   C^ H   �^    �^ ]   �^ @   U_    �_    �_ !   �_    �_    �_    
`    `    -`    E`    M`    e`    r`    �` 	   �` 
   �`    �` M   �`    a    a 	    a    *a 	   ;a    Ea    \a    ba 	   ia    sa    �a    �a    �a    �a    �a    �a    �a 	   b    b D   1b 
   vb    �b 2   �b    �b    �b    �b "   �b    c    c     .c �   Oc �   �c c   �d �   �d �  �e �   �g    +h (   ?h    hh    zh    �h    �h    �h .   �h q   �h F   Ti q   �i )   
j :   7j 2   rj �   �j ~   4k �   �k j   �l 9   m *   =m    hm    zm 	   �m    �m    �m    �m    �m    �m    �m 
   �m    �m    
n    n    n    6n    Fn    `n 
   in 
   wn 
   �n    �n    �n    �n    �n ,  �n    p    
p    p ,   "p    Op _   Vp b   �p .   q 2   Hq >   {q    �q    �q    �q �   �q    Sr    Yr (   ir t   �r #   s    +s {   ;s /   �s ^  �s Z   Fw �   �w �   Dx �   �x :   �y @  z �   [{ �  �{ �   o} 3   g~    �~ P   �~ ^   �~ b   [ a   � l    � H   �� f   ր     =� 3   ^� 	   ��    �� M   �� #   ��    #�    *�    D�    Q� k   l� 
   ؂ 0   � S   �    k� "   �� U   �� d   �� �  b�    "�    <� �   X�    9� 
   E�    P�    Y�    v� .   � u  ��    $�    @�    P�    e� }  n� $   �    �    � l   "�    ��    �� 5   ��    ԋ    � .   �� �   (�    ӌ    � S   �� &   J�    q� 	   ��    �� -   �� l   ͍    :� ?   Q� �   �� �   _� j   �    W�   o� L   ��    ב 3   �    !� �   6� �   ��    Ɠ H   ޓ �   '� �   Ŕ $   �� '   ��    � �  �� 5   ̗ &   �    )� $   G� '   l� �   �� "   M�    p� 7   �� E    
   �    � $   3� &   X� c  � �  � .  s� 8   �� 3   ۠    � $   � �  A� %  ͢ �   � !   �� 
  ͤ   إ M   � 4   .� `  c� �  Ī 
   K� 
   Y� :   g� 
   �� [  ��    	� m  (� �  ��   }�    �� �   �� C   1� =   u� u   �� �  )� 5  �� �   � �  �� s   ?� !  �� m   պ �   C� �   +� U   �� �   O� ,   � &   F�    m� 
   ��    �� (   �� ,   ξ a   �� 2   ]� 	   �� 1   �� U  ̿ =   "�    `� �   a� �   �� &   ��     ��     ��    � 9   +� $   e� ,   �� %   ��   �� )   �� �   �    �� �   �� �   �� �   l� q  �� �   j� /   C� �   s� 	   	� r  � q   �� #   �� �  �    ��    ��    � "   &� *   I�    t�    {� Z   �� �   ��   ��    ��    �� !   �� �   �� F  �� �   �� �   ��    0�    G�    ^�    x�    ��    ��    �� C   �� (   �   *� y   B�    �� {   �� �   O� Q   �� {   #� _   �� V   ��    V� j   _� ^   �� �   )� e   �� c   R�    �� �   �� j   �� I   2� i   |� c   �� G   J� 
   �� >   �� t   �� �   T� M   
� �   [�    � 0  � J   P� c   �� �   ��    �� 3  �� �   ��    �� !   �� 	   � 
   � (  � .   ?� _   n� �   �� �   �� �   �� �   2� �   � �   �� �   S� k   � H  �� z   �� �  H� �   ��   �� 
   �� 
   �� 
   �� �   �� 
   �� 
   �� N   �� g   �� �   b� 
   ?� �   M� 8   �� �   
� <   �� �   �� f   [� 
   �� �   �� 
   e� 
   s� 
   �� _  �� �   �� *  ~�    �    �    � �   � &   � 0   � B  
 �   M !   �         0 =   L ?   � 5   �           ! �   B    $ �  C   
	 �   &
 d   �
 �   $ ;  � /  
 �  M �   � �   � �   Y    � �      � �  �   ^ J  u �   � �  U �   � �   � �   b 1   � E       ] 6   r    �    �    �   �    � �   � 1   l  
   �  	   �  
   �  '   �  �   �  U   o! T   �!   " 5   7#    m#    v#    }# %   �#    �#    �#    �#    �#    �#    �#    �#     $ ,   $ �   5$    �$    �$    �$     %    
%    %    &%    2%    ?%    W% &   f% �   �% 
   & -   !& ^   O& �   �& �   U' �   ( W  �( �   J* b  +    z, w   �, -   �, W   )- X   �- �   �- �   �. L   / k   �/    80 �   I0 �   �0 �   T1    �1     �1    
2    2    62 "   E2    h2 !   p2    �2    �2    �2 !   �2    �2    3    &3    >3    E3 
   X3    f3    n3    �3 1   �3 3   �3 �   �3 �   �4 -   -5 $   [5 _   �5 n   �5    O6 2   �6 /   7 6   27 ;   i7 ;   �7 2   �7 �   8 V  9   c:    |; K   �; �   �; 5   k<   �< �   �= �   �> G   -?    u? �   �? �   I@ �   �@ .   A j   �A �   B "   �B /   C #   >C G   bC �   �C �   <D    �D    �D    �D #   �D �   �D <   �E /   �E 5   F -   ;F "   iF &   �F J   �F &   �F n   %G >   �G !  �G V   �H f   LI �   �I E   kJ     �J    �J     �J    K     3K    TK     tK @   �K ;   �K �  L F   �N >   3O I   rO &   �O    �O w   �O �   hP �   �P �   �Q 
   �R �   �R 3   /S    cS �   wS    `T ,   |T H   �T 4   �T h   'U _   �U R   �U    CV %   `V �   �V 3   cW '   �W W   �W   X L   Y �   lY i   Z a   Z �   �Z 0   c[ <   �[ �   �[    �\ h   �\    ] �   3] =   ^ =   I^    �^ %   �^ �   �^ P   �_ M   	` �   W` Z   a X   fa �   �a �   ^b    �b    �b M   c F   `c    �c    �c    �c    �c     d .   d �   Dd f   �d !   6e )   Xe 4   �e ?   �e F   �e �   >f D   �f �   g m   �g /   �g p   (h   �h 7   �i ^   �i    4j Q   Mj H   �j $   �j �   
k }   �k 4   l D   Kl    �l 6   �l ^   �l    <m y   Pm .   �m    �m 3   n a   En �   �n    �o    �o    �o K   �o    p Y   5p E   �p �   �p �   �q    r B   1r �   tr $   <s �   as E   t '   Ut W   }t   �t 	   �u    �u    �u    �u '   v >   .v f   mv    �v    �v    �v '    w E   (w B   nw 	   �w _   �w \   x    xx +   �x ?   �x 0   �x    *y }   By   �y N   �z    %{    7{ �   F{ d  | d   g}    �} �  �} �  � 3   J� �   ~� #   � �  0� 2   �� �   �    ��    ��    �� "   �� X   � �   =� x   ň o   >� "   �� d   щ I   6� 2   �� {   �� @   /� 9   p� X   �� <   � U   @� �   �� �   A� :   � �   B� �  � �   �� M   � }  ͑ 2  K� �   ~� 3  f�   �� 8   ��    ח �   � 5   �� _  � z   D� N   ��    � !    � �  B�    � �   � B  �� G  @� V  �� I   ߢ ^   )� �   �� 4   � /   M� o   }� c   �    Q� 1   f� :   �� )   ӥ     �� ;   � ~   Z� J   ٦    $� p   +�   �� �   ��    ]�    s� 
   �� Z   �� g   �� g   \� �   Ī �   �� !   ,� -   N�   |� 
   �� �   �� #  J�   n� \   �� 0   �    �    �    $� S   (� 0   |� �   ��   B� h   Z�    õ    ֵ %   � %   � o   5�    �� =   ��    �� 1   � ,   8�    e�    x� l   ��    �    �� )   � '   9�    a� �   e� �   � q   � {   Z� ^   ֺ �   5�    �    +� �   G� �   � �   �� 	   Ͼ �   پ T   [� K   �� f   �� u   c� n   ��    H� X   b�    ��    ��    ��    ��    �    *�    >�    O�    l�    y� 9   �� 9   �� 8   �� +   2� >   ^� 6   �� %   ��    ��    � J   3� "   ~� 
   �� 8   �� .   ��    � ;   ��    ��    �� .   � &   5�    \� L   w� Q   �� [   �    r�    s�    ��    ��    �� 9   �� 	   �    '�    9� �   O� .   �� 3   	�    =� 
   D� 	   O� C   Y�    �� �   ��    ��    ��    �� $   �� (   $� !   M� &   o� ,   �� ,   �� 2   �� 3   #�    W� d   ^� *   ��    ��    
� ;   � V   X�    �� 2   �� *   � y   -� f   �� \   �    k� $   s� 	   ��    ��    �� '   �� �  �� ~   �� #   C�    g� -   l�    �� /   ��    ��    �� �   �� [   ��   ��    �� -   � �   2� -   �� +   � )   4� ,   ^� <   �� 4   ��    ��    � F   &� "   m� ;   �� :   �� d  � I   l� ]   �� ^   � #   s� �   �� *   &�    Q� ]   e�    �� +   ��    � B   � I   b� ,   �� �   ��    ��    �� %   ��    ��    ��    � &   (�    O�    b� e   {� )  ��     � -   ,� �   Z� �   N�    � -   6�    d�    w�     ��    ��    ��    ��    ��    ��    �    � 
   /�    =�    L�    ^�    k�    w� '   �� �   �� #  �� �  �� D  w� �  ��   �� �  ��    z� G  ��    ��   ��   � �  � �   �� �  �� ;   l� �   �� L   s� #   �� E   �� H   *� h   s� m   �� �   J� �   � �   �� w  ��     *� �   K� �   B  j   1 �   �    X �   m �  ^    �    	   � p     �   q   � �   � �   �	 �   .
    �
    �
    �
 D    9   P    � �   � U   ; �   � '   
 �   =
 �   �
 g   � f   � I   e z   � c   * 3   � �   � k   U �   � �   z    � 1   
 ,   < g   i 1   �         U       i     �    � H   � +   � B   "    e    � 
   �    � 	   � o   � �   # Y   � H   �    G *   O G   z    �    �    �    �    �    �    �         	       '    :    L    \    p    �    � 
   �    �    �    �    �      �   '    � `   � �   C    5    = 
   P    ^    f    n    r �   ~ �   e T   �    B    \ w   y    � �   	 �   � +    *   G �   r     t   ! �   �! z   �" 2   �" �   1# "   �# #   !$ _   E$ �   �$ 3   Y% �   �% �   B& �   �& l   ]'    �' 	   �'    �' 
   �'    ( 
   !(    /(    B( �   `( �   �( �   |) �   *    �* ~   , T   �, �  �, �  �.   �0 �   �1 %  V2 p  |3    �4 �   �4 x  �5 �   K7 �   8 -  �: �   )= �  �=    M? L   `? �   �? �  V@ L   �A   -B    =C    CC    JC �   `C I   �C �   GD >   �D o   E N   wE ]   �E %   $F �   JF Q   �F ,   HG j   uG �   �G    �H  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: fr
Language-Team: fr <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n > 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library est une bibliothèque populaire (et illégale). Ils ont pris la collection de Library Genesis et l'ont rendue facilement consultable. En plus de cela, ils sont devenus très efficaces pour solliciter de nouvelles contributions de livres, en incitant les utilisateurs contributeurs avec divers avantages. Actuellement, ils ne contribuent pas ces nouveaux livres à Library Genesis. Et contrairement à Library Genesis, ils ne rendent pas leur collection facilement duplicable, ce qui empêche une large préservation. Cela est important pour leur modèle économique, car ils facturent l'accès à leur collection en masse (plus de 10 livres par jour). Nous ne portons pas de jugement moral sur le fait de facturer l'accès en masse à une collection de livres illégale. Il est indéniable que la Z-Library a réussi à élargir l'accès au savoir et à obtenir plus de livres. Nous sommes simplement ici pour faire notre part : assurer la préservation à long terme de cette collection privée. - Anna et l'équipe (<a %(reddit)s>Reddit</a>) Dans la version originale du Pirate Library Mirror (EDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>), nous avons créé un site miroir de Z-Library, une grande collection de livres illégale. Pour rappel, voici ce que nous avons écrit dans cet article de blog original : Cette collection remonte à la mi-2021. Entre-temps, la Z-Library a connu une croissance fulgurante : elle a ajouté environ 3,8 millions de nouveaux livres. Il y a bien sûr quelques doublons, mais la majorité semble être de nouveaux livres légitimes ou des scans de meilleure qualité de livres précédemment soumis. Cela est en grande partie dû à l'augmentation du nombre de modérateurs bénévoles à la Z-Library et à leur système de téléversement en masse avec déduplication. Nous tenons à les féliciter pour ces réalisations. Nous sommes heureux d'annoncer que nous avons récupéré tous les livres ajoutés à la Z-Library entre notre dernier site miroir et août 2022. Nous avons également récupéré certains livres que nous avions manqués la première fois. En tout, cette nouvelle collection fait environ 24 To, ce qui est bien plus grand que la précédente (7 To). Notre site miroir fait maintenant 31 To au total. Encore une fois, nous avons dédupliqué par rapport à Library Genesis, car il existe déjà des torrents disponibles pour cette collection. Veuillez vous rendre sur le Pirate Library Mirror pour découvrir la nouvelle collection (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d’Anna</a>). Vous y trouverez plus d'informations sur la structure des fichiers et sur ce qui a changé depuis la dernière fois. Nous ne mettrons pas de lien ici, car il s'agit simplement d'un site de blog qui n'héberge aucun matériel illégal. Bien sûr, le partage est également un excellent moyen de nous aider. Merci à tous ceux qui partagent notre précédent ensemble de torrents. Nous sommes reconnaissants pour la réponse positive et heureux qu'il y ait tant de personnes qui se soucient de la préservation du savoir et de la culture de cette manière inhabituelle. 3x nouveaux livres ajoutés au Pirate Library Mirror (+24 To, 3,8 millions de livres) Lisez les articles complémentaires par TorrentFreak : <a %(torrentfreak)s>premier</a>, <a %(torrentfreak_2)s>deuxième</a> - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) articles complémentaires par TorrentFreak : <a %(torrentfreak)s>premier</a>, <a %(torrentfreak_2)s>deuxième</a> Il n'y a pas si longtemps, les « bibliothèques fantômes » étaient en déclin. Sci-Hub, la vaste archive illégale d'articles académiques, avait cessé d'accepter de nouvelles œuvres en raison de poursuites judiciaires. « Z-Library », la plus grande bibliothèque illégale de livres, a vu ses créateurs présumés arrêtés pour des accusations criminelles de violation du droit d'auteur. Ils ont incroyablement réussi à échapper à leur arrestation, mais leur bibliothèque n'en est pas moins menacée. Certains pays font déjà une version de cela. TorrentFreak <a %(torrentfreak)s>a rapporté</a> que la Chine et le Japon ont introduit des exceptions pour l'IA dans leurs lois sur le droit d'auteur. Il n'est pas clair pour nous comment cela interagit avec les traités internationaux, mais cela donne certainement une couverture à leurs entreprises nationales, ce qui explique ce que nous avons observé. Quant à l'Archive d'Anna — nous continuerons notre travail clandestin ancré dans la conviction morale. Pourtant, notre plus grand souhait est d'entrer dans la lumière et d'amplifier notre impact légalement. Veuillez réformer le droit d'auteur. Lorsque Z-Library a été menacée de fermeture, j'avais déjà sauvegardé toute sa bibliothèque et cherchais une plateforme pour l'héberger. C'était ma motivation pour lancer l'Archive d'Anna : une continuation de la mission derrière ces initiatives précédentes. Depuis, nous sommes devenus la plus grande bibliothèque fantôme au monde, hébergeant plus de 140 millions de textes protégés par le droit d'auteur dans de nombreux formats — livres, articles académiques, magazines, journaux, et au-delà. Mon équipe et moi sommes des idéologues. Nous croyons que préserver et héberger ces fichiers est moralement juste. Les bibliothèques du monde entier voient leurs financements réduits, et nous ne pouvons pas non plus faire confiance au patrimoine de l'humanité aux entreprises. Puis est venue l'IA. Pratiquement toutes les grandes entreprises construisant des LLM nous ont contactés pour s'entraîner sur nos données. La plupart (mais pas toutes !) des entreprises basées aux États-Unis ont reconsidéré une fois qu'elles ont réalisé la nature illégale de notre travail. En revanche, les entreprises chinoises ont accueilli notre collection avec enthousiasme, apparemment peu préoccupées par sa légalité. Cela est notable étant donné le rôle de la Chine en tant que signataire de presque tous les grands traités internationaux sur le droit d'auteur. Nous avons donné un accès à haute vitesse à environ 30 entreprises. La plupart d'entre elles sont des entreprises de LLM, et certaines sont des courtiers en données, qui revendront notre collection. La plupart sont chinoises, bien que nous ayons également travaillé avec des entreprises des États-Unis, d'Europe, de Russie, de Corée du Sud et du Japon. DeepSeek <a %(arxiv)s>a admis</a> qu'une version antérieure a été entraînée sur une partie de notre collection, bien qu'ils restent discrets sur leur dernier modèle (probablement aussi entraîné sur nos données cependant). Si l'Occident veut rester en tête dans la course aux LLM, et finalement à l'AGI, il doit reconsidérer sa position sur le droit d'auteur, et rapidement. Que vous soyez d'accord ou non avec nous sur notre position morale, cela devient maintenant une question d'économie, et même de sécurité nationale. Tous les blocs de pouvoir construisent des super-scientifiques, des super-pirates et des super-militaires artificiels. La liberté de l'information devient une question de survie pour ces pays — même une question de sécurité nationale. Notre équipe vient du monde entier, et nous n'avons pas d'alignement particulier. Mais nous encouragerions les pays avec des lois sur le droit d'auteur strictes à utiliser cette menace existentielle pour les réformer. Alors, que faire ? Notre première recommandation est simple : raccourcir la durée du droit d'auteur. Aux États-Unis, le droit d'auteur est accordé pour 70 ans après la mort de l'auteur. C'est absurde. Nous pouvons aligner cela sur les brevets, qui sont accordés pour 20 ans après le dépôt. Cela devrait être plus que suffisant pour que les auteurs de livres, d'articles, de musique, d'art et d'autres œuvres créatives soient pleinement compensés pour leurs efforts (y compris les projets à plus long terme tels que les adaptations cinématographiques). Ensuite, au minimum, les décideurs politiques devraient inclure des exceptions pour la préservation et la diffusion massive de textes. Si la perte de revenus des clients individuels est la principale préoccupation, la distribution au niveau personnel pourrait rester interdite. En retour, ceux capables de gérer de vastes dépôts — les entreprises formant des LLM, ainsi que les bibliothèques et autres archives — seraient couverts par ces exceptions. La réforme du droit d'auteur est nécessaire pour la sécurité nationale En résumé : les LLM chinois (y compris DeepSeek) sont entraînés sur mon archive illégale de livres et d'articles — la plus grande au monde. L'Occident doit réviser le droit d'auteur pour des raisons de sécurité nationale. Veuillez consulter le <a %(all_isbns)s>billet de blog original</a> pour plus d'informations. Nous avons lancé un défi pour améliorer cela. Nous offririons une récompense de 6 000 $ pour la première place, 3 000 $ pour la deuxième place et 1 000 $ pour la troisième place. En raison de la réponse écrasante et des soumissions incroyables, nous avons décidé d'augmenter légèrement le pool de prix et d'attribuer une troisième place à quatre participants, chacun recevant 500 $. Les gagnants sont ci-dessous, mais assurez-vous de consulter toutes les soumissions <a %(annas_archive)s>ici</a>, ou téléchargez notre <a %(a_2025_01_isbn_visualization_files)s>torrent combiné</a>. Première place 6 000 $ : phiresky Cette <a %(phiresky_github)s>soumission</a> (<a %(annas_archive_note_2951)s>commentaire Gitlab</a>) est tout simplement tout ce que nous voulions, et plus encore ! Nous avons particulièrement apprécié les options de visualisation incroyablement flexibles (même avec prise en charge des shaders personnalisés), mais avec une liste complète de préréglages. Nous avons également aimé la rapidité et la fluidité de l'ensemble, la mise en œuvre simple (qui n'a même pas de backend), la carte miniature astucieuse et l'explication détaillée dans leur <a %(phiresky_github)s>billet de blog</a>. Un travail incroyable, et un gagnant bien mérité ! - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nos cœurs sont remplis de gratitude. Idées notables Gratte-ciels pour la rareté Beaucoup de curseurs pour comparer les datasets, comme si vous étiez un DJ. Barre d'échelle avec le nombre de livres. Belles étiquettes. Schéma de couleurs par défaut cool et carte thermique. Vue de carte unique et filtres Annotations, et aussi statistiques en direct Statistiques en direct Quelques autres idées et mises en œuvre que nous avons particulièrement aimées : Nous pourrions continuer encore un moment, mais arrêtons-nous ici. Assurez-vous de regarder toutes les soumissions <a %(annas_archive)s>ici</a>, ou téléchargez notre <a %(a_2025_01_isbn_visualization_files)s>torrent combiné</a>. Tant de soumissions, et chacune apporte une perspective unique, que ce soit dans l'interface utilisateur ou l'implémentation. Nous intégrerons au moins la soumission de première place dans notre site principal, et peut-être d'autres. Nous avons également commencé à réfléchir à la manière d'organiser le processus d'identification, de confirmation, puis d'archivage des livres les plus rares. Plus d'informations à venir sur ce front. Merci à tous ceux qui ont participé. C'est incroyable que tant de gens se soucient. Basculement facile des datasets pour des comparaisons rapides. Tous les ISBN SSNOs de CADAL Fuite de données de CERLALC SSIDs de DuXiu Index des eBooks d'EBSCOhost Google Livres Goodreads Internet Archive ISBNdb Registre mondial des éditeurs ISBN Libby Fichiers dans l'Archive d'Anna Nexus/STC OCLC/Worldcat OpenLibrary Bibliothèque d'État de Russie Bibliothèque Impériale de Trantor Deuxième place 3 000 $ : hypha « Bien que les carrés et rectangles parfaits soient mathématiquement plaisants, ils ne fournissent pas une meilleure localité dans un contexte de cartographie. Je crois que l'asymétrie inhérente à ces courbes de Hilbert ou Morton classiques n'est pas un défaut mais une caractéristique. Tout comme le contour en forme de botte de l'Italie la rend instantanément reconnaissable sur une carte, les "particularités" uniques de ces courbes peuvent servir de repères cognitifs. Cette distinction peut améliorer la mémoire spatiale et aider les utilisateurs à s'orienter, rendant potentiellement plus facile la localisation de régions spécifiques ou la détection de motifs. » Une autre <a %(annas_archive_note_2913)s>soumission</a> incroyable. Pas aussi flexible que la première place, mais nous avons en fait préféré sa visualisation au niveau macro par rapport à la première place (courbe de remplissage d'espace, bordures, étiquetage, surlignage, panoramique et zoom). Un <a %(annas_archive_note_2971)s>commentaire</a> de Joe Davis a résonné avec nous : Et encore beaucoup d'options pour la visualisation et le rendu, ainsi qu'une interface utilisateur incroyablement fluide et intuitive. Une solide deuxième place ! - Anna et l'équipe (<a %(reddit)s>Reddit</a>) Il y a quelques mois, nous avons annoncé une <a %(all_isbns)s>récompense de 10 000 $</a> pour réaliser la meilleure visualisation possible de nos données montrant l'espace ISBN. Nous avons insisté sur la démonstration des fichiers que nous avons/n'avons pas encore archivés, et nous avons ensuite ajouté un ensemble de données décrivant combien de bibliothèques détiennent des ISBN (une mesure de rareté). Nous avons été submergés par la réponse. Il y a eu tant de créativité. Un grand merci à tous ceux qui ont participé : votre énergie et votre enthousiasme sont contagieux ! En fin de compte, nous voulions répondre aux questions suivantes : <strong>quels livres existent dans le monde, combien en avons-nous déjà archivés, et sur quels livres devrions-nous nous concentrer ensuite ?</strong> C'est formidable de voir que tant de personnes se soucient de ces questions. Nous avons commencé par une visualisation de base nous-mêmes. En moins de 300 ko, cette image représente succinctement la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité : Troisième place 500 $ #1 : maxlion Dans cette <a %(annas_archive_note_2940)s>soumission</a>, nous avons vraiment aimé les différents types de vues, en particulier les vues de comparaison et d'éditeur. Troisième place 500 $ #2 : abetusk Bien que l'interface utilisateur ne soit pas la plus soignée, cette <a %(annas_archive_note_2917)s>soumission</a> coche beaucoup de cases. Nous avons particulièrement aimé sa fonctionnalité de comparaison. Troisième place 500 $ #3 : conundrumer0 Comme la première place, cette <a %(annas_archive_note_2975)s>soumission</a> nous a impressionnés par sa flexibilité. En fin de compte, c'est ce qui fait un excellent outil de visualisation : une flexibilité maximale pour les utilisateurs avancés, tout en gardant les choses simples pour les utilisateurs moyens. Troisième place 500 $ #4 : charelf La dernière <a %(annas_archive_note_2947)s>soumission</a> à recevoir une récompense est assez basique, mais possède des fonctionnalités uniques que nous avons vraiment appréciées. Nous avons aimé la façon dont ils montrent combien de datasets couvrent un ISBN particulier comme mesure de popularité/fiabilité. Nous avons également beaucoup aimé la simplicité mais l'efficacité de l'utilisation d'un curseur d'opacité pour les comparaisons. Gagnants de la récompense de visualisation ISBN de 10 000 $ En résumé : Nous avons reçu des soumissions incroyables pour la récompense de visualisation ISBN de 10 000 $. Contexte Comment les Archives d’Anna peuvent-elles accomplir leur mission de sauvegarder toutes les connaissances de l’humanité, sans savoir quels livres sont encore disponibles ? Nous avons besoin d’une liste de tâches. Une façon de cartographier cela est à travers les numéros ISBN, qui depuis les années 1970 ont été attribués à chaque livre publié (dans la plupart des pays). Il n’existe pas d’autorité centrale qui connaît toutes les attributions d’ISBN. Au lieu de cela, c’est un système distribué, où les pays obtiennent des plages de numéros, qui sont ensuite attribuées à de grands éditeurs, qui peuvent à leur tour sous-diviser les plages pour les petits éditeurs. Enfin, des numéros individuels sont attribués aux livres. Nous avons commencé à cartographier les ISBN <a %(blog)s>il y a deux ans</a> avec notre extraction de ISBNdb. Depuis, nous avons extrait de nombreuses autres sources de metadata, telles que <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, et plus encore. Une liste complète peut être trouvée sur les pages “Datasets” et “Torrents” des Archives d’Anna. Nous avons maintenant de loin la plus grande collection de metadata de livres entièrement ouverte et facilement téléchargeable (et donc d’ISBN) au monde. Nous avons <a %(blog)s>écrit abondamment</a> sur pourquoi nous nous soucions de la préservation, et pourquoi nous sommes actuellement dans une fenêtre critique. Nous devons maintenant identifier les livres rares, négligés et particulièrement à risque et les préserver. Avoir de bons metadata sur tous les livres du monde aide à cela. Prime de 10 000 $ Une forte considération sera accordée à l’utilisabilité et à l’esthétique. Afficher les metadata réels pour les ISBN individuels lors du zoom, tels que le titre et l’auteur. Meilleure courbe de remplissage d’espace. Par exemple, un zigzag, allant de 0 à 4 sur la première ligne puis revenant (en sens inverse) de 5 à 9 sur la deuxième ligne — appliqué de manière récursive. Différents schémas de couleurs personnalisables. Vues spéciales pour comparer les datasets. Moyens de déboguer les problèmes, tels que d'autres metadata qui ne concordent pas bien (par exemple, des titres très différents). Annoter des images avec des commentaires sur les ISBN ou les plages. Tout heuristique pour identifier les livres rares ou en danger. Toutes les idées créatives que vous pouvez proposer ! Code Le code pour générer ces images, ainsi que d'autres exemples, se trouve dans <a %(annas_archive)s>ce répertoire</a>. Nous avons conçu un format de données compact, avec lequel toutes les informations ISBN requises représentent environ 75 Mo (compressé). La description du format de données et le code pour le générer se trouvent <a %(annas_archive_l1244_1319)s>ici</a>. Pour la récompense, vous n'êtes pas obligé d'utiliser cela, mais c'est probablement le format le plus pratique pour commencer. Vous pouvez transformer notre metadata comme vous le souhaitez (bien que tout votre code doive être open source). Nous avons hâte de voir ce que vous allez proposer. Bonne chance ! Forkez ce dépôt, et éditez ce post de blog HTML (aucun autre backend en dehors de notre backend Flask n’est autorisé). Rendez l’image ci-dessus zoomable en douceur, afin que vous puissiez zoomer jusqu’aux ISBN individuels. Cliquer sur les ISBN devrait vous amener à une page de metadata ou à une recherche sur les Archives d’Anna. Vous devez toujours pouvoir passer entre tous les différents datasets. Les plages de pays et les plages d’éditeurs doivent être mises en évidence au survol. Vous pouvez utiliser par exemple <a %(github_xlcnd_isbnlib)s>data4info.py dans isbnlib</a> pour les informations sur les pays, et notre extraction “isbngrp” pour les éditeurs (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Cela doit bien fonctionner sur ordinateur de bureau et mobile. Il y a beaucoup à explorer ici, nous annonçons donc une prime pour améliorer la visualisation ci-dessus. Contrairement à la plupart de nos primes, celle-ci est limitée dans le temps. Vous devez <a %(annas_archive)s>soumettre</a> votre code open source avant le 31-01-2025 (23:59 UTC). La meilleure soumission recevra 6 000 $, la deuxième place 3 000 $, et la troisième place 1 000 $. Toutes les primes seront attribuées en utilisant Monero (XMR). Ci-dessous se trouvent les critères minimaux. Si aucune soumission ne répond aux critères, nous pourrions tout de même attribuer certaines primes, mais cela sera à notre discrétion. Pour des points bonus (ce ne sont que des idées — laissez libre cours à votre créativité) : Vous POUVEZ vous écarter complètement des critères minimaux et réaliser une visualisation complètement différente. Si elle est vraiment spectaculaire, elle peut être éligible pour la récompense, mais à notre discrétion. Faites vos soumissions en postant un commentaire sur <a %(annas_archive)s>ce problème</a> avec un lien vers votre dépôt forké, demande de fusion ou diff. - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Cette image fait 1000×800 pixels. Chaque pixel représente 2 500 ISBN. Si nous avons un fichier pour un ISBN, nous rendons ce pixel plus vert. Si nous savons qu'un ISBN a été émis, mais que nous n'avons pas de fichier correspondant, nous le rendons plus rouge. En moins de 300 ko, cette image représente succinctement la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité (quelques centaines de Go compressés en entier). Elle montre également : il reste beaucoup de travail pour sauvegarder les livres (nous n'avons que 16%). Visualisation de tous les ISBN — Prime de 10 000 $ d'ici le 31-01-2025 Cette image représente la plus grande « liste de livres » entièrement ouverte jamais assemblée dans l'histoire de l'humanité. Visualisation En plus de l’image d’ensemble, nous pouvons également examiner les datasets individuels que nous avons acquis. Utilisez le menu déroulant et les boutons pour passer de l’un à l’autre. Il y a beaucoup de motifs intéressants à voir dans ces images. Pourquoi y a-t-il une certaine régularité des lignes et des blocs, qui semble se produire à différentes échelles ? Quelles sont les zones vides ? Pourquoi certains datasets sont-ils si regroupés ? Nous laisserons ces questions comme un exercice pour le lecteur. - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusion Avec cette norme, nous pouvons effectuer des publications de manière plus incrémentale et ajouter plus facilement de nouvelles sources de données. Nous avons déjà quelques publications passionnantes en préparation ! Nous espérons également qu’il deviendra plus facile pour d’autres bibliothèques fantômes de mirrorer nos collections. Après tout, notre objectif est de préserver le savoir et la culture humains pour toujours, donc plus il y a de redondance, mieux c’est. Exemple Prenons notre récente publication de Z-Library comme exemple. Elle se compose de deux collections : “<span style="background: #fffaa3">zlib3_records</span>” et “<span style="background: #ffd6fe">zlib3_files</span>”. Cela nous permet de récupérer et de publier séparément les enregistrements de metadata des fichiers de livres réels. Ainsi, nous avons publié deux torrents avec des fichiers de metadata : Nous avons également publié une série de torrents avec des dossiers de données binaires, mais uniquement pour la collection “<span style="background: #ffd6fe">zlib3_files</span>”, 62 au total : En exécutant <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>, nous pouvons voir ce qu’il y a à l’intérieur : Dans ce cas, il s’agit de la metadata d’un livre tel que rapporté par Z-Library. Au niveau supérieur, nous avons seulement “aacid” et “metadata”, mais pas de “data_folder”, car il n’y a pas de données binaires correspondantes. L’AACID contient “22430000” comme identifiant principal, que nous pouvons voir est pris de “zlibrary_id”. Nous pouvons nous attendre à ce que d’autres AAC dans cette collection aient la même structure. Maintenant, exécutons <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> : Il s’agit d’une metadata AAC beaucoup plus petite, bien que l’essentiel de cet AAC soit situé ailleurs dans un fichier binaire ! Après tout, nous avons cette fois un “data_folder”, donc nous pouvons nous attendre à ce que les données binaires correspondantes se trouvent à <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Le “metadata” contient le “zlibrary_id”, donc nous pouvons facilement l’associer à l’AAC correspondant dans la collection “zlib_records”. Nous aurions pu l’associer de plusieurs manières différentes, par exemple via AACID — la norme ne le prescrit pas. Notez qu’il n’est pas non plus nécessaire que le champ “metadata” soit lui-même en JSON. Il pourrait s’agir d’une chaîne contenant du XML ou tout autre format de données. Vous pourriez même stocker des informations de metadata dans le blob binaire associé, par exemple si c’est beaucoup de données. Fichiers et metadata hétérogènes, dans un format aussi proche que possible de l'original. Les données binaires peuvent être servies directement par des serveurs web comme Nginx. Identifiants hétérogènes dans les bibliothèques sources, voire absence d'identifiants. Publications séparées de metadata par rapport aux données de fichiers, ou publications uniquement de metadata (par exemple, notre publication ISBNdb). Distribution via torrents, bien que d'autres méthodes de distribution soient possibles (par exemple, IPFS). Enregistrements immuables, car nous devons supposer que nos torrents vivront éternellement. Publications incrémentales / publications ajoutables. Lisible et inscriptible par machine, de manière pratique et rapide, surtout pour notre pile (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspection humaine relativement facile, bien que cela soit secondaire par rapport à la lisibilité par machine. Facile à semer nos collections avec un seedbox standard loué. Objectifs de conception Nous ne nous soucions pas que les fichiers soient faciles à naviguer manuellement sur le disque, ou recherchables sans prétraitement. Nous ne nous soucions pas d'être directement compatibles avec les logiciels de bibliothèque existants. Bien qu'il devrait être facile pour quiconque de semer notre collection en utilisant des torrents, nous ne nous attendons pas à ce que les fichiers soient utilisables sans connaissances techniques significatives et engagement. Notre cas d'utilisation principal est la distribution de fichiers et de metadata associés provenant de différentes collections existantes. Nos considérations les plus importantes sont : Quelques non-objectifs : Puisque Anna’s Archive est open source, nous voulons utiliser directement notre format. Lorsque nous actualisons notre index de recherche, nous n'accédons qu'aux chemins disponibles publiquement, afin que quiconque qui bifurque notre bibliothèque puisse démarrer rapidement. <strong>AAC.</strong> AAC (Conteneur des Archives d'Anna) est un élément unique composé de <strong>metadata</strong>, et éventuellement de <strong>données binaires</strong>, qui sont toutes deux immuables. Il possède un identifiant unique global, appelé <strong>AACID</strong>. <strong>AACID.</strong> Le format de l'AACID est le suivant : <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Par exemple, un AACID réel que nous avons publié est <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Plage d'AACID.</strong> Étant donné que les AACIDs contiennent des timestamps qui augmentent de manière monotone, nous pouvons les utiliser pour indiquer des plages au sein d'une collection particulière. Nous utilisons ce format : <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, où les timestamps sont inclusifs. Cela est cohérent avec la notation ISO 8601. Les plages sont continues et peuvent se chevaucher, mais en cas de chevauchement, elles doivent contenir des archives identiques à celles précédemment publiées dans cette collection (puisque les AACs sont immuables). Les archives manquantes ne sont pas autorisées. <code>{collection}</code> : le nom de la collection, qui peut contenir des lettres ASCII, des chiffres et des underscores (mais pas de double underscores). <code>{collection-specific ID}</code> : un identifiant spécifique à la collection, si applicable, par exemple l'ID de Z-Library. Peut être omis ou tronqué. Doit être omis ou tronqué si l'AACID dépasse autrement 150 caractères. <code>{ISO 8601 timestamp}</code> : une version courte de l'ISO 8601, toujours en UTC, par exemple <code>20220723T194746Z</code>. Ce nombre doit augmenter de manière monotone à chaque publication, bien que sa signification exacte puisse différer selon la collection. Nous suggérons d'utiliser le moment de la collecte ou de la génération de l'ID. <code>{shortuuid}</code> : un UUID mais compressé en ASCII, par exemple en utilisant base57. Nous utilisons actuellement la bibliothèque Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Dossier de données binaires.</strong> Un dossier contenant les données binaires d'une plage d'AACs, pour une collection particulière. Ceux-ci ont les propriétés suivantes : Le répertoire doit contenir des fichiers de données pour tous les AACs dans la plage spécifiée. Chaque fichier de données doit avoir son AACID comme nom de fichier (sans extensions). Le nom du répertoire doit être une plage d'AACID, précédée de <code style="color: green">annas_archive_data__</code>, et sans suffixe. Par exemple, l'une de nos publications réelles a un répertoire appelé<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Il est recommandé de rendre ces dossiers relativement gérables en taille, par exemple, ne dépassant pas 100 Go à 1 To chacun, bien que cette recommandation puisse évoluer avec le temps. <strong>Collection.</strong> Chaque AAC appartient à une collection, qui par définition est une liste d'AACs sémantiquement cohérente. Cela signifie que si vous apportez un changement significatif au format des metadata, vous devez créer une nouvelle collection. Le standard <strong>Fichier de metadata.</strong> Un fichier de metadata contient les metadata d'une plage d'AACs, pour une collection particulière. Ceux-ci ont les propriétés suivantes : <code>data_folder</code> est optionnel, et est le nom du dossier de données binaires qui contient les données binaires correspondantes. Le nom de fichier des données binaires correspondantes dans ce dossier est l'AACID de l'archive. Chaque objet JSON doit contenir les champs suivants au niveau supérieur : <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optionnel). Aucun autre champ n'est autorisé. Le nom de fichier doit être une plage d'AACID, précédée de <code style="color: red">annas_archive_meta__</code> et suivie de <code>.jsonl.zstd</code>. Par exemple, l'une de nos publications s'appelle<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Comme indiqué par l'extension de fichier, le type de fichier est <a %(jsonlines)s>JSON Lines</a> compressé avec <a %(zstd)s>Zstandard</a>. <code>metadata</code> est un metadata arbitraire, selon la sémantique de la collection. Il doit être sémantiquement cohérent au sein de la collection. Le préfixe <code style="color: red">annas_archive_meta__</code> peut être adapté au nom de votre institution, par exemple <code style="color: red">my_institute_meta__</code>. <strong>Collections de « archives » et de « fichiers ».</strong> Par convention, il est souvent pratique de publier les « archives » et les « fichiers » comme des collections différentes, afin qu'ils puissent être publiés à des moments différents, par exemple en fonction des taux de collecte. Une « archive » est une collection contenant uniquement des metadata, avec des informations comme les titres de livres, les auteurs, les ISBN, etc., tandis que les « fichiers » sont les collections qui contiennent les fichiers eux-mêmes (pdf, epub). Finalement, nous avons opté pour une norme relativement simple. Elle est assez souple, non normative, et en cours d'élaboration. <strong>Torrents.</strong> Les fichiers de metadata et les dossiers de données binaires peuvent être regroupés dans des torrents, avec un torrent par fichier de metadata, ou un torrent par dossier de données binaires. Les torrents doivent avoir le nom de fichier/répertoire original plus un suffixe <code>.torrent</code> comme nom de fichier. <a %(wikipedia_annas_archive)s>Les Archives d’Anna</a> sont devenues de loin la plus grande bibliothèque fantôme du monde, et la seule bibliothèque fantôme de cette envergure qui soit entièrement open-source et open-data. Ci-dessous se trouve un tableau de notre page Datasets (légèrement modifié) : Nous avons accompli cela de trois manières : Refléter les bibliothèques fantômes open-data existantes (comme Sci-Hub et Library Genesis). Aider les bibliothèques fantômes qui souhaitent être plus ouvertes, mais qui n'avaient pas le temps ou les ressources pour le faire (comme la collection de bandes dessinées de Libgen). Extraire les bibliothèques qui ne souhaitent pas partager en masse (comme Z-Library). Pour (2) et (3), nous gérons désormais nous-mêmes une collection considérable de torrents (des centaines de To). Jusqu'à présent, nous avons abordé ces collections comme des cas uniques, ce qui signifie une infrastructure et une organisation des données sur mesure pour chaque collection. Cela ajoute une surcharge significative à chaque publication et rend particulièrement difficile la réalisation de publications plus incrémentales. C'est pourquoi nous avons décidé de standardiser nos publications. Ceci est un article technique dans lequel nous introduisons notre standard : <strong>Les Conteneurs d'Anna’s Archive</strong>. Conteneurs des Archives d’Anna (CAA) : standardiser les publications de la plus grande bibliothèque fantôme du monde Les Archives d’Anna sont devenues la plus grande bibliothèque fantôme du monde, nous obligeant à standardiser nos publications. Plus de 300 Go de couvertures de livres publiées Enfin, nous sommes heureux d'annoncer une petite publication. En collaboration avec les personnes qui gèrent le fork Libgen.rs, nous partageons toutes leurs couvertures de livres via des torrents et IPFS. Cela répartira la charge de visualisation des couvertures entre plus de machines et les préservera mieux. Dans de nombreux cas (mais pas tous), les couvertures de livres sont incluses dans les fichiers eux-mêmes, donc c'est une sorte de "données dérivées". Mais les avoir dans IPFS est toujours très utile pour le fonctionnement quotidien à la fois de l'Archive d'Anna et des différents forks de Library Genesis. Comme d'habitude, vous pouvez trouver cette publication sur le Pirate Library Mirror (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>). Nous ne mettrons pas de lien ici, mais vous pouvez facilement le trouver. Nous espérons pouvoir ralentir un peu notre rythme, maintenant que nous avons une alternative décente à Z-Library. Cette charge de travail n'est pas particulièrement durable. Si vous êtes intéressé à aider avec la programmation, les opérations de serveur ou le travail de préservation, n'hésitez pas à nous contacter. Il y a encore beaucoup de <a %(annas_archive)s>travail à faire</a>. Merci pour votre intérêt et votre soutien. Passer à ElasticSearch Certaines requêtes prenaient énormément de temps, au point de monopoliser toutes les connexions ouvertes. Par défaut, MySQL a une longueur minimale de mot, ou votre index peut devenir vraiment grand. Les gens ont signalé ne pas pouvoir rechercher "Ben Hur". La recherche n'était rapide que lorsqu'elle était entièrement chargée en mémoire, ce qui nous obligeait à obtenir une machine plus coûteuse pour l'exécuter, plus quelques commandes pour précharger l'index au démarrage. Nous n'aurions pas pu l'étendre facilement pour créer de nouvelles fonctionnalités, comme une meilleure <a %(wikipedia_cjk_characters)s>tokenisation pour les langues sans espaces</a>, le filtrage/facettage, le tri, les suggestions "vouliez-vous dire", l'autocomplétion, etc. Un de nos <a %(annas_archive)s>tickets</a> était un fourre-tout de problèmes avec notre système de recherche. Nous utilisions la recherche en texte intégral de MySQL, puisque nous avions toutes nos données dans MySQL de toute façon. Mais cela avait ses limites : Après avoir discuté avec plusieurs experts, nous avons opté pour ElasticSearch. Ce n'est pas parfait (leurs suggestions "vouliez-vous dire" par défaut et les fonctionnalités d'autocomplétion sont médiocres), mais dans l'ensemble, c'est bien mieux que MySQL pour la recherche. Nous ne sommes toujours pas <a %(youtube)s>très enthousiastes</a> à l'idée de l'utiliser pour des données critiques (bien qu'ils aient fait beaucoup de <a %(elastic_co)s>progrès</a>), mais dans l'ensemble, nous sommes assez satisfaits du changement. Pour l'instant, nous avons mis en place une recherche beaucoup plus rapide, un meilleur support linguistique, un meilleur tri par pertinence, différentes options de tri, et le filtrage par langue/type de livre/type de fichier. Si vous êtes curieux de savoir comment cela fonctionne, <a %(annas_archive_l140)s>jetez</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>coup d'œil</a>. C'est assez accessible, bien qu'il pourrait y avoir plus de commentaires… L’Archive d’Anna est entièrement open source Nous croyons que l’information doit être libre, et notre propre code ne fait pas exception. Nous avons publié tout notre code sur notre instance Gitlab hébergée en privé : <a %(annas_archive)s>Logiciel d’Anna</a>. Nous utilisons également le gestionnaire de problèmes pour organiser notre travail. Si vous souhaitez vous engager dans notre développement, c’est un excellent point de départ. Pour vous donner un aperçu des choses sur lesquelles nous travaillons, prenez notre récent travail sur les améliorations de performance côté client. Comme nous n’avons pas encore implémenté la pagination, nous retournions souvent des pages de recherche très longues, avec 100 à 200 résultats. Nous ne voulions pas couper les résultats de recherche trop tôt, mais cela signifiait que cela ralentissait certains appareils. Pour cela, nous avons mis en place une petite astuce : nous avons enveloppé la plupart des résultats de recherche dans des commentaires HTML (<code><!-- --></code>), puis écrit un petit Javascript qui détecterait quand un résultat devrait devenir visible, moment auquel nous déballerions le commentaire : La "virtualisation" du DOM implémentée en 23 lignes, pas besoin de bibliothèques sophistiquées ! C'est le genre de code pragmatique rapide que l'on obtient lorsque l'on dispose de peu de temps et de problèmes réels à résoudre. Il a été rapporté que notre recherche fonctionne désormais bien sur les appareils lents ! Un autre grand effort a été d'automatiser la construction de la base de données. Lors de notre lancement, nous avons simplement rassemblé différentes sources de manière désordonnée. Maintenant, nous voulons les garder à jour, alors nous avons écrit un ensemble de scripts pour télécharger de nouveaux metadata à partir des deux forks de Library Genesis, et les intégrer. L'objectif est non seulement de rendre cela utile pour notre archive, mais aussi de faciliter les choses pour quiconque souhaite explorer les metadata de la bibliothèque fantôme. L'objectif serait un notebook Jupyter contenant toutes sortes de metadata intéressants, afin que nous puissions faire plus de recherches comme déterminer quel <a %(blog)s>pourcentage d'ISBNs est préservé à jamais</a>. Enfin, nous avons remanié notre système de dons. Vous pouvez désormais utiliser une carte de crédit pour déposer directement de l'argent dans nos portefeuilles crypto, sans vraiment avoir besoin de connaître quoi que ce soit sur les cryptomonnaies. Nous continuerons à surveiller l'efficacité de ce système en pratique, mais c'est un grand pas en avant. Avec la fermeture de Z-Library et l’arrestation (présumée) de ses fondateurs, nous avons travaillé d’arrache-pied pour fournir une bonne alternative avec l’Archive d’Anna (nous ne la lierons pas ici, mais vous pouvez la rechercher sur Google). Voici quelques-unes des choses que nous avons récemment accomplies. Mise à jour d’Anna : archive entièrement open source, ElasticSearch, plus de 300 Go de couvertures de livres Nous avons travaillé d’arrache-pied pour fournir une bonne alternative avec l’Archive d’Anna. Voici quelques-unes des choses que nous avons récemment accomplies. Analyse Les doublons sémantiques (différents scans du même livre) peuvent théoriquement être filtrés, mais c'est délicat. En regardant manuellement les bandes dessinées, nous avons trouvé trop de faux positifs. Il y a quelques doublons purement par MD5, ce qui est relativement gaspilleur, mais les filtrer ne nous donnerait qu'environ 1% in d'économies. À cette échelle, cela représente toujours environ 1 To, mais aussi, à cette échelle, 1 To n'a pas vraiment d'importance. Nous préférons ne pas risquer de détruire accidentellement des données dans ce processus. Nous avons trouvé un tas de données non liées aux livres, comme des films basés sur des bandes dessinées. Cela semble également gaspilleur, car ils sont déjà largement disponibles par d'autres moyens. Cependant, nous avons réalisé que nous ne pouvions pas simplement filtrer les fichiers de films, car il y a aussi des <em>bandes dessinées interactives</em> qui ont été publiées sur ordinateur, que quelqu'un a enregistrées et sauvegardées sous forme de films. En fin de compte, tout ce que nous pourrions supprimer de la collection ne représenterait qu'un faible pourcentage. Puis nous nous sommes rappelés que nous sommes des accumulateurs de données, et que les personnes qui vont faire un site miroir sont aussi des accumulateurs de données, alors, « QU'EST-CE QUE VOUS VOULEZ DIRE PAR SUPPRIMER ?! » :) Lorsque vous recevez 95 To déversés dans votre cluster de stockage, vous essayez de comprendre ce qu'il y a dedans… Nous avons fait quelques analyses pour voir si nous pouvions réduire un peu la taille, par exemple en supprimant les doublons. Voici quelques-unes de nos découvertes : Nous vous présentons donc la collection complète et non modifiée. C'est beaucoup de données, mais nous espérons que suffisamment de personnes se soucieront de la partager quand même. Collaboration Étant donné sa taille, cette collection figurait depuis longtemps sur notre liste de souhaits, donc après notre succès avec la sauvegarde de Z-Library, nous avons mis le cap sur cette collection. Au début, nous l'avons extraite directement, ce qui a été un véritable défi, car leur serveur n'était pas en très bon état. Nous avons obtenu environ 15 To de cette manière, mais c'était lent. Heureusement, nous avons réussi à entrer en contact avec l'opérateur de la bibliothèque, qui a accepté de nous envoyer toutes les données directement, ce qui a été beaucoup plus rapide. Il a tout de même fallu plus de six mois pour transférer et traiter toutes les données, et nous avons failli tout perdre à cause d'une corruption de disque, ce qui aurait signifié tout recommencer. Cette expérience nous a convaincus qu'il est important de diffuser ces données le plus rapidement possible, afin qu'elles puissent être dupliquées largement. Nous ne sommes qu'à un ou deux incidents malheureux de perdre cette collection à jamais ! La collection Aller vite signifie que la collection est un peu désorganisée… Jetons un coup d'œil. Imaginez que nous ayons un système de fichiers (que nous divisons en réalité en torrents) : Le premier répertoire, <code>/repository</code>, est la partie la plus structurée de cela. Ce répertoire contient ce qu'on appelle des "mille répertoires" : des répertoires contenant chacun un millier de fichiers, numérotés de manière incrémentale dans la base de données. Le répertoire <code>0</code> contient des fichiers avec comic_id 0–999, et ainsi de suite. C'est le même schéma que Library Genesis utilise pour ses collections de fiction et de non-fiction. L'idée est que chaque "mille répertoire" soit automatiquement transformé en torrent dès qu'il est rempli. Cependant, l'opérateur de Libgen.li n'a jamais créé de torrents pour cette collection, et donc les mille répertoires sont probablement devenus gênants, et ont laissé place à des "répertoires non triés". Ce sont <code>/comics0</code> à <code>/comics4</code>. Ils contiennent tous des structures de répertoires uniques, qui avaient probablement du sens pour collecter les fichiers, mais qui n'ont plus beaucoup de sens pour nous maintenant. Heureusement, le metadata fait toujours référence directement à tous ces fichiers, donc leur organisation de stockage sur disque n'a en fait pas d'importance ! Le metadata est disponible sous la forme d'une base de données MySQL. Cela peut être téléchargé directement depuis le site Web de Libgen.li, mais nous le rendrons également disponible dans un torrent, aux côtés de notre propre table avec tous les hachages MD5. <q>Dr. Barbara Gordon essaie de se perdre dans le monde banal de la bibliothèque…</q> Forks de Libgen Tout d'abord, un peu de contexte. Vous connaissez peut-être Library Genesis pour leur collection épique de livres. Moins de gens savent que les bénévoles de Library Genesis ont créé d'autres projets, tels qu'une vaste collection de magazines et de documents standards, une sauvegarde complète de Sci-Hub (en collaboration avec la fondatrice de Sci-Hub, Alexandra Elbakyan), et en effet, une immense collection de bandes dessinées. À un moment donné, différents opérateurs de miroirs de Library Genesis ont pris des chemins séparés, ce qui a donné lieu à la situation actuelle avec plusieurs "forks" différents, portant tous encore le nom de Library Genesis. Le fork Libgen.li possède de manière unique cette collection de bandes dessinées, ainsi qu'une collection de magazines considérable (sur laquelle nous travaillons également). Collecte de fonds Nous publions ces données en gros morceaux. Le premier torrent est de <code>/comics0</code>, que nous avons mis dans un énorme fichier .tar de 12 To. C'est mieux pour votre disque dur et votre logiciel de torrent qu'une multitude de petits fichiers. Dans le cadre de cette publication, nous organisons une collecte de fonds. Nous cherchons à lever 20 000 $ pour couvrir les coûts opérationnels et de sous-traitance pour cette collection, ainsi que pour permettre des projets en cours et futurs. Nous avons quelques <em>projets énormes</em> en préparation. <em>Qui soutiens-je avec mon don ?</em> En bref : nous sauvegardons tout le savoir et la culture de l'humanité, et les rendons facilement accessibles. Tout notre code et nos données sont open source, nous sommes un projet entièrement géré par des bénévoles, et nous avons déjà sauvegardé 125 To de livres (en plus des torrents existants de Libgen et Scihub). En fin de compte, nous construisons un volant d'inertie qui permet et incite les gens à trouver, numériser et sauvegarder tous les livres du monde. Nous écrirons sur notre plan directeur dans un futur article. :) Si vous faites un don pour une adhésion de 12 mois « Amazing Archivist » (780 $), vous pouvez <strong>« adopter un torrent »</strong>, ce qui signifie que nous mettrons votre nom d'utilisateur ou votre message dans le nom de fichier de l'un des torrents ! Vous pouvez faire un don en vous rendant sur <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> et en cliquant sur le bouton « Faites un don ». Nous recherchons également plus de bénévoles : ingénieurs logiciels, chercheurs en sécurité, experts marchands anonymes et traducteurs. Vous pouvez également nous soutenir en fournissant des services d'hébergement. Et bien sûr, veuillez partager nos torrents ! Merci à tous ceux qui nous ont déjà soutenus si généreusement ! Vous faites vraiment une différence. Voici les torrents publiés jusqu'à présent (nous traitons encore le reste) : Tous les torrents peuvent être trouvés sur <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> sous « Datasets » (nous ne faisons pas de lien direct là-bas, pour que les liens vers ce blog ne soient pas supprimés de Reddit, Twitter, etc.). De là, suivez le lien vers le site Tor. <a %(news_ycombinator)s>Discuter sur Hacker News</a> Quelles sont les prochaines étapes ? Un tas de torrents sont excellents pour la préservation à long terme, mais pas tellement pour l'accès quotidien. Nous travaillerons avec des partenaires d'hébergement pour mettre toutes ces données en ligne (puisque l'Archive d'Anna n'héberge rien directement). Bien sûr, vous pourrez trouver ces liens de téléchargement sur l'Archive d'Anna. Nous invitons également tout le monde à faire des choses avec ces données ! Aidez-nous à mieux les analyser, les dédupliquer, les mettre sur IPFS, les remixer, entraîner vos modèles d'IA avec elles, et ainsi de suite. Elles sont toutes à vous, et nous avons hâte de voir ce que vous en ferez. Enfin, comme dit précédemment, nous avons encore quelques sorties massives à venir (si <em>quelqu'un</em> pouvait <em>accidentellement</em> nous envoyer un dump d'une <em>certaine</em> base de données ACS4, vous savez où nous trouver...), ainsi que la construction du volant d'inertie pour sauvegarder tous les livres du monde. Alors restez à l'écoute, nous ne faisons que commencer. - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) La plus grande bibliothèque fantôme de bandes dessinées est probablement celle d'un fork particulier de Library Genesis : Libgen.li. L'administrateur unique de ce site a réussi à rassembler une collection de bandes dessinées incroyable de plus de 2 millions de fichiers, totalisant plus de 95 To. Cependant, contrairement à d'autres collections de Library Genesis, celle-ci n'était pas disponible en masse via des torrents. Vous ne pouviez accéder à ces bandes dessinées qu'individuellement via son serveur personnel lent — un point de défaillance unique. Jusqu'à aujourd'hui ! Dans cet article, nous vous en dirons plus sur cette collection, ainsi que sur notre collecte de fonds pour soutenir davantage ce travail. L'Archive d'Anna a sauvegardé la plus grande bibliothèque fantôme de bandes dessinées au monde (95 To) — vous pouvez aider à la partager La plus grande bibliothèque fantôme de bandes dessinées au monde avait un point de défaillance unique... jusqu'à aujourd'hui. Avertissement : cet article de blog a été déprécié. Nous avons décidé que l'IPFS n'est pas encore prêt pour le grand public. Nous continuerons à lier des fichiers sur IPFS depuis l'Archive d'Anna lorsque cela est possible, mais nous ne l'hébergerons plus nous-mêmes, ni ne recommandons aux autres de créer un site miroir en utilisant IPFS. Veuillez consulter notre page Torrents si vous souhaitez aider à préserver notre collection. Mettre 5 998 794 livres sur IPFS Une multiplication de copies Revenons à notre question initiale : comment pouvons-nous prétendre préserver nos collections à perpétuité ? Le principal problème ici est que notre collection a <a %(torrents_stats)s>augmenté</a> à un rythme rapide, en extrayant et en open-sourçant certaines collections massives (en plus du travail incroyable déjà réalisé par d’autres bibliothèques fantômes de données ouvertes comme Sci-Hub et Library Genesis). Cette croissance des données rend plus difficile la duplication des collections à travers le monde. Le stockage de données est coûteux ! Mais nous sommes optimistes, surtout en observant les trois tendances suivantes. La <a %(annas_archive_stats)s>taille totale</a> de nos collections, au cours des derniers mois, ventilée par nombre de seeders de torrent. Tendances des prix des HDD de différentes sources (cliquez pour voir l’étude). <a %(critical_window_chinese)s>Version chinoise 中文版</a>, discutez sur <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Nous avons cueilli les fruits à portée de main Celle-ci découle directement de nos priorités discutées ci-dessus. Nous préférons travailler d’abord sur la libération de grandes collections. Maintenant que nous avons sécurisé certaines des plus grandes collections au monde, nous nous attendons à ce que notre croissance soit beaucoup plus lente. Il reste encore une longue traîne de petites collections, et de nouveaux livres sont numérisés ou publiés chaque jour, mais le rythme sera probablement beaucoup plus lent. Nous pourrions encore doubler ou même tripler de taille, mais sur une période plus longue. Améliorations de l'OCR. Priorités Code logiciel de science et d'ingénierie Versions fictives ou de divertissement de tout ce qui précède Données géographiques (par exemple, cartes, études géologiques) Données internes d'entreprises ou de gouvernements (fuites) Données de mesure comme les mesures scientifiques, les données économiques, les rapports d'entreprise Enregistrements de metadata en général (de non-fiction et fiction ; d'autres médias, art, personnes, etc. ; y compris les critiques) Livres non-fictionnels Magazines non-fictionnels, journaux, manuels Transcriptions non-fictionnelles de conférences, documentaires, podcasts Données organiques comme les séquences d'ADN, les graines de plantes ou les échantillons microbiens Articles académiques, revues, rapports Sites web de science et d'ingénierie, discussions en ligne Transcriptions de procédures légales ou judiciaires Uniquement à risque de destruction (par exemple, par la guerre, les réductions de financement, les poursuites judiciaires ou la persécution politique) Rares Uniquement sous-exposées Pourquoi nous soucions-nous autant des articles et des livres ? Mettons de côté notre croyance fondamentale en la préservation en général — nous pourrions écrire un autre article à ce sujet. Alors pourquoi les articles et les livres spécifiquement ? La réponse est simple : <strong>densité d'information</strong>. Par mégaoctet de stockage, le texte écrit stocke le plus d'informations parmi tous les médias. Bien que nous nous préoccupions à la fois de la connaissance et de la culture, nous nous soucions davantage de la première. Dans l'ensemble, nous trouvons une hiérarchie de densité d'information et d'importance de la préservation qui ressemble à peu près à ceci : Le classement dans cette liste est quelque peu arbitraire — plusieurs éléments sont à égalité ou font l'objet de désaccords au sein de notre équipe — et nous oublions probablement certaines catégories importantes. Mais c'est à peu près ainsi que nous priorisons. Certains de ces éléments sont trop différents des autres pour que nous nous en préoccupions (ou sont déjà pris en charge par d'autres institutions), comme les données organiques ou géographiques. Mais la plupart des éléments de cette liste sont en réalité importants pour nous. Un autre grand facteur dans notre priorisation est le risque auquel une œuvre est exposée. Nous préférons nous concentrer sur les œuvres qui sont : Enfin, nous nous soucions de l'échelle. Nous avons un temps et des ressources limités, donc nous préférons passer un mois à sauver 10 000 livres plutôt que 1 000 livres — s'ils sont à peu près également précieux et à risque. <em><q>Ce qui est perdu ne peut être récupéré ; mais sauvons ce qui reste : non pas par des coffres et des serrures qui les protègent du regard et de l’usage du public, en les confiant à l’oubli du temps, mais par une telle multiplication de copies, qui les mettra hors de portée des accidents.</q></em><br>— Thomas Jefferson, 1791 Bibliothèque fantôme Le code peut être open source sur Github, mais Github dans son ensemble ne peut pas être facilement dupliqué et donc préservé (bien que dans ce cas particulier, il existe suffisamment de copies distribuées de la plupart des dépôts de code) Les enregistrements de metadata peuvent être librement consultés sur le site Worldcat, mais pas téléchargés en masse (jusqu’à ce que nous les <a %(worldcat_scrape)s>ayons extraits</a>) Reddit est gratuit à utiliser, mais a récemment mis en place des mesures anti-extraction strictes, à la suite de la formation de LLM avides de données (plus à ce sujet plus tard) Il existe de nombreuses organisations qui ont des missions similaires et des priorités similaires. En effet, il y a des bibliothèques, archives, laboratoires, musées et autres institutions chargées de la préservation de ce type. Beaucoup d'entre elles sont bien financées, par des gouvernements, des particuliers ou des entreprises. Mais elles ont un énorme angle mort : le système juridique. C'est là que réside le rôle unique des bibliothèques fantômes, et la raison pour laquelle l'Archive d'Anna existe. Nous pouvons faire des choses que d'autres institutions ne sont pas autorisées à faire. Maintenant, ce n'est pas (souvent) que nous pouvons archiver des matériaux qu'il est illégal de conserver ailleurs. Non, il est légal dans de nombreux endroits de construire une archive avec n'importe quels livres, articles, magazines, et ainsi de suite. Mais ce qui manque souvent aux archives légales, c’est <strong>la redondance et la longévité</strong>. Il existe des livres dont une seule copie est conservée dans une bibliothèque physique quelque part. Il existe des enregistrements de metadata gardés par une seule entreprise. Il existe des journaux uniquement préservés sur microfilm dans une seule archive. Les bibliothèques peuvent subir des réductions de financement, les entreprises peuvent faire faillite, les archives peuvent être bombardées et brûlées jusqu’au sol. Ce n’est pas hypothétique — cela arrive tout le temps. Ce que nous pouvons faire de manière unique sur l’Archive d’Anna, c’est stocker de nombreuses copies d’œuvres, à grande échelle. Nous pouvons collecter des articles, des livres, des magazines, et plus encore, et les distribuer en masse. Nous le faisons actuellement via des torrents, mais les technologies exactes importent peu et évolueront avec le temps. L’important est de distribuer de nombreuses copies à travers le monde. Cette citation d’il y a plus de 200 ans reste toujours vraie : Une petite note sur le domaine public. Puisque l’Archive d’Anna se concentre de manière unique sur des activités illégales dans de nombreux endroits à travers le monde, nous ne nous soucions pas des collections largement disponibles, telles que les livres du domaine public. Les entités légales s’en occupent souvent déjà bien. Cependant, il y a des considérations qui nous amènent parfois à travailler sur des collections publiquement disponibles : - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Les coûts de stockage continuent de baisser de manière exponentielle 3. Améliorations de la densité d'information Nous stockons actuellement les livres dans les formats bruts qui nous sont fournis. Certes, ils sont compressés, mais souvent, ce sont encore de grandes numérisations ou photographies de pages. Jusqu'à présent, les seules options pour réduire la taille totale de notre collection étaient une compression plus agressive ou la déduplication. Cependant, pour obtenir des économies significatives, les deux sont trop destructeurs à notre goût. Une forte compression des photos peut rendre le texte à peine lisible. Et la déduplication nécessite une grande confiance dans le fait que les livres soient exactement les mêmes, ce qui est souvent trop imprécis, surtout si le contenu est le même mais que les numérisations sont faites à des moments différents. Il y a toujours eu une troisième option, mais sa qualité était si abominable que nous ne l'avons jamais envisagée : <strong>OCR, ou Reconnaissance Optique de Caractères</strong>. C'est le processus de conversion des photos en texte brut, en utilisant l'IA pour détecter les caractères dans les photos. Les outils pour cela existent depuis longtemps et sont assez corrects, mais « assez correct » n'est pas suffisant pour des fins de préservation. Cependant, les récents modèles d'apprentissage profond multimodal ont fait des progrès extrêmement rapides, bien que toujours à des coûts élevés. Nous nous attendons à ce que la précision et les coûts s'améliorent considérablement dans les années à venir, au point qu'il deviendra réaliste de l'appliquer à l'ensemble de notre bibliothèque. Lorsque cela se produira, nous conserverons probablement toujours les fichiers originaux, mais en plus, nous pourrions avoir une version beaucoup plus petite de notre bibliothèque que la plupart des gens voudront reproduire. Le point fort est que le texte brut lui-même se compresse encore mieux et est beaucoup plus facile à dédupliquer, nous offrant encore plus d'économies. Dans l'ensemble, il n'est pas irréaliste de s'attendre à une réduction d'au moins 5 à 10 fois de la taille totale des fichiers, voire plus. Même avec une réduction conservatrice de 5 fois, nous envisagerions <strong>1 000 à 3 000 $ dans 10 ans même si notre bibliothèque triple de taille</strong>. Au moment de la rédaction, les <a %(diskprices)s>prix des disques</a> par To sont d’environ 12 $ pour les disques neufs, 8 $ pour les disques d’occasion et 4 $ pour les bandes. Si nous sommes prudents et ne regardons que les disques neufs, cela signifie que stocker un pétaoctet coûte environ 12 000 $. Si nous supposons que notre bibliothèque triplera de 900 To à 2,7 Po, cela signifierait 32 400 $ pour dupliquer notre bibliothèque entière. En ajoutant l’électricité, le coût d’autres matériels, etc., arrondissons à 40 000 $. Ou avec des bandes, plutôt entre 15 000 $ et 20 000 $. D’un côté <strong>15 000 $–40 000 $ pour la somme de toutes les connaissances humaines est une aubaine</strong>. De l’autre, c’est un peu raide d’attendre des tonnes de copies complètes, surtout si nous voulons aussi que ces personnes continuent de partager leurs torrents pour le bénéfice des autres. C’est aujourd’hui. Mais le progrès avance : Les coûts des disques durs par To ont été réduits d’environ un tiers au cours des 10 dernières années, et continueront probablement de baisser à un rythme similaire. Les bandes semblent suivre une trajectoire similaire. Les prix des SSD baissent encore plus rapidement, et pourraient surpasser les prix des HDD d’ici la fin de la décennie. Si cela se confirme, alors dans 10 ans, nous pourrions envisager seulement 5 000 $–13 000 $ pour dupliquer notre collection entière (1/3), ou même moins si nous croissons moins en taille. Bien que cela représente encore beaucoup d’argent, cela sera accessible pour de nombreuses personnes. Et cela pourrait être encore mieux grâce au point suivant… À l'Archive d'Anna, on nous demande souvent comment nous pouvons prétendre préserver nos collections à perpétuité, alors que la taille totale approche déjà 1 Pétaoctet (1000 To), et continue de croître. Dans cet article, nous examinerons notre philosophie, et verrons pourquoi la prochaine décennie est cruciale pour notre mission de préservation des connaissances et de la culture de l'humanité. Fenêtre critique Si ces prévisions sont exactes, nous <strong>n'avons qu'à attendre quelques années</strong> avant que notre collection entière ne soit largement reproduite. Ainsi, pour reprendre les mots de Thomas Jefferson, « placée hors de portée des accidents ». Malheureusement, l'avènement des LLM, et leur formation gourmande en données, a mis de nombreux détenteurs de droits d'auteur sur la défensive. Encore plus qu'ils ne l'étaient déjà. De nombreux sites Web rendent plus difficile le scraping et l'archivage, les poursuites judiciaires fusent, et pendant ce temps, les bibliothèques et archives physiques continuent d'être négligées. Nous ne pouvons qu'attendre que ces tendances continuent de s'aggraver, et que de nombreuses œuvres soient perdues bien avant d'entrer dans le domaine public. <strong>Nous sommes à la veille d'une révolution dans la préservation, mais <q>ce qui est perdu ne peut être récupéré.</q></strong> Nous avons une fenêtre critique d'environ 5 à 10 ans pendant laquelle il est encore assez coûteux d'exploiter une bibliothèque fantôme et de créer de nombreux sites miroirs à travers le monde, et pendant laquelle l'accès n'a pas encore été complètement fermé. Si nous pouvons franchir cette fenêtre, alors nous aurons effectivement préservé le savoir et la culture de l'humanité à perpétuité. Nous ne devrions pas laisser ce temps se perdre. Nous ne devrions pas laisser cette fenêtre critique se refermer sur nous. Allons-y. La fenêtre critique des bibliothèques fantômes Comment pouvons-nous prétendre préserver nos collections à perpétuité, alors qu'elles approchent déjà de 1 Po ? Collection Quelques informations supplémentaires sur la collection. <a %(duxiu)s>Duxiu</a> est une base de données massive de livres numérisés, créée par le <a %(chaoxing)s>SuperStar Digital Library Group</a>. La plupart sont des livres académiques, numérisés pour les rendre disponibles numériquement aux universités et bibliothèques. Pour notre public anglophone, <a %(library_princeton)s>Princeton</a> et l'<a %(guides_lib_uw)s>Université de Washington</a> offrent de bons aperçus. Il y a aussi un excellent article donnant plus de contexte : <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (à consulter dans l'Archive d'Anna). Les livres de Duxiu ont longtemps été piratés sur l'internet chinois. Ils sont généralement vendus pour moins d'un dollar par des revendeurs. Ils sont typiquement distribués en utilisant l'équivalent chinois de Google Drive, qui a souvent été piraté pour permettre plus d'espace de stockage. Quelques détails techniques peuvent être trouvés <a %(github_duty_machine)s>ici</a> et <a %(github_821_github_io)s>ici</a>. Bien que les livres aient été distribués de manière semi-publique, il est assez difficile de les obtenir en masse. Nous avions cela en haut de notre liste de tâches, et avons alloué plusieurs mois de travail à temps plein pour cela. Cependant, récemment, un bénévole incroyable, étonnant et talentueux nous a contactés, nous disant qu'il avait déjà fait tout ce travail — à grands frais. Ils ont partagé l'intégralité de la collection avec nous, sans rien attendre en retour, sauf la garantie d'une préservation à long terme. Vraiment remarquable. Ils ont accepté de demander de l'aide de cette manière pour faire l'OCR de la collection. La collection compte 7 543 702 fichiers. C'est plus que la non-fiction de Library Genesis (environ 5,3 millions). La taille totale des fichiers est d'environ 359 To (326 TiB) dans sa forme actuelle. Nous sommes ouverts à d'autres propositions et idées. Contactez-nous simplement. Consultez l'Archive d'Anna pour plus d'informations sur nos collections, nos efforts de préservation, et comment vous pouvez aider. Merci ! Pages d'exemple Pour nous prouver que vous avez un bon pipeline, voici quelques pages d'exemple pour commencer, tirées d'un livre sur les supraconducteurs. Votre pipeline doit gérer correctement les mathématiques, les tableaux, les graphiques, les notes de bas de page, etc. Envoyez vos pages traitées à notre adresse e-mail. Si elles sont satisfaisantes, nous vous enverrons plus en privé, et nous nous attendons à ce que vous puissiez rapidement exécuter votre pipeline sur celles-ci également. Une fois que nous serons satisfaits, nous pourrons conclure un accord. - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Version chinoise 中文版</a>, <a %(news_ycombinator)s>Discuter sur Hacker News</a> Ceci est un court article de blog. Nous recherchons une entreprise ou une institution pour nous aider avec l'OCR et l'extraction de texte pour une collection massive que nous avons acquise, en échange d'un accès exclusif anticipé. Après la période d'embargo, nous publierons bien sûr l'intégralité de la collection. Un texte académique de haute qualité est extrêmement utile pour l'entraînement des LLM. Bien que notre collection soit en chinois, cela devrait être utile même pour l'entraînement des LLM en anglais : les modèles semblent encoder des concepts et des connaissances indépendamment de la langue source. Pour cela, le texte doit être extrait des scans. Qu'est-ce que l'Archive d'Anna en retire ? Une recherche en texte intégral des livres pour ses utilisateurs. Parce que nos objectifs s'alignent avec ceux des développeurs de LLM, nous recherchons un collaborateur. Nous sommes prêts à vous donner <strong>un accès anticipé exclusif à cette collection en masse pendant 1 an</strong>, si vous pouvez effectuer une OCR et une extraction de texte appropriées. Si vous êtes prêt à partager l'intégralité du code de votre pipeline avec nous, nous serions prêts à prolonger l'embargo sur la collection. Accès exclusif pour les entreprises LLM à la plus grande collection de livres non-fictionnels chinois au monde <em><strong>TL;DR :</strong> Les Archives d'Anna ont acquis une collection unique de 7,5 millions / 350 To de livres non-fictionnels chinois — plus grande que Library Genesis. Nous sommes prêts à donner à une entreprise LLM un accès exclusif, en échange d'un OCR et d'une extraction de texte de haute qualité.</em> Architecture système Disons donc que vous avez trouvé des entreprises prêtes à héberger votre site web sans vous fermer — appelons-les “fournisseurs amoureux de la liberté” 😄. Vous découvrirez rapidement que tout héberger chez eux est plutôt coûteux, donc vous pourriez vouloir trouver des “fournisseurs bon marché” et faire l'hébergement réel là-bas, en passant par les fournisseurs amoureux de la liberté. Si vous le faites correctement, les fournisseurs bon marché ne sauront jamais ce que vous hébergez, et ne recevront jamais de plaintes. Avec tous ces fournisseurs, il y a un risque qu'ils vous ferment quand même, donc vous avez également besoin de redondance. Nous avons besoin de cela à tous les niveaux de notre pile. Une entreprise quelque peu amoureuse de la liberté qui s'est mise dans une position intéressante est Cloudflare. Ils ont <a %(blog_cloudflare)s>argumenté</a> qu'ils ne sont pas un fournisseur d'hébergement, mais une utilité, comme un FAI. Ils ne sont donc pas soumis aux demandes de retrait DMCA ou autres, et transmettent toute demande à votre véritable fournisseur d'hébergement. Ils sont allés jusqu'à aller en justice pour protéger cette structure. Nous pouvons donc les utiliser comme une autre couche de mise en cache et de protection. Cloudflare n'accepte pas les paiements anonymes, donc nous ne pouvons utiliser que leur plan gratuit. Cela signifie que nous ne pouvons pas utiliser leurs fonctionnalités d'équilibrage de charge ou de basculement. Nous avons donc <a %(annas_archive_l255)s>implémenté cela nous-mêmes</a> au niveau du domaine. Lors du chargement de la page, le navigateur vérifiera si le domaine actuel est toujours disponible, et sinon, il réécrit toutes les URL vers un autre domaine. Étant donné que Cloudflare met en cache de nombreuses pages, cela signifie qu'un utilisateur peut atterrir sur notre domaine principal, même si le serveur proxy est en panne, et ensuite au clic suivant être déplacé vers un autre domaine. Nous avons également des préoccupations opérationnelles normales à gérer, telles que la surveillance de la santé des serveurs, la journalisation des erreurs backend et frontend, etc. Notre architecture de basculement permet également plus de robustesse sur ce front, par exemple en exécutant un ensemble complètement différent de serveurs sur l'un des domaines. Nous pouvons même exécuter des versions plus anciennes du code et des datasets sur ce domaine séparé, au cas où un bug critique dans la version principale passerait inaperçu. Nous pouvons également nous prémunir contre un retournement de Cloudflare contre nous, en le supprimant d'un des domaines, comme ce domaine séparé. Différentes permutations de ces idées sont possibles. Conclusion Cela a été une expérience intéressante d'apprendre à configurer un moteur de recherche de bibliothèque fantôme robuste et résilient. Il y a beaucoup plus de détails à partager dans de futurs articles, alors faites-moi savoir ce que vous aimeriez apprendre davantage ! Comme toujours, nous recherchons des dons pour soutenir ce travail, alors n'oubliez pas de consulter la page Faites un don sur l'Archive d'Anna. Nous recherchons également d'autres types de soutien, tels que des subventions, des sponsors à long terme, des fournisseurs de paiement à haut risque, peut-être même des publicités (de bon goût !). Et si vous souhaitez contribuer de votre temps et de vos compétences, nous recherchons toujours des développeurs, des traducteurs, etc. Merci pour votre intérêt et votre soutien. Jetons d'innovation Commençons par notre pile technologique. Elle est délibérément ennuyeuse. Nous utilisons Flask, MariaDB et ElasticSearch. C'est littéralement tout. La recherche est en grande partie un problème résolu, et nous n'avons pas l'intention de la réinventer. De plus, nous devons dépenser nos <a %(mcfunley)s>jetons d'innovation</a> sur autre chose : ne pas être fermés par les autorités. Alors, à quel point Anna’s Archive est-elle légale ou illégale exactement ? Cela dépend principalement de la juridiction légale. La plupart des pays croient en une forme de droit d'auteur, ce qui signifie que des personnes ou des entreprises se voient attribuer un monopole exclusif sur certains types d'œuvres pour une certaine période. En passant, chez Anna’s Archive, nous pensons que bien qu'il y ait certains avantages, dans l'ensemble, le droit d'auteur est un net-négatif pour la société — mais c'est une histoire pour une autre fois. Ce monopole exclusif sur certaines œuvres signifie qu'il est illégal pour quiconque en dehors de ce monopole de distribuer directement ces œuvres — y compris nous. Mais Anna’s Archive est un moteur de recherche qui ne distribue pas directement ces œuvres (du moins pas sur notre site web en clair), donc nous devrions être en règle, n'est-ce pas ? Pas exactement. Dans de nombreuses juridictions, il est non seulement illégal de distribuer des œuvres protégées par le droit d'auteur, mais aussi de créer des liens vers des endroits qui le font. Un exemple classique de cela est la loi DMCA des États-Unis. C'est l'extrémité la plus stricte du spectre. À l'autre extrémité du spectre, il pourrait théoriquement y avoir des pays sans aucune loi sur le droit d'auteur, mais ceux-ci n'existent pas vraiment. Pratiquement tous les pays ont une forme de loi sur le droit d'auteur dans leurs livres. L'application est une autre histoire. Il y a beaucoup de pays dont les gouvernements ne se soucient pas d'appliquer la loi sur le droit d'auteur. Il y a aussi des pays entre les deux extrêmes, qui interdisent la distribution d'œuvres protégées par le droit d'auteur, mais n'interdisent pas de créer des liens vers ces œuvres. Une autre considération est au niveau de l'entreprise. Si une entreprise opère dans une juridiction qui ne se soucie pas du droit d'auteur, mais que l'entreprise elle-même n'est pas prête à prendre de risque, alors elle pourrait fermer votre site web dès que quelqu'un se plaint à ce sujet. Enfin, une grande considération est les paiements. Puisque nous devons rester anonymes, nous ne pouvons pas utiliser les méthodes de paiement traditionnelles. Cela nous laisse avec les cryptomonnaies, et seulement un petit sous-ensemble d'entreprises les soutiennent (il existe des cartes de débit virtuelles payées par crypto, mais elles sont souvent non acceptées). - Anna et l'équipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Je gère <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, le plus grand moteur de recherche open-source à but non lucratif pour les <a %(wikipedia_shadow_library)s>bibliothèques fantômes</a>, comme Sci-Hub, Library Genesis et Z-Library. Notre objectif est de rendre la connaissance et la culture facilement accessibles, et finalement de construire une communauté de personnes qui, ensemble, archivent et préservent <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tous les livres du monde</a>. Dans cet article, je vais montrer comment nous gérons ce site web, et les défis uniques qui accompagnent l'exploitation d'un site web avec un statut juridique discutable, puisqu'il n'y a pas d'“AWS pour les associations caritatives fantômes”. <em>Consultez également l'article associé <a %(blog_how_to_become_a_pirate_archivist)s>Comment devenir un archiviste pirate</a>.</em> Comment gérer une bibliothèque fantôme : opérations à l'Archive d'Anna Il n'y a pas de <q>AWS pour les associations caritatives fantômes,</q> alors comment gérons-nous l'Archive d'Anna ? Outils Serveur d'application : Flask, MariaDB, ElasticSearch, Docker. Développement : Gitlab, Weblate, Zulip. Gestion des serveurs : Ansible, Checkmk, UFW. Hébergement statique Onion : Tor, Nginx. Serveur proxy : Varnish. Voyons quels outils nous utilisons pour accomplir tout cela. Cela évolue beaucoup à mesure que nous rencontrons de nouveaux problèmes et trouvons de nouvelles solutions. Il y a certaines décisions sur lesquelles nous avons hésité. L'une d'elles concerne la communication entre serveurs : nous utilisions Wireguard pour cela, mais nous avons constaté qu'il cessait parfois de transmettre des données, ou ne les transmettait que dans une seule direction. Cela s'est produit avec plusieurs configurations différentes de Wireguard que nous avons essayées, telles que <a %(github_costela_wesher)s>wesher</a> et <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Nous avons également essayé de faire passer les ports via SSH, en utilisant autossh et sshuttle, mais nous avons rencontré <a %(github_sshuttle)s>des problèmes là-bas</a> (bien qu'il ne soit toujours pas clair pour moi si autossh souffre de problèmes TCP-sur-TCP ou non — cela me semble juste être une solution bancale, mais peut-être que c'est en fait correct ?). Au lieu de cela, nous sommes revenus à des connexions directes entre serveurs, cachant qu'un serveur fonctionne sur des fournisseurs bon marché en utilisant le filtrage IP avec UFW. Cela a l'inconvénient que Docker ne fonctionne pas bien avec UFW, à moins d'utiliser <code>network_mode: "host"</code>. Tout cela est un peu plus sujet aux erreurs, car vous exposez votre serveur à Internet avec juste une petite mauvaise configuration. Peut-être devrions-nous revenir à autossh — vos retours seraient très appréciés ici. Nous avons également hésité entre Varnish et Nginx. Nous aimons actuellement Varnish, mais il a ses bizarreries et ses aspérités. Il en va de même pour Checkmk : nous ne l'adorons pas, mais il fonctionne pour l'instant. Weblate a été correct mais pas incroyable — je crains parfois qu'il ne perde mes données chaque fois que j'essaie de les synchroniser avec notre dépôt git. Flask a été bon dans l'ensemble, mais il a quelques bizarreries étranges qui ont coûté beaucoup de temps à déboguer, comme la configuration de domaines personnalisés, ou des problèmes avec son intégration SqlAlchemy. Jusqu'à présent, les autres outils ont été excellents : nous n'avons pas de plaintes sérieuses concernant MariaDB, ElasticSearch, Gitlab, Zulip, Docker et Tor. Tous ont eu quelques problèmes, mais rien de trop sérieux ou chronophage. Communauté Le premier défi pourrait être surprenant. Ce n'est pas un problème technique, ni un problème juridique. C'est un problème psychologique : faire ce travail dans l'ombre peut être incroyablement solitaire. Selon ce que vous prévoyez de faire et votre modèle de menace, vous devrez peut-être être très prudent. À une extrémité du spectre, nous avons des personnes comme Alexandra Elbakyan*, la fondatrice de Sci-Hub, qui est très ouverte sur ses activités. Mais elle risque fortement d'être arrêtée si elle visite un pays occidental à ce stade, et pourrait faire face à des décennies de prison. Est-ce un risque que vous seriez prêt à prendre ? Nous sommes à l'autre extrémité du spectre ; étant très prudents pour ne laisser aucune trace et ayant une sécurité opérationnelle forte. * Comme mentionné sur HN par "ynno", Alexandra ne voulait initialement pas être connue : "Ses serveurs étaient configurés pour émettre des messages d'erreur détaillés de PHP, y compris le chemin complet du fichier source en faute, qui était sous le répertoire /home/<USER>'utilisateur qu'elle avait en ligne sur un site non lié, attaché à son vrai nom. Avant cette révélation, elle était anonyme." Donc, utilisez des noms d'utilisateur aléatoires sur les ordinateurs que vous utilisez pour ces choses, au cas où vous malconfigureriez quelque chose. Cette discrétion, cependant, a un coût psychologique. La plupart des gens aiment être reconnus pour le travail qu'ils font, et pourtant vous ne pouvez pas prendre de crédit pour cela dans la vie réelle. Même des choses simples peuvent être difficiles, comme des amis vous demandant ce que vous avez fait (à un moment donné "jouer avec mon NAS / homelab" devient lassant). C'est pourquoi il est si important de trouver une communauté. Vous pouvez renoncer à une partie de la sécurité opérationnelle en vous confiant à des amis très proches, en qui vous savez que vous pouvez avoir une confiance profonde. Même alors, soyez prudent de ne rien mettre par écrit, au cas où ils devraient remettre leurs e-mails aux autorités, ou si leurs appareils sont compromis d'une autre manière. Mieux encore est de trouver d'autres pirates. Si vos amis proches sont intéressés à vous rejoindre, tant mieux ! Sinon, vous pourriez être en mesure de trouver d'autres personnes en ligne. Malheureusement, c'est encore une communauté de niche. Jusqu'à présent, nous n'avons trouvé qu'une poignée d'autres personnes actives dans cet espace. Les forums de Library Genesis et r/DataHoarder semblent être de bons points de départ. L'Archive Team compte également des individus partageant les mêmes idées, bien qu'ils opèrent dans le cadre de la loi (même si dans certaines zones grises de la loi). Les scènes traditionnelles de "warez" et de piratage comptent également des personnes qui pensent de manière similaire. Nous sommes ouverts aux idées sur la manière de favoriser la communauté et d'explorer des idées. N'hésitez pas à nous envoyer un message sur Twitter ou Reddit. Peut-être pourrions-nous organiser une sorte de forum ou de groupe de discussion. Un défi est que cela peut facilement être censuré lorsque l'on utilise des plateformes courantes, donc nous devrions l'héberger nous-mêmes. Il y a aussi un compromis entre rendre ces discussions entièrement publiques (plus d'engagement potentiel) et les rendre privées (ne pas laisser les "cibles" potentielles savoir que nous sommes sur le point de les scraper). Nous devrons réfléchir à cela. Faites-nous savoir si cela vous intéresse ! Conclusion Nous espérons que cela sera utile pour les archivistes pirates débutants. Nous sommes ravis de vous accueillir dans ce monde, alors n'hésitez pas à nous contacter. Préservons autant que possible les connaissances et la culture du monde, et diffusons-les largement. Projets 4. Sélection des données Souvent, vous pouvez utiliser les metadata pour déterminer un sous-ensemble raisonnable de données à télécharger. Même si vous souhaitez finalement télécharger toutes les données, il peut être utile de prioriser les éléments les plus importants en premier, au cas où vous seriez détecté et que les défenses seraient améliorées, ou parce que vous auriez besoin d'acheter plus de disques, ou simplement parce que quelque chose d'autre surviendrait dans votre vie avant que vous ne puissiez tout télécharger. Par exemple, une collection peut avoir plusieurs éditions de la même ressource sous-jacente (comme un livre ou un film), où l'une est marquée comme étant de la meilleure qualité. Sauvegarder ces éditions en premier serait très judicieux. Vous pourriez éventuellement vouloir sauvegarder toutes les éditions, car dans certains cas, les metadata pourraient être mal étiquetées, ou il pourrait y avoir des compromis inconnus entre les éditions (par exemple, la "meilleure édition" pourrait être la meilleure à bien des égards mais pire à d'autres, comme un film ayant une résolution plus élevée mais sans sous-titres). Vous pouvez également rechercher dans votre base de données de metadata pour trouver des éléments intéressants. Quel est le plus gros fichier hébergé, et pourquoi est-il si gros ? Quel est le plus petit fichier ? Y a-t-il des motifs intéressants ou inattendus en ce qui concerne certaines catégories, langues, etc. ? Y a-t-il des titres en double ou très similaires ? Y a-t-il des motifs quant à la date d'ajout des données, comme un jour où de nombreux fichiers ont été ajoutés en même temps ? Vous pouvez souvent apprendre beaucoup en examinant le jeu de données de différentes manières. Dans notre cas, nous avons dédupliqué les livres de Z-Library par rapport aux hachages md5 dans Library Genesis, économisant ainsi beaucoup de temps de téléchargement et d'espace disque. C'est une situation assez unique cependant. Dans la plupart des cas, il n'existe pas de bases de données complètes des fichiers déjà correctement préservés par d'autres pirates. Cela représente en soi une énorme opportunité pour quelqu'un. Ce serait formidable d'avoir un aperçu régulièrement mis à jour des éléments comme la musique et les films qui sont déjà largement partagés sur les sites de torrents, et qui sont donc de moindre priorité à inclure dans les sites miroirs pirates. 6. Distribution Vous avez les données, vous donnant ainsi possession du premier site miroir pirate de votre cible (très probablement). À bien des égards, la partie la plus difficile est terminée, mais la partie la plus risquée est encore devant vous. Après tout, jusqu'à présent, vous avez été furtif ; volant sous le radar. Tout ce que vous aviez à faire était d'utiliser un bon VPN tout au long, de ne pas remplir vos informations personnelles dans aucun formulaire (évidemment), et peut-être d'utiliser une session de navigateur spéciale (ou même un ordinateur différent). Maintenant, vous devez distribuer les données. Dans notre cas, nous voulions d'abord contribuer les livres à Library Genesis, mais nous avons rapidement découvert les difficultés de cela (tri fiction vs non-fiction). Nous avons donc décidé de distribuer en utilisant des torrents de style Library Genesis. Si vous avez l'opportunité de contribuer à un projet existant, cela pourrait vous faire gagner beaucoup de temps. Cependant, il n'existe actuellement pas beaucoup de sites miroirs pirates bien organisés. Disons donc que vous décidez de distribuer des torrents vous-même. Essayez de garder ces fichiers petits, afin qu'ils soient faciles à reproduire sur d'autres sites web. Vous devrez alors semer les torrents vous-même, tout en restant anonyme. Vous pouvez utiliser un VPN (avec ou sans redirection de port), ou payer avec des Bitcoins mélangés pour un Seedbox. Si vous ne savez pas ce que certains de ces termes signifient, vous aurez beaucoup de lecture à faire, car il est important que vous compreniez les compromis de risque ici. Vous pouvez héberger les fichiers torrent eux-mêmes sur des sites de torrents existants. Dans notre cas, nous avons choisi d'héberger réellement un site web, car nous voulions également diffuser notre philosophie de manière claire. Vous pouvez le faire vous-même de manière similaire (nous utilisons Njalla pour nos domaines et notre hébergement, payés avec des Bitcoins mélangés), mais n'hésitez pas à nous contacter pour que nous hébergions vos torrents. Nous cherchons à construire un index complet de sites miroirs pirates au fil du temps, si cette idée prend. Quant à la sélection de VPN, beaucoup a déjà été écrit à ce sujet, nous allons donc simplement répéter le conseil général de choisir par réputation. Des politiques de non-journalisation testées en justice avec de longs antécédents de protection de la vie privée sont l'option la moins risquée, à notre avis. Notez que même lorsque vous faites tout correctement, vous ne pouvez jamais atteindre un risque zéro. Par exemple, lors de la mise en semence de vos torrents, un acteur étatique très motivé peut probablement examiner les flux de données entrants et sortants pour les serveurs VPN, et déduire qui vous êtes. Ou vous pouvez simplement faire une erreur d'une manière ou d'une autre. Nous l'avons probablement déjà fait, et le referons. Heureusement, les États-nations ne se soucient pas <em>tant</em> que ça du piratage. Une décision à prendre pour chaque projet est de savoir s'il faut le publier en utilisant la même identité qu'auparavant, ou non. Si vous continuez à utiliser le même nom, alors les erreurs de sécurité opérationnelle des projets précédents pourraient revenir vous hanter. Mais publier sous différents noms signifie que vous ne construisez pas une réputation durable. Nous avons choisi d'avoir une sécurité opérationnelle forte dès le départ pour pouvoir continuer à utiliser la même identité, mais nous n'hésiterons pas à publier sous un nom différent si nous faisons une erreur ou si les circonstances l'exigent. Faire passer le mot peut être délicat. Comme nous l'avons dit, c'est encore une communauté de niche. Nous avons initialement posté sur Reddit, mais avons vraiment gagné en traction sur Hacker News. Pour l'instant, notre recommandation est de le poster à quelques endroits et de voir ce qui se passe. Et encore une fois, contactez-nous. Nous aimerions diffuser la parole de plus d'efforts d'archivage pirate. 1. Sélection du domaine / philosophie Il n'y a pas de pénurie de connaissances et de patrimoine culturel à sauvegarder, ce qui peut être accablant. C'est pourquoi il est souvent utile de prendre un moment pour réfléchir à ce que peut être votre contribution. Chacun a une façon différente de penser à cela, mais voici quelques questions que vous pourriez vous poser : Dans notre cas, nous nous soucions particulièrement de la préservation à long terme de la science. Nous connaissions Library Genesis, et comment il était entièrement mirrorer plusieurs fois via des torrents. Nous aimions cette idée. Puis un jour, l'un de nous a essayé de trouver des manuels scientifiques sur Library Genesis, mais n'a pas pu les trouver, remettant en question sa complétude. Nous avons ensuite cherché ces manuels en ligne, et les avons trouvés ailleurs, ce qui a planté la graine de notre projet. Même avant de connaître la Z-Library, nous avions l'idée de ne pas essayer de collecter tous ces livres manuellement, mais de nous concentrer sur le mirroring des collections existantes, et de les contribuer à Library Genesis. Quelles compétences avez-vous que vous pouvez utiliser à votre avantage ? Par exemple, si vous êtes un expert en sécurité en ligne, vous pouvez trouver des moyens de contourner les blocages IP pour des cibles sécurisées. Si vous êtes excellent pour organiser des communautés, alors peut-être pouvez-vous rassembler des gens autour d'un objectif. Il est utile de connaître un peu la programmation, ne serait-ce que pour maintenir une bonne sécurité opérationnelle tout au long de ce processus. Quel serait un domaine à fort effet de levier sur lequel se concentrer ? Si vous allez passer X heures à archiver des contenus pirates, comment pouvez-vous obtenir le meilleur "retour sur investissement" ? Quelles sont les façons uniques dont vous pensez à cela ? Vous pourriez avoir des idées ou des approches intéressantes que d'autres auraient pu manquer. Combien de temps avez-vous pour cela ? Notre conseil serait de commencer petit et de réaliser des projets plus importants au fur et à mesure que vous vous y habituez, mais cela peut devenir très prenant. Pourquoi cela vous intéresse-t-il ? Qu'est-ce qui vous passionne ? Si nous pouvons réunir un groupe de personnes qui archivent toutes les choses qui leur tiennent spécifiquement à cœur, cela couvrirait beaucoup ! Vous en saurez beaucoup plus que la personne moyenne sur votre passion, comme quelles sont les données importantes à sauvegarder, quelles sont les meilleures collections et communautés en ligne, etc. 3. Scraping de metadata Date d'ajout/modification : pour que vous puissiez revenir plus tard et télécharger des fichiers que vous n'avez pas téléchargés auparavant (bien que vous puissiez souvent aussi utiliser l'ID ou le hash pour cela). Hash (md5, sha1) : pour confirmer que vous avez téléchargé le fichier correctement. ID : peut être un ID interne, mais des IDs comme ISBN ou DOI sont également utiles. Nom de fichier / emplacement Description, catégorie, tags, auteurs, langue, etc. Taille : pour calculer l'espace disque dont vous avez besoin. Entrons un peu plus dans les détails techniques. Pour récupérer les metadata des sites web, nous avons gardé les choses assez simples. Nous utilisons des scripts Python, parfois curl, et une base de données MySQL pour stocker les résultats. Nous n'avons pas utilisé de logiciel de scraping sophistiqué capable de cartographier des sites complexes, car jusqu'à présent, nous n'avons eu besoin de récupérer qu'un ou deux types de pages en énumérant simplement les identifiants et en analysant le HTML. S'il n'y a pas de pages facilement énumérables, vous pourriez avoir besoin d'un véritable crawler qui essaie de trouver toutes les pages. Avant de commencer à récupérer un site entier, essayez de le faire manuellement pendant un moment. Parcourez vous-même quelques dizaines de pages pour comprendre comment cela fonctionne. Parfois, vous rencontrerez déjà des blocages IP ou d'autres comportements intéressants de cette manière. Il en va de même pour le scraping de données : avant de vous plonger trop profondément dans cet objectif, assurez-vous de pouvoir effectivement télécharger ses données. Pour contourner les restrictions, il y a quelques choses que vous pouvez essayer. Y a-t-il d'autres adresses IP ou serveurs qui hébergent les mêmes données mais n'ont pas les mêmes restrictions ? Y a-t-il des points de terminaison API qui n'ont pas de restrictions, alors que d'autres en ont ? À quel rythme de téléchargement votre IP est-elle bloquée, et pour combien de temps ? Ou n'êtes-vous pas bloqué mais ralenti ? Que se passe-t-il si vous créez un compte utilisateur, comment les choses changent-elles alors ? Pouvez-vous utiliser HTTP/2 pour garder les connexions ouvertes, et cela augmente-t-il le taux auquel vous pouvez demander des pages ? Y a-t-il des pages qui listent plusieurs fichiers à la fois, et les informations listées y sont-elles suffisantes ? Les éléments que vous voudrez probablement sauvegarder incluent : Nous faisons généralement cela en deux étapes. D'abord, nous téléchargeons les fichiers HTML bruts, généralement directement dans MySQL (pour éviter beaucoup de petits fichiers, dont nous parlerons plus en détail ci-dessous). Ensuite, dans une étape distincte, nous parcourons ces fichiers HTML et les analysons dans de véritables tables MySQL. De cette façon, vous n'avez pas à tout re-télécharger depuis le début si vous découvrez une erreur dans votre code d'analyse, car vous pouvez simplement retraiter les fichiers HTML avec le nouveau code. Il est également souvent plus facile de paralléliser l'étape de traitement, ce qui permet de gagner du temps (et vous pouvez écrire le code de traitement pendant que le scraping est en cours, au lieu d'avoir à écrire les deux étapes en même temps). Enfin, notez que pour certaines cibles, le scraping des metadata est tout ce qu'il y a. Il existe d'énormes collections de metadata qui ne sont pas correctement préservées. Titre Sélection du domaine / philosophie : Sur quoi souhaitez-vous vous concentrer, et pourquoi ? Quelles sont vos passions, compétences et circonstances uniques que vous pouvez utiliser à votre avantage ? Sélection de la cible : Quelle collection spécifique allez-vous mirrorer ? Scraping des metadata : Cataloguer les informations sur les fichiers, sans réellement télécharger les fichiers eux-mêmes (souvent beaucoup plus volumineux). Sélection des données : Sur la base des metadata, déterminer quelles données sont les plus pertinentes à archiver maintenant. Cela pourrait être tout, mais souvent il y a un moyen raisonnable d'économiser de l'espace et de la bande passante. Scraping des données : Obtenir réellement les données. Distribution : Les emballer dans des torrents, les annoncer quelque part, inciter les gens à les diffuser. 5. Extraction des données Vous êtes maintenant prêt à télécharger réellement les données en masse. Comme mentionné précédemment, à ce stade, vous devriez déjà avoir téléchargé manuellement un tas de fichiers, pour mieux comprendre le comportement et les restrictions de la cible. Cependant, il y aura encore des surprises pour vous une fois que vous commencerez à télécharger beaucoup de fichiers à la fois. Notre conseil ici est principalement de garder les choses simples. Commencez par télécharger simplement un tas de fichiers. Vous pouvez utiliser Python, puis passer à plusieurs threads. Mais parfois, il est encore plus simple de générer directement des fichiers Bash à partir de la base de données, puis d'en exécuter plusieurs dans plusieurs fenêtres de terminal pour augmenter l'échelle. Un petit truc technique à mentionner ici est l'utilisation de OUTFILE dans MySQL, que vous pouvez écrire n'importe où si vous désactivez "secure_file_priv" dans mysqld.cnf (et assurez-vous également de désactiver/contourner AppArmor si vous êtes sur Linux). Nous stockons les données sur de simples disques durs. Commencez avec ce que vous avez, et développez lentement. Il peut être accablant de penser à stocker des centaines de To de données. Si c'est la situation à laquelle vous faites face, mettez d'abord un bon sous-ensemble, et dans votre annonce, demandez de l'aide pour stocker le reste. Si vous souhaitez obtenir plus de disques durs vous-même, alors r/DataHoarder propose de bonnes ressources pour obtenir de bonnes affaires. Essayez de ne pas trop vous soucier des systèmes de fichiers sophistiqués. Il est facile de tomber dans le piège de la configuration de choses comme ZFS. Un détail technique à connaître cependant, est que de nombreux systèmes de fichiers ne gèrent pas bien un grand nombre de fichiers. Nous avons trouvé qu'une solution simple consiste à créer plusieurs répertoires, par exemple pour différentes plages d'ID ou préfixes de hachage. Après avoir téléchargé les données, assurez-vous de vérifier l'intégrité des fichiers en utilisant les hachages dans les metadata, si disponibles. 2. Sélection de la cible Accessible : n'utilise pas des tonnes de couches de protection pour vous empêcher de récupérer leurs metadata et données. Connaissance spéciale : vous avez des informations particulières sur cet objectif, comme un accès spécial à cette collection, ou vous avez trouvé comment contourner leurs défenses. Ce n'est pas obligatoire (notre projet à venir ne fait rien de spécial), mais cela aide certainement ! Grande Alors, nous avons notre domaine d'intérêt, maintenant quelle collection spécifique devons-nous mirrorer ? Il y a quelques éléments qui font un bon objectif : Lorsque nous avons trouvé nos manuels de sciences sur des sites autres que Library Genesis, nous avons essayé de comprendre comment ils avaient fait leur chemin sur Internet. Nous avons ensuite découvert Z-Library, et réalisé que bien que la plupart des livres n'y apparaissent pas en premier, ils finissent par s'y retrouver. Nous avons appris sa relation avec Library Genesis, et la structure d'incitation (financière) et l'interface utilisateur supérieure, qui en faisaient une collection beaucoup plus complète. Nous avons ensuite effectué quelques préliminaires de scraping de metadata et de données, et réalisé que nous pouvions contourner leurs limites de téléchargement IP, en tirant parti de l'accès spécial de l'un de nos membres à de nombreux serveurs proxy. En explorant différents objectifs, il est déjà important de cacher vos traces en utilisant des VPN et des adresses e-mail jetables, dont nous parlerons plus tard. Unique : pas déjà bien couverte par d'autres projets. Lorsque nous réalisons un projet, il comporte plusieurs phases : Ce ne sont pas des phases complètement indépendantes, et souvent des idées d'une phase ultérieure vous renvoient à une phase antérieure. Par exemple, lors du scraping des metadata, vous pourriez réaliser que la cible que vous avez sélectionnée a des mécanismes de défense au-delà de votre niveau de compétence (comme des blocages IP), donc vous revenez en arrière et trouvez une autre cible. - Anna et l'équipe (<a %(reddit)s>Reddit</a>) Des livres entiers peuvent être écrits sur le <em>pourquoi</em> de la préservation numérique en général, et de l'archivisme pirate en particulier, mais donnons un bref aperçu pour ceux qui ne sont pas trop familiers. Le monde produit plus de savoir et de culture que jamais auparavant, mais plus que jamais, une grande partie est perdue. L'humanité confie en grande partie ce patrimoine à des entreprises comme les éditeurs académiques, les services de streaming et les entreprises de médias sociaux, et elles ne se sont souvent pas révélées être de grands gardiens. Consultez le documentaire Digital Amnesia, ou vraiment n'importe quelle conférence de Jason Scott. Il y a certaines institutions qui font un bon travail d'archivage autant qu'elles le peuvent, mais elles sont liées par la loi. En tant que pirates, nous sommes dans une position unique pour archiver des collections qu'elles ne peuvent pas toucher, en raison de l'application des droits d'auteur ou d'autres restrictions. Nous pouvons également reproduire des collections de nombreuses fois à travers le monde, augmentant ainsi les chances d'une préservation adéquate. Pour l'instant, nous n'entrerons pas dans les discussions sur les avantages et les inconvénients de la propriété intellectuelle, la moralité de la violation de la loi, les réflexions sur la censure ou la question de l'accès au savoir et à la culture. Avec tout cela de côté, plongeons dans le <em>comment</em>. Nous partagerons comment notre équipe est devenue des archivistes pirates, et les leçons que nous avons apprises en cours de route. Il y a de nombreux défis lorsque vous vous lancez dans ce voyage, et nous espérons pouvoir vous aider à traverser certains d'entre eux. Comment devenir un archiviste pirate Le premier défi pourrait être surprenant. Ce n'est pas un problème technique, ni un problème juridique. C'est un problème psychologique. Avant de plonger, deux mises à jour sur le Pirate Library Mirror (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d’Anna</a>) : Nous avons reçu des dons extrêmement généreux. Le premier était de 10 000 $ de la part d'un individu anonyme qui a également soutenu "bookwarrior", le fondateur original de Library Genesis. Un merci spécial à bookwarrior pour avoir facilité ce don. Le second était un autre don de 10 000 $ d'un donateur anonyme, qui a pris contact après notre dernière publication et a été inspiré pour aider. Nous avons également reçu un certain nombre de petits dons. Merci beaucoup pour tout votre soutien généreux. Nous avons des projets passionnants en préparation que cela soutiendra, alors restez à l'écoute. Nous avons rencontré quelques difficultés techniques avec la taille de notre deuxième publication, mais nos torrents sont maintenant en ligne et partagés. Nous avons également reçu une offre généreuse d'un individu anonyme pour partager notre collection sur leurs serveurs à très haute vitesse, donc nous faisons un téléversement spécial vers leurs machines, après quoi tous ceux qui téléchargent la collection devraient voir une grande amélioration de la vitesse. Articles de blog Bonjour, je suis Anna. J'ai créé <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, la plus grande bibliothèque fantôme du monde. Ceci est mon blog personnel, dans lequel mes coéquipiers et moi écrivons sur le piratage, la préservation numérique, et plus encore. Connectez-vous avec moi sur <a %(reddit)s>Reddit</a>. Notez que ce site web est juste un blog. Nous n'hébergeons ici que nos propres mots. Aucun torrent ou autre fichier protégé par des droits d'auteur n'est hébergé ou lié ici. <strong>Bibliothèque</strong> - Comme la plupart des bibliothèques, nous nous concentrons principalement sur les documents écrits comme les livres. Nous pourrions nous étendre à d'autres types de médias à l'avenir. <strong>Miroir</strong> - Nous sommes strictement un miroir de bibliothèques existantes. Nous nous concentrons sur la préservation, pas sur la facilité de recherche et de téléchargement des livres (accès) ou sur la création d'une grande communauté de personnes qui contribuent de nouveaux livres (approvisionnement). <strong>Pirate</strong> - Nous violons délibérément la loi sur le droit d'auteur dans la plupart des pays. Cela nous permet de faire quelque chose que les entités légales ne peuvent pas faire : s'assurer que les livres sont diffusés largement. <em>Nous ne faisons pas de lien vers les fichiers depuis ce blog. Veuillez les trouver vous-même.</em> - Anna et l'équipe (<a %(reddit)s>Reddit</a>) Ce projet (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>) vise à contribuer à la préservation et à la libération des connaissances humaines. Nous apportons notre modeste contribution, dans les pas des grands qui nous ont précédés. L'objectif de ce projet est illustré par son nom : La première bibliothèque que nous avons reproduite est Z-Library. C'est une bibliothèque populaire (et illégale). Ils ont pris la collection de Library Genesis et l'ont rendue facilement consultable. De plus, ils sont devenus très efficaces pour solliciter de nouvelles contributions de livres, en incitant les utilisateurs contributeurs avec divers avantages. Actuellement, ils ne contribuent pas ces nouveaux livres à Library Genesis. Et contrairement à Library Genesis, ils ne rendent pas leur collection facilement reproductible, ce qui empêche une large préservation. Cela est important pour leur modèle économique, car ils facturent l'accès à leur collection en masse (plus de 10 livres par jour). Nous ne portons pas de jugement moral sur le fait de facturer l'accès en masse à une collection de livres illégale. Il est indéniable que la Z-Library a réussi à élargir l'accès au savoir et à obtenir plus de livres. Nous sommes simplement ici pour faire notre part : assurer la préservation à long terme de cette collection privée. Nous vous invitons à aider à préserver et libérer les connaissances humaines en téléchargeant et en partageant nos torrents. Consultez la page du projet pour plus d'informations sur l'organisation des données. Nous vous invitons également à contribuer avec vos idées sur les collections à reproduire ensuite, et comment procéder. Ensemble, nous pouvons accomplir beaucoup. Ceci n'est qu'une petite contribution parmi tant d'autres. Merci, pour tout ce que vous faites. Présentation du Miroir de la Bibliothèque Pirate : Préserver 7 To de livres (qui ne sont pas dans Libgen) 10% of l'héritage écrit de l'humanité préservé pour toujours <strong>Google.</strong> Après tout, ils ont fait cette recherche pour Google Books. Cependant, leurs metadata ne sont pas accessibles en masse et sont plutôt difficiles à extraire. <strong>Divers systèmes de bibliothèques et archives individuels.</strong> Il existe des bibliothèques et des archives qui n'ont pas été indexées et agrégées par aucun des précédents, souvent parce qu'elles sont sous-financées, ou pour d'autres raisons ne souhaitent pas partager leurs données avec Open Library, OCLC, Google, etc. Beaucoup d'entre elles ont des enregistrements numériques accessibles via Internet, et elles ne sont souvent pas très bien protégées, donc si vous voulez aider et vous amuser à apprendre sur des systèmes de bibliothèques étranges, ce sont d'excellents points de départ. <strong>ISBNdb.</strong> C'est le sujet de cet article de blog. ISBNdb extrait des données de divers sites web pour les metadata de livres, en particulier les données de tarification, qu'ils vendent ensuite aux libraires, afin qu'ils puissent fixer le prix de leurs livres en fonction du reste du marché. Puisque les ISBN sont assez universels de nos jours, ils ont effectivement construit une « page web pour chaque livre ». <strong>Open Library.</strong> Comme mentionné précédemment, c'est leur mission entière. Ils ont collecté d'énormes quantités de données de bibliothèques provenant de bibliothèques coopérantes et d'archives nationales, et continuent de le faire. Ils ont également des bibliothécaires bénévoles et une équipe technique qui tentent de dédupliquer les enregistrements et de les étiqueter avec toutes sortes de metadata. Le meilleur de tout, leur ensemble de données est complètement ouvert. Vous pouvez simplement <a %(openlibrary)s>le télécharger</a>. <strong>WorldCat.</strong> C'est un site web géré par l'organisation à but non lucratif OCLC, qui vend des systèmes de gestion de bibliothèques. Ils agrègent les metadata de livres de nombreuses bibliothèques et les rendent disponibles via le site web WorldCat. Cependant, ils gagnent également de l'argent en vendant ces données, donc elles ne sont pas disponibles pour un téléchargement en masse. Ils ont quelques ensembles de données en masse plus limités disponibles en téléchargement, en coopération avec des bibliothèques spécifiques. 1. Pour une définition raisonnable de « pour toujours ». ;) 2. Bien sûr, le patrimoine écrit de l'humanité est bien plus que des livres, surtout de nos jours. Pour les besoins de cet article et de nos récentes publications, nous nous concentrons sur les livres, mais nos intérêts vont plus loin. 3. Il y a beaucoup plus à dire sur Aaron Swartz, mais nous voulions simplement le mentionner brièvement, car il joue un rôle central dans cette histoire. Avec le temps, plus de gens pourraient rencontrer son nom pour la première fois, et pourront ensuite plonger dans le terrier du lapin eux-mêmes. <strong>Copies physiques.</strong> Évidemment, cela n'est pas très utile, car ce ne sont que des duplicatas du même document. Ce serait génial si nous pouvions préserver toutes les annotations que les gens font dans les livres, comme les célèbres « gribouillis dans les marges » de Fermat. Mais hélas, cela restera un rêve d'archiviste. <strong>« Éditions ».</strong> Ici, vous comptez chaque version unique d'un livre. Si quelque chose est différent, comme une couverture différente ou une préface différente, cela compte comme une édition différente. <strong>Fichiers.</strong> Lorsqu'on travaille avec des bibliothèques fantômes comme Library Genesis, Sci-Hub ou Z-Library, il y a une considération supplémentaire. Il peut y avoir plusieurs scans de la même édition. Et les gens peuvent créer de meilleures versions de fichiers existants, en scannant le texte à l'aide de l'OCR, ou en rectifiant des pages qui ont été scannées de travers. Nous voulons compter ces fichiers comme une seule édition, ce qui nécessiterait de bons metadata, ou une déduplication utilisant des mesures de similarité de documents. <strong>« Œuvres ».</strong> Par exemple, « Harry Potter et la Chambre des Secrets » en tant que concept logique, englobant toutes ses versions, comme les différentes traductions et réimpressions. C'est une définition assez utile, mais il peut être difficile de tracer la ligne de ce qui compte. Par exemple, nous voulons probablement préserver les différentes traductions, bien que les réimpressions avec seulement des différences mineures pourraient ne pas être aussi importantes. - Anna et l'équipe (<a %(reddit)s>Reddit</a>) Avec le Miroir de la Bibliothèque Pirate (ÉDIT : déplacé vers <a %(wikipedia_annas_archive)s>Les Archives d'Anna</a>), notre objectif est de prendre tous les livres du monde et de les préserver pour toujours.<sup>1</sup> Entre nos torrents Z-Library et les torrents originaux de Library Genesis, nous avons 11 783 153 fichiers. Mais combien cela représente-t-il vraiment ? Si nous dédupliquions correctement ces fichiers, quel pourcentage de tous les livres du monde avons-nous préservé ? Nous aimerions vraiment avoir quelque chose comme ça : Commençons par quelques chiffres approximatifs : Dans Z-Library/Libgen et Open Library, il y a beaucoup plus de livres que d'ISBN uniques. Cela signifie-t-il que beaucoup de ces livres n'ont pas d'ISBN, ou que les metadata des ISBN sont simplement manquantes ? Nous pouvons probablement répondre à cette question avec une combinaison de correspondance automatisée basée sur d'autres attributs (titre, auteur, éditeur, etc.), en intégrant plus de sources de données, et en extrayant les ISBN des scans réels des livres eux-mêmes (dans le cas de Z-Library/Libgen). Combien de ces ISBN sont uniques ? Cela est mieux illustré par un diagramme de Venn : Pour être plus précis : Nous avons été surpris par le peu de chevauchement qu'il y a ! ISBNdb possède une énorme quantité d'ISBN qui n'apparaissent ni dans Z-Library ni dans Open Library, et il en va de même (à un degré moindre mais toujours substantiel) pour les deux autres. Cela soulève de nombreuses nouvelles questions. Dans quelle mesure le rapprochement automatisé aiderait-il à étiqueter les livres qui n'ont pas été étiquetés avec des ISBN ? Y aurait-il beaucoup de correspondances et donc un chevauchement accru ? De plus, que se passerait-il si nous ajoutions un 4ème ou 5ème ensemble de données ? Quel chevauchement verrions-nous alors ? Cela nous donne un point de départ. Nous pouvons maintenant examiner tous les ISBN qui n'étaient pas dans l'ensemble de données de Z-Library, et qui ne correspondent pas non plus aux champs titre/auteur. Cela peut nous aider à préserver tous les livres du monde : d'abord en scrappant Internet pour des scans, puis en sortant dans la vie réelle pour numériser des livres. Ce dernier pourrait même être financé par la foule, ou motivé par des « primes » de personnes souhaitant voir certains livres numérisés. Tout cela est une histoire pour une autre fois. Si vous souhaitez aider dans l'un de ces domaines — analyse approfondie ; extraction de plus de metadata ; recherche de plus de livres ; OCR de livres ; faire cela pour d'autres domaines (par exemple, articles, livres audio, films, émissions de télévision, magazines) ou même rendre certaines de ces données disponibles pour des choses comme l'entraînement de modèles de langage ML / de grande taille — veuillez me contacter (<a %(reddit)s>Reddit</a>). Si vous êtes spécifiquement intéressé par l'analyse de données, nous travaillons à rendre nos ensembles de données et scripts disponibles dans un format plus facile à utiliser. Ce serait formidable si vous pouviez simplement forker un notebook et commencer à jouer avec cela. Enfin, si vous souhaitez soutenir ce travail, veuillez envisager de faire un don. Il s'agit d'une opération entièrement gérée par des bénévoles, et votre contribution fait une énorme différence. Chaque contribution compte. Pour l'instant, nous acceptons les dons en crypto ; consultez la page Faites un don sur l'Archive d'Anna. Pour un pourcentage, nous avons besoin d'un dénominateur : le nombre total de livres jamais publiés.<sup>2</sup> Avant la disparition de Google Books, un ingénieur du projet, Leonid Taycher, <a %(booksearch_blogspot)s>a essayé d'estimer</a> ce nombre. Il est arrivé — avec humour — à 129 864 880 (« au moins jusqu'à dimanche »). Il a estimé ce nombre en construisant une base de données unifiée de tous les livres du monde. Pour cela, il a rassemblé différents ensembles de données et les a ensuite fusionnés de diverses manières. En passant, il y a une autre personne qui a tenté de cataloguer tous les livres du monde : Aaron Swartz, le regretté activiste numérique et co-fondateur de Reddit.<sup>3</sup> Il a <a %(youtube)s>lancé Open Library</a> avec pour objectif « une page web pour chaque livre jamais publié », en combinant des données provenant de nombreuses sources différentes. Il a fini par payer le prix ultime pour son travail de préservation numérique lorsqu'il a été poursuivi pour avoir téléchargé en masse des articles académiques, ce qui a conduit à son suicide. Inutile de dire que c'est l'une des raisons pour lesquelles notre groupe est pseudonyme, et pourquoi nous faisons très attention. Open Library est toujours héroïquement géré par des personnes de l'Internet Archive, poursuivant l'héritage d'Aaron. Nous y reviendrons plus tard dans cet article. Dans le billet de blog de Google, Taycher décrit certains des défis liés à l'estimation de ce nombre. Tout d'abord, qu'est-ce qui constitue un livre ? Il existe quelques définitions possibles : Les « Éditions » semblent être la définition la plus pratique de ce que sont les « livres ». Heureusement, cette définition est également utilisée pour attribuer des numéros ISBN uniques. Un ISBN, ou International Standard Book Number, est couramment utilisé pour le commerce international, car il est intégré au système international de codes-barres (« International Article Number »). Si vous voulez vendre un livre en magasin, il a besoin d'un code-barres, donc vous obtenez un ISBN. Le billet de blog de Taycher mentionne que bien que les ISBN soient utiles, ils ne sont pas universels, car ils n'ont été réellement adoptés qu'au milieu des années soixante-dix, et pas partout dans le monde. Néanmoins, l'ISBN est probablement l'identifiant le plus largement utilisé pour les éditions de livres, donc c'est notre meilleur point de départ. Si nous pouvons trouver tous les ISBN du monde, nous obtenons une liste utile des livres qui doivent encore être préservés. Alors, où obtenons-nous les données ? Il existe un certain nombre d'efforts existants qui tentent de compiler une liste de tous les livres du monde : Dans cet article, nous sommes heureux d'annoncer une petite sortie (comparée à nos précédentes sorties de Z-Library). Nous avons extrait la plupart d'ISBNdb, et rendu les données disponibles pour le téléchargement en torrent sur le site de Pirate Library Mirror (EDIT : déplacé vers <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a> ; nous ne le lierons pas directement ici, il suffit de le rechercher). Il s'agit d'environ 30,9 millions d'enregistrements (20 Go en <a %(jsonlines)s>JSON Lines</a> ; 4,4 Go compressés). Sur leur site web, ils affirment qu'ils ont en fait 32,6 millions d'enregistrements, donc nous avons peut-être manqué certains, ou <em>ils</em> pourraient faire quelque chose de mal. Dans tous les cas, pour l'instant, nous ne partagerons pas exactement comment nous l'avons fait — nous laisserons cela comme un exercice pour le lecteur. ;-) Ce que nous partagerons, c'est une analyse préliminaire, pour essayer de se rapprocher de l'estimation du nombre de livres dans le monde. Nous avons examiné trois ensembles de données : ce nouvel ensemble de données ISBNdb, notre première sortie de metadata que nous avons extraite de la bibliothèque fantôme Z-Library (qui inclut Library Genesis), et le dump de données d'Open Library. Dump ISBNdb, ou Combien de Livres Sont Préservés Pour Toujours ? Si nous devions correctement dédupliquer les fichiers des bibliothèques fantômes, quel pourcentage de tous les livres du monde avons-nous préservé ? Mises à jour sur <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, la plus grande bibliothèque véritablement ouverte de l'histoire humaine. <em>Refonte de WorldCat</em> Données <strong>Format ?</strong> <a %(blog)s>Conteneurs d'Anna’s Archive (AAC)</a>, qui est essentiellement des <a %(jsonlines)s>JSON Lines</a> compressées avec <a %(zstd)s>Zstandard</a>, plus quelques sémantiques standardisées. Ces conteneurs enveloppent divers types d'enregistrements, basés sur les différentes extractions que nous avons déployées. Il y a un an, nous avons <a %(blog)s>entrepris</a> de répondre à cette question : <strong>Quel pourcentage de livres a été préservé de manière permanente par les bibliothèques fantômes ?</strong> Examinons quelques informations de base sur les données : Une fois qu'un livre entre dans une bibliothèque fantôme de données ouvertes comme <a %(wikipedia_library_genesis)s>Library Genesis</a>, et maintenant <a %(wikipedia_annas_archive)s>l'Archive d'Anna</a>, il est dupliqué dans le monde entier (via des torrents), le préservant ainsi pratiquement pour toujours. Pour répondre à la question de savoir quel pourcentage de livres a été préservé, nous devons connaître le dénominateur : combien de livres existent au total ? Et idéalement, nous n'avons pas seulement un chiffre, mais des metadata réelles. Ensuite, nous pouvons non seulement les comparer aux bibliothèques fantômes, mais aussi <strong>créer une liste de livres restants à préserver !</strong> Nous pourrions même commencer à rêver d'un effort participatif pour parcourir cette liste. Nous avons extrait des données de <a %(wikipedia_isbndb_com)s>ISBNdb</a> et téléchargé le <a %(openlibrary)s>jeu de données d'Open Library</a>, mais les résultats étaient insatisfaisants. Le principal problème était qu'il n'y avait pas beaucoup de chevauchement des ISBN. Consultez ce diagramme de Venn de <a %(blog)s>notre article de blog</a> : Nous avons été très surpris par le peu de chevauchement entre ISBNdb et Open Library, qui incluent tous deux des données de diverses sources, telles que des extractions web et des enregistrements de bibliothèques. Si les deux faisaient un bon travail pour trouver la plupart des ISBN existants, leurs cercles auraient sûrement un chevauchement substantiel, ou l'un serait un sous-ensemble de l'autre. Cela nous a fait nous demander combien de livres se trouvent <em>complètement en dehors de ces cercles</em> ? Nous avons besoin d'une base de données plus grande. C'est alors que nous avons porté notre attention sur la plus grande base de données de livres au monde : <a %(wikipedia_worldcat)s>WorldCat</a>. Il s'agit d'une base de données propriétaire de l'organisation à but non lucratif <a %(wikipedia_oclc)s>OCLC</a>, qui agrège des enregistrements de metadata de bibliothèques du monde entier, en échange de donner à ces bibliothèques l'accès à l'ensemble du jeu de données, et de les faire apparaître dans les résultats de recherche des utilisateurs finaux. Bien que l'OCLC soit une organisation à but non lucratif, leur modèle économique nécessite de protéger leur base de données. Eh bien, nous sommes désolés de le dire, amis de l'OCLC, nous allons tout révéler. :-) Au cours de l'année écoulée, nous avons méticuleusement extrait tous les enregistrements de WorldCat. Au début, nous avons eu un coup de chance. WorldCat venait de lancer la refonte complète de son site web (en août 2022). Cela comprenait une refonte substantielle de leurs systèmes backend, introduisant de nombreuses failles de sécurité. Nous avons immédiatement saisi l'opportunité et avons pu extraire des centaines de millions (!) d'enregistrements en quelques jours. Après cela, les failles de sécurité ont été lentement corrigées une par une, jusqu'à ce que la dernière que nous ayons trouvée soit corrigée il y a environ un mois. À ce moment-là, nous avions pratiquement tous les enregistrements et nous ne cherchions qu'à obtenir des enregistrements de qualité légèrement supérieure. Nous avons donc estimé qu'il était temps de publier ! 1,3 milliard de grattage WorldCat <em><strong>TL;DR :</strong> L'Archive d'Anna a gratté l'intégralité de WorldCat (la plus grande collection de metadata de bibliothèque au monde) pour créer une liste de livres à préserver.</em> WorldCat Avertissement : cet article de blog a été déprécié. Nous avons décidé que l'IPFS n'est pas encore prêt pour le grand public. Nous continuerons à lier des fichiers sur IPFS depuis l'Archive d'Anna lorsque cela est possible, mais nous ne l'hébergerons plus nous-mêmes, ni ne recommandons aux autres de créer un site miroir en utilisant IPFS. Veuillez consulter notre page Torrents si vous souhaitez aider à préserver notre collection. Aidez à semer Z-Library sur IPFS Téléchargement depuis un Serveur Partenaire SciDB Emprunt externe Emprunt externe (version "print-disabled") Téléchargement externe Explorer les métadonnées Disponible dans les torrents Retour  (+%(num)s bonus) non payé payé annulé expiré En attente de la confirmation d'Anna invalide Le texte continue ci-dessous en anglais. Aller Réinitialiser Avancer Dernier Si votre adresse email ne fonctionne pas sur les forums de Libgen, nous vous conseillons d'utiliser <a %(a_mail)s>Proton Mail</a> (gratuit). Vous pouvez aussi <a %(a_manual)s>demander manuellement</a> que votre compte soit activé. (peut nécessiter une <a %(a_browser)s>vérification du navigateur</a> — téléchargements illimités !) Serveur Partenaire Rapide #%(number)s (recommandé) (légèrement plus rapide, mais avec une liste d'attente) (aucune vérification de navigateur requise) (pas de vérification du navigateur ou de listes d'attente) (pas de liste d'attente, mais peut être très lent) Serveur Partenaire lent #%(number)s Livre audio Bande-dessinée Livre (fiction) Livre (non-fiction) Livre (inconnu) Article de journal Magazine Partition de musique Autre Documents standards Certaines pages n'ont pas pu être converties en PDF Marqué comme endommagé dans Libgen.li Non visible dans Libgen.li Non visible dans Libgen.rs Fiction Non visible dans Libgen.rs Non-Fiction L'exécution de exiftool a échoué sur ce fichier Marqué comme "mauvais fichier" dans Z-Library Manquant sur Z-Library Marqué comme "spam" dans Z-Library Impossible d'ouvrir le fichier (ex : fichier corrompu, DRM) Revendication de droits d'auteur Problèmes de téléchargement (ex : impossible de se connecter, message d'erreur, extrême lenteur) Métadonnées incorrectes (ex : titre, description, image de couverture) Autre Mauvaise qualité (ex : problèmes de formatage, scan de mauvaise qualité, pages manquantes) Ce fichier/spam devrait être retiré (ex : pub, contenu abusif) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brillant Bibliophile Libraire Lucide Dico Doux-Dingue Archiviste Astral Téléchargements bonus Cerlalc Métadonnées tchèques DuXiu 读秀 Index des eBooks EBSCOhost Google Livres (Google Books) Goodreads HathiTrust IA Prêt numérique contrôlé par Internet Archive (Controlled Digital Lending) ISBNdb ISBN GRP Libgen.li Exclure "scimag" Libgen.rs Non-Fiction et Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Bibliothèque d'État de Russie Sci-Hub Via Libgen.li « scimag » Sci-Hub / Libgen « scimag » Trantor Téléversements vers AA Z-Library Z-Library chinois Recherche par titre, auteur, langue, type de fichier, ISBN, MD5, … Rechercher Auteur Description et commentaires dans les métadonnées Édition Nom du fichier d'origine Maison d'édition (rechercher un champs spécifique) Titre Année de publication Afficher les détails techniques Cette cryptomonnaie exige un montant minimum plus haut que la normale. Veuillez choisir un abonnement ou une cryptomonnaie différente. La demande n'a pas pu être complétée. Veuillez réessayer dans quelques minutes, et si le problème persiste, contactez-nous à l'adresse %(email)s avec une capture d'écran. Une erreur inconnue s'est produite. Veuillez nous contacter à %(email)s avec une capture d'écran. Erreur durant le processus de paiement. Merci d'attendre un moment puis d'essayer à nouveau. Si le problème persiste pendant plus de 24 heures, merci de nous contacter à %(email)s avec une capture d'écran. Nous menons une campagne de financement pour <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">sauvegarder</a> la plus grande bibliothèque de comics au monde. Merci pour votre soutien ! <a href="/donate">Faire un don.</a> Si vous ne pouvez pas faire de don, soutenez nous en parlant à vos amis, et en nous suivant sur <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, ou <a href="https://t.me/annasarchiveorg">Telegram</a>. Ne nous envoyez pas d'e-mail pour une <a %(a_request)s>demande de livre</a><br>ou de <a %(a_upload)s>téléversement</a> d'un petit fichier (<10k). Les Archives d'Anna DMCA / réclamations de droits d'auteurs Restez en contact Reddit Alternatives SLUM (%(unaffiliated)s) non affilié Les Archives d'Anna ont besoin de votre aide ! Si vous faites un don maintenant, vous obtenez <strong>le double</strong> du nombre de téléchargements rapides. Beaucoup essaient de nous faire tomber, mais nous continuons la lutte. Si vous faites un don ce mois-ci, vous obtenez <strong>le double</strong> du nombre de téléchargements rapides. Valable jusqu'à la fin du mois en cours. Sauver le savoir humain : un super cadeau de vacances ! Les adhésions seront prolongées en conséquence. Les serveurs partenaires sont indisponibles en raison de fermetures de l'hébergement. Ils devraient bientôt être de nouveau opérationnels. Pour augmenter la résilience des Archives d'Anna, nous recherchons des volontaires capables de maintenir des archives-miroir. Nous disposons d'une nouvelle méthode de don : %(method_name)s. S'il vous plait, considérez %(donate_link_open_tag)sdonner </a> - Faire fonctionner ce site coûte cher, et vos dons font vraiment la différence. Merci beaucoup. Parrainez un ami, et bénéficiez tous les deux de %(percentage)s%% de téléchargements rapides en plus ! Surprenez un proche, offrez-lui un compte avec adhésion. Le parfait cadeau pour la Saint-Valentin ! En savoir plus… Compte Activité Avancé Le Blog d'Anna ↗ Les Logiciels d'Anna ↗ beta Explorateur de Codes Jeux de données Contribuer Fichiers téléchargés FAQ Accueil Améliorer les métadonnées Données de LLM Se connecter / S'inscrire Mes dons Profil public Rechercher Sécurité Torrents Traduire ↗ Bénévolat & Récompenses Téléchargements récents : 📚&nbsp;La plus grande bibliothèque open-source et open-data au monde. ⭐️&nbsp;Archive-miroir de Sci-Hub, Library Genesis, Z-Library, et autres. 📈&nbsp;%(book_any)s livres, %(journal_article)s articles, %(book_comic)s bandes-dessinées, %(magazine)s magazines — préservés pour toujours.  et  et plus encore DuXiu Bibliothèque de prêt de l'Internet Archive LibGen 📚&nbsp;La plus grande bibliothèque véritablement ouverte de l’histoire de l’humanité. 📈&nbsp;%(book_count)s&nbsp;livres, %(paper_count)s&nbsp;articles— préservés à tout jamais. ⭐️&nbsp;Archive-miroir pour %(libraries)s. Nous arpentons et rendons open-source %(scraped)s. Tout notre code et nos données sont entièrement open-source. OpenLib Sci-Hub ,  📚 La plus grande bibliothèque open source de données libres.<br>⭐️ Archive-miroir de Scihub, Libgen, Zlib, et bien d'autres. Z-Lib Archives d'Anna Requête invalide. Visitez %(websites)s. La plus grande bibliothèque open-source et open-data au monde. Inclus Sci-Hub, Library Genesis, Z-Library, et plus. Rechercher dans les Archives d'Anna Archives d'Anna Veuillez actualiser pour réessayer. <a %(a_contact)s>Contactez-nous</a> si le problème persiste pendant plusieurs heures. 🔥 Problème lors du chargement de cette page <li>1. Suivez-nous sur <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, ou <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Parlez des Archives d'Anna sur Twitter, Reddit, Tiktok, Instagram, au café du coin, à la bibliothèque, partout où vous allez ! Nous ne croyons pas en la rétention de l'information — Si notre site venait à être fermé, nous réapparaîtrions ailleurs, puisque notre code et nos données sont "open source".</li><li>3. Si vous le pouvez, pensez à <a href="/donate">faire un don</a>.</li><li>4. Aidez-nous <a href="https://translate.annas-software.org/">à traduire</a> notre site dans d'autres langues.</li><li>5. Si vous faites de la programmation, envisagez de contribuer à notre <a href="https://annas-software.org/">projet "open source"</a>, ou à seeder nos <a href="/datasets">torrents</a>.</li> 10. Créez ou aidez à maintenir la page Wikipédia des Archives d'Anna dans votre langue. 11. Nous cherchons à placer de petites publicités de bon goût. Si vous souhaitez faire de la publicité sur les Archives d'Anna, veuillez nous le faire savoir. 6. Si vous êtes un chercheur en sécurité, nous pouvons utiliser vos compétences tant en attaque qu'en défense. Consultez notre page <a %(a_security)s>Sécurité</a>. 7. Nous recherchons des experts en paiements pour commerçants anonymes. Pouvez-vous nous aider à ajouter des moyens plus pratiques de faire un don ? PayPal, WeChat, cartes-cadeaux. Si vous connaissez quelqu'un, veuillez nous contacter. 8. Nous recherchons toujours plus de capacité de serveur. 9. Vous pouvez nous aider en signalant des problèmes sur les fichiers, en laissant des commentaires et en créant des listes directement sur ce site Web. Vous pouvez également aider en <a %(a_upload)s>téléversant davantage de livres</a>, en résolvant des problèmes de fichiers ou en formatant des livres existants. Pour plus d'informations sur la façon de devenir bénévole, consultez notre page <a %(a_volunteering)s>Bénévolat & Récompenses</a>. Nous croyons fermement à la libre circulation de l'information et à la préservation des connaissances et de la culture. Avec ce moteur de recherche, nous nous appuyons sur les épaules des géants. Nous respectons profondément le travail acharné des personnes qui ont créé les différentes bibliothèques clandestines, et nous espérons que ce moteur de recherche élargira leur portée. Pour rester informé de nos progrès, suivez Anna sur <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ou <a href="https://t.me/annasarchiveorg">Telegram</a>. Pour toutes questions ou suggestions, contactez Anna à l'adresse %(email)s. Identifiant de compte (Account ID) : %(account_id)s Se déconnecter ❌ Une erreur est survenue. Veuillez rafraîchir la page et essayer à nouveau. ✅ Vous êtes désormais déconnecté. Rafraîchissez la page pour vous connecter à nouveau. Téléchargements rapides utilisés (dernières 24 heures) : <strong>%(used)s / %(total)s</strong> Adhésion : <strong>%(tier_name)s</strong> jusqu'au%(until_date)s <a %(a_extend)s>(prolonger)</a> Vous pouvez combiner plusieurs adhésions (les téléchargements rapides par 24 heures seront additionnés). Adhésion : <strong>Aucune</strong> <a %(a_become)s>(devenir membre)</a> Contactez Anna à l'adresse %(email)s si vous souhaitez souscrire à une adhésion de rang supérieur. Profil public : %(profile_link)s Clé secrète (ne pas divulguer !) : %(secret_key)s révéler Rejoignez-nous ici ! Adhérez à un <a %(a_tier)s>rang supérieur</a> pour rejoindre notre groupe. Groupe Telegram exclusif : %(link)s Compte quels téléchargements ? Se connecter Ne perdez pas votre clé ! Clé secrète invalide. Vérifiez votre clé et essayez à nouveau, ou créez un nouveau compte ci-dessous. Clé secrète Entrez votre clé secrète pour vous connecter : Compte enregistré avec un ancien email ? Entrez votre <a %(a_open)s>email ici</a>. Créer un nouveau compte Vous n'avez pas encore de compte ? Vous êtes bien inscrit ! Votre clé secrète est : <span %(span_key)s>%(key)s</span> Conservez précieusement cette clé. En la perdant, vous perdez également l'accès à votre compte. <li %(li_item)s><strong>Favori/Marque-page.</strong> Vous pouvez ajouter cette page à vos favoris/marques-page afin de conserver votre clé.</li><li %(li_item)s><strong>Téléchargement.</strong> Cliquez sur <a %(a_download)s>ce lien</a> pour télécharger votre clé.</li><li %(li_item)s><strong>Gestionnaire de mots de passe.</strong> Utilisez un gestionnaire de mots de passe pour enregistrer la clé lorsque vous la saisissez ci-dessous.</li> Se connecter / S'inscrire Vérification du navigateur Avertissement : le code contient des caractères Unicode incorrects et peut se comporter de manière incorrecte dans diverses situations. Le binaire brut peut être décodé à partir de la représentation base64 dans l'URL. Description Étiquette Préfixe URL pour un code spécifique Site web Codes commençant par « %(prefix_label)s » Veuillez ne pas scraper ces pages. Nous vous recommandons plutôt de <a %(a_import)s>générer</a> ou de <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB, et d'exécuter notre <a %(a_software)s>code source ouvert</a>. Les données brutes peuvent être explorées manuellement via des fichiers JSON tels que <a %(a_json_file)s>celui-ci</a>. Moins de %(count)s archives URL générique Explorateur de codes Index de Explorez les codes avec lesquels les enregistrements sont étiquetés, par préfixe. La colonne « enregistrements » montre le nombre d'enregistrements étiquetés avec des codes ayant le préfixe donné, comme vu dans le moteur de recherche (y compris les enregistrements uniquement de métadonnées). La colonne « codes » montre combien de codes réels ont un préfixe donné. Préfixe de code connu « %(key)s » Plus… Préfixe %(count)s archive correspondant à "%(prefix_label)s" %(count)s archives correspondant à "%(prefix_label)s" codes archives Les « %%s » seront remplacés par la valeur du code Rechercher dans Anna's Archive Codes URL pour un code spécifique : « %(url)s » Cette page peut prendre un certain temps à se générer, c'est pourquoi elle nécessite un captcha Cloudflare. <a %(a_donate)s>Les membres</a> peuvent passer ce captcha. Abus signalé : Meilleure version Souhaitez-vous signaler cet utilisateur pour comportement abusif ou inapproprié ? Problème du fichier : %(file_issue)s commentaire caché Répondre Signaler un abus Vous avez signalé cet utilisateur pour abus. Toute réclamation de droits d'auteur à cette adresse mail sera ignorée ; veuillez utiliser le formulaire. Montrer l'adresse mail Nous accueillons avec plaisir tous vos retours et suggestions ! Cependant, compte tenu de la quantité de spam et de courriels insensés que nous recevons, veuillez cocher les cases ci-contre pour nous confirmer avoir compris les conditions posées pour nous contacter. Toute autre manière de nous contacter pour une réclamation de droits d'auteur conduira à la suppression automatique du message concerné. Pour les réclamation des droits d'auteur et autres DMCA, remplissez <a %(a_copyright)s>ce formulaire</a>. Adresse mail de contact URLs sur Anna's Archive (obligatoire). Une par ligne. Veuillez inclure uniquement les URLs qui décrivent exactement la même édition d'un livre. Si vous souhaitez faire une réclamation pour plusieurs livres ou plusieurs éditions, veuillez soumettre ce formulaire plusieurs fois. Les réclamations regroupant plusieurs livres ou éditions seront rejetées. Adresse (obligatoire) Description claire du document source (obligatoire) E-mail (obligatoire) URLs vers le document source, une par ligne (obligatoire). Veuillez en inclure autant que possible, pour nous aider à vérifier votre réclamation (par exemple, Amazon, WorldCat, Google Books, DOI). ISBNs des documents sources (si applicable). Un par ligne. Veuillez inclure uniquement ceux qui correspondent exactement à l'édition pour laquelle vous signalez une réclamation de droits d'auteur. Votre nom (obligatoire) ❌ Une erreur s'est produite. Veuillez recharger la page et réessayer. ✅ Merci d'avoir soumis votre réclamation de droits d'auteur. Nous l'examinerons dès que possible. Veuillez recharger la page pour en soumettre une autre. <a %(a_openlib)s>Open Library</a> URLs du document source, une par ligne. Veuillez prendre un moment pour rechercher votre document source sur Open Library. Cela nous aidera à vérifier votre réclamation. Numéro de téléphone (obligatoire) Déclaration et signature (obligatoire) Soumettre la réclamation Si vous avez une réclamation de droits d'auteurs (DMCA) ou autre réclamation similaire, veuillez remplir ce formulaire aussi précisément que possible. Si vous rencontrez des problèmes, veuillez nous contacter à notre adresse DMCA dédiée : %(email)s. Notez qu'aucune réclamation envoyée par e-mail à cette adresse ne sera traitée, elle est uniquement destinée aux questions. Veuillez utiliser le formulaire ci-dessous pour soumettre vos réclamations. Formulaire de réclamation des droits d'auteur (DMCA) Exemple d'archive sur Anna’s Archive Torrents par Anna’s Archive Format Conteneurs d'Anna’s Archive Scripts pour importer des métadonnées Si vous êtes intéressé par la réplication de cet ensemble de données à des fins d’<a %(a_archival)s>archivage</a> ou de <a %(a_llm)s>formation LLM</a>, veuillez nous contacter. Dernière mise à jour : %(date)s Site principal %(source)s Documentation des métadonnées (la plupart des champs) Fichiers reproduits par Anna’s Archive : %(count)s (%(percent)s%%) Ressources Total des fichiers : %(count)s Taille totale du fichier : %(size)s Notre article de blog sur ces données <a %(duxiu_link)s>Duxiu</a> est une immense base de données de livres numérisés, créée par le <a %(superstar_link)s>SuperStar Digital Library Group</a>. La plupart sont des livres académiques, numérisés afin de les rendre disponibles numériquement aux universités et bibliothèques. Pour notre public anglophone, <a %(princeton_link)s>Princeton</a> et l'<a %(uw_link)s>Université de Washington</a> offrent de bons aperçus. Il y a aussi un excellent article donnant plus de contexte : <a %(article_link)s>« Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine »</a>. Les livres de Duxiu ont longtemps été piratés sur l'internet chinois. Ils sont généralement vendus pour moins d'un dollar par des revendeurs. Ils sont typiquement distribués en utilisant l'équivalent chinois de Google Drive, qui a souvent été piraté pour permettre plus d'espace de stockage. Quelques détails techniques peuvent être trouvés <a %(link1)s>ici</a> et <a %(link2)s>ici</a>. Bien que les livres aient été distribués de manière semi-publique, il est assez difficile de les obtenir en masse. Nous avions cela en haut de notre liste de tâches, et avons alloué plusieurs mois de travail à temps plein pour cela. Cependant, fin 2023, un bénévole incroyable, étonnant et talentueux nous a contactés, nous disant qu'il avait déjà fait tout ce travail — à grands frais. Ils ont partagé la collection complète avec nous, sans rien attendre en retour, sauf la garantie d'une préservation à long terme. Vraiment remarquable. Plus d'informations de nos bénévoles (notes brutes) : Adapté de notre <a %(a_href)s>article de blog</a>. DuXiu 读秀 %(count)s fichier %(count)s fichiers Cet ensemble de données est étroitement lié à l'<a %(a_datasets_openlib)s>ensemble de données Open Library</a>. Il contient un scrape de toutes les métadonnées et une grande partie des fichiers de la bibliothèque de prêt numérique contrôlé par Internet Archive (Controlled Digital Lending). Les mises à jour sont publiées dans le <a %(a_aac)s>format Conteneur d'Anna's Archive</a>. Ces archives sont directement référencées à partir de l'ensemble de données Open Library, mais contiennent également des archives qui ne sont pas dans Open Library. Nous avons également un certain nombre de fichiers de données extraits par des membres de la communauté au fil des ans. La collection se compose de deux parties. Vous avez besoin des deux parties pour obtenir toutes les données (sauf les torrents remplacés, qui sont barrés sur la page des torrents). Bibliothèque de prêt numérique notre première version, avant que nous ne standardisions sur le <a %(a_aac)s>format des Conteneurs d'Anna’s Archive (AAC)</a>. Contient des métadonnées (en json et xml), des pdfs (des systèmes de prêt numérique acsm et lcpdf), et des vignettes de couverture. nouvelles versions incrémentielles, utilisant le format AAC. Contient uniquement des métadonnées avec des horodatages après le 01-01-2023, puisque le reste est déjà couvert par « IA (Internet Archive) ». Également tous les fichiers pdf, cette fois des systèmes de prêt acsm et « bookreader » (lecteur web de l'Internet Archive). Bien que le nom ne soit pas exactement correct, nous continuons à peupler les fichiers bookreader dans la collection ia2_acsmpdf_files, car ils sont mutuellement exclusifs. Prêt numérique contrôlé par Internet Archive (Controlled Digital Lending) 98%%+ des fichiers sont disponibles à la recherche. Notre mission est d'archiver tous les livres du monde (ainsi que les articles, magazines, etc.), et de les rendre largement accessibles. Nous croyons que tous les livres devraient être dupliqués autant que possible pour assurer leur redondance et leur résilience. C'est pourquoi nous regroupons des fichiers provenant de diverses sources. Certaines sources sont complètement ouvertes et peuvent être dupliquées en masse (comme Sci-Hub). D'autres sont fermées et protectrices, nous essayons donc de les scraper pour « libérer » leurs livres. D'autres encore se situent quelque part entre les deux. Toutes nos données peuvent être <a %(a_torrents)s>téléchargées via torrent</a>, et toutes nos métadonnées peuvent être <a %(a_anna_software)s>générées</a> ou <a %(a_elasticsearch)s>téléchargées</a> sous forme de bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement via des fichiers JSON tels que <a %(a_dbrecord)s>celui-ci</a>. Métadonnées ISBN Site web Dernière mise à jour : %(isbn_country_date)s (%(link)s) Ressources L'Agence internationale de l'ISBN publie régulièrement les plages qu'elle a attribuées aux agences nationales de l'ISBN. À partir de cela, nous pouvons déterminer à quel pays, région ou groupe linguistique appartient cet ISBN. Nous utilisons actuellement ces données indirectement, via la bibliothèque Python <a %(a_isbnlib)s>isbnlib</a>. ISBN Informations sur les pays Il s'agit d'un dump de nombreux appels à isbndb.com en septembre 2022. Nous avons essayé de couvrir toutes les plages ISBN. Il s'agit d'environ 30,9 millions d'enregistrements. Sur leur site web, ils prétendent avoir en réalité 32,6 millions d'enregistrements, donc nous avons peut-être manqué certains, ou <em>ils</em> pourraient faire quelque chose de mal. Les réponses JSON sont pratiquement brutes de leur serveur. Un problème de qualité des données que nous avons remarqué est que pour les numéros ISBN-13 qui commencent par un préfixe différent de « 978- », ils incluent toujours un champ « isbn » qui est simplement le numéro ISBN-13 avec les trois premiers chiffres supprimés (et le chiffre de contrôle recalculé). C'est évidemment incorrect, mais c'est ainsi qu'ils semblent le faire, donc nous ne l'avons pas modifié. Un autre problème potentiel que vous pourriez rencontrer est que le champ « isbn13 » contient des doublons, ce qui empêche son utilisation comme clé primaire dans une base de données. En revanche, la combinaison des champs « isbn13 » et « isbn » semble être unique. Version 1 (2022-10-31) Les torrents de fiction sont en retard (bien que les IDs ~4-6M ne soient pas torrentés car ils se chevauchent avec nos torrents Zlib). Notre article de blog sur la sortie des bandes dessinées et comics Torrents de bandes dessinées et de comics sur Anna's Archive Pour l'historique des différentes bifurcations de Library Genesis, voir la page de <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li contient la plupart du même contenu et des mêmes métadonnées que Libgen.rs, mais possède quelques collections supplémentaires, à savoir des bandes dessinées et des comics, des magazines et des documents références. Il a également intégré <a %(a_scihub)s>Sci-Hub</a> dans ses métadonnées et son moteur de recherche, ce que nous utilisons pour notre base de données. Les métadonnées de cette bibliothèque sont disponibles librement <a %(a_libgen_li)s>sur libgen.li</a>. Cependant, ce serveur est lent et ne prend pas en charge la reprise des connexions interrompues. Les mêmes fichiers sont également disponibles sur <a %(a_ftp)s>un serveur FTP</a>, qui fonctionne mieux. La non-fiction semble également avoir divergé, mais sans nouveaux torrents. Il semble que cela se soit produit depuis début 2022, bien que nous ne l'ayons pas vérifié. Selon l'administrateur de Libgen.li, la collection « fiction_rus » (fiction russe) devrait être couverte par des torrents régulièrement publiés par <a %(a_booktracker)s>booktracker.org</a>, notamment les torrents <a %(a_flibusta)s>flibusta</a> et <a %(a_librusec)s>lib.rus.ec</a> (que nous reproduisons <a %(a_torrents)s>ici</a>, bien que nous n'ayons pas encore établi quels torrents correspondent à quels fichiers). La collection de fiction a ses propres torrents (divergents de <a %(a_href)s>Libgen.rs</a>) à partir de %(start)s. Certaines plages sans torrents (comme les plages de fiction f_3463000 à f_4260000) sont probablement des fichiers de Z-Library (ou d'autres doublons). Malgré tout nous souhaitons faire un peu de déduplication et créer des torrents pour les fichiers uniques de lgli dans ces plages-là. Les statistiques pour toutes les collections peuvent être trouvées <a %(a_href)s>sur le site de libgen</a>. Des torrents sont disponibles pour la plupart des contenus supplémentaires, notamment des torrents pour les bandes dessinées et comics, magazines et des documents standards ont été publiés en collaboration avec Anna's Archive. Notez que les fichiers torrent faisant référence à « libgen.is » sont explicitement des sites miroirs de <a %(a_libgen)s>Libgen.rs</a> (« .is » est un domaine différent utilisé par Libgen.rs). Une ressource utile pour utiliser les métadonnées est <a %(a_href)s>cette page</a>. %(icon)s Leur collection « fiction_rus » (fiction russe) n'a pas de torrents dédiés, mais est couverte par des torrents d'autres sources, et nous maintenons un <a %(fiction_rus)s>site miroir</a>. Torrents de fiction russe sur Anna's Archive Torrents de fiction sur Anna's Archive Forum de discussion Métadonnées Métadonnées via FTP Torrents de magazines sur Anna's Archive Informations sur les champs de métadonnées Reproduction d'autres torrents (et torrents uniques de fiction et de bandes dessinées et comics) Torrents de documents standards sur Anna's Archive Libgen.li Torrents d'Anna's Archive (couvertures de livres) Library Genesis est connu pour d'ores et déjà rendre généreusement ses données disponibles en masse via des torrents. Notre collection Libgen consiste en des données auxiliaires qu'ils ne publient pas directement, en partenariat avec eux. Un grand merci à tous ceux impliqués avec Library Genesis pour leur collaboration avec nous ! Notre blog à propos de la parution des couvertures de livres Cette page concerne la version « .rs ». Elle est connue pour publier régulièrement à la fois ses métadonnées et le contenu complet de son catalogue de livres. Sa collection de livres est divisée entre une partie fiction et une partie non-fiction. Une ressource utile pour utiliser les métadonnées est <a %(a_metadata)s>cette page</a> (elle bloque les plages d'IP, un VPN peut être nécessaire). Depuis mars 2024, de nouveaux torrents sont publiés dans <a %(a_href)s>ce fil de discussion du forum</a> (Les plages d'IP sont bloquées, un VPN pourrait être nécessaire). Torrents de fiction sur Anna's Archive Torrents de fiction de Libgen.rs Forum de discussion de Libgen.rs Métadonnées de Libgen.rs Informations sur les champs de métadonnées de Libgen.rs Torrents de non-fiction de Libgen.rs Torrents de non-fiction sur Anna’s Archive %(example)s pour un livre de fiction. Cette <a %(blog_post)s>première parution</a> est assez petite : environ 300 Go de couvertures de livres de la branche Libgen.rs, à la fois de fiction et de non-fiction. Elles sont organisées de la même manière qu'elles apparaissent sur libgen.rs, par exemple : %(example)s pour un livre de non-fiction. Tout comme avec la collection Z-Library, nous les avons tous mis dans un gros fichier .tar, qui peut être monté en utilisant <a %(a_ratarmount)s>ratarmount</a> si vous souhaitez servir les fichiers directement. Parution 1 (%(date)s) L'histoire rapide des différentes bifurcations de Library Genesis (ou « Libgen ») est qu'au fil du temps, les différentes personnes impliquées dans le projet Library Genesis se sont disputées et ont pris des chemins séparés. Selon ce <a %(a_mhut)s>post sur le forum</a>, Libgen.li était initialement hébergé sur « http://free-books.dontexist.com ». La version « .fun » a été créée par le fondateur original. Elle est en cours de refonte vers une nouvelle version plus distribuée. La version <a %(a_li)s> « .li »</a> possède une vaste collection de bandes dessinées et comics, ainsi que d'autres contenus, qui ne sont pas (encore) disponibles en téléchargement massif via torrents. Elle dispose d'une collection de torrents séparée de livres de fiction, et contient les métadonnées de <a %(a_scihub)s>Sci-Hub</a> dans sa base de données. La version « .rs » a des données très similaires et publie le plus souvent sa collection en torrents groupés. Elle est grossièrement divisée en une section « fiction » et une section « non-fiction ». Initialement sur « http://gen.lib.rus.ec ». <a %(a_zlib)s>Z-Library</a> est en quelque sorte aussi une branche de Library Genesis, bien qu'ils aient utilisé un nom différent pour leur projet. Libgen.rs Nous enrichissons également notre collection avec des sources uniquement de métadonnées, que nous pouvons associer à des fichiers, par exemple en utilisant des numéros ISBN ou d'autres champs. Vous trouverez ci-dessous un aperçu de celles-ci. Encore une fois, certaines de ces sources sont complètement ouvertes, tandis que pour d'autres, nous devons les scraper. Notez que dans la recherche de métadonnées, nous affichons les documents originaux. Nous n'en fusionnons aucun. Sources de métadonnées uniquement Open Library est un projet open source de l'Internet Archive visant à cataloguer tous les livres du monde. Il possède l'une des plus grandes opérations de numérisation de livres au monde et propose de nombreux livres en prêt numérique. Son catalogue de méta-données de livres est librement téléchargeable et est inclus sur les Archives d’Anna (bien qu'il ne soit actuellement pas dans la recherche, sauf si vous recherchez explicitement un ID Open Library). Open Library Doublons exclus Dernière mise à jour Pourcentages du nombre de fichiers %% dupliqué par AA / torrents disponibles Taille Source Vous trouverez ci-dessous un aperçu rapide des sources des fichiers sur Anna’s Archive. Comme les bibliothèques fantômes synchronisent souvent leurs données entre elles, il y a un chevauchement considérable entre elles. C'est pourquoi les chiffres ne correspondent pas au total. Le pourcentage de « sites miroirs et de seeding effectués par Anna's Archive » montre combien de fichiers nous reproduisons nous-mêmes. Nous semons ces fichiers en masse au travers des torrents et les rendons disponibles en téléchargement direct via des sites partenaires. Aperçu Total Torrents sur l’Archive d’Anna Pour en savoir plus sur Sci-Hub, veuillez consulter son <a %(a_scihub)s>site officiel</a>, sa <a %(a_wikipedia)s>page Wikipedia</a> et cette <a %(a_radiolab)s>interview en podcast</a>. Notez que Sci-Hub a été <a %(a_reddit)s>gelé depuis 2021</a>. Il avait été gelé auparavant, mais en 2021, quelques millions d'articles ont été ajoutés. Cependant, un nombre limité d'articles continue d'être ajouté aux collections “scimag” de Libgen, bien que cela ne justifie pas de nouveaux torrents en masse. Nous utilisons les métadonnées de Sci-Hub fournies par <a %(a_libgen_li)s>Libgen.li</a> dans sa collection “scimag”. Nous utilisons également le dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Notez que les torrents “smarch” sont <a %(a_smarch)s>obsolètes</a> et ne sont donc pas inclus dans notre liste de torrents. Torrents sur Libgen.li Torrents sur Libgen.rs Métadonnées et torrents Mises à jour sur Reddit Interview en podcast Page Wikipedia Sci-Hub Sci-Hub : gelé depuis 2021 ; la plupart disponibles via torrents Libgen.li : ajouts mineurs depuis</div> Certaines bibliothèques sources encouragent le partage massif de leurs données via des torrents, tandis que d'autres ne partagent pas facilement leur collection. Dans ce dernier cas, Anna’s Archive essaie de scraper leurs collections et de les rendre disponibles (voir notre page <a %(a_torrents)s>Torrents</a>). Il existe également des situations intermédiaires, par exemple, où les bibliothèques sources sont disposées à partager, mais n'ont pas les ressources pour le faire. Dans ces cas, nous essayons également d'aider. Vous trouverez ci-dessous un aperçu de la manière dont nous interagissons avec les différentes bibliothèques sources. Bibliothèques sources %(icon)s Diverses bases de données de fichiers dispersées sur l'internet chinois ; souvent des bases de données payantes %(icon)s La plupart des fichiers ne sont accessibles qu'avec des comptes premium BaiduYun ; vitesses de téléchargement lentes. %(icon)s Anna’s Archive gère une collection de <a %(duxiu)s>fichiers DuXiu</a> %(icon)s De multiples bases de données de métadonnées dispersée à travers l'internet chinois ; mais souvent payantes %(icon)s Aucun dump de métadonnées n'est facilement accessible pour leur collection entière. %(icon)s Anna’s Archive gère une collection de <a %(duxiu)s>métadonnées DuXiu</a> Fichiers %(icon)s Fichiers seulement disponibles au prêt de manière limitée, avec restrictions d'accès variées %(icon)s Les Archives d'Anna gèrent une collection de <a %(ia)s>fichiers Internet Archive</a> %(icon)s Certaines métadonnées disponibles via <a %(openlib)s>les dépôts de base de données Open Library</a>, mais ceux-ci ne couvrent pas l'entierté de la collection de l'Internet Archive %(icon)s Pas de dépôts de métadonnées facilement accessibles pour la totalité de leur collection %(icon)s Les Archives d'Anna gèrent une collection de <a %(ia)s>métadonnées Internet Archive</a> Dernière mise à jour %(icon)s Anna's Archive et Libgen.li gèrent ensemble des collections de <a %(comics)s>bandes dessinées et comics</a>, <a %(magazines)s>magazines</a>, <a %(standarts)s>documents standards</a>, et <a %(fiction)s>fiction (dérivée de Libgen.rs)</a>. %(icon)s Les torrents de Non-Fiction sont partagés avec Libgen.rs (et miroirés <a %(libgenli)s>ici</a>). %(icon)s <a %(dbdumps)s>Dépôts de base de donnée HTTP</a> trimestriels %(icon)s Torrents automatisés pour la <a %(nonfiction)s>Non-Fiction</a> et la <a %(fiction)s>Fiction</a> %(icon)s Les Archives d'Anna gère une liste de <a %(covers)s>torrents de couvertures de livres</a> %(icon)s <a %(dbdumps)s>Dépôt de base de données HTTP</a> journalier Métadonnées %(icon)s Dumps de base de données <a %(dbdumps)s>mensuels</a> %(icon)s Torrents de données disponibles <a %(scihub1)s>ici</a>, <a %(scihub2)s>ici</a>, et <a %(libgenli)s>ici</a> %(icon)s Certains nouveaux fichiers sont <a %(libgenrs)s>en cours</a> <a %(libgenli)s>d'ajout</a> à la section "scimag" de Libgen’s, mais pas assez pour justifier un nouveau torrent %(icon)s Sci-Hub a gelé le téléversement de nouveaux fichiers depuis 2021. %(icon)s Dépôts de métadonnées disponibles <a %(scihub1)s>ici</a> et <a %(scihub2)s>ici</a>, font aussi partie de la <a %(libgenli)s>base de données Libgen.li</a> (que nous utilisons) Source %(icon)s Diverses sources plus petites ou ponctuelles. Nous encourageons les gens à téléverser d'abord dans d'autres bibliothèques fantômes, mais parfois les gens ont des collections trop grandes pour que d'autres puissent les trier, bien que pas assez grandes pour justifier leur propre catégorie. %(icon)s Pas disponible directement en masse, protégé contre le scraping %(icon)s Anna’s Archive gère une collection de <a %(worldcat)s>métadonnées OCLC (WorldCat)</a> %(icon)s Les Archives d'Anna et Z-Library gèrent une collection de <a %(metadata)s>métadonnées Z-Library</a> et de <a %(files)s>fichiers Z-Library</a> en collaboration Datasets Nous combinons toutes les sources mentionnées ci-dessus en une seule base de données unifiée que nous utilisons pour alimenter ce site web. Cette base de données unifiée n'est pas disponible directement, mais comme Anna’s Archive est entièrement open source, elle peut être assez facilement <a %(a_generated)s>générée</a> ou <a %(a_downloaded)s>téléchargée</a> sous forme de bases de données ElasticSearch ou MariaDB. Les scripts sur cette page téléchargeront automatiquement toutes les métadonnées requises des sources mentionnées ci-dessus. Si vous souhaitez explorer nos données avant d'exécuter ces scripts localement, vous pouvez consulter nos fichiers JSON, qui renvoient à d'autres fichiers JSON. <a %(a_json)s>Ce fichier</a> est un bon point de départ. Base de données unifiée Torrents par l’Archive d’Anna parcourir rechercher Diverses sources plus petites ou ponctuelles. Nous encourageons les gens à téléverser d'abord dans d'autres bibliothèques de l'ombre, mais parfois les gens ont des collections trop grandes pour que d'autres puissent les trier, bien que pas assez grandes pour justifier leur propre catégorie. Aperçu de la <a %(a1)s>page des datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Semble être assez complet. De notre bénévole « cgiym ». D'un <a %(a_href)s><q>torrent ACM Digital Library 2020</q></a>. A un chevauchement assez important avec les collections de papiers existantes, mais très peu de correspondances MD5, nous avons donc décidé de le conserver entièrement. Extraction de <q>iRead eBooks</q> (= phonétiquement <q>ai rit i-books</q>; airitibooks.com), par le bénévole <q>j</q>. Correspond à la metadata <q>airitibooks</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>. D'une collection <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. En partie de la source originale, en partie de the-eye.eu, en partie d'autres miroirs. D'un site web privé de torrents de livres, <a %(a_href)s>Bibliotik</a> (souvent appelé « Bib »), dont les livres ont été regroupés en torrents par nom (A.torrent, B.torrent) et distribués via the-eye.eu. De notre bénévole « bpb9v ». Pour plus d'informations sur <a %(a_href)s>CADAL</a>, voir les notes sur notre <a %(a_duxiu)s>page du dataset DuXiu</a>. Plus de notre bénévole « bpb9v », principalement des fichiers DuXiu, ainsi qu'un dossier « WenQu » et « SuperStar_Journals » (SuperStar est la société derrière DuXiu). De notre bénévole « cgiym », textes chinois de diverses sources (représentés sous forme de sous-répertoires), y compris de <a %(a_href)s>China Machine Press</a> (un grand éditeur chinois). Collections non-chinoises (représentées sous forme de sous-répertoires) de notre bénévole « cgiym ». Extraction de livres sur l'architecture chinoise, par le bénévole <q>cm</q> : <q>Je l'ai obtenue en exploitant une vulnérabilité du réseau à la maison d'édition, mais cette faille a depuis été corrigée</q>. Correspond à la metadata <q>chinese_architecture</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>. Livres de la maison d'édition académique <a %(a_href)s>De Gruyter</a>, collectés à partir de quelques grands torrents. Grattage de <a %(a_href)s>docer.pl</a>, un site polonais de partage de fichiers axé sur les livres et autres œuvres écrites. Gratté fin 2023 par le bénévole « p ». Nous n'avons pas de bonnes métadonnées du site original (même pas les extensions de fichiers), mais nous avons filtré les fichiers ressemblant à des livres et avons souvent pu extraire des métadonnées des fichiers eux-mêmes. DuXiu epubs, directement de DuXiu, collectés par le bénévole « w ». Seuls les livres récents de DuXiu sont disponibles directement via des ebooks, donc la plupart de ceux-ci doivent être récents. Fichiers DuXiu restants du bénévole « m », qui n'étaient pas au format propriétaire PDG de DuXiu (le principal <a %(a_href)s>dataset DuXiu</a>). Collectés à partir de nombreuses sources originales, malheureusement sans conserver ces sources dans le chemin du fichier. <span></span> <span></span> <span></span> Extraction de livres érotiques, par le bénévole <q>do no harm</q>. Correspond à la metadata <q>hentai</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>. <span></span> <span></span> Collection grattée d'un éditeur de manga japonais par le bénévole « t ». <a %(a_href)s>Archives judiciaires sélectionnées de Longquan</a>, fournies par le bénévole « c ». Grattage de <a %(a_href)s>magzdb.org</a>, un allié de Library Genesis (il est lié sur la page d'accueil de libgen.rs) mais qui ne voulait pas fournir ses fichiers directement. Obtenu par le bénévole « p » fin 2023. <span></span> Divers petits téléchargements, trop petits pour être leur propre sous-collection, mais représentés sous forme de répertoires. Ebooks d'AvaxHome, un site russe de partage de fichiers. Archive de journaux et magazines. Correspond à la metadata <q>newsarch_magz</q> dans <a %(a1)s><q>Autres extractions de metadata</q></a>. Extraction du <a %(a1)s>Philosophy Documentation Center</a>. Collection du bénévole « o » qui a collecté des livres polonais directement à partir des sites de sortie originaux (« scène »). Collections combinées de <a %(a_href)s>shuge.org</a> par les bénévoles « cgiym » et « woz9ts ». <span></span> <a %(a_href)s>« Bibliothèque Impériale de Trantor »</a> (nommée d'après la bibliothèque fictive), grattée en 2022 par le bénévole « t ». <span></span> <span></span> <span></span> Sous-sous-collections (représentées sous forme de répertoires) du bénévole « woz9ts » : <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (par <a %(a_sikuquanshu)s>Dizhi(迪志)</a> à Taïwan), mebook (mebook.cc, 我的小书屋, ma petite bibliothèque — woz9ts : « Ce site se concentre principalement sur le partage de fichiers ebook de haute qualité, dont certains sont mis en page par le propriétaire lui-même. Le propriétaire a été <a %(a_arrested)s>arrêté</a> en 2019 et quelqu'un a fait une collection des fichiers qu'il a partagés. »). Fichiers restants de DuXiu du volontaire « woz9ts », qui n’étaient pas au format propriétaire PDG de DuXiu (à convertir encore en PDF). La collection de “téléversements” est divisée en sous-collections plus petites, qui sont indiquées dans les AACIDs et les noms de torrents. Toutes les sous-collections ont d'abord été dédupliquées par rapport à la collection principale, bien que les fichiers JSON de métadonnées “upload_records” contiennent encore beaucoup de références aux fichiers originaux. Les fichiers non livres ont également été retirés de la plupart des sous-collections, et ne sont généralement <em>pas</em> notés dans les “upload_records” JSON. Les sous-collections sont : Notes Sous-collection De nombreuses sous-collections elles-mêmes sont composées de sous-sous-collections (par exemple, de différentes sources originales), qui sont représentées comme des répertoires dans les champs “filepath”. Téléversements vers Anna’s Archive Notre article de blog à propos de cette donnée <a %(a_worldcat)s>WorldCat</a> est une base de données propriétaire de l'organisation à but non lucratif <a %(a_oclc)s>OCLC</a>, qui regoupe des enregistrements de métadonnées provenant de bibliothèques du monde entier. Il s'agit probablement de la plus grande collection de métadonnées de bibliothèques au monde. En octobre 2023, nous avons <a %(a_scrape)s>publié</a> une extraction complète de la base de données OCLC (WorldCat), au format <a %(a_aac)s>Conteneurs d'Anna's Archive</a>. Octobre 2023, première version : OCLC (WorldCat) Torrents par Anna's Archive Exemple d’archive sur Anna's Archive (collection originale) Exemple d’archive sur Anna's Archive (collection « zlib3 ») Torrents par Anna's Archive (métadonnées + contenu) Article de blog sur la Version 1 Article de blog sur la Version 2 Fin 2022, les présumés fondateurs de Z-Library ont été arrêtés, et les domaines ont été saisis par les autorités américaines. Depuis, le site web revient lentement en ligne. On ne sait pas qui le gère actuellement. Mise à jour en février 2023. Z-Library a ses racines dans la communauté <a %(a_href)s>Library Genesis</a>, et a initialement démarré avec leurs données. Depuis, elle s’est considérablement professionnalisée et dispose d’une interface beaucoup plus moderne. Ils sont donc capables de recevoir beaucoup plus de dons, tant monétaires pour continuer à améliorer leur site web, que de dons de nouveaux livres. Ils ont amassé une grande collection en plus de Library Genesis. La collection se compose de trois parties. Les pages de description originales pour les deux premières parties sont conservées ci-dessous. Vous avez besoin des trois parties pour obtenir toutes les données (sauf les torrents remplacés, qui sont barrés sur la page des torrents). %(title)s : notre première version. C’était la toute première version de ce qui était alors appelé la « Pirate Library Mirror » (« pilimi »). %(title)s : seconde version, cette fois incluant tous les fichiers emballés dans des fichiers .tar. %(title)s : nouvelles versions incrémentielles, utilisant le <a %(a_href)s>format Anna’s Archive Containers (AAC)</a>, maintenant publié en collaboration avec l’équipe de Z-Library. Le site miroir initial a été obtenu laborieusement au cours des années 2021 et 2022. À ce stade, il est légèrement obsolète : il reflète l’état de la collection en juin 2021. Nous mettrons cela à jour à l’avenir. Pour l’instant, nous nous concentrons sur la parution de cette première publication. Comme Library Genesis est déjà préservé avec des torrents publics et est inclus dans Z-Library, nous avons effectué une déduplication de base contre Library Genesis en juin 2022. Pour cela, nous avons utilisé des hachages MD5. Il est probable qu'il y ait beaucoup plus de contenu dupliqué dans la bibliothèque, comme plusieurs formats de fichiers pour le même livre. Cela est difficile à détecter avec précision, donc nous ne le faisons pas. Après la déduplication, il nous reste plus de 2 millions de fichiers, totalisant un peu moins de 7 To. La collection se compose de deux parties : un dump MySQL “.sql.gz” des métadonnées, et les 72 fichiers torrent d'environ 50-100 Go chacun. Les métadonnées contiennent les données telles que rapportées par le site Z-Library (titre, auteur, description, type de fichier), ainsi que la taille réelle du fichier et le md5sum que nous avons observé, car parfois ces informations ne concordent pas. Il semble y avoir des plages de fichiers pour lesquelles Z-Library elle-même a des métadonnées incorrectes. Nous avons peut-être aussi téléchargé incorrectement des fichiers dans certains cas isolés, que nous essaierons de détecter et de corriger à l'avenir. Les gros fichiers torrent contiennent les données réelles des livres, avec l'ID Z-Library comme nom de fichier. Les extensions de fichiers peuvent être reconstruites à l'aide du dump des métadonnées. La collection est un mélange de contenu de fiction et de non-fiction (non séparé comme dans Library Genesis). La qualité est également très variable. Cette première version est maintenant entièrement disponible. Notez que les fichiers torrent ne sont disponibles que via notre site miroir Tor. Publication 1 (%(date)s) Il s'agit d'un seul fichier torrent supplémentaire. Il ne contient aucune nouvelle information, mais il contient des données qui peuvent prendre du temps à calculer. Cela le rend pratique à avoir, car télécharger ce torrent est souvent plus rapide que de le calculer à partir de zéro. En particulier, il contient des index SQLite pour les fichiers tar, à utiliser avec <a %(a_href)s>ratarmount</a>. Version 2 addendum (%(date)s) Nous avons récupéré tous les livres ajoutés à Z-Library entre notre dernier site miroir et août 2022. Nous avons également repris et récupéré certains livres que nous avions manqués la première fois. En tout, cette nouvelle collection fait environ 24 To. Encore une fois, cette collection est dédupliquée par rapport à Library Genesis, car il existe déjà des torrents disponibles pour cette collection. Les données sont organisées de manière similaire à la première version. Il y a un dump MySQL “.sql.gz” des métadonnées, qui inclut également toutes les métadonnées de la première version, la remplaçant ainsi. Nous avons également ajouté de nouvelles colonnes : Nous l'avons mentionné la dernière fois, mais juste pour clarifier : “filename” et “md5” sont les propriétés réelles du fichier, tandis que “filename_reported” et “md5_reported” sont ce que nous avons récupéré de Z-Library. Parfois, ces deux informations ne concordent pas, donc nous avons inclus les deux. Pour cette version, nous avons changé le classement en “utf8mb4_unicode_ci”, qui devrait être compatible avec les anciennes versions de MySQL. Les fichiers de données sont similaires à la dernière fois, bien qu'ils soient beaucoup plus gros. Nous n'avons tout simplement pas pris la peine de créer des tonnes de petits fichiers torrent. “pilimi-zlib2-0-14679999-extra.torrent” contient tous les fichiers que nous avons manqués dans la dernière version, tandis que les autres torrents sont tous de nouvelles plages d'ID.  <strong>Mise à jour %(date)s:</strong> Nous avons fait la plupart de nos torrents trop gros, ce qui a causé des difficultés aux clients torrent. Nous les avons supprimés et avons publié de nouveaux torrents. <strong>Mise à jour %(date)s:</strong> Il y avait encore trop de fichiers, donc nous les avons enveloppés dans des fichiers tar et avons publié de nouveaux torrents à nouveau. %(key)s : si ce fichier est déjà dans Library Genesis, dans la collection de non-fiction ou de fiction (correspondance par md5). %(key)s : dans quel torrent se trouve ce fichier. %(key)s : défini lorsque nous n'avons pas pu télécharger le livre. Version 2 (%(date)s) Publications de Zlib (pages de description originales) Domaine Tor Site principal Scrape de Z-Library La collection « chinoise » dans Z-Library semble être la même que notre collection DuXiu, mais avec des MD5s différents. Nous excluons ces fichiers des torrents pour éviter les doublons, mais nous les affichons toujours dans notre index de recherche. Méta-données Vous bénéficiez de %(percentage)s%% de téléchargements rapides en bonus, car l'utilisateur %(profile_link)s vous a parrainé. Cela s'applique à toute la période d'adhésion. Faites un don Rejoindre Sélectionné jusqu'à %(percentage)s%% de réduction Alipay prend en charge les cartes de crédit/débit internationales. Voir <a %(a_alipay)s>ce guide</a> pour plus d’informations. Envoyez-nous des cartes cadeau Amazon.com en utilisant votre carte de crédit/débit. Vous pouvez acheter de la crypto-monnaie en utilisant votre carte de crédit/débit. WeChat (Weixin Pay) accepte les cartes de crédit/débit internationales. Dans l'appli WeChat, allez dans “Me → Services → Wallet → Add a Card”. Si vous ne voyez pas ce menu, activez-le en naviguant dans “Me → Settings → General → Tools → Weixin Pay → Enable”. (utiliser lors de l'envoi d'Ethereum depuis Coinbase) copié ! copier (montant minimal le plus bas) (attention : montant minimum élevé) -%(percentage)s%% 12 mois 1 mois 24 mois 3 mois 48 mois 6 mois 96 mois Sélectionnez la durée de votre abonnement. <div %(div_monthly_cost)s></div><div %(div_after)s>après <span %(span_discount)s></span> réductions</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% pour 12 mois pour 1 mois pour 24 mois pour 3 mois pour 48 mois pour 6 mois pour 96 mois %(monthly_cost)s / mois contactez-nous Serveurs <strong>SFTP</strong> directs Dons à l'échelle d'une entreprise ou échange contre de nouvelles collections (ex : nouvelles numérisations, jeu de données OCR). Accès expert Accès haut débit <strong>illimité</strong> <div %(div_question)s>Puis-je améliorer mon adhésion ou obtenir plusieurs adhésions ?</div> <div %(div_question)s>Puis-je faire un don sans devenir membre ?</div> Bien sûr. Nous acceptons les dons de tout montant à cette adresse Monero (XMR) : %(address)s. <div %(div_question)s>Que signifient les plages par mois ?</div> Vous pouvez atteindre le bas d'une plage en appliquant toutes les réductions, comme choisir une période plus longue qu'un mois. <div %(div_question)s>Les adhésions sont-elles renouvelées automatiquement ?</div> Les adhésions<strong>ne sont pas</strong> renouvelées automatiquement. Vous pouvez choisir librement la durée de votre adhésion. <div %(div_question)s>Comment utilisez-vous les dons ?</div> 100%% sont utilisés pour préserver et rendre accessible les connaissances et la culture du monde. Nos dépenses sont orientées actuellement principalement vers les serveurs, le stockage et la bande passante. Aucune somme n'est reversée personnellement à un membre de l'équipe. <div %(div_question)s>Puis-je faire un don important ?</div> Ce serait formidable ! Pour les dons supérieurs à quelques milliers de dollars, contactez-nous directement à l'adresse suivante %(email)s. <div %(div_question)s>Avez-vous d'autres moyens de paiement ?</div> Actuellement non. Beaucoup de gens ne veulent pas que des archives comme celle-ci existent, nous devons donc être prudents. Si vous pouvez nous aider à mettre en place d'autres méthodes de paiement (plus pratiques) en toute sécurité, contactez nous à l'adresse suivante %(email)s. FAQ Dons Vous avez déjà un <a %(a_donation)s>don en cours</a>. Veuillez le finaliser ou l'annuler avant d'en faire un nouveau. <a %(a_all_donations)s>Voir tous mes dons</a> Pour les dons supérieurs à 5 000 $, veuillez nous contacter directement à %(email)s. Nous acceptons les dons conséquents provenant d'individus fortunés ou d'institutions.  Veuillez noter que bien que les adhésions sur cette page soient « par mois », ce sont des dons uniques (non récurrents). Voir la <a %(faq)s>FAQ sur les dons</a>. Les Archives d'Anna est un projet à but non lucratif, open-source et aux données libres. En faisant un don et en devenant membre, vous soutenez nos opérations et notre développement. À tous nos membres : merci de nous permettre de continuer ! ❤️ Pour plus d'informations, consultez la <a %(a_donate)s>FAQ sur les dons</a>. Pour devenir membre, veuillez <a %(a_login)s>vous connecter ou vous inscrire </a>. Merci de votre soutien ! $%(cost)s / mois Si vous avez fait une erreur au moment du paiement, nous ne pourrons pas vous rembourser, mais nous essaierons de trouver un arrangement. Cherchez la page "Crypto" dans votre application ou site web PayPal. Elle se trouve généralement dans la catégorie"Finances". Accédez à la page "Bitcoin" de votre application ou site web PayPal. Appuyez sur le bouton "Transférer" %(transfer_icon)s, puis "Envoyer". Alipay Alipay 支付宝 / WeChat 微信 Carte Cadeau Amazon Carte cadeau %(amazon)s Carte bancaire Carte bancaire (via l'application) Binance Crédit/débit/Apple/Google (BMC) Cash App Carte de crédit/débit Carte de crédit/débit 2 Carte de crédit/débit (secours) Crypto-monnaie %(bitcoin_icon)s Carte / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (régulier) Pix (Brésil) Revolut (temporairement indisponible) WeChat Sélectionner la cryptomonnaie de votre choix : Faites un don en utilisant une carte cadeau Amazon. <strong>IMPORTANT :</strong> Cette option est pour %(amazon)s. Si vous souhaitez utiliser un autre site Amazon, sélectionnez-le ci-dessus. <strong>IMPORTANT:</strong> Nous utilisons exclusivement le site Amazon.com, et pas les variantes locales. Par exemple, .fr, .de, .co.uk, .ca, ne sont PAS supportés. Veuillez NE PAS écrire votre propre message. Entrez le montant exact : %(amount)s Notez que nous devons arrondir aux valeurs acceptées par nos revendeurs (%(minimum)s minimum). Faites un don en utilisant une carte de crédit/débit, via l'application Alipay (très facile à configurer). Installez l'application Alipay depuis l'<a %(a_app_store)s>Apple App Store</a> ou le <a %(a_play_store)s>Google Play Store</a>. S'inscrire en utilisant un numéro de téléphone. Aucune autre donnée personnelle n'est requise. <span %(style)s>1</span>Installez l'application Alipay Supporté : Visa, MasterCard, JCB, Diners Club et Discover. Voir <a %(a_alipay)s>ce guide</a> pour plus d'informations. <span %(style)s>2</span>Ajoutez une carte bancaire Avec Binance, vous achetez du Bitcoin avec une carte de crédit/débit ou un compte bancaire, pour ensuite nous faire don de cette quantité de Bitcoin. Ainsi, nous pouvons recevoir votre don tout en préservant votre anonymat et votre sécurité. Binance est disponible dans la plupart des pays du monde et fonctionne avec la plupart des banques et cartes de crédit/débit. Pour nous faire don, c'est actuellement la méthode que nous vous recommandons. Merci d'avance pour le temps que vous aurez pris pour apprendre à nous faire don par ce biais, cette méthode nous aide énormément. Pour les cartes de crédit/débit, Apple Pay et Google Pay, nous utilisons “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Dans leur système, un “café” vaut 5 $, donc votre don sera arrondi au multiple de 5 le plus proche. Faites un don via Cash App. Si vous avez Cash App, c'est le moyen le plus facile de nous faire un don ! Notez que pour les transactions inférieures à %(amount)s, Cash App impose %(fee)s de frais. Pour %(amount)s et plus, c'est gratuit ! Faites un don avec une carte de crédit ou de débit. Cette méthode utilise un fournisseur de cryptomonnaie comme convertisseur intermédiaire. Cela peut être un peu déroutant, alors veuillez n'utiliser cette méthode que si les autres ne fonctionnent pas. De plus, elle ne fonctionne pas dans tous les pays. Nous ne pouvons pas accepter directement les cartes de crédit/débit, car les banques ne veulent pas travailler avec nous. ☹ Cependant, il existe plusieurs façons d’utiliser indirectement les cartes de crédit/débit avec méthodes de paiement : Les crypto-monnaies acceptées en don sont les BTC, ETH, XMR et SOL. Utilisez cette option si vous êtes déjà familier des crypto-monnaies. Les crypto-monnaies acceptées en don sont les BTC, ETH, XMR et autres. Service express Crypto Si vous utilisez de la crypto pour la première fois, nous vous suggérons d'utiliser %(options)s pour acheter et faire un don en Bitcoin (la cryptomonnaie originale et la plus utilisée). Notez que pour les petits dons, les frais de carte de crédit peuvent anéantir notre réduction de %(discount)s, aussi, nous recommandons des abonnements plus longs. Faites un don en utilisant une carte de crédit/débit, PayPal ou encore Venmo. Vous pouvez choisir une de ces méthodes sur la page suivante. Google Pay et Apple Pay peuvent aussi marcher. Notez que pour les petits dons, les frais sont élevés, aussi, nous recommandons un abonnement plus long. Pour faire un don par PayPal, nous utiliserons PayPal Crypto, ce qui nous permet de rester anonyme. Nous vous remercions de prendre le temps d'apprendre à utiliser cette méthode, car cela nous aide beaucoup. Faites un don en utilisant PayPal. Faites un don en utilisant votre compte PayPal. Faites un don en utilisant Revolut. Si vous avez Revolut, c’est le moyen le plus simple de faire un don ! Cette méthode de paiement n'accepte pas les montants au-dessus de %(amount)s. Veuillez choisir un abonnement ou un moyen de paiement différent. Cette méthode de paiement requiert un minimum de %(amount)s. Veuillez choisir un abonnement ou un moyen de paiement différent. Binance Coinbase Kraken Sélectionnez un moyen de paiement. “Adopte un torrent” : votre nom d'utilisateur ou message dans le nom d'un fichier torrent <div %(div_months)s> une fois tous les 12 mois d'adhésion</div> Votre nom d'utilisateur ou mention anonyme dans les crédits Accès anticipé aux nouvelles fonctionnalités Compte Telegram exclusif avec les coulisses du projet %(number)s téléchargements rapides par jour si vous faites un don ce mois-ci ! Accès à l' <a %(a_api)s>API JSON</a> Statut Légendaire dans la préservation de la culture et du savoir humain Avantages précédents, avec en plus : Gagnez <strong>%(percentage)s%% de téléchargements bonus</strong> en <a %(a_refer)s>parrainant des amis</a>. Documents SciDB <strong>illimités</strong> sans vérification Lorsque vous posez des questions sur le compte ou les dons, ajoutez votre identifiant de compte, des captures d'écran, reçus, autant d'informations que possible. Nous ne vérifions nos e-mails que toutes les 1-2 semaines, donc ne pas inclure ces informations retardera toute résolution. Pour obtenir encore plus de téléchargements, <a %(a_refer)s>parrainez vos amis</a> ! Nous sommes une petite équipe de bénévoles. Nous pouvons prendre 1 à 2 semaines à vous répondre. Notez que le nom ou la photo du compte peut vous sembler étrange. Ne vous inquiétez pas ! Ces comptes sont gérés par nos partenaires de dons. Nos comptes n'ont pas été piratés. Faire un don <span %(span_cost)s></span> <span %(span_label)s></span> pour 12 mois “%(tier_name)s” pour 1 mois “%(tier_name)s” pour 24 mois “%(tier_name)s” pour 3 mois “%(tier_name)s” pour 48 mois “%(tier_name)s” pour 6 mois “%(tier_name)s” pour 96 mois “%(tier_name)s” Vous pouvez toujours annuler le don durant la phase de paiement. Cliquez sur le bouton "Faire un don" pour confirmer le don. <strong>Remarque importante :</strong>Le prix des cryptomonnaies peut énormément fluctuer, d'une différence atteignant parfois 20 %% en quelques minutes. C'est toujours moins que les frais que nous payons auprès de nombreux services de paiement, qui facturent souvent 50 à 60 %% pour travailler avec une "organisation clandestine" comme la nôtre. <u>Si vous nous envoyez le justificatif de paiement contenant le prix initial que vous avez payé avant ces possibles fluctuations, nous accréditerons votre compte avec l'abonnement choisi</u> (à condition que le justificatif date de quelques heures tout au plus). Nous apprécions sincèrement votre volonté à surmonter ce genre de contraintes afin nous soutenir ! ❤️ ❌ Une erreur est survenue. Veuillez recharger la page et réessayer. <span %(span_circle)s>1</span>Achetez des bitcoins avec Paypal <span %(span_circle)s>2</span>Transférez les bitcoins vers notre adresse ✅ Redirection vers la page de don… Faire un don Merci d'attendre au moins <span %(span_hours)s>24 heures</span> (puis d'actualiser cette page) avant de nous contacter. Si vous désirez faire un don (quel que soit le montant) sans demander d'adhésion, utilisez librement cette adresse Monero (XMR) : %(address)s. Après avoir envoyé votre carte-cadeau, notre système automatisé confirmera sa réception en quelques minutes. Si ça ne marche pas, essayez de la renvoyer (<a %(a_instr)s>instructions</a>). Si ça ne marche toujours pas, veuillez nous envoyer un email et Anna fera une vérification manuelle (ce qui peut prendre plusieurs jours), et prenez bien soin de préciser si vous avez déjà tenté de la renvoyer. Exemple : Veuillez utiliser le <a %(a_form)s>formulaire officiel Amazon.com</a> pour nous envoyer une carte-cadeau de %(amount)s à l'adresse ci-dessous. "À" récipiendaire de l'email dans le formulaire : Carte-cadeau Amazon Nous ne pouvons accepter d'autres moyens d'envoi de carte-cadeau, <strong>envoyez-les exclusivement via le formulaire officiel sur Amazon.com</strong>. Nous ne pourrons pas vous renvoyer votre carte-cadeau si vous ne l'utilisez pas. À utiliser une seule fois. Unique à votre compte, ne le divulguez pas. En attente de la carte-cadeau… (rafraîchissez la page pour vérifier) Ouvrir la <a %(a_href)s>page de don par QR code</a>. Scannez le code QR avec l'application Alipay, ou appuyez sur le bouton pour ouvrir l'application Alipay. Merci de patienter ; la page peut prendre un certain temps à se charger car elle est en Chine. <span %(style)s>3</span>Faire un don (scanner le code QR ou appuyer sur le bouton) Achetez des PYUSD sur PayPal Acheter du Bitcoin (BTC) sur Cash App Achetez-en un peu plus (nous recommandons %(more)s de plus) que le montant dont vous souhaitez faire don (%(amount)s), pour couvrir les frais de transaction. Vous garderez le reste qui n'aura pas servi à la transaction. Allez à la page « Bitcoin » (BTC) dans Cash App. Transférez le Bitcoin à notre adresse Pour les petits dons (moins de 25 $), vous devrez peut-être utiliser Rush ou Priority. Cliquez sur le bouton "Envoyer du bitcoin" pour effectuer un "retrait". Passez des dollars au BTC en appuyant sur l’icône %(icon)s. Entrez le montant en BTC ci-dessous et cliquez sur "Envoyer". Regardez <a %(help_video)s>cette vidéo</a> si vous êtes bloqué. Les services express sont pratiques, mais facturent des frais plus élevés. Vous pouvez les utiliser à la place d'un échange crypto si vous souhaitez faire rapidement un don plus important et que des frais de 5 à 10 $ de nous dérangent pas. Assurez-vous d'envoyer le montant exact en crypto indiqué sur la page de don, et non le montant en $USD. Sinon, les frais seront déduits et nous ne pourrons pas traiter automatiquement votre adhésion. Parfois, la confirmation peut prendre jusqu'à 24 heures, alors assurez-vous de rafraîchir cette page (même si elle a expiré). Instructions pour les cartes de crédit / débit Faites un don via notre page pour cartes de crédit / débit Certaines des étapes font mention de "crypto wallet" ou portefeuille de cryptomonnaies, mais pas d'inquiétude, vous n'avez besoin d'aucune connaissance en cryptomonnaies pour continuer. Instructions pour %(coin_name)s Scannez ce code QR avec votre application Crypto Wallet pour remplir rapidement les détails de paiement Analyser le code QR à payer Nous ne proposons pour l'instant que la version standard des cryptomonnaies et aucun réseau marginal de crypto. Une transaction peut nécessiter une heure avant d'être confirmée, selon la cryptomonnaie utilisée. Faites un don de %(amount)s sur <a %(a_page)s>cette page</a>. Ce don a expiré. Veuillez l'annuler et en créer un nouveau. Si vous avez déjà payé : Oui, j'ai envoyé mon reçu par email Si le taux d'échange de la crypto-monnaie a fluctué durant la transaction, veillez à inclure le reçu contenant le taux initial d'échange. Nous vous remercions de faire l'effort d'utiliser la crypto-monnaie, cela nous aide beaucoup ! ❌ Une erreur est survenue. Veuillez rafraîchir la page et essayer à nouveau. <span %(span_circle)s>%(circle_number)s</span>Envoyez-nous le reçu par email Si vous rencontrez un quelconque problème, veuillez nous contacter à l'adresse %(email)s et inclure autant d'informations que possible (comme des captures d'écran par exemple). ✅ Merci pour votre don ! Anna activera manuellement votre adhésion dans quelques jours. Envoyez un reçu ou une capture d'écran à votre adresse de vérification personnelle : Quand vous nous aurez envoyé votre reçu par email, cliquez sur ce bouton, afin qu'Anna puisse le vérifier manuellement (cela peut prendre quelques jours) : Envoyez un reçu ou une capture d'écran à votre adresse de vérification personnelle. N'UTILISEZ PAS cette adresse email pour votre don via PayPal. Annuler Oui, veuillez annuler Êtes-vous sûr de vouloir annuler ? N'annulez pas si vous avez déjà payé. ❌ Une erreur est survenue. Veuillez recharger la page et réessayer. Faire un nouveau don ✅ Votre don a été annulé. Date : %(date)s Identifiant : %(id)s Commander à nouveau Statut : <span %(span_label)s>%(label)s</span> Total : %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mois pour %(duration)s mois, incluant %(discounts)s%% de remise)</span> Total : %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mois pour %(duration)s mois)</span> 1. Renseignez votre adresse mail. 2. Sélectionnez votre moyen de paiement. 3. Sélectionnez à nouveau votre moyen de paiement. 4. Choisissez un portefeuille "Self-hosted" ("auto-hébergé"). 5. Cliquez sur "I confirm ownership" ("Je confirme son appartenance"). 6. Vous devriez recevoir un reçu par mail. Veuillez nous le transmettre, et nous confirmerons votre don aussi vite que possible. (si vous le souhaitez, vous pouvez annuler et créer un nouveau don) Les instructions de paiement ont expiré. Si vous souhaitez faire un autre don, utilisez le bouton "Commander à nouveau" ci-dessus. Vous avez déjà payé. Si vous souhaitez tout de même consulter les instructions de paiement, cliquez ici : Afficher les anciennes instructions de paiement Si la page de don ne fonctionne pas, essayez une autre connexion internet (par exemple, VPN ou internet mobile). Malheureusement, la page Alipay est souvent accessible uniquement depuis <strong>la Chine continentale</strong>. Vous devrez peut-être désactiver temporairement votre VPN, ou utiliser un VPN vers la Chine continentale (ou Hong Kong, qui fonctionne parfois). <span %(span_circle)s>1</span>Faites un don avec Alipay Faites un don du montant total de %(total)s en utilisant <a %(a_account)s>ce compte Alipay</a> Instructions pour Alipay <span %(span_circle)s>1</span>Faites un transfert vers l'un de nos comptes crypto Faites un don d'un montant total de %(total)s à l'une de ces adresses : Instructions pour les cryptomonnaies Suivez les instructions pour acheter des Bitcoins (BTC). Vous n'avez besoin d'acheter que le montant que vous souhaitez donner, %(total)s. Entrez notre adresse Bitcoin (BTC) en tant que destinataire, et suivez les instructions pour envoyer votre don de %(total)s : <span %(span_circle)s>1</span>Faites un don avec Pix Faites un don total de %(total)s avec <a %(a_account)s>ce compte Pix Instructions pour Pix <span %(span_circle)s>1</span>Faites un don sur WeChat Faites un don du montant total de %(total)s en utilisant <a %(a_account)s>ce compte WeChat</a> Instructions WeChat Utilisez l’un des services express « carte de crédit vers Bitcoin » suivants, qui ne prennent que quelques minutes : Adresse BTC / Bitcoin (portefeuille externe) : Montant BTC / Bitcoin : Remplissez les champs suivants dans le formulaire : Si l'une de ces informations est obsolète, veuillez nous envoyer un email pour nous en informer. Veuillez utiliser ce <span %(underline)s>montant exact</span>. Votre coût total pourrait être plus élevé en raison des frais de carte de crédit. Pour les petits montants, cela peut malheureusement dépasser notre remise. (minimum : %(minimum)s) (minimum : %(minimum)s) (minimum : %(minimum)s) (minimum : %(minimum)s, pas de vérification pour la première transaction) (minimum : %(minimum)s) (minimum : %(minimum)s selon le pays, pas de vérification pour la première transaction) Suivez les instructions afin d'acheter des PYUSD (PayPal US dollars). Achetez un peu plus (nous vous recommandons d'ajouter %(more)s) que le montant dont vous souhaitez faire don (%(amount)s), pour couvrir les frais de transaction. Vous garderez ce qu'il restera. Accédez à la page "PYUSD" de l'application ou site web PayPal. Appuyez sur le bouton "Transférer" %(icon)s, puis sur "Envoyer". Mise à jour du statut Pour réinitialiser le minuteur, créez simplement un nouveau don. Assurez-vous d'utiliser le montant en BTC ci-dessous, <em>PAS</em> en euros ou en dollars, sinon nous ne recevrons pas le montant correct et ne pourrons pas confirmer automatiquement votre adhésion. Acheter du Bitcoin (BTC) sur Revolut Achetez un peu plus (nous recommandons %(more)s de plus) que le montant que vous donnez (%(amount)s), pour couvrir les frais de transaction. Vous garderez tout ce qui reste. Allez à la page "Crypto" dans Revolut pour acheter du Bitcoin (BTC). Transférez le Bitcoin à notre adresse Pour les petits dons (moins de 25 $), vous devrez peut-être utiliser Rush ou Priority. Cliquez sur le bouton « Envoyer du bitcoin » pour effectuer un « retrait ». Passez des euros au BTC en appuyant sur l’icône %(icon)s. Entrez le montant en BTC ci-dessous et cliquez sur « Envoyer ». Regardez <a %(help_video)s>cette vidéo</a> si vous êtes bloqué. Statut : 1 2 Guide pas-à-pas Reportez-vous au pas-à-pas ci-dessous. Ou vous prenez le risque de bloquer l'accès à votre compte ! Si vous ne l'avez pas déjà fait, récupérez votre clé secrète qui vous permet de vous connecter : Merci pour votre don ! Temps restant : Don Transférez %(amount)s vers %(account)s En attente de confirmation (rafraîchissez la page pour vérifier)… En attente du transfert (rafraîchissez la page pour vérifier)… Plus tôt Les téléchargements rapides des dernières 24 heures comptent dans votre limite journalière. Les téléchargements depuis les Serveurs Partenaire Rapides sont indiqués par un %(icon)s. Dernières 18 heures Aucun fichier téléchargé pour le moment. Les fichiers téléchargés ne sont pas affichés publiquement. Toutes les heures sont indiquées au format UTC. Fichiers téléchargés Si vous téléchargez un même fichier avec un téléchargement rapide et un téléchargement lent, il apparaîtra deux fois. Ne vous inquiétez pas trop, beaucoup de gens téléchargent depuis les sites web auxquels nous renvoyons, et il est extrêmement rare d'avoir des problèmes. Cependant, pour rester en sécurité, nous recommandons d'utiliser un VPN (payant), ou <a %(a_tor)s>Tor</a> (gratuit). J'ai téléchargé 1984 de George Orwell, la police va-t-elle venir chez moi ? Vous êtes Anna ! Qui est Anna ? Nous avons une API JSON pour les membres afin d'obtenir un lien de téléchargement rapide : <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation dans le JSON lui-même). Pour d'autres cas d'utilisation, comme parcourir tous les fichiers, créer des recherches personnalisées, etc., nous recommandons de <a %(a_generate)s>générer</a> ou de<a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Les données brutes peuvent être explorées manuellement <a %(a_explore)s>via des fichiers JSON</a>. Notre liste de torrents peut également être téléchargée en tant que <a %(a_torrents)s>JSON</a>. Avez-vous une API ? Nous n'hébergeons aucun fichier protégé par des droits d'auteur ici. Nous sommes un moteur de recherche et, en tant que tel, nous indexons uniquement les métadonnées déjà disponibles publiquement. Lors du téléchargement à partir des sources externes, nous vous suggérons de vérifier les lois applicables dans votre pays pour savoir ce qui est autorisé. Nous ne sommes pas responsables du contenu hébergé par d'autres. Si vous avez des plaintes concernant ce que vous voyez ici, le mieux est de contacter le site web d'origine. Nous intégrons leurs modifications dans notre base de données. Si vous pensez vraiment avoir une plainte DMCA valide à laquelle nous devrions répondre, veuillez remplir le <a %(a_copyright)s>formulaire de réclamation DMCA / Droits d'auteur</a>. Nous prenons vos plaintes au sérieux et nous vous répondrons dès que possible. Comment signaler une violation de droits d'auteur ? Voici quelques livres qui revêtent une importance singulière pour le monde des bibliothèques fantômes et de la préservation numérique : Quels sont vos livres préférés ? Nous tenons également à rappeler à tout le monde que tout notre code et nos données sont entièrement open source. C'est très rare pour des projets comme le nôtre — nous ne connaissons aucun autre projet avec un catalogue aussi vaste qui soit aussi complètement open source. Nous encourageons toute personne qui pense que nous gérons mal notre projet à prendre notre code et nos données et à créer sa propre bibliothèque fantôme ! Nous ne disons pas cela par dépit ou autre — nous pensons sincèrement que ce serait génial, car cela élèverait le niveau pour tout le monde et préserverait mieux l'héritage de l'humanité. Je déteste la façon dont vous gérez ce projet ! On aimerait beaucoup voir des <a %(a_mirrors)s>des bibliothèques-miroir</a> se mettre en place, action pour laquelle nous pourrions apporter notre soutien financier. Comment je peux vous aider ? Effectivement, oui. Notre inspiration pour collecter des métadonnées est Aaron Swartz et son objectif d'« une seule page web pour chaque livre jamais publié », pour lequel il a créé <a %(a_openlib)s>Open Library</a>. Ce projet a accompli beaucoup, mais notre position unique nous permet d'obtenir des métadonnées qu'ils ne peuvent pas. Une autre inspiration était notre désir de savoir <a %(a_blog)s>combien de livres il y a dans le monde</a>, afin que nous puissions calculer combien de livres il nous reste à sauver. Collectez-vous des métadonnées ? Notez que mhut.org bloque certaines plages d'IP, donc un VPN pourrait être nécessaire. <strong>Android :</strong> Cliquez sur le menu à trois points en haut à droite, et sélectionnez « Ajouter à l'écran d'accueil ». <strong>iOS :</strong> Cliquez sur le bouton « Partager » en bas et sélectionnez « Ajouter à l'écran d'accueil ». Nous n'avons pas d'application mobile officielle, mais vous pouvez installer ce site web comme une application. Avez-vous une application mobile ? Veuillez les envoyer à l'<a %(a_archive)s>Internet Archive</a>. Ils les préserveront correctement. Comment puis-je faire un don de livres ou d'autres matériaux physiques ? Comment faire une demande pour un livre manquant ? <a %(a_blog)s>Blog d'Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — mises à jour régulières <a %(a_software)s>Logiciel d'Anna</a> — notre code open source <a %(a_datasets)s>Datasets</a> — à propos des données <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domaines alternatifs Y a-t-il plus de ressources concernant les Archives d'Anna ? <a %(a_translate)s>Traduire les logiciels d'Anna</a> — notre système de traduction <a %(a_wikipedia)s>Wikipédia</a> — plus d'informations nous concernant (veuillez aider à maintenir cette page à jour, ou à en créer une pour votre propre langue !) Sélectionnez les paramètres souhaités, laissez la barre de recherche vide, cliquez sur « Rechercher », puis ajoutez la page aux favoris en utilisant la fonction de favoris de votre navigateur. Comment puis-je sauvegarder mes paramètres de recherche ? Nous invitons les chercheurs en cybersécurité à rechercher des vulnérabilités dans nos systèmes. Nous sommes de grands partisans de la divulgation responsable. Contactez-nous <a %(a_contact)s>ici</a>. Nous ne sommes actuellement pas en mesure d'offrir des primes de bogues, sauf pour les vulnérabilités qui peuvent potentiellement <a %(a_link)s>compromettre notre anonymat</a>. Pour ces dernières, nous offrons des primes allant de 10 000 à 50 000 $. À l'avenir, nous aimerions offrir ces primes pour une sélection de vulnérabilités plus large ! Veuillez noter que les attaques d'ingénierie sociale ne sont pas comprises. Si vous êtes intéressé par la cybersécurité offensive et souhaitez aider à archiver les connaissances et la culture du monde, contactez-nous. Il y a de nombreuses façons de nous aider. Avez-vous un programme de divulgation responsable des failles de sécurité ? Nous n'avons malheureusement pas assez de ressources pour offrir à tout le monde des téléchargements à haute vitesse, bien que nous aimerions le faire. Si un riche bienfaiteur souhaite se manifester et nous fournir cela, ce serait incroyable, mais d'ici-là, nous faisons de notre mieux. Nous sommes un projet à but non lucratif qui peut à peine se maintenir grâce aux dons. C'est pourquoi nous avons mis en place deux systèmes pour les téléchargements gratuits avec nos partenaires : des serveurs partagés avec des téléchargements lents et des serveurs légèrement plus rapides avec une liste d'attente (pour réduire le nombre de personnes téléchargeant en même temps). Nous effectuons également une <a %(a_verification)s>vérification du navigateur</a> pour nos téléchargements lents, car les bots et scrapers en abuseraient, rendant les choses encore plus lentes pour les utilisateurs légitimes. Notez que, lorsque vous utilisez le navigateur Tor, vous devrez peut-être ajuster vos paramètres de sécurité. Avec l'option la plus basse, appelée « Standard », le défi Cloudflare turnstile réussit. Avec les options plus élevées, appelées « Plus sûr » et « Le plus sûr », le défi échoue. Pour les fichiers volumineux, les téléchargements lents peuvent parfois se couper en cours de route. Nous vous recommandons d'utiliser un gestionnaire de téléchargement (tel que JDownloader) pour reprendre automatiquement les téléchargements volumineux. Pourquoi les téléchargements lents sont-ils si lents ? Foire aux questions (FAQ) Utilisez le <a %(a_list)s>générateur de liste de torrents</a> pour générer une liste de torrents qui ont le plus besoin d'être partagés, dans les limites de votre espace de stockage. Oui, consultez la page <a %(a_llm)s>données LLM</a>. La plupart des torrents contiennent les fichiers directement, ce qui signifie que vous pouvez demander à votre client torrent de ne télécharger que les fichiers souhaités. Pour déterminer quels fichiers télécharger, vous pouvez <a %(a_generate)s>générer</a> nos métadonnées, ou <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Malheureusement, un certain nombre de collections de torrents contiennent des fichiers .zip ou .tar à la racine, auquel cas, vous devez télécharger l'intégralité du torrent avant de pouvoir sélectionner des fichiers individuels. Aucun outil facile à utiliser pour filtrer les torrents n'est encore disponible, mais nous accueillons les contributions. (Nous avons <a %(a_ideas)s>quelques idées</a> pour ce dernier cas cependant.) Réponse longue : Réponse courte : pas facilement. Nous essayons de minimiser les doublons et chevauchements entre les torrents de cette liste, mais cela ne peut pas toujours être réalisé et dépend fortement des bibliothèques sources. Pour celles qui publient leurs propres torrents, cela échappe à notre contrôle. Pour les torrents publiés par Anna’s Archive, nous dédupliquons uniquement en fonction du hachage MD5, ce qui signifie que différentes versions du même livre peuvent être dupliquées. Oui. Ce sont en fait des PDF et des EPUB, ils n'ont tout simplement pas d'extension dans beaucoup de nos torrents. Il y a deux endroits où vous pouvez trouver les métadonnées des fichiers torrents, y compris leurs types/extensions : 1. Chaque collection a ses propres métadonnées. Par exemple, les <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> ont une base de métadonnées hébergée sur le site de Libgen.rs. Nous lions généralement les métadonnées pertinentes à partir de la <a %(a_datasets)s>page du jeu de données</a> de chaque collection. 2. Nous recommandons de <a %(a_generate)s>générer</a> ou de <a %(a_download)s>télécharger</a> nos bases de données ElasticSearch et MariaDB. Celles-ci contiennent une entrée pour chaque archive des Archives d'Anna avec les fichiers torrent correspondants (si disponibles), sous "torrent_paths" dans le JSON ElasticSearch. Certains clients torrent ne prennent pas en charge les grandes tailles de pièces, ce que beaucoup de nos torrents ont (pour les plus récents, nous ne le faisons plus — même si c'est valide selon les spécifications !). Essayez donc un autre client si vous rencontrez ce problème, ou adressez-vous aux créateurs de votre client torrent. Je voudrais aider à partager, mais je n'ai pas beaucoup d'espace disque. Les torrents sont trop lents ; puis-je télécharger les données directement depuis le site ? Puis-je télécharger seulement un sous-ensemble des fichiers, comme ceux écrits dans une certaine langue ou concernant un sujet particulier ? Comment gérez-vous les doublons dans les torrents ? Puis-je obtenir la liste des torrents en JSON ? Je ne vois pas de fichiers PDF ou EPUB dans les torrents, seulement des fichiers binaires ? Que dois-je faire ? Pourquoi mon client torrent ne peut-il pas ouvrir certains de vos fichiers torrent / liens magnet ? FAQ sur les torrents Comment puis-je téléverser de nouveaux livres ? Veuillez consulter <a %(a_href)s>cet excellent projet</a>. Avez-vous un moniteur de disponibilité ? C'est quoi les Archives d'Anna ? Devenez membre pour utiliser les téléchargements rapides. Nous acceptons désormais les cartes cadeaux Amazon, les cartes de crédit et de débit, les cryptomonnaies, Alipay et WeChat. Vous êtes arrivé à court de téléchargements rapides pour aujourd'hui. Accès Téléchargements par heure ces 30 derniers jours. Moyenne par heure : %(hourly)s. Moyenne par jour : %(daily)s. Nous travaillons avec des partenaires afin de rendre nos collections accessibles de manière facile et libre à n'importe qui. Nous pensons que tout le monde a le droit à la sagesse collective de l'humanité. Et <a %(a_search)s>pas aux dépens des auteurs</a>. Les jeux de données utilisés par les Archives d'Anna sont totalement ouverts, et peuvent être massivement reproduits à l'aide de torrents. <a %(a_datasets)s>En savoir plus… ⁣</a> Archive à long-terme Base de données complète Rechercher Livres, articles, magazines, bande dessinés, archives de bibliothèques, métadonnées… Tout notre <a %(a_code)s>code</a> et nos <a %(a_datasets)s>données</a> sont complètement open-source. <span %(span_anna)s>Les Archives d'Anna</span> est un projet à but non lucratif ayant deux objectifs : <li><strong>Préservation :</strong> Sauvegarder toutes les connaissances et la culture de l'humanité.</li><li><strong>Accès :</strong> Rendre ces connaissances et cette culture accessibles à n'importe qui dans le monde.</li> Nous fournissons la plus grande collection au monde de données textuelles de haute qualité. <a %(a_llm)s>Apprenez-en plus…</a> Données d'entraînement pour LLM 🪩 Archives-Miroir : appel à bénévoles Si vous êtes responsable d'un organisme de paiement anonyme et à haut risque, contactez-nous. Nous cherchons également des annonceurs à la recherche d'emplacements pour de petites publicités de bon goût. Toutes les recettes servent directement à nos efforts de préservation. Préservation Nous estimons avoir préservé environ <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% des livres dans le monde</a>. Nous préservons les livres, bandes dessinées, magazines et autres, en rassemblant ces documents depuis une variété de <a href="https://en.wikipedia.org/wiki/Shadow_library">bibliothèques fantôme</a>, bibliothèques officielles et autres collections, tout cela au même endroit. Toutes ces données sont pour toujours préservées, car nous facilitons leur duplication massive — à l'aide de torrents — créant ainsi une multitude de copies à travers le monde. Certaines bibliothèques fantôme font déjà ce travail (Sci-Hub, Library Genesis par exemple), tandis que les Archives d'Anna s'occupent de "libérer" celles qui ne proposent pas de redistribution massive (Z-Library par exemple) ou qui ne sont tout simplement pas des bibliothèques fantômes (Internet Achive, DuXiu par exemple). Cette large redistribution, combinée à un code en open-source, rend notre site internet résilient aux tentatives d'extinction, et assure une préservation à long terme des connaissances et de la culture humaine. Apprenez-en plus sur <a href="/datasets">nos jeux de données</a>. Si vous êtes <a %(a_member)s>membre</a>, la vérification navigateur n'est pas nécessaire. 🧬&nbsp;SciDB est une continuation de Sci-Hub. SciDB Ouvrir DOI Sci-Hub a <a %(a_paused)s>mis en pause</a> le téléversement de nouveaux articles. Accès direct à %(count)s articles académiques 🧬&nbsp;SciDB est une continuation de Sci-Hub, avec son interface familière et la visualisation directe des PDF. Entrez votre DOI pour commencer. Nous avons l'intégralité de la collection Sci-Hub, ainsi que de nouveaux articles. La plupart peuvent être consultées directement avec une interface familière similaire à Sci-Hub. Certains peuvent être téléchargés via des sources externes dont nous affichons des liens. Vous pouvez apporter une aide énorme en diffusant nos torrents. <a %(a_torrents)s>En savoir plus…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Nous recherchons des bénévoles En tant que projet à but non lucratif et open source, nous recherchons toujours des personnes pour nous aider. Téléchargements IPFS Liste par %(by)s, créée <span %(span_time)s>%(time)s</span> Sauvegarder ❌ Une erreur est survenue. Veuillez réessayer. ✅ Sauvegardé. Veuillez recharger la page. La liste est vide. modifier Ajoutez ou retirez des éléments de cette liste en accédant à un fichier et en ouvrant l'onglet "Listes". Liste Comment nous pouvons aider Suppression des doublons (déduplication) Extraction de texte et de métadonnées OCR Nous sommes en mesure de fournir un accès à haute vitesse à nos collections complètes, ainsi qu'à des collections non publiées. Il s'agit d'un accès de niveau entreprise que nous pouvons fournir contre des dons de l'ordre de dizaines de milliers de dollars USD. Nous sommes également prêts à échanger cela contre des collections de haute qualité que nous n'avons pas encore. Nous pouvons vous rembourser si vous êtes en mesure de nous fournir un enrichissement de nos données, tel que : Soutenez l'archivage à long terme des connaissances humaines, tout en obtenant de meilleures données pour votre modèle ! <a %(a_contact)s>Contactez-nous</a> pour discuter de la manière dont nous pouvons collaborer. Il est bien compris que les LLM prospèrent grâce à des données de haute qualité. Nous avons la plus grande collection de livres, articles, magazines, etc. au monde, qui sont parmi les sources de texte de la plus haute qualité. Données LLM Échelle et portée uniques Notre collection contient plus de cent millions de fichiers, y compris des revues académiques, des manuels scolaires et des magazines. Nous atteignons cette échelle en combinant de grands dépôts existants. Certaines de nos collections sources sont déjà disponibles en vrac (Sci-Hub, et certaines parties de Libgen). D'autres sources, nous les avons libérées nous-mêmes. <a %(a_datasets)s>Datasets</a> montre un aperçu complet. Notre collection comprend des millions de livres, articles et magazines d'avant l'ère des e-books. De grandes parties de cette collection ont déjà été OCRisées, et ont déjà peu de chevauchement interne. Continuer Si vous avez perdu votre clé, merci de <a %(a_contact)s>nous contacter</a> en nous fournissant le plus d'informations possibles. Vous aurez peut-être à créer un compte temporaire afin de pouvoir nous contacter. Merci de vous <a %(a_account)s>connecter</a> pour consulter cette page.</a> Afin d'empêcher des robots de créer trop de comptes, nous devons d'abord vérifier votre navigateur. Si vous êtes pris dans une boucle infinie, nous vous recommandons d'installer <a %(a_privacypass)s>Privacy Pass</a>. Il peut également être utile de désactiver les bloqueurs de publicités et autres extensions de navigateur. Se connecter / S'inscrire Les Archives d'Anna sont temporairement en maintenance. Veuillez revenir dans une heure. Auteur alternatif Description alternative Édition alternative Extension alternative Nom de fichier alternatif Éditeur alternatif Titre alternatif date de libération publique Lire plus… description Rechercher par numéro CADAL SSNO sur les Archives d'Anna Rechercher par numéro SSID DuXiu sur les Archives d'Anna Rechercher par nombre DXID DuXiu sur les Archives d'Anna Rechercher par ISBN sur les Archives d'Anna Rechercher par numéro OCLC (WorldCat) sur les Archives d'Anna Rechercher par Open Library ID sur les Archives d'Anna Visualiseur en ligne d'Anna's Archive %(count)s pages affectées Après le téléchargement : Une meilleure version de ce fichier est peut-être disponible sur %(link)s Téléchargements torrent en masse collection Utilisez des outils en ligne pour convertir les formats. Outils de conversion recommandés : %(links)s Pour les fichiers volumineux, nous recommandons d'utiliser un gestionnaire de téléchargements pour éviter les interruptions. Gestionnaires de téléchargements recommandés : %(links)s Index des eBooks EBSCOhost (spécialistes uniquement) (cliquez ensuite sur "GET" en haut de la page) (cliquez sur "GET" en haut de la page) Téléchargements externes Il vous en reste %(remaining)s aujourd'hui. Merci d'être un membre ! ❤️ Vous avez épuisé votre quantité de téléchargements rapides pour aujourd'hui. Vous avez téléchargé ce fichier récemment. Les liens restent valides pendant un moment. <strong>🚀 Téléchargements rapides</strong> Devenez <a %(a_membership)s>membre</a> pour soutenir la préservation à long terme des livres, des documents, etc. Pour vous remercier de votre soutien, vous bénéficiez de téléchargements rapides. ❤️ 🚀 Téléchargements rapides 🐢 Téléchargements lents Emprunter à Internet Archive Portail IPFS #%(num)d (vous devrez peut-être essayer plusieurs fois avec IPFS) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction leurs publicités sont connues pour contenir des logiciels malveillants, utilisez donc un bloqueur de publicités ou ne cliquez pas dessus La fonction « Envoyer vers Kindle » d'Amazon La fonction « Envoyer vers Kobo/Kindle » de djazz MagzDB ManualsLib Nexus/STC (Les fichiers Nexus/STC peuvent avoir un téléchargement instable) Aucun téléchargement trouvé. Toutes les options de téléchargement devraient pouvoir être utilisées en toute sécurité. Cela dit, soyez toujours prudent lorsque vous téléchargez des fichiers depuis internet. Par exemple, veillez à maintenir vos appareils à jour. (pas de redirection) Ouvrir dans notre visualiseur (ouvrir dans le visualiseur) Option #%(num)d : %(link)s %(extra)s Retrouver l'archive originelle sur CADAL Rechercher manuellement sur DuXiu Trouver l'archive d'origine sur ISBNdb Rechercher l'archive originale dans WorldCat Trouver l'archive d'origine sur Open Library Rechercher par ISBN sur d'autres bases de données (réservé aux comptes vérifiés "print-disabled") PubMed Vous aurez besoin d'un lecteur d'ebook ou de PDF pour ouvrir le fichier, selon le format du fichier. Lecteurs d'ebooks recommandés : %(links)s Anna’s Archive 🧬 SciDB Sci-Hub : %(doi)s (le DOI associé peut ne pas être disponible dans Sci-Hub) Vous pouvez envoyer des fichiers PDF et EPUB à votre Kindle ou à votre eReader Kobo. Outils recommandés : %(links)s Plus d'informations dans la <a %(a_slow)s>FAQ</a>. Soutenez les auteurs et les bibliothèques Si vous aimez cela et que vous en avez les moyens, envisagez d'acheter l'original ou de soutenir directement les auteurs. Si cela est disponible dans votre bibliothèque locale, envisagez de l'emprunter gratuitement là-bas. Téléchargements depuis un Serveur Partenaire temporairement indisponibles pour ce fichier. torrent Depuis nos partenaires de confiance. Z-Library Z-Library avec TOR (nécessite le Navigateur TOR) afficher les téléchargements externes <span class="font-bold">❌ Ce fichier semble être problématique et a été retiré de la bibliothèque-source. </span> Parfois, c'est à la demande d'un détenteur de droits d'auteur ou encore parce qu'une meilleure alternative est disponible, ou bien car le fichier est corrompu. Le télécharger ne devrait sans doute pas vous poser de problèmes, mais nous préférons vous recommander de chercher une alternative à ce fichier. Plus de détails : Si vous voulez toujours télécharger le fichier, soyez sûr d'utiliser un logiciel de confiance et mis à jour pour l'ouvrir. commentaires dans les métadonnées AA : Rechercher "%(name)s" sur les Archives d'Anna Explorateur de Codes : Voir dans l'Explorateur de Codes “%(name)s” URL : Site internet : Si vous possédez ce document et qu'il n'est pas encore disponible sur les Archives d'Anna, envisagez de <a %(a_request)s>le téléverser</a>. Fichier de prêt numérique (Controlled Digital Lending) de l'Internet Archive “%(id)s” Ceci est l'archive d'un fichier provenant de l'Internet Archive, et non un fichier en téléchargement direct. Vous pouvez essayer d'emprunter ce livre (lien ci-dessous), ou d'utiliser cette URL <a %(a_request)s> lorsque vous faites une demande de fichier</a>. Améliorer les métadonnées Archive de métadonnées du CADAL SSNO %(id)s Ceci est une archive de métadonnées, pas un fichier téléchargeable. Vous pouvez utiliser cette URL <a %(a_request)s>lorsque vous faites une demande de fichier</a>. Archive de métadonnées du SSID DuXiu %(id)s Archive de métadonnées de l'ISBNdb %(id)s Archive de métadonnées MagzDB ID %(id)s Archive de métadonnées Nexus/STC ID %(id)s Archive de métadonnées du numéro d'OCLC (WorldCat) %(id)s Archive de métadonnées de l'Open Library ID %(id)s Fichier Sci-Hub "%(id)s" Aucun résultat “%(md5_input)s” n'a pas été trouvé dans notre base de données. Ajouter un commentaire (%(count)s) Vous pouvez obtenir le md5 à partir de l'URL, par exemple. MD5 d'une meilleure version de ce fichier (si applicable). Remplissez ceci s'il existe un autre fichier qui correspond étroitement à ce fichier (même édition, même extension de fichier si vous pouvez en trouver un), que les gens devraient utiliser à la place de ce fichier. Si vous connaissez une meilleure version de ce fichier en dehors de l’Archive d’Anna, veuillez <a %(a_upload)s>la téléverser</a>. Quelque chose s'est mal passé. Veuillez recharger la page et réessayer. Vous avez bien laissé votre commentaire. Cela peut prendre une minute pour qu'il apparaisse. Veuillez utiliser le <a %(a_copyright)s>formulaire de réclamation DMCA / Droits d'auteur</a>. Décrire le problème (obligatoire) Si ce fichier est de grande qualité, vous pouvez en discuter ici ! Sinon, veuillez utiliser le bouton « Signaler un problème de fichier ». Excellente qualité du fichier (%(count)s) Qualité du fichier Apprenez comment <a %(a_metadata)s>améliorer les métadonnées</a> de ce fichier vous-même. Description du problème Veuillez <a %(a_login)s>vous connecter</a>. J'ai adoré ce livre ! Aidez la communauté en signalant la qualité de ce fichier ! 🙌 Quelque chose s'est mal passé. Veuillez recharger la page et réessayer. Signaler un problème de fichier (%(count)s) Merci d'avoir envoyé votre signalement. Il sera affiché sur cette page et examiné manuellement par Anna (jusqu'à ce que nous ayons un système de modération approprié). Laisser un commentaire Envoyer le signalement Quel est le problème de ce fichier ? Emprunter (%(count)s) Commentaires (%(count)s) Téléchargement (%(count)s) Explorer les métadonnées (%(count)s) Listes (%(count)s) Statistiques (%(count)s) Pour plus d'informations sur ce fichier en particulier, consultez son <a %(a_href)s>fichier JSON</a>. Ceci est un fichier géré par la bibliothèque <a %(a_ia)s>Prêt Numérique Contrôlé de l'IA</a>, et indexé par Anna’s Archive pour la recherche. Pour des informations sur les différents jeux de donnés que nous avons compilés, consultez la <a %(a_datasets)s>page des Jeux de données</a>. Métadonnées de l'archive liée Améliorer les métadonnées sur Open Library Un « fichier MD5 » est un hash calculé à partir du contenu du fichier, et est unique en fonction de ce contenu. Toutes les bibliothèques fantômes que nous avons indexées ici utilisent principalement les MD5 pour identifier les fichiers. Un fichier peut apparaître dans plusieurs bibliothèques fantômes. Pour des informations sur les différents datasets que nous avons compilés, consultez la <a %(a_datasets)s>page des Datasets</a>. Signaler la qualité du fichier Nombre total de téléchargements : %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Méta-données tchèques %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Livres %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Attention : plusieurs archives liées : Lorsque vous regardez un livre sur les Archives d’Anna, vous pouvez voir divers champs : titre, auteur, éditeur, édition, année, description, nom de fichier, et plus encore. Toutes ces informations sont appelées <em>métadonnées</em>. Comme nous combinons des livres de diverses <em>bibliothèques sources</em>, nous affichons les métadonnées disponibles dans ces bibliothèques source. Par exemple, pour un livre que nous avons obtenu de Library Genesis, nous afficherons le titre de la base de données de Library Genesis. Parfois, un livre est présent dans <em>plusieurs</em> bibliothèques sources, qui peuvent avoir des métadonnées différentes. Dans ce cas, nous affichons simplement la version la plus longue de chaque champ, car celle-ci contient probablement les informations les plus utiles ! Nous afficherons toujours les autres champs sous la description, par exemple en tant que « titre alternatif » (mais seulement s'ils sont différents). Nous extrayons également des <em>codes</em> tels que des identifiants et des classificateurs de la bibliothèque source. Les <em>identifiants</em> (ISBN, DOI, Open Library ID, Google Books ID, Amazon ID) représentent de manière unique une édition particulière d'un livre. Les <em>classificateurs</em> (Dewey Decimal (DCC), UDC, LCC, RVK, GOST) regroupent plusieurs livres similaires. Parfois, ces codes sont explicitement liés dans les bibliothèques sources. Par ailleurs, il est possible de les extraire du nom de fichier ou de la description (principalement ISBN et DOI). Nous pouvons utiliser les identifiants pour trouver des entrées dans des <em>collections de métadonnées uniquement</em>, telles que OpenLibrary, ISBNdb, ou WorldCat/OCLC. Il y a un <em>onglet de métadonnées</em> spécifique dans notre moteur de recherche si vous souhaitez parcourir ces collections. Nous utilisons les entrées correspondantes pour remplir les champs de métadonnées manquants (par exemple, si un titre manque), ou par exemple comme « titre alternatif » (s'il existe un titre existant). Pour voir exactement d'où proviennent les métadonnées d'un livre, consultez l'<em>onglet « Détails techniques »</em> sur la page d'un livre. Il contient un lien vers le JSON brut de ce livre, avec des pointeurs vers le JSON brut des entrées originales. Pour plus d'informations, consultez les pages suivantes : <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Recherche (onglet métadonnées)</a>, <a %(a_codes)s>Explorateur de codes</a>, et <a %(a_example)s>Exemple de fichier JSON de métadonnées</a>. Enfin, toutes nos métadonnées peuvent être <a %(a_generated)s>générées</a> ou <a %(a_downloaded)s>téléchargées</a> sous forme de bases de données ElasticSearch et MariaDB. Contexte Vous pouvez aider à la préservation des livres en améliorant les métadonnées ! Tout d'abord, lisez les informations de base concernant les métadonnées sur les Archives d’Anna, puis apprenez comment améliorer les métadonnées en les liant avec Open Library, et gagnez une adhésion gratuite sur les Archives d’Anna. Améliorer les métadonnées Donc, si vous trouver un fichier avec de mauvaises métadonnées, comment devriez-vous le corriger ? Vous pouvez aller à la bibliothèque source et suivre ses procédures pour corriger les métadonnées, mais que faire si un fichier est présent dans plusieurs bibliothèques sources ? Il y a un identifiant qui est traité de manière spéciale sur les Archives d’Anna. <strong>Le champ md5 "annas_archive" d'Open Library remplace toujours toutes les autres métadonnées !</strong> Revenons un peu en arrière et apprenons en un peu plus sur Open Library. Open Library a été fondée en 2006 par Aaron Swartz avec pour objectif « une page web pour chaque livre publié ». C'est une sorte de Wikipédia pour les métadonnées de livres : tout le monde peut l'éditer, elle est sous licence libre, et peut être entièrement téléchargée. C'est une base de données de livres qui correspond le plus avec notre mission — en fait, les Archives d’Anna a été inspirée par la vision et la vie d'Aaron Swartz. Au lieu de réinventer la roue, nous avons décidé de rediriger nos bénévoles vers Open Library. Si vous voyez un livre avec des métadonnées incorrectes, vous pouvez aider de la manière suivante : Notez que cela ne fonctionne que pour les livres, pas pour les articles académiques ou d'autres types de fichiers. Pour d'autres types de fichiers, nous recommandons toujours de trouver la bibliothèque source. Il peut falloir quelques semaines pour que les modifications soient incluses dans les Archives d'Anna, car nous devons télécharger la dernière version de la base de données d'Open Library et régénérer notre index de recherche.  Allez sur le <a %(a_openlib)s>site web d'Open Library</a>. Trouvez l'archive correcte du livre. <strong>ATTENTION :</strong> assurez-vous de sélectionner la <strong>bonne édition</strong>. Dans Open Library, vous avez des « œuvres » et des « éditions ». Une « œuvre » pourrait être « Harry Potter à l'école des sorciers ». Une « édition » pourrait être : La première édition de 1997 publiée par Bloomsbery avec 256 pages. L'édition de poche de 2003 publiée par Raincoast Books avec 223 pages. La traduction polonaise de 2000 « Harry Potter I Kamie Filozoficzn » par Media Rodzina avec 328 pages. Toutes ces éditions ont des ISBN et des contenus différents, alors assurez-vous de sélectionner la bonne ! Modifiez l'entrée (ou créez-la si elle n'existe pas), et ajoutez autant d'informations utiles que possible ! Vous êtes ici de toute façon, alors autant rendre l'archive vraiment incroyable. Sous « ID Numbers », sélectionnez « Anna’s Archive » et ajoutez le MD5 du livre provenant d'Anna’s Archive. C'est la longue chaîne de lettres et de chiffres après « /md5/ » dans l'URL. Essayez de trouver d'autres fichiers dans les Archives d'Anna qui correspondent également à cette archive, et ajoutez-les aussi. À l'avenir, nous pourrons regrouper ceux-ci comme des doublons sur notre page de recherche. Lorsque vous avez terminé, notez l'URL que vous venez de mettre à jour. Une fois que vous avez mis à jour au moins 30 archives avec les MD5 des Archives d'Anna, envoyez-nous un <a %(a_contact)s>email</a> avec la liste. Nous vous offrirons une adhésion gratuite aux Archives d'Anna, afin que vous puissiez plus facilement faire ce travail (et en guise de remerciement pour votre aide). Ces modifications doivent être de bonne qualité et ajouter des informations substantielles, sinon votre demande sera rejetée. Elle le sera également si l'une des modifications est annulée ou corrigée par les modérateurs d'Open Library. Correspondance avec Open Library Si vous vous impliquez de manière significative dans le développement et les opérations de notre travail, nous pouvons discuter du partage d'une plus grande partie des revenus des dons avec vous, pour que vous les déployiez selon les besoins. Nous ne paierons l'hébergement que lorsque vous aurez tout mis en place et démontré que vous êtes capable de maintenir l'archive à jour avec les mises à jour. Cela signifie que vous devrez payer les 1-2 premiers mois de votre poche. Votre temps ne sera pas compensé (et le nôtre non plus), car il s'agit d'un travail purement bénévole. Nous sommes prêts à couvrir les frais d'hébergement et de VPN, initialement jusqu'à 200 $ par mois. Cela suffit pour un serveur de recherche de base et un proxy protégé par le DMCA. Frais d'hébergement Veuillez <strong>ne pas nous contacter</strong> pour demander la permission ou pour des questions de base. Les actions parlent plus fort que les mots ! Toutes les informations sont disponibles, alors allez-y et configurez votre site miroir. N'hésitez pas à poster des tickets ou des demandes de fusion sur notre Gitlab lorsque vous rencontrez des problèmes. Nous pourrions avoir besoin de développer certaines fonctionnalités spécifiques au site miroir avec vous, telles que le rebranding de « l'Archive d'Anna » au nom de votre site, la désactivation (initiale) des comptes utilisateurs, ou le lien vers notre site principal depuis les pages de livres. Une fois que vous avez votre site miroir en fonctionnement, veuillez nous contacter. Nous aimerions examiner votre OpSec, et une fois que ce sera solide, nous créerons un lien vers votre site miroir et commencerons à travailler plus étroitement avec vous. Merci d’avance à tous ceux qui sont prêts à contribuer de cette manière ! Ce n’est pas pour les âmes sensibles, mais cela renforcerait la longévité de la plus grande bibliothèque véritablement ouverte de l’histoire humaine. Commencer Pour augmenter la résilience de l’Archive d’Anna, nous recherchons des volontaires pour gérer des miroirs. Votre version est clairement distinguée comme un site miroir, par exemple « L’Archive de Bob, un site miroir d’Anna’s Archive ». Vous êtes prêt à prendre les risques associés à ce travail, qui sont significatifs. Vous avez une compréhension approfondie de la sécurité opérationnelle requise. Le contenu de <a %(a_shadow)s>ces</a> <a %(a_pirate)s>articles</a> vous est évident. Initialement, nous ne vous donnerons pas accès aux téléchargements de notre serveur partenaire, mais si tout se passe bien, nous pourrons les partager avec vous. Vous exécutez la base de code open source d’Anna’s Archive, et vous mettez régulièrement à jour à la fois le code et les données. Vous êtes prêt à contribuer à notre <a %(a_codebase)s>code source</a> — en collaboration avec notre équipe — pour que cela se réalise. Nous recherchons ceci : Miroirs : appel aux volontaires Faire un autre don. Aucun don pour le moment. <a %(a_donate)s>Faire mon premier don.</a> Les détails des dons ne sont pas affichés publiquement. Mes dons 📡 Pour la reproduction massive de notre collection en archives-miroir, consultez les pages <a %(a_datasets)s>Jeux de données</a> et <a %(a_torrents)s>Torrents</a>. Téléchargements depuis votre adresse IP depuis les dernières 24 heures :%(count)s. 🚀 Pour obtenir des téléchargements plus rapides et passer la vérification navigateur, <a %(a_membership)s>devenez membre</a>. Télécharger depuis le site partenaire N'hésitez pas à continuer de naviguer sur les Archives d'Anna dans un autre onglet en attendant (si votre navigateur prend en charge l'actualisation des onglets en arrière-plan). N'hésitez pas à laisser plusieurs pages de téléchargement se charger en simultané (mais veuillez ne télécharger qu'un seul fichier à la fois par serveur). Une fois que vous obtenez un lien de téléchargement, ce dernier est valable pendant plusieurs heures. Merci d'avoir attendu, cela permet de garder le site accessible gratuitement pour tout le monde ! 😊 <a %(a_main)s>&lt; Tous les liens de téléchargement pour ce fichier</a> ❌ Les téléchargements lents ne sont pas disponibles via les VPN de Cloudflare ou par les adresses IP qu'ils utilisent. ❌ Les téléchargements lents ne sont disponibles que via le site officiel. Visitez %(websites)s. <a %(a_download)s>📚 Télécharger maintenant</a> Afin de donner à chacun l'opportunité de télécharger des fichiers gratuitement, vous devez attendre avant de pouvoir télécharger ce fichier. Veuillez patienter <span %(span_countdown)s>%(wait_seconds)s</span> secondes pour télécharger ce fichier. Attention : de nombreux téléchargements ont été effectués depuis votre adresse IP au cours des dernières 24 heures. Les téléchargements peuvent être plus lents que d'habitude. Si vous utilisez un VPN, une connexion internet partagée ou si votre FAI partage des IP, cet avertissement peut en être la cause. Sauvegarder ❌ Une erreur est survenue. Veuillez réessayer. ✅ Sauvegardé. Veuillez recharger la page. Modifier votre nom public. Votre identifiant (la partie après le “#”) ne peut pas être modifiée. Profil créé <span %(span_time)s>%(time)s</span> modifier Listes Créez une nouvelle liste en accédant à un fichier et en ouvrant l'onglet "Listes". Aucune liste pour le moment Le profil n'a pas été trouvé. Profil Pour le moment, nous ne pouvons pas nous occuper des demandes de livres. Ne nous envoyez pas vos demandes de livres. Merci de faire vos demandes sur les forums de Z-Library ou Libgen. Archive sur les Archives d'Anna DOI : %(doi)s Télécharger SciDB Nexus/STC Aucun aperçu disponible pour le moment. Téléchargez le fichier depuis <a %(a_path)s>les Archives d'Anna</a>. Pour soutenir l'accessibilité et la préservation à long terme des connaissances humaines, devenez <a %(a_donate)s>membre</a>. En bonus, 🧬&nbsp;SciDB se charge plus rapidement pour les membres, sans aucune limite. Ça ne marche pas ? Essayez de <a %(a_refresh)s>rafraîchir</a> la page. Sci-Hub Ajouter un champs de recherche spécifique Rechercher dans les descriptions et commentaires dans les métadonnées Année de publication Avancé Accès Contenu Afficher Liste Tableau Type de fichier Langue Trier par Le plus volumineux Le plus pertinent Le plus récent (taille du fichier) (libéré par AA) (année de parution) Le plus ancien Aléatoire Le moins volumineux Source arpentés et libérés par AA Digital Lending (%(count)s) Articles de journaux (%(count)s) Nous avons trouvé des correspondances dans : %(in)s. Vous pouvez vous référer à l'URL qui s'y trouve lorsque vous <a %(a_request)s>demandez un fichier</a>. Métadonnées (%(count)s) Pour explorer l'index de recherche par codes, utilisez <a %(a_href)s>l'Explorateur de Codes</a>. L'index de recherche est mis à jour mensuellement. Il comprend actuellement les entrées jusqu'au %(last_data_refresh_date)s. Pour plus d'informations techniques, voir %(link_open_tag)s la page concernant les jeux de données utilisés</a>. Exclure Inclure uniquement Non vérifié plus… Suivant … Précédent Cet index de recherhe contient actuellement des métadonnées provenant de la librairie de prêt numérique (Controlled Digital Lending) de l'Internet Archive. <a %(a_datasets)s>Plus d'informations sur notre panel de données</a>. Pour plus de librairies de prêt numérique, consultez <a %(a_wikipedia)s>Wikipédia</a> et le <a %(a_mobileread)s>wiki MobileRead</a>. Pour les DMCA / réclamations de droits d'auteur <a %(a_copyright)s>cliquez ici</a>. Temps de téléchargement Erreur lors de la recherche. Essayez de <a %(a_reload)s>recharger la page</a>. Si le problème persiste, merci de nous envoyer un mail à %(email)s. Téléchargement rapide En fait, tout le monde peut aider à préserver ces fichiers en essaimant notre <a %(a_torrents)s> liste unifiée de torrents</a>. ➡️ Parfois, cela se produit incorrectement lorsque le serveur de recherche est lent. Dans ce cas, <a %(a_attrs)s>recharger</a> peut aider. ❌ Ce fichier pourrait avoir un problème. Vous cherchez des articles scientifiques ? Cet index de recherche contient actuellement des métadonnées provenant de diverses sources. <a %(a_datasets)s>Plus d'informations sur nos jeux de données</a>. Il existe dans le monde beaucoup, beaucoup de sources de métadonnées différentes pour les œuvres écrites. <a %(a_wikipedia)s>Cette page Wikipedia</a> est un bon début, mais is vous connaissez l'existence d'autres bonnes listes, merci de nous en faire part. Concernant les métadonnées, nous affichons celles d'origine. Nous ne fusionnons aucunes métadonnées entre elles. Ce catalogue ouvert de livres, articles, et autres œuvres écrites que nous avons est actuellement le plus exhaustif au monde. Nous reproduisons les catalogues de Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>et d'autres encore</a>. <span %(classname)s>Aucun fichier trouvé.</span> Essayez de réduire le nombre de mots-clés, de filtres ou d'en changer. Résultats %(from)s à %(to)s (%(total)s au total) Si vous découvrez d'autres bibliothèques fantôme ("shadow librairies") dont nous devrions faire la reproduction, ou si vous avez une quelconque question, merci de nous contacter à l'adresse %(email)s. %(num)d correspondances partielles %(num)d+ correspondances partielles Tapez ici votre texte pour effectuer une recherche dans les bibliothèques de prêt numérique. Taper ici votre texte pour effectuer une recherche dans notre catalogue de %(count)s fichiers en téléchargement direct, que nous <a %(a_preserve)s>préservons pour toujours</a>. Tapez ici votre texte pour effectuer une recherche. Tapez ici votre texte pour effectuer une recherche dans notre catalogue de %(count)s articles de journaux et académiques, que nous <a %(a_preserve)s>préservons pour toujours</a>. Tapez ici les métadonnées de bibliothèques à rechercher. Cela peut être utile lorsque vous <a %(a_request)s>faites une demande de fichier</a>. Conseil : utilisez les raccourcis clavier "/" (recherche), "enter" (recherche), "j" (haut), "k" (bas) pour une navigation plus rapide. Ce sont des enregistrements de métadonnées, <span %(classname)s>pas</span> des fichiers téléchargeables. Paramètres de recherche Recherche Prêt numérique Télécharger Articles de journaux Métadonnées Nouvelle recherche %(search_input)s - Rechercher La recherche a pris trop de temps, ce qui risque de vous produire des résultats inexacts. <a %(a_reload)s>Recharger</a> la page peut être parfois utile. La recherche a pris trop de temps, ce qui est courant pour les requêtes larges. Le nombre de filtres peut ne pas être précis. Pour les gros téléversements (plus de 10000 fichiers) qui ne sont pas acceptés par Libgen ou Z-Library, merci de nous contacter à %(a_email)s. Pour Libgen.li, assurez-vous de d'abord vous connecter sur <a %(a_forum)s >leur forum</a> avec le nom d'utilisateur %(username)s et le mot de passe %(password)s, puis retournez sur leur <a %(a_upload_page)s >page de téléversement</a>. Pour le moment, nous vous suggérons de téléverser de nouveaux livres sur les forks de Library Genesis. Voici un <a %(a_guide)s>guide pratique</a> à ce sujet. Remarquez que les deux forks auxquels nous faisons référence sur ce site se basent sur le même système de téléversement. Pour les petits téléversements (jusqu'à 10 000 fichiers), veuillez les téléverser à la fois sur %(first)s et %(second)s. Alternativement, vous pouvez les téléverser sur Z-Library <a %(a_upload)s>ici</a>. Pour téléverser des écrits académiques, veuillez également (en plus de Library Genesis) les téléverser sur <a %(a_stc_nexus)s>STC Nexus</a>. Ils sont la meilleure bibliothèque fantôme pour les nouveaux articles. Nous ne les avons pas encore intégrés, mais nous le ferons à un moment donné. Vous pouvez utiliser leur <a %(a_telegram)s>bot de téléversement sur Telegram</a>, ou contacter l'adresse indiquée dans leur message épinglé si vous avez beaucoup de fichiers à téléverser. <span %(label)s>Travail de bénévolat intensif (primes de 50 USD à 5 000 USD) :</span> si vous pouvez consacrer beaucoup de temps et/ou de ressources à notre mission, nous serions ravis de travailler plus étroitement avec vous. Vous pourrez éventuellement rejoindre l'équipe interne. Bien que notre budget soit serré, nous sommes en mesure d'attribuer des <span %(bold)s>💰 primes monétaires</span> pour le travail le plus intense. <span %(label)s>Travail bénévole léger :</span> si vous ne pouvez consacrer que quelques heures de temps en temps, il existe encore de nombreuses façons de nous aider. Nous récompensons les bénévoles réguliers avec des <span %(bold)s>🤝 adhésions à Anna’s Archive</span>. Anna’s Archive repose sur des bénévoles comme vous. Nous accueillons tous les niveaux d'engagement et recherchons principalement deux types d'aide : Si vous ne pouvez pas offrir de votre temps, vous pouvez toujours nous aider beaucoup en <a %(a_donate)s>faisant un don</a>, en <a %(a_torrents)s>partageant nos torrents</a>, en <a %(a_uploading)s>téléchargeant des livres</a>, ou en <a %(a_help)s>parlant de l'Archive d'Anna à vos amis</a>. <span %(bold)s>Entreprises :</span> nous offrons un accès direct à haute vitesse à nos collections en échange d'un don de niveau entreprise ou en échange de nouvelles collections (par exemple, nouveaux scans, datasets OCRisés, enrichissement de nos données). <a %(a_contact)s>Contactez-nous</a> si cela vous concerne. Voir aussi notre <a %(a_llm)s>page LLM</a>. Primes Nous recherchons toujours des personnes ayant de solides compétences en programmation ou en sécurité offensive pour s'impliquer. Vous pouvez jouer un rôle important dans la préservation de l'héritage de l'humanité. En guise de remerciement, nous offrons des abonnements pour les contributions solides. En guise de plus grand remerciement, nous offrons des primes monétaires pour les tâches particulièrement importantes et difficiles. Cela ne doit pas être considéré comme un remplacement d'emploi, mais c'est une incitation supplémentaire et peut aider à couvrir les coûts engagés. La plupart de notre code est en open source, et nous vous demanderons d'accepter que le vôtre le soit aussi lors de l'attribution de la prime. Il y a quelques exceptions que nous pouvons discuter au cas par cas. Les primes sont attribuées à la première personne qui termine une tâche. N'hésitez pas à commenter un ticket de prime pour informer les autres que vous travaillez sur quelque chose, afin que les autres puissent attendre ou vous contacter pour faire équipe. Mais sachez que les autres sont toujours libres de travailler dessus et d'essayer de vous devancer. Cependant, nous n'attribuons pas de primes pour un travail bâclé. Si deux soumissions de haute qualité sont faites à peu près en même temps (dans un délai d'un jour ou deux), nous pourrions choisir d'attribuer des primes aux deux, à notre discrétion, par exemple 100%% pour la première soumission et 50%% pour la deuxième soumission (soit 150%% au total). Pour les primes les plus importantes (en particulier les primes de scraping), veuillez nous contacter lorsque vous avez complété environ 5%% de la tâche et que vous êtes convaincu que votre méthode pourra s'adapter à l'ensemble de l'objectif. Vous devrez partager votre méthode avec nous afin que nous puissions donner notre avis. De plus, de cette manière, nous pouvons décider quoi faire s'il y a plusieurs personnes proches d'obtenir une prime, comme potentiellement l'attribuer à plusieurs personnes, encourager les gens à faire équipe, etc. AVERTISSEMENT : les tâches à haute prime sont <span %(bold)s>difficiles</span> — il peut être avisé de commencer par des tâches plus faciles. Allez sur notre <a %(a_gitlab)s>liste de problèmes Gitlab</a> et triez par « Priorité du label ». Cela montre à peu près l'ordre des tâches qui nous importent. Les tâches sans primes explicites sont toujours éligibles à une adhésion, en particulier celles marquées «Accepted » et « Anna's favorite ». Il sera peut-être nécessaire de commencer par un « Starter project ». Bénévolat léger Nous avons maintenant également un canal Matrix synchronisé à %(matrix)s. Si vous avez quelques heures à consacrer, vous pouvez aider de plusieurs façons. Assurez-vous de rejoindre le <a %(a_telegram)s>chat des bénévoles sur Telegram</a>. En signe de reconnaissance, nous offrons généralement 6 mois de “Bibliothécaire Chanceux” pour les jalons de base, et plus pour un travail de bénévolat continu. Tous les jalons nécessitent un travail de haute qualité — un travail bâclé nous nuit plus qu'il ne nous aide et nous le rejetterons. Veuillez <a %(a_contact)s>nous envoyer un email</a> lorsque vous atteignez un jalon. %(links)s liens ou captures d'écran des demandes que vous avez satisfaites. Répondre aux demandes de livres (ou d'articles, etc.) sur les forums de Z-Library ou de Library Genesis. Nous n'avons pas notre propre système de demande de livres, mais nous mirroirons ces bibliothèques, donc les améliorer rend également l'Archive d'Anna meilleure. Jalon Tâche Dépend de la tâche. Petites tâches postées sur notre <a %(a_telegram)s>chat des bénévoles sur Telegram</a>. Généralement pour l'adhésion, parfois pour de petites primes. Petites tâches publiées dans notre groupe de discussion de bénévoles. Assurez-vous de laisser un commentaire sur les problèmes que vous résolvez, afin que d'autres ne dupliquent pas votre travail. %(links)s liens des enregistrements que vous avez améliorés. Vous pouvez utiliser la <a %(a_list)s >liste des problèmes de metadata aléatoires</a> comme point de départ. Améliorer les métadonnées en <a %(a_metadata)s>liant</a> avec Open Library. Cela devrait montrer que vous informez quelqu'un de l'Archive d'Anna, et qu'il vous remercie. %(links)s liens ou captures d'écran. Diffuser la nouvelle de l'Archive d'Anna. Par exemple, en recommandant des livres sur AA, en partageant nos articles de blog, ou en dirigeant les gens vers notre site web. Traduire entièrement une langue (si elle n'était pas déjà presque terminée.) <a %(a_translate)s>Traduire</a> le site web. Lien vers l'historique des modifications montrant que vous avez apporté des contributions significatives. Améliorer la page Wikipedia de l'Archive d'Anna dans votre langue. Inclure des informations de la page Wikipedia d'AA dans d'autres langues, ainsi que de notre site web et blog. Ajouter des références à AA sur d'autres pages pertinentes. Bénévolat & Récompenses 