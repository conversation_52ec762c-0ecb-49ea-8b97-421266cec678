#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Cais annilys. Ewch i %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Llyfrgell <PERSON>'r <PERSON>"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " ac "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "a mwy"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Rydym yn adlewyrchu %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Rydym yn sgrapio ac yn agor ffynhonnell %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Mae ein holl god a data yn gwbl agored."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Y llyfrgell fwyaf gwirioneddol agored yn hanes dynoliaeth."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;llyfrau, %(paper_count)s&nbsp;papurau — wedi'u cadw am byth."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Y llyfrgell ffynhonnell agored fwyaf yn y byd. ⭐️&nbsp;Yn adlewyrchu Sci-Hub, Library Genesis, Z-Library, ac yn fwy. 📈&nbsp;%(book_any)s llyfrau, %(journal_article)s papurau, %(book_comic)s comics, %(magazine)s cylchgronau — wedi'u cadw am byth."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Y llyfrgell ffynhonnell agored fwyaf yn y byd.<br>⭐️ Yn adlewyrchu Scihub, Libgen, Zlib, ac yn fwy."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata anghywir (e.e. teitl, disgrifiad, delwedd clawr)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemau lawrlwytho (e.e. methu cysylltu, neges gwall, araf iawn)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Methu agor y ffeil (e.e. ffeil wedi'i llygru, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Ansawdd gwael (e.e. problemau fformatio, ansawdd sgan gwael, tudalennau ar goll)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Sbam / dylid tynnu'r ffeil (e.e. hysbysebu, cynnwys sarhaus)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Hawliad hawlfraint"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Arall"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Lawrlwythiadau bonws"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Llyfrgi Llawenyddus"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Llyfrgellydd Llwyddiannus"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Casglwr Data Disglair"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Archifydd Anhygoel"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s i gyd"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) i gyd"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonws)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "di-dâl"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "wedi talu"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "wedi'i ganslo"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "wedi dod i ben"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "yn aros i Anna gadarnhau"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "annilys"

#, fuzzy
msgid "page.donate.title"
msgstr "Rhoi"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Mae gennych <a %(a_donation)s>rhoddiad presennol</a> ar y gweill. Gorffennwch neu canslwch y rhoddiad hwnnw cyn gwneud rhoddiad newydd."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Gweld fy holl roddiadau</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Mae Archif Anna yn brosiect dielw, cod agored, data agored. Trwy roi a dod yn aelod, rydych yn cefnogi ein gweithrediadau a'n datblygiad. I'n holl aelodau: diolch am ein cadw i fynd! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Am fwy o wybodaeth, edrychwch ar y <a %(a_donate)s>Cwestiynau Cyffredin Rhoddion</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "I gael hyd yn oed mwy o lawrlwythiadau, <a %(a_refer)s>cyfeiriwch eich ffrindiau</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Rydych yn cael %(percentage)s%% lawrlwythiadau cyflym bonws, oherwydd eich bod wedi cael eich cyfeirio gan ddefnyddiwr %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Mae hyn yn berthnasol i'r cyfnod aelodaeth cyfan."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s lawrlwythiadau cyflym y dydd"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "os ydych chi'n cyfrannu'r mis hwn!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mis"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Ymuno"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Wedi'i ddewis"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "hyd at %(percentage)s%% o ostyngiadau"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "papurau SciDB <strong>diderfyn</strong> heb wirio"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>API JSON</a> mynediad"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Ennill <strong>%(percentage)s%% lawrlwythiadau bonws</strong> trwy <a %(a_refer)s>gyfeirio ffrindiau</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Eich enw defnyddiwr neu sôn dienw yn y clod"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Buddion blaenorol, ynghyd â:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Mynediad cynnar i nodweddion newydd"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram unigryw gyda diweddariadau y tu ôl i'r llenni"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Mabwysiadu torrent”: eich enw defnyddiwr neu neges mewn enw ffeil torrent <div %(div_months)s>unwaith bob 12 mis o aelodaeth</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Statws chwedlonol wrth gadw gwybodaeth a diwylliant dynoliaeth"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Mynediad Arbenigol"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "cysylltwch â ni"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Rydym yn dîm bach o wirfoddolwyr. Gall gymryd 1-2 wythnos i ni ymateb."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Mynediad cyflymder uchel</strong> diderfyn"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Gweinyddion <strong>SFTP</strong> uniongyrchol"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Rhodd lefel-fenter neu gyfnewid am gasgliadau newydd (e.e. sganiau newydd, setiau data OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Rydym yn croesawu rhoddion mawr gan unigolion cyfoethog neu sefydliadau. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Ar gyfer rhoddion dros $5000 cysylltwch â ni'n uniongyrchol yn %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Cofiwch, er bod yr aelodaeth ar y dudalen hon yn “y mis”, maent yn roddion un-amser (heb eu hailadrodd). Gweler y <a %(faq)s>Cwestiynau Cyffredin am Roddion</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Os hoffech wneud rhodd (unrhyw swm) heb aelodaeth, mae croeso i chi ddefnyddio'r cyfeiriad Monero (XMR) hwn: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Dewiswch ddull talu os gwelwch yn dda."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(ar gael dros dro)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s cerdyn anrheg"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Cerdyn banc (gan ddefnyddio ap)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Cerdyn credyd/debyd"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (arferol)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Cerdyn / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Credyd/debyd/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Cerdyn banc"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Cerdyn credyd/debyd (wrth gefn)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Cerdyn credyd/debyd 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Gyda crypto gallwch roi gan ddefnyddio BTC, ETH, XMR, a SOL. Defnyddiwch yr opsiwn hwn os ydych eisoes yn gyfarwydd â cryptocurrency."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Gyda crypto gallwch roi gan ddefnyddio BTC, ETH, XMR, a mwy."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Os ydych yn defnyddio crypto am y tro cyntaf, rydym yn awgrymu defnyddio %(options)s i brynu a rhoi Bitcoin (y cryptocurrency gwreiddiol a mwyaf defnyddiedig)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "I roi rhodd gan ddefnyddio PayPal US, rydym yn mynd i ddefnyddio PayPal Crypto, sy'n ein galluogi i aros yn ddienw. Rydym yn gwerthfawrogi eich bod yn cymryd yr amser i ddysgu sut i roi rhodd gan ddefnyddio'r dull hwn, gan ei fod yn ein helpu ni'n fawr."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Rhoi rhodd gan ddefnyddio PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Rhoi rhodd gan ddefnyddio Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Os oes gennych Cash App, dyma'r ffordd hawsaf i roi rhodd!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Nodwch y gall Cash App godi ffi o %(fee)s ar gyfer trafodion o dan %(amount)s. Am %(amount)s neu fwy, mae'n rhad ac am ddim!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Rhoddwch gan ddefnyddio Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Os oes gennych Revolut, dyma'r ffordd hawsaf i roi!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Rhoi rhodd gyda cherdyn credyd neu ddebyd."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Efallai y bydd Google Pay ac Apple Pay hefyd yn gweithio."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Nodwch y gall y ffioedd cerdyn credyd ddileu ein %(discount)s%% disgownt ar gyfer rhoddion bach, felly rydym yn argymell tanysgrifiadau hirach."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Nodwch y gall y ffioedd fod yn uchel ar gyfer rhoddion bach, felly rydym yn argymell tanysgrifiadau hirach."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Gyda Binance, rydych yn prynu Bitcoin gyda cherdyn credyd/debyd neu gyfrif banc, ac yna'n rhoi'r Bitcoin hwnnw i ni. Fel hyn gallwn aros yn ddiogel ac yn ddienw wrth dderbyn eich rhodd."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Mae Binance ar gael ym mron pob gwlad, ac yn cefnogi'r rhan fwyaf o fanciau a chardiau credyd/debyd. Dyma ein prif argymhelliad ar hyn o bryd. Rydym yn gwerthfawrogi eich amser i ddysgu sut i roi gan ddefnyddio'r dull hwn, gan ei fod yn ein helpu ni'n fawr."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Rhoddwch gan ddefnyddio eich cyfrif PayPal arferol."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Rhoddwch gan ddefnyddio cerdyn credyd/debyd, PayPal, neu Venmo. Gallwch ddewis rhwng y rhain ar y dudalen nesaf."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Rhoddwch gan ddefnyddio cerdyn rhodd Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Sylwch fod angen i ni grynhoi i symiau a dderbynnir gan ein hailwerthwyr (lleiafswm %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>PWYSIG:</strong> Dim ond Amazon.com rydym yn ei gefnogi, nid gwefannau Amazon eraill. Er enghraifft, .de, .co.uk, .ca, NID ydynt yn cael eu cefnogi."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>PWYSIG:</strong> Mae'r opsiwn hwn ar gyfer %(amazon)s. Os ydych chi am ddefnyddio gwefan Amazon arall, dewiswch hi uchod."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Mae'r dull hwn yn defnyddio darparwr cryptocurrency fel trosi canolradd. Gall hyn fod ychydig yn ddryslyd, felly defnyddiwch y dull hwn yn unig os nad yw dulliau talu eraill yn gweithio. Hefyd, nid yw'n gweithio ym mhob gwlad."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Rhowch rodd gan ddefnyddio cerdyn credyd/debyd, drwy ap Alipay (hawdd iawn i'w sefydlu)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Gosod ap Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Gosodwch yr ap Alipay o'r <a %(a_app_store)s>Apple App Store</a> neu <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Cofrestrwch gan ddefnyddio eich rhif ffôn."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nid oes angen unrhyw fanylion personol pellach."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Ychwanegu cerdyn banc"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Cefnogir: Visa, MasterCard, JCB, Diners Club a Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Gweler <a %(a_alipay)s>y canllaw hwn</a> am fwy o wybodaeth."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Ni allwn gefnogi cardiau credyd/debyd yn uniongyrchol, oherwydd nid yw banciau eisiau gweithio gyda ni. ☹ Fodd bynnag, mae sawl ffordd i ddefnyddio cardiau credyd/debyd beth bynnag, gan ddefnyddio dulliau talu eraill:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Cerdyn Rhodd Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Anfonwch gardiau rhodd Amazon.com atom gan ddefnyddio eich cerdyn credyd/debyd."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Mae Alipay yn cefnogi cardiau credyd/debyd rhyngwladol. Gweler <a %(a_alipay)s>y canllaw hwn</a> am fwy o wybodaeth."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "Mae WeChat (Weixin Pay) yn cefnogi cardiau credyd/debyd rhyngwladol. Yn yr ap WeChat, ewch i “Me => Services => Wallet => Add a Card”. Os nad ydych yn gweld hynny, ei alluogi gan ddefnyddio “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Gallwch brynu crypto gan ddefnyddio cardiau credyd/debyd."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Gwasanaethau crypto cyflym"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Mae gwasanaethau cyflym yn gyfleus, ond yn codi ffioedd uwch."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Gallwch ddefnyddio hyn yn lle cyfnewidfa crypto os ydych yn chwilio i wneud rhodd fwy yn gyflym ac nid oes gennych broblem â ffi o $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Sicrhewch eich bod yn anfon yr union swm crypto a ddangosir ar y dudalen rhoddion, nid y swm mewn $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Fel arall, bydd y ffi yn cael ei thynnu a ni allwn brosesu eich aelodaeth yn awtomatig."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(isafswm: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(isafswm: %(minimum)s yn dibynnu ar y wlad, dim dilysiad ar gyfer y trafodiad cyntaf)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(isafswm: %(minimum)s, dim dilysiad ar gyfer y trafodiad cyntaf)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(isafswm: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(isafswm: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(isafswm: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Os yw unrhyw un o’r wybodaeth hon yn hen, anfonwch e-bost atom i roi gwybod i ni."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Ar gyfer cardiau credyd, cardiau debyd, Apple Pay, a Google Pay, rydym yn defnyddio “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Yn eu system, mae un “coffi” yn gyfwerth â $5, felly bydd eich rhodd yn cael ei rowndio i'r lluosrif agosaf o 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Dewiswch pa mor hir rydych chi am danysgrifio."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mis"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 mis"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 mis"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 mis"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 mis"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 mis"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 mis"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>ar ôl <span %(span_discount)s></span> gostyngiadau</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Mae'r dull talu hwn yn gofyn am isafswm o %(amount)s. Dewiswch hyd neu ddull talu gwahanol."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Rhoi"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Mae'r dull talu hwn yn caniatáu uchafswm o %(amount)s yn unig. Dewiswch hyd neu ddull talu gwahanol."

#, fuzzy
msgid "page.donate.login2"
msgstr "I ddod yn aelod, os gwelwch yn dda <a %(a_login)s>Mewngofnodwch neu Cofrestrwch</a>. Diolch am eich cefnogaeth!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Dewiswch eich darn arian crypto dewisol:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(swm lleiaf isaf)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(defnyddiwch wrth anfon Ethereum o Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(rhybudd: swm lleiaf uchel)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Cliciwch y botwm rhoi i gadarnhau'r rhodd hon."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Rhoi <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Gallwch ddal i ganslo'r rhodd yn ystod y ddesg dalu."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Yn ailgyfeirio i'r dudalen rhoi…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "am 1 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "am 3 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "am 6 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "am 12 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "am 24 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "am 48 mis"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "am 96 mis"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "am 1 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "am 3 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "am 6 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "am 12 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "am 24 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "am 48 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "am 96 mis “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Rhodd"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Dyddiad: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Cyfanswm: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mis am %(duration)s mis, gan gynnwys %(discounts)s%% gostyngiad)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Cyfanswm: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mis am %(duration)s mis)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Statws: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Dynodydd: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Canslo"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Ydych chi'n siŵr eich bod am ganslo? Peidiwch â chanslo os ydych eisoes wedi talu."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Ydw, canslwch os gwelwch yn dda"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Mae eich rhodd wedi'i chanslo."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Gwnewch rodd newydd"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Aeth rhywbeth o'i le. Llwythwch y dudalen eto a cheisiwch eto."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Ail-archebu"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Rydych eisoes wedi talu. Os ydych am adolygu'r cyfarwyddiadau talu beth bynnag, cliciwch yma:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Dangos cyfarwyddiadau talu hen"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Diolch am eich rhodd!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Os nad ydych eisoes wedi gwneud hynny, ysgrifennwch eich allwedd gyfrinachol i lawr ar gyfer mewngofnodi:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Fel arall efallai y cewch eich cloi allan o'r cyfrif hwn!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Mae'r cyfarwyddiadau talu bellach yn hen. Os hoffech wneud rhodd arall, defnyddiwch y botwm “Ail-archebu” uchod."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nodyn pwysig:</strong> Gall prisiau crypto amrywio'n wyllt, weithiau hyd yn oed cymaint â 20%% mewn ychydig funudau. Mae hyn yn dal i fod yn llai na'r ffioedd a godir gan lawer o ddarparwyr talu, sy'n aml yn codi 50-60%% am weithio gyda \"elusen gysgodol\" fel ni. <u>Os anfonwch y dderbynneb gyda'r pris gwreiddiol a dalwyd gennych, byddwn yn dal i gredydu eich cyfrif ar gyfer yr aelodaeth a ddewiswyd</u> (cyhyd â bod y dderbynneb ddim yn hŷn na ychydig oriau). Rydym yn gwerthfawrogi'n fawr eich bod yn barod i ddelio â phethau fel hyn er mwyn ein cefnogi ni! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Mae'r rhodd hon wedi dod i ben. Canslwch a chreu un newydd os gwelwch yn dda."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Cyfarwyddiadau crypto"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Trosglwyddo i un o'n cyfrifon crypto"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Rhowch y cyfanswm o %(total)s i un o'r cyfeiriadau hyn:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Prynu Bitcoin ar Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Dewch o hyd i'r dudalen \"Crypto\" yn eich ap neu wefan PayPal. Mae hyn fel arfer o dan \"Finances\"."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Dilynwch y cyfarwyddiadau i brynu Bitcoin (BTC). Dim ond y swm rydych am ei roi sydd angen i chi ei brynu, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Trosglwyddo'r Bitcoin i'n cyfeiriad"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Ewch i'r dudalen \"Bitcoin\" yn eich ap neu wefan PayPal. Pwyswch y botwm \"Transfer\" %(transfer_icon)s, ac yna \"Send\"."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Nodwch ein cyfeiriad Bitcoin (BTC) fel y derbynnydd, a dilynwch y cyfarwyddiadau i anfon eich rhodd o %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Cyfarwyddiadau cerdyn credyd / debyd"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Rhoddwch drwy ein tudalen cerdyn credyd / debyd"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Rhoddwch %(amount)s ar <a %(a_page)s>y dudalen hon</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Gweler y canllaw cam wrth gam isod."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Statws:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Yn aros am gadarnhad (adnewyddwch y dudalen i wirio)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Yn aros am drosglwyddiad (adnewyddwch y dudalen i wirio)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Amser sy'n weddill:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(efallai y byddwch am ganslo a chreu rhodd newydd)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "I ailosod y cloc, crëwch rodd newydd yn syml."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Diweddaru statws"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Os byddwch yn wynebu unrhyw broblemau, cysylltwch â ni yn %(email)s a chynnwys cymaint o wybodaeth â phosibl (megis sgrinluniau)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Os ydych eisoes wedi talu:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Weithiau gall cadarnhad gymryd hyd at 24 awr, felly sicrhewch eich bod yn adnewyddu'r dudalen hon (hyd yn oed os yw wedi dod i ben)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Prynwch PYUSD coin ar PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Dilynwch y cyfarwyddiadau i brynu PYUSD coin (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Prynwch ychydig yn fwy (rydym yn argymell %(more)s yn fwy) na'r swm rydych yn ei roi (%(amount)s), i dalu am ffioedd trafodion. Byddwch yn cadw unrhyw beth sy'n weddill."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Ewch i'r dudalen “PYUSD” yn eich ap neu wefan PayPal. Pwyswch y botwm “Trosglwyddo” %(icon)s, ac yna “Anfon”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Trosglwyddo %(amount)s i %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Prynwch Bitcoin (BTC) ar Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Ewch i'r dudalen “Bitcoin” (BTC) yn Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Prynwch ychydig yn fwy (rydym yn argymell %(more)s yn fwy) na'r swm rydych yn ei roi (%(amount)s), i dalu am ffioedd trafodion. Byddwch yn cadw unrhyw beth sy'n weddill."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Trosglwyddwch y Bitcoin i'n cyfeiriad"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Cliciwch y botwm “Anfon bitcoin” i wneud “tynnu'n ôl”. Newidiwch o ddoleri i BTC trwy wasgu'r eicon %(icon)s. Rhowch y swm BTC isod a chliciwch “Anfon”. Gweler <a %(help_video)s>y fideo hwn</a> os ydych yn cael trafferth."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Ar gyfer rhoddion bach (o dan $25), efallai y bydd angen i chi ddefnyddio Rush neu Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Prynwch Bitcoin (BTC) ar Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Ewch i’r dudalen “Crypto” ar Revolut i brynu Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Prynwch ychydig yn fwy (rydym yn argymell %(more)s yn fwy) na’r swm rydych yn ei roi (%(amount)s), i dalu am ffioedd trafodion. Byddwch yn cadw unrhyw beth sy’n weddill."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Trosglwyddwch y Bitcoin i’n cyfeiriad ni"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Cliciwch y botwm “Anfon bitcoin” i wneud “tynnu’n ôl”. Newidiwch o ewros i BTC trwy wasgu’r eicon %(icon)s. Nodwch y swm BTC isod a chliciwch “Anfon”. Gweler <a %(help_video)s>y fideo hwn</a> os ydych yn cael trafferth."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Gwnewch yn siŵr eich bod yn defnyddio'r swm BTC isod, <em>NID</em> ewros neu ddoleri, fel arall ni fyddwn yn derbyn y swm cywir ac ni allwn gadarnhau eich aelodaeth yn awtomatig."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Ar gyfer rhoddion bach (o dan $25) efallai y bydd angen i chi ddefnyddio Rush neu Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Defnyddiwch unrhyw un o’r gwasanaethau “carden gredyd i Bitcoin” cyflym canlynol, sy’n cymryd ychydig funudau yn unig:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Llenwch y manylion canlynol yn y ffurflen:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Swm BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Defnyddiwch y <span %(underline)s>union swm</span> hwn. Efallai y bydd eich cyfanswm cost yn uwch oherwydd ffioedd carden gredyd. Ar gyfer symiau bach gall hyn fod yn fwy na’n disgownt, yn anffodus."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Cyfeiriad BTC / Bitcoin (waled allanol):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "Cyfarwyddiadau %(coin_name)s"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Rydym ond yn cefnogi'r fersiwn safonol o arian crypto, dim rhwydweithiau neu fersiynau ecsotig o arian. Gall gymryd hyd at awr i gadarnhau'r trafodyn, yn dibynnu ar yr arian."

msgid "page.donation.crypto_qr_code_title"
msgstr "Sganiwch god QR i dalu"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Sganiwch y cod QR hwn gyda'ch app Waled Crypto i lenwi'r manylion talu yn gyflym"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Cerdyn rhodd Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Defnyddiwch y <a %(a_form)s>ffurflen swyddogol Amazon.com</a> i anfon cerdyn anrheg o %(amount)s i'r cyfeiriad e-bost isod."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Ni allwn dderbyn dulliau eraill o gardiau anrheg, <strong>dim ond a anfonir yn uniongyrchol o'r ffurflen swyddogol ar Amazon.com</strong>. Ni allwn ddychwelyd eich cerdyn anrheg os nad ydych yn defnyddio'r ffurflen hon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Rhowch y swm union: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Peidiwch â ysgrifennu eich neges eich hun."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Cyfeiriad e-bost y derbynnydd yn y ffurflen:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unigryw i'ch cyfrif, peidiwch â rhannu."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Defnyddiwch unwaith yn unig."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Yn aros am gerdyn anrheg... (adnewyddwch y dudalen i wirio)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Ar ôl anfon eich cerdyn anrheg, bydd ein system awtomatig yn ei gadarnhau o fewn ychydig funudau. Os nad yw hyn yn gweithio, ceisiwch anfon eich cerdyn anrheg eto (<a %(a_instr)s>cyfarwyddiadau</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Os nad yw hynny'n gweithio o hyd, anfonwch e-bost atom ac fe fydd Anna yn ei adolygu â llaw (gall hyn gymryd ychydig ddyddiau), a sicrhewch eich bod yn sôn os ydych wedi ceisio anfon eto."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Enghraifft:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Sylwch y gall enw'r cyfrif neu'r llun edrych yn rhyfedd. Dim angen poeni! Mae'r cyfrifon hyn yn cael eu rheoli gan ein partneriaid rhoddion. Nid yw ein cyfrifon wedi cael eu hacio."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Cyfarwyddiadau Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Rhoi ar Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Rhoddwch y swm cyfan o %(total)s gan ddefnyddio <a %(a_account)s>y cyfrif Alipay hwn</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Os yw'r dudalen rhoddion yn cael ei rhwystro, ceisiwch gysylltiad rhyngrwyd gwahanol (e.e. VPN neu ryngrwyd ffôn)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Yn anffodus, mae'r dudalen Alipay yn aml yn hygyrch yn unig o <strong>dir mawr Tsieina</strong>. Efallai y bydd angen i chi analluogi eich VPN dros dro, neu ddefnyddio VPN i dir mawr Tsieina (neu weithiau mae Hong Kong hefyd yn gweithio)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Gwneud rhodd (sganiwch y cod QR neu pwyswch y botwm)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Agorwch y <a %(a_href)s>tudalen rhodd cod QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Sganiwch y cod QR gyda'r ap Alipay, neu pwyswch y botwm i agor yr ap Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Byddwch yn amyneddgar; efallai y bydd y dudalen yn cymryd peth amser i lwytho gan ei bod yn Tsieina."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Cyfarwyddiadau WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Rhowch ar WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Rhowch y cyfanswm o %(total)s gan ddefnyddio <a %(a_account)s>y cyfrif WeChat hwn</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Cyfarwyddiadau Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Rhowch ar Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Rhowch y cyfanswm o %(total)s gan ddefnyddio <a %(a_account)s>y cyfrif Pix hwn"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Anfonwch y dderbynneb atom"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Anfonwch dderbynneb neu lun sgrin i’ch cyfeiriad dilysu personol. Peidiwch â defnyddio’r cyfeiriad e-bost hwn ar gyfer eich rhodd PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Anfonwch dderbynneb neu lun sgrin i'ch cyfeiriad dilysu personol:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Os yw'r gyfradd gyfnewid crypto wedi amrywio yn ystod y trafodiad, sicrhewch gynnwys y dderbynneb sy'n dangos y gyfradd gyfnewid wreiddiol. Rydym yn gwerthfawrogi'n fawr eich bod wedi cymryd y drafferth i ddefnyddio crypto, mae'n ein helpu ni'n fawr!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Pan fyddwch wedi e-bostio'ch derbynneb, cliciwch y botwm hwn, fel y gall Anna ei adolygu â llaw (gall hyn gymryd ychydig ddyddiau):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Ie, rwyf wedi e-bostio fy nherbynneb"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Diolch am eich rhodd! Bydd Anna yn actifadu eich aelodaeth â llaw o fewn ychydig ddyddiau."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Canllaw cam wrth gam"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Mae rhai o'r camau'n sôn am waledi crypto, ond peidiwch â phoeni, nid oes rhaid i chi ddysgu unrhyw beth am crypto ar gyfer hyn."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Rhowch eich e-bost."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Dewiswch eich dull talu."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Dewiswch eich dull talu eto."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Dewiswch waled “Hunan-letya”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Cliciwch “Rwy'n cadarnhau perchnogaeth”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Dylech dderbyn derbynneb e-bost. Anfonwch hwnnw atom, a byddwn yn cadarnhau eich rhodd cyn gynted â phosibl."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Arhoswch o leiaf <span %(span_hours)s>24 awr</span> (a diweddaru'r dudalen hon) cyn cysylltu â ni."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Os gwnaethoch gamgymeriad yn ystod y taliad, ni allwn wneud ad-daliadau, ond byddwn yn ceisio ei gywiro."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Fy rhoddion"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Nid yw manylion rhoddion yn cael eu dangos yn gyhoeddus."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Dim rhoddion eto. <a %(a_donate)s>Gwneud fy rhodd gyntaf.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Gwneud rhodd arall."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Ffeiliau wedi'u lawrlwytho"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Mae lawrlwythiadau o Weinyddion Partner Cyflym wedi'u marcio gan %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Os lawrlwythoch ffeil gyda lawrlwythiadau cyflym ac araf, bydd yn ymddangos ddwywaith."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Mae lawrlwythiadau cyflym yn y 24 awr diwethaf yn cyfrif tuag at y terfyn dyddiol."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Mae'r holl amseroedd yn UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Nid yw ffeiliau wedi'u lawrlwytho yn cael eu dangos yn gyhoeddus."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Dim ffeiliau wedi'u lawrlwytho eto."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "18 awr diwethaf"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Yn gynharach"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Cyfrif"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Mewngofnodi / Cofrestru"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID Cyfrif: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Proffil cyhoeddus: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Allwedd gyfrinachol (peidiwch â rhannu!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "dangos"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Aelodaeth: <strong>%(tier_name)s</strong> tan %(until_date)s <a %(a_extend)s>(estyn)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Aelodaeth: <strong>Dim</strong> <a %(a_become)s>(dod yn aelod)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Llwythiadau cyflym a ddefnyddiwyd (24 awr diwethaf): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "pa lawrlwythiadau?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grŵp Telegram unigryw: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Ymunwch â ni yma!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Uwchraddio i <a %(a_tier)s>haen uwch</a> i ymuno â'n grŵp."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Cysylltwch ag Anna yn %(email)s os oes gennych ddiddordeb mewn uwchraddio eich aelodaeth i haen uwch."

#, fuzzy
msgid "page.contact.title"
msgstr "E-bost cyswllt"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Gallwch gyfuno sawl aelodaeth (bydd llwythiadau cyflym fesul 24 awr yn cael eu hychwanegu gyda'i gilydd)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Proffil cyhoeddus"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Ffeiliau wedi'u lawrlwytho"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Fy rhoddion"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Allgofnodi"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Rydych bellach wedi allgofnodi. Ail-lwythwch y dudalen i fewngofnodi eto."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Cofrestriad llwyddiannus! Eich allwedd gyfrinachol yw: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Cadwch yr allwedd hon yn ofalus. Os byddwch yn ei cholli, byddwch yn colli mynediad i'ch cyfrif."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Nod Tudalen.</strong> Gallwch nodi'r dudalen hon i adfer eich allwedd.</li><li %(li_item)s><strong>Lawrlwytho.</strong> Cliciwch <a %(a_download)s>y ddolen hon</a> i lawrlwytho eich allwedd.</li><li %(li_item)s><strong>Rheolwr cyfrineiriau.</strong> Defnyddiwch reolwr cyfrineiriau i gadw'r allwedd pan fyddwch yn ei nodi isod.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Nodwch eich allwedd gyfrinachol i fewngofnodi:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Allwedd gyfrinachol"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Mewngofnodi"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Allwedd gyfrinachol annilys. Gwiriwch eich allwedd a cheisiwch eto, neu fel arall cofrestrwch gyfrif newydd isod."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Peidiwch â cholli eich allwedd!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Oes gennych chi ddim cyfrif eto?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Cofrestrwch gyfrif newydd"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Os ydych wedi colli eich allwedd, cysylltwch â ni <a %(a_contact)s>yma</a> a darparwch gymaint o wybodaeth â phosibl."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Efallai y bydd angen i chi greu cyfrif newydd dros dro i gysylltu â ni."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Hen gyfrif seiliedig ar e-bost? Rhowch eich <a %(a_open)s>e-bost yma</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Rhestr"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "golygu"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Cadw"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Wedi'i gadw. Ail-lwythwch y dudalen."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Aeth rhywbeth o'i le. Ceisiwch eto."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Rhestr gan %(by)s, wedi'i chreu <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Mae'r rhestr yn wag."

#, fuzzy
msgid "page.list.new_item"
msgstr "Ychwanegu neu dynnu oddi ar y rhestr hon trwy ddod o hyd i ffeil a chlicio ar y tab “Rhestrau”."

#, fuzzy
msgid "page.profile.title"
msgstr "Proffil"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Heb ddod o hyd i'r proffil."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "golygu"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Newid eich enw arddangos. Ni ellir newid eich adnabodwr (y rhan ar ôl “#”)."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Cadw"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Wedi'i gadw. Ail-lwythwch y dudalen."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Aeth rhywbeth o'i le. Ceisiwch eto."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Proffil wedi'i greu <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Rhestrau"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Dim rhestrau eto"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Creu rhestr newydd trwy ddod o hyd i ffeil a chlicio ar y tab “Rhestrau”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Mae angen diwygio hawlfraint er mwyn diogelwch cenedlaethol"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Mae LLMs Tsieineaidd (gan gynnwys DeepSeek) wedi'u hyfforddi ar fy archif anghyfreithlon o lyfrau a phapurau — y mwyaf yn y byd. Mae angen i'r Gorllewin ailwampio cyfraith hawlfraint fel mater o ddiogelwch cenedlaethol."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "erthyglau cydymaith gan TorrentFreak: <a %(torrentfreak)s>cyntaf</a>, <a %(torrentfreak_2)s>ail</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Nid yn rhy hir yn ôl, roedd “llyfrgelloedd cysgodol” yn marw. Roedd Sci-Hub, yr archif anghyfreithlon enfawr o bapurau academaidd, wedi rhoi'r gorau i dderbyn gweithiau newydd, oherwydd achosion cyfreithiol. Gwelodd “Z-Library”, y llyfrgell anghyfreithlon fwyaf o lyfrau, fod ei chrewyr honedig wedi'u harestio ar gyhuddiadau hawlfraint troseddol. Fe wnaethant lwyddo i ddianc o'u harest, ond nid yw eu llyfrgell o dan fygythiad llai."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Pan wynebodd Z-Library gau, roeddwn eisoes wedi gwneud copi wrth gefn o'i llyfrgell gyfan ac yn chwilio am blatfform i'w lletya. Dyna oedd fy ysgogiad i ddechrau Archif Anna: parhad o'r genhadaeth y tu ôl i'r mentrau cynharach hynny. Ers hynny rydym wedi tyfu i fod y llyfrgell gysgodol fwyaf yn y byd, gan letya mwy na 140 miliwn o destunau hawlfraint ar draws nifer o fformatau — llyfrau, papurau academaidd, cylchgronau, papurau newydd, ac yn y blaen."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mae fy nhîm a minnau yn ideolegwyr. Credwn fod cadw a lletya'r ffeiliau hyn yn foesol gywir. Mae llyfrgelloedd ledled y byd yn gweld toriadau cyllid, ac ni allwn ymddiried treftadaeth dynoliaeth i gorfforaethau chwaith."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Yna daeth AI. Cysylltodd bron pob cwmni mawr sy'n adeiladu LLMs â ni i hyfforddi ar ein data. Ailystyriodd y rhan fwyaf (ond nid pob un!) o gwmnïau yn yr UD unwaith y sylweddolon nhw natur anghyfreithlon ein gwaith. I'r gwrthwyneb, mae cwmnïau Tsieineaidd wedi cofleidio ein casgliad yn frwdfrydig, yn amlwg heb eu poeni gan ei gyfreithlondeb. Mae hyn yn nodedig o ystyried rôl Tsieina fel llofnodwr i bron pob prif gytundeb hawlfraint rhyngwladol."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Rydym wedi rhoi mynediad cyflym iawn i tua 30 o gwmnïau. Mae'r rhan fwyaf ohonynt yn gwmnïau LLM, ac mae rhai yn ddelwyr data, a fydd yn ailwerthu ein casgliad. Mae'r rhan fwyaf ohonynt yn Tsieineaidd, er ein bod hefyd wedi gweithio gyda chwmnïau o'r UD, Ewrop, Rwsia, De Korea, a Japan. Cyfaddefodd DeepSeek <a %(arxiv)s></a> bod fersiwn cynharach wedi'i hyfforddi ar ran o'n casgliad, er eu bod yn dawel iawn am eu model diweddaraf (mae'n debyg ei fod hefyd wedi'i hyfforddi ar ein data serch hynny)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Os yw'r Gorllewin eisiau aros ar y blaen yn y ras o LLMs, ac yn y pen draw, AGI, mae angen iddo ailystyried ei safbwynt ar hawlfraint, ac yn fuan. P'un a ydych yn cytuno â ni ai peidio ar ein hachos moesol, mae hyn bellach yn dod yn achos economaidd, a hyd yn oed o ddiogelwch cenedlaethol. Mae pob bloc pŵer yn adeiladu uwch-wyddonwyr artiffisial, uwch-hacwyr, ac uwch-filwriaethau. Mae rhyddid gwybodaeth yn dod yn fater o oroesi i'r gwledydd hyn — hyd yn oed yn fater o ddiogelwch cenedlaethol."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Mae ein tîm o bob cwr o'r byd, ac nid oes gennym aliniad penodol. Ond byddem yn annog gwledydd â chyfreithiau hawlfraint cryf i ddefnyddio'r bygythiad bodolaethol hwn i'w diwygio. Felly beth i'w wneud?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Mae ein hargymhelliad cyntaf yn syml: byrhau'r tymor hawlfraint. Yn yr UD, rhoddir hawlfraint am 70 mlynedd ar ôl marwolaeth yr awdur. Mae hyn yn afresymol. Gallwn ddod â hyn i gyd-fynd â phatentau, sy'n cael eu rhoi am 20 mlynedd ar ôl ffeilio. Dylai hyn fod yn fwy na digon o amser i awduron llyfrau, papurau, cerddoriaeth, celf, a gweithiau creadigol eraill, gael eu digolledu'n llawn am eu hymdrechion (gan gynnwys prosiectau tymor hwy fel addasiadau ffilm)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Yna, o leiaf, dylai llunwyr polisi gynnwys eithriadau ar gyfer cadw a lledaenu testunau ar raddfa fawr. Os yw colli refeniw gan gwsmeriaid unigol yn brif bryder, gallai dosbarthiad ar lefel bersonol aros yn waharddedig. Yn ei dro, byddai'r rhai sy'n gallu rheoli cronfeydd data enfawr — cwmnïau sy'n hyfforddi LLMs, ynghyd â llyfrgelloedd ac archifau eraill — yn cael eu cynnwys gan yr eithriadau hyn."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Mae rhai gwledydd eisoes yn gwneud fersiwn o hyn. Adroddodd TorrentFreak <a %(torrentfreak)s> </a> bod Tsieina a Japan wedi cyflwyno eithriadau AI i'w cyfreithiau hawlfraint. Nid yw'n glir i ni sut mae hyn yn rhyngweithio â chytundebau rhyngwladol, ond mae'n sicr yn rhoi clawr i'w cwmnïau domestig, sy'n egluro'r hyn rydyn ni wedi bod yn ei weld."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "O ran Archif Anna — byddwn yn parhau â'n gwaith tanddaearol wedi'i wreiddio mewn argyhoeddiad moesol. Eto ein dymuniad mwyaf yw dod i'r golau, a chwyddo ein heffaith yn gyfreithlon. Os gwelwch yn dda, diwygio hawlfraint."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Darllenwch yr erthyglau cydymaith gan TorrentFreak: <a %(torrentfreak)s>cyntaf</a>, <a %(torrentfreak_2)s>ail</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Enillwyr y wobr $10,000 am weledigaeth ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Cawsom rai cyflwyniadau anhygoel i'r wobr $10,000 am weledigaeth ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Ychydig fisoedd yn ôl cyhoeddasom wobr <a %(all_isbns)s>$10,000</a> i wneud y weledigaeth orau bosibl o'n data yn dangos y gofod ISBN. Pwysleisiwyd dangos pa ffeiliau sydd gennym/heb eu harchifo eisoes, ac yn ddiweddarach dataset yn disgrifio faint o lyfrgelloedd sy'n dal ISBNs (mesur o brinder)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Rydym wedi cael ein llethu gan yr ymateb. Bu cymaint o greadigrwydd. Diolch mawr i bawb sydd wedi cymryd rhan: mae eich egni a'ch brwdfrydedd yn heintus!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Yn y pen draw, roeddem am ateb y cwestiynau canlynol: <strong>pa lyfrau sy'n bodoli yn y byd, faint rydym eisoes wedi'u harchifo, a pha lyfrau y dylem ganolbwyntio arnynt nesaf?</strong> Mae'n wych gweld cymaint o bobl yn poeni am y cwestiynau hyn."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Dechreuon ni gyda delweddu sylfaenol ein hunain. Mewn llai na 300kb, mae'r llun hwn yn cynrychioli'r \"rhestr o lyfrau\" fwyaf agored a grëwyd erioed yn hanes dynoliaeth:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Pob ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Ffeiliau yn Archif Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNOs CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Gollyngiad data CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSIDau DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Mynegai eLyfrau EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archif Rhyngrwyd"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Cofrestr Fyd-eang o Gyhoeddwyr ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Llyfrgell Wladol Rwsia"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Llyfrgell Ymerodrol Trantor"

#, fuzzy
msgid "common.back"
msgstr "Yn ôl"

#, fuzzy
msgid "common.forward"
msgstr "Ymlaen"

#, fuzzy
msgid "common.last"
msgstr "Diwethaf"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Gweler y <a %(all_isbns)s>cofnod blog gwreiddiol</a> am fwy o wybodaeth."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Fe wnaethom gyhoeddi her i wella ar hyn. Byddem yn dyfarnu gwobr gyntaf o $6,000, ail le o $3,000, a thrydydd lle o $1,000. Oherwydd yr ymateb llethol a'r cyflwyniadau anhygoel, rydym wedi penderfynu cynyddu'r pwll gwobr ychydig, a dyfarnu trydydd lle pedair ffordd o $500 yr un. Mae'r enillwyr isod, ond sicrhewch edrych ar yr holl gyflwyniadau <a %(annas_archive)s>yma</a>, neu lawrlwythwch ein <a %(a_2025_01_isbn_visualization_files)s>torrwr cyfun</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Lle cyntaf $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Mae'r <a %(phiresky_github)s>cyflwyniad</a> hwn (<a %(annas_archive_note_2951)s>sylw Gitlab</a>) yn syml bopeth yr oeddem ei eisiau, a mwy! Roeddem yn arbennig o hoff o'r opsiynau delweddu hynod hyblyg (hyd yn oed yn cefnogi cysgodion arfer), ond gyda rhestr gynhwysfawr o ragosodiadau. Roeddem hefyd yn hoffi pa mor gyflym a llyfn yw popeth, y gweithrediad syml (nad oes ganddo hyd yn oed gefnlen), y map bach clyfar, a'r esboniad helaeth yn eu <a %(phiresky_github)s>cofnod blog</a>. Gwaith anhygoel, a'r enillydd haeddiannol!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Ail le $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Cyflwyniad <a %(annas_archive_note_2913)s>anhygoel</a> arall. Nid mor hyblyg â'r lle cyntaf, ond mewn gwirionedd roeddem yn well gennym ei delweddu ar lefel macro dros y lle cyntaf (curf llenwi gofod, ffiniau, labelu, amlygu, panning, a chwyddo). Mae sylw <a %(annas_archive_note_2971)s>gan Joe Davis</a> wedi taro tant gyda ni:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Er bod sgwariau a hirsgwariau perffaith yn foddhaol yn fathemategol, nid ydynt yn darparu lleolrwydd uwch mewn cyd-destun mapio. Credaf nad yw'r anghymesuredd sydd yn gynhenid yn y rhain Hilbert neu Morton clasurol yn ddiffyg ond yn nodwedd. Yn union fel mae amlinelliad enwog siâp esgid yr Eidal yn ei gwneud yn hawdd ei adnabod ar fap, gall y \"quirks\" unigryw o'r curfau hyn wasanaethu fel tirnodau gwybyddol. Gall y nodedigedd hwn wella cof gofodol a helpu defnyddwyr i'w hunain, gan wneud lleoli ardaloedd penodol neu sylwi ar batrymau yn haws o bosibl.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Ac mae dal llawer o opsiynau ar gyfer delweddu a rendro, yn ogystal â rhyngwyneb defnyddiwr hynod llyfn a greddfol. Ail le cadarn!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Trydydd lle $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Yn y <a %(annas_archive_note_2940)s>cyflwyniad</a> hwn roeddem wir yn hoffi'r gwahanol fathau o olygfeydd, yn enwedig y golygfeydd cymharu a chyhoeddwr."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Trydydd lle $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Er nad yw'r rhyngwyneb defnyddiwr mwyaf gorffenedig, mae'r <a %(annas_archive_note_2917)s>cyflwyniad</a> hwn yn ticio llawer o'r blychau. Roeddem yn arbennig o hoff o'i nodwedd cymharu."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Trydydd lle $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Fel y lle cyntaf, mae'r <a %(annas_archive_note_2975)s>cyflwyniad</a> hwn wedi creu argraff arnom gyda'i hyblygrwydd. Yn y pen draw, dyma beth sy'n gwneud offeryn delweddu gwych: hyblygrwydd mwyaf ar gyfer defnyddwyr pŵer, tra'n cadw pethau'n syml ar gyfer defnyddwyr cyffredin."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Trydydd lle $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Mae'r <a %(annas_archive_note_2947)s>cyflwyniad</a> olaf i gael gwobr yn eithaf sylfaenol, ond mae ganddo rai nodweddion unigryw yr oeddem wir yn eu hoffi. Roeddem yn hoffi sut maen nhw'n dangos faint o datasets sy'n cwmpasu ISBN penodol fel mesur o boblogrwydd/dibynadwyedd. Roeddem hefyd yn hoffi symlrwydd ond effeithiolrwydd defnyddio llithrydd tryloywder ar gyfer cymariaethau."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Syniadau nodedig"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Rhagor o syniadau a gweithrediadau yr oeddem yn arbennig o hoff ohonynt:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Cyrnau am brinder"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Ystadegau byw"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Nodiadau, ac hefyd ystadegau byw"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Golwg map unigryw a hidlwyr"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Cynllun lliw diofyn cŵl a map gwres."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Toglo datasets yn hawdd ar gyfer cymariaethau cyflym."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Labeli pert."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Bar graddfa gyda nifer o lyfrau."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Llawer o sleidwyr i gymharu datasets, fel pe baech yn DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Gallem barhau am sbel, ond gadewch i ni stopio yma. Sicrhewch eich bod yn edrych ar yr holl gyflwyniadau <a %(annas_archive)s>yma</a>, neu lawrlwythwch ein <a %(a_2025_01_isbn_visualization_files)s>torrwr cyfun</a>. Cymaint o gyflwyniadau, ac mae pob un yn dod â safbwynt unigryw, boed hynny yn y UI neu'r gweithrediad."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Byddwn o leiaf yn ymgorffori'r cyflwyniad cyntaf i'n prif wefan, ac efallai rhai eraill. Rydym hefyd wedi dechrau meddwl am sut i drefnu'r broses o nodi, cadarnhau, ac yna archifo'r llyfrau prinnaf. Mwy i ddod ar y blaen hwn."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Diolch i bawb a gymerodd ran. Mae'n anhygoel bod cymaint o bobl yn poeni."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Mae ein calonnau'n llawn o ddiolchgarwch."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Gweledoli'r Holl ISBNs — $10,000 gwobr erbyn 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Mae'r llun hwn yn cynrychioli'r \"rhestr o lyfrau\" fwyaf agored a gasglwyd erioed yn hanes dynoliaeth."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Mae'r llun hwn yn 1000×800 picsel. Mae pob picsel yn cynrychioli 2,500 ISBN. Os oes gennym ffeil ar gyfer ISBN, rydym yn gwneud y picsel hwnnw'n fwy gwyrdd. Os ydym yn gwybod bod ISBN wedi'i gyhoeddi, ond nad oes gennym ffeil gyfatebol, rydym yn ei wneud yn fwy coch."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Mewn llai na 300kb, mae'r llun hwn yn cynrychioli'n gryno'r \"rhestr o lyfrau\" fwyaf agored a gasglwyd erioed yn hanes dynoliaeth (ychydig gannoedd o GB wedi'i gywasgu'n llawn)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Mae hefyd yn dangos: mae llawer o waith ar ôl wrth gefnogi llyfrau (dim ond 16% sydd gennym)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Cefndir"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Sut gall Archif Anna gyflawni ei genhadaeth o gefnogi holl wybodaeth dynoliaeth, heb wybod pa lyfrau sydd dal allan yna? Mae angen rhestr TODO arnom. Un ffordd o fapio hyn yw trwy rifau ISBN, sydd ers y 1970au wedi cael eu neilltuo i bob llyfr a gyhoeddwyd (yn y mwyafrif o wledydd)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Nid oes awdurdod canolog sy'n gwybod pob neilltuad ISBN. Yn lle hynny, mae'n system ddosbarthedig, lle mae gwledydd yn cael ystodau o rifau, sy'n neilltuo ystodau llai i gyhoeddwyr mawr, a allai is-rannu ystodau i gyhoeddwyr llai. Yn olaf, mae rhifau unigol yn cael eu neilltuo i lyfrau."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Dechreuon ni fapio ISBNs <a %(blog)s>ddwy flynedd yn ôl</a> gyda'n crafu o ISBNdb. Ers hynny, rydym wedi crafu llawer mwy o ffynonellau metadata, megis <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, a mwy. Gellir dod o hyd i restr lawn ar y tudalennau “Datasets” a “Torrents” ar Archif Anna. Mae gennym bellach y casgliad mwyaf llawn agored, hawdd ei lawrlwytho o metadata llyfrau (ac felly ISBNs) yn y byd."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Rydym wedi <a %(blog)s>ysgrifennu'n helaeth</a> am pam rydym yn poeni am gadwraeth, a pham rydym ar hyn o bryd mewn ffenestr hanfodol. Rhaid i ni nawr nodi llyfrau prin, heb lawer o sylw, ac unigryw mewn perygl a'u cadw. Mae cael metadata da ar bob llyfr yn y byd yn helpu gyda hynny."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Gweledol"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Yn ogystal â'r ddelwedd trosolwg, gallwn hefyd edrych ar y datasets unigol rydym wedi'u caffael. Defnyddiwch y gwymplen a'r botymau i newid rhyngddynt."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Mae llawer o batrymau diddorol i'w gweld yn y lluniau hyn. Pam mae rhywfaint o reoleidd-dra o linellau a blociau, sy'n ymddangos ar wahanol raddfeydd? Beth yw'r ardaloedd gwag? Pam mae rhai datasets mor glwstredig? Gadawn y cwestiynau hyn fel ymarfer i'r darllenydd."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Gwobr $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Mae llawer i'w archwilio yma, felly rydym yn cyhoeddi gwobr am wella'r gweledigaeth uchod. Yn wahanol i'r mwyafrif o'n gwobrau, mae'r un hon wedi'i rhwymo gan amser. Mae'n rhaid i chi <a %(annas_archive)s>gyflwyno</a> eich cod ffynhonnell agored erbyn 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Bydd y cyflwyniad gorau yn cael $6,000, yr ail le yw $3,000, a'r trydydd lle yw $1,000. Bydd pob gwobr yn cael ei dyfarnu gan ddefnyddio Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Isod mae'r meini prawf lleiaf. Os nad yw unrhyw gyflwyniad yn cwrdd â'r meini prawf, efallai y byddwn yn dal i ddyfarnu rhai gwobrau, ond bydd hynny yn ôl ein disgresiwn."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fforciwch y repo hwn, a golygu'r HTML post blog hwn (dim backend arall heblaw ein backend Flask sy'n cael ei ganiatáu)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Gwnewch y llun uchod yn llyfn i'w chwyddo, fel y gallwch chwyddo i ISBNs unigol. Dylai clicio ar ISBNs fynd â chi i dudalen metadata neu chwiliad ar Archif Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Rhaid i chi allu newid rhwng pob dataset gwahanol."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Dylai ystodau gwledydd ac ystodau cyhoeddwyr gael eu hamlygu wrth hofran. Gallwch ddefnyddio e.e. <a %(github_xlcnd_isbnlib)s>data4info.py yn isbnlib</a> ar gyfer gwybodaeth gwlad, a'n crafu “isbngrp” ar gyfer cyhoeddwyr (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Rhaid iddo weithio'n dda ar ben-desg a symudol."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Am bwyntiau bonws (dim ond syniadau yw'r rhain — gadewch i'ch creadigrwydd redeg yn wyllt):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Bydd ystyriaeth gref yn cael ei roi i ddefnyddioldeb a pha mor dda mae'n edrych."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Dangos metadata gwirioneddol ar gyfer ISBNs unigol wrth chwyddo i mewn, megis teitl ac awdur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Gwell cromlin llenwi gofod. E.e. zig-zag, yn mynd o 0 i 4 ar y rhes gyntaf ac yna'n ôl (yn wrthdro) o 5 i 9 ar yr ail res — wedi'i gymhwyso'n ailadroddus."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Cynlluniau lliw gwahanol neu addasadwy."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Golygfeydd arbennig ar gyfer cymharu datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Ffyrdd i ddadfygio problemau, megis metadata eraill nad ydynt yn cytuno'n dda (e.e. teitlau gwahanol iawn)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anodi delweddau gyda sylwadau ar ISBNs neu ystodau."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Unrhyw heuristig ar gyfer adnabod llyfrau prin neu mewn perygl."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Unrhyw syniadau creadigol y gallwch ddod i fyny â nhw!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Gallwch gwbl wyro oddi wrth y meini prawf lleiaf, a gwneud delweddu cwbl wahanol. Os yw'n wirioneddol ysblennydd, yna mae hynny'n gymwys ar gyfer y wobr, ond ar ein disgresiwn ni."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Gwnewch gyflwyniadau trwy bostio sylw i <a %(annas_archive)s>y mater hwn</a> gyda dolen i'ch repo wedi'i fforcio, cais uno, neu diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Cod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Gellir dod o hyd i'r cod i gynhyrchu'r delweddau hyn, yn ogystal ag enghreifftiau eraill, yn <a %(annas_archive)s>y cyfeiriadur hwn</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Daethom i fyny â fformat data cryno, gyda'r holl wybodaeth ISBN ofynnol tua 75MB (wedi'i gywasgu). Gellir dod o hyd i ddisgrifiad o'r fformat data a'r cod i'w gynhyrchu <a %(annas_archive_l1244_1319)s>yma</a>. Ar gyfer y wobr nid oes angen i chi ddefnyddio hyn, ond mae'n debyg mai dyma'r fformat mwyaf cyfleus i ddechrau gyda. Gallwch drawsnewid ein metadata fodd bynnag y dymunwch (er bod yn rhaid i'ch holl god fod yn ffynhonnell agored)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Allwn ni ddim aros i weld beth y byddwch chi'n dod i fyny â. Pob lwc!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Cynwysyddion Archif Anna (AAC): safoni rhyddhau o lyfrgell cysgodol fwyaf y byd"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Mae Archif Anna wedi dod yn llyfrgell gysgodol fwyaf y byd, gan ein gorfodi i safoni ein rhyddhau."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "Mae <a %(wikipedia_annas_archive)s>Archif Anna</a> wedi dod yn llyfrgell gysgodol fwyaf y byd o bell ffordd, a'r unig lyfrgell gysgodol o'i maint sy'n gwbl ffynhonnell agored a data agored. Isod mae tabl o'n tudalen Datasets (wedi'i addasu ychydig):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Gwnaethom gyflawni hyn mewn tri ffordd:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Adlewyrchu llyfrgelloedd cysgodol data agored presennol (fel Sci-Hub a Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Helpu llyfrgelloedd cysgodol sydd eisiau bod yn fwy agored, ond nad oedd ganddynt yr amser na'r adnoddau i wneud hynny (fel casgliad comics Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Crafu llyfrgelloedd nad ydynt yn dymuno rhannu'n swmp (fel Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Ar gyfer (2) a (3) rydym bellach yn rheoli casgliad sylweddol o dorrents ein hunain (100au o TBs). Hyd yma rydym wedi trin y casgliadau hyn fel unwaith yn unig, sy'n golygu seilwaith a threfniadaeth data wedi'u teilwra ar gyfer pob casgliad. Mae hyn yn ychwanegu gorlwyth sylweddol i bob rhyddhad, ac yn ei gwneud yn arbennig o anodd gwneud rhyddhadau mwy cynyddol."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Dyna pam y penderfynasom safoni ein rhyddhadau. Mae hwn yn bost blog technegol lle rydym yn cyflwyno ein safon: <strong>Cynwysyddion Archif Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Nodau dylunio"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Ein prif achos defnydd yw dosbarthu ffeiliau a metadata cysylltiedig o wahanol gasgliadau presennol. Ein prif ystyriaethau yw:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Ffeiliau a metadata amrywiol, mor agos â phosibl i'r fformat gwreiddiol."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Dynodwyr amrywiol yn y llyfrgelloedd ffynhonnell, neu hyd yn oed diffyg dynodwyr."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Rhyddhadau ar wahân o metadata yn erbyn data ffeil, neu ryddhadau metadata yn unig (e.e. ein rhyddhad ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Dosbarthiad trwy dorrents, er gyda'r posibilrwydd o ddulliau dosbarthu eraill (e.e. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Cofnodion anadnewyddadwy, gan y dylem dybio y bydd ein torrents yn byw am byth."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Rhyddhadau cynyddol / rhyddhadau y gellir eu hatodi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Darllenadwy a ysgrifennadwy gan beiriant, yn gyfleus ac yn gyflym, yn enwedig ar gyfer ein pentwr (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Arolygiad dynol cymharol hawdd, er bod hyn yn eilradd i ddarllenadwyedd peiriant."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Hawdd i hadu ein casgliadau gyda blwch hadau rhent safonol."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Gellir gweini data deuaidd yn uniongyrchol gan weinyddion gwe fel Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Rhai nodau nad ydynt yn bwysig:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nid ydym yn poeni am ffeiliau sy'n hawdd i'w llywio â llaw ar ddisg, neu'n chwiliadwy heb rag-brosesu."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nid ydym yn poeni am fod yn uniongyrchol gydnaws â meddalwedd llyfrgell bresennol."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Er y dylai fod yn hawdd i unrhyw un hadu ein casgliad gan ddefnyddio torrents, nid ydym yn disgwyl i'r ffeiliau fod yn ddefnyddiadwy heb wybodaeth dechnegol sylweddol ac ymrwymiad."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Gan fod Archif Anna yn ffynhonnell agored, rydym am ddefnyddio ein fformat yn uniongyrchol. Pan fyddwn yn adnewyddu ein mynegai chwilio, dim ond llwybrau sydd ar gael yn gyhoeddus yr ydym yn eu cyrchu, fel y gall unrhyw un sy'n fforcio ein llyfrgell ddechrau'n gyflym."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Y safon"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Yn y pen draw, fe wnaethom setlo ar safon gymharol syml. Mae'n eithaf rhydd, yn anghyfarwyddol, ac yn waith sy'n parhau."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> Mae AAC (Container Archif Anna) yn eitem sengl sy'n cynnwys <strong>metadata</strong>, ac yn ddewisol <strong>data deuaidd</strong>, y ddau ohonynt yn anadnewyddadwy. Mae ganddo ddynodwr unigryw yn fyd-eang, o'r enw <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Casgliad.</strong> Mae pob AAC yn perthyn i gasgliad, sydd yn ôl diffiniad yn rhestr o AACs sy'n gyson yn semantig. Mae hynny'n golygu os gwnewch newid sylweddol i fformat y metadata, yna mae'n rhaid i chi greu casgliad newydd."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>casgliadau “cofnodion” a “ffeiliau”.</strong> Yn ôl confensiwn, mae'n aml yn gyfleus rhyddhau “cofnodion” a “ffeiliau” fel casgliadau gwahanol, fel y gellir eu rhyddhau ar wahanol amserlenni, e.e. yn seiliedig ar gyfraddau sgrapio. Mae “cofnod” yn gasgliad metadata yn unig, sy'n cynnwys gwybodaeth fel teitlau llyfrau, awduron, ISBNs, ac ati, tra bod “ffeiliau” yn gasgliadau sy'n cynnwys y ffeiliau gwirioneddol eu hunain (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Dyma fformat AACID: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Er enghraifft, mae AACID gwirioneddol a ryddhawyd gennym yn <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: enw'r casgliad, a all gynnwys llythrennau ASCII, rhifau, ac islinellau (ond dim islinellau dwbl)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: fersiwn fer o'r ISO 8601, bob amser yn UTC, e.e. <code>20220723T194746Z</code>. Mae'n rhaid i'r rhif hwn gynyddu'n fonotonig ar gyfer pob rhyddhad, er y gall ei semanteg union wahaniaethu fesul casgliad. Awgrymwn ddefnyddio amser sgrapio neu gynhyrchu'r ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: dynodwr penodol i'r casgliad, os yw'n berthnasol, e.e. ID Llyfrgell Z. Gellir ei hepgor neu ei dorri. Rhaid ei hepgor neu ei dorri os byddai'r AACID fel arall yn fwy na 150 o gymeriadau."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID ond wedi'i gywasgu i ASCII, e.e. gan ddefnyddio base57. Ar hyn o bryd rydym yn defnyddio'r llyfrgell <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Ystod AACID.</strong> Gan fod AACIDs yn cynnwys stampiau amser sy'n cynyddu'n fonotonig, gallwn ddefnyddio hynny i ddynodi ystodau o fewn casgliad penodol. Rydym yn defnyddio'r fformat hwn: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, lle mae'r stampiau amser yn gynhwysol. Mae ystodau'n barhaus, a gallant orgyffwrdd, ond os oes orgyffwrdd rhaid iddynt gynnwys cofnodion union yr un fath â'r rhai a ryddhawyd yn flaenorol yn y casgliad hwnnw (gan fod AACs yn anadnewyddadwy). Ni chaniateir cofnodion coll."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Ffeil metadata.</strong> Mae ffeil metadata yn cynnwys metadata ystod o AACs, ar gyfer un casgliad penodol. Mae'r rhain yn cynnwys y priodweddau canlynol:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Rhaid i enw'r ffeil fod yn ystod AACID, wedi'i ragddodi gyda <code style=\"color: red\">annas_archive_meta__</code> ac wedi'i ddilyn gan <code>.jsonl.zstd</code>. Er enghraifft, un o'n rhyddhadau yw<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Fel y nodir gan estyniad y ffeil, mae'r math o ffeil yn <a %(jsonlines)s>JSON Lines</a> wedi'i gywasgu gyda <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Rhaid i bob gwrthrych JSON gynnwys y meysydd canlynol ar y lefel uchaf: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (dewisol). Ni chaniateir unrhyw feysydd eraill."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> yw metadata ar hap, yn ôl semanteg y casgliad. Rhaid iddo fod yn gyson yn semantig o fewn y casgliad."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> yw dewisol, ac mae'n enw'r ffolder data deuaidd sy'n cynnwys y data deuaidd cyfatebol. Enw'r ffeil o'r data deuaidd cyfatebol o fewn y ffolder honno yw AACID y cofnod."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Gellir addasu'r rhagddodiad <code style=\"color: red\">annas_archive_meta__</code> i enw eich sefydliad, e.e. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Ffolder data deuaidd.</strong> Ffolder gyda data deuaidd ystod o AACs, ar gyfer un casgliad penodol. Mae'r rhain yn cynnwys y priodweddau canlynol:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Rhaid i enw'r cyfeiriadur fod yn ystod AACID, wedi'i ragddodi gyda <code style=\"color: green\">annas_archive_data__</code>, a heb unrhyw ôl-ddodiad. Er enghraifft, mae un o'n rhyddhadau gwirioneddol yn cynnwys cyfeiriadur o'r enw<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Rhaid i'r cyfeiriadur gynnwys ffeiliau data ar gyfer pob AAC o fewn yr ystod benodol. Rhaid i bob ffeil data gael ei AACID fel enw'r ffeil (heb estyniadau)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Argymhellir gwneud y ffolderi hyn yn weddol reoladwy o ran maint, e.e. ddim yn fwy na 100GB-1TB yr un, er y gall yr argymhelliad hwn newid dros amser."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Gellir pecynnu'r ffeiliau metadata a'r ffolderi data deuaidd mewn torrents, gyda un torrent fesul ffeil metadata, neu un torrent fesul ffolder data deuaidd. Rhaid i'r torrents gael yr enw ffeil/gosod gwreiddiol ynghyd â'r ôl-ddodiad <code>.torrent</code> fel eu henw ffeil."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Enghraifft"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Gadewch i ni edrych ar ein rhyddhad diweddar o Z-Library fel enghraifft. Mae'n cynnwys dwy gasgliad: “<span style=\"background: #fffaa3\">zlib3_records</span>” a “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Mae hyn yn ein galluogi i grafu a rhyddhau cofnodion metadata ar wahân i'r ffeiliau llyfrau gwirioneddol. Felly, fe wnaethom ryddhau dau torrent gyda ffeiliau metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Fe wnaethom hefyd ryddhau criw o torrents gyda ffolderi data deuaidd, ond dim ond ar gyfer y casgliad “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 i gyd:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Trwy redeg <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> gallwn weld beth sydd y tu mewn:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Yn yr achos hwn, mae'n metadata llyfr fel y nodwyd gan Z-Library. Ar y lefel uchaf dim ond “aacid” a “metadata” sydd gennym, ond dim “data_folder”, gan nad oes data deuaidd cyfatebol. Mae'r AACID yn cynnwys “22430000” fel yr ID cynradd, y gallwn weld ei fod wedi'i gymryd o “zlibrary_id”. Gallwn ddisgwyl i AACs eraill yn y casgliad hwn gael yr un strwythur."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nawr gadewch i ni redeg <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Mae hwn yn metadata AAC llawer llai, er bod y rhan fwyaf o'r AAC hwn wedi'i leoli mewn ffeil deuaidd yn rhywle arall! Wedi'r cyfan, mae gennym “data_folder” y tro hwn, felly gallwn ddisgwyl i'r data deuaidd cyfatebol gael ei leoli yn <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Mae'r “metadata” yn cynnwys y “zlibrary_id”, felly gallwn ei gysylltu'n hawdd â'r AAC cyfatebol yn y casgliad “zlib_records”. Gallem fod wedi cysylltu mewn nifer o ffyrdd gwahanol, e.e. trwy AACID — nid yw'r safon yn rhagnodi hynny."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nodwch nad yw'n angenrheidiol i'r maes “metadata” ei hun fod yn JSON. Gallai fod yn llinyn sy'n cynnwys XML neu unrhyw fformat data arall. Gallech hyd yn oed storio gwybodaeth metadata yn y blob deuaidd cysylltiedig, e.e. os yw'n llawer o ddata."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Casgliad"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Gyda'r safon hon, gallwn wneud rhyddhadau yn fwy graddol, a mwy o ffynonellau data newydd yn haws. Mae gennym eisoes ychydig o ryddhadau cyffrous yn y biblinell!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Rydym hefyd yn gobeithio y bydd yn haws i lyfrgelloedd cysgodol eraill adlewyrchu ein casgliadau. Wedi'r cyfan, ein nod yw cadw gwybodaeth a diwylliant dynol am byth, felly po fwyaf o orddybiaeth, gorau oll."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Diweddariad Anna: archif ffynhonnell agored llawn, ElasticSearch, 300GB+ o gloriau llyfrau"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Rydym wedi bod yn gweithio'n ddi-baid i ddarparu dewis amgen da gyda Archif Anna. Dyma rai o'r pethau rydym wedi'u cyflawni yn ddiweddar."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Gyda Z-Library yn mynd i lawr a'i sylfaenwyr (honedig) yn cael eu harestio, rydym wedi bod yn gweithio'n ddi-baid i ddarparu dewis amgen da gyda Archif Anna (ni fyddwn yn cysylltu â hi yma, ond gallwch ei Google). Dyma rai o'r pethau rydym wedi'u cyflawni yn ddiweddar."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Mae Archif Anna yn ffynhonnell agored llawn"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Credwn y dylai gwybodaeth fod yn rhydd, ac nid yw ein cod ein hunain yn eithriad. Rydym wedi rhyddhau ein holl god ar ein Gitlab preifat: <a %(annas_archive)s>Meddalwedd Anna</a>. Rydym hefyd yn defnyddio'r tracwr materion i drefnu ein gwaith. Os ydych chi eisiau cymryd rhan yn ein datblygiad, mae hwn yn lle gwych i ddechrau."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "I roi blas i chi o'r pethau rydym yn gweithio arnynt, cymerwch ein gwaith diweddar ar welliannau perfformiad ar ochr y cleient. Gan nad ydym wedi gweithredu tudalennau eto, byddem yn aml yn dychwelyd tudalennau chwilio hir iawn, gyda 100-200 o ganlyniadau. Nid oeddem am dorri'r canlyniadau chwilio yn rhy fuan, ond roedd hyn yn golygu y byddai'n arafu rhai dyfeisiau. Ar gyfer hyn, gweithredasom dric bach: fe wnaethom lapio'r rhan fwyaf o'r canlyniadau chwilio mewn sylwadau HTML (<code><!-- --></code>), ac yna ysgrifennu ychydig o Javascript a fyddai'n canfod pryd y dylai canlyniad ddod yn weladwy, ar ba adeg y byddem yn dadlapio'r sylw:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "Wedi gweithredu \"rhithwiroli\" DOM mewn 23 llinell, dim angen llyfrgelloedd ffansi! Dyma'r math o god pragmatig cyflym y byddwch yn ei gael pan fydd gennych amser cyfyngedig, a phroblemau go iawn sydd angen eu datrys. Adroddwyd bod ein chwiliad bellach yn gweithio'n dda ar ddyfeisiau araf!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Ymdrech fawr arall oedd awtomeiddio adeiladu'r gronfa ddata. Pan lansiwyd ni, fe wnaethom dynnu gwahanol ffynonellau at ei gilydd yn ddigymell. Nawr rydym am eu cadw'n gyfredol, felly ysgrifennwyd nifer o sgriptiau i lawrlwytho metadata newydd o'r ddau fforc Library Genesis, a'u hintegreiddio. Y nod yw nid yn unig gwneud hyn yn ddefnyddiol ar gyfer ein harchif, ond i wneud pethau'n hawdd i unrhyw un sydd eisiau chwarae o gwmpas gyda metadata llyfrgell cysgodol. Y nod fyddai llyfr nodiadau Jupyter sydd â phob math o metadata diddorol ar gael, fel y gallwn wneud mwy o ymchwil fel darganfod pa <a %(blog)s>ganran o ISBNs sy'n cael eu cadw am byth</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Yn olaf, rydym wedi ailwampio ein system rhoddion. Gallwch nawr ddefnyddio cerdyn credyd i adneuo arian yn uniongyrchol i'n waledau crypto, heb wir angen gwybod unrhyw beth am arian cyfred digidol. Byddwn yn parhau i fonitro pa mor dda mae hyn yn gweithio yn ymarferol, ond mae hyn yn fargen fawr."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Newid i ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Un o'n <a %(annas_archive)s>tocynnau</a> oedd bag o faterion gyda'n system chwilio. Defnyddiwyd chwiliad testun llawn MySQL, gan fod gennym ein holl ddata yn MySQL beth bynnag. Ond roedd ganddo ei gyfyngiadau:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Roedd rhai ymholiadau yn cymryd amser hir iawn, i'r pwynt lle byddent yn meddiannu'r holl gysylltiadau agored."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Yn ddiofyn mae gan MySQL hyd geiriau lleiaf, neu gall eich mynegai fynd yn fawr iawn. Adroddwyd nad oedd pobl yn gallu chwilio am “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Roedd chwilio ond yn weddol gyflym pan oedd wedi'i lwytho'n llawn yn y cof, a oedd yn ei gwneud yn ofynnol i ni gael peiriant mwy costus i redeg hyn arno, ynghyd â rhai gorchmynion i raglwytho'r mynegai ar gychwyn."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Ni fyddem wedi gallu ei ymestyn yn hawdd i adeiladu nodweddion newydd, fel <a %(wikipedia_cjk_characters)s>tokenization gwell ar gyfer ieithoedd heb fylchau gwyn</a>, hidlo/ffasetu, didoli, awgrymiadau \"oeddet ti'n golygu\", awtocomplete, ac yn y blaen."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Ar ôl siarad â llu o arbenigwyr, penderfynwyd ar ElasticSearch. Nid yw wedi bod yn berffaith (mae eu hawgrymiadau “oeddet ti'n golygu” diofyn a nodweddion awtocomplete yn wael), ond ar y cyfan mae wedi bod yn llawer gwell na MySQL ar gyfer chwilio. Nid ydym yn <a %(youtube)s>rhy hoff</a> o'i ddefnyddio ar gyfer unrhyw ddata hanfodol (er eu bod wedi gwneud llawer o <a %(elastic_co)s>cynnydd</a>), ond ar y cyfan rydym yn eithaf hapus gyda'r newid."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Am y tro, rydym wedi gweithredu chwilio llawer cyflymach, cefnogaeth iaith gwell, didoli perthnasedd gwell, opsiynau didoli gwahanol, a hidlo ar iaith/math o lyfr/math o ffeil. Os ydych yn chwilfrydig sut mae'n gweithio, <a %(annas_archive_l140)s>edrychwch</a> <a %(annas_archive_l1115)s>ar</a> <a %(annas_archive_l1635)s>hyn</a>. Mae'n eithaf hygyrch, er y gallai ddefnyddio mwy o sylwadau…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ o gloriau llyfrau wedi'u rhyddhau"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Yn olaf, rydym yn falch o gyhoeddi rhyddhad bach. Mewn cydweithrediad â'r bobl sy'n gweithredu'r fforc Libgen.rs, rydym yn rhannu eu holl gloriau llyfrau trwy dorrents ac IPFS. Bydd hyn yn dosbarthu'r llwyth o weld y cloriau ymhlith mwy o beiriannau, a bydd yn eu cadw'n well. Mewn llawer (ond nid pob) achos, mae'r cloriau llyfrau wedi'u cynnwys yn y ffeiliau eu hunain, felly mae hyn yn fath o \"ddata deilliedig\". Ond mae ei gael yn IPFS yn dal i fod yn ddefnyddiol iawn ar gyfer gweithrediad dyddiol Archif Anna a'r gwahanol fforc Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Fel arfer, gallwch ddod o hyd i'r rhyddhad hwn yn y Drych Llyfrgell Môr-ladron (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>). Ni fyddwn yn cysylltu ag ef yma, ond gallwch ddod o hyd iddo'n hawdd."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Gobeithio y gallwn ymlacio ein cyflymder ychydig, nawr bod gennym ddewis arall gweddus i Z-Library. Nid yw'r llwyth gwaith hwn yn arbennig o gynaliadwy. Os oes gennych ddiddordeb mewn helpu gyda rhaglennu, gweithrediadau gweinydd, neu waith cadwraeth, cysylltwch â ni yn bendant. Mae llawer o <a %(annas_archive)s>waith i'w wneud</a> o hyd. Diolch am eich diddordeb a'ch cefnogaeth."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Mae Archif Anna wedi gwneud copi wrth gefn o lyfrgell cysgodol comics fwyaf y byd (95TB) — gallwch helpu i hadu"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Roedd gan lyfrgell cysgodol comics fwyaf y byd un pwynt methiant.. tan heddiw."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Trafodwch ar Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Mae'r llyfrgell cysgodol fwyaf o lyfrau comics yn debygol o fod yn un o fforc penodol Library Genesis: Libgen.li. Llwyddodd yr un gweinyddwr sy'n rhedeg y safle hwnnw i gasglu casgliad comics anhygoel o dros 2 filiwn o ffeiliau, yn gyfanswm o dros 95TB. Fodd bynnag, yn wahanol i gasgliadau eraill Library Genesis, nid oedd hwn ar gael yn swmp trwy dorrents. Dim ond trwy ei weinydd personol araf y gallech gael mynediad at y comics hyn yn unigol — un pwynt methiant. Tan heddiw!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Yn y post hwn byddwn yn dweud mwy wrthych am y casgliad hwn, ac am ein casgliad arian i gefnogi mwy o'r gwaith hwn."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Mae Dr. Barbara Gordon yn ceisio colli ei hun yn y byd cyffredin o'r llyfrgell…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Ffyrc Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Yn gyntaf, rhywfaint o gefndir. Efallai eich bod yn adnabod Library Genesis am eu casgliad llyfrau epig. Mae llai o bobl yn gwybod bod gwirfoddolwyr Library Genesis wedi creu prosiectau eraill, megis casgliad sylweddol o gylchgronau a dogfennau safonol, copi wrth gefn llawn o Sci-Hub (mewn cydweithrediad â sylfaenydd Sci-Hub, Alexandra Elbakyan), ac yn wir, casgliad enfawr o gomics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Ar ryw adeg aeth gweithredwyr gwahanol o ddrychau Library Genesis eu ffyrdd ar wahân, a arweiniodd at y sefyllfa bresennol o gael nifer o “ffyrc” gwahanol, i gyd yn dal i gario'r enw Library Genesis. Mae gan y fforc Libgen.li yn unig y casgliad comics hwn, yn ogystal â chasgliad sylweddol o gylchgronau (yr ydym hefyd yn gweithio arno)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Cydweithrediad"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "O ystyried ei faint, mae'r casgliad hwn wedi bod ar ein rhestr ddymuniadau ers amser maith, felly ar ôl ein llwyddiant gyda chefnogi Z-Library, gosodon ni ein golygon ar y casgliad hwn. Ar y dechrau fe wnaethon ni ei grafu'n uniongyrchol, a oedd yn dipyn o her, gan nad oedd eu gweinydd yn y cyflwr gorau. Cawsom tua 15TB fel hyn, ond roedd yn araf."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Yn ffodus, llwyddon ni i gysylltu â gweithredwr y llyfrgell, a gytunodd i anfon yr holl ddata atom yn uniongyrchol, a oedd yn llawer cyflymach. Cymerodd fwy na hanner blwyddyn o hyd i drosglwyddo a phrosesu'r holl ddata, a bron i ni golli'r cyfan i lygredd disg, a fyddai wedi golygu dechrau o'r newydd."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Mae'r profiad hwn wedi gwneud i ni gredu ei bod yn bwysig cael y data hwn allan yno cyn gynted â phosibl, fel y gellir ei adlewyrchu'n eang. Dim ond un neu ddau o ddigwyddiadau amseru anffodus sydd gennym i ffwrdd rhag colli'r casgliad hwn am byth!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Y casgliad"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Mae symud yn gyflym yn golygu bod y casgliad ychydig yn anhrefnus… Gadewch i ni edrych. Dychmygwch fod gennym system ffeiliau (sydd mewn gwirionedd rydym yn ei rhannu ar draws toriannau):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Y cyfeiriadur cyntaf, <code>/repository</code>, yw'r rhan fwy strwythuredig o hyn. Mae'r cyfeiriadur hwn yn cynnwys y cyfeirlyfrau “mil”: cyfeirlyfrau pob un â miloedd o ffeiliau, sy'n cael eu rhifo'n gynyddol yn y gronfa ddata. Mae cyfeiriadur <code>0</code> yn cynnwys ffeiliau gyda comic_id 0–999, ac yn y blaen."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dyma'r un cynllun ag y mae Library Genesis wedi bod yn ei ddefnyddio ar gyfer ei gasgliadau ffuglen a ffeithiol. Y syniad yw bod pob “mil dir” yn cael ei droi'n awtomatig yn doriant cyn gynted ag y mae'n cael ei lenwi."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Fodd bynnag, ni wnaeth gweithredwr Libgen.li byth doriannau ar gyfer y casgliad hwn, ac felly mae'n debyg bod y mil dir wedi dod yn anghyfleus, ac wedi rhoi ffordd i “gyfeirlyfrau heb eu didoli”. Mae'r rhain yn <code>/comics0</code> trwy <code>/comics4</code>. Maent i gyd yn cynnwys strwythurau cyfeirlyfr unigryw, a oedd yn debygol o wneud synnwyr ar gyfer casglu'r ffeiliau, ond nid ydynt yn gwneud llawer o synnwyr i ni nawr. Yn ffodus, mae'r metadata yn dal i gyfeirio'n uniongyrchol at yr holl ffeiliau hyn, felly nid yw eu trefniadaeth storio ar ddisg yn bwysig mewn gwirionedd!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Mae'r metadata ar gael ar ffurf cronfa ddata MySQL. Gellir ei lawrlwytho'n uniongyrchol o wefan Libgen.li, ond byddwn hefyd yn ei wneud ar gael mewn toriant, ochr yn ochr â'n tabl ein hunain gyda'r holl hasys MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Dadansoddiad"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Pan gewch 95TB wedi'i daflu i'ch clwstwr storio, rydych chi'n ceisio gwneud synnwyr o'r hyn sydd hyd yn oed yno… Gwnaethom rywfaint o ddadansoddiad i weld a allem leihau'r maint ychydig, megis trwy dynnu dyblygu. Dyma rai o'n canfyddiadau:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Gellir hidlo dyblygu semantig (sganiadau gwahanol o'r un llyfr) yn ddamcaniaethol, ond mae'n anodd. Wrth edrych â llaw trwy'r comics gwelsom ormod o ganlyniadau ffug."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Mae rhai dyblygu yn unig trwy MD5, sy'n gymharol wastraffus, ond byddai eu hidlo allan yn rhoi tua 1% in arbedion i ni yn unig. Ar y raddfa hon mae hynny'n dal i fod tua 1TB, ond hefyd, ar y raddfa hon nid yw 1TB yn bwysig mewn gwirionedd. Byddem yn well gennym beidio â mentro dinistrio data yn ddamweiniol yn y broses hon."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Fe wnaethom ddod o hyd i lwyth o ddata nad yw'n llyfrau, megis ffilmiau yn seiliedig ar lyfrau comig. Mae hynny hefyd yn ymddangos yn wastraffus, gan fod y rhain eisoes ar gael yn eang trwy ddulliau eraill. Fodd bynnag, sylweddolon ni na allem hidlo ffeiliau ffilm yn unig, gan fod yna hefyd <em>llyfrau comig rhyngweithiol</em> a ryddhawyd ar y cyfrifiadur, a recordiwyd gan rywun ac a gadwyd fel ffilmiau."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Yn y pen draw, dim ond ychydig y cant y gallem ei arbed trwy ddileu unrhyw beth o'r casgliad. Yna cofiasom ein bod ni'n gasglwyr data, ac mae'r bobl a fydd yn adlewyrchu hyn hefyd yn gasglwyr data, ac felly, “BETH YDYCH CHI'N EI FEDDWL, DILEU?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Felly rydym yn cyflwyno i chi, y casgliad llawn, heb ei addasu. Mae'n llawer o ddata, ond rydym yn gobeithio y bydd digon o bobl yn poeni i'w hadu beth bynnag."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Codi Arian"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Rydym yn rhyddhau'r data hwn mewn rhai darnau mawr. Mae'r tŵr cyntaf yn <code>/comics0</code>, a roesom mewn un ffeil .tar enfawr 12TB. Mae hynny'n well i'ch gyriant caled a'ch meddalwedd tŵr na miliwn o ffeiliau llai."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Fel rhan o'r rhyddhau hwn, rydym yn cynnal codi arian. Rydym yn ceisio codi $20,000 i dalu costau gweithredu a chontractio ar gyfer y casgliad hwn, yn ogystal â galluogi prosiectau parhaus a'r dyfodol. Mae gennym rai <em>enfawr</em> ar y gweill."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Pwy ydw i'n ei gefnogi gyda fy rhodd?</em> Yn fyr: rydym yn gwneud copi wrth gefn o holl wybodaeth a diwylliant dynoliaeth, ac yn ei gwneud yn hawdd ei gyrchu. Mae ein holl god a data yn ffynhonnell agored, rydym yn brosiect sy'n cael ei redeg yn gyfan gwbl gan wirfoddolwyr, ac rydym wedi arbed 125TB o lyfrau hyd yma (yn ychwanegol at dwrenti Libgen a Scihub sy'n bodoli eisoes). Yn y pen draw rydym yn adeiladu olwyn hedfan sy'n galluogi ac yn cymell pobl i ddod o hyd i, sganio, a gwneud copi wrth gefn o'r holl lyfrau yn y byd. Byddwn yn ysgrifennu am ein cynllun meistr mewn post yn y dyfodol. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Os byddwch yn rhoi rhodd am aelodaeth “Amazing Archivist” am 12 mis ($780), cewch <strong>“fabwysiadu tŵr”</strong>, sy'n golygu y byddwn yn rhoi eich enw defnyddiwr neu neges yn enw ffeil un o'r tŵrenti!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Gallwch roi rhodd trwy fynd i <a %(wikipedia_annas_archive)s>Archif Anna</a> a chlicio'r botwm “Rhoi”. Rydym hefyd yn chwilio am fwy o wirfoddolwyr: peirianwyr meddalwedd, ymchwilwyr diogelwch, arbenigwyr masnachwr dienw, a chyfieithwyr. Gallwch hefyd ein cefnogi trwy ddarparu gwasanaethau cynnal. Ac wrth gwrs, os gwelwch yn dda, hadwch ein tŵrenti!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Diolch i bawb sydd eisoes wedi ein cefnogi mor hael! Rydych chi wir yn gwneud gwahaniaeth."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Dyma'r tŵrenti a ryddhawyd hyd yma (rydym yn dal i brosesu'r gweddill):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Gellir dod o hyd i'r holl dŵrenti ar <a %(wikipedia_annas_archive)s>Archif Anna</a> o dan “Datasets” (nid ydym yn cysylltu yno'n uniongyrchol, felly nid yw dolenni i'r blog hwn yn cael eu tynnu o Reddit, Twitter, ac ati). O'r fan honno, dilynwch y ddolen i'r wefan Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Beth sy'n nesaf?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Mae criw o dŵrenti yn wych ar gyfer cadwraeth hirdymor, ond nid cymaint ar gyfer mynediad bob dydd. Byddwn yn gweithio gyda phartneriaid cynnal i gael yr holl ddata hwn ar y we (gan nad yw Archif Anna yn cynnal unrhyw beth yn uniongyrchol). Wrth gwrs, byddwch yn gallu dod o hyd i'r dolenni lawrlwytho hyn ar Archif Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Rydym hefyd yn gwahodd pawb i wneud pethau gyda'r data hwn! Helpwch ni i'w ddadansoddi'n well, ei ddad-ddyblygu, ei roi ar IPFS, ei ailgymysgu, hyfforddi eich modelau AI gyda'i gilydd, ac yn y blaen. Mae'n eiddo i chi i gyd, ac ni allwn aros i weld beth rydych chi'n ei wneud ag ef."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Yn olaf, fel y dywedwyd o'r blaen, mae gennym rai rhyddhau enfawr yn dod i fyny (os gallai <em>rhywun</em> <em>anfon damweiniol</em> dump o gronfa ddata <em>ACS4 benodol</em>, rydych chi'n gwybod ble i ddod o hyd i ni…), yn ogystal â adeiladu'r olwyn hedfan ar gyfer gwneud copi wrth gefn o'r holl lyfrau yn y byd."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Felly arhoswch yn tiwn, rydym ond yn dechrau."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x llyfrau newydd wedi'u hychwanegu at y Drych Llyfrgell Môr-leidr (+24TB, 3.8 miliwn o lyfrau)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Yn rhyddhau gwreiddiol y Drych Llyfrgell Môr-leidr (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>), gwnaethom ddrych o Z-Library, casgliad llyfrau anghyfreithlon mawr. Fel atgoffa, dyma beth ysgrifennwyd gennym yn y blog gwreiddiol hwnnw:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Mae Z-Library yn llyfrgell boblogaidd (ac anghyfreithlon). Maent wedi cymryd casgliad Library Genesis a'i wneud yn hawdd chwilio. Ar ben hynny, maent wedi dod yn effeithiol iawn wrth geisio cyfraniadau llyfrau newydd, trwy gymell defnyddwyr cyfrannol gyda gwahanol fanteision. Ar hyn o bryd nid ydynt yn cyfrannu'r llyfrau newydd hyn yn ôl i Library Genesis. Ac yn wahanol i Library Genesis, nid ydynt yn gwneud eu casgliad yn hawdd ei adlewyrchu, sy'n atal cadwraeth eang. Mae hyn yn bwysig i'w model busnes, gan eu bod yn codi arian am fynediad at eu casgliad yn swmpus (mwy na 10 llyfr y dydd)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nid ydym yn gwneud barn foesol am godi arian am fynediad swmpus i gasgliad llyfrau anghyfreithlon. Mae'n sicr bod y Llyfrgell Z wedi bod yn llwyddiannus wrth ehangu mynediad at wybodaeth, a chael mwy o lyfrau. Rydym yma i wneud ein rhan: sicrhau cadwraeth hirdymor y casgliad preifat hwn."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Mae'r casgliad hwnnw'n dyddio'n ôl i ganol 2021. Yn y cyfamser, mae'r Llyfrgell Z wedi bod yn tyfu ar gyfradd syfrdanol: maent wedi ychwanegu tua 3.8 miliwn o lyfrau newydd. Mae rhai dyblygiadau yno, yn sicr, ond mae'r mwyafrif ohono'n ymddangos fel llyfrau newydd dilys, neu sganiau o ansawdd uwch o lyfrau a gyflwynwyd yn flaenorol. Mae hyn yn bennaf oherwydd y nifer cynyddol o gymedrolwyr gwirfoddol yn y Llyfrgell Z, a'u system lanlwytho swmpus gyda diddyblygiad. Hoffem eu llongyfarch ar y cyflawniadau hyn."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Rydym yn falch o gyhoeddi ein bod wedi cael yr holl lyfrau a ychwanegwyd at y Llyfrgell Z rhwng ein drych diwethaf ac Awst 2022. Rydym hefyd wedi mynd yn ôl a sgrapio rhai llyfrau a gollwyd gennym y tro cyntaf. Ar y cyfan, mae'r casgliad newydd hwn tua 24TB, sy'n llawer mwy na'r un diwethaf (7TB). Mae ein drych bellach yn 31TB i gyd. Unwaith eto, fe wnaethom ddiddyblygu yn erbyn Library Genesis, gan fod torrenti ar gael eisoes ar gyfer y casgliad hwnnw."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Ewch i Ddrych Llyfrgell y Môr-ladron i edrych ar y casgliad newydd (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>). Mae mwy o wybodaeth yno am sut mae'r ffeiliau wedi'u strwythuro, a beth sydd wedi newid ers y tro diwethaf. Ni fyddwn yn cysylltu ag ef o fan hyn, gan mai dim ond gwefan blog yw hon nad yw'n cynnal unrhyw ddeunyddiau anghyfreithlon."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Wrth gwrs, mae hadu hefyd yn ffordd wych o'n helpu ni. Diolch i bawb sy'n hadu ein set flaenorol o dorrenti. Rydym yn ddiolchgar am yr ymateb cadarnhaol, ac yn hapus bod cymaint o bobl sy'n poeni am gadw gwybodaeth a diwylliant yn y ffordd anarferol hon."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Sut i ddod yn archifydd môr-ladron"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Efallai y bydd yr her gyntaf yn un sy'n syndod. Nid yw'n broblem dechnegol, nac yn broblem gyfreithiol. Mae'n broblem seicolegol."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Cyn i ni blymio i mewn, dau ddiweddariad ar Ddrych Llyfrgell y Môr-ladron (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Cawsom rai rhoddion hynod hael. Y cyntaf oedd $10k gan unigolyn dienw sydd hefyd wedi bod yn cefnogi \"bookwarrior\", sylfaenydd gwreiddiol Library Genesis. Diolch arbennig i bookwarrior am hwyluso'r rhodd hon. Yr ail oedd $10k arall gan roddwr dienw, a gysylltodd ar ôl ein rhyddhad diwethaf, ac a gafodd ei ysbrydoli i helpu. Cawsom hefyd nifer o roddion llai. Diolch yn fawr iawn am eich cefnogaeth hael. Mae gennym rai prosiectau newydd cyffrous yn y gwaith y bydd hyn yn eu cefnogi, felly arhoswch yn tiwn."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Cawsom rai anawsterau technegol gyda maint ein hail ryddhad, ond mae ein torrenti i fyny ac yn hadu nawr. Cawsom hefyd gynnig hael gan unigolyn dienw i hadu ein casgliad ar eu gweinyddion cyflym iawn, felly rydym yn gwneud lanlwythiad arbennig i'w peiriannau, ar ôl hynny dylai pawb arall sy'n lawrlwytho'r casgliad weld gwelliant mawr mewn cyflymder."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Gellir ysgrifennu llyfrau cyfan am y <em>pam</em> o gadwraeth ddigidol yn gyffredinol, ac archifyddiaeth môr-ladron yn benodol, ond gadewch i ni roi cyflwyniad cyflym i'r rhai nad ydynt yn rhy gyfarwydd. Mae'r byd yn cynhyrchu mwy o wybodaeth a diwylliant nag erioed o'r blaen, ond hefyd mae mwy ohono'n cael ei golli nag erioed o'r blaen. Mae dynoliaeth yn bennaf yn ymddiried mewn corfforaethau fel cyhoeddwyr academaidd, gwasanaethau ffrydio, a chwmnïau cyfryngau cymdeithasol gyda'r etifeddiaeth hon, ac nid ydynt wedi profi i fod yn stiwardiaid gwych yn aml. Edrychwch ar y ffilm ddogfen Digital Amnesia, neu unrhyw sgwrs gan Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Mae rhai sefydliadau sy'n gwneud gwaith da o archifo cymaint ag y gallant, ond maent wedi'u rhwymo gan y gyfraith. Fel môr-ladron, rydym mewn sefyllfa unigryw i archifo casgliadau na allant gyffwrdd â nhw, oherwydd gorfodi hawlfraint neu gyfyngiadau eraill. Gallwn hefyd ddrychio casgliadau lawer gwaith drosodd, ar draws y byd, gan gynyddu'r siawns o gadwraeth briodol."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Am y tro, ni fyddwn yn mynd i mewn i drafodaethau am fanteision ac anfanteision eiddo deallusol, moesoldeb torri'r gyfraith, myfyrdodau ar sensoriaeth, neu'r mater o fynediad at wybodaeth a diwylliant. Gyda hynny i gyd allan o'r ffordd, gadewch i ni blymio i'r <em>sut</em>. Byddwn yn rhannu sut y daeth ein tîm yn archifyddion môr-ladron, a'r gwersi a ddysgwyd gennym ar hyd y ffordd. Mae llawer o heriau pan fyddwch yn cychwyn ar y daith hon, a gobeithio y gallwn eich helpu trwy rai ohonynt."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Cymuned"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Efallai y bydd yr her gyntaf yn un sy'n syndod. Nid yw'n broblem dechnegol, nac yn broblem gyfreithiol. Mae'n broblem seicolegol: gall gwneud y gwaith hwn yn y cysgodion fod yn hynod unig. Yn dibynnu ar yr hyn rydych chi'n bwriadu ei wneud, a'ch model bygythiad, efallai y bydd yn rhaid i chi fod yn ofalus iawn. Ar un pen o'r sbectrwm mae gennym bobl fel Alexandra Elbakyan*, sylfaenydd Sci-Hub, sy'n agored iawn am ei gweithgareddau. Ond mae hi mewn perygl uchel o gael ei harestio pe bai'n ymweld â gwlad orllewinol ar hyn o bryd, a gallai wynebu degawdau o amser carchar. Ai dyna'r risg y byddech chi'n barod i'w chymryd? Rydym ar ben arall y sbectrwm; yn ofalus iawn i beidio â gadael unrhyw olion, ac yn cael diogelwch gweithredol cryf."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Fel y soniwyd ar HN gan \"ynno\", doedd Alexandra ddim eisiau cael ei hadnabod i ddechrau: \"Roedd ei gweinyddion wedi'u sefydlu i allyrru negeseuon gwall manwl o PHP, gan gynnwys llwybr llawn y ffeil ffynhonnell ffaelu, a oedd o dan gyfeiriadur /home/<USER>'i atodi i'w henw go iawn. Cyn y datguddiad hwn, roedd hi'n ddienw.\" Felly, defnyddiwch enwau defnyddiwr ar hap ar y cyfrifiaduron rydych chi'n eu defnyddio ar gyfer y pethau hyn, rhag ofn i chi gamffurfio rhywbeth."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Fodd bynnag, mae'r cyfrinachedd hwnnw'n dod â chost seicolegol. Mae'r rhan fwyaf o bobl yn caru cael eu cydnabod am y gwaith maen nhw'n ei wneud, ac eto ni allwch gymryd unrhyw gredyd am hyn mewn bywyd go iawn. Gall pethau syml hyd yn oed fod yn heriol, fel ffrindiau yn gofyn i chi beth rydych chi wedi bod yn ei wneud (ar ryw adeg mae \"cymysgu gyda fy NAS / homelab\" yn mynd yn hen)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dyma pam mae mor bwysig dod o hyd i ryw gymuned. Gallwch ildio rhywfaint o ddiogelwch gweithredol trwy ymddiried mewn rhai ffrindiau agos iawn, y gwyddoch y gallwch ymddiried ynddynt yn ddwfn. Hyd yn oed wedyn byddwch yn ofalus i beidio â rhoi unrhyw beth yn ysgrifenedig, rhag ofn y bydd yn rhaid iddynt drosglwyddo eu negeseuon e-bost i'r awdurdodau, neu os yw eu dyfeisiau wedi'u cyfaddawdu mewn rhyw ffordd arall."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Gwell fyth yw dod o hyd i rai môr-ladron cydymaith. Os yw eich ffrindiau agos yn awyddus i ymuno â chi, gwych! Fel arall, efallai y byddwch yn gallu dod o hyd i eraill ar-lein. Yn anffodus mae hyn yn dal i fod yn gymuned gilfach. Hyd yn hyn rydym wedi dod o hyd i lond llaw o eraill sy'n weithgar yn y maes hwn. Mae'n ymddangos bod fforwmau Library Genesis, a r/DataHoarder yn lleoedd cychwyn da. Mae'r Tîm Archif hefyd yn cynnwys unigolion o'r un meddylfryd, er eu bod yn gweithredu o fewn y gyfraith (hyd yn oed os yw mewn rhai ardaloedd llwyd o'r gyfraith). Mae'r golygfeydd \"warez\" a môr-ladrad traddodiadol hefyd yn cynnwys pobl sy'n meddwl mewn ffyrdd tebyg."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Rydym yn agored i syniadau ar sut i feithrin cymuned a thrafod syniadau. Mae croeso i chi anfon neges atom ar Twitter neu Reddit. Efallai y gallem gynnal rhyw fath o fforwm neu grŵp sgwrsio. Un her yw y gall hyn gael ei sensro'n hawdd wrth ddefnyddio llwyfannau cyffredin, felly byddai'n rhaid i ni ei gynnal ein hunain. Mae hefyd gyfaddawd rhwng cael y trafodaethau hyn yn gwbl gyhoeddus (mwy o ymgysylltiad posibl) yn erbyn eu gwneud yn breifat (peidio â gadael i \"dargedau\" posibl wybod ein bod ar fin eu crafu). Bydd yn rhaid i ni feddwl am hynny. Gadewch i ni wybod os oes gennych ddiddordeb yn hyn!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Prosiectau"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Pan fyddwn yn gwneud prosiect, mae ganddo ychydig o gamau:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Dewis parth / athroniaeth: Ble rydych chi'n fras eisiau canolbwyntio, a pham? Beth yw eich angerdd, sgiliau, a'ch amgylchiadau unigryw y gallwch eu defnyddio er eich budd?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Dewis targed: Pa gasgliad penodol fyddwch chi'n ei adlewyrchu?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Crafu metadata: Catalogio gwybodaeth am y ffeiliau, heb lawrlwytho'r ffeiliau (sydd yn aml yn llawer mwy) eu hunain."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Dewis data: Yn seiliedig ar y metadata, culhau pa ddata sy'n fwyaf perthnasol i'w archifo ar hyn o bryd. Gallai fod yn bopeth, ond yn aml mae ffordd resymol o arbed lle a lled band."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Crafu data: Cael y data mewn gwirionedd."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Dosbarthu: Pecynnu mewn torrenti, ei gyhoeddi yn rhywle, cael pobl i'w ledaenu."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Nid yw'r rhain yn gamau cwbl annibynnol, ac yn aml mae mewnwelediadau o gam diweddarach yn eich anfon yn ôl i gam cynharach. Er enghraifft, yn ystod crafu metadata efallai y byddwch yn sylweddoli bod gan y targed a ddewiswyd gennych fecanweithiau amddiffynnol y tu hwnt i'ch lefel sgiliau (fel blociau IP), felly rydych yn mynd yn ôl ac yn dod o hyd i darged gwahanol."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Dewis parth / athroniaeth"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Nid oes prinder gwybodaeth a threftadaeth ddiwylliannol i'w hachub, sy'n gallu bod yn llethol. Dyna pam ei bod yn aml yn ddefnyddiol cymryd eiliad a meddwl am beth all eich cyfraniad fod."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Mae gan bawb ffordd wahanol o feddwl am hyn, ond dyma rai cwestiynau y gallech eu gofyn i chi'ch hun:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Pam ydych chi'n ymddiddori yn hyn? Beth yw eich angerdd? Os gallwn gael criw o bobl sy'n archifo'r mathau o bethau y maent yn gofalu amdanynt yn benodol, byddai hynny'n cwmpasu llawer! Byddwch yn gwybod llawer mwy na'r person cyffredin am eich angerdd, fel pa ddata sy'n bwysig i'w gadw, beth yw'r casgliadau a'r cymunedau ar-lein gorau, ac yn y blaen."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Pa sgiliau sydd gennych y gallwch eu defnyddio er eich budd? Er enghraifft, os ydych yn arbenigwr diogelwch ar-lein, gallwch ddod o hyd i ffyrdd o drechu blociau IP ar gyfer targedau diogel. Os ydych yn wych am drefnu cymunedau, yna efallai y gallwch gasglu rhai pobl at ei gilydd o amgylch nod. Mae'n ddefnyddiol gwybod rhywfaint o raglennu serch hynny, os dim ond i gadw diogelwch gweithredol da trwy gydol y broses hon."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Faint o amser sydd gennych ar gyfer hyn? Ein cyngor fyddai dechrau'n fach a gwneud prosiectau mwy wrth i chi ddod i arfer â hynny, ond gall ddod yn hollol amsugno."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Beth fyddai'n faes uchel-lever i ganolbwyntio arno? Os ydych chi'n mynd i dreulio X oriau ar archifo môr-ladron, yna sut allwch chi gael y \"bang for your buck\" mwyaf?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Beth yw'r ffyrdd unigryw rydych chi'n meddwl am hyn? Efallai bod gennych rai syniadau neu ddulliau diddorol y gallai eraill fod wedi'u colli."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Yn ein hachos ni, roeddem yn poeni'n arbennig am gadw gwyddoniaeth yn y tymor hir. Roeddem yn gwybod am Library Genesis, a sut roedd wedi'i adlewyrchu'n llawn lawer gwaith drosodd gan ddefnyddio torrenti. Roeddem wrth ein bodd â'r syniad hwnnw. Yna un diwrnod, ceisiodd un ohonom ddod o hyd i rai llyfrau gwyddonol ar Library Genesis, ond ni allai ddod o hyd iddynt, gan godi amheuaeth ynghylch pa mor gyflawn oedd mewn gwirionedd. Yna chwiliwyd y llyfrau hynny ar-lein, a'u canfod mewn mannau eraill, a blannodd yr hedyn ar gyfer ein prosiect. Hyd yn oed cyn i ni wybod am y Z-Library, roedd gennym y syniad o beidio â cheisio casglu'r holl lyfrau hynny â llaw, ond i ganolbwyntio ar adlewyrchu casgliadau presennol, a'u cyfrannu yn ôl i Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Dewis targed"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Felly, mae gennym ein hardal rydym yn edrych arni, nawr pa gasgliad penodol y dylem ei adlewyrchu? Mae yna ychydig o bethau sy'n gwneud targed da:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Mawr"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unigryw: nid yw eisoes wedi'i gwmpasu'n dda gan brosiectau eraill."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Hygyrch: nid yw'n defnyddio llawer o haenau o amddiffyniad i'ch atal rhag crafu eu metadata a'u data."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Mewnwelediad arbennig: mae gennych rywfaint o wybodaeth arbennig am y targed hwn, fel eich bod rywsut yn cael mynediad arbennig i'r casgliad hwn, neu eich bod wedi darganfod sut i drechu eu hamddiffynfeydd. Nid yw hyn yn ofynnol (nid yw ein prosiect sydd ar ddod yn gwneud unrhyw beth arbennig), ond mae'n sicr yn helpu!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Pan wnaethom ddod o hyd i'n llyfrau gwyddoniaeth ar wefannau heblaw Library Genesis, ceisiasom ddarganfod sut y gwnaethant eu ffordd ar y rhyngrwyd. Yna daethom o hyd i'r Z-Library, a sylweddolon ni, er nad yw'r rhan fwyaf o lyfrau'n ymddangos yno gyntaf, maent yn y pen draw yn gorffen yno. Dysgom am ei berthynas â Library Genesis, a'r strwythur cymhelliant (ariannol) a'r rhyngwyneb defnyddiwr uwchraddol, y ddau ohonynt yn gwneud casgliad llawer mwy cyflawn. Yna gwnaethom ychydig o grawsio metadata a data cychwynnol, a sylweddolon ni y gallem fynd o gwmpas eu cyfyngiadau lawrlwytho IP, gan ddefnyddio mynediad arbennig un o'n haelodau i lawer o weinyddion dirprwy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Wrth i chi archwilio targedau gwahanol, mae eisoes yn bwysig cuddio eich olion trwy ddefnyddio VPNs a chyfeiriadau e-bost tafladwy, y byddwn yn siarad mwy amdanynt yn nes ymlaen."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Crafu metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Gadewch i ni fynd ychydig yn fwy technegol yma. Ar gyfer crafu'r metadata o wefannau, rydym wedi cadw pethau'n eithaf syml. Rydym yn defnyddio sgriptiau Python, weithiau curl, a chronfa ddata MySQL i storio'r canlyniadau ynddi. Nid ydym wedi defnyddio unrhyw feddalwedd crafu ffansi sy'n gallu mapio gwefannau cymhleth, gan mai hyd yma dim ond angen crafu un neu ddau fath o dudalennau trwy gyfrif drwy IDs a pharsio'r HTML. Os nad oes tudalennau hawdd eu cyfrif, yna efallai y bydd angen crafwr priodol arnoch sy'n ceisio dod o hyd i bob tudalen."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Cyn i chi ddechrau crafu gwefan gyfan, ceisiwch wneud hynny â llaw am ychydig. Ewch drwy ychydig ddwsin o dudalennau eich hun, i gael syniad o sut mae hynny'n gweithio. Weithiau byddwch eisoes yn rhedeg i mewn i flociau IP neu ymddygiad diddorol arall fel hyn. Mae'r un peth yn wir am grawsio data: cyn mynd yn rhy ddwfn i'r targed hwn, gwnewch yn siŵr y gallwch chi lawrlwytho ei ddata yn effeithiol."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "I fynd o gwmpas cyfyngiadau, mae yna ychydig o bethau y gallwch chi roi cynnig arnynt. A oes unrhyw gyfeiriadau IP neu weinyddion eraill sy'n cynnal yr un data ond nad oes ganddynt yr un cyfyngiadau? A oes unrhyw bwyntiau API nad oes ganddynt gyfyngiadau, tra bod eraill yn gwneud hynny? Ar ba gyfradd lawrlwytho mae eich IP yn cael ei flocio, ac am ba mor hir? Neu a ydych chi ddim yn cael eich blocio ond yn cael eich arafu i lawr? Beth os ydych chi'n creu cyfrif defnyddiwr, sut mae pethau'n newid wedyn? A allwch chi ddefnyddio HTTP/2 i gadw cysylltiadau ar agor, ac a yw hynny'n cynyddu'r gyfradd y gallwch chi ofyn am dudalennau? A oes tudalennau sy'n rhestru sawl ffeil ar unwaith, ac a yw'r wybodaeth a restrir yno'n ddigonol?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Pethau rydych chi'n debygol o fod eisiau eu cadw yn cynnwys:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Teitl"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Enw ffeil / lleoliad"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: gall fod yn ID mewnol, ond mae IDs fel ISBN neu DOI yn ddefnyddiol hefyd."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Maint: i gyfrifo faint o le disg sydd ei angen arnoch."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): i gadarnhau eich bod wedi lawrlwytho'r ffeil yn iawn."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Dyddiad ychwanegu / newid: fel y gallwch ddod yn ôl yn nes ymlaen a lawrlwytho ffeiliau nad ydych wedi'u lawrlwytho o'r blaen (er y gallwch chi hefyd ddefnyddio'r ID neu'r hash ar gyfer hyn yn aml)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Disgrifiad, categori, tagiau, awduron, iaith, ac ati."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Rydym fel arfer yn gwneud hyn mewn dau gam. Yn gyntaf rydym yn lawrlwytho'r ffeiliau HTML crai, fel arfer yn uniongyrchol i MySQL (i osgoi llawer o ffeiliau bach, y byddwn yn siarad mwy amdanynt isod). Yna, mewn cam ar wahân, rydym yn mynd drwy'r ffeiliau HTML hynny ac yn eu parsu i mewn i dablau MySQL gwirioneddol. Fel hyn nid oes rhaid i chi ail-lawrlwytho popeth o'r dechrau os byddwch yn darganfod gwall yn eich cod parsu, gan y gallwch chi ailbrosesu'r ffeiliau HTML gyda'r cod newydd. Mae hefyd yn aml yn haws i baralelu'r cam prosesu, gan arbed rhywfaint o amser (a gallwch ysgrifennu'r cod prosesu tra bod y crafu yn rhedeg, yn lle gorfod ysgrifennu'r ddau gam ar unwaith)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Yn olaf, nodwch mai ar gyfer rhai targedau dim ond crafu metadata sydd ar gael. Mae yna rai casgliadau metadata enfawr allan yna nad ydynt wedi'u cadw'n iawn."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Dethol data"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Yn aml gallwch ddefnyddio'r metadata i ffiguro allan is-set resymol o ddata i'w lawrlwytho. Hyd yn oed os ydych yn y pen draw eisiau lawrlwytho'r holl ddata, gall fod yn ddefnyddiol blaenoriaethu'r eitemau pwysicaf yn gyntaf, rhag ofn eich bod yn cael eich canfod a bod amddiffynfeydd yn cael eu gwella, neu oherwydd y byddai angen i chi brynu mwy o ddisgiau, neu'n syml oherwydd bod rhywbeth arall yn codi yn eich bywyd cyn i chi allu lawrlwytho popeth."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Er enghraifft, gallai casgliad gynnwys sawl argraffiad o'r un adnodd sylfaenol (fel llyfr neu ffilm), lle mae un wedi'i farcio fel yr ansawdd gorau. Byddai arbed y rhain yn gyntaf yn gwneud llawer o synnwyr. Efallai y byddwch yn y pen draw eisiau arbed pob argraffiad, gan y gallai'r metadata fod wedi'i dagio'n anghywir mewn rhai achosion, neu efallai y bydd cyfaddawdau anhysbys rhwng argraffiadau (er enghraifft, efallai mai'r \"argraffiad gorau\" yw'r gorau yn y rhan fwyaf o ffyrdd ond yn waeth mewn ffyrdd eraill, fel ffilm â phenderfyniad uwch ond heb is-deitlau)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Gallwch hefyd chwilio eich cronfa ddata metadata i ddod o hyd i bethau diddorol. Beth yw'r ffeil fwyaf sy'n cael ei chynnal, a pham mae mor fawr? Beth yw'r ffeil leiaf? A oes patrymau diddorol neu annisgwyl o ran categorïau penodol, ieithoedd, ac ati? A oes teitlau dyblyg neu debyg iawn? A oes patrymau i pryd y cafodd data ei ychwanegu, fel un diwrnod lle cafodd llawer o ffeiliau eu hychwanegu ar unwaith? Gallwch yn aml ddysgu llawer trwy edrych ar y set ddata mewn ffyrdd gwahanol."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Yn ein hachos ni, fe wnaethom ddad-ddyblygu llyfrau Z-Library yn erbyn y hashiau md5 yn Library Genesis, gan arbed llawer o amser lawrlwytho a lle ar ddisg. Mae hon yn sefyllfa eithaf unigryw serch hynny. Yn y rhan fwyaf o achosion nid oes cronfeydd data cynhwysfawr o ba ffeiliau sydd eisoes wedi'u cadw'n iawn gan fôr-ladron eraill. Mae hyn ynddo'i hun yn gyfle enfawr i rywun allan yna. Byddai'n wych cael trosolwg wedi'i ddiweddaru'n rheolaidd o bethau fel cerddoriaeth a ffilmiau sydd eisoes wedi'u hau'n eang ar wefannau torrent, ac felly'n flaenoriaeth is i'w cynnwys mewn drychau môr-ladron."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Crafu data"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nawr rydych chi'n barod i lawrlwytho'r data mewn swmp. Fel y soniwyd o'r blaen, ar y pwynt hwn dylech eisoes fod wedi lawrlwytho criw o ffeiliau â llaw, i ddeall yn well ymddygiad a chyfyngiadau'r targed. Fodd bynnag, bydd dal i fod yn sioc i chi unwaith y byddwch yn dechrau lawrlwytho llawer o ffeiliau ar unwaith."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Ein cyngor yma yw cadw pethau'n syml. Dechreuwch trwy lawrlwytho criw o ffeiliau. Gallwch ddefnyddio Python, ac yna ehangu i sawl edefyn. Ond weithiau hyd yn oed yn symlach yw cynhyrchu ffeiliau Bash yn uniongyrchol o'r gronfa ddata, ac yna rhedeg sawl un ohonynt mewn sawl ffenestr derfynell i gynyddu. Tric technegol cyflym sy'n werth ei grybwyll yma yw defnyddio OUTFILE yn MySQL, y gallwch ei ysgrifennu unrhyw le os ydych yn anablu \"secure_file_priv\" yn mysqld.cnf (a sicrhewch hefyd anablu/gor-reoli AppArmor os ydych ar Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Rydym yn storio'r data ar ddisgiau caled syml. Dechreuwch gyda beth bynnag sydd gennych, ac ehangu'n araf. Gall fod yn llethol meddwl am storio cannoedd o TBs o ddata. Os mai dyna'r sefyllfa rydych chi'n ei hwynebu, dim ond rhoi is-set dda allan yn gyntaf, ac yn eich cyhoeddiad gofynnwch am help i storio'r gweddill. Os ydych chi eisiau cael mwy o yriannau caled eich hun, yna mae r/DataHoarder yn cynnig rhai adnoddau da ar gael bargen dda."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Ceisiwch beidio â phoeni gormod am systemau ffeil soffistigedig. Mae'n hawdd syrthio i'r twll cwningen o sefydlu pethau fel ZFS. Un manylyn technegol i fod yn ymwybodol ohono, fodd bynnag, yw nad yw llawer o systemau ffeil yn delio'n dda â llawer o ffeiliau. Rydym wedi canfod bod ffordd osgoi syml yw creu sawl cyfeiriadur, e.e. ar gyfer gwahanol ystodau ID neu ragddodiaid hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Ar ôl lawrlwytho'r data, sicrhewch wirio cywirdeb y ffeiliau gan ddefnyddio hashiau yn y metadata, os ydynt ar gael."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Dosbarthu"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Mae gennych y data, gan roi i chi feddiant o ddrych môr-ladron cyntaf y byd o'ch targed (yn fwyaf tebygol). Mewn sawl ffordd mae'r rhan anoddaf drosodd, ond mae'r rhan fwyaf peryglus o hyd o'ch blaen. Wedi'r cyfan, hyd yma rydych wedi bod yn ddirgel; hedfan o dan y radar. Y cyfan oedd rhaid i chi ei wneud oedd defnyddio VPN da drwyddi draw, peidio â llenwi eich manylion personol mewn unrhyw ffurflenni (duh), ac efallai defnyddio sesiwn porwr arbennig (neu hyd yn oed gyfrifiadur gwahanol)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nawr mae'n rhaid i chi ddosbarthu'r data. Yn ein hachos ni roeddem yn gyntaf eisiau cyfrannu'r llyfrau yn ôl i Library Genesis, ond yna darganfyddom yn gyflym yr anawsterau yn hynny (didoli ffuglen yn erbyn ffeithiol). Felly penderfynasom ar ddosbarthu gan ddefnyddio torrenti arddull Library Genesis. Os oes gennych y cyfle i gyfrannu at brosiect presennol, yna gallai hynny arbed llawer o amser i chi. Fodd bynnag, nid oes llawer o ddrychau môr-ladron wedi'u trefnu'n dda ar gael ar hyn o bryd."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Felly gadewch i ni ddweud eich bod yn penderfynu dosbarthu torrenti eich hun. Ceisiwch gadw'r ffeiliau hynny'n fach, fel eu bod yn hawdd eu hadlewyrchu ar wefannau eraill. Bydd yn rhaid i chi hau'r torrenti eich hun, tra'n aros yn ddienw. Gallwch ddefnyddio VPN (gyda neu heb anfon porth), neu dalu gyda Bitcoins wedi'u troelli am Seedbox. Os nad ydych yn gwybod beth mae rhai o'r termau hynny'n ei olygu, bydd gennych lawer o ddarllen i'w wneud, gan ei bod yn bwysig eich bod yn deall y cyfaddawdau risg yma."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Gallwch gynnal y ffeiliau torrent eu hunain ar wefannau torrent presennol. Yn ein hachos ni, dewisom gynnal gwefan mewn gwirionedd, gan ein bod hefyd eisiau lledaenu ein hathroniaeth mewn ffordd glir. Gallwch wneud hyn eich hun mewn modd tebyg (rydym yn defnyddio Njalla ar gyfer ein parthau a'n cynnal, wedi'u talu am gyda Bitcoins wedi'u troelli), ond hefyd mae croeso i chi gysylltu â ni i gael i ni gynnal eich torrenti. Rydym yn edrych i adeiladu mynegai cynhwysfawr o ddrychau môr-ladron dros amser, os yw'r syniad hwn yn dal ymlaen."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "O ran dewis VPN, mae llawer wedi'i ysgrifennu am hyn eisoes, felly dim ond ailadrodd cyngor cyffredinol dewis yn ôl enw da y byddwn ni. Polisïau dim-log wedi'u profi yn y llys gyda hanes hir o amddiffyn preifatrwydd yw'r opsiwn risg isaf, yn ein barn ni. Nodwch hyd yn oed pan fyddwch yn gwneud popeth yn iawn, ni allwch byth gyrraedd risg sero. Er enghraifft, wrth hau eich torrenti, gall actor gwladwriaethol hynod gymhellol edrych ar lifoedd data sy'n dod i mewn ac allan ar gyfer gweinyddion VPN, a dod i'r casgliad pwy ydych chi. Neu gallwch chi ddim ond gwneud camgymeriad rywsut. Mae'n debyg ein bod eisoes wedi gwneud, a byddwn eto. Yn ffodus, nid yw gwladwriaethau cenedlaethol yn poeni <em>cymaint</em> am fôr-ladrad."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Un penderfyniad i'w wneud ar gyfer pob prosiect, yw a ddylid ei gyhoeddi gan ddefnyddio'r un hunaniaeth ag o'r blaen, neu beidio. Os byddwch yn parhau i ddefnyddio'r un enw, yna gall camgymeriadau mewn diogelwch gweithredol o brosiectau cynharach ddod yn ôl i'ch brathu. Ond mae cyhoeddi o dan enwau gwahanol yn golygu nad ydych yn adeiladu enw da sy'n para'n hirach. Dewisom gael diogelwch gweithredol cryf o'r dechrau fel y gallwn barhau i ddefnyddio'r un hunaniaeth, ond ni fyddwn yn petruso i gyhoeddi o dan enw gwahanol os ydym yn gwneud camgymeriad neu os yw'r amgylchiadau'n galw am hynny."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Gall cael y gair allan fod yn anodd. Fel y dywedasom, mae hwn yn dal i fod yn gymuned gilfach. Yn wreiddiol postiwyd ar Reddit, ond cafodd wir lwyddiant ar Hacker News. Am nawr ein hargymhelliad yw ei bostio mewn ychydig o leoedd a gweld beth sy'n digwydd. Ac eto, cysylltwch â ni. Byddem wrth ein bodd yn lledaenu'r gair am fwy o ymdrechion archif môr-ladron."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Casgliad"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Gobeithio bod hyn yn ddefnyddiol i archifwyr môr-ladron newydd sy'n dechrau. Rydym yn gyffrous i'ch croesawu i'r byd hwn, felly peidiwch ag oedi i gysylltu. Gadewch i ni gadw cymaint o wybodaeth a diwylliant y byd ag y gallwn, a'i adlewyrchu ymhell ac agos."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Cyflwyno Drych Llyfrgell y Môr-ladron: Cadw 7TB o lyfrau (nad ydynt yn Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Mae'r prosiect hwn (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>) yn anelu at gyfrannu at gadw a rhyddhau gwybodaeth ddynol. Rydym yn gwneud ein cyfraniad bach a gostyngedig, yn ôl troed y mawrion o'n blaen."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Mae ffocws y prosiect hwn yn cael ei ddangos gan ei enw:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Môr-ladron</strong> - Rydym yn fwriadol yn torri cyfraith hawlfraint yn y rhan fwyaf o wledydd. Mae hyn yn ein galluogi i wneud rhywbeth na all endidau cyfreithiol ei wneud: sicrhau bod llyfrau'n cael eu hadlewyrchu ymhell ac agos."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Llyfrgell</strong> - Fel y rhan fwyaf o lyfrgelloedd, rydym yn canolbwyntio'n bennaf ar ddeunyddiau ysgrifenedig fel llyfrau. Efallai y byddwn yn ehangu i fathau eraill o gyfryngau yn y dyfodol."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Drych</strong> - Rydym yn llythrennol yn ddrych o lyfrgelloedd presennol. Rydym yn canolbwyntio ar gadwraeth, nid ar wneud llyfrau'n hawdd eu chwilio a'u lawrlwytho (mynediad) neu feithrin cymuned fawr o bobl sy'n cyfrannu llyfrau newydd (ffynhonnell)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Y llyfrgell gyntaf yr ydym wedi'i hadlewyrchu yw Z-Library. Mae hon yn llyfrgell boblogaidd (ac anghyfreithlon). Maent wedi cymryd casgliad Library Genesis a'i wneud yn hawdd ei chwilio. Ar ben hynny, maent wedi dod yn effeithiol iawn wrth ofyn am gyfraniadau llyfrau newydd, trwy gymell defnyddwyr sy'n cyfrannu gyda gwahanol fanteision. Ar hyn o bryd, nid ydynt yn cyfrannu'r llyfrau newydd hyn yn ôl i Library Genesis. Ac yn wahanol i Library Genesis, nid ydynt yn gwneud eu casgliad yn hawdd ei adlewyrchu, sy'n atal cadwraeth eang. Mae hyn yn bwysig i'w model busnes, gan eu bod yn codi tâl am fynediad at eu casgliad yn gyfan gwbl (mwy na 10 llyfr y dydd)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nid ydym yn gwneud barn foesol am godi arian am fynediad swmpus i gasgliad llyfrau anghyfreithlon. Mae'n sicr bod y Llyfrgell Z wedi bod yn llwyddiannus wrth ehangu mynediad at wybodaeth, a chael mwy o lyfrau. Rydym yma i wneud ein rhan: sicrhau cadwraeth hirdymor y casgliad preifat hwn."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Hoffem eich gwahodd i helpu i gadw a rhyddhau gwybodaeth ddynol trwy lawrlwytho a hadu ein toriadau. Gweler tudalen y prosiect am fwy o wybodaeth am sut mae'r data wedi'i drefnu."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Hoffem hefyd eich gwahodd yn fawr i gyfrannu eich syniadau ar ba gasgliadau i'w hadlewyrchu nesaf, a sut i fynd ati. Gyda'n gilydd gallwn gyflawni llawer. Dim ond cyfraniad bach yw hwn ymhlith nifer di-rif o rai eraill. Diolch, am bopeth a wnewch."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nid ydym yn cysylltu â'r ffeiliau o'r blog hwn. Dewch o hyd iddo eich hun.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump ISBNdb, neu Faint o Lyfrau Sy'n Cael eu Cadw am Byth?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Pe baem yn dad-ddyblygu'r ffeiliau o lyfrgelloedd cysgodol yn iawn, pa ganran o'r holl lyfrau yn y byd yr ydym wedi'u cadw?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Gyda Drych Llyfrgell y Môr-ladron (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>), ein nod yw cymryd yr holl lyfrau yn y byd, a'u cadw am byth.<sup>1</sup> Rhwng ein toriadau Z-Library, a'r toriadau gwreiddiol Library Genesis, mae gennym 11,783,153 o ffeiliau. Ond faint yw hynny, mewn gwirionedd? Pe baem yn dad-ddyblygu'r ffeiliau hynny'n iawn, pa ganran o'r holl lyfrau yn y byd yr ydym wedi'u cadw? Hoffem wir gael rhywbeth fel hyn:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% o o dreftadaeth ysgrifenedig dynoliaeth wedi'i gadw am byth"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Ar gyfer canran, mae angen rhifydd arnom: cyfanswm nifer y llyfrau a gyhoeddwyd erioed.<sup>2</sup> Cyn diwedd Google Books, ceisiodd peiriannydd ar y prosiect, Leonid Taycher, <a %(booksearch_blogspot)s>amcangyfrif</a> y nifer hwn. Daeth i'r casgliad — yn ddoniol — gyda 129,864,880 (“o leiaf tan ddydd Sul”). Amcangyfrifodd y nifer hwn trwy adeiladu cronfa ddata unedig o'r holl lyfrau yn y byd. Ar gyfer hyn, tynnodd ynghyd wahanol setiau data ac yna eu cyfuno mewn gwahanol ffyrdd."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Fel nodyn cyflym, mae yna berson arall a geisiodd gatalogio'r holl lyfrau yn y byd: Aaron Swartz, yr ymgyrchydd digidol diweddar a chyd-sylfaenydd Reddit.<sup>3</sup> Dechreuodd <a %(youtube)s>Open Library</a> gyda'r nod o greu “un dudalen we ar gyfer pob llyfr a gyhoeddwyd erioed”, gan gyfuno data o lawer o wahanol ffynonellau. Talodd y pris eithaf am ei waith cadwraeth digidol pan gafodd ei erlyn am lawrlwytho papurau academaidd yn swmpus, gan arwain at ei hunanladdiad. Nid oes angen dweud, dyma un o'r rhesymau pam mae ein grŵp yn defnyddio ffugenwau, a pham ein bod yn ofalus iawn. Mae Open Library yn dal i gael ei redeg yn arwrol gan bobl yn yr Internet Archive, gan barhau â threftadaeth Aaron. Byddwn yn dychwelyd at hyn yn ddiweddarach yn y post hwn."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Yn y blogbost Google, mae Taycher yn disgrifio rhai o'r heriau gyda chynnal amcangyfrif o'r nifer hwn. Yn gyntaf, beth sy'n ffurfio llyfr? Mae yna ychydig o ddiffiniadau posibl:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copïau corfforol.</strong> Yn amlwg nid yw hyn yn ddefnyddiol iawn, gan eu bod yn ddim ond dyblygiadau o'r un deunydd. Byddai'n wych pe gallem gadw'r holl nodiadau y mae pobl yn eu gwneud mewn llyfrau, fel \"scribbles in the margins\" enwog Fermat. Ond gwaetha'r modd, bydd hynny'n parhau i fod yn freuddwyd archifydd."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Gweithiau”.</strong> Er enghraifft, “Harry Potter and the Chamber of Secrets” fel cysyniad rhesymegol, yn cwmpasu'r holl fersiynau ohono, fel gwahanol gyfieithiadau ac ailargraffiadau. Mae hwn yn fath o ddiffiniad defnyddiol, ond gall fod yn anodd tynnu'r llinell o'r hyn sy'n cyfrif. Er enghraifft, mae'n debyg ein bod am gadw gwahanol gyfieithiadau, er nad yw ailargraffiadau gyda newidiadau bach yn unig efallai mor bwysig."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Argraffiadau”.</strong> Yma rydych yn cyfrif pob fersiwn unigryw o lyfr. Os yw unrhyw beth amdano yn wahanol, fel clawr gwahanol neu ragair gwahanol, mae'n cyfrif fel argraffiad gwahanol."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Ffeiliau.</strong> Wrth weithio gyda llyfrgelloedd cysgodol fel Library Genesis, Sci-Hub, neu Z-Library, mae ystyriaeth ychwanegol. Gall fod sawl sgan o'r un argraffiad. A gall pobl wneud fersiynau gwell o ffeiliau presennol, trwy sganio'r testun gan ddefnyddio OCR, neu gywiro tudalennau a gafodd eu sganio ar ongl. Rydym am gyfrif y ffeiliau hyn fel un argraffiad yn unig, a fyddai'n gofyn am metadata da, neu ddeduplication gan ddefnyddio mesurau tebygrwydd dogfennau."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Mae “Argraffiadau” yn ymddangos fel y diffiniad mwyaf ymarferol o'r hyn yw “llyfrau”. Yn gyfleus, defnyddir y diffiniad hwn hefyd ar gyfer neilltuo rhifau ISBN unigryw. Mae ISBN, neu Rhif Safonol Rhyngwladol Llyfr, yn cael ei ddefnyddio'n gyffredin ar gyfer masnach ryngwladol, gan ei fod wedi'i integreiddio â'r system cod bar rhyngwladol (”Rhif Erthygl Rhyngwladol”). Os ydych am werthu llyfr mewn siopau, mae angen cod bar arno, felly rydych yn cael ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Mae blogbost Taycher yn sôn bod tra bod ISBNs yn ddefnyddiol, nid ydynt yn gyffredinol, gan mai dim ond yn wirioneddol fabwysiadwyd yn y saithdegau canol, ac nid ym mhobman o gwmpas y byd. Serch hynny, mae ISBN yn debygol o fod yr adnabodwr mwyaf eang o argraffiadau llyfrau, felly dyma ein man cychwyn gorau. Os gallwn ddod o hyd i'r holl ISBNs yn y byd, cawn restr ddefnyddiol o ba lyfrau sydd angen eu cadw o hyd."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Felly, ble ydym yn cael y data? Mae nifer o ymdrechion presennol sy'n ceisio llunio rhestr o'r holl lyfrau yn y byd:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Wedi'r cyfan, gwnaethant yr ymchwil hwn ar gyfer Google Books. Fodd bynnag, nid yw eu metadata yn hygyrch yn swmpus ac yn eithaf anodd ei sgrapio."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Fel y soniwyd o'r blaen, dyma eu cenhadaeth gyfan. Maent wedi dod o hyd i symiau enfawr o ddata llyfrgell o lyfrgelloedd cydweithredol ac archifau cenedlaethol, ac yn parhau i wneud hynny. Maent hefyd yn cael llyfrgellwyr gwirfoddol a thîm technegol sy'n ceisio diddyfnu cofnodion, a'u tagio gyda phob math o metadata. Yn fwyaf oll, mae eu set ddata yn gwbl agored. Gallwch syml <a %(openlibrary)s>ei lawrlwytho</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Mae hwn yn wefan a redir gan yr elusen OCLC, sy'n gwerthu systemau rheoli llyfrgelloedd. Maent yn agregu metadata llyfrau o lawer o lyfrgelloedd, ac yn ei wneud ar gael trwy wefan WorldCat. Fodd bynnag, maent hefyd yn gwneud arian trwy werthu'r data hwn, felly nid yw ar gael ar gyfer lawrlwytho swmpus. Maent yn cael rhai setiau data swmpus mwy cyfyngedig ar gael ar gyfer lawrlwytho, mewn cydweithrediad â llyfrgelloedd penodol."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dyma bwnc y blogbost hwn. Mae ISBNdb yn sgrapio gwahanol wefannau am metadata llyfrau, yn enwedig data prisio, y maent wedyn yn ei werthu i werthwyr llyfrau, fel y gallant brisio eu llyfrau yn unol â gweddill y farchnad. Gan fod ISBNs yn eithaf cyffredinol y dyddiau hyn, maent yn effeithiol wedi adeiladu “tudalen we ar gyfer pob llyfr”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Amrywiol systemau llyfrgell unigol ac archifau.</strong> Mae llyfrgelloedd ac archifau nad ydynt wedi'u mynegeio ac wedi'u cyfuno gan unrhyw un o'r rhai uchod, yn aml oherwydd eu bod yn tanariannu, neu am resymau eraill nad ydynt am rannu eu data gyda Open Library, OCLC, Google, ac ati. Mae llawer o'r rhain yn cael cofnodion digidol hygyrch trwy'r rhyngrwyd, ac maent yn aml ddim yn cael eu diogelu'n dda iawn, felly os ydych am helpu allan a chael hwyl yn dysgu am systemau llyfrgell rhyfedd, mae'r rhain yn bwyntiau cychwyn gwych."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Yn y post hwn, rydym yn falch o gyhoeddi rhyddhad bach (o'i gymharu â'n rhyddhadau Z-Library blaenorol). Rydym wedi sgrapio'r rhan fwyaf o ISBNdb, ac wedi gwneud y data ar gael ar gyfer torri ar wefan y Pirate Library Mirror (GOLYGU: symudwyd i <a %(wikipedia_annas_archive)s>Archif Anna</a>; ni fyddwn yn cysylltu â hi yma'n uniongyrchol, dim ond chwilio amdani). Mae'r rhain yn tua 30.9 miliwn o gofnodion (20GB fel <a %(jsonlines)s>Llinellau JSON</a>; 4.4GB wedi'i gywasgu). Ar eu gwefan maent yn honni bod ganddynt mewn gwirionedd 32.6 miliwn o gofnodion, felly efallai ein bod wedi colli rhai, neu <em>maent</em> yn gwneud rhywbeth o'i le. Beth bynnag, am nawr ni fyddwn yn rhannu'n union sut wnaethom hynny — byddwn yn gadael hynny fel ymarfer i'r darllenydd. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Yr hyn a rannwn yw rhywfaint o ddadansoddiad cychwynnol, i geisio dod yn agosach at amcangyfrif nifer y llyfrau yn y byd. Edrychon ni ar dair set ddata: y set ddata ISBNdb newydd hon, ein rhyddhad gwreiddiol o metadata a sgrapiwyd o'r llyfrgell cysgodol Z-Library (sy'n cynnwys Library Genesis), a'r dump data Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Gadewch i ni ddechrau gyda rhai rhifau bras:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Yn y ddau Z-Library/Libgen ac Open Library mae llawer mwy o lyfrau na ISBNs unigryw. A yw hynny'n golygu nad oes gan lawer o'r llyfrau hynny ISBNs, neu a yw'r metadata ISBN yn syml ar goll? Gallwn ateb y cwestiwn hwn yn ôl pob tebyg gyda chyfuniad o baru awtomataidd yn seiliedig ar briodoleddau eraill (teitl, awdur, cyhoeddwr, ac ati), tynnu mwy o ffynonellau data, a thynnu ISBNs o'r sganiau llyfrau gwirioneddol eu hunain (yn achos Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Faint o'r ISBNs hynny sy'n unigryw? Mae hyn yn cael ei ddangos orau gyda diagram Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "I fod yn fwy manwl:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Roedden ni'n synnu gan ba mor ychydig o orgyffwrdd sydd! Mae gan ISBNdb swm enfawr o ISBNs nad ydynt yn ymddangos yn naill ai Z-Library neu Open Library, ac mae'r un peth yn wir (i raddau llai ond dal yn sylweddol) ar gyfer y ddau arall. Mae hyn yn codi llawer o gwestiynau newydd. Faint fyddai paru awtomatig yn helpu i dagio'r llyfrau nad oeddent wedi'u tagio gyda ISBNs? A fyddai llawer o barau ac felly mwy o orgyffwrdd? Hefyd, beth fyddai'n digwydd pe baem yn dod â set ddata 4ydd neu 5ed i mewn? Faint o orgyffwrdd fyddem yn ei weld wedyn?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Mae hyn yn rhoi man cychwyn i ni. Gallwn nawr edrych ar yr holl ISBNs nad oeddent yn y set ddata Z-Library, ac nad ydynt yn cyfateb i feysydd teitl/awdur chwaith. Gall hynny roi gafael i ni ar gadw'r holl lyfrau yn y byd: yn gyntaf trwy sgrapio'r rhyngrwyd am sganiau, yna trwy fynd allan mewn bywyd go iawn i sganio llyfrau. Gallai'r olaf hyd yn oed gael ei ariannu gan y dorf, neu ei yrru gan \"bounty\" gan bobl a fyddai'n hoffi gweld llyfrau penodol yn cael eu digideiddio. Mae hynny i gyd yn stori ar gyfer amser gwahanol."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Os ydych chi eisiau helpu gyda unrhyw un o hyn — dadansoddiad pellach; sgrapio mwy o metadata; dod o hyd i fwy o lyfrau; OCR o lyfrau; gwneud hyn ar gyfer meysydd eraill (e.e. papurau, llyfrau sain, ffilmiau, sioeau teledu, cylchgronau) neu hyd yn oed gwneud rhywfaint o'r data hwn ar gael ar gyfer pethau fel hyfforddi modelau iaith mawr / ML — cysylltwch â mi (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Os oes gennych ddiddordeb penodol yn y dadansoddi data, rydym yn gweithio ar wneud ein setiau data a sgriptiau ar gael mewn fformat haws i'w ddefnyddio. Byddai'n wych pe gallech chi jyst fforcio llyfr nodiadau a dechrau chwarae gyda hyn."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Yn olaf, os ydych chi eisiau cefnogi'r gwaith hwn, ystyriwch wneud rhodd. Mae hwn yn weithrediad sy'n cael ei redeg yn gyfan gwbl gan wirfoddolwyr, ac mae eich cyfraniad yn gwneud gwahaniaeth enfawr. Mae pob tamaid yn helpu. Ar hyn o bryd rydym yn derbyn rhoddion mewn crypto; gweler y dudalen Rhoddion ar Archif Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Am ryw ddiffiniad rhesymol o \"am byth\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Wrth gwrs, mae treftadaeth ysgrifenedig dynoliaeth yn llawer mwy na llyfrau, yn enwedig y dyddiau hyn. Er mwyn y post hwn a'n rhyddhadau diweddar rydym yn canolbwyntio ar lyfrau, ond mae ein diddordebau yn ymestyn ymhellach."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Mae llawer mwy y gellir ei ddweud am Aaron Swartz, ond roeddem am ei grybwyll yn fyr, gan ei fod yn chwarae rhan ganolog yn y stori hon. Wrth i amser fynd heibio, efallai y bydd mwy o bobl yn dod ar draws ei enw am y tro cyntaf, ac yn gallu plymio i'r twll cwningen eu hunain wedyn."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Y ffenestr feirniadol o lyfrgelloedd cysgodol"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Sut allwn ni honni ein bod yn cadw ein casgliadau am byth, pan maent eisoes yn agosáu at 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Fersiwn Tsieineaidd 中文版</a>, trafodwch ar <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Yn Archif Anna, rydym yn aml yn cael ein gofyn sut allwn ni honni ein bod yn cadw ein casgliadau am byth, pan mae'r maint cyfan eisoes yn agosáu at 1 Petabyte (1000 TB), ac yn dal i dyfu. Yn yr erthygl hon byddwn yn edrych ar ein ffilosofi, ac yn gweld pam mae'r degawd nesaf yn hanfodol i'n cenhadaeth o gadw gwybodaeth a diwylliant dynoliaeth."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Mae'r <a %(annas_archive_stats)s>maint cyfan</a> o'n casgliadau, dros y misoedd diwethaf, wedi'i dorri i lawr yn ôl nifer y hadau torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Blaenoriaethau"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Pam rydym ni'n poeni cymaint am bapurau a llyfrau? Gadewch i ni roi ein cred sylfaenol mewn cadwraeth yn gyffredinol o'r neilltu — efallai y byddwn yn ysgrifennu post arall am hynny. Felly pam papurau a llyfrau yn benodol? Mae'r ateb yn syml: <strong>dwysedd gwybodaeth</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Am bob megabeit o storfa, mae testun ysgrifenedig yn storio'r mwyaf o wybodaeth allan o'r holl gyfryngau. Er ein bod yn poeni am wybodaeth a diwylliant, rydym yn poeni mwy am y cyntaf. Yn gyffredinol, rydym yn dod o hyd i hierarchaeth o ddwysedd gwybodaeth a phwysigrwydd cadwraeth sy'n edrych yn fras fel hyn:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Papurau academaidd, cyfnodolion, adroddiadau"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Data organig fel dilyniannau DNA, hadau planhigion, neu samplau microbaidd"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Llyfrau ffeithiol"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Cod meddalwedd gwyddoniaeth a pheirianneg"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Data mesur fel mesuriadau gwyddonol, data economaidd, adroddiadau corfforaethol"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Gwefannau gwyddoniaeth a pheirianneg, trafodaethau ar-lein"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Cylchgronau ffeithiol, papurau newydd, llawlyfrau"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Trawsgrifiadau ffeithiol o sgyrsiau, dogfennau, podlediadau"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Data mewnol o gorfforaethau neu lywodraethau (gollwng)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Cofnodion metadata yn gyffredinol (o ffeithiol a ffuglen; o gyfryngau eraill, celf, pobl, ac ati; gan gynnwys adolygiadau)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Data daearyddol (e.e. mapiau, arolygon daearegol)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Trawsgrifiadau o weithdrefnau cyfreithiol neu lys"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fersiynau ffuglennol neu adloniant o'r uchod i gyd"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Mae'r safle yn y rhestr hon yn eithaf mympwyol — mae sawl eitem yn gyfartal neu mae anghytundebau o fewn ein tîm — ac mae'n debyg ein bod yn anghofio rhai categorïau pwysig. Ond dyma'n fras sut rydym yn blaenoriaethu."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Mae rhai o'r eitemau hyn yn rhy wahanol i'r lleill i ni boeni amdanynt (neu maent eisoes wedi'u gofalu amdanynt gan sefydliadau eraill), megis data organig neu data daearyddol. Ond mae'r rhan fwyaf o'r eitemau yn y rhestr hon yn bwysig i ni mewn gwirionedd."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Ffactor mawr arall yn ein blaenoriaethu yw pa mor fawr yw'r risg i waith penodol. Rydym yn well gennym ganolbwyntio ar weithiau sydd:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Prin"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Yn unigryw o dan ffocws"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Yn unigryw mewn perygl o ddinistr (e.e. gan ryfel, toriadau cyllid, achosion cyfreithiol, neu erledigaeth wleidyddol)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Yn olaf, rydym yn poeni am raddfa. Mae gennym amser ac arian cyfyngedig, felly byddem yn well gennym dreulio mis yn achub 10,000 o lyfrau na 1,000 o lyfrau — os ydynt yn gyfartal o werth ac mewn perygl."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Llyfrgelloedd cysgodol"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Mae llawer o sefydliadau sydd â chenhadaeth debyg, a blaenoriaethau tebyg. Yn wir, mae llyfrgelloedd, archifau, labordai, amgueddfeydd, a sefydliadau eraill wedi'u tasgu gyda chadwraeth o'r fath. Mae llawer o'r rhain wedi'u hariannu'n dda, gan lywodraethau, unigolion, neu gorfforaethau. Ond mae ganddynt un diffyg enfawr: y system gyfreithiol."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Dyma lle mae rôl unigryw llyfrgelloedd cysgodol, a'r rheswm bod Archif Anna yn bodoli. Gallwn wneud pethau nad yw sefydliadau eraill yn cael eu gwneud. Nawr, nid yw'n (aml) ein bod yn gallu archifo deunyddiau sy'n anghyfreithlon i'w cadw mewn mannau eraill. Na, mae'n gyfreithlon mewn llawer o leoedd i adeiladu archif gydag unrhyw lyfrau, papurau, cylchgronau, ac ati."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ond yr hyn sydd yn aml yn brin mewn archifau cyfreithiol yw <strong>gorddibyniaeth a hirhoedledd</strong>. Mae llyfrau yn bodoli lle mae dim ond un copi yn bodoli mewn llyfrgell gorfforol rywle. Mae cofnodion metadata yn bodoli sy'n cael eu gwarchod gan un corfforaeth. Mae papurau newydd yn bodoli sydd ond wedi'u cadw ar ficroffilm mewn un archif. Gall llyfrgelloedd gael toriadau cyllid, gall corfforaethau fynd yn fethdalwr, gall archifau gael eu bomio a'u llosgi i'r llawr. Nid yw hyn yn ddamcaniaethol — mae hyn yn digwydd drwy'r amser."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Yr hyn y gallwn ei wneud yn unigryw yn Archif Anna yw storio llawer o gopïau o weithiau, ar raddfa fawr. Gallwn gasglu papurau, llyfrau, cylchgronau, a mwy, a'u dosbarthu mewn swmp. Ar hyn o bryd rydym yn gwneud hyn drwy dorrenti, ond nid yw'r technolegau union yn bwysig a byddant yn newid dros amser. Y rhan bwysig yw cael llawer o gopïau wedi'u dosbarthu ar draws y byd. Mae'r dyfyniad hwn o dros 200 mlynedd yn ôl yn dal i fod yn wir:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ni ellir adfer yr hyn sydd wedi'i golli; ond gadewch i ni achub yr hyn sy'n weddill: nid trwy fynwentydd a chloi sy'n eu gwahardd o'r llygad cyhoeddus a'r defnydd, wrth eu consignio i wastraff amser, ond trwy luosi copïau, a fydd yn eu gosod y tu hwnt i gyrraedd damwain.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Nodyn cyflym am barth cyhoeddus. Gan fod Archif Anna yn canolbwyntio'n unigryw ar weithgareddau sy'n anghyfreithlon mewn llawer o leoedd ledled y byd, nid ydym yn trafferthu gyda chasgliadau sydd ar gael yn eang, megis llyfrau parth cyhoeddus. Yn aml mae endidau cyfreithiol eisoes yn gofalu'n dda am hynny. Fodd bynnag, mae ystyriaethau sy'n gwneud i ni weithiau weithio ar gasgliadau sydd ar gael yn gyhoeddus:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Gellir gweld cofnodion metadata yn rhydd ar wefan Worldcat, ond ni ellir eu lawrlwytho mewn swmp (hyd nes i ni <a %(worldcat_scrape)s>sgrafio</a> nhw)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Gall cod fod yn ffynhonnell agored ar Github, ond ni ellir adlewyrchu Github yn ei gyfanrwydd yn hawdd ac felly ei gadw (er bod copïau wedi'u dosbarthu'n ddigonol o'r rhan fwyaf o gasgliadau cod yn yr achos penodol hwn)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Mae Reddit yn rhad ac am ddim i'w ddefnyddio, ond mae wedi cyflwyno mesurau gwrth-sgrafio llym yn ddiweddar, yn sgil hyfforddiant LLM sy'n llwglyd am ddata (mwy am hynny yn nes ymlaen)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Lluosi copïau"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Yn ôl at ein cwestiwn gwreiddiol: sut y gallwn honni ein bod yn cadw ein casgliadau am byth? Y prif broblem yma yw bod ein casgliad wedi bod yn <a %(torrents_stats)s>tyfu</a> ar gyflymder cyflym, trwy sgrafio a rhyddhau rhai casgliadau enfawr (ar ben y gwaith anhygoel sydd eisoes wedi'i wneud gan lyfrgelloedd cysgodol data agored eraill fel Sci-Hub a Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Mae'r twf hwn mewn data yn ei gwneud yn anoddach i'r casgliadau gael eu hadlewyrchu ledled y byd. Mae storio data yn ddrud! Ond rydym yn optimistaidd, yn enwedig wrth arsylwi ar y tri thueddiad canlynol."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Rydym wedi codi'r ffrwythau isel"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Mae hyn yn dilyn yn uniongyrchol o'n blaenoriaethau a drafodwyd uchod. Rydym yn well gennym weithio ar ryddhau casgliadau mawr yn gyntaf. Nawr ein bod wedi sicrhau rhai o'r casgliadau mwyaf yn y byd, rydym yn disgwyl i'n twf fod yn llawer arafach."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Mae dal i fod cynffon hir o gasgliadau llai, ac mae llyfrau newydd yn cael eu sganio neu eu cyhoeddi bob dydd, ond mae'r gyfradd yn debygol o fod yn llawer arafach. Efallai y byddwn yn dal i ddyblu neu hyd yn oed dreblu mewn maint, ond dros gyfnod hirach o amser."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Mae costau storio yn parhau i ostwng yn esbonyddol"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Ar adeg ysgrifennu, mae <a %(diskprices)s>prisiau disgiau</a> fesul TB tua $12 ar gyfer disgiau newydd, $8 ar gyfer disgiau a ddefnyddiwyd, a $4 ar gyfer tâp. Os ydym yn geidwadol ac yn edrych yn unig ar ddisgiau newydd, mae hynny'n golygu bod storio petabeit yn costio tua $12,000. Os ydym yn tybio y bydd ein llyfrgell yn treblu o 900TB i 2.7PB, byddai hynny'n golygu $32,400 i adlewyrchu ein llyfrgell gyfan. Gan ychwanegu trydan, cost caledwedd arall, ac ati, gadewch i ni ei grynhoi i $40,000. Neu gyda thâp mwy fel $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Ar un llaw <strong>$15,000–$40,000 am swm holl wybodaeth ddynol yw bargen</strong>. Ar y llaw arall, mae'n dipyn serth i ddisgwyl tunnell o gopïau llawn, yn enwedig os hoffem hefyd i'r bobl hynny gadw eu toriadau ar gyfer budd eraill."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dyna heddiw. Ond mae cynnydd yn symud ymlaen:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Mae costau gyriannau caled fesul TB wedi'u torri'n fras yn drydydd dros y 10 mlynedd diwethaf, ac mae'n debygol y byddant yn parhau i ostwng ar gyflymder tebyg. Mae'n ymddangos bod tâp ar drywydd tebyg. Mae prisiau SSD yn gostwng hyd yn oed yn gyflymach, ac efallai y byddant yn cymryd drosodd prisiau HDD erbyn diwedd y degawd."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tueddiadau prisiau HDD o wahanol ffynonellau (cliciwch i weld yr astudiaeth)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Os yw hyn yn dal, yna mewn 10 mlynedd efallai y byddwn yn edrych ar ddim ond $5,000–$13,000 i adlewyrchu ein casgliad cyfan (1/3), neu hyd yn oed yn llai os ydym yn tyfu llai o ran maint. Er ei fod yn dal i fod yn llawer o arian, bydd hyn yn gyflawnadwy i lawer o bobl. A gallai fod hyd yn oed yn well oherwydd y pwynt nesaf…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Gwelliannau mewn dwysedd gwybodaeth"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Ar hyn o bryd, rydym yn storio llyfrau yn y fformatau crai y maent yn cael eu rhoi i ni. Wrth gwrs, maent wedi'u cywasgu, ond yn aml maent yn dal i fod yn sganiau neu'n ffotograffau mawr o dudalennau."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Hyd yma, yr unig opsiynau i leihau maint cyfan ein casgliad yw trwy gywasgu mwy ymosodol, neu ddileu dyblygiadau. Fodd bynnag, i gael arbedion digon sylweddol, mae'r ddau yn rhy golledig i'n blas. Gall cywasgu trwm o ffotograffau wneud testun yn anodd ei ddarllen. Ac mae dileu dyblygiadau yn gofyn am hyder uchel bod llyfrau yn union yr un fath, sy'n aml yn rhy anghywir, yn enwedig os yw'r cynnwys yr un fath ond bod y sganiau wedi'u gwneud ar achlysuron gwahanol."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Mae bob amser wedi bod yn drydydd opsiwn, ond mae ei ansawdd wedi bod mor wael na wnaethom ei ystyried: <strong>OCR, neu Adnabod Cymeriadau Optegol</strong>. Dyma'r broses o drosi ffotograffau yn destun plaen, trwy ddefnyddio AI i ganfod y cymeriadau yn y ffotograffau. Mae offer ar gyfer hyn wedi bodoli ers amser maith, ac wedi bod yn eithaf da, ond nid yw “eithaf da” yn ddigon ar gyfer dibenion cadwraeth."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Fodd bynnag, mae modelau dysgu dwfn aml-fodd diweddar wedi gwneud cynnydd hynod gyflym, er bod costau uchel o hyd. Rydym yn disgwyl i gywirdeb a chostau wella'n ddramatig yn y blynyddoedd i ddod, i'r pwynt lle bydd yn realistig ei gymhwyso i'n llyfrgell gyfan."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Gwelliannau OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Pan fydd hynny'n digwydd, mae'n debyg y byddwn yn dal i gadw'r ffeiliau gwreiddiol, ond yn ogystal gallwn gael fersiwn llawer llai o'n llyfrgell y bydd y rhan fwyaf o bobl eisiau ei hadlewyrchu. Y pwynt pwysig yw bod testun crai ei hun yn cywasgu hyd yn oed yn well, ac yn llawer haws i ddileu dyblygiadau, gan roi mwy o arbedion i ni."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Yn gyffredinol, nid yw'n afrealistig disgwyl gostyngiad o leiaf 5-10x mewn maint ffeil cyfan, efallai hyd yn oed yn fwy. Hyd yn oed gyda gostyngiad ceidwadol o 5x, byddem yn edrych ar <strong>$1,000–$3,000 mewn 10 mlynedd hyd yn oed os bydd ein llyfrgell yn treblu o ran maint</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Ffenestr hanfodol"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Os yw'r rhagolygon hyn yn gywir, mae angen i ni <strong>ddisgwyl ychydig flynyddoedd</strong> cyn y bydd ein casgliad cyfan yn cael ei adlewyrchu'n eang. Felly, yn ôl geiriau Thomas Jefferson, “wedi'i osod y tu hwnt i gyrraedd damwain.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Yn anffodus, mae dyfodiad LLMs, a'u hyfforddiant sy'n llwglyd am ddata, wedi rhoi llawer o ddeiliaid hawlfraint ar y diffensif. Hyd yn oed yn fwy nag yr oeddent eisoes. Mae llawer o wefannau yn ei gwneud yn anoddach i grafu a chofnodi, mae achosion cyfreithiol yn hedfan o gwmpas, ac ar yr un pryd mae llyfrgelloedd a chofnodion corfforol yn parhau i gael eu hesgeuluso."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Dim ond disgwyl y gallwn i'r tueddiadau hyn barhau i waethygu, a llawer o weithiau i gael eu colli cyn iddynt fynd i'r parth cyhoeddus."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Rydym ar drothwy chwyldro mewn cadwraeth, ond <q>ni ellir adfer y colledig.</q></strong> Mae gennym ffenestr hanfodol o tua 5-10 mlynedd lle mae'n dal yn eithaf drud i weithredu llyfrgell gysgodol a chreu llawer o adlewyrchiadau o amgylch y byd, ac yn ystod y cyfnod hwn nid yw mynediad wedi'i gau'n llwyr eto."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Os gallwn bontio'r ffenestr hon, yna byddwn wir wedi cadw gwybodaeth a diwylliant dynoliaeth am byth. Ni ddylem adael i'r amser hwn fynd yn wastraff. Ni ddylem adael i'r ffenestr hanfodol hon gau arnom."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Gadewch i ni fynd."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Mynediad unigryw i gwmnïau LLM i'r casgliad llyfrau ffeithiol Tsieineaidd mwyaf yn y byd"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Fersiwn Tsieineaidd 中文版</a>, <a %(news_ycombinator)s>Trafodwch ar Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Mae Archif Anna wedi caffael casgliad unigryw o 7.5 miliwn / 350TB o lyfrau ffeithiol Tsieineaidd — yn fwy na Library Genesis. Rydym yn barod i roi mynediad unigryw i gwmni LLM, yn gyfnewid am OCR o ansawdd uchel a thynnu testun.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Mae hwn yn gofnod blog byr. Rydym yn chwilio am ryw gwmni neu sefydliad i'n helpu gyda OCR a thynnu testun ar gyfer casgliad enfawr a gawsom, yn gyfnewid am fynediad cynnar unigryw. Ar ôl y cyfnod embargo, byddwn wrth gwrs yn rhyddhau'r casgliad cyfan."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Mae testun academaidd o ansawdd uchel yn hynod ddefnyddiol ar gyfer hyfforddi LLMs. Er bod ein casgliad yn Tsieinëeg, dylai hyn fod yn ddefnyddiol hyd yn oed ar gyfer hyfforddi LLMs Saesneg: mae modelau'n ymddangos i amgodio cysyniadau a gwybodaeth waeth beth yw iaith y ffynhonnell."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Ar gyfer hyn, mae angen echdynnu testun o'r sganiau. Beth mae Archif Anna yn ei gael allan ohono? Chwilio testun llawn o'r llyfrau ar gyfer ei ddefnyddwyr."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Oherwydd bod ein nodau yn cyd-fynd â nodau datblygwyr LLM, rydym yn chwilio am gydweithredwr. Rydym yn barod i roi <strong>mynediad cynnar unigryw i'r casgliad hwn yn swmp am 1 flwyddyn</strong>, os gallwch wneud OCR a thynnu testun yn iawn. Os ydych yn barod i rannu cod cyfan eich piblinell gyda ni, byddem yn barod i embargo'r casgliad am gyfnod hirach."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Tudalennau enghreifftiol"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "I brofi i ni fod gennych biblinell dda, dyma rai tudalennau enghreifftiol i ddechrau arnynt, o lyfr ar uwch-ddargludyddion. Dylai eich piblinell drin mathemateg, tablau, siartiau, nodiadau troed, ac yn y blaen yn iawn."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Anfonwch eich tudalennau wedi'u prosesu i'n e-bost. Os ydynt yn edrych yn dda, byddwn yn anfon mwy atoch yn breifat, ac rydym yn disgwyl i chi allu rhedeg eich piblinell yn gyflym ar y rheini hefyd. Unwaith y byddwn yn fodlon, gallwn wneud bargen."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Casgliad"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Rhagor o wybodaeth am y casgliad. Mae <a %(duxiu)s>Duxiu</a> yn gronfa ddata enfawr o lyfrau wedi'u sganio, a grëwyd gan y <a %(chaoxing)s>SuperStar Digital Library Group</a>. Mae'r rhan fwyaf yn llyfrau academaidd, wedi'u sganio er mwyn eu gwneud ar gael yn ddigidol i brifysgolion a llyfrgelloedd. Ar gyfer ein cynulleidfa Saesneg, mae <a %(library_princeton)s>Princeton</a> a'r <a %(guides_lib_uw)s>University of Washington</a> yn cynnig trosolwg da. Mae hefyd erthygl ragorol sy'n rhoi mwy o gefndir: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (chwiliwch amdano yn Archif Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Mae llyfrau o Duxiu wedi cael eu hacio ers amser maith ar y rhyngrwyd Tsieineaidd. Fel arfer, maent yn cael eu gwerthu am lai na doler gan ailwerthwyr. Maent yn cael eu dosbarthu fel arfer gan ddefnyddio'r cyfwerth Tsieineaidd o Google Drive, sydd wedi cael ei hacio'n aml i ganiatáu mwy o le storio. Gellir dod o hyd i rai manylion technegol <a %(github_duty_machine)s>yma</a> ac <a %(github_821_github_io)s>yma</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Er bod y llyfrau wedi cael eu dosbarthu'n lled-gyhoeddus, mae'n eithaf anodd eu cael yn swmp. Roeddem wedi gosod hyn yn uchel ar ein rhestr TODO, ac wedi neilltuo sawl mis o waith llawn amser ar ei gyfer. Fodd bynnag, yn ddiweddar, cysylltodd gwirfoddolwr anhygoel, anhygoel, a thalentog â ni, gan ddweud wrthym eu bod wedi gwneud yr holl waith hwn eisoes — ar gost fawr. Rhannodd y casgliad llawn gyda ni, heb ddisgwyl unrhyw beth yn ôl, ac eithrio'r warant o gadwraeth hirdymor. Gwirioneddol anhygoel. Cytunasant i ofyn am gymorth yn y ffordd hon i gael y casgliad OCR'ed."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Mae'r casgliad yn cynnwys 7,543,702 ffeil. Mae hyn yn fwy na Library Genesis ffeithiol (tua 5.3 miliwn). Mae cyfanswm maint y ffeil tua 359TB (326TiB) yn ei ffurf bresennol."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Rydym yn agored i gynigion a syniadau eraill. Cysylltwch â ni. Edrychwch ar Archif Anna am ragor o wybodaeth am ein casgliadau, ymdrechion cadwraeth, a sut y gallwch helpu. Diolch!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Rhybudd: mae'r cofnod blog hwn wedi'i ddad-ddatgan. Rydym wedi penderfynu nad yw IPFS yn barod eto ar gyfer amser brig. Byddwn yn dal i gysylltu â ffeiliau ar IPFS o Archif Anna pan fo'n bosibl, ond ni fyddwn yn ei gynnal ein hunain mwyach, nac yn argymell eraill i adlewyrchu gan ddefnyddio IPFS. Gweler ein tudalen Torrents os ydych am helpu i gadw ein casgliad."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Helpwch i hadu Z-Library ar IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Sut i redeg llyfrgell gysgodol: gweithrediadau yn Archif Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Nid oes <q>AWS ar gyfer elusennau cysgodol,</q> felly sut rydym yn rhedeg Archif Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Rwy'n rhedeg <a %(wikipedia_annas_archive)s>Archif Anna</a>, y peiriant chwilio di-elw ffynhonnell agored mwyaf yn y byd ar gyfer <a %(wikipedia_shadow_library)s>llyfrgelloedd cysgodol</a>, fel Sci-Hub, Library Genesis, a Z-Library. Ein nod yw gwneud gwybodaeth a diwylliant yn hawdd i'w cyrraedd, ac yn y pen draw i adeiladu cymuned o bobl sy'n archifo ac yn cadw <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>pob llyfr yn y byd</a> gyda'i gilydd."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Yn yr erthygl hon byddaf yn dangos sut rydym yn rhedeg y wefan hon, a'r heriau unigryw sy'n dod gyda gweithredu gwefan gyda statws cyfreithiol amheus, gan nad oes “AWS ar gyfer elusennau cysgodol”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Edrychwch hefyd ar yr erthygl chwaer <a %(blog_how_to_become_a_pirate_archivist)s>Sut i ddod yn archifydd môr-ladron</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tocynnau arloesi"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Gadewch i ni ddechrau gyda'n stac technoleg. Mae'n fwriadol o ddiflas. Rydym yn defnyddio Flask, MariaDB, ac ElasticSearch. Dyna ydyw yn llythrennol. Mae chwilio yn broblem wedi'i datrys i raddau helaeth, ac nid ydym yn bwriadu ei hail-ddyfeisio. Ar ben hynny, mae'n rhaid i ni wario ein <a %(mcfunley)s>tocynnau arloesi</a> ar rywbeth arall: peidio â chael ein cau i lawr gan yr awdurdodau."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Felly pa mor gyfreithlon neu anghyfreithlon yw Archif Anna yn union? Mae hyn yn dibynnu'n bennaf ar yr awdurdodaeth gyfreithiol. Mae'r rhan fwyaf o wledydd yn credu mewn rhyw fath o hawlfraint, sy'n golygu bod pobl neu gwmnïau yn cael monopoli unigryw ar rai mathau o weithiau am gyfnod penodol o amser. Fel nodyn ochr, yn Archif Anna rydym yn credu er bod rhai manteision, mae hawlfraint yn gyffredinol yn niweidiol i gymdeithas — ond mae hynny'n stori ar gyfer amser arall."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Mae'r monopoli unigryw hwn ar rai gweithiau yn golygu ei bod yn anghyfreithlon i unrhyw un y tu allan i'r monopoli hwn ddosbarthu'r gweithiau hynny'n uniongyrchol — gan gynnwys ni. Ond mae Archif Anna yn beiriant chwilio nad yw'n dosbarthu'r gweithiau hynny'n uniongyrchol (o leiaf nid ar ein gwefan clearnet), felly dylem fod yn iawn, iawn? Nid yn union. Mewn llawer o awdurdodaethau mae'n anghyfreithlon nid yn unig i ddosbarthu gweithiau hawlfraint, ond hefyd i gysylltu â lleoedd sy'n gwneud hynny. Enghraifft glasurol o hyn yw cyfraith DMCA yr Unol Daleithiau."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dyna'r pen mwyaf llym o'r sbectrwm. Ar ben arall y sbectrwm gallai fod gwledydd heb unrhyw gyfreithiau hawlfraint o gwbl, ond nid ydynt yn bodoli mewn gwirionedd. Mae gan bron pob gwlad ryw fath o gyfraith hawlfraint ar y llyfrau. Mae gorfodi yn stori wahanol. Mae digon o wledydd â llywodraethau nad ydynt yn poeni am orfodi cyfraith hawlfraint. Mae hefyd wledydd rhwng y ddau eithaf, sy'n gwahardd dosbarthu gweithiau hawlfraint, ond nad ydynt yn gwahardd cysylltu â gweithiau o'r fath."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Ystyriaeth arall yw ar lefel y cwmni. Os yw cwmni'n gweithredu mewn awdurdodaeth nad yw'n poeni am hawlfraint, ond nad yw'r cwmni ei hun yn barod i gymryd unrhyw risg, yna efallai y byddant yn cau eich gwefan i lawr cyn gynted ag y bydd unrhyw un yn cwyno amdani."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Yn olaf, ystyriaeth fawr yw taliadau. Gan fod angen i ni aros yn ddienw, ni allwn ddefnyddio dulliau talu traddodiadol. Mae hyn yn ein gadael gyda cryptocurrencies, ac mae dim ond is-set fach o gwmnïau yn cefnogi'r rheini (mae cardiau debyd rhithwir a delir gan crypto, ond nid ydynt yn cael eu derbyn yn aml)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Pensaernïaeth system"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Felly gadewch i ni ddweud eich bod wedi dod o hyd i rai cwmnïau sy'n barod i gynnal eich gwefan heb eich cau i lawr — gadewch i ni alw'r rhain yn “darparwyr sy'n caru rhyddid” 😄. Byddwch yn darganfod yn gyflym bod cynnal popeth gyda nhw yn eithaf drud, felly efallai y byddwch am ddod o hyd i rai “darparwyr rhad” a gwneud y cynnal gwirioneddol yno, gan brocsio trwy'r darparwyr sy'n caru rhyddid. Os gwnewch hynny'n iawn, ni fydd y darparwyr rhad byth yn gwybod beth rydych chi'n ei gynnal, ac ni fyddant byth yn derbyn unrhyw gwynion."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Gyda'r holl ddarparwyr hyn mae risg iddynt eich cau i lawr beth bynnag, felly mae angen gormodedd arnoch hefyd. Mae angen hyn ar bob lefel o'n stac."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Un cwmni sy'n caru rhyddid i ryw raddau sydd wedi rhoi ei hun mewn sefyllfa ddiddorol yw Cloudflare. Maent wedi <a %(blog_cloudflare)s>dadlau</a> nad ydynt yn ddarparwr cynnal, ond yn gyfleustod, fel ISP. Felly nid ydynt yn ddarostyngedig i geisiadau DMCA neu geisiadau tynnu i lawr eraill, ac maent yn anfon unrhyw geisiadau ymlaen at eich darparwr cynnal gwirioneddol. Maent wedi mynd cyn belled â mynd i'r llys i amddiffyn y strwythur hwn. Gallwn felly eu defnyddio fel haen arall o storfa a diogelwch."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Nid yw Cloudflare yn derbyn taliadau dienw, felly dim ond eu cynllun am ddim y gallwn ei ddefnyddio. Mae hyn yn golygu na allwn ddefnyddio eu nodweddion cydbwyso llwyth neu fethiant. Felly rydym wedi <a %(annas_archive_l255)s>gweithredu hyn ein hunain</a> ar lefel y parth. Ar lwytho tudalen, bydd y porwr yn gwirio a yw'r parth cyfredol ar gael o hyd, ac os nad yw, mae'n ailysgrifennu pob URL i barth gwahanol. Gan fod Cloudflare yn storio llawer o dudalennau, mae hyn yn golygu y gall defnyddiwr lanio ar ein prif barth, hyd yn oed os yw'r gweinydd procsïo i lawr, ac yna ar y clic nesaf gael ei symud i barth arall."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Mae gennym hefyd bryderon gweithredol arferol i ddelio â nhw, megis monitro iechyd gweinydd, logio gwallau cefn a blaen, ac yn y blaen. Mae ein pensaernïaeth methiant yn caniatáu mwy o gadarnwch ar y blaen hwn hefyd, er enghraifft trwy redeg set hollol wahanol o weinyddion ar un o'r parthau. Gallwn hyd yn oed redeg hen fersiynau o'r cod a'r datasets ar y parth ar wahân hwn, rhag ofn y bydd byg critigol yn y prif fersiwn yn mynd heb ei sylwi."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Gallwn hefyd warchod yn erbyn Cloudflare yn troi yn ein herbyn, trwy ei dynnu o un o'r parthau, megis y parth ar wahân hwn. Mae gwahanol gyfuniadau o'r syniadau hyn yn bosibl."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Offer"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Gadewch i ni edrych ar ba offer rydym yn eu defnyddio i gyflawni hyn i gyd. Mae hyn yn esblygu'n fawr wrth i ni ddod ar draws problemau newydd a dod o hyd i atebion newydd."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Gweinydd cais: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Gweinydd procsïo: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Rheoli gweinydd: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Datblygiad: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Gweinydd statig Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Mae rhai penderfyniadau yr ydym wedi mynd yn ôl ac ymlaen arnynt. Un ohonynt yw'r cyfathrebu rhwng gweinyddion: roeddem yn arfer defnyddio Wireguard ar gyfer hyn, ond canfuom ei fod weithiau'n stopio trosglwyddo unrhyw ddata, neu'n trosglwyddo data mewn un cyfeiriad yn unig. Digwyddodd hyn gyda sawl gwahanol osodiad Wireguard a geisiasom, megis <a %(github_costela_wesher)s>wesher</a> a <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ceisiasom hefyd diwnelu porthladdoedd dros SSH, gan ddefnyddio autossh a sshuttle, ond cawsom <a %(github_sshuttle)s>problemau yno</a> (er nad yw'n glir i mi o hyd os yw autossh yn dioddef o faterion TCP-dros-TCP ai peidio — mae'n teimlo fel ateb janky i mi ond efallai ei fod yn iawn mewn gwirionedd?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Yn lle hynny, aethom yn ôl at gysylltiadau uniongyrchol rhwng gweinyddion, gan guddio bod gweinydd yn rhedeg ar y darparwyr rhad gan ddefnyddio hidlo IP gyda UFW. Mae hyn yn cael yr anfantais nad yw Docker yn gweithio'n dda gyda UFW, oni bai eich bod yn defnyddio <code>network_mode: \"host\"</code>. Mae hyn i gyd ychydig yn fwy tueddol o gamgymeriadau, oherwydd byddwch yn agor eich gweinydd i'r rhyngrwyd gyda dim ond camgymeriad bach mewn ffurfweddiad. Efallai y dylem symud yn ôl at autossh — byddai adborth yn cael ei groesawu'n fawr yma."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Rydym hefyd wedi mynd yn ôl ac ymlaen ar Varnish vs. Nginx. Ar hyn o bryd rydym yn hoffi Varnish, ond mae ganddo ei quirks a'i ymylon garw. Mae'r un peth yn berthnasol i Checkmk: nid ydym yn ei garu, ond mae'n gweithio am nawr. Mae Weblate wedi bod yn iawn ond nid yn anhygoel — weithiau rwy'n ofni y bydd yn colli fy nata pryd bynnag y byddaf yn ceisio ei gysoni â'n repo git. Mae Flask wedi bod yn dda yn gyffredinol, ond mae ganddo rai quirks rhyfedd sydd wedi costio llawer o amser i ddadfygio, megis ffurfweddu parthau arfer, neu faterion gyda'i integreiddio SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Hyd yma mae'r offer eraill wedi bod yn wych: nid oes gennym gwynion difrifol am MariaDB, ElasticSearch, Gitlab, Zulip, Docker, a Tor. Mae gan bob un o'r rhain rai materion, ond dim byd difrifol neu sy'n cymryd llawer o amser."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Casgliad"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Mae wedi bod yn brofiad diddorol dysgu sut i sefydlu peiriant chwilio llyfrgell cysgodol cadarn a gwydn. Mae llawer mwy o fanylion i'w rhannu mewn postiadau yn y dyfodol, felly gadewch i mi wybod beth yr hoffech chi ddysgu mwy amdano!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Fel bob amser, rydym yn chwilio am roddion i gefnogi'r gwaith hwn, felly sicrhewch eich bod yn edrych ar y dudalen Rhoi ar Archif Anna. Rydym hefyd yn chwilio am fathau eraill o gefnogaeth, megis grantiau, noddwyr tymor hir, darparwyr taliadau risg uchel, efallai hyd yn oed hysbysebion (blasus!). Ac os ydych chi eisiau cyfrannu eich amser a'ch sgiliau, rydym bob amser yn chwilio am ddatblygwyr, cyfieithwyr, ac ati. Diolch am eich diddordeb a'ch cefnogaeth."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna a'r tîm (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Helo, fi yw Anna. Creais <a %(wikipedia_annas_archive)s>Archif Anna</a>, llyfrgell gysgodol fwyaf y byd. Dyma fy mlog personol, lle rwyf i a fy nghydweithwyr yn ysgrifennu am fôr-ladrad, cadwraeth ddigidol, a mwy."

#, fuzzy
msgid "blog.index.text2"
msgstr "Cysylltwch â mi ar <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Sylwch mai dim ond blog yw'r wefan hon. Dim ond ein geiriau ein hunain yr ydym yn eu cynnal yma. Nid oes unrhyw dorrents neu ffeiliau hawlfraint eraill yn cael eu cynnal neu eu cysylltu yma."

#, fuzzy
msgid "blog.index.heading"
msgstr "Postiadau blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B sgrapio WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Rhoi 5,998,794 o lyfrau ar IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Rhybudd: mae'r cofnod blog hwn wedi'i ddad-ddatgan. Rydym wedi penderfynu nad yw IPFS yn barod eto ar gyfer amser brig. Byddwn yn dal i gysylltu â ffeiliau ar IPFS o Archif Anna pan fo'n bosibl, ond ni fyddwn yn ei gynnal ein hunain mwyach, nac yn argymell eraill i adlewyrchu gan ddefnyddio IPFS. Gweler ein tudalen Torrents os ydych am helpu i gadw ein casgliad."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Mae Archif Anna wedi sgrapio holl WorldCat (y casgliad metadata llyfrgell mwyaf yn y byd) i wneud rhestr TODO o lyfrau sydd angen eu cadw.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Flwyddyn yn ôl, fe wnaethom <a %(blog)s>osod allan</a> i ateb y cwestiwn hwn: <strong>Pa ganran o lyfrau sydd wedi'u cadw'n barhaol gan lyfrgelloedd cysgodol?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Unwaith y bydd llyfr yn cyrraedd llyfrgell cysgodol data agored fel <a %(wikipedia_library_genesis)s>Library Genesis</a>, ac yn awr <a %(wikipedia_annas_archive)s>Archif Anna</a>, mae'n cael ei adlewyrchu ledled y byd (trwy dorrents), gan ei gadw'n ymarferol am byth."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "I ateb y cwestiwn o ba ganran o lyfrau sydd wedi'u cadw, mae angen i ni wybod y rhifydd: faint o lyfrau sy'n bodoli i gyd? Ac yn ddelfrydol nid oes gennym ddim ond rhif, ond metadata gwirioneddol. Yna gallwn nid yn unig eu paru yn erbyn llyfrgelloedd cysgodol, ond hefyd <strong>creu rhestr TODO o lyfrau sy'n weddill i'w cadw!</strong> Gallem hyd yn oed ddechrau breuddwydio am ymdrech torfol i fynd i lawr y rhestr TODO hon."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Rhwygasom <a %(wikipedia_isbndb_com)s>ISBNdb</a>, a lawrlwythwyd y set ddata <a %(openlibrary)s>Open Library</a>, ond roedd y canlyniadau'n anfoddhaol. Y prif broblem oedd nad oedd llawer o orgyffwrdd o ISBNs. Gweler y diagram Venn hwn o <a %(blog)s>ein post blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Roeddem yn synnu'n fawr gan ba mor ychydig o orgyffwrdd oedd rhwng ISBNdb ac Open Library, y ddau ohonynt yn cynnwys data o wahanol ffynonellau yn hael, megis sgrapiau gwe a chofnodion llyfrgell. Os ydynt yn gwneud gwaith da o ddod o hyd i'r rhan fwyaf o ISBNs sydd allan yna, byddai eu cylchoedd yn sicr yn orgyffwrdd yn sylweddol, neu byddai un yn is-set o'r llall. Fe wnaethom feddwl, sawl llyfr sy'n cwympo <em>yn llwyr y tu allan i'r cylchoedd hyn</em>? Mae angen cronfa ddata fwy arnom."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Dyna pryd y gosodon ni ein golygon ar y gronfa ddata llyfrau fwyaf yn y byd: <a %(wikipedia_worldcat)s>WorldCat</a>. Mae hon yn gronfa ddata breiniol gan y sefydliad dielw <a %(wikipedia_oclc)s>OCLC</a>, sy'n casglu cofnodion metadata o lyfrgelloedd ledled y byd, yn gyfnewid am roi mynediad i'r llyfrgelloedd hynny i'r set ddata lawn, a'u cael i ymddangos yn y canlyniadau chwilio i ddefnyddwyr terfynol."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Er bod OCLC yn sefydliad dielw, mae eu model busnes yn gofyn am ddiogelu eu cronfa ddata. Wel, mae'n ddrwg gennym ddweud, ffrindiau yn OCLC, rydym yn rhoi'r cyfan i ffwrdd. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Dros y flwyddyn ddiwethaf, rydym wedi sgrapio pob cofnod WorldCat yn fanwl. Ar y dechrau, cawsom lwc dda. Roedd WorldCat newydd gyflwyno eu hailgynllun gwefan gyflawn (yn Awst 2022). Roedd hyn yn cynnwys ailwampio sylweddol o'u systemau cefn, gan gyflwyno llawer o wendidau diogelwch. Fe wnaethom fanteisio ar y cyfle ar unwaith, a llwyddo i sgrapio cannoedd o filiynau (!) o gofnodion mewn dim ond ychydig ddyddiau."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Ailgynllun WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Ar ôl hynny, cafodd y gwendidau diogelwch eu trwsio'n araf un wrth un, tan i'r olaf a ddarganfyddwyd gennym gael ei drwsio tua mis yn ôl. Erbyn hynny roedd gennym bron pob cofnod, ac roeddem ond yn mynd am gofnodion o ansawdd ychydig yn uwch. Felly teimlwn ei bod yn amser i ryddhau!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Gadewch i ni edrych ar rai gwybodaeth sylfaenol am y data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Fformat?</strong> <a %(blog)s>Cynwysyddion Archif Anna (AAC)</a>, sy'n y bôn yn <a %(jsonlines)s>Llinellau JSON</a> wedi'u cywasgu gyda <a %(zstd)s>Zstandard</a>, ynghyd â rhai semanteg safonol. Mae'r cynwysyddion hyn yn lapio gwahanol fathau o gofnodion, yn seiliedig ar y sgrapiau gwahanol a ddefnyddiwyd gennym."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Digwyddodd gwall anhysbys. Cysylltwch â ni yn %(email)s gyda sgrinlun."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Mae gan yr arian hwn isafswm uwch na'r arfer. Dewiswch hyd gwahanol neu arian gwahanol."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Methu cwblhau'r cais. Rhowch gynnig arall arni mewn ychydig funudau, ac os yw'n parhau i ddigwydd cysylltwch â ni yn %(email)s gyda sgrinlun."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Gwall wrth brosesu taliad. Arhoswch funud a cheisiwch eto. Os bydd y broblem yn parhau am fwy na 24 awr, cysylltwch â ni yn %(email)s gyda sgrinlun."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "sylw cudd"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Mater ffeil: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Fersiwn gwell"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Ydych chi eisiau riportio'r defnyddiwr hwn am ymddygiad sarhaus neu amhriodol?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Riportio cam-drin"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Cam-drin wedi'i riportio:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Rydych wedi riportio'r defnyddiwr hwn am gam-drin."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Ateb"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Os gwelwch yn dda <a %(a_login)s>mewngofnodi</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Rydych wedi gadael sylw. Efallai y bydd yn cymryd munud iddo ymddangos."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s tudalennau yr effeithiwyd arnynt"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Ddim yn weladwy yn Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Ddim yn weladwy yn Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Ddim yn weladwy yn Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Wedi'i farcio'n dorri yn Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Ar goll o Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Wedi'i farcio fel “sbam” yn Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Wedi'i farcio fel “ffeil wael” yn Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Ni ellid trosi pob tudalen i PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Methodd exiftool yn rhedeg ar y ffeil hon"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Llyfr (anhysbys)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Llyfr (anfuglen)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Llyfr (ffuglen)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Erthygl cylchgrawn"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Dogfen safonau"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Cylchgrawn"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Llyfr comig"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Sgôr gerddorol"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Llyfr Sain"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Arall"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Lawrlwytho gweinydd partner"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Llwytho allanol"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Benthyciad allanol"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Benthyciad allanol (print wedi'i anablu)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Archwilio metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Wedi'i gynnwys mewn torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Llyfrgell Z"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Tsieineaidd"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Llwythiadau i AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Mynegai eLyfrau EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadata Tsiec"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Llyfrau Google"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Llyfrgell Wladol Rwsia"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Teitl"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Awdur"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Cyhoeddwr"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Argraffiad"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Blwyddyn cyhoeddi"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Enw ffeil gwreiddiol"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Disgrifiad a sylwadau metadata"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Nid yw lawrlwythiadau Gweinydd Partner ar gael dros dro ar gyfer y ffeil hon."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Gweinydd Partner Cyflym #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(argymhellir)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(dim dilysiad porwr na rhestrau aros)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Gweinydd Partner Araf #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(ychydig yn gyflymach ond gyda rhestr aros)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(dim rhestr aros, ond gall fod yn araf iawn)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Ffeithiol"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ffuglen"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(hefyd cliciwch “GET” ar y brig)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(cliciwch “GET” ar y brig)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "mae eu hysbysebion yn hysbys am gynnwys meddalwedd maleisus, felly defnyddiwch rwystro hysbysebion neu peidiwch â chlicio ar hysbysebion"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Gall ffeiliau Nexus/STC fod yn annibynadwy i'w lawrlwytho)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library ar Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(yn gofyn am Borwr Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Benthyca o'r Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(dim ond i ddeiliaid print anabl)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(efallai na fydd DOI cysylltiedig ar gael yn Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "casgliad"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Lawrlwythiadau torrent swmpus"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(arbenigwyr yn unig)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Chwilio Archif Anna am ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Chwilio amrywiol gronfeydd data eraill am ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Dod o hyd i'r cofnod gwreiddiol yn ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Chwilio Archif Anna am ID Llyfrgell Agored"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Dod o hyd i'r cofnod gwreiddiol yn Llyfrgell Agored"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Chwilio Archif Anna am rif OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Dod o hyd i'r cofnod gwreiddiol yn WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Chwilio Archif Anna am rif SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Chwilio â llaw ar DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Chwilio Archif Anna am rif SSNO CADAL"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Dod o hyd i'r cofnod gwreiddiol yn CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Chwilio Archif Anna am rif DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Mynegai eLyfrau EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Archif Anna 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(dim ond dim dilysu porwr)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata Tsiec %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "disgrifiad"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Enw ffeil amgen"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Teitl arall"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Awdur amgen"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Cyhoeddwr amgen"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Argraffiad amgen"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Estyniad amgen"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "sylwadau metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Disgrifiad arall"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "dyddiad agor ffynhonnell"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Ffeil Sci-Hub “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Ffeil Benthyca Digidol Rheoledig Internet Archive “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Mae hwn yn gofnod o ffeil o'r Internet Archive, nid ffeil y gellir ei lawrlwytho'n uniongyrchol. Gallwch geisio benthyg y llyfr (dolen isod), neu ddefnyddio'r URL hwn wrth <a %(a_request)s>gofyn am ffeil</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Os oes gennych y ffeil hon ac nad yw ar gael eto yn Archif Anna, ystyriwch <a %(a_request)s>ei lanlwytho</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Cofnod metadata ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Cofnod metadata Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Cofnod metadata rhif OCLC (WorldCat) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Cofnod metadata SSID DuXiu %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Cofnod metadata SSNO CADAL %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Cofnod metadata MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Cofnod metadata Nexus/STC ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Mae hwn yn gofnod metadata, nid ffeil y gellir ei lawrlwytho. Gallwch ddefnyddio'r URL hwn wrth <a %(a_request)s>gofyn am ffeil</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata o'r cofnod cysylltiedig"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Gwella metadata ar Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Rhybudd: cofnodion cysylltiedig lluosog:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Gwella metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Adrodd ansawdd ffeil"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Amser lawrlwytho"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Gwefan:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Chwilio Archif Anna am “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Archwiliwr Codau:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Gweld yn Archwiliwr Codau “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Darllen mwy…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Lawrlwythiadau (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Benthyg (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Archwilio metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Sylwadau (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Rhestrau (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Ystadegau (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Manylion technegol"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Efallai bod gan y ffeil hon broblemau, ac mae wedi'i chuddio o lyfrgell ffynhonnell.</span> Weithiau mae hyn ar gais deiliad hawlfraint, weithiau oherwydd bod dewis gwell ar gael, ond weithiau oherwydd problem gyda'r ffeil ei hun. Efallai y bydd yn iawn i'w lawrlwytho, ond rydym yn argymell chwilio am ffeil arall yn gyntaf. Mwy o fanylion:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Efallai bod fersiwn gwell o'r ffeil hon ar gael yn %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Os ydych chi'n dal i fod eisiau lawrlwytho'r ffeil hon, sicrhewch eich bod yn defnyddio meddalwedd dibynadwy, wedi'i diweddaru i'w hagor."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Lawrlwythiadau cyflym"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Lawrlwythiadau cyflym</strong> Dewch yn <a %(a_membership)s>aelod</a> i gefnogi cadwraeth hirdymor llyfrau, papurau, a mwy. I ddangos ein diolch am eich cefnogaeth, cewch lawrlwythiadau cyflym. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Os byddwch yn rhoi rhodd y mis hwn, byddwch yn cael <strong>dwbl</strong> nifer y lawrlwythiadau cyflym."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Lawrlwythiadau cyflym</strong> Mae gennych %(remaining)s ar ôl heddiw. Diolch am fod yn aelod! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Lawrlwythiadau cyflym</strong> Rydych wedi rhedeg allan o lawrlwythiadau cyflym am heddiw."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Lawrlwythiadau cyflym</strong> Rydych wedi lawrlwytho'r ffeil hon yn ddiweddar. Mae dolenni'n parhau'n ddilys am ychydig."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Dewis #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(dim ailgyfeirio)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(agor yn y gwylwyr)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Cyfeiriwch ffrind, a byddwch chi a'ch ffrind yn cael %(percentage)s%% lawrlwythiadau cyflym bonws!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Dysgu mwy…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Lawrlwythiadau araf"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Gan bartneriaid dibynadwy."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mwy o wybodaeth yn y <a %(a_slow)s>Cwestiynau Cyffredin</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(efallai y bydd angen <a %(a_browser)s>gwirio porwr</a> — lawrlwythiadau diderfyn!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Ar ôl lawrlwytho:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Agor yn ein gwylwyr"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "dangos lawrlwythiadau allanol"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Lawrlwythiadau allanol"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Dim lawrlwythiadau wedi'u darganfod."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Mae gan bob opsiwn lawrlwytho yr un ffeil, ac mae'n ddiogel i'w defnyddio. Wedi dweud hynny, byddwch bob amser yn ofalus wrth lawrlwytho ffeiliau o'r rhyngrwyd, yn enwedig o safleoedd allanol i Archif Anna. Er enghraifft, sicrhewch eich bod yn cadw eich dyfeisiau wedi'u diweddaru."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Ar gyfer ffeiliau mawr, rydym yn argymell defnyddio rheolwr lawrlwytho i atal tarfu."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Rheolwyr lawrlwytho a argymhellir: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Bydd angen darllenydd e-lyfr neu PDF arnoch i agor y ffeil, yn dibynnu ar y fformat ffeil."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Darllenwyr e-lyfrau a argymhellir: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Gwylwyr ar-lein Archif Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Defnyddiwch offer ar-lein i drosi rhwng fformatau."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Offer trosi a argymhellir: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Gallwch anfon ffeiliau PDF ac EPUB i'ch Kindle neu Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Offer a argymhellir: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Anfon i Kindle” gan Amazon"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Anfon i Kobo/Kindle” gan djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Cefnogi awduron a llyfrgelloedd"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Os ydych chi'n hoffi hyn ac yn gallu ei fforddio, ystyriwch brynu'r gwreiddiol, neu gefnogi'r awduron yn uniongyrchol."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Os yw hwn ar gael yn eich llyfrgell leol, ystyriwch ei fenthyg am ddim yno."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Ansawdd ffeil"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Helpwch y gymuned trwy adrodd ansawdd y ffeil hon! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Adrodd problem ffeil (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Ansawdd ffeil gwych (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Ychwanegu sylw (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Beth sy'n bod ar y ffeil hon?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Defnyddiwch y <a %(a_copyright)s>ffurflen hawliad DMCA / Hawlfraint</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Disgrifiwch y broblem (gofynnol)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Disgrifiad o'r broblem"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 o fersiwn gwell o'r ffeil hon (os yn berthnasol)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Llenwch hyn os oes ffeil arall sy'n cyfateb yn agos i'r ffeil hon (yr un argraffiad, yr un estyniad ffeil os gallwch ddod o hyd i un), y dylai pobl ei ddefnyddio yn lle'r ffeil hon. Os ydych chi'n gwybod am fersiwn gwell o'r ffeil hon y tu allan i Archif Anna, yna os gwelwch yn dda <a %(a_upload)s>lanlwythwch hi</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Gallwch gael yr md5 o'r URL, e.e."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Cyflwyno adroddiad"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Dysgwch sut i <a %(a_metadata)s>wella'r metadata</a> ar gyfer y ffeil hon eich hun."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Diolch am gyflwyno eich adroddiad. Bydd yn cael ei ddangos ar y dudalen hon, yn ogystal â chael ei adolygu'n llaw gan Anna (nes bod gennym system gymedroli briodol)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Os oes gan y ffeil hon ansawdd gwych, gallwch drafod unrhyw beth amdani yma! Os nad yw, defnyddiwch y botwm “Adrodd problem ffeil”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Roeddwn i wrth fy modd â'r llyfr hwn!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Gadael sylw"

#, fuzzy
msgid "common.english_only"
msgstr "Mae'r testun isod yn parhau yn Saesneg."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Cyfanswm lawrlwythiadau: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Mae “MD5 ffeil” yn hash sy'n cael ei gyfrifo o gynnwys y ffeil, ac mae'n eithaf unigryw yn seiliedig ar y cynnwys hwnnw. Mae'r holl lyfrgelloedd cysgodol yr ydym wedi'u mynegeio yma yn defnyddio MD5s yn bennaf i adnabod ffeiliau."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Efallai y bydd ffeil yn ymddangos mewn sawl llyfrgell gysgodol. Am wybodaeth am y datasets amrywiol yr ydym wedi'u llunio, gweler y <a %(a_datasets)s>Tudalen Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Mae hon yn ffeil a reolir gan lyfrgell <a %(a_ia)s>Benthyca Digidol Rheoledig IA</a>, ac wedi'i mynegeio gan Archif Anna ar gyfer chwilio. Am wybodaeth am y datasets amrywiol yr ydym wedi'u llunio, gweler y <a %(a_datasets)s>Tudalen Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Am wybodaeth am y ffeil benodol hon, edrychwch ar ei <a %(a_href)s>ffeil JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problem wrth lwytho'r dudalen hon"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Adnewyddwch i roi cynnig arall arni. <a %(a_contact)s>Cysylltwch â ni</a> os yw'r broblem yn parhau am oriau lawer."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Heb ei ganfod"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "Ni chanfuwyd “%(md5_input)s” yn ein cronfa ddata."

#, fuzzy
msgid "page.login.title"
msgstr "Mewngofnodi / Cofrestru"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Gwirio porwr"

#, fuzzy
msgid "page.login.text1"
msgstr "Er mwyn atal spam-bots rhag creu llawer o gyfrifon, mae angen i ni wirio eich porwr yn gyntaf."

#, fuzzy
msgid "page.login.text2"
msgstr "Os ydych chi'n cael eich dal mewn dolen anfeidrol, rydym yn argymell gosod <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Efallai y bydd hefyd yn helpu i ddiffodd blocwyr hysbysebion ac estyniadau porwr eraill."

#, fuzzy
msgid "page.codes.title"
msgstr "Codau"

#, fuzzy
msgid "page.codes.heading"
msgstr "Archwiliwr Codau"

#, fuzzy
msgid "page.codes.intro"
msgstr "Archwiliwch y codau y mae cofnodion wedi'u tagio gyda nhw, yn ôl rhagddodiad. Mae'r golofn “cofnodion” yn dangos nifer y cofnodion sydd wedi'u tagio gyda chodau gyda'r rhagddodiad penodol, fel y gwelir yn y peiriant chwilio (gan gynnwys cofnodion metadata yn unig). Mae'r golofn “codau” yn dangos faint o godau gwirioneddol sydd â rhagddodiad penodol."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Gallai gymryd peth amser i gynhyrchu'r dudalen hon, a dyna pam mae angen captcha Cloudflare. Gall <a %(a_donate)s>Aelodau</a> hepgor y captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Peidiwch â sgrapio'r tudalennau hyn. Yn lle hynny rydym yn argymell <a %(a_import)s>cynhyrchu</a> neu <a %(a_download)s>lawrlwytho</a> ein cronfeydd data ElasticSearch a MariaDB, a rhedeg ein <a %(a_software)s>cod ffynhonnell agored</a>. Gellir archwilio'r data crai yn llaw drwy ffeiliau JSON fel <a %(a_json_file)s>hwn</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Rhagddodiad"

#, fuzzy
msgid "common.form.go"
msgstr "Ewch"

#, fuzzy
msgid "common.form.reset"
msgstr "Ailosod"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Chwilio Archif Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Rhybudd: mae gan y cod gymeriadau Unicode anghywir ynddo, a gallai ymddwyn yn anghywir mewn amrywiol sefyllfaoedd. Gellir dadgodio'r deuaidd crai o'r cynrychiolaeth base64 yn yr URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Rhagddodiad cod hysbys “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Rhagddodiad"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Disgrifiad"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL ar gyfer cod penodol"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "Bydd “%%s” yn cael eu disodli gyda gwerth y cod"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL cyffredinol"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Gwefan"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "0 cofnod yn cyfateb i “%(prefix_label)s”"
msgstr[1] "%(count)s cofnod yn cyfateb i “%(prefix_label)s”"
msgstr[2] "2 gofnod yn cyfateb i “%(prefix_label)s”"
msgstr[3] "3 cofnod yn cyfateb i “%(prefix_label)s”"
msgstr[4] "6 cofnod yn cyfateb i “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL ar gyfer cod penodol: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mwy…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codau sy'n dechrau gyda “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Mynegai o"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "cofnodion"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codau"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Fewer than %(count)s cofnod"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Ar gyfer hawliadau DMCA / hawlfraint, defnyddiwch <a %(a_copyright)s>y ffurflen hon</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Bydd unrhyw ffyrdd eraill o gysylltu â ni am hawliadau hawlfraint yn cael eu dileu'n awtomatig."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Rydym yn croesawu eich adborth a'ch cwestiynau yn fawr iawn!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Fodd bynnag, oherwydd y nifer o negeseuon sbam a negeseuon e-bost nonsens rydym yn eu derbyn, gwiriwch y blychau i gadarnhau eich bod yn deall yr amodau hyn ar gyfer cysylltu â ni."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Bydd hawliadau hawlfraint i'r e-bost hwn yn cael eu hanwybyddu; defnyddiwch y ffurflen yn lle hynny."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Nid yw gweinyddion partner ar gael oherwydd cau lletya. Dylent fod ar gael eto cyn bo hir."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Bydd aelodaeth yn cael ei hymestyn yn unol â hynny."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Peidiwch ag anfon e-bost atom i <a %(a_request)s>ofyn am lyfrau</a><br>neu lanlwythiadau bach (<10k) <a %(a_upload)s>lanlwythiadau</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Wrth ofyn cwestiynau am gyfrif neu roddion, ychwanegwch eich ID cyfrif, sgrinluniau, derbynebau, cymaint o wybodaeth â phosibl. Dim ond bob 1-2 wythnos rydym yn gwirio ein e-bost, felly bydd peidio â chynnwys y wybodaeth hon yn oedi unrhyw ddatrysiad."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Dangos e-bost"

#, fuzzy
msgid "page.copyright.title"
msgstr "Ffurflen hawliad DMCA / Hawlfraint"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Os oes gennych hawliad DCMA neu hawlfraint arall, llenwch y ffurflen hon mor fanwl â phosibl. Os ydych yn cael unrhyw broblemau, cysylltwch â ni yn ein cyfeiriad DMCA pwrpasol: %(email)s. Sylwch na fydd hawliadau a anfonir trwy e-bost i'r cyfeiriad hwn yn cael eu prosesu, dim ond ar gyfer cwestiynau ydyw. Defnyddiwch y ffurflen isod i gyflwyno eich hawliadau."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLau ar Archif Anna (gofynnol). Un fesul llinell. Rhowch URLau sy'n disgrifio'r un argraffiad union o lyfr yn unig. Os ydych am wneud hawliad ar gyfer sawl llyfr neu sawl argraffiad, cyflwynwch y ffurflen hon sawl gwaith."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Bydd hawliadau sy'n cynnwys sawl llyfr neu argraffiad gyda'i gilydd yn cael eu gwrthod."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Eich enw (gofynnol)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Cyfeiriad (gofynnol)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Rhif ffôn (gofynnol)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-bost (gofynnol)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Disgrifiad clir o'r deunydd ffynhonnell (gofynnol)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs y deunydd ffynhonnell (os yn berthnasol). Un fesul llinell. Rhowch dim ond y rhai sy'n cyfateb yn union i'r argraffiad yr ydych yn adrodd hawliad hawlfraint ar ei gyfer."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs y deunydd ffynhonnell, un fesul llinell. Cymerwch funud i chwilio Open Library am eich deunydd ffynhonnell. Bydd hyn yn ein helpu i wirio eich hawliad."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs i'r deunydd ffynhonnell, un fesul llinell (gofynnol). Rhowch gymaint â phosibl, i'n helpu i wirio eich hawliad (e.e. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Datganiad a llofnod (gofynnol)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Cyflwyno hawliad"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Diolch am gyflwyno eich hawliad hawlfraint. Byddwn yn ei adolygu cyn gynted â phosibl. Ail-lwythwch y dudalen i gyflwyno un arall."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Aeth rhywbeth o'i le. Ail-lwythwch y dudalen a cheisiwch eto."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Os ydych chi'n ymddiddori mewn adlewyrchu'r set ddata hon at ddibenion <a %(a_archival)s>archifo</a> neu <a %(a_llm)s>hyfforddi LLM</a>, cysylltwch â ni."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Ein cenhadaeth yw archifo'r holl lyfrau yn y byd (yn ogystal â phapurau, cylchgronau, ac ati), a'u gwneud yn eang hygyrch. Credwn y dylai pob llyfr gael ei adlewyrchu'n eang, i sicrhau dyblygu a gwydnwch. Dyma pam rydym yn casglu ffeiliau o amrywiaeth o ffynonellau. Mae rhai ffynonellau yn gwbl agored ac yn gallu cael eu hadlewyrchu'n swmpus (fel Sci-Hub). Mae eraill yn gaeedig ac yn amddiffynnol, felly rydym yn ceisio eu sgrapio er mwyn “rhyddhau” eu llyfrau. Mae eraill yn disgyn rhywle yn y canol."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Gellir <a %(a_torrents)s>torrenti</a> ein holl ddata, a gellir <a %(a_anna_software)s>creu</a> neu <a %(a_elasticsearch)s>lawrlwytho</a> ein holl metadata fel cronfeydd data ElasticSearch a MariaDB. Gellir archwilio'r data crai yn llaw trwy ffeiliau JSON fel <a %(a_dbrecord)s>hwn</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Trosolwg"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Isod mae trosolwg cyflym o ffynonellau'r ffeiliau ar Archif Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Ffynhonnell"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Maint"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% wedi'i adlewyrchu gan AA / torrents ar gael"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Canrannau nifer y ffeiliau"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Diweddarwyd ddiwethaf"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Ffuglen a Ffuglen"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "0 ffeiliau"
msgstr[1] "%(count)s ffeil"
msgstr[2] "2 ffeiliau"
msgstr[3] "3 ffeiliau"
msgstr[4] "6 ffeiliau"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Trwy Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: wedi'i rewi ers 2021; y rhan fwyaf ar gael trwy dorrenti"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: ychwanegiadau bach ers hynny</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Ac eithrio “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Mae torrenti ffuglen ar ei hôl hi (er bod IDs ~4-6M heb eu torrenti gan eu bod yn gorgyffwrdd â'n torrenti Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Mae'r casgliad “Tsieineaidd” yn Z-Library yn ymddangos yr un fath â'n casgliad DuXiu, ond gyda MD5s gwahanol. Rydym yn eithrio'r ffeiliau hyn o dorrents i osgoi dyblygu, ond yn dal i'w dangos yn ein mynegai chwilio."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Benthyca Digidol Rheoledig IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ o ffeiliau yn chwiliadwy."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Cyfanswm"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Heb gynnwys dyblygu"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Gan fod y llyfrgelloedd cysgodol yn aml yn cysoni data o'i gilydd, mae llawer o orgyffwrdd rhwng y llyfrgelloedd. Dyna pam nad yw'r niferoedd yn cyfateb i'r cyfanswm."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Mae'r ganran “wedi'i hadlewyrchu a'i hau gan Archif Anna” yn dangos faint o ffeiliau rydym yn eu hadlewyrchu ein hunain. Rydym yn hau'r ffeiliau hynny yn swmp trwy dorrents, ac yn eu gwneud ar gael i'w lawrlwytho'n uniongyrchol trwy wefannau partner."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Llyfrgelloedd ffynhonnell"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Mae rhai llyfrgelloedd ffynhonnell yn hyrwyddo rhannu swmpus eu data drwy dorrenti, tra nad yw eraill yn rhannu eu casgliad yn rhwydd. Yn yr achos olaf, mae Archif Anna yn ceisio sgrapio eu casgliadau, a'u gwneud ar gael (gweler ein tudalen <a %(a_torrents)s>Torrenti</a>). Mae hefyd sefyllfaoedd canol, er enghraifft, lle mae llyfrgelloedd ffynhonnell yn barod i rannu, ond nad oes ganddynt yr adnoddau i wneud hynny. Yn yr achosion hynny, rydym hefyd yn ceisio helpu."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Isod mae trosolwg o sut rydym yn rhyngwynebu gyda'r gwahanol lyfrgelloedd ffynhonnell."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Ffynhonnell"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Ffeiliau"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dympiau cronfa ddata <a %(dbdumps)s>HTTP dyddiol</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents awtomataidd ar gyfer <a %(nonfiction)s>Ffuglen</a> a <a %(fiction)s>Anffuglen</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(covers)s>dorrents cloriau llyfrau</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Mae Sci-Hub wedi rhewi ffeiliau newydd ers 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dympiau metadata ar gael <a %(scihub1)s>yma</a> ac <a %(scihub2)s>yma</a>, yn ogystal â rhan o gronfa ddata <a %(libgenli)s>Libgen.li</a> (yr ydym yn ei ddefnyddio)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents data ar gael <a %(scihub1)s>yma</a>, <a %(scihub2)s>yma</a>, ac <a %(libgenli)s>yma</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Mae rhai ffeiliau newydd yn <a %(libgenrs)s>cael eu</a> <a %(libgenli)s>hychwanegu</a> at “scimag” Libgen, ond nid digon i gyfiawnhau torrents newydd"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dympiau cronfa ddata <a %(dbdumps)s>HTTP chwarterol</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Rhennir torrents Anffuglen gyda Libgen.rs (ac wedi'u hadlewyrchu <a %(libgenli)s>yma</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Mae Archif Anna a Libgen.li yn cyd-reoli casgliadau o <a %(comics)s>lyfrau comig</a>, <a %(magazines)s>cylchgronau</a>, <a %(standarts)s>dogfennau safonol</a>, a <a %(fiction)s>ffuglen (wedi gwahanu o Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Nid oes gan eu casgliad “fiction_rus” (ffuglen Rwsiaidd) dorrents pwrpasol, ond mae'n cael ei gwmpasu gan dorrents eraill, ac rydym yn cadw <a %(fiction_rus)s>drych</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Mae Archif Anna a Z-Library yn rheoli casgliad o <a %(metadata)s>metadata Z-Library</a> a <a %(files)s>ffeiliau Z-Library</a> ar y cyd"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Mae rhywfaint o metadata ar gael trwy <a %(openlib)s>dympiau cronfa ddata Open Library</a>, ond nid ydynt yn cwmpasu'r casgliad IA cyfan"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Dim dympiau metadata hawdd eu cyrchu ar gael ar gyfer eu casgliad cyfan"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(ia)s>metadata IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Ffeiliau ar gael i'w benthyg yn unig ar sail gyfyngedig, gyda gwahanol gyfyngiadau mynediad"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(ia)s>ffeiliau IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Amrywiol gronfeydd data metadata wedi'u gwasgaru o amgylch y rhyngrwyd Tsieineaidd; er yn aml cronfeydd data taledig"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Dim llwythiadau metadata hawdd eu cyrraedd ar gael ar gyfer eu casgliad cyfan."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(duxiu)s>metadata DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Amrywiol gronfeydd data ffeiliau wedi'u gwasgaru o gwmpas rhyngrwyd Tsieineaidd; er yn aml cronfeydd data taledig"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Mae'r rhan fwyaf o ffeiliau ond yn hygyrch gan ddefnyddio cyfrifon BaiduYun premiwm; cyflymderau lawrlwytho araf."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(duxiu)s>ffeiliau DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Amrywiol ffynonellau llai neu unwaith yn unig. Rydym yn annog pobl i lwytho i fyny i lyfrgelloedd cysgodol eraill yn gyntaf, ond weithiau mae gan bobl gasgliadau sy'n rhy fawr i eraill eu didoli, er nad ydynt yn ddigon mawr i haeddu eu categori eu hunain."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Ffynonellau metadata yn unig"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Rydym hefyd yn cyfoethogi ein casgliad gyda ffynonellau metadata yn unig, y gallwn eu paru â ffeiliau, e.e. gan ddefnyddio rhifau ISBN neu feysydd eraill. Isod mae trosolwg o'r rheini. Unwaith eto, mae rhai o'r ffynonellau hyn yn gwbl agored, tra ar gyfer eraill mae'n rhaid i ni sgrapio nhw."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Ein hysbrydoliaeth ar gyfer casglu metadata yw nod Aaron Swartz o “un dudalen we ar gyfer pob llyfr a gyhoeddwyd erioed”, y creodd <a %(a_openlib)s>Open Library</a> ar ei gyfer. Mae'r prosiect hwnnw wedi gwneud yn dda, ond mae ein sefyllfa unigryw yn ein galluogi i gael metadata na allant hwy. Ysbrydoliaeth arall oedd ein hawydd i wybod <a %(a_blog)s>faint o lyfrau sydd yn y byd</a>, fel y gallwn gyfrifo faint o lyfrau sydd gennym ar ôl i'w hachub."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nodwch, yn y chwiliad metadata, rydym yn dangos y cofnodion gwreiddiol. Nid ydym yn uno unrhyw gofnodion."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Diweddarwyd ddiwethaf"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Llwythiadau cronfa ddata misol <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Ddim ar gael yn uniongyrchol mewn swmp, wedi'i ddiogelu rhag sgrapio"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Cronfa ddata unedig"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Rydym yn cyfuno'r holl ffynonellau uchod i mewn i un gronfa ddata unedig a ddefnyddiwn i wasanaethu'r wefan hon. Nid yw'r gronfa ddata unedig hon ar gael yn uniongyrchol, ond gan fod Archif Anna yn gwbl agored, gellir ei <a %(a_generated)s>chynhyrchu</a> neu ei <a %(a_downloaded)s>lawrlwytho</a> yn eithaf hawdd fel cronfeydd data ElasticSearch a MariaDB. Bydd y sgriptiau ar y dudalen honno yn lawrlwytho'r holl metadata angenrheidiol yn awtomatig o'r ffynonellau a grybwyllir uchod."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Os hoffech archwilio ein data cyn rhedeg y sgriptiau hynny yn lleol, gallwch edrych ar ein ffeiliau JSON, sy'n cysylltu ymhellach â ffeiliau JSON eraill. <a %(a_json)s>Mae'r ffeil hon</a> yn fan cychwyn da."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Addaswyd o'n <a %(a_href)s>cofnod blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> yw cronfa ddata enfawr o lyfrau wedi'u sganio, a grëwyd gan y <a %(superstar_link)s>SuperStar Digital Library Group</a>. Mae'r rhan fwyaf yn llyfrau academaidd, wedi'u sganio er mwyn eu gwneud ar gael yn ddigidol i brifysgolion a llyfrgelloedd. Ar gyfer ein cynulleidfa Saesneg, mae <a %(princeton_link)s>Princeton</a> a <a %(uw_link)s>Phrifysgol Washington</a> yn cynnig trosolwg da. Mae hefyd erthygl ragorol yn rhoi mwy o gefndir: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Mae llyfrau o Duxiu wedi cael eu hacio ers amser maith ar y rhyngrwyd Tsieineaidd. Fel arfer, maent yn cael eu gwerthu am lai na doler gan ailwerthwyr. Maent yn cael eu dosbarthu'n nodweddiadol gan ddefnyddio'r hyn sy'n cyfateb i Google Drive yn Tsieina, sydd wedi cael ei hacio'n aml i ganiatáu mwy o le storio. Gellir dod o hyd i rai manylion technegol <a %(link1)s>yma</a> ac <a %(link2)s>yma</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Er bod y llyfrau wedi cael eu dosbarthu'n lled-gyhoeddus, mae'n eithaf anodd eu cael mewn swmp. Roedd hyn yn uchel ar ein rhestr TODO, ac fe wnaethom neilltuo sawl mis o waith llawn amser ar ei gyfer. Fodd bynnag, yn hwyr yn 2023, cysylltodd gwirfoddolwr anhygoel, rhyfeddol a thalentog â ni, gan ddweud eu bod wedi gwneud yr holl waith hwn eisoes — ar gost fawr. Rhannodd y casgliad llawn gyda ni, heb ddisgwyl unrhyw beth yn ôl, ac eithrio'r warant o gadw hirdymor. Yn wir, yn rhyfeddol."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Adnoddau"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Cyfanswm ffeiliau: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Cyfanswm maint ffeiliau: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Ffeiliau wedi'u hadlewyrchu gan Archif Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Diweddarwyd ddiwethaf: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents gan Archif Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Cofnod enghreifftiol ar Archif Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Ein cofnod blog am y data hwn"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Sgriptiau ar gyfer mewnforio metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Fformat Cynwysyddion Archif Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mwy o wybodaeth gan ein gwirfoddolwyr (nodiadau crai):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "Llogi Digidol Rheoledig IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Mae'r set ddata hon yn gysylltiedig yn agos â'r <a %(a_datasets_openlib)s>set ddata Open Library</a>. Mae'n cynnwys sgrap o'r holl fetadata a rhan fawr o ffeiliau o Lyfrgell Benthyca Digidol Rheoledig IA. Caiff diweddariadau eu rhyddhau yn y <a %(a_aac)s>fformat Cynwysyddion Archif Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Cyfeirir at y cofnodion hyn yn uniongyrchol o'r set ddata Open Library, ond mae hefyd yn cynnwys cofnodion nad ydynt yn Open Library. Mae gennym hefyd nifer o ffeiliau data a sgrapiwyd gan aelodau'r gymuned dros y blynyddoedd."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Mae'r casgliad yn cynnwys dwy ran. Mae angen y ddwy ran arnoch i gael yr holl ddata (ac eithrio torrenti sydd wedi'u disodli, sydd wedi'u croesi allan ar y dudalen torrenti)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "ein rhyddhau cyntaf, cyn i ni safoni ar y <a %(a_aac)s>Fformat Cynwysyddion Archif Anna (AAC)</a>. Yn cynnwys metadata (fel json ac xml), pdfs (o systemau benthyca digidol acsm a lcpdf), a mân-luniau cloriau."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "rhyddhau newydd cynyddol, gan ddefnyddio AAC. Dim ond metadata gyda stampiau amser ar ôl 2023-01-01 sydd wedi'i gynnwys, gan fod y gweddill eisoes wedi'i gwmpasu gan “ia”. Hefyd pob ffeil pdf, y tro hwn o'r systemau benthyca acsm a “bookreader” (darllenydd gwe IA). Er nad yw'r enw'n hollol gywir, rydym yn dal i boblogi ffeiliau bookreader i'r casgliad ia2_acsmpdf_files, gan eu bod yn unigryw."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Prif wefan %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Llyfrgell Benthyca Ddigidol"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dogfennaeth metadata (y mwyafrif o feysydd)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Gwybodaeth gwlad ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Mae'r Asiantaeth ISBN Ryngwladol yn rhyddhau'r ystodau y mae wedi'u dyrannu i asiantaethau ISBN cenedlaethol yn rheolaidd. O hyn gallwn ddeillio pa wlad, rhanbarth, neu grŵp iaith y mae'r ISBN hwn yn perthyn iddo. Ar hyn o bryd rydym yn defnyddio'r data hwn yn anuniongyrchol, trwy'r llyfrgell Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Adnoddau"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Diweddarwyd ddiwethaf: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Gwefan ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Am gefndir y ffyrc gwahanol o Library Genesis, gweler y dudalen ar gyfer <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Mae gan Libgen.li y rhan fwyaf o'r un cynnwys a metadata â Libgen.rs, ond mae ganddo rai casgliadau ar ben hyn, sef comics, cylchgronau, a dogfennau safonol. Mae hefyd wedi integreiddio <a %(a_scihub)s>Sci-Hub</a> i'w metadata a pheiriant chwilio, sef yr hyn rydym yn ei ddefnyddio ar gyfer ein cronfa ddata."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Mae'r metadata ar gyfer y llyfrgell hon ar gael yn rhydd <a %(a_libgen_li)s>yn libgen.li</a>. Fodd bynnag, mae'r gweinydd hwn yn araf ac nid yw'n cefnogi ailddechrau cysylltiadau wedi torri. Mae'r un ffeiliau hefyd ar gael ar <a %(a_ftp)s>weinydd FTP</a>, sy'n gweithio'n well."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Mae torrents ar gael ar gyfer y rhan fwyaf o'r cynnwys ychwanegol, yn enwedig mae torrents ar gyfer comics, cylchgronau, a dogfennau safonol wedi'u rhyddhau mewn cydweithrediad ag Archif Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Mae gan y casgliad ffuglen ei dorrents ei hun (wedi gwahanu o <a %(a_href)s>Libgen.rs</a>) gan ddechrau yn %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Yn ôl gweinyddwr Libgen.li, dylai'r casgliad “fiction_rus” (ffuglen Rwsiaidd) gael ei gwmpasu gan dorrents a ryddheir yn rheolaidd o <a %(a_booktracker)s>booktracker.org</a>, yn enwedig y torrents <a %(a_flibusta)s>flibusta</a> a <a %(a_librusec)s>lib.rus.ec</a> (yr ydym yn eu drych <a %(a_torrents)s>yma</a>, er nad ydym wedi sefydlu eto pa dorrents sy'n cyfateb i ba ffeiliau)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Gellir dod o hyd i ystadegau ar gyfer pob casgliad <a %(a_href)s>ar wefan libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Mae'n ymddangos bod ffeithiol hefyd wedi gwyro, ond heb donnau newydd. Mae'n ymddangos bod hyn wedi digwydd ers dechrau 2022, er nad ydym wedi gwirio hyn."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Mae'n debyg bod rhai ystodau heb dorrents (megis ystodau ffuglen f_3463000 i f_4260000) yn ffeiliau Llyfrgell Z (neu ddyblyg arall), er efallai y byddwn am wneud rhywfaint o ddeduplication a gwneud torrents ar gyfer ffeiliau unigryw lgli yn yr ystodau hyn."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Sylwch fod y ffeiliau torrent sy'n cyfeirio at “libgen.is” yn ddrych penodol o <a %(a_libgen)s>Libgen.rs</a> (“.is” yw parth gwahanol a ddefnyddir gan Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Adnodd defnyddiol wrth ddefnyddio'r metadata yw <a %(a_href)s>y dudalen hon</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrenti ffuglen ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrenti comics ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrenti cylchgronau ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents dogfennau safonol ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents ffuglen Rwsiaidd ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata trwy FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Gwybodaeth maes metadata"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Drych o dorrenti eraill (a thorrenti ffuglen a comics unigryw)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Fforwm trafod"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Ein post blog am ryddhau'r llyfrau comics"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Mae'r stori gyflym am y ffyrc gwahanol o Library Genesis (neu “Libgen”) yn dweud bod y bobl wahanol a oedd yn ymwneud â Library Genesis wedi cwympo allan dros amser, ac wedi mynd eu ffyrdd ar wahân."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Crëwyd y fersiwn “.fun” gan y sylfaenydd gwreiddiol. Mae'n cael ei ailwampio o blaid fersiwn newydd, mwy dosbarthedig."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Mae gan y fersiwn “.rs” ddata tebyg iawn, ac mae'n rhyddhau eu casgliad yn gyson mewn torrenti swmpus. Mae'n cael ei rannu'n fras yn adran “ffuglen” ac adran “anfuglen”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Yn wreiddiol yn “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Mae gan y <a %(a_li)s>fersiwn “.li”</a> gasgliad enfawr o gomics, yn ogystal â chynnwys arall, nad yw (eto) ar gael i'w lawrlwytho'n swmpus trwy dorrenti. Mae ganddo gasgliad torrent ar wahân o lyfrau ffuglen, ac mae'n cynnwys metadata <a %(a_scihub)s>Sci-Hub</a> yn ei gronfa ddata."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Yn ôl y <a %(a_mhut)s>swydd fforwm hon</a>, roedd Libgen.li yn wreiddiol wedi'i gynnal yn “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "Mae <a %(a_zlib)s>Z-Library</a> mewn rhyw ystyr hefyd yn fforc o Library Genesis, er eu bod wedi defnyddio enw gwahanol ar gyfer eu prosiect."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Mae'r dudalen hon yn ymwneud â'r fersiwn “.rs”. Mae'n adnabyddus am gyhoeddi ei metadata a chynnwys llawn ei gatalog llyfrau yn gyson. Mae ei gasgliad llyfrau wedi'i rannu rhwng adran ffuglen ac adran anfuglen."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Adnodd defnyddiol wrth ddefnyddio'r metadata yw <a %(a_metadata)s>y dudalen hon</a> (yn blocio ystodau IP, efallai y bydd angen VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "O fis Mawrth 2024, mae torrenti newydd yn cael eu postio yn <a %(a_href)s>y edefyn fforwm hwn</a> (yn blocio ystodau IP, efallai y bydd angen VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrenti Ffeithiol ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrenti Ffuglen ar Archif Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Gwybodaeth maes metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrenti Ffeithiol Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrenti Ffuglen Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Fforwm trafod Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrenti gan Archif Anna (cloriau llyfrau)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Ein blog am y rhyddhau cloriau llyfrau"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Mae Library Genesis yn adnabyddus am wneud eu data ar gael yn hael mewn swmp drwy dorrenti. Mae ein casgliad Libgen yn cynnwys data ategol nad ydynt yn ei ryddhau'n uniongyrchol, mewn partneriaeth â hwy. Diolch yn fawr i bawb sy'n ymwneud â Library Genesis am weithio gyda ni!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Rhyddhau 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Mae'r <a %(blog_post)s>rhyddhau cyntaf</a> hwn yn eithaf bach: tua 300GB o gloriau llyfrau o'r fforc Libgen.rs, yn cynnwys ffuglen a ffeithiol. Maent wedi'u trefnu yn yr un ffordd ag y maent yn ymddangos ar libgen.rs, e.e.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s ar gyfer llyfr ffeithiol."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s ar gyfer llyfr ffuglen."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Yn union fel gyda'r casgliad Z-Library, rydym wedi rhoi'r cyfan mewn ffeil .tar fawr, y gellir ei mowntio gan ddefnyddio <a %(a_ratarmount)s>ratarmount</a> os ydych chi eisiau gwasanaethu'r ffeiliau'n uniongyrchol."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> yw cronfa ddata berchnogol gan y sefydliad dielw <a %(a_oclc)s>OCLC</a>, sy'n casglu cofnodion metadata o lyfrgelloedd ledled y byd. Mae'n debygol mai dyma'r casgliad metadata llyfrgell fwyaf yn y byd."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Hydref 2023, rhyddhau cychwynnol:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Ym mis Hydref 2023 rydym <a %(a_scrape)s>wedi rhyddhau</a> sgrap cynhwysfawr o gronfa ddata OCLC (WorldCat), yn y <a %(a_aac)s>fformat Cynwysyddion Archif Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrenti gan Archif Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Ein post blog am y data hwn"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Mae Open Library yn brosiect ffynhonnell agored gan yr Internet Archive i gatalogio pob llyfr yn y byd. Mae ganddo un o'r gweithrediadau sganio llyfrau mwyaf yn y byd, ac mae ganddo lawer o lyfrau ar gael ar gyfer benthyca digidol. Mae ei gatalog metadata llyfrau ar gael yn rhydd i'w lawrlwytho, ac mae wedi'i gynnwys ar Archif Anna (er nad yw ar hyn o bryd yn y chwiliad, ac eithrio os ydych chi'n chwilio'n benodol am ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Rhyddhad 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Mae hwn yn ddymp o lawer o alwadau i isbndb.com yn ystod mis Medi 2022. Ceisiasom gwmpasu'r holl ystodau ISBN. Mae tua 30.9 miliwn o gofnodion. Ar eu gwefan maent yn honni bod ganddynt mewn gwirionedd 32.6 miliwn o gofnodion, felly efallai ein bod wedi colli rhai, neu <em>efallai</em> eu bod yn gwneud rhywbeth o'i le."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Mae'r ymatebion JSON yn eithaf crai o'u gweinydd. Un mater ansawdd data a sylwyd gennym, yw bod ar gyfer rhifau ISBN-13 sy'n dechrau gyda rhagddodiad gwahanol i “978-”, maent yn dal i gynnwys maes “isbn” sy'n syml yw'r rhif ISBN-13 gyda'r tri rhif cyntaf wedi'u torri i ffwrdd (a'r digid gwirio wedi'i ailgyfrifo). Mae hyn yn amlwg yn anghywir, ond dyma sut maent yn ymddangos i'w wneud, felly ni wnaethom ei newid."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Mater posibl arall y gallech ddod ar ei draws, yw'r ffaith bod maes “isbn13” yn cynnwys dyblygu, felly ni allwch ei ddefnyddio fel allwedd gynradd mewn cronfa ddata. Mae'n ymddangos bod meysydd “isbn13”+“isbn” wedi'u cyfuno yn unigryw."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Am wybodaeth gefndir am Sci-Hub, cyfeiriwch at ei <a %(a_scihub)s>wefan swyddogol</a>, <a %(a_wikipedia)s>tudalen Wicipedia</a>, a'r <a %(a_radiolab)s>cyfweliad podlediad</a> hwn."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Sylwch fod Sci-Hub wedi cael ei <a %(a_reddit)s>rewi ers 2021</a>. Cafodd ei rewi o'r blaen, ond yn 2021 ychwanegwyd ychydig filiynau o bapurau. Serch hynny, ychwanegir nifer cyfyngedig o bapurau at gasgliadau “scimag” Libgen, er nad yw'n ddigon i warantu torrenti swmp newydd."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Rydym yn defnyddio metadata Sci-Hub fel y'i darperir gan <a %(a_libgen_li)s>Libgen.li</a> yn ei gasgliad “scimag”. Rydym hefyd yn defnyddio'r set ddata <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Sylwch fod y torrenti “smarch” wedi'u <a %(a_smarch)s>dirymu</a> ac felly nid ydynt wedi'u cynnwys yn ein rhestr torrenti."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrenti ar Archif Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata a thorrenti"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrenti ar Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrenti ar Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Diweddariadau ar Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Tudalen Wicipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Cyfweliad podlediad"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Llwythiadau i Archif Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Trosolwg o <a %(a1)s>dudalen datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Amrywiol ffynonellau llai neu unwaith yn unig. Rydym yn annog pobl i lwytho i fyny i lyfrgelloedd cysgodol eraill yn gyntaf, ond weithiau mae gan bobl gasgliadau sy'n rhy fawr i eraill eu didoli, er nad ydynt yn ddigon mawr i haeddu eu categori eu hunain."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Mae'r casgliad “llwytho i fyny” wedi'i rannu'n is-gasgliadau llai, sy'n cael eu nodi yn yr AACIDs ac enwau torrent. Cafodd pob is-gasgliad ei ddad-ddyblygu yn erbyn y prif gasgliad yn gyntaf, er bod y ffeiliau JSON “upload_records” metadata yn dal i gynnwys llawer o gyfeiriadau at y ffeiliau gwreiddiol. Cafodd ffeiliau nad ydynt yn llyfrau eu tynnu hefyd o'r rhan fwyaf o is-gasgliadau, ac fel arfer <em>nid ydynt</em> wedi'u nodi yn y “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Mae llawer o is-gasgliadau eu hunain yn cynnwys is-is-gasgliadau (e.e. o wahanol ffynonellau gwreiddiol), sy'n cael eu cynrychioli fel cyfeiriaduron yn y meysydd “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Yr is-gasgliadau yw:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Is-gasgliad"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Nodiadau"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "pori"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "chwilio"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "O <a %(a_href)s>aaaaarg.fail</a>. Ymddengys yn weddol gyflawn. Gan ein gwirfoddolwr “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "O <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Mae ganddo ormodedd eithaf uchel gyda chasgliadau papurau presennol, ond ychydig iawn o gyfatebion MD5, felly penderfynasom ei gadw'n gyfan gwbl."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Crafiad o <q>iRead eBooks</q> (= ffonetig <q>ai rit i-books</q>; airitibooks.com), gan wirfoddolwr <q>j</q>. Yn cyfateb i metadata <q>airitibooks</q> yn <a %(a1)s><q>Crafiadau metadata eraill</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "O gasgliad <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Yn rhannol o'r ffynhonnell wreiddiol, yn rhannol o the-eye.eu, yn rhannol o ddrychau eraill."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "O wefan torrent llyfrau preifat, <a %(a_href)s>Bibliotik</a> (a elwir yn aml yn “Bib”), lle cafodd llyfrau eu pecynnu mewn torrents yn ôl enw (A.torrent, B.torrent) a'u dosbarthu trwy the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Gan ein gwirfoddolwr “bpb9v”. Am fwy o wybodaeth am <a %(a_href)s>CADAL</a>, gweler y nodiadau ar ein <a %(a_duxiu)s>tudalen set ddata DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mwy gan ein gwirfoddolwr “bpb9v”, yn bennaf ffeiliau DuXiu, yn ogystal â ffolder “WenQu” a “SuperStar_Journals” (SuperStar yw'r cwmni y tu ôl i DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Gan ein gwirfoddolwr “cgiym”, testunau Tsieineaidd o wahanol ffynonellau (a gynrychiolir fel is-gyfeiriaduron), gan gynnwys o <a %(a_href)s>China Machine Press</a> (cyhoeddwr mawr yn Tsieina)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Casgliadau nad ydynt yn Tsieineaidd (a gynrychiolir fel is-gyfeiriaduron) gan ein gwirfoddolwr “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Crafiad o lyfrau am bensaernïaeth Tsieineaidd, gan wirfoddolwr <q>cm</q>: <q>Fe'i cefais trwy fanteisio ar wendid rhwydwaith yn y tŷ cyhoeddi, ond mae'r bwlch hwnnw wedi'i gau ers hynny</q>. Yn cyfateb i metadata <q>chinese_architecture</q> yn <a %(a1)s><q>Crafiadau metadata eraill</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Llyfrau o'r tŷ cyhoeddi academaidd <a %(a_href)s>De Gruyter</a>, wedi'u casglu o ychydig o torrents mawr."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Sgrap o <a %(a_href)s>docer.pl</a>, gwefan rhannu ffeiliau Pwyleg sy'n canolbwyntio ar lyfrau a gweithiau ysgrifenedig eraill. Wedi'i sgrapio ddiwedd 2023 gan wirfoddolwr “p”. Nid oes gennym fetadata da o'r wefan wreiddiol (hyd yn oed dim estyniadau ffeiliau), ond fe wnaethom hidlo am ffeiliau tebyg i lyfrau ac roeddem yn aml yn gallu echdynnu metadata o'r ffeiliau eu hunain."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, yn uniongyrchol o DuXiu, wedi'u casglu gan wirfoddolwr “w”. Dim ond llyfrau DuXiu diweddar sydd ar gael yn uniongyrchol trwy ebooks, felly mae'n rhaid bod y rhan fwyaf o'r rhain yn ddiweddar."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Ffeiliau DuXiu sy'n weddill gan wirfoddolwr “m”, nad oeddent yn fformat PDG perchnogol DuXiu (y prif <a %(a_href)s>set ddata DuXiu</a>). Wedi'u casglu o lawer o ffynonellau gwreiddiol, yn anffodus heb gadw'r ffynonellau hynny yn y llwybr ffeil."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Crafiad o lyfrau erotig, gan wirfoddolwr <q>do no harm</q>. Yn cyfateb i metadata <q>hentai</q> yn <a %(a1)s><q>Crafiadau metadata eraill</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Casgliad wedi'i sgrapio o gyhoeddwr Manga Siapaneaidd gan wirfoddolwr “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archifau barnwrol dethol Longquan</a>, a ddarparwyd gan wirfoddolwr “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Sgrap o <a %(a_href)s>magzdb.org</a>, cynghreiriad o Library Genesis (mae'n gysylltiedig ar hafan libgen.rs) ond nad oeddent am ddarparu eu ffeiliau'n uniongyrchol. Wedi'i gael gan wirfoddolwr “p” ddiwedd 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Amrywiol uwchlwythiadau bach, yn rhy fach i fod yn is-gasgliad eu hunain, ond wedi'u cynrychioli fel cyfeiriaduron."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-lyfrau o AvaxHome, gwefan rhannu ffeiliau Rwsiaidd."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archif o bapurau newydd a chylchgronau. Yn cyfateb i metadata <q>newsarch_magz</q> yn <a %(a1)s><q>Crafiadau metadata eraill</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Crafiad o'r <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Casgliad gwirfoddolwr “o” a gasglodd lyfrau Pwyleg yn uniongyrchol o wefannau rhyddhau gwreiddiol (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Casgliadau cyfunol o <a %(a_href)s>shuge.org</a> gan wirfoddolwyr “cgiym” a “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Llyfrgell Ymerodrol Trantor”</a> (a enwir ar ôl y llyfrgell ffuglennol), wedi'i sgrapio yn 2022 gan wirfoddolwr “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Is-is-gasgliadau (a gynrychiolir fel cyfeiriaduron) gan wirfoddolwr “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (gan <a %(a_sikuquanshu)s>Dizhi(迪志)</a> yn Taiwan), mebook (mebook.cc, 我的小书屋, fy ystafell lyfrau fach — woz9ts: “Mae'r wefan hon yn canolbwyntio'n bennaf ar rannu ffeiliau ebook o ansawdd uchel, mae rhai ohonynt wedi'u gosod gan y perchennog ei hun. Cafodd y perchennog ei <a %(a_arrested)s>arestio</a> yn 2019 a gwnaeth rhywun gasgliad o'r ffeiliau a rannodd.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Ffeiliau DuXiu sy’n weddill gan wirfoddolwr “woz9ts”, nad oeddent yn fformat PDG perchnogol DuXiu (i’w trosi i PDF o hyd)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrenti gan Archif Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Crafu Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Mae gan Z-Library ei wreiddiau yn y gymuned <a %(a_href)s>Library Genesis</a>, ac yn wreiddiol fe’i cychwynnwyd gyda’u data. Ers hynny, mae wedi proffesiynoli’n sylweddol, ac mae ganddo ryngwyneb llawer mwy modern. Felly, maent yn gallu cael llawer mwy o roddion, yn ariannol i barhau i wella eu gwefan, yn ogystal â rhoddion o lyfrau newydd. Maent wedi casglu casgliad mawr yn ychwanegol at Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Diweddariad o Chwefror 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Yn ddiwedd 2022, arestiwyd sylfaenwyr honedig Z-Library, a chafodd parthau eu cipio gan awdurdodau’r Unol Daleithiau. Ers hynny mae’r wefan wedi bod yn araf yn dod ar-lein eto. Nid yw’n hysbys pwy sy’n ei rhedeg ar hyn o bryd."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Mae’r casgliad yn cynnwys tri rhan. Mae’r tudalennau disgrifiad gwreiddiol ar gyfer y ddwy ran gyntaf wedi’u cadw isod. Mae angen y tair rhan arnoch i gael yr holl ddata (ac eithrio torrenti sydd wedi’u disodli, sydd wedi’u croesi allan ar y dudalen torrenti)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: ein rhyddhad cyntaf. Dyma oedd y rhyddhad cyntaf o’r hyn a elwid bryd hynny yn “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: ail ryddhad, y tro hwn gyda’r holl ffeiliau wedi’u lapio mewn ffeiliau .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: rhyddhadau newydd cynyddol, gan ddefnyddio’r <a %(a_href)s>Fformat Cynwysyddion Archif Anna (AAC)</a>, bellach wedi’u rhyddhau mewn cydweithrediad â thîm Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrenti gan Archif Anna (metadata + cynnwys)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Cofnod enghreifftiol ar Archif Anna (casgliad gwreiddiol)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Cofnod enghreifftiol ar Archif Anna (casgliad “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Prif wefan"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Parth Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Cofnod blog am Ryddhad 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Cofnod blog am Ryddhad 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Rhyddhadau Zlib (tudalennau disgrifiad gwreiddiol)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Rhyddhad 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Cafodd y drych cychwynnol ei gael yn ofalus dros gyfnod 2021 a 2022. Ar hyn o bryd mae ychydig yn hen: mae’n adlewyrchu cyflwr y casgliad ym mis Mehefin 2021. Byddwn yn diweddaru hyn yn y dyfodol. Ar hyn o bryd rydym yn canolbwyntio ar gael y rhyddhad cyntaf hwn allan."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Gan fod Library Genesis eisoes wedi'i gadw gyda thorrents cyhoeddus, ac wedi'i gynnwys yn y Z-Library, gwnaethom ddeduplication sylfaenol yn erbyn Library Genesis ym mis Mehefin 2022. Ar gyfer hyn, defnyddiwyd hashiau MD5. Mae'n debygol bod llawer mwy o gynnwys dyblyg yn y llyfrgell, megis fformatau ffeil lluosog gyda'r un llyfr. Mae hyn yn anodd ei ganfod yn gywir, felly nid ydym yn gwneud hynny. Ar ôl y deduplication, rydym yn cael dros 2 filiwn o ffeiliau, yn gyfanswm o ychydig dan 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Mae'r casgliad yn cynnwys dau ran: dump MySQL “.sql.gz” o'r metadata, a'r 72 ffeil torrent o tua 50-100GB yr un. Mae'r metadata yn cynnwys y data fel y'i hadroddwyd gan wefan Z-Library (teitl, awdur, disgrifiad, math o ffeil), yn ogystal â'r maint ffeil gwirioneddol a'r md5sum a welsom, gan nad ydynt bob amser yn cytuno. Mae'n ymddangos bod ystodau o ffeiliau lle mae gan y Z-Library metadata anghywir ei hun. Efallai hefyd ein bod wedi lawrlwytho ffeiliau'n anghywir mewn rhai achosion unigol, y byddwn yn ceisio eu canfod a'u trwsio yn y dyfodol."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Mae'r ffeiliau torrent mawr yn cynnwys y data llyfrau gwirioneddol, gyda'r ID Z-Library fel enw'r ffeil. Gellir ailadeiladu'r estyniadau ffeil gan ddefnyddio'r dump metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Mae'r casgliad yn gymysgedd o gynnwys ffeithiol a ffuglen (heb ei wahanu fel yn Library Genesis). Mae'r ansawdd hefyd yn amrywio'n eang."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Mae'r rhyddhad cyntaf hwn bellach ar gael yn llawn. Nodwch fod y ffeiliau torrent ar gael yn unig trwy ein drych Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Rhyddhad 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Rydym wedi cael pob llyfr a ychwanegwyd at y Z-Library rhwng ein drych diwethaf ac Awst 2022. Rydym hefyd wedi mynd yn ôl a sgrapio rhai llyfrau a gollwyd gennym y tro cyntaf. Yn gyfan gwbl, mae'r casgliad newydd hwn tua 24TB. Unwaith eto, mae'r casgliad hwn wedi'i ddeduplicated yn erbyn Library Genesis, gan fod torrents eisoes ar gael ar gyfer y casgliad hwnnw."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Mae'r data wedi'i drefnu'n debyg i'r rhyddhad cyntaf. Mae yna dump MySQL “.sql.gz” o'r metadata, sy'n cynnwys yr holl metadata o'r rhyddhad cyntaf hefyd, gan ei ddisodli. Rydym hefyd wedi ychwanegu rhai colofnau newydd:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: a yw'r ffeil hon eisoes yn Library Genesis, naill ai yn y casgliad ffeithiol neu ffuglen (wedi'i gyfateb gan md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: pa torrent mae'r ffeil hon ynddo."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: wedi'i osod pan na allem lawrlwytho'r llyfr."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Soniasom am hyn y tro diwethaf, ond dim ond i egluro: “enw'r ffeil” a “md5” yw priodweddau gwirioneddol y ffeil, tra bod “enw'r ffeil a adroddwyd” a “md5 a adroddwyd” yn yr hyn a sgrapiwyd gennym o Z-Library. Weithiau nid yw'r ddau hyn yn cytuno â'i gilydd, felly rydym wedi cynnwys y ddau."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Ar gyfer y rhyddhad hwn, rydym wedi newid y collation i “utf8mb4_unicode_ci”, a ddylai fod yn gydnaws â fersiynau hŷn o MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Mae'r ffeiliau data yn debyg i'r tro diwethaf, er eu bod yn llawer mwy. Ni allem fod wedi trafferthu creu tunnell o ffeiliau torrent llai. Mae “pilimi-zlib2-0-14679999-extra.torrent” yn cynnwys yr holl ffeiliau a gollwyd gennym yn y rhyddhad diwethaf, tra bod y torrents eraill i gyd yn ystodau ID newydd. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Diweddariad %(date)s:</strong> Gwnaethom y rhan fwyaf o'n torrents yn rhy fawr, gan achosi i gleientiaid torrent gael trafferth. Rydym wedi eu tynnu a rhyddhau torrents newydd."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Diweddariad %(date)s:</strong> Roedd dal gormod o ffeiliau, felly rydym wedi eu lapio mewn ffeiliau tar a rhyddhau torrents newydd eto."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Rhyddhad 2 atodiad (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Mae hwn yn ffeil torrent ychwanegol sengl. Nid yw'n cynnwys unrhyw wybodaeth newydd, ond mae ganddo rai data ynddo a all gymryd peth amser i'w gyfrifo. Mae hynny'n ei gwneud yn gyfleus i'w gael, gan fod lawrlwytho'r torrent hwn yn aml yn gyflymach na'i gyfrifo o'r dechrau. Yn benodol, mae'n cynnwys mynegeion SQLite ar gyfer y ffeiliau tar, i'w defnyddio gyda <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Cwestiynau Cyffredin (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Beth yw Archif Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Archif Anna</span> yw prosiect dielw gyda dau nod:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Cadwraeth:</strong> Wrth gefnogi'r holl wybodaeth a diwylliant dynol.</li><li><strong>Mynediad:</strong> Gwneud y wybodaeth a'r diwylliant hwn ar gael i unrhyw un yn y byd.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Mae ein holl <a %(a_code)s>cod</a> a <a %(a_datasets)s>data</a> yn hollol agored."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Cadwraeth"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Rydym yn cadw llyfrau, papurau, comics, cylchgronau, a mwy, trwy ddod â'r deunyddiau hyn o wahanol <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">llyfrgelloedd cysgodol</a>, llyfrgelloedd swyddogol, a chasgliadau eraill at ei gilydd mewn un lle. Cedwir yr holl ddata hwn am byth trwy ei gwneud yn hawdd i'w ddyblygu mewn swmp — gan ddefnyddio torrenti — gan arwain at lawer o gopïau ledled y byd. Mae rhai llyfrgelloedd cysgodol eisoes yn gwneud hyn eu hunain (e.e. Sci-Hub, Library Genesis), tra bod Archif Anna yn “rhyddhau” llyfrgelloedd eraill nad ydynt yn cynnig dosbarthiad swmp (e.e. Z-Library) neu nad ydynt yn llyfrgelloedd cysgodol o gwbl (e.e. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Mae'r dosbarthiad eang hwn, ynghyd â chod ffynhonnell agored, yn gwneud ein gwefan yn wydn i gael ei dileu, ac yn sicrhau cadwraeth hirdymor gwybodaeth a diwylliant dynol. Dysgwch fwy am <a href=\"/datasets\">ein setiau data</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Rydym yn amcangyfrif ein bod wedi cadw tua <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% o lyfrau'r byd</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Mynediad"

#, fuzzy
msgid "page.home.access.text"
msgstr "Rydym yn gweithio gyda phartneriaid i wneud ein casgliadau'n hawdd ac yn rhydd i unrhyw un eu cyrchu. Rydym yn credu bod gan bawb hawl i ddoethineb ar y cyd dynoliaeth. Ac <a %(a_search)s>nid ar draul awduron</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Lawrlwythiadau bob awr yn y 30 diwrnod diwethaf. Cyfartaledd bob awr: %(hourly)s. Cyfartaledd dyddiol: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Rydym yn credu'n gryf yn llif rhydd gwybodaeth, a chadwraeth gwybodaeth a diwylliant. Gyda'r peiriant chwilio hwn, rydym yn adeiladu ar ysgwyddau cewri. Rydym yn parchu'n ddwfn waith caled y bobl sydd wedi creu'r llyfrgelloedd cysgodol amrywiol, ac rydym yn gobeithio y bydd y peiriant chwilio hwn yn ehangu eu cyrhaeddiad."

#, fuzzy
msgid "page.about.text3"
msgstr "I aros yn gyfredol ar ein cynnydd, dilynwch Anna ar <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> neu <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Ar gyfer cwestiynau ac adborth cysylltwch ag Anna yn %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Sut alla i helpu?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Dilynwch ni ar <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, neu <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Lledaenwch y gair am Archif Anna ar Twitter, Reddit, Tiktok, Instagram, yn eich caffi neu lyfrgell leol, neu ble bynnag yr ewch! Nid ydym yn credu mewn cadw porth — os cawn ein dileu byddwn yn ymddangos yn rhywle arall, gan fod ein holl god a data yn hollol agored.</li><li>3. Os ydych yn gallu, ystyriwch <a href=\"/donate\">rhoi</a>.</li><li>4. Helpwch <a href=\"https://translate.annas-software.org/\">gyfieithu</a> ein gwefan i wahanol ieithoedd.</li><li>5. Os ydych yn beiriannydd meddalwedd, ystyriwch gyfrannu at ein <a href=\"https://annas-software.org/\">ffynhonnell agored</a>, neu hadu ein <a href=\"/datasets\">torrenti</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Mae gennym bellach sianel Matrix wedi'i chysoni yn %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Os ydych yn ymchwilydd diogelwch, gallwn ddefnyddio eich sgiliau ar gyfer ymosod a diogelu. Edrychwch ar ein tudalen <a %(a_security)s>Diogelwch</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "Rydym yn chwilio am arbenigwyr mewn taliadau ar gyfer masnachwyr dienw. Allwch chi ein helpu i ychwanegu ffyrdd mwy cyfleus i roi? PayPal, WeChat, cardiau rhodd. Os ydych chi'n adnabod unrhyw un, cysylltwch â ni."

#, fuzzy
msgid "page.about.help.text8"
msgstr "Rydym bob amser yn chwilio am fwy o gapasiti gweinydd."

#, fuzzy
msgid "page.about.help.text9"
msgstr "Gallwch helpu trwy riportio problemau ffeiliau, gadael sylwadau, a chreu rhestrau ar y wefan hon. Gallwch hefyd helpu trwy <a %(a_upload)s>lwytho mwy o lyfrau i fyny</a>, neu drwsio problemau ffeiliau neu fformatio llyfrau presennol."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Creu neu helpu i gynnal y dudalen Wicipedia ar gyfer Archif Anna yn eich iaith."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Rydym yn chwilio am leoli hysbysebion bach, chwaethus. Os hoffech hysbysebu ar Archif Anna, rhowch wybod i ni."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Byddem wrth ein bodd pe bai pobl yn sefydlu <a %(a_mirrors)s>drychau</a>, a byddwn yn cefnogi hyn yn ariannol."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Am wybodaeth fwy helaeth ar sut i wirfoddoli, gweler ein tudalen <a %(a_volunteering)s>Gwirfoddoli a Bounties</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Pam mae'r lawrlwythiadau araf mor araf?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Nid oes gennym ddigon o adnoddau i roi lawrlwythiadau cyflym i bawb yn y byd, cymaint ag y byddem yn hoffi. Pe bai noddwr cyfoethog yn camu ymlaen i ddarparu hyn i ni, byddai hynny'n anhygoel, ond tan hynny, rydym yn gwneud ein gorau. Rydym yn brosiect dielw sy'n gallu cynnal ei hun prin trwy roddion."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dyma pam rydym wedi gweithredu dau system ar gyfer lawrlwythiadau am ddim, gyda'n partneriaid: gweinyddion a rennir gyda lawrlwythiadau araf, a gweinyddion ychydig yn gyflymach gyda rhestr aros (i leihau nifer y bobl sy'n lawrlwytho ar yr un pryd)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Mae gennym hefyd <a %(a_verification)s>gwirio porwr</a> ar gyfer ein lawrlwythiadau araf, oherwydd fel arall byddai bots a sgrapiwr yn eu camddefnyddio, gan wneud pethau hyd yn oed yn arafach i ddefnyddwyr dilys."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Nodwch, wrth ddefnyddio'r Porwr Tor, efallai y bydd angen i chi addasu eich gosodiadau diogelwch. Ar y lleiaf o'r opsiynau, o'r enw “Safonol”, mae'r her turnstile Cloudflare yn llwyddo. Ar yr opsiynau uwch, o'r enw “Mwy Diogel” a “Mwyaf Diogel”, mae'r her yn methu."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Weithiau gall lawrlwythiadau araf ar gyfer ffeiliau mawr dorri yn y canol. Rydym yn argymell defnyddio rheolwr lawrlwytho (fel JDownloader) i ailddechrau lawrlwythiadau mawr yn awtomatig."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Cwestiynau Cyffredin am Roddion"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>A yw aelodaeth yn adnewyddu'n awtomatig?</div> Nid yw aelodaeth yn <strong>adnewyddu'n awtomatig</strong>. Gallwch ymuno am gyhyd neu fyr ag y dymunwch."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>A allaf uwchraddio fy aelodaeth neu gael sawl aelodaeth?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>A oes gennych ddulliau talu eraill?</div> Ar hyn o bryd, nac oes. Nid yw llawer o bobl eisiau archifau fel hyn i fodoli, felly mae'n rhaid i ni fod yn ofalus. Os gallwch ein helpu i sefydlu dulliau talu eraill (mwy cyfleus) yn ddiogel, cysylltwch â ni yn %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Beth mae'r ystodau y mis yn ei olygu?</div> Gallwch gyrraedd ochr isaf ystod trwy gymhwyso'r holl ostyngiadau, megis dewis cyfnod hirach na mis."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Ar beth ydych chi'n gwario rhoddion?</div> Mae 100%% yn mynd i gadw a gwneud gwybodaeth a diwylliant y byd yn hygyrch. Ar hyn o bryd rydym yn gwario'r rhan fwyaf ohono ar weinyddion, storio, a lled band. Nid yw unrhyw arian yn mynd i unrhyw aelodau tîm yn bersonol."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>A allaf wneud rhodd fawr?</div> Byddai hynny'n wych! Ar gyfer rhoddion dros ychydig filoedd o ddoleri, cysylltwch â ni'n uniongyrchol yn %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>A allaf wneud rhodd heb ddod yn aelod?</div> Wrth gwrs. Rydym yn derbyn rhoddion o unrhyw swm ar y cyfeiriad Monero (XMR) hwn: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Sut ydw i'n llwytho llyfrau newydd i fyny?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Fel arall, gallwch eu llwytho i fyny i Z-Library <a %(a_upload)s>yma</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Ar gyfer llwythiadau bach (hyd at 10,000 o ffeiliau) os gwelwch yn dda llwythwch nhw i %(first)s a %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Ar hyn o bryd, rydym yn awgrymu llwytho llyfrau newydd i fyny i ffyrc Library Genesis. Dyma <a %(a_guide)s>canllaw defnyddiol</a>. Nodwch fod y ddau ffork rydym yn eu mynegeio ar y wefan hon yn tynnu o'r un system llwytho i fyny."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Ar gyfer Libgen.li, gwnewch yn siŵr eich bod yn mewngofnodi gyntaf ar <a %(a_forum)s>eu fforwm</a> gyda'r enw defnyddiwr %(username)s a'r cyfrinair %(password)s, ac yna dychwelwch i'w <a %(a_upload_page)s>tudalen llwytho i fyny</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Os nad yw eich cyfeiriad e-bost yn gweithio ar fforymau Libgen, rydym yn argymell defnyddio <a %(a_mail)s>Proton Mail</a> (am ddim). Gallwch hefyd <a %(a_manual)s>ofyn yn llaw</a> am i'ch cyfrif gael ei actifadu."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Nodwch fod mhut.org yn blocio rhai ystodau IP, felly efallai y bydd angen VPN."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Ar gyfer llwythiadau mawr (dros 10,000 ffeil) nad ydynt yn cael eu derbyn gan Libgen neu Z-Library, cysylltwch â ni yn %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "I lwytho papurau academaidd i fyny, rhowch wybod hefyd (yn ogystal â Library Genesis) i <a %(a_stc_nexus)s>STC Nexus</a>. Nhw yw'r llyfrgell gysgodol orau ar gyfer papurau newydd. Nid ydym wedi eu hintegreiddio eto, ond byddwn ar ryw adeg. Gallwch ddefnyddio eu <a %(a_telegram)s>bot llwytho i fyny ar Telegram</a>, neu gysylltu â'r cyfeiriad a restrir yn eu neges wedi'i phinio os oes gennych ormod o ffeiliau i'w llwytho i fyny fel hyn."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Sut ydw i'n gofyn am lyfrau?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Ar hyn o bryd, ni allwn ddarparu ceisiadau am lyfrau."

#, fuzzy
msgid "page.request.forums"
msgstr "Gwnewch eich ceisiadau ar fforymau Z-Library neu Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Peidiwch ag anfon e-bost atom gyda'ch ceisiadau llyfrau."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Ydych chi'n casglu metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Ydyn, yn wir."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Lawrlwythais 1984 gan George Orwell, a fydd yr heddlu'n dod at fy nrws?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Peidiwch â phoeni gormod, mae llawer o bobl yn lawrlwytho o wefannau sy'n gysylltiedig â ni, ac mae'n hynod o brin i gael trafferth. Fodd bynnag, i aros yn ddiogel rydym yn argymell defnyddio VPN (tâl), neu <a %(a_tor)s>Tor</a> (am ddim)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Sut ydw i'n arbed fy ngosodiadau chwilio?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Dewiswch y gosodiadau rydych chi'n eu hoffi, cadwch y blwch chwilio yn wag, cliciwch “Chwilio”, ac yna nodwch y dudalen gan ddefnyddio nodwedd nod tudalen eich porwr."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Oes gennych ap symudol?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nid oes gennym ap symudol swyddogol, ond gallwch osod y wefan hon fel ap."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Cliciwch y ddewislen tri dot yn y gornel dde uchaf, a dewiswch “Ychwanegu at y Sgrin Gartref”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Cliciwch y botwm “Rhannu” ar y gwaelod, a dewiswch “Ychwanegu at y Sgrin Gartref”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Oes gennych API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Mae gennym un API JSON sefydlog ar gyfer aelodau, ar gyfer cael URL lawrlwytho cyflym: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dogfennaeth o fewn JSON ei hun)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Ar gyfer achosion defnydd eraill, fel ailadrodd trwy ein holl ffeiliau, adeiladu chwiliad personol, ac ati, rydym yn argymell <a %(a_generate)s>creu</a> neu <a %(a_download)s>lawrlwytho</a> ein cronfeydd data ElasticSearch a MariaDB. Gellir archwilio'r data crai â llaw <a %(a_explore)s>trwy ffeiliau JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Gellir lawrlwytho ein rhestr torrent crai fel <a %(a_torrents)s>JSON</a> hefyd."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Cwestiynau Cyffredin Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Hoffwn helpu i hadu, ond nid oes gennyf lawer o le ar y ddisg."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Defnyddiwch y <a %(a_list)s>cynhyrchydd rhestr torrent</a> i gynhyrchu rhestr o dorrents sydd fwyaf angen eu hadu, o fewn eich terfynau storio."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Mae'r torrents yn rhy araf; a allaf lawrlwytho'r data'n uniongyrchol gennych chi?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ie, gweler y dudalen <a %(a_llm)s>data LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "A allaf lawrlwytho dim ond is-set o'r ffeiliau, fel dim ond iaith neu bwnc penodol?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Ateb byr: nid yn hawdd."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Ateb hir:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Mae'r rhan fwyaf o dorrents yn cynnwys y ffeiliau'n uniongyrchol, sy'n golygu y gallwch chi gyfarwyddo cleientiaid torrent i lawrlwytho dim ond y ffeiliau sydd eu hangen. I benderfynu pa ffeiliau i'w lawrlwytho, gallwch <a %(a_generate)s>gynhyrchu</a> ein metadata, neu <a %(a_download)s>lawrlwytho</a> ein cronfeydd data ElasticSearch a MariaDB. Yn anffodus, mae nifer o gasgliadau torrent yn cynnwys ffeiliau .zip neu .tar wrth y gwraidd, ac yn yr achos hwnnw mae angen i chi lawrlwytho'r torrent cyfan cyn gallu dewis ffeiliau unigol."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Mae gennym <a %(a_ideas)s>rhai syniadau</a> ar gyfer yr achos olaf serch hynny.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nid oes offer hawdd i'w defnyddio ar gyfer hidlo lawrlwythiadau torrenti ar gael eto, ond rydym yn croesawu cyfraniadau."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Sut ydych chi'n trin dyblygu yn y torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Rydym yn ceisio cadw dyblygu neu orgyffwrdd i'r lleiafswm rhwng y torrents yn y rhestr hon, ond ni ellir cyflawni hyn bob amser, ac mae'n dibynnu'n helaeth ar bolisïau'r llyfrgelloedd ffynhonnell. Ar gyfer llyfrgelloedd sy'n rhoi eu torrents eu hunain allan, mae allan o'n dwylo. Ar gyfer torrents a ryddhawyd gan Archif Anna, rydym yn ddeduplicate yn seiliedig ar hash MD5 yn unig, sy'n golygu nad yw gwahanol fersiynau o'r un llyfr yn cael eu ddeduplicate."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "A allaf gael y rhestr torrent fel JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ie."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Dydw i ddim yn gweld PDFau na EPUBau yn y torfeydd, dim ond ffeiliau binary? Beth ddylwn i ei wneud?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Mewn gwirionedd, PDFau ac EPUBau yw'r rhain, ond nid oes ganddynt estyniad mewn llawer o'n torfeydd. Mae dau le lle gallwch ddod o hyd i'r metadata ar gyfer ffeiliau torrent, gan gynnwys y mathau o ffeiliau/estyniadau:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Mae gan bob casgliad neu ryddhad ei metadata ei hun. Er enghraifft, mae gan dorrau <a %(a_libgen_nonfic)s>Libgen.rs</a> gronfa ddata metadata cyfatebol sy'n cael ei chynnal ar wefan Libgen.rs. Rydym fel arfer yn cysylltu â'r adnoddau metadata perthnasol o dudalen <a %(a_datasets)s>dataset</a> pob casgliad."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Rydym yn argymell <a %(a_generate)s>creu</a> neu <a %(a_download)s>lawrlwytho</a> ein cronfeydd data ElasticSearch a MariaDB. Mae'r rhain yn cynnwys mapio ar gyfer pob cofnod yn Archif Anna i'w ffeiliau torrent cyfatebol (os ydynt ar gael), o dan \"torrent_paths\" yn JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Pam na all fy nghleient torrenti agor rhai o'ch ffeiliau torrenti / dolenni magnet?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Nid yw rhai cleientiaid torrenti yn cefnogi meintiau darn mawr, sydd gan lawer o'n torrenti (ar gyfer rhai mwy newydd nid ydym yn gwneud hyn mwyach — er ei fod yn ddilys yn ôl y manylebau!). Felly ceisiwch gleient gwahanol os byddwch yn rhedeg i mewn i hyn, neu cwynwch wrth wneuthurwyr eich cleient torrenti."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Oes gennych raglen datgelu cyfrifol?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Rydym yn croesawu ymchwilwyr diogelwch i chwilio am fregusrwydd yn ein systemau. Rydym yn gefnogwyr mawr o ddatgelu cyfrifol. Cysylltwch â ni <a %(a_contact)s>yma</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Ar hyn o bryd, nid ydym yn gallu dyfarnu gwobrau chwilod, heblaw am fregusrwydd sydd â'r <a %(a_link)s >potensial i beryglu ein hanhysbysrwydd</a>, lle rydym yn cynnig gwobrau yn yr ystod $10k-50k. Hoffem gynnig cwmpas ehangach ar gyfer gwobrau chwilod yn y dyfodol! Sylwer bod ymosodiadau peirianneg gymdeithasol y tu allan i gwmpas."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Os oes gennych ddiddordeb mewn diogelwch ymosodol, ac eisiau helpu i archifo gwybodaeth a diwylliant y byd, gwnewch yn siŵr eich bod yn cysylltu â ni. Mae llawer o ffyrdd y gallwch helpu."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "A oes mwy o adnoddau am Archif Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Is-reddit</a> — diweddariadau rheolaidd"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Meddalwedd Anna</a> — ein cod ffynhonnell agored"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Cyfieithu ar Feddalwedd Anna</a> — ein system gyfieithu"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — am y data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — parthau amgen"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wicipedia</a> — mwy amdanom ni (os gwelwch yn dda helpwch i gadw'r dudalen hon wedi'i diweddaru, neu crëwch un ar gyfer eich iaith eich hun!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Sut ydw i'n riportio tor-cyfraith hawlfraint?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nid ydym yn cynnal unrhyw ddeunyddiau hawlfraint yma. Rydym yn beiriant chwilio, ac felly dim ond mynegeio metadata sydd eisoes ar gael yn gyhoeddus yr ydym yn ei wneud. Wrth lawrlwytho o'r ffynonellau allanol hyn, byddem yn awgrymu eich bod yn gwirio'r cyfreithiau yn eich awdurdodaeth ynghylch yr hyn a ganiateir. Nid ydym yn gyfrifol am gynnwys a gynhelir gan eraill."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Os oes gennych gwynion am yr hyn a welwch yma, eich bet gorau yw cysylltu â'r wefan wreiddiol. Rydym yn tynnu eu newidiadau i mewn i'n cronfa ddata yn rheolaidd. Os ydych wir yn meddwl bod gennych gŵyn DMCA ddilys y dylem ymateb iddi, llenwch y <a %(a_copyright)s>ffurflen hawliad DMCA / Hawlfraint</a>. Rydym yn cymryd eich cwynion o ddifrif, a byddwn yn cysylltu â chi cyn gynted â phosibl."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Rwy'n casáu sut rydych chi'n rhedeg y prosiect hwn!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Hoffem hefyd atgoffa pawb bod ein holl god a data yn hollol agored. Mae hyn yn unigryw ar gyfer prosiectau fel ein un ni — nid ydym yn ymwybodol o unrhyw brosiect arall gyda chatalog mor enfawr sydd hefyd yn hollol agored. Rydym yn croesawu unrhyw un sy'n meddwl ein bod yn rhedeg ein prosiect yn wael i gymryd ein cod a'n data a sefydlu eu llyfrgell gysgodol eu hunain! Nid ydym yn dweud hyn allan o ddig neu rywbeth — rydym yn wirioneddol yn meddwl y byddai hyn yn wych gan y byddai'n codi'r safon i bawb, ac yn well cadw treftadaeth dynoliaeth."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Oes gennych chi fonitor amser gweithredu?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Gweler <a %(a_href)s>y prosiect rhagorol hwn</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Sut mae rhoi llyfrau neu ddeunyddiau corfforol eraill?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Anfonwch nhw at yr <a %(a_archive)s>Internet Archive</a>. Byddant yn eu cadw'n iawn."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Pwy yw Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Chi yw Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Beth yw eich hoff lyfrau?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Dyma rai llyfrau sydd â phwysigrwydd arbennig i fyd llyfrgelloedd cysgodol a chadwraeth ddigidol:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Rydych wedi rhedeg allan o lawrlwythiadau cyflym heddiw."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Dewch yn aelod i ddefnyddio lawrlwythiadau cyflym."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Rydym nawr yn cefnogi cardiau anrheg Amazon, cardiau credyd a debyd, crypto, Alipay, a WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Cronfa ddata lawn"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Llyfrau, papurau, cylchgronau, comics, cofnodion llyfrgell, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Chwilio"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Mae Sci-Hub wedi <a %(a_paused)s>oedi</a> uwchlwytho papurau newydd."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;Mae SciDB yn barhad o Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Mynediad uniongyrchol i %(count)s bapurau academaidd"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Agor"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Os ydych chi'n <a %(a_member)s>aelod</a>, nid oes angen gwirio porwr."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archif hirdymor"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Mae'r datasets a ddefnyddir yn Archif Anna yn gwbl agored, ac gellir eu drych yn gyfan gwbl gan ddefnyddio torfeydd. <a %(a_datasets)s>Dysgwch fwy…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Gallwch helpu'n aruthrol drwy hau torrents. <a %(a_torrents)s>Dysgwch fwy…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s hauwyr"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s hauwyr"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s hauwyr"

#, fuzzy
msgid "page.home.llm.header"
msgstr "data hyfforddi LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Mae gennym y casgliad mwyaf yn y byd o ddata testun o ansawdd uchel. <a %(a_llm)s>Dysgwch fwy…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Drychau: galw am wirfoddolwyr"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Chwilio am wirfoddolwyr"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Fel prosiect di-elw, ffynhonnell agored, rydym bob amser yn chwilio am bobl i helpu."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Os ydych yn rhedeg prosesydd taliadau dienw risg uchel, cysylltwch â ni. Rydym hefyd yn chwilio am bobl sy'n edrych i osod hysbysebion bach chwaethus. Mae'r holl elw yn mynd at ein hymdrechion cadwraeth."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Lawrlwythiadau IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Pob dolen lawrlwytho ar gyfer y ffeil hon: <a %(a_main)s>Prif dudalen y ffeil</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "Porth IPFS #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(efallai y bydd angen i chi geisio sawl gwaith gyda IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 I gael lawrlwythiadau cyflymach a hepgor y gwiriadau porwr, <a %(a_membership)s>dewch yn aelod</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Ar gyfer drych swmpus o'n casgliad, edrychwch ar y tudalennau <a %(a_datasets)s>Datasets</a> a <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Data LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Mae'n ddealladwy bod LLMs yn ffynnu ar ddata o ansawdd uchel. Mae gennym y casgliad mwyaf o lyfrau, papurau, cylchgronau, ac ati yn y byd, sy'n rhai o'r ffynonellau testun o'r ansawdd uchaf."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Graddfa ac ystod unigryw"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Mae ein casgliad yn cynnwys dros gant miliwn o ffeiliau, gan gynnwys cyfnodolion academaidd, llyfrau testun, a chylchgronau. Rydym yn cyflawni'r raddfa hon trwy gyfuno cronfeydd data mawr presennol."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Mae rhai o'n casgliadau ffynhonnell eisoes ar gael yn swmp (Sci-Hub, a rhannau o Libgen). Rhyddhaodd ffynonellau eraill gennym ni ein hunain. <a %(a_datasets)s>Datasets</a> yn dangos trosolwg llawn."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Mae ein casgliad yn cynnwys miliynau o lyfrau, papurau, a chylchgronau o'r cyfnod cyn e-lyfrau. Mae rhannau mawr o'r casgliad hwn eisoes wedi'u OCRio, ac mae ganddynt ychydig iawn o orgyffwrdd mewnol."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Sut y gallwn helpu"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Rydym yn gallu darparu mynediad cyflym iawn i'n casgliadau llawn, yn ogystal â chasgliadau heb eu rhyddhau."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Mae hwn yn fynediad lefel fenter y gallwn ei ddarparu am roddion yn yr ystod o ddegau o filoedd USD. Rydym hefyd yn barod i fasnachu hyn am gasgliadau o ansawdd uchel nad oes gennym eto."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Gallwn ad-dalu chi os ydych yn gallu darparu cyfoethogi ein data, fel:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Tynnu orgyffwrdd (dedupliad)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Echdynnu testun a metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Cefnogi archifo hirdymor gwybodaeth ddynol, tra'n cael data gwell ar gyfer eich model!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Cysylltwch â ni</a> i drafod sut y gallwn weithio gyda'n gilydd."

#, fuzzy
msgid "page.login.continue"
msgstr "Parhau"

#, fuzzy
msgid "page.login.please"
msgstr "Os gwelwch yn dda <a %(a_account)s>mewngofnodwch</a> i weld y dudalen hon.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Mae Archif Anna ar gau dros dro ar gyfer cynnal a chadw. Dewch yn ôl mewn awr."

#, fuzzy
msgid "page.metadata.header"
msgstr "Gwella metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Gallwch helpu i gadw llyfrau trwy wella metadata! Yn gyntaf, darllenwch y cefndir am metadata ar Archif Anna, ac yna dysgwch sut i wella metadata trwy gysylltu ag Open Library, a chael aelodaeth am ddim ar Archif Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Cefndir"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Pan edrychwch ar lyfr ar Archif Anna, gallwch weld meysydd amrywiol: teitl, awdur, cyhoeddwr, argraffiad, blwyddyn, disgrifiad, enw ffeil, a mwy. Gelwir yr holl ddarnau o wybodaeth hynny yn <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Gan ein bod yn cyfuno llyfrau o wahanol <em>lyfrgelloedd ffynhonnell</em>, rydym yn dangos pa bynnag metadata sydd ar gael yn y llyfrgell ffynhonnell honno. Er enghraifft, ar gyfer llyfr a gawsom o Library Genesis, byddwn yn dangos y teitl o gronfa ddata Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Weithiau mae llyfr yn bresennol mewn <em>llawer o</em> lyfrgelloedd ffynhonnell, a allai fod â meysydd metadata gwahanol. Yn yr achos hwnnw, rydym yn syml yn dangos y fersiwn hiraf o bob maes, gan fod y fersiwn honno'n debygol o gynnwys y wybodaeth fwyaf defnyddiol! Byddwn yn dal i ddangos y meysydd eraill islaw'r disgrifiad, e.e. fel ”teitl amgen” (ond dim ond os ydynt yn wahanol)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Rydym hefyd yn echdynnu <em>codau</em> fel adnabodwyr a dosbarthwyr o'r llyfrgell ffynhonnell. <em>Adnabodwyr</em> yn cynrychioli argraffiad penodol o lyfr yn unigryw; enghreifftiau yw ISBN, DOI, Open Library ID, Google Books ID, neu Amazon ID. <em>Dosbarthwyr</em> yn grwpio llyfrau tebyg lluosog; enghreifftiau yw Dewey Decimal (DCC), UDC, LCC, RVK, neu GOST. Weithiau mae'r codau hyn wedi'u cysylltu'n benodol mewn llyfrgelloedd ffynhonnell, ac weithiau gallwn eu hechdynnu o'r enw ffeil neu'r disgrifiad (yn bennaf ISBN a DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Gallwn ddefnyddio adnabodwyr i ddod o hyd i gofnodion mewn <em>casgliadau metadata yn unig</em>, fel OpenLibrary, ISBNdb, neu WorldCat/OCLC. Mae tab <em>metadata penodol</em> yn ein peiriant chwilio os hoffech bori'r casgliadau hynny. Rydym yn defnyddio cofnodion cyfatebol i lenwi meysydd metadata coll (e.e. os yw teitl ar goll), neu e.e. fel “teitl amgen” (os oes teitl presennol)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "I weld yn union o ble y daeth metadata llyfr, gweler y tab <em>“Manylion technegol”</em> ar dudalen llyfr. Mae ganddo ddolen i'r JSON crai ar gyfer y llyfr hwnnw, gyda phwyntiau i'r JSON crai o'r cofnodion gwreiddiol."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Am fwy o wybodaeth, gweler y tudalennau canlynol: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Chwilio (tab metadata)</a>, <a %(a_codes)s>Archwiliwr Codau</a>, ac <a %(a_example)s>Enghraifft metadata JSON</a>. Yn olaf, gellir <a %(a_generated)s>creu</a> neu <a %(a_downloaded)s>lawrlwytho</a> ein metadata i gyd fel cronfeydd data ElasticSearch a MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Cysylltu ag Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Felly os byddwch yn dod ar draws ffeil gyda metadata gwael, sut ddylech chi ei drwsio? Gallwch fynd i'r llyfrgell ffynhonnell a dilyn ei gweithdrefnau ar gyfer trwsio metadata, ond beth i'w wneud os yw ffeil yn bresennol mewn sawl llyfrgell ffynhonnell?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Mae un adnabodwr sy'n cael ei drin yn arbennig ar Archif Anna. <strong>Mae'r maes annas_archive md5 ar Open Library bob amser yn diystyru'r holl metadata arall!</strong> Gadewch i ni fynd yn ôl ychydig yn gyntaf a dysgu am Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Sefydlwyd Open Library yn 2006 gan Aaron Swartz gyda'r nod o “un dudalen we ar gyfer pob llyfr a gyhoeddwyd erioed”. Mae'n fath o Wicipedia ar gyfer metadata llyfrau: gall pawb ei olygu, mae'n rhydd i'w drwyddedu, a gellir ei lawrlwytho'n swmpus. Mae'n gronfa ddata llyfrau sy'n fwyaf cyd-fynd â'n cenhadaeth — mewn gwirionedd, mae Archif Anna wedi'i hysbrydoli gan weledigaeth a bywyd Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Yn lle ailddyfeisio'r olwyn, penderfynasom ailgyfeirio ein gwirfoddolwyr tuag at Open Library. Os gwelwch lyfr sydd â metadata anghywir, gallwch helpu yn y ffordd ganlynol:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Ewch i'r <a %(a_openlib)s>gwefan Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Dewch o hyd i'r cofnod llyfr cywir. <strong>RHybudd:</strong> sicrhewch eich bod yn dewis y <strong>argraffiad</strong> cywir. Yn Open Library, mae gennych “gweithiau” ac “argraffiadau”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Gallai “waith” fod yn “Harry Potter and the Philosopher's Stone”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Gallai “argraffiad” fod yn:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Cyhoeddwyd y rhifyn cyntaf yn 1997 gan Bloomsbery gyda 256 tudalen."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Cyhoeddwyd y rhifyn clawr meddal yn 2003 gan Raincoast Books gyda 223 tudalen."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Cyfieithiad Pwyleg 2000 “Harry Potter I Kamie Filozoficzn” gan Media Rodzina gyda 328 tudalen."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Mae gan bob un o'r rhifynnau hyn ISBNau gwahanol a chynnwys gwahanol, felly gwnewch yn siŵr eich bod yn dewis yr un cywir!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Golygu'r cofnod (neu ei greu os nad oes un yn bodoli), ac ychwanegu cymaint o wybodaeth ddefnyddiol ag y gallwch! Rydych chi yma nawr beth bynnag, felly efallai y byddwch hefyd yn gwneud y cofnod yn wirioneddol anhygoel."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "O dan “Rhifau ID” dewiswch “Archif Anna” ac ychwanegwch MD5 y llyfr o Archif Anna. Dyma'r llinyn hir o lythrennau a rhifau ar ôl “/md5/” yn y URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Ceisiwch ddod o hyd i ffeiliau eraill yn Archif Anna sy'n cyfateb i'r cofnod hwn hefyd, ac ychwanegwch y rheini hefyd. Yn y dyfodol gallwn grwpio'r rheini fel dyblygiadau ar dudalen chwilio Archif Anna."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Pan fyddwch wedi gorffen, ysgrifennwch y URL yr ydych newydd ei ddiweddaru. Unwaith y byddwch wedi diweddaru o leiaf 30 o gofnodion gyda MD5s Archif Anna, anfonwch <a %(a_contact)s>ebost</a> atom a danfonwch y rhestr. Byddwn yn rhoi aelodaeth am ddim i chi ar gyfer Archif Anna, fel y gallwch wneud y gwaith hwn yn haws (ac fel diolch am eich help). Rhaid i'r rhain fod yn olygiadau o ansawdd uchel sy'n ychwanegu symiau sylweddol o wybodaeth, fel arall bydd eich cais yn cael ei wrthod. Bydd eich cais hefyd yn cael ei wrthod os bydd unrhyw un o'r golygiadau'n cael eu gwrthdroi neu eu cywiro gan gymedrolwyr Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Nodwch fod hyn yn gweithio ar gyfer llyfrau yn unig, nid papurau academaidd neu fathau eraill o ffeiliau. Ar gyfer mathau eraill o ffeiliau rydym yn dal i argymell dod o hyd i'r llyfrgell ffynhonnell. Gall gymryd ychydig wythnosau i newidiadau gael eu cynnwys yn Archif Anna, gan fod angen i ni lawrlwytho'r dump data Open Library diweddaraf, ac ail-gynhyrchu ein mynegai chwilio."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Drychau: galw am wirfoddolwyr"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "I gynyddu gwydnwch Archif Anna, rydym yn chwilio am wirfoddolwyr i redeg drychau."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Rydym yn chwilio am hyn:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Rydych yn rhedeg cod ffynhonnell agored Archif Anna, ac yn diweddaru'r cod a'r data yn rheolaidd."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Mae eich fersiwn yn cael ei wahaniaethu'n glir fel drych, e.e. “Archif Bob, drych Archif Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Rydych yn barod i gymryd y risgiau sy'n gysylltiedig â'r gwaith hwn, sy'n sylweddol. Mae gennych ddealltwriaeth ddofn o'r diogelwch gweithredol sydd ei angen. Mae cynnwys <a %(a_shadow)s>y</a> <a %(a_pirate)s>postiadau</a> hyn yn amlwg i chi."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Rydych yn barod i gyfrannu at ein <a %(a_codebase)s>cod ffynhonnell</a> — mewn cydweithrediad â'n tîm — er mwyn gwneud hyn yn bosibl."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Yn y dechrau ni fyddwn yn rhoi mynediad i chi i lawrlwythiadau ein gweinydd partner, ond os aiff pethau'n dda, gallwn rannu hynny gyda chi."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Costau cynnal"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Rydym yn barod i dalu costau cynnal a VPN, yn wreiddiol hyd at $200 y mis. Mae hyn yn ddigonol ar gyfer gweinydd chwilio sylfaenol a phroxy wedi'i ddiogelu gan DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Byddwn ond yn talu am gynnal unwaith y byddwch wedi sefydlu popeth, ac wedi dangos eich bod yn gallu cadw'r archif yn gyfredol gyda diweddariadau. Mae hyn yn golygu y bydd yn rhaid i chi dalu am y 1-2 mis cyntaf o'ch poced."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Ni fydd eich amser yn cael ei iawndal (ac nid yw ein hamser ni chwaith), gan mai gwaith gwirfoddol pur yw hwn."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Os byddwch yn cymryd rhan sylweddol yn natblygiad a gweithrediadau ein gwaith, gallwn drafod rhannu mwy o'r refeniw rhoddion gyda chi, i chi ei ddefnyddio fel y bo'n angenrheidiol."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Dechrau arni"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Os gwelwch yn dda <strong>peidiwch â chysylltu â ni</strong> i ofyn am ganiatâd, neu am gwestiynau sylfaenol. Mae gweithredoedd yn siarad yn uwch na geiriau! Mae'r holl wybodaeth allan yna, felly ewch ymlaen gyda sefydlu eich drych."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Mae croeso i chi bostio tocynnau neu geisiadau uno i'n Gitlab pan fyddwch yn rhedeg i mewn i broblemau. Efallai y bydd angen i ni adeiladu rhai nodweddion penodol i'r drych gyda chi, fel ail-frandio o “Archif Anna” i enw eich gwefan, (yn wreiddiol) analluogi cyfrifon defnyddwyr, neu gysylltu yn ôl i'n prif safle o dudalennau llyfrau."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Unwaith y bydd eich drych yn rhedeg, cysylltwch â ni os gwelwch yn dda. Byddem wrth ein bodd yn adolygu eich OpSec, ac unwaith y bydd hynny'n gadarn, byddwn yn cysylltu â'ch drych, ac yn dechrau gweithio'n agosach gyda chi."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Diolch ymlaen llaw i unrhyw un sy'n barod i gyfrannu yn y ffordd hon! Nid yw'n addas i'r gwangalon, ond byddai'n cryfhau hirhoedledd y llyfrgell fwyaf gwirioneddol agored yn hanes dynoliaeth."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Lawrlwytho o wefan partner"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Dim ond trwy'r wefan swyddogol y mae lawrlwythiadau araf ar gael. Ewch i %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Nid yw lawrlwythiadau araf ar gael trwy VPNs Cloudflare neu fel arall o gyfeiriadau IP Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Arhoswch <span %(span_countdown)s>%(wait_seconds)s</span> eiliad i lawrlwytho'r ffeil hon."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Defnyddiwch yr URL canlynol i lawrlwytho: <a %(a_download)s>Lawrlwytho nawr</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Diolch am aros, mae hyn yn cadw'r wefan yn hygyrch am ddim i bawb! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Rhybudd: bu llawer o lawrlwythiadau o'ch cyfeiriad IP yn y 24 awr diwethaf. Efallai y bydd lawrlwythiadau'n arafach nag arfer."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Lawrlwythiadau o'ch cyfeiriad IP yn y 24 awr diwethaf: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Os ydych yn defnyddio VPN, cysylltiad rhyngrwyd a rennir, neu os yw eich ISP yn rhannu IPs, efallai mai dyna'r rheswm am y rhybudd hwn."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Er mwyn rhoi cyfle i bawb lawrlwytho ffeiliau am ddim, mae angen i chi aros cyn y gallwch lawrlwytho'r ffeil hon."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Mae croeso i chi barhau i bori Archif Anna mewn tab gwahanol tra'n aros (os yw eich porwr yn cefnogi adnewyddu tabiau cefndir)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Mae croeso i chi aros i dudalennau lawrlwytho lluosog lwytho ar yr un pryd (ond lawrlwythwch un ffeil ar y tro fesul gweinydd yn unig)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Unwaith y byddwch yn cael dolen lawrlwytho mae'n ddilys am sawl awr."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Archif Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Cofnod yn Archif Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Lawrlwytho"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "I gefnogi hygyrchedd a chadwraeth hirdymor gwybodaeth ddynol, dewch yn <a %(a_donate)s>aelod</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Fel bonws, 🧬&nbsp;mae SciDB yn llwytho'n gyflymach i aelodau, heb unrhyw gyfyngiadau."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Ddim yn gweithio? Ceisiwch <a %(a_refresh)s>adnewyddu</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Dim rhagolwg ar gael eto. Lawrlwythwch y ffeil o <a %(a_path)s>Archif Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;Mae SciDB yn barhad o Sci-Hub, gyda'i ryngwyneb cyfarwydd a gweld PDFs yn uniongyrchol. Rhowch eich DOI i weld."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Mae gennym gasgliad llawn Sci-Hub, yn ogystal â phapurau newydd. Gellir gweld y rhan fwyaf yn uniongyrchol gyda rhyngwyneb cyfarwydd, yn debyg i Sci-Hub. Gellir lawrlwytho rhai trwy ffynonellau allanol, ac yn yr achos hwnnw rydym yn dangos dolenni at y rheini."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Chwilio"

#, fuzzy
msgid "page.search.title.new"
msgstr "Chwiliad newydd"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Cynnwys yn unig"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Eithrio"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Heb ei wirio"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Lawrlwytho"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Erthyglau cyfnodolion"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Benthyca Digidol"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Teitl, awdur, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Chwilio"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Gosodiadau chwilio"

#, fuzzy
msgid "page.search.submit"
msgstr "Chwilio"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Cymerodd y chwiliad yn rhy hir, sy'n gyffredin ar gyfer ymholiadau eang. Efallai na fydd y cyfrifon hidlo yn gywir."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Cymerodd y chwiliad yn rhy hir, sy'n golygu efallai y gwelwch ganlyniadau anghywir. Weithiau mae <a %(a_reload)s>ail-lwytho</a> y dudalen yn helpu."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Dangos"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Rhestr"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabl"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Uwch"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Chwilio disgrifiadau a sylwadau metadata"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Ychwanegu maes chwilio penodol"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(chwilio maes penodol)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Blwyddyn cyhoeddi"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Cynnwys"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Math o ffeil"

#, fuzzy
msgid "page.search.more"
msgstr "mwy…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Mynediad"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Ffynhonnell"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "wedi'i sgrapio a'i agor-ffynhonnell gan AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Iaith"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Trefnu yn ôl"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Mwyaf perthnasol"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Diweddaraf"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(blwyddyn cyhoeddi)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Hynaf"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Mwyaf"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(maint ffeil)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Lleiaf"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(ffynhonnell agored)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Ar hap"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Mae'r mynegai chwilio yn cael ei ddiweddaru'n fisol. Ar hyn o bryd mae'n cynnwys cofnodion hyd at %(last_data_refresh_date)s. Am fwy o wybodaeth dechnegol, gweler y <a %(link_open_tag)s>tudalen datasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "I archwilio'r mynegai chwilio yn ôl codau, defnyddiwch y <a %(a_href)s>Archwiliwr Codau</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Teipiwch yn y blwch i chwilio ein catalog o %(count)s ffeiliau y gellir eu lawrlwytho'n uniongyrchol, yr ydym yn <a %(a_preserve)s>eu cadw am byth</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Mewn gwirionedd, gall unrhyw un helpu i gadw'r ffeiliau hyn trwy hadu ein <a %(a_torrents)s>rhestr unedig o dorrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Ar hyn o bryd mae gennym y catalog agored mwyaf cynhwysfawr o lyfrau, papurau, a gweithiau ysgrifenedig eraill yn y byd. Rydym yn adlewyrchu Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>a mwy</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Os byddwch yn dod o hyd i “lyfrgelloedd cysgodol” eraill y dylem eu hadlewyrchu, neu os oes gennych unrhyw gwestiynau, cysylltwch â ni yn %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Ar gyfer hawliadau DMCA / hawlfraint <a %(a_copyright)s>cliciwch yma</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Awgrym: defnyddiwch fysellau llwybr byr “/” (ffocws chwilio), “enter” (chwilio), “j” (i fyny), “k” (i lawr), “<” (tudalen flaenorol), “>” (tudalen nesaf) ar gyfer llywio cyflymach."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Chwilio am bapurau?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Teipiwch yn y blwch i chwilio ein catalog o %(count)s papurau academaidd ac erthyglau cyfnodolion, yr ydym yn <a %(a_preserve)s>eu cadw am byth</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Teipiwch yn y blwch i chwilio am ffeiliau mewn llyfrgelloedd benthyca digidol."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Mae'r mynegai chwilio hwn ar hyn o bryd yn cynnwys metadata o lyfrgell Benthyca Digidol Rheoledig yr Internet Archive. <a %(a_datasets)s>Mwy am ein datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Am fwy o lyfrgelloedd benthyca digidol, gweler <a %(a_wikipedia)s>Wicipedia</a> a'r <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Teipiwch yn y blwch i chwilio am metadata o lyfrgelloedd. Gall hyn fod yn ddefnyddiol wrth <a %(a_request)s>ofyn am ffeil</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Mae'r mynegai chwilio hwn ar hyn o bryd yn cynnwys metadata o wahanol ffynonellau metadata. <a %(a_datasets)s>Mwy am ein datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Ar gyfer metadata, rydym yn dangos y cofnodion gwreiddiol. Nid ydym yn uno cofnodion."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Mae llawer iawn o ffynonellau metadata ar gyfer gweithiau ysgrifenedig ledled y byd. <a %(a_wikipedia)s>Mae'r dudalen Wicipedia hon</a> yn fan cychwyn da, ond os ydych chi'n gwybod am restrau da eraill, rhowch wybod i ni."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Teipiwch yn y blwch i chwilio."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Mae'r rhain yn gofnodion metadata, <span %(classname)s>nid</span> ffeiliau y gellir eu lawrlwytho."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Gwall yn ystod chwilio."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Ceisiwch <a %(a_reload)s>ail-lwytho'r dudalen</a>. Os yw'r broblem yn parhau, anfonwch e-bost atom yn %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Dim ffeiliau wedi'u darganfod.</span> Ceisiwch dermau chwilio a hidlwyr llai neu wahanol."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Weithiau mae hyn yn digwydd yn anghywir pan fydd y gweinydd chwilio yn araf. Mewn achosion o'r fath, gall <a %(a_attrs)s>ail-lwytho</a> helpu."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Rydym wedi dod o hyd i gyfatebiadau yn: %(in)s. Gallwch gyfeirio at y URL a geir yno wrth <a %(a_request)s>ofyn am ffeil</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Erthyglau Cyfnodolion (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Benthyca Digidol (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Canlyniadau %(from)s-%(to)s (%(total)s i gyd)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ cyfatebiadau rhannol"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d cyfatebiadau rhannol"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Gwirfoddoli a Bounties"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Mae Archif Anna yn dibynnu ar wirfoddolwyr fel chi. Rydym yn croesawu pob lefel o ymrwymiad, ac mae gennym ddwy brif gategori o gymorth rydym yn chwilio amdano:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Gwaith gwirfoddol ysgafn:</span> os gallwch ond sbario ychydig oriau yma ac acw, mae dal digon o ffyrdd y gallwch helpu. Rydym yn gwobrwyo gwirfoddolwyr cyson gyda <span %(bold)s>🤝 aelodaeth Archif Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Gwaith gwirfoddol trwm (USD$50-USD$5,000 gwobrau):</span> os ydych chi'n gallu neilltuo llawer o amser a/neu adnoddau i'n cenhadaeth, byddem wrth ein bodd yn gweithio'n agosach gyda chi. Yn y pen draw gallwch ymuno â'r tîm mewnol. Er bod gennym gyllideb dynn, rydym yn gallu dyfarnu <span %(bold)s>💰 gwobrau ariannol</span> am y gwaith mwyaf dwys."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Os nad ydych yn gallu gwirfoddoli eich amser, gallwch dal ein helpu llawer trwy <a %(a_donate)s>roi arian</a>, <a %(a_torrents)s>hadlu ein torrentydd</a>, <a %(a_uploading)s>lanlwytho llyfrau</a>, neu <a %(a_help)s>dweud wrth eich ffrindiau am Archif Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Cwmnïau:</span> rydym yn cynnig mynediad uniongyrchol cyflym i'n casgliadau yn gyfnewid am rodd lefel-fenter neu gyfnewid am gasgliadau newydd (e.e. sganiau newydd, datasets OCR, cyfoethogi ein data). <a %(a_contact)s>Cysylltwch â ni</a> os mai chi yw hwn. Gweler hefyd ein <a %(a_llm)s>tudalen LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Gwirfoddoli ysgafn"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Os oes gennych ychydig oriau i'w sbario, gallwch helpu mewn nifer o ffyrdd. Sicrhewch eich bod yn ymuno â'r <a %(a_telegram)s>sgwrs gwirfoddolwyr ar Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Fel arwydd o werthfawrogiad, rydym fel arfer yn rhoi 6 mis o “Llyfrgellydd Lwcus” am gerrig milltir sylfaenol, a mwy am waith gwirfoddoli parhaus. Mae pob carreg filltir yn gofyn am waith o ansawdd uchel — mae gwaith llac yn ein brifo mwy nag y mae'n ein helpu ac fe'i gwrthodir. Cysylltwch â ni trwy <a %(a_contact)s>ebost</a> pan fyddwch yn cyrraedd carreg filltir."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tasg"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Carreg filltir"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Lledaenu gair Archif Anna. Er enghraifft, trwy argymell llyfrau ar AA, cysylltu â'n postiadau blog, neu gyfeirio pobl at ein gwefan yn gyffredinol."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s dolenni neu sgrinluniau."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Dylai'r rhain ddangos i chi rywun yn cael gwybod am Archif Anna, a nhw'n diolch i chi."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Gwella metadata trwy <a %(a_metadata)s>gysylltu</a> â Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Gallwch ddefnyddio'r <a %(a_list)s>rhestr o faterion metadata ar hap</a> fel man cychwyn."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Gwnewch yn siŵr eich bod yn gadael sylw ar faterion rydych chi'n eu trwsio, fel na fydd eraill yn dyblygu eich gwaith."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s dolenni neu gofnodion rydych chi wedi'u gwella."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Cyfieithu</a> y wefan."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Cyfieithu iaith yn llawn (os nad oedd yn agos at gwblhau eisoes)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Gwella'r dudalen Wicipedia ar gyfer Archif Anna yn eich iaith. Cynnwys gwybodaeth o dudalen Wicipedia AA mewn ieithoedd eraill, ac o'n gwefan a'n blog. Ychwanegu cyfeiriadau at AA ar dudalennau perthnasol eraill."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Dolen i hanes golygu yn dangos eich bod wedi gwneud cyfraniadau sylweddol."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Bodloni ceisiadau llyfrau (neu bapur, ac ati) ar fforymau Z-Library neu Library Genesis. Nid oes gennym ein system ceisiadau llyfrau ein hunain, ond rydym yn adlewyrchu'r llyfrgelloedd hynny, felly mae eu gwneud yn well yn gwneud Archif Anna yn well hefyd."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s dolenni neu sgrinluniau o geisiadau rydych chi wedi'u cyflawni."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Tasgau bach a bostiwyd ar ein <a %(a_telegram)s>sgwrs gwirfoddolwyr ar Telegram</a>. Fel arfer ar gyfer aelodaeth, weithiau ar gyfer gwobrau bach."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Tasgau bach wedi'u postio yn ein grŵp sgwrsio gwirfoddolwyr."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Yn dibynnu ar y dasg."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Gwobrau"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Rydym bob amser yn chwilio am bobl gyda sgiliau rhaglenni cadarn neu sgiliau diogelwch ymosodol i gymryd rhan. Gallwch wneud gwahaniaeth mawr wrth gadw treftadaeth dynoliaeth."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Fel diolch, rydym yn rhoi aelodaeth am gyfraniadau cadarn. Fel diolch mawr, rydym yn rhoi gwobrau ariannol am dasgau arbennig o bwysig ac anodd. Ni ddylid ystyried hyn fel amnewid am swydd, ond mae'n gymhelliant ychwanegol a gall helpu gyda chostau a godir."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Mae'r rhan fwyaf o'n cod yn ffynhonnell agored, a byddwn yn gofyn hynny am eich cod hefyd wrth ddyfarnu'r wobr. Mae rhai eithriadau y gallwn eu trafod ar sail unigol."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Dyfernir gwobrau i'r person cyntaf i gwblhau tasg. Mae croeso i chi roi sylw ar docyn gwobr i roi gwybod i eraill eich bod yn gweithio ar rywbeth, fel y gall eraill aros neu gysylltu â chi i ffurfio tîm. Ond byddwch yn ymwybodol bod eraill yn dal i fod yn rhydd i weithio arno hefyd a cheisio eich curo i'r wobr. Fodd bynnag, nid ydym yn dyfarnu gwobrau am waith di-ofal. Os gwneir dau gyflwyniad o ansawdd uchel yn agos at ei gilydd (o fewn diwrnod neu ddau), efallai y byddwn yn dewis dyfarnu gwobrau i'r ddau, yn ôl ein disgresiwn, er enghraifft 100%% am y cyflwyniad cyntaf a 50%% am yr ail gyflwyniad (felly 150%% i gyd)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Ar gyfer y gwobrau mwy (yn enwedig gwobrau sgrapio), cysylltwch â ni pan fyddwch wedi cwblhau ~5%% ohono, ac rydych yn hyderus y bydd eich dull yn graddio i'r garreg filltir lawn. Bydd yn rhaid i chi rannu eich dull gyda ni fel y gallwn roi adborth. Hefyd, fel hyn gallwn benderfynu beth i'w wneud os oes sawl person yn agos at wobr, megis dyfarnu i sawl person, annog pobl i ffurfio tîm, ac ati."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "RHybudd: mae'r tasgau gwobr uchel yn <span %(bold)s>anodd</span> — efallai y bydd yn ddoeth dechrau gyda rhai haws."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Ewch i'n <a %(a_gitlab)s>rhestr materion Gitlab</a> a didoli yn ôl “Blaenoriaeth Label”. Mae hyn yn dangos yn fras y drefn o dasgau rydym yn poeni amdanynt. Mae tasgau heb wobrau penodol yn dal i fod yn gymwys i gael aelodaeth, yn enwedig y rhai wedi'u marcio “Derbyniwyd” ac “Hoff Anna”. Efallai y byddwch am ddechrau gyda “Prosiect cychwynnol”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Diweddariadau am <a %(wikipedia_annas_archive)s>Archif Anna</a>, y llyfrgell wirioneddol agored fwyaf yn hanes dynoliaeth."

#, fuzzy
msgid "layout.index.title"
msgstr "Archif Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Llyfrgell data agored ffynhonnell agored fwyaf y byd. Yn adlewyrchu Sci-Hub, Library Genesis, Z-Library, a mwy."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Chwilio Archif Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Mae Archif Anna angen eich help!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Mae llawer yn ceisio ein dymchwel, ond rydym yn ymladd yn ôl."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Os rhoddwch nawr, cewch <strong>dwbl</strong> nifer y lawrlwythiadau cyflym."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Yn ddilys tan ddiwedd y mis hwn."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Rhoi rhodd"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Achub gwybodaeth ddynol: anrheg gwyliau wych!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Synnu rhywun annwyl, rhoi cyfrif iddynt gyda aelodaeth."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "I gynyddu gwydnwch Archif Anna, rydym yn chwilio am wirfoddolwyr i redeg drychau."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Yr anrheg Dydd Sant Ffolant perffaith!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Mae gennym ddull rhodd newydd ar gael: %(method_name)s. Ystyriwch %(donate_link_open_tag)srhoi rhodd</a> — nid yw'n rhad rhedeg y wefan hon, ac mae eich rhodd yn gwneud gwahaniaeth gwirioneddol. Diolch yn fawr iawn."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Rydym yn cynnal ymgyrch codi arian ar gyfer <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">gefnogi</a> llyfrgell cysgodol comics fwyaf y byd. Diolch am eich cefnogaeth! <a href=\"/donate\">Rhoddwch.</a> Os na allwch roi rhodd, ystyriwch ein cefnogi trwy ddweud wrth eich ffrindiau, a'n dilyn ar <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, neu <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Lawrlwythiadau diweddar:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Chwilio"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Cwestiynau Cyffredin"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Gwella metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Gwirfoddoli & Gwobrau"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Setiau Data"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Gweithgaredd"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Archwiliwr Codau"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Data LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Hafan"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Meddalwedd Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Cyfieithu ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Mewngofnodi / Cofrestru"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Cyfrif"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Archif Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Cadwch mewn cysylltiad"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "Hawliadau DMCA / hawlfraint"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Uwch"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Diogelwch"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Dewisiadau Amgen"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "anghyfaffiliated"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Efallai bod problemau gyda'r ffeil hon."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Lawrlwytho cyflym"

#, fuzzy
msgid "page.donate.copy"
msgstr "copïo"

#, fuzzy
msgid "page.donate.copied"
msgstr "wedi'i gopïo!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Blaenorol"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Nesaf"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "dim ond y mis hwn!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Mae Sci-Hub wedi <a %(a_closed)s>oedi</a> uwchlwytho papurau newydd."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Dewiswch opsiwn talu. Rydym yn rhoi gostyngiadau ar daliadau seiliedig ar grypthograffeg %(bitcoin_icon)s, oherwydd rydym yn cael llai o ffioedd (yn sylweddol)."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Dewiswch opsiwn talu. Ar hyn o bryd dim ond taliadau seiliedig ar grypthograffeg sydd gennym %(bitcoin_icon)s, gan fod proseswyr talu traddodiadol yn gwrthod gweithio gyda ni."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Ni allwn gefnogi cardiau credyd/debyd yn uniongyrchol, oherwydd nid yw banciau eisiau gweithio gyda ni. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Fodd bynnag, mae sawl ffordd i ddefnyddio cardiau credyd/debyd beth bynnag, gan ddefnyddio ein dulliau talu eraill:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Lawrlwythiadau araf ac allanol"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Lawrlwythiadau"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Os ydych yn defnyddio crypto am y tro cyntaf, rydym yn awgrymu defnyddio %(option1)s, %(option2)s, neu %(option3)s i brynu a rhoi Bitcoin (y cryptocurrency gwreiddiol a mwyaf defnyddir)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 dolen o gofnodion a wellaist."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 dolen neu sgrinlun."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 dolen neu sgrinlun o geisiadau a gyflawnwyd gennych."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Os oes gennych ddiddordeb mewn adlewyrchu'r datasets hyn at ddibenion <a %(a_faq)s>archifo</a> neu <a %(a_llm)s>hyfforddi LLM</a>, cysylltwch â ni."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Os oes gennych ddiddordeb mewn adlewyrchu'r set ddata hon at ddibenion <a %(a_archival)s>archifo</a> neu <a %(a_llm)s>hyfforddiant LLM</a>, cysylltwch â ni."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Prif wefan"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Gwybodaeth gwlad ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Os oes gennych ddiddordeb mewn adlewyrchu'r set ddata hon at ddibenion <a %(a_archival)s>archifo</a> neu <a %(a_llm)s>hyfforddi LLM</a>, cysylltwch â ni."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Mae'r Asiantaeth ISBN Ryngwladol yn rhyddhau'n rheolaidd yr ystodau y mae wedi'u dyrannu i asiantaethau ISBN cenedlaethol. O hyn gallwn ddeillio pa wlad, rhanbarth, neu grŵp iaith y mae'r ISBN hwn yn perthyn iddo. Ar hyn o bryd rydym yn defnyddio'r data hwn yn anuniongyrchol, drwy'r llyfrgell Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Adnoddau"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Diweddarwyd ddiwethaf: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Gwefan ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Heb gynnwys “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Ein hysbrydoliaeth ar gyfer casglu metadata yw nod Aaron Swartz o “un dudalen we ar gyfer pob llyfr a gyhoeddwyd erioed”, y creodd <a %(a_openlib)s>Open Library</a> ar ei gyfer."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Mae'r prosiect hwnnw wedi gwneud yn dda, ond mae ein sefyllfa unigryw yn ein galluogi i gael metadata na allant."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Ysbrydoliaeth arall oedd ein hawydd i wybod <a %(a_blog)s>faint o lyfrau sydd yn y byd</a>, fel y gallwn gyfrifo faint o lyfrau sydd gennym ar ôl i'w hachub."

#~ msgid "page.partner_download.text1"
#~ msgstr "Er mwyn rhoi cyfle i bawb lawrlwytho ffeiliau am ddim, mae angen i chi aros <strong>%(wait_seconds)s eiliad</strong> cyn y gallwch lawrlwytho'r ffeil hon."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Adnewyddu tudalen yn awtomatig. Os byddwch yn colli'r ffenestr lawrlwytho, bydd y taimer yn ailgychwyn, felly argymhellir adnewyddu awtomatig."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Lawrlwytho nawr"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Trosi: defnyddiwch offer ar-lein i drosi rhwng fformatau. Er enghraifft, i drosi rhwng epub a pdf, defnyddiwch <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: lawrlwythwch y ffeil (cefnogir pdf neu epub), yna <a %(a_kindle)s>anfonwch ef i Kindle</a> gan ddefnyddio'r we, ap, neu e-bost. Offer defnyddiol: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Cefnogi awduron: Os ydych chi'n hoffi hyn ac yn gallu ei fforddio, ystyriwch brynu'r gwreiddiol, neu gefnogi'r awduron yn uniongyrchol."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Cefnogi llyfrgelloedd: Os yw hwn ar gael yn eich llyfrgell leol, ystyriwch ei fenthyg am ddim yno."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Ddim ar gael yn uniongyrchol mewn swmp, dim ond mewn lled-swmp y tu ôl i wal dalu"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Mae Archif Anna yn rheoli casgliad o <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "Mae ISBNdb yn gwmni sy'n sgrapio amrywiol siopau llyfrau ar-lein i ddod o hyd i fetadata ISBN. Mae Archif Anna wedi bod yn gwneud copïau wrth gefn o fetadata llyfrau ISBNdb. Mae'r metadata hwn ar gael trwy Archif Anna (er nad yw ar hyn o bryd yn y chwiliad, ac eithrio os ydych chi'n chwilio am rif ISBN yn benodol)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Am fanylion technegol, gweler isod. Ar ryw adeg gallwn ei ddefnyddio i bennu pa lyfrau sy'n dal i fod ar goll o lyfrgelloedd cysgodol, er mwyn blaenoriaethu pa lyfrau i'w darganfod a/neu eu sganio."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Ein post blog am y data hwn"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Sgrapio ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Ar hyn o bryd mae gennym un torrent, sy'n cynnwys ffeil 4.4GB wedi'i chywasgu <a %(a_jsonl)s>Llinellau JSON</a> (20GB heb ei chywasgu): “isbndb_2022_09.jsonl.gz”. I fewnforio ffeil “.jsonl” i PostgreSQL, gallwch ddefnyddio rhywbeth fel <a %(a_script)s>y sgript hwn</a>. Gallwch hyd yn oed ei bibellio'n uniongyrchol gan ddefnyddio rhywbeth fel %(example_code)s fel ei fod yn dadbacio ar y hedfan."

#~ msgid "page.donate.wait"
#~ msgstr "Arhoswch o leiaf <span %(span_hours)s>dwy awr</span> (a diweddarwch y dudalen hon) cyn cysylltu â ni."

#~ msgid "page.codes.search_archive"
#~ msgstr "Chwilio Archif Anna am “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Rhoddwch gan ddefnyddio Alipay neu WeChat. Gallwch ddewis rhwng y rhain ar y dudalen nesaf."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Lledaenu gair Archif Anna ar gyfryngau cymdeithasol a fforymau ar-lein, trwy argymell llyfrau neu restrau ar AA, neu ateb cwestiynau."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Mae casgliad Ffuglen wedi gwyro ond mae ganddo <a %(libgenli)s>torrents</a> o hyd, er nad ydynt wedi'u diweddaru ers 2022 (mae gennym lawrlwythiadau uniongyrchol)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Mae Archif Anna a Libgen.li yn rheoli casgliadau o <a %(comics)s>lyfrau comig</a> ac <a %(magazines)s>cylchgronau</a> ar y cyd."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Dim torrents ar gyfer casgliadau ffuglen Rwsiaidd a dogfennau safonol."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Nid oes unrhyw dorrenti ar gael ar gyfer y cynnwys ychwanegol. Mae'r torrenti sydd ar wefan Libgen.li yn ddrych o dorrenti eraill a restrir yma. Yr eithriad yw torrenti ffuglen sy'n dechrau ar %(fiction_starting_point)s. Mae'r torrenti comics a chylchgronau yn cael eu rhyddhau fel cydweithrediad rhwng Archif Anna a Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "O gasgliad <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> tarddiad uniongyrchol yn aneglur. Yn rhannol o the-eye.eu, yn rhannol o ffynonellau eraill."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

