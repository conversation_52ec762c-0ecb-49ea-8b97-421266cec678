msgid "layout.index.invalid_request"
msgstr "Solicitação Invalida. Acesse %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Biblioteca de Empréstimos do Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " e "

msgid "layout.index.header.tagline_and_more"
msgstr "e mais"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nós es<PERSON> %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Recolhemos e disponibilizamos conteúdo do %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Nosso código e dados são totalmente abertos."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;A maior biblioteca verdadeiramente aberta da história da humanidade."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;livros, %(paper_count)s&nbsp;artigos — preservados para sempre."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp; A maior biblioteca de código e dados abertos do mundo. ⭐️&nbsp; Espelhamos Sci-Hub, Library Genesis, Z-Library e muito mais. 📈&nbsp; %(book_any)s livros, %(journal_article)s artigos, %(book_comic)s quadrinhos, %(magazine)s revistas — preservados para sempre."

msgid "layout.index.header.tagline_short"
msgstr "📚 A maior biblioteca de código e dados abertos do mundo.<br>⭐️ Espelhamos Sci-Hub, Libgen, Zlib, e mais."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadados incorretos (p. ex. título, descrição, imagem da capa)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemas ao baixar (p. ex. não é possível conectar, mensagem de erro, muito lento)"

msgid "common.md5_report_type_mapping.broken"
msgstr "O arquivo não pôde ser aberto (p. ex. arquivo corrompido, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Qualidade baixa (p. ex. problemas de formatação, má qualidade de digitalização, páginas faltando)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / arquivo deve ser removido (p. ex. publicidade, conteúdo abusivo)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reivindicação de direitos autorais"

msgid "common.md5_report_type_mapping.other"
msgstr "Outros"

msgid "common.membership.tier_name.bonus"
msgstr "Downloads bônus"

msgid "common.membership.tier_name.2"
msgstr "Bibliófilo Brilhante"

msgid "common.membership.tier_name.3"
msgstr "Arquivista Afortunado"

msgid "common.membership.tier_name.4"
msgstr "Acumulador de Dados Deslumbrante"

msgid "common.membership.tier_name.5"
msgstr "Arquivista Admirável"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s %(amount_usd)s total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bônus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "não pago"

msgid "common.donation.order_processing_status_labels.1"
msgstr "pago"

msgid "common.donation.order_processing_status_labels.2"
msgstr "cancelado"

msgid "common.donation.order_processing_status_labels.3"
msgstr "vencido"

msgid "common.donation.order_processing_status_labels.4"
msgstr "esperando Anna confirmar"

msgid "common.donation.order_processing_status_labels.5"
msgstr "inválido"

msgid "page.donate.title"
msgstr "Doar"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Você tem uma <a %(a_donation)s>doação existente</a> em andamento. Por favor, finalize ou cancele essa doação antes de fazer uma nova doação."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ver todas as minhas doações</a>"

msgid "page.donate.header.text1"
msgstr "Acervo da Anna é um projeto sem fins lucrativos, de código e dados abertos. Ao doar e tornar-se um membro, você apoia o nosso funcionamento e crescimento. A todos os nossos membros: muito obrigado por nos ajudar a manter o projeto! ❤️"

msgid "page.donate.header.text2"
msgstr "Para obter mais informações, consulte a <a %(a_donate)s>FAQ - doações</a>."

msgid "page.donate.refer.text1"
msgstr "Para obter ainda mais downloads, <a %(a_refer)s>indique seus amigos</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Você possui %(percentage)s%% downloads rápidos bônus, pois você foi indicado pelo usuário %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Isso se aplica a todo o período de assinatura."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s downloads rápidos por dia"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se você doar este mês!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mês"

msgid "page.donate.buttons.join"
msgstr "Participe"

msgid "page.donate.buttons.selected"
msgstr "Selecionado"

msgid "page.donate.buttons.up_to_discounts"
msgstr "até %(percentage)s%% de desconto"

msgid "page.donate.perks.scidb"
msgstr "Artigos do SciDB <strong>ilimitados</strong> sem verificação"

msgid "page.donate.perks.jsonapi"
msgstr "Acesso à <a %(a_api)s>API JSON</a>"

msgid "page.donate.perks.refer"
msgstr "Ganhe <strong>%(percentage)s%% downloads bônus</strong> <a %(a_refer)s>indicando amigos</a>."

msgid "page.donate.perks.credits"
msgstr "O seu nome de usuário ou uma menção anónima nos créditos"

msgid "page.donate.perks.previous_plus"
msgstr "Vantagens anteriores, mais:"

msgid "page.donate.perks.early_access"
msgstr "Acesso antecipado a novas funcionalidades"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Atualizações exclusivas dos bastidores no Telegram"

msgid "page.donate.perks.adopt"
msgstr "“Adote um torrent”: seu usuário ou uma mensagem no nome de um torrent <div %(div_months)s>uma vez a cada 12 meses de assinatura</div>"

msgid "page.donate.perks.legendary"
msgstr "Status lendário na preservação do conhecimento e da cultura da humanidade"

msgid "page.donate.expert.title"
msgstr "Acesso Avançado"

msgid "page.donate.expert.contact_us"
msgstr "Contate-nos"

msgid "page.donate.small_team"
msgstr "Nós somos um pequeno grupo de voluntários. Pode levar de 1 a 2 semanas para responder."

msgid "page.donate.expert.unlimited_access"
msgstr "Acesso de alta velocidade <strong>Ilimitado</strong>"

msgid "page.donate.expert.direct_sftp"
msgstr "Servidores<strong>SFTP</strong> diretos"

msgid "page.donate.expert.enterprise_donation"
msgstr "Doação ou troca em nível empresarial para novas coleções (por exemplo, novos escaneamentos, conjuntos de dados com OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Aceitamos grandes doações de indivíduos bem-afortunados ou instituições ricas. "

msgid "page.donate.header.large_donations"
msgstr "Para doações acima de 5000 dólares, entre em contato diretamente no email %(email)s."

msgid "page.donate.header.recurring"
msgstr "Esteja ciente de que, embora as assinaturas nesta página sejam \"por mês\", elas são doações únicas (não recorrentes). Veja a <a %(faq)s>FAQ - Doações</a>."

msgid "page.donate.without_membership"
msgstr "Se quiser fazer uma doação (qualquer valor) sem ser membro, sinta-se à vontade para usar este endereço Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Por favor, selecione um método de pagamento."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporariamente indisponível)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "cartão-presente da %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Cartão bancário (usando app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Cripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Cartão de crédito/débito"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (EUA) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regular)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Cartão / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Crédito / débito / Apple / Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Cartão bancário"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Cartão de crédito/débito (backup)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Cartão de crédito/débito 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Com cripto você pode doar com as moedas BTC, ETH, XMR, e SOL. Use essa opção se você já é familiarizado com criptomoedas."

msgid "page.donate.payment.desc.crypto2"
msgstr "Com cripto você pode doar usando BTC, ETH, XMR e muito mais."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se você está usando criptomoeda pela primeira vez, sugerimos usar %(options)s para comprar e doar Bitcoin (a criptomoeda original e mais usada)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Para doar usando o PayPal (EUA), usamos PayPal cripto, que nos permite permanecer anônimos. Agradecemos por dedicar seu tempo para aprender como doar usando este método, pois é de grande ajuda para nós."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Doe usando o PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Doe usando o Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se você tiver Cash App, essa é a maneira mais fácil de doar!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Observe que para transações abaixo de %(amount)s, o Cash App pode cobrar uma taxa de %(fee)s. Para %(amount)s ou mais, é gratuito!"

msgid "page.donate.payment.desc.revolut"
msgstr "Doe usando Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se você usa Revolut, esta é a maneira mais fácil de doar!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Doe com um cartão de crédito ou débito."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay também devem funcionar."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Observe que para doações pequenas, as taxas do cartão de crédito podem eliminar nosso desconto de %(discount)s%%, então recomendamos assinaturas mais longas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Observe que para doações pequenas, as taxas são altas, então recomendamos assinaturas mais longas."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Com a Binance, você compra Bitcoin com um cartão de crédito/débito ou conta bancária e depois doa esse Bitcoin para nós. Dessa forma, podemos permanecer seguros e anônimos ao aceitar sua doação."

msgid "page.donate.payment.desc.binance_p2"
msgstr "A Binance está disponível em quase todos os países e oferece suporte à maioria dos bancos e cartões de crédito/débito. Esta é atualmente a nossa principal recomendação. Agradecemos por você dedicar seu tempo para aprender como doar por meio desse método, pois nos ajuda muito."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Doe usando sua conta regular do PayPal."

msgid "page.donate.payment.desc.givebutter"
msgstr "Doe usando cartão de crédito/débito, PayPal ou Venmo. Você pode escolher entre eles na próxima página."

msgid "page.donate.payment.desc.amazon"
msgstr "Doe usando um gift card da Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Observe que precisamos arredondar para valores aceitos por nossos revendedores (mínimo de %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Oferecemos suporte apenas para Amazon.com, e não outros sites da Amazon. Por exemplo, .br, .de, .co.uk e .ca NÃO são suportados."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Esta opção é para %(amazon)s. Se você deseja usar outro site da Amazon, selecione-o acima."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Este método usa um provedor de criptomoeda como conversão intermediária. Isso pode ser um pouco confuso, portanto, use esse método apenas se outros métodos de pagamento não funcionarem. Também não funciona em todos os países."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Doe usando um cartão de crédito/débito, através do aplicativo Alipay (super fácil de configurar)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instale o aplicativo Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instale o aplicativo Alipay na <a %(a_app_store)s>Apple App Store</a> ou <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registre-se usando seu número de telefone."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nenhum outro detalhe pessoal é necessário."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Adicione um cartão bancário"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Aceitamos: Visa, MasterCard, JCB, Diners Club e Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Veja <a %(a_alipay)s>este guia</a> para mais informações."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Não podemos aceitar cartões de crédito/débito diretamente, porque os bancos não querem trabalhar conosco. ☹ No entanto, há várias maneiras de usar cartões de crédito/débito, utilizando outros métodos de pagamento:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Cartão Presente Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Envie-nos cartões-presente da Amazon.com usando seu cartão de crédito/débito."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay aceita cartões de crédito/débito internacionais. Veja <a %(a_alipay)s>este guia</a> para mais informações."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) oferece suporte a cartões de crédito/débito internacionais. No aplicativo WeChat, vá para “Eu → Serviços → Carteira → Adicionar um cartão”. Se não encontrar, habilite-o usando “Eu → Configurações → Geral → Ferramentas → Weixin Pay → Ativar”."

msgid "page.donate.ccexp.crypto"
msgstr "Você pode comprar criptomoedas usando cartões de crédito/débito."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Serviços expressos de criptomoeda"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Os serviços expressos são convenientes, mas cobram taxas mais altas."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Você pode usar isso ao invés de uma corretora de criptomoedas se estiver procurando fazer uma doação grande mais rapidamente, e não se importar com uma taxa de US$ 5 a US$ 10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Certifique-se de enviar a quantidade exata de criptomoeda mostrada na página de doação, não o valor em $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Caso contrário, a taxa será subtraída e não poderemos processar automaticamente sua assinatura."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(mínimo: %(minimum)s dependendo do país, sem verificação para a primeira transação)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(mínimo: %(minimum)s, sem verificação para a primeira transação)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se alguma dessas informações estiver desatualizada, por favor envie-nos um e-mail para nos informar."

msgid "page.donate.payment.desc.bmc"
msgstr "Para cartões de crédito, cartões de débito, Apple Pay e Google Pay, usamos “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). No sistema deles, um “café” equivale a US$ 5, então sua doação será arredondada para o múltiplo de 5 mais próximo."

msgid "page.donate.duration.intro"
msgstr "Selecione por quanto tempo você quer assinar."

msgid "page.donate.duration.1_mo"
msgstr "1 mês"

msgid "page.donate.duration.3_mo"
msgstr "3 meses"

msgid "page.donate.duration.6_mo"
msgstr "6 meses"

msgid "page.donate.duration.12_mo"
msgstr "12 meses"

msgid "page.donate.duration.24_mo"
msgstr "24 meses"

msgid "page.donate.duration.48_mo"
msgstr "48 meses"

msgid "page.donate.duration.96_mo"
msgstr "96 meses"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>depois de <span %(span_discount)s></span> de desconto</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Este método de pagamento requer um mínimo de %(amount)s. Por favor, selecione uma duração ou método de pagamento diferente."

msgid "page.donate.buttons.donate"
msgstr "Doar"

msgid "page.donate.payment.maximum_method"
msgstr "Este método de pagamento apenas permite um máximo de %(amount)s. Por favor selecione um método ou duração diferentes."

msgid "page.donate.login2"
msgstr "Para se tornar um membro, por favor <a %(a_login)s>Faça Log in ou Registre-se</a>. Obrigado pelo seu suporte!"

msgid "page.donate.payment.crypto_select"
msgstr "Selecione a criptomoeda desejada:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(valor mínimo mais baixo)"

msgid "page.donate.coinbase_eth"
msgstr "(use ao enviar Ethereum do Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(aviso: valor mínimo alto)"

msgid "page.donate.submit.confirm"
msgstr "Clique no botão doar para confirmar esta doação."

msgid "page.donate.submit.button"
msgstr "Doar <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Você ainda pode cancelar a doação até a última etapa."

msgid "page.donate.submit.success"
msgstr "✅ Redirecionando para a página de doação…"

msgid "page.donate.submit.failure"
msgstr "❌ Algo deu errado. Por favor, atualize a página e tente novamente."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mês"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "Por 1 mês"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "por 3 meses"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "por 6 meses"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "por 12 meses"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "por 24 meses"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "por 48 meses"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "por 96 meses"

msgid "page.donate.submit.button.label.1_mo"
msgstr "por 1 mês “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "por 3 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "por 6 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "por 12 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "por 24 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "por 48 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "por 96 meses “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Doação"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mês por %(duration)s meses, incluindo %(discounts)s%% de desconto)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mês por %(duration)s meses)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificador: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Cancelar"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Tem certeza de que deseja cancelar? Não cancele se já tiver efetuado o pagamento."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Sim, quero cancelar"

msgid "page.donation.header.cancel.success"
msgstr "✅ Sua doação foi cancelada."

msgid "page.donation.header.cancel.new_donation"
msgstr "Fazer uma nova doação"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Algo deu errado. Por favor, recarregue a página e tente novamente."

msgid "page.donation.header.reorder"
msgstr "Repetir doação"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Você já efetuou o pagamento. Se desejar revisar as instruções de pagamento de qualquer maneira, clique aqui:"

msgid "page.donation.old_instructions.show_button"
msgstr "Mostrar as instruções de pagamento antigas"

msgid "page.donation.thank_you_donation"
msgstr "Obrigado por sua doação!"

msgid "page.donation.thank_you.secret_key"
msgstr "Caso ainda não tenha, anote sua chave secreta para fazer login:"

msgid "page.donation.thank_you.locked_out"
msgstr "Caso contrário, poderá ficar sem acesso a esta conta!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "As instruções de pagamento estão desatualizadas. Se você deseja fazer outra doação, use o botão \"Repetir doação\" acima."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> os preços das criptomoedas podem flutuar muito, às vezes até 20%% em alguns minutos. Essa flutuação ainda é menor que as taxas que pagamos a vários provedores de pagamento, que geralmente cobram entre 50-60%% para uma \"caridade escondida\" como a nossa. <u>Se você nos enviar o comprovante do valor original que pagou, iremos aplicar na sua conta a assinatura escolhida. </u> (se o comprovante for de poucas horas atrás). Agradecemos que você esteja disposto a lidar com coisas assim para nos ajudar! ❤️"

msgid "page.donation.expired"
msgstr "Essa doação foi expirada. Por favor cancele e crie uma nova."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instruções para criptomoedas"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transferir para uma de nossas contas de criptomoedas"

msgid "page.donation.payment.crypto.text1"
msgstr "Faça a doação do valor total de %(total)s para um destes endereços:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compre Bitcoin no PayPal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Encontre a página \"Cripto\" em seu aplicativo ou site do PayPal. Normalmente, isso está localizado em \"Finanças\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Siga as instruções para comprar Bitcoin (BTC). Você só precisa comprar a quantidade que deseja doar, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transferir o Bitcoin para nosso endereço"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vá para a página \"Bitcoin\" em seu aplicativo ou site do PayPal. Clique no botão \"Transferir\" %(transfer_icon)s e, em seguida, em \"Enviar\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Insira nosso endereço Bitcoin (BTC) como o destinatário e siga as instruções para enviar sua doação de %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instruções para cartões de crédito e débito"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Doe através de nossa página para cartões de crédito e débito"

msgid "page.donation.donate_on_this_page"
msgstr "Doe %(amount)s nessa <a %(a_page)s>página</a>."

msgid "page.donation.stepbystep_below"
msgstr "Preste atenção no guia passo-a-passo a seguir."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Aguardando confirmação (atualize a página para verificar)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Aguardando transferência (atualize a página para verificar)…"

msgid "page.donation.time_left_header"
msgstr "Tempo restante:"

msgid "page.donation.might_want_to_cancel"
msgstr "(você pode querer cancelar e criar uma nova doação)"

msgid "page.donation.reset_timer"
msgstr "Para resetar o timer, simplesmente crie uma nova doação."

msgid "page.donation.refresh_status"
msgstr "Atualizar status"

msgid "page.donation.footer.issues_contact"
msgstr "Se você encontrar algum problema, por favor nos contate em %(email)s e inclua o máximo de informação possível (como capturas de tela)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Se você já pagou:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Às vezes, a confirmação pode levar até 24 horas, então certifique-se de atualizar esta página (mesmo que ela tenha expirado)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Compre a moeda PYUSD no PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Siga as instruções para comprar a moeda PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Vá para a página \"PYUSD\" no seu aplicativo ou site do PayPal. Aperte o botão \"Transferir\" %(icon)s, e em seguida \"Enviar\"."

msgid "page.donation.transfer_amount_to"
msgstr "Transfira %(amount)s para %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Compre Bitcoin (BTC) no Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vá para a página “Bitcoin” (BTC) no Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante."

msgid "page.donation.cash_app_btc.step2"
msgstr "Transfira o Bitcoin para o nosso endereço"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clique no botão “Enviar bitcoin” para fazer uma “retirada”. Mude de dólares para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se você estiver com dúvidas."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Para pequenas doações (menos de $25), você pode precisar usar Rush ou Priority."

msgid "page.donation.revolut.step1"
msgstr "Compre Bitcoin (BTC) no Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Vá para a página “Cripto” no Revolut para comprar Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante."

msgid "page.donation.revolut.step2"
msgstr "Transfira o Bitcoin para o nosso endereço"

msgid "page.donation.revolut.step2.transfer"
msgstr "Clique no botão “Enviar bitcoin” para fazer uma “retirada”. Mude de euros para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se você ficar com dúvidas."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Certifique-se de usar o valor em BTC abaixo, <em>NÃO</em> euros ou dólares, caso contrário, não receberemos o valor correto e não poderemos confirmar automaticamente sua assinatura."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Para pequenas doações (menos de $25), você pode precisar usar Rush ou Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Use qualquer um dos seguintes serviços expressos de “cartão de crédito para Bitcoin”, que levam apenas alguns minutos:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Preencha os seguintes detalhes no formulário:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Valor em BTC / Bitcoin:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Por favor, use este <span %(underline)s>valor exato</span>. Seu custo total pode ser maior devido às taxas do cartão de crédito. Para pequenos valores, isso pode ser mais do que nosso desconto, infelizmente."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Endereço BTC / Bitcoin (carteira externa):"

msgid "page.donation.crypto_instructions"
msgstr "Instruções para %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Nós só apoiamos a versão padrão das criptomoedas, sem redes ou versões exóticas das moedas. Pode levar até uma hora para confirmar a transação, dependendo da moeda."

msgid "page.donation.crypto_qr_code_title"
msgstr "Digitalize o código QR para pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Digitalize este código QR com seu aplicativo Crypto Wallet para preencher rapidamente os detalhes do pagamento"

msgid "page.donation.amazon.header"
msgstr "Gift Card da Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Por favor use o <a %(a_form)s>formulário oficial da Amazon.com</a> para nos enviar um gift card de%(amount)s para o endereço de email abaixo."

msgid "page.donation.amazon.only_official"
msgstr "Nós não podemos aceitar outros métodos de gift cards, <strong> somente os enviados diretamente do formulário oficial da Amazon.com</strong>. Nós não podemos devolver o seu gift card se você não fizer uso deste formulário."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Insira o valor exato: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Por favor, NÃO escreva sua própria mensagem."

msgid "page.donation.amazon.form_to"
msgstr "\"Para\" email do destinatário no formulário:"

msgid "page.donation.amazon.unique"
msgstr "Único para sua conta, não compartilhe."

msgid "page.donation.amazon.only_use_once"
msgstr "Use apenas uma vez."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Esperando pelo gift card... (atualize a página para checar)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Após enviar o seu gift card, nosso sistema automatizado irá confirmar em poucos minutos. Caso não funcione, tente reenviar seu gift card (<a %(a_instr)s>instruções</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Caso ainda não funcione por favor nos envie um email e a Anna eventualmente irá verificar manualmente (isso poderá levar alguns dias), e tenha certeza de mencionar que você já tentou reenviar."

msgid "page.donation.amazon.example"
msgstr "Exemplo:"

msgid "page.donate.strange_account"
msgstr "Observe que o nome de usuário ou a imagem podem parecer estranhos. Não se preocupe! Essas contas são administradas pelos nossos parceiros de doação. Nossas contas não foram hackeadas."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instruções Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Doe no Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Doe o valor total de %(total)s usando <a %(a_account)s>esta conta Alipay</a>"

msgid "page.donation.page_blocked"
msgstr "Se a página de doação for bloqueada, tente uma conexão de internet diferente (por exemplo, VPN ou internet do celular)."

msgid "page.donation.payment.alipay.error"
msgstr "Infelizmente, a página Alipay geralmente só é acessível na <strong>China continental</strong>. Pode ser necessário desativar temporariamente sua VPN ou usar uma VPN para a China continental (ou Hong Kong também funciona às vezes)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faça uma doação (escaneie o código QR ou pressione o botão)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Abra a <a%(a_href)s>página de doação com código QR</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Escaneie o código QR com o aplicativo Alipay ou pressione o botão para abrir o aplicativo Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Por favor, seja paciente; a página pode demorar um pouco para carregar, pois está na China."

msgid "page.donation.payment.wechat.top_header"
msgstr "Instruções do WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Doe no WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Doe o valor total de %(total)s usando <a %(a_account)s>esta conta do WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instruções para Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span> Doe por Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Doe a quantia total de %(total)s usando a conta Pix <a %(a_account)s>"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Envie o comprovante por email"

msgid "page.donation.footer.verification"
msgstr "Envie um recibo ou captura de tela para o seu endereço de verificação pessoal. NÃO use este endereço de e-mail para sua doação via PayPal."

msgid "page.donation.footer.text1"
msgstr "Enviar um comprovante ou captura de tela para seu endereço de verificação pessoal:"

msgid "page.donation.footer.crypto_note"
msgstr "Se a taxa de câmbio da criptomoeda flutuou durante a transação, não esqueça de incluir o comprovante mostrando a taxa de câmbio original. Agradecemos muito o esforço de usar criptomoedas, nos ajuda muito!"

msgid "page.donation.footer.text2"
msgstr "Após enviar seu recibo por e-mail, clique neste botão para que Anna possa verificar manualmente (isso pode levar alguns dias):"

msgid "page.donation.footer.button"
msgstr "Sim, eu enviei meu comprovante"

msgid "page.donation.footer.success"
msgstr "✅ Obrigado pela sua doação! Anna ativará manualmente sua assinatura dentro de alguns dias."

msgid "page.donation.footer.failure"
msgstr "❌ Algo deu errado. Por favor, recarregue a página e tente novamente."

msgid "page.donation.stepbystep"
msgstr "Guia passo-a-passo"

msgid "page.donation.crypto_dont_worry"
msgstr "Alguns dos passos mencionam carteiras de criptomoedas, mas não se preocupe, você não precisa aprender nada sobre criptomoedas para isso."

msgid "page.donation.hoodpay.step1"
msgstr "1. Insira seu email."

msgid "page.donation.hoodpay.step2"
msgstr "2. Selecione seu método de pagamento."

msgid "page.donation.hoodpay.step3"
msgstr "3. Selecione novamente seu método de pagamento."

msgid "page.donation.hoodpay.step4"
msgstr "4. Selecionar carteira \"Self-hosted / auto-hospedada\"."

msgid "page.donation.hoodpay.step5"
msgstr "5. Clique em \"Eu confirmo titularidade\"."

msgid "page.donation.hoodpay.step6"
msgstr "6. Você receberá um recibo via email. Por favor nos envie isso e vamos confirmar sua doação o mais rápido possível."

msgid "page.donate.wait_new"
msgstr "Por favor, aguarde pelo menos <span %(span_hours)s>24 horas</span> (e atualize esta página) antes de nos contatar."

msgid "page.donate.mistake"
msgstr "Se você cometeu um erro durante o pagamento, não podemos reembolsar, mas podemos tentar resolver a situação."

msgid "page.my_donations.title"
msgstr "Minhas doações"

msgid "page.my_donations.not_shown"
msgstr "Os detalhes de doações não são exibidos publicamente."

msgid "page.my_donations.no_donations"
msgstr "Ainda não há doações. <a %(a_donate)s>Quero fazer minha primeira doação.</a>"

msgid "page.my_donations.make_another"
msgstr "Fazer outra doação."

msgid "page.downloaded.title"
msgstr "Arquivos baixados"

msgid "page.downloaded.fast_partner_star"
msgstr "Downloads dos nossos Servidores Rápidos de Parceiros estão marcados com %(icon)s."

msgid "page.downloaded.twice"
msgstr "Se você baixou um arquivo com ambos downloads rápido e lento, aparecerá duas vezes."

msgid "page.downloaded.fast_download_time"
msgstr "Downloads rápidos nas últimas 24 horas contam no limite diário."

msgid "page.downloaded.times_utc"
msgstr "Todos os horários estão em UTC."

msgid "page.downloaded.not_public"
msgstr "Arquivos baixados não são exibidos publicamente."

msgid "page.downloaded.no_files"
msgstr "Nenhum arquivo baixado até o momento."

msgid "page.downloaded.last_18_hours"
msgstr "Últimas 18 horas"

msgid "page.downloaded.earlier"
msgstr "Anteriormente"

msgid "page.account.logged_in.title"
msgstr "Conta"

msgid "page.account.logged_out.title"
msgstr "Entrar / Registrar"

msgid "page.account.logged_in.account_id"
msgstr "ID da conta: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Perfil público: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chave secreta (não compartilhe!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "mostrar"

msgid "page.account.logged_in.membership_has_some"
msgstr "Assinatura: <strong>%(tier_name)s</strong> até %(until_date)s <a %(a_extend)s>(estender)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Assinatura: <strong>Nenhuma</strong> <a %(a_become)s>(Torne-se um Membro)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Downloads rápidos usados (últimas 24 horas): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "quais downloads?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupo exclusivo do Telegram: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Participe aqui!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Atualize para um <a %(a_tier)s>nível superior</a> para participar do nosso grupo."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Entre em contato com Anna em %(email)s se estiver interessado em atualizar sua assinatura para um nível superior."

msgid "page.contact.title"
msgstr "Email de contato"

msgid "page.account.logged_in.membership_multiple"
msgstr "Você pode combinar várias assinaturas (downloads rápidos por 24 horas serão somados)."

msgid "layout.index.header.nav.public_profile"
msgstr "Perfil público"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Arquivos baixados"

msgid "layout.index.header.nav.my_donations"
msgstr "Minhas doações"

msgid "page.account.logged_in.logout.button"
msgstr "Sair"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Agora você está desconectado. Recarregue a página para fazer login novamente."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Algo deu errado. Por favor, recarregue a página e tente novamente."

msgid "page.account.logged_out.registered.text1"
msgstr "Cadastro bem sucedido! Sua chave secreta é: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Guarde essa chave com cuidado. Se você a perder, você perderá o acesso à sua conta."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Favoritar.</strong> Você pode favoritar esta página para recuperar sua chave.</li><li %(li_item)s><strong>Baixar.</strong> Clique<a %(a_download)s>neste link</a> para baixar sua chave.</li><li %(li_item)s><strong>Gerenciador de senhas.</strong> Utilizar um gerenciador de senhas para salvar a sua chave quando coloca-la abaixo</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Digite sua chave secreta para fazer login:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chave secreta"

msgid "page.account.logged_out.key_form.button"
msgstr "Entrar"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Chave secreta inválida. Verifique sua chave e tente novamente, ou então registre uma nova conta abaixo."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Não perca sua chave!"

msgid "page.account.logged_out.register.header"
msgstr "Ainda não tem uma conta?"

msgid "page.account.logged_out.register.button"
msgstr "Registrar uma nova conta"

msgid "page.login.lost_key"
msgstr "Se perdeu sua chave, <a %(a_contact)s>entre em contato conosco</a> e forneça o máximo de informações possível."

msgid "page.login.lost_key_contact"
msgstr "Talvez seja necessário criar temporariamente uma nova conta para entrar em contato conosco."

msgid "page.account.logged_out.old_email.button"
msgstr "Conta antiga baseada em e-mail? Insira seu <a %(a_open)s>email aqui</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "editar"

msgid "page.list.edit.button"
msgstr "Salvar"

msgid "page.list.edit.success"
msgstr "✅ Salvo. Por favor recarregue a página."

msgid "page.list.edit.failure"
msgstr "❌ Algo deu errado. Por favor tente novamente."

msgid "page.list.by_and_date"
msgstr "Lista por %(by)s, criada <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "A lista está vazia."

msgid "page.list.new_item"
msgstr "Para adicionar ou remover desta lista, encontre um arquivo e abra a aba \"Listas\"."

msgid "page.profile.title"
msgstr "Perfil"

msgid "page.profile.not_found"
msgstr "Perfil não encontrado."

msgid "page.profile.header.edit"
msgstr "editar"

msgid "page.profile.change_display_name.text"
msgstr "Altere o seu nome de perfil. O seu identificador (a parte a depois de\"#\") não pode ser alterada."

msgid "page.profile.change_display_name.button"
msgstr "Guardar"

msgid "page.profile.change_display_name.success"
msgstr "✅ Salvo. Por favor atualize a página."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo deu errado. Por favor tente novamente."

msgid "page.profile.created_time"
msgstr "Perfil criado <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listas"

msgid "page.profile.lists.no_lists"
msgstr "Nenhuma lista ainda"

msgid "page.profile.lists.new_list"
msgstr "Crie uma nova lista escolhendo um arquivo e abrindo a aba \"Listas\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "A reforma dos direitos autorais é necessária para a segurança nacional"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Resumindo: LLMs chineses (incluindo DeepSeek) são treinados no meu arquivo ilegal de livros e artigos — o maior do mundo. O Ocidente precisa reformular a lei de direitos autorais como uma questão de segurança nacional."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artigos complementares do TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Não muito tempo atrás, as “bibliotecas-sombra” estavam morrendo. O Sci-Hub, o enorme arquivo ilegal de artigos acadêmicos, havia parado de receber novas obras, devido a processos judiciais. A “Z-Library”, a maior biblioteca ilegal de livros, viu seus supostos criadores serem presos por acusações criminais de direitos autorais. Eles incrivelmente conseguiram escapar de sua prisão, mas sua biblioteca não está menos ameaçada."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quando a Z-Library enfrentou o fechamento, eu já havia feito backup de toda a sua biblioteca e estava procurando uma plataforma para abrigá-la. Essa foi minha motivação para iniciar o Acervo da Anna: uma continuação da missão por trás dessas iniciativas anteriores. Desde então, crescemos para ser a maior biblioteca-sombra do mundo, hospedando mais de 140 milhões de textos protegidos por direitos autorais em vários formatos — livros, artigos acadêmicos, revistas, jornais e além."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Minha equipe e eu somos ideólogos. Acreditamos que preservar e hospedar esses arquivos é moralmente correto. Bibliotecas ao redor do mundo estão vendo cortes de financiamento, e também não podemos confiar o patrimônio da humanidade às corporações."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Então veio a IA. Praticamente todas as grandes empresas que constroem LLMs nos contataram para treinar com nossos dados. A maioria (mas não todas!) das empresas sediadas nos EUA reconsiderou uma vez que perceberam a natureza ilegal do nosso trabalho. Em contraste, empresas chinesas abraçaram entusiasticamente nossa coleção, aparentemente despreocupadas com sua legalidade. Isso é notável, dado o papel da China como signatária de quase todos os principais tratados internacionais de direitos autorais."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Concedemos acesso de alta velocidade a cerca de 30 empresas. A maioria delas são empresas de LLM, e algumas são corretoras de dados, que revenderão nossa coleção. A maioria é chinesa, embora também tenhamos trabalhado com empresas dos EUA, Europa, Rússia, Coreia do Sul e Japão. A DeepSeek <a %(arxiv)s>admitiu</a> que uma versão anterior foi treinada com parte de nossa coleção, embora eles sejam discretos sobre seu modelo mais recente (provavelmente também treinado com nossos dados)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se o Ocidente quiser se manter à frente na corrida dos LLMs e, em última análise, da AGI, precisa reconsiderar sua posição sobre direitos autorais, e logo. Quer você concorde ou não conosco sobre nosso caso moral, isso agora está se tornando um caso de economia, e até mesmo de segurança nacional. Todos os blocos de poder estão construindo super-cientistas artificiais, super-hackers e super-militares. A liberdade de informação está se tornando uma questão de sobrevivência para esses países — até mesmo uma questão de segurança nacional."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Nossa equipe é de todo o mundo, e não temos um alinhamento particular. Mas encorajamos países com leis de direitos autorais rígidas a usarem essa ameaça existencial para reformá-las. Então, o que fazer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Nossa primeira recomendação é simples: encurtar o prazo de direitos autorais. Nos EUA, os direitos autorais são concedidos por 70 anos após a morte do autor. Isso é absurdo. Podemos alinhar isso com patentes, que são concedidas por 20 anos após o depósito. Isso deve ser mais do que tempo suficiente para que autores de livros, artigos, música, arte e outras obras criativas sejam totalmente compensados por seus esforços (incluindo projetos de longo prazo, como adaptações cinematográficas)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Então, no mínimo, os formuladores de políticas deveriam incluir exceções para a preservação em massa e disseminação de textos. Se a perda de receita de clientes individuais for a principal preocupação, a distribuição em nível pessoal poderia permanecer proibida. Em contrapartida, aqueles capazes de gerenciar vastos repositórios — empresas que treinam LLMs, juntamente com bibliotecas e outros arquivos — seriam cobertos por essas exceções."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Alguns países já estão fazendo uma versão disso. O TorrentFreak <a %(torrentfreak)s>relatou</a> que China e Japão introduziram exceções de IA em suas leis de direitos autorais. Não está claro para nós como isso interage com tratados internacionais, mas certamente dá cobertura para suas empresas domésticas, o que explica o que temos visto."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quanto ao Acervo da Anna — continuaremos nosso trabalho subterrâneo enraizado em convicções morais. No entanto, nosso maior desejo é entrar na luz e amplificar nosso impacto legalmente. Por favor, reformem os direitos autorais."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leia os artigos complementares do TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vencedores da recompensa de $10.000 pela visualização de ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Resumo: Recebemos algumas submissões incríveis para a recompensa de $10.000 pela visualização de ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Há alguns meses, anunciamos uma <a %(all_isbns)s>recompensa de $10.000</a> para fazer a melhor visualização possível dos nossos dados mostrando o espaço de ISBN. Enfatizamos mostrar quais arquivos já arquivamos/não arquivamos, e mais tarde um conjunto de dados descrevendo quantas bibliotecas possuem ISBNs (uma medida de raridade)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Ficamos impressionados com a resposta. Houve tanta criatividade. Um grande obrigado a todos que participaram: sua energia e entusiasmo são contagiantes!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "No final, queríamos responder às seguintes perguntas: <strong>quais livros existem no mundo, quantos já arquivamos e em quais livros devemos focar a seguir?</strong> É ótimo ver tantas pessoas se importando com essas questões."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Começamos com uma visualização básica por nós mesmos. Em menos de 300kb, esta imagem representa sucintamente a maior \"lista de livros\" totalmente aberta já montada na história da humanidade:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Todos os ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Arquivos no Acervo da Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNOs do CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Vazamento de dados do CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSIDs do DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Índice de eBooks do EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Livros"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registro Global de Editores ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Estatal Russa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Voltar"

#, fuzzy
msgid "common.forward"
msgstr "Avançar"

#, fuzzy
msgid "common.last"
msgstr "Último"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Por favor, veja a <a %(all_isbns)s>postagem original do blog</a> para mais informações."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Lançamos um desafio para melhorar isso. Ofereceríamos uma recompensa de $6.000 para o primeiro lugar, $3.000 para o segundo lugar e $1.000 para o terceiro lugar. Devido à resposta esmagadora e às incríveis submissões, decidimos aumentar ligeiramente o prêmio e conceder um terceiro lugar dividido entre quatro participantes, com $500 cada. Os vencedores estão abaixo, mas não deixe de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou baixe nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primeiro lugar $6.000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Esta <a %(phiresky_github)s>submissão</a> (<a %(annas_archive_note_2951)s>comentário no Gitlab</a>) é simplesmente tudo o que queríamos, e mais! Gostamos especialmente das opções de visualização incrivelmente flexíveis (até mesmo suportando shaders personalizados), mas com uma lista abrangente de predefinições. Também gostamos de como tudo é rápido e suave, da implementação simples (que nem sequer tem um backend), do minimapa inteligente e da explicação extensa em seu <a %(phiresky_github)s>post no blog</a>. Trabalho incrível, e o vencedor merecido!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Segundo lugar $3.000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Outra <a %(annas_archive_note_2913)s>submissão</a> incrível. Não tão flexível quanto o primeiro lugar, mas na verdade preferimos sua visualização em nível macro em relação ao primeiro lugar (curva de preenchimento de espaço, bordas, rotulagem, destaque, panorâmica e zoom). Um <a %(annas_archive_note_2971)s>comentário</a> de Joe Davis ressoou conosco:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Embora quadrados e retângulos perfeitos sejam matematicamente agradáveis, eles não oferecem superioridade de localidade em um contexto de mapeamento. Acredito que a assimetria inerente a esses Hilbert ou Morton clássico não é uma falha, mas uma característica. Assim como o famoso contorno em forma de bota da Itália a torna instantaneamente reconhecível em um mapa, as \"peculiaridades\" únicas dessas curvas podem servir como marcos cognitivos. Essa distinção pode melhorar a memória espacial e ajudar os usuários a se orientarem, potencialmente facilitando a localização de regiões específicas ou a percepção de padrões.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ainda muitas opções para visualização e renderização, além de uma interface incrivelmente suave e intuitiva. Um sólido segundo lugar!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terceiro lugar $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Nesta <a %(annas_archive_note_2940)s>submissão</a> realmente gostamos dos diferentes tipos de visualizações, em particular as visualizações de comparação e de editoras."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terceiro lugar $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Embora não tenha a interface mais polida, esta <a %(annas_archive_note_2917)s>submissão</a> atende a muitos critérios. Gostamos particularmente de sua funcionalidade de comparação."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terceiro lugar $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Como o primeiro lugar, esta <a %(annas_archive_note_2975)s>submissão</a> nos impressionou com sua flexibilidade. Em última análise, é isso que faz um ótimo ferramenta de visualização: flexibilidade máxima para usuários avançados, mantendo as coisas simples para usuários comuns."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terceiro lugar $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "A última <a %(annas_archive_note_2947)s>submissão</a> a receber uma recompensa é bastante básica, mas tem algumas características únicas que realmente gostamos. Gostamos de como eles mostram quantos datasets cobrem um determinado ISBN como uma medida de popularidade/confiabilidade. Também gostamos muito da simplicidade, mas eficácia, de usar um controle deslizante de opacidade para comparações."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ideias notáveis"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Algumas ideias e implementações adicionais que gostamos particularmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Arranha-céus para raridade"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Estatísticas ao vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotações, e também estatísticas ao vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Visualização de mapa única e filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Esquema de cores padrão e mapa de calor legais."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Alternância fácil de datasets para comparações rápidas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Rótulos bonitos."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de escala com número de livros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Muitos controles deslizantes para comparar datasets, como se você fosse um DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Poderíamos continuar por um tempo, mas vamos parar por aqui. Certifique-se de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou baixe nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Tantas submissões, e cada uma traz uma perspectiva única, seja na interface ou na implementação."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Incorporaremos pelo menos a submissão do primeiro lugar em nosso site principal, e talvez algumas outras. Também começamos a pensar em como organizar o processo de identificar, confirmar e, em seguida, arquivar os livros mais raros. Mais novidades virão nessa área."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Obrigado a todos que participaram. É incrível que tantas pessoas se importem."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nossos corações estão cheios de gratidão."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizando Todos os ISBNs — recompensa de $10.000 até 31-01-2025"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Esta imagem representa a maior “lista de livros” totalmente aberta já montada na história da humanidade."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Esta imagem tem 1000×800 pixels. Cada pixel representa 2.500 ISBNs. Se temos um arquivo para um ISBN, tornamos esse pixel mais verde. Se sabemos que um ISBN foi emitido, mas não temos um arquivo correspondente, tornamos ele mais vermelho."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Em menos de 300kb, esta imagem representa sucintamente a maior “lista de livros” totalmente aberta já montada na história da humanidade (alguns centenas de GB comprimidos na íntegra)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ela também mostra: há muito trabalho a ser feito no backup de livros (temos apenas 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Contexto"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Como o Acervo da Anna pode alcançar sua missão de fazer backup de todo o conhecimento da humanidade, sem saber quais livros ainda estão por aí? Precisamos de uma lista de tarefas. Uma maneira de mapear isso é através dos números ISBN, que desde os anos 1970 foram atribuídos a cada livro publicado (na maioria dos países)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Não há uma autoridade central que conheça todas as atribuições de ISBN. Em vez disso, é um sistema distribuído, onde os países recebem intervalos de números, que então atribuem intervalos menores a grandes editoras, que podem subdividir ainda mais os intervalos para editoras menores. Finalmente, números individuais são atribuídos aos livros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Começamos a mapear ISBNs <a %(blog)s>há dois anos</a> com nossa coleta do ISBNdb. Desde então, coletamos muitas outras fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby e mais. Uma lista completa pode ser encontrada nas páginas “Datasets” e “Torrents” no Acervo da Anna. Agora temos, de longe, a maior coleção totalmente aberta e facilmente baixável de metadata de livros (e, portanto, ISBNs) do mundo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Nós <a %(blog)s>escrevemos extensivamente</a> sobre por que nos importamos com a preservação e por que estamos atualmente em uma janela crítica. Devemos agora identificar livros raros, pouco focados e exclusivamente em risco e preservá-los. Ter bons metadata de todos os livros do mundo ajuda nisso."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizando"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Além da imagem geral, também podemos olhar para os datasets individuais que adquirimos. Use o menu suspenso e os botões para alternar entre eles."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Há muitos padrões interessantes para ver nessas imagens. Por que há alguma regularidade de linhas e blocos, que parece acontecer em diferentes escalas? Quais são as áreas vazias? Por que certos datasets são tão agrupados? Deixaremos essas perguntas como um exercício para o leitor."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensa de $10.000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Há muito a explorar aqui, então estamos anunciando uma recompensa para melhorar a visualização acima. Ao contrário da maioria de nossas recompensas, esta tem um prazo. Você deve <a %(annas_archive)s>enviar</a> seu código de código aberto até 31-01-2025 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "A melhor submissão receberá $6.000, o segundo lugar $3.000 e o terceiro lugar $1.000. Todas as recompensas serão concedidas usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Abaixo estão os critérios mínimos. Se nenhuma submissão atender aos critérios, ainda podemos conceder algumas recompensas, mas isso será a nosso critério."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Faça um fork deste repositório e edite este HTML de postagem de blog (nenhum outro backend além do nosso backend Flask é permitido)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faça com que a imagem acima seja suavemente ampliável, para que você possa dar zoom até os ISBNs individuais. Clicar nos ISBNs deve levá-lo a uma página de metadata ou pesquisa no Acervo da Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Você ainda deve ser capaz de alternar entre todos os diferentes datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Intervalos de países e editoras devem ser destacados ao passar o mouse. Você pode usar, por exemplo, <a %(github_xlcnd_isbnlib)s>data4info.py no isbnlib</a> para informações de países, e nossa coleta “isbngrp” para editoras (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Deve funcionar bem em desktop e mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Para pontos extras (estas são apenas ideias — deixe sua criatividade fluir):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Será dada uma consideração especial à usabilidade e à estética."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mostrar metadados reais para ISBNs individuais ao dar zoom, como título e autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Melhor curva de preenchimento de espaço. Por exemplo, um zigue-zague, indo de 0 a 4 na primeira linha e depois voltando (em reverso) de 5 a 9 na segunda linha — aplicado recursivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Esquemas de cores diferentes ou personalizáveis."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vistas especiais para comparar Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Maneiras de depurar problemas, como outros metadados que não concordam bem (por exemplo, títulos muito diferentes)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotar imagens com comentários sobre ISBNs ou intervalos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualquer heurística para identificar livros raros ou em risco."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Quaisquer ideias criativas que você possa ter!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Você PODE se desviar completamente dos critérios mínimos e fazer uma visualização completamente diferente. Se for realmente espetacular, isso qualifica para a recompensa, mas a nosso critério."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Faça envios postando um comentário em <a %(annas_archive)s>este problema</a> com um link para seu repositório bifurcado, solicitação de mesclagem ou diferença."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Código"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "O código para gerar essas imagens, bem como outros exemplos, pode ser encontrado neste <a %(annas_archive)s>diretório</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Criamos um formato de dados compacto, com o qual todas as informações necessárias do ISBN ocupam cerca de 75MB (comprimido). A descrição do formato de dados e o código para gerá-lo podem ser encontrados <a %(annas_archive_l1244_1319)s>aqui</a>. Para a recompensa, você não é obrigado a usar isso, mas provavelmente é o formato mais conveniente para começar. Você pode transformar nosso metadata como quiser (embora todo o seu código deva ser de código aberto)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Mal podemos esperar para ver o que você vai criar. Boa sorte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contêineres do Acervo da Anna (AAC): padronizando lançamentos da maior biblioteca-sombra do mundo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "O Acervo da Anna se tornou a maior biblioteca-sombra do mundo, exigindo que padronizemos nossos lançamentos."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Acervo da Anna</a> se tornou de longe a maior biblioteca-sombra do mundo, e a única biblioteca-sombra de sua escala que é totalmente de código aberto e dados abertos. Abaixo está uma tabela da nossa página de Datasets (ligeiramente modificada):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Conseguimos isso de três maneiras:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Espelhando bibliotecas-sombra de dados abertos existentes (como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Ajudando bibliotecas-sombra que querem ser mais abertas, mas não tinham tempo ou recursos para isso (como a coleção de quadrinhos do Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspando bibliotecas que não desejam compartilhar em massa (como a Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Para (2) e (3) agora gerenciamos uma coleção considerável de torrents nós mesmos (centenas de TBs). Até agora, abordamos essas coleções como casos únicos, significando infraestrutura e organização de dados sob medida para cada coleção. Isso adiciona uma sobrecarga significativa a cada lançamento e torna particularmente difícil fazer lançamentos mais incrementais."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "É por isso que decidimos padronizar nossos lançamentos. Este é um post técnico no blog no qual estamos introduzindo nosso padrão: <strong>Contêineres do Acervo da Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Objetivos de design"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Nosso principal caso de uso é a distribuição de arquivos e metadata associada de diferentes coleções existentes. Nossas considerações mais importantes são:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Arquivos e metadata heterogêneos, o mais próximo possível do formato original."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificadores heterogêneos nas bibliotecas de origem, ou até mesmo a ausência de identificadores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Lançamentos separados de metadata versus dados de arquivos, ou lançamentos apenas de metadata (por exemplo, nosso lançamento ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuição através de torrents, embora com a possibilidade de outros métodos de distribuição (por exemplo, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registros imutáveis, já que devemos assumir que nossos torrents viverão para sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Lançamentos incrementais / lançamentos adicionáveis."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Legível e gravável por máquinas, de forma conveniente e rápida, especialmente para nossa pilha (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspeção humana relativamente fácil, embora isso seja secundário à legibilidade por máquinas."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Fácil de semear nossas coleções com um seedbox padrão alugado."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dados binários podem ser servidos diretamente por servidores web como Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Alguns não-objetivos:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Não nos importamos que os arquivos sejam fáceis de navegar manualmente no disco, ou pesquisáveis sem pré-processamento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Não nos importamos em ser diretamente compatíveis com software de biblioteca existente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Embora deva ser fácil para qualquer um semear nossa coleção usando torrents, não esperamos que os arquivos sejam utilizáveis sem conhecimento técnico significativo e comprometimento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Como o Acervo da Anna é de código aberto, queremos usar nosso formato diretamente. Quando atualizamos nosso índice de busca, acessamos apenas caminhos publicamente disponíveis, para que qualquer um que faça um fork de nossa biblioteca possa começar rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "O padrão"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Por fim, optamos por um padrão relativamente simples. É bastante flexível, não normativo e está em desenvolvimento."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Container do Acervo da Anna) é um único item que consiste em <strong>metadata</strong>, e opcionalmente <strong>dados binários</strong>, ambos imutáveis. Ele possui um identificador globalmente único, chamado <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Coleção.</strong> Cada AAC pertence a uma coleção, que por definição é uma lista de AACs que são semanticamente consistentes. Isso significa que se você fizer uma mudança significativa no formato dos metadata, então terá que criar uma nova coleção."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Coleções de “registros” e “arquivos”.</strong> Por convenção, é frequentemente conveniente lançar “registros” e “arquivos” como coleções diferentes, para que possam ser lançados em cronogramas diferentes, por exemplo, com base nas taxas de scraping. Um “registro” é uma coleção apenas de metadata, contendo informações como títulos de livros, autores, ISBNs, etc., enquanto “arquivos” são as coleções que contêm os arquivos reais (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> O formato do AACID é este: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por exemplo, um AACID real que lançamos é <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: o nome da coleção, que pode conter letras ASCII, números e sublinhados (mas sem sublinhados duplos)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: uma versão curta do ISO 8601, sempre em UTC, por exemplo, <code>20220723T194746Z</code>. Este número deve aumentar monotonamente a cada lançamento, embora seu significado exato possa diferir por coleção. Sugerimos usar o horário da extração ou da geração do ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: um identificador específico da coleção, se aplicável, por exemplo, o ID da Z-Library. Pode ser omitido ou truncado. Deve ser omitido ou truncado se o AACID exceder 150 caracteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: um UUID, mas comprimido para ASCII, por exemplo, usando base57. Atualmente usamos a biblioteca Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Faixa de AACID.</strong> Como os AACIDs contêm timestamps que aumentam monotonamente, podemos usá-los para denotar intervalos dentro de uma coleção específica. Usamos este formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, onde os timestamps são inclusivos. Isso é consistente com a notação ISO 8601. Os intervalos são contínuos e podem se sobrepor, mas em caso de sobreposição devem conter registros idênticos aos lançados anteriormente naquela coleção (já que os AACs são imutáveis). Registros ausentes não são permitidos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Arquivo de metadata.</strong> Um arquivo de metadata contém a metadata de um intervalo de AACs, para uma coleção específica. Estes têm as seguintes propriedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "O nome do arquivo deve ser uma faixa de AACID, prefixada com <code style=\"color: red\">annas_archive_meta__</code> e seguida por <code>.jsonl.zstd</code>. Por exemplo, um de nossos lançamentos é chamado<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Como indicado pela extensão do arquivo, o tipo de arquivo é <a %(jsonlines)s>JSON Lines</a> comprimido com <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada objeto JSON deve conter os seguintes campos no nível superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Nenhum outro campo é permitido."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> é metadata arbitrária, de acordo com a semântica da coleção. Deve ser semanticamente consistente dentro da coleção."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> é opcional e é o nome da pasta de dados binários que contém os dados binários correspondentes. O nome do arquivo dos dados binários correspondentes dentro dessa pasta é o AACID do registro."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "O prefixo <code style=\"color: red\">annas_archive_meta__</code> pode ser adaptado para o nome da sua instituição, por exemplo, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Pasta de dados binários.</strong> Uma pasta com os dados binários de um intervalo de AACs, para uma coleção específica. Estas têm as seguintes propriedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "O nome do diretório deve ser uma faixa de AACID, prefixada com <code style=\"color: green\">annas_archive_data__</code>, e sem sufixo. Por exemplo, um de nossos lançamentos reais tem um diretório chamado<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "O diretório deve conter arquivos de dados para todos os AACs dentro do intervalo especificado. Cada arquivo de dados deve ter seu AACID como nome do arquivo (sem extensões)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Recomenda-se que essas pastas sejam gerenciáveis em tamanho, por exemplo, não maiores que 100GB-1TB cada, embora essa recomendação possa mudar ao longo do tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Os arquivos de metadata e as pastas de dados binários podem ser agrupados em torrents, com um torrent por arquivo de metadata ou um torrent por pasta de dados binários. Os torrents devem ter o nome original do arquivo/diretório mais um sufixo <code>.torrent</code> como seu nome de arquivo."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Vamos olhar para nosso recente lançamento da Z-Library como exemplo. Ele consiste em duas coleções: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Isso nos permite raspar e liberar separadamente registros de metadata dos arquivos de livros reais. Assim, lançamos dois torrents com arquivos de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Também lançamos um monte de torrents com pastas de dados binários, mas apenas para a coleção “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 no total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Executando <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver o que está dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Neste caso, são metadados de um livro conforme relatado pela Z-Library. No nível superior, temos apenas “aacid” e “metadata”, mas nenhum “data_folder”, já que não há dados binários correspondentes. O AACID contém “22430000” como ID principal, que podemos ver é retirado de “zlibrary_id”. Podemos esperar que outros AACs nesta coleção tenham a mesma estrutura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Agora vamos executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Este é um metadata AAC muito menor, embora a maior parte deste AAC esteja localizada em outro lugar em um arquivo binário! Afinal, temos um “data_folder” desta vez, então podemos esperar que os dados binários correspondentes estejam localizados em <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. O “metadata” contém o “zlibrary_id”, então podemos facilmente associá-lo ao AAC correspondente na coleção “zlib_records”. Poderíamos ter associado de várias maneiras diferentes, por exemplo, através do AACID — o padrão não prescreve isso."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Observe que também não é necessário que o campo \"metadata\" seja em si JSON. Ele pode ser uma string contendo XML ou qualquer outro formato de dados. Você pode até armazenar informações de metadata no blob binário associado, por exemplo, se for uma grande quantidade de dados."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusão"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Com este padrão, podemos fazer lançamentos de forma mais incremental e adicionar novas fontes de dados com mais facilidade. Já temos alguns lançamentos empolgantes em andamento!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Também esperamos que se torne mais fácil para outras bibliotecas-sombra espelharem nossas coleções. Afinal, nosso objetivo é preservar o conhecimento e a cultura humana para sempre, então quanto mais redundância, melhor."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Atualização da Anna: acervo totalmente open source, ElasticSearch, mais de 300GB de capas de livros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Temos trabalhado incansavelmente para oferecer uma boa alternativa com o Acervo da Anna. Aqui estão algumas das coisas que alcançamos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Com o Z-Library saindo do ar e seus (supostos) fundadores sendo presos, temos trabalhado incansavelmente para oferecer uma boa alternativa com o Acervo da Anna (não vamos colocar o link aqui, mas você pode procurar no Google). Aqui estão algumas das coisas que alcançamos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "O Acervo da Anna é totalmente open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Acreditamos que a informação deve ser livre, e nosso próprio código não é exceção. Liberamos todo o nosso código em nossa instância do Gitlab hospedada privadamente: <a %(annas_archive)s>Software da Anna</a>. Também usamos o rastreador de problemas para organizar nosso trabalho. Se você quiser se envolver com nosso desenvolvimento, este é um ótimo lugar para começar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Para dar uma ideia das coisas em que estamos trabalhando, veja nosso trabalho recente em melhorias de desempenho no lado do cliente. Como ainda não implementamos a paginação, frequentemente retornávamos páginas de busca muito longas, com 100-200 resultados. Não queríamos cortar os resultados da busca muito cedo, mas isso significava que poderia desacelerar alguns dispositivos. Para isso, implementamos um pequeno truque: envolvemos a maioria dos resultados da busca em comentários HTML (<code><!-- --></code>), e então escrevemos um pequeno Javascript que detectaria quando um resultado deveria se tornar visível, momento em que removeríamos o comentário:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "\"Virtualização\" do DOM implementada em 23 linhas, sem necessidade de bibliotecas sofisticadas! Este é o tipo de código pragmático rápido que você acaba criando quando tem tempo limitado e problemas reais que precisam ser resolvidos. Foi relatado que nossa busca agora funciona bem em dispositivos lentos!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Outro grande esforço foi automatizar a construção do banco de dados. Quando lançamos, simplesmente juntamos diferentes fontes de forma desordenada. Agora queremos mantê-las atualizadas, então escrevemos vários scripts para baixar novos metadata dos dois forks do Library Genesis e integrá-los. O objetivo não é apenas tornar isso útil para nosso acervo, mas facilitar para qualquer pessoa que queira explorar metadata de bibliotecas-sombra. O objetivo seria um notebook Jupyter que tenha todos os tipos de metadata interessantes disponíveis, para que possamos fazer mais pesquisas, como descobrir qual <a %(blog)s>percentual de ISBNs é preservado para sempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, reformulamos nosso sistema de doações. Agora você pode usar um cartão de crédito para depositar dinheiro diretamente em nossas carteiras de criptomoedas, sem realmente precisar saber nada sobre criptomoedas. Continuaremos monitorando como isso funciona na prática, mas é um grande avanço."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Mudança para ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Um dos nossos <a %(annas_archive)s>tickets</a> era um conjunto de problemas com nosso sistema de busca. Usávamos a busca de texto completo do MySQL, já que tínhamos todos os nossos dados no MySQL de qualquer forma. Mas tinha seus limites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Algumas consultas demoravam muito, a ponto de monopolizarem todas as conexões abertas."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Por padrão, o MySQL tem um comprimento mínimo de palavra, ou seu índice pode ficar realmente grande. As pessoas relataram não conseguir buscar por \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "A busca era apenas um pouco rápida quando totalmente carregada na memória, o que exigia que adquiríssemos uma máquina mais cara para rodar isso, além de alguns comandos para pré-carregar o índice na inicialização."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Não teríamos sido capazes de estendê-la facilmente para construir novos recursos, como melhor <a %(wikipedia_cjk_characters)s>tokenização para idiomas sem espaços</a>, filtragem/facetação, ordenação, sugestões de \"você quis dizer\", autocompletar, e assim por diante."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Depois de conversar com vários especialistas, optamos pelo ElasticSearch. Não tem sido perfeito (as sugestões padrão de “você quis dizer” e os recursos de autocompletar são ruins), mas no geral tem sido muito melhor que o MySQL para busca. Ainda não estamos <a %(youtube)s>muito entusiasmados</a> em usá-lo para qualquer dado crítico (embora tenham feito muitos <a %(elastic_co)s>progressos</a>), mas no geral estamos bastante satisfeitos com a mudança."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por enquanto, implementamos uma busca muito mais rápida, melhor suporte a idiomas, melhor ordenação por relevância, diferentes opções de ordenação e filtragem por idioma/tipo de livro/tipo de arquivo. Se você está curioso sobre como funciona, <a %(annas_archive_l140)s>dê</a> <a %(annas_archive_l1115)s>uma</a> <a %(annas_archive_l1635)s>olhada</a>. É bastante acessível, embora pudesse ter mais comentários…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Mais de 300GB de capas de livros liberadas"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, estamos felizes em anunciar um pequeno lançamento. Em colaboração com o pessoal que opera o fork Libgen.rs, estamos compartilhando todas as capas de livros deles através de torrents e IPFS. Isso distribuirá a carga de visualização das capas entre mais máquinas e as preservará melhor. Em muitos (mas não todos) casos, as capas dos livros estão incluídas nos próprios arquivos, então isso é uma espécie de \"dados derivados\". Mas tê-las no IPFS ainda é muito útil para a operação diária tanto do Acervo da Anna quanto dos vários forks do Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costume, você pode encontrar este lançamento no Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>). Não vamos linkar aqui, mas você pode encontrá-lo facilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Esperamos poder relaxar um pouco nosso ritmo, agora que temos uma alternativa decente ao Z-Library. Esta carga de trabalho não é particularmente sustentável. Se você estiver interessado em ajudar com programação, operações de servidor ou trabalho de preservação, entre em contato conosco. Ainda há muito <a %(annas_archive)s>trabalho a ser feito</a>. Obrigado pelo seu interesse e apoio."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "O Acervo da Anna fez backup da maior biblioteca-sombra de quadrinhos do mundo (95TB) — você pode ajudar a semeá-la"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "A maior biblioteca-sombra de quadrinhos do mundo tinha um único ponto de falha... até hoje."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discutir no Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "A maior biblioteca-sombra de quadrinhos é provavelmente a de um fork específico do Library Genesis: o Libgen.li. O único administrador que gerencia esse site conseguiu reunir uma coleção insana de quadrinhos com mais de 2 milhões de arquivos, totalizando mais de 95TB. No entanto, ao contrário de outras coleções do Library Genesis, esta não estava disponível em massa através de torrents. Você só podia acessar esses quadrinhos individualmente através de seu servidor pessoal lento — um único ponto de falha. Até hoje!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Neste post, vamos contar mais sobre essa coleção e sobre nossa campanha de arrecadação de fundos para apoiar mais esse trabalho."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dra. Barbara Gordon tenta se perder no mundo mundano da biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Forks do Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primeiro, um pouco de contexto. Você pode conhecer o Library Genesis por sua épica coleção de livros. Menos pessoas sabem que os voluntários do Library Genesis criaram outros projetos, como uma coleção considerável de revistas e documentos padrão, um backup completo do Sci-Hub (em colaboração com a fundadora do Sci-Hub, Alexandra Elbakyan) e, de fato, uma enorme coleção de quadrinhos."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Em algum momento, diferentes operadores de espelhos do Library Genesis seguiram caminhos separados, o que deu origem à situação atual de ter vários \"forks\" diferentes, todos ainda carregando o nome Library Genesis. O fork Libgen.li possui exclusivamente essa coleção de quadrinhos, além de uma coleção considerável de revistas (na qual também estamos trabalhando)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaboração"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada a sua dimensão, essa coleção há muito tempo está na nossa lista de desejos, então, após nosso sucesso com o backup do Z-Library, miramos nessa coleção. No início, fizemos a raspagem diretamente, o que foi um grande desafio, já que o servidor deles não estava nas melhores condições. Conseguimos cerca de 15TB dessa forma, mas foi um processo lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Felizmente, conseguimos entrar em contato com o operador da biblioteca, que concordou em nos enviar todos os dados diretamente, o que foi muito mais rápido. Ainda assim, levou mais de meio ano para transferir e processar todos os dados, e quase perdemos tudo devido à corrupção de disco, o que significaria começar tudo de novo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Essa experiência nos fez acreditar que é importante divulgar esses dados o mais rápido possível, para que possam ser espelhados amplamente. Estamos a apenas um ou dois incidentes de azar de perder essa coleção para sempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "A coleção"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Mover-se rapidamente significa que a coleção está um pouco desorganizada... Vamos dar uma olhada. Imagine que temos um sistema de arquivos (que na realidade estamos dividindo em torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "O primeiro diretório, <code>/repository</code>, é a parte mais estruturada disso. Este diretório contém os chamados \"milhares de dirs\": diretórios, cada um com milhares de arquivos, que são numerados incrementalmente no banco de dados. O diretório <code>0</code> contém arquivos com comic_id de 0 a 999, e assim por diante."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Este é o mesmo esquema que a Library Genesis tem usado para suas coleções de ficção e não-ficção. A ideia é que cada \"milhar de dir\" seja automaticamente transformado em um torrent assim que estiver cheio."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "No entanto, o operador do Libgen.li nunca fez torrents para esta coleção, e assim os milhares de dirs provavelmente se tornaram inconvenientes, dando lugar aos \"dirs não classificados\". Estes são <code>/comics0</code> até <code>/comics4</code>. Todos eles contêm estruturas de diretórios únicas, que provavelmente faziam sentido para coletar os arquivos, mas não fazem muito sentido para nós agora. Felizmente, o metadata ainda se refere diretamente a todos esses arquivos, então a organização de armazenamento no disco não importa realmente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "O metadata está disponível na forma de um banco de dados MySQL. Isso pode ser baixado diretamente do site do Libgen.li, mas também o disponibilizaremos em um torrent, juntamente com nossa própria tabela com todos os hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Análise"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quando você recebe 95TB despejados em seu cluster de armazenamento, tenta entender o que há ali... Fizemos algumas análises para ver se poderíamos reduzir um pouco o tamanho, como removendo duplicatas. Aqui estão algumas de nossas descobertas:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplicatas semânticas (diferentes digitalizações do mesmo livro) podem teoricamente ser filtradas, mas é complicado. Ao olhar manualmente através dos quadrinhos, encontramos muitos falsos positivos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Existem algumas duplicatas apenas por MD5, o que é relativamente desperdiçador, mas filtrá-las nos daria apenas cerca de 1% in de economia. Nesta escala, isso ainda é cerca de 1TB, mas também, nesta escala, 1TB não importa muito. Preferimos não arriscar destruir dados acidentalmente nesse processo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Encontramos um monte de dados não relacionados a livros, como filmes baseados em quadrinhos. Isso também parece desperdício, já que esses já estão amplamente disponíveis por outros meios. No entanto, percebemos que não poderíamos simplesmente filtrar arquivos de filmes, já que também existem <em>quadrinhos interativos</em> que foram lançados no computador, que alguém gravou e salvou como filmes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "No final, qualquer coisa que pudéssemos deletar da coleção economizaria apenas alguns por cento. Então lembramos que somos acumuladores de dados, e as pessoas que irão espelhar isso também são acumuladores de dados, então, \"O QUE VOCÊ QUER DIZER COM DELETAR?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Portanto, estamos apresentando a você a coleção completa e não modificada. É uma grande quantidade de dados, mas esperamos que pessoas suficientes se importem em semeá-la de qualquer maneira."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Arrecadação de fundos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Estamos lançando esses dados em alguns grandes blocos. O primeiro torrent é de <code>/comics0</code>, que colocamos em um enorme arquivo .tar de 12TB. Isso é melhor para seu disco rígido e software de torrent do que um milhão de arquivos menores."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte deste lançamento, estamos fazendo uma arrecadação de fundos. Estamos buscando arrecadar $20.000 para cobrir custos operacionais e de contratação para esta coleção, bem como possibilitar projetos contínuos e futuros. Temos alguns <em>enormes</em> em andamento."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Quem estou apoiando com minha doação?</em> Em resumo: estamos fazendo backup de todo o conhecimento e cultura da humanidade e tornando-os facilmente acessíveis. Todo o nosso código e dados são de código aberto, somos um projeto totalmente gerido por voluntários e já salvamos 125TB de livros até agora (além dos torrents já existentes do Libgen e Scihub). No final, estamos construindo um volante que permite e incentiva as pessoas a encontrar, digitalizar e fazer backup de todos os livros do mundo. Escreveremos sobre nosso plano mestre em um post futuro. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se você doar para uma assinatura de 12 meses como \"Arquivista Admirável\" (R$ 780), você poderá <strong>“adotar um torrent”</strong>, o que significa que colocaremos seu nome de usuário ou mensagem no nome de um dos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Você pode doar acessando o <a %(wikipedia_annas_archive)s>Acervo da Anna</a> e clicando no botão “Doar”. Também estamos procurando mais voluntários: engenheiros de software, pesquisadores de segurança, especialistas em comércio anônimo e tradutores. Você também pode nos apoiar fornecendo serviços de hospedagem. E, claro, por favor, semeie nossos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Obrigado a todos que já nos apoiaram tão generosamente! Vocês estão realmente fazendo a diferença."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Aqui estão os torrents lançados até agora (ainda estamos processando o restante):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Todos os torrents podem ser encontrados no <a %(wikipedia_annas_archive)s>Acervo da Anna</a> em “Datasets” (não vinculamos diretamente, para que os links para este blog não sejam removidos do Reddit, Twitter, etc). A partir daí, siga o link para o site Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "O que vem a seguir?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Um monte de torrents são ótimos para preservação a longo prazo, mas não tanto para acesso diário. Estaremos trabalhando com parceiros de hospedagem para colocar todos esses dados na web (já que o Acervo da Anna não hospeda nada diretamente). Claro que você poderá encontrar esses links de download no Acervo da Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Também estamos convidando todos a fazer algo com esses dados! Ajude-nos a analisá-los melhor, desduplicá-los, colocá-los no IPFS, remixá-los, treinar seus modelos de IA com eles, e assim por diante. Tudo é seu, e mal podemos esperar para ver o que você fará com isso."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como dito antes, ainda temos alguns lançamentos massivos por vir (se <em>alguém</em> pudesse <em>acidentalmente</em> nos enviar um dump de um <em>certo</em> banco de dados ACS4, você sabe onde nos encontrar...), além de construir o volante para fazer backup de todos os livros do mundo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Então fique ligado, estamos apenas começando."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novos livros adicionados ao Espelho da Biblioteca Pirata (+24TB, 3,8 milhões de livros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "No lançamento original do Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>), fizemos um espelho do Z-Library, uma grande coleção de livros ilegais. Como lembrete, isso é o que escrevemos naquele post original do blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library é uma biblioteca popular (e ilegal). Eles pegaram a coleção do Library Genesis e a tornaram facilmente pesquisável. Além disso, eles se tornaram muito eficazes em solicitar novas contribuições de livros, incentivando usuários contribuintes com várias vantagens. Atualmente, eles não contribuem com esses novos livros de volta para o Library Genesis. E, ao contrário do Library Genesis, eles não tornam sua coleção facilmente espelhável, o que impede a preservação ampla. Isso é importante para o modelo de negócios deles, já que cobram dinheiro pelo acesso em massa à sua coleção (mais de 10 livros por dia)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegais. É inegável que o Z-Library tem sido bem-sucedido em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer nossa parte: garantir a preservação a longo prazo dessa coleção privada."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Essa coleção data de meados de 2021. Nesse meio tempo, o Z-Library tem crescido a uma taxa impressionante: eles adicionaram cerca de 3,8 milhões de novos livros. Há alguns duplicados lá, claro, mas a maioria parece ser de livros realmente novos ou de digitalizações de maior qualidade de livros previamente submetidos. Isso se deve em grande parte ao aumento do número de moderadores voluntários no Z-Library e ao seu sistema de upload em massa com desduplicação. Gostaríamos de parabenizá-los por essas conquistas."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Estamos felizes em anunciar que conseguimos todos os livros que foram adicionados ao Z-Library entre nosso último espelho e agosto de 2022. Também voltamos e raspamos alguns livros que perdemos na primeira vez. No total, essa nova coleção tem cerca de 24TB, que é muito maior que a última (7TB). Nosso espelho agora tem 31TB no total. Novamente, desduplicamos contra o Library Genesis, já que já existem torrents disponíveis para essa coleção."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Por favor, vá ao Espelho da Biblioteca Pirata para conferir a nova coleção (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>). Há mais informações lá sobre como os arquivos estão estruturados e o que mudou desde a última vez. Não vamos vincular a partir daqui, já que este é apenas um site de blog que não hospeda nenhum material ilegal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Claro, fazer seed também é uma ótima maneira de nos ajudar. Agradecemos a todos que estão fazendo seed do nosso conjunto anterior de torrents. Estamos gratos pela resposta positiva e felizes que há tantas pessoas que se importam com a preservação do conhecimento e da cultura de uma maneira tão incomum."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Como se tornar um arquivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antes de começarmos, duas atualizações sobre o Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Recebemos algumas doações extremamente generosas. A primeira foi de $10 mil de um indivíduo anônimo que também tem apoiado \"bookwarrior\", o fundador original do Library Genesis. Agradecimentos especiais a bookwarrior por facilitar esta doação. A segunda foi outra doação de $10 mil de um doador anônimo, que entrou em contato após nosso último lançamento e foi inspirado a ajudar. Também recebemos várias doações menores. Muito obrigado por todo o seu generoso apoio. Temos alguns novos projetos empolgantes em andamento que isso irá apoiar, então fiquem atentos."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Tivemos algumas dificuldades técnicas com o tamanho do nosso segundo lançamento, mas nossos torrents estão ativos e semeando agora. Também recebemos uma oferta generosa de um indivíduo anônimo para semear nossa coleção em seus servidores de altíssima velocidade, então estamos fazendo um upload especial para suas máquinas, após o qual todos os outros que estão baixando a coleção devem ver uma grande melhoria na velocidade."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Livros inteiros podem ser escritos sobre o <em>porquê</em> da preservação digital em geral, e do arquivismo pirata em particular, mas vamos dar uma breve introdução para aqueles que não estão muito familiarizados. O mundo está produzindo mais conhecimento e cultura do que nunca, mas também mais disso está sendo perdido do que nunca. A humanidade confia amplamente em corporações como editoras acadêmicas, serviços de streaming e empresas de redes sociais para cuidar desse patrimônio, e muitas vezes elas não se mostraram grandes guardiãs. Confira o documentário Digital Amnesia, ou qualquer palestra de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Existem algumas instituições que fazem um bom trabalho arquivando o máximo que podem, mas elas estão limitadas pela lei. Como piratas, estamos em uma posição única para arquivar coleções que elas não podem tocar, devido à aplicação de direitos autorais ou outras restrições. Também podemos espelhar coleções muitas vezes, ao redor do mundo, aumentando assim as chances de preservação adequada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por enquanto, não entraremos em discussões sobre os prós e contras da propriedade intelectual, a moralidade de quebrar a lei, reflexões sobre censura ou a questão do acesso ao conhecimento e à cultura. Com tudo isso fora do caminho, vamos mergulhar no <em>como</em>. Vamos compartilhar como nossa equipe se tornou arquivistas piratas e as lições que aprendemos ao longo do caminho. Existem muitos desafios quando você embarca nesta jornada, e esperamos poder ajudá-lo em alguns deles."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunidade"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico: fazer este trabalho nas sombras pode ser incrivelmente solitário. Dependendo do que você está planejando fazer e do seu modelo de ameaça, você pode ter que ser muito cuidadoso. Em uma extremidade do espectro, temos pessoas como Alexandra Elbakyan*, a fundadora do Sci-Hub, que é muito aberta sobre suas atividades. Mas ela corre alto risco de ser presa se visitar um país ocidental neste momento, e pode enfrentar décadas de prisão. É um risco que você estaria disposto a correr? Estamos na outra extremidade do espectro; sendo muito cuidadosos para não deixar nenhum rastro e tendo uma forte segurança operacional."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como mencionado no HN por \"ynno\", Alexandra inicialmente não queria ser conhecida: \"Seus servidores foram configurados para emitir mensagens de erro detalhadas do PHP, incluindo o caminho completo do arquivo fonte com falha, que estava sob o diretório /home/<USER>" Portanto, use nomes de usuário aleatórios nos computadores que você usa para essas coisas, caso você configure algo incorretamente."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Esse sigilo, no entanto, vem com um custo psicológico. A maioria das pessoas adora ser reconhecida pelo trabalho que faz, e ainda assim você não pode receber nenhum crédito por isso na vida real. Até mesmo coisas simples podem ser desafiadoras, como amigos perguntando o que você tem feito (em algum momento \"mexendo com meu NAS / homelab\" fica velho)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "É por isso que é tão importante encontrar alguma comunidade. Você pode abrir mão de um pouco de segurança operacional confiando em alguns amigos muito próximos, que você sabe que pode confiar profundamente. Mesmo assim, tenha cuidado para não colocar nada por escrito, caso eles tenham que entregar seus e-mails às autoridades, ou se seus dispositivos forem comprometidos de alguma outra forma."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Melhor ainda é encontrar alguns colegas piratas. Se seus amigos próximos estiverem interessados em se juntar a você, ótimo! Caso contrário, você pode encontrar outros online. Infelizmente, esta ainda é uma comunidade de nicho. Até agora, encontramos apenas um punhado de outros que estão ativos neste espaço. Bons pontos de partida parecem ser os fóruns do Library Genesis e o r/DataHoarder. A Archive Team também tem indivíduos com ideias semelhantes, embora operem dentro da lei (mesmo que em algumas áreas cinzentas da lei). As cenas tradicionais de \"warez\" e pirataria também têm pessoas que pensam de maneira semelhante."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Estamos abertos a ideias sobre como fomentar a comunidade e explorar ideias. Sinta-se à vontade para nos enviar uma mensagem no Twitter ou Reddit. Talvez possamos hospedar algum tipo de fórum ou grupo de bate-papo. Um desafio é que isso pode ser facilmente censurado ao usar plataformas comuns, então teríamos que hospedar nós mesmos. Há também um equilíbrio entre ter essas discussões totalmente públicas (mais potencial de engajamento) versus torná-las privadas (não deixar \"alvos\" potenciais saberem que estamos prestes a espelhá-los). Teremos que pensar sobre isso. Deixe-nos saber se você está interessado nisso!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projetos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quando fazemos um projeto, ele tem algumas fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Seleção de domínio / filosofia: Onde você quer se concentrar aproximadamente e por quê? Quais são suas paixões, habilidades e circunstâncias únicas que você pode usar a seu favor?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Seleção de alvo: Qual coleção específica você irá espelhar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raspagem de metadata: Catalogação de informações sobre os arquivos, sem realmente baixar os arquivos (geralmente muito maiores) em si."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Seleção de dados: Com base na metadata, restringir quais dados são mais relevantes para arquivar agora. Pode ser tudo, mas muitas vezes há uma maneira razoável de economizar espaço e largura de banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raspagem de dados: Realmente obter os dados."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuição: Empacotar em torrents, anunciar em algum lugar, fazer com que as pessoas espalhem."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Essas não são fases completamente independentes, e muitas vezes percepções de uma fase posterior te levam de volta a uma fase anterior. Por exemplo, durante a raspagem de metadata, você pode perceber que o alvo que selecionou tem mecanismos de defesa além do seu nível de habilidade (como bloqueios de IP), então você volta e encontra um alvo diferente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Seleção de domínio / filosofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Não falta conhecimento e patrimônio cultural a serem salvos, o que pode ser avassalador. É por isso que muitas vezes é útil parar um momento e pensar sobre qual pode ser sua contribuição."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Cada um tem uma maneira diferente de pensar sobre isso, mas aqui estão algumas perguntas que você poderia se fazer:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Por que você está interessado nisso? Pelo que você é apaixonado? Se conseguirmos um grupo de pessoas que arquivem os tipos de coisas que elas especificamente se importam, isso cobriria muito! Você saberá muito mais do que a pessoa média sobre sua paixão, como quais são os dados importantes a serem salvos, quais são as melhores coleções e comunidades online, e assim por diante."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quais habilidades você tem que pode usar a seu favor? Por exemplo, se você é um especialista em segurança online, pode encontrar maneiras de derrotar bloqueios de IP para alvos seguros. Se você é ótimo em organizar comunidades, então talvez possa reunir algumas pessoas em torno de um objetivo. É útil saber um pouco de programação, mesmo que apenas para manter uma boa segurança operacional durante todo esse processo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quanto tempo você tem para isso? Nosso conselho seria começar pequeno e fazer projetos maiores à medida que você se acostuma, mas pode se tornar tudo consumindo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Qual seria uma área de alto impacto para se concentrar? Se você vai gastar X horas em arquivamento pirata, então como você pode obter o maior \"retorno sobre o investimento\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quais são as maneiras únicas que você está pensando sobre isso? Você pode ter algumas ideias ou abordagens interessantes que outros podem ter perdido."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "No nosso caso, nos importávamos particularmente com a preservação a longo prazo da ciência. Sabíamos sobre o Library Genesis, e como ele foi totalmente espelhado muitas vezes usando torrents. Adoramos essa ideia. Então, um dia, um de nós tentou encontrar alguns livros didáticos científicos no Library Genesis, mas não conseguiu encontrá-los, colocando em dúvida o quão completo ele realmente era. Então procuramos esses livros didáticos online e os encontramos em outros lugares, o que plantou a semente para o nosso projeto. Mesmo antes de sabermos sobre o Z-Library, tivemos a ideia de não tentar coletar todos esses livros manualmente, mas de nos concentrar em espelhar coleções existentes e contribuir com elas de volta para o Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Seleção de alvo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Então, temos nossa área que estamos analisando, agora qual coleção específica devemos espelhar? Existem algumas coisas que fazem um bom alvo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Único: não já bem coberto por outros projetos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Acessível: não usa toneladas de camadas de proteção para impedir que você raspe seus metadata e dados."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Insight especial: você tem alguma informação especial sobre este alvo, como de alguma forma tem acesso especial a esta coleção, ou descobriu como derrotar suas defesas. Isso não é necessário (nosso próximo projeto não faz nada especial), mas certamente ajuda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quando encontramos nossos livros didáticos de ciência em sites diferentes do Library Genesis, tentamos descobrir como eles chegaram à internet. Então encontramos o Z-Library e percebemos que, embora a maioria dos livros não apareça primeiro lá, eles acabam chegando lá. Aprendemos sobre sua relação com o Library Genesis e a estrutura de incentivos (financeiros) e a interface de usuário superior, ambas as quais tornaram-no uma coleção muito mais completa. Em seguida, fizemos algumas raspagens preliminares de metadata e dados, e percebemos que poderíamos contornar seus limites de download de IP, aproveitando o acesso especial de um de nossos membros a muitos servidores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Enquanto você explora diferentes alvos, já é importante esconder seus rastros usando VPNs e endereços de e-mail descartáveis, sobre os quais falaremos mais tarde."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Raspagem de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Vamos ser um pouco mais técnicos aqui. Para realmente raspar o metadata de sites, mantivemos as coisas bem simples. Usamos scripts Python, às vezes curl, e um banco de dados MySQL para armazenar os resultados. Não usamos nenhum software de raspagem sofisticado que possa mapear sites complexos, já que até agora só precisávamos raspar um ou dois tipos de páginas apenas enumerando através de ids e analisando o HTML. Se não houver páginas facilmente enumeradas, então você pode precisar de um rastreador adequado que tente encontrar todas as páginas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antes de começar a raspar um site inteiro, tente fazer isso manualmente por um tempo. Passe por algumas dezenas de páginas você mesmo, para ter uma noção de como isso funciona. Às vezes, você já encontrará bloqueios de IP ou outros comportamentos interessantes dessa forma. O mesmo vale para a raspagem de dados: antes de se aprofundar muito nesse alvo, certifique-se de que pode realmente baixar seus dados de forma eficaz."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Para contornar restrições, há algumas coisas que você pode tentar. Existem outros endereços IP ou servidores que hospedam os mesmos dados, mas não têm as mesmas restrições? Existem endpoints de API que não têm restrições, enquanto outros têm? Em que taxa de download seu IP é bloqueado e por quanto tempo? Ou você não é bloqueado, mas tem a velocidade reduzida? E se você criar uma conta de usuário, como as coisas mudam então? Você pode usar HTTP/2 para manter as conexões abertas, e isso aumenta a taxa com que você pode solicitar páginas? Existem páginas que listam vários arquivos de uma vez, e as informações listadas lá são suficientes?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Coisas que você provavelmente quer salvar incluem:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Título"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome do arquivo / localização"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pode ser algum ID interno, mas IDs como ISBN ou DOI também são úteis."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Tamanho: para calcular quanto espaço em disco você precisa."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): para confirmar que você baixou o arquivo corretamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data de adição/modificação: para que você possa voltar mais tarde e baixar arquivos que não baixou antes (embora você também possa usar o ID ou hash para isso)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrição, categoria, tags, autores, idioma, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Normalmente fazemos isso em duas etapas. Primeiro, baixamos os arquivos HTML brutos, geralmente diretamente para o MySQL (para evitar muitos arquivos pequenos, sobre os quais falamos mais abaixo). Depois, em uma etapa separada, passamos por esses arquivos HTML e os analisamos em tabelas MySQL reais. Dessa forma, você não precisa rebaixar tudo do zero se descobrir um erro no seu código de análise, já que pode apenas reprocessar os arquivos HTML com o novo código. Também é frequentemente mais fácil paralelizar a etapa de processamento, economizando assim algum tempo (e você pode escrever o código de processamento enquanto a raspagem está em execução, em vez de ter que escrever ambas as etapas de uma vez)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, observe que para alguns alvos a raspagem de metadata é tudo o que há. Existem algumas coleções enormes de metadata por aí que não estão devidamente preservadas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Seleção de dados"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Frequentemente, você pode usar a metadata para determinar um subconjunto razoável de dados para baixar. Mesmo que você eventualmente queira baixar todos os dados, pode ser útil priorizar os itens mais importantes primeiro, caso você seja detectado e as defesas sejam melhoradas, ou porque você precisaria comprar mais discos, ou simplesmente porque algo mais surge em sua vida antes que você possa baixar tudo."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Por exemplo, uma coleção pode ter várias edições do mesmo recurso subjacente (como um livro ou um filme), onde uma é marcada como sendo de melhor qualidade. Salvar essas edições primeiro faria muito sentido. Você pode eventualmente querer salvar todas as edições, já que em alguns casos a metadata pode estar incorretamente etiquetada, ou pode haver compensações desconhecidas entre edições (por exemplo, a \"melhor edição\" pode ser a melhor em muitos aspectos, mas pior em outros, como um filme ter uma resolução mais alta, mas faltar legendas)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Você também pode pesquisar seu banco de dados de metadata para encontrar coisas interessantes. Qual é o maior arquivo hospedado e por que é tão grande? Qual é o menor arquivo? Existem padrões interessantes ou inesperados quando se trata de certas categorias, idiomas, e assim por diante? Existem títulos duplicados ou muito semelhantes? Existem padrões de quando os dados foram adicionados, como um dia em que muitos arquivos foram adicionados de uma vez? Você pode frequentemente aprender muito observando o conjunto de dados de diferentes maneiras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "No nosso caso, deduplicamos os livros da Z-Library contra os hashes md5 na Library Genesis, economizando assim muito tempo de download e espaço em disco. Esta é uma situação bastante única, no entanto. Na maioria dos casos, não existem bancos de dados abrangentes de quais arquivos já estão devidamente preservados por colegas piratas. Isso em si é uma grande oportunidade para alguém por aí. Seria ótimo ter uma visão geral regularmente atualizada de coisas como músicas e filmes que já estão amplamente semeados em sites de torrent, e são, portanto, de menor prioridade para incluir em espelhos piratas."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Raspagem de dados"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Agora você está pronto para realmente baixar os dados em massa. Como mencionado antes, neste ponto você já deve ter baixado manualmente um monte de arquivos, para entender melhor o comportamento e as restrições do alvo. No entanto, ainda haverá surpresas reservadas para você quando realmente começar a baixar muitos arquivos de uma vez."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nosso conselho aqui é principalmente manter a simplicidade. Comece apenas baixando um monte de arquivos. Você pode usar Python e, em seguida, expandir para múltiplas threads. Mas às vezes é ainda mais simples gerar arquivos Bash diretamente do banco de dados e, em seguida, executar vários deles em várias janelas de terminal para escalar. Um truque técnico rápido que vale a pena mencionar aqui é usar OUTFILE no MySQL, que você pode escrever em qualquer lugar se desativar \"secure_file_priv\" no mysqld.cnf (e certifique-se de também desativar/substituir o AppArmor se estiver no Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Armazenamos os dados em discos rígidos simples. Comece com o que você tem e expanda lentamente. Pode ser assustador pensar em armazenar centenas de TBs de dados. Se essa é a situação que você está enfrentando, coloque primeiro um bom subconjunto e, no seu anúncio, peça ajuda para armazenar o restante. Se você quiser adquirir mais discos rígidos, o r/DataHoarder tem alguns bons recursos para conseguir bons negócios."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Tente não se preocupar muito com sistemas de arquivos sofisticados. É fácil cair na armadilha de configurar coisas como ZFS. Um detalhe técnico a ser observado, no entanto, é que muitos sistemas de arquivos não lidam bem com muitos arquivos. Descobrimos que uma solução simples é criar múltiplos diretórios, por exemplo, para diferentes intervalos de ID ou prefixos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Após baixar os dados, certifique-se de verificar a integridade dos arquivos usando hashes no metadata, se disponível."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuição"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Você tem os dados, o que lhe dá posse do primeiro espelho pirata do mundo do seu alvo (provavelmente). De muitas maneiras, a parte mais difícil já passou, mas a parte mais arriscada ainda está à sua frente. Afinal, até agora você foi furtivo; voando sob o radar. Tudo o que você precisava fazer era usar um bom VPN o tempo todo, não preencher seus dados pessoais em nenhum formulário (óbvio), e talvez usar uma sessão de navegador especial (ou até mesmo um computador diferente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Agora você tem que distribuir os dados. No nosso caso, primeiro queríamos contribuir com os livros de volta para o Library Genesis, mas rapidamente descobrimos as dificuldades nisso (classificação de ficção vs não-ficção). Então decidimos pela distribuição usando torrents no estilo Library Genesis. Se você tiver a oportunidade de contribuir para um projeto existente, isso pode economizar muito tempo. No entanto, atualmente não há muitos espelhos piratas bem organizados por aí."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Então, digamos que você decida distribuir torrents por conta própria. Tente manter esses arquivos pequenos, para que sejam fáceis de espelhar em outros sites. Você terá que semear os torrents por conta própria, enquanto ainda permanece anônimo. Você pode usar uma VPN (com ou sem encaminhamento de porta) ou pagar com Bitcoins misturados por um Seedbox. Se você não sabe o que alguns desses termos significam, terá muito o que ler, pois é importante que você entenda as compensações de risco aqui."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Você pode hospedar os arquivos torrent em sites de torrent existentes. No nosso caso, escolhemos realmente hospedar um site, já que também queríamos espalhar nossa filosofia de forma clara. Você pode fazer isso por conta própria de maneira semelhante (usamos Njalla para nossos domínios e hospedagem, pagos com Bitcoins misturados), mas também sinta-se à vontade para nos contatar para que possamos hospedar seus torrents. Estamos procurando construir um índice abrangente de espelhos piratas ao longo do tempo, se essa ideia pegar."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Quanto à seleção de VPN, já foi escrito muito sobre isso, então vamos apenas repetir o conselho geral de escolher pela reputação. Políticas de não registro testadas em tribunal com longos históricos de proteção à privacidade são a opção de menor risco, na nossa opinião. Note que mesmo quando você faz tudo certo, nunca pode chegar a zero risco. Por exemplo, ao semear seus torrents, um ator estatal altamente motivado provavelmente pode observar os fluxos de dados de entrada e saída dos servidores VPN e deduzir quem você é. Ou você pode simplesmente cometer um erro de alguma forma. Provavelmente já cometemos, e cometeremos novamente. Felizmente, os estados-nação não se importam <em>tanto</em> com pirataria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Uma decisão a ser tomada para cada projeto é se publicá-lo usando a mesma identidade de antes ou não. Se você continuar usando o mesmo nome, então erros na segurança operacional de projetos anteriores podem voltar para te prejudicar. Mas publicar sob nomes diferentes significa que você não constrói uma reputação duradoura. Escolhemos ter uma forte segurança operacional desde o início para que possamos continuar usando a mesma identidade, mas não hesitaremos em publicar sob um nome diferente se cometermos um erro ou se as circunstâncias exigirem."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Divulgar a palavra pode ser complicado. Como dissemos, esta ainda é uma comunidade de nicho. Originalmente postamos no Reddit, mas realmente ganhamos tração no Hacker News. Por enquanto, nossa recomendação é postar em alguns lugares e ver o que acontece. E novamente, entre em contato conosco. Adoraríamos espalhar a palavra sobre mais esforços de arquivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusão"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Esperamos que isso seja útil para novos arquivistas piratas iniciantes. Estamos animados para recebê-lo neste mundo, então não hesite em entrar em contato. Vamos preservar o máximo possível do conhecimento e cultura do mundo, e espelhá-lo amplamente."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Apresentando o Espelho da Biblioteca Pirata: Preservando 7TB de livros (que não estão no Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Este projeto (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>) visa contribuir para a preservação e libertação do conhecimento humano. Fazemos nossa pequena e humilde contribuição, nos passos dos grandes que vieram antes de nós."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "O foco deste projeto é ilustrado pelo seu nome:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Deliberadamente violamos a lei de direitos autorais na maioria dos países. Isso nos permite fazer algo que entidades legais não podem fazer: garantir que os livros sejam espelhados amplamente."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Como a maioria das bibliotecas, focamos principalmente em materiais escritos como livros. Podemos expandir para outros tipos de mídia no futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Espelho</strong> - Somos estritamente um espelho de bibliotecas existentes. Focamos na preservação, não em tornar os livros facilmente pesquisáveis e baixáveis (acesso) ou em fomentar uma grande comunidade de pessoas que contribuem com novos livros (fonte)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "A primeira biblioteca que espelhamos é a Z-Library. Esta é uma biblioteca popular (e ilegal). Eles pegaram a coleção do Library Genesis e a tornaram facilmente pesquisável. Além disso, eles se tornaram muito eficazes em solicitar novas contribuições de livros, incentivando usuários contribuintes com várias vantagens. Atualmente, eles não contribuem com esses novos livros de volta para o Library Genesis. E, ao contrário do Library Genesis, eles não tornam sua coleção facilmente espelhável, o que impede a preservação ampla. Isso é importante para o modelo de negócios deles, já que cobram dinheiro pelo acesso à coleção em massa (mais de 10 livros por dia)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegais. É inegável que o Z-Library tem sido bem-sucedido em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer nossa parte: garantir a preservação a longo prazo dessa coleção privada."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Gostaríamos de convidá-lo a ajudar a preservar e liberar o conhecimento humano baixando e semeando nossos torrents. Veja a página do projeto para mais informações sobre como os dados estão organizados."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Também gostaríamos muito de convidá-lo a contribuir com suas ideias sobre quais coleções espelhar a seguir e como fazê-lo. Juntos podemos alcançar muito. Esta é apenas uma pequena contribuição entre inúmeras outras. Obrigado, por tudo o que você faz."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Não vinculamos os arquivos deste blog. Por favor, encontre-os você mesmo.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump do ISBNdb, ou Quantos Livros Estão Preservados para Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se fôssemos deduplicar adequadamente os arquivos das bibliotecas-sombra, que porcentagem de todos os livros do mundo teríamos preservado?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Com o Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>), nosso objetivo é pegar todos os livros do mundo e preservá-los para sempre.<sup>1</sup> Entre nossos torrents do Z-Library e os torrents originais do Library Genesis, temos 11.783.153 arquivos. Mas quantos são realmente? Se deduplicássemos adequadamente esses arquivos, que porcentagem de todos os livros do mundo teríamos preservado? Gostaríamos muito de ter algo assim:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of do patrimônio escrito da humanidade preservado para sempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Para uma porcentagem, precisamos de um denominador: o número total de livros já publicados.<sup>2</sup> Antes do fim do Google Books, um engenheiro do projeto, Leonid Taycher, <a %(booksearch_blogspot)s>tentou estimar</a> esse número. Ele chegou — em tom de brincadeira — a 129.864.880 (“pelo menos até domingo”). Ele estimou esse número construindo um banco de dados unificado de todos os livros do mundo. Para isso, ele reuniu diferentes datasets e os mesclou de várias maneiras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como uma rápida observação, há outra pessoa que tentou catalogar todos os livros do mundo: Aaron Swartz, o falecido ativista digital e cofundador do Reddit.<sup>3</sup> Ele <a %(youtube)s>iniciou a Open Library</a> com o objetivo de “uma página da web para cada livro já publicado”, combinando dados de várias fontes diferentes. Ele acabou pagando o preço máximo por seu trabalho de preservação digital quando foi processado por baixar em massa artigos acadêmicos, levando ao seu suicídio. Nem é preciso dizer que esta é uma das razões pelas quais nosso grupo é pseudônimo e por que estamos sendo muito cuidadosos. A Open Library ainda está sendo heroicamente administrada por pessoas do Internet Archive, continuando o legado de Aaron. Voltaremos a isso mais tarde neste post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "No post do blog do Google, Taycher descreve alguns dos desafios ao estimar esse número. Primeiro, o que constitui um livro? Existem algumas definições possíveis:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Cópias físicas.</strong> Obviamente, isso não é muito útil, já que são apenas duplicatas do mesmo material. Seria interessante se pudéssemos preservar todas as anotações que as pessoas fazem nos livros, como os famosos “rabiscos nas margens” de Fermat. Mas, infelizmente, isso permanecerá como um sonho de arquivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obras”.</strong> Por exemplo, “Harry Potter e a Câmara Secreta” como um conceito lógico, englobando todas as suas versões, como diferentes traduções e reimpressões. Esta é uma definição meio útil, mas pode ser difícil traçar a linha do que conta. Por exemplo, provavelmente queremos preservar diferentes traduções, embora reimpressões com apenas pequenas diferenças possam não ser tão importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edições”.</strong> Aqui você conta cada versão única de um livro. Se algo sobre ele for diferente, como uma capa diferente ou um prefácio diferente, conta como uma edição diferente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Arquivos.</strong> Ao trabalhar com bibliotecas-sombra como Library Genesis, Sci-Hub ou Z-Library, há uma consideração adicional. Pode haver várias digitalizações da mesma edição. E as pessoas podem criar versões melhores de arquivos existentes, digitalizando o texto usando OCR ou corrigindo páginas que foram digitalizadas em ângulo. Queremos contar esses arquivos como uma única edição, o que exigiria uma boa metadata ou deduplicação usando medidas de similaridade de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edições” parecem ser a definição mais prática do que são “livros”. Convenientemente, essa definição também é usada para atribuir números ISBN únicos. Um ISBN, ou Número Padrão Internacional de Livro, é comumente usado para comércio internacional, pois está integrado ao sistema internacional de código de barras (“Número de Artigo Internacional”). Se você quiser vender um livro em lojas, ele precisa de um código de barras, então você obtém um ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "O post do blog de Taycher menciona que, embora os ISBNs sejam úteis, eles não são universais, já que foram realmente adotados apenas em meados dos anos setenta, e não em todo o mundo. Ainda assim, o ISBN é provavelmente o identificador mais amplamente usado para edições de livros, então é nosso melhor ponto de partida. Se pudermos encontrar todos os ISBNs do mundo, teremos uma lista útil de quais livros ainda precisam ser preservados."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Então, onde conseguimos os dados? Existem vários esforços existentes que estão tentando compilar uma lista de todos os livros do mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Afinal, eles fizeram essa pesquisa para o Google Books. No entanto, sua metadata não é acessível em massa e é bastante difícil de extrair."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como mencionado antes, esta é toda a sua missão. Eles obtiveram enormes quantidades de dados de bibliotecas de bibliotecas cooperantes e arquivos nacionais, e continuam a fazê-lo. Eles também têm bibliotecários voluntários e uma equipe técnica que está tentando deduplicar registros e marcá-los com todos os tipos de metadata. O melhor de tudo, seu conjunto de dados é completamente aberto. Você pode simplesmente <a %(openlibrary)s>baixá-lo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Este é um site administrado pela organização sem fins lucrativos OCLC, que vende sistemas de gerenciamento de bibliotecas. Eles agregam metadata de livros de várias bibliotecas e a disponibilizam através do site WorldCat. No entanto, eles também ganham dinheiro vendendo esses dados, então não estão disponíveis para download em massa. Eles têm alguns conjuntos de dados em massa mais limitados disponíveis para download, em cooperação com bibliotecas específicas."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este é o tema deste post do blog. O ISBNdb extrai dados de vários sites para metadata de livros, em particular dados de preços, que eles então vendem para livreiros, para que possam precificar seus livros de acordo com o restante do mercado. Como os ISBNs são bastante universais hoje em dia, eles efetivamente construíram uma “página da web para cada livro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Vários sistemas de bibliotecas individuais e arquivos.</strong> Existem bibliotecas e arquivos que não foram indexados e agregados por nenhum dos mencionados acima, muitas vezes porque são subfinanciados ou, por outras razões, não querem compartilhar seus dados com a Open Library, OCLC, Google, e assim por diante. Muitos desses têm registros digitais acessíveis pela internet, e muitas vezes não são muito bem protegidos, então, se você quiser ajudar e se divertir aprendendo sobre sistemas de bibliotecas estranhos, esses são ótimos pontos de partida."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Neste post, estamos felizes em anunciar um pequeno lançamento (comparado aos nossos lançamentos anteriores do Z-Library). Extraímos a maior parte do ISBNdb e disponibilizamos os dados para torrent no site do Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>; não vamos linkar diretamente aqui, apenas procure por ele). São cerca de 30,9 milhões de registros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4,4GB compactados). No site deles, afirmam que na verdade têm 32,6 milhões de registros, então talvez tenhamos perdido alguns, ou <em>eles</em> podem estar fazendo algo errado. Em qualquer caso, por enquanto não compartilharemos exatamente como fizemos isso — deixaremos isso como um exercício para o leitor. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "O que vamos compartilhar é uma análise preliminar, para tentar chegar mais perto de estimar o número de livros no mundo. Analisamos três datasets: este novo dataset do ISBNdb, nossa versão original de metadata que extraímos da biblioteca-sombra Z-Library (que inclui o Library Genesis) e o dump de dados da Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Vamos começar com alguns números aproximados:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Em ambos Z-Library/Libgen e Open Library há muitos mais livros do que ISBNs únicos. Isso significa que muitos desses livros não têm ISBNs, ou a metadata do ISBN está simplesmente faltando? Provavelmente podemos responder a essa pergunta com uma combinação de correspondência automatizada baseada em outros atributos (título, autor, editor, etc.), trazendo mais fontes de dados e extraindo ISBNs das próprias digitalizações dos livros (no caso de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quantos desses ISBNs são únicos? Isso é melhor ilustrado com um diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Para ser mais preciso:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Ficamos surpresos com o quão pouco sobreposição existe! O ISBNdb tem uma enorme quantidade de ISBNs que não aparecem nem no Z-Library nem na Open Library, e o mesmo vale (em menor grau, mas ainda substancial) para os outros dois. Isso levanta muitas novas questões. Quanto a correspondência automatizada ajudaria a marcar os livros que não foram marcados com ISBNs? Haveria muitas correspondências e, portanto, aumento da sobreposição? Além disso, o que aconteceria se trouxermos um 4º ou 5º conjunto de dados? Quanta sobreposição veríamos então?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Isso nos dá um ponto de partida. Agora podemos olhar para todos os ISBNs que não estavam no conjunto de dados do Z-Library e que não correspondem aos campos de título/autor também. Isso pode nos dar uma ideia de como preservar todos os livros do mundo: primeiro extraindo a internet por digitalizações, depois saindo na vida real para digitalizar livros. Este último poderia até ser financiado coletivamente ou impulsionado por “recompensas” de pessoas que gostariam de ver livros específicos digitalizados. Tudo isso é uma história para outro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se você quiser ajudar com qualquer uma dessas atividades — análise adicional; extração de mais metadata; encontrar mais livros; realizar OCR em livros; fazer isso para outros domínios (por exemplo, artigos, audiolivros, filmes, programas de TV, revistas) ou até mesmo disponibilizar alguns desses dados para coisas como treinamento de ML / modelos de linguagem de grande porte — por favor, entre em contato comigo (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se você está especificamente interessado na análise de dados, estamos trabalhando para disponibilizar nossos datasets e scripts em um formato mais fácil de usar. Seria ótimo se você pudesse simplesmente fazer um fork de um notebook e começar a explorar isso."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, se você quiser apoiar este trabalho, por favor, considere fazer uma doação. Esta é uma operação totalmente gerida por voluntários, e sua contribuição faz uma enorme diferença. Toda ajuda conta. Por enquanto, aceitamos doações em criptomoedas; veja a página de Doações no Acervo da Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Para alguma definição razoável de \"para sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Claro, o patrimônio escrito da humanidade é muito mais do que livros, especialmente nos dias de hoje. Para o propósito deste post e nossos lançamentos recentes, estamos focando em livros, mas nossos interesses vão além."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Há muito mais a ser dito sobre Aaron Swartz, mas queríamos apenas mencioná-lo brevemente, já que ele desempenha um papel crucial nesta história. Com o passar do tempo, mais pessoas podem se deparar com seu nome pela primeira vez e, posteriormente, mergulhar no assunto por conta própria."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "A janela crítica das bibliotecas-sombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Como podemos afirmar que preservamos nossas coleções para sempre, quando elas já estão se aproximando de 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versão em Chinês 中文版</a>, discuta no <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "No Acervo da Anna, frequentemente nos perguntam como podemos afirmar que preservamos nossas coleções para sempre, quando o tamanho total já está se aproximando de 1 Petabyte (1000 TB) e continua crescendo. Neste artigo, vamos examinar nossa filosofia e ver por que a próxima década é crítica para nossa missão de preservar o conhecimento e a cultura da humanidade."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "O <a %(annas_archive_stats)s>tamanho total</a> de nossas coleções, nos últimos meses, dividido pelo número de seeders de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioridades"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Por que nos importamos tanto com artigos e livros? Vamos deixar de lado nossa crença fundamental na preservação em geral — podemos escrever outro post sobre isso. Então, por que especificamente artigos e livros? A resposta é simples: <strong>densidade de informação</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabyte de armazenamento, o texto escrito armazena a maior quantidade de informação de todos os meios. Embora nos importemos tanto com conhecimento quanto com cultura, nos importamos mais com o primeiro. No geral, encontramos uma hierarquia de densidade de informação e importância da preservação que se parece mais ou menos com isto:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Artigos acadêmicos, periódicos, relatórios"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dados orgânicos como sequências de DNA, sementes de plantas ou amostras microbianas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Livros de não-ficção"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Código de software de ciência e engenharia"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dados de medição como medições científicas, dados econômicos, relatórios corporativos"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sites de ciência e engenharia, discussões online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistas de não-ficção, jornais, manuais"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcrições de não-ficção de palestras, documentários, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dados internos de corporações ou governos (vazamentos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Registros de metadata em geral (de não-ficção e ficção; de outros meios, arte, pessoas, etc.; incluindo resenhas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dados geográficos (por exemplo, mapas, levantamentos geológicos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcrições de processos legais ou judiciais"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versões ficcionais ou de entretenimento de tudo o que foi mencionado acima"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "A classificação nesta lista é um tanto arbitrária — vários itens estão empatados ou têm discordâncias dentro de nossa equipe — e provavelmente estamos esquecendo algumas categorias importantes. Mas é mais ou menos assim que priorizamos."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Alguns desses itens são muito diferentes dos outros para nos preocuparmos (ou já são cuidados por outras instituições), como dados orgânicos ou dados geográficos. Mas a maioria dos itens desta lista é realmente importante para nós."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Outro grande fator em nossa priorização é o quanto uma determinada obra está em risco. Preferimos focar em obras que são:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Raras"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamente negligenciadas"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamente em risco de destruição (por exemplo, por guerra, cortes de financiamento, processos judiciais ou perseguição política)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, nos importamos com a escala. Temos tempo e dinheiro limitados, então preferimos gastar um mês salvando 10.000 livros do que 1.000 livros — se eles forem igualmente valiosos e estiverem em risco."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliotecas-sombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Existem muitas organizações que têm missões e prioridades semelhantes. De fato, há bibliotecas, arquivos, laboratórios, museus e outras instituições encarregadas da preservação desse tipo. Muitas delas são bem financiadas, por governos, indivíduos ou corporações. Mas elas têm um ponto cego enorme: o sistema legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Aqui reside o papel único das bibliotecas-sombra e a razão pela qual o Acervo da Anna existe. Podemos fazer coisas que outras instituições não têm permissão para fazer. Agora, não é (frequentemente) que podemos arquivar materiais que são ilegais de preservar em outros lugares. Não, é legal em muitos lugares construir um arquivo com quaisquer livros, artigos, revistas, e assim por diante."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Mas o que os arquivos legais muitas vezes carecem é de <strong>redundância e longevidade</strong>. Existem livros dos quais apenas uma cópia existe em alguma biblioteca física em algum lugar. Existem registros de metadata guardados por uma única corporação. Existem jornais preservados apenas em microfilme em um único arquivo. Bibliotecas podem sofrer cortes de financiamento, corporações podem falir, arquivos podem ser bombardeados e queimados até o chão. Isso não é hipotético — isso acontece o tempo todo."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "A coisa que podemos fazer de forma única no Acervo da Anna é armazenar muitas cópias de obras, em grande escala. Podemos coletar artigos, livros, revistas e mais, e distribuí-los em massa. Atualmente, fazemos isso através de torrents, mas as tecnologias exatas não importam e mudarão com o tempo. A parte importante é distribuir muitas cópias pelo mundo. Esta citação de mais de 200 anos atrás ainda soa verdadeira:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>O que está perdido não pode ser recuperado; mas vamos salvar o que resta: não por cofres e fechaduras que os afastam do olhar e uso do público, consignando-os ao desperdício do tempo, mas por uma multiplicação de cópias, que os coloque além do alcance de acidentes.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Uma nota rápida sobre domínio público. Como o Acervo da Anna foca exclusivamente em atividades que são ilegais em muitos lugares ao redor do mundo, não nos preocupamos com coleções amplamente disponíveis, como livros de domínio público. Entidades legais geralmente já cuidam bem disso. No entanto, há considerações que às vezes nos fazem trabalhar em coleções publicamente disponíveis:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Registros de metadata podem ser visualizados livremente no site do Worldcat, mas não baixados em massa (até que nós <a %(worldcat_scrape)s>os raspamos</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "O código pode ser de código aberto no Github, mas o Github como um todo não pode ser facilmente espelhado e, assim, preservado (embora, neste caso particular, haja cópias suficientemente distribuídas da maioria dos repositórios de código)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "O Reddit é gratuito para usar, mas recentemente implementou medidas rigorosas contra raspagem, na esteira do treinamento de LLM faminto por dados (mais sobre isso depois)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Uma multiplicação de cópias"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Voltando à nossa pergunta original: como podemos afirmar que preservamos nossas coleções para sempre? O principal problema aqui é que nossa coleção tem <a %(torrents_stats)s>crescido</a> rapidamente, raspando e tornando algumas coleções massivas de código aberto (além do trabalho incrível já feito por outras bibliotecas-sombra de dados abertos como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Esse crescimento de dados torna mais difícil que as coleções sejam espelhadas ao redor do mundo. O armazenamento de dados é caro! Mas estamos otimistas, especialmente ao observar as três tendências a seguir."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Colhemos os frutos mais fáceis"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Este segue diretamente de nossas prioridades discutidas acima. Preferimos trabalhar na liberação de grandes coleções primeiro. Agora que garantimos algumas das maiores coleções do mundo, esperamos que nosso crescimento seja muito mais lento."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Ainda há uma longa cauda de coleções menores, e novos livros são digitalizados ou publicados todos os dias, mas a taxa provavelmente será muito mais lenta. Podemos ainda dobrar ou até triplicar de tamanho, mas ao longo de um período de tempo mais longo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Os custos de armazenamento continuam a cair exponencialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "No momento da escrita, os <a %(diskprices)s>preços dos discos</a> por TB estão em torno de $12 para discos novos, $8 para discos usados e $4 para fita. Se formos conservadores e olharmos apenas para discos novos, isso significa que armazenar um petabyte custa cerca de $12.000. Se assumirmos que nossa biblioteca triplicará de 900TB para 2,7PB, isso significaria $32.400 para espelhar toda a nossa biblioteca. Adicionando eletricidade, custo de outros hardwares, e assim por diante, vamos arredondar para $40.000. Ou com fita, mais como $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Por um lado, <strong>$15.000–$40.000 para a soma de todo o conhecimento humano é uma pechincha</strong>. Por outro lado, é um pouco íngreme esperar toneladas de cópias completas, especialmente se também quisermos que essas pessoas continuem semeando seus torrents para o benefício de outros."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Isso é hoje. Mas o progresso avança:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Os custos de discos rígidos por TB foram reduzidos em cerca de um terço nos últimos 10 anos e provavelmente continuarão a cair em um ritmo semelhante. A fita parece estar em uma trajetória semelhante. Os preços dos SSDs estão caindo ainda mais rápido e podem superar os preços dos HDDs até o final da década."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendências de preços de HDD de diferentes fontes (clique para ver o estudo)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se isso se mantiver, então em 10 anos podemos estar olhando para apenas $5.000–$13.000 para espelhar toda a nossa coleção (1/3), ou ainda menos se crescermos menos em tamanho. Embora ainda seja muito dinheiro, isso será acessível para muitas pessoas. E pode ser ainda melhor por causa do próximo ponto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Melhorias na densidade de informação"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Atualmente, armazenamos livros nos formatos brutos em que nos são entregues. Claro, eles são comprimidos, mas muitas vezes ainda são grandes digitalizações ou fotografias de páginas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Até agora, as únicas opções para reduzir o tamanho total de nossa coleção foram através de compressão mais agressiva ou deduplicação. No entanto, para obter economias significativas, ambas são muito prejudiciais para o nosso gosto. A compressão pesada de fotos pode tornar o texto quase ilegível. E a deduplicação requer alta confiança de que os livros sejam exatamente os mesmos, o que muitas vezes é impreciso, especialmente se o conteúdo for o mesmo, mas as digitalizações forem feitas em ocasiões diferentes."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Sempre houve uma terceira opção, mas sua qualidade era tão abismal que nunca a consideramos: <strong>OCR, ou Reconhecimento Óptico de Caracteres</strong>. Este é o processo de converter fotos em texto simples, usando IA para detectar os caracteres nas fotos. Ferramentas para isso existem há muito tempo e são bastante decentes, mas \"bastante decente\" não é suficiente para fins de preservação."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "No entanto, modelos recentes de aprendizado profundo multimodal têm feito progressos extremamente rápidos, embora ainda a custos elevados. Esperamos que tanto a precisão quanto os custos melhorem dramaticamente nos próximos anos, a ponto de se tornar realista aplicá-los a toda a nossa biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Melhorias no OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quando isso acontecer, provavelmente ainda preservaremos os arquivos originais, mas, além disso, poderíamos ter uma versão muito menor de nossa biblioteca que a maioria das pessoas desejará espelhar. O ponto chave é que o texto bruto em si se comprime ainda melhor e é muito mais fácil de deduplicar, proporcionando-nos ainda mais economias."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "No geral, não é irrealista esperar pelo menos uma redução de 5-10x no tamanho total dos arquivos, talvez até mais. Mesmo com uma redução conservadora de 5x, estaríamos olhando para <strong>$1.000–$3.000 em 10 anos, mesmo que nossa biblioteca triplique de tamanho</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Janela crítica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se essas previsões forem precisas, nós <strong>só precisamos esperar alguns anos</strong> antes que toda a nossa coleção seja amplamente espelhada. Assim, nas palavras de Thomas Jefferson, \"colocada além do alcance do acidente\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Infelizmente, o advento dos LLMs, e seu treinamento faminto por dados, colocou muitos detentores de direitos autorais na defensiva. Ainda mais do que já estavam. Muitos sites estão tornando mais difícil raspar e arquivar, processos judiciais estão voando por aí, e enquanto isso, bibliotecas e arquivos físicos continuam a ser negligenciados."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Só podemos esperar que essas tendências continuem a piorar, e muitas obras sejam perdidas bem antes de entrarem em domínio público."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Estamos à beira de uma revolução na preservação, mas <q>o perdido não pode ser recuperado.</q></strong> Temos uma janela crítica de cerca de 5-10 anos durante a qual ainda é bastante caro operar uma biblioteca-sombra e criar muitos espelhos ao redor do mundo, e durante a qual o acesso ainda não foi completamente fechado."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se conseguirmos atravessar essa janela, então de fato teremos preservado o conhecimento e a cultura da humanidade para sempre. Não devemos deixar esse tempo ser desperdiçado. Não devemos deixar essa janela crítica se fechar para nós."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Vamos lá."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Acesso exclusivo para empresas de LLM à maior coleção de livros de não-ficção chineses do mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versão chinesa 中文版</a>, <a %(news_ycombinator)s>Discutir no Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Resumo:</strong> O Acervo da Anna adquiriu uma coleção única de 7,5 milhões / 350TB de livros de não-ficção chineses — maior que a Library Genesis. Estamos dispostos a dar a uma empresa de LLM acesso exclusivo, em troca de OCR de alta qualidade e extração de texto.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Este é um breve post no blog. Estamos procurando alguma empresa ou instituição para nos ajudar com OCR e extração de texto para uma coleção massiva que adquirimos, em troca de acesso exclusivo antecipado. Após o período de embargo, liberaremos, é claro, toda a coleção."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Texto acadêmico de alta qualidade é extremamente útil para o treinamento de LLMs. Embora nossa coleção seja chinesa, isso deve ser útil até mesmo para o treinamento de LLMs em inglês: os modelos parecem codificar conceitos e conhecimentos independentemente da língua de origem."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Para isso, o texto precisa ser extraído das digitalizações. O que o Acervo da Anna ganha com isso? Pesquisa de texto completo dos livros para seus usuários."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Como nossos objetivos estão alinhados com os dos desenvolvedores de LLM, estamos procurando um colaborador. Estamos dispostos a lhe dar <strong>acesso antecipado exclusivo a esta coleção em massa por 1 ano</strong>, se você puder fazer OCR e extração de texto adequados. Se você estiver disposto a compartilhar todo o código do seu pipeline conosco, estaríamos dispostos a embargar a coleção por mais tempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Páginas de exemplo"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Para nos provar que você tem um bom pipeline, aqui estão algumas páginas de exemplo para começar, de um livro sobre supercondutores. Seu pipeline deve lidar adequadamente com matemática, tabelas, gráficos, notas de rodapé, e assim por diante."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envie suas páginas processadas para nosso e-mail. Se estiverem boas, enviaremos mais em particular, e esperamos que você consiga rodar rapidamente seu pipeline nelas também. Uma vez que estivermos satisfeitos, podemos fazer um acordo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Coleção"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Algumas informações adicionais sobre a coleção. <a %(duxiu)s>Duxiu</a> é um banco de dados massivo de livros digitalizados, criado pelo <a %(chaoxing)s>SuperStar Digital Library Group</a>. A maioria são livros acadêmicos, digitalizados para torná-los disponíveis digitalmente para universidades e bibliotecas. Para nosso público de língua inglesa, <a %(library_princeton)s>Princeton</a> e a <a %(guides_lib_uw)s>Universidade de Washington</a> têm boas visões gerais. Há também um excelente artigo que fornece mais contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (procure no Acervo da Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Os livros do Duxiu há muito tempo são pirateados na internet chinesa. Geralmente, são vendidos por menos de um dólar por revendedores. Eles são tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(github_duty_machine)s>aqui</a> e <a %(github_821_github_io)s>aqui</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Isso estava no topo da nossa lista de tarefas, e alocamos vários meses de trabalho em tempo integral para isso. No entanto, recentemente, um voluntário incrível, surpreendente e talentoso nos procurou, dizendo que já havia feito todo esse trabalho — a um grande custo. Eles compartilharam a coleção completa conosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente notável. Eles concordaram em pedir ajuda desta forma para que a coleção fosse OCRizada."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "A coleção possui 7.543.702 arquivos. Isso é mais do que a não-ficção do Library Genesis (cerca de 5,3 milhões). O tamanho total dos arquivos é de cerca de 359TB (326TiB) em sua forma atual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Estamos abertos a outras propostas e ideias. Basta nos contatar. Confira o Acervo da Anna para mais informações sobre nossas coleções, esforços de preservação e como você pode ajudar. Obrigado!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Acervo da Anna quando possível, mas não vamos mais hospedá-los nós mesmos, nem recomendamos que outros espelhem usando IPFS. Por favor, veja nossa página de Torrents se você quiser ajudar a preservar nossa coleção."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Ajude a semear o Z-Library no IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Como operar uma biblioteca-sombra: operações no Acervo da Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Não há <q>AWS para instituições de caridade-sombra,</q> então como operamos o Acervo da Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Eu opero o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, o maior motor de busca de código aberto sem fins lucrativos do mundo para <a %(wikipedia_shadow_library)s>bibliotecas-sombra</a>, como Sci-Hub, Library Genesis e Z-Library. Nosso objetivo é tornar o conhecimento e a cultura facilmente acessíveis e, em última análise, construir uma comunidade de pessoas que, juntas, arquivam e preservam <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos os livros do mundo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Neste artigo, mostrarei como operamos este site e os desafios únicos que vêm com a operação de um site com status legal questionável, já que não há \"AWS para instituições de caridade-sombra\"."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Confira também o artigo irmão <a %(blog_how_to_become_a_pirate_archivist)s>Como se tornar um arquivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokens de inovação"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Vamos começar com nossa pilha de tecnologia. Ela é deliberadamente entediante. Usamos Flask, MariaDB e ElasticSearch. E é literalmente isso. A busca é em grande parte um problema resolvido, e não pretendemos reinventá-la. Além disso, temos que gastar nossos <a %(mcfunley)s>tokens de inovação</a> em outra coisa: não sermos derrubados pelas autoridades."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Então, quão legal ou ilegal é exatamente o Acervo da Anna? Isso depende principalmente da jurisdição legal. A maioria dos países acredita em alguma forma de direitos autorais, o que significa que pessoas ou empresas recebem um monopólio exclusivo sobre certos tipos de obras por um determinado período de tempo. Como observação, no Acervo da Anna acreditamos que, embora existam alguns benefícios, no geral os direitos autorais são um prejuízo líquido para a sociedade — mas essa é uma história para outro momento."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Esse monopólio exclusivo sobre certas obras significa que é ilegal para qualquer pessoa fora desse monopólio distribuir diretamente essas obras — incluindo nós. Mas o Acervo da Anna é um motor de busca que não distribui diretamente essas obras (pelo menos não em nosso site na clearnet), então deveríamos estar bem, certo? Não exatamente. Em muitas jurisdições, não é apenas ilegal distribuir obras protegidas por direitos autorais, mas também vincular a lugares que o fazem. Um exemplo clássico disso é a lei DMCA dos Estados Unidos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Esse é o extremo mais rigoroso do espectro. No outro extremo do espectro, teoricamente, poderia haver países sem leis de direitos autorais, mas esses realmente não existem. Praticamente todos os países têm algum tipo de lei de direitos autorais em vigor. A aplicação é uma história diferente. Existem muitos países com governos que não se importam em aplicar a lei de direitos autorais. Existem também países entre os dois extremos, que proíbem a distribuição de obras protegidas por direitos autorais, mas não proíbem a vinculação a tais obras."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Outra consideração é no nível da empresa. Se uma empresa opera em uma jurisdição que não se importa com direitos autorais, mas a própria empresa não está disposta a correr nenhum risco, então eles podem fechar seu site assim que alguém reclamar sobre ele."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, uma grande consideração são os pagamentos. Como precisamos permanecer anônimos, não podemos usar métodos de pagamento tradicionais. Isso nos deixa com criptomoedas, e apenas um pequeno subconjunto de empresas as aceita (existem cartões de débito virtuais pagos por cripto, mas eles muitas vezes não são aceitos)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arquitetura do sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Então, digamos que você encontrou algumas empresas dispostas a hospedar seu site sem fechá-lo — vamos chamá-las de “provedores amantes da liberdade” 😄. Você rapidamente descobrirá que hospedar tudo com eles é bastante caro, então pode querer encontrar alguns “provedores baratos” e fazer a hospedagem real lá, usando os provedores amantes da liberdade como proxy. Se você fizer isso corretamente, os provedores baratos nunca saberão o que você está hospedando e nunca receberão reclamações."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Com todos esses provedores, há o risco de eles fecharem você de qualquer maneira, então você também precisa de redundância. Precisamos disso em todos os níveis da nossa pilha."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Uma empresa um tanto amante da liberdade que se colocou em uma posição interessante é a Cloudflare. Eles <a %(blog_cloudflare)s>argumentaram</a> que não são um provedor de hospedagem, mas uma utilidade, como um ISP. Portanto, não estão sujeitos a solicitações de remoção da DMCA ou outras, e encaminham quaisquer solicitações para o seu provedor de hospedagem real. Eles chegaram a ir ao tribunal para proteger essa estrutura. Podemos, portanto, usá-los como outra camada de cache e proteção."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "A Cloudflare não aceita pagamentos anônimos, então só podemos usar seu plano gratuito. Isso significa que não podemos usar seus recursos de balanceamento de carga ou failover. Portanto, <a %(annas_archive_l255)s>implementamos isso nós mesmos</a> no nível do domínio. Ao carregar a página, o navegador verificará se o domínio atual ainda está disponível e, caso contrário, reescreverá todos os URLs para um domínio diferente. Como a Cloudflare armazena em cache muitas páginas, isso significa que um usuário pode acessar nosso domínio principal, mesmo que o servidor proxy esteja fora do ar, e então, no próximo clique, ser movido para outro domínio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ainda temos preocupações operacionais normais para lidar, como monitorar a saúde do servidor, registrar erros de backend e frontend, e assim por diante. Nossa arquitetura de failover permite mais robustez nesse aspecto também, por exemplo, executando um conjunto completamente diferente de servidores em um dos domínios. Podemos até executar versões mais antigas do código e dos datasets nesse domínio separado, caso um bug crítico na versão principal passe despercebido."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Também podemos nos proteger contra a Cloudflare se voltar contra nós, removendo-a de um dos domínios, como este domínio separado. Diferentes permutações dessas ideias são possíveis."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ferramentas"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vamos ver quais ferramentas usamos para realizar tudo isso. Isso está evoluindo muito à medida que encontramos novos problemas e descobrimos novas soluções."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidor de aplicação: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gerenciamento de servidores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Desenvolvimento: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hospedagem estática Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Há algumas decisões sobre as quais temos ido e voltado. Uma delas é a comunicação entre servidores: costumávamos usar Wireguard para isso, mas descobrimos que ele ocasionalmente para de transmitir qualquer dado, ou só transmite dados em uma direção. Isso aconteceu com várias configurações diferentes de Wireguard que tentamos, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Também tentamos tunelar portas via SSH, usando autossh e sshuttle, mas encontramos <a %(github_sshuttle)s>problemas lá</a> (embora ainda não esteja claro para mim se o autossh sofre de problemas de TCP sobre TCP ou não — só parece uma solução instável para mim, mas talvez esteja tudo bem?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Em vez disso, voltamos para conexões diretas entre servidores, escondendo que um servidor está rodando em provedores baratos usando filtragem de IP com UFW. Isso tem a desvantagem de que o Docker não funciona bem com UFW, a menos que você use <code>network_mode: \"host\"</code>. Tudo isso é um pouco mais propenso a erros, porque você exporá seu servidor à internet com apenas uma pequena configuração incorreta. Talvez devêssemos voltar para o autossh — feedback seria muito bem-vindo aqui."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Também temos ido e voltado entre Varnish e Nginx. Atualmente gostamos do Varnish, mas ele tem suas peculiaridades e arestas. O mesmo se aplica ao Checkmk: não o amamos, mas funciona por enquanto. O Weblate tem sido aceitável, mas não incrível — às vezes temo que ele perca meus dados sempre que tento sincronizá-lo com nosso repositório git. O Flask tem sido bom no geral, mas tem algumas peculiaridades estranhas que custaram muito tempo para depurar, como configurar domínios personalizados ou problemas com sua integração SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Até agora, as outras ferramentas têm sido ótimas: não temos reclamações sérias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Todos eles tiveram alguns problemas, mas nada muito sério ou que consuma muito tempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusão"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Tem sido uma experiência interessante aprender a configurar um motor de busca de biblioteca-sombra robusto e resiliente. Há muitos mais detalhes para compartilhar em posts futuros, então me avise sobre o que você gostaria de saber mais!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como sempre, estamos em busca de doações para apoiar este trabalho, então não deixe de visitar a página de Doações no Acervo da Anna. Também estamos procurando outros tipos de apoio, como subsídios, patrocinadores de longo prazo, provedores de pagamento de alto risco, talvez até anúncios (de bom gosto!). E se você quiser contribuir com seu tempo e habilidades, estamos sempre procurando desenvolvedores, tradutores, e assim por diante. Obrigado pelo seu interesse e apoio."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Olá, sou a Anna. Eu criei o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, a maior biblioteca-sombra do mundo. Este é meu blog pessoal, no qual eu e meus colegas escrevemos sobre pirataria, preservação digital e muito mais."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conecte-se comigo no <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Observe que este site é apenas um blog. Nós apenas hospedamos nossas próprias palavras aqui. Nenhum torrent ou outros arquivos protegidos por direitos autorais são hospedados ou vinculados aqui."

#, fuzzy
msgid "blog.index.heading"
msgstr "Postagens no blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "Raspagem de 1,3B do WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Colocando 5.998.794 livros no IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Acervo da Anna quando possível, mas não vamos mais hospedá-los nós mesmos, nem recomendamos que outros espelhem usando IPFS. Por favor, veja nossa página de Torrents se você quiser ajudar a preservar nossa coleção."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Resumo:</strong> O Acervo da Anna raspou todo o WorldCat (a maior coleção de metadata de bibliotecas do mundo) para fazer uma lista de tarefas de livros que precisam ser preservados.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Há um ano, nós <a %(blog)s>começamos</a> a responder a esta pergunta: <strong>Qual porcentagem de livros foi permanentemente preservada por bibliotecas-sombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Uma vez que um livro entra em uma biblioteca-sombra de dados abertos como a <a %(wikipedia_library_genesis)s>Library Genesis</a>, e agora o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, ele é espelhado em todo o mundo (através de torrents), preservando-o praticamente para sempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Para responder à pergunta de qual porcentagem de livros foi preservada, precisamos saber o denominador: quantos livros existem no total? E, idealmente, não temos apenas um número, mas metadata real. Então, podemos não apenas compará-los com bibliotecas-sombra, mas também <strong>criar uma lista de tarefas dos livros restantes para preservar!</strong> Poderíamos até começar a sonhar com um esforço colaborativo para percorrer essa lista de tarefas."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Raspamos o <a %(wikipedia_isbndb_com)s>ISBNdb</a> e baixamos o <a %(openlibrary)s>conjunto de dados da Open Library</a>, mas os resultados foram insatisfatórios. O principal problema era que não havia muita sobreposição de ISBNs. Veja este diagrama de Venn do <a %(blog)s>nosso post no blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ficamos muito surpresos com o quão pouco havia de sobreposição entre ISBNdb e Open Library, ambos os quais incluem dados de várias fontes, como raspagens da web e registros de bibliotecas. Se ambos fazem um bom trabalho em encontrar a maioria dos ISBNs por aí, seus círculos certamente teriam uma sobreposição substancial, ou um seria um subconjunto do outro. Isso nos fez questionar, quantos livros estão <em>completamente fora desses círculos</em>? Precisamos de um banco de dados maior."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Foi então que miramos no maior banco de dados de livros do mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Este é um banco de dados proprietário da organização sem fins lucrativos <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliotecas de todo o mundo, em troca de dar a essas bibliotecas acesso ao conjunto completo de dados e de aparecerem nos resultados de busca dos usuários finais."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Embora a OCLC seja uma organização sem fins lucrativos, seu modelo de negócios exige a proteção de seu banco de dados. Bem, lamentamos dizer, amigos da OCLC, estamos liberando tudo. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "No último ano, raspamos meticulosamente todos os registros do WorldCat. No início, tivemos um golpe de sorte. O WorldCat estava lançando a reformulação completa de seu site (em agosto de 2022). Isso incluiu uma revisão substancial de seus sistemas de backend, introduzindo muitas falhas de segurança. Aproveitamos imediatamente a oportunidade e conseguimos raspar centenas de milhões (!) de registros em poucos dias."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Reformulação do WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Depois disso, as falhas de segurança foram corrigidas lentamente uma a uma, até que a última que encontramos foi corrigida há cerca de um mês. Naquela época, já tínhamos praticamente todos os registros e estávamos apenas buscando registros de qualidade ligeiramente superior. Então sentimos que é hora de lançar!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Vamos dar uma olhada em algumas informações básicas sobre os dados:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contêineres do Acervo da Anna (AAC)</a>, que é essencialmente <a %(jsonlines)s>JSON Lines</a> comprimido com <a %(zstd)s>Zstandard</a>, além de algumas semânticas padronizadas. Esses contêineres envolvem vários tipos de registros, com base nas diferentes raspagens que implantamos."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dados"

msgid "dyn.buy_membership.error.unknown"
msgstr "Um erro desconhecido ocorreu. Por favor nos contate em %(email)s com uma captura de tela."

msgid "dyn.buy_membership.error.minimum"
msgstr "Essa moeda tem um mínimo mais alto que o habitual. Por favor selecione uma duração diferente ou uma moeda diferente."

msgid "dyn.buy_membership.error.try_again"
msgstr "A solicitação não pôde ser concluída. Por favor, tente novamente em alguns minutos, e se continuar acontecendo, entre em contato conosco em %(email)s com uma captura de tela."

msgid "dyn.buy_membership.error.wait"
msgstr "Erro no processamento do pagamento. Aguarde um momento e tente novamente. Se o problema persistir por mais de 24 horas, entre em contato conosco pelo e-mail %(email)s com uma captura de tela."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentário oculto"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema no arquivo: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Melhor versão"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Você deseja denunciar este usuário por comportamento abusivo ou inadequado?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Denunciar abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso denunciado:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Você denunciou este usuário por abuso."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responder"

msgid "page.md5.quality.logged_out_login"
msgstr "Por favor, <a %(a_login)s>faça login</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Você deixou um comentário. Pode levar um minuto para ele aparecer."

msgid "page.md5.quality.comment_error"
msgstr "Algo deu errado. Por favor, recarregue a página e tente novamente."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s páginas afetadas"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Não visível na área de Não-Ficção do Libgen.rs"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Não visível na área de Ficção do Libgen.rs"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Não visível no Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcado como corrompido no Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Indisponível no Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcado como “spam” no Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcado como “arquivo ruim” no Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nem todas as páginas puderam ser convertidas para PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "A execução do exiftool falhou neste arquivo"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Livro (desconhecido)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Livro (não ficção)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Livro (ficção)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artigo de periódico"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documento de normas"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Quadrinhos"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musical"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolivro"

msgid "common.md5_content_type_mapping.other"
msgstr "Outros"

msgid "common.access_types_mapping.aa_download"
msgstr "Download via servidor de parceiros"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Download externo"

msgid "common.access_types_mapping.external_borrow"
msgstr "Empréstimo externo"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Empréstimo externo (impressão desabilitada)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Explore os metadados"

msgid "common.access_types_mapping.torrents_available"
msgstr "Contido em torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinês"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Envia para AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "Índice de eBooks EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadados tchecos"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Livros"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Estatal Russa"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Título"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Editora"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edição"

msgid "common.specific_search_fields.year"
msgstr "Ano de publicação"

msgid "common.specific_search_fields.original_filename"
msgstr "Nome do arquivo original"

msgid "common.specific_search_fields.description_comments"
msgstr "Descrição e comentários de metadados"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads do servidor parceiro temporariamente indisponíveis para este arquivo."

msgid "common.md5.servers.fast_partner"
msgstr "Servidor Parceiro Rápido #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomendado)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sem verificação de navegador ou listas de espera)"

msgid "common.md5.servers.slow_partner"
msgstr "Servidor Parceiro Lento #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(um pouco mais rápido, mas com lista de espera)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sem lista de espera, mas pode ser muito lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Não ficção"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficção"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(clique também em “GET” no topo)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(clique em “GET” no topo)"

msgid "page.md5.box.download.libgen_ads"
msgstr "seus anúncios são conhecidos por conter software malicioso, então use um bloqueador de anúncios ou não clique em anúncios"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Os arquivos Nexus/STC podem não serem confiáveis para download)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library no Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(precisa do navegador Tor)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Emprestar do Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(impressão desabilitada, somente para membros)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(o DOI associado pode não estar disponível no Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "coleção"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Downloads torrent em massa"

msgid "page.md5.box.download.experts_only"
msgstr "(apenas especialistas)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Busque no Acervo da Anna um ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Procure em vários outros bancos de dados pelo ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Encontre o registro original em ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Busque no Acervo da Anna um ID Open Library"

msgid "page.md5.box.download.original_openlib"
msgstr "Encontre o registro original na Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Busque no Acervo da Anna um número OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Procurar o registro original no WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Busque no Acervo da Anna um SSID DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Pesquise manualmente no DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Busque no Acervo da Anna um número CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Encontre o registro original em CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Busque no Acervo da Anna um número DuXiu DXID"

msgid "page.md5.box.download.edsebk"
msgstr "Índice de eBooks EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Acervo da Anna 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(não é necessária a verificação do navegador)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadados tchecos %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Livros %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metadados"

msgid "page.md5.box.descr_title"
msgstr "descrição"

msgid "page.md5.box.alternative_filename"
msgstr "Nome de arquivo alternativo"

msgid "page.md5.box.alternative_title"
msgstr "Título alternativo"

msgid "page.md5.box.alternative_author"
msgstr "Autor alternativo"

msgid "page.md5.box.alternative_publisher"
msgstr "Editora alternativa"

msgid "page.md5.box.alternative_edition"
msgstr "Edição alternativa"

msgid "page.md5.box.alternative_extension"
msgstr "Extensão alternativa"

msgid "page.md5.box.metadata_comments_title"
msgstr "comentários de metadados"

msgid "page.md5.box.alternative_description"
msgstr "Descrição alternativa"

msgid "page.md5.box.date_open_sourced_title"
msgstr "data de lançamento público"

msgid "page.md5.header.scihub"
msgstr "Arquivo do Sci-Hub \"%(id)s\""

msgid "page.md5.header.ia"
msgstr "Arquivo de Controle de Empréstimo Digital (Controlled Digital Lending) do Internet Archive \"%(id)s\""

msgid "page.md5.header.ia_desc"
msgstr "Esse é um registro de um arquivo do Internet Archive, não é um arquivo que pode ser baixado diretamente. Você pode tentar emprestar o livro (link abaixo), ou usar essa URL quando <a %(a_request)s>pedir por um arquivo</a>."

msgid "page.md5.header.consider_upload"
msgstr "Se você tem esse arquivo e ele ainda não está disponível no Acervo da Anna, considere <a %(a_request)s>enviá-lo</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Registro de metadados do ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Registro de metadados da Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Registro de metadados do número OCLC (WorldCat) %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Registro de metadados DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Registro de metadados CADAL SSNO %(id)s"

msgid "page.md5.header.meta_magzdb_id"
msgstr "Registro de metadados MagzDB ID %(id)s"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Registro de metadados Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Esse é um registro de metadados, não um arquivo disponível para download. Você pode usar essa URL quando <a %(a_request)s>pedir por um arquivo</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Metadados do registro vinculado"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Melhorar metadados na Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Aviso: múltiplos registros vinculados:"

msgid "page.md5.header.improve_metadata"
msgstr "Melhorar metadados"

msgid "page.md5.text.report_quality"
msgstr "Reportar qualidade do arquivo"

msgid "page.search.results.download_time"
msgstr "Tempo de download"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Site:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Busque no Acervo da Anna por “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Explorador de códigos:"

msgid "page.md5.codes.code_search"
msgstr "Ver no Explorador de Códigos “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Leia mais…"

msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Empréstimos (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Explore os metadados (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentários (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listas (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Estatísticas (%(count)s)"

msgid "common.tech_details"
msgstr "Detalhes técnicos"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Este arquivo pode ter problemas e foi ocultado de uma biblioteca fonte. </span> Às vezes por causa de um pedido de um detentor de direitos autorais, às vezes porque outro arquivo alternativo está disponível, mas também porque pode haver um problema com o próprio arquivo. Você pode conseguir baixá-lo, mas sugerimos que você procure outro arquivo. Mais detalhes:"

msgid "page.md5.box.download.better_file"
msgstr "Uma versão melhor desse arquivo pode estar disponível em %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Se você ainda quiser baixar este arquivo, certifique-se de usar apenas programas confiáveis e atualizados para abri-lo."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Downloads rápidos"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Torne-se um <a %(a_membership)s>membro</a> para apoiar a preservação a longo prazo de livros, artigos e mais. Para mostrar nossa gratidão pelo seu apoio, você ganha downloads rápidos. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se você doar este mês, receberá o <strong>dobro</strong> do número de downloads rápidos."

msgid "page.md5.box.download.header_fast_member"
msgstr "Você tem %(remaining)s sobrando hoje. Obrigado por ser um membro! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Você ficou sem downloads rápidos por hoje."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Você baixou esse arquivo recentemente. Links continuam válidos por um tempo."

msgid "page.md5.box.download.option"
msgstr "Opção #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(sem redirecionamento)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(abrir no visualizador)"

msgid "layout.index.header.banner.refer"
msgstr "Indique um amigo, e vocês dois recebem %(percentage)s%% downloads rápidos bônus!"

msgid "layout.index.header.learn_more"
msgstr "Saiba mais…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Downloads lentos"

msgid "page.md5.box.download.trusted_partners"
msgstr "De parceiros confiáveis."

msgid "page.md5.box.download.slow_faq"
msgstr "Mais informações na<a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(pode exigir <a %(a_browser)s>verificação do navegador</a> — downloads ilimitados!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Após o download:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Abrir no nosso visualizador"

msgid "page.md5.box.external_downloads"
msgstr "mostrar downloads externos"

msgid "page.md5.box.download.header_external"
msgstr "Downloads externos"

msgid "page.md5.box.download.no_found"
msgstr "Nenhum download encontrado."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Todas as opções de download contêm o mesmo arquivo e devem ser seguras para uso. Dito isso, tenha sempre cuidado ao baixar arquivos da internet, principalmente de sites externos ao Acervo da Anna. Por exemplo, certifique-se de manter seus dispositivos atualizados."

msgid "page.md5.box.download.dl_managers"
msgstr "Para arquivos grandes, recomendamos o uso de um gerenciador de downloads para evitar interrupções."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Gerenciadores de download recomendados: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Você precisará de um leitor de ebook ou PDF para abrir o arquivo, dependendo do formato do arquivo."

msgid "page.md5.box.download.readers.links"
msgstr "Leitores de eBooks recomendados: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Visualizador online do Arquivo da Anna"

msgid "page.md5.box.download.conversion"
msgstr "Use ferramentas online para converter entre formatos."

msgid "page.md5.box.download.conversion.links"
msgstr "Ferramentas de conversão recomendadas: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Você pode enviar arquivos PDF e EPUB para o seu eReader Kindle ou Kobo."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ferramentas recomendadas: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Enviar para Kindle” da Amazon"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Enviar para Kobo/Kindle” do djazz"

msgid "page.md5.box.download.support"
msgstr "Apoie autores e bibliotecas"

msgid "page.md5.box.download.support.authors"
msgstr "Se você gostou e pode pagar, considere comprar o original ou apoiar os autores diretamente."

msgid "page.md5.box.download.support.libraries"
msgstr "Se estiver disponível na sua biblioteca local, considere pegá-lo emprestado gratuitamente lá."

msgid "page.md5.quality.header"
msgstr "Qualidade do arquivo"

msgid "page.md5.quality.report"
msgstr "Ajude a comunidade pontuando a qualidade deste arquivo! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Reportar problema no arquivo (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Ótima qualidade do arquivo (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Adicionar comentário (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "O que há de errado com este arquivo?"

msgid "page.md5.quality.copyright"
msgstr "Por favor, use o <a %(a_copyright)s>formulário de reivindicação de DMCA / Direitos Autorais</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Descreva o problema (obrigatório)"

msgid "page.md5.quality.issue_description"
msgstr "Descrição do problema"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 de uma versão melhor deste arquivo (se aplicável)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Preencha isso se houver outro arquivo que seja estritamente semelhantemente a este arquivo (mesma edição, mesma extensão de arquivo, se você puder encontrar um), que as pessoas devem usar em vez deste arquivo. Se você souber de uma versão melhor deste arquivo fora do Arquivo da Anna, então, por favor, <a %(a_upload)s>faça o upload</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Você pode obter o md5 a partir do URL, por exemplo."

msgid "page.md5.quality.submit_report"
msgstr "Enviar reporte"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Aprenda como <a %(a_metadata)s>melhorar os metadados</a> deste arquivo você mesmo."

msgid "page.md5.quality.report_thanks"
msgstr "Obrigado por enviar seu reporte. Ele será exibido nesta página e revisado manualmente por nós (até termos um sistema de moderação adequado)."

msgid "page.md5.quality.report_error"
msgstr "Algo deu errado. Por favor, recarregue a página e tente novamente."

msgid "page.md5.quality.great.summary"
msgstr "Se este arquivo tiver ótima qualidade, você pode colocar suas ideas sobre ele aqui! Caso contrário, use o botão “Relatar problema no arquivo”."

msgid "page.md5.quality.loved_the_book"
msgstr "Adorei este livro!"

msgid "page.md5.quality.submit_comment"
msgstr "Deixar um comentário"

msgid "common.english_only"
msgstr "O texto continua abaixo, em inglês."

msgid "page.md5.text.stats.total_downloads"
msgstr "Total de downloads: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Um “MD5 do arquivo” é um algoritmo criptográfico que é calculado a partir do conteúdo do arquivo e é o único aceitável com base nesse conteúdo. Todas as bibliotecas-sombra que indexamos aqui usam principalmente MD5s para identificar arquivos."

msgid "page.md5.text.md5_info.text2"
msgstr "Um arquivo pode aparecer em várias bibliotecas-sombra. Para informações sobre os diversos datasets que compilamos, veja a <a %(a_datasets)s>página de Datasets</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Este é um arquivo gerenciado pela biblioteca <a %(a_ia)s>IA’s Controlled Digital Lending</a> e indexado pelo Acervo da Anna para buscas. Para informações sobre os diversos bancos de dados que compilamos, veja a <a %(a_datasets)s>página de Bancos de Dados</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Para obter informações sobre este arquivo específico, confira seu <a %(a_href)s>arquivo JSON</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Problema ao carregar esta página"

msgid "page.aarecord_issue.text"
msgstr "Por favor, atualize a página para tentar novamente. <a %(a_contact)s>Entre em contato conosco</a> se o problema persistir por várias horas."

msgid "page.md5.invalid.header"
msgstr "Nenhum resultado"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” não foi encontrado em nosso banco de dados."

msgid "page.login.title"
msgstr "Entrar / Registrar"

msgid "page.browserverification.header"
msgstr "Verificação do navegador"

msgid "page.login.text1"
msgstr "Para evitar que bots de spam criem muitas contas, precisamos verificar seu navegador primeiro."

msgid "page.login.text2"
msgstr "Se você cair em um loop infinito, recomendamos instalar o <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Também pode ajudar desativar bloqueadores de anúncios e outras extensões do navegador."

msgid "page.codes.title"
msgstr "Códigos"

msgid "page.codes.heading"
msgstr "Explorador de Códigos"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explore os códigos com os quais os registros são etiquetados, por prefixo. A coluna “registros” mostra o número de registros etiquetados com códigos com o prefixo dado, conforme visto no mecanismo de busca (incluindo registros apenas de metadados). A coluna “códigos” mostra quantos códigos reais têm um determinado prefixo."

msgid "page.codes.why_cloudflare"
msgstr "Esta página pode demorar um pouco para ser gerada, por isso requer um captcha do Cloudflare. <a %(a_donate)s>Membros</a> podem pular o captcha."

msgid "page.codes.dont_scrape"
msgstr "Por favor, não faça \"scraping\" destas páginas. Ao invez disso, recomendamos <a %(a_import)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados da ElasticSearch e MariaDB, e executar nosso <a %(a_software)s>código aberto</a>. Os dados brutos podem ser explorados manualmente através de arquivos JSON como <a %(a_json_file)s>este aqui</a>."

msgid "page.codes.prefix"
msgstr "Prefixo"

msgid "common.form.go"
msgstr "Ir"

msgid "common.form.reset"
msgstr "Redefinir"

msgid "page.codes.search_archive_start"
msgstr "Buscar no Acervo da Anna"

msgid "page.codes.bad_unicode"
msgstr "Aviso: o código contém caracteres Unicode incorretos e pode se comportar de maneira inadequada em várias situações. O binário bruto pode ser decodificado a partir da representação base64 no URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefixo de código conhecido “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Prefixo"

msgid "page.codes.code_label"
msgstr "Etiqueta"

msgid "page.codes.code_description"
msgstr "Descrição"

msgid "page.codes.code_url"
msgstr "URL para um código específico"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” será substituído pelo valor do código"

msgid "page.codes.generic_url"
msgstr "URL genérico"

msgid "page.codes.code_website"
msgstr "Website"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s registro correspondente a “%(prefix_label)s”"
msgstr[1] "%(count)s registros correspondentes a “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL para código específico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mais…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Códigos começando com “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Índice de"

msgid "page.codes.records_prefix"
msgstr "registros"

msgid "page.codes.records_codes"
msgstr "códigos"

msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s registros"

msgid "page.contact.dmca.form"
msgstr "Para reivindicações de DMCA/direitos autorais, use <a %(a_copyright)s>este formulário</a>."

msgid "page.contact.dmca.delete"
msgstr "Quaisquer outras formas de nos contatar sobre reivindicações de direitos autorais serão automaticamente excluídas."

msgid "page.contact.checkboxes.text1"
msgstr "Agradecemos muito seus comentários e perguntas!"

msgid "page.contact.checkboxes.text2"
msgstr "No entanto, devido à quantidade de spam e e-mails sem sentido que recebemos, marque as caixas para confirmar que compreende estas condições ao entrar em contato conosco."

msgid "page.contact.checkboxes.copyright"
msgstr "As reivindicações de direitos autorais deste e-mail serão ignoradas; use o formulário."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Servidores parceiros estão indisponíveis devido ao fechamento das hospedagens. Eles devem voltar em breve."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "As assinaturas serão estendidas de acordo."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Não nos envie e-mails para <a %(a_request)s>solicitar livros</a><br>ou pequenos (<10k) <a %(a_upload)s>uploads</a>."

msgid "page.donate.please_include"
msgstr "Ao fazer perguntas sobre contas ou doações, adicione o ID da sua conta, capturas de tela, recibos e o máximo de informações possível. Verificamos nosso e-mail apenas a cada 1–2 semanas, portanto, não incluir essas informações atrasará qualquer resolução."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostrar e-mail"

msgid "page.copyright.title"
msgstr "Formulário de reivindicação de Copyright (direitos autorais)/DMCA"

msgid "page.copyright.intro"
msgstr "Se você tiver uma reivindicação de DMCA ou outros direitos autorais (Copyright), preencha este formulário o mais precisamente possível. Se encontrar algum problema, entre em contato conosco no nosso endereço dedicado ao DMCA: %(email)s. Note que reivindicações enviadas por e-mail para este endereço não serão processadas; ele é apenas para perguntas. Use o formulário abaixo para enviar suas reivindicações."

msgid "page.copyright.form.aa_urls"
msgstr "URLs no Acervo da Anna (obrigatório). Um por linha. Inclua apenas links que correspondam exatamente a mesma edição de um livro. Se você quiser fazer uma reivindicação para vários livros ou várias edições, por favor, envie este formulário várias vezes."

msgid "page.copyright.form.aa_urls.note"
msgstr "Reivindicações que agrupem vários livros ou edições serão rejeitadas."

msgid "page.copyright.form.name"
msgstr "Seu nome (obrigatório)"

msgid "page.copyright.form.address"
msgstr "Endereço (obrigatório)"

msgid "page.copyright.form.phone"
msgstr "Número de telefone (obrigatório)"

msgid "page.copyright.form.email"
msgstr "E-mail (obrigatório)"

msgid "page.copyright.form.description"
msgstr "Descrição clara do material de origem (obrigatório)"

msgid "page.copyright.form.isbns"
msgstr "ISBNs do material de origem (se aplicável). Um por linha. Inclua apenas aqueles que correspondem exatamente à edição para a qual você está relatando uma reivindicação de direitos autorais."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs do material de origem, uma por linha. Reserve um momento para pesquisar o material de origem na Open Library. Isso nos ajudará a verificar sua reivindicação."

msgid "page.copyright.form.external_urls"
msgstr "URLs para o material de origem, uma por linha (obrigatório). Inclua o máximo possível, para nos ajudar a verificar sua reivindicação (por exemplo, Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Declaração e assinatura (obrigatório)"

msgid "page.copyright.form.submit_claim"
msgstr "Enviar reivindicação"

msgid "page.copyright.form.on_success"
msgstr "✅ Obrigado por enviar sua reivindicação de direitos autorais. Vamos revisá-la o mais rápido possível. Recarregue a página para enviar outra."

msgid "page.copyright.form.on_failure"
msgstr "❌ Algo deu errado. Recarregue a página e tente novamente."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "Se você estiver interessado em espelhar este conjunto de dados para <a %(a_archival)s>arquivamento</a> ou para fins de <a %(a_llm)s>treinamento de LLM</a>, por favor, entre em contato conosco."

msgid "page.datasets.intro.text2"
msgstr "Nossa missão é arquivar todos os livros do mundo (bem como artigos, revistas, etc.) e torná-los amplamente acessíveis. Acreditamos que todos os livros devem ser espelhados amplamente, para garantir redundância e resiliência. É por isso que estamos reunindo arquivos de várias fontes. Algumas fontes são completamente abertas e podem ser espelhadas em massa (como o Sci-Hub). Outras são fechadas e protetoras, então tentamos raspá-las (scraping) para “libertar” seus livros. Outras estão em algum lugar no meio."

msgid "page.datasets.intro.text3"
msgstr "Todos os nossos dados podem ser <a %(a_torrents)s>baixados via torrent</a>, e todos os nossos metadados podem ser <a %(a_anna_software)s>gerados</a> ou <a %(a_elasticsearch)s>baixados</a> como bancos de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente através de arquivos JSON como <a %(a_dbrecord)s>este</a>."

msgid "page.datasets.overview.title"
msgstr "Visão geral"

msgid "page.datasets.overview.text1"
msgstr "Abaixo está uma visão geral rápida das fontes dos arquivos no Arquivo da Anna."

msgid "page.datasets.overview.source.header"
msgstr "Fonte"

msgid "page.datasets.overview.size.header"
msgstr "Tamanho"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% espelhado por AA / torrents disponíveis"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentagens do número de arquivos"

msgid "page.datasets.overview.last_updated.header"
msgstr "Última atualização"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Não-ficção e Ficção"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s arquivo"
msgstr[1] "%(count)s arquivos"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelado desde 2021; a maioria disponível através de torrents"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: pequenas adições desde então</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluindo “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrents de ficção estão atrasados (embora IDs ~4-6M não tenham sido torrenteados, pois se sobrepõem aos nossos torrents do Zlib)."

msgid "page.datasets.zlibzh.searchable"
msgstr "A coleção “Chinesa” na Z-Library parece ser a mesma que a nossa coleção DuXiu, mas com MD5s diferentes. Excluímos esses arquivos dos torrents para evitar duplicação, mas ainda os mostramos em nosso índice de busca."

msgid "common.record_sources_mapping.iacdl"
msgstr "Empréstimo Digital Controlado pela IA"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dos arquivos são pesquisáveis."

msgid "page.datasets.overview.total"
msgstr "Total"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluindo duplicados"

msgid "page.datasets.overview.text4"
msgstr "Como as bibliotecas-sombra frequentemente sincronizam dados entre si, há uma sobreposição considerável entre as bibliotecas. É por isso que os números não somam o total."

msgid "page.datasets.overview.text5"
msgstr "A porcentagem “espelhada e semeada pelo Arquivo da Anna” mostra quantos arquivos nós mesmos espelhamos. Semeamos esses arquivos em massa através de torrents e os disponibilizamos para download direto através de sites parceiros."

msgid "page.datasets.source_libraries.title"
msgstr "Bibliotecas-fonte"

msgid "page.datasets.source_libraries.text1"
msgstr "Algumas bibliotecas-fonte promovem o compartilhamento em massa de seus dados através de torrents, enquanto outras não compartilham prontamente sua coleção. No último caso, o Arquivo da Anna tenta extrair suas coleções e torná-las disponíveis (veja nossa página de <a %(a_torrents)s>Torrents</a>). Existem também situações intermediárias, por exemplo, onde as bibliotecas-fonte estão dispostas a compartilhar, mas não têm os recursos para fazê-lo. Nesses casos, também tentamos ajudar."

msgid "page.datasets.source_libraries.text2"
msgstr "Abaixo está uma visão geral de como interagimos com as diferentes bibliotecas-fonte."

msgid "page.datasets.sources.source.header"
msgstr "Fonte"

msgid "page.datasets.sources.files.header"
msgstr "Arquivos"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dumps diários de banco de dados <a %(dbdumps)s>HTTP</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizados para <a %(nonfiction)s>Não-Ficção</a> e <a %(fiction)s>Ficção</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s O Arquivo da Anna gerencia uma coleção de <a %(covers)s>torrents de capas de livros</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s O Sci-Hub congelou novos arquivos desde 2021."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dumps de metadados disponíveis <a %(scihub1)s>aqui</a> e <a %(scihub2)s>aqui</a>, bem como como parte do <a %(libgenli)s>banco de dados Libgen.li</a> (que usamos)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents de dados disponíveis <a %(scihub1)s>aqui</a>, <a %(scihub2)s>aqui</a> e <a %(libgenli)s>aqui</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Alguns novos arquivos estão <a %(libgenrs)s>sendo</a> <a %(libgenli)s>adicionados</a> ao “scimag” do Libgen, mas não o suficiente para justificar novos torrents"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dumps trimestrais de banco de dados <a %(dbdumps)s>HTTP</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrents de Não-Ficção são compartilhados com o Libgen.rs (e espelhados <a %(libgenli)s>aqui</a>)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Arquivo da Anna e Libgen.li gerenciam colaborativamente coleções de <a %(comics)s>quadrinhos</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos comuns</a> e <a %(fiction)s>ficção (divergente do Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s A coleção “fiction_rus” deles (ficção russa) não possui torrents dedicados, mas é coberta por torrents de outros, e mantemos um <a %(fiction_rus)s>espelho</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Arquivo da Anna e Z-Library gerenciam colaborativamente uma coleção de <a %(metadata)s>metadados do Z-Library</a> e <a %(files)s>arquivos do Z-Library</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Alguns metadados disponíveis através de <a %(openlib)s>dumps de banco de dados da Open Library</a>, mas esses não cobrem toda a coleção do IA"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Não há dumps de metadados facilmente acessíveis disponíveis para toda a coleção"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Arquivo da Anna gerencia uma coleção de <a %(ia)s>metadados do IA</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Arquivos disponíveis apenas para empréstimo de forma limitada, com várias restrições de acesso"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Arquivo da Anna gerencia uma coleção de <a %(ia)s>arquivos do IA</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Vários bancos de dados de metadados espalhados pela internet chinesa; embora muitas vezes sejam bancos de dados pagos"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Não há dumps de metadados facilmente acessíveis disponíveis para toda a coleção."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Arquivo da Anna gerencia uma coleção de <a %(duxiu)s>metadados do DuXiu</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Vários bancos de dados de arquivos espalhados pela internet chinesa; embora muitas vezes sejam bancos de dados pagos"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s A maioria dos arquivos só é acessível usando contas premium do BaiduYun; velocidades de download lentas."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Arquivo da Anna gerencia uma coleção de <a %(duxiu)s>arquivos do DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Várias fontes menores ou pontuais. Encorajamos as pessoas a fazerem upload para outras bibliotecas-sombra primeiro, mas às vezes as pessoas têm coleções grandes demais para outros organizarem, embora não grandes o suficiente para justificar sua própria categoria."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Fontes exclusivamente de metadados"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Também enriquecemos nossa coleção com fontes exclusivamente de metadados, que podemos então associar a arquivos, por exemplo, usando números ISBN ou outros campos. Abaixo está uma visão geral dessas fontes. Novamente, algumas dessas fontes são completamente abertas, enquanto outras precisamos extrair."

msgid "page.faq.metadata.inspiration"
msgstr "Nossa inspiração para coletar metadados é o objetivo de Aaron Swartz de “uma página da web para cada livro já publicado”, para o qual ele criou a <a %(a_openlib)s>Open Library</a>. Esse projeto tem se saído bem, mas nossa posição única nos permite obter metadados que eles não conseguem. Outra inspiração foi nosso desejo de saber <a %(a_blog)s>quantos livros existem no mundo</a>, para que possamos calcular quantos livros ainda temos para salvar."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Note que na busca de metadados, mostramos os registros originais. Não fazemos nenhuma mescla de registros."

msgid "page.datasets.sources.last_updated.header"
msgstr "Última atualização"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Dumps de banco de dados <a %(dbdumps)s>mensais</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Não disponível diretamente em massa, protegido contra scraping"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s O Arquivo da Anna gerencia uma coleção de <a %(worldcat)s>metadados OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Banco de dados unificado"

msgid "page.datasets.unified_database.text1"
msgstr "Combinamos todas as fontes acima em um banco de dados unificado que usamos para preencher este site. Este banco de dados unificado não está disponível diretamente, mas como o Arquivo da Anna é totalmente de código aberto, ele pode ser <a %(a_generated)s>gerado</a> ou <a %(a_downloaded)s>baixado</a> como bancos de dados ElasticSearch e MariaDB. Os scripts nessa página baixarão automaticamente todos os metadados necessários das fontes mencionadas acima."

msgid "page.datasets.unified_database.text2"
msgstr "Se você quiser explorar nossos dados antes de executar esses scripts localmente, pode olhar nossos arquivos JSON, que se vinculam então a outros arquivos JSON. <a %(a_json)s>Este arquivo</a> é um bom ponto de partida."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptado de nossa <a %(a_href)s>postagem no blog</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> é um banco de dados enorme de livros digitalizados, criado pelo <a %(superstar_link)s>SuperStar Digital Library Group</a>. A maioria são livros acadêmicos, digitalizados para torná-los disponíveis digitalmente para universidades e bibliotecas. Para nosso público de língua inglesa, <a %(princeton_link)s>Princeton</a> e a <a %(uw_link)s>University of Washington</a> têm bons resumos (overviews). Há também um excelente artigo que fornece mais contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Os livros do Duxiu têm sido pirateados na internet chinesa há muito tempo. Normalmente, eles são vendidos por menos de um dólar por revendedores. Eles são tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(link1)s>aqui</a> e <a %(link2)s>aqui</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Tivemos isso no topo da nossa lista de tarefas e alocamos vários meses de trabalho em tempo integral para isso. No entanto, no final de 2023, um voluntário incrível, maravilhoso e talentoso nos procurou, dizendo que já havia feito todo esse trabalho — a um grande custo. Ele compartilhou a coleção completa conosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente incrível."

msgid "page.datasets.common.resources"
msgstr "Recursos"

msgid "page.datasets.common.total_files"
msgstr "Total de arquivos: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Tamanho total dos arquivos: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Arquivos espelhados pelo Arquivo da Anna: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Última atualização: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrents feitos pelo Arquivo da Anna"

msgid "page.datasets.common.aa_example_record"
msgstr "Exemplo de registro no Arquivo da Anna"

msgid "page.datasets.duxiu.blog_post"
msgstr "Nossa postagem no blog sobre esses dados"

msgid "page.datasets.common.import_scripts"
msgstr "Scripts para importar metadados"

msgid "page.datasets.common.aac"
msgstr "Formato \"Containers do Arquivo da Anna\""

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mais informações de nossos voluntários (notas brutas):"

msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.ia.description"
msgstr "Este conjunto de dados está intimamente relacionado ao <a %(a_datasets_openlib)s>conjunto de dados da Open Library</a>. Ele contém uma raspagem / scraping de todos os metadados e uma grande parte dos arquivos da Biblioteca de Empréstimo Digital Controlado da IA. As atualizações são lançadas no <a %(a_aac)s>formato \"Containers do Arquivo da Anna\"</a>."

msgid "page.datasets.ia.description2"
msgstr "Esses registros são referenciados diretamente do conjunto de dados da Open Library, mas também contêm registros que não estão na Open Library. Também temos vários arquivos de dados raspados por membros da comunidade ao longo dos anos."

msgid "page.datasets.ia.description3"
msgstr "A coleção consiste em duas partes. Você precisa de ambas as partes para obter todos os dados (exceto torrents de substituição, que estão riscados na página de torrents)."

msgid "page.datasets.ia.part1"
msgstr "nosso primeiro lançamento, antes de padronizarmos no formato <a %(a_aac)s>Containers do Arquivo da Anna (AAC)</a>. Contém metadados (em json e xml), pdfs (dos sistemas de empréstimo digital acsm e lcpdf) e miniaturas de capas."

msgid "page.datasets.ia.part2"
msgstr "lançamentos incrementais, usando AAC. Contém apenas metadados com carimbos de data após 01-01-2023, já que o restante já está coberto por “ia”. Também todos os arquivos pdf, desta vez dos sistemas de empréstimo acsm e “bookreader” (leitor web do IA). Apesar do nome não ser exatamente correto, ainda populamos arquivos do bookreader na coleção ia2_acsmpdf_files, já que são mutuamente exclusivos."

msgid "page.datasets.common.main_website"
msgstr "Site principal %(source)s"

msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca de Empréstimo Digital"

msgid "page.datasets.common.metadata_docs"
msgstr "Documentação de metadados (a maioria dos campos)"

msgid "page.datasets.isbn_ranges.title"
msgstr "Informações sobre o país no ISBN"

msgid "page.datasets.isbn_ranges.text1"
msgstr "A Agência Internacional do ISBN regularmente divulga as faixas que foram alocadas para as agências nacionais de ISBN. A partir disso, podemos determinar a qual país, região ou grupo linguístico pertence esse ISBN. Atualmente, usamos esses dados indiretamente, através da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Recursos"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Última atualização: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Site do ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadados"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Para a história dos diferentes forks do Library Genesis, veja a página do <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "O Libgen.li contém a maior parte do mesmo conteúdo e metadados do Libgen.rs, mas possui algumas coleções adicionais, como quadrinhos, revistas e documentos comuns. Ele também integrou o <a %(a_scihub)s>Sci-Hub</a> em seus metadados e motor de busca, que é o que usamos para nosso banco de dados."

msgid "page.datasets.libgen_li.description3"
msgstr "Os metadados desta biblioteca estão disponíveis gratuitamente <a %(a_libgen_li)s>em libgen.li</a>. No entanto, este servidor é lento e não suporta a retomada de conexões interrompidas. Os mesmos arquivos também estão disponíveis em <a %(a_ftp)s>um servidor FTP</a>, que funciona melhor."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents estão disponíveis para a maioria do conteúdo adicional, especialmente, torrents para quadrinhos, revistas e documentos comuns, foram lançados em colaboração com o Arquivo da Anna."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "A coleção de ficção tem seus próprios torrents (divergente do <a %(a_href)s>Libgen.rs</a>) começando em %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "De acordo com o administrador do Libgen.li, a coleção “fiction_rus” (ficção russa) deve ser coberta por torrents lançados regularmente de <a %(a_booktracker)s>booktracker.org</a>, especialmente os torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que espelhamos <a %(a_torrents)s>aqui</a>, embora ainda não tenhamos estabelecido quais torrents correspondem a quais arquivos)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Estatísticas para todas as coleções podem ser encontradas <a %(a_href)s>no site do libgen</a>."

msgid "page.datasets.libgen_li.description4.1"
msgstr "A não-ficção também parece ter divergido, mas sem novos torrents. Parece que isso tem acontecido desde o início de 2022, embora não tenhamos verificado."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certas faixas sem torrents (como as faixas de ficção f_3463000 a f_4260000) são provavelmente arquivos da Z-Library (ou outros arquivos duplicados), embora estamos pensando em fazer uma desduplicação e criar torrents exclusivos para os arquivos lgli nessas faixas."

msgid "page.datasets.libgen_li.description5"
msgstr "Observe que os arquivos torrent que se referem a “libgen.is” são explicitamente espelhados de <a %(a_libgen)s>Libgen.rs</a> (“.is” é um domínio diferente usado pelo Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Um recurso útil para usar os metadados é <a %(a_href)s>esta página</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de ficção no Arquivo da Anna"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de quadrinhos no Arquivo da Anna"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de revistas no Arquivo da Anna"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos comuns no Arquivo da Anna"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de ficção russa no Arquivo da Anna"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadados"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadados via FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informações sobre campos de metadados"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Espelho de outros torrents (e torrents únicos de ficção e quadrinhos)"

msgid "page.datasets.libgen_li.forum"
msgstr "Fórum de discussão"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nosso post sobre o lançamento dos quadrinhos"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "A história rápida dos diferentes forks do Library Genesis (ou “Libgen”) é que, com o tempo, as diferentes pessoas envolvidas com o Library Genesis tiveram desentendimentos e seguiram caminhos separados."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "A versão “.fun” foi criada pelo fundador original. Está sendo reformulada em favor de uma nova versão, mais distribuída."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "A versão “.rs” tem dados muito semelhantes e lança sua coleção em torrents em massa de forma mais consistente. É aproximadamente dividida em uma seção de “ficção” e outra de “não-ficção”."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originalmente em “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "A <a %(a_li)s>versão “.li”</a> tem uma coleção enorme de quadrinhos, bem como outros conteúdos, que ainda não estão disponíveis para download em massa através de torrents. Também tem uma coleção separada de torrents de livros de ficção e contém os metadados do <a %(a_scihub)s>Sci-Hub</a> em seu banco de dados."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "De acordo com este <a %(a_mhut)s>post no fórum</a>, o Libgen.li foi originalmente hospedado em “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> de certa forma também é um fork do Library Genesis, embora tenham usado um nome diferente para seu projeto."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Esta página é sobre a versão “.rs”. É conhecida por publicar consistentemente tanto seus metadados quanto o conteúdo completo de seu catálogo de livros. Sua coleção de livros é dividida entre uma parte de ficção e outra de não-ficção."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Um recurso útil para usar os metadados é <a %(a_metadata)s>esta página</a> (bloqueia algumas faixas de IP, pode ser necessário usar VPN)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de março de 2024, novos torrents estão sendo postados neste <a %(a_href)s>tópico do fórum</a> (bloqueia algumas faixas de IP, pode ser necessário o uso de VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de Não-Ficção no Arquivo da Anna"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de Ficção no Arquivo da Anna"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadados do Libgen.rs"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informações sobre campos de metadados do Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de Não-Ficção do Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de Ficção do Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Fórum de Discussão do Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents feitos pelo Arquivo da Anna (capas de livros)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Nosso blog sobre o lançamento das capas de livros"

msgid "page.datasets.libgen_rs.about"
msgstr "A Library Genesis é conhecida por já disponibilizar generosamente seus dados em massa através de torrents. Nossa coleção do Libgen consiste em dados auxiliares que eles não liberam diretamente, disponibilizados em parceria com eles. Muito obrigado a todos envolvidos com a Library Genesis por trabalharem conosco!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Lançamento 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Este <a %(blog_post)s>primeiro lançamento</a> é bem pequeno: cerca de 300GB de capas de livros do fork Libgen.rs, tanto de ficção quanto de não-ficção. Elas estão organizadas da mesma forma que aparecem no libgen.rs, por exemplo:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s para um livro de não-ficção."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s para um livro de ficção."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Assim como na coleção da Z-Library, colocamos todos em um grande arquivo .tar, que pode ser montado usando <a %(a_ratarmount)s>ratarmount</a> se você quiser acessar os arquivos diretamente."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> é um banco de dados proprietário da organização sem fins lucrativos <a %(a_oclc)s>OCLC</a>, que agrega registros de metadados de bibliotecas de todo o mundo. Provavelmente é a maior coleção de metadados de bibliotecas do mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Outubro de 2023, lançamento inicial:"

msgid "page.datasets.worldcat.description2"
msgstr "Em outubro de 2023, <a %(a_scrape)s>lançamos</a> um grande scrapping de banco de dados da OCLC (WorldCat), no <a %(a_aac)s>formato \"Containers do Arquivo da Anna\"</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrents feitos pelo Arquivo da Anna"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nosso post no blog sobre esses dados"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "A Open Library é um projeto de código aberto do Internet Archive para catalogar todos os livros do mundo. Possui uma das maiores operações de digitalização de livros do mundo e tem muitos livros disponíveis para empréstimo digital. Seu catálogo de metadados de livros está disponível gratuitamente para download e está incluído no Arquivo da Anna (embora atualmente não esteja na busca, exceto se você procurar explicitamente por um ID da Open Library)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metadados"

msgid "page.datasets.isbndb.release1.title"
msgstr "Lançamento 1 (31/01/2022)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Este é um dump de muitas chamadas para isbndb.com durante setembro de 2022. Tentamos cobrir todas as faixas de ISBN. São cerca de 30,9 milhões de registros. No site deles, afirmam que possuem 32,6 milhões de registros, então podemos ter perdido alguns de alguma forma, ou <em>eles</em> podem estar fazendo algo errado."

msgid "page.datasets.isbndb.release1.text2"
msgstr "As respostas JSON são praticamente brutas do servidor deles. Um problema de qualidade de dados que notamos é que, para números ISBN-13 que começam com um prefixo diferente de “978-”, eles ainda incluem um campo “isbn” que simplesmente é o número ISBN-13 com os três primeiros números cortados (e o dígito de verificação recalculado). Isso é obviamente errado, mas é assim que eles parecem fazer, então não alteramos."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Outro problema potencial que você pode encontrar é o fato de que o campo “isbn13” tem duplicatas, então você não pode usá-lo como chave primária em um banco de dados. Os campos combinados “isbn13”+“isbn” parecem ser únicos."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Para obter mais informações sobre o Sci-Hub, consulte seu <a %(a_scihub)s>site oficial</a>, <a %(a_wikipedia)s>página na Wikipedia</a> e esta <a %(a_radiolab)s>entrevista em podcast</a>."

msgid "page.datasets.scihub.description2"
msgstr "Observe que o Sci-Hub está <a %(a_reddit)s>congelado desde 2021</a>. Ele já havia sido congelado antes, mas em 2021 alguns milhões de artigos foram adicionados. Ainda assim, um número limitado de artigos é adicionado às coleções “scimag” do Libgen, embora não o suficiente para justificar novos torrents em massa."

msgid "page.datasets.scihub.description3"
msgstr "Usamos os metadados do Sci-Hub fornecidos pelo <a %(a_libgen_li)s>Libgen.li</a> em sua coleção “scimag”. Também usamos o conjunto de dados <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Observe que os torrents “smarch” estão <a %(a_smarch)s>obsoletos</a> e, portanto, não estão incluídos em nossa lista de torrents."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents no Arquivo da Anna"

msgid "page.datasets.scihub.link_metadata"
msgstr "Metadados e torrents"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents no Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents no Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Atualizações no Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Página na Wikipedia"

msgid "page.datasets.scihub.link_podcast"
msgstr "Entrevista em podcast"

msgid "page.datasets.upload.title"
msgstr "Uploads para o Arquivo da Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Visão geral da <a %(a1)s>página de datasets</a>."

msgid "page.datasets.upload.description"
msgstr "Diversas fontes menores ou pontuais. Incentivamos as pessoas a fazerem upload em outras bibliotecas-sombra primeiro, mas às vezes as pessoas têm coleções que são grandes demais para que outros possam organizar, embora não sejam grandes o suficiente para justificar sua própria categoria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "A coleção de “uploads” é dividida em subcoleções menores, que são indicadas nos AACIDs e nos nomes dos torrents. Todas as subcoleções foram primeiro desduplicadas em relação à coleção principal, embora os arquivos JSON de metadados “upload_records” ainda contenham muitas referências aos arquivos originais. Arquivos que não são livros também foram removidos da maioria das subcoleções e, geralmente, <em>não</em> são mencionados no JSON “upload_records”."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Muitas subcoleções são compostas por sub-sub-coleções (por exemplo, de diferentes fontes originais), que são representadas como diretórios nos campos “filepath”."

msgid "page.datasets.upload.subs.heading"
msgstr "As subcoleções são:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcoleção"

msgid "page.datasets.upload.subs.notes"
msgstr "Anotações"

msgid "page.datasets.upload.action.browse"
msgstr "navegar"

msgid "page.datasets.upload.action.search"
msgstr "pesquisar"

msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bem completo. Do nosso voluntário “cgiym”."

msgid "page.datasets.upload.source.acm"
msgstr "De um torrent da <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tem uma sobreposição considerável com coleções de artigos existentes, mas muito poucos correspondem em MD5, então decidimos mantê-lo completamente."

msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), por voluntário <q>j</q>. Corresponde ao metadado de <q>airitibooks</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>."

msgid "page.datasets.upload.source.alexandrina"
msgstr "De uma coleção <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Pacialmente da fonte original, pacialmente de the-eye.eu, pacialmente de outros espelhos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "De um site privado de torrents de livros, <a %(a_href)s>Bibliotik</a> (frequentemente referido como “Bib”), cujos livros foram agrupados em torrents por nome (A.torrent, B.torrent) e distribuídos através do the-eye.eu."

msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Do nosso voluntário “bpb9v”. Para mais informações sobre <a %(a_href)s>CADAL</a>, veja as anotações na nossa <a %(a_duxiu)s>página do conjunto de dados DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mais do nosso voluntário “bpb9v”, principalmente arquivos DuXiu, bem como uma pasta “WenQu” e “SuperStar_Journals” (SuperStar é a empresa por trás do DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Do nosso voluntário “cgiym”, textos chineses de várias fontes (representados como subdiretórios), incluindo da <a %(a_href)s>China Machine Press</a> (uma grande editora chinesa)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleções não chinesas (representadas como subdiretórios) do nosso voluntário “cgiym”."

msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrapes de livros sobre arquitetura chinesa, pelo voluntário <q>cm</q>: <q>Consegui explorando uma vulnerabilidade de rede na editora, mas essa brecha já foi fechada</q>. Corresponde aos metadados de <q>chinese_architecture</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Livros da editora acadêmica <a %(a_href)s>De Gruyter</a>, coletados de alguns torrents grandes ."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape de <a %(a_href)s>docer.pl</a>, um site polonês de compartilhamento de arquivos focado em livros e outras obras escritas. Scrape realizado no final de 2023 pelo voluntário “p”. Não temos bons metadados do site original (nem mesmo extensões de arquivo), mas filtramos arquivos semelhantes a livros e muitas vezes conseguimos extrair metadados dos próprios arquivos."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epubs do DuXiu, diretamente do DuXiu, coletados pelo voluntário “w”. Apenas livros recentes do DuXiu estão disponíveis diretamente através de ebooks, então a maioria desses deve ser recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Arquivos restantes do DuXiu do voluntário “m”, que não estavam no formato proprietário PDG do DuXiu (o principal <a %(a_href)s>conjunto de dados do DuXiu</a>). Coletados de muitas fontes originais, infelizmente sem preservar essas fontes no caminho do arquivo."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

msgid "page.datasets.upload.source.hentai"
msgstr "Raspagem de livros eróticos, pelo voluntário <q>do no harm</q>. Corresponde aos metadados de <q>hentai</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

msgid "page.datasets.upload.source.japanese_manga"
msgstr "Coleção raspada de um editor japonês de Mangá pelo voluntário <q>t</q>.."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arquivos judiciais selecionados de Longquan</a>, fornecidos pelo voluntário “c”."

msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape de <a %(a_href)s>magzdb.org</a>, um aliado da Library Genesis (está vinculado na página inicial do libgen.rs) mas que não quis fornecer seus arquivos diretamente. Obtido pelo voluntário <q>p</q> no final de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

msgid "page.datasets.upload.source.misc"
msgstr "Diversos pequenos uploads, pequenos demais para serem uma subcoleção própria, mas representados como diretórios. O diretório <q>oo42hcksBxZYAOjqwGWu</q> corresponde aos metadados <q>czech_oo42hcks</q> em <a %(a1)s><q> outros scrapes de metadados</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks do AvaxHome, um site russo de compartilhamento de arquivos."

msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arquivo de jornais e revistas. Corresponde aos metadados de <q>newsarch_magz</q> em <a %(a1)s><q>Outras raspagens de metadados</q></a>."

msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape do <a %(a1)s>Philosophy Documentation Center</a>."

msgid "page.datasets.upload.source.polish"
msgstr "Coleção do voluntário <q>o</q> que coletou livros poloneses diretamente dos sites de lançamento original (<q>scene</q>)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coleções combinadas de <a %(a_href)s>shuge.org</a> pelos voluntários “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomeada em homenagem à biblioteca fictícia), raspada em 2022 pelo voluntário “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-coleções (representadas como diretórios) do voluntário “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> em Taiwan), mebook (mebook.cc, 我的小书屋, meu pequeno quarto de livros — woz9ts: “Este site foca principalmente em compartilhar arquivos de ebooks de alta qualidade, alguns dos quais são diagramados pelo próprio dono. O dono foi <a %(a_arrested)s>preso</a> em 2019 e alguém fez uma coleção dos arquivos que ele compartilhou.”)."

msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Arquivos restantes do DuXiu do voluntário <q>woz9ts</q>, que não estavam no formato proprietário PDG do DuXiu (ainda a serem convertidos para PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents feitos pelo Arquivo da Anna"

msgid "page.datasets.zlib.title"
msgstr "Scrape do Z-Library"

msgid "page.datasets.zlib.description.intro"
msgstr "O Z-Library tem suas raízes na comunidade <a %(a_href)s>Library Genesis</a> e originalmente começou com os dados deles. Desde então, profissionalizou-se consideravelmente e possui uma interface muito mais moderna. Portanto, eles conseguem obter muitas mais doações, tanto monetárias para continuar melhorando seu site, quanto doações de novos livros. Eles acumularam uma grande coleção além da Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Atualização de fevereiro de 2023."

msgid "page.datasets.zlib.description.allegations"
msgstr "No final de 2022, os supostos fundadores do Z-Library foram presos e os domínios foram apreendidos pelas autoridades dos Estados Unidos. Desde então, o site tem lentamente voltado a ficar online. Não se sabe quem o administra atualmente."

msgid "page.datasets.zlib.description.three_parts"
msgstr "A coleção consiste em três partes. As páginas de descrição originais para as duas primeiras partes estão preservadas abaixo. Você precisa das três partes para obter todos os dados (exceto torrents de substituição, que estão riscados na página de torrents)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nosso primeiro lançamento. Este foi o primeiro lançamento do que então era chamado de “Pirate Library Mirror” (“pilimi”)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: segundo lançamento, desta vez com todos os arquivos comprimidos em arquivos .tar."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: lançamentos incrementais, usando o formato <a %(a_href)s>Containers do Arquivo da Anna (AAC)</a>, agora lançados em colaboração com a equipe da Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents feitos pelo Arquivo da Anna (metadados + conteúdo)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplo de registro no Arquivo da Anna (coleção original)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplo de registro no Arquivo da Anna (coleção “zlib3”)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Site principal"

msgid "page.datasets.zlib.link.onion"
msgstr "Domínio Tor"

msgid "page.datasets.zlib.blog.release1"
msgstr "Postagem no blog sobre o Lançamento 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Postagem no blog sobre o Lançamento 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Lançamentos do Zlib (páginas de descrição originais)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Lançamento 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "O espelhamento inicial foi obtido meticulosamente ao longo de 2021 e 2022. Neste ponto, está ligeiramente desatualizado: reflete o estado da coleção em junho de 2021. Atualizaremos isso no futuro. No momento, estamos focados em lançar esta primeira versão."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Como o Library Genesis já está preservado com torrents públicos e está incluído na Z-Library, fizemos uma deduplicação básica contra o Library Genesis em junho de 2022. Para isso, usamos hashes MD5. Provavelmente há muito mais conteúdo duplicado na biblioteca, como vários formatos de arquivo do mesmo livro. Isso é difícil de detectar com precisão, então não o fazemos. Após a deduplicação, continuamos com mais de 2 milhões de arquivos, totalizando pouco menos de 7TB."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "A coleção consiste em duas partes: um dump MySQL “.sql.gz” dos metadados e os 72 arquivos torrent de cerca de 50-100GB cada. Os metadados contêm os dados conforme relatados pelo site da Z-Library (título, autor, descrição, tipo de arquivo), bem como o tamanho real do arquivo e o md5sum que observamos, já que às vezes esses não coincidem. Parece haver faixas de arquivos para os quais a própria Z-Library tem metadados incorretos. Também podemos ter baixado arquivos incorretamente em alguns casos isolados, que tentaremos detectar e corrigir no futuro."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Os arquivos de torrent grandes contêm os dados reais dos livros, com o ID da Z-Library como nome do arquivo. As extensões de arquivo podem ser reconstruídas usando o dump de metadados."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "A coleção é uma mistura de conteúdo de não-ficção e ficção (não estão separadas como no Library Genesis). A qualidade também varia amplamente."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Este primeiro lançamento está agora totalmente disponível. Note que os arquivos torrent estão disponíveis apenas através do nosso espelho Tor."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Lançamento 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Obtivemos todos os livros que foram adicionados à Z-Library entre nosso último espelhamento e agosto de 2022. Também voltamos e raspamos alguns livros que perdemos na primeira vez. No total, esta nova coleção tem cerca de 24TB. Novamente, esta coleção é deduplicada em relação ao Library Genesis, já que já existem torrents disponíveis para essa coleção."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Os dados estão organizados de forma semelhante ao primeiro lançamento. Há um dump MySQL “.sql.gz” dos metadados, que também inclui todos os metadados do primeiro lançamento, substituindo-o. Também adicionamos algumas novas colunas:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: se este arquivo já está no Library Genesis, seja na coleção de não-ficção ou ficção (correspondido pelo md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: em qual torrent este arquivo está."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: definido quando não conseguimos baixar o livro."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mencionamos isso da última vez, mas só para esclarecer: “filename” e “md5” são as propriedades reais do arquivo, enquanto “filename_reported” e “md5_reported” são as informações que extraímos do Z-Library. Às vezes, esses dois não coincidem, então incluímos ambos."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Para esta versão, mudamos a \"collation\" para “utf8mb4_unicode_ci”, que deve ser compatível com versões mais antigas do MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Os arquivos de dados são semelhantes aos da última vez, embora sejam muito maiores. Simplesmente não nos preocupamos em criar toneladas de arquivos torrent menores. “pilimi-zlib2-0-14679999-extra.torrent” contém todos os arquivos que perdemos na última versão, enquanto os outros torrents são todos novos intervalos de ID. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Atualização %(date)s:</strong> Fizemos a maioria dos nossos torrents muito grandes, causando dificuldades nos clientes de torrent. Nós os removemos e lançamos novos torrents."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Atualização %(date)s:</strong> Ainda havia muitos arquivos, então os compactamos em arquivos tar e lançamos novos torrents novamente."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Adendo da Versão 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Este é um único arquivo torrent extra. Ele não contém nenhuma informação nova, mas tem alguns dados que podem demorar para serem computados. Isso o torna conveniente, pois baixar este torrent é frequentemente mais rápido do que computá-lo do zero. Em particular, ele contém índices SQLite para os arquivos tar, para uso com <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Perguntas frequentes (FAQ)"

msgid "page.faq.what_is.title"
msgstr "O que é Arquivo da Anna?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Arquivo da Anna</span> é um projeto sem fins lucrativos com dois objetivos:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Preservação:</strong> Fazer o back up de todo o conhecimento e cultura da humanidade.</li><li><strong>Acesso:</strong> Tornar esse conhecimento e cultura disponíveis a qualquer pessoa no mundo.</li>"

msgid "page.home.intro.open_source"
msgstr "Todos os nossos <a %(a_code)s>códigos</a> e <a %(a_datasets)s>dados</a> são completamente abertos."

msgid "page.home.preservation.header"
msgstr "Preservação"

msgid "page.home.preservation.text1"
msgstr "Preservamos livros, artigos, quadrinhos, revistas e muito mais, trazendo esses materiais de várias <a href=\"https://pt.wikipedia.org/wiki/Shadow_library\">bibliotecas-sombra</a>, bibliotecas oficiais e outras coleções juntas em um só lugar. Todos esses dados são preservados para sempre, facilitando a duplicação em massa — usando torrents — resultando em muitas cópias em todo o mundo. Algumas bibliotecas-sombra já fazem isso sozinhas (por exemplo, Sci-Hub, Library Genesis), enquanto o Arquivo da Anna “libera” outras bibliotecas que não oferecem distribuição em massa (por exemplo, Z-Library) ou que não são bibliotecas-sombra (por exemplo, Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Essa ampla distribuição, combinada ao código aberto, torna nosso website resiliente contra quedas, e garante a preservação a longo prazo do conhecimento e da cultura da humanidade. Saiba mais sobre <a href=\"/datasets\">nossos datasets</a>."

msgid "page.home.preservation.label"
msgstr "Estimamos que já preservamos cerca de <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% dos livros do mundo</a>."

msgid "page.home.access.header"
msgstr "Acesso"

msgid "page.home.access.text"
msgstr "Trabalhamos com parceiros para manter nossa coleção acessível e gratuita para todos. Acreditamos que todos tem o direito de acesso ao conhecimento coletivo da Humanidade. E <a %(a_search)s>não a custo de autores</a>."

msgid "page.home.access.label"
msgstr "Downloads por hora nos últimos 30 dias. Média por hora: %(hourly)s. Média diária: %(daily)s."

msgid "page.about.text2"
msgstr "Acreditamos na livre circulação da informação e na preservação do conhecimento e da cultura. Com esse motor de busca construímos em cima de projetos de enorme importância. Respeitamos profundamente o trabalho duro das pessoas que criaram diversas bibliotecas-sombra e acreditamos que este motor de busca irá ampliar seu alcance."

msgid "page.about.text3"
msgstr "Para saber como está nosso trabalho, siga a Anna no <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Para perguntas e opiniões, por favor envie um email para a Anna em %(email)s."

msgid "page.faq.help.title"
msgstr "Como posso ajudar?"

msgid "page.about.help.text"
msgstr "<li>1. Siga-nos no <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Divulgue Arquivo da Anna no Twitter, Reddit, TikTok, Instagram, em sua cafeteria local, biblioteca ou onde quer que vá! Não acreditamos em restrições de acesso - se formos removidos, simplesmente surgiremos em outro lugar, já que todo nosso código e dados são totalmente abertos.</li><li>3. Se puder, considere <a href=\"/donate\">fazer uma doação</a>.</li><li>4. Ajude a <a href=\"https://translate.annas-software.org/\">traduzir</a> nosso site para diferentes idiomas.</li><li>5. Se você é um engenheiro de software, considere contribuir para nosso <a href=\"https://annas-software.org/\">código aberto</a> ou compartilhar nossos <a href=\"/datasets\">torrents</a>.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "Agora também temos um canal Matrix sincronizado em %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Se você é um pesquisador de segurança, podemos utilizar suas habilidades tanto para ataque quanto para defesa. Confira nossa página de <a %(a_security)s>Segurança</a>."

msgid "page.about.help.text7"
msgstr "7. Estamos procurando por especialistas em pagamentos para comerciantes anônimos. Você pode nos ajudar a adicionar formas mais convenientes de doação? PayPal, WeChat, cartões-presente (gift cards). Se você conhece alguém, por favor, entre em contato conosco."

msgid "page.about.help.text8"
msgstr "8. Estamos sempre em busca de mais capacidade de servidor."

msgid "page.about.help.text9"
msgstr "9. Você pode ajudar relatando problemas com arquivos, deixando comentários e criando listas diretamente neste site. Você também pode ajudar fazendo <a %(a_upload)s>upload de mais livros</a>, corrigindo problemas de arquivos ou de formatação nos livros existentes."

msgid "page.about.help.text10"
msgstr "10. Crie ou ajude a manter a página da Wikipedia do Acervo da Anna em seu idioma."

msgid "page.about.help.text11"
msgstr "11. Colocaremos anúncios pequenos e de bom gosto. Se gostaria de anunciar no Acervo da Anna, por favor nos avise."

msgid "page.faq.help.mirrors"
msgstr "Adoraríamos que as pessoas instalassem <a %(a_mirrors)s>espelhos</a> e apoiaremos financeiramente isso."

msgid "page.about.help.volunteer"
msgstr "Para informações mais detalhadas sobre como ser voluntário, veja nossa página de <a %(a_volunteering)s>Voluntariado & Recompensas</a>."

msgid "page.faq.slow.title"
msgstr "Por que os downloads lentos são tão lentos?"

msgid "page.faq.slow.text1"
msgstr "Literalmente, não temos recursos suficientes para oferecer downloads em alta velocidade a todas as pessoas do mundo, tanto quanto gostaríamos. Se um benfeitor rico quisesse dar um passo à frente e fornecer isso para nós, seria incrível, mas até lá, estamos dando o nosso melhor. Somos um projeto sem fins lucrativos que mal consegue se sustentar com doações."

msgid "page.faq.slow.text2"
msgstr "Por isso implementamos dois sistemas de downloads gratuitos, com nossos parceiros: servidores compartilhados com downloads lentos, e servidores um pouco mais rápidos com lista de espera (para reduzir o número de pessoas baixando ao mesmo tempo)."

msgid "page.faq.slow.text3"
msgstr "Também temos <a %(a_verification)s>verificação de navegador</a> para nossos downloads lentos, caso contrário, bots e scrapers irão abusar deles, tornando as coisas ainda mais lentas para usuários legítimos."

msgid "page.faq.slow.text4"
msgstr "Note que, ao usar o Navegador Tor, você pode precisar ajustar suas configurações de segurança. Na opção mais baixa, chamada “Padrão”, o desafio do Cloudflare turnstile funciona. Mas nas opções mais altas, chamadas “Mais seguro” e “Mais seguro ainda”, o desafio falha."

msgid "page.faq.slow.text5"
msgstr "Para arquivos grandes, às vezes downloads lentos podem ser interrompidos no meio. Recomendamos usar um gerenciador de downloads (como o JDownloader) para retomar automaticamente downloads grandes."

msgid "page.donate.faq.title"
msgstr "Dúvidas frequentes sobre doações"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>As assinaturas são renovadas automaticamente?</div> As assinaturas <strong>não</strong> são renovadas automaticamente. Você pode participar por quanto tempo quiser."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Posso atualizar minha assinatura ou obter várias assinaturas?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Vocês usam outras formas de pagamento?</div> Atualmente não. Muitas pessoas não querem que arquivos como este existam, então precisamos tomar cuidado. Se você pode nos ajudar a usar alguma outra forma de pagamento com segurança, entre em contato no %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>O que significam as faixas por mês?</div> Você pode alcançar o lado mais baixo de uma faixa aplicando todos os descontos, como escolher um período superior a um mês."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Como vocês usam as doações?</div> 100%% do valor é usado para preservar e tornar o conhecimento e a cultura do mundo acessíveis. Atualmente nós gastamos a maior parte em servidores, armazenamento e largura de banda. Nenhum dinheiro vai pessoalmente para membros do time."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Posso fazer uma doação grande?</div> Seria incrível! Para doações acima de mil dólares, por favor entre em contato em %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Posso fazer uma doação sem me tornar membro?</div> Claro. Aceitamos doações de qualquer valor neste endereço Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "Como faço para enviar novos livros?"

msgid "page.upload.zlib.text1"
msgstr "Como alternativa, você pode carregá-los na Z-Library <a %(a_upload)s>aqui</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Para pequenos uploads (até 10.000 arquivos), por favor, envie-os tanto para %(first)s quanto para %(second)s."

msgid "page.upload.text1"
msgstr "Por enquanto, sugerimos o upload de novos livros para as bifurcações do Library Genesis. Aqui está um <a %(a_guide)s>guia prático</a>. Observe que ambas as bifurcações que indexamos neste site são extraídas desse mesmo sistema de upload."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Para o Libgen.li, certifique-se de primeiro fazer login no <a %(a_forum)s>fórum deles</a> com o nome de usuário %(username)s e senha %(password)s, e depois retornar à <a %(a_upload_page)s>página de upload</a> deles."

msgid "common.libgen.email"
msgstr "Se o seu endereço de e-mail não funcionar nos fóruns Libgen, recomendamos usar <a %(a_mail)s>Proton Mail</a> (gratuito). Você também pode <a %(a_manual)s>solicitar manualmente</a> a ativação de sua conta."

msgid "page.faq.mhut_upload"
msgstr "Observe que mhut.org bloqueia determinados intervalos de IP, portanto, pode ser necessária uma VPN."

msgid "page.upload.large.text"
msgstr "Para envios grandes (acima de 10,000 arquivos) que não foram aceitos pela Libgen ou Z-Library, por favor nos contate por %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Para fazer upload de trabalhos acadêmicos, faça upload também (além do Library Genesis) para <a %(a_stc_nexus)s>STC Nexus</a>. Eles são a melhor shadow library para novos artigos. Ainda não os integramos, mas iremos em algum momento. Você pode usar o <a %(a_telegram)s>bot de upload no Telegram</a> ou entrar em contato com o endereço listado na mensagem fixada se tiver muitos arquivos para enviar dessa forma."

msgid "page.faq.request.title"
msgstr "Como faço para solicitar livros?"

msgid "page.request.cannot_accomodate"
msgstr "Atualmente, não podemos atender pedidos de livros."

msgid "page.request.forums"
msgstr "Por favor, faça suas solicitações nos fóruns Z-Library ou Libgen."

msgid "page.request.dont_email"
msgstr "Não nos envie por e-mail seus pedidos de livros."

msgid "page.faq.metadata.title"
msgstr "Você coleta metadados?"

msgid "page.faq.metadata.indeed"
msgstr "Nós realmente fazemos."

msgid "page.faq.1984.title"
msgstr "Baixei 1984 de George Orwell, a polícia vai bater na minha porta?"

msgid "page.faq.1984.text"
msgstr "Não se preocupe muito, há muitas pessoas baixando de sites vinculados por nós e é extremamente raro ter problemas. No entanto, para se manter seguro, recomendamos usar uma VPN (paga) ou <a %(a_tor)s>Tor</a> (gratuita)."

msgid "page.faq.save_search.title"
msgstr "Como faço para salvar minhas configurações de pesquisa?"

msgid "page.faq.save_search.text1"
msgstr "Selecione as configurações desejadas, mantenha a caixa de pesquisa vazia, clique em “Pesquisar” e adicione a página aos favoritos usando o recurso de favoritos do seu navegador."

msgid "page.faq.mobile.title"
msgstr "Você tem um aplicativo móvel?"

msgid "page.faq.mobile.text1"
msgstr "Não temos um aplicativo móvel oficial, mas você pode instalar este site como um aplicativo."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> clique no menu de três pontos no canto superior direito e selecione “Adicionar à tela inicial”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> clique no botão “Compartilhar” na parte inferior e selecione “Adicionar à tela inicial”."

msgid "page.faq.api.title"
msgstr "Você tem uma API?"

msgid "page.faq.api.text1"
msgstr "Temos uma API JSON estável para membros, para obter uma URL de download rápido: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentação no próprio JSON)."

msgid "page.faq.api.text2"
msgstr "Para outros casos de uso, como iterar todos os nossos arquivos, criar pesquisas personalizadas e assim por diante, recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente <a %(a_explore)s>por meio de arquivos JSON</a>."

msgid "page.faq.api.text3"
msgstr "Nossa lista bruta de torrents também pode ser baixada como <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "Perguntas frequentes sobre torrents"

msgid "page.faq.torrents.q1"
msgstr "Gostaria de ajudar semeando no torrent, mas não tenho muito espaço em disco."

msgid "page.faq.torrents.a1"
msgstr "Use o <a %(a_list)s>gerador de lista de torrents</a> para gerar uma lista de torrents que mais precisam de torrent, dentro dos seus limites de espaço de armazenamento."

msgid "page.faq.torrents.q2"
msgstr "Os torrents são muito lentos; posso baixar os dados diretamente de você?"

msgid "page.faq.torrents.a2"
msgstr "Sim, consulte a página <a %(a_llm)s>dados LLM</a>."

msgid "page.faq.torrents.q3"
msgstr "Posso baixar apenas um subconjunto de arquivos, como apenas um idioma ou tópico específico?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Resposta curta: não facilmente."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Resposta longa:"

msgid "page.faq.torrents.a3"
msgstr "A maioria dos torrents contém os arquivos diretamente, o que significa que você pode instruir os clientes de torrent a baixar apenas os arquivos necessários. Para determinar quais arquivos baixar, você pode <a %(a_generate)s>gerar</a> nossos metadados, ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Infelizmente, várias coleções de torrents contêm arquivos .zip ou .tar na raiz, nesse caso você precisará baixar o torrent inteiro antes de poder selecionar arquivos individuais."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Entretanto, temos <a %(a_ideas)s>algumas ideias</a> para o último caso.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Ainda não há ferramentas fáceis de usar para filtrar torrents, mas aceitamos contribuições."

msgid "page.faq.torrents.q4"
msgstr "Como você lida com duplicatas nos torrents?"

msgid "page.faq.torrents.a4"
msgstr "Tentamos manter o mínimo de duplicação ou sobreposição entre os torrents desta lista, mas isso nem sempre pode ser alcançado e depende muito das políticas das bibliotecas de origem. Para bibliotecas que lançam seus próprios torrents, isso está fora de nosso controle. Para torrents lançados pelo Arquivo da Anna, desduplicamos apenas com base no hash MD5, o que significa que versões diferentes do mesmo livro não são desduplicadas."

msgid "page.faq.torrents.q5"
msgstr "Posso obter a lista de torrents como JSON?"

msgid "page.faq.torrents.a5"
msgstr "Sim."

msgid "page.faq.torrents.q6"
msgstr "Não vejo PDFs ou EPUBs nos torrents, apenas arquivos binários? O que eu faço?"

msgid "page.faq.torrents.a6"
msgstr "Na verdade, são PDFs e EPUBs, mas não têm extensão em muitos de nossos torrents. Existem dois locais onde você pode encontrar os metadados de arquivos torrent, incluindo os tipos/extensões de arquivo:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Cada coleção ou lançamento possui seus próprios metadados. Por exemplo, <a %(a_libgen_nonfic)s>torrents Libgen.rs</a> têm um banco de dados de metadados correspondente hospedado no site Libgen.rs. Normalmente criamos links para recursos de metadados relevantes da <a %(a_datasets)s>página do conjunto de dados</a> de cada coleção."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Ele contém um mapeamento para cada registro no arquivo da Anna para seus arquivos torrent correspondentes (se disponíveis), em “torrent_paths” no ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Por que meu cliente de torrent não consegue abrir alguns dos seus arquivos torrent / links magnet?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Alguns clientes de torrent não suportam tamanhos de peça grandes, que muitos dos nossos torrents possuem (para os mais novos, não fazemos mais isso — mesmo que seja válido pelas especificações!). Portanto, tente um cliente diferente se encontrar esse problema, ou reclame com os desenvolvedores do seu cliente de torrent."

msgid "page.faq.security.title"
msgstr "Você tem um programa de divulgação responsável?"

msgid "page.faq.security.text1"
msgstr "Damos as boas-vindas aos pesquisadores de segurança para procurar vulnerabilidades em nossos sistemas. Somos grandes defensores da divulgação responsável. Entre em contato conosco <a %(a_contact)s>aqui</a>."

msgid "page.faq.security.text2"
msgstr "No momento, não podemos conceder recompensas por bugs, exceto por vulnerabilidades que tenham <a %(a_link)s >potencial de comprometer nosso anonimato</a>, para as quais oferecemos recompensas na faixa de US$ 10 mil a US$ 50 mil. Gostaríamos de oferecer um escopo mais amplo para recompensas de bugs no futuro! Observe que os ataques de engenharia social estão fora do escopo."

msgid "page.faq.security.text3"
msgstr "Se você está interessado em segurança ofensiva e deseja ajudar a arquivar o conhecimento e a cultura do mundo, entre em contato conosco. Há muitas maneiras pelas quais você pode ajudar."

msgid "page.faq.resources.title"
msgstr "Existem mais recursos sobre o Arquivo da Anna?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog da Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — atualizações regulares"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software da Anna</a> — nosso código-fonte aberto"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduza no software da Anna</a> — nosso sistema de tradução"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Conjuntos de dados</a> — sobre os dados"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domínios alternativos"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mais sobre nós (por favor, ajude a manter esta página atualizada ou crie uma para seu próprio idioma!)"

msgid "page.faq.copyright.title"
msgstr "Como faço para denunciar violação de direitos autorais?"

msgid "page.faq.copyright.text1"
msgstr "Não hospedamos nenhum material protegido por direitos autorais aqui. Somos um mecanismo de busca e, como tal, indexamos apenas metadados que já estão disponíveis publicamente. Ao fazer download dessas fontes externas, sugerimos que você verifique as leis da sua jurisdição com relação ao que é permitido. Não nos responsabilizamos por conteúdo hospedado por terceiros."

msgid "page.faq.copyright.text2"
msgstr "Se você tiver reclamações sobre o que vê aqui, sua melhor aposta é entrar em contato com o site original. Colocamos regularmente suas alterações em nosso banco de dados. Se você realmente acha que tem uma reclamação válida de DMCA à qual devemos responder, preencha o <a %(a_copyright)s>formulário de reivindicação de DMCA/Direitos autorais</a>. Levamos suas reclamações a sério e entraremos em contato com você o mais breve possível."

msgid "page.faq.hate.title"
msgstr "Odeio como você está executando este projeto!"

msgid "page.faq.hate.text1"
msgstr "Gostaríamos também de lembrar a todos que todo o nosso código e dados são totalmente de código aberto. Isso é exclusivo para projetos como o nosso — não temos conhecimento de nenhum outro projeto com um catálogo igualmente massivo que também seja totalmente de código aberto. Damos as boas-vindas a qualquer pessoa que pense que executamos mal nosso projeto para pegar nosso código e dados e configurar sua própria shadow library! Não estamos dizendo isso por despeito ou algo assim – realmente achamos que isso seria incrível, já que aumentaria o padrão para todos e preservaria melhor o legado da humanidade."

msgid "page.faq.uptime.title"
msgstr "Você tem um monitor de tempo de atividade?"

msgid "page.faq.uptime.text1"
msgstr "Por favor, veja <a %(a_href)s>este excelente projeto</a>."

msgid "page.faq.physical.title"
msgstr "Como posso doar livros ou outros materiais físicos?"

msgid "page.faq.physical.text1"
msgstr "Por favor, envie-os para o <a %(a_archive)s>Internet Archive</a>. Eles os preservarão adequadamente."

msgid "page.faq.anna.title"
msgstr "Quem é Anna?"

msgid "page.faq.anna.text1"
msgstr "Você é Anna!"

msgid "page.faq.favorite.title"
msgstr "Quais são seus livros favoritos?"

msgid "page.faq.favorite.text1"
msgstr "Aqui estão alguns livros que têm um significado especial para o mundo das shadow libraries e da preservação digital:"

msgid "page.fast_downloads.no_more_new"
msgstr "Você ficou sem downloads rápidos hoje."

msgid "page.fast_downloads.no_member"
msgstr "Torne-se um membro para usar downloads rápidos."

msgid "page.fast_downloads.no_member_2"
msgstr "Agora aceitamos cartões-presente da Amazon, cartões de crédito e débito, criptomoedas, Alipay e WeChat."

msgid "page.home.full_database.header"
msgstr "Base de dados completa"

msgid "page.home.full_database.subtitle"
msgstr "Livros, artigos, revistas, quadrinhos, registro de biblioteca, metadados, …"

msgid "page.home.full_database.search"
msgstr "Buscar"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub <a %(a_paused)s>pausou</a> o upload de novos artigos."

msgid "page.home.scidb.continuation"
msgstr "🧬 &nbsp;SciDB é uma continuação do Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Acesso direto a %(count)s artigos acadêmicos"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Abrir"

msgid "page.home.scidb.browser_verification"
msgstr "Caso você seja um <a %(a_member)s>membro</a>, não é necessário a verificação do browser."

msgid "page.home.archive.header"
msgstr "Arquivo de longo prazo"

msgid "page.home.archive.body"
msgstr "Os conjuntos de dados usados no Arquivo da Anna são completamente abertos e podem ser espelhados em massa usando torrents. <a %(a_datasets)s>Saiba mais…</a>"

msgid "page.home.torrents.body"
msgstr "Você pode ajudar enormemente semeando torrents. <a %(a_torrents)s>Saiba mais…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s semeadores"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s semeadores"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s semeadores"

msgid "page.home.llm.header"
msgstr "Dados para treinamento de LLM"

msgid "page.home.llm.body"
msgstr "Nós temos a maior coleção do mundo de dados de texto de alta qualidade. <a %(a_llm)s>Mais informações...</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: recrutando voluntários"

msgid "page.home.volunteering.header"
msgstr "🤝 Procurando voluntários"

msgid "page.home.volunteering.help_out"
msgstr "Como um projeto sem fins lucrativos e de código aberto, estamos sempre procurando pessoas para ajudar."

msgid "page.home.payment_processor.body"
msgstr "Se administra um processador de pagamentos anônimo de alto risco, entre em contato conosco. Também procuramos pessoas que desejam colocar pequenos anúncios de bom gosto. Todos os rendimentos vão para nossos esforços de preservação."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog da Anna ↗"

msgid "page.ipfs_downloads.title"
msgstr "Downloads de IPFS"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Todos os links para download deste arquivo</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Gateway IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(talvez seja necessário tentar várias vezes com IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Para ter downloads mais rápidos, <a %(a_membership)s>vire um membro</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Para fazer um mirror em massa da nossa coleção, cheque as páginas de <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

msgid "page.llm.title"
msgstr "Dados para LLM"

msgid "page.llm.intro"
msgstr "É bem compreendido que os LLMs prosperam com dados de alta qualidade. Temos a maior coleção de livros, artigos, revistas, etc. do mundo, que são algumas das fontes de texto de mais alta qualidade."

msgid "page.llm.unique_scale"
msgstr "Escala e alcance únicos"

msgid "page.llm.unique_scale.text1"
msgstr "Nossa coleção contém mais de cem milhões de arquivos, incluindo periódicos acadêmicos, livros didáticos e revistas. Alcançamos essa escala combinando grandes repositórios existentes."

msgid "page.llm.unique_scale.text2"
msgstr "Algumas de nossas coleções de origem já estão disponíveis em massa (Sci-Hub e partes do Libgen). Outras fontes nós mesmos liberamos. <a %(a_datasets)s>Datasets</a> mostra uma visão geral completa."

msgid "page.llm.unique_scale.text3"
msgstr "Nossa coleção inclui milhões de livros, artigos e revistas de antes da era dos e-books. Grandes partes dessa coleção já foram convertidas para texto e já têm pouca sobreposição interna."

msgid "page.llm.how_we_can_help"
msgstr "Como podemos ajudar"

msgid "page.llm.how_we_can_help.text1"
msgstr "Podemos fornecer acesso em alta velocidade às nossas coleções completas, bem como a coleções não lançadas."

msgid "page.llm.how_we_can_help.text2"
msgstr "Este é um acesso em nível empresarial que podemos fornecer por doações na faixa de dezenas de milhares de dólares. Também estamos dispostos a trocar isso por coleções de alta qualidade que ainda não temos."

msgid "page.llm.how_we_can_help.text3"
msgstr "Podemos reembolsá-lo se você puder nos fornecer melhoramento de nossos dados, como:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR (Reconhecimento ótico de caracteres)"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Remoção de copias (deduplicação)"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Extração de texto e metadados"

msgid "page.llm.how_we_can_help.text4"
msgstr "Apoie o arquivamento a longo prazo do conhecimento humano, enquanto obtém dados melhores para o seu modelo!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Entre em contato</a> para discutir como podemos trabalhar juntos."

msgid "page.login.continue"
msgstr "Continuar"

msgid "page.login.please"
msgstr "Por favor, <a %(a_account)s>faça login</a> para visualizar esta página.</a>"

msgid "page.maintenance.header"
msgstr "O Arquivo da Anna está temporariamente fora do ar para manutenção. Por favor, volte em uma hora."

msgid "page.metadata.header"
msgstr "Melhorar metadados"

msgid "page.metadata.body1"
msgstr "Você pode ajudar na preservação de livros melhorando os metadados! Primeiro, leia o contexto sobre metadados no Arquivo da Anna e, em seguida, aprenda como melhorar os metadados através da vinculação com a Open Library, e ganhe uma assinatura gratuita no Arquivo da Anna."

msgid "page.metadata.background.title"
msgstr "Contexto"

msgid "page.metadata.background.body1"
msgstr "Ao consultar um livro no Arquivo da Anna, você pode ver vários campos: título, autor, editora, edição, ano, descrição, nome do arquivo e muito mais. Todas essas informações são chamadas de <em>metadados</em>."

msgid "page.metadata.background.body2"
msgstr "Como combinamos livros de várias <em>bibliotecas de origem</em>, mostramos todos os metadados disponíveis nessa biblioteca de origem. Por exemplo, para um livro obtido na Library Genesis, mostraremos o título do banco de dados da Library Genesis."

msgid "page.metadata.background.body3"
msgstr "Às vezes, um livro está presente em <em>múltiplas</em> bibliotecas de origem, que podem ter diferentes campos de metadados. Nesse caso, simplesmente mostramos a versão mais longa de cada campo, pois essa, esperamos, contém as informações mais úteis! Ainda mostraremos os outros campos abaixo da descrição, por exemplo, como \"título alternativo\" (mas apenas se forem diferentes)."

msgid "page.metadata.background.body4"
msgstr "Também extraímos <em>códigos</em> como identificadores e classificadores da biblioteca de origem. <em>Identificadores</em> representam exclusivamente uma edição específica de um livro; exemplos são ISBN, DOI, ID da Open Library, ID do Google Books ou ID da Amazon. <em>Classificadores</em> agrupam vários livros semelhantes; exemplos são Dewey Decimal (DCC), UDC, LCC, RVK ou GOST. Às vezes, esses códigos estão explicitamente vinculados nas bibliotecas de origem, e às vezes podemos extraí-los do nome do arquivo ou da descrição (principalmente ISBN e DOI)."

msgid "page.metadata.background.body5"
msgstr "Podemos usar identificadores para encontrar registros em <em>coleções apenas de metadados</em>, como OpenLibrary, ISBNdb ou WorldCat/OCLC. Há uma aba específica de <em>metadados</em> em nosso mecanismo de busca se você quiser navegar por essas coleções. Usamos registros correspondentes para preencher campos de metadados ausentes (por exemplo, se um título estiver faltando), ou, por exemplo, como \"título alternativo\" (se houver um título existente)."

msgid "page.metadata.background.body6"
msgstr "Para ver exatamente de onde vieram os metadados de um livro, veja a aba <em>“Detalhes técnicos”</em> na página do livro. Ela tem um link para o JSON desse livro, com apontadores para o JSON dos registros originais."

msgid "page.metadata.background.body7"
msgstr "Para mais informações, veja as seguintes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Busca (aba de metadados)</a>, <a %(a_codes)s>Explorador de Códigos</a> e <a %(a_example)s>Exemplo de JSON de metadados</a>. Finalmente, todos os nossos metadados podem ser <a %(a_generated)s>gerados</a> ou <a %(a_downloaded)s>baixados</a> como bancos de dados ElasticSearch e MariaDB."

msgid "page.metadata.openlib.title"
msgstr "Vinculação com Open Library"

msgid "page.metadata.openlib.body1"
msgstr "Então, se encontrar um arquivo com metadados ruins, como deve corrigi-lo? Você pode ir à biblioteca de origem e seguir seus procedimentos para corrigir os metadados, mas o que fazer se um arquivo estiver presente em várias bibliotecas de origem?"

msgid "page.metadata.openlib.body2"
msgstr "Há um identificador tratado de forma especial no Arquivo da Anna. <strong>O campo annas_archive md5 na Open Library sempre substitui todos os outros metadados!</strong> Vamos voltar um pouco e aprender sobre Open Library."

msgid "page.metadata.openlib.body3"
msgstr "A Open Library foi fundada em 2006 por Aaron Swartz com o objetivo de \"uma página web para cada livro já publicado\". É uma espécie de Wikipedia para metadados de livros: todos podem editá-la, é licenciada livremente e pode ser baixada em massa. É um banco de dados de livros que está mais alinhado com nossa missão — na verdade, o Arquivo da Anna foi inspirado pela visão e vida de Aaron Swartz."

msgid "page.metadata.openlib.body4"
msgstr "Em vez de reinventar a roda, decidimos direcionar nossos voluntários para a Open Library. Se você vir um livro com metadados incorretos, pode ajudar da seguinte maneira:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Vá para o <a %(a_openlib)s>site da Open Library</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Encontre o registro correto do livro. <strong>AVISO:</strong> certifique-se de selecionar a <strong>edição</strong> correta. Na Open Library, você tem \"obras\" e \"edições\"."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Uma \"obra\" poderia ser \"Harry Potter e a Pedra Filosofal\"."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Uma \"edição\" poderia ser:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "A primeira edição de 1997 publicada pela Bloomsbury com 256 páginas."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "A edição de brochura de 2003 publicada pela Raincoast Books com 223 páginas."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "A tradução polonesa de 2000 “Harry Potter I Kamie Filozoficzn” pela Media Rodzina com 328 páginas."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Todas essas edições têm ISBNs e conteúdos diferentes, então certifique-se de selecionar a correta!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Edite o registro (ou crie um se não existir) e adicione o máximo de informações úteis que puder! Você já está aqui, então aproveite para tornar o registro realmente incrível."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Em “Números de ID”, selecione “Arquivo da Anna” e adicione o MD5 do livro do Arquivo da Anna. Este é o longo conjunto de letras e números após “/md5/” na URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Tente encontrar outros arquivos no Arquivo da Anna que também correspondam a este registro e adicione-os também. No futuro, podemos agrupar esses como duplicatas na página de busca do Arquivo da Anna."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando terminar, anote a URL que você acabou de atualizar. Depois de atualizar pelo menos 30 registros com os MD5s do Arquivo da Anna, envie-nos um <a %(a_contact)s>email</a> com a lista. Nós lhe daremos uma assinatura gratuita do Arquivo da Anna, para que você possa fazer esse trabalho mais facilmente (e como um agradecimento pela sua ajuda). Essas edições precisam ser de alta qualidade e adicionar uma quantidade substancial de informações, caso contrário, sua solicitação será rejeitada. Sua solicitação também será rejeitada se alguma das edições for revertida ou corrigida pelos moderadores da Open Library."

msgid "page.metadata.openlib.body5"
msgstr "Observe que isso só funciona para livros, não para artigos acadêmicos ou outros tipos de arquivos. Para outros tipos de arquivos, ainda recomendamos encontrar a biblioteca fonte. Pode levar algumas semanas para que as mudanças sejam incluídas no Arquivo da Anna, pois precisamos baixar o último dump de dados da Open Library e regenerar nosso índice de busca."

msgid "page.mirrors.title"
msgstr "Espelhos: chamada para voluntários"

msgid "page.mirrors.intro"
msgstr "Para aumentar a resiliência do Arquivo da Anna, estamos procurando voluntários para operar espelhos."

msgid "page.mirrors.text1"
msgstr "Estamos procurando por isso:"

msgid "page.mirrors.list.run_anna"
msgstr "Você executa a base de código aberto do Arquivo da Anna e atualiza regularmente tanto o código quanto os dados."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Sua versão é claramente distinguida como um espelho, por exemplo, “Arquivo do Bob, um espelho do Arquivo da Anna”."

msgid "page.mirrors.list.know_the_risks"
msgstr "Você está disposto a assumir os riscos associados a este trabalho, que são significativos. Você tem um profundo entendimento da segurança operacional necessária. O conteúdo destes <a %(a_shadow)s>posts</a> <a %(a_pirate)s>aqui</a> é auto evidente para você."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "Você está disposto a contribuir para nossa <a %(a_codebase)s>base de código</a> — em colaboração com nossa equipe — para que isso aconteça."

msgid "page.mirrors.list.maybe_partner"
msgstr "Inicialmente, não daremos acesso aos downloads do nosso servidor parceiro, mas se tudo correr bem, podemos compartilhar isso com você."

msgid "page.mirrors.expenses.title"
msgstr "Despesas de hospedagem"

msgid "page.mirrors.expenses.text1"
msgstr "Estamos dispostos a cobrir despesas de hospedagem e VPN, inicialmente até $200 por mês. Isso é suficiente para um servidor de busca básico e um proxy protegido por DMCA / direitos autorais."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Só pagaremos pela hospedagem depois que você tiver tudo configurado e demonstrado que é capaz de manter o arquivo atualizado com as atualizações. Isso significa que você terá que pagar pelos primeiros 1-2 meses do próprio bolso."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Seu tempo não será compensado (e nem o nosso), já que este é um trabalho puramente voluntário."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se você se envolver significativamente no desenvolvimento e operações do nosso trabalho, podemos discutir o compartilhamento de mais da receita de doações com você, para que você possa utilizá-la conforme necessário."

msgid "page.mirrors.getting_started.title"
msgstr "Começando"

msgid "page.mirrors.getting_started.text1"
msgstr "Por favor <strong>não nos contate</strong> para pedir permissão ou para perguntas básicas. Ações falam mais alto que palavras! Todas as informações estão disponíveis, então vá em frente e configure seu espelho."

msgid "page.mirrors.getting_started.text2"
msgstr "Sinta-se à vontade para postar tickets ou solicitações de mesclagem no nosso Gitlab quando encontrar problemas. Podemos precisar construir alguns recursos específicos para espelhos com você, como a mudança de “Arquivo da Anna” para o nome do seu site, (inicialmente) desativar contas de usuário, ou linkar de volta para nosso site principal a partir das páginas de livros."

msgid "page.mirrors.getting_started.text3"
msgstr "Uma vez que seu espelho esteja funcionando, por favor, entre em contato conosco. Adoraríamos revisar sua OpSec, e uma vez que isso esteja sólido, vamos linkar para o seu espelho e começar a trabalhar mais de perto com você."

msgid "page.mirrors.getting_started.text4"
msgstr "Agradecemos antecipadamente a todos que estão dispostos a contribuir dessa forma! Não é para os fracos de coração, mas solidificaria a longevidade da maior biblioteca verdadeiramente aberta da história humana."

msgid "page.partner_download.header"
msgstr "Download de site parceiro"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Downloads lentos estão disponíveis apenas no site oficial. Visita %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Downloads lentos não estão disponíveis por meio de VPNs da Cloudflare ou de outros endereços IP da Cloudflare."

msgid "page.partner_download.wait_banner"
msgstr "Por favor, aguarde <span %(span_countdown)s>%(wait_seconds)s</span> segundos para baixar este arquivo."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Baixar agora</a>"

msgid "page.partner_download.li4"
msgstr "Obrigado por esperar, isso mantém o site acessível gratuitamente para todos! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Aviso: houve muitos downloads do seu endereço IP nas últimas 24 horas. Os downloads podem ser mais lentos que o normal."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads do seu endereço IP nas últimas 24 horas: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Se você estiver usando uma VPN, uma conexão de Internet compartilhada ou se seu ISP compartilhar IPs, este aviso pode ser devido a isso."

msgid "page.partner_download.wait"
msgstr "Para poder dar a todos a oportunidade de baixar arquivos gratuitamente, você precisa esperar antes de poder baixar este arquivo."

msgid "page.partner_download.li1"
msgstr "Sinta-se à vontade para continuar navegando no arquivo da Anna em uma guia diferente enquanto espera (se o seu navegador suportar a atualização das guias de fundo)."

msgid "page.partner_download.li2"
msgstr "Sinta-se à vontade para aguardar o carregamento de várias páginas de download ao mesmo tempo (mas baixe apenas um arquivo ao mesmo tempo, por servidor)."

msgid "page.partner_download.li3"
msgstr "Após obter um link de download, ele é válido por várias horas."

msgid "layout.index.header.title"
msgstr "Acervo da Anna"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Registro no Arquivo da Anna"

msgid "page.scidb.download"
msgstr "Download"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Para apoiar a acessibilidade e a preservação a longo prazo do conhecimento humano, torne-se um <a %(a_donate)s>membro</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "Como bônus, 🧬 SciDB carrega mais rápido para os membros, sem limites."

msgid "page.scidb.refresh"
msgstr "Não está funcionando? Tente <a %(a_refresh)s>atualizar</a>."

msgid "page.scidb.no_preview_new"
msgstr "Nenhuma visualização disponível ainda. Baixe o arquivo do <a %(a_path)s>Arquivo da Anna</a>."

msgid "page.home.scidb.text2"
msgstr "🧬 SciDB é uma continuação do Sci-Hub, com sua interface familiar e visualização direta de PDFs. Digite seu DOI para visualizar."

msgid "page.home.scidb.text3"
msgstr "Temos a coleção completa do Sci-Hub, bem como novos artigos. A maioria pode ser visualizada diretamente com uma interface familiar, semelhante ao Sci-Hub. Alguns podem ser baixados de fontes externas; nesse caso, mostramos links para eles."

msgid "page.search.title.results"
msgstr "%(search_input)s - Busca"

msgid "page.search.title.new"
msgstr "Nova busca"

msgid "page.search.icon.include_only"
msgstr "Apenas Incluir"

msgid "page.search.icon.exclude"
msgstr "Excluir"

msgid "page.search.icon.unchecked"
msgstr "Não verificado"

msgid "page.search.tabs.download"
msgstr "Download"

msgid "page.search.tabs.journals"
msgstr "Artigos de jornal"

msgid "page.search.tabs.digital_lending"
msgstr "Empréstimo digital"

msgid "page.search.tabs.metadata"
msgstr "Metadados"

msgid "common.search.placeholder"
msgstr "Pesquisar título, autor, idioma, tipo de arquivo, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Pesquisar"

msgid "page.search.search_settings"
msgstr "Configurações de busca"

msgid "page.search.submit"
msgstr "Buscar"

msgid "page.search.too_long_broad_query"
msgstr "A pesquisa demorou muito, o que é comum em consultas amplas. As contagens do filtro podem não ser precisas."

msgid "page.search.too_inaccurate"
msgstr "A pesquisa demorou muito, o que significa que você poderá ver resultados imprecisos. Às vezes, <a %(a_reload)s>recarregar</a> a página ajuda."

msgid "page.search.filters.display.header"
msgstr "Exibir"

msgid "page.search.filters.display.list"
msgstr "Lista"

msgid "page.search.filters.display.table"
msgstr "Tabela"

msgid "page.search.advanced.header"
msgstr "Avançado"

msgid "page.search.advanced.description_comments"
msgstr "Descrições de pesquisa e comentários de metadados"

msgid "page.search.advanced.add_specific"
msgstr "Adicione um campo de pesquisa específico"

msgid "common.specific_search_fields.select"
msgstr "(campo específico de pesquisa)"

msgid "page.search.advanced.field.year_published"
msgstr "Ano de publicação"

msgid "page.search.filters.content.header"
msgstr "Conteúdo"

msgid "page.search.filters.filetype.header"
msgstr "Tipo de arquivo"

msgid "page.search.more"
msgstr "mais…"

msgid "page.search.filters.access.header"
msgstr "Acesso"

msgid "page.search.filters.source.header"
msgstr "Fonte"

msgid "page.search.filters.source.scraped"
msgstr "raspado e de código aberto por AA"

msgid "page.search.filters.language.header"
msgstr "Idioma"

msgid "page.search.filters.order_by.header"
msgstr "Ordene por"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Mais relevante"

msgid "page.search.filters.sorting.newest"
msgstr "Mais recente"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(ano de publicação)"

msgid "page.search.filters.sorting.oldest"
msgstr "Mais antigo"

msgid "page.search.filters.sorting.largest"
msgstr "Maior"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(tamanho do arquivo)"

msgid "page.search.filters.sorting.smallest"
msgstr "Menor"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(publicado gratuitamente)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aleatório"

msgid "page.search.header.update_info"
msgstr "O índice de busca é atualizado mensalmente. Atualmente, ele possui arquivos até %(last_data_refresh_date)s. Para informações mais técnicas, veja a página do %(link_open_tag)sdatasets</a>."

msgid "page.search.header.codes_explorer"
msgstr "Para explorar o índice de busca por códigos, use o <a %(a_href)s>Explorador de Códigos</a>."

msgid "page.search.results.search_downloads"
msgstr "Digite na caixa para procurar em nosso catálogo de %(count)s arquivos de download direto, os quais nós <a %(a_preserve)s>preservamos para sempre</a>."

msgid "page.search.results.help_preserve"
msgstr "Na verdade, qualquer pessoa pode ajudar a preservar esses arquivos propagando nossa <a %(a_torrents)s>lista unificada de torrents</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Nós atualmente temos o catálogo aberto de livros, artigos e outros trabalhos escritos mais completo do mundo. Nós espalhamos Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e mais</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Caso você encontre outras \"shadow libraries\" que devemos espelhar, ou caso tenha qualquer dúvida, por favor nos contate em %(email)s."

msgid "page.search.results.dmca"
msgstr "Para reclamações de direitos autorais / DMCA <a %(a_copyright)s>clique aqui</a>."

msgid "page.search.results.shortcuts"
msgstr "Dica: use atalhos do teclado \"/\" (foco na busca), \"enter\" (buscar), \"j\" (para cima), \"k\"(para baixo) para uma navegação mais rápida."

msgid "page.search.results.looking_for_papers"
msgstr "Procurando por artigos?"

msgid "page.search.results.search_journals"
msgstr "Digite na caixa para buscar em nosso catálogo de %(count)s trabalhos acadêmicos e artigos de jornal, os quais nós <a %(a_preserve)s>preservamos para sempre</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Digite na caixa para procurar por arquivos em bibliotecas de empréstimo digital."

msgid "page.search.results.digital_lending_info"
msgstr "Esse index de busca atualmente inclui metadados da biblioteca de empréstimo digital do Internet Archive. <a %(a_datasets)s>Mais sobre nossos bancos de dados</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Para mais bibliotecas de empréstimo digital, veja <a %(a_wikipedia)s>Wikipedia</a> e a <a %(a_mobileread)s>Wiki do MobileRead</a>."

msgid "page.search.results.search_metadata"
msgstr "Digite na caixa para procurar por metadados de bibliotecas. Isso pode ser útil quando estiver <a %(a_request)s>solicitando um arquivo</a>."

msgid "page.search.results.metadata_info"
msgstr "Esse index de procura atualmente inclui metadados do ISBNdb e Open Library. <a %(a_datasets)s>Mais sobre nossos bancos de dados</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Para metadados, mostramos os registros originais. Não fazemos nenhuma fusão de registros."

msgid "page.search.results.metadata_info_more"
msgstr "Tem muitas, muitas fontes de metadados para obras escritas ao redor do mundo. <a %(a_wikipedia)s>Essa página da Wikipedia</a> é um bom começo, mas se você quiser conhecer outras boas listas, por favor nos deixe saber."

msgid "page.search.results.search_generic"
msgstr "Digite na caixa para buscar."

msgid "page.search.results.these_are_records"
msgstr "Estes são registros de metadados, e<span %(classname)s>não</span> arquivos para download."

msgid "page.search.results.error.header"
msgstr "Erro durante a pesquisa."

msgid "page.search.results.error.unknown"
msgstr "Tente <a %(a_reload)s>atualizar a página</a>. Caso o problema persista, por favor nos envie um email em %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Nenhum arquivo encontrado.</span> Tente usar termos a menos ou diferentes na busca e nos filtros."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Às vezes isso acontece incorretamente quando o servidor de busca está lento. Nesses casos, <a %(a_attrs)s>recarregar</a> pode ajudar."

msgid "page.search.found_matches.main"
msgstr "Encontramos correspondências em: %(in)s. Você pode consultar a URL encontrada lá ao <a %(a_request)s>solicitar um arquivo</a>."

msgid "page.search.found_matches.journals"
msgstr "Artigos de Jornal (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Empréstimo digital (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadados (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Resultados %(from)s-%(to)s (%(total)s total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ correspondências parciais"

msgid "page.search.results.partial"
msgstr "%(num)d correspondências parciais"

msgid "page.volunteering.title"
msgstr "Voluntariado & Recompensas"

msgid "page.volunteering.intro.text1"
msgstr "O Arquivo da Anna depende de voluntários como você. Acolhemos todos os níveis de comprometimento e temos duas principais categorias de ajuda que estamos procurando:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Trabalho leve de voluntariado:</span> se você só pode dedicar algumas horas aqui e ali, ainda há muitas maneiras de ajudar. Recompensamos voluntários consistentes com <span %(bold)s>🤝 assinaturas no Arquivo da Anna</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Trabalho voluntário intenso (recompensas de USD$50-USD$5.000):</span> se você puder dedicar muito tempo e/ou recursos à nossa missão, adoraríamos trabalhar mais de perto com você. Eventualmente, você pode se juntar a nossa equipe interna. Embora tenhamos um orçamento apertado, podemos conceder <span %(bold)s>💰 recompensas monetárias</span> para os trabalhos mais intensos."

msgid "page.volunteering.intro.text2"
msgstr "Se você não puder doar seu tempo, ainda pode nos ajudar muito <a %(a_donate)s>doando dinheiro</a>, <a %(a_torrents)s>semeando nossos torrents</a>, <a %(a_uploading)s>fazendo o upload de livros</a> ou <a %(a_help)s>contando aos seus amigos sobre o Arquivo da Anna</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Empresas:</span> oferecemos acesso direto e de alta velocidade às nossas coleções em troca de doações em nível empresarial ou em troca de novas coleções (por exemplo, novos scans, datasets OCR, enriquecimento de nossos dados). <a %(a_contact)s>Entre em contato</a> se for o seu caso. Veja também nossa <a %(a_llm)s>página de LLM</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Voluntariado leve"

msgid "page.volunteering.section.light.text1"
msgstr "Se você tem algumas horas livres, pode ajudar de várias maneiras. Certifique-se de participar do <a %(a_telegram)s>chat de voluntários no Telegram</a>."

msgid "page.volunteering.section.light.text2"
msgstr "Como forma de agradecimento, geralmente oferecemos 6 meses de “Arquivista Afortunado” para marcos básicos, e mais para trabalho voluntário contínuo. Todos os marcos exigem trabalho de alta qualidade — trabalho descuidado nos prejudica mais do que ajuda e será rejeitado. Por favor, <a %(a_contact)s>envie-nos um e-mail</a> quando atingir um marco."

msgid "page.volunteering.table.header.task"
msgstr "Tarefa"

msgid "page.volunteering.table.header.milestone"
msgstr "Marco"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Divulgando o Arquivo da Anna. Por exemplo, recomendando livros no AA, compartilhando links para nossas postagens no blog ou, de modo geral, direcionando pessoas para nosso site."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links ou capturas de tela."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Esses devem mostrar você informando alguém sobre o Arquivo da Anna, e essa pessoa agradecendo."

msgid "page.volunteering.table.open_library.task"
msgstr "Melhore os metadados os <a %(a_metadata)s>vinculando</a> com a Open Library."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Você pode usar a <a %(a_list)s>lista de problemas de metadados aleatórios</a> como ponto de partida."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Certifique-se de deixar um comentário nos problemas que você resolver, para que outros não dupliquem seu trabalho."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links de registros que você melhorou."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traduzindo</a> o site."

msgid "page.volunteering.table.translate.milestone"
msgstr "Traduzir completamente um idioma (se ele não estava quase concluído.)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "Melhore a página da Wikipédia do Arquivo da Anna no seu idioma. Inclua informações da página da Wikipédia do AA em outros idiomas, e do nosso site e blog. Adicione referências ao AA em outras páginas relevantes."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link para o histórico de edições mostrando que você fez contribuições significativas."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Atendendo solicitações de livros (ou artigos, etc) nos fóruns do Z-Library ou do Library Genesis. Não temos nosso próprio sistema de solicitações de livros, mas espelhamos essas bibliotecas, então melhorá-las também melhora o Arquivo da Anna."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links ou capturas de tela de solicitações que você atendeu."

msgid "page.volunteering.table.misc.task"
msgstr "Pequenas tarefas postadas no nosso <a %(a_telegram)s>chat de voluntários no Telegram</a>. Geralmente para assinaturas, às vezes para pequenas recompensas."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Pequenas tarefas postadas em nosso grupo de chat de voluntários."

msgid "page.volunteering.table.misc.milestone"
msgstr "Depende da tarefa."

msgid "page.volunteering.section.bounties.heading"
msgstr "Recompensas"

msgid "page.volunteering.section.bounties.text1"
msgstr "Estamos sempre procurando por pessoas com sólidas habilidades em programação ou segurança ofensiva dispostas a colaborar. Você pode fazer uma diferença significativa na preservação do legado da humanidade."

msgid "page.volunteering.section.bounties.text2"
msgstr "Como agradecimento, oferecemos status de membro por contribuições sólidas. Como um grandíssimo obrigado, oferecemos recompensas monetárias para tarefas particularmente importantes e difíceis. Isto não deve ser visto como um substituto para um emprego, mas é um incentivo extra e pode ajudar com eventuais custos."

msgid "page.volunteering.section.bounties.text3"
msgstr "Grande parte do nosso código é aberto e pediremos o mesmo do seu código para concedermos a remuneração. Podemos discutir exceções de forma individual, se necessário."

msgid "page.volunteering.section.bounties.text4"
msgstr "As recompensas são concedidas à primeira pessoa que completar uma tarefa. Sinta-se à vontade para comentar em um ticket de recompensa para que outros saibam que você está trabalhando em algo, para que possam esperar ou entrar em contato com você para formar uma equipe. Mas esteja ciente de que outros ainda são livres para trabalhar nisso também e tentar vencê-lo. No entanto, não concedemos recompensas por trabalho descuidado. Se duas submissões de alta qualidade forem feitas próximas uma da outra (dentro de um ou dois dias), podemos optar por conceder recompensas a ambas, a nosso critério, por exemplo, 100%% para a primeira submissão e 50%% para a segunda submissão (totalizando 150%%)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Para as recompensas maiores (especialmente recompensas de scraping), entre em contato conosco quando você tiver completado ~5%% dela, e estiver confiante de que seu método escalará para o marco completo. Você terá que compartilhar seu método conosco para que possamos dar feedback. Além disso, dessa forma podemos decidir o que fazer se houver várias pessoas chegando perto de uma recompensa, como potencialmente concedê-la a várias pessoas, encorajar as pessoas a formarem equipes, etc."

msgid "page.volunteering.section.bounties.text6"
msgstr "AVISO: as tarefas de alta recompensa são <span %(bold)s>difíceis</span> — pode ser sábio começar com as mais fáceis."

msgid "page.volunteering.section.bounties.text7"
msgstr "Vá para nossa <a %(a_gitlab)s>lista de issues no Gitlab</a> e classifique por “Label priority”. Isso mostra aproximadamente a ordem das tarefas que nos importam. Tarefas sem recompensas explícitas ainda são elegíveis para status de membro, especialmente aquelas marcadas como “Accepted” e “Anna’s favorite”. Você pode querer começar com um “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Atualizações sobre o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, a maior biblioteca verdadeiramente aberta da história humana."

msgid "layout.index.title"
msgstr "Acervo da Anna"

msgid "layout.index.meta.description"
msgstr "A maior biblioteca open-source e open-data do mundo. Inclui Sci-Hub, Library Genesis, Z-Library, e mais."

msgid "layout.index.meta.opensearch"
msgstr "Pesquise no arquivo da Anna"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Arquivo da Anna precisa da sua ajuda!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Muitos tentam nos derrubar, mas nós revidamos."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se você doar agora, receberá <strong>o dobro</strong> de downloads rápidos."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Válido até o final deste mês."

msgid "layout.index.header.nav.donate"
msgstr "Doar"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvando o conhecimento humano: um ótimo presente de feriado!"

msgid "layout.index.header.banner.surprise"
msgstr "Surpreenda um ente querido, dê-lhe uma conta com adesão."

msgid "layout.index.header.banner.mirrors"
msgstr "Para aumentar a resiliência do Arquivo da Anna, estamos procurando voluntários para operar espelhos."

msgid "layout.index.header.banner.valentine_gift"
msgstr "O presente de Dia dos Namorados perfeito!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Temos um novo método para receber doações: %(method_name)s. Por favor, considere %(donate_link_open_tag)sdoar</a> — não é barato manter este site funcionando, e sua doação faz muita diferença! Muito obrigada."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Estamos fazendo uma campanha para <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">fazer o backup</a> da maior shadow library de quadrinhos do mundo Obrigado pelo seu apoio! <a href=\"/donate\">Doar.</a> Se você não puder doar, considere nos apoiar falando para seus amigos e nos seguindo no <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Downloads recentes:"

msgid "layout.index.header.nav.search"
msgstr "Pesquisar"

msgid "layout.index.header.nav.faq"
msgstr "Perguntas frequentes"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Melhorar metadados"

msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariado e recompensas"

msgid "layout.index.header.nav.datasets"
msgstr "Bancos de dados"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Atividade"

msgid "layout.index.header.nav.codes"
msgstr "Explorador de códigos"

msgid "layout.index.header.nav.llm_data"
msgstr "Dados para LLM"

msgid "layout.index.header.nav.home"
msgstr "Início"

msgid "layout.index.header.nav.annassoftware"
msgstr "Programas da Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduza ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Entrar / Cadastrar"

msgid "layout.index.header.nav.account"
msgstr "Conta"

msgid "layout.index.footer.list1.header"
msgstr "Acervo da Anna"

msgid "layout.index.footer.list2.header"
msgstr "Mantenha contato"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / Reivindicação de Direitos Autorais"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Avançado"

msgid "layout.index.header.nav.security"
msgstr "Segurança"

msgid "layout.index.footer.list3.header"
msgstr "Alternativas"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "não afiliado"

msgid "page.search.results.issues"
msgstr "❌ Este arquivo pode ter problemas."

msgid "page.search.results.fast_download"
msgstr "Download rápido"

msgid "page.donate.copy"
msgstr "copiar"

msgid "page.donate.copied"
msgstr "copiado!"

msgid "page.search.pagination.prev"
msgstr "Anterior"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Próximo"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Espelho #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% do patrimônio escrito da humanidade preservado para sempre %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datasets ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Baixe o ebook/arquivo %(extension)s grátis de:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Temos várias opções para download para o caso de alguma delas não funcionar. Elas todas têm exatamente o mesmo arquivo."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Saiba que o Arquivo da Ana não hospeda qualquer conteúdo. Apenas direcionamos os links para sites de outras pessoas. Se você acha que tem uma solicitação válida de DMCA/Lei de direitos autorais, por favor, dê uma olhada na página %(about_link)ssobre nós</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Espelho anônimo Z-Library #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Doe"

#~ msgid "page.donate.header"
#~ msgstr "Doe"

#~ msgid "page.donate.text1"
#~ msgstr "O Arquivo da Anna é um projeto sem fins lucrativos e de código aberto mantido totalmente por voluntários. Aceitamos doações para cobrir nossos custos como hospedagem, domínios, desenvolvimento e outros gastos."

#~ msgid "page.donate.text2"
#~ msgstr "Com suas contribuições conseguimos manter o site funcionando, melhorar as funções e preservar mais acervos."

#~ msgid "page.donate.text3"
#~ msgstr "Doações recentes: %(donations)s. Obrigada a todos pela generosidade. Apreciamos sua confiança depositada em nós com qualquer valor que vocês puderem doar."

#~ msgid "page.donate.text4"
#~ msgstr "Para doar, selecione o método preferido abaixo. Se tiver algum problema, fale com a gente em %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Cartão de crédito/débito"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Criptomoedas"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Perguntas"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Vá a %(link_open_tag)sessa página</a> e siga as instruções, escaneando o QR code ou clicando no link para “paypal.me”. Se não funcionar, tente atualizar a página, outra conta poderá aparecer."

#~ msgid "page.donate.cc.header"
#~ msgstr "Cartão de crédito/débito"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Usamos o Sendwyre para depositar diretamente em nossa carteira de Bitcoin (BTC). Leva cerca de 5 minutos."

#~ msgid "page.donate.cc.text2"
#~ msgstr "O valor mínimo da transação por este método é $30, e uma taxa de aproximadamente $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Etapas:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copie o endereço de nossa carteira Bitcoin (BTC): %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Vá para %(link_open_tag)sesta página</a> e clique em \"buy crypto instantly\" (comprar criptomoedas instantaneamente)"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Cole o endereço de nossa carteira e siga as instruções"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Criptomoedas"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(também funciona para BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Por favor use esta %(link_open_tag)sconta do Alipay</a> para enviar sua doação."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Essa opção de doação não está funcionando no momento. Por favor, tente mais tarde. Obrigada por querer fazer uma doação, ficamos gratas!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Por favor, use %(link_open_tag)sesta página Pix</a> para enviar sua doação. Se não funcionar, tente atualizar a página, pois isso pode resultar em uma conta diferente."

#~ msgid "page.donate.faq.header"
#~ msgstr "Perguntas frequentes"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">O Arquivo da Ana (Anna’s Archive)</span> é um projeto com o objetivo de catalogar todos os livros que existem agregando dados de várias fontes. Também acompanhamos o progresso da humanidade em disponibilizar todos esses livros facilmente em forma digital através de \"<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>\". Saiba mais <a href=\"/about\">sobre nós.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Livro (todos)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Início"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datasets ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Nenhum resultado"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” não é um ISBN válido. Os ISBNs tem 10 ou 13 caracteres, sem contar as barras opcionais. Todos os caracteres devem ser números, exceto o último, que pode ser \"X\". O último caractere é o \"digito de checagem\", que deve corresponder ao checksum computado pelos outros números. Os números também devem estar em uma faixa válida disponibilizada pela International ISBN Agency."

#~ msgid "page.isbn.results.text"
#~ msgstr "Arquivos correspondentes em nosso banco de dados:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Nenhum arquivo correspondente em nosso banco de dados."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Busca ▶ %(num)d+ resultados para <span class=\"italic\">%(search_input)s</span> (nos metadados de shadow library)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Busca ▶ %(num)d+ resultados para <span class=\"italic\">%(search_input)s</span> (nos metadados de shadow library)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Busca ▶ Erro na busca por <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Pesquisar ▶ Nova pesquisa"

#~ msgid "page.donate.header.text3"
#~ msgstr "Você também pode doar sem criar uma conta (os mesmos métodos de pagamento são aceitos para doações únicas e assinaturas):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Faça uma doação única anônima (sem vantagens)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Selecione um método de pagamento. Favor considerar pagamento por criptomoedas %(bitcoin_icon)s, pois assim ficamos sujeitos a menos taxas."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Se você já possui criptomoedas, estes são os nossos endereços."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Muito obrigada por ajudar! Este projeto não seria possível sem você."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Para doações usando PayPal utilizamos PayPal Crypto, que nos permite permanecer anônimos. Agradeceríamos se tomasse parte de seu tempo para aprender a como doar utilizado este método, já que ele nos ajuda muito."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Siga as instruções para comprar Bitcoin (BTC). Você só precisa comprar a quantidade que deseja doar."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Se você perder um pouco de Bitcoin devido à flutuação ou a taxas, <em> por favor, não se preocupe </em>. Isso é normal com criptomoedas, mas nos permite operar de forma anônima."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Digite nosso endereço de Bitcoin (BTC) como destinatário e siga as instruções para enviar sua doação:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Por favor utilize <a %(a_account)s>esta conta Alipay</a> para enviar sua doação."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Por favor,utilize <a %(a_account)s>esta conta Pix</a> para enviar sua doação."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Se o seu método de pagamento não está na lista, a maneira mais fácil seria baixar o <a href=\"https://paypal.com/\">PayPal</a> ou o <a href=\"https://coinbase.com/\">Coinbase</a> em seu telefone e comprar um pouco de Bitcoin (BTC) lá. Em seguida, você pode enviá-lo para nosso endereço: %(address)s. Na maioria dos países, esse processo deve levar apenas alguns minutos para ser configurado."

#~ msgid "page.search.results.error.text"
#~ msgstr "Tente <a href=\"javascript:location.reload()\">atualizar a página</a>. Se o problema continuar, nos avise através do <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">|Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Para se tornar membro, <a href=\"/login\">entre ou registre-se</a>. Se preferir não fazer uma conta, selecione \"Fazer uma doação única anônima\" acima. Obrigada pelo apoio!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Início"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Sobre"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Doar"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datasets"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "App para celular"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog da Anna (em inglês)"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Código Fonte"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traduza"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s torrents"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;%(libraries)s espelhadas, e mais."

#~ msgid "page.home.preservation.text"
#~ msgstr "Preservamos livros, artigos, quadrinhos, revistas e mais, trazendo esses materiais de várias <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a> e unindo-os em um único lugar. Todos esses dados são preservados para sempre porque os tornamos fáceis de duplicar em massa, resultando em várias cópias ao redor do mundo. Essa distribuição ampla, combinada com código aberto, também torna nosso site resiliente a derrubadas. Saiba mais sobre <a href=\"/datasets\">nossos datasets</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datasets ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Nenhum resultado"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" não parece um DOI. Ele deve começar com \"10.\" e ter uma barra oblíqua (/)."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL canônico: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Este arquivo pode estar no %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Arquivos correspondentes em nosso banco de dados:"

#~ msgid "page.doi.results.none"
#~ msgstr "Nenhum arquivo correspondente encontrado em nosso banco de dados."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Downloads rápidos</strong> Seus downloads rápidos esgotaram por hoje. Por favor, contate a Anna em %(email)s se você tiver interesse em fazer um upgrade na sua assinatura."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Já não tem downloads rápidos hoje. Contace Anna em %(email)s se tiver interesse em atualizar a sua subscrição."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Posso contribuir de outras formas?</div> Sim! Veja a seção \"Como ajudar\" na página <a href=\"/about\">sobre nós</a>."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Eu não gosto que vocês estejam \"monetizando\" o Arquivo da Anna!</div> Se você não gosta de como nós operamos o projeto, vá gerir sua própria shadow library! Todos os nossos dados e o código são open source, então nada está te impedindo. ;)"

#~ msgid "page.request.title"
#~ msgstr "Requisitar livros"

#~ msgid "page.request.text1"
#~ msgstr "Por ora, você poderia enviar seus pedidos de eBooks ao <a %(a_forum)s>fórum Libgen.rs</a>? Você pode criar uma conta lá e postar em um desses tópicos:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Para eBooks, use <a %(a_ebook)s>esse tópico</a>.</li><li %(li_item)s>Para livros não disponíveis como eBooks, use <a %(a_regular)s>esse tópico</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Em ambos os casos, se certifique de seguir as regras mencionadas nas threads."

#~ msgid "page.upload.title"
#~ msgstr "Upload"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Biblioteca Gênesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Biblioteca-Z"

#~ msgid "page.upload.large.header"
#~ msgstr "Grandes envios."

#~ msgid "page.about.title"
#~ msgstr "Sobre"

#~ msgid "page.about.header"
#~ msgstr "Sobre"

#~ msgid "page.home.search.header"
#~ msgstr "Pesquisar"

#~ msgid "page.home.search.intro"
#~ msgstr "Pesquise nosso acervo de shadow libraries."

#~ msgid "page.home.random_book.header"
#~ msgstr "Livro Aleatório"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Ir a um livro aleatório do catálogo."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Livro Aleatório"

#~ msgid "page.about.text1"
#~ msgstr "O Arquivo da Anna é um mecanismo de busca sem fins lucrativos e de código aberto para “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliotecas sombrias</a>”. Foi criado por <a href=\"http://annas-blog.org\">Anna</a>, que sentiu a necessidade de ter um lugar central para buscar livros, artigos, quadrinhos, revistas e outros documentos."

#~ msgid "page.about.text4"
#~ msgstr "Se você receber um aviso válido do DMCA, olhe o fim desta página ou, nos contacte pelo %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Explorar livros"

#~ msgid "page.home.explore.intro"
#~ msgstr "Uma combinação de livros populares e livros que têm algum significado especial para o mundo das shadow libraries e da preservação digital."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat não oficial"

#~ msgid "page.wechat.body"
#~ msgstr "Nós temos uma página não oficial no WeChat, mantida por um membro da comunidade. Use o código abaixo para acessar."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Sobre"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "App para celular"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "WeChat não oficial"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Pedir livros"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Upload"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Indique amigos"

#~ msgid "page.about.help.header"
#~ msgstr "Como ajudar"

#~ msgid "page.refer.title"
#~ msgstr "Indique amigos para receber downloads rápidos bônus"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Membros podem indicar amigos e receber downloads bônus."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Para cada amigo que se tornar membro:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>ELes</strong> recebem%(percentage)s%% downloads bônus além dos downloads diários regulares, durante o período de assinatura."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Você</strong> obtém o mesmo número de downloads bônus além dos downloads diários regulares, pelo mesmo período da assinatura de seu amigo(até um total de %(max)s downloads bônus a qualquer momento). Você deve manter uma assinatura ativa para usar seus downloads bônus."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Exemplo:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Seu amigo usa seu link de indicação para se inscrever por 3 meses como membro “Bibliotecário Sortudo”, que vem com %(num)s downloads rápidos."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Ele recebe %(num)s downloads bônus todo dia, pelos próximos 3 meses."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Você também recebe %(num)s downloads bônus todo dia, pelos mesmos 3 meses."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Link de indicação:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Entre</a> e se torne membro para indicar amigos."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Torne-se membro</a> para indicar amigos."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Ou adicione %(referral_suffix)s ao fim do outro link, e a indicação será lembrada quando seu amigo se tornar membro."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Doe a quantia total de %(total)s usando <a %(a_account)s> essa conta Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Alternativamente, você pode carregá-los para a Biblioteca-Z <%(a_upload)s>here</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Para aumentar a resiliência do Arquivo da Anna, estamos procurando voluntários para operar espelhos. <a href=\"/mirrors\">Saiba mais…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Mirrors: recrutando voluntários"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "somente este mês!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>pausou</a> o upload de novos artigos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selecione uma opção de pagamento. Damos descontos para pagamentos com criptomoedas %(bitcoin_icon)s porque temos menos taxas a pagar."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selecione uma opção de pagamento. Atualmente, apenas aceitamos pagamentos em criptomoedas %(bitcoin_icon)s, já que os processadores de pagamento tradicionais se recusam a trabalhar conosco."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Não podemos oferecer suporte direto a cartões de crédito/débito porque os bancos não querem trabalhar conosco. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "No entanto, existem várias maneiras de usar cartões de crédito/débito, usando nossos outros métodos de pagamento:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Downloads lentos e externos"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se estiver usando criptomoedas pela primeira vez, sugerimos usar %(option1)s, %(option2)s ou %(option3)s para comprar e doar Bitcoin (a criptomoeda original e mais usada)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links de registros que você melhorou."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links ou capturas de tela."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links ou capturas de tela dos pedidos que você atendeu."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se você estiver interessado em espelhar esses datasets para <a %(a_faq)s>arquivamento</a> ou <a %(a_llm)s>treinamento de LLM</a>, por favor, entre em contato conosco."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se você estiver interessado em espelhar este conjunto de dados para <a %(a_archival)s>arquivamento</a> ou para <a %(a_llm)s>treinamento de LLM</a>, entre em contato conosco."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Site principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informações de país do ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se você estiver interessado em espelhar este conjunto de dados para fins de <a %(a_archival)s>arquivamento</a> ou <a %(a_llm)s>treinamento de LLM</a>, por favor, entre em contato conosco."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "A Agência Internacional do ISBN lança regularmente os intervalos que alocou para as agências nacionais de ISBN. A partir disso, podemos derivar a que país, região ou grupo linguístico pertence esse ISBN. Atualmente, usamos esses dados indiretamente, através da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Recursos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Última atualização: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Site do ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadados"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluindo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Nossa inspiração para coletar metadados é o objetivo de Aaron Swartz de “uma página web para cada livro já publicado”, para o qual ele criou a <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Esse projeto teve um bom desempenho, mas nossa posição única nos permite obter metadados que eles não conseguem."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Outra inspiração foi nosso desejo de saber <a %(a_blog)s>quantos livros existem no mundo</a>, para podermos calcular quantos livros ainda temos para salvar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Para dar a todos a oportunidade de baixar arquivos gratuitamente, você precisa esperar <strong>%(wait_seconds)s segundos</strong> antes de poder baixar este arquivo."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Atualizar página automaticamente. Se você perder a janela de download, o temporizador será reiniciado, então a atualização automática é recomendada."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Baixe Agora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "converter: use ferramentas online para converter entre formatos. Por exemplo, para converter entre epub e pdf, use <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: baixe o arquivo (pdf ou epub são suportados) e <a %(a_kindle)s>envie-o para Kindle</a> usando a web, aplicativo ou e-mail. Ferramentas úteis: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Apoie os autores: Se você gostou disto e tem condições, considere comprar o original ou apoiar os autores diretamente."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Apoie as bibliotecas: Se isto estiver disponível na sua biblioteca local, considere pegar emprestado gratuitamente lá."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Não disponível diretamente em massa, apenas em semi-massa atrás de um paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Arquivo da Anna gerencia uma coleção de <a %(isbndb)s>metadados do ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "A ISBNdb é uma empresa que coleta dados de várias livrarias online para encontrar metadados de ISBN. O Arquivo da Anna tem feito backups dos metadados de livros da ISBNdb. Esses metadados estão disponíveis através do Arquivo da Anna (embora atualmente não estejam na busca, exceto se você procurar explicitamente por um número ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Para detalhes técnicos, veja abaixo. Em algum momento, podemos usá-los para determinar quais livros ainda estão faltando nas bibliotecas sombra, a fim de priorizar quais livros encontrar e/ou digitalizar."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nosso post no blog sobre esses dados"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Coleta de dados da ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Atualmente, temos um único torrent, que contém um arquivo de 4,4GB em formato <a %(a_jsonl)s>JSON Lines</a> compactado (20GB descompactado): “isbndb_2022_09.jsonl.gz”. Para importar um arquivo “.jsonl” no PostgreSQL, você pode usar algo como <a %(a_script)s>este script</a>. Você pode até mesmo canalizá-lo diretamente usando algo como %(example_code)s para que ele descompacte em tempo real."

#~ msgid "page.donate.wait"
#~ msgstr "Aguarde pelo menos <span %(span_hours)s>duas horas</span> (e atualize esta página) antes de entrar em contato conosco."

#~ msgid "page.codes.search_archive"
#~ msgstr "Pesquisar no Arquivo da Anna por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Faça uma doação usando Alipay ou WeChat. Você pode escolher entre eles na próxima página."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Divulgar o Arquivo da Anna nas redes sociais e fóruns online, recomendando livros ou listas no AA, ou respondendo perguntas."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s A coleção de Ficção divergiu, mas ainda tem <a %(libgenli)s>torrents</a>, embora não atualizados desde 2022 (temos downloads diretos)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Arquivo da Anna e Libgen.li gerenciam colaborativamente coleções de <a %(comics)s>quadrinhos</a> e <a %(magazines)s>revistas</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Sem torrents para coleções de ficção russa e documentos padrão."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Não há torrents disponíveis para o conteúdo adicional. Os torrents que estão no site Libgen.li são espelhos de outros torrents listados aqui. A única exceção são os torrents de ficção começando em %(fiction_starting_point)s. Os torrents de quadrinhos e revistas são lançados como uma colaboração entre o Arquivo da Anna e o Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "De uma coleção <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origem exata desconhecida. Parte do the-eye.eu, parte de outras fontes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

