��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  xd O  �f ,   Jh   wh   �i �  �k q  ^m 7  �n [   p s   dp F   �p k   q �  �q `  Gs �   �t �  �u    �w �  �x �  �z 2  z| �   �~ �   �  z� I   I� �   �� Y   s�   ̈́    ߆ ?  �� F   ?� -   ��    ��    ŉ O   � &   1�    X� 0   j� '   �� ,   Ê    �� J   � ;  Q�   �� O   �� <   �    )�    8�    G�    e�    t� 
   �� 	   ��    ��    ��     Î    �    � 	   � 
   �    �    )�    B�    a� �  }� m  � �   t� ,   � S  1� �   �� �   � �   �    Η �   �    �� �   �� $   x� !  ��    �� �  ߚ ?   w� j   ��    "� K  +� d  w� �  ܟ 0  ��    Ѣ E   � Q   -� �   � 1   <� (   n� u   �� :   
� ?   H� /   ��    �� |   �� �  =� >   � �   X� �   � J   �� 0  �� '   '�   O� �   b� �   �� O   �� �   � �   �� F   U� �   �� �   �� Z   L� E   �� n   �    \� �   i� !  �� F    � 
   g� �   r� �   (�    � ~  � �   ��    >� �  ?� �   ź _  ��   � Q   ,� M   ~� f   �� �   3� y   �� W   6� 7   �� �   �� c   e� B   ��    � {    � Y   �� �   �� �   ��    W�   n�   z� C  �� X  �� �    � �   �� 2  �� �   �� �   j� �   � k  �� �   1�   �� 	   �� �   �� �   �� �   }� t  ?� �   �� �   <� �   �� �  �� x   p� @  ��   *� #   C� [   g� �   �� O   T� |  �� �   !� c   �� m   =� *   �� K  �� �   "� �   �    �� W   �� �   � �   ��   �� �   �� �  �� �  a� *   
� �  5� �  �� 7  U� �  �� 4  2� !  g� e   �� �   ��    �� �   �� 2  Z� �  ��   )� �   :� �   3  
   �  o   M  w �   �    � �   � K  v �   � ,  � �   �	 L   �
    �
 �   w  �     �   ,   ( ?  ? �    s  i g   � T   E   � 3   �    � F  �   2 .  F /   u F   �   � �    v   � ]    �  a "   �      �  '  �   �! �   �" N   
# �   Y# "   �# �   
$   %    &    & ,   %& K   R& B   �& 8   �& \   ' v   w'    �' +   ( F   2( U   y( -   �( 2   �( /   0) �   `)    �)    �)   * Z  $+ �   , �   y- ~   j. �   �. @  �/    �0 �   1 �   2 �   �2 J  Q3 �  �4   /6 �  ?8 �  �9 F   ; ?   �; )   < �   0<   �< �  ? .  �@ \  �A   %C .  @D +  oF &   �G ?  �G 8  I u  ;J    �K �   �K \  �L �   	N S  �N �   �O 
   �P (   �P r   	Q 	   |Q �  �Q �  'T c  �U �   /X �   �X    �Y �   �Y �   �Z F   �[ q   \   t\   �] �   �^ �  N_ e   �` %  Wa    }b   �b �   �d �  Se �  Pg �  �i �   �k 
   �l �   �l �  �m    to k  �o   �p )  
s 5  4u   jw N  vx F   �y �  z �   �{ ~   �| @   E} b   �}    �} ?   �} (   5~ 3   ^~ '   �~    �~ �   �~ �  t �  W� %  O� �   u� 
   ]� �  h� F  Z� f  �� �  � �  �� y  � 
   ��   ��    ��    �� �  ŕ 5  g� 0  �� n  Λ    =� �  O� �  =�   /�   1� �  P� 7  4� v  l� $   � �   � u   ˭ �  A� �  @� �   � �   �� �   ?� �  �    m� �   �� I   .� L   x�    ŷ 3   � =   � 4  W� �  �� �  >� 3   � �  � �   ��    �� �   �� B   e� �   �� �   3� ,   � b   /�    �� Z  �� X  � �  [� �  
� w   ��    � k   � 
  ��    �� �   �� �  -� �   �� 1   �� 1   �� j  �� ,   \� |  �� �  � �  �� #   �� |   �� �   4� F  �� �  
�    �� �   �� .   �� �   �� �   ��   c� �   r� T   O� ,   �� �   �� 0   �� �  � O  �� �    �   �� b   �� B   :� �   }� ?  &� �  f� �  �� �  �� 9   �� �   � (  �� T   � �   u� �  @� �  <� ,   �� �  � /   �� �  .� T       ` 3  w 8  � �  � 	  � 9  � �  �	    � �   � �  � �  � �   M    � F  � A   ! �   c �   � #   x    � E  � �   � F   �   � �  � +  � �  � �  �! �   �# �  H$ D  �%    6' �   S'    ( �  &( "   �) "   �)    �)    �) -   
*    8*    I*    ^*    r*    y* 	   �*    �* 	   �*    �*    �* 	   �* $   �*    �* 	   �*    +    + �   + W   �+ %   ?, 
   e, 0   s, 2   �, 4   �, /   - #   <- 
   `- 
   k-    v-    �-    �-    �-    �-    �-    �-    �- 6   �- $   3.    X. /   s. 4   �. -   �. ,   /    3/ $   N/ @   s/ $   �/ V   �/ B   00    s0 g   z0 H   �0    +1    G1    X1    x1    �1     �1    �1    �1    �1    �1    2    2 
   /2 	   =2 
   G2    R2 &   U2    |2    �2 	   �2    �2 	   �2    �2    �2    �2 	   �2    �2    �2    3    3    $3    ?3    ]3 
   e3 	   s3    }3 A   �3 	   �3    �3 '   �3    	4    4    +4    34    S4    [4    o4 w   �4 �   �4 Y   �5 �   6 �  �6 t   �8    �8 +   	9    59    F9    M9    Z9 
   r9 %   �9 N   �9 /   �9 ]   %:     �: >   �: +   �: l   ; f   |; �   �; S   �< :   = )   M= 
   w=    �= 	   �= 	   �=    �=    �=    �=    �=    �=    �=    �=    	>    >    &>    9>    H>    [>    l> 	   |> 
   �>    �>    �>    �>    �>   �>    �?    �?    �? .   �?    -@ O   4@ _   �@ *   �@ 7   A .   GA    vA    ~A    �A p   �A    �A     B ,   B h   <B    �B    �B �   �B &   ^C 7  �C R   �F r   G �   �G 	  3H :   =I 
  xI �   �J Q  K �   cL    \M    xM G   }M T   �M T   N ]   oN Y   �N M   'O r   uO !   �O 1   
P    <P    DP R   TP %   �P    �P    �P    �P    �P i   Q 
   kQ *   yQ H   �Q    �Q    R N    R W   oR e  �R    -T    @T �   [T    &U    2U    ;U    CU    cU .   kU d  �U    �V 
   W    *W 
   AW S  LW *   �X    �X    �X w   �X    SY 	   \Y 2   fY    �Y    �Y +   �Y �   �Y    xZ    �Z M   �Z #   �Z    [ 	   [    ([ (   8[ Z   a[    �[ 0   �[ �   �[ v   �\ ]    ]    ~]   �] K   �^    �^ 6   �^    3_ �   I_ �   `    �` <   �` �   !a �   �a "   ~b (   �b    �b �  �b D   �d &   �d $   �d '   e    Be �   be    $f    Df 2   ^f C   �f    �f    �f $   �f (    g ]  Ig �  �i   9k 9   Lm 5   �m    �m $   �m g  �m �   Vo �   Hp !   �p �   q �  r    �s &   �s   �s U  �u 	   Nw    Xw 7   ew    �w D  �w #   �x C  y �  Sz �   |    �| �   } -   �} )   �} m   �} -  h~ &  � �   �� �  \� x   �� 
  t� a   �� �   � �   �� J   c� �   �� -   a� '   ��    �� 	   ̇    և '   � '   � H   8� 0   �� 	   �� 6   �� ?  � 2   3� �   f� �   c� �   � '   �� !   Ȍ !   �    � 4   #� &   X� ,   � &   �� �   Ӎ +   �� �   �    �� �   Ǐ �   �� �    � I  �� �   � -   �� �   � 	   w� 6  �� k   �� "   $� �  G�    �    )�    >� #   T� +   x�    ��    �� Q   �� �   � �   ��    ��    ��    �� �   Ϛ F  �� �   Ԝ �   ��    �    ,�    B�    W�    p�    ��    �� I   �� 0   � �  � V   �    m� ~   � t   �� U   s�    ɢ _   I� V   ��     � l   	� O   v� �   Ƥ ^   b� P   ��    � �   (� o   � D   �� f   ɧ b   0� A   �� 	   ը ;   ߨ s   � �   �� 6   ?� �   v�    #�   )� I   A� b   �� �   �    �� �  �� �   m�    J� $   c�    �� 	   �� &  �� 2   �� _   �� �   T� �   3� �   � �   �� �   �� �   /� �   ܶ ^   ��   �� a   � {  r� �   �   �� 
   ¼ 
   м 
   ޼ �   � 
   �� 
   �� M   �� c   �� �   \� 
   <�   J� B   N� �   �� 8   � |   R� c   �� 
   3� �   A� 
   �� 
   �� 
   �� 2  �� �   /� �  ��    ��    ��    �� �   ��    �� $   �� 	  �� �   �� %   ��    �� $   �� ;   �� >   � <   Y� &   �� &   �� �   �� #   �� �  �� 
  �� �   �� ]   =� �   ��   E� �  K� 9  6� �   p� �   ,� �   ��    \� l  u�    �� q  � �   s� #  e� �   �� N  � �   ]� �   �    �� ,   *� 9   W�    �� 8   ��    ��    ��    �� �   � 	   �� p    � /   q�    �� 	   ��    �� !   �� v   �� Q   U� D   �� &  �� $   �    8�    A�    H�    c�    �    ��    ��    ��    ��    ��    ��    �� .   �� �   ��    ��    �� 
   ��    ��    ��    ��    ��    ��    �    � '   +� }   S�    �� 4   �� Z   � �   r� �   � �   �� *  �� �   ��   ]� #   }� �   �� 8   5� W   n� T   �� �   � �   �� N   �� n   ��    n� p   � s   �� �   d�    ��     ��    �    2�    Q�    c�    �� )   ��    ��    ��    �� $   ��    �    4�    M�    k�    r�    ��    ��    ��    �� !   �� "   �� {   � �   �� .   !�     P� h   q� f   �� s   A� +   �� ,   �� 3   � 9   B� ;   |� 5   �� �   ��    �� 0  ��    � ?   #� �   c� *   �� �   � �   ��    �� =   b  "   �  �   �  �   V l   � /   f f   � �   �    � '   �    	 <    z   Z �   �    V    ^    g -   n �   � =   ' )   e 4   � %   �    � #    L   '    t ]   � >   �   - H   :	 X   �	 �   �	 =   �
     �
    �
         9     Y    z     � :   � 3   � &  * E   Q 6   � G   � 0       G s   L �   � �   F �   �    � �   � -   T    � �   �    } (   � <   � ;   � c   3 ]   � X   �    N     m �   � 3   F *   z R   � �   � F   � �   ; p   � c   a �   � 0   I A   z �   �    H o   h "   � �   � /   � >   �        , �   K G    K   g �   � _   ? U   � �   � �   v     !    ! S   %! G   y!    �!    �!    �!    "    " -   /" �   ]" e   �"    P# &   e# 0   �# 6   �# (   �# y   $ 6   �$ �   �$ p   O% ,   �% {   �% �   i& +   W' L   �'    �' R   �' G   7(    ( s   �( w   ) *   �) E   �)    �) +   * O   =*    �* }   �* +   "+    N+ .   f+ f   �+ �   �+    �,    �,    �, E   -    Z- Y   q- =   �- �   	. }   �.    ?/ :   P/ �   �/    F0 �   f0 F   1 *   e1 R   �1 �   �1    �2    �2    �2    �2 0   �2 7   *3 @   b3    �3    �3    �3 %   �3 ?   �3 @   =4 
   ~4 B   �4 S   �4    #5 &   55 2   \5 !   �5    �5 V   �5 �   6 B   �6    <7 
   K7 �   Y7 L  8 W   R9    �9 |  �9 �  :; :   = x   <= !   �= v  �= /   N@ h   ~@    �@    �@ �  A    �B d   �B    `C x   �C ^   YD    �D e   �D 4   >E !   sE z   �E E   F ;   VF Z   �F .   �F R   G �   oG �   �G :   �H �   �H z  �I �   @K 3   �K o  3L �   �M �   �N    qO �   �P -   XQ    �Q �   �Q 3   JR 	  ~R `   �T J   �T    4U     DU �  eU    $W �   )W U  �W '  NY I  vZ N   �[ J   \ ]   Z\ ,   �\ *   �\ P   ] c   a] #   �] $   �] 9   ^ +   H^    t^ 0   �^ k   �^ (   +_    T_ `   [_ �   �_ �   �`    9a    Pa    ga M   na d   �a `   !b �   �b q   Xc    �c %   �c �   d 
   �d �   e �  �e �   _h ^   Si 1   �i    �i    �i    �i =   �i -   2j �   `j �   �j T   �k    /l    El (   [l    �l g   �l    	m <   m    Xm /   _m *   �m    �m    �m Q   �m    (n    .n $   Bn    gn )   �n q   �n �   #o U   �o l   Pp R   �p �   q    �q    �q �   r �   �r �   �s 	   Qt s   [t \   �t M   ,u ^   zu a   �u Y   ;v    �v c   �v    w    w    6w    Kw    aw    }w    �w    �w    �w    �w .   �w &   
x .   1x     `x 3   �x +   �x &   �x    y    $y C   6y    zy 	   �y 5   �y 1   �y d   z 1   lz    �z    �z %   �z    �z    { I   ({ ,   r{ N   �{ �   �{    �|    �|    �|    �| 7   } 	   L}    V}    i}    �} "   ~ &   $~    K~ 
   R~ 	   ]~ B   g~    �~   �~    �    �    � $   � %   B�    h� &   �� (   �� ,   ր 3   � /   7�    g� e   n� *   ԁ    ��    � 8   *� H   c� #   �� +   Ђ    �� \   � `   u� P   փ    '�    /� 	   I�    S�    d�    � �  �� y   *�    ��    �� +       � ,   �    3�    8� y   >� d   �� �   �    �� '   � �   :� '   ҉ &   �� &   !� )   H� 7   r� ,   ��    ׊    � @   � !   E� 4   g� 8   �� Y  Ջ C   /� D   s� c   �� "   � �   ?� '   ֎    �� S   �    g� *   �    �� <   �� C   �� (   >� �   g�    ��    � %   �    E�    ^�    w�     ��    ��    �� d   ۑ 	  @�    J� "   j� �   �� �   ��    4�    R�    p�    ��    ��    ��    ��    ٕ    �    �    �    #� 
   4�    B�    Q�    c�    p�    |� '   �� �   �� �   �� �  �� >  � �  N� �   � �  ��    ��   ��    �� �   �� �   �� �  �� �   )� n  ե 6   D� �   {� :   ,�    g� G   �� O   ˨ i   � g   �� �   � �   �� �   U� x  !�    �� �   �� �   �� c   �� �   �    �� �   ı �  �� �   #� �   � 
   ߵ f   � x   Q� 
  ʶ �   շ r   ^� �   Ѹ    g� #   ��    �� R   �� 9   �    K� �   \� ?   � N   *�    y� �   �� �   ;� B   ׼ S   � A   n� v   �� U   '� '   }� �   �� f   '� y   �� �   �    �� /   �� (   �� a   �� 1   V�    ��    �� C   ��    ��    ��    � 3   
� 1   A� E   s�    ��    ��    ��    �� 	   �� _   �� |   [� J   �� =   #�    a� )   i� 4   ��    �� 	   ��    �� 	   ��    ��    ��    �    �    � 
   "�    -�    3�    B�    O�    d�    ~�    �� 
   ��    ��    �� "   ��    ��    �� �   �    �� ^   �� �   �    ��    ��    ��    ��    �    
�    � �   � �   �� R   >�    ��    �� s   ��    0� �   A� �   �� $   W�    |� �   �� �   � [   �� �   S� u   � ,   �� �   �� "   A� #   d� Q   �� �   ��    r� �   �� �   3� �   �� [   G�    ��    ��    ��    ��    �� 	   �� 
   ��    � �    � m   �� �    � �   �� �   �� n   y� P   �� �  9� �  �� �   p� �   f� 
  � f  �    �� �   �� @  e� �   �� �  T� �  � {   � ~  ��    � ?   � �   Y� e  �� H   Z� �   ��    ��    ��    �� �   �� A   ]� u   �� 0   � f   F� L   �� `   �� $   [� �   �� G   2� )   z� [   �� �    �    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: pt_BR
Language-Team: pt_BR <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n > 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library é uma biblioteca popular (e ilegal). Eles pegaram a coleção do Library Genesis e a tornaram facilmente pesquisável. Além disso, eles se tornaram muito eficazes em solicitar novas contribuições de livros, incentivando usuários contribuintes com várias vantagens. Atualmente, eles não contribuem com esses novos livros de volta para o Library Genesis. E, ao contrário do Library Genesis, eles não tornam sua coleção facilmente espelhável, o que impede a preservação ampla. Isso é importante para o modelo de negócios deles, já que cobram dinheiro pelo acesso em massa à sua coleção (mais de 10 livros por dia). Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegais. É inegável que o Z-Library tem sido bem-sucedido em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer nossa parte: garantir a preservação a longo prazo dessa coleção privada. - Anna e a equipe (<a %(reddit)s>Reddit</a>) No lançamento original do Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>), fizemos um espelho do Z-Library, uma grande coleção de livros ilegais. Como lembrete, isso é o que escrevemos naquele post original do blog: Essa coleção data de meados de 2021. Nesse meio tempo, o Z-Library tem crescido a uma taxa impressionante: eles adicionaram cerca de 3,8 milhões de novos livros. Há alguns duplicados lá, claro, mas a maioria parece ser de livros realmente novos ou de digitalizações de maior qualidade de livros previamente submetidos. Isso se deve em grande parte ao aumento do número de moderadores voluntários no Z-Library e ao seu sistema de upload em massa com desduplicação. Gostaríamos de parabenizá-los por essas conquistas. Estamos felizes em anunciar que conseguimos todos os livros que foram adicionados ao Z-Library entre nosso último espelho e agosto de 2022. Também voltamos e raspamos alguns livros que perdemos na primeira vez. No total, essa nova coleção tem cerca de 24TB, que é muito maior que a última (7TB). Nosso espelho agora tem 31TB no total. Novamente, desduplicamos contra o Library Genesis, já que já existem torrents disponíveis para essa coleção. Por favor, vá ao Espelho da Biblioteca Pirata para conferir a nova coleção (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>). Há mais informações lá sobre como os arquivos estão estruturados e o que mudou desde a última vez. Não vamos vincular a partir daqui, já que este é apenas um site de blog que não hospeda nenhum material ilegal. Claro, fazer seed também é uma ótima maneira de nos ajudar. Agradecemos a todos que estão fazendo seed do nosso conjunto anterior de torrents. Estamos gratos pela resposta positiva e felizes que há tantas pessoas que se importam com a preservação do conhecimento e da cultura de uma maneira tão incomum. 3x novos livros adicionados ao Espelho da Biblioteca Pirata (+24TB, 3,8 milhões de livros) Leia os artigos complementares do TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a> - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artigos complementares do TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a> Não muito tempo atrás, as “bibliotecas-sombra” estavam morrendo. O Sci-Hub, o enorme arquivo ilegal de artigos acadêmicos, havia parado de receber novas obras, devido a processos judiciais. A “Z-Library”, a maior biblioteca ilegal de livros, viu seus supostos criadores serem presos por acusações criminais de direitos autorais. Eles incrivelmente conseguiram escapar de sua prisão, mas sua biblioteca não está menos ameaçada. Alguns países já estão fazendo uma versão disso. O TorrentFreak <a %(torrentfreak)s>relatou</a> que China e Japão introduziram exceções de IA em suas leis de direitos autorais. Não está claro para nós como isso interage com tratados internacionais, mas certamente dá cobertura para suas empresas domésticas, o que explica o que temos visto. Quanto ao Acervo da Anna — continuaremos nosso trabalho subterrâneo enraizado em convicções morais. No entanto, nosso maior desejo é entrar na luz e amplificar nosso impacto legalmente. Por favor, reformem os direitos autorais. Quando a Z-Library enfrentou o fechamento, eu já havia feito backup de toda a sua biblioteca e estava procurando uma plataforma para abrigá-la. Essa foi minha motivação para iniciar o Acervo da Anna: uma continuação da missão por trás dessas iniciativas anteriores. Desde então, crescemos para ser a maior biblioteca-sombra do mundo, hospedando mais de 140 milhões de textos protegidos por direitos autorais em vários formatos — livros, artigos acadêmicos, revistas, jornais e além. Minha equipe e eu somos ideólogos. Acreditamos que preservar e hospedar esses arquivos é moralmente correto. Bibliotecas ao redor do mundo estão vendo cortes de financiamento, e também não podemos confiar o patrimônio da humanidade às corporações. Então veio a IA. Praticamente todas as grandes empresas que constroem LLMs nos contataram para treinar com nossos dados. A maioria (mas não todas!) das empresas sediadas nos EUA reconsiderou uma vez que perceberam a natureza ilegal do nosso trabalho. Em contraste, empresas chinesas abraçaram entusiasticamente nossa coleção, aparentemente despreocupadas com sua legalidade. Isso é notável, dado o papel da China como signatária de quase todos os principais tratados internacionais de direitos autorais. Concedemos acesso de alta velocidade a cerca de 30 empresas. A maioria delas são empresas de LLM, e algumas são corretoras de dados, que revenderão nossa coleção. A maioria é chinesa, embora também tenhamos trabalhado com empresas dos EUA, Europa, Rússia, Coreia do Sul e Japão. A DeepSeek <a %(arxiv)s>admitiu</a> que uma versão anterior foi treinada com parte de nossa coleção, embora eles sejam discretos sobre seu modelo mais recente (provavelmente também treinado com nossos dados). Se o Ocidente quiser se manter à frente na corrida dos LLMs e, em última análise, da AGI, precisa reconsiderar sua posição sobre direitos autorais, e logo. Quer você concorde ou não conosco sobre nosso caso moral, isso agora está se tornando um caso de economia, e até mesmo de segurança nacional. Todos os blocos de poder estão construindo super-cientistas artificiais, super-hackers e super-militares. A liberdade de informação está se tornando uma questão de sobrevivência para esses países — até mesmo uma questão de segurança nacional. Nossa equipe é de todo o mundo, e não temos um alinhamento particular. Mas encorajamos países com leis de direitos autorais rígidas a usarem essa ameaça existencial para reformá-las. Então, o que fazer? Nossa primeira recomendação é simples: encurtar o prazo de direitos autorais. Nos EUA, os direitos autorais são concedidos por 70 anos após a morte do autor. Isso é absurdo. Podemos alinhar isso com patentes, que são concedidas por 20 anos após o depósito. Isso deve ser mais do que tempo suficiente para que autores de livros, artigos, música, arte e outras obras criativas sejam totalmente compensados por seus esforços (incluindo projetos de longo prazo, como adaptações cinematográficas). Então, no mínimo, os formuladores de políticas deveriam incluir exceções para a preservação em massa e disseminação de textos. Se a perda de receita de clientes individuais for a principal preocupação, a distribuição em nível pessoal poderia permanecer proibida. Em contrapartida, aqueles capazes de gerenciar vastos repositórios — empresas que treinam LLMs, juntamente com bibliotecas e outros arquivos — seriam cobertos por essas exceções. A reforma dos direitos autorais é necessária para a segurança nacional Resumindo: LLMs chineses (incluindo DeepSeek) são treinados no meu arquivo ilegal de livros e artigos — o maior do mundo. O Ocidente precisa reformular a lei de direitos autorais como uma questão de segurança nacional. Por favor, veja a <a %(all_isbns)s>postagem original do blog</a> para mais informações. Lançamos um desafio para melhorar isso. Ofereceríamos uma recompensa de $6.000 para o primeiro lugar, $3.000 para o segundo lugar e $1.000 para o terceiro lugar. Devido à resposta esmagadora e às incríveis submissões, decidimos aumentar ligeiramente o prêmio e conceder um terceiro lugar dividido entre quatro participantes, com $500 cada. Os vencedores estão abaixo, mas não deixe de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou baixe nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Primeiro lugar $6.000: phiresky Esta <a %(phiresky_github)s>submissão</a> (<a %(annas_archive_note_2951)s>comentário no Gitlab</a>) é simplesmente tudo o que queríamos, e mais! Gostamos especialmente das opções de visualização incrivelmente flexíveis (até mesmo suportando shaders personalizados), mas com uma lista abrangente de predefinições. Também gostamos de como tudo é rápido e suave, da implementação simples (que nem sequer tem um backend), do minimapa inteligente e da explicação extensa em seu <a %(phiresky_github)s>post no blog</a>. Trabalho incrível, e o vencedor merecido! - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nossos corações estão cheios de gratidão. Ideias notáveis Arranha-céus para raridade Muitos controles deslizantes para comparar datasets, como se você fosse um DJ. Barra de escala com número de livros. Rótulos bonitos. Esquema de cores padrão e mapa de calor legais. Visualização de mapa única e filtros Anotações, e também estatísticas ao vivo Estatísticas ao vivo Algumas ideias e implementações adicionais que gostamos particularmente: Poderíamos continuar por um tempo, mas vamos parar por aqui. Certifique-se de ver todas as submissões <a %(annas_archive)s>aqui</a>, ou baixe nosso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Tantas submissões, e cada uma traz uma perspectiva única, seja na interface ou na implementação. Incorporaremos pelo menos a submissão do primeiro lugar em nosso site principal, e talvez algumas outras. Também começamos a pensar em como organizar o processo de identificar, confirmar e, em seguida, arquivar os livros mais raros. Mais novidades virão nessa área. Obrigado a todos que participaram. É incrível que tantas pessoas se importem. Alternância fácil de datasets para comparações rápidas. Todos os ISBNs SSNOs do CADAL Vazamento de dados do CERLALC SSIDs do DuXiu Índice de eBooks do EBSCOhost Google Livros Goodreads Internet Archive ISBNdb Registro Global de Editores ISBN Libby Arquivos no Acervo da Anna Nexus/STC OCLC/Worldcat OpenLibrary Biblioteca Estatal Russa Biblioteca Imperial de Trantor Segundo lugar $3.000: hypha “Embora quadrados e retângulos perfeitos sejam matematicamente agradáveis, eles não oferecem superioridade de localidade em um contexto de mapeamento. Acredito que a assimetria inerente a esses Hilbert ou Morton clássico não é uma falha, mas uma característica. Assim como o famoso contorno em forma de bota da Itália a torna instantaneamente reconhecível em um mapa, as "peculiaridades" únicas dessas curvas podem servir como marcos cognitivos. Essa distinção pode melhorar a memória espacial e ajudar os usuários a se orientarem, potencialmente facilitando a localização de regiões específicas ou a percepção de padrões.” Outra <a %(annas_archive_note_2913)s>submissão</a> incrível. Não tão flexível quanto o primeiro lugar, mas na verdade preferimos sua visualização em nível macro em relação ao primeiro lugar (curva de preenchimento de espaço, bordas, rotulagem, destaque, panorâmica e zoom). Um <a %(annas_archive_note_2971)s>comentário</a> de Joe Davis ressoou conosco: E ainda muitas opções para visualização e renderização, além de uma interface incrivelmente suave e intuitiva. Um sólido segundo lugar! - Anna e a equipe (<a %(reddit)s>Reddit</a>) Há alguns meses, anunciamos uma <a %(all_isbns)s>recompensa de $10.000</a> para fazer a melhor visualização possível dos nossos dados mostrando o espaço de ISBN. Enfatizamos mostrar quais arquivos já arquivamos/não arquivamos, e mais tarde um conjunto de dados descrevendo quantas bibliotecas possuem ISBNs (uma medida de raridade). Ficamos impressionados com a resposta. Houve tanta criatividade. Um grande obrigado a todos que participaram: sua energia e entusiasmo são contagiantes! No final, queríamos responder às seguintes perguntas: <strong>quais livros existem no mundo, quantos já arquivamos e em quais livros devemos focar a seguir?</strong> É ótimo ver tantas pessoas se importando com essas questões. Começamos com uma visualização básica por nós mesmos. Em menos de 300kb, esta imagem representa sucintamente a maior "lista de livros" totalmente aberta já montada na história da humanidade: Terceiro lugar $500 #1: maxlion Nesta <a %(annas_archive_note_2940)s>submissão</a> realmente gostamos dos diferentes tipos de visualizações, em particular as visualizações de comparação e de editoras. Terceiro lugar $500 #2: abetusk Embora não tenha a interface mais polida, esta <a %(annas_archive_note_2917)s>submissão</a> atende a muitos critérios. Gostamos particularmente de sua funcionalidade de comparação. Terceiro lugar $500 #3: conundrumer0 Como o primeiro lugar, esta <a %(annas_archive_note_2975)s>submissão</a> nos impressionou com sua flexibilidade. Em última análise, é isso que faz um ótimo ferramenta de visualização: flexibilidade máxima para usuários avançados, mantendo as coisas simples para usuários comuns. Terceiro lugar $500 #4: charelf A última <a %(annas_archive_note_2947)s>submissão</a> a receber uma recompensa é bastante básica, mas tem algumas características únicas que realmente gostamos. Gostamos de como eles mostram quantos datasets cobrem um determinado ISBN como uma medida de popularidade/confiabilidade. Também gostamos muito da simplicidade, mas eficácia, de usar um controle deslizante de opacidade para comparações. Vencedores da recompensa de $10.000 pela visualização de ISBN Resumo: Recebemos algumas submissões incríveis para a recompensa de $10.000 pela visualização de ISBN. Contexto Como o Acervo da Anna pode alcançar sua missão de fazer backup de todo o conhecimento da humanidade, sem saber quais livros ainda estão por aí? Precisamos de uma lista de tarefas. Uma maneira de mapear isso é através dos números ISBN, que desde os anos 1970 foram atribuídos a cada livro publicado (na maioria dos países). Não há uma autoridade central que conheça todas as atribuições de ISBN. Em vez disso, é um sistema distribuído, onde os países recebem intervalos de números, que então atribuem intervalos menores a grandes editoras, que podem subdividir ainda mais os intervalos para editoras menores. Finalmente, números individuais são atribuídos aos livros. Começamos a mapear ISBNs <a %(blog)s>há dois anos</a> com nossa coleta do ISBNdb. Desde então, coletamos muitas outras fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby e mais. Uma lista completa pode ser encontrada nas páginas “Datasets” e “Torrents” no Acervo da Anna. Agora temos, de longe, a maior coleção totalmente aberta e facilmente baixável de metadata de livros (e, portanto, ISBNs) do mundo. Nós <a %(blog)s>escrevemos extensivamente</a> sobre por que nos importamos com a preservação e por que estamos atualmente em uma janela crítica. Devemos agora identificar livros raros, pouco focados e exclusivamente em risco e preservá-los. Ter bons metadata de todos os livros do mundo ajuda nisso. Recompensa de $10.000 Será dada uma consideração especial à usabilidade e à estética. Mostrar metadados reais para ISBNs individuais ao dar zoom, como título e autor. Melhor curva de preenchimento de espaço. Por exemplo, um zigue-zague, indo de 0 a 4 na primeira linha e depois voltando (em reverso) de 5 a 9 na segunda linha — aplicado recursivamente. Esquemas de cores diferentes ou personalizáveis. Vistas especiais para comparar Datasets. Maneiras de depurar problemas, como outros metadados que não concordam bem (por exemplo, títulos muito diferentes). Anotar imagens com comentários sobre ISBNs ou intervalos. Qualquer heurística para identificar livros raros ou em risco. Quaisquer ideias criativas que você possa ter! Código O código para gerar essas imagens, bem como outros exemplos, pode ser encontrado neste <a %(annas_archive)s>diretório</a>. Criamos um formato de dados compacto, com o qual todas as informações necessárias do ISBN ocupam cerca de 75MB (comprimido). A descrição do formato de dados e o código para gerá-lo podem ser encontrados <a %(annas_archive_l1244_1319)s>aqui</a>. Para a recompensa, você não é obrigado a usar isso, mas provavelmente é o formato mais conveniente para começar. Você pode transformar nosso metadata como quiser (embora todo o seu código deva ser de código aberto). Mal podemos esperar para ver o que você vai criar. Boa sorte! Faça um fork deste repositório e edite este HTML de postagem de blog (nenhum outro backend além do nosso backend Flask é permitido). Faça com que a imagem acima seja suavemente ampliável, para que você possa dar zoom até os ISBNs individuais. Clicar nos ISBNs deve levá-lo a uma página de metadata ou pesquisa no Acervo da Anna. Você ainda deve ser capaz de alternar entre todos os diferentes datasets. Intervalos de países e editoras devem ser destacados ao passar o mouse. Você pode usar, por exemplo, <a %(github_xlcnd_isbnlib)s>data4info.py no isbnlib</a> para informações de países, e nossa coleta “isbngrp” para editoras (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Deve funcionar bem em desktop e mobile. Há muito a explorar aqui, então estamos anunciando uma recompensa para melhorar a visualização acima. Ao contrário da maioria de nossas recompensas, esta tem um prazo. Você deve <a %(annas_archive)s>enviar</a> seu código de código aberto até 31-01-2025 (23:59 UTC). A melhor submissão receberá $6.000, o segundo lugar $3.000 e o terceiro lugar $1.000. Todas as recompensas serão concedidas usando Monero (XMR). Abaixo estão os critérios mínimos. Se nenhuma submissão atender aos critérios, ainda podemos conceder algumas recompensas, mas isso será a nosso critério. Para pontos extras (estas são apenas ideias — deixe sua criatividade fluir): Você PODE se desviar completamente dos critérios mínimos e fazer uma visualização completamente diferente. Se for realmente espetacular, isso qualifica para a recompensa, mas a nosso critério. Faça envios postando um comentário em <a %(annas_archive)s>este problema</a> com um link para seu repositório bifurcado, solicitação de mesclagem ou diferença. - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Esta imagem tem 1000×800 pixels. Cada pixel representa 2.500 ISBNs. Se temos um arquivo para um ISBN, tornamos esse pixel mais verde. Se sabemos que um ISBN foi emitido, mas não temos um arquivo correspondente, tornamos ele mais vermelho. Em menos de 300kb, esta imagem representa sucintamente a maior “lista de livros” totalmente aberta já montada na história da humanidade (alguns centenas de GB comprimidos na íntegra). Ela também mostra: há muito trabalho a ser feito no backup de livros (temos apenas 16%). Visualizando Todos os ISBNs — recompensa de $10.000 até 31-01-2025 Esta imagem representa a maior “lista de livros” totalmente aberta já montada na história da humanidade. Visualizando Além da imagem geral, também podemos olhar para os datasets individuais que adquirimos. Use o menu suspenso e os botões para alternar entre eles. Há muitos padrões interessantes para ver nessas imagens. Por que há alguma regularidade de linhas e blocos, que parece acontecer em diferentes escalas? Quais são as áreas vazias? Por que certos datasets são tão agrupados? Deixaremos essas perguntas como um exercício para o leitor. - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusão Com este padrão, podemos fazer lançamentos de forma mais incremental e adicionar novas fontes de dados com mais facilidade. Já temos alguns lançamentos empolgantes em andamento! Também esperamos que se torne mais fácil para outras bibliotecas-sombra espelharem nossas coleções. Afinal, nosso objetivo é preservar o conhecimento e a cultura humana para sempre, então quanto mais redundância, melhor. Exemplo Vamos olhar para nosso recente lançamento da Z-Library como exemplo. Ele consiste em duas coleções: “<span style="background: #fffaa3">zlib3_records</span>” e “<span style="background: #ffd6fe">zlib3_files</span>”. Isso nos permite raspar e liberar separadamente registros de metadata dos arquivos de livros reais. Assim, lançamos dois torrents com arquivos de metadata: Também lançamos um monte de torrents com pastas de dados binários, mas apenas para a coleção “<span style="background: #ffd6fe">zlib3_files</span>”, 62 no total: Executando <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver o que está dentro: Neste caso, são metadados de um livro conforme relatado pela Z-Library. No nível superior, temos apenas “aacid” e “metadata”, mas nenhum “data_folder”, já que não há dados binários correspondentes. O AACID contém “22430000” como ID principal, que podemos ver é retirado de “zlibrary_id”. Podemos esperar que outros AACs nesta coleção tenham a mesma estrutura. Agora vamos executar <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Este é um metadata AAC muito menor, embora a maior parte deste AAC esteja localizada em outro lugar em um arquivo binário! Afinal, temos um “data_folder” desta vez, então podemos esperar que os dados binários correspondentes estejam localizados em <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. O “metadata” contém o “zlibrary_id”, então podemos facilmente associá-lo ao AAC correspondente na coleção “zlib_records”. Poderíamos ter associado de várias maneiras diferentes, por exemplo, através do AACID — o padrão não prescreve isso. Observe que também não é necessário que o campo "metadata" seja em si JSON. Ele pode ser uma string contendo XML ou qualquer outro formato de dados. Você pode até armazenar informações de metadata no blob binário associado, por exemplo, se for uma grande quantidade de dados. Arquivos e metadata heterogêneos, o mais próximo possível do formato original. Dados binários podem ser servidos diretamente por servidores web como Nginx. Identificadores heterogêneos nas bibliotecas de origem, ou até mesmo a ausência de identificadores. Lançamentos separados de metadata versus dados de arquivos, ou lançamentos apenas de metadata (por exemplo, nosso lançamento ISBNdb). Distribuição através de torrents, embora com a possibilidade de outros métodos de distribuição (por exemplo, IPFS). Registros imutáveis, já que devemos assumir que nossos torrents viverão para sempre. Lançamentos incrementais / lançamentos adicionáveis. Legível e gravável por máquinas, de forma conveniente e rápida, especialmente para nossa pilha (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspeção humana relativamente fácil, embora isso seja secundário à legibilidade por máquinas. Fácil de semear nossas coleções com um seedbox padrão alugado. Objetivos de design Não nos importamos que os arquivos sejam fáceis de navegar manualmente no disco, ou pesquisáveis sem pré-processamento. Não nos importamos em ser diretamente compatíveis com software de biblioteca existente. Embora deva ser fácil para qualquer um semear nossa coleção usando torrents, não esperamos que os arquivos sejam utilizáveis sem conhecimento técnico significativo e comprometimento. Nosso principal caso de uso é a distribuição de arquivos e metadata associada de diferentes coleções existentes. Nossas considerações mais importantes são: Alguns não-objetivos: Como o Acervo da Anna é de código aberto, queremos usar nosso formato diretamente. Quando atualizamos nosso índice de busca, acessamos apenas caminhos publicamente disponíveis, para que qualquer um que faça um fork de nossa biblioteca possa começar rapidamente. <strong>AAC.</strong> AAC (Container do Acervo da Anna) é um único item que consiste em <strong>metadata</strong>, e opcionalmente <strong>dados binários</strong>, ambos imutáveis. Ele possui um identificador globalmente único, chamado <strong>AACID</strong>. <strong>AACID.</strong> O formato do AACID é este: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por exemplo, um AACID real que lançamos é <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Faixa de AACID.</strong> Como os AACIDs contêm timestamps que aumentam monotonamente, podemos usá-los para denotar intervalos dentro de uma coleção específica. Usamos este formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, onde os timestamps são inclusivos. Isso é consistente com a notação ISO 8601. Os intervalos são contínuos e podem se sobrepor, mas em caso de sobreposição devem conter registros idênticos aos lançados anteriormente naquela coleção (já que os AACs são imutáveis). Registros ausentes não são permitidos. <code>{collection}</code>: o nome da coleção, que pode conter letras ASCII, números e sublinhados (mas sem sublinhados duplos). <code>{collection-specific ID}</code>: um identificador específico da coleção, se aplicável, por exemplo, o ID da Z-Library. Pode ser omitido ou truncado. Deve ser omitido ou truncado se o AACID exceder 150 caracteres. <code>{ISO 8601 timestamp}</code>: uma versão curta do ISO 8601, sempre em UTC, por exemplo, <code>20220723T194746Z</code>. Este número deve aumentar monotonamente a cada lançamento, embora seu significado exato possa diferir por coleção. Sugerimos usar o horário da extração ou da geração do ID. <code>{shortuuid}</code>: um UUID, mas comprimido para ASCII, por exemplo, usando base57. Atualmente usamos a biblioteca Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Pasta de dados binários.</strong> Uma pasta com os dados binários de um intervalo de AACs, para uma coleção específica. Estas têm as seguintes propriedades: O diretório deve conter arquivos de dados para todos os AACs dentro do intervalo especificado. Cada arquivo de dados deve ter seu AACID como nome do arquivo (sem extensões). O nome do diretório deve ser uma faixa de AACID, prefixada com <code style="color: green">annas_archive_data__</code>, e sem sufixo. Por exemplo, um de nossos lançamentos reais tem um diretório chamado<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Recomenda-se que essas pastas sejam gerenciáveis em tamanho, por exemplo, não maiores que 100GB-1TB cada, embora essa recomendação possa mudar ao longo do tempo. <strong>Coleção.</strong> Cada AAC pertence a uma coleção, que por definição é uma lista de AACs que são semanticamente consistentes. Isso significa que se você fizer uma mudança significativa no formato dos metadata, então terá que criar uma nova coleção. O padrão <strong>Arquivo de metadata.</strong> Um arquivo de metadata contém a metadata de um intervalo de AACs, para uma coleção específica. Estes têm as seguintes propriedades: <code>data_folder</code> é opcional e é o nome da pasta de dados binários que contém os dados binários correspondentes. O nome do arquivo dos dados binários correspondentes dentro dessa pasta é o AACID do registro. Cada objeto JSON deve conter os seguintes campos no nível superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Nenhum outro campo é permitido. O nome do arquivo deve ser uma faixa de AACID, prefixada com <code style="color: red">annas_archive_meta__</code> e seguida por <code>.jsonl.zstd</code>. Por exemplo, um de nossos lançamentos é chamado<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Como indicado pela extensão do arquivo, o tipo de arquivo é <a %(jsonlines)s>JSON Lines</a> comprimido com <a %(zstd)s>Zstandard</a>. <code>metadata</code> é metadata arbitrária, de acordo com a semântica da coleção. Deve ser semanticamente consistente dentro da coleção. O prefixo <code style="color: red">annas_archive_meta__</code> pode ser adaptado para o nome da sua instituição, por exemplo, <code style="color: red">my_institute_meta__</code>. <strong>Coleções de “registros” e “arquivos”.</strong> Por convenção, é frequentemente conveniente lançar “registros” e “arquivos” como coleções diferentes, para que possam ser lançados em cronogramas diferentes, por exemplo, com base nas taxas de scraping. Um “registro” é uma coleção apenas de metadata, contendo informações como títulos de livros, autores, ISBNs, etc., enquanto “arquivos” são as coleções que contêm os arquivos reais (pdf, epub). Por fim, optamos por um padrão relativamente simples. É bastante flexível, não normativo e está em desenvolvimento. <strong>Torrents.</strong> Os arquivos de metadata e as pastas de dados binários podem ser agrupados em torrents, com um torrent por arquivo de metadata ou um torrent por pasta de dados binários. Os torrents devem ter o nome original do arquivo/diretório mais um sufixo <code>.torrent</code> como seu nome de arquivo. <a %(wikipedia_annas_archive)s>Acervo da Anna</a> se tornou de longe a maior biblioteca-sombra do mundo, e a única biblioteca-sombra de sua escala que é totalmente de código aberto e dados abertos. Abaixo está uma tabela da nossa página de Datasets (ligeiramente modificada): Conseguimos isso de três maneiras: Espelhando bibliotecas-sombra de dados abertos existentes (como Sci-Hub e Library Genesis). Ajudando bibliotecas-sombra que querem ser mais abertas, mas não tinham tempo ou recursos para isso (como a coleção de quadrinhos do Libgen). Raspando bibliotecas que não desejam compartilhar em massa (como a Z-Library). Para (2) e (3) agora gerenciamos uma coleção considerável de torrents nós mesmos (centenas de TBs). Até agora, abordamos essas coleções como casos únicos, significando infraestrutura e organização de dados sob medida para cada coleção. Isso adiciona uma sobrecarga significativa a cada lançamento e torna particularmente difícil fazer lançamentos mais incrementais. É por isso que decidimos padronizar nossos lançamentos. Este é um post técnico no blog no qual estamos introduzindo nosso padrão: <strong>Contêineres do Acervo da Anna</strong>. Contêineres do Acervo da Anna (AAC): padronizando lançamentos da maior biblioteca-sombra do mundo O Acervo da Anna se tornou a maior biblioteca-sombra do mundo, exigindo que padronizemos nossos lançamentos. Mais de 300GB de capas de livros liberadas Finalmente, estamos felizes em anunciar um pequeno lançamento. Em colaboração com o pessoal que opera o fork Libgen.rs, estamos compartilhando todas as capas de livros deles através de torrents e IPFS. Isso distribuirá a carga de visualização das capas entre mais máquinas e as preservará melhor. Em muitos (mas não todos) casos, as capas dos livros estão incluídas nos próprios arquivos, então isso é uma espécie de "dados derivados". Mas tê-las no IPFS ainda é muito útil para a operação diária tanto do Acervo da Anna quanto dos vários forks do Library Genesis. Como de costume, você pode encontrar este lançamento no Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>). Não vamos linkar aqui, mas você pode encontrá-lo facilmente. Esperamos poder relaxar um pouco nosso ritmo, agora que temos uma alternativa decente ao Z-Library. Esta carga de trabalho não é particularmente sustentável. Se você estiver interessado em ajudar com programação, operações de servidor ou trabalho de preservação, entre em contato conosco. Ainda há muito <a %(annas_archive)s>trabalho a ser feito</a>. Obrigado pelo seu interesse e apoio. Mudança para ElasticSearch Algumas consultas demoravam muito, a ponto de monopolizarem todas as conexões abertas. Por padrão, o MySQL tem um comprimento mínimo de palavra, ou seu índice pode ficar realmente grande. As pessoas relataram não conseguir buscar por "Ben Hur". A busca era apenas um pouco rápida quando totalmente carregada na memória, o que exigia que adquiríssemos uma máquina mais cara para rodar isso, além de alguns comandos para pré-carregar o índice na inicialização. Não teríamos sido capazes de estendê-la facilmente para construir novos recursos, como melhor <a %(wikipedia_cjk_characters)s>tokenização para idiomas sem espaços</a>, filtragem/facetação, ordenação, sugestões de "você quis dizer", autocompletar, e assim por diante. Um dos nossos <a %(annas_archive)s>tickets</a> era um conjunto de problemas com nosso sistema de busca. Usávamos a busca de texto completo do MySQL, já que tínhamos todos os nossos dados no MySQL de qualquer forma. Mas tinha seus limites: Depois de conversar com vários especialistas, optamos pelo ElasticSearch. Não tem sido perfeito (as sugestões padrão de “você quis dizer” e os recursos de autocompletar são ruins), mas no geral tem sido muito melhor que o MySQL para busca. Ainda não estamos <a %(youtube)s>muito entusiasmados</a> em usá-lo para qualquer dado crítico (embora tenham feito muitos <a %(elastic_co)s>progressos</a>), mas no geral estamos bastante satisfeitos com a mudança. Por enquanto, implementamos uma busca muito mais rápida, melhor suporte a idiomas, melhor ordenação por relevância, diferentes opções de ordenação e filtragem por idioma/tipo de livro/tipo de arquivo. Se você está curioso sobre como funciona, <a %(annas_archive_l140)s>dê</a> <a %(annas_archive_l1115)s>uma</a> <a %(annas_archive_l1635)s>olhada</a>. É bastante acessível, embora pudesse ter mais comentários… O Acervo da Anna é totalmente open source Acreditamos que a informação deve ser livre, e nosso próprio código não é exceção. Liberamos todo o nosso código em nossa instância do Gitlab hospedada privadamente: <a %(annas_archive)s>Software da Anna</a>. Também usamos o rastreador de problemas para organizar nosso trabalho. Se você quiser se envolver com nosso desenvolvimento, este é um ótimo lugar para começar. Para dar uma ideia das coisas em que estamos trabalhando, veja nosso trabalho recente em melhorias de desempenho no lado do cliente. Como ainda não implementamos a paginação, frequentemente retornávamos páginas de busca muito longas, com 100-200 resultados. Não queríamos cortar os resultados da busca muito cedo, mas isso significava que poderia desacelerar alguns dispositivos. Para isso, implementamos um pequeno truque: envolvemos a maioria dos resultados da busca em comentários HTML (<code><!-- --></code>), e então escrevemos um pequeno Javascript que detectaria quando um resultado deveria se tornar visível, momento em que removeríamos o comentário: "Virtualização" do DOM implementada em 23 linhas, sem necessidade de bibliotecas sofisticadas! Este é o tipo de código pragmático rápido que você acaba criando quando tem tempo limitado e problemas reais que precisam ser resolvidos. Foi relatado que nossa busca agora funciona bem em dispositivos lentos! Outro grande esforço foi automatizar a construção do banco de dados. Quando lançamos, simplesmente juntamos diferentes fontes de forma desordenada. Agora queremos mantê-las atualizadas, então escrevemos vários scripts para baixar novos metadata dos dois forks do Library Genesis e integrá-los. O objetivo não é apenas tornar isso útil para nosso acervo, mas facilitar para qualquer pessoa que queira explorar metadata de bibliotecas-sombra. O objetivo seria um notebook Jupyter que tenha todos os tipos de metadata interessantes disponíveis, para que possamos fazer mais pesquisas, como descobrir qual <a %(blog)s>percentual de ISBNs é preservado para sempre</a>. Finalmente, reformulamos nosso sistema de doações. Agora você pode usar um cartão de crédito para depositar dinheiro diretamente em nossas carteiras de criptomoedas, sem realmente precisar saber nada sobre criptomoedas. Continuaremos monitorando como isso funciona na prática, mas é um grande avanço. Com o Z-Library saindo do ar e seus (supostos) fundadores sendo presos, temos trabalhado incansavelmente para oferecer uma boa alternativa com o Acervo da Anna (não vamos colocar o link aqui, mas você pode procurar no Google). Aqui estão algumas das coisas que alcançamos recentemente. Atualização da Anna: acervo totalmente open source, ElasticSearch, mais de 300GB de capas de livros Temos trabalhado incansavelmente para oferecer uma boa alternativa com o Acervo da Anna. Aqui estão algumas das coisas que alcançamos recentemente. Análise Duplicatas semânticas (diferentes digitalizações do mesmo livro) podem teoricamente ser filtradas, mas é complicado. Ao olhar manualmente através dos quadrinhos, encontramos muitos falsos positivos. Existem algumas duplicatas apenas por MD5, o que é relativamente desperdiçador, mas filtrá-las nos daria apenas cerca de 1% in de economia. Nesta escala, isso ainda é cerca de 1TB, mas também, nesta escala, 1TB não importa muito. Preferimos não arriscar destruir dados acidentalmente nesse processo. Encontramos um monte de dados não relacionados a livros, como filmes baseados em quadrinhos. Isso também parece desperdício, já que esses já estão amplamente disponíveis por outros meios. No entanto, percebemos que não poderíamos simplesmente filtrar arquivos de filmes, já que também existem <em>quadrinhos interativos</em> que foram lançados no computador, que alguém gravou e salvou como filmes. No final, qualquer coisa que pudéssemos deletar da coleção economizaria apenas alguns por cento. Então lembramos que somos acumuladores de dados, e as pessoas que irão espelhar isso também são acumuladores de dados, então, "O QUE VOCÊ QUER DIZER COM DELETAR?!" :) Quando você recebe 95TB despejados em seu cluster de armazenamento, tenta entender o que há ali... Fizemos algumas análises para ver se poderíamos reduzir um pouco o tamanho, como removendo duplicatas. Aqui estão algumas de nossas descobertas: Portanto, estamos apresentando a você a coleção completa e não modificada. É uma grande quantidade de dados, mas esperamos que pessoas suficientes se importem em semeá-la de qualquer maneira. Colaboração Dada a sua dimensão, essa coleção há muito tempo está na nossa lista de desejos, então, após nosso sucesso com o backup do Z-Library, miramos nessa coleção. No início, fizemos a raspagem diretamente, o que foi um grande desafio, já que o servidor deles não estava nas melhores condições. Conseguimos cerca de 15TB dessa forma, mas foi um processo lento. Felizmente, conseguimos entrar em contato com o operador da biblioteca, que concordou em nos enviar todos os dados diretamente, o que foi muito mais rápido. Ainda assim, levou mais de meio ano para transferir e processar todos os dados, e quase perdemos tudo devido à corrupção de disco, o que significaria começar tudo de novo. Essa experiência nos fez acreditar que é importante divulgar esses dados o mais rápido possível, para que possam ser espelhados amplamente. Estamos a apenas um ou dois incidentes de azar de perder essa coleção para sempre! A coleção Mover-se rapidamente significa que a coleção está um pouco desorganizada... Vamos dar uma olhada. Imagine que temos um sistema de arquivos (que na realidade estamos dividindo em torrents): O primeiro diretório, <code>/repository</code>, é a parte mais estruturada disso. Este diretório contém os chamados "milhares de dirs": diretórios, cada um com milhares de arquivos, que são numerados incrementalmente no banco de dados. O diretório <code>0</code> contém arquivos com comic_id de 0 a 999, e assim por diante. Este é o mesmo esquema que a Library Genesis tem usado para suas coleções de ficção e não-ficção. A ideia é que cada "milhar de dir" seja automaticamente transformado em um torrent assim que estiver cheio. No entanto, o operador do Libgen.li nunca fez torrents para esta coleção, e assim os milhares de dirs provavelmente se tornaram inconvenientes, dando lugar aos "dirs não classificados". Estes são <code>/comics0</code> até <code>/comics4</code>. Todos eles contêm estruturas de diretórios únicas, que provavelmente faziam sentido para coletar os arquivos, mas não fazem muito sentido para nós agora. Felizmente, o metadata ainda se refere diretamente a todos esses arquivos, então a organização de armazenamento no disco não importa realmente! O metadata está disponível na forma de um banco de dados MySQL. Isso pode ser baixado diretamente do site do Libgen.li, mas também o disponibilizaremos em um torrent, juntamente com nossa própria tabela com todos os hashes MD5. <q>Dra. Barbara Gordon tenta se perder no mundo mundano da biblioteca…</q> Forks do Libgen Primeiro, um pouco de contexto. Você pode conhecer o Library Genesis por sua épica coleção de livros. Menos pessoas sabem que os voluntários do Library Genesis criaram outros projetos, como uma coleção considerável de revistas e documentos padrão, um backup completo do Sci-Hub (em colaboração com a fundadora do Sci-Hub, Alexandra Elbakyan) e, de fato, uma enorme coleção de quadrinhos. Em algum momento, diferentes operadores de espelhos do Library Genesis seguiram caminhos separados, o que deu origem à situação atual de ter vários "forks" diferentes, todos ainda carregando o nome Library Genesis. O fork Libgen.li possui exclusivamente essa coleção de quadrinhos, além de uma coleção considerável de revistas (na qual também estamos trabalhando). Arrecadação de fundos Estamos lançando esses dados em alguns grandes blocos. O primeiro torrent é de <code>/comics0</code>, que colocamos em um enorme arquivo .tar de 12TB. Isso é melhor para seu disco rígido e software de torrent do que um milhão de arquivos menores. Como parte deste lançamento, estamos fazendo uma arrecadação de fundos. Estamos buscando arrecadar $20.000 para cobrir custos operacionais e de contratação para esta coleção, bem como possibilitar projetos contínuos e futuros. Temos alguns <em>enormes</em> em andamento. <em>Quem estou apoiando com minha doação?</em> Em resumo: estamos fazendo backup de todo o conhecimento e cultura da humanidade e tornando-os facilmente acessíveis. Todo o nosso código e dados são de código aberto, somos um projeto totalmente gerido por voluntários e já salvamos 125TB de livros até agora (além dos torrents já existentes do Libgen e Scihub). No final, estamos construindo um volante que permite e incentiva as pessoas a encontrar, digitalizar e fazer backup de todos os livros do mundo. Escreveremos sobre nosso plano mestre em um post futuro. :) Se você doar para uma assinatura de 12 meses como "Arquivista Admirável" (R$ 780), você poderá <strong>“adotar um torrent”</strong>, o que significa que colocaremos seu nome de usuário ou mensagem no nome de um dos torrents! Você pode doar acessando o <a %(wikipedia_annas_archive)s>Acervo da Anna</a> e clicando no botão “Doar”. Também estamos procurando mais voluntários: engenheiros de software, pesquisadores de segurança, especialistas em comércio anônimo e tradutores. Você também pode nos apoiar fornecendo serviços de hospedagem. E, claro, por favor, semeie nossos torrents! Obrigado a todos que já nos apoiaram tão generosamente! Vocês estão realmente fazendo a diferença. Aqui estão os torrents lançados até agora (ainda estamos processando o restante): Todos os torrents podem ser encontrados no <a %(wikipedia_annas_archive)s>Acervo da Anna</a> em “Datasets” (não vinculamos diretamente, para que os links para este blog não sejam removidos do Reddit, Twitter, etc). A partir daí, siga o link para o site Tor. <a %(news_ycombinator)s>Discutir no Hacker News</a> O que vem a seguir? Um monte de torrents são ótimos para preservação a longo prazo, mas não tanto para acesso diário. Estaremos trabalhando com parceiros de hospedagem para colocar todos esses dados na web (já que o Acervo da Anna não hospeda nada diretamente). Claro que você poderá encontrar esses links de download no Acervo da Anna. Também estamos convidando todos a fazer algo com esses dados! Ajude-nos a analisá-los melhor, desduplicá-los, colocá-los no IPFS, remixá-los, treinar seus modelos de IA com eles, e assim por diante. Tudo é seu, e mal podemos esperar para ver o que você fará com isso. Finalmente, como dito antes, ainda temos alguns lançamentos massivos por vir (se <em>alguém</em> pudesse <em>acidentalmente</em> nos enviar um dump de um <em>certo</em> banco de dados ACS4, você sabe onde nos encontrar...), além de construir o volante para fazer backup de todos os livros do mundo. Então fique ligado, estamos apenas começando. - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) A maior biblioteca-sombra de quadrinhos é provavelmente a de um fork específico do Library Genesis: o Libgen.li. O único administrador que gerencia esse site conseguiu reunir uma coleção insana de quadrinhos com mais de 2 milhões de arquivos, totalizando mais de 95TB. No entanto, ao contrário de outras coleções do Library Genesis, esta não estava disponível em massa através de torrents. Você só podia acessar esses quadrinhos individualmente através de seu servidor pessoal lento — um único ponto de falha. Até hoje! Neste post, vamos contar mais sobre essa coleção e sobre nossa campanha de arrecadação de fundos para apoiar mais esse trabalho. O Acervo da Anna fez backup da maior biblioteca-sombra de quadrinhos do mundo (95TB) — você pode ajudar a semeá-la A maior biblioteca-sombra de quadrinhos do mundo tinha um único ponto de falha... até hoje. Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Acervo da Anna quando possível, mas não vamos mais hospedá-los nós mesmos, nem recomendamos que outros espelhem usando IPFS. Por favor, veja nossa página de Torrents se você quiser ajudar a preservar nossa coleção. Colocando 5.998.794 livros no IPFS Uma multiplicação de cópias Voltando à nossa pergunta original: como podemos afirmar que preservamos nossas coleções para sempre? O principal problema aqui é que nossa coleção tem <a %(torrents_stats)s>crescido</a> rapidamente, raspando e tornando algumas coleções massivas de código aberto (além do trabalho incrível já feito por outras bibliotecas-sombra de dados abertos como Sci-Hub e Library Genesis). Esse crescimento de dados torna mais difícil que as coleções sejam espelhadas ao redor do mundo. O armazenamento de dados é caro! Mas estamos otimistas, especialmente ao observar as três tendências a seguir. O <a %(annas_archive_stats)s>tamanho total</a> de nossas coleções, nos últimos meses, dividido pelo número de seeders de torrent. Tendências de preços de HDD de diferentes fontes (clique para ver o estudo). <a %(critical_window_chinese)s>Versão em Chinês 中文版</a>, discuta no <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Colhemos os frutos mais fáceis Este segue diretamente de nossas prioridades discutidas acima. Preferimos trabalhar na liberação de grandes coleções primeiro. Agora que garantimos algumas das maiores coleções do mundo, esperamos que nosso crescimento seja muito mais lento. Ainda há uma longa cauda de coleções menores, e novos livros são digitalizados ou publicados todos os dias, mas a taxa provavelmente será muito mais lenta. Podemos ainda dobrar ou até triplicar de tamanho, mas ao longo de um período de tempo mais longo. Melhorias no OCR. Prioridades Código de software de ciência e engenharia Versões ficcionais ou de entretenimento de tudo o que foi mencionado acima Dados geográficos (por exemplo, mapas, levantamentos geológicos) Dados internos de corporações ou governos (vazamentos) Dados de medição como medições científicas, dados econômicos, relatórios corporativos Registros de metadata em geral (de não-ficção e ficção; de outros meios, arte, pessoas, etc.; incluindo resenhas) Livros de não-ficção Revistas de não-ficção, jornais, manuais Transcrições de não-ficção de palestras, documentários, podcasts Dados orgânicos como sequências de DNA, sementes de plantas ou amostras microbianas Artigos acadêmicos, periódicos, relatórios Sites de ciência e engenharia, discussões online Transcrições de processos legais ou judiciais Unicamente em risco de destruição (por exemplo, por guerra, cortes de financiamento, processos judiciais ou perseguição política) Raras Unicamente negligenciadas Por que nos importamos tanto com artigos e livros? Vamos deixar de lado nossa crença fundamental na preservação em geral — podemos escrever outro post sobre isso. Então, por que especificamente artigos e livros? A resposta é simples: <strong>densidade de informação</strong>. Por megabyte de armazenamento, o texto escrito armazena a maior quantidade de informação de todos os meios. Embora nos importemos tanto com conhecimento quanto com cultura, nos importamos mais com o primeiro. No geral, encontramos uma hierarquia de densidade de informação e importância da preservação que se parece mais ou menos com isto: A classificação nesta lista é um tanto arbitrária — vários itens estão empatados ou têm discordâncias dentro de nossa equipe — e provavelmente estamos esquecendo algumas categorias importantes. Mas é mais ou menos assim que priorizamos. Alguns desses itens são muito diferentes dos outros para nos preocuparmos (ou já são cuidados por outras instituições), como dados orgânicos ou dados geográficos. Mas a maioria dos itens desta lista é realmente importante para nós. Outro grande fator em nossa priorização é o quanto uma determinada obra está em risco. Preferimos focar em obras que são: Finalmente, nos importamos com a escala. Temos tempo e dinheiro limitados, então preferimos gastar um mês salvando 10.000 livros do que 1.000 livros — se eles forem igualmente valiosos e estiverem em risco. <em><q>O que está perdido não pode ser recuperado; mas vamos salvar o que resta: não por cofres e fechaduras que os afastam do olhar e uso do público, consignando-os ao desperdício do tempo, mas por uma multiplicação de cópias, que os coloque além do alcance de acidentes.</q></em><br>— Thomas Jefferson, 1791 Bibliotecas-sombra O código pode ser de código aberto no Github, mas o Github como um todo não pode ser facilmente espelhado e, assim, preservado (embora, neste caso particular, haja cópias suficientemente distribuídas da maioria dos repositórios de código) Registros de metadata podem ser visualizados livremente no site do Worldcat, mas não baixados em massa (até que nós <a %(worldcat_scrape)s>os raspamos</a>) O Reddit é gratuito para usar, mas recentemente implementou medidas rigorosas contra raspagem, na esteira do treinamento de LLM faminto por dados (mais sobre isso depois) Existem muitas organizações que têm missões e prioridades semelhantes. De fato, há bibliotecas, arquivos, laboratórios, museus e outras instituições encarregadas da preservação desse tipo. Muitas delas são bem financiadas, por governos, indivíduos ou corporações. Mas elas têm um ponto cego enorme: o sistema legal. Aqui reside o papel único das bibliotecas-sombra e a razão pela qual o Acervo da Anna existe. Podemos fazer coisas que outras instituições não têm permissão para fazer. Agora, não é (frequentemente) que podemos arquivar materiais que são ilegais de preservar em outros lugares. Não, é legal em muitos lugares construir um arquivo com quaisquer livros, artigos, revistas, e assim por diante. Mas o que os arquivos legais muitas vezes carecem é de <strong>redundância e longevidade</strong>. Existem livros dos quais apenas uma cópia existe em alguma biblioteca física em algum lugar. Existem registros de metadata guardados por uma única corporação. Existem jornais preservados apenas em microfilme em um único arquivo. Bibliotecas podem sofrer cortes de financiamento, corporações podem falir, arquivos podem ser bombardeados e queimados até o chão. Isso não é hipotético — isso acontece o tempo todo. A coisa que podemos fazer de forma única no Acervo da Anna é armazenar muitas cópias de obras, em grande escala. Podemos coletar artigos, livros, revistas e mais, e distribuí-los em massa. Atualmente, fazemos isso através de torrents, mas as tecnologias exatas não importam e mudarão com o tempo. A parte importante é distribuir muitas cópias pelo mundo. Esta citação de mais de 200 anos atrás ainda soa verdadeira: Uma nota rápida sobre domínio público. Como o Acervo da Anna foca exclusivamente em atividades que são ilegais em muitos lugares ao redor do mundo, não nos preocupamos com coleções amplamente disponíveis, como livros de domínio público. Entidades legais geralmente já cuidam bem disso. No entanto, há considerações que às vezes nos fazem trabalhar em coleções publicamente disponíveis: - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Os custos de armazenamento continuam a cair exponencialmente 3. Melhorias na densidade de informação Atualmente, armazenamos livros nos formatos brutos em que nos são entregues. Claro, eles são comprimidos, mas muitas vezes ainda são grandes digitalizações ou fotografias de páginas. Até agora, as únicas opções para reduzir o tamanho total de nossa coleção foram através de compressão mais agressiva ou deduplicação. No entanto, para obter economias significativas, ambas são muito prejudiciais para o nosso gosto. A compressão pesada de fotos pode tornar o texto quase ilegível. E a deduplicação requer alta confiança de que os livros sejam exatamente os mesmos, o que muitas vezes é impreciso, especialmente se o conteúdo for o mesmo, mas as digitalizações forem feitas em ocasiões diferentes. Sempre houve uma terceira opção, mas sua qualidade era tão abismal que nunca a consideramos: <strong>OCR, ou Reconhecimento Óptico de Caracteres</strong>. Este é o processo de converter fotos em texto simples, usando IA para detectar os caracteres nas fotos. Ferramentas para isso existem há muito tempo e são bastante decentes, mas "bastante decente" não é suficiente para fins de preservação. No entanto, modelos recentes de aprendizado profundo multimodal têm feito progressos extremamente rápidos, embora ainda a custos elevados. Esperamos que tanto a precisão quanto os custos melhorem dramaticamente nos próximos anos, a ponto de se tornar realista aplicá-los a toda a nossa biblioteca. Quando isso acontecer, provavelmente ainda preservaremos os arquivos originais, mas, além disso, poderíamos ter uma versão muito menor de nossa biblioteca que a maioria das pessoas desejará espelhar. O ponto chave é que o texto bruto em si se comprime ainda melhor e é muito mais fácil de deduplicar, proporcionando-nos ainda mais economias. No geral, não é irrealista esperar pelo menos uma redução de 5-10x no tamanho total dos arquivos, talvez até mais. Mesmo com uma redução conservadora de 5x, estaríamos olhando para <strong>$1.000–$3.000 em 10 anos, mesmo que nossa biblioteca triplique de tamanho</strong>. No momento da escrita, os <a %(diskprices)s>preços dos discos</a> por TB estão em torno de $12 para discos novos, $8 para discos usados e $4 para fita. Se formos conservadores e olharmos apenas para discos novos, isso significa que armazenar um petabyte custa cerca de $12.000. Se assumirmos que nossa biblioteca triplicará de 900TB para 2,7PB, isso significaria $32.400 para espelhar toda a nossa biblioteca. Adicionando eletricidade, custo de outros hardwares, e assim por diante, vamos arredondar para $40.000. Ou com fita, mais como $15.000–$20.000. Por um lado, <strong>$15.000–$40.000 para a soma de todo o conhecimento humano é uma pechincha</strong>. Por outro lado, é um pouco íngreme esperar toneladas de cópias completas, especialmente se também quisermos que essas pessoas continuem semeando seus torrents para o benefício de outros. Isso é hoje. Mas o progresso avança: Os custos de discos rígidos por TB foram reduzidos em cerca de um terço nos últimos 10 anos e provavelmente continuarão a cair em um ritmo semelhante. A fita parece estar em uma trajetória semelhante. Os preços dos SSDs estão caindo ainda mais rápido e podem superar os preços dos HDDs até o final da década. Se isso se mantiver, então em 10 anos podemos estar olhando para apenas $5.000–$13.000 para espelhar toda a nossa coleção (1/3), ou ainda menos se crescermos menos em tamanho. Embora ainda seja muito dinheiro, isso será acessível para muitas pessoas. E pode ser ainda melhor por causa do próximo ponto… No Acervo da Anna, frequentemente nos perguntam como podemos afirmar que preservamos nossas coleções para sempre, quando o tamanho total já está se aproximando de 1 Petabyte (1000 TB) e continua crescendo. Neste artigo, vamos examinar nossa filosofia e ver por que a próxima década é crítica para nossa missão de preservar o conhecimento e a cultura da humanidade. Janela crítica Se essas previsões forem precisas, nós <strong>só precisamos esperar alguns anos</strong> antes que toda a nossa coleção seja amplamente espelhada. Assim, nas palavras de Thomas Jefferson, "colocada além do alcance do acidente". Infelizmente, o advento dos LLMs, e seu treinamento faminto por dados, colocou muitos detentores de direitos autorais na defensiva. Ainda mais do que já estavam. Muitos sites estão tornando mais difícil raspar e arquivar, processos judiciais estão voando por aí, e enquanto isso, bibliotecas e arquivos físicos continuam a ser negligenciados. Só podemos esperar que essas tendências continuem a piorar, e muitas obras sejam perdidas bem antes de entrarem em domínio público. <strong>Estamos à beira de uma revolução na preservação, mas <q>o perdido não pode ser recuperado.</q></strong> Temos uma janela crítica de cerca de 5-10 anos durante a qual ainda é bastante caro operar uma biblioteca-sombra e criar muitos espelhos ao redor do mundo, e durante a qual o acesso ainda não foi completamente fechado. Se conseguirmos atravessar essa janela, então de fato teremos preservado o conhecimento e a cultura da humanidade para sempre. Não devemos deixar esse tempo ser desperdiçado. Não devemos deixar essa janela crítica se fechar para nós. Vamos lá. A janela crítica das bibliotecas-sombra Como podemos afirmar que preservamos nossas coleções para sempre, quando elas já estão se aproximando de 1 PB? Coleção Algumas informações adicionais sobre a coleção. <a %(duxiu)s>Duxiu</a> é um banco de dados massivo de livros digitalizados, criado pelo <a %(chaoxing)s>SuperStar Digital Library Group</a>. A maioria são livros acadêmicos, digitalizados para torná-los disponíveis digitalmente para universidades e bibliotecas. Para nosso público de língua inglesa, <a %(library_princeton)s>Princeton</a> e a <a %(guides_lib_uw)s>Universidade de Washington</a> têm boas visões gerais. Há também um excelente artigo que fornece mais contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (procure no Acervo da Anna). Os livros do Duxiu há muito tempo são pirateados na internet chinesa. Geralmente, são vendidos por menos de um dólar por revendedores. Eles são tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(github_duty_machine)s>aqui</a> e <a %(github_821_github_io)s>aqui</a>. Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Isso estava no topo da nossa lista de tarefas, e alocamos vários meses de trabalho em tempo integral para isso. No entanto, recentemente, um voluntário incrível, surpreendente e talentoso nos procurou, dizendo que já havia feito todo esse trabalho — a um grande custo. Eles compartilharam a coleção completa conosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente notável. Eles concordaram em pedir ajuda desta forma para que a coleção fosse OCRizada. A coleção possui 7.543.702 arquivos. Isso é mais do que a não-ficção do Library Genesis (cerca de 5,3 milhões). O tamanho total dos arquivos é de cerca de 359TB (326TiB) em sua forma atual. Estamos abertos a outras propostas e ideias. Basta nos contatar. Confira o Acervo da Anna para mais informações sobre nossas coleções, esforços de preservação e como você pode ajudar. Obrigado! Páginas de exemplo Para nos provar que você tem um bom pipeline, aqui estão algumas páginas de exemplo para começar, de um livro sobre supercondutores. Seu pipeline deve lidar adequadamente com matemática, tabelas, gráficos, notas de rodapé, e assim por diante. Envie suas páginas processadas para nosso e-mail. Se estiverem boas, enviaremos mais em particular, e esperamos que você consiga rodar rapidamente seu pipeline nelas também. Uma vez que estivermos satisfeitos, podemos fazer um acordo. - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versão chinesa 中文版</a>, <a %(news_ycombinator)s>Discutir no Hacker News</a> Este é um breve post no blog. Estamos procurando alguma empresa ou instituição para nos ajudar com OCR e extração de texto para uma coleção massiva que adquirimos, em troca de acesso exclusivo antecipado. Após o período de embargo, liberaremos, é claro, toda a coleção. Texto acadêmico de alta qualidade é extremamente útil para o treinamento de LLMs. Embora nossa coleção seja chinesa, isso deve ser útil até mesmo para o treinamento de LLMs em inglês: os modelos parecem codificar conceitos e conhecimentos independentemente da língua de origem. Para isso, o texto precisa ser extraído das digitalizações. O que o Acervo da Anna ganha com isso? Pesquisa de texto completo dos livros para seus usuários. Como nossos objetivos estão alinhados com os dos desenvolvedores de LLM, estamos procurando um colaborador. Estamos dispostos a lhe dar <strong>acesso antecipado exclusivo a esta coleção em massa por 1 ano</strong>, se você puder fazer OCR e extração de texto adequados. Se você estiver disposto a compartilhar todo o código do seu pipeline conosco, estaríamos dispostos a embargar a coleção por mais tempo. Acesso exclusivo para empresas de LLM à maior coleção de livros de não-ficção chineses do mundo <em><strong>Resumo:</strong> O Acervo da Anna adquiriu uma coleção única de 7,5 milhões / 350TB de livros de não-ficção chineses — maior que a Library Genesis. Estamos dispostos a dar a uma empresa de LLM acesso exclusivo, em troca de OCR de alta qualidade e extração de texto.</em> Arquitetura do sistema Então, digamos que você encontrou algumas empresas dispostas a hospedar seu site sem fechá-lo — vamos chamá-las de “provedores amantes da liberdade” 😄. Você rapidamente descobrirá que hospedar tudo com eles é bastante caro, então pode querer encontrar alguns “provedores baratos” e fazer a hospedagem real lá, usando os provedores amantes da liberdade como proxy. Se você fizer isso corretamente, os provedores baratos nunca saberão o que você está hospedando e nunca receberão reclamações. Com todos esses provedores, há o risco de eles fecharem você de qualquer maneira, então você também precisa de redundância. Precisamos disso em todos os níveis da nossa pilha. Uma empresa um tanto amante da liberdade que se colocou em uma posição interessante é a Cloudflare. Eles <a %(blog_cloudflare)s>argumentaram</a> que não são um provedor de hospedagem, mas uma utilidade, como um ISP. Portanto, não estão sujeitos a solicitações de remoção da DMCA ou outras, e encaminham quaisquer solicitações para o seu provedor de hospedagem real. Eles chegaram a ir ao tribunal para proteger essa estrutura. Podemos, portanto, usá-los como outra camada de cache e proteção. A Cloudflare não aceita pagamentos anônimos, então só podemos usar seu plano gratuito. Isso significa que não podemos usar seus recursos de balanceamento de carga ou failover. Portanto, <a %(annas_archive_l255)s>implementamos isso nós mesmos</a> no nível do domínio. Ao carregar a página, o navegador verificará se o domínio atual ainda está disponível e, caso contrário, reescreverá todos os URLs para um domínio diferente. Como a Cloudflare armazena em cache muitas páginas, isso significa que um usuário pode acessar nosso domínio principal, mesmo que o servidor proxy esteja fora do ar, e então, no próximo clique, ser movido para outro domínio. Ainda temos preocupações operacionais normais para lidar, como monitorar a saúde do servidor, registrar erros de backend e frontend, e assim por diante. Nossa arquitetura de failover permite mais robustez nesse aspecto também, por exemplo, executando um conjunto completamente diferente de servidores em um dos domínios. Podemos até executar versões mais antigas do código e dos datasets nesse domínio separado, caso um bug crítico na versão principal passe despercebido. Também podemos nos proteger contra a Cloudflare se voltar contra nós, removendo-a de um dos domínios, como este domínio separado. Diferentes permutações dessas ideias são possíveis. Conclusão Tem sido uma experiência interessante aprender a configurar um motor de busca de biblioteca-sombra robusto e resiliente. Há muitos mais detalhes para compartilhar em posts futuros, então me avise sobre o que você gostaria de saber mais! Como sempre, estamos em busca de doações para apoiar este trabalho, então não deixe de visitar a página de Doações no Acervo da Anna. Também estamos procurando outros tipos de apoio, como subsídios, patrocinadores de longo prazo, provedores de pagamento de alto risco, talvez até anúncios (de bom gosto!). E se você quiser contribuir com seu tempo e habilidades, estamos sempre procurando desenvolvedores, tradutores, e assim por diante. Obrigado pelo seu interesse e apoio. Tokens de inovação Vamos começar com nossa pilha de tecnologia. Ela é deliberadamente entediante. Usamos Flask, MariaDB e ElasticSearch. E é literalmente isso. A busca é em grande parte um problema resolvido, e não pretendemos reinventá-la. Além disso, temos que gastar nossos <a %(mcfunley)s>tokens de inovação</a> em outra coisa: não sermos derrubados pelas autoridades. Então, quão legal ou ilegal é exatamente o Acervo da Anna? Isso depende principalmente da jurisdição legal. A maioria dos países acredita em alguma forma de direitos autorais, o que significa que pessoas ou empresas recebem um monopólio exclusivo sobre certos tipos de obras por um determinado período de tempo. Como observação, no Acervo da Anna acreditamos que, embora existam alguns benefícios, no geral os direitos autorais são um prejuízo líquido para a sociedade — mas essa é uma história para outro momento. Esse monopólio exclusivo sobre certas obras significa que é ilegal para qualquer pessoa fora desse monopólio distribuir diretamente essas obras — incluindo nós. Mas o Acervo da Anna é um motor de busca que não distribui diretamente essas obras (pelo menos não em nosso site na clearnet), então deveríamos estar bem, certo? Não exatamente. Em muitas jurisdições, não é apenas ilegal distribuir obras protegidas por direitos autorais, mas também vincular a lugares que o fazem. Um exemplo clássico disso é a lei DMCA dos Estados Unidos. Esse é o extremo mais rigoroso do espectro. No outro extremo do espectro, teoricamente, poderia haver países sem leis de direitos autorais, mas esses realmente não existem. Praticamente todos os países têm algum tipo de lei de direitos autorais em vigor. A aplicação é uma história diferente. Existem muitos países com governos que não se importam em aplicar a lei de direitos autorais. Existem também países entre os dois extremos, que proíbem a distribuição de obras protegidas por direitos autorais, mas não proíbem a vinculação a tais obras. Outra consideração é no nível da empresa. Se uma empresa opera em uma jurisdição que não se importa com direitos autorais, mas a própria empresa não está disposta a correr nenhum risco, então eles podem fechar seu site assim que alguém reclamar sobre ele. Finalmente, uma grande consideração são os pagamentos. Como precisamos permanecer anônimos, não podemos usar métodos de pagamento tradicionais. Isso nos deixa com criptomoedas, e apenas um pequeno subconjunto de empresas as aceita (existem cartões de débito virtuais pagos por cripto, mas eles muitas vezes não são aceitos). - Anna e a equipe (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Eu opero o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, o maior motor de busca de código aberto sem fins lucrativos do mundo para <a %(wikipedia_shadow_library)s>bibliotecas-sombra</a>, como Sci-Hub, Library Genesis e Z-Library. Nosso objetivo é tornar o conhecimento e a cultura facilmente acessíveis e, em última análise, construir uma comunidade de pessoas que, juntas, arquivam e preservam <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos os livros do mundo</a>. Neste artigo, mostrarei como operamos este site e os desafios únicos que vêm com a operação de um site com status legal questionável, já que não há "AWS para instituições de caridade-sombra". <em>Confira também o artigo irmão <a %(blog_how_to_become_a_pirate_archivist)s>Como se tornar um arquivista pirata</a>.</em> Como operar uma biblioteca-sombra: operações no Acervo da Anna Não há <q>AWS para instituições de caridade-sombra,</q> então como operamos o Acervo da Anna? Ferramentas Servidor de aplicação: Flask, MariaDB, ElasticSearch, Docker. Desenvolvimento: Gitlab, Weblate, Zulip. Gerenciamento de servidores: Ansible, Checkmk, UFW. Hospedagem estática Onion: Tor, Nginx. Servidor proxy: Varnish. Vamos ver quais ferramentas usamos para realizar tudo isso. Isso está evoluindo muito à medida que encontramos novos problemas e descobrimos novas soluções. Há algumas decisões sobre as quais temos ido e voltado. Uma delas é a comunicação entre servidores: costumávamos usar Wireguard para isso, mas descobrimos que ele ocasionalmente para de transmitir qualquer dado, ou só transmite dados em uma direção. Isso aconteceu com várias configurações diferentes de Wireguard que tentamos, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Também tentamos tunelar portas via SSH, usando autossh e sshuttle, mas encontramos <a %(github_sshuttle)s>problemas lá</a> (embora ainda não esteja claro para mim se o autossh sofre de problemas de TCP sobre TCP ou não — só parece uma solução instável para mim, mas talvez esteja tudo bem?). Em vez disso, voltamos para conexões diretas entre servidores, escondendo que um servidor está rodando em provedores baratos usando filtragem de IP com UFW. Isso tem a desvantagem de que o Docker não funciona bem com UFW, a menos que você use <code>network_mode: "host"</code>. Tudo isso é um pouco mais propenso a erros, porque você exporá seu servidor à internet com apenas uma pequena configuração incorreta. Talvez devêssemos voltar para o autossh — feedback seria muito bem-vindo aqui. Também temos ido e voltado entre Varnish e Nginx. Atualmente gostamos do Varnish, mas ele tem suas peculiaridades e arestas. O mesmo se aplica ao Checkmk: não o amamos, mas funciona por enquanto. O Weblate tem sido aceitável, mas não incrível — às vezes temo que ele perca meus dados sempre que tento sincronizá-lo com nosso repositório git. O Flask tem sido bom no geral, mas tem algumas peculiaridades estranhas que custaram muito tempo para depurar, como configurar domínios personalizados ou problemas com sua integração SqlAlchemy. Até agora, as outras ferramentas têm sido ótimas: não temos reclamações sérias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Todos eles tiveram alguns problemas, mas nada muito sério ou que consuma muito tempo. Comunidade O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico: fazer este trabalho nas sombras pode ser incrivelmente solitário. Dependendo do que você está planejando fazer e do seu modelo de ameaça, você pode ter que ser muito cuidadoso. Em uma extremidade do espectro, temos pessoas como Alexandra Elbakyan*, a fundadora do Sci-Hub, que é muito aberta sobre suas atividades. Mas ela corre alto risco de ser presa se visitar um país ocidental neste momento, e pode enfrentar décadas de prisão. É um risco que você estaria disposto a correr? Estamos na outra extremidade do espectro; sendo muito cuidadosos para não deixar nenhum rastro e tendo uma forte segurança operacional. * Como mencionado no HN por "ynno", Alexandra inicialmente não queria ser conhecida: "Seus servidores foram configurados para emitir mensagens de erro detalhadas do PHP, incluindo o caminho completo do arquivo fonte com falha, que estava sob o diretório /home/<USER>" Portanto, use nomes de usuário aleatórios nos computadores que você usa para essas coisas, caso você configure algo incorretamente. Esse sigilo, no entanto, vem com um custo psicológico. A maioria das pessoas adora ser reconhecida pelo trabalho que faz, e ainda assim você não pode receber nenhum crédito por isso na vida real. Até mesmo coisas simples podem ser desafiadoras, como amigos perguntando o que você tem feito (em algum momento "mexendo com meu NAS / homelab" fica velho). É por isso que é tão importante encontrar alguma comunidade. Você pode abrir mão de um pouco de segurança operacional confiando em alguns amigos muito próximos, que você sabe que pode confiar profundamente. Mesmo assim, tenha cuidado para não colocar nada por escrito, caso eles tenham que entregar seus e-mails às autoridades, ou se seus dispositivos forem comprometidos de alguma outra forma. Melhor ainda é encontrar alguns colegas piratas. Se seus amigos próximos estiverem interessados em se juntar a você, ótimo! Caso contrário, você pode encontrar outros online. Infelizmente, esta ainda é uma comunidade de nicho. Até agora, encontramos apenas um punhado de outros que estão ativos neste espaço. Bons pontos de partida parecem ser os fóruns do Library Genesis e o r/DataHoarder. A Archive Team também tem indivíduos com ideias semelhantes, embora operem dentro da lei (mesmo que em algumas áreas cinzentas da lei). As cenas tradicionais de "warez" e pirataria também têm pessoas que pensam de maneira semelhante. Estamos abertos a ideias sobre como fomentar a comunidade e explorar ideias. Sinta-se à vontade para nos enviar uma mensagem no Twitter ou Reddit. Talvez possamos hospedar algum tipo de fórum ou grupo de bate-papo. Um desafio é que isso pode ser facilmente censurado ao usar plataformas comuns, então teríamos que hospedar nós mesmos. Há também um equilíbrio entre ter essas discussões totalmente públicas (mais potencial de engajamento) versus torná-las privadas (não deixar "alvos" potenciais saberem que estamos prestes a espelhá-los). Teremos que pensar sobre isso. Deixe-nos saber se você está interessado nisso! Conclusão Esperamos que isso seja útil para novos arquivistas piratas iniciantes. Estamos animados para recebê-lo neste mundo, então não hesite em entrar em contato. Vamos preservar o máximo possível do conhecimento e cultura do mundo, e espelhá-lo amplamente. Projetos 4. Seleção de dados Frequentemente, você pode usar a metadata para determinar um subconjunto razoável de dados para baixar. Mesmo que você eventualmente queira baixar todos os dados, pode ser útil priorizar os itens mais importantes primeiro, caso você seja detectado e as defesas sejam melhoradas, ou porque você precisaria comprar mais discos, ou simplesmente porque algo mais surge em sua vida antes que você possa baixar tudo. Por exemplo, uma coleção pode ter várias edições do mesmo recurso subjacente (como um livro ou um filme), onde uma é marcada como sendo de melhor qualidade. Salvar essas edições primeiro faria muito sentido. Você pode eventualmente querer salvar todas as edições, já que em alguns casos a metadata pode estar incorretamente etiquetada, ou pode haver compensações desconhecidas entre edições (por exemplo, a "melhor edição" pode ser a melhor em muitos aspectos, mas pior em outros, como um filme ter uma resolução mais alta, mas faltar legendas). Você também pode pesquisar seu banco de dados de metadata para encontrar coisas interessantes. Qual é o maior arquivo hospedado e por que é tão grande? Qual é o menor arquivo? Existem padrões interessantes ou inesperados quando se trata de certas categorias, idiomas, e assim por diante? Existem títulos duplicados ou muito semelhantes? Existem padrões de quando os dados foram adicionados, como um dia em que muitos arquivos foram adicionados de uma vez? Você pode frequentemente aprender muito observando o conjunto de dados de diferentes maneiras. No nosso caso, deduplicamos os livros da Z-Library contra os hashes md5 na Library Genesis, economizando assim muito tempo de download e espaço em disco. Esta é uma situação bastante única, no entanto. Na maioria dos casos, não existem bancos de dados abrangentes de quais arquivos já estão devidamente preservados por colegas piratas. Isso em si é uma grande oportunidade para alguém por aí. Seria ótimo ter uma visão geral regularmente atualizada de coisas como músicas e filmes que já estão amplamente semeados em sites de torrent, e são, portanto, de menor prioridade para incluir em espelhos piratas. 6. Distribuição Você tem os dados, o que lhe dá posse do primeiro espelho pirata do mundo do seu alvo (provavelmente). De muitas maneiras, a parte mais difícil já passou, mas a parte mais arriscada ainda está à sua frente. Afinal, até agora você foi furtivo; voando sob o radar. Tudo o que você precisava fazer era usar um bom VPN o tempo todo, não preencher seus dados pessoais em nenhum formulário (óbvio), e talvez usar uma sessão de navegador especial (ou até mesmo um computador diferente). Agora você tem que distribuir os dados. No nosso caso, primeiro queríamos contribuir com os livros de volta para o Library Genesis, mas rapidamente descobrimos as dificuldades nisso (classificação de ficção vs não-ficção). Então decidimos pela distribuição usando torrents no estilo Library Genesis. Se você tiver a oportunidade de contribuir para um projeto existente, isso pode economizar muito tempo. No entanto, atualmente não há muitos espelhos piratas bem organizados por aí. Então, digamos que você decida distribuir torrents por conta própria. Tente manter esses arquivos pequenos, para que sejam fáceis de espelhar em outros sites. Você terá que semear os torrents por conta própria, enquanto ainda permanece anônimo. Você pode usar uma VPN (com ou sem encaminhamento de porta) ou pagar com Bitcoins misturados por um Seedbox. Se você não sabe o que alguns desses termos significam, terá muito o que ler, pois é importante que você entenda as compensações de risco aqui. Você pode hospedar os arquivos torrent em sites de torrent existentes. No nosso caso, escolhemos realmente hospedar um site, já que também queríamos espalhar nossa filosofia de forma clara. Você pode fazer isso por conta própria de maneira semelhante (usamos Njalla para nossos domínios e hospedagem, pagos com Bitcoins misturados), mas também sinta-se à vontade para nos contatar para que possamos hospedar seus torrents. Estamos procurando construir um índice abrangente de espelhos piratas ao longo do tempo, se essa ideia pegar. Quanto à seleção de VPN, já foi escrito muito sobre isso, então vamos apenas repetir o conselho geral de escolher pela reputação. Políticas de não registro testadas em tribunal com longos históricos de proteção à privacidade são a opção de menor risco, na nossa opinião. Note que mesmo quando você faz tudo certo, nunca pode chegar a zero risco. Por exemplo, ao semear seus torrents, um ator estatal altamente motivado provavelmente pode observar os fluxos de dados de entrada e saída dos servidores VPN e deduzir quem você é. Ou você pode simplesmente cometer um erro de alguma forma. Provavelmente já cometemos, e cometeremos novamente. Felizmente, os estados-nação não se importam <em>tanto</em> com pirataria. Uma decisão a ser tomada para cada projeto é se publicá-lo usando a mesma identidade de antes ou não. Se você continuar usando o mesmo nome, então erros na segurança operacional de projetos anteriores podem voltar para te prejudicar. Mas publicar sob nomes diferentes significa que você não constrói uma reputação duradoura. Escolhemos ter uma forte segurança operacional desde o início para que possamos continuar usando a mesma identidade, mas não hesitaremos em publicar sob um nome diferente se cometermos um erro ou se as circunstâncias exigirem. Divulgar a palavra pode ser complicado. Como dissemos, esta ainda é uma comunidade de nicho. Originalmente postamos no Reddit, mas realmente ganhamos tração no Hacker News. Por enquanto, nossa recomendação é postar em alguns lugares e ver o que acontece. E novamente, entre em contato conosco. Adoraríamos espalhar a palavra sobre mais esforços de arquivismo pirata. 1. Seleção de domínio / filosofia Não falta conhecimento e patrimônio cultural a serem salvos, o que pode ser avassalador. É por isso que muitas vezes é útil parar um momento e pensar sobre qual pode ser sua contribuição. Cada um tem uma maneira diferente de pensar sobre isso, mas aqui estão algumas perguntas que você poderia se fazer: No nosso caso, nos importávamos particularmente com a preservação a longo prazo da ciência. Sabíamos sobre o Library Genesis, e como ele foi totalmente espelhado muitas vezes usando torrents. Adoramos essa ideia. Então, um dia, um de nós tentou encontrar alguns livros didáticos científicos no Library Genesis, mas não conseguiu encontrá-los, colocando em dúvida o quão completo ele realmente era. Então procuramos esses livros didáticos online e os encontramos em outros lugares, o que plantou a semente para o nosso projeto. Mesmo antes de sabermos sobre o Z-Library, tivemos a ideia de não tentar coletar todos esses livros manualmente, mas de nos concentrar em espelhar coleções existentes e contribuir com elas de volta para o Library Genesis. Quais habilidades você tem que pode usar a seu favor? Por exemplo, se você é um especialista em segurança online, pode encontrar maneiras de derrotar bloqueios de IP para alvos seguros. Se você é ótimo em organizar comunidades, então talvez possa reunir algumas pessoas em torno de um objetivo. É útil saber um pouco de programação, mesmo que apenas para manter uma boa segurança operacional durante todo esse processo. Qual seria uma área de alto impacto para se concentrar? Se você vai gastar X horas em arquivamento pirata, então como você pode obter o maior "retorno sobre o investimento"? Quais são as maneiras únicas que você está pensando sobre isso? Você pode ter algumas ideias ou abordagens interessantes que outros podem ter perdido. Quanto tempo você tem para isso? Nosso conselho seria começar pequeno e fazer projetos maiores à medida que você se acostuma, mas pode se tornar tudo consumindo. Por que você está interessado nisso? Pelo que você é apaixonado? Se conseguirmos um grupo de pessoas que arquivem os tipos de coisas que elas especificamente se importam, isso cobriria muito! Você saberá muito mais do que a pessoa média sobre sua paixão, como quais são os dados importantes a serem salvos, quais são as melhores coleções e comunidades online, e assim por diante. 3. Raspagem de metadata Data de adição/modificação: para que você possa voltar mais tarde e baixar arquivos que não baixou antes (embora você também possa usar o ID ou hash para isso). Hash (md5, sha1): para confirmar que você baixou o arquivo corretamente. ID: pode ser algum ID interno, mas IDs como ISBN ou DOI também são úteis. Nome do arquivo / localização Descrição, categoria, tags, autores, idioma, etc. Tamanho: para calcular quanto espaço em disco você precisa. Vamos ser um pouco mais técnicos aqui. Para realmente raspar o metadata de sites, mantivemos as coisas bem simples. Usamos scripts Python, às vezes curl, e um banco de dados MySQL para armazenar os resultados. Não usamos nenhum software de raspagem sofisticado que possa mapear sites complexos, já que até agora só precisávamos raspar um ou dois tipos de páginas apenas enumerando através de ids e analisando o HTML. Se não houver páginas facilmente enumeradas, então você pode precisar de um rastreador adequado que tente encontrar todas as páginas. Antes de começar a raspar um site inteiro, tente fazer isso manualmente por um tempo. Passe por algumas dezenas de páginas você mesmo, para ter uma noção de como isso funciona. Às vezes, você já encontrará bloqueios de IP ou outros comportamentos interessantes dessa forma. O mesmo vale para a raspagem de dados: antes de se aprofundar muito nesse alvo, certifique-se de que pode realmente baixar seus dados de forma eficaz. Para contornar restrições, há algumas coisas que você pode tentar. Existem outros endereços IP ou servidores que hospedam os mesmos dados, mas não têm as mesmas restrições? Existem endpoints de API que não têm restrições, enquanto outros têm? Em que taxa de download seu IP é bloqueado e por quanto tempo? Ou você não é bloqueado, mas tem a velocidade reduzida? E se você criar uma conta de usuário, como as coisas mudam então? Você pode usar HTTP/2 para manter as conexões abertas, e isso aumenta a taxa com que você pode solicitar páginas? Existem páginas que listam vários arquivos de uma vez, e as informações listadas lá são suficientes? Coisas que você provavelmente quer salvar incluem: Normalmente fazemos isso em duas etapas. Primeiro, baixamos os arquivos HTML brutos, geralmente diretamente para o MySQL (para evitar muitos arquivos pequenos, sobre os quais falamos mais abaixo). Depois, em uma etapa separada, passamos por esses arquivos HTML e os analisamos em tabelas MySQL reais. Dessa forma, você não precisa rebaixar tudo do zero se descobrir um erro no seu código de análise, já que pode apenas reprocessar os arquivos HTML com o novo código. Também é frequentemente mais fácil paralelizar a etapa de processamento, economizando assim algum tempo (e você pode escrever o código de processamento enquanto a raspagem está em execução, em vez de ter que escrever ambas as etapas de uma vez). Finalmente, observe que para alguns alvos a raspagem de metadata é tudo o que há. Existem algumas coleções enormes de metadata por aí que não estão devidamente preservadas. Título Seleção de domínio / filosofia: Onde você quer se concentrar aproximadamente e por quê? Quais são suas paixões, habilidades e circunstâncias únicas que você pode usar a seu favor? Seleção de alvo: Qual coleção específica você irá espelhar? Raspagem de metadata: Catalogação de informações sobre os arquivos, sem realmente baixar os arquivos (geralmente muito maiores) em si. Seleção de dados: Com base na metadata, restringir quais dados são mais relevantes para arquivar agora. Pode ser tudo, mas muitas vezes há uma maneira razoável de economizar espaço e largura de banda. Raspagem de dados: Realmente obter os dados. Distribuição: Empacotar em torrents, anunciar em algum lugar, fazer com que as pessoas espalhem. 5. Raspagem de dados Agora você está pronto para realmente baixar os dados em massa. Como mencionado antes, neste ponto você já deve ter baixado manualmente um monte de arquivos, para entender melhor o comportamento e as restrições do alvo. No entanto, ainda haverá surpresas reservadas para você quando realmente começar a baixar muitos arquivos de uma vez. Nosso conselho aqui é principalmente manter a simplicidade. Comece apenas baixando um monte de arquivos. Você pode usar Python e, em seguida, expandir para múltiplas threads. Mas às vezes é ainda mais simples gerar arquivos Bash diretamente do banco de dados e, em seguida, executar vários deles em várias janelas de terminal para escalar. Um truque técnico rápido que vale a pena mencionar aqui é usar OUTFILE no MySQL, que você pode escrever em qualquer lugar se desativar "secure_file_priv" no mysqld.cnf (e certifique-se de também desativar/substituir o AppArmor se estiver no Linux). Armazenamos os dados em discos rígidos simples. Comece com o que você tem e expanda lentamente. Pode ser assustador pensar em armazenar centenas de TBs de dados. Se essa é a situação que você está enfrentando, coloque primeiro um bom subconjunto e, no seu anúncio, peça ajuda para armazenar o restante. Se você quiser adquirir mais discos rígidos, o r/DataHoarder tem alguns bons recursos para conseguir bons negócios. Tente não se preocupar muito com sistemas de arquivos sofisticados. É fácil cair na armadilha de configurar coisas como ZFS. Um detalhe técnico a ser observado, no entanto, é que muitos sistemas de arquivos não lidam bem com muitos arquivos. Descobrimos que uma solução simples é criar múltiplos diretórios, por exemplo, para diferentes intervalos de ID ou prefixos de hash. Após baixar os dados, certifique-se de verificar a integridade dos arquivos usando hashes no metadata, se disponível. 2. Seleção de alvo Acessível: não usa toneladas de camadas de proteção para impedir que você raspe seus metadata e dados. Insight especial: você tem alguma informação especial sobre este alvo, como de alguma forma tem acesso especial a esta coleção, ou descobriu como derrotar suas defesas. Isso não é necessário (nosso próximo projeto não faz nada especial), mas certamente ajuda! Grande Então, temos nossa área que estamos analisando, agora qual coleção específica devemos espelhar? Existem algumas coisas que fazem um bom alvo: Quando encontramos nossos livros didáticos de ciência em sites diferentes do Library Genesis, tentamos descobrir como eles chegaram à internet. Então encontramos o Z-Library e percebemos que, embora a maioria dos livros não apareça primeiro lá, eles acabam chegando lá. Aprendemos sobre sua relação com o Library Genesis e a estrutura de incentivos (financeiros) e a interface de usuário superior, ambas as quais tornaram-no uma coleção muito mais completa. Em seguida, fizemos algumas raspagens preliminares de metadata e dados, e percebemos que poderíamos contornar seus limites de download de IP, aproveitando o acesso especial de um de nossos membros a muitos servidores proxy. Enquanto você explora diferentes alvos, já é importante esconder seus rastros usando VPNs e endereços de e-mail descartáveis, sobre os quais falaremos mais tarde. Único: não já bem coberto por outros projetos. Quando fazemos um projeto, ele tem algumas fases: Essas não são fases completamente independentes, e muitas vezes percepções de uma fase posterior te levam de volta a uma fase anterior. Por exemplo, durante a raspagem de metadata, você pode perceber que o alvo que selecionou tem mecanismos de defesa além do seu nível de habilidade (como bloqueios de IP), então você volta e encontra um alvo diferente. - Anna e a equipe (<a %(reddit)s>Reddit</a>) Livros inteiros podem ser escritos sobre o <em>porquê</em> da preservação digital em geral, e do arquivismo pirata em particular, mas vamos dar uma breve introdução para aqueles que não estão muito familiarizados. O mundo está produzindo mais conhecimento e cultura do que nunca, mas também mais disso está sendo perdido do que nunca. A humanidade confia amplamente em corporações como editoras acadêmicas, serviços de streaming e empresas de redes sociais para cuidar desse patrimônio, e muitas vezes elas não se mostraram grandes guardiãs. Confira o documentário Digital Amnesia, ou qualquer palestra de Jason Scott. Existem algumas instituições que fazem um bom trabalho arquivando o máximo que podem, mas elas estão limitadas pela lei. Como piratas, estamos em uma posição única para arquivar coleções que elas não podem tocar, devido à aplicação de direitos autorais ou outras restrições. Também podemos espelhar coleções muitas vezes, ao redor do mundo, aumentando assim as chances de preservação adequada. Por enquanto, não entraremos em discussões sobre os prós e contras da propriedade intelectual, a moralidade de quebrar a lei, reflexões sobre censura ou a questão do acesso ao conhecimento e à cultura. Com tudo isso fora do caminho, vamos mergulhar no <em>como</em>. Vamos compartilhar como nossa equipe se tornou arquivistas piratas e as lições que aprendemos ao longo do caminho. Existem muitos desafios quando você embarca nesta jornada, e esperamos poder ajudá-lo em alguns deles. Como se tornar um arquivista pirata O primeiro desafio pode ser surpreendente. Não é um problema técnico, nem um problema legal. É um problema psicológico. Antes de começarmos, duas atualizações sobre o Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>): Recebemos algumas doações extremamente generosas. A primeira foi de $10 mil de um indivíduo anônimo que também tem apoiado "bookwarrior", o fundador original do Library Genesis. Agradecimentos especiais a bookwarrior por facilitar esta doação. A segunda foi outra doação de $10 mil de um doador anônimo, que entrou em contato após nosso último lançamento e foi inspirado a ajudar. Também recebemos várias doações menores. Muito obrigado por todo o seu generoso apoio. Temos alguns novos projetos empolgantes em andamento que isso irá apoiar, então fiquem atentos. Tivemos algumas dificuldades técnicas com o tamanho do nosso segundo lançamento, mas nossos torrents estão ativos e semeando agora. Também recebemos uma oferta generosa de um indivíduo anônimo para semear nossa coleção em seus servidores de altíssima velocidade, então estamos fazendo um upload especial para suas máquinas, após o qual todos os outros que estão baixando a coleção devem ver uma grande melhoria na velocidade. Postagens no blog Olá, sou a Anna. Eu criei o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, a maior biblioteca-sombra do mundo. Este é meu blog pessoal, no qual eu e meus colegas escrevemos sobre pirataria, preservação digital e muito mais. Conecte-se comigo no <a %(reddit)s>Reddit</a>. Observe que este site é apenas um blog. Nós apenas hospedamos nossas próprias palavras aqui. Nenhum torrent ou outros arquivos protegidos por direitos autorais são hospedados ou vinculados aqui. <strong>Biblioteca</strong> - Como a maioria das bibliotecas, focamos principalmente em materiais escritos como livros. Podemos expandir para outros tipos de mídia no futuro. <strong>Espelho</strong> - Somos estritamente um espelho de bibliotecas existentes. Focamos na preservação, não em tornar os livros facilmente pesquisáveis e baixáveis (acesso) ou em fomentar uma grande comunidade de pessoas que contribuem com novos livros (fonte). <strong>Pirata</strong> - Deliberadamente violamos a lei de direitos autorais na maioria dos países. Isso nos permite fazer algo que entidades legais não podem fazer: garantir que os livros sejam espelhados amplamente. <em>Não vinculamos os arquivos deste blog. Por favor, encontre-os você mesmo.</em> - Anna e a equipe (<a %(reddit)s>Reddit</a>) Este projeto (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>) visa contribuir para a preservação e libertação do conhecimento humano. Fazemos nossa pequena e humilde contribuição, nos passos dos grandes que vieram antes de nós. O foco deste projeto é ilustrado pelo seu nome: A primeira biblioteca que espelhamos é a Z-Library. Esta é uma biblioteca popular (e ilegal). Eles pegaram a coleção do Library Genesis e a tornaram facilmente pesquisável. Além disso, eles se tornaram muito eficazes em solicitar novas contribuições de livros, incentivando usuários contribuintes com várias vantagens. Atualmente, eles não contribuem com esses novos livros de volta para o Library Genesis. E, ao contrário do Library Genesis, eles não tornam sua coleção facilmente espelhável, o que impede a preservação ampla. Isso é importante para o modelo de negócios deles, já que cobram dinheiro pelo acesso à coleção em massa (mais de 10 livros por dia). Não fazemos julgamentos morais sobre cobrar dinheiro pelo acesso em massa a uma coleção de livros ilegais. É inegável que o Z-Library tem sido bem-sucedido em expandir o acesso ao conhecimento e em obter mais livros. Estamos aqui simplesmente para fazer nossa parte: garantir a preservação a longo prazo dessa coleção privada. Gostaríamos de convidá-lo a ajudar a preservar e liberar o conhecimento humano baixando e semeando nossos torrents. Veja a página do projeto para mais informações sobre como os dados estão organizados. Também gostaríamos muito de convidá-lo a contribuir com suas ideias sobre quais coleções espelhar a seguir e como fazê-lo. Juntos podemos alcançar muito. Esta é apenas uma pequena contribuição entre inúmeras outras. Obrigado, por tudo o que você faz. Apresentando o Espelho da Biblioteca Pirata: Preservando 7TB de livros (que não estão no Libgen) 10% of do patrimônio escrito da humanidade preservado para sempre <strong>Google.</strong> Afinal, eles fizeram essa pesquisa para o Google Books. No entanto, sua metadata não é acessível em massa e é bastante difícil de extrair. <strong>Vários sistemas de bibliotecas individuais e arquivos.</strong> Existem bibliotecas e arquivos que não foram indexados e agregados por nenhum dos mencionados acima, muitas vezes porque são subfinanciados ou, por outras razões, não querem compartilhar seus dados com a Open Library, OCLC, Google, e assim por diante. Muitos desses têm registros digitais acessíveis pela internet, e muitas vezes não são muito bem protegidos, então, se você quiser ajudar e se divertir aprendendo sobre sistemas de bibliotecas estranhos, esses são ótimos pontos de partida. <strong>ISBNdb.</strong> Este é o tema deste post do blog. O ISBNdb extrai dados de vários sites para metadata de livros, em particular dados de preços, que eles então vendem para livreiros, para que possam precificar seus livros de acordo com o restante do mercado. Como os ISBNs são bastante universais hoje em dia, eles efetivamente construíram uma “página da web para cada livro”. <strong>Open Library.</strong> Como mencionado antes, esta é toda a sua missão. Eles obtiveram enormes quantidades de dados de bibliotecas de bibliotecas cooperantes e arquivos nacionais, e continuam a fazê-lo. Eles também têm bibliotecários voluntários e uma equipe técnica que está tentando deduplicar registros e marcá-los com todos os tipos de metadata. O melhor de tudo, seu conjunto de dados é completamente aberto. Você pode simplesmente <a %(openlibrary)s>baixá-lo</a>. <strong>WorldCat.</strong> Este é um site administrado pela organização sem fins lucrativos OCLC, que vende sistemas de gerenciamento de bibliotecas. Eles agregam metadata de livros de várias bibliotecas e a disponibilizam através do site WorldCat. No entanto, eles também ganham dinheiro vendendo esses dados, então não estão disponíveis para download em massa. Eles têm alguns conjuntos de dados em massa mais limitados disponíveis para download, em cooperação com bibliotecas específicas. 1. Para alguma definição razoável de "para sempre". ;) 2. Claro, o patrimônio escrito da humanidade é muito mais do que livros, especialmente nos dias de hoje. Para o propósito deste post e nossos lançamentos recentes, estamos focando em livros, mas nossos interesses vão além. 3. Há muito mais a ser dito sobre Aaron Swartz, mas queríamos apenas mencioná-lo brevemente, já que ele desempenha um papel crucial nesta história. Com o passar do tempo, mais pessoas podem se deparar com seu nome pela primeira vez e, posteriormente, mergulhar no assunto por conta própria. <strong>Cópias físicas.</strong> Obviamente, isso não é muito útil, já que são apenas duplicatas do mesmo material. Seria interessante se pudéssemos preservar todas as anotações que as pessoas fazem nos livros, como os famosos “rabiscos nas margens” de Fermat. Mas, infelizmente, isso permanecerá como um sonho de arquivista. <strong>“Edições”.</strong> Aqui você conta cada versão única de um livro. Se algo sobre ele for diferente, como uma capa diferente ou um prefácio diferente, conta como uma edição diferente. <strong>Arquivos.</strong> Ao trabalhar com bibliotecas-sombra como Library Genesis, Sci-Hub ou Z-Library, há uma consideração adicional. Pode haver várias digitalizações da mesma edição. E as pessoas podem criar versões melhores de arquivos existentes, digitalizando o texto usando OCR ou corrigindo páginas que foram digitalizadas em ângulo. Queremos contar esses arquivos como uma única edição, o que exigiria uma boa metadata ou deduplicação usando medidas de similaridade de documentos. <strong>“Obras”.</strong> Por exemplo, “Harry Potter e a Câmara Secreta” como um conceito lógico, englobando todas as suas versões, como diferentes traduções e reimpressões. Esta é uma definição meio útil, mas pode ser difícil traçar a linha do que conta. Por exemplo, provavelmente queremos preservar diferentes traduções, embora reimpressões com apenas pequenas diferenças possam não ser tão importantes. - Anna e a equipe (<a %(reddit)s>Reddit</a>) Com o Espelho da Biblioteca Pirata (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>), nosso objetivo é pegar todos os livros do mundo e preservá-los para sempre.<sup>1</sup> Entre nossos torrents do Z-Library e os torrents originais do Library Genesis, temos 11.783.153 arquivos. Mas quantos são realmente? Se deduplicássemos adequadamente esses arquivos, que porcentagem de todos os livros do mundo teríamos preservado? Gostaríamos muito de ter algo assim: Vamos começar com alguns números aproximados: Em ambos Z-Library/Libgen e Open Library há muitos mais livros do que ISBNs únicos. Isso significa que muitos desses livros não têm ISBNs, ou a metadata do ISBN está simplesmente faltando? Provavelmente podemos responder a essa pergunta com uma combinação de correspondência automatizada baseada em outros atributos (título, autor, editor, etc.), trazendo mais fontes de dados e extraindo ISBNs das próprias digitalizações dos livros (no caso de Z-Library/Libgen). Quantos desses ISBNs são únicos? Isso é melhor ilustrado com um diagrama de Venn: Para ser mais preciso: Ficamos surpresos com o quão pouco sobreposição existe! O ISBNdb tem uma enorme quantidade de ISBNs que não aparecem nem no Z-Library nem na Open Library, e o mesmo vale (em menor grau, mas ainda substancial) para os outros dois. Isso levanta muitas novas questões. Quanto a correspondência automatizada ajudaria a marcar os livros que não foram marcados com ISBNs? Haveria muitas correspondências e, portanto, aumento da sobreposição? Além disso, o que aconteceria se trouxermos um 4º ou 5º conjunto de dados? Quanta sobreposição veríamos então? Isso nos dá um ponto de partida. Agora podemos olhar para todos os ISBNs que não estavam no conjunto de dados do Z-Library e que não correspondem aos campos de título/autor também. Isso pode nos dar uma ideia de como preservar todos os livros do mundo: primeiro extraindo a internet por digitalizações, depois saindo na vida real para digitalizar livros. Este último poderia até ser financiado coletivamente ou impulsionado por “recompensas” de pessoas que gostariam de ver livros específicos digitalizados. Tudo isso é uma história para outro momento. Se você quiser ajudar com qualquer uma dessas atividades — análise adicional; extração de mais metadata; encontrar mais livros; realizar OCR em livros; fazer isso para outros domínios (por exemplo, artigos, audiolivros, filmes, programas de TV, revistas) ou até mesmo disponibilizar alguns desses dados para coisas como treinamento de ML / modelos de linguagem de grande porte — por favor, entre em contato comigo (<a %(reddit)s>Reddit</a>). Se você está especificamente interessado na análise de dados, estamos trabalhando para disponibilizar nossos datasets e scripts em um formato mais fácil de usar. Seria ótimo se você pudesse simplesmente fazer um fork de um notebook e começar a explorar isso. Finalmente, se você quiser apoiar este trabalho, por favor, considere fazer uma doação. Esta é uma operação totalmente gerida por voluntários, e sua contribuição faz uma enorme diferença. Toda ajuda conta. Por enquanto, aceitamos doações em criptomoedas; veja a página de Doações no Acervo da Anna. Para uma porcentagem, precisamos de um denominador: o número total de livros já publicados.<sup>2</sup> Antes do fim do Google Books, um engenheiro do projeto, Leonid Taycher, <a %(booksearch_blogspot)s>tentou estimar</a> esse número. Ele chegou — em tom de brincadeira — a 129.864.880 (“pelo menos até domingo”). Ele estimou esse número construindo um banco de dados unificado de todos os livros do mundo. Para isso, ele reuniu diferentes datasets e os mesclou de várias maneiras. Como uma rápida observação, há outra pessoa que tentou catalogar todos os livros do mundo: Aaron Swartz, o falecido ativista digital e cofundador do Reddit.<sup>3</sup> Ele <a %(youtube)s>iniciou a Open Library</a> com o objetivo de “uma página da web para cada livro já publicado”, combinando dados de várias fontes diferentes. Ele acabou pagando o preço máximo por seu trabalho de preservação digital quando foi processado por baixar em massa artigos acadêmicos, levando ao seu suicídio. Nem é preciso dizer que esta é uma das razões pelas quais nosso grupo é pseudônimo e por que estamos sendo muito cuidadosos. A Open Library ainda está sendo heroicamente administrada por pessoas do Internet Archive, continuando o legado de Aaron. Voltaremos a isso mais tarde neste post. No post do blog do Google, Taycher descreve alguns dos desafios ao estimar esse número. Primeiro, o que constitui um livro? Existem algumas definições possíveis: “Edições” parecem ser a definição mais prática do que são “livros”. Convenientemente, essa definição também é usada para atribuir números ISBN únicos. Um ISBN, ou Número Padrão Internacional de Livro, é comumente usado para comércio internacional, pois está integrado ao sistema internacional de código de barras (“Número de Artigo Internacional”). Se você quiser vender um livro em lojas, ele precisa de um código de barras, então você obtém um ISBN. O post do blog de Taycher menciona que, embora os ISBNs sejam úteis, eles não são universais, já que foram realmente adotados apenas em meados dos anos setenta, e não em todo o mundo. Ainda assim, o ISBN é provavelmente o identificador mais amplamente usado para edições de livros, então é nosso melhor ponto de partida. Se pudermos encontrar todos os ISBNs do mundo, teremos uma lista útil de quais livros ainda precisam ser preservados. Então, onde conseguimos os dados? Existem vários esforços existentes que estão tentando compilar uma lista de todos os livros do mundo: Neste post, estamos felizes em anunciar um pequeno lançamento (comparado aos nossos lançamentos anteriores do Z-Library). Extraímos a maior parte do ISBNdb e disponibilizamos os dados para torrent no site do Pirate Library Mirror (EDIT: movido para <a %(wikipedia_annas_archive)s>Acervo da Anna</a>; não vamos linkar diretamente aqui, apenas procure por ele). São cerca de 30,9 milhões de registros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4,4GB compactados). No site deles, afirmam que na verdade têm 32,6 milhões de registros, então talvez tenhamos perdido alguns, ou <em>eles</em> podem estar fazendo algo errado. Em qualquer caso, por enquanto não compartilharemos exatamente como fizemos isso — deixaremos isso como um exercício para o leitor. ;-) O que vamos compartilhar é uma análise preliminar, para tentar chegar mais perto de estimar o número de livros no mundo. Analisamos três datasets: este novo dataset do ISBNdb, nossa versão original de metadata que extraímos da biblioteca-sombra Z-Library (que inclui o Library Genesis) e o dump de dados da Open Library. Dump do ISBNdb, ou Quantos Livros Estão Preservados para Sempre? Se fôssemos deduplicar adequadamente os arquivos das bibliotecas-sombra, que porcentagem de todos os livros do mundo teríamos preservado? Atualizações sobre o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, a maior biblioteca verdadeiramente aberta da história humana. <em>Reformulação do WorldCat</em> Dados <strong>Formato?</strong> <a %(blog)s>Contêineres do Acervo da Anna (AAC)</a>, que é essencialmente <a %(jsonlines)s>JSON Lines</a> comprimido com <a %(zstd)s>Zstandard</a>, além de algumas semânticas padronizadas. Esses contêineres envolvem vários tipos de registros, com base nas diferentes raspagens que implantamos. Há um ano, nós <a %(blog)s>começamos</a> a responder a esta pergunta: <strong>Qual porcentagem de livros foi permanentemente preservada por bibliotecas-sombra?</strong> Vamos dar uma olhada em algumas informações básicas sobre os dados: Uma vez que um livro entra em uma biblioteca-sombra de dados abertos como a <a %(wikipedia_library_genesis)s>Library Genesis</a>, e agora o <a %(wikipedia_annas_archive)s>Acervo da Anna</a>, ele é espelhado em todo o mundo (através de torrents), preservando-o praticamente para sempre. Para responder à pergunta de qual porcentagem de livros foi preservada, precisamos saber o denominador: quantos livros existem no total? E, idealmente, não temos apenas um número, mas metadata real. Então, podemos não apenas compará-los com bibliotecas-sombra, mas também <strong>criar uma lista de tarefas dos livros restantes para preservar!</strong> Poderíamos até começar a sonhar com um esforço colaborativo para percorrer essa lista de tarefas. Raspamos o <a %(wikipedia_isbndb_com)s>ISBNdb</a> e baixamos o <a %(openlibrary)s>conjunto de dados da Open Library</a>, mas os resultados foram insatisfatórios. O principal problema era que não havia muita sobreposição de ISBNs. Veja este diagrama de Venn do <a %(blog)s>nosso post no blog</a>: Ficamos muito surpresos com o quão pouco havia de sobreposição entre ISBNdb e Open Library, ambos os quais incluem dados de várias fontes, como raspagens da web e registros de bibliotecas. Se ambos fazem um bom trabalho em encontrar a maioria dos ISBNs por aí, seus círculos certamente teriam uma sobreposição substancial, ou um seria um subconjunto do outro. Isso nos fez questionar, quantos livros estão <em>completamente fora desses círculos</em>? Precisamos de um banco de dados maior. Foi então que miramos no maior banco de dados de livros do mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Este é um banco de dados proprietário da organização sem fins lucrativos <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliotecas de todo o mundo, em troca de dar a essas bibliotecas acesso ao conjunto completo de dados e de aparecerem nos resultados de busca dos usuários finais. Embora a OCLC seja uma organização sem fins lucrativos, seu modelo de negócios exige a proteção de seu banco de dados. Bem, lamentamos dizer, amigos da OCLC, estamos liberando tudo. :-) No último ano, raspamos meticulosamente todos os registros do WorldCat. No início, tivemos um golpe de sorte. O WorldCat estava lançando a reformulação completa de seu site (em agosto de 2022). Isso incluiu uma revisão substancial de seus sistemas de backend, introduzindo muitas falhas de segurança. Aproveitamos imediatamente a oportunidade e conseguimos raspar centenas de milhões (!) de registros em poucos dias. Depois disso, as falhas de segurança foram corrigidas lentamente uma a uma, até que a última que encontramos foi corrigida há cerca de um mês. Naquela época, já tínhamos praticamente todos os registros e estávamos apenas buscando registros de qualidade ligeiramente superior. Então sentimos que é hora de lançar! Raspagem de 1,3B do WorldCat <em><strong>Resumo:</strong> O Acervo da Anna raspou todo o WorldCat (a maior coleção de metadata de bibliotecas do mundo) para fazer uma lista de tarefas de livros que precisam ser preservados.</em> WorldCat Aviso: este post no blog foi descontinuado. Decidimos que o IPFS ainda não está pronto para o horário nobre. Ainda vamos linkar para arquivos no IPFS a partir do Acervo da Anna quando possível, mas não vamos mais hospedá-los nós mesmos, nem recomendamos que outros espelhem usando IPFS. Por favor, veja nossa página de Torrents se você quiser ajudar a preservar nossa coleção. Ajude a semear o Z-Library no IPFS Download via servidor de parceiros SciDB Empréstimo externo Empréstimo externo (impressão desabilitada) Download externo Explore os metadados Contido em torrents Voltar  (+%(num)s bônus) não pago pago cancelado vencido esperando Anna confirmar inválido O texto continua abaixo, em inglês. Ir Redefinir Avançar Último Se o seu endereço de e-mail não funcionar nos fóruns Libgen, recomendamos usar <a %(a_mail)s>Proton Mail</a> (gratuito). Você também pode <a %(a_manual)s>solicitar manualmente</a> a ativação de sua conta. (pode exigir <a %(a_browser)s>verificação do navegador</a> — downloads ilimitados!) Servidor Parceiro Rápido #%(number)s (recomendado) (um pouco mais rápido, mas com lista de espera) (não é necessária a verificação do navegador) (sem verificação de navegador ou listas de espera) (sem lista de espera, mas pode ser muito lento) Servidor Parceiro Lento #%(number)s Audiolivro Quadrinhos Livro (ficção) Livro (não ficção) Livro (desconhecido) Artigo de periódico Revista Partitura musical Outros Documento de normas Nem todas as páginas puderam ser convertidas para PDF Marcado como corrompido no Libgen.li Não visível no Libgen.li Não visível na área de Ficção do Libgen.rs Não visível na área de Não-Ficção do Libgen.rs A execução do exiftool falhou neste arquivo Marcado como “arquivo ruim” no Z-Library Indisponível no Z-Library Marcado como “spam” no Z-Library O arquivo não pôde ser aberto (p. ex. arquivo corrompido, DRM) Reivindicação de direitos autorais Problemas ao baixar (p. ex. não é possível conectar, mensagem de erro, muito lento) Metadados incorretos (p. ex. título, descrição, imagem da capa) Outros Qualidade baixa (p. ex. problemas de formatação, má qualidade de digitalização, páginas faltando) Spam / arquivo deve ser removido (p. ex. publicidade, conteúdo abusivo) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s %(amount_usd)s total Bibliófilo Brilhante Arquivista Afortunado Acumulador de Dados Deslumbrante Arquivista Admirável Downloads bônus Cerlalc Metadados tchecos DuXiu 读秀 Índice de eBooks EBSCOhost Google Livros Goodreads HathiTrust IA Empréstimo Digital Controlado pela IA ISBNdb ISBN GRP Libgen.li Excluindo “scimag” Libgen.rs Não-ficção e Ficção Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Biblioteca Estatal Russa Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Envia para AA Z-Library Z-Library Chinês Pesquisar título, autor, idioma, tipo de arquivo, ISBN, MD5, … Pesquisar Autor Descrição e comentários de metadados Edição Nome do arquivo original Editora (campo específico de pesquisa) Título Ano de publicação Detalhes técnicos Essa moeda tem um mínimo mais alto que o habitual. Por favor selecione uma duração diferente ou uma moeda diferente. A solicitação não pôde ser concluída. Por favor, tente novamente em alguns minutos, e se continuar acontecendo, entre em contato conosco em %(email)s com uma captura de tela. Um erro desconhecido ocorreu. Por favor nos contate em %(email)s com uma captura de tela. Erro no processamento do pagamento. Aguarde um momento e tente novamente. Se o problema persistir por mais de 24 horas, entre em contato conosco pelo e-mail %(email)s com uma captura de tela. Estamos fazendo uma campanha para <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">fazer o backup</a> da maior shadow library de quadrinhos do mundo Obrigado pelo seu apoio! <a href="/donate">Doar.</a> Se você não puder doar, considere nos apoiar falando para seus amigos e nos seguindo no <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, ou <a href="https://t.me/annasarchiveorg">Telegram</a>. Não nos envie e-mails para <a %(a_request)s>solicitar livros</a><br>ou pequenos (<10k) <a %(a_upload)s>uploads</a>. Acervo da Anna DMCA / Reivindicação de Direitos Autorais Mantenha contato Reddit Alternativas SLUM (%(unaffiliated)s) não afiliado Arquivo da Anna precisa da sua ajuda! Se você doar agora, receberá <strong>o dobro</strong> de downloads rápidos. Muitos tentam nos derrubar, mas nós revidamos. Se você doar este mês, receberá o <strong>dobro</strong> do número de downloads rápidos. Válido até o final deste mês. Salvando o conhecimento humano: um ótimo presente de feriado! As assinaturas serão estendidas de acordo. Servidores parceiros estão indisponíveis devido ao fechamento das hospedagens. Eles devem voltar em breve. Para aumentar a resiliência do Arquivo da Anna, estamos procurando voluntários para operar espelhos. Temos um novo método para receber doações: %(method_name)s. Por favor, considere %(donate_link_open_tag)sdoar</a> — não é barato manter este site funcionando, e sua doação faz muita diferença! Muito obrigada. Indique um amigo, e vocês dois recebem %(percentage)s%% downloads rápidos bônus! Surpreenda um ente querido, dê-lhe uma conta com adesão. O presente de Dia dos Namorados perfeito! Saiba mais… Conta Atividade Avançado Blog da Anna ↗ Programas da Anna ↗ beta Explorador de códigos Bancos de dados Doar Arquivos baixados Perguntas frequentes Início Melhorar metadados Dados para LLM Entrar / Cadastrar Minhas doações Perfil público Pesquisar Segurança Torrents Traduza ↗ Voluntariado e recompensas Downloads recentes: 📚&nbsp; A maior biblioteca de código e dados abertos do mundo. ⭐️&nbsp; Espelhamos Sci-Hub, Library Genesis, Z-Library e muito mais. 📈&nbsp; %(book_any)s livros, %(journal_article)s artigos, %(book_comic)s quadrinhos, %(magazine)s revistas — preservados para sempre.  e  e mais DuXiu Biblioteca de Empréstimos do Internet Archive LibGen 📚&nbsp;A maior biblioteca verdadeiramente aberta da história da humanidade. 📈&nbsp;%(book_count)s&nbsp;livros, %(paper_count)s&nbsp;artigos — preservados para sempre. ⭐️&nbsp;Nós espelhamos %(libraries)s. Recolhemos e disponibilizamos conteúdo do %(scraped)s. Nosso código e dados são totalmente abertos. OpenLib Sci-Hub ,  📚 A maior biblioteca de código e dados abertos do mundo.<br>⭐️ Espelhamos Sci-Hub, Libgen, Zlib, e mais. Z-Lib Acervo da Anna Solicitação Invalida. Acesse %(websites)s. A maior biblioteca open-source e open-data do mundo. Inclui Sci-Hub, Library Genesis, Z-Library, e mais. Pesquise no arquivo da Anna Acervo da Anna Por favor, atualize a página para tentar novamente. <a %(a_contact)s>Entre em contato conosco</a> se o problema persistir por várias horas. 🔥 Problema ao carregar esta página <li>1. Siga-nos no <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ou <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Divulgue Arquivo da Anna no Twitter, Reddit, TikTok, Instagram, em sua cafeteria local, biblioteca ou onde quer que vá! Não acreditamos em restrições de acesso - se formos removidos, simplesmente surgiremos em outro lugar, já que todo nosso código e dados são totalmente abertos.</li><li>3. Se puder, considere <a href="/donate">fazer uma doação</a>.</li><li>4. Ajude a <a href="https://translate.annas-software.org/">traduzir</a> nosso site para diferentes idiomas.</li><li>5. Se você é um engenheiro de software, considere contribuir para nosso <a href="https://annas-software.org/">código aberto</a> ou compartilhar nossos <a href="/datasets">torrents</a>.</li> 10. Crie ou ajude a manter a página da Wikipedia do Acervo da Anna em seu idioma. 11. Colocaremos anúncios pequenos e de bom gosto. Se gostaria de anunciar no Acervo da Anna, por favor nos avise. 6. Se você é um pesquisador de segurança, podemos utilizar suas habilidades tanto para ataque quanto para defesa. Confira nossa página de <a %(a_security)s>Segurança</a>. 7. Estamos procurando por especialistas em pagamentos para comerciantes anônimos. Você pode nos ajudar a adicionar formas mais convenientes de doação? PayPal, WeChat, cartões-presente (gift cards). Se você conhece alguém, por favor, entre em contato conosco. 8. Estamos sempre em busca de mais capacidade de servidor. 9. Você pode ajudar relatando problemas com arquivos, deixando comentários e criando listas diretamente neste site. Você também pode ajudar fazendo <a %(a_upload)s>upload de mais livros</a>, corrigindo problemas de arquivos ou de formatação nos livros existentes. Para informações mais detalhadas sobre como ser voluntário, veja nossa página de <a %(a_volunteering)s>Voluntariado & Recompensas</a>. Acreditamos na livre circulação da informação e na preservação do conhecimento e da cultura. Com esse motor de busca construímos em cima de projetos de enorme importância. Respeitamos profundamente o trabalho duro das pessoas que criaram diversas bibliotecas-sombra e acreditamos que este motor de busca irá ampliar seu alcance. Para saber como está nosso trabalho, siga a Anna no <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> ou <a href="https://t.me/annasarchiveorg">Telegram</a>. Para perguntas e opiniões, por favor envie um email para a Anna em %(email)s. ID da conta: %(account_id)s Sair ❌ Algo deu errado. Por favor, recarregue a página e tente novamente. ✅ Agora você está desconectado. Recarregue a página para fazer login novamente. Downloads rápidos usados (últimas 24 horas): <strong>%(used)s / %(total)s</strong> Assinatura: <strong>%(tier_name)s</strong> até %(until_date)s <a %(a_extend)s>(estender)</a> Você pode combinar várias assinaturas (downloads rápidos por 24 horas serão somados). Assinatura: <strong>Nenhuma</strong> <a %(a_become)s>(Torne-se um Membro)</a> Entre em contato com Anna em %(email)s se estiver interessado em atualizar sua assinatura para um nível superior. Perfil público: %(profile_link)s Chave secreta (não compartilhe!): %(secret_key)s mostrar Participe aqui! Atualize para um <a %(a_tier)s>nível superior</a> para participar do nosso grupo. Grupo exclusivo do Telegram: %(link)s Conta quais downloads? Entrar Não perca sua chave! Chave secreta inválida. Verifique sua chave e tente novamente, ou então registre uma nova conta abaixo. Chave secreta Digite sua chave secreta para fazer login: Conta antiga baseada em e-mail? Insira seu <a %(a_open)s>email aqui</a>. Registrar uma nova conta Ainda não tem uma conta? Cadastro bem sucedido! Sua chave secreta é: <span %(span_key)s>%(key)s</span> Guarde essa chave com cuidado. Se você a perder, você perderá o acesso à sua conta. <li %(li_item)s><strong>Favoritar.</strong> Você pode favoritar esta página para recuperar sua chave.</li><li %(li_item)s><strong>Baixar.</strong> Clique<a %(a_download)s>neste link</a> para baixar sua chave.</li><li %(li_item)s><strong>Gerenciador de senhas.</strong> Utilizar um gerenciador de senhas para salvar a sua chave quando coloca-la abaixo</li> Entrar / Registrar Verificação do navegador Aviso: o código contém caracteres Unicode incorretos e pode se comportar de maneira inadequada em várias situações. O binário bruto pode ser decodificado a partir da representação base64 no URL. Descrição Etiqueta Prefixo URL para um código específico Website Códigos começando com “%(prefix_label)s” Por favor, não faça "scraping" destas páginas. Ao invez disso, recomendamos <a %(a_import)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados da ElasticSearch e MariaDB, e executar nosso <a %(a_software)s>código aberto</a>. Os dados brutos podem ser explorados manualmente através de arquivos JSON como <a %(a_json_file)s>este aqui</a>. Menos de %(count)s registros URL genérico Explorador de Códigos Índice de Explore os códigos com os quais os registros são etiquetados, por prefixo. A coluna “registros” mostra o número de registros etiquetados com códigos com o prefixo dado, conforme visto no mecanismo de busca (incluindo registros apenas de metadados). A coluna “códigos” mostra quantos códigos reais têm um determinado prefixo. Prefixo de código conhecido “%(key)s” Mais… Prefixo %(count)s registro correspondente a “%(prefix_label)s” %(count)s registros correspondentes a “%(prefix_label)s” códigos registros “%%s” será substituído pelo valor do código Buscar no Acervo da Anna Códigos URL para código específico: “%(url)s” Esta página pode demorar um pouco para ser gerada, por isso requer um captcha do Cloudflare. <a %(a_donate)s>Membros</a> podem pular o captcha. Abuso denunciado: Melhor versão Você deseja denunciar este usuário por comportamento abusivo ou inadequado? Problema no arquivo: %(file_issue)s comentário oculto Responder Denunciar abuso Você denunciou este usuário por abuso. As reivindicações de direitos autorais deste e-mail serão ignoradas; use o formulário. Mostrar e-mail Agradecemos muito seus comentários e perguntas! No entanto, devido à quantidade de spam e e-mails sem sentido que recebemos, marque as caixas para confirmar que compreende estas condições ao entrar em contato conosco. Quaisquer outras formas de nos contatar sobre reivindicações de direitos autorais serão automaticamente excluídas. Para reivindicações de DMCA/direitos autorais, use <a %(a_copyright)s>este formulário</a>. Email de contato URLs no Acervo da Anna (obrigatório). Um por linha. Inclua apenas links que correspondam exatamente a mesma edição de um livro. Se você quiser fazer uma reivindicação para vários livros ou várias edições, por favor, envie este formulário várias vezes. Reivindicações que agrupem vários livros ou edições serão rejeitadas. Endereço (obrigatório) Descrição clara do material de origem (obrigatório) E-mail (obrigatório) URLs para o material de origem, uma por linha (obrigatório). Inclua o máximo possível, para nos ajudar a verificar sua reivindicação (por exemplo, Amazon, WorldCat, Google Books, DOI). ISBNs do material de origem (se aplicável). Um por linha. Inclua apenas aqueles que correspondem exatamente à edição para a qual você está relatando uma reivindicação de direitos autorais. Seu nome (obrigatório) ❌ Algo deu errado. Recarregue a página e tente novamente. ✅ Obrigado por enviar sua reivindicação de direitos autorais. Vamos revisá-la o mais rápido possível. Recarregue a página para enviar outra. <a %(a_openlib)s>Open Library</a> URLs do material de origem, uma por linha. Reserve um momento para pesquisar o material de origem na Open Library. Isso nos ajudará a verificar sua reivindicação. Número de telefone (obrigatório) Declaração e assinatura (obrigatório) Enviar reivindicação Se você tiver uma reivindicação de DMCA ou outros direitos autorais (Copyright), preencha este formulário o mais precisamente possível. Se encontrar algum problema, entre em contato conosco no nosso endereço dedicado ao DMCA: %(email)s. Note que reivindicações enviadas por e-mail para este endereço não serão processadas; ele é apenas para perguntas. Use o formulário abaixo para enviar suas reivindicações. Formulário de reivindicação de Copyright (direitos autorais)/DMCA Exemplo de registro no Arquivo da Anna Torrents feitos pelo Arquivo da Anna Formato "Containers do Arquivo da Anna" Scripts para importar metadados Se você estiver interessado em espelhar este conjunto de dados para <a %(a_archival)s>arquivamento</a> ou para fins de <a %(a_llm)s>treinamento de LLM</a>, por favor, entre em contato conosco. Última atualização: %(date)s Site principal %(source)s Documentação de metadados (a maioria dos campos) Arquivos espelhados pelo Arquivo da Anna: %(count)s (%(percent)s%%) Recursos Total de arquivos: %(count)s Tamanho total dos arquivos: %(size)s Nossa postagem no blog sobre esses dados <a %(duxiu_link)s>Duxiu</a> é um banco de dados enorme de livros digitalizados, criado pelo <a %(superstar_link)s>SuperStar Digital Library Group</a>. A maioria são livros acadêmicos, digitalizados para torná-los disponíveis digitalmente para universidades e bibliotecas. Para nosso público de língua inglesa, <a %(princeton_link)s>Princeton</a> e a <a %(uw_link)s>University of Washington</a> têm bons resumos (overviews). Há também um excelente artigo que fornece mais contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Os livros do Duxiu têm sido pirateados na internet chinesa há muito tempo. Normalmente, eles são vendidos por menos de um dólar por revendedores. Eles são tipicamente distribuídos usando o equivalente chinês do Google Drive, que muitas vezes foi hackeado para permitir mais espaço de armazenamento. Alguns detalhes técnicos podem ser encontrados <a %(link1)s>aqui</a> e <a %(link2)s>aqui</a>. Embora os livros tenham sido distribuídos semi-publicamente, é bastante difícil obtê-los em massa. Tivemos isso no topo da nossa lista de tarefas e alocamos vários meses de trabalho em tempo integral para isso. No entanto, no final de 2023, um voluntário incrível, maravilhoso e talentoso nos procurou, dizendo que já havia feito todo esse trabalho — a um grande custo. Ele compartilhou a coleção completa conosco, sem esperar nada em troca, exceto a garantia de preservação a longo prazo. Verdadeiramente incrível. Mais informações de nossos voluntários (notas brutas): Adaptado de nossa <a %(a_href)s>postagem no blog</a>. DuXiu 读秀 %(count)s arquivo %(count)s arquivos Este conjunto de dados está intimamente relacionado ao <a %(a_datasets_openlib)s>conjunto de dados da Open Library</a>. Ele contém uma raspagem / scraping de todos os metadados e uma grande parte dos arquivos da Biblioteca de Empréstimo Digital Controlado da IA. As atualizações são lançadas no <a %(a_aac)s>formato "Containers do Arquivo da Anna"</a>. Esses registros são referenciados diretamente do conjunto de dados da Open Library, mas também contêm registros que não estão na Open Library. Também temos vários arquivos de dados raspados por membros da comunidade ao longo dos anos. A coleção consiste em duas partes. Você precisa de ambas as partes para obter todos os dados (exceto torrents de substituição, que estão riscados na página de torrents). Biblioteca de Empréstimo Digital nosso primeiro lançamento, antes de padronizarmos no formato <a %(a_aac)s>Containers do Arquivo da Anna (AAC)</a>. Contém metadados (em json e xml), pdfs (dos sistemas de empréstimo digital acsm e lcpdf) e miniaturas de capas. lançamentos incrementais, usando AAC. Contém apenas metadados com carimbos de data após 01-01-2023, já que o restante já está coberto por “ia”. Também todos os arquivos pdf, desta vez dos sistemas de empréstimo acsm e “bookreader” (leitor web do IA). Apesar do nome não ser exatamente correto, ainda populamos arquivos do bookreader na coleção ia2_acsmpdf_files, já que são mutuamente exclusivos. IA Controlled Digital Lending 98%%+ dos arquivos são pesquisáveis. Nossa missão é arquivar todos os livros do mundo (bem como artigos, revistas, etc.) e torná-los amplamente acessíveis. Acreditamos que todos os livros devem ser espelhados amplamente, para garantir redundância e resiliência. É por isso que estamos reunindo arquivos de várias fontes. Algumas fontes são completamente abertas e podem ser espelhadas em massa (como o Sci-Hub). Outras são fechadas e protetoras, então tentamos raspá-las (scraping) para “libertar” seus livros. Outras estão em algum lugar no meio. Todos os nossos dados podem ser <a %(a_torrents)s>baixados via torrent</a>, e todos os nossos metadados podem ser <a %(a_anna_software)s>gerados</a> ou <a %(a_elasticsearch)s>baixados</a> como bancos de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente através de arquivos JSON como <a %(a_dbrecord)s>este</a>. Metadados Site do ISBN Última atualização: %(isbn_country_date)s (%(link)s) Recursos A Agência Internacional do ISBN regularmente divulga as faixas que foram alocadas para as agências nacionais de ISBN. A partir disso, podemos determinar a qual país, região ou grupo linguístico pertence esse ISBN. Atualmente, usamos esses dados indiretamente, através da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>. Informações sobre o país no ISBN Este é um dump de muitas chamadas para isbndb.com durante setembro de 2022. Tentamos cobrir todas as faixas de ISBN. São cerca de 30,9 milhões de registros. No site deles, afirmam que possuem 32,6 milhões de registros, então podemos ter perdido alguns de alguma forma, ou <em>eles</em> podem estar fazendo algo errado. As respostas JSON são praticamente brutas do servidor deles. Um problema de qualidade de dados que notamos é que, para números ISBN-13 que começam com um prefixo diferente de “978-”, eles ainda incluem um campo “isbn” que simplesmente é o número ISBN-13 com os três primeiros números cortados (e o dígito de verificação recalculado). Isso é obviamente errado, mas é assim que eles parecem fazer, então não alteramos. Outro problema potencial que você pode encontrar é o fato de que o campo “isbn13” tem duplicatas, então você não pode usá-lo como chave primária em um banco de dados. Os campos combinados “isbn13”+“isbn” parecem ser únicos. Lançamento 1 (31/01/2022) Torrents de ficção estão atrasados (embora IDs ~4-6M não tenham sido torrenteados, pois se sobrepõem aos nossos torrents do Zlib). Nosso post sobre o lançamento dos quadrinhos Torrents de quadrinhos no Arquivo da Anna Para a história dos diferentes forks do Library Genesis, veja a página do <a %(a_libgen_rs)s>Libgen.rs</a>. O Libgen.li contém a maior parte do mesmo conteúdo e metadados do Libgen.rs, mas possui algumas coleções adicionais, como quadrinhos, revistas e documentos comuns. Ele também integrou o <a %(a_scihub)s>Sci-Hub</a> em seus metadados e motor de busca, que é o que usamos para nosso banco de dados. Os metadados desta biblioteca estão disponíveis gratuitamente <a %(a_libgen_li)s>em libgen.li</a>. No entanto, este servidor é lento e não suporta a retomada de conexões interrompidas. Os mesmos arquivos também estão disponíveis em <a %(a_ftp)s>um servidor FTP</a>, que funciona melhor. A não-ficção também parece ter divergido, mas sem novos torrents. Parece que isso tem acontecido desde o início de 2022, embora não tenhamos verificado. De acordo com o administrador do Libgen.li, a coleção “fiction_rus” (ficção russa) deve ser coberta por torrents lançados regularmente de <a %(a_booktracker)s>booktracker.org</a>, especialmente os torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que espelhamos <a %(a_torrents)s>aqui</a>, embora ainda não tenhamos estabelecido quais torrents correspondem a quais arquivos). A coleção de ficção tem seus próprios torrents (divergente do <a %(a_href)s>Libgen.rs</a>) começando em %(start)s. Certas faixas sem torrents (como as faixas de ficção f_3463000 a f_4260000) são provavelmente arquivos da Z-Library (ou outros arquivos duplicados), embora estamos pensando em fazer uma desduplicação e criar torrents exclusivos para os arquivos lgli nessas faixas. Estatísticas para todas as coleções podem ser encontradas <a %(a_href)s>no site do libgen</a>. Torrents estão disponíveis para a maioria do conteúdo adicional, especialmente, torrents para quadrinhos, revistas e documentos comuns, foram lançados em colaboração com o Arquivo da Anna. Observe que os arquivos torrent que se referem a “libgen.is” são explicitamente espelhados de <a %(a_libgen)s>Libgen.rs</a> (“.is” é um domínio diferente usado pelo Libgen.rs). Um recurso útil para usar os metadados é <a %(a_href)s>esta página</a>. %(icon)s A coleção “fiction_rus” deles (ficção russa) não possui torrents dedicados, mas é coberta por torrents de outros, e mantemos um <a %(fiction_rus)s>espelho</a>. Torrents de ficção russa no Arquivo da Anna Torrents de ficção no Arquivo da Anna Fórum de discussão Metadados Metadados via FTP Torrents de revistas no Arquivo da Anna Informações sobre campos de metadados Espelho de outros torrents (e torrents únicos de ficção e quadrinhos) Torrents de documentos comuns no Arquivo da Anna Libgen.li Torrents feitos pelo Arquivo da Anna (capas de livros) A Library Genesis é conhecida por já disponibilizar generosamente seus dados em massa através de torrents. Nossa coleção do Libgen consiste em dados auxiliares que eles não liberam diretamente, disponibilizados em parceria com eles. Muito obrigado a todos envolvidos com a Library Genesis por trabalharem conosco! Nosso blog sobre o lançamento das capas de livros Esta página é sobre a versão “.rs”. É conhecida por publicar consistentemente tanto seus metadados quanto o conteúdo completo de seu catálogo de livros. Sua coleção de livros é dividida entre uma parte de ficção e outra de não-ficção. Um recurso útil para usar os metadados é <a %(a_metadata)s>esta página</a> (bloqueia algumas faixas de IP, pode ser necessário usar VPN). A partir de março de 2024, novos torrents estão sendo postados neste <a %(a_href)s>tópico do fórum</a> (bloqueia algumas faixas de IP, pode ser necessário o uso de VPN). Torrents de Ficção no Arquivo da Anna Torrents de Ficção do Libgen.rs Fórum de Discussão do Libgen.rs Metadados do Libgen.rs Informações sobre campos de metadados do Libgen.rs Torrents de Não-Ficção do Libgen.rs Torrents de Não-Ficção no Arquivo da Anna %(example)s para um livro de ficção. Este <a %(blog_post)s>primeiro lançamento</a> é bem pequeno: cerca de 300GB de capas de livros do fork Libgen.rs, tanto de ficção quanto de não-ficção. Elas estão organizadas da mesma forma que aparecem no libgen.rs, por exemplo: %(example)s para um livro de não-ficção. Assim como na coleção da Z-Library, colocamos todos em um grande arquivo .tar, que pode ser montado usando <a %(a_ratarmount)s>ratarmount</a> se você quiser acessar os arquivos diretamente. Lançamento 1 (%(date)s) A história rápida dos diferentes forks do Library Genesis (ou “Libgen”) é que, com o tempo, as diferentes pessoas envolvidas com o Library Genesis tiveram desentendimentos e seguiram caminhos separados. De acordo com este <a %(a_mhut)s>post no fórum</a>, o Libgen.li foi originalmente hospedado em “http://free-books.dontexist.com”. A versão “.fun” foi criada pelo fundador original. Está sendo reformulada em favor de uma nova versão, mais distribuída. A <a %(a_li)s>versão “.li”</a> tem uma coleção enorme de quadrinhos, bem como outros conteúdos, que ainda não estão disponíveis para download em massa através de torrents. Também tem uma coleção separada de torrents de livros de ficção e contém os metadados do <a %(a_scihub)s>Sci-Hub</a> em seu banco de dados. A versão “.rs” tem dados muito semelhantes e lança sua coleção em torrents em massa de forma mais consistente. É aproximadamente dividida em uma seção de “ficção” e outra de “não-ficção”. Originalmente em “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> de certa forma também é um fork do Library Genesis, embora tenham usado um nome diferente para seu projeto. Libgen.rs Também enriquecemos nossa coleção com fontes exclusivamente de metadados, que podemos então associar a arquivos, por exemplo, usando números ISBN ou outros campos. Abaixo está uma visão geral dessas fontes. Novamente, algumas dessas fontes são completamente abertas, enquanto outras precisamos extrair. Note que na busca de metadados, mostramos os registros originais. Não fazemos nenhuma mescla de registros. Fontes exclusivamente de metadados A Open Library é um projeto de código aberto do Internet Archive para catalogar todos os livros do mundo. Possui uma das maiores operações de digitalização de livros do mundo e tem muitos livros disponíveis para empréstimo digital. Seu catálogo de metadados de livros está disponível gratuitamente para download e está incluído no Arquivo da Anna (embora atualmente não esteja na busca, exceto se você procurar explicitamente por um ID da Open Library). Open Library Excluindo duplicados Última atualização Percentagens do número de arquivos %% espelhado por AA / torrents disponíveis Tamanho Fonte Abaixo está uma visão geral rápida das fontes dos arquivos no Arquivo da Anna. Como as bibliotecas-sombra frequentemente sincronizam dados entre si, há uma sobreposição considerável entre as bibliotecas. É por isso que os números não somam o total. A porcentagem “espelhada e semeada pelo Arquivo da Anna” mostra quantos arquivos nós mesmos espelhamos. Semeamos esses arquivos em massa através de torrents e os disponibilizamos para download direto através de sites parceiros. Visão geral Total Torrents no Arquivo da Anna Para obter mais informações sobre o Sci-Hub, consulte seu <a %(a_scihub)s>site oficial</a>, <a %(a_wikipedia)s>página na Wikipedia</a> e esta <a %(a_radiolab)s>entrevista em podcast</a>. Observe que o Sci-Hub está <a %(a_reddit)s>congelado desde 2021</a>. Ele já havia sido congelado antes, mas em 2021 alguns milhões de artigos foram adicionados. Ainda assim, um número limitado de artigos é adicionado às coleções “scimag” do Libgen, embora não o suficiente para justificar novos torrents em massa. Usamos os metadados do Sci-Hub fornecidos pelo <a %(a_libgen_li)s>Libgen.li</a> em sua coleção “scimag”. Também usamos o conjunto de dados <a %(a_dois)s>dois-2022-02-12.7z</a>. Observe que os torrents “smarch” estão <a %(a_smarch)s>obsoletos</a> e, portanto, não estão incluídos em nossa lista de torrents. Torrents no Libgen.li Torrents no Libgen.rs Metadados e torrents Atualizações no Reddit Entrevista em podcast Página na Wikipedia Sci-Hub Sci-Hub: congelado desde 2021; a maioria disponível através de torrents Libgen.li: pequenas adições desde então</div> Algumas bibliotecas-fonte promovem o compartilhamento em massa de seus dados através de torrents, enquanto outras não compartilham prontamente sua coleção. No último caso, o Arquivo da Anna tenta extrair suas coleções e torná-las disponíveis (veja nossa página de <a %(a_torrents)s>Torrents</a>). Existem também situações intermediárias, por exemplo, onde as bibliotecas-fonte estão dispostas a compartilhar, mas não têm os recursos para fazê-lo. Nesses casos, também tentamos ajudar. Abaixo está uma visão geral de como interagimos com as diferentes bibliotecas-fonte. Bibliotecas-fonte %(icon)s Vários bancos de dados de arquivos espalhados pela internet chinesa; embora muitas vezes sejam bancos de dados pagos %(icon)s A maioria dos arquivos só é acessível usando contas premium do BaiduYun; velocidades de download lentas. %(icon)s Arquivo da Anna gerencia uma coleção de <a %(duxiu)s>arquivos do DuXiu</a> %(icon)s Vários bancos de dados de metadados espalhados pela internet chinesa; embora muitas vezes sejam bancos de dados pagos %(icon)s Não há dumps de metadados facilmente acessíveis disponíveis para toda a coleção. %(icon)s Arquivo da Anna gerencia uma coleção de <a %(duxiu)s>metadados do DuXiu</a> Arquivos %(icon)s Arquivos disponíveis apenas para empréstimo de forma limitada, com várias restrições de acesso %(icon)s Arquivo da Anna gerencia uma coleção de <a %(ia)s>arquivos do IA</a> %(icon)s Alguns metadados disponíveis através de <a %(openlib)s>dumps de banco de dados da Open Library</a>, mas esses não cobrem toda a coleção do IA %(icon)s Não há dumps de metadados facilmente acessíveis disponíveis para toda a coleção %(icon)s Arquivo da Anna gerencia uma coleção de <a %(ia)s>metadados do IA</a> Última atualização %(icon)s Arquivo da Anna e Libgen.li gerenciam colaborativamente coleções de <a %(comics)s>quadrinhos</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos comuns</a> e <a %(fiction)s>ficção (divergente do Libgen.rs)</a>. %(icon)s Torrents de Não-Ficção são compartilhados com o Libgen.rs (e espelhados <a %(libgenli)s>aqui</a>). %(icon)s Dumps trimestrais de banco de dados <a %(dbdumps)s>HTTP</a> %(icon)s Torrents automatizados para <a %(nonfiction)s>Não-Ficção</a> e <a %(fiction)s>Ficção</a> %(icon)s O Arquivo da Anna gerencia uma coleção de <a %(covers)s>torrents de capas de livros</a> %(icon)s Dumps diários de banco de dados <a %(dbdumps)s>HTTP</a> Metadados %(icon)s Dumps de banco de dados <a %(dbdumps)s>mensais</a> %(icon)s Torrents de dados disponíveis <a %(scihub1)s>aqui</a>, <a %(scihub2)s>aqui</a> e <a %(libgenli)s>aqui</a> %(icon)s Alguns novos arquivos estão <a %(libgenrs)s>sendo</a> <a %(libgenli)s>adicionados</a> ao “scimag” do Libgen, mas não o suficiente para justificar novos torrents %(icon)s O Sci-Hub congelou novos arquivos desde 2021. %(icon)s Dumps de metadados disponíveis <a %(scihub1)s>aqui</a> e <a %(scihub2)s>aqui</a>, bem como como parte do <a %(libgenli)s>banco de dados Libgen.li</a> (que usamos) Fonte %(icon)s Várias fontes menores ou pontuais. Encorajamos as pessoas a fazerem upload para outras bibliotecas-sombra primeiro, mas às vezes as pessoas têm coleções grandes demais para outros organizarem, embora não grandes o suficiente para justificar sua própria categoria. %(icon)s Não disponível diretamente em massa, protegido contra scraping %(icon)s O Arquivo da Anna gerencia uma coleção de <a %(worldcat)s>metadados OCLC (WorldCat)</a> %(icon)s Arquivo da Anna e Z-Library gerenciam colaborativamente uma coleção de <a %(metadata)s>metadados do Z-Library</a> e <a %(files)s>arquivos do Z-Library</a> Datasets Combinamos todas as fontes acima em um banco de dados unificado que usamos para preencher este site. Este banco de dados unificado não está disponível diretamente, mas como o Arquivo da Anna é totalmente de código aberto, ele pode ser <a %(a_generated)s>gerado</a> ou <a %(a_downloaded)s>baixado</a> como bancos de dados ElasticSearch e MariaDB. Os scripts nessa página baixarão automaticamente todos os metadados necessários das fontes mencionadas acima. Se você quiser explorar nossos dados antes de executar esses scripts localmente, pode olhar nossos arquivos JSON, que se vinculam então a outros arquivos JSON. <a %(a_json)s>Este arquivo</a> é um bom ponto de partida. Banco de dados unificado Torrents feitos pelo Arquivo da Anna navegar pesquisar Diversas fontes menores ou pontuais. Incentivamos as pessoas a fazerem upload em outras bibliotecas-sombra primeiro, mas às vezes as pessoas têm coleções que são grandes demais para que outros possam organizar, embora não sejam grandes o suficiente para justificar sua própria categoria. Visão geral da <a %(a1)s>página de datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bem completo. Do nosso voluntário “cgiym”. De um torrent da <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tem uma sobreposição considerável com coleções de artigos existentes, mas muito poucos correspondem em MD5, então decidimos mantê-lo completamente. Scrape de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), por voluntário <q>j</q>. Corresponde ao metadado de <q>airitibooks</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>. De uma coleção <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Pacialmente da fonte original, pacialmente de the-eye.eu, pacialmente de outros espelhos. De um site privado de torrents de livros, <a %(a_href)s>Bibliotik</a> (frequentemente referido como “Bib”), cujos livros foram agrupados em torrents por nome (A.torrent, B.torrent) e distribuídos através do the-eye.eu. Do nosso voluntário “bpb9v”. Para mais informações sobre <a %(a_href)s>CADAL</a>, veja as anotações na nossa <a %(a_duxiu)s>página do conjunto de dados DuXiu</a>. Mais do nosso voluntário “bpb9v”, principalmente arquivos DuXiu, bem como uma pasta “WenQu” e “SuperStar_Journals” (SuperStar é a empresa por trás do DuXiu). Do nosso voluntário “cgiym”, textos chineses de várias fontes (representados como subdiretórios), incluindo da <a %(a_href)s>China Machine Press</a> (uma grande editora chinesa). Coleções não chinesas (representadas como subdiretórios) do nosso voluntário “cgiym”. Scrapes de livros sobre arquitetura chinesa, pelo voluntário <q>cm</q>: <q>Consegui explorando uma vulnerabilidade de rede na editora, mas essa brecha já foi fechada</q>. Corresponde aos metadados de <q>chinese_architecture</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>. Livros da editora acadêmica <a %(a_href)s>De Gruyter</a>, coletados de alguns torrents grandes . Scrape de <a %(a_href)s>docer.pl</a>, um site polonês de compartilhamento de arquivos focado em livros e outras obras escritas. Scrape realizado no final de 2023 pelo voluntário “p”. Não temos bons metadados do site original (nem mesmo extensões de arquivo), mas filtramos arquivos semelhantes a livros e muitas vezes conseguimos extrair metadados dos próprios arquivos. Epubs do DuXiu, diretamente do DuXiu, coletados pelo voluntário “w”. Apenas livros recentes do DuXiu estão disponíveis diretamente através de ebooks, então a maioria desses deve ser recente. Arquivos restantes do DuXiu do voluntário “m”, que não estavam no formato proprietário PDG do DuXiu (o principal <a %(a_href)s>conjunto de dados do DuXiu</a>). Coletados de muitas fontes originais, infelizmente sem preservar essas fontes no caminho do arquivo. <span></span> <span></span> <span></span> Raspagem de livros eróticos, pelo voluntário <q>do no harm</q>. Corresponde aos metadados de <q>hentai</q> em <a %(a1)s><q>Outros scrapes de metadados</q></a>. <span></span> <span></span> Coleção raspada de um editor japonês de Mangá pelo voluntário <q>t</q>.. <a %(a_href)s>Arquivos judiciais selecionados de Longquan</a>, fornecidos pelo voluntário “c”. Scrape de <a %(a_href)s>magzdb.org</a>, um aliado da Library Genesis (está vinculado na página inicial do libgen.rs) mas que não quis fornecer seus arquivos diretamente. Obtido pelo voluntário <q>p</q> no final de 2023. <span></span> Diversos pequenos uploads, pequenos demais para serem uma subcoleção própria, mas representados como diretórios. O diretório <q>oo42hcksBxZYAOjqwGWu</q> corresponde aos metadados <q>czech_oo42hcks</q> em <a %(a1)s><q> outros scrapes de metadados</q></a>. Ebooks do AvaxHome, um site russo de compartilhamento de arquivos. Arquivo de jornais e revistas. Corresponde aos metadados de <q>newsarch_magz</q> em <a %(a1)s><q>Outras raspagens de metadados</q></a>. Scrape do <a %(a1)s>Philosophy Documentation Center</a>. Coleção do voluntário <q>o</q> que coletou livros poloneses diretamente dos sites de lançamento original (<q>scene</q>). Coleções combinadas de <a %(a_href)s>shuge.org</a> pelos voluntários “cgiym” e “woz9ts”. <span></span> <a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomeada em homenagem à biblioteca fictícia), raspada em 2022 pelo voluntário “t”. <span></span> <span></span> <span></span> Sub-sub-coleções (representadas como diretórios) do voluntário “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> em Taiwan), mebook (mebook.cc, 我的小书屋, meu pequeno quarto de livros — woz9ts: “Este site foca principalmente em compartilhar arquivos de ebooks de alta qualidade, alguns dos quais são diagramados pelo próprio dono. O dono foi <a %(a_arrested)s>preso</a> em 2019 e alguém fez uma coleção dos arquivos que ele compartilhou.”). Arquivos restantes do DuXiu do voluntário <q>woz9ts</q>, que não estavam no formato proprietário PDG do DuXiu (ainda a serem convertidos para PDF). A coleção de “uploads” é dividida em subcoleções menores, que são indicadas nos AACIDs e nos nomes dos torrents. Todas as subcoleções foram primeiro desduplicadas em relação à coleção principal, embora os arquivos JSON de metadados “upload_records” ainda contenham muitas referências aos arquivos originais. Arquivos que não são livros também foram removidos da maioria das subcoleções e, geralmente, <em>não</em> são mencionados no JSON “upload_records”. As subcoleções são: Anotações Subcoleção Muitas subcoleções são compostas por sub-sub-coleções (por exemplo, de diferentes fontes originais), que são representadas como diretórios nos campos “filepath”. Uploads para o Arquivo da Anna Nosso post no blog sobre esses dados <a %(a_worldcat)s>WorldCat</a> é um banco de dados proprietário da organização sem fins lucrativos <a %(a_oclc)s>OCLC</a>, que agrega registros de metadados de bibliotecas de todo o mundo. Provavelmente é a maior coleção de metadados de bibliotecas do mundo. Em outubro de 2023, <a %(a_scrape)s>lançamos</a> um grande scrapping de banco de dados da OCLC (WorldCat), no <a %(a_aac)s>formato "Containers do Arquivo da Anna"</a>. Outubro de 2023, lançamento inicial: OCLC (WorldCat) Torrents feitos pelo Arquivo da Anna Exemplo de registro no Arquivo da Anna (coleção original) Exemplo de registro no Arquivo da Anna (coleção “zlib3”) Torrents feitos pelo Arquivo da Anna (metadados + conteúdo) Postagem no blog sobre o Lançamento 1 Postagem no blog sobre o Lançamento 2 No final de 2022, os supostos fundadores do Z-Library foram presos e os domínios foram apreendidos pelas autoridades dos Estados Unidos. Desde então, o site tem lentamente voltado a ficar online. Não se sabe quem o administra atualmente. Atualização de fevereiro de 2023. O Z-Library tem suas raízes na comunidade <a %(a_href)s>Library Genesis</a> e originalmente começou com os dados deles. Desde então, profissionalizou-se consideravelmente e possui uma interface muito mais moderna. Portanto, eles conseguem obter muitas mais doações, tanto monetárias para continuar melhorando seu site, quanto doações de novos livros. Eles acumularam uma grande coleção além da Library Genesis. A coleção consiste em três partes. As páginas de descrição originais para as duas primeiras partes estão preservadas abaixo. Você precisa das três partes para obter todos os dados (exceto torrents de substituição, que estão riscados na página de torrents). %(title)s: nosso primeiro lançamento. Este foi o primeiro lançamento do que então era chamado de “Pirate Library Mirror” (“pilimi”). %(title)s: segundo lançamento, desta vez com todos os arquivos comprimidos em arquivos .tar. %(title)s: lançamentos incrementais, usando o formato <a %(a_href)s>Containers do Arquivo da Anna (AAC)</a>, agora lançados em colaboração com a equipe da Z-Library. O espelhamento inicial foi obtido meticulosamente ao longo de 2021 e 2022. Neste ponto, está ligeiramente desatualizado: reflete o estado da coleção em junho de 2021. Atualizaremos isso no futuro. No momento, estamos focados em lançar esta primeira versão. Como o Library Genesis já está preservado com torrents públicos e está incluído na Z-Library, fizemos uma deduplicação básica contra o Library Genesis em junho de 2022. Para isso, usamos hashes MD5. Provavelmente há muito mais conteúdo duplicado na biblioteca, como vários formatos de arquivo do mesmo livro. Isso é difícil de detectar com precisão, então não o fazemos. Após a deduplicação, continuamos com mais de 2 milhões de arquivos, totalizando pouco menos de 7TB. A coleção consiste em duas partes: um dump MySQL “.sql.gz” dos metadados e os 72 arquivos torrent de cerca de 50-100GB cada. Os metadados contêm os dados conforme relatados pelo site da Z-Library (título, autor, descrição, tipo de arquivo), bem como o tamanho real do arquivo e o md5sum que observamos, já que às vezes esses não coincidem. Parece haver faixas de arquivos para os quais a própria Z-Library tem metadados incorretos. Também podemos ter baixado arquivos incorretamente em alguns casos isolados, que tentaremos detectar e corrigir no futuro. Os arquivos de torrent grandes contêm os dados reais dos livros, com o ID da Z-Library como nome do arquivo. As extensões de arquivo podem ser reconstruídas usando o dump de metadados. A coleção é uma mistura de conteúdo de não-ficção e ficção (não estão separadas como no Library Genesis). A qualidade também varia amplamente. Este primeiro lançamento está agora totalmente disponível. Note que os arquivos torrent estão disponíveis apenas através do nosso espelho Tor. Lançamento 1 (%(date)s) Este é um único arquivo torrent extra. Ele não contém nenhuma informação nova, mas tem alguns dados que podem demorar para serem computados. Isso o torna conveniente, pois baixar este torrent é frequentemente mais rápido do que computá-lo do zero. Em particular, ele contém índices SQLite para os arquivos tar, para uso com <a %(a_href)s>ratarmount</a>. Adendo da Versão 2 (%(date)s) Obtivemos todos os livros que foram adicionados à Z-Library entre nosso último espelhamento e agosto de 2022. Também voltamos e raspamos alguns livros que perdemos na primeira vez. No total, esta nova coleção tem cerca de 24TB. Novamente, esta coleção é deduplicada em relação ao Library Genesis, já que já existem torrents disponíveis para essa coleção. Os dados estão organizados de forma semelhante ao primeiro lançamento. Há um dump MySQL “.sql.gz” dos metadados, que também inclui todos os metadados do primeiro lançamento, substituindo-o. Também adicionamos algumas novas colunas: Mencionamos isso da última vez, mas só para esclarecer: “filename” e “md5” são as propriedades reais do arquivo, enquanto “filename_reported” e “md5_reported” são as informações que extraímos do Z-Library. Às vezes, esses dois não coincidem, então incluímos ambos. Para esta versão, mudamos a "collation" para “utf8mb4_unicode_ci”, que deve ser compatível com versões mais antigas do MySQL. Os arquivos de dados são semelhantes aos da última vez, embora sejam muito maiores. Simplesmente não nos preocupamos em criar toneladas de arquivos torrent menores. “pilimi-zlib2-0-14679999-extra.torrent” contém todos os arquivos que perdemos na última versão, enquanto os outros torrents são todos novos intervalos de ID.  <strong>Atualização %(date)s:</strong> Fizemos a maioria dos nossos torrents muito grandes, causando dificuldades nos clientes de torrent. Nós os removemos e lançamos novos torrents. <strong>Atualização %(date)s:</strong> Ainda havia muitos arquivos, então os compactamos em arquivos tar e lançamos novos torrents novamente. %(key)s: se este arquivo já está no Library Genesis, seja na coleção de não-ficção ou ficção (correspondido pelo md5). %(key)s: em qual torrent este arquivo está. %(key)s: definido quando não conseguimos baixar o livro. Lançamento 2 (%(date)s) Lançamentos do Zlib (páginas de descrição originais) Domínio Tor Site principal Scrape do Z-Library A coleção “Chinesa” na Z-Library parece ser a mesma que a nossa coleção DuXiu, mas com MD5s diferentes. Excluímos esses arquivos dos torrents para evitar duplicação, mas ainda os mostramos em nosso índice de busca. Metadados Você possui %(percentage)s%% downloads rápidos bônus, pois você foi indicado pelo usuário %(profile_link)s. Isso se aplica a todo o período de assinatura. Doar Participe Selecionado até %(percentage)s%% de desconto Alipay aceita cartões de crédito/débito internacionais. Veja <a %(a_alipay)s>este guia</a> para mais informações. Envie-nos cartões-presente da Amazon.com usando seu cartão de crédito/débito. Você pode comprar criptomoedas usando cartões de crédito/débito. WeChat (Weixin Pay) oferece suporte a cartões de crédito/débito internacionais. No aplicativo WeChat, vá para “Eu → Serviços → Carteira → Adicionar um cartão”. Se não encontrar, habilite-o usando “Eu → Configurações → Geral → Ferramentas → Weixin Pay → Ativar”. (use ao enviar Ethereum do Coinbase) copiado! copiar (valor mínimo mais baixo) (aviso: valor mínimo alto) -%(percentage)s%% 12 meses 1 mês 24 meses 3 meses 48 meses 6 meses 96 meses Selecione por quanto tempo você quer assinar. <div %(div_monthly_cost)s></div><div %(div_after)s>depois de <span %(span_discount)s></span> de desconto</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% por 12 meses Por 1 mês por 24 meses por 3 meses por 48 meses por 6 meses por 96 meses %(monthly_cost)s / mês Contate-nos Servidores<strong>SFTP</strong> diretos Doação ou troca em nível empresarial para novas coleções (por exemplo, novos escaneamentos, conjuntos de dados com OCR). Acesso Avançado Acesso de alta velocidade <strong>Ilimitado</strong> <div %(div_question)s>Posso atualizar minha assinatura ou obter várias assinaturas?</div> <div %(div_question)s>Posso fazer uma doação sem me tornar membro?</div> Claro. Aceitamos doações de qualquer valor neste endereço Monero (XMR): %(address)s. <div %(div_question)s>O que significam as faixas por mês?</div> Você pode alcançar o lado mais baixo de uma faixa aplicando todos os descontos, como escolher um período superior a um mês. <div %(div_question)s>As assinaturas são renovadas automaticamente?</div> As assinaturas <strong>não</strong> são renovadas automaticamente. Você pode participar por quanto tempo quiser. <div %(div_question)s>Como vocês usam as doações?</div> 100%% do valor é usado para preservar e tornar o conhecimento e a cultura do mundo acessíveis. Atualmente nós gastamos a maior parte em servidores, armazenamento e largura de banda. Nenhum dinheiro vai pessoalmente para membros do time. <div %(div_question)s>Posso fazer uma doação grande?</div> Seria incrível! Para doações acima de mil dólares, por favor entre em contato em %(email)s. <div %(div_question)s>Vocês usam outras formas de pagamento?</div> Atualmente não. Muitas pessoas não querem que arquivos como este existam, então precisamos tomar cuidado. Se você pode nos ajudar a usar alguma outra forma de pagamento com segurança, entre em contato no %(email)s. Dúvidas frequentes sobre doações Você tem uma <a %(a_donation)s>doação existente</a> em andamento. Por favor, finalize ou cancele essa doação antes de fazer uma nova doação. <a %(a_all_donations)s>Ver todas as minhas doações</a> Para doações acima de 5000 dólares, entre em contato diretamente no email %(email)s. Aceitamos grandes doações de indivíduos bem-afortunados ou instituições ricas.  Esteja ciente de que, embora as assinaturas nesta página sejam "por mês", elas são doações únicas (não recorrentes). Veja a <a %(faq)s>FAQ - Doações</a>. Acervo da Anna é um projeto sem fins lucrativos, de código e dados abertos. Ao doar e tornar-se um membro, você apoia o nosso funcionamento e crescimento. A todos os nossos membros: muito obrigado por nos ajudar a manter o projeto! ❤️ Para obter mais informações, consulte a <a %(a_donate)s>FAQ - doações</a>. Para se tornar um membro, por favor <a %(a_login)s>Faça Log in ou Registre-se</a>. Obrigado pelo seu suporte! $%(cost)s / mês Se você cometeu um erro durante o pagamento, não podemos reembolsar, mas podemos tentar resolver a situação. Encontre a página "Cripto" em seu aplicativo ou site do PayPal. Normalmente, isso está localizado em "Finanças". Vá para a página "Bitcoin" em seu aplicativo ou site do PayPal. Clique no botão "Transferir" %(transfer_icon)s e, em seguida, em "Enviar". Alipay Alipay 支付宝 / WeChat 微信 Cartão Presente Amazon cartão-presente da %(amazon)s Cartão bancário Cartão bancário (usando app) Binance Crédito / débito / Apple / Google (BMC) Cash App Cartão de crédito/débito Cartão de crédito/débito 2 Cartão de crédito/débito (backup) Cripto %(bitcoin_icon)s Cartão / PayPal / Venmo PayPal (EUA) %(bitcoin_icon)s PayPal PayPal (regular) Pix (Brasil) Revolut (temporariamente indisponível) WeChat Selecione a criptomoeda desejada: Doe usando um gift card da Amazon. <strong>IMPORTANTE:</strong> Esta opção é para %(amazon)s. Se você deseja usar outro site da Amazon, selecione-o acima. <strong>IMPORTANTE:</strong> Oferecemos suporte apenas para Amazon.com, e não outros sites da Amazon. Por exemplo, .br, .de, .co.uk e .ca NÃO são suportados. Por favor, NÃO escreva sua própria mensagem. Insira o valor exato: %(amount)s Observe que precisamos arredondar para valores aceitos por nossos revendedores (mínimo de %(minimum)s). Doe usando um cartão de crédito/débito, através do aplicativo Alipay (super fácil de configurar). Instale o aplicativo Alipay na <a %(a_app_store)s>Apple App Store</a> ou <a %(a_play_store)s>Google Play Store</a>. Registre-se usando seu número de telefone. Nenhum outro detalhe pessoal é necessário. <span %(style)s>1</span>Instale o aplicativo Alipay Aceitamos: Visa, MasterCard, JCB, Diners Club e Discover. Veja <a %(a_alipay)s>este guia</a> para mais informações. <span %(style)s>2</span>Adicione um cartão bancário Com a Binance, você compra Bitcoin com um cartão de crédito/débito ou conta bancária e depois doa esse Bitcoin para nós. Dessa forma, podemos permanecer seguros e anônimos ao aceitar sua doação. A Binance está disponível em quase todos os países e oferece suporte à maioria dos bancos e cartões de crédito/débito. Esta é atualmente a nossa principal recomendação. Agradecemos por você dedicar seu tempo para aprender como doar por meio desse método, pois nos ajuda muito. Para cartões de crédito, cartões de débito, Apple Pay e Google Pay, usamos “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). No sistema deles, um “café” equivale a US$ 5, então sua doação será arredondada para o múltiplo de 5 mais próximo. Doe usando o Cash App. Se você tiver Cash App, essa é a maneira mais fácil de doar! Observe que para transações abaixo de %(amount)s, o Cash App pode cobrar uma taxa de %(fee)s. Para %(amount)s ou mais, é gratuito! Doe com um cartão de crédito ou débito. Este método usa um provedor de criptomoeda como conversão intermediária. Isso pode ser um pouco confuso, portanto, use esse método apenas se outros métodos de pagamento não funcionarem. Também não funciona em todos os países. Não podemos aceitar cartões de crédito/débito diretamente, porque os bancos não querem trabalhar conosco. ☹ No entanto, há várias maneiras de usar cartões de crédito/débito, utilizando outros métodos de pagamento: Com cripto você pode doar com as moedas BTC, ETH, XMR, e SOL. Use essa opção se você já é familiarizado com criptomoedas. Com cripto você pode doar usando BTC, ETH, XMR e muito mais. Serviços expressos de criptomoeda Se você está usando criptomoeda pela primeira vez, sugerimos usar %(options)s para comprar e doar Bitcoin (a criptomoeda original e mais usada). Observe que para doações pequenas, as taxas do cartão de crédito podem eliminar nosso desconto de %(discount)s%%, então recomendamos assinaturas mais longas. Doe usando cartão de crédito/débito, PayPal ou Venmo. Você pode escolher entre eles na próxima página. Google Pay e Apple Pay também devem funcionar. Observe que para doações pequenas, as taxas são altas, então recomendamos assinaturas mais longas. Para doar usando o PayPal (EUA), usamos PayPal cripto, que nos permite permanecer anônimos. Agradecemos por dedicar seu tempo para aprender como doar usando este método, pois é de grande ajuda para nós. Doe usando o PayPal. Doe usando sua conta regular do PayPal. Doe usando Revolut. Se você usa Revolut, esta é a maneira mais fácil de doar! Este método de pagamento apenas permite um máximo de %(amount)s. Por favor selecione um método ou duração diferentes. Este método de pagamento requer um mínimo de %(amount)s. Por favor, selecione uma duração ou método de pagamento diferente. Binance Coinbase Kraken Por favor, selecione um método de pagamento. “Adote um torrent”: seu usuário ou uma mensagem no nome de um torrent <div %(div_months)s>uma vez a cada 12 meses de assinatura</div> O seu nome de usuário ou uma menção anónima nos créditos Acesso antecipado a novas funcionalidades Atualizações exclusivas dos bastidores no Telegram %(number)s downloads rápidos por dia se você doar este mês! Acesso à <a %(a_api)s>API JSON</a> Status lendário na preservação do conhecimento e da cultura da humanidade Vantagens anteriores, mais: Ganhe <strong>%(percentage)s%% downloads bônus</strong> <a %(a_refer)s>indicando amigos</a>. Artigos do SciDB <strong>ilimitados</strong> sem verificação Ao fazer perguntas sobre contas ou doações, adicione o ID da sua conta, capturas de tela, recibos e o máximo de informações possível. Verificamos nosso e-mail apenas a cada 1–2 semanas, portanto, não incluir essas informações atrasará qualquer resolução. Para obter ainda mais downloads, <a %(a_refer)s>indique seus amigos</a>! Nós somos um pequeno grupo de voluntários. Pode levar de 1 a 2 semanas para responder. Observe que o nome de usuário ou a imagem podem parecer estranhos. Não se preocupe! Essas contas são administradas pelos nossos parceiros de doação. Nossas contas não foram hackeadas. Doar <span %(span_cost)s></span> <span %(span_label)s></span> por 12 meses “%(tier_name)s” por 1 mês “%(tier_name)s” por 24 meses “%(tier_name)s” por 3 meses “%(tier_name)s” por 48 meses “%(tier_name)s” por 6 meses “%(tier_name)s” por 96 meses “%(tier_name)s” Você ainda pode cancelar a doação até a última etapa. Clique no botão doar para confirmar esta doação. <strong>Nota importante:</strong> os preços das criptomoedas podem flutuar muito, às vezes até 20%% em alguns minutos. Essa flutuação ainda é menor que as taxas que pagamos a vários provedores de pagamento, que geralmente cobram entre 50-60%% para uma "caridade escondida" como a nossa. <u>Se você nos enviar o comprovante do valor original que pagou, iremos aplicar na sua conta a assinatura escolhida. </u> (se o comprovante for de poucas horas atrás). Agradecemos que você esteja disposto a lidar com coisas assim para nos ajudar! ❤️ ❌ Algo deu errado. Por favor, atualize a página e tente novamente. <span %(span_circle)s>1</span>Compre Bitcoin no PayPal <span %(span_circle)s>2</span>Transferir o Bitcoin para nosso endereço ✅ Redirecionando para a página de doação… Doar Por favor, aguarde pelo menos <span %(span_hours)s>24 horas</span> (e atualize esta página) antes de nos contatar. Se quiser fazer uma doação (qualquer valor) sem ser membro, sinta-se à vontade para usar este endereço Monero (XMR): %(address)s. Após enviar o seu gift card, nosso sistema automatizado irá confirmar em poucos minutos. Caso não funcione, tente reenviar seu gift card (<a %(a_instr)s>instruções</a>). Caso ainda não funcione por favor nos envie um email e a Anna eventualmente irá verificar manualmente (isso poderá levar alguns dias), e tenha certeza de mencionar que você já tentou reenviar. Exemplo: Por favor use o <a %(a_form)s>formulário oficial da Amazon.com</a> para nos enviar um gift card de%(amount)s para o endereço de email abaixo. "Para" email do destinatário no formulário: Gift Card da Amazon Nós não podemos aceitar outros métodos de gift cards, <strong> somente os enviados diretamente do formulário oficial da Amazon.com</strong>. Nós não podemos devolver o seu gift card se você não fizer uso deste formulário. Use apenas uma vez. Único para sua conta, não compartilhe. Esperando pelo gift card... (atualize a página para checar) Abra a <a%(a_href)s>página de doação com código QR</a>. Escaneie o código QR com o aplicativo Alipay ou pressione o botão para abrir o aplicativo Alipay. Por favor, seja paciente; a página pode demorar um pouco para carregar, pois está na China. <span %(style)s>3</span>Faça uma doação (escaneie o código QR ou pressione o botão) Compre a moeda PYUSD no PayPal Compre Bitcoin (BTC) no Cash App Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante. Vá para a página “Bitcoin” (BTC) no Cash App. Transfira o Bitcoin para o nosso endereço Para pequenas doações (menos de $25), você pode precisar usar Rush ou Priority. Clique no botão “Enviar bitcoin” para fazer uma “retirada”. Mude de dólares para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se você estiver com dúvidas. Os serviços expressos são convenientes, mas cobram taxas mais altas. Você pode usar isso ao invés de uma corretora de criptomoedas se estiver procurando fazer uma doação grande mais rapidamente, e não se importar com uma taxa de US$ 5 a US$ 10. Certifique-se de enviar a quantidade exata de criptomoeda mostrada na página de doação, não o valor em $USD. Caso contrário, a taxa será subtraída e não poderemos processar automaticamente sua assinatura. Às vezes, a confirmação pode levar até 24 horas, então certifique-se de atualizar esta página (mesmo que ela tenha expirado). Instruções para cartões de crédito e débito Doe através de nossa página para cartões de crédito e débito Alguns dos passos mencionam carteiras de criptomoedas, mas não se preocupe, você não precisa aprender nada sobre criptomoedas para isso. Instruções para %(coin_name)s Digitalize este código QR com seu aplicativo Crypto Wallet para preencher rapidamente os detalhes do pagamento Digitalize o código QR para pagar Nós só apoiamos a versão padrão das criptomoedas, sem redes ou versões exóticas das moedas. Pode levar até uma hora para confirmar a transação, dependendo da moeda. Doe %(amount)s nessa <a %(a_page)s>página</a>. Essa doação foi expirada. Por favor cancele e crie uma nova. Se você já pagou: Sim, eu enviei meu comprovante Se a taxa de câmbio da criptomoeda flutuou durante a transação, não esqueça de incluir o comprovante mostrando a taxa de câmbio original. Agradecemos muito o esforço de usar criptomoedas, nos ajuda muito! ❌ Algo deu errado. Por favor, recarregue a página e tente novamente. <span %(span_circle)s>%(circle_number)s</span>Envie o comprovante por email Se você encontrar algum problema, por favor nos contate em %(email)s e inclua o máximo de informação possível (como capturas de tela). ✅ Obrigado pela sua doação! Anna ativará manualmente sua assinatura dentro de alguns dias. Enviar um comprovante ou captura de tela para seu endereço de verificação pessoal: Após enviar seu recibo por e-mail, clique neste botão para que Anna possa verificar manualmente (isso pode levar alguns dias): Envie um recibo ou captura de tela para o seu endereço de verificação pessoal. NÃO use este endereço de e-mail para sua doação via PayPal. Cancelar Sim, quero cancelar Tem certeza de que deseja cancelar? Não cancele se já tiver efetuado o pagamento. ❌ Algo deu errado. Por favor, recarregue a página e tente novamente. Fazer uma nova doação ✅ Sua doação foi cancelada. Data: %(date)s Identificador: %(id)s Repetir doação Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mês por %(duration)s meses, incluindo %(discounts)s%% de desconto)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mês por %(duration)s meses)</span> 1. Insira seu email. 2. Selecione seu método de pagamento. 3. Selecione novamente seu método de pagamento. 4. Selecionar carteira "Self-hosted / auto-hospedada". 5. Clique em "Eu confirmo titularidade". 6. Você receberá um recibo via email. Por favor nos envie isso e vamos confirmar sua doação o mais rápido possível. (você pode querer cancelar e criar uma nova doação) As instruções de pagamento estão desatualizadas. Se você deseja fazer outra doação, use o botão "Repetir doação" acima. Você já efetuou o pagamento. Se desejar revisar as instruções de pagamento de qualquer maneira, clique aqui: Mostrar as instruções de pagamento antigas Se a página de doação for bloqueada, tente uma conexão de internet diferente (por exemplo, VPN ou internet do celular). Infelizmente, a página Alipay geralmente só é acessível na <strong>China continental</strong>. Pode ser necessário desativar temporariamente sua VPN ou usar uma VPN para a China continental (ou Hong Kong também funciona às vezes). <span %(span_circle)s>1</span>Doe no Alipay Doe o valor total de %(total)s usando <a %(a_account)s>esta conta Alipay</a> Instruções Alipay <span %(span_circle)s>1</span>Transferir para uma de nossas contas de criptomoedas Faça a doação do valor total de %(total)s para um destes endereços: Instruções para criptomoedas Siga as instruções para comprar Bitcoin (BTC). Você só precisa comprar a quantidade que deseja doar, %(total)s. Insira nosso endereço Bitcoin (BTC) como o destinatário e siga as instruções para enviar sua doação de %(total)s: <span %(span_circle)s>1</span> Doe por Pix Doe a quantia total de %(total)s usando a conta Pix <a %(a_account)s> Instruções para Pix <span %(span_circle)s>1</span>Doe no WeChat Doe o valor total de %(total)s usando <a %(a_account)s>esta conta do WeChat</a> Instruções do WeChat Use qualquer um dos seguintes serviços expressos de “cartão de crédito para Bitcoin”, que levam apenas alguns minutos: Endereço BTC / Bitcoin (carteira externa): Valor em BTC / Bitcoin: Preencha os seguintes detalhes no formulário: Se alguma dessas informações estiver desatualizada, por favor envie-nos um e-mail para nos informar. Por favor, use este <span %(underline)s>valor exato</span>. Seu custo total pode ser maior devido às taxas do cartão de crédito. Para pequenos valores, isso pode ser mais do que nosso desconto, infelizmente. (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s, sem verificação para a primeira transação) (mínimo: %(minimum)s) (mínimo: %(minimum)s dependendo do país, sem verificação para a primeira transação) Siga as instruções para comprar a moeda PYUSD (PayPal USD). Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante. Vá para a página "PYUSD" no seu aplicativo ou site do PayPal. Aperte o botão "Transferir" %(icon)s, e em seguida "Enviar". Atualizar status Para resetar o timer, simplesmente crie uma nova doação. Certifique-se de usar o valor em BTC abaixo, <em>NÃO</em> euros ou dólares, caso contrário, não receberemos o valor correto e não poderemos confirmar automaticamente sua assinatura. Compre Bitcoin (BTC) no Revolut Compre um pouco mais (recomendamos %(more)s a mais) do que o valor que você está doando (%(amount)s), para cobrir as taxas de transação. Você ficará com qualquer valor restante. Vá para a página “Cripto” no Revolut para comprar Bitcoin (BTC). Transfira o Bitcoin para o nosso endereço Para pequenas doações (menos de $25), você pode precisar usar Rush ou Priority. Clique no botão “Enviar bitcoin” para fazer uma “retirada”. Mude de euros para BTC pressionando o ícone %(icon)s. Insira o valor em BTC abaixo e clique em “Enviar”. Veja <a %(help_video)s>este vídeo</a> se você ficar com dúvidas. Status: 1 2 Guia passo-a-passo Preste atenção no guia passo-a-passo a seguir. Caso contrário, poderá ficar sem acesso a esta conta! Caso ainda não tenha, anote sua chave secreta para fazer login: Obrigado por sua doação! Tempo restante: Doação Transfira %(amount)s para %(account)s Aguardando confirmação (atualize a página para verificar)… Aguardando transferência (atualize a página para verificar)… Anteriormente Downloads rápidos nas últimas 24 horas contam no limite diário. Downloads dos nossos Servidores Rápidos de Parceiros estão marcados com %(icon)s. Últimas 18 horas Nenhum arquivo baixado até o momento. Arquivos baixados não são exibidos publicamente. Todos os horários estão em UTC. Arquivos baixados Se você baixou um arquivo com ambos downloads rápido e lento, aparecerá duas vezes. Não se preocupe muito, há muitas pessoas baixando de sites vinculados por nós e é extremamente raro ter problemas. No entanto, para se manter seguro, recomendamos usar uma VPN (paga) ou <a %(a_tor)s>Tor</a> (gratuita). Baixei 1984 de George Orwell, a polícia vai bater na minha porta? Você é Anna! Quem é Anna? Temos uma API JSON estável para membros, para obter uma URL de download rápido: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentação no próprio JSON). Para outros casos de uso, como iterar todos os nossos arquivos, criar pesquisas personalizadas e assim por diante, recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Os dados brutos podem ser explorados manualmente <a %(a_explore)s>por meio de arquivos JSON</a>. Nossa lista bruta de torrents também pode ser baixada como <a %(a_torrents)s>JSON</a>. Você tem uma API? Não hospedamos nenhum material protegido por direitos autorais aqui. Somos um mecanismo de busca e, como tal, indexamos apenas metadados que já estão disponíveis publicamente. Ao fazer download dessas fontes externas, sugerimos que você verifique as leis da sua jurisdição com relação ao que é permitido. Não nos responsabilizamos por conteúdo hospedado por terceiros. Se você tiver reclamações sobre o que vê aqui, sua melhor aposta é entrar em contato com o site original. Colocamos regularmente suas alterações em nosso banco de dados. Se você realmente acha que tem uma reclamação válida de DMCA à qual devemos responder, preencha o <a %(a_copyright)s>formulário de reivindicação de DMCA/Direitos autorais</a>. Levamos suas reclamações a sério e entraremos em contato com você o mais breve possível. Como faço para denunciar violação de direitos autorais? Aqui estão alguns livros que têm um significado especial para o mundo das shadow libraries e da preservação digital: Quais são seus livros favoritos? Gostaríamos também de lembrar a todos que todo o nosso código e dados são totalmente de código aberto. Isso é exclusivo para projetos como o nosso — não temos conhecimento de nenhum outro projeto com um catálogo igualmente massivo que também seja totalmente de código aberto. Damos as boas-vindas a qualquer pessoa que pense que executamos mal nosso projeto para pegar nosso código e dados e configurar sua própria shadow library! Não estamos dizendo isso por despeito ou algo assim – realmente achamos que isso seria incrível, já que aumentaria o padrão para todos e preservaria melhor o legado da humanidade. Odeio como você está executando este projeto! Adoraríamos que as pessoas instalassem <a %(a_mirrors)s>espelhos</a> e apoiaremos financeiramente isso. Como posso ajudar? Nós realmente fazemos. Nossa inspiração para coletar metadados é o objetivo de Aaron Swartz de “uma página da web para cada livro já publicado”, para o qual ele criou a <a %(a_openlib)s>Open Library</a>. Esse projeto tem se saído bem, mas nossa posição única nos permite obter metadados que eles não conseguem. Outra inspiração foi nosso desejo de saber <a %(a_blog)s>quantos livros existem no mundo</a>, para que possamos calcular quantos livros ainda temos para salvar. Você coleta metadados? Observe que mhut.org bloqueia determinados intervalos de IP, portanto, pode ser necessária uma VPN. <strong>Android:</strong> clique no menu de três pontos no canto superior direito e selecione “Adicionar à tela inicial”. <strong>iOS:</strong> clique no botão “Compartilhar” na parte inferior e selecione “Adicionar à tela inicial”. Não temos um aplicativo móvel oficial, mas você pode instalar este site como um aplicativo. Você tem um aplicativo móvel? Por favor, envie-os para o <a %(a_archive)s>Internet Archive</a>. Eles os preservarão adequadamente. Como posso doar livros ou outros materiais físicos? Como faço para solicitar livros? <a %(a_blog)s>Blog da Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — atualizações regulares <a %(a_software)s>Software da Anna</a> — nosso código-fonte aberto <a %(a_datasets)s>Conjuntos de dados</a> — sobre os dados <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domínios alternativos Existem mais recursos sobre o Arquivo da Anna? <a %(a_translate)s>Traduza no software da Anna</a> — nosso sistema de tradução <a %(a_wikipedia)s>Wikipedia</a> — mais sobre nós (por favor, ajude a manter esta página atualizada ou crie uma para seu próprio idioma!) Selecione as configurações desejadas, mantenha a caixa de pesquisa vazia, clique em “Pesquisar” e adicione a página aos favoritos usando o recurso de favoritos do seu navegador. Como faço para salvar minhas configurações de pesquisa? Damos as boas-vindas aos pesquisadores de segurança para procurar vulnerabilidades em nossos sistemas. Somos grandes defensores da divulgação responsável. Entre em contato conosco <a %(a_contact)s>aqui</a>. No momento, não podemos conceder recompensas por bugs, exceto por vulnerabilidades que tenham <a %(a_link)s >potencial de comprometer nosso anonimato</a>, para as quais oferecemos recompensas na faixa de US$ 10 mil a US$ 50 mil. Gostaríamos de oferecer um escopo mais amplo para recompensas de bugs no futuro! Observe que os ataques de engenharia social estão fora do escopo. Se você está interessado em segurança ofensiva e deseja ajudar a arquivar o conhecimento e a cultura do mundo, entre em contato conosco. Há muitas maneiras pelas quais você pode ajudar. Você tem um programa de divulgação responsável? Literalmente, não temos recursos suficientes para oferecer downloads em alta velocidade a todas as pessoas do mundo, tanto quanto gostaríamos. Se um benfeitor rico quisesse dar um passo à frente e fornecer isso para nós, seria incrível, mas até lá, estamos dando o nosso melhor. Somos um projeto sem fins lucrativos que mal consegue se sustentar com doações. Por isso implementamos dois sistemas de downloads gratuitos, com nossos parceiros: servidores compartilhados com downloads lentos, e servidores um pouco mais rápidos com lista de espera (para reduzir o número de pessoas baixando ao mesmo tempo). Também temos <a %(a_verification)s>verificação de navegador</a> para nossos downloads lentos, caso contrário, bots e scrapers irão abusar deles, tornando as coisas ainda mais lentas para usuários legítimos. Note que, ao usar o Navegador Tor, você pode precisar ajustar suas configurações de segurança. Na opção mais baixa, chamada “Padrão”, o desafio do Cloudflare turnstile funciona. Mas nas opções mais altas, chamadas “Mais seguro” e “Mais seguro ainda”, o desafio falha. Para arquivos grandes, às vezes downloads lentos podem ser interrompidos no meio. Recomendamos usar um gerenciador de downloads (como o JDownloader) para retomar automaticamente downloads grandes. Por que os downloads lentos são tão lentos? Perguntas frequentes (FAQ) Use o <a %(a_list)s>gerador de lista de torrents</a> para gerar uma lista de torrents que mais precisam de torrent, dentro dos seus limites de espaço de armazenamento. Sim, consulte a página <a %(a_llm)s>dados LLM</a>. A maioria dos torrents contém os arquivos diretamente, o que significa que você pode instruir os clientes de torrent a baixar apenas os arquivos necessários. Para determinar quais arquivos baixar, você pode <a %(a_generate)s>gerar</a> nossos metadados, ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Infelizmente, várias coleções de torrents contêm arquivos .zip ou .tar na raiz, nesse caso você precisará baixar o torrent inteiro antes de poder selecionar arquivos individuais. Ainda não há ferramentas fáceis de usar para filtrar torrents, mas aceitamos contribuições. (Entretanto, temos <a %(a_ideas)s>algumas ideias</a> para o último caso.) Resposta longa: Resposta curta: não facilmente. Tentamos manter o mínimo de duplicação ou sobreposição entre os torrents desta lista, mas isso nem sempre pode ser alcançado e depende muito das políticas das bibliotecas de origem. Para bibliotecas que lançam seus próprios torrents, isso está fora de nosso controle. Para torrents lançados pelo Arquivo da Anna, desduplicamos apenas com base no hash MD5, o que significa que versões diferentes do mesmo livro não são desduplicadas. Sim. Na verdade, são PDFs e EPUBs, mas não têm extensão em muitos de nossos torrents. Existem dois locais onde você pode encontrar os metadados de arquivos torrent, incluindo os tipos/extensões de arquivo: 1. Cada coleção ou lançamento possui seus próprios metadados. Por exemplo, <a %(a_libgen_nonfic)s>torrents Libgen.rs</a> têm um banco de dados de metadados correspondente hospedado no site Libgen.rs. Normalmente criamos links para recursos de metadados relevantes da <a %(a_datasets)s>página do conjunto de dados</a> de cada coleção. 2. Recomendamos <a %(a_generate)s>gerar</a> ou <a %(a_download)s>baixar</a> nossos bancos de dados ElasticSearch e MariaDB. Ele contém um mapeamento para cada registro no arquivo da Anna para seus arquivos torrent correspondentes (se disponíveis), em “torrent_paths” no ElasticSearch JSON. Alguns clientes de torrent não suportam tamanhos de peça grandes, que muitos dos nossos torrents possuem (para os mais novos, não fazemos mais isso — mesmo que seja válido pelas especificações!). Portanto, tente um cliente diferente se encontrar esse problema, ou reclame com os desenvolvedores do seu cliente de torrent. Gostaria de ajudar semeando no torrent, mas não tenho muito espaço em disco. Os torrents são muito lentos; posso baixar os dados diretamente de você? Posso baixar apenas um subconjunto de arquivos, como apenas um idioma ou tópico específico? Como você lida com duplicatas nos torrents? Posso obter a lista de torrents como JSON? Não vejo PDFs ou EPUBs nos torrents, apenas arquivos binários? O que eu faço? Por que meu cliente de torrent não consegue abrir alguns dos seus arquivos torrent / links magnet? Perguntas frequentes sobre torrents Como faço para enviar novos livros? Por favor, veja <a %(a_href)s>este excelente projeto</a>. Você tem um monitor de tempo de atividade? O que é Arquivo da Anna? Torne-se um membro para usar downloads rápidos. Agora aceitamos cartões-presente da Amazon, cartões de crédito e débito, criptomoedas, Alipay e WeChat. Você ficou sem downloads rápidos hoje. Acesso Downloads por hora nos últimos 30 dias. Média por hora: %(hourly)s. Média diária: %(daily)s. Trabalhamos com parceiros para manter nossa coleção acessível e gratuita para todos. Acreditamos que todos tem o direito de acesso ao conhecimento coletivo da Humanidade. E <a %(a_search)s>não a custo de autores</a>. Os conjuntos de dados usados no Arquivo da Anna são completamente abertos e podem ser espelhados em massa usando torrents. <a %(a_datasets)s>Saiba mais…</a> Arquivo de longo prazo Base de dados completa Buscar Livros, artigos, revistas, quadrinhos, registro de biblioteca, metadados, … Todos os nossos <a %(a_code)s>códigos</a> e <a %(a_datasets)s>dados</a> são completamente abertos. <span %(span_anna)s>Arquivo da Anna</span> é um projeto sem fins lucrativos com dois objetivos: <li><strong>Preservação:</strong> Fazer o back up de todo o conhecimento e cultura da humanidade.</li><li><strong>Acesso:</strong> Tornar esse conhecimento e cultura disponíveis a qualquer pessoa no mundo.</li> Nós temos a maior coleção do mundo de dados de texto de alta qualidade. <a %(a_llm)s>Mais informações...</a> Dados para treinamento de LLM 🪩 Mirrors: recrutando voluntários Se administra um processador de pagamentos anônimo de alto risco, entre em contato conosco. Também procuramos pessoas que desejam colocar pequenos anúncios de bom gosto. Todos os rendimentos vão para nossos esforços de preservação. Preservação Estimamos que já preservamos cerca de <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% dos livros do mundo</a>. Preservamos livros, artigos, quadrinhos, revistas e muito mais, trazendo esses materiais de várias <a href="https://pt.wikipedia.org/wiki/Shadow_library">bibliotecas-sombra</a>, bibliotecas oficiais e outras coleções juntas em um só lugar. Todos esses dados são preservados para sempre, facilitando a duplicação em massa — usando torrents — resultando em muitas cópias em todo o mundo. Algumas bibliotecas-sombra já fazem isso sozinhas (por exemplo, Sci-Hub, Library Genesis), enquanto o Arquivo da Anna “libera” outras bibliotecas que não oferecem distribuição em massa (por exemplo, Z-Library) ou que não são bibliotecas-sombra (por exemplo, Internet Archive, DuXiu). Essa ampla distribuição, combinada ao código aberto, torna nosso website resiliente contra quedas, e garante a preservação a longo prazo do conhecimento e da cultura da humanidade. Saiba mais sobre <a href="/datasets">nossos datasets</a>. Caso você seja um <a %(a_member)s>membro</a>, não é necessário a verificação do browser. 🧬 &nbsp;SciDB é uma continuação do Sci-Hub. SciDB Abrir DOI Sci-Hub <a %(a_paused)s>pausou</a> o upload de novos artigos. Acesso direto a %(count)s artigos acadêmicos 🧬 SciDB é uma continuação do Sci-Hub, com sua interface familiar e visualização direta de PDFs. Digite seu DOI para visualizar. Temos a coleção completa do Sci-Hub, bem como novos artigos. A maioria pode ser visualizada diretamente com uma interface familiar, semelhante ao Sci-Hub. Alguns podem ser baixados de fontes externas; nesse caso, mostramos links para eles. Você pode ajudar enormemente semeando torrents. <a %(a_torrents)s>Saiba mais…</a> >%(count)s semeadores <%(count)s semeadores %(count_min)s–%(count_max)s semeadores 🤝 Procurando voluntários Como um projeto sem fins lucrativos e de código aberto, estamos sempre procurando pessoas para ajudar. Downloads de IPFS Lista por %(by)s, criada <span %(span_time)s>%(time)s</span> Salvar ❌ Algo deu errado. Por favor tente novamente. ✅ Salvo. Por favor recarregue a página. A lista está vazia. editar Para adicionar ou remover desta lista, encontre um arquivo e abra a aba "Listas". Lista Como podemos ajudar Remoção de copias (deduplicação) Extração de texto e metadados OCR (Reconhecimento ótico de caracteres) Podemos fornecer acesso em alta velocidade às nossas coleções completas, bem como a coleções não lançadas. Este é um acesso em nível empresarial que podemos fornecer por doações na faixa de dezenas de milhares de dólares. Também estamos dispostos a trocar isso por coleções de alta qualidade que ainda não temos. Podemos reembolsá-lo se você puder nos fornecer melhoramento de nossos dados, como: Apoie o arquivamento a longo prazo do conhecimento humano, enquanto obtém dados melhores para o seu modelo! <a %(a_contact)s>Entre em contato</a> para discutir como podemos trabalhar juntos. É bem compreendido que os LLMs prosperam com dados de alta qualidade. Temos a maior coleção de livros, artigos, revistas, etc. do mundo, que são algumas das fontes de texto de mais alta qualidade. Dados para LLM Escala e alcance únicos Nossa coleção contém mais de cem milhões de arquivos, incluindo periódicos acadêmicos, livros didáticos e revistas. Alcançamos essa escala combinando grandes repositórios existentes. Algumas de nossas coleções de origem já estão disponíveis em massa (Sci-Hub e partes do Libgen). Outras fontes nós mesmos liberamos. <a %(a_datasets)s>Datasets</a> mostra uma visão geral completa. Nossa coleção inclui milhões de livros, artigos e revistas de antes da era dos e-books. Grandes partes dessa coleção já foram convertidas para texto e já têm pouca sobreposição interna. Continuar Se perdeu sua chave, <a %(a_contact)s>entre em contato conosco</a> e forneça o máximo de informações possível. Talvez seja necessário criar temporariamente uma nova conta para entrar em contato conosco. Por favor, <a %(a_account)s>faça login</a> para visualizar esta página.</a> Para evitar que bots de spam criem muitas contas, precisamos verificar seu navegador primeiro. Se você cair em um loop infinito, recomendamos instalar o <a %(a_privacypass)s>Privacy Pass</a>. Também pode ajudar desativar bloqueadores de anúncios e outras extensões do navegador. Entrar / Registrar O Arquivo da Anna está temporariamente fora do ar para manutenção. Por favor, volte em uma hora. Autor alternativo Descrição alternativa Edição alternativa Extensão alternativa Nome de arquivo alternativo Editora alternativa Título alternativo data de lançamento público Leia mais… descrição Busque no Acervo da Anna um número CADAL SSNO Busque no Acervo da Anna um SSID DuXiu Busque no Acervo da Anna um número DuXiu DXID Busque no Acervo da Anna um ISBN Busque no Acervo da Anna um número OCLC (WorldCat) Busque no Acervo da Anna um ID Open Library Visualizador online do Arquivo da Anna %(count)s páginas afetadas Após o download: Uma versão melhor desse arquivo pode estar disponível em %(link)s Downloads torrent em massa coleção Use ferramentas online para converter entre formatos. Ferramentas de conversão recomendadas: %(links)s Para arquivos grandes, recomendamos o uso de um gerenciador de downloads para evitar interrupções. Gerenciadores de download recomendados: %(links)s Índice de eBooks EBSCOhost (apenas especialistas) (clique também em “GET” no topo) (clique em “GET” no topo) Downloads externos Você tem %(remaining)s sobrando hoje. Obrigado por ser um membro! ❤️ Você ficou sem downloads rápidos por hoje. Você baixou esse arquivo recentemente. Links continuam válidos por um tempo. Torne-se um <a %(a_membership)s>membro</a> para apoiar a preservação a longo prazo de livros, artigos e mais. Para mostrar nossa gratidão pelo seu apoio, você ganha downloads rápidos. ❤️ 🚀 Downloads rápidos 🐢 Downloads lentos Emprestar do Internet Archive Gateway IPFS #%(num)d (talvez seja necessário tentar várias vezes com IPFS) Libgen.li Libgen.rs Ficção Libgen.rs Não ficção seus anúncios são conhecidos por conter software malicioso, então use um bloqueador de anúncios ou não clique em anúncios “Enviar para Kindle” da Amazon “Enviar para Kobo/Kindle” do djazz MagzDB ManualsLib Nexus/STC (Os arquivos Nexus/STC podem não serem confiáveis para download) Nenhum download encontrado. Todas as opções de download contêm o mesmo arquivo e devem ser seguras para uso. Dito isso, tenha sempre cuidado ao baixar arquivos da internet, principalmente de sites externos ao Acervo da Anna. Por exemplo, certifique-se de manter seus dispositivos atualizados. (sem redirecionamento) Abrir no nosso visualizador (abrir no visualizador) Opção #%(num)d: %(link)s %(extra)s Encontre o registro original em CADAL Pesquise manualmente no DuXiu Encontre o registro original em ISBNdb Procurar o registro original no WorldCat Encontre o registro original na Open Library Procure em vários outros bancos de dados pelo ISBN (impressão desabilitada, somente para membros) PubMed Você precisará de um leitor de ebook ou PDF para abrir o arquivo, dependendo do formato do arquivo. Leitores de eBooks recomendados: %(links)s Acervo da Anna 🧬 SciDB Sci-Hub: %(doi)s (o DOI associado pode não estar disponível no Sci-Hub) Você pode enviar arquivos PDF e EPUB para o seu eReader Kindle ou Kobo. Ferramentas recomendadas: %(links)s Mais informações na<a %(a_slow)s>FAQ</a>. Apoie autores e bibliotecas Se você gostou e pode pagar, considere comprar o original ou apoiar os autores diretamente. Se estiver disponível na sua biblioteca local, considere pegá-lo emprestado gratuitamente lá. Downloads do servidor parceiro temporariamente indisponíveis para este arquivo. torrent De parceiros confiáveis. Z-Library Z-Library no Tor (precisa do navegador Tor) mostrar downloads externos <span class="font-bold">❌ Este arquivo pode ter problemas e foi ocultado de uma biblioteca fonte. </span> Às vezes por causa de um pedido de um detentor de direitos autorais, às vezes porque outro arquivo alternativo está disponível, mas também porque pode haver um problema com o próprio arquivo. Você pode conseguir baixá-lo, mas sugerimos que você procure outro arquivo. Mais detalhes: Se você ainda quiser baixar este arquivo, certifique-se de usar apenas programas confiáveis e atualizados para abri-lo. comentários de metadados AA: Busque no Acervo da Anna por “%(name)s” Explorador de códigos: Ver no Explorador de Códigos “%(name)s” URL: Site: Se você tem esse arquivo e ele ainda não está disponível no Acervo da Anna, considere <a %(a_request)s>enviá-lo</a>. Arquivo de Controle de Empréstimo Digital (Controlled Digital Lending) do Internet Archive "%(id)s" Esse é um registro de um arquivo do Internet Archive, não é um arquivo que pode ser baixado diretamente. Você pode tentar emprestar o livro (link abaixo), ou usar essa URL quando <a %(a_request)s>pedir por um arquivo</a>. Melhorar metadados Registro de metadados CADAL SSNO %(id)s Esse é um registro de metadados, não um arquivo disponível para download. Você pode usar essa URL quando <a %(a_request)s>pedir por um arquivo</a>. Registro de metadados DuXiu SSID %(id)s Registro de metadados do ISBNdb %(id)s Registro de metadados MagzDB ID %(id)s Registro de metadados Nexus/STC ID %(id)s Registro de metadados do número OCLC (WorldCat) %(id)s Registro de metadados da Open Library %(id)s Arquivo do Sci-Hub "%(id)s" Nenhum resultado “%(md5_input)s” não foi encontrado em nosso banco de dados. Adicionar comentário (%(count)s) Você pode obter o md5 a partir do URL, por exemplo. MD5 de uma versão melhor deste arquivo (se aplicável). Preencha isso se houver outro arquivo que seja estritamente semelhantemente a este arquivo (mesma edição, mesma extensão de arquivo, se você puder encontrar um), que as pessoas devem usar em vez deste arquivo. Se você souber de uma versão melhor deste arquivo fora do Arquivo da Anna, então, por favor, <a %(a_upload)s>faça o upload</a>. Algo deu errado. Por favor, recarregue a página e tente novamente. Você deixou um comentário. Pode levar um minuto para ele aparecer. Por favor, use o <a %(a_copyright)s>formulário de reivindicação de DMCA / Direitos Autorais</a>. Descreva o problema (obrigatório) Se este arquivo tiver ótima qualidade, você pode colocar suas ideas sobre ele aqui! Caso contrário, use o botão “Relatar problema no arquivo”. Ótima qualidade do arquivo (%(count)s) Qualidade do arquivo Aprenda como <a %(a_metadata)s>melhorar os metadados</a> deste arquivo você mesmo. Descrição do problema Por favor, <a %(a_login)s>faça login</a>. Adorei este livro! Ajude a comunidade pontuando a qualidade deste arquivo! 🙌 Algo deu errado. Por favor, recarregue a página e tente novamente. Reportar problema no arquivo (%(count)s) Obrigado por enviar seu reporte. Ele será exibido nesta página e revisado manualmente por nós (até termos um sistema de moderação adequado). Deixar um comentário Enviar reporte O que há de errado com este arquivo? Empréstimos (%(count)s) Comentários (%(count)s) Downloads (%(count)s) Explore os metadados (%(count)s) Listas (%(count)s) Estatísticas (%(count)s) Para obter informações sobre este arquivo específico, confira seu <a %(a_href)s>arquivo JSON</a>. Este é um arquivo gerenciado pela biblioteca <a %(a_ia)s>IA’s Controlled Digital Lending</a> e indexado pelo Acervo da Anna para buscas. Para informações sobre os diversos bancos de dados que compilamos, veja a <a %(a_datasets)s>página de Bancos de Dados</a>. Metadados do registro vinculado Melhorar metadados na Open Library Um “MD5 do arquivo” é um algoritmo criptográfico que é calculado a partir do conteúdo do arquivo e é o único aceitável com base nesse conteúdo. Todas as bibliotecas-sombra que indexamos aqui usam principalmente MD5s para identificar arquivos. Um arquivo pode aparecer em várias bibliotecas-sombra. Para informações sobre os diversos datasets que compilamos, veja a <a %(a_datasets)s>página de Datasets</a>. Reportar qualidade do arquivo Total de downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadados tchecos %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Livros %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Aviso: múltiplos registros vinculados: Ao consultar um livro no Arquivo da Anna, você pode ver vários campos: título, autor, editora, edição, ano, descrição, nome do arquivo e muito mais. Todas essas informações são chamadas de <em>metadados</em>. Como combinamos livros de várias <em>bibliotecas de origem</em>, mostramos todos os metadados disponíveis nessa biblioteca de origem. Por exemplo, para um livro obtido na Library Genesis, mostraremos o título do banco de dados da Library Genesis. Às vezes, um livro está presente em <em>múltiplas</em> bibliotecas de origem, que podem ter diferentes campos de metadados. Nesse caso, simplesmente mostramos a versão mais longa de cada campo, pois essa, esperamos, contém as informações mais úteis! Ainda mostraremos os outros campos abaixo da descrição, por exemplo, como "título alternativo" (mas apenas se forem diferentes). Também extraímos <em>códigos</em> como identificadores e classificadores da biblioteca de origem. <em>Identificadores</em> representam exclusivamente uma edição específica de um livro; exemplos são ISBN, DOI, ID da Open Library, ID do Google Books ou ID da Amazon. <em>Classificadores</em> agrupam vários livros semelhantes; exemplos são Dewey Decimal (DCC), UDC, LCC, RVK ou GOST. Às vezes, esses códigos estão explicitamente vinculados nas bibliotecas de origem, e às vezes podemos extraí-los do nome do arquivo ou da descrição (principalmente ISBN e DOI). Podemos usar identificadores para encontrar registros em <em>coleções apenas de metadados</em>, como OpenLibrary, ISBNdb ou WorldCat/OCLC. Há uma aba específica de <em>metadados</em> em nosso mecanismo de busca se você quiser navegar por essas coleções. Usamos registros correspondentes para preencher campos de metadados ausentes (por exemplo, se um título estiver faltando), ou, por exemplo, como "título alternativo" (se houver um título existente). Para ver exatamente de onde vieram os metadados de um livro, veja a aba <em>“Detalhes técnicos”</em> na página do livro. Ela tem um link para o JSON desse livro, com apontadores para o JSON dos registros originais. Para mais informações, veja as seguintes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Busca (aba de metadados)</a>, <a %(a_codes)s>Explorador de Códigos</a> e <a %(a_example)s>Exemplo de JSON de metadados</a>. Finalmente, todos os nossos metadados podem ser <a %(a_generated)s>gerados</a> ou <a %(a_downloaded)s>baixados</a> como bancos de dados ElasticSearch e MariaDB. Contexto Você pode ajudar na preservação de livros melhorando os metadados! Primeiro, leia o contexto sobre metadados no Arquivo da Anna e, em seguida, aprenda como melhorar os metadados através da vinculação com a Open Library, e ganhe uma assinatura gratuita no Arquivo da Anna. Melhorar metadados Então, se encontrar um arquivo com metadados ruins, como deve corrigi-lo? Você pode ir à biblioteca de origem e seguir seus procedimentos para corrigir os metadados, mas o que fazer se um arquivo estiver presente em várias bibliotecas de origem? Há um identificador tratado de forma especial no Arquivo da Anna. <strong>O campo annas_archive md5 na Open Library sempre substitui todos os outros metadados!</strong> Vamos voltar um pouco e aprender sobre Open Library. A Open Library foi fundada em 2006 por Aaron Swartz com o objetivo de "uma página web para cada livro já publicado". É uma espécie de Wikipedia para metadados de livros: todos podem editá-la, é licenciada livremente e pode ser baixada em massa. É um banco de dados de livros que está mais alinhado com nossa missão — na verdade, o Arquivo da Anna foi inspirado pela visão e vida de Aaron Swartz. Em vez de reinventar a roda, decidimos direcionar nossos voluntários para a Open Library. Se você vir um livro com metadados incorretos, pode ajudar da seguinte maneira: Observe que isso só funciona para livros, não para artigos acadêmicos ou outros tipos de arquivos. Para outros tipos de arquivos, ainda recomendamos encontrar a biblioteca fonte. Pode levar algumas semanas para que as mudanças sejam incluídas no Arquivo da Anna, pois precisamos baixar o último dump de dados da Open Library e regenerar nosso índice de busca.  Vá para o <a %(a_openlib)s>site da Open Library</a>. Encontre o registro correto do livro. <strong>AVISO:</strong> certifique-se de selecionar a <strong>edição</strong> correta. Na Open Library, você tem "obras" e "edições". Uma "obra" poderia ser "Harry Potter e a Pedra Filosofal". Uma "edição" poderia ser: A primeira edição de 1997 publicada pela Bloomsbury com 256 páginas. A edição de brochura de 2003 publicada pela Raincoast Books com 223 páginas. A tradução polonesa de 2000 “Harry Potter I Kamie Filozoficzn” pela Media Rodzina com 328 páginas. Todas essas edições têm ISBNs e conteúdos diferentes, então certifique-se de selecionar a correta! Edite o registro (ou crie um se não existir) e adicione o máximo de informações úteis que puder! Você já está aqui, então aproveite para tornar o registro realmente incrível. Em “Números de ID”, selecione “Arquivo da Anna” e adicione o MD5 do livro do Arquivo da Anna. Este é o longo conjunto de letras e números após “/md5/” na URL. Tente encontrar outros arquivos no Arquivo da Anna que também correspondam a este registro e adicione-os também. No futuro, podemos agrupar esses como duplicatas na página de busca do Arquivo da Anna. Quando terminar, anote a URL que você acabou de atualizar. Depois de atualizar pelo menos 30 registros com os MD5s do Arquivo da Anna, envie-nos um <a %(a_contact)s>email</a> com a lista. Nós lhe daremos uma assinatura gratuita do Arquivo da Anna, para que você possa fazer esse trabalho mais facilmente (e como um agradecimento pela sua ajuda). Essas edições precisam ser de alta qualidade e adicionar uma quantidade substancial de informações, caso contrário, sua solicitação será rejeitada. Sua solicitação também será rejeitada se alguma das edições for revertida ou corrigida pelos moderadores da Open Library. Vinculação com Open Library Se você se envolver significativamente no desenvolvimento e operações do nosso trabalho, podemos discutir o compartilhamento de mais da receita de doações com você, para que você possa utilizá-la conforme necessário. Só pagaremos pela hospedagem depois que você tiver tudo configurado e demonstrado que é capaz de manter o arquivo atualizado com as atualizações. Isso significa que você terá que pagar pelos primeiros 1-2 meses do próprio bolso. Seu tempo não será compensado (e nem o nosso), já que este é um trabalho puramente voluntário. Estamos dispostos a cobrir despesas de hospedagem e VPN, inicialmente até $200 por mês. Isso é suficiente para um servidor de busca básico e um proxy protegido por DMCA / direitos autorais. Despesas de hospedagem Por favor <strong>não nos contate</strong> para pedir permissão ou para perguntas básicas. Ações falam mais alto que palavras! Todas as informações estão disponíveis, então vá em frente e configure seu espelho. Sinta-se à vontade para postar tickets ou solicitações de mesclagem no nosso Gitlab quando encontrar problemas. Podemos precisar construir alguns recursos específicos para espelhos com você, como a mudança de “Arquivo da Anna” para o nome do seu site, (inicialmente) desativar contas de usuário, ou linkar de volta para nosso site principal a partir das páginas de livros. Uma vez que seu espelho esteja funcionando, por favor, entre em contato conosco. Adoraríamos revisar sua OpSec, e uma vez que isso esteja sólido, vamos linkar para o seu espelho e começar a trabalhar mais de perto com você. Agradecemos antecipadamente a todos que estão dispostos a contribuir dessa forma! Não é para os fracos de coração, mas solidificaria a longevidade da maior biblioteca verdadeiramente aberta da história humana. Começando Para aumentar a resiliência do Arquivo da Anna, estamos procurando voluntários para operar espelhos. Sua versão é claramente distinguida como um espelho, por exemplo, “Arquivo do Bob, um espelho do Arquivo da Anna”. Você está disposto a assumir os riscos associados a este trabalho, que são significativos. Você tem um profundo entendimento da segurança operacional necessária. O conteúdo destes <a %(a_shadow)s>posts</a> <a %(a_pirate)s>aqui</a> é auto evidente para você. Inicialmente, não daremos acesso aos downloads do nosso servidor parceiro, mas se tudo correr bem, podemos compartilhar isso com você. Você executa a base de código aberto do Arquivo da Anna e atualiza regularmente tanto o código quanto os dados. Você está disposto a contribuir para nossa <a %(a_codebase)s>base de código</a> — em colaboração com nossa equipe — para que isso aconteça. Estamos procurando por isso: Espelhos: chamada para voluntários Fazer outra doação. Ainda não há doações. <a %(a_donate)s>Quero fazer minha primeira doação.</a> Os detalhes de doações não são exibidos publicamente. Minhas doações 📡 Para fazer um mirror em massa da nossa coleção, cheque as páginas de <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>. Downloads do seu endereço IP nas últimas 24 horas: %(count)s. 🚀 Para ter downloads mais rápidos, <a %(a_membership)s>vire um membro</a>. Download de site parceiro Sinta-se à vontade para continuar navegando no arquivo da Anna em uma guia diferente enquanto espera (se o seu navegador suportar a atualização das guias de fundo). Sinta-se à vontade para aguardar o carregamento de várias páginas de download ao mesmo tempo (mas baixe apenas um arquivo ao mesmo tempo, por servidor). Após obter um link de download, ele é válido por várias horas. Obrigado por esperar, isso mantém o site acessível gratuitamente para todos! 😊 <a %(a_main)s>&lt; Todos os links para download deste arquivo</a> ❌ Downloads lentos não estão disponíveis por meio de VPNs da Cloudflare ou de outros endereços IP da Cloudflare. ❌ Downloads lentos estão disponíveis apenas no site oficial. Visita %(websites)s. <a %(a_download)s>📚 Baixar agora</a> Para poder dar a todos a oportunidade de baixar arquivos gratuitamente, você precisa esperar antes de poder baixar este arquivo. Por favor, aguarde <span %(span_countdown)s>%(wait_seconds)s</span> segundos para baixar este arquivo. Aviso: houve muitos downloads do seu endereço IP nas últimas 24 horas. Os downloads podem ser mais lentos que o normal. Se você estiver usando uma VPN, uma conexão de Internet compartilhada ou se seu ISP compartilhar IPs, este aviso pode ser devido a isso. Guardar ❌ Algo deu errado. Por favor tente novamente. ✅ Salvo. Por favor atualize a página. Altere o seu nome de perfil. O seu identificador (a parte a depois de"#") não pode ser alterada. Perfil criado <span %(span_time)s>%(time)s</span> editar Listas Crie uma nova lista escolhendo um arquivo e abrindo a aba "Listas". Nenhuma lista ainda Perfil não encontrado. Perfil Atualmente, não podemos atender pedidos de livros. Não nos envie por e-mail seus pedidos de livros. Por favor, faça suas solicitações nos fóruns Z-Library ou Libgen. Registro no Arquivo da Anna DOI: %(doi)s Download SciDB Nexus/STC Nenhuma visualização disponível ainda. Baixe o arquivo do <a %(a_path)s>Arquivo da Anna</a>. Para apoiar a acessibilidade e a preservação a longo prazo do conhecimento humano, torne-se um <a %(a_donate)s>membro</a>. Como bônus, 🧬 SciDB carrega mais rápido para os membros, sem limites. Não está funcionando? Tente <a %(a_refresh)s>atualizar</a>. Sci-Hub Adicione um campo de pesquisa específico Descrições de pesquisa e comentários de metadados Ano de publicação Avançado Acesso Conteúdo Exibir Lista Tabela Tipo de arquivo Idioma Ordene por Maior Mais relevante Mais recente (tamanho do arquivo) (publicado gratuitamente) (ano de publicação) Mais antigo Aleatório Menor Fonte raspado e de código aberto por AA Empréstimo digital (%(count)s) Artigos de Jornal (%(count)s) Encontramos correspondências em: %(in)s. Você pode consultar a URL encontrada lá ao <a %(a_request)s>solicitar um arquivo</a>. Metadados (%(count)s) Para explorar o índice de busca por códigos, use o <a %(a_href)s>Explorador de Códigos</a>. O índice de busca é atualizado mensalmente. Atualmente, ele possui arquivos até %(last_data_refresh_date)s. Para informações mais técnicas, veja a página do %(link_open_tag)sdatasets</a>. Excluir Apenas Incluir Não verificado mais… Próximo … Anterior Esse index de busca atualmente inclui metadados da biblioteca de empréstimo digital do Internet Archive. <a %(a_datasets)s>Mais sobre nossos bancos de dados</a>. Para mais bibliotecas de empréstimo digital, veja <a %(a_wikipedia)s>Wikipedia</a> e a <a %(a_mobileread)s>Wiki do MobileRead</a>. Para reclamações de direitos autorais / DMCA <a %(a_copyright)s>clique aqui</a>. Tempo de download Erro durante a pesquisa. Tente <a %(a_reload)s>atualizar a página</a>. Caso o problema persista, por favor nos envie um email em %(email)s. Download rápido Na verdade, qualquer pessoa pode ajudar a preservar esses arquivos propagando nossa <a %(a_torrents)s>lista unificada de torrents</a>. ➡️ Às vezes isso acontece incorretamente quando o servidor de busca está lento. Nesses casos, <a %(a_attrs)s>recarregar</a> pode ajudar. ❌ Este arquivo pode ter problemas. Procurando por artigos? Esse index de procura atualmente inclui metadados do ISBNdb e Open Library. <a %(a_datasets)s>Mais sobre nossos bancos de dados</a>. Tem muitas, muitas fontes de metadados para obras escritas ao redor do mundo. <a %(a_wikipedia)s>Essa página da Wikipedia</a> é um bom começo, mas se você quiser conhecer outras boas listas, por favor nos deixe saber. Para metadados, mostramos os registros originais. Não fazemos nenhuma fusão de registros. Nós atualmente temos o catálogo aberto de livros, artigos e outros trabalhos escritos mais completo do mundo. Nós espalhamos Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e mais</a>. <span %(classname)s>Nenhum arquivo encontrado.</span> Tente usar termos a menos ou diferentes na busca e nos filtros. Resultados %(from)s-%(to)s (%(total)s total) Caso você encontre outras "shadow libraries" que devemos espelhar, ou caso tenha qualquer dúvida, por favor nos contate em %(email)s. %(num)d correspondências parciais %(num)d+ correspondências parciais Digite na caixa para procurar por arquivos em bibliotecas de empréstimo digital. Digite na caixa para procurar em nosso catálogo de %(count)s arquivos de download direto, os quais nós <a %(a_preserve)s>preservamos para sempre</a>. Digite na caixa para buscar. Digite na caixa para buscar em nosso catálogo de %(count)s trabalhos acadêmicos e artigos de jornal, os quais nós <a %(a_preserve)s>preservamos para sempre</a>. Digite na caixa para procurar por metadados de bibliotecas. Isso pode ser útil quando estiver <a %(a_request)s>solicitando um arquivo</a>. Dica: use atalhos do teclado "/" (foco na busca), "enter" (buscar), "j" (para cima), "k"(para baixo) para uma navegação mais rápida. Estes são registros de metadados, e<span %(classname)s>não</span> arquivos para download. Configurações de busca Buscar Empréstimo digital Download Artigos de jornal Metadados Nova busca %(search_input)s - Busca A pesquisa demorou muito, o que significa que você poderá ver resultados imprecisos. Às vezes, <a %(a_reload)s>recarregar</a> a página ajuda. A pesquisa demorou muito, o que é comum em consultas amplas. As contagens do filtro podem não ser precisas. Para envios grandes (acima de 10,000 arquivos) que não foram aceitos pela Libgen ou Z-Library, por favor nos contate por %(a_email)s. Para o Libgen.li, certifique-se de primeiro fazer login no <a %(a_forum)s>fórum deles</a> com o nome de usuário %(username)s e senha %(password)s, e depois retornar à <a %(a_upload_page)s>página de upload</a> deles. Por enquanto, sugerimos o upload de novos livros para as bifurcações do Library Genesis. Aqui está um <a %(a_guide)s>guia prático</a>. Observe que ambas as bifurcações que indexamos neste site são extraídas desse mesmo sistema de upload. Para pequenos uploads (até 10.000 arquivos), por favor, envie-os tanto para %(first)s quanto para %(second)s. Como alternativa, você pode carregá-los na Z-Library <a %(a_upload)s>aqui</a>. Para fazer upload de trabalhos acadêmicos, faça upload também (além do Library Genesis) para <a %(a_stc_nexus)s>STC Nexus</a>. Eles são a melhor shadow library para novos artigos. Ainda não os integramos, mas iremos em algum momento. Você pode usar o <a %(a_telegram)s>bot de upload no Telegram</a> ou entrar em contato com o endereço listado na mensagem fixada se tiver muitos arquivos para enviar dessa forma. <span %(label)s>Trabalho voluntário intenso (recompensas de USD$50-USD$5.000):</span> se você puder dedicar muito tempo e/ou recursos à nossa missão, adoraríamos trabalhar mais de perto com você. Eventualmente, você pode se juntar a nossa equipe interna. Embora tenhamos um orçamento apertado, podemos conceder <span %(bold)s>💰 recompensas monetárias</span> para os trabalhos mais intensos. <span %(label)s>Trabalho leve de voluntariado:</span> se você só pode dedicar algumas horas aqui e ali, ainda há muitas maneiras de ajudar. Recompensamos voluntários consistentes com <span %(bold)s>🤝 assinaturas no Arquivo da Anna</span>. O Arquivo da Anna depende de voluntários como você. Acolhemos todos os níveis de comprometimento e temos duas principais categorias de ajuda que estamos procurando: Se você não puder doar seu tempo, ainda pode nos ajudar muito <a %(a_donate)s>doando dinheiro</a>, <a %(a_torrents)s>semeando nossos torrents</a>, <a %(a_uploading)s>fazendo o upload de livros</a> ou <a %(a_help)s>contando aos seus amigos sobre o Arquivo da Anna</a>. <span %(bold)s>Empresas:</span> oferecemos acesso direto e de alta velocidade às nossas coleções em troca de doações em nível empresarial ou em troca de novas coleções (por exemplo, novos scans, datasets OCR, enriquecimento de nossos dados). <a %(a_contact)s>Entre em contato</a> se for o seu caso. Veja também nossa <a %(a_llm)s>página de LLM</a>. Recompensas Estamos sempre procurando por pessoas com sólidas habilidades em programação ou segurança ofensiva dispostas a colaborar. Você pode fazer uma diferença significativa na preservação do legado da humanidade. Como agradecimento, oferecemos status de membro por contribuições sólidas. Como um grandíssimo obrigado, oferecemos recompensas monetárias para tarefas particularmente importantes e difíceis. Isto não deve ser visto como um substituto para um emprego, mas é um incentivo extra e pode ajudar com eventuais custos. Grande parte do nosso código é aberto e pediremos o mesmo do seu código para concedermos a remuneração. Podemos discutir exceções de forma individual, se necessário. As recompensas são concedidas à primeira pessoa que completar uma tarefa. Sinta-se à vontade para comentar em um ticket de recompensa para que outros saibam que você está trabalhando em algo, para que possam esperar ou entrar em contato com você para formar uma equipe. Mas esteja ciente de que outros ainda são livres para trabalhar nisso também e tentar vencê-lo. No entanto, não concedemos recompensas por trabalho descuidado. Se duas submissões de alta qualidade forem feitas próximas uma da outra (dentro de um ou dois dias), podemos optar por conceder recompensas a ambas, a nosso critério, por exemplo, 100%% para a primeira submissão e 50%% para a segunda submissão (totalizando 150%%). Para as recompensas maiores (especialmente recompensas de scraping), entre em contato conosco quando você tiver completado ~5%% dela, e estiver confiante de que seu método escalará para o marco completo. Você terá que compartilhar seu método conosco para que possamos dar feedback. Além disso, dessa forma podemos decidir o que fazer se houver várias pessoas chegando perto de uma recompensa, como potencialmente concedê-la a várias pessoas, encorajar as pessoas a formarem equipes, etc. AVISO: as tarefas de alta recompensa são <span %(bold)s>difíceis</span> — pode ser sábio começar com as mais fáceis. Vá para nossa <a %(a_gitlab)s>lista de issues no Gitlab</a> e classifique por “Label priority”. Isso mostra aproximadamente a ordem das tarefas que nos importam. Tarefas sem recompensas explícitas ainda são elegíveis para status de membro, especialmente aquelas marcadas como “Accepted” e “Anna’s favorite”. Você pode querer começar com um “Starter project”. Voluntariado leve Agora também temos um canal Matrix sincronizado em %(matrix)s. Se você tem algumas horas livres, pode ajudar de várias maneiras. Certifique-se de participar do <a %(a_telegram)s>chat de voluntários no Telegram</a>. Como forma de agradecimento, geralmente oferecemos 6 meses de “Arquivista Afortunado” para marcos básicos, e mais para trabalho voluntário contínuo. Todos os marcos exigem trabalho de alta qualidade — trabalho descuidado nos prejudica mais do que ajuda e será rejeitado. Por favor, <a %(a_contact)s>envie-nos um e-mail</a> quando atingir um marco. %(links)s links ou capturas de tela de solicitações que você atendeu. Atendendo solicitações de livros (ou artigos, etc) nos fóruns do Z-Library ou do Library Genesis. Não temos nosso próprio sistema de solicitações de livros, mas espelhamos essas bibliotecas, então melhorá-las também melhora o Arquivo da Anna. Marco Tarefa Depende da tarefa. Pequenas tarefas postadas no nosso <a %(a_telegram)s>chat de voluntários no Telegram</a>. Geralmente para assinaturas, às vezes para pequenas recompensas. Pequenas tarefas postadas em nosso grupo de chat de voluntários. Certifique-se de deixar um comentário nos problemas que você resolver, para que outros não dupliquem seu trabalho. %(links)s links de registros que você melhorou. Você pode usar a <a %(a_list)s>lista de problemas de metadados aleatórios</a> como ponto de partida. Melhore os metadados os <a %(a_metadata)s>vinculando</a> com a Open Library. Esses devem mostrar você informando alguém sobre o Arquivo da Anna, e essa pessoa agradecendo. %(links)s links ou capturas de tela. Divulgando o Arquivo da Anna. Por exemplo, recomendando livros no AA, compartilhando links para nossas postagens no blog ou, de modo geral, direcionando pessoas para nosso site. Traduzir completamente um idioma (se ele não estava quase concluído.) <a %(a_translate)s>Traduzindo</a> o site. Link para o histórico de edições mostrando que você fez contribuições significativas. Melhore a página da Wikipédia do Arquivo da Anna no seu idioma. Inclua informações da página da Wikipédia do AA em outros idiomas, e do nosso site e blog. Adicione referências ao AA em outras páginas relevantes. Voluntariado & Recompensas 