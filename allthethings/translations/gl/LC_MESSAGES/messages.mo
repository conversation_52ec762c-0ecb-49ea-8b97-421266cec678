��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b c  sd K  �f ,   #h   Ph �  di �  :k j   m ,  kn Y   �o r   �o F   ep l   �p �  q l  �r �   8t �  $u �   w 
  x �  z /  | �   2~    �  � G   Ă �   � W   �   F�    N� I  n� F   �� -   ��    -�    <� @   V� %   ��    �� 0   Љ    � .    �    O� G   h� @  �� 	  � L   �� 7   H�    ��    ��    ��    ��    ��    ߍ 	   �    ��    �     �    /�    5� 	   Q� 
   [�    i�    u�    ��    �� �  Ȏ ^  N� �   �� ,   F� E  s� �   �� �   U� �   P�    � �   :�    ٗ �   �� $   �� A  �    $� �  D� =   ՛ j   �    ~� Z  �� \  � �  C�   �    +� @   A� N   �� �   Ѣ 0   ~� (   �� o   أ 8   H� ?   �� &   ��    � w   � �  h� 8   /� �   h� �   � =   �� 1  � *   � $  I� �   n� �   � N   �� �   � �   ̭ F   |� �   î �   �� T   o� G   İ r   �    � �   ��   � F   5�    |� �   �� �   9�    %� v  -� �   �� �   P� v  O� �   ƹ d  ��   � R   1� P   �� ^   տ �   4� w   �� Y   6� 2   �� �   �� \   b� F   ��    � |   � Q   �� �   �� �   ��    U�   m� �   r� B  q� O  �� �   � �   �� ;  t� �   �� �   f� �   � i  �� �   #�   ��    �� �   �� �   �� �   � q  ?� �   �� �   7� �   �� �  x� �   l� C  �� 5  5� "   k� a   �� �   �� H   �� t  �� �   J� f   � y   o� .   �� E  � �   ^� �  �    �� W   �� �   !� �   �� 
  �� �   �� �  �� �  ;� 1   �� q   � �  �� #  '� �  K� -  ��   %� q   8� �   ��    ?� �   H� >  � �  D�   �� �   �� �   �� 
   �� y  �� 7   �   P    = �   J A   �   J    �   ; O    	    p	 �  �	 o      � �   �   �
 A  � �   � j  � a   F P   �   � 3    
   ? =  J   � )  � .   � F   �   / �   ? �   � \   E �  � !   '    I |  h �   � �   �  N   E! �   �!     #"   D"   H#    O$    `$ ,   l$ 9   �$ <   �$ :   % Y   K% u   �%    & *   2& @   ]& S   �& '   �& 9   ' 3   T' |   �'    (    ( "  $( N  G) �   �* 
  �+ {   �, �   
- /  �-    / �   / �   0 �   �0 K  `1   �2   ,4 �  16 �  �7 F   v9 <   �9 (   �9 �   #:   �: �  �< /  �> T  �? ,  
A   :B "  MD $   pE 5  �E 9  �F s  H    yI �   �I W  uJ �   �K ^  UL �   �M 
   �N +   �N j   �N 
   BO �  MO �  �Q =  lS �   �U �   �V    KW   _W 
  aX F   oY r   �Y   )Z #  H[ �   l\ �  ] e   �^ )  
_    7` �  O` �   +b �  �b ~  �d �  :g �   9i    �i �   �i �  �j    �l T  �l   %n   *p   ;r   Bt 7  Ku F   �v �  �v �   �x �   ~y E    z T   Fz    �z @   �z (   �z .   { *   @{    k{ �   �{ �  $| �  �~   π �   ߂ 
   �� �  ʃ <  �� n  � �  Q� Z  ܋ j  7�    �� 	  �� 	   ��     �  ّ F  c�    �� r  ��    � �  /� �  *� �  � �  � �  � &  ¤ i  � %   S� �   y�    2� �  �� �  q� �   � �   �� �   F� w  �    e� �   �� H   )� M   r�     �� 9   � 6   � J  R� �  �� p  Q� 0   ¹ �  � �   ��    c� �   k� A   � �   a� �   � +   �� a   �    N� [  f� >  �� �  � �  �� �   5�    �� _   ��   6�    P� �   W� �  �� �   �� 6   H� /   � t  �� ,   $� i  Q� �  �� �  N� &   0� {   W� �   �� F  e� �  ��    n� �   �� .   o� �   �� �   S�   	� �   #� Q   � ,   a� 
  �� 7   �� �  �� K  p� �   ��   �� `   �� A   �� �   @�   �� y  � �  �� �  l� 9   j� �   ��   �� ?  �� �   �� �  �� �  �� ,   e� �  �� +   o� �  �� L   Y�    ��    ��   �� �  ��   � ,  � �  �   � �   � �  w	 �  S �   
   �
 Y  � C    �   ` �   �    q    � A  � �   � 5   �    � �  � /  � �  � �  � �   P �  
 8  �    �  �   !    �! �  �! "   e#    �#    �#    �# *   �#    �#    �#    	$    $    $$ 	   6$    @$ 	   G$    Q$ "   Z$ 	   }$ -   �$    �$    �$    �$    �$ �   �$ ]   �% !   & 
   (& 5   6& (   l& 5   �& .   �& "   �& 
   '    ('    /'    @'    U'    j'    '    �'    �'    �' 1   �'    �'    ( !   ( &   @( .   g( ,   �(    �( $   �( c   �( "   _) T   �) K   �)    #* \   )* J   �*    �*    �* !   �*     + *   3+    ^+ (   s+    �+    �+    �+    �+    �+    �+ 	   �+ 
   ,    , "   ,    9,    @, 	   I,    S, 	   k,    u,    �,    �, 	   �,    �,    �,    �,    �, #   �,    -    "-    *- 	   7-    A- #   S-    w-    ~- /   �-    �-    �- 	   �-     �-    .    
.    . g   1. �   �. i   G/ �   �/ �  m0 r   2    �2 !   �2    �2    �2    �2    �2    �2 )   3 R   .3 4   �3 J   �3    4 ?    4 2   `4 z   �4 b   5 �   q5 u   M6 A   �6 &   7    ,7    97 
   ?7    J7    S7    d7    z7    7    �7    �7    �7    �7    �7    �7 	   �7    �7    8    (8 	   88 
   B8    M8    V8    f8    �8   �8    �9    �9    �9 *   �9    �9 3   �9 ^   : %   u: )   �: 4   �:    �:    ;    
; n   
;    |;    �; )   �; m   �;    )<    D< ~   U< $   �<   �< Q   @ l   c@ �   �@ �   �A =   cB �   �B �   �C L  $D �   qE    eF    �F C   �F O   �F X   $G ^   }G T   �G L   1H f   ~H !   �H /   I    7I    ?I T   OI %   �I    �I    �I    �I    �I g   J 
   sJ 4   �J j   �J    !K    ;K N   YK T   �K u  �K    sM    �M �   �M    mN    yN    �N    �N 	   �N /   �N c  �N    HP 
   eP    sP 
   �P 9  �P    �Q    �Q    �Q w    R    xR 	   �R 2   �R    �R    �R +   �R �   S    �S    �S H   �S $   T    0T 	   BT    LT #   \T ^   �T    �T -   �T �   U |   �U V   UV    �V �   �V J   �W    �W 2   X    ;X �   [X �   Y    �Y :   �Y �   $Z �   �Z     l[ #   �[    �[ �  �[ .   W] &   �]    �] (   �]    �] �   ^     �^    �^ 3    _ D   4_    y_    �_ %   �_ (   �_ P  �_ n  @b   �c :   �e 3   f    5f &   Bf Z  if �   �g �   �h    bi �   �i �  kj "   
l !   0l �  Rl S  Qn 	   �o    �o 8   �o    �o 9  p    =q K  [q �  �r �   Kt    .u �   Hu :   �u &   v l   2v ,  �v )  �w �   �x �  �y y   %{   �{ b   �| �   
} �   �} J   �~ �   �~ ,   � '   �    � 	   �    � '   
� "   2� C   U� 3   �� 	   ̀ 1   ׀ 2  	� 5   <�   r� �   y� �   �� (   �� !   ��    ل    �� 1   � %   B� ,   h� &   �� �   �� *   �� �   ҆    �� �   �� �   w� }   �� ?  x� �   �� -   �� �   �� 	   M� <  W� v   ��    � �  #�    �    �    � #   � +   B�    n�    v� Y   |� �   ֐ �   w� 
   t�    ��    �� �   �� >  i� �   �� �   d�    ��    �    !�    6�    P�    f�    {� K   �� 0   ϖ �   � h   �    R� |   d� w   � Z   Y� |   �� f   1� Z   �� 	   � g   �� T   e� �   �� e   G� T   ��    � �   � i   � F   m� e   �� g   � E   �� 	   Ƞ A   Ҡ w   � �   �� 5   <� �   r�    "� 0  (� G   Y� g   �� �   	�    �� �  �� �   ��    ��    ��    ��    �� '  Ȩ 3   � b   $� �   �� �   [� �   0� �   �� �   �� �   2� �   ׮ \   ��   � d   � t  j� �   ߲   �� 
   �� 
   �� 
   ϴ �   ݴ 
   z� 
   �� H   �� g   ߵ �   G� 
   1� {   ?� A   �� �   �� 9   ��    �� c   ;� 
   �� �   �� 
   1� 
   ?� 
   M� 7  [� �   �� �  �    �    .� 
   4� �   B�    �� -   �   A� �   M� $   ��    "�    2� <   P� ?   �� 2   �� '    � '   (� �   P� #   M� �  q�   � �   � \   �� �   �   �� �  �� H  �� �   �� �   �� �   :�    �� i  ��     T� `  u� �   ��   �� �   �� Y  i� �   �� �   {� x   � ,   �� :   ��    �� 7   
�    E�    Q�    e� �   z� 	   e� g   o� -   ��    �    
�    �     � w   ?� Y   �� F   � �   X� *   J� 	   u�    �    �� &   ��    ��    ��    ��    ��    ��    ��    ��    � .   � �   :�    ��    �� 
   ��    
�    �    +�    <�    L�    ]�    t� )   �� x   ��    *� 4   9� _   n� �   �� �   }� �   1�   �� �    � /  �� #   �� �   � 3   �� ]   �� M   +� �   y� �   -� ]   #� ]   ��    �� e   �� g   U� �   ��    L�    S�    c�    �    ��    ��    �� #   ��    ��    ��    � %   7�    ]�    t�    ��    ��    ��    ��    ��    ��    �� .    � ,   /� |   \� �   �� -   f� &   �� m   �� c   )� t   �� .   � (   1� /   Z� ;   �� @   �� 0   � �   8�   �� +  �    0� J   I� �   �� '   $� �   L� �   ,� v   � 8   ��    �� �   �� �   n� h   � .   z� s   �� �   �    �� *   ��    &� 6   :� �   q� r   ��    i�    q�    z� (   �� �   �� 0   A� )   r� 7   �� &   ��    �� #   
� K   1�    }� h   �� 9   �   <� T   E  Q   �  �   �  <   �    �    �        2 $   J    o $   � ?   � 9   � b  & B   � 6   � B    -   F    t h   z �   � �   i �   &    � �   � 9   �	    �	 �   �	    �
 *   �
 A    <   F _   � Y   � S   =    �     � �   � 2   r
 $   �
 O   �
 �    D    �   [ k   � Z   V {   � 1   - ?   _ �   �     % k   F     � �   � 1   � 9   �    � .     �   / >    J   K �   � Y    W   r �   � �   a         	 C    E   Z    � !   �    �    �    � -    �   > h   �    8 $   R ,   w )   � %   � l   � 8   a �   � Z   & %   � �   � �   ( ,    Q   G    � O   � G       I q   i    � )   [  O   �     �  ,   �  Q   !    k! n   �! -   �!    " *   9" f   d" �   �"    �#    �#    �# F   �#    .$ Z   E$ ?   �$ �   �$ {   ~%    �% H   & �   U&    ' �   =' E   �' $   ( N   B(   �(    �)    �)    �)    �) 8   �) -   �) J   *    i*    �*    �* "   �* @   �* @   �* 
   @+ L   N+ H   �+    �+ (   �+ 5   ,    U,    s, S   �, �   �, B   �-    . 
    . �   .. S  �. [   40 
   �0 M  �0 u  �1 3   b3 }   �3 #   4 9  84 -   r6 a   �6    7    7 �  7    �8 \   �8 �   C9 |   �9 d   G:    �: _   �: 3   );    ]; {   x; @   �; 1   5< Y   g< *   �< T   �< �   A= �   �= .   �> �   �> u  }? �   �@ ,   �A i  �A �   @C �   =D $  E �   CF +    G 7   ,G �   dG 8   H !  OH f   qJ L   �J    %K    5K �  UK    M �   	M a  �M ;  KO 2  �P D   �Q O   �Q Z   OR *   �R +   �R F   S g   HS    �S    �S 3   �S &   T    5T -   NT i   |T *   �T    U ]   U �   vU �   YV    W    W    3W P   SW i   �W c   X �   rX k   DY ;   �Y )   �Y �   Z 
   �Z �   [ �  �[ �   9^ U   2_ 1   �_    �_    �_    �_ H   �_ 1   ` �   F` �   �` P   �a    b    1b %   Db    jb a   �b    �b @   �b    6c .   =c +   lc    �c    �c V   �c    
d    d 0   'd     Xd    yd w   }d �   �d S   �e h   f S   �f �   �f    �g    �g �   �g �   }h �   Gi 	   j z   j V   �j G   �j b   0k g   �k _   �k    [l X   yl    �l    �l    �l    m    (m    Em    Xm $   lm    �m    �m ,   �m /   �m ,   n    5n 2   Tn -   �n "   �n    �n    �n E   o    Qo 
   oo 7   zo 2   �o [   �o -   Ap    op    �p &   �p    �p    �p s   �p S   kq �   �q �   Hr    )s    As "   Ws    zs 4   �s 	   �s    �s    �s �   �s    ~t #   �t    �t 
   �t 	   �t ?   �t    u   9u    @v    Sv    gv $   xv "   �v    �v #   �v %   �v )   %w "   Ow 7   rw    �w ^   �w *   x    ;x    Wx 6   hx >   �x #   �x ,   y    /y d   Ly \   �y Y   z    hz    pz 	   �z    �z    �z    �z �  �z ~   �|    	}    $} #   (}    L} ,   d}    �} 
   �} p   �} D   ~ �   W~    0 )   C �   m )   � %   � )   D� ,   n� 6   �� +   Ҁ    ��    � :   '�    b� %   �� ;   �� ;  � A   � ;   a� _   ��    �� �   � $   ��    ܄ Q   �    C� -   [�    �� F   �� A   � 2   )� �   \�    ��    �     �    7�    M�    e�    {�    ��    �� d   Ǉ 
  ,�    :� %   Y� �   � �   n� %   $�    J�    f�    y�    ��    ��    ��    ΋    �    ��    �    � 
   (�    6�    E�    W�    d�    p� &   �� �   �� �   x� }  h� )  � �  � �   �� �  ��    H�   U�    h� �   ~� �   j� �  [� �   � q  �� 8   
� �   C� <   �    (� F   F� Q   �� f   ߞ q   F� �   �� �   Z� �   � �  ҡ    S� �   q� �   =� f   � �   ��    .� �   C� o  6� �   �� �   �� 	   e� d   o� �   ԫ �   U� �   O� d   ҭ �   7�    ��    ׮    �� N   � 6   [�    �� {   �� F   !� z   h� &   � �   
� �   �� T   ;� P   �� b   � �   D� d   ȳ R   -� }   �� ^   �� �   ]� y   �    f� -   m� +   �� _   Ƕ 3   '�    [�    b� E   i�    ��    ŷ    ٷ 9   ߷ 3   � C   M�    ��    �� 	   ��    ø 	   ɸ g   Ӹ v   ;� ]   �� 6   �    G� &   O� 5   v�    ��    ��    ɺ    к    غ    ߺ    �    �    �� 
   �    �    � 
   +�    6�    K�    _�    u� 	   �� 
   ��    �� %   ��    ƻ !   � {   �    �� ^   �� �   ��    ��    ǽ 
   ӽ    �    �    �    �� �    � �   �� K   N�    ��    �� r   ǿ    :� }   K� �   �� $   S�    x� �   �� �   <� _   � �   k� w   3� ,   �� �   ��    _� "   ~� K   �� �   ��    �� �   �� y   =� �   �� W   ��    ��    ��    �� 	   �    �    -�    6�    E� ~   b� z   �� �   \� �   �� �   �� b   �� F   � �  \� }  ��   p� �   v�    � l  8�    �� �   �� 1  t� �   �� �  Z� �  � �   �� {  K�    �� A   �� �   � p  �� K   !� �   m�    b�    g�    n� �   �� A   � l   `� 1   �� `   �� E   `� `   �� ,   � �   4� D   �� ,   &� X   S� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: gl
Language-Team: gl <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library é unha biblioteca popular (e ilegal). Tomaron a colección de Library Genesis e fixérona facilmente buscable. Ademais, fixéronse moi efectivos solicitando novas contribucións de libros, incentivando aos usuarios contribuíntes con varios beneficios. Actualmente non contribúen estes novos libros de volta a Library Genesis. E a diferenza de Library Genesis, non fan que a súa colección sexa facilmente espellable, o que impide unha ampla preservación. Isto é importante para o seu modelo de negocio, xa que cobran diñeiro por acceder á súa colección en masa (máis de 10 libros por día). Non facemos xuízos morais sobre cobrar diñeiro por acceso masivo a unha colección de libros ilegal. Non hai dúbida de que a Z-Library tivo éxito en expandir o acceso ao coñecemento e en conseguir máis libros. Estamos aquí simplemente para facer a nosa parte: asegurar a preservación a longo prazo desta colección privada. - Anna e o equipo (<a %(reddit)s>Reddit</a>) No lanzamento orixinal do Espello da Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>), fixemos un espello de Z-Library, unha gran colección de libros ilegal. Como recordatorio, isto é o que escribimos nesa publicación orixinal do blog: Esa colección data de mediados de 2021. Mentres tanto, a Z-Library creceu a un ritmo asombroso: engadiron uns 3,8 millóns de novos libros. Hai algúns duplicados, claro, pero a maioría parecen ser libros realmente novos ou escaneos de maior calidade de libros previamente enviados. Isto débese en gran parte ao aumento do número de moderadores voluntarios na Z-Library e ao seu sistema de carga masiva con deduplicación. Gustaríanos felicitalos por estes logros. Estamos felices de anunciar que conseguimos todos os libros que se engadiron á Z-Library entre o noso último espello e agosto de 2022. Tamén volvemos atrás e raspamos algúns libros que perdemos a primeira vez. En total, esta nova colección é duns 24TB, que é moito máis grande que a anterior (7TB). O noso espello agora ten un total de 31TB. De novo, deduplicamos contra Library Genesis, xa que xa hai torrents dispoñibles para esa colección. Por favor, vaia ao Espello da Biblioteca Pirata para ver a nova colección (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>). Alí hai máis información sobre como están estruturados os ficheiros e o que cambiou desde a última vez. Non enlazaremos desde aquí, xa que este é só un sitio web de blog que non alberga ningún material ilegal. Por suposto, sementar tamén é unha gran forma de axudarnos. Grazas a todos os que están sementando o noso conxunto anterior de torrents. Estamos agradecidos pola resposta positiva e felices de que haxa tanta xente que se preocupa pola preservación do coñecemento e a cultura deste xeito inusual. 3x novos libros engadidos ao Espello da Biblioteca Pirata (+24TB, 3,8 millóns de libros) Le os artigos complementarios de TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a> - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artigos complementarios de TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a> Non hai moito tempo, as “bibliotecas na sombra” estaban morrendo. Sci-Hub, o enorme arquivo ilegal de artigos académicos, deixara de aceptar novas obras debido a demandas xudiciais. “Z-Library”, a maior biblioteca ilegal de libros, viu como os seus presuntos creadores foron arrestados por cargos criminais de dereitos de autor. Incriblemente lograron escapar do seu arresto, pero a súa biblioteca non está menos ameazada. Algúns países xa están a facer unha versión disto. TorrentFreak <a %(torrentfreak)s>informou</a> que China e Xapón introducíron excepcións de IA nas súas leis de dereitos de autor. Non está claro para nós como isto interactúa cos tratados internacionais, pero certamente dá cobertura ás súas empresas domésticas, o que explica o que estivemos vendo. En canto ao Arquivo de Anna — continuaremos o noso traballo subterráneo arraigado na convicción moral. Con todo, o noso maior desexo é saír á luz, e amplificar o noso impacto legalmente. Por favor, reformen os dereitos de autor. Cando Z-Library enfrontou o peche, eu xa fixera unha copia de seguridade de toda a súa biblioteca e estaba buscando unha plataforma para aloxala. Esa foi a miña motivación para comezar o Arquivo de Anna: unha continuación da misión detrás desas iniciativas anteriores. Desde entón, medramos ata ser a maior biblioteca na sombra do mundo, aloxando máis de 140 millóns de textos con dereitos de autor en numerosos formatos — libros, artigos académicos, revistas, xornais e máis aló. O meu equipo e eu somos ideólogos. Cremos que preservar e aloxar estes arquivos é moralmente correcto. As bibliotecas de todo o mundo están vendo recortes de financiamento, e tampouco podemos confiar o patrimonio da humanidade ás corporacións. Logo chegou a IA. Prácticamente todas as grandes empresas que constrúen LLMs contactáronnos para adestrar cos nosos datos. A maioría (pero non todas!) das empresas con sede nos EUA reconsideraron unha vez que se deron conta da natureza ilegal do noso traballo. Pola contra, as empresas chinesas abrazaron entusiasticamente a nosa colección, aparentemente sen preocuparse pola súa legalidade. Isto é notable dado o papel de China como signatario de case todos os principais tratados internacionais de dereitos de autor. Demos acceso de alta velocidade a unhas 30 empresas. A maioría delas son empresas de LLM, e algunhas son intermediarios de datos, que revenderán a nosa colección. A maioría son chinesas, aínda que tamén traballamos con empresas dos EUA, Europa, Rusia, Corea do Sur e Xapón. DeepSeek <a %(arxiv)s>admitiu</a> que unha versión anterior foi adestrada en parte da nosa colección, aínda que son reservados sobre o seu último modelo (probablemente tamén adestrado cos nosos datos). Se Occidente quere manterse á fronte na carreira dos LLMs, e finalmente, da AGI, necesita reconsiderar a súa posición sobre os dereitos de autor, e pronto. Estea de acordo connosco ou non sobre o noso caso moral, isto está a converterse nun caso de economía, e incluso de seguridade nacional. Todos os bloques de poder están a construír super-científicos artificiais, super-hackers e super-militares. A liberdade de información está a converterse nunha cuestión de supervivencia para estes países — incluso unha cuestión de seguridade nacional. O noso equipo é de todo o mundo, e non temos unha alineación particular. Pero animaríamos aos países con leis de dereitos de autor fortes a usar esta ameaza existencial para reformalas. Entón, que facer? A nosa primeira recomendación é sinxela: acurtar o prazo dos dereitos de autor. Nos EUA, os dereitos de autor concédense por 70 anos despois da morte do autor. Isto é absurdo. Podemos aliñalo cos patentes, que se conceden por 20 anos despois da presentación. Isto debería ser máis que suficiente tempo para que os autores de libros, artigos, música, arte e outras obras creativas, sexan completamente compensados polos seus esforzos (incluíndo proxectos a longo prazo como adaptacións cinematográficas). Logo, como mínimo, os responsables políticos deberían incluír excepcións para a preservación masiva e a difusión de textos. Se a perda de ingresos de clientes individuais é a principal preocupación, a distribución a nivel persoal podería seguir prohibida. En cambio, aqueles capaces de xestionar vastos repositorios — empresas que adestran LLMs, xunto con bibliotecas e outros arquivos — estarían cubertos por estas excepcións. A reforma dos dereitos de autor é necesaria para a seguridade nacional TL;DR: Os LLM chineses (incluíndo DeepSeek) están adestrados no meu arquivo ilegal de libros e artigos — o maior do mundo. Occidente necesita reformar a lei de dereitos de autor como unha cuestión de seguridade nacional. Consulte a <a %(all_isbns)s>entrada orixinal do blog</a> para obter máis información. Emitimos un desafío para mellorar isto. Outorgaríamos un premio de primeiro lugar de $6,000, segundo lugar de $3,000 e terceiro lugar de $1,000. Debido á resposta esmagadora e ás incríbeis presentacións, decidimos aumentar lixeiramente o fondo de premios e outorgar un terceiro lugar compartido de $500 cada un. Os gañadores están a continuación, pero asegúrese de ver todas as presentacións <a %(annas_archive)s>aquí</a>, ou descargar o noso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Primeiro lugar $6,000: phiresky Esta <a %(phiresky_github)s>presentación</a> (<a %(annas_archive_note_2951)s>comentario en Gitlab</a>) é simplemente todo o que queriamos, e máis! Gustáronnos especialmente as opcións de visualización incríbelmente flexibles (incluso admitindo sombreadores personalizados), pero cunha lista completa de predefinidos. Tamén nos gustou o rápido e fluído que é todo, a implementación sinxela (que nin sequera ten un backend), o minimapa intelixente e a extensa explicación na súa <a %(phiresky_github)s>entrada de blog</a>. Un traballo incríbel, e un gañador ben merecido! - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Os nosos corazóns están cheos de gratitude. Ideas notables Rascacielos para a rareza Moitos deslizadores para comparar datasets, coma se foses un DJ. Barra de escala co número de libros. Etiquetas bonitas. Esquema de cores predeterminado e mapa de calor. Vista de mapa única e filtros Anotacións, e tamén estatísticas en directo Estatísticas en directo Algunhas ideas e implementacións máis que nos gustaron especialmente: Poderiamos seguir durante un tempo, pero imos parar aquí. Asegúrate de ver todas as presentacións <a %(annas_archive)s>aquí</a>, ou descarga o noso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Moitas presentacións, e cada unha achega unha perspectiva única, xa sexa na UI ou na implementación. Polo menos incorporaremos a presentación do primeiro lugar no noso sitio web principal, e quizais algunhas outras. Tamén comezamos a pensar en como organizar o proceso de identificar, confirmar e logo arquivar os libros máis raros. Máis novidades neste aspecto. Grazas a todos os que participaron. É incrible que tanta xente se preocupe. Cambio sinxelo de datasets para comparacións rápidas. Todos os ISBNs CADAL SSNOs Fuga de datos de CERLALC DuXiu SSIDs Índice de eBooks de EBSCOhost Google Books Goodreads Internet Archive ISBNdb Rexistro Global de Editores ISBN Libby Arquivos no Arquivo de Anna Nexus/STC OCLC/Worldcat OpenLibrary Biblioteca Estatal Rusa Biblioteca Imperial de Trantor Segundo lugar $3,000: hypha “Aínda que os cadrados e rectángulos perfectos son matematicamente agradables, non proporcionan unha superior localidade nun contexto de mapeo. Creo que a asimetría inherente a estes Hilbert ou Morton clásico non é un defecto senón unha característica. Do mesmo xeito que o famoso contorno en forma de bota de Italia fai que sexa instantaneamente recoñecible nun mapa, as "peculiaridades" únicas destas curvas poden servir como fitos cognitivos. Esta distintividade pode mellorar a memoria espacial e axudar aos usuarios a orientarse, potencialmente facilitando a localización de rexións específicas ou a detección de patróns.” Outra <a %(annas_archive_note_2913)s>presentación</a> incríbel. Non tan flexible como o primeiro lugar, pero de feito preferimos a súa visualización a nivel macro sobre o primeiro lugar (curva de recheo de espazo, bordos, etiquetado, resaltado, desprazamento e zoom). Un <a %(annas_archive_note_2971)s>comentario</a> de Joe Davis resoou connosco: E aínda moitas opcións para visualizar e renderizar, así como unha interface de usuario incríbelmente fluída e intuitiva. Un sólido segundo lugar! - Anna e o equipo (<a %(reddit)s>Reddit</a>) Hai uns meses anunciamos unha <a %(all_isbns)s>recompensa de $10,000</a> para facer a mellor visualización posible dos nosos datos mostrando o espazo ISBN. Enfatizamos mostrar que arquivos temos/non temos arquivados xa, e máis tarde un conxunto de datos que describe cantas bibliotecas teñen ISBNs (unha medida de rareza). Estamos abrumados pola resposta. Houbo tanta creatividade. Un gran agradecemento a todos os que participaron: a vosa enerxía e entusiasmo son contaxiosos! En última instancia, queriamos responder ás seguintes preguntas: <strong>que libros existen no mundo, cantos arquivamos xa, e en cales deberiamos centrarnos a continuación?</strong> É estupendo ver que tanta xente se preocupa por estas preguntas. Comezamos cunha visualización básica nós mesmos. En menos de 300kb, esta imaxe representa sucintamente a maior “lista de libros” completamente aberta xamais ensamblada na historia da humanidade: Terceiro lugar $500 #1: maxlion Nesta <a %(annas_archive_note_2940)s>presentación</a> realmente nos gustaron os diferentes tipos de vistas, en particular as vistas de comparación e editor. Terceiro lugar $500 #2: abetusk Aínda que non é a interface de usuario máis pulida, esta <a %(annas_archive_note_2917)s>presentación</a> cumpre moitos dos requisitos. Gustounos especialmente a súa función de comparación. Terceiro lugar $500 #3: conundrumer0 Como o primeiro lugar, esta <a %(annas_archive_note_2975)s>presentación</a> impresionounos coa súa flexibilidade. En última instancia, isto é o que fai que unha ferramenta de visualización sexa excelente: máxima flexibilidade para usuarios avanzados, mentres se manteñen as cousas sinxelas para os usuarios medios. Terceiro lugar $500 #4: charelf A última <a %(annas_archive_note_2947)s>presentación</a> en recibir un premio é bastante básica, pero ten algunhas características únicas que realmente nos gustaron. Gustounos como mostran cantos datasets cobren un ISBN particular como medida de popularidade/confiabilidade. Tamén nos gustou moito a simplicidade pero efectividade de usar un control deslizante de opacidade para comparacións. Gañadores da recompensa de visualización de ISBN de $10,000 TL;DR: Recibimos algunhas presentacións incribles para a recompensa de visualización de ISBN de $10,000. Antecedentes Como pode o Arquivo de Anna cumprir a súa misión de facer unha copia de seguridade de todo o coñecemento da humanidade, sen saber que libros aínda están por aí? Necesitamos unha lista de tarefas. Unha forma de mapear isto é a través dos números ISBN, que desde os anos 70 foron asignados a cada libro publicado (na maioría dos países). Non hai unha autoridade central que coñeza todas as asignacións de ISBN. En cambio, é un sistema distribuído, onde os países obteñen rangos de números, que logo asignan rangos máis pequenos aos principais editores, que poden subdividir aínda máis os rangos a editores menores. Finalmente, os números individuais son asignados aos libros. Comezamos a mapear os ISBNs <a %(blog)s>hai dous anos</a> co noso raspado de ISBNdb. Desde entón, raspamos moitas máis fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e máis. Unha lista completa pódese atopar nas páxinas de “Datasets” e “Torrents” no Arquivo de Anna. Agora temos, con moito, a maior colección totalmente aberta e facilmente descargable de metadata de libros (e polo tanto ISBNs) no mundo. Escribimos <a %(blog)s>amplamente</a> sobre por que nos importa a preservación, e por que estamos actualmente nunha xanela crítica. Debemos agora identificar libros raros, pouco enfocados e unicamente en risco e preservalos. Ter boa metadata de todos os libros do mundo axuda con iso. Recompensa de $10,000 Darase unha forte consideración á usabilidade e ao bo aspecto. Mostrar metadata real para ISBNs individuais ao ampliar, como título e autor. Mellor curva de recheo de espazo. Por exemplo, un zig-zag, indo de 0 a 4 na primeira fila e logo de volta (en reverso) de 5 a 9 na segunda fila — aplicado recursivamente. Esquemas de cores diferentes ou personalizables. Vistas especiais para comparar datasets. Formas de depurar problemas, como outros metadata que non coinciden ben (por exemplo, títulos moi diferentes). Anotar imaxes con comentarios sobre ISBNs ou intervalos. Calquera heurística para identificar libros raros ou en risco. Calquera idea creativa que poidas ter! Código O código para xerar estas imaxes, así como outros exemplos, pódese atopar neste <a %(annas_archive)s>directorio</a>. Ideamos un formato de datos compacto, co que toda a información ISBN requirida ocupa uns 75MB (comprimido). A descrición do formato de datos e o código para xeneralo pódense atopar <a %(annas_archive_l1244_1319)s>aquí</a>. Para a recompensa non estás obrigado a usar isto, pero probablemente sexa o formato máis conveniente para comezar. Podes transformar o noso metadata como queiras (aínda que todo o teu código ten que ser de código aberto). Estamos ansiosos por ver o que se che ocorre. Boa sorte! Faga un fork deste repositorio, e edite este post de blog en HTML (non se permiten outros backends ademais do noso backend Flask). Faga que a imaxe anterior sexa suavemente ampliable, para que poida ampliar ata os ISBNs individuais. Facer clic nos ISBNs debería levalo a unha páxina de metadata ou busca no Arquivo de Anna. Aínda debe poder cambiar entre todos os diferentes datasets. Os rangos de países e os rangos de editores deben destacarse ao pasar o rato. Pode usar, por exemplo, <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> para información de países, e o noso raspado “isbngrp” para editores (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Debe funcionar ben en escritorio e móbil. Hai moito que explorar aquí, así que estamos anunciando unha recompensa por mellorar a visualización anterior. A diferenza da maioría das nosas recompensas, esta ten un límite de tempo. Debe <a %(annas_archive)s>enviar</a> o seu código de código aberto antes do 2025-01-31 (23:59 UTC). A mellor presentación recibirá $6,000, o segundo lugar $3,000, e o terceiro lugar $1,000. Todas as recompensas serán entregadas usando Monero (XMR). A continuación están os criterios mínimos. Se ningunha presentación cumpre os criterios, aínda poderiamos outorgar algunhas recompensas, pero iso será á nosa discreción. Para puntos extra (estas son só ideas — deixe que a súa creatividade voa): PODES desviarte completamente dos criterios mínimos e facer unha visualización completamente diferente. Se é realmente espectacular, entón cualifica para a recompensa, pero a nosa discreción. Fai as túas presentacións publicando un comentario en <a %(annas_archive)s>este problema</a> cunha ligazón ao teu repositorio bifurcado, solicitude de fusión ou diferenza. - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Esta imaxe ten 1000×800 píxeles. Cada píxel representa 2,500 ISBNs. Se temos un ficheiro para un ISBN, facemos ese píxel máis verde. Se sabemos que se emitiu un ISBN, pero non temos un ficheiro correspondente, facémolo máis vermello. En menos de 300kb, esta imaxe representa sucintamente a maior “lista de libros” totalmente aberta xamais ensamblada na historia da humanidade (uns centos de GB comprimidos en total). Tamén mostra: queda moito traballo por facer para respaldar libros (só temos 16%). Visualizando Todos os ISBNs — recompensa de $10,000 para o 2025-01-31 Esta imaxe representa a maior “lista de libros” totalmente aberta xamais ensamblada na historia da humanidade. Visualización Ademais da imaxe xeral, tamén podemos ver os datasets individuais que adquirimos. Use o despregable e os botóns para cambiar entre eles. Hai moitos patróns interesantes para ver nestas imaxes. Por que hai certa regularidade de liñas e bloques, que parece ocorrer a diferentes escalas? Que son as áreas baleiras? Por que certos datasets están tan agrupados? Deixaremos estas preguntas como un exercicio para o lector. - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusión Con este estándar, podemos facer lanzamentos de forma máis incremental e engadir novas fontes de datos máis facilmente. Xa temos algúns lanzamentos emocionantes en proceso! Tamén esperamos que sexa máis doado para outras bibliotecas sombra espellar as nosas coleccións. Despois de todo, o noso obxectivo é preservar o coñecemento e a cultura humana para sempre, polo que canto máis redundancia, mellor. Exemplo Vexamos o noso recente lanzamento de Z-Library como exemplo. Consiste en dúas coleccións: “<span style="background: #fffaa3">zlib3_records</span>” e “<span style="background: #ffd6fe">zlib3_files</span>”. Isto permítenos extraer e lanzar por separado os rexistros de metadata dos ficheiros reais dos libros. Así, lanzamos dous torrents con ficheiros de metadata: Tamén lanzamos un conxunto de torrents con carpetas de datos binarios, pero só para a colección “<span style="background: #ffd6fe">zlib3_files</span>”, 62 en total: Executando <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver o que hai dentro: Neste caso, é a metadata dun libro segundo informa Z-Library. No nivel superior só temos “aacid” e “metadata”, pero non hai “data_folder”, xa que non hai datos binarios correspondentes. O AACID contén “22430000” como ID principal, que podemos ver que se toma de “zlibrary_id”. Podemos esperar que outros AACs nesta colección teñan a mesma estrutura. Agora imos executar <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Esta é unha metadata AAC moito máis pequena, aínda que a maior parte deste AAC está situada noutro lugar nun ficheiro binario! Despois de todo, temos un “data_folder” esta vez, polo que podemos esperar que os datos binarios correspondentes estean situados en <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. A “metadata” contén o “zlibrary_id”, polo que podemos asocialo facilmente co AAC correspondente na colección “zlib_records”. Poderiamos asocialo de varias maneiras diferentes, por exemplo, a través de AACID — o estándar non prescribe iso. Teña en conta que tampouco é necesario que o campo “metadata” sexa en si mesmo JSON. Podería ser unha cadea que conteña XML ou calquera outro formato de datos. Incluso podería almacenar información de metadata no blob binario asociado, por exemplo, se é moita información. Ficheiros e metadata heteroxéneos, no formato máis próximo ao orixinal posible. Os datos binarios poden ser servidos directamente por servidores web como Nginx. Identificadores heteroxéneos nas bibliotecas de orixe, ou incluso a falta de identificadores. Lanzamentos separados de metadata fronte a datos de ficheiros, ou lanzamentos só de metadata (por exemplo, o noso lanzamento de ISBNdb). Distribución a través de torrents, aínda que coa posibilidade doutros métodos de distribución (por exemplo, IPFS). Rexistros inmutables, xa que debemos supoñer que os nosos torrents vivirán para sempre. Lanzamentos incrementais / lanzamentos ampliables. Lexible e escribible por máquinas, de forma conveniente e rápida, especialmente para a nosa pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspección humana algo fácil, aínda que isto é secundario á lexibilidade por máquinas. Fácil de sementar as nosas coleccións cun seedbox estándar alugado. Obxectivos de deseño Non nos importa que os ficheiros sexan fáciles de navegar manualmente no disco, ou que sexan buscables sen preprocesamento. Non nos importa ser directamente compatibles co software de biblioteca existente. Aínda que debería ser fácil para calquera sementar a nosa colección usando torrents, non esperamos que os ficheiros sexan utilizables sen un coñecemento técnico significativo e compromiso. O noso caso de uso principal é a distribución de ficheiros e metadata asociada de diferentes coleccións existentes. As nosas consideracións máis importantes son: Algúns non-obxectivos: Dado que o Arquivo de Anna é de código aberto, queremos usar o noso formato directamente. Cando actualizamos o noso índice de busca, só accedemos a rutas dispoñibles publicamente, para que calquera que bifurque a nosa biblioteca poida comezar rapidamente. <strong>AAC.</strong> AAC (Contedor do Arquivo de Anna) é un único elemento que consta de <strong>metadata</strong> e, opcionalmente, <strong>datos binarios</strong>, ambos inmutables. Ten un identificador único global, chamado <strong>AACID</strong>. <strong>AACID.</strong> O formato de AACID é este: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por exemplo, un AACID real que lanzamos é <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Rango de AACID.</strong> Dado que os AACIDs conteñen marcas de tempo que aumentan monotonamente, podemos usalo para denotar rangos dentro dunha colección particular. Usamos este formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, onde as marcas de tempo son inclusivas. Isto é consistente coa notación ISO 8601. Os rangos son continuos e poden superpoñerse, pero en caso de superposición deben conter rexistros idénticos aos previamente lanzados nesa colección (xa que os AACs son inmutables). Non se permiten rexistros faltantes. <code>{collection}</code>: o nome da colección, que pode conter letras ASCII, números e guións baixos (pero non dobre guión baixo). <code>{collection-specific ID}</code>: un identificador específico da colección, se é aplicable, por exemplo, o ID de Z-Library. Pode ser omitido ou truncado. Debe ser omitido ou truncado se o AACID superaría os 150 caracteres. <code>{ISO 8601 timestamp}</code>: unha versión curta do ISO 8601, sempre en UTC, por exemplo, <code>20220723T194746Z</code>. Este número ten que aumentar monotonamente para cada lanzamento, aínda que a súa semántica exacta pode diferir por colección. Suxerimos usar o tempo de scraping ou de xeración do ID. <code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por exemplo, usando base57. Actualmente usamos a biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Cartafol de datos binarios.</strong> Un cartafol cos datos binarios dun rango de AACs, para unha colección particular. Teñen as seguintes propiedades: O directorio debe conter ficheiros de datos para todos os AACs dentro do rango especificado. Cada ficheiro de datos debe ter o seu AACID como nome de ficheiro (sen extensións). O nome do directorio debe ser un rango de AACID, prefixado con <code style="color: green">annas_archive_data__</code>, e sen sufixo. Por exemplo, un dos nosos lanzamentos reais ten un directorio chamado<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Recoméndase facer que estas carpetas sexan algo manexables en tamaño, por exemplo, que non sexan máis grandes de 100GB-1TB cada unha, aínda que esta recomendación pode cambiar co tempo. <strong>Colección.</strong> Cada AAC pertence a unha colección, que por definición é unha lista de AACs que son semanticamente consistentes. Isto significa que se fas un cambio significativo no formato dos metadata, entón tes que crear unha nova colección. O estándar <strong>Ficheiro de metadata.</strong> Un ficheiro de metadata contén os metadata dun rango de AACs, para unha colección particular. Teñen as seguintes propiedades: <code>data_folder</code> é opcional, e é o nome do cartafol de datos binarios que contén os datos binarios correspondentes. O nome do ficheiro dos datos binarios correspondentes dentro dese cartafol é o AACID do rexistro. Cada obxecto JSON debe conter os seguintes campos no nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Non se permiten outros campos. O nome do ficheiro debe ser un rango de AACID, prefixado con <code style="color: red">annas_archive_meta__</code> e seguido por <code>.jsonl.zstd</code>. Por exemplo, un dos nosos lanzamentos chámase<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Como indica a extensión do ficheiro, o tipo de ficheiro é <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>. <code>metadata</code> son metadata arbitrarios, segundo a semántica da colección. Deben ser semanticamente consistentes dentro da colección. O prefixo <code style="color: red">annas_archive_meta__</code> pode ser adaptado ao nome da túa institución, por exemplo, <code style="color: red">my_institute_meta__</code>. <strong>Coleccións de “rexistros” e “ficheiros”.</strong> Por convención, adoita ser conveniente lanzar “rexistros” e “ficheiros” como coleccións diferentes, para que poidan ser lanzadas en diferentes horarios, por exemplo, baseándose nas taxas de scraping. Un “rexistro” é unha colección só de metadata, que contén información como títulos de libros, autores, ISBNs, etc., mentres que os “ficheiros” son as coleccións que conteñen os ficheiros reais (pdf, epub). Finalmente, decidimos un estándar relativamente sinxelo. É bastante flexible, non normativo e está en proceso de desenvolvemento. <strong>Torrents.</strong> Os ficheiros de metadata e as carpetas de datos binarios poden agruparse en torrents, cun torrent por ficheiro de metadata ou cun torrent por carpeta de datos binarios. Os torrents deben ter o nome orixinal do ficheiro/directorio máis un sufixo <code>.torrent</code> como o seu nome de ficheiro. <a %(wikipedia_annas_archive)s>O Arquivo de Anna</a> converteuse de lonxe na maior biblioteca na sombra do mundo, e a única biblioteca na sombra da súa escala que é completamente de código aberto e datos abertos. A continuación móstrase unha táboa da nosa páxina de Datasets (lixeiramente modificada): Conseguimos isto de tres maneiras: Reflexionando bibliotecas na sombra de datos abertos existentes (como Sci-Hub e Library Genesis). Axudando a bibliotecas na sombra que queren ser máis abertas, pero non tiñan o tempo ou os recursos para facelo (como a colección de cómics de Libgen). Raspando bibliotecas que non desexan compartir en masa (como Z-Library). Para (2) e (3) agora xestionamos unha considerable colección de torrents nós mesmos (centos de TBs). Ata agora abordamos estas coleccións como casos únicos, o que significa infraestrutura e organización de datos a medida para cada colección. Isto engade unha carga significativa a cada lanzamento e fai especialmente difícil realizar lanzamentos máis incrementais. Por iso decidimos estandarizar os nosos lanzamentos. Esta é unha publicación técnica no blog na que estamos a introducir o noso estándar: <strong>Contedores do Arquivo de Anna</strong>. Contedores do Arquivo de Anna (AAC): estandarizando lanzamentos da maior biblioteca na sombra do mundo O Arquivo de Anna converteuse na maior biblioteca na sombra do mundo, requirindo que estandaricemos os nosos lanzamentos. Máis de 300GB de portadas de libros liberadas Finalmente, estamos felices de anunciar un pequeno lanzamento. En colaboración coas persoas que operan o fork Libgen.rs, estamos compartindo todas as súas portadas de libros a través de torrents e IPFS. Isto distribuirá a carga de ver as portadas entre máis máquinas e preservaraas mellor. En moitos (pero non todos) casos, as portadas dos libros están incluídas nos propios ficheiros, polo que isto é unha especie de "datos derivados". Pero telos en IPFS aínda é moi útil para o funcionamento diario tanto do Arquivo de Anna como dos diversos forks de Library Genesis. Como de costume, pode atopar este lanzamento no Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>). Non enlazaremos aquí, pero pode atopalo facilmente. Agardamos poder relaxar un pouco o noso ritmo, agora que temos unha alternativa decente a Z-Library. Esta carga de traballo non é particularmente sostible. Se está interesado en axudar con programación, operacións de servidor ou traballo de preservación, definitivamente póñase en contacto connosco. Aínda hai moito <a %(annas_archive)s>traballo por facer</a>. Grazas polo seu interese e apoio. Cambiar a ElasticSearch Algunhas consultas tardaban moito, ata o punto de acaparar todas as conexións abertas. Por defecto, MySQL ten unha lonxitude mínima de palabra, ou o seu índice pode facerse moi grande. Informouse de que non se podía buscar "Ben Hur". A busca só era algo rápida cando estaba completamente cargada na memoria, o que requiría que conseguísemos unha máquina máis cara para executala, ademais dalgúns comandos para precargar o índice ao iniciar. Non teriamos sido capaces de amplialo facilmente para construír novas funcións, como mellor <a %(wikipedia_cjk_characters)s>tokenización para linguas sen espazos</a>, filtrado/facetado, ordenación, suxestións de "quixo dicir", autocompletado, e así por diante. Un dos nosos <a %(annas_archive)s>tickets</a> era un conxunto de problemas co noso sistema de busca. Usamos a busca de texto completo de MySQL, xa que tiñamos todos os nosos datos en MySQL de todos os xeitos. Pero tiña os seus límites: Despois de falar con varios expertos, decidimos usar ElasticSearch. Non foi perfecto (as súas suxestións de "quixo dicir" e funcións de autocompletado por defecto son malas), pero en xeral foi moito mellor que MySQL para a busca. Aínda non estamos <a %(youtube)s>moi convencidos</a> de usalo para calquera dato crítico (aínda que fixeron moitos <a %(elastic_co)s>avances</a>), pero en xeral estamos bastante contentos co cambio. Por agora, implementamos unha busca moito máis rápida, mellor soporte de linguas, mellor ordenación por relevancia, diferentes opcións de ordenación e filtrado por lingua/tipo de libro/tipo de ficheiro. Se tes curiosidade sobre como funciona, <a %(annas_archive_l140)s>bótalle</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>ollo</a>. É bastante accesible, aínda que podería usar algúns comentarios máis… O Arquivo de Anna é totalmente de código aberto Cremos que a información debería ser libre, e o noso propio código non é unha excepción. Publicamos todo o noso código na nosa instancia de Gitlab aloxada privadamente: <a %(annas_archive)s>Software de Anna</a>. Tamén usamos o rastreador de problemas para organizar o noso traballo. Se queres participar no noso desenvolvemento, este é un bo lugar para comezar. Para darche unha idea das cousas nas que estamos traballando, toma o noso traballo recente en melloras de rendemento do lado do cliente. Como aínda non implementamos a paginación, a miúdo devolvemos páxinas de busca moi longas, con 100-200 resultados. Non queriamos cortar os resultados da busca demasiado pronto, pero isto significaba que ralentizaría algúns dispositivos. Para isto, implementamos un pequeno truco: envolvemos a maioría dos resultados da busca en comentarios HTML (<code><!-- --></code>), e logo escribimos un pequeno Javascript que detectaría cando un resultado debería facerse visible, momento no que desenvolveriamos o comentario: A "virtualización" do DOM implementouse en 23 liñas, sen necesidade de bibliotecas sofisticadas! Este é o tipo de código rápido e pragmático que se obtén cando se ten pouco tempo e problemas reais que resolver. Informouse de que a nosa busca agora funciona ben en dispositivos lentos! Outro gran esforzo foi automatizar a construción da base de datos. Cando lanzamos, simplemente xuntamos diferentes fontes ao azar. Agora queremos mantelas actualizadas, polo que escribimos un conxunto de scripts para descargar novos metadata dos dous forks de Library Genesis e integralos. O obxectivo non é só facer isto útil para o noso arquivo, senón tamén facilitar as cousas a calquera que queira experimentar cos metadata da biblioteca na sombra. O obxectivo sería un caderno de Jupyter que teña todo tipo de metadata interesante dispoñible, para que poidamos facer máis investigacións como descubrir que <a %(blog)s>porcentaxe de ISBNs se preservan para sempre</a>. Finalmente, renovamos o noso sistema de doazóns. Agora pode usar unha tarxeta de crédito para depositar diñeiro directamente nas nosas carteiras de criptomoedas, sen realmente necesitar saber nada sobre criptomoedas. Seguiremos monitorizando como funciona isto na práctica, pero é un gran avance. Co peche de Z-Library e a detención dos seus (presuntos) fundadores, estivemos traballando sen descanso para ofrecer unha boa alternativa co Arquivo de Anna (non o enlazaremos aquí, pero podes buscalo en Google). Aquí están algunhas das cousas que logramos recentemente. Actualización de Anna: arquivo totalmente de código aberto, ElasticSearch, máis de 300GB de portadas de libros Estivemos traballando sen descanso para ofrecer unha boa alternativa co Arquivo de Anna. Aquí están algunhas das cousas que logramos recentemente. Análise Os duplicados semánticos (diferentes escaneos do mesmo libro) poden teoricamente ser filtrados, pero é complicado. Ao revisar manualmente os cómics atopamos demasiados falsos positivos. Hai algúns duplicados puramente por MD5, o que é relativamente desperdiciador, pero filtralos só nos daría un aforro de aproximadamente 1% in. A esta escala aínda é aproximadamente 1TB, pero tamén, a esta escala 1TB realmente non importa. Preferimos non arriscar a destruír datos accidentalmente neste proceso. Atopamos un montón de datos non relacionados con libros, como películas baseadas en cómics. Iso tamén parece desperdiciador, xa que xa están amplamente dispoñibles por outros medios. Non obstante, decatámonos de que non podiamos simplemente filtrar os ficheiros de películas, xa que tamén hai <em>libros de cómics interactivos</em> que foron lanzados no ordenador, que alguén gravou e gardou como películas. En definitiva, calquera cousa que puidésemos eliminar da colección só aforraría uns poucos por cento. Entón lembramos que somos acumuladores de datos, e as persoas que van espellar isto tamén son acumuladores de datos, así que, "¡QUE QUERES DICIR, ELIMINAR?!" :) Cando recibes 95TB no teu clúster de almacenamento, tentas entender o que hai alí… Fixemos algunha análise para ver se podiamos reducir un pouco o tamaño, como eliminando duplicados. Aquí están algúns dos nosos achados: Por iso, estamos presentándovos a colección completa e sen modificar. É moita información, pero esperamos que a suficiente xente se preocupe por compartila de todos os xeitos. Colaboración Dada a súa magnitude, esta colección leva moito tempo na nosa lista de desexos, así que despois do noso éxito coa copia de seguridade de Z-Library, fixamos a nosa atención nesta colección. Ao principio, raspámola directamente, o que foi todo un reto, xa que o seu servidor non estaba nas mellores condicións. Conseguimos uns 15TB deste xeito, pero foi un proceso lento. Por sorte, conseguimos contactar co operador da biblioteca, que aceptou enviarnos todos os datos directamente, o que foi moito máis rápido. Aínda así, levou máis de medio ano transferir e procesar todos os datos, e case perdemos todo debido a unha corrupción do disco, o que significaría comezar de novo. Esta experiencia fíxonos crer que é importante sacar estes datos ao público o máis rápido posible, para que poidan ser espellados amplamente. Estamos a só un ou dous incidentes desafortunados de perder esta colección para sempre! A colección Moverse rápido significa que a colección está un pouco desorganizada… Imos botarlle un ollo. Imaxina que temos un sistema de ficheiros (que na realidade estamos dividindo en torrents): O primeiro directorio, <code>/repository</code>, é a parte máis estruturada disto. Este directorio contén os chamados “mil dirs”: directorios cada un con mil ficheiros, que están numerados incrementalmente na base de datos. O directorio <code>0</code> contén ficheiros con comic_id 0–999, e así sucesivamente. Este é o mesmo esquema que Library Genesis leva usando para as súas coleccións de ficción e non ficción. A idea é que cada “mil dir” se converta automaticamente nun torrent tan pronto como se encha. Non obstante, o operador de Libgen.li nunca fixo torrents para esta colección, e así os mil dirs probablemente se volveron incómodos, e deron paso a “dirs non clasificados”. Estes son <code>/comics0</code> a <code>/comics4</code>. Todos conteñen estruturas de directorios únicas, que probablemente tiñan sentido para recoller os ficheiros, pero agora non nos fan moito sentido. Por sorte, o metadata aínda se refire directamente a todos estes ficheiros, polo que a súa organización de almacenamento no disco realmente non importa! O metadata está dispoñible en forma dunha base de datos MySQL. Pódese descargar directamente desde o sitio web de Libgen.li, pero tamén o faremos dispoñible nun torrent, xunto coa nosa propia táboa con todos os hashes MD5. <q>A Dra. Barbara Gordon intenta perderse no mundo cotián da biblioteca…</q> Forks de Libgen Primeiro, un pouco de contexto. Pode que coñezas Library Genesis pola súa épica colección de libros. Menos xente sabe que os voluntarios de Library Genesis crearon outros proxectos, como unha considerable colección de revistas e documentos estándar, unha copia de seguridade completa de Sci-Hub (en colaboración coa fundadora de Sci-Hub, Alexandra Elbakyan) e, de feito, unha enorme colección de cómics. Nalgún momento, diferentes operadores dos espellos de Library Genesis seguiron camiños separados, o que deu lugar á situación actual de ter varios "forks" diferentes, todos aínda co nome de Library Genesis. O fork de Libgen.li ten de forma única esta colección de cómics, así como unha considerable colección de revistas (na que tamén estamos a traballar). Recaudación de fondos Estamos lanzando estes datos en grandes bloques. O primeiro torrent é de <code>/comics0</code>, que puxemos nun enorme ficheiro .tar de 12TB. Iso é mellor para o teu disco duro e software de torrent que un millón de ficheiros máis pequenos. Como parte deste lanzamento, estamos facendo unha recaudación de fondos. Estamos buscando recadar 20.000 dólares para cubrir os custos operativos e de contratación para esta colección, así como para habilitar proxectos en curso e futuros. Temos algúns <em>enormes</em> en proceso. <em>¿A quen estou apoiando coa miña doazón?</em> En resumo: estamos respaldando todo o coñecemento e cultura da humanidade, e facéndoo facilmente accesible. Todo o noso código e datos son de código aberto, somos un proxecto completamente dirixido por voluntarios, e ata agora salvamos 125TB de libros (ademais dos torrents existentes de Libgen e Scihub). En última instancia, estamos construíndo un volante que habilita e incentiva ás persoas a atopar, escanear e respaldar todos os libros do mundo. Escribiremos sobre o noso plan mestre nunha publicación futura. :) Se doas para unha subscrición de 12 meses de “Amazing Archivist” (780 dólares), podes <strong>“adoptar un torrent”</strong>, o que significa que poñeremos o teu nome de usuario ou mensaxe no nome dun dos torrents! Podes doar indo a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a> e facendo clic no botón “Doar”. Tamén estamos buscando máis voluntarios: enxeñeiros de software, investigadores de seguridade, expertos en comercio anónimo e tradutores. Tamén podes apoiarnos proporcionando servizos de hospedaxe. E, por suposto, por favor comparte os nosos torrents! Grazas a todos os que xa nos apoiaron tan xenerosamente! Realmente estás facendo unha diferenza. Aquí están os torrents lanzados ata agora (aínda estamos procesando o resto): Todos os torrents pódense atopar en <a %(wikipedia_annas_archive)s>Arquivo de Anna</a> baixo “Datasets” (non enlazamos directamente alí, para que as ligazóns a este blog non sexan eliminadas de Reddit, Twitter, etc). Desde alí, segue a ligazón ao sitio web de Tor. <a %(news_ycombinator)s>Discutir en Hacker News</a> Que segue? Un conxunto de torrents son xeniais para a preservación a longo prazo, pero non tanto para o acceso diario. Traballaremos con socios de hospedaxe para subir todos estes datos á web (xa que o Arquivo de Anna non hospeda nada directamente). Por suposto, poderás atopar estas ligazóns de descarga no Arquivo de Anna. Tamén estamos invitando a todos a facer cousas con estes datos! Axúdanos a analizalos mellor, deduplicalos, poñelos en IPFS, mesturalos, adestrar os teus modelos de IA con eles, e así por diante. Son todos teus, e estamos ansiosos por ver o que fas con eles. Finalmente, como dixemos antes, aínda temos algúns lanzamentos enormes por vir (se <em>alguén</em> puidese <em>accidentalmente</em> enviarnos un volcado dunha <em>certa</em> base de datos ACS4, xa sabes onde atoparnos...), así como construír o volante para respaldar todos os libros do mundo. Así que mantente atento, acabamos de comezar. - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) A maior biblioteca na sombra de cómics probablemente sexa a dun fork particular de Library Genesis: Libgen.li. O único administrador que xestiona ese sitio conseguiu reunir unha colección de cómics incrible de máis de 2 millóns de ficheiros, totalizando máis de 95TB. Non obstante, a diferenza doutras coleccións de Library Genesis, esta non estaba dispoñible en masa a través de torrents. Só podía acceder a estes cómics individualmente a través do seu servidor persoal lento: un único punto de fallo. Ata hoxe! Nesta publicación contarémosche máis sobre esta colección e sobre a nosa recadación de fondos para apoiar máis este traballo. O Arquivo de Anna fixo unha copia de seguridade da maior biblioteca na sombra de cómics do mundo (95TB): pode axudar a sementala A maior biblioteca na sombra de cómics do mundo tiña un único punto de fallo... ata hoxe. Advertencia: esta publicación no blog foi obsoleta. Decidimos que IPFS aínda non está listo para o horario de máxima audiencia. Aínda ligaremos a arquivos en IPFS desde o Arquivo de Anna cando sexa posible, pero non o aloxaremos nós mesmos máis, nin recomendamos a outros que espellen usando IPFS. Consulta a nosa páxina de Torrents se queres axudar a preservar a nosa colección. Poñendo 5,998,794 libros en IPFS Unha multiplicación de copias Volvendo á nosa pregunta orixinal: como podemos afirmar que preservamos as nosas coleccións para sempre? O principal problema aquí é que a nosa colección estivo <a %(torrents_stats)s>crecendo</a> a un ritmo rápido, raspando e abrindo algunhas coleccións masivas (ademais do traballo incrible xa feito por outras bibliotecas de datos abertos como Sci-Hub e Library Genesis). Este crecemento de datos fai que sexa máis difícil que as coleccións se espellen por todo o mundo. O almacenamento de datos é caro! Pero somos optimistas, especialmente ao observar as seguintes tres tendencias. O <a %(annas_archive_stats)s>tamaño total</a> das nosas coleccións, nos últimos meses, desglosado polo número de seeders de torrent. Tendencias de prezos de HDD de diferentes fontes (fai clic para ver o estudo). <a %(critical_window_chinese)s>Versión en chinés 中文版</a>, discute en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Collemos o froito máis baixo Este segue directamente das nosas prioridades discutidas anteriormente. Preferimos traballar en liberar grandes coleccións primeiro. Agora que aseguramos algunhas das coleccións máis grandes do mundo, esperamos que o noso crecemento sexa moito máis lento. Aínda hai unha longa cola de coleccións máis pequenas, e novos libros escanéanse ou publícanse todos os días, pero a taxa probablemente será moito máis lenta. Aínda podemos duplicar ou incluso triplicar o tamaño, pero nun período de tempo máis longo. Melloras no OCR. Prioridades Código de software de ciencia e enxeñaría Versións ficticias ou de entretemento de todo o anterior Datos xeográficos (por exemplo, mapas, estudos xeolóxicos) Datos internos de corporacións ou gobernos (filtracións) Datos de medición como medicións científicas, datos económicos, informes corporativos Rexistros de metadata en xeral (de non ficción e ficción; doutros medios, arte, persoas, etc.; incluíndo reseñas) Libros de non ficción Revistas de non ficción, xornais, manuais Transcricións de non ficción de charlas, documentais, podcasts Datos orgánicos como secuencias de ADN, sementes de plantas ou mostras microbianas Artigos académicos, revistas, informes Sitios web de ciencia e enxeñaría, discusións en liña Transcricións de procedementos legais ou xudiciais Únicamente en risco de destrución (por exemplo, por guerra, recortes de financiamento, demandas ou persecución política) Raras Únicamente desatendidas Por que nos importan tanto os artigos e os libros? Deixemos de lado a nosa crenza fundamental na preservación en xeral — poderiamos escribir outra publicación sobre iso. Entón, por que artigos e libros especificamente? A resposta é sinxela: <strong>densidade de información</strong>. Por megabyte de almacenamento, o texto escrito almacena a maior cantidade de información de todos os medios. Aínda que nos importan tanto o coñecemento como a cultura, preocúpanos máis o primeiro. En xeral, atopamos unha xerarquía de densidade de información e importancia da preservación que se parece aproximadamente a isto: A clasificación nesta lista é algo arbitraria: varios elementos están empatados ou hai desacordos dentro do noso equipo, e probablemente estamos esquecendo algunhas categorías importantes. Pero isto é aproximadamente como priorizamos. Algúns destes elementos son demasiado diferentes dos outros para que nos preocupemos (ou xa están atendidos por outras institucións), como os datos orgánicos ou os datos xeográficos. Pero a maioría dos elementos desta lista son realmente importantes para nós. Outro gran factor na nosa priorización é o risco que corre unha determinada obra. Preferimos centrarnos en obras que son: Finalmente, preocúpanos a escala. Temos tempo e diñeiro limitados, polo que preferimos pasar un mes salvando 10.000 libros que 1.000 libros, se son aproximadamente igual de valiosos e están en risco. <em><q>O perdido non se pode recuperar; pero salvemos o que queda: non por cámaras e peches que os afastan da vista e uso do público, consignándoos ao desperdicio do tempo, senón por unha multiplicación de copias, que os coloque fóra do alcance do accidente.</q></em><br>— Thomas Jefferson, 1791 Bibliotecas sombra O código pode ser de código aberto en Github, pero Github no seu conxunto non se pode espellar facilmente e, polo tanto, preservar (aínda que neste caso particular hai copias suficientemente distribuídas da maioría dos repositorios de código) Os rexistros de metadata pódense ver libremente no sitio web de Worldcat, pero non se poden descargar en masa (ata que os <a %(worldcat_scrape)s>raspamos</a>) Reddit é gratuíto de usar, pero recentemente puxo en marcha medidas anti-raspado estritas, á luz do adestramento de LLM famento de datos (máis sobre iso máis adiante) Hai moitas organizacións que teñen misións similares e prioridades similares. De feito, hai bibliotecas, arquivos, laboratorios, museos e outras institucións encargadas da preservación deste tipo. Moitas delas están ben financiadas por gobernos, individuos ou corporacións. Pero teñen un enorme punto cego: o sistema legal. Aquí reside o papel único das bibliotecas sombra e a razón pola que existe o Arquivo de Anna. Podemos facer cousas que outras institucións non están permitidas facer. Agora, non é (a miúdo) que poidamos arquivar materiais que son ilegais de preservar noutros lugares. Non, é legal en moitos lugares construír un arquivo con calquera libro, papel, revista, e así por diante. Pero o que a miúdo lles falta aos arquivos legais é <strong>redundancia e lonxevidade</strong>. Existen libros dos que só hai unha copia nunha biblioteca física nalgún lugar. Existen rexistros de metadata gardados por unha única corporación. Existen xornais só preservados en microfilme nun único arquivo. As bibliotecas poden sufrir recortes de financiamento, as corporacións poden quebrar, os arquivos poden ser bombardeados e queimados ata os cimentos. Isto non é hipotético: isto sucede todo o tempo. O que podemos facer de forma única en O Arquivo de Anna é almacenar moitas copias de obras, a gran escala. Podemos recoller artigos, libros, revistas e máis, e distribuílos en masa. Actualmente facémolo a través de torrents, pero as tecnoloxías exactas non importan e cambiarán co tempo. A parte importante é conseguir que moitas copias se distribúan por todo o mundo. Esta cita de hai máis de 200 anos aínda resoa: Unha nota rápida sobre o dominio público. Dado que O Arquivo de Anna se centra de forma única en actividades que son ilegais en moitos lugares do mundo, non nos molestamos con coleccións amplamente dispoñibles, como os libros de dominio público. As entidades legais a miúdo xa coidan ben diso. Non obstante, hai consideracións que nos fan traballar ás veces en coleccións dispoñibles publicamente: - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Os custos de almacenamento seguen caendo exponencialmente 3. Melloras na densidade da información Actualmente almacenamos libros nos formatos orixinais nos que se nos entregan. Claro, están comprimidos, pero a miúdo aínda son grandes escaneos ou fotografías de páxinas. Ata agora, as únicas opcións para reducir o tamaño total da nosa colección foron a través dunha compresión máis agresiva ou a deduplicación. Non obstante, para obter aforros significativos, ambas son demasiado perdas para o noso gusto. A compresión pesada de fotos pode facer que o texto sexa apenas lexible. E a deduplicación require unha alta confianza de que os libros sexan exactamente iguais, o que a miúdo é demasiado inexacto, especialmente se os contidos son os mesmos pero os escaneos se fan en ocasións diferentes. Sempre houbo unha terceira opción, pero a súa calidade foi tan abismal que nunca a consideramos: <strong>OCR, ou recoñecemento óptico de caracteres</strong>. Este é o proceso de converter fotos en texto simple, usando IA para detectar os caracteres nas fotos. As ferramentas para isto existen desde hai tempo e son bastante decentes, pero "bastante decentes" non é suficiente para fins de preservación. Non obstante, os recentes modelos de aprendizaxe profunda multimodal fixeron un progreso extremadamente rápido, aínda que a custos elevados. Agardamos que tanto a precisión como os custos melloren dramaticamente nos próximos anos, ata o punto de que será realista aplicalo a toda a nosa biblioteca. Cando iso suceda, probablemente aínda preservaremos os ficheiros orixinais, pero ademais poderiamos ter unha versión moito máis pequena da nosa biblioteca que a maioría da xente quererá espellar. O truco é que o texto en bruto en si mesmo comprímese aínda mellor e é moito máis doado de deduplicar, dándonos aínda máis aforros. En xeral, non é irrealista esperar polo menos unha redución de 5-10 veces no tamaño total dos ficheiros, quizais incluso máis. Mesmo cunha redución conservadora de 5 veces, estaríamos mirando <strong>1.000–3.000 dólares en 10 anos, mesmo se a nosa biblioteca triplica o seu tamaño</strong>. No momento de escribir isto, os <a %(diskprices)s>prezos dos discos</a> por TB están ao redor de $12 para discos novos, $8 para discos usados e $4 para cinta. Se somos conservadores e só miramos discos novos, iso significa que almacenar un petabyte custa uns $12,000. Se asumimos que a nosa biblioteca triplicará de 900TB a 2.7PB, iso significaría $32,400 para espellar toda a nosa biblioteca. Engadindo electricidade, custo doutro hardware, e así por diante, redondeamos a $40,000. Ou con cinta máis como $15,000–$20,000. Por unha banda <strong>$15,000–$40,000 pola suma de todo o coñecemento humano é unha ganga</strong>. Por outra banda, é un pouco elevado esperar toneladas de copias completas, especialmente se tamén queremos que esas persoas sigan sementando os seus torrents para o beneficio doutros. Iso é hoxe. Pero o progreso avanza: Os custos dos discos duros por TB reducíronse aproximadamente a un terzo nos últimos 10 anos, e probablemente seguirán caendo a un ritmo similar. A cinta parece estar nunha traxectoria similar. Os prezos dos SSD están caendo aínda máis rápido, e poderían superar os prezos dos HDD a finais da década. Se isto se mantén, entón en 10 anos poderiamos estar mirando só $5,000–$13,000 para espellar toda a nosa colección (1/3), ou incluso menos se crecemos menos en tamaño. Aínda que segue sendo moito diñeiro, isto será alcanzable para moitas persoas. E podería ser aínda mellor debido ao seguinte punto… En O Arquivo de Anna, a miúdo pregúntannos como podemos afirmar que preservamos as nosas coleccións para sempre, cando o tamaño total xa está a achegarse a 1 Petabyte (1000 TB), e aínda está a medrar. Neste artigo veremos a nosa filosofía, e veremos por que a próxima década é crítica para a nosa misión de preservar o coñecemento e a cultura da humanidade. Xanela crítica Se estas previsións son precisas, <strong>só necesitamos esperar un par de anos</strong> antes de que toda a nosa colección sexa amplamente espellada. Así, nas palabras de Thomas Jefferson, "colocada fóra do alcance do accidente". Desafortunadamente, a chegada dos LLMs, e o seu adestramento famento de datos, puxo a moitos titulares de dereitos de autor á defensiva. Aínda máis do que xa estaban. Moitos sitios web están facendo máis difícil raspar e arquivar, as demandas están voando, e mentres tanto as bibliotecas físicas e os arquivos seguen sendo descoidados. Só podemos esperar que estas tendencias continúen empeorando, e moitas obras se perdan moito antes de que entren no dominio público. <strong>Estamos á véspera dunha revolución na preservación, pero <q>o perdido non se pode recuperar.</q></strong> Temos unha xanela crítica de aproximadamente 5-10 anos durante a cal aínda é bastante caro operar unha biblioteca na sombra e crear moitos espellos ao redor do mundo, e durante a cal o acceso aínda non foi completamente pechado. Se podemos cruzar esta xanela, entón realmente teremos preservado o coñecemento e a cultura da humanidade para sempre. Non debemos deixar que este tempo se desperdicie. Non debemos deixar que esta xanela crítica se peche sobre nós. Imos aló. A xanela crítica das bibliotecas de sombra Como podemos afirmar que preservamos as nosas coleccións para sempre, cando xa están a achegarse a 1 PB? Colección Algúns detalles máis sobre a colección. <a %(duxiu)s>Duxiu</a> é unha base de datos masiva de libros escaneados, creada polo <a %(chaoxing)s>SuperStar Digital Library Group</a>. A maioría son libros académicos, escaneados para facelos dispoñibles dixitalmente para universidades e bibliotecas. Para o noso público de fala inglesa, <a %(library_princeton)s>Princeton</a> e a <a %(guides_lib_uw)s>Universidade de Washington</a> teñen boas visións xerais. Tamén hai un excelente artigo que ofrece máis contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (búscao no Arquivo de Anna). Os libros de Duxiu levan moito tempo sendo pirateados na internet chinesa. Normalmente véndense por menos dun dólar por revendedores. Xeralmente distribúense usando o equivalente chinés de Google Drive, que a miúdo foi hackeado para permitir máis espazo de almacenamento. Algúns detalles técnicos pódense atopar <a %(github_duty_machine)s>aquí</a> e <a %(github_821_github_io)s>aquí</a>. Aínda que os libros foron distribuídos semi-publicamente, é bastante difícil obtelos en masa. Tiñamos isto alto na nosa lista de tarefas, e asignamos varios meses de traballo a tempo completo para iso. Non obstante, recentemente un voluntario incrible, asombroso e talentoso contactou connosco, dicíndonos que xa fixera todo este traballo — a gran custo. Compartiron a colección completa connosco, sen esperar nada a cambio, excepto a garantía de preservación a longo prazo. Verdadeiramente notable. Acordaron pedir axuda deste xeito para obter a colección OCR. A colección consta de 7.543.702 arquivos. Isto é máis que a non-ficción de Library Genesis (aproximadamente 5,3 millóns). O tamaño total dos arquivos é de aproximadamente 359TB (326TiB) na súa forma actual. Estamos abertos a outras propostas e ideas. Só contacta connosco. Consulta o Arquivo de Anna para máis información sobre as nosas coleccións, esforzos de preservación, e como podes axudar. Grazas! Páxinas de exemplo Para demostrarnos que tes unha boa cadea de procesamento, aquí tes algunhas páxinas de exemplo para comezar, dun libro sobre superconductores. A túa cadea de procesamento debería manexar correctamente matemáticas, táboas, gráficos, notas ao pé, etc. Envía as túas páxinas procesadas ao noso correo electrónico. Se teñen boa pinta, enviarémosche máis en privado, e esperamos que poidas executar rapidamente a túa cadea de procesamento sobre esas tamén. Unha vez que esteamos satisfeitos, podemos facer un trato. - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versión chinesa 中文版</a>, <a %(news_ycombinator)s>Discutir en Hacker News</a> Este é un breve post no blog. Estamos buscando algunha empresa ou institución que nos axude co OCR e a extracción de texto para unha colección masiva que adquirimos, a cambio de acceso exclusivo anticipado. Despois do período de embargo, por suposto, liberaremos toda a colección. Os textos académicos de alta calidade son extremadamente útiles para o adestramento de LLMs. Aínda que a nosa colección é chinesa, isto debería ser útil incluso para adestrar LLMs en inglés: os modelos parecen codificar conceptos e coñecementos independentemente do idioma de orixe. Para isto, o texto necesita ser extraído das escaneos. Que obtén o Arquivo de Anna disto? Busca de texto completo dos libros para os seus usuarios. Porque os nosos obxectivos se aliñan cos dos desenvolvedores de LLM, estamos buscando un colaborador. Estamos dispostos a darche <strong>acceso anticipado exclusivo a esta colección en masa durante 1 ano</strong>, se podes facer un OCR e extracción de texto adecuados. Se estás disposto a compartir todo o código da túa cadea de procesamento connosco, estaríamos dispostos a embargar a colección por máis tempo. Acceso exclusivo para empresas de LLM á maior colección de libros de non ficción chineses do mundo <em><strong>TL;DR:</strong> O Arquivo de Anna adquiriu unha colección única de 7,5 millóns / 350TB de libros de non ficción chineses, máis grande que Library Genesis. Estamos dispostos a dar a unha empresa de LLM acceso exclusivo, a cambio de OCR de alta calidade e extracción de texto.</em> Arquitectura do sistema Entón, digamos que atopaches algunhas empresas dispostas a aloxar o teu sitio web sen derrubalo: chamémoslles “provedores amantes da liberdade” 😄. Pronto descubrirás que aloxar todo con eles é bastante caro, polo que quizais queiras atopar algúns “provedores baratos” e facer o aloxamento real alí, proxectando a través dos provedores amantes da liberdade. Se o fas ben, os provedores baratos nunca saberán o que estás aloxando e nunca recibirán queixas. Con todos estes provedores hai o risco de que te derruben de todos os xeitos, polo que tamén necesitas redundancia. Necesitamos isto en todos os niveis da nosa pila. Unha empresa algo amante da liberdade que se puxo nunha posición interesante é Cloudflare. Eles <a %(blog_cloudflare)s>argumentaron</a> que non son un provedor de aloxamento, senón unha utilidade, como un ISP. Polo tanto, non están suxeitos a solicitudes de retirada de DMCA ou outras, e redirixen calquera solicitude ao teu provedor de aloxamento real. Chegaron ao punto de ir a xuízo para protexer esta estrutura. Polo tanto, podemos usalos como outra capa de caché e protección. Cloudflare non acepta pagos anónimos, polo que só podemos usar o seu plan gratuíto. Isto significa que non podemos usar as súas funcións de balanceo de carga ou failover. Polo tanto, <a %(annas_archive_l255)s>implementamos isto nós mesmos</a> a nivel de dominio. Ao cargar a páxina, o navegador comprobará se o dominio actual aínda está dispoñible, e se non, reescribe todas as URL a un dominio diferente. Dado que Cloudflare almacena en caché moitas páxinas, isto significa que un usuario pode aterrar no noso dominio principal, mesmo se o servidor proxy está caído, e logo no seguinte clic ser trasladado a outro dominio. Aínda temos tamén preocupacións operativas normais coas que tratar, como monitorizar a saúde do servidor, rexistrar erros de backend e frontend, e así por diante. A nosa arquitectura de failover permite unha maior robustez neste aspecto tamén, por exemplo, executando un conxunto completamente diferente de servidores nun dos dominios. Incluso podemos executar versións máis antigas do código e datasets neste dominio separado, no caso de que un erro crítico na versión principal pase desapercibido. Tamén podemos protexernos contra Cloudflare volvéndose contra nós, eliminándoo dun dos dominios, como este dominio separado. Diferentes permutacións destas ideas son posibles. Conclusión Foi unha experiencia interesante aprender a configurar un motor de busca de biblioteca na sombra robusto e resiliente. Hai moitos máis detalles para compartir en publicacións posteriores, así que avísame sobre o que che gustaría aprender máis! Como sempre, estamos buscando doazóns para apoiar este traballo, así que asegúrate de visitar a páxina de Doazóns no Arquivo de Anna. Tamén estamos buscando outros tipos de apoio, como subvencións, patrocinadores a longo prazo, provedores de pago de alto risco, quizais incluso anuncios (de bo gusto!). E se queres contribuír co teu tempo e habilidades, sempre estamos buscando desenvolvedores, tradutores, etc. Grazas polo teu interese e apoio. Tokens de innovación Comecemos coa nosa pila tecnolóxica. É deliberadamente aburrida. Usamos Flask, MariaDB e ElasticSearch. E iso é literalmente todo. A busca é en gran medida un problema resolto, e non pretendemos reinventalo. Ademais, temos que gastar os nosos <a %(mcfunley)s>tokens de innovación</a> noutra cousa: non ser derrubados polas autoridades. Entón, que tan legal ou ilegal é exactamente o Arquivo de Anna? Isto depende principalmente da xurisdición legal. A maioría dos países cren nalgunha forma de copyright, o que significa que ás persoas ou empresas se lles asigna un monopolio exclusivo sobre certos tipos de obras durante un período de tempo determinado. Como nota á marxe, no Arquivo de Anna cremos que, aínda que hai algúns beneficios, en xeral o copyright é un neto negativo para a sociedade, pero esa é unha historia para outro momento. Este monopolio exclusivo sobre certas obras significa que é ilegal para calquera fóra deste monopolio distribuír directamente esas obras, incluíndonos a nós. Pero o Arquivo de Anna é un motor de busca que non distribúe directamente esas obras (polo menos non no noso sitio web en clearnet), así que deberiamos estar ben, non? Non exactamente. En moitas xurisdicións non só é ilegal distribuír obras con copyright, senón tamén enlazar a lugares que o fan. Un exemplo clásico disto é a lei DMCA dos Estados Unidos. Ese é o extremo máis estrito do espectro. No outro extremo do espectro podería teoricamente haber países sen leis de copyright, pero estes realmente non existen. Prácticamente todos os países teñen algunha forma de lei de copyright nos seus libros. A aplicación é unha historia diferente. Hai moitos países con gobernos que non se preocupan por facer cumprir a lei de copyright. Tamén hai países entre os dous extremos, que prohiben distribuír obras con copyright, pero non prohiben enlazar a tales obras. Outra consideración é a nivel de empresa. Se unha empresa opera nunha xurisdición que non se preocupa polo copyright, pero a propia empresa non está disposta a asumir ningún risco, entón poderían pechar o seu sitio web tan pronto como alguén se queixe del. Finalmente, unha gran consideración son os pagos. Como necesitamos permanecer anónimos, non podemos usar métodos de pago tradicionais. Isto déixanos coas criptomoedas, e só un pequeno subconxunto de empresas as soporta (hai tarxetas de débito virtuais pagadas con cripto, pero a miúdo non son aceptadas). - Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Eu opero <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, o maior motor de busca de código aberto sen ánimo de lucro do mundo para <a %(wikipedia_shadow_library)s>bibliotecas na sombra</a>, como Sci-Hub, Library Genesis e Z-Library. O noso obxectivo é facer que o coñecemento e a cultura sexan facilmente accesibles, e finalmente construír unha comunidade de persoas que xuntas arquiven e preserven <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos os libros do mundo</a>. Neste artigo amosarei como operamos este sitio web, e os desafíos únicos que veñen con operar un sitio web cun estado legal cuestionable, xa que non hai “AWS para caridades na sombra”. <em>Consulta tamén o artigo irmán <a %(blog_how_to_become_a_pirate_archivist)s>Como converterse nun arquivista pirata</a>.</em> Como operar unha biblioteca na sombra: operacións no Arquivo de Anna Non hai <q>AWS para caridades na sombra,</q> entón como operamos o Arquivo de Anna? Ferramentas Servidor de aplicacións: Flask, MariaDB, ElasticSearch, Docker. Desenvolvemento: Gitlab, Weblate, Zulip. Xestión de servidores: Ansible, Checkmk, UFW. Aloxamento estático de Onion: Tor, Nginx. Servidor proxy: Varnish. Vexamos que ferramentas usamos para lograr todo isto. Isto está evolucionando moito a medida que nos atopamos con novos problemas e atopamos novas solucións. Hai algunhas decisións nas que fomos e viñemos. Unha é a comunicación entre servidores: antes usabamos Wireguard para isto, pero descubrimos que ocasionalmente deixa de transmitir calquera dato, ou só transmite datos nunha dirección. Isto ocorreu con varias configuracións diferentes de Wireguard que probamos, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Tamén intentamos tunelar portos sobre SSH, usando autossh e sshuttle, pero atopamos <a %(github_sshuttle)s>problemas alí</a> (aínda que aínda non está claro para min se autossh sofre de problemas de TCP-sobre-TCP ou non — só me parece unha solución chapuceira pero quizais estea ben?). En vez diso, volvemos ás conexións directas entre servidores, ocultando que un servidor está funcionando nos provedores baratos usando filtrado de IP con UFW. Isto ten o inconveniente de que Docker non funciona ben con UFW, a menos que uses <code>network_mode: "host"</code>. Todo isto é un pouco máis propenso a erros, porque exporás o teu servidor a internet cunha pequena mala configuración. Quizais deberiamos volver a autossh — os comentarios serían moi benvidos aquí. Tamén fomos e viñemos entre Varnish e Nginx. Actualmente gústanos Varnish, pero ten as súas peculiaridades e arestas. O mesmo aplícase a Checkmk: non nos encanta, pero funciona por agora. Weblate estivo ben pero non incrible — ás veces temo que perda os meus datos cada vez que intento sincronizalo co noso repositorio git. Flask foi bo en xeral, pero ten algunhas peculiaridades estrañas que custaron moito tempo para depurar, como configurar dominios personalizados, ou problemas coa súa integración de SqlAlchemy. Ata agora os outros ferramentas foron xeniais: non temos queixas serias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Todos estes tiveron algúns problemas, pero nada demasiado serio ou que consuma moito tempo. Comunidade O primeiro desafío pode ser sorprendente. Non é un problema técnico, nin un problema legal. É un problema psicolóxico: facer este traballo nas sombras pode ser incrible solitario. Dependendo do que planeas facer e do teu modelo de ameaza, pode que teñas que ser moi coidadoso. Nun extremo do espectro temos persoas como Alexandra Elbakyan*, a fundadora de Sci-Hub, que é moi aberta sobre as súas actividades. Pero está en alto risco de ser arrestada se visitase un país occidental neste momento, e podería enfrontarse a décadas de prisión. É ese un risco que estarías disposto a asumir? Estamos no outro extremo do espectro; sendo moi coidadosos de non deixar ningún rastro e tendo unha forte seguridade operativa. * Como se mencionou en HN por "ynno", Alexandra inicialmente non quería ser coñecida: "Os seus servidores estaban configurados para emitir mensaxes de erro detalladas de PHP, incluíndo a ruta completa do ficheiro fonte con erro, que estaba baixo o directorio /home/<USER>" Así que, usa nomes de usuario aleatorios nos ordenadores que uses para estas cousas, no caso de que configures algo incorrectamente. Esa secrecía, con todo, vén cun custo psicolóxico. A maioría das persoas adoran ser recoñecidas polo traballo que fan, e aínda así non podes recibir ningún crédito por isto na vida real. Incluso cousas simples poden ser desafiantes, como amigos preguntándoche en que estiveches traballando (en algún momento "trasteando co meu NAS / homelab" faise vello). Por iso é tan importante atopar algunha comunidade. Podes renunciar a algo de seguridade operativa confiando en algúns amigos moi próximos, que sabes que podes confiar profundamente. Mesmo entón, ten coidado de non poñer nada por escrito, no caso de que teñan que entregar os seus correos electrónicos ás autoridades, ou se os seus dispositivos están comprometidos dalgún outro xeito. Mellor aínda é atopar algúns compañeiros piratas. Se os teus amigos próximos están interesados en unirse a ti, xenial! Se non, poderías atopar outros en liña. Tristemente, esta aínda é unha comunidade de nicho. Ata agora só atopamos un puñado de outros que están activos neste espazo. Bos lugares para comezar parecen ser os foros de Library Genesis e r/DataHoarder. O Archive Team tamén ten individuos afíns, aínda que operan dentro da lei (aínda que en algunhas áreas grises da lei). As escenas tradicionais de "warez" e piratería tamén teñen persoas que pensan de xeito similar. Estamos abertos a ideas sobre como fomentar a comunidade e explorar ideas. Non dubide en enviarnos unha mensaxe en Twitter ou Reddit. Quizais poderiamos organizar algún tipo de foro ou grupo de chat. Un dos desafíos é que isto pode ser facilmente censurado ao usar plataformas comúns, polo que teriamos que aloxalo nós mesmos. Tamén hai un equilibrio entre ter estas discusións totalmente públicas (máis potencial de participación) fronte a facelas privadas (non deixar que os potenciais "obxectivos" saiban que estamos a piques de rastrexalos). Teremos que pensar niso. Avísanos se estás interesado nisto! Conclusión Agardamos que isto sexa útil para os novos arquivistas piratas que comezan. Estamos emocionados de darvos a benvida a este mundo, así que non dubidedes en contactar. Imos preservar tanto coñecemento e cultura do mundo como poidamos, e espellalo por todas partes. Proxectos 4. Selección de datos A miúdo pode usar a metadata para determinar un subconxunto razoable de datos para descargar. Mesmo se finalmente quere descargar todos os datos, pode ser útil priorizar os elementos máis importantes primeiro, no caso de que sexa detectado e as defensas melloren, ou porque necesitaría mercar máis discos, ou simplemente porque xorde algo máis na súa vida antes de poder descargar todo. Por exemplo, unha colección pode ter múltiples edicións do mesmo recurso subxacente (como un libro ou unha película), onde unha está marcada como de mellor calidade. Gardar esas edicións primeiro tería moito sentido. Eventualmente pode querer gardar todas as edicións, xa que nalgúns casos a metadata pode estar etiquetada incorrectamente, ou pode haber compensacións descoñecidas entre edicións (por exemplo, a "mellor edición" pode ser a mellor en moitos aspectos pero peor noutros, como unha película que ten unha resolución máis alta pero carece de subtítulos). Tamén pode buscar na súa base de datos de metadata para atopar cousas interesantes. Cal é o ficheiro máis grande que está aloxado, e por que é tan grande? Cal é o ficheiro máis pequeno? Hai patróns interesantes ou inesperados en canto a certas categorías, idiomas, etc.? Hai títulos duplicados ou moi similares? Hai patróns sobre cando se engadiron os datos, como un día no que se engadiron moitos ficheiros á vez? A miúdo pode aprender moito observando o conxunto de datos de diferentes maneiras. No noso caso, deduplicamos os libros de Z-Library contra os hashes md5 en Library Genesis, aforrando así moito tempo de descarga e espazo en disco. Esta é unha situación bastante única, con todo. Na maioría dos casos non hai bases de datos completas de que ficheiros xa están adecuadamente preservados por outros piratas. Isto en si mesmo é unha gran oportunidade para alguén por aí. Sería xenial ter unha visión xeral actualizada regularmente de cousas como música e películas que xa están amplamente compartidas en sitios de torrents, e que polo tanto son de menor prioridade para incluír en espellos piratas. 6. Distribución Ten os datos, dándolle así a posesión do primeiro espello pirata do mundo do seu obxectivo (moi probablemente). En moitos aspectos a parte máis difícil xa pasou, pero a parte máis arriscada aínda está por diante. Despois de todo, ata agora estivo en segredo; voando baixo o radar. Todo o que tiña que facer era usar un bo VPN en todo momento, non encher os seus datos persoais en ningún formulario (obviamente), e quizais usar unha sesión de navegador especial (ou mesmo un ordenador diferente). Agora ten que distribuír os datos. No noso caso primeiro quixemos contribuír os libros de volta a Library Genesis, pero logo descubrimos rapidamente as dificultades nisto (clasificación de ficción vs non ficción). Así que decidimos distribuír usando torrents ao estilo de Library Genesis. Se ten a oportunidade de contribuír a un proxecto existente, entón iso podería aforrarlle moito tempo. Con todo, actualmente non hai moitos espellos piratas ben organizados por aí. Así que digamos que decide distribuír torrents vostede mesmo. Tente manter eses ficheiros pequenos, para que sexan fáciles de espellar noutros sitios web. Entón terá que sementar os torrents vostede mesmo, mentres aínda permanece anónimo. Pode usar un VPN (con ou sen reenvío de portos), ou pagar con Bitcoins mesturados por un Seedbox. Se non sabe o que significan algúns deses termos, terá moito que ler, xa que é importante que entenda os compromisos de risco aquí. Pode aloxar os ficheiros torrent en sitios web de torrents existentes. No noso caso, escollemos realmente aloxar un sitio web, xa que tamén queriamos difundir a nosa filosofía de forma clara. Pode facer isto vostede mesmo de maneira similar (usamos Njalla para os nosos dominios e aloxamento, pagado con Bitcoins mesturados), pero tamén non dubide en contactarnos para que aloxemos os seus torrents. Estamos buscando construír un índice completo de espellos piratas co tempo, se esta idea colle. En canto á selección de VPN, xa se escribiu moito sobre isto, así que só repetiremos o consello xeral de elixir por reputación. Políticas de non rexistro probadas en tribunais con longos rexistros de protección da privacidade é a opción de menor risco, na nosa opinión. Teña en conta que mesmo cando fai todo ben, nunca pode chegar a risco cero. Por exemplo, ao sementar os seus torrents, un actor estatal altamente motivado probablemente pode observar os fluxos de datos entrantes e saíntes para servidores VPN, e deducir quen é vostede. Ou simplemente pode cometer un erro dalgún xeito. Probablemente xa o fixemos, e volveremos facelo. Afortunadamente, os estados nación non se preocupan <em>tanto</em> pola piratería. Unha decisión a tomar para cada proxecto, é se publicalo usando a mesma identidade que antes, ou non. Se segue usando o mesmo nome, entón os erros na seguridade operativa de proxectos anteriores poderían volver a morderlle. Pero publicar baixo diferentes nomes significa que non constrúe unha reputación máis duradeira. Escollemos ter unha forte seguridade operativa desde o principio para poder seguir usando a mesma identidade, pero non dubidaremos en publicar baixo un nome diferente se cometemos un erro ou se as circunstancias o requiren. Facer correr a voz pode ser complicado. Como dixemos, esta aínda é unha comunidade de nicho. Inicialmente publicamos en Reddit, pero realmente conseguimos tracción en Hacker News. Por agora a nosa recomendación é publicalo en algúns lugares e ver que pasa. E de novo, contáctenos. Encantaríanos difundir a palabra de máis esforzos de arquivismo pirata. 1. Selección de dominio / filosofía Non hai escaseza de coñecemento e patrimonio cultural que salvar, o que pode ser abafador. Por iso, a miúdo é útil tomarse un momento e pensar en cal pode ser a túa contribución. Cada persoa ten unha forma diferente de pensar sobre isto, pero aquí tes algunhas preguntas que poderías facerche a ti mesmo: No noso caso, preocupábanos especialmente a preservación a longo prazo da ciencia. Coñeciamos Library Genesis, e como foi completamente espellado moitas veces usando torrents. Encantounos esa idea. Entón un día, un de nós intentou atopar algúns libros de texto científicos en Library Genesis, pero non puido atopalos, poñendo en dúbida o completa que realmente era. Logo buscamos eses libros de texto en liña, e atopámolos noutros lugares, o que plantou a semente para o noso proxecto. Mesmo antes de coñecer a Z-Library, tiñamos a idea de non tentar recoller todos eses libros manualmente, senón centrarnos en espellar coleccións existentes, e contribuílas de volta a Library Genesis. Que habilidades tes que podes usar ao teu favor? Por exemplo, se es un experto en seguridade en liña, podes atopar formas de derrotar bloqueos de IP para obxectivos seguros. Se es bo organizando comunidades, entón quizais poidas reunir a algunhas persoas ao redor dun obxectivo. É útil saber algo de programación, aínda que só sexa para manter unha boa seguridade operativa durante este proceso. Cal sería unha área de alto impacto na que centrarse? Se vas gastar X horas en arquivar pirata, entón como podes obter o maior "rendemento polo teu diñeiro"? Cales son as formas únicas nas que estás pensando sobre isto? Poderías ter algunhas ideas ou enfoques interesantes que outros poderían ter pasado por alto. Canto tempo tes para isto? O noso consello sería comezar pequeno e facer proxectos máis grandes a medida que te afás, pero pode chegar a consumir todo o teu tempo. Por que estás interesado nisto? Que che apaixona? Se podemos conseguir un grupo de persoas que arquiven todos os tipos de cousas que lles importan especificamente, iso cubriría moito! Saberás moito máis que a persoa media sobre a túa paixón, como cal é a información importante para gardar, cales son as mellores coleccións e comunidades en liña, e así por diante. 3. Extracción de metadata Data engadida/modificada: para que poida volver máis tarde e descargar ficheiros que non descargou antes (aínda que a miúdo tamén pode usar o ID ou hash para isto). Hash (md5, sha1): para confirmar que descargou o ficheiro correctamente. ID: pode ser algún ID interno, pero IDs como ISBN ou DOI tamén son útiles. Nome do ficheiro / localización Descrición, categoría, etiquetas, autores, idioma, etc. Tamaño: para calcular canto espazo en disco necesita. Imos poñernos un pouco máis técnicos aquí. Para realmente extraer a metadata dos sitios web, mantivemos as cousas bastante sinxelas. Usamos scripts de Python, ás veces curl, e unha base de datos MySQL para almacenar os resultados. Non usamos ningún software de extracción sofisticado que poida mapear sitios web complexos, xa que ata agora só necesitamos extraer un ou dous tipos de páxinas simplemente enumerando a través de ids e analizando o HTML. Se non hai páxinas facilmente enumeradas, entón pode necesitar un rastreador adecuado que intente atopar todas as páxinas. Antes de comezar a extraer un sitio web completo, intente facelo manualmente por un tempo. Pase por unhas ducias de páxinas vostede mesmo, para ter unha idea de como funciona iso. Ás veces xa se atopará con bloqueos de IP ou outro comportamento interesante deste xeito. O mesmo ocorre coa extracción de datos: antes de profundar demasiado neste obxectivo, asegúrese de que realmente pode descargar os seus datos de forma efectiva. Para sortear restricións, hai algunhas cousas que pode probar. Hai outros enderezos IP ou servidores que aloxan os mesmos datos pero non teñen as mesmas restricións? Hai algún punto final de API que non teña restricións, mentres que outros si? A que ritmo de descarga se bloquea o seu IP, e por canto tempo? Ou non está bloqueado pero ralentizado? Que pasa se crea unha conta de usuario, como cambian as cousas entón? Pode usar HTTP/2 para manter as conexións abertas, e iso aumenta a taxa á que pode solicitar páxinas? Hai páxinas que listan varios ficheiros á vez, e a información listada alí é suficiente? Cousas que probablemente queira gardar inclúen: Normalmente facemos isto en dúas etapas. Primeiro descargamos os ficheiros HTML en bruto, xeralmente directamente en MySQL (para evitar moitos ficheiros pequenos, do que falamos máis abaixo). Logo, nun paso separado, revisamos eses ficheiros HTML e analizámolos en táboas MySQL reais. Deste xeito non ten que volver descargar todo desde cero se descobre un erro no seu código de análise, xa que pode simplemente reprocesar os ficheiros HTML co novo código. Tamén é a miúdo máis fácil paralelizar o paso de procesamento, aforrando así algo de tempo (e pode escribir o código de procesamento mentres a extracción está en marcha, en lugar de ter que escribir ambos pasos á vez). Finalmente, teña en conta que para algúns obxectivos a extracción de metadata é todo o que hai. Hai algunhas coleccións enormes de metadata que non están adecuadamente preservadas. Título Selección de dominio / filosofía: Onde queres centrarte aproximadamente e por que? Cales son as túas paixóns, habilidades e circunstancias únicas que podes usar ao teu favor? Selección de obxectivo: Que colección específica vas espellar? Rastreo de metadata: Catalogar información sobre os arquivos, sen descargar realmente os arquivos (a miúdo moito máis grandes) en si mesmos. Selección de datos: Baseándose na metadata, reducir cales datos son máis relevantes para arquivar agora mesmo. Podería ser todo, pero a miúdo hai unha forma razoable de aforrar espazo e ancho de banda. Rastreo de datos: Obter realmente os datos. Distribución: Empaquetalo en torrents, anuncialo nalgún lugar, conseguir que a xente o difunda. 5. Extracción de datos Agora está listo para realmente descargar os datos en masa. Como se mencionou antes, neste punto xa debería ter descargado manualmente un montón de ficheiros, para comprender mellor o comportamento e as restricións do obxectivo. Con todo, aínda haberá sorpresas para vostede unha vez que realmente comece a descargar moitos ficheiros á vez. O noso consello aquí é principalmente mantelo sinxelo. Comece simplemente descargando un montón de ficheiros. Pode usar Python, e logo expandirse a múltiples fíos. Pero ás veces aínda máis sinxelo é xerar ficheiros Bash directamente desde a base de datos, e logo executar varios deles en múltiples ventás de terminal para escalar. Un truco técnico rápido que vale a pena mencionar aquí é usar OUTFILE en MySQL, que pode escribir en calquera lugar se desactiva "secure_file_priv" en mysqld.cnf (e asegúrese de desactivar/invalidar AppArmor se está en Linux). Almacenamos os datos en discos duros simples. Comece co que teña, e expanda lentamente. Pode ser abrumador pensar en almacenar centos de TBs de datos. Se esa é a situación que está a afrontar, simplemente poña un bo subconxunto primeiro, e no seu anuncio pida axuda para almacenar o resto. Se quere conseguir máis discos duros vostede mesmo, entón r/DataHoarder ten algúns bos recursos para conseguir boas ofertas. Tente non preocuparse demasiado por sistemas de ficheiros sofisticados. É fácil caer na madrigueira do coello configurando cousas como ZFS. Un detalle técnico do que ser consciente, con todo, é que moitos sistemas de ficheiros non manexan ben moitos ficheiros. Atopamos que unha solución sinxela é crear múltiples directorios, por exemplo, para diferentes rangos de ID ou prefixos de hash. Despois de descargar os datos, asegúrese de comprobar a integridade dos ficheiros usando hashes na metadata, se están dispoñibles. 2. Selección de obxectivo Accesible: non usa moitas capas de protección para evitar que extraia a súa metadata e datos. Información especial: ten algunha información especial sobre este obxectivo, como que dalgún xeito ten acceso especial a esta colección, ou descubriu como derrotar as súas defensas. Isto non é necesario (o noso próximo proxecto non fai nada especial), pero certamente axuda! Grande Entón, temos a nosa área que estamos a examinar, agora que colección específica imos espellar? Hai un par de cousas que fan un bo obxectivo: Cando atopamos os nosos libros de texto de ciencia en sitios web distintos de Library Genesis, intentamos descubrir como chegaron a internet. Logo atopamos a Z-Library, e decatámonos de que, aínda que a maioría dos libros non aparecen alí primeiro, finalmente acaban alí. Aprendemos sobre a súa relación con Library Genesis, e a estrutura de incentivos (financeiros) e a interface de usuario superior, ambas as cales a facían unha colección moito máis completa. Logo fixemos algunha extracción preliminar de metadata e datos, e decatámonos de que podiamos sortear os seus límites de descarga de IP, aproveitando o acceso especial dun dos nosos membros a moitos servidores proxy. Mentres explora diferentes obxectivos, xa é importante ocultar os seus rastros usando VPNs e enderezos de correo electrónico desbotables, do que falaremos máis adiante. Única: non está xa ben cuberta por outros proxectos. Cando facemos un proxecto, ten un par de fases: Estas non son fases completamente independentes, e a miúdo as ideas dunha fase posterior envíante de volta a unha fase anterior. Por exemplo, durante o rastreo de metadata podes darte conta de que o obxectivo que seleccionaches ten mecanismos defensivos máis aló do teu nivel de habilidade (como bloqueos de IP), polo que volves atrás e atopas un obxectivo diferente. - Anna e o equipo (<a %(reddit)s>Reddit</a>) Pódense escribir libros enteiros sobre o <em>por que</em> da preservación dixital en xeral, e do arquivismo pirata en particular, pero imos dar unha breve introdución para aqueles que non están moi familiarizados. O mundo está producindo máis coñecemento e cultura que nunca, pero tamén se está perdendo máis que nunca. A humanidade confía en gran medida en corporacións como editoriais académicas, servizos de streaming e empresas de redes sociais para este patrimonio, e a miúdo non demostraron ser grandes custodios. Consulte o documental Digital Amnesia, ou realmente calquera charla de Jason Scott. Hai algunhas institucións que fan un bo traballo arquivando tanto como poden, pero están limitadas pola lei. Como piratas, estamos nunha posición única para arquivar coleccións que eles non poden tocar, debido á aplicación dos dereitos de autor ou outras restricións. Tamén podemos espellar coleccións moitas veces, en todo o mundo, aumentando así as posibilidades de preservación adecuada. Por agora, non entraremos en discusións sobre os pros e contras da propiedade intelectual, a moralidade de romper a lei, reflexións sobre a censura ou o tema do acceso ao coñecemento e a cultura. Con todo iso fóra do camiño, imos mergullarnos no <em>como</em>. Compartiremos como o noso equipo se converteu en arquivistas piratas e as leccións que aprendemos ao longo do camiño. Hai moitos desafíos cando emprendes esta viaxe, e esperamos poder axudarche con algúns deles. Como converterse nun arquivista pirata O primeiro desafío pode ser sorprendente. Non é un problema técnico, nin un problema legal. É un problema psicolóxico. Antes de comezar, dúas actualizacións sobre o Espello da Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>): Recibimos algunhas doazóns extremadamente xenerosas. A primeira foi de $10k dun individuo anónimo que tamén estivo apoiando a "bookwarrior", o fundador orixinal de Library Genesis. Un agradecemento especial a bookwarrior por facilitar esta doazón. A segunda foi outro $10k dun doante anónimo, que se puxo en contacto despois do noso último lanzamento e foi inspirado para axudar. Tamén tivemos unha serie de doazóns máis pequenas. Moitas grazas por todo o voso xeneroso apoio. Temos algúns proxectos novos emocionantes en marcha que isto apoiará, así que estean atentos. Tivemos algúns problemas técnicos co tamaño do noso segundo lanzamento, pero os nosos torrents están agora activos e sementando. Tamén recibimos unha oferta xenerosa dun individuo anónimo para sementar a nosa colección nos seus servidores de moi alta velocidade, así que estamos facendo unha carga especial nas súas máquinas, despois da cal todos os demais que estean descargando a colección deberían ver unha gran mellora na velocidade. Publicacións do blog Ola, son Anna. Creei <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, a maior biblioteca na sombra do mundo. Este é o meu blog persoal, no que eu e os meus compañeiros escribimos sobre piratería, preservación dixital e máis. Conéctate comigo en <a %(reddit)s>Reddit</a>. Ten en conta que este sitio web é só un blog. Só aloxamos as nosas propias palabras aquí. Non se aloxan nin se enlazan torrents ou outros ficheiros con dereitos de autor aquí. <strong>Biblioteca</strong> - Como a maioría das bibliotecas, centramos principalmente en materiais escritos como libros. Poderiamos expandirnos a outros tipos de medios no futuro. <strong>Espello</strong> - Somos estritamente un espello de bibliotecas existentes. Centrámonos na preservación, non en facer que os libros sexan facilmente buscables e descargables (acceso) ou en fomentar unha gran comunidade de persoas que contribúan con novos libros (fonte). <strong>Pirata</strong> - Deliberadamente violamos a lei de dereitos de autor na maioría dos países. Isto permítenos facer algo que as entidades legais non poden facer: asegurarnos de que os libros sexan espellados por todas partes. <em>Non enlazamos aos arquivos desde este blog. Por favor, atópao ti mesmo.</em> - Anna e o equipo (<a %(reddit)s>Reddit</a>) Este proxecto (EDIT: trasladado a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>) ten como obxectivo contribuír á preservación e liberación do coñecemento humano. Facemos a nosa pequena e humilde contribución, seguindo os pasos dos grandes que nos precederon. O enfoque deste proxecto está ilustrado polo seu nome: A primeira biblioteca que espellamos é Z-Library. Esta é unha biblioteca popular (e ilegal). Tomaron a colección de Library Genesis e fixérona facilmente buscable. Ademais, convertéronse en moi efectivos para solicitar novas contribucións de libros, incentivando aos usuarios contribuíntes con varios beneficios. Actualmente non contribúen estes novos libros de volta a Library Genesis. E a diferenza de Library Genesis, non fan que a súa colección sexa facilmente espellable, o que impide unha ampla preservación. Isto é importante para o seu modelo de negocio, xa que cobran diñeiro por acceder á súa colección en masa (máis de 10 libros por día). Non facemos xuízos morais sobre cobrar diñeiro por acceso masivo a unha colección de libros ilegal. Non hai dúbida de que a Z-Library tivo éxito en expandir o acceso ao coñecemento e en conseguir máis libros. Estamos aquí simplemente para facer a nosa parte: asegurar a preservación a longo prazo desta colección privada. Gustaríanos invitarvos a axudar a preservar e liberar o coñecemento humano descargando e compartindo os nosos torrents. Consulte a páxina do proxecto para obter máis información sobre como están organizados os datos. Tamén nos gustaría moito invitarvos a contribuír coas vosas ideas sobre que coleccións espellar a continuación e como facelo. Xuntos podemos lograr moito. Esta é só unha pequena contribución entre innumerables outras. Grazas, por todo o que facedes. Presentando o Espello da Biblioteca Pirata: Preservando 7TB de libros (que non están en Libgen) 10% of do patrimonio escrito da humanidade preservado para sempre <strong>Google.</strong> Despois de todo, fixeron esta investigación para Google Books. Non obstante, o seu metadata non é accesible en masa e é bastante difícil de raspar. <strong>Varios sistemas de bibliotecas individuais e arquivos.</strong> Hai bibliotecas e arquivos que non foron indexados e agregados por ningún dos anteriores, a miúdo porque están infradotados, ou por outras razóns non queren compartir os seus datos con Open Library, OCLC, Google, etc. Moitos destes teñen rexistros dixitais accesibles a través de internet, e a miúdo non están moi ben protexidos, polo que se queres axudar e divertirte aprendendo sobre sistemas de bibliotecas estraños, estes son grandes puntos de partida. <strong>ISBNdb.</strong> Este é o tema desta publicación do blog. ISBNdb raspa varios sitios web para obter metadata de libros, en particular datos de prezos, que logo venden a librerías, para que poidan prezo os seus libros de acordo co resto do mercado. Dado que os ISBNs son bastante universais hoxe en día, efectivamente construíron unha "páxina web para cada libro". <strong>Open Library.</strong> Como se mencionou antes, esta é a súa misión completa. Obtiveron enormes cantidades de datos de bibliotecas de bibliotecas cooperantes e arquivos nacionais, e continúan facéndoo. Tamén teñen bibliotecarios voluntarios e un equipo técnico que están intentando deduplicar rexistros e etiquetalos con todo tipo de metadata. O mellor de todo é que o seu conxunto de datos é completamente aberto. Podes simplemente <a %(openlibrary)s>descargalo</a>. <strong>WorldCat.</strong> Este é un sitio web xestionado pola organización sen ánimo de lucro OCLC, que vende sistemas de xestión de bibliotecas. Agregan metadata de libros de moitas bibliotecas e fan que estea dispoñible a través do sitio web de WorldCat. Non obstante, tamén gañan diñeiro vendendo estes datos, polo que non están dispoñibles para descarga masiva. Teñen algúns conxuntos de datos masivos máis limitados dispoñibles para descarga, en cooperación con bibliotecas específicas. 1. Para algunha definición razoable de "para sempre". ;) 2. Por suposto, o patrimonio escrito da humanidade é moito máis que libros, especialmente hoxe en día. Para o propósito desta publicación e os nosos lanzamentos recentes estamos centrados nos libros, pero os nosos intereses van máis alá. 3. Hai moito máis que se pode dicir sobre Aaron Swartz, pero só queriamos mencionarlle brevemente, xa que xoga un papel crucial nesta historia. Co paso do tempo, máis persoas poderían atopar o seu nome por primeira vez, e posteriormente mergullarse no burato do coello por si mesmas. <strong>Copias físicas.</strong> Obviamente isto non é moi útil, xa que son só duplicados do mesmo material. Sería xenial se puidésemos preservar todas as anotacións que a xente fai nos libros, como os famosos "garabatos nos marxes" de Fermat. Pero, por desgraza, iso seguirá sendo un soño para os arquivistas. <strong>“Edicións”.</strong> Aquí contas cada versión única dun libro. Se algo é diferente, como unha portada diferente ou un prólogo diferente, conta como unha edición diferente. <strong>Arquivos.</strong> Cando se traballa con bibliotecas na sombra como Library Genesis, Sci-Hub ou Z-Library, hai unha consideración adicional. Pode haber múltiples escaneos da mesma edición. E a xente pode facer mellores versións dos arquivos existentes, escaneando o texto usando OCR ou rectificando páxinas que foron escaneadas en ángulo. Queremos contar estes arquivos como unha soa edición, o que requiriría un bo metadata ou deduplicación usando medidas de similitude de documentos. <strong>“Obras”.</strong> Por exemplo, "Harry Potter e a Cámara dos Segredos" como un concepto lóxico, que abarca todas as súas versións, como diferentes traducións e reimpresións. Esta é unha definición algo útil, pero pode ser difícil trazar a liña do que conta. Por exemplo, probablemente queiramos preservar diferentes traducións, aínda que as reimpresións con só pequenas diferenzas poden non ser tan importantes. - Anna e o equipo (<a %(reddit)s>Reddit</a>) Co Espello da Biblioteca Pirata (EDIT: trasladado a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>), o noso obxectivo é tomar todos os libros do mundo e preservalos para sempre.<sup>1</sup> Entre os nosos torrents de Z-Library e os torrents orixinais de Library Genesis, temos 11.783.153 arquivos. Pero cantos son realmente? Se deduplicásemos correctamente eses arquivos, que porcentaxe de todos os libros do mundo temos preservado? Realmente gustaríanos ter algo así: Comecemos con algúns números aproximados: En Z-Library/Libgen e Open Library hai moitos máis libros que ISBNs únicos. Significa iso que moitos deses libros non teñen ISBNs, ou simplemente falta o metadata do ISBN? Probablemente poidamos responder a esta pregunta cunha combinación de coincidencia automatizada baseada noutros atributos (título, autor, editor, etc.), incorporando máis fontes de datos e extraendo ISBNs dos propios escaneos dos libros (no caso de Z-Library/Libgen). Cantos deses ISBNs son únicos? Isto ilústrase mellor cun diagrama de Venn: Para ser máis precisos: Sorprendeunos o pouco solapamento que hai! ISBNdb ten unha gran cantidade de ISBNs que non aparecen nin en Z-Library nin en Open Library, e o mesmo ocorre (aínda que nun grao menor pero aínda substancial) cos outros dous. Isto xera moitas novas preguntas. Canto axudaría a coincidencia automatizada a etiquetar os libros que non foron etiquetados con ISBNs? Haberá moitas coincidencias e, polo tanto, un aumento do solapamento? Ademais, que pasaría se engadísemos un cuarto ou quinto conxunto de datos? Canto solapamento veríamos entón? Isto dános un punto de partida. Agora podemos ver todos os ISBNs que non estaban no conxunto de datos de Z-Library e que tampouco coinciden cos campos de título/autor. Iso pode darnos unha idea de como preservar todos os libros do mundo: primeiro rastrexando internet en busca de escaneos, logo saíndo na vida real para escanear libros. Este último incluso podería ser financiado colectivamente, ou impulsado por "recompensas" de persoas que queren ver determinados libros dixitalizados. Todo iso é unha historia para outro momento. Se queres axudar con calquera destas tarefas — análise adicional; rastrexo de máis metadata; atopar máis libros; OCR de libros; facer isto para outros dominios (por exemplo, artigos, audiolibros, películas, programas de televisión, revistas) ou incluso facer dispoñibles algúns destes datos para cousas como adestramento de modelos de linguaxe grande — por favor, contacta comigo (<a %(reddit)s>Reddit</a>). Se estás especificamente interesado na análise de datos, estamos traballando para facer que os nosos conxuntos de datos e scripts estean dispoñibles nun formato máis fácil de usar. Sería xenial se puideses simplemente bifurcar un caderno e comezar a xogar con isto. Finalmente, se queres apoiar este traballo, considera facer unha doazón. Esta é unha operación totalmente dirixida por voluntarios, e a túa contribución marca unha gran diferenza. Cada pouco axuda. Por agora aceptamos doazóns en criptomoeda; consulta a páxina de Doazóns en O Arquivo de Anna. Para unha porcentaxe, necesitamos un denominador: o número total de libros publicados.<sup>2</sup> Antes da desaparición de Google Books, un enxeñeiro do proxecto, Leonid Taycher, <a %(booksearch_blogspot)s>intentou estimar</a> este número. Chegou — en broma — a 129.864.880 (“polo menos ata o domingo”). Estimou este número construíndo unha base de datos unificada de todos os libros do mundo. Para isto, reuniu diferentes Datasets e logo fusionounos de varias maneiras. Como un breve paréntese, hai outra persoa que intentou catalogar todos os libros do mundo: Aaron Swartz, o falecido activista dixital e cofundador de Reddit.<sup>3</sup> El <a %(youtube)s>comezou Open Library</a> co obxectivo de "unha páxina web para cada libro publicado", combinando datos de moitas fontes diferentes. Acabou pagando o prezo máis alto polo seu traballo de preservación dixital cando foi procesado por descargar masivamente artigos académicos, o que levou ao seu suicidio. Non fai falta dicir que esta é unha das razóns polas que o noso grupo é pseudónimo e por que estamos sendo moi coidadosos. Open Library aínda está sendo heroicamente xestionada por persoas no Internet Archive, continuando o legado de Aaron. Volveremos a isto máis adiante nesta publicación. No blog de Google, Taycher describe algúns dos desafíos para estimar este número. Primeiro, que constitúe un libro? Hai algunhas definicións posibles: As “Edicións” parecen a definición máis práctica do que son os “libros”. Convenientemente, esta definición tamén se usa para asignar números ISBN únicos. Un ISBN, ou Número Internacional Normalizado do Libro, úsase comúnmente para o comercio internacional, xa que está integrado co sistema internacional de códigos de barras ("Número Internacional de Artigo"). Se queres vender un libro en tendas, necesita un código de barras, polo que obtés un ISBN. A publicación do blog de Taycher menciona que, aínda que os ISBNs son útiles, non son universais, xa que só foron realmente adoptados a mediados dos anos setenta, e non en todo o mundo. Aínda así, o ISBN é probablemente o identificador máis amplamente usado das edicións de libros, polo que é o noso mellor punto de partida. Se podemos atopar todos os ISBNs do mundo, obtemos unha lista útil de que libros aínda necesitan ser preservados. Entón, de onde obtemos os datos? Hai unha serie de esforzos existentes que están intentando compilar unha lista de todos os libros do mundo: Nesta publicación, estamos felices de anunciar un pequeno lanzamento (en comparación cos nosos lanzamentos anteriores de Z-Library). Raspamos a maior parte de ISBNdb e fixemos que os datos estean dispoñibles para torrenting no sitio web do Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>; non o enlazaremos aquí directamente, só búscao). Estes son aproximadamente 30,9 millóns de rexistros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4,4GB comprimidos). No seu sitio web afirman que en realidade teñen 32,6 millóns de rexistros, polo que poderiamos ter perdido algúns, ou <em>eles</em> poderían estar facendo algo mal. En calquera caso, por agora non compartiremos exactamente como o fixemos: deixaremos iso como un exercicio para o lector. ;-) O que compartiremos é unha análise preliminar, para intentar achegarnos a estimar o número de libros no mundo. Observamos tres conxuntos de datos: este novo conxunto de datos de ISBNdb, o noso lanzamento orixinal de metadata que raspamos da biblioteca na sombra de Z-Library (que inclúe Library Genesis) e o volcado de datos de Open Library. Volcado de ISBNdb, ou Cantos Libros Están Preservados Para Sempre? Se deduplicásemos correctamente os arquivos das bibliotecas sombra, que porcentaxe de todos os libros do mundo temos preservado? Actualizacións sobre <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>, a maior biblioteca verdadeiramente aberta na historia da humanidade. <em>Redeseño de WorldCat</em> Datos <strong>Formato?</strong> <a %(blog)s>Contedores de Arquivo de Anna (AAC)</a>, que son esencialmente <a %(jsonlines)s>Liñas JSON</a> comprimidas con <a %(zstd)s>Zstandard</a>, máis algúns semánticos estandarizados. Estes contedores envolven varios tipos de rexistros, baseados nos diferentes raspados que despregamos. Hai un ano, <a %(blog)s>comezamos</a> a responder esta pregunta: <strong>Que porcentaxe de libros foron preservados permanentemente por bibliotecas na sombra?</strong> Imos ver algunha información básica sobre os datos: Unha vez que un libro entra nunha biblioteca na sombra de datos abertos como <a %(wikipedia_library_genesis)s>Library Genesis</a>, e agora <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, é espellado por todo o mundo (a través de torrents), preservándoo practicamente para sempre. Para responder á pregunta de que porcentaxe de libros foi preservada, necesitamos saber o denominador: cantos libros existen en total? E idealmente non só temos un número, senón metadata real. Entón non só podemos comparalos coas bibliotecas na sombra, senón tamén <strong>crear unha lista de TODO dos libros restantes para preservar!</strong> Incluso poderiamos comezar a soñar cun esforzo de colaboración para percorrer esta lista de TODO. Raspamos <a %(wikipedia_isbndb_com)s>ISBNdb</a> e descargamos o <a %(openlibrary)s>conxunto de datos de Open Library</a>, pero os resultados non foron satisfactorios. O principal problema foi que non había moita superposición de ISBNs. Vexa este diagrama de Venn do <a %(blog)s>nosso post no blog</a>: Ficamos moi sorprendidos pola pouca superposición que había entre ISBNdb e Open Library, ambos os cales inclúen datos de varias fontes, como raspados web e rexistros de bibliotecas. Se ambos fan un bo traballo atopando a maioría dos ISBNs, os seus círculos seguramente terían unha superposición substancial, ou un sería un subconxunto do outro. Preguntámonos, cantos libros caen <em>completamente fóra destes círculos</em>? Necesitamos unha base de datos máis grande. Foi entón cando fixamos a nosa atención na maior base de datos de libros do mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta é unha base de datos propietaria da organización sen ánimo de lucro <a %(wikipedia_oclc)s>OCLC</a>, que agrega rexistros de metadata de bibliotecas de todo o mundo, a cambio de darlles acceso ao conxunto de datos completo e facer que aparezan nos resultados de busca dos usuarios finais. Aínda que OCLC é unha organización sen ánimo de lucro, o seu modelo de negocio require protexer a súa base de datos. Ben, lamentamos dicir, amigos de OCLC, que o imos dar todo. :-) Durante o último ano, raspamos meticulosamente todos os rexistros de WorldCat. Ao principio, tivemos un golpe de sorte. WorldCat estaba a lanzar o seu redeseño completo do sitio web (en agosto de 2022). Isto incluíu unha revisión substancial dos seus sistemas de backend, introducindo moitas fallas de seguridade. Aproveitamos inmediatamente a oportunidade e puidemos raspar centos de millóns (!) de rexistros en poucos días. Despois diso, as fallas de seguridade foron corrixidas lentamente unha por unha, ata que a última que atopamos foi parcheada hai aproximadamente un mes. Para entón, tiñamos practicamente todos os rexistros e só buscabamos rexistros de calidade lixeiramente superior. Así que sentimos que é hora de liberar! Raspado de 1.3B de WorldCat <em><strong>TL;DR:</strong> O Arquivo de Anna raspou todo WorldCat (a maior colección de metadata de bibliotecas do mundo) para facer unha lista de TODO de libros que necesitan ser preservados.</em> WorldCat Advertencia: esta publicación no blog foi obsoleta. Decidimos que IPFS aínda non está listo para o horario de máxima audiencia. Aínda ligaremos a arquivos en IPFS desde o Arquivo de Anna cando sexa posible, pero non o aloxaremos nós mesmos máis, nin recomendamos a outros que espellen usando IPFS. Consulta a nosa páxina de Torrents se queres axudar a preservar a nosa colección. Axuda a sementar Z-Library en IPFS Descarga do Partner Server SciDB Préstamo externo Préstamo externo (impresión desactivada) Descarga externa Explorar metadata Contido en torrents Volver  (+%(num)s bonus) sen pagar pagado cancelado expirado agardando á confirmación de Anna inválido O texto a continuación continúa en inglés. Ir Restablecer Adiante Último Se a túa dirección de correo non funciona nos foros de Libgen, recomendámoslle empregar <a %(a_mail)s>Proton Mail</a> (free). Tamén pode <a %(a_manual)s>solicitar manualmente</a> a activación da súa conta. (pode precisar <a %(a_browser)s>verificación do navegador</a> — ¡ Descargas ilimitadas !) Servidor Fast Partner #%(number)s (recomendado) (lixeiramente máis rápido pero con lista de espera) (non é necesario verificar o navegador) (sen verificación do navegador nin listas de espera) (sen lista de espera, pero pode ser moi lento) Servidor Partner lento #%(number)s Audiolibro Cómic Libro (Ficción) Libro (Non ficción) Libro (descoñecido) Artículo de revista Revista Partitura musical Outros Documento normativo Non todas as páxinas se puideron converter a PDF Marcado como roto en Libgen.li Non visible en Libgen.li Non visible en Libgen.rs Ficción Non visible en Libgen.rs. Non ficción A execución de exiftool fallou neste ficheiro Marcado como “arquivo malo” en Z-Library Falta en Z-Library Marcado como “spam” en Z-Library O arquivo non puido ser aberto (Por exemplo. arquivo corrupto, DRM [Xestión de Dereitos Dixitais]) Reclamación por dereitos de autor Problemas de descarga (Por exemplo. non pode conectarse, mensaxe de erro, moi lento) Metadatos incorrectos (por exemplo. título, descrición, imaxe de portada) Outro Mala calidade (Por exemplo. problemas co formato, mala calidade de escaneo, faltan páxinas) Spam / arquivo debe ser eliminado (Por exemplo. anuncios, contido abusivo) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brilliant Bookworm Bibliotecario afortunado (Lucky Librarian) Dazzling Datahoarder Asombroso Arquivista (Amazing Archivist) Descargas bonus Cerlalc Metadatos checos DuXiu 读秀 Índice de eBooks de EBSCOhost Google Books Goodreads HathiTrust IA Préstamo Dixital Controlado de IA ISBNdb ISBN GRP Libgen.li Excluíndo “scimag” Libgen.rs Non-Ficción e Ficción Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Biblioteca Estatal Rusa Sci-Hub A través de Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Subidas a AA Z-Library Z-Library Chinesa Título, autor, DOI, ISBN, MD5, … Buscar Autor Comentarios sobre a descripción e os metadatos Edición Nome doo arquivo orixinal Editorial (buscar en un campo específico) Título Ano de publicación Detalles técnicos Esta moeda precisa dun importe mínimo superior ao habitual. Seleccione outra duración ou outra moeda. Non se puido completar a solicitude. Por favor volva a intentalo nuns minutos, e se segue ocurrindo, póñase en contacto con nós en %(email)s con unha captura de pantalla. Produciuse un erro descoñecido. Póñase en contacto con nós en %(email)s con unha captura de pantalla. Erro no procesamento do pago. Espere un momento e inténteo de novo. Se o problema persiste por máis de 24 horas, póñase en contacto con nós en %(email)s con unha captura de pantalla. Estamos recaudando fondos para <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">respaldar</a> a maior biblioteca de cómics na sombra do mundo. Grazas polo teu apoio ! <a href="/donate">Doa.</a> Se non podes doar, considera apoiarnos correndo a voz, e síguenos en <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, ou <a href="https://t.me/annasarchiveorg">Telegram</a>. Non nos envíes correos a <a %(a_request)s>solicitar libros</a><br>ou pequenas (<10k) <a %(a_upload)s>subidas</a>. Anna's Archive Reclamacións de DMCA / copyright Mantéñase en contacto Reddit Alternativas SLUM (%(unaffiliated)s) non afiliado ¡Anna’s Archive necesita a túa axuda! Se doas agora, obterás <strong>o dobre</strong> do número de descargas rápidas. Moitos intentan derrubarnos, pero loitamos de volta. Se doas este mes, obterás <strong>o dobre</strong> de descargas rápidas. Válido ata o final deste mes. Salvando o coñecemento humano: un gran agasallo de vacacións! As subscricións serán ampliadas en consecuencia. Os servidores asociados non están dispoñibles debido ao peche de aloxamentos. Deberían estar operativos de novo pronto. Para aumentar a resistencia de Anna's Arquive, buscamos voluntarios que se encarguen dos espellos. Temos un novo métodos de doazón dispoñible: %(method_name)s. Por favor considere %(donate_link_open_tag)sdoar</a> — non é barato manter este sitio web, e a túa doazón realmente marca a diferencia. Moitas grazas. Recomenda a un amigo, tanto ti como o teu amigo recibiredes %(percentage)s%% de bonificación en descargas rápidas ! Sorprende a un ser querido, agasalla unha conta con subscrición. O regalo perfecto para San Valentín ! Ler máis… Conta Actividade Avanzado Blog de Anna ↗ Anna’s Software ↗ beta Explorador de códigos Datasets Doa Arquivos descargados Preguntas Frecuentes (FAQ) Inicio Mellorar metadatos Datos LLM Iniciar sesión / Rexistrarse As miñas doazóns Perfil público Búsqueda Seguridade Torrents Traducción ↗ Voluntariado e Recompensas Descargas recentes: 📚&nbsp;A maior biblioteca de datos de código aberto do mundo ⭐️&nbsp;Espellos Sci-Hub, Library Genesis, Z-Library, e máis. 📈&nbsp;%(book_any)s libros, %(journal_article)s papers, %(book_comic)s comics, %(magazine)s revistas — preservadas para sempre.  e  e máis DiXiu Biblioteca de Préstamos de Anna's Archive LibGen 📚&nbsp;A librería aberta máis grande do mundo. 📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;papers — preservados para sempre. ⭐️&nbsp;Reflexamos %(libraries)s. Facemos scrape e open-source %(scraped)s. O noso código e información son de código aberto. OpenLib Sci-Hub ,  📚 A maior biblioteca de datos de código aberto do mundo.<br>⭐️ Espellos Scihub, Libgen, Zlib, e máis. Z-Lib Anna's Archive Petición Inválida. Visita %(websites)s. A maior biblioteca de datos de código aberto do mundo. Reflexa Sci-Hub, Library Genesis, Z-Library, e máis. Buscar en Anna’s Archive Anna’s Archive Por favor, actualice para tentalo de novo. <a %(a_contact)s>Contacte connosco</a> se o problema persiste durante varias horas. 🔥 Problema ao cargar esta páxina <li>1.Síguenos en <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, ou <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Corre a voz sobre Anna's Archive en Twitter, Reddit, TikTok, Instagram, na túa cafetería, biblioteca local ou onde sexa que vaias ! Non cremos no control de acceso: se nos retiran, apareceremos noutro sitio, xa que, todo o noso código e datos son de código aberto.</li><li>3. Se podes, considera <a href="/donate">doar</a>.</li><li>4. Axuda a <a href="https://translate.annas-software.org/">traducir</a> a nosa páxina web a diferentes idiomas.</li><li>5. Se eres un Enxeñeiro de Software, considera contribuír ao noso <a href="https://annas-software.org/">código aberto</a>, ou sembrando os nosos <a href="/datasets">torrents</a>.</li> 10. Crea ou axuda a crear a páxina de Wikipedia de Anna's Archive no teu idioma. 11. Queremos poñer anuncios pequenos e de bo gusto. Se queres anunciarte en Anna's Archive, fáinolo saber. 6. Se eres un investigador de seguridade, podemos empregar as túas habilidades tanto para ataque como para a defensa. Bota un vistazo a nosa páxina de <a %(a_security)s>Seguridade</a>. 7. Estamos na procura de expertos en pagos para comerciantes anónimos. ¿ Podes axudarnos a engadir formas máis cómodas de doar ? PayPal, WeChat, tarxetas regalo.Se coñeces a alguén, ponte en contacto con nós. 8. Sempre estamos na procura de máis capacidade de servidor. 9. Podes axudar informando de problemas cos arquivos, deixando comentarios e creando listas neste mesmo sitio web. Tamén podes axudar <a %(a_upload)s>subindo máis libros</a>, ou arreglando problemas de arquivos ou de formato de libros existentes. Para obter máis información sobre como ser voluntario, consulte a nosa páxina de <a %(a_volunteering)s>Voluntariado e Recompensas</a>. Creemos firmemente na libre circulación de información e na preservación do coñecemento e a cultura. Con este motor de búsqueda, apoiámonos a hombros de xigantes. Respetamos profundamente o duro traballo das persoas que crearon as distintas bibliotecas na sombra, e esperamos que este motor de búsqueda amplíe o seu alcance. Para estar ao día dos nosos progresos, sigue a Anna en <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> or <a href="https://t.me/annasarchiveorg">Telegram</a>. Para preguntas e comentarios, ponte en contacto con Anna en %(email)s. ID da conta: %(account_id)s Pechar sesión ❌ Algo fallou. Por favor, refresca a páxina e volve a intentalo. ✅ Pechaches a túa sesión. Refresca a páxina para volver a iniciar sesión. Descargas rápidas empregadas (últimas 24 horas): <strong>%(used)s / %(total)s</strong> Suscripción: <strong>%(tier_name)s</strong> ata %(until_date)s <a %(a_extend)s>(extender)</a> Podes combinar varias suscripcións (as descargas rápidas cada 24 horas sumaranse). Suscripción: <strong>Ningunha</strong> <a %(a_become)s>(facerse membro)</a> Contacta con Anna en %(email)s se estás interesado en actualizar a túa suscripción a unha superior. Perfil público: %(profile_link)s Chave secreta (non a compartas): %(secret_key)s mostrar Únete a nós ! Actualizar a unha <a %(a_tier)s>suscripción superior</a> para unirte ao noso grupo. Grupo exclusivo de Telegram: %(link)s Conta qué descargas ? Iniciar sesión Non perdas a túa chave ! Chave secreta inválida. Comproba a túa chave e volve a intentalo, ou rexistra unha nova conta abaixo. Chave secreta Introduce a túa chave secreta para iniciar sesión: Conta con unha dirección de correo vella ? Introduce a túa dirección de correo <a %(a_open)s>aquí</a>. Rexistrar unha nova conta Non tes unha conta todavía ? Rexistro correcto ! A túa chave secreta é: <span %(span_key)s>%(key)s</span> Gárda esta chave con coidado. Se a perdes, perderás con ela o acceso a túa conta. <li %(li_item)s><strong>Bookmark.</strong>Podes gardar esta páxina para recuperar a túa chave.</li><li %(li_item)s><strong>Descargar.</strong> Clica <a %(a_download)s>neste link</a> para descargar a túa chave.</li><li %(li_item)s><strong>Administrador de contrasinais.</strong> Emprega un administrador de contrasinais para gardar a chave cando a introduzas abaixo.</li> Iniciar sesión / Rexistrarse Verificación do navegador Aviso: o código contén caracteres Unicode incorrectos e pode comportarse incorrectamente en varias situacións. O binario bruto pódese decodificar a partir da representación base64 na URL. Descrición Etiqueta Prefixo URL para un código específico Sitio web Códigos que comezan con “%(prefix_label)s” Recomendamos non rastrexar estas páxinas. En vez diso, suxerimos <a %(a_import)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos de ElasticSearch e MariaDB, e executar o noso <a %(a_software)s>código de código aberto</a>. Os datos en bruto pódense explorar manualmente a través de ficheiros JSON como <a %(a_json_file)s>este</a>. Menos de %(count)s rexistros URL xenérica Explorador de Códigos Índice de Explora os códigos cos que se etiquetan os rexistros, por prefixo. A columna “rexistros” mostra o número de rexistros etiquetados con códigos co prefixo dado, como se ve no motor de busca (incluíndo rexistros só de metadatos). A columna “códigos” mostra cantos códigos reais teñen un prefixo dado. Prefixo coñecido “%(key)s” Máis… Prefixo Rexistro %(count)s que coincide con “%(prefix_label)s” Rexistros %(count)s que coinciden con “%(prefix_label)s” códigos rexistros “%%s” será substituído polo valor do código Buscar o Arquivo de Anna Códigos URL para código específico: “%(url)s” Esta páxina pode tardar un pouco en xenerarse, por iso require un captcha de Cloudflare. <a %(a_donate)s>Os membros</a> poden saltar o captcha. Abuso denunciado: Versión mellorada Quere denunciar a este usuario por comportamento abusivo ou inapropiado? Problema co ficheiro: %(file_issue)s comentario oculto Responder Denunciar abuso Denunciou a este usuario por abuso. Non se terán en conta as reclamacións de dereitos de autor enviadas por correo electrónico. Amosar email Agradecemos os seus comentarios e preguntas ! Non obstante, dada a cantidade de spam e correos electrónicos sen sentido que recibimos, marque as casillas para confirmar que entende estas condicións para poñerse en contacto con nós. Calquera outra forma de poñerse en contacto con nós sobre reclamacións de dereitos de autor eliminarase automáticamente. Para reclamacións DMCA / copyright, empregue <a %(a_copyright)s> este formulario</a>. Email de contacto URLs en Anna’s Archive (requirido). Un por liña. Inclúe só URLs que describan exactamente a mesma edición dun libro. Se queres facer unha reclamación para varios libros ou varias edicións, envía este formulario varias veces. As reclamacións que agrupen varios libros ou edicións serán rexeitadas. Enderezo (requirido) Descrición clara do material de orixe (requirido) Correo electrónico (requirido) URLs do material de orixe, un por liña (requirido). Inclúe tantos como sexa posible, para axudarnos a verificar a túa reclamación (por exemplo, Amazon, WorldCat, Google Books, DOI). ISBNs do material de orixe (se é aplicable). Un por liña. Inclúe só aqueles que coincidan exactamente coa edición para a que estás informando dunha reclamación de dereitos de autor. O teu nome (requirido) ❌ Algo saíu mal. Recarga a páxina e inténtao de novo. ✅ Grazas por enviar a túa reclamación de dereitos de autor. Revisarémola o antes posible. Recarga a páxina para enviar outra. <a %(a_openlib)s>Open Library</a> URLs do material de orixe, un por liña. Tómate un momento para buscar o teu material de orixe en Open Library. Isto axudaranos a verificar a túa reclamación. Número de teléfono (requirido) Declaración e sinatura (requirido) Enviar reclamación Se tes unha reclamación de DMCA ou outro tipo de reclamación de dereitos de autor, enche este formulario o máis precisamente posible. Se tes algún problema, contacta connosco na nosa dirección dedicada a DMCA: %(email)s. Ten en conta que as reclamacións enviadas a esta dirección non serán procesadas, só é para preguntas. Usa o formulario a continuación para enviar as túas reclamacións. Formulario de reclamación de DMCA / Copyright Exemplo de rexistro no Arquivo de Anna Torrents do Arquivo de Anna Formato de Contedores do Arquivo de Anna Scripts para importar metadatos Se estás interesado en espellar este conxunto de datos para <a %(a_archival)s>arquivo</a> ou para fins de <a %(a_llm)s>adestramento de LLM</a>, por favor contacta connosco. Última actualización: %(date)s Sitio web principal %(source)s Documentación de metadatos (a maioría dos campos) Ficheiros espellados polo Arquivo de Anna: %(count)s (%(percent)s%%) Recursos Total de ficheiros: %(count)s Tamaño total dos ficheiros: %(size)s A nosa entrada de blog sobre estes datos <a %(duxiu_link)s>Duxiu</a> é unha base de datos masiva de libros escaneados, creada polo <a %(superstar_link)s>SuperStar Digital Library Group</a>. A maioría son libros académicos, escaneados para facelos dispoñibles dixitalmente a universidades e bibliotecas. Para a nosa audiencia de fala inglesa, <a %(princeton_link)s>Princeton</a> e a <a %(uw_link)s>Universidade de Washington</a> teñen boas visións xerais. Tamén hai un excelente artigo que ofrece máis contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Os libros de Duxiu levan moito tempo sendo pirateados na internet chinesa. Normalmente véndense por menos dun dólar por revendedores. Adoitan distribuírse usando o equivalente chinés de Google Drive, que a miúdo foi hackeado para permitir máis espazo de almacenamento. Algúns detalles técnicos pódense atopar <a %(link1)s>aquí</a> e <a %(link2)s>aquí</a>. Aínda que os libros foron distribuídos de forma semi-pública, é bastante difícil obtelos en grandes cantidades. Tiñámolo moi alto na nosa lista de tarefas pendentes, e asignamos varios meses de traballo a tempo completo para iso. Con todo, a finais de 2023 un voluntario incrible, asombroso e talentoso contactou connosco, dicíndonos que xa fixera todo este traballo — a gran custo. Compartiron connosco a colección completa, sen esperar nada a cambio, excepto a garantía de preservación a longo prazo. Realmente notable. Máis información dos nosos voluntarios (notas en bruto): Adaptado da nosa <a %(a_href)s>entrada de blog</a>. DuXiu 读秀 %(count)s ficheiro %(count)s ficheiros Este conxunto de datos está estreitamente relacionado co <a %(a_datasets_openlib)s>conxunto de datos de Open Library</a>. Contén unha raspadura de todos os metadatos e unha gran parte dos ficheiros da Biblioteca de Préstamo Dixital Controlado da IA. As actualizacións publícanse no <a %(a_aac)s>formato de Contedores de Anna’s Archive</a>. Estes rexistros están sendo referidos directamente do conxunto de datos de Open Library, pero tamén conteñen rexistros que non están en Open Library. Tamén temos varios ficheiros de datos raspados por membros da comunidade ao longo dos anos. A colección consta de dúas partes. Necesitas ambas partes para obter todos os datos (excepto os torrents substituídos, que están tachados na páxina de torrents). Biblioteca de Préstamo Dixital a nosa primeira versión, antes de estandarizarnos no formato <a %(a_aac)s>Contedores do Arquivo de Anna (AAC)</a>. Contén metadatos (como json e xml), pdfs (dos sistemas de préstamo dixital acsm e lcpdf) e miniaturas de portadas. novas versións incrementais, usando AAC. Só contén metadatos con marcas de tempo despois de 2023-01-01, xa que o resto xa está cuberto por "ia". Tamén todos os ficheiros pdf, esta vez dos sistemas de préstamo acsm e "bookreader" (o lector web de IA). A pesar de que o nome non é exactamente correcto, seguimos poboando ficheiros de bookreader na colección ia2_acsmpdf_files, xa que son mutuamente excluíntes. Préstamo Dixital Controlado de IA 98%%+ dos arquivos son buscables. A nosa misión é arquivar todos os libros do mundo (así como artigos, revistas, etc), e facelos amplamente accesibles. Cremos que todos os libros deberían ser espellados amplamente, para garantir a redundancia e a resiliencia. Por iso estamos reunindo ficheiros de varias fontes. Algunhas fontes son completamente abertas e poden ser espelladas en masa (como Sci-Hub). Outras son pechadas e protectoras, polo que intentamos raspalas para “liberar” os seus libros. Outras están nalgún punto intermedio. Todos os nosos datos poden ser <a %(a_torrents)s>torrenteados</a>, e todos os nosos metadatos poden ser <a %(a_anna_software)s>xenerados</a> ou <a %(a_elasticsearch)s>descargados</a> como bases de datos ElasticSearch e MariaDB. Os datos en bruto poden ser explorados manualmente a través de ficheiros JSON como <a %(a_dbrecord)s>este</a>. Metadatos Sitio web do ISBN Última actualización: %(isbn_country_date)s (%(link)s) Recursos A Axencia Internacional do ISBN publica regularmente os rangos que asignou ás axencias nacionais do ISBN. A partir disto, podemos determinar a que país, rexión ou grupo de linguas pertence este ISBN. Actualmente usamos estes datos indirectamente, a través da biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>. Información do país do ISBN Este é un volcado de moitas chamadas a isbndb.com durante setembro de 2022. Tentamos cubrir todos os rangos de ISBN. Son aproximadamente 30,9 millóns de rexistros. No seu sitio web afirman que realmente teñen 32,6 millóns de rexistros, polo que poderiamos ter perdido algúns, ou <em>eles</em> poderían estar facendo algo mal. As respostas JSON son practicamente crúas do seu servidor. Un problema de calidade de datos que notamos é que para os números ISBN-13 que comezan cun prefixo diferente a “978-”, aínda inclúen un campo “isbn” que simplemente é o número ISBN-13 cos tres primeiros números cortados (e o díxito de verificación recalculado). Isto obviamente está mal, pero así parece que o fan, polo que non o alteramos. Outro problema potencial que pode atopar é o feito de que o campo “isbn13” ten duplicados, polo que non pode usalo como clave primaria nunha base de datos. Os campos combinados “isbn13”+“isbn” parecen ser únicos. Lanzamento 1 (2022-10-31) Os torrents de ficción están atrasados (aínda que os IDs ~4-6M non están en torrents xa que se solapan cos nosos torrents de Zlib). A nosa publicación no blog sobre o lanzamento dos cómics Torrents de cómics no Arquivo de Anna Para a historia dos diferentes forks de Library Genesis, vexa a páxina de <a %(a_libgen_rs)s>Libgen.rs</a>. O Libgen.li contén a maior parte do mesmo contido e metadatos que o Libgen.rs, pero ten algunhas coleccións adicionais, a saber, cómics, revistas e documentos estándar. Tamén integrou <a %(a_scihub)s>Sci-Hub</a> nos seus metadatos e motor de busca, que é o que usamos para a nosa base de datos. Os metadatos desta biblioteca están dispoñibles gratuitamente <a %(a_libgen_li)s>en libgen.li</a>. Non obstante, este servidor é lento e non admite a reanudación de conexións interrompidas. Os mesmos ficheiros tamén están dispoñibles nun <a %(a_ftp)s>servidor FTP</a>, que funciona mellor. A non ficción tamén parece terse desviado, pero sen novos torrents. Parece que isto sucedeu desde principios de 2022, aínda que non o verificamos. Segundo o administrador de Libgen.li, a colección “fiction_rus” (ficción rusa) debería estar cuberta por torrents lanzados regularmente desde <a %(a_booktracker)s>booktracker.org</a>, especialmente os torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que espellamos <a %(a_torrents)s>aquí</a>, aínda que aínda non establecemos que torrents corresponden a que arquivos). A colección de ficción ten os seus propios torrents (divergentes de <a %(a_href)s>Libgen.rs</a>) a partir de %(start)s. Certos rangos sen torrents (como os rangos de ficción f_3463000 a f_4260000) probablemente sexan arquivos de Z-Library (ou outros duplicados), aínda que quizais queiramos facer algunha deduplicación e crear torrents para arquivos únicos de lgli nestes rangos. As estatísticas de todas as coleccións pódense atopar <a %(a_href)s>no sitio web de libgen</a>. Os torrents están dispoñibles para a maioría do contido adicional, especialmente os torrents para cómics, revistas e documentos estándar foron lanzados en colaboración co Arquivo de Anna. Teña en conta que os arquivos torrent que se refiren a “libgen.is” son explicitamente espellos de <a %(a_libgen)s>Libgen.rs</a> (“.is” é un dominio diferente usado por Libgen.rs). Un recurso útil para usar os metadatos é <a %(a_href)s>esta páxina</a>. %(icon)s A súa colección “fiction_rus” (ficción rusa) non ten torrents dedicados, pero está cuberta por torrents doutros, e mantemos un <a %(fiction_rus)s>espello</a>. Torrents de ficción rusa no Arquivo de Anna Torrents de ficción no Arquivo de Anna Foro de discusión Metadatos Metadatos vía FTP Torrents de revistas no Arquivo de Anna Información do campo de metadatos Espello doutros torrents (e torrents únicos de ficción e cómics) Torrents de documentos estándar no Arquivo de Anna Libgen.li Torrents de Anna’s Archive (portadas de libros) Library Genesis é coñecido por xa facer xenerosamente os seus datos dispoñibles en masa a través de torrents. A nosa colección de Libgen consiste en datos auxiliares que non liberan directamente, en colaboración con eles. Moitas grazas a todos os implicados en Library Genesis por traballar connosco! O noso blog sobre o lanzamento das portadas de libros Esta páxina trata sobre a versión “.rs”. É coñecida por publicar consistentemente tanto os seus metadatos como o contido completo do seu catálogo de libros. A súa colección de libros está dividida entre unha parte de ficción e outra de non ficción. Un recurso útil para usar os metadatos é <a %(a_metadata)s>esta páxina</a> (bloquea rangos de IP, pode ser necesario un VPN). A partir de 2024-03, novos torrents están a ser publicados neste <a %(a_href)s>fío do foro</a> (bloquea rangos de IP, pode ser necesario un VPN). Torrents de ficción en Anna’s Archive Torrents de ficción de Libgen.rs Foro de discusión de Libgen.rs Metadatos de Libgen.rs Información dos campos de metadatos de Libgen.rs Torrents de non ficción de Libgen.rs Torrents de non ficción en Anna’s Archive %(example)s para un libro de ficción. Este <a %(blog_post)s>primeiro lanzamento</a> é bastante pequeno: uns 300GB de portadas de libros do fork de Libgen.rs, tanto de ficción como de non ficción. Están organizados do mesmo xeito que aparecen en libgen.rs, por exemplo: %(example)s para un libro de non ficción. Igual que coa colección de Z-Library, puxémolos todos nun gran ficheiro .tar, que se pode montar usando <a %(a_ratarmount)s>ratarmount</a> se queres servir os ficheiros directamente. Lanzamento 1 (%(date)s) A breve historia das diferentes bifurcacións de Library Genesis (ou “Libgen”) é que co paso do tempo, as diferentes persoas involucradas en Library Genesis tiveron desacordos e seguiron camiños separados. Segundo esta <a %(a_mhut)s>publicación no foro</a>, Libgen.li foi orixinalmente aloxado en “http://free-books.dontexist.com”. A versión “.fun” foi creada polo fundador orixinal. Está sendo renovada a favor dunha nova versión máis distribuída. A <a %(a_li)s>versión “.li”</a> ten unha colección masiva de cómics, así como doutro contido, que aínda non está dispoñible para descarga masiva a través de torrents. Ten unha colección de torrents separada de libros de ficción, e contén os metadatos de <a %(a_scihub)s>Sci-Hub</a> na súa base de datos. A versión “.rs” ten datos moi similares e publica a súa colección en torrents masivos de forma consistente. Está aproximadamente dividida nunha sección de “ficción” e outra de “non ficción”. Orixinalmente en “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> nalgún sentido tamén é unha bifurcación de Library Genesis, aínda que usaron un nome diferente para o seu proxecto. Libgen.rs Tamén enriquecemos a nosa colección con fontes só de metadatos, que podemos emparellar con ficheiros, por exemplo, usando números ISBN ou outros campos. A continuación, preséntase unha visión xeral desas fontes. De novo, algunhas destas fontes son completamente abertas, mentres que outras temos que raspalas. Teña en conta que na busca de metadatos, mostramos os rexistros orixinais. Non facemos ningunha fusión de rexistros. Fontes só de metadatos Open Library é un proxecto de código aberto do Internet Archive para catalogar todos os libros do mundo. Ten unha das maiores operacións de escaneo de libros do mundo, e ten moitos libros dispoñibles para préstamo dixital. O seu catálogo de metadatos de libros está dispoñible para descarga gratuíta, e está incluído en Anna’s Archive (aínda que actualmente non está na busca, excepto se buscas explicitamente un ID de Open Library). Open Library Excluíndo duplicados Última actualización Porcentaxes do número de ficheiros %% espellado por AA / torrents dispoñibles Tamaño Fonte A continuación, unha rápida visión xeral das fontes dos ficheiros en Anna’s Archive. Como as bibliotecas sombra adoitan sincronizar datos entre si, hai unha considerable superposición entre as bibliotecas. Por iso os números non suman o total. A porcentaxe de “espellado e sementado por Anna’s Archive” mostra cantos ficheiros espellamos nós mesmos. Sementamos eses ficheiros en masa a través de torrents, e facémolos dispoñibles para descarga directa a través de sitios web asociados. Visión xeral Total Torrents en Anna’s Archive Para obter información sobre Sci-Hub, consulte o seu <a %(a_scihub)s>sitio web oficial</a>, a súa <a %(a_wikipedia)s>páxina de Wikipedia</a> e esta <a %(a_radiolab)s>entrevista en podcast</a>. Teña en conta que Sci-Hub foi <a %(a_reddit)s>conxelado desde 2021</a>. Xa fora conxelado antes, pero en 2021 engadíronse uns poucos millóns de artigos. Aínda así, engádense algúns artigos limitados ás coleccións “scimag” de Libgen, aínda que non o suficiente como para xustificar novos torrents masivos. Usamos os metadatos de Sci-Hub proporcionados por <a %(a_libgen_li)s>Libgen.li</a> na súa colección “scimag”. Tamén usamos o conxunto de datos <a %(a_dois)s>dois-2022-02-12.7z</a>. Teña en conta que os torrents “smarch” están <a %(a_smarch)s>obsoletos</a> e, polo tanto, non están incluídos na nosa lista de torrents. Torrents en Libgen.li Torrents en Libgen.rs Metadatos e torrents Actualizacións en Reddit Entrevista en podcast Páxina de Wikipedia Sci-Hub Sci-Hub: conxelado desde 2021; a maioría dispoñible a través de torrents Libgen.li: pequenas adicións desde entón</div> Algunhas bibliotecas fonte promoven o intercambio masivo dos seus datos a través de torrents, mentres que outras non comparten facilmente a súa colección. No último caso, Anna’s Archive tenta raspar as súas coleccións e facelas dispoñibles (vexa a nosa páxina de <a %(a_torrents)s>Torrents</a>). Tamén hai situacións intermedias, por exemplo, onde as bibliotecas fonte están dispostas a compartir, pero non teñen os recursos para facelo. Neses casos, tamén tentamos axudar. A continuación, preséntase unha visión xeral de como interactuamos coas diferentes bibliotecas fonte. Bibliotecas fonte %(icon)s Varias bases de datos de ficheiros espalladas pola internet chinesa; aínda que a miúdo son bases de datos de pago %(icon)s A maioría dos ficheiros só son accesibles usando contas premium de BaiduYun; velocidades de descarga lentas. %(icon)s O Arquivo de Anna xestiona unha colección de <a %(duxiu)s>ficheiros de DuXiu</a> %(icon)s Varias bases de datos de metadatos espalladas pola internet chinesa; aínda que a miúdo son bases de datos de pago %(icon)s Non hai dispoñibles volcados de metadatos facilmente accesibles para toda a súa colección. %(icon)s O Arquivo de Anna xestiona unha colección de <a %(duxiu)s>metadatos de DuXiu</a> Ficheiros %(icon)s Ficheiros só dispoñibles para préstamo de forma limitada, con varias restricións de acceso %(icon)s O Arquivo de Anna xestiona unha colección de <a %(ia)s>ficheiros de IA</a> %(icon)s Algúns metadatos dispoñibles a través de <a %(openlib)s>Open Library database dumps</a>, pero non cobren toda a colección de IA %(icon)s Non hai volcados de metadatos facilmente accesibles dispoñibles para toda a súa colección %(icon)s O Arquivo de Anna xestiona unha colección de <a %(ia)s>metadatos de IA</a> Última actualización %(icon)s O Arquivo de Anna e Libgen.li xestionan conxuntamente coleccións de <a %(comics)s>cómics</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos estándar</a> e <a %(fiction)s>ficción (divergida de Libgen.rs)</a>. %(icon)s Os torrents de Non-Ficción compártense con Libgen.rs (e espellados <a %(libgenli)s>aquí</a>). %(icon)s Volcados trimestrais da <a %(dbdumps)s>base de datos HTTP</a> %(icon)s Torrents automatizados para <a %(nonfiction)s>Non-Ficción</a> e <a %(fiction)s>Ficción</a> %(icon)s O Arquivo de Anna xestiona unha colección de <a %(covers)s>torrents de portadas de libros</a> %(icon)s Verteduras diarias de <a %(dbdumps)s>bases de datos HTTP</a> Metadatos %(icon)s Verteduras mensuais de <a %(dbdumps)s>bases de datos</a> %(icon)s Torrents de datos dispoñibles <a %(scihub1)s>aquí</a>, <a %(scihub2)s>aquí</a>, e <a %(libgenli)s>aquí</a> %(icon)s Algúns novos ficheiros están <a %(libgenrs)s>sendo</a> <a %(libgenli)s>engadidos</a> ao “scimag” de Libgen, pero non o suficiente para xustificar novos torrents %(icon)s Sci-Hub conxelou novos ficheiros desde 2021. %(icon)s Volcados de metadatos dispoñibles <a %(scihub1)s>aquí</a> e <a %(scihub2)s>aquí</a>, así como parte da <a %(libgenli)s>base de datos de Libgen.li</a> (que usamos) Fonte %(icon)s Varias fontes máis pequenas ou únicas. Animamos ás persoas a subir primeiro a outras bibliotecas sombra, pero ás veces as persoas teñen coleccións que son demasiado grandes para que outros as clasifiquen, aínda que non o suficientemente grandes como para merecer a súa propia categoría. %(icon)s Non dispoñible directamente en masa, protexido contra raspado %(icon)s O Arquivo de Anna xestiona unha colección de <a %(worldcat)s>metadatos de OCLC (WorldCat)</a> %(icon)s O Arquivo de Anna e Z-Library xestionan colaborativamente unha colección de <a %(metadata)s>metadatos de Z-Library</a> e <a %(files)s>ficheiros de Z-Library</a> Datasets Combinamos todas as fontes anteriores nunha base de datos unificada que usamos para servir este sitio web. Esta base de datos unificada non está dispoñible directamente, pero como Anna’s Archive é totalmente de código aberto, pode ser bastante facilmente <a %(a_generated)s>xerada</a> ou <a %(a_downloaded)s>descargada</a> como bases de datos ElasticSearch e MariaDB. Os scripts desa páxina descargarán automaticamente todos os metadatos necesarios das fontes mencionadas anteriormente. Se desexa explorar os nosos datos antes de executar eses scripts localmente, pode consultar os nosos ficheiros JSON, que enlazan con outros ficheiros JSON. <a %(a_json)s>Este ficheiro</a> é un bo punto de partida. Base de datos unificada Torrents polo Arquivo de Anna navegar buscar Varias fontes máis pequenas ou únicas. Animamos ás persoas a subir primeiro a outras bibliotecas sombra, pero ás veces as persoas teñen coleccións que son demasiado grandes para que outros as clasifiquen, aínda que non o suficientemente grandes como para merecer a súa propia categoría. Visión xeral da <a %(a1)s>páxina de datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bastante completo. Do noso voluntario “cgiym”. Dun <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Ten unha alta superposición coas coleccións de artigos existentes, pero moi poucos coincidencias de MD5, polo que decidimos mantelo completamente. Raspado de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), por voluntario <q>j</q>. Corresponde ao metadata de <q>airitibooks</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>. Dunha colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte da fonte orixinal, parte de the-eye.eu, parte doutros espellos. Dun sitio web privado de torrents de libros, <a %(a_href)s>Bibliotik</a> (a miúdo referido como “Bib”), dos cales os libros foron agrupados en torrents por nome (A.torrent, B.torrent) e distribuídos a través de the-eye.eu. Do noso voluntario "bpb9v". Para máis información sobre <a %(a_href)s>CADAL</a>, vexa as notas na nosa <a %(a_duxiu)s>páxina de datos de DuXiu</a>. Máis do noso voluntario "bpb9v", principalmente ficheiros de DuXiu, así como un cartafol "WenQu" e "SuperStar_Journals" (SuperStar é a empresa detrás de DuXiu). Do noso voluntario "cgiym", textos chineses de varias fontes (representados como subdirectorios), incluíndo de <a %(a_href)s>China Machine Press</a> (un importante editor chinés). Coleccións non chinesas (representadas como subdirectorios) do noso voluntario “cgiym”. Raspado de libros sobre arquitectura chinesa, por voluntario <q>cm</q>: <q>Conseguino explotando unha vulnerabilidade de rede na editorial, pero esa fenda xa foi pechada</q>. Corresponde ao metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>. Libros da editorial académica <a %(a_href)s>De Gruyter</a>, recollidos de algúns grandes torrents. Raspado de <a %(a_href)s>docer.pl</a>, un sitio web polaco de intercambio de arquivos centrado en libros e outros traballos escritos. Raspado a finais de 2023 polo voluntario “p”. Non temos bos metadatos do sitio web orixinal (nin sequera extensións de arquivo), pero filtramos arquivos semellantes a libros e a miúdo puidemos extraer metadatos dos propios arquivos. Epubs de DuXiu, directamente de DuXiu, recollidos polo voluntario "w". Só os libros recentes de DuXiu están dispoñibles directamente a través de ebooks, polo que a maioría destes deben ser recentes. Arquivos restantes de DuXiu do voluntario “m”, que non estaban no formato propietario PDG de DuXiu (o principal <a %(a_href)s>conxunto de datos de DuXiu</a>). Recollidos de moitas fontes orixinais, lamentablemente sen preservar esas fontes na ruta do arquivo. <span></span> <span></span> <span></span> Raspado de libros eróticos, por voluntario <q>do no harm</q>. Corresponde ao metadata de <q>hentai</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>. <span></span> <span></span> Colección raspada dun editor xaponés de manga polo voluntario “t”. <a %(a_href)s>Arquivos xudiciais seleccionados de Longquan</a>, proporcionados polo voluntario “c”. Raspado de <a %(a_href)s>magzdb.org</a>, un aliado de Library Genesis (está vinculado na páxina de inicio de libgen.rs) pero que non quixo proporcionar os seus arquivos directamente. Obtido polo voluntario “p” a finais de 2023. <span></span> Varias pequenas subidas, demasiado pequenas como para ser a súa propia subcolección, pero representadas como directorios. Ebooks de AvaxHome, un sitio web ruso de intercambio de arquivos. Arquivo de xornais e revistas. Corresponde ao metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>. Raspado do <a %(a1)s>Philosophy Documentation Center</a>. Colección do voluntario “o” que recolleu libros polacos directamente dos sitios web de lanzamento orixinal (“escena”). Coleccións combinadas de <a %(a_href)s>shuge.org</a> polos voluntarios “cgiym” e “woz9ts”. <span></span> <a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomeada pola biblioteca ficticia), raspada en 2022 polo voluntario “t”. <span></span> <span></span> <span></span> Sub-sub-coleccións (representadas como directorios) do voluntario "woz9ts": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taiwán), mebook (mebook.cc, 我的小书屋, a miña pequena libraría — woz9ts: "Este sitio céntrase principalmente en compartir ficheiros de ebooks de alta calidade, algúns dos cales están maquetados polo propio propietario. O propietario foi <a %(a_arrested)s>detido</a> en 2019 e alguén fixo unha colección dos ficheiros que compartiu."). Ficheiros restantes de DuXiu do voluntario “woz9ts”, que non estaban no formato propietario PDG de DuXiu (aínda por converter a PDF). A colección de "subidas" está dividida en subcoleccións máis pequenas, que están indicadas nos AACIDs e nos nomes dos torrents. Todas as subcoleccións foron primeiro deduplicadas contra a colección principal, aínda que os ficheiros JSON de metadatos "upload_records" aínda conteñen moitas referencias aos ficheiros orixinais. Os ficheiros non relacionados con libros tamén foron eliminados da maioría das subcoleccións, e normalmente <em>non</em> están anotados no JSON de "upload_records". As subcoleccións son: Notas Subcolección Moitas subcoleccións están compostas por sub-sub-coleccións (por exemplo, de diferentes fontes orixinais), que están representadas como directorios nos campos de “filepath”. Subidas ao Arquivo de Anna A nosa publicación no blog sobre estes datos <a %(a_worldcat)s>WorldCat</a> é unha base de datos propietaria da organización sen ánimo de lucro <a %(a_oclc)s>OCLC</a>, que agrega rexistros de metadatos de bibliotecas de todo o mundo. Probablemente sexa a maior colección de metadatos de bibliotecas do mundo. En outubro de 2023 <a %(a_scrape)s>publicamos</a> unha extracción completa da base de datos de OCLC (WorldCat), no <a %(a_aac)s>formato de Contedores de Anna’s Archive</a>. Outubro de 2023, lanzamento inicial: OCLC (WorldCat) Torrents por Anna’s Archive Exemplo de rexistro no Arquivo de Anna (colección orixinal) Exemplo de rexistro no Arquivo de Anna (colección “zlib3”) Torrents por Arquivo de Anna (metadatos + contido) Publicación no blog sobre a Versión 1 Publicación no blog sobre a Versión 2 A finais de 2022, os presuntos fundadores de Z-Library foron arrestados, e os dominios foron incautados polas autoridades dos Estados Unidos. Desde entón, o sitio web foi lentamente volvendo a estar en liña. Descoñécese quen o xestiona actualmente. Actualización de febreiro de 2023. Z-Library ten as súas raíces na comunidade de <a %(a_href)s>Library Genesis</a>, e orixinalmente comezou coas súas datos. Desde entón, profesionalizouse considerablemente, e ten unha interface moito máis moderna. Por iso, son capaces de obter moitas máis doazóns, tanto monetarias para seguir mellorando o seu sitio web, como doazóns de novos libros. Acumularon unha gran colección ademais de Library Genesis. A colección consta de tres partes. As páxinas de descrición orixinais das dúas primeiras partes consérvanse a continuación. Necesitas as tres partes para obter todos os datos (agás os torrents substituídos, que están tachados na páxina de torrents). %(title)s: a nosa primeira versión. Esta foi a primeira versión do que entón se chamaba o “Espello da Biblioteca Pirata” (“pilimi”). %(title)s: segunda versión, esta vez con todos os ficheiros empaquetados en ficheiros .tar. %(title)s: novas versións incrementais, usando o <a %(a_href)s>formato de Contedores de Arquivo de Anna (AAC)</a>, agora lanzado en colaboración co equipo de Z-Library. O espello inicial obtívose meticulosamente ao longo de 2021 e 2022. Neste momento está lixeiramente desactualizado: reflicte o estado da colección en xuño de 2021. Actualizaremos isto no futuro. Agora mesmo estamos centrados en lanzar esta primeira versión. Dado que Library Genesis xa está preservada con torrents públicos e está incluída na Z-Library, fixemos unha deduplicación básica contra Library Genesis en xuño de 2022. Para isto usamos hashes MD5. Probablemente hai moito máis contido duplicado na biblioteca, como múltiples formatos de ficheiros co mesmo libro. Isto é difícil de detectar con precisión, polo que non o facemos. Despois da deduplicación quedamos con máis de 2 millóns de ficheiros, totalizando pouco menos de 7TB. A colección consta de dúas partes: un volcado MySQL “.sql.gz” dos metadatos, e os 72 ficheiros torrent de arredor de 50-100GB cada un. Os metadatos conteñen os datos informados polo sitio web de Z-Library (título, autor, descrición, tipo de ficheiro), así como o tamaño real do ficheiro e o md5sum que observamos, xa que ás veces estes non coinciden. Parece haber rangos de ficheiros para os que a propia Z-Library ten metadatos incorrectos. Tamén poderiamos ter descargado ficheiros incorrectamente nalgúns casos illados, que intentaremos detectar e corrixir no futuro. Os grandes ficheiros torrent conteñen os datos reais dos libros, co ID de Z-Library como nome do ficheiro. As extensións dos ficheiros pódense reconstruír usando o volcado de metadatos. A colección é unha mestura de contido de non ficción e ficción (non separado como en Library Genesis). A calidade tamén varía moito. Esta primeira versión xa está completamente dispoñible. Teña en conta que os ficheiros torrent só están dispoñibles a través do noso espello Tor. Versión 1 (%(date)s) Este é un único ficheiro torrent extra. Non contén ningunha información nova, pero ten algúns datos que poden levar un tempo calcular. Iso fai que sexa conveniente telo, xa que descargar este torrent adoita ser máis rápido que calculalo desde cero. En particular, contén índices SQLite para os ficheiros tar, para usar con <a %(a_href)s>ratarmount</a>. Addenda da versión 2 (%(date)s) Conseguimos todos os libros que se engadiron á Z-Library entre o noso último espello e agosto de 2022. Tamén volvemos atrás e raspamos algúns libros que perdemos a primeira vez. En total, esta nova colección é duns 24 TB. De novo, esta colección está deduplicada contra Library Genesis, xa que xa hai torrents dispoñibles para esa colección. Os datos están organizados de forma similar á primeira versión. Hai un volcado MySQL “.sql.gz” dos metadatos, que tamén inclúe todos os metadatos da primeira versión, substituíndoa así. Tamén engadimos algunhas novas columnas: Mencionamos isto a última vez, pero só para aclarar: “filename” e “md5” son as propiedades reais do ficheiro, mentres que “filename_reported” e “md5_reported” son o que extraemos de Z-Library. Ás veces estes dous non coinciden entre si, polo que incluímos ambos. Para esta versión, cambiamos a colación a “utf8mb4_unicode_ci”, que debería ser compatible con versións máis antigas de MySQL. Os ficheiros de datos son similares aos da última vez, aínda que son moito máis grandes. Simplemente non nos molestamos en crear toneladas de ficheiros torrent máis pequenos. “pilimi-zlib2-0-14679999-extra.torrent” contén todos os ficheiros que perdemos na última versión, mentres que os outros torrents son todos novos rangos de ID.  <strong>Actualización %(date)s:</strong> Fixemos a maioría dos nosos torrents demasiado grandes, causando problemas aos clientes de torrent. Eliminámolos e lanzamos novos torrents. <strong>Actualización %(date)s:</strong> Aínda había demasiados ficheiros, polo que os empaquetamos en ficheiros tar e lanzamos novos torrents de novo. %(key)s: se este ficheiro xa está en Library Genesis, na colección de non ficción ou ficción (coincidencia por md5). %(key)s: en que torrent está este ficheiro. %(key)s: establecido cando non puidemos descargar o libro. Versión 2 (%(date)s) Lanzamentos de Zlib (páxinas de descrición orixinais) Dominio Tor Sitio web principal Raspado de Z-Library A colección “Chinesa” en Z-Library parece ser a mesma que a nosa colección de DuXiu, pero con diferentes MD5s. Excluímos estes ficheiros dos torrents para evitar duplicacións, pero aínda os mostramos no noso índice de busca. Metadatos Obtés %(percentage)s%% descargas rápidas extra, porque fuches referido polo usuario %(profile_link)s. Esto aplica a todo o período de afiliación. Doar Únete Seleccionado ata %(percentage)s%% de desconto Alipay admite tarxetas de crédito/débito internacionais. Vexa <a %(a_alipay)s>esta guía</a> para máis información. Envíanos tarxetas regalo de Amazon.com empregando a túa tarxeta de crédito ou débito. Podes comprar criptomoedas empregando tarxetas de crédito ou débito. WeChat (Weixin Pay) admite tarxetas de crédito/débito internacionais. Na app de WeChat, vai a“Me => Services => Wallet => Add a Card”. Se non atopas o menú, actívao en “Me => Settings => General => Tools => Weixin Pay => Enable”. (use cando envíe Ethereum desde Coinbase) copiado ! copiar (importe mínimo) (Advertencia: importe mínimo elevado) -%(percentage)s%% 12 meses 1 mes 24 meses 3 meses 48 meses 6 meses 96 meses Selecciona por canto tempo queres suscribirte. <div %(div_monthly_cost)s></div><div %(div_after)s>despois <span %(span_discount)s></span> descontos</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% durante 12 meses durante 1 mes durante 24 meses durante 3 meses durante 48 meses durante 6 meses durante 96 meses %(monthly_cost)s / mes contácta con nós Servidores <strong>SFTP</strong> directos Donación ou intercambio a nivel empresarial de novas coleccións (por exemplo, novos escaneos, conxuntos de datos OCR). Acceso Experto Acceso <strong>Ilimitado</strong> de alta velocidade <div %(div_question)s>¿Podo mellorar a miña subscrición ou obter varias subscricións?</div> <div %(div_question)s>¿Podo facer unha doazón sen facerme membro?</div> Por suposto. Aceptamos doazóns de calquera cantidade nesta dirección de Monero (XMR): %(address)s. <div %(div_question)s>Que significan os rangos por mes?</div> Podes chegar ao lado inferior dun rango aplicando todos os descontos, como elixir un período máis longo que un mes. <div %(div_question)s>As afiliacións renóvanse automáticamente ?</div> As afiliacións <strong>non</strong> se renovan automáticamente. Podes afiliarte o tempo que desexes. <div %(div_question)s>En qué se gastan as doazóns ?</div> O 100%% destínase a preservar e facer accesible o coñecemento e a cultura do mundo. Actualmente gastámolo principalmente en servidores, almacenamento e ancho de banda. Ningún membro do equipo recibe diñeiro persoalmente. <div %(div_question)s>Podo facer unha doazón importante ?</div> Sería fantástico ! Para doazóns superiores a uns miles de dólares, póñase en contacto con nos directamente en %(email)s. <div %(div_question)s>Tedes outros métodos de pago ?</div> Actualmente non. Moita xente non quere que existan arquivos coma este, así que temos que ter coidado. Se podes axudarnos a configurar outros métodos de pago (máis cómodos) de forma segura, por favor ponte en contacto con nós en %(email)s. Preguntas frecuentes sobre doazóns Tes unha <a %(a_donation)s>donación existente</a> en progreso. Por favor termina ou cancela dita donación antes de realizar outra. <a %(a_all_donations)s>Ver as miñas donacións</a> Para donacións superiores a 5000 $, póñase en contacto con nós directamente en %(email)s. Agradecemos as grandes doazóns de particulares ou institucións adiñeradas  Teña en conta que aínda que as subscricións nesta páxina son "por mes", son doazóns únicas (non recorrentes). Consulte as <a %(faq)s>Preguntas Frecuentes sobre doazóns</a>. Anna's Archive (O Arquivo de Anna) é un proxecto sen ánimo de lucro, de código aberto e datos abertos. Ao donar e facerte membro, apoias as nosas operación e desenvolvemento. A todos os nosos membros: ¡grazas por manternos en marcha! ❤️ Para máis información, consulta as <a %(a_donate)s>Preguntas Frecuentes sobre doazóns</a>. Para facerse socio, <a %(a_login)s>Iniciar sesión ou Rexistrarse</a>. Grazas polo seu apoio! $%(cost)s / mes Se cometiches un erro durante o pagamento, non podemos facer devolucións, pero tentaremos arreglalo. Atopa a páxina “Crypto” na aplicación ou sitio web de PayPal. Normalmente atópase en "Finances". Vai a páxina "Bitcoin" na súa aplicación ou sitio web de PayPal. Peme o botón "Transferir" %(transfer_icon)s e, a continuación, "Enviar". Alipay Alipay / WeChat Tarxeta de regalo de Amazon %(amazon)s tarxeta regalo Tarxeta bancaria Tarxeta bancaria (usando app) Binance Crédito/débito/Apple/Google (BMC) Cash App Tarxeta de Crédito/débito Tarxeta de Crédito/Débito 2 Tarxeta de crédito/débito (reserva) Cripto%(bitcoin_icon)s Tarxeta / PayPal / Venmo PayPal (EEUU) %(bitcoin_icon)s PayPal PayPal (regular) Pix (Brazil) Revolut (non dispoñible temporalmente) WeChat Selecciona a súa criptomoneda de preferencia: Doe empregando una tarxeta regalo de Amazon. <strong>IMPORTANTE:</strong> Esta opción é para %(amazon)s. Se desexa usar outro sitio web de Amazon, selecciónao arriba. <strong>IMPORTANTE:</strong> Só soportamos Amazon.com, non outros sitios web de Amazon. Por exemplo, .de, .co.uk, .ca, NON son compatibles. Por favor, NON escriba a súa propia mensaxe. Introduza o importe exacto: %(amount)s Teña en conta que debemos redondear aos importes aceptados polos nosos revendedoresre (mínimo %(minimum)s). Doe usando unha tarxeta de crédito/débito, a través da app de Alipay (moi fácil de configurar). Instale a app de Alipay desde a <a %(a_app_store)s>Apple App Store</a> ou <a %(a_play_store)s>Google Play Store</a>. Rexístrese usando o seu número de teléfono. Non se requiren máis detalles persoais. <span %(style)s>1</span>Instale a app de Alipay Compatibles: Visa, MasterCard, JCB, Diners Club e Discover. Consulte <a %(a_alipay)s>esta guía</a> para máis información. <span %(style)s>2</span>Engadir tarxeta bancaria Con Binance, compras Bitcoin cunha tarxeta de crédito/débito ou conta bancaria, e logo doas ese Bitcoin a nós. Deste xeito, podemos manter a seguridade e o anonimato ao aceptar a túa doazón. Binance está dispoñible en case todos os países e admite a maioría dos bancos e tarxetas de crédito/débito. Actualmente, esta é a nosa principal recomendación. Agradecemos que tomes o tempo para aprender a doar usando este método, xa que nos axuda moito. Para tarxetas de crédito, tarxetas de débito, Apple Pay e Google Pay, usamos “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). No seu sistema, un “café” é igual a $5, polo que a túa doazón será redondeada ao múltiplo máis próximo de 5. Doa empregando Cash App. Se vostede dispón de Cash App, este é o método máis sinxelo para doar! Note que para doacións menores a %(amount)s, Cash App pode cobrarlle %(fee)s de comisión. Para doacións de %(amount)s ou máis, é gratuito! Doe con tarxeta de crédito ou débito. Este método usa un provedor de criptomoedas como conversión intermedia. Isto pode ser un pouco confuso, así que usa este método só se os outros métodos de pago non funcionan. Ademais, non funciona en todos os países. Non podemos aceptar tarxetas de crédito/débito directamente, porque os bancos non queren traballar connosco. ☹ Con todo, hai varias formas de usar tarxetas de crédito/débito de todos os xeitos, usando outros métodos de pago: Con cripto podes donar usando BTC, ETH, XMR e SOL. Utiliza esta opción se xa estás familiarizado coas criptomonedas. Con cripto podes donar empregando BTC, ETH, CMR e máis. Servizos exprés de cripto Se estás a usar criptomoedas por primeira vez, suxerímos usar %(options)s para mercar e doar Bitcoin (a criptomoeda orixinal e máis usada). Note que para pequenas doacións, as comisións das tarxetas de crédito poden eliminar o%(discount)s%% de desconto, así que recomendamos doacións máis largas. Doa usando tarxeta de crédito/débito, PayPal ou Venmo. Podes escoller entre estas na seguinte páxina. Google Pay e Apple Pay tamén poden funcionar. Teña en conta que para doacións pequenas as cuotas son elevadas, polo que recomendamos suscricións máis largas. Para doar empregando PayPal EEUU, empregaremos PayPal Crypto, que nos permite manter o anonimato. Agradecémoslle que se tome a molestia de aprender a donar con este método, xa que nos axuda moito. Doa empregando PayPal. Doa usando a túa conta regular de PayPal. Doa usando Revolut. Se tes Revolut, esta é a forma máis sinxela de doar! Este método de pago únicamente permite un máximo de %(amount)s. Por favor, seleccione una duración ou método de pago diferente. Este pagamento require un mínimo de %(amount)s. Por favor selecciona unha duración ou método de pago diferente. Binance Coinbase Kraken Por favor selecciona un método de pago. “Apadriña un torrent”: o teu nome de usuario ou mensaxe no nome dun arquivo torrent <div %(div_months)s>un por cada 12 meses de afiliación</div> O teu usuario ou mención anónima nos créditos Acceso anticipado a novas funcionalidades Telegram exclusivo con actualizacións entre bastidores %(number)s descargas rápidas por día se doas este mes! Acceso á <a %(a_api)s>API JSON</a> Estatus lexendario na preservación do coñecemento e cultura da humanidade Vantaxes anteriores, máis: Gaña <strong>%(percentage)s%% descargas de bonificación</strong> ao <a %(a_refer)s>referir amigos</a>. SciDB papers <strong>ilimitado</strong> sen verificación Ao preguntar sobre contas ou doazóns, engade o teu ID de conta, capturas de pantalla, recibos, tanta información como sexa posible. Só revisamos o noso correo electrónico cada 1-2 semanas, polo que non incluír esta información atrasará calquera resolución. Para conseguir aínda máis descargas, <a %(a_refer)s>recomenda aos teus amigos</a>! Somos un pequeno equipo de voluntarios. Pode levarnos de 1 a 2 semanas responder. Ten en conta que o nome da conta ou a imaxe poden parecer extranos. Non se preocupe ! Éstas contas están xestionadas polos nosos socios doantes. As nosas contas non foron pirateadas. Doa <span %(span_cost)s></span> <span %(span_label)s></span> 12 meses "%(tier_name)s" durante 1 mes "%(tier_name)s" 24 meses "%(tier_name)s" 3 meses "%(tier_name)s" durante 48 meses “%(tier_name)s” 6 meses "%(tier_name)s" durante 96 meses “%(tier_name)s” Aínda pode cancelar a doación durante o proceso de pagamento. Clica no botón de doación para confirmar esta doación. <strong>Nota importante:</strong> Os prezos das criptomoedas poden fluctuar moito, ás veces tanto como un 20%% en poucos minutos. Isto segue a ser menos que as comisións nas que incurrimos con moitos proveedores de pagos, que moitas veces cobran entre un 50-60%% por traballar con unha "organización benéfica na sombra". <u>Se nos envía o recibo co prezo orixinal que se pagou, abonaremos na súa conta o importe da afiliación elexida</u> (sempre e cando o recibo non teña máis dunhas horas de antiguedade). Agradecemos moito que estés disposto a pasar por este tedioso proceso para apoiarnos ! ❤️ ❌ Algo saíu mal. Por favor, recarga a páxina e volve a tentar. <span %(span_circle)s>1</span>Compra Bitcoin en Paypal <span %(span_circle)s>2</span>Transife o Bitcoin a nosa dirección ✅ Redireccionando á páxina de doación… Donar Espera polo menos <span %(span_hours)s>24 horas</span> (e actualiza esta páxina) antes de contactarnos. Se queres facer unha doazón (de calquera importe) sen membresía, podes facelo a través deste enderezo a Monero (XMR): %(address)s. Despois de enviar a túa tarxeta regalo, o noso sistema automatizado confirmará nuns minutos. Se isto non funciona, tenta reenviar a túa tarxeta regalo (<a %(a_instr)s>instructions</a>). Se segue sen funcionar, envíanos un correo electrónico e Anna o revisará manualmente (isto pode levar uns días), e asegúrate de mencionar se xa intentaches reenvialo. Exemplo: Por favor, emprega o <a %(a_form)s> formulario oficial de Amazon.com</a> para enviarnos unha tarxeta regalo de %(amount)s á dirección de correo electrónico que aparece a continuación. "Para" correo electrónico do destinatario no formulario: Tarxeta regalo de Amazon Non podemos aceptar outros métodos de tarxetas regalo, <strong>só as enviadas directamente dende o formulario oficial de Amazon.com</strong>. Non podemos devolver a túa tarxeta regalo se non empregas este formulario. Usar só unha vez. Único para a túa conta, non o compartas. Agardando tarxeta regalo... (actualiza a páxina para comprobalo) Abra a <a %(a_href)s>páxina de doazóns con código QR</a>. Escanee o código QR coa aplicación Alipay, ou prema o botón para abrir a aplicación Alipay. Por favor, sexa paciente; a páxina pode tardar un pouco en cargar xa que está en China. <span %(style)s>3</span>Faga unha doazón (escanear o código QR ou prema o botón) Comprar moeda PYUSD en PayPal Mercar Bitcoin (BTC) en Cash App Mercar un pouco máis (recomendamos %(more)s máis) que a cantidade que estás a doar (%(amount)s), para cubrir as taxas de transacción. Quedarás co que sobre. Vai á páxina de “Bitcoin” (BTC) en Cash App. Transfire o Bitcoin ao noso enderezo Para doazóns pequenas (menos de $25), pode que necesite usar Rush ou Priority. Preme o botón de “Enviar bitcoin” para facer unha “retirada”. Cambia de dólares a BTC premendo o icono %(icon)s. Introduce a cantidade de BTC a continuación e preme “Enviar”. Vexa <a %(help_video)s>este vídeo</a> se te quedas atascado. Os servizos exprés son convenientes, pero cobran taxas máis altas. Pode usar isto en lugar dun intercambio de cripto se está buscando facer unha doazón maior rapidamente e non lle importa unha taxa de $5-10. Asegúrese de enviar a cantidade exacta de cripto mostrada na páxina de doazóns, non a cantidade en $USD. Se non, a taxa será restada e non poderemos procesar automaticamente a súa subscrición. Ás veces a confirmación pode tardar ata 24 horas, así que asegúrese de actualizar esta páxina (aínda que xa caducou). Instruccións para tarxetas de Crédito / Débito Doar a través da nosa páxina de tarxeta de Crédito / Débito Algúns dos pasos mencionan carteiras criptográficas, pero non te preocupes, non tes que aprender nada sobre criptomoedas para isto. instruccións para %(coin_name)s Escanea este código QR coa túa aplicación de carteira cripto para encher rapidamente os detalles do pago Escanear o código QR para pagar Só admitimos a versión estándar de criptomoedas, nada de redes ou versións exóticas de moedas. A confirmación da transacción pode tardar ata unha hora, dependendo da moeda. Doa %(amount)s en <a %(a_page)s>esta páxina</a>. Esta doazón expirou. Por favor cancela e crea unha nova. Se xa pagou: Si, enviei o meu recibo por coreo electrónico Se o tipo de cambio de criptomoedas fluctuou durante a transacción, asegúrese de incluir o recibo que mostre o tipo de cambio orixinal. Agradecémoslle que se tome o tempo de empregar criptomoedas, ¡ Axúdanos moito ! ❌ Algo fallou. Volva a cargar a páxina e inténteo de novo. <span %(span_circle)s>%(circle_number)s</span>Envíanos o recibo por email Se tes algún probema, ponte en contacto con nós en %(email)s e inclúe toda a información posible (como capturas de pantalla). ✅ ¡Grazas pola túa doazón! Anna activará manualmente a túa afiliación nuns días. Evía un recibo ou unha captura de pantalla a súa dirección de verificación persoal: Cando xa teñas enviado o recibo por correo electrónico, fai click neste botón para que Anna poida revisalo manualmente (esto pode levar uns días): Envía un recibo ou captura de pantalla ao seu enderezo de verificación persoal. NON use este enderezo de correo electrónico para a súa doazón por PayPal. Cancelar Sí, cancela Estás seguro de que queres cancelar ? Non canceles se xa pagaches. ❌ Algo saíu mal. Por favor, recarga a páxina e inténtao de novo. Fazer unha nova doazón ✅ A túa doazón foi cancelada. Data: %(date)s Identificador: %(id)s Volver a comprar Estado: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes durante %(duration)s meses, incluíndo %(discounts)s%% de desconto)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes durante %(duration)s meses)</span> 1. Introduce o teu email. 2. Selecciona o teu método de pago. 3. Selecciona de novo o teu método de pago. 4. Selecciona carteira “Self-hosted”. 5. Clica “confirmo a propiedade”. 6. Deberias recibir un recibo no email. Por favor envíanolo e confirmaremos a túa doazón o antes posible. (pode ser que desexe cancelar e crear unha nova doazón) As instruccións de pagamento non están actualizadas. Se queres facer outra doazón, emprega o botón "Volver a comprar" que está arriba. Xa pagaches. Se queres revisar as instrucción de pagamento de igual maneira, clica aquí: Mostrar instruccións de pago antigas Se a páxina de doazóns está bloqueada, proba unha conexión a internet diferente (por exemplo, VPN ou internet do teléfono). Desafortunadamente, a páxina de Alipay adoita ser accesible só desde <strong>China continental</strong>. Pode que teña que desactivar temporalmente o seu VPN, ou usar un VPN para China continental (ou Hong Kong tamén funciona ás veces). <span %(span_circle)s>1</span>Doar en Alipay Doa o importe total de %(total)s usando <a %(a_account)s>esta conta de Alipay</a> Instruccións de Alipay <span %(span_circle)s>1</span>Transfire a unha das túas contas de criptomoedas Doa a cantidade total de %(total)s a algunha das seguintes direccións: Instruccións para criptomoedas Siga as instruccións para comprar Bitcoin (BTC). Únicamente debe comprar a cantidade que quera doar, %(total)s. Introduza a nosa dirección de Bitcoin (BTC) como destinatario e siga as instruccións para enviar a súa doazón de %(total)s: <span %(span_circle)s>1</span>Doar en Pix Doar o importe total de %(total)s empregando <a %(a_account)s>esta conta de Pix Instruccións para Pix <span %(span_circle)s>1</span>Doar en WeChat Doa o importe total de %(total)s usando <a %(a_account)s>esta conta de WeChat</a> Instrucións de WeChat Use calquera dos seguintes servizos exprés de “tarxeta de crédito a Bitcoin”, que só levan uns minutos: Enderezo de BTC / Bitcoin (carteira externa): Importe de BTC / Bitcoin: Encha os seguintes detalles no formulario: Se algunha desta información está desactualizada, envíenos un correo electrónico para informarnos. Utilice este <span %(underline)s>importe exacto</span>. O seu custo total pode ser maior debido ás taxas da tarxeta de crédito. Para importes pequenos, isto pode ser máis que o noso desconto, desafortunadamente. (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s, sen verificación para a primeira transacción) (mínimo: %(minimum)s) (mínimo: %(minimum)s dependendo do país, sen verificación para a primeira transacción) Segue as instruccións para comprar a moeda PYUSD (PayPal USD). Compra un pouco máis (recomendamos %(more)s máis) que a cantidade que vas doar (%(amount)s), para cubrir os gastos de transacción. Quedarás co que sobre. Vai á páxina "PYUSD" na súa aplicación ou sitio web de PayPal. Preme o botón "Transfer" %(icon)s, e despois, "Enviar". Actualizar estado Para reiniciar o temporizador, é suficiente con crear una nova doazón. Asegúrate de usar a cantidade de BTC a continuación, <em>NON</em> euros ou dólares, doutro xeito non recibiremos a cantidade correcta e non poderemos confirmar automaticamente a túa subscrición. Mercar Bitcoin (BTC) en Revolut Compre un pouco máis (recomendamos %(more)s máis) do importe que está a doar (%(amount)s), para cubrir as taxas de transacción. Gardará o que sobre. Vaia á páxina de “Crypto” en Revolut para mercar Bitcoin (BTC). Transfira o Bitcoin ao noso enderezo Para doazóns pequenas (menos de $25) pode que necesite usar Rush ou Priority. Faga clic no botón “Enviar bitcoin” para facer unha “retirada”. Cambie de euros a BTC premendo na icona %(icon)s. Introduza o importe en BTC a continuación e faga clic en “Enviar”. Vexa <a %(help_video)s>este vídeo</a> se se atopa con dificultades. Estado: 1 2 Guía paso a paso Consulta a guía paso a paso que figura a continuación. Do contrario, ¡pode que te bloqueen a conta! Se aínda non o fixeches, anota a túa clave secreta para iniciar sesión: Grazas pola túa doazón! Tempo restante: Doazón Transfire %(amount)s a %(account)s Agardando confirmación (actualice a páxina para comprobalo)… Agardando transferencia (actualice a páxina para comprobalo)… Anteriormente As descargas rápidas nas derradeiras 24 horas contan para o límite diario. As descargas desde servidores Fast Partner están marcadas con %(icon)s. Últimas 18 horas Aínda non se descargou ningún arquivo. Os arquivos descargados non se mostran públicamente. Todas as horas están en UTC. Arquivos descargados Se descargaches un arquivo con descargas rápidas e lentas, mostrarase dúas veces. Non te preocupes demasiado, hai moitas persoas descargando de sitios web enlazados por nós, e é extremadamente raro meterse en problemas. Con todo, para estar seguro recomendámosche usar un VPN (de pago), ou <a %(a_tor)s>Tor</a> (gratuíto). Descarguei 1984 de George Orwell, virá a policía á miña porta? Ti es Anna! Quen é Anna? Temos unha API JSON estable para membros, para obter unha URL de descarga rápida: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentación dentro do propio JSON). Para outros casos de uso, como iterar a través de todos os nosos ficheiros, construír buscas personalizadas, etc., recomendamos <a %(a_generate)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Os datos en bruto pódense explorar manualmente <a %(a_explore)s>a través de ficheiros JSON</a>. A nosa lista de torrents en bruto tamén se pode descargar como <a %(a_torrents)s>JSON</a>. Tes unha API? Non aloxamos ningún material con copyright aquí. Somos un motor de busca e, como tal, só indexamos metadatos que xa están dispoñibles publicamente. Ao descargar destas fontes externas, suxerímosche que comprobes as leis na túa xurisdición respecto ao que está permitido. Non somos responsables do contido aloxado por outros. Se tes queixas sobre o que ves aquí, o mellor é contactar co sitio web orixinal. Regularmente actualizamos a nosa base de datos cos seus cambios. Se realmente cres que tes unha queixa válida de DMCA á que debemos responder, enche o <a %(a_copyright)s>formulario de reclamación de DMCA / Copyright</a>. Tomamos as túas queixas en serio e responderemos o antes posible. Como podo informar sobre infraccións de copyright? Aquí tes algúns libros que teñen un significado especial para o mundo das bibliotecas na sombra e a preservación dixital: Cales son os teus libros favoritos? Tamén queremos lembrar a todos que todo o noso código e datos son completamente de código aberto. Isto é único para proxectos como o noso: non coñecemos ningún outro proxecto cun catálogo tan masivo que tamén sexa completamente de código aberto. Dámoslle a benvida a calquera que pense que xestionamos mal o noso proxecto a tomar o noso código e datos e crear a súa propia biblioteca na sombra! Non o dicimos por despeito ou algo así: realmente pensamos que sería incrible xa que elevaría o nivel para todos e preservaría mellor o legado da humanidade. ¡Odio como estás xestionando este proxecto! Encantaríanos que a xente montara <a %(a_mirrors)s>espellos</a>, e o apoiaremos económicamente. Como podo axudar ? Así é. A nosa inspiración para recoller metadatos é o obxectivo de Aaron Swartz de “unha páxina web para cada libro publicado”, para o cal creou <a %(a_openlib)s>Open Library</a>. Ese proxecto foi ben, pero a nosa posición única permítenos obter metadatos que eles non poden. Outra inspiración foi o noso desexo de saber <a %(a_blog)s>cantos libros hai no mundo</a>, para poder calcular cantos libros aínda temos que salvar. ¿ Recolledes metadatos ? Teña en conta que mhut.org bloquea certos rangos de IP, polo que pode ser necesario un VPN. <strong>Android:</strong> Fai clic no menú de tres puntos na parte superior dereita e selecciona “Engadir á pantalla de inicio”. <strong>iOS:</strong> Fai clic no botón “Compartir” na parte inferior e selecciona “Engadir á pantalla de inicio”. Non temos unha aplicación móbil oficial, pero podes instalar este sitio web como unha aplicación. Tes unha aplicación móbil? Por favor, envíeos ao <a %(a_archive)s>Internet Archive</a>. Eles preservaranos adecuadamente. Como podo doar libros ou outros materiais físicos? ¿ Cómo solicito libros ? <a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizacións regulares <a %(a_software)s>Software de Anna</a> — o noso código aberto <a %(a_datasets)s>Datasets</a> — sobre os datos <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternativos Hai máis recursos sobre Anna’s Archive? <a %(a_translate)s>Traducir no Software de Anna</a> — o noso sistema de tradución <a %(a_wikipedia)s>Wikipedia</a> — máis sobre nós (por favor, axuda a manter esta páxina actualizada, ou crea unha na túa propia lingua!) Seleccione as configuracións que lle gusten, deixe o cadro de busca baleiro, faga clic en “Buscar”, e logo marque a páxina usando a función de marcadores do seu navegador. Como gardo as miñas configuracións de busca? Dámoslle a benvida aos investigadores de seguridade para buscar vulnerabilidades nos nosos sistemas. Somos grandes defensores da divulgación responsable. Contacta connosco <a %(a_contact)s>aquí</a>. Actualmente non podemos ofrecer recompensas por erros, excepto por vulnerabilidades que teñan o <a %(a_link)s>potencial de comprometer o noso anonimato</a>, para as cales ofrecemos recompensas na franxa de $10k-50k. Gustaríanos ofrecer un alcance máis amplo para recompensas por erros no futuro! Ten en conta que os ataques de enxeñaría social están fóra do alcance. Se estás interesado na seguridade ofensiva e queres axudar a arquivar o coñecemento e a cultura do mundo, asegúrate de contactar connosco. Hai moitas formas nas que podes axudar. Tes un programa de divulgación responsable? Literalmente non temos suficientes recursos para dar a todos no mundo descargas de alta velocidade, por moito que nos gustaría. Se un benefactor rico quixese dar un paso adiante e proporcionarnos isto, sería incrible, pero ata entón, estamos facendo o mellor que podemos. Somos un proxecto sen ánimo de lucro que apenas se pode soster a través de doazóns. Por iso implementamos dous sistemas para descargas gratuítas, cos nosos socios: servidores compartidos con descargas lentas, e servidores lixeiramente máis rápidos cunha lista de espera (para reducir o número de persoas descargando ao mesmo tempo). Tamén temos <a %(a_verification)s>verificación do navegador</a> para as nosas descargas lentas, porque doutro xeito os bots e os scrapers abusarían delas, facendo as cousas aínda máis lentas para os usuarios lexítimos. Teña en conta que, ao usar o Tor Browser, pode que necesite axustar a configuración de seguridade. Na opción máis baixa, chamada “Estándar”, o desafío de Cloudflare turnstile ten éxito. Nas opcións máis altas, chamadas “Máis seguro” e “O máis seguro”, o desafío falla. Para arquivos grandes, ás veces as descargas lentas poden romperse no medio. Recomendamos usar un xestor de descargas (como JDownloader) para retomar automaticamente as descargas grandes. Por que as descargas lentas son tan lentas? Preguntas Frecuentes (FAQ - Frequently Asked Questions) Usa o <a %(a_list)s>xenerador de listas de torrents</a> para xerar unha lista de torrents que máis precisan ser compartidos, dentro dos límites do teu espazo de almacenamento. Si, consulta a páxina de <a %(a_llm)s>datos de LLM</a>. A maioría dos torrents conteñen os ficheiros directamente, o que significa que podes instruír aos clientes de torrents para que só descarguen os ficheiros necesarios. Para determinar que ficheiros descargar, podes <a %(a_generate)s>xerar</a> os nosos metadatos, ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Desafortunadamente, un número de coleccións de torrents conteñen ficheiros .zip ou .tar na raíz, nese caso necesitas descargar todo o torrent antes de poder seleccionar ficheiros individuais. Aínda non hai ferramentas fáciles de usar para filtrar torrents, pero agradecemos as contribucións. (Aínda que temos <a %(a_ideas)s>algunhas ideas</a> para este último caso.) Resposta longa: Resposta curta: non facilmente. Tentamos manter a duplicación ou solapamento mínimo entre os torrents nesta lista, pero isto non sempre se pode conseguir, e depende moito das políticas das bibliotecas fonte. Para as bibliotecas que publican os seus propios torrents, está fóra das nosas mans. Para os torrents lanzados por Anna’s Archive, deduplicamos só baseándonos no hash MD5, o que significa que diferentes versións do mesmo libro non se deduplican. Si. Estes son realmente PDFs e EPUBs, só que non teñen unha extensión en moitos dos nosos torrents. Hai dous lugares onde podes atopar os metadatos para os ficheiros de torrents, incluíndo os tipos/extensións de ficheiros: 1. Cada colección ou lanzamento ten os seus propios metadatos. Por exemplo, os <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> teñen unha base de datos de metadatos correspondente aloxada no sitio web de Libgen.rs. Normalmente ligamos aos recursos de metadatos relevantes desde a <a %(a_datasets)s>páxina do conxunto de datos</a> de cada colección. 2. Recomendamos <a %(a_generate)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Estas conteñen un mapeo para cada rexistro en Anna’s Archive cos seus ficheiros de torrent correspondentes (se están dispoñibles), baixo “torrent_paths” no JSON de ElasticSearch. Algúns clientes de torrent non admiten tamaños de peza grandes, que moitos dos nosos torrents teñen (para os máis novos xa non facemos isto, aínda que é válido segundo as especificacións!). Así que proba con outro cliente se te atopas con isto, ou queixate aos creadores do teu cliente de torrent. Gustaríame axudar a sementar, pero non teño moito espazo en disco. Os torrents son demasiado lentos; podo descargar os datos directamente de vós? Podo descargar só un subconxunto dos ficheiros, como só un idioma ou tema en particular? Como manexades os duplicados nos torrents? ¿Podo obter a lista de torrents como JSON? Non vexo PDFs ou EPUBs nos torrents, só ficheiros binarios? Que fago? Por que o meu cliente de torrent non pode abrir algúns dos vosos ficheiros torrent / ligazóns magnet? FAQ de Torrents Cómo subo libros novos ? Consulte <a %(a_href)s>este excelente proxecto</a>. Tes un monitor de tempo de actividade? Qué é Anna's Archive ? Faite socio para empregar descargas rápidas. Agora aceptamos tarxetas regalo de Amazon, tarxetas de crédito e débito, criptomoedas, Alipay e WeChat. Quedaches sen descargas rápidas por hoxe. Acceso Descargas por hora nos últimos 30 días. Media horaria: %(hourly)s. Media diaria: %(daily)s. Traballamos con socios para que calquera poida acceder as nosas coleccións de forma fácil e gratuita. Creemos que todo o mundo ten dereito á sabiduría colectiva da humanidade. E <a %(a_search)s>non a costa dos autores</a>. Os conxuntos de datos (datasets) empregados en Anna’s Archive son totalmente aberto e poden reproducirse masivamente mediante torrents. <a %(a_datasets)s>Lee máis…</a> Arquivo a longo prazo Base de datos completa Busca na base de datos completa Libros, periódicos, revistas, cómics, rexistros de bibliotecas, metadatos, … Todos o noso <a %(a_code)s>código</a> e <a %(a_datasets)s>datos</a> son completamente de código aberto. <span %(span_anna)s>Anna’s Archive</span> é un proxecto sen ánimo de lucro con dous obxectivos: <li><strong>Preservación:</strong> Respaldar todo o coñecemento e a cultura da humanidade.</li><li><strong>Acceso:</strong> Poñer este coñecemento e cultura a disposición de calquera persoa do mundo.</li> Dispoñemos da maior colección do mundo de datos de texto de alta calidade. <a %(a_llm)s>Leer máis…</a> Datos de entrenamento de modelos LLM (Large Language Model) 🪩 Espellos: unha chamada a voluntarios Se xestionas un procesador de pagos anónimos de alto risco, ponte en contacto con nós. Tamén buscamos persoas que desexen publicar anuncios de bo gusto. Todos os ingresos destinaranse aos nosos esforzos pola conservación. Preservación Estimamos que temos preservado a redor de <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% de todo-los libros do mundo</a>. Preservamos libros, periódicos, cómics, revistas e moito máis, reunindo estos materiais de diversas <a href="https://en.wikipedia.org/wiki/Shadow_library">bibliotecas na sombra</a>, bibliotecas oficiais, e outras coleccións en un mesmo lugar. Todos estes datos se preservan para sempre facilitando a súa duplicación masiva - mediante torrents -, o que da lugar a numerosas copias en todo o mundo (Por exemplo: Sci-Hub, Library Genesis), mentres que Anna's Archive "libera" outras bibliotecas que non ofrecen distribución masiva (Por exemplo Z-Library) ou que non son bibliotecas na sombra en absoluto (Por exemplo: Internet Archive, DuXiu). Esta amplia difusión, combinada co código aberto, fai que o noso sitio web sexa resistente aos ataques e garantiza a conservación a largo prazo do coñecemento e a cultura da humanidade. Lea máis sobre <a href="/datasets">os nosos datasets</a>. Se eres un <a %(a_member)s>membro</a>, a verificación de navegador non é requerida. 🧬&nbsp;SciDB é unha continuación de Sci-Hub. SciDB Aberto DOI Sci-Hub ten unha subida <a %(a_paused)s>pausada</a> de novos artículos. Acceso directo a %(count)s artículos académicos 🧬&nbsp;SciDB é unha continuación de Sci-Hub, coa súa interface familiar e visualización directa de PDFs. Introduza o seu DOI para ver. Temos a colección completa de Sci-Hub, así como novos artigos. A maioría pódense ver directamente cunha interface familiar, similar a Sci-Hub. Algúns pódense descargar a través de fontes externas, nese caso mostramos ligazóns a esas fontes. Podes axudar enormemente sembrando torrents. <a %(a_torrents)s>Leer máis…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Buscamos voluntarios Como proxecto sen ánimo de lucro e de código aberto, sempre estamos buscando xente para axudar. Descargas IPFS Ordear por %(by)s, creación <span %(span_time)s>%(time)s</span> Gardar ❌ Algo fallou. Por favor, volve a intentalo. ✅ Gardado. Por favor, refresca a páxina. A lista está valeira. editar Engade ou elimina arquivos desta lista buscando un arquivo e abrindo a lapela "Lista". Lista Como podemos axudar Eliminación de superposicións (deduplicación) Extracción de texto e metadatos OCR Podemos proporcionar acceso de alta velocidade ás nosas coleccións completas, así como a coleccións non publicadas. Este é un acceso a nivel empresarial que podemos proporcionar por doazóns no rango de decenas de miles de USD. Tamén estamos dispostos a intercambiar isto por coleccións de alta calidade que aínda non temos. Podemos reembolsarlle se pode proporcionarnos enriquecemento dos nosos datos, como: ¡Apoie o arquivo a longo prazo do coñecemento humano, mentres obtén mellores datos para o seu modelo! <a %(a_contact)s>Contacte connosco</a> para discutir como podemos traballar xuntos. Enténdese ben que os LLM prosperan con datos de alta calidade. Temos a maior colección de libros, artigos, revistas, etc. do mundo, que son algunhas das fontes de texto de maior calidade. Datos de LLM Escala e alcance únicos A nosa colección contén máis de cen millóns de arquivos, incluíndo revistas académicas, libros de texto e revistas. Conseguimos esta escala combinando grandes repositorios existentes. Algunhas das nosas coleccións de orixe xa están dispoñibles en masa (Sci-Hub e partes de Libgen). Outras fontes liberámolas nós mesmos. <a %(a_datasets)s>Datasets</a> mostra unha visión completa. A nosa colección inclúe millóns de libros, artigos e revistas de antes da era do libro electrónico. Grandes partes desta colección xa foron OCRizadas e xa teñen pouca superposición interna. Continuar Se perdiches a túa chave, por favor <a %(a_contact)s>contáctanos</a> e proporciona tanta información como sexa posible. É posible que teñas que crear temporalmente unha nova conta para contactar con nós. Por favor <a %(a_account)s>inicia sesión</a>para ver esta páxina.</a> Para evitar que os bots de spam creen moitas contas, primeiro temos que verificar o teu navegador. Se quedas atrapado nun bucle infinito, recomendámosche instalar <a %(a_privacypass)s>Privacy Pass</a>. Tamén pode ser útil desactivar os bloqueadores de anuncios e outras extensións do navegador. Iniciar sesión / Rexistrarse O Arquivo de Anna está temporalmente fóra de servizo por mantemento. Volve nunha hora. Autor alternativo Descrición alternativa Edición alternativa Extensión alternativa Nome de ficheiro alternativo Editor alternativo Título alternativo data de lanzamento en Anna's Archive Lee máis… descripción Busca en Anna's Archive o número CADAL SSNO Busca en Anna's Archive o número SSID de DuXiu Busca en Anna's Archive o número DuXiu DXID Buscar ISBN no Arquivo de Anna Buscar en Anna's Archive o número OCLC (WorldCat) Buscar en Anna's Arquive polo Open Library ID Visor en liña de Anna’s Archive %(count)s páxinas afectadas Despois de descargar: Unha versión mellor deste arquivo pode estar dispoñible en %(link)s Descargas masivas de torrents colección Use ferramentas en liña para converter entre formatos. Ferramentas de conversión recomendadas: %(links)s Para arquivos grandes, recomendamos usar un xestor de descargas para evitar interrupcións. Xestores de descargas recomendados: %(links)s Índice de eBooks de EBSCOhost (só expertos) (pulse tamén "GET" na parte superior) (pulse "GET" na parte superior) Descargas externas <strong>🚀 Descargas rápidas</strong> Quédanlle %(remaining)s descargas hoxe . Grazas por ser un socio ! ❤️ <strong>🚀 Descargas rápidas</strong> Quedaches sen descargas rápidas por hoxe. <strong>🚀 Descargas rápidas</strong> Vostede descargou este arquivo recentemente. Os enlaces seguen sendo válidos durante un tempo. <strong>🚀 Descargas rápidas</strong> Fágase <a %(a_membership)s>socio</a> para apoiar a conservación a longo prazo de libros, documentos, e moito máis. Como mostra da nosa gratitude, obterá descargas rápidas. ❤️ 🚀 Descargas rápidas 🐢 Descargas lentas Tomar prestado de Internet Archive IPFS Gateway #%(num)d (pode que teña que intentalo varias veces con IPFS) Libgen.li Libgen.rs Ficción Libgen.rs Non Ficción os seus anuncios son coñecidos por conter software malicioso, así que usa un bloqueador de anuncios ou non fagas clic nos anuncios “Enviar a Kindle” de Amazon “Enviar a Kobo/Kindle” de djazz MagzDB ManualsLib Nexus/STC (Os ficheiros Nexus/STC poden ser pouco fiables para descargar) Non se atoparon descargas. Todas as opcións de descarga teñen o mesmo arquivo e deberían ser seguras. Dito isto, ten sempre coidado ao descargar arquivos de Internet, especialmente de sitios externos a Anna's Archive. Por exemplo, asegúrate de manter actualizados os teus dispositivos. (sen redirección) Abrir no noso visor (abrir no visor) Opción #%(num)d: %(link)s %(extra)s Atopa o rexistro orixinal en CADAL Busca manualmente en DuXiu Atopa o rexistro orixinal no ISBNdb Atopa o rexistro orixinal en WorldCat Atopa o rexistro orixinal na Open Library Buscar ISBN noutras bases de datos (únicamente para usuarios con problemas de impresión) PubMed Necesitará un lector de ebooks ou PDF para abrir o arquivo, dependendo do formato do arquivo. Lectores de ebooks recomendados: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (O DOI asociado pode non estar dispoñible en Sci-Hub) Pode enviar arquivos PDF e EPUB ao seu Kindle ou Kobo eReader. Ferramentas recomendadas: %(links)s Máis información no <a %(a_slow)s>FAQ</a>. Apoiar autores e bibliotecas Se lle gusta isto e pode permitilo, considere comprar o orixinal ou apoiar directamente aos autores. Se isto está dispoñible na súa biblioteca local, considere pedilo prestado de balde alí. As descargas do Servidor Partner non están dispoñibles temporalmente para este arquivo. torrent De partners de confianza. Z-Library Z-Library en Tor (requiere o Navegador Tor) mostrar descargas externas <span class="font-bold">❌ Este arquivo pode ter problemas e foi ocultado dunha biblioteca de fontes.</span> Ás veces isto débese a unha petición do titular de dereitos de autor, outras veces é porque hai unha alternativa mellor dispoñible, pero ás veces débese a un problema co propio arquivo. É posible que aínda poida descargarse, pero recomendámoslle que primeiro procure un arquivo alternativo. Máis información: Se aínda así desexa descargar o arquivo, asegúrese de empregar únicamente software actualizado e de confianza para abrilo. comentarios sobre metadata AA: Buscar "%(name)s" en Anna's Archive Explorador de Códigos: Ver en Explorador de Códigos “%(name)s” URL: Sitio Web: Se tes éste arquivo e nón está dispoñible en Anna's Archive todavía, considera <a %(a_request)s>subilo</a>. Arquivo de préstamo dixital controlado de Internet Archive "%(id)s" Esto é un rexistro dun arquivo do Internet Archive, non un arquivo descargable directamente. Podes intentar tomar prestado o libro (link abaixo),ou empregar ésta URL cando <a %(a_request)s>solicites un arquivo</a>. Mellorar metadatos Rexistro da metadata de CADAL SSNO %(id)s Esto é un rexistro de metadata, non un arquivo descargable. Podes empregar ésta URL cando <a %(a_request)s>solicites un arquivo</a>. Rexistro da metadata de DuXiu SSID %(id)s Rexistro de metadata da ISBNdb %(id)s Rexistro de metadatos de MagzDB ID %(id)s Rexistro de metadatos de Nexus/STC ID %(id)s Rexistro da metadata do número OCLC (WorldCat) %(id)s Rexistro de metadata da Open Library %(id)s Arquivo Sci-Hub “%(id)s” Non atopado “%(md5_input)s” non foi atopado na nosa base de datos. Engadir comentario (%(count)s) Podes obter o md5 da URL, por exemplo MD5 dunha versión mellor deste ficheiro (se é aplicable). Enche isto se hai outro ficheiro que coincida estreitamente con este ficheiro (mesma edición, mesma extensión de ficheiro se podes atopar un), que a xente debería usar en lugar deste ficheiro. Se coñeces unha versión mellor deste ficheiro fóra do Arquivo de Anna, entón por favor <a %(a_upload)s>cárgaa</a>. Algo saíu mal. Por favor, recarga a páxina e inténtao de novo. Deixaches un comentario. Pode tardar un minuto en amosarse. Por favor, usa o <a %(a_copyright)s>formulario de reclamación de DMCA / Dereitos de autor</a>. Describe o problema (requirido) Se este ficheiro ten gran calidade, podes discutir calquera cousa sobre el aquí! Se non, por favor usa o botón “Informar dun problema co ficheiro”. Boa calidade do ficheiro (%(count)s) Calidade do ficheiro Aprende como <a %(a_metadata)s>mellorar os metadatos</a> deste ficheiro ti mesmo. Descrición do problema Por favor, <a %(a_login)s>inicia sesión</a>. Encantoume este libro! ¡Axuda á comunidade informando sobre a calidade deste ficheiro! 🙌 Algo saíu mal. Por favor, recarga a páxina e inténtao de novo. Informar sobre un problema co ficheiro (%(count)s) Grazas por enviar o teu informe. Amosarase nesta páxina, así como revisado manualmente por Anna (ata que teñamos un sistema de moderación adecuado). Deixar comentario Enviar informe Que está mal con este ficheiro? Préstamo (%(count)s) Comentarios (%(count)s) Descargas (%(count)s) Explorar metadatos (%(count)s) Listas (%(count)s) Estadísticas (%(count)s) Para información sobre este ficheiro en particular, consulta o seu <a %(a_href)s>ficheiro JSON</a>. Este é un ficheiro xestionado pola biblioteca de <a %(a_ia)s>Préstamo Dixital Controlado do IA</a>, e indexado por Anna’s Archive para a busca. Para información sobre os diferentes datasets que temos compilado, consulta a <a %(a_datasets)s>páxina de Datasets</a>. Metadatos do rexistro enlazado Mellorar os metadatos en Open Library Un “MD5 do ficheiro” é un hash que se calcula a partir do contido do ficheiro, e é razoablemente único baseado nese contido. Todas as bibliotecas sombra que temos indexadas aquí usan principalmente MD5s para identificar ficheiros. Un ficheiro pode aparecer en múltiples bibliotecas sombra. Para información sobre os diferentes datasets que temos compilado, consulta a <a %(a_datasets)s>páxina de Datasets</a>. Informar sobre a calidade do ficheiro Descargas totais: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadatos checos %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Aviso: múltiples rexistros enlazados: Cando mira un libro en Arquivo de Anna, pode ver varios campos: título, autor, editorial, edición, ano, descrición, nome do ficheiro, e máis. Todas esas pezas de información chámanse <em>metadatos</em>. Como combinamos libros de varias <em>bibliotecas fonte</em>, mostramos os metadatos dispoñibles nesa biblioteca fonte. Por exemplo, para un libro que obtivemos de Library Genesis, mostraremos o título da base de datos de Library Genesis. Ás veces un libro está presente en <em>múltiples</em> bibliotecas fonte, que poden ter diferentes campos de metadatos. Nese caso, simplemente mostramos a versión máis longa de cada campo, xa que esa, con sorte, contén a información máis útil! Aínda mostraremos os outros campos debaixo da descrición, por exemplo, como "título alternativo" (pero só se son diferentes). Tamén extraemos <em>códigos</em> como identificadores e clasificadores da biblioteca fonte. <em>Os identificadores</em> representan de forma única unha edición particular dun libro; exemplos son ISBN, DOI, Open Library ID, Google Books ID, ou Amazon ID. <em>Os clasificadores</em> agrupan varios libros similares; exemplos son Dewey Decimal (DCC), UDC, LCC, RVK, ou GOST. Ás veces estes códigos están explicitamente vinculados nas bibliotecas fonte, e ás veces podemos extraelos do nome do ficheiro ou da descrición (principalmente ISBN e DOI). Podemos usar identificadores para atopar rexistros en <em>coleccións só de metadatos</em>, como OpenLibrary, ISBNdb, ou WorldCat/OCLC. Hai unha <em>pestaña de metadatos</em> específica no noso motor de busca se desexa explorar esas coleccións. Usamos rexistros coincidentes para encher campos de metadatos que faltan (por exemplo, se falta un título), ou por exemplo, como "título alternativo" (se hai un título existente). Para ver exactamente de onde proviñeron os metadatos dun libro, consulte a <em>pestaña "Detalles técnicos"</em> na páxina dun libro. Ten unha ligazón ao JSON en bruto dese libro, con referencias ao JSON en bruto dos rexistros orixinais. Para máis información, consulte as seguintes páxinas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Busca (pestaña de metadatos)</a>, <a %(a_codes)s>Explorador de códigos</a>, e <a %(a_example)s>Exemplo de metadatos JSON</a>. Finalmente, todos os nosos metadatos poden ser <a %(a_generated)s>xenerados</a> ou <a %(a_downloaded)s>descargados</a> como bases de datos ElasticSearch e MariaDB. Información Pode axudar á preservación dos libros mellorando os metadatos! Primeiro, lea a información sobre metadatos en Arquivo de Anna, e despois aprenda como mellorar os metadatos a través da vinculación con Open Library, e gañe unha subscrición gratuíta en Arquivo de Anna. Mellorar os metadatos Entón, se atopa un ficheiro con malos metadatos, como debería arranxalo? Pode ir á biblioteca fonte e seguir os seus procedementos para arranxar os metadatos, pero que facer se un ficheiro está presente en varias bibliotecas fonte? Hai un identificador que se trata de forma especial en Arquivo de Anna. <strong>O campo annas_archive md5 en Open Library sempre sobrescribe todos os outros metadatos!</strong> Retrocedamos un pouco primeiro e aprendamos sobre Open Library. Open Library foi fundada en 2006 por Aaron Swartz co obxectivo de "unha páxina web para cada libro publicado". É unha especie de Wikipedia para metadatos de libros: todos poden editala, está licenciada libremente, e pode ser descargada en masa. É unha base de datos de libros que está máis aliñada coa nosa misión — de feito, Arquivo de Anna foi inspirada pola visión e vida de Aaron Swartz. En lugar de reinventar a roda, decidimos redirixir os nosos voluntarios cara a Open Library. Se ve un libro que ten metadatos incorrectos, pode axudar do seguinte xeito: Ten en conta que isto só funciona para libros, non para artigos académicos ou outros tipos de arquivos. Para outros tipos de arquivos aínda recomendamos atopar a biblioteca fonte. Pode levar unhas semanas para que os cambios se inclúan en Anna’s Archive, xa que necesitamos descargar o último volcado de datos de Open Library e rexenerar o noso índice de busca.  Vaia ao <a %(a_openlib)s>sitio web de Open Library</a>. Atopa o rexistro correcto do libro. <strong>AVISO:</strong> asegúrese de seleccionar a <strong>edición</strong> correcta. En Open Library, ten "obras" e "edicións". Unha "obra" podería ser "Harry Potter e a Pedra Filosofal". Unha "edición" podería ser: A primeira edición de 1997 publicada por Bloomsbery con 256 páxinas. A edición de 2003 en tapa branda publicada por Raincoast Books con 223 páxinas. A tradución polaca de 2000 “Harry Potter I Kamie Filozoficzn” por Media Rodzina con 328 páxinas. Todas esas edicións teñen diferentes ISBNs e contidos distintos, así que asegúrate de seleccionar a correcta! Edita o rexistro (ou créao se non existe) e engade tanta información útil como poidas! Xa que estás aquí, mellor fai que o rexistro sexa realmente incrible. Baixo “Números de ID” selecciona “Anna’s Archive” e engade o MD5 do libro de Anna’s Archive. Este é o longo cadea de letras e números despois de “/md5/” na URL. Tenta atopar outros arquivos en Anna’s Archive que tamén coincidan con este rexistro, e engádeos tamén. No futuro poderemos agrupalos como duplicados na páxina de busca de Anna’s Archive. Cando remates, anota a URL que acabas de actualizar. Unha vez que teñas actualizado polo menos 30 rexistros con MD5s de Anna’s Archive, envíanos un <a %(a_contact)s>correo electrónico</a> e envíanos a lista. Darémosche unha subscrición gratuíta para Anna’s Archive, para que poidas facer este traballo máis facilmente (e como agradecemento pola túa axuda). Estas deben ser edicións de alta calidade que engadan cantidades substanciais de información, doutro xeito a túa solicitude será rexeitada. A túa solicitude tamén será rexeitada se algunha das edicións é revertida ou corrixida polos moderadores de Open Library. Vinculación con Open Library Se se involucra significativamente no desenvolvemento e operacións do noso traballo, podemos discutir compartir máis dos ingresos por doazóns con vostede, para que os despregue segundo sexa necesario. Só pagaremos polo aloxamento unha vez que teña todo configurado e demostre que é capaz de manter o arquivo actualizado con actualizacións. Isto significa que terá que pagar os primeiros 1-2 meses do seu propio peto. O seu tempo non será compensado (e o noso tampouco), xa que este é un traballo puramente voluntario. Estamos dispostos a cubrir os gastos de aloxamento e VPN, inicialmente ata $200 por mes. Isto é suficiente para un servidor de busca básico e un proxy protexido por DMCA. Gastos de aloxamento Por favor, <strong>non nos contacte</strong> para pedir permiso ou para preguntas básicas. ¡As accións falan máis alto que as palabras! Toda a información está dispoñible, así que simplemente adiante coa configuración do seu espello. Non dubide en publicar tickets ou solicitudes de fusión no noso Gitlab cando teña problemas. Podemos necesitar construír algunhas funcións específicas para espellos con vostede, como rebranding de “Anna’s Archive” ao nome do seu sitio web, (inicialmente) desactivar contas de usuario, ou ligar de volta ao noso sitio principal desde as páxinas dos libros. Unha vez que teña o seu espello en funcionamento, por favor, contacte connosco. Gustaríanos revisar a súa OpSec, e unha vez que estea sólida, ligaremos ao seu espello e comezaremos a traballar máis estreitamente con vostede. ¡Grazas de antemán a calquera que estea disposto a contribuír deste xeito! Non é para os de corazón débil, pero solidificaría a lonxevidade da maior biblioteca verdadeiramente aberta na historia da humanidade. Comezando Para aumentar a resiliencia do Arquivo de Anna, estamos buscando voluntarios para executar espellos. A súa versión está claramente distinguida como un espello, por exemplo, “Arquivo de Bob, un espello de Anna’s Archive”. Está disposto a asumir os riscos asociados con este traballo, que son significativos. Ten un profundo entendemento da seguridade operativa requirida. O contido de <a %(a_shadow)s>estas</a> <a %(a_pirate)s>publicacións</a> é evidente para vostede. Inicialmente non lle daremos acceso ás descargas do noso servidor asociado, pero se todo vai ben, podemos compartilo con vostede. Executa o código aberto de Anna’s Archive e actualiza regularmente tanto o código como os datos. Está disposto a contribuír á nosa <a %(a_codebase)s>base de código</a> — en colaboración co noso equipo — para que isto suceda. Estamos buscando isto: Espellos: chamada a voluntarios Facer outra doazón. Non hai doazóns todavía. <a %(a_donate)s>Facer a miña primeira doazón.</a> Os detalles das doazóns non se mostran públicamente. As miñas doazóns 📡 Para duplicar a nosa colección, consulta as páxinas <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>. Descargas dende a súa dirección IP nas últimas 24 horas: %(count)s. 🚀 Para conseguir descargas máis rápidas e saltar as comprobacións do navegador, <a %(a_membership)s>faite socio</a>. Descargar do sitio web do noso Partner Podes seguir navegando polo Arquivo de Anna nunha pestana diferente mentres esperas (se o teu navegador admite a actualización de pestanas en segundo plano). Non dubides en esperar a que se carguen varias páxinas de descarga ao mesmo tempo (pero por favor só descarga un ficheiro á vez por servidor). Unha vez que obteñas unha ligazón de descarga, será válida durante varias horas. Grazas por esperar, isto mantén o sitio web accesible de balde para todos! 😊 🔗 Todos os enlaces de descarga de este arquivo: <a %(a_main)s>Páxina principal do arquivo</a>. ❌ As descargas lentas non están dispoñibles a través de VPN de Cloudflare ou de outro modo desde direccións IP de Cloudflare. ❌ As descargas lentas só están dispoñibles a través do sitio web oficial. Visita %(websites)s. 📚 Emprega a seguinte URL para descargar: <a %(a_download)s>Descargar agora</a>. Co fin de dar a todos a oportunidade de descargar ficheiros de balde, ten que agardar antes de poder descargar este ficheiro. Agarde <span %(span_countdown)s>%(wait_seconds)s</span> segundos para descargar este ficheiro. Advertencia: producíronse moitas descargas dende a súa dirección IP nas últimas 24 horas. As descargas poden ser máis lentas do habitual. Se estás a usar unha VPN, unha conexión a internet compartida ou o teu ISP comparte IPs, este aviso pode deberse a iso. Gardar ❌ Algo fallou. Por favor volve a intentalo. ✅ Gardado. Por favor, refresca a páxina. Cambia o teu nome público. O teu identificador (a parte despois do "#") non pode ser cambiada. Perfil creado o <span %(span_time)s>%(time)s</span> editar Listas Crea unha nova lista buscando un arquivo e abrindo a lapela "Listas". Aínda non hai listas Perfil non atopado. Pefil Polo momento, non podemos antender solicitudes de libros. Non nos contactes para facer solicitudes de libros. Por favor fai as túas solicitudes en foros de Z-Library ou Libgen. Rexistro en Anna's Archive DOI: %(doi)s Descargar SciDB Nexus/STC Aínda non hai vista previa dispoñible. Descarga o ficheiro desde <a %(a_path)s>O Arquivo de Anna</a>. Para apoiar a accesibilidade e a preservación a longo prazo do coñecemento humano, faise <a %(a_donate)s>membro</a>. Como bonificación, 🧬&nbsp;SciDB carga máis rápido para os membros, sen ningún límite. Non funciona ? Intenta <a %(a_refresh)s>refrescar</a>. Sci-Hub Engadir campo de búsqueda específico Descripcións de búsqueda e comentarios de metadatos Ano de publicación Avanzado Acceso Contido Amosar Lista Táboa Tipo de arquivo Idioma Ordear por Máis grande Máis relevante Máis novo (tamaño de arquivo) (de código aberto) (ano de publicación) Máis antigo Aleatorio Máis pequeno Fonte recuperado e de código aberto por AA Préstamos dixitais (%(count)s) Artículos de revista (%(count)s) Atopamos coincidencias en: %(in)s. Pode facer referencia a URL atopada alí cando <a %(a_request)s>solicite un arquivo</a>. Metadatos (%(count)s) Para explorar o índice de busca por códigos, usa o <a %(a_href)s>Explorador de Códigos</a>. O índice de búsqueda actualízase mensualmente. Actualmente inclúe entradas ata %(last_data_refresh_date)s. Para máis información técnica, consulte a %(link_open_tag)spáxina de datasets</a>. Excluir Incluir só Sen verificar máis… Seguinte … Anterior Este índice de búsqueda inclúe actualmente metadatos da Biblioteca de Péstamo Dixital (Controlled Digital Lending) de Internet Arquive. <a %(a_datasets)s>Máis sobre os nosos datasets</a>. Para coñecer máis bibliotecas de préstamo dixital, consulte <a %(a_wikipedia)s>Wikipedia</a> e a <a %(a_mobileread)s>MobileRead Wiki</a>. Para reclamacióons de DMCA / copyright <a %(a_copyright)s>clica aquí</a>. Tempo de descargar Erro durante a búsqueda. Intenta <a %(a_reload)s>refrescar a páxina</a>. Se o problema persiste, por favor contacta con nós en %(email)s. Descarga rápida De feito, calquera pode axudar a preservar estos arquivos sembrando a nosa <a %(a_torrents)s>lista unificada de torrents</a>. ➡️ Ás veces isto ocorre incorrectamente cando o servidor de busca está lento. Neses casos, <a %(a_attrs)s>recargar</a> pode axudar. ❌ Este arquivo pode ter problemas. ¿ Busca documentos ? Este índice de búsqueda inclúe actualmente metadatos de varias fontes de metadatos. <a %(a_datasets)s>Máis información sobre os nosos datasets (conxuntos de datos)</a>. Existen moitísimas fontes de metadatos para obras escritas en todo o mundo. <a %(a_wikipedia)s>Esta páxina de Wikipedia</a> é un bo comezo, pero se coñece outras boas listas, non dude en comunicárnolo. Para os metadatos, mostramos os rexistros orixinais. Non facemos ningunha fusión de rexistros. Actualmente dispoñemos do catálogo aberto de libros, artículos e outras obras escritas máis completo do mundo. Somos espello de Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e máis</a>. <span class="font-bold">Non se atoparon arquivos.</span> Intenta con menos ou diferentes termos de búsqueda e filtros. Resultados %(from)s-%(to)s (%(total)s total) Se atopas outras "bibliotecas na sombra" que debamos reflexar, ou se tes algunha pregunta, póñase en contacto con nós en %(email)s. %(num)d coincidencias parciais %(num)d+ de coincidencias parciais Escriba na caixa para buscar arquivos nas bibliotecas de préstamo dixital. Escriba na caixa para buscar directamente no noso catálogo de %(count)s arquivos descargables, que <a %(a_preserve)s>conservamos para sempre</a>. Escriba na caixa para buscar. Escriba na caixa para buscar no noso catálogo de %(count)s traballos académicos e artículos de revistas, que <a %(a_preserve)s>conservamos para sempre</a>. Escriba na caixa para buscar metadata de bibliotecas. Esto pode ser útil cando <a %(a_request)s>solicite un arquivo</a>. Consello: emprega atallos de teclado “/” (foco na búsqueda), “enter” (buscar), “j” (arriba), “k” (abaixo), “<” (páxina anterior), “>” (seguinte páxina) para navegar máis rápido. Estes son rexistros de metadatos, <span %(classname)s>non</span> arquivos descargables. Axustes de búsqueda Buscar Préstamo dixital Descargar Artículos de revistas Metadata Nova búsqueda %(search_input)s - Búsqueda A búsqueda tardou demasiado, o que é habitual en consultas amplias. Ás veces <a %(a_reload)s>refrescar</a> a páxina axuda. A búsqueda tardou demasiado, o que é habitual en consultas amplias. É posible que o reconto de filtros non sexa exacto. Para grandes subidas de datos (máis de 10,000 arquivos) que non sexan aceptados por Libgen ou Z-Library, por favor contacta con nós en %(a_email)s. Para Libgen.li, asegúrese de iniciar sesión primeiro no <a %(a_forum)s>seu foro</a> co nome de usuario %(username)s e contrasinal %(password)s, e logo volva á súa <a %(a_upload_page)s>páxina de carga</a>. Polo momento suxerímoslle que cargue os novos libros nos forks de Library Genesis. Aquí tes unha <a %(a_guide)s>guía práctica</a>. Teña en conta que os dous forks que indexamos neste sitio web baséanse neste mesmo sistema de carga. Para cargas pequenas (ata 10.000 ficheiros) por favor súbaos tanto a %(first)s como a %(second)s. Alternativamente, podes subilos a Z-Library <a %(a_upload)s>aquí</a>. Para subir artigos académicos, por favor tamén (ademais de Library Genesis) suba a <a %(a_stc_nexus)s>STC Nexus</a>. Son a mellor biblioteca na sombra para novos artigos. Aínda non os integramos, pero farémolo nalgún momento. Pode usar o seu <a %(a_telegram)s>bot de subida en Telegram</a>, ou contactar coa dirección listada na súa mensaxe fixada se ten demasiados arquivos para subir deste xeito. <span %(label)s>Traballo voluntario intenso (recompensas de USD$50-USD$5,000):</span> se podes dedicar moito tempo e/ou recursos á nosa misión, encantaríanos traballar máis estreitamente contigo. Eventualmente podes unirte ao equipo interno. Aínda que temos un orzamento axustado, podemos outorgar <span %(bold)s>💰 recompensas monetarias</span> polo traballo máis intenso. <span %(label)s>Traballo lixeiro de voluntariado:</span> se só pode dedicar unhas poucas horas aquí e alí, aínda hai moitas formas en que pode axudar. Recompensamos aos voluntarios consistentes con <span %(bold)s>🤝 membresías de Anna’s Archive</span>. Anna’s Archive depende de voluntarios como vostede. Damos a benvida a todos os niveis de compromiso e temos dúas categorías principais de axuda que estamos buscando: Se non podes ofrecer o teu tempo como voluntario, aínda podes axudarnos moito <a %(a_donate)s>doando diñeiro</a>, <a %(a_torrents)s>semeando os nosos torrents</a>, <a %(a_uploading)s>subindo libros</a>, ou <a %(a_help)s>contándolle aos teus amigos sobre o Arquivo de Anna</a>. <span %(bold)s>Empresas:</span> ofrecemos acceso directo de alta velocidade ás nosas coleccións a cambio dunha doazón a nivel empresarial ou a cambio de novas coleccións (por exemplo, novas escaneos, datasets OCR, enriquecemento dos nosos datos). <a %(a_contact)s>Contacta connosco</a> se este é o teu caso. Vexa tamén a nosa <a %(a_llm)s>páxina de LLM</a>. Recompensas Sempre estamos buscando persoas con habilidades sólidas en programación ou seguridade ofensiva para involucrarse. Podes facer unha contribución seria na preservación do legado da humanidade. Como agradecemento, ofrecemos membresía por contribucións sólidas. Como un gran agradecemento, ofrecemos recompensas monetarias por tarefas particularmente importantes e difíciles. Isto non debe ser visto como un substituto dun traballo, pero é un incentivo extra e pode axudar cos custos incorridos. A maior parte do noso código é de código aberto, e pediremos o mesmo do teu código ao outorgar a recompensa. Hai algunhas excepcións que podemos discutir de forma individual. As recompensas outórganse á primeira persoa que complete unha tarefa. Sente libre de comentar nun ticket de recompensa para que outros saiban que estás traballando en algo, para que outros poidan esperar ou contactarte para formar equipo. Pero ten en conta que outros aínda son libres de traballar niso tamén e tentar superarte. Non obstante, non outorgamos recompensas por traballos descoidados. Se se fan dúas presentacións de alta calidade moi próximas entre si (dentro dun día ou dous), podemos optar por outorgar recompensas a ambas, á nosa discreción, por exemplo 100%% para a primeira presentación e 50%% para a segunda presentación (así que 150%% en total). Para as recompensas máis grandes (especialmente as recompensas de scraping), por favor contacta connosco cando completes ~5%% dela, e esteas seguro de que o teu método escalará ata o fito completo. Terás que compartir o teu método connosco para que poidamos dar feedback. Ademais, deste xeito podemos decidir que facer se hai varias persoas achegándose a unha recompensa, como potencialmente outorgala a varias persoas, animar a formar equipos, etc. AVISO: as tarefas de alta recompensa son <span %(bold)s>difíciles</span> — pode ser sabio comezar con tarefas máis fáciles. Vai á nosa <a %(a_gitlab)s>lista de problemas en Gitlab</a> e ordena por “Prioridade de etiqueta”. Isto mostra aproximadamente a orde das tarefas que nos importan. As tarefas sen recompensas explícitas aínda son elixibles para a membresía, especialmente aquelas marcadas como “Aceptadas” e “Favoritas de Anna”. Pode que queiras comezar cun “Proxecto inicial”. Voluntariado lixeiro Agora tamén temos un canal de Matrix sincronizado en %(matrix)s. Se tes unhas poucas horas libres, podes axudar de varias maneiras. Asegúrate de unirte ao <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Como mostra de agradecemento, normalmente damos 6 meses de “Bibliotecario Afortunado” por fitos básicos, e máis por traballo voluntario continuado. Todos os fitos requiren traballo de alta calidade — o traballo desleixado préxudicanos máis do que axuda e rexeitarémolo. Por favor <a %(a_contact)s>envíanos un correo electrónico</a> cando alcances un fito. Ligazóns ou capturas de pantalla das solicitudes que cumpriches %(links)s. Cumprir solicitudes de libros (ou artigos, etc) nos foros de Z-Library ou Library Genesis. Non temos o noso propio sistema de solicitudes de libros, pero espellamos esas bibliotecas, así que melloralas fai que o Arquivo de Anna tamén mellore. Fito Tarefa Depende da tarefa. Pequenas tarefas publicadas no noso <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Normalmente para membresía, ás veces para pequenas recompensas. Pequenas tarefas publicadas no noso grupo de chat de voluntarios. Asegúrate de deixar un comentario nos problemas que resolves, para que outros non dupliquen o teu traballo. Ligazóns de rexistros que melloraches %(links)s. Podes usar a <a %(a_list)s >lista de problemas de metadata aleatorios</a> como punto de partida. Mellorar os metadatos <a %(a_metadata)s>ligando</a> con Open Library. Estas deberían mostrarche informando a alguén sobre o Arquivo de Anna, e eles agradecéndoche. Ligazóns ou capturas de pantalla %(links)s. Espallando a palabra do Arquivo de Anna. Por exemplo, recomendando libros en AA, ligando ás nosas publicacións no blog ou, en xeral, dirixindo á xente ao noso sitio web. Traducir completamente un idioma (se non estaba xa case completado). <a %(a_translate)s>Traducir</a> o sitio web. Ligazón ao historial de edicións mostrando que fixeches contribucións significativas. Mellorar a páxina de Wikipedia do Arquivo de Anna no teu idioma. Inclúe información da páxina de Wikipedia de AA noutros idiomas, e do noso sitio web e blog. Engade referencias a AA noutras páxinas relevantes. Voluntariado e Recompensas 