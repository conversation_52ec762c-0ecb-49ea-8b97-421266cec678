msgid "layout.index.invalid_request"
msgstr "Petición Inválida. Visita %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Biblioteca de Préstamos de Anna's Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DiXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " e "

msgid "layout.index.header.tagline_and_more"
msgstr "e máis"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Reflexamos %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Facemos scrape e open-source %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "O noso código e información son de código aberto."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;A librería aberta máis grande do mundo."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;papers — preservados para sempre."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;A maior biblioteca de datos de código aberto do mundo ⭐️&nbsp;Espellos Sci-Hub, Library Genesis, Z-Library, e máis. 📈&nbsp;%(book_any)s libros, %(journal_article)s papers, %(book_comic)s comics, %(magazine)s revistas — preservadas para sempre."

msgid "layout.index.header.tagline_short"
msgstr "📚 A maior biblioteca de datos de código aberto do mundo.<br>⭐️ Espellos Scihub, Libgen, Zlib, e máis."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadatos incorrectos (por exemplo. título, descrición, imaxe de portada)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemas de descarga (Por exemplo. non pode conectarse, mensaxe de erro, moi lento)"

msgid "common.md5_report_type_mapping.broken"
msgstr "O arquivo non puido ser aberto (Por exemplo. arquivo corrupto, DRM [Xestión de Dereitos Dixitais])"

msgid "common.md5_report_type_mapping.pages"
msgstr "Mala calidade (Por exemplo. problemas co formato, mala calidade de escaneo, faltan páxinas)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / arquivo debe ser eliminado (Por exemplo. anuncios, contido abusivo)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamación por dereitos de autor"

msgid "common.md5_report_type_mapping.other"
msgstr "Outro"

msgid "common.membership.tier_name.bonus"
msgstr "Descargas bonus"

msgid "common.membership.tier_name.2"
msgstr "Brilliant Bookworm"

msgid "common.membership.tier_name.3"
msgstr "Bibliotecario afortunado (Lucky Librarian)"

msgid "common.membership.tier_name.4"
msgstr "Dazzling Datahoarder"

msgid "common.membership.tier_name.5"
msgstr "Asombroso Arquivista (Amazing Archivist)"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "sen pagar"

msgid "common.donation.order_processing_status_labels.1"
msgstr "pagado"

msgid "common.donation.order_processing_status_labels.2"
msgstr "cancelado"

msgid "common.donation.order_processing_status_labels.3"
msgstr "expirado"

msgid "common.donation.order_processing_status_labels.4"
msgstr "agardando á confirmación de Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "inválido"

msgid "page.donate.title"
msgstr "Donar"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Tes unha <a %(a_donation)s>donación existente</a> en progreso. Por favor termina ou cancela dita donación antes de realizar outra."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ver as miñas donacións</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archive (O Arquivo de Anna) é un proxecto sen ánimo de lucro, de código aberto e datos abertos. Ao donar e facerte membro, apoias as nosas operación e desenvolvemento. A todos os nosos membros: ¡grazas por manternos en marcha! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Para máis información, consulta as <a %(a_donate)s>Preguntas Frecuentes sobre doazóns</a>."

msgid "page.donate.refer.text1"
msgstr "Para conseguir aínda máis descargas, <a %(a_refer)s>recomenda aos teus amigos</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Obtés %(percentage)s%% descargas rápidas extra, porque fuches referido polo usuario %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Esto aplica a todo o período de afiliación."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s descargas rápidas por día"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se doas este mes!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mes"

msgid "page.donate.buttons.join"
msgstr "Únete"

msgid "page.donate.buttons.selected"
msgstr "Seleccionado"

msgid "page.donate.buttons.up_to_discounts"
msgstr "ata %(percentage)s%% de desconto"

msgid "page.donate.perks.scidb"
msgstr "SciDB papers <strong>ilimitado</strong> sen verificación"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Acceso á <a %(a_api)s>API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Gaña <strong>%(percentage)s%% descargas de bonificación</strong> ao <a %(a_refer)s>referir amigos</a>."

msgid "page.donate.perks.credits"
msgstr "O teu usuario ou mención anónima nos créditos"

msgid "page.donate.perks.previous_plus"
msgstr "Vantaxes anteriores, máis:"

msgid "page.donate.perks.early_access"
msgstr "Acceso anticipado a novas funcionalidades"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram exclusivo con actualizacións entre bastidores"

msgid "page.donate.perks.adopt"
msgstr "“Apadriña un torrent”: o teu nome de usuario ou mensaxe no nome dun arquivo torrent <div %(div_months)s>un por cada 12 meses de afiliación</div>"

msgid "page.donate.perks.legendary"
msgstr "Estatus lexendario na preservación do coñecemento e cultura da humanidade"

msgid "page.donate.expert.title"
msgstr "Acceso Experto"

msgid "page.donate.expert.contact_us"
msgstr "contácta con nós"

msgid "page.donate.small_team"
msgstr "Somos un pequeno equipo de voluntarios. Pode levarnos de 1 a 2 semanas responder."

msgid "page.donate.expert.unlimited_access"
msgstr "Acceso <strong>Ilimitado</strong> de alta velocidade"

msgid "page.donate.expert.direct_sftp"
msgstr "Servidores <strong>SFTP</strong> directos"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donación ou intercambio a nivel empresarial de novas coleccións (por exemplo, novos escaneos, conxuntos de datos OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Agradecemos as grandes doazóns de particulares ou institucións adiñeradas "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Para donacións superiores a 5000 $, póñase en contacto con nós directamente en %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Teña en conta que aínda que as subscricións nesta páxina son \"por mes\", son doazóns únicas (non recorrentes). Consulte as <a %(faq)s>Preguntas Frecuentes sobre doazóns</a>."

msgid "page.donate.without_membership"
msgstr "Se queres facer unha doazón (de calquera importe) sen membresía, podes facelo a través deste enderezo a Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Por favor selecciona un método de pago."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(non dispoñible temporalmente)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s tarxeta regalo"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Tarxeta bancaria (usando app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Cripto%(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Tarxeta de Crédito/débito"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (EEUU) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regular)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Tarxeta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Crédito/débito/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Tarxeta bancaria"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Tarxeta de crédito/débito (reserva)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Tarxeta de Crédito/Débito 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay / WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "Con cripto podes donar usando BTC, ETH, XMR e SOL. Utiliza esta opción se xa estás familiarizado coas criptomonedas."

msgid "page.donate.payment.desc.crypto2"
msgstr "Con cripto podes donar empregando BTC, ETH, CMR e máis."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se estás a usar criptomoedas por primeira vez, suxerímos usar %(options)s para mercar e doar Bitcoin (a criptomoeda orixinal e máis usada)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Para doar empregando PayPal EEUU, empregaremos PayPal Crypto, que nos permite manter o anonimato. Agradecémoslle que se tome a molestia de aprender a donar con este método, xa que nos axuda moito."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Doa empregando PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Doa empregando Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se vostede dispón de Cash App, este é o método máis sinxelo para doar!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Note que para doacións menores a %(amount)s, Cash App pode cobrarlle %(fee)s de comisión. Para doacións de %(amount)s ou máis, é gratuito!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Doa usando Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se tes Revolut, esta é a forma máis sinxela de doar!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Doe con tarxeta de crédito ou débito."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay tamén poden funcionar."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Note que para pequenas doacións, as comisións das tarxetas de crédito poden eliminar o%(discount)s%% de desconto, así que recomendamos doacións máis largas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Teña en conta que para doacións pequenas as cuotas son elevadas, polo que recomendamos suscricións máis largas."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, compras Bitcoin cunha tarxeta de crédito/débito ou conta bancaria, e logo doas ese Bitcoin a nós. Deste xeito, podemos manter a seguridade e o anonimato ao aceptar a túa doazón."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance está dispoñible en case todos os países e admite a maioría dos bancos e tarxetas de crédito/débito. Actualmente, esta é a nosa principal recomendación. Agradecemos que tomes o tempo para aprender a doar usando este método, xa que nos axuda moito."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Doa usando a túa conta regular de PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Doa usando tarxeta de crédito/débito, PayPal ou Venmo. Podes escoller entre estas na seguinte páxina."

msgid "page.donate.payment.desc.amazon"
msgstr "Doe empregando una tarxeta regalo de Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Teña en conta que debemos redondear aos importes aceptados polos nosos revendedoresre (mínimo %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Só soportamos Amazon.com, non outros sitios web de Amazon. Por exemplo, .de, .co.uk, .ca, NON son compatibles."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Esta opción é para %(amazon)s. Se desexa usar outro sitio web de Amazon, selecciónao arriba."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Este método usa un provedor de criptomoedas como conversión intermedia. Isto pode ser un pouco confuso, así que usa este método só se os outros métodos de pago non funcionan. Ademais, non funciona en todos os países."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Doe usando unha tarxeta de crédito/débito, a través da app de Alipay (moi fácil de configurar)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instale a app de Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instale a app de Alipay desde a <a %(a_app_store)s>Apple App Store</a> ou <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Rexístrese usando o seu número de teléfono."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Non se requiren máis detalles persoais."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Engadir tarxeta bancaria"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Compatibles: Visa, MasterCard, JCB, Diners Club e Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Consulte <a %(a_alipay)s>esta guía</a> para máis información."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Non podemos aceptar tarxetas de crédito/débito directamente, porque os bancos non queren traballar connosco. ☹ Con todo, hai varias formas de usar tarxetas de crédito/débito de todos os xeitos, usando outros métodos de pago:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Tarxeta de regalo de Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Envíanos tarxetas regalo de Amazon.com empregando a túa tarxeta de crédito ou débito."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay admite tarxetas de crédito/débito internacionais. Vexa <a %(a_alipay)s>esta guía</a> para máis información."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) admite tarxetas de crédito/débito internacionais. Na app de WeChat, vai a“Me => Services => Wallet => Add a Card”. Se non atopas o menú, actívao en “Me => Settings => General => Tools => Weixin Pay => Enable”."

msgid "page.donate.ccexp.crypto"
msgstr "Podes comprar criptomoedas empregando tarxetas de crédito ou débito."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servizos exprés de cripto"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Os servizos exprés son convenientes, pero cobran taxas máis altas."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Pode usar isto en lugar dun intercambio de cripto se está buscando facer unha doazón maior rapidamente e non lle importa unha taxa de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Asegúrese de enviar a cantidade exacta de cripto mostrada na páxina de doazóns, non a cantidade en $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Se non, a taxa será restada e non poderemos procesar automaticamente a súa subscrición."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(mínimo: %(minimum)s dependendo do país, sen verificación para a primeira transacción)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(mínimo: %(minimum)s, sen verificación para a primeira transacción)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(mínimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se algunha desta información está desactualizada, envíenos un correo electrónico para informarnos."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Para tarxetas de crédito, tarxetas de débito, Apple Pay e Google Pay, usamos “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). No seu sistema, un “café” é igual a $5, polo que a túa doazón será redondeada ao múltiplo máis próximo de 5."

msgid "page.donate.duration.intro"
msgstr "Selecciona por canto tempo queres suscribirte."

msgid "page.donate.duration.1_mo"
msgstr "1 mes"

msgid "page.donate.duration.3_mo"
msgstr "3 meses"

msgid "page.donate.duration.6_mo"
msgstr "6 meses"

msgid "page.donate.duration.12_mo"
msgstr "12 meses"

msgid "page.donate.duration.24_mo"
msgstr "24 meses"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 meses"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 meses"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>despois <span %(span_discount)s></span> descontos</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Este pagamento require un mínimo de %(amount)s. Por favor selecciona unha duración ou método de pago diferente."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Doar"

msgid "page.donate.payment.maximum_method"
msgstr "Este método de pago únicamente permite un máximo de %(amount)s. Por favor, seleccione una duración ou método de pago diferente."

msgid "page.donate.login2"
msgstr "Para facerse socio, <a %(a_login)s>Iniciar sesión ou Rexistrarse</a>. Grazas polo seu apoio!"

msgid "page.donate.payment.crypto_select"
msgstr "Selecciona a súa criptomoneda de preferencia:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(importe mínimo)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(use cando envíe Ethereum desde Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(Advertencia: importe mínimo elevado)"

msgid "page.donate.submit.confirm"
msgstr "Clica no botón de doación para confirmar esta doación."

msgid "page.donate.submit.button"
msgstr "Doa <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Aínda pode cancelar a doación durante o proceso de pagamento."

msgid "page.donate.submit.success"
msgstr "✅ Redireccionando á páxina de doación…"

msgid "page.donate.submit.failure"
msgstr "❌ Algo saíu mal. Por favor, recarga a páxina e volve a tentar."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mes"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "durante 1 mes"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "durante 3 meses"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "durante 6 meses"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "durante 12 meses"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "durante 24 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "durante 48 meses"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "durante 96 meses"

msgid "page.donate.submit.button.label.1_mo"
msgstr "durante 1 mes \"%(tier_name)s\""

msgid "page.donate.submit.button.label.3_mo"
msgstr "3 meses \"%(tier_name)s\""

msgid "page.donate.submit.button.label.6_mo"
msgstr "6 meses \"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "12 meses \"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "24 meses \"%(tier_name)s\""

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "durante 48 meses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "durante 96 meses “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Doazón"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes durante %(duration)s meses, incluíndo %(discounts)s%% de desconto)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes durante %(duration)s meses)</span>"

msgid "page.donation.header.status"
msgstr "Estado: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificador: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Cancelar"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Estás seguro de que queres cancelar ? Non canceles se xa pagaches."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Sí, cancela"

msgid "page.donation.header.cancel.success"
msgstr "✅ A túa doazón foi cancelada."

msgid "page.donation.header.cancel.new_donation"
msgstr "Fazer unha nova doazón"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Algo saíu mal. Por favor, recarga a páxina e inténtao de novo."

msgid "page.donation.header.reorder"
msgstr "Volver a comprar"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Xa pagaches. Se queres revisar as instrucción de pagamento de igual maneira, clica aquí:"

msgid "page.donation.old_instructions.show_button"
msgstr "Mostrar instruccións de pago antigas"

msgid "page.donation.thank_you_donation"
msgstr "Grazas pola túa doazón!"

msgid "page.donation.thank_you.secret_key"
msgstr "Se aínda non o fixeches, anota a túa clave secreta para iniciar sesión:"

msgid "page.donation.thank_you.locked_out"
msgstr "Do contrario, ¡pode que te bloqueen a conta!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "As instruccións de pagamento non están actualizadas. Se queres facer outra doazón, emprega o botón \"Volver a comprar\" que está arriba."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> Os prezos das criptomoedas poden fluctuar moito, ás veces tanto como un 20%% en poucos minutos. Isto segue a ser menos que as comisións nas que incurrimos con moitos proveedores de pagos, que moitas veces cobran entre un 50-60%% por traballar con unha \"organización benéfica na sombra\". <u>Se nos envía o recibo co prezo orixinal que se pagou, abonaremos na súa conta o importe da afiliación elexida</u> (sempre e cando o recibo non teña máis dunhas horas de antiguedade). Agradecemos moito que estés disposto a pasar por este tedioso proceso para apoiarnos ! ❤️"

msgid "page.donation.expired"
msgstr "Esta doazón expirou. Por favor cancela e crea unha nova."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instruccións para criptomoedas"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfire a unha das túas contas de criptomoedas"

msgid "page.donation.payment.crypto.text1"
msgstr "Doa a cantidade total de %(total)s a algunha das seguintes direccións:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compra Bitcoin en Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Atopa a páxina “Crypto” na aplicación ou sitio web de PayPal. Normalmente atópase en \"Finances\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Siga as instruccións para comprar Bitcoin (BTC). Únicamente debe comprar a cantidade que quera doar, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transife o Bitcoin a nosa dirección"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vai a páxina \"Bitcoin\" na súa aplicación ou sitio web de PayPal. Peme o botón \"Transferir\" %(transfer_icon)s e, a continuación, \"Enviar\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Introduza a nosa dirección de Bitcoin (BTC) como destinatario e siga as instruccións para enviar a súa doazón de %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instruccións para tarxetas de Crédito / Débito"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Doar a través da nosa páxina de tarxeta de Crédito / Débito"

msgid "page.donation.donate_on_this_page"
msgstr "Doa %(amount)s en <a %(a_page)s>esta páxina</a>."

msgid "page.donation.stepbystep_below"
msgstr "Consulta a guía paso a paso que figura a continuación."

msgid "page.donation.status_header"
msgstr "Estado:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Agardando confirmación (actualice a páxina para comprobalo)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Agardando transferencia (actualice a páxina para comprobalo)…"

msgid "page.donation.time_left_header"
msgstr "Tempo restante:"

msgid "page.donation.might_want_to_cancel"
msgstr "(pode ser que desexe cancelar e crear unha nova doazón)"

msgid "page.donation.reset_timer"
msgstr "Para reiniciar o temporizador, é suficiente con crear una nova doazón."

msgid "page.donation.refresh_status"
msgstr "Actualizar estado"

msgid "page.donation.footer.issues_contact"
msgstr "Se tes algún probema, ponte en contacto con nós en %(email)s e inclúe toda a información posible (como capturas de pantalla)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Se xa pagou:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Ás veces a confirmación pode tardar ata 24 horas, así que asegúrese de actualizar esta páxina (aínda que xa caducou)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Comprar moeda PYUSD en PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Segue as instruccións para comprar a moeda PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Compra un pouco máis (recomendamos %(more)s máis) que a cantidade que vas doar (%(amount)s), para cubrir os gastos de transacción. Quedarás co que sobre."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Vai á páxina \"PYUSD\" na súa aplicación ou sitio web de PayPal. Preme o botón \"Transfer\" %(icon)s, e despois, \"Enviar\"."

msgid "page.donation.transfer_amount_to"
msgstr "Transfire %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Mercar Bitcoin (BTC) en Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vai á páxina de “Bitcoin” (BTC) en Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Mercar un pouco máis (recomendamos %(more)s máis) que a cantidade que estás a doar (%(amount)s), para cubrir as taxas de transacción. Quedarás co que sobre."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfire o Bitcoin ao noso enderezo"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Preme o botón de “Enviar bitcoin” para facer unha “retirada”. Cambia de dólares a BTC premendo o icono %(icon)s. Introduce a cantidade de BTC a continuación e preme “Enviar”. Vexa <a %(help_video)s>este vídeo</a> se te quedas atascado."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Para doazóns pequenas (menos de $25), pode que necesite usar Rush ou Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Mercar Bitcoin (BTC) en Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Vaia á páxina de “Crypto” en Revolut para mercar Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Compre un pouco máis (recomendamos %(more)s máis) do importe que está a doar (%(amount)s), para cubrir as taxas de transacción. Gardará o que sobre."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfira o Bitcoin ao noso enderezo"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Faga clic no botón “Enviar bitcoin” para facer unha “retirada”. Cambie de euros a BTC premendo na icona %(icon)s. Introduza o importe en BTC a continuación e faga clic en “Enviar”. Vexa <a %(help_video)s>este vídeo</a> se se atopa con dificultades."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Asegúrate de usar a cantidade de BTC a continuación, <em>NON</em> euros ou dólares, doutro xeito non recibiremos a cantidade correcta e non poderemos confirmar automaticamente a túa subscrición."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Para doazóns pequenas (menos de $25) pode que necesite usar Rush ou Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Use calquera dos seguintes servizos exprés de “tarxeta de crédito a Bitcoin”, que só levan uns minutos:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Encha os seguintes detalles no formulario:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Importe de BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Utilice este <span %(underline)s>importe exacto</span>. O seu custo total pode ser maior debido ás taxas da tarxeta de crédito. Para importes pequenos, isto pode ser máis que o noso desconto, desafortunadamente."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Enderezo de BTC / Bitcoin (carteira externa):"

msgid "page.donation.crypto_instructions"
msgstr "instruccións para %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Só admitimos a versión estándar de criptomoedas, nada de redes ou versións exóticas de moedas. A confirmación da transacción pode tardar ata unha hora, dependendo da moeda."

msgid "page.donation.crypto_qr_code_title"
msgstr "Escanear o código QR para pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Escanea este código QR coa túa aplicación de carteira cripto para encher rapidamente os detalles do pago"

msgid "page.donation.amazon.header"
msgstr "Tarxeta regalo de Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Por favor, emprega o <a %(a_form)s> formulario oficial de Amazon.com</a> para enviarnos unha tarxeta regalo de %(amount)s á dirección de correo electrónico que aparece a continuación."

msgid "page.donation.amazon.only_official"
msgstr "Non podemos aceptar outros métodos de tarxetas regalo, <strong>só as enviadas directamente dende o formulario oficial de Amazon.com</strong>. Non podemos devolver a túa tarxeta regalo se non empregas este formulario."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Introduza o importe exacto: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Por favor, NON escriba a súa propia mensaxe."

msgid "page.donation.amazon.form_to"
msgstr "\"Para\" correo electrónico do destinatario no formulario:"

msgid "page.donation.amazon.unique"
msgstr "Único para a túa conta, non o compartas."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usar só unha vez."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Agardando tarxeta regalo... (actualiza a páxina para comprobalo)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Despois de enviar a túa tarxeta regalo, o noso sistema automatizado confirmará nuns minutos. Se isto non funciona, tenta reenviar a túa tarxeta regalo (<a %(a_instr)s>instructions</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Se segue sen funcionar, envíanos un correo electrónico e Anna o revisará manualmente (isto pode levar uns días), e asegúrate de mencionar se xa intentaches reenvialo."

msgid "page.donation.amazon.example"
msgstr "Exemplo:"

msgid "page.donate.strange_account"
msgstr "Ten en conta que o nome da conta ou a imaxe poden parecer extranos. Non se preocupe ! Éstas contas están xestionadas polos nosos socios doantes. As nosas contas non foron pirateadas."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instruccións de Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Doar en Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Doa o importe total de %(total)s usando <a %(a_account)s>esta conta de Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Se a páxina de doazóns está bloqueada, proba unha conexión a internet diferente (por exemplo, VPN ou internet do teléfono)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Desafortunadamente, a páxina de Alipay adoita ser accesible só desde <strong>China continental</strong>. Pode que teña que desactivar temporalmente o seu VPN, ou usar un VPN para China continental (ou Hong Kong tamén funciona ás veces)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Faga unha doazón (escanear o código QR ou prema o botón)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Abra a <a %(a_href)s>páxina de doazóns con código QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Escanee o código QR coa aplicación Alipay, ou prema o botón para abrir a aplicación Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Por favor, sexa paciente; a páxina pode tardar un pouco en cargar xa que está en China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instrucións de WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Doar en WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Doa o importe total de %(total)s usando <a %(a_account)s>esta conta de WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instruccións para Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Doar en Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Doar o importe total de %(total)s empregando <a %(a_account)s>esta conta de Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Envíanos o recibo por email"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Envía un recibo ou captura de pantalla ao seu enderezo de verificación persoal. NON use este enderezo de correo electrónico para a súa doazón por PayPal."

msgid "page.donation.footer.text1"
msgstr "Evía un recibo ou unha captura de pantalla a súa dirección de verificación persoal:"

msgid "page.donation.footer.crypto_note"
msgstr "Se o tipo de cambio de criptomoedas fluctuou durante a transacción, asegúrese de incluir o recibo que mostre o tipo de cambio orixinal. Agradecémoslle que se tome o tempo de empregar criptomoedas, ¡ Axúdanos moito !"

msgid "page.donation.footer.text2"
msgstr "Cando xa teñas enviado o recibo por correo electrónico, fai click neste botón para que Anna poida revisalo manualmente (esto pode levar uns días):"

msgid "page.donation.footer.button"
msgstr "Si, enviei o meu recibo por coreo electrónico"

msgid "page.donation.footer.success"
msgstr "✅ ¡Grazas pola túa doazón! Anna activará manualmente a túa afiliación nuns días."

msgid "page.donation.footer.failure"
msgstr "❌ Algo fallou. Volva a cargar a páxina e inténteo de novo."

msgid "page.donation.stepbystep"
msgstr "Guía paso a paso"

msgid "page.donation.crypto_dont_worry"
msgstr "Algúns dos pasos mencionan carteiras criptográficas, pero non te preocupes, non tes que aprender nada sobre criptomoedas para isto."

msgid "page.donation.hoodpay.step1"
msgstr "1. Introduce o teu email."

msgid "page.donation.hoodpay.step2"
msgstr "2. Selecciona o teu método de pago."

msgid "page.donation.hoodpay.step3"
msgstr "3. Selecciona de novo o teu método de pago."

msgid "page.donation.hoodpay.step4"
msgstr "4. Selecciona carteira “Self-hosted”."

msgid "page.donation.hoodpay.step5"
msgstr "5. Clica “confirmo a propiedade”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Deberias recibir un recibo no email. Por favor envíanolo e confirmaremos a túa doazón o antes posible."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Espera polo menos <span %(span_hours)s>24 horas</span> (e actualiza esta páxina) antes de contactarnos."

msgid "page.donate.mistake"
msgstr "Se cometiches un erro durante o pagamento, non podemos facer devolucións, pero tentaremos arreglalo."

msgid "page.my_donations.title"
msgstr "As miñas doazóns"

msgid "page.my_donations.not_shown"
msgstr "Os detalles das doazóns non se mostran públicamente."

msgid "page.my_donations.no_donations"
msgstr "Non hai doazóns todavía. <a %(a_donate)s>Facer a miña primeira doazón.</a>"

msgid "page.my_donations.make_another"
msgstr "Facer outra doazón."

msgid "page.downloaded.title"
msgstr "Arquivos descargados"

msgid "page.downloaded.fast_partner_star"
msgstr "As descargas desde servidores Fast Partner están marcadas con %(icon)s."

msgid "page.downloaded.twice"
msgstr "Se descargaches un arquivo con descargas rápidas e lentas, mostrarase dúas veces."

msgid "page.downloaded.fast_download_time"
msgstr "As descargas rápidas nas derradeiras 24 horas contan para o límite diario."

msgid "page.downloaded.times_utc"
msgstr "Todas as horas están en UTC."

msgid "page.downloaded.not_public"
msgstr "Os arquivos descargados non se mostran públicamente."

msgid "page.downloaded.no_files"
msgstr "Aínda non se descargou ningún arquivo."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Últimas 18 horas"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Anteriormente"

msgid "page.account.logged_in.title"
msgstr "Conta"

msgid "page.account.logged_out.title"
msgstr "Iniciar sesión / Rexistrarse"

msgid "page.account.logged_in.account_id"
msgstr "ID da conta: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Perfil público: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chave secreta (non a compartas): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "mostrar"

msgid "page.account.logged_in.membership_has_some"
msgstr "Suscripción: <strong>%(tier_name)s</strong> ata %(until_date)s <a %(a_extend)s>(extender)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Suscripción: <strong>Ningunha</strong> <a %(a_become)s>(facerse membro)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Descargas rápidas empregadas (últimas 24 horas): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "qué descargas ?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupo exclusivo de Telegram: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Únete a nós !"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Actualizar a unha <a %(a_tier)s>suscripción superior</a> para unirte ao noso grupo."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Contacta con Anna en %(email)s se estás interesado en actualizar a túa suscripción a unha superior."

msgid "page.contact.title"
msgstr "Email de contacto"

msgid "page.account.logged_in.membership_multiple"
msgstr "Podes combinar varias suscripcións (as descargas rápidas cada 24 horas sumaranse)."

msgid "layout.index.header.nav.public_profile"
msgstr "Perfil público"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Arquivos descargados"

msgid "layout.index.header.nav.my_donations"
msgstr "As miñas doazóns"

msgid "page.account.logged_in.logout.button"
msgstr "Pechar sesión"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Pechaches a túa sesión. Refresca a páxina para volver a iniciar sesión."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Algo fallou. Por favor, refresca a páxina e volve a intentalo."

msgid "page.account.logged_out.registered.text1"
msgstr "Rexistro correcto ! A túa chave secreta é: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Gárda esta chave con coidado. Se a perdes, perderás con ela o acceso a túa conta."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Bookmark.</strong>Podes gardar esta páxina para recuperar a túa chave.</li><li %(li_item)s><strong>Descargar.</strong> Clica <a %(a_download)s>neste link</a> para descargar a túa chave.</li><li %(li_item)s><strong>Administrador de contrasinais.</strong> Emprega un administrador de contrasinais para gardar a chave cando a introduzas abaixo.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Introduce a túa chave secreta para iniciar sesión:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chave secreta"

msgid "page.account.logged_out.key_form.button"
msgstr "Iniciar sesión"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Chave secreta inválida. Comproba a túa chave e volve a intentalo, ou rexistra unha nova conta abaixo."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Non perdas a túa chave !"

msgid "page.account.logged_out.register.header"
msgstr "Non tes unha conta todavía ?"

msgid "page.account.logged_out.register.button"
msgstr "Rexistrar unha nova conta"

msgid "page.login.lost_key"
msgstr "Se perdiches a túa chave, por favor <a %(a_contact)s>contáctanos</a> e proporciona tanta información como sexa posible."

msgid "page.login.lost_key_contact"
msgstr "É posible que teñas que crear temporalmente unha nova conta para contactar con nós."

msgid "page.account.logged_out.old_email.button"
msgstr "Conta con unha dirección de correo vella ? Introduce a túa dirección de correo <a %(a_open)s>aquí</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "editar"

msgid "page.list.edit.button"
msgstr "Gardar"

msgid "page.list.edit.success"
msgstr "✅ Gardado. Por favor, refresca a páxina."

msgid "page.list.edit.failure"
msgstr "❌ Algo fallou. Por favor, volve a intentalo."

msgid "page.list.by_and_date"
msgstr "Ordear por %(by)s, creación <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "A lista está valeira."

msgid "page.list.new_item"
msgstr "Engade ou elimina arquivos desta lista buscando un arquivo e abrindo a lapela \"Lista\"."

msgid "page.profile.title"
msgstr "Pefil"

msgid "page.profile.not_found"
msgstr "Perfil non atopado."

msgid "page.profile.header.edit"
msgstr "editar"

msgid "page.profile.change_display_name.text"
msgstr "Cambia o teu nome público. O teu identificador (a parte despois do \"#\") non pode ser cambiada."

msgid "page.profile.change_display_name.button"
msgstr "Gardar"

msgid "page.profile.change_display_name.success"
msgstr "✅ Gardado. Por favor, refresca a páxina."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo fallou. Por favor volve a intentalo."

msgid "page.profile.created_time"
msgstr "Perfil creado o <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listas"

msgid "page.profile.lists.no_lists"
msgstr "Aínda non hai listas"

msgid "page.profile.lists.new_list"
msgstr "Crea unha nova lista buscando un arquivo e abrindo a lapela \"Listas\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "A reforma dos dereitos de autor é necesaria para a seguridade nacional"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Os LLM chineses (incluíndo DeepSeek) están adestrados no meu arquivo ilegal de libros e artigos — o maior do mundo. Occidente necesita reformar a lei de dereitos de autor como unha cuestión de seguridade nacional."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artigos complementarios de TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Non hai moito tempo, as “bibliotecas na sombra” estaban morrendo. Sci-Hub, o enorme arquivo ilegal de artigos académicos, deixara de aceptar novas obras debido a demandas xudiciais. “Z-Library”, a maior biblioteca ilegal de libros, viu como os seus presuntos creadores foron arrestados por cargos criminais de dereitos de autor. Incriblemente lograron escapar do seu arresto, pero a súa biblioteca non está menos ameazada."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Cando Z-Library enfrontou o peche, eu xa fixera unha copia de seguridade de toda a súa biblioteca e estaba buscando unha plataforma para aloxala. Esa foi a miña motivación para comezar o Arquivo de Anna: unha continuación da misión detrás desas iniciativas anteriores. Desde entón, medramos ata ser a maior biblioteca na sombra do mundo, aloxando máis de 140 millóns de textos con dereitos de autor en numerosos formatos — libros, artigos académicos, revistas, xornais e máis aló."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "O meu equipo e eu somos ideólogos. Cremos que preservar e aloxar estes arquivos é moralmente correcto. As bibliotecas de todo o mundo están vendo recortes de financiamento, e tampouco podemos confiar o patrimonio da humanidade ás corporacións."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Logo chegou a IA. Prácticamente todas as grandes empresas que constrúen LLMs contactáronnos para adestrar cos nosos datos. A maioría (pero non todas!) das empresas con sede nos EUA reconsideraron unha vez que se deron conta da natureza ilegal do noso traballo. Pola contra, as empresas chinesas abrazaron entusiasticamente a nosa colección, aparentemente sen preocuparse pola súa legalidade. Isto é notable dado o papel de China como signatario de case todos os principais tratados internacionais de dereitos de autor."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Demos acceso de alta velocidade a unhas 30 empresas. A maioría delas son empresas de LLM, e algunhas son intermediarios de datos, que revenderán a nosa colección. A maioría son chinesas, aínda que tamén traballamos con empresas dos EUA, Europa, Rusia, Corea do Sur e Xapón. DeepSeek <a %(arxiv)s>admitiu</a> que unha versión anterior foi adestrada en parte da nosa colección, aínda que son reservados sobre o seu último modelo (probablemente tamén adestrado cos nosos datos)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se Occidente quere manterse á fronte na carreira dos LLMs, e finalmente, da AGI, necesita reconsiderar a súa posición sobre os dereitos de autor, e pronto. Estea de acordo connosco ou non sobre o noso caso moral, isto está a converterse nun caso de economía, e incluso de seguridade nacional. Todos os bloques de poder están a construír super-científicos artificiais, super-hackers e super-militares. A liberdade de información está a converterse nunha cuestión de supervivencia para estes países — incluso unha cuestión de seguridade nacional."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "O noso equipo é de todo o mundo, e non temos unha alineación particular. Pero animaríamos aos países con leis de dereitos de autor fortes a usar esta ameaza existencial para reformalas. Entón, que facer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "A nosa primeira recomendación é sinxela: acurtar o prazo dos dereitos de autor. Nos EUA, os dereitos de autor concédense por 70 anos despois da morte do autor. Isto é absurdo. Podemos aliñalo cos patentes, que se conceden por 20 anos despois da presentación. Isto debería ser máis que suficiente tempo para que os autores de libros, artigos, música, arte e outras obras creativas, sexan completamente compensados polos seus esforzos (incluíndo proxectos a longo prazo como adaptacións cinematográficas)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Logo, como mínimo, os responsables políticos deberían incluír excepcións para a preservación masiva e a difusión de textos. Se a perda de ingresos de clientes individuais é a principal preocupación, a distribución a nivel persoal podería seguir prohibida. En cambio, aqueles capaces de xestionar vastos repositorios — empresas que adestran LLMs, xunto con bibliotecas e outros arquivos — estarían cubertos por estas excepcións."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Algúns países xa están a facer unha versión disto. TorrentFreak <a %(torrentfreak)s>informou</a> que China e Xapón introducíron excepcións de IA nas súas leis de dereitos de autor. Non está claro para nós como isto interactúa cos tratados internacionais, pero certamente dá cobertura ás súas empresas domésticas, o que explica o que estivemos vendo."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "En canto ao Arquivo de Anna — continuaremos o noso traballo subterráneo arraigado na convicción moral. Con todo, o noso maior desexo é saír á luz, e amplificar o noso impacto legalmente. Por favor, reformen os dereitos de autor."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Le os artigos complementarios de TorrentFreak: <a %(torrentfreak)s>primeiro</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Gañadores da recompensa de visualización de ISBN de $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Recibimos algunhas presentacións incribles para a recompensa de visualización de ISBN de $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Hai uns meses anunciamos unha <a %(all_isbns)s>recompensa de $10,000</a> para facer a mellor visualización posible dos nosos datos mostrando o espazo ISBN. Enfatizamos mostrar que arquivos temos/non temos arquivados xa, e máis tarde un conxunto de datos que describe cantas bibliotecas teñen ISBNs (unha medida de rareza)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Estamos abrumados pola resposta. Houbo tanta creatividade. Un gran agradecemento a todos os que participaron: a vosa enerxía e entusiasmo son contaxiosos!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "En última instancia, queriamos responder ás seguintes preguntas: <strong>que libros existen no mundo, cantos arquivamos xa, e en cales deberiamos centrarnos a continuación?</strong> É estupendo ver que tanta xente se preocupa por estas preguntas."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Comezamos cunha visualización básica nós mesmos. En menos de 300kb, esta imaxe representa sucintamente a maior “lista de libros” completamente aberta xamais ensamblada na historia da humanidade:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Todos os ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Arquivos no Arquivo de Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuga de datos de CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Rexistro Global de Editores ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Estatal Rusa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Volver"

#, fuzzy
msgid "common.forward"
msgstr "Adiante"

#, fuzzy
msgid "common.last"
msgstr "Último"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Consulte a <a %(all_isbns)s>entrada orixinal do blog</a> para obter máis información."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Emitimos un desafío para mellorar isto. Outorgaríamos un premio de primeiro lugar de $6,000, segundo lugar de $3,000 e terceiro lugar de $1,000. Debido á resposta esmagadora e ás incríbeis presentacións, decidimos aumentar lixeiramente o fondo de premios e outorgar un terceiro lugar compartido de $500 cada un. Os gañadores están a continuación, pero asegúrese de ver todas as presentacións <a %(annas_archive)s>aquí</a>, ou descargar o noso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primeiro lugar $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Esta <a %(phiresky_github)s>presentación</a> (<a %(annas_archive_note_2951)s>comentario en Gitlab</a>) é simplemente todo o que queriamos, e máis! Gustáronnos especialmente as opcións de visualización incríbelmente flexibles (incluso admitindo sombreadores personalizados), pero cunha lista completa de predefinidos. Tamén nos gustou o rápido e fluído que é todo, a implementación sinxela (que nin sequera ten un backend), o minimapa intelixente e a extensa explicación na súa <a %(phiresky_github)s>entrada de blog</a>. Un traballo incríbel, e un gañador ben merecido!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Segundo lugar $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Outra <a %(annas_archive_note_2913)s>presentación</a> incríbel. Non tan flexible como o primeiro lugar, pero de feito preferimos a súa visualización a nivel macro sobre o primeiro lugar (curva de recheo de espazo, bordos, etiquetado, resaltado, desprazamento e zoom). Un <a %(annas_archive_note_2971)s>comentario</a> de Joe Davis resoou connosco:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Aínda que os cadrados e rectángulos perfectos son matematicamente agradables, non proporcionan unha superior localidade nun contexto de mapeo. Creo que a asimetría inherente a estes Hilbert ou Morton clásico non é un defecto senón unha característica. Do mesmo xeito que o famoso contorno en forma de bota de Italia fai que sexa instantaneamente recoñecible nun mapa, as \"peculiaridades\" únicas destas curvas poden servir como fitos cognitivos. Esta distintividade pode mellorar a memoria espacial e axudar aos usuarios a orientarse, potencialmente facilitando a localización de rexións específicas ou a detección de patróns.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E aínda moitas opcións para visualizar e renderizar, así como unha interface de usuario incríbelmente fluída e intuitiva. Un sólido segundo lugar!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terceiro lugar $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Nesta <a %(annas_archive_note_2940)s>presentación</a> realmente nos gustaron os diferentes tipos de vistas, en particular as vistas de comparación e editor."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terceiro lugar $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Aínda que non é a interface de usuario máis pulida, esta <a %(annas_archive_note_2917)s>presentación</a> cumpre moitos dos requisitos. Gustounos especialmente a súa función de comparación."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terceiro lugar $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Como o primeiro lugar, esta <a %(annas_archive_note_2975)s>presentación</a> impresionounos coa súa flexibilidade. En última instancia, isto é o que fai que unha ferramenta de visualización sexa excelente: máxima flexibilidade para usuarios avanzados, mentres se manteñen as cousas sinxelas para os usuarios medios."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terceiro lugar $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "A última <a %(annas_archive_note_2947)s>presentación</a> en recibir un premio é bastante básica, pero ten algunhas características únicas que realmente nos gustaron. Gustounos como mostran cantos datasets cobren un ISBN particular como medida de popularidade/confiabilidade. Tamén nos gustou moito a simplicidade pero efectividade de usar un control deslizante de opacidade para comparacións."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ideas notables"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Algunhas ideas e implementacións máis que nos gustaron especialmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Rascacielos para a rareza"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Estatísticas en directo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotacións, e tamén estatísticas en directo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista de mapa única e filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Esquema de cores predeterminado e mapa de calor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Cambio sinxelo de datasets para comparacións rápidas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etiquetas bonitas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de escala co número de libros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Moitos deslizadores para comparar datasets, coma se foses un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Poderiamos seguir durante un tempo, pero imos parar aquí. Asegúrate de ver todas as presentacións <a %(annas_archive)s>aquí</a>, ou descarga o noso <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Moitas presentacións, e cada unha achega unha perspectiva única, xa sexa na UI ou na implementación."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Polo menos incorporaremos a presentación do primeiro lugar no noso sitio web principal, e quizais algunhas outras. Tamén comezamos a pensar en como organizar o proceso de identificar, confirmar e logo arquivar os libros máis raros. Máis novidades neste aspecto."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Grazas a todos os que participaron. É incrible que tanta xente se preocupe."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Os nosos corazóns están cheos de gratitude."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizando Todos os ISBNs — recompensa de $10,000 para o 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Esta imaxe representa a maior “lista de libros” totalmente aberta xamais ensamblada na historia da humanidade."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Esta imaxe ten 1000×800 píxeles. Cada píxel representa 2,500 ISBNs. Se temos un ficheiro para un ISBN, facemos ese píxel máis verde. Se sabemos que se emitiu un ISBN, pero non temos un ficheiro correspondente, facémolo máis vermello."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En menos de 300kb, esta imaxe representa sucintamente a maior “lista de libros” totalmente aberta xamais ensamblada na historia da humanidade (uns centos de GB comprimidos en total)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Tamén mostra: queda moito traballo por facer para respaldar libros (só temos 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Antecedentes"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Como pode o Arquivo de Anna cumprir a súa misión de facer unha copia de seguridade de todo o coñecemento da humanidade, sen saber que libros aínda están por aí? Necesitamos unha lista de tarefas. Unha forma de mapear isto é a través dos números ISBN, que desde os anos 70 foron asignados a cada libro publicado (na maioría dos países)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Non hai unha autoridade central que coñeza todas as asignacións de ISBN. En cambio, é un sistema distribuído, onde os países obteñen rangos de números, que logo asignan rangos máis pequenos aos principais editores, que poden subdividir aínda máis os rangos a editores menores. Finalmente, os números individuais son asignados aos libros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Comezamos a mapear os ISBNs <a %(blog)s>hai dous anos</a> co noso raspado de ISBNdb. Desde entón, raspamos moitas máis fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e máis. Unha lista completa pódese atopar nas páxinas de “Datasets” e “Torrents” no Arquivo de Anna. Agora temos, con moito, a maior colección totalmente aberta e facilmente descargable de metadata de libros (e polo tanto ISBNs) no mundo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Escribimos <a %(blog)s>amplamente</a> sobre por que nos importa a preservación, e por que estamos actualmente nunha xanela crítica. Debemos agora identificar libros raros, pouco enfocados e unicamente en risco e preservalos. Ter boa metadata de todos os libros do mundo axuda con iso."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualización"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Ademais da imaxe xeral, tamén podemos ver os datasets individuais que adquirimos. Use o despregable e os botóns para cambiar entre eles."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Hai moitos patróns interesantes para ver nestas imaxes. Por que hai certa regularidade de liñas e bloques, que parece ocorrer a diferentes escalas? Que son as áreas baleiras? Por que certos datasets están tan agrupados? Deixaremos estas preguntas como un exercicio para o lector."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensa de $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Hai moito que explorar aquí, así que estamos anunciando unha recompensa por mellorar a visualización anterior. A diferenza da maioría das nosas recompensas, esta ten un límite de tempo. Debe <a %(annas_archive)s>enviar</a> o seu código de código aberto antes do 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "A mellor presentación recibirá $6,000, o segundo lugar $3,000, e o terceiro lugar $1,000. Todas as recompensas serán entregadas usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "A continuación están os criterios mínimos. Se ningunha presentación cumpre os criterios, aínda poderiamos outorgar algunhas recompensas, pero iso será á nosa discreción."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Faga un fork deste repositorio, e edite este post de blog en HTML (non se permiten outros backends ademais do noso backend Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Faga que a imaxe anterior sexa suavemente ampliable, para que poida ampliar ata os ISBNs individuais. Facer clic nos ISBNs debería levalo a unha páxina de metadata ou busca no Arquivo de Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Aínda debe poder cambiar entre todos os diferentes datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Os rangos de países e os rangos de editores deben destacarse ao pasar o rato. Pode usar, por exemplo, <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> para información de países, e o noso raspado “isbngrp” para editores (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Debe funcionar ben en escritorio e móbil."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Para puntos extra (estas son só ideas — deixe que a súa creatividade voa):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Darase unha forte consideración á usabilidade e ao bo aspecto."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mostrar metadata real para ISBNs individuais ao ampliar, como título e autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Mellor curva de recheo de espazo. Por exemplo, un zig-zag, indo de 0 a 4 na primeira fila e logo de volta (en reverso) de 5 a 9 na segunda fila — aplicado recursivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Esquemas de cores diferentes ou personalizables."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vistas especiais para comparar datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Formas de depurar problemas, como outros metadata que non coinciden ben (por exemplo, títulos moi diferentes)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotar imaxes con comentarios sobre ISBNs ou intervalos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Calquera heurística para identificar libros raros ou en risco."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Calquera idea creativa que poidas ter!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "PODES desviarte completamente dos criterios mínimos e facer unha visualización completamente diferente. Se é realmente espectacular, entón cualifica para a recompensa, pero a nosa discreción."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fai as túas presentacións publicando un comentario en <a %(annas_archive)s>este problema</a> cunha ligazón ao teu repositorio bifurcado, solicitude de fusión ou diferenza."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Código"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "O código para xerar estas imaxes, así como outros exemplos, pódese atopar neste <a %(annas_archive)s>directorio</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Ideamos un formato de datos compacto, co que toda a información ISBN requirida ocupa uns 75MB (comprimido). A descrición do formato de datos e o código para xeneralo pódense atopar <a %(annas_archive_l1244_1319)s>aquí</a>. Para a recompensa non estás obrigado a usar isto, pero probablemente sexa o formato máis conveniente para comezar. Podes transformar o noso metadata como queiras (aínda que todo o teu código ten que ser de código aberto)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Estamos ansiosos por ver o que se che ocorre. Boa sorte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contedores do Arquivo de Anna (AAC): estandarizando lanzamentos da maior biblioteca na sombra do mundo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "O Arquivo de Anna converteuse na maior biblioteca na sombra do mundo, requirindo que estandaricemos os nosos lanzamentos."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>O Arquivo de Anna</a> converteuse de lonxe na maior biblioteca na sombra do mundo, e a única biblioteca na sombra da súa escala que é completamente de código aberto e datos abertos. A continuación móstrase unha táboa da nosa páxina de Datasets (lixeiramente modificada):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Conseguimos isto de tres maneiras:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Reflexionando bibliotecas na sombra de datos abertos existentes (como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Axudando a bibliotecas na sombra que queren ser máis abertas, pero non tiñan o tempo ou os recursos para facelo (como a colección de cómics de Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspando bibliotecas que non desexan compartir en masa (como Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Para (2) e (3) agora xestionamos unha considerable colección de torrents nós mesmos (centos de TBs). Ata agora abordamos estas coleccións como casos únicos, o que significa infraestrutura e organización de datos a medida para cada colección. Isto engade unha carga significativa a cada lanzamento e fai especialmente difícil realizar lanzamentos máis incrementais."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Por iso decidimos estandarizar os nosos lanzamentos. Esta é unha publicación técnica no blog na que estamos a introducir o noso estándar: <strong>Contedores do Arquivo de Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Obxectivos de deseño"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "O noso caso de uso principal é a distribución de ficheiros e metadata asociada de diferentes coleccións existentes. As nosas consideracións máis importantes son:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Ficheiros e metadata heteroxéneos, no formato máis próximo ao orixinal posible."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificadores heteroxéneos nas bibliotecas de orixe, ou incluso a falta de identificadores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Lanzamentos separados de metadata fronte a datos de ficheiros, ou lanzamentos só de metadata (por exemplo, o noso lanzamento de ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribución a través de torrents, aínda que coa posibilidade doutros métodos de distribución (por exemplo, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Rexistros inmutables, xa que debemos supoñer que os nosos torrents vivirán para sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Lanzamentos incrementais / lanzamentos ampliables."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Lexible e escribible por máquinas, de forma conveniente e rápida, especialmente para a nosa pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspección humana algo fácil, aínda que isto é secundario á lexibilidade por máquinas."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Fácil de sementar as nosas coleccións cun seedbox estándar alugado."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Os datos binarios poden ser servidos directamente por servidores web como Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Algúns non-obxectivos:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Non nos importa que os ficheiros sexan fáciles de navegar manualmente no disco, ou que sexan buscables sen preprocesamento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Non nos importa ser directamente compatibles co software de biblioteca existente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Aínda que debería ser fácil para calquera sementar a nosa colección usando torrents, non esperamos que os ficheiros sexan utilizables sen un coñecemento técnico significativo e compromiso."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Dado que o Arquivo de Anna é de código aberto, queremos usar o noso formato directamente. Cando actualizamos o noso índice de busca, só accedemos a rutas dispoñibles publicamente, para que calquera que bifurque a nosa biblioteca poida comezar rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "O estándar"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Finalmente, decidimos un estándar relativamente sinxelo. É bastante flexible, non normativo e está en proceso de desenvolvemento."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contedor do Arquivo de Anna) é un único elemento que consta de <strong>metadata</strong> e, opcionalmente, <strong>datos binarios</strong>, ambos inmutables. Ten un identificador único global, chamado <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Colección.</strong> Cada AAC pertence a unha colección, que por definición é unha lista de AACs que son semanticamente consistentes. Isto significa que se fas un cambio significativo no formato dos metadata, entón tes que crear unha nova colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Coleccións de “rexistros” e “ficheiros”.</strong> Por convención, adoita ser conveniente lanzar “rexistros” e “ficheiros” como coleccións diferentes, para que poidan ser lanzadas en diferentes horarios, por exemplo, baseándose nas taxas de scraping. Un “rexistro” é unha colección só de metadata, que contén información como títulos de libros, autores, ISBNs, etc., mentres que os “ficheiros” son as coleccións que conteñen os ficheiros reais (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> O formato de AACID é este: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por exemplo, un AACID real que lanzamos é <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: o nome da colección, que pode conter letras ASCII, números e guións baixos (pero non dobre guión baixo)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: unha versión curta do ISO 8601, sempre en UTC, por exemplo, <code>20220723T194746Z</code>. Este número ten que aumentar monotonamente para cada lanzamento, aínda que a súa semántica exacta pode diferir por colección. Suxerimos usar o tempo de scraping ou de xeración do ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificador específico da colección, se é aplicable, por exemplo, o ID de Z-Library. Pode ser omitido ou truncado. Debe ser omitido ou truncado se o AACID superaría os 150 caracteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por exemplo, usando base57. Actualmente usamos a biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Rango de AACID.</strong> Dado que os AACIDs conteñen marcas de tempo que aumentan monotonamente, podemos usalo para denotar rangos dentro dunha colección particular. Usamos este formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, onde as marcas de tempo son inclusivas. Isto é consistente coa notación ISO 8601. Os rangos son continuos e poden superpoñerse, pero en caso de superposición deben conter rexistros idénticos aos previamente lanzados nesa colección (xa que os AACs son inmutables). Non se permiten rexistros faltantes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Ficheiro de metadata.</strong> Un ficheiro de metadata contén os metadata dun rango de AACs, para unha colección particular. Teñen as seguintes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "O nome do ficheiro debe ser un rango de AACID, prefixado con <code style=\"color: red\">annas_archive_meta__</code> e seguido por <code>.jsonl.zstd</code>. Por exemplo, un dos nosos lanzamentos chámase<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Como indica a extensión do ficheiro, o tipo de ficheiro é <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada obxecto JSON debe conter os seguintes campos no nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). Non se permiten outros campos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> son metadata arbitrarios, segundo a semántica da colección. Deben ser semanticamente consistentes dentro da colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> é opcional, e é o nome do cartafol de datos binarios que contén os datos binarios correspondentes. O nome do ficheiro dos datos binarios correspondentes dentro dese cartafol é o AACID do rexistro."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "O prefixo <code style=\"color: red\">annas_archive_meta__</code> pode ser adaptado ao nome da túa institución, por exemplo, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Cartafol de datos binarios.</strong> Un cartafol cos datos binarios dun rango de AACs, para unha colección particular. Teñen as seguintes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "O nome do directorio debe ser un rango de AACID, prefixado con <code style=\"color: green\">annas_archive_data__</code>, e sen sufixo. Por exemplo, un dos nosos lanzamentos reais ten un directorio chamado<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "O directorio debe conter ficheiros de datos para todos os AACs dentro do rango especificado. Cada ficheiro de datos debe ter o seu AACID como nome de ficheiro (sen extensións)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Recoméndase facer que estas carpetas sexan algo manexables en tamaño, por exemplo, que non sexan máis grandes de 100GB-1TB cada unha, aínda que esta recomendación pode cambiar co tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Os ficheiros de metadata e as carpetas de datos binarios poden agruparse en torrents, cun torrent por ficheiro de metadata ou cun torrent por carpeta de datos binarios. Os torrents deben ter o nome orixinal do ficheiro/directorio máis un sufixo <code>.torrent</code> como o seu nome de ficheiro."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Vexamos o noso recente lanzamento de Z-Library como exemplo. Consiste en dúas coleccións: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Isto permítenos extraer e lanzar por separado os rexistros de metadata dos ficheiros reais dos libros. Así, lanzamos dous torrents con ficheiros de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Tamén lanzamos un conxunto de torrents con carpetas de datos binarios, pero só para a colección “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 en total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Executando <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver o que hai dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Neste caso, é a metadata dun libro segundo informa Z-Library. No nivel superior só temos “aacid” e “metadata”, pero non hai “data_folder”, xa que non hai datos binarios correspondentes. O AACID contén “22430000” como ID principal, que podemos ver que se toma de “zlibrary_id”. Podemos esperar que outros AACs nesta colección teñan a mesma estrutura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Agora imos executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Esta é unha metadata AAC moito máis pequena, aínda que a maior parte deste AAC está situada noutro lugar nun ficheiro binario! Despois de todo, temos un “data_folder” esta vez, polo que podemos esperar que os datos binarios correspondentes estean situados en <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. A “metadata” contén o “zlibrary_id”, polo que podemos asocialo facilmente co AAC correspondente na colección “zlib_records”. Poderiamos asocialo de varias maneiras diferentes, por exemplo, a través de AACID — o estándar non prescribe iso."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Teña en conta que tampouco é necesario que o campo “metadata” sexa en si mesmo JSON. Podería ser unha cadea que conteña XML ou calquera outro formato de datos. Incluso podería almacenar información de metadata no blob binario asociado, por exemplo, se é moita información."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusión"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con este estándar, podemos facer lanzamentos de forma máis incremental e engadir novas fontes de datos máis facilmente. Xa temos algúns lanzamentos emocionantes en proceso!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Tamén esperamos que sexa máis doado para outras bibliotecas sombra espellar as nosas coleccións. Despois de todo, o noso obxectivo é preservar o coñecemento e a cultura humana para sempre, polo que canto máis redundancia, mellor."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Actualización de Anna: arquivo totalmente de código aberto, ElasticSearch, máis de 300GB de portadas de libros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Estivemos traballando sen descanso para ofrecer unha boa alternativa co Arquivo de Anna. Aquí están algunhas das cousas que logramos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Co peche de Z-Library e a detención dos seus (presuntos) fundadores, estivemos traballando sen descanso para ofrecer unha boa alternativa co Arquivo de Anna (non o enlazaremos aquí, pero podes buscalo en Google). Aquí están algunhas das cousas que logramos recentemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "O Arquivo de Anna é totalmente de código aberto"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Cremos que a información debería ser libre, e o noso propio código non é unha excepción. Publicamos todo o noso código na nosa instancia de Gitlab aloxada privadamente: <a %(annas_archive)s>Software de Anna</a>. Tamén usamos o rastreador de problemas para organizar o noso traballo. Se queres participar no noso desenvolvemento, este é un bo lugar para comezar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Para darche unha idea das cousas nas que estamos traballando, toma o noso traballo recente en melloras de rendemento do lado do cliente. Como aínda non implementamos a paginación, a miúdo devolvemos páxinas de busca moi longas, con 100-200 resultados. Non queriamos cortar os resultados da busca demasiado pronto, pero isto significaba que ralentizaría algúns dispositivos. Para isto, implementamos un pequeno truco: envolvemos a maioría dos resultados da busca en comentarios HTML (<code><!-- --></code>), e logo escribimos un pequeno Javascript que detectaría cando un resultado debería facerse visible, momento no que desenvolveriamos o comentario:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "A \"virtualización\" do DOM implementouse en 23 liñas, sen necesidade de bibliotecas sofisticadas! Este é o tipo de código rápido e pragmático que se obtén cando se ten pouco tempo e problemas reais que resolver. Informouse de que a nosa busca agora funciona ben en dispositivos lentos!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Outro gran esforzo foi automatizar a construción da base de datos. Cando lanzamos, simplemente xuntamos diferentes fontes ao azar. Agora queremos mantelas actualizadas, polo que escribimos un conxunto de scripts para descargar novos metadata dos dous forks de Library Genesis e integralos. O obxectivo non é só facer isto útil para o noso arquivo, senón tamén facilitar as cousas a calquera que queira experimentar cos metadata da biblioteca na sombra. O obxectivo sería un caderno de Jupyter que teña todo tipo de metadata interesante dispoñible, para que poidamos facer máis investigacións como descubrir que <a %(blog)s>porcentaxe de ISBNs se preservan para sempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, renovamos o noso sistema de doazóns. Agora pode usar unha tarxeta de crédito para depositar diñeiro directamente nas nosas carteiras de criptomoedas, sen realmente necesitar saber nada sobre criptomoedas. Seguiremos monitorizando como funciona isto na práctica, pero é un gran avance."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Cambiar a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Un dos nosos <a %(annas_archive)s>tickets</a> era un conxunto de problemas co noso sistema de busca. Usamos a busca de texto completo de MySQL, xa que tiñamos todos os nosos datos en MySQL de todos os xeitos. Pero tiña os seus límites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Algunhas consultas tardaban moito, ata o punto de acaparar todas as conexións abertas."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Por defecto, MySQL ten unha lonxitude mínima de palabra, ou o seu índice pode facerse moi grande. Informouse de que non se podía buscar \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "A busca só era algo rápida cando estaba completamente cargada na memoria, o que requiría que conseguísemos unha máquina máis cara para executala, ademais dalgúns comandos para precargar o índice ao iniciar."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Non teriamos sido capaces de amplialo facilmente para construír novas funcións, como mellor <a %(wikipedia_cjk_characters)s>tokenización para linguas sen espazos</a>, filtrado/facetado, ordenación, suxestións de \"quixo dicir\", autocompletado, e así por diante."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Despois de falar con varios expertos, decidimos usar ElasticSearch. Non foi perfecto (as súas suxestións de \"quixo dicir\" e funcións de autocompletado por defecto son malas), pero en xeral foi moito mellor que MySQL para a busca. Aínda non estamos <a %(youtube)s>moi convencidos</a> de usalo para calquera dato crítico (aínda que fixeron moitos <a %(elastic_co)s>avances</a>), pero en xeral estamos bastante contentos co cambio."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por agora, implementamos unha busca moito máis rápida, mellor soporte de linguas, mellor ordenación por relevancia, diferentes opcións de ordenación e filtrado por lingua/tipo de libro/tipo de ficheiro. Se tes curiosidade sobre como funciona, <a %(annas_archive_l140)s>bótalle</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>ollo</a>. É bastante accesible, aínda que podería usar algúns comentarios máis…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Máis de 300GB de portadas de libros liberadas"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, estamos felices de anunciar un pequeno lanzamento. En colaboración coas persoas que operan o fork Libgen.rs, estamos compartindo todas as súas portadas de libros a través de torrents e IPFS. Isto distribuirá a carga de ver as portadas entre máis máquinas e preservaraas mellor. En moitos (pero non todos) casos, as portadas dos libros están incluídas nos propios ficheiros, polo que isto é unha especie de \"datos derivados\". Pero telos en IPFS aínda é moi útil para o funcionamento diario tanto do Arquivo de Anna como dos diversos forks de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costume, pode atopar este lanzamento no Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>). Non enlazaremos aquí, pero pode atopalo facilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Agardamos poder relaxar un pouco o noso ritmo, agora que temos unha alternativa decente a Z-Library. Esta carga de traballo non é particularmente sostible. Se está interesado en axudar con programación, operacións de servidor ou traballo de preservación, definitivamente póñase en contacto connosco. Aínda hai moito <a %(annas_archive)s>traballo por facer</a>. Grazas polo seu interese e apoio."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "O Arquivo de Anna fixo unha copia de seguridade da maior biblioteca na sombra de cómics do mundo (95TB): pode axudar a sementala"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "A maior biblioteca na sombra de cómics do mundo tiña un único punto de fallo... ata hoxe."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discutir en Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "A maior biblioteca na sombra de cómics probablemente sexa a dun fork particular de Library Genesis: Libgen.li. O único administrador que xestiona ese sitio conseguiu reunir unha colección de cómics incrible de máis de 2 millóns de ficheiros, totalizando máis de 95TB. Non obstante, a diferenza doutras coleccións de Library Genesis, esta non estaba dispoñible en masa a través de torrents. Só podía acceder a estes cómics individualmente a través do seu servidor persoal lento: un único punto de fallo. Ata hoxe!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Nesta publicación contarémosche máis sobre esta colección e sobre a nosa recadación de fondos para apoiar máis este traballo."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>A Dra. Barbara Gordon intenta perderse no mundo cotián da biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Forks de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primeiro, un pouco de contexto. Pode que coñezas Library Genesis pola súa épica colección de libros. Menos xente sabe que os voluntarios de Library Genesis crearon outros proxectos, como unha considerable colección de revistas e documentos estándar, unha copia de seguridade completa de Sci-Hub (en colaboración coa fundadora de Sci-Hub, Alexandra Elbakyan) e, de feito, unha enorme colección de cómics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Nalgún momento, diferentes operadores dos espellos de Library Genesis seguiron camiños separados, o que deu lugar á situación actual de ter varios \"forks\" diferentes, todos aínda co nome de Library Genesis. O fork de Libgen.li ten de forma única esta colección de cómics, así como unha considerable colección de revistas (na que tamén estamos a traballar)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaboración"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada a súa magnitude, esta colección leva moito tempo na nosa lista de desexos, así que despois do noso éxito coa copia de seguridade de Z-Library, fixamos a nosa atención nesta colección. Ao principio, raspámola directamente, o que foi todo un reto, xa que o seu servidor non estaba nas mellores condicións. Conseguimos uns 15TB deste xeito, pero foi un proceso lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Por sorte, conseguimos contactar co operador da biblioteca, que aceptou enviarnos todos os datos directamente, o que foi moito máis rápido. Aínda así, levou máis de medio ano transferir e procesar todos os datos, e case perdemos todo debido a unha corrupción do disco, o que significaría comezar de novo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Esta experiencia fíxonos crer que é importante sacar estes datos ao público o máis rápido posible, para que poidan ser espellados amplamente. Estamos a só un ou dous incidentes desafortunados de perder esta colección para sempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "A colección"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Moverse rápido significa que a colección está un pouco desorganizada… Imos botarlle un ollo. Imaxina que temos un sistema de ficheiros (que na realidade estamos dividindo en torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "O primeiro directorio, <code>/repository</code>, é a parte máis estruturada disto. Este directorio contén os chamados “mil dirs”: directorios cada un con mil ficheiros, que están numerados incrementalmente na base de datos. O directorio <code>0</code> contén ficheiros con comic_id 0–999, e así sucesivamente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Este é o mesmo esquema que Library Genesis leva usando para as súas coleccións de ficción e non ficción. A idea é que cada “mil dir” se converta automaticamente nun torrent tan pronto como se encha."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Non obstante, o operador de Libgen.li nunca fixo torrents para esta colección, e así os mil dirs probablemente se volveron incómodos, e deron paso a “dirs non clasificados”. Estes son <code>/comics0</code> a <code>/comics4</code>. Todos conteñen estruturas de directorios únicas, que probablemente tiñan sentido para recoller os ficheiros, pero agora non nos fan moito sentido. Por sorte, o metadata aínda se refire directamente a todos estes ficheiros, polo que a súa organización de almacenamento no disco realmente non importa!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "O metadata está dispoñible en forma dunha base de datos MySQL. Pódese descargar directamente desde o sitio web de Libgen.li, pero tamén o faremos dispoñible nun torrent, xunto coa nosa propia táboa con todos os hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Análise"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Cando recibes 95TB no teu clúster de almacenamento, tentas entender o que hai alí… Fixemos algunha análise para ver se podiamos reducir un pouco o tamaño, como eliminando duplicados. Aquí están algúns dos nosos achados:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Os duplicados semánticos (diferentes escaneos do mesmo libro) poden teoricamente ser filtrados, pero é complicado. Ao revisar manualmente os cómics atopamos demasiados falsos positivos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Hai algúns duplicados puramente por MD5, o que é relativamente desperdiciador, pero filtralos só nos daría un aforro de aproximadamente 1% in. A esta escala aínda é aproximadamente 1TB, pero tamén, a esta escala 1TB realmente non importa. Preferimos non arriscar a destruír datos accidentalmente neste proceso."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Atopamos un montón de datos non relacionados con libros, como películas baseadas en cómics. Iso tamén parece desperdiciador, xa que xa están amplamente dispoñibles por outros medios. Non obstante, decatámonos de que non podiamos simplemente filtrar os ficheiros de películas, xa que tamén hai <em>libros de cómics interactivos</em> que foron lanzados no ordenador, que alguén gravou e gardou como películas."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "En definitiva, calquera cousa que puidésemos eliminar da colección só aforraría uns poucos por cento. Entón lembramos que somos acumuladores de datos, e as persoas que van espellar isto tamén son acumuladores de datos, así que, \"¡QUE QUERES DICIR, ELIMINAR?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Por iso, estamos presentándovos a colección completa e sen modificar. É moita información, pero esperamos que a suficiente xente se preocupe por compartila de todos os xeitos."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Recaudación de fondos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Estamos lanzando estes datos en grandes bloques. O primeiro torrent é de <code>/comics0</code>, que puxemos nun enorme ficheiro .tar de 12TB. Iso é mellor para o teu disco duro e software de torrent que un millón de ficheiros máis pequenos."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte deste lanzamento, estamos facendo unha recaudación de fondos. Estamos buscando recadar 20.000 dólares para cubrir os custos operativos e de contratación para esta colección, así como para habilitar proxectos en curso e futuros. Temos algúns <em>enormes</em> en proceso."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>¿A quen estou apoiando coa miña doazón?</em> En resumo: estamos respaldando todo o coñecemento e cultura da humanidade, e facéndoo facilmente accesible. Todo o noso código e datos son de código aberto, somos un proxecto completamente dirixido por voluntarios, e ata agora salvamos 125TB de libros (ademais dos torrents existentes de Libgen e Scihub). En última instancia, estamos construíndo un volante que habilita e incentiva ás persoas a atopar, escanear e respaldar todos os libros do mundo. Escribiremos sobre o noso plan mestre nunha publicación futura. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se doas para unha subscrición de 12 meses de “Amazing Archivist” (780 dólares), podes <strong>“adoptar un torrent”</strong>, o que significa que poñeremos o teu nome de usuario ou mensaxe no nome dun dos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Podes doar indo a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a> e facendo clic no botón “Doar”. Tamén estamos buscando máis voluntarios: enxeñeiros de software, investigadores de seguridade, expertos en comercio anónimo e tradutores. Tamén podes apoiarnos proporcionando servizos de hospedaxe. E, por suposto, por favor comparte os nosos torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Grazas a todos os que xa nos apoiaron tan xenerosamente! Realmente estás facendo unha diferenza."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Aquí están os torrents lanzados ata agora (aínda estamos procesando o resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Todos os torrents pódense atopar en <a %(wikipedia_annas_archive)s>Arquivo de Anna</a> baixo “Datasets” (non enlazamos directamente alí, para que as ligazóns a este blog non sexan eliminadas de Reddit, Twitter, etc). Desde alí, segue a ligazón ao sitio web de Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Que segue?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un conxunto de torrents son xeniais para a preservación a longo prazo, pero non tanto para o acceso diario. Traballaremos con socios de hospedaxe para subir todos estes datos á web (xa que o Arquivo de Anna non hospeda nada directamente). Por suposto, poderás atopar estas ligazóns de descarga no Arquivo de Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Tamén estamos invitando a todos a facer cousas con estes datos! Axúdanos a analizalos mellor, deduplicalos, poñelos en IPFS, mesturalos, adestrar os teus modelos de IA con eles, e así por diante. Son todos teus, e estamos ansiosos por ver o que fas con eles."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como dixemos antes, aínda temos algúns lanzamentos enormes por vir (se <em>alguén</em> puidese <em>accidentalmente</em> enviarnos un volcado dunha <em>certa</em> base de datos ACS4, xa sabes onde atoparnos...), así como construír o volante para respaldar todos os libros do mundo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Así que mantente atento, acabamos de comezar."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x novos libros engadidos ao Espello da Biblioteca Pirata (+24TB, 3,8 millóns de libros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "No lanzamento orixinal do Espello da Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>), fixemos un espello de Z-Library, unha gran colección de libros ilegal. Como recordatorio, isto é o que escribimos nesa publicación orixinal do blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library é unha biblioteca popular (e ilegal). Tomaron a colección de Library Genesis e fixérona facilmente buscable. Ademais, fixéronse moi efectivos solicitando novas contribucións de libros, incentivando aos usuarios contribuíntes con varios beneficios. Actualmente non contribúen estes novos libros de volta a Library Genesis. E a diferenza de Library Genesis, non fan que a súa colección sexa facilmente espellable, o que impide unha ampla preservación. Isto é importante para o seu modelo de negocio, xa que cobran diñeiro por acceder á súa colección en masa (máis de 10 libros por día)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Non facemos xuízos morais sobre cobrar diñeiro por acceso masivo a unha colección de libros ilegal. Non hai dúbida de que a Z-Library tivo éxito en expandir o acceso ao coñecemento e en conseguir máis libros. Estamos aquí simplemente para facer a nosa parte: asegurar a preservación a longo prazo desta colección privada."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Esa colección data de mediados de 2021. Mentres tanto, a Z-Library creceu a un ritmo asombroso: engadiron uns 3,8 millóns de novos libros. Hai algúns duplicados, claro, pero a maioría parecen ser libros realmente novos ou escaneos de maior calidade de libros previamente enviados. Isto débese en gran parte ao aumento do número de moderadores voluntarios na Z-Library e ao seu sistema de carga masiva con deduplicación. Gustaríanos felicitalos por estes logros."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Estamos felices de anunciar que conseguimos todos os libros que se engadiron á Z-Library entre o noso último espello e agosto de 2022. Tamén volvemos atrás e raspamos algúns libros que perdemos a primeira vez. En total, esta nova colección é duns 24TB, que é moito máis grande que a anterior (7TB). O noso espello agora ten un total de 31TB. De novo, deduplicamos contra Library Genesis, xa que xa hai torrents dispoñibles para esa colección."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Por favor, vaia ao Espello da Biblioteca Pirata para ver a nova colección (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>). Alí hai máis información sobre como están estruturados os ficheiros e o que cambiou desde a última vez. Non enlazaremos desde aquí, xa que este é só un sitio web de blog que non alberga ningún material ilegal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Por suposto, sementar tamén é unha gran forma de axudarnos. Grazas a todos os que están sementando o noso conxunto anterior de torrents. Estamos agradecidos pola resposta positiva e felices de que haxa tanta xente que se preocupa pola preservación do coñecemento e a cultura deste xeito inusual."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Como converterse nun arquivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "O primeiro desafío pode ser sorprendente. Non é un problema técnico, nin un problema legal. É un problema psicolóxico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antes de comezar, dúas actualizacións sobre o Espello da Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Recibimos algunhas doazóns extremadamente xenerosas. A primeira foi de $10k dun individuo anónimo que tamén estivo apoiando a \"bookwarrior\", o fundador orixinal de Library Genesis. Un agradecemento especial a bookwarrior por facilitar esta doazón. A segunda foi outro $10k dun doante anónimo, que se puxo en contacto despois do noso último lanzamento e foi inspirado para axudar. Tamén tivemos unha serie de doazóns máis pequenas. Moitas grazas por todo o voso xeneroso apoio. Temos algúns proxectos novos emocionantes en marcha que isto apoiará, así que estean atentos."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Tivemos algúns problemas técnicos co tamaño do noso segundo lanzamento, pero os nosos torrents están agora activos e sementando. Tamén recibimos unha oferta xenerosa dun individuo anónimo para sementar a nosa colección nos seus servidores de moi alta velocidade, así que estamos facendo unha carga especial nas súas máquinas, despois da cal todos os demais que estean descargando a colección deberían ver unha gran mellora na velocidade."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Pódense escribir libros enteiros sobre o <em>por que</em> da preservación dixital en xeral, e do arquivismo pirata en particular, pero imos dar unha breve introdución para aqueles que non están moi familiarizados. O mundo está producindo máis coñecemento e cultura que nunca, pero tamén se está perdendo máis que nunca. A humanidade confía en gran medida en corporacións como editoriais académicas, servizos de streaming e empresas de redes sociais para este patrimonio, e a miúdo non demostraron ser grandes custodios. Consulte o documental Digital Amnesia, ou realmente calquera charla de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Hai algunhas institucións que fan un bo traballo arquivando tanto como poden, pero están limitadas pola lei. Como piratas, estamos nunha posición única para arquivar coleccións que eles non poden tocar, debido á aplicación dos dereitos de autor ou outras restricións. Tamén podemos espellar coleccións moitas veces, en todo o mundo, aumentando así as posibilidades de preservación adecuada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por agora, non entraremos en discusións sobre os pros e contras da propiedade intelectual, a moralidade de romper a lei, reflexións sobre a censura ou o tema do acceso ao coñecemento e a cultura. Con todo iso fóra do camiño, imos mergullarnos no <em>como</em>. Compartiremos como o noso equipo se converteu en arquivistas piratas e as leccións que aprendemos ao longo do camiño. Hai moitos desafíos cando emprendes esta viaxe, e esperamos poder axudarche con algúns deles."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunidade"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "O primeiro desafío pode ser sorprendente. Non é un problema técnico, nin un problema legal. É un problema psicolóxico: facer este traballo nas sombras pode ser incrible solitario. Dependendo do que planeas facer e do teu modelo de ameaza, pode que teñas que ser moi coidadoso. Nun extremo do espectro temos persoas como Alexandra Elbakyan*, a fundadora de Sci-Hub, que é moi aberta sobre as súas actividades. Pero está en alto risco de ser arrestada se visitase un país occidental neste momento, e podería enfrontarse a décadas de prisión. É ese un risco que estarías disposto a asumir? Estamos no outro extremo do espectro; sendo moi coidadosos de non deixar ningún rastro e tendo unha forte seguridade operativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como se mencionou en HN por \"ynno\", Alexandra inicialmente non quería ser coñecida: \"Os seus servidores estaban configurados para emitir mensaxes de erro detalladas de PHP, incluíndo a ruta completa do ficheiro fonte con erro, que estaba baixo o directorio /home/<USER>" Así que, usa nomes de usuario aleatorios nos ordenadores que uses para estas cousas, no caso de que configures algo incorrectamente."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Esa secrecía, con todo, vén cun custo psicolóxico. A maioría das persoas adoran ser recoñecidas polo traballo que fan, e aínda así non podes recibir ningún crédito por isto na vida real. Incluso cousas simples poden ser desafiantes, como amigos preguntándoche en que estiveches traballando (en algún momento \"trasteando co meu NAS / homelab\" faise vello)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Por iso é tan importante atopar algunha comunidade. Podes renunciar a algo de seguridade operativa confiando en algúns amigos moi próximos, que sabes que podes confiar profundamente. Mesmo entón, ten coidado de non poñer nada por escrito, no caso de que teñan que entregar os seus correos electrónicos ás autoridades, ou se os seus dispositivos están comprometidos dalgún outro xeito."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Mellor aínda é atopar algúns compañeiros piratas. Se os teus amigos próximos están interesados en unirse a ti, xenial! Se non, poderías atopar outros en liña. Tristemente, esta aínda é unha comunidade de nicho. Ata agora só atopamos un puñado de outros que están activos neste espazo. Bos lugares para comezar parecen ser os foros de Library Genesis e r/DataHoarder. O Archive Team tamén ten individuos afíns, aínda que operan dentro da lei (aínda que en algunhas áreas grises da lei). As escenas tradicionais de \"warez\" e piratería tamén teñen persoas que pensan de xeito similar."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Estamos abertos a ideas sobre como fomentar a comunidade e explorar ideas. Non dubide en enviarnos unha mensaxe en Twitter ou Reddit. Quizais poderiamos organizar algún tipo de foro ou grupo de chat. Un dos desafíos é que isto pode ser facilmente censurado ao usar plataformas comúns, polo que teriamos que aloxalo nós mesmos. Tamén hai un equilibrio entre ter estas discusións totalmente públicas (máis potencial de participación) fronte a facelas privadas (non deixar que os potenciais \"obxectivos\" saiban que estamos a piques de rastrexalos). Teremos que pensar niso. Avísanos se estás interesado nisto!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proxectos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Cando facemos un proxecto, ten un par de fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selección de dominio / filosofía: Onde queres centrarte aproximadamente e por que? Cales son as túas paixóns, habilidades e circunstancias únicas que podes usar ao teu favor?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selección de obxectivo: Que colección específica vas espellar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Rastreo de metadata: Catalogar información sobre os arquivos, sen descargar realmente os arquivos (a miúdo moito máis grandes) en si mesmos."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selección de datos: Baseándose na metadata, reducir cales datos son máis relevantes para arquivar agora mesmo. Podería ser todo, pero a miúdo hai unha forma razoable de aforrar espazo e ancho de banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Rastreo de datos: Obter realmente os datos."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribución: Empaquetalo en torrents, anuncialo nalgún lugar, conseguir que a xente o difunda."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Estas non son fases completamente independentes, e a miúdo as ideas dunha fase posterior envíante de volta a unha fase anterior. Por exemplo, durante o rastreo de metadata podes darte conta de que o obxectivo que seleccionaches ten mecanismos defensivos máis aló do teu nivel de habilidade (como bloqueos de IP), polo que volves atrás e atopas un obxectivo diferente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selección de dominio / filosofía"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Non hai escaseza de coñecemento e patrimonio cultural que salvar, o que pode ser abafador. Por iso, a miúdo é útil tomarse un momento e pensar en cal pode ser a túa contribución."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Cada persoa ten unha forma diferente de pensar sobre isto, pero aquí tes algunhas preguntas que poderías facerche a ti mesmo:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Por que estás interesado nisto? Que che apaixona? Se podemos conseguir un grupo de persoas que arquiven todos os tipos de cousas que lles importan especificamente, iso cubriría moito! Saberás moito máis que a persoa media sobre a túa paixón, como cal é a información importante para gardar, cales son as mellores coleccións e comunidades en liña, e así por diante."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Que habilidades tes que podes usar ao teu favor? Por exemplo, se es un experto en seguridade en liña, podes atopar formas de derrotar bloqueos de IP para obxectivos seguros. Se es bo organizando comunidades, entón quizais poidas reunir a algunhas persoas ao redor dun obxectivo. É útil saber algo de programación, aínda que só sexa para manter unha boa seguridade operativa durante este proceso."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Canto tempo tes para isto? O noso consello sería comezar pequeno e facer proxectos máis grandes a medida que te afás, pero pode chegar a consumir todo o teu tempo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Cal sería unha área de alto impacto na que centrarse? Se vas gastar X horas en arquivar pirata, entón como podes obter o maior \"rendemento polo teu diñeiro\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Cales son as formas únicas nas que estás pensando sobre isto? Poderías ter algunhas ideas ou enfoques interesantes que outros poderían ter pasado por alto."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "No noso caso, preocupábanos especialmente a preservación a longo prazo da ciencia. Coñeciamos Library Genesis, e como foi completamente espellado moitas veces usando torrents. Encantounos esa idea. Entón un día, un de nós intentou atopar algúns libros de texto científicos en Library Genesis, pero non puido atopalos, poñendo en dúbida o completa que realmente era. Logo buscamos eses libros de texto en liña, e atopámolos noutros lugares, o que plantou a semente para o noso proxecto. Mesmo antes de coñecer a Z-Library, tiñamos a idea de non tentar recoller todos eses libros manualmente, senón centrarnos en espellar coleccións existentes, e contribuílas de volta a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selección de obxectivo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Entón, temos a nosa área que estamos a examinar, agora que colección específica imos espellar? Hai un par de cousas que fan un bo obxectivo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Única: non está xa ben cuberta por outros proxectos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accesible: non usa moitas capas de protección para evitar que extraia a súa metadata e datos."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Información especial: ten algunha información especial sobre este obxectivo, como que dalgún xeito ten acceso especial a esta colección, ou descubriu como derrotar as súas defensas. Isto non é necesario (o noso próximo proxecto non fai nada especial), pero certamente axuda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Cando atopamos os nosos libros de texto de ciencia en sitios web distintos de Library Genesis, intentamos descubrir como chegaron a internet. Logo atopamos a Z-Library, e decatámonos de que, aínda que a maioría dos libros non aparecen alí primeiro, finalmente acaban alí. Aprendemos sobre a súa relación con Library Genesis, e a estrutura de incentivos (financeiros) e a interface de usuario superior, ambas as cales a facían unha colección moito máis completa. Logo fixemos algunha extracción preliminar de metadata e datos, e decatámonos de que podiamos sortear os seus límites de descarga de IP, aproveitando o acceso especial dun dos nosos membros a moitos servidores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Mentres explora diferentes obxectivos, xa é importante ocultar os seus rastros usando VPNs e enderezos de correo electrónico desbotables, do que falaremos máis adiante."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Extracción de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Imos poñernos un pouco máis técnicos aquí. Para realmente extraer a metadata dos sitios web, mantivemos as cousas bastante sinxelas. Usamos scripts de Python, ás veces curl, e unha base de datos MySQL para almacenar os resultados. Non usamos ningún software de extracción sofisticado que poida mapear sitios web complexos, xa que ata agora só necesitamos extraer un ou dous tipos de páxinas simplemente enumerando a través de ids e analizando o HTML. Se non hai páxinas facilmente enumeradas, entón pode necesitar un rastreador adecuado que intente atopar todas as páxinas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antes de comezar a extraer un sitio web completo, intente facelo manualmente por un tempo. Pase por unhas ducias de páxinas vostede mesmo, para ter unha idea de como funciona iso. Ás veces xa se atopará con bloqueos de IP ou outro comportamento interesante deste xeito. O mesmo ocorre coa extracción de datos: antes de profundar demasiado neste obxectivo, asegúrese de que realmente pode descargar os seus datos de forma efectiva."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Para sortear restricións, hai algunhas cousas que pode probar. Hai outros enderezos IP ou servidores que aloxan os mesmos datos pero non teñen as mesmas restricións? Hai algún punto final de API que non teña restricións, mentres que outros si? A que ritmo de descarga se bloquea o seu IP, e por canto tempo? Ou non está bloqueado pero ralentizado? Que pasa se crea unha conta de usuario, como cambian as cousas entón? Pode usar HTTP/2 para manter as conexións abertas, e iso aumenta a taxa á que pode solicitar páxinas? Hai páxinas que listan varios ficheiros á vez, e a información listada alí é suficiente?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Cousas que probablemente queira gardar inclúen:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Título"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome do ficheiro / localización"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pode ser algún ID interno, pero IDs como ISBN ou DOI tamén son útiles."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Tamaño: para calcular canto espazo en disco necesita."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): para confirmar que descargou o ficheiro correctamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data engadida/modificada: para que poida volver máis tarde e descargar ficheiros que non descargou antes (aínda que a miúdo tamén pode usar o ID ou hash para isto)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrición, categoría, etiquetas, autores, idioma, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Normalmente facemos isto en dúas etapas. Primeiro descargamos os ficheiros HTML en bruto, xeralmente directamente en MySQL (para evitar moitos ficheiros pequenos, do que falamos máis abaixo). Logo, nun paso separado, revisamos eses ficheiros HTML e analizámolos en táboas MySQL reais. Deste xeito non ten que volver descargar todo desde cero se descobre un erro no seu código de análise, xa que pode simplemente reprocesar os ficheiros HTML co novo código. Tamén é a miúdo máis fácil paralelizar o paso de procesamento, aforrando así algo de tempo (e pode escribir o código de procesamento mentres a extracción está en marcha, en lugar de ter que escribir ambos pasos á vez)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, teña en conta que para algúns obxectivos a extracción de metadata é todo o que hai. Hai algunhas coleccións enormes de metadata que non están adecuadamente preservadas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selección de datos"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "A miúdo pode usar a metadata para determinar un subconxunto razoable de datos para descargar. Mesmo se finalmente quere descargar todos os datos, pode ser útil priorizar os elementos máis importantes primeiro, no caso de que sexa detectado e as defensas melloren, ou porque necesitaría mercar máis discos, ou simplemente porque xorde algo máis na súa vida antes de poder descargar todo."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Por exemplo, unha colección pode ter múltiples edicións do mesmo recurso subxacente (como un libro ou unha película), onde unha está marcada como de mellor calidade. Gardar esas edicións primeiro tería moito sentido. Eventualmente pode querer gardar todas as edicións, xa que nalgúns casos a metadata pode estar etiquetada incorrectamente, ou pode haber compensacións descoñecidas entre edicións (por exemplo, a \"mellor edición\" pode ser a mellor en moitos aspectos pero peor noutros, como unha película que ten unha resolución máis alta pero carece de subtítulos)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Tamén pode buscar na súa base de datos de metadata para atopar cousas interesantes. Cal é o ficheiro máis grande que está aloxado, e por que é tan grande? Cal é o ficheiro máis pequeno? Hai patróns interesantes ou inesperados en canto a certas categorías, idiomas, etc.? Hai títulos duplicados ou moi similares? Hai patróns sobre cando se engadiron os datos, como un día no que se engadiron moitos ficheiros á vez? A miúdo pode aprender moito observando o conxunto de datos de diferentes maneiras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "No noso caso, deduplicamos os libros de Z-Library contra os hashes md5 en Library Genesis, aforrando así moito tempo de descarga e espazo en disco. Esta é unha situación bastante única, con todo. Na maioría dos casos non hai bases de datos completas de que ficheiros xa están adecuadamente preservados por outros piratas. Isto en si mesmo é unha gran oportunidade para alguén por aí. Sería xenial ter unha visión xeral actualizada regularmente de cousas como música e películas que xa están amplamente compartidas en sitios de torrents, e que polo tanto son de menor prioridade para incluír en espellos piratas."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extracción de datos"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Agora está listo para realmente descargar os datos en masa. Como se mencionou antes, neste punto xa debería ter descargado manualmente un montón de ficheiros, para comprender mellor o comportamento e as restricións do obxectivo. Con todo, aínda haberá sorpresas para vostede unha vez que realmente comece a descargar moitos ficheiros á vez."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "O noso consello aquí é principalmente mantelo sinxelo. Comece simplemente descargando un montón de ficheiros. Pode usar Python, e logo expandirse a múltiples fíos. Pero ás veces aínda máis sinxelo é xerar ficheiros Bash directamente desde a base de datos, e logo executar varios deles en múltiples ventás de terminal para escalar. Un truco técnico rápido que vale a pena mencionar aquí é usar OUTFILE en MySQL, que pode escribir en calquera lugar se desactiva \"secure_file_priv\" en mysqld.cnf (e asegúrese de desactivar/invalidar AppArmor se está en Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Almacenamos os datos en discos duros simples. Comece co que teña, e expanda lentamente. Pode ser abrumador pensar en almacenar centos de TBs de datos. Se esa é a situación que está a afrontar, simplemente poña un bo subconxunto primeiro, e no seu anuncio pida axuda para almacenar o resto. Se quere conseguir máis discos duros vostede mesmo, entón r/DataHoarder ten algúns bos recursos para conseguir boas ofertas."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Tente non preocuparse demasiado por sistemas de ficheiros sofisticados. É fácil caer na madrigueira do coello configurando cousas como ZFS. Un detalle técnico do que ser consciente, con todo, é que moitos sistemas de ficheiros non manexan ben moitos ficheiros. Atopamos que unha solución sinxela é crear múltiples directorios, por exemplo, para diferentes rangos de ID ou prefixos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Despois de descargar os datos, asegúrese de comprobar a integridade dos ficheiros usando hashes na metadata, se están dispoñibles."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribución"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Ten os datos, dándolle así a posesión do primeiro espello pirata do mundo do seu obxectivo (moi probablemente). En moitos aspectos a parte máis difícil xa pasou, pero a parte máis arriscada aínda está por diante. Despois de todo, ata agora estivo en segredo; voando baixo o radar. Todo o que tiña que facer era usar un bo VPN en todo momento, non encher os seus datos persoais en ningún formulario (obviamente), e quizais usar unha sesión de navegador especial (ou mesmo un ordenador diferente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Agora ten que distribuír os datos. No noso caso primeiro quixemos contribuír os libros de volta a Library Genesis, pero logo descubrimos rapidamente as dificultades nisto (clasificación de ficción vs non ficción). Así que decidimos distribuír usando torrents ao estilo de Library Genesis. Se ten a oportunidade de contribuír a un proxecto existente, entón iso podería aforrarlle moito tempo. Con todo, actualmente non hai moitos espellos piratas ben organizados por aí."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Así que digamos que decide distribuír torrents vostede mesmo. Tente manter eses ficheiros pequenos, para que sexan fáciles de espellar noutros sitios web. Entón terá que sementar os torrents vostede mesmo, mentres aínda permanece anónimo. Pode usar un VPN (con ou sen reenvío de portos), ou pagar con Bitcoins mesturados por un Seedbox. Se non sabe o que significan algúns deses termos, terá moito que ler, xa que é importante que entenda os compromisos de risco aquí."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Pode aloxar os ficheiros torrent en sitios web de torrents existentes. No noso caso, escollemos realmente aloxar un sitio web, xa que tamén queriamos difundir a nosa filosofía de forma clara. Pode facer isto vostede mesmo de maneira similar (usamos Njalla para os nosos dominios e aloxamento, pagado con Bitcoins mesturados), pero tamén non dubide en contactarnos para que aloxemos os seus torrents. Estamos buscando construír un índice completo de espellos piratas co tempo, se esta idea colle."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "En canto á selección de VPN, xa se escribiu moito sobre isto, así que só repetiremos o consello xeral de elixir por reputación. Políticas de non rexistro probadas en tribunais con longos rexistros de protección da privacidade é a opción de menor risco, na nosa opinión. Teña en conta que mesmo cando fai todo ben, nunca pode chegar a risco cero. Por exemplo, ao sementar os seus torrents, un actor estatal altamente motivado probablemente pode observar os fluxos de datos entrantes e saíntes para servidores VPN, e deducir quen é vostede. Ou simplemente pode cometer un erro dalgún xeito. Probablemente xa o fixemos, e volveremos facelo. Afortunadamente, os estados nación non se preocupan <em>tanto</em> pola piratería."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Unha decisión a tomar para cada proxecto, é se publicalo usando a mesma identidade que antes, ou non. Se segue usando o mesmo nome, entón os erros na seguridade operativa de proxectos anteriores poderían volver a morderlle. Pero publicar baixo diferentes nomes significa que non constrúe unha reputación máis duradeira. Escollemos ter unha forte seguridade operativa desde o principio para poder seguir usando a mesma identidade, pero non dubidaremos en publicar baixo un nome diferente se cometemos un erro ou se as circunstancias o requiren."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Facer correr a voz pode ser complicado. Como dixemos, esta aínda é unha comunidade de nicho. Inicialmente publicamos en Reddit, pero realmente conseguimos tracción en Hacker News. Por agora a nosa recomendación é publicalo en algúns lugares e ver que pasa. E de novo, contáctenos. Encantaríanos difundir a palabra de máis esforzos de arquivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Agardamos que isto sexa útil para os novos arquivistas piratas que comezan. Estamos emocionados de darvos a benvida a este mundo, así que non dubidedes en contactar. Imos preservar tanto coñecemento e cultura do mundo como poidamos, e espellalo por todas partes."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentando o Espello da Biblioteca Pirata: Preservando 7TB de libros (que non están en Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Este proxecto (EDIT: trasladado a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>) ten como obxectivo contribuír á preservación e liberación do coñecemento humano. Facemos a nosa pequena e humilde contribución, seguindo os pasos dos grandes que nos precederon."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "O enfoque deste proxecto está ilustrado polo seu nome:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Deliberadamente violamos a lei de dereitos de autor na maioría dos países. Isto permítenos facer algo que as entidades legais non poden facer: asegurarnos de que os libros sexan espellados por todas partes."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Como a maioría das bibliotecas, centramos principalmente en materiais escritos como libros. Poderiamos expandirnos a outros tipos de medios no futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Espello</strong> - Somos estritamente un espello de bibliotecas existentes. Centrámonos na preservación, non en facer que os libros sexan facilmente buscables e descargables (acceso) ou en fomentar unha gran comunidade de persoas que contribúan con novos libros (fonte)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "A primeira biblioteca que espellamos é Z-Library. Esta é unha biblioteca popular (e ilegal). Tomaron a colección de Library Genesis e fixérona facilmente buscable. Ademais, convertéronse en moi efectivos para solicitar novas contribucións de libros, incentivando aos usuarios contribuíntes con varios beneficios. Actualmente non contribúen estes novos libros de volta a Library Genesis. E a diferenza de Library Genesis, non fan que a súa colección sexa facilmente espellable, o que impide unha ampla preservación. Isto é importante para o seu modelo de negocio, xa que cobran diñeiro por acceder á súa colección en masa (máis de 10 libros por día)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Non facemos xuízos morais sobre cobrar diñeiro por acceso masivo a unha colección de libros ilegal. Non hai dúbida de que a Z-Library tivo éxito en expandir o acceso ao coñecemento e en conseguir máis libros. Estamos aquí simplemente para facer a nosa parte: asegurar a preservación a longo prazo desta colección privada."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Gustaríanos invitarvos a axudar a preservar e liberar o coñecemento humano descargando e compartindo os nosos torrents. Consulte a páxina do proxecto para obter máis información sobre como están organizados os datos."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Tamén nos gustaría moito invitarvos a contribuír coas vosas ideas sobre que coleccións espellar a continuación e como facelo. Xuntos podemos lograr moito. Esta é só unha pequena contribución entre innumerables outras. Grazas, por todo o que facedes."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Non enlazamos aos arquivos desde este blog. Por favor, atópao ti mesmo.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Volcado de ISBNdb, ou Cantos Libros Están Preservados Para Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se deduplicásemos correctamente os arquivos das bibliotecas sombra, que porcentaxe de todos os libros do mundo temos preservado?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Co Espello da Biblioteca Pirata (EDIT: trasladado a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>), o noso obxectivo é tomar todos os libros do mundo e preservalos para sempre.<sup>1</sup> Entre os nosos torrents de Z-Library e os torrents orixinais de Library Genesis, temos 11.783.153 arquivos. Pero cantos son realmente? Se deduplicásemos correctamente eses arquivos, que porcentaxe de todos os libros do mundo temos preservado? Realmente gustaríanos ter algo así:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of do patrimonio escrito da humanidade preservado para sempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Para unha porcentaxe, necesitamos un denominador: o número total de libros publicados.<sup>2</sup> Antes da desaparición de Google Books, un enxeñeiro do proxecto, Leonid Taycher, <a %(booksearch_blogspot)s>intentou estimar</a> este número. Chegou — en broma — a 129.864.880 (“polo menos ata o domingo”). Estimou este número construíndo unha base de datos unificada de todos os libros do mundo. Para isto, reuniu diferentes Datasets e logo fusionounos de varias maneiras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como un breve paréntese, hai outra persoa que intentou catalogar todos os libros do mundo: Aaron Swartz, o falecido activista dixital e cofundador de Reddit.<sup>3</sup> El <a %(youtube)s>comezou Open Library</a> co obxectivo de \"unha páxina web para cada libro publicado\", combinando datos de moitas fontes diferentes. Acabou pagando o prezo máis alto polo seu traballo de preservación dixital cando foi procesado por descargar masivamente artigos académicos, o que levou ao seu suicidio. Non fai falta dicir que esta é unha das razóns polas que o noso grupo é pseudónimo e por que estamos sendo moi coidadosos. Open Library aínda está sendo heroicamente xestionada por persoas no Internet Archive, continuando o legado de Aaron. Volveremos a isto máis adiante nesta publicación."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "No blog de Google, Taycher describe algúns dos desafíos para estimar este número. Primeiro, que constitúe un libro? Hai algunhas definicións posibles:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copias físicas.</strong> Obviamente isto non é moi útil, xa que son só duplicados do mesmo material. Sería xenial se puidésemos preservar todas as anotacións que a xente fai nos libros, como os famosos \"garabatos nos marxes\" de Fermat. Pero, por desgraza, iso seguirá sendo un soño para os arquivistas."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obras”.</strong> Por exemplo, \"Harry Potter e a Cámara dos Segredos\" como un concepto lóxico, que abarca todas as súas versións, como diferentes traducións e reimpresións. Esta é unha definición algo útil, pero pode ser difícil trazar a liña do que conta. Por exemplo, probablemente queiramos preservar diferentes traducións, aínda que as reimpresións con só pequenas diferenzas poden non ser tan importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edicións”.</strong> Aquí contas cada versión única dun libro. Se algo é diferente, como unha portada diferente ou un prólogo diferente, conta como unha edición diferente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Arquivos.</strong> Cando se traballa con bibliotecas na sombra como Library Genesis, Sci-Hub ou Z-Library, hai unha consideración adicional. Pode haber múltiples escaneos da mesma edición. E a xente pode facer mellores versións dos arquivos existentes, escaneando o texto usando OCR ou rectificando páxinas que foron escaneadas en ángulo. Queremos contar estes arquivos como unha soa edición, o que requiriría un bo metadata ou deduplicación usando medidas de similitude de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "As “Edicións” parecen a definición máis práctica do que son os “libros”. Convenientemente, esta definición tamén se usa para asignar números ISBN únicos. Un ISBN, ou Número Internacional Normalizado do Libro, úsase comúnmente para o comercio internacional, xa que está integrado co sistema internacional de códigos de barras (\"Número Internacional de Artigo\"). Se queres vender un libro en tendas, necesita un código de barras, polo que obtés un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "A publicación do blog de Taycher menciona que, aínda que os ISBNs son útiles, non son universais, xa que só foron realmente adoptados a mediados dos anos setenta, e non en todo o mundo. Aínda así, o ISBN é probablemente o identificador máis amplamente usado das edicións de libros, polo que é o noso mellor punto de partida. Se podemos atopar todos os ISBNs do mundo, obtemos unha lista útil de que libros aínda necesitan ser preservados."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Entón, de onde obtemos os datos? Hai unha serie de esforzos existentes que están intentando compilar unha lista de todos os libros do mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Despois de todo, fixeron esta investigación para Google Books. Non obstante, o seu metadata non é accesible en masa e é bastante difícil de raspar."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como se mencionou antes, esta é a súa misión completa. Obtiveron enormes cantidades de datos de bibliotecas de bibliotecas cooperantes e arquivos nacionais, e continúan facéndoo. Tamén teñen bibliotecarios voluntarios e un equipo técnico que están intentando deduplicar rexistros e etiquetalos con todo tipo de metadata. O mellor de todo é que o seu conxunto de datos é completamente aberto. Podes simplemente <a %(openlibrary)s>descargalo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Este é un sitio web xestionado pola organización sen ánimo de lucro OCLC, que vende sistemas de xestión de bibliotecas. Agregan metadata de libros de moitas bibliotecas e fan que estea dispoñible a través do sitio web de WorldCat. Non obstante, tamén gañan diñeiro vendendo estes datos, polo que non están dispoñibles para descarga masiva. Teñen algúns conxuntos de datos masivos máis limitados dispoñibles para descarga, en cooperación con bibliotecas específicas."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este é o tema desta publicación do blog. ISBNdb raspa varios sitios web para obter metadata de libros, en particular datos de prezos, que logo venden a librerías, para que poidan prezo os seus libros de acordo co resto do mercado. Dado que os ISBNs son bastante universais hoxe en día, efectivamente construíron unha \"páxina web para cada libro\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Varios sistemas de bibliotecas individuais e arquivos.</strong> Hai bibliotecas e arquivos que non foron indexados e agregados por ningún dos anteriores, a miúdo porque están infradotados, ou por outras razóns non queren compartir os seus datos con Open Library, OCLC, Google, etc. Moitos destes teñen rexistros dixitais accesibles a través de internet, e a miúdo non están moi ben protexidos, polo que se queres axudar e divertirte aprendendo sobre sistemas de bibliotecas estraños, estes son grandes puntos de partida."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Nesta publicación, estamos felices de anunciar un pequeno lanzamento (en comparación cos nosos lanzamentos anteriores de Z-Library). Raspamos a maior parte de ISBNdb e fixemos que os datos estean dispoñibles para torrenting no sitio web do Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>; non o enlazaremos aquí directamente, só búscao). Estes son aproximadamente 30,9 millóns de rexistros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4,4GB comprimidos). No seu sitio web afirman que en realidade teñen 32,6 millóns de rexistros, polo que poderiamos ter perdido algúns, ou <em>eles</em> poderían estar facendo algo mal. En calquera caso, por agora non compartiremos exactamente como o fixemos: deixaremos iso como un exercicio para o lector. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "O que compartiremos é unha análise preliminar, para intentar achegarnos a estimar o número de libros no mundo. Observamos tres conxuntos de datos: este novo conxunto de datos de ISBNdb, o noso lanzamento orixinal de metadata que raspamos da biblioteca na sombra de Z-Library (que inclúe Library Genesis) e o volcado de datos de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Comecemos con algúns números aproximados:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "En Z-Library/Libgen e Open Library hai moitos máis libros que ISBNs únicos. Significa iso que moitos deses libros non teñen ISBNs, ou simplemente falta o metadata do ISBN? Probablemente poidamos responder a esta pregunta cunha combinación de coincidencia automatizada baseada noutros atributos (título, autor, editor, etc.), incorporando máis fontes de datos e extraendo ISBNs dos propios escaneos dos libros (no caso de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Cantos deses ISBNs son únicos? Isto ilústrase mellor cun diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Para ser máis precisos:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Sorprendeunos o pouco solapamento que hai! ISBNdb ten unha gran cantidade de ISBNs que non aparecen nin en Z-Library nin en Open Library, e o mesmo ocorre (aínda que nun grao menor pero aínda substancial) cos outros dous. Isto xera moitas novas preguntas. Canto axudaría a coincidencia automatizada a etiquetar os libros que non foron etiquetados con ISBNs? Haberá moitas coincidencias e, polo tanto, un aumento do solapamento? Ademais, que pasaría se engadísemos un cuarto ou quinto conxunto de datos? Canto solapamento veríamos entón?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Isto dános un punto de partida. Agora podemos ver todos os ISBNs que non estaban no conxunto de datos de Z-Library e que tampouco coinciden cos campos de título/autor. Iso pode darnos unha idea de como preservar todos os libros do mundo: primeiro rastrexando internet en busca de escaneos, logo saíndo na vida real para escanear libros. Este último incluso podería ser financiado colectivamente, ou impulsado por \"recompensas\" de persoas que queren ver determinados libros dixitalizados. Todo iso é unha historia para outro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se queres axudar con calquera destas tarefas — análise adicional; rastrexo de máis metadata; atopar máis libros; OCR de libros; facer isto para outros dominios (por exemplo, artigos, audiolibros, películas, programas de televisión, revistas) ou incluso facer dispoñibles algúns destes datos para cousas como adestramento de modelos de linguaxe grande — por favor, contacta comigo (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se estás especificamente interesado na análise de datos, estamos traballando para facer que os nosos conxuntos de datos e scripts estean dispoñibles nun formato máis fácil de usar. Sería xenial se puideses simplemente bifurcar un caderno e comezar a xogar con isto."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, se queres apoiar este traballo, considera facer unha doazón. Esta é unha operación totalmente dirixida por voluntarios, e a túa contribución marca unha gran diferenza. Cada pouco axuda. Por agora aceptamos doazóns en criptomoeda; consulta a páxina de Doazóns en O Arquivo de Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Para algunha definición razoable de \"para sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Por suposto, o patrimonio escrito da humanidade é moito máis que libros, especialmente hoxe en día. Para o propósito desta publicación e os nosos lanzamentos recentes estamos centrados nos libros, pero os nosos intereses van máis alá."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Hai moito máis que se pode dicir sobre Aaron Swartz, pero só queriamos mencionarlle brevemente, xa que xoga un papel crucial nesta historia. Co paso do tempo, máis persoas poderían atopar o seu nome por primeira vez, e posteriormente mergullarse no burato do coello por si mesmas."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "A xanela crítica das bibliotecas de sombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Como podemos afirmar que preservamos as nosas coleccións para sempre, cando xa están a achegarse a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versión en chinés 中文版</a>, discute en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "En O Arquivo de Anna, a miúdo pregúntannos como podemos afirmar que preservamos as nosas coleccións para sempre, cando o tamaño total xa está a achegarse a 1 Petabyte (1000 TB), e aínda está a medrar. Neste artigo veremos a nosa filosofía, e veremos por que a próxima década é crítica para a nosa misión de preservar o coñecemento e a cultura da humanidade."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "O <a %(annas_archive_stats)s>tamaño total</a> das nosas coleccións, nos últimos meses, desglosado polo número de seeders de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioridades"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Por que nos importan tanto os artigos e os libros? Deixemos de lado a nosa crenza fundamental na preservación en xeral — poderiamos escribir outra publicación sobre iso. Entón, por que artigos e libros especificamente? A resposta é sinxela: <strong>densidade de información</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabyte de almacenamento, o texto escrito almacena a maior cantidade de información de todos os medios. Aínda que nos importan tanto o coñecemento como a cultura, preocúpanos máis o primeiro. En xeral, atopamos unha xerarquía de densidade de información e importancia da preservación que se parece aproximadamente a isto:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Artigos académicos, revistas, informes"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Datos orgánicos como secuencias de ADN, sementes de plantas ou mostras microbianas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libros de non ficción"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Código de software de ciencia e enxeñaría"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Datos de medición como medicións científicas, datos económicos, informes corporativos"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sitios web de ciencia e enxeñaría, discusións en liña"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistas de non ficción, xornais, manuais"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcricións de non ficción de charlas, documentais, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Datos internos de corporacións ou gobernos (filtracións)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Rexistros de metadata en xeral (de non ficción e ficción; doutros medios, arte, persoas, etc.; incluíndo reseñas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Datos xeográficos (por exemplo, mapas, estudos xeolóxicos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcricións de procedementos legais ou xudiciais"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versións ficticias ou de entretemento de todo o anterior"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "A clasificación nesta lista é algo arbitraria: varios elementos están empatados ou hai desacordos dentro do noso equipo, e probablemente estamos esquecendo algunhas categorías importantes. Pero isto é aproximadamente como priorizamos."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Algúns destes elementos son demasiado diferentes dos outros para que nos preocupemos (ou xa están atendidos por outras institucións), como os datos orgánicos ou os datos xeográficos. Pero a maioría dos elementos desta lista son realmente importantes para nós."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Outro gran factor na nosa priorización é o risco que corre unha determinada obra. Preferimos centrarnos en obras que son:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Raras"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Únicamente desatendidas"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Únicamente en risco de destrución (por exemplo, por guerra, recortes de financiamento, demandas ou persecución política)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, preocúpanos a escala. Temos tempo e diñeiro limitados, polo que preferimos pasar un mes salvando 10.000 libros que 1.000 libros, se son aproximadamente igual de valiosos e están en risco."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliotecas sombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Hai moitas organizacións que teñen misións similares e prioridades similares. De feito, hai bibliotecas, arquivos, laboratorios, museos e outras institucións encargadas da preservación deste tipo. Moitas delas están ben financiadas por gobernos, individuos ou corporacións. Pero teñen un enorme punto cego: o sistema legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Aquí reside o papel único das bibliotecas sombra e a razón pola que existe o Arquivo de Anna. Podemos facer cousas que outras institucións non están permitidas facer. Agora, non é (a miúdo) que poidamos arquivar materiais que son ilegais de preservar noutros lugares. Non, é legal en moitos lugares construír un arquivo con calquera libro, papel, revista, e así por diante."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Pero o que a miúdo lles falta aos arquivos legais é <strong>redundancia e lonxevidade</strong>. Existen libros dos que só hai unha copia nunha biblioteca física nalgún lugar. Existen rexistros de metadata gardados por unha única corporación. Existen xornais só preservados en microfilme nun único arquivo. As bibliotecas poden sufrir recortes de financiamento, as corporacións poden quebrar, os arquivos poden ser bombardeados e queimados ata os cimentos. Isto non é hipotético: isto sucede todo o tempo."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "O que podemos facer de forma única en O Arquivo de Anna é almacenar moitas copias de obras, a gran escala. Podemos recoller artigos, libros, revistas e máis, e distribuílos en masa. Actualmente facémolo a través de torrents, pero as tecnoloxías exactas non importan e cambiarán co tempo. A parte importante é conseguir que moitas copias se distribúan por todo o mundo. Esta cita de hai máis de 200 anos aínda resoa:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>O perdido non se pode recuperar; pero salvemos o que queda: non por cámaras e peches que os afastan da vista e uso do público, consignándoos ao desperdicio do tempo, senón por unha multiplicación de copias, que os coloque fóra do alcance do accidente.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Unha nota rápida sobre o dominio público. Dado que O Arquivo de Anna se centra de forma única en actividades que son ilegais en moitos lugares do mundo, non nos molestamos con coleccións amplamente dispoñibles, como os libros de dominio público. As entidades legais a miúdo xa coidan ben diso. Non obstante, hai consideracións que nos fan traballar ás veces en coleccións dispoñibles publicamente:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Os rexistros de metadata pódense ver libremente no sitio web de Worldcat, pero non se poden descargar en masa (ata que os <a %(worldcat_scrape)s>raspamos</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "O código pode ser de código aberto en Github, pero Github no seu conxunto non se pode espellar facilmente e, polo tanto, preservar (aínda que neste caso particular hai copias suficientemente distribuídas da maioría dos repositorios de código)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit é gratuíto de usar, pero recentemente puxo en marcha medidas anti-raspado estritas, á luz do adestramento de LLM famento de datos (máis sobre iso máis adiante)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Unha multiplicación de copias"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Volvendo á nosa pregunta orixinal: como podemos afirmar que preservamos as nosas coleccións para sempre? O principal problema aquí é que a nosa colección estivo <a %(torrents_stats)s>crecendo</a> a un ritmo rápido, raspando e abrindo algunhas coleccións masivas (ademais do traballo incrible xa feito por outras bibliotecas de datos abertos como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Este crecemento de datos fai que sexa máis difícil que as coleccións se espellen por todo o mundo. O almacenamento de datos é caro! Pero somos optimistas, especialmente ao observar as seguintes tres tendencias."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Collemos o froito máis baixo"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Este segue directamente das nosas prioridades discutidas anteriormente. Preferimos traballar en liberar grandes coleccións primeiro. Agora que aseguramos algunhas das coleccións máis grandes do mundo, esperamos que o noso crecemento sexa moito máis lento."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Aínda hai unha longa cola de coleccións máis pequenas, e novos libros escanéanse ou publícanse todos os días, pero a taxa probablemente será moito máis lenta. Aínda podemos duplicar ou incluso triplicar o tamaño, pero nun período de tempo máis longo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Os custos de almacenamento seguen caendo exponencialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "No momento de escribir isto, os <a %(diskprices)s>prezos dos discos</a> por TB están ao redor de $12 para discos novos, $8 para discos usados e $4 para cinta. Se somos conservadores e só miramos discos novos, iso significa que almacenar un petabyte custa uns $12,000. Se asumimos que a nosa biblioteca triplicará de 900TB a 2.7PB, iso significaría $32,400 para espellar toda a nosa biblioteca. Engadindo electricidade, custo doutro hardware, e así por diante, redondeamos a $40,000. Ou con cinta máis como $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Por unha banda <strong>$15,000–$40,000 pola suma de todo o coñecemento humano é unha ganga</strong>. Por outra banda, é un pouco elevado esperar toneladas de copias completas, especialmente se tamén queremos que esas persoas sigan sementando os seus torrents para o beneficio doutros."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Iso é hoxe. Pero o progreso avanza:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Os custos dos discos duros por TB reducíronse aproximadamente a un terzo nos últimos 10 anos, e probablemente seguirán caendo a un ritmo similar. A cinta parece estar nunha traxectoria similar. Os prezos dos SSD están caendo aínda máis rápido, e poderían superar os prezos dos HDD a finais da década."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendencias de prezos de HDD de diferentes fontes (fai clic para ver o estudo)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se isto se mantén, entón en 10 anos poderiamos estar mirando só $5,000–$13,000 para espellar toda a nosa colección (1/3), ou incluso menos se crecemos menos en tamaño. Aínda que segue sendo moito diñeiro, isto será alcanzable para moitas persoas. E podería ser aínda mellor debido ao seguinte punto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Melloras na densidade da información"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Actualmente almacenamos libros nos formatos orixinais nos que se nos entregan. Claro, están comprimidos, pero a miúdo aínda son grandes escaneos ou fotografías de páxinas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Ata agora, as únicas opcións para reducir o tamaño total da nosa colección foron a través dunha compresión máis agresiva ou a deduplicación. Non obstante, para obter aforros significativos, ambas son demasiado perdas para o noso gusto. A compresión pesada de fotos pode facer que o texto sexa apenas lexible. E a deduplicación require unha alta confianza de que os libros sexan exactamente iguais, o que a miúdo é demasiado inexacto, especialmente se os contidos son os mesmos pero os escaneos se fan en ocasións diferentes."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Sempre houbo unha terceira opción, pero a súa calidade foi tan abismal que nunca a consideramos: <strong>OCR, ou recoñecemento óptico de caracteres</strong>. Este é o proceso de converter fotos en texto simple, usando IA para detectar os caracteres nas fotos. As ferramentas para isto existen desde hai tempo e son bastante decentes, pero \"bastante decentes\" non é suficiente para fins de preservación."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Non obstante, os recentes modelos de aprendizaxe profunda multimodal fixeron un progreso extremadamente rápido, aínda que a custos elevados. Agardamos que tanto a precisión como os custos melloren dramaticamente nos próximos anos, ata o punto de que será realista aplicalo a toda a nosa biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Melloras no OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Cando iso suceda, probablemente aínda preservaremos os ficheiros orixinais, pero ademais poderiamos ter unha versión moito máis pequena da nosa biblioteca que a maioría da xente quererá espellar. O truco é que o texto en bruto en si mesmo comprímese aínda mellor e é moito máis doado de deduplicar, dándonos aínda máis aforros."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "En xeral, non é irrealista esperar polo menos unha redución de 5-10 veces no tamaño total dos ficheiros, quizais incluso máis. Mesmo cunha redución conservadora de 5 veces, estaríamos mirando <strong>1.000–3.000 dólares en 10 anos, mesmo se a nosa biblioteca triplica o seu tamaño</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Xanela crítica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se estas previsións son precisas, <strong>só necesitamos esperar un par de anos</strong> antes de que toda a nosa colección sexa amplamente espellada. Así, nas palabras de Thomas Jefferson, \"colocada fóra do alcance do accidente\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Desafortunadamente, a chegada dos LLMs, e o seu adestramento famento de datos, puxo a moitos titulares de dereitos de autor á defensiva. Aínda máis do que xa estaban. Moitos sitios web están facendo máis difícil raspar e arquivar, as demandas están voando, e mentres tanto as bibliotecas físicas e os arquivos seguen sendo descoidados."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Só podemos esperar que estas tendencias continúen empeorando, e moitas obras se perdan moito antes de que entren no dominio público."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Estamos á véspera dunha revolución na preservación, pero <q>o perdido non se pode recuperar.</q></strong> Temos unha xanela crítica de aproximadamente 5-10 anos durante a cal aínda é bastante caro operar unha biblioteca na sombra e crear moitos espellos ao redor do mundo, e durante a cal o acceso aínda non foi completamente pechado."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se podemos cruzar esta xanela, entón realmente teremos preservado o coñecemento e a cultura da humanidade para sempre. Non debemos deixar que este tempo se desperdicie. Non debemos deixar que esta xanela crítica se peche sobre nós."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Imos aló."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Acceso exclusivo para empresas de LLM á maior colección de libros de non ficción chineses do mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versión chinesa 中文版</a>, <a %(news_ycombinator)s>Discutir en Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> O Arquivo de Anna adquiriu unha colección única de 7,5 millóns / 350TB de libros de non ficción chineses, máis grande que Library Genesis. Estamos dispostos a dar a unha empresa de LLM acceso exclusivo, a cambio de OCR de alta calidade e extracción de texto.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Este é un breve post no blog. Estamos buscando algunha empresa ou institución que nos axude co OCR e a extracción de texto para unha colección masiva que adquirimos, a cambio de acceso exclusivo anticipado. Despois do período de embargo, por suposto, liberaremos toda a colección."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Os textos académicos de alta calidade son extremadamente útiles para o adestramento de LLMs. Aínda que a nosa colección é chinesa, isto debería ser útil incluso para adestrar LLMs en inglés: os modelos parecen codificar conceptos e coñecementos independentemente do idioma de orixe."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Para isto, o texto necesita ser extraído das escaneos. Que obtén o Arquivo de Anna disto? Busca de texto completo dos libros para os seus usuarios."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Porque os nosos obxectivos se aliñan cos dos desenvolvedores de LLM, estamos buscando un colaborador. Estamos dispostos a darche <strong>acceso anticipado exclusivo a esta colección en masa durante 1 ano</strong>, se podes facer un OCR e extracción de texto adecuados. Se estás disposto a compartir todo o código da túa cadea de procesamento connosco, estaríamos dispostos a embargar a colección por máis tempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Páxinas de exemplo"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Para demostrarnos que tes unha boa cadea de procesamento, aquí tes algunhas páxinas de exemplo para comezar, dun libro sobre superconductores. A túa cadea de procesamento debería manexar correctamente matemáticas, táboas, gráficos, notas ao pé, etc."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envía as túas páxinas procesadas ao noso correo electrónico. Se teñen boa pinta, enviarémosche máis en privado, e esperamos que poidas executar rapidamente a túa cadea de procesamento sobre esas tamén. Unha vez que esteamos satisfeitos, podemos facer un trato."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Colección"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Algúns detalles máis sobre a colección. <a %(duxiu)s>Duxiu</a> é unha base de datos masiva de libros escaneados, creada polo <a %(chaoxing)s>SuperStar Digital Library Group</a>. A maioría son libros académicos, escaneados para facelos dispoñibles dixitalmente para universidades e bibliotecas. Para o noso público de fala inglesa, <a %(library_princeton)s>Princeton</a> e a <a %(guides_lib_uw)s>Universidade de Washington</a> teñen boas visións xerais. Tamén hai un excelente artigo que ofrece máis contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (búscao no Arquivo de Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Os libros de Duxiu levan moito tempo sendo pirateados na internet chinesa. Normalmente véndense por menos dun dólar por revendedores. Xeralmente distribúense usando o equivalente chinés de Google Drive, que a miúdo foi hackeado para permitir máis espazo de almacenamento. Algúns detalles técnicos pódense atopar <a %(github_duty_machine)s>aquí</a> e <a %(github_821_github_io)s>aquí</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Aínda que os libros foron distribuídos semi-publicamente, é bastante difícil obtelos en masa. Tiñamos isto alto na nosa lista de tarefas, e asignamos varios meses de traballo a tempo completo para iso. Non obstante, recentemente un voluntario incrible, asombroso e talentoso contactou connosco, dicíndonos que xa fixera todo este traballo — a gran custo. Compartiron a colección completa connosco, sen esperar nada a cambio, excepto a garantía de preservación a longo prazo. Verdadeiramente notable. Acordaron pedir axuda deste xeito para obter a colección OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "A colección consta de 7.543.702 arquivos. Isto é máis que a non-ficción de Library Genesis (aproximadamente 5,3 millóns). O tamaño total dos arquivos é de aproximadamente 359TB (326TiB) na súa forma actual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Estamos abertos a outras propostas e ideas. Só contacta connosco. Consulta o Arquivo de Anna para máis información sobre as nosas coleccións, esforzos de preservación, e como podes axudar. Grazas!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Advertencia: esta publicación no blog foi obsoleta. Decidimos que IPFS aínda non está listo para o horario de máxima audiencia. Aínda ligaremos a arquivos en IPFS desde o Arquivo de Anna cando sexa posible, pero non o aloxaremos nós mesmos máis, nin recomendamos a outros que espellen usando IPFS. Consulta a nosa páxina de Torrents se queres axudar a preservar a nosa colección."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Axuda a sementar Z-Library en IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Como operar unha biblioteca na sombra: operacións no Arquivo de Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Non hai <q>AWS para caridades na sombra,</q> entón como operamos o Arquivo de Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Eu opero <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, o maior motor de busca de código aberto sen ánimo de lucro do mundo para <a %(wikipedia_shadow_library)s>bibliotecas na sombra</a>, como Sci-Hub, Library Genesis e Z-Library. O noso obxectivo é facer que o coñecemento e a cultura sexan facilmente accesibles, e finalmente construír unha comunidade de persoas que xuntas arquiven e preserven <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos os libros do mundo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Neste artigo amosarei como operamos este sitio web, e os desafíos únicos que veñen con operar un sitio web cun estado legal cuestionable, xa que non hai “AWS para caridades na sombra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Consulta tamén o artigo irmán <a %(blog_how_to_become_a_pirate_archivist)s>Como converterse nun arquivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokens de innovación"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Comecemos coa nosa pila tecnolóxica. É deliberadamente aburrida. Usamos Flask, MariaDB e ElasticSearch. E iso é literalmente todo. A busca é en gran medida un problema resolto, e non pretendemos reinventalo. Ademais, temos que gastar os nosos <a %(mcfunley)s>tokens de innovación</a> noutra cousa: non ser derrubados polas autoridades."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Entón, que tan legal ou ilegal é exactamente o Arquivo de Anna? Isto depende principalmente da xurisdición legal. A maioría dos países cren nalgunha forma de copyright, o que significa que ás persoas ou empresas se lles asigna un monopolio exclusivo sobre certos tipos de obras durante un período de tempo determinado. Como nota á marxe, no Arquivo de Anna cremos que, aínda que hai algúns beneficios, en xeral o copyright é un neto negativo para a sociedade, pero esa é unha historia para outro momento."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Este monopolio exclusivo sobre certas obras significa que é ilegal para calquera fóra deste monopolio distribuír directamente esas obras, incluíndonos a nós. Pero o Arquivo de Anna é un motor de busca que non distribúe directamente esas obras (polo menos non no noso sitio web en clearnet), así que deberiamos estar ben, non? Non exactamente. En moitas xurisdicións non só é ilegal distribuír obras con copyright, senón tamén enlazar a lugares que o fan. Un exemplo clásico disto é a lei DMCA dos Estados Unidos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Ese é o extremo máis estrito do espectro. No outro extremo do espectro podería teoricamente haber países sen leis de copyright, pero estes realmente non existen. Prácticamente todos os países teñen algunha forma de lei de copyright nos seus libros. A aplicación é unha historia diferente. Hai moitos países con gobernos que non se preocupan por facer cumprir a lei de copyright. Tamén hai países entre os dous extremos, que prohiben distribuír obras con copyright, pero non prohiben enlazar a tales obras."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Outra consideración é a nivel de empresa. Se unha empresa opera nunha xurisdición que non se preocupa polo copyright, pero a propia empresa non está disposta a asumir ningún risco, entón poderían pechar o seu sitio web tan pronto como alguén se queixe del."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, unha gran consideración son os pagos. Como necesitamos permanecer anónimos, non podemos usar métodos de pago tradicionais. Isto déixanos coas criptomoedas, e só un pequeno subconxunto de empresas as soporta (hai tarxetas de débito virtuais pagadas con cripto, pero a miúdo non son aceptadas)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arquitectura do sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Entón, digamos que atopaches algunhas empresas dispostas a aloxar o teu sitio web sen derrubalo: chamémoslles “provedores amantes da liberdade” 😄. Pronto descubrirás que aloxar todo con eles é bastante caro, polo que quizais queiras atopar algúns “provedores baratos” e facer o aloxamento real alí, proxectando a través dos provedores amantes da liberdade. Se o fas ben, os provedores baratos nunca saberán o que estás aloxando e nunca recibirán queixas."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con todos estes provedores hai o risco de que te derruben de todos os xeitos, polo que tamén necesitas redundancia. Necesitamos isto en todos os niveis da nosa pila."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Unha empresa algo amante da liberdade que se puxo nunha posición interesante é Cloudflare. Eles <a %(blog_cloudflare)s>argumentaron</a> que non son un provedor de aloxamento, senón unha utilidade, como un ISP. Polo tanto, non están suxeitos a solicitudes de retirada de DMCA ou outras, e redirixen calquera solicitude ao teu provedor de aloxamento real. Chegaron ao punto de ir a xuízo para protexer esta estrutura. Polo tanto, podemos usalos como outra capa de caché e protección."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare non acepta pagos anónimos, polo que só podemos usar o seu plan gratuíto. Isto significa que non podemos usar as súas funcións de balanceo de carga ou failover. Polo tanto, <a %(annas_archive_l255)s>implementamos isto nós mesmos</a> a nivel de dominio. Ao cargar a páxina, o navegador comprobará se o dominio actual aínda está dispoñible, e se non, reescribe todas as URL a un dominio diferente. Dado que Cloudflare almacena en caché moitas páxinas, isto significa que un usuario pode aterrar no noso dominio principal, mesmo se o servidor proxy está caído, e logo no seguinte clic ser trasladado a outro dominio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Aínda temos tamén preocupacións operativas normais coas que tratar, como monitorizar a saúde do servidor, rexistrar erros de backend e frontend, e así por diante. A nosa arquitectura de failover permite unha maior robustez neste aspecto tamén, por exemplo, executando un conxunto completamente diferente de servidores nun dos dominios. Incluso podemos executar versións máis antigas do código e datasets neste dominio separado, no caso de que un erro crítico na versión principal pase desapercibido."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Tamén podemos protexernos contra Cloudflare volvéndose contra nós, eliminándoo dun dos dominios, como este dominio separado. Diferentes permutacións destas ideas son posibles."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ferramentas"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vexamos que ferramentas usamos para lograr todo isto. Isto está evolucionando moito a medida que nos atopamos con novos problemas e atopamos novas solucións."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidor de aplicacións: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Xestión de servidores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Desenvolvemento: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Aloxamento estático de Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Hai algunhas decisións nas que fomos e viñemos. Unha é a comunicación entre servidores: antes usabamos Wireguard para isto, pero descubrimos que ocasionalmente deixa de transmitir calquera dato, ou só transmite datos nunha dirección. Isto ocorreu con varias configuracións diferentes de Wireguard que probamos, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Tamén intentamos tunelar portos sobre SSH, usando autossh e sshuttle, pero atopamos <a %(github_sshuttle)s>problemas alí</a> (aínda que aínda non está claro para min se autossh sofre de problemas de TCP-sobre-TCP ou non — só me parece unha solución chapuceira pero quizais estea ben?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "En vez diso, volvemos ás conexións directas entre servidores, ocultando que un servidor está funcionando nos provedores baratos usando filtrado de IP con UFW. Isto ten o inconveniente de que Docker non funciona ben con UFW, a menos que uses <code>network_mode: \"host\"</code>. Todo isto é un pouco máis propenso a erros, porque exporás o teu servidor a internet cunha pequena mala configuración. Quizais deberiamos volver a autossh — os comentarios serían moi benvidos aquí."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Tamén fomos e viñemos entre Varnish e Nginx. Actualmente gústanos Varnish, pero ten as súas peculiaridades e arestas. O mesmo aplícase a Checkmk: non nos encanta, pero funciona por agora. Weblate estivo ben pero non incrible — ás veces temo que perda os meus datos cada vez que intento sincronizalo co noso repositorio git. Flask foi bo en xeral, pero ten algunhas peculiaridades estrañas que custaron moito tempo para depurar, como configurar dominios personalizados, ou problemas coa súa integración de SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Ata agora os outros ferramentas foron xeniais: non temos queixas serias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Todos estes tiveron algúns problemas, pero nada demasiado serio ou que consuma moito tempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Foi unha experiencia interesante aprender a configurar un motor de busca de biblioteca na sombra robusto e resiliente. Hai moitos máis detalles para compartir en publicacións posteriores, así que avísame sobre o que che gustaría aprender máis!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como sempre, estamos buscando doazóns para apoiar este traballo, así que asegúrate de visitar a páxina de Doazóns no Arquivo de Anna. Tamén estamos buscando outros tipos de apoio, como subvencións, patrocinadores a longo prazo, provedores de pago de alto risco, quizais incluso anuncios (de bo gusto!). E se queres contribuír co teu tempo e habilidades, sempre estamos buscando desenvolvedores, tradutores, etc. Grazas polo teu interese e apoio."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e o equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Ola, son Anna. Creei <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, a maior biblioteca na sombra do mundo. Este é o meu blog persoal, no que eu e os meus compañeiros escribimos sobre piratería, preservación dixital e máis."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conéctate comigo en <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Ten en conta que este sitio web é só un blog. Só aloxamos as nosas propias palabras aquí. Non se aloxan nin se enlazan torrents ou outros ficheiros con dereitos de autor aquí."

#, fuzzy
msgid "blog.index.heading"
msgstr "Publicacións do blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "Raspado de 1.3B de WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Poñendo 5,998,794 libros en IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Advertencia: esta publicación no blog foi obsoleta. Decidimos que IPFS aínda non está listo para o horario de máxima audiencia. Aínda ligaremos a arquivos en IPFS desde o Arquivo de Anna cando sexa posible, pero non o aloxaremos nós mesmos máis, nin recomendamos a outros que espellen usando IPFS. Consulta a nosa páxina de Torrents se queres axudar a preservar a nosa colección."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> O Arquivo de Anna raspou todo WorldCat (a maior colección de metadata de bibliotecas do mundo) para facer unha lista de TODO de libros que necesitan ser preservados.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Hai un ano, <a %(blog)s>comezamos</a> a responder esta pregunta: <strong>Que porcentaxe de libros foron preservados permanentemente por bibliotecas na sombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Unha vez que un libro entra nunha biblioteca na sombra de datos abertos como <a %(wikipedia_library_genesis)s>Library Genesis</a>, e agora <a %(wikipedia_annas_archive)s>o Arquivo de Anna</a>, é espellado por todo o mundo (a través de torrents), preservándoo practicamente para sempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Para responder á pregunta de que porcentaxe de libros foi preservada, necesitamos saber o denominador: cantos libros existen en total? E idealmente non só temos un número, senón metadata real. Entón non só podemos comparalos coas bibliotecas na sombra, senón tamén <strong>crear unha lista de TODO dos libros restantes para preservar!</strong> Incluso poderiamos comezar a soñar cun esforzo de colaboración para percorrer esta lista de TODO."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Raspamos <a %(wikipedia_isbndb_com)s>ISBNdb</a> e descargamos o <a %(openlibrary)s>conxunto de datos de Open Library</a>, pero os resultados non foron satisfactorios. O principal problema foi que non había moita superposición de ISBNs. Vexa este diagrama de Venn do <a %(blog)s>nosso post no blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ficamos moi sorprendidos pola pouca superposición que había entre ISBNdb e Open Library, ambos os cales inclúen datos de varias fontes, como raspados web e rexistros de bibliotecas. Se ambos fan un bo traballo atopando a maioría dos ISBNs, os seus círculos seguramente terían unha superposición substancial, ou un sería un subconxunto do outro. Preguntámonos, cantos libros caen <em>completamente fóra destes círculos</em>? Necesitamos unha base de datos máis grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Foi entón cando fixamos a nosa atención na maior base de datos de libros do mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta é unha base de datos propietaria da organización sen ánimo de lucro <a %(wikipedia_oclc)s>OCLC</a>, que agrega rexistros de metadata de bibliotecas de todo o mundo, a cambio de darlles acceso ao conxunto de datos completo e facer que aparezan nos resultados de busca dos usuarios finais."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Aínda que OCLC é unha organización sen ánimo de lucro, o seu modelo de negocio require protexer a súa base de datos. Ben, lamentamos dicir, amigos de OCLC, que o imos dar todo. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Durante o último ano, raspamos meticulosamente todos os rexistros de WorldCat. Ao principio, tivemos un golpe de sorte. WorldCat estaba a lanzar o seu redeseño completo do sitio web (en agosto de 2022). Isto incluíu unha revisión substancial dos seus sistemas de backend, introducindo moitas fallas de seguridade. Aproveitamos inmediatamente a oportunidade e puidemos raspar centos de millóns (!) de rexistros en poucos días."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redeseño de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Despois diso, as fallas de seguridade foron corrixidas lentamente unha por unha, ata que a última que atopamos foi parcheada hai aproximadamente un mes. Para entón, tiñamos practicamente todos os rexistros e só buscabamos rexistros de calidade lixeiramente superior. Así que sentimos que é hora de liberar!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Imos ver algunha información básica sobre os datos:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contedores de Arquivo de Anna (AAC)</a>, que son esencialmente <a %(jsonlines)s>Liñas JSON</a> comprimidas con <a %(zstd)s>Zstandard</a>, máis algúns semánticos estandarizados. Estes contedores envolven varios tipos de rexistros, baseados nos diferentes raspados que despregamos."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Datos"

msgid "dyn.buy_membership.error.unknown"
msgstr "Produciuse un erro descoñecido. Póñase en contacto con nós en %(email)s con unha captura de pantalla."

msgid "dyn.buy_membership.error.minimum"
msgstr "Esta moeda precisa dun importe mínimo superior ao habitual. Seleccione outra duración ou outra moeda."

msgid "dyn.buy_membership.error.try_again"
msgstr "Non se puido completar a solicitude. Por favor volva a intentalo nuns minutos, e se segue ocurrindo, póñase en contacto con nós en %(email)s con unha captura de pantalla."

msgid "dyn.buy_membership.error.wait"
msgstr "Erro no procesamento do pago. Espere un momento e inténteo de novo. Se o problema persiste por máis de 24 horas, póñase en contacto con nós en %(email)s con unha captura de pantalla."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentario oculto"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema co ficheiro: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versión mellorada"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Quere denunciar a este usuario por comportamento abusivo ou inapropiado?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Denunciar abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso denunciado:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Denunciou a este usuario por abuso."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responder"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Por favor, <a %(a_login)s>inicia sesión</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Deixaches un comentario. Pode tardar un minuto en amosarse."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Algo saíu mal. Por favor, recarga a páxina e inténtao de novo."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s páxinas afectadas"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visible en Libgen.rs. Non ficción"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visible en Libgen.rs Ficción"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visible en Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcado como roto en Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Falta en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcado como “spam” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcado como “arquivo malo” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Non todas as páxinas se puideron converter a PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "A execución de exiftool fallou neste ficheiro"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (descoñecido)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libro (Non ficción)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libro (Ficción)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artículo de revista"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documento normativo"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Cómic"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musical"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibro"

msgid "common.md5_content_type_mapping.other"
msgstr "Outros"

msgid "common.access_types_mapping.aa_download"
msgstr "Descarga do Partner Server"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Descarga externa"

msgid "common.access_types_mapping.external_borrow"
msgstr "Préstamo externo"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Préstamo externo (impresión desactivada)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Explorar metadata"

msgid "common.access_types_mapping.torrents_available"
msgstr "Contido en torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinesa"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Subidas a AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadatos checos"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Estatal Rusa"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Título"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Editorial"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edición"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Ano de publicación"

msgid "common.specific_search_fields.original_filename"
msgstr "Nome doo arquivo orixinal"

msgid "common.specific_search_fields.description_comments"
msgstr "Comentarios sobre a descripción e os metadatos"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "As descargas do Servidor Partner non están dispoñibles temporalmente para este arquivo."

msgid "common.md5.servers.fast_partner"
msgstr "Servidor Fast Partner #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomendado)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sen verificación do navegador nin listas de espera)"

msgid "common.md5.servers.slow_partner"
msgstr "Servidor Partner lento #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(lixeiramente máis rápido pero con lista de espera)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sen lista de espera, pero pode ser moi lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non Ficción"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficción"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(pulse tamén \"GET\" na parte superior)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(pulse \"GET\" na parte superior)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "os seus anuncios son coñecidos por conter software malicioso, así que usa un bloqueador de anuncios ou non fagas clic nos anuncios"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Os ficheiros Nexus/STC poden ser pouco fiables para descargar)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library en Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requiere o Navegador Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Tomar prestado de Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(únicamente para usuarios con problemas de impresión)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(O DOI asociado pode non estar dispoñible en Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "colección"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Descargas masivas de torrents"

msgid "page.md5.box.download.experts_only"
msgstr "(só expertos)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Buscar ISBN no Arquivo de Anna"

msgid "page.md5.box.download.other_isbn"
msgstr "Buscar ISBN noutras bases de datos"

msgid "page.md5.box.download.original_isbndb"
msgstr "Atopa o rexistro orixinal no ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Buscar en Anna's Arquive polo Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Atopa o rexistro orixinal na Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Buscar en Anna's Archive o número OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Atopa o rexistro orixinal en WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Busca en Anna's Archive o número SSID de DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Busca manualmente en DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Busca en Anna's Archive o número CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Atopa o rexistro orixinal en CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Busca en Anna's Archive o número DuXiu DXID"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(non é necesario verificar o navegador)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadatos checos %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadatos"

msgid "page.md5.box.descr_title"
msgstr "descripción"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nome de ficheiro alternativo"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Título alternativo"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autor alternativo"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editor alternativo"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edición alternativa"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extensión alternativa"

msgid "page.md5.box.metadata_comments_title"
msgstr "comentarios sobre metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Descrición alternativa"

msgid "page.md5.box.date_open_sourced_title"
msgstr "data de lanzamento en Anna's Archive"

msgid "page.md5.header.scihub"
msgstr "Arquivo Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Arquivo de préstamo dixital controlado de Internet Archive \"%(id)s\""

msgid "page.md5.header.ia_desc"
msgstr "Esto é un rexistro dun arquivo do Internet Archive, non un arquivo descargable directamente. Podes intentar tomar prestado o libro (link abaixo),ou empregar ésta URL cando <a %(a_request)s>solicites un arquivo</a>."

msgid "page.md5.header.consider_upload"
msgstr "Se tes éste arquivo e nón está dispoñible en Anna's Archive todavía, considera <a %(a_request)s>subilo</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Rexistro de metadata da ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Rexistro de metadata da Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Rexistro da metadata do número OCLC (WorldCat) %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Rexistro da metadata de DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Rexistro da metadata de CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Rexistro de metadatos de MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Rexistro de metadatos de Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Esto é un rexistro de metadata, non un arquivo descargable. Podes empregar ésta URL cando <a %(a_request)s>solicites un arquivo</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadatos do rexistro enlazado"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Mellorar os metadatos en Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Aviso: múltiples rexistros enlazados:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Mellorar metadatos"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Informar sobre a calidade do ficheiro"

msgid "page.search.results.download_time"
msgstr "Tempo de descargar"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Sitio Web:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Buscar \"%(name)s\" en Anna's Archive"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorador de Códigos:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Ver en Explorador de Códigos “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Lee máis…"

msgid "page.md5.tabs.downloads"
msgstr "Descargas (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Préstamo (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Explorar metadatos (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentarios (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listas (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Estadísticas (%(count)s)"

msgid "common.tech_details"
msgstr "Detalles técnicos"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Este arquivo pode ter problemas e foi ocultado dunha biblioteca de fontes.</span> Ás veces isto débese a unha petición do titular de dereitos de autor, outras veces é porque hai unha alternativa mellor dispoñible, pero ás veces débese a un problema co propio arquivo. É posible que aínda poida descargarse, pero recomendámoslle que primeiro procure un arquivo alternativo. Máis información:"

msgid "page.md5.box.download.better_file"
msgstr "Unha versión mellor deste arquivo pode estar dispoñible en %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Se aínda así desexa descargar o arquivo, asegúrese de empregar únicamente software actualizado e de confianza para abrilo."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Descargas rápidas"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Descargas rápidas</strong> Fágase <a %(a_membership)s>socio</a> para apoiar a conservación a longo prazo de libros, documentos, e moito máis. Como mostra da nosa gratitude, obterá descargas rápidas. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se doas este mes, obterás <strong>o dobre</strong> de descargas rápidas."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Descargas rápidas</strong> Quédanlle %(remaining)s descargas hoxe . Grazas por ser un socio ! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Descargas rápidas</strong> Quedaches sen descargas rápidas por hoxe."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Descargas rápidas</strong> Vostede descargou este arquivo recentemente. Os enlaces seguen sendo válidos durante un tempo."

msgid "page.md5.box.download.option"
msgstr "Opción #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(sen redirección)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(abrir no visor)"

msgid "layout.index.header.banner.refer"
msgstr "Recomenda a un amigo, tanto ti como o teu amigo recibiredes %(percentage)s%% de bonificación en descargas rápidas !"

msgid "layout.index.header.learn_more"
msgstr "Ler máis…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Descargas lentas"

msgid "page.md5.box.download.trusted_partners"
msgstr "De partners de confianza."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Máis información no <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(pode precisar <a %(a_browser)s>verificación do navegador</a> — ¡ Descargas ilimitadas !)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Despois de descargar:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Abrir no noso visor"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostrar descargas externas"

msgid "page.md5.box.download.header_external"
msgstr "Descargas externas"

msgid "page.md5.box.download.no_found"
msgstr "Non se atoparon descargas."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Todas as opcións de descarga teñen o mesmo arquivo e deberían ser seguras. Dito isto, ten sempre coidado ao descargar arquivos de Internet, especialmente de sitios externos a Anna's Archive. Por exemplo, asegúrate de manter actualizados os teus dispositivos."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Para arquivos grandes, recomendamos usar un xestor de descargas para evitar interrupcións."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Xestores de descargas recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Necesitará un lector de ebooks ou PDF para abrir o arquivo, dependendo do formato do arquivo."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Lectores de ebooks recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visor en liña de Anna’s Archive"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Use ferramentas en liña para converter entre formatos."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Ferramentas de conversión recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Pode enviar arquivos PDF e EPUB ao seu Kindle ou Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Ferramentas recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Enviar a Kindle” de Amazon"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Enviar a Kobo/Kindle” de djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Apoiar autores e bibliotecas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Se lle gusta isto e pode permitilo, considere comprar o orixinal ou apoiar directamente aos autores."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Se isto está dispoñible na súa biblioteca local, considere pedilo prestado de balde alí."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Calidade do ficheiro"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "¡Axuda á comunidade informando sobre a calidade deste ficheiro! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Informar sobre un problema co ficheiro (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Boa calidade do ficheiro (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Engadir comentario (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Que está mal con este ficheiro?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Por favor, usa o <a %(a_copyright)s>formulario de reclamación de DMCA / Dereitos de autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Describe o problema (requirido)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descrición do problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 dunha versión mellor deste ficheiro (se é aplicable)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Enche isto se hai outro ficheiro que coincida estreitamente con este ficheiro (mesma edición, mesma extensión de ficheiro se podes atopar un), que a xente debería usar en lugar deste ficheiro. Se coñeces unha versión mellor deste ficheiro fóra do Arquivo de Anna, entón por favor <a %(a_upload)s>cárgaa</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Podes obter o md5 da URL, por exemplo"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Enviar informe"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Aprende como <a %(a_metadata)s>mellorar os metadatos</a> deste ficheiro ti mesmo."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Grazas por enviar o teu informe. Amosarase nesta páxina, así como revisado manualmente por Anna (ata que teñamos un sistema de moderación adecuado)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Algo saíu mal. Por favor, recarga a páxina e inténtao de novo."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Se este ficheiro ten gran calidade, podes discutir calquera cousa sobre el aquí! Se non, por favor usa o botón “Informar dun problema co ficheiro”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Encantoume este libro!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Deixar comentario"

msgid "common.english_only"
msgstr "O texto a continuación continúa en inglés."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Descargas totais: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “MD5 do ficheiro” é un hash que se calcula a partir do contido do ficheiro, e é razoablemente único baseado nese contido. Todas as bibliotecas sombra que temos indexadas aquí usan principalmente MD5s para identificar ficheiros."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un ficheiro pode aparecer en múltiples bibliotecas sombra. Para información sobre os diferentes datasets que temos compilado, consulta a <a %(a_datasets)s>páxina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Este é un ficheiro xestionado pola biblioteca de <a %(a_ia)s>Préstamo Dixital Controlado do IA</a>, e indexado por Anna’s Archive para a busca. Para información sobre os diferentes datasets que temos compilado, consulta a <a %(a_datasets)s>páxina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Para información sobre este ficheiro en particular, consulta o seu <a %(a_href)s>ficheiro JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema ao cargar esta páxina"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Por favor, actualice para tentalo de novo. <a %(a_contact)s>Contacte connosco</a> se o problema persiste durante varias horas."

msgid "page.md5.invalid.header"
msgstr "Non atopado"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” non foi atopado na nosa base de datos."

msgid "page.login.title"
msgstr "Iniciar sesión / Rexistrarse"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verificación do navegador"

msgid "page.login.text1"
msgstr "Para evitar que os bots de spam creen moitas contas, primeiro temos que verificar o teu navegador."

#, fuzzy
msgid "page.login.text2"
msgstr "Se quedas atrapado nun bucle infinito, recomendámosche instalar <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Tamén pode ser útil desactivar os bloqueadores de anuncios e outras extensións do navegador."

#, fuzzy
msgid "page.codes.title"
msgstr "Códigos"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorador de Códigos"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explora os códigos cos que se etiquetan os rexistros, por prefixo. A columna “rexistros” mostra o número de rexistros etiquetados con códigos co prefixo dado, como se ve no motor de busca (incluíndo rexistros só de metadatos). A columna “códigos” mostra cantos códigos reais teñen un prefixo dado."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Esta páxina pode tardar un pouco en xenerarse, por iso require un captcha de Cloudflare. <a %(a_donate)s>Os membros</a> poden saltar o captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Recomendamos non rastrexar estas páxinas. En vez diso, suxerimos <a %(a_import)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos de ElasticSearch e MariaDB, e executar o noso <a %(a_software)s>código de código aberto</a>. Os datos en bruto pódense explorar manualmente a través de ficheiros JSON como <a %(a_json_file)s>este</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefixo"

#, fuzzy
msgid "common.form.go"
msgstr "Ir"

#, fuzzy
msgid "common.form.reset"
msgstr "Restablecer"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Buscar o Arquivo de Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Aviso: o código contén caracteres Unicode incorrectos e pode comportarse incorrectamente en varias situacións. O binario bruto pódese decodificar a partir da representación base64 na URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefixo coñecido “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefixo"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiqueta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Descrición"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL para un código específico"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” será substituído polo valor do código"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL xenérica"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Sitio web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "Rexistro %(count)s que coincide con “%(prefix_label)s”"
msgstr[1] "Rexistros %(count)s que coinciden con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL para código específico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Máis…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Códigos que comezan con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Índice de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "rexistros"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "códigos"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s rexistros"

msgid "page.contact.dmca.form"
msgstr "Para reclamacións DMCA / copyright, empregue <a %(a_copyright)s> este formulario</a>."

msgid "page.contact.dmca.delete"
msgstr "Calquera outra forma de poñerse en contacto con nós sobre reclamacións de dereitos de autor eliminarase automáticamente."

msgid "page.contact.checkboxes.text1"
msgstr "Agradecemos os seus comentarios e preguntas !"

msgid "page.contact.checkboxes.text2"
msgstr "Non obstante, dada a cantidade de spam e correos electrónicos sen sentido que recibimos, marque as casillas para confirmar que entende estas condicións para poñerse en contacto con nós."

msgid "page.contact.checkboxes.copyright"
msgstr "Non se terán en conta as reclamacións de dereitos de autor enviadas por correo electrónico."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Os servidores asociados non están dispoñibles debido ao peche de aloxamentos. Deberían estar operativos de novo pronto."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "As subscricións serán ampliadas en consecuencia."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Non nos envíes correos a <a %(a_request)s>solicitar libros</a><br>ou pequenas (<10k) <a %(a_upload)s>subidas</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Ao preguntar sobre contas ou doazóns, engade o teu ID de conta, capturas de pantalla, recibos, tanta información como sexa posible. Só revisamos o noso correo electrónico cada 1-2 semanas, polo que non incluír esta información atrasará calquera resolución."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Amosar email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulario de reclamación de DMCA / Copyright"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Se tes unha reclamación de DMCA ou outro tipo de reclamación de dereitos de autor, enche este formulario o máis precisamente posible. Se tes algún problema, contacta connosco na nosa dirección dedicada a DMCA: %(email)s. Ten en conta que as reclamacións enviadas a esta dirección non serán procesadas, só é para preguntas. Usa o formulario a continuación para enviar as túas reclamacións."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs en Anna’s Archive (requirido). Un por liña. Inclúe só URLs que describan exactamente a mesma edición dun libro. Se queres facer unha reclamación para varios libros ou varias edicións, envía este formulario varias veces."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "As reclamacións que agrupen varios libros ou edicións serán rexeitadas."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "O teu nome (requirido)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Enderezo (requirido)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Número de teléfono (requirido)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Correo electrónico (requirido)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descrición clara do material de orixe (requirido)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs do material de orixe (se é aplicable). Un por liña. Inclúe só aqueles que coincidan exactamente coa edición para a que estás informando dunha reclamación de dereitos de autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs do material de orixe, un por liña. Tómate un momento para buscar o teu material de orixe en Open Library. Isto axudaranos a verificar a túa reclamación."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs do material de orixe, un por liña (requirido). Inclúe tantos como sexa posible, para axudarnos a verificar a túa reclamación (por exemplo, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaración e sinatura (requirido)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Enviar reclamación"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Grazas por enviar a túa reclamación de dereitos de autor. Revisarémola o antes posible. Recarga a páxina para enviar outra."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Algo saíu mal. Recarga a páxina e inténtao de novo."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Se estás interesado en espellar este conxunto de datos para <a %(a_archival)s>arquivo</a> ou para fins de <a %(a_llm)s>adestramento de LLM</a>, por favor contacta connosco."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "A nosa misión é arquivar todos os libros do mundo (así como artigos, revistas, etc), e facelos amplamente accesibles. Cremos que todos os libros deberían ser espellados amplamente, para garantir a redundancia e a resiliencia. Por iso estamos reunindo ficheiros de varias fontes. Algunhas fontes son completamente abertas e poden ser espelladas en masa (como Sci-Hub). Outras son pechadas e protectoras, polo que intentamos raspalas para “liberar” os seus libros. Outras están nalgún punto intermedio."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Todos os nosos datos poden ser <a %(a_torrents)s>torrenteados</a>, e todos os nosos metadatos poden ser <a %(a_anna_software)s>xenerados</a> ou <a %(a_elasticsearch)s>descargados</a> como bases de datos ElasticSearch e MariaDB. Os datos en bruto poden ser explorados manualmente a través de ficheiros JSON como <a %(a_dbrecord)s>este</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Visión xeral"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "A continuación, unha rápida visión xeral das fontes dos ficheiros en Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Tamaño"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% espellado por AA / torrents dispoñibles"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Porcentaxes do número de ficheiros"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Última actualización"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Ficción e Ficción"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s ficheiro"
msgstr[1] "%(count)s ficheiros"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "A través de Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: conxelado desde 2021; a maioría dispoñible a través de torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: pequenas adicións desde entón</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluíndo “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Os torrents de ficción están atrasados (aínda que os IDs ~4-6M non están en torrents xa que se solapan cos nosos torrents de Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "A colección “Chinesa” en Z-Library parece ser a mesma que a nosa colección de DuXiu, pero con diferentes MD5s. Excluímos estes ficheiros dos torrents para evitar duplicacións, pero aínda os mostramos no noso índice de busca."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Préstamo Dixital Controlado de IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dos arquivos son buscables."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluíndo duplicados"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Como as bibliotecas sombra adoitan sincronizar datos entre si, hai unha considerable superposición entre as bibliotecas. Por iso os números non suman o total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "A porcentaxe de “espellado e sementado por Anna’s Archive” mostra cantos ficheiros espellamos nós mesmos. Sementamos eses ficheiros en masa a través de torrents, e facémolos dispoñibles para descarga directa a través de sitios web asociados."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliotecas fonte"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Algunhas bibliotecas fonte promoven o intercambio masivo dos seus datos a través de torrents, mentres que outras non comparten facilmente a súa colección. No último caso, Anna’s Archive tenta raspar as súas coleccións e facelas dispoñibles (vexa a nosa páxina de <a %(a_torrents)s>Torrents</a>). Tamén hai situacións intermedias, por exemplo, onde as bibliotecas fonte están dispostas a compartir, pero non teñen os recursos para facelo. Neses casos, tamén tentamos axudar."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "A continuación, preséntase unha visión xeral de como interactuamos coas diferentes bibliotecas fonte."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Ficheiros"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Verteduras diarias de <a %(dbdumps)s>bases de datos HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizados para <a %(nonfiction)s>Non-Ficción</a> e <a %(fiction)s>Ficción</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(covers)s>torrents de portadas de libros</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub conxelou novos ficheiros desde 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Volcados de metadatos dispoñibles <a %(scihub1)s>aquí</a> e <a %(scihub2)s>aquí</a>, así como parte da <a %(libgenli)s>base de datos de Libgen.li</a> (que usamos)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents de datos dispoñibles <a %(scihub1)s>aquí</a>, <a %(scihub2)s>aquí</a>, e <a %(libgenli)s>aquí</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Algúns novos ficheiros están <a %(libgenrs)s>sendo</a> <a %(libgenli)s>engadidos</a> ao “scimag” de Libgen, pero non o suficiente para xustificar novos torrents"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Volcados trimestrais da <a %(dbdumps)s>base de datos HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Os torrents de Non-Ficción compártense con Libgen.rs (e espellados <a %(libgenli)s>aquí</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s O Arquivo de Anna e Libgen.li xestionan conxuntamente coleccións de <a %(comics)s>cómics</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos estándar</a> e <a %(fiction)s>ficción (divergida de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s A súa colección “fiction_rus” (ficción rusa) non ten torrents dedicados, pero está cuberta por torrents doutros, e mantemos un <a %(fiction_rus)s>espello</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s O Arquivo de Anna e Z-Library xestionan colaborativamente unha colección de <a %(metadata)s>metadatos de Z-Library</a> e <a %(files)s>ficheiros de Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Algúns metadatos dispoñibles a través de <a %(openlib)s>Open Library database dumps</a>, pero non cobren toda a colección de IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Non hai volcados de metadatos facilmente accesibles dispoñibles para toda a súa colección"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(ia)s>metadatos de IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Ficheiros só dispoñibles para préstamo de forma limitada, con varias restricións de acceso"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(ia)s>ficheiros de IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Varias bases de datos de metadatos espalladas pola internet chinesa; aínda que a miúdo son bases de datos de pago"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Non hai dispoñibles volcados de metadatos facilmente accesibles para toda a súa colección."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(duxiu)s>metadatos de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Varias bases de datos de ficheiros espalladas pola internet chinesa; aínda que a miúdo son bases de datos de pago"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s A maioría dos ficheiros só son accesibles usando contas premium de BaiduYun; velocidades de descarga lentas."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(duxiu)s>ficheiros de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Varias fontes máis pequenas ou únicas. Animamos ás persoas a subir primeiro a outras bibliotecas sombra, pero ás veces as persoas teñen coleccións que son demasiado grandes para que outros as clasifiquen, aínda que non o suficientemente grandes como para merecer a súa propia categoría."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fontes só de metadatos"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Tamén enriquecemos a nosa colección con fontes só de metadatos, que podemos emparellar con ficheiros, por exemplo, usando números ISBN ou outros campos. A continuación, preséntase unha visión xeral desas fontes. De novo, algunhas destas fontes son completamente abertas, mentres que outras temos que raspalas."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "A nosa inspiración para recoller metadatos é o obxectivo de Aaron Swartz de “unha páxina web para cada libro publicado”, para o cal creou <a %(a_openlib)s>Open Library</a>. Ese proxecto foi ben, pero a nosa posición única permítenos obter metadatos que eles non poden. Outra inspiración foi o noso desexo de saber <a %(a_blog)s>cantos libros hai no mundo</a>, para poder calcular cantos libros aínda temos que salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Teña en conta que na busca de metadatos, mostramos os rexistros orixinais. Non facemos ningunha fusión de rexistros."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Última actualización"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Verteduras mensuais de <a %(dbdumps)s>bases de datos</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Non dispoñible directamente en masa, protexido contra raspado"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(worldcat)s>metadatos de OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Base de datos unificada"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Combinamos todas as fontes anteriores nunha base de datos unificada que usamos para servir este sitio web. Esta base de datos unificada non está dispoñible directamente, pero como Anna’s Archive é totalmente de código aberto, pode ser bastante facilmente <a %(a_generated)s>xerada</a> ou <a %(a_downloaded)s>descargada</a> como bases de datos ElasticSearch e MariaDB. Os scripts desa páxina descargarán automaticamente todos os metadatos necesarios das fontes mencionadas anteriormente."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Se desexa explorar os nosos datos antes de executar eses scripts localmente, pode consultar os nosos ficheiros JSON, que enlazan con outros ficheiros JSON. <a %(a_json)s>Este ficheiro</a> é un bo punto de partida."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptado da nosa <a %(a_href)s>entrada de blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> é unha base de datos masiva de libros escaneados, creada polo <a %(superstar_link)s>SuperStar Digital Library Group</a>. A maioría son libros académicos, escaneados para facelos dispoñibles dixitalmente a universidades e bibliotecas. Para a nosa audiencia de fala inglesa, <a %(princeton_link)s>Princeton</a> e a <a %(uw_link)s>Universidade de Washington</a> teñen boas visións xerais. Tamén hai un excelente artigo que ofrece máis contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Os libros de Duxiu levan moito tempo sendo pirateados na internet chinesa. Normalmente véndense por menos dun dólar por revendedores. Adoitan distribuírse usando o equivalente chinés de Google Drive, que a miúdo foi hackeado para permitir máis espazo de almacenamento. Algúns detalles técnicos pódense atopar <a %(link1)s>aquí</a> e <a %(link2)s>aquí</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Aínda que os libros foron distribuídos de forma semi-pública, é bastante difícil obtelos en grandes cantidades. Tiñámolo moi alto na nosa lista de tarefas pendentes, e asignamos varios meses de traballo a tempo completo para iso. Con todo, a finais de 2023 un voluntario incrible, asombroso e talentoso contactou connosco, dicíndonos que xa fixera todo este traballo — a gran custo. Compartiron connosco a colección completa, sen esperar nada a cambio, excepto a garantía de preservación a longo prazo. Realmente notable."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total de ficheiros: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Tamaño total dos ficheiros: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Ficheiros espellados polo Arquivo de Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Última actualización: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents do Arquivo de Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplo de rexistro no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "A nosa entrada de blog sobre estes datos"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts para importar metadatos"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formato de Contedores do Arquivo de Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Máis información dos nosos voluntarios (notas en bruto):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "Préstamo Dixital Controlado de IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Este conxunto de datos está estreitamente relacionado co <a %(a_datasets_openlib)s>conxunto de datos de Open Library</a>. Contén unha raspadura de todos os metadatos e unha gran parte dos ficheiros da Biblioteca de Préstamo Dixital Controlado da IA. As actualizacións publícanse no <a %(a_aac)s>formato de Contedores de Anna’s Archive</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Estes rexistros están sendo referidos directamente do conxunto de datos de Open Library, pero tamén conteñen rexistros que non están en Open Library. Tamén temos varios ficheiros de datos raspados por membros da comunidade ao longo dos anos."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "A colección consta de dúas partes. Necesitas ambas partes para obter todos os datos (excepto os torrents substituídos, que están tachados na páxina de torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "a nosa primeira versión, antes de estandarizarnos no formato <a %(a_aac)s>Contedores do Arquivo de Anna (AAC)</a>. Contén metadatos (como json e xml), pdfs (dos sistemas de préstamo dixital acsm e lcpdf) e miniaturas de portadas."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "novas versións incrementais, usando AAC. Só contén metadatos con marcas de tempo despois de 2023-01-01, xa que o resto xa está cuberto por \"ia\". Tamén todos os ficheiros pdf, esta vez dos sistemas de préstamo acsm e \"bookreader\" (o lector web de IA). A pesar de que o nome non é exactamente correcto, seguimos poboando ficheiros de bookreader na colección ia2_acsmpdf_files, xa que son mutuamente excluíntes."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sitio web principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca de Préstamo Dixital"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentación de metadatos (a maioría dos campos)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Información do país do ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "A Axencia Internacional do ISBN publica regularmente os rangos que asignou ás axencias nacionais do ISBN. A partir disto, podemos determinar a que país, rexión ou grupo de linguas pertence este ISBN. Actualmente usamos estes datos indirectamente, a través da biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Última actualización: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sitio web do ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Para a historia dos diferentes forks de Library Genesis, vexa a páxina de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "O Libgen.li contén a maior parte do mesmo contido e metadatos que o Libgen.rs, pero ten algunhas coleccións adicionais, a saber, cómics, revistas e documentos estándar. Tamén integrou <a %(a_scihub)s>Sci-Hub</a> nos seus metadatos e motor de busca, que é o que usamos para a nosa base de datos."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Os metadatos desta biblioteca están dispoñibles gratuitamente <a %(a_libgen_li)s>en libgen.li</a>. Non obstante, este servidor é lento e non admite a reanudación de conexións interrompidas. Os mesmos ficheiros tamén están dispoñibles nun <a %(a_ftp)s>servidor FTP</a>, que funciona mellor."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Os torrents están dispoñibles para a maioría do contido adicional, especialmente os torrents para cómics, revistas e documentos estándar foron lanzados en colaboración co Arquivo de Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "A colección de ficción ten os seus propios torrents (divergentes de <a %(a_href)s>Libgen.rs</a>) a partir de %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Segundo o administrador de Libgen.li, a colección “fiction_rus” (ficción rusa) debería estar cuberta por torrents lanzados regularmente desde <a %(a_booktracker)s>booktracker.org</a>, especialmente os torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que espellamos <a %(a_torrents)s>aquí</a>, aínda que aínda non establecemos que torrents corresponden a que arquivos)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "As estatísticas de todas as coleccións pódense atopar <a %(a_href)s>no sitio web de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "A non ficción tamén parece terse desviado, pero sen novos torrents. Parece que isto sucedeu desde principios de 2022, aínda que non o verificamos."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certos rangos sen torrents (como os rangos de ficción f_3463000 a f_4260000) probablemente sexan arquivos de Z-Library (ou outros duplicados), aínda que quizais queiramos facer algunha deduplicación e crear torrents para arquivos únicos de lgli nestes rangos."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Teña en conta que os arquivos torrent que se refiren a “libgen.is” son explicitamente espellos de <a %(a_libgen)s>Libgen.rs</a> (“.is” é un dominio diferente usado por Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Un recurso útil para usar os metadatos é <a %(a_href)s>esta páxina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de ficción no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de cómics no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de revistas no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos estándar no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de ficción rusa no Arquivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadatos vía FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Información do campo de metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Espello doutros torrents (e torrents únicos de ficción e cómics)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Foro de discusión"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "A nosa publicación no blog sobre o lanzamento dos cómics"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "A breve historia das diferentes bifurcacións de Library Genesis (ou “Libgen”) é que co paso do tempo, as diferentes persoas involucradas en Library Genesis tiveron desacordos e seguiron camiños separados."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "A versión “.fun” foi creada polo fundador orixinal. Está sendo renovada a favor dunha nova versión máis distribuída."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "A versión “.rs” ten datos moi similares e publica a súa colección en torrents masivos de forma consistente. Está aproximadamente dividida nunha sección de “ficción” e outra de “non ficción”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Orixinalmente en “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "A <a %(a_li)s>versión “.li”</a> ten unha colección masiva de cómics, así como doutro contido, que aínda non está dispoñible para descarga masiva a través de torrents. Ten unha colección de torrents separada de libros de ficción, e contén os metadatos de <a %(a_scihub)s>Sci-Hub</a> na súa base de datos."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Segundo esta <a %(a_mhut)s>publicación no foro</a>, Libgen.li foi orixinalmente aloxado en “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> nalgún sentido tamén é unha bifurcación de Library Genesis, aínda que usaron un nome diferente para o seu proxecto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Esta páxina trata sobre a versión “.rs”. É coñecida por publicar consistentemente tanto os seus metadatos como o contido completo do seu catálogo de libros. A súa colección de libros está dividida entre unha parte de ficción e outra de non ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Un recurso útil para usar os metadatos é <a %(a_metadata)s>esta páxina</a> (bloquea rangos de IP, pode ser necesario un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de 2024-03, novos torrents están a ser publicados neste <a %(a_href)s>fío do foro</a> (bloquea rangos de IP, pode ser necesario un VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de non ficción en Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de ficción en Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Información dos campos de metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de non ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Foro de discusión de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents de Anna’s Archive (portadas de libros)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "O noso blog sobre o lanzamento das portadas de libros"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis é coñecido por xa facer xenerosamente os seus datos dispoñibles en masa a través de torrents. A nosa colección de Libgen consiste en datos auxiliares que non liberan directamente, en colaboración con eles. Moitas grazas a todos os implicados en Library Genesis por traballar connosco!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Lanzamento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Este <a %(blog_post)s>primeiro lanzamento</a> é bastante pequeno: uns 300GB de portadas de libros do fork de Libgen.rs, tanto de ficción como de non ficción. Están organizados do mesmo xeito que aparecen en libgen.rs, por exemplo:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s para un libro de non ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s para un libro de ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Igual que coa colección de Z-Library, puxémolos todos nun gran ficheiro .tar, que se pode montar usando <a %(a_ratarmount)s>ratarmount</a> se queres servir os ficheiros directamente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> é unha base de datos propietaria da organización sen ánimo de lucro <a %(a_oclc)s>OCLC</a>, que agrega rexistros de metadatos de bibliotecas de todo o mundo. Probablemente sexa a maior colección de metadatos de bibliotecas do mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Outubro de 2023, lanzamento inicial:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "En outubro de 2023 <a %(a_scrape)s>publicamos</a> unha extracción completa da base de datos de OCLC (WorldCat), no <a %(a_aac)s>formato de Contedores de Anna’s Archive</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents por Anna’s Archive"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "A nosa publicación no blog sobre estes datos"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library é un proxecto de código aberto do Internet Archive para catalogar todos os libros do mundo. Ten unha das maiores operacións de escaneo de libros do mundo, e ten moitos libros dispoñibles para préstamo dixital. O seu catálogo de metadatos de libros está dispoñible para descarga gratuíta, e está incluído en Anna’s Archive (aínda que actualmente non está na busca, excepto se buscas explicitamente un ID de Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Lanzamento 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Este é un volcado de moitas chamadas a isbndb.com durante setembro de 2022. Tentamos cubrir todos os rangos de ISBN. Son aproximadamente 30,9 millóns de rexistros. No seu sitio web afirman que realmente teñen 32,6 millóns de rexistros, polo que poderiamos ter perdido algúns, ou <em>eles</em> poderían estar facendo algo mal."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "As respostas JSON son practicamente crúas do seu servidor. Un problema de calidade de datos que notamos é que para os números ISBN-13 que comezan cun prefixo diferente a “978-”, aínda inclúen un campo “isbn” que simplemente é o número ISBN-13 cos tres primeiros números cortados (e o díxito de verificación recalculado). Isto obviamente está mal, pero así parece que o fan, polo que non o alteramos."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Outro problema potencial que pode atopar é o feito de que o campo “isbn13” ten duplicados, polo que non pode usalo como clave primaria nunha base de datos. Os campos combinados “isbn13”+“isbn” parecen ser únicos."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Para obter información sobre Sci-Hub, consulte o seu <a %(a_scihub)s>sitio web oficial</a>, a súa <a %(a_wikipedia)s>páxina de Wikipedia</a> e esta <a %(a_radiolab)s>entrevista en podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Teña en conta que Sci-Hub foi <a %(a_reddit)s>conxelado desde 2021</a>. Xa fora conxelado antes, pero en 2021 engadíronse uns poucos millóns de artigos. Aínda así, engádense algúns artigos limitados ás coleccións “scimag” de Libgen, aínda que non o suficiente como para xustificar novos torrents masivos."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usamos os metadatos de Sci-Hub proporcionados por <a %(a_libgen_li)s>Libgen.li</a> na súa colección “scimag”. Tamén usamos o conxunto de datos <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Teña en conta que os torrents “smarch” están <a %(a_smarch)s>obsoletos</a> e, polo tanto, non están incluídos na nosa lista de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents en Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadatos e torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents en Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents en Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Actualizacións en Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Páxina de Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Entrevista en podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Subidas ao Arquivo de Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Visión xeral da <a %(a1)s>páxina de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Varias fontes máis pequenas ou únicas. Animamos ás persoas a subir primeiro a outras bibliotecas sombra, pero ás veces as persoas teñen coleccións que son demasiado grandes para que outros as clasifiquen, aínda que non o suficientemente grandes como para merecer a súa propia categoría."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "A colección de \"subidas\" está dividida en subcoleccións máis pequenas, que están indicadas nos AACIDs e nos nomes dos torrents. Todas as subcoleccións foron primeiro deduplicadas contra a colección principal, aínda que os ficheiros JSON de metadatos \"upload_records\" aínda conteñen moitas referencias aos ficheiros orixinais. Os ficheiros non relacionados con libros tamén foron eliminados da maioría das subcoleccións, e normalmente <em>non</em> están anotados no JSON de \"upload_records\"."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Moitas subcoleccións están compostas por sub-sub-coleccións (por exemplo, de diferentes fontes orixinais), que están representadas como directorios nos campos de “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "As subcoleccións son:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcolección"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notas"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "navegar"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "buscar"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bastante completo. Do noso voluntario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Dun <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Ten unha alta superposición coas coleccións de artigos existentes, pero moi poucos coincidencias de MD5, polo que decidimos mantelo completamente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raspado de <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), por voluntario <q>j</q>. Corresponde ao metadata de <q>airitibooks</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Dunha colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte da fonte orixinal, parte de the-eye.eu, parte doutros espellos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Dun sitio web privado de torrents de libros, <a %(a_href)s>Bibliotik</a> (a miúdo referido como “Bib”), dos cales os libros foron agrupados en torrents por nome (A.torrent, B.torrent) e distribuídos a través de the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Do noso voluntario \"bpb9v\". Para máis información sobre <a %(a_href)s>CADAL</a>, vexa as notas na nosa <a %(a_duxiu)s>páxina de datos de DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Máis do noso voluntario \"bpb9v\", principalmente ficheiros de DuXiu, así como un cartafol \"WenQu\" e \"SuperStar_Journals\" (SuperStar é a empresa detrás de DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Do noso voluntario \"cgiym\", textos chineses de varias fontes (representados como subdirectorios), incluíndo de <a %(a_href)s>China Machine Press</a> (un importante editor chinés)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Coleccións non chinesas (representadas como subdirectorios) do noso voluntario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raspado de libros sobre arquitectura chinesa, por voluntario <q>cm</q>: <q>Conseguino explotando unha vulnerabilidade de rede na editorial, pero esa fenda xa foi pechada</q>. Corresponde ao metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libros da editorial académica <a %(a_href)s>De Gruyter</a>, recollidos de algúns grandes torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Raspado de <a %(a_href)s>docer.pl</a>, un sitio web polaco de intercambio de arquivos centrado en libros e outros traballos escritos. Raspado a finais de 2023 polo voluntario “p”. Non temos bos metadatos do sitio web orixinal (nin sequera extensións de arquivo), pero filtramos arquivos semellantes a libros e a miúdo puidemos extraer metadatos dos propios arquivos."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epubs de DuXiu, directamente de DuXiu, recollidos polo voluntario \"w\". Só os libros recentes de DuXiu están dispoñibles directamente a través de ebooks, polo que a maioría destes deben ser recentes."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Arquivos restantes de DuXiu do voluntario “m”, que non estaban no formato propietario PDG de DuXiu (o principal <a %(a_href)s>conxunto de datos de DuXiu</a>). Recollidos de moitas fontes orixinais, lamentablemente sen preservar esas fontes na ruta do arquivo."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raspado de libros eróticos, por voluntario <q>do no harm</q>. Corresponde ao metadata de <q>hentai</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Colección raspada dun editor xaponés de manga polo voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arquivos xudiciais seleccionados de Longquan</a>, proporcionados polo voluntario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Raspado de <a %(a_href)s>magzdb.org</a>, un aliado de Library Genesis (está vinculado na páxina de inicio de libgen.rs) pero que non quixo proporcionar os seus arquivos directamente. Obtido polo voluntario “p” a finais de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Varias pequenas subidas, demasiado pequenas como para ser a súa propia subcolección, pero representadas como directorios."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks de AvaxHome, un sitio web ruso de intercambio de arquivos."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arquivo de xornais e revistas. Corresponde ao metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Outros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raspado do <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Colección do voluntario “o” que recolleu libros polacos directamente dos sitios web de lanzamento orixinal (“escena”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Coleccións combinadas de <a %(a_href)s>shuge.org</a> polos voluntarios “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperial de Trantor”</a> (nomeada pola biblioteca ficticia), raspada en 2022 polo voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-coleccións (representadas como directorios) do voluntario \"woz9ts\": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taiwán), mebook (mebook.cc, 我的小书屋, a miña pequena libraría — woz9ts: \"Este sitio céntrase principalmente en compartir ficheiros de ebooks de alta calidade, algúns dos cales están maquetados polo propio propietario. O propietario foi <a %(a_arrested)s>detido</a> en 2019 e alguén fixo unha colección dos ficheiros que compartiu.\")."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Ficheiros restantes de DuXiu do voluntario “woz9ts”, que non estaban no formato propietario PDG de DuXiu (aínda por converter a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents polo Arquivo de Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Raspado de Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ten as súas raíces na comunidade de <a %(a_href)s>Library Genesis</a>, e orixinalmente comezou coas súas datos. Desde entón, profesionalizouse considerablemente, e ten unha interface moito máis moderna. Por iso, son capaces de obter moitas máis doazóns, tanto monetarias para seguir mellorando o seu sitio web, como doazóns de novos libros. Acumularon unha gran colección ademais de Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Actualización de febreiro de 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "A finais de 2022, os presuntos fundadores de Z-Library foron arrestados, e os dominios foron incautados polas autoridades dos Estados Unidos. Desde entón, o sitio web foi lentamente volvendo a estar en liña. Descoñécese quen o xestiona actualmente."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "A colección consta de tres partes. As páxinas de descrición orixinais das dúas primeiras partes consérvanse a continuación. Necesitas as tres partes para obter todos os datos (agás os torrents substituídos, que están tachados na páxina de torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: a nosa primeira versión. Esta foi a primeira versión do que entón se chamaba o “Espello da Biblioteca Pirata” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: segunda versión, esta vez con todos os ficheiros empaquetados en ficheiros .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: novas versións incrementais, usando o <a %(a_href)s>formato de Contedores de Arquivo de Anna (AAC)</a>, agora lanzado en colaboración co equipo de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents por Arquivo de Anna (metadatos + contido)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplo de rexistro no Arquivo de Anna (colección orixinal)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplo de rexistro no Arquivo de Anna (colección “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sitio web principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Dominio Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Publicación no blog sobre a Versión 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Publicación no blog sobre a Versión 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Lanzamentos de Zlib (páxinas de descrición orixinais)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Versión 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "O espello inicial obtívose meticulosamente ao longo de 2021 e 2022. Neste momento está lixeiramente desactualizado: reflicte o estado da colección en xuño de 2021. Actualizaremos isto no futuro. Agora mesmo estamos centrados en lanzar esta primeira versión."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Dado que Library Genesis xa está preservada con torrents públicos e está incluída na Z-Library, fixemos unha deduplicación básica contra Library Genesis en xuño de 2022. Para isto usamos hashes MD5. Probablemente hai moito máis contido duplicado na biblioteca, como múltiples formatos de ficheiros co mesmo libro. Isto é difícil de detectar con precisión, polo que non o facemos. Despois da deduplicación quedamos con máis de 2 millóns de ficheiros, totalizando pouco menos de 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "A colección consta de dúas partes: un volcado MySQL “.sql.gz” dos metadatos, e os 72 ficheiros torrent de arredor de 50-100GB cada un. Os metadatos conteñen os datos informados polo sitio web de Z-Library (título, autor, descrición, tipo de ficheiro), así como o tamaño real do ficheiro e o md5sum que observamos, xa que ás veces estes non coinciden. Parece haber rangos de ficheiros para os que a propia Z-Library ten metadatos incorrectos. Tamén poderiamos ter descargado ficheiros incorrectamente nalgúns casos illados, que intentaremos detectar e corrixir no futuro."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Os grandes ficheiros torrent conteñen os datos reais dos libros, co ID de Z-Library como nome do ficheiro. As extensións dos ficheiros pódense reconstruír usando o volcado de metadatos."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "A colección é unha mestura de contido de non ficción e ficción (non separado como en Library Genesis). A calidade tamén varía moito."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Esta primeira versión xa está completamente dispoñible. Teña en conta que os ficheiros torrent só están dispoñibles a través do noso espello Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Versión 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Conseguimos todos os libros que se engadiron á Z-Library entre o noso último espello e agosto de 2022. Tamén volvemos atrás e raspamos algúns libros que perdemos a primeira vez. En total, esta nova colección é duns 24 TB. De novo, esta colección está deduplicada contra Library Genesis, xa que xa hai torrents dispoñibles para esa colección."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Os datos están organizados de forma similar á primeira versión. Hai un volcado MySQL “.sql.gz” dos metadatos, que tamén inclúe todos os metadatos da primeira versión, substituíndoa así. Tamén engadimos algunhas novas columnas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: se este ficheiro xa está en Library Genesis, na colección de non ficción ou ficción (coincidencia por md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: en que torrent está este ficheiro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: establecido cando non puidemos descargar o libro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mencionamos isto a última vez, pero só para aclarar: “filename” e “md5” son as propiedades reais do ficheiro, mentres que “filename_reported” e “md5_reported” son o que extraemos de Z-Library. Ás veces estes dous non coinciden entre si, polo que incluímos ambos."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Para esta versión, cambiamos a colación a “utf8mb4_unicode_ci”, que debería ser compatible con versións máis antigas de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Os ficheiros de datos son similares aos da última vez, aínda que son moito máis grandes. Simplemente non nos molestamos en crear toneladas de ficheiros torrent máis pequenos. “pilimi-zlib2-0-14679999-extra.torrent” contén todos os ficheiros que perdemos na última versión, mentres que os outros torrents son todos novos rangos de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Actualización %(date)s:</strong> Fixemos a maioría dos nosos torrents demasiado grandes, causando problemas aos clientes de torrent. Eliminámolos e lanzamos novos torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Actualización %(date)s:</strong> Aínda había demasiados ficheiros, polo que os empaquetamos en ficheiros tar e lanzamos novos torrents de novo."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addenda da versión 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Este é un único ficheiro torrent extra. Non contén ningunha información nova, pero ten algúns datos que poden levar un tempo calcular. Iso fai que sexa conveniente telo, xa que descargar este torrent adoita ser máis rápido que calculalo desde cero. En particular, contén índices SQLite para os ficheiros tar, para usar con <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Preguntas Frecuentes (FAQ - Frequently Asked Questions)"

msgid "page.faq.what_is.title"
msgstr "Qué é Anna's Archive ?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> é un proxecto sen ánimo de lucro con dous obxectivos:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Preservación:</strong> Respaldar todo o coñecemento e a cultura da humanidade.</li><li><strong>Acceso:</strong> Poñer este coñecemento e cultura a disposición de calquera persoa do mundo.</li>"

msgid "page.home.intro.open_source"
msgstr "Todos o noso <a %(a_code)s>código</a> e <a %(a_datasets)s>datos</a> son completamente de código aberto."

msgid "page.home.preservation.header"
msgstr "Preservación"

msgid "page.home.preservation.text1"
msgstr "Preservamos libros, periódicos, cómics, revistas e moito máis, reunindo estos materiais de diversas <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliotecas na sombra</a>, bibliotecas oficiais, e outras coleccións en un mesmo lugar. Todos estes datos se preservan para sempre facilitando a súa duplicación masiva - mediante torrents -, o que da lugar a numerosas copias en todo o mundo (Por exemplo: Sci-Hub, Library Genesis), mentres que Anna's Archive \"libera\" outras bibliotecas que non ofrecen distribución masiva (Por exemplo Z-Library) ou que non son bibliotecas na sombra en absoluto (Por exemplo: Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Esta amplia difusión, combinada co código aberto, fai que o noso sitio web sexa resistente aos ataques e garantiza a conservación a largo prazo do coñecemento e a cultura da humanidade. Lea máis sobre <a href=\"/datasets\">os nosos datasets</a>."

msgid "page.home.preservation.label"
msgstr "Estimamos que temos preservado a redor de <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% de todo-los libros do mundo</a>."

msgid "page.home.access.header"
msgstr "Acceso"

msgid "page.home.access.text"
msgstr "Traballamos con socios para que calquera poida acceder as nosas coleccións de forma fácil e gratuita. Creemos que todo o mundo ten dereito á sabiduría colectiva da humanidade. E <a %(a_search)s>non a costa dos autores</a>."

msgid "page.home.access.label"
msgstr "Descargas por hora nos últimos 30 días. Media horaria: %(hourly)s. Media diaria: %(daily)s."

msgid "page.about.text2"
msgstr "Creemos firmemente na libre circulación de información e na preservación do coñecemento e a cultura. Con este motor de búsqueda, apoiámonos a hombros de xigantes. Respetamos profundamente o duro traballo das persoas que crearon as distintas bibliotecas na sombra, e esperamos que este motor de búsqueda amplíe o seu alcance."

msgid "page.about.text3"
msgstr "Para estar ao día dos nosos progresos, sigue a Anna en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Para preguntas e comentarios, ponte en contacto con Anna en %(email)s."

msgid "page.faq.help.title"
msgstr "Como podo axudar ?"

msgid "page.about.help.text"
msgstr "<li>1.Síguenos en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Corre a voz sobre Anna's Archive en Twitter, Reddit, TikTok, Instagram, na túa cafetería, biblioteca local ou onde sexa que vaias ! Non cremos no control de acceso: se nos retiran, apareceremos noutro sitio, xa que, todo o noso código e datos son de código aberto.</li><li>3. Se podes, considera <a href=\"/donate\">doar</a>.</li><li>4. Axuda a <a href=\"https://translate.annas-software.org/\">traducir</a> a nosa páxina web a diferentes idiomas.</li><li>5. Se eres un Enxeñeiro de Software, considera contribuír ao noso <a href=\"https://annas-software.org/\">código aberto</a>, ou sembrando os nosos <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Agora tamén temos un canal de Matrix sincronizado en %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Se eres un investigador de seguridade, podemos empregar as túas habilidades tanto para ataque como para a defensa. Bota un vistazo a nosa páxina de <a %(a_security)s>Seguridade</a>."

msgid "page.about.help.text7"
msgstr "7. Estamos na procura de expertos en pagos para comerciantes anónimos. ¿ Podes axudarnos a engadir formas máis cómodas de doar ? PayPal, WeChat, tarxetas regalo.Se coñeces a alguén, ponte en contacto con nós."

msgid "page.about.help.text8"
msgstr "8. Sempre estamos na procura de máis capacidade de servidor."

msgid "page.about.help.text9"
msgstr "9. Podes axudar informando de problemas cos arquivos, deixando comentarios e creando listas neste mesmo sitio web. Tamén podes axudar <a %(a_upload)s>subindo máis libros</a>, ou arreglando problemas de arquivos ou de formato de libros existentes."

msgid "page.about.help.text10"
msgstr "10. Crea ou axuda a crear a páxina de Wikipedia de Anna's Archive no teu idioma."

msgid "page.about.help.text11"
msgstr "11. Queremos poñer anuncios pequenos e de bo gusto. Se queres anunciarte en Anna's Archive, fáinolo saber."

msgid "page.faq.help.mirrors"
msgstr "Encantaríanos que a xente montara <a %(a_mirrors)s>espellos</a>, e o apoiaremos económicamente."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Para obter máis información sobre como ser voluntario, consulte a nosa páxina de <a %(a_volunteering)s>Voluntariado e Recompensas</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Por que as descargas lentas son tan lentas?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Literalmente non temos suficientes recursos para dar a todos no mundo descargas de alta velocidade, por moito que nos gustaría. Se un benefactor rico quixese dar un paso adiante e proporcionarnos isto, sería incrible, pero ata entón, estamos facendo o mellor que podemos. Somos un proxecto sen ánimo de lucro que apenas se pode soster a través de doazóns."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Por iso implementamos dous sistemas para descargas gratuítas, cos nosos socios: servidores compartidos con descargas lentas, e servidores lixeiramente máis rápidos cunha lista de espera (para reducir o número de persoas descargando ao mesmo tempo)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Tamén temos <a %(a_verification)s>verificación do navegador</a> para as nosas descargas lentas, porque doutro xeito os bots e os scrapers abusarían delas, facendo as cousas aínda máis lentas para os usuarios lexítimos."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Teña en conta que, ao usar o Tor Browser, pode que necesite axustar a configuración de seguridade. Na opción máis baixa, chamada “Estándar”, o desafío de Cloudflare turnstile ten éxito. Nas opcións máis altas, chamadas “Máis seguro” e “O máis seguro”, o desafío falla."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Para arquivos grandes, ás veces as descargas lentas poden romperse no medio. Recomendamos usar un xestor de descargas (como JDownloader) para retomar automaticamente as descargas grandes."

msgid "page.donate.faq.title"
msgstr "Preguntas frecuentes sobre doazóns"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>As afiliacións renóvanse automáticamente ?</div> As afiliacións <strong>non</strong> se renovan automáticamente. Podes afiliarte o tempo que desexes."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>¿Podo mellorar a miña subscrición ou obter varias subscricións?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Tedes outros métodos de pago ?</div> Actualmente non. Moita xente non quere que existan arquivos coma este, así que temos que ter coidado. Se podes axudarnos a configurar outros métodos de pago (máis cómodos) de forma segura, por favor ponte en contacto con nós en %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Que significan os rangos por mes?</div> Podes chegar ao lado inferior dun rango aplicando todos os descontos, como elixir un período máis longo que un mes."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>En qué se gastan as doazóns ?</div> O 100%% destínase a preservar e facer accesible o coñecemento e a cultura do mundo. Actualmente gastámolo principalmente en servidores, almacenamento e ancho de banda. Ningún membro do equipo recibe diñeiro persoalmente."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Podo facer unha doazón importante ?</div> Sería fantástico ! Para doazóns superiores a uns miles de dólares, póñase en contacto con nos directamente en %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>¿Podo facer unha doazón sen facerme membro?</div> Por suposto. Aceptamos doazóns de calquera cantidade nesta dirección de Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "Cómo subo libros novos ?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativamente, podes subilos a Z-Library <a %(a_upload)s>aquí</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Para cargas pequenas (ata 10.000 ficheiros) por favor súbaos tanto a %(first)s como a %(second)s."

msgid "page.upload.text1"
msgstr "Polo momento suxerímoslle que cargue os novos libros nos forks de Library Genesis. Aquí tes unha <a %(a_guide)s>guía práctica</a>. Teña en conta que os dous forks que indexamos neste sitio web baséanse neste mesmo sistema de carga."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Para Libgen.li, asegúrese de iniciar sesión primeiro no <a %(a_forum)s>seu foro</a> co nome de usuario %(username)s e contrasinal %(password)s, e logo volva á súa <a %(a_upload_page)s>páxina de carga</a>."

msgid "common.libgen.email"
msgstr "Se a túa dirección de correo non funciona nos foros de Libgen, recomendámoslle empregar <a %(a_mail)s>Proton Mail</a> (free). Tamén pode <a %(a_manual)s>solicitar manualmente</a> a activación da súa conta."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Teña en conta que mhut.org bloquea certos rangos de IP, polo que pode ser necesario un VPN."

msgid "page.upload.large.text"
msgstr "Para grandes subidas de datos (máis de 10,000 arquivos) que non sexan aceptados por Libgen ou Z-Library, por favor contacta con nós en %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Para subir artigos académicos, por favor tamén (ademais de Library Genesis) suba a <a %(a_stc_nexus)s>STC Nexus</a>. Son a mellor biblioteca na sombra para novos artigos. Aínda non os integramos, pero farémolo nalgún momento. Pode usar o seu <a %(a_telegram)s>bot de subida en Telegram</a>, ou contactar coa dirección listada na súa mensaxe fixada se ten demasiados arquivos para subir deste xeito."

msgid "page.faq.request.title"
msgstr "¿ Cómo solicito libros ?"

msgid "page.request.cannot_accomodate"
msgstr "Polo momento, non podemos antender solicitudes de libros."

msgid "page.request.forums"
msgstr "Por favor fai as túas solicitudes en foros de Z-Library ou Libgen."

msgid "page.request.dont_email"
msgstr "Non nos contactes para facer solicitudes de libros."

msgid "page.faq.metadata.title"
msgstr "¿ Recolledes metadatos ?"

msgid "page.faq.metadata.indeed"
msgstr "Así é."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Descarguei 1984 de George Orwell, virá a policía á miña porta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Non te preocupes demasiado, hai moitas persoas descargando de sitios web enlazados por nós, e é extremadamente raro meterse en problemas. Con todo, para estar seguro recomendámosche usar un VPN (de pago), ou <a %(a_tor)s>Tor</a> (gratuíto)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Como gardo as miñas configuracións de busca?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Seleccione as configuracións que lle gusten, deixe o cadro de busca baleiro, faga clic en “Buscar”, e logo marque a páxina usando a función de marcadores do seu navegador."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Tes unha aplicación móbil?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Non temos unha aplicación móbil oficial, pero podes instalar este sitio web como unha aplicación."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Fai clic no menú de tres puntos na parte superior dereita e selecciona “Engadir á pantalla de inicio”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Fai clic no botón “Compartir” na parte inferior e selecciona “Engadir á pantalla de inicio”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Tes unha API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Temos unha API JSON estable para membros, para obter unha URL de descarga rápida: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentación dentro do propio JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Para outros casos de uso, como iterar a través de todos os nosos ficheiros, construír buscas personalizadas, etc., recomendamos <a %(a_generate)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Os datos en bruto pódense explorar manualmente <a %(a_explore)s>a través de ficheiros JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "A nosa lista de torrents en bruto tamén se pode descargar como <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ de Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Gustaríame axudar a sementar, pero non teño moito espazo en disco."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Usa o <a %(a_list)s>xenerador de listas de torrents</a> para xerar unha lista de torrents que máis precisan ser compartidos, dentro dos límites do teu espazo de almacenamento."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Os torrents son demasiado lentos; podo descargar os datos directamente de vós?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Si, consulta a páxina de <a %(a_llm)s>datos de LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Podo descargar só un subconxunto dos ficheiros, como só un idioma ou tema en particular?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Resposta curta: non facilmente."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Resposta longa:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "A maioría dos torrents conteñen os ficheiros directamente, o que significa que podes instruír aos clientes de torrents para que só descarguen os ficheiros necesarios. Para determinar que ficheiros descargar, podes <a %(a_generate)s>xerar</a> os nosos metadatos, ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Desafortunadamente, un número de coleccións de torrents conteñen ficheiros .zip ou .tar na raíz, nese caso necesitas descargar todo o torrent antes de poder seleccionar ficheiros individuais."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Aínda que temos <a %(a_ideas)s>algunhas ideas</a> para este último caso.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Aínda non hai ferramentas fáciles de usar para filtrar torrents, pero agradecemos as contribucións."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Como manexades os duplicados nos torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Tentamos manter a duplicación ou solapamento mínimo entre os torrents nesta lista, pero isto non sempre se pode conseguir, e depende moito das políticas das bibliotecas fonte. Para as bibliotecas que publican os seus propios torrents, está fóra das nosas mans. Para os torrents lanzados por Anna’s Archive, deduplicamos só baseándonos no hash MD5, o que significa que diferentes versións do mesmo libro non se deduplican."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "¿Podo obter a lista de torrents como JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Si."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Non vexo PDFs ou EPUBs nos torrents, só ficheiros binarios? Que fago?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Estes son realmente PDFs e EPUBs, só que non teñen unha extensión en moitos dos nosos torrents. Hai dous lugares onde podes atopar os metadatos para os ficheiros de torrents, incluíndo os tipos/extensións de ficheiros:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Cada colección ou lanzamento ten os seus propios metadatos. Por exemplo, os <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> teñen unha base de datos de metadatos correspondente aloxada no sitio web de Libgen.rs. Normalmente ligamos aos recursos de metadatos relevantes desde a <a %(a_datasets)s>páxina do conxunto de datos</a> de cada colección."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomendamos <a %(a_generate)s>xerar</a> ou <a %(a_download)s>descargar</a> as nosas bases de datos ElasticSearch e MariaDB. Estas conteñen un mapeo para cada rexistro en Anna’s Archive cos seus ficheiros de torrent correspondentes (se están dispoñibles), baixo “torrent_paths” no JSON de ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Por que o meu cliente de torrent non pode abrir algúns dos vosos ficheiros torrent / ligazóns magnet?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Algúns clientes de torrent non admiten tamaños de peza grandes, que moitos dos nosos torrents teñen (para os máis novos xa non facemos isto, aínda que é válido segundo as especificacións!). Así que proba con outro cliente se te atopas con isto, ou queixate aos creadores do teu cliente de torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Tes un programa de divulgación responsable?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Dámoslle a benvida aos investigadores de seguridade para buscar vulnerabilidades nos nosos sistemas. Somos grandes defensores da divulgación responsable. Contacta connosco <a %(a_contact)s>aquí</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Actualmente non podemos ofrecer recompensas por erros, excepto por vulnerabilidades que teñan o <a %(a_link)s>potencial de comprometer o noso anonimato</a>, para as cales ofrecemos recompensas na franxa de $10k-50k. Gustaríanos ofrecer un alcance máis amplo para recompensas por erros no futuro! Ten en conta que os ataques de enxeñaría social están fóra do alcance."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Se estás interesado na seguridade ofensiva e queres axudar a arquivar o coñecemento e a cultura do mundo, asegúrate de contactar connosco. Hai moitas formas nas que podes axudar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Hai máis recursos sobre Anna’s Archive?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizacións regulares"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software de Anna</a> — o noso código aberto"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traducir no Software de Anna</a> — o noso sistema de tradución"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — sobre os datos"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternativos"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — máis sobre nós (por favor, axuda a manter esta páxina actualizada, ou crea unha na túa propia lingua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Como podo informar sobre infraccións de copyright?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Non aloxamos ningún material con copyright aquí. Somos un motor de busca e, como tal, só indexamos metadatos que xa están dispoñibles publicamente. Ao descargar destas fontes externas, suxerímosche que comprobes as leis na túa xurisdición respecto ao que está permitido. Non somos responsables do contido aloxado por outros."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Se tes queixas sobre o que ves aquí, o mellor é contactar co sitio web orixinal. Regularmente actualizamos a nosa base de datos cos seus cambios. Se realmente cres que tes unha queixa válida de DMCA á que debemos responder, enche o <a %(a_copyright)s>formulario de reclamación de DMCA / Copyright</a>. Tomamos as túas queixas en serio e responderemos o antes posible."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "¡Odio como estás xestionando este proxecto!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Tamén queremos lembrar a todos que todo o noso código e datos son completamente de código aberto. Isto é único para proxectos como o noso: non coñecemos ningún outro proxecto cun catálogo tan masivo que tamén sexa completamente de código aberto. Dámoslle a benvida a calquera que pense que xestionamos mal o noso proxecto a tomar o noso código e datos e crear a súa propia biblioteca na sombra! Non o dicimos por despeito ou algo así: realmente pensamos que sería incrible xa que elevaría o nivel para todos e preservaría mellor o legado da humanidade."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Tes un monitor de tempo de actividade?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Consulte <a %(a_href)s>este excelente proxecto</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Como podo doar libros ou outros materiais físicos?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Por favor, envíeos ao <a %(a_archive)s>Internet Archive</a>. Eles preservaranos adecuadamente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Quen é Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Ti es Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Cales son os teus libros favoritos?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Aquí tes algúns libros que teñen un significado especial para o mundo das bibliotecas na sombra e a preservación dixital:"

msgid "page.fast_downloads.no_more_new"
msgstr "Quedaches sen descargas rápidas por hoxe."

msgid "page.fast_downloads.no_member"
msgstr "Faite socio para empregar descargas rápidas."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Agora aceptamos tarxetas regalo de Amazon, tarxetas de crédito e débito, criptomoedas, Alipay e WeChat."

msgid "page.home.full_database.header"
msgstr "Base de datos completa"

msgid "page.home.full_database.subtitle"
msgstr "Libros, periódicos, revistas, cómics, rexistros de bibliotecas, metadatos, …"

msgid "page.home.full_database.search"
msgstr "Busca na base de datos completa"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ten unha subida <a %(a_paused)s>pausada</a> de novos artículos."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB é unha continuación de Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Acceso directo a %(count)s artículos académicos"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Aberto"

msgid "page.home.scidb.browser_verification"
msgstr "Se eres un <a %(a_member)s>membro</a>, a verificación de navegador non é requerida."

msgid "page.home.archive.header"
msgstr "Arquivo a longo prazo"

msgid "page.home.archive.body"
msgstr "Os conxuntos de datos (datasets) empregados en Anna’s Archive son totalmente aberto e poden reproducirse masivamente mediante torrents. <a %(a_datasets)s>Lee máis…</a>"

msgid "page.home.torrents.body"
msgstr "Podes axudar enormemente sembrando torrents. <a %(a_torrents)s>Leer máis…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "Datos de entrenamento de modelos LLM (Large Language Model)"

msgid "page.home.llm.body"
msgstr "Dispoñemos da maior colección do mundo de datos de texto de alta calidade. <a %(a_llm)s>Leer máis…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Espellos: unha chamada a voluntarios"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Buscamos voluntarios"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Como proxecto sen ánimo de lucro e de código aberto, sempre estamos buscando xente para axudar."

msgid "page.home.payment_processor.body"
msgstr "Se xestionas un procesador de pagos anónimos de alto risco, ponte en contacto con nós. Tamén buscamos persoas que desexen publicar anuncios de bo gusto. Todos os ingresos destinaranse aos nosos esforzos pola conservación."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog de Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Descargas IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Todos os enlaces de descarga de este arquivo: <a %(a_main)s>Páxina principal do arquivo</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(pode que teña que intentalo varias veces con IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Para conseguir descargas máis rápidas e saltar as comprobacións do navegador, <a %(a_membership)s>faite socio</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Para duplicar a nosa colección, consulta as páxinas <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Datos de LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Enténdese ben que os LLM prosperan con datos de alta calidade. Temos a maior colección de libros, artigos, revistas, etc. do mundo, que son algunhas das fontes de texto de maior calidade."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Escala e alcance únicos"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "A nosa colección contén máis de cen millóns de arquivos, incluíndo revistas académicas, libros de texto e revistas. Conseguimos esta escala combinando grandes repositorios existentes."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Algunhas das nosas coleccións de orixe xa están dispoñibles en masa (Sci-Hub e partes de Libgen). Outras fontes liberámolas nós mesmos. <a %(a_datasets)s>Datasets</a> mostra unha visión completa."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "A nosa colección inclúe millóns de libros, artigos e revistas de antes da era do libro electrónico. Grandes partes desta colección xa foron OCRizadas e xa teñen pouca superposición interna."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Como podemos axudar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Podemos proporcionar acceso de alta velocidade ás nosas coleccións completas, así como a coleccións non publicadas."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Este é un acceso a nivel empresarial que podemos proporcionar por doazóns no rango de decenas de miles de USD. Tamén estamos dispostos a intercambiar isto por coleccións de alta calidade que aínda non temos."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Podemos reembolsarlle se pode proporcionarnos enriquecemento dos nosos datos, como:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Eliminación de superposicións (deduplicación)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extracción de texto e metadatos"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "¡Apoie o arquivo a longo prazo do coñecemento humano, mentres obtén mellores datos para o seu modelo!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contacte connosco</a> para discutir como podemos traballar xuntos."

msgid "page.login.continue"
msgstr "Continuar"

msgid "page.login.please"
msgstr "Por favor <a %(a_account)s>inicia sesión</a>para ver esta páxina.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "O Arquivo de Anna está temporalmente fóra de servizo por mantemento. Volve nunha hora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Mellorar os metadatos"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Pode axudar á preservación dos libros mellorando os metadatos! Primeiro, lea a información sobre metadatos en Arquivo de Anna, e despois aprenda como mellorar os metadatos a través da vinculación con Open Library, e gañe unha subscrición gratuíta en Arquivo de Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Información"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Cando mira un libro en Arquivo de Anna, pode ver varios campos: título, autor, editorial, edición, ano, descrición, nome do ficheiro, e máis. Todas esas pezas de información chámanse <em>metadatos</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Como combinamos libros de varias <em>bibliotecas fonte</em>, mostramos os metadatos dispoñibles nesa biblioteca fonte. Por exemplo, para un libro que obtivemos de Library Genesis, mostraremos o título da base de datos de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Ás veces un libro está presente en <em>múltiples</em> bibliotecas fonte, que poden ter diferentes campos de metadatos. Nese caso, simplemente mostramos a versión máis longa de cada campo, xa que esa, con sorte, contén a información máis útil! Aínda mostraremos os outros campos debaixo da descrición, por exemplo, como \"título alternativo\" (pero só se son diferentes)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Tamén extraemos <em>códigos</em> como identificadores e clasificadores da biblioteca fonte. <em>Os identificadores</em> representan de forma única unha edición particular dun libro; exemplos son ISBN, DOI, Open Library ID, Google Books ID, ou Amazon ID. <em>Os clasificadores</em> agrupan varios libros similares; exemplos son Dewey Decimal (DCC), UDC, LCC, RVK, ou GOST. Ás veces estes códigos están explicitamente vinculados nas bibliotecas fonte, e ás veces podemos extraelos do nome do ficheiro ou da descrición (principalmente ISBN e DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Podemos usar identificadores para atopar rexistros en <em>coleccións só de metadatos</em>, como OpenLibrary, ISBNdb, ou WorldCat/OCLC. Hai unha <em>pestaña de metadatos</em> específica no noso motor de busca se desexa explorar esas coleccións. Usamos rexistros coincidentes para encher campos de metadatos que faltan (por exemplo, se falta un título), ou por exemplo, como \"título alternativo\" (se hai un título existente)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Para ver exactamente de onde proviñeron os metadatos dun libro, consulte a <em>pestaña \"Detalles técnicos\"</em> na páxina dun libro. Ten unha ligazón ao JSON en bruto dese libro, con referencias ao JSON en bruto dos rexistros orixinais."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Para máis información, consulte as seguintes páxinas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Busca (pestaña de metadatos)</a>, <a %(a_codes)s>Explorador de códigos</a>, e <a %(a_example)s>Exemplo de metadatos JSON</a>. Finalmente, todos os nosos metadatos poden ser <a %(a_generated)s>xenerados</a> ou <a %(a_downloaded)s>descargados</a> como bases de datos ElasticSearch e MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Vinculación con Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Entón, se atopa un ficheiro con malos metadatos, como debería arranxalo? Pode ir á biblioteca fonte e seguir os seus procedementos para arranxar os metadatos, pero que facer se un ficheiro está presente en varias bibliotecas fonte?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Hai un identificador que se trata de forma especial en Arquivo de Anna. <strong>O campo annas_archive md5 en Open Library sempre sobrescribe todos os outros metadatos!</strong> Retrocedamos un pouco primeiro e aprendamos sobre Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library foi fundada en 2006 por Aaron Swartz co obxectivo de \"unha páxina web para cada libro publicado\". É unha especie de Wikipedia para metadatos de libros: todos poden editala, está licenciada libremente, e pode ser descargada en masa. É unha base de datos de libros que está máis aliñada coa nosa misión — de feito, Arquivo de Anna foi inspirada pola visión e vida de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "En lugar de reinventar a roda, decidimos redirixir os nosos voluntarios cara a Open Library. Se ve un libro que ten metadatos incorrectos, pode axudar do seguinte xeito:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Vaia ao <a %(a_openlib)s>sitio web de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Atopa o rexistro correcto do libro. <strong>AVISO:</strong> asegúrese de seleccionar a <strong>edición</strong> correcta. En Open Library, ten \"obras\" e \"edicións\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Unha \"obra\" podería ser \"Harry Potter e a Pedra Filosofal\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Unha \"edición\" podería ser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "A primeira edición de 1997 publicada por Bloomsbery con 256 páxinas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "A edición de 2003 en tapa branda publicada por Raincoast Books con 223 páxinas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "A tradución polaca de 2000 “Harry Potter I Kamie Filozoficzn” por Media Rodzina con 328 páxinas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Todas esas edicións teñen diferentes ISBNs e contidos distintos, así que asegúrate de seleccionar a correcta!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edita o rexistro (ou créao se non existe) e engade tanta información útil como poidas! Xa que estás aquí, mellor fai que o rexistro sexa realmente incrible."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Baixo “Números de ID” selecciona “Anna’s Archive” e engade o MD5 do libro de Anna’s Archive. Este é o longo cadea de letras e números despois de “/md5/” na URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Tenta atopar outros arquivos en Anna’s Archive que tamén coincidan con este rexistro, e engádeos tamén. No futuro poderemos agrupalos como duplicados na páxina de busca de Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Cando remates, anota a URL que acabas de actualizar. Unha vez que teñas actualizado polo menos 30 rexistros con MD5s de Anna’s Archive, envíanos un <a %(a_contact)s>correo electrónico</a> e envíanos a lista. Darémosche unha subscrición gratuíta para Anna’s Archive, para que poidas facer este traballo máis facilmente (e como agradecemento pola túa axuda). Estas deben ser edicións de alta calidade que engadan cantidades substanciais de información, doutro xeito a túa solicitude será rexeitada. A túa solicitude tamén será rexeitada se algunha das edicións é revertida ou corrixida polos moderadores de Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Ten en conta que isto só funciona para libros, non para artigos académicos ou outros tipos de arquivos. Para outros tipos de arquivos aínda recomendamos atopar a biblioteca fonte. Pode levar unhas semanas para que os cambios se inclúan en Anna’s Archive, xa que necesitamos descargar o último volcado de datos de Open Library e rexenerar o noso índice de busca."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Espellos: chamada a voluntarios"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Para aumentar a resiliencia do Arquivo de Anna, estamos buscando voluntarios para executar espellos."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Estamos buscando isto:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Executa o código aberto de Anna’s Archive e actualiza regularmente tanto o código como os datos."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "A súa versión está claramente distinguida como un espello, por exemplo, “Arquivo de Bob, un espello de Anna’s Archive”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Está disposto a asumir os riscos asociados con este traballo, que son significativos. Ten un profundo entendemento da seguridade operativa requirida. O contido de <a %(a_shadow)s>estas</a> <a %(a_pirate)s>publicacións</a> é evidente para vostede."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Está disposto a contribuír á nosa <a %(a_codebase)s>base de código</a> — en colaboración co noso equipo — para que isto suceda."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inicialmente non lle daremos acceso ás descargas do noso servidor asociado, pero se todo vai ben, podemos compartilo con vostede."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Gastos de aloxamento"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Estamos dispostos a cubrir os gastos de aloxamento e VPN, inicialmente ata $200 por mes. Isto é suficiente para un servidor de busca básico e un proxy protexido por DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Só pagaremos polo aloxamento unha vez que teña todo configurado e demostre que é capaz de manter o arquivo actualizado con actualizacións. Isto significa que terá que pagar os primeiros 1-2 meses do seu propio peto."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "O seu tempo non será compensado (e o noso tampouco), xa que este é un traballo puramente voluntario."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se se involucra significativamente no desenvolvemento e operacións do noso traballo, podemos discutir compartir máis dos ingresos por doazóns con vostede, para que os despregue segundo sexa necesario."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Comezando"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Por favor, <strong>non nos contacte</strong> para pedir permiso ou para preguntas básicas. ¡As accións falan máis alto que as palabras! Toda a información está dispoñible, así que simplemente adiante coa configuración do seu espello."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Non dubide en publicar tickets ou solicitudes de fusión no noso Gitlab cando teña problemas. Podemos necesitar construír algunhas funcións específicas para espellos con vostede, como rebranding de “Anna’s Archive” ao nome do seu sitio web, (inicialmente) desactivar contas de usuario, ou ligar de volta ao noso sitio principal desde as páxinas dos libros."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Unha vez que teña o seu espello en funcionamento, por favor, contacte connosco. Gustaríanos revisar a súa OpSec, e unha vez que estea sólida, ligaremos ao seu espello e comezaremos a traballar máis estreitamente con vostede."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "¡Grazas de antemán a calquera que estea disposto a contribuír deste xeito! Non é para os de corazón débil, pero solidificaría a lonxevidade da maior biblioteca verdadeiramente aberta na historia da humanidade."

msgid "page.partner_download.header"
msgstr "Descargar do sitio web do noso Partner"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ As descargas lentas só están dispoñibles a través do sitio web oficial. Visita %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ As descargas lentas non están dispoñibles a través de VPN de Cloudflare ou de outro modo desde direccións IP de Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Agarde <span %(span_countdown)s>%(wait_seconds)s</span> segundos para descargar este ficheiro."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Emprega a seguinte URL para descargar: <a %(a_download)s>Descargar agora</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Grazas por esperar, isto mantén o sitio web accesible de balde para todos! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Advertencia: producíronse moitas descargas dende a súa dirección IP nas últimas 24 horas. As descargas poden ser máis lentas do habitual."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descargas dende a súa dirección IP nas últimas 24 horas: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Se estás a usar unha VPN, unha conexión a internet compartida ou o teu ISP comparte IPs, este aviso pode deberse a iso."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Co fin de dar a todos a oportunidade de descargar ficheiros de balde, ten que agardar antes de poder descargar este ficheiro."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Podes seguir navegando polo Arquivo de Anna nunha pestana diferente mentres esperas (se o teu navegador admite a actualización de pestanas en segundo plano)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Non dubides en esperar a que se carguen varias páxinas de descarga ao mesmo tempo (pero por favor só descarga un ficheiro á vez por servidor)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Unha vez que obteñas unha ligazón de descarga, será válida durante varias horas."

msgid "layout.index.header.title"
msgstr "Anna's Archive"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Rexistro en Anna's Archive"

msgid "page.scidb.download"
msgstr "Descargar"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Para apoiar a accesibilidade e a preservación a longo prazo do coñecemento humano, faise <a %(a_donate)s>membro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Como bonificación, 🧬&nbsp;SciDB carga máis rápido para os membros, sen ningún límite."

msgid "page.scidb.refresh"
msgstr "Non funciona ? Intenta <a %(a_refresh)s>refrescar</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Aínda non hai vista previa dispoñible. Descarga o ficheiro desde <a %(a_path)s>O Arquivo de Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB é unha continuación de Sci-Hub, coa súa interface familiar e visualización directa de PDFs. Introduza o seu DOI para ver."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Temos a colección completa de Sci-Hub, así como novos artigos. A maioría pódense ver directamente cunha interface familiar, similar a Sci-Hub. Algúns pódense descargar a través de fontes externas, nese caso mostramos ligazóns a esas fontes."

msgid "page.search.title.results"
msgstr "%(search_input)s - Búsqueda"

msgid "page.search.title.new"
msgstr "Nova búsqueda"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Incluir só"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Excluir"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Sen verificar"

msgid "page.search.tabs.download"
msgstr "Descargar"

msgid "page.search.tabs.journals"
msgstr "Artículos de revistas"

msgid "page.search.tabs.digital_lending"
msgstr "Préstamo dixital"

msgid "page.search.tabs.metadata"
msgstr "Metadata"

msgid "common.search.placeholder"
msgstr "Título, autor, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Buscar"

msgid "page.search.search_settings"
msgstr "Axustes de búsqueda"

msgid "page.search.submit"
msgstr "Buscar"

msgid "page.search.too_long_broad_query"
msgstr "A búsqueda tardou demasiado, o que é habitual en consultas amplias. É posible que o reconto de filtros non sexa exacto."

msgid "page.search.too_inaccurate"
msgstr "A búsqueda tardou demasiado, o que é habitual en consultas amplias. Ás veces <a %(a_reload)s>refrescar</a> a páxina axuda."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Amosar"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Táboa"

msgid "page.search.advanced.header"
msgstr "Avanzado"

msgid "page.search.advanced.description_comments"
msgstr "Descripcións de búsqueda e comentarios de metadatos"

msgid "page.search.advanced.add_specific"
msgstr "Engadir campo de búsqueda específico"

msgid "common.specific_search_fields.select"
msgstr "(buscar en un campo específico)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Ano de publicación"

msgid "page.search.filters.content.header"
msgstr "Contido"

msgid "page.search.filters.filetype.header"
msgstr "Tipo de arquivo"

msgid "page.search.more"
msgstr "máis…"

msgid "page.search.filters.access.header"
msgstr "Acceso"

msgid "page.search.filters.source.header"
msgstr "Fonte"

msgid "page.search.filters.source.scraped"
msgstr "recuperado e de código aberto por AA"

msgid "page.search.filters.language.header"
msgstr "Idioma"

msgid "page.search.filters.order_by.header"
msgstr "Ordear por"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Máis relevante"

msgid "page.search.filters.sorting.newest"
msgstr "Máis novo"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(ano de publicación)"

msgid "page.search.filters.sorting.oldest"
msgstr "Máis antigo"

msgid "page.search.filters.sorting.largest"
msgstr "Máis grande"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(tamaño de arquivo)"

msgid "page.search.filters.sorting.smallest"
msgstr "Máis pequeno"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(de código aberto)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aleatorio"

msgid "page.search.header.update_info"
msgstr "O índice de búsqueda actualízase mensualmente. Actualmente inclúe entradas ata %(last_data_refresh_date)s. Para máis información técnica, consulte a %(link_open_tag)spáxina de datasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Para explorar o índice de busca por códigos, usa o <a %(a_href)s>Explorador de Códigos</a>."

msgid "page.search.results.search_downloads"
msgstr "Escriba na caixa para buscar directamente no noso catálogo de %(count)s arquivos descargables, que <a %(a_preserve)s>conservamos para sempre</a>."

msgid "page.search.results.help_preserve"
msgstr "De feito, calquera pode axudar a preservar estos arquivos sembrando a nosa <a %(a_torrents)s>lista unificada de torrents</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Actualmente dispoñemos do catálogo aberto de libros, artículos e outras obras escritas máis completo do mundo. Somos espello de Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e máis</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Se atopas outras \"bibliotecas na sombra\" que debamos reflexar, ou se tes algunha pregunta, póñase en contacto con nós en %(email)s."

msgid "page.search.results.dmca"
msgstr "Para reclamacióons de DMCA / copyright <a %(a_copyright)s>clica aquí</a>."

msgid "page.search.results.shortcuts"
msgstr "Consello: emprega atallos de teclado “/” (foco na búsqueda), “enter” (buscar), “j” (arriba), “k” (abaixo), “<” (páxina anterior), “>” (seguinte páxina) para navegar máis rápido."

msgid "page.search.results.looking_for_papers"
msgstr "¿ Busca documentos ?"

msgid "page.search.results.search_journals"
msgstr "Escriba na caixa para buscar no noso catálogo de %(count)s traballos académicos e artículos de revistas, que <a %(a_preserve)s>conservamos para sempre</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Escriba na caixa para buscar arquivos nas bibliotecas de préstamo dixital."

msgid "page.search.results.digital_lending_info"
msgstr "Este índice de búsqueda inclúe actualmente metadatos da Biblioteca de Péstamo Dixital (Controlled Digital Lending) de Internet Arquive. <a %(a_datasets)s>Máis sobre os nosos datasets</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Para coñecer máis bibliotecas de préstamo dixital, consulte <a %(a_wikipedia)s>Wikipedia</a> e a <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Escriba na caixa para buscar metadata de bibliotecas. Esto pode ser útil cando <a %(a_request)s>solicite un arquivo</a>."

msgid "page.search.results.metadata_info"
msgstr "Este índice de búsqueda inclúe actualmente metadatos de varias fontes de metadatos. <a %(a_datasets)s>Máis información sobre os nosos datasets (conxuntos de datos)</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Para os metadatos, mostramos os rexistros orixinais. Non facemos ningunha fusión de rexistros."

msgid "page.search.results.metadata_info_more"
msgstr "Existen moitísimas fontes de metadatos para obras escritas en todo o mundo. <a %(a_wikipedia)s>Esta páxina de Wikipedia</a> é un bo comezo, pero se coñece outras boas listas, non dude en comunicárnolo."

msgid "page.search.results.search_generic"
msgstr "Escriba na caixa para buscar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Estes son rexistros de metadatos, <span %(classname)s>non</span> arquivos descargables."

msgid "page.search.results.error.header"
msgstr "Erro durante a búsqueda."

msgid "page.search.results.error.unknown"
msgstr "Intenta <a %(a_reload)s>refrescar a páxina</a>. Se o problema persiste, por favor contacta con nós en %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Non se atoparon arquivos.</span> Intenta con menos ou diferentes termos de búsqueda e filtros."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Ás veces isto ocorre incorrectamente cando o servidor de busca está lento. Neses casos, <a %(a_attrs)s>recargar</a> pode axudar."

msgid "page.search.found_matches.main"
msgstr "Atopamos coincidencias en: %(in)s. Pode facer referencia a URL atopada alí cando <a %(a_request)s>solicite un arquivo</a>."

msgid "page.search.found_matches.journals"
msgstr "Artículos de revista (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Préstamos dixitais (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadatos (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Resultados %(from)s-%(to)s (%(total)s total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ de coincidencias parciais"

msgid "page.search.results.partial"
msgstr "%(num)d coincidencias parciais"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariado e Recompensas"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive depende de voluntarios como vostede. Damos a benvida a todos os niveis de compromiso e temos dúas categorías principais de axuda que estamos buscando:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Traballo lixeiro de voluntariado:</span> se só pode dedicar unhas poucas horas aquí e alí, aínda hai moitas formas en que pode axudar. Recompensamos aos voluntarios consistentes con <span %(bold)s>🤝 membresías de Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Traballo voluntario intenso (recompensas de USD$50-USD$5,000):</span> se podes dedicar moito tempo e/ou recursos á nosa misión, encantaríanos traballar máis estreitamente contigo. Eventualmente podes unirte ao equipo interno. Aínda que temos un orzamento axustado, podemos outorgar <span %(bold)s>💰 recompensas monetarias</span> polo traballo máis intenso."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Se non podes ofrecer o teu tempo como voluntario, aínda podes axudarnos moito <a %(a_donate)s>doando diñeiro</a>, <a %(a_torrents)s>semeando os nosos torrents</a>, <a %(a_uploading)s>subindo libros</a>, ou <a %(a_help)s>contándolle aos teus amigos sobre o Arquivo de Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Empresas:</span> ofrecemos acceso directo de alta velocidade ás nosas coleccións a cambio dunha doazón a nivel empresarial ou a cambio de novas coleccións (por exemplo, novas escaneos, datasets OCR, enriquecemento dos nosos datos). <a %(a_contact)s>Contacta connosco</a> se este é o teu caso. Vexa tamén a nosa <a %(a_llm)s>páxina de LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariado lixeiro"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Se tes unhas poucas horas libres, podes axudar de varias maneiras. Asegúrate de unirte ao <a %(a_telegram)s>chat de voluntarios en Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Como mostra de agradecemento, normalmente damos 6 meses de “Bibliotecario Afortunado” por fitos básicos, e máis por traballo voluntario continuado. Todos os fitos requiren traballo de alta calidade — o traballo desleixado préxudicanos máis do que axuda e rexeitarémolo. Por favor <a %(a_contact)s>envíanos un correo electrónico</a> cando alcances un fito."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tarefa"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Fito"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Espallando a palabra do Arquivo de Anna. Por exemplo, recomendando libros en AA, ligando ás nosas publicacións no blog ou, en xeral, dirixindo á xente ao noso sitio web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "Ligazóns ou capturas de pantalla %(links)s."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Estas deberían mostrarche informando a alguén sobre o Arquivo de Anna, e eles agradecéndoche."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Mellorar os metadatos <a %(a_metadata)s>ligando</a> con Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Podes usar a <a %(a_list)s >lista de problemas de metadata aleatorios</a> como punto de partida."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Asegúrate de deixar un comentario nos problemas que resolves, para que outros non dupliquen o teu traballo."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "Ligazóns de rexistros que melloraches %(links)s."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traducir</a> o sitio web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traducir completamente un idioma (se non estaba xa case completado)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Mellorar a páxina de Wikipedia do Arquivo de Anna no teu idioma. Inclúe información da páxina de Wikipedia de AA noutros idiomas, e do noso sitio web e blog. Engade referencias a AA noutras páxinas relevantes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Ligazón ao historial de edicións mostrando que fixeches contribucións significativas."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Cumprir solicitudes de libros (ou artigos, etc) nos foros de Z-Library ou Library Genesis. Non temos o noso propio sistema de solicitudes de libros, pero espellamos esas bibliotecas, así que melloralas fai que o Arquivo de Anna tamén mellore."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "Ligazóns ou capturas de pantalla das solicitudes que cumpriches %(links)s."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Pequenas tarefas publicadas no noso <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Normalmente para membresía, ás veces para pequenas recompensas."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Pequenas tarefas publicadas no noso grupo de chat de voluntarios."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depende da tarefa."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Recompensas"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Sempre estamos buscando persoas con habilidades sólidas en programación ou seguridade ofensiva para involucrarse. Podes facer unha contribución seria na preservación do legado da humanidade."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Como agradecemento, ofrecemos membresía por contribucións sólidas. Como un gran agradecemento, ofrecemos recompensas monetarias por tarefas particularmente importantes e difíciles. Isto non debe ser visto como un substituto dun traballo, pero é un incentivo extra e pode axudar cos custos incorridos."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "A maior parte do noso código é de código aberto, e pediremos o mesmo do teu código ao outorgar a recompensa. Hai algunhas excepcións que podemos discutir de forma individual."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "As recompensas outórganse á primeira persoa que complete unha tarefa. Sente libre de comentar nun ticket de recompensa para que outros saiban que estás traballando en algo, para que outros poidan esperar ou contactarte para formar equipo. Pero ten en conta que outros aínda son libres de traballar niso tamén e tentar superarte. Non obstante, non outorgamos recompensas por traballos descoidados. Se se fan dúas presentacións de alta calidade moi próximas entre si (dentro dun día ou dous), podemos optar por outorgar recompensas a ambas, á nosa discreción, por exemplo 100%% para a primeira presentación e 50%% para a segunda presentación (así que 150%% en total)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Para as recompensas máis grandes (especialmente as recompensas de scraping), por favor contacta connosco cando completes ~5%% dela, e esteas seguro de que o teu método escalará ata o fito completo. Terás que compartir o teu método connosco para que poidamos dar feedback. Ademais, deste xeito podemos decidir que facer se hai varias persoas achegándose a unha recompensa, como potencialmente outorgala a varias persoas, animar a formar equipos, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AVISO: as tarefas de alta recompensa son <span %(bold)s>difíciles</span> — pode ser sabio comezar con tarefas máis fáciles."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Vai á nosa <a %(a_gitlab)s>lista de problemas en Gitlab</a> e ordena por “Prioridade de etiqueta”. Isto mostra aproximadamente a orde das tarefas que nos importan. As tarefas sen recompensas explícitas aínda son elixibles para a membresía, especialmente aquelas marcadas como “Aceptadas” e “Favoritas de Anna”. Pode que queiras comezar cun “Proxecto inicial”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Actualizacións sobre <a %(wikipedia_annas_archive)s>Arquivo de Anna</a>, a maior biblioteca verdadeiramente aberta na historia da humanidade."

msgid "layout.index.title"
msgstr "Anna’s Archive"

msgid "layout.index.meta.description"
msgstr "A maior biblioteca de datos de código aberto do mundo. Reflexa Sci-Hub, Library Genesis, Z-Library, e máis."

msgid "layout.index.meta.opensearch"
msgstr "Buscar en Anna’s Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "¡Anna’s Archive necesita a túa axuda!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Moitos intentan derrubarnos, pero loitamos de volta."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se doas agora, obterás <strong>o dobre</strong> do número de descargas rápidas."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Válido ata o final deste mes."

msgid "layout.index.header.nav.donate"
msgstr "Doa"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvando o coñecemento humano: un gran agasallo de vacacións!"

msgid "layout.index.header.banner.surprise"
msgstr "Sorprende a un ser querido, agasalla unha conta con subscrición."

msgid "layout.index.header.banner.mirrors"
msgstr "Para aumentar a resistencia de Anna's Arquive, buscamos voluntarios que se encarguen dos espellos."

msgid "layout.index.header.banner.valentine_gift"
msgstr "O regalo perfecto para San Valentín !"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Temos un novo métodos de doazón dispoñible: %(method_name)s. Por favor considere %(donate_link_open_tag)sdoar</a> — non é barato manter este sitio web, e a túa doazón realmente marca a diferencia. Moitas grazas."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Estamos recaudando fondos para <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">respaldar</a> a maior biblioteca de cómics na sombra do mundo. Grazas polo teu apoio ! <a href=\"/donate\">Doa.</a> Se non podes doar, considera apoiarnos correndo a voz, e síguenos en <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, ou <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Descargas recentes:"

msgid "layout.index.header.nav.search"
msgstr "Búsqueda"

msgid "layout.index.header.nav.faq"
msgstr "Preguntas Frecuentes (FAQ)"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Mellorar metadatos"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariado e Recompensas"

msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Actividade"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorador de códigos"

msgid "layout.index.header.nav.llm_data"
msgstr "Datos LLM"

msgid "layout.index.header.nav.home"
msgstr "Inicio"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traducción ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Iniciar sesión / Rexistrarse"

msgid "layout.index.header.nav.account"
msgstr "Conta"

msgid "layout.index.footer.list1.header"
msgstr "Anna's Archive"

msgid "layout.index.footer.list2.header"
msgstr "Mantéñase en contacto"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "Reclamacións de DMCA / copyright"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avanzado"

msgid "layout.index.header.nav.security"
msgstr "Seguridade"

msgid "layout.index.footer.list3.header"
msgstr "Alternativas"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non afiliado"

msgid "page.search.results.issues"
msgstr "❌ Este arquivo pode ter problemas."

msgid "page.search.results.fast_download"
msgstr "Descarga rápida"

msgid "page.donate.copy"
msgstr "copiar"

msgid "page.donate.copied"
msgstr "copiado !"

msgid "page.search.pagination.prev"
msgstr "Anterior"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Seguinte"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Espellos %(libraries)s, e máis."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr ""

#~ msgid "page.doi.breadcrumbs"
#~ msgstr ""

#~ msgid "page.doi.invalid.header"
#~ msgstr ""

#~ msgid "page.doi.invalid.text"
#~ msgstr ""

#~ msgid "page.doi.box.header"
#~ msgstr ""

#~ msgid "page.doi.box.canonical_url"
#~ msgstr ""

#~ msgid "page.doi.box.scihub"
#~ msgstr ""

#~ msgid "page.doi.results.text"
#~ msgstr ""

#~ msgid "page.doi.results.none"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr ""

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr ""

#~ msgid "page.about.header"
#~ msgstr ""

#~ msgid "page.home.search.header"
#~ msgstr ""

#~ msgid "page.home.search.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr ""

#~ msgid "page.home.explore.header"
#~ msgstr ""

#~ msgid "page.home.explore.intro"
#~ msgstr ""

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr ""

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr ""

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr ""

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Doar o importe total de %(total)s empregando <a %(a_account)s>esta conta de Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Alternativamente, pode subilos a Z-Library <a %(a_upload)s>aquí</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Para aumentar a resiliencia de Anna's Archive, buscamos voluntarios para realizar espellos. <a href=\"/mirrors\">Leer máis…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Espellos: unha chamada a voluntarios"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "só este mes!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub <a %(a_closed)s>pausou</a> a subida de novos artigos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Seleccione unha opción de pago. Facemos descontos para pagos basados en criptomonedas %(bitcoin_icon)s, porque incurrimos en (moitas) menos comisións."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Seleccione unha opción de pago. Actualmente só dispoñemos de pagos basados en criptomonedas %(bitcoin_icon)s, xa que os procesadores de pagos tradicionais néganse a traballar con nosco."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Non podemos admitir tarxetas de crédito/débito directamente, porque os bancos non queren traballar connosco. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Con todo, hai varias formas de usar tarxetas de crédito/débito de todos os xeitos, empregando os nosos outros métodos de pago:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Descargas lentas e externas"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descargas"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se estás empregando criptomonedas por primeira vez, suxerímoste %(option1)s, %(option2)s, ou %(option3)s para comprar e donar Bitcoin (a criptomoneda orixinal e a máis empregada)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 ligazóns de rexistros que melloraches."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 ligazóns ou capturas de pantalla."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 ligazóns ou capturas de pantalla das solicitudes que cumpriches."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se estás interesado en espellar estes datasets para <a %(a_faq)s>arquivo</a> ou para <a %(a_llm)s>adestramento de LLM</a>, por favor contacta connosco."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se estás interesado en espellar este conxunto de datos para <a %(a_archival)s>arquivo</a> ou para <a %(a_llm)s>adestramento de LLM</a>, contacta connosco."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sitio web principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Información do país do ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se está interesado en espellar este conxunto de datos para fins de <a %(a_archival)s>arquivo</a> ou <a %(a_llm)s>formación de LLM</a>, póñase en contacto connosco."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "A Axencia Internacional do ISBN publica regularmente os rangos que asignou ás axencias nacionais do ISBN. A partir disto, podemos deducir a que país, rexión ou grupo lingüístico pertence este ISBN. Actualmente usamos estes datos indirectamente, a través da biblioteca Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Recursos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Última actualización: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sitio web do ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadatos"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluíndo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "A nosa inspiración para recopilar metadatos e o obxectivo de Aaron Swartz de \"unha páxina web por cada libro xamais publicado\", para o que creou a <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Ese proxecto foi ben, pero a nosa posición única permítenos obter metadatos que eles non poden."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Outra inspiración foi o noso desexo de saber <a %(a_blog)s>cantos libros hai no mundo</a>, para poder calcular cantos libros quedan por salvar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Para dar a todos a oportunidade de descargar arquivos de balde, necesitas esperar <strong>%(wait_seconds)s segundos</strong> antes de poder descargar este arquivo."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Actualizar a páxina automaticamente. Se perde a xanela de descarga, o temporizador reiniciarase, polo que se recomenda a actualización automática."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Descargar agora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converter: usa ferramentas en liña para converter entre formatos. Por exemplo, para converter entre epub e pdf, usa <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descarga o ficheiro (pdf ou epub son compatibles), despois <a %(a_kindle)s>envíao a Kindle</a> usando web, aplicación ou correo electrónico. Ferramentas útiles: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Apoia aos autores: Se che gusta e podes permitircho considera comprar o orixinal ou apoiar directamente aos autores."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Apoia ás bibliotecas: Se está dispoñible na túa biblioteca local, considera a posibilidade de tomalo prestado gratuitamente alí."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Non dispoñible directamente en masa, só en semi-masa detrás dun muro de pago"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s O Arquivo de Anna xestiona unha colección de <a %(isbndb)s>metadatos de ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb é unha empresa que rastrexa varias librarías en liña para atopar metadatos de ISBN. O Arquivo de Anna estivo facendo copias de seguridade dos metadatos dos libros de ISBNdb. Estes metadatos están dispoñibles a través do Arquivo de Anna (aínda que actualmente non na busca, excepto se buscas explicitamente un número de ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Para detalles técnicos, vexa a continuación. Nalgún momento podemos usalo para determinar que libros aínda faltan nas bibliotecas sombra, co fin de priorizar cales atopar e/ou escanear."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "A nosa publicación no blog sobre estes datos"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Rastrexo de ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actualmente temos un único torrent, que contén un ficheiro de 4,4 GB comprimido en gzip <a %(a_jsonl)s>JSON Lines</a> (20 GB descomprimido): “isbndb_2022_09.jsonl.gz”. Para importar un ficheiro “.jsonl” en PostgreSQL, pode usar algo como <a %(a_script)s>este script</a>. Incluso pode canalizalo directamente usando algo como %(example_code)s para que se descomprima sobre a marcha."

#~ msgid "page.donate.wait"
#~ msgstr "Por favor, agarde polo menos <span %(span_hours)s>dúas horas</span> (e refresca a páxina) antes de poñerte en contacto con nós."

#~ msgid "page.codes.search_archive"
#~ msgstr "Buscar en Anna’s Archive por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Doe empregando Alipay ou WeChat. Pode elixir entre ambas na seguinte páxina."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Difundir a palabra sobre o Arquivo de Anna nas redes sociais e foros en liña, recomendando libros ou listas en AA, ou respondendo preguntas."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s A colección de Ficción diverxeu pero aínda ten <a %(libgenli)s>torrents</a>, aínda que non se actualizou desde 2022 (temos descargas directas)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s O Arquivo de Anna e Libgen.li xestionan conxuntamente coleccións de <a %(comics)s>cómics</a> e <a %(magazines)s>revistas</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Non hai torrents para coleccións de ficción rusa e documentos estándar."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Non hai torrents dispoñibles para o contido adicional. Os torrents que están no sitio web de Libgen.li son espellos doutros torrents listados aquí. A única excepción son os torrents de ficción que comezan en %(fiction_starting_point)s. Os torrents de cómics e revistas son lanzados como unha colaboración entre o Arquivo de Anna e Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Dunha colección <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> orixe exacta descoñecida. Parte de the-eye.eu, parte doutras fontes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

